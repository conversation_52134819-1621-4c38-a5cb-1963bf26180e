<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="授权书驳回列表页" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="<%=basePath%>static/css/authorization_print.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/font/font.css" />
<link rel="stylesheet" href="<%=basePath%>static/new/css/common/lib/pikaday.css?rnd=${resourceVersionKey}" />
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>

<style>

    /* 公章类型下拉框样式 */
    .seal-type-select {
        width: 300px !important; /* 增加宽度以完整显示选项文本 */
        max-width: 100%;
    }

    .form-submit {
        border: 1px solid #000; /* 添加边框样式 */
        padding: 20px; /* 添加内边距以便内容不紧贴边框 */
        margin-top: 40px;
        margin-bottom: 20px; /* 添加底部外边距以便与下方内容区分 */
    }
    .wrapper {
        padding: 20px;
        width: 800px;
        margin: 0 auto;
    }

    .wrapper .form-item {
        margin-top: 10px;
        padding: 7px 0;
        display: flex;
    }

    .wrapper .form-item .item-label {
        width: 120px;
        text-align: right;
        margin-right: 10px;
    }
</style>

<div class="print-tab">
    <c:forEach var="authorizationApplie" items="${authorizationApplies}">
        <c:choose>
            <c:when test="${authorizationApplie.authorizationApplyId eq authorizationApplyId}">
                <a href="<%= basePath %>/order/quote/authorizationView.do?quoteorderId=${authorizationApplie.quoteorderId}&authorizationApplyId=${authorizationApplie.authorizationApplyId}" class="tab-item" style="height: 80px;background-color: #D7D7D7">

                    <div class="item-num" style="height: 30px">${authorizationApplie.authorizationApplyNum}</div>
                    <div class="item-status status-red" style="height: 30px">
                        <c:if test ="${authorizationApplie.applyStatus eq '1'}">审核中</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '2'}">驳回</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '3'}">审核通过</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '4'}">已取消</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '5'}">已作废</c:if>
                    </div>
                </a>
            </c:when>
            <c:otherwise>


                <a href="<%= basePath %>/order/quote/authorizationView.do?quoteorderId=${authorizationApplie.quoteorderId}&authorizationApplyId=${authorizationApplie.authorizationApplyId}" class="tab-item" style="height: 60px">

                    <div class="item-num">${authorizationApplie.authorizationApplyNum}</div>
                    <div class="item-status status-red">
                        <c:if test ="${authorizationApplie.applyStatus eq '1'}">审核中</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '2'}">驳回</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '3'}">审核通过</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '4'}">已取消</c:if>
                        <c:if test ="${authorizationApplie.applyStatus eq '5'}">已作废</c:if>
                    </div>
                </a>

            </c:otherwise>
        </c:choose>
    </c:forEach>

</div>
<form method="post" action="<%= basePath %>/order/quote/authorizationApply.do" id="tjform" onsubmit="return checkSubmit()">
    <input type="hidden" id="companyInfoListWithSeq" name="companyInfoListWithSeq" value='${companyInfoListWithSeq}'/>

    <input type="hidden" value="${authorizationApply.quoteorderId}" name="quoteorderId">
    <input type="hidden" value="${authorizationApply.skuId}" name="skuId" id="skuId">
    <input type="hidden" value="${authorizationApply.authorizationApplyNum}" name="authorizationApplyNum" id="authorizationApplyNum">
    <input type="hidden" name="temporaryStorageId" value="${authorizationStorageId}">
    <input type="hidden" name="authorizationApplyId" value="${authorizationApplyId}">
    <input type="hidden" name="maxId" value="${maxId}" id="maxId">
    <input type="hidden" name="formToken" value="${formToken}" id="formToken">
    <div class="formpublic">
        <div class="wrapper">
            <div class="header">
                <div class="logo">
                    <img src="<%=basePath%>static/images/print_logo.png" alt="">
                </div>
                <div class="header-txt" style="padding-right:80px;">
                    <br>
                    <br>
                    <br>
                    授权书编号：${authorizationApply.authorizationApplyNum}<br>
                </div>
            </div>
            <div class="title">授 权 书</div>
            <div class="content">
                <p style="text-indent: 0;">致：<input type="text" class="input-print left xl" value="${authorizationApply.purchaseOrBidding}" name="purchaseOrBidding" id="purchaseOrBidding" placeholder="请填写采购单位/招标公司" maxlength="200"></p>
                <p>南京贝登医疗股份有限公司作为
                    <input type="text" class="input-print xl" value="${authorizationApply.productCompany}" id="productCompany" name="productCompany" maxlength="200" placeholder="请填写生产厂家" maxlength="200">
                    <select class="select-print" name="natureOfOperation" id="natureOfOperation">
                        <option value="1" <c:if test ="${authorizationApply.natureOfOperation eq 1}">selected="selected"</c:if>>生产</option>
                        <option value="2" <c:if test ="${authorizationApply.natureOfOperation eq 2}">selected="selected"</c:if>>代理销售</option>
                    </select>

                    <input type="text" class="select-print" name="brandName" id="brandName" value="${authorizationApply.brandName}" onclick="findSku(${authorizationApply.quoteorderId});" readonly>
                    品牌的
                    <input type="text" class="select-print" style="width: 320px" name="skuName" id="skuName" value="${authorizationApply.skuName}" onclick="findSku(${authorizationApply.quoteorderId});" readonly>

                    <input type="hidden" class="select-print" name="skuModel" id="model" value="${authorizationApply.skuModel}">
                    的
                    <select class="select-print" name="distributionsType" id="distributionsType">
                        <option value="1" <c:if test ="${authorizationApply.distributionsType eq 1}">selected="selected"</c:if>>独家经销商</option>
                        <option value="2" <c:if test ="${authorizationApply.distributionsType eq 2}">selected="selected"</c:if>>经销商</option>
                        <option value="3" <c:if test ="${authorizationApply.distributionsType eq 3}">selected="selected"</c:if>>代理商</option>
                    </select>
                    ，在此授权
                    <input type="text" class="input-print xl J-company-1" value="${authorizationApply.authorizedCompany}" id="authorizedCompany" placeholder="（授权公司）" maxlength="200">
                    （被授权人）使用上述货物就项目：
                    <input type="text" style="width: 200px;" value="${authorizationApply.purchaseProjectName}" name="purchaseProjectName" class="input-print xl" id="purchaseProjectName" placeholder="请填写采购项目全称" maxlength="200">
                    项目编号：
                    <input type="text" value="${authorizationApply.purchaseProjectNum}" name="purchaseProjectNum" class="input-print l" id="purchaseProjectNum"  placeholder="请填写采购项目编号" maxlength="200">
                    递交
                    <select class="select-print" name="fileType" id="fileType">
                        <option value="1" <c:if test ="${authorizationApply.fileType eq 1}">selected="selected"</c:if>>投标</option>
                        <option value="2" <c:if test ="${authorizationApply.fileType eq 2}">selected="selected"</c:if>>响应</option>
                    </select>
                    文件，且
                    <input style="width: 200px;" type="text" class="input-print xl J-company-2" value="${authorizationApply.authorizedCompany}" name="authorizedCompany" placeholder="（授权公司）" maxlength="200">
                    以其自己的名义处理后续的商业谈判和签署合同并独立承担责任。
                </p>
                <p>
                    以上所述授权产品的相关售后服务支持工作将由
                    <input type="text" class="input-print xl" value="${authorizationApply.aftersalesCompany}" name="aftersalesCompany" id="aftersalesCompany" placeholder="（售后支持公司全称）" maxlength="200">
                    提供。
                </p>
                <p>
                    该授权未经南京贝登医疗股份有限公司书面同意，被授权人不可转授任何第三方。被授权人禁止将该授权书作为任何网络线上渠道的资格审核材料，且禁止被授权人在任何网络线上渠道展示和销售该授权书涉及的商品。
                </p>
                <p class="J-date-range">
                    本授权的有效期：
                    <input type="text"  autocomplete="off" value="${authorizationApply.beginTime}" id="beginTime" name="beginTime" class="input-print input-date l" readonly>
                    至
                    <input type="text"  autocomplete="off" value="${authorizationApply.endTime}" id="endTime" name="endTime" class="input-print input-date l" readonly>
                    (控制：<span id="pikadayNum" style="display: contents;">项目授权，时间不得超过90天</span>)
                </p>
                <p class="top right">
                    南京贝登医疗股份有限公司
                </p>
                <p class="right">
                    <select class="input-print x2" name="applyYear" id="applyYear">
                        <option value="${authorizationApply.applyYear}" selected="selected">${authorizationApply.applyYear}</option>
                        <option value="${dateYear}">${dateYear}</option>
                        <option value="${dateYear+1}">${dateYear+1}</option>
                        <option value="${dateYear+2}">${dateYear+2}</option>
                        <option value="${dateYear+3}">${dateYear+3}</option>
                        <option value="${dateYear+4}">${dateYear+4}</option>
                        <option value="${dateYear+5}">${dateYear+5}</option>
                        <option value="${dateYear+6}">${dateYear+6}</option>
                        <option value="${dateYear+7}">${dateYear+7}</option>
                        <option value="${dateYear+8}">${dateYear+8}</option>
                        <option value="${dateYear+9}">${dateYear+9}</option>
                        <option value="${dateYear+10}">${dateYear+10}</option>
                    </select>
                    年
                    <select class="input-print x2" name="applyMonth" id="applyMonth">
                        <option value="${authorizationApply.applyMonth}" selected="selected">${authorizationApply.applyMonth}</option>
                        <option value="01">01</option>
                        <option value="02">02</option>
                        <option value="03">03</option>
                        <option value="04">04</option>
                        <option value="05">05</option>
                        <option value="06">06</option>
                        <option value="07">07</option>
                        <option value="08">08</option>
                        <option value="09">09</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                    </select>
                    月
                    <select class="input-print x2" name="applyDay" id="applyDay">
                        <option value="${authorizationApply.applyDay}" selected="selected">${authorizationApply.applyDay}</option>
                        <option value="01">01</option>
                        <option value="02">02</option>
                        <option value="03">03</option>
                        <option value="04">04</option>
                        <option value="05">05</option>
                        <option value="06">06</option>
                        <option value="07">07</option>
                        <option value="08">08</option>
                        <option value="09">09</option>
                        <option value="10">10</option>
                        <option value="11">11</option>
                        <option value="12">12</option>
                        <option value="13">13</option>
                        <option value="14">14</option>
                        <option value="15">15</option>
                        <option value="16">16</option>
                        <option value="17">17</option>
                        <option value="18">18</option>
                        <option value="19">19</option>
                        <option value="20">20</option>
                        <option value="21">21</option>
                        <option value="22">22</option>
                        <option value="23">23</option>
                        <option value="24">24</option>
                        <option value="25">25</option>
                        <option value="26">26</option>
                        <option value="27">27</option>
                        <option value="28">28</option>
                        <option value="29">29</option>
                        <option value="30">30</option>
                        <option value="31">31</option>
                    </select>
                    日                </p>
            </div>
            <div class="footer">
                <div class="footer-l">www.vedeng.com</div>
                <div class="footer-r">医疗器械互联网供应链服务平台</div>
            </div>

            <div class="form-item">
                <!--新增授权类型字段 authType-->
                <div class="item-label"><span style="color:red;">*</span>授权类型：</div>
                <div class="item-fields">
                    <input type="radio" name="authType" value="0" onclick="initDateForApply(90,false)"
                           <c:if test="${(empty authorizationApply.authType) or (authorizationApply.authType eq 0)}">checked</c:if>>项目授权
                    <input type="radio" name="authType" value="1" onclick="initDateForApply(365,false)"
                           <c:if test="${(not empty authorizationApply.authType) and (authorizationApply.authType eq 1)}">checked</c:if>>经销授权
                </div>
            </div>
            <div class="form-item">
                <div class="item-label" ><span style="color:red;">*</span>授权书模板：</div>
                <div class="item-fields"><input type="radio" name="standardTemplate" onclick="javascript:clickBiaoZhun();" value="0" <c:if test="${authorizationApply.standardTemplate eq 0}">checked</c:if>>标准模板 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" name="standardTemplate" onclick="javascript:clickNotBiaoZhun();" value="1" <c:if test="${authorizationApply.standardTemplate eq 1}">checked</c:if>>非标准模板
                </div>
            </div>

            <div class="form-item" id="notBiaoZhunDiv">
                <div class="item-label"><span style="color:red;">*</span>非标授权书附件：</div>
                <div class="item-fields">
                    <input type="hidden" id="domain" name="domain" value="${domain}">
                    <div>
                        <div class="form-blanks">
                            <c:choose>
                                <c:when test="${nonStandardAuthorizationUrl ne null && nonStandardAuthorizationUrl ne ''}">
                                    <div class="c_1">
                                        <div class="pos_rel f_left ">
                                            <input type="file" class="upload_file" name="lwfile"
                                                   id="file_non" style="display: none;"
                                                   onchange="uploadFileNonStandardAuthorization(this,'non');"/>

                                            <input type="text"
                                                   class="input-middle"
                                                   id="name_non"
                                                   readonly="readonly"
                                                   placeholder="请上传附件"
                                                   name="nonStandardAuthorizationName"
                                                   onclick="file_non.click();"
                                                   value="${nonStandardAuthorizationName}">

                                            <input type="hidden"
                                                   id="uri_non"
                                                   name="nonStandardAuthorizationUrl"
                                                   value="${nonStandardAuthorizationUrl}">

                                            <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                   id="busUpload"
                                                   onclick="return $('#file_non').click();">浏览</label>

                                            <div class="font-red" style="display: none;">请上传附件</div>
                                        </div>


                                        <c:choose>
                                            <c:when test="${nonStandardAuthorizationUrl ne null && nonStandardAuthorizationUrl ne ''}">
                                                <div class="f_left ">
                                                    <i class="iconsuccesss ml7" id="img_icon_non"></i>

                                                    <a href="http://${domain}${nonStandardAuthorizationUrl}"
                                                       target="_blank"
                                                       class="font-blue cursor-pointer mr5 ml10 mt4"
                                                       id="img_view_non">查看</a>

                                                    <span class="font-red cursor-pointer mt4"
                                                          onclick="del('non')"
                                                          id="img_del_non">删除</span>
                                                </div>
                                            </c:when>
                                        </c:choose>

                                        <div class="clear"></div>
                                    </div>
                                </c:when>

                                <c:otherwise>
                                    <div class="c_1">
                                        <div class="pos_rel f_left">
                                            <input type="file"
                                                   class="upload_file"
                                                   name="lwfile"
                                                   id="file_non"
                                                   style="display: none;"
                                                   onchange="uploadFileNonStandardAuthorization(this,'non');"/>

                                            <input type="text"
                                                   class="input-middle"
                                                   id="name_non"
                                                   readonly="readonly"
                                                   placeholder="请上传附件"
                                                   name="nonStandardAuthorizationName"
                                                   onclick="file_non.click();"
                                            >

                                            <input type="hidden" id="uri_non" name="nonStandardAuthorizationUrl">

                                            <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                   id="busUpload"
                                                   onclick="return $('#file_non').click();">浏览</label>

                                            <div class="font-red" style="display: none;">请上传附件</div>
                                        </div>

                                        <!-- 上传成功出现 -->
                                        <i class="iconsuccesss ml7 none" id="img_icon_non"></i>
                                        <a href="" target="_blank"
                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                           id="img_view_non">查看</a>
                                        <span class="font-red cursor-pointer mt4 none" onclick="del('non')"
                                              id="img_del_non">删除</span>
                                        <div class="clear"></div>
                                    </div>
                                </c:otherwise>

                            </c:choose>

                            <!-- 上传成功出现 -->
                            <div class="pos_rel f_left">
                                <i class="iconsuccesss mt3 none" id="img_icon_non"></i>
                                <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none"
                                   id="img_view_non">查看</a>
                                <span class="font-red cursor-pointer  mt3 none" onclick="del('non')"
                                      id="img_del_non">删除</span>
                            </div>
                            <div class='clear'></div>
                        </div>
                    </div>
                    <div class="tip">
                        <input type="hidden"
                               id="whether_sign"
                               name="whetherSign"
                               value="${whetherSign}">
                        <div id="whether_sign_tip" style="display: none;color: #ff0000;font-size: 12px">文件无法识别自动盖电子章，该申请需在授权专员审批后联系财务盖鲜章</div>
                    </div>

                    <div class="tip">
                        <div style="font-size: 12px">仅限上传20M以内文本型PDF。</div>
                    </div>
                </div>
            </div>

            <div class="form-item">
                <div class="item-label">其他附件：</div>
                <div class="item-fields">
                    <input type="hidden" id="domain" name="domain" value="${domain}">
                    <div>

                        <div class="form-blanks">
                            <c:choose>
                                <c:when test="${!empty attachmentList }">
                                    <c:forEach items="${attachmentList }" var="bus" varStatus="st">
                                        <div class="mb8 c_1">
                                            <div class="pos_rel f_left ">
                                                <input type="file" class="upload_file" name="lwfile"
                                                       id="file_${st.index}" style="display: none;"
                                                       onchange="uploadFile(this,${st.index});"/>
                                                <c:choose>
                                                    <c:when test="${st.index == 0 }">
                                                        <input type="text" class="input-middle" id="name_${st.index}"
                                                               readonly="readonly"
                                                               placeholder="请上传附件" name="fileName"
                                                               onclick="file_${st.index}.click();"
                                                               value="${bus.name}">
                                                        <input type="hidden" id="uri_${st.index}" name="fileUri"
                                                               value="${bus.uri}">
                                                    </c:when>
                                                    <c:otherwise>
                                                        <input type="text" class="input-middle"
                                                               id="name_${st.index}" readonly="readonly"
                                                               placeholder="请上传附件" name="fileName"
                                                               onclick="file_${st.index}.click();"
                                                               value="${bus.name}">
                                                        <input type="hidden" id="uri_${st.index}" name="fileUri"
                                                               value="${bus.uri}">
                                                    </c:otherwise>
                                                </c:choose>
                                                <label class="bt-bg-style bt-middle bg-light-blue ml10"
                                                       id="busUpload"
                                                       onclick="return $('#file_${st.index}').click();">浏览</label>
                                                <div class="font-red " style="display: none;">请上传附件</div>
                                            </div>

                                            <c:choose>
                                                <c:when test="${bus.uri ne null && bus.uri ne ''}">
                                                    <div class="f_left ">
                                                        <i class="iconsuccesss ml7" id="img_icon_${st.index}"></i>
                                                        <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4"
                                                           id="img_view_${st.index}">查看</a>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="del(${st.index})" id="img_del_${st.index}">删除</span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4"
                                                                          onclick="delAttachment(this)" id="img_del_${st.index}">删除</span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                </c:when>
                                                <c:otherwise>
                                                    <div class="f_left ">
                                                        <i class="iconsuccesss ml7 none" id="img_icon_${st.index}"></i>
                                                        <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                           class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                           id="img_view_${st.index}">查看</a>
                                                        <c:choose>
                                                            <c:when test="${st.index == 0 }">
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="del(${st.index})" id="img_del_${st.index}">删除</span>
                                                            </c:when>
                                                            <c:otherwise>
                                                                    <span class="font-red cursor-pointer mt4 none"
                                                                          onclick="delAttachment(this)" id="img_del_${st.index}">删除</span>
                                                            </c:otherwise>
                                                        </c:choose>
                                                    </div>
                                                </c:otherwise>
                                            </c:choose>
                                            <div class="clear"></div>
                                        </div>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <div class="mb8 c_1">
                                        <div class="pos_rel f_left mb8">
                                            <input type="file" class="upload_file" name="lwfile" id="file_1"
                                                   style="display: none;" onchange="uploadFile(this,1);"/>
                                            <input type="text" class="input-middle" id="name_1" readonly="readonly"
                                                   placeholder="请上传附件" name="fileName" onclick="file_1.click();"
                                                   value="${bus.name}">
                                            <input type="hidden" id="uri_1" name="fileUri" value="${bus.uri}">
                                            <div class="font-red " style="display: none;">请上传附件</div>
                                        </div>
                                        <label class="bt-bg-style bt-middle bg-light-blue ml4" id="busUpload"
                                               onclick="return $('#file_1').click();">浏览</label>
                                        <!-- 上传成功出现 -->
                                        <c:choose>
                                            <c:when test="${!empty bus.uri}">
                                                <i class="iconsuccesss ml7" id="img_icon_4"></i>
                                                <a href="http://${bus.domain}${bus.uri}" target="_blank"
                                                   class="font-blue cursor-pointer mr5 ml10 mt4"
                                                   id="img_view_1">查看</a>
                                                <span class="font-red cursor-pointer mt4" onclick="del(1)"
                                                      id="img_del_1">删除</span>
                                            </c:when>
                                            <c:otherwise>
                                                <i class="iconsuccesss ml7 none" id="img_icon_1"></i>
                                                <a href="" target="_blank"
                                                   class="font-blue cursor-pointer mr5 ml10 mt4 none"
                                                   id="img_view_1">查看</a>
                                                <span class="font-red cursor-pointer mt4 none" onclick="del(1)"
                                                      id="img_del_1">删除</span>
                                            </c:otherwise>
                                        </c:choose>
                                        <div class="clear"></div>
                                    </div>
                                </c:otherwise>
                            </c:choose>
                            <div class="pos_rel f_left">
                                <i class="iconsuccesss mt3 none" id="img_icon_1"></i>
                                <a href="" target="_blank" class="font-blue cursor-pointer  mt3 none" id="img_view_1">查看</a>
                                <span class="font-red cursor-pointer  mt3 none" onclick="del(1)" id="img_del_1">删除</span>
                            </div>
                            <div class='clear'></div>
                        </div>


                        <div class="mt8" id="conadd">
                            <span class="bt-border-style bt-small border-blue" onclick="conadd();">继续添加</span>
                        </div>
                    </div>
                    <div class="tip">
                        <div style="font-size: 12px">仅限上传20M以内，WORD/PDF/JPG/PNG。</div>
                    </div>
                </div>
            </div>

            <div class="form-item">
                <div class="item-label">事项描述：</div>
                <div class="item-fields">
                <textarea class="input-textarea" name="described" id="described" placeholder="授权书若使用非标准模板，请说明原因，100字以内。" cols="30"
                          rows="5" maxlength="100">${authorizationApply.described}</textarea>
                </div>
            </div>
            <div class="form-item">
                <div class="item-label"><span style="color:red;">*</span>公章类型：</div>
                <div class="item-fields">
                    <select name="sealType" id="sealType" class="input-middle seal-type-select" onchange="changeSealType()">
                        <c:forEach items="${companyInfoList}" var="company">
                            <option value="${company.frontEndSeq}" <c:if test="${not empty authorizationStorage.sealType and authorizationStorage.sealType eq company.frontEndSeq}">selected</c:if>>${company.companyName}</option>
                        </c:forEach>
                       <%-- <option value="1" <c:if test="${empty authorizationApply.sealType or authorizationApply.sealType eq 1}">selected</c:if>>南京贝登医疗股份有限公司</option>
                        <option value="2" <c:if test="${authorizationApply.sealType eq 2}">selected</c:if>>南京医购优选供应链管理有限公司</option>--%>
                    </select>

                </div>
            </div>
            <div class="form-item">
                <div class="item-label"><span style="color:red;">*</span>份数：</div>
                <div class="item-fields"><input type="text" class="select-print" name="num" id="auNum" value="${authorizationApply.num}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^1-9]/g,'0')}else{this.value=this.value.replace(/\D/g,'')}"> &nbsp;&nbsp;份</span>
                </div>
            </div>

        </div>
    </div>

    <div class="add-tijiao pb15">
        <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF" onclick="tj()">
            提交
        </button>
        <%--<button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF" id="baocun"
                onclick="bc();">
            保存
        </button>--%><!-- 驳回之后不允许再次保存草稿 -->
        <button type="button" class="btn btn-blue btn-large pop-new-data" style="background-color: #1E9FFF" id="ch"
                layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./chApply.do?authorizationApplyId=${authorizationApplyId}"}'>
            撤回申请
        </button>
        <button type="button" class="btn btn-blue btn-large" style="background-color: #1E9FFF;"
                onclick="authorizationPre()">
            预览
        </button>
        <span style="display:none;"><div class="title-click nobor addtitle2" id="authorizationPreview"></div></span>
        <input type="hidden" value="${historicInfo.startUser}" id="sUser">
        <input type="hidden" value="${cuUserName}" id="sUserName">
    </div>

    <div class="parts tcenter" style="width: 800px;margin: 0 auto">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">审核记录</div>
        </div>
        <table class="table  table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th style="width:50px">操作人</th>
                <th style="width:80px">操作时间</th>
                <th style="width:80px">操作事项</th>
                <th style="width:80px">备注</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                <c:if test="${not empty  hi.activityName}">
                    <tr>
                        <td>
                            <c:choose>
                                <c:when test="${hi.activityType == 'startEvent'}">
                                    ${startUser}
                                </c:when>
                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                </c:when>
                                <c:otherwise>
                                    <c:if test="${historicActivityInstance.size() == status.count}">
                                        ${verifyUsers}
                                    </c:if>
                                    <c:if test="${historicActivityInstance.size() != status.count}">
                                        ${hi.assignee}
                                    </c:if>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                        <td>
                            <c:choose>
                                <c:when test="${hi.activityType == 'startEvent'}">
                                    开始
                                </c:when>
                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                    结束
                                </c:when>
                                <c:otherwise>
                                    ${hi.activityName}
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td class="font-red">${commentMap[hi.taskId]}</td>
                    </tr>
                </c:if>
            </c:forEach>
            </tbody>
        </table>
    </div>

</form>
<script type="text/javascript">
    $(function() {
        <c:if test="${(empty authorizationApply.standardTemplate) or (authorizationApply.standardTemplate eq 0)}">
        clickBiaoZhun();
        </c:if>
        $(".select-print").css("border-color","#0099FF");
        $(".input-print").css("border-color","#0099FF");

        // 页面加载完成后立即调用changeSealType函数，确保公司名称与选择的公章类型一致
        changeSealType();
    })
</script>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/order/quote/authorization_view_bh.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp" %>
