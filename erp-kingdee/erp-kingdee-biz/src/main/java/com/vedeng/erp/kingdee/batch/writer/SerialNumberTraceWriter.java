package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto;
import com.vedeng.erp.kingdee.batch.repository.SerialNumberTraceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockUpdateDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: sn码关联表数据推送金蝶
 * @date 2023/6/28 22:55
 */
@Service
@Slf4j
public class SerialNumberTraceWriter extends BaseWriter<KingDeeSaleOutStockUpdateDto> {
    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockService;

    @Autowired
    private SerialNumberTraceDtoMapper serialNumberTraceDtoMapper;

    @Override
    public void doWrite(KingDeeSaleOutStockUpdateDto kingDeeSaleOutStockUpdateDto, JobParameters params, ExecutionContext stepContext) throws Exception {
        KingDeeSaleOutStockDto kingDeeSaleOutStockDto = kingDeeSaleOutStockUpdateDto.getKingDeeSaleOutStockDto();
        kingDeeSaleOutStockService.update(kingDeeSaleOutStockDto);
        //更新is_push字段为1
        SerialNumberTraceDto serialNumberTraceDto = SerialNumberTraceDto.builder()
                .serialNumberTraceId(kingDeeSaleOutStockUpdateDto.getSerialNumberTraceId())
                .isPush(ErpConstant.ONE).build();
        serialNumberTraceDtoMapper.updateIsPushByPrimaryKey(serialNumberTraceDto);
    }
}
