package com.vedeng.order.enums;

import com.vedeng.common.constant.OrderConstant;
import lombok.Getter;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum SaleOrderTypeEnum {

    VS(OrderConstant.ORDER_TYPE_SALE, "销售订单"),
    JCO(OrderConstant.ORDER_TYPE_JCO, "集采线上订单"),
    JCF(OrderConstant.ORDER_TYPE_JCF, "集采线下订单"),
    ZXF(OrderConstant.ORDER_TYPE_ZXF, "线下直销订单"),
    ;

    private Integer type;
    private String message;

    SaleOrderTypeEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }


    /**
     * 判断是否为集采订单
     *
     * @param orderType
     * @return
     */
    public static boolean isJcOrder(Integer orderType) {
        return JCO.getType().equals(orderType) || JCF.getType().equals(orderType);
    }

    /**
     * 判断是否线下直销订单
     * @param orderType
     * @return
     */
    public static boolean isZxfOrder(Integer orderType) {
        return ZXF.getType().equals(orderType);
    }
}
