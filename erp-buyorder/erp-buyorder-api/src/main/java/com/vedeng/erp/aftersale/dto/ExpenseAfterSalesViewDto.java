package com.vedeng.erp.aftersale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @description 退票页信息
 * @date 2022/10/31 16:07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseAfterSalesViewDto {

    /**
     * 售后单id
     */
    private Long expenseAfterSalesId;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票号码
     */
    private String invoiceNo;
}
