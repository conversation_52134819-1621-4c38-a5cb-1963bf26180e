package com.vedeng.common.constant;

public enum RegisterPlatformEnum {
    // 注册平台（1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采,7区域商城
    BD(1,"贝登医疗"),YXG(2,"医械购"),<PERSON><PERSON>(3,"科研购"),<PERSON><PERSON>(4,"集团业务部"),OTHER(5,"其他"),J<PERSON>(6,"集采"),QY(7,"区域商城"),
    TMH(32,"科研特麦帮");
    private Integer registNo;
    private String name;
    private RegisterPlatformEnum(Integer i,String name){
        registNo=i;
        this.name = name;
    }
    public Integer getRegistNo(){return registNo;}
    public String getName(){return name;}
}
