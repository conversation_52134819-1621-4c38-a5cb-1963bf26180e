package com.newtask.service.impl;

import com.newtask.service.TraderLabelService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.BitsetUtils;
import com.vedeng.crm.api.dto.customergroup.TraderGroupEXTDto;
import com.vedeng.crm.api.dto.customergroup.TraderLabelEXTDto;
import com.vedeng.trader.dao.RTraderLabelJTraderMapper;
import com.vedeng.trader.model.RTraderLabelJTrader;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.BitSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class TraderLabelServiceImpl extends TraderGroupOrLabelService implements TraderLabelService {
    private Logger logger = LoggerFactory.getLogger(TraderGroupServiceImpl.class);

    @Resource
    private RTraderLabelJTraderMapper rTraderLabelJTraderMapper;
    @Override
    public ResultInfo<?> handleTraderLabel(List<TraderLabelEXTDto> traderLabelList, List<Integer> organizations) {
        for (TraderLabelEXTDto lab : traderLabelList) {
            if (lab == null || lab.getTraderLabelId() == null || lab.getRuleCondition() == null) {
                continue;
            }
            ResultInfo<?> result = handleOneLabel(lab, organizations);
            if (result.getStatus().equals(0)) {
                saveResultToCrm(lab.getTraderLabelId(), 1, "label");
            } else {
                saveResultToCrm(lab.getTraderLabelId(), 0, "label");
            }

        }
        return new ResultInfo(0, "操作成功");
    }

    @Override
    public ResultInfo handleOneLabel(TraderLabelEXTDto label, List<Integer> organizations) {
        if (label == null || label.getTraderLabelId() == null) {
            return new ResultInfo(-1, "客户群信息为空");
        }

        if (label.getRuleCondition() == null) {
            return new ResultInfo(-1, "操作失败，生效规则不得为空");
        }
        try {
            BitSet result = getBaseTraderIds(label, organizations);
            logger.info("基本信息结果集",result.toString());
            solveTraderIds(result, label);
            if (result.length() == 0) {
                updateTraderLabelRelation(result, label.getTraderLabelId());
                return new ResultInfo(0, "操作成功");
            }
            if (CollectionUtils.isEmpty(label.getBehaviorList())) {
                updateTraderLabelRelation(result, label.getTraderLabelId());
                return new ResultInfo(0, "操作成功");
            }
            ResultInfo behaviorRes=solveBehaviors(result,label.getBehaviorList(),label);
            if(behaviorRes.getCode()==-1){
                return behaviorRes;
            }
            updateTraderLabelRelation(result, label.getTraderLabelId());
        } catch (Exception ex) {
            logger.error("处理单个分群报错", ex);
            return new ResultInfo(-1, "处理单个分群出错");
        }
        return new ResultInfo(0, "操作成功");
    }

    /**
     * <b>Description:</b>更新客户分群信息<br>
     *
     * @param
     * @return
     * @Note <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/5/27
     */
    private void updateTraderLabelRelation(BitSet result, Integer labelId) {
        Page page = new Page(1, 1000);
        int currentPage = 1;
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("labelId", labelId);
        queryMap.put("page", page);
        BitSet old = new BitSet();
        do {
            List<Integer> oldTraderIds = rTraderLabelJTraderMapper.getTraderIdsListPage(queryMap);
            if (CollectionUtils.isNotEmpty(oldTraderIds)) {
                for (Integer id : oldTraderIds) {
                    if (id == null) {
                        continue;
                    }
                    old.set(id);
                }
            }
            currentPage++;
            page.setPageNo(currentPage);
        } while (currentPage <= page.getTotalPage());
        List<Integer> insertIds = null;
        if (old.length() == 0) {
            insertIds = BitsetUtils.bitSet2List(result);
        } else {
            BitSet oldOne = (BitSet) old.clone();
            old.andNot(result);
            List<Integer> deleteTraderIds = BitsetUtils.bitSet2List(old);
            if(CollectionUtils.isNotEmpty(deleteTraderIds)) {
                rTraderLabelJTraderMapper.deleteByLabelIdAndTraderIds(labelId, deleteTraderIds);
            }
            result.andNot(oldOne);
            insertIds = BitsetUtils.bitSet2List(result);
        }
        if (CollectionUtils.isNotEmpty(insertIds)) {
            for (Integer id : insertIds) {
                if (id == null) {
                    continue;
                }
                RTraderLabelJTrader rTraderGroupJTrader = new RTraderLabelJTrader();
                rTraderGroupJTrader.setTraderLabelId(labelId);
                rTraderGroupJTrader.setTraderId(id);
                rTraderLabelJTraderMapper.insert(rTraderGroupJTrader);
            }
        }
    }
}
