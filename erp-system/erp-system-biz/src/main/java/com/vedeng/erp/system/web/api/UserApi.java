package com.vedeng.erp.system.web.api;

import cn.hutool.core.collection.CollectionUtil;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.web.api
 * @Date 2022/7/15 17:22
 */
@ExceptionController
@RestController
@RequestMapping("/user")
@Slf4j
public class UserApi {

    @Autowired
    private UserApiService userApiService;

    /**
     * 获取所有下属包括自己
     */
    @RequestMapping(value = "/getAllSubUserList")
    @NoNeedAccessAuthorization
    public R<?> getAllSubUserList() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin())
                .username(currentUser.getUsername())
                .companyId(currentUser.getCompanyId())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();
//        return R.success(userApiService.getAllSubUserList(userDto, Collections.singletonList(310), false));


        List<Integer> positionType = Collections.singletonList(310);
        List<UserDto> allSubUserList = userApiService.getAllSubUserList(userDto, positionType, false);
        List<UserDto> userListByOrgId = userApiService.getMyUserListByUserOrgsList(userDto,positionType,false);
        if(CollectionUtil.isNotEmpty(userListByOrgId)){

            Set<Integer> userIds = new HashSet<>();
            for (UserDto u : allSubUserList) {
                userIds.add(u.getUserId());
            }
            // 遍历 userListByOrgId 并添加那些 userId 不在 userIds Set 中的 User 对象
            for (UserDto newUser : userListByOrgId) {
                if (!userIds.contains(newUser.getUserId())) {
                    allSubUserList.add(newUser);
                    userIds.add(newUser.getUserId()); // 更新 userIds Set
                }
            }
            // 此时 userList 包含了原始用户以及新添加的，没有重复 userId 的用户

            // 使用自定义比较器进行排序，不区分大小写
            Collections.sort(allSubUserList, new Comparator<UserDto>() {
                @Override
                public int compare(UserDto u1, UserDto u2) {
                    return u1.getUsername().compareToIgnoreCase(u2.getUsername());
                }
            });
        }
        return R.success(allSubUserList);

    }

    @RequestMapping(value = "/getUserList")
    @NoNeedAccessAuthorization
    public R<?> getUserList() {
        return R.success(userApiService.getUserList());
    }

    /**
     * 根据职位类型查询职位下的所有用户
     *
     * @param positionType 职位类型
     * @return List<UserDto>
     */
    @RequestMapping(value = "/getByPositionType")
    @NoNeedAccessAuthorization
    public R<List<UserDto>> getByPositionType(@RequestParam Integer positionType) {
        return R.success(userApiService.getUserByPositionType(positionType));
    }
    @RequestMapping(value = "/getUserPermissionForSmartQuote")
    @NoNeedAccessAuthorization
    public R<?> getUserPermissionForAIQuote(String jobNumber){
        if (null == jobNumber){
            return R.error("请传入工号");
        }
        return R.success(userApiService.getUserPermissionForAIQuote(Integer.valueOf(jobNumber)));
    }

    /**
     * 全部在职人员（ERP账号不为离职）
     */
    @RequestMapping(value = "/getAllNotDisabledUserList")
    @NoNeedAccessAuthorization
    public R<?> getAllNotDisabledUserList() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        UserDto userDto = UserDto.builder().isAdmin(currentUser.getIsAdmin())
                .username(currentUser.getUsername())
                .companyId(currentUser.getCompanyId())
                .positType(currentUser.getPositType())
                .userId(currentUser.getId())
                .build();
        return R.success(userApiService.getAllSubUserList(userDto, null, false));
    }

    /**
     * 所属部门查询
     * @param userId
     * @return
     */
    @RequestMapping(value = "/getOrgName")
    @NoNeedAccessAuthorization
    public R<String> getOrgName(@RequestParam Integer userId) {
        List<String> orgNameList = userApiService.getOrgNameList(userId);
        if(CollectionUtil.isNotEmpty(orgNameList)){
            return R.success("success",String.join(",",orgNameList));
        }
        return R.success("success","");
    }
    
}
