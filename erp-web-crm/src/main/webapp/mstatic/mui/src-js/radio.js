

Vue.component('ui-radio', {
    template: `
        <div
            class="vd-ui-radio-item"
            :class="{
                'vd-ui-radio-item-checked': currentChecked,
                'vd-ui-radio-item-disabled': disabled,
                'vd-ui-radio-item-labeltype': type === 'label'
            }"
            @click="handlerClick()"
        >
            <div class="vd-ui-radio-inner">
                <div class="vd-ui-radio-icon">
                    <div class="vd-ui-radio-icon-selected"></div>
                </div>
                <span class="vd-ui-radio-label">{{ label }}</span>
                <span class="vd-ui-radio-tip">{{ tip }}</span>
            </div>
        </div>
    `,

    props: {
        label: {
            type: String,
            default: "",
        },
        value: {
            type: String,
        },
        tip: {
            type: String,
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: '' //label
        }
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (!this.disabled && (this.clearable || !this.currentChecked)) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})

Vue.component('ui-radio-group', {
    template: `
        <div class="vd-ui-radio-group" :class="{'is-label': type==='label'}">
            <template v-for="(item, index) in boxList">
                <ui-radio
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :tip="item.tip"
                    :checked.sync="item.checked"
                    :disabled="disabled || item.disabled"
                    :clearable="clearable"
                    @change="handlerChange(index, $event)"
                    :type="type"
                ></ui-radio>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,

    watch: {
        list: {
            handler () {
                this.setList();
            },
            deep: true,
            immediate: true
        },
        value (newV) {
            this.setList();
        },
    },

    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: "",
        clearable: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: '' //label
        }
    },
    mounted() {
        // this.$form.setValidEl(this);
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                if (item.value == this.value) {
                    item.checked = true;
                }
            });
        },
        handlerChange(index, data) {
            let valueItem = null;
            this.boxList.forEach((item, i) => {
                if(index === i) {
                    this.$set(this.boxList[i], "checked", data);
                } else {
                    this.$set(this.boxList[i], "checked", false);
                }
                if (item.checked) {
                    valueItem = item;
                }
            });

            let value = valueItem ? valueItem.value : '';

            if (value !== this.value) {
                this.checkValid(value);
                this.$emit("input", value);
                this.$emit("change", valueItem);
            }
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})
