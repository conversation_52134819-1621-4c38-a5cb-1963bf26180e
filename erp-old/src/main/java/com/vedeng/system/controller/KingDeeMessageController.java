package com.vedeng.system.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.system.model.ListConfig;
import com.vedeng.system.service.ExceptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpSession;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/7/15
 */
@Controller
@RequestMapping("/system/list")
public class KingDeeMessageController extends BaseController {


    @Autowired
    private ExceptService exceptService;


    @RequestMapping(value = "/deleteKingDeeEventMsgById", method = RequestMethod.GET)
    @ResponseBody
    public ResultInfo<ListConfig> getListConfigurationInfo(@RequestParam Integer kingDeeEventMsgId,
                                                           HttpSession session){
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        Integer userId = user.getUserId();
        exceptService.deleteEventMsgByMessageid(kingDeeEventMsgId,userId);

        ResultInfo<ListConfig> resultInfo = new ResultInfo<>();
        resultInfo.setCode(0);
        resultInfo.setData(null);
        resultInfo.setMessage("删除成功");
        return resultInfo;
    }




}
