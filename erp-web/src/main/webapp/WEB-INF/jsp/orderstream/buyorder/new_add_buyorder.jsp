<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="新增采购" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/add_edit_buyorder.js?rnd=${resourceVersionKey}'>
</script>

<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>

<style>
    .rebate_class > div {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: row;
        height: 100%;
    }
</style>
<script type="text/javascript">

    function recieveTraderInfo(traderId, traderSupplierName) {

        var skuNos = [];
        var buyorderGoodsIdList = [];
        var isGift = [];

        <c:forEach var="bgv" items="${buyorderVo.buyorderGoodsVoList}" varStatus="num">
        if ('${bgv.sku}' != 'V127063') {
            skuNos.push('${bgv.sku}');
            isGift.push('${bgv.isGift}')
            buyorderGoodsIdList.push('${bgv.buyorderGoodsId}');
        }
        </c:forEach>

        for (var i = 0; i < skuNos.length; i++) {
            if (isGift[i] == 0) {
                $.ajax({
                    url: page_url + '/order/buyorder/getSkuPriceInfoBySkuNo.do',
                    data: {"skuNo": skuNos[i]},
                    type: "POST",
                    dataType: "json",
                    async: false,
                    success: function (data) {
                        if (data.code == 0) {

                            var skuNo = data.data.skuNo;

                            //如果sku有对应的价格信息
                            var purchaseList = data.data.purchaseList;

                            for (var j = 0; j < purchaseList.length; j++) {
                                if (purchaseList[j].traderId == traderId) {
                                    $("#price" + skuNo).val(purchaseList[j].purchasePrice);
                                    $("#price" + skuNo).attr("readonly", "true");
                                    $("#price" + skuNo).blur();
                                    $("#priceValue" + skuNo).val(buyorderGoodsIdList[i] + "|" + purchaseList[j].purchasePrice);
                                    return;
                                }
                            }

                            $("#price" + skuNo).removeAttr("readonly");

                        } else {
                            $("#price" + data.data).removeAttr("readonly");
                        }
                    },
                    error: function (data) {
                        if (data.status == 1001) {
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                        }
                    }
                });
            }

        }
    }

</script>
<body>
<form action="${pageContext.request.contextPath}/order/newBuyorder/newSaveCreateOrEditBuyorder.do" method="post" id="myform">

    <input type="hidden" name="isNew" value="${buyorderVo.isNew}">
    <input type="hidden" name="buyorderNo" value="${buyorderVo.buyorderNo}">
    <input type="hidden" name="isBhOrder" value="${isBhOrder}" id="isBhOrder">
    <div class="t-line-wrap J-line-wrap" style="margin-bottom: 20px;margin-top: 30px;margin-left: 8%;"
         data-json='[{"label":"待确认","status":2},{"label":"审核中"},{"label":"待付款"},{"label":"待发货"},{"label":"待收货"},{"label":"待收票"},{"label":"已完结"}]'>
    </div>

    <div class="pb100 content pt10">
        <!-- ----------------------------------基本信息 ------------------------------------- -->
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">基本信息</div>
            </div>
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <tbody>
                <tr>
                    <td class="table-smaller">采购单号</td>
                    <td>${buyorderVo.buyorderNo}</td>
                    <td>是否直发</td>
                    <c:if test="${buyorderVo.deliveryDirect eq 0}">
                        <td>否</td>
                    </c:if>
                    <c:if test="${buyorderVo.deliveryDirect eq 1}">
                        <td>是</td>
                    </c:if>

                </tr>
                <tr>
                    <td>创建人</td>
                    <td>${buyorderVo.createName}</td>
                    <td>部门</td>
                    <td>${buyorderVo.buyDepartmentName}</td>
                </tr>
                <tr>
                    <td>创建时间</td>
                    <td><date:date value="${buyorderVo.addTime}"/></td>
                    <td class="table-smaller">订单状态</td>
                    <td>
                        <c:choose>
                            <c:when test="${buyorderVo.status eq 0}">
                                <span>待确认</span>
                            </c:when>
                            <c:when test="${buyorderVo.status eq 1}">
                                <span>进行中</span>
                            </c:when>
                            <c:when test="${buyorderVo.status eq 2}">
                                <span>已完结</span>
                            </c:when>
                            <c:otherwise>
                                <span>已关闭</span>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <input type="hidden" name="beforeParams" value="${beforeParams}"/>
        <input type="hidden" name="uri" value="${uri}"/>
        <input type="hidden" name="deliveryDirect" id="deliveryDirect" value="${buyorderVo.deliveryDirect}"/>
        <input type="hidden" name="buyorderId" value="${buyorderVo.buyorderId}"/>
        <input type="hidden" name="status" value="${buyorderVo.status}"/>
        <input type="hidden" name="goodsIds" value="${buyorderVo.goodsIds}"/>
        <input type="hidden" name="saleorderGoodsIds" value="${buyorderVo.saleorderGoodsIds}"/>
        <input type="hidden" name="formToken" value="${formToken}"/>
        <input type="hidden" name="orderType" value="${buyorderVo.orderType}"/>
        <input type="hidden" id="isGiftOrder" value="${buyorderVo.isGift}"/>
        <input type="hidden" value=true id="isAdd">
        <!-- -----------------------------------供应商信息------------------------------------------------>
        <div class="parts content1 ">
            <div class="title-container">
                <div class="table-title nobor">
                    供应商信息
                </div>
            </div>
            <div style="margin-top: 15px">
                <ul class="payplan" style="margin-bottom:0;">
                    <li>
                        <div class="infor_name mt0">
                            <span>*</span>
                            <label>供应商</label>
                        </div>
                        <div class="f_left">
                            <div class='inputfloat'>
                                <c:if test="${buyorderVo.traderId ne 0}">
                                    <span id="name">${buyorderVo.traderName}</span>
                                    <span id="certificateOverdue" style="color: red;display: none">(已过期)</span>
                                    <label class="bt-bg-style bg-light-blue bt-small" onclick="research();"
                                           id="research">重新搜索</label>
                                    <input type="text" placeholder="请输入供应商名称" class="input-largest none"
                                           name="searchTraderName" id="searchTraderName"
                                           value="${buyorderVo.traderName}">
                                    <label class="bt-bg-style bg-light-blue bt-small none" onclick="searchSupplier();"
                                           id="errorMes">搜索</label>
                                </c:if>
                                <c:if test="${buyorderVo.traderId eq 0}">
                                    <span class="none" id="name">${buyorderVo.traderName}</span>
                                    <span id="certificateOverdue" style="color: red;display: none">(已过期)</span>
                                    <label class="bt-bg-style bg-light-blue bt-small none" onclick="research();"
                                           id="research">重新搜索</label>
                                    <input type="text" placeholder="请输入供应商名称" class="input-largest"
                                           name="searchTraderName" id="searchTraderName"
                                           value="${buyorderVo.traderName}">
                                    <label class="bt-bg-style bg-light-blue bt-small" onclick="searchSupplier();"
                                           id="errorMes">搜索</label>
                                </c:if>
                                <span style="display:none;">
									<!-- 弹框 -->
									<div class="title-click nobor  pop-new-data" id="popSupplier"></div>
								</span>
                                <input type="hidden" id="traderSupplierId" name="traderSupplierId"/>
                                <input type="hidden" id="validRebateCharge" name="validRebateCharge"/>
                                <input type="hidden" id="usedRebateCharge" name="usedRebateCharge" value="0.00"/>
                                <input type="hidden" id="traderId" name="traderId" value="${buyorderVo.traderId}"/>
                                <input type="hidden" id="traderName" name="traderName"
                                       value="${buyorderVo.traderName}"/>
                                <input type="hidden" id="periodBalance" name="periodBalance"
                                       value="${buyorderVo.periodBalance}"/>
                            </div>
                            <div class="font-red none"></div>
                        </div>
                    </li>

                    <li
                            <c:if test="${buyorderVo.traderContactId eq 0}">class="none "</c:if> id="contact">
                        <div class="infor_name">
                            <span>*</span>
                            <label>联系人</label>
                        </div>
                        <div class="f_left">
                            <div class='inputfloat'>
                                <select id="traderContactId" name="traderContactStr" class="input-largest">
                                    <option value="">请选择</option>
                                    <c:forEach var="contact" items="${contactList}" varStatus="status">
                                        <option value="${contact.traderContactId}|${contact.name}|${contact.mobile}|${contact.telephone}"
                                                <c:if test="${contact.traderContactId eq buyorderVo.traderContactId }">selected="selected"</c:if>>${contact.name}/${contact.mobile}/${contact.telephone}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="font-red none">联系人不允许为空</div>
                        </div>
                    </li>
                    <li
                            <c:if test="${buyorderVo.traderAddressId eq 0}">class="none "</c:if> id="address">
                        <div class="infor_name">
                            <span>*</span>
                            <label>联系地址</label>
                        </div>
                        <div class="f_left">
                            <div class='inputfloat'>
                                <select id="traderAddressId" name="traderAddressStr" class="input-largest">
                                    <option value="">请选择</option>
                                    <c:forEach var="add" items="${tarderAddressList}" varStatus="status">
                                        <option value="${add.traderAddress.traderAddressId}|${add.area}|${add.traderAddress.address}"
                                                <c:if test="${add.traderAddress.traderAddressId eq buyorderVo.traderAddressId }">selected="selected"</c:if>>${add.area}&nbsp;&nbsp;${add.traderAddress.address}</option>
                                    </c:forEach>
                                </select>
                            </div>
                            <div class="font-red none">联系地址不允许为空</div>
                        </div>
                    </li>
                    <li
                            <c:if test="${buyorderVo.traderContactId eq 0}">class="none "</c:if> id="comment">
                        <div class="infor_name mt0">
                            <label>供应商备注:</label>
                        </div>
                        <div class="f_left" id="supplierComments">
                        </div>
                        <input type="hidden" id="traderComments" name="traderComments"
                               value="${buyorderVo.traderComments}"/>
                    </li>
                </ul>
            </div>
        </div>
        <%--赠品文案--%>
        <c:if test="${bgAllGift}">
            <div style="font-size: 14px;background-color: #ffbe80;width: 400px;margin: 5px 0;font-weight: bold">
                <span style="color: red">!&nbsp;&nbsp;</span>当前均为赠品商品，请选择真实的供应商并维护赠品参考价。
            </div>
        </c:if>
        <!-- ----------------------------------产品信息 ------------------------------------- -->
        <div class='parts table-style77'>
            <div class="title-container mb10" style="margin-bottom: 0;">
                <div class="table-title nobor">产品信息</div>

                <c:if test="${buyorderVo.geInfoFlag ne 0}">
                    <div class="title-click nobor ">
                        <a onclick="checkGeInfo(${buyorderVo.buyorderId})">添加GE采集信息</a>
                    </div>
                </c:if>
            </div>
            <c:forEach var="bgv" items="${buyorderVo.buyorderGoodsVoList}" varStatus="num">
                <table class="table table-style7" <c:if test="${bgv.goodsId eq 127063}">id="yf"</c:if>>
                    <thead>
                    <tr>
                        <th class="wid3">选择</th>
                        <th class="wid4">序号</th>
                        <th class="wid6">订货号</th>
                        <th class="wid10">产品名称</th>
                        <th class="wid8">品牌</th>
                        <th class="wid6">规格/型号</th>
                        <th class="wid6">采购数量</th>
                        <th class="wid6">单位</th>
                        <th class="wid8">可用库存/库存量</th>
                        <th class="wid6">在途数量</th>
                        <c:if test="${bgv.isGift eq 1 && isBhOrder}"><th class="wid10">赠品参考价</th></c:if>
                        <c:if test="${bgv.isGift ne 1 && isBhOrder}"><th class="wid10">是否为赠品</th></c:if>
                        <c:if test="${!isBhOrder && bgv.isGift eq 1}"><th class="wid10">赠品参考价</th></c:if>
                        <c:if test="${!isBhOrder}"><th class="wid10">是否为赠品</th></c:if>
                        <th class="wid9">单价</th>
                        <th class="wid9">总额</th>
                        <th class="wid9">采购预计发货日</th>
                        <th class="wid9">采购预计到货日</th>
                        <th class="wid10">货期（天）</th>
                        <th class="wid8">是否有授权</th>
                        <th class="wid14 rebate_class">
                            <div>
                                返利信息
                                <label class="bt-bg-style bg-light-blue bt-smaller" style="margin-left: 10px" onclick="rebate(${bgv.buyorderId},${bgv.buyorderGoodsId},${bgv.goodsId})">编辑</label>
                                <span class="title-click nobor  pop-new-data" id="rebateIframe"></span>
                            </div>
                        </th>
                        <th class="wid8">内部备注</th>
                        <th class="wid8">安调信息</th>
                        <th class="wid8">采购备注</th>
                        <th class="wid8">实际采购价</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <%--返利信息传递给后端--%>
                        <input type="hidden" name="goodsList[${num.index}].buyorderGoodsId" value="${bgv.buyorderGoodsId}"/>
                        <input type="hidden" name="goodsList[${num.index}].rebateAmount" id="goodsListRebateAmount${bgv.goodsId}"/>
                        <input type="hidden" name="goodsList[${num.index}].rebatePrice" id="goodsListRebatePrice${bgv.goodsId}"/>
                        <input type="hidden" name="goodsList[${num.index}].rebateAfterPrice" id="goodsListRebateAfterPrice${bgv.goodsId}"/>
                        <input type="hidden" name="goodsList[${num.index}].rebateNum" id="goodsListRebateNum${bgv.goodsId}"/>
                        <input type="hidden" name="goodsList[${num.index}].goodsName" value="${bgv.goodsName}"/>
                            
                        <input type="hidden" name="goodsList[${num.index}].actualPurchasePrice" id="goodsListActualPurchasePrice${bgv.goodsId}" value="${bgv.actualPurchasePrice}" />

                        <input type="hidden" name="buyorderGoodsId" value="${bgv.buyorderGoodsId}"/>
                        <td>
                            <c:choose>
                                <c:when test="${bgv.geSkuFlag ne 0}">
                                    <input type="checkbox" name="oneSelect" alt="${bgv.buyorderGoodsId}"/>
                                    <input type="hidden" name="geBuyorderGoodsId" value="${bgv.buyorderGoodsId}">
                                </c:when>
                                <c:otherwise>
                                    <span style="text-align: center"><input type="checkbox" disabled></span>
                                </c:otherwise>
                            </c:choose>
                        </td>
                        <td>${num.count}</td>
                        <td>${newSkuInfosMap[bgv.sku].SKU_NO}</td>
                        <td class="text-left">
                            <c:if test="${bgv.isGift eq 1}">
                            <span style="color: red">赠品&nbsp;</span>
                            </c:if>
                            <div class="customername pos_rel" style="display: inline">
									<span class="font-blue cursor-pointer addtitle"
                                          tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
													"link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.goodsName}
													&nbsp;<i class="iconbluemouth contorlIcon"></i><br/></span>
                                <c:set var="skuNo" value="${bgv.sku}"></c:set>
                                <%@ include file="../../common/new_sku_common_tip.jsp" %>
                            </div>
                        </td>
                        <td>${newSkuInfosMap[bgv.sku].BRAND_NAME}</td>
                        <td>${newSkuInfosMap[bgv.sku].SPEC}/${newSkuInfosMap[bgv.sku].MODEL}</td>

                        <td>
	                        <span altTotal="${bgv.goodsId}" class="buySum">
<%--		                        <c:if test="${bgv.num ==0}">${bgv.buySum}</c:if>--%>
<%--		                        <c:if test="${bgv.num !=0}">${bgv.num}</c:if>--%>
                                ${bgv.buySum}
	                        </span>
                            <c:if test="${bgv.goodsId ne 127063}"> </c:if>
                            <c:if test="${bgv.num ==0}">
                                <input type="hidden" name="buySum" alt="${bgv.buyorderGoodsId}"
                                       value="${bgv.buyorderGoodsId}|${bgv.buySum}"/>
                            </c:if>
                            <c:if test="${bgv.num !=0}">
                                <input type="hidden" name="buySum" alt="${bgv.buyorderGoodsId}"
                                       value="${bgv.buyorderGoodsId}|${bgv.num}"/>
                            </c:if>

                        </td>
                        <td>${newSkuInfosMap[bgv.sku].UNIT_NAME}</td>
                        <td>
                            <c:if test="${bgv.goodsId ne 127063}"></c:if>
                                ${bgv.canUseGoodsStock != null ? bgv.canUseGoodsStock : 0}/${bgv.goodsStock == null ? 0 : bgv.goodsStock}

                        </td>

                        <td>${bgv.onWay}</td>
                        <c:if test="${bgv.isGift eq 1 && isBhOrder}">
                        <td>
                            <input id="referprice${bgv.sku}" type="text"
                                   class="wid11 warning-color1" name="xreferPrice" alt3="${bgv.goodsId}" altIsGift="${bgv.isGift}"
                                   value='<fmt:formatNumber type="number" value="${bgv.referPrice}" pattern="0.00" maxFractionDigits="2" />'
                                   autocomplete="off"
                                   onblur="expensePrice2(this,${bgv.buyorderGoodsId})">
                            <input id="priceValue${bgv.sku}" type="hidden" name="referPrice" alt="${bgv.goodsId}"
                                   value="${bgv.buyorderGoodsId}|${bgv.referPrice}">


                            <input type="hidden" class="f_left" type="checkbox" name="isGiftCheck" altGiftCheck="${bgv.goodsId}" altIsGift="${bgv.isGift}" altGiftBuyorderGoodsId="${bgv.buyorderGoodsId}" autocomplete="off" id="isGiftCheck" <c:if test="${bgv.isGift == 1}">checked="checked"</c:if>
                                   onchange="changeBuyOrderIsGift(${bgv.buyorderGoodsId}, ${bgv.goodsId})">

                        </td>
                        </c:if>
                        <c:if test="${bgv.isGift ne 1 && isBhOrder}">
                            <td>
                                <input class="f_left" type="checkbox" name="isGiftCheck" altGiftCheck="${bgv.goodsId}" altGiftBuyorderGoodsId="${bgv.buyorderGoodsId}" autocomplete="off" id="isGiftCheck"
                                       onchange="changeBuyOrderIsGift(${bgv.buyorderGoodsId}, ${bgv.goodsId})" <c:if test="${bgv.isGift eq 1}">checked="checked"</c:if>>选为赠品商品
                            </td>
                        </c:if>
                        <input type="hidden" name="isGiftGoods" altGift="${bgv.buyorderGoodsId}" value='${bgv.buyorderGoodsId}|${bgv.isGift}'>
                        <c:if test="${!isBhOrder}">
                            <c:if test="${bgv.isGift eq 1}">
                            <td>
                                <input id="referprice${bgv.sku}" type="text"
                                       class="wid11 warning-color1" name="xreferPrice" alt3="${bgv.goodsId}" altIsGift="${bgv.isGift}"
                                       value='<fmt:formatNumber type="number" value="${bgv.referPrice}" pattern="0.00" maxFractionDigits="2" />'
                                       autocomplete="off"
                                       onblur="expensePrice2(this,${bgv.buyorderGoodsId})">
                                <input id="priceValue${bgv.sku}" type="hidden" name="referPrice" alt="${bgv.goodsId}"
                                       value="${bgv.buyorderGoodsId}|${bgv.referPrice}">
                            </td>
                            </c:if>
                            <td>
                                <input class="f_left" type="checkbox" name="isGiftCheck" altGiftCheck="${bgv.goodsId}" altGiftBuyorderGoodsId="${bgv.buyorderGoodsId}" autocomplete="off" id="isGiftCheck"
                                       onchange="changeBuyOrderIsGift(${bgv.buyorderGoodsId}, ${bgv.goodsId})" <c:if test="${bgv.isGift eq 1}">checked="checked"</c:if>>选为赠品商品
                            </td>
                        </c:if>
                            <input id="tempNum${bgv.goodsId}" type="hidden" value="${saleorderGoods.saleBuyNum}"/>
                            <input id="tempPrice${bgv.sku}" type="hidden" value="${bgv.price}" />
                        <td>
                            <input id="price${bgv.sku}" type="text" altTotal="${bgv.goodsId}"
                                   class="wid11 warning-color1" name="xprice"
                                   value="<fmt:formatNumber type="number" value="${bgv.price}" pattern="0.00" maxFractionDigits="2" />"
                                   autocomplete="off"
                                   onblur="changPrice(this,${bgv.buyorderGoodsId},${bgv.goodsId});"
                            >
                            <input id="priceValue${bgv.sku}" type="hidden" name="price" alt="${bgv.goodsId}"
                                   value="${bgv.buyorderGoodsId}|${bgv.price}">

                        </td>
                        <td>
                        	<span class="oneAllMoney" alt="${bgv.buyorderGoodsId}" altGood="${bgv.goodsId}">
                        		<fmt:formatNumber type="number" value="${bgv.oneBuyorderGoodsAmount}" pattern="0.00"
                                                  maxFractionDigits="2"/>
                        	</span>
                        </td>
                        <td>
                            <input name="xSendGoodsTime" id="xSendGoodsTime${bgv.buyorderGoodsId}"
                                   class="Wdate f_left m0 input-smaller96 warning-color1"
                                   onblur="changeSendGoodsTime(this,${bgv.buyorderGoodsId})"
                                   autocomplete="off" type="text" altSendGoodsTime="${bgv.buyorderGoodsId}"
                                   placeholder="请选择日期"
                                   onClick="WdatePicker()"
                                   value='<date:date value ="${bgv.sendGoodsTime}" format="yyyy-MM-dd"/>'/>
                            <input id="sendGoodsTimeValue${bgv.buyorderGoodsId}" type="hidden" name="sendGoodsTimeStr"
                                   alt="${bgv.buyorderGoodsId}"
                                   value='${bgv.buyorderGoodsId}|<date:date value ="${bgv.sendGoodsTime}" format="yyyy-MM-dd"/>'>
                        </td>

                        <td>
                            <input name="xReceiveGoodsTime" id="xReceiveGoodsTime${bgv.buyorderGoodsId}"
                                   class="Wdate f_left input-smaller96 warning-color1"
                                   onblur="changeReceiveGoodsTime(this,${bgv.buyorderGoodsId})"
                                   autocomplete="off" type="text" altReceiveGoodsTime="${bgv.buyorderGoodsId}"
                                   placeholder="请选择日期"
                                   onClick="WdatePicker()"
                                   value='<date:date value ="${bgv.receiveGoodsTime}" format="yyyy-MM-dd"/>'/>
                            <input id="receiveGoodsTimeValue${bgv.buyorderGoodsId}" type="hidden"
                                   name="receiveGoodsTimeStr" alt="${bgv.buyorderGoodsId}"
                                   value='${bgv.buyorderGoodsId}|<date:date value ="${bgv.receiveGoodsTime}" format="yyyy-MM-dd"/>'>
                        </td>
                        <td>
                            <input name="deliveryCycleDispaly" alt="${bgv.buyorderGoodsId}" type="text"
                                   placeholder="如（3）天" class="wid8" value="${bgv.deliveryCycle}"
                                   onblur="changDeliveryCycleComments(this,${bgv.buyorderGoodsId});">
                            <input type="hidden" name="deliveryCycle"
                                   value="${bgv.buyorderGoodsId}|${bgv.deliveryCycle}">
                        </td>

                        <td>
                            <select name="isHaveAuthInput" id="isHaveAuthInput" onchange="changeAuth(this,${bgv.buyorderGoodsId});">
                                <option value="1">是</option>
                                <option value="0">否</option>
                            </select>
                            <input type="hidden" name="isHaveAuth" value="${bgv.buyorderGoodsId}">
                        </td>
                        <td class="text-left">
                            <span  style="display: block">返利总额：&nbsp;<span name="totalRebate" id="totalRebate${bgv.goodsId}"></span></span>
                            <span  style="display: block">返利单价：&nbsp;<span name="rebatePrice" id="rebatePrice${bgv.goodsId}"></span></span>
                            <span  style="display: block">返利后单价：&nbsp;<span name="afterRebatePrice" id="afterRebatePrice${bgv.goodsId}"></span></span>
                        </td>

                        <td>
                            <textarea name="goodsCommentsDispaly" alt="${bgv.buyorderGoodsId}" class="wid7"
                                      onblur="changComments(this,${bgv.buyorderGoodsId});">${bgv.goodsComments}</textarea>
                            <input type="hidden" name="goodsComments"
                                   value="${bgv.buyorderGoodsId}|${bgv.goodsComments}">
                        </td>
                        <td>
                            <textarea name="installationDispaly" placeholder="是否含安调" alt="${bgv.buyorderGoodsId}"
                                      class="wid7"
                                      onblur="changComments(this,${bgv.buyorderGoodsId});">${bgv.installation}</textarea>
                            <input type="hidden" name="installation" value="${bgv.buyorderGoodsId}|${bgv.installation}">
                        </td>

                        <td>
                            <textarea name="insideCommentsDispaly" alt="${bgv.buyorderGoodsId}" class="wid7"
                                      onblur="changComments(this,${bgv.buyorderGoodsId});">${bgv.insideComments}</textarea>
                            <input type="hidden" name="insideComments"
                                   value="${bgv.buyorderGoodsId}|${bgv.insideComments}">
                        </td>
                        <td>
                            <input id="actualPurchasePrice${bgv.sku}" type="text"
                                   class="wid7 warning-color1" name="xactualPurchasePrice" alt4="${bgv.goodsId}"
                                   value='<fmt:formatNumber type="number" value="${bgv.actualPurchasePrice}" pattern="0.00" maxFractionDigits="2" />'
                                   autocomplete="off"
                                   onblur="actualPurchasePrice(this,${bgv.buyorderGoodsId},${buyorderVo.traderId})">
                        </td>
                        <span style="display: none;" class="oneAllMoneyAct" altAct="${bgv.buyorderGoodsId}"
                              altGoodAct="${bgv.goodsId}"><fmt:formatNumber type="number"
                                                                            value="${bgv.oneBuyorderGoodsAmountAct}"
                                                                            pattern="0.00"
                                                                            maxFractionDigits="2"/></span>
                    </tr>
                    <c:if test="${bgv.geContractNo ne null and bgv.geContractNo ne '' }">
                        <tr>
                            <td colspan="16" class="table-container ">
                                <table class="table">
                                    <thead>
                                    <tr style="background-color: #F5F5F5">
                                        <th class="wid20">GE合同编号</th>
                                        <th class="wid20 ">GE销售订单编号</th>
                                        <th class="wid30 ">序列号</th>
                                        <th class="wid20 ">操作项</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td>${bgv.geContractNo}</td>
                                        <td>${bgv.geSaleContractNo}</td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${bgv.masterSlaveLists eq null}">
                                                    -
                                                </c:when>
                                                <c:otherwise>
                                                    <c:forEach var="mslm" items="${bgv.masterSlaveLists}">
                                                        主机：${mslm.vBuyorderSncodeMaster.masterSncode}
                                                        探头：
                                                        <c:choose>
                                                            <c:when test="${empty mslm.vBuyorderSncodeSlaves}">
                                                                -
                                                            </c:when>
                                                            <c:otherwise>
                                                                <c:forEach var="msls"
                                                                           items="${mslm.vBuyorderSncodeSlaves}"
                                                                           varStatus="stat">
                                                                    <c:if test="${msls.slaveSncode ne ''}">
                                                                        ${msls.slaveSncode}
                                                                        <c:if test="${!stat.last}">,</c:if>
                                                                    </c:if>
                                                                </c:forEach>
                                                            </c:otherwise>
                                                        </c:choose>
                                                        <br>
                                                    </c:forEach>
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                            <input type="button" onclick="delGeInfoCollection(${bgv.buyorderGoodsId})"
                                                   value="删除">
                                            <input type="button"
                                                   onclick="addSncode('${bgv.sku}',${bgv.goodsId},${bgv.buyorderGoodsId})"
                                                   value="添加序列号">
                                            <input type="button"
                                                   onclick="updateGeInfoCollection(${bgv.buyorderGoodsId},'${bgv.geContractNo}','${bgv.geSaleContractNo}','${bgv.sku}')"
                                                   value="编辑">
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </c:if>
                    <c:if test="${bgv.goodsId ne 127063}"></c:if>
                    <tr>
                        <td colspan="21" class="table-container ">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th class="wid10">关联单号</th>
                                    <th class="wid12">申请人</th>
                                    <th class="wid18">数量</th>
                                    <th class="wid12">销售价</th>
                                    <th class="wid12">销售货期</th>
                                    <th class="wid14">内部备注</th>
                                    <th class="wid14">产品备注</th>
                                    <th class="wid15">终端客户名称</th>
                                </tr>
                                </thead>
                                <tbody>
                                <c:forEach var="saleorderGoods" items="${bgv.saleorderGoodsVoList}">
                                    <tr>
                                        <td>${saleorderGoods.saleorderNo}</td>
                                        <td>${saleorderGoods.applicantName}</td>
                                        <td>
                                            <c:if test="${buyorderVo.deliveryDirect eq 1}">
                                                <input type="hidden" name="dbBuyNum"
                                                       value="${bgv.buyorderGoodsId}|${saleorderGoods.saleorderGoodsId}|${saleorderGoods.needBuyNum}">
                                                <input type="text" style="width: 60px;" class='mr4 warning-color1'
                                                       alt1="${saleorderGoods.goodsId}" name="saleorderGoodsNum"
                                                       value="${saleorderGoods.needBuyNum}" autocomplete="off"
                                                       onblur="addNum(this,${saleorderGoods.needBuyNum},${bgv.buyorderGoodsId},${saleorderGoods.saleorderGoodsId})"
                                                       >/
                                                ${saleorderGoods.needBuyNum eq null ? 0 : saleorderGoods.needBuyNum}
                                            </c:if>
                                            <c:if test="${buyorderVo.deliveryDirect eq 0}">
                                                <input type="hidden" name="dbBuyNum"
                                                       value="${bgv.buyorderGoodsId}|${saleorderGoods.saleorderGoodsId}|${saleorderGoods.needBuyNum}">
                                                <input type="text" style="width: 60px;" class='mr4 warning-color1'
                                                       alt1="${saleorderGoods.goodsId}" name="saleorderGoodsNum"
                                                       value="${saleorderGoods.needBuyNum}" autocomplete="off"
                                                       onblur="addNum(this,${saleorderGoods.needBuyNum},${bgv.buyorderGoodsId},${saleorderGoods.saleorderGoodsId})"
                                                       "
                                                >/
                                                ${saleorderGoods.needBuyNum eq null ? 0 : saleorderGoods.needBuyNum}
                                            </c:if>
                                        </td>
                                        <td><fmt:formatNumber type="number" value="${saleorderGoods.price}"
                                                              pattern="0.00" maxFractionDigits="2"/></td>
                                        <td>${saleorderGoods.deliveryCycle}</td>
                                        <td><span class="warning-color1">${saleorderGoods.insideComments}</span></td>
                                        <td>${saleorderGoods.goodsComments}</td>
                                        <td>${saleorderGoods.terminalTraderName}</td>
                                    </tr>
                                </c:forEach>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </c:forEach>
            <div class="tablelastline">
                总件数<span class="warning-color1" id="zSum">${buyorderVo.buyorderSum}</span>，
                总金额<span class="warning-color1" id="zMoney">${buyorderVo.buyorderAmount}</span>
                ，返利总额<span class="warning-color1" id="rebateTotal">${buyorderVo.usedTotalRebate}</span>
                ，应付金额<span class="warning-color1" id="rebateNeedPay">${buyorderVo.buyorderAmount - buyorderVo.usedTotalRebate}</span>
            </div>
            <span style="display: none;" class="warning-color1" id="zMoneyAct">${buyorderVo.buyorderAmountAct}</span>
        </div>


        <%-------------------------------------虚拟商品信息------------------------------------------------------%>
        <div class='parts table-style77'>
            <div class="title-container mb10" style="margin-bottom: 0px;">
                <div class="table-title nobor">虚拟商品信息</div>
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"800px","height":"800px","title":"添加虚拟商品","link":"/ezadmin/list/list-expenseSkuList"}'>
                    添加虚拟商品
                </div>
            </div>
            <input type="hidden" name="buyorderExpenseId" value="${buyOrderExpenseId}"/>
            <c:forEach var="item" items="${buyorderVo.buyorderExpenseItemDtos}" varStatus="status">
            <table class="table table-style7" id="virtualGoods">
                    <thead>
                        <tr>
                            <th width="5%">序号</th>
                            <th width="10%">订货号</th>
                            <th width="22%">产品名称</th>
                            <th width="10%">费用类别</th>
                            <th width="8%">是否可库存管理</th>
                            <th width="10%">采购数量</th>
                            <th width="10%">单价</th>
                            <th width="10%">总额</th>
                            <th width="15%">采购备注</th>
<%--                            <th width="5%">操作</th>--%>
                        </tr>
                    </thead>

                    <tbody>
                        <tr>
                            <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseItemId" value="${item.buyorderExpenseItemId}"/>
                            <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseId" value="${item.buyorderExpenseId}"/>
                            <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].isDelete" value="0"/>
                        <%----------------------js中会按照顺序取goodsId，因此如果要在这里添加或删除input 需要修改js---------------%>
                            <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseItemDetailDto.buyorderExpenseItemDetailId" value="${item.buyorderExpenseItemDetailDto.buyorderExpenseItemDetailId}"/>
                            <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseItemDetailDto.buyorderExpenseItemId" value="${item.buyorderExpenseItemId}"/>
                            <td>${status.count}</td>
                            <td>${item.buyorderExpenseItemDetailDto.sku}</td>
                            <td>${item.buyorderExpenseItemDetailDto.goodsName}</td>
                            <td>${item.buyorderExpenseItemDetailDto.expenseCategoryName}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${item.buyorderExpenseItemDetailDto.haveStockManage eq 0}">
                                        否
                                    </c:when>
                                    <c:when test="${item.buyorderExpenseItemDetailDto.haveStockManage eq 1}">
                                        是
                                    </c:when>
                                    <c:otherwise>-</c:otherwise>
                                </c:choose>
                            </td>
                            <td >
                                <span altTotal="${item.goodsId}" name="buyorderExpenseItemDtos[${status.index}].num" class="ebuySum">${item.num}</span>
                                <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].num" type="text" value="${item.num}"/>

                            </td>
                            <td>
                                <input name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseItemDetailDto.price" type="text" class="wid11 warning-color1"
                                       value='<fmt:formatNumber type="number" value="${item.buyorderExpenseItemDetailDto.price}" pattern="0.00" maxFractionDigits="2" />'
                                       alt2="${item.goodsId}" id="price${status.index}"
                                       vbuy = "buyprice"
                                       onkeydown="checkInputPrice('price${status.index}',10);" onkeyup="checkInputPrice('price${status.index}',10);"
                                       onblur="expensePrice(this)">
                            </td>
                            <td>
                                <span class="oneAllEMoney" altVirtual="${item.goodsId}">
                                    <fmt:formatNumber value="${item.buyorderExpenseItemDetailDto.price * item.num}" type="numer" pattern="0.00" maxFractionDigits="2"/>
                                </span>
                            </td>
                            <td>
                                <input name="buyorderExpenseItemDtos[${status.index}].buyorderExpenseItemDetailDto.insideComments" type="text" value="${item.buyorderExpenseItemDetailDto.insideComments}">
                            </td>
<%--                            <td>--%>
<%--                                <span class="edit-user forbid clcforbid"--%>
<%--                                      onclick="delExpenseGoods(${status.count});">删除</span>--%>
<%--                            </td>--%>
                        </tr>
<%--                        虚拟商品关联销售订单--%>
                        <tr>
                            <c:if test="${not empty item.buyOrderSaleOrderGoodsDetailDtos}">
                                <td colspan="20" class="table-container">
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th class="wid10">关联单号</th>
                                            <th class="wid12">申请人</th>
                                            <th class="wid18">数量</th>
                                            <th class="wid12">销售价</th>
                                            <th class="wid12">销售货期</th>
                                            <th class="wid14">内部备注</th>
                                            <th class="wid14">商品备注</th>
                                            <th class="wid15">终端客户名称</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        <c:forEach var="list" items="${item.buyOrderSaleOrderGoodsDetailDtos}" varStatus="sgStatus">
                                            <tr>
                                            <input type="hidden" name="buyorderExpenseItemDtos[${status.index}].buyOrderSaleOrderGoodsDetailDtos[${sgStatus.index}].buyorderExpenseJSaleorderId" value="${list.buyorderExpenseJSaleorderId}"/>
                                            <td>
                                                <c:if test="${list.orderType ne 2}">
                                                    <a class="addtitle" href="javascript:void(0);"
                                                       tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                            "link":"/orderstream/saleorder/detail.do?saleOrderId=${list.saleorderId}"}'>${list.saleorderNo}</a>
                                                </c:if>

                                                <c:if test="${list.orderType eq 2}">
                                                    <a class="addtitle" href="javascript:void(0);"
                                                       tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","title":"订单信息",
                                                        "link":"./order/saleorder/viewBhSaleorder.do?saleorderId=${list.saleorderId}"}'>${list.saleorderNo}</a>
                                                </c:if>
                                            </td>
                                            <td>${list.applicantName}</td>
                                            <td><input type="hidden"
                                                       value="${item.buyorderExpenseItemId}|${list.saleorderGoodsId}|${list.num}">
                                                <input type="text" style="width: 60px;" class='mr4 warning-color1'  vbuy = "buynum"
                                                       alt1="${list.goodsId}" name="buyorderExpenseItemDtos[${status.index}].buyOrderSaleOrderGoodsDetailDtos[${sgStatus.index}].buyNum"
                                                       value="${list.num}"
                                                       autocomplete="off"
                                                       id="eNum${status.index}"
                                                       onkeydown="checkInputENum('eNum${status.index}');" onkeyup="checkInputENum('eNum${status.index}',10);"
                                                       onblur="addENum(this,${list.num},${item.buyorderExpenseItemId},${list.saleorderGoodsId})" >/${list.num}</td>
                                            <td><fmt:formatNumber type="number" value="${list.price}"
                                                                  pattern="0.00" maxFractionDigits="2"/></td>
                                            <td>${list.deliveryCycle}</td>
                                            <td><span class="warning-color1">${list.insideComments}</span></td>
                                            <td>${list.goodsComments}</td>
                                            <td>${list.terminalTraderName}</td>
                                            </tr>
                                        </c:forEach>
                                        </tbody>
                                    </table>
                                </td>
                            </c:if>
                        </tr>

                    </tbody>
                </table>
            </c:forEach>
            <table class="table" id="virtualGoodsAdd">
                <tbody>
                </tbody>
            </table>
            <div class="tablelastline">
                总件数<span class="warning-color1" id="eSum">${buyorderVo.ebuyorderSum}</span>
                ，总金额<span class="warning-color1" id="eMoney">${buyorderVo.ebuyorderAmount}</span>
            </div>
        </div>


        <!-- ----------------------------------付款计划 ------------------------------------- -->
        <div class="parts content1">
            <div class="title-container" style="margin-bottom: 15px;">
                <div class="table-title nobor">
                    付款信息
                </div>
            </div>
            <ul class="payplan" id="payplan">
                <li>
                    <div class="infor_name infor_name72">
                        <span>*</span>
                        <label>付款方式</label>
                    </div>
                    <div class="f_left inputfloat">
                        <select id="paymentType" name="paymentType" class="input-middle" onchange="payment(this);">
                            <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                <option value="${list.sysOptionDefinitionId}"
                                        <c:if test="${list.sysOptionDefinitionId eq buyorderVo.paymentType }">selected="selected"</c:if>>${list.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                    <div class="f_left inputfloat">
                        <div id="displayBankAcceptance" <c:if test="${buyorderVo.traderId ne 613042 or buyorderVo.paymentType ne 419}">class="none"</c:if>>
                            <input type="checkbox" name="bankAcceptanceCheckbox" class="mt5" onclick="bankAcceptanceChecked();" <c:if test="${buyorderVo.bankAcceptance == 1}">checked="checked"</c:if>>
                            <input type="hidden" name="bankAcceptance" value="${buyorderVo.bankAcceptance}">
                            <label class="infor_name infor_name72">银行承兑汇票</label>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72 ">
                        <span>*</span>
                        <label>预付金额</label>
                    </div>

                    <div class="f_left">
                        <div class='inputfloat'>
                            <input type="text" class="input-middle" id="prepaidAmount" name="prepaidAmount"
                                   value="${buyorderVo.buyorderAmount}" readonly="readonly"/>
                        </div>
                        <div id="prepaidAmountError"></div>
                    </div>

                </li>
                <li id="accountPeriodLi"
                    <c:if test="${(buyorderVo.paymentType eq 419) or (buyorderVo.paymentType eq 0)}">style="display:none"</c:if>>
                    <!-- 419先款后货100%预付:0默认不显示 -->
                    <div class="infor_name infor_name72 ">
                        <label>账期支付</label>
                    </div>
                    <div class="f_left inputfloat">
                        <div class='inputfloat'>
                            <input type="text" class="input-middle" name="accountPeriodAmount" id="accountPeriodAmount"
                                   value="${buyorderVo.accountPeriodAmount=='0.00'?'':buyorderVo.accountPeriodAmount}">
                            <input type="hidden" name="haveAccountPeriod" id="haveAccountPeriod" value="0">
                        </div>
                        <div id="accountPeriodAmountError"></div>
                    </div>
                    <div id="accountPeriodAmountError"></div>
                </li>
                <li id="retainageLi" <c:if test="${buyorderVo.paymentType ne 424}">style="display:none"</c:if>>
                    <!-- 424先货后款自定义 -->
                    <div class="infor_name infor_name72">
                        <span>*</span>
                        <label>尾款</label>
                    </div>
                    <div class="f_left ">
                        <div class='inputfloat'>
                            <input type="text" class="input-middle" name="retainageAmount" id="retainageAmount"
                                   value="${buyorderVo.retainageAmount=='0.00'?'':buyorderVo.retainageAmount}">
                            <label class="ml10 mr10 mt4">尾款期限</label>
                            <input type="text" class="input-smaller" name="retainageAmountMonth"
                                   id="retainageAmountMonth"
                                   value="${buyorderVo.retainageAmountMonth==0?'':buyorderVo.retainageAmountMonth}">
                            <label class='mt4'>个月</label>
                        </div>
                        <div id="retainageAmountError"></div>
                    </div>

                </li>
                <li>
                    <div class="infor_name infor_name72">
                        <label>付款备注</label>
                    </div>
                    <div class="f_left">
                        <div class='inputfloat'>
                            <input type="text" placeholder="对内使用，适用于向财务部同事告知付款要求"
                                   class="input-xx" name="paymentComments" id="paymentComments"
                                   value="${buyorderVo.paymentComments}"/>
                        </div>
                        <div id="paymentCommentsError"></div>
                    </div>
                </li>
                <div class="font-grey9 ml90 ">
                    供应商当前帐期剩余额度<span id="periodAmount">0</span>元，帐期天数<span id="periodDay">0</span>天；如需更改帐期，您需要在供应商详情财务信息中申请帐期；
                </div>
                <div id="pay"></div>
            </ul>
        </div>
        <div class="parts content1">
            <div class="title-container" style="margin-bottom: 15px;">
                <div class="table-title nobor">
                    收票信息
                </div>
            </div>
            <ul class="payplan" id="payplan">
                <li>
                    <div class="infor_name infor_name72">
                        <label>收票种类</label>
                    </div>
                    <!-- 获取当前日期 -->
                    <jsp:useBean id="now" class="java.util.Date"/>
                    <fmt:formatDate value="${now}" type="both" dateStyle="long" var="today" pattern="yyyy-MM-dd"/>
                    <div class="f_left inputfloat">
                        <select id="invoiceType" name="invoiceType" onchange="" class="input-middle">
                            <!-- 4月1号后税率只有13% -->
                            <c:choose>
                                <c:when test="${today >= '2019-04-01'}">
                                    <c:forEach var="list" items="${receiptTypes}" varStatus="status">
                                        <c:if test="${list.sysOptionDefinitionId ne 429 and list.sysOptionDefinitionId ne 430}"><!-- 屏蔽17%税率 -->
                                            <option value="${list.sysOptionDefinitionId}" id="${list.comments}"
                                                    <c:if test="${list.sysOptionDefinitionId eq 972}">selected</c:if>>${list.title}
                                            </option>
                                        </c:if>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    <c:forEach var="list" items="${receiptTypes}" varStatus="status">
                                        <option value="${list.sysOptionDefinitionId}"
                                                <c:if test="${list.sysOptionDefinitionId eq 682}">selected="selected"</c:if>>${list.title}</option>
                                    </c:forEach>
                                </c:otherwise>
                            </c:choose>
                        </select>
                    </div>
                </li>

                <li>
                    <div class="infor_name infor_name72">
                        <label>收票备注</label>
                    </div>
                    <div class="f_left">
                        <div class='inputfloat'>
                            <input type="text" placeholder="对内使用，适用于向财务部同事告知收票要求" id="invoiceComments"
                                   class="input-xx" name="invoiceComments" value="${buyorderVo.invoiceComments }"/>
                        </div>
                        <div id="invoiceCommentsError"></div>
                    </div>
                </li>
            </ul>
        </div>
        <div class="parts content1 ">
            <div class="title-container" style="margin-bottom: 15px;">
                <div class="table-title nobor">
                    收货信息
                </div>
            </div>
            <ul class="payplan">
                <c:if test="${buyorderVo.deliveryDirect eq 1}">
                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>收货客户</label>
                        </div>
                        <div class="f_left inputfloat">${buyorderVo.takeTraderName}</div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>收货联系人</label>
                        </div>
                        <div class="f_left inputfloat">
                                ${buyorderVo.takeTraderContactName}/${buyorderVo.takeTraderContactTelephone}/${buyorderVo.takeTraderContactMobile}
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72">
                            <span>*</span>
                            <label>收货地区</label>
                        </div>
                        <div class="f_left inputfloat">
                                ${buyorderVo.takeTraderArea}
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name72">
                            <label>收货地址</label>
                        </div>
                        <div class="f_left inputfloat">
                                ${buyorderVo.takeTraderAddress}
                        </div>
                    </li>
                </c:if>
                <li>
                    <div class="infor_name infor_name72">
                        <label>指定物流公司</label>
                    </div>
                    <div class="f_left inputfloat">
                        <select class="input-middle" name="logisticsId">
                            <option value="0">请选择</option>
                            <c:forEach var="list" items="${logisticsList}">
                                <c:if test="${list.isEnable eq 1}">
                                    <option value="${list.logisticsId}"
                                            <c:if test="${list.logisticsId eq buyorderVo.logisticsId }">selected="selected"</c:if>>${list.name}</option>
                                </c:if>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <c:if test="${buyorderVo.deliveryDirect eq 0}">
                    <li>
                        <div class="infor_name infor_name72">
                            <label>收货地址</label>
                        </div>
                        <div class="f_left inputfloat">
                            <select class="input-middle" name="takeAddressId">
                                <option value="0">请选择</option>
                                <c:forEach var="list" items="${addressList}">
                                    <c:if test="${(buyorderVo.takeTraderAddressId != null && buyorderVo.takeTraderAddressId != 0) && list.addressId eq buyorderVo.takeTraderAddressId }">
                                        <option value="${list.addressId}|${list.companyName}|${list.areas}"
                                                selected="selected">
                                                ${list.companyName}/${list.areas}/${list.address}/${list.contactName}/${list.mobile}</option>
                                    </c:if>
                                    <c:if test="${ buyorderVo.takeTraderAddressId ==null || buyorderVo.takeTraderAddressId ==0}">
                                        <option value="${list.addressId}|${list.companyName}|${list.areas}"
                                                <c:if test="${list.isDefault eq 1 }">selected="selected"</c:if> >
                                                ${list.companyName}/${list.areas}/${list.address}/${list.contactName}/${list.mobile}</option>
                                    </c:if>
                                </c:forEach>
                            </select>
                        </div>
                    </li>
                </c:if>
                <li>
                    <div class="infor_name infor_name72">
                        <label>运费说明</label>
                    </div>
                    <div class="f_left inputfloat">
                        <select class="input-middle" name="freightDescription">
                            <c:forEach var="list" items="${freightDescriptions}">
                                <option value="${list.sysOptionDefinitionId}">${list.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72">
                        <label>物流备注</label>
                    </div>
                    <div class="f_left ">
                        <div class="inputfloat">
                            <input type="text" name="logisticsComments" placeholder="对内使用，适用于向物流部同事告知发货要求，默认同步客户信息中物流备注"
                                   id="logisticsComments" class="input-xx" value="${buyorderVo.logisticsComments }"/>
                        </div>
                        <div id="logisticsCommentsError"></div>
                    </div>
                </li>
            </ul>
        </div>

        <!-- ----------------------------------其他信息 ------------------------------------- -->
        <div class="parts content1 ">
            <div class="title-container" style="margin-bottom: 15px;">
                <div class="table-title nobor">
                    其他信息
                </div>
            </div>
            <ul class="payplan">
                <li>
                    <div class="infor_name infor_name72">
                        <label>补充条款</label>
                    </div>
                    <div class="f_left">
                        <div class="inputfloat">
                            <input type="text" name="additionalClause" id="additionalClause"
                                   placeholder="对外使用，适用于向供应商填写补充条款" class="input-xx"
                                   value="${buyorderVo.additionalClause }"/>
                        </div>
                        <div id="additionalClauseError"></div>
                    </div>
                </li>
                <li>
                    <div class="infor_name infor_name72">
                        <label>内部备注</label>
                    </div>
                    <div class="f_left ">
                        <div class="inputfloat">
                            <input type="text" name="comments" id="comments"
                                   placeholder="对内使用，客户不可见,可用作自己的备注" class="input-xx" value="${buyorderVo.comments }"/>
                        </div>
                        <div id="commentsError"></div>
                    </div>
                </li>
            </ul>
        </div>

        <%--实际供应商信息--%>
        <div class="parts content1">

            <div id="actualSupplierInfo" <c:if test="${buyorderVo.traderId ne 613042}">class="none"</c:if>>

                <div class="title-container">
                    <div class="table-title nobor">
                        实际供应商信息
                    </div>
                </div>
                <div style="margin-top: 15px">
                    <ul class="payplan" style="margin-bottom:0;">
                        <li>
                            <div class="infor_name mt0">
                                <span>*</span>
                                <label>实际供应商：</label>
                            </div>
                            <div class="f_left">
                                <div class='inputfloat'>
                                    <c:if test="${buyorderVo.traderIdAct and buyorderVo.traderIdAct ne 0}">
                                        <span id="nameAct">${buyorderVo.traderNameAct}</span>
                                        <c:if test="${buyorderVo.certificateOverdueAct}">
                                            <span id="showOverdueAct" style="color: red">(已过期)</span>
                                        </c:if>
                                        <span id="certificateOverdueAct" style="color: red;display: none">(已过期)</span>
                                        <label class="bt-bg-style bg-light-blue bt-small" onclick="researchAct();" id="researchAct">重新搜索</label>
                                        <input type="text" placeholder="请输入供应商名称" class="input-largest none"
                                               name="searchTraderNameAct" id="searchTraderNameAct" value="${buyorderVo.traderNameAct}">
                                        <label class="bt-bg-style bg-light-blue bt-small none" onclick="searchSupplierAct();"
                                               id="errorMesAct">搜索</label>
                                    </c:if>
                                    <c:if test="${!buyorderVo.traderIdAct or buyorderVo.traderIdAct eq 0}">
                                        <span class="none" id="nameAct">${buyorderVo.traderNameAct}</span>
                                        <span id="certificateOverdueAct" style="color: red;display: none">(已过期)</span>
                                        <label class="bt-bg-style bg-light-blue bt-small none" onclick="researchAct();"
                                               id="researchAct">重新搜索</label>
                                        <input type="text" placeholder="请输入供应商名称" class="input-largest" name="searchTraderNameAct"
                                               id="searchTraderNameAct" value="${buyorderVo.traderNameAct}">
                                        <label class="bt-bg-style bg-light-blue bt-small" onclick="searchSupplierAct();"
                                               id="errorMesAct">搜索</label>
                                    </c:if>
                                    <span style="display:none;">
									<!-- 弹框 -->
									<div class="title-click nobor  pop-new-data" id="popSupplierAct"></div>
								</span>
                                    <input type="hidden" id="traderSupplierIdAct" name="traderSupplierIdAct"/>
                                    <input type="hidden" id="traderIdAct" name="traderIdAct" value="${buyorderVo.traderIdAct}"/>
                                    <input type="hidden" id="traderNameAct" name="traderNameAct" value="${buyorderVo.traderNameAct}"/>
                                </div>
                                <div class="font-red none"></div>
                            </div>

                            <!-- 添加复选框 -->
                            <div class="f_left">
                                <div class="inputfloat">
                                    <input type="checkbox" id="needInvoiceCheck" name="needInvoiceCheck" value="1" checked>
                                    <label class="ml5">开票</label>
                                    <input type="hidden" id="needInvoice" name="needInvoice" value="1">
                                </div>
                            </div>

                        </li>

                        <li
                                <c:if test="${!buyorderVo.traderContactIdAct or buyorderVo.traderContactIdAct eq 0}">class="none "</c:if> id="contactAct">
                            <div class="infor_name">
                                <span>*</span>
                                <label>联系人：</label>
                            </div>
                            <div class="f_left">
                                <div class='inputfloat'>
                                    <select id="traderContactIdAct" name="traderContactStrAct" class="input-largest">
                                        <option value="">请选择</option>
                                        <c:forEach var="contact" items="${contactListAct}" varStatus="status">
                                            <option value="${contact.traderContactId}|${contact.name}|${contact.mobile}|${contact.telephone}"
                                                    <c:if test="${contact.traderContactId eq buyorderVo.traderContactIdAct }">selected="selected"</c:if>>${contact.name}/${contact.mobile}/${contact.telephone}</option>
                                        </c:forEach>
                                    </select>
                                </div>
                                <div class="font-red none">联系人不允许为空</div>
                            </div>
                        </li>

                        <li>
                            <div class="infor_name infor_name72">
                                <label>付款方式：</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select id="paymentTypeAct" name="paymentTypeAct" class="input-middle" onchange="paymentAct(this);">
                                    <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                        <option value="${list.sysOptionDefinitionId}"
                                                <c:if test="${list.sysOptionDefinitionId eq buyorderVo.paymentTypeAct }">selected="selected"</c:if>>${list.title}</option>
                                    </c:forEach>
                                </select>
                            </div>
                        </li>
                        <li>
                            <div class="infor_name infor_name72 ">
                                <label>付款金额：</label>
                            </div>

                            <div class="f_left">
                                <div class='inputfloat'>
                                    <input type="text" class="input-middle" id="prepaidAmountAct" name="prepaidAmountAct"
                                           value="${buyorderVo.prepaidAmountAct}" readonly="readonly"/>
                                </div>
                                <div id="prepaidAmountErrorAct"></div>
                            </div>

                        </li>
                        <li id="accountPeriodLiAct"
                            <c:if test="${(buyorderVo.paymentTypeAct eq 419) or (buyorderVo.paymentTypeAct eq 0) or buyorderVo.paymentTypeAct == null}">style="display:none"</c:if>>
                            <!-- 419先款后货100%预付:0默认不显示 -->
                            <div class="infor_name infor_name72 ">
                                <label>账期支付：</label>
                            </div>
                            <div class="f_left inputfloat">
                                <div class='inputfloat'>
                                    <input type="text" class="input-middle" name="accountPeriodAmountAct" id="accountPeriodAmountAct"
                                           value="${buyorderVo.accountPeriodAmountAct=='0.00'?'':buyorderVo.accountPeriodAmountAct}">
                                </div>
                                <div id="accountPeriodAmountErrorAct"></div>
                            </div>
                        </li>
                        <li id="retainageLiAct" <c:if test="${buyorderVo.paymentTypeAct ne 424}">style="display:none"</c:if>>
                            <!-- 424先货后款自定义 -->
                            <div class="infor_name infor_name72">
                                <label>尾款：</label>
                            </div>
                            <div class="f_left ">
                                <div class='inputfloat'>
                                    <input type="text" class="input-middle" name="retainageAmountAct" id="retainageAmountAct"
                                           value="${buyorderVo.retainageAmountAct=='0.00'?'':buyorderVo.retainageAmountAct}">
                                    <label class="ml10 mr10 mt4">尾款期限</label>
                                    <input type="text" class="input-smaller" name="retainageAmountMonthAct"
                                           id="retainageAmountMonthAct"
                                           value="${buyorderVo.retainageAmountMonthAct==0?'':buyorderVo.retainageAmountMonthAct}">
                                    <label class='mt4'>个月</label>
                                </div>
                                <div id="retainageAmountErrorAct"></div>
                            </div>

                        </li>

                        <li>
                            <div class="infor_name infor_name72">
                                <label>收票种类：</label>
                            </div>
                            <div class="f_left inputfloat">
                                <select id="invoiceTypeAct" name="invoiceTypeAct" onchange="" class="input-middle">
                                    <c:forEach var="list" items="${receiptTypes}" varStatus="status">
                                        <c:if test="${list.sysOptionDefinitionId ne 429 and list.sysOptionDefinitionId ne 430}"><!-- 屏蔽17%税率 -->
                                            <option value="${list.sysOptionDefinitionId}" id="${list.comments}"
                                                <c:if test="${list.sysOptionDefinitionId eq 972}">selected="selected"</c:if>>${list.title}</option>
                                            </option>
                                        </c:if>
                                    </c:forEach>
                                </select>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

        </div>

        <div class="tcenter mb10">
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10" id="sub">确定</button>
        </div>
    </div>
</form>
</body>

</html>