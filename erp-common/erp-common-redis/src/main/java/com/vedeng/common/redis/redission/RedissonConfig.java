package com.vedeng.common.redis.redission;

import cn.hutool.core.util.StrUtil;
import io.netty.channel.nio.NioEventLoopGroup;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.redisson.config.SubscriptionMode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class RedissonConfig {

    private static final Logger logger = LoggerFactory.getLogger(RedissonConfig.class);

    public static final String REDIS_PREFIX = "redis://";

    @Value("${redis.sentinel1}")
    private String sentinel1;

    @Value("${redis.sentinel2}")
    private String sentinel2;

    @Value("${redis.sentinel3}")
    private String sentinel3;

    @Value("${redis.sentinelPassword}")
    private String password;

    @Value("${masterName}")
    private String masterName;

    private int connectionMinimumIdleSize = 10;
    private int idleConnectionTimeout = 10000;
    private int connectTimeout = 10000;
    private int timeout = 3000;
    private int retryAttempts = 3;
    private int retryInterval = 1500;
    private int failedSlaveReconnectionInterval = 3000;
    private int failedSlaveCheckInterval = 60000;

    private int subscriptionsPerConnection = 5;
    private String clientName = null;
    private int subscriptionConnectionMinimumIdleSize = 1;
    private int subscriptionConnectionPoolSize = 50;
    private int database = 0;
    private int dnsMonitoringInterval = 5000;

    private int slaveConnectionMinimumIdleSize = 24;
    private int slaveConnectionPoolSize = 64;
    private int masterConnectionMinimumIdleSize = 24;
    private int masterConnectionPoolSize = 64;
    private ReadMode readMode = ReadMode.SLAVE;
    private SubscriptionMode subscriptionMode = SubscriptionMode.SLAVE;

    private int thread = 8; //当前处理核数量 * 2

    @Bean(destroyMethod = "shutdown")
    RedissonClient client() {

        Config config = new Config();

        logger.info("redis哨兵地址 ：{} {} {}",sentinel1,sentinel2,sentinel3);

        config.useSentinelServers()
                .addSentinelAddress(REDIS_PREFIX + sentinel1)
                .addSentinelAddress(REDIS_PREFIX + sentinel2)
                .addSentinelAddress(REDIS_PREFIX + sentinel3)
                .setIdleConnectionTimeout(connectionMinimumIdleSize)
                .setIdleConnectionTimeout(idleConnectionTimeout)
                .setConnectTimeout(connectTimeout)
                .setTimeout(timeout)
                .setRetryAttempts(retryAttempts)
                .setRetryInterval(retryInterval)
                .setFailedSlaveCheckInterval(failedSlaveCheckInterval)
                .setFailedSlaveReconnectionInterval(failedSlaveReconnectionInterval)
                .setSubscriptionsPerConnection(subscriptionsPerConnection)
                .setSubscriptionConnectionMinimumIdleSize(subscriptionConnectionMinimumIdleSize)
                .setSubscriptionConnectionPoolSize(subscriptionConnectionPoolSize)
                .setDnsMonitoringInterval(dnsMonitoringInterval)
                .setSlaveConnectionMinimumIdleSize(slaveConnectionMinimumIdleSize)
                .setSlaveConnectionPoolSize(slaveConnectionPoolSize)
                .setMasterConnectionPoolSize(masterConnectionPoolSize)
                .setReadMode(readMode)
                .setSubscriptionMode(subscriptionMode)
                .setMasterName(masterName)
                .setDatabase(database)
                .setMasterConnectionMinimumIdleSize(masterConnectionMinimumIdleSize)
                .setClientName(clientName);

        if (StrUtil.isNotBlank(password)) {
            config.useSentinelServers().setPassword(password);
        }
        config.setCodec(new JsonJacksonCodec());
        config.setThreads(thread);
        config.setEventLoopGroup(new NioEventLoopGroup());
        return Redisson.create(config);
    }
}
