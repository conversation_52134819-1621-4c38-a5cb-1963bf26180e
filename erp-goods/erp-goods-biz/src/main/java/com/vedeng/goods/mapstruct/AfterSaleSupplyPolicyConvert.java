package com.vedeng.goods.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.goods.domain.entity.AfterSaleSupplyPolicy;
import com.vedeng.goods.dto.AfterSaleSupplyPolicyDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AfterSaleSupplyPolicyConvert extends BaseMapStruct<AfterSaleSupplyPolicy, AfterSaleSupplyPolicyDto> {
}
