package com.vedeng.logistics.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutVo;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: 盘亏出库
 * @author: Zeno.zuo
 * @email: <EMAIL>
 * @date: 2022/11/16 15:36
 **/
@Service
@Slf4j
public class InventoryLossWarehouseGoodsOutDetailServiceImpl extends BaseWarehouseGoodsOutDetailService{

    /**
     * wms出库单类型-盘亏出库
     */
    private final Integer WMS_OUTPUT_ORDER_TYPE_INVENTORY_LOSS = 4;

    /**
     * 盘亏出库详情页
     */
    private final String DETAIL_URL_INVENTORY_LOSS_OUT = "/wms/inventoryOut/details.do?inventoryOutOrderId=";


    @Override
    public WarehouseGoodsOutVo detailWarehouseGoodsOut(String outInNo) {
        WarehouseGoodsOutVo warehouseGoodsOutVo = commonDetailWarehouseGoodsOut(outInNo, WarehouseGoodsOutEnum.INVENTORY_LOSS_WAREHOUSE_OUT.getErpCode());
        if(warehouseGoodsOutVo != null){
            String wmsInventoryLossNo = warehouseGoodsOutVo.getRelateNo();
            if (StringUtils.isNotBlank(wmsInventoryLossNo)){
                WmsOutputOrder wmsInventoryOutOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(wmsInventoryLossNo,WMS_OUTPUT_ORDER_TYPE_INVENTORY_LOSS);
                if(Objects.nonNull(wmsInventoryOutOrder)){
                    warehouseGoodsOutVo.setBelongUserName(wmsInventoryOutOrder.getCreator());
                    if(StringUtils.isNotBlank(wmsInventoryOutOrder.getCreator())){
                        User inventoryCreator = userMapper.getUserByUserName(wmsInventoryOutOrder.getCreator());
                        if(inventoryCreator != null && StringUtils.isNotBlank(inventoryCreator.getOrgName())){
                            warehouseGoodsOutVo.setBelongUserOrgName(inventoryCreator.getOrgName());
                        }
                    }
                }

            }
        }
        return warehouseGoodsOutVo;
    }

    @Override
    public List<WarehouseGoodsOutLogVo> relatedNoWarehouseGoodsOutLogList(String relatedNo) {
        List<WarehouseGoodsOutLogVo> warehouseGoodsOutLogVoList = warehouseGoodsOutInMapper.selectWarehouseGoodsOutLogListByRelatedNo(WarehouseGoodsOutEnum.INVENTORY_LOSS_WAREHOUSE_OUT.getErpCode(), relatedNo);
        return CollectionUtils.isNotEmpty(warehouseGoodsOutLogVoList) ? warehouseGoodsOutLogVoList : new ArrayList<>();
    }

    @Override
    public String relatedNoDetailRedirect(String relatedNo) {
        WmsOutputOrder wmsOutputOrder = wmsOutputOrderMapper.getWmsOutputOrderByOrderNoAndType(relatedNo,WMS_OUTPUT_ORDER_TYPE_INVENTORY_LOSS);
        String url = "";
        if (wmsOutputOrder != null){
            url = REDIRECT_URL_PREFIX + DETAIL_URL_INVENTORY_LOSS_OUT + wmsOutputOrder.getId();
        }
        return url;
    }
}
