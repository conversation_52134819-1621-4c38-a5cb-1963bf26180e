package com.vedeng.order.chain;

/**
 * <AUTHOR>
 * @ClassName RiskEnum.java
 * @Description TODO 风控枚举
 * @createTime 2020年12月08日 10:19:00
 */

public enum RiskEnum {
    FIRST_RISK(1,"首营商品质量风控信息不完整",""),

    SKU_RISK(2,"SKU商品质量风控信息不完整",""),

    SPU_RISK(3,"SPU商品质量风控信息不完整",""),

    TRADER_RISK(4,"客户质量风控信息（仓库地址）不完整",""),

    SUPPLIER_RISK(5,"供应商质量风控信息不完整",""),

    TRADER_QA_RISK(6,"客户资质审核未通过",""),

    SKU_SPU_FIRST_RISK(7,"商品质量风控不完整",""),

    ;

    private Integer model;

    private String riskMessage;

    private String param;

    RiskEnum(Integer model, String riskMessage, String param) {
        this.model = model;
        this.riskMessage = riskMessage;
        this.param = param;
    }

    public Integer getModel() {
        return model;
    }

    public void setModel(Integer model) {
        this.model = model;
    }

    public String getRiskMessage() {
        return riskMessage;
    }

    public void setRiskMessage(String riskMessage) {
        this.riskMessage = riskMessage;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }
}
