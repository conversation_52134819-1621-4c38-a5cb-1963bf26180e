function repurchase(){

    let skuList = []
    var saleOrderIds = $("#saleOrderIds").val();
    var traderId = $("#traderId").val();
    if(saleOrderIds === ""){
        layer.alert("请选择交易记录！");
        return false;
    }
    saleOrderIds  = Array.from(new Set(saleOrderIds.split(",")));
    var id = (new Date()).valueOf();
    var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
    var url= page_url + "/orderstream/saleorder/productConfirmPage.do?saleOrderIds="+saleOrderIds + "&traderId=" + traderId;
    var item = {'id':id, 'name':"商品确认", 'url': url, 'closable': true };
    // self.parent.parent.closableTab.addTab(item);
    // self.parent.parent.closableTab.resizeMove();
    // $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    if (typeof(self.parent.parent.closableTab) != 'undefined') {
        self.parent.parent.closableTab.addTab(item);
        self.parent.parent.closableTab.resizeMove();
        $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }else{
        try{
            var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
            window.parent.parent.postMessage({
                from:'ez',
                name: title,
                url:url,
                id:"tab-"+uniqueName
            }, '*');
        }catch (e){}
    }
}

function uploadJdOrder(){
    var id = (new Date()).valueOf();
    var frontPageId = $(window.parent.document).find('.active').eq(1).attr('id');
    var url= page_url + "/orderstream/jdsaleorder/upLoadJdFile.do";
    var item = {'id':id, 'name':"批量制单", 'url': url, 'closable': true };
    // self.parent.parent.closableTab.addTab(item);
    // self.parent.parent.closableTab.resizeMove();
    // $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    if (typeof(self.parent.parent.closableTab) != 'undefined') {
        self.parent.parent.closableTab.addTab(item);
        self.parent.parent.closableTab.resizeMove();
        $(window.parent.parent.document).find('.active').eq(1).children('iframe').attr('data-frontpageid', frontPageId);
    }else{
        try{
            var uniqueName = url.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
            window.parent.parent.postMessage({
                from:'ez',
                name: title,
                url:url,
                id:"tab-"+uniqueName
            }, '*');
        }catch (e){}
    }
}

function goodsCheckClick(obj){
    var ids = $("#saleOrderGoodsIds",window.parent.document).val();
    var orderIds = $("#saleOrderIds",window.parent.document).val();
    var saleOrderGoodsIds = ids === "" ? [] : ids;
    var saleOrderIds = orderIds === "" ? [] : orderIds;
    if (ids !== ""){
        saleOrderGoodsIds =ids.split(",");
    }
    if (orderIds !== ""){
        saleOrderIds =orderIds.split(",");
    }
    var saleOrderGoodsId =  $(obj).attr("saleOrderGoodsId")
    var saleOrderId =  $(obj).attr("saleOrderId")
    if($(obj).is(":checked")){
        saleOrderGoodsIds.push(saleOrderGoodsId);
        saleOrderIds.push(saleOrderId);
    }else {
        arrayRemoveItem(saleOrderGoodsIds,saleOrderGoodsId)
        arrayRemoveItem(saleOrderIds,saleOrderId)
    }
    $("#saleOrderGoodsIds",window.parent.document).attr("value",saleOrderGoodsIds);
    $("#saleOrderIds",window.parent.document).attr("value",saleOrderIds);

}

function arrayRemoveItem(arr, delVal) {
    if (arr instanceof Array) {
        var index = arr.indexOf(delVal);
        if (index > -1) {
            arr.splice(index, 1);
        }
    }
}

function searchBussiness(){

    checkLogin();
    delWarnTips("errorTxtMsg");
    var traderId = $("#traderId").val();
    var traderCustomerId = $("#traderCustomerId").val();
    var customerNature = $("#customerNature").val();
    $("#bussiness-info-div").attr("src",'./searchBusinessList.do?traderId='+traderId + "&traderCustomerId=" + traderCustomerId+"&customerNature=" + customerNature + "&" + $("#search").serialize());

}

