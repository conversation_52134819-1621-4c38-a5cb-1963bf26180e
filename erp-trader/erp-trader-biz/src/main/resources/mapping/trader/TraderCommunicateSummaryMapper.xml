<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCommunicateSummaryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity">
    <id column="COMMUNICATE_SUMMARY_ID" jdbcType="BIGINT" property="communicateSummaryId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CUSTOMER_INTENTIONS" jdbcType="VARCHAR" property="customerIntentions" />
    <result column="INTENTION_GOODS" jdbcType="VARCHAR" property="intentionGoods" />
    <result column="BRANDS" jdbcType="VARCHAR" property="brands" />
    <result column="MODELS" jdbcType="VARCHAR" property="models" />
    <result column="CUSTOMER_TYPES" jdbcType="VARCHAR" property="customerTypes" />
    <result column="IS_INTENTION" jdbcType="VARCHAR" property="isIntention" />
    <result column="IS_ADD_WECHAT" jdbcType="VARCHAR" property="isAddWechat" />
    <result column="IS_EFFECTIVE_COMMUNICATION" jdbcType="VARCHAR" property="isEffectiveCommunication" />
    <result column="COMMUNICATE_RECORD_ID" jdbcType="INTEGER" property="communicateRecordId" />
    <result column="UPDATE_LOG" property="updateLog"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    COMMUNICATE_SUMMARY_ID, ADD_TIME, MOD_TIME, CUSTOMER_INTENTIONS, INTENTION_GOODS, 
    BRANDS, MODELS, CUSTOMER_TYPES, IS_INTENTION, IS_ADD_WECHAT, IS_EFFECTIVE_COMMUNICATION, 
    COMMUNICATE_RECORD_ID, UPDATE_LOG
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_COMMUNICATE_SUMMARY
    where COMMUNICATE_SUMMARY_ID = #{communicateSummaryId,jdbcType=BIGINT}
  </select>
    <select id="getLatestSummaryByTraderId" resultMap="BaseResultMap">
      SELECT
        tcs.*
      FROM
        T_COMMUNICATE_SUMMARY tcs
          LEFT JOIN T_COMMUNICATE_RECORD tcr ON
          tcs.COMMUNICATE_RECORD_ID = tcr.COMMUNICATE_RECORD_ID
      WHERE
        tcr.TRADER_ID = #{traderId,jdbcType=INTEGER}
      ORDER BY
        tcs.ADD_TIME DESC
      limit 1
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_COMMUNICATE_SUMMARY
    where COMMUNICATE_SUMMARY_ID = #{communicateSummaryId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity">
    insert into T_COMMUNICATE_SUMMARY (COMMUNICATE_SUMMARY_ID, ADD_TIME, MOD_TIME, 
      CUSTOMER_INTENTIONS, INTENTION_GOODS, BRANDS, 
      MODELS, CUSTOMER_TYPES, IS_INTENTION, 
      IS_ADD_WECHAT, IS_EFFECTIVE_COMMUNICATION, 
      COMMUNICATE_RECORD_ID, UPDATE_LOG)
    values (#{communicateSummaryId,jdbcType=BIGINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{customerIntentions,jdbcType=VARCHAR}, #{intentionGoods,jdbcType=VARCHAR}, #{brands,jdbcType=VARCHAR}, 
      #{models,jdbcType=VARCHAR}, #{customerTypes,jdbcType=VARCHAR}, #{isIntention,jdbcType=VARCHAR}, 
      #{isAddWechat,jdbcType=VARCHAR}, #{isEffectiveCommunication,jdbcType=VARCHAR}, 
      #{communicateRecordId,jdbcType=INTEGER}, #{updateLog,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity">
    insert into T_COMMUNICATE_SUMMARY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="communicateSummaryId != null">
        COMMUNICATE_SUMMARY_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="customerIntentions != null">
        CUSTOMER_INTENTIONS,
      </if>
      <if test="intentionGoods != null">
        INTENTION_GOODS,
      </if>
      <if test="brands != null">
        BRANDS,
      </if>
      <if test="models != null">
        MODELS,
      </if>
      <if test="customerTypes != null">
        CUSTOMER_TYPES,
      </if>
      <if test="isIntention != null">
        IS_INTENTION,
      </if>
      <if test="isAddWechat != null">
        IS_ADD_WECHAT,
      </if>
      <if test="isEffectiveCommunication != null">
        IS_EFFECTIVE_COMMUNICATION,
      </if>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID,
      </if>
      <if test="updateLog != null">
        UPDATE_LOG,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="communicateSummaryId != null">
        #{communicateSummaryId,jdbcType=BIGINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerIntentions != null">
        #{customerIntentions,jdbcType=VARCHAR},
      </if>
      <if test="intentionGoods != null">
        #{intentionGoods,jdbcType=VARCHAR},
      </if>
      <if test="brands != null">
        #{brands,jdbcType=VARCHAR},
      </if>
      <if test="models != null">
        #{models,jdbcType=VARCHAR},
      </if>
      <if test="customerTypes != null">
        #{customerTypes,jdbcType=VARCHAR},
      </if>
      <if test="isIntention != null">
        #{isIntention,jdbcType=VARCHAR},
      </if>
      <if test="isAddWechat != null">
        #{isAddWechat,jdbcType=VARCHAR},
      </if>
      <if test="isEffectiveCommunication != null">
        #{isEffectiveCommunication,jdbcType=VARCHAR},
      </if>
      <if test="communicateRecordId != null">
        #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="updateLog != null">
        #{updateLog,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity">
    update T_COMMUNICATE_SUMMARY
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="customerIntentions != null">
        CUSTOMER_INTENTIONS = #{customerIntentions,jdbcType=VARCHAR},
      </if>
      <if test="intentionGoods != null">
        INTENTION_GOODS = #{intentionGoods,jdbcType=VARCHAR},
      </if>
      <if test="brands != null">
        BRANDS = #{brands,jdbcType=VARCHAR},
      </if>
      <if test="models != null">
        MODELS = #{models,jdbcType=VARCHAR},
      </if>
      <if test="customerTypes != null">
        CUSTOMER_TYPES = #{customerTypes,jdbcType=VARCHAR},
      </if>
      <if test="isIntention != null">
        IS_INTENTION = #{isIntention,jdbcType=VARCHAR},
      </if>
      <if test="isAddWechat != null">
        IS_ADD_WECHAT = #{isAddWechat,jdbcType=VARCHAR},
      </if>
      <if test="isEffectiveCommunication != null">
        IS_EFFECTIVE_COMMUNICATION = #{isEffectiveCommunication,jdbcType=VARCHAR},
      </if>
      <if test="communicateRecordId != null">
        COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      </if>
      <if test="updateLog != null">
        UPDATE_LOG = #{updateLog,jdbcType=VARCHAR},
      </if>
    </set>
    where COMMUNICATE_SUMMARY_ID = #{communicateSummaryId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.CommunicateSummaryEntity">
    update T_COMMUNICATE_SUMMARY
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CUSTOMER_INTENTIONS = #{customerIntentions,jdbcType=VARCHAR},
      INTENTION_GOODS = #{intentionGoods,jdbcType=VARCHAR},
      BRANDS = #{brands,jdbcType=VARCHAR},
      MODELS = #{models,jdbcType=VARCHAR},
      CUSTOMER_TYPES = #{customerTypes,jdbcType=VARCHAR},
      IS_INTENTION = #{isIntention,jdbcType=VARCHAR},
      IS_ADD_WECHAT = #{isAddWechat,jdbcType=VARCHAR},
      IS_EFFECTIVE_COMMUNICATION = #{isEffectiveCommunication,jdbcType=VARCHAR},
      COMMUNICATE_RECORD_ID = #{communicateRecordId,jdbcType=INTEGER},
      UPDATE_LOG = #{updateLog,jdbcType=VARCHAR}
    where COMMUNICATE_SUMMARY_ID = #{communicateSummaryId,jdbcType=BIGINT}
  </update>
</mapper>