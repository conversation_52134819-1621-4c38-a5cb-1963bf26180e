Vue.component('ui-card-switch', {
    template: `<div class="ui-card-switch-wrap">
       <div class="ui-card-swich-item line-1" title="一列布局" :class="{active: line == 1}" @click="setLine(1)"></div>
       <div class="ui-card-swich-item line-2" title="两列布局" :class="{active: line == 2}" @click="setLine(2)"></div>
    </div>`,
    props: {
        //页面唯一标识，根据这个标识进行当前页面样式切换的缓存存取。
        name: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            line: 1
        }
    },
    created() {
        this.getCardLine();
    },
    mounted(){
       
    },
    methods: {
        getCardLine() {
            let cardLineData = localStorage.getItem('card_line_data');

            if(cardLineData) {
                let lineObj = JSON.parse(cardLineData);

                if(lineObj[this.name]) {
                    this.line = lineObj[this.name];
                }
            }

            this.$emit('input', this.line);
        },
        setLine(num) {
            if(num == this.line) {
                return;
            }

            let lineObj = {};
            let cardLineData = localStorage.getItem('card_line_data');

            if(cardLineData) {
                lineObj = JSON.parse(cardLineData);
            }

            lineObj[this.name] = num;

            localStorage.setItem('card_line_data', JSON.stringify(lineObj));
            this.line = num;

            this.$emit('input', this.line);
        }
    }
})