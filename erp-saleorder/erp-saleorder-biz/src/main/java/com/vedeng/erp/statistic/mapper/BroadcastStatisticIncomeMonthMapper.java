package com.vedeng.erp.statistic.mapper;

import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeDayWithBLOBs;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonth;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonthExample;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonthKey;
import com.vedeng.erp.broadcast.domain.entity.BroadcastStatisticIncomeMonthWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BroadcastStatisticIncomeMonthMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    long countByExample(BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int deleteByExample(BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(BroadcastStatisticIncomeMonthKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int insert(BroadcastStatisticIncomeMonthWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int insertSelective(BroadcastStatisticIncomeMonthWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    List<BroadcastStatisticIncomeMonthWithBLOBs> selectByExampleWithBLOBs(BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    List<BroadcastStatisticIncomeMonth> selectByExample(BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    BroadcastStatisticIncomeMonthWithBLOBs selectByPrimaryKey(BroadcastStatisticIncomeMonthKey key);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") BroadcastStatisticIncomeMonthWithBLOBs record, @Param("example") BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int updateByExampleWithBLOBs(@Param("record") BroadcastStatisticIncomeMonthWithBLOBs record, @Param("example") BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") BroadcastStatisticIncomeMonth record, @Param("example") BroadcastStatisticIncomeMonthExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(BroadcastStatisticIncomeMonthWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int updateByPrimaryKeyWithBLOBs(BroadcastStatisticIncomeMonthWithBLOBs record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_BROADCAST_STATISTIC_INCOME_MONTH
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(BroadcastStatisticIncomeMonth record);
    
    /**
     * 批量插入月到款金额
     * @param broadcastStatisticIncomeDayList
     */
    void batchInsert(@Param("broadcastStatisticIncomeMonthWithBLOBsList") List<BroadcastStatisticIncomeMonthWithBLOBs> broadcastStatisticIncomeMonthWithBLOBsList);
}