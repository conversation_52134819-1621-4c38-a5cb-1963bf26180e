package com.smallhospital.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.newtask.service.ReportOrderSkuService;
import com.smallhospital.constant.UrlConstant;
import com.smallhospital.dao.ElContractMapper;
import com.smallhospital.dao.ElSkuMapper;
import com.smallhospital.dto.*;
import com.smallhospital.model.ElSku;
import com.smallhospital.model.vo.ELSkuVO;
import com.smallhospital.service.ElSkuService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.EmptyUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.api.request.goods.CatAndSkuParameter;
import com.vedeng.goods.api.response.category.CategoryBaseResponce;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.GoodsAttachmentGenerateExtendMapper;
import com.vedeng.goods.model.GoodsAttachment;
import com.vedeng.goods.service.CoreOperateInfoService;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import java.util.*;

@Service
@Deprecated
@Slf4j
public class ElSkuServiceImpl implements ElSkuService {

    @Value("${api_url}")
    private String apiUrl;

    @Value("${api_http}")
    protected String api_http;


    private String goodsApiUrl;

    @Autowired
    private BasePriceService basePriceService;

    @Autowired
    private GoodsAttachmentGenerateExtendMapper goodsAttachmentGenerateExtendMapper;


    @Autowired
    private ElSkuMapper skuMapper;

    @Autowired
    @Qualifier("attachmentMapper")
    private AttachmentMapper attachmentMapper;

    private static final Logger LOGGER = LoggerFactory.getLogger(ElSkuServiceImpl.class);

    @Autowired
    private ElContractMapper contractMapper;

    @Override
    public boolean insert(ElSku record) {
        try {
            this.skuMapper.insertSelective(record);
        }catch (Exception e){
            log.error("【insert】处理异常",e);
            return false;
        }
        return true;
    }

    @Override
    public ElSku findElSkuById(Integer skuId) {
        return skuMapper.selectByPrimaryKey(skuId);
    }

    @Override
    public List<ELSkuVO> querylistPage(ELSkuVO skuVO, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("sku", skuVO);
        map.put("page", page);
        return skuMapper.querylistPage(map);
    }

    @Override
    public void batchAddSkus(List<ElSku> skuLists) {
        this.skuMapper.batchAddSkus(skuLists);
    }

    @Override
    public void deleteById(Integer elSkuId) {
        skuMapper.deleteByPrimaryKey(elSkuId);
    }

    @Override
    public List<Integer> findAllSkuIds() {
        return skuMapper.findAllSkuIds();
    }

//    @Override
//    public List<ELSkuBasicInfo> findElSkulistPage(ELSkuDto skuDto,Page page) {
//        Map<String, Object> map = new HashMap<String, Object>();
//
//        if(!StringUtils.isEmpty(skuDto.getBaseCategoryId())){
//            List<Integer> categoryIds = new ArrayList<>();
//            findChildCategoryId(Integer.valueOf(skuDto.getBaseCategoryId()),categoryIds);
//            map.put("categoryIds", categoryIds);
//        }
//
//        map.put("skuDto", skuDto);
//        map.put("page",page);
//        return skuMapper.findElSkulistPage(map);
//    }
    @Override
    public ELSkuDataInfo findElSkulistPage(ELSkuInfoDto skuDto) {
        LOGGER.info("小医院获取医械购产品数据入参:" + JSON.toJSONString(skuDto));
        TypeReference<RestfulResult<ELSkuDataInfo>> typeReference = new TypeReference<RestfulResult<ELSkuDataInfo>>(){};
        RestfulResult<ELSkuDataInfo> restfulResult = HttpRestClientUtil.restPost(apiUrl + UrlConstant.GET_ALL_SKU_PAGE, typeReference,null, skuDto);
        LOGGER.info("小医院获取医械购产品数据出参:" + JSON.toJSONString(restfulResult));
        ELSkuDataInfo elSkuDataInfo = new ELSkuDataInfo();
        if (restfulResult == null || !restfulResult.isSuccess() || Objects.isNull(restfulResult.getData())){
            return elSkuDataInfo;
        }
        if (restfulResult.getData().getTotal() == null || restfulResult.getData().getTotal().equals(0)){
            elSkuDataInfo.setTotal(0);
        }
        List<ELSkuDetaiDto> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(restfulResult.getData().getSkuList())) {
            restfulResult.getData().getSkuList().stream().forEach(detail -> {
                SkuPriceInfoDetailResponseDto responseDto = basePriceService.findSkuPriceInfoBySkuNo(detail.getProductCode());
                SkuDetailDto detailDto = new SkuDetailDto();
                BeanUtils.copyProperties(detail,detailDto);
                if (responseDto != null){
                    detailDto.setPrice(responseDto.getTerminalPrice().toString());
                }
                ELSkuDetailInfo skuDetailInfo = skuMapper.getDetailSkuInfo(detail.getProductId());
                if (skuDetailInfo != null) {
                    detailDto.setModel(skuDetailInfo.getModel());
                    //医械购分类id
                    CatAndSkuParameter catAndSkuParameter = new CatAndSkuParameter();
                    catAndSkuParameter.setPlatformId(2);
                    catAndSkuParameter.setSkuNo(detail.getProductCode());
                    List<CategoryBaseResponce> categoryList = HttpRestClientUtil.restPost(goodsApiUrl + HttpURLConstant.GOODS_QUERY_CATEGORY_BYSKUNO,
                            new TypeReference<List<CategoryBaseResponce>>(){}, null, catAndSkuParameter);
                    if (CollectionUtils.isNotEmpty(categoryList)){
                        Optional<CategoryBaseResponce> optional= categoryList.stream().filter(category->category.getLEVEL() == 3).findFirst();
                        if (optional != null && optional.isPresent()){
                            Integer categoryId = optional.get().getvCategoryId();
                            detailDto.setCategoryId(categoryId);
                        }
                    }
                    detailDto.setGenericName(precessDefaultValue(skuDetailInfo.getGenericName(), "/"));
                    detailDto.setRemark("");
                    detailDto.setApprovalNumber(precessDefaultValue(skuDetailInfo.getApprovalNumber(), "/"));
                    detailDto.setProductionAddress(precessDefaultValue(skuDetailInfo.getProductionAddress(), "/"));
                    detailDto.setRegistrationCertificateProductName(precessDefaultValue(skuDetailInfo.getRegistrationCertificateProductName(), "/"));
                    detailDto.setValidityDate(Integer.valueOf(precessDefaultValue(skuDetailInfo.getValidityDate(), "0")));
                    detailDto.setValidityDateStart(formatDate(skuDetailInfo.getIssuingDate()));
                    detailDto.setValidityDateEnd(formatDate(skuDetailInfo.getEffectiveDate()));
                    processCrtificatePicAddress(detailDto, skuDetailInfo.getRegistrationNumberId());
                    detailDto.setDocumentType(StringUtil.isEmpty(skuDetailInfo.getApprovalNumber()) ? "2" : "1");
                    detailDto.setCompanyName(precessDefaultValue(skuDetailInfo.getCompanyName(), "/"));
                    detailDto.setCompanyAbbr("/");
                    detailDto.setUniformCreditCode("/");
                    detailDto.setPackageUnit("/");
                    detailDto.setEnterpriseLicenseRecordNumber(precessDefaultValue(skuDetailInfo.getEnterpriseLicenseRecordNumber(), "/"));
                    detailDto.setRegisteredAddress(precessDefaultValue(skuDetailInfo.getRegisteredAddress(), "/"));
                    detailDto.setManufacturerId(precessDefaultValue(skuDetailInfo.getManufacturerId(), ("V" + detail.getProductId())));
                }
                list.add(detailDto);
            });
            elSkuDataInfo.setTotal(restfulResult.getData().getTotal());
        }
        elSkuDataInfo.setSkuList(list);
        return elSkuDataInfo;
    }

    private void processCrtificatePicAddress(SkuDetailDto detail,Integer registerNumberId) {

        if(registerNumberId == null){
            return;
        }

        // 所有附件
        List<Integer> attachmentFunction = Arrays.asList(
                // 注册证附件/备案凭证附件
                CommonConstants.ATTACHMENT_FUNCTION_975,
                // 说明书
                CommonConstants.ATTACHMENT_FUNCTION_976,
                // 营业执照
                CommonConstants.ATTACHMENT_FUNCTION_1000,
                // 生产企业卫生许可证
                CommonConstants.ATTACHMENT_FUNCTION_977,
                // 生产企业生产许可证
                CommonConstants.ATTACHMENT_FUNCTION_978,
                // 商标注册证
                CommonConstants.ATTACHMENT_FUNCTION_979,
                // 注册登记表附件
                CommonConstants.ATTACHMENT_FUNCTION_980,
                // 产品图片（单包装/大包装）
                CommonConstants.ATTACHMENT_FUNCTION_981
        );

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
        paramMap.put("attachmentFunction", attachmentFunction);
        paramMap.put("registrationNumberId", registerNumberId);

        List<Attachment> attachments = attachmentMapper.getAttachmentsList(paramMap);

        // 注册证附件/备案凭证附件
        List<Attachment> zczAttachments = new ArrayList<>();

        // 产品图片（单包装/大包装）
        List<Attachment> picAttachments = new ArrayList<>();

        //生产厂家图片
        List<Attachment> manufacturerAttachments = new ArrayList<>();

        for (int i = 0; i < attachments.size(); i++) {

            Attachment attachment = attachments.get(i);

            if(EmptyUtils.isEmpty(attachment.getUri())){
                continue;
            }

            // 将注册证分组
            if(CommonConstants.ATTACHMENT_FUNCTION_975.equals(attachment.getAttachmentFunction())){
                zczAttachments.add(attachment);
            }

            // 产品图片（单包装/大包装）
            if(CommonConstants.ATTACHMENT_FUNCTION_981.equals(attachment.getAttachmentFunction())){
                picAttachments.add(attachment);
            }

            // 营业执照
            if(CommonConstants.ATTACHMENT_FUNCTION_1000.equals(attachment.getAttachmentFunction())){
                manufacturerAttachments.add(attachment);
            }
            // 说明书
            if(CommonConstants.ATTACHMENT_FUNCTION_976.equals(attachment.getAttachmentFunction())){
                manufacturerAttachments.add(attachment);
            }
            // 生产企业卫生许可证
            if(CommonConstants.ATTACHMENT_FUNCTION_977.equals(attachment.getAttachmentFunction())){
                manufacturerAttachments.add(attachment);
            }
            // 生产企业生产许可证
            if(CommonConstants.ATTACHMENT_FUNCTION_978.equals(attachment.getAttachmentFunction())){
                manufacturerAttachments.add(attachment);
            }
            // 商标注册证
            if(CommonConstants.ATTACHMENT_FUNCTION_979.equals(attachment.getAttachmentFunction())){
                manufacturerAttachments.add(attachment);
            }
            // 注册登记表附件
            if(CommonConstants.ATTACHMENT_FUNCTION_980.equals(attachment.getAttachmentFunction())){
                manufacturerAttachments.add(attachment);
            }
        }

        // 注册证附件/备案凭证附件
        List<String> zczUrls = new ArrayList<>();
        zczAttachments.stream().forEach(attachment -> {
            zczUrls.add(this.api_http+attachment.getDomain()+attachment.getUri());
        });
        detail.setCrtificatePicAddress(zczUrls);

        // 产品图片（单包装/大包装）
        List<String> picUrls = new ArrayList<>();
//        picAttachments.stream().forEach(attachment -> {
//            picUrls.add(this.api_http+attachment.getDomain()+attachment.getUri());
//        });
//        detail.setProductPic(picUrls);

        //查询该sku对应的spu图片
        GoodsAttachment goodsAttachment = new GoodsAttachment();
        goodsAttachment.setGoodsId(detail.getProductId());
        goodsAttachment.setStatus(CommonConstants.STATUS_1);
        goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SKU_1001);
        List<GoodsAttachment> goodsAttachmentList = goodsAttachmentGenerateExtendMapper.getGoodsAttachment(goodsAttachment);
        goodsAttachmentList.stream().forEach(attachment ->{
            picUrls.add(this.api_http + attachment.getDomain()+attachment.getUri());
        });
        detail.setProductPic(picUrls);
        //生产厂家图片
        List<String> manufacturerPicUrls = new ArrayList<>();
        manufacturerAttachments.stream().forEach(attachment -> {
            manufacturerPicUrls.add(this.api_http+attachment.getDomain()+attachment.getUri());
        });
        detail.setManufacturerPicUrls(manufacturerPicUrls);
    }

    private String formatDate(Long dateTime){
        if(dateTime == null || dateTime == 0L){
            return "";
        }
        return DateUtil.convertString(dateTime,"yyyy-MM-dd");
    }

    public String precessDefaultValue(String value,String defaultValue){
        return StringUtil.isEmpty(value) ? defaultValue : value;
    }


    /**
     * 递归查询当分类的子分类id
     * @param baseCategoryId
     * @param categoryIds
     */
    private void findChildCategoryId(Integer baseCategoryId, List<Integer> categoryIds) {
        categoryIds.add(baseCategoryId);
        List<Integer> childCategoryIds = skuMapper.findChildCategoryId(baseCategoryId);
        if(CollectionUtils.isEmpty(childCategoryIds)){
            return;
        }
        childCategoryIds.forEach(categoryId -> {
            findChildCategoryId(categoryId,categoryIds);
        });
    }


    @Override
    public ELSkuDetailInfo findDetailSkuInfo(Integer skuId) {
        return skuMapper.findDetailSkuInfo(skuId);
    }

    @Override
    public boolean checkIsValidSku(Integer skuId) {
        return skuMapper.findValidSku(skuId) > 0;
    }

    @Override
    public List<ElDateInfo> getDeliveryDate(ElDateDto dateDto) {
        LOGGER.info("查询医械购产品的发货日期入参:" + JSON.toJSONString(dateDto));
        List<String> skuNos = new ArrayList<>();
        dateDto.getProductIdList().stream().forEach(productId -> skuNos.add("V"+productId));
        TypeReference<RestfulResult<List<Map<String,String>>>> typeReference = new TypeReference<RestfulResult<List<Map<String,String>>>>(){};
        RestfulResult<List<Map<String,String>>> restfulResult = HttpRestClientUtil.restPost(apiUrl + UrlConstant.GET_DELIVERY_DATE , typeReference,null,skuNos);
        LOGGER.info("查询医械购产品的发货日期出参:" + JSON.toJSONString(restfulResult));
        if (Objects.isNull(restfulResult) || !restfulResult.isSuccess() || Objects.isNull(restfulResult.getData())){
            return new ArrayList<>();
        }
        List<ElDateInfo> dateInfos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(restfulResult.getData())){
            restfulResult.getData().stream().forEach(map ->{
                ElDateInfo dateInfo = new ElDateInfo();
                dateInfo.setProductId(Integer.valueOf(map.get("productCode").substring(1)));
                dateInfo.setDeliveryDate(map.get("stock"));
                dateInfos.add(dateInfo);
            });
        }
        return dateInfos;
    }
}