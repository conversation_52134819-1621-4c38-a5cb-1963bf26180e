package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeProfitLossDto;
import com.vedeng.erp.kingdee.service.KingDeeProfitLossApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 盘亏出库
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchProfitLossWriter extends BaseWriter<KingDeeProfitLossDto> {

    @Autowired
    private KingDeeProfitLossApiService kingDeeProfitLossApiService;

    @Override
    public void doWrite(KingDeeProfitLossDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchProfitLossWriterService#doWrite,盘亏出库{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveProfitLoss);
        kingDeeProfitLossApiService.register(dto,true);
    }

}
