package com.newtask.quoteorder.strategy;

import com.newtask.quoteorder.model.QuotedAlarmRecord;
import com.vedeng.order.enums.QuotedAlarmLevelEnum;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import com.vedeng.order.model.Quoteorder;

import java.time.Duration;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 采购端触发报价预警策略
 *
 * <AUTHOR> [<EMAIL>]
 */
public class SupplierStrategy extends AbstractQuotedAlarmStrategy implements QuotedAlarmStrategy {

    /**
     * Time Intervals
     */
    private final static Duration HALF_HOUR = Duration.ofMinutes(30);
    private final static Duration ONE_HOUR = Duration.ofHours(1);
    private final static Duration ONE_DAY = Duration.ofDays(1);


    private final static Map<Integer, Duration> UPGRADE_RULE_MAP = new LinkedHashMap<>(3);

    static {
        UPGRADE_RULE_MAP.put(QuotedAlarmLevelEnum.LEVEL_ONE.getLevel(), ONE_DAY);
        UPGRADE_RULE_MAP.put(QuotedAlarmLevelEnum.LEVEL_TWO.getLevel(), ONE_HOUR);
        UPGRADE_RULE_MAP.put(QuotedAlarmLevelEnum.LEVEL_THREE.getLevel(), HALF_HOUR);
    }

    @Override
    public void upgradeForNextLevel(QuotedAlarmRecord quotedAlarmRecord) {
        Integer upgradedLevel = getNextLevel(quotedAlarmRecord.getCurrentLevel());

        Duration interval = UPGRADE_RULE_MAP.get(upgradedLevel);

        long nextTriggerTime = getNextTriggerTime(getBeginTimeForNextTrigger(quotedAlarmRecord), interval, true);

        quotedAlarmRecord.setTriggerTime(nextTriggerTime);
        quotedAlarmRecord.setCurrentLevel(upgradedLevel);
    }

    @Override
    protected QuotedAlarmModeEnum getMode() {
        return QuotedAlarmModeEnum.PURCHASER_MODE;
    }
}
