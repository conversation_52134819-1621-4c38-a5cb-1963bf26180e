package com.vedeng.common.core.listenerEvent.eventresult;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/5 9:44
 * @version 1.0
 */
@Setter
@Getter
public class AuditRecordEventResult {
    /**
     * 操作人
     */
    public String operator;

    /**
     * 操作时间
     */
    public Date operationTime;

    /**
     * 操作事项
     */
    public String operation;

    /**
     * 备注
     */
    public String remark;
}