package com.vedeng.erp.kingdee.batch.common.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ItemReadListener;
import org.springframework.batch.core.ItemWriteListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 写监听器
 * @date 2023/2/22 10:22
 */
@Component
@Slf4j
public class BaseWriteListener<T> implements ItemWriteListener<T> {

    @Override
    public void beforeWrite(List<? extends T> items) {
        log.info("写之前: {}", JSONObject.toJSONString(items));
    }

    @Override
    public void afterWrite(List<? extends T> items) {
        log.info("写之后:{}", JSONObject.toJSONString(items));
    }

    @Override
    public void onWriteError(Exception ex, List<? extends T> items) {
        log.error("写 error: {} , error message: {}", items, ex.getMessage(), ex);
    }
}
