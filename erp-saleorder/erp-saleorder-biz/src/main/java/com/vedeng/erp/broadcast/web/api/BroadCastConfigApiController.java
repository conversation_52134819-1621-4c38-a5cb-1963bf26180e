package com.vedeng.erp.broadcast.web.api;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.erp.broadcast.domain.dto.BroadcastGlobalConfigDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity;
import com.vedeng.erp.broadcast.service.BroadcastGlobalConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 到款通知配置
 */
@RestController
@RequestMapping("/broadcast/config")
public class BroadCastConfigApiController {


    private static final Logger log = LoggerFactory.getLogger(BroadCastConfigApiController.class);
    @Resource
    BroadcastGlobalConfigService broadcastGlobalConfigService;
    /**
     *  初始化
     * @return BroadcastGlobalConfigDto
     */
    @RequestMapping("/init")
    @ResponseBody
    public R<BroadcastGlobalConfigDto> init() {
        BroadcastGlobalConfigDto dto=broadcastGlobalConfigService.getGlobalConfig();
        return R.success(dto);
    }
    /**
     * 保存
     * @return BroadcastGlobalConfigDto
     */
    @RequestMapping("/saveOrUpdate")
    @ResponseBody
    public R<BroadcastGlobalConfigDto> save(HttpServletRequest request, @RequestBody BroadcastGlobalConfigDto dto) {
        CurrentUser user = CurrentUser.getCurrentUser();
        if (user == null) {
            return R.error(CommonConstants.FAIL_CODE, "登陆信息过期");
        }
        try {
            broadcastGlobalConfigService.updateGlobalConfig(dto, user);
            return R.success();
        }catch (Exception e){
            log.error("",e);
            return R.error(CommonConstants.FAIL_CODE, "保存失败");
        }

    }

}
