package com.vedeng.filedelivery.model;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * apollo统一配置文件，文件寄送快递100对接
 * @ClassName:  ApolloCommon   
 * @author: <PERSON>.yang
 * @date:   2024年10月25日 上午10:20:15    
 * @Copyright:
 */
@Component
public class FileDeliveryApolloCommon {
	
	/**是否可以进行面单下单*/
    @Value("${file_delivery_can_submit_order:true}")
	public boolean canSubmitOrder;
	
	/**面单下单中寄件人名称*/
    @Value("${file_delivery_send_qw_message:0}")
	public Integer sendQwMessage;
	
	//=======================================================================

	/**面单下单中寄件人名称*/
    @Value("${file_delivery_send_name:物流部}")
	public String fileDeliverySendName;
    
    /**面单下单中寄件人电话*/
    @Value("${file_delivery_send_mobile:025-68538253}")
	public String fileDeliverySendMobile;
    
    //=======================================================================
    
    /**快递100接口key*/
    @Value("${file_delivery_kd100_key:vIStKARH975}")
	public String fileDeliveryKd100Key;
    
    /**快递100接口secret*/
    @Value("${file_delivery_kd100_secret:36ad932779ac40b8ae777fa0f12988ce}")
	public String fileDeliveryKd100Secret;
    
    //=======================================================================
    
    /**ems快递公司名称*/
    @Value("${file_delivery_ems_com:ems}")
	public String fileDeliveryEmsCom;
    
    /**ems快递公司ID*/
    @Value("${file_delivery_ems_id:1100101858690}")
	public String fileDeliveryEmsId;
    
    /**ems快递公司key*/
    @Value("${file_delivery_ems_key:23539006211001018586901}")
	public String fileDeliveryEmsKey;
    
    /**ems快递公司模板ID*/
    @Value("${file_delivery_ems_tempId:b1998f0d0f004be89690018912da9a73}")
	public String fileDeliveryEmsTempId;

    //=======================================================================
    
    /**邮政电商标快公司名称*/
    @Value("${file_delivery_emsdsbk_com:youzhengdsbk}")
	public String fileDeliveryEmsdsbkCom;
    
    /**邮政电商标快公司ID*/
    @Value("${file_delivery_emsdsbk_id:1100101858690}")
	public String fileDeliveryEmsdsbkId;
    
    /**邮政电商标快公司key*/
    @Value("${file_delivery_emsdsbk_key:235390062110010185869010}")
	public String fileDeliveryEmsdsbkKey;
    
    /**邮政电商标快公司模板ID*/
    @Value("${file_delivery_emsdsbk_tempId:a9c67d8ad4d446dc81c5a6aa828e9acf}")
	public String fileDeliveryEmsdsbkTempId;
    
    
    //=======================================================================
    
    /**顺丰快递公司名称*/
    @Value("${file_delivery_sf_com:shunfeng}")
	public String fileDeliverySfCom;
    
    /**顺丰快递公司ID*/
    @Value("${file_delivery_sf_id:0255074446}")
	public String fileDeliverySfId;

    /**顺丰快递公司key*/
    @Value("${file_delivery_sf_key:NJBDYL}")
	public String fileDeliverySfKey;
    
    /**顺丰快递公司密码*/
    @Value("${file_delivery_sf_secret:DFcGeY9XcaF6cNoIpiAZ3flqYJftsTiz}")
	public String fileDeliverySfSecret;
    
    /**顺丰快递公司模板ID*/
    @Value("${file_delivery_sf_tempId:f43e6af2888b401e9888a0118b5f5685}")
	public String fileDeliverySfTempId;
    
    /**顺丰快递公司子模板ID*/
    @Value("${file_delivery_sf_child_tempId:f43e6af2888b401e9888a0118b5f5685}")
    public String fileDeliverySfChildTempId;
    //=======================================================================
    
    /**zt快递公司名称*/
    @Value("${file_delivery_zt_com:zhongtong}")
	public String fileDeliveryZtCom;
    
    /**zt快递公司ID*/
    @Value("${file_delivery_zt_id:ZTO771608534610173}")
	public String fileDeliveryZtId;
    
    /**zt快递公司key*/
    @Value("${file_delivery_zt_key:YWCWXX8D}")
	public String fileDeliveryZtKey;
    
    /**zt快递公司模板ID*/
    @Value("${file_delivery_zt_tempId:45be2fc745164054b4de6abef8764de0}")
	public String fileDeliveryZtTempId;
    
    //=======================================================================
 
    @Value("${file_delivery_ems_printname:EMS}")
	public String emsprintname; 
	
    @Value("${file_delivery_sf_printname:顺丰}")
	public String sfprintname;
	
    @Value("${file_delivery_ems_printname_cw:QR-588}")
	public Object emsprintname_cw;

    @Value("${file_delivery_sf_printname_cw:ZDesigner GK88d}")
	public Object sfprintname_cw;
    
    
    @Value("${file_delivery_zt_printname:ZTO}")
	public String ztprintname;

    //=======================================================================
    /**默认生成的打印码 在 0000 ~ 9999之间*/
    @Value("${file_delivery_max_printCode:10000}")
	public int maxPrintCode;

    /**打印码即将耗尽时的提示阈值，即 maxPrintCode 已使用了 tipsPrintCode 个进行log.error提示*/
    @Value("${file_delivery_tips_printCode:8000}")
	public Integer tipsPrintCode;

    @Value("${file_delivery_qw_message_url}")
	public String qwMessageUrl;


    
}   
