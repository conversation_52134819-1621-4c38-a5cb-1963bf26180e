package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.BuyorderGoods;
import java.util.List;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named("newBuyorderGoodsMapper")
public interface BuyorderGoodsMapper {
    int deleteByPrimaryKey(Integer buyorderGoodsId);

    int insert(BuyorderGoods record);

    int insertOrUpdate(BuyorderGoods record);

    int insertOrUpdateSelective(BuyorderGoods record);

    int insertSelective(BuyorderGoods record);

    BuyorderGoods selectByPrimaryKey(Integer buyorderGoodsId);

    int updateByPrimaryKeySelective(BuyorderGoods record);

    int updateByPrimaryKey(BuyorderGoods record);

    int updateBatch(List<BuyorderGoods> list);

    int updateBatchSelective(List<BuyorderGoods> list);

    int batchInsert(@Param("list") List<BuyorderGoods> list);

    List<BuyorderGoods> findByBuyorderId(@Param("buyorderId")Integer buyorderId);

}