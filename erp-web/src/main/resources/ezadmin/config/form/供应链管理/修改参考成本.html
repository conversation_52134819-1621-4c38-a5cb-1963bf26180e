<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>修改参考成本</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="update_costprice" datasource="erp-datasourcetarget" success_url="reload">
  <div id="APPEND_HEAD" class="append"></div>
  <div class="layui-container">
   <form id="inputForm" method="post" class="layui-form">
    <div class="layui-card" group_name="EZ_DEFAULT_GROUP">
     <div class="layui-card-header">
       EZ_DEFAULT_GROUP 
     </div>
     <div class="layui-card-body">
      <div class="layui-form-item "><label class="layui-form-label">订单号</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="SALEORDER_NO" type="span" value="" item_name="SALEORDER_NO" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">订货号</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="SKU" type="span" value="" item_name="SKU" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item "><label class="layui-form-label">商品名称</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="GOODS_NAME" type="span" value="" item_name="GOODS_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
      <div class="layui-form-item ">
       <label class="layui-form-label">价格</label>
       <div class="layui-input-block form-group">
        <input class="form-item" name="REFERENCE_COST_PRICE" type="text" value="" item_name="REFERENCE_COST_PRICE" placeholder="" style="" alias="" jdbctype="" data="" datatype="" lay_verify="required|number" item_desc="" group_data="" valid_rule="" valid_msg="" item_max_upload="">
       </div>
      </div>
     </div>
    </div>
   </form>
   <form id="configForm" class="layui-form">
    <div class="layui-card" group_name="表单数据配置">
     <div class="layui-card-header">
       表单数据配置 
     </div>
     <div class="layui-card-body">
     </div>
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">初始表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="init" name="INIT_EXPRESS" type="">StringBuilder sql=new StringBuilder();

sql.append("select SALEORDER_GOODS_ID ID,B.SALEORDER_NO   ,A.SKU  ,A.GOODS_NAME  ,
       REFERENCE_COST_PRICE   FROM T_SALEORDER_GOODS A
LEFT JOIN T_SALEORDER B ON  A.SALEORDER_ID=B.SALEORDER_ID
WHERE A.IS_DELETE=0
      and  SALEORDER_GOODS_ID=${ID}  ");

return select(sql).get(0);</pre>
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">提交表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="submit" name="SUBMIT_EXPRESS" type="">companyId=$$("COMPANY_ID");                 		
sessionUserId=$$("EZ_SESSION_USER_ID_KEY");        
sessionUserName=$$("EZ_SESSION_USER_NAME_KEY");    
if(!isNotBlank("ID")){                  		

 	return 0;
}else{                 								
	StringBuilder updateSql=new StringBuilder();				
	updateSql.append("update T_SALEORDER_GOODS set ");                	
	updateSql.append("
 		REFERENCE_COST_PRICE = #{REFERENCE_COST_PRICE}
");							

	updateSql.append(" where SALEORDER_GOODS_ID=#{ID} ");				

	update(updateSql);                							
	return ID;                 							
}</pre>
     </div>
    </div>
    <div class="layui-form-item "><label class="layui-form-label">删除表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" id="delete" name="DELETE_EXPRESS" type=""></pre>
     </div>
    </div>
    <div class="layui-form-item ">
     <label class="layui-form-label">分组表达式</label>
     <div class="layui-input-block form-group">
      <pre class="form-item layui-code" name="GROUP_DATA" type=""></pre>
     </div>
    </div>
   </form>
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
  <div id="APPEND_FOOT" class="append">
  </div>
 </body>
</html>