Vue.component('ui-date-range', {
    template: `<div class="vd-ui-date-range-wrap">
        <div class="vd-ui-date-range-item">
            <ui-date-picker
                type="date"
                placeholder="起始时间"
                v-model="startTime"
                :max="endTime"
                @input="handlerChange"
            ></ui-date-picker>
        </div>
        <div class="vd-ui-date-range-gap">-</div>
        <div class="vd-ui-date-range-item">
            <ui-date-picker
                type="date"
                placeholder="截止时间"
                v-model="endTime"
                :min="startTime"
                @input="handlerChange"
            ></ui-date-picker>
        </div>
    </div>`,
    data() {
        return {
            startTime: '',
            endTime: ''
        }
    },
    props: {
       value: {
            type: Array,
            default() {
                return [];
            }
       }
    },
    computed: {

    },
    mounted() {
        if(this.value && this.value.length) {
            this.startTime = this.value[0] || '';
            this.endTime = this.value[1] || '';
        }
    },
    methods: {
        handlerChange() {
            let value = [];
            if(this.startTime || this.endTime) {
                value = [this.startTime, this.endTime];
            }
            this.value = value;
            this.$emit('input', value);
            this.$emit('change', value);
        }
    }
})
