package com.wms.inventoryadjustment.controller;

import com.alibaba.fastjson.JSONObject;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.system.service.RoleService;
import com.wms.inventoryadjustment.model.dto.InventoryAdjustmentSearchDto;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentDetailVo;
import com.wms.inventoryadjustment.model.vo.InventoryAdjustmentVo;
import com.wms.inventoryadjustment.service.InventoryAdjustmentService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存调整
 *
 * @author: hugo
 * @date 2020/7/29 11:40:18
 */
@Controller
@RequestMapping("/wms/inventory")
public class InventoryAdjustmentController extends BaseController {
    @Autowired
    private InventoryAdjustmentService inventoryAdjustmentService;

    @Autowired
    private RoleService roleService;

    @Value("${adjustment_detail_per}")
    private String adjustmentDetailPer;

    /**
     * <b>Description:</b><br> 库存调整单列表
     *
     * @return
     * @Note <b>Author:</b> hugo
     * <br><b>Date:</b> 2020年7月29日 13:13:13
     */
    @ResponseBody
    @RequestMapping(value = "/inventoryAdjustment")
    public ModelAndView inventoryAdjustment(HttpServletRequest request,
                                            InventoryAdjustmentSearchDto inventoryAdjustmentSearchDto,
                                            @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                            @RequestParam(required = false, defaultValue = "10") Integer pageSize) {
        ModelAndView mav = new ModelAndView("wms/inventory/index_inventoryAdjustment");
        try {
            logger.info("检索库存调整单列表 inventoryAdjustmentSearchDto:{}" + JsonUtils.translateToJson(inventoryAdjustmentSearchDto));
            Page page = getPageTag(request, pageNo, pageSize);
            Map<String, Object> map = inventoryAdjustmentService.getInventoryAdjustmentVos(inventoryAdjustmentSearchDto, page);
            if (MapUtils.isNotEmpty(map)) {
                mav.addObject("inventoryAdjustmentVos", map.get("inventoryAdjustmentVos"));
                mav.addObject("page", map.get("page"));
            }
        } catch (Exception e) {
            logger.error("/warehouse/warehouses/inventoryAdjustment", e);
        }
        mav.addObject("inventoryAdjustmentSearchDto", inventoryAdjustmentSearchDto);
        return mav;
    }

    /**
     * <b>Description:</b><br> 库存调整单详情
     *
     * @return
     * @Note <b>Author:</b> hugo
     * <br><b>Date:</b> 2020年7月29日 13:13:13
     */
    @ResponseBody
    @RequestMapping(value = "/inventoryAdjustmentDetail")
    public ModelAndView inventoryAdjustmentDetail(Integer inventoryAdjustmentId, HttpSession session) {
        logger.info("查看库存转移单详情信息 + inventoryAdjustmentId" + inventoryAdjustmentId);
        ModelAndView mav = new ModelAndView("wms/inventory/view_inventoryAdjustmentDetail");
        try {
            InventoryAdjustmentVo inventoryAdjustmentVo = inventoryAdjustmentService.getInventoryAdjustmentById(inventoryAdjustmentId);
            List<InventoryAdjustmentDetailVo> inventoryAdjustmentDetailVos = inventoryAdjustmentService.getInventoryAdjustmentDetailsById(inventoryAdjustmentId);
            mav.addObject("inventoryAdjustmentVo", inventoryAdjustmentVo);
            mav.addObject("inventoryAdjustmentDetailVos", inventoryAdjustmentDetailVos);
        } catch (Exception e) {
            logger.error("wms/inventory/inventoryAdjustmentDetail", e);
        }

        try {
            User sessionUser = (User) session.getAttribute(ErpConst.CURR_USER);
            List<Integer> userRoleIds = roleService.getUserRoles(sessionUser).stream().map(Role::getRoleId).collect(Collectors.toList());
            JSONObject.parseArray(adjustmentDetailPer, Integer.class).stream().forEach(roleId -> {
                if (CollectionUtils.isNotEmpty(userRoleIds)) {
                    userRoleIds.stream().forEach(userRoleId -> {
                        if (roleId.equals(userRoleId)) {
                            mav.addObject("havePermission", true);
                        }
                    });
                }
            });
        } catch (Exception e) {
            logger.error("获取库存转移单详情价格权限error inventoryAdjustmentId: " + inventoryAdjustmentId, e);
        }
        return mav;
    }
}
