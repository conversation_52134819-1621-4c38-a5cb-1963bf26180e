<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.BusinessOrderCategoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity">
    <!--@mbg.generated-->
    <!--@Table T_BUSINESS_ORDER_CATEGORY-->
    <id column="ID" jdbcType="BIGINT" property="id" />
    <result column="BUSINESS_ID" jdbcType="INTEGER" property="businessId" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="KEYWORDS" jdbcType="VARCHAR" property="keywords" />
    <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, BUSINESS_ID, BUSINESS_TYPE, KEYWORDS, CATEGORY_ID, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_BUSINESS_ORDER_CATEGORY
    where ID = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_BUSINESS_ORDER_CATEGORY
    where ID = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_ORDER_CATEGORY (BUSINESS_ID, BUSINESS_TYPE, KEYWORDS, 
      CATEGORY_ID, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME, ADD_TIME, 
      MOD_TIME)
    values (#{businessId,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, #{keywords,jdbcType=VARCHAR}, 
      #{categoryId,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_ORDER_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="keywords != null and keywords != ''">
        KEYWORDS,
      </if>
      <if test="categoryId != null">
        CATEGORY_ID,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="keywords != null and keywords != ''">
        #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity">
    <!--@mbg.generated-->
    update T_BUSINESS_ORDER_CATEGORY
    <set>
      <if test="businessId != null">
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="keywords != null and keywords != ''">
        KEYWORDS = #{keywords,jdbcType=VARCHAR},
      </if>
      <if test="categoryId != null">
        CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.BusinessOrderCategoryEntity">
    <!--@mbg.generated-->
    update T_BUSINESS_ORDER_CATEGORY
    set BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      KEYWORDS = #{keywords,jdbcType=VARCHAR},
      CATEGORY_ID = #{categoryId,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_BUSINESS_ORDER_CATEGORY
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="BUSINESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessId != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.businessId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.businessType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="KEYWORDS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.keywords != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.keywords,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="CATEGORY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.categoryId != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.categoryId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BUSINESS_ORDER_CATEGORY
    (BUSINESS_ID, BUSINESS_TYPE, KEYWORDS, CATEGORY_ID, CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, ADD_TIME, MOD_TIME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.businessId,jdbcType=INTEGER}, #{item.businessType,jdbcType=INTEGER}, #{item.keywords,jdbcType=VARCHAR}, 
        #{item.categoryId,jdbcType=INTEGER}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2025-04-12-->
  <select id="findByBusinessIdAndBusinessType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_BUSINESS_ORDER_CATEGORY
    where BUSINESS_ID=#{businessId,jdbcType=INTEGER} and BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
  </select>

  <!--根据业务ID和业务类型删除记录-->
  <delete id="deleteByBusinessIdAndBusinessType">
    delete from T_BUSINESS_ORDER_CATEGORY
    where BUSINESS_ID=#{businessId,jdbcType=INTEGER} and BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
  </delete>
</mapper>