package com.vedeng.order.service.impl;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleContractAutoAuditService;
import com.vedeng.order.service.SaleorderDataSyncService;
import com.vedeng.system.service.VerifiesRecordService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName SaleContractAutoAuditServiceImpl.java
 * @Description TODO
 * @createTime 2023年03月23日 16:14:00
 */
@Service
public class SaleContractAutoAuditServiceImpl implements SaleContractAutoAuditService {

    Logger logger= LoggerFactory.getLogger(SaleContractAutoAuditServiceImpl.class);
    @Autowired
    @Qualifier("saleorderMapper")
    private SaleorderMapper saleorderMapper;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Resource
    private SaleorderDataSyncService saleorderDataSyncService;


    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Override
    public void startProcessInstance(Saleorder saleorder, User user) {
            try {
//                Map<String, Object> historicInfo3 = actionProcdefService.getHistoric(processEngine,
//                        "contractReturnVerify_" + "_" + saleorder.getSaleorderId());
//                if (historicInfo3.get("endStatus") != null) {
//                    if (!historicInfo3.get("endStatus").equals("驳回")) {
//                        logger.info("contractReturnVerify合同审核重复开启:", saleorder.getSaleorderNo());
//                        return;
//                    }
//                }

                Saleorder saleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorder.getSaleorderId());
                Map<String, Object> variableMap = new HashMap<String, Object>();
                saleorder.setOptType("orderDetail");
                variableMap.put("saleorderInfo", saleorderInfo);
                variableMap.put("currentAssinee", user.getUsername());
                variableMap.put("processDefinitionKey", "contractReturnVerify");
                variableMap.put("businessKey", "contractReturnVerify_" + saleorder.getSaleorderId());
                variableMap.put("relateTableKey", saleorder.getSaleorderId());
                variableMap.put("relateTable", "T_SALEORDER");
                actionProcdefService.createProcessInstance(null, "contractReturnVerify",
                        "contractReturnVerify_" + saleorder.getSaleorderId(), variableMap);
                // 默认申请人通过
                // 根据BusinessKey获取生成的审核实例
                Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                        "contractReturnVerify_" + saleorder.getSaleorderId());
                if (historicInfo.get("endStatus") != "审核完成") {
                    Task taskInfo = (Task) historicInfo.get("taskInfo");
                    String taskId = taskInfo.getId();
                    Authentication.setAuthenticatedUserId(user.getUsername());
                    Map<String, Object> variables = new HashMap<String, Object>();
                    // 默认审批通过
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskId, "",
                            user.getUsername(), variables);
                    // 如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    }
                }

                saleorderDataSyncService.syncContractStatusBySaleorderId(saleorder.getSaleorderId(),"apply");
            }catch (Exception e){
                logger.error("onlie editApplyValidContractReturn:", e);
            }

    }
}
