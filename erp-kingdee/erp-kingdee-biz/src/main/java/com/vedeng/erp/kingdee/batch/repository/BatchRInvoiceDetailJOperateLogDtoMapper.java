package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.finance.dto.InvoiceRelationOperateLogDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface BatchRInvoiceDetailJOperateLogDtoMapper {
    List<BatchRInvoiceDetailJOperateLogDto> findByAll(BatchRInvoiceDetailJOperateLogDto batchRInvoiceDetailJOperateLogDto);

    List<BatchRInvoiceDetailJOperateLogDto> findByInvoiceIds(@Param("ids")List<Integer> ids);

    List<BatchRInvoiceDetailJOperateLogDto> findByInvoiceId(@Param("invoiceId")Integer invoiceId);

    List<BatchRInvoiceDetailJOperateLogDto> findByInvoiceIdAndOperateType(@Param("invoiceId")Integer invoiceId,
                                                                                       @Param("operateType")Integer operateType);

    int insertSelective(BatchRInvoiceDetailJOperateLogDto record);

    int updateByPrimaryKeySelective(BatchRInvoiceDetailJOperateLogDto record);

    List<BatchRInvoiceDetailJOperateLogDto> getValidWarehousingLogByRelatedId(@Param("detailGoodsId") Integer detailGoodsId,
                                                                              @Param("afterSaleNo") String afterSaleNo,
                                                                              @Param("operateType") Integer operateType,
                                                                              @Param("isVirtual") Integer isVirtual);

    List<BatchRInvoiceDetailJOperateLogDto> getValidWarehousingLogByRelatedIds(@Param("detailGoodsIdList") List<Integer> detailGoodsIdList,
                                                                              @Param("operateType") Integer operateType);
    /**
     * 根据 销售出库记录详情id 查询该条出库记录详情已被分摊的数量总和
     *
     * @param operateLogId 出入库记录详情id
     * @return 已被分摊的数量总和
     */
    BigDecimal getSumNumByOperateLogId(@Param("operateLogId") Integer operateLogId);

    /**
     * 条件检索售后商品ID
     */
    Integer getAfterGoodsIdByCondition(@Param("saleorderGoodsId") Integer saleorderGoodsId,@Param("afterSalesId") Integer afterSalesId);

    /**
     * 根据发票明细id和操作类型查询出入库明细id
     *
     * @param invoiceDetailId
     * @param operateType
     * @return
     */
    List<BatchRInvoiceDetailJOperateLogDto> findByInvoiceDetailIdAndOperateType(@Param("invoiceDetailId")Integer invoiceDetailId, @Param("operateType")Integer operateType);

    /**
     * 根据发票id和操作类型查询对应的出库单信息
     *
     * @param invoiceId   发票id
     * @param operateType 操作类型 1采购入库 2销售出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库  12盘盈入库 13报废出库 14领用出库 16 盘亏出库,
     * @return 最大出入库时间
     */
    Date findWarehouseOutByInvoiceId(@Param("invoiceId") Integer invoiceId, @Param("operateType") Integer operateType);

    /**
     * 查询入库单所有数据
     * @param operateLogId
     * @return
     */
    List<BatchRInvoiceDetailJOperateLogDto> findByOperateLogId(@Param("operateLogId")Integer operateLogId);



    /**
     * 查询当前发票明细已经绑定过出库单的数量
     *
     * @param invoiceDetailId 发票明细id
     * @return 数量
     */
    BigDecimal getInvoiceDetailOccupyNum(@Param("invoiceDetailId") Integer invoiceDetailId);


    /**
     * 更具主键删除
     * @param rInvoiceDetailJOperateLogIds
     * @return
     */
    int deleteByRInvoiceDetailJOperateLogId(@Param("list")List<Integer> rInvoiceDetailJOperateLogIds);


}