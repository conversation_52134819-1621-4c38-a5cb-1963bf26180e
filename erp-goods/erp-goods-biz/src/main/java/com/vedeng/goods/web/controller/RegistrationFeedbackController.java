package com.vedeng.goods.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.goods.domain.dto.FeedbackCountDto;
import com.vedeng.goods.mapper.RegistrationFeedbackRecordMapper;
import com.vedeng.goods.service.RegistrationFeedbackRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @create 2022−10-31 上午10:23
 * @description
 */
@Controller
@RequestMapping("/registrationFeedbackController")
@Slf4j
public class RegistrationFeedbackController extends BaseController {

    @Resource
    RegistrationFeedbackRecordMapper feedbackRecordMapper;
    @Resource
    RegistrationFeedbackRecordService feedbackRecordService;

    @RequestMapping(value = "/view")
    @ExcludeAuthorization
    public ModelAndView registrationIssueFeedback(@RequestParam("registrationNumberId") Integer registrationNumberId,
                                                  @RequestParam("registrationNumber") String registrationNumber,
                                                  @RequestParam("levelName") String levelName,
                                                  @RequestParam("positionName") String positionName,
                                                  @RequestParam("goodsLevelNo")Integer goodsLevelNo,
                                                  @RequestParam("goodsPositionNo")Integer goodsPositionNo,
                                                  @RequestParam("productMgrName")String productMgrName,
                                                  @RequestParam("assignmentManagerId")Integer assignmentManagerId,
                                                  @RequestParam("productAssistantName")String productAssistantName,
                                                  @RequestParam("assignmentAssistantId")Integer assignmentAssistantId,
                                                  @RequestParam("fileType")Integer fileType,
                                                  @RequestParam("firstEngageId")Integer firstEngageId){
        try {
            ModelAndView mv = new ModelAndView("vue/view/registrationFeedback/add");
            mv.addObject("registrationNumberId", registrationNumberId);
            mv.addObject("registrationNumber", URLDecoder.decode(registrationNumber, "utf-8"));
            mv.addObject("levelName", URLDecoder.decode(levelName, "utf-8"));
            mv.addObject("positionName", goodsPositionNo == 0 ? "无档位": URLDecoder.decode(positionName, "utf-8"));
            mv.addObject("goodsLevelNo", goodsLevelNo);
            mv.addObject("goodsPositionNo", goodsPositionNo);
            mv.addObject("productMgrName", URLDecoder.decode(productMgrName, "utf-8"));
            mv.addObject("assignmentManagerId", assignmentManagerId);
            mv.addObject("productAssistantName", URLDecoder.decode(productAssistantName, "utf-8"));
            mv.addObject("assignmentAssistantId", assignmentAssistantId);
            mv.addObject("fileType", fileType);
            mv.addObject("firstEngageId", firstEngageId);
            mv.addObject("fileTypeLabel", fileType == 1 ? "附件" : "源文件");
            //当前商品是否有历史反馈问题及各类问题反馈数量
            Map<String,Object> result = feedbackRecordService.handleFeedbackNum(registrationNumberId);
            mv.addObject("countInfo",result.get("sb"));
            mv.addObject("hasHistoryFeedback",result.get("flag"));
            return mv;
        } catch (UnsupportedEncodingException e) {
            log.error("【registrationIssueFeedback】处理异常",e);
        }
        return null;
    }

    @RequestMapping(value = "/handle")
    @ExcludeAuthorization
    public ModelAndView handle(@RequestParam("registrationFeedbackRecordId")Integer registrationFeedbackRecordId,
                               @RequestParam("problemType")Integer problemType,
                               @RequestParam("creatorLabel")String creatorLabel){
        ModelAndView mv = new ModelAndView("vue/view/registrationFeedback/handle");
        mv.addObject("registrationFeedbackRecordId",registrationFeedbackRecordId);
        switch (problemType){
            case 1:
                mv.addObject("problemType","印章缺失/不清晰");
                break;
            case 2:
                mv.addObject("problemType","打印内容模糊");
                break;
            case 3:
                mv.addObject("problemType","水印内容不当");
                break;
            case 4:
                mv.addObject("problemType","已过期");
                break;
            case 5:
                mv.addObject("problemType","其他");
                break;
            default:
                break;
        }
        try {
            mv.addObject("creatorLabel",URLDecoder.decode(creatorLabel,"utf-8"));
        } catch (UnsupportedEncodingException e) {
            log.error("【handle】处理异常",e);
        }
        return mv;
    }

}
