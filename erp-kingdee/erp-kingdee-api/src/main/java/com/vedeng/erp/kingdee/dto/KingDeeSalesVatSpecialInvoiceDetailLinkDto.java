package com.vedeng.erp.kingdee.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * FSALESICENTRYLink
 *
 * <AUTHOR>
 */
@Data
public class KingDeeSalesVatSpecialInvoiceDetailLinkDto {
    /**
     * 实体主键
     */
    private Integer fLinkId;
    /**
     * 业务流程图
     */
    private String fsalesicentryLinkFflowid;
    /**
     * 推进线路ID
     */
    private Integer fsalesicentryLinkFflowlineid;
    /**
     * fsalesicentryLinkFruleid
     */
    private String fsalesicentryLinkFruleid;
    /**
     * 源单表内码
     */
    private String fsalesicentryLinkFstableid;
    /**
     * 源单表
     */
    private String fsalesicentryLinkFstablename;
    /**
     * 源单内码
     */
    private String fsalesicentryLinkFsbillid;
    /**
     * 源单分录内码
     */
    private String fsalesicentryLinkFsid;
    /**
     * 原单的数量
     */
    private BigDecimal fsalesicentryLinkFbasicunitqtyold;
    /**
     * 修改携带量（实开数量）
     */
    private BigDecimal fsalesicentryLinkFbasicunitqty;
    /**
     * 原单的金额
     */
    private BigDecimal fsalesicentryLinkFallamountforold;
    /**
     * 开票的金额（实开金额）
     */
    private BigDecimal fsalesicentryLinkFallamountfor;


    public KingDeeSalesVatSpecialInvoiceDetailLinkDto() {
        this.fsalesicentryLinkFflowid = "";
        this.fsalesicentryLinkFflowlineid = 0;
        this.fsalesicentryLinkFruleid = "IV_ReceivableToSalesIC";
        this.fsalesicentryLinkFstableid = "0";
        this.fsalesicentryLinkFstablename = "t_AR_receivableEntry";
    }

}