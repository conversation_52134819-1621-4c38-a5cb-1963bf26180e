@import (less) '../common/mixin.css';

.page-main-header-wrap {
    position: fixed;
    top: 50px;
    left: 0;
    width: 100%;
    z-index: 11;
}

.page-main-header {
    height: 60px;
    display: flex;
    align-items: center;
    background: #fff;
    padding-left: 20px;
    min-width: 1300px;

    .header-title {
        font-size: 20px;
        font-weight: 700;
        margin-right: 20px;
    }

    .order-status-tag {
        line-height: 27px;
        padding: 0 10px;
        border-radius: 3px;
        background: rgba(255, 237, 224, 0.97);
        color: #f60;
        font-size: 14px;
        margin-right: 20px;

        &.tag-green {
            background: #E3F7E3;
            color: #13BF13;
        }
    }

    .order-business-info {
        display: flex;
        align-items: center;
        flex: 1;

        .business-info-item {
            display: flex;
            align-items: center;
            position: relative;

            // &:first-child {
            //     margin-right: 21px;

            //     &::before {
            //         content: "";
            //         height: 14px;
            //         width: 1px;
            //         background: #E1E5E8;
            //         position: absolute;
            //         top: 3.5px;
            //         right: -10px;
            //     }
            // }

            .info-label {
                color: #999;
            }

            .info-txt {
                color: #09f;
                cursor: pointer;

                &:hover {
                    color: #f60;
                }
            }
        }
    }

    .header-link {
        color: #09f;
        cursor: pointer;
        margin-right: 20px;
        white-space: nowrap;

        &:hover {
            color: #f60;
        }

        .vd-ui_icon {
            font-size: 16px;
            vertical-align: -2px;
        }
    }

    .header-options {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-left: 20px;

        .options-btns {
            display: flex;
            align-items: center;
            white-space: nowrap;

            .vd-ui-button {
                margin-right: 10px;

                &:last-child {
                    margin-right: 20px;
                }

                .icon-selected2 {
                    color: #13BF13;
                }
            }
        }

        .options-icons {
            display: flex;
            align-items: center;

            .btn-icon {
                width: 64px;
                height: 60px;
                position: relative;
                cursor: pointer;

                &::before,
                &::after {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-size: 24px 24px;
                    background-position: center;
                    background-repeat: no-repeat;
                }

                &::after {
                    opacity: 0;
                }

                &:hover {
                    &::before {
                        opacity: 0;
                    }

                    &::after {
                        opacity: 1;
                    }
                }

                &.btn-message {
                    &::before {
                        background-image: url(../../image/icon/msg.svg);
                    }

                    &::after {
                        background-image: url(../../image/icon/msg-active.svg);
                    }
                }

                &.btn-record {
                    &::before {
                        background-image: url(../../image/icon/record.svg);
                    }

                    &::after {
                        background-image: url(../../image/icon/record-active.svg);
                    }
                }

                &.btn-time {
                    &::before {
                        background-image: url(../../image/icon/time.svg);
                    }

                    &::after {
                        background-image: url(../../image/icon/time-active.svg);
                    }
                }

                &.btn-user {
                    &::before {
                        background-image: url(../../image/icon/user.svg);
                    }

                    &::after {
                        background-image: url(../../image/icon/user-active.svg);
                    }
                }
            }
        }

        .h-a-item-num {
            position: absolute;
            top: 8px;
            right: 20px;
            background: #e64545;
            border-radius: 10px;
            color: #fff;
            padding: 0 6px;
            z-index: 5;
            line-height: 20px;
            transform: translateX(50%);
            min-width: 20px;
            text-align: center;
        }
    }
}

.page-top-tip {
    background: #E0F3FF;
    display: flex;
    align-items: center;
    padding: 10px 15px;

    .vd-ui_icon {
        font-size: 16px;
        line-height: 1;
    }

    .icon-info2 {
        margin-right: 10px;
        color: #09f;
    }

    .icon-m-message {
        color: #f60;
    }

    .tip-option {
        color: #09f;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }
}

.quote-wrap {
    padding-top: 122px;
    padding: 121px 20px 60px 20px;
    margin: auto;
    min-width: 1260px;

    &.no-tip {
        padding-top: 80px;
    }

    .quote-content {
        background: #fff;

        .options-wrap {
            display: flex;
            align-items: center;
            padding: 10px 20px 10px 10px;
            border: solid 1px #E1E5E8;
            border-bottom: 0;

            &.fixed {
                position: fixed;
                width: calc(100% - 40px);
                // max-width: 1880px;
                background: #fff;
                z-index: 50;
            }

            .options-l {
                flex: 1;
                display: flex;
                align-items: center;
            }

            .options-btns {
                display: flex;
                align-items: center;

                .vd-ui-button {
                    margin-right: 10px;
                }
            }

            .options-links {
                display: flex;
                align-items: center;
                margin-left: 10px;
    
                .link-item {
                    color: #09f;
                    cursor: pointer;
                    margin-right: 20px;
                    position: relative;

                    &::before {
                        content: "";
                        height: 14px;
                        width: 1px;
                        background: #E1E5E8;
                        position: absolute;
                        top: 3.5px;
                        right: -10px;
                    }
                    
                    &:hover {
                        color: #f60;
                    }

                    .icon-sms {
                        font-size: 16px;
                        vertical-align: -2px;
                        margin-right: 5px;
                        line-height: 1;
                    }

                    &:last-child {
                        &::before {
                            display: none;
                        }
                    }
                }
            }

            .selected-num {
                color: #999;
                position: relative;

                &::before {
                    content: "";
                    height: 14px;
                    width: 1px;
                    background: #E1E5E8;
                    position: absolute;
                    top: 3.5px;
                    left: -10px;
                }
            }

            .filter-wrap {
                display: flex;
                align-items: center;
                margin-right: 40px;
                position: relative;

                &::before {
                    content: "";
                    width: 1px;
                    height: 14px;
                    background: #e1e5e8;
                    position: absolute;
                    top: 4px;
                    right: -20px;
                }

                .vd-ui-custom-select-wrap {
                    margin-right: 20px;

                    &:last-child {
                        margin-right: 0;
                    }
                }

                .filter-checkbox-item {
                    margin-right: 20px;
                    
                    .vd-ui-checkbox-item {
                        color: #999;
                    }
                }

                .filter-item {
                    display: flex;
                    align-items: center;
                    color: #999;
                    cursor: pointer;

                    &:last-child {
                        margin-right: 0;
                    }

                    &:hover {
                        color: #f60;
                    }

                    .vd-ui_icon {
                        font-size: 16px;
                        line-height: 1;
                        margin-left: 5px;

                        &.active {
                            color: #f60;
                        }
                    }
                }
            }

            .price-total {
                line-height: 33px;
                
                .price-label {
                    color: #999;
                }

                .price-num {
                    color: #e64545;
                    font-weight: 700;
                    margin-right: 5px;
                }
            }
        }

        .options-wrap-placeholder {
            height: 54px;
        }

        .vd-ui-table-wrap {
            .vd-ui-table-header {
                top: 1px;
            }

            .vd-ui-table {
                .vd-ui-tr {

                    .vd-ui-th {
                        border: 1px solid#E1E5E8;
                        padding: 8px 10px;
                        font-size: 12px;

                        &:first-child {
                            background: #F5F7FA;
                            padding-left: 10px;
                            padding-right: 10px;
                        }
                    }

                    .vd-ui-td {
                        border: 1px solid#E1E5E8;
                        padding: 10px 3px 10px 10px;

                        &:first-child {
                            background: #F5F7FA;
                            padding-left: 10px;
                            padding-right: 10px;
                        }

                        &.num {
                            padding-left: 0;
                            padding-right: 0;
                            text-align: center;
                            background: #F5F7FA;
                        }

                        .td-remark-btn {
                            height: 108px;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 12px;
                            padding-right: 7px;

                            .btn-link {
                                color: #09f;
                                cursor: pointer;
                                
                                &:hover {
                                    color: #f60;
                                }
                            }
                        }

                        .option-item {
                            color: #09f;
                            cursor: pointer;

                            &:hover {
                                color: #f60;
                            }
                        }

                        &.td-need-info {
                            position: relative;

                            .option-item-delete {
                                position: absolute;
                                bottom: 0;
                                right: 0; 
                                font-size: 16px;
                                line-height: 1;
                                width: 24px;
                                height: 24px;
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                color: #999;
                                background: transparent;
                                cursor: pointer;

                                &:hover {
                                    background: #EBEFF2;
                                    color: #f60;
                                }
                            }
                        }

                        .td-remark-info {
                            font-size: 12px;
                            padding-right: 7px;
                            position: relative;
                            height: 108px;

                            .remark-title {
                                display: flex;
                                justify-content: space-between;
                                margin-bottom: 5px;

                                .title-name {
                                    flex: 1;
                                }
                            }

                            .remark-cnt {
                                color: #999;
                            }

                            .remark-options {
                                display: flex;
                                justify-content: space-between;
                                position: absolute;
                                bottom: 3px;
                                left: 0;
                                padding-right: 7px;
                                width: 100%;

                                .remark-option-item {
                                    color: #09f;
                                    cursor: pointer;

                                    &:hover {
                                        color: #f60;
                                    }
                                }
                            }
                        }

                        &.prod-cnt {
                            position: relative;

                            .prod-status-tag {
                                position: absolute;
                                top: 0;
                                right: 0;
                                background: rgba(255, 102, 0, 0.1);
                                color: #f60;
                                padding: 2px 4px;
                            }
                        }

                        .quote-td-cnt {
                            padding-right: 7px;
                            height: 108px;
                            .scrollbar;
                            overflow: auto;
                            font-size: 12px;
                            word-break: break-all;
                            word-wrap: break-word;
                            line-break: anywhere;  //控制中文标点任何地方换行

                            .label-item {
                                display: flex;
                                margin-bottom: 4px;

                                &.label-sku {
                                    .link, .txt {
                                        flex: none;
                                        margin-right: 5px;
                                    }
                                }

                                &:last-child {
                                    margin-bottom: 0;
                                }

                                .label {
                                    color: #999;

                                    &.highlight {
                                        color: #f60;
                                    }

                                    &.label-tip {
                                        display: flex;
                                        align-items: center;

                                        .txt {
                                            margin-right: 5px;
                                        }
                                    }
                                }

                                .txt {
                                    flex: 1;

                                    &.red {
                                        color: #e64545;
                                    }

                                    &.green {
                                        color: #13BF13;
                                    }

                                    &.orange {
                                        color: #FF6600;
                                    }

                                    &.blue {
                                        color: #09f;
                                    }

                                    &.report-txt {
                                        display: flex;
                                        align-items: center;

                                        .txt-option {
                                            cursor: pointer;
                                        }

                                        &:hover {
                                            .txt-option {
                                                color: #f60;
                                            }
                                        }

                                        .vd-ui_icon {
                                            line-height: 1;
                                            font-size: 16px;
                                            vertical-align: text-bottom;

                                            &.icon-edit {
                                                margin-left: 5px;

                                                &.disabled {
                                                    color: #999;
                                                    cursor: not-allowed;

                                                    &:hover {
                                                        color: #999;
                                                    }
                                                }
                                            }
                                        }

                                        .txt-tip {
                                            margin-left: 10px;
                                        }
                                    }
                                }

                                .link {
                                    flex: 1;
                                    color: #09f;
                                    cursor: pointer;
                                    font-size: 12px;

                                    &:hover {
                                        color: #f60;
                                    }
                                }

                                .user-list {
                                    flex: 1;
                                }

                                .user-item {
                                    display: flex;
                                    align-items: center;
                                    margin-bottom: 5px;

                                    &:last-child {
                                        margin-bottom: 0
                                    }

                                    .user-avatar {
                                        width: 18px;
                                        height: 18px;
                                        border-radius: 2px;
                                        overflow: hidden;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin-right: 5px;

                                        img {
                                            max-width: 100%;
                                            max-height: 100%;
                                        }
                                    }

                                    .user-name {
                                        flex: 1;
                                    }
                                }
                            }

                            .text-item {
                                display: flex;

                                .txt {
                                    flex: 1;
                                }
                            }

                            .edit-item {
                                display: flex;
                                align-items: center;
                                margin-bottom: 5px;

                                &.can-edit {
                                    margin-bottom: 10px;
                                }

                                &:last-child {
                                    margin-bottom: 0;
                                }

                                .label {
                                    color: #999;
                                    width: 65px;
                                    white-space: nowrap;
                                    text-align: right;

                                    &.highlight {
                                        color: #f60;
                                    }
                                }

                                .content {
                                    flex: 1;
                                    display: flex;
                                    align-items: center;

                                    .unit {
                                        margin: 0 5px;
                                        flex: 1;
                                    }

                                    .vd-ui-number-input {
                                        width: 92px;
                                    }
                                }

                                .icon-caution2 {
                                    font-size: 16px;
                                    color: #f60;
                                }

                                // &.edit-item-price {
                                //     position: relative;
                                // }
                            }

                            &.center {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                            }

                            &.option {
                                padding-right: 5px;

                                .option-item {
                                    margin-right: 0;

                                    &::before {
                                        display: none;
                                    }
                                }
                            }

                            &.visible {
                                overflow: visible;
                            }

                            .goods-info-wrap {
                                display: flex;

                                .goods-img {
                                    width: 60px;
                                    height: 60px;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    margin-right: 10px;
                                    position: relative;

                                    img {
                                        max-width: 100%;
                                        max-height: 100%;
                                    }

                                    .goods-tags {
                                        display: flex;
                                        align-items: center;
                                        position: absolute;
                                        top: 0;
                                        left: 0;

                                        .tag-item {
                                            width: 16px;
                                            height: 16px;
                                            background-size: 100% 100%;
                                            margin-right: 3px;
                                            
                                            &.tag-zeng {
                                                background-image: url(../../image/common/prod-tag-zeng.svg);
                                            }

                                            &:last-child {
                                                margin-right: 0;
                                            }
                                        }
                                    }
                                }
                                

                                .goods-info {
                                    flex: 1;
                                }
                            }

                            .vd-ui-select-link {
                                margin-bottom: 5px;
                            }

                            .option-item {
                                color: #09f;
                                cursor: pointer;
                                margin-bottom: 5px;

                                &:last-child {
                                    margin-bottom: 0;
                                }

                                &:hover {
                                    color: #f60;
                                }
                            }

                            .input-strong {
                                .vd-ui-input__inner {
                                    font-weight: 700;
                                    color: #e64545;
                                }
                            }

                            .value-change {
                                .vd-ui-input__inner {
                                    background: #FBEDB3;
                                }
                            }

                            .cnt-input-wrap {
                                position: relative;

                                &.focus {
                                    .vd-ui-input__inner {
                                        border-color: #12B2B2;
                                    }
                                }

                                .focus-tip {
                                    position: absolute;
                                    bottom: 26px;
                                    min-width: 100%;
                                    white-space: nowrap;
                                    background: #12B2B2;
                                    color: #fff;
                                    left: 0;
                                    font-size: 12px;
                                    line-height: 16px;
                                    padding: 0 2px;
                                    z-index: 3;
                                    border-radius: 2px;
                                }
                            }

                            .vd-ui-custom-placeholder.open {
                                .user-select-placeholder .icon-down {
                                    transform: rotate(180deg);
                                }
                            }

                            .user-select-placeholder {
                                display: flex;
                                color: #09f;
                                align-items: center;
                                cursor: pointer;

                                &:hover {
                                    color: #f60;
                                }

                                .icon-down {
                                    font-size: 16px;
                                    line-height: 1;
                                    margin-left: 5px;
                                    transition: transform .22s ease;
                                }

                                .user-info {
                                    display: flex;
                                    align-items: center;

                                    .user-avatar {
                                        width: 18px;
                                        height: 18px;
                                        border-radius: 2px;
                                        overflow: hidden;
                                        display: flex;
                                        align-items: center;
                                        justify-content: center;
                                        margin-right: 5px;

                                        img {
                                            max-width: 100%;
                                            max-height: 100%;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    &:hover {
                        .vd-ui-td {
                            background: #F5F7FA;

                            // &:first-child,
                            // &.num {
                            //     background: #F5F7FA;
                            // }

                            // &.color1 {
                            //     background: #F5F7FA;
                            // }
                        }
                    }

                    &.on-select {
                        .vd-ui-td {
                            background: #F0F9FF;

                            // &:first-child,
                            // &.num {
                            //     background: #D9F0FF;
                            // }

                            // &.color1 {
                            //     background: #E5F5FF;
                            // }

                            &:last-child {
                                z-index: 4;
                            }
                        }
                    }

                    &:first-child {
                        .quote-td-cnt .cnt-input-wrap .focus-tip {
                            bottom: auto;
                            top: 26px;
                        }
                    }
                }
            }
        }

    }
}

.multi-add-wrap {
    display: flex;

    .multi-add-l {
        width: 180px;
        margin-right: 20px;
    }

    .multi-add-r {
        flex: 1;

        .multi-add-tips {
            color: #999;
            margin-bottom: 15px;
            line-height: 24px;
        }

        .multi-error-wrap {
            display: flex;
            color: #e64545;
            word-break: break-all;

            .icon-error2 {
                font-size: 16px;
                line-height: 1;
                margin-right: 5px;
                margin-top: 3px;
            }
        }
    }
}

.tmpl-download-wrap {
    display: flex;
    align-items: center;
    margin-top: 20px;
    align-items: center;

    .tmpl-icon {
        width: 13px;
        height: 16px;
        margin-right: 5px;

        img {
            vertical-align: -1px;
            width: 100%;
            height: 100%;
        }
    }

    .tmpl-link {
        color: #09f;
        margin-left: 10px;
        cursor: pointer;

        &:hover {
            color: #f60;
        }
    }
}

.import-dialog {
    .vd-ui-dialog--in .vd-ui-dialog_content {
        overflow: visible;
    }
}

.dlg-consultation-users {
    overflow: hidden;

    .user-list {
        display: flex;
        flex-wrap: wrap;
        margin-bottom: -5px;
        margin-right: -5px;

        .user-item {
            display: flex;
            margin-right: 5px;
            align-items: center;
            padding: 0 10px 0 5px;
            height: 33px;
            background: #F5F7FA;
            border-radius: 3px;
            margin-bottom: 5px;

            .item-avatar {
                width: 18px;
                height: 18px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 2px;
                overflow: hidden;
                margin-right: 5px;

                img {
                    max-width: 100%;
                    max-height: 100%;
                }
            }
        }
    }
}

.ui-popup-message-box .msg-button-choice .vd-button.vd-button-link {
    background: none;
    color: #09f;
    padding: 0;
    border: 0;
    height: auto;
    line-height: 1.5;
    margin-right: 10px;
    display: flex;
    align-items: center;

    &:hover {
        color: #f60;
    }
}

.share-dlg-wrap {
    .share-info {
        .info-item {
            display: flex;
            margin-bottom: 15px;

            .info-label {
                color: #999;
                width: 140px;
                text-align: right;
                margin-right: 10px;
            }

            .price {
                color: #e64545;
                font-weight: 700;
            }

            &:last-child {
                margin-bottom: 0;
            }
        }
    }

    .share-msg {
        padding: 10px;
        background: #F5F7FA;
        border-radius: 3px;
        margin-top: 20px;
        margin-bottom: 20px;

        .msg-link {
            color: #09f;

            &:hover {
                color: #f60;
            }
        }
    }

    .share-footer {
        display: flex;
        align-items: center;

        .footer-tip {
            color: #f60;
            flex: 1;

            .icon-info2 {
                font-size: 16px;
                line-height: 1;
                margin-right: 5px;
                vertical-align: -2px;
            }
        }

        .footer-options {
            display: flex;
            align-items: center;
        
            .footer-link {
                color: #09f;
                margin-right: 20px;

                &:hover {
                    color: #f60;
                }
            }
        }
    }
}

.biz-dlg-info-wrap {
    .dlg-info-block {
        padding-bottom: 20px;
        border-bottom: solid 1px #E1E5E8;
        margin-bottom: 20px;

        &:last-child {
            border-bottom: 0;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .dlg-info-title {
            font-weight: 700;
            margin-bottom: 20px;
        }

        .dlg-info-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;

            &:last-child {
                margin-bottom: 10px;
            }

            .info-label {
                font-size: 14px;
                color: #999;
                width: 140px;
                margin-right: 10px;
                text-align: right;
            }

            .info-txt {
                flex: 1;
            }
        }
    }
}

.req-des-wrap {
    .vd-ui-textarea-place {
        vertical-align: top;
    }

    .dlg-form-footer {
        margin-top: 19px;

        .vd-ui-button {
            margin-right: 10px;
        }
    }
}

.custom-add-dlg-wrap {
    .vd-ui-textarea-place {
        vertical-align: top;
    }

    .custom-add-form {
        max-height: 471px;
        overflow-y: auto;
        overscroll-behavior: contain;
        .scrollbar;
    }


    .dlg-form-footer {
        padding-left: 210px;
        margin-top: 20px;

        .vd-ui-button {
            margin-right: 10px;
        }
    }
}

.remark-dialog-info-wrap {
    .remark-info-options {
        margin-bottom: 20px;
    }

    .remark-info-list {
        max-height: 512px;
        overflow-y: auto;
        padding-right: 11px;
        padding-left: 21px;
        .scrollbar;

        .remark-info-item {
            padding-bottom: 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid #e1e5e8;
            position: relative;

            &:before {
                content: "";
                width: 11px;
                height: 11px;
                border-radius: 50%;
                background: #09f;
                position: absolute;
                top: 5px;
                left: -21px;
            }

            &:after {
                content: "";
                width: 1px;
                height: calc(100% + 9px);
                border-left: 1px dashed #e1e5e8;
                position: absolute;
                top: 16px;
                left: -16px;
            }

            &:last-child {
                margin-bottom: 0;
                padding-bottom: 0;
                border-bottom: 0;

                &::after {
                    display: none;
                }
            }

            .remark-item-t {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;

                .remark-user {
                    display: flex;
                    align-items: center;

                    .user-avatar {
                        width: 20px;
                        height: 20px;
                        border-radius: 3px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-right: 5px;

                        img {
                            max-width: 100%;
                            max-height: 100%;
                        }
                    }
                }

                .remark-time {
                    color: #999;
                }
            }
        }
    }
}

.form-last-radio {
    .form-item:last-child {
        .vd-ui-radio-group {
            margin-bottom: 0;

            .vd-ui-radio-item {
                margin-bottom: 0;
            }
        }
    }
}

.price-history-wrap {
    // position: fixed;
    // left: calc(100% - 10px);
    // top: -4px;
    background: #fff;
    border-radius: 3px;
    box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
    padding: 10px;
    width: 350px;
    display: none;
    z-index: 8888;

    &.show {
        display: block;
    }

    &::before {
        content: "";
        width: 0;
        height: 0;
        border: 5px solid transparent;
        border-right-color: #fff;
        left: -10px;
        top: 10px;
        position: absolute;
    }

    .price-history-table {
        .price-history-tbody {
            max-height: 320px;
            overflow: auto;
            .scrollbar;

            &.scroll {
                margin-right: -6px;
            } 
        }

        .price-history-tr {
            display: flex;

            .price-history-th, .price-history-td {
                margin-right: -1px;

                &:nth-child(1) {
                    width: 130px;
                }

                &:nth-child(2) {
                    width: 100px;
                }

                &:nth-child(3) {
                    flex: 1;
                    margin-right: 0;
                }
            }

            .price-history-th {
                background: #F5F7FA;
                padding: 5px 10px;
                border: 1px solid #E1E5E8;
                color: #999;
            }

            .price-history-td {
                padding: 5px 10px;
                border: 1px solid transparent;
                border-bottom: 1px solid #e1e5e8;
                
                &:nth-child(1) {
                    border-left: 1px solid #e1e5e8;
                }
               
                &:nth-child(3) {
                    border-right: 1px solid #e1e5e8;
                }
            }

            .option-user-info {
                display: flex;
                align-items: center;

                .user-avatar {
                    width: 20px;
                    height: 20px;
                    border-radius: 3px;
                    overflow: hidden;
                    margin-right: 5px;

                    img {
                        width: 100%;
                        height: 100%;
                        object-fit: cover;
                        vertical-align: top;
                    }
                }
                
                .user-name {
                    flex: 1;
                }
            }

            .txt-right {
                text-align: right;
            }

            .price-txt {
                color: #E64545;
            }

            .txt-grey {
                color: #999;
            }
        }
    }

    .price-history-loading {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 10px 0;
        border: solid 1px #E1E5E8;
        margin-top: -1px;
        color: #666;

        @keyframes loading {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .icon-loading {
            font-size: 16px;
            color: #09f;
            margin-right: 5px;
            line-height: 1;
            animation: loading 2s linear infinite;
        }
    }

    .price-history-empty {
        text-align: center;
        padding: 10px;
        border: solid 1px #E1E5E8;
        margin-top: -1px;
        color: #999;
    }
}

.option-refresh-tip-wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 99999;

    .option-refresh-tip-cnt {
        background: #fff;
        border-radius: 5px;
        box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
        padding: 60px 40px 40px 40px;
        text-align: center;

        .option-tip-icon {
            width: 48px;
            height: 48px;
            background-image: url(../../image/option-tip-icon.svg);
            background-size: 100% 100%;
            display: inline-block;
            vertical-align: top;
            margin-bottom: 30px;
        }

        .option-tip-txt {
            margin-bottom: 20px;
        }

        .vd-ui-button {
            border-radius: 18px;
            padding: 6px 50px;
        }
    }
}

.group-user-list .group-user-item-wrap {
    display: flex;
    align-items: center;

    .user-avatar {
        width: 20px;
        height: 20px;
        border-radius: 3px;
        overflow: hidden;
        margin-right: 5px;
        
        img {
            width: 20px;
            height: 20px;
            object-fit: cover;
            vertical-align: top;
        }
    }
}

.report-radio-wrap {
    .vd-ui-radio-group {
        display: flex;
        align-items: center;

        .vd-ui-radio-item {
            margin-right: 9px;

            &:last-child {
                margin-right: 0;
            }

            .vd-ui-radio-icon {
                margin-top: 3px;
            }
        }
    }
}

.report-reason-wrap .vd-ui-textarea .vd-ui-textarea__inner {
    font-size: 12px;

    &::placeholder {
        font-size: 12px;
    }
} 

.goods-tip-info {
    min-width: 200px;
    
    .goods-tip-item {
        display: flex;
        margin-bottom: 5px;

        .item-label {
            color: #999;
        }

        .item-txt {
            flex: 1;
        }

        &:last-child {
            margin-bottom: 0;
        }
    }
}

.goods-tip-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100px;

    @keyframes loading {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    .icon-loading {
        font-size: 16px;
        color: #09f;
        margin-right: 5px;
        line-height: 1;
        animation: loading 2s linear infinite;
    }
}

.order-type {
    .red {
        color: #e64545;
    }
}

.create-chat-tip {
    line-height: 30px;
    margin-right: 10px;
    color: #e64545;
}

.create-user-status {
    margin-right: 10px;
    display: flex;
    align-items: center;
}