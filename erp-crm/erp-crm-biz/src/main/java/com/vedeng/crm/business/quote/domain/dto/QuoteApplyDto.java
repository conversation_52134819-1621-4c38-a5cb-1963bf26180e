package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class QuoteApplyDto {


    /**
     * 报价单
     */
    private Integer quoteorderId;

    /**
     * 授权书申请状态根据此展示按钮
     * 1申请授权书 2授权书申请中 3打印授权书
     */
    private Integer flag;//

    /**
     * 对应的flag的name值
     * 1申请授权书 2授权书申请中 3打印授权书
     */
    private String applyName;

    /**
     * 有效商品数量
     */
    private Integer sqnum;

    private String quoteApplyLink;

    private String quoteApplyInnerLink;

    /**
     * 报价单商品明细
     */
    private List<CrmQuoteOrderCoreSkuDto> quoteGoodsList;






}
