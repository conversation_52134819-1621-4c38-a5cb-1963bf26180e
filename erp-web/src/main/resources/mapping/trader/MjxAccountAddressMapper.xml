<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.MjxAccountAddressMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.MjxAccountAddress" >
    <!--          -->
    <id column="ADDRESS_ID" property="addressId" jdbcType="INTEGER" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="DELIVERY_NAME" property="deliveryName" jdbcType="VARCHAR" />
    <result column="TELPHONE" property="telphone" jdbcType="VARCHAR" />
    <result column="AREA" property="area" jdbcType="VARCHAR" />
    <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR" />
    <result column="IS_DELIVERY_DEFAULT" property="isDeliveryDefault" jdbcType="BIT" />
    <result column="IS_INVOICE_DEFAULT" property="isInvoiceDefault" jdbcType="BIT" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="TITLE_NAME" property="titleName" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="VARCHAR" />
    <result column="MJX_CONTACT_ADDERSS_ID" property="mjxContactAdderssId" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    ADDRESS_ID, TRADER_ID, DELIVERY_NAME, TELPHONE, AREA, AREA_IDS, IS_DELIVERY_DEFAULT, 
    IS_INVOICE_DEFAULT, ADDRESS, TITLE_NAME, ADD_TIME, MOD_TIME, CREATOR , MJX_CONTACT_ADDERSS_ID,IS_ENABLE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_MJX_ACCOUNT_ADDRESS
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_MJX_ACCOUNT_ADDRESS
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.trader.model.MjxAccountAddress" >
    <!--          -->
    insert into T_MJX_ACCOUNT_ADDRESS (ADDRESS_ID, TRADER_ID, DELIVERY_NAME, 
      TELPHONE, AREA, AREA_IDS, 
      IS_DELIVERY_DEFAULT, IS_INVOICE_DEFAULT, ADDRESS, 
      TITLE_NAME, ADD_TIME, MOD_TIME, 
      CREATOR,MJX_CONTACT_ADDERSS_ID,IS_ENABLE)
    values (#{addressId,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{deliveryName,jdbcType=VARCHAR}, 
      #{telphone,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{areaIds,jdbcType=VARCHAR}, 
      #{isDeliveryDefault,jdbcType=BIT}, #{isInvoiceDefault,jdbcType=BIT}, #{address,jdbcType=VARCHAR}, 
      #{titleName,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{modTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=VARCHAR},#{mjxContactAdderssId,jdbcType=INTEGER},#{isEnable,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.trader.model.MjxAccountAddress" >
    <!--          -->
    insert into T_MJX_ACCOUNT_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="deliveryName != null" >
        DELIVERY_NAME,
      </if>
      <if test="telphone != null" >
        TELPHONE,
      </if>
      <if test="area != null" >
        AREA,
      </if>
      <if test="areaIds != null" >
        AREA_IDS,
      </if>
      <if test="isDeliveryDefault != null" >
        IS_DELIVERY_DEFAULT,
      </if>
      <if test="isInvoiceDefault != null" >
        IS_INVOICE_DEFAULT,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="titleName != null" >
        TITLE_NAME,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="mjxContactAdderssId !=null">
      	MJX_CONTACT_ADDERSS_ID,
      </if>
      <if test="isEnable != null">
      	IS_ENABLE
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="addressId != null" >
        #{addressId,jdbcType=INTEGER},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="deliveryName != null" >
        #{deliveryName,jdbcType=VARCHAR},
      </if>
      <if test="telphone != null" >
        #{telphone,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaIds != null" >
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="isDeliveryDefault != null" >
        #{isDeliveryDefault,jdbcType=BIT},
      </if>
      <if test="isInvoiceDefault != null" >
        #{isInvoiceDefault,jdbcType=BIT},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="titleName != null" >
        #{titleName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mjxContactAdderssId !=null">
      	#{mjxContactAdderssId,jdbcType=INTEGER},
      </if>
      <if test="isEnable!=null">
      	#{isEnable,jdbcType=BIT}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.MjxAccountAddress" >
    <!--          -->
    update T_MJX_ACCOUNT_ADDRESS
    <set >
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="deliveryName != null" >
        DELIVERY_NAME = #{deliveryName,jdbcType=VARCHAR},
      </if>
      <if test="telphone != null" >
        TELPHONE = #{telphone,jdbcType=VARCHAR},
      </if>
      <if test="area != null" >
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="areaIds != null" >
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="isDeliveryDefault != null" >
        IS_DELIVERY_DEFAULT = #{isDeliveryDefault,jdbcType=BIT},
      </if>
      <if test="isInvoiceDefault != null" >
        IS_INVOICE_DEFAULT = #{isInvoiceDefault,jdbcType=BIT},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="titleName != null" >
        TITLE_NAME = #{titleName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="mjxContactAdderssId !=null">
      	MJX_CONTACT_ADDERSS_ID =#{mjxContactAdderssId,jdbcType=INTEGER},
      </if>
      <if test="isEnable !=null">
      	IS_ENABLE =#{isEnable,jdbcType=BIT}
      </if>
    </set>
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.MjxAccountAddress" >
    <!--          -->
    update T_MJX_ACCOUNT_ADDRESS
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      DELIVERY_NAME = #{deliveryName,jdbcType=VARCHAR},
      TELPHONE = #{telphone,jdbcType=VARCHAR},
      AREA = #{area,jdbcType=VARCHAR},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      IS_DELIVERY_DEFAULT = #{isDeliveryDefault,jdbcType=BIT},
      IS_INVOICE_DEFAULT = #{isInvoiceDefault,jdbcType=BIT},
      ADDRESS = #{address,jdbcType=VARCHAR},
      TITLE_NAME = #{titleName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=VARCHAR},
      MJX_CONTACT_ADDERSS_ID =#{mjxContactAdderssId,jdbcType=INTEGER},
      IS_ENABLE =#{isEnable,jdbcType=BIT}
    where ADDRESS_ID = #{addressId,jdbcType=INTEGER}
  </update>
  <select id="getAddressInfoByParam" parameterType="com.vedeng.trader.model.MjxAccountAddress" resultMap="BaseResultMap">
   select 
    <include refid="Base_Column_List" />
    from T_MJX_ACCOUNT_ADDRESS
    where MJX_CONTACT_ADDERSS_ID =#{mjxContactAdderssId,jdbcType=INTEGER}
  </select>
  <select id="getMjxAccountAddressList" parameterType="com.vedeng.trader.model.MjxAccountAddress" resultMap="BaseResultMap">
  	select 
    <include refid="Base_Column_List" />
    from T_MJX_ACCOUNT_ADDRESS
    where IS_ENABLE =#{isEnable,jdbcType=BIT} and TRADER_ID = #{traderId,jdbcType=INTEGER} order BY `ADD_TIME` DESC
  </select>
</mapper>