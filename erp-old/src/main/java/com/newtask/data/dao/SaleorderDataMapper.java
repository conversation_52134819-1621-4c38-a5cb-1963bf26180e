package com.newtask.data.dao;

import com.newtask.data.dto.AfterSalesStatusDto;
import com.newtask.data.dto.SaleoderDataDto;
import com.newtask.data.dto.WarehouseOperateDataDto;
import com.vedeng.finance.model.SaleorderData;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 *
 */
@Repository
public interface SaleorderDataMapper {

    List<Integer> getSaleorderFirstPayTimeIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);


    List<Map<String, Object>> findFirstPayTimeBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    Long getSaleorderById(Integer id);

    void insertSaleOrder(@Param("id") Integer id, @Param("nowTime") Long nowTime);

    void updateSaleorderFirstPayTime(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") Long nowTime);

    List<Map<String, Object>> findLastInvoiceTimeBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderLastInvoiceTime(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") Long nowTime);

    List<Integer> getSaleorderLastInvoiceTimeIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Integer> getSaleorderFirstExpressTimeIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);


    List<Map<String, Object>> findFirstExpressTimeBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderFirstExpressTime(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") Long nowTime);

    List<Integer> getSaleorderContractStatusIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findContractStatusBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderContractStatus(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") Long nowTime);


    void updateSaleorderLeftAmountPeriod(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") Long nowTime);



    void updateSaleorderAutoCheck(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") Long nowTime);



    void updateCommunicateNumCheck(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos,@Param("nowTime") long nowTime);

    List<Integer> getSaleorderAfterSalesStatusIdsList(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<AfterSalesStatusDto> findAfterSalesStatusBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateAfterSalesStatusCheck(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos,@Param("nowTime") long nowTime);

    List<Integer> getSaleorderInvoiceApplyFlayIdsList(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findInvoiceApplyFlayBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderInvoiceApplyFlay(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos,@Param("nowTime") long nowTime);

    List<Integer> getSaleorderVerifyStatusIdsList(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findVerifyStatusBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderVerifyStatus(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos,@Param("nowTime") long nowTime);

    List<Integer> getSaleorderContractVerifyStatusIdsList(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Map<String, Object>> findContractVerifyStatusBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderContractVerifyStatus(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos,@Param("nowTime") long nowTime);

    void updateSaleorderSubStatus(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") long nowTime);

    List<Integer> getSaleorderSubStatusIdsList(@Param("startTime") Long startTime,@Param("endTime") Long endTime);

    List<Saleorder> findSubStatusBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    List<Map<String, Object>> getSaleorderConcatNameInfoByTime(@Param("saleorderIds")List<Integer> saleorderIds);

    void updateSaleorderNameConcatInfo(@Param("saleoderDataDtos")List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime")long nowTime);

    List<Integer> getSaleorderCurrentOrgAndUserIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findCurrentOrgAndUserIdsBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderCurrentOrgAndUserId(@Param("saleoderDataDtos") List<SaleoderDataDto> saleoderDataDtos, @Param("nowTime") long nowTime);
    List<Integer> getSaleorderCouponTypeIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findCouponTypeBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderCouponType(@Param("saleorderId") Integer saleorderId, @Param("couponType") Long couponType, @Param("nowTime") long nowTime);

    List<Map<String, Object>> getSaleorderRealTotalAmountIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    void updateRealTotalAmount(@Param("saleorderId") Integer saleorderId, @Param("realTotalAmount") String realTotalAmount, @Param("nowTime") long nowTime);

    List<Integer> getSaleorderRealPayAmountIdsList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Map<String, Object>> findRealPayAmountBySaleorderId(@Param("bizIds") List<Integer> bizIds);

    void updateSaleorderRealPayAmount(@Param("saleorderId") Integer saleorderId, @Param("realPayAmount") String realPayAmount, @Param("nowTime") long nowTime);

    void updateSaleorderGoodsBuyStatus(@Param("saleorderGoodsVos")List<SaleorderGoods> saleorderGoods, @Param("nowTime")long nowTime);

    SaleorderGoodsVo getSaleorderGoodsInfoById(@Param("saleorderGoodsId")Integer saleorderGoodsId);

    List<Integer> getBuyStatusChangedGoodsFirstQuery(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    List<Integer> getFailLeftAccountPeriodSaleorderList(@Param("start") Integer start);

    /**
     * <AUTHOR>
     * @desc 根据
     * @param startTime
     * @param endTime
     * @return
     */
    List<Integer> getSaleorderIdBySaleorderGoodsModTime(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * @Description 根据订单主键id查询订单数据
     * @Param saleorderId
     * @Return {@link SaleorderData}
     */
    SaleorderData getSaleOrderDataBySaleOrderId(@Param("saleorderId") Integer saleorderId);

    /**
     * 根据商品Id修改催办状态
     * @param saleorderId 订单Id
     */
    void updateUrageById(@Param("saleorderId") Integer saleorderId);

    /**
     * 根据订单id查询回传合同审核状态
     * @param saleOrderId
     * @return
     */
    Integer contractVerifyStatusBySaleOrderId(@Param("saleOrderId") Integer saleOrderId);
}
