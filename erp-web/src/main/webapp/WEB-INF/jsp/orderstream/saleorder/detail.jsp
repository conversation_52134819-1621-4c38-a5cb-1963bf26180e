<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="销售详情" scope="application" />

<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/order/style.css">
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/view.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_invoice_common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/saleorder_common.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/orderstream/saleorder/detail.js?rnd=${resourceVersionKey}'></script>
<style>
    /* 自动关闭倒计时样式 */
    .auto-close-countdown {
        background: rgba(255, 102, 0, 0.1);
        border-left: 2px solid #ff6600;
        padding: 4px 6px;
        font-size: 10px;
        color: #ff6600;
        border-radius: 3px;
        display: inline-block;
        max-width: 100%;
    }

    .auto-close-countdown span {
        font-weight: bold;
    }

    .auto-close-countdown .countdown-description {
        color: #999;
        font-size: 9px;
        display: block;
        margin-top: 2px;
    }

    /* 倒计时替换未收款文字的样式 */
    .auto-close-countdown.replace-tip {
        margin: 0;
        display: block;
        text-align: left;
        background: transparent;
        border: none;
        padding: 0;
        font-size: 12px;
        color: #ff6600;
        line-height: 1.2;
    }

    .auto-close-countdown.replace-tip .countdown-description {
        color: #999;
        font-size: 10px;
        margin-top: 2px;
    }
</style>
<script>
    $(function() {
        var saleorderId = $("input[name='saleorderId']").val();

        var url = page_url + '/orderstream/saleorder/detail.do?saleOrderId=' + saleorderId;
        if ($(window.frameElement).attr('src').indexOf("saleorder/detail") < 0) {
            $(window.frameElement).attr('data-url', url);
        }
    })
</script>
<div class="content mt10 ">
    <div class="parts" style="margin-bottom: 40px;">
        <!-- 这一块内容是页头进度条 -->
        <div class="t-line-wrap J-line-wrap"
             data-json=''></div>
        <!-- end -->
    </div>

    <%--功能按钮--%>
    <tags:saleorder_detail_buttons saleorder="${saleorder}" orderCheckStatus="${orderCheckStatus}" riskFlag="${riskFlag}"
                                   taskInfo="${taskInfo}" isNotDelPriceZero="${isNotDelPriceZero}" saleInvoiceApplyList="${saleInvoiceApplyList}"
                                   customer="${customer}" currentUser="${currentUser}" candidateUserMap="${candidateUserMap}"
                                   verifyUserList="${verifyUserList}" pendingOrderFlag="${pendingOrderFlag}" shInfoJudge="${shInfoJudge}"
                                   isShowInvoiceBtn="${isShowInvoiceBtn}" contractUrl="${contractUrl}" taskInfoContractReturn = "${taskInfoContractReturn}"
                                   candidateUserMapContractReturn = "${candidateUserMapContractReturn}" verifyUserListContractReturn = "${verifyUserListContractReturn}" statusAllCheck = "${statusAllCheck}" beforeConfOnline = "${beforeConfOnline}" showButtonApplyInvoice="${showButtonApplyInvoice}"/>

    <%--基本信息--%>
    <div class="parts" liname="基本信息" id="基本信息">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">基本信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">订单号</td>
                <td>${saleorder.saleorderNo}</td>
                <td class="table-smaller">订单状态</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.status eq 0}">待确认</c:when>
                        <c:when test="${saleorder.status eq 1}">进行中</c:when>
                        <c:when test="${saleorder.status eq 2}">已完结</c:when>
                        <c:when test="${saleorder.status eq 3}">已关闭</c:when>
                        <c:when test="${saleorder.status eq 4}">待用户确认</c:when>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>创建者</td>
                <td>${saleorder.creatorName}</td>
                <td>创建时间</td>
                <td><date:date value ="${saleorder.addTime}"/></td>
            </tr>
            <c:choose>
                <c:when test="${saleorder.orderType eq 5}">
                    <%--耗材订单不展示--%>
                </c:when>
                <c:otherwise>
                    <tr>
                        <td>生效状态</td>
                        <td>
                            <c:choose>
                                <c:when test="${saleorder.validStatus eq 0}">未生效</c:when>
                                <c:when test="${saleorder.validStatus eq 1}">已生效</c:when>
                                <c:otherwise></c:otherwise>
                            </c:choose>
                        </td>
                        <td>生效时间</td>
                        <td>
                            <c:if test="${saleorder.validTime > 0}">
                                <date:date value ="${saleorder.validTime}"/>
                            </c:if>
                        </td>
                    </tr>
                </c:otherwise>
            </c:choose>
            <tr>
                <td>销售部门</td>
                <td>${saleorder.salesDeptName}</td>
                <td>归属销售</td>
                <td>${saleorder.optUserName}</td>
            </tr>
            <c:choose>
                <c:when test="${saleorder.orderType eq 9}">
                    <tr>
                        <td>商机编号</td>
                        <td>
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewsaleorder${saleorder.bussinessChanceId}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${saleorder.bussinessChanceId}","title":"商机信息"}'>${saleorder.bussinessChanceNo}</a>
                        </td>

                        <td></td>
                        <td>

                        </td>
                    </tr>
                </c:when>
                <c:when test="${saleorder.orderType eq 5 || saleorder.orderType eq 7}">

<%--                    <c:if test="${quoteLinkBdLog}">--%>
                        <tr>
                            <td>商机编号</td>
                            <td>
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder${saleorder.bussinessChanceId}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${saleorder.bussinessChanceId}","title":"商机信息"}'>${saleorder.bussinessChanceNo}</a>
                            </td>

                            <td></td>
                            <td>

                            </td>
                        </tr>
<%--                    </c:if>--%>

                </c:when>
                <c:when test="${saleorder.orderType eq 8}">
                    <tr>
                        <td>商机编号</td>
                        <td>
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewsaleorder${saleorder.bussinessChanceId}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${saleorder.bussinessChanceId}","title":"商机信息"}'>${saleorder.bussinessChanceNo}</a>
                        </td>

                        <td></td>
                        <td>

                        </td>
                    </tr>
                </c:when>
                <c:otherwise>
                    <tr>

                            <td><c:if test="${saleorder.orderType ne 1 || (saleorder.orderType eq 1 && quoteLinkBdLog)}">商机编号</c:if></td>
                            <td>
                                <c:if test="${saleorder.orderType ne 1 || (saleorder.orderType eq 1 && quoteLinkBdLog)}"><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder${saleorder.bussinessChanceId}","link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${saleorder.bussinessChanceId}","title":"商机信息"}'>${saleorder.bussinessChanceNo}</a>
                                </c:if>
                            </td>

                        <td></td>
                        <td>

                        </td>
                    </tr>
<%--                    <tr>--%>
<%--                        <td>计入业绩</td>--%>
<%--                        <td id="perf_flag">--%>
<%--                            <c:choose>--%>
<%--                                <c:when test="${saleorder.isSalesPerformance eq 1}">已计入</c:when>--%>
<%--                                <c:otherwise>未计入</c:otherwise>--%>
<%--                            </c:choose>--%>
<%--                        </td>--%>
<%--                        <td>计入业绩时间</td>--%>
<%--                        <td id="perf_time">--%>
<%--                            <c:choose>--%>
<%--                                <c:when test="${saleorder.isSalesPerformance eq 1}"><date:date value ="${saleorder.salesPerformanceTime}"/></c:when>--%>
<%--                                <c:otherwise>-</c:otherwise>--%>
<%--                            </c:choose>--%>
<%--                        </td>--%>
<%--                    </tr>--%>
                </c:otherwise>
            </c:choose>
            <c:choose>
                <c:when test="${saleorder.orderType eq 5}">
                    <tr>
                        <td>用户确认收货时间</td>
                        <td colspan="3"><date:date value="${saleorder.webTakeDeliveryTime}" format="yyyy-MM-dd" /></td>
                    </tr>
                </c:when>
                <c:otherwise></c:otherwise>
            </c:choose>
            </tbody>
        </table>
    </div>

    <%--客户信息--%>
    <div class="parts" liname="客户信息" id="客户信息">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                客户信息
            </div>
            <c:if test="${saleorder.customerType == 427 && saleorder.validStatus == 1}">
                <a class="title-click nobor addtitle" tabTitle='{"link":"/trader/customer/editbaseinfo.do?traderCustomerId=${customer.traderCustomerId}","title":"完善客户标签"}'>完善客户标签</a>
            </c:if>

            <c:if test="${saleorder.status!=3 and saleorder.traderId != null and saleorder.traderId > 0}">
                <div class="title-click nobor" onclick="syncNewTraderName(${saleorder.saleorderId},${saleorder.invoiceStatus},${saleorder.traderId})">
                    名称同步
                </div>
            </c:if>
            <c:if test="${saleorder.orderType==1}">
                <c:if test="${quoteInfo.isSend==0 && saleorder.validStatus==0 && saleorder.status!=3}">
                    <c:if test="${saleorder.verifyStatus == null || saleorder.verifyStatus eq 2}">
                        <c:if test="${erpAccountId != null }">
                 				<span class="font-strange mr4">
									<a class="edit-user addtitle" tabTitle='{"num":"viewwebaccount${erpAccountId}",
										"link":"./trader/accountweb/view.do?erpAccountId=${erpAccountId}&saleorderId=${saleorder.saleorderId}","title":"关联客户"}'>关联客户</a>
                				</span>
                        </c:if>
                        <c:if test="${erpAccountId == null }">
                            <button type="button" class="font-strange mr4 "  onclick="alert('请检查订单注册手机号!')">关联客户</button>
                        </c:if>
                    </c:if>
                </c:if>
            </c:if>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
                <tr>
                    <td class="table-smaller">客户名称</td>
                    <c:choose>
                        <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 7}">
                            <td>
                                <div class="customername pos_rel">
                                    <c:if test="${saleorder.isTraderRisk > 0}">
                                        <img src="${pageContext.request.contextPath}/static/images/risk.png" width="28px" id="riskFlag_${saleorder.traderId}"
                                             onclick="checkRiskTrader('${saleorder.traderId}','2')">
                                    </c:if>
                            <span class="font-blue mr4">
								<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewcustomer${groupCustomer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${groupCustomer.traderCustomerId}&traderId=${saleorder.groupCustomerId}", "title":"客户信息"}'>
                                        ${groupCustomer.name}
                                </a>
								</span>
                                    <i class="iconbluemouth"></i>
                                    <div class="pos_abs customernameshow mouthControlPos">
                                        报价次数：${groupCustomer.quoteCount} <br>
                                        交易次数：${groupCustomer.buyCount} <br>
                                        交易金额：${groupCustomer.buyMoney} <br>
                                        上次交易日期：<date:date value="${groupCustomer.lastBussinessTime}" format="yyyy-MM-dd"/> <br>
                                        归属销售：${groupCustomer.ownerSale}
                                    </div>
                                </div>
                            </td>
                        </c:when>
                        <c:otherwise>
                            <td>
                                <div class="customername pos_rel">
                                    <c:if test="${saleorder.isTraderRisk > 0}">
                                        <img src="${pageContext.request.contextPath}/static/images/risk.png" width="28px" id="riskFlag_${saleorder.traderId}"
                                             onclick="checkRiskTrader('${saleorder.traderId}','2')">
                                    </c:if>
                                    <span class="font-blue mr4">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.traderId}",
										"title":"客户信息"}'>
									<c:if test="${not empty traderGroupMap && not empty traderGroupMap[saleorder.traderId]}">
                                        <span style="color: red">【${traderGroupMap[saleorder.traderId].traderGroupName}】</span>
                                    </c:if>
									${saleorder.traderName}
                           		</a>
                                </span>
                                    <i class="iconbluemouth"></i>
                                    <div class="pos_abs customernameshow mouthControlPos">
                                        报价次数：${customer.quoteCount} <br>
                                        交易次数：${customer.buyCount} <br>
                                        交易金额：${customer.buyMoney} <br>
                                        上次交易日期：<date:date value="${customer.lastBussinessTime}" format="yyyy-MM-dd" /> <br>
                                        归属销售：${customer.ownerSale}
                                    </div>
                                </div>
                            </td>
                        </c:otherwise>
                    </c:choose>
                    <td class="table-smaller">客户类型 & 客户性质</td>
                    <td>
                        <c:choose>
                            <c:when test="${customer.customerType == 426}">科研医疗</c:when>
                            <c:when test="${customer.customerType == 427}">临床医疗</c:when>
                        </c:choose>
                        &
                        <c:choose>
                            <c:when test="${customer.customerNature == 465}">分销</c:when>
                            <c:when test="${customer.customerNature == 466}">终端</c:when>
                        </c:choose>
                    </td>
                </tr>
                <tr>
                    <td>联系人</td>
                    <td>${saleorder.traderContactName}</td>
                    <td>注册帐号</td>
                    <td><c:if test="${saleorder.orderType==1}">${saleorder.createMobile}</c:if></td>
                </tr>
                <tr>
                    <td>手机</td>
                    <td>
                        <c:if test="${not empty saleorder.traderContactMobile}">
                            <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.traderContactMobile}',${saleorder.traderId},1,2,${saleorder.saleorderId},${saleorder.traderContactId});"></i>
                        </c:if>
                            ${saleorder.traderContactMobile}
                    </td>
                    <td>电话</td>
                    <td>
                        <c:if test="${not empty saleorder.traderContactTelephone}">
                            <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.traderContactTelephone}',${saleorder.traderId},1,2,${saleorder.saleorderId},${saleorder.traderContactId});"></i>
                        </c:if>
                        ${saleorder.traderContactTelephone}
                    </td>
                </tr>
                <c:choose>
                    <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 9 || saleorder.orderType eq 5 || saleorder.orderType eq 7}">
                        <tr>
                            <td>地区</td>
                            <td>${saleorder.traderArea} </td>
                            <td>联系地址</td>
                            <td>${saleorder.traderAddress}</td>
                        </tr>
                    </c:when>
                    <c:otherwise>
                        <tr>
                            <td>地区</td>
                            <td colspan="3">${saleorder.traderArea} &nbsp; ${saleorder.traderAddress}</td>
                        </tr>
                    </c:otherwise>
                </c:choose>
                <tr>
                    <td>客户备注</td>
                    <td colspan="3">${saleorder.traderComments}</td>
                </tr>
                <tr>
                    <td class="wid30">营业执照</td>
                    <td class="text-left" colspan="3">
                        <c:choose>
                            <c:when test="${business ne null && business.uri ne null}">
                                <a href="http://${business.domain}${business.uri}" target="_blank">营业执照</a>
                            </c:when>
                            <c:otherwise>
                                营业执照
                            </c:otherwise>
                        </c:choose>
                        &nbsp;&nbsp;&nbsp;&nbsp; 有效期：
                        <date:date value="${business.begintime}" format="yyyy-MM-dd" />
                        <c:if test="${business ne null && business.endtime eq null}">-无限期</c:if>
                        <c:if test="${business.endtime ne null}">-<date:date value="${business.endtime}" format="yyyy-MM-dd" /></c:if>
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <c:if test="${business.endtime ne null && business.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
                        <c:if test="${business.isMedical eq 1}">含有医疗器械</c:if>
                    </td>
                </tr>
                <tr>
                    <td class="wid30">税务登记证</td>
                    <td class="text-left" colspan="3">
                        <c:choose>
                            <c:when test="${tax ne null && tax.uri ne null}">
                                <a href="http://${tax.domain}${tax.uri}" target="_blank">税务登记证</a>
                            </c:when>
                            <c:otherwise>
                                税务登记证
                            </c:otherwise>
                        </c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
                        有效期：  <date:date value ="${tax.begintime}" format="yyyy-MM-dd"/>
                        <c:if test="${tax ne null && tax.endtime eq null}">-无限期</c:if>
                        <c:if test="${tax.endtime ne null}">-<date:date value ="${tax.endtime}" format="yyyy-MM-dd"/></c:if>
                        <c:if test="${tax.endtime ne null && tax.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
                    </td>
                </tr>
                <tr>
                    <td class="wid30">组织机构代码证</td>
                    <td class="text-left" colspan="3">
                        <c:choose>
                            <c:when test="${orga ne null && orga.uri ne null}">
                                <a href="http://${orga.domain}${orga.uri}" target="_blank">组织机构代码证</a>
                            </c:when>
                            <c:otherwise>
                                组织机构代码证
                            </c:otherwise>
                        </c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
                        有效期：<date:date value ="${orga.begintime}" format="yyyy-MM-dd"/>
                        <c:if test="${orga ne null && orga.endtime eq null}">-无限期</c:if>
                        <c:if test="${orga.endtime ne null}">-<date:date value ="${orga.endtime}" format="yyyy-MM-dd"/></c:if>
                        <c:if test="${orga.endtime ne null && orga.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
                    </td>
                </tr>
                <c:if test="${customerProperty eq 465}"><!-- 分销 -->
                    <tr>
                        <td class="wid30">医疗器械二类备案凭证</td>
                        <td class="text-left" colspan="3">
                            <c:choose>
                                <c:when test="${twoMedicalList ne null}">
                                    <c:forEach items="${twoMedicalList }" var="twoMedical" varStatus="st">
                                        <c:if test="${st.index == 0}">
                                            <c:set var="twoBeginTime" value="${twoMedical.begintime}" />
                                            <c:set var="twoEndTime" value="${twoMedical.endtime}" />
                                            <c:set var="sn" value="${twoMedical.sn}" />
                                        </c:if>
                                        <c:if test="${twoMedical.uri ne null && not empty twoMedical.uri}">
                                            <a href="http://${twoMedical.domain}${twoMedical.uri}" target="_blank">医疗器械二类备案凭证 - ${st.index + 1}</a>&nbsp;&nbsp;
                                        </c:if>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    医疗器械二类备案凭证
                                </c:otherwise>
                            </c:choose>&nbsp;&nbsp;&nbsp;&nbsp; 有效期：
                            <date:date value="${twoBeginTime} " format="yyyy-MM-dd" />
                            <c:if test="${twoMedicalList ne null and twoEndTime eq null && not empty twoMedicalList}">-无限期</c:if>
                            <c:if test="${twoEndTime ne null}">-<date:date value="${twoEndTime}" format="yyyy-MM-dd" /></c:if>
                            &nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${sn}
                            <c:if test="${twoEndTime ne null && twoEndTime lt now }"><span style="color: red">（已过期）</span></c:if>
                        </td>
                    </tr>


                    <tr>
                        <td class="wid30">二类医疗资质详情</td>
                        <td class="text-left" colspan="3">
                            <c:if test="${not empty medicalCertificates }">
                                <c:forEach items="${medicalCertificates }" var="mc">
                                    <c:if test="${mc.medicalCategoryId eq 194}">${mc.title}&nbsp;&nbsp;</c:if>
                                    <c:if test="${mc.medicalCategoryLevel eq 239 ||mc.medicalCategoryLevel eq 241}">${mc.title}（二类）&nbsp;&nbsp;</c:if>
                                </c:forEach>
                            </c:if></td>
                    </tr>
                    <tr>
                        <td class="wid30">三类医疗资质</td>
                        <td class="text-left" colspan="3">
                            <c:choose>
                                <c:when test="${threeMedicalList ne null }">
                                    <c:forEach items="${threeMedicalList}" var="three" varStatus="st">
                                        <c:if test="${three.uri ne null && not empty three.uri}">
                                            <a href="http://${three.domain}${three.uri}" target="_blank">三类医疗资质- ${st.index + 1}</a>&nbsp;&nbsp;
                                        </c:if>
                                    </c:forEach>
                                </c:when>
                                <%--<c:when test="${not empty threeMedical && not empty threeMedical.uri}">--%>
                                <%--<a href="http://${threeMedical.domain}${threeMedical.uri}" target="_blank">三类医疗资质</a>--%>
                                <%--</c:when>--%>
                                <c:otherwise>
                                    三类医疗资质
                                </c:otherwise>
                            </c:choose>
                            <%--<c:choose>
                                <c:when test="${threeMedical ne null && threeMedical.uri ne null}">
                                    <a href="http://${threeMedical.domain}${threeMedical.uri}" target="_blank">三类医疗资质</a>
                                </c:when>
                                <c:otherwise>
                                    三类医疗资质
                                </c:otherwise>
                            </c:choose>--%>
                            &nbsp;&nbsp;&nbsp;&nbsp; 有效期：<date:date value="${threeMedical.begintime} " format="yyyy-MM-dd" />
                            <c:if test="${threeMedical ne null && threeMedical.endtime eq null}">-无限期</c:if>
                            <c:if test="${threeMedical.endtime ne null}">-<date:date value="${threeMedical.endtime} " format="yyyy-MM-dd" /></c:if>
                            &nbsp;&nbsp;&nbsp;&nbsp;许可证编号：${threeMedical.sn} <c:if test="${threeMedical.endtime ne null && threeMedical.endtime lt now }">
                            <span style="color: red">（已过期）</span>
                        </c:if>
                        </td>
                    </tr>
                    <tr>
                        <td class="wid30">三类医疗资质详情</td>
                        <td class="text-left" colspan="3">
                            <c:if test="${not empty medicalCertificates }">
                                <c:forEach items="${medicalCertificates }" var="mc">
                                    <c:if test="${mc.medicalCategoryId eq 194}">${mc.title}&nbsp;&nbsp;</c:if>
                                    <c:if
                                            test="${mc.medicalCategoryLevel eq 240 || mc.medicalCategoryLevel eq 241}">${mc.title}（三类）&nbsp;&nbsp;</c:if>
                                </c:forEach>
                            </c:if>
                        </td>
                    </tr>
                </c:if>
                <c:if test="${customerProperty eq 466}"><!-- 终端 -->
                    <tr>
                        <td class="table-smallest">医疗机构执业许可证</td>
                        <td style="text-align: left;" colspan="3">
                            <c:choose>
                                <c:when test="${practiceList ne null }">
                                    <c:forEach items="${practiceList }" var="practice" varStatus="st">
                                        <c:if test="${st.index == 0}">
                                            <c:set var="beginTime" value="${practice.begintime}"></c:set>
                                            <c:set var="endTime" value="${practice.endtime}"></c:set>
                                            <c:set var="sn" value="${practice.sn}"></c:set>
                                        </c:if>
                                        <c:if test="${practice.uri ne null && not empty practice.uri}">
                                            <a href="http://${practice.domain}${practice.uri}" target="_blank">医疗机构执业许可证 - ${st.index + 1}</a>&nbsp;&nbsp;
                                        </c:if>
                                    </c:forEach>
                                </c:when>
                                <c:otherwise>
                                    医疗机构执业许可证&nbsp;
                                </c:otherwise>
                            </c:choose>&nbsp;&nbsp;&nbsp;
                            有效期：<date:date value ="${beginTime} " format="yyyy-MM-dd"/>
                            <c:if test="${practiceList ne null  && endTime eq null && not empty practiceList}">-无限期</c:if>
                            <c:if test="${endTime ne null}">
                                -<date:date value ="${endTime} " format="yyyy-MM-dd"/>
                            </c:if>&nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${sn}
                            <c:if test="${endTime ne null && endTime ne 0 && endTime lt now }"><span style="color: red">（已过期）</span></c:if>
                        </td>
                    </tr>
                </c:if>
            </tbody>
        </table>
    </div>

    <%--终端信息--%>
    <c:if test="${saleorder.customerNature eq 465}">
            <div class="parts " liname="终端信息" id="终端信息">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        终端信息
                    </div>
                    <div class="title-click nobor J-terminal-edit">添加终端</div>
                </div>
                <table class="table new-table">
                    <colgroup>
                        <col width="300px">
                        <col width="100px">
                        <col width="94px">
                        <col width="106px">
                        <col width="300px">
                        <col width="90px">
                    </colgroup>
                    <thead>
                        <tr>
                            <th>终端名称</th>
                            <th>终端性质</th>
                            <th>终端联系人</th>
                            <th>终端联系电话</th>
                            <th>终端地址</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody class="J-terminal-list-body"></tbody>
                </table>
            </div>
    </c:if>

    <%--产品信息--%>
    <div class="parts" liname="产品信息" id="产品信息">
        <tags:saleorder_goods_info saleorder="${saleorder}" saleorderGoodsList="${saleorderGoodsList}" newSkuInfosMap="${newSkuInfosMap}"
                                   terminalTypes="${terminalTypes}" regions="${regions}" skuNoAndPriceMap="${skuNoAndPriceMap}"
                                   componentList="${componentList}" consultUserList="${consultUserList}" quoteInfo="${quoteInfo}"
                                   expressSeList="${expressSeList}" specialDeliveryList="${specialDeliveryList}" realAmount="${saleorderDataInfo['realAmount']}"
                                   saleorderCoupon="${saleorderCoupon}" totalReferenceCostPrice="${totalReferenceCostPrice}" awardAmount="${saleorderDataInfo['awardAmount']}"/>
    </div>
    <%--促销信息--%>
    <c:choose>
        <c:when test="${(saleorder.orderType eq 9 || saleorder.orderType eq 5 || saleorder.orderType eq 1)
        && !empty saleorderCoupon}">
            <div class="parts" liname="促销信息" id="促销信息">
                <div class="title-container title-container-blue">
                    <div class="table-title nobor">
                        促销信息
                    </div>
                </div>
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                        <tr>
                            <th>优惠劵名称</th>
                            <th>优惠劵类型</th>
                            <th>优惠金额</th>
                            <th>使用门槛</th>
                            <th>品类限制</th>
                            <th>有效期</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:if test="${saleorderCoupon != null}">
                            <td>${saleorderCoupon.couponCode}</td>
                            <td>${saleorderCoupon.couponType eq 1 ? "满减卷":"-"}</td>
                            <td>
                                <c:if test="${saleorderCoupon.denomination == null}">
                                    -
                                </c:if>
                                <fmt:formatNumber type="number" value="${saleorderCoupon.denomination}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:if test="${saleorderCoupon.useThreshold == null}">
                                    -
                                </c:if>
                                <fmt:formatNumber type="number" value="${saleorderCoupon.useThreshold}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>${saleorderCoupon.limitTypeStr == null ? '-' : saleorderCoupon.limitTypeStr}</td>
                            <td>
                                <date:date value ="${saleorderCoupon.effevtiveStartTime} " format="yyyy-MM-dd"/>
                                ~
                                <date:date value ="${saleorderCoupon.effevtiveEndTime} " format="yyyy-MM-dd"/>
                            </td>
                        </c:if>
                    </tbody>
                </table>
            </div>
        </c:when>
        <c:otherwise></c:otherwise>
    </c:choose>
    <%--收货信息--%>
    <div class="parts" liname="收货信息" id="收货信息">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                收货信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">收货客户</td>
                <td>
                    <c:if test="${not empty traderGroupMap && not empty traderGroupMap[saleorder.takeTraderId]}">
                        <span style="color: red">【${traderGroupMap[saleorder.takeTraderId].traderGroupName}】</span>
                    </c:if>
                    <c:choose>
                        <c:when test="${saleorder.orderType==1 }">
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${saleorder.traderId}",
										"title":"客户信息"}'>
                                    ${saleorder.traderName}
                            </a>
                        </c:when>
                        <c:otherwise>
                            <a class="addtitle" href="javascript:void(0);"
                               tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${takeCustomer.traderCustomerId}&traderId=${saleorder.takeTraderId}",
										"title":"客户信息"}'>
                                    ${saleorder.takeTraderName}
                            </a>
                        </c:otherwise>
                    </c:choose>
                </td>
                <td class="table-smaller">收货联系人</td>
                <td>${saleorder.takeTraderContactName}</td>
            </tr>
            <tr>
                <td>手机</td>
                <td>
                    <c:if test="${not empty saleorder.takeTraderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.takeTraderContactMobile}',${saleorder.takeTraderId},1,2,${saleorder.saleorderId},${saleorder.takeTraderContactId});"></i>
                    </c:if>
                    ${saleorder.takeTraderContactMobile}
                </td>
                <td>电话</td>
                <td>
                    <c:if test="${not empty saleorder.takeTraderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.takeTraderContactTelephone}',${saleorder.takeTraderId},1,2,${saleorder.saleorderId},${saleorder.takeTraderContactId});"></i>
                    </c:if>
                    ${saleorder.takeTraderContactTelephone}
                </td>
            </tr>
            <tr>
                <td>发货方式</td>
                <td>
                    <c:forEach var="list" items="${deliveryTypes}">
                        <c:if test="${saleorder.deliveryType == list.sysOptionDefinitionId}">${list.title}</c:if>
                    </c:forEach>
                </td>
                <td>发货要求</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.deliveryClaim eq 0}">立即发货</c:when>
                        <c:when test="${saleorder.deliveryClaim eq 1}">
                            等通知发货
                            <c:if test="${saleorder.deliveryDelayTime > 0}">（<date:date value ="${saleorder.deliveryDelayTime} " format="yyyy-MM-dd"/>）</c:if>
                        </c:when>
                        <c:otherwise>-</c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>随货出库单</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.isPrintout ne 0 && saleorder.isPrintout ne -1}">打印</c:when>
                        <c:otherwise>不打印</c:otherwise>
                    </c:choose>
                    &nbsp;
                    <c:choose>
                        <c:when test="${saleorder.isPrintout eq 1}">含价格</c:when>
                        <c:otherwise>不含价格</c:otherwise>
                    </c:choose>
                </td>
                <td></td>
                <td></td>
            </tr>
            <tr>
                <td>收货地区</td>
                <td>${saleorder.takeTraderArea}</td>
                <td>收货地址</td>
                <td>${saleorder.takeTraderAddress}</td>
            </tr>
<%--            <tr>--%>
<%--                <td>指定物流公司</td>--%>
<%--                <td>--%>
<%--                    <c:forEach var="list" items="${logisticsList}">--%>
<%--                        <c:if test="${saleorder.logisticsId == list.logisticsId}">${list.name}</c:if>--%>
<%--                    </c:forEach>--%>
<%--                </td>--%>
<%--                <td>运费说明</td>--%>
<%--                <td>--%>
<%--                    <c:forEach var="list" items="${freightDescriptions}">--%>
<%--                        <c:if test="${saleorder.freightDescription == list.sysOptionDefinitionId}">${list.title}</c:if>--%>
<%--                    </c:forEach>--%>
<%--                </td>--%>
<%--            </tr>--%>
            <tr>
                <td>物流备注</td>
                <td colspan="3">${saleorder.logisticsComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <%--收票信息--%>
    <div class="parts" liname="收票信息" id="收票信息">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                收票信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">收票客户</td>
                <td>
                    <c:if test="${not empty traderGroupMap && not empty traderGroupMap[saleorder.invoiceTraderId]}">
                        <span style="color: red">【${traderGroupMap[saleorder.invoiceTraderId].traderGroupName}】</span>
                    </c:if>

                    <a class="addtitle" href="javascript:void(0);"
                       tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${invoiceCustomer.traderCustomerId}&traderId=${saleorder.invoiceTraderId}",
										"title":"客户信息"}'>
                        ${saleorder.invoiceTraderName}
                    </a>
                </td>
                <td class="table-smaller">收票联系人</td>
                <td>${saleorder.invoiceTraderContactName}</td>
            </tr>
            <tr>
                <td>手机</td>
                <td>
                    <c:if test="${not empty saleorder.invoiceTraderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.invoiceTraderContactMobile}',${saleorder.invoiceTraderId},1,2,${saleorder.saleorderId},${saleorder.invoiceTraderContactId});"></i>
                    </c:if>
                    ${saleorder.invoiceTraderContactMobile}
                </td>
                <td>电话</td>
                <td>
                    <c:if test="${not empty saleorder.invoiceTraderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.invoiceTraderContactTelephone}',${saleorder.invoiceTraderId},1,2,${saleorder.saleorderId},${saleorder.invoiceTraderContactId});"></i>
                    </c:if>
                    ${saleorder.invoiceTraderContactTelephone}
                </td>
            </tr>
            <tr>
<%--                <td>发票是否寄送</td>--%>
<%--                <td>--%>
<%--                    <c:choose>--%>
<%--                        <c:when test="${saleorder.isSendInvoice eq 0}">不寄送</c:when>--%>
<%--                        <c:otherwise>寄送</c:otherwise>--%>
<%--                    </c:choose>--%>
<%--                </td>--%>
                <td>延迟开票</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.isDelayInvoice eq 1}">是</c:when>
                        <c:otherwise>否</c:otherwise>
                    </c:choose>
                </td>
                <td>发票类型</td>
                <td>
                    <c:forEach var="list" items="${invoiceTypes}">
                        <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
                    </c:forEach>
                </td>
            </tr>
<%--            <tr>--%>
<%--                <td>开票方式</td>--%>
<%--                <td>--%>
<%--                    <c:choose>--%>
<%--                        <c:when test="${saleorder.invoiceMethod eq 1}">手动纸质开票</c:when>--%>
<%--                        <c:when test="${saleorder.invoiceMethod eq 2}">自动纸质开票</c:when>--%>
<%--                        <c:when test="${saleorder.invoiceMethod eq 3}">自动电子发票</c:when>--%>
<%--                        <c:when test="${saleorder.invoiceMethod eq 4}">自动数电发票</c:when>--%>
<%--                    </c:choose>--%>
<%--                </td>--%>
<%--            </tr>--%>
            <c:choose>
                <c:when test="${saleorder.orderType eq 8 || saleorder.orderType eq 9 || saleorder.orderType eq 5 || saleorder.orderType eq 7}">
                    <tr>
                        <td>是否票货同行</td>
                        <td colspan="3">
                            <c:choose>
                                <c:when test="${saleorder.isSendInvoice eq 0}"></c:when>
                                <c:when test="${saleorder.isSendInvoice eq 1 && saleorder.isSameAddress eq 0}">票货不同行</c:when>
                                <c:when test="${saleorder.isSendInvoice eq 1 && saleorder.isSameAddress eq 1}">
                                    票货同行&nbsp;&nbsp;
                                    <c:choose>
                                        <c:when test="${saleorder.invoiceSendNode eq 0}">
                                            全部发货时一次寄送
                                        </c:when>
                                        <c:when test="${saleorder.invoiceSendNode eq 1}">
                                            每次发货时分别寄送
                                        </c:when>
                                        <c:otherwise></c:otherwise>
                                    </c:choose>
                                    <c:choose>
                                        <c:when test="${saleorder.reasonNo != null && saleorder.reasonNo != ''}">
                                            <c:set var="reasonNoStr" value="${saleorder.reasonNo}"/>
                                            <div  class="customername pos_rel">
                                                <i class="iconredsigh"></i>
                                                <div style="float: none" class="pos_abs customernameshow mouthControlPos">
                                                    <b>此订单不满足"票货同行"!</b>
                                                    <br>
                                                    <br>
                                                    <div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'1')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
                                                        1、发票是否寄送：寄送；
                                                        <c:if test="${fn:contains(reasonNoStr,'1')}">
                                                            <font color="red">（不满足）</font>
                                                        </c:if>
                                                    </div>
                                                    <div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'2')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
                                                        2、开票方式：自动电子发票；
                                                        <c:if test="${fn:contains(reasonNoStr,'2')}">
                                                            <font color="red">（不满足）</font>
                                                        </c:if>
                                                    </div>
                                                    <div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'3')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
                                                        3、订单中全部商品的发货方式为“普发”；
                                                        <c:if test="${fn:contains(reasonNoStr,'3')}">
                                                            <font color="red">（不满足）</font>
                                                        </c:if>
                                                    </div>
                                                    <div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'4')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
                                                        4、订单无退票、退货售后单；
                                                        <c:if test="${fn:contains(reasonNoStr,'4')}">
                                                            <font color="red">（不满足）</font>
                                                        </c:if>
                                                    </div>
                                                    <div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'5')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
                                                        5、 “货票客户”相同，且“货票地址”相同；
                                                        <c:if test="${fn:contains(reasonNoStr,'5')}">
                                                            <font color="red">（不满足）</font>
                                                        </c:if>
                                                    </div>
                                                    <div style="color:<c:choose><c:when test="${fn:contains(reasonNoStr,'6')}">red</c:when><c:otherwise>darkgray</c:otherwise></c:choose>">
                                                        6、 “票货是否同行”：票货同行；
                                                        <c:if test="${fn:contains(reasonNoStr,'6')}">
                                                            <font color="red">（不满足）</font>
                                                        </c:if>
                                                    </div>
                                                    <br>
                                                </div>
                                            </div>
                                        </c:when>
                                    </c:choose>
                                </c:when>
                                <c:otherwise></c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:when>
                <c:otherwise></c:otherwise>
            </c:choose>
            <tr>
<%--                <td>收票地区</td>--%>
<%--                <td>${saleorder.invoiceTraderArea}</td>--%>
<%--                <td>收票地址</td>--%>
<%--                <td>${saleorder.invoiceTraderAddress}</td>--%>
                <td>收票邮箱</td>
                <td colspan="3">${saleorder.invoiceEmail}</td>
<%--                <td>开票留言</td>--%>
<%--                <td>${saleorder.invoiceComments}</td>--%>
            </tr>
            </tbody>
        </table>
    </div>

    <%--付款计划--%>
    <div class="parts content1" liname="付款计划" id="付款计划">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                付款计划
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th style="width:150px">计划</th>
                <th style="width:150px">计划内容</th>
                <th style="width:150px">支付金额</th>
                <th>备注</th>
            </tr>
            </thead>
            <c:choose>
                <%--上线之前的付款结构兼容尾款功能--%>
                <c:when test="${saleorder.validStatus eq 1 and saleorder.validTime lt retentionMoneyUptime}">
                    <tbody>
                    <c:if test="${saleorder.paymentType eq 419}">
                        <tr>
                            <td>第一期</td>
                            <td>预付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.prepaidAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>
                                <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                    <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">${list.title}</c:if>
                                </c:forEach>
                            </td>
                        </tr>
                    </c:if>
                    <c:if test="${saleorder.paymentType ne 419 and saleorder.paymentType ne 424}">
                        <tr>
                            <td>第一期</td>
                            <td>预付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.prepaidAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>
                                <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                    <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">${list.title}</c:if>
                                </c:forEach>
                            </td>
                        </tr>
                        <tr>
                            <td>第二期</td>
                            <td>账期付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>到货后${customer.periodDay}天内支付
                                <c:if test="${saleorder.logisticsCollection eq 1}">
                                    / 物流代收
                                </c:if>
                            </td>
                        </tr>
                    </c:if>
                    <c:if test="${saleorder.paymentType eq 424}">
                        <tr>
                            <td>第一期</td>
                            <td>预付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.prepaidAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>
                                <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                    <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">${list.title}</c:if>
                                </c:forEach>
                            </td>
                        </tr>
                        <tr>
                            <td>第二期</td>
                            <td>账期付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>到货后${customer.periodDay}天内支付
                                <c:if test="${saleorder.logisticsCollection eq 1}">
                                    / 物流代收
                                </c:if>
                            </td>
                        </tr>
                        <tr>
                            <td>第三期</td>
                            <td>尾款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.retainageAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>到货后${saleorder.retainageAmountMonth}个月内支付</td>
                        </tr>
                    </c:if>
                    <tr>
                        <td>收款备注</td>
                        <td colspan="3">${saleorder.paymentComments}</td>
                    </tr>
                    </tbody>
                </c:when>
                <%--上线之后采用质保金的展示方式--%>
                <c:otherwise>
                    <tbody>
                    <c:if test="${saleorder.paymentType eq 419}">
                        <tr>
                            <td>第一期</td>
                            <td>预付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.prepaidAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>
                                <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                    <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">${list.title}</c:if>
                                </c:forEach>
                            </td>
                        </tr>
                    </c:if>
                    <c:if test="${saleorder.paymentType ne 419}">
                        <tr>
                            <td>第一期</td>
                            <td>预付款</td>
                            <td><fmt:formatNumber type="number" value="${saleorder.prepaidAmount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>
                                <c:forEach var="list" items="${paymentTermList}" varStatus="status">
                                    <c:if test="${list.sysOptionDefinitionId eq saleorder.paymentType}">${list.title}</c:if>
                                </c:forEach>
                            </td>
                        </tr>

                        <c:if test="${saleorder.accountPeriodAmount.unscaledValue() != 0}">
                            <tr>
                                <td>第二期</td>
                                <td>账期付款(合同余款)</td>
                                <td><fmt:formatNumber type="number" value="${saleorder.accountPeriodAmount - saleorder.retentionMoney}" pattern="0.00" maxFractionDigits="2" /></td>
                                <td>发货/开票后${saleorder.periodDay}天内支付
                                    <c:if test="${saleorder.logisticsCollection eq 1}">
                                        / 物流代收
                                    </c:if>
                                </td>
                            </tr>
                        </c:if>

                        <c:if test="${saleorder.retentionMoney.unscaledValue() != 0}">
                            <tr>
                                <td>第三期</td>
                                <td>账期付款(质保金)</td>
                                <td><fmt:formatNumber type="number" value="${saleorder.retentionMoney}" pattern="0.00" maxFractionDigits="2" /></td>
                                <td>发货/开票后${saleorder.retentionMoneyDay}天内支付</td>
                            </tr>
                        </c:if>
                    </c:if>
                    <tr>
                        <td>收款备注</td>
                        <td colspan="3">${saleorder.paymentComments}</td>
                    </tr>
                    </tbody>
                </c:otherwise>
            </c:choose>
        </table>
    </div>

    <%--其他信息--%>
    <div class="parts" liname="其他信息" id="其他信息">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                其他信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <c:if test="${saleorder.orderType==1 ||saleorder.orderType==5 }">
                <tr>
                    <td class="table-smaller">客户下单备注</td>
                    <td colspan="3" class="text-left">${saleorder.bdtraderComments}</td>
                </tr>
            </c:if>
            <tr>
                <td class="table-smaller">附加条款</td>
                <td colspan="3" class="text-left">${saleorder.additionalClause}</td>
            </tr>
            <tr>
                <td class="table-smaller">内部备注</td>
                <td colspan="3" class="text-left">${saleorder.comments}</td>
            </tr>
            <tr>
                <td class="table-smaller">订单测试</td>
                <td colspan="3" class="text-left">${saleorder.orderTestContent}</td>
            </tr>

            <c:if test="${saleorder.orderType ==3}">
                <tr>
                    <td class="table-smaller">订货备注</td>
                    <td colspan="3" class="text-left">
                        <c:if test="${isUrgent == 1}">
                            加急&nbsp;&nbsp;
                        </c:if>
                        <c:if test="${isCold == 1}">
                            使用冷链&nbsp;&nbsp;
                        </c:if>
                        <c:if test="${saleorder.freightDescription == 474}">
                            快递到付&nbsp;&nbsp;
                        </c:if>
                    </td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>



    <%--沟通记录--%>
    <tags:saleorder_communication_record_info saleorder="${saleorder}" communicateList="${communicateList}" />

    <%--报价咨询--%>
    <c:if test="${saleorder.quoteorderId != null and saleorder.quoteorderId > 0 }">
        <div class="parts content1" liname="报价咨询" id="报价咨询">
            <tags:quoteorder_consult_info consultList="${consultList}" userList="${consultUserList}" roleType="${roleType}" />
            <div class="table-buttons">
                <%--增加状态处理--%>
                <%--<c:if test="${not empty processNodeList && (processNodeList.get(0).status eq 2 || processNodeList.get(1).status eq 2 || processNodeList.get(6).status eq 2 || processNodeList.get(7).status eq 2)}"></c:if>--%>
                <c:if test="${saleorder.status==0 or saleorder.status==4}">
                    <tags:consult_button_list consultRelatedId="${saleorder.saleorderId}" consultType="1" goodsCount="${numOfGoods}"
                                              roleType="${roleType}" orderStatus="${saleorder.status}" />
                </c:if>
            </div>
        </div>
    </c:if>

    <c:if test="${saleorder.validStatus eq 1}">
        <%--开票申请--%>
        <tags:saleorder_invoice_apply_info saleorderDataInfo="${saleorderDataInfo}" saleInvoiceApplyList="${saleInvoiceApplyList}" invoiceTypes="${invoiceTypes}" />
    </c:if>

    <c:if test="${saleorder.validStatus eq 1 or saleorder.orderType eq 5}">
        <%--交易信息--%>
        <div class="parts content1" liname="交易信息" id="交易信息">
            <div class="title-container title-container-yellow">
                <div class="table-title nobor">
                    交易信息
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th style="width: 100px">财务流水号</th>
                    <th style="width: 80px">业务类型</th>
                    <th style="width: 100px">交易金额</th>
                    <th style="width: 130px">交易时间</th>
                    <th style="width: 80px">交易主体</th>
                    <th style="width: 80px">交易方式</th>
                    <th>交易名称</th>
                    <th style="width: 200px">交易备注</th>
                    <th style="width: 100px">操作人</th>
                    <th style="width: 130px">操作时间</th>
                    <th style="width: 80px">操作</th>
                </tr>
                </thead>
                <tbody>
                <c:choose>
                    <c:when test="${not empty capitalBillList}">
                        <c:forEach items="${capitalBillList}" var="list" varStatus="">
                            <tr>
                                <td>${list.capitalBillNo}</td>
                                <td>
                                    <c:forEach var="typeList" items="${bussinessTypes}" varStatus="">
                                        <c:if test="${typeList.sysOptionDefinitionId eq list.capitalBillDetail.bussinessType}">${typeList.title}</c:if>
                                    </c:forEach>
                                </td>
                                <td><fmt:formatNumber type="number" value="${list.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                                <td>
                                    <c:if test="${list.traderTime != 0}">
                                        <date:date value="${list.traderTime}" />
                                    </c:if>
                                </td>
                                <td>
                                    <c:if test="${list.traderSubject == 1}">
                                        对公
                                    </c:if>
                                    <c:if test="${list.traderSubject == 2}">
                                        对私
                                    </c:if>
                                </td>
                                <td>
                                    <c:forEach var="modeList" items="${traderModes}" varStatus="">
                                        <c:if test="${modeList.sysOptionDefinitionId eq list.traderMode}">${modeList.title}</c:if>
                                    </c:forEach>
                                </td>
                                <td>${list.payer}</td>
                                <td class="text-left">${list.comments}</td>
                                <td>${list.creatorName}</td>
                                <td>
                                    <c:if test="${list.addTime != 0}">
                                        <date:date value="${list.addTime}" />
                                    </c:if>
                                </td>
                                <td class="caozuo">
                                    <span class="caozuo-blue" onclick="openFile('${list.receiptUrl}')">回单</span>
                                </td>
                            </tr>
                        </c:forEach>
                    </c:when>
                    <c:otherwise>
                        <tr>
                            <td colspan='10'>暂无交易信息</td>
                        </tr>
                    </c:otherwise>
                </c:choose>
                <tr>
                    <td colspan="11" style="text-align: left; background: #eaf2fd;">
                        <!-- 判断客户是否触发账期 -->
                        <c:set var="accountPeriodAmount" value="0" />
                        <c:if test="${saleorderDataInfo['paymentAmount'] >= saleorder.accountPeriodAmount}">
                            <c:set var="accountPeriodAmount" value="${saleorder.accountPeriodAmount}" />
                        </c:if>
                        订单原金额：<fmt:formatNumber type="number" value="${saleorder.totalAmount}" pattern="0.00" maxFractionDigits="2" />
                        &nbsp;&nbsp;&nbsp;&nbsp;

                        订单实际金额：<fmt:formatNumber type="number" value='${saleorderDataInfo["realAmount"]}' pattern="0.00" maxFractionDigits="2" />
                        &nbsp;&nbsp;

                        <span style="color:red">未收金额：<fmt:formatNumber type="number" value='${saleorderDataInfo["realAmount"] - (saleorderDataInfo["paymentAmount"] + saleorderDataInfo["periodAmount"]  - saleorderDataInfo["lackAccountPeriodAmount"] - saleorderDataInfo["refundBalanceAmount"])}' pattern="0.00" maxFractionDigits="2" /></span>
                        &nbsp;=&nbsp;
                        订单实际金额：<fmt:formatNumber type="number" value='${saleorderDataInfo["realAmount"]}' pattern="0.00" maxFractionDigits="2" />
                        &nbsp;-&nbsp;
                        客户实付金额：<fmt:formatNumber type="number" value='${saleorderDataInfo["paymentAmount"] + saleorderDataInfo["periodAmount"] - saleorderDataInfo["lackAccountPeriodAmount"] - saleorderDataInfo["refundBalanceAmount"]}' pattern="0.00" maxFractionDigits="2" />
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </c:if>

    <c:if test="${saleorder.validStatus eq 1}">
        <%--发票信息--%>
        <tags:saleorder_invoice_info saleorder="${saleorder}" saleInvoiceList="${saleInvoiceList}" invoiceTypes="${invoiceTypes}" saleorderDataInfo="${saleorderDataInfo}" />
    </c:if>



    <%--审核记录--%>
<%--    <c:if test="${saleorder.validStatus eq 0 || saleorder.validStatus eq 1 || orderCheckStatus >= 0 || saleorder.isRisk eq 1}">--%>
        <tags:saleorder_audit_record_info saleorder="${saleorder}" />
<%--    </c:if>--%>

    <c:if test="${saleorder.validStatus eq 1}">
        <%--物流信息--%>
        <tags:saleorder_express_info specialSkuIdList="${specialSkuIdList}" saleorder="${saleorder}" qualityFlagByUserId="${qualityFlagByUserId}" expressList="${expressList}" saleorderGoodsList="${saleorderGoodsList}" displayPrUpFs = "${displayPrUpFs}" />
    </c:if>

    <c:if test="${showExpressReceiptInfo eq 1}">
        <%--客户在线确认记录--%>
        <tags:express_online_receipt_info  expressOnlineReceiptList="${expressOnlineReceiptList}" traderContactName="${saleorder.traderContactName}"/>
    </c:if>

    <%--确认单审核记录--%>
    <c:if test="${saleorder.validStatus eq 1}">
        <tags:confirmation_info confirmationList="${confirmationList}"/>
    </c:if>
    <c:if test="${saleorder.validStatus eq 1}">
        <%--订单修改申请--%>
        <tags:saleorder_modify_apply_info saleorderModifyApplyList="${saleorderModifyApplyList}" />
    </c:if>
    <!--VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）-->
<%--    <div>
        <div class="title-container">
            <div class="table-title nobor">客户签收记录</div>


        </div>

        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid15">序号</th>
                <th class="wid15">产品名称</th>
                <th class="wid20">客户已签收数量</th>
                <th class="wid20">客户签收时间</th>
            </tr>


            </thead>

            <tbody id="customerSignature">

            </tbody>
        </table>

    </div>--%>

    <%--合同回传 & 送货单回传--%>
    <tags:saleorder_contract_reutrn_info saleorder="${saleorder}" saleorderAttachmentList="${saleorderAttachmentList}"
                                         taskInfoContractReturn="${taskInfoContractReturn}" commentMapContractReturn="${commentMapContractReturn}"
                                         historicActivityInstanceContractReturn="${historicActivityInstanceContractReturn}" />

    <c:if test="${saleorder.validStatus eq 1}">
        <%--录保卡--%>
        <tags:saleorder_warranty_info saleorder="${saleorder}" goodsWarrantys="${goodsWarranties}" />

        <%--售后列表--%>
        <c:if test="${saleorder.paymentStatus > 0 || saleorder.deliveryStatus > 0 || saleorder.arrivalStatus > 0 || saleorder.invoiceStatus > 0 ||
        saleInvoiceApplyList.size() > 0}">
            <tags:saleorder_after_sales_info saleorder="${saleorder}" afterSalesList="${afterSalesList}" />
        </c:if>
    </c:if>

    <!--VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）-->
<%--    <div>
        <div class="title-container">
            <div class="table-title nobor">订单确认</div>

            <input type="text" style="display: none" id="copyUrl"  />


            <div class="title-click nobor" id="sendMessage">发送短信</div>
            <div class="title-click nobor" id="copyLink">复制链接</div>
            <div  style="float: right; margin-right: 30px">确认时间:
                <span id="confirmTime"></span>
            </div>

        </div>

        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="wid15">操作人</th>
                <th class="wid15">短信发送时间</th>
                <th class="wid20">发送方式</th>
            </tr>


            </thead>

            <tbody id="orderConfirm">

            </tbody>
        </table>

    </div>--%>

    <input type="hidden" name="formToken" value="${formToken}"/>
    <input type="hidden" name="saleorderId" value="${saleorder.saleorderId}">

    <!-- 终端操作 -->
    <tags:terminal_add_dialog/>
</div>
<script>
    $(function (){
        $('.J-line-wrap').attr('data-json',JSON.stringify(${processNodeList}));

        // 等待流程节点渲染完成后初始化倒计时
        setTimeout(function() {
            // 首先检查是否需要处理自动关闭刷新
            var shouldInitCountdown = checkAutoCloseRefreshOnLoad();
            // 只有在检查通过的情况下才初始化倒计时
            if (shouldInitCountdown) {
                initAutoCloseCountdown();
            } else {
                console.log('由于已达到最大重试次数或订单已关闭，跳过倒计时初始化');
            }
        }, 100);
    });

    // ==================== 全局配置变量 ====================

    /**
     * 销售订单自动关闭天数配置
     * 说明：订单审核通过后，如果在指定天数内未收款且未开票，系统将自动关闭订单
     * 排除条件：已有业务往来的订单（已收款或已开票）不会被自动关闭
     * 影响范围：
     * 1. 倒计时显示的描述文字（如"审核通过X天后未收款未开票自动关闭"）
     * 2. 后端接口中的倒计时计算逻辑
     * 3. 24小时预警的计算（基于总天数的最后24小时）
     * 4. 自动关闭的触发时机
     *
     * 修改此变量后，整个自动关闭功能的天数配置都会相应调整
     */
    var AUTO_CLOSE_DAYS = 60;

    // 倒计时定时器引用，用于在达到最大重试次数时清除定时器
    var countdownTimer = null;

    // 全局变量：倒计时剩余天数
    var countdownRemainingDays = 0;

    // 全局标志：是否已停止自动刷新逻辑
    var autoRefreshStopped = false;

    // ==================== 辅助函数 ====================

    // 获取倒计时剩余天数的辅助函数
    function getCountdownRemainingDays() {
        return countdownRemainingDays;
    }

    // 设置倒计时剩余天数的辅助函数
    function setCountdownRemainingDays(days) {
        countdownRemainingDays = days;
    }

    // 获取自动关闭天数配置的辅助函数
    function getAutoCloseDays() {
        return AUTO_CLOSE_DAYS;
    }

    // 获取自动关闭总毫秒数的辅助函数
    function getAutoCloseMilliseconds() {
        return AUTO_CLOSE_DAYS * 24 * 60 * 60 * 1000;
    }

    // 计算已过天数的辅助函数
    function getElapsedDays() {
        return AUTO_CLOSE_DAYS - countdownRemainingDays;
    }

    // 计算进度百分比的辅助函数
    function getProgressPercentage() {
        if (AUTO_CLOSE_DAYS === 0) return 100;
        return Math.round((getElapsedDays() / AUTO_CLOSE_DAYS) * 100);
    }

    // 示例：获取订单状态信息的函数（展示如何使用AUTO_CLOSE_DAYS进行计算）
    function getOrderStatusInfo() {
        return {
            totalDays: getAutoCloseDays(),                    // 总配置天数
            remainingDays: getCountdownRemainingDays(),       // 剩余天数
            elapsedDays: getElapsedDays(),                    // 已过天数
            progressPercentage: getProgressPercentage(),      // 进度百分比
            totalMilliseconds: getAutoCloseMilliseconds(),    // 总毫秒数
            isUrgent: getCountdownRemainingDays() <= 7,       // 是否紧急（剩余7天内）
            isWarning: getCountdownRemainingDays() <= 1       // 是否警告（剩余1天内）
        };
    }

    /**
     * 注意：修改AUTO_CLOSE_DAYS变量后，还需要同步修改以下后端代码中的硬编码天数：
     * 1. NewSaleorderController.getAutoCloseCountdown() 方法中的 60L * 24 * 60 * 60 * 1000
     * 2. SaleOrderAutoCloseServiceImpl 中的 SIXTY_DAYS_MILLIS 常量
     * 3. 相关的定时任务和数据库查询逻辑
     */

    // 自动关闭倒计时功能
    function initAutoCloseCountdown() {
        var saleOrderId = $("input[name='saleorderId']").val();
        var validStatus = ${saleorder.validStatus};
        var paymentStatus = ${saleorder.paymentStatus};
        var invoiceStatus = ${saleorder.invoiceStatus};
        var orderStatus = ${saleorder.status};

        // 只有同时满足以下条件的订单才显示倒计时：
        // 1. 审核通过（validStatus = 1）
        // 2. 未付款（paymentStatus = 0，排除部分收款=1和全部收款=2）
        // 3. 未开票（invoiceStatus = 0，排除部分开票=1和全部开票=2）
        // 4. 订单状态为进行中（status = 1）
        // 对于已有业务往来的订单（已收款或已开票），不显示自动关闭倒计时
        if (validStatus === 1 && paymentStatus === 0 && invoiceStatus === 0 && orderStatus === 1) {
            // 在"待收款"节点下方添加倒计时显示
            addCountdownToProcessNode();
            updateCountdown();
            // 每分钟更新一次倒计时，保存定时器引用
            countdownTimer = setInterval(updateCountdown, 60000);
        } else if (orderStatus === 3) {
            // 如果订单已关闭，确保倒计时组件被隐藏
            $('.auto-close-countdown').hide();
        } else if (paymentStatus !== 0 || invoiceStatus !== 0) {
            // 如果订单已有业务往来（收款或开票），隐藏倒计时组件
            $('.auto-close-countdown').hide();
            console.log('订单已有业务往来，不显示自动关闭倒计时。paymentStatus:', paymentStatus, 'invoiceStatus:', invoiceStatus);
        }
    }

    // 在流程节点中添加倒计时显示
    function addCountdownToProcessNode() {
        // 查找"待收款"节点（通常是第3个节点，索引为2）
        var $paymentNode = $('.J-line-wrap .t-line-item').eq(2);
        if ($paymentNode.length > 0) {
            // 检查是否已经添加了倒计时
            if ($paymentNode.find('.auto-close-countdown').length === 0) {
                // 创建倒计时HTML，使用replace-tip样式替换未收款文字
                var countdownHtml = '<div class="auto-close-countdown replace-tip">' +
                    '<span id="autoCloseCountdown">正在计算剩余时间...</span>' +
                    '<div class="countdown-description">(审核通过后' + AUTO_CLOSE_DAYS + '天未处理自动关闭)</div>' +
                    '</div>';

                // 查找tip区域，如果存在则替换其内容
                var $tipArea = $paymentNode.find('.t-line-item-tip');
                if ($tipArea.length > 0) {
                    // 直接替换tip区域的内容
                    $tipArea.html(countdownHtml);
                } else {
                    // 如果没有tip区域，添加到内容区域
                    $paymentNode.find('.t-line-item-cnt').append(countdownHtml);
                }

                // 为节点添加has-countdown类
                $paymentNode.addClass('has-countdown');
            }
        }
    }

    function updateCountdown() {
        // 检查全局标志，如果已停止则直接返回
        if (autoRefreshStopped) {
            console.log('自动刷新已停止，跳过倒计时更新');
            return;
        }

        var saleOrderId = $("input[name='saleorderId']").val();
        var refreshKey = 'autoCloseRefresh_' + saleOrderId;
        var refreshCount = parseInt(sessionStorage.getItem(refreshKey) || '0');

        // 如果已经达到最大重试次数，停止倒计时更新
        if (refreshCount >= 3) {
            console.log('已达到最大重试次数，停止倒计时更新');
            performCompleteCleanup();
            return;
        }

        $.ajax({
            url: '/orderstream/saleorder/getAutoCloseCountdown.do',
            type: 'GET',
            data: { saleOrderId: saleOrderId },
            dataType: 'json',
            success: function(response) {
                if (response.code === 0) {
                    var data = response.data;
                    if (data.showCountdown) {
                        // 基于前端配置的AUTO_CLOSE_DAYS重新计算剩余时间
                        var recalculatedRemainingTime = calculateRemainingTimeBasedOnConfig(data.validTime);
                        var countdownText = formatCountdownTime(recalculatedRemainingTime);

                        // 如果formatCountdownTime返回null，说明时间已到，触发页面刷新
                        if (countdownText === null) {
                            // 再次检查重试次数，防止在AJAX请求期间计数器被更新
                            var currentRefreshCount = parseInt(sessionStorage.getItem(refreshKey) || '0');
                            if (currentRefreshCount < 3) {
                                handleAutoCloseRefresh();
                            } else {
                                console.log('倒计时到期但已达到最大重试次数，停止刷新');
                                performCompleteCleanup();
                            }
                        } else {
                            $('#autoCloseCountdown').html(countdownText);

                            // 如果剩余时间小于24小时，显示红色警告
                            // 24小时预警基于总配置天数的最后24小时
                            var twentyFourHoursInMillis = 24 * 60 * 60 * 1000;
                            if (recalculatedRemainingTime <= twentyFourHoursInMillis) {
                                $('#autoCloseCountdown').css('color', '#ff0000');
                            }
                        }
                    } else {
                        // 隐藏倒计时显示
                        $('.auto-close-countdown').hide();
                    }
                }
            },
            error: function() {
                console.log('获取倒计时信息失败');
            }
        });
    }

    // 基于前端配置的AUTO_CLOSE_DAYS重新计算剩余时间
    function calculateRemainingTimeBasedOnConfig(validTime) {
        var currentTime = new Date().getTime();
        var elapsedTime = currentTime - validTime;
        var totalConfiguredTime = getAutoCloseMilliseconds(); // 使用前端配置的天数
        var remainingTime = totalConfiguredTime - elapsedTime;

        return Math.max(0, remainingTime); // 确保不返回负数
    }

    function formatCountdownTime(milliseconds) {
        if (milliseconds <= 0) {
            // 当时间到期时，更新全局变量并返回null表示需要触发页面刷新
            countdownRemainingDays = 0;
            return null;
        }

        var days = Math.floor(milliseconds / (1000 * 60 * 60 * 24));
        var hours = Math.floor((milliseconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        var minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));

        // 更新全局变量
        countdownRemainingDays = days;

        // 计算总配置天数，用于显示进度信息
        var totalDays = AUTO_CLOSE_DAYS;
        var elapsedDays = totalDays - days;

        var result = '剩余';
        if (days > 0) {
            result += days + '天';
        }
        if (hours > 0) {
            result += hours + '时';
        }
        if (minutes > 0) {
            result += minutes + '分';
        }

        if (result === '剩余') {
            result = '剩余不足1分钟';
        }

        return result;
    }

    // 处理自动关闭时的页面刷新逻辑
    function handleAutoCloseRefresh() {
        // 检查全局标志，如果已停止则直接返回
        if (autoRefreshStopped) {
            console.log('自动刷新已停止，跳过刷新逻辑');
            return;
        }

        var saleOrderId = $("input[name='saleorderId']").val();
        var refreshKey = 'autoCloseRefresh_' + saleOrderId;
        var refreshCount = parseInt(sessionStorage.getItem(refreshKey) || '0');

        // 如果刷新次数已达到最大值，停止刷新
        if (refreshCount >= 3) {
            console.log('订单自动关闭刷新已达到最大重试次数(3次)，停止刷新');
            sessionStorage.removeItem(refreshKey);
            performCompleteCleanup();
            return;
        }

        // 先通过AJAX检查订单当前状态
        $.ajax({
            url: '/orderstream/saleorder/getOrderStatusInfo.do',
            type: 'GET',
            data: { saleOrderId: saleOrderId },
            dataType: 'json',
            success: function(response) {
                if (response.code === 0 && response.data && response.data.status === 3) {
                    // 订单已关闭，清除刷新计数并停止
                    console.log('检测到订单已关闭，停止自动刷新');
                    sessionStorage.removeItem(refreshKey);
                    performCompleteCleanup();
                    return;
                }

                // 订单未关闭，继续刷新逻辑
                refreshCount++;
                sessionStorage.setItem(refreshKey, refreshCount.toString());
                console.log('订单自动关闭，执行第' + refreshCount + '次页面刷新');
                window.location.reload();
            },
            error: function() {
                // 如果检查状态失败，仍然执行刷新，但增加计数
                refreshCount++;
                sessionStorage.setItem(refreshKey, refreshCount.toString());
                console.log('无法检查订单状态，执行第' + refreshCount + '次页面刷新');
                window.location.reload();
            }
        });
    }

    // 页面加载完成后检查是否需要处理自动关闭刷新
    // 返回值：true表示可以继续初始化倒计时，false表示应该停止所有自动刷新逻辑
    function checkAutoCloseRefreshOnLoad() {
        var saleOrderId = $("input[name='saleorderId']").val();
        var refreshKey = 'autoCloseRefresh_' + saleOrderId;
        var refreshCount = parseInt(sessionStorage.getItem(refreshKey) || '0');
        var currentOrderStatus = ${saleorder.status};

        // 如果有刷新记录
        if (refreshCount > 0) {
            // 如果订单状态已更新为关闭，清除刷新计数
            if (currentOrderStatus === 3) {
                sessionStorage.removeItem(refreshKey);
                console.log('订单状态已更新为关闭，清除自动刷新记录');
                // 执行完整的清理操作
                performCompleteCleanup();
                return false; // 阻止倒计时初始化
            } else if (refreshCount >= 3) {
                // 如果已达到最大重试次数但状态仍未更新，清除记录并停止
                sessionStorage.removeItem(refreshKey);
                console.log('页面加载时检测到已达到最大重试次数，停止所有自动刷新逻辑');
                // 执行完整的清理操作
                performCompleteCleanup();
                return false; // 阻止倒计时初始化
            }
        }

        return true; // 可以继续初始化倒计时
    }

    // 执行完整的清理操作
    function performCompleteCleanup() {
        // 设置全局标志，阻止后续的自动刷新逻辑
        autoRefreshStopped = true;

        // 清除定时器
        if (countdownTimer) {
            clearInterval(countdownTimer);
            countdownTimer = null;
            console.log('已清除倒计时定时器');
        }

        // 隐藏倒计时组件
        $('.auto-close-countdown').hide();
        console.log('已隐藏倒计时组件');

        // 移除相关的CSS类
        $('.J-line-wrap .t-line-item').removeClass('has-countdown');

        // 清理可能存在的倒计时HTML
        $('.auto-close-countdown').remove();

        console.log('已完成所有清理操作，自动刷新逻辑已彻底停止');
    }


    // 显示订单关闭通知
    function showOrderClosedNotification() {
        // 创建通知元素
        var $notification = $('<div class="order-closed-notification">' +
            '<div class="notification-content">' +
                '<i class="icon-warning" style="color: #ff6600; margin-right: 8px;">⚠</i>' +
                '<span>订单已自动关闭</span>' +
                '<button type="button" class="close-btn" onclick="$(this).closest(\'.order-closed-notification\').fadeOut()">×</button>' +
            '</div>' +
        '</div>');

        // 添加样式
        $notification.css({
            'position': 'fixed',
            'top': '20px',
            'right': '20px',
            'background': '#fff3cd',
            'border': '1px solid #ffeaa7',
            'border-radius': '4px',
            'padding': '12px 16px',
            'box-shadow': '0 2px 8px rgba(0,0,0,0.1)',
            'z-index': '9999',
            'font-size': '14px',
            'color': '#856404'
        });

        $notification.find('.notification-content').css({
            'display': 'flex',
            'align-items': 'center'
        });

        $notification.find('.close-btn').css({
            'background': 'none',
            'border': 'none',
            'font-size': '18px',
            'cursor': 'pointer',
            'margin-left': '12px',
            'color': '#856404'
        });

        // 添加到页面并显示动画
        $('body').append($notification);
        $notification.hide().fadeIn(300);

        // 5秒后自动消失
        setTimeout(function() {
            $notification.fadeOut(300, function() {
                $(this).remove();
            });
        }, 5000);
    }

</script>

<script type="text/javascript" src='<%= basePath %>static/js/logistics/warehouseOut/viewWarehouseOut.js?rnd=${resourceVersionKey}'></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/openfile.js?rnd=${resourceVersionKey}"></script>
</body>
</html>
