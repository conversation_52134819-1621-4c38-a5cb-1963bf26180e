package com.pricecenter.service.validator;

import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.dto.ValidatorResult;
import com.pricecenter.service.Validator;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 价格相关校验器
 */
@Service
public class PriceValidatorForReference implements Validator<PriceInfoUploadValidatorDto> {

    public static BigDecimal Three_Bill = BigDecimal.valueOf(300000000);

    @Override
    public ValidatorResult validator(PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        ValidatorResult result = ValidatorResult.newBuild();

        //获取所有的行
        List<Row> rowList = priceInfoUploadValidatorDto.getRows();

        Row thisRow = null;
        int rowNum = 0;

        int terminalCellNum = 1;
        int distributionCellNum = 2;
        int groupCellNum = 3;

        for(int i = 0;i < rowList.size();i++){

            rowNum = i;

            thisRow = rowList.get(i);

            Cell terminalCell = thisRow.getCell(terminalCellNum);
            Cell distributionCell = thisRow.getCell(distributionCellNum);
            Cell groupCell = thisRow.getCell(groupCellNum);

            /*if ((terminalCell == null || terminalCell.getCellType() ==  CellType.BLANK)
                    || (distributionCell == null || distributionCell.getCellType() ==  CellType.BLANK)
                    || (groupCell == null || groupCell.getCellType() ==  CellType.BLANK)) {
                return result.setMessage("行"+(rowNum + 1)+"中终端参考价,经销参考价,集团参考价为空，提交失败");
            }*/

            if(!validatorCell(terminalCell,rowNum,result) || !validatorCell(distributionCell,rowNum,result) || !validatorCell(groupCell,rowNum,result)){
                return result;
            }
        }

        return result.setResult(true);
    }

    private boolean validatorCell(Cell terminalCell, int rowNum, ValidatorResult result) {

        if(terminalCell != null && terminalCell.getCellType() !=  CellType.BLANK){

            terminalCell.setCellType(CellType.STRING);
            String terminalValue = terminalCell.getStringCellValue();
            //数字保留2位小数
            if(!priceVadator(terminalValue)){
                result.setMessage("行"+(rowNum + 1)+"中终端参考价,经销参考价,集团参考价只能是正数，且最多2位小数，提交失败");
                return false;
            }

            BigDecimal  terminalPrice = new BigDecimal(terminalValue);

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”为0，提交失败
            if(terminalPrice.compareTo(BigDecimal.ZERO) == 0){
                result.setMessage("行"+(rowNum + 1)+"中终端参考价,经销参考价,集团参考价为0，提交失败");
                return false;
            }

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”超过3亿，提交失败
            if(terminalPrice.compareTo(Three_Bill) > 0){
                result.setMessage("行"+(rowNum + 1)+"中终端参考价,经销参考价,集团参考价超过3亿，提交失败");
                return false;
            }
        }

        return true;
    }


    public boolean priceVadator(String priceStr) {
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$"); // 判断小数点后2位的数字的正则表达式
        Matcher match = pattern.matcher(priceStr);
        return match.matches();
    }

}
