package com.wms.dto;

import lombok.Data;

/**
 * @Description:  出库修改接口DTO
 * @Author:       davis
 * @Date:         2021/6/9 上午8:55
 * @Version:      1.0
 */
@Data
public class ModifyOrderDto extends AbstractCommonDto {

    private String SOReference1;

    private String OrderNo;

    private String orderType;

    /**
     * 等待发货截止日
     */
    private String expectedShipmentTime2;

    /**
     * 是否货齐发货 Y/N
     */
    private String hedi04;

    /**
     * 发货类型，必传
     * A 正常发货
     * B 等通知发货
     * C 多地址发货
     */
    private String hedi07;

    /**
     * 省
     */
    private String consigneeProvince ;

    /**
     * 城市
     */
    private String consigneeCity ;

    /**
     * 区
     */
    private String consigneeDistrict;

    /**
     * 街道
     */
    private String consigneeStreet;

    /**
     * 邮编
     */
    private String consigneeZip ;

    /**
     * 收货详细地址
     */
    private String consigneeAddress1 ;

    /**
     * 收货人联系人1
     */
    private String consigneeContact;

    /**
     * 收货人电话1
     */
    private String consigneeTel1;

    /**
     * 收货人手机1
     */
    private String consigneeTel2;

    /**
     * 是否签回单
     */
    private String hedi01;

    /**
     * 是否打印4联随货同行单 必传  Y/N
     */
    private String hedi03;

    /**
     * 预留字段
     */
    private String userDefine1;

    /**
     * 预留字段
     */
    private String userDefine2;

    /**
     * 预留字段
     */
    private String userDefine3;

    /**
     * 预留字段
     */
    private String userDefine4;

    /**
     * 预留字段
     */
    private String userDefine5;

    /**
     * 备注
     */
    private String notes;
}
