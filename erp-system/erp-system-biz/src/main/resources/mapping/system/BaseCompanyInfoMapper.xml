<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.BaseCompanyInfoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity">
    <!--@mbg.generated-->
    <!--@Table T_BASE_COMPANY_INFO-->
    <id column="ID" property="id" />
    <result column="COMPANY_NAME" property="companyName" />
    <result column="COMPANY_SHORT_NAME" property="companyShortName" />
    <result column="FRONT_END_SEQ" property="frontEndSeq" />
    <result column="KINGDEE_ACCOUNT_CODE" property="kingdeeAccountCode" />
    <result column="BUSINESS_LICENSE" property="businessLicense" />
    <result column="COMPANY_ADDRESS" property="companyAddress" />
    <result column="CONTACT_PHONE" property="contactPhone" />
    <result column="BANK_NAME" property="bankName" />
    <result column="BANK_ACCOUNT" property="bankAccount" />
    <result column="CONTRACT_ADDRESS" property="contractAddress" />
    <result column="CUSTOMER_TRADER_ID" property="customerTraderId" />
    <result column="SUPPLIER_TRADER_ID" property="supplierTraderId" />
    <result column="CREATE_TIME" property="createTime" />
    <result column="UPDATE_TIME" property="updateTime" />
    <result column="CREATE_USER" property="createUser" />
    <result column="UPDATE_USER" property="updateUser" />
    <result column="IS_DELETED" property="isDeleted" />
    <result column="ERP_DOMAIN" property="erpDomain" />
    <result column="DETAIL_JSON" property="detailJson" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, COMPANY_NAME, COMPANY_SHORT_NAME, FRONT_END_SEQ, KINGDEE_ACCOUNT_CODE, BUSINESS_LICENSE,
    COMPANY_ADDRESS, CONTACT_PHONE, BANK_NAME, BANK_ACCOUNT, CONTRACT_ADDRESS, CUSTOMER_TRADER_ID,
    SUPPLIER_TRADER_ID, CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, IS_DELETED,
    ERP_DOMAIN, DETAIL_JSON
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BASE_COMPANY_INFO
    where ID = #{id}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_BASE_COMPANY_INFO
    where ID = #{id}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BASE_COMPANY_INFO (COMPANY_NAME, COMPANY_SHORT_NAME, FRONT_END_SEQ, KINGDEE_ACCOUNT_CODE,
    BUSINESS_LICENSE, COMPANY_ADDRESS, CONTACT_PHONE, BANK_NAME, BANK_ACCOUNT,
    CONTRACT_ADDRESS, CUSTOMER_TRADER_ID, SUPPLIER_TRADER_ID, CREATE_TIME, UPDATE_TIME,
    CREATE_USER, UPDATE_USER, IS_DELETED, ERP_DOMAIN, DETAIL_JSON)
    values (#{companyName}, #{companyShortName}, #{frontEndSeq}, #{kingdeeAccountCode},
    #{businessLicense}, #{companyAddress}, #{contactPhone}, #{bankName}, #{bankAccount},
    #{contractAddress}, #{customerTraderId}, #{supplierTraderId}, #{createTime}, #{updateTime},
    #{createUser}, #{updateUser}, #{isDeleted}, #{erpDomain}, #{detailJson})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_BASE_COMPANY_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyName != null and companyName != ''">
        COMPANY_NAME,
      </if>
      <if test="companyShortName != null and companyShortName != ''">
        COMPANY_SHORT_NAME,
      </if>
      <if test="frontEndSeq != null">
        FRONT_END_SEQ,
      </if>
      <if test="kingdeeAccountCode != null and kingdeeAccountCode != ''">
        KINGDEE_ACCOUNT_CODE,
      </if>
      <if test="businessLicense != null and businessLicense != ''">
        BUSINESS_LICENSE,
      </if>
      <if test="companyAddress != null and companyAddress != ''">
        COMPANY_ADDRESS,
      </if>
      <if test="contactPhone != null and contactPhone != ''">
        CONTACT_PHONE,
      </if>
      <if test="bankName != null and bankName != ''">
        BANK_NAME,
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        BANK_ACCOUNT,
      </if>
      <if test="contractAddress != null and contractAddress != ''">
        CONTRACT_ADDRESS,
      </if>
      <if test="customerTraderId != null">
        CUSTOMER_TRADER_ID,
      </if>
      <if test="supplierTraderId != null">
        SUPPLIER_TRADER_ID,
      </if>
      <if test="createTime != null">
        CREATE_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="createUser != null and createUser != ''">
        CREATE_USER,
      </if>
      <if test="updateUser != null and updateUser != ''">
        UPDATE_USER,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="erpDomain != null and erpDomain != ''">
        ERP_DOMAIN,
      </if>
      <if test="detailJson != null and detailJson != ''">
        DETAIL_JSON,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyName != null and companyName != ''">
        #{companyName},
      </if>
      <if test="companyShortName != null and companyShortName != ''">
        #{companyShortName},
      </if>
      <if test="frontEndSeq != null">
        #{frontEndSeq},
      </if>
      <if test="kingdeeAccountCode != null and kingdeeAccountCode != ''">
        #{kingdeeAccountCode},
      </if>
      <if test="businessLicense != null and businessLicense != ''">
        #{businessLicense},
      </if>
      <if test="companyAddress != null and companyAddress != ''">
        #{companyAddress},
      </if>
      <if test="contactPhone != null and contactPhone != ''">
        #{contactPhone},
      </if>
      <if test="bankName != null and bankName != ''">
        #{bankName},
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        #{bankAccount},
      </if>
      <if test="contractAddress != null and contractAddress != ''">
        #{contractAddress},
      </if>
      <if test="customerTraderId != null">
        #{customerTraderId},
      </if>
      <if test="supplierTraderId != null">
        #{supplierTraderId},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="createUser != null and createUser != ''">
        #{createUser},
      </if>
      <if test="updateUser != null and updateUser != ''">
        #{updateUser},
      </if>
      <if test="isDeleted != null">
        #{isDeleted},
      </if>
      <if test="erpDomain != null and erpDomain != ''">
        #{erpDomain},
      </if>
      <if test="detailJson != null and detailJson != ''">
        #{detailJson},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity">
    <!--@mbg.generated-->
    update T_BASE_COMPANY_INFO
    <set>
      <if test="companyName != null and companyName != ''">
        COMPANY_NAME = #{companyName},
      </if>
      <if test="companyShortName != null and companyShortName != ''">
        COMPANY_SHORT_NAME = #{companyShortName},
      </if>
      <if test="frontEndSeq != null">
        FRONT_END_SEQ = #{frontEndSeq},
      </if>
      <if test="kingdeeAccountCode != null and kingdeeAccountCode != ''">
        KINGDEE_ACCOUNT_CODE = #{kingdeeAccountCode},
      </if>
      <if test="businessLicense != null and businessLicense != ''">
        BUSINESS_LICENSE = #{businessLicense},
      </if>
      <if test="companyAddress != null and companyAddress != ''">
        COMPANY_ADDRESS = #{companyAddress},
      </if>
      <if test="contactPhone != null and contactPhone != ''">
        CONTACT_PHONE = #{contactPhone},
      </if>
      <if test="bankName != null and bankName != ''">
        BANK_NAME = #{bankName},
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        BANK_ACCOUNT = #{bankAccount},
      </if>
      <if test="contractAddress != null and contractAddress != ''">
        CONTRACT_ADDRESS = #{contractAddress},
      </if>
      <if test="customerTraderId != null">
        CUSTOMER_TRADER_ID = #{customerTraderId},
      </if>
      <if test="supplierTraderId != null">
        SUPPLIER_TRADER_ID = #{supplierTraderId},
      </if>
      <if test="createTime != null">
        CREATE_TIME = #{createTime},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime},
      </if>
      <if test="createUser != null and createUser != ''">
        CREATE_USER = #{createUser},
      </if>
      <if test="updateUser != null and updateUser != ''">
        UPDATE_USER = #{updateUser},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted},
      </if>
      <if test="erpDomain != null and erpDomain != ''">
        ERP_DOMAIN = #{erpDomain},
      </if>
      <if test="detailJson != null and detailJson != ''">
        DETAIL_JSON = #{detailJson},
      </if>
    </set>
    where ID = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity">
    <!--@mbg.generated-->
    update T_BASE_COMPANY_INFO
    set COMPANY_NAME = #{companyName},
    COMPANY_SHORT_NAME = #{companyShortName},
    FRONT_END_SEQ = #{frontEndSeq},
    KINGDEE_ACCOUNT_CODE = #{kingdeeAccountCode},
    BUSINESS_LICENSE = #{businessLicense},
    COMPANY_ADDRESS = #{companyAddress},
    CONTACT_PHONE = #{contactPhone},
    BANK_NAME = #{bankName},
    BANK_ACCOUNT = #{bankAccount},
    CONTRACT_ADDRESS = #{contractAddress},
    CUSTOMER_TRADER_ID = #{customerTraderId},
    SUPPLIER_TRADER_ID = #{supplierTraderId},
    CREATE_TIME = #{createTime},
    UPDATE_TIME = #{updateTime},
    CREATE_USER = #{createUser},
    UPDATE_USER = #{updateUser},
    IS_DELETED = #{isDeleted},
    ERP_DOMAIN = #{erpDomain},
    DETAIL_JSON = #{detailJson}
    where ID = #{id}
  </update>


  <select id="selectByShortName" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BASE_COMPANY_INFO
    where COMPANY_SHORT_NAME = #{shortName,jdbcType=VARCHAR}
  </select>

  <select id="selectByCompanyName" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BASE_COMPANY_INFO
    where COMPANY_NAME = #{companyName,jdbcType=VARCHAR}
  </select>

  <select id="selectByCompanyNames" parameterType="java.lang.String" resultType="com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_BASE_COMPANY_INFO
    where COMPANY_NAME IN
    <foreach collection="companyNames" index="index" separator="," open="(" item="companyName" close=")">
      #{companyName}
    </foreach>
  </select>

  <select id="findAll" resultMap="BaseResultMap">
    select * from T_BASE_COMPANY_INFO where IS_DELETED = 0
  </select>
</mapper>