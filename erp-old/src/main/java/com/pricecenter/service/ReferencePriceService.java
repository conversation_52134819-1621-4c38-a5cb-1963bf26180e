package com.pricecenter.service;

import com.pricecenter.dto.*;

import java.util.List;

/**
 * 参考价
 */
public interface ReferencePriceService {

    PageResultDto<SkuReferencePriceResponseDto> findByPage(SkuReferencePricePageQueryDto queryDto);

    SkuReferencePriceResponseDto findReferencePriceById(Integer referencePriceId);

    void editReferencePrice(EditReferencePriceDto editReferencePriceDto);

    void batchUploadReferencePrice(List<ReferencePriceUploadDto> referencePriceDtoList);
}
