package com.wms.service.processor.input;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.ReturnStatusConstant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.controller.WMSController;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 外借出库单 -> 入库处理器
 */
@Service
public class WmsLendInputProcessor extends AbstractInputOrderProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmsLendInputProcessor.class);

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper outputOrderGoodsMapper;

    @Autowired
    @Qualifier("lendInputCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Override
    protected void commonValidator(InputOrderDto inputOrderDto) throws Exception {

        if(!WmsInterfaceOrderType.INPUT_LEND_IN.equals(inputOrderDto.getASNType())){
            throw new Exception("入库单:"+inputOrderDto.getASNReference1()+"的类型错误!");
        }

    }

    @Override
    protected void updateOrderData(InputOrderDto inputOrderDto) {

        String lendOutNo = inputOrderDto.getASNReference1();

        List<InputOrderGoodsDto> inputOrderGoodsList = inputOrderDto.getDetails();

        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByOrderNo(lendOutNo);

        List<WmsOutputOrderGoods> outputOrderGoodList = outputOrderGoodsMapper.queryOutputGoodsByLendOutId(wmsOutputOrder.getId());

//        ThreadLocalContext.put("outputOrderGoodList",outputOrderGoodList);

        boolean isAllRecieve = true;

        String currentTime = DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT);

        for(WmsOutputOrderGoods outputOrderGood : outputOrderGoodList) {

            int receievedNum = inputOrderGoodsList.stream()
                                                  .filter(g -> g.getSKU().equals(outputOrderGood.getSkuNo()))
                                                  .mapToInt(g -> g.getReceivedQty().intValue())
                                                  .sum();

            int realNum = outputOrderGood.getOutputNum();

            WmsOutputOrderGoods updateGood = new WmsOutputOrderGoods();
            updateGood.setId(outputOrderGood.getId());
            updateGood.setAlreadyInputNum(outputOrderGood.getAlreadyInputNum() + receievedNum);


            updateGood.setLastInputTime(currentTime);
            updateGood.setUpdateTime(currentTime);
            updateGood.setInputStatus(updateGood.getAlreadyInputNum() == realNum ? ReturnStatusConstant.RETURNED : ReturnStatusConstant.PART_RETURN);

            LOGGER.info("外借单入库，更新外借单商品的数据:" + JSON.toJSONString(updateGood));

            outputOrderGoodsMapper.updateByPrimaryKeySelective(updateGood);

            //未全部收货
            if(updateGood.getInputStatus() == ReturnStatusConstant.PART_RETURN ){
                isAllRecieve = false;
            }
        }

        WmsOutputOrder outputOrderUpdate = new WmsOutputOrder();
        outputOrderUpdate.setId(wmsOutputOrder.getId());
        outputOrderUpdate.setUpdateTime(currentTime);
        outputOrderUpdate.setReturnStatus(isAllRecieve ? ReturnStatusConstant.RETURNED : ReturnStatusConstant.PART_RETURN);

        LOGGER.info("外借单入库，更新外借单的数据:" + JSON.toJSONString(outputOrderUpdate));
        this.outputOrderMapper.updateByPrimaryKeySelective(outputOrderUpdate);

    }

    @Override
    protected int getOperateType(InputOrderDto inputOrderDto) {
        return StockOperateTypeConst.LENDOUT_WAREHOUSE_IN;
    }

    @Override
    protected int getWmsLogicalOperateType(InputOrderDto inputOrderDto) {
        return WmsLogicalOperateTypeEnum.LENDOUT_EXG.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(InputOrderDto inputOrderDto, InputOrderGoodsDto goodsDto) {

        return Integer.valueOf(goodsDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 3;
    }

}
