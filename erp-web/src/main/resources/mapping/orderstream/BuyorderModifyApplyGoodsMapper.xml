<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.BuyorderModifyApplyGoodsMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods">
            <id property="buyorderModifyApplyGoodsId" column="BUYORDER_MODIFY_APPLY_GOODS_ID" jdbcType="INTEGER"/>
            <result property="buyorderModifyApplyId" column="BUYORDER_MODIFY_APPLY_ID" jdbcType="INTEGER"/>
            <result property="buyorderGoodsId" column="BUYORDER_GOODS_ID" jdbcType="INTEGER"/>
            <result property="insideComments" column="INSIDE_COMMENTS" jdbcType="VARCHAR"/>
            <result property="oldInsideComments" column="OLD_INSIDE_COMMENTS" jdbcType="VARCHAR"/>
            <result property="oldSendGoodsTime" column="OLD_SEND_GOODS_TIME" jdbcType="BIGINT"/>
            <result property="sendGoodsTime" column="SEND_GOODS_TIME" jdbcType="BIGINT"/>
            <result property="oldReceiveGoodsTime" column="OLD_RECEIVE_GOODS_TIME" jdbcType="BIGINT"/>
            <result property="receiveGoodsTime" column="RECEIVE_GOODS_TIME" jdbcType="BIGINT"/>
            <result property="oldPrice" column="OLD_PRICE" jdbcType="DECIMAL"/>
            <result property="price" column="PRICE" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        BUYORDER_MODIFY_APPLY_GOODS_ID,BUYORDER_MODIFY_APPLY_ID,BUYORDER_GOODS_ID,
        INSIDE_COMMENTS,OLD_INSIDE_COMMENTS,OLD_SEND_GOODS_TIME,
        SEND_GOODS_TIME,OLD_RECEIVE_GOODS_TIME,RECEIVE_GOODS_TIME,
        OLD_PRICE,PRICE
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_BUYORDER_MODIFY_APPLY_GOODS
        where  BUYORDER_MODIFY_APPLY_GOODS_ID = #{buyorderModifyApplyGoodsId,jdbcType=INTEGER}
    </select>
    <select id="selectAllByApplyId" resultType="com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods">
        select
        <include refid="Base_Column_List" />
        from T_BUYORDER_MODIFY_APPLY_GOODS
        where  BUYORDER_MODIFY_APPLY_ID = #{buyOrderModifyApplyId,jdbcType=INTEGER}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from T_BUYORDER_MODIFY_APPLY_GOODS
        where  BUYORDER_MODIFY_APPLY_GOODS_ID = #{buyorderModifyApplyGoodsId,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods" useGeneratedKeys="true">
        insert into T_BUYORDER_MODIFY_APPLY_GOODS
        ( BUYORDER_MODIFY_APPLY_GOODS_ID,BUYORDER_MODIFY_APPLY_ID,BUYORDER_GOODS_ID
        ,INSIDE_COMMENTS,OLD_INSIDE_COMMENTS,OLD_SEND_GOODS_TIME
        ,SEND_GOODS_TIME,OLD_RECEIVE_GOODS_TIME,RECEIVE_GOODS_TIME
        ,OLD_PRICE,PRICE)
        values (#{buyorderModifyApplyGoodsId,jdbcType=INTEGER},#{buyorderModifyApplyId,jdbcType=INTEGER},#{buyorderGoodsId,jdbcType=INTEGER}
        ,#{insideComments,jdbcType=VARCHAR},#{oldInsideComments,jdbcType=VARCHAR},#{oldSendGoodsTime,jdbcType=BIGINT}
        ,#{sendGoodsTime,jdbcType=BIGINT},#{oldReceiveGoodsTime,jdbcType=BIGINT},#{receiveGoodsTime,jdbcType=BIGINT}
        ,#{oldPrice,jdbcType=DECIMAL},#{price,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods" useGeneratedKeys="true">
        insert into T_BUYORDER_MODIFY_APPLY_GOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="buyorderModifyApplyGoodsId != null">BUYORDER_MODIFY_APPLY_GOODS_ID,</if>
                <if test="buyorderModifyApplyId != null">BUYORDER_MODIFY_APPLY_ID,</if>
                <if test="buyorderGoodsId != null">BUYORDER_GOODS_ID,</if>
                <if test="insideComments != null">INSIDE_COMMENTS,</if>
                <if test="oldInsideComments != null">OLD_INSIDE_COMMENTS,</if>
                <if test="oldSendGoodsTime != null">OLD_SEND_GOODS_TIME,</if>
                <if test="sendGoodsTime != null">SEND_GOODS_TIME,</if>
                <if test="oldReceiveGoodsTime != null">OLD_RECEIVE_GOODS_TIME,</if>
                <if test="receiveGoodsTime != null">RECEIVE_GOODS_TIME,</if>
                <if test="oldPrice != null">OLD_PRICE,</if>
                <if test="price != null">PRICE,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="buyorderModifyApplyGoodsId != null"> #{buyorderModifyApplyGoodsId,jdbcType=INTEGER},</if>
                <if test="buyorderModifyApplyId != null"> #{buyorderModifyApplyId,jdbcType=INTEGER},</if>
                <if test="buyorderGoodsId != null"> #{buyorderGoodsId,jdbcType=INTEGER},</if>
                <if test="insideComments != null"> #{insideComments,jdbcType=VARCHAR},</if>
                <if test="oldInsideComments != null"> #{oldInsideComments,jdbcType=VARCHAR},</if>
                <if test="oldSendGoodsTime != null"> #{oldSendGoodsTime,jdbcType=BIGINT},</if>
                <if test="sendGoodsTime != null"> #{sendGoodsTime,jdbcType=BIGINT},</if>
                <if test="oldReceiveGoodsTime != null"> #{oldReceiveGoodsTime,jdbcType=BIGINT},</if>
                <if test="receiveGoodsTime != null"> #{receiveGoodsTime,jdbcType=BIGINT},</if>
                <if test="oldPrice != null"> #{oldPrice,jdbcType=DECIMAL},</if>
                <if test="price != null"> #{price,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods">
        update T_BUYORDER_MODIFY_APPLY_GOODS
        <set>
                <if test="buyorderModifyApplyId != null">
                    BUYORDER_MODIFY_APPLY_ID = #{buyorderModifyApplyId,jdbcType=INTEGER},
                </if>
                <if test="buyorderGoodsId != null">
                    BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER},
                </if>
                <if test="insideComments != null">
                    INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
                </if>
                <if test="oldInsideComments != null">
                    OLD_INSIDE_COMMENTS = #{oldInsideComments,jdbcType=VARCHAR},
                </if>
                <if test="oldSendGoodsTime != null">
                    OLD_SEND_GOODS_TIME = #{oldSendGoodsTime,jdbcType=BIGINT},
                </if>
                <if test="sendGoodsTime != null">
                    SEND_GOODS_TIME = #{sendGoodsTime,jdbcType=BIGINT},
                </if>
                <if test="oldReceiveGoodsTime != null">
                    OLD_RECEIVE_GOODS_TIME = #{oldReceiveGoodsTime,jdbcType=BIGINT},
                </if>
                <if test="receiveGoodsTime != null">
                    RECEIVE_GOODS_TIME = #{receiveGoodsTime,jdbcType=BIGINT},
                </if>
                <if test="oldPrice != null">
                    OLD_PRICE = #{oldPrice,jdbcType=DECIMAL},
                </if>
                <if test="price != null">
                    PRICE = #{price,jdbcType=DECIMAL},
                </if>
        </set>
        where   BUYORDER_MODIFY_APPLY_GOODS_ID = #{buyorderModifyApplyGoodsId,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.dto.BuyorderModifyApplyGoods">
        update T_BUYORDER_MODIFY_APPLY_GOODS
        set
            BUYORDER_MODIFY_APPLY_ID =  #{buyorderModifyApplyId,jdbcType=INTEGER},
            BUYORDER_GOODS_ID =  #{buyorderGoodsId,jdbcType=INTEGER},
            INSIDE_COMMENTS =  #{insideComments,jdbcType=VARCHAR},
            OLD_INSIDE_COMMENTS =  #{oldInsideComments,jdbcType=VARCHAR},
            OLD_SEND_GOODS_TIME =  #{oldSendGoodsTime,jdbcType=BIGINT},
            SEND_GOODS_TIME =  #{sendGoodsTime,jdbcType=BIGINT},
            OLD_RECEIVE_GOODS_TIME =  #{oldReceiveGoodsTime,jdbcType=BIGINT},
            RECEIVE_GOODS_TIME =  #{receiveGoodsTime,jdbcType=BIGINT},
            OLD_PRICE =  #{oldPrice,jdbcType=DECIMAL},
            PRICE =  #{price,jdbcType=DECIMAL}
        where   BUYORDER_MODIFY_APPLY_GOODS_ID = #{buyorderModifyApplyGoodsId,jdbcType=INTEGER}
    </update>


    <insert id="batchInsertSelective" parameterType="com.vedeng.order.model.BuyorderModifyApplyGoods" >
        insert into T_BUYORDER_MODIFY_APPLY_GOODS ( BUYORDER_MODIFY_APPLY_ID,
        BUYORDER_GOODS_ID, INSIDE_COMMENTS, OLD_INSIDE_COMMENTS
        ,OLD_SEND_GOODS_TIME,SEND_GOODS_TIME,OLD_RECEIVE_GOODS_TIME,RECEIVE_GOODS_TIME,PRICE,OLD_PRICE,
        OLD_DELIVERY_CYCLE,DELIVERY_CYCLE, IS_NEW,IS_HAVE_AUTH,OLD_IS_HAVE_AUTH,REFER_PRICE,OLD_REFER_PRICE)
        values
        <foreach collection ="records" item="bmag" index= "index" separator =",">
            (#{bmag.buyorderModifyApplyId,jdbcType=INTEGER}, #{bmag.buyorderGoodsId,jdbcType=INTEGER}, #{bmag.insideComments,jdbcType=VARCHAR}, #{bmag.oldInsideComments,jdbcType=VARCHAR}
            ,#{bmag.oldSendGoodsTime,jdbcType=BIGINT},#{bmag.sendGoodsTime,jdbcType=BIGINT}
            ,#{bmag.oldReceiveGoodsTime,jdbcType=BIGINT},#{bmag.receiveGoodsTime,jdbcType=BIGINT},#{bmag.price,jdbcType=DECIMAL},#{bmag.oldPrice,jdbcType=DECIMAL},
            #{bmag.oldDeliveryCycle,jdbcType=VARCHAR}, #{bmag.deliveryCycle,jdbcType=VARCHAR}, #{bmag.isNew,jdbcType=INTEGER}, #{bmag.isHaveAuth,jdbcType=INTEGER}, #{bmag.oldIsHaveAuth,jdbcType=INTEGER}
            ,#{bmag.referPrice,jdbcType=DECIMAL},#{bmag.oldReferPrice,jdbcType=DECIMAL})
        </foreach >
    </insert>


</mapper>
