package com.vedeng.erp.finance.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity;
import com.vedeng.erp.finance.dto.SettlementNoticeRecordDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * 财务结款通知记录转换器
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface SettlementNoticeRecordConvertor extends BaseMapStruct<SettlementNoticeRecordEntity, SettlementNoticeRecordDto> {
}
