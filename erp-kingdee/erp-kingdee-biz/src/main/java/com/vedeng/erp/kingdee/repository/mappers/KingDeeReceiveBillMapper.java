package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;

import java.util.List;

public interface KingDeeReceiveBillMapper {
    /**
     * delete by primary key
     * @param kingDeeReceiveBillId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeeReceiveBillId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeReceiveBillEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeReceiveBillEntity record);

    /**
     * select by primary key
     * @param kingDeeReceiveBillId primary key
     * @return object by primary key
     */
    KingDeeReceiveBillEntity selectByPrimaryKey(Integer kingDeeReceiveBillId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeReceiveBillEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeReceiveBillEntity record);

    /**
     * 根据是否推送金蝶附件状态查询erp银行流水id
     * <AUTHOR>
     * @return
     */
    List<KingDeeReceiveBillEntity> queryErpBankBillIdByType(Integer fileIsPush);

    /**
     * 更新回单推送状态
     * <AUTHOR>
     * @param kingDeeReceiveBillId
     * @return
     */
    int updatePushStatus(Integer kingDeeReceiveBillId);
}