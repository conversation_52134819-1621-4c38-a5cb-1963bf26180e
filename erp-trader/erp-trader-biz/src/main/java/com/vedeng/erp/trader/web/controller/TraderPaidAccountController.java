package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * 供应商已交易
 *
 * <AUTHOR>
 * @since 2022-07-12 15:44:47
 */
@Controller
@RequestMapping("/traderPaidAccount")
@Slf4j
public class TraderPaidAccountController extends BaseController {


    @RequestMapping(value = "/upLoadFile")
    public String upLoadFile() {
        return view();
    }

}

