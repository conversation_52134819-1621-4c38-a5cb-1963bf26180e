$(function(){
    var zone = $("#updateTerminalInfo").find("#zone :selected").val();
    if (zone == 1) {
        $("#updateTerminalInfo").find("#province").val(0);
    }
    $("#paymentType").change(function(){
        checkLogin();
        $("form :input").parents('li').find('.warning').remove();
        var num = $(this).val();
        if($(this).val()=="419"){
            $("#accountPeriodLi").hide();
            $("#billPeriodSettlementTypeLi").hide();
            $("#retentionMoneyLi").hide();
            //$("#retainageLi").hide();
        }else if($(this).val()=="424"){
            $("#accountPeriodLi").show();
            $("#billPeriodSettlementTypeLi").show();
            $("#retentionMoneyLi").show();
            //$("#retainageLi").show();
        }else{
            $("#accountPeriodLi").show();
            $("#billPeriodSettlementTypeLi").show();
            $("#retentionMoneyLi").show();
            //$("#retainageLi").hide();
        }

        $("#prepaidAmount").val('0.00');$("#accountPeriodAmount").val('0.00');
        $("#retainageAmount").val('0.00');


        //modify by tomcat.Hui ******** VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 start
        var totleMoney= $("#goodsTotleMoney").val();
        if ($("#goodsOrderType").val() == "5" || $("#goodsOrderType").val() == "1"){
            totleMoney = $("#goodsTotleAmount").val();
        }
        //modify by tomcat.Hui ******** VDERP-1294 编辑订单金额错误  增加隐藏域，用于jQuery下拉框联动 end

        switch (num) {
            case "419":
                $("#prepaidAmount").val(Number(totleMoney).toFixed(2));
                $("#prepaidAmount").attr("readonly",true);
                break;
            case "420":
                $("#prepaidAmount").val((Number(totleMoney)*0.8).toFixed(2));
                $("#accountPeriodAmount").val(((Number(totleMoney)-(Number($("#prepaidAmount").val()))).toFixed(2)));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "421":
                $("#prepaidAmount").val((Number(totleMoney)*0.5).toFixed(2));
                $("#accountPeriodAmount").val(((Number(totleMoney)-(Number($("#prepaidAmount").val()))).toFixed(2)));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "422":
                $("#prepaidAmount").val((Number(totleMoney)*0.3).toFixed(2));
                $("#accountPeriodAmount").val(((Number(totleMoney)-(Number($("#prepaidAmount").val()))).toFixed(2)));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "423":
                $("#prepaidAmount").removeAttr("readonly");
                $("#accountPeriodAmount").removeAttr("readonly");
                $("#prepaidAmount").val(0);
                $("#accountPeriodAmount").val(Number(totleMoney).toFixed(2));
                $("#prepaidAmount").attr("readonly",true);
                $("#accountPeriodAmount").attr("readonly",true);
                break;
            case "424":
                $("#prepaidAmount").removeAttr("readonly");
                $("#accountPeriodAmount").removeAttr("readonly");
                //$("#prepaidAmount").val('');$("#accountPeriodAmount").val('');
                break;
            default:
        }
        //付款计划改变时刷新余款和质保金
        $('#retentionMoney').val(0);
        $('#retentionMoneyFont').html(0);
        $('#retentionMoneySpan').hide();
        $('#spareMoneyLab').html(parseFloat($('#accountPeriodAmount').val()).toFixed(2));
    })
    if($('.content2 .payplan .tips-all select').val()==1){
        $('.tips-error').show();
    }
    $('.content2 .payplan .tips-all select').change(function () {
        if($(this).val() === '1'){
            $('.tips-error').show();
        }else{
            $('.tips-error').hide();
        }
    })

    $("#paymentType").trigger('change');

    //合并问题  自定义初始化付款计划
    $('#retentionMoney').val($('#retentionMoneyFlag').val());
    if ($('#paymentType').val() == 424){
        $('#prepaidAmount').val($('#prepaidAmountFlag').val())
        $('#accountPeriodAmount').val($('#accountPeriodAmountFlag').val())
    }
    //先款后货无质保金；2.质保金为0无质保金期限；
    if ($('#paymentType').val() == 419) {
        $('#retentionMoneyLi').hide();
    } else {
        if (parseFloat($('#retentionMoney').val()) == 0){
            $('#retentionMoneySpan').hide();
        } else {
            $('#retentionMoneySpan').show();
            $('#retentionMoneyFont').html($('#retentionMoneyFlag').val());
            $('#spareMoneyLab').html((parseFloat($('#accountPeriodAmount').val()) - parseFloat($('#retentionMoneyFlag').val())).toFixed(2));
        }
    }

    //票货同行初始化
    // VDERP-15759数电发票 去除 寄送/不寄送的页面展示联动
    if ($("input[name='isSendInvoice']:checked").val() == 0){
        var orderType = $("#orderType").val();
        $('#isSameAddressLi').hide();
        if(orderType != 7 && orderType != 8){
            $('#invoiceCustomerLi').hide()
        }
        // $('#traderContactLi').hide();
        // $('#invoiceTraderAddressLi').hide();
        // $('#invoiceTraderContactLi').hide();
        // $('#invoiceTraderContactMobileLi').hide();
        $('#invoiceEmailLi').hide();
    }

    /**
     * “发票寄送节点”，该按钮显示的前提是：
     （1）“发票是否寄送”选择“寄送”
     （2）“货票地址是否相同”选择“相同”
     */
    if ($("input[name='isSendInvoice']:checked").val() != 1 || $("input[name='isSameAddress']:checked").val() != 1){
        $('#invoiceSendNodeLi').hide();
    }

    deliveryClaimChange();
    isSameAddressChecked($("input[name='isSameAddress']:checked").val());

    $("#prepaidAmount").mouseleave(function (){
        var num = $("#paymentType").val();
        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if(num=="424") {//自定义
            var prepaidAmount = $("#prepaidAmount").val();
            if (prepaidAmount == "") {
                warnTips("prepaidAmountError", "预付金额不允许为空");//文本框ID和提示用语
                return false;
            }
            else if (prepaidAmount < 0) {
                warnTips("prepaidAmountError", "预付金额不允许小于0");//文本框ID和提示用语
                return false;
            }
            else if (prepaidAmount == 0 || prepaidAmount == 0.0 || prepaidAmount == 0.00) {
                warnTips("prepaidAmountError", "请选择付款方式：“先货后款，预付0%”");//文本框ID和提示用语
                return false;
            }
            else if (prepaidAmount.length > 12) {
                warnTips("prepaidAmountError", "预付金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }
            else if (prepaidAmount != "") {
                if (!reg.test(prepaidAmount)) {
                    warnTips("prepaidAmountError", "预付金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }else {
                    warnTips("prepaidAmountError", "");//文本框ID和提示用语
                }
            }
        }
    })
})
function checkprepaidAmount(){
    var num = $("#paymentType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if(num=="424") {//自定义
        var prepaidAmount = $("#prepaidAmount").val();
        if (prepaidAmount == "") {
            warnTips("prepaidAmountError", "预付金额不允许为空");//文本框ID和提示用语
            return false;
        }
        else if (prepaidAmount < 0) {
            warnTips("prepaidAmountError", "预付金额不允许小于0");//文本框ID和提示用语
            return false;
        }
        else if (prepaidAmount == 0 || prepaidAmount == 0.0 || prepaidAmount == 0.00) {
            warnTips("prepaidAmountError", "请选择付款方式：“先货后款，预付0%”");//文本框ID和提示用语
            return false;
        }
        else if (prepaidAmount.length > 12) {
            warnTips("prepaidAmountError", "预付金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }
        else if (prepaidAmount != "") {
            if (!reg.test(prepaidAmount)) {
                warnTips("prepaidAmountError", "预付金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }else {
                warnTips("prepaidAmountError", "");//文本框ID和提示用语
            }
        }
    }
}
function changeIsPrintout() {
    var isPrint = $("#is_print").val();
    var isScientificDept = $("#is_scientificDept").val();
    var isYxgOrgFlag=$("#isYxgOrgFlag").val();
    if (isPrint == 2){
        //不打印随货出库单
        $("#is_printout").val(0);
        if ($("#is_price").length == 1){
            $("#print_price").remove();
        }
    } else if (isPrint == 1){
        //按最新需求-删除
        /*//判断打印出库单是否带价格
        if (isScientificDept == "true"){
            $("#is_printout").val(3);
        } else {*/
            if ($("#is_price").length == 0){
                $("#print_out_order").append(" <div id =\"print_price\" style=\"display: inline-block\">\n" +
                    "                                            <select  class=\"input-middle\" id='is_price' name = \"isPrintout\" onchange='changeIsPrice()' >\n" +
                    "                                                <option value=\"0\">请选择</option>\n" +
                    "                                                <option value=\"1\">含价格</option>\n" +
                    "                                                <option value=\"2\">不含价格</option>\n" +
                    "                                            </select>\n" +

                    "                                        </div>");
                $("#is_printout").val(-2);
            }
        /*}*/
    } else if (isPrint == -1){
        $("#is_printout").val(-1);
        if ($("#is_price").length == 1){
            $("#print_price").remove();
        }
    }
}

function changeIsPrice() {
    var isPrice = $("#is_price").val();
    if (isPrice == 1){
        $("#is_printout").val(1);
    } else if (isPrice == 2){
        $("#is_printout").val(2);
    } else if (isPrice == 0){
        $("#is_printout").val(-2);
    }
}

/**
 * 是否寄送发票信息
 *
 * @param orderType
 * @param sendFlag
 */
function isSendInvoiceChecked(orderType, sendFlag) {
    if(orderType == 8 || orderType == 7 || orderType == 5 || orderType == 9){
        if(sendFlag == 0){
            $('#isSameAddressLi').show();
            // $('#traderContactLi').hide();
            // $('#invoiceTraderAddressLi').hide();
            // $('#invoiceTraderContactLi').hide();
            // $('#invoiceTraderContactMobileLi').hide();
            $('#invoiceSendNodeLi').hide();
            $('#invoiceEmailPart').hide();
            // $("#syncInvoiceAddress").hide();
            // $("#chooseInvoicePart").show();
        }else {
            $('#isSameAddressLi').show();
            // $('#traderContactLi').show();
            // $('#invoiceTraderAddressLi').show();
            // $('#invoiceTraderContactLi').show();
            // $('#invoiceTraderContactMobileLi').show();
            $('#invoiceSendNodeLi').show();
            $('#invoiceEmailPart').show();
            // $("#syncInvoiceAddress").show();
            // $("#chooseInvoicePart").hide();
        }
    }else {
        if (sendFlag == 0){
            $('#isSameAddressLi').hide();
            $('#invoiceCustomerLi').hide();
            // $('#traderContactLi').hide();
            // $('#invoiceTraderAddressLi').hide();
            // $('#invoiceTraderContactLi').hide();
            // $('#invoiceTraderContactMobileLi').hide();
            $('#invoiceSendNodeLi').hide();
            $('#invoiceEmailPart').hide();

            // if (orderType == 9 || orderType ==5) {
            //     $("#syncInvoiceAddress").hide();
            //     $("#chooseInvoicePart").show();
            // }

            $("#invoiceTraderContactMobile_5").removeAttr("disabled");
            $("#invoiceTraderAddressId-province").removeAttr("disabled");
            $("#invoiceTraderAddressId-city").removeAttr("disabled");
            $("#invoiceTraderAddressId-zone").removeAttr("disabled");
            $("#invoiceTraderContactName_5").removeAttr("disabled");
            $("#invoiceTraderAddress_5").removeAttr("disabled");

        } else {
            $('#isSameAddressLi').hide();
            $('#invoiceCustomerLi').show();
            // $('#traderContactLi').show();
            // $('#invoiceTraderAddressLi').show();
            // $('#invoiceTraderContactLi').show();
            // $('#invoiceTraderContactMobileLi').show();
            $('#invoiceSendNodeLi').hide();
            $('#invoiceEmailPart').show();

            // if (orderType == 9 || orderType ==5) {
            //     $("#syncInvoiceAddress").show();
            //     $("#chooseInvoicePart").hide();
            // }

            // 寄送&&货票同行，不可改
            if ($("input[name='isSameAddress']:checked").val() == 1) {
                $("#invoiceTraderContactMobile_5").attr("disabled","disabled");
                $("#invoiceTraderAddressId-province").attr("disabled","disabled");
                $("#invoiceTraderAddressId-city").attr("disabled","disabled");
                $("#invoiceTraderAddressId-zone").attr("disabled","disabled");
                $("#invoiceTraderContactName_5").attr("disabled","disabled");
                $("#invoiceTraderAddress_5").attr("disabled","disabled");
            }
        }
    }
    // $("#invoiceType").removeAttr("disabled");
    // $("#invoiceMethod").removeAttr("disabled");
    checkInvoiceGoodsStatus();

    // 不寄送时，相当于货票不同行
    if (sendFlag == 0) {
        $('input[name="isSameAddress"][value="0"]').prop('checked', true);
        isSameAddressChecked(0);
    }
}

function isDelayInvoiceChecked() {
    checkLogin();
    if(typeof($("input[name='isDelayInvoiceCheckbox']:checked").val()) == "undefined") {
        $("input[name='isDelayInvoice']").val(0);
    } else {
        $("input[name='isDelayInvoice']").val(1);
    }
}

function updatePayment(obj){
    checkLogin();
    if($(obj).val()=="419"){
        $("#accountPeriodLi").hide();
        $("#billPeriodSettlementTypeLi").hide();
        $("#retentionMoneyLi").hide();
        //$("#retainageLi").hide();
        $('#retentionMoneySpan').hide();
    }else if($(obj).val()=="424"){
        $("#accountPeriodLi").show();
        $("#billPeriodSettlementTypeLi").show();
        $("#retentionMoneyLi").show();
        //$("#retainageLi").show();
        $('#retentionMoneySpan').show();
    }else{
        $("#accountPeriodLi").show();
        $("#billPeriodSettlementTypeLi").show();
        $("#retentionMoneyLi").show();
        //$("#retainageLi").hide();
        $('#retentionMoneySpan').show();
    }
}

function updateInvoiceType(obj){
    checkLogin();
    if($(obj).val()=="681" || $(obj).val()=="971"){
        if($("input:checkbox[name='isSendInvoiceCheckbox']").is(":checked") && $("input[name='isSendInvoice']").val() == "0"){
            $("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="4">自动数电发票</option>');
        }else{
            $("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="3">自动电子发票</option><option value="4">自动数电发票</option>');
        }
    }else if($(obj).val()=="682" || $(obj).val()=="972"){
        $("select[name='invoiceMethod']").html('<option value="0">请选择</option><option value="1">手动纸质开票</option><option value="2">自动纸质开票</option><option value="4">自动数电发票</option>');
    }else {
        $("select[name='invoiceMethod']").html('<option value="0">请选择</option>');
    }
}

function logisticsCheck() {
    checkLogin();
    $("#logisticsCollection").val($("#logisticsCheckBox").is(":checked")==true?1:0);
}

function delSaleorderGoods(saleorderGoodsId, saleorderId, skuNo, scene){
    checkLogin();
    layer.confirm("您是否确认该操作？", {
        btn: ['确定','取消'] //按钮
    }, function(){
        $.ajax({
            type: "POST",
            url: "./delSaleorderGoodsById.do",
            data: {'saleorderGoodsId':saleorderGoodsId,'saleorderId':saleorderId,'isDelete':1},
            dataType:'json',
            success: function(data){
                // layer.alert(data.message,{
                // 	closeBtn: 0,
                // 	btn: ['确定'] //按钮
                // }, function(){
                // 	window.location.reload();
                // });
                if (data.code === 0) {
                    deleteInsideComments(saleorderGoodsId, saleorderId, skuNo, scene);
                }else {
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }, function(){
    });
}

/**
 * 删除内部备注
 * @param type
 * @returns {boolean}
 */
function deleteInsideComments(saleorderGoodsId, saleorderId, skuNo, scene) {
    $.ajax({
        type: "POST",
        url: "/order/saleorder/deleteInsideComment.do",
        data: {'detailId':saleorderGoodsId,'relationId':saleorderId,'skuNo':skuNo,'scene':0},
        dataType:'json',
        success: function(data){
            layer.alert(data.message,{
                closeBtn: 0,
                btn: ['确定'] //按钮
            }, function(){
                window.location.reload();
            });
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

// 判断是否为正整数
function checkNumber(theObj) {
    return new RegExp('^[1-9]\\d*$').test(theObj);
}

function editSubmit(type){
    checkLogin();
    var $form = $("#editForm");
    $("form :input").parents('li').find('.warning').remove();

    var orderType = Number(type);

    // 只有7/8时，才需要校验 收票客户的select不为空
    if (orderType == 8 || orderType == 7) {
        var selectedInvoiceTraderId = $('#take_trader_2').val();
        if (selectedInvoiceTraderId == 0) {
            warnTips("invoiceTraderIdMsg","收票客户不能为空");
            return false;
        }
    }

    // 不寄送
    var isSendInvoice = $("input[name='isSendInvoice']:checked").val();
    if(orderType == 5 || orderType == 8 || orderType == 9 || orderType == 7){
        //耗材，医械购线上线下，集采都需校验票货同行的必填项
        //寄送发票时才对票货地址是否相同做校验
        if ($("input[name='isSendInvoice']").val() == 1){
            if ($("input[name='isSameAddress']:checked").val() == undefined || $("input[name='isSameAddress']:checked").val() == null) {
                warnTips("isSameAddressMsg","票货是否同行不能为空");
                return false;
            }
        }
        /**
         * “发票寄送节点”，该按钮显示的前提是：
         （1）“发票是否寄送”选择“寄送”
         （2）“货票地址是否相同”选择“相同”
         */
        if ($("input[name='isSendInvoice']").val() == 1 && $("input[name='isSameAddress']:checked").val() == 1){
            if ($("input[name='invoiceSendNode']:checked").val() == undefined || $("input[name='invoiceSendNode']:checked").val() == null) {
                warnTips("invoiceSendNodeMsg","发票寄送节点不能为空");
                return false;
            }
        }
    }
    var deliveryTypeSelect = $("#deliveryTypeSelect").val();
    if(deliveryTypeSelect == 0){
        warnTips("deliveryTypeSelectMsg","发货方式不能为空");
        return false;
    }
    if(orderType != 5) {
        //非耗材订单校验
        if ($("select[name='traderContactId']").val() == 0) {
            warnTips("traderContactIdMsg","联系人不允许为空");
            return false;
        }
        var traderAddressId = $("select[name='traderAddressId']").val();
        if (traderAddressId == 0 || traderAddressId == null || traderAddressId == undefined) {
            warnTips("traderAddressIdMsg","联系地址不允许为空");
            return false;
        }

        if ($("select[name='takeTraderContactId']").val() == 0) {
            warnTips("takeTraderContactIdMsg","收货联系人不允许为空");
            return false;
        }

        var traderContactIdText = $("select[name='traderContactId']").find("option:selected").text();
        var traderContactIdTextArr = traderContactIdText.split('/');
        $("input[name='traderContactName']").val(traderContactIdTextArr[0]);
        $("input[name='traderContactTelephone']").val(traderContactIdTextArr[1]);
        $("input[name='traderContactMobile']").val(traderContactIdTextArr[2]);
        var traderAddressIdText = $("select[name='traderAddressId']").find("option:selected").text();
        var traderAddressIdTextArr = traderAddressIdText.split(/\/(.+)/, 2);  //只分割两份出来
        $("input[name='traderArea']").val(traderAddressIdTextArr[0]);
        $("input[name='traderAddress']").val(traderAddressIdTextArr[1]);
        var takeTraderContactIdText = $("select[name='takeTraderContactId']").find("option:selected").text();
        var takeTraderContactIdTextArr = takeTraderContactIdText.split('/');
        $("input[name='takeTraderContactName']").val(takeTraderContactIdTextArr[0]);
        $("input[name='takeTraderContactTelephone']").val(takeTraderContactIdTextArr[1]);
        $("input[name='takeTraderContactMobile']").val(takeTraderContactIdTextArr[2]);
        var takeTraderAddressIdText = $("select[name='takeTraderAddressId']").find("option:selected").text();
        var takeTraderAddressIdTextArr = takeTraderAddressIdText.split(/\/(.+)/, 2);  //只分割两份出来
        $("input[name='takeTraderArea']").val(takeTraderAddressIdTextArr[0]);
        $("input[name='takeTraderAddress']").val(takeTraderAddressIdTextArr[1]);

        var takeAreaLength = takeTraderAddressIdTextArr[0].split(' ').length;
        /*	if(takeAreaLength != 3) {
                warnTips("takeTraderAddressIdMsg","收货地址请补充完省市区");
                return false;
            }*/

        if ($("select[name='takeTraderAddressId']").val() == 0) {
            warnTips("takeTraderAddressIdMsg","收货地址不允许为空");
            return false;
        }

        var logisticsComments = $("#logisticsComments").val();
        if(logisticsComments.length>256){
            warnTips("logisticsComments","物流备注长度应该在0-256个字符之间");
            return false;
        }

        //非HC订单寄送发票时的校验
        // VDERP-15759 数电发票——不管是否寄送，都需要填写 收票联系人
        // if(isSendInvoice == 1 || isSendInvoice == '1'){
            var invoiceTraderContactId = $('#trader_contact_2 option:selected').val();
            if (invoiceTraderContactId == 0 || invoiceTraderContactId == undefined) {
                    warnTips("invoiceTraderContactIdMsg","收票联系人不允许为空");
                    return false;
            }

            var invoiceTraderContactIdText = $("select[name='invoiceTraderContactId']").find("option:selected").text();
            var invoiceTraderContactIdTextArr = invoiceTraderContactIdText.split('/');
            $("input[name='invoiceTraderContactName']").val(invoiceTraderContactIdTextArr[0]);
            $("input[name='invoiceTraderContactTelephone']").val(invoiceTraderContactIdTextArr[1]);
            $("input[name='invoiceTraderContactMobile']").val(invoiceTraderContactIdTextArr[2]);

            // var invoiceTraderAddressIdText = $("select[name='invoiceTraderAddressId']").find("option:selected").text();
            // var invoiceTraderAddressIdTextArr = invoiceTraderAddressIdText.split('/');
            // $("input[name='invoiceTraderArea']").val(invoiceTraderAddressIdTextArr[0]);
            // $("input[name='invoiceTraderAddress']").val(invoiceTraderAddressIdTextArr[1]);

            // select的id和name重复，导致取值错误，非常坑，全是复制粘贴的代码
            // var invoiceAreaId = $('#address_2 option:selected').val();
            // if(invoiceAreaId == 0) {
            //     warnTips("invoiceTraderAddressIdMsg","收票地址不允许为空");
            //     return false;
            // }
            //
            // var invoiceAreaLength = invoiceTraderAddressIdTextArr[0].split(' ').length;
            // if(invoiceAreaLength != 3) {
            //     warnTips("invoiceTraderAddressIdMsg","收票地址请补充完省市区");
            //     return false;
            // }
            //
            // if ($("select[name='invoiceTraderAddressId']").val() == 0) {
            //     warnTips("invoiceTraderAddressIdMsg","收票地址不允许为空");
            //     return false;
            // }

        // }

    } else {
        var traderContactName = $.trim($("#traderContactName_5").val());
        // 联系人
        if (undefined == traderContactName || traderContactName == '') {
            warnTips("5_traderContactNameMsg","联系人不允许为空");
            return false;
        }

        if (traderContactName.length > 40) {
            warnTips("5_traderContactNameMsg","联系人最大支持20个汉字");
            return false;
        }
        // 联系人手机号
        var traderContactMobile = $.trim($("#traderContactMobile_5").val());
        if (!/^\d{11}$/.test(traderContactMobile) || !/^1.*/.test(traderContactMobile)
            || /^10.*/.test(traderContactMobile)
            || /^11.*/.test(traderContactMobile)
            || /^12.*/.test(traderContactMobile)) {
            warnTips("5_traderContactMobileMsg","联系人手机号格式错误");
            return false;
        }
        // traderAddressId
        var traderAddressId = $("#traderAddressId").val();
        if(undefined == traderAddressId || traderAddressId == 0 || traderAddressId == '') {
            warnTips("5_traderAddressMsg","联系地址请补充完省市区");
            return false;
        }
        // 联系地址
        var traderAddress = $.trim($("#traderAddress_5").val());
        if (undefined == traderAddress || traderAddress == '') {
            warnTips("5_traderAddressMsg","联系地址不可为空");
            return false;
        }
        if (traderAddress.length > 200) {
            warnTips("5_traderAddressMsg","联系地址最大支持100个汉字");
            return false;
        }

        // 收货联系人
        var takeTraderContactName = $.trim($("#takeTraderContactName_5").val());
        if(undefined == takeTraderContactName || takeTraderContactName == '') {
            warnTips("5_takeTraderContactName","收货联系人不可为空");
            return false;
        }
        if(takeTraderContactName.length > 40) {
            warnTips("5_takeTraderContactName","收货联系人最大支持20个汉字");
            return false;
        }
        // 收货手机号
        var takeTraderContactMobile = $.trim($("#takeTraderContactMobile_5").val());
        if(!/^\d{11}$/.test(takeTraderContactMobile) || !/^1.*/.test(takeTraderContactMobile)
            || /^10.*/.test(takeTraderContactMobile)
            || /^11.*/.test(takeTraderContactMobile)
            || /^12.*/.test(takeTraderContactMobile)) {
            warnTips("5_takeTraderContactMobile","收货手机号格式错误");
            return false;
        }
        // 收货地址
        var takeTraderAddressId = $("#takeTraderAddressId").val();
        if(undefined == takeTraderAddressId || '' == takeTraderAddressId || 0 == takeTraderAddressId) {
            warnTips("5_takeTraderAddress","收货地址请补充完省市区");
            return false;
        }
        var takeTraderAddress = $.trim($("#takeTraderAddress_5").val());
        if(undefined == takeTraderAddress || '' == takeTraderAddress) {
            warnTips("5_takeTraderAddress","收货地址不可为空");
            return false;
        }

        if(takeTraderAddress.length > 200) {
            warnTips("5_takeTraderContactName","收货地址最大支持100个汉字");
            return false;
        }
        var logisticsComments = $("#logisticsComments").val();
        if(logisticsComments.length>256){
            warnTips("logisticsComments","物流备注长度应该在0-256个字符之间");
            return false;
        }

        // 是否寄送 -- 寄送则校验
        // VDERP-15759 数电发票——不管是否寄送，都需要填写 收票联系人
        // if(isSendInvoice == 1 || isSendInvoice == '1') {
            // 收票联系人
            var invoiceTraderContactName = $.trim($("#invoiceTraderContactName_5").val());
            if(undefined == invoiceTraderContactName || '' == invoiceTraderContactName) {
                warnTips("5_invoiceTraderContactName","收票联系人不可为空");
                return false;
            }
            if(invoiceTraderContactName.length > 40) {
                warnTips("5_invoiceTraderContactName","收票联系人最大支持20个汉字");
                return false;
            }

            var invoiceTraderContactMobile = $.trim($("#invoiceTraderContactMobile_5").val());
            if(!/^\d{11}$/.test(invoiceTraderContactMobile) || !/^1.*/.test(invoiceTraderContactMobile)
                || /^10.*/.test(invoiceTraderContactMobile)
                || /^11.*/.test(invoiceTraderContactMobile)
                || /^12.*/.test(invoiceTraderContactMobile)) {
                warnTips("5_invoiceTraderContactMobile","收票手机号格式错误");
                return false;
            }

            // var invoiceTraderAddressId = $("#invoiceTraderAddressId").val();
            // if(undefined == invoiceTraderAddressId || '' == invoiceTraderAddressId || 0 == invoiceTraderAddressId) {
            //     warnTips("5_invoiceTraderAddress","收票地址请补充完省市区");
            //     return false;
            // }
            // var invoiceTraderAddress = $.trim($("#invoiceTraderAddress_5").val());
            // if(undefined == invoiceTraderAddress || '' == invoiceTraderAddress) {
            //     warnTips("5_invoiceTraderAddress","收票地址不可为空");
            //     return false;
            // }
            //
            // if(invoiceTraderAddress.length > 200) {
            //     warnTips("5_invoiceTraderAddress","收票地址最大支持100个汉字");
            //     return false;
            // }

            var invoiceEmail = $.trim($("#invoiceEmail").val());
            if(invoiceEmail != '' && invoiceEmail != null && invoiceEmail != undefined &&
                !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(invoiceEmail)) {
                warnTips("invoiceEmail", "收票邮箱格式错误");
                return false;
            }
        // }

    }
    var belongPlatform = $("#belongPlatform").val();
    // 校验货期为正整数
    let skuDeliveryCycle_Tips = false;
    //针对科研购
    if (belongPlatform == 3){
        $("input[class^='skuDeliveryCycle']").each(function(){
            let deliveryCycle = $(this).val().split("天")[0];
            var re = /^[1-9]\d*-[1-9]\d*$/; ;
            var regExp = /^[1-9]\d*$/ ;
            if (deliveryCycle.length != 0 && (!re.test(deliveryCycle) && !regExp.test(deliveryCycle))){
                skuDeliveryCycle_Tips = true;
                warnTips($(this).attr('class'),"请填写货期");
                $('.J-goods-table-error').show();
                return false;
            }
            var cycle = deliveryCycle.split("-");
            if (parseInt(cycle[1]) < parseInt(cycle[0])){
                skuDeliveryCycle_Tips = true;
                warnTips($(this).attr('class'),"请填写货期");//文本框ID和提示用语
                $('.J-goods-table-error').show();
                return false;
            }
        });
    }else {
    $("input[class^='skuDeliveryCycle']").each(function(){
        let deliveryCycle = $(this).val().split("天");
        if (!checkNumber(deliveryCycle[0])){
            skuDeliveryCycle_Tips = true;
            warnTips($(this).attr('class'),"请填写货期");
            $('.J-goods-table-error').show();
            return false;
        }
    });
    }
    if(skuDeliveryCycle_Tips){
        return false;
    }

    if ($("select[name='invoiceType']").val() == 0) {
        warnTips("invoiceTypeMsg","发票类型不允许为空");
        return false;
    }
    // if ($("select[name='invoiceMethod']").val() == 0) {
    //     warnTips("invoiceMethodMsg","开票方式不允许为空");
    //     return false;
    // }
    //$form.find("#logisticsCollection").val($form.find("#logisticsCheckBox").is(":checked")==true?1:0);
    // var invoiceComments = $("#invoiceComments").val();
    // if(invoiceComments.length>256){
    //     warnTips("invoiceComments","开票备注长度应该在0-256个字符之间");
    //     return false;
    // }
    var invoiceEmail = $("#invoiceEmail").val();
    if(invoiceEmail != '' && invoiceEmail != null && invoiceEmail !== undefined &&
        !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(invoiceEmail)) {
        warnTips("invoiceEmail", "收票邮箱格式错误");
        return false;
    }
    if(invoiceEmail.length>64){
        warnTips("invoiceEmail","开票邮箱长度应该在0-64个字符之间");
        return false;
    }

    var totleMoney= $("#goodsTotleMoney").val();
    if ($("#goodsOrderType").val() == "5" || $("#goodsOrderType").val() == "1"){
        totleMoney = $("#goodsTotleAmount").val();
    }


    var num = $form.find("#paymentType").val();
    var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
    if(num=="424"){//自定义
        var retainageAmount = $("#retainageAmount").val();
        var prepaidAmount = $("#prepaidAmount").val();
        if (prepaidAmount ==""){
            warnTips("prepaidAmountError","预付金额不允许为空");//文本框ID和提示用语
            return false;
        }
        if (prepaidAmount <0){
            warnTips("prepaidAmountError","预付金额不允许小于0");//文本框ID和提示用语
            return false;
        }
        if (prepaidAmount ==0||prepaidAmount==0.0||prepaidAmount==0.00){
            warnTips("prepaidAmountError","请选择付款方式：“先货后款，预付0%”");//文本框ID和提示用语
            return false;
        }
        if(prepaidAmount.length > 12){
            warnTips("prepaidAmountError","预付金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }

        if(prepaidAmount!=""){
            if(!reg.test(prepaidAmount)){
                warnTips("prepaidAmountError","预付金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
        }



        var accountPeriodAmount = $("#accountPeriodAmount").val();
        if (accountPeriodAmount ==""){
            warnTips("prepaidAmountError","帐期支付金额不允许为空");//文本框ID和提示用语
            return false;
        }
        if (accountPeriodAmount<0){
            warnTips("accountPeriodAmountError","账期支付金额不允许小于0");//文本框ID和提示用语
            return false;
        }
        if(accountPeriodAmount.length > 12){
            warnTips("accountPeriodAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }
        if(accountPeriodAmount!=""){
            if(!reg.test(accountPeriodAmount)){
                warnTips("accountPeriodAmountError","帐期支付金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
        }

        if(prepaidAmount!="" && accountPeriodAmount!=""){
            var goodsTotleMoney = $("#goodsTotleMoney").val();
            if(Number(prepaidAmount) + Number(accountPeriodAmount)  != Number(goodsTotleMoney)){
                warnTips("error_div","支付金额总额与总金额不符，请验证！");//文本框ID和提示用语
                return false;
            }
        }
    } else if(num!="419"){//419先货后款，预付100%
        var accountPeriodAmount = $("#accountPeriodAmount").val();
        var retainageAmount = $("#retainageAmount").val();
        var prepaidAmount = $("#prepaidAmount").val();
        if(accountPeriodAmount.length > 12){
            warnTips("accountPeriodAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
            return false;
        }
        if(prepaidAmount!="" && accountPeriodAmount!=""){
            if((Number(prepaidAmount) + Number(accountPeriodAmount)).toFixed(2) != Number(totleMoney).toFixed(2)){
                warnTips("error_div","支付金额总额与总金额不符，请验证！");//文本框ID和提示用语
                return false;
            }
        }
        if(accountPeriodAmount!=""){
            if(!reg.test(accountPeriodAmount)){
                warnTips("accountPeriodAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                return false;
            }
        }else{
            warnTips("accountPeriodAmountError","账期支付金额必须大于0");//文本框ID和提示用语
            return false;
        }
    }

    var paymentComments = $("#paymentComments").val();
    if(paymentComments.length > 256){
        warnTips("paymentComments","收款备注长度应该在2-256个字符之间");//文本框ID和提示用语
        return false;
    }

    // var additionalClause = $("#additionalClause").val();
    // if(additionalClause.length > 256){
    //     warnTips("additionalClause","附加条款长度应该在0-256个字符之间");//文本框ID和提示用语
    //     return false;
    // }

    var comments = $("#comments").val();
    if(comments.length > 1024){
        warnTips("comments","内部备注长度应该在0-1024个字符之间");//文本框ID和提示用语
        return false;
    }

    // if ($("#updateTerminalInfo").find("#provinceId :selected").val() == 0) {
    //     warnTips("sales_area_msg_div","销售区域不允许为空");//文本框ID和提示用语
    //     return false;
    // }
    // if ($("#updateTerminalInfo").find("#cityId :selected").val() == 0) {
    //     warnTips("sales_area_msg_div","城市不允许为空");//文本框ID和提示用语
    //     return false;
    // }
    // if ($("#updateTerminalInfo").find("#areaId :selected").val() == 0) {
    //     warnTips("sales_area_msg_div","区域不允许为空");//文本框ID和提示用语
    //     return false;
    // }
    // if ($("#updateTerminalInfo").find("#terminalTraderNatureSelect :selected").val() == 0) {
    //     warnTips("terminalTraderNatureMsg","终端性质不允许为空");//文本框ID和提示用语
    //     return false;
    // }
    // var terminalLength = $(".J-terminal-list-body").find("tr").length || 0;
    // var tdEmpty = $(".J-terminal-list-body").find(".td-empty").length ||0;
    // if(tdEmpty >0 ){
    //     $('.J-terminal-empty').addClass("error");
    //     return false;
    // }
    // $('.J-terminal-empty').removeClass("error");

    if(!validTerminalTraderNature()) {
        return false;
    }

    // $('.J-terminal-empty').css("color","");

    //校验随货出款单不能为空
    var isPrintout = $("#is_printout").val();
    if (isPrintout == -1 || isPrintout == '') {
        warnTips("isPrintoutMsg", "是否打印随货出库单不能为空");
        return false;
    }
    if (isPrintout == -2) {
        warnTips("isPriceMsg", "随货出库单是否自带价格不能为空");
        return false;
    }
    if (isPrintout == 4) {
        warnTips("isPriceMsg", "随货出库单是否自带价格不能为空");
        return false;
    }

    // if ($('select[name="privinceId"]').val() == 0 ||  $('select[name="cityId"]').val() == 0 || $('select[name="areaId"]').val() == 0) {
    //     warnTips("sales_area_msg_div", "请完善终端销售区域");
    //     return false;
    // }
    //
    // if ($('select[name="privinceId"]').val() == 0 ||  $('select[name="cityId"]').val() == 0 || $('select[name="areaId"]').val() == 0) {
    //     warnTips("sales_area_msg_div", "请完善终端销售区域");
    //     return false;
    // }

    $('input[name="provinceId"]').val($('select[name="provinceId"]').val());
    $('input[name="cityId"]').val($('select[name="cityId"]').val());
    $('input[name="areaId"]').val($('select[name="areaId"]').val());

    var provinceName = $('#orderTerminal-province').find('option:selected').text();
    var cityName = $('#orderTerminal-city').find('option:selected').text();
    var areaName = $('#orderTerminal-area').find('option:selected').text();
    $('input[name="provinceName"]').val(provinceName == "请选择" ? "" : provinceName);
    $('input[name="cityName"]').val(cityName == "请选择" ? "" : cityName);
    $('input[name="areaName"]').val(areaName == "请选择" ? "" : areaName);

    if(orderType != 5) {

        /**
         * 非先款后货校验质保金相关内容
         */
        if ($('#paymentType').val() != 419){
            //质保金
            var retentionMoney = $('#retentionMoney').val();
            if( /^[0-9]+([.]{1}[0-9]{1,2})?$/.test(retentionMoney) ){
                //质保金金额
                var accountPeriodAmount = $('#accountPeriodAmount').val();
                if (parseFloat(retentionMoney) <= accountPeriodAmount){
                    var goodsTotleMoney = $('#goodsTotleMoney').val();
                    if (parseFloat(retentionMoney) <= goodsTotleMoney/2){
                        $('#retentionMoneyFont').html(retentionMoney);
                        delWarnTips('accountPeriodAmountError')
                    } else {
                        warnTips("accountPeriodAmountError","质保金不得超过合同总金额的50%");
                        return false;
                    }
                } else {
                    warnTips("accountPeriodAmountError","质保金不得超过账期支付金额");
                    return false;
                }
            } else {
                warnTips("accountPeriodAmountError","只可填写数字，最多保留两位小数");
                return false;
            }

            //存在质保金时校验质保金期限
            if (parseFloat($('#retentionMoney').val()) != 0){
                var retentionMoneyDay = $('#retentionMoneyDay').val();
                if(!(/(^[1-9]\d*$)/.test(retentionMoneyDay))){
                    warnTips('retentionMoneyDayError','只可填写正整数');
                    return false;
                }
            } else {
                $('#retentionMoneyDay').val(0);
            }
        }
    }

    if ($('#paymentType').val() == 419){
        $('#retentionMoney').val(0);
        $('#retentionMoneyDay').val(0);
        $('#periodDay').val(0);
        $('#retentionMoneyDay').val(0);
    } else {
        /**
         * 余款账期天数校验
         */
        var periodDay = $('#periodDay').val();
        if(!(/(^[1-9]\d*$)/.test(periodDay))){
            warnTips('retentionMoneyDayError','只可填写正整数');
            return false;
        }
    }

    var deliveryClaimSelect = $("#deliveryClaimSelect").val();
    var deliveryDelayTimeStr = $("#deliveryDelayTimeStr").val();
    if (deliveryClaimSelect == 1 && deliveryDelayTimeStr=="" ) {
        warnTips("deliveryDelayTimeStrMsg", "请填写等待截止日期");
        return false;
    }

    // 校验产品是否配置内部备注标签
    let specialSkuList = getSpecialGoodsList();
    let remarkError = 0
    $('#goodsTbody').children('tr').each(function(i, v){
        if (i === $('#goodsTbody').children('tr') - 1) {
            return false;
        }
        $(v).children('td').each(function(_i, _v){
            if ($(_v).hasClass('c-comments') && !$(_v).children('.customername').children('.customernameshow').html()) {
                let skuId = parseInt($(_v).attr('skuId'));
                let skuList = specialSkuList.filter(function (sku) {
                    return skuId === sku;
                })
                if (skuList.length > 0) {
                    return false;
                }
                remarkError++;
                $(_v).children('.no_remark_error').css('display','block');
            }
        })
    })
    if (remarkError > 0) {
        return false;
    }
    // 检验是否满足票货同行条件
    if ($("input[name='isSameAddress']:checked").val() == 1 && $("input[name='isSendInvoice']").val() ==1) {

        var reasonNo = isSameAddressSubmitChecked(type);

        if (reasonNo != ""){
            layer.open({
                type: 2,
                shadeClose: false, //点击遮罩关闭
                area: ["30%", "40%"],
                title: "提示",
                content: "/order/jc/returnTipsPage.do?reasonNo=" + reasonNo,
            });
            return false;
        }
    }

    // 集团客户下单范围校验
    // if ((orderType == 7 || orderType == 8 ) && !checkGoodsRangeForGroupCustomer()) {
    //     return false;
    // }

    //未添加销售商品不做校验
    if ($("#goodsTbody tr").length<=1) {
        //提交订单信息
        // $('#invoiceType').attr("disabled",false);
        // $('#invoiceMethod').attr("disabled",false);
        $form.submit();
        return;
    }
    if (orderType == 7 || orderType == 8){
        var text = $('#costomerLink').text();
        layer.confirm('推送的手机号：'+text+'-'+traderContactIdTextArr, {
                icon: 3,
                title: '订单待确认提醒',
                btn: ['确认', '取消'],
            },
            function(index, layero) {
                layer.close(index);
                //提交订单信息
                // $('#invoiceType').attr("disabled",false);
                // $('#invoiceMethod').attr("disabled",false);
                $form.submit();
            },
            function(index){
                layer.close(index);
            });
    }else {
        // $('#invoiceType').attr("disabled",false);
        // $('#invoiceMethod').attr("disabled",false);
        $form.submit();
    }
}
//校验票货同行条件
// 1.	发票是否寄送：寄送；
// 2.	开票方式：自动电子发票；
// 3.	订单中全部商品的发货方式为“普发”；
// 4.	订单不存在非“已关闭”状态的“销售订单退货”或“销售订单退票”的售后单；
// 5.	“货票主体”（客户）相同，并且“货票地址”相同；
// 6.	“票货是否同行”：票货同行；
function isSameAddressSubmitChecked(type){
    //没有满足的原因
    var reasonNo="";
    //订单Id
    var saleorderId = $("input[name='saleorderId']").val();

    //发票是否寄送
    var isSendInvoice =  $("input[name='isSendInvoice']").val();
    if(isSendInvoice != 1){
        reasonNo = reasonNo + "1";
    }
    //开票方式
    var invoiceMethod =  $("select[name='invoiceMethod']").val();
    if(invoiceMethod != 3 && invoiceMethod != 4){
        reasonNo = reasonNo + "2";
    }
    // if(type == 5){
    //     //线上订单
    //     var takeTraderName = $("#trader_name_1").val();
    //     var takeTraderAddressId = $("#takeTraderAddressId").val();
    //     var invoiceTraderName = $("#trader_name_2").val();
    //     var invoiceTraderAddressId = $("#invoiceTraderAddressId").val();
    //     var takeTraderAddress_5 = $.trim($("#takeTraderAddress_5").val());
    //     var invoiceTraderAddress_5 = $.trim($("#invoiceTraderAddress_5").val());
    //     if(takeTraderName != invoiceTraderName || takeTraderAddressId != invoiceTraderAddressId || takeTraderAddress_5 != invoiceTraderAddress_5){
    //         reasonNo = reasonNo + "5";
    //     }
    // }else{
    //     // 收货客户
    //     var takeTraderId =  $('#take_trader_1 option:selected').val();
    //     //收货地址
    //     var takeTraderAddressId =  $('select[name=takeTraderAddressId] > option:selected').val();
    //     // 收票客户
    //     var invoiceTraderId = $('#take_trader_2 option:selected').val();
    //     //收票地址
    //     var invoiceTraderAddressId = $("select[name='invoiceTraderAddressId']").val();
    //
    //     if(takeTraderId != invoiceTraderId || takeTraderAddressId != invoiceTraderAddressId){
    //         reasonNo = reasonNo + "5";
    //     }
    // }

    //票货是否同行
    var isSameAddress = $("input[name='isSameAddress']:checked").val();
    if(isSameAddress != 1){
        reasonNo = reasonNo + "6";
    }
    $.ajax
    ({
        type : "POST",
        url : page_url+"/order/jc/isSameAddressSubmitCheck.do",
        data :{'saleorderId':saleorderId},
        dataType : 'json',
        async: false,
        success : function(res) {
            reasonNo = reasonNo + res.data;
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

    return reasonNo;
}
/**
 * 页面产品操作前保存部分信息（操作前调用）
 * 客户信息、收货信息、收票信息
 * @param type
 */
function saveBeforeOperateSku(type) {

    // 客户信息
    let traderContactId;
    let traderContactName;
    let traderContactTelephone;
    let traderContactMobile;
    let traderAddressId;
    let traderArea;
    let traderAddress;
    let traderAreaId;

    // 终端信息
    let terminalName;
    let terminalTraderNature;
    let dwhTerminalId;
    let unifiedSocialCreditIdentifier;
    let organizationCode;
    let provinceName;
    let cityName;
    let areaName;
    let provinceId;
    let cityId;
    let areaId;


    // 收货信息
    let takeTraderId;
    let takeTraderName;
    let takeTraderContactId;
    let takeTraderContactName;
    let takeTraderContactTelephone;
    let takeTraderContactMobile;
    let takeTraderAddressId;
    let takeTraderArea;
    let takeTraderAreaId;
    let takeTraderAddress;
    let deliveryType;
    let deliveryClaim;
    let deliveryDelayTimeStr;
    let isPrintout;
    let logisticsId;
    let freightDescription;
    let logisticsComments;

    // 收票信息
    let invoiceTraderId;
    let invoiceTraderName;
    let isSendInvoice;
    let invoiceTraderContactId;
    let invoiceTraderContactName;
    let invoiceTraderContactTelephone;
    let invoiceTraderContactMobile;
    let invoiceTraderAddressId;
    let invoiceTraderArea;
    let invoiceTraderAreaId;
    let invoiceTraderAddress;
    let invoiceType;
    let invoiceMethod;
    let invoiceComments;
    let isDelayInvoice;

    //票货同行信息
    let isSameAddress;
    let invoiceSendNode;
    var orderType = $("#orderType").val();
    // 保存客户信息
    function saveCustomerInfoBeforeOperateSku(type) {

        if(type != 5){
            // 客户名称(默认)
            // 客户类型(默认)
            // 联系人
            if ($("select[name='traderContactId']").val() != 0) {

                traderContactId = $("select[name='traderContactId']").val();
                let traderContactIdText = $("select[name='traderContactId']").find("option:selected").text();
                let traderContactIdTextArr = traderContactIdText.split('/');
                traderContactName = traderContactIdTextArr[0];
                traderContactTelephone = traderContactIdTextArr[1];
                traderContactMobile = traderContactIdTextArr[2];
            }

            // 联系地址
            if ($("select[name='traderAddressId']").val() != 0) {

                traderAddressId = $("select[name='traderAddressId']").val();
                let traderAddressIdText = $("select[name='traderAddressId']").find("option:selected").text();
                let traderAddressIdTextArr = traderAddressIdText.split('/');
                traderArea = traderAddressIdTextArr[0];
                traderAddress = traderAddressIdTextArr[1];
            }
        }else {
            traderContactName = $("#traderContactName_5").val();
            traderContactMobile = $("#traderContactMobile_5").val();
            traderArea = $("#traderArea_5").val();
            traderAddress = $("#traderAddress_5").val();
            traderAreaId = $("#traderAddressId").val();
        }


        // provinceId = $('input[name="provinceId"]').val();
        // cityId = $('input[name="cityId"]').val();
        // areaId = $('input[name="areaId"]').val();
        //
        // provinceName = $('input[name="provinceName"]').val();
        // cityName = $('input[name="cityName"]').val();
        // areaName = $('input[name="areaName"]').val();

        provinceId = $('#orderTerminal-province option:selected').val();
        provinceName = provinceId == 0 ? "" : $('#orderTerminal-province option:selected').text();
        cityId = $('#orderTerminal-city option:selected').val();
        cityName = cityId == 0 ? "" : $('#orderTerminal-city option:selected').text();
        areaId = $('#orderTerminal-area option:selected').val();
        areaName = areaId == 0 ? "" : $('#orderTerminal-area option:selected').text();


        terminalName = $('input[name="terminalName"]').val();
        dwhTerminalId = $('input[name="dwhTerminalId"]').val();
        unifiedSocialCreditIdentifier = $('input[name="unifiedSocialCreditIdentifier"]').val();
        organizationCode = $('input[name="organizationCode"]').val();
        terminalTraderNature = $('#terminalTraderNatureSelect option:selected').val();
        debugger

    }

    // 保存收货信息
    function saveTakeTraderInfoBeforeOperateSku(type) {
        // 收货客户(默认)
        // 收货联系人
        if(type != 5){
            if ($("select[name='takeTraderContactId']").val() != 0) {

                takeTraderContactId = $("select[name = 'takeTraderContactId']").val();
                let takeTraderContactIdText = $("select[name='takeTraderContactId']").find("option:selected").text();
                let takeTraderContactIdTextArr = takeTraderContactIdText.split('/');
                takeTraderContactName = takeTraderContactIdTextArr[0];
                takeTraderContactTelephone = takeTraderContactIdTextArr[1];
                takeTraderContactMobile = takeTraderContactIdTextArr[2];
            }

            // 收货地址
            if ($("select[name='takeTraderAddressId']").val() != 0) {

                takeTraderAddressId = $("select[name='takeTraderAddressId']").val();
                let takeTraderAddressIdText = $("select[name='takeTraderAddressId']").find("option:selected").text();
                let takeTraderAddressIdTextArr = takeTraderAddressIdText.split('/');
                takeTraderArea = takeTraderAddressIdTextArr[0];
                takeTraderAddress = takeTraderAddressIdTextArr[1];
            }
        }else {
            takeTraderContactName = $("#takeTraderContactName_5").val();
            takeTraderContactMobile = $("#takeTraderContactMobile_5").val();
            takeTraderArea = $("#takeTraderArea_5").val();
            takeTraderAreaId = $("#takeTraderAddressId").val();
            takeTraderAddress = $("#takeTraderAddress_5").val();
        }
        if(type == 8 || type == 7){
            takeTraderId = $("#take_trader_1 option:selected").val();
            takeTraderName = $("#take_trader_1 option:selected").text();
        }
        // 发货方式
        deliveryType = $("select[name='deliveryType']").val();

        // 发货要求
        deliveryClaim = $("select[name='deliveryClaim']").val();
        // 等通知发货 - 等待截止日
        deliveryDelayTimeStr = $("input[name='deliveryDelayTimeStr']").val();

        // 随货出库单
        isPrintout = $("input[name='isPrintout']").val();

        // 指定物流公司
        logisticsId = $("select[name='logisticsId']").val();

        // 运费说明
        freightDescription = $("select[name='freightDescription']").val();

        // 物流备注
        logisticsComments = $("input[name='logisticsComments']").val();

    }

    // 保存收票信息
    function saveInvoiceInfoBeforeOperateSku(type) {

        // 发票是否寄送
        isSendInvoice = $("input[name='isSendInvoice']:checked").val();

        if(type != 5){
            // 收货客户 (默认)
            // 收票联系人
            if ($("select[name='invoiceTraderContactId']").val() != 0) {

                invoiceTraderContactId = $("select[name='invoiceTraderContactId']").val();
                let invoiceTraderContactIdText = $("select[name='invoiceTraderContactId']").find("option:selected").text();
                let invoiceTraderContactIdTextArr = invoiceTraderContactIdText.split('/');
                invoiceTraderContactName = invoiceTraderContactIdTextArr[0];
                invoiceTraderContactTelephone = invoiceTraderContactIdTextArr[1];
                invoiceTraderContactMobile = invoiceTraderContactIdTextArr[2];
            }

            // 收票地址
            if ($("select[name='invoiceTraderAddressId']").val() != 0) {

                invoiceTraderAddressId = $("select[name='invoiceTraderAddressId']").val();
                let invoiceTraderAddressIdText = $("select[name='invoiceTraderAddressId']").find("option:selected").text();
                let invoiceTraderAddressIdTextArr = invoiceTraderAddressIdText.split('/');
                invoiceTraderArea = invoiceTraderAddressIdTextArr[0];
                invoiceTraderAddress = invoiceTraderAddressIdTextArr[1];
            }
        }else {
            invoiceTraderContactName = $("#invoiceTraderContactName_5").val();
            invoiceTraderContactMobile = $("#invoiceTraderContactMobile_5").val();
            invoiceTraderArea = $("#invoiceTraderArea_5").val();
            invoiceTraderAreaId = $("#invoiceTraderAddressId").val();
            invoiceTraderAddress = $("#invoiceTraderAddress_5").val();
        }
        if(type == 5 || type == 8 || type == 9 || type == 7){
            //耗材，医械购线下，集采需保存票货同行信息
            invoiceSendNode = $("input[name='invoiceSendNode']:checked").val();
            isSameAddress = $("input[name='isSameAddress']:checked").val();
        }
        if(type == 8 || type == 7){
            invoiceTraderId = $("#take_trader_2 option:selected").val();
            invoiceTraderName = $("#take_trader_2 option:selected").text();
        }
        // 发票类型
        invoiceType = $("select[name='invoiceType']").val();

        // 开票方式
        invoiceMethod = $("select[name='invoiceMethod']").val();

        // 开票备注
        invoiceComments = $("input[name='invoiceComments']").val();

        // 暂缓开票
        isDelayInvoice = $("input[name='isDelayInvoice']").val();

    }

    saveCustomerInfoBeforeOperateSku(orderType);

    saveTakeTraderInfoBeforeOperateSku(orderType);

    saveInvoiceInfoBeforeOperateSku(orderType);

    function saveCustomerInfo(orderType) {

        $.ajax({
            url: page_url + '/orderstream/saleorder/saveEditOrderBeforeOperateSku.do',
            data: {
                "saleorderId": $("input[name='saleorderId']").val(),
                "traderContactId": traderContactId,
                "traderContactName": traderContactName,
                "traderContactTelephone": traderContactTelephone,
                "traderContactMobile": traderContactMobile,
                "traderAddressId": traderAddressId,
                "traderArea": traderArea,
                "traderAreaId": traderAreaId,
                "traderAddress": traderAddress,
                "takeTraderId": takeTraderId,
                "takeTraderName": takeTraderName,
                "takeTraderContactId": takeTraderContactId,
                "takeTraderContactName": takeTraderContactName,
                "takeTraderContactTelephone": takeTraderContactTelephone,
                "takeTraderContactMobile": takeTraderContactMobile,
                "takeTraderAddressId": takeTraderAddressId,
                "takeTraderArea": takeTraderArea,
                "takeTraderAreaId": takeTraderAreaId,
                "takeTraderAddress": takeTraderAddress,
                "deliveryType": deliveryType,
                "deliveryClaim": deliveryClaim,
                "deliveryDelayTimeStr": deliveryDelayTimeStr,
                "isPrintout": isPrintout,
                "logisticsId": logisticsId,
                "freightDescription": freightDescription,
                "logisticsComments": logisticsComments,
                "isSendInvoice": isSendInvoice,
                "invoiceTraderId": invoiceTraderId,
                "invoiceTraderName": invoiceTraderName,
                "invoiceTraderAddressId": invoiceTraderAddressId,
                "invoiceTraderContactName": invoiceTraderContactName,
                "invoiceTraderContactTelephone": invoiceTraderContactTelephone,
                "invoiceTraderContactMobile": invoiceTraderContactMobile,
                "invoiceTraderContactId": invoiceTraderContactId,
                "invoiceTraderArea": invoiceTraderArea,
                "invoiceTraderAreaId": invoiceTraderAreaId,
                "invoiceTraderAddress": invoiceTraderAddress,
                "invoiceType": invoiceType,
                "invoiceMethod": invoiceMethod,
                "invoiceComments": invoiceComments,
                "isDelayInvoice": isDelayInvoice,
                "invoiceSendNode": invoiceSendNode,
                "isSameAddress": isSameAddress
            },
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
            },
            error: function (data) {
            }
        });
    }

    saveCustomerInfo(type);

    function saveTerminal(orderType) {

        $.ajax({
            url: page_url + '/orderstream/saleorder/saveTerminal.do',
            data: {
                "businessId": $("input[name='saleorderId']").val(),
                "terminalName": terminalName,
                "terminalTraderNature": terminalTraderNature,
                "dwhTerminalId": dwhTerminalId,
                "unifiedSocialCreditIdentifier": unifiedSocialCreditIdentifier,
                "organizationCode": organizationCode,
                "provinceName": provinceName,
                "cityName": cityName,
                "areaName": areaName,
                "provinceId": provinceId,
                "cityId": cityId,
                "areaId": areaId
            },
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
            },
            error: function (data) {
            }
        });
    }

    //saveTerminal(type);
}

function searchTerminal(){
    checkLogin();
//	$("#updateTerminalInfo").find("#errorMes").removeClass("errorbor");
    delWarnTips("errorTxtMsg");

    var searchTraderName = $("#updateTerminalInfo").find("#searchTraderName").val()==undefined?"":$("#updateTerminalInfo").find("#searchTraderName").val();
    /*	if(searchTraderName==""){
            warnTips("errorMes","请输入终端信息");//文本框ID和提示用语
            return false;
        }*/
    $("#updateTerminalInfo").find("#terminalDiv").attr('layerParams','{"width":"70%","height":"80%","title":"报价终端","link":"'+ page_url+'/orderstream/saleorder/getTerminalList.do?searchTraderName=' + encodeURI(searchTraderName) + '&optType=addQuoteTerminal"}');
    $("#updateTerminalInfo").find("#terminalDiv").click();

}

function searchTerminalNew(){

    checkLogin();
    delWarnTips("errorTxtMsg");
    $("#terminal-info-div").attr("src",'./getAsyncTerminalList.do?searchTraderName='+$("#searchTraderName").val());
    $("#terminal-info-div").show();

}

// VDERP-15595
function searchOrderTerminal(){
    checkLogin();
    delWarnTips("errorTxtMsg");
    $("#updateTerminalInfo").find("#terminalDiv").attr('layerParams','{"width":"70%","height":"80%","title":"终端信息","link":"'+ page_url+'/order/terminal/dialog.do?scene=0&hasTerminalInfo=0"}');
    $("#updateTerminalInfo").find("#terminalDiv").click();
}


function repeatSearchTerminal(){
    checkLogin();
    $("#terminalName").val("");
    $("#terminalTraderNameDiv").html("");
    $("#quotePayMoneForm").find(".orderTerminal").each(function(i){
        $(this).val("");
    });
    $("#terminalNameDetail").hide();
    $("#terminalNameCheck").show();
    $("#orderTerminal-province").val("0");
    $("#orderTerminal-city").val("0");
    $("#orderTerminal-area").val("0");
}


function synTerminalTraderName(){
    $("#terminalTraderName").val($('#terminalTraderName1').val());
}

function agingSearchTerminal(){
    checkLogin();
    $("#searchTraderName").val("");
    $("#terminalTraderNameDiv").html("");
    $("#quotePayMoneForm").find(".terminal").each(function(i){
        $(this).val("");
    });
    $("#terminalNameDetail").hide();
    $("#terminalNameCheck").show();
}

function goodsCheckClick(obj){
    if($(obj).is(":checked")){
        var num = 0;
        $("input:checkbox[name='goodsCheckName']").each(function(i){
            if($(this).is(":checked")){
                num ++;
            }
        });
        if(num == $("input:checkbox[name='goodsCheckName']").length){
            $("input:checkbox[name='goodsCheckAllName']").prop("checked",true);
        }
    }else{
        $("input:checkbox[name='goodsCheckAllName']").prop("checked",false);
    }
}

function goodsCheckAllClick(obj){
    if($(obj).is(":checked")){
        $("input:checkbox[name='goodsCheckName']").each(function(i){
            $(this).prop("checked",true);
        });
    }else{
        $("input:checkbox[name='goodsCheckName']").each(function(i){
            $(this).prop("checked",false);
        });
    }
}



function updateSaleGoodsInit(saleorderId,scene){
    checkLogin();

    function extracted() {
        saveBeforeOperateSku();

        var saleorderGoodsIdArr = [];
        let isDirectPurchase = 0;
        let skuList = []
        $("input:checkbox[name='goodsCheckName']:checked").each(function (i) {
            saleorderGoodsIdArr.push($(this).val());
            console.log("isDirectPurchase", $(this).attr('isDirectPurchase'));
            if ($(this).attr('isDirectPurchase') == 1) {
                isDirectPurchase = 1;
            }
            skuList.push({
                skuId: $(this).attr('skuId'),
                skuNo: $(this).attr('skuNo'),
                skuName: $(this).attr('skuName')
            })
        });
        if (isDirectPurchase == 1) {
            layer.alert("选中的包含贝登商城&quot;促销&quot;商品，暂不支持修改直/普发类型。");
            return false;
        }
        if (saleorderGoodsIdArr.length == 0) {
            layer.alert("请选择要修改的商品！");
            return false;
        }
        if (!checkGoods(skuList, 0)) {
            return;
        }
        $("#saleGoodsDeliveryDirect").attr('layerParams', '{"width":"700px","height":"650px","title":"修改订单商品","link":"' + page_url + '/orderstream/saleorder/updateSaleGoodsInit.do?saleorderGoodsIdArr=' + saleorderGoodsIdArr + '&saleorderId=' + saleorderId + '&scene=' + scene + '"}');
        $("#saleGoodsDeliveryDirect").click();
    }

    $.ajax({
        url: page_url + '/orderstream/saleorder/validCheckStatus.do',
        data: {"saleorderId": $("input[name='saleorderId']").val()},
        type: "POST",
        dataType: "json",
        async: false,
        success: function (data) {
            if (data.code == 0) {
                extracted();
            } else {
                layer.alert(data.message);
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function changeOwnerUserId(obj)
{
    var ownerUserId = $(obj).val();

    $("#ownerUserId").val(ownerUserId);

}

/**
 * 质保金金额校验
 */
function retainageAmountChange() {
    //质保金
    var retentionMoney = $('#retentionMoney').val();
    if( /^[0-9]+([.]{1}[0-9]{1,2})?$/.test(retentionMoney) ){
        //质保金金额
        var accountPeriodAmount = $('#accountPeriodAmount').val();
        if (parseFloat(retentionMoney) <= accountPeriodAmount){
            var goodsTotleMoney = $('#goodsTotleMoney').val();
            if (parseFloat(retentionMoney) <= goodsTotleMoney/2){
                $('#retentionMoneyFont').html(retentionMoney);
                $('#spareMoneyLab').html((parseFloat($('#accountPeriodAmount').val()) - parseFloat(retentionMoney)).toFixed(2));
                if (parseFloat(retentionMoney) != 0 ){
                    $('#retentionMoneySpan').show();
                } else {
                    $('#retentionMoneySpan').hide();
                }
                delWarnTips('accountPeriodAmountError')
            } else {
                warnTips("accountPeriodAmountError","质保金不得超过合同总金额的50%");
                return false;
            }
        } else {
            warnTips("accountPeriodAmountError","质保金不得超过账期支付金额");
            return false;
        }
    } else {
        warnTips("accountPeriodAmountError","只可填写数字，最多保留两位小数");
        return false;
    }
}

/**
 * 质保金期限校验
 */
function retainageAmountDayChange() {
    var retentionMoneyDay = $('#retentionMoneyDay').val();
    if(!(/(^[1-9]\d*$)/.test(retentionMoneyDay))){
        warnTips('retentionMoneyDayError','只可填写正整数');
    } else {
        delWarnTips('retentionMoneyDayError')
    }
}

/**
 * 余款账期天数校验
 */
function periodDayChange() {
    var accountPeriodAmount = $('#accountPeriodAmount').val();
    if (accountPeriodAmount != undefined && accountPeriodAmount != '' && accountPeriodAmount != 0){
        var  periodDay = $('#periodDay').val();
        if(!(/(^[1-9]\d*$)/.test( periodDay))){
            warnTips('retentionMoneyDayError','只可填写正整数');
        } else {
            delWarnTips('retentionMoneyDayError')
        }
    } else {
        $('#periodDay').val(0);
    }
}

/**
 * 自定义账期金额
 */
function accountPeriodAmountChange() {
    var accountPeriodAmount  = $('#accountPeriodAmount').val();
    if (accountPeriodAmount != undefined && accountPeriodAmount != '' && accountPeriodAmount != 0){
        $('#spareMoneyLab').html(parseFloat(accountPeriodAmount) - parseFloat($('#retentionMoney').val()).toFixed(2));
    } else {
        $('#spareMoneyLab').html(0.00);
        $('#retentionMoney').val(0);
        $('#retentionMoneySpan').hide();
    }
}

/**
 * 票货地址是否相同
 *
 * @param isSameAddressFlag
 */
function isSameAddressChecked(isSameAddressFlag) {
    checkInvoiceGoodsStatus();
    var orderType = $("#orderType").val();
    var isSameAddress = $("input[name='isSameAddress']:checked").val();
    if (isSameAddress == 1 && orderType ==5){
        $('#invoiceTraderContactName_5').val($('#takeTraderContactName_5').val());
        $('#invoiceTraderContactMobile_5').val($('#takeTraderContactMobile_5').val());
        $('#invoiceTraderAddressId-province').val($('#takeTraderAddressId-province').val())
        setRegion($('#takeTraderAddressId-province').val(), 'invoiceTraderAddressId-city');
        $("#invoiceTraderAddressId-city").val($('#takeTraderAddressId-city').val());
        setRegion($('#takeTraderAddressId-city').val(), 'invoiceTraderAddressId-zone');
        $('#invoiceTraderAddressId-zone').val($('#takeTraderAddressId-zone').val());
        $('#invoiceTraderAddress_5').val($('#takeTraderAddress_5').val());
        //收票地址地区标记
        $('#invoiceTraderAddressId').val($('#takeTraderAddressId-zone').val());
        $("#invoiceTraderAddress_5").attr("disabled","disabled");
        $("#invoiceTraderAddressId-zone").attr("disabled","disabled");
        $("#invoiceTraderAddressId-city").attr("disabled","disabled");
        $("#invoiceTraderAddressId-province").attr("disabled","disabled");
        $("#invoiceTraderContactMobile_5").attr("disabled","disabled");
        $("#invoiceTraderContactName_5").attr("disabled","disabled");
        $("#invoiceType").val(971);//13%普票
        updateInvoiceType($("#invoiceType"));
        // $("#invoiceType").attr("disabled","disabled");
        $("#invoiceMethod").val(4);//自动数电发票
        // $("#invoiceMethod").attr("disabled","disabled");
        $('.tips-error').hide();
        // $("#invoiceEmailPart").show();
    }else if(isSameAddress == 1 && orderType !=5){
        if(orderType == 7 || orderType == 8){
            $("#take_trader_2").val($("#take_trader_1").val());
            changeInvoiceTraderFilter();
            $("#showInvoiceTrader").val($("#take_trader_1").find("option:selected").text());
            $("#syncInvoiceTrader").show();
            $("#chooseInvoiceTraderPart").hide()
        }
        $("#invoiceType").val(971);//13%普票
        updateInvoiceType($("#invoiceType"));
        // $("#invoiceType").attr("disabled","disabled");
        $("#invoiceMethod").val(4);//自动数电发票
        // $("#invoiceMethod").attr("disabled","disabled");
        $('#address_2').data('prev', $('#address_2').val());
        $("#address_2").val($("#address_1").val());
        $("#showInvoice").val($("#address_1").find("option:selected").text());
        $("#syncInvoiceAddress").show();
        $("#chooseInvoicePart").hide();
        // $("#invoiceEmailPart").show();
        $('.tips-error').hide();
        //集采订单同步收票客户为收货客户
    }else if(isSameAddress != 1){
        // $("#invoiceType").removeAttr("disabled");
        // $("#invoiceMethod").removeAttr("disabled");
        $("#invoiceTraderAddress_5").removeAttr("disabled");
        $("#invoiceTraderAddressId-zone").removeAttr("disabled");
        $("#invoiceTraderAddressId-city").removeAttr("disabled");
        $("#invoiceTraderAddressId-province").removeAttr("disabled");
        $("#invoiceTraderContactMobile_5").removeAttr("disabled");
        $("#invoiceTraderContactName_5").removeAttr("disabled");
        $("#syncInvoiceAddress").hide();
        $("#chooseInvoicePart").show();
        // $("#invoiceEmailPart").hide();
        if(orderType == 7 || orderType == 8){
            $("#syncInvoiceTrader").hide();
            $("#chooseInvoiceTraderPart").show()
        }
        if($('#address_2').data('prev')){
            $("#address_2").val($('#address_2').data('prev'));
        }
    }
}

/**
 * 设置地区信息
 *
 * @param regionId 地区ID
 * @param regionType 下级地区标签
 */
function setRegion(regionId,regionType) {
    $.ajax
    ({
        type : "POST",
        url : page_url+"/system/region/getregion.do",
        data :{'regionId':regionId},
        dataType : 'json',
        async: false,
        success : function(data) {
            $option = "<option value='0'>请选择</option>";
            $.each(data.listData,function(i,n){
                $option += "<option value='"+data.listData[i]['regionId']+"'>"+data.listData[i]['regionName']+"</option>";
            });
            $("#" + regionType).empty();
            $("#" + regionType).html($option);
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });
}

/**
 * 检查票货同行状态
 */
function checkInvoiceGoodsStatus() {
    /**
     * “发票寄送节点”，该按钮显示的前提是：
     （1）“发票是否寄送”选择“寄送”
     （2）“货票地址是否相同”选择“相同”
     */
    if ($("input[name='isSameAddress']:checked").val() == 1){
        $('#invoiceSendNodeLi').show();
        $('#isSendInvoice').val(1);
    } else {
        $('#invoiceSendNodeLi').hide();
        $('#isSendInvoice').val(0);
    }
}

// 内部备注重复点击 比较最后一次点击时间和当前时间
var lastClick;
function lockClick(){
    let nowClick = new Date()
    if (lastClick === undefined) {
        lastClick = nowClick
        return true
    } else {
        if (Math.round((nowClick.getTime() - lastClick.getTime())) > 2000) {
            lastClick = nowClick
            return true
        }
        else {
            lastClick = nowClick
            return false
        }
    }
}

/**
 * 内部备注组件触发事件
 * @param dom 当前dom元素
 */
function insideRemark(dom) {

    if(!lockClick()){
        return;
    }

    let labelData = $(dom).attr('label_data');
    let remark = $(dom).val();
    let skuList = [];
    $(dom).siblings("input[name='skuList']").each(function (i,v) {
        let sku = {
            skuId: $(v).attr('skuId'),
            skuNo:  $(v).attr('skuNo'),
            skuName: $(v).attr('skuName')
        }
        skuList.push(sku)
    });
    let relationId = $("input[name='saleorderId']").val();
    new LabelMark({
        el: dom,
        value: labelData,
        url: page_url + '/order/remarkComponent/getInitComponent.do',
        query: {
            scene: 0,
            isAll: 0,
            remark: remark,
            relationId: relationId,
            skuList: skuList
        }
    });
}

// 内部备注重复点击 比较最后一次点击时间和当前时间
var lastClick;
function lockClick(){
    let nowClick = new Date()
    if (lastClick === undefined) {
        lastClick = nowClick
        return true
    } else {
        if (Math.round((nowClick.getTime() - lastClick.getTime())) > 2000) {
            lastClick = nowClick
            return true
        }
        else {
            lastClick = nowClick
            return false
        }
    }
}

/**
 * 内部备注组件触发事件
 * @param dom 当前dom元素
 */
function editinsideRemark(dom) {

    if(!lockClick()){
        return;
    }

    var id = "#" +  $(dom).prev().attr("id")
    let labelData = $(id).attr('label_data');
    let scene = parseInt($(id).attr('scene'))
    let remark = $(id).val();
    let hasRemark = false;
    if (remark) {
        hasRemark = true;
    }
    let skuList = [
        {
            skuId: $('#goodsId').val(),
            skuNo: $('#sku').val(),
            skuName: $('#goodsName').val()
        }
    ]
    if (!checkGoods(skuList, 1)) {
        return;
    }
    let relationId = $("#saleorderId").val();
    new LabelMark({
        el: id,
        value: labelData,
        url: page_url + '/order/remarkComponent/getInitComponent.do',
        query: {
            scene: 0,
            isAll: 1,
            remark: remark,
            hasRemark: hasRemark,
            relationId: relationId,
            skuList: skuList,
            showComponentIcon: true
        },
        saveUrl: page_url + '/orderstream/saleorder/updateInsideComments.do'
    });
}

function deliveryClaimChange(){
    if($("#deliveryClaimSelect").val() ==1){
        $("#waitDeadlineDiv").show();
    }else{
        $("#waitDeadlineDiv").hide();
        $("#deliveryDelayTimeStr").val('');
    }
}

function addContact(url){
    checkLogin();
    var open = layer.open({
        type: 2,
        title: '新增联系人',
        shadeClose: false,
        area : ['800px', '600px'],
        content: url
    });
}

function addAddress(url){
    checkLogin();
    var open = layer.open({
        type: 2,
        title: '新增地址',
        shadeClose: false,
        area : ['800px', '600px'],
        content: url
    });
}

function switchInput(){
    checkLogin();
    $(".searchfunc").hide();
    $("#terminalNameCheck").hide();
    $("#terminalName").show();
    $("#terminalType").show();
    $("#switchInput").hide();
    $("#switchSelect").show();
    $("#terminalNameDetail").hide();
    $("#terminal-info-div").hide();
    $("#terminalTraderName").val("")
}

function switchSelect(){
    checkLogin();
    $("#terminalNameCheck").show();
    $("#terminalName").hide();
    $("#terminalType").hide();
    $("#switchSelect").hide();
    $("#switchInput").show();
    $("#terminalNameDetail").hide();
    $("#terminalTraderName").val("")
}

function triggerContactInfoChanged() {
    var orderType = $("#orderType").val();
    if(orderType == 8 || orderType == 7) {
        //集采线下客户展示
        var contactId = $('#contact_3').find('option:selected').val();
        if (contactId == 0 || contactId == '') {
            $("#contact_position_1").html('');
            $("#allowed_goods_types_1").html('');
            return
        }

        //根据客户ID获取联系人列表&地址列表
        $.ajax({
            url: page_url + '/order/jc/getContactInfo.do',
            data: {"contactId": contactId},
            type: "get",
            dataType: "json",
            success: function (data) {
                if (data.code !== 0 && data.data !== null) {
                    return
                }
                //处理集采客户 -- 需显示集团标示信息
                if (data.data.groupLevel == 1) {
                    $('.group-label.parent').show();
                } else {
                    $('.group-label.child').show();
                }
                //处理集采客户
                $("#contact_position_1").html(data.data.positions);
                $("#allowed_goods_types_1").html(data.data.allowedGoodsTypes);
            }
        });
    }
}

function checkGoodsRangeForGroupCustomer() {
    return true;
}
//集采修改收货客户信息
function changeTakeTraderFilter(){
    var name = $('#take_trader_1 option:selected').text();
    var val = $('#take_trader_1 option:selected').val();
    if(val==0 || val=='') {
        $('input[name=takeTraderId]').val('');
        $('input[name=takeTraderName]').val('');
        return
    }
    $('input[name=takeTraderId]').val(val);
    $('input[name=takeTraderName]').val(name);

    var lastAddressId = $('select[name=takeTraderAddressId] > option:selected').val();
    var lastContactId = $('select[name=takeTraderContactId] > option:selected').val();
    //根据客户ID获取联系人列表&地址列表
    getContactAndAddress(val , '1',lastAddressId, lastContactId);

}
function getContactAndAddress(traderId , indexId, lastAddressId , lastContactId) {
    $.ajax({
        async:false,
        url: '/orderstream/saleorder/getCustomerContactAndAddress.do',
        data:{"traderId": traderId },
        type:"POST",
        dataType : "json",
        success:function(data){
            if (data.code == 0) {
                var addressStr = '<option value="0">请选择</option>';
                for(var i = 0; i< data.param.addressList.length; i++) {
                    var isSelected = data.param.addressList[i].traderAddress.traderAddressId == lastAddressId? 'selected = "selected"' : '';
                    addressStr += '<option value="' + data.param.addressList[i].traderAddress.traderAddressId + '" ' + isSelected + '>' + data.param.addressList[i].area + '/' + data.param.addressList[i].traderAddress.address + '</option>';
                }
                var contactStr = '<option value="0">请选择</option>';
                for(var i = 0; i< data.param.contactList.length; i++) {
                    var isSelected = data.param.contactList[i].traderContactId == lastContactId ? 'selected = "selected"' : '';
                    contactStr += '<option value="' + data.param.contactList[i].traderContactId + '" ' + isSelected + '>' + data.param.contactList[i].name + '/' + data.param.contactList[i].telephone + '/' + data.param.contactList[i].mobile + '</option>';
                }
                $("#trader_contact_"+indexId).html(contactStr);
                $("#address_"+indexId).html(addressStr);

                $("#add_contact_"+indexId).attr('layerParams', '{"width":"430px","height":"220px","title":"添加联系人","link":"/order/jc/addContact.do?traderId='+ traderId +'&indexId=' + indexId + '"}');
                $("#add_address_"+indexId).attr('layerParams', '{"width":"600px","height":"200px","title":"添加地址","link":"/order/jc/addAddress.do?traderId='+traderId+'&indexId=' + indexId + '"}');
            } else {
                layer.alert(data.message,{ icon: 2 });
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

function changeInvoiceTraderFilter(){
    var name = $('#take_trader_2 option:selected').text();
    var invoiceTraderId = $('#take_trader_2 option:selected').val();

    //获取客户账户信息
    getCustomerSettlementInfo(invoiceTraderId);

    if(invoiceTraderId == 0 || invoiceTraderId=='') {
        $('input[name=invoiceTraderId]').val('');
        $('input[name=invoiceTraderName]').val('');
        return
    }
    $('input[name=invoiceTraderId]').val(invoiceTraderId);
    $('input[name=invoiceTraderName]').val(name);

    var lastAddressId = $('select[name=invoiceTraderAddressId] > option:selected').val();
    var lastContactId = $('select[name=invoiceTraderContactId] > option:selected').val();
    //根据客户ID获取联系人列表&地址列表
    getContactAndAddress(invoiceTraderId , '2',lastAddressId, lastContactId);

}
/**
 * 获取客户信息及帐期信息
 *
 * @param traderId
 */
function getCustomerSettlementInfo(traderId) {
    if (traderId == null || traderId == '' || traderId == 0) {
        $('#invoiceCustomerPeriodBalance').text('0.00');
        $('#invoiceCustomerPeriodDay').text('0');
        return
    }

    $.ajax({
        url: '/trader/customer/getCustomerSettlementInfo.do',
        data:{"traderId": traderId },
        type:"GET",
        dataType : "json",
        success:function(data){
            if (data.code == 0 && data.data != null) {
                var customerInfo = data.data;

                //客户当前账期剩余额度
                var accountPeriodLeft = returnFloat(customerInfo.accountPeriodLeft);
                var periodAmount = returnFloat(customerInfo.periodAmount);

                var parsedPeriodDay = parseInt(customerInfo.periodDay);
                var periodDay = isNaN(parsedPeriodDay) ? 0 : parsedPeriodDay;
                //收票客户帐期天数
                $('#periodDay').val(periodDay);
                //收票客户帐期余额
                $("#accountPeriodLeft").val(accountPeriodLeft);
                //收票客户帐期总额
                $('#accountPeriodAmount').val(periodAmount);

                $('#invoiceCustomerPeriodBalance').text(accountPeriodLeft);
                $('#invoiceCustomerPeriodDay').text(periodDay);

                //触发付款结算计算
                $("#paymentType").trigger('change');
            } else {
                layer.alert('获取收票客户的帐期失败，原因: '+data.message,{ icon: 2 });
                $('#invoiceCustomerPeriodBalance').text('0.00');
                $('#invoiceCustomerPeriodDay').text('0');
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
    function returnFloat(value){
        var value=Math.floor(parseFloat(value)*100)/100;
        var xsd=value.toString().split(".");
        if(xsd.length==1){
            value=value.toString()+".00";
            return value;
        }
        if(xsd.length>1){
            if(xsd[1].length<2){
                value=value.toString()+"0";
            }
            return value;
        }
    }
}
