package com.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data

public class SelectDto {
    private Object value;
    private String label;
    private String name;
   // private Boolean selected;

    public SelectDto(Object value, String label) {
        // 默认构造函数
        this.value = value;
        this.label = label;
    }
    public SelectDto(Object value, String label, String name) {
        // 带name的构造函数
        this.value = value;
        this.label = label;
        this.name = name;
    }
}
