package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KingDeeNeedReceiveAdjustMapper {
    /**
     * delete by primary key
     * @param kingDeeNeedReceiveId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer kingDeeNeedReceiveId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeNeedReceiveAdjustEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeNeedReceiveAdjustEntity record);

    /**
     * select by primary key
     * @param kingDeeNeedReceiveId primary key
     * @return object by primary key
     */
    KingDeeNeedReceiveAdjustEntity selectByPrimaryKey(Integer kingDeeNeedReceiveId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeNeedReceiveAdjustEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeNeedReceiveAdjustEntity record);

    int batchInsert(@Param("list") List<KingDeeNeedReceiveAdjustEntity> list);

    Integer countMatchNum(String afterSaleNo);
}