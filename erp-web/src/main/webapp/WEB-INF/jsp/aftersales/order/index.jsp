<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="售后列表" scope="application" />	
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript"></script>
<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/index.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/aftersales/order/index.js?rnd=${resourceVersionKey}'></script>
	<div class="searchfunc">
		<form method="post" id="search" action="<%= basePath %>/aftersales/order/getAfterSalesPage.do">
			<ul>
				<li>
					<label class="infor_name">售后单号</label>
					<input type="text" class="input-middle" name="afterSalesNo" id="afterSalesNo" value="${afterSalesVo.afterSalesNo}"/>
				</li>
				<li>
					<label class="infor_name">对应订单号</label>
					<input type="text" class="input-middle" name="orderNo" id="orderNo" value="${afterSalesVo.orderNo}"/>
				</li>
				<c:if test="${isSupply eq 1}">
				<li>
					<label class="infor_name">产品归属</label>
					<select class="input-middle" name="beLongProductUserId" id="beLongProductUserId">
						<option value="">全部</option>
						<c:forEach items="${productUserList}" var="productUser">
								<option value="${productUser.userId }"
										<c:if test="${productUser.userId == afterSalesVo.beLongProductUserId}">selected="selected"</c:if>>${productUser.username }</option>
						</c:forEach>
					</select>
				</li>
				</c:if>
				<c:if test="${isSupply ne 1}">
				<li>
					<label class="infor_name">客户名称</label>
					<input type="text" class="input-middle" name="traderName" id="traderName" value="${afterSalesVo.traderName}"/>
				</li>
				</c:if>
				<li>
					<label class="infor_name">订单状态</label>
					<select class="input-middle" name="atferSalesStatus" id="atferSalesStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.atferSalesStatus eq 0}">selected</c:if> value="0">待确认</option>
						<option <c:if test="${afterSalesVo.atferSalesStatus eq 1}">selected</c:if> value="1">进行中</option>
						<option <c:if test="${afterSalesVo.atferSalesStatus eq 2}">selected</c:if> value="2">已完结</option>
						<option <c:if test="${afterSalesVo.atferSalesStatus eq 3}">selected</c:if> value="3">已关闭</option>
					</select>
				</li>
				<li>
					<label class="infor_name">审核状态</label>
					<select class="input-middle" name="status" id="status">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.status eq 0}">selected</c:if> value="0">待审核</option>
						<option <c:if test="${afterSalesVo.status eq 1}">selected</c:if> value="1">审核中</option>
						<option <c:if test="${afterSalesVo.status eq 2}">selected</c:if> value="2">审核通过</option>
						<option <c:if test="${afterSalesVo.status eq 3}">selected</c:if> value="3">审核不通过</option>
					</select>
				</li>
				<li>
					<label class="infor_name">开票状态</label>
					<select class="input-middle" name="invoiceStatus" id="invoiceStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.invoiceStatus eq 0}">selected</c:if> value="0">未开票</option>
						<option <c:if test="${afterSalesVo.invoiceStatus eq 1}">selected</c:if> value="1">部分开票</option>
						<option <c:if test="${afterSalesVo.invoiceStatus eq 2}">selected</c:if> value="2">全部开票</option>
					</select>
				</li>
				<li>
					<label class="infor_name">收票状态</label>
					<select class="input-middle" name="receiveInvoiceStatus" id="receiveInvoiceStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.receiveInvoiceStatus eq 0}">selected</c:if> value="0">未收票</option>
						<option <c:if test="${afterSalesVo.receiveInvoiceStatus eq 1}">selected</c:if> value="1">部分收票</option>
						<option <c:if test="${afterSalesVo.receiveInvoiceStatus eq 2}">selected</c:if> value="2">全部收票</option>
					</select>
				</li>
				<li>
					<label class="infor_name">收款状态</label>
					<select class="input-middle" name="receivePaymentStatus" id="receivePaymentStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.receivePaymentStatus eq 0}">selected</c:if> value="0">未收款</option>
						<option <c:if test="${afterSalesVo.receivePaymentStatus eq 1}">selected</c:if> value="1">部分收款</option>
						<option <c:if test="${afterSalesVo.receivePaymentStatus eq 2}">selected</c:if> value="2">全部收款</option>
					</select>
				</li>
				<li>
					<label class="infor_name">付款状态</label>
					<select class="input-middle" name="paymentStatus" id="paymentStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.paymentStatus eq 0}">selected</c:if> value="0">未付款</option>
						<option <c:if test="${afterSalesVo.paymentStatus eq 1}">selected</c:if> value="1">部分付款</option>
						<option <c:if test="${afterSalesVo.paymentStatus eq 2}">selected</c:if> value="2">全部付款</option>
					</select>
				</li>
				<li>
					<label class="infor_name">业务类型</label>
					<select class="input-middle" name="type" id="type">
						<option value="">全部</option>
						<c:forEach items="${sysList }" var="sys">
							<c:if test="${empty sys.relatedField }">
								<option value="${sys.sysOptionDefinitionId }"
								<c:if test="${sys.sysOptionDefinitionId == afterSalesVo.type}">selected="selected"</c:if>>${sys.title }</option>
							</c:if>
							
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">售后人员</label>
					<select class="input-middle" name="serviceUserId" id="serviceUserId">
						<option value="">全部</option>
						<c:forEach items="${serviceUserList }" var="su">
							<c:if test="${sys.relatedField == null}">
							<option value="${su.userId }"
								<c:if test="${su.userId == afterSalesVo.serviceUserId}">selected="selected"</c:if>>${su.username}</option>
								</c:if>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">操作人员</label>
					<select class="input-middle" name="currentAuditName" id="currentAuditName">
						<option value="">全部</option>
						<option value="${loginUser.username}"
                 <c:if test="${loginUser.username==afterSalesVo.currentAuditName}">selected="selected"</c:if>>${loginUser.username}</option>
					</select>
				</li>
				<li>
					<div class="infor_name specialinfor">
						<select name="timeType">
							<option value="1" <c:if test="${afterSalesVo.timeType == 1 }">selected="selected"</c:if>>申请时间</option>
							<option value="2" <c:if test="${afterSalesVo.timeType == 2 }">selected="selected"</c:if>>生效时间</option>
							<option value="3" <c:if test="${afterSalesVo.timeType == 3 }">selected="selected"</c:if>>完结时间</option>
						</select>
					</div> 
					<input type="hidden" name="search" value="click">
					<input type="hidden" name="nowDate" value="${nowDate}">
					<input type="hidden" name="pre1MonthDate" value="${pre1MonthDate}">
					
					<input class="Wdate f_left input-smaller96 m0" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})"
						name="starttime" id="starttime"  type="text" placeholder="请选择日期" value='<c:choose>
							<c:when test="${afterSalesVo.starttime != ''}">
								<date:date value ="${afterSalesVo.searchStartTime}" format="yyyy-MM-dd"/>
							</c:when>
							<c:when test="${afterSalesVo.starttime == ''}">
							</c:when>
							<c:otherwise>
								${pre1MonthDate}
							</c:otherwise>
						</c:choose>'>
					
					<div class="f_left ml1 mr1 mt4">-</div> 
					<input class="Wdate f_left input-smaller96" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})"
						name="endtime" id="endtime" type="text" placeholder="请选择日期" autocomplete="off" value='<c:choose>
							<c:when test="${afterSalesVo.endtime != ''}">
								<date:date value ="${afterSalesVo.searchEndTime}" format="yyyy-MM-dd"/>
							</c:when>
							<c:when test="${afterSalesVo.endtime == ''}">
							</c:when>
							<c:otherwise>
								${nowDate}
							</c:otherwise>
						</c:choose>'>
				</li>
				<li>
					<label class="infor_name">归属销售</label>
					<select class="input-middle" name="optUserId" id="optUserId">
						<option value="">全部</option>
						<c:forEach items="${userList}" var="list">
							<option value="${list.userId}" <c:if test="${afterSalesVo.optUserId eq list.userId}">selected="selected"</c:if>>${list.username}</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">销售部门</label>
					<select class="input-middle" name="orgId" id="orgId">
						<option value="">全部</option>
							<c:forEach items="${orgList}" var="org">
								<option value="${org.orgId}" <c:if test="${afterSalesVo.orgId eq org.orgId}">selected="selected"</c:if>>${org.orgName}</option>
							</c:forEach>
					</select>
				</li>

				<li>
					<label class="infor_name">联系方式</label>
					<input type="text" class="input-middle" name="mobileNumber" id="mobileNumber" value="${afterSalesVo.mobileNumber}"/>
				</li>
                <c:if test="${afterSalesVo.type==1135}">
				<li>
					<label class="infor_name">寄送状态</label>
					<select class="input-middle" name="invoiceSendStatus" id="invoiceSendStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.invoiceSendStatus eq 0}">selected</c:if> value="0">未寄送</option>
						<option <c:if test="${afterSalesVo.invoiceSendStatus eq 1}">selected</c:if> value="1">部分寄送</option>
						<option <c:if test="${afterSalesVo.invoiceSendStatus eq 2}">selected</c:if> value="2">全部寄送</option>
					</select>
				</li>

				<li>
					<label class="infor_name">签收状态</label>
					<select class="input-middle" name="invoiceArrivalStatus" id="invoiceArrivalStatus">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.invoiceArrivalStatus eq 0}">selected</c:if> value="0">未签收</option>
						<option <c:if test="${afterSalesVo.invoiceArrivalStatus eq 1}">selected</c:if> value="1">部分签收</option>
						<option <c:if test="${afterSalesVo.invoiceArrivalStatus eq 2}">selected</c:if> value="2">全部签收</option>
					</select>
				</li>
				</c:if>
				<c:if test="${isSupply eq 1}">
				<li>
					<label class="infor_name">对应入库单号</label>
					<input type="text" class="input-middle" name="inOrderStr" id="inOrderStr" value="${afterSalesVo.inOrderStr}"/>
				</li>
				</c:if>
				<li>
					<label class="infor_name">售后来源</label>
					<select class="input-middle" name="source" id="source">
						<option value="">全部</option>
						<option <c:if test="${afterSalesVo.source eq 2}">selected</c:if> value="2">贝登前台</option>
						<option  <c:if test="${afterSalesVo.source eq 0}">selected</c:if> value="0">ERP</option>
						<option <c:if test="${afterSalesVo.source eq 1}">selected</c:if> value="1">医械购前台</option>
					</select>
				</li>
			</ul>
			<div class="tcenter">
				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
				<c:if test="${isSupply ne 1}">
				<span class="bt-small bg-light-blue bt-bg-style addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
						"link":"./aftersales/order/addAfterSalesPage.do?flag=at","title":"新增售后"}'>新增售后</span>
				</c:if>
				<!-- <span class="bt-small bg-light-blue bt-bg-style" onclick="exportList();">导出列表</span> -->
			</div>
		</form>
	</div>
	<div class="content">
		<div class="normal-list-page">
			<div >
				<table
					class="table">
					<thead>
						<tr>
							<th class="wid3">序号</th>
							<th class="wid9">售后单号</th>
							<th class="wid8">来源</th>
							<th class="wid8">对应订单号</th>
							<c:if test="${isSupply ne 1}">
							<th class="wid15">客户名称</th>
							</c:if>
							<th class="wid8">归属销售</th>
							<c:if test="${isSupply eq 1}">
							<th class="wid10">对应入库单号</th>
							<th class="wid8">产品归属</th>
							</c:if>
							<th class="wid10">业务类型</th>
							<th class="wid10">销售部门</th>
							<c:if test="${isSupply ne 1}">
							<th class="wid8">创建人</th>
							</c:if>
							<th class="wid8">售后人员</th>
							<th class="wid8">操作人员</th>
							<th class="wid8">订单状态</th>
							<th class="wid8">开票状态</th>
							<th class="wid8">收票状态</th>
							<th class="wid10">申请时间</th>
							<th class="wid10">生效时间</th>
							<th class="wid10">完结时间</th>
							<th class="wid8">审核状态</th>
						</tr>
					</thead>
					<tbody>
						<c:forEach var="aftersales" items="${list}" varStatus="num">
							<c:set var="shenhe" value="0"></c:set>
							<c:if test="${aftersales.verifyStatus==0 && null!=aftersales.verifyUsernameList}">
								<c:forEach items="${aftersales.verifyUsernameList}" var="verifyUsernameInfo">
									<c:if test="${verifyUsernameInfo == loginUser.username}">
										<c:set var="shenhe" value="1"></c:set>
									</c:if>
								</c:forEach>
							</c:if>
							<tr>
								<td>${num.count}</td>
								<td>
									<c:if test="${aftersales.atferSalesStatus eq 0}"><span class="orangecircle"></span></c:if>
									<c:if test="${aftersales.atferSalesStatus eq 1}"><span class="greencircle"></span></c:if>
									<c:if test="${aftersales.atferSalesStatus eq 2}"><span class="bluecircle"></span></c:if>
									<c:if test="${aftersales.atferSalesStatus eq 3}"><span class="greycircle"></span></c:if>
								<!-- 
									<c:if test="${aftersales.isView ne 1}">
										${aftersales.afterSalesNo}
									</c:if> -->
									<c:if test="${not empty aftersales.orderNo}">
										<span class="font-blue addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${aftersales.afterSalesId}&traderType=1","title":"售后详情"}'>${aftersales.afterSalesNo}${aftersales.verifyStatus eq 0 and shenhe == 1 ?"<font color='red'>[审]</font>":""}</span>
									</c:if>
									<c:if test="${empty aftersales.orderNo}">
										<span class="font-blue addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${aftersales.afterSalesId}","title":"售后详情"}'>${aftersales.afterSalesNo}${aftersales.verifyStatus eq 0 and shenhe == 1 ?"<font color='red'>[审]</font>":""}</span>
									</c:if>
								</td>
								<td>
									<c:choose>
										<c:when test="${aftersales.source == 1 }">
											医械购前台
										</c:when>
										<c:when test="${aftersales.source == 2}">
											贝登前台
										</c:when>
										<c:otherwise>
											ERP
										</c:otherwise>
									</c:choose>
								</td>
								<td>
									<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
										"link":"<%= basePath %>/order/saleorder/view.do?saleorderId=${aftersales.orderId}","title":"订单信息"}'>${aftersales.orderNo}</a>
								</td>
								<c:if test="${isSupply ne 1}">
								<td>
									<a class="addtitle" href="javascript:void(0);"
										tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
											"link":"./trader/customer/baseinfo.do?traderId=${aftersales.traderId}",
											"title":"客户信息"}'>
										<c:choose>
											<c:when test="${not empty traderMap && not empty traderMap[aftersales.traderId]}">
												【${traderMap[aftersales.traderId].traderGroupName}】${aftersales.traderName}
											</c:when>
											<c:otherwise>
												${aftersales.traderName}
											</c:otherwise>
										</c:choose>
									</a>
								</td>
								</c:if>
								<td>${aftersales.userName}</td>
								<c:if test="${isSupply eq 1}">
								<%-- 对应入库单号 --%>
								<td>
									<c:forEach var="inOrder" items="${aftersales.inOrderList}" varStatus="status">
										<c:choose>
											<c:when test="${status.last}">
												<c:choose>
													<c:when test="${inOrder.orderType eq 1}">
														<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./order/buyorder/viewBuyordersh.do?buyorderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo}</span>
													</c:when>
													<c:when test="${inOrder.orderType eq 2}">
														<c:if test="${isSupply eq 1} ">
														<span class="font-blue addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${inOrder.buyorderId}&traderType=2","title":"售后详情"}'>${inOrder.buyorderNo}</span>
														</c:if>
														<c:if test="${isSupply ne 1}">
														<span class="font-blue addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${inOrder.buyorderId}&traderType=1","title":"售后详情"}'>${inOrder.buyorderNo}</span>
														</c:if>
													</c:when>
													<c:otherwise>
														<c:choose>
															<c:when test="${fn:startsWith(inOrder.buyorderNo,'WJD')}">
														<span class="font-blue addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./wms/commodityLendOut/detail.do?lendOutId=${inOrder.buyorderId}","title":"详情"}'>${inOrder.buyorderNo}</span>
															</c:when>
															<c:when test="${fn:startsWith(inOrder.buyorderNo,'JY')}">
														<span class="font-blue addtitle" tabTitle='{"num":"lendOutdetailJump${inOrder.buyorderId}",
														"link":"./warehouse/warehousesout/lendOutdetailJump.do?lendOutId=${inOrder.buyorderId}","title":"外借详情页"}'>${inOrder.buyorderNo}</span>
															</c:when>
															<c:when test="${fn:startsWith(inOrder.buyorderNo,'PY')}">
														<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
													"link":"./wms/surplusIn/applyIndex.do?wmsInputOrderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo}</span>
															</c:when>
															<c:when test="${fn:startsWith(inOrder.buyorderNo,'ADJ')}">
														<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
													"link":"./wms/inventory/inventoryAdjustmentDetail.do?inventoryAdjustmentId=?wmsInputOrderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo}</span>
															</c:when>
														</c:choose>
													</c:otherwise>
												</c:choose>
											</c:when>
											<c:otherwise>
												<c:choose>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'VB')}}">
															<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
															"link":"./order/buyorder/viewBuyordersh.do?buyorderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo},</span>
													</c:when>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'VP')}">
															<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
															"link":"./order/buyorder/viewBuyordersh.do?buyorderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo},</span>
													</c:when>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'SH')}">
														<c:if test="${isSupply eq 1} ">
															<span class="font-blue addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
															"link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${inOrder.buyorderId}&traderType=2","title":"售后详情"}'>${inOrder.buyorderNo},</span>
														</c:if>
														<c:if test="${isSupply ne 1}">
															<span class="font-blue addtitle" tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
															"link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${inOrder.buyorderId}&traderType=1","title":"售后详情"}'>${inOrder.buyorderNo},</span>
														</c:if>
													</c:when>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'WJD')}">
															<span class="font-blue addtitle" tabTitle='{"num":"customer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
															"link":"./wms/commodityLendOut/detail.do?lendOutId=${inOrder.buyorderId}","title":"详情"}'>${inOrder.buyorderNo},</span>
													</c:when>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'JY')}">
															<span class="font-blue addtitle" tabTitle='{"num":"lendOutdetailJump${inOrder.buyorderId}",
															"link":"./warehouse/warehousesout/lendOutdetailJump.do?lendOutId=${inOrder.buyorderId}","title":"外借详情页"}'>${inOrder.buyorderNo},</span>
													</c:when>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'PY')}">
															<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./wms/surplusIn/applyIndex.do?wmsInputOrderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo},</span>
													</c:when>
													<c:when test="${fn:startsWith(inOrder.buyorderNo,'ADJ')}">
															<span class="font-blue addtitle" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
														"link":"./wms/inventory/inventoryAdjustmentDetail.do?inventoryAdjustmentId=?wmsInputOrderId=${inOrder.buyorderId}","title":"订单信息"}'>${inOrder.buyorderNo},</span>
													</c:when>
												</c:choose>
											</c:otherwise>
										</c:choose>
									</c:forEach>
								</td>
								<%-- 归属产品 --%>
								<td>
									<c:forEach items="${aftersales.beLongProductUserList}" var="user" varStatus="status">
										<c:choose>
											<c:when test="${status.last}">
												${user.username}
											</c:when>
											<c:otherwise>
												${user.username},
											</c:otherwise>
										</c:choose>
									</c:forEach>
								</td>
								</c:if>
								<td>${aftersales.typeName}</td>
								<td>${aftersales.orgName}</td>
								<c:if test="${isSupply ne 1}">
								<td>
									<c:if test="${aftersales.source eq 2 and not empty aftersales.createFrontEndUser}">${aftersales.createFrontEndUser }</c:if>
									<c:if test="${aftersales.source eq 2 and not empty aftersales.createFrontEndUser}"><br />（前台登录客户）</c:if>
									<c:if test="${aftersales.source ne 2}">${aftersales.creatorName}</c:if>
								</td>
								</c:if>
								<td>
									<c:if test="${aftersales.subjectType eq 537}">${aftersales.creatorName}</c:if>
									<c:if test="${aftersales.subjectType ne 537}">${aftersales.serviceUserName}</c:if>
								</td>
								<td>
									${aftersales.verifyUsername}
								</td>
								<td>
									<c:if test="${aftersales.atferSalesStatus eq 0}">待确认</c:if>
									<c:if test="${aftersales.atferSalesStatus eq 1}">进行中</c:if>
									<c:if test="${aftersales.atferSalesStatus eq 2}">已完结</c:if>
									<c:if test="${aftersales.atferSalesStatus eq 3}">已关闭</c:if>
								</td>
								<td>
									<c:if test="${aftersales.invoiceStatus eq 0}">未开票</c:if>
									<c:if test="${aftersales.invoiceStatus eq 1}">部分开票</c:if>
									<c:if test="${aftersales.invoiceStatus eq 2}">全部开票</c:if>
								</td>
								<td>
									<c:if test="${aftersales.receiveInvoiceStatus eq 0}">未收票</c:if>
									<c:if test="${aftersales.receiveInvoiceStatus eq 1}">部分收票</c:if>
									<c:if test="${aftersales.receiveInvoiceStatus eq 2}">全部收票</c:if>
								</td>
								<td><date:date value ="${aftersales.addTime}"/></td>
								<td><date:date value ="${aftersales.validTime}"/></td>
								<td><fmt:formatDate value="${aftersales.afterSalesWixTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
								<td>
									<c:if test="${empty aftersales.verifyStatus}">待审核</c:if>
									<c:if test="${aftersales.verifyStatus eq 0}">审核中</c:if>
									<c:if test="${aftersales.verifyStatus eq 1}">审核通过</c:if>
									<c:if test="${aftersales.verifyStatus eq 2}">审核不通过</c:if>
								</td>
							</tr>
						</c:forEach>
						<c:if test="${empty list}">
			      			<tr>
			      				<td colspan="12">查询无结果！请尝试使用其他搜索条件。</td>
			      			</tr>
				       	</c:if>
					</tbody>
				</table>
			</div>
		</div>
       	<tags:page page="${page}"/>
	</div>
</body>

</html>


