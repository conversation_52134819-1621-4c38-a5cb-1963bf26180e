package com.vedeng.common.core.base;

import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础视图跳转Controller
 * @date 2022/7/11 10:37
 */
@Slf4j
public class BaseController {

    @Value("${lxcrmUrl}")
    protected String lxcrmUrl;

    private final static String PATH_TMP = "vue/view{}/{}";

    /**
     * 构建实际访问的地址
     *
     * @param url 地址 当参数为空自动拼接当前方法名称作为返回地址
     * @return 实际地址
     *
     * 例如：
     * vue/view/businessleads/index
     */
    public String view(String... url) {
        RequestMapping classRequestMapping = AnnotationUtil.getAnnotation(this.getClass(), RequestMapping.class);
        if (classRequestMapping == null || ArrayUtil.isEmpty(classRequestMapping.value())) {
            throw new ServiceException(BaseResponseCode.URL_NOT_FOUNT);
        }
        if (ArrayUtil.isEmpty(url)) {
            url = new String[]{""};
            url[0] = new Throwable().getStackTrace()[1].getMethodName();
        }
        String path = StrUtil.format(PATH_TMP, classRequestMapping.value()[0].toLowerCase(), url[0]);
        log.info("返回真实路由地址[{}]",path);
        return path;
    }


}
