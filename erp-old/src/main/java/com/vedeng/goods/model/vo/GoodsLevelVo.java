package com.vedeng.goods.model.vo;


import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import lombok.Data;

import java.util.List;

@Data
public class GoodsLevelVo {

    private Integer id;

    private String uniqueIdentifier;

    private String levelName;

    private String description;

    private Boolean allowSyncFrontend;

    /**
     * 上架必办事项
     */
    private List<String> todoItems;
    /**
     * 等级对应的rule
     */
    private GoodsValidatedRule validatedRule;

}