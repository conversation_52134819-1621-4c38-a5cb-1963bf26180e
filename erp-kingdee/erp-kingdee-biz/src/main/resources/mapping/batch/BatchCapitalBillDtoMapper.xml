<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper">


    <select id="findByAll" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT
            tcb.CAPITAL_BILL_NO,
            tcb.TRADER_TIME,
            tcbd.ORDER_NO,
            tts.TRADER_SUPPLIER_ID,
            tcb.TRADER_MODE,
            tcb.AMOUNT
        FROM
            T_CAPITAL_BILL_DETAIL tcbd
        LEFT JOIN T_CAPITAL_BILL tcb ON
            tcbd.CAPITAL_BILL_ID = tcb.CAPITAL_BILL_ID
        LEFT JOIN T_TRADER_SUPPLIER tts ON tcbd.TRADER_ID = tts.TRADER_ID
        WHERE
             tcbd.BUSSINESS_TYPE = 525
            AND tcb.TRADER_MODE in (528,10000)
            <if test="beginTime != null">
                AND tcb.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
            </if>
            <if test="endTime != null">
                AND tcb.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
            </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="afterSaleToBalanceReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT TCB.CAPITAL_BILL_NO,
               TCB.TRADER_TIME,
               TAS.ORDER_NO,
               TCBD.ORDER_NO as afterSaleNo,
               TTC.TRADER_CUSTOMER_ID,
               TTC.TRADER_ID,
               TCB.AMOUNT
        FROM T_CAPITAL_BILL_DETAIL TCBD
                 LEFT JOIN T_CAPITAL_BILL TCB ON
            TCBD.CAPITAL_BILL_ID = TCB.CAPITAL_BILL_ID
                 LEFT JOIN T_TRADER_CUSTOMER TTC ON TCBD.TRADER_ID = TTC.TRADER_ID
                 LEFT JOIN T_AFTER_SALES TAS ON TCBD.RELATED_ID = TAS.AFTER_SALES_ID
                 LEFT JOIN T_AFTER_SALES_DETAIL TASD ON TASD.AFTER_SALES_ID = TAS.AFTER_SALES_ID
        WHERE TCB.TRADER_MODE = 530     #退还余额
          AND TCB.TRADER_TYPE = 5       #支出
          AND TCBD.ORDER_TYPE = 3       #售后订单
          AND TCBD.BUSSINESS_TYPE = 531 #退款
          AND TASD.REFUND != 2          #退到客户
        <if test="beginTime != null">
            AND TCB.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="saleOrderPayByBalanceReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT TCB.CAPITAL_BILL_NO,
               TCB.TRADER_TIME,
               TCBD.ORDER_NO,
               TTC.TRADER_CUSTOMER_ID,
               TCB.AMOUNT
        FROM T_CAPITAL_BILL_DETAIL TCBD
                 LEFT JOIN T_CAPITAL_BILL TCB ON
            TCBD.CAPITAL_BILL_ID = TCB.CAPITAL_BILL_ID
                 LEFT JOIN T_TRADER_CUSTOMER TTC ON TCBD.TRADER_ID = TTC.TRADER_ID
        WHERE TCB.TRADER_MODE = 528         #余额支付
        AND TCB.TRADER_TYPE in (4,1)           #转入
        AND TCBD.BUSSINESS_TYPE in (526,533)     #订单收款
        <if test="beginTime != null">
            AND TCB.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="afterSalePayByBalanceReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT TCB.CAPITAL_BILL_NO,
               TCB.TRADER_TIME,
               TAS.ORDER_NO,
               TCBD.ORDER_NO AS afterSaleNo,
               TTC.TRADER_CUSTOMER_ID,
               TCB.AMOUNT
        FROM T_CAPITAL_BILL_DETAIL TCBD
                 LEFT JOIN T_CAPITAL_BILL TCB ON
            TCBD.CAPITAL_BILL_ID = TCB.CAPITAL_BILL_ID
                 LEFT JOIN T_TRADER_CUSTOMER TTC ON TCBD.TRADER_ID = TTC.TRADER_ID
                 LEFT JOIN T_AFTER_SALES TAS ON TCBD.RELATED_ID = TAS.AFTER_SALES_ID
        WHERE TCB.TRADER_MODE = 528         #余额支付
          and TCB.TRADER_TYPE = 1           #收入
          and TCBD.ORDER_TYPE = 3           #售后订单
          and TCBD.BUSSINESS_TYPE = 526     #订单收款
        <if test="beginTime != null">
            AND TCB.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="afterSalePayByBalanceNegativeReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT TCB.CAPITAL_BILL_NO,
        TCB.TRADER_TIME,
        TAS.ORDER_NO,
        TCBD.ORDER_NO AS afterSaleNo,
        TTC.TRADER_CUSTOMER_ID,
        TCB.AMOUNT
        FROM T_CAPITAL_BILL_DETAIL TCBD
        LEFT JOIN T_CAPITAL_BILL TCB ON
        TCBD.CAPITAL_BILL_ID = TCB.CAPITAL_BILL_ID
        LEFT JOIN T_TRADER_CUSTOMER TTC ON TCBD.TRADER_ID = TTC.TRADER_ID
        LEFT JOIN T_AFTER_SALES TAS ON TCBD.RELATED_ID = TAS.AFTER_SALES_ID
        LEFT JOIN T_AFTER_SALES_DETAIL TASD ON TASD.AFTER_SALES_ID = TAS.AFTER_SALES_ID
        WHERE TCB.TRADER_MODE = 528         #余额支付
        and TCB.TRADER_TYPE = 1           #收入
        and TCBD.ORDER_TYPE = 3           #售后订单
        and TCBD.BUSSINESS_TYPE = 526     #订单收款
        AND TASD.REFUND = 2               #退到客户
        <if test="beginTime != null">
            AND TCB.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="buyOrderAfterSaleBalanceReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT TCB.CAPITAL_BILL_NO,
        TCB.TRADER_TIME,
        TAS.ORDER_NO,
        TCBD.ORDER_NO AS afterSaleNo,
        TTS.TRADER_SUPPLIER_ID,
        TCB.TRADER_MODE,
        TCB.AMOUNT,
        TCBD.BUSSINESS_TYPE
        FROM T_CAPITAL_BILL_DETAIL TCBD
        LEFT JOIN T_CAPITAL_BILL TCB ON
        TCBD.CAPITAL_BILL_ID = TCB.CAPITAL_BILL_ID
        LEFT JOIN T_TRADER_SUPPLIER TTS ON TCBD.TRADER_ID = TTS.TRADER_ID
        LEFT JOIN T_AFTER_SALES TAS ON TCBD.RELATED_ID = TAS.AFTER_SALES_ID
        WHERE TCB.TRADER_MODE in (530,10000) -- 退还余额
        and TCB.TRADER_TYPE in (4)   -- 转入 -- 返利
        and TCBD.ORDER_TYPE = 3   -- 售后
        and TCBD.BUSSINESS_TYPE = 531 -- 退款
        <if test="beginTime != null">
            AND TCB.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="buyOrderSettleAfterSaleBalanceReader" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT TCB.CAPITAL_BILL_NO,
        TCB.TRADER_TIME,
        TCBD.ORDER_NO AS orderNo,
        TCBD.ORDER_NO AS afterSaleNo,
        TTS.TRADER_SUPPLIER_ID,
        TCB.TRADER_MODE,
        TCB.AMOUNT,
        TCBD.BUSSINESS_TYPE
        FROM T_CAPITAL_BILL_DETAIL TCBD
        LEFT JOIN T_CAPITAL_BILL TCB ON
        TCBD.CAPITAL_BILL_ID = TCB.CAPITAL_BILL_ID
        LEFT JOIN T_TRADER_SUPPLIER TTS ON TCBD.TRADER_ID = TTS.TRADER_ID
        LEFT JOIN T_SETTLEMENT_BILL TSB ON TCBD.RELATED_ID = TSB.SETTLE_BILL_ID
        WHERE TCB.TRADER_MODE in (10000) -- 退还余额
        and TCB.TRADER_TYPE in (4)   -- 转入 -- 返利
        and TCBD.ORDER_TYPE = 6   -- 返利
        and TCBD.BUSSINESS_TYPE = 526 -- 收款
        <if test="beginTime != null">
            AND TCB.TRADER_TIME <![CDATA[>=]]> #{beginTime,jdbcType=BIGINT}
        </if>
        <if test="endTime != null">
            AND TCB.TRADER_TIME <![CDATA[<=]]> #{endTime,jdbcType=BIGINT}
        </if>
        LIMIT #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryCapitalBillByBankBillId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT
            A.CAPITAL_BILL_ID,A.CAPITAL_BILL_NO,A.TRADER_SUBJECT,A.TRADER_TYPE,A.TRADER_MODE,A.AMOUNT,
            B.ORDER_NO, A.PAYEE_BANK_ACCOUNT,  A.PAYEE_BANK_NAME, A.PAYEE,B.BUSSINESS_TYPE,B.TRADER_ID
            FROM T_CAPITAL_BILL A
            LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
        WHERE BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </select>

    <select id="getFirstCapitalBillTraderTime" resultType="java.lang.Long">
        select
            a.TRADER_TIME
        from T_CAPITAL_BILL a
                 join T_CAPITAL_BILL_DETAIL b on a.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
                 left join T_BANK_BILL c ON a.BANK_BILL_ID = c.BANK_BILL_ID
        where
            b.ORDER_TYPE = 3
            AND b.RELATED_ID = #{afterSalesId,jdbcType=INTEGER}
        order by a.TRADER_TIME
        limit 1
    </select>

    <select id="queryCapitalBillRelateOrderNo" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT
            A.CAPITAL_BILL_ID,A.CAPITAL_BILL_NO,A.TRADER_SUBJECT,A.TRADER_TYPE,A.TRADER_MODE,A.AMOUNT,B.TRADER_ID,
            B.ORDER_NO
        FROM T_BANK_BILL T
                 LEFT JOIN T_CAPITAL_BILL_DETAIL B ON T.ORDER_NO = B.ORDER_NO
                 LEFT JOIN T_CAPITAL_BILL A ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
        WHERE T.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
          AND A.TRADER_TYPE =1
          AND A.TRADER_MODE in (520, 522)
    </select>
    <select id="queryCapitalBillByTranFlow" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        SELECT
            A.CAPITAL_BILL_ID,A.CAPITAL_BILL_NO,A.TRADER_SUBJECT,A.TRADER_TYPE,A.TRADER_MODE,A.AMOUNT,
            B.ORDER_NO, A.PAYEE_BANK_ACCOUNT,  A.PAYEE_BANK_NAME, A.PAYEE,B.BUSSINESS_TYPE,B.TRADER_ID
        FROM T_CAPITAL_BILL A
                 LEFT JOIN T_CAPITAL_BILL_DETAIL B ON A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
        WHERE TRAN_FLOW = #{tranFlow}
    </select>
    <select id="getOrderNoByBankBillId" resultType="java.lang.String">
        SELECT CBD.ORDER_NO
        FROM T_CAPITAL_BILL_DETAIL CBD
        LEFT JOIN T_CAPITAL_BILL CB ON CBD.CAPITAL_BILL_ID = CB.CAPITAL_BILL_ID
        WHERE CB.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
        LIMIT 1
    </select>

    <select id="getFirstDetailByBankBillId" resultType="com.vedeng.erp.finance.dto.CapitalBillDetailDto">
        select tcd.*
        from T_CAPITAL_BILL tc
                 left join T_CAPITAL_BILL_DETAIL tcd on tc.CAPITAL_BILL_ID = tcd.CAPITAL_BILL_ID
        where tc.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
        limit 1
    </select>

    <select id="queryByTraderTypeAndBankBillId" resultType="com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto">
        select
        A.AMOUNT,
        A.TRADER_SUBJECT,
        B.ORDER_NO,
        B.ORDER_TYPE
        from T_CAPITAL_BILL A
        left join T_CAPITAL_BILL_DETAIL B on A.CAPITAL_BILL_ID = B.CAPITAL_BILL_ID
        where A.BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
          and A.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
          and A.TRADER_MODE in (520, 521, 522, 10001)
    </select>
</mapper>
