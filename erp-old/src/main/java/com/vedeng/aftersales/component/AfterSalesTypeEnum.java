package com.vedeng.aftersales.component;

/**
 * @Author: daniel
 * @Date: 2021/6/21 16 04
 * @Description:
 */
public enum AfterSalesTypeEnum {

    /**
     * 销售订单退货
     */
    RETURNS_SALEORDER(539,"销售订单退货"),

    EXCHANGES_SALEORDER(540,"销售订单换货"),

//    INSTALL_COMMISSION_SALEORDER(541,"销售订单安调"),
    INSTALL_AFTERSALES_ATY(4090, "销售安调（合同安调）"),
    INSTALL_AFTERSALES_ATN(4091, "销售安调（附加服务）"),

    REFUND_TICKET_SALEORDER(542,"销售订单退票"),

    REFUND_MONEY_SALEORDER(543, "销售订单退款"),

    TECH_CONSULT_SALEORDER(544,"销售订单技术咨询"),

    OTHERS_SALEORDER(545,"销售订单其他"),

    MAINTENANCE_SALEORDER(584,"销售订单维修"),

    LOST_TICKET_SALEORDER(1135,"销售订单丢票"),

    NONPERFORMENCE_PAYMENT(3321,"销售订单未履约赔付"),

    DELIVERY_COMPLAINT(3322,"销售订单送货投诉"),

    INSTALL_COMPLAINT(3323,"销售订单安装投诉"),

    MAINTAIN_COMPLAINT(3324,"销售订单维修投诉"),

    NULL(0,"异常数据"),
    ;

    private Integer code;

    private String name;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    AfterSalesTypeEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static AfterSalesTypeEnum getEnumByCode(Integer code){
        for (AfterSalesTypeEnum item : AfterSalesTypeEnum.values()){
            if (item.getCode().equals(code)){
                return item;
            }
        }
        return NULL;
    }
}
