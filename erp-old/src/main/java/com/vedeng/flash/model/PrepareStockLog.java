package com.vedeng.flash.model;

import lombok.Data;

/**
 * @Description:  安全库存日志
 * @Author:       davis
 * @Date:         2021/5/21 下午5:10
 * @Version:      1.0
 */
@Data
public class PrepareStockLog {

    /**
     * 库存ID
     */
    private Integer safeStockId;

    /**
     * 定品ID
     */
    private Integer prepareId;

    /**
     * SKUID
     */
    private Integer skuId;

    /**
     * 安全库存
     */
    private Integer safeStock;

    /**
     * 安全库存原值
     */
    private Integer originSafeStock;

    /**
     * 调整原因
     */
    private Integer operateType;

    /**
     * 调整人
     */
    private Integer operateUserId;

    /**
     * 调整人名称
     */
    private String operateUserName;

    /**
     * 调整时间
     */
    private Long operateTime;

    /**
     * 调整原因描述
     */
    private String operateReason;

}
