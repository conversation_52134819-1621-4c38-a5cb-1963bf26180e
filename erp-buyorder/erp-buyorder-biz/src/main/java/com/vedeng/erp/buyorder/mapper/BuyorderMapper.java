package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.Buyorder;
import com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods;
import com.vedeng.erp.buyorder.dto.*;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Named("newBuyorderMapper")
public interface BuyorderMapper {

	/**
	 * 在库商品列表
	 * @param goodsId
	 * @return
	 */
	List<BuyOrderDto> getBuyorderVoList(Integer goodsId);

	/**
	 * <AUTHOR>
	 * @desc 根据采购单号查询采购单信息
	 * @param buyorderNo
	 * @return
	 */
	BuyOrderDto queryInfoByNo(String buyorderNo);

	/**
	 * 根据采购订单id查询采购订单信息
	 *
	 * @param buyorderId 采购订单id
	 * @return 采购订单信息
	 */
	BuyOrderApiDto findByBuyorderId(@Param("buyorderId") Integer buyorderId);

	/**
	 * 根据采购订单id查询采购订单信息
	 *
	 * @param buyorderId 采购订单id
	 * @return 采购订单信息
	 */
	BuyOrderInfoDto getBuyOrderInfo(Integer buyorderId);



	/**
	 * 根据采购订单编号查询采购订单信息
	 *
	 * @param buyorderNo 采购订单编号
	 * @return 采购订单信息
	 */
	BuyOrderApiDto findByBuyorderNo(@Param("buyorderNo") String buyorderNo);




	/**
	 * 查询指定物流下的物流明细
	 * @param expressId
	 * @return
	 */
	List<PeerListBuyorderGoods> getNeedEditBuyGoods(Integer expressId);

	/**
	 * 查询指定物流明细的详细信息
	 * @param integers
	 * @return
	 */
	List<PeerListBuyorderGoods> getDetailsByExpressDetailId(@Param("list") List<Integer> integers);


	int insert(Buyorder record);

	int insertSelective(Buyorder record);

	Buyorder selectByPrimaryKey(Integer buyorderId);

	int updateByPrimaryKeySelective(Buyorder record);

	int updateByPrimaryKey(Buyorder record);

	/**
	 * 获取采购单以及对应sku对应的销售单saleOrderGoodsId
	 * @param buyOrderId
	 * @param skus
	 * @return
	 */
	List<Integer> selectSalorderGoodsBySkuBuyOrderId(@Param("buyOrderId") Integer buyOrderId, @Param("skus") List<String> skus);

	/**
	 * 更新 专项发货
	 * @param saleorderGoodsList saleOrderGoodsId的集合
	 * @return
	 */
	int updateSaleOrderGoodsSpecialDevlivery(@Param("list") List<Integer> saleorderGoodsList);

	/**
	 * 查询销售单id
	 * @param salerOrderGoodsId
	 * @return
	 */
	Integer selectSalorderIdBySaleOrderGoodsId(Integer salerOrderGoodsId);

	int updateComponentRelation(@Param("saleOrderId") Integer saleOrderId, @Param("skus") List<String> collect);

	/**
	 * 获取采购单关联的销售单
	 * @param buyorderId
	 * @return
	 */
	List<Integer> getSaleorderIdListByBuyorderId(@Param("buyorderId") Integer buyorderId);
	/**
	 * 获取采购单关联的销售单
	 * @param saleorderIds
	 * @return
	 */
	List<Integer> getBuyorderIdListBySaleorderIds(@Param("list") List<Integer> saleorderIds);

	/**
	 * 查询该SKU最近的一笔已生效采购订单的采购价用以计算公允价。
	 *
	 * @param goodsId skuId
	 * @return 该sku的采购价
	 */
	BigDecimal getBuyOrderGoodsPriceByGoodsId(@Param("goodsId") Integer goodsId);

	/**
	 * 查询沟通记录关联的采购单信息（id 单号）
	 *
	 * @param list 采购单id集合
	 * @return id 单号
	 */
	List<Map<String, Object>> getCommunicateBuyOrderInfo(@Param("list") List<Integer> list);

	/**
	 * 根据采购单Id和skuNo查询采购商品
	 *
	 * @param buyOrderId 采购单Id
	 * @param skuNo    skuNo
	 * @return BUYORDER_GOODS_ID (buyorder模块下没有BUYORDER_GOODS的实体和dto)
	 */
	List<Integer> getByBuyOrderIdAndSkuNo(@Param("buyOrderId") Integer buyOrderId, @Param("skuNo") String skuNo);

	/**
	 * 根据采购单Id更新付款状态和收票状态
	 * @param updatedPaymentStatus
	 * @param updatedInvoiceStatus
	 * @param buyorderId
	 * @return
	 */
	int updatePaymentStatusAndInvoiceStatusByBuyorderId(@Param("updatedPaymentStatus") Integer updatedPaymentStatus, @Param("updatedInvoiceStatus") Integer updatedInvoiceStatus, @Param("buyorderId") Integer buyorderId);

	/**
	 * 获取采购单合同审核状态
	 * @param relatedId
	 * @return
	 */
	List<Integer> getContractReviewStatus(Integer relatedId);

	List<BuyorderContractAuditDto> getContractAuditInfo(@Param("buyOrderId") Integer relatedId);
	List<BuyorderContractAttachmentDto> getAttachmentInfo(@Param("buyOrderId") Integer relatedId,@Param("addTimeList") List<Long> addTimeList);

	List<BuyOrderKingDeeDto> kingDeePageSelect(BuyOrderKingDeeRequestDto buyOrderKingDeeRequestDto);

	List<Map<String,String>> getBdSaleOrderListByBuyOrderNo(@Param("buyOrderNoList") List<String> buyOrderNoList);


    List<ExpressGoodsDto> getExpressGoodsList( @Param("buyOrderNo") String buyOrderNo);

    /**
     * 根据采购单ID查询发票信息列表
     * @param buyorderId 采购单ID
     * @return 发票信息列表
     */
    List<BuyOrderInvoiceDto> getInvoiceListByBuyorderId(@Param("buyorderId") Integer buyorderId);
}
