<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认审核" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript">

    function complementTask() {
        checkLogin();
        var comment = $("#comment").val()
        var pass = $("#pass").val()

        if(pass =="false" && comment == ""){
            warnTips("comment","请填写备注");
            return false;
        }
        if(comment.length > 1024){
            warnTips("comment","备注内容不允许超过256个字符");
            return false;
        }
        $(".bg-light-green").prop('disabled', true).text("提交中").css('background-color', 'gray');

        if ($('#alreadySubmit').val() == 0){
            $('#alreadySubmit').val(1);
            $.ajax({
                type: "POST",
                url: "./complementTask.do",
                data: $('#complement').serialize(),
                dataType:'json',
                success: function(data){
                    if (data.code == 0) {
                        layer.close(index);
                        window.parent.location.reload();
                    } else {
                        $(".bg-light-green").prop('disabled', false).text("提交").css('background-color', '');
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    $(".bg-light-green").prop('disabled', false).text("提交").css('background-color', '');
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }
    }

</script>
<div class="formpublic">
    <form method="post" action="" id="complement">
        <ul>
            <li>
                <div class="infor_name">
                    <c:if test="${pass==false}">
                        <span>*</span>
                    </c:if>
                    <lable for='name'>备注</lable>
                </div>
                <div class="f_left">
                    <input type="text" name="comment" id="comment" class="input-larger" value="" />
                </div>
            </li>
        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" value="${taskId}" name="taskId" id="taskId">
            <input type="hidden" id="alreadySubmit" value="0">
            <input type="hidden" value="${sampleOutId}" name="sampleOutId" id="sampleOutId">
            <input type="hidden" value="${pass}" name="pass" id="pass">
            <button type="button" class="bg-light-green" onclick="complementTask()">提交</button>
            <button class="dele" type="button" id="close-layer">取消</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>