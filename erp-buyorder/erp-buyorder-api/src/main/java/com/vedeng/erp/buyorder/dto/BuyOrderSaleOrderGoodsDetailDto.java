package com.vedeng.erp.buyorder.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class BuyOrderSaleOrderGoodsDetailDto extends BaseDto {

    /**
     * 关联订单主键
     */
    private Integer buyorderExpenseJSaleorderId;

    /**
     * 销售订单ID
     */
    private Integer saleorderId;

    /**
     * 销售详情ID
     */
    private Integer saleorderGoodsId;

    /**
     * 费用订单ID
     */
    private Integer buyorderExpenseId;

    /**
     * 采购费用明细ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * 订单归属人
     */
    private String applicantName;

    /**
     * 需采数量
     */
    private Integer num;

    /**
     * 已采数量
     */
    private Integer alreadyNum;

    /**
     * 采购数量
     */
    private Integer buyNum;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 货期
     */
    private Integer deliveryCycle;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 内部备注
     */
    private String insideComments;

    /**
     * 产品备注
     */
    private String goodsComments;

    /**
     * 归属人ID
     */
    private Integer userId;

    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 订单类型
     */
    private Integer orderType;

}
