package com.vedeng.order.model;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AuthorizationApply {

    /**
     * 授权书申请主键
     */
    private Integer authorizationApplyId;

    /**
     * 授权书申请号
     */
    private String authorizationApplyNum;

    /**
     * 报价单id
     */
    private Integer quoteorderId;

    /**
     * skuid
     */
    private Integer skuId;

    /**
     * 采购单位/招标公司
     */
    private String purchaseOrBidding;

    /**
     * 生产厂家
     */
    private String productCompany;

    /**
     * 经营性质1为生产2为代理3为销售
     */
    private Integer natureOfOperation;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 产品型号
     */
    private String skuModel;

    /**
     * 经销类型1为独家经销商2为经销商3为代理商
     */
    private Integer distributionsType;

    /**
     * 授权公司
     */
    private String authorizedCompany;

    /**
     * 采购项目全程
     */
    private String purchaseProjectName;

    /**
     * 采购项目编号
     */
    private String purchaseProjectNum;

    /**
     * 文件类型，1为投标2为响应
     */
    private Integer fileType;

    /**
     * 售后公司全称
     */
    private String aftersalesCompany;

    /**
     * 授权有限期开始日期
     */
    private String beginTime;

    /**
     * 授权有效期结束日期
     */
    private String endTime;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long modTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updator;

    /**
     * 申请人
     */
    private String applyPerson;

    /**
     * 当前审核人
     */
    private String reviewer;

    /**
     * 描述
     */
    private String described;

    /**
     * 份数
     */
    private Integer num;

    /**
     * 审核状态
     */
    private Integer applyStatus;

    /**
     * 授权书提交年月
     */
    private String yearAndMonth;

    /**
     * 是否标准模板
     */
    private Integer standardTemplate;

    /**
     * 注释
     */
    private String comment;

    /**
     * 申请年份
     */
    private String applyYear;

    /**
     * 申请月份
     */
    private String applyMonth;

    /**
     * 申请日期
     */
    private String applyDay;


    /**
     * 非标授权书附件链接
     */
    private String nonStandardAuthorizationUrl;

    /**
     * 非标授权书附件名称
     */
    private String nonStandardAuthorizationName;

    /**
     * 非标授权书签章附件链接
     */
    private String nonStandardAuthorizationSignUrl;

    /**
     * 非标授权书签章附件名称
     */
    private String nonStandardAuthorizationSignName;

    /**
     * 附件是否可签章
     */
    private Integer whetherSign;

    /**
     * 0.项目授权 1.经销授权
     */
    private Integer authType;

    /**
     * 公章类型 1.南京贝登医疗股份有限公司 2.南京医购优选供应链管理有限公司
     */
    private Integer sealType;

    public void setPurchaseProjectName(String purchaseProjectName) {
        this.purchaseProjectName = StrUtil.trim(purchaseProjectName);
    }

    public String getPurchaseProjectName() {
        return StrUtil.trim(purchaseProjectName);

    }

    public void setPurchaseProjectNum(String purchaseProjectNum) {
        this.purchaseProjectNum = StrUtil.trim(purchaseProjectNum);
    }

    public String  getPurchaseProjectNum() {
        return StrUtil.trim(purchaseProjectNum);
    }


}

