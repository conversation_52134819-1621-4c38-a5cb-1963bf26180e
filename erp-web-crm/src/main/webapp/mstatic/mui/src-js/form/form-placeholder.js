
Vue.component('ui-placeholder', {
    template: `
        <div class="form-placeholder" @click="handlerClick">
            <span>
                <slot></slot>
            </span>
            <i :class="['vd-ui_icon', icon]"></i>
        </div>
    `,
    props: {
        icon: {
            type: String,
            default: 'icon-right'
        },
    },
    data() {
        return {
        };
    },
    mounted() {
    },
    methods: {
        handlerClick () {
            this.$emit('click');
        }
    }
})