package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.aftersale.domain.entity.AfterSaleAuditInfoEntity;
import com.vedeng.erp.aftersale.mapper.AfterSaleAuditInfoMapper;
import com.vedeng.erp.saleorder.service.AfterSaleAuditInfoService;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2023-01-09
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@Slf4j
public class AfterSaleAuditInfoServiceImpl implements AfterSaleAuditInfoService {
    @Autowired
    private AfterSaleAuditInfoMapper afterSaleAuditInfoMapper;

    @Override
    public Set<String> getProductManageAndAsistNameList(List<ProductManageAndAsistDto> manageAndAsistDtoList) {
        Set<String> manageAndAsistNameSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductAssitName());
            }

            if (StringUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductManageName());
            }
        });

        return manageAndAsistNameSet;
    }

    @Override
    public List<Integer> getProductManageAndAsistIdList(List<ProductManageAndAsistDto> manageAndAsistDtoList) {
        Set<Integer> manageAndAsistNameIdSet = new HashSet<>();

        manageAndAsistDtoList.stream().forEach(manageAndAsist -> {

            if (StringUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductAssitUserId());
            }

            if (StringUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductManageUserId());
            }

        });

        return new ArrayList<>(manageAndAsistNameIdSet);
    }

    @Override
    public void insertAfterSaleAuditInfo(List<ProductManageAndAsistDto> manageAndAsistDtoList, AfterSalesVo afterSalesInfo) {
        for (ProductManageAndAsistDto productManageAndAsistDto : manageAndAsistDtoList) {
            //保存至销售售后产品审核信息表
            AfterSaleAuditInfoEntity afterSaleAuditInfoEntity=new AfterSaleAuditInfoEntity();
            afterSaleAuditInfoEntity.setAfterSaleId(afterSalesInfo.getAfterSalesId());
            afterSaleAuditInfoEntity.setSkuNo(productManageAndAsistDto.getSkuNo());
            afterSaleAuditInfoEntity.setUserId(productManageAndAsistDto.getProductAssitUserId() + "," + productManageAndAsistDto.getProductManageUserId());
            afterSaleAuditInfoEntity.setUserName(productManageAndAsistDto.getProductAssitName() + "," + productManageAndAsistDto.getProductManageName());
            afterSaleAuditInfoMapper.insertSelective(afterSaleAuditInfoEntity);
        }
    }

    @Override
    public List<AfterSaleAuditInfoEntity> findGoodsAuditList(AfterSaleAuditInfoEntity entity) {
        return afterSaleAuditInfoMapper.findAuditListByAfterSaleorderId(entity);
    }

    @Override
    public void clearAuditInfoByAfterSaleId(Integer afterSaleId) {
        afterSaleAuditInfoMapper.clearAuditInfoByAfterSaleId(afterSaleId);
    }
}
