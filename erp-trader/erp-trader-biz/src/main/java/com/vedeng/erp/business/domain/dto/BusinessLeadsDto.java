package com.vedeng.erp.business.domain.dto;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.system.dto.CustomDataOperDto;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 商机线索
 * @date 2022/7/8 14:35
 */
@Getter
@Setter
public class BusinessLeadsDto extends BaseDto {


    /**
     * 创建时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date addTime;

    /**
     * 修改时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date modTime;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 线索编号
     */
    private String leadsNo;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 客户id-traderCustomerId
     */
    private Integer traderCustomerId;

    private Integer  customerNature;

    /**
     * 客户名称
     */
    @ExcelProperty("客户名称")
    private String traderName;

    /**
     * 返回给crm的前端erp的超链接
     */
    private String traderNameLink;

    /**
     * ERP内部链接
     */
    private String traderNameInnerLink;

    /**
     * 联系人
     */
    @ExcelProperty("* 联系人")
    private String contact;

    /**
     * 手机号
     */
    @ExcelProperty("* 手机")
    private String phone;

    /**
     * 电话
     */
    @ExcelProperty("电话")
    private String telephone;

    /**
     * 产品信息
     */
    @ExcelProperty("产品信息")
    private String goodsInfo;

    /**
     * 详细地址
     */
    @ExcelProperty("地区（省、市、区、详细地址）")
    private String address;

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;

    /**
     * 线索跟进状态(0未分配，1.未处理、2.跟进中、3.已关闭、4.已商机)
     */
    private Integer followStatus;

    /**
     * 多选搜索
     * 线索跟进状态(1.未处理、2.跟进中、3.已关闭、4.已商机)
     */
    private List<Integer> followStatusList;

    /**
     * 首次跟进时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date firstFollowTime;

    /**
     * 跟进图片地址：英文逗号分割的地址
     */
    private List<String> followPicList;

    /**
     * 线索类型（1.销售自拓线索）
     */
    private Integer type;

    /**
     * 线索分级（0无效 1有效）
     */
    private Integer status;

    /**
     * 无效原因
     */
    private String invalidReason;

    /**
     * 线索标签id string
     */
    private String tagIds;

    /**
     * 线索标签id list
     */
    private List<Integer> tagIdList;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人
     */
    private String belonger;

    /**
     * 归属人头像
     */
    private String belongerPic;


    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 县id
     */
    private Integer countyId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 是否转商机
     */
    private Boolean turnBusinessChanceFlag;


    /**
     * 快速检索
     */
    private Integer quickSearch;

    /**
     * 当前时间-线索创建时间大于72h的日期
     */
    private Date quickSearchDate;

    /**
     * 联系方式
     */
    private String contactWay;

    /**
     * 归属人IdList
     */
    private List<Integer> belongerIdList;


    /**
     * 是否关闭
     */
    private Boolean closeFlag;

    /**
     * 下次沟通日期
     */
    @JsonFormat(pattern="yyyy-MM-dd", timezone="GMT+8")
    private Date nextCommunicationDate;

    /**
     * 关闭时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date closeTime;

    /**
     * 转商机时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date turnBusinessChanceTime;

    /**
     * 分配时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone="GMT+8")
    private Date assignTime;

    /**
     * 关闭理由
     */
    private String closeReason;

    /**
     * 关闭原因类型
     * 1无法取得联系/2招投标无授权/3产品不在经营范围/4产品不在经营区域/5仅咨询技术问题/6客户没有购买意愿/7没有竞争力、价格没优势/8其他
     */
    private Integer closeReasonType;

    /**
     * 关闭原因类型对应的描述
     */
    private String closeReasonTypeName;



    /**
     * 联系人Id
     */
    private Integer traderContactId;

    /**
     * 最后一次沟通记录
     */
    private CommunicateRecordDto communicateRecordDto;

    /**
     * 置顶
     */
    private CustomDataOperDto customDataOperDto;

    /**
     * 模糊查询聚合字段
     */
    private String mergeFiled;

    /**
     * 省ids
     */
    private Set<Integer> provinceIdList = new HashSet<>();
    /**
     * 市ids
     */
    private Set<Integer> cityIdList = new HashSet<>();
    /**
     * 县ids
     */
    private Set<Integer> countyIdList = new HashSet<>();

    /**
     * 标签
     */
    private List<CustomTagDto> tags;


    /**
     * 商机id
     */
    private Integer businessChanceId;


    /**
     * 下次沟通日期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date nextCommunicationDateStart;

    /**
     * 下次沟通日期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date nextCommunicationDateEnd;


    /**
     * 分配时间 转商机时间 关闭时间 创建时间
     * 搜索时间
     */
    private String searchTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date endTime;

    /**
     * 沟通记录 搜索条件
     */
    private String communicateContent;


    /**
     * 目标用户id集合
     */
    private List<Integer> userIdList;

    /**
     * 所有下属用户id集合
     */
    private List<Integer> allSubordinateUserIdList;

    /**
     * 当前登陆用户
     */
    private Integer currentUserId;

    /**
     * 创建人
     */
    private String creatorName;

    /**
     * 创建人头像
     */
    private String creatorPic;


    public Date getNextCommunicationDate() {
        return communicateRecordDto != null ? communicateRecordDto.getNextContactDate() : null;
    }

    public Date getQuickSearchDate() {
        return quickSearch != null ? DateUtil.offsetHour(new Date(), -72) : null;
    }


    /**
     * 线索类型字典库
     */
    private Integer clueType;

    /**
     * 线索类型-集合
     */
    private List<Integer> clueTypeList;


    private String clueTypeName;

    /**
     * 询价行为字典库
     */
    private Integer inquiry;

    /**
     *  询价行为-集合
     */
    private List<Integer> inquiryList;

    private String inquiryName;

    /**
     * 渠道类型字典库
     */
    private Integer source;

    /**
     * 渠道类型
     */
    private String sourceName;

    /**
     * 渠道类型集合
     */
    private List<Integer> sourceIdList;

    /**
     * 渠道名称字典库
     */
    private Integer communication;

    /**
     * 渠道名称
     */
    private String communicationName;

    /**
     * 渠道名称集合
     */
    private List<Integer> communicationIdList;

    /**
     * 三级分类
     */
    private String content;

    /**
     * 其他联系方式
     */
    private String otherContactInfo;

    /**
     * 线索合并状态，0未合并，1被合并，2合并其他的
     */
    private Integer mergeStatus;

    /**
     * 是否分配销售 1未分配 2已分配
     */
    private Integer assignOrNot;

    /**
     * 咨询入口
     */
    private Integer entrances;

    /**
     * 咨询入口-集合
     */
    private List<Integer> entrancesList;

    /**
     * 功能-名称
     */
    private String entrancesName;

    /**
     * 功能
     */
    private Integer functions;

    private List<Integer> functionsList;


    /**
     * 渠道类型
     */
    private String functionsName;

    /**
     * 查询所有数据:1表示查询所有数据，给总机使用
     */
    private Integer queryAll;

    private List<Integer> creatorUserIdList;//创建人ID集合

    private List<Integer> shareUserIdList;//被分享人ID

    private List<String> traderNameList;//客户名称集合

    private String startAddTime;

    private String endAddTime;

    private String startTurnBusinessChanceTime;

    private String endTurnBusinessChanceTime;

    private String startCloseTime;

    private String endCloseTime;

    private String startAssignTime;

    private String endAssignTime;

    private String startLastCommunicationTime; //最近一条沟通记录的创建时间-开始时间

    private String endLastCommunicationTime;//最近一条沟通记录的创建时间-结束时间


    /**
     * 是否发送微信标识
     */
    private  String sendVx;

    /**
     * 天眼查标识
     */
    private String tycFlag;

    //线索和商机合并新建之后，新增的几个字段
    /**
     * 预计成单金额-列表字段
     */
    private BigDecimal amount;

    /**
     * 预计成单日期-列表字段
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
//    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Long orderTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderTimeDate;

    /**
     * 采购方式-字典值-（直接采购 招投标采购）-搜索用
     */
    private Integer purchasingType;

    /**
     * 终端名称-搜索用
     */
    private String terminalTraderName;

    /**
     * 终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
     */
    private Integer terminalTraderNature;

    /**
     * 终端性质-名称
     */
    private String terminalTraderNatureStr;

    /**
     * 终端区域(省市区id，逗号分隔)
     */
    private String terminalTraderRegion;

    /**
     * 终端区域名称
     */
    private String terminalTraderRegionStr;


    /**
     * （业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
     */
    private Integer businessType;

    /**
     * 商机类型-页面回显字段
     */
    private String businessTypeStr;

    /**
     * 客情关系(1熟悉终端决策人，2熟悉使用人，多选逗号分隔)
     */
    private String customerRelationship;

    /**
     * 客情关系-详情展示用
     */
    private List<String> customerRelationshipStr;


    /**
     * 招投标阶段-字典值(提案咨询、立项论证、意向公示、公开招标、合同签署)
     */
    private Integer biddingPhase;

    /**
     * 招标参数(1可调整，2不可调整)-搜索用
     */
    private Integer biddingParameter;


    /**
     * 关联终端信息
     */
    private OrderTerminalDto orderTerminalDto;

    /**
     * 采购方式-名称
     */
    private String purchasingTypeStr;

    /**
     * 招投标阶段-名称
     */
    private String biddingPhaseStr;

    /**
     * 联系方式字段搜索
     */
    private String contactMerge;

    /**
     * 列表展示商品分类
     */
    private List<String> categoryList;


    /**
     * 分类ids
     */
    private String categoryIds;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 商品分类ID列表
     */
    private List<Integer> categoryIdList;

    /**
     * 省份ID列表
     */
    private List<Integer> provinceIds;

    /**
     * 城市ID列表
     */
    private List<Integer> cityIds;
    

    /**部门ID列表*/
	private List<Integer> organizationIdList;
    
    /**属于部门ID列表下的所有用户ID*/
    private List<Integer> belongOrgUserIdList;
}
