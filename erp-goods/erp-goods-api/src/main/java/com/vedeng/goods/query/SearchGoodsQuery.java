package com.vedeng.goods.query;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SearchGoodsQuery {

    /**
     * 搜索关键字 - 包含订货号skuNo、商品名称skuName以及(制造商)型号model/规格spec
     */
    private String searchKeyword;

    /**
     * (筛选项)三级分类
     * -   根据搜索结果中三级分类的SKU数量降序排列，数量相同时自然排序，有多少展示多少
     */
    private Integer thirdCategoryId;

    /**
     * (筛选项)品牌
     * -   根据搜索结果中品牌的SKU数量降序排列，数量相同时自然排序，有多少展示多少
     */
    private List<Integer> brandIdList;

    /**
     * (筛选项 true-有库存 false-无库存)库存
     */
    private Boolean availableStockFlag;


    /**
     * 排序
     * -  0 综合排序   按照商品创建时间降序排列，时间相同按照则自然排序
     * -  1 销量      点击将列表商品进行降序排列，销量相同时自然排序
     * -  2 终端价    点击将列表商品市场价进行升降序排列，价格相同时自然排序
     * -  3 经销价    点击将列表商品经销价进行升降序排列，价格相同时自然排序
     */
    private Integer sortType;

    /**
     * 升序或者降序 1-升 0-降
     */
    private Integer lift;

    /**
     *页码 默认1
     */
    private Integer pageNumber=1;

    /**
     *页容量 默认10
     */
    private Integer pageSize=10;


    /**
     * 是否是count接口
     */
    private Boolean countFlag = false;

}
