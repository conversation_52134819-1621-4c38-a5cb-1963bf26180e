package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.dto.AcceptanceBillDto;
import com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity;
import com.vedeng.erp.finance.mapper.AcceptanceBillMapper;
import com.vedeng.erp.finance.service.AcceptanceBillService;
import com.vedeng.infrastructure.bank.api.domain.*;
import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class AcceptanceBillServiceImpl implements AcceptanceBillService{

    @Autowired
    private AcceptanceBillMapper acceptanceBillMapper;


    @Autowired
    private AcceptanceBillApiService acceptanceBillApiService;


    @Value("${custAccount}")
    private String custAccount;

    @Value("${shoucustAccount:*********}")
    private String shouCustAccount;

    @Value("${bank.api.shouwreceiverBank:}")
    private String shouwreceiverBank;

    @Value("${bank.api.shoureceiverBankCode:}")
    private String shoureceiverBankCode;

    @Value("${bank.api.shoureceiverBankNo:}")
    private String shoureceiverBankNo;

    @Value("${bank.api.shouaoAcctNo:}")
    private String shouaoAcctNo;

    @Value("${bank.api.shouaoAcctName:}")
    private String shouaoAcctName;

    @Value("${bank.api.shouaoBankNo:}")
    private String shouaoBankNo;




    @Override
    public int deleteByPrimaryKey(Long acceptanceBilId) {
        return acceptanceBillMapper.deleteByPrimaryKey(acceptanceBilId);
    }

    @Override
    public int insert(AcceptanceBillEntity record) {
        return acceptanceBillMapper.insert(record);
    }

    @Override
    public int insertSelective(AcceptanceBillEntity record) {
        return acceptanceBillMapper.insertSelective(record);
    }

    @Override
    public AcceptanceBillEntity selectByPrimaryKey(Long acceptanceBilId) {
        return acceptanceBillMapper.selectByPrimaryKey(acceptanceBilId);
    }

    @Override
    public int updateByPrimaryKeySelective(AcceptanceBillEntity record) {
        return acceptanceBillMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(AcceptanceBillEntity record) {
        return acceptanceBillMapper.updateByPrimaryKey(record);
    }

    @Override
    public PageInfo<AcceptanceBillDto> page(PageParam<AcceptanceBillDto> query) {
        log.info("page:入参:{}", JSON.toJSONString(query));
        AcceptanceBillDto param = query.getParam();
        return PageHelper.startPage(query).doSelectPageInfo(() -> acceptanceBillMapper.findByAll(param));
    }

    @Override
    public List<AcceptanceBillEntity> findByBillNumber(String billNumber) {
        return acceptanceBillMapper.findByBillNumber(billNumber);
    }

    @Override
    public List<AcceptanceBillEntity> findByBankBillIdIn(List<Integer> bankBillIdCollection) {
        return acceptanceBillMapper.findByBankBillIdIn(bankBillIdCollection);
    }

    @Override
    public List<AcceptanceBillEntity> findAcceptanceBillNonTerminatedState() {
        return acceptanceBillMapper.findAcceptanceBillNonTerminatedState();
    }

    @Override
    public boolean signup(String billNumber) {
        B2eNbsQryStaySignUpDraftsReqBody body = createSignUpBody(billNumber);

        B2eNbsDraftSignUpResponse signUpResponse =  acceptanceBillApiService.b2eNbsDraftSignUp(body);
        boolean checkSignUpResult = checkSignUpResult(signUpResponse);
        if(checkSignUpResult){

            List<AcceptanceBillEntity> dataList = acceptanceBillMapper.findByBillNumber(billNumber);
            if(CollectionUtil.isNotEmpty(dataList)){
                AcceptanceBillEntity billEntityUpdate = new AcceptanceBillEntity();
                billEntityUpdate.setAcceptanceBilId(dataList.get(0).getAcceptanceBilId());
                billEntityUpdate.setBillNumber(billNumber);
                billEntityUpdate.setBillStatus(3);//已收票
                billEntityUpdate.setModTime(new Date());
                acceptanceBillMapper.updateByPrimaryKeySelective(billEntityUpdate);
            }

        }
        log.info("signup result:"+checkSignUpResult);
        return checkSignUpResult;
    }

    @Override
    public boolean draftDiscount(String billNumber,String rate,Integer userId) {
        List<AcceptanceBillEntity> billEntityList =  findByBillNumber(billNumber);
        if(CollectionUtil.isEmpty(billEntityList)){
            return false;
        }
        AcceptanceBillEntity entity = billEntityList.get(0);
        B2eNbsDraftDiscountReqBody reqBody = createB2eNbsDraftDiscountReqBody(entity,rate);
        B2eNbsDraftDiscountResponse response = this.acceptanceBillApiService.b2eNbsDraftDiscount(reqBody);
        boolean draftDiscountStatus = checkDraftDiscountResult(response);
        if(draftDiscountStatus){
            //成功：更新贴现状态 = 贴现中；
            AcceptanceBillEntity billEntityUpdate = new AcceptanceBillEntity();
            billEntityUpdate.setAcceptanceBilId(entity.getAcceptanceBilId());
            billEntityUpdate.setBillNumber(billNumber);
//            billEntityUpdate.setRate();//贴现利率
            billEntityUpdate.setDiscountStatus(2);//贴现状态 1.未贴现，2.贴现中，3.已贴现，4.贴现失败
            billEntityUpdate.setModTime(new Date());
            billEntityUpdate.setUpdater(userId);
            acceptanceBillMapper.updateByPrimaryKeySelective(billEntityUpdate);
            checkDraftDiscountStatus(billNumber);
            return true;
        }else{
            String errorMessage = checkDraftDiscountResultErrorMessage(response);
            if(StringUtils.isNotBlank(errorMessage)){
                throw new RuntimeException(errorMessage);
            }
            checkDraftDiscountStatus(billNumber);
            throw new RuntimeException(getErrorMessage(response));
        }

    }

    @Override
    public void refreshDraftDiscount() {
        List<AcceptanceBillEntity> billEntityList =  acceptanceBillMapper.findDraftDiscountProcessing();
        log.info("共有贴现中的承兑汇票为:"+billEntityList.size());
        for(AcceptanceBillEntity billEntity:billEntityList){
            this.checkDraftDiscountStatus(billEntity.getBillNumber());
        }
    }

    @Override
    public void autoSignup() {
        List<AcceptanceBillEntity> billEntityList =  acceptanceBillMapper.findSignupList();
        log.info("共有待签收(已承兑)中的承兑汇票为:"+billEntityList.size());
        for(AcceptanceBillEntity billEntity:billEntityList){
            this.signup(billEntity.getBillNumber());
        }
    }

    private void checkDraftDiscountStatus(String billNumber){
        B2eNbsQryDraftTransStatusReqBody reqBody = createB2eNbsQryDraftTransStatusReqBody(billNumber);
        B2eNbsQryDraftTransStatusResponse statusResponse = this.acceptanceBillApiService.b2eNbsQryDraftTransStatus(reqBody,true);
        String draftTranStatus = checkDraftTranStatus(statusResponse);
        List<AcceptanceBillEntity> dataList = acceptanceBillMapper.findByBillNumber(billNumber);
        switch (draftTranStatus){
            case "0":
                break;
            case "1":
                break;
            case "2":
                AcceptanceBillEntity billEntityUpdate = new AcceptanceBillEntity();
                billEntityUpdate.setAcceptanceBilId(dataList.get(0).getAcceptanceBilId());
                billEntityUpdate.setBillNumber(billNumber);
                billEntityUpdate.setDiscountStatus(3);//贴现状态 1.未贴现，2.贴现中，3.已贴现，4.贴现失败
                billEntityUpdate.setModTime(new Date());
                acceptanceBillMapper.updateByPrimaryKeySelective(billEntityUpdate);
                break;
            case "3":
                AcceptanceBillEntity billEntityUpdate4 = new AcceptanceBillEntity();
                billEntityUpdate4.setAcceptanceBilId(dataList.get(0).getAcceptanceBilId());
                billEntityUpdate4.setBillNumber(billNumber);
                billEntityUpdate4.setDiscountStatus(4);//贴现状态 1.未贴现，2.贴现中，3.已贴现，4.贴现失败
                billEntityUpdate4.setModTime(new Date());
                acceptanceBillMapper.updateByPrimaryKeySelective(billEntityUpdate4);
                break;
            default:
                ;
        }
       return ;

    }

    /**
     * 检查贴现的处理结果:<br/>
     * 0：待处理<br/>
     * 1：处理中<br/>
     * 2：处理成功<br/>
     * 3：处理失败<br/>
     * @param statusResponse
     * @return
     */
    private String checkDraftTranStatus(B2eNbsQryDraftTransStatusResponse statusResponse){
        if(statusResponse!=null && statusResponse.isSuccess() && statusResponse.getXDataBody()!=null
                && CollectionUtil.isNotEmpty(statusResponse.getXDataBody().getList())
        ){
            List<B2eNbsQryDraftTransStatusResponse.BillInfo> list = statusResponse.getXDataBody().getList();
            //同一个票据，可以发起多次贴现申请，因此根据以下状态优先级取状态，作为更新ERP本地数据的依据。
            // 优先级顺序：2 -> 1 -> 3 -> 0
            String[] priorityStatusCodes = {"2",  "1", "3","0"};
            for (String priorityStatusCode : priorityStatusCodes) {
                for (B2eNbsQryDraftTransStatusResponse.BillInfo billInfo : list) {
                    if (priorityStatusCode.equals(billInfo.getStatusCode())) {
                        return priorityStatusCode;
                    }
                }
            }
        }
        return StringUtils.EMPTY;
    }


    /**
     * 承兑汇票-接口：票据交易状态查询(B2eNbsQryDraftTransStatus)<br/> 请求的参数对象组装
     * @param billNumber
     * @return
     */
    private B2eNbsQryDraftTransStatusReqBody createB2eNbsQryDraftTransStatusReqBody(String billNumber ) {
        B2eNbsQryDraftTransStatusReqBody reqBody = new B2eNbsQryDraftTransStatusReqBody();
        reqBody.setTrnId(IdUtil.simpleUUID());
        reqBody.setCustAccount(shouCustAccount);//此处使用收票方的账号
        reqBody.setOperType("1");
        reqBody.setInsId("");
        reqBody.setBusiStage("03");
        reqBody.setBillType("AC01");
        reqBody.setBeginAcptDt("");
        reqBody.setEndAcptDt("");
        reqBody.setBeginEndDate("");
        reqBody.setEndDate("");
        reqBody.setBeginApplyDt("");
        reqBody.setEndApplyDt("");
        reqBody.setBillNo(billNumber);
        reqBody.setPageNo("");
        reqBody.setPageSize("");
        return  reqBody;
    }


    /**
     * 接口名称：贴现申请(B2eNbsDraftDiscount)<br/> 请求的参数对象组装
     * @param entity
     * @param rate
     * @return
     */
    private B2eNbsDraftDiscountReqBody createB2eNbsDraftDiscountReqBody(AcceptanceBillEntity entity ,String rate){
        B2eNbsDraftDiscountReqBody requestBody = new B2eNbsDraftDiscountReqBody();
        B2eNbsDraftDiscountReqBody.BillDiscountMap billDiscountMap = new B2eNbsDraftDiscountReqBody.BillDiscountMap();
        billDiscountMap.setBillNo(entity.getBillNumber());
        billDiscountMap.setBillRangeStart(entity.getSubBillIntervalStart());
        billDiscountMap.setBillRangeEnd(entity.getSubBillIntervalEnd());//
        billDiscountMap.setDueDt(DateUtil.format(entity.getMaturityDate(),"yyyyMMdd")); // maturityDate 汇票到期:yyyyMMdd
        // 设置小数位数为两位，并使用四舍五入模式
        BigDecimal billAmountRounded = entity.getBillAmount().setScale(2, RoundingMode.DOWN);
        billDiscountMap.setTransAmt(billAmountRounded.toString());

        List<B2eNbsDraftDiscountReqBody.BillDiscountMap > billMapList = new ArrayList<>();
        billMapList.add(billDiscountMap);
        requestBody.setList(billMapList);
        requestBody.setTrnId(IdUtil.simpleUUID());
        requestBody.setInsId(IdUtil.simpleUUID());
        requestBody.setCustAccount(shouCustAccount);
        requestBody.setDiscountBank("1");
        requestBody.setReceiverAcctNo("0");
        requestBody.setReceiverBank(shouwreceiverBank);
        requestBody.setReceiverBankCode(shoureceiverBankCode);
        requestBody.setReceiverBankNo(shoureceiverBankNo);
        requestBody.setAoAcctNo(shouaoAcctNo);
        requestBody.setAoAcctName(shouaoAcctName);
        requestBody.setAoBankNo(shouaoBankNo);
        requestBody.setAoAcctBankCode("");
        requestBody.setBanEndrsmtMark("EM01");
        requestBody.setDiscType("RM00");
        requestBody.setSttlmMk("ST02");
        requestBody.setDiscRate(rate);
        requestBody.setDiscDt(getCurrentDate());
        requestBody.setPayType("2");
        requestBody.setBuyPayPcet("");
        requestBody.setRemark("");
        requestBody.setRedeemOpenDt("");
        requestBody.setRedeemDueDt("");
        requestBody.setRedeemRate("");
        requestBody.setRedeemRateType("");
        requestBody.setRedeemAmt("");

        return requestBody;

    }

    private static final String CODE_SUCCESS = "1";


    private static final String STATUS_CODE_2 = "2";//处理成功  0：待处理/1：处理中/2：处理成功/3：处理失败


    private String getCurrentDate(){
        return DateUtil.format(new Date(),"yyyyMMdd");
    }




    private boolean checkSignUpResult(B2eNbsDraftSignUpResponse signUpResponse){
        if(signUpResponse!=null && signUpResponse.isSuccess() && signUpResponse.getXDataBody()!=null
                && CollectionUtil.isNotEmpty(signUpResponse.getXDataBody().getList())
                && CODE_SUCCESS.equals(signUpResponse.getXDataBody().getList().get(0).getRetCode())
            ){
            return true;

        }
        return false;
    }

    private boolean checkDraftDiscountResult(B2eNbsDraftDiscountResponse draftDiscountResponse){
        if(draftDiscountResponse!=null && draftDiscountResponse.isSuccess() && draftDiscountResponse.getXDataBody()!=null
                && CollectionUtil.isNotEmpty(draftDiscountResponse.getXDataBody().getList())
                && CODE_SUCCESS.equals(draftDiscountResponse.getXDataBody().getList().get(0).getRetCode())
        ){
            return true;

        }
        return false;
    }
    private String checkDraftDiscountResultErrorMessage(B2eNbsDraftDiscountResponse draftDiscountResponse){
        if(draftDiscountResponse!=null && draftDiscountResponse.isSuccess() && draftDiscountResponse.getXDataBody()!=null
                && CollectionUtil.isNotEmpty(draftDiscountResponse.getXDataBody().getList())
                && !CODE_SUCCESS.equals(draftDiscountResponse.getXDataBody().getList().get(0).getRetCode())
        ){
            return draftDiscountResponse.getXDataBody().getList().get(0).getRetMsg();

        }
        return null;
    }

    private String getErrorMessage(BankResponse bankResponse){
        if(bankResponse!=null && bankResponse.getResponseHeader() != null
                && bankResponse.getResponseHeader().getStatus() !=null
                && StringUtils.isNotBlank(bankResponse.getResponseHeader().getStatus().getMessage())
        ){
            return bankResponse.getResponseHeader().getStatus().getMessage();

        }
        return "未获取到银行服务接口成功的标识，请检查";
    }


    /**
     *可签收列表查询-使用收票方的账号进行操作
     * @param billNumber
     * @return
     */
    private B2eNbsQryStaySignUpDraftsReqBody createSignUpBody(String billNumber){

        B2eNbsQryStaySignUpDraftsReqBody requestBody = new B2eNbsQryStaySignUpDraftsReqBody();
        requestBody.setTrnId(IdUtil.simpleUUID());
        requestBody.setCustAccount(shouCustAccount);
        requestBody.setQueryType("02");
        requestBody.setPageNo("");
        requestBody.setPageSize("");
        requestBody.setBillNo(billNumber);
        requestBody.setBillType("AC01");
        requestBody.setMinBillMoney("");
        requestBody.setMaxBillMoney("");
        requestBody.setBeginAcptDt("");
        requestBody.setEndAcptDt("");
        requestBody.setBeginEndDate("");
        requestBody.setEndDate("");
        return requestBody;

    }


}
