package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchBuyorderExpenseDto;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface BatchBuyorderExpenseDtoMapper {

    /**
     * 获取采购费用单
     *
     * @param expenseBuyorderExpenseDto expenseBuyorderExpenseDto
     * @return List<BatchBuyorderExpenseDto>
     */
    List<BatchBuyorderExpenseDto> findByAll(BatchBuyorderExpenseDto expenseBuyorderExpenseDto);


    String getAfterSalesNoByBuyorderExpenseId(@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 获取售后单号
     * @param expenseAfterSalesId
     * @return
     */
    String getAfterSalesNo(@Param("expenseAfterSalesId") Integer expenseAfterSalesId);

    /**
     * 获取售后费用单创建时间
     * @param expenseAfterSalesId
     * @return
     */
    Date getAfterSalesTime(@Param("expenseAfterSalesId") Integer expenseAfterSalesId);

    /**
     * 根据采购费用单号获取供应商id
     */
    Integer findTraderSupplierIdByBuyorderExpenseNo(@Param("buyorderExpenseNo") String buyorderExpenseNo);

    /**
     * 根据费用售后订单号获取供应商id
     */
    Integer findTraderSupplierIdByExpenseAfterSalesNo(@Param("expenseAfterSalesNo") String expenseAfterSalesNo);

}