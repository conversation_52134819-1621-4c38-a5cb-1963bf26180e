package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;
import com.vedeng.erp.trader.service.TraderDealerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 经销商 相关数据
 * @date 2023/8/17 8:54
 **/
@ExceptionController
@RestController
@RequestMapping("/traderDealerApi")
@Slf4j
public class TraderDealerApi {


    @Autowired
    private TraderDealerService traderDealerService;

    @RequestMapping(value = "/traderDealerFrontData")
    @NoNeedAccessAuthorization
    public R<TraderDealerFrontDto> traderDealerFrontData(Integer traderCustomerId) {
        return R.success(traderDealerService.traderDealerFrontData(traderCustomerId));
    }
}
