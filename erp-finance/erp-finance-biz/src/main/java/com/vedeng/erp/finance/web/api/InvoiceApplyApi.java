package com.vedeng.erp.finance.web.api;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.finance.dto.AfterSaleApplyDto;
import com.vedeng.erp.finance.dto.AfterSaleApplyInfoDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRuleDto;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Author: ckt
 * @Date: 2023/10/21
 * @Description: 开票申请
 *
 */

@ExceptionController
@RestController
@Slf4j
@RequestMapping("/invoiceApply/api")
public class InvoiceApplyApi {
    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;
    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET,value = "/checkInvoiceApplyStStatus")
    @ResponseBody
    public R<?> checkInvoiceApplyStStatus(Integer invoiceApplyId){
        InvoiceApplyDto invoiceApply = invoiceApplyApiService.getInvoiceApply(invoiceApplyId);
        return R.success(invoiceApply.getValidStatus());
    }

    /**
     * 售后开票申请信息
     *
     * @param afterSalesId 售后单id
     * @return
     */
    @RequestMapping("/afterSaleApplyInfo")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> afterSaleApplyInfo(Integer afterSalesId) {
        log.info("售后开票申请信息入参：{}", afterSalesId);
        AfterSaleApplyInfoDto afterSaleApplyInfoDto = invoiceApplyApiService.afterSaleApplyInfo(afterSalesId);
        return R.success(afterSaleApplyInfoDto);
    }

    /**
     * 售后开票申请
     */
    @RequestMapping("/afterSaleApply")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> afterSaleApply(@RequestBody AfterSaleApplyDto afterSaleApplyDto) {
        log.info("售后开票申请,入参：{}", JSON.toJSONString(afterSaleApplyDto));
        invoiceApplyApiService.afterSaleApply(afterSaleApplyDto);
        return R.success();
    }
    /**
     * 开票配置-申请或开票规则
     */
    @RequestMapping("/getCheckRuleList")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getCheckRuleList(Integer ruleType) {
        List<InvoiceCheckRuleDto> checkRuleList = invoiceApplyApiService.getCheckRuleList(ruleType);
        return R.success(checkRuleList);
    }
}
