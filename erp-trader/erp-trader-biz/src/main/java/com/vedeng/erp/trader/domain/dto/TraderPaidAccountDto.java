package com.vedeng.erp.trader.domain.dto;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 交易者已交易账号
 * @date 2022/8/11 15:51
 */
@Getter
@Setter
public class TraderPaidAccountDto extends BaseDto {

    /**
     * 主键
     */
    private Integer traderPaidAccountId;

    /**
     * 交易者名称
     */
    @ExcelProperty("* 供应商名称")
    private String traderName;

    /**
     * 交易者银行交易账号
     */
    @ExcelProperty("* 供应商银行账号")
    private String traderPaidAccount;

    /**
     * 是否删除
     */
    private Integer isDel;

    public String getTraderName() {
        return StrUtil.trim(traderName);
    }

    public String getTraderPaidAccount() {
        return StrUtil.trim(traderPaidAccount);
    }


    public void setTraderName(String traderName) {
        this.traderName = StrUtil.trim(traderName);
    }

    public void setTraderPaidAccount(String traderPaidAccount) {
        this.traderPaidAccount = StrUtil.trim(traderPaidAccount);
    }
}
