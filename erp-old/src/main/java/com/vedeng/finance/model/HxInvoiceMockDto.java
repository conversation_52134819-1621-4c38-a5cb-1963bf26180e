package com.vedeng.finance.model;

import lombok.Data;

@Data
public class HxInvoiceMockDto {

    /**
     * 发票状态，0正常，1失控，2作废，3红冲，4异常
     */
    private String fpzt;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票金额
     */
    private String amount;

    /**
     * 销方名称
     */
    private String xfmc;

    /**
     * 销方地址电话
     */
    private String xfdzdh;

    /**
     * 销方开户行及账号
     */
    private String xfyhzh;

    /**
     * 购方税号
     */
    private String gfsh;

    /**
     * 购方名称
     */
    private String gfmc;

    /**
     * 购方地址电话
     */
    private String gfdzdh;

    /**
     * 购方开户行及账号
     */
    private String gfyhzh;

    /**
     * 开票日期
     */
    private String kprq;

    /**
     * 备注
     */
    private String bz;

    /**
     * 开票人
     */
    private String kpr;

    /**
     * 复核人
     */
    private String fhr;

    /**
     * 收款人
     */
    private String skr;
}
