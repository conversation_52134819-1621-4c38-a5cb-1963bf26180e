<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/ui.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/flowOrder/add.css?rnd=${resourceVersionKey}">

<div id="page-container" class="wrap">
    <template>
        <div class="container-l" :class="{hide: !isShowLeft}">
            <div class="l-title" v-if="isAudit">业务流转单号：{{ flowOrderNo }}</div>
            <div class="l-title" v-else>销售单号：{{ baseOrderNo }}</div>
            <div class="l-steps">
                <div class="step-list reverse">
                    <div class="step-item">
                        <div class="step-l">
                            <div class="step-l-txt">0级</div>
                        </div>
                        <div class="step-r">
                            <div class="step-company">{{ sourceErpName }}</div>
                        </div>
                    </div>
                    <div class="step-item" v-for="(item, index) in supplierData">
                        <div class="step-l">
                            <div class="step-l-txt">{{ index + 1 }}级</div>
                            <div class="step-gap">
                                <i class="el-icon-top"></i>
                            </div>
                        </div>
                        <div class="step-r">
                            <div class="step-company">
                                <div class="text-line-1" :title="item.traderName || ''">{{ item.traderName || '-' }}</div></div>
                            <div class="step-rate">
                                <div class="rate-item">加价率：{{ totalAddRate[index + 1] || '-' }}</div>
                                <div class="rate-item">毛利率：{{ totalProfitRate[index + 1] || '-' }}</div>
                            </div>
                            <div class="step-gap">
                                <i class="el-icon-top"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="l-trigger-show" @click="isShowLeft = !isShowLeft" :title="isShowLeft ? '收起' : '展开'">
                <i class="el-icon-arrow-left"></i>
            </div>
        </div>
        <div class="container-r" :class="{'left-hide': !isShowLeft}">
            <div class="content-block" v-if="isAudit">
                <div class="block-title">
                    单据信息
                </div>
                <div class="block-table">
                    <el-table :data="nodeInfoDate" size="mini" border style="width: 100%">
                        <el-table-column label="采购信息" align="center">
                            <el-table-column align="center" label="节点" width="60">
                                <template slot-scope="scope">
                                    {{ scope.row.buyOrderNode || '-' }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="采购单号" min-width="185">
                                <template slot-scope="scope">
                                    {{ scope.row.buyOrderNo || '-' }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="付款状态" width="110px">
                                <template slot-scope="scope">
                                    {{ getStatusDescription('付款状态',scope.row.buyOrderPayStatus) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="入库状态" width="110px">
                                <template slot-scope="scope">
                                    {{ getStatusDescription('入库状态',scope.row.buyOrderInStatus) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="收票状态" width="110px">
                                <template slot-scope="scope">
                                    {{ getStatusDescription('收票状态',scope.row.buyOrderInvoiceStatus) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="进项发票" min-width="100">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="text" v-if="scope.row.buyOrderNode" @click="openDialog(scope.row.buyOrderInvoiceDto)">查看</el-button>
                                    <span v-else>-</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="销售信息" align="center">
                            <el-table-column align="center" label="节点" width="60">
                                <template slot-scope="scope">
                                    {{ scope.row.saleOrderNode || '-'  }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="销售单号" min-width="185">
                                <template slot-scope="scope">
                                    {{ scope.row.saleOrderNo || '-'}}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="收款状态" width="110px">
                                <template slot-scope="scope">
                                    {{ getStatusDescription('收款状态',scope.row.saleOrderReceiveStatus) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="出库状态" width="110px">
                                <template slot-scope="scope">
                                    {{ getStatusDescription('出库状态',scope.row.saleOrderOutStatus) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="开票状态" width="110px">
                                <template slot-scope="scope">
                                    {{ getStatusDescription('开票状态',scope.row.saleOrderInvoiceStatus) }}
                                </template>
                            </el-table-column>
                            <el-table-column align="center" label="销项发票" min-width="100">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="text"  v-if="scope.row.saleOrderNode" @click="openDialog(scope.row.saleOrderInvoiceDto)">查看</el-button>
                                    <span v-else>-</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                        <el-table-column label="合同" align="center">
                            <el-table-column align="center" label="销售合同" min-width="120">
                                <template slot-scope="scope">
                                    <!-- 仅在销售信息行显示合同按钮 -->
                                    <div v-if="scope.row.saleOrderNo || scope.row.flowOrderInfoNo">
                                        <!-- 有合同数据且需要上传功能：显示"下载、上传合同"按钮 -->
                                        <div v-if="scope.row.contractUrl && scope.row.needUploadContract">
                                            <el-button size="mini" type="text" @click="openContract(scope.row.contractUrl)">下载</el-button>
                                            <el-button size="mini" type="text" @click="showUploadDialog(scope.row)">上传合同</el-button>
                                        </div>
                                        <!-- 有合同数据但不需要上传功能：仅显示"下载"按钮 -->
                                        <el-button size="mini" type="text" v-else-if="scope.row.contractUrl && !scope.row.needUploadContract" @click="openContract(scope.row.contractUrl)">下载</el-button>
                                        <!-- 无合同数据但需要上传功能：显示"上传合同"按钮 -->
                                        <el-button size="mini" type="text" v-else-if="!scope.row.contractUrl && scope.row.needUploadContract" @click="showUploadDialog(scope.row)">上传合同</el-button>
                                        <!-- 无合同数据且不需要上传功能：显示"暂无合同"文本 -->
                                        <span v-else>暂无合同</span>
                                    </div>
                                    <!-- 非销售信息行：不显示任何内容或显示"-" -->
                                    <span v-else>-</span>
                                </template>
                            </el-table-column>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <div class="content-block" v-if="!isAudit">
                <div class="block-title">
                    客户信息
                </div>
                <div class="block-table">
                    <el-table :data="supplierData" size="mini" border>
                        <el-table-column align="center" label="节点" width="45" type="index"></el-table-column>
                        <el-table-column align="center" label="客户" min-width="180" >
                            <template slot-scope="scope">
                                {{ scope.row.traderName || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="开票" width="45">
                            <template slot-scope="scope">
                                {{ scope.row.isInvoice ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="联系人" width="185" >
                            <template slot-scope="scope">
                                {{ scope.row.traderContactName ? scope.row.traderContactName + ' ' + scope.row.traderContactPhone : '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="联系地址" width="250" >
                            <template slot-scope="scope">
                                {{ scope.row.traderContactAddress || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收货人" width="185" >
                            <template slot-scope="scope">
                                {{ scope.row.receiverName ? scope.row.receiverName + ' ' + scope.row.receiverPhone : '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收货地址" width="250" >
                            <template slot-scope="scope">
                                {{ scope.row.receiverAddress || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收票人" width="185" >
                            <template slot-scope="scope">
                                {{ scope.row.invoiceReceiverName ? scope.row.invoiceReceiverName + ' ' + scope.row.invoiceReceiverPhone : '-' }}
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="content-block">
                <div class="block-title">
                    商品信息
                </div>
                <div class="block-table">
                    <el-table :data="goodsData" ref="goodsTable" :summary-method="getGoodsSummary" show-summary max-height="360" size="mini" border style="width: 100%">
                        <el-table-column align="center" label="订货号" width="80" prop="skuNo" fixed="left"></el-table-column>
                        <el-table-column align="center" label="产品名称" min-width="220">
                            <template slot-scope="scope">
                                <div class="text-line-1" :title="scope.row.productName">{{ scope.row.productName }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="品牌" width="100">
                            <template slot-scope="scope">
                                <div class="text-line-1" :title="scope.row.brand">{{ scope.row.brand }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="规格/型号" width="100">
                            <template slot-scope="scope">
                                <div class="text-line-1" :title="scope.row.model">{{ scope.row.model }}</div>
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="数量" width="70" prop="quantity"></el-table-column>
                        <el-table-column align="center" label="单位" width="90" prop="unit"></el-table-column>

                        <template v-if="supplierData.length">
                            <template v-for="(item, index) in supplierData">
                                <template v-if="index == 0">
                                    <el-table-column align="center" width="105" :label="index + '-' + (index + 1) + '销售价'" :prop="index + 1">
                                        <template slot-scope="scope">
                                            {{ scope.row.flowNodeOrderDetailPriceDtoList[0].price }}
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-else>
                                    <el-table-column align="center" width="105" :label="index + '-' + (index + 1) + '加价率'" :prop="index + 1">
                                        <template slot-scope="scope">
                                            <div>
                                                <div class="header-txt">
                                                {{ scope.row.flowNodeOrderDetailPriceDtoList[index].markupRate }}%
                                                </div>
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column align="center" width="120" :label="index + '-' + (index + 1) + '销售价'" :prop="index + 1">
                                        <template slot-scope="scope">
                                            <div>
                                                {{ scope.row.flowNodeOrderDetailPriceDtoList[index].price }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column align="center" width="90" :label="index + '级毛利率'" :prop="index + 1">
                                        <template slot-scope="scope">
                                            {{ scope.row.flowNodeOrderDetailPriceDtoList[index].grossProfitRate || scope.row.flowNodeOrderDetailPriceDtoList[index].grossProfitRate === 0 ? scope.row.flowNodeOrderDetailPriceDtoList[index].grossProfitRate + '%' : '-' }}
                                        </template>
                                    </el-table-column>
                                </template>
                            </template>
                            <template v-for="(item, index) in supplierShowList">
                                <el-table-column align="center" :prop="index + 1" width="105" :label="index + '-' + (index + 1) + '销售金额'">
                                    <template slot-scope="scope">
                                        {{ calcGoodsItemLevelTotal(index, scope.row) }}
                                    </template>
                                </el-table-column>
                            </template>
                        </template>

                        <el-table-column align="center" label="贝登发货数量" width="100" prop="deliveryQuantity"></el-table-column>
                    </el-table>
                </div>
            </div>

            <div class="content-block" v-if="isAudit">
                <div class="block-title">
                    客户信息
                </div>
                <div class="block-table">
                    <el-table :data="supplierData" size="mini" border>
                        <el-table-column align="center" label="节点" width="45" type="index">
                            <template slot-scope="scope">
                                {{ scope.row.nodeLevel}}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="客户" min-width="180" >
                            <template slot-scope="scope">
                                {{ scope.row.traderName || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="开票" width="45">
                            <template slot-scope="scope">
                                {{ scope.row.isInvoice ? '是' : '否' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="联系人" width="185" >
                            <template slot-scope="scope">
                                {{ scope.row.traderContactName ? scope.row.traderContactName + ' ' + scope.row.traderContactPhone : '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="联系地址" width="250" >
                            <template slot-scope="scope">
                                {{ scope.row.traderContactAddress || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收货人" width="185" >
                            <template slot-scope="scope">
                                {{ scope.row.receiverName ? scope.row.receiverName + ' ' + scope.row.receiverPhone : '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收货地址" width="250" >
                            <template slot-scope="scope">
                                {{ scope.row.receiverAddress || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收票人" width="185" >
                            <template slot-scope="scope">
                                {{ scope.row.invoiceReceiverName ? scope.row.invoiceReceiverName + ' ' + scope.row.invoiceReceiverPhone : '-' }}
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>

            <div class="content-block">
                <div class="block-title">
                    款票信息
                </div>
                <div class="block-table">
                    <el-table :data="supplierData" size="mini" border style="width: 100%">
                        <el-table-column align="center" label="节点" width="60">
                            <template slot-scope="scope">
                                {{ scope.$index }}-{{ scope.$index + 1 }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收款方式" min-width="185">
                            <template slot-scope="scope">
                                {{ getPayTypeLabel(scope.row.paymentMethod) }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="收款金额" width="110px">
                            <template slot-scope="scope">
                                {{ scope.row.amount || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="账期支付" width="110px">
                            <template slot-scope="scope">
                                {{ scope.row.creditPayment || '-' }}
                            </template>
                        </el-table-column>
                        <el-table-column align="center" label="发票类型" min-width="220">
                            <template slot-scope="scope">
                                {{ getInvoiceTypeLabel(scope.row.invoiceType) }}
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </div>
            <div class="container-r-footer" v-if="!isAudit">
                <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
                <el-button type="success" size="small" @click="handleAudit">审核</el-button>
                <el-button type="danger" size="small" @click="handleDelete">删除</el-button>
            </div>
        </div>
    </template>

    <!-- dialog 弹窗 -->
    <el-dialog
            title="发票"
            :visible.sync="dialogVisible"
            width="20%"
            :before-close="handleClose"
    >
        <div class="line"></div>
        <!-- 发票列表 -->
        <div v-if="currentInvoiceData && currentInvoiceData.length > 0">
            <div
                    v-for="(invoice, index) in currentInvoiceData"
                    :key="index"
                    class="invoice-item"
            >
                <span>{{ invoice.invoiceNo }}</span>
                <el-button v-if="invoice.invoiceNo" type="text" @click="openInvoice(invoice.invoiceUrl)">
                    查看
                </el-button>
            </div>
        </div>
        <!-- 无数据时的提示 -->
        <div v-else>
            <p>暂无发票文件</p>
        </div>
    </el-dialog>

    <!-- 合同上传对话框 -->
    <el-dialog
            title="上传合同"
            :visible.sync="uploadDialogVisible"
            width="40%"
            :before-close="handleUploadClose"
    >
        <div v-if="currentUploadRow && currentUploadRow.contractUrl" style="margin-bottom: 15px;">
            <el-alert
                    title="当前已有合同文件，上传新文件将替换现有合同"
                    type="warning"
                    :closable="false"
                    show-icon>
            </el-alert>
        </div>

        <el-upload
                ref="contractUpload"
                :action="uploadUrl"
                :data="uploadData"
                :before-upload="beforeUpload"
                :on-change="onFileChange"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :on-progress="onUploadProgress"
                :file-list="fileList"
                :auto-upload="false"
                accept=".pdf"
                drag
                :limit="1"
        >
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            <div class="el-upload__tip" slot="tip">
                <div>只能上传PDF文件，且不超过10MB</div>
            </div>
        </el-upload>

        <div v-if="uploadProgress > 0 && uploadProgress < 100" style="margin-top: 15px;">
            <el-progress :percentage="uploadProgress" :status="uploadStatus"></el-progress>
        </div>

        <span slot="footer" class="dialog-footer">
            <el-button @click="handleUploadClose">取 消</el-button>
            <el-button type="primary" @click="confirmUpload" :loading="uploading" :disabled="fileList.length === 0">
                {{ uploading ? '上传中...' : '确定上传' }}
            </el-button>
        </span>
    </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/flowOrder/sale_detail.js?rnd=${resourceVersionKey}"></script>
<style>
    .invoice-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 3px 0;
    }
    .line{
        border-bottom: 1px solid #eee;
        width: 100%;
    }
    .el-dialog__body{
        padding: 5px 30px;
    }
</style>
