package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.system.SystemUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRInvoiceDetailJOperateLogDto;
import com.vedeng.erp.kingdee.batch.dto.BuyOrderExpenseExcelDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 采购票的应付单兼容21-22 费用 解析excel
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseInvoiceCompatibleExpenseProcessor implements ItemProcessor<BatchInvoiceDto, BatchInvoiceDto> {

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;

    @Autowired
    private BatchRInvoiceDetailJOperateLogDtoMapper batchRInvoiceDetailJOperateLogDtoMapper;

    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;

    private ConcurrentMap<String, List<BuyOrderExpenseExcelDto>> excelDtoList = new ConcurrentHashMap<>();

    private final static String FILE_PATH = SystemUtil.getOsInfo().isLinux()?"/tmp/21-22采购费用.xlsx":"C:\\Users\\<USER>\\Desktop\\21-22采购费用.xlsx";


    @Override
    public BatchInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("处理采购单蓝字有效票{}", JSON.toJSONString(batchInvoiceDto));

        // 排除采购蓝字作废
        List<BatchInvoiceDto> blueDisEnableByInvoiceCodeAndInvoiceNo = batchInvoiceDtoMapper.findBlueDisEnableByInvoiceCodeAndInvoiceNo(batchInvoiceDto.getInvoiceCode(), batchInvoiceDto.getInvoiceNo(), 503);
        if (CollUtil.isNotEmpty(blueDisEnableByInvoiceCodeAndInvoiceNo)) {
            log.info("当前采购蓝票存在蓝字作废{}", JSON.toJSONString(blueDisEnableByInvoiceCodeAndInvoiceNo));
            return null;
        }

        List<BatchRInvoiceDetailJOperateLogDto> jOperateLogDtos = batchRInvoiceDetailJOperateLogDtoMapper.findByInvoiceId(batchInvoiceDto.getInvoiceId());
        batchInvoiceDto.setBatchRInvoiceDetailJOperateLogDtos(jOperateLogDtos);
        // 发票类型 1电票 2 纸票
        if (StrUtil.isNotEmpty(batchInvoiceDto.getInvoiceTypeName()) && batchInvoiceDto.getInvoiceTypeName().contains("专用发票")) {
            // 过滤未认证的专票
            if (!batchInvoiceDto.getIsAuth().equals(1)) {
                log.info("蓝票专票id:{}需要认证", JSON.toJSONString(batchInvoiceDto.getInvoiceId()));
                return null;
            }
        }
        List<BatchInvoiceDetailDto> byInvoiceIdAndBuyorderGoodsId = batchInvoiceDetailDtoMapper.findByInvoiceIdAndBuyorderGoodsId(batchInvoiceDto.getInvoiceId());
        batchInvoiceDto.setBatchInvoiceDetailDtoList(byInvoiceIdAndBuyorderGoodsId);


        // 处理 解析 excel
        if (CollUtil.isEmpty(excelDtoList)) {
            boolean file = FileUtil.isFile(FILE_PATH);
            if (file) {
                try {
                    ExcelReader reader = ExcelUtil.getReader(FILE_PATH, 0);
                    List<BuyOrderExpenseExcelDto> buyOrderExpenseExcelDtos = reader.readAll(BuyOrderExpenseExcelDto.class);
                    if (CollUtil.isNotEmpty(buyOrderExpenseExcelDtos)) {
                        excelDtoList = buyOrderExpenseExcelDtos.stream().collect(Collectors.groupingByConcurrent(BuyOrderExpenseExcelDto::getBuyOrderNo));
                    }
                } catch (Exception e) {
                    log.error("解析21-22采购费用异常:", e);
                    excelDtoList = new ConcurrentHashMap<>();
                }
            } else {
                return batchInvoiceDto;
            }

        }

        List<BuyOrderExpenseExcelDto> buyOrderExpenseExcelDtos = excelDtoList.get(batchInvoiceDto.getOrderNo());
        if (CollUtil.isEmpty(buyOrderExpenseExcelDtos)) {
            // excel为空 跳过
            return batchInvoiceDto;
        }
        // 打标赋值
        batchInvoiceDto.setHasExpense(Boolean.TRUE);
        batchInvoiceDto.setExcelDtoList(buyOrderExpenseExcelDtos);
        for (BuyOrderExpenseExcelDto item : buyOrderExpenseExcelDtos) {
            String sku = item.getSku();
            if (CollUtil.isNotEmpty(jOperateLogDtos)) {
                List<BatchRInvoiceDetailJOperateLogDto> collect = jOperateLogDtos.stream().filter(x -> sku.equals(x.getSku())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect)) {
                    collect.forEach(x -> x.setHasExpense(Boolean.TRUE));
                }
            }
        }

        return batchInvoiceDto;
    }


}
