<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchWmsInOutPersonMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOutPersonDto">
    <!--@mbg.generated-->
    <!--@Table T_WMS_IN_OUT_PERSON-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="DATE" jdbcType="DATE" property="date" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="TYPE" jdbcType="INTEGER" property="type" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, `DATE`, `NAME`, `TYPE`, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_IN_OUT_PERSON
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_WMS_IN_OUT_PERSON
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOutPersonDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_IN_OUT_PERSON (`DATE`, `NAME`, `TYPE`, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME
      )
    values (#{date,jdbcType=DATE}, #{name,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOutPersonDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_IN_OUT_PERSON
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        `DATE`,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="type != null">
        `TYPE`,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOutPersonDto">
    <!--@mbg.generated-->
    update T_WMS_IN_OUT_PERSON
    <set>
      <if test="date != null">
        `DATE` = #{date,jdbcType=DATE},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchWmsInOutPersonDto">
    <!--@mbg.generated-->
    update T_WMS_IN_OUT_PERSON
    set `DATE` = #{date,jdbcType=DATE},
      `NAME` = #{name,jdbcType=VARCHAR},
      `TYPE` = #{type,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-12-11-->
  <select id="selectByTypeAndDate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WMS_IN_OUT_PERSON
    where `TYPE`=#{type,jdbcType=INTEGER} and `DATE`=#{date,jdbcType=DATE}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-11-->
  <select id="selectByType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_WMS_IN_OUT_PERSON
    where `TYPE`=#{type,jdbcType=INTEGER}
  </select>
</mapper>