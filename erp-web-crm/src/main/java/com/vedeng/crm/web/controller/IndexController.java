package com.vedeng.crm.web.controller;

import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.base.R;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 首页
 * @date 2024/7/17 16:35
 */
@Controller
@Slf4j
public class IndexController extends BaseController {

    @RequestMapping("/")
    public String index() {
        return "/vue/view/crm/index";
    }

    /**
     * <b>Description:</b><br>
     * 登出
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年6月28日 下午1:58:31
     */
    @ResponseBody
    @RequestMapping(value = "/logout")
    public R<?> logout(HttpServletRequest request, HttpServletResponse response) {
        //允许跨域请求
//        response.setHeader("Access-Control-Allow-Origin", "*");
//        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
//        response.setHeader("Access-Control-Allow-Methods", "GET, PUT, OPTIONS, POST");
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");

        response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS, PATCH");
        response.setHeader("Access-Control-Allow-Headers", "Authorization,Content-Type,X-Requested-With,version,gomanager,token,formtoken,source");
        response.setHeader("Access-Control-Max-Age", "3600");

        Subject currentUser = SecurityUtils.getSubject();
        currentUser.logout();
        R resultInfo = new R<>();
        resultInfo.setCode(0);
        resultInfo.setMessage("操作成功");
        return resultInfo;
    }


    @RequestMapping(value = "/logoutForJsonp")
    public void logoutForJsonp(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String callback = request.getParameter("callback");
        String result = "{\"status\": \"success\"}";

        if (callback != null) {
            response.setContentType("application/javascript");
            PrintWriter out = response.getWriter();
            out.println(callback + "(" + result + ");");
        } else {
            response.setContentType("application/json");
            PrintWriter out = response.getWriter();
            out.println(result);
        }
    }


}
