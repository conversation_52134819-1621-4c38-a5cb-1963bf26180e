package com.vedeng.trader.service;

import com.vedeng.common.page.Page;
import com.vedeng.trader.model.dto.CustomerBillPeriodApplyListDto;
import com.vedeng.trader.model.dto.CustomerBillPeriodApplyListQueryDto;
import com.vedeng.trader.model.dto.CustomerBillPeriodListQueryDto;
import com.vedeng.trader.model.dto.CustomerBillPeriodListViewDto;

import java.util.List;

/**
 * <AUTHOR>
 * 客户信用账期相关服务
 */
public interface TraderCreditService {

    /**
     * 检索账期列表
     *
     * @param queryDto 检索条件
     * @param page    分页信息
     * @return
     */
    CustomerBillPeriodListViewDto getCustomerBillPeriodListView(CustomerBillPeriodListQueryDto queryDto, Page page);

    /**
     * 检索账期申请列表
     *
     * @param queryDto
     * @param page
     * @return
     */
    CustomerBillPeriodApplyListDto getCustomerBillPeriodApplyList(CustomerBillPeriodApplyListQueryDto queryDto, Page page);

    /**
     * @description: 获取该创建人的账期申请
     * @return:
     * @author: Strange
     * @date: 2021/9/8
     **/
    List<Long> getCustomerBIllPeriodApplyByCreator(Integer status, Integer userId);
}
