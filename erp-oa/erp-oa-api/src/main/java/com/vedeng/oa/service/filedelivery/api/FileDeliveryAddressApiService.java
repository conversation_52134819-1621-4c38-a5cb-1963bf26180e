package com.vedeng.oa.service.filedelivery.api;

import java.util.List;

import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;

/**
 * 文件寄送地址管理服务
 * <AUTHOR>
 *
 */
public interface FileDeliveryAddressApiService {

	/**
	 * 批量新增文件地址信息
	 * @param fileDeliveryAddressVO
	 * @param userId 
	 * @return
	 */
	public  FileDeliveryAddressVO  saveFileDeliveryAddress(FileDeliveryAddressVO fileDeliveryAddressVO,Integer userId);
	/**
	 * 根据主键获取文件地址列表
	 * @param fileDeliveryId
	 * @return
	 */
	public List<FileDeliveryAddressVO> getFileDeliveryAddress(Integer fileDeliveryId);

	/**
	 * 根据id返回地址列表（按ID升序）
	 * @param ids
	 * @return
	 */
	public List<FileDeliveryAddressVO> getFileDeliveryAddressByIds(List<Integer> ids);
	
	/**
	 * 根据主键删除收件单位
	 * @param id
	 * @return
	 */
	public int deleteFileDeliveryAddressById(Integer id,Integer userId);

}
