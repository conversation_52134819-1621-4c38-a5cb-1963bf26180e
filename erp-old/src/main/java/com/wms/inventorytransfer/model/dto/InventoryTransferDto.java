package com.wms.inventorytransfer.model.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 库存转移单数据传输对象
 *
 * <AUTHOR>
 * @date 2020/7/20 9:05:06
 */
public class InventoryTransferDto implements Serializable {
    /**
     * 库存转移单ID
     */
    private Integer inventoryTransferId;

    /**
     * 库存转移单类型
     */
    private Integer type;

    /**
     * 活动ID
     */
    private Integer actionId;

    /**
     * '转移单状态  （1进行中  2已完结）',
     */
    private Integer status;

    /**
     * 库存转移单详情
     */
    private List<InventoryTransferDetailDto> inventoryTransferDetailDtos;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public List<InventoryTransferDetailDto> getInventoryTransferDetailDtos() {
        return inventoryTransferDetailDtos;
    }

    public void setInventoryTransferDetailDtos(List<InventoryTransferDetailDto> inventoryTransferDetailDtos) {
        this.inventoryTransferDetailDtos = inventoryTransferDetailDtos;
    }

    public Integer getInventoryTransferId() {
        return inventoryTransferId;
    }

    public void setInventoryTransferId(Integer inventoryTransferId) {
        this.inventoryTransferId = inventoryTransferId;
    }

    public Integer getActionId() {
        return actionId;
    }

    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }
}
