package com.vedeng.erp.settlement.service;

import com.vedeng.erp.finance.domain.dto.BankBillCustomerAccountDto;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;

import java.util.List;

public interface BankBillService {

    List<BankBillCustomerAccountDto> queryBankBillBySaleOrderId(Integer saleOrderId);

    int deleteByPrimaryKey(Integer bankBillId);

    int insert(BankBillEntity record);

    int insertSelective(BankBillEntity record);

    BankBillEntity selectByPrimaryKey(Integer bankBillId);

    int updateByPrimaryKeySelective(BankBillEntity record);

    int updateByPrimaryKey(BankBillEntity record);

    void create(BankBillDto bankBillDto);

    List<BankBillDto> findAcceptanceBill();
}
