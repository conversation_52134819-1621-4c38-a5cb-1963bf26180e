package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.ezadmin.common.utils.Utils;
import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.Region;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.quote.domain.UpdateTerminalInfoDto;
import com.vedeng.erp.quote.mapper.NewQuoteMapper;
import com.vedeng.erp.saleorder.constant.TerminalCustomerEnum;
import com.vedeng.erp.saleorder.dao.*;
import com.vedeng.erp.saleorder.domain.SaleorderAdditionalClause;
import com.vedeng.erp.saleorder.domain.SaleorderAdditionalClauseExample;
import com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity;
import com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalCustomerEntity;
import com.vedeng.erp.saleorder.domain.entity.SaleorderEntity;
import com.vedeng.erp.saleorder.dto.*;
import com.vedeng.erp.saleorder.enums.OrderTerminalConstant;
import com.vedeng.erp.saleorder.feign.OneDataTerminalFeignApi;
import com.vedeng.erp.saleorder.mapstruct.OrderTerminalConvertor;
import com.vedeng.erp.saleorder.mapstruct.SaleOrderTerminalConvertor;
import com.vedeng.erp.saleorder.mapstruct.SaleOrderTerminalCustomerConvertor;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseApiService;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseService;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import com.vedeng.infrastructure.tyc.service.TycSearchService;
import com.vedeng.onedataapi.api.terminal.req.TerminalReqDto;
import com.vedeng.onedataapi.api.terminal.res.TerminalDataRes;
import com.vedeng.system.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.ezadmin.plugins.parser.MapParser;
import top.ezadmin.plugins.parser.parse.ResultModel;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class OrderAdditionalClauseServiceImpl implements OrderAdditionalClauseApiService, OrderAdditionalClauseService {
    @Autowired
    private SaleorderAdditionalClauseMapper saleorderAdditionalClauseMapper;
    @Autowired
    private SaleOrderMapper saleOrderMapper;
    @Autowired
    private RegionService regionService;
    @Override
    public  SaleorderAdditionalClauseDto  getSaleorderAdditionalClauseByOrderId(SaleorderAdditionalClauseDto additionalClauseDto) {
        log.info("查询附加条款{}", JSONObject.toJSONString(additionalClauseDto));
        if(Objects.isNull(additionalClauseDto)||Objects.isNull(additionalClauseDto.getSaleorderId())){
            return null;
        }
        SaleorderEntity saleOrder = saleOrderMapper.findBySaleorderId(additionalClauseDto.getSaleorderId());
        if(Objects.isNull(saleOrder)){
            return null;
        }
        SaleorderAdditionalClauseExample example=new SaleorderAdditionalClauseExample();
        example.createCriteria().andIsDeleteEqualTo(0).andSaleorderIdEqualTo(additionalClauseDto.getSaleorderId());
        List<SaleorderAdditionalClause> saleorderAdditionalClauses = saleorderAdditionalClauseMapper.selectByExample(example);
        //没有数据或者没有选中时，封装一个未选中的 自定义条款
        if(CollectionUtils.isEmpty(saleorderAdditionalClauses)||
                StringUtils.isBlank(StringUtils.trim(saleorderAdditionalClauses.get(0).getAdditionalClauseNos())) ){
            //合同展示
            SaleorderAdditionalClauseDto saleorderAdditionalClauseDto=new SaleorderAdditionalClauseDto();
            saleorderAdditionalClauseDto.setSaleorderId(additionalClauseDto.getSaleorderId());
            saleorderAdditionalClauseDto.setAdditionalClause(saleOrder.getAdditionalClause()  );
            saleorderAdditionalClauseDto.setAdditionalClauseFinalShow(saleorderAdditionalClauseDto.getAdditionalClause());
            return saleorderAdditionalClauseDto;
        }
        SaleorderAdditionalClause saleorderAdditionalClause = saleorderAdditionalClauses.get(0);
        SaleorderAdditionalClauseDto saleorderAdditionalClauseDto = new SaleorderAdditionalClauseDto();
        BeanUtils.copyProperties(saleorderAdditionalClause,saleorderAdditionalClauseDto);
        //计算show
        log.info("all nos:{}",saleorderAdditionalClause.getAdditionalClauseNos());
        String[] ids=StringUtils.split(saleorderAdditionalClause.getAdditionalClauseNos(),",");
        StringBuilder show=new StringBuilder();
        for (int i = 0; i < ids.length; i++) {
            String value=OrderConstant.ORDER_ADDITIONAL_CLAUSES_MAP.get(ids[i].trim());
            if(StringUtils.equals("401",ids[i].trim())){
                //只有自定义条款不为空的时候才加上
                if(StringUtils.isNotBlank(saleorderAdditionalClauseDto.getAdditionalClause())){
                    show.append((i+1)+"、").append(value);
                    SaleorderAdditionalClauseItemDto dto=new SaleorderAdditionalClauseItemDto();
                    dto.setAdditionalClauseNo("401");
                    dto.setAdditionalClauseItem(saleorderAdditionalClauseDto.getAdditionalClause());
                    saleorderAdditionalClauseDto.getAdditionalClauseItemShowList().add(dto);
                }
            }else{

                //自定义条款单独处理
                if(StringUtils.isNotBlank(value)){
                    show.append((i+1)+"、").append(value).append("<br>");

                    SaleorderAdditionalClauseItemDto dto=new SaleorderAdditionalClauseItemDto();
                    dto.setAdditionalClauseNo(ids[i].trim());
                    dto.setAdditionalClauseItem(fillParam(saleorderAdditionalClauseDto,value.toString()));
                    saleorderAdditionalClauseDto.getAdditionalClauseItemShowList().add(dto);
                }
            }
        }
        //给合同用的
        saleorderAdditionalClauseDto.setAdditionalClauseFinalShow(fillParam(saleorderAdditionalClauseDto,show.toString()));
        return saleorderAdditionalClauseDto;
    }

    private String fillParam(SaleorderAdditionalClauseDto saleorderAdditionalClauseDto,String item){
        Map<String,Object> param=new HashMap<>();
        //安装地址
        param.put("installationAddress", Utils.trimNull(saleorderAdditionalClauseDto.getInstallationAddress()));
        //销往客户
        param.put("traderName", Utils.trimNull(saleorderAdditionalClauseDto.getTraderName()));
        //产品名称103
        param.put("prodName103", Utils.trimNull(saleorderAdditionalClauseDto.getProdName103()));
        //产品名称105
        param.put("prodName105", Utils.trimNull(saleorderAdditionalClauseDto.getProdName105()));
        //地区 省市
        param.put("saleProvinceName", Utils.trimNull(saleorderAdditionalClauseDto.getSaleProvinceName()));
        param.put("saleCityName", Utils.trimNull(saleorderAdditionalClauseDto.getSaleCityName()));
        //医疗机构名称
        param.put("terminalTraderName", Utils.trimNull(saleorderAdditionalClauseDto.getTerminalTraderName()));
        //序列号
        param.put("snCode", Utils.trimNull(saleorderAdditionalClauseDto.getSnCode()));
        //预付试剂款
        param.put("prepaidReagentAmount", Utils.trimNull(saleorderAdditionalClauseDto.getPrepaidReagentAmount()));
        //自定义条款
        param.put("selfAdditionalClause", Utils.trimEmptyDefault(saleorderAdditionalClauseDto.getAdditionalClause(),"无"));
        ResultModel resultModel=MapParser.parseDefaultEmpty(item,param);
        return resultModel.getResult();
    }

    @Transactional
    @Override
    public void saveSaleorderAdditionalClause(SaleorderAdditionalClauseDto additionalClauseDto) {
        log.info("保存附加条款{}", JSONObject.toJSONString(additionalClauseDto));

        SaleorderAdditionalClause additionalClause=new SaleorderAdditionalClause();
        BeanUtils.copyProperties(additionalClauseDto,additionalClause);
        //省市快照,减少查询
        if(additionalClauseDto.getSaleCityId()!=null){
            Region city=regionService.getRegionByRegionId(additionalClauseDto.getSaleCityId());
            if(city!=null){
                Region province=regionService.getRegionByRegionId(city.getParentId());
                if(province!=null){
                    additionalClause.setSaleProvinceId(province.getRegionId());
                    additionalClause.setSaleProvinceName(province.getRegionName());
                    additionalClause.setSaleCityName(city.getRegionName());
                }
            }
        }
        if(StringUtils.isBlank(additionalClauseDto.getAdditionalClauseNos())){
            additionalClause.setIsDelete(1);
        }else{
            additionalClause.setIsDelete(0);
        }
        if(additionalClauseDto.getId()!=null){
            SaleorderAdditionalClause saleorderAdditionalClauseDb=   saleorderAdditionalClauseMapper.selectByPrimaryKey(additionalClauseDto.getId());
            additionalClause.setCreator(saleorderAdditionalClauseDb.getCreator());
            additionalClause.setAddTime(saleorderAdditionalClauseDb.getAddTime());
            saleorderAdditionalClauseMapper.updateByPrimaryKey(additionalClause);
            log.info("修改附加条款成功 {}", JSONObject.toJSONString(additionalClauseDto));
        }else{

            saleorderAdditionalClauseMapper.insert(additionalClause);
            log.info("添加附加条款成功 {}", JSONObject.toJSONString(additionalClauseDto));
        }
    }


}
