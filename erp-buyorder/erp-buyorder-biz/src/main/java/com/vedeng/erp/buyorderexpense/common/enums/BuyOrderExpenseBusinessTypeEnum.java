package com.vedeng.erp.buyorderexpense.common.enums;

/**
 * <AUTHOR>
 * @description 采购费用单业务类型枚举类
 * @date 2022/11/18 12:57
 **/
public enum BuyOrderExpenseBusinessTypeEnum {



    BUYORDER(1,"采购"),
    AFTERSALE(2,"采购售后"),
    ;

    private Integer code;

    private String desc;


    BuyOrderExpenseBusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
