<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchSettlementBillItemDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto">
    <!--@mbg.generated-->
    <!--@Table T_SETTLEMENT_BILL_ITEM-->
    <id column="SETTLE_ITEM_BILL_ID" jdbcType="INTEGER" property="settleItemBillId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SETTLE_BILL_ID" jdbcType="INTEGER" property="settleBillId" />
    <result column="SETTLE_BILL_NO" jdbcType="VARCHAR" property="settleBillNo" />
    <result column="TRADER_DIRECTION" jdbcType="INTEGER" property="traderDirection" />
    <result column="SETTLEMENT_TYPE" jdbcType="INTEGER" property="settlementType" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="BUSINESS_NO" jdbcType="VARCHAR" property="businessNo" />
    <result column="BUSINESS_ID" jdbcType="INTEGER" property="businessId" />
    <result column="BUSINESS_ITEM_ID" jdbcType="INTEGER" property="businessItemId" />
    <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="NUMBER" jdbcType="DECIMAL" property="number" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="INVOICE_STATUS" jdbcType="INTEGER" property="invoiceStatus" />
    <result column="SETTLEMENT_STATUS" jdbcType="INTEGER" property="settlementStatus" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SETTLE_ITEM_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    SETTLE_BILL_ID, SETTLE_BILL_NO, TRADER_DIRECTION, SETTLEMENT_TYPE, BUSINESS_TYPE, 
    BUSINESS_NO, BUSINESS_ID, BUSINESS_ITEM_ID, PRODUCT_NAME, PRICE, `NUMBER`, AMOUNT, 
    INVOICE_STATUS, SETTLEMENT_STATUS, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL_ITEM
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SETTLEMENT_BILL_ITEM
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SETTLE_ITEM_BILL_ID" keyProperty="settleItemBillId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL_ITEM (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      SETTLE_BILL_ID, SETTLE_BILL_NO, TRADER_DIRECTION, 
      SETTLEMENT_TYPE, BUSINESS_TYPE, BUSINESS_NO, 
      BUSINESS_ID, BUSINESS_ITEM_ID, PRODUCT_NAME, 
      PRICE, `NUMBER`, AMOUNT, 
      INVOICE_STATUS, SETTLEMENT_STATUS, IS_DELETE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{settleBillId,jdbcType=INTEGER}, #{settleBillNo,jdbcType=VARCHAR}, #{traderDirection,jdbcType=INTEGER}, 
      #{settlementType,jdbcType=INTEGER}, #{businessType,jdbcType=VARCHAR}, #{businessNo,jdbcType=VARCHAR}, 
      #{businessId,jdbcType=INTEGER}, #{businessItemId,jdbcType=INTEGER}, #{productName,jdbcType=VARCHAR}, 
      #{price,jdbcType=DECIMAL}, #{number,jdbcType=DECIMAL}, #{amount,jdbcType=DECIMAL}, 
      #{invoiceStatus,jdbcType=INTEGER}, #{settlementStatus,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SETTLE_ITEM_BILL_ID" keyProperty="settleItemBillId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL_ITEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="settleBillId != null">
        SETTLE_BILL_ID,
      </if>
      <if test="settleBillNo != null">
        SETTLE_BILL_NO,
      </if>
      <if test="traderDirection != null">
        TRADER_DIRECTION,
      </if>
      <if test="settlementType != null">
        SETTLEMENT_TYPE,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="businessNo != null">
        BUSINESS_NO,
      </if>
      <if test="businessId != null">
        BUSINESS_ID,
      </if>
      <if test="businessItemId != null">
        BUSINESS_ITEM_ID,
      </if>
      <if test="productName != null">
        PRODUCT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="number != null">
        `NUMBER`,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillId != null">
        #{settleBillId,jdbcType=INTEGER},
      </if>
      <if test="settleBillNo != null">
        #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderDirection != null">
        #{traderDirection,jdbcType=INTEGER},
      </if>
      <if test="settlementType != null">
        #{settlementType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null">
        #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessItemId != null">
        #{businessItemId,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        #{number,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL_ITEM
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillId != null">
        SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER},
      </if>
      <if test="settleBillNo != null">
        SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderDirection != null">
        TRADER_DIRECTION = #{traderDirection,jdbcType=INTEGER},
      </if>
      <if test="settlementType != null">
        SETTLEMENT_TYPE = #{settlementType,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      </if>
      <if test="businessNo != null">
        BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      </if>
      <if test="businessId != null">
        BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      </if>
      <if test="businessItemId != null">
        BUSINESS_ITEM_ID = #{businessItemId,jdbcType=INTEGER},
      </if>
      <if test="productName != null">
        PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="number != null">
        `NUMBER` = #{number,jdbcType=DECIMAL},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL_ITEM
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER},
      SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      TRADER_DIRECTION = #{traderDirection,jdbcType=INTEGER},
      SETTLEMENT_TYPE = #{settlementType,jdbcType=INTEGER},
      BUSINESS_TYPE = #{businessType,jdbcType=VARCHAR},
      BUSINESS_NO = #{businessNo,jdbcType=VARCHAR},
      BUSINESS_ID = #{businessId,jdbcType=INTEGER},
      BUSINESS_ITEM_ID = #{businessItemId,jdbcType=INTEGER},
      PRODUCT_NAME = #{productName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      `NUMBER` = #{number,jdbcType=DECIMAL},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=INTEGER},
      SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where SETTLE_ITEM_BILL_ID = #{settleItemBillId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-11-28-->
  <select id="selectBySettleBillIdAndInvoiceStatusBlue" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL_ITEM
    where SETTLE_BILL_ID=#{settleBillId,jdbcType=INTEGER}
    and INVOICE_STATUS in (0,1)
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-30-->
  <select id="selectByBusinessItemIdAndBuyOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL_ITEM
    where BUSINESS_ITEM_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and SETTLEMENT_TYPE = 1 and BUSINESS_TYPE = 'buyOrder'
  </select>

  <select id="selectBySettleBillIdAndInvoiceStatusRed" resultType="com.vedeng.erp.kingdee.batch.dto.BatchSettlementBillItemDto">
    select
    SETTLE_ITEM_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
    SETTLE_BILL_ID, SETTLE_BILL_NO, SETTLEMENT_TYPE, BUSINESS_TYPE, BUSINESS_NO, BUSINESS_ID,
    BUSINESS_ITEM_ID, PRODUCT_NAME, TSBI.PRICE, `NUMBER`, AMOUNT, INVOICE_STATUS,TASG.ORDER_DETAIL_ID buyOrderGoodsId
    from T_SETTLEMENT_BILL_ITEM TSBI left join T_AFTER_SALES_GOODS TASG on TASG.AFTER_SALES_GOODS_ID = TSBI.BUSINESS_ITEM_ID
    where SETTLE_BILL_ID=#{settleBillId,jdbcType=INTEGER}
    and INVOICE_STATUS in (0,1)
  </select>

<!--auto generated by MybatisCodeHelper on 2023-11-30-->
  <select id="selectBySettleBillId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL_ITEM
    where SETTLE_BILL_ID=#{settleBillId,jdbcType=INTEGER}
  </select>

  <select id="selectByBusinessItemIdAndAfterSale" resultMap="BaseResultMap">
    select
    SETTLE_ITEM_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
    SETTLE_BILL_ID, SETTLE_BILL_NO, SETTLEMENT_TYPE, BUSINESS_TYPE, BUSINESS_NO, BUSINESS_ID,
    BUSINESS_ITEM_ID, PRODUCT_NAME, PRICE, `NUMBER`, AMOUNT, INVOICE_STATUS
    from T_SETTLEMENT_BILL_ITEM
    where BUSINESS_ITEM_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item,jdbcType=INTEGER}
    </foreach>
    and SETTLEMENT_TYPE = 1 and BUSINESS_TYPE = 'buyOrderAfterSale'
  </select>
</mapper>