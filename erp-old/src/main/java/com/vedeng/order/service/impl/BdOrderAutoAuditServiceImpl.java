package com.vedeng.order.service.impl;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderGoodsAptitudeConstants;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.service.CategoryService;
import com.vedeng.goods.service.GoodsChannelPriceService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.service.BdOrderAutoAuditService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoPurchaseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
public class BdOrderAutoAuditServiceImpl implements BdOrderAutoAuditService {
    public static final Logger logger = LoggerFactory.getLogger(BdOrderAutoAuditServiceImpl.class);

    @Autowired
    private SaleorderService saleorderService;

    @Resource
    private UserMapper userMapper;

    @Autowired
    @Qualifier("userService")
    protected UserService userService;

    @Autowired
    @Qualifier("traderCustomerService")
    protected TraderCustomerService traderCustomerService;

    @Autowired
    @Qualifier("goodsChannelPriceService")
    protected GoodsChannelPriceService goodsChannelPriceService;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private BasePriceService basePriceService;

    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    @Qualifier("verifiesRecordService")
    protected VerifiesRecordService verifiesRecordService;

    @Autowired
    @Qualifier("logicalSaleorderChooseService")
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    @Qualifier("categoryService")
    protected CategoryService categoryService;

    @Override
    public boolean bdOrderAutoAudit(Saleorder saleorder) {
        try{
            ResultInfo res = saleorderService.isValidSaleOrder(saleorder);
            if (res != null && res.getCode() == -1) {
                logger.info("BD订单自动审核时校验订单生效状态发生错误 - saleorderOrderId: {}, message:{}",
                        saleorder.getSaleorderId(), res.getMessage());
                // 取消自动审核
                cancelAutoAudit(saleorder);
                return false;
            }

            User user = userMapper.getUserByName("njadmin");

            //查询订单
            Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);

            //查询订单商品
            List<SaleorderGoods> saleOrderGoodList = saleorderService.getSaleorderGoodsById(saleorder);

            //设置归属销售人员名称
            User userInfo = userService.getUserByTraderId(saleorderInfo.getTraderId(), 1);
            saleorderInfo.setOptUserName(userInfo == null ? "" : userInfo.getUsername());
            //设置自动审核发起人
            saleorderInfo.setCurrentOperator(userInfo == null ? "njadmin" : userInfo.getUsername());

            // 设置是否绑定微信
            String weixin = Optional.ofNullable(saleorderInfo.getTraderContactId())
                    .map(traderContactId -> traderCustomerService.getTraderContactById(traderContactId))
                    .map(tcInfo -> tcInfo.getWeixin())
                    .orElse(null);

            saleorderInfo.setIsWeiXin(StringUtils.isNotBlank(weixin) ? 1 : 0);

            //设置产品核价信息
            TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(saleorderInfo.getTraderId());
            goodsChannelPriceService.getSaleChannelPriceList(
                    saleorderInfo.getSalesAreaId(), saleorderInfo.getCustomerNature(), customer.getOwnership(),
                    saleOrderGoodList);

            //设置订单的商品类型
            setSaleOrderGoodType(saleorderInfo,saleOrderGoodList,user);

            //设置订单的coseUserIds
            setSaleOrderCostUserIds(saleorder.getSaleorderId(),saleOrderGoodList,user);

            //设置商品的用户列表
            setSaleOrderGoodUserIdStr(saleOrderGoodList,user);

            //处理商品的价格
            dealWithSaleOrderGoodsPrice(saleorder.getSaleorderId());

            //开启流程实例
            startProcessInstance(saleorderInfo,user);
        }catch (Exception e){
            logger.error("BD订单自动审核-----bdOrderAutoAudit error-----:", e);
        }
        return true;
    }

    /**
     * 设置销售单的商品类型
     * @param saleorderInfo
     */
    private void setSaleOrderGoodType(Saleorder saleorderInfo,List<SaleorderGoods> saleOrderGoodList,User user) {

        // 订单中产品类型（0未维护,1 只有设备,2 只有试剂,3 又有试剂又有设备）
        saleorderInfo.setGoodsType(0);

        if(CollectionUtils.isEmpty(saleOrderGoodList)){
            return;
        }

        List<Integer> categoryList = new ArrayList<>();

        List<Integer> goodsTypeList = new ArrayList<>();

        for (SaleorderGoods saleorderGood : saleOrderGoodList) {

            BigDecimal settlementPrice = saleorderService.getSaleorderGoodsSettlementPrice(saleorderGood.getGoodsId(),
                    user.getCompanyId());

            // 区域管制，货期大于核价货期，直发，报价小于结算价(货期暂时取消),参考成本为0
            if (saleorderGood.getAreaControl().equals(1) || saleorderGood.getDeliveryDirect().equals(1)
                    || (null != settlementPrice && settlementPrice.compareTo(saleorderGood.getPrice()) == 1)
                    || saleorderGood.getReferenceCostPrice().compareTo(BigDecimal.ZERO) == 0) {
                categoryList.add(saleorderGood.getCategoryId());
            }
            if (saleorderGood.getGoods() != null) {
                if (saleorderGood.getIsDelete() != null && saleorderGood.getIsDelete() == 0 && saleorderGood.getGoods().getGoodsType() != null
                        && (saleorderGood.getGoods().getGoodsType() == 316 || saleorderGood.getGoods().getGoodsType() == 319)) {
                    goodsTypeList.add(1);
                } else if (saleorderGood.getIsDelete() != null && saleorderGood.getIsDelete() == 0
                        && saleorderGood.getGoods().getGoodsType() != null
                        && (saleorderGood.getGoods().getGoodsType() == 317 || saleorderGood.getGoods().getGoodsType() == 318)) {
                    goodsTypeList.add(2);
                }
            }
        }

        if (CollectionUtils.isEmpty(goodsTypeList)) {
            return;
        }

        List<Integer> newList = new ArrayList(new HashSet(goodsTypeList));

        if (newList.size() == 2) {
            saleorderInfo.setGoodsType(3);
        }

        if (newList.size() == 1) {
            saleorderInfo.setGoodsType(newList.get(0));
        }
    }

    private void setSaleOrderCostUserIds(Integer saleorderId,List<SaleorderGoods> saleOrderGoodList, User user) {

        if (CollectionUtils.isEmpty(saleOrderGoodList)) {
            return;
        }

        // 未填写成本价的采购人员集合
        List<String> noPriceBuyorderUser = new ArrayList<>();
        for (int i = 0; i < saleOrderGoodList.size(); i++) {
            // 参考成本为0
            if (saleOrderGoodList.get(i).getReferenceCostPrice().compareTo(BigDecimal.ZERO) == 0) {
                if (saleOrderGoodList.get(i).getGoodsUserIdStr() != null) {
                    String goodsUserIdStr = saleOrderGoodList.get(i).getGoodsUserIdStr();
                    List<String> goodsUserIds = Arrays.asList(goodsUserIdStr.split(";"));
                    if (goodsUserIds != null && goodsUserIds.size() > 0) {
                        noPriceBuyorderUser.addAll(goodsUserIds);
                    }
                }
            }
        }
        // noPriceBuyorderUser 去重
        String costUserIds = "";
        if (noPriceBuyorderUser != null && noPriceBuyorderUser.size() > 0) {
            noPriceBuyorderUser = new ArrayList(new HashSet(noPriceBuyorderUser));
            costUserIds = StringUtils.join(noPriceBuyorderUser.toArray(), ",");
        }
        Saleorder saleorderData = new Saleorder();
        saleorderData.setSaleorderId(saleorderId);
        saleorderData.setCostUserIds(costUserIds);
        saleorderService.saveEditSaleorderInfo(saleorderData,user);

    }

    /**
     * 设置商品的用户id集合
     * @param saleOrderGoodList
     */
    private void setSaleOrderGoodUserIdStr(List<SaleorderGoods> saleOrderGoodList,User user) {

        if(CollectionUtils.isEmpty(saleOrderGoodList)){
            return;
        }

        // 刷新未添加产品成本人员ID集合
        List<Integer> categoryIdList = new ArrayList<>();
        for (int i = 0; i < saleOrderGoodList.size(); i++) {
            if (saleOrderGoodList.get(i).getGoods().getCategoryId() == null) {
                categoryIdList.add(0);
            } else {
                categoryIdList.add(saleOrderGoodList.get(i).getGoods().getCategoryId());
            }
        }
        categoryIdList = new ArrayList<Integer>(new HashSet<Integer>(categoryIdList));

        // 根据分类查询对应分类归属，如果是为分配的返回产品部默认人
        List<User> categoryUserList = categoryService.getCategoryOwner(categoryIdList, user.getCompanyId());

        for (int i = 0; i < saleOrderGoodList.size(); i++) {
            for (int j = 0; j < categoryUserList.size(); j++) {
                if (saleOrderGoodList.get(i).getGoods().getCategoryId() == null) {
                    saleOrderGoodList.get(i).getGoods().setCategoryId(0);
                }
                if (categoryUserList.get(j).getCategoryId()
                        .equals(saleOrderGoodList.get(i).getGoods().getCategoryId())) {
                    saleOrderGoodList.get(i)
                            .setGoodsUserIdStr((saleOrderGoodList.get(i).getGoodsUserIdStr() == null ? ""
                                    : saleOrderGoodList.get(i).getGoodsUserIdStr() + ";")
                                    + categoryUserList.get(j).getUserId() + ";");
                }
            }
        }

    }

    /**
     * 请求价格中心 处理成本价
     * @param
     */
    private void dealWithSaleOrderGoodsPrice(Integer saleorderId) {

        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderId);

        List<SaleorderGoods> saleorderGoodList = saleorderGoodsMapper.getSaleordergoodsList(saleorder);

        if(CollectionUtils.isEmpty(saleorderGoodList)){
            return;
        }

        for(SaleorderGoods saleorderGood : saleorderGoodList){

            //对于运费或者参考成本不为0的产品无需判断无需判断
            if("V127063".equals(saleorderGood.getSku()) || BigDecimal.ZERO.compareTo(saleorderGood.getReferenceCostPrice()) != 0){
                continue;
            }

            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = basePriceService.findSkuPriceInfoBySkuNo(saleorderGood.getSku());
            //未核价无需判断
            if (skuPriceInfoDetailResponseDto == null) {
                continue;
            }

            SkuPriceInfoPurchaseDto skuPriceInfoPurchaseDto = skuPriceInfoDetailResponseDto.getPurchaseList().stream()
                    .max((e1, e2) -> e1.compareTo(e2)).orElseGet(null);
            //没有采购价无需判断
            if(skuPriceInfoPurchaseDto == null){
                continue;
            }

            //去修改成本价
            SaleorderGoods updateSaleOrderGoods = new SaleorderGoods();
            updateSaleOrderGoods.setSaleorderGoodsId(saleorderGood.getSaleorderGoodsId());
            updateSaleOrderGoods.setReferenceCostPrice(skuPriceInfoPurchaseDto.getPurchasePrice());
            updateSaleOrderGoods.setModTime(DateUtil.gainNowDate());
            saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGoods);
        }

    }


    private void startProcessInstance(Saleorder saleorderInfo,User user) throws Exception{

        TaskService taskService = processEngine.getTaskService();

        String processKey = "bd_order_auto_verify";
        String businessKey =  processKey + "_" + saleorderInfo.getSaleorderId();
        //VDERP-17686 销售订单增加成本/利润计算 插入N条初始化数据，job每2分钟跑一次，不做实时查询，onedata服务在阿里云，防止接口响应慢影响审批。
        saleorderService.refreshSaleOrderBuyPrice(saleorderInfo.getSaleorderId());

        Map<String, Object> variableMap = new HashMap<String, Object>();

        // 开始生成流程(如果没有taskId表示新流程需要生成)
        variableMap.put("supplyjudge", 1);
        variableMap.put("orgId", user.getOrgId());
        variableMap.put("orderId", saleorderInfo.getSaleorderId());
        variableMap.put("saleorderInfo", saleorderInfo);
        variableMap.put("fawu", false);

        //默认申请人是订单归属人
        String currentAssinee = StringUtil.isBlank(saleorderInfo.getCurrentOperator()) ?
                saleorderInfo.getOptUserName() : saleorderInfo.getCurrentOperator();
        String ownnerUserName = Optional.ofNullable(saleorderInfo.getOwnerUserId())
                .map(ownerUserId -> userService.getUserById(ownerUserId))
                .map(ownUser -> ownUser.getUsername())
                .orElse("");

        // 默认传客户归属
        variableMap.put("currentAssinee", StringUtils.isBlank(ownnerUserName) ? currentAssinee : ownnerUserName);
        variableMap.put("processDefinitionKey", processKey);
        variableMap.put("businessKey", businessKey);
        variableMap.put("relateTableKey", saleorderInfo.getSaleorderId());
        variableMap.put("relateTable", "T_SALEORDER");

        // 设置审核完成监听器回写参数
        variableMap.put("tableName", "T_SALEORDER");
        variableMap.put("id", "SALEORDER_ID");
        variableMap.put("idValue", saleorderInfo.getSaleorderId());
        variableMap.put("key", "VALID_STATUS");
        variableMap.put("value", 1);
        variableMap.put("key1", "STATUS");
        variableMap.put("value1", 1);
        // 回写数据的表在db中
        variableMap.put("db", 2);
        if (StringUtil.isNotBlank(saleorderInfo.getCurrentOperator())){
            variableMap.put("startUser", saleorderInfo.getCurrentOperator());
        }

        actionProcdefService.createProcessInstance(null, processKey,businessKey, variableMap);

        //更新订单的锁定状态
        actionProcdefService.updateInfo("T_SALEORDER", "SALEORDER_ID", saleorderInfo.getSaleorderId(),
                "LOCKED_STATUS", 1, 2);

        Saleorder saleorderLocked = new Saleorder();
        saleorderLocked.setLockedReason("订单审核");
        saleorderLocked.setSaleorderId(saleorderInfo.getSaleorderId());
        saleorderService.saveEditSaleorderInfo(saleorderLocked, user);


        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", true);

        //申请人审核通过
        finishCurrentTask(businessKey,user,currentAssinee,variables,"");

        //销售主管审核通过
        finishCurrentTask(businessKey,user,"njadmin",variables,"订单自动审核");

        variables.put(OrderGoodsAptitudeConstants.KEY_AUTO_CHECK_APTITUDE,false);
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderInfo.getSaleorderId());

        if (saleorder != null && saleorder.getTraderId() != null) {
            TraderCustomer traderCustomer = traderCustomerService.getSimpleCustomer(saleorder.getTraderId());
            if (traderCustomer != null && traderCustomer.getTraderCustomerId() != null) {
                VerifiesInfo verifiesInfo = traderCustomerService.getCustomerAptitudeVerifiesInfo(traderCustomer.getTraderCustomerId());
                if (verifiesInfo != null && OrderGoodsAptitudeConstants.APTITTUDE_IS_PASSED.equals(verifiesInfo.getStatus())) {
                    variables.put(OrderGoodsAptitudeConstants.KEY_AUTO_CHECK_APTITUDE, true);
                }
            }
        }

        //销售经理审核通过
        finishCurrentTask(businessKey,user,"njadmin",variables,"订单自动审核");

        Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();

        if (OrderGoodsAptitudeConstants.KEY_AUTOCHECK_APTITUDE.equals(nextTask.getName())) {

            ResultInfo resultInfo = saleorderService.checkGoodAptitude(saleorderInfo.getSaleorderId());

            boolean auditPass = true;
            if(resultInfo.getCode() == 0 || resultInfo.getCode() == 1){
                auditPass = true;
            }else {
                auditPass = false;
            }

            variableMap.put("pass",auditPass);

            String comment = auditPass ? saleorderService.getCommentsOfAutoCheck(saleorderInfo.getSaleorderId()) : resultInfo.getMessage();

            //资质自动审核
            finishCurrentTask(businessKey,user,"njadmin",variableMap,comment);


           /* ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, nextTask.getId(), "订单自动审核","njadmin", variableMap);

            if(!complementStatus.getData().equals("endEvent")){
                verifiesRecordService.saveVerifiesInfo(nextTask.getId(), 0);
            }*/

        }
    }

    private void finishCurrentTask(String businessKey,User user,String currentAssinee,Map<String, Object> variables,String comment) {
        Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
        String taskId = taskInfo.getId();

        Authentication.setAuthenticatedUserId(user.getUsername());

        ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskId, comment,
                currentAssinee, variables);
        // 如果未结束添加审核对应主表的审核状态
        if (!complementStatus.getData().equals("endEvent")) {
            verifiesRecordService.saveVerifiesInfo(taskId, 0);
        }
    }


    /**
     * @Description 取消自动审核
     * @Param saleorderInfo
     */
    private void cancelAutoAudit(Saleorder saleorderInfo) {
        logger.info("bd订单取消自动审核，saleOrderId = {}",saleorderInfo.getSaleorderId());
        Saleorder updateSaleOrder = new Saleorder();
        updateSaleOrder.setSaleorderId(saleorderInfo.getSaleorderId());
        updateSaleOrder.setAutoAudit(ErpConst.ZERO);
        saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);
    }

}
