package com.wms.service.stockcalculate;

/**
 * <AUTHOR>
 * @ClassName AbstractStockinfoCaculateInterace.java
 * @Description TODO 逻辑库占用算法抽象类
 * @createTime 2020年07月21日 13:26:00
 */
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public abstract class AbstractStockinfoCaculateInterace implements StockinfoCaculateInterface {

    Logger logger = LoggerFactory.getLogger(AbstractStockinfoCaculateInterace.class);

    @Override
    public List<WarehouseDto> calculateStockInfo(List<StockCalculateDto> details) {

        List<WarehouseDto> warehouseStockList = new ArrayList<>();

        calculateStockNum(details,warehouseStockList);

        calculateOcupyNum(details,warehouseStockList);

        return warehouseStockList;
    }


    protected abstract void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList);

    protected abstract void calculateOcupyNum(List<StockCalculateDto> details,List<WarehouseDto> warehouseStockList);

    // begin 对于同一个商品拆包裹，导致库存错误问题修复 hollis

    /**
     * @param details
     * @param warehouseStockList
     * @param isDeduct 判断是- 还是+
     * @param isStock true stock false occupy
     */
    public void calculateStockOrOccupyNumBase(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList,boolean isDeduct,boolean isStock){
            details.stream().forEach(stockCalculateDto->{

                WarehouseDto warehouseStock = this.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                        stockCalculateDto.getSku(),
                        stockCalculateDto.getLogicalWarehouseId());
                int num = 1;
                if (isDeduct) {
                    num = -1;
                }
                if (isStock) {
                    warehouseStock.setStockNum(num * stockCalculateDto.getStockNum() + warehouseStock.getStockNum());
                } else {
                    warehouseStock.setOccupyNum(num * stockCalculateDto.getOccupyNum() + warehouseStock.getOccupyNum());
                }

            });

    }
    // end 对于同一个商品拆包裹，导致库存错误问题修复 hollis

    public WarehouseDto getWarehouseStockBySkuAndLogcic(List<WarehouseDto> warehouseStockList, String sku,Integer logicalWarehouseId){

        WarehouseDto warehouseStock = warehouseStockList.stream()
                .filter(stock -> {
                    return stock.getSku().equals(sku) && stock.getLogicalWarehouseId().equals(logicalWarehouseId);
                })
                .findFirst()
                .orElse(null);

        if(warehouseStock == null){
            warehouseStock = new WarehouseDto();
            warehouseStock.setSku(sku);
            warehouseStock.setLogicalWarehouseId(logicalWarehouseId);
            warehouseStock.setStockNum(0);
            warehouseStock.setOccupyNum(0);
            warehouseStockList.add(warehouseStock);
        }

        return warehouseStock;

    }

}
