package com.vedeng.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.aftersales.service.impl.AfterSalesServiceImpl;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.BusinessSourceTypeEnum;
import com.vedeng.common.core.enums.SettlementTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.finance.dao.InvoiceAfterMapper;
import com.vedeng.finance.dao.InvoiceMapper;
import com.vedeng.finance.enums.InvoiceTypeEnum;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceAfter;
import com.vedeng.finance.model.InvoiceDetail;
import com.vedeng.finance.service.InvoiceAfterService;
import com.vedeng.goods.dao.RCategoryJUserMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.model.dto.GoodsOperateNumDto;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.BuyorderDataService;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.order.service.SaleorderDataService;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service("invoiceAfterService")
public class InvoiceAfterServiceImpl extends BaseServiceimpl implements InvoiceAfterService{
	public static Logger logger = LoggerFactory.getLogger(InvoiceAfterServiceImpl.class);

	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;
	
	@Autowired
	@Qualifier("rCategoryJUserMapper")
	private RCategoryJUserMapper rCategoryJUserMapper;

	@Resource
	private InvoiceMapper invoiceMapper;

	@Resource
	private BuyorderMapper buyorderMapper;

	@Autowired
	private OrderAccountPeriodService orderAccountPeriodService;

	@Resource
	private InvoiceAfterMapper invoiceAfterMapper;

	@Autowired
	private SaleorderDataService saleorderDataService;

	@Resource
	private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

	@Autowired
	private BuyorderInfoSyncService buyorderInfoSyncService;

	@Autowired
	private AfterSalesMapper afterSalesMapper;

	@Resource
	private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

	@Autowired
	private InvoiceApiService invoiceApiService;

	@Autowired
	private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;

	@Autowired
	private AfterSalesService afterSalesService;

	@Autowired
	private AfterSalesServiceImpl afterSalesServiceImpl;

	@Autowired
	private BuyorderDataService buyorderDataService;

	@Autowired
	private CapitalBillApiService capitalBillApiService;


	@Override
	public Map<String,Object> getFinanceAfterListPage(InvoiceAfter invoiceAfter, Page page) {
		Map<String,Object> map = new HashMap<>();
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<List<InvoiceAfter>>> TypeRef = new TypeReference<ResultInfo<List<InvoiceAfter>>>() {};
		String url=httpUrl + "finance/after/getfinanceafterlistpage.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, invoiceAfter,clientId,clientKey, TypeRef ,page);
			if(result!=null && result.getCode()==0){
				map.put("page", result.getPage());
				
				List<InvoiceAfter> invoiceAfterList = (List<InvoiceAfter>)result.getData();
				map.put("invoiceAfterList", invoiceAfterList);
				List<Integer> userIdList = new ArrayList<>();//创建人员
				for(int i=0;i<invoiceAfterList.size();i++){
					userIdList.add(invoiceAfterList.get(i).getCreator());
					userIdList.add(invoiceAfterList.get(i).getServiceUserId());
				}
				userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
				List<User> userList = new ArrayList<>();
				if(userIdList.size() > 0){
					userList = userMapper.getUserByUserIds(userIdList);
					map.put("userList", userList);
				}
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return map;
	}

	@Override
	public AfterSalesVo getFinanceAfterSaleDetail(AfterSalesVo afterSales,HttpSession session) {
		try {
			if(afterSales.getSubjectType() == null || afterSales.getType() == null){
				// 定义反序列化 数据格式
				final TypeReference<ResultInfo<AfterSalesVo>> TypeRef2 = new TypeReference<ResultInfo<AfterSalesVo>>() {};
				AfterSales as = new AfterSales();
				as.setAfterSalesId(afterSales.getAfterSalesId());
				String url2 = httpUrl + "aftersales/order/selectbyid.htm";
				ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(url2, as, clientId, clientKey, TypeRef2);
				if(result2 != null && result2.getCode() == 0){
					afterSales = (AfterSalesVo)result2.getData();
					afterSales.setTraderType(afterSales.getSubjectType());
				}
			}
			String url = "";
			//获取售后订单基本信息-售后申请-所属订单
			if(afterSales.getType().intValue() == 539){//销售订单退货
				url = httpUrl + "finance/after/getfinanceaftersalethdetail.htm";
			}else if(afterSales.getType().intValue() == 540){//销售换货
				url = httpUrl + "finance/after/getfinanceaftersalehhdetail.htm";
			}else if(afterSales.getType().intValue() == 541 || afterSales.getType().intValue() == 584 || afterSales.getType().intValue() == 4090 || afterSales.getType().intValue() == 4091){//541销售安调--584销售维修
				url = httpUrl + "finance/after/getfinanceaftersaleatdetail.htm";
			}else if(afterSales.getType().intValue() == 542||afterSales.getType().intValue() == 1135){//销售退票
				url = httpUrl + "finance/after/getfinanceaftersaletpdetail.htm";
			}else if(afterSales.getType().intValue() == 543){//销售退款
				url = httpUrl + "finance/after/getfinanceaftersaletkdetail.htm";
			}else if(afterSales.getType().intValue() == 546){//采购退货
				url = httpUrl + "finance/after/getfinanceafterbuythdetail.htm";
			}else if(afterSales.getType().intValue() == 547){//采购换货
				url = httpUrl + "finance/after/getfinanceafterbuyhhdetail.htm";
			}else if(afterSales.getType().intValue() == 550 || afterSales.getType().intValue() == 585){//550第三方安调--585第三方维修
				url = httpUrl + "finance/after/getfinanceafterotheratdetail.htm";
			}else if(afterSales.getType().intValue() == 551){//第三方退款
				url = httpUrl + "finance/after/getfinanceafterothertkdetail.htm";
			}
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
			ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef);
			if(result != null && result.getCode() == 0){
				
				JSONObject json = JSONObject.fromObject(result.getData());
				AfterSalesVo afterSalesVo = JsonUtils.readValue(json.toString(), AfterSalesVo.class);
				if(afterSalesVo != null){
					User user = (User)session.getAttribute(ErpConst.CURR_USER);
					
					afterSalesVo.setOrgName(getOrgNameByOrgId(afterSalesVo.getOrgId()));
					List<Integer> userIdList = new ArrayList<>();//创建人员
					userIdList.add(afterSalesVo.getCreator());//创建者
					userIdList.add(afterSalesVo.getUserId());//归属销售
					//退货产品信息
					if(afterSalesVo.getAfterSalesGoodsList() != null){
						for (AfterSalesGoodsVo asgv : afterSalesVo.getAfterSalesGoodsList()) {
							//产品负责人
							asgv.setGoodsHeader(rCategoryJUserMapper.getUserByCategoryNm(asgv.getCategoryId(), user.getCompanyId()));

						}
					}

					//退货入库记录
					if(afterSalesVo.getAfterReturnGoodsInStorageList() != null){
						for(int i=0;i<afterSalesVo.getAfterReturnGoodsInStorageList().size();i++){
							userIdList.add(afterSalesVo.getAfterReturnGoodsInStorageList().get(i).getCheckStatusUser());//退货入库操作人
							userIdList.add(afterSalesVo.getAfterReturnGoodsInStorageList().get(i).getCreator());//退货入库操作人
						}
					}
					//退货出库记录
					if(afterSalesVo.getAfterReturnGoodsOutStorageList() != null){
						for(int i=0;i<afterSalesVo.getAfterReturnGoodsOutStorageList().size();i++){
							userIdList.add(afterSalesVo.getAfterReturnGoodsOutStorageList().get(i).getCheckStatusUser());//退货入库操作人
							userIdList.add(afterSalesVo.getAfterReturnGoodsOutStorageList().get(i).getCreator());//退货入库操作人
						}
					}
					//售后过程
					if(afterSalesVo.getAfterSalesRecordVoList() != null){
						for (AfterSalesRecordVo asrv : afterSalesVo.getAfterSalesRecordVoList()) {
							userIdList.add(asrv.getCreator());//售后过程操作人
						}
					}
					//发票记录
					if(afterSalesVo.getAfterOpenInvoiceList() != null){
						for(int i=0;i<afterSalesVo.getAfterOpenInvoiceList().size();i++){
							if(afterSalesVo.getAfterOpenInvoiceList().get(i)!=null){
								userIdList.add(afterSalesVo.getAfterOpenInvoiceList().get(i).getCreator());//发票记录操作人
								userIdList.add(afterSalesVo.getAfterOpenInvoiceList().get(i).getValidUserId());//发票审核人
							}
						}
					}
					//录票记录
					if(afterSalesVo.getAfterInInvoiceList() != null){
						for(int i=0;i<afterSalesVo.getAfterInInvoiceList().size();i++){
							if(afterSalesVo.getAfterInInvoiceList().get(i)!=null){
								userIdList.add(afterSalesVo.getAfterInInvoiceList().get(i).getCreator());//发票记录操作人
								userIdList.add(afterSalesVo.getAfterInInvoiceList().get(i).getValidUserId());//发票审核人
							}
						}
					}
					//已退票记录
					if(afterSalesVo.getAfterReturnInvoiceVoList() != null){
						for(int i=0;i<afterSalesVo.getAfterReturnInvoiceVoList().size();i++){
							if(afterSalesVo.getAfterReturnInvoiceVoList().get(i)!=null){
								userIdList.add(afterSalesVo.getAfterReturnInvoiceVoList().get(i).getCreator());//发票记录操作人
							}
						}
					}
					if(afterSalesVo.getAfterSalesInvoiceVoList()!=null){
					    for(int i=0;i<afterSalesVo.getAfterSalesInvoiceVoList().size();i++){
					        if(afterSalesVo.getAfterSalesInvoiceVoList().get(i)!=null){
					            userIdList.add(afterSalesVo.getAfterSalesInvoiceVoList().get(i).getCreator());
                            }
							AfterSalesInvoiceVo afterSalesInvoiceVo = afterSalesVo.getAfterSalesInvoiceVoList().get(i);
							afterSalesInvoiceVo.setAfterSalesId(afterSalesVo.getAfterSalesId());
							afterSalesInvoiceVo = getAfterReturnInvoiceInfo(afterSalesInvoiceVo);
							afterSalesVo.getAfterSalesInvoiceVoList().get(i).setCurrentMonthInvoice(afterSalesInvoiceVo.getCurrentMonthInvoice());
                        }
                    }
					//交易记录
					if(afterSalesVo.getAfterCapitalBillList() != null){
						for(int i=0;i<afterSalesVo.getAfterCapitalBillList().size();i++){
							userIdList.add(afterSalesVo.getAfterCapitalBillList().get(i).getCreator());//交易记录操作人
						}
					}
					//退票材料
					if(afterSalesVo.getAfterInvoiceAttachmentList() != null){
						for(int i=0;i<afterSalesVo.getAfterInvoiceAttachmentList().size();i++){
							userIdList.add(afterSalesVo.getAfterInvoiceAttachmentList().get(i).getCreator());//退票材料操作人
						}
					}
					//售后过程
					if(afterSalesVo.getAfterSalesRecordVoList() != null){
						for(int i=0;i<afterSalesVo.getAfterSalesRecordVoList().size();i++){
							userIdList.add(afterSalesVo.getAfterSalesRecordVoList().get(i).getCreator());//售后过程操作人
						}
					}
					//合同回传
					if(afterSalesVo.getAfterContractAttachmentList() != null){
						for(int i=0;i<afterSalesVo.getAfterContractAttachmentList().size();i++){
							userIdList.add(afterSalesVo.getAfterContractAttachmentList().get(i).getCreator());//合同回传操作人
						}
					}
					//付款记录
					if(afterSalesVo.getAfterPayApplyList() != null){
						for(int i=0;i<afterSalesVo.getAfterPayApplyList().size();i++){
							userIdList.add(afterSalesVo.getAfterPayApplyList().get(i).getCreator());//付款记录申请人
						}
					}
					//物流信息
					if(afterSalesVo.getExpresseList() != null){
						for(int i=0;i<afterSalesVo.getExpresseList().size();i++){
							userIdList.add(afterSalesVo.getExpresseList().get(i).getCreator());//物流操作人
						}
					}
					userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
					List<User> userList = userMapper.getUserByUserIds(userIdList);
					afterSalesVo.setUserList(userList);
					afterSalesVo.setUserName(getUserNameByUserId(afterSalesVo.getUserId()));

					if(afterSalesVo.getServiceUserId() != null && afterSalesVo.getServiceUserId() > 0){
						afterSalesVo.setServiceUserName(getUserNameByUserId(afterSalesVo.getServiceUserId()));
					}

					afterSalesVo.setAfterSalesStatusUserName(getUserNameByUserId(afterSalesVo.getAfterSalesStatusUser()));
					if(afterSalesVo.getType() == 541 || afterSalesVo.getType() == 584 || afterSalesVo.getType() == 550 || afterSalesVo.getType() == 585
							|| afterSalesVo.getType() == 4090 || afterSalesVo.getType() == 4091){
						//安调维修录票
						afterSalesVo.setAfterSalesInvoiceVoList(afterSalesInvoiceMapper.getAfterSalesAWInvoiceVoList(afterSalesVo.getAfterSalesId()));
					}
					//关联采购单号
					setRelatedBuyorderNos(afterSalesVo);
					if (afterSalesVo.getType() == 546){
						afterSalesServiceImpl.calBuyOrderAfterSalesTh(afterSalesVo);
					}
				}
				return afterSalesVo;
			}
		} catch (IOException e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return null;
	}

	public void setRelatedBuyorderNos(AfterSalesVo afterSalesVo){
		List<AfterSalesGoodsVo> list = afterSalesVo.getAfterSalesGoodsList();
		List<Integer> saleOrderGoodsIdList = new ArrayList<>();
		List<BuyorderGoodsVo> buyOrderGoodsList = new ArrayList<>();
		for(int i=0;i<list.size();i++){
			saleOrderGoodsIdList.add(list.get(i).getOrderDetailId());
		}
		buyOrderGoodsList = saleorderDataService.getBuyOrderInfoBySaleGoodsIdForAfterSale(saleOrderGoodsIdList);
		for (AfterSalesGoodsVo asgv : list) {
			for(BuyorderGoodsVo buyGoods:buyOrderGoodsList){
				if(buyGoods.getSaleorderGoodsId().equals(asgv.getOrderDetailId())){
					if(buyGoods.getBuyOrderNoStr() != null && buyGoods.getBuyOrderIdStr() != null){
						List<Buyorder> buyorderList = new ArrayList<>();
						String[] split = buyGoods.getBuyOrderNoStr().split(",");
						if(split != null && split.length > 0){
							for(int m=0;m<split.length;m++){
								Buyorder buyOrder = new Buyorder();
								buyOrder.setBuyorderNo(split[m]);
								String s = buyGoods.getBuyOrderIdStr().split(",")[m];
								buyOrder.setBuyorderId(Integer.valueOf("".equals(s)?"0":s));
								buyorderList.add(buyOrder);
							}
						}
						asgv.setBuyorderNos(buyorderList);
					}
				}
			}
		}
	}

	@Override
	public AfterSalesDetailVo getAfterCapitalBillInfo(AfterSalesDetailVo afterDetailVo) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<AfterSalesDetailVo>> TypeRef = new TypeReference<ResultInfo<AfterSalesDetailVo>>() {};
		String url=httpUrl + "finance/after/getaftercapitalbillinfo.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterDetailVo,clientId,clientKey, TypeRef);
			if(result!=null && result.getCode()==0){
				
				AfterSalesDetailVo afterSalesDetailVo = (AfterSalesDetailVo)result.getData();
				return afterSalesDetailVo;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return null;
	}

	@Override
	public AfterSalesInvoiceVo getAfterReturnInvoiceInfo(AfterSalesInvoiceVo afterSalesInvoiceVo) {
		if(afterSalesInvoiceVo.getType() != null && afterSalesInvoiceVo.getType().equals(SysOptionConstant.ID_548)){
			//采购仅退票
			AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesInvoiceVo.getAfterSalesId());
			afterSalesInvoiceVo.setAfterType(afterSales.getType());
			afterSalesInvoiceVo.setOrderId(afterSales.getOrderId());
			afterSalesInvoiceVo.setSubjectType(afterSales.getSubjectType());
			//写死红字有效
			afterSalesInvoiceVo.setCurrentMonthInvoice(ErpConst.ZERO);
			//查询仅退票发票信息
			InvoiceDto invoiceDto = invoiceApiService.queryInvoiceByInvoiceNoAndRelatedId(afterSalesInvoiceVo.getInvoiceNo(),afterSales.getOrderId(),SysOptionConstant.ID_503);
			afterSalesInvoiceVo.setInvoiceCode(invoiceDto.getInvoiceCode());
			afterSalesInvoiceVo.setInvoiceNo(invoiceDto.getInvoiceNo());
			afterSalesInvoiceVo.setAmount(invoiceDto.getAmount());
			afterSalesInvoiceVo.setInvoiceType(invoiceDto.getInvoiceType());
			afterSalesInvoiceVo.setInvoiceProperty(invoiceDto.getInvoiceProperty());
			afterSalesInvoiceVo.setRatio(invoiceDto.getRatio());
			afterSalesInvoiceVo.setOldInvoiceNo(invoiceDto.getInvoiceNo());
			List<AfterSalesGoodsVo> afterSalesGoodsVoList = invoiceAfterMapper.queryTpAfterSalesGoods(afterSales.getAfterSalesId());
			afterSalesInvoiceVo.setAfterGoodsList(afterSalesGoodsVoList);
			return afterSalesInvoiceVo;
		}else {
			//获取发票信息
			afterSalesInvoiceVo = invoiceAfterMapper.getAfterReturnInvoiceInfo(afterSalesInvoiceVo);
			if (afterSalesInvoiceVo == null) {
				return null;
			}

            //发票为当月发票时，直接蓝字作废
            if (Objects.nonNull(afterSalesInvoiceVo.getAfterType()) && SysOptionConstant.ID_546.equals(afterSalesInvoiceVo.getAfterType())) {
                afterSalesInvoiceVo.setCurrentMonthInvoice(ErpConst.ZERO);
                afterSalesInvoiceVo.setAmountCount(invoiceAfterMapper.selectRedInvoiceAmountCount(afterSalesInvoiceVo.getInvoiceId()));
            } else if (DateUtil.convertString(afterSalesInvoiceVo.getAddTime(), DateUtil.DATE_FORMAT_MONTH)
                    .equals(DateUtil.convertString(DateUtil.gainNowDate(), DateUtil.DATE_FORMAT_MONTH))) {
                afterSalesInvoiceVo.setCurrentMonthInvoice(ErpConst.ONE);
            } else {
                //非当月发票，红字有效
                afterSalesInvoiceVo.setCurrentMonthInvoice(ErpConst.ZERO);
                afterSalesInvoiceVo.setAmountCount(invoiceAfterMapper.selectRedInvoiceAmountCount(afterSalesInvoiceVo.getInvoiceId()));
            }

			//销售
			if (afterSalesInvoiceVo.getSubjectType().intValue() == SysOptionConstant.ID_535) {
				afterSalesInvoiceVo.setAfterGoodsList(invoiceAfterMapper.getAfterReturnInvoiceGoodsList(afterSalesInvoiceVo));
				//采购
			}
			if (afterSalesInvoiceVo.getSubjectType().intValue() == SysOptionConstant.ID_536) {
				//查询采购发票中产品信息
				afterSalesInvoiceVo.setAfterGoodsList(invoiceAfterMapper.getAfterReturnInvoiceGoodsList(afterSalesInvoiceVo));
				setBuyOrderWarehouseOperateLogInfo(afterSalesInvoiceVo);
			}

			setCanRecordAmount(afterSalesInvoiceVo);
			return afterSalesInvoiceVo;
		}
	}

	/**
	 * 处理采购单商品出入库信息
	 * @param afterSalesInvoiceVo
	 */
	private void setBuyOrderWarehouseOperateLogInfo(AfterSalesInvoiceVo afterSalesInvoiceVo) {
		if(CollectionUtils.isEmpty(afterSalesInvoiceVo.getAfterGoodsList())){
			return;
		}

		if (!SysOptionConstant.ID_546.equals(afterSalesInvoiceVo.getAfterType())){
			return;
		}

		List<Integer> afterSalesGoodsIdList = afterSalesInvoiceVo.getAfterGoodsList().stream()
				.map(AfterSalesGoods::getAfterSalesGoodsId).filter(integer -> integer > 0).collect(Collectors.toList());

		if (CollectionUtils.isEmpty(afterSalesGoodsIdList)){
			return;
		}

		List<GoodsOperateNumDto> goodsOperateNumByGoodsIds = warehouseGoodsOutInItemMapper.getGoodsOperateNumByGoodsIds(afterSalesGoodsIdList);

		if (CollectionUtils.isEmpty(goodsOperateNumByGoodsIds)){
			afterSalesInvoiceVo.getAfterGoodsList().forEach(item -> item.setOutcnt(ErpConst.ZERO));
		}

		Map<Integer, Integer> afterSaleGoodsNumMap = goodsOperateNumByGoodsIds.stream().collect(Collectors.toMap(GoodsOperateNumDto::getRelatedId, GoodsOperateNumDto::getTotalNum));

		afterSalesInvoiceVo.getAfterGoodsList().forEach(item -> {
			item.setOutcnt(afterSaleGoodsNumMap.getOrDefault(item.getAfterSalesGoodsId(), ErpConst.ZERO));
			item.setCanReturnInvoiceNum(Collections.min(Arrays.asList(item.getNum(), item.getInvoiceNum().intValue(), item.getOutcnt())));
		});
	}

	/**
	 * VDERP-2352 【财务管理】采购录票允许存在微小差异
	 *
	 * @param afterSalesInvoiceVo
	 */
	private void setCanRecordAmount(AfterSalesInvoiceVo afterSalesInvoiceVo) {
		try {
			if (afterSalesInvoiceVo == null || CollectionUtils.isEmpty(afterSalesInvoiceVo.getAfterGoodsList())){
				return;
			}
			Integer subjectType = afterSalesInvoiceVo.getSubjectType();
			afterSalesInvoiceVo.getAfterGoodsList().forEach(afterSalesGoodsVo -> {
				BigDecimal invoiceTotalAmount = null;
				BigDecimal invoiceTotalNum = null;
				if(SysOptionConstant.ID_535.equals(subjectType)){
					invoiceTotalAmount = invoiceMapper.getSaleorderHaveInvoiceTotalAmount(afterSalesGoodsVo.getOrderDetailId());
				}
				if(SysOptionConstant.ID_536.equals(subjectType)){
					if (SysOptionConstant.ID_546.equals(afterSalesInvoiceVo.getAfterType())){
						invoiceTotalNum = new BigDecimal(afterSalesGoodsVo.getCanReturnInvoiceNum().toString());
						invoiceTotalAmount  = invoiceTotalNum.multiply(afterSalesGoodsVo.getOrderPrice().subtract(afterSalesGoodsVo.getRebatePrice()));
					} else {
						invoiceTotalAmount = buyorderMapper.getHaveInvoiceTotalAmount(afterSalesGoodsVo.getOrderDetailId());
						invoiceTotalNum = buyorderMapper.getHaveInvoiceNums(afterSalesGoodsVo.getOrderDetailId());
					}
					logger.info("采购订单商品的收票总额 buyorderGoodsId:{}，invoiceTotalAmount:{},invoiceTotalNum{},",
							afterSalesGoodsVo.getOrderDetailId(), invoiceTotalAmount,invoiceTotalNum);
				}
				afterSalesGoodsVo.setCanRecordAmount(invoiceTotalAmount != null ? invoiceTotalAmount : BigDecimal.ZERO);
				afterSalesGoodsVo.setCanRecordNum(invoiceTotalNum != null ? invoiceTotalNum : BigDecimal.ZERO);
			});
		} catch (Exception e) {
			logger.error("计算采购售后商品可录票金额 error", e);
		}
	}

	@Override
	public void preSaveFullyDigitalRedInvoice(InvoiceRedConfirmationApiDto invoiceRedConfirmationDto) {
		// 校验发票是否已退票
		AfterSalesInvoice afterSalesInvoice = afterSalesInvoiceMapper.selectOneByRelatedId(
				invoiceRedConfirmationDto.getAfterSaleBusinessOrderId()
				, invoiceRedConfirmationDto.getBlueInvoiceId());
		if (afterSalesInvoice == null) {
			logger.error("无退票信息,售后id:{}", invoiceRedConfirmationDto.getAfterSaleBusinessOrderId());
			throw new ServiceException("无退票信息");
		}
		if (ErpConstant.ONE.equals(afterSalesInvoice.getStatus())) {
			return;
		}

		try {
			Invoice invoice = new Invoice();
			BigDecimal totalAmount = null;
			List<Integer> saleOrderGoodsIds = null;
			List<BigDecimal> invoiceNums = null;
			List<BigDecimal> invoicePriceList = null;
			List<BigDecimal> invoiceTotalAmountList = null;
			BigDecimal taxRate = CollUtil.getFirst(invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList()).getTaxRate();
			// 全部退票根据售后单和蓝票组装数据
			if(Objects.equals(invoiceRedConfirmationDto.getRedConfirmationScope(), ErpConstant.ZERO)){
				List<InvoiceDetailDto> invoiceDetailDtoList = invoiceApiService.findByInvoiceId(CollUtil.newArrayList(invoiceRedConfirmationDto.getBlueInvoiceId()));
				invoiceDetailDtoList.sort(Comparator.comparing(InvoiceDetailDto::getInvoiceDetailId));
				totalAmount = invoiceDetailDtoList.stream()
						.map(InvoiceDetailDto::getTotalAmount).map(BigDecimal::abs).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
				saleOrderGoodsIds = invoiceDetailDtoList.stream()
						.map(InvoiceDetailDto::getDetailgoodsId).collect(Collectors.toList());
				invoiceNums = invoiceDetailDtoList.stream()
						.map(InvoiceDetailDto::getNum).map(BigDecimal::abs).collect(Collectors.toList());
				invoicePriceList = invoiceDetailDtoList.stream()
						.map(d -> d.getPrice().divide(BigDecimal.ONE.add(taxRate), 8, RoundingMode.HALF_UP)).map(BigDecimal::abs).collect(Collectors.toList());
				invoiceTotalAmountList = invoiceDetailDtoList.stream()
						.map(InvoiceDetailDto::getTotalAmount).map(BigDecimal::abs).collect(Collectors.toList());
			}

			// 部分退票根据红字确认单明细组装数据
			if (Objects.equals(invoiceRedConfirmationDto.getRedConfirmationScope(), ErpConstant.ONE)) {
				// 排序，保证与前端参数顺序一直一致
				invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList()
						.sort(Comparator.comparing(InvoiceRedConfirmationItemApiDto::getInvoiceRedConfirmationItemId));
				totalAmount = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList().stream()
						.map(InvoiceRedConfirmationItemApiDto::getPricePlusTaxes).map(BigDecimal::abs).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
				saleOrderGoodsIds = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList().stream()
						.map(InvoiceRedConfirmationItemApiDto::getBusinessOrderItemId).collect(Collectors.toList());
				invoiceNums = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList().stream()
						.map(InvoiceRedConfirmationItemApiDto::getQuantity).map(BigDecimal::abs).collect(Collectors.toList());
				invoicePriceList = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList().stream()
						.map(InvoiceRedConfirmationItemApiDto::getUnitPrice).map(BigDecimal::abs).collect(Collectors.toList());
				invoiceTotalAmountList = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList().stream()
						.map(InvoiceRedConfirmationItemApiDto::getPricePlusTaxes).map(BigDecimal::abs).collect(Collectors.toList());
			}

			invoice.setAmount(totalAmount);
			invoice.setDetailGoodsIdList(saleOrderGoodsIds);
			invoice.setInvoiceNumList(invoiceNums);
			invoice.setInvoicePriceList(invoicePriceList);
			invoice.setInvoiceTotleAmountList(invoiceTotalAmountList);

			// 售后退票id
			invoice.setAfterId(afterSalesInvoice.getAfterSalesInvoiceId());
			invoice.setOpenInvoiceTime(invoiceRedConfirmationDto.getOpenRedInvoiceTime());
			invoice.setOpenInvoiceTimeStr(cn.hutool.core.date.DateUtil.formatDateTime(invoiceRedConfirmationDto.getOpenRedInvoiceTime()));
			invoice.setRatio(taxRate);
			InvoiceDto invoiceDto = invoiceApiService.findbyInvoiceId(invoiceRedConfirmationDto.getBlueInvoiceId());
			invoice.setInvoiceType(invoiceDto.getInvoiceType());
			invoice.setInvoiceNo(invoiceRedConfirmationDto.getRedInvoiceNo());
			invoice.setRelatedId(invoiceRedConfirmationDto.getBusinessOrderId());
			invoice.setAfterSalesId(invoiceRedConfirmationDto.getAfterSaleBusinessOrderId());
			invoice.setInvoiceId(invoiceRedConfirmationDto.getBlueInvoiceId());

			boolean hasEmpty = ObjectUtil.hasEmpty(invoice.getAfterId(), invoice.getAmount(), invoice.getDetailGoodsIdList(), invoice.getInvoiceNumList()
					, invoice.getInvoicePriceList(), invoice.getInvoiceTotleAmountList(), invoice.getOpenInvoiceTime(), invoice.getOpenInvoiceTimeStr()
					, invoice.getRatio(), invoice.getInvoiceType(), invoice.getInvoiceNo(), invoice.getRelatedId(), invoice.getAfterSalesId()
					, invoice.getInvoiceId());

			if(hasEmpty){
				logger.error("售后开红票参数丢失,{}",JSON.toJSONString(invoice));
				throw new ServiceException("售后开红票参数丢失，请检查");
			}


			invoice.setInvoiceProperty(3);
			invoice.setType(SysOptionConstant.ID_505);
			invoice.setTag(1);
			invoice.setIsEnable(1);
			// 数电发票默认红字有效
			invoice.setColorType(1);
			invoice.setValidStatus(1);
			invoice.setInvoiceCode("0000000000");


			//保存售后退票发票信息
			CurrentUser user = CurrentUser.getCurrentUser();
			invoice.setAddTime(DateUtil.gainNowDate());
			invoice.setCompanyId(ErpConst.NJ_COMPANY_ID);
			invoice.setCreator(user.getId());
			invoice.setModTime(DateUtil.gainNowDate());
			invoice.setUpdater(user.getId());

			ResultInfo<?> resultInfo = this.saveAfterReturnInvoice(invoice);
			if(resultInfo.getCode().equals(ResultInfo.ResultCode.ERROR.getCode())){
				throw new ServiceException(resultInfo.getMessage());
			}
			//刷新售后订单的退票状态
			logger.info("退货退票保存刷新售后订单的退票状态 afterSalesId:{}", invoice.getAfterSalesId());
			afterSalesService.refreshInvoiceRefundStatus(invoice.getAfterSalesId());
		} catch (Exception e) {
			logger.error("调用old售后开票逻辑异常:", e);
			throw new ServiceException("调用old售后开票逻辑异常", e);
		}
	}

	@Override
	public ResultInfo<?> saveAfterReturnInvoice(Invoice invoice) {
		logger.info("发票信息:{}",JSON.toJSONString(invoice));
		dealBuyOrderBlueInvalidInvoice(invoice);

		// 保存售后退票发票信息
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "finance/after/saveafterreturninvoice.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, invoice,clientId,clientKey, TypeRef);

			dealBuyorderInvoiceStatus(invoice);

			dealBuyOrderInvoiceRelationOperateLog(invoice);

			invoiceAfterMapper.updateAfterInvoiceStatus(invoice.getAfterSalesId(),invoice.getInvoiceId(),invoice.getUpdater(),invoice.getModTime());

			// 计算更新售后单的 退票状态
			buyorderInfoSyncService.calculateAndUpdateInvoiceRefundStatus(invoice.getAfterSalesId());
			logger.info("财务售后页面确认退票保存后计算并更新售后单的退票状态， 售后单id：{}", invoice.getAfterSalesId());

			if(result!=null && result.getCode()==0){

				if (invoice.getType() == 505) {// 销售
					if (ErpConst.THREE.equals(invoice.getInvoiceProperty())) {
						Invoice currentInvoice = invoiceMapper.selectByPrimaryKey(invoice.getInvoiceId());
						// 同时保存下载发票任务
						DownloadInvoice data = new DownloadInvoice();
						data.setInvoiceNo(invoice.getInvoiceNo());
						data.setInvoiceApplyId(currentInvoice.getInvoiceApplyId().toString());
						data.setInvoiceDate(invoice.getOpenInvoiceTimeStr());
						data.setSkipCheckExtend(Boolean.TRUE);
						fullyDigitalInvoiceApiService.downloadInvoice(data);
					}
				}

				logger.info("售后退票确认进行账期逾期编码处理 invoice:{}", JSON.toJSONString(invoice));
				orderAccountPeriodService.dealAccountPeriodOverdueCodeByRefundInvoice(invoice);
				return result;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return ResultInfo.error();
		}
		return ResultInfo.error();
	}

	/**
	 * 处理采购售后发票关联出库关系问题
	 * @param invoice
	 */
	private void dealBuyOrderInvoiceRelationOperateLog(Invoice invoice) {
		try {
			logger.info("处理采购售后发票关联出库关系问题 invoice:{}", JSON.toJSONString(invoice));

            AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(invoice.getAfterSalesId());
			if (afterSales == null || !SysOptionConstant.ID_536.equals(afterSales.getSubjectType())){
				logger.info("处理采购售后发票关联出库关系问题非采购订单 invoiceNo:{}", invoice.getInvoiceNo());
				return;
			}
			if (afterSales.getValidTime() < 1672502400000L){
			    return;
            }

			if (!SysOptionConstant.ID_546.equals(afterSales.getType())){
				logger.info("处理采购售后发票关联出库关系问题非退货订单 invoiceNo:{}", invoice.getInvoiceNo());
				return;
			}

			if (!ErpConst.ONE.equals(invoice.getColorType())){
				logger.warn("处理采购售后发票关联出库关系问题不为红字 invoice:{}", JSON.toJSONString(invoice));
				return;
			}

			Invoice invoiceTarget = invoiceMapper.getInvoiceInfoByCustomCondition(invoice);

			if(!Objects.nonNull(invoiceTarget)){
				logger.warn("处理采购售后发票关联出库关系问题发票信息异常 invoice:{}", JSON.toJSONString(invoice));
				throw new RuntimeException("处理采购售后发票关联出库关系问题发票信息异常");
			}
			//invoiceApiService.saveRelatedIssueLogId(invoiceTarget.getInvoiceId());
		} catch (Exception e) {
			logger.error("处理采购售后发票关联出库关系问题error invoice:{}", JSON.toJSONString(invoice), e);
		}
	}

	/**
	 * 处理采购订单蓝字作废发票信息
	 * @param invoice
	 */
	private void dealBuyOrderBlueInvalidInvoice(Invoice invoice) {
		logger.info("处理采购订单蓝字作废发票信息 invoice:{}", JSON.toJSONString(invoice));
		if (!InvoiceTypeEnum.BUY_ORDER.getType().equals(invoice.getType()) ||
				!ErpConst.TWO.equals(invoice.getColorType()) ||
				!ErpConst.ZERO.equals(invoice.getIsEnable()) ||
				!ObjectUtils.notEmpty(invoice.getInvoiceId())) {
			return;
		}
		Invoice invoiceInfo = invoiceMapper.selectByPrimaryKey(invoice.getInvoiceId());
		logger.info("原发票基础信息 invoiceInfo:{}", JSON.toJSONString(invoiceInfo));

		List<Invoice> blueInvalidInvoiceByCondition = invoiceMapper.getBlueInvalidInvoiceByCondition(
				invoiceInfo.getInvoiceNo(), invoiceInfo.getInvoiceCode(), invoiceInfo.getRelatedId(), ErpConst.TWO);
		if (CollectionUtils.isEmpty(blueInvalidInvoiceByCondition)) {
			return;
		}
		invoice.setCompanyId(ErpConst.SIX);
	}

	/**
	 * 处理采购订单收票状态信息
	 *
	 * @param invoice
	 */
	private void dealBuyorderInvoiceStatus(Invoice invoice) {
		logger.info("售后退货处理采购订单收票状态信息 start invoiceNo:{}", invoice.getInvoiceNo());
		AfterSales afterSales = invoiceMapper.getAfterSalesByAfterInvoiceId(invoice.getAfterId());
		if (afterSales == null || afterSales.getSubjectType() != 536){
			logger.info("售后退货的非采购订单 invoiceNo:{}", invoice.getInvoiceNo());
			return;
		}
		List<Integer> buyorderIds = invoiceMapper.getInvoiceGoodsNum(invoice).stream()
				.map(InvoiceDetail::getRelatedId).distinct().collect(Collectors.toList());
		logger.info("采购订单退票关联的采购订单ID信息 buyorderIds:{}", JSON.toJSONString(buyorderIds));
		if (CollectionUtils.isEmpty(buyorderIds)){
			return;
		}

		buyorderIds.forEach(this :: saveBuyorderInvoiceStatus);
	}

	@Override
	public AfterSalesGoodsVo getAfterOpenInvoiceInfoAt(AfterSalesGoodsVo afterSalesGoodsVo) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "finance/after/getafteropeninvoiceinfoat.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesGoodsVo,clientId,clientKey, TypeRef);
			if(result!=null && result.getCode()==0){
				JSONObject json = JSONObject.fromObject(result.getData());
				AfterSalesGoodsVo afterGoodsVo = JsonUtils.readValue(json.toString(), AfterSalesGoodsVo.class);
				return afterGoodsVo;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return null;
	}

	/**
	 * 保存财务售后安调开票信息
	 */
	@Override
	public ResultInfo<?> saveAfterOpenInvoiceAt(Invoice invoice) {
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "finance/after/saveafteropeninvoiceat.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, invoice,clientId,clientKey, TypeRef);
			if(result!=null && result.getCode()==0){
				return result;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
		return new ResultInfo<>();
	}

	/**
	 * 获取售后退票开具发票信息
	 */
	@Override
	public Invoice getAfterOpenInvoiceInfoTp(Invoice invoice) {
		// 定义反序列化 数据格式
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "finance/after/getafteropeninvoiceinfotp.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, invoice,clientId,clientKey, TypeRef);
			if(result!=null && result.getCode()==0){
				JSONObject json = JSONObject.fromObject(result.getData());
				Invoice invoiceResult = JsonUtils.readValue(json.toString(), Invoice.class);
				return invoiceResult;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return null;
		}
		return null;
	}

	@Override
	public ResultInfo<?> saveAfterOpenInvoiceTp(Invoice invoice) {
		final TypeReference<ResultInfo<AfterSalesDetailVo>> TypeRef = new TypeReference<ResultInfo<AfterSalesDetailVo>>() {};
		String url=httpUrl + "finance/invoice/saveopeninvoice.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, invoice,clientId,clientKey, TypeRef);
			if(result!=null){
				if(result.getCode() == 0 && result.getData() != null){
					AfterSalesDetailVo asdv = (AfterSalesDetailVo)result.getData();
					if(asdv.getTraderId() != null){
						//根据客户Id查询客户负责人
						List<Integer> userIdList = userMapper.getUserIdListByTraderId(asdv.getTraderId(),ErpConst.ONE);
						Map<String,String> map = new HashMap<>();
						map.put("saleorderNo", asdv.getAfterSalesNo());
						//售後開票后，發送消息給客戶負責人，單號為售後單號
						MessageUtil.sendMessage(11, userIdList, map, "./order/saleorder/viewAfterSalesDetail.do?afterSalesId="+asdv.getAfterSalesId());
					}
				}
				return result;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
		return new ResultInfo<>();
	}

	/**
	 * 编辑售后发票记录信息
	 */
	@Override
	public ResultInfo<?> editAfterInvoice(AfterSalesInvoice afterSalesInvoice) {
		final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
		String url=httpUrl + "finance/after/editafterinvoice.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSalesInvoice, clientId, clientKey, TypeRef);
			if(result!=null && result.getCode()==0){
				return result;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
		return new ResultInfo<>();
	}

	@Override
	public ResultInfo<?> afterThRefundAmountBalance(AfterSales afterSales) {
		final TypeReference<ResultInfo<AfterSalesDetailVo>> TypeRef = new TypeReference<ResultInfo<AfterSalesDetailVo>>() {};
		String url=httpUrl + "finance/after/afterthrefundamountbalance.htm";
		try {
			ResultInfo<?> result = (ResultInfo<?>)HttpClientUtils.post(url, afterSales, clientId, clientKey, TypeRef);
			if(result!=null){
				if(result.getCode() == 0 && result.getData() != null){
					//审核通过--进行中
					if(afterSales.getStatus() == 2 && afterSales.getAtferSalesStatus() == 1){
						//销售--退货
						if(afterSales.getSubjectType() != null && afterSales.getSubjectType()==535 && afterSales.getType() == 539){
							//发送消息--售后销售订单退货-退款审核通过（钱退到账户余额）
							AfterSalesDetailVo new_afterSales = (AfterSalesDetailVo)result.getData();
							if(new_afterSales != null && new_afterSales.getTraderId() != null){
								//根据客户Id查询客户负责人
								List<Integer> userIdList = userMapper.getUserIdListByTraderId(new_afterSales.getTraderId(),ErpConst.ONE);
								Map<String,String> map = new HashMap<>();
								map.put("afterorderNo", new_afterSales.getAfterSalesNo());
								MessageUtil.sendMessage(40, userIdList, map, "./order/saleorder/viewAfterSalesDetail.do?afterSalesId="+new_afterSales.getAfterSalesId());//
							}
						}
					}
				}
				return result;
			}
		}catch (Exception e) {
			logger.error(Contant.ERROR_MSG, e);
			return new ResultInfo<>();
		}
		return new ResultInfo<>();
	}

}
