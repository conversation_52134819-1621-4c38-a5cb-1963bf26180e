package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.CapitalBillSettlementTypeEntity;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2024/1/8 13:50
 **/
public interface CapitalBillSettlementTypeMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(CapitalBillSettlementTypeEntity record);

    int insertSelective(CapitalBillSettlementTypeEntity record);

    CapitalBillSettlementTypeEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(CapitalBillSettlementTypeEntity record);

    int updateByPrimaryKey(CapitalBillSettlementTypeEntity record);
}