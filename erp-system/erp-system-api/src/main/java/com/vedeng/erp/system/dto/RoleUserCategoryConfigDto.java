package com.vedeng.erp.system.dto;

import lombok.Data;

import java.util.List;

/**
 * 人员与商品分类配置DTO
 */
@Data
public class RoleUserCategoryConfigDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 人员用户ID
     */
    private Integer userId;

    /**
     * 人员用户IDS
     */
    private List<Integer> userIds;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 商品分类IDS 逗号分隔
     */
    private String categoryIds;
} 