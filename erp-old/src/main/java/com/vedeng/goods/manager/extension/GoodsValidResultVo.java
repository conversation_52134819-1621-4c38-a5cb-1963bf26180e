package com.vedeng.goods.manager.extension;


import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsValidResultVo {

    private Boolean hasFailure = false;
    private Integer failCount = 0;
    private List<Item> failItems = new LinkedList<>();
    private List<Item> successItems = new LinkedList<>();

    public void addItemSuccessfully(Integer serviceId, String serviceName) {
        successItems.add(new Item(serviceId, serviceName, true));
    }

    public void addItemFailed(Integer serviceId, String serviceName, String message) {
        if (!hasFailure) {
            hasFailure = true;
        }
        failItems.add(new Item(serviceId, serviceName, false, message));
        failCount++;
    }

    public boolean isHasFailure() {
        return hasFailure;
    }

    public Integer getFailCount() {
        return failCount;
    }

    public List<Item> getFailItems() {
        return failItems;
    }

    public List<Item> getSuccessItems() {
        return successItems;
    }

    public static class Item {
        private Integer serviceId;
        private String serviceName;
        private Boolean success;
        private String errorMessage;

        public Item(Integer serviceId, String serviceName, Boolean success) {
            this(serviceId, serviceName, success, null);
        }

        Item(Integer serviceId, String serviceName, Boolean success, String errorMessage) {
            this.serviceId = serviceId;
            this.serviceName = serviceName;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        public Integer getServiceId() {
            return serviceId;
        }

        public void setServiceId(Integer serviceId) {
            this.serviceId = serviceId;
        }

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public Boolean getSuccess() {
            return success;
        }

        public void setSuccess(Boolean success) {
            this.success = success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
}
