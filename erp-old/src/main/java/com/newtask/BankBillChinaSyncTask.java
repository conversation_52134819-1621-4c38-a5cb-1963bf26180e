package com.newtask;

import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.BankBill;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@JobHandler(value = "BankBillChinaSyncTask")
public class BankBillChinaSyncTask extends AbstractJobHandler {

    private static final Logger LOGGER  = LoggerFactory.getLogger(BankBillDataSyncTask.class);
    @Value("${http_url}")
    protected String httpUrl;

    @Value("${client_id}")
    protected String clientId;

    @Value("${client_key}")
    protected String clientKey;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("start ----------------");
        BankBill bankBill=new BankBill();
        bankBill.setBankTag(3);
        bankBill.setCompanyId(1);
        String  searchEndTime = DateUtil.convertString(DateUtil.sysTimeMillis(), DateUtil.DATE_FORMAT);
        bankBill.setSearchEndtime(searchEndTime);
        bankBill.setSearchBegintime(searchEndTime);
        bankBill.setSyncBankFlag(1);
        Map<String, Object> map = new HashMap<>();
        // 调用银行流水列表
        String url = httpUrl + "finance/bankbill/getbankbillpaymatchlistpage.htm";
        final TypeReference<ResultInfo<List<BankBill>>> TypeRef = new TypeReference<ResultInfo<List<BankBill>>>() {
        };
        try {
            // 获取匹配过后的银行流水列表
            HttpClientUtils.postTimeOut5Min(url, bankBill, clientId, clientKey, TypeRef);
        }catch (Exception e){
            XxlJobLogger.log(e);
        }
        XxlJobLogger.log("end ----------------");
        return ReturnT.SUCCESS;
    }

}
