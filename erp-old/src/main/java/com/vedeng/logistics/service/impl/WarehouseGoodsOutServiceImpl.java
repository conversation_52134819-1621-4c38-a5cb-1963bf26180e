package com.vedeng.logistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSaleBuyorderDirectOutLog;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.logistics.dao.RExpressWarehouseGoodsOutInMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInMapper;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.eums.WarehouseOutInSourceEnum;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOutIn;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.bo.SaleAndBuyOrderGoodsBO;
import com.vedeng.logistics.model.dto.RExpressWarehouseGoodsOutInDto;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.logistics.service.WarehouseGoodsOutService;
import com.vedeng.logistics.utils.SnUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.wms.dao.WmsInOutPersonMapper;
import com.wms.dao.WmsOutOrderMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dto.WmsInOutPersonDto;
import com.wms.dto.WmsOutOrderDto;
import com.wms.model.po.WmsOutputOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2022-11-14
 * @Description: TODO
 * @Version: 1.0
 */
@Service
@Slf4j
public class WarehouseGoodsOutServiceImpl implements WarehouseGoodsOutService {

    private static final int AT_LENGTH = 16;

    private static final double SCALE = 0.6;

    private static final String RENDER_URL = "/api/render";
    @Autowired
    private WarehouseGoodsOutInMapper warehouseGoodsOutInMapper;
    @Qualifier("afterSalesMapper")
    @Autowired
    private AfterSalesMapper afterSalesMapper;
    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

    @Autowired
    private GoodsMapper goodsMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    protected UserMapper userMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Value("${oss_http}")
    private String ossHttp;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    //出库单验收报告pdf中图片地址
    @Value("${warehouseOutReport.imgpath}")
    private String imgPath;

    @Autowired
    private RExpressWarehouseGoodsOutInMapper rExpressWarehouseGoodsOutInMapper;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Autowired
    private WmsOutOrderMapper wmsOutOrderMapper;

    @Autowired
    private WmsInOutPersonMapper wmsInOutPersonMapper;

    /**
     * 客户类型-经销商
     */
    private final Integer TRADER_TYPE_CUSTOMER = 1;

    @Override
    public WarehouseGoodsOutIn insertWarehouseGoodsOutIN(WarehouseGoodsOutIn warehouseGoodsOutIn) {
        //系统自动生成erp出库单号
        String orderNo = warehouseGoodsOutIn.getRelateNo();
        String wmsOrderNo = warehouseGoodsOutIn.getWmsNo();
        Integer outInType = warehouseGoodsOutIn.getOutInType();
        String outInCompany = "";
        //采购类型，查询收发货方
        if (WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType) || WarehouseGoodsOutEnum.PURCHASE_RETURN_WAREHOUSE_OUT.getErpCode().equals(outInType)) {
            outInCompany = warehouseGoodsOutInMapper.getBuyTraderNameByAftersale(orderNo);
        }
        //销售类型，查询收发货方
        if(StockOperateTypeConst.WAREHOUSE_OUT.equals(outInType)){
            outInCompany=warehouseGoodsOutInMapper.getSaleTraderNameBySaleOrder(orderNo);
        }
        //销售换货出库类型，查询收发货方
        if(StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_OUT.equals(outInType)){
            outInCompany=warehouseGoodsOutInMapper.getSaleTraderNameByAftersale(orderNo);
        }
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_CK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
        warehouseGoodsOutIn.setOutInNo(outInNo);
        warehouseGoodsOutIn.setWmsNo(wmsOrderNo);
        warehouseGoodsOutIn.setRelateNo(orderNo);
        warehouseGoodsOutIn.setOutInCompany(StringUtils.isBlank(outInCompany)?"":outInCompany);
        Date now = new Date();
        warehouseGoodsOutIn.setOutInTime(now);
        warehouseGoodsOutIn.setCreator(2);
        warehouseGoodsOutIn.setUpdater(2);
        //保存出入库日志主表
        log.info("开始保存出入库日志表outInNo:{},wmsOrderNo:{},relateNo:{}", outInNo, wmsOrderNo, orderNo);
        return saveDb(warehouseGoodsOutIn, () -> warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn));
    }

    /**
     * 插入T_WAREHOUSE_GOODS_OUT_IN和直发产品出入库日志
     *
     * @param user
     * @param afterSaleBuyorderDirectOutLog
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDirectOutInLog(User user, List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLog) {
        if (CollectionUtils.isEmpty(afterSaleBuyorderDirectOutLog)) {
            return;
        }
        log.info("开始生成售后出库单，afterSaleBuyorderDirectOutLog:{}",JSON.toJSONString(afterSaleBuyorderDirectOutLog));
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList=new ArrayList<>();
        //获取售后id和售后类型
        AfterSaleBuyorderDirectOutLog outlog = afterSaleBuyorderDirectOutLog.get(0);
        //通过afterSalesId获取该售后商品的所有sn码
        Integer afterSalesId = afterSaleBuyorderDirectOutLog.get(0).getAfterSalesId();
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        //组装主表数据
        WarehouseGoodsOutIn warehouseGoodsOutIn =convertToWarehouseGoodsOutIn(afterSales,outlog,user);
        log.info("开始保存售后出库单，warehouseGoodsOutIn:{}",JSON.toJSONString(warehouseGoodsOutIn));
        warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn);
        //获取该售后单关联的采购单编码
        String orderNo = afterSales.getOrderNo();

        for (AfterSaleBuyorderDirectOutLog saleBuyorderDirectOutLog : afterSaleBuyorderDirectOutLog) {
            CoreSku coreSku  =coreSkuMapper.selectByPrimaryKey(saleBuyorderDirectOutLog.getGoodsId());
            //直发产品出入库日志T_WAREHOUSE_GOODS_OPERATE_LOG_DIRECT
            //若商品“是否厂家赋SN码”为是，则针对每一个商品都生成系统编码(取自采购入库)；若为“否”则为空
            if (Objects.nonNull(coreSku) &&Boolean.TRUE.equals(coreSku.getIsFactorySnCode())){
                //查询采购入库该商品的sn码列表
                List<String> orderNos = Arrays.asList(orderNo);
                List<String> snList=warehouseGoodsOutInMapper.getBarcodFactoryListByOrderNo(orderNos,saleBuyorderDirectOutLog.getGoodsId());
                //查询售后已使用的全部sn码
                List<String> existSnList=warehouseGoodsOutInMapper.getExistBarcodFactoryList(orderNo,saleBuyorderDirectOutLog.getGoodsId());
                List<String> remainList = SnUtil.duplicateRemovalList(snList, existSnList);
                Integer num = saleBuyorderDirectOutLog.getNum();
                for (int i=0;i<num;i++){
                    //生成SN码
                    String sn = CollectionUtils.isNotEmpty(remainList)&&remainList.size()>=i?remainList.get(i):"";
                    WarehouseGoodsOutInItem item=new WarehouseGoodsOutInItem();
                    item.setBarcodeFactory(sn);
                    item.setNum(new BigDecimal(Constants.ONE));
                    item.setBatchNumber("");
                    convertTowarehouseGoodsOutInItem(item,saleBuyorderDirectOutLog,warehouseGoodsOutIn,user);
                    log.info("开始保存售后出库单从表，warehouseGoodsOutInItem:{}",JSON.toJSONString(item));
                    warehouseGoodsOutInItemMapper.insertSelective(item);
                    warehouseGoodsOutInItemList.add(item);
                }
            }else{
                //查询采购入库该商品的批次码列表
                List<String> batchList=warehouseGoodsOutInMapper.getBatchNumberListByOrderNo(orderNo,saleBuyorderDirectOutLog.getGoodsId());
                WarehouseGoodsOutInItem logDirect=new WarehouseGoodsOutInItem();
                logDirect.setNum(new BigDecimal(saleBuyorderDirectOutLog.getNum()));
                logDirect.setBatchNumber("");
                if (CollectionUtils.isNotEmpty(batchList)){
                    //打乱批次列表顺序，随机取厂商批号
                    Collections.shuffle(batchList);
                    logDirect.setBatchNumber(batchList.get(0));
                }
                logDirect.setBarcodeFactory("");
                convertTowarehouseGoodsOutInItem(logDirect,saleBuyorderDirectOutLog,warehouseGoodsOutIn,user);
                log.info("开始保存售后出库单从表，warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
                warehouseGoodsOutInItemMapper.insertSelective(logDirect);
                warehouseGoodsOutInItemList.add(logDirect);
            }
        }
        try {
            //生成出库复核单
            createWarehouseGoodsOutReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
        }catch (Exception e){
            log.error("生成出库复核单失败",e);
        }
    }

    @Override
    public WarehouseGoodsOutInItem insertWarehouseGoodsOutInItem(WarehouseGoodsOperateLog inlog, WarehouseGoodsOutIn warehouseGoodsOutIn) {
        WarehouseGoodsOutInItem warehouseGoodsOutInItem=new WarehouseGoodsOutInItem();
        warehouseGoodsOutInItem.setOutInNo(warehouseGoodsOutIn.getOutInNo());
        warehouseGoodsOutInItem.setWmsNo(warehouseGoodsOutIn.getWmsNo());
        convertToWarehouseGoodsOutInItem(inlog,warehouseGoodsOutInItem);
        warehouseGoodsOutInItemMapper.insertSelective(warehouseGoodsOutInItem);
        return warehouseGoodsOutInItem;
    }

    private void convertToWarehouseGoodsOutInItem(WarehouseGoodsOperateLog inlog, WarehouseGoodsOutInItem warehouseGoodsOutInItem) {
        warehouseGoodsOutInItem.setBarcodeId(inlog.getBarcodeId());
        warehouseGoodsOutInItem.setCompanyId(inlog.getCompanyId());
        warehouseGoodsOutInItem.setLogType(inlog.getLogType());
        warehouseGoodsOutInItem.setOperateType(inlog.getOperateType());
        warehouseGoodsOutInItem.setRelatedId(inlog.getRelatedId());
        warehouseGoodsOutInItem.setWarehousePickingDetailId(inlog.getWarehousePickingDetailId());
        warehouseGoodsOutInItem.setGoodsId(inlog.getGoodsId());
        warehouseGoodsOutInItem.setBarcodeFactory(inlog.getBarcodeFactory());
        warehouseGoodsOutInItem.setNum(new BigDecimal(inlog.getNum()));
        warehouseGoodsOutInItem.setWarehouseId(inlog.getWarehouseId());
        warehouseGoodsOutInItem.setStorageRoomId(inlog.getStorageRoomId());
        warehouseGoodsOutInItem.setStorageAreaId(inlog.getStorageAreaId());
        warehouseGoodsOutInItem.setStorageLocationId(inlog.getStorageLocationId());
        warehouseGoodsOutInItem.setStorageRackId(inlog.getStorageRackId());
        warehouseGoodsOutInItem.setBatchNumber(inlog.getBatchNumber());
        warehouseGoodsOutInItem.setExpirationDate(inlog.getExpirationDate()!=null?DateUtil.convertString(inlog.getExpirationDate(),""):"");
        warehouseGoodsOutInItem.setCheckStatus(inlog.getCheckStatus());
        warehouseGoodsOutInItem.setCheckStatusUser(inlog.getCheckStatusUser());
        warehouseGoodsOutInItem.setCheckStatusTime(inlog.getCheckStatusTime()!=null?DateUtil.convertString(inlog.getCheckStatusTime(),""):"");
        warehouseGoodsOutInItem.setRecheckStatus(inlog.getRecheckStatus());
        warehouseGoodsOutInItem.setRecheckStatusUser(inlog.getRecheckStatusUser());
        warehouseGoodsOutInItem.setRecheckStatusTime(inlog.getRecheckStatusTime()!=null?DateUtil.convertString(inlog.getRecheckStatusTime(),""):"");
        warehouseGoodsOutInItem.setIsExpress(inlog.getIsExpress());
        warehouseGoodsOutInItem.setProductDate(inlog.getProductDate()!=null?DateUtil.convertString(inlog.getProductDate(),""):"");
        warehouseGoodsOutInItem.setCostPrice(inlog.getCostPrice());
        warehouseGoodsOutInItem.setIsUse(inlog.getIsUse());
        warehouseGoodsOutInItem.setLogicalWarehouseId(inlog.getLogicalWarehouseId());
        warehouseGoodsOutInItem.setVedengBatchNumber(inlog.getVedengBatchNumer());
        warehouseGoodsOutInItem.setLastStockNum(inlog.getLastStockNum());
        warehouseGoodsOutInItem.setSterilizationBatchNumber(inlog.getSterilizationBatchNo());
        warehouseGoodsOutInItem.setNewCostPrice(inlog.getNewCostPrice());
        warehouseGoodsOutInItem.setDedicatedBuyorderNo(inlog.getDedicatedBuyorderNo());
    }

    private WarehouseGoodsOutInItem convertTowarehouseGoodsOutInItem(WarehouseGoodsOutInItem logDirect,AfterSaleBuyorderDirectOutLog saleBuyorderDirectOutLog,WarehouseGoodsOutIn warehouseGoodsOutIn, User user) {
        String outInNo = warehouseGoodsOutIn.getOutInNo();
        logDirect.setOutInNo(outInNo);
        logDirect.setCompanyId(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getCompanyId());
        logDirect.setOperateType(warehouseGoodsOutIn.getOutInType());
        logDirect.setRelatedId(saleBuyorderDirectOutLog.getAfterSalesGoodsId());
        logDirect.setGoodsId(saleBuyorderDirectOutLog.getGoodsId());
        logDirect.setCheckStatusTime(DateUtil.convertString(System.currentTimeMillis(),""));
        logDirect.setExpirationDate(saleBuyorderDirectOutLog.getValidTime()!=null?DateUtil.convertString(saleBuyorderDirectOutLog.getValidTime(),""):"");
        logDirect.setProductDate(saleBuyorderDirectOutLog.getProduceTime()!=null?DateUtil.convertString(saleBuyorderDirectOutLog.getProduceTime(),""):"");
        logDirect.setSterilizationBatchNumber(saleBuyorderDirectOutLog.getSterilizationNumber());
        logDirect.setRecheckStatus(Constants.ONE);
        logDirect.setRecheckStatusTime(saleBuyorderDirectOutLog.getOutTimeStr());
        Integer userId = user != null ? user.getUserId() : 2;
        logDirect.setCreator(userId);
        logDirect.setUpdater(userId);
        logDirect.setVedengBatchNumber(logDirect.getBatchNumber());
        logDirect.setLogType(1);
        return logDirect;
    }

    /**
     * 保存入库
     *
     * @param t
     * @param supplier
     * @param <T>
     * @return
     */
    private <T> T saveDb(T t, Supplier<Integer> supplier) {
        if (supplier.get() > 0) {
            log.info("保存出入库日志主表成功");
            return t;
        }
        log.info("保存出入库日志主表失败");
        return null;
    }

    private WarehouseGoodsOutIn convertToWarehouseGoodsOutIn(AfterSales afterSales,AfterSaleBuyorderDirectOutLog outlog,User user){
        WarehouseGoodsOutIn warehouseGoodsOutIn = new WarehouseGoodsOutIn();
        //生成erp出库单号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_CK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
        Integer afterSalesId = outlog.getAfterSalesId();
        warehouseGoodsOutIn.setRelateNo(afterSales.getAfterSalesNo());
        if (StockOperateTypeConst.AFTERBUYORDER_BACK_FINSH.equals(afterSales.getType())) {
            warehouseGoodsOutIn.setOutInType(StockOperateTypeConst.BUYORDER_WAREHOUSE_BACK_OUT);
        } else if (StockOperateTypeConst.AFTERBUYORDER_CHANGE_FINSH.equals(afterSales.getType())) {
            warehouseGoodsOutIn.setOutInType(StockOperateTypeConst.BUYORDER_WAREHOUSE_CHANGE_OUT);
        }
        //采购类型，查询收发货方T_WAREHOUSE_GOODS_OUT_IN
        String outInCompany = warehouseGoodsOutInMapper.getBuyTraderNameByAftersale(warehouseGoodsOutIn.getRelateNo());
        warehouseGoodsOutIn.setOutInNo(outInNo);
        warehouseGoodsOutIn.setOutInCompany(outInCompany);
        Date now = new Date();
        warehouseGoodsOutIn.setOutInTime(now);
        warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
        Integer userId = user != null ? user.getUserId() : 2;
        String userName = user != null ? user.getUsername() : "";
        warehouseGoodsOutIn.setCreator(userId);
        warehouseGoodsOutIn.setUpdater(userId);
        warehouseGoodsOutIn.setCreatorName(userName);
        warehouseGoodsOutIn.setUpdaterName(userName);
        return warehouseGoodsOutIn;
    }


    @Override
    public void regenerateWarehouseOutReport(Long id) {
        WarehouseGoodsOutIn warehouseGoodsOutIn = warehouseGoodsOutInMapper.selectByPrimaryKey(id);
        if (Objects.isNull(warehouseGoodsOutIn)) {
            log.info("regenerateWarehouseOutReport：入库单未查询到 warehouseGoodsOutInId:{}",id);
            return;
        }
        WarehouseGoodsOutInItem warehouseGoodsOutInItem = new WarehouseGoodsOutInItem();
        warehouseGoodsOutInItem.setOutInNo(warehouseGoodsOutIn.getOutInNo());
        warehouseGoodsOutInItem.setIsDelete(0);
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = warehouseGoodsOutInItemMapper.findByAll(warehouseGoodsOutInItem);

        //新增验收报告附件
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ErpConst.WAREHOUSER_ATTACHMET_TYPE);
        attachment.setAttachmentFunction(ErpConst.WAREHOUSE_ATTACHMET_OUT_FUNCTION);
        attachment.setRelatedId(Integer.parseInt(String.valueOf(warehouseGoodsOutIn.getWarehouseGoodsOutInId())));
        log.info("regenerateWarehouseOutReport 删除历史复核单：{}",JSON.toJSONString(attachment));
        attachmentMapper.delAttachment(attachment);

        createWarehouseGoodsOutReport(warehouseGoodsOutIn, warehouseGoodsOutInItemList);
    }

    /**
     * 判断是否满足销售出库安调拆行条件
     * @param realateId
     * @param operateType
     * @param inlog
     * @return
     */
    @Override
    public Boolean haveInstallationBreakLine(int realateId, int operateType, WarehouseGoodsOperateLog inlog) {
        Boolean haveInstallationBreakLine = Boolean.FALSE;
        int num = Math.abs(inlog.getNum());
        //满足销售出库类型且商品数量>1时，在进一步做拆行判断
        if (StockOperateTypeConst.WAREHOUSE_OUT.equals(operateType) && num > 1) {
            log.info("开始销售出库安调拆行判断realateId{}", realateId);
            SaleorderGoods saleorderGoods = new SaleorderGoods();
            saleorderGoods.setSaleorderGoodsId(realateId);
            try {
                //根据saleorderGoodsId查询需要赋sn码拆行的数据
                SaleorderGoods goods = saleorderGoodsMapper.queryHaveInstallationAndSn(saleorderGoods);
                if (Objects.nonNull(goods)) {
                    haveInstallationBreakLine = Boolean.TRUE;
                }
            } catch (Exception e) {
                log.error("销售出库安调拆行查询判断异常", e);
            }
        }
        return haveInstallationBreakLine;
    }

    @Override
    public Boolean haveInstallationExchangeBreakLine(int realateId, int operateType, WarehouseGoodsOperateLog inlog) {
        Boolean haveInstallationExchangeBreakLine = Boolean.FALSE;
        int num = Math.abs(inlog.getNum());

        if (StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_OUT.equals(operateType) && num > 1) {
            log.info("开始销售换货出库安调拆行判断realateId{}", realateId);

            AfterSalesGoods afterSalesGoods = new AfterSalesGoods();

            afterSalesGoods.setAfterSalesGoodsId(realateId);
            try {
                //根据afterSaleorderGoodsId查询需要赋sn码拆行的数据

                AfterSalesGoods goods = afterSalesGoodsMapper.queryHaveInstallationAndSn(afterSalesGoods);

                if (Objects.nonNull(goods)) {
                    haveInstallationExchangeBreakLine = Boolean.TRUE;
                }
            } catch (Exception e) {
                log.error("销售换货出库安调拆行查询判断异常", e);
            }
        }

        if ((StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_IN.equals(operateType)||StockOperateTypeConst.ORDER_WAREHOUSE_BACK_IN.equals(operateType)) && num > 1) {
            log.info("开始销售换货入库或销售退货入库安调拆行判断realateId{}", realateId);

            AfterSalesGoods afterSalesGoods = new AfterSalesGoods();

            afterSalesGoods.setAfterSalesGoodsId(realateId);
            try {
                //根据afterSaleorderGoodsId查询需要赋sn码拆行的数据

                AfterSalesGoods goods = afterSalesGoodsMapper.queryHaveInstallationAndSn(afterSalesGoods);

                if (Objects.nonNull(goods)) {
                    haveInstallationExchangeBreakLine = Boolean.TRUE;
                }
            } catch (Exception e) {
                log.error("销售换货入库或销售退货入库安调拆行查询判断异常", e);
            }
        }
        if ((StockOperateTypeConst.WAREHOUSE_IN.equals(operateType)) && num > 1) {
            log.info("开始采购入库安调拆行判断realateId{}", realateId);

            try {
                List<Integer> buyorderGoodsIdList = buyorderGoodsMapper.queryHaveInstallationAndSn(realateId);

                if (CollectionUtils.isNotEmpty(buyorderGoodsIdList)) {
                    haveInstallationExchangeBreakLine = Boolean.TRUE;
                }
            } catch (Exception e) {
                log.error("采购入库安调拆行查询判断异常", e);
            }

        }
        return haveInstallationExchangeBreakLine;
    }

    /**
     * 生成出库复核单
     * @param warehouse
     * @param warehouseGoodsOutInItemList
     */
    @Override
    public void createWarehouseGoodsOutReport(WarehouseGoodsOutIn warehouse, List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList) {
        log.info("出库单主信息：{}，出库单明细信息：{}",warehouse, JSON.toJSONString(warehouseGoodsOutInItemList));
        //生产销售出库、销售换货出库、采购退货出库、采购换货出库的复核报告
        if(CollectionUtils.isEmpty(warehouseGoodsOutInItemList)) {
            return;
        }
        //拼接HTML文档
        StringBuilder sb = new StringBuilder();
        Integer outInType = warehouse.getOutInType();
        boolean isOne = WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
                WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
                WarehouseGoodsOutEnum.PURCHASE_RETURN_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
                WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
                WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getErpCode().equals(outInType);

        boolean isTwo = WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
                WarehouseGoodsOutEnum.INVENTORY_LOSS_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
                WarehouseGoodsOutEnum.UNIT_CONVERSION_OUT.getErpCode().equals(outInType) ;
        if (isOne) {
            templateOneOut(warehouse, warehouseGoodsOutInItemList, sb);

        } else if (isTwo) {
            templateTwoOut(warehouse, warehouseGoodsOutInItemList, sb);

        } else {
            templateOtherOut(warehouse, warehouseGoodsOutInItemList, sb);
        }

        try{
            htmlStrToPdfGetFile(sb.toString(),warehouse);
        }catch (Exception e){
            log.error("转换PDF失败...",e);
        }
    }

    private void getPickerAndReviewer(WarehouseGoodsOutIn warehouse, WmsOutOrderDto data) {
        if (StrUtil.isNotEmpty(warehouse.getWmsNo())) {
            List<WmsOutOrderDto> wmsOutOrderDtos = wmsOutOrderMapper.selectByWmsNo(warehouse.getWmsNo());
            if (CollUtil.isNotEmpty(wmsOutOrderDtos)) {
                data.setPicker(wmsOutOrderDtos.get(0).getPicker());
                data.setReviewer(wmsOutOrderDtos.get(0).getReviewer());
            }
        }
        if (StrUtil.isEmpty(data.getPicker())) {
            DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate("2023-11-28");
            DateTime outInDate = cn.hutool.core.date.DateUtil.parseDate(cn.hutool.core.date.DateUtil.formatDateTime(warehouse.getOutInTime()));
            String s = cn.hutool.core.date.DateUtil.formatDate(outInDate);
            int compare = cn.hutool.core.date.DateUtil.compare(dateTime, outInDate);
            if (compare<0) {

                List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByType(1003);
                if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
                    data.setPicker(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
                }
            } else {

                List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByTypeAndDate(3, s);
                if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
                    data.setPicker(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
                }


            }
        }

        if (StrUtil.isEmpty(data.getReviewer())) {
            DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate("2023-11-28");
            DateTime outInDate = cn.hutool.core.date.DateUtil.parseDate(cn.hutool.core.date.DateUtil.formatDateTime(warehouse.getOutInTime()));
            String s = cn.hutool.core.date.DateUtil.formatDate(outInDate);
            int compare = cn.hutool.core.date.DateUtil.compare(dateTime, outInDate);
            if (compare<0) {
                List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByType(1004);
                if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
                    data.setReviewer(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
                }
            } else {
                List<WmsInOutPersonDto> wmsInOutPersonDtos = wmsInOutPersonMapper.selectByTypeAndDate(4, s);
                if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
                    data.setReviewer(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
                }

            }

        }
    }

    private void templateOneOut(WarehouseGoodsOutIn warehouse, List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList, StringBuilder sb) {

        String preFix = "<html><head><title>出库复核报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>出库复核报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>单据类型：outInType</span><span style=\"margin-left:60px\">出库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:50px;height:30px\">序号</th><th rowspan=2 style=\"width:25%\">产品名称</th><th rowspan=2 style=\"width:80px\">订货号</th><th rowspan=2 style=\"width:100px\">型号/规格</th><th rowspan=2 style=\"width:60px\">单位</th><th rowspan=2 style=\"width:15%\">注册证编号</th><th rowspan=2 style=\"width:80px\">拣货数量</th><th rowspan=2 style=\"width:80px\">复核数量</th><th rowspan=2 style=\"width:80px\">复核结果</th><th colspan=6 style=\"width:80px;height:35px\">复核项目</th><th rowspan=2 style=\"width:80px\">出库状态</th><tr><th style=\"width:70px\">外观</th><th style=\"width:70px\">包装</th><th style=\"width:70px\">标签</th><th style=\"width:70px\">效期</th><th style=\"width:80px\">随货单据</th><th style=\"width:70px\">其他</th></tr></tr></thead><tbody id=\"effect\"style=\"text-align: center;\">";
        String companyName = "/";
        Integer outInType = warehouse.getOutInType();
        String serviceByErpCode = WarehouseGoodsOutEnum.getTypeByErpCode(outInType);
        if(WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
            //销售出库
            Saleorder saleorderByOrderNo = saleorderMapper.getSaleorderByOrderNo(warehouse.getRelateNo());
            companyName = StrUtil.isNotEmpty(saleorderByOrderNo.getTraderName())?saleorderByOrderNo.getTraderName():"/";
        }else if (WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
            //销售换货出库
            AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(warehouse.getRelateNo());
            Saleorder saleorderByOrderNo = saleorderMapper.getSaleorderByOrderNo(afterSalesByNo.getOrderNo());
            companyName = StrUtil.isNotEmpty(saleorderByOrderNo.getTraderName())?saleorderByOrderNo.getTraderName():"/";
        }else if (WarehouseGoodsOutEnum.PURCHASE_RETURN_WAREHOUSE_OUT.getErpCode().equals(outInType) || WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
            //采购换货、采购退货
            AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(warehouse.getRelateNo());
            Buyorder buyOrderByOrderNo = buyorderMapper.getBuyOrderByOrderNo(afterSalesByNo.getOrderNo());
            companyName = StrUtil.isNotEmpty(buyOrderByOrderNo.getTraderName())?buyOrderByOrderNo.getTraderName():"/";
        } else if (WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
            WmsOutputOrder outputOrder = outputOrderMapper.selectByOrderNo(warehouse.getRelateNo());
            if (Objects.nonNull(outputOrder)) {
                companyName = StrUtil.isNotEmpty(outputOrder.getBorrowTraderName())?outputOrder.getBorrowTraderName():"/";
            }
        }


        WmsOutOrderDto data = new WmsOutOrderDto();
        getPickerAndReviewer(warehouse, data);


        //替换：复核报告编号（outNo）、关联订单号(relateNo)、替换客户/供应商名称(outInCompany)
        sb.append(preFix.replace("outInNo", warehouse.getOutInNo())
                .replace("outInCompany",companyName)
                .replace("outInType",serviceByErpCode)
                .replace("relateNo", warehouse.getRelateNo()));

        //替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、拣货数量（outNum）、复核数量（checkNum）、复核结果（checkResult）
        //出库状态（goodsOutStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、效期（goodsPeriod）、随货单据（goodsDocuments）、其他（others）
        for(int i = 0; i < warehouseGoodsOutInItemList.size(); i++){
            WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
            //如果为采购入库
            OutInDetail skuInfo = new OutInDetail();
            Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
            querySkuInfo(skuInfo, skuId);
            String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>outNum</td><td>checkNum</td><td>checkResult</td><td>goodsFace</td><td>goodsPackage</td><td>goodsSign</td><td>goodsPeriod</td><td>goodsDocuments</td><td>others</td><td>goodsOutStatus</td></tr>";
            StringBuilder model = new StringBuilder();
            if (StrUtil.isNotEmpty(skuInfo.getModel())) {
                model.append(skuInfo.getModel());
            }
            if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
                model.append("/");
            }
            model.append(skuInfo.getSpec());
            sb.append(middleFix.replace("pageIndex",String.valueOf(i+1))
                    .replace("goodsName",skuInfo.getSkuName())
                    .replace("skuNo", skuInfo.getSkuNo())
                    .replace("goodsModel", model.toString())
                    .replace("unit", skuInfo.getUnitName())
                    .replace("registerNo", skuInfo.getRegistrationNumber())
                    .replace("outNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
                    .replace("checkNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
                    .replace("checkResult","合格")
                    .replace("goodsOutStatus","已出库")
                    .replace("goodsFace","合格")
                    .replace("goodsPackage","合格")
                    .replace("goodsSign","合格")
                    .replace("goodsPeriod","合格")
                    .replace("goodsDocuments","合格")
                    .replace("others","合格")
            );
        }

        String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>拣货员：sorter</span><span style=\"margin-left:240px\">复核员：checker</span><span style=\"margin-left:300px\">出库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">出库完成</span></div></div></div></body></html>";
        //替换：拣货员（sorter）、复核员（checker）、复核出库时间（checkTime）、复核图片
        sb.append(surFix.replace("sorter", StrUtil.isEmpty(data.getPicker())?"":data.getPicker())
                .replace("checker", StrUtil.isEmpty(data.getReviewer())?"":data.getReviewer())
                .replace("checkTime", DateUtil.DateToString(warehouse.getOutInTime(), DateUtil.DATE_FORMAT)));
        log.info("create html in param:{}", sb);
    }


    private void templateTwoOut(WarehouseGoodsOutIn warehouse, List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList, StringBuilder sb) {
        String preFix = "<html><head><title>出库报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>出库报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>单据类型：outInType</span><span style=\"margin-left:60px\">出库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:80px;height:30px\">序号</th><th rowspan=2 style=\"width:25%\">产品名称</th><th rowspan=2 style=\"width:140px\">订货号</th><th rowspan=2 style=\"width:140px\">型号/规格</th><th rowspan=2 style=\"width:140px\">单位</th><th rowspan=2 style=\"width:15%\">注册证编号</th><th rowspan=2 style=\"width:160px\">出库数量</th><th rowspan=2 style=\"width:120px\">出库状态</th></tr></thead><tbody id=\"effect\"style=\"text-align: center;\">";

        String companyName = "/";
        Integer outInType = warehouse.getOutInType();
        String serviceByErpCode = WarehouseGoodsOutEnum.getTypeByErpCode(outInType);
        if(WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getErpCode().equals(outInType)){
            // 样品出 外借
            WmsOutputOrder outputOrder = outputOrderMapper.selectByOrderNo(warehouse.getRelateNo());
            if (Objects.nonNull(outputOrder)) {
                companyName = StrUtil.isNotEmpty(outputOrder.getBorrowTraderName())?outputOrder.getBorrowTraderName():"/";
            }
        }
        WmsOutOrderDto data = new WmsOutOrderDto();
        getPickerAndReviewer(warehouse, data);

        //替换：复核报告编号（outNo）、关联订单号(relateNo)、替换客户/供应商名称(outInCompany)
        sb.append(preFix.replace("outInNo", warehouse.getOutInNo())
                .replace("outInCompany",companyName)
                .replace("outInType",serviceByErpCode)
                .replace("relateNo", warehouse.getRelateNo()));

        //替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、拣货数量（outNum）、复核数量（checkNum）、复核结果（checkResult）
        //出库状态（goodsOutStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、效期（goodsPeriod）、随货单据（goodsDocuments）、其他（others）
        for(int i = 0; i < warehouseGoodsOutInItemList.size(); i++){
            WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
            //如果为采购入库
            OutInDetail skuInfo = new OutInDetail();
            Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
            querySkuInfo(skuInfo, skuId);
            String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>outNum</td><td>goodsOutStatus</td></tr>";
            StringBuilder model = new StringBuilder();
            if (StrUtil.isNotEmpty(skuInfo.getModel())) {
                model.append(skuInfo.getModel());
            }
            if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
                model.append("/");
            }
            model.append(skuInfo.getSpec());
            sb.append(middleFix.replace("pageIndex", String.valueOf(i + 1))
                    .replace("goodsName", skuInfo.getSkuName())
                    .replace("skuNo", skuInfo.getSkuNo())
                    .replace("goodsModel", model.toString())
                    .replace("unit", skuInfo.getUnitName())
                    .replace("registerNo", skuInfo.getRegistrationNumber())
                    .replace("outNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
                    .replace("goodsOutStatus", "已出库")
                    );
        }

        String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>拣货员：sorter</span><span style=\"margin-left:600px\">出库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">出库完成</span></div></div></div></body></html>";
        //替换：拣货员（sorter）、复核员（checker）、复核出库时间（checkTime）、复核图片
        sb.append(surFix.replace("sorter", StrUtil.isEmpty(data.getPicker())?"":data.getPicker())
                .replace("checkTime", DateUtil.DateToString(warehouse.getOutInTime(), DateUtil.DATE_FORMAT))
        );
        log.info("create html in param:{}", sb);
    }


    private void templateOtherOut(WarehouseGoodsOutIn warehouse, List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList, StringBuilder sb) {
        String preFix = "<html><head><title>出库报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>出库报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >单据类型：outInType</span><span style=\"margin-left:60px\">出库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:80px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:140px\">订货号</th><th rowspan=2 style=\"width:140px\">型号/规格</th><th rowspan=2 style=\"width:140px\">单位</th><th rowspan=2 style=\"width:160px\">出库数量</th><th rowspan=2 style=\"width:190px\">质量状态</th><th rowspan=2 style=\"width:160px\">处置方式</th></tr></thead><tbody id=\"effect\"style=\"text-align: center;\">";
        String companyName = "/";
        Integer outInType = warehouse.getOutInType();
        String serviceByErpCode = WarehouseGoodsOutEnum.getTypeByErpCode(outInType);
        String disposalMethod = WarehouseGoodsOutEnum.SCRAP_WAREHOUSE_OUT.getErpCode().equals(outInType) ? "交由第三方销毁" : "内部使用";
        log.info("出库单关联单号：{},客户/供应商名称：{}", warehouse.getRelateNo(),companyName);

        //替换：复核报告编号（outNo）、关联订单号(relateNo)、替换客户/供应商名称(outInCompany)
        sb.append(preFix.replace("outInNo", warehouse.getOutInNo())
                .replace("outInCompany",companyName)
                .replace("outInType",serviceByErpCode)
                .replace("relateNo", warehouse.getRelateNo()));

        //替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、拣货数量（outNum）、复核数量（checkNum）、复核结果（checkResult）
        //出库状态（goodsOutStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、效期（goodsPeriod）、随货单据（goodsDocuments）、其他（others）
        for(int i = 0; i < warehouseGoodsOutInItemList.size(); i++){
            WarehouseGoodsOutInItem warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
            //如果为采购入库
            OutInDetail skuInfo = new OutInDetail();
            Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
            querySkuInfo(skuInfo, skuId);
            String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>outNum</td><td>qualityStatus</td><td>disposalMethod</td></tr></tbody>";
            StringBuilder model = new StringBuilder();
            if (StrUtil.isNotEmpty(skuInfo.getModel())) {
                model.append(skuInfo.getModel());
            }
            if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
                model.append("/");
            }
            model.append(skuInfo.getSpec());
            sb.append(middleFix.replace("pageIndex",String.valueOf(i+1))
                    .replace("goodsName",skuInfo.getSkuName())
                    .replace("skuNo", skuInfo.getSkuNo())
                    .replace("unit", skuInfo.getUnitName())
                    .replace("goodsModel", model.toString())
                    .replace("qualityStatus", "不合格")
                    .replace("outNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
                    .replace("disposalMethod",disposalMethod)
            );
        }

        WmsOutOrderDto data = new WmsOutOrderDto();
        getPickerAndReviewer(warehouse, data);

        String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >拣货员：sorter</span><span style=\"margin-left:240px\">出库员：checker</span><span style=\"margin-left:300px\">出库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">出库完成</span></div></div></div></body></html>";
        //替换：拣货员（sorter）、复核员（checker）、复核出库时间（checkTime）、复核图片
        sb.append(surFix.replace("sorter",StrUtil.isEmpty(data.getPicker())?"":data.getPicker())
                .replace("checker",StrUtil.isEmpty(data.getReviewer())?"":data.getReviewer())
                .replace("checkTime",DateUtil.DateToString(warehouse.getOutInTime(),DateUtil.DATE_FORMAT))
                );
        log.info("create html in param:{}", sb);
    }


    /**
     * 获取商品明细信息
     * @param skuInfo
     * @param skuId
     */
    @Override
    public void querySkuInfo(OutInDetail skuInfo, Integer skuId) {
        List<Integer> skuIdList = Arrays.asList(skuId);
        List<Map<String, Object>> maps = goodsMapper.skuTipList(skuIdList);
        if(CollectionUtils.isNotEmpty(maps)) {
            Map<String,Object> sku = maps.get(0);
            if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))) {
                skuInfo.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
            }
            skuInfo.setSkuId(Integer.valueOf(sku.get("SKU_ID").toString()));
            skuInfo.setSpuId(Integer.valueOf(sku.get("SPU_ID").toString()));
            skuInfo.setSkuNo(sku.get("SKU_NO").toString());
            skuInfo.setSkuName(sku.get("SHOW_NAME").toString());
            skuInfo.setBrandName(sku.get("BRAND_NAME").toString());
            if(Objects.nonNull(sku.get("SPEC"))){
                skuInfo.setSpec(sku.get("SPEC").toString());
            }
            skuInfo.setModel(sku.get("MODEL").toString());
            skuInfo.setRegistrationNumber(sku.get("REGISTRATION_NUMBER").toString());
            skuInfo.setUnitName(sku.get("UNIT_NAME").toString());
        }
    }

    /**
     * 直发销售出库记录
     * @param session_user
     * @param buyorder
     * @param id_sendN_sendedN_sumN
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertSaleorderDirectOut(User session_user, Buyorder buyorder, String id_sendN_sendedN_sumN, Express express) {
        //组装主表数据
        WarehouseGoodsOutIn warehouseGoodsOutIn=new WarehouseGoodsOutIn();
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList=new ArrayList<>();
        Map<Integer,Integer> getNumMap=new HashMap<>();
        if (StringUtils.isBlank(id_sendN_sendedN_sumN)) {
            log.info("id_sendN_sendedN_sumN为空,id_sendN_sendedN_sumN:{}",id_sendN_sendedN_sumN);
            return;
        }
        List<SaleAndBuyOrderGoodsBO> saleOrderList=new ArrayList<>();
        String[] idAndSendNumAllArray = id_sendN_sendedN_sumN.split("_");
        List<Integer> buyOrderGoodsIds=new ArrayList<>();
        for (String idAndSendNumArray : idAndSendNumAllArray) {
            if (StringUtils.isBlank(idAndSendNumArray)){
                break;
            }
            String[] idAndSendNum = idAndSendNumArray.split("\\|");
            Integer buyOrderGoodsId = Integer.parseInt(idAndSendNum[0]);
            Integer sendNum = Integer.parseInt(idAndSendNum[1]);
            buyOrderGoodsIds.add(buyOrderGoodsId);
            getNumMap.put(buyOrderGoodsId,sendNum);
        }
        //根据采购订单的产品id匹配关联销售单及销售产品id
        saleOrderList= buyorderMapper.getSaleAndBuyOrderGoodsByIds(buyOrderGoodsIds);
        if (CollectionUtils.isEmpty(saleOrderList)){
            log.info("出库单未匹配到关联销售单及销售产品saleOrderList:{}",JSON.toJSONString(saleOrderList));
            return;
        }
        //获取采购单下的销售单列表
        List<WarehouseGoodsOutInItem> itemList=new ArrayList<>();
        saleOrderList.forEach(x->{});
        //根据采购订单的产品id匹配关联销售单及销售产品id
        for (SaleAndBuyOrderGoodsBO bo : saleOrderList) {
            WarehouseGoodsOutInItem warehouseGoodsOutInItem=new WarehouseGoodsOutInItem();
            //如果匹配到该销售订单，则封装itemlist
            warehouseGoodsOutInItem.setGoodsId(bo.getGoodsId());
            warehouseGoodsOutInItem.setNum(new BigDecimal(getNumMap.get(bo.getBuyorderGoodsId())));
            warehouseGoodsOutInItem.setRelatedId(bo.getSaleorderGoodsId());
            itemList.add(warehouseGoodsOutInItem);
            warehouseGoodsOutIn.setRelateNo(bo.getSaleorderNo());
        }
        //出入库类型
        warehouseGoodsOutIn.setOutInType(StockOperateTypeConst.WAREHOUSE_OUT);
        //保存主表信息
        convertToWarehouseGoodsOutIn(warehouseGoodsOutIn,session_user,warehouseGoodsOutInMapper::getSaleTraderNameBySaleOrder);
        log.info("出库单主表数据插入warehouseGoodsOutIn:{}",JSON.toJSONString(warehouseGoodsOutIn));
        warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn);
        //保存item表信息
        for (WarehouseGoodsOutInItem item : itemList) {
            CoreSku coreSku  =coreSkuMapper.selectByPrimaryKey(item.getGoodsId());
            //若商品“是否厂家赋SN码”为是，则针对每一个商品都生成系统编码(取自采购入库)；若为“否”则为空
            if (Objects.nonNull(coreSku) &&Boolean.TRUE.equals(coreSku.getIsFactorySnCode())){
                //查询采购入库该商品的sn码列表
                List<String> orderNos = Arrays.asList(buyorder.getBuyorderNo());
                List<String> snList=warehouseGoodsOutInMapper.getBarcodFactoryListByOrderNo(orderNos,item.getGoodsId());
                //查询售后已使用的全部sn码
                List<String> existSnList=warehouseGoodsOutInMapper.getExistBarcodFactoryList(buyorder.getBuyorderNo(),item.getGoodsId());
                snList=snList.stream().filter(x->!existSnList.contains(x)).collect(Collectors.toList());
                Integer num = item.getNum().intValue();
                for (int i=0;i<num;i++){
                    //生成SN码
                    String sn = CollectionUtils.isNotEmpty(snList)&&snList.size()>=i?snList.get(i):"";
                    WarehouseGoodsOutInItem logDirect=new WarehouseGoodsOutInItem();
                    logDirect.setBarcodeFactory(sn);
                    logDirect.setNum(new BigDecimal(Constants.ONE));
                    logDirect.setBatchNumber("");
                    logDirect.setRelatedId(item.getRelatedId());
                    logDirect.setGoodsId(item.getGoodsId());
                    convertTowarehouseGoodsOutInItem(logDirect,warehouseGoodsOutIn,session_user);
                    warehouseGoodsOutInItemList.add(logDirect);
                    log.info("出库单从表数据插入warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
                    warehouseGoodsOutInItemMapper.insertSelective(logDirect);
                }
            }else{
                //查询采购入库该商品的批次码列表
                List<String> batchList=warehouseGoodsOutInMapper.getBatchNumberListByOrderNo(buyorder.getBuyorderNo(),item.getGoodsId());
                WarehouseGoodsOutInItem logDirect=new WarehouseGoodsOutInItem();
                logDirect.setNum(item.getNum());
                logDirect.setBatchNumber("");
                if (CollectionUtils.isNotEmpty(batchList)){
                    //打乱批次列表顺序，随机取厂商批号
                    Collections.shuffle(batchList);
                    logDirect.setBatchNumber(batchList.get(0));
                }
                logDirect.setBarcodeFactory("");
                logDirect.setRelatedId(item.getRelatedId());
                logDirect.setGoodsId(item.getGoodsId());
                convertTowarehouseGoodsOutInItem(logDirect,warehouseGoodsOutIn,session_user);
                warehouseGoodsOutInItemList.add(logDirect);
                log.info("出库单从表数据插入warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
                warehouseGoodsOutInItemMapper.insertSelective(logDirect);
            }
        }

        RExpressWarehouseGoodsOutInDto rExpressWarehouseGoodsOutInDto = new RExpressWarehouseGoodsOutInDto();
        rExpressWarehouseGoodsOutInDto.setWarehouseGoodsOutInId(warehouseGoodsOutIn.getWarehouseGoodsOutInId().intValue());
        rExpressWarehouseGoodsOutInDto.setExpressId(express.getExpressId());
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        rExpressWarehouseGoodsOutInDto.setCreator(currentUser.getId());
        rExpressWarehouseGoodsOutInDto.setCreatorName(currentUser.getUsername());
        rExpressWarehouseGoodsOutInDto.setUpdater(currentUser.getId());
        rExpressWarehouseGoodsOutInDto.setUpdaterName(currentUser.getUsername());
        rExpressWarehouseGoodsOutInMapper.insertSelective(rExpressWarehouseGoodsOutInDto);
        try {
            //生成出库复核单
            createWarehouseGoodsOutReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
        }catch (Exception e){
            log.error("生成出库复核单失败",e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAfterSalesDirectOutInLog(AfterSalesGoods afterSalesGoods, AfterSalesDirectInfo afterSalesDirectInfo, User user) {
        log.info("销售换货生成出库单，afterSalesDirectInfo:{}",JSON.toJSONString(afterSalesDirectInfo));
        //组装主表数据
        WarehouseGoodsOutIn warehouseGoodsOutIn=new WarehouseGoodsOutIn();
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList=new ArrayList<>();
        //通过afterSalesId获取该售后商品的所有sn码
        Integer afterSalesId = afterSalesDirectInfo.getAfterSalesId();
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        warehouseGoodsOutIn.setRelateNo(afterSales.getAfterSalesNo());
        //出入库类型
        warehouseGoodsOutIn.setOutInType(StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_OUT);
        //保存主表信息
        convertToWarehouseGoodsOutIn(warehouseGoodsOutIn,user,warehouseGoodsOutInMapper::getSaleTraderNameByAftersale);
        log.info("销售换货生成出库单主表保存，warehouseGoodsOutIn:{}",JSON.toJSONString(warehouseGoodsOutIn));
        warehouseGoodsOutInMapper.insertSelective(warehouseGoodsOutIn);
        //保存item表信息
        CoreSku coreSku  =coreSkuMapper.selectByPrimaryKey(afterSalesGoods.getGoodsId());

        //若商品“是否厂家赋SN码”为是，则针对每一个商品都生成系统编码(取自采购入库)；若为“否”则为空
        if (Objects.nonNull(coreSku) &&Boolean.TRUE.equals(coreSku.getIsFactorySnCode())){
            //获取页面填写sn码
            String factoryCode = afterSalesDirectInfo.getFactoryCode();
            //查询售后已使用的全部sn码
            Integer num = afterSalesDirectInfo.getNum();
            for (int i=0;i<num;i++){
                //生成SN码
                WarehouseGoodsOutInItem logDirect=new WarehouseGoodsOutInItem();
                logDirect.setBarcodeFactory(StringUtils.isNotEmpty(factoryCode)?factoryCode:"");
                logDirect.setNum(new BigDecimal(Constants.ONE));
                logDirect.setBatchNumber("");
                logDirect.setRelatedId(afterSalesDirectInfo.getAfterSalesGoodsId());
                logDirect.setGoodsId(afterSalesGoods.getGoodsId());
                convertTowarehouseGoodsOutInItem(logDirect,warehouseGoodsOutIn,user);
                warehouseGoodsOutInItemList.add(logDirect);
                log.info("销售换货生成出库单从表保存，warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
                warehouseGoodsOutInItemMapper.insertSelective(logDirect);
            }
        }else{
            //获取页面填写批次码
            String factoryCode = afterSalesDirectInfo.getFactoryCode();
            WarehouseGoodsOutInItem logDirect=new WarehouseGoodsOutInItem();
            logDirect.setNum(new BigDecimal(afterSalesDirectInfo.getNum()));
            logDirect.setBatchNumber(factoryCode);
            logDirect.setBarcodeFactory("");
            logDirect.setRelatedId(afterSalesDirectInfo.getAfterSalesGoodsId());
            logDirect.setGoodsId(afterSalesGoods.getGoodsId());
            convertTowarehouseGoodsOutInItem(logDirect,warehouseGoodsOutIn,user);
            warehouseGoodsOutInItemList.add(logDirect);
            log.info("销售换货生成出库单从表保存，warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
            warehouseGoodsOutInItemMapper.insertSelective(logDirect);
        }
        try {
            //生成出库复核单
            createWarehouseGoodsOutReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
        }catch (Exception e){
            log.error("生成出库复核单失败",e);
        }

    }

    @Override
    public boolean isExistsSnInWarehouseGoodsOut(String serialNumber, List<Integer> warehouseGoodsOutInType) {
        return warehouseGoodsOutInItemMapper.findByBarcodeFactoryAndOperateTypeIn(serialNumber,warehouseGoodsOutInType);
    }

    private void convertTowarehouseGoodsOutInItem(WarehouseGoodsOutInItem logDirect, WarehouseGoodsOutIn warehouseGoodsOutIn, User user) {
        String outInNo = warehouseGoodsOutIn.getOutInNo();
        logDirect.setOutInNo(outInNo);
        logDirect.setCompanyId(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getCompanyId());
        logDirect.setOperateType(warehouseGoodsOutIn.getOutInType());
        logDirect.setCheckStatusTime(DateUtil.convertString(System.currentTimeMillis(),""));
        logDirect.setRecheckStatus(Constants.ONE);
        Integer userId = user != null ? user.getUserId() : 2;
        logDirect.setCreator(userId);
        logDirect.setUpdater(userId);
        logDirect.setVedengBatchNumber(logDirect.getBatchNumber());
        logDirect.setLogType(1);
    }

    private void convertToWarehouseGoodsOutIn(WarehouseGoodsOutIn warehouseGoodsOutIn, User user, Function<String,String> companyFunction) {
        //生成erp出库单号
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_CK);
        String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
        // 销售类型，查询收发货方
        String outInCompany = companyFunction.apply(warehouseGoodsOutIn.getRelateNo());
        warehouseGoodsOutIn.setOutInNo(outInNo);
        warehouseGoodsOutIn.setOutInCompany(outInCompany);
        Date now = new Date();
        warehouseGoodsOutIn.setOutInTime(now);
        warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
        Integer userId = user != null ? user.getUserId() : 2;
        String userName = user != null ? user.getUsername() : "";
        warehouseGoodsOutIn.setCreator(userId);
        warehouseGoodsOutIn.setUpdater(userId);
        warehouseGoodsOutIn.setCreatorName(userName);
        warehouseGoodsOutIn.setUpdaterName(userName);
    }

    @Retryable(value = NullPointerException.class, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public void htmlStrToPdfGetFile(String html,WarehouseGoodsOutIn warehouseGoodsOutIn){
        String html2PdfUrl = html2PdfDomain + RENDER_URL;
        UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
        urlToPdfParam.setHtml(html);
        UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
        UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm" , "1cm" , "1cm" , "0cm");
        pdf.setMargin(margin);
        pdf.setScale(SCALE);
        urlToPdfParam.setPdf(pdf);
        // 上传出库单报告返回oss链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf","复核单" + warehouseGoodsOutIn.getOutInNo(), urlToPdfParam);
        if(StringUtil.isBlank(ossUrl)){
            log.info("上传oss返回Url为空，可能出现IO异常，准备重试");
            throw new NullPointerException("返回的ossUrl为空");
        }
        log.info("生成出库复核报告,出库单号：{},返回地址ossUrl:{}" , warehouseGoodsOutIn.getOutInNo(),ossUrl);

        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ErpConst.WAREHOUSER_ATTACHMET_TYPE);
        attachment.setAttachmentFunction(ErpConst.WAREHOUSE_ATTACHMET_OUT_FUNCTION);
        attachment.setRelatedId(Integer.parseInt(String.valueOf(warehouseGoodsOutIn.getWarehouseGoodsOutInId())));
        String domainAndUri = ossUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf('/');
        String domain = domainAndUri.substring(0, domainIndex);
        String uri = domainAndUri.substring(domainIndex);
        attachment.setDomain(domain);
        attachment.setUri(uri);
        attachment.setName("复核单");
        attachment.setSuffix("pdf");
        attachment.setAddTime(System.currentTimeMillis());
        attachmentMapper.insertSelective(attachment);
    }
}
