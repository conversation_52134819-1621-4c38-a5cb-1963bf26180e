package com.vedeng.trader.model;

public class TraderAccountInfo {
    private Integer ssoAccountId;
    private String traderName;
    private Integer source=1;
    /**
     * 三证合一 0：否 1：是
     */
    private Integer threeInOne=0;
    private Integer companyId=1;
    /**
     * 是否展示会员价，0不展示，1展示
     */
    private Integer isShowVipPrice=0;

    public Integer getIsShowVipPrice() {
        return isShowVipPrice;
    }

    public void setIsShowVipPrice(Integer isShowVipPrice) {
        this.isShowVipPrice = isShowVipPrice;
    }

    public Integer getSsoAccountId() {
        return ssoAccountId;
    }

    public void setSsoAccountId(Integer ssoAccountId) {
        this.ssoAccountId = ssoAccountId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Integer getThreeInOne() {
        return threeInOne;
    }

    public void setThreeInOne(Integer threeInOne) {
        this.threeInOne = threeInOne;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }
}
