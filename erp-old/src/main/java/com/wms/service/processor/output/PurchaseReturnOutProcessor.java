package com.wms.service.processor.output;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.processor.input.WmsLendInputProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * WMS采购退货出库单审核处理器
 */
@Service
public class PurchaseReturnOutProcessor extends AbstractOutputOrderProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmsLendInputProcessor.class);

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Autowired
    @Qualifier("purchaseReturnOutCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.OUT_PURCHASE_RETURN.equals(requestBean.getOrderType())){
            throw new Exception("入库单:"+requestBean.getOrderType()+"的类型错误!");
        }
    }

    @Override
    protected void updateOrderData(OutputDto outputDto) {

        String afterSaleOrderNo = WmsCommonUtil.getOriginalOrderNo(outputDto.getSOReference1());

        AfterSales afterSale = afterSalesMapper.getAfterSalesByNo(afterSaleOrderNo);

        List<AfterSalesGoodsVo> afterSaleGoodList =
                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSale.getAfterSalesId());

        ThreadLocalContext.put("afterSaleInfo",afterSale);
        ThreadLocalContext.put("afterSaleGoodList",afterSaleGoodList);

        List<OutputGoodDto> details = outputDto.getDetails();

        details.stream().forEach(outputGoodDto -> {

            AfterSalesGoodsVo afterSalesGoodsVo = getRelateAfterSaleGood(afterSaleGoodList,outputGoodDto.getSKU());

            AfterSalesGoods updateGood = new AfterSalesGoods();
            updateGood.setAfterSalesGoodsId(afterSalesGoodsVo.getAfterSalesGoodsId());
            updateGood.setDeliveryNum(afterSalesGoodsVo.getDeliveryNum() +  outputGoodDto.getQtyShipped().intValue());
            updateGood.setDeliveryTime(System.currentTimeMillis());

            //2-全部发货 1-部分发货
            updateGood.setDeliveryStatus(updateGood.getDeliveryNum() == afterSalesGoodsVo.getNum() ? 2 : 1);

            LOGGER.info("采购退货出库，更新售后单商品的数据:" + JSON.toJSONString(updateGood));
            afterSalesGoodsMapper.updateByPrimaryKeySelective(updateGood);
        });

    }

    private AfterSalesGoodsVo getRelateAfterSaleGood(List<AfterSalesGoodsVo> afterSaleGoodList,String sku) {
        return afterSaleGoodList
                .stream()
                .filter(afterSalesGood -> {
                    return afterSalesGood.getSku().equals(sku);
                })
                .findFirst()
                .orElse(null);
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.BUYORDER_WAREHOUSE_BACK_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.PURCHASE_RETURN.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto){

        /*String sku = outputGoodDto.getSKU();

        List<AfterSalesGoodsVo> afterSaleGoodList = ThreadLocalContext.get("afterSaleGoodList");

        return afterSaleGoodList
                    .stream()
                    .filter(afterSalesGood -> {
                        return afterSalesGood.getSku().equals(sku);
                    })
                    .map(afterSalesGood -> afterSalesGood.getAfterSalesGoodsId())
                    .findFirst()
                    .orElse(0);*/

        return Integer.valueOf(outputGoodDto.getUserDefine1());

    }



    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }
}
