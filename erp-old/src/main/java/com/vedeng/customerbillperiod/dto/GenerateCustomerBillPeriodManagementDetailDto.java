package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: daniel
 * @Date: 2021/7/26 14 55
 * @Description: 生成客户账期管理明细的传输类
 */
@Data
public class GenerateCustomerBillPeriodManagementDetailDto {

    private Integer companyId;

    private Long customerId;

    /**
     * 订单号
     */
    private String orderNo;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 生成账期逾期管理明细的类型，1订单发货，2订单开票 @CustomerBillPeriodOverdueManageDetailTypeEnum
     */
    private Integer type;

    /**
     * 明细金额
     */
    private BigDecimal amount;

    /**
     * 关联的物流id/发票id
     */
    private Long relatedId;

    private Long addTime;
}
