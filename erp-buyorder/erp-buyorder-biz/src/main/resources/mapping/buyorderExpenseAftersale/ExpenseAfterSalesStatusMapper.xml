<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesStatusMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity">
    <id column="EXPENSE_AFTER_SALES_STATUS_ID" jdbcType="BIGINT" property="expenseAfterSalesStatusId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="AFTER_SALES_STATUS" jdbcType="INTEGER" property="afterSalesStatus" />
    <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
    <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus" />
    <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime" />
    <result column="REFUND_STATUS" jdbcType="INTEGER" property="refundStatus" />
    <result column="RETURN_INVOICE_STATUS" jdbcType="INTEGER" property="returnInvoiceStatus" />
    <result column="NEED_RETURN_AMOUNT" jdbcType="DECIMAL" property="needReturnAmount" />
    <result column="REPAYMENT_PERIOD" jdbcType="DECIMAL" property="repaymentPeriod" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    EXPENSE_AFTER_SALES_STATUS_ID, EXPENSE_AFTER_SALES_ID, AFTER_SALES_STATUS, AUDIT_STATUS, 
    VALID_STATUS, VALID_TIME, REFUND_STATUS, RETURN_INVOICE_STATUS, NEED_RETURN_AMOUNT,
    REPAYMENT_PERIOD, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER,
    UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_EXPENSE_AFTER_SALES_STATUS
    where EXPENSE_AFTER_SALES_STATUS_ID = #{expenseAfterSalesStatusId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from T_EXPENSE_AFTER_SALES_STATUS
    where EXPENSE_AFTER_SALES_STATUS_ID = #{expenseAfterSalesStatusId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="EXPENSE_AFTER_SALES_STATUS_ID" keyProperty="expenseAfterSalesStatusId" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity" useGeneratedKeys="true">
    insert into T_EXPENSE_AFTER_SALES_STATUS (EXPENSE_AFTER_SALES_ID, AFTER_SALES_STATUS, 
      AUDIT_STATUS, VALID_STATUS, VALID_TIME,
      REFUND_STATUS, RETURN_INVOICE_STATUS, NEED_RETURN_AMOUNT,
      REPAYMENT_PERIOD, IS_DELETE, ADD_TIME,
      CREATOR, CREATOR_NAME, MOD_TIME, 
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{expenseAfterSalesId,jdbcType=BIGINT}, #{afterSalesStatus,jdbcType=INTEGER},
      #{auditStatus,jdbcType=INTEGER}, #{validStatus,jdbcType=INTEGER}, #{validTime,jdbcType=TIMESTAMP},
      #{refundStatus,jdbcType=INTEGER}, #{returnInvoiceStatus,jdbcType=INTEGER}, #{needReturnAmount,jdbcType=DECIMAL},
      #{repaymentPeriod,jdbcType=DECIMAL}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="EXPENSE_AFTER_SALES_STATUS_ID" keyProperty="expenseAfterSalesStatusId" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity" useGeneratedKeys="true">
    insert into T_EXPENSE_AFTER_SALES_STATUS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID,
      </if>
      <if test="afterSalesStatus != null">
        AFTER_SALES_STATUS,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="validStatus != null">
        VALID_STATUS,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="refundStatus != null">
        REFUND_STATUS,
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS,
      </if>
      <if test="needReturnAmount != null">
        NEED_RETURN_AMOUNT,
      </if>
      <if test="repaymentPeriod != null">
        REPAYMENT_PERIOD,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expenseAfterSalesId != null">
        #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="afterSalesStatus != null">
        #{afterSalesStatus,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundStatus != null">
        #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="returnInvoiceStatus != null">
        #{returnInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="needReturnAmount != null">
        #{needReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentPeriod != null">
        #{repaymentPeriod,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity">
    update T_EXPENSE_AFTER_SALES_STATUS
    <set>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="afterSalesStatus != null">
        AFTER_SALES_STATUS = #{afterSalesStatus,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundStatus != null">
        REFUND_STATUS = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="needReturnAmount != null">
        NEED_RETURN_AMOUNT = #{needReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentPeriod != null">
        REPAYMENT_PERIOD = #{repaymentPeriod,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPENSE_AFTER_SALES_STATUS_ID = #{expenseAfterSalesStatusId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity">
    update T_EXPENSE_AFTER_SALES_STATUS
    set EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      AFTER_SALES_STATUS = #{afterSalesStatus,jdbcType=INTEGER},
      AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      VALID_STATUS = #{validStatus,jdbcType=INTEGER},
      VALID_TIME = #{validTime,jdbcType=TIMESTAMP},
      REFUND_STATUS = #{refundStatus,jdbcType=INTEGER},
      RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=INTEGER},
      NEED_RETURN_AMOUNT = #{needReturnAmount,jdbcType=DECIMAL},
      REPAYMENT_PERIOD = #{repaymentPeriod,jdbcType=DECIMAL},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where EXPENSE_AFTER_SALES_STATUS_ID = #{expenseAfterSalesStatusId,jdbcType=BIGINT}
  </update>
  <update id="updateByExpenseAfterSalesIdSelective" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity">
    update T_EXPENSE_AFTER_SALES_STATUS
    <set>
      <if test="afterSalesStatus != null">
        AFTER_SALES_STATUS = #{afterSalesStatus,jdbcType=INTEGER},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null">
        VALID_STATUS = #{validStatus,jdbcType=INTEGER},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refundStatus != null">
        REFUND_STATUS = #{refundStatus,jdbcType=INTEGER},
      </if>
      <if test="returnInvoiceStatus != null">
        RETURN_INVOICE_STATUS = #{returnInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="needReturnAmount != null">
        NEED_RETURN_AMOUNT = #{needReturnAmount,jdbcType=DECIMAL},
      </if>
      <if test="repaymentPeriod != null">
        REPAYMENT_PERIOD=#{repaymentPeriod,jdbcType=DECIMAL},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
  </update>

  <select id="selectByExpenseAfterSalesId" resultType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesStatusEntity">
    SELECT
    EXPENSE_AFTER_SALES_STATUS_ID, EXPENSE_AFTER_SALES_ID, AFTER_SALES_STATUS, AUDIT_STATUS, VALID_STATUS,
    VALID_TIME, REFUND_STATUS, RETURN_INVOICE_STATUS, NEED_RETURN_AMOUNT, REPAYMENT_PERIOD, IS_DELETE, ADD_TIME,
    CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, UPDATE_REMARK
    FROM
    T_EXPENSE_AFTER_SALES_STATUS teass
    WHERE
    teass.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    AND teass.IS_DELETE = 0;

  </select>

<!--auto generated by MybatisCodeHelper on 2023-01-30-->
  <select id="findByExpenseAfterSalesIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_EXPENSE_AFTER_SALES_STATUS
        where EXPENSE_AFTER_SALES_ID in
        <foreach item="item" index="index" collection="expenseAfterSalesIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
        and IS_DELETE = 0
    </select>
</mapper>