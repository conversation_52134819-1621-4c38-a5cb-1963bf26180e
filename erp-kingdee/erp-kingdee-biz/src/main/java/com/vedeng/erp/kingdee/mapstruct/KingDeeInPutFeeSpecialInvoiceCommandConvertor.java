package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.dto.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶 进项专票 dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeInPutFeeSpecialInvoiceCommandConvertor extends BaseCommandMapStruct<KingDeeInPutFeeSpecialInvoiceCommand,InPutFeeSpecialInvoiceDto> {
    /**
     * KingDeeInPutFeeSpecialInvoiceCommand
     *
     * @param dto InPutFeePlainInvoiceDto
     * @return KingDeeInPutFeePlainInvoiceCommand
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "FINVOICENO", source = "finvoiceno")
    @Mapping(target = "f_QZOK_FPDM", source = "FQzokFpdm")
    @Mapping(target = "FINVOICEDATE", source = "finvoicedate")
    @Mapping(target = "FDATE", source = "fdate")
    @Mapping(target = "FCONTACTUNITTYPE", source = "fcontactunittype")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "FSUPPLIERID.FNumber", source = "fsupplierid")
    @Mapping(target = "FSETTLEORGID.FNumber", source = "fsettleorgid")
    @Mapping(target = "FPURCHASEORGID.FNumber", source = "fpurchaseorgid")
    @Mapping(target = "FDOCUMENTSTATUS", source = "fdocumentstatus")
    @Override
    KingDeeInPutFeeSpecialInvoiceCommand toCommand(InPutFeeSpecialInvoiceDto dto);

    /**
     * InPutFeePlainInvoiceDetailCommand
     *
     * @param dto InPutFeePlainInvoiceDetailDto
     * @return InPutFeePlainInvoiceDetailCommand
     */
    @Mapping(target = "FEXPENSEID.FNumber", source = "fexpenseid")
    @Mapping(target = "FQty", source = "FQty")
    @Mapping(target = "FUNITPRICE", source = "funitprice")
    @Mapping(target = "FTAXRATE", source = "ftaxrate")
    @Mapping(target = "f_QZOK_BDDJHID", source = "FQzokBddjhid")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_BDSKU", source = "FQzokBdsku")
    @Mapping(target = "FSOURCETYPE", source = "fsourcetype")
    KingDeeInPutFeeSpecialInvoiceCommand.InPutFeeSpecialInvoiceDetailCommand toCommand(InPutFeeSpecialInvoiceDetailDto dto);

    /**
     * InPutFeePlainInvoiceDetailLinkCommand
     *
     * @param dto InPutFeePlainInvoiceDetailLinkDto
     * @return InPutFeePlainInvoiceDetailLinkCommand
     */
    @Mapping(target = "FPUREXPINVENTRY_Link_FFlowId", source = "fpurexpinventryLinkFflowid")
    @Mapping(target = "FPUREXPINVENTRY_Link_FFlowLineId", source = "fpurexpinventryLinkFflowlineid")
    @Mapping(target = "FPUREXPINVENTRY_Link_FRuleId", source = "fpurexpinventryLinkFruleid")
    @Mapping(target = "FPUREXPINVENTRY_Link_FSTableId", source = "fpurexpinventryLinkFstableid")
    @Mapping(target = "FPUREXPINVENTRY_Link_FSTableName", source = "fpurexpinventryLinkFstablename")
    @Mapping(target = "FPUREXPINVENTRY_Link_FSBillId", source = "fpurexpinventryLinkFsbillid")
    @Mapping(target = "FPUREXPINVENTRY_Link_FSId", source = "fpurexpinventryLinkFsid")
    @Mapping(target = "FPUREXPINVENTRY_Link_FAMOUNTFOR_DOLD", source = "fpurexpinventryLinkFamountforDold")
    @Mapping(target = "FPUREXPINVENTRY_Link_FAMOUNTFOR_D", source = "fpurexpinventryLinkFamountforD")
    KingDeeInPutFeeSpecialInvoiceCommand.InPutFeeSpecialInvoiceDetailLinkCommand toCommand(InPutFeeSpecialInvoiceDetailLinkDto dto);
}
