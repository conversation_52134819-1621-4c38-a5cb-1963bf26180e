package com.vedeng.system.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;

import lombok.Data;

/**
 * @Author: daniel
 * @Date: 2021/3/9 16 35
 * @Description: url2Pdf组件的请求参数
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UrlToPdfParam {

    private String url;

    private Pdf pdf;
    
    /**
     * html 文本格式数据（有强验证，需要保证html正确性）
     */
    private String html;

    @Data
    public static class Pdf{

        private PdfMargin margin;

        private Double scale=1.0;

        public static class PdfMargin{

            private String top;

            private String right;

            private String left;

            private String bottom;

            public String getTop() {
                return top;
            }

            public void setTop(String top) {
                this.top = top;
            }

            public String getRight() {
                return right;
            }

            public void setRight(String right) {
                this.right = right;
            }

            public String getLeft() {
                return left;
            }

            public void setLeft(String left) {
                this.left = left;
            }

            public String getBottom() {
                return bottom;
            }

            public void setBottom(String bottom) {
                this.bottom = bottom;
            }

            public PdfMargin(String top, String right, String left, String bottom) {
                this.top = top;
                this.right = right;
                this.left = left;
                this.bottom = bottom;
            }

            public PdfMargin() {
            }
        }
    }
    public UrlToPdfParam() {

    }

    public static UrlToPdfParam defaultUrlToPdfParam(String url) {
        UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
        urlToPdfParam.setUrl(url);
        UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
        UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm", "1cm", "1cm", "0cm");
        pdf.setMargin(margin);
        pdf.setScale(1.5);
        urlToPdfParam.setPdf(pdf);
        return urlToPdfParam;
    }


}
