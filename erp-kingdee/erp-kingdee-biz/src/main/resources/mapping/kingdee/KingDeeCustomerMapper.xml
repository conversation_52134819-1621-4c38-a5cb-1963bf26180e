<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_CUSTOMER-->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="F_NUMBER" jdbcType="INTEGER" property="fNumber" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="F_CUST_ID" jdbcType="VARCHAR" property="fCustId" />
    <result column="F_CREATE_ORG_ID" jdbcType="INTEGER" property="fCreateOrgId" />
    <result column="F_USE_ORG_ID" jdbcType="INTEGER" property="fUseOrgId" />
    <result column="F_NAME" jdbcType="VARCHAR" property="fName" />
    <result column="F_SHORT_NAME" jdbcType="VARCHAR" property="fShortName" />
    <result column="F_CUST_TYPE_ID" jdbcType="VARCHAR" property="fCustTypeId" />
    <result column="F_ADDRESS" jdbcType="VARCHAR" property="fAddress" />
    <result column="F_INVOICE_TITLE" jdbcType="VARCHAR" property="fInvoiceTitle" />
    <result column="F_TAX_REGISTER_CODE" jdbcType="VARCHAR" property="fTaxRegisterCode" />
    <result column="F_INVOICE_BANK_NAME" jdbcType="VARCHAR" property="fInvoiceBankName" />
    <result column="F_INVOICE_TEL" jdbcType="VARCHAR" property="fInvoiceTel" />
    <result column="F_INVOICE_BANK_ACCOUNT" jdbcType="VARCHAR" property="fInvoiceBankAccount" />
    <result column="F_INVOICE_ADDRESS" jdbcType="VARCHAR" property="fInvoiceAddress" />
    <result column="F_GROUP" jdbcType="VARCHAR" property="fGroup" />
    <result column="F_TRADING_CURRID" jdbcType="VARCHAR" property="fTradingCurrid" />
    <result column="F_SETTLE_TYPE_ID" jdbcType="VARCHAR" property="fSettleTypeId" />
    <result column="F_SALDEPT_ID" jdbcType="VARCHAR" property="fSaldeptId" />
    <result column="F_TAX_RATE" jdbcType="VARCHAR" property="fTaxRate" />
    <result column="F_Q_Z_O_K_TRADE_TERMS" jdbcType="VARCHAR" property="fQZOKTradeTerms" />
    <result column="fQzokSyyygt" jdbcType="VARCHAR" property="fqzoksyyygt" />
    <result column="fQzokSsjt" jdbcType="VARCHAR" property="fqzokssjt" />
    <result column="fQzokXym" jdbcType="VARCHAR" property="fqzokxym" />
    <result column="fQzokKhxztext" jdbcType="VARCHAR" property="fqzokkhxztext" />
    <result column="fQzokZdkhfltext" jdbcType="VARCHAR" property="fqzokzdkhfltext" />
    <result column="fQzokYljgfltext" jdbcType="VARCHAR" property="fqzokyljgfltext" />
    <result column="fQzokKhxfltext" jdbcType="VARCHAR" property="fqzokkhxfltext" />
    <result column="fQzokYydjtext" jdbcType="VARCHAR" property="fqzokyydjtext" />
    <result column="fQzokKhdj" jdbcType="VARCHAR" property="fqzokkhdj" />
    <result column="fprovince" jdbcType="VARCHAR" property="fprovince" />
    <result column="FT_BD_CUSTBANK" jdbcType="LONGVARCHAR" property="ftBdCustbank" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, F_NUMBER, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_CUST_ID, 
    F_CREATE_ORG_ID, F_USE_ORG_ID, F_NAME, F_SHORT_NAME, F_CUST_TYPE_ID, F_ADDRESS, F_INVOICE_TITLE, 
    F_TAX_REGISTER_CODE, F_INVOICE_BANK_NAME, F_INVOICE_TEL, F_INVOICE_BANK_ACCOUNT, 
    F_INVOICE_ADDRESS, F_GROUP, F_TRADING_CURRID, F_SETTLE_TYPE_ID, F_SALDEPT_ID, F_TAX_RATE, 
    F_Q_Z_O_K_TRADE_TERMS, fQzokSyyygt, fQzokSsjt, fQzokXym, fQzokKhxztext, fQzokZdkhfltext, 
    fQzokYljgfltext, fQzokKhxfltext, fQzokYydjtext, fQzokKhdj, fprovince, FT_BD_CUSTBANK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_CUSTOMER
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_CUSTOMER
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_CUSTOMER (F_NUMBER, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME, F_CUST_ID, F_CREATE_ORG_ID, 
      F_USE_ORG_ID, F_NAME, F_SHORT_NAME, 
      F_CUST_TYPE_ID, F_ADDRESS, F_INVOICE_TITLE, 
      F_TAX_REGISTER_CODE, F_INVOICE_BANK_NAME, F_INVOICE_TEL, 
      F_INVOICE_BANK_ACCOUNT, F_INVOICE_ADDRESS, F_GROUP, 
      F_TRADING_CURRID, F_SETTLE_TYPE_ID, F_SALDEPT_ID, 
      F_TAX_RATE, F_Q_Z_O_K_TRADE_TERMS, fQzokSyyygt, 
      fQzokSsjt, fQzokXym, fQzokKhxztext, 
      fQzokZdkhfltext, fQzokYljgfltext, fQzokKhxfltext, 
      fQzokYydjtext, fQzokKhdj, fprovince, 
      FT_BD_CUSTBANK)
    values (#{fNumber,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR}, #{fCustId,jdbcType=VARCHAR}, #{fCreateOrgId,jdbcType=INTEGER}, 
      #{fUseOrgId,jdbcType=INTEGER}, #{fName,jdbcType=VARCHAR}, #{fShortName,jdbcType=VARCHAR}, 
      #{fCustTypeId,jdbcType=VARCHAR}, #{fAddress,jdbcType=VARCHAR}, #{fInvoiceTitle,jdbcType=VARCHAR}, 
      #{fTaxRegisterCode,jdbcType=VARCHAR}, #{fInvoiceBankName,jdbcType=VARCHAR}, #{fInvoiceTel,jdbcType=VARCHAR}, 
      #{fInvoiceBankAccount,jdbcType=VARCHAR}, #{fInvoiceAddress,jdbcType=VARCHAR}, #{fGroup,jdbcType=VARCHAR}, 
      #{fTradingCurrid,jdbcType=VARCHAR}, #{fSettleTypeId,jdbcType=VARCHAR}, #{fSaldeptId,jdbcType=VARCHAR}, 
      #{fTaxRate,jdbcType=VARCHAR}, #{fQZOKTradeTerms,jdbcType=VARCHAR}, #{fqzoksyyygt,jdbcType=VARCHAR}, 
      #{fqzokssjt,jdbcType=VARCHAR}, #{fqzokxym,jdbcType=VARCHAR}, #{fqzokkhxztext,jdbcType=VARCHAR}, 
      #{fqzokzdkhfltext,jdbcType=VARCHAR}, #{fqzokyljgfltext,jdbcType=VARCHAR}, #{fqzokkhxfltext,jdbcType=VARCHAR}, 
      #{fqzokyydjtext,jdbcType=VARCHAR}, #{fqzokkhdj,jdbcType=VARCHAR}, #{fprovince,jdbcType=VARCHAR}, 
      #{ftBdCustbank,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_CUSTOMER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fNumber != null">
        F_NUMBER,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fCustId != null">
        F_CUST_ID,
      </if>
      <if test="fCreateOrgId != null">
        F_CREATE_ORG_ID,
      </if>
      <if test="fUseOrgId != null">
        F_USE_ORG_ID,
      </if>
      <if test="fName != null">
        F_NAME,
      </if>
      <if test="fShortName != null">
        F_SHORT_NAME,
      </if>
      <if test="fCustTypeId != null">
        F_CUST_TYPE_ID,
      </if>
      <if test="fAddress != null">
        F_ADDRESS,
      </if>
      <if test="fInvoiceTitle != null">
        F_INVOICE_TITLE,
      </if>
      <if test="fTaxRegisterCode != null">
        F_TAX_REGISTER_CODE,
      </if>
      <if test="fInvoiceBankName != null">
        F_INVOICE_BANK_NAME,
      </if>
      <if test="fInvoiceTel != null">
        F_INVOICE_TEL,
      </if>
      <if test="fInvoiceBankAccount != null">
        F_INVOICE_BANK_ACCOUNT,
      </if>
      <if test="fInvoiceAddress != null">
        F_INVOICE_ADDRESS,
      </if>
      <if test="fGroup != null">
        F_GROUP,
      </if>
      <if test="fTradingCurrid != null">
        F_TRADING_CURRID,
      </if>
      <if test="fSettleTypeId != null">
        F_SETTLE_TYPE_ID,
      </if>
      <if test="fSaldeptId != null">
        F_SALDEPT_ID,
      </if>
      <if test="fTaxRate != null">
        F_TAX_RATE,
      </if>
      <if test="fQZOKTradeTerms != null">
        F_Q_Z_O_K_TRADE_TERMS,
      </if>
      <if test="fqzoksyyygt != null">
        fQzokSyyygt,
      </if>
      <if test="fqzokssjt != null">
        fQzokSsjt,
      </if>
      <if test="fqzokxym != null">
        fQzokXym,
      </if>
      <if test="fqzokkhxztext != null">
        fQzokKhxztext,
      </if>
      <if test="fqzokzdkhfltext != null">
        fQzokZdkhfltext,
      </if>
      <if test="fqzokyljgfltext != null">
        fQzokYljgfltext,
      </if>
      <if test="fqzokkhxfltext != null">
        fQzokKhxfltext,
      </if>
      <if test="fqzokyydjtext != null">
        fQzokYydjtext,
      </if>
      <if test="fqzokkhdj != null">
        fQzokKhdj,
      </if>
      <if test="fprovince != null">
        fprovince,
      </if>
      <if test="ftBdCustbank != null">
        FT_BD_CUSTBANK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fNumber != null">
        #{fNumber,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fCustId != null">
        #{fCustId,jdbcType=VARCHAR},
      </if>
      <if test="fCreateOrgId != null">
        #{fCreateOrgId,jdbcType=INTEGER},
      </if>
      <if test="fUseOrgId != null">
        #{fUseOrgId,jdbcType=INTEGER},
      </if>
      <if test="fName != null">
        #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fShortName != null">
        #{fShortName,jdbcType=VARCHAR},
      </if>
      <if test="fCustTypeId != null">
        #{fCustTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fAddress != null">
        #{fAddress,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceTitle != null">
        #{fInvoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="fTaxRegisterCode != null">
        #{fTaxRegisterCode,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceBankName != null">
        #{fInvoiceBankName,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceTel != null">
        #{fInvoiceTel,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceBankAccount != null">
        #{fInvoiceBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceAddress != null">
        #{fInvoiceAddress,jdbcType=VARCHAR},
      </if>
      <if test="fGroup != null">
        #{fGroup,jdbcType=VARCHAR},
      </if>
      <if test="fTradingCurrid != null">
        #{fTradingCurrid,jdbcType=VARCHAR},
      </if>
      <if test="fSettleTypeId != null">
        #{fSettleTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fSaldeptId != null">
        #{fSaldeptId,jdbcType=VARCHAR},
      </if>
      <if test="fTaxRate != null">
        #{fTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="fQZOKTradeTerms != null">
        #{fQZOKTradeTerms,jdbcType=VARCHAR},
      </if>
      <if test="fqzoksyyygt != null">
        #{fqzoksyyygt,jdbcType=VARCHAR},
      </if>
      <if test="fqzokssjt != null">
        #{fqzokssjt,jdbcType=VARCHAR},
      </if>
      <if test="fqzokxym != null">
        #{fqzokxym,jdbcType=VARCHAR},
      </if>
      <if test="fqzokkhxztext != null">
        #{fqzokkhxztext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokzdkhfltext != null">
        #{fqzokzdkhfltext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokyljgfltext != null">
        #{fqzokyljgfltext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokkhxfltext != null">
        #{fqzokkhxfltext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokyydjtext != null">
        #{fqzokyydjtext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokkhdj != null">
        #{fqzokkhdj,jdbcType=VARCHAR},
      </if>
      <if test="fprovince != null">
        #{fprovince,jdbcType=VARCHAR},
      </if>
      <if test="ftBdCustbank != null">
        #{ftBdCustbank,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity">
    <!--@mbg.generated-->
    update KING_DEE_CUSTOMER
    <set>
      <if test="fNumber != null">
        F_NUMBER = #{fNumber,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fCustId != null">
        F_CUST_ID = #{fCustId,jdbcType=VARCHAR},
      </if>
      <if test="fCreateOrgId != null">
        F_CREATE_ORG_ID = #{fCreateOrgId,jdbcType=INTEGER},
      </if>
      <if test="fUseOrgId != null">
        F_USE_ORG_ID = #{fUseOrgId,jdbcType=INTEGER},
      </if>
      <if test="fName != null">
        F_NAME = #{fName,jdbcType=VARCHAR},
      </if>
      <if test="fShortName != null">
        F_SHORT_NAME = #{fShortName,jdbcType=VARCHAR},
      </if>
      <if test="fCustTypeId != null">
        F_CUST_TYPE_ID = #{fCustTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fAddress != null">
        F_ADDRESS = #{fAddress,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceTitle != null">
        F_INVOICE_TITLE = #{fInvoiceTitle,jdbcType=VARCHAR},
      </if>
      <if test="fTaxRegisterCode != null">
        F_TAX_REGISTER_CODE = #{fTaxRegisterCode,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceBankName != null">
        F_INVOICE_BANK_NAME = #{fInvoiceBankName,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceTel != null">
        F_INVOICE_TEL = #{fInvoiceTel,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceBankAccount != null">
        F_INVOICE_BANK_ACCOUNT = #{fInvoiceBankAccount,jdbcType=VARCHAR},
      </if>
      <if test="fInvoiceAddress != null">
        F_INVOICE_ADDRESS = #{fInvoiceAddress,jdbcType=VARCHAR},
      </if>
      <if test="fGroup != null">
        F_GROUP = #{fGroup,jdbcType=VARCHAR},
      </if>
      <if test="fTradingCurrid != null">
        F_TRADING_CURRID = #{fTradingCurrid,jdbcType=VARCHAR},
      </if>
      <if test="fSettleTypeId != null">
        F_SETTLE_TYPE_ID = #{fSettleTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fSaldeptId != null">
        F_SALDEPT_ID = #{fSaldeptId,jdbcType=VARCHAR},
      </if>
      <if test="fTaxRate != null">
        F_TAX_RATE = #{fTaxRate,jdbcType=VARCHAR},
      </if>
      <if test="fQZOKTradeTerms != null">
        F_Q_Z_O_K_TRADE_TERMS = #{fQZOKTradeTerms,jdbcType=VARCHAR},
      </if>
      <if test="fqzoksyyygt != null">
        fQzokSyyygt = #{fqzoksyyygt,jdbcType=VARCHAR},
      </if>
      <if test="fqzokssjt != null">
        fQzokSsjt = #{fqzokssjt,jdbcType=VARCHAR},
      </if>
      <if test="fqzokxym != null">
        fQzokXym = #{fqzokxym,jdbcType=VARCHAR},
      </if>
      <if test="fqzokkhxztext != null">
        fQzokKhxztext = #{fqzokkhxztext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokzdkhfltext != null">
        fQzokZdkhfltext = #{fqzokzdkhfltext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokyljgfltext != null">
        fQzokYljgfltext = #{fqzokyljgfltext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokkhxfltext != null">
        fQzokKhxfltext = #{fqzokkhxfltext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokyydjtext != null">
        fQzokYydjtext = #{fqzokyydjtext,jdbcType=VARCHAR},
      </if>
      <if test="fqzokkhdj != null">
        fQzokKhdj = #{fqzokkhdj,jdbcType=VARCHAR},
      </if>
      <if test="fprovince != null">
        fprovince = #{fprovince,jdbcType=VARCHAR},
      </if>
      <if test="ftBdCustbank != null">
        FT_BD_CUSTBANK = #{ftBdCustbank,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeCustomerEntity">
    <!--@mbg.generated-->
    update KING_DEE_CUSTOMER
    set F_NUMBER = #{fNumber,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      F_CUST_ID = #{fCustId,jdbcType=VARCHAR},
      F_CREATE_ORG_ID = #{fCreateOrgId,jdbcType=INTEGER},
      F_USE_ORG_ID = #{fUseOrgId,jdbcType=INTEGER},
      F_NAME = #{fName,jdbcType=VARCHAR},
      F_SHORT_NAME = #{fShortName,jdbcType=VARCHAR},
      F_CUST_TYPE_ID = #{fCustTypeId,jdbcType=VARCHAR},
      F_ADDRESS = #{fAddress,jdbcType=VARCHAR},
      F_INVOICE_TITLE = #{fInvoiceTitle,jdbcType=VARCHAR},
      F_TAX_REGISTER_CODE = #{fTaxRegisterCode,jdbcType=VARCHAR},
      F_INVOICE_BANK_NAME = #{fInvoiceBankName,jdbcType=VARCHAR},
      F_INVOICE_TEL = #{fInvoiceTel,jdbcType=VARCHAR},
      F_INVOICE_BANK_ACCOUNT = #{fInvoiceBankAccount,jdbcType=VARCHAR},
      F_INVOICE_ADDRESS = #{fInvoiceAddress,jdbcType=VARCHAR},
      F_GROUP = #{fGroup,jdbcType=VARCHAR},
      F_TRADING_CURRID = #{fTradingCurrid,jdbcType=VARCHAR},
      F_SETTLE_TYPE_ID = #{fSettleTypeId,jdbcType=VARCHAR},
      F_SALDEPT_ID = #{fSaldeptId,jdbcType=VARCHAR},
      F_TAX_RATE = #{fTaxRate,jdbcType=VARCHAR},
      F_Q_Z_O_K_TRADE_TERMS = #{fQZOKTradeTerms,jdbcType=VARCHAR},
      fQzokSyyygt = #{fqzoksyyygt,jdbcType=VARCHAR},
      fQzokSsjt = #{fqzokssjt,jdbcType=VARCHAR},
      fQzokXym = #{fqzokxym,jdbcType=VARCHAR},
      fQzokKhxztext = #{fqzokkhxztext,jdbcType=VARCHAR},
      fQzokZdkhfltext = #{fqzokzdkhfltext,jdbcType=VARCHAR},
      fQzokYljgfltext = #{fqzokyljgfltext,jdbcType=VARCHAR},
      fQzokKhxfltext = #{fqzokkhxfltext,jdbcType=VARCHAR},
      fQzokYydjtext = #{fqzokyydjtext,jdbcType=VARCHAR},
      fQzokKhdj = #{fqzokkhdj,jdbcType=VARCHAR},
      fprovince = #{fprovince,jdbcType=VARCHAR},
      FT_BD_CUSTBANK = #{ftBdCustbank,jdbcType=LONGVARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update KING_DEE_CUSTOMER
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="F_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fNumber != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fNumber,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CUST_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fCustId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fCustId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CREATE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fCreateOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fCreateOrgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_USE_ORG_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fUseOrgId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fUseOrgId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SHORT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fShortName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fShortName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_CUST_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fCustTypeId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fCustTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fAddress != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_INVOICE_TITLE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fInvoiceTitle != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fInvoiceTitle,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_TAX_REGISTER_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fTaxRegisterCode != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fTaxRegisterCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_INVOICE_BANK_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fInvoiceBankName != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fInvoiceBankName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_INVOICE_TEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fInvoiceTel != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fInvoiceTel,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_INVOICE_BANK_ACCOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fInvoiceBankAccount != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fInvoiceBankAccount,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_INVOICE_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fInvoiceAddress != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fInvoiceAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_GROUP = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fGroup != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fGroup,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_TRADING_CURRID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fTradingCurrid != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fTradingCurrid,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SETTLE_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSettleTypeId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSettleTypeId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_SALDEPT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fSaldeptId != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fSaldeptId,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_TAX_RATE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fTaxRate != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fTaxRate,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="F_Q_Z_O_K_TRADE_TERMS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fQZOKTradeTerms != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fQZOKTradeTerms,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokSyyygt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzoksyyygt != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzoksyyygt,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokSsjt = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokssjt != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokssjt,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokXym = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokxym != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokxym,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokKhxztext = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokkhxztext != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokkhxztext,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokZdkhfltext = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokzdkhfltext != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokzdkhfltext,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokYljgfltext = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokyljgfltext != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokyljgfltext,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokKhxfltext = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokkhxfltext != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokkhxfltext,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokYydjtext = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokyydjtext != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokyydjtext,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fQzokKhdj = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fqzokkhdj != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fqzokkhdj,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="fprovince = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fprovince != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.fprovince,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FT_BD_CUSTBANK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ftBdCustbank != null">
            when ID = #{item.id,jdbcType=INTEGER} then #{item.ftBdCustbank,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_CUSTOMER
    (F_NUMBER, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, F_CUST_ID, 
      F_CREATE_ORG_ID, F_USE_ORG_ID, F_NAME, F_SHORT_NAME, F_CUST_TYPE_ID, F_ADDRESS, 
      F_INVOICE_TITLE, F_TAX_REGISTER_CODE, F_INVOICE_BANK_NAME, F_INVOICE_TEL, F_INVOICE_BANK_ACCOUNT, 
      F_INVOICE_ADDRESS, F_GROUP, F_TRADING_CURRID, F_SETTLE_TYPE_ID, F_SALDEPT_ID, F_TAX_RATE, 
      F_Q_Z_O_K_TRADE_TERMS, fQzokSyyygt, fQzokSsjt, fQzokXym, fQzokKhxztext, fQzokZdkhfltext, 
      fQzokYljgfltext, fQzokKhxfltext, fQzokYydjtext, fQzokKhdj, fprovince, FT_BD_CUSTBANK
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fNumber,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updaterName,jdbcType=VARCHAR}, #{item.fCustId,jdbcType=VARCHAR}, #{item.fCreateOrgId,jdbcType=INTEGER}, 
        #{item.fUseOrgId,jdbcType=INTEGER}, #{item.fName,jdbcType=VARCHAR}, #{item.fShortName,jdbcType=VARCHAR}, 
        #{item.fCustTypeId,jdbcType=VARCHAR}, #{item.fAddress,jdbcType=VARCHAR}, #{item.fInvoiceTitle,jdbcType=VARCHAR}, 
        #{item.fTaxRegisterCode,jdbcType=VARCHAR}, #{item.fInvoiceBankName,jdbcType=VARCHAR}, 
        #{item.fInvoiceTel,jdbcType=VARCHAR}, #{item.fInvoiceBankAccount,jdbcType=VARCHAR}, 
        #{item.fInvoiceAddress,jdbcType=VARCHAR}, #{item.fGroup,jdbcType=VARCHAR}, #{item.fTradingCurrid,jdbcType=VARCHAR}, 
        #{item.fSettleTypeId,jdbcType=VARCHAR}, #{item.fSaldeptId,jdbcType=VARCHAR}, #{item.fTaxRate,jdbcType=VARCHAR}, 
        #{item.fQZOKTradeTerms,jdbcType=VARCHAR}, #{item.fqzoksyyygt,jdbcType=VARCHAR}, 
        #{item.fqzokssjt,jdbcType=VARCHAR}, #{item.fqzokxym,jdbcType=VARCHAR}, #{item.fqzokkhxztext,jdbcType=VARCHAR}, 
        #{item.fqzokzdkhfltext,jdbcType=VARCHAR}, #{item.fqzokyljgfltext,jdbcType=VARCHAR}, 
        #{item.fqzokkhxfltext,jdbcType=VARCHAR}, #{item.fqzokyydjtext,jdbcType=VARCHAR}, 
        #{item.fqzokkhdj,jdbcType=VARCHAR}, #{item.fprovince,jdbcType=VARCHAR}, #{item.ftBdCustbank,jdbcType=LONGVARCHAR}
        )
    </foreach>
  </insert>

  <select id="queryInfoByCustomerId" resultType="com.vedeng.erp.kingdee.dto.KingDeeCustomerDto">
    SELECT * FROM
    KING_DEE_CUSTOMER
    WHERE F_NUMBER = #{traderCustomerId,jdbcType=INTEGER}
  </select>

  <select id="selectByFNumber" resultMap="BaseResultMap">
    SELECT * FROM
    KING_DEE_CUSTOMER
    WHERE F_NUMBER = #{fNumber,jdbcType=INTEGER}
  </select>

  <select id="queryInfoByCustomerName" resultMap="BaseResultMap">
    SELECT * FROM
      KING_DEE_CUSTOMER
    WHERE F_NAME = #{traderCustomerName,jdbcType=VARCHAR}
  </select>
  <select id="queryFCUSTIDByCustomerName" resultType="java.lang.String">
    SELECT F_CUST_ID FROM
      KING_DEE_CUSTOMER
    WHERE F_NAME = #{traderCustomerName,jdbcType=VARCHAR}
  </select>
  <select id="queryFNUMBERByCustomerName" resultType="java.lang.String">
    SELECT F_NUMBER FROM
      KING_DEE_CUSTOMER
    WHERE F_NAME = #{traderCustomerName,jdbcType=VARCHAR}
  </select>
</mapper>