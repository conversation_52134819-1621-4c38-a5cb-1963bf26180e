package com.wms.service;

import com.vedeng.common.key.BASE64Coding;
import com.vedeng.common.util.DateUtil;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsRequest;
import com.wms.dto.WmsResponse;
import net.sf.json.JSONObject;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.util.Arrays;

/**
 * 抽象的WMS接口
 */
@Service
public abstract class AbstractWmsInterface implements WmsInterface {

    public static Logger LOGGER = LoggerFactory.getLogger(AbstractWmsInterface.class);

    @Value("${wms.client_customerid}")
    private String clientCustomerid;

    @Value("${wms.client_db}")
    private String clientDb;

    @Value("${wms.appkey}")
    private String appKey;

    @Value("${wms.apptoken}")
    private String appToken;

    @Value("${wms.fluxurl}")
    private String fluxUrl;

    /**
     * 定义接口的请求步骤
     * @return
     */
    public WmsResponse request(Object ... param) throws Exception{

        //设置请求头数据
        WmsRequest wmsRequest = new WmsRequest();

        //封装请求头
        encapsulateRequestHeader(wmsRequest);
        //封装data
        wmsRequest.setData(getRequestData(param));


        //最大重试次数
        int maxRetry = 1;

        String httpResponse = null;
        long start=System.currentTimeMillis();
        for(int i = 0;i <= maxRetry;i++){
            //执行请求
            try {
                httpResponse = executeRequest(wmsRequest);
                break;
            }catch (Exception e){

                LOGGER.error("请求失败",e);

                //重试最大次数还是失败，直接抛异常 后期可以考虑将其丢入到内存队列，然后定时重试等等
                if(i == maxRetry){
                    throw new Exception("请求WMS接口异常",e);
                }
            }
        }
        LOGGER.info("请求wms时间：{}ms 参数：{} 返回：{}",(System.currentTimeMillis()-start),wmsRequest.getData(),httpResponse);
        //解析http响应到erp自己的响应
        WmsResponse wmsResponse = parseHttpResponse(httpResponse);

        return wmsResponse;
    }

    /**
     * 封装请求头参数
     * @param wmsRequest
     */
    protected void encapsulateRequestHeader(WmsRequest wmsRequest) throws Exception{

        //设置method和messageid属性
        setMethodAndMessageId(wmsRequest);

        //设置公共的请求参数，所有接口都一样的
        setCommonRequestParam(wmsRequest);

        //计算签名值
        calculateSign(wmsRequest);

    }

    /**
     * 计算签名值
     * @param wmsRequest
     */
    protected void calculateSign(WmsRequest wmsRequest) throws Exception{

        // 发送的json data报文
        String jsonData = wmsRequest.getData();
        String signStr = WMSContant.WMS_APP_SECRET + jsonData + WMSContant.WMS_APP_SECRET;

        // 计算md5函数
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        messageDigest.update(signStr.getBytes());

        // digest()最后确定返回md5 hash值，返回值为8为字符串。因为md5 hash值是16位的hex值，实际上就是8位的字符
        // BigInteger函数则将8位的字符串转换成16位hex值，用字符串来表示；得到字符串形式的hash值
        String compeleteSign = new BigInteger(1, messageDigest.digest()).toString(16);
        // 以0开头,BigInteger会把0省略掉，需补全至32位
        if (compeleteSign.length() < 32) {
            compeleteSign = "0" + compeleteSign;
        }

        compeleteSign = BASE64Coding.encode(compeleteSign); // base64编码
        wmsRequest.setSign(compeleteSign);
    }

    /**
     * 设置通用的请求参数
     * @param wmsRequest
     */
    protected void setCommonRequestParam(WmsRequest wmsRequest) {

        wmsRequest.setClientCustomerId(this.clientCustomerid);
        wmsRequest.setClientDb(this.clientDb);
        wmsRequest.setAppKey(this.appKey);
        wmsRequest.setAppToken(this.appToken);
        wmsRequest.setTimestamp(DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT));

    }

    /**
     * 设置method和messageId 对于不同的接口这个参数值不一样
     * @param wmsRequest
     */
    protected abstract void setMethodAndMessageId(WmsRequest wmsRequest);

    /**
     * 根据参数执行请求
     * @param wmsRequest
     * @return
     */
    protected String executeRequest(WmsRequest wmsRequest) throws Exception{

        HttpClient client = new HttpClient();

        PostMethod post = new PostMethod(fluxUrl);
        post.addRequestHeader("Content-Encoding", "UTF-8");
        post.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");

        // 接口API方法名，根据接口做相应
        post.setParameter("method", wmsRequest.getMethod());
        post.setParameter("client_customerid", wmsRequest.getClientCustomerId());
        post.setParameter("client_db", wmsRequest.getClientDb());
        // 消息代码，根据接口做相应调整
        post.setParameter("messageid", wmsRequest.getMessageId());
        post.setParameter("apptoken", wmsRequest.getAppToken());
        post.setParameter("timestamp", wmsRequest.getTimestamp());
        post.setParameter("appkey", wmsRequest.getAppKey());
        // URL编码
        post.setParameter("sign", URLEncoder.encode(wmsRequest.getSign().toUpperCase(), "utf-8"));
        // URL编码
        /*post.setParameter("data", URLEncoder.encode(wmsRequest.getData(), "utf-8"));*/

        post.setParameter("data", wmsRequest.getData());

        // 设置连接的超时时间
        client.getHttpConnectionManager().getParams().setConnectionTimeout(3 * 1000);
        // 设置读取数据的超时时间
        client.getHttpConnectionManager().getParams().setSoTimeout(180 * 1000);

        //LOGGER.info("WMS->接口请求参数:" + JSON.toJSON(wmsRequest));

        NameValuePair[] nameValuePairs = post.getParameters();
        Arrays.stream(nameValuePairs).forEach(nameValuePair -> {
            LOGGER.info("参数名称:" + nameValuePair.getName() + ",参数值:" + nameValuePair.getValue());
            if("data".equals(nameValuePair.getName())){
//                XxlJobLogger.log("参数名称:data,参数值:{}",nameValuePair.getValue());
            }
        });

        String responseStr = null;

        try {

            client.executeMethod(post);

            responseStr = new String(post.getResponseBody(), "UTF-8");

            //String responseStr = URLDecoder.decode(post.getResponseBodyAsString(), "UTF-8");
            LOGGER.info("WMS->接口请求响应:" + responseStr);

        } catch (Exception e) {
            throw e;
        } finally {
            post.releaseConnection();
        }

        return responseStr;
    }

    /**
     * 将wms的响应转换下
     * @param httpResponse
     * @return
     */
    protected <T> WmsResponse parseHttpResponse(String httpResponse) {

        JSONObject responseJsonObject = JSONObject.fromObject(httpResponse).getJSONObject("Response");

        JSONObject returnObject = responseJsonObject.getJSONObject("return");

        WmsResponse wmsResponse = new WmsResponse();
        wmsResponse.setReturnFlag(returnObject.getString("returnFlag"));
        wmsResponse.setReturnCode(returnObject.getString("returnCode"));
        wmsResponse.setReturnDesc(returnObject.getString("returnDesc"));
        String totalStr ="";
        try{
            if(returnObject.has("totalCount_Field")){
                totalStr = returnObject.getString("totalCount_Field");
            }
            wmsResponse.setTotalCount(totalStr);
        }catch(Exception e){
            LOGGER.error("",e);
        }

        T data = this.parseResponseDate(responseJsonObject);
        wmsResponse.setData(data);

        return wmsResponse;
    }

    /**
     * 封装请求参数的获取
     * @param param
     * @return
     */
    public String getRequestData(Object ... param) {
        JSONObject xmldataObj = new JSONObject();
        xmldataObj.put("xmldata",getXmlDate(param));
        return xmldataObj.toString();
    }

    /**
     * 获取接口的请求body,对于不同的接口实现不同
     * @param param
     * @return
     */
    protected abstract JSONObject getXmlDate(Object ... param);

    protected abstract <T> T parseResponseDate(JSONObject returnObject);

}
