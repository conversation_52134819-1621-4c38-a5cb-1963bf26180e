package com.newtask.goods;

import com.alibaba.fastjson.TypeReference;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ApiUrlConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.stock.api.stock.dto.BatchQueryStockDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 迁移资质
 */
@JobHandler(value = "SyncSkuStockNumberTask")
@Component
public class SyncSkuStockNumberTask extends AbstractJobHandler {
    private static final Logger LOGGER = LoggerFactory.getLogger(SyncSkuStockNumberTask.class);


    @Value("${stock_url}")
    private String stockUrl;
    @Autowired
    GoodsMapper goodsMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        TypeReference<RestfulResult <List<WarehouseDto>>> stockType = new TypeReference<RestfulResult<List<WarehouseDto>> >(){};
        BatchQueryStockDto queryStockDto=new BatchQueryStockDto();
        RestfulResult<List<WarehouseDto>>  stockInfoDto = HttpRestClientUtil.restPost(stockUrl +  ApiUrlConstant.SYNC_SKU_STOCK_INFO, stockType, null,
                queryStockDto);
        if (Objects.isNull(stockInfoDto) || Objects.isNull(stockInfoDto.getData())){
            LOGGER.info("getLastestChangedStockInfoSimple 查询结果为空，不处理");
            return ReturnT.SUCCESS;
        }
        stockInfoDto.getData().parallelStream().forEach(item->{
            try {
                goodsMapper.updateSkuOcc(item.getSku(), item.getOccupyNum() < 0 ? 0 : item.getOccupyNum());
            }catch(Exception e){XxlJobLogger.log(e);}
            try {
            goodsMapper.updateSkuLock(item.getSku() ,item.getActionLockNum()<0?0:item.getActionLockNum());
        }catch(Exception e){XxlJobLogger.log(e);}
        });
        return ReturnT.SUCCESS;
    }

}
