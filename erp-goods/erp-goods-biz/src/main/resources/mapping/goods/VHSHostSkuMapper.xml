<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.VHSHostSkuMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.dto.VHSHostSkuDTO">
    <id column="HOST_SKU_ID" jdbcType="INTEGER" property="hostSkuId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="OP_HOST_WORD_ID" jdbcType="INTEGER" property="opHostWordId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="UPDATER" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    HOST_SKU_ID, SKU, OP_HOST_WORD_ID, ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.dto.VHSHostSkuDTOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_H_S_HOST_SKU
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_H_S_HOST_SKU
    where HOST_SKU_ID = #{hostSkuId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from V_H_S_HOST_SKU
    where HOST_SKU_ID = #{hostSkuId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.dto.VHSHostSkuDTOExample">
    delete from V_H_S_HOST_SKU
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="HOST_SKU_ID" keyProperty="hostSkuId" parameterType="com.vedeng.goods.dto.VHSHostSkuDTO" useGeneratedKeys="true">
    insert into V_H_S_HOST_SKU (SKU, OP_HOST_WORD_ID, ADD_TIME,
      MODE_TIME, IS_DELETE, CREATOR,
      UPDATER)
    values (#{sku,jdbcType=VARCHAR}, #{opHostWordId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER}, #{creator,jdbcType=VARCHAR},
      #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="HOST_SKU_ID" keyProperty="hostSkuId" parameterType="com.vedeng.goods.dto.VHSHostSkuDTO" useGeneratedKeys="true">
    insert into V_H_S_HOST_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        SKU,
      </if>
      <if test="opHostWordId != null">
        OP_HOST_WORD_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="opHostWordId != null">
        #{opHostWordId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.dto.VHSHostSkuDTOExample" resultType="java.lang.Long">
    select count(*) from V_H_S_HOST_SKU
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <update id="updateByExampleSelective" parameterType="map">
    update V_H_S_HOST_SKU
    <set>
      <if test="record.hostSkuId != null">
        HOST_SKU_ID = #{record.hostSkuId,jdbcType=INTEGER},
      </if>
      <if test="record.sku != null">
        SKU = #{record.sku,jdbcType=VARCHAR},
      </if>
      <if test="record.opHostWordId != null">
        OP_HOST_WORD_ID = #{record.opHostWordId,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modeTime != null">
        MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update V_H_S_HOST_SKU
    set HOST_SKU_ID = #{record.hostSkuId,jdbcType=INTEGER},
      SKU = #{record.sku,jdbcType=VARCHAR},
      OP_HOST_WORD_ID = #{record.opHostWordId,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{record.isDelete,jdbcType=INTEGER},
      CREATOR = #{record.creator,jdbcType=VARCHAR},
      UPDATER = #{record.updater,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.dto.VHSHostSkuDTO">
    update V_H_S_HOST_SKU
    <set>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="opHostWordId != null">
        OP_HOST_WORD_ID = #{opHostWordId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where HOST_SKU_ID = #{hostSkuId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.dto.VHSHostSkuDTO">
    update V_H_S_HOST_SKU
    set SKU = #{sku,jdbcType=VARCHAR},
      OP_HOST_WORD_ID = #{opHostWordId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=VARCHAR}
    where HOST_SKU_ID = #{hostSkuId,jdbcType=INTEGER}
  </update>

  <select id="getSkuHostByOpIdAndSku" resultType="com.vedeng.goods.dto.VHSHostSkuDTO">

    SELECT
      *
    FROM
      V_H_S_HOST_SKU A
    WHERE
      A.SKU = #{sku}
      AND A.OP_HOST_WORD_ID = #{opHostWordId}
  </select>

</mapper>
