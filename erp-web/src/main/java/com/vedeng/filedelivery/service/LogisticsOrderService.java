package com.vedeng.filedelivery.service;

import java.util.List;

/**
 * 面单下单服务
 * <AUTHOR>
 *
 */
public interface LogisticsOrderService {

	/**
	 * 寄送服务面单下单操作
	 * @param fileDeliveryId
	 * @param userId 
	 * @param userNumber 
	 * @param printType 
	 * @params systemSource 请求来源【"erp","express"】
	 * @params invokeType 触发类型【1：审核通过触发,2：打印触发,3:express应用回调接口】
	 */
	List<String> submit(Integer fileDeliveryId, Integer userId,Integer printType, String systemSource,Integer invokeType);

}
