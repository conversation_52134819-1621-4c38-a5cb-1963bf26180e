package com.vedeng.erp.finance.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 承兑汇票
 * @date 2024/9/9 9:24
 */
@RequestMapping("/acceptanceBill")
@Controller
@Slf4j
public class AcceptanceBillController extends BaseController {


    /**
     * 列表
     */
    @RequestMapping(value = "/index")
    @ExcludeAuthorization
    public String index() {
        return view();
    }


}
