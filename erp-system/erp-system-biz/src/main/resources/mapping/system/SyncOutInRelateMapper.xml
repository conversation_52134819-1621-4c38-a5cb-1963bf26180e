<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.SyncOutInRelateMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity">
        <id column="ID" property="id" />
        <result column="OUT_BUSINESS_NO" property="outBusinessNo" />
        <result column="BUSINESS_TYPE" property="businessType" />
        <result column="IN_BUSINESS_NO" property="inBusinessNo" />
        <result column="SOURCE_ERP" property="sourceErp" />
        <result column="REQUEST_CONTENT" property="requestContent" />
        <result column="PROCESS_STATUS" property="processStatus" />
        <result column="CREATE_TIME" property="createTime" />
        <result column="UPDATE_TIME" property="updateTime" />
        <result column="CREATE_USER" property="createUser" />
        <result column="UPDATE_USER" property="updateUser" />
        <result column="IS_DELETED" property="isDeleted" />
    </resultMap>

    <sql id="Base_Column_List">
        ID, OUT_BUSINESS_NO, BUSINESS_TYPE, IN_BUSINESS_NO, SOURCE_ERP, REQUEST_CONTENT, PROCESS_STATUS,
        CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, IS_DELETED
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_OUT_IN_RELATE
        where ID = #{id}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from T_SYNC_OUT_IN_RELATE
        where ID = #{id}
    </delete>

    <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity" useGeneratedKeys="true">
        insert into T_SYNC_OUT_IN_RELATE (OUT_BUSINESS_NO, BUSINESS_TYPE, IN_BUSINESS_NO,
            SOURCE_ERP, REQUEST_CONTENT, PROCESS_STATUS, CREATE_TIME, UPDATE_TIME,
            CREATE_USER, UPDATE_USER, IS_DELETED)
        values (#{outBusinessNo}, #{businessType}, #{inBusinessNo},
            #{sourceErp}, #{requestContent}, #{processStatus}, #{createTime}, #{updateTime},
            #{createUser}, #{updateUser}, #{isDeleted})
    </insert>

    <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity" useGeneratedKeys="true">
        insert into T_SYNC_OUT_IN_RELATE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="outBusinessNo != null">
                OUT_BUSINESS_NO,
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE,
            </if>
            <if test="inBusinessNo != null">
                IN_BUSINESS_NO,
            </if>
            <if test="sourceErp != null">
                SOURCE_ERP,
            </if>
            <if test="requestContent != null">
                REQUEST_CONTENT,
            </if>
            <if test="processStatus != null">
                PROCESS_STATUS,
            </if>
            <if test="createTime != null">
                CREATE_TIME,
            </if>
            <if test="updateTime != null">
                UPDATE_TIME,
            </if>
            <if test="createUser != null">
                CREATE_USER,
            </if>
            <if test="updateUser != null">
                UPDATE_USER,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="outBusinessNo != null">
                #{outBusinessNo},
            </if>
            <if test="businessType != null">
                #{businessType},
            </if>
            <if test="inBusinessNo != null">
                #{inBusinessNo},
            </if>
            <if test="sourceErp != null">
                #{sourceErp},
            </if>
            <if test="requestContent != null">
                #{requestContent},
            </if>
            <if test="processStatus != null">
                #{processStatus},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createUser != null">
                #{createUser},
            </if>
            <if test="updateUser != null">
                #{updateUser},
            </if>
            <if test="isDeleted != null">
                #{isDeleted},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity">
        update T_SYNC_OUT_IN_RELATE
        <set>
            <if test="outBusinessNo != null">
                OUT_BUSINESS_NO = #{outBusinessNo},
            </if>
            <if test="businessType != null">
                BUSINESS_TYPE = #{businessType},
            </if>
            <if test="inBusinessNo != null">
                IN_BUSINESS_NO = #{inBusinessNo},
            </if>
            <if test="sourceErp != null">
                SOURCE_ERP = #{sourceErp},
            </if>
            <if test="requestContent != null">
                REQUEST_CONTENT = #{requestContent},
            </if>
            <if test="processStatus != null">
                PROCESS_STATUS = #{processStatus},
            </if>
            <if test="createTime != null">
                CREATE_TIME = #{createTime},
            </if>
            <if test="updateTime != null">
                UPDATE_TIME = #{updateTime},
            </if>
            <if test="createUser != null">
                CREATE_USER = #{createUser},
            </if>
            <if test="updateUser != null">
                UPDATE_USER = #{updateUser},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted},
            </if>
        </set>
        where ID = #{id}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.SyncOutInRelateEntity">
        update T_SYNC_OUT_IN_RELATE
        set OUT_BUSINESS_NO = #{outBusinessNo},
            BUSINESS_TYPE = #{businessType},
            IN_BUSINESS_NO = #{inBusinessNo},
            SOURCE_ERP = #{sourceErp},
            REQUEST_CONTENT = #{requestContent},
            PROCESS_STATUS = #{processStatus},
            CREATE_TIME = #{createTime},
            UPDATE_TIME = #{updateTime},
            CREATE_USER = #{createUser},
            UPDATE_USER = #{updateUser},
            IS_DELETED = #{isDeleted}
        where ID = #{id}
    </update>

    <select id="selectByProcessStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_OUT_IN_RELATE
        where PROCESS_STATUS = #{processStatus}
        and IS_DELETED = 0
    </select>

    <select id="selectByOutBusinessNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_OUT_IN_RELATE
        where OUT_BUSINESS_NO = #{outBusinessNo}
        and IS_DELETED = 0
    </select>

    <select id="selectByInBusinessNo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_SYNC_OUT_IN_RELATE
        where IN_BUSINESS_NO = #{inBusinessNo}
        and IS_DELETED = 0
    </select>
</mapper> 