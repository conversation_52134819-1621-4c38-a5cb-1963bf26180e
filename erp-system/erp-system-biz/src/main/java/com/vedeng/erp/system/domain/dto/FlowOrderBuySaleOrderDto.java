package com.vedeng.erp.system.domain.dto;

import lombok.Data;

import java.util.List;

@Data
public class FlowOrderBuySaleOrderDto {

    /**
     * 1
     */
    private Integer nodeLevel;

    /**
     * 0-1
     */
    private String buyOrderNode;

    private String buyOrderNo;

    private Integer buyOrderId;

    private String buyOrderPayStatus;

    private String buyOrderInStatus;

    private String buyOrderInvoiceStatus;

    private String buyOrderInvoiceInfo;

    private List<FlowInvoiceDto> buyOrderInvoiceDto;

    /**
     * 1-0
     */
    private String saleOrderNode;

    private String saleOrderNo;

    private Integer saleOrderId;

    private String saleOrderReceiveStatus;

    private String saleOrderOutStatus;

    private String saleOrderInvoiceStatus;

    private String saleOrderInvoiceInfo;

    private List<FlowInvoiceDto> saleOrderInvoiceDto;

    /**
     * 合同
     */
    private String contractUrl;

    /**
     * 是否需要上传合同（仅在有采购信息的行显示，且最大节点交易商不是贝登子公司时为true）
     */
    private Boolean needUploadContract;
}
