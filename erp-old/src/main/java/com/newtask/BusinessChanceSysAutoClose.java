package com.newtask;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.newtask.model.BqoSysAutoCloseDto;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.service.QuoteService;
import com.vedeng.system.service.UserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.joda.time.DateTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date created in 2021/3/19 9:35
 * 系统自动关闭商机、报价
 */
@JobHandler("BusinessChanceSysAutoClose")
@Component
public class BusinessChanceSysAutoClose extends AbstractJobHandler {

    @Resource
    private QuoteService quoteService;

    @Resource
    private UserService userService;

    @Resource
    private BussinessChanceMapper bussinessChanceMapper;

    @Resource
    private QuoteorderMapper quoteorderMapper;

    private final static Logger LOGGER = LoggerFactory.getLogger(BusinessChanceSysAutoClose.class);

    final private static String QUOTEORDER = "报价单";

    final private static String BUSSINESS_CHANCE = "商机";


    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        try {
            sysCloseOrder(QUOTEORDER);
            sysCloseOrder(BUSSINESS_CHANCE);
        } catch (Exception e) {
            XxlJobLogger.log("系统自动关闭商机、报价异常：", e);
            return FAIL;
        }
        return SUCCESS;
    }


    /**
     * 系统自动关闭商机、报价
     *
     * @param closeType
     */
    private void sysCloseOrder(String closeType) {

        int start = 0, limit = 100;

        List<BqoSysAutoCloseDto> list = new LinkedList<>();

        Map<Integer, Integer> belongPlatformOfAllSales = userService.getBelongPlatformOfAllSales(1);

        if (QUOTEORDER.equals(closeType)) {
            while (true) {
                List<BqoSysAutoCloseDto> quoteCommList = quoteorderMapper.getQuoteCommList(start * limit, limit);
                if (Objects.nonNull(quoteCommList)) {
                    list.addAll(quoteCommList);
                }
                if (CollectionUtils.isEmpty(quoteCommList)) {
                    break;
                }
                start++;
            }
        }

        if (BUSSINESS_CHANCE.equals(closeType)) {
            while (true) {
                LinkedList<BqoSysAutoCloseDto> bussCommList = bussinessChanceMapper.getBussCommList(start * limit, limit);
                if (Objects.nonNull(bussCommList)) {
                    list.addAll(bussCommList);
                }
                if (CollectionUtils.isEmpty(bussCommList)) {
                    break;
                }
                start++;
            }
        }

        if (CollectionUtils.isNotEmpty(list)) {
            List<BqoSysAutoCloseDto> bcSysAutoCloseDtoList = list.parallelStream().filter(bqoSysAutoCloseDto -> {
                LinkedList<Long> objects = Lists.newLinkedList(Arrays.asList(
                        ObjectUtils.defaultIfNull(bqoSysAutoCloseDto.getOrderCreateTime(), Long.MIN_VALUE),
                        ObjectUtils.defaultIfNull(bqoSysAutoCloseDto.getComRecordCreateTime(), Long.MIN_VALUE),
                        ObjectUtils.defaultIfNull(bqoSysAutoCloseDto.getComRecordNextConTime(), Long.MIN_VALUE)
                ));
                objects.sort(Long::compareTo);
                Long maxMills = objects.getLast();
                Integer platform = belongPlatformOfAllSales.get(bqoSysAutoCloseDto.getUserId());
                if (Objects.isNull(platform)){
                    platform = 0;
                }
                return platform != 3 && DateTime.now().minusDays(30).isAfter(maxMills);
            }).collect(Collectors.toList());


            if (CollectionUtils.isNotEmpty(bcSysAutoCloseDtoList)) {
                //关闭原因（字典项）
                Integer closeReason;
                //关闭备注
                String closeComment;
                int MAX_NUMBER = 100;
                int limitTwo = (bcSysAutoCloseDtoList.size() + MAX_NUMBER - 1) / MAX_NUMBER;
                List<List<BqoSysAutoCloseDto>> collect = Stream.iterate(0, n -> n + 1).limit(limitTwo).parallel()
                        .map(a ->
                                bcSysAutoCloseDtoList.stream().skip(a * MAX_NUMBER).limit(MAX_NUMBER).parallel().collect(Collectors.toList())
                        ).collect(Collectors.toList());

                for (List<BqoSysAutoCloseDto> bqoSysAutoCloseDtos : collect) {
                    if (QUOTEORDER.equals(closeType)) {
                        closeReason = quoteService.getSysOptionDefIdBypt(1600, "SYS_AUTO_CLOSE_TYPE_1");
                        closeComment = "逾期未处理，系统自动关闭";
                        quoteorderMapper.batchCloseQuoteOrder(bqoSysAutoCloseDtos, closeReason, closeComment);
                        LOGGER.info("报价逾期未处理，系统自动关闭" + JSON.toJSONString(bqoSysAutoCloseDtos));
                        XxlJobLogger.log("报价逾期未处理，系统自动关闭" + JSON.toJSONString(bqoSysAutoCloseDtos));
                        List<BqoSysAutoCloseDto> bussChances =
                                bqoSysAutoCloseDtos.stream().filter(BqoSysAutoCloseDto -> BqoSysAutoCloseDto.getBussinessChanceId() != null).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(bussChances)) {
                            closeReason = quoteService.getSysOptionDefIdBypt(395, "SYS_AUTO_CLOSE_TYPE_2");
                            closeComment = "报价单关闭后，自动关闭商机";
                            bussinessChanceMapper.batchCloseBussChance(bussChances, closeReason, closeComment);
                            LOGGER.info("报价单关闭后，自动关闭商机" + JSON.toJSONString(bqoSysAutoCloseDtos));
                            XxlJobLogger.log("报价单关闭后，自动关闭商机" + JSON.toJSONString(bqoSysAutoCloseDtos));
                        }
                    }
                    if (BUSSINESS_CHANCE.equals(closeType)) {
                        closeReason = quoteService.getSysOptionDefIdBypt(395, "SYS_AUTO_CLOSE_TYPE_1");
                        closeComment = "逾期未处理，系统自动关闭";
                        bussinessChanceMapper.batchCloseBussChance(bqoSysAutoCloseDtos, closeReason, closeComment);
                        LOGGER.info("商机逾期未处理，系统自动关闭" + JSON.toJSONString(bqoSysAutoCloseDtos));
                        XxlJobLogger.log("商机逾期未处理，系统自动关闭" + JSON.toJSONString(bqoSysAutoCloseDtos));
                    }

                }

            }
        }
    }
}
