<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="供应链工作台" scope="application" />
<%@ include file="../common/common.jsp"%>
<div class="content mt10 ">
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                交易合规待办项
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th class="wid9">待办事项名称</th>
                <th class="wid8">待办事项清单</th>
                <th class="wid9">等候的订单数</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>商品信息待完善</td>
                <td title="商品信息完善" id="riskCheckSkuTodoListCount" redirect="${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[0]}">0</td>
                <td id="riskCheckSkuTodoListCountGroupByOrder">0</td>
            </tr>
            <tr>
                <td>供应商信息待完善</td>
                <td title="供应商信息完善" id="riskCheckTraderSupplyTodoListCount" redirect="${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[1]}">0</td>
                <td id="riskCheckTraderSupplyTodoListCountGroupByOrder">0</td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts" id="maintainSkuDiv">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                商品分级分档信息完善待办项
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th class="wid9">待办事项</th>
                <th class="wid8">A级</th>
                <th class="wid9">B级</th>
                <th class="wid11">C级</th>
                <th class="wid9">D级</th>
            </tr>
            </thead>
            <tbody>
            <tr class="maintain-data" id="maintainSkuAndSpuCountGroupByGrade"
                redirect="/goods/vgoods/list.do?buzTypeFromTodoList=9&goodsLevelFromTodoList=">
                <td>商品信息完善</td>
                <td class="1">0</td>
                <td class="2">0</td>
                <td class="3">0</td>
                <td class="4">0</td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                商品推送待办事项
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th class="wid9">待办事项</th>
                <th class="wid8">A级</th>
                <th class="wid9">B级</th>
                <th class="wid11">C级</th>
                <th class="wid9">D级</th>
            </tr>
            </thead>
            <tbody>
            <tr class="maintain-data" id="maintainDeliveryTimeCountGroupByGrade"
                redirect="${ezDomain}/ezlist/list/list.html?pageId=${ezIdArray[2]}&GOODS_LEVEL_NO=">
                <td title="预计可发货时间维护">预计可发货时间维护</td>
                <td class="1">0</td>
                <td class="2">0</td>
                <td class="3">0</td>
                <td class="4">0</td>
            </tr>
            <tr class="maintain-data" id="maintainPriceCountGroupByGrade" redirect="/price/basePriceMaintain/index.do?includeSkuNosStr=">
                <td>商品核价维护</td>
                <td class="1">0</td>
                <td class="2">0</td>
                <td class="3">0</td>
                <td class="4">0</td>
            </tr>
            <tr class="maintain-data" id="maintainAftersalePolicyCountGroupByGrade"
                redirect="/aftersale/serviceStandard/index.do?buzTypeFromTodoList=12&goodsLevelFromTodoList=">
                <td>贝登售后政策维护</td>
                <td class="1">0</td>
                <td class="2">0</td>
                <td class="3">0</td>
                <td class="4">0</td>
            </tr>
            <tr class="maintain-data" id="maintainSupplyAftersalePolicyCountGroupByGrade"
                redirect="/aftersale/serviceStandard/index.do?buzTypeFromTodoList=13&goodsLevelFromTodoList=">
                <td>供应商售后政策维护</td>
                <td class="1">0</td>
                <td class="2">0</td>
                <td class="3">0</td>
                <td class="4">0</td>
            </tr>
            <tr class="maintain-data" id="maintainOperationInfoCountGroupByGrade"
                redirect="/goods/vgoods/list.do?buzTypeFromTodoList=16&goodsLevelFromTodoList=">
                <td>运营信息维护</td>
                <td class="1">0</td>
                <td class="2">0</td>
                <td class="3">0</td>
                <td class="4">0</td>
            </tr>
            </tbody>
        </table>
    </div>
</div>


<script>

    getSupplyTodoList();
    $(setInterval(getSupplyTodoList,10000))

    function getSupplyTodoList(){
        $.getJSON('/todolist/supplyChain/statistics.do',function (data){
            renderMaintainData(data.data);
        });
    }

    function renderMaintainData(data){
        if (data.moduleHiddenFlag){
            $("#maintainSkuDiv").hide();
        }

        if (data.riskCheckSkuTodoListCount > 0){
            let innerHtml = "<a class='addtitle_dyn' href='javascript:void(0);' tabtitle='{\"num\":\"riskCheckSkuTodoListCount\",\"title\":\"商品信息完善\",\"link\":\""
                + $("#riskCheckSkuTodoListCount").attr("redirect");
            if (typeof (data.subordinateList) != 'undefined' && data.subordinateList != null){
                innerHtml += "&ASSIGNMENT_ASSISTANT_ID=" + data.subordinateList + "&ASSIGNMENT_MANAGER_ID=" + data.subordinateList;
            }
            innerHtml += "\"}'>" + data.riskCheckSkuTodoListCount + "</a>";
            $("#riskCheckSkuTodoListCount").html(innerHtml);
        }
        if (data.riskCheckTraderSupplyTodoListCount > 0){
            let innerHtml =
                "<a class='addtitle_dyn' href='javascript:void(0);' tabtitle='{\"num\":\"riskCheckTraderSupplyTodoListCount\",\"title\":\"供应商信息待完善\",\"link\":\""
                + $("#riskCheckTraderSupplyTodoListCount").attr("redirect");
            if (typeof (data.subordinateList) != 'undefined' && data.subordinateList != null){
                innerHtml += "&USER_LIST=" + data.subordinateList;
            }
            innerHtml += "\"}'>" + data.riskCheckTraderSupplyTodoListCount + "</a>";
            $("#riskCheckTraderSupplyTodoListCount").html(innerHtml);
        }
        $("#riskCheckSkuTodoListCountGroupByOrder").text(data.riskCheckSkuTodoListCountGroupByOrder);
        $("#riskCheckTraderSupplyTodoListCountGroupByOrder").text(data.riskCheckTraderSupplyTodoListCountGroupByOrder);

        $(".maintain-data").each(function (){
            let id = this.id;
            let map = eval("data." + id);
            let redirect = $(this).attr('redirect');
            $(this).children('td').each(function (){
                let classValue = $(this).attr('class');
                if (typeof(classValue) != "undefined"){
                    let value = eval("map[" + classValue + "]");
                    if (typeof(value) != "undefined"){
                        if (value == 0){
                            $(this).text(0);
                        } else {
                            let innerHtml = "<a class='addtitle_dyn' href='javascript:void(0);' tabtitle='{\"num\":\"" + id;
                            if (id == 'maintainPriceCountGroupByGrade'){
                                let skuList = data.maintainPriceSkuList[classValue];
                                innerHtml += "\",\"link\":\"" + redirect + skuList;
                                if (typeof (data.subordinateList) != 'undefined' && data.subordinateList != null){
                                   innerHtml += "&subordinateList=" + data.subordinateList
                                }
                            }else if (id == 'maintainDeliveryTimeCountGroupByGrade'){
                                innerHtml += "\",\"title\":\"预计可发货时间\",\"link\":\"" + redirect + classValue;
                                if (typeof (data.subordinateList) != 'undefined' && data.subordinateList != null){
                                    innerHtml += "&USER_LIST=" + data.subordinateList
                                }
                            }
                            else {
                                innerHtml += "\",\"link\":\"" + redirect + classValue;
                                if (typeof (data.subordinateList) != 'undefined' && data.subordinateList != null){
                                    innerHtml += "&subordinateList=" + data.subordinateList
                                }
                            }
                            this.innerHTML = innerHtml + "\"}'>" + value + "</a>";
                        }
                    }
                }
            })
        })
    }
</script>
<%@ include file="../common/footer.jsp"%>