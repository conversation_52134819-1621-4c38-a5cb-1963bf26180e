package com.vedeng.erp.finance.service;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.dto.PayApplyReqDto;
import com.vedeng.erp.finance.dto.PayApplyRespDto;
import com.vedeng.erp.finance.mapper.PayApplyDefineMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NewPayApplyServiceImpl implements NewPayApplyService {

    @Autowired
    private PayApplyDefineMapper payApplyDefineMapper;


    @Override
    public PageInfo<PayApplyRespDto> pageList(PageParam<PayApplyReqDto> payApplyReqDto) {
        PageInfo<PayApplyRespDto> objectPageInfo = PageHelper.startPage(payApplyReqDto.getPageNum(), payApplyReqDto.getPageSize()).doSelectPageInfo(() -> payApplyDefineMapper.pageList(payApplyReqDto.getParam()));
        return objectPageInfo;
    }

    @Override
    public void updateOffline(Integer payApplyId, Integer offline) {
        payApplyDefineMapper.updateOffline(payApplyId,offline);
    }
}
