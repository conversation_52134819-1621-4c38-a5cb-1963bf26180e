package com.vedeng.erp.finance.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationItemDto;
import com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity;
import com.vedeng.erp.finance.dto.InvoiceRedConfirmationApiDto;
import com.vedeng.erp.finance.dto.InvoiceRedConfirmationItemApiDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface InvoiceRedConfirmationItemApiConvertor {

    InvoiceRedConfirmationItemDto to(InvoiceRedConfirmationItemApiDto dto);

}
