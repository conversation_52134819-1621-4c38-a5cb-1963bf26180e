<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="编辑财务信息" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<div class="content">
    <div class="parts">
        <div class="title-container">
            <div class="table-title ">
                基本信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <input type="hidden" id="bussinessChanceId" value="${bussinessChanceVo.bussinessChanceId}">
                <td class="table-smaller">商机编号</td>
                <td>${bussinessChanceVo.oldChanceNo}</td>
                <td>商机状态</td>
                <td>
                        	<span class="font-red">
                        		<c:if test="${bussinessChanceVo.status eq 0}">未处理</c:if>
                        		<c:if test="${bussinessChanceVo.status eq 1}">报价中</c:if>
                        		<c:if test="${bussinessChanceVo.status eq 2}">已报价</c:if>
                        		<c:if test="${bussinessChanceVo.status eq 3}">已订单</c:if>
                        		<c:if test="${bussinessChanceVo.status eq 4}">已关闭</c:if>
                        	</span>
                </td>

            </tr>
            <tr>
                <td class="table-smaller">商机类型</td>
                <td>${bussinessChanceVo.typeName}</td>
                <td>归属销售</td>
                <td>${bussinessChanceVo.salerName}</td>
            </tr>
            <tr>
                <td>创建者</td>
                <td>${bussinessChanceVo.creatorName}</td>
                <td>创建时间</td>
                <td><date:date value ="${bussinessChanceVo.addTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
            </tr>
            <tr>
                <td>商机时间</td>
                <td><date:date value ="${bussinessChanceVo.receiveTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                <td>分配时间</td>
                <td><date:date value ="${bussinessChanceVo.assignTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
            </tr>


            <tr>
                <td>商机来源</td>
                <td>${bussinessChanceVo.sourceName}</td>
                <td>询价方式</td>
                <td>${bussinessChanceVo.communicationName}</td>
            </tr>
            <tr>
                <td>咨询入口</td>
                <td>${bussinessChanceVo.entranceName}</td>
                <td>功能</td>
                <td>${bussinessChanceVo.functionName}</td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td>${bussinessChanceVo.traderName}</td>
                <td>地区</td>
                <td>${bussinessChanceVo.areas}</td>
            </tr>
            <tr>
                <td>联系人</td>
                <td>${bussinessChanceVo.traderContactName}</td>
                <td>手机号</td>
                <td>
                    <c:if test="${not empty bussinessChanceVo.mobile}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${bussinessChanceVo.mobile}',0,1,1,${bussinessChanceVo.bussinessChanceId},0);"></i>
                    </c:if>
                    ${bussinessChanceVo.mobile}
                </td>
            </tr>
            <tr>
                <td>电话</td>
                <td>
                    <c:if test="${not empty bussinessChanceVo.telephone}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${bussinessChanceVo.telephone}',0,1,1,${bussinessChanceVo.bussinessChanceId},0);"></i>
                    </c:if>
                    ${bussinessChanceVo.telephone}
                </td>
                <td>其他联系方式</td>
                <td>${bussinessChanceVo.otherContact}</td>
            </tr>
            <tr>
                <td>产品分类</td>
                <td>${bussinessChanceVo.goodsCategoryName}</td>
                <td>产品品牌</td>
                <td>${bussinessChanceVo.goodsBrand}</td>
            </tr>
            <tr>
                <td>产品名称</td>
                <td>${bussinessChanceVo.goodsName}</td>
                <td>附件</td>
                <td>
                    <c:if test="${!empty bussinessChanceVo.attachmentUri}">
                        <a href="http://${bussinessChanceVo.attachmentDomain}${bussinessChanceVo.attachmentUri}" target="_blank">查看</a>
                    </c:if>
                    <c:if test="${empty bussinessChanceVo.attachmentUri}">

                    </c:if>
                </td>
            </tr>

            <tr>
                <td>询价产品</td>
                <td colspan="3" class="overflow-hidden">
                    ${bussinessChanceVo.content}<br/>
                    <%--   									<span class="edit-user addtitle mt8 mb5" --%>
                    <%--                        		tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"${pageContext.request.contextPath}/order/saleorder/toSaleJHSearchPage.do","title":"检索产品"}'>检索产品</span>--%>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>
