html, body {
    font-size: 13px;
    margin: 0;
    background: #f5f7fa;
    color: #333;

    * {
        box-sizing: border-box;
    }
}

.list-wrap {
    min-width: 960px;
    padding-bottom: 20px;

    .search-wrap {
        padding: 20px;
        background: #fff;
        margin-bottom: 20px;

        .search-filter-list {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: -10px;
    
            .search-item {
                width: 25%;
                display: flex;
                align-items: center;
                margin-bottom: 10px;
    
                .search-label {
                    width: 100px;
                    text-align: right;
                    margin-right: 5px;
                    color: #666;
                }
    
                .search-cnt {
                    flex: 1;

                    .el-date-editor--daterange.el-input__inner {
                        width: 100%;

                        .el-range-separator {
                            width: 18px;
                        }
                    }

                    .el-select {
                        width: 100%;
                    }
                }
            }
        }
    }

    .search-filter-btns {
        margin-top: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .list-container {
        padding: 0 20px;

        .tab-title-txt {
            position: relative;

            .el-badge {
                line-height: 1;
                vertical-align: top;
                position: absolute;
                top: 10px;
                right: -5px;
                transform: translateX(50%);
            }

            .el-badge__content {
                border: 0;
            }
        }

        .list-table-wrap {
            margin-top: -1px;

            .table-th th {
                background: #f5f7fa;
            }

            .el-table__empty-text {
                padding: 100px 0;
            }
        }

        .text-link {
            color: #409EFF;
            cursor: pointer;

            &:hover {
                color: #f60;
            }
        }

        .pagination-wrap {
            padding: 10px 20px;
            display: flex;
            justify-content: flex-end;
            background: #fff;
        }
    }
}

.global-loading {
    font-size: 32px;

    .el-icon-loading {
        color: #fff;
    }
}