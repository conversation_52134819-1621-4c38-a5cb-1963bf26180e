package com.newtask.quoteorder.strategy;

import com.newtask.quoteorder.exception.QuotedAlarmUpgradedException;
import com.newtask.quoteorder.model.QuotedAlarmRecord;
import com.vedeng.order.enums.QuotedAlarmLevelEnum;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import com.vedeng.order.model.Quoteorder;
import lombok.Getter;

import java.time.Duration;
import java.util.*;

/**
 * 销售端触发报价预警策略
 *
 * <AUTHOR> [<EMAIL>]
 */
public class SalesmanStrategy extends AbstractQuotedAlarmStrategy implements QuotedAlarmStrategy {

    /**
     * Time Intervals
     */
    private final static Duration ONE_DAY = Duration.ofDays(1);
    private final static Duration THREE_DAYS = Duration.ofDays(3);
    private final static Duration SIX_DAYS = Duration.ofDays(6);
    private final static Duration SIXTEEN_DAYS = Duration.ofDays(16);
    private final static Duration TWENTY_SIX_DAYS = Duration.ofDays(26);
    private final static Duration FIFTY_SIX_DAYS = Duration.ofDays(56);
    private final static Duration EIGHTY_SIX_DAYS = Duration.ofDays(86);
    private final static Duration NINETY_SIX_DAYS = Duration.ofDays(90);


    /**
     * 采购时时间类型
     */
    private final static Integer NOT_NEED = -1;
    private final static Integer LESS_FIVE_DAYS = 411;
    private final static Integer LESS_TEN_DAYS = 412;
    private final static Integer LESS_TWENTY_DAYS = 413;
    private final static Integer ONE_MONTH = 414;
    private final static Integer TWO_MONTHS = 415;
    private final static Integer THREE_MONTHS = 416;
    private final static Integer MORE_THREE_MONTHS = 417;


    @Override
    public void upgradeForNextLevel(QuotedAlarmRecord quotedAlarmRecord, Integer purchaseTimeType) {
        //设置下一次更新时间点
        Integer upgradedLevel = getNextLevel(quotedAlarmRecord.getCurrentLevel());

        SalesmanUpgradeRule salesmanUpgradeRule = SalesmanUpgradeRule.getUpgradeRule(upgradedLevel, purchaseTimeType);

        if (salesmanUpgradeRule == null) {
            throw new QuotedAlarmUpgradedException("销售预警模式模式下，未匹配到level:" + upgradedLevel + "的升级规则");
        }

        long nextTriggerTime = getNextTriggerTime(getBeginTimeForNextTrigger(quotedAlarmRecord), salesmanUpgradeRule.getInternal(), salesmanUpgradeRule.getWorkingDayEnable());

        quotedAlarmRecord.setCurrentLevel(upgradedLevel);
        quotedAlarmRecord.setTriggerTime(nextTriggerTime);
    }

    @Override
    protected QuotedAlarmModeEnum getMode() {
        return QuotedAlarmModeEnum.SALESMAN_MODE;
    }


    /**
     * 销售端预警升级规则
     */
    @Getter
    enum SalesmanUpgradeRule {

        //~===========================================================    二级预警，在首次回复时触发

        RULE_ONE(NOT_NEED, THREE_DAYS, Boolean.TRUE),


        //~===========================================================    一级预警需要考虑采购时间

        RULE_TWO(LESS_FIVE_DAYS, ONE_DAY, Boolean.TRUE),
        RULE_THREE(LESS_TEN_DAYS, SIX_DAYS, Boolean.TRUE),
        RULE_FOUR(LESS_TWENTY_DAYS, SIXTEEN_DAYS, Boolean.TRUE),
        RULE_FIVE(ONE_MONTH, TWENTY_SIX_DAYS, Boolean.FALSE),
        RULE_SIX(TWO_MONTHS, FIFTY_SIX_DAYS, Boolean.FALSE),
        RULE_SEVEN(THREE_MONTHS, EIGHTY_SIX_DAYS, Boolean.FALSE),
        RULE_EIGHT(MORE_THREE_MONTHS, NINETY_SIX_DAYS, Boolean.FALSE),
        ;

        private Integer purchasingTimeType;
        private Duration internal;
        private Boolean workingDayEnable;


        private final static Map<Integer, SalesmanUpgradeRule> UPGRADE_RULE_MAP = new LinkedHashMap<>(SalesmanUpgradeRule.values().length);

        static {
            UPGRADE_RULE_MAP.put(NOT_NEED, SalesmanUpgradeRule.RULE_ONE);
            UPGRADE_RULE_MAP.put(LESS_FIVE_DAYS, SalesmanUpgradeRule.RULE_TWO);
            UPGRADE_RULE_MAP.put(LESS_TEN_DAYS, SalesmanUpgradeRule.RULE_THREE);
            UPGRADE_RULE_MAP.put(LESS_TWENTY_DAYS, SalesmanUpgradeRule.RULE_FOUR);
            UPGRADE_RULE_MAP.put(ONE_MONTH, SalesmanUpgradeRule.RULE_FIVE);
            UPGRADE_RULE_MAP.put(TWO_MONTHS, SalesmanUpgradeRule.RULE_SIX);
            UPGRADE_RULE_MAP.put(THREE_MONTHS, SalesmanUpgradeRule.RULE_SEVEN);
            UPGRADE_RULE_MAP.put(MORE_THREE_MONTHS, SalesmanUpgradeRule.RULE_EIGHT);
        }


        SalesmanUpgradeRule(Integer purchasingTimeType, Duration internal, boolean workingDayEnable) {
            this.purchasingTimeType = purchasingTimeType;
            this.internal = internal;
            this.workingDayEnable = workingDayEnable;
        }


        public static SalesmanUpgradeRule getUpgradeRule(Integer nextLevel, Integer purchasingTimeType) {
            SalesmanUpgradeRule salesmanUpgradeRule = null;

            boolean needPurchasingTime = true;
            if (nextLevel.equals(QuotedAlarmLevelEnum.LEVEL_TWO.getLevel())) {
                needPurchasingTime = false;
            }

            //二级预警不需要考虑采购时间
            if (!needPurchasingTime) {
                return UPGRADE_RULE_MAP.get(NOT_NEED);
            }

            for (Map.Entry<Integer, SalesmanUpgradeRule> entry : UPGRADE_RULE_MAP.entrySet()) {
                if (Objects.equals(purchasingTimeType, entry.getKey())) {
                    salesmanUpgradeRule = entry.getValue();
                }
            }

            return salesmanUpgradeRule;
        }
    }

}
