<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>盘亏出库列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="inventoryOutList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>订货号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="sku_no" alias="b" empty_show="-">
            </div>
        </div>

        <div class="layui-inline ">
            <label>产品名称</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="SKU_NAME" alias="sku" empty_show="-">
            </div>
        </div>

        <div class="layui-inline ">
            <label>品牌</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="BRAND_NAME" alias="BR" empty_show="-">
            </div>
        </div>

        <div class="layui-inline ">
            <label>型号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="MODEL" alias="sku" empty_show="-">
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="verify_status" alias="a" jdbctype="INTEGER" placeholder="默认全部"
                        data='[{"K":"1","V":"审核中"},{"K":"2","V":"审核通过"},{"K":"3","V":"审核不通过"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">出库状态</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="out_status"  alias="b" jdbctype="INTEGER" placeholder="默认全部"
                        data='[{"K":"0","V":"未出库"},{"K":"1","V":"部分出库"},{"K":"2","V":"全部出库"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class="layui-inline ">
            <label>出库单号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="order_no" alias="a" empty_show="-">
            </div>
        </div>

        <div class="layui-inline ">
            <label>创建日期</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="VARCHER" type="search"
                        placeholder="请输入" name="add_time" alias="a" oper="BETWEEN"></object>
            </div>
        </div>


    </form>
    <hr class="layui-border-blue">

    <div class="btn-group bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/wms/inventoryOut/inventoryOutAdd.do" opentype="PARENT"  windowname="新增">+
            新增盘亏出库单
        </button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th name="order_no" body="td-spanlink" url="/wms/inventoryOut/details.do?inventoryOutOrderId=${WMSOUTORDERID}" windowname="盘亏出库单详情页" opentype="PARENT">盘亏出库单号</th>

            <th name="sku_no">订货号</th>
            <th name="SKU_NAME">产品名称</th>
            <th name="BRAND_NAME">品牌</th>
            <th name="MODEL">型号</th>
            <th name="UNIT_NAME">单位</th>
            <th name="output_num">出库数量</th>
            <th name="logicalName">逻辑仓</th>
            <th name="addTime">创建日期</th>
            <th name="already_output_num">已出库数量</th>
            <th name="outStatus">出库状态</th>
            <th name="verifyStatus">审核状态</th>

            <!--<th type="rowbutton" id="rowbutton">-->
            <!--    <button class="layui-btn list-row-button" type="single" opentype="MODEL"-->
            <!--            url="/bank/edit.do?bankId=${BANK_ID}" windowname="编辑" area="600px,400px"-->
            <!--            name="update">编辑-->
            <!--    </button>-->
            <!--    <button class="layui-btn list-row-button" type="single" opentype="CONFIRM_AJAX"-->
            <!--            url="/bank/delete.do?bankId=${BANK_ID}" windowname="删除" name="update">删除-->
            <!--    </button>-->
            <!--</th>-->
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by a.id desc" groupby="">
                    SELECT
                        a.id WMSOUTORDERID,
                        a.order_no,
                        b.sku_no,
                        sku.SKU_NAME,
                        BR.BRAND_NAME,
                        sku.MODEL,
                        UN.UNIT_NAME,
                        b.output_num,
                        sys.TITLE logicalName,
                        STR_TO_DATE( a.add_time, '%Y-%m-%d' ) addTime,
                        b.already_output_num,
                    CASE
                            b.out_status
                            WHEN 0 THEN
                            '未出库'
                            WHEN 1 THEN
                            '部分出库'
                            WHEN 2 THEN
                            '全部出库' ELSE ''
                    END outStatus,
                    CASE
                            a.verify_status
                            WHEN 0 THEN
                            '待审核'
                            WHEN 1 THEN
                            '审核中'
                            WHEN 2 THEN
                            '审核通过'
                            WHEN 3 THEN
                            '审核不通过'
                            WHEN 4 THEN
                            '已关闭'
                            ELSE ''
                    END verifyStatus
                    FROM
                        T_WMS_OUTPUT_ORDER a
                        LEFT JOIN T_WMS_OUTPUT_ORDER_GOODS b ON a.id = b.wms_output_order_id
                        LEFT JOIN V_CORE_SKU sku ON b.sku_no = sku.SKU_NO
                        LEFT JOIN T_UNIT UN ON UN.UNIT_ID = sku.BASE_UNIT_ID
                        LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
                        LEFT JOIN T_BRAND BR ON BR.BRAND_ID = spu.BRAND_ID
                        LEFT JOIN T_SYS_OPTION_DEFINITION sys ON sys.SYS_OPTION_DEFINITION_ID = b.LOGICAL_WAREHOUSE_ID
                    WHERE
                        a.type = 4
                        AND b.is_delete = 0
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>