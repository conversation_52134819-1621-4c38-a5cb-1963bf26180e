<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="删除关联商品" scope="application"/>
<%@ include file="../../common/common.jsp" %>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/supplier/add_related_sku.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic">
    <input type="hidden" name="userId" value="${userId}">
    <div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th class="table-smallest8">
                    <input type="checkbox" name="checkAllSku" onclick="selectAll(this)"/>
                </th>
                <th class="wid5">订货号</th>
                <th class="wid13">产品名称</th>
                <th class="wid8">品牌</th>
                <th class="wid8">规格/型号</th>
            </tr>
            </thead>
            <tbody class="employeestate">
            <c:if test="${not empty skuList}">
                <c:forEach items="${skuList}" var="sku" varStatus="status">
                    <tr>
                        <td>
                            <input type="checkbox"  name="checkOne" value="${sku.skuSupplyAuthDetailId}">
                        </td>
                        <td>${sku.skuNo}</td>
                        <td>${sku.skuName}</td>
                        <td>${sku.brandName}</td>
                        <td><c:choose>
                            <c:when test="${sku.spuType eq 316 or sku.spuType eq 1008}">${sku.model}</c:when>
                            <c:otherwise>${sku.spec}</c:otherwise>
                        </c:choose></td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <c:if test="${empty skuList}">
            <!-- 查询无结果弹出 -->
            <div class="noresult">暂无关联的商品信息！</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>

<div class="add-tijiao" style="margin-top: 50px">
    <button type="button" class="bg-light-blue" onclick="saveDeleteRelatedSku()">提交</button>
</div>
