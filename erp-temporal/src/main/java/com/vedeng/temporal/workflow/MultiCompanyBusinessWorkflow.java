package com.vedeng.temporal.workflow;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import io.temporal.workflow.WorkflowInterface;
import io.temporal.workflow.WorkflowMethod;

/**
 * 多公司业务流程工作流接口
 * 定义多公司间业务流程自动化的工作流程
 *
 * <AUTHOR>
 * @version 2.0
 * @since 2025-06-20
 */
@WorkflowInterface
public interface MultiCompanyBusinessWorkflow {

    /**
     * 执行多公司业务流程（并行执行模式）
     *
     * 并行执行三个业务流程：
     * 1. 采购-销售-库存流程：处理业务单据流转（先销售单，再采购单）
     * 2. 发票录入流程：处理发票录入，内部检查依赖状态
     * 3. 付款传递流程：处理付款流转，内部检查依赖状态
     *
     * 执行特点：
     * - 三个流程同时开始执行
     * - 发票和付款流程会自动等待必要的前置条件（如销售单、采购单生成）
     * - 三个流程都成功才算整体成功
     *
     * @param request 业务请求对象
     * @return 流程执行结果
     */
    @WorkflowMethod
    CompanyBusinessResponse executeMultiCompanyBusiness(CompanyBusinessRequest request);


}
