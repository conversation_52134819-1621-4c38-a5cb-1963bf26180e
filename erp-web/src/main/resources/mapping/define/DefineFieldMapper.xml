<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.define.dao.DefineFieldMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.define.model.DefineField" >
    <!--          -->
    <id column="DEFINE_ID" property="defineId" jdbcType="INTEGER" />
    <result column="BUSSINESS" property="bussiness" jdbcType="VARCHAR" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="FIELD" property="field" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    DEFINE_ID, BUSSINESS, USER_ID, FIELD, ADD_TIME, CREATOR
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from E_DEFINE_FIELD
    where DEFINE_ID = #{defineId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from E_DEFINE_FIELD
    where DEFINE_ID = #{defineId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.define.model.DefineField" >
    <!--          -->
    insert into E_DEFINE_FIELD (DEFINE_ID, BUSSINESS, USER_ID, 
      FIELD, ADD_TIME, CREATOR
      )
    values (#{defineId,jdbcType=INTEGER}, #{bussiness,jdbcType=VARCHAR}, #{userId,jdbcType=INTEGER}, 
      #{field,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.define.model.DefineField" >
    <!--          -->
    insert into E_DEFINE_FIELD
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="defineId != null" >
        DEFINE_ID,
      </if>
      <if test="bussiness != null" >
        BUSSINESS,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="field != null" >
        FIELD,
      </if>
      <if test="isShow != null" >
        IS_SHOW,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="defineId != null" >
        #{defineId,jdbcType=INTEGER},
      </if>
      <if test="bussiness != null" >
        #{bussiness,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="field != null" >
        #{field,jdbcType=VARCHAR},
      </if>
      <if test="isShow != null" >
        #{isShow,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.define.model.DefineField" >
    <!--          -->
    update E_DEFINE_FIELD
    <set >
      <if test="bussiness != null" >
        BUSSINESS = #{bussiness,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="field != null" >
        FIELD = #{field,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
    </set>
    where DEFINE_ID = #{defineId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.define.model.DefineField" >
    <!--          -->
    update E_DEFINE_FIELD
    set BUSSINESS = #{bussiness,jdbcType=VARCHAR},
      USER_ID = #{userId,jdbcType=INTEGER},
      FIELD = #{field,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER}
    where DEFINE_ID = #{defineId,jdbcType=INTEGER}
  </update>
  
  <select id="getDefineFeld" parameterType="com.vedeng.define.model.DefineField" resultType="com.vedeng.define.model.DefineField">
  	select
  		DEFINE_ID, BUSSINESS, USER_ID, FIELD, ADD_TIME, CREATOR, IS_SHOW
  	from 
  		E_DEFINE_FIELD
  	where
  		BUSSINESS = #{bussiness,jdbcType=VARCHAR}
  		and
  		USER_ID = #{userId,jdbcType=INTEGER}
  		and
  		FIELD = #{field,jdbcType=VARCHAR}
  	limit 1
  </select>
  
  <delete id="deleteMyDefine" parameterType="com.vedeng.define.model.DefineField" >
  	delete
  	from 
  		E_DEFINE_FIELD
  	where 
  		BUSSINESS = #{bussiness,jdbcType=VARCHAR}
  		and
  		USER_ID = #{userId,jdbcType=INTEGER}
  		<if test="field != null">
  		and
  		FIELD = #{field,jdbcType=VARCHAR}
  		</if>
  </delete>
  
  <update id="updateMyDefine" parameterType="com.vedeng.define.model.DefineField">
  	update
  		E_DEFINE_FIELD
  	set 
  		IS_SHOW = #{isShow,jdbcType=INTEGER}
  	where
  		BUSSINESS = #{bussiness,jdbcType=VARCHAR}
  		and
  		USER_ID = #{userId,jdbcType=INTEGER}
  		and
  		FIELD = #{field,jdbcType=VARCHAR}
  </update>
</mapper>