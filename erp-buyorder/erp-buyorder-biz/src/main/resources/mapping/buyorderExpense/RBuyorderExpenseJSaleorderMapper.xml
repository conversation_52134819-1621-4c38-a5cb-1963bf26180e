<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorderexpense.mapper.RBuyorderExpenseJSaleorderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_BUYORDER_EXPENSE_J_SALEORDER-->
    <id column="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" jdbcType="INTEGER" property="tRBuyorderExpenseJSaleorderId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId" />
    <result column="BUYORDER_EXPENSE_ITEM_ID" jdbcType="INTEGER" property="buyorderExpenseItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
  </resultMap>
  <resultMap id="ExpenseBuyForSaleDetailMap" type="com.vedeng.erp.buyorder.dto.ExpenseBuyForSaleDetail">
    <result column="SALEORDER_GOODS_ID"  jdbcType="INTEGER" property="saleorderGoodsId"/>
    <result column="BUYORDER_NO" jdbcType="VARCHAR" property="buyorderNo"/>
    <result column="BUYORDER_DEMAND" jdbcType="VARCHAR" property="buyorderDemand"/>
    <result column="BUY_PROCESS_MOD_TIME_STRING" jdbcType="VARCHAR" property="buyProcessModTimeString"/>
    <result column="BUY_DOCK_USER_NAME" jdbcType="VARCHAR" property="buyDockUserName"/>
    <result column="PAYMENT_STATUS" jdbcType="INTEGER" property="buyorderStatus"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    T_R_BUYORDER_EXPENSE_J_SALEORDER_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, SALEORDER_ID, SALEORDER_GOODS_ID, BUYORDER_EXPENSE_ID, BUYORDER_EXPENSE_ITEM_ID, 
    SKU_ID, SKU_NO, NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" keyProperty="tRBuyorderExpenseJSaleorderId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_BUYORDER_EXPENSE_J_SALEORDER (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      SALEORDER_ID, SALEORDER_GOODS_ID, BUYORDER_EXPENSE_ID, 
      BUYORDER_EXPENSE_ITEM_ID, SKU_ID, SKU_NO, 
      NUM)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{saleorderId,jdbcType=INTEGER}, #{saleorderGoodsId,jdbcType=INTEGER}, #{buyorderExpenseId,jdbcType=INTEGER}, 
      #{buyorderExpenseItemId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR}, 
      #{num,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" keyProperty="tRBuyorderExpenseJSaleorderId" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_BUYORDER_EXPENSE_J_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="buyorderExpenseId != null">
        BUYORDER_EXPENSE_ID,
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="num != null">
        NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseId != null">
        #{buyorderExpenseId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseItemId != null">
        #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity">
    <!--@mbg.generated-->
    update T_R_BUYORDER_EXPENSE_J_SALEORDER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseId != null">
        BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
      </if>
      <if test="buyorderExpenseItemId != null">
        BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
    </set>
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity">
    <!--@mbg.generated-->
    update T_R_BUYORDER_EXPENSE_J_SALEORDER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
      BUYORDER_EXPENSE_ITEM_ID = #{buyorderExpenseItemId,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER}
    where T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{tRBuyorderExpenseJSaleorderId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="T_R_BUYORDER_EXPENSE_J_SALEORDER_ID" keyProperty="tRBuyorderExpenseJSaleorderId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_BUYORDER_EXPENSE_J_SALEORDER
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, SALEORDER_ID, 
      SALEORDER_GOODS_ID, BUYORDER_EXPENSE_ID, BUYORDER_EXPENSE_ITEM_ID, SKU_ID, SKU_NO, 
      NUM)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.saleorderId,jdbcType=INTEGER}, #{item.saleorderGoodsId,jdbcType=INTEGER}, 
        #{item.buyorderExpenseId,jdbcType=INTEGER}, #{item.buyorderExpenseItemId,jdbcType=INTEGER}, 
        #{item.skuId,jdbcType=INTEGER}, #{item.skuNo,jdbcType=VARCHAR}, #{item.num,jdbcType=INTEGER}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-01-05-->
  <select id="selectBySaleorderGoodsIdIn" resultMap="BaseResultMap">
    select
    TRBEJS.ADD_TIME, TRBEJS.MOD_TIME, TRBEJS.CREATOR, TRBEJS.UPDATER, TRBEJS.CREATOR_NAME, TRBEJS.UPDATER_NAME, TRBEJS.T_R_BUYORDER_EXPENSE_J_SALEORDER_ID,
    TRBEJS.SALEORDER_ID, TRBEJS.SALEORDER_GOODS_ID, TRBEJS.BUYORDER_EXPENSE_ID, TRBEJS.BUYORDER_EXPENSE_ITEM_ID, TRBEJS.SKU_ID, TRBEJS.SKU_NO, TRBEJS.NUM
    from T_R_BUYORDER_EXPENSE_J_SALEORDER TRBEJS left join T_BUYORDER_EXPENSE TBE on TRBEJS.BUYORDER_EXPENSE_ID =
    TBE.BUYORDER_EXPENSE_ID
    where TRBEJS.SALEORDER_GOODS_ID in
    <foreach item="item" index="index" collection="saleorderGoodsIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    and TBE.STATUS in (1,2)
    order by TRBEJS.BUYORDER_EXPENSE_ID desc;

  </select>

  <select id="getTotalNumBySaleorderGoodsIdAndBuyorderExpenseId" resultMap="BaseResultMap">
    select
    TRBEJS.BUYORDER_EXPENSE_ID, TRBEJS.SALEORDER_GOODS_ID,SUM(TRBEJS.NUM) NUM
    from T_R_BUYORDER_EXPENSE_J_SALEORDER TRBEJS
    where TRBEJS.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
    and TRBEJS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    group by TRBEJS.BUYORDER_EXPENSE_ID, TRBEJS.SALEORDER_GOODS_ID
  </select>

  <select id="getReturnNumByBuyorderExpenseIdAndSaleorderGoodsId" resultMap="BaseResultMap">
    select TEAS.BUYORDER_EXPENSE_ID, TREASJS.SALEORDER_GOODS_ID, SUM(TREASJS.AFTER_SALES_NUM) NUM
    from T_EXPENSE_AFTER_SALES TEAS
    left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEAS.EXPENSE_AFTER_SALES_ID = TEASS.EXPENSE_AFTER_SALES_ID
    left join T_EXPENSE_AFTER_SALES_ITEM TEASI on TEAS.EXPENSE_AFTER_SALES_ID = TEASI.EXPENSE_AFTER_SALES_ID
    left join T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS
    on TEASI.EXPENSE_AFTER_SALES_ITEM_ID = TREASJS.EXPENSE_AFTER_SALES_ITEM_ID
    where TEASS.AFTER_SALES_STATUS = 2
    and TEAS.IS_DELETE = 0
    and TEASI.IS_DELETE = 0
    and TEAS.EXPENSE_AFTER_SALES_TYPE = 4121
    and TEAS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    and TREASJS.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
    group by TEAS.BUYORDER_EXPENSE_ID, TREASJS.SALEORDER_GOODS_ID
  </select>

  <select id="getRelatedDetail" resultType="com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto">
    select
    <include refid="Base_Column_List"/>
    from T_R_BUYORDER_EXPENSE_J_SALEORDER
      where BUYORDER_EXPENSE_ITEM_ID in
      <foreach item="item" collection="buyorderExpenseItemDtoList" open="(" separator="," close=")">
        #{item.buyorderExpenseItemId}
      </foreach>
  </select>

  <update id="updateBuyNum">
    UPDATE T_R_BUYORDER_EXPENSE_J_SALEORDER SET
    MOD_TIME = now(),
    NUM = CASE
    <foreach collection="buyOrderSaleOrderGoodsDetailDtoList" item="item" index="index">
      WHEN T_R_BUYORDER_EXPENSE_J_SALEORDER_ID = #{item.buyorderExpenseJSaleorderId} THEN #{item.buyNum}
    </foreach>
    END
    WHERE T_R_BUYORDER_EXPENSE_J_SALEORDER_ID IN
    <foreach collection="buyOrderSaleOrderGoodsDetailDtoList" index="index" item="item" open="(" separator="," close=")">
      #{item.buyorderExpenseJSaleorderId}
    </foreach>
  </update>

  <select id="historyBuyNum" resultType="com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto">
    select COALESCE(sum(a.Num), 0) Num,
    SALEORDER_GOODS_ID
    from T_R_BUYORDER_EXPENSE_J_SALEORDER a
    left join T_BUYORDER_EXPENSE b
    on a.BUYORDER_EXPENSE_ID = b.BUYORDER_EXPENSE_ID
    where SALEORDER_GOODS_ID in
    <foreach item="item" collection="buyOrderSaleOrderGoodsDetailDtoList" open="(" separator="," close=")">
    #{item.saleorderGoodsId}
    </foreach>
    and b.STATUS in (0,1,2)
    group by SALEORDER_GOODS_ID
  </select>
  <select id="expenseBuyorderAfterSaleNum"
          resultType="com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto">
    select COALESCE(sum(if(b.IS_AUTO = 0,a.AFTER_SALES_NUM,0)), 0) Num,
    a.SALEORDER_GOODS_ID
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER a left join T_EXPENSE_AFTER_SALES b on a.EXPENSE_AFTER_SALES_ID = b.EXPENSE_AFTER_SALES_ID
    left join T_EXPENSE_AFTER_SALES_STATUS c on a.EXPENSE_AFTER_SALES_ID = c.EXPENSE_AFTER_SALES_ID
    where SALEORDER_GOODS_ID in
    <foreach item="item" collection="buyOrderSaleOrderGoodsDetailDtoList" open="(" separator="," close=")">
      #{item.saleorderGoodsId}
    </foreach>
    and c.AFTER_SALES_STATUS = 2
    group by SALEORDER_GOODS_ID;
  </select>


  <select id="getBuyorderExpenseByAfterSalesId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
      TRBEJS.T_R_BUYORDER_EXPENSE_J_SALEORDER_ID, TRBEJS.ADD_TIME, TRBEJS.MOD_TIME, TRBEJS.CREATOR, TRBEJS.UPDATER, TRBEJS.CREATOR_NAME,
      TRBEJS.UPDATER_NAME, TRBEJS.SALEORDER_ID, TRBEJS.SALEORDER_GOODS_ID, TRBEJS.BUYORDER_EXPENSE_ID, TRBEJS.BUYORDER_EXPENSE_ITEM_ID,
      TRBEJS.SKU_ID, TRBEJS.SKU_NO, TASG.NUM
    from T_R_BUYORDER_EXPENSE_J_SALEORDER TRBEJS
    INNER JOIN T_AFTER_SALES TAS ON TRBEJS.SALEORDER_ID = TAS.ORDER_ID
    INNER JOIN T_AFTER_SALES_GOODS TASG ON TASG.AFTER_SALES_ID = TAS.AFTER_SALES_ID
    AND TASG.GOODS_ID = TRBEJS.SKU_ID
    where TAS.AFTER_SALES_ID= #{afterSalesId,jdbcType=INTEGER}
      and TRBEJS.NUM &gt; 0
  </select>

  <select id="getSaleorderByBuyorderExpenseId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    select SALEORDER_ID
    from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    group by SALEORDER_ID
  </select>
  <select id="findExpenseSaleorderIds" resultType="java.lang.Integer">
    select SALEORDER_GOODS_ID
    from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where SALEORDER_GOODS_ID in
    <foreach item="ids" collection="ids" index="index" open="(" separator="," close=")">
      #{ids}
    </foreach>
    group by SALEORDER_GOODS_ID
  </select>

  <select id="findByBuyorderExpenseId" resultType="com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto">
    select  <include refid="Base_Column_List"/>
    from T_R_BUYORDER_EXPENSE_J_SALEORDER
    where BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
  </select>

  <select id="getReleaseReturnEarlyWarnByAfterSalesId"
          parameterType="java.lang.Integer" resultType="com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity">
    select TRBEJS.SALEORDER_ID        saleorderId,
           TRBEJS.SALEORDER_GOODS_ID  saleorderGoodsId,
           TRBEJS.BUYORDER_EXPENSE_ID buyorderExpenseId,
           TRBEJS.SKU_ID              skuId,
           TRBEJS.SKU_NO              skuNo,
           TRBEJS.NUM                 num
    from T_AFTER_SALES TAS
           JOIN T_AFTER_SALES_GOODS TASG on TAS.AFTER_SALES_ID = TASG.AFTER_SALES_ID
           JOIN T_R_BUYORDER_EXPENSE_J_SALEORDER TRBEJS ON TRBEJS.SALEORDER_ID = TAS.ORDER_ID
           JOIN T_BUYORDER_EXPENSE TBE ON TRBEJS.BUYORDER_EXPENSE_ID = TBE.BUYORDER_EXPENSE_ID
      AND TASG.GOODS_ID = TRBEJS.SKU_ID
    WHERE TAS.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
      AND IFNULL(TBE.IS_RETURN_EARLY_WARN, 0) = 1
  </select>

<!--auto generated by MybatisCodeHelper on 2023-01-17-->
  <select id="selectByBuyorderExpenseItemId" resultType="com.vedeng.erp.buyorder.dto.RBuyorderExpenseJSaleorderDto">
    select
    TRBEJS.ADD_TIME, TRBEJS.MOD_TIME, TRBEJS.CREATOR, TRBEJS.UPDATER, CREATOR_NAME, UPDATER_NAME, T_R_BUYORDER_EXPENSE_J_SALEORDER_ID,
    TRBEJS.SALEORDER_ID, SALEORDER_GOODS_ID, BUYORDER_EXPENSE_ID, BUYORDER_EXPENSE_ITEM_ID, SKU_ID, SKU_NO, NUM,TS.SATISFY_DELIVERY_TIME
    from T_R_BUYORDER_EXPENSE_J_SALEORDER TRBEJS left join T_SALEORDER TS on TRBEJS.SALEORDER_ID = TS.SALEORDER_ID
    where BUYORDER_EXPENSE_ITEM_ID in
    <foreach collection="list" item="item" separator="," close=")" open="(">
      #{item}
    </foreach>
    order by BUYORDER_EXPENSE_ID desc;

  </select>

  <select id="needReplaceExpenseBuyDetails" resultMap="ExpenseBuyForSaleDetailMap">
    select a.SALEORDER_GOODS_ID,
    c.BUYORDER_NO,
    case
    when c.ORDER_TYPE = 0 then
    case
    when a.DELIVERY_DIRECT = 1 and a.DELIVERY_STATUS = 2 and c.AUDIT_STATUS = 2 then concat('供应商已发货（', sum(b.NUM) + '',
    '/',
    (a.NUM - sum(b.NUM)) + '', '）')
    when c.PAYMENT_STATUS = 2 and c.AUDIT_STATUS = 2 then '采购单已付款'
    when c.PAYMENT_STATUS != 2 and c.AUDIT_STATUS = 2 then '采购单已创建'
    end
    when c.ORDER_TYPE = 1 then
    case
    when c.PAYMENT_STATUS != 0 and a.DELIVERY_STATUS = 0 then '采购单已创建'
    when c.PAYMENT_STATUS != 0 and a.DELIVERY_STATUS = 2 then concat('供应商已发货（', sum(b.NUM) + '', '/',
    (a.NUM - sum(b.NUM)) + '', '）')
    end
    end BUYORDER_DEMAND,
    case
    when c.ORDER_TYPE = 0 then
    case
    when a.DELIVERY_DIRECT = 1 and a.DELIVERY_STATUS = 2 and c.AUDIT_STATUS = 2
    then FROM_UNIXTIME(a.DELIVERY_TIME / 1000, '%Y-%m-%d %H:%i:%s')
    when c.PAYMENT_STATUS = 2 and c.AUDIT_STATUS = 2 then c.PAYMENT_TIME
    when c.PAYMENT_STATUS != 2 and c.AUDIT_STATUS = 2 then c.ADD_TIME
    end
    when c.ORDER_TYPE = 1 then
    case
    when c.PAYMENT_STATUS != 0 and a.DELIVERY_STATUS = 0 then c.ADD_TIME
    when c.PAYMENT_STATUS != 0 and a.DELIVERY_STATUS = 2
    then FROM_UNIXTIME(a.DELIVERY_TIME / 1000, '%Y-%m-%d %H:%i:%s')
    end
    end BUY_PROCESS_MOD_TIME_STRING,
    b.CREATOR_NAME BUY_DOCK_USER_NAME,
    c.PAYMENT_STATUS
    from T_SALEORDER_GOODS a
    left join T_R_BUYORDER_EXPENSE_J_SALEORDER b
    on a.SALEORDER_GOODS_ID = b.SALEORDER_GOODS_ID
    left join T_BUYORDER_EXPENSE c
    on b.BUYORDER_EXPENSE_ID = c.BUYORDER_EXPENSE_ID
    left join T_BUYORDER d
    on c.BUYORDER_ID = d.BUYORDER_ID
    where c.VALID_STATUS = 1 and c.BUSINESS_TYPE = 1
    and a.SALEORDER_GOODS_ID in
    <foreach item="item" index="index" collection="saleorderGoodsIdCollection"
             open="(" separator="," close=")">
      #{item,jdbcType=INTEGER}
    </foreach>
    group by a.SALEORDER_GOODS_ID
  </select>

<!--auto generated by MybatisCodeHelper on 2023-01-31-->
  <delete id="deleteByBuyorderExpenseId">
        delete from T_R_BUYORDER_EXPENSE_J_SALEORDER
        where BUYORDER_EXPENSE_ID=#{buyorderExpenseId,jdbcType=INTEGER}
    </delete>
</mapper>