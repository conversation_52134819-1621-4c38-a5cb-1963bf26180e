package com.wms.service.chain.step;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.util.DateUtil;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.ExgOrderDto;
import com.wms.dto.ExgOrderGoodDto;
import com.wms.dto.StockAllocationResult;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 外接单出库
 */
@Service
public class LendOutWmsRequestHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(LendOutWmsRequestHandler.class);

    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private WmsOutputOrderMapper outputOrderMapper;

    @Resource
    private UserMapper userMapper;
    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Long lendOutOrderId = context.get("lendOutOrderId");

        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(lendOutOrderId);
        LendOutWmsRequestHandler bean = SpringUtil.getBean(LendOutWmsRequestHandler.class);
        Integer wmsSendOrderId = bean.saveWmsSendOrder(wmsOutputOrder);
        List<StockAllocationResult> allocationResultList = context.get("allocationResultList");

        List<WmsOutputOrderGoods> outputOrderGoodList = context.get("outputOrderGoodList");

        ExgOrderDto exgOrderDto = convertToExgOrderDto(lendOutOrderId,allocationResultList,outputOrderGoodList);

        try {
            //同步外借单->WMS
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_EXG_DATA);

            LOGGER.info("WMS外借单出库下传的 id:{},单号:{},请求:{}",lendOutOrderId,wmsOutputOrder.getOrderNo(), JSON.toJSONString(exgOrderDto));
            LOGGER.info("WMS外借单出库下传的 id:{},单号:{},请求:{}",lendOutOrderId,wmsOutputOrder.getOrderNo(), JSON.toJSONString(exgOrderDto));

            WmsResponse wmsResponse = wmsInterface.request(exgOrderDto);

            //请求成功
            if ("1".equals(wmsResponse.getReturnFlag())) {
                WmsSendOrder updateWmsSendOrder = new WmsSendOrder();
                updateWmsSendOrder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendOrder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendOrder);
            }

            LOGGER.info("WMS外借单出库下传的 单号:{},响应:{}",wmsOutputOrder.getOrderNo(),JSON.toJSONString(wmsResponse));
            //LOGGER.info("WMS单位转换单入库下传的 单号:{},响应:{}",wmsOutputOrder.getOrderNo(),JSON.toJSONString(wmsResponse));

        } catch (Exception e) {
            LOGGER.error("下发单位转换单入库失败error 单号:"+wmsOutputOrder.getOrderNo(),e);
            //XxlJobLogger.log("下发单位转换单入库失败error 单号:"+wmsOutputOrder.getOrderNo(),e);
        }

    }

    private ExgOrderDto convertToExgOrderDto(Long lendOutOrderId,List<StockAllocationResult> allocationResultList,List<WmsOutputOrderGoods> outputOrderGoodList) {

        WmsOutputOrder wmsOutputOrder = outputOrderMapper.selectByPrimaryKey(lendOutOrderId);

        ExgOrderDto exgOrderDto = new ExgOrderDto();
        //借货单号
        exgOrderDto.setDOCNO(wmsOutputOrder.getOrderNo());
        //单据类型
        exgOrderDto.setDOC_TYPE(WmsInterfaceOrderType.EXG_JH);
        //是否闪电换货
        exgOrderDto.setIs_exchange("N");
        //审核时间
        exgOrderDto.setApproveTime(DateUtil.convertString(System.currentTimeMillis(),DateUtil.TIME_FORMAT));

        User user = userMapper.getUserByName(wmsOutputOrder.getCreator());

        //部门
        exgOrderDto.setDepartment(user.getOrgName());
        //制单员
        exgOrderDto.setStaffName(user.getUsername());

        //客户ID或供应商ID
        exgOrderDto.setConsigneeID(wmsOutputOrder.getBorrowTraderId() + Strings.EMPTY);
        //客户名称或供应商名称
        exgOrderDto.setConsigneeName(wmsOutputOrder.getBorrowTraderName());

        String[] areas = wmsOutputOrder.getReceiverAddress().split(" ");
        //省
        exgOrderDto.setC_Province(areas[0]);
        //城市
        exgOrderDto.setC_City(areas[1]);
        //区
        exgOrderDto.setC_district(areas[2]);
        exgOrderDto.setNotes(wmsOutputOrder.getLogisticCommnet());

        //街道 无
        exgOrderDto.setC_street("/");
        //收货详细地址
        exgOrderDto.setC_Address1(wmsOutputOrder.getDetailAddress());
        //收货人联系人
        exgOrderDto.setC_Contact(wmsOutputOrder.getReceiver());
        //收货人电话
        exgOrderDto.setC_Tel1(wmsOutputOrder.getReceiverPhone());
        //收货人手机
        exgOrderDto.setC_Tel2(wmsOutputOrder.getReceiverTelphone());
        //售后原因
        exgOrderDto.setAsreasons(wmsOutputOrder.getApplyReason());

        //换货出库单商品
        exgOrderDto.setDetails(convertAllocationResult(allocationResultList,outputOrderGoodList));

        return exgOrderDto;
    }

    /**
     * @param allocationResultList
     * @param outputOrderGoodList
     * @return
     */
    private List<ExgOrderGoodDto> convertAllocationResult(List<StockAllocationResult> allocationResultList, List<WmsOutputOrderGoods> outputOrderGoodList) {

        if(CollectionUtils.isEmpty(allocationResultList)){
            return null;
        }

        return allocationResultList.stream()
                .map(result -> {
                    ExgOrderGoodDto exgOrderGoodDto = new ExgOrderGoodDto();
                    exgOrderGoodDto.setQty(BigDecimal.valueOf(result.getNum()));
                    exgOrderGoodDto.setSKU(result.getSku());
                    exgOrderGoodDto.setD_EDI_07(getWmsLendoutGoodId(outputOrderGoodList,result.getSku()) + StringUtils.EMPTY);
                    exgOrderGoodDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(result.getLogicalWarehouseId()));
                    //换货单不存在专项和非专项发货的情况，因此全部传*
                    exgOrderGoodDto.setLotAtt07("*");
                    return exgOrderGoodDto;
                }).collect(Collectors.toList());

    }

    public Long getWmsLendoutGoodId(List<WmsOutputOrderGoods> outputOrderGoodList,String sku){

        return outputOrderGoodList.stream()
                                  .filter(outputOrderGood -> outputOrderGood.getSkuNo().equals(sku))
                                  .map(outputOrderGood -> outputOrderGood.getId())
                                  .findFirst()
                                  .orElse(0L);

    }

    @Transactional
    public Integer saveWmsSendOrder(WmsOutputOrder wmsOutputOrder) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(WmsSendOrderTypeEnum.LEND_OUT.getCode());
            Long id = wmsOutputOrder.getId();
            wmsSendOrder.setOrderId(id.intValue());
            wmsSendOrder.setOrderNo(wmsOutputOrder.getOrderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = CurrentUser.getCurrentUser().getId();
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            LOGGER.error("saveWmsSendOrder error，下发外借出库库：{}",wmsOutputOrder.getOrderNo(),e);
//            XxlJobLogger.log("saveWmsSendOrder error，下发库存转换入库单：{}",wmsOutputOrder.getOrderNo(),e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }
}

