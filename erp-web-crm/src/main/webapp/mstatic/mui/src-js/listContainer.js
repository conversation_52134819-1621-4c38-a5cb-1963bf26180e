Vue.component('crm-m-search-item', {
    template: `<div class="crm-m-search-item" v-show="isShow" :class="{vertical: type === 'vertical' || (type === 'toggle' && active), active: active}">
        <div class="crm-m-search-item-title">{{ title }}</div>
        <div class="crm-m-search-item-cnt">
            <slot></slot>
        </div>
    </div>`,
    name: 'crm-m-search-item',
    props: {
        title: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: '' //默认左右,vertical:上下结构,toggle:无值左右，有值上下结构
        },
        value: {
            type: String | Number | Array | Object,
        },
        lock: {
            type: Boolean,
            default: false
        },
        vkey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isShow: true
        };
    },
    computed: {
        active() {
            let flag = false;
            let value = this.$parent.$parent.value[this.vkey];

            console.log(this.value)

            switch (typeof value) {
                case 'string': {
                    if (value && value.trim()) {
                        flag = true;
                    }
                    break;
                }
                case 'number': {
                    if (value) {
                        flag = true;
                    }
                    break;
                }
                case 'object': {
                    if (value) {
                        if (Array.isArray(value) && value.length) {
                            flag = true;
                        } else if (Object.keys(value).length) {
                            flag = true;
                        }
                    }
                    break;
                }
            }

            return flag;
        }
    },
    mounted() {
    },
    methods: {
        toggleCheck() {
            this.checked = !this.checked;
            this.$emit('update:checked', this.checked);
            this.$emit('change', this.checked);
        }
    }
})

Vue.component('crm-list-container', {
    template: `<div class="crm-m-list-layout">
        <div class="crm-m-list-header">
            <div class="crm-m-list-tab" @click="toggleShowTabList">
                <div class="crm-m-list-tab-txt" :class="{show: isShowTab}">{{ tabTxt }}<i class="vd-ui_icon icon-down" v-if="tabList.length"></i></div>
            </div>
            <div class="crm-m-list-header-options">
                <div class="header-option-item option-sort" :class="{show: isShowSort}" v-if="!(!filterNum && !(list && list.length)) && filterSortList && filterSortList.length" @click="toggleShowSort">
                    <i class="vd-ui_icon icon-paixu"></i>
                </div>
                <div class="header-option-item option-filter" @click="showFilterList">
                    <i class="vd-ui_icon icon-filter"></i>
                    <div class="header-option-filter-num" v-if="filterNum">
                        <div class="num-txt">{{ filterNum }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="crm-m-list-cnt" ref="listContainer" v-show="!isFirstloading">
            <template v-if="list && list.length"> 
                <div class="crm-m-list-inner">
                    <template v-for="(item, index) in list">
                        <slot name="list-item" v-bind:item="item"></slot>
                    </template>
                </div>
                <div class="crm-m-list-loading" v-show="list.length < total">
                    <i class="vd-ui_icon icon-loading"></i>加载中...
                </div>
            </template>
            <template v-else>
                <div class="crm-m-list-empty">
                    <template v-if="!filterNum">
                        <div class="crm-m-list-empty-pic">
                            <img src="/mstatic/image/empty/list-empty.svg" />
                        </div>
                        <div class="crm-m-list-empty-txt">暂无数据</div>
                    </template>
                    <template v-else>
                        <div class="crm-m-list-empty-pic">
                            <img src="/mstatic/image/empty/search-empty.svg" />
                        </div>
                        <div class="crm-m-list-empty-txt">抱歉，没有找到您想搜索的结果</div>
                    </template>
                </div>
            </template>
        </div>
        <crm-slide-dialog ref="listTab" :isShow.sync="isShowTab" v-if="tabList.length" zindex="10" type="down">
            <div class="crm-m-tab-wrap">
                <div class="crm-m-tab-item" @click="changeTab({})" :class="{active: !customSearchId && customSearchId !== 0}">
                    <div class="crm-m-tab-item-txt">{{ title }}</div>
                </div>
                <div class="crm-m-tab-item" @click="changeTab(item)" v-for="(item, index) in tabList" :class="{active: customSearchId == item.id}">
                    <div class="crm-m-tab-item-txt">{{ item.label }}</div>
                    <i class="vd-ui_icon icon-edit" @click.stop="editCustomFilter(item)"></i>
                    <i class="vd-ui_icon icon-delete" @click.stop="deleteCustomSearch(item)"></i>
                </div>
            </div>
        </crm-slide-dialog>
        <crm-slide-dialog ref="listSort" :isShow.sync="isShowSort" v-if="filterSortList.length" zindex="10" type="down">
            <div class="crm-m-sort-wrap">
                <div class="crm-m-sort-item" @click="changeSort(item)" v-for="(item, index) in filterSortList" :class="{active: sortOrder == item.order}">
                    {{ item.label }}
                </div>
            </div>
        </crm-slide-dialog>
        <crm-slide-dialog ref="filterList" title="筛选" topOptionTxt="设置" :maskHide="false" @topOption="showFilterSetting">
            <div class="crm-list-filter-wrap">
                <div class="crm-list-filter-cnt" ref="filterContent">
                    <slot name="filter"></slot>
                </div>
                <div class="crm-list-filter-footer">
                    <div class="filter-footer-option" :class="{disabled: tabList.length >= 5}" @click="addCustomFilter">
                        <i class="option-icon option-add"></i>
                        <div class="option-txt">自定义</div>
                    </div>
                    <div class="filter-footer-option" @click="resetSearchParams">
                        <i class="option-icon option-reset"></i>
                        <div class="option-txt">重置</div>
                    </div>
                    <div class="filter-footer-btns">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="hideFilterList">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerSearch">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
        <crm-slide-dialog ref="filterSetting" title="搜索筛选设置" :maskHide="false">
            <div class="crm-list-filter-setting">
                <draggable v-model="filterSettingShowList" class="filter-setting-list" ghost-class="placehodler" @sort="handlerSettingSort">
                    <div class="filter-setting-item" :class="{active: item.isShow || item.disabled, disabled: item.disabled}" v-for="(item, index) in filterSettingShowList">
                        <div class="filter-setting-item-txt">{{ item.label }}</div>
                        <div class="filter-setting-item-option">
                            <vd-ui-toggle :checked.sync="item.isShow" :disabled="item.disabled"></vd-ui-toggle>
                        </div>
                    </div>
                </draggable>
                <div class="filter-setting-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="hideFilterSetting">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="saveSettingData">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
        <ui-custom-dialog :isShow.sync="isShowCustomFilterDialog" title="自定义筛选" :lock="true">
            <div class="crm-filter-add-wrap">
                <div class="crm-filter-form-wrap">
                    <div class="crm-filter-form-label">
                        <span class="must">*</span>
                        <div>标题名称：</div>
                    </div>
                    <div class="crm-filter-form-cnt">
                        <ui-input maxlength="7" v-model="customFilterName"></ui-input>
                    </div>
                </div>
                <div class="crm-filter-form-tip">建议标题设置简约、直接、准确，最多可输入7个字</div>
            </div>
            <template v-slot:footer>
                <button 
                    class="mui-cdb"
                    @click="isShowCustomFilterDialog = false"
                >取消</button>
                <button 
                    class="mui-cdb confirm"
                    :class="{disabled: !customFilterName.trim()}"
                    @click="saveCustomFilter"
                >保存</button>
            </template>
        </ui-custom-dialog>
        <a class="crm-list-add" v-if="addLink" :href="addLink">
            <i class="vd-ui_icon icon-add"></i>
        </a>
    </div>`,
    props: {
        title: '',
        filterSortList: {
            type: Array,
            default() {
                return [];
            }
        },
        addLink: '',
        value: {},
        listKey: '',
        listUrl: ''
    },
    data() {
        return {
            tabList: [],
            customSearchId: '',
            isShowTab: false,
            tabTxt: '',
            isShowSort: false,
            sortOrder: '',
            filterSettingList: [],
            filterSettingShowList: [],
            list: [],
            total: 0,
            isFirstloading: true, //首次加载隐藏页面
            isloading: false,
            pageNum: 1,
            searchParams: {},
            querySettingId: '', //参数保存id 
            isShowCustomFilterDialog: false,
            customFilterName: '',
            customEditId: '',
            filterNum: 0,
            filterKeys: []
        };
    },
    computed: {

    },
    mounted() {
        this.getFilterKeys();
        this.tabTxt = this.title;
        this.$nextTick(() => {
            this.getSettingInfo();
        })
        this.getList();
        window.addEventListener('scroll', this.checkScroll);

        this.searchParams = JSON.parse(JSON.stringify(this.value));
    },
    methods: {
        getFilterKeys() {
            this.$refs.filterList.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name === 'crm-m-search-item' && searchItem.vkey) {
                    this.filterKeys.push(searchItem.vkey)
                }
            })

            console.log(this.filterKeys)
        },
        getList() {
            this.isloading = true;
            if(this.pageNum == 1) {
                GLOBAL.showGlobalLoading();
            }

            if(this.isFirstloading) {
                this.getFilterNum();
            }

            axios.post(this.listUrl, {
                pageNum: this.pageNum,
                pageSize: 100,
                orderBy: this.sortOrder,
                param: this.value
            }).then(({ data }) => {
                this.isloading = false;
                GLOBAL.hideGlobalLoading();
                
                this.isFirstloading = false;
                if (data.code === 0) {
                    if(this.pageNum == 1) {
                        this.list = data.data.list;
                    } else {
                        this.list = this.list.concat(data.data.list);
                    }
                    this.total = data.data.total;
                }
            })
        },
        checkScroll() {
            let winScroll = window.scrollY;
            let containerHeight = this.$refs.listContainer.offsetHeight;
            let pageHeight = window.screen.height;

            if (!this.isloading && this.list.length < this.total && pageHeight + winScroll > containerHeight - 50) {
                this.pageNum++;
                this.getList();
            }
        },
        getSettingInfo() {
            this.$axios.post('/crm/common/m/searchList', {
                searchFromEnum: this.listKey
            }).then(({ data }) => {
                if (data.code === 0) {
                    let tabList = data.data.conditionList || [];
                    let settingList = data.data.queryMap ? JSON.parse(data.data.queryMap.searchContent || '[]') : [];
                    this.querySettingId = data.data.queryMap ? data.data.queryMap.id : '';

                    tabList.forEach(item => {
                        this.tabList.push({
                            label: item.searchName,
                            id: item.id,
                            isCustom: true,
                            customData: JSON.parse(item.searchContent)
                        })
                    })

                    let filterSettinglist = [];
                    this.$refs.filterList.$children.forEach((item, index) => {
                        if(item.$options.name === 'crm-m-search-item') {
                            let flag = false;
                            settingList.forEach(settingItem => {
                                if(item.title === settingItem.label) {
                                    filterSettinglist.push(settingItem);
                                    flag = true;
                                }
                            })

                            console.log(item.$options.propsData.lock)

                            if(!flag) {
                                filterSettinglist.push({
                                    label: item.title,
                                    sort: index,
                                    isShow: true,
                                    disabled: !!item.$options.propsData.lock
                                });
                            }
                        }
                    })

                    this.filterSettingList = filterSettinglist;

                    this.checkSettingShow();
                }
            })
        },
        toggleShowTabList() {
            if (this.$refs.listTab) {
                this.isShowTab = !this.isShowTab;
                this.isShowSort = false;
            }
        },
        toggleShowSort() {
            if (this.$refs.listSort) {
                this.isShowSort = !this.isShowSort;
                this.isShowTab = false;
            }
        },
        changeTab(item) {
            if (item.id) {
                this.customSearchId = item.id;
                this.tabTxt = item.label;
                this.value = JSON.parse(JSON.stringify(item.customData));
                this.searchParams = JSON.parse(JSON.stringify(this.value));
            } else {
                this.customSearchId = '';
                this.tabTxt = this.title;
                this.clearParams();
                this.searchParams = JSON.parse(JSON.stringify(this.value));
            }

            this.getList();

            this.$nextTick(() => {
                this.getFilterNum();
            })
            this.isShowTab = false;
        },
        changeSort(item) {
            this.sortOrder = item.order || '';
            this.isShowSort = false;
            this.getList();
        },
        showFilterList() {
            let params = JSON.parse(JSON.stringify(this.searchParams));
            this.$emit('input', params);
            this.$refs.filterList.show();
            this.isShowTab = false;
            this.isShowSort = false;
        },
        hideFilterList() {
            this.$refs.filterList.hide();
        },
        showFilterSetting() {
            this.filterSettingShowList = JSON.parse(JSON.stringify(this.filterSettingList));
            this.filterSettingShowList.sort((a, b) => {
                return a.sort - b.sort;
            })
            this.$refs.filterSetting.show();
        },
        hideFilterSetting() {
            this.$emit('input', this.searchParams);
            this.$refs.filterSetting.hide();
        },
        checkSettingShow() {
            let list = [];
            let num = 0;

            this.$refs.filterList.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name === 'crm-m-search-item' && searchItem.title) {
                    let filterItemInfo = null;

                    this.filterSettingList.forEach(filterItem => {
                        if (searchItem.title === filterItem.label) {
                            filterItemInfo = filterItem;
                            
                            if(!filterItem.isShow) {
                                searchItem.isShow = false;
                            } else {
                                searchItem.isShow = true;
                            }
                        }
                    })

                    list.push({
                        el: searchItem.$el,
                        sort: filterItemInfo ? filterItemInfo.sort : index
                    })

                    num++;
                }
            });

            console.log(list)

            list.sort((a, b) => {
                return a.sort - b.sort;
            })

            list.forEach(item => {
                this.$refs.filterContent.append(item.el);
            })
        },
        handlerSettingSort() {
            this.filterSettingShowList.forEach((item, index) => {
                item.sort = index;
            })
        },
        saveSettingData() {
            this.filterSettingList = JSON.parse(JSON.stringify(this.filterSettingShowList));
            this.checkSettingShow();
            this.hideFilterSetting();

            //将修改的设置存储到后台
            this.$axios.post('/crm/common/m/saveSearchConfig', {
                searchFromEnum: this.listKey,
                searchType: 'QUERY',
                searchContent: JSON.stringify(this.filterSettingList),
                id: this.querySettingId
            }).then(({ data }) => {
                
            })
        },
        deleteCustomSearch(item) {
            this.customEditId = item.id;
            let _this = this;
            this.$dialog({
                type: 'warn',
                message: '删除后将无法恢复，您确定需要删除吗?',
                buttons: [{
                    txt: '取消'
                }, {
                    txt: '删除',
                    btnClass: 'delete',
                    callback() {
                        _this.$axios.post('/crm/common/m/deleteSearchConfig', {
                            id: _this.customEditId
                        }).then(({ data }) => {
                            if (data.success) {
                                _this.tabList.forEach((item, index) => {
                                    if (item.id == _this.customEditId) {
                                        _this.tabList.splice(index, 1);
                                        if (_this.customSearchId == item.id) {
                                            _this.changeTab({});
                                        }
                                    }
                                })

                                _this.$message.success('删除成功');
                            } else {
                                _this.$message.warn(data.message);
                            }
                        })
                    }
                }]
            })
        },
        handlerSearch() {
            this.pageNum == 1;
            this.getList();
            this.searchParams = JSON.parse(JSON.stringify(this.value));
            console.log(this.searchParams)
            this.hideFilterList();

            this.getFilterNum();
        },
        clearParams() {
            for (let item in this.value) {
                if (typeof this.value[item] === 'string' || typeof this.value[item] === 'number') {
                    this.value[item] = '';
                } else if (Array.isArray(this.value[item])) {
                    this.value[item] = [];
                } else if (typeof this.value[item] === 'object') {
                    this.value[item] = {};
                }
            }

            this.$emit('input', this.value);
        },
        resetSearchParams() {
            this.clearParams();
        },
        getFilterNum() {
            let num = 0;

            for (let item in this.value) {
                if(this.filterKeys.indexOf(item) !== -1) {
                    if (typeof this.value[item] === 'string' || typeof this.value[item] === 'number') {
                        if(this.value[item]) {
                            num++;
                        }
                    } else if (Array.isArray(this.value[item])) {
                        if(this.value[item].length) {
                            num++;
                        }
                    } else if (typeof this.value[item] === 'object') {
                        if(Object.keys(this.value[item]).length) {
                            num++;
                        }
                    }
                }
            }

            this.filterNum = num;
        },
        addCustomFilter() {
            if(this.tabList.length > 5) {
                return;
            }

            this.isShowCustomFilterDialog = true;
            this.customFilterName = '';
            this.customEditId = '';
        },
        editCustomFilter(item) {
            this.isShowCustomFilterDialog = true;
            this.customFilterName = item.label;
            this.customEditId = item.id;
        },
        saveCustomFilter() {
            this.$axios.post('/crm/common/m/saveSearchConfig', {
                searchName: this.customFilterName,
                searchFromEnum: this.listKey,
                searchType: "LIST",
                searchContent: JSON.stringify(this.value),
                id: this.customEditId || ''
            }).then(({ data }) => {
                if (data.success) {
                    this.$message.success(this.customEditId ? '操作成功' : '新增成功');
                    this.isShowCustomFilterDialog = false;

                    if (this.customEditId) {
                        this.tabList.forEach(item => {
                            if (item.id == this.customEditId) {
                                item.label = this.customFilterName;

                                if(this.customSearchId == item.id) {
                                    this.tabTxt = this.customFilterName;
                                }
                            }
                        })
                    } else {
                        this.tabList.push({
                            label: this.customFilterName,
                            id: data.data,
                            isCustom: true,
                            customData: this.value
                        })
                    }

                    this.isShowCustomDialog = false;
                } else {
                    this.$message.warn(data.message);
                }
            })
        }
    }
})