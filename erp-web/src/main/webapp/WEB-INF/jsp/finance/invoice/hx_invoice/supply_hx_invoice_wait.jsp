<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="进项票（供应链）" scope="application"/>
<%@ include file="../../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/supply_hx_invoice_wait.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/hx_invoice_common.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<div class="layui-tab layui-tab-brief" lay-filter="test">
    <ul class="layui-tab-title">
        <li onclick="changeTab(0)" class="layui-this">待录票</li>
        <li onclick="changeTab(1)">审核中</li>
        <li onclick="changeTab(2)">已审核</li>
        <li onclick="changeTab(3)">待退票</li>
        <li onclick="changeTab(4)">无效票</li>
        <li class="addtitle btn-small bt-bg-style bg-light-blue" href="javascript:void(0); "
            tabtitle='{"num":"finance_invoice_buyInvoiceInput",
                                    "link":"/finance/invoice/buyInvoiceInput.do","title":"采购录票", "closable": "0"}'
            style="float: right; height: 38px">手工录票
        </li>
    </ul>
</div>
<div class="layui-tab-item layui-show">
    <div class="searchfunc ">
        <form method="post" id="search" action="/supplyChain/invoice/hx_invoice_wait.do">
            <ul>
                <li>
                    <label class="infor_name">关键词</label>
                    <input type="text" class="input-middle" name="keyword" id="keyword"
                           value="${invoiceSearch.keyword}"/>
                </li>

                <li>
                    <label class="infor_name">归属采购</label>
                    <select class="input-middle" name="userId" id="userId">
                        <option value="0">全部</option>
                        <c:forEach items="${userList}" var="user">
                            <option value="${user.userId}"
                                    <c:if test="${invoiceSearch.userId eq user.userId}">selected="selected"</c:if>>${user.username}</option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <label class="infor_name">票种</label>
                    <select class="input-middle" name="invoiceTaxRate" id="invoiceTaxRate">
                        <option value="0"
                                <c:if test="${invoiceSearch.invoiceTaxRate == 0}">selected</c:if> >全部
                        </option>
                        <option value="971"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 13 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            13%增值税普通发票
                        </option>
                        <option value="972"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 13 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                            13%增值税专用发票
                        </option>
                        <option value="681"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 16 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            16%增值税普通发票
                        </option>
                        <option value="682"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 16 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                            16%增值税专用发票
                        </option>
                        <option value="683"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 6 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>6%增值税普通发票
                        </option>
                        <option value="684"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 6 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>6%增值税专用发票
                        </option>
                        <option value="685"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 3 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>3%增值税普通发票
                        </option>
                        <option value="686"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 3 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>3%增值税专用发票
                        </option>
                        <option value="687"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 0 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>0%增值税普通发票
                        </option>
                    </select>
                </li>

                <li>
                    <div class="infor_name">
                        开票时间
                    </div>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期"
                           onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                           name="startAddDateStr" id="startAddDateStr"
                           value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" onClick="WdatePicker()"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                           name="endAddDateStr" id="endAddDateStr" autocomplete="off"
                           value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
                </li>
                <li>
                    <label class="infor_name">发票备注</label>
                    <input type="text" class="input-middle" name="comment" id="comment"
                           value="${invoiceSearch.comment}"/>
                </li>

                <li>
                    <label class="infor_name">发票类型</label>
                    <select class="input-middle" name="invoiceStatus" id="invoiceStatus">
                        <option value="0">全部</option>
                        <option value="1" <c:if test="${invoiceSearch.invoiceStatus eq 1}">selected="selected"</c:if>>采购票</option>
                        <option value="5" <c:if test="${invoiceSearch.invoiceStatus eq 5}">selected="selected"</c:if>>费用票</option>
                    </select>
                </li>

              <%--  接入原有录票系统该功能暂时隐藏--%>
               <%-- <li>
                    <label class="infor_name">仅看暂存</label>
                    <select class="input-middle" name="isRecording" id="isRecording">
                        <option value="1"
                                <c:if test="${invoiceSearch.isRecording == 1}">selected</c:if> >
                            是
                        </option>
                        <option value="0"
                                <c:if test="${invoiceSearch.isRecording == null || invoiceSearch.isRecording == 0}">selected</c:if>>
                            否
                        </option>
                    </select>
                </li>--%>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">查询</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
            </div>
        </form>
        <br>
        <div class="content">
            <div class="">
                <div style="width:1752px;" class='superdiv'>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid6">发票号码</th>
                            <th class="wid10">供应商</th>
                            <th class="wid4">归属采购</th>
                            <th class="wid4">发票总额</th>
                            <th class="wid4">已录票金额</th>
                            <th class="wid6">票种</th>
                            <th class="wid4">发票类型</th>
                            <th class="wid9">发票备注</th>
                            <th class="wid6">发票代码</th>
                            <th class="wid6">开票时间</th>
                            <th class="wid8">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:forEach var="hxInvoiceVo" items="${list}">
                            <tr>
                                <td>
                                        ${hxInvoiceVo.invoiceNum}
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${hxInvoiceVo.traderId eq 0 || hxInvoiceVo.traderId == null}">
                                            ${hxInvoiceVo.salerName}
                                        </c:when>
                                        <c:otherwise>
                                                       <span style="color: #438DEF" class="addtitle"
                                                             href="javascript:void(0);"
                                                             tabTitle='{"num":"viewtrader${hxInvoiceVo.traderId}",
                                                             "link":"/trader/supplier/baseinfo.do?traderId=${hxInvoiceVo.traderId}","title":"供应商信息"}'><c:out
                                                               value="${hxInvoiceVo.salerName}"
                                                               escapeXml="true"/></span>
                                        </c:otherwise>
                                    </c:choose>
                                </td>

                                <td>
                                    <c:forEach items="${userList}" var="user">
                                        <c:if test="${user.userId == hxInvoiceVo.orderUserId}">${user.username}</c:if>
                                    </c:forEach>
                                </td>
                                <td>
                                    <fmt:formatNumber type="number" value="${hxInvoiceVo.amount}" pattern="0.00"
                                                      maxFractionDigits="2"/>

                                </td>
                                <td>
                                    <fmt:formatNumber type="number" value="${hxInvoiceVo.recordedAmount}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <c:if test="${hxInvoiceVo.taxRate != null && hxInvoiceVo.invoiceCategory != null}">
                                        ${hxInvoiceVo.taxRate}%增值税<c:if test="${hxInvoiceVo.invoiceCategory == '0'}">普通</c:if><c:if test="${hxInvoiceVo.invoiceCategory == '1'}">专用</c:if>发票
                                    </c:if>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${hxInvoiceVo.invoiceStatus == 1}">
                                            采购票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.invoiceStatus == 5}">
                                            费用票
                                        </c:when>
                                    </c:choose>
                                </td>
                                <td>
                                    ${hxInvoiceVo.comment}
                                </td>
                                <td>
                                        ${hxInvoiceVo.invoiceCode}
                                </td>
                                <td>
                                    <date:date value="${hxInvoiceVo.createTime}" format="yyyy-MM-dd"/>
                                </td>
                                <td>
                                    <a onclick="viewAndDownloadHxInvoiceHref('${hxInvoiceVo.invoiceCode}','${hxInvoiceVo.invoiceNum}','${hxInvoiceVo.hxInvoiceId}',1)">
                                        <font style="color: #438DEF"
                                              id="viewInvocieFont${hxInvoiceVo.hxInvoiceId}">查看发票</font>
                                    </a>
                                    <%--<a class="addtitle" href="javascript:void(0); "
                                       tabtitle='{"num":"hx_invoice_record_init${hxInvoiceVo.hxInvoiceId}",
                                    "link":"/supplyChain/invoice/hx_invoice_record_init.do?hxInvoiceId=${hxInvoiceVo.hxInvoiceId}","title":"录票",
                                    "closable": "0"}'>
                                        <font style="color: #438DEF">录票</font>
                                    </a>--%>
                                    <a class="addtitle" href="javascript:void(0); "
                                       tabtitle='{"num":"hx_invoice_record_init${hxInvoiceVo.hxInvoiceId}",
                                    "link":"/finance/invoice/buyInvoiceInput.do?traderName=${hxInvoiceVo.salerName}&hxInvoiceId=${hxInvoiceVo.hxInvoiceId}&firstFlag=1","title":"录票",
                                    "closable": "0"}'>
                                        <font style="color: #438DEF">录票</font>
                                    </a>

                                    <a onclick="saveHxInvoiceStatus(${hxInvoiceVo.hxInvoiceId},4)"
                                       href="javascript:void(0)">
                                        <font style="color: #438DEF">标为非采购票</font>
                                    </a>
                                    |
                                    <a onclick="saveHxInvoiceStatus(${hxInvoiceVo.hxInvoiceId},9)"
                                       href="javascript:void(0)">
                                        <font style="color: #438DEF">采购退票</font>
                                    </a>
                                </td>
                            </tr>
                            <tr style="display: none" id="imgTr${hxInvoiceVo.hxInvoiceId}">
                                <td colspan="9" height="350px" id="invoiceImg${hxInvoiceVo.hxInvoiceId}">
                                </td>
                            </tr>
                            <input id="imgFlag${hxInvoiceVo.hxInvoiceId}" value="0" type="hidden">
                            <input id="imgSrc${hxInvoiceVo.hxInvoiceId}" value="${hxInvoiceVo.attachment}"
                                   type="hidden">
                        </c:forEach>
                        </tbody>
                    </table>

                    <c:if test="${empty list}">
                        <!-- 查询无结果弹出 -->
                        <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
                    </c:if>
                </div>
            </div>
            <div>
                <tags:page page="${page}"/>
            </div>
        </div>
    </div>
</div>