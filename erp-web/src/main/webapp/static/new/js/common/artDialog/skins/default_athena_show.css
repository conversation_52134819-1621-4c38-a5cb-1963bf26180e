@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */
 
/* common start */
body { _margin:0; _height:100%; /*IE6 BUG*/ }
.aui_outer { text-align:left; }
table.aui_border, table.aui_dialog { border:0; margin:0; border-collapse:collapse; width:auto; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_c, .aui_e, .aui_sw, .aui_s, .aui_se, .aui_header, .aui_tdIcon, .aui_main, .aui_footer { padding:0; }
.aui_title { overflow:hidden; text-overflow: ellipsis; }
.aui_state_noTitle .aui_title { display:none; }
.aui_close { display:block; position:absolute; text-decoration:none; outline:none; _cursor:pointer; }
.aui_close:hover { text-decoration:none; }
.aui_main { min-width:9em; min-width:0\9/*IE8 BUG*/; }
.aui_content { display:inline-block; *zoom:1; *display:inline; border:none 0; padding:20px;}
.aui_content.aui_state_full { display:block; width:100%; margin:0; padding:0!important; height:100%; }
.aui_loading { width:96px; height:32px; text-align:left; text-indent:-999em; overflow:hidden; background:url(icons/loading.gif) no-repeat center center; }
.aui_icon { vertical-align: middle; }
.aui_icon div { width:48px; height:48px; margin:10px 0 10px 10px; background-position: center center; background-repeat:no-repeat; }
.aui_buttons { padding:8px; text-align:center; white-space:nowrap; }
.aui_buttons button { margin-left:15px; padding: 6px 8px; cursor: pointer; display: inline-block; text-align: center; line-height: 1; *padding:4px 10px; *height:2em; letter-spacing:2px; width:auto; overflow:visible; *width:1; color: #333; border: solid 1px #999; border-radius: 5px; background: #DDD; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFF', endColorstr='#DDDDDD'); background: linear-gradient(top, #FFF, #DDD); background: -moz-linear-gradient(top, #FFF, #DDD); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#FFF), to(#DDD)); }
.aui_buttons button::-moz-focus-inner{ border:0; padding:0; margin:0; }
.aui_buttons button:focus { outline:none 0; border-color:#426DC9; }
.aui_buttons button:hover { color:#000; border-color:#666; }
.aui_buttons button:active { border-color:#666; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DDDDDD', endColorstr='#FFFFFF'); background: linear-gradient(top, #DDD, #FFF); background: -moz-linear-gradient(top, #DDD, #FFF); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#DDD), to(#FFF)); }
.aui_buttons button[disabled] { cursor:default; color:#666; background:#DDD; border: solid 1px #999; filter:alpha(opacity=50); opacity:.5; }
button.highlight,button.aui_state_highlight { color: #FFF; border: solid 1px #1c6a9e; background: #2288cc; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee', endColorstr='#2288cc'); background: linear-gradient(top, #33bbee, #2288cc); background: -moz-linear-gradient(top, #33bbee, #2288cc); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#33bbee), to(#2288cc)); }
button.highlight,button.aui_state_highlight:hover { color:#FFF; border-color:#0F3A56; }
button.highlight,button.aui_state_highlight:active { border-color:#1c6a9e; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee', endColorstr='#2288cc'); background: linear-gradient(top, #33bbee, #2288cc); background: -moz-linear-gradient(top, #33bbee, #2288cc); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#33bbee), to(#2288cc)); }
/* common end */

.aui_inner { background:#FFF; }
.aui_nw, .aui_ne, .aui_sw, .aui_se { width:6px; height:6px; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_e, .aui_sw, .aui_s, .aui_se { background:#4c4f59;}
.aui_nw{
	border-radius: 3px 0 0 0;
}
.aui_ne{
	border-radius: 0 3px 0 0;
}
.aui_sw{
	border-radius: 0 0 0 3px;
}
.aui_se{
	border-radius: 0 0 3px 0;
}
.aui_state_lock .aui_nw, .aui_state_lock .aui_n, .aui_state_lock .aui_ne, .aui_state_lock .aui_w, .aui_state_lock .aui_e, .aui_state_lock .aui_sw, .aui_state_lock .aui_s, .aui_state_lock .aui_se { background:rgba(0, 0, 0, .5); background:#000\9!important; filter:alpha(opacity=50); }
.aui_titleBar { position:relative; height:100%; }
.aui_title { height:32px; line-height:32px; padding:0 20px;font-weight:bold; background-color:#f1f1f1;  border-bottom:1px solid #e6e6e6; }
.aui_state_drag .aui_title { background: linear-gradient(top, #bdc6cd, #edf5f8); background: -moz-linear-gradient(top, #bdc6cd, #edf5f8); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#bdc6cd), to(#edf5f8)); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#bdc6cd', endColorstr='#edf5f8'); }
.aui_close { padding:0; top:7px; right:4px; width:21px; height:21px; line-height:21px; font-size:22px; font-weight:bold; color:#b8b8b8; text-align:center; }
.aui_close:hover { color:#666; }
.aui_content { color:#666; }
.aui_state_focus .aui_content { color:#000; }
.aui_buttons { background-color:#f1f1f1; border-top:solid 1px #e6e6e6; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_se { width:3px; height:3px; }
.aui_state_noTitle .aui_inner { border:1px solid #666; background:#FFF; }
.aui_state_noTitle .aui_outer { border:none 0; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_n, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_w, .aui_state_noTitle .aui_e, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_s, .aui_state_noTitle .aui_se { background:rgba(0, 0, 0, .05); background:#000\9!important; filter:alpha(opacity=5)!important; }
.aui_state_noTitle .aui_titleBar { bottom:0; _bottom:0; _margin-top:0; }
.aui_state_noTitle .aui_close { top:0; right:0; width:18px; height:18px; line-height:18px; text-align:center; text-indent:0; font-size:18px; text-decoration:none; color:#214FA3; background:none; filter:!important; }
.aui_state_noTitle .aui_close:hover, .aui_state_noTitle .aui_close:active { text-decoration:none; color:#900; }