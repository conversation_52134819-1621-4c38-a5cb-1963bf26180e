<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.firstengage.dao.GoodsRegisrationExpiryDateMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.GoodsRegisrationExpiryDate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    <id column="GOODS_REGISTRATION_EXPIRY_DATE_ID" property="goodsRegistrationExpiryDateId" jdbcType="INTEGER" />
    <result column="REGISTRATION_NUMBER_ID" property="registrationNumberId" jdbcType="INTEGER" />
    <result column="TYPE" property="type" jdbcType="INTEGER" />
    <result column="START_TIME" property="startTime" jdbcType="BIGINT" />
    <result column="END_TIME" property="endTime" jdbcType="BIGINT" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    GOODS_REGISTRATION_EXPIRY_DATE_ID, REGISTRATION_NUMBER_ID, `TYPE`, START_TIME, END_TIME, 
    IS_DELETE, ADD_TIME, UPDATE_TIME, CREATOR, UPDATOR
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDateExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_GOODS_REGISTRATION_EXPIRY_DATE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_GOODS_REGISTRATION_EXPIRY_DATE
    where GOODS_REGISTRATION_EXPIRY_DATE_ID = #{goodsRegistrationExpiryDateId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    delete from T_GOODS_REGISTRATION_EXPIRY_DATE
    where GOODS_REGISTRATION_EXPIRY_DATE_ID = #{goodsRegistrationExpiryDateId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDateExample" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    delete from T_GOODS_REGISTRATION_EXPIRY_DATE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="goodsRegistrationExpiryDateId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_REGISTRATION_EXPIRY_DATE (REGISTRATION_NUMBER_ID, `TYPE`, START_TIME, 
      END_TIME, IS_DELETE, ADD_TIME, 
      UPDATE_TIME, CREATOR, UPDATOR
      )
    values (#{registrationNumberId,jdbcType=INTEGER}, #{type,jdbcType=INTEGER}, #{startTime,jdbcType=BIGINT}, 
      #{endTime,jdbcType=BIGINT}, #{isDelete,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, 
      #{updateTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{updator,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    <selectKey resultType="java.lang.Integer" keyProperty="goodsRegistrationExpiryDateId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_REGISTRATION_EXPIRY_DATE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="registrationNumberId != null" >
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="type != null" >
        `TYPE`,
      </if>
      <if test="startTime != null" >
        START_TIME,
      </if>
      <if test="endTime != null" >
        END_TIME,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="registrationNumberId != null" >
        #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null" >
        #{endTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDateExample" resultType="java.lang.Integer" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    select count(*) from T_GOODS_REGISTRATION_EXPIRY_DATE
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    update T_GOODS_REGISTRATION_EXPIRY_DATE
    <set >
      <if test="record.goodsRegistrationExpiryDateId != null" >
        GOODS_REGISTRATION_EXPIRY_DATE_ID = #{record.goodsRegistrationExpiryDateId,jdbcType=INTEGER},
      </if>
      <if test="record.registrationNumberId != null" >
        REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="record.type != null" >
        `TYPE` = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.startTime != null" >
        START_TIME = #{record.startTime,jdbcType=BIGINT},
      </if>
      <if test="record.endTime != null" >
        END_TIME = #{record.endTime,jdbcType=BIGINT},
      </if>
      <if test="record.isDelete != null" >
        IS_DELETE = #{record.isDelete,jdbcType=BIT},
      </if>
      <if test="record.addTime != null" >
        ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      </if>
      <if test="record.updateTime != null" >
        UPDATE_TIME = #{record.updateTime,jdbcType=BIGINT},
      </if>
      <if test="record.creator != null" >
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updator != null" >
        UPDATOR = #{record.updator,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    update T_GOODS_REGISTRATION_EXPIRY_DATE
    set GOODS_REGISTRATION_EXPIRY_DATE_ID = #{record.goodsRegistrationExpiryDateId,jdbcType=INTEGER},
      REGISTRATION_NUMBER_ID = #{record.registrationNumberId,jdbcType=INTEGER},
      `TYPE` = #{record.type,jdbcType=INTEGER},
      START_TIME = #{record.startTime,jdbcType=BIGINT},
      END_TIME = #{record.endTime,jdbcType=BIGINT},
      IS_DELETE = #{record.isDelete,jdbcType=BIT},
      ADD_TIME = #{record.addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{record.updateTime,jdbcType=BIGINT},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATOR = #{record.updator,jdbcType=INTEGER}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    update T_GOODS_REGISTRATION_EXPIRY_DATE
    <set >
      <if test="registrationNumberId != null" >
        REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="startTime != null" >
        START_TIME = #{startTime,jdbcType=BIGINT},
      </if>
      <if test="endTime != null" >
        END_TIME = #{endTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
    </set>
    where GOODS_REGISTRATION_EXPIRY_DATE_ID = #{goodsRegistrationExpiryDateId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDate" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 23 13:22:16 CST 2020.
    -->
    update T_GOODS_REGISTRATION_EXPIRY_DATE
    set REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER},
      `TYPE` = #{type,jdbcType=INTEGER},
      START_TIME = #{startTime,jdbcType=BIGINT},
      END_TIME = #{endTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATOR = #{updator,jdbcType=INTEGER}
    where GOODS_REGISTRATION_EXPIRY_DATE_ID = #{goodsRegistrationExpiryDateId,jdbcType=INTEGER}
  </update>

  <update id="deleteByRegistrationNumberId">
    UPDATE T_GOODS_REGISTRATION_EXPIRY_DATE SET IS_DELETE=1 WHERE REGISTRATION_NUMBER_ID=#{registrationNumberId}
  </update>

  <select id="getListByRegistrationNumberId" resultMap="BaseResultMap">
    SELECT * FROM T_GOODS_REGISTRATION_EXPIRY_DATE WHERE REGISTRATION_NUMBER_ID=#{registrationNumberId} AND IS_DELETE=0
  </select>

  <select id="getValidTimeByRegistrationNumber"
          resultType="com.vedeng.firstengage.model.GoodsRegisrationExpiryDate">
     SELECT * FROM T_GOODS_REGISTRATION_EXPIRY_DATE
     WHERE REGISTRATION_NUMBER_ID=#{registrationNumberId}
     AND TYPE = #{attachmentFunction}
     AND IS_DELETE=0
  </select>
</mapper>