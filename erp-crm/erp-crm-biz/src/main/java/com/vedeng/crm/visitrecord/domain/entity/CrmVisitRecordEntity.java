package com.vedeng.crm.visitrecord.domain.entity;

import lombok.Data;
import java.util.Date;

/**
 * 拜访记录实体类
 */
@Data
public class CrmVisitRecordEntity {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 拜访记录编号
     */
    private String visitRecordNo;
    
    /**
     * 计划拜访时间
     */
    private Date planVisitDate;
    
    /**
     * 拜访人ID
     */
    private Integer visitorId;
    
    /**
     * 拜访人姓名
     */
    private String visitorName;
    
    /**
     * 拜访目标
     */
    private String visitTarget;
    
    /**
     * 省份编码
     */
    private Integer provinceCode;
    
    /**
     * 省份名称
     */
    private String provinceName;
    
    /**
     * 城市编码
     */
    private Integer cityCode;
    
    /**
     * 城市名称
     */
    private String cityName;
    
    /**
     * 区域编码
     */
    private Integer areaCode;
    
    /**
     * 区域名称
     */
    private String areaName;
    
    /**
     * 拜访地址
     */
    private String visitAddress;
    
    /**
     * 客户名称
     */
    private String customerName;
    
    /**
     * 客户来源
     */
    private Integer customerFrom;
    
    /**
     * 客户性质
     */
    private Integer customerNature;
    
    /**
     * 商户ID
     */
    private Integer traderId;
    
    /**
     * 商户客户ID
     */
    private Integer traderCustomerId;
    
    /**
     * 实际拜访时间
     */
    private Date actualVisitDate;
    
    /**
     * 打卡状态
     */
    private String cardOff;
    
    /**
     * 打卡时间
     */
    private Date cardTime;
    
    /**
     * 图片列表
     */
    private String pictureList;
    
    /**
     * 联系人姓名
     */
    private String contactName;
    
    /**
     * 联系人手机
     */
    private String contactMobile;
    
    /**
     * 联系人电话
     */
    private String contactTele;
    
    /**
     * 联系人职位
     */
    private String contactPosition;
    
    /**
     * 其他联系方式
     */
    private String otherContact;


    /** 拜访记录添加的-联系人信息-姓名 */
    private String recordContactName;

    /** 拜访记录添加的-联系人信息-手机 */
    private String recordContactMobile;

    /** 拜访记录添加的-联系人信息-电话 */
    private String recordContactTele;

    /** 拜访记录添加的-联系人信息-职位 */
    private String recordContactPosition;

    /** 记录添加的其它联系方式 */
    private String recordOtherContact;

    /** 记录添加的无联系人 */
    private String recordNoContract;
    
    /**
     * 是否展示PPT
     */
    private String showPpt;
    
    /**
     * 是否邀请注册
     */
    private String inviteReg;
    
    /**
     * 注册手机号
     */
    private String regMobile;
    
    /**
     * 商户合同ID
     */
    private Integer traderContractId;
    
    /**
     * 沟通内容
     */
    private String commucateContent;
    
    /**
     * 下次拜访时间
     */
    private Date nextVisitDate;
    
    /**
     * 是否创建商机
     */
    private String createBusinessChange;
    
    /**
     * 拜访是否成功
     */
    private String visitSuccess;
    
    /**
     * 添加时间
     */
    private Date addTime;
    
    /**
     * 添加人ID
     */
    private Integer addUserId;
    
    /**
     * 修改时间
     */
    private Date modTime;
    
    /**
     * 修改人ID
     */
    private Integer modUserId;
    
    /**
     * 是否删除
     */
    private Integer isDelete;
    
    /**
     * 拜访记录状态
     */
    private Integer visitRecordStatus;
    
    /**
     * 关闭原因类型
     */
    private Integer closeReasonType;
    
    /**
     * 关闭原因内容
     */
    private String closeReasonContent;
    
    /**
     * 是否无联系人
     */
    private String noContract;
    
    /**
     * 关联类型
     */
    private Integer relateType;
    
    /**
     * 商机ID
     */
    private Integer bussinessChanceId;
    
    /**
     * 商机编号
     */
    private String bussinessChanceNo;
    
    /**
     * 完成时间
     */
    private Date completeDatetime;
    
    /**
     * 关闭时间
     */
    private Date closeDatetime;
    
    /**
     * 备注
     */
    private String remark;
    
    /**
     * 客户需求
     */
    private String customerRequires;
    
    /**
     * 组织ID
     */
    private Integer orgId;
    
    /**
     * 组织分组
     */
    private String orgGroup;
} 