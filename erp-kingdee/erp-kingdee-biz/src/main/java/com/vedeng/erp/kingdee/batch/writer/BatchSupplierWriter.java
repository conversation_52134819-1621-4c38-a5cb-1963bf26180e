package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.repository.BatchSupplierFinanceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.service.KingDeeSupplierApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 11:07
 */
@Service
@Slf4j
public class BatchSupplierWriter extends BaseWriter<KingDeeSupplierDto> {

    @Autowired
    private KingDeeSupplierApiService kingDeeSupplierApiService;

    @Override
    public void doWrite(KingDeeSupplierDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("审计供应商信息推送：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.updateSupplier);
        kingDeeSupplierApiService.register(item,true);
    }
}