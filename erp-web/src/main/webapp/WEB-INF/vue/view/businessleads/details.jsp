<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<%--<script type="text/javascript" src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/mylayer.js'></script>--%>
<style>

</style>
<div id="app" style="display: none;">
    <div style="margin: 15px">
        <el-row>
            <el-col :span="12" style="padding-left: 50px;">
                <span><span v-if="businessLeads.mergeStatus == 2" style="color: red;">[合]</span>{{businessLeads.leadsNo}}</span>
                <span style="margin-left: 30px" v-if="businessLeads.status != null">
                    <el-switch
                            class="switchStyle"
                            v-model="businessLeads.status"
                            :active-value="1"
                            :inactive-value="0"
                            active-text="有效"
                            inactive-text="无效"
                            active-color="#409EFF"
                            inactive-color="#909399"
                            @change="modifyInvalid"
                    >
                    </el-switch>
                    <span v-if="businessLeads.status == 0">{{businessLeads.invalidReason}}</span>
                </span>
            </el-col>
            <el-col :span="12">
                <div style="float: right;padding-right: 50px;">
                    <el-button
                            v-show="(businessLeads.followStatus == 1 || businessLeads.followStatus == 2) && businessLeads.clueType != 394"
                            type="primary" size="mini"
                            @click="editLeadsInfo">
                        编辑
                    </el-button>
                    <el-button
                            v-show="businessLeads.followStatus != 3"
                            type="primary" size="mini" @click="addCommunicateRecord">新增沟通记录
                    </el-button>
                    <el-button
                            v-show="businessLeads.followStatus == 1 || businessLeads.followStatus == 2"
                            type="info" size="mini" @click="closedLeads">关闭线索
                    </el-button>
                    <el-button
                            v-show="businessLeads.followStatus == 1 || businessLeads.followStatus == 2"
                            type="warning" size="mini"
                            @click="leadsConvertor"
                    >转商机
                    </el-button>
                </div>
            </el-col>
        </el-row>

        <el-row>
            <el-descriptions>
                <el-descriptions-item label="线索类型">{{clueTypeText}}</el-descriptions-item>
                <el-descriptions-item label="询价行为">{{inquiryText}}</el-descriptions-item>
                <el-descriptions-item label="渠道类型">{{businessLeads.sourceName}}</el-descriptions-item>
                <el-descriptions-item label="渠道名称">{{businessLeads.communicationName}}</el-descriptions-item>
                <el-descriptions-item label="三级分类">{{businessLeads.content}}</el-descriptions-item>
                <el-descriptions-item label="其他联系方式">{{businessLeads.otherContactInfo}}</el-descriptions-item>
                <el-descriptions-item label="联系人">{{businessLeads.contact}}</el-descriptions-item>
                <el-descriptions-item label="手机/电话">

                    <div style="margin-top: -15px;">
                        <span v-if="businessLeads.phone" @click="call(businessLeads.phone)">
                            {{businessLeads.phone}}<img height="21"
                                                        src="${pageContext.request.contextPath}/static/vue/images/telephone.png">
                        </span>
                        <div>
                            <sapn v-if="businessLeads.telephone" @click="call(businessLeads.telephone)">
                                {{businessLeads.telephone}}<img height="21"
                                                                src="${pageContext.request.contextPath}/static/vue/images/telephone.png">
                            </sapn>
                        </div>
                    </div>

                </el-descriptions-item>
                <el-descriptions-item v-if="businessLeads.traderId == 0" label="客户名称">{{businessLeads.traderName}}
                </el-descriptions-item>
                <el-descriptions-item v-else label="客户名称">
                    <el-button style="padding-top: 3px" type="text" @click="openTraderInfo">
                        {{businessLeads.traderName}}
                    </el-button>
                </el-descriptions-item>


                <el-form-item v-else label="客户名称:" label-width="150px">

                </el-form-item>


                <el-descriptions-item label="地区">
                    <div>
                        <span v-if="businessLeads.province">{{businessLeads.province}}</span>
                        <span v-if="businessLeads.city">-{{businessLeads.city}}</span>
                        <span v-if="businessLeads.county">-{{businessLeads.county}}</span>
                        <div v-if="businessLeads.address">
                            {{businessLeads.address}}
                        </div>
                    </div>
                </el-descriptions-item>
<%--                <el-descriptions-item label="线索来源">销售自拓线索</el-descriptions-item>--%>
                <el-descriptions-item label="线索归属">{{businessLeads.belonger}}</el-descriptions-item>
                <el-descriptions-item label="分配时间">{{parseTime(businessLeads.assignTime)}}</el-descriptions-item>
            </el-descriptions>
        </el-row>
        <el-row>
            <el-descriptions>
                <el-descriptions-item label="跟进状态">
                    <span v-if="businessLeads.followStatus === 1" style="color: #f61c1c">未处理</span>
                    <span v-if="businessLeads.followStatus === 2" style="color: #16780d">跟进中</span>
                    <span v-if="businessLeads.followStatus === 3" style="color: #c2c2c2">
                        已关闭
                    <span>{{businessLeads.closeReason}}</span>
                    </span>
                    <span v-if="businessLeads.followStatus === 4" style="color: #d58c0f">已商机</span>
                </el-descriptions-item>
            </el-descriptions>
        </el-row>
        <el-row>
            <el-descriptions>
                <el-descriptions-item label="产品信息">
                    {{businessLeads.goodsInfo}}
                </el-descriptions-item>
            </el-descriptions>
        </el-row>

<%--        <el-row>--%>
<%--            <el-descriptions>--%>
<%--                <el-descriptions-item label="线索标签">--%>
<%--                    <span v-for="(item,inedx) in leadsTagList" :label="item.id" :key="inedx" style="margin-right: 10px">--%>
<%--                        <el-tag :style="{color: item.cssClass,borderColor:item.cssClass}" effect="plain">--%>
<%--                            {{item.name}}--%>
<%--                        </el-tag>--%>
<%--                    </span>--%>

<%--                </el-descriptions-item>--%>
<%--            </el-descriptions>--%>
<%--        </el-row>--%>

        <el-row>
            <el-descriptions>
                <el-descriptions-item label="备注信息">
                    {{businessLeads.remark}}
                </el-descriptions-item>
            </el-descriptions>
        </el-row>

        <el-row>
            <el-descriptions>
                <el-descriptions-item label="跟进图片" >
                    <div style="width:80%;word-wrap:break-word;">
                        <span  v-for="fit in businessLeads.followPicList" :key="fit">
                            <el-image
                                    style="width: 100px; height: 100px;margin-left: 20px"
                                    :src="fit"
                                    :preview-src-list="[fit]"
                            ></el-image>
                        </span>
                    </div>

                </el-descriptions-item>
            </el-descriptions>
        </el-row>

        <el-row>
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="跟进情况" name="first">

                    <div style="margin: 10px">
                        <el-row v-show="businessLeads.followStatus != 3">
                            <el-button type="text" @click="addCommunicateRecord">+ 新增沟通记录</el-button>
                        </el-row>


                        <el-row v-for="item in communicateRecordList" :key="item.communicateRecordId">
                            <div style="border: 1px solid #d7dae2;border-radius: 0px">
                                <el-row>
                                    <div style="margin: 10px;" class="fontStyle">
                                        <el-col :span="12">
                                            <div>
                                                {{ item.creatorName }}
                                            </div>
                                        </el-col>
                                        <el-col :span="12">
                                            <div style="float: right">
                                                {{parseTime(item.begintime, '{y}-{m}-{d} {h}:{i}')}}
                                            </div>
                                        </el-col>
                                    </div>
                                </el-row>

                                <div>
                                    <div style="margin: 0 10px 0 50px;line-height: 28px" class="fontStyle">
                                        <div>
                                            {{ item.communicateGoalName}}{{ item.communicateModeName}}{{ item.contactContent}}{{ item.contentSuffix}}
                                        </div>
                                        <div v-if="item.nextContactDate != null">
                                            下次沟通时间 : &nbsp;{{parseTime(item.nextContactDate, '{y}-{m}-{d}')}}
                                        </div>
                                        <div v-if="item.nextContactContent != null">
                                            下次沟通内容: &nbsp; {{ item.nextContactContent }}
                                        </div>
                                    </div>
                                </div>

                                <div style="margin: 0 20px 20px 20px">
                                    <el-divider></el-divider>
                                    <span v-if="item.coid !=null">
                                        <img @click="playrecord(item.coidUri)"
                                             src="${pageContext.request.contextPath}/static/vue/images/trumpet.png" style="height: 20px">
                                    </span>
                                    <span>
                                        <span v-if="item.traderContactId != null" class="fontStyle">
                                            {{ item.contactName }}&nbsp;{{ item.contactDepartment
                                            }}&nbsp;{{ item.contactPosition }}
                                        </span>
                                        <span v-else style="height: 30px">
                                            {{ item.contact }}
                                        </span>
                                    </span>
                                </div>

                            </div>
                        </el-row>


                        <el-row v-if="communicateRecordTotalNum > 3">
                            <div style="float: right">
                                <el-button v-if="showMoreFalg" type="text" @click="getMoreCommunicateRecord">查看更多跟进情况
                                </el-button>
                            </div>

                        </el-row>
                    </div>

                </el-tab-pane>
                <el-tab-pane label="客户属性" name="second">
                    <el-row>
                        <div style="border: 1px solid #d7dae2;border-radius: 0px">
                            <el-row>
                                <div style="margin-top: 20px;padding-left: 20px">交易信息</div>
                                <el-divider></el-divider>
                                <el-descriptions>
                                    <el-descriptions-item label="历史交易总额">{{crmTraderInfo.totalAmount}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="历史交易次数">{{crmTraderInfo.totalBuyNum}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="订单平均金额">{{crmTraderInfo.averageAmount}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="线上订单数量">{{crmTraderInfo.onlineOrderNum}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="线下订单数量">{{crmTraderInfo.offlineOrderNum}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="线上订单比例">{{crmTraderInfo.onlineOrderRate}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="初次交易时间">{{crmTraderInfo.firstBusinessTime}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="最近交易时间">{{crmTraderInfo.lastBusinessTime}}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="消费频次">{{crmTraderInfo.consumptionFrequency}}
                                    </el-descriptions-item>
                                </el-descriptions>
                                <el-descriptions>
                                    <el-descriptions-item label="经营品牌">{{crmTraderInfo.brands}}</el-descriptions-item>
                                </el-descriptions>
                                <el-descriptions>
                                    <el-descriptions-item label="经营分类">{{crmTraderInfo.classification}}
                                    </el-descriptions-item>
                                </el-descriptions>
                                <el-descriptions>
                                    <el-descriptions-item label="经营科室">{{crmTraderInfo.department}}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </el-row>
                        </div>
                    </el-row>

                    <el-row>
                        <div style="border: 1px solid #d7dae2;border-radius: 0px">
                            <el-row>
                                <div style="margin-top: 20px;padding-left: 20px">中标信息</div>
                                <el-divider></el-divider>

                                <el-table
                                        :data="bidData"
                                        style="width: 100%"
                                        row-key="infoId"
                                        border
                                        :tree-props="{children: 'otherGoodsInfo', hasChildren: 'hasChildren'}">
                                    <el-table-column
                                            label="招标信息"
                                            width="120">
                                        <el-table-column
                                                label="招标单位"
                                                prop="zhaobiaoUnit">
                                        </el-table-column>
                                        <el-table-column
                                                label="地区"
                                                prop="zhaobiaoArea">
                                        </el-table-column>
                                        <el-table-column
                                                label="单位性质"
                                                prop="zhaobiaoXingzhi">
                                        </el-table-column>
                                        <el-table-column
                                                label="单位等级"
                                                prop="zhaobiaoLevel">
                                        </el-table-column>
                                        <el-table-column
                                                label="单位类型"
                                                prop="zhaobiaoType">
                                        </el-table-column>
                                    </el-table-column>

                                    <el-table-column
                                            label="中标商品"
                                            width="120">
                                        <el-table-column
                                                label="商品"
                                                prop="zhongbiaoGoods">
                                        </el-table-column>
                                        <el-table-column
                                                label="品牌"
                                                prop="zhongbiaoBrand">
                                        </el-table-column>
                                        <el-table-column
                                                label="型号"
                                                prop="zhongbiaoModel">
                                        </el-table-column>
                                    </el-table-column>

                                    <el-table-column
                                            prop="name"
                                            label="操作"
                                            width="120">
                                        <template slot-scope="scope">
                                            <el-button v-if="scope.row.zhaobiaoUnit" type="text" size="small" @click="viewZhaoBiao(scope.row.infoId)">查看招投标</el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-row>
                        </div>
                    </el-row>


                </el-tab-pane>
                <el-tab-pane v-if="businessLeads.mergeStatus == 2" label="线索合并" name="three">
                    <template>
                        <el-table
                                :data="mergeTableData"
                                border
                                style="width: 90%">
                            <el-table-column
                                    prop="leadsNo"
                                    label="线索编号"
                                    width="200">
                            </el-table-column>
                            <el-table-column
                                    prop="belonger"
                                    label="归属销售"
                                    width="150">
                            </el-table-column>
                            <el-table-column
                                    prop="threeClass"
                                    label="三级分类"
                                    width="300">
                            </el-table-column>
                            <el-table-column
                                    prop="inquireSku"
                                    label="产品名称">
                            </el-table-column>
                            <el-table-column
                                    prop="inquireType"
                                    label="线索类型"
                                    width="200">
                            </el-table-column>
                            <el-table-column
                                    prop="inquireTime"
                                    label="询问时间"
                                    width="200">
                            </el-table-column>
                        </el-table>
                    </template>
                </el-tab-pane>
            </el-tabs>
        </el-row>
    </div>

    <el-dialog title="沟通记录" :visible.sync="dialogFormVisible" top="20px">
        <el-form :model="communicateRecordDto" ref="communicateRecordFrom" :rules="rules">
            <div v-show="businessLeads.traderId!= 0">
                <el-form-item label="客户名称:" label-width="150px">
                    {{businessLeads.traderName}}
                </el-form-item>

                <el-form-item label="联系人:" label-width="150px" prop="traderContactNameView" :rules="this.businessLeads.traderId !== 0 ? rules.traderContactNameView: [{required: false}]">
                    <el-select
                            v-model="communicateRecordDto.traderContactNameView"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择联系人"
                            style="width: 300px;"
                            @clear="traderContactClearComm"
                    >
                        <el-option
                                v-for="item in traderConcatDatas"
                                :key="item.traderContactId"
                                :label="item.name"
                                :value="item.traderContactId"
                                @click.native="traderConcatSelectComm(item)"
                        >
                            <span style="float: left">{{ item.name + ' ' + item.mobile + ' ' + item.telephone }}</span>
                        </el-option>

                    </el-select>
                    <span class="title-click  bg-light-bule pop-new-data bt-small mr10 bt-bg-style J-add-prod"
                          id="concat" onclick="addTraderContract()"
                          style="margin-left:24px;color: #3384ef;padding-right: 10px;cursor: pointer;height: 34px;line-height: 34px;"
                          :layerParams="'{&quot;width&quot;:&quot;50%&quot;,&quot;height&quot;:&quot;75%&quot;,&quot;title&quot;:&quot;添加联系人&quot;,&quot;link&quot;:&quot;/orderstream/saleorder/addContact.do?indexId=3&amp;traderId='+businessLeads.traderId+'&quot;}'"><span
                            v-show="businessLeads.traderId!=0">添加联系人</span></span>

                </el-form-item>

            </div>

            <div v-show="businessLeads.traderId== 0">
                <el-form-item label="联系人:" label-width="150px" prop="contact" :rules="businessLeads.traderId === 0 ? rules.contact: [{required: false}]">
                    <el-input v-model="communicateRecordDto.contact" placeholder="请输入联系人"
                              style="width: 300px;"></el-input>
                </el-form-item>

                <el-form-item label="联系电话:" label-width="150px" prop="contactMob" :rules="businessLeads.traderId === 0 ? rules.contactMob: [{required: false}]">
                    <el-input v-model="communicateRecordDto.contactMob" placeholder="请输入联系电话"
                              style="width: 300px;"></el-input>
                </el-form-item>
            </div>

            <el-form-item label="沟通时间:" label-width="150px" prop="time">
                <el-date-picker
                        v-model="communicateRecordDto.time"
                        type="datetimerange"
                        value-format="timestamp"
                        range-separator="至"
                        start-placeholder="沟通开始时间"
                        end-placeholder="沟通结束时间">

                </el-date-picker>
            </el-form-item>

            <el-form-item label="沟通内容:" label-width="150px" prop="contentSuffix">
                <el-input
                        v-model="communicateRecordDto.contentSuffix"
                        type="textarea"
                        placeholder="沟通内容最多输入200个汉字"
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="下次沟通时间:" label-width="150px" prop="nextContactDate">
                <el-date-picker
                        v-model="communicateRecordDto.nextContactDate"
                        type="date"
                        :disabled="communicateRecordDto.noneNextDate"
                        placeholder="选择日期时间">
                </el-date-picker>
                <el-checkbox v-model="communicateRecordDto.noneNextDate"
                             @change="communicateRecordNextBind">暂无下次沟通记录
                </el-checkbox>
            </el-form-item>

            <el-form-item label="下次沟通内容:" label-width="150px" prop="nextContactContent">
                <el-input
                        v-model="communicateRecordDto.nextContactContent"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="6"
                >
                </el-input>
            </el-form-item>

            <el-form-item label="备注:" label-width="150px" prop="comments">
                <el-input
                        v-model="communicateRecordDto.comments"
                        type="textarea"
                        placeholder=""
                        style="width: 500px"
                        maxlength="200"
                        rows="3"
                >
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer" style="text-align: center">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="submitCommunicateRecord('communicateRecordFrom')">确 定</el-button>
        </div>
    </el-dialog>
</div>


<script src="${pageContext.request.contextPath}/static/api/trader/businessLeads.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/communicateRecord.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderContact.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/customTag.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/region.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/user.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const leadsInfo = {
        leadsId: ${leadsId}
    }
    let vm0 = null
    const sendThis0 = (_this) => {
        vm0 = _this;
    }

    new Vue({
        el: '#app',
        data() {
            var dateTime = new Date().getTime();
            var dateTimeEnd = new Date(dateTime + 1000 * 60 * 2).getTime();
            return {
                dialogFormVisible: false,
                formLabelWidth: '120px',
                rules: {
                    'nextContactDate': [
                        {type: 'date', required: true, message: '请输入下次沟通时间', trigger: 'blur'}
                    ],
                    'time': [
                        {type: 'array', required: true, message: '请输入沟通时间', trigger: ['blur']}
                    ],
                    'contentSuffix': [
                        {required: true,  pattern: /[^\s]/, message: '请输入沟通内容', trigger: 'blur'}
                    ],
                    'contact': [
                        {required: true, trigger: 'blur' ,validator: this.validateName},
                    ],
                    'contactMob': [
                        {required: true, trigger: 'blur', validator: this.validatePhone},
                    ],
                    traderContactNameView: [{required: true, message: '请选择联系人', trigger: ['change', 'blur']}],
                },
                businessLeads: {
                    leadsNo: 'L12321323',
                    status: 0,
                    invalidReason: 'hhhhhhhhh',
                    contact: '张良',
                    phone: '1888888888',
                    telephone: '05228999',
                    traderName: '',
                    province: '',
                    city: '',
                    county: '',
                    address: '',
                    type: '',
                    belonger: '',
                    followStatus: '',
                    goodsInfo: '',
                    tagIdList: [],
                    remark: '',
                    followPicList: [],
                    closeReason: '',
                    mergeStatus: 0
                },
                //新增沟通记录
                communicateRecordDto: {
                    traderContactNameView: '',
                    relatedId: '',
                    traderId: '',
                    companyId: 1,
                    traderContactId: '',
                    contact: null,
                    time: [dateTime, dateTimeEnd],
                    begintime: null,
                    endtime: null,
                    noneNextDate: false,
                    isLfasr: 0,
                    contactMob: null,
                    contentSuffix: null,
                    communicateType: 4109,
                    nextContactDate: null
                },
                traderConcatDatas: [],

                communicateRecordList: [],

                //查看更多
                communicateRecordTotalNum: '',

                showMoreFalg: true,

                activeName: 'first',
                crmTraderInfo: '',
                bidData: [],
                //自定义标签值
                leadsTagList: [],
                mergeTableData: []

            };
        },
        created() {
            //获取线索信息
            getLeadsDetails({"id": leadsInfo.leadsId}).then(res => {
                this.businessLeads = res.data.data;
                if (this.businessLeads.tagIdList) {
                    getBusinessTags({"type": "1"}).then((result) => {
                        for (let i = 0; i < this.businessLeads.tagIdList.length; i++) {
                            for (let j = 0; j < result.data.data.length; j++) {
                                if (this.businessLeads.tagIdList[i] === result.data.data[j].id) {
                                    this.leadsTagList.push(result.data.data[j])
                                }
                            }
                        }
                    });
                }
            });

            //获取沟通记录
            getCommunicateRecord(
                {
                    "param": {"communicateType": 4109, "relatedId": leadsInfo.leadsId},
                    "orderBy": 'COMMUNICATE_RECORD_ID desc',
                    "pageSize": 3
                }
            ).then(res => {
                this.communicateRecordList = res.data.data.list;
                this.communicateRecordTotalNum = res.data.data.total;
            });

            //更新首次跟进时间
            updateLeadsFirstFollowTime({"id": leadsInfo.leadsId});

            sendThis0(this);

        },

        mounted() {
            loadingApp()
        },

        methods: {
            modifyInvalid() {
                if (this.businessLeads.status === 0) {
                    this.$prompt('', '无效原因', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputPlaceholder: '请填写无效原因',

                        inputPattern: /[^\s]/,
                        inputErrorMessage: '请填写无效的原因'
                    }).then(({value}) => {
                        updateLeadsStatus({
                            "id": leadsInfo.leadsId,
                            "status": this.businessLeads.status,
                            "invalidReason": value.trim()
                        });
                        this.businessLeads.invalidReason = value.trim();
                    }).catch(() => {
                        this.businessLeads.status = 1;
                    });
                }
                if (this.businessLeads.status === 1) {
                    updateLeadsStatus({"id": leadsInfo.leadsId, "status": this.businessLeads.status, "invalidReason": ''});
                    this.businessLeads.invalidReason = '';
                }
            },
            closedLeads() {
                this.$prompt('', '关闭原因', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    inputPlaceholder: '请填写关闭原因',
                    inputPattern: /[^\s]/,
                    inputValidator: this.closeReasonInputValidator,
                    inputErrorMessage: '请填写关闭的原因'
                }).then(({value}) => {
                    closedLeads({
                        "id": leadsInfo.leadsId,
                        "followStatus": 3,
                        "closeReason": value.trim()
                    });
                    this.businessLeads.followStatus = 3;
                    this.businessLeads.closeReason = value.trim();
                }).catch((e) => {
                    console.log(e)
                });

            },
            closeReasonInputValidator(value){
               if (value.length > 50){
                   return "最多支持50个字"
               }
                return true;
            },
            validateName(rule, value, callback) {
                if (this.businessLeads.traderId !== 0) {
                    callback();
                    return;
                }
                if (value ==="" || value === undefined || value === null || value.length === 0) {
                    callback(new Error('请输入联系人'));
                }else if(value.length > 20)  {
                    callback(new Error('联系人姓名不能超过20个字符'));
                }else {
                    callback();
                }
            },
            validatePhone(rule, value, callback) {
                if (this.businessLeads.traderId !== 0) {
                    callback();
                    return;
                }
                if (value === '') {
                    callback(new Error('请输入联系电话'));
                    retrun;
                }
                var phoneReg = /(^1[3|4|5|6|7|8|9]\d{9}$)|(^09\d{8}$)/;
                if (!phoneReg.test(value)) {
                    callback(new Error('请输入正确的电话号码'));
                } else {
                    callback();
                }
            },
            handleClick(tab, event) {
                if (this.businessLeads.traderId !== 0 && tab.index === '1') {
                    getBidInfoByTraderId({'traderId': this.businessLeads.traderId}).then((result) => {
                        this.bidData = result.data.data.data;
                    });
                    getTraderInfoByTraderId({'traderId': this.businessLeads.traderId}).then((result) => {
                        this.crmTraderInfo = result.data.data.data;
                    });
                }
                if (tab.index === '2') {
                    getLeadMerge( this.businessLeads.leadsNo ).then((data) => {
                        if(data.data.success){
                            this.mergeTableData = data.data.data;
                        }
                    });
                }
            },

            //新增沟通记录页面====begin
            addCommunicateRecord() {
                this.dialogFormVisible = true;
                this.communicateRecordDto.relatedId = leadsInfo.leadsId
                if (this.businessLeads.traderId !== 0) {
                    this.communicateRecordDto.traderId = this.businessLeads.traderId
                    getTraderContactDat(
                        {
                            "param": {"traderId": this.businessLeads.traderId},
                            "orderBy": 'TRADER_CONTACT_ID desc',
                            "pageSize": 1000
                        }
                    ).then(res => {
                        this.traderConcatDatas = res.data.data.list
                        console.log(this.traderConcatDatas)
                    });
                } else {
                    var array = []
                    if (this.businessLeads.contact != null
                        && this.businessLeads.contact !== ''
                        && this.businessLeads.phone != null
                        && this.businessLeads.phone !== '') {
                        let mob = {
                            traderContactId: 1,
                            name: this.businessLeads.contact,
                            mobile: this.businessLeads.phone,
                        };
                        array.push(mob)
                    }
                    if (this.businessLeads.contact != null
                        && this.businessLeads.contact !== ''
                        && this.businessLeads.telephone != null
                        && this.businessLeads.telephone !== '') {
                        let data = {
                            traderContactId: 2,
                            name: this.businessLeads.contact,
                            mobile: this.businessLeads.telephone
                        };
                        array.push(data)
                    }
                    this.traderConcatDatas = array;
                    this.communicateRecordDto.contact = this.businessLeads.contact;
                    this.communicateRecordDto.contactMob = this.businessLeads.phone;
                    console.log(this.traderConcatDatas)
                }
            },
            submitCommunicateRecord(form) {
                this.$refs[form].validate((valid) => {
                    console.log(1,valid)
                    if (valid) {
                        if (this.communicateRecordDto.noneNextDate) {
                            this.communicateRecordDto.noneNextDate = 1;
                        } else {
                            this.communicateRecordDto.noneNextDate = 0;
                        }
                        if (this.communicateRecordDto.time.length > 1) {
                            this.communicateRecordDto.begintime = this.communicateRecordDto.time[0]
                            this.communicateRecordDto.endtime = this.communicateRecordDto.time[1]
                        }
                        console.log(this.communicateRecordDto)
                        if(this.businessLeads.traderId == 0){
                            this.communicateRecordDto.traderContactNameView = this.communicateRecordDto.contact + " " + this.communicateRecordDto.contactMob
                        }
                        addCommunicateRecord(this.communicateRecordDto).then(res => {
                            this.dialogFormVisible = false;
                            updateLeadsFirstFollowStatus({"id": leadsInfo.leadsId});
                            this.traderContactClearComm();
                            debugger
                            //setTimeout(location.reload(),500);
                            setTimeout(function () {
                                location.reload()
                            }, 500);
                        }).catch((e) => {
                            console.log(e)
                        });
                    }
                })
            },
            traderConcatSelectComm(item) {
                if (this.businessLeads.traderId !== 0) {
                    this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile + " " + item.telephone
                    this.communicateRecordDto.traderContactId = item.traderContactId

                } else {
                    this.communicateRecordDto.traderContactNameView = item.name + " " + item.mobile
                    this.communicateRecordDto.contact = item.name
                    this.communicateRecordDto.contactMob = item.mobile
                }
            },
            communicateRecordNextBind() {
                if (this.communicateRecordDto.noneNextDate) {
                    this.communicateRecordDto.nextContactDate =null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            // type: 'date',
                            required: false,
                            message: '',
                            trigger: 'change'
                        }]
                    };
                } else {
                    this.communicateRecordDto.nextContactDate =null;
                    this.rules = {
                        ...this.rules,
                        'nextContactDate': [{
                            // type: 'date',
                            required: true,
                            message: '请输入下次沟通时间',
                            trigger: 'change'
                        }]
                    };
                }
            },
            traderContactClearComm() {
                this.communicateRecordDto.traderContactNameView = null;
                this.communicateRecordDto.traderContactId = null;
                this.communicateRecordDto.contact = null;
                this.communicateRecordDto.contactMob = null;
            },
            //新增沟通记录页面====end

            getMoreCommunicateRecord() {
                this.showMoreFalg = false;
                //获取更多沟通记录
                getCommunicateRecord(
                    {
                        "param": {"communicateType": 4109, "relatedId": leadsInfo.leadsId},
                        "orderBy": 'COMMUNICATE_RECORD_ID desc',
                        "pageSize": 1000
                    }
                ).then(res => {
                    this.communicateRecordList = res.data.data.list;
                });

            },



            //播放录音
            playrecord(url) {
                // console.log(data);
                debugger
                if (url != '') {
                    debugger
                    layer.open({
                        type: 2,
                        shadeClose: false, //点击遮罩关闭
                        area: ['360px', '80px'],
                        title: false,
                        content: ['/system/call/getrecordpaly.do?url=' + url],
                        success: function (layero, index) {
                            //layer.iframeAuto(index);
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                            }
                        }
                    });
                }
            },
            call(phone) {
                //phone,traderId,traderType,callType,orderId,traderContactId
                updateLeadsFirstFollowStatus({"id": vm0.businessLeads.id});
                callout(
                    phone,
                    this.businessLeads.traderId,
                    this.businessLeads.traderId !== 0 ? 1 : 0,
                    7,
                    this.businessLeads.id,
                    this.businessLeads.traderContactId);

            },
            openTraderInfo() {
                openTab("客户信息", '/trader/customer/baseinfo.do?traderId=' + this.businessLeads.traderId);
            },

            editLeadsInfo() {
                openTab("编辑线索", '/businessLeads/edit.do?id=' + leadsInfo.leadsId)
                this.closeThis()
            },
            leadsConvertor() {
                openTab("新增商机", '/businessChance/leadsConvertor.do?businessLeadsId=' + leadsInfo.leadsId)
                this.closeThis()
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            viewZhaoBiao(infoId){
                openTab("查看招投标", 'https://www.vedeng.com/zhaobiao/detail-' + infoId + '.html')
            },
        },

        computed: {
            clueTypeText() {
                switch (this.businessLeads.clueType) {
                    case 391:
                        return '总机询价';
                    case 394:
                        return '自主询价';
                    default:
                        return '';
                }
            },
            inquiryText() {
                switch (this.businessLeads.inquiry) {
                    case 4001:
                        return '即时通讯';
                    case 4002:
                        return '电话';
                    case 4003:
                        return '留言';
                    default:
                        return '';
                }
            }
        }

    });


    function updateTrader() {
        debugger
        let data = {
            "param": {"traderId": vm0.businessLeads.traderId},
            "orderBy": 'TRADER_CONTACT_ID desc',
            "pageSize": 1000
        }
        getTraderContactDat(data).then(res => {
            vm0.traderConcatDatas = res.data.data.list;
            if (vm0.traderConcatDatas.length > 0) {
                vm0.communicateRecordDto.traderContactNameView = vm0.traderConcatDatas[0].name + " " + vm0.traderConcatDatas[0].mobile + " " + vm0.traderConcatDatas[0].telephone
                vm0.communicateRecordDto.traderContactId = vm0.traderConcatDatas[0].traderContactId
            }
        })

    }




</script>

<style>
    .el-row {
        margin-bottom: 10px;
    }

    .switchStyle .el-switch__label {
        position: absolute;
        display: none;
        color: #fff;
    }

    .switchStyle .el-switch__label--left {
        z-index: 9;
        left: 24px;
    }

    .switchStyle .el-switch__label--right {
        z-index: 9;
        left: -4px;
    }

    .switchStyle .el-switch__label.is-active {
        display: block;
    }

    .switchStyle.el-switch .el-switch__core,
    .el-switch .el-switch__label {
        width: 60px !important;
    }

    .el-descriptions-item__label.has-colon {
        width: 130px;
        justify-content: flex-end;
    }

    .cell {
        text-align: center;
    }


    .primaryButton {
        color: #FFF;
        background-color: #409EFF;
        border-color: #409EFF;
        font-size: 12px;
        border-radius: 3px;
    }

    .fontStyle{
        font-size: 14px;
        color: #606266;
    }

</style>