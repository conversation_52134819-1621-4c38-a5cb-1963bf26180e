package com.vedeng.finance.dao;

import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.vo.CapitalBillVo;
import com.vedeng.homepage.model.vo.EchartsDataVo;
import com.vedeng.order.model.Saleorder;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName CapitalBillMapper.java
 * @Description TODO
 * @createTime 2020年09月24日 18:18:00
 */
@Named("capitalBillMapper")
public interface CapitalBillMapper {

    int insert(CapitalBill record);

    int insertSelective(CapitalBill record);

    CapitalBill selectByPrimaryKey(Integer capitalBillId);

    int updateByPrimaryKeySelective(CapitalBill record);

    int updateByPrimaryKey(CapitalBill record);

    /**
     * <b>Description:</b><br>
     * 获取订单账期还款信息
     *
     * @param orderId
     *            对应订单ID
     * @param orderType
     *            销订单类型 1销售订单 2采购订单 3售后订单
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年9月6日 下午2:53:59
     */
    List<CapitalBill> getPeriodCapitalBill(@Param("orderId") Integer orderId, @Param("orderType") Integer orderType);

    /**
     * <b>Description:</b><br>
     * 获取订单账期支付信息
     *
     * @param orderId
     *            对应订单ID
     * @param orderType
     *            销订单类型 1销售订单 2采购订单 3售后订单
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年9月6日 下午2:53:59
     */
    List<CapitalBill> getPeriodPayCapitalBill(@Param("orderId") Integer orderId, @Param("orderType") Integer orderType);

    /**
     * <b>Description:</b><br>
     * 获取资金流水列表（不分页）
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年9月15日 下午1:34:39
     */
    List<CapitalBill> getCapitalBillList(CapitalBill capitalBill);


    /**
     * 根据订单号去查询资金流水
     * @return
     */
    List<CapitalBill> getCapitalBillListByOrderNo(BankBillExtDo bankBillExtDo);

    /**
     * 根据订单号去查询资金流水
     *
     * @param orderNo
     * @return
     */
    List<CapitalBill> matchAlipayCapitalBillByCapitalSearchFlow(@Param("capitalSearchFlow") String capitalSearchFlow);

    /**
     * <b>Description:</b><br>
     * 获取资金流水列表（分页）
     *
     * @param map
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年9月15日 下午1:36:29
     */
    List<CapitalBill> getCapitalBillListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 获取付款记录（分页）
     *
     * @param map
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年9月25日 下午6:09:06
     */
    List<CapitalBill> getPaymentRecordListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 获取收款记录（分页）
     *
     * @param map
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年9月25日 下午6:09:25
     */
    List<CapitalBill> getCollectionRecordListPage(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 根据销售订单ID查询订单账期付款额
     *
     * @param saleorderIdList
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2017年9月27日 下午6:21:30
     */
    List<CapitalBill> getCapitalListBySaleorderId(@Param(value = "saleorderIdList") List<Integer> saleorderIdList);

    /**
     *
     * <b>Description:</b><br>
     * 根据销售ID获取销售订单已付款金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> Michael <br>
     *       <b>Date:</b> 2017年9月28日 下午4:11:15
     */
    Saleorder getSaleorderCapitalById(Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 获取资金流水(收入/支出)统计
     *
     * @param map
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年10月25日 下午5:57:42
     */
    List<Map<String, Object>> getCapitalBillTotal(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 获取付款记录订单已付款金额
     *
     * @param map
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年10月26日 上午11:29:28
     */
    BigDecimal getOrderPaymentTotalAmount(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 获取付款记录本次到款金额
     *
     * @param map
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年10月26日 上午11:34:43
     */
    BigDecimal getThisPaymentTotalAmount(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 获取今年完成的数据
     *
     * @param echartsDataVo
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月24日 下午5:51:03
     */
    List<BigDecimal> getEchartDataVoComplete(EchartsDataVo echartsDataVo);

    /**
     * <b>Description:</b><br>
     * 获取去年完成的数据
     *
     * @param echartsDataVo
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月24日 下午5:51:03
     */
    List<BigDecimal> getEchartDataVoCompleteLastYear(EchartsDataVo echartsDataVo);

    /**
     * <b>Description:</b><br>
     * 查询销售工程师的本月到款额
     *
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月30日 上午9:48:07
     */
    BigDecimal getSaleEngineerThisMonthMoney(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 查询销售工程师的所有到款额
     *
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月30日 上午9:48:07
     */
    BigDecimal getSaleEngineerAllMoney(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 查询销售工程师成交的客户总数
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月30日 下午3:11:50
     */
    List<Integer> getSaleEngineerTurnoverCustomers(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 查询销售工程师多次成交的客户数
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月30日 下午3:11:50
     */
    List<Integer> getSaleEngineerManyTurnoverCustomers(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 获取待同步的交易流水记录
     *
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2017年12月4日 下午5:05:37
     */
    List<CapitalBill> waitSyncCapitalBillList();

    /**
     * <b>Description:</b><br>
     * 获取流水
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2018年1月16日 上午11:45:10
     */
    CapitalBillVo getCapitalBillById(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 查询售后订单收款金额
     *
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2017年11月30日 上午9:48:07
     */
    BigDecimal getAftersaleServiceAmountBill(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 查询支付宝提现金额
     *
     * @param saleorderId
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2018年6月5日 下午4:08:13
     */
    BigDecimal getzhifubaoAmount(@Param(value = "saleorderId") Integer saleorderId);

    /**
     * <b>Description:</b><br>
     * 销售订单/采购订单 资金流水判断是否有信用支付
     *
     * @param orderType
     * @param relatedId
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2018年5月7日 下午3:09:04
     */
    Integer isAccountPeriod(@Param(value = "orderType") Integer orderType,
                            @Param(value = "relatedId") Integer relatedId);

    /**
     * <b>Description:</b><br>
     * 查询对应售后的退款记录
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2018年5月10日 下午2:04:36
     */
    List<CapitalBill> getAfterReturnCapitalBillList(CapitalBill capitalBill);

    /**
     * <b>Description:</b><br>
     * 客户列表中查询交易时间的traderidlist
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2018年5月11日 下午1:17:37
     */
    List<Integer> getCapitalBillTreaderIdList(@Param("startTime") Long startTime, @Param("endTime") Long endTime);

    /**
     * <b>Description:</b><br>
     * 已收款列表记录-查询总数计算
     *
     * @param map
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2018年9月5日 上午9:38:49
     */
    Map<String, Object> getNewCollectionRecordCount(Map<String, Object> map);

    /**
     * <b>Description:</b><br>
     * 付款记录列表-按新要求修改
     *
     * @param map
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2018年9月5日 下午4:08:09
     */
    List<CapitalBill> getNewPaymentRecordListPage(Map<String, Object> map);


    /**
     * <b>Description:</b><br>
     * 已付款列表记录-查询总数计算
     *
     * @param map
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2018年9月5日 下午5:53:27
     */
    Map<String, Object> getNewPaymentRecordCount(Map<String, Object> map);


    /**
     * <b>Description:</b> 查询交易流水（耗材商城）
     *
     * @param capitalBill
     * @return CapitalBill
     * @Note <b>Author：</b> lijie <b>Date:</b> 2018年11月5日 下午3:05:55
     */
    CapitalBill getCapitalBillByTranFlow(CapitalBill capitalBill);

    /**
     *
     * <b>Description:根据订单号获取支付方式</b>
     *
     * @param saleorderNo
     * @return String
     * @Note <b>Author：</b> cooper.xu <b>Date:</b> 2018年11月26日 下午5:45:52
     */
    Integer getTradeModeByOrderNo(String saleorderNo);

    /**
     * 根据订单的ID获取订单的最新交易时间
     *
     * @param saleorderNo
     * @return
     */
    long getLastestTime(String saleorderNo);

    void updateCapitalBill(@Param("bankBillId") Integer bankBillId, @Param("capitalBillId") Integer capitalBillId);


    void updateCapitalBillByBankBillIdAndTranFlow(@Param("bankBillId") Integer bankBillId, @Param("capitalBillId") Integer capitalBillId, @Param("tranFlow") String tranFlow);

    void batchUpdateCapitalBill(@Param("bankBillId") Integer bankBillId, @Param("capitalBillIdList") List<Integer> capitalBillIdList);


    /**
     * 获取订单的支付金额（现金支付+账期-售后）
     * @param saleOrderId 订单号
     * @return 流水
     */
    BigDecimal getPayedAmountOfSaleorder(@Param("saleOrderId") Integer saleOrderId);

    /**
     * @Description 查询销售账期订单（先货后款，预付%0）是否有信用支付记录
     * @Param saleOrderId
     * @Return {@link int}
     */
    int getSaleOrderCreditPaymentRecord(@Param("saleOrderId") Integer saleOrderId);

    /**
     * <b>Description:</b><br>
     * 查询对应售后的退款记录
     *
     * @param capitalBill
     * @return
     * @Note <b>Author:</b> duke <br>
     *       <b>Date:</b> 2018年5月10日 下午2:04:36
     */
    List<CapitalBill> queryBuyorderExpenseAfterReturnCapitalBillList(CapitalBill capitalBill);
}
