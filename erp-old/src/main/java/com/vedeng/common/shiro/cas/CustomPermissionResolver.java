package com.vedeng.common.shiro.cas;

import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.authz.Permission;
import org.apache.shiro.authz.permission.PermissionResolver;

import java.util.LinkedList;
import java.util.StringTokenizer;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CustomPermissionResolver implements PermissionResolver {

    private static final String DELIMITER = "/";

    @Override
    public Permission resolvePermission(String permissionStr) {
        if (StringUtils.isBlank(permissionStr)) {
            return NonPermission.getInstance();
        }

            StringTokenizer stringTokenizer = new StringTokenizer(permissionStr.trim(), DELIMITER, false);

        LinkedList<String> parts = new LinkedList<>();
        while (stringTokenizer.hasMoreElements()) {
            String s1 = stringTokenizer.nextToken();
            parts.add(s1);
        }

        return new CustomPermission(parts);
    }


    private static class NonPermission implements Permission {

        private final static NonPermission INSTANCE = new NonPermission();

        private NonPermission() {
        }

        public static NonPermission getInstance() {
            return INSTANCE;
        }


        @Override
        public boolean implies(Permission p) {
            return false;
        }
    }

}
