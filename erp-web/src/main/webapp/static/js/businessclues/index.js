function viewRemark(remark) {
    if (remark == '') {
        remark = '暂无!';
    }
	layer.alert(remark);
}

function changeTopStatus(businessCluesId, status) {

    $.ajax({
        url: '/business/clues/changeTopStatus.do',
        data:{"businessCluesId":businessCluesId,"top":status},
        type:"POST",
        dataType : "json",
        async: false,
        success: function(){
            window.location.reload();
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}
