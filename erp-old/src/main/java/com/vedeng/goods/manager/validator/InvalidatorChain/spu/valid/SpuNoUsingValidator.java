package com.vedeng.goods.manager.validator.InvalidatorChain.spu.valid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSpu;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.vedeng.goods.manager.validator.enums.GoodsValidatorErrorEnum.SpuNotUsingValidatorError;


@Service
public class SpuNoUsingValidator extends GoodsInvalidatorChain {

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Override
    public void doInvalid() {
        String spuNo = getSpuNo();
        CoreSpu spuInfoBySpuNo = coreSpuMapper.getSpuInfoBySpuNo(spuNo);
        if(spuInfoBySpuNo!=null && spuInfoBySpuNo.getStatus() == 0){
            return ;
        }
        throw new InvalidatorChainException(SpuNotUsingValidatorError.getCode(),SpuNotUsingValidatorError.getMessage());
    }
}
