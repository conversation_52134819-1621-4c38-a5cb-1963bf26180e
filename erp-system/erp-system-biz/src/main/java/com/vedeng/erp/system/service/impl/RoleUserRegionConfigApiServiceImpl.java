package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.domain.entity.RegionEntity;
import com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity;
import com.vedeng.erp.system.dto.RoleUserRegionConfigDto;
import com.vedeng.erp.system.mapper.RoleUserRegionConfigMapper;
import com.vedeng.erp.system.mapstruct.RoleUserRegionConfigConvertor;
import com.vedeng.erp.system.service.RegionService;
import com.vedeng.erp.system.service.RoleUserRegionConfigApiService;
import com.vedeng.erp.system.vo.*;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.uac.api.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 人员与产线区域配置Service实现
 */
@Slf4j
@Service
public class RoleUserRegionConfigApiServiceImpl implements RoleUserRegionConfigApiService {

    @Autowired
    private RoleUserRegionConfigMapper roleUserRegionConfigMapper;
    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;
    @Autowired
    private RoleUserRegionConfigConvertor roleUserRegionConfigConvertor;
    @Autowired
    private RegionService regionService;

    @Override
    public PageInfo<RoleUserRegionConfigVO> pageRoleUserRegionConfig(PageParam<RoleUserRegionConfigQueryVO> pageParam) {
        RoleUserRegionConfigQueryVO query = pageParam.getParam();

        // 如果查询条件包含在职状态，先获取符合条件的用户ID列表
        final List<Integer> filteredUserIds;
        if (query != null && query.getEmploymentStatus() != null) {
            // 获取所有用户信息
            RestfulResult<List<UserInfoDto>> userByUserIds = uacWxUserInfoApiService.getUserByStatus(query.getEmploymentStatus());
            // 过滤出指定状态的用户ID
            filteredUserIds = userByUserIds.getData().stream()
                    .map(UserInfoDto::getId)
                    .collect(Collectors.toList());

            // 如果没有符合条件的用户，直接返回空结果
            if (filteredUserIds.isEmpty()) {
                return new PageInfo<>(new ArrayList<>());
            }
        } else {
            filteredUserIds = null;
        }

        // 如果查询条件包含部门，获取部门下的用户ID列表
        List<Integer> departmentUserIds = null;
        if (query != null && query.getDepartments() != null && !query.getDepartments().isEmpty()) {
            // 获取所有用户信息
            RestfulResult<List<UserInfoDto>> userListByDepartmentIds = uacWxUserInfoApiService.getUserListByDepartmentIds(query.getDepartments());
            // 过滤出指定部门的用户ID
            departmentUserIds = userListByDepartmentIds.getData().stream()
                    .map(UserInfoDto::getId)
                    .collect(Collectors.toList());

            // 如果没有符合条件的用户，直接返回空结果
            if (departmentUserIds.isEmpty()) {
                return new PageInfo<>(new ArrayList<>());
            }
        }

        // 合并用户ID列表
        List<Integer> finalUserIds = new ArrayList<>();
        if (filteredUserIds != null && departmentUserIds != null) {
            // 取两个列表的交集
            finalUserIds = filteredUserIds.stream()
                    .filter(departmentUserIds::contains)
                    .collect(Collectors.toList());
        } else if (filteredUserIds != null) {
            finalUserIds = filteredUserIds;
        } else if (departmentUserIds != null) {
            query.setDepartmentUserIds(departmentUserIds);
        }

        if(CollUtil.isNotEmpty(query.getBusinessUserIds())){
            finalUserIds.addAll(query.getBusinessUserIds());
        }

        query.setUserIds(finalUserIds);

        // 执行分页查询
        PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize());
        List<RoleUserRegionConfigVO> list = roleUserRegionConfigMapper.listRoleUserRegionConfig(query);

        // 填充用户在职状态和部门信息
        fillUserInfo(list);

        return new PageInfo<>(list);
    }

    /**
     * 填充用户信息（在职状态和部门）
     */
    private void fillUserInfo(List<RoleUserRegionConfigVO> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        // 收集所有用户ID
        List<Integer> userIds = new ArrayList<>();
        for (RoleUserRegionConfigVO vo : list) {
            if (vo.getOnlineSalesId() != null) {
                userIds.add(vo.getOnlineSalesId());
            }
            if (vo.getOfflineSalesId() != null) {
                userIds.add(vo.getOfflineSalesId());
            }
            if (vo.getProductionUserId() != null) {
                userIds.add(vo.getProductionUserId());
            }
        }

        // 去重
        userIds = userIds.stream().distinct().collect(Collectors.toList());

        // 如果没有用户ID，则无需查询用户信息
        if (userIds.isEmpty()) {
            return;
        }

        // 批量获取用户状态
        RestfulResult<List<UserInfoDto>> userByUserIdsResult = uacWxUserInfoApiService.getUserByUserIds(userIds);
        Map<Integer, Integer> userStatusMap = userByUserIdsResult.getData().stream()
                .collect(Collectors.toMap(UserInfoDto::getId, UserInfoDto::getWorkingStatus));

        // 批量获取用户详细信息（包含部门）
        RestfulResult<List<UserDetailInfoDTO>> userAndDepartmentInfoResult = uacWxUserInfoApiService.getUserDetailsByIds(userIds);
        Map<Integer, List<DepartmentInfoDto>> userDepartmentInfoMap = userAndDepartmentInfoResult.getData().stream()
                .collect(Collectors.toMap(UserDetailInfoDTO::getUserId, UserDetailInfoDTO::getDepartments)); // 直接获取部门列表

        // 批量获取用户头像
        Map<Integer, String> userAvatarMap = userByUserIdsResult.getData().stream()
                .collect(Collectors.toMap(UserInfoDto::getId, user -> {
                    // 优先使用别名头像，如果没有则使用普通头像
                    String avatarUrl = user.getAliasHeadPicture();
                    if (avatarUrl == null || avatarUrl.isEmpty()) {
                        avatarUrl = user.getHeadPicture();
                    }
                    return avatarUrl;
                }));

        // 填充状态和部门信息
        for (RoleUserRegionConfigVO vo : list) {
            if (vo.getOnlineSalesId() != null) {
                Integer userId = vo.getOnlineSalesId();
                vo.setOnlineSalesStatus(userStatusMap.getOrDefault(userId, 1) == 1 ? 0 : 1);
                // 设置部门列表
                List<DepartmentInfoDto> onlineSalesDeptDtos = userDepartmentInfoMap.getOrDefault(userId, new ArrayList<>());
                List<RoleUserRegionConfigVO.Department> onlineSalesDepartments = onlineSalesDeptDtos.stream()
                        .map(dto -> new RoleUserRegionConfigVO.Department(dto.getDepartmentId(), dto.getDepartmentName()))
                        .collect(Collectors.toList());
                vo.setOnlineSalesDepartments(onlineSalesDepartments);

                // 设置用户名称
                UserInfoDto onlineSalesUser = userByUserIdsResult.getData().stream()
                        .filter(u -> u.getId().equals(userId))
                        .findFirst()
                        .orElse(null);
                if (onlineSalesUser != null) {
                    vo.setOnlineSalesName(onlineSalesUser.getDisplayName());
                }
                // 设置用户头像
                vo.setOnlineSalesAvatar(userAvatarMap.getOrDefault(userId, ""));
            }
            if (vo.getOfflineSalesId() != null) {
                Integer userId = vo.getOfflineSalesId();
                vo.setOfflineSalesStatus(userStatusMap.getOrDefault(userId, 1) == 1 ? 0 : 1);
                // 设置部门列表
                List<DepartmentInfoDto> offlineSalesDeptDtos = userDepartmentInfoMap.getOrDefault(userId, new ArrayList<>());
                List<RoleUserRegionConfigVO.Department> offlineSalesDepartments = offlineSalesDeptDtos.stream()
                        .map(dto -> new RoleUserRegionConfigVO.Department(dto.getDepartmentId(), dto.getDepartmentName()))
                        .collect(Collectors.toList());
                vo.setOfflineDepartments(offlineSalesDepartments);

                // 设置用户名称
                UserInfoDto offlineSalesUser = userByUserIdsResult.getData().stream()
                        .filter(u -> u.getId().equals(userId))
                        .findFirst()
                        .orElse(null);
                if (offlineSalesUser != null) {
                    vo.setOfflineSalesName(offlineSalesUser.getDisplayName());
                }
                // 设置用户头像
                vo.setOfflineSalesAvatar(userAvatarMap.getOrDefault(userId, ""));
            }
            if (vo.getProductionUserId() != null) {
                Integer userId = vo.getProductionUserId();
                vo.setProductionUserStatus(userStatusMap.getOrDefault(userId, 1) == 1 ? 0 : 1);
                // 设置用户名称
                UserInfoDto productionUser = userByUserIdsResult.getData().stream()
                        .filter(u -> u.getId().equals(userId))
                        .findFirst()
                        .orElse(null);
                if (productionUser != null) {
                    vo.setProductionUserName(productionUser.getDisplayName());
                }
                // 设置用户头像
                vo.setProductionUserAvatar(userAvatarMap.getOrDefault(userId, ""));
            }
        }
    }

    @Override
    public RoleUserRegionConfigDto getById(Long id) {
        RoleUserRegionConfigEntity entity = getEntityById(id);
        return roleUserRegionConfigConvertor.toDto(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveRoleUserRegionConfig(RoleUserRegionConfigDto dto) {
        if (dto == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 如果regions为空，直接新增一条记录
        if (dto.getRegions() == null || dto.getRegions().isEmpty()) {
            RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
            entity.setOnlineSalesId(dto.getOnlineSalesId());
            entity.setOfflineSalesId(dto.getOfflineSalesId());
            entity.setProductionUserId(dto.getProductionUserId());

            // 检查是否已存在相同配置，存在则跳过
            if (isConfigExists(entity)) {
                log.info("数据已存在，跳过添加");
                return true;
            }

            return saveEntity(entity);
        }

        List<RoleUserRegionConfigDto.Region> regions = dto.getRegions();
        for (RoleUserRegionConfigDto.Region region : regions) {
            // 如果省ID为空，但有市ID，则根据市ID获取省ID
            if (region.getProvinceId() == null && region.getCityId() != null) {
                // 获取市信息
                RegionEntity city = regionService.getRegionById(region.getCityId().longValue());
                if (city == null || city.getParentId() == null) {
                    log.warn("未找到市[{}]的信息或市的父级省信息", region.getCityId());
                    continue;
                }

                // 获取省信息
                RegionEntity province = regionService.getRegionById(city.getParentId());
                if (province == null) {
                    log.warn("未找到市[{}]对应的省信息", region.getCityId());
                    continue;
                }

                // 创建并保存记录
                RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
                entity.setOnlineSalesId(dto.getOnlineSalesId());
                entity.setOfflineSalesId(dto.getOfflineSalesId());
                entity.setProductionUserId(dto.getProductionUserId());
                entity.setProvinceId(province.getRegionId().intValue());
                entity.setProvince(province.getRegionName());
                entity.setCityId(region.getCityId());
                entity.setCity(city.getRegionName());

                // 检查是否已存在相同配置，存在则跳过
                if (isConfigExists(entity)) {
                    log.info("数据已存在，跳过添加: 省[{}]市[{}]", province.getRegionName(), city.getRegionName());
                    continue;
                }

                saveEntity(entity);
                continue;
            }

            // 如果只传入了省，需要查询该省下所有市
            if (region.getProvinceId() != null && region.getCityId() == null) {
                // 获取省名称
                RegionEntity regionById = regionService.getRegionById(region.getProvinceId().longValue());
                String provinceName = regionById.getRegionName();
                if (provinceName == null) {
                    log.warn("未找到省[{}]的信息", region.getProvinceId());
                    continue;
                }

                // 获取该省下所有市
                List<RegionEntity> cities = regionService.listByParentRegionId(region.getProvinceId());
                if (cities == null || cities.isEmpty()) {
                    log.warn("省[{}]下没有找到市信息", provinceName);
                    continue;
                }

                // 为每个市创建配置
                for (RegionEntity city : cities) {
                    saveSingleConfig(dto, region.getProvinceId(), provinceName,
                            city.getRegionId().intValue(), city.getRegionName());
                }
            }
        }
        return true;
    }

    /**
     * 保存单条人员与产线区域配置
     */
    private void saveSingleConfig(RoleUserRegionConfigDto dto, Integer provinceId, String provinceName,
                                  Integer cityId, String cityName) {
        // 构建实体对象
        RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
        entity.setOnlineSalesId(dto.getOnlineSalesId());
        entity.setOfflineSalesId(dto.getOfflineSalesId());
        entity.setProductionUserId(dto.getProductionUserId());
        entity.setProvinceId(provinceId);
        entity.setProvince(provinceName);
        entity.setCityId(cityId);
        entity.setCity(cityName);

        // 检查是否已存在相同配置
        if (isConfigExists(entity)) {
            log.warn("数据已存在，跳过添加: 省[{}]市[{}]", provinceName, cityName);
            return;
        }

        // 保存到数据库
        saveEntity(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleUserRegionConfig(RoleUserRegionConfigDto dto) {
        if (dto == null) {
            throw new IllegalArgumentException("参数不能为空");
        }

        // 如果regions为空，直接更新记录
        if (dto.getRegions() == null || dto.getRegions().isEmpty()) {
            RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
            entity.setId(dto.getId());
            entity.setOnlineSalesId(dto.getOnlineSalesId());
            entity.setOfflineSalesId(dto.getOfflineSalesId());
            entity.setProductionUserId(dto.getProductionUserId());
            return updateEntity(entity);
        }

        List<RoleUserRegionConfigDto.Region> regions = dto.getRegions();
        boolean result = true;
        for (RoleUserRegionConfigDto.Region region : regions) {
            // 如果省ID为空，但有市ID，则根据市ID获取省ID
            if (region.getProvinceId() == null && region.getCityId() != null) {
                // 获取市信息
                RegionEntity city = regionService.getRegionById(region.getCityId().longValue());
                if (city == null || city.getParentId() == null) {
                    log.warn("未找到市[{}]的信息或市的父级省信息", region.getCityId());
                    continue;
                }

                // 获取省信息
                RegionEntity province = regionService.getRegionById(city.getParentId());
                if (province == null) {
                    log.warn("未找到市[{}]对应的省信息", region.getCityId());
                    continue;
                }

                // 更新记录
                RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
                entity.setId(dto.getId());
                entity.setOnlineSalesId(dto.getOnlineSalesId());
                entity.setOfflineSalesId(dto.getOfflineSalesId());
                entity.setProductionUserId(dto.getProductionUserId());
                entity.setProvinceId(province.getRegionId().intValue());
                entity.setProvince(province.getRegionName());
                entity.setCityId(region.getCityId());
                entity.setCity(city.getRegionName());
                result = result && updateEntity(entity);
                continue;
            }

            // 如果省ID为空，直接更新记录
            if (region.getProvinceId() == null) {
                RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
                entity.setId(dto.getId());
                entity.setOnlineSalesId(dto.getOnlineSalesId());
                entity.setOfflineSalesId(dto.getOfflineSalesId());
                entity.setProductionUserId(dto.getProductionUserId());
                result = result && updateEntity(entity);
                continue;
            }

            // 如果只传入了省，需要查询该省下所有市
            if (region.getProvinceId() != null &&  region.getCityId() == null) {
                // 获取省名称
                RegionEntity regionById = regionService.getRegionById(region.getProvinceId().longValue());
                String provinceName = regionById.getRegionName();
                if (provinceName == null) {
                    log.warn("未找到省[{}]的信息", region.getProvinceId());
                    continue;
                }

                // 获取该省下所有市
                List<RegionEntity> cities = regionService.listByParentRegionId(region.getProvinceId());
                if (cities == null || cities.isEmpty()) {
                    log.warn("省[{}]下没有找到市信息", provinceName);
                    continue;
                }

                // 为每个市更新配置
                for (RegionEntity city : cities) {
                    result = result && updateSingleConfig(dto, region.getProvinceId(), provinceName,
                            city.getRegionId().intValue(), city.getRegionName());
                }
            }
        }
        return result;
    }

    /**
     * 更新单条人员与产线区域配置
     */
    private boolean updateSingleConfig(RoleUserRegionConfigDto dto, Integer provinceId, String provinceName,
                                       Integer cityId, String cityName) {
        // 构建实体对象
        RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();
        entity.setId(dto.getId());
        entity.setOnlineSalesId(dto.getOnlineSalesId());
        entity.setOfflineSalesId(dto.getOfflineSalesId());
        entity.setProductionUserId(dto.getProductionUserId());
        entity.setProvinceId(provinceId);
        entity.setProvince(provinceName);
        entity.setCityId(cityId);
        entity.setCity(cityName);
        // 更新到数据库
        return updateEntity(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRoleUserRegionConfig(Long id) {
        return deleteEntity(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchDeleteRoleUserRegionConfig(List<Long> ids) {
        return batchDeleteEntity(ids);
    }

    @Override
    public List<BusinessUserVO> listAllBusinessUsers(String userName) {
        return roleUserRegionConfigMapper.listAllBusinessUsers(userName);
    }


    private RoleUserRegionConfigEntity getEntityById(Long id) {
        return roleUserRegionConfigMapper.selectByPrimaryKey(id);
    }

    private boolean saveEntity(RoleUserRegionConfigEntity entity) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        entity.setAddTime(new Date());
        entity.setCreator(currentUser.getId());
        entity.setCreatorName(currentUser.getUsername());
        entity.setModTime(new Date());
        entity.setUpdater(currentUser.getId());
        entity.setUpdaterName(currentUser.getUsername());
        return roleUserRegionConfigMapper.insert(entity) > 0;
    }

    private boolean updateEntity(RoleUserRegionConfigEntity entity) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        entity.setModTime(new Date());
        entity.setUpdater(currentUser.getId());
        entity.setUpdaterName(currentUser.getUsername());
        return roleUserRegionConfigMapper.updateByPrimaryKeySelective(entity) > 0;
    }

    private boolean deleteEntity(Long id) {
        return roleUserRegionConfigMapper.deleteByPrimaryKey(id) > 0;
    }

    private boolean batchDeleteEntity(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }
        return roleUserRegionConfigMapper.batchDelete(ids) > 0;
    }

    @Override
    public List<RoleUserRegionConfigVO> listRoleUserRegionConfig(RoleUserRegionConfigQueryVO query) {
        // 查询所有数据
        List<RoleUserRegionConfigVO> list = roleUserRegionConfigMapper.listRoleUserRegionConfig(query);

        // 填充用户在职状态和部门信息
        fillUserInfo(list);

        // 处理省市信息，分别设置参考省和参考市字段
        for (RoleUserRegionConfigVO vo : list) {
            if (StringUtils.hasText(vo.getProvince())) {
                vo.setProvinceDisplay(vo.getProvince());
            }
            if (StringUtils.hasText(vo.getCity())) {
                vo.setCityDisplay(vo.getCity());
            }
        }
        return list;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importData(MultipartFile file) {
        if (file == null) {
            return "请选择文件";
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || (!fileName.toLowerCase().endsWith(".xlsx") && !fileName.toLowerCase().endsWith(".xls"))) {
            return "仅支持xlsx和xls格式";
        }

        try {
            // 使用EasyExcel读取数据
            List<RoleUserRegionConfigEntity> entityList = new ArrayList<>();
            List<Integer> errorRows = new ArrayList<>();
            List<String> errorMessages = new ArrayList<>();
            List<Integer> duplicateRows = new ArrayList<>();
            final java.util.concurrent.atomic.AtomicInteger skipCount = new java.util.concurrent.atomic.AtomicInteger(0); // 跳过的记录数

            EasyExcel.read(file.getInputStream(), RoleUserRegionConfigImportVO.class, new AnalysisEventListener<RoleUserRegionConfigImportVO>() {
                private int rowIndex = 1; // 从1开始，跳过表头

                @Override
                public void invoke(RoleUserRegionConfigImportVO data, AnalysisContext context) {
                    rowIndex++;
                    try {
                        RoleUserRegionConfigEntity entity = new RoleUserRegionConfigEntity();

                        CurrentUser currentUser = CurrentUser.getCurrentUser();
                        entity.setAddTime(new Date());
                        entity.setCreator(currentUser.getId());
                        entity.setCreatorName(currentUser.getUsername());
                        entity.setModTime(new Date());
                        entity.setUpdater(currentUser.getId());
                        entity.setUpdaterName(currentUser.getUsername());

                        // 处理线上销售
                        if (!StringUtils.hasText(data.getOnlineSales())) {
                            errorRows.add(rowIndex);
                            // 不提示具体的错误原因
                            errorMessages.add("第" + rowIndex + "行数据错误");
                            return;
                        }
                        entity.setOnlineSalesId(getUserIdByName(data.getOnlineSales()));

                        // 处理线下销售
                        if (StringUtils.hasText(data.getOfflineSales())) {
                            entity.setOfflineSalesId(getUserIdByName(data.getOfflineSales()));
                        }

                        // 处理销售产线人员
                        if (StringUtils.hasText(data.getProductionUser())) {
                            entity.setProductionUserId(getUserIdByName(data.getProductionUser()));
                        }

                        // 处理参考省和参考市
                        String province = null;
                        String city = null;

                        // 处理参考省
                        if (StringUtils.hasText(data.getReferenceProvince())) {
                            province = data.getReferenceProvince();
                        }

                        // 处理参考市
                        if (StringUtils.hasText(data.getReferenceCity())) {
                            city = data.getReferenceCity();
                        }

                        // 如果有省份和城市信息，设置到实体中
                        if (province != null) {
                            entity.setProvince(province);
                            entity.setProvinceId(regionService.getProvinceId(province));
                        }

                        if (city != null) {
                            entity.setCity(city);
                            entity.setCityId(regionService.getCityId(city, province));
                        }

                        // 如果没有省份信息，但有城市信息，尝试根据城市获取省份
                        if (province == null && city != null) {
                            // 获取市信息
                            Integer cityId = regionService.getCityId(city, null);
                            if (cityId != null) {
                                RegionEntity cityEntity = regionService.getRegionById(cityId.longValue());
                                if (cityEntity != null && cityEntity.getParentId() != null) {
                                    // 获取省信息
                                    RegionEntity provinceEntity = regionService.getRegionById(cityEntity.getParentId());
                                    if (provinceEntity != null) {
                                        entity.setProvince(provinceEntity.getRegionName());
                                        entity.setProvinceId(provinceEntity.getRegionId().intValue());
                                    }
                                }
                            }
                        }

                        // 如果没有省市信息，则跳过该条记录
                        if (entity.getProvinceId() == null && entity.getCityId() == null) {
                            // 不需要设置错误，因为参考省市是可选的
                        }

                        // 检查是否存在重复配置（数据库中已存在）
                        if (isConfigExists(entity)) {
                            log.info("第{}行数据已存在，跳过添加", rowIndex);
                            skipCount.incrementAndGet();
                            duplicateRows.add(rowIndex);
                            return;
                        }

                        // 检查是否存在重复配置（在导入数据中）
                        boolean isDuplicate = false;
                        for (RoleUserRegionConfigEntity configEntity : entityList) {
                            boolean onlineSalesMatch = configEntity.getOnlineSalesId() != null &&
                                    configEntity.getOnlineSalesId().equals(entity.getOnlineSalesId());

                            boolean offlineSalesMatch = (configEntity.getOfflineSalesId() == null && entity.getOfflineSalesId() == null) ||
                                    (configEntity.getOfflineSalesId() != null && entity.getOfflineSalesId() != null &&
                                    configEntity.getOfflineSalesId().equals(entity.getOfflineSalesId()));

                            boolean productionUserMatch = (configEntity.getProductionUserId() == null && entity.getProductionUserId() == null) ||
                                    (configEntity.getProductionUserId() != null && entity.getProductionUserId() != null &&
                                    configEntity.getProductionUserId().equals(entity.getProductionUserId()));

                            boolean provinceMatch = (configEntity.getProvinceId() == null && entity.getProvinceId() == null) ||
                                    (configEntity.getProvinceId() != null && entity.getProvinceId() != null &&
                                    configEntity.getProvinceId().equals(entity.getProvinceId()));

                            boolean cityMatch = (configEntity.getCityId() == null && entity.getCityId() == null) ||
                                    (configEntity.getCityId() != null && entity.getCityId() != null &&
                                    configEntity.getCityId().equals(entity.getCityId()));

                            if (onlineSalesMatch && offlineSalesMatch && productionUserMatch && provinceMatch && cityMatch) {
                                isDuplicate = true;
                                break;
                            }
                        }

                        if (isDuplicate) {
                            log.info("第{}行数据在导入数据中重复，跳过添加", rowIndex);
                            skipCount.incrementAndGet();
                            duplicateRows.add(rowIndex);
                        } else {
                            entityList.add(entity);
                        }
                    } catch (Exception e) {
                        errorRows.add(rowIndex);
                        // 不提示具体的错误原因
                        errorMessages.add("第" + rowIndex + "行数据错误");
                        log.error("导入第{}行数据出错: {}", rowIndex, e.getMessage());
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    // 所有数据解析完成后调用
                }
            }).sheet().doRead();

            StringBuilder resultMessage = new StringBuilder();

            if (!errorRows.isEmpty()) {
                // 修改为"数据错误：第X/Y/Z行"格式
                resultMessage.append("数据错误：第").append(errorRows.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("/")))
                    .append("行\n");
            }

            if (!duplicateRows.isEmpty()) {
                // 修改为"数据重复：第X/Y/Z行"格式
                resultMessage.append("数据重复：第").append(duplicateRows.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining("/")))
                    .append("行\n");
            }

            if (entityList.isEmpty()) {
                if (skipCount.get() > 0) {
                    return "所有数据已存在，导入0条数据\n" +
                           "跳过" + skipCount.get() + "条重复数据";
                }
                return resultMessage.length() > 0 ? resultMessage.toString() : "导入数据为空";
            }

            // 批量保存
            roleUserRegionConfigMapper.batchInsert(entityList);

            if (resultMessage.length() > 0) {
                return resultMessage.toString() + "成功导入：" + entityList.size() + "条数据";
            } else if (skipCount.get() > 0) {
                return "数据重复：第" + duplicateRows.stream().map(String::valueOf).collect(Collectors.joining("/")) + "行\n" +
                       "成功导入：" + entityList.size() + "条数据";
            } else {
                return "成功导入：" + entityList.size() + "条数据";
            }
        } catch (ExcelAnalysisException e) {
            log.error("Excel解析错误", e);
            return "请检查Excel模板是否正确";
        } catch (Exception e) {
            log.error("导入数据出错", e);
            return "导入数据出错: " + e.getMessage();
        }
    }

    @Override
    public String exportData(RoleUserRegionConfigQueryVO query, HttpServletResponse response) {
        try {
            // 查询所有数据
            List<RoleUserRegionConfigVO> list = listRoleUserRegionConfig(query);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("人员与产线区域配置.xlsx", "UTF-8"));

            // 使用EasyExcel导出
            EasyExcel.write(response.getOutputStream(), RoleUserRegionConfigVO.class)
                    .sheet("人员与产线区域配置")
                    .doWrite(list);

            return "导出成功";
        } catch (IOException e) {
            log.error("导出数据出错", e);
            return "导出数据出错: " + e.getMessage();
        }
    }

    @Override
    public String downloadTemplate(HttpServletResponse response) {
        try {
            // 创建模板数据
            List<RoleUserRegionConfigImportVO> templateData = new ArrayList<>();
            RoleUserRegionConfigImportVO template = new RoleUserRegionConfigImportVO();
            template.setOnlineSales("必填，企业微信英文名");
            template.setOfflineSales("选填，企业微信英文名");
            template.setProductionUser("选填，企业微信英文名");
            template.setReferenceProvince("选填，例如：江苏省、北京市");
            template.setReferenceCity("选填，例如：南京市、朝阳区");
            templateData.add(template);

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("人员与产线区域配置导入模板.xlsx", "UTF-8"));

            // 使用EasyExcel导出模板
            EasyExcel.write(response.getOutputStream(), RoleUserRegionConfigImportVO.class)
                    .sheet("导入模板")
                    .doWrite(templateData);

            return "模板下载成功";
        } catch (IOException e) {
            log.error("下载模板出错", e);
            return "下载模板出错: " + e.getMessage();
        }
    }


    /**
     * 根据用户名获取用户ID
     *
     * @param name 用户名
     * @return 用户ID，如果未找到则返回null
     */
    private Integer getUserIdByName(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }

        try {
            // 调用企业微信API获取用户信息
            RestfulResult<List<UserInfoDto>> result = uacWxUserInfoApiService.search(name);

            if (result != null && result.getData() != null && !result.getData().isEmpty()) {
                // 返回第一个匹配用户的ID
                return result.getData().get(0).getId();
            }

            log.warn("未找到用户: {}", name);
            return null;
        } catch (Exception e) {
            log.error("获取用户ID失败: {}", name, e);
            return null;
        }
    }

    /**
     * 检查配置是否已存在
     *
     * @param entity 配置实体
     * @return 是否存在
     */
    private boolean isConfigExists(RoleUserRegionConfigEntity entity) {
        // 使用mapper中已有的checkDuplicate方法检查数据重复
        int count = roleUserRegionConfigMapper.checkDuplicate(entity);
        return count > 0;
    }
}