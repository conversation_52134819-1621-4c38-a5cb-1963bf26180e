package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveRefundBillCommand;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundEntryDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeeReceiveRefundBillCommandConvertor extends BaseCommandMapStruct<KingDeeReceiveRefundBillCommand, KingDeeReceiveRefundBillDto> {

    @Mapping(target = "FBillTypeID.FNumber",source = "FBillTypeID")
    @Mapping(target = "FCONTACTUNIT.FNumber",source = "FCONTACTUNIT")
    @Mapping(target = "FRECTUNIT.FNumber",source = "FRECTUNIT")
    @Mapping(target = "FSETTLEORGID.FNumber",source = "FSETTLEORGID")
    @Mapping(target = "FSALEORGID.FNumber",source = "FSALEORGID")
    @Mapping(target = "FPAYORGID.FNumber",source = "FPAYORGID")
    @Mapping(target = "f_QZOK_PZGSYWDH", source = "FQzokPzgsywdh")
    @Mapping(target = "f_QZOK_ZDTJYH", source = "FQzokZdtjyh")
    KingDeeReceiveRefundBillCommand  toCommand(KingDeeReceiveRefundBillDto dto);

    @Mapping(target = "FSETTLETYPEID.FNumber",source = "FSETTLETYPEID")
    @Mapping(target = "FPURPOSEID.FNumber",source = "FPURPOSEID")
    @Mapping(target = "FACCOUNTID.FNumber",source = "FACCOUNTID")
    KingDeeReceiveRefundBillCommand.KingDeeReceiveRefundEntryCommand toCommand(KingDeeReceiveRefundEntryDto dto);
}
