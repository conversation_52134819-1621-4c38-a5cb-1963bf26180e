<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:choose>
    <c:when test="${afterSales.type eq 584}">
        <c:set var="title" value="申请付款-维修" scope="application" />
    </c:when>
    <c:otherwise>
        <c:set var="title" value="申请付款-安调" scope="application" />
    </c:otherwise>
</c:choose>
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>/static/js/orderstream/aftersales/add_apply_pay.js?rnd=${resourceVersionKey}'></script>
<div class="form-list  form-tips8">
    <form method="post" action="/order/afterSalesCommon/saveApplyPay.do">
        <ul>
            <li>
                <div class="form-tips">
                    <lable>业务类型</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks ">
                        订单付款
                    </div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>付款方式：</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <ul>
                            <li>
                                <input type="radio" name="traderMode" value="521" checked="checked" onclick="bankZfbIsShow('bankIsShow');">
                                <label>银行</label>
                            </li>
                            <li>
                                <input type="radio" name="traderMode" value="520" onclick="bankZfbIsShow('zfbIsShow');">
                                <label>支付宝</label>
                            </li>
                        </ul>
                    </div>
                    <div id="traderModeError" class="font-red none" >交易方式不允许为空</div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>交易主体</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <ul>
                            <li>
                                <input type="radio" name="traderSubject" value="2" checked="checked">
                                <label>对私</label>
                            </li>
                            <li>
                                <input type="radio" name="traderSubject" value="1" >
                                <label>对公</label>
                            </li>
                        </ul>
                    </div>
                    <div id="traderSubjectError" class="font-red none" >交易主体不允许为空</div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>交易名称：</lable>
                </div>
                <div class="f_left">
                    <div class="form-blanks">
                        <ul class="aftersales selectedRadio">
                            <c:forEach items="${afterSales.afterSalesInstallstionVoList }" var="asi" varStatus="status">
                                <li>
                                    <input style="display: none;" type="radio" <c:if test="${status.count eq 1 }">checked="true"</c:if> name="name" value="${status.count}">
                                    <input type="hidden" class="traderName" id="name_${status.count}" value="${asi.name}">
                                    <input type="hidden" id="company_${status.count}" value="${asi.company}">
                                    <input type="hidden" id="bank_${status.count}" value="${asi.bank}">
                                    <input type="hidden" id="bankCode_${status.count}" value="${asi.bankCode}">
                                    <input type="hidden" id="bankAccount_${status.count}" value="${asi.bankAccount}">
                                    <input type="hidden" class="engineerId" id="engineerId_${status.count}" value="${asi.engineerId}">
                                    <input type="hidden" id="engineerAmount_${status.count}" value="${asi.engineerAmount}">
                                    <input type="hidden" class="afterSalesInstallstionId" id="afterSalesInstallstionId_${status.count}" value="${asi.afterSalesInstallstionId}">
                                    <input type="hidden" id="payApplyTotalAmount_${status.count}" value="${asi.payApplyTotalAmount}">
                                    <input type="hidden" id="payApplySum_${status.count}" value="${asi.payApplySum}">
                                    <input type="hidden" name="formToken" value="${formToken}"/>
                                    <label>${asi.name}</label>
                                    <label>身份证号: </label>
                                    <label> <c:if test="${empty asi.card}">-</c:if> ${asi.card}</label>
                                    <label>手机号: </label>
                                    <label> <c:if test="${empty asi.mobile}">-</c:if> ${asi.mobile}</label>
                                </li>
                            </c:forEach>
                        </ul>
                    </div>
                    <input type="hidden" name="relatedId" value="${afterSales.afterSalesId}">
                    <input type="hidden" name="traderType" id="1">
                    <input type="hidden" name="isUseBalance" value="0">
                    <div id="tarderNameError"></div>
                </div>
            </li>

            <li >
                <div class="form-tips" >
                    <span>*</span>
                    <label id="bankIsShow" class="label-isShow">开户银行</label>
                    <lable id="zfbIsShow" class="label-isShow" style="display: none;">支付宝</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks ">
                        <input type="text" class="input-largest" id="bank" name="bank" maxlength="50">
                    </div>
                    <div id="bankError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>银行/支付宝账号</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="bankAccount" name="bankAccount" oninput = "value=value.replace(/[^\d]/g,'')" maxlength="32">
                    </div>
                    <div id="bankAccountError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>开户行支付联行号</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="bankCode" name="bankCode" oninput = "value=value.replace(/[^\d]/g,'')" maxlength="20">
                    </div>
                    <div id="bankCodeError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>内部付款备注</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="comments" name="comments">
                        <label style="color: gray;">告知财务付款需关注的事项</label>
                    </div>
                    <div id="commentsError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <lable>银行回单备注</lable>
                </div>
                <div class="f_left ">
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="bankRemark" name="bankRemark">
                        <label style="color: gray;">写入银行回单的备注信息</label>
                    </div>
                    <div id="bankRemarkError"></div>
                </div>
            </li>
            <li>
                <div class="form-tips">
                    <span>*</span>
                    <lable>申请付款金额</lable>
                </div>
                <div class="f_left" style='width:80%;'>
                    <div class="form-blanks">
                        <input type="text" class="input-largest" id="amount" name="amount" readonly="readonly">
                    </div>
                    <div id="amountError"></div>
                    <div id="totalAmountError"></div>
                    <table class="table mt10">
                        <thead>
                        <tr>
                            <th class="wid15">产品名称</th>
                            <th class="wid8">单价</th>
                            <th class="wid8">数量</th>
                            <th class="wid13">申请数量/已申请数量</th>
                            <th class="wid13">申请总额/已申请总额</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:choose>
                            <c:when test="${empty afterSales.afterSalesGoodsList && not empty afterSales.afterSalesInstallstionVoList}">
                                <c:set var="afterSalesGoodsListAmount" value="0.00"></c:set>
                                <tr>
                                    <td class="text-left">
                                        <c:forEach items="${afterSales.afterSalesInstallstionVoList }" var="asit" >
                                                <c:set value="${asit.engineerAmount + afterSalesGoodsListAmount}" var="afterSalesGoodsListAmount"></c:set>
                                        </c:forEach>
                                        <c:choose>
                                            <c:when test="${afterSales.type eq 584}">
                                                <span class="font-blue cursor-pointer">
                                                    维修费:
                                                </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span class="font-blue cursor-pointer">
                                                    安调费:
                                                </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                    <td>
                                        <input type="hidden" name="price" id="price" value="${afterSalesGoodsListAmount}"/>${afterSalesGoodsListAmount}
                                    </td>
                                    <td>
                                        <input type="hidden" id="sum" value="1"/>1
                                    </td>
                                    <td>
                                        <input type="text" style="width:100px;" name="num" id="num" >/<span id="paySum"></span>
                                        <input type="hidden" id="payApplySum" value=""/>
                                    </td>
                                    <td>
                                        <input type="text" style="width:100px;" name="totalAmount" id="totalAmount" readonly="readonly">/<span id="payTotalAmount"></span>
                                        <input type="hidden" id="payApplyTotalAmount" value=""/>
                                    </td>
                                </tr>
                            </c:when>
                            <c:otherwise>
                                <c:forEach items="${afterSales.afterSalesGoodsList}" var="asg">
                                    <tr>
                                        <td class="text-left">
                                        <span class="font-blue cursor-pointer addtitle"
                                              tabTitle='{"num":"viewgoods<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                            "link":"/order/afterSalesCommon/viewbaseinfo.do?goodsId=${asg.goodsId}","title":"产品信息"}'>${asg.goodsName}</span>
                                            <div>${asg.sku}</div>
                                            <input type="hidden" name="detailgoodsId" value="${asg.afterSalesGoodsId}" />
                                        </td>
                                        <td>
                                            <span id="engPrice"></span>
                                            <input type="hidden" name="price" id="price"/>
                                        </td>
                                        <td>
                                            <input type="hidden" id="sum" value="${asg.num}"/>${asg.num}
                                        </td>
                                        <td>
                                            <input type="text" style="width:100px;" name="num" id="num" >/<span id="paySum"></span>
                                            <input type="hidden" id="payApplySum" value=""/>
                                        </td>
                                        <td>
                                            <input type="text" style="width:100px;" name="totalAmount" id="totalAmount" readonly="readonly">/<span id="payTotalAmount"></span>
                                            <input type="hidden" id="payApplyTotalAmount" value=""/>
                                        </td>
                                    </tr>
                                </c:forEach>
                            </c:otherwise>
                        </c:choose>

                        </tbody>
                    </table>
                    <div class="pop-friend-tips mt6">
                        <div class="add-tijiao text-left mt8">
                            <button type="submit" id="submit">提交</button>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>