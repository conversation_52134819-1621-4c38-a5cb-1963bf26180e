package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.*;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.erp.finance.service.InvoiceApplyDetailXhService;
import com.vedeng.erp.finance.service.InvoiceService;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.vedeng.infrastructure.taxes.common.constant.TaxesConstant.Y;

/**
 * 红字确认单初始化
 */
@Service
@Slf4j
public class TaxesInvoiceRedConfirmInitOpenApiImpl extends AbstractTaxesOpenApiHandler {

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private InvoiceApplyDetailXhService invoiceApplyDetailXhService;

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceRedConfirmInitOrgResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceRedConfirmInitOrgResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult = new SaleInvoiceRedConfirmInitOrgResponseDto();
        }
        taxesResult.setOrgStr(decodeStr);
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        String message = super.getMessage(return_info, taxesResult.getMessage());
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(message);

        // 成功与否：除判断Return_code值，需要额外判断消息体的Code值，
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code()) && Y.equals(taxesResult.getCode())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }

        SaleInvoiceRedConfirmInitRequestDto saleInvoiceRedConfirmInitRequestDto = (SaleInvoiceRedConfirmInitRequestDto) taxesParam;
        InvoiceDto invoiceDto = invoiceService.findByInvoiceNo(saleInvoiceRedConfirmInitRequestDto.getLzfpqdhm());
        if (Objects.isNull(invoiceDto)){
            log.info("红字初始化后置处理结果：{}",JSONObject.toJSON(taxesResult));
            return taxesResult;
        }
        List<InvoiceApplyDetailXhDto> xhDto = invoiceApplyDetailXhService.getByApplyId(invoiceDto.getInvoiceApplyId());
        if (Objects.isNull(xhDto)){
            log.info("红字初始化后置处理结果：{}",JSONObject.toJSON(taxesResult));
            return taxesResult;
        }
        Map<Integer, InvoiceApplyDetailXhDto> map = xhDto.stream().collect(Collectors.toMap(InvoiceApplyDetailXhDto::getXh, Function.identity(),(key1, key2) -> key1));
        if (MapUtil.isEmpty(map)){
            log.info("红字初始化后置处理结果：{}",JSONObject.toJSON(taxesResult));
            return taxesResult;
        }
        List<SaleInvoiceRedConfirmInitOrgResponseDto.Hzqrxxmx> mxzbList = taxesResult.getHzqrxxmxList();

        for (SaleInvoiceRedConfirmInitOrgResponseDto.Hzqrxxmx mxzb : mxzbList) {
            InvoiceApplyDetailXhDto dto = map.get(Integer.valueOf(mxzb.getXh()));
            if (Objects.nonNull(dto)){
                mxzb.setXh(String.valueOf(dto.getInvoiceApplyDetailId()));
            }
        }
        log.info("红字初始化后置处理结果：{}",JSONObject.toJSON(taxesResult));
        return taxesResult;
    }
}


