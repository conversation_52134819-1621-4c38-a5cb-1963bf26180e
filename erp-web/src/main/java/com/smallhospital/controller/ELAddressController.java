package com.smallhospital.controller;

import com.alibaba.fastjson.JSON;
import com.smallhospital.dto.ElResultDTO;
import com.vedeng.authorization.model.Region;
import com.vedeng.common.controller.BaseController;
import com.vedeng.system.service.RegionService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/12/16 11:24
 */
@Controller
@RequestMapping("/el/address")
public class ELAddressController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ELAddressController.class);

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;

    /***
     * @description: 小医院获取区域信息
     * @param region
     * @return {@link ElResultDTO}
     * @throws
     * <AUTHOR>
     * @date 2020/12/15 18:46
     */
    @ResponseBody
    @RequestMapping(value="/getRegion",method = RequestMethod.POST)
    public ElResultDTO getRegionByRegionId(@RequestBody Region region) {
        LOGGER.info("小医院系统获取区域信息请求参数:" + JSON.toJSONString(region));
        List<Region> regionList = regionService.getRegionByParentId(region.getRegionId());
        if(CollectionUtils.isNotEmpty(regionList)){
            ElResultDTO resultInfo = ElResultDTO.ok();
            resultInfo.setData(regionList);
            resultInfo.setTotal(regionList.size());
            LOGGER.info("小医院系统获取区域信息响应参数:" + JSON.toJSONString(resultInfo));
            return resultInfo;
        }
        return ElResultDTO.error("小医院系统获取区域信息失败");
    }
}
