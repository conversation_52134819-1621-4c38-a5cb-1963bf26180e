package com.vedeng.finance.enums;

import lombok.Getter;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/6 11:04
 */
@Getter
public enum InvoiceTypeEnum {

    /**
     * 采购订单发票类型
     */
    BUY_ORDER(503),

    /**
     * 售后发票类型
     */
    AFTER_SALE(504),

    /**
     * 采购费用发票类型
     */
    BUY_EXPENSE_ORDER(4126);

    private final Integer type;

    InvoiceTypeEnum(Integer type) {
        this.type = type;
    }
}
