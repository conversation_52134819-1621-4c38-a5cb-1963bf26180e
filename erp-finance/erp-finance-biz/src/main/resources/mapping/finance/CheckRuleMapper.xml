<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.CheckRuleMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.CheckRuleEntity">
    <!--@mbg.generated-->
    <!--@Table T_CHECK_RULE-->
    <id column="CHECK_RULE_ID" jdbcType="BIGINT" property="checkRuleId" />
    <result column="RULE_TYPE" jdbcType="INTEGER" property="ruleType" />
    <result column="RULE_CODE" jdbcType="VARCHAR" property="ruleCode" />
    <result column="RULE_NAME" jdbcType="VARCHAR" property="ruleName" />
    <result column="RULE_CONTENT" jdbcType="VARCHAR" property="ruleContent" />
    <result column="USAGE_SCENE" jdbcType="VARCHAR" property="usageScene" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CHECK_RULE_ID, RULE_TYPE, RULE_CODE, RULE_NAME, RULE_CONTENT, USAGE_SCENE, ADD_TIME, 
    MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_CHECK_RULE
    where CHECK_RULE_ID = #{checkRuleId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_CHECK_RULE
    where CHECK_RULE_ID = #{checkRuleId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="CHECK_RULE_ID" keyProperty="checkRuleId" parameterType="com.vedeng.erp.finance.domain.entity.CheckRuleEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CHECK_RULE (RULE_TYPE, RULE_CODE, RULE_NAME, 
      RULE_CONTENT, USAGE_SCENE, ADD_TIME, 
      MOD_TIME, CREATOR, UPDATER, 
      CREATOR_NAME, UPDATER_NAME)
    values (#{ruleType,jdbcType=INTEGER}, #{ruleCode,jdbcType=VARCHAR}, #{ruleName,jdbcType=VARCHAR},
      #{ruleContent,jdbcType=VARCHAR}, #{usageScene,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="CHECK_RULE_ID" keyProperty="checkRuleId" parameterType="com.vedeng.erp.finance.domain.entity.CheckRuleEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CHECK_RULE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="ruleType != null">
        RULE_TYPE,
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE,
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME,
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT,
      </if>
      <if test="usageScene != null and usageScene != ''">
        USAGE_SCENE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="usageScene != null and usageScene != ''">
        #{usageScene,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.CheckRuleEntity">
    <!--@mbg.generated-->
    update T_CHECK_RULE
    <set>
      <if test="ruleType != null">
        RULE_TYPE = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="usageScene != null and usageScene != ''">
        USAGE_SCENE = #{usageScene,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where CHECK_RULE_ID = #{checkRuleId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.CheckRuleEntity">
    <!--@mbg.generated-->
    update T_CHECK_RULE
    set RULE_TYPE = #{ruleType,jdbcType=INTEGER},
      RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      USAGE_SCENE = #{usageScene,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where CHECK_RULE_ID = #{checkRuleId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_CHECK_RULE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="RULE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RULE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="RULE_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleContent,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="USAGE_SCENE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.usageScene,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where CHECK_RULE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.checkRuleId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_CHECK_RULE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="RULE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleType != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleCode != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RULE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleName != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="RULE_CONTENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ruleContent != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.ruleContent,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="USAGE_SCENE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.usageScene != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.usageScene,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when CHECK_RULE_ID = #{item.checkRuleId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where CHECK_RULE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.checkRuleId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="CHECK_RULE_ID" keyProperty="checkRuleId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CHECK_RULE
    (RULE_TYPE, RULE_CODE, RULE_NAME, RULE_CONTENT, USAGE_SCENE, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.ruleType,jdbcType=INTEGER}, #{item.ruleCode,jdbcType=VARCHAR}, #{item.ruleName,jdbcType=VARCHAR},
        #{item.ruleContent,jdbcType=VARCHAR}, #{item.usageScene,jdbcType=VARCHAR}, #{item.addTime,jdbcType=TIMESTAMP}, 
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="CHECK_RULE_ID" keyProperty="checkRuleId" parameterType="com.vedeng.erp.finance.domain.entity.CheckRuleEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CHECK_RULE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkRuleId != null">
        CHECK_RULE_ID,
      </if>
      RULE_TYPE,
      RULE_CODE,
      RULE_NAME,
      RULE_CONTENT,
      USAGE_SCENE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      UPDATER,
      CREATOR_NAME,
      UPDATER_NAME,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkRuleId != null">
        #{checkRuleId,jdbcType=BIGINT},
      </if>
      #{ruleType,jdbcType=INTEGER},
      #{ruleCode,jdbcType=VARCHAR},
      #{ruleName,jdbcType=VARCHAR},
      #{ruleContent,jdbcType=VARCHAR},
      #{usageScene,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updaterName,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="checkRuleId != null">
        CHECK_RULE_ID = #{checkRuleId,jdbcType=BIGINT},
      </if>
      RULE_TYPE = #{ruleType,jdbcType=INTEGER},
      RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      USAGE_SCENE = #{usageScene,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="CHECK_RULE_ID" keyProperty="checkRuleId" parameterType="com.vedeng.erp.finance.domain.entity.CheckRuleEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_CHECK_RULE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkRuleId != null">
        CHECK_RULE_ID,
      </if>
      <if test="ruleType != null">
        RULE_TYPE,
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE,
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME,
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT,
      </if>
      <if test="usageScene != null and usageScene != ''">
        USAGE_SCENE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="checkRuleId != null">
        #{checkRuleId,jdbcType=BIGINT},
      </if>
      <if test="ruleType != null">
        #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="usageScene != null and usageScene != ''">
        #{usageScene,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="checkRuleId != null">
        CHECK_RULE_ID = #{checkRuleId,jdbcType=BIGINT},
      </if>
      <if test="ruleType != null">
        RULE_TYPE = #{ruleType,jdbcType=INTEGER},
      </if>
      <if test="ruleCode != null and ruleCode != ''">
        RULE_CODE = #{ruleCode,jdbcType=VARCHAR},
      </if>
      <if test="ruleName != null and ruleName != ''">
        RULE_NAME = #{ruleName,jdbcType=VARCHAR},
      </if>
      <if test="ruleContent != null and ruleContent != ''">
        RULE_CONTENT = #{ruleContent,jdbcType=VARCHAR},
      </if>
      <if test="usageScene != null and usageScene != ''">
        USAGE_SCENE = #{usageScene,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="queryByRuleType" resultMap="BaseResultMap">
    SELECT *
    FROM T_CHECK_RULE
    WHERE RULE_TYPE = #{ruleType,jdbcType=INTEGER}
  </select>
</mapper>