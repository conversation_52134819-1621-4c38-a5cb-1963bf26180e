package com.wms.dao.generate;

import com.wms.model.ddi.generate.VWarehouseGoodsOperateLog;

public interface VWarehouseGoodsOperateLogMapper {
    int deleteByPrimaryKey(Integer warehouseGoodsOperateLogId);

    int insert(VWarehouseGoodsOperateLog record);

    int insertSelective(VWarehouseGoodsOperateLog record);

    VWarehouseGoodsOperateLog selectByPrimaryKey(Integer warehouseGoodsOperateLogId);

    int updateByPrimaryKeySelective(VWarehouseGoodsOperateLog record);

    int updateByPrimaryKey(VWarehouseGoodsOperateLog record);
}