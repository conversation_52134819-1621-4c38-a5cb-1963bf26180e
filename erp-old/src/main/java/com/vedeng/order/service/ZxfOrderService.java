package com.vedeng.order.service;

import com.vedeng.common.service.BaseService;
import com.vedeng.order.model.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * <b>Description:</b><br>
 * 订单管理接口
 * 
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.order.service <br>
 *       <b>ClassName:</b> SaleorderService <br>
 *       <b>Date:</b> 2017年7月5日 下午1:42:24
 */
public interface ZxfOrderService extends BaseService {

	/**
	 * <b>Description:</b><br>
	 * 保存新增的销售订单
	 *
	 * @param saleorder
	 * @param request
	 * @param session
	 * @return
	 * @Note <b>Author:</b> leo.yang <br>
	 *       <b>Date:</b> 2017年12月16日 下午3:23:14
	 */
	Saleorder saveAddSaleorderInfo(Saleorder saleorder, HttpServletRequest request, HttpSession session);

}