package com.vedeng.erp.finance.api;

import com.vedeng.common.core.base.R;
import com.vedeng.erp.finance.dto.AlipayReceiptDto;
import org.springframework.web.bind.annotation.*;


/**
 * <AUTHOR>
 * @description 支付宝支付回单接受
 * @date 2022/9/5 13:00
 **/
@RequestMapping("/bankBill/api")
public interface BankBillReceiptApi {


    /**
     * 支付宝回单
     */
    @RequestMapping(value = "/alipayReceipt")
    R<?> alipayReceipt(@RequestBody AlipayReceiptDto alipayReceiptDto);


}
