<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>签到打卡</title>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/mstatic/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/mui/mui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/css/pages/card.css?staticResourceVersion=${requestScope.staticResourceVersion}">

</head>

<body>
    <jsp:include page="./head_import_im.jsp"></jsp:include>
    <input type="hidden" id="visitRecordId" name="visitRecordId" value="${param.id}"/>
    <div class="page-wrap" id="page-container">
        <template v-if="!isloading">
            <div class="card-head-info">{{ traderName }}</div>
            <div class="card-form-wrap">
                <div class="card-form-block">
                    <div class="card-form-title"><span class="must">*</span>打卡位置</div>
                    <div class="card-location-wrap">
                        <div class="card-location-info" v-if="longitude || preLongitude">
                            <div class="info-l">
                                <template v-if="longitude">
                                    <div class="info-txt">
                                        <i class="vd-ui_icon icon-yes2"></i>
                                        已获取当前位置
                                    </div>
                                </template>
                                <template v-else>
                                    <div class="info-txt done">已打卡</div>
                                    <div class="info-txt" v-if="cardTime">{{ cardTime }}</div>
                                </template>
                            </div>
                            <div class="info-r" @click="showLocation">
                                <i class="vd-ui_icon  icon-address"></i>
                                <template v-if="longitude">查看地图</template>
                                <template v-else>查看打卡位置</template>
                            </div>
                        </div>
                        <div v-if="isLocationError" class="card-error">
                            <i class="vd-ui_icon icon-error2"></i>
                            获取打卡位置失败，请检查网络或企微定位授权
                        </div>
                        <template v-if="preLongitude && longitude">
                            <div class="card-reload-tip">
                                <i class="vd-ui_icon icon-info1"></i>
                                <div class="tip-txt">提交后将更新您的打卡位置</div>
                                <div class="tip-link" @click="cancelUpdate">取消更新</div>
                            </div>
                        </template>
                        <div class="card-location-btn" v-if="!(preLongitude && longitude)">
                            <vd-ui-button @click="getLocation" type="primary-line" :loading="isLocationloading">
                                <template v-if="isLocationError">
                                    <div class="btn-inner"><i class="vd-ui_icon icon-address2" v-if="!isLocationloading"></i><i v-else class="vd-ui_icon icon-loading"></i>重新获取打卡位置</div>
                                </template>
                                <template v-else>
                                    <div class="btn-inner"><i class="vd-ui_icon icon-rotate" v-if="!isLocationloading"></i><i v-else class="vd-ui_icon icon-loading"></i>更新打卡位置</div>
                                </template>
                            </vd-ui-button>
                        </div>
                    </div>
                </div>
                <div class="card-form-block" v-if="needPic">
                    <div class="card-form-title"><span class="must">*</span>拍照打卡</div>
                    <div class="card-upload-wrap">
                        <ui-wx-upload :list="picList" @change="handlerUploadChange"></ui-wx-upload>
                        <div class="form-tip">- 仅支持拍照<br>- 最多支持5张照片</div>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <vd-ui-button type="primary" @click="submitCard">提交</vd-ui-button>
            </div>
        </template>
    </div>
    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script src="/mstatic/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/mui/mui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/pages/visitRecord/card.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
