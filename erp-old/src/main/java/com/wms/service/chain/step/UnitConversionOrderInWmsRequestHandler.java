package com.wms.service.chain.step;

import cn.hutool.core.date.DateUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.flash.enums.WmsSendOrderTypeEnum;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderDto;
import com.wms.unitconversionorder.model.dto.WmsUnitConversionOrderItemDto;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 单位转换单入库 下午发wms
 * <AUTHOR>
 */
@Service
@Slf4j
public class UnitConversionOrderInWmsRequestHandler extends AbstractHandlerStep {

    // 盘库出库默认的出库人地址等信息
    public static final String CONSIGNEE_ID = "169873";
    public static final String CONSIGNEE_NAME = "南京BF（仓库）";
    public static final String PROVINCE = "江苏省";
    public static final String CITY = "南京市";
    public static final String DISTRICT = "栖霞区";
    public static final String CONSIGNEE_ADDRESS_1 = "经天路6号中电熊猫物流园区4#库二层";
    public static final String CONSIGNEE_CONTACT = "Judi";
    public static final String CONSIGNEE_TEL_1 = "13057686452";
    @Resource
    private WMSInterfaceFactory wmsInterfaceFactory;


    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;


    @Autowired
    private WmsSendOrderMapper wmsSendOrderMapper;



    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        Integer wmsUnitConversionOrderId = context.get("wmsUnitConversionOrderId");
        WmsUnitConversionOrderDto wmsUnitConversionOrderDto = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrderId);
        List<WmsUnitConversionOrderItemDto> wmsUnitConversionOrderItemDtoList = wmsUnitConversionOrderDto.getWmsUnitConversionOrderItemDtoList();
        UnitConversionOrderOutWmsRequestHandler bean = SpringUtil.getBean(UnitConversionOrderOutWmsRequestHandler.class);
        Integer wmsSendOrderId = bean.saveWmsSendOrder(wmsUnitConversionOrderDto);
        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.UNIT_CONVERSION_IN);
        putPurchaseOrderDto.setDocNo(wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo());
        putPurchaseOrderDto.setPoCreationTime(DateUtil.formatDateTime(wmsUnitConversionOrderDto.getAddTime()));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.formatDateTime(new Date()));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.formatDateTime(new Date()));
        putPurchaseOrderDto.setSupplierId(0);
        putPurchaseOrderDto.setSupplierName("VEDENG");
        putPurchaseOrderDto.setPoReferenceA(wmsUnitConversionOrderDto.getOrgName());
        putPurchaseOrderDto.setPoReferenceB(wmsUnitConversionOrderDto.getCreatorName());
        putPurchaseOrderDto.setNotes(wmsUnitConversionOrderDto.getComments());

        List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();
        for (WmsUnitConversionOrderItemDto wmsUnitConversionOrderItemDto : wmsUnitConversionOrderItemDtoList) {
            PutPurchaseOrderGoodsDto orderGoodsDto = new PutPurchaseOrderGoodsDto();
            orderGoodsDto.setSku(wmsUnitConversionOrderItemDto.getTargetSkuNo());
            orderGoodsDto.setOrderedQty(wmsUnitConversionOrderItemDto.getTargetNum().intValue());
            orderGoodsDto.setDedi04(wmsUnitConversionOrderItemDto.getWmsUnitConversionOrderItemId().toString());
            details.add(orderGoodsDto);
        }
        putPurchaseOrderDto.setDetails(details);


        try {
            log.info("WMS单位转换单入库下传的 单号:{},请求:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(), JSON.toJSONString(putPurchaseOrderDto));
            //XxlJobLogger.log("WMS单位转换单入库下传的 单号:{},请求:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(), JSON.toJSONString(putPurchaseOrderDto));
            WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);

            WmsResponse response = putSaleOrderOutputInterface.request(putPurchaseOrderDto);
            //请求失败 补偿
            if("1".equals(response.getReturnFlag())){
                WmsSendOrder updateWmsSendOrder = new WmsSendOrder();
                updateWmsSendOrder.setWmsSendOrderId(wmsSendOrderId);
                updateWmsSendOrder.setSendStatus(ErpConst.ONE);
                wmsSendOrderMapper.updateByPrimaryKeySelective(updateWmsSendOrder);
            }
            log.info("WMS单位转换单入库下传的 单号:{},响应:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),JSON.toJSONString(response));
            //XxlJobLogger.log("WMS单位转换单入库下传的 单号:{},响应:{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),JSON.toJSONString(response));
        } catch (Exception e) {
            log.error("下发单位转换单入库失败error 单号:"+wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
            //XxlJobLogger.log("下发单位转换单入库失败error 单号:"+wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
        }


    }

    @Transactional
    public Integer saveWmsSendOrder(WmsUnitConversionOrderDto wmsUnitConversionOrderDto) {
        WmsSendOrder wmsSendOrder = new WmsSendOrder();
        try {
            wmsSendOrder.setOrderType(WmsSendOrderTypeEnum.UNIT_CONVERSION_IN.getCode());
            Integer id = wmsUnitConversionOrderDto.getWmsUnitConversionOrderId();
            wmsSendOrder.setOrderId(id);
            wmsSendOrder.setOrderNo(wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo());
            WmsSendOrder oldInfo = wmsSendOrderMapper.getWmsSendOrderInfo(wmsSendOrder);
            if(oldInfo != null){
                WmsSendOrder update = new WmsSendOrder();
                update.setSendStatus(0);
                update.setWmsSendOrderId(oldInfo.getWmsSendOrderId());
                wmsSendOrderMapper.updateByPrimaryKeySelective(update);
                return oldInfo.getWmsSendOrderId();
            }
            Integer userId = CurrentUser.getCurrentUser().getId();
            wmsSendOrder.setCreator(userId);
            wmsSendOrder.setUpdater(userId);
            wmsSendOrder.setSendStatus(0);
            wmsSendOrderMapper.insertSelective(wmsSendOrder);
        } catch (Exception e) {
            log.error("saveWmsSendOrder error，下发库存转换入库单：{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
            //XxlJobLogger.log("saveWmsSendOrder error，下发库存转换入库单：{}",wmsUnitConversionOrderDto.getWmsUnitConversionOrderNo(),e);
        }
        return wmsSendOrder.getWmsSendOrderId();
    }

}

