package com.vedeng.ge.controller;

import com.google.common.collect.Lists;
import com.vedeng.authorization.model.RUserRole;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.domain.entity.GeActionLog;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceDetail;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDetailDto;
import com.vedeng.erp.buyorder.dto.GeBusinessChanceDto;
import com.vedeng.erp.buyorder.service.GeBusinessChanceDetailService;
import com.vedeng.erp.buyorder.service.GeBusinessChanceFeedbackService;
import com.vedeng.erp.buyorder.service.GeBusinessChanceService;
import com.vedeng.authorization.model.Region;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.erp.buyorder.domain.entity.GeBusinessChance;
import com.vedeng.erp.saleorder.api.QuoteInfoService;
import com.vedeng.erp.buyorder.dto.GeExamineBasicDto;
import com.vedeng.erp.buyorder.dto.GeExamineFeedBackDto;
import com.vedeng.erp.saleorder.dto.QuoteInfoDto;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.SysOptionDefinitionService;
import com.vedeng.system.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Ge商机控制类
 */
@Controller
@RequestMapping("/businesschance/ge")
public class GeBusinessChanceController extends BaseController {

    @Autowired
    private QuoteInfoService quoteInfoService;

    @Autowired
    private GeBusinessChanceService geBusinessChanceService;

    @Autowired
    private GeBusinessChanceDetailService geBusinessChanceDetailService;

    @Autowired
    private GeBusinessChanceFeedbackService geBusinessChanceFeedbackService;

    @Autowired
    private RegionService regionService;

    @Autowired
    private UserService userService;

    @Autowired
    private SysOptionDefinitionService sysOptionDefinitionService;
    /**
     * @desc 打开新增商机页面
     * <AUTHOR>
     * @param request
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "/addgebusinesschance")
    public ModelAndView addGeBusinessChance(HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        //获取省区
        List<Region> provinceList = regionService.getRegionByParentId(ErpConst.ONE);
        mv.addObject("provinceList", provinceList);
        //获取商机来源字典
        SysOptionDefinition geBusinesschanceSource = new SysOptionDefinition();
        geBusinesschanceSource.setSysOptionDefinitionId(SysOptionConstant.GE_BUSINESS_CHANCE_SOURCE);
        List<SysOptionDefinition> geBusinesschanceSources = sysOptionDefinitionService.getChilds(geBusinesschanceSource);
        mv.addObject("geBusinesschanceSources",geBusinesschanceSources);
        mv.setViewName("/ge/add_gebusinesschance");
        return mv;
    }

    /**
     * @desc 报价单搜索页面
     * <AUTHOR>
     * @param quoteorderNo
     * @return
     */
    @RequestMapping(value = "/gesearchquote")
    public ModelAndView geSearchQuote(String quoteorderNo,Integer showType){
        ModelAndView mv = new ModelAndView();
        if(quoteorderNo != null && !"".equals(quoteorderNo)){
            QuoteInfoDto quoteInfoDto = quoteInfoService.getQuoteInfoForGe(quoteorderNo);
            if(quoteInfoDto == null){
                mv.addObject("msg","报价单号有误，请重新输入。");
            }else if(ErpConst.TWO.equals(quoteInfoDto.getFollowOrderStatus())){
                mv.addObject("msg","此报价单已关闭，请重新输入。");
            }else {
                mv.addObject("quoteInfoDto",quoteInfoDto);
            }
        }
        mv.addObject("showType",showType);
        mv.setViewName("/ge/gesearch_quote");
        return mv;
    }

    /**
     * @desc 商机审核页面
     * <AUTHOR>
     * @param geBussinessChanceId
     * @return
     */
    @RequestMapping("/geExamine")
    public ModelAndView geExamine(Integer geBussinessChanceId){
        ModelAndView mv = new ModelAndView();
        if (geBussinessChanceId!=null && !"".equals(String.valueOf(geBussinessChanceId)) ) {
            //查基础信息
            GeExamineBasicDto geExamineBasicDto = geBusinessChanceService.queryBasicInfo(geBussinessChanceId);

            //查GE反馈
            GeExamineFeedBackDto geExamineFeedBackDto =geBusinessChanceFeedbackService.queryFednBackInfo(geBussinessChanceId);

            if (geExamineFeedBackDto!=null  && geExamineFeedBackDto.getAccountAreaId() != null) {
                // 地区
                List<Region> regionList = (List<Region>) regionService.getRegion(geExamineFeedBackDto.getAccountAreaId(), 1);
                if (regionList != null && (!regionList.isEmpty())) {
                    for (Region r : regionList) {
                        switch (r.getRegionType()) {
                            case 1:
                                List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
                                mv.addObject("provinceRegion", r);
                                mv.addObject("cityList", cityList);
                                break;
                            case 2:
                                List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
                                mv.addObject("cityRegion", r);
                                mv.addObject("zoneList", zoneList);
                                break;
                            case 3:
                                mv.addObject("zoneRegion", r);
                                break;
                            default:
                                mv.addObject("countryRegion", r);
                                break;
                        }
                    }
                }

            }

            // 省级地区
            List<Region> provinceList = regionService.getRegionByParentId(1);

            //操作日志
            List<GeActionLog> geActionLogs= geBusinessChanceService.queryOperationLog(geBussinessChanceId);

            mv.addObject("geExamineBasicDto",geExamineBasicDto);
            mv.addObject("geExamineFeedBackDto",geExamineFeedBackDto);
            mv.addObject("provinceList", provinceList);
            mv.addObject("geActionLogs",geActionLogs);
            mv.setViewName("/ge/ge_examine");
        }

        return mv;
    }

    /**
     * @desc 保存ge审核
     * @return
     * <AUTHOR>
     */
    @RequestMapping("/saveGeExamine")
    public ModelAndView saveGeExamine(HttpServletRequest request,GeExamineFeedBackDto geExamineFeedBackDto){
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();
        //校验规则
        try {
            geBusinessChanceFeedbackService.checkGeExamine(geExamineFeedBackDto);
        }catch (ShowErrorMsgException e){
            geExamineFeedBackDto.setErrors(Lists.newArrayList(e.getErrorMsg()));
            request.setAttribute("geExamineFeedBackDto", geExamineFeedBackDto);
            return new ModelAndView("forward:./geExamineError.do");
        }
        try {
            geBusinessChanceFeedbackService.saveGeExamine(geExamineFeedBackDto,user);
        }catch (Exception e){
            logger.error("GE审核失败:", e);
            mv.addObject("message","GE审核失败");
            return fail(mv);

        }
        mv.setViewName("redirect:/businesschance/ge/viewgebusinesschance.do?geBussinessChanceId="+geExamineFeedBackDto.getGeBussinessChanceId());
        return mv;

    }

    @RequestMapping("/geExamineError")
    public ModelAndView getForwardPage(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        GeExamineFeedBackDto geExamineFeedBackDto = (GeExamineFeedBackDto) request.getAttribute("geExamineFeedBackDto");

        //查基础信息
        GeExamineBasicDto geExamineBasicDto = geBusinessChanceService.queryBasicInfo(geExamineFeedBackDto.getGeBussinessChanceId());
        if (geExamineFeedBackDto!=null && geExamineFeedBackDto.getAccountAreaId() != null) {
            // 地区
            List<Region> regionList = (List<Region>) regionService.getRegion(geExamineFeedBackDto.getAccountAreaId(), 1);
            if (regionList != null && (!regionList.isEmpty())) {
                for (Region r : regionList) {
                    switch (r.getRegionType()) {
                        case 1:
                            List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
                            mv.addObject("provinceRegion", r);
                            mv.addObject("cityList", cityList);
                            break;
                        case 2:
                            List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
                            mv.addObject("cityRegion", r);
                            mv.addObject("zoneList", zoneList);
                            break;
                        case 3:
                            mv.addObject("zoneRegion", r);
                            break;
                        default:
                            mv.addObject("countryRegion", r);
                            break;
                    }
                }
            }

        }

        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);

        List<GeActionLog> geActionLogs= geBusinessChanceService.queryOperationLog(geExamineFeedBackDto.getGeBussinessChanceId());

        mv.addObject("geExamineBasicDto",geExamineBasicDto);
        mv.addObject("provinceList", provinceList);
        mv.addObject("geExamineFeedBackDto",geExamineFeedBackDto);
        mv.addObject("geActionLogs",geActionLogs);
        mv.setViewName("/ge/ge_examine");
        return mv;
    }

    /**
     * @desc 保存ge商机申请
     * <AUTHOR>
     * @param geBusinessChance
     * @param request
     * @return
     */
    @FormToken(remove = true)
    @RequestMapping(value = "/savegebusinesschance")
    public ModelAndView saveGeBusinessChance(GeBusinessChance geBusinessChance, HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        User currentUser = getSessionUser(request);
        ResultInfo result = geBusinessChanceService.saveGeBusinesschance(geBusinessChance,currentUser);
        GeBusinessChance inInfo = (GeBusinessChance) result.getData();
        if (ErpConst.ZERO.equals(result.getCode())) {
            mv.addObject("url", "/businesschance/ge/viewgebusinesschance.do?geBussinessChanceId="+inInfo.getGeBussinessChanceId());
            return success(mv);
        } else {
            mv.addObject("message",result.getMessage());
            return fail(mv);
        }
    }

    /**
     * <AUTHOR>
     * @desc GE商机详情页展示
     * @param geBussinessChanceId
     * @return
     */
    @RequestMapping(value = "/viewgebusinesschance")
    public ModelAndView viewgGeBusinessChance(Integer geBussinessChanceId,HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        User currentUser = getSessionUser(request);
        GeBusinessChanceDto geBusinessChanceDto = geBusinessChanceService.searchGeBusinessChanceInfo(mv,geBussinessChanceId);
        //判断GE人员
        List<RUserRole> rUserRoleListByUserId = userService.getRUserRoleListByUserId(currentUser.getUserId());
        List<Integer> collect = rUserRoleListByUserId.stream().map(RUserRole::getRoleId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            // GE角色id250
            if (collect.contains(250)) {
                mv.addObject("geRole", true);
            }
        }
        //查询报价单的归属销售及上级
        QuoteInfoDto quoteInfoDto = quoteInfoService.getQuoteInfoForGe(geBusinessChanceDto.getQuoteorderNo());
        List<User> pUserList = userService.getUserParentDetailInfo(quoteInfoDto.getUserId());
        List<Integer> pUserIds = pUserList.stream().map(User::getUserId).collect(Collectors.toList());
        if(pUserIds.contains(currentUser.getUserId()) || quoteInfoDto.getUserId().equals(currentUser.getUserId())){
            mv.addObject("haveEdit",true);
        }
        mv.setViewName("/ge/view_gebusinesschance");
        return mv;
    }

    /**
     * <AUTHOR>
     * @desc GE商机详情页展示
     * @param geBussinessChanceId
     * @return
     */
    @RequestMapping(value = "/geuserviewgebusinesschance")
    public ModelAndView geuserviewgebusinesschance(Integer geBussinessChanceId,HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        User currentUser = getSessionUser(request);
        geBusinessChanceService.searchGeBusinessChanceInfo(mv,geBussinessChanceId);
        mv.setViewName("/ge/view_gebusinesschance_geuser");
        return mv;
    }

    /**
     * <AUTHOR>
     * @desc GE商机维护页面
     * @param geBussinessChanceId
     * @return
     */
    @FormToken(save = true)
    @RequestMapping(value = "/editgebusinesschance")
    public ModelAndView editGeBusinessChance(Integer geBussinessChanceId,HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        User currentUser = getSessionUser(request);
        GeBusinessChanceDto geBusinessChanceDto = geBusinessChanceService.queryGeBusinessChanceById(geBussinessChanceId);
        mv.addObject("geBusinessChance",geBusinessChanceDto);
        GeBusinessChanceDetailDto geBusinessChanceDetailDto = geBusinessChanceDetailService.queryByGeBusinessChanceId(geBussinessChanceId);
        mv.addObject("geBusinessChanceDetail",geBusinessChanceDetailDto);
        //查询医院等级规模字典
        SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
        sysOptionDefinition.setSysOptionDefinitionId(SysOptionConstant.GE_HOSPITAL_SIZE);
        List<SysOptionDefinition> geHospitalSizeSys = sysOptionDefinitionService.getChilds(sysOptionDefinition);
        mv.addObject("geHospitalSizeSys",geHospitalSizeSys);
        //查询采购形式字典
        sysOptionDefinition.setSysOptionDefinitionId(SysOptionConstant.GE_BUY_TYPE);
        List<SysOptionDefinition> geBuyTypeSys = sysOptionDefinitionService.getChilds(sysOptionDefinition);
        mv.addObject("geBuyTypeSys",geBuyTypeSys);
        //查询项目阶段字典
        sysOptionDefinition.setSysOptionDefinitionId(SysOptionConstant.GE_PROJECT_PHASE);
        List<SysOptionDefinition> geProjectPhaseSys = sysOptionDefinitionService.getChilds(sysOptionDefinition);
        mv.addObject("geProjectPhaseSys",geProjectPhaseSys);
        mv.setViewName("/ge/edit_gebusinesschance");
        return mv;
    }

    /**
     * <AUTHOR>
     * @desc GE商机维护信息保存
     * @param geBusinessChanceDetail
     * @param request
     * @return
     */
    @FormToken(remove = true)
    @RequestMapping(value = "/saveeditgebusinesschance")
    public ModelAndView saveEditGeBusinessChance(GeBusinessChanceDetail geBusinessChanceDetail, HttpServletRequest request,
                                                 String expectBuyTimeStr,String installTimeStr,String needTimeStr,String evolveTimeStr){
        ModelAndView mv = new ModelAndView();
        User currentUser = getSessionUser(request);
        //处理日期转为date
        geBusinessChanceDetail.setExpectBuyTime(DateUtil.StringToDate(expectBuyTimeStr,"yyyy-MM"));
        geBusinessChanceDetail.setInstallTime(DateUtil.StringToDate(installTimeStr));
        geBusinessChanceDetail.setNeedTime(DateUtil.StringToDate(needTimeStr));
        geBusinessChanceDetail.setEvolveTime(DateUtil.StringToDate(evolveTimeStr));
        GeBusinessChanceDetail result = geBusinessChanceDetailService.saveEditGeBusinessChance(geBusinessChanceDetail,currentUser);
        if (result != null) {
            mv.addObject("url", "/businesschance/ge/viewgebusinesschance.do?geBussinessChanceId="+result.getGeBussinessChanceId());
            return success(mv);
        } else {
            mv.addObject("message","维护的GE商机状态已变化，请校验后重新提交");
            return fail(mv);
        }
    }
}
