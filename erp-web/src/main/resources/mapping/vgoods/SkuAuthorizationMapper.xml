<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.SkuAuthorizationMapper">

    <select id="getAllTerminalTypes" resultType="com.vedeng.goods.api.dto.SysOptionDefinition">
    SELECT
        SYS_OPTION_DEFINITION_ID,
        PARENT_ID,
        STATUS,
        TITLE
    FROM
        T_SYS_OPTION_DEFINITION
    WHERE
        `STATUS` = 1
        AND PARENT_ID = (
        SELECT
          SYS_OPTION_DEFINITION_ID
        FROM
          T_SYS_OPTION_DEFINITION
        WHERE
          TITLE = 'SKU终端类型'
         )
  </select>

    <select id="getSkuAuthrizationItemBySkuId" resultType="com.vedeng.goods.model.dto.SkuAuthrizationItemDto">
        SELECT
            T1.SKU_AUTHORIZATION_REGION_ID,
            T1.SKU_ID,
            T1.REGION_ID,
            T1.SNOW_FLAKE_ID,
            T1.IS_ENABLE,
            T2.SKU_AUTHORIZATION_TERMINALTYPE_ID,
            T2.TERMINAL_TYPE_ID
        FROM
            `T_SKU_AUTHORIZATION_REGION` T1
            INNER JOIN T_SKU_AUTHORIZATION_TERMINALTYPE T2 ON T1.SKU_ID = T2.SKU_ID
            AND T1.SNOW_FLAKE_ID = T2.SNOW_FLAKE_ID
        WHERE
            T1.IS_ENABLE = 1
            AND T2.IS_ENABLE = 1
            AND T1.SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>

    <insert id="insertSkuAuthrizationRegion">
        INSERT INTO
          T_SKU_AUTHORIZATION_REGION
            ( `SKU_ID`,
              `REGION_ID`,
              `SNOW_FLAKE_ID`,
              `ADD_TIME`,
              `CREATOR` )
        VALUES
            (
              #{skuId,jdbcType=INTEGER},
              #{regionId,jdbcType=INTEGER},
              #{snowFlakeId,jdbcType=BIGINT},
              #{addTime,jdbcType=BIGINT},
              #{creator,jdbcType=BIGINT}
                  )
    </insert>

    <update id="deleteSkuAuthrizationRegionByCondition">
        UPDATE `T_SKU_AUTHORIZATION_REGION`
        SET IS_ENABLE = 0,
        UPDATER = #{userId,jdbcType=INTEGER},
        MOD_TIME = (SELECT unix_timestamp( now( ) ) * 1000)
        WHERE
        SNOW_FLAKE_ID= #{snowFlakeId,jdbcType=BIGINT}
        AND REGION_ID IN
        <foreach collection="regionIds" item="regionId" open="(" close=")" separator=",">
            #{regionId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="disabledSkuAuthrizationRegionBySnowFlakeIds">
        UPDATE `T_SKU_AUTHORIZATION_REGION`
        SET IS_ENABLE = 0,
        UPDATER = #{userId,jdbcType=INTEGER},
        MOD_TIME = (SELECT unix_timestamp( now( ) ) * 1000)
        WHERE
        SNOW_FLAKE_ID IN
        <foreach collection="snowFlakeIds" item="snowFlakeId" open="(" close=")" separator=",">
            #{snowFlakeId,jdbcType=BIGINT}
        </foreach>
    </update>


    <insert id="insertSkuAuthrizationTerminalType">
        INSERT INTO
          T_SKU_AUTHORIZATION_TERMINALTYPE
            (
             `SKU_ID`,
              `TERMINAL_TYPE_ID`,
              `SNOW_FLAKE_ID`,
              `ADD_TIME`,
              `CREATOR`
               )
        VALUES
            (
              #{skuId,jdbcType=INTEGER},
              #{terminalTypeId,jdbcType=INTEGER},
              #{snowFlakeId,jdbcType=BIGINT},
              #{addTime,jdbcType=BIGINT},
              #{creator,jdbcType=BIGINT}
              )
    </insert>

    <update id="deleteSkuAuthrizationTerminalTypeByCondition">
        UPDATE `T_SKU_AUTHORIZATION_TERMINALTYPE`
        SET IS_ENABLE = 0,
        UPDATER = #{userId,jdbcType=INTEGER},
        MOD_TIME = (SELECT unix_timestamp( now( ) ) * 1000)
        WHERE
        SNOW_FLAKE_ID= #{snowFlakeId,jdbcType=BIGINT}
        AND TERMINAL_TYPE_ID IN
        <foreach collection="terminaltypeIds" item="terminaltypeId" open="(" close=")" separator=",">
            #{terminaltypeId,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="disabledSkuAuthrizationTerminalTypeBySnowFlakeIds">
        UPDATE `T_SKU_AUTHORIZATION_TERMINALTYPE`
        SET IS_ENABLE = 0,
        UPDATER = #{userId,jdbcType=INTEGER},
        MOD_TIME = (SELECT unix_timestamp( now( ) ) * 1000)
        WHERE
        SNOW_FLAKE_ID IN
        <foreach collection="snowFlakeIds" item="snowFlakeId" open="(" close=")" separator=",">
            #{snowFlakeId,jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>