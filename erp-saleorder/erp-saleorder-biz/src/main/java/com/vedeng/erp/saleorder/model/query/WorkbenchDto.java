package com.vedeng.erp.saleorder.model.query;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.model.query
 * @Date 2022/5/19 11:22
 */
@Getter
@Setter
public class WorkbenchDto {

    /**
     * 采购状态
     */
    private Integer purchaseStatus;

    /**
     * 用户IDList
     */
    private List<Integer> userIds;

    /**
     * 线上线下订单判断
     */
    private Integer onLineOrderTypeFlg;

    /**
     * 付款状态
     */
    private Integer paymentStatus;

    /**
     * 发货状态
     */
    private Integer deliveryStatus;

    /**
     * 未完结售后单标识
     */
    private Integer noCompletedAfterSaleFalg;

    /**
     *查询日期
     */
    private Long forwardThirtyDate;
    /**
     * 查询年份
     */
    private Integer year;
    /**
     * 查询月份
     */
    private Integer month;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 结束时间
     */
    private Long endTime;
}
