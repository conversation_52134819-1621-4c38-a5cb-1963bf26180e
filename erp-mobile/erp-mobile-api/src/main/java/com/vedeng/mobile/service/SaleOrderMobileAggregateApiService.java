package com.vedeng.mobile.service;


import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.mobile.dto.*;
import com.vedeng.mobile.query.*;

import java.util.List;

/**
 * 销售订单移动端聚合接口
 */
public interface SaleOrderMobileAggregateApiService {

    /**
     * 查询销售订单列表
     *
     * @param query 查询参数
     * @return List
     */
    SaleorderListPageDto querySaleorderList(SaleorderListQuery query);

    /**
     * 查询销售订单对应的售后订单列表
     *
     * @param query 查询参数
     * @return List
     */
    List<SaleorderOfAftersalesListDto> querySaleorderOfAftersaleList(SaleorderOfAftersalesListQuery query);

    /**
     * 查询销售订单详情
     *
     * @param query 查询参数
     * @return List
     */
    SaleorderDetailDto querySaleorderDetail(SaleorderDetailQuery query);

    /**
     * 查询销售订单下的物流信息
     *
     * @param query 查询参数
     * @return List
     */
    List<SaleorderOfLogisticsInfoDto> querylogisticsInfo(SaleorderOfLogisticsInfoQuery query);

    /**
     * 查看合同
     *
     * @param query 查询参数
     * @return List
     */
    ResourceFileDto viewContract(ViewContractQuery query);

    /**
     * 查看发票
     *
     * @param query 查询参数
     * @return List
     */
    List<ResourceFileDto> viewInvoice(ViewInvoiceQuery query);


    /**
     * 根据客户名称查询最近一次的订单-按创建时间
     * @param customerName
     * @return
     */
    SaleorderInfoDto querySaleorderByCustomerName(String customerName);


}
