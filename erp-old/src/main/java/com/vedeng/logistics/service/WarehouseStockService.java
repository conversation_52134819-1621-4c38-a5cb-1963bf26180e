package com.vedeng.logistics.service;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.BaseService;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.order.model.Saleorder;
import com.vedeng.stock.api.stock.dto.ActionStockDto;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.wms.dto.WmsStockRequest;
import com.wms.dto.WmsStockResponse;

import java.util.List;
import java.util.Map;

/**
*   库存管理
* @Author:strange
* @Date:15:03 2019-11-05
*/
public interface WarehouseStockService extends BaseService {

    String insertNewStock(Integer count,Integer goodsId);

    public String oldinsertNewStock(Integer count,Integer goodsId);
    /**
    *更新订单中占用数量(传入订单id更新该订单占用数量,i=1时更新所有订单)
    * @Author:strange
    * @Date:19:10 2019-11-11
     *
    */
    Map<String,Integer> updateSaleorderOccupyNum(Saleorder saleorder ,Integer i);

    /**
    *根据订单id获取此订单商品占用数量
    * @Author:strange
    * @Date:10:24 2019-11-08
    */
    Map<String, Integer> getOccupyNum(Integer saleorderId);

    /**
    *调用库存服务更新占用数量
    * @Author:strange
    * @Date:15:37 2019-11-12
     * @param saleorder 订单信息
     * @param type 业务类型
    */
    @Deprecated
    int updateOccupyStockService(Saleorder saleorder, int type);

    /**
    *更新库存服务中库存数量
    * @Author:strange
    * @Date:20:00 2019-11-12
    */
    int updateStockNumService(List<WarehouseGoodsOperateLog> wlogList);
    /**
    *获取合格仓和近效期仓库存总计数据
    * @Author:strange
    * @Date:17:26 2019-11-19
    */
    Map<String, WarehouseStock> getStockInfo(List<String> list);
    /**
    *更新活动订单占用数量
    * @Author:strange
    * @Date:16:03 2019-12-05
    */
    Map<String, Integer> updateSaleorderActionOccupyNum(Saleorder saleorder, int i);
    /**
    *获取活动商品出库量和占用数量
    * @Author:strange
    * @Date:10:01 2019-12-23
    */
    ActionStockDto getActionGoodsInfo(Integer actionId);
    /**
    *获取订单所有活动id
    * @Author:strange
    * @Date:13:20 2019-12-23
    */
    List<Integer> getAllActionId(Integer day);

    /**
     * @description:  获取逻辑仓库存 key是sku
     * @return:  Map<String, List<WarehouseStock>>
     * @author: Strange
     * @date: 2020/7/23
     **/
    Map<String, List<WarehouseStock>> getLogicalStockInfo(List<String> list);

    /**
     * @description: 获取逻辑仓库存  key是sku+逻辑仓id
     * @return: Map<String, WarehouseStock>
     * @author: Strange
     * @date: 2020/7/23
     **/
    Map<String, WarehouseStock> getLogicalStockMapInfo(List<String> skuList);

    /**
     * 初始化新订单占用数据
     * @param saleorderId
     * @return
     */
    ResultInfo initOrderOccupy(Integer saleorderId);


    void updateStockInfo(StockInfoDto stockInfoDto) throws Exception;

    /**
     * @description: 获取wms库存量详情
     * @return: List<WmsStockResponse>
     * @author: Strange
     * @date: 2020/9/27
     **/
    List<WmsStockResponse> getWmsStockInfo(WmsStockRequest wmsStockRequest);

    /**
     * @description:获取wms库存量汇总
     * @return:  List<WarehouseStock>
     * @author: Strange
     * @date: 2020/9/27
     *
     * @param skuList*/
    List<WarehouseStock> getWmsSimpleStockInfo(List<String> skuList);

    /**
     * @description: 获取出库单类型
     * @return: String
     * @author: Strange
     * @date: 2020/11/12
     **/
    String getPrintOutType(Saleorder saleorder);


    /**
     *  获取出库单类型
     * @param saleorder
     * @param flag 是否带价格
     * @return
     */
    String getPrintOutType(Saleorder saleorder,Boolean flag);

    /**
     * @description: 更新占用错误数据
     * @return:
     * @author: Strange
     * @date: 2021/1/25
     **/
    void updateSaleorderErrorOccupy();

    /**
     * 查询普发商品和直发商品出库单ID
     * @param saleorder
     * @return
     */
    Map<String,String> getQueryParmsWdlIdsAndDirect(Saleorder saleorder);
}
