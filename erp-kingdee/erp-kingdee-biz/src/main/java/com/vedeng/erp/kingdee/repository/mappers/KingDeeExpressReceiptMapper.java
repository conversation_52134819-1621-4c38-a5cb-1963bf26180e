package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface KingDeeExpressReceiptMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(KingDeeExpressReceiptEntity record);

    int insertOrUpdate(KingDeeExpressReceiptEntity record);

    int insertOrUpdateSelective(KingDeeExpressReceiptEntity record);

    int insertSelective(KingDeeExpressReceiptEntity record);

    KingDeeExpressReceiptEntity selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(KingDeeExpressReceiptEntity record);

    int updateByPrimaryKey(KingDeeExpressReceiptEntity record);

    int updateBatch(List<KingDeeExpressReceiptEntity> list);

    int updateBatchSelective(List<KingDeeExpressReceiptEntity> list);

    int batchInsert(@Param("list") List<KingDeeExpressReceiptEntity> list);
}