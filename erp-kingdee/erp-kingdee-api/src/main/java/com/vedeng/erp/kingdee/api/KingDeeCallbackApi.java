package com.vedeng.erp.kingdee.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.kingdee.dto.KingDeeHongRuiBuyOrderDto;
import com.vedeng.erp.kingdee.dto.KingDeeInvoiceVoucherDto;
import com.vedeng.erp.kingdee.dto.KingDeeOrderPaymentDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBankQueryDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeHongRuiBuyOrderQueryDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePayBankResultDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶回调接口
 * @date 2022/12/6 17:19
 */
@RequestMapping("/api/kingdee")
public interface KingDeeCallbackApi {

    /**
     * 金蝶发票凭证回调
     *
     * @return R
     */
    @RequestMapping(value = "/invoiceVoucher", method = RequestMethod.POST)
    @ResponseBody
    R<?> invoiceVoucher(@RequestBody List<KingDeeInvoiceVoucherDto> kingDeeInvoiceVoucherDtoList);

    /**
     * 根据对应付款申请内订单号数据回调接口
     * @param kingDeeOrderPaymentDtoList
     * @return R
     */
    @RequestMapping(value = "/orderPayment", method = RequestMethod.POST)
    @ResponseBody
    R<?> orderPayment( @RequestBody List<KingDeeOrderPaymentDto> kingDeeOrderPaymentDtoList);

    /**
     * 金蝶鸿瑞采购订单回调
     * @param kingDeeHongRuiBuyOrderQueryDto
     * @return
     */
    @RequestMapping(value = "/pageQueryBuyOrder", method = RequestMethod.POST)
    @ResponseBody
    R<PageInfo<KingDeeHongRuiBuyOrderDto>> pageQueryBuyOrder(@RequestBody KingDeeHongRuiBuyOrderQueryDto kingDeeHongRuiBuyOrderQueryDto);

    @RequestMapping(value = "/queryPayBank", method = RequestMethod.POST)
    @ResponseBody
    R<List<KingDeePayBankResultDto>> queryPayBank(@RequestBody KingDeePayBankQueryDto kingDeePayBankQueryDto);


}
