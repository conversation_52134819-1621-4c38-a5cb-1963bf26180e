package com.vedeng.erp.finance.notify.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceQrResponseDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.notify.NotifyService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.infrastructure.email.service.impl.JavaMailSenderEmailService;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/22 15:46
 **/
@Service
@Slf4j
public class WxAppNotifyService implements NotifyService {

    @Autowired
    private TaxesConfig taxesConfig;

    @Autowired
    private UserWorkApiService userWorkApiService;

    @Override
    public void notifyMessage(InvoiceDto invoiceDto) {

        log.info("开票发送企业微信通知：{}", JSON.toJSONString(invoiceDto));

        String email = StrUtil.isNotEmpty(invoiceDto.getEmail()) ? invoiceDto.getEmail() : StrUtil.isEmpty(invoiceDto.getInvoiceTraderContactEmail())?"":invoiceDto.getInvoiceTraderContactEmail();

        String msg = StrUtil.format(taxesConfig.getINVOICE_WX_MSG_TEMPLATE(), invoiceDto.getOrderNo(),
                invoiceDto.getInvoiceNo(),
                StrUtil.isEmpty(invoiceDto.getInvoiceTraderContactName()) ? "" : invoiceDto.getInvoiceTraderContactName(),
                StrUtil.isEmpty(invoiceDto.getInvoiceTraderContactMobile()) ? "" : invoiceDto.getInvoiceTraderContactMobile(),
                email
        );
        log.info("发送企业微信通知信息：{}", msg);
        try {
            List<Integer> wxAppUserId = invoiceDto.getWxAppUserId();
            if (CollUtil.isEmpty(wxAppUserId)) {
                return;
            }

            wxAppUserId.forEach(
                    x->{
                        try {
                            if (!ErpConstant.ONE.equals(x) && !ErpConstant.TWO.equals(x)) {
                                userWorkApiService.sendInvoiceMsg(x, msg);
                            }
                        } catch (Exception e) {
                            log.error("数电发票企业微信通知发送失败：{}", x, e);
                        }
                    }
            );
            return;
        } catch (Exception e) {
            log.error("数电发票企业微信通知发送失败：{}", JSON.toJSONString(invoiceDto), e);
        }
        log.info("数电发票企业微信通知失败：{}", JSON.toJSONString(invoiceDto));
    }



}
