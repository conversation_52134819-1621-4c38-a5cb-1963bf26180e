package com.vedeng.common.constant;

public enum BelongPlatformEnum {
    // 归属平台（1贝登医疗，2医械购，3科研购，4集团业务部，5其他，6集采,7区域商城
    BD(1,"贝登医疗"),YXG(2,"医械购"),K<PERSON>(3,"科研购"),<PERSON><PERSON>(4,"集团业务部"),OTHER(5,"其他"),JC(6,"集采"),QY(7,"区域商城")
    ,TMH(32,"科研特麦帮");
    private Integer belong;
    private String name;
    private BelongPlatformEnum(Integer i,String name){
        belong=i;
        this.name = name;
    }
    public Integer getBelong(){return belong;}
    public String getName(){return name;}
}
