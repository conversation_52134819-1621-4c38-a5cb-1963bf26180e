package com.vedeng.erp.aftersale.api;

import com.vedeng.erp.aftersale.dto.AfterSaleServiceStandardInfoDto;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 售后信息api
 * @Date 2022/1/10 19:49
 */
@RequestMapping("/order/aftersales")
public interface AftersaleInfoApi {

    /**
     * 根据SKU_ID获取售后政策
     *
     * @param skuNo
     * @return
     */
    @RequestMapping(value = "/getEffectAfterSalePolicy")
    AfterSaleServiceStandardInfoDto getEffectAfterSalePolicy( String skuNo);


}
