package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import com.vedeng.erp.kingdee.service.KingDeeAllocationApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 外借出库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchAllocationOutWriter extends BaseWriter<KingDeeAllocationDto> {

    @Autowired
    private KingDeeAllocationApiService kingDeeAllocationApiService;


    @Override
    public void doWrite(KingDeeAllocationDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("BatchAllocationOutWriterService#doWrite,外借出库单,{}", JSON.toJSONString(dto));
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveAllocation);
        kingDeeAllocationApiService.register(dto,true);
    }

}
