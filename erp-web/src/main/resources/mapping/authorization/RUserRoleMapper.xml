<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.RUserRoleMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.authorization.model.RUserRole" >
    <id column="USER_ROLE_ID" property="userRoleId" jdbcType="INTEGER" />
    <result column="ROLE_ID" property="roleId" jdbcType="INTEGER" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    USER_ROLE_ID, ROLE_ID, USER_ID
  </sql>
  
  <!-- deleteByUserId -->
  <delete id="deleteByUserId" parameterType="java.lang.Integer" >
    delete from T_R_USER_ROLE
    where USER_ID = #{userId,jdbcType=INTEGER}
  </delete>
  
  <!-- 添加员工角色 -->
  <insert id="insert" parameterType="com.vedeng.authorization.model.RUserRole" >
    insert into T_R_USER_ROLE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userRoleId != null" >
        USER_ROLE_ID,
      </if>
      <if test="roleId != null" >
        ROLE_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userRoleId != null" >
        #{userRoleId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null" >
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  
   <select id="getRUserRoleListByUserId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
	    select 
	    <include refid="Base_Column_List" />
	    from T_R_USER_ROLE
	    where USER_ID = #{userId,jdbcType=INTEGER}
	</select>
	
	<select id="getRoleIdListByUserId" resultType="java.lang.Integer" >
	    select 
	    ROLE_ID
	    from T_R_USER_ROLE
	    where USER_ID = #{userId,jdbcType=INTEGER}
	</select>
	
	<select id="getUserIdList" resultType="java.lang.Integer">
	    select 
	    	USER_ID
	    from T_R_USER_ROLE
	    	where ROLE_ID in 
	    <foreach item="roleId" index="index" collection="roleIdList" open="(" separator="," close=")">  
			  #{roleId}
		</foreach>
	</select>

    <select id="getUserIdListAndNotDisabled" resultType="java.lang.Integer">
        select
        USER_ID
        from T_R_USER_ROLE
        where ROLE_ID in
        <foreach item="roleId" index="index" collection="roleIdList" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </select>
</mapper>