package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeAllocationEntity;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeAllocationDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 调拨单  贝登dto 转 金蝶 command
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeAllocationConvertor extends BaseMapStruct<KingDeeAllocationEntity, KingDeeAllocationDto> {
	
	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "FBillEntry", source = "FBillEntry", qualifiedByName = "expensesToJsonArray")
    @Override
    KingDeeAllocationEntity toEntity(KingDeeAllocationDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "FBillEntry", source = "FBillEntry", qualifiedByName = "expensesJsonArrayToList")
    @Override
    KingDeeAllocationDto toDto(KingDeeAllocationEntity entity);
	
    
    /**
     * entity 中JSONArray 转 原对象
     *
     * @param array JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("expensesJsonArrayToList")
    default List<KingDeeAllocationDetailDto> entryJsonArrayToList(JSONArray array) {
        if (CollUtil.isEmpty(array)) {
            return Collections.emptyList();
        }
        return array.toJavaList(KingDeeAllocationDetailDto.class);
    }

    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("expensesToJsonArray")
    default JSONArray entryListToJsonArray(List<KingDeeAllocationDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
    
}
