package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品级信息表
 */
@Getter
@Setter
public class GoodsLevelEntity extends BaseEntity {
    /**
    * ID
    */
    private Integer id;

    /**
    * 商品级别唯一标识
    */
    private String uniqueIdentifier;

    /**
    * 商品级别名称
    */
    private String levelName;

    /**
    * 商品级别描述
    */
    private String description;

    /**
    * 档位次序（递减）
    */
    private Integer ordinal;

    /**
    * 是否删除，0:否，1:是
    */
    private Boolean isDeleted;

    /**
    * 是否可以推送至前台
    */
    private Boolean allowSyncFrontend;

    /**
    * 添加纪录人ID
    */
    private Integer creatorId;

    /**
    * 上次更新人ID
    */
    private Integer updaterId;
}