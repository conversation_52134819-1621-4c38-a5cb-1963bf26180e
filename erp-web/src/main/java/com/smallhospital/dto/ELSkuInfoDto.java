package com.smallhospital.dto;

import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/10/2610:53
 */
public class ELSkuInfoDto {

    private String brandName;

    private String spec;

    private String unit;

    private String skuName;

    private Integer vCategoryId;

    private Integer currentPage = 1;

    private Integer pageSize = 10;

    private Date startTime;

    private Date endTime;

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }


    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getvCategoryId() {
        return vCategoryId;
    }

    public void setvCategoryId(Integer vCategoryId) {
        this.vCategoryId = vCategoryId;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Override
    public String toString() {
        return "ELSkuInfoDto{" +
                "brandName='" + brandName + '\'' +
                ", spec='" + spec + '\'' +
                ", unit='" + unit + '\'' +
                ", skuName='" + skuName + '\'' +
                ", vCategoryId=" + vCategoryId +
                ", currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                '}';
    }
}
