package com.vedeng.infrastructure.kingdee.domain.command;


import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶扩展批量save参数对象
 * @date 2022/8/25 18:55
 */
public class BatchSaveExtCommand<T> extends BaseCommand {

    private int Creator;

    private ArrayList<String> NeedReturnFields = new ArrayList<>();

    private ArrayList<String> NeedUpDateFields = new ArrayList<>();


    /**
     * 是否自动提交审核
     */
    private final Boolean isAutoSubmitAndAudit;

    /**
     * 是否自动调整字段顺序
     */
    private final Boolean isAutoAdjustField = true;

    private final List<T> Model;

    public BatchSaveExtCommand(List<T> t, boolean isAutoSubmitAndAudit, String formId) {
        super(formId);
        this.isAutoSubmitAndAudit = isAutoSubmitAndAudit;
        this.Model = t;
    }

    public BatchSaveExtCommand(List<T> t, String formId) {
        super(formId);
        this.isAutoSubmitAndAudit = true;
        this.Model = t;
    }

    public BatchSaveExtCommand(List<T> t, String formId, int creator, ArrayList<String> needReturnFields, ArrayList<String> needUpDateFields, Boolean isAutoSubmitAndAudit) {
        super(formId);
        this.isAutoSubmitAndAudit = isAutoSubmitAndAudit;
        this.Model = t;
        Creator = creator;
        NeedReturnFields = needReturnFields;
        NeedUpDateFields = needUpDateFields;
    }

    public Boolean getAutoSubmitAndAudit() {
        return isAutoSubmitAndAudit;
    }


    public int getCreator() {
        return Creator;
    }


    public ArrayList<String> getNeedReturnFields() {
        return NeedReturnFields;
    }

    public ArrayList<String> getNeedUpDateFields() {
        return NeedUpDateFields;
    }


    public Boolean getAutoAdjustField() {
        return isAutoAdjustField;
    }

    public List<T> getModel() {
        return Model;
    }
}
