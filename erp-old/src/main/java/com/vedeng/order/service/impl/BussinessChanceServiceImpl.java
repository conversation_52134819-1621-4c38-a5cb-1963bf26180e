package com.vedeng.order.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.dao.RUserRoleMapper;
import com.vedeng.authorization.dao.UserDetailMapper;
import com.vedeng.authorization.dao.UserLoginLogMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.call.feign.RemoteCrmTaskApiService;
import com.vedeng.common.constant.BCConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.common.util.*;
import com.vedeng.erp.saleorder.api.AutoRegistrationService;
import com.vedeng.erp.system.dto.BussinessChanceMessageVo;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.erp.trader.service.PublicCustomerRecordApiService;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.BncLink;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.MergeBussinessChanceQuery;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.NewSourceDicDto;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.model.vo.MergeChanceGoods;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.phoneticWriting.dao.PhoneticWritingMapper;
import com.vedeng.phoneticWriting.model.PhoneticWriting;
import com.vedeng.system.dao.MessageMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.CommunicateRecord;
import com.vedeng.trader.model.TraderContact;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderCustomerService;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service("bussinessChanceService")
public class BussinessChanceServiceImpl extends BaseServiceimpl implements BussinessChanceService {
    public static Logger logger = LoggerFactory.getLogger(BussinessChanceServiceImpl.class);

    @Autowired
    @Qualifier("userMapper")
    private UserMapper userMapper;

    @Autowired
    @Qualifier("rTraderJUserMapper")
    private RTraderJUserMapper rTraderJUserMapper;

    @Resource
    private CommunicateRecordMapper communicateRecordMapper;
    @Autowired
    @Qualifier("traderCustomerService")
    private TraderCustomerService traderCustomerService;

    @Autowired
    @Qualifier("userLoginLogMapper")
    private UserLoginLogMapper userLoginLogMapper;

    @Autowired
    @Qualifier("userDetailMapper")
    private UserDetailMapper userDetailMapper;

    @Autowired
    @Qualifier("messageMapper")
    private MessageMapper messageMapper;

    @Autowired
    @Qualifier("bussinessChanceMapper")
    private BussinessChanceMapper bussinessChanceMapper;

    @Autowired
    @Qualifier("phoneticWritingMapper")
    private PhoneticWritingMapper phoneticWritingMapper;

    @Resource
    private TraderContactMapper traderContactMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Value("${oss_url}")
    private String ossUrl;

    @Value("${im_bc_communication}")
    protected Integer imBcCommunication;

    @Autowired
    private RUserRoleMapper rUserRoleMapper;

    @Resource
    private QuoteorderMapper quoteorderMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private AutoRegistrationService autoRegistrationService;

    @Autowired
    private PublicCustomerRecordApiService publicCustomerRecordApiService;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    @Value("${redis_dbtype}")
    protected String dbType;// 开发redis，测试redis

    @Autowired
    private OperationLogApiService operationLogApiService;

    @Override
    public List<SysOptionDefinition> getBussInquiryData(Integer parentId) {

        List<SysOptionDefinition> resultList = new ArrayList<>();
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST + parentId)) {
            String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST + parentId);
            // 避免json为空或null的字符串或[null]的字符串
            if (org.apache.commons.lang3.StringUtils.isNotBlank(jsonStr) && !"null".equalsIgnoreCase(jsonStr)
                    && !"[null]".equalsIgnoreCase(jsonStr) && !"[]".equalsIgnoreCase(jsonStr)) {

                resultList = JSON.parseArray(jsonStr, SysOptionDefinition.class);
                logger.info("从缓存获取渠道类型,dbType:{},key前缀:{},parentId:{},resultList:{}",dbType,ErpConst.KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST,parentId, JSONUtil.toJsonStr(resultList));
            }
        }
        // 从redis中获取为null，则从库中查询
        if (org.apache.commons.collections.CollectionUtils.isEmpty(resultList)) {
            // 调用根据parendId获取数字字典子list
            // 2级
            if (parentId.equals(4062)) {
                SysOptionDefinition sysOptionDefinition = sysOptionDefinitionMapper.selectByPrimaryKey(366);
                SysOptionDefinition sysOptionDefinition1 = sysOptionDefinitionMapper.selectByPrimaryKey(477);
                resultList.add(sysOptionDefinition);
                resultList.add(sysOptionDefinition1);
            } else if (parentId.equals(366) || parentId.equals(477)) {
                List<String> list = Arrays.asList(inquiryValue.split(","));
                List<SysOptionDefinition> inquiryList = getSysOptionDefinitionListByParentId(SysOptionConstant.ID_376);
                Map<String, SysOptionDefinition> map = inquiryList.stream().collect(Collectors.toMap(SysOptionDefinition::getTitle, s -> s));
                for (String str : list) {
                    resultList.add(map.get(str));
                }
            } else {
                resultList = sysOptionDefinitionMapper.getDictionaryByParentId(parentId);
                if (!org.springframework.util.CollectionUtils.isEmpty(resultList)) {
                    Optional<SysOptionDefinition> first = resultList.stream().findFirst();
                    if (first.isPresent()) {
                        // 3
                        SysOptionDefinition sysOptionDefinition = first.get();
                        List<SysOptionDefinition> dictionaryByParentId = sysOptionDefinitionMapper.getDictionaryByParentId(sysOptionDefinition.getSysOptionDefinitionId());
                        sysOptionDefinition.setSysOptionDefinitions(dictionaryByParentId);
                        if (org.activiti.editor.language.json.converter.util.CollectionUtils.isNotEmpty(dictionaryByParentId)) {
                            // 4级
                            Optional<SysOptionDefinition> first1 = dictionaryByParentId.stream().findFirst();
                            if (first1.isPresent()) {
                                SysOptionDefinition sysOptionDefinition1 = first1.get();
                                List<SysOptionDefinition> dictionaryByParentId2 = sysOptionDefinitionMapper.getDictionaryByParentId(sysOptionDefinition1.getSysOptionDefinitionId());
                                sysOptionDefinition1.setSysOptionDefinitions(dictionaryByParentId2);
                            }

                        }
                    }
                }
            }
            logger.info("更新渠道类型缓存,dbType:{},key前缀:{},parentId:{},resultList:{}",dbType,ErpConst.KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST,parentId, JSONUtil.toJsonStr(resultList));
            JedisUtils.set(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_BUSS_LIST + parentId,
                    JsonUtils.convertConllectionToJsonStr(resultList), cacheSecond);
        }


        return resultList;
    }

    @Override
    public BussinessChance listBussinessByMobileAndSource(String loginMobile, Integer source) {
        return bussinessChanceMapper.listBussinessByMobileAndSource(loginMobile,source);
    }

    @Override
    public Map<String, Object> getServiceBussinessChanceListPage(BussinessChanceVo bussinessChance, Page page) {
        // 调用接口
        String url = httpUrl + "order/bussinesschance/getservicebussinesschancelistpage.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BussinessChanceVo>>> TypeRef = new TypeReference<ResultInfo<List<BussinessChanceVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bussinessChance, clientId, clientKey,
                    TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<BussinessChanceVo> list = Optional.ofNullable((List<BussinessChanceVo>) result.getData()).orElse(new ArrayList<>());
            // 归属销售人员信息补充
            if (list.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (BussinessChanceVo b : list) {
                    if (b.getUserId() > 0 && !userIds.contains(b.getUserId())) {
                        userIds.add(b.getUserId());
                    }
                }
                if (userIds.size() > 0) {
                    Map<Integer, User> userMap = userMapper.getUserByUserIds(userIds).stream().collect(Collectors.toMap(User::getUserId, item -> item));
                    list.parallelStream()
                            .filter(bussinessChanceVo -> bussinessChanceVo.getUserId() > 0)
                            .forEach(
                                    item -> {
                                        if (userMap.containsKey(item.getUserId())) {
                                            item.setSaleUser(userMap.get(item.getUserId()).getUsername());
                                            item.setSaleDeptName(getOrgaNameByUserId(item.getUserId()));
                                        }
                                    }
                            );
                }

                //如果商机为合并之后的商机取商机中的归属销售
                for (BussinessChanceVo b : list) {
                    if (OrderConstant.NEW_MERGED_STATUS.equals(b.getMergeStatus())) {
                        if (b.getUserId() == null) {
                            b.setSaleUser(null);
                            b.setSaleDeptName(null);
                            continue;
                        }
                        b.setSaleUser(userMapper.getUserNameByUserId(b.getUserId()));
                        b.setSaleDeptName(getOrgaNameByUserId(b.getUserId()));
                    }
                }
            }

            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            map.put("page", page);
            return map;
        } catch (IOException e) {
            logger.error("" + JSON.toJSONString(bussinessChance), e);
            throw new RuntimeException();
        }
    }

    @Override
    public Map<String, Object> getSaleBussinessChanceListPage(BussinessChanceVo bussinessChance, Page page) {
        // 调用接口
        String url = httpUrl + "order/bussinesschance/getsalebussinesschancelistpage.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BussinessChanceVo>>> TypeRef = new TypeReference<ResultInfo<List<BussinessChanceVo>>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bussinessChance, clientId, clientKey,
                    TypeRef, page);
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<BussinessChanceVo> list = Optional.ofNullable((List<BussinessChanceVo>) result.getData()).orElse(new ArrayList<>());
            List<Integer> businessIdList = new ArrayList<>();
            // 归属销售人员信息补充
            if (list.size() > 0) {
                List<Integer> userIds = new ArrayList<>();
                for (BussinessChanceVo b : list) {
                    businessIdList.add(b.getBussinessChanceId());
                    if (b.getUserId() > 0 && !userIds.contains(b.getUserId())) {
                        userIds.add(b.getUserId());
                    }
                }
                if (userIds.size() > 0) {
                    Map<Integer, User> userMap = userMapper.getUserByUserIds(userIds).stream().collect(Collectors.toMap(User::getUserId, item -> item));
                    list.parallelStream()
                            .filter(bussinessChanceVo -> bussinessChanceVo.getUserId() > 0)
                            .forEach(
                                    item -> {
                                        if (userMap.containsKey(item.getUserId())) {
                                            item.setSaleUser(userMap.get(item.getUserId()).getUsername());
                                            item.setSaleDeptName(getOrgaNameByUserId(item.getUserId()));
                                        }
                                    }
                            );
                }
                //如果商机为合并之后的商机取商机中的归属销售
                for (BussinessChanceVo b : list) {
                    if (OrderConstant.NEW_MERGED_STATUS.equals(b.getMergeStatus())) {
                        if (b.getUserId() == null) {
                            b.setSaleUser(null);
                            b.setSaleDeptName(null);
                            continue;
                        }
                        b.setSaleUser(userMapper.getUserNameByUserId(b.getUserId()));
                        b.setSaleDeptName(getOrgaNameByUserId(b.getUserId()));
                    }
                }

            }
            //补充商机备注信息
            CommunicateRecord comRe = new CommunicateRecord();
            if (CollectionUtils.isNotEmpty(businessIdList)) {
                comRe.setBussinessChanceIds(businessIdList);
                List<CommunicateRecord> recordList = communicateRecordMapper.getCommunicateList(comRe);
                for (BussinessChanceVo bussinessChanceVo : list) {
                    for (CommunicateRecord communicateRecord : recordList) {
                        if (bussinessChanceVo.getBussinessChanceId().equals(communicateRecord.getRelatedId())) {
                            if (StringUtil.isNotBlank(communicateRecord.getContactContent())) {
                                bussinessChanceVo.setComments(communicateRecord.getContactContent());
                                break;
                            }
                        }
                    }
                }
            }

            //补充商机沟通内容
            //VDERP-3908 商机列表--【沟通内容】字段中显示沟通内容，【沟通内容】取“沟通记录”中（含客户、报价单、订单模块中同步过来的沟通记录）最近一条“沟通记录”中的【沟通内容】信息，有什么显示什么，有标签显示标签，有文字显示文字，标签和文字都有，则都显示；
            if (CollectionUtils.isNotEmpty(list)) {
                list = list.parallelStream()
                        .peek(bussinessChanceVo -> {
                            List<CommunicateRecord> communicateRecords = new ArrayList<>();
                            CommunicateRecord bncComm = new CommunicateRecord();
                            bncComm.setCommunicateType(244);
                            bncComm.setRelatedId(bussinessChanceVo.getBussinessChanceId());
                            communicateRecords.add(bncComm);
                            if (bussinessChanceVo.getTraderId() != null && bussinessChanceVo.getTraderId() > 0) {
                                CommunicateRecord traderComm = new CommunicateRecord();
                                traderComm.setCommunicateType(242);
                                traderComm.setRelatedId(bussinessChanceVo.getTraderId());
                                communicateRecords.add(traderComm);
                            }
                            List<Quoteorder> quoteorders = quoteorderMapper.getQuoteOrderByBussinessChanceId(bussinessChanceVo.getBussinessChanceId(), 1);
                            if (quoteorders != null && quoteorders.size() > 0) {
                                CommunicateRecord quoteComm = new CommunicateRecord();
                                quoteComm.setCommunicateType(245);
                                quoteComm.setRelatedId(quoteorders.get(0).getQuoteorderId());
                                communicateRecords.add(quoteComm);

                                List<Saleorder> saleorderList = saleorderMapper.getSaleorderByQuoteorderId(quoteorders.get(0).getQuoteorderId());
                                if (saleorderList != null && saleorderList.size() > 0) {
                                    CommunicateRecord saleorderComm = new CommunicateRecord();
                                    saleorderComm.setCommunicateType(246);
                                    saleorderComm.setRelatedId(saleorderList.get(0).getSaleorderId());
                                    communicateRecords.add(saleorderComm);
                                }
                            }
                            //查询最新一条沟通记录
                            CommunicateRecord latestComm = communicateRecordMapper.getLatestCommunicationRecordByRecordList(communicateRecords);
                            if (latestComm != null) {
                                List<Tag> tags = communicateRecordMapper.getCommunicationTags(latestComm.getCommunicateRecordId());
                                if (tags != null && tags.size() > 0) {
                                    bussinessChanceVo.setTags(tags);
                                }
                                bussinessChanceVo.setContactContent(latestComm.getContactContent());
                            }
                        })
                        .collect(Collectors.toList());
            }
            page = result.getPage();
            Map<String, Object> map = new HashMap<>();
            map.put("list", list);
            map.put("page", page);
            return map;
        } catch (IOException e) {
            logger.error("" + JSON.toJSONString(bussinessChance), e);
            throw new RuntimeException(e);
        }
    }

    @Autowired
    private UserWorkApiService userWorkApiService;

    public Integer checkBusiness2OtherSaleUser(Integer saleUserId){
        try{
            String dateTime = DateUtil.getStringDateNow2();
            Integer newUserId = userWorkApiService.getUserBusiness2OtherSaleUser(saleUserId,dateTime);
            logger.info("分配商机时，检查商机是否需要调整为另一个员工：{},{}",new Object[]{saleUserId,newUserId});
            return newUserId;
        }catch (Exception e){
            logger.error("checkBusiness2OtherSaleUser error",e);
            return saleUserId;
        }

    }

    @Override
    public ResultInfo<?> assignBussinessChance(BussinessChanceVo bussinessChanceVo, HttpSession session) {
        Long time = DateUtil.sysTimeMillis();
        bussinessChanceVo.setAssignTime(time);
        //重置第一次查看时间
        bussinessChanceVo.setFirstViewTime(0L);

        List<BussinessChance> list = bussinessChanceMapper.queryForassignBussinessChance(bussinessChanceVo.getBussinessChanceIds());
        List<String> strList = new ArrayList<>();
        for(BussinessChance bus:list){
            if(bus != null &&  bus.getMergeStatus()  != null &&
                    bus.getMergeStatus() == 1){//  `MERGE_STATUS` COMMENT '合并状态，0未合并，1被合并，2合并其他的',
                strList.add(bus.getBussinessChanceNo() );
            }
        }
        if(CollectionUtil.isNotEmpty(strList)){//如果商机是被合并状态，则提示用户
            return ResultInfo.error("商机"+StringUtils.join(strList,",")+"已被合并，请刷新后重试");
        }


        // 接口调用
        String url = httpUrl + "order/bussinesschance/assignbussinesschance.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<BussinessChance>>> TypeRef = new TypeReference<ResultInfo<List<BussinessChance>>>() {
        };
        // 分配销售userId
        Integer saleUserId = bussinessChanceVo.getUserId();
        Integer newSaleUserId = checkBusiness2OtherSaleUser(saleUserId);
        bussinessChanceVo.setUserId(newSaleUserId);

        //判断当前是否为休息时间:
        //boolean isWorkTime = userWorkApiService.isWorkTime();

        try {
            ResultInfo<List<BussinessChance>> result = (ResultInfo<List<BussinessChance>>) HttpClientUtils.post(url, bussinessChanceVo, clientId, clientKey, TypeRef);
            if (null != result && result.getCode() == 0) {
                List<BussinessChance> resultList = result.getData();
                if (null != resultList && resultList.size() > 0) {
                    for (BussinessChance vo : resultList) {
                        if (null == vo) {
                            continue;
                        }
                        //商机分配销售
                        List<Integer> userIdList = new LinkedList<Integer>();
                        userIdList.add(newSaleUserId);
                        Map<String, String> mes_map = new HashMap<>();
                        mes_map.put("bussinessChanceNo", vo.getBussinessChanceNo());
                        //商机分配给销售后，发送消息给销售
//                        MessageUtil.sendMessage(69, userIdList, mes_map, "./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=" + vo.getBussinessChanceId() + "&traderId=0");
                        MessageUtil.sendMessage(69, userIdList, mes_map, "/businessChance/details.do?id=" + vo.getBussinessChanceId());

//                        if(!isWorkTime){

                            BussinessChanceMessageVo messageVo = new BussinessChanceMessageVo();
                            messageVo.setBussinessNo(vo.getBussinessChanceNo());
                            messageVo.setCustomerName(StringUtils.isEmpty(vo.getTraderName())?"":vo.getTraderName());
                            messageVo.setMobile((StringUtils.isEmpty(vo.getTraderContactName())?"":vo.getTraderContactName())+" "+  (StringUtils.isEmpty(vo.getMobile())?"":vo.getMobile()));
                            messageVo.setSendTime(DateUtil.convertString(time,null));
                            messageVo.setRemark(
                                    (StringUtils.isEmpty(vo.getContent())?"":vo.getContent())+ "/"+
                                            (StringUtils.isEmpty(vo.getProductComments())?"":vo.getProductComments()));
                            try{
                                userWorkApiService.sendMsg(newSaleUserId,messageVo);
                            }catch (Exception e){
                                logger.error("非工作日推送消息失败",e);
                            }

//                        }
                    }
                }
            }
            return result;
        } catch (IOException e) {
            logger.error( JSON.toJSONString(bussinessChanceVo), e);
        }
        return null;
    }

    @Override
    public void convertBussinessChanceIfMerged(BussinessChance bussinessChance) {
        BussinessChance bc = bussinessChanceMapper.getMergeStatus(bussinessChance.getBussinessChanceId());
        if (bc == null
                || StringUtil.isBlank(bc.getBussinessChanceNo())
                || !OrderConstant.BE_MERGED_STATUS.equals(bc.getMergeStatus())) {
            return;
        }
        Integer id = bussinessChanceMapper.getNewMergeChanceId(bc.getBussinessChanceNo());
        if (id != null) {
            bussinessChance.setBussinessChanceId(id);
        }
    }



    @Override
    public BussinessChance getBusinessChanceByChanceNo(String bussinessChanceNo) {
        return bussinessChanceMapper.getBusinessChanceByChanceNo(bussinessChanceNo);
    }

    /**
     * <b>Description:</b><br>
     * 保存商机
     *
     * @param bussinessChance
     * @param user
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月26日 下午2:17:38
     */
    @Override
    public ResultInfo<?> saveBussinessChance(BussinessChance bussinessChance, User user, Attachment attachment) {
        //商机对应销售（售后添加商机时，需分配销售人员；；销售管理商机页面无指定人员）
        Integer bussinessChanceUserId = bussinessChance.getUserId();
        Map<String, Object> map = new HashMap<>();
        if (bussinessChance.getBussinessChanceId() == null || bussinessChance.getBussinessChanceId() == 0) {
            bussinessChance.setCreator(user.getUserId());
            bussinessChance.setAddTime(DateUtil.sysTimeMillis());
            if (bussinessChance.getType().equals(SysOptionConstant.ID_392)) {
                bussinessChance.setOrgId(user.getOrgId());
                bussinessChance.setUserId(user.getUserId());
            }
        }

        map.put("bussinessChance", bussinessChance);
        bussinessChance.setUpdater(user.getUserId());
        bussinessChance.setModTime(DateUtil.sysTimeMillis());

        if (attachment != null) {
            attachment.setCreator(user.getUserId());
            attachment.setAddTime(DateUtil.sysTimeMillis());
            attachment.setAttachmentFunction(SysOptionConstant.ID_463);

            //根据attachmentId来区分附件的存储位置，当attachmentId = 1时，存储在oss上；否则存储在ftp server上
            if (attachment.getAttachmentId() != null && attachment.getAttachmentId() == -1) {
                attachment.setDomain(ossUrl);
                //attachmentId置为空
                attachment.setAttachmentId(null);
            } else {
                attachment.setDomain(picUrl);
                String str = attachment.getUri().substring(attachment.getUri().lastIndexOf(".") + 1);
                if (str.equals("jpg") || str.equals("png") || str.equals("gif")) {
                    attachment.setAttachmentType(SysOptionConstant.ID_460);
                } else {
                    attachment.setAttachmentType(SysOptionConstant.ID_461);
                }
            }

            map.put("attachment", attachment);
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<BussinessChance>> TypeRef = new TypeReference<ResultInfo<BussinessChance>>() {
        };
        try {
            logger.info("请求保存商机 bussinessChance:{}" + bussinessChance.toString());
            ResultInfo<BussinessChance> result = (ResultInfo<BussinessChance>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_BUSSNESS_CHANCE, map, clientId, clientKey, TypeRef);

            if (result != null && result.getCode() == 0 && result.getData() != null && null != bussinessChanceUserId) {
                BussinessChance bc = (BussinessChance) result.getData();
                //VDERP-10080新增商机，解锁公海锁定客户start
                publicCustomerRecordApiService.unLockTrader(bc.getTraderId(), bc.getBussinessChanceId(), ErpConst.ONE, user.getUserId());
                //VDERP-10080新增商机，解锁公海锁定客户end
                // -------------关联成功 联系人注册 商机关联客户，商机的客户所属平台为贝登医疗，客户性质为分销 --------
                if (bussinessChance.getTraderContactId() != null) {
                    autoRegistrationService.doRegistration(bussinessChance.getTraderContactId(), false);
                }
                // -------------关联成功 联系人注册--------

                if (null != bc) {
                    //销售自建类商机不应该收到消息提醒
                    if (user.getUserId().equals(bc.getUserId())) {
                        return result;
                    }
                    List<Integer> userIdList = new ArrayList<Integer>();
                    Map<String, String> mesMap = new HashMap<>();
                    mesMap.put("bussinessChanceNo", bc.getBussinessChanceNo());

                    if (bc.getUserId() == null || bc.getUserId() == 0) {
                        //如果商机没有分配到销售，则给售前总机角色的账户发送站内消息
                        userIdList = rUserRoleMapper.getUserIdListAndNotDisabled(Collections.singletonList(17));
                    } else {
                        userIdList.add(bc.getUserId());
                    }
                    //推送消息，如果商机关联到销售，则向销售推送消息，如果没有关联销售，则向售前总机角色推送消息
//                    MessageUtil.sendMessage(69, userIdList, mesMap, "./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=" + bc.getBussinessChanceId() + "&traderId=0");
                    MessageUtil.sendMessage(69, userIdList, mesMap, "/businessChance/details.do?id=" + bc.getBussinessChanceId());


                }

            }
            return result;
        } catch (IOException e) {
            try {
                logger.error("" + JsonUtils.translateToJson(bussinessChance), e);
            } catch (IOException ioException) {
                //IGNOR
            }
            return null;
        }

    }

    /**
     * <b>Description:</b><br>
     * 查询售后商机详情
     *
     * @param bussinessChance
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年6月27日 下午6:54:40
     */
    @Override
    public Map<String, Object> getAfterSalesDetail(BussinessChance bussinessChance, Page page) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<BussinessChanceVo>> TypeRef2 = new TypeReference<ResultInfo<BussinessChanceVo>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_BUSSNESS_CHANCE_DETAIL,
                    bussinessChance, clientId, clientKey, TypeRef2);
            Map<String, Object> map = new HashMap<>();
//			JSONObject json = JSONObject.fromObject(result2.getData());
//			BussinessChanceVo bcv = (BussinessChanceVo) JSONObject.toBean(json, BussinessChanceVo.class);
            BussinessChanceVo bcv = (BussinessChanceVo) result2.getData();
            if (ObjectUtils.notEmpty(bcv.getCreator())) {
                bcv.setCreatorName(userMapper.selectByPrimaryKey(bcv.getCreator()).getUsername());
            }

            if (ObjectUtils.notEmpty(bcv.getUserId())) {
                User user = userMapper.selectByPrimaryKey(bcv.getUserId());
                bcv.setSalerName(user == null ? null : user.getUsername());
            }
            if (ObjectUtils.notEmpty(bcv.getAreaId())) {
                bcv.setAreas(getAddressByAreaId(bcv.getAreaId()));
            }
            map.put("bussinessChanceVo", bcv);
            CommunicateRecord cr = new CommunicateRecord();
            cr.setBussinessChanceId(bcv.getBussinessChanceId());
            //VDERP-3323 商机、报价、订单、客户信息模块中沟通记录信息相互同步
            //查看商机是否有关联的报价单和订单
            Optional.ofNullable(quoteorderMapper.getQuoteorderByBussinessChanceId(bcv.getBussinessChanceId()))
                    .ifPresent(quoteorder -> {
                        cr.setQuoteorderId(quoteorder.getQuoteorderId());
                        List<Saleorder> saleorderList = saleorderMapper.getSaleorderByQuoteorderId(quoteorder.getQuoteorderId());
                        if (!saleorderList.isEmpty()) {
                            cr.setSaleorderId(saleorderList.get(0).getSaleorderId());
                        }
                    });
            Map<String, Object> paraMap = new HashMap<>();
            paraMap.put("communicateRecord", cr);
            paraMap.put("page", page);
            List<CommunicateRecord> communicateList = new ArrayList<>();
            try {
                communicateList = traderCustomerService.getCommunicateRecordListPage(cr, page);
                for (int i = 0; i < communicateList.size(); i++) {
                    //判断是否转译完成
                    PhoneticWriting phoneticWriting = phoneticWritingMapper.getPhoneticWriting(communicateList.get(i).getCommunicateRecordId());
                    if (phoneticWriting != null) {
                        if (StringUtils.isNotBlank(phoneticWriting.getOriginalContent())) {
                            communicateList.get(i).setIsTranslation(1);
                        } else {
                            communicateList.get(i).setIsTranslation(0);
                        }
                    } else {
                        communicateList.get(i).setIsTranslation(0);
                    }
                }
            } catch (Exception e) {

                logger.error(Contant.ERROR_MSG, e);
            }
            if (communicateList != null && communicateList.size() > 0) {
                map.put("communicateList", communicateList);
            }
            return map;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * <b>Description:</b><br> 跳转编辑售后商机页面
     *
     * @param bussinessChance
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月29日 下午1:08:47
     */
    @Override
    public BussinessChanceVo toAfterSalesEditPage(BussinessChance bussinessChance) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_BUSSNESS_CHANCE_DETAIL,
                    bussinessChance, clientId, clientKey, TypeRef2);
            JSONObject json = JSONObject.fromObject(result2.getData());
            Map<String, Class> classMap = new HashMap<>();
            classMap.put("oldChanceList", MergeChanceGoods.class);
            BussinessChanceVo bcv = (BussinessChanceVo) JSONObject.toBean(json, BussinessChanceVo.class, classMap);
//			bcv.setCreatorName(userMapper.selectByPrimaryKey(bcv.getCreator()).getUsername());
//			bcv.setSalerName(userMapper.selectByPrimaryKey(bcv.getUserId()).getUsername());
            if (bcv.getAreaId() != null && bcv.getAreaId() != 0) {
                bcv.setAreas(getAddressByAreaId(bcv.getAreaId()));
            }
            return bcv;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * <b>Description:</b><br> 保存确认的客户信息
     *
     * @param bussinessChance
     * @param user
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年6月30日 下午5:42:28
     */
    @Override
    public TraderCustomerVo saveConfirmCustomer(BussinessChance bussinessChance, User user, TraderContact traderContact) {
        bussinessChance.setOrgId(user.getOrgId());
        bussinessChance.setUpdater(user.getUserId());
        bussinessChance.setModTime(DateUtil.sysTimeMillis());
        //确认客户，同步更新商机的归属销售
        bussinessChance.setUserId(rTraderJUserMapper.getUserByTraderId(bussinessChance.getTraderId()).getUserId());
        //新增客户联系人，不更新商机的基本信息
        bussinessChance.setMobile(null);
        bussinessChance.setTelephone(null);
        Map<String, Object> paraMap = new HashMap<>();
        paraMap.put("bussinessChance", bussinessChance);
        if ((bussinessChance.getTraderContactId() == null || bussinessChance.getTraderContactId() == 0) && traderContact != null) {
            traderContact.setCreator(user.getUserId());
            traderContact.setAddTime(DateUtil.sysTimeMillis());
            traderContact.setUpdater(user.getUserId());
            traderContact.setModTime(DateUtil.sysTimeMillis());
            traderContact.setTraderId(bussinessChance.getTraderId());
            traderContact.setIsOnJob(ErpConst.ONE);
            traderContact.setTraderType(ErpConst.ONE);
            traderContact.setIsEnable(ErpConst.ONE);
            paraMap.put("traderContact", traderContact);
        }
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef2 = new TypeReference<ResultInfo<?>>() {
        };
        try {
			/*ResultInfo<?> result2 = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.SAVE_CONFIRM_CUSTOMER, paraMap,
					clientId, clientKey, TypeRef2);
			JSONObject josn=JSONObject.fromObject(result2.getData());
			TraderCustomerVo tcv=(TraderCustomerVo) JSONObject.toBean(josn, TraderCustomerVo.class);*/
            // 代理对象，避免事务失效
            BussinessChanceServiceImpl thisProxy = (BussinessChanceServiceImpl) (AopContext.currentProxy());
            TraderCustomerVo tcv = thisProxy.saveConfirmCustomer(bussinessChance, traderContact);
            return tcv;
        } catch (Exception e) {
            logger.error("" + JSON.toJSONString(bussinessChance), e);
            return null;
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public TraderCustomerVo saveConfirmCustomer(BussinessChance bussinessChance, TraderContact tc) throws Exception {

        List<TraderContact> list = null;
        if (tc != null && !org.springframework.util.StringUtils.isEmpty(tc.getMobile()) && Objects.isNull(tc.getTraderId())) {
            list = traderContactMapper.getTraderContactByNameAndMobile(tc);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            TraderCustomerVo tcv = new TraderCustomerVo();
            tcv.setTraderCustomerId(-1);
            return tcv;
        }
        if (tc != null) {
            traderContactMapper.insertSelective(tc);
        } else {
            tc = traderContactMapper.selectByPrimaryKey(bussinessChance.getTraderContactId());
        }
        bussinessChance.setCheckTraderContactName(tc.getName());
        bussinessChance.setCheckTraderContactTelephone(tc.getTelephone());
        bussinessChance.setCheckMobile(tc.getMobile());
        bussinessChance.setTraderContactId(tc.getTraderContactId());
        TraderCustomerVo tcv = traderCustomerMapper.getCustomerInfo(bussinessChance.getTraderId());
        tcv.setTraderContactId(tc.getTraderContactId());
        Integer num = bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChance);
        if (num > 0) {
            return tcv;
        }
        return null;
    }

    /**
     * <b>Description:</b><br> 获取上传文件的域名
     *
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年7月17日 下午3:47:39
     */
    @Override
    public String getUploadDomain() {
        return picUrl;
    }

    @Override
    public ResultInfo editBussinessChance(BussinessChance bussinessChance) {
        String url = httpUrl + "order/bussinesschance/editbussinesschance.htm";
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {
        };
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, bussinessChance, clientId, clientKey, TypeRef);
            if (result.getCode() == 0) {
                return new ResultInfo(0, "操作成功");
            } else {
                return new ResultInfo();
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo();
        }
    }

    @Override
    public BussinessChance getBussinessChanceInfo(BussinessChance bussinessChance) {
        return bussinessChanceMapper.selectByPrimaryKey(bussinessChance.getBussinessChanceId());
    }

    @Override
    public Boolean saveAddBussinessStatus(BussinessChance bussinessChance) {
        Boolean flag = false;
        bussinessChance.setType(null);
        int i = bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChance);
        if (i > 0) {
            flag = true;
            closeBncAfterLink(bussinessChance);
        }
        return flag;
    }

    @Override
    public List<BussinessChanceVo> getTraderHistoryListPage(BussinessChanceVo bussiness, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("page", page);
        map.put("bussinessChanceVo", bussiness);
        List<BussinessChanceVo> traderHistoryListPage = bussinessChanceMapper.getTraderHistoryListPage(map);
        for (BussinessChanceVo bussinessChanceVo : traderHistoryListPage) {
            bussinessChanceVo.setAssignTimeStr(DateUtil.convertString(bussinessChanceVo.getAssignTime(), "yyyy-MM-dd hh:mm:ss"));
            bussinessChanceVo.setFirstViewTimeStr(DateUtil.convertString(bussinessChanceVo.getFirstViewTime(), "yyyy-MM-dd hh:mm:ss"));
        }
        return traderHistoryListPage;
    }

    @Override
    public List<BussinessChanceVo> getTraderHasHistoryBussiness(Integer traderId) {
        return bussinessChanceMapper.getTraderHasHistoryBussiness(traderId);
    }

    @Override
    public void sendMessageIfMerge(Integer id, BussinessChance bc) {
        if ((id == null || id < 1)
                && (bc != null && OrderConstant.NEW_MERGED_STATUS.equals(bc.getMergeStatus()))
                && (bc.getUserId() != null && bc.getUserId() > 0)) {
            List<Integer> userIds = new ArrayList<>();

            userIds.add(bc.getUserId());
            Map<String, String> map = new HashMap<>();
            map.put("bussinessChanceNo", bc.getBussinessChanceNo());
//            String url = "./order/bussinesschance/toAfterSalesDetailPage.do?bussinessChanceId=" + bc.getBussinessChanceId();
            String url = "/businessChance/details.do?id=" + bc.getBussinessChanceId();
            MessageUtil.sendMessage(109, userIds, map, url);
        }

    }

    @Override
    public ResultInfo<User> getOldChanceOwner(String mobile) {
        if (StringUtil.isBlank(mobile)) {
            return new ResultInfo<>();
        }
        String nowDate = DateUtil.getNowDate(null);
        String beginTimeStr = nowDate + " 00:00:00";
        String endTimeStr = nowDate + " 23:59:59";
        MergeBussinessChanceQuery query = new MergeBussinessChanceQuery();
        query.setMobile(mobile);
        query.setBeginTime(DateUtil.convertLong(beginTimeStr, DateUtil.TIME_FORMAT));
        query.setEndTime(DateUtil.convertLong(endTimeStr, DateUtil.TIME_FORMAT));
        BussinessChance oldChance = bussinessChanceMapper.getChanceCanMerge(query);
        User user = new User();
        if (oldChance != null) {
            user.setUserId(oldChance.getUserId());
        }
        ResultInfo<User> resultInfo = new ResultInfo<>(0, "操作成功");
        resultInfo.setData(user);
        return resultInfo;
    }

    @Override
    public ResultInfo getBussinessChanceAndQuoteInfo(Integer bussinessChanceId) {
        ResultInfo resultInfo = new ResultInfo();
        if (bussinessChanceId == null || bussinessChanceId == 0) {
            return resultInfo;
        }
        BussinessChanceVo bussinessChanceVo = bussinessChanceMapper.getBusinessChanceInfo(bussinessChanceId);
        if (bussinessChanceVo.getQuoteorderId() != null) {
            Quoteorder quoteorder = new Quoteorder();
            quoteorder.setQuoteorderId(bussinessChanceVo.getQuoteorderId());
            quoteorder = quoteorderMapper.getQuoteInfoByQuoteordeId(quoteorder);

            bussinessChanceVo.setQuoteorder(quoteorder);
        }
        resultInfo.setCode(0);
        resultInfo.setData(bussinessChanceVo);
        return resultInfo;
    }

    @Override
    public void closeBncAfterLink(BussinessChance bussinessChance) {
        if (StringUtil.isBlank(bussinessChance.getBncLink())
                || bussinessChance.getBncLink().length() < ErpConst.FIVE) {
            return;
        }
        List<BncLink> linkedList = JSON.parseArray(bussinessChance.getBncLink(), BncLink.class);
        linkedList.stream().forEach(b -> {
            BussinessChance closeBnc = new BussinessChance();
            closeBnc.setBussinessChanceId(b.getId());
            closeBnc.setStatus(ErpConst.FOUR);
            closeBnc.setClosedComments("手动关闭无效商机");
            bussinessChanceMapper.updateByPrimaryKeySelective(closeBnc);
        });
    }

    private Integer getBcStatusByTerm(BussinessChance bussinessChance) {
        if (BCConstants.BC_CLOSED.equals(bussinessChance.getStatus())) {
            return BCConstants.BC_CLOSED;
        }
        Quoteorder quoteorder = quoteorderMapper.getQuoteorderByBCId(bussinessChance.getBussinessChanceId());
        if (quoteorder != null && quoteorder.getQuoteorderId() > 0) {
            List<Saleorder> saleorderList = saleorderMapper.getSaleorderByQuoteorderId(quoteorder.getQuoteorderId());
            if (CollectionUtils.isNotEmpty(saleorderList)) {
                for (Saleorder s : saleorderList) {
                    if (OrderConstant.ORDER_ALL_PAYMENT.equals(s.getPaymentStatus())) {
                        return BCConstants.BC_ORDERED;
                    }
                }
                return BCConstants.BC_ORDERING;
            }
            if (ErpConst.ONE.equals(quoteorder.getValidStatus())) {
                return BCConstants.BC_QUOTED;
            }
            return BCConstants.BC_QUOTING;
        }


        if (bussinessChance.getUserId() == null || bussinessChance.getUserId() == 0) {
            return BCConstants.BC_UN_ASSIGN;
        }
        CommunicateRecord cr = new CommunicateRecord();
        cr.setBussinessChanceId(bussinessChance.getBussinessChanceId());
        Map<String, Object> map = new HashMap<>();
        map.put("communicateRecord", cr);
        List<CommunicateRecord> records = communicateRecordMapper.getCommunicateRecordList(map);
        if (CollectionUtils.isNotEmpty(records)) {
            return BCConstants.BC_HANDLED;
        }
        return BCConstants.BC_UN_HANDLE;
    }

    @Override
    public void updateBcStatusByTerm(BussinessChance bussinessChance) {
        if (bussinessChance == null
                || bussinessChance.getBussinessChanceId() == null
                || bussinessChance.getBussinessChanceId() == 0) {
            return;
        }
        bussinessChance.setStatus(getBcStatusByTerm(bussinessChance));
        logger.info("更新商机状态信息 info:{}", JSON.toJSONString(bussinessChance));
        bussinessChanceMapper.updateBCStatus(bussinessChance);
    }

    @Override
    public BussinessChance relateCloseBussChance(Integer bussinessChanceId, String optionType, Integer level) {
        BussinessChanceVo businessChanceInfo = bussinessChanceMapper.getBusinessChanceInfo(bussinessChanceId);
        if (Objects.isNull(businessChanceInfo)) {
            return null;
        }
        Integer closeReason = getSysOptionDefIdBypt(395, "SYS_AUTO_CLOSE_TYPE_2");
        String closeComment = "";
        if (level == 3) {
            closeComment = "订单关闭后，自动关闭商机";
        }

        if (level == 2) {
            closeComment = "报价单关闭后，自动关闭商机";
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        bussinessChanceMapper.relateCloseBussChanceById(businessChanceInfo.getBussinessChanceId(), closeReason, closeComment);
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(bussinessChanceId);
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_CLOSE);

        try{
            RemoteCrmTaskApiService.TaskDto taskDto = new RemoteCrmTaskApiService.TaskDto();
            taskDto.setBizId(bussinessChanceId);
            taskDto.setApiUserId(currentUser.getId());
            taskDto.setApiUserName(currentUser.getUsername());
            remoteCrmTaskApiService.closeTaskForBusinessChance(taskDto);
        }catch (Exception e){
            logger.error("商机关闭时，自动关闭任务失败",e);
        }


        return businessChanceInfo;
    }

    @Autowired
    RemoteCrmTaskApiService remoteCrmTaskApiService;


    @Override
    public int updateEditComments(BussinessChance bussinessChance) {
        int num = bussinessChanceMapper.updateEditComments(bussinessChance);
        return num;
    }

    @Override
    public void updateBcStatusIfSaleorderPaid(Integer saleorderId, Integer status) {
        logger.info("订单付款状态变更，更新商机状态，orderId:{},status:{}", saleorderId, status);
        try {
            BussinessChance bussinessChance = bussinessChanceMapper.getBCBySaleorderId(saleorderId);
            if (bussinessChance != null
                    && bussinessChance.getBussinessChanceId() != null
                    && !BCConstants.BC_CLOSED.equals(bussinessChance.getStatus())) {
                bussinessChance.setStatus(status);
                bussinessChanceMapper.updateBCStatus(bussinessChance);
            }
        } catch (Exception ex) {
            logger.error("订单付款状态变更，更新商机状态失败,orderId:{},status:{}，exception:{}", saleorderId, status, ex);
        }
    }

    public Integer getSysOptionDefIdBypt(Integer parentId, String optionType) {
        SysOptionDefinition sysOptionDefinition;
        List<SysOptionDefinition> sysOptionDefinitionListByParentId = getSysOptionDefinitionListByParentId(parentId);
        sysOptionDefinition = sysOptionDefinitionListByParentId.stream().filter(item -> optionType.equals(item.getOptionType())).findFirst().orElse(null);
        return sysOptionDefinition != null ? sysOptionDefinition.getSysOptionDefinitionId() : 0;
    }

    @Override
    public void updateFirstViewTime(BussinessChance bussinessChance) {
        bussinessChanceMapper.updateByPrimaryKeySelective(bussinessChance);
    }

    @Override
    public List<NewSourceDicDto> getNewSource(List<SysOptionDefinition> newSourceListAll) {
        List<NewSourceDicDto> newSourceDicDtoList = new ArrayList<>();
        newSourceListAll.forEach(parent -> {
            NewSourceDicDto dicDto = new NewSourceDicDto();
            dicDto.setSysOptionDefinitionId(parent.getSysOptionDefinitionId());
            dicDto.setTitle(parent.getTitle());
            dicDto.setSort(parent.getSort());
            dicDto.setParentNode(Boolean.TRUE);
            //获取子节点
            List<SysOptionDefinition> childNodes = sysOptionDefinitionMapper.getDictionaryByParentId(parent.getSysOptionDefinitionId());
            List<NewSourceDicDto> childList = new ArrayList<>();
            childNodes.forEach(child -> {
                NewSourceDicDto childDto = new NewSourceDicDto();
                childDto.setSysOptionDefinitionId(child.getSysOptionDefinitionId());
                childDto.setTitle(child.getTitle());
                childDto.setSort(child.getSort());
                childDto.setParentNode(Boolean.FALSE);
                childList.add(childDto);
            });
            dicDto.setChildren(childList.stream().sorted(Comparator.comparing(NewSourceDicDto::getSort)).collect(Collectors.toList()));
            newSourceDicDtoList.add(dicDto);
        });
        return newSourceDicDtoList.stream().sorted(Comparator.comparing(NewSourceDicDto::getSort)).collect(Collectors.toList());
    }
}
