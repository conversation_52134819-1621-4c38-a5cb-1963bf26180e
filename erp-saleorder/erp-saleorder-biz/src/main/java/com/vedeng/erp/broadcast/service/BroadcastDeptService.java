package com.vedeng.erp.broadcast.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptFormDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptListDto;
import com.vedeng.erp.broadcast.domain.dto.BroadCastDeptQueryDto;
import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptEntity;

import java.util.List;

public interface BroadcastDeptService {
    /**
     * 根据父ID获取的广播部门
     * @param parentId
     * @return
     */
     List<BroadcastDeptEntity> getBroadcastDeptListByParentId(Integer parentId,String deptName);
     /**
      * 获取所有广播部门
      * @return
      */
      List<BroadcastDeptEntity> getBroadcastDeptListAll();

      /**
       * 分页查询播报部门列表
       * 支持部门名称模糊查询、父级部门筛选和AED用户筛选
       * 
       * @param pageParam 分页查询参数，包含分页信息和查询条件
       * @return 分页播报部门列表，包含总数、页码等分页信息
       */
      PageInfo<BroadCastDeptListDto> getBroadcastDeptListPage(PageParam<BroadCastDeptQueryDto> pageParam);
      
      /**
       * 保存播报部门
       * 新增播报部门或小组
       * 
       * @param formDto 播报部门表单数据
       */
      void saveBroadcastDept(BroadCastDeptFormDto formDto);
      
      /**
       * 更新播报部门
       * 编辑已存在的播报部门或小组
       * 
       * @param formDto 播报部门表单数据，必须包含ID
       */
      void updateBroadcastDept(BroadCastDeptFormDto formDto);
      
      /**
       * 删除播报部门
       * 逻辑删除播报部门或小组
       * 
       * @param id 播报部门ID
       */
      void deleteBroadcastDept(Integer id);
}
