package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchAttachmentDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAttachmentDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购赠品入库单附件推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BuyOrderGiftInAcceptanceFormProcessor extends BaseProcessor<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto> {
    public static final String ZERO = "0";

    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private BatchAttachmentDtoMapper batchAttachmentDtoMapper;

    @Autowired
    private KingDeeStorageInService kingDeeStorageInService;

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Override
    public KingDeeFileDataDto doProcess(BatchWarehouseGoodsOutInDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购赠品入库单附件入参对象：{}", JSON.toJSONString(input));
        BatchAttachmentDto query = BatchAttachmentDto.builder()
                .attachmentType(462)
                .attachmentFunction(4211)
                .relatedId(input.getWarehouseGoodsOutInId().intValue())
                .build();
        BatchAttachmentDto batchAttachmentDto = batchAttachmentDtoMapper.purchaseInfindByQuery(query);
        if (Objects.isNull(batchAttachmentDto) || StrUtil.isEmpty(batchAttachmentDto.getUri()) || StrUtil.isEmpty(batchAttachmentDto.getDomain())) {
            log.info("暂未查询到采购赠品入库单附件: {}", JSON.toJSONString(input));
            return null;
        }

        String fileUrl = ossHttp + batchAttachmentDto.getDomain() + batchAttachmentDto.getUri();
        KingDeeFileDataDto kingDeeFileQuery = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.STK_MISCELLANEOUS)
                .erpId(input.getWarehouseGoodsOutInId().toString())
                .url(fileUrl)
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(kingDeeFileQuery);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(kingDeeFileQuery));
            return null;
        }

        KingDeeStorageInDto storageInDto = new KingDeeStorageInDto();
        storageInDto.setFBillNo(input.getOutInNo());
        kingDeeStorageInService.query(storageInDto);
        if (storageInDto.getFId() == null || ZERO.equals(storageInDto.getFId())) {
            log.info("上传采购赠品入库单附件,入库单未推送金蝶：{}", input.getOutInNo());
            return null;
        }

        FileInfoDto base64FromUrl = FileInfoUtils.getBase64FromUrl(fileUrl);
        String name = StrUtil.isEmpty(batchAttachmentDto.getName()) ? "file" + base64FromUrl.getSuffix() : batchAttachmentDto.getName() + base64FromUrl.getSuffix();
        return KingDeeFileDataDto.builder()
                .fileName(name)
//                .sendByte(base64FromUrl.getFileBase64())
                .aliasFileName(batchAttachmentDto.getName())
                .billNo(input.getOutInNo())
                .formId(storageInDto.getFormId())
                .isLast(true)
                .fId(storageInDto.getFId())
                .url(fileUrl)
                .erpId(input.getWarehouseGoodsOutInId().toString())
                .businessId(storageInDto.getFormId() + input.getWarehouseGoodsOutInId().toString())
                .build();
    }
}
