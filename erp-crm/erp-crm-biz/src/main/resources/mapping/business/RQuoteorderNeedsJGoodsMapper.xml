<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.business.quote.mapper.RQuoteorderNeedsJGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_QUOTEORDER_NEEDS_J_GOODS-->
    <id column="R_QUOTEORDER_NEEDS_J_GOODS_ID" jdbcType="BIGINT" property="rQuoteorderNeedsJGoodsId" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="QUOTEORDER_NEEDS_ID" jdbcType="BIGINT" property="quoteorderNeedsId" />
    <result column="QUOTEORDER_GOODS_ID" jdbcType="INTEGER" property="quoteorderGoodsId" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    R_QUOTEORDER_NEEDS_J_GOODS_ID, QUOTEORDER_ID, QUOTEORDER_NEEDS_ID, QUOTEORDER_GOODS_ID,
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_QUOTEORDER_NEEDS_J_GOODS
    where R_QUOTEORDER_NEEDS_J_GOODS_ID = #{rQuoteorderNeedsJGoodsId,jdbcType=BIGINT}
  </select>

  <select id="selectByQuoteorderGoodsId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_R_QUOTEORDER_NEEDS_J_GOODS
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER} and IS_DELETE = 0
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_R_QUOTEORDER_NEEDS_J_GOODS
    where R_QUOTEORDER_NEEDS_J_GOODS_ID = #{rQuoteorderNeedsJGoodsId,jdbcType=BIGINT}
  </delete>
  <insert id="insertSelective" keyColumn="R_QUOTEORDER_NEEDS_J_GOODS_ID" keyProperty="rQuoteorderNeedsJGoodsId" parameterType="com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_QUOTEORDER_NEEDS_J_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="quoteorderNeedsId != null">
        QUOTEORDER_NEEDS_ID,
      </if>
      <if test="quoteorderGoodsId != null">
        QUOTEORDER_GOODS_ID,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNeedsId != null">
        #{quoteorderNeedsId,jdbcType=BIGINT},
      </if>
      <if test="quoteorderGoodsId != null">
        #{quoteorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity">
    <!--@mbg.generated-->
    update T_R_QUOTEORDER_NEEDS_J_GOODS
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNeedsId != null">
        QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT},
      </if>
      <if test="quoteorderGoodsId != null">
        QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where R_QUOTEORDER_NEEDS_J_GOODS_ID = #{rQuoteorderNeedsJGoodsId,jdbcType=BIGINT}
  </update>

  <update id="updateByQuoteGoodsId" parameterType="com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity">
    <!--@mbg.generated-->
    update T_R_QUOTEORDER_NEEDS_J_GOODS
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="quoteorderNeedsId != null">
        QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.business.quote.domain.entity.RQuoteorderNeedsJGoodsEntity">
    <!--@mbg.generated-->
    update T_R_QUOTEORDER_NEEDS_J_GOODS
    set QUOTEORDER_NEEDS_ID = #{quoteorderNeedsId,jdbcType=BIGINT},
      QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where R_QUOTEORDER_NEEDS_J_GOODS_ID = #{rQuoteorderNeedsJGoodsId,jdbcType=BIGINT}
  </update>

  <select id="selectByQuoteorderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_R_QUOTEORDER_NEEDS_J_GOODS
    where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} AND IS_DELETE = 0
  </select>

  <update id="batchDeleteByQuoteorderNeedsId">
    update T_R_QUOTEORDER_NEEDS_J_GOODS set IS_DELETE = 1
    where QUOTEORDER_NEEDS_ID in
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.quoteorderNeedsId,jdbcType=BIGINT}
    </foreach>
    </update>
</mapper>
