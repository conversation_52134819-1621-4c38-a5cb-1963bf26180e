package com.vedeng.erp.aftersale.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 退票记录退票状态
 * @date 2022/10/28 14:50
 */
public enum ExpenseAfterSalesInvoiceStatusEnum {

    /**
     * 退票状态
     */
    NOT(0, "未退票"),
    ALREADY(1, "已退票"),
    LOADING(2, "退票中"),
    DONT_NEED(3, "无需退票");

    private final Integer code;

    private final String desc;


    ExpenseAfterSalesInvoiceStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
