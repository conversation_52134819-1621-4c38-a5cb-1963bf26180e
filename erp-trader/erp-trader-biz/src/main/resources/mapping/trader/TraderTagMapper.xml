<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderTagMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderTagEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_TAG-->
    <id column="TRADER_TAG_ID" jdbcType="INTEGER" property="traderTagId" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TAG_ID" jdbcType="INTEGER" property="tagId" />
  </resultMap>

  <resultMap id="BaseDtoResultMap" type="com.vedeng.erp.trader.domain.dto.TraderTagDto">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_TAG-->
    <id column="TRADER_TAG_ID" jdbcType="INTEGER" property="traderTagId" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TAG_ID" jdbcType="INTEGER" property="tagId" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_TAG_ID, TRADER_TYPE, TRADER_ID, TAG_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_TAG
    where TRADER_TAG_ID = #{traderTagId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_TAG
    where TRADER_TAG_ID = #{traderTagId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_TAG_ID" keyProperty="traderTagId" parameterType="com.vedeng.erp.trader.domain.entity.TraderTagEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_TAG (TRADER_TYPE, TRADER_ID, TAG_ID
      )
    values (#{traderType,jdbcType=INTEGER}, #{traderId,jdbcType=INTEGER}, #{tagId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_TAG_ID" keyProperty="traderTagId" parameterType="com.vedeng.erp.trader.domain.entity.TraderTagEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_TAG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="tagId != null">
        TAG_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderType != null">
        #{traderType,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        #{tagId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderTagEntity">
    <!--@mbg.generated-->
    update T_TRADER_TAG
    <set>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="tagId != null">
        TAG_ID = #{tagId,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_TAG_ID = #{traderTagId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderTagEntity">
    <!--@mbg.generated-->
    update T_TRADER_TAG
    set TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TAG_ID = #{tagId,jdbcType=INTEGER}
    where TRADER_TAG_ID = #{traderTagId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_TAG_ID" keyProperty="traderTagId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_TAG
    (TRADER_TYPE, TRADER_ID, TAG_ID)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderType,jdbcType=INTEGER}, #{item.traderId,jdbcType=INTEGER}, #{item.tagId,jdbcType=INTEGER}
        )
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-07-25-->
  <delete id="deleteByTraderIdAndTraderType">
        delete from T_TRADER_TAG
        where TRADER_ID=#{traderId,jdbcType=INTEGER} and TRADER_TYPE=#{traderType,jdbcType=INTEGER}
    </delete>

  <resultMap id="TagDtoMap" type="com.vedeng.erp.system.dto.TagDto">
    <!--@mbg.generated-->
    <!--@Table T_TAG-->
    <id column="TAG_ID" jdbcType="INTEGER" property="tagId" />
    <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
    <result column="TAG_TYPE" jdbcType="INTEGER" property="tagType" />
    <result column="IS_RECOMMEND" jdbcType="BOOLEAN" property="isRecommend" />
    <result column="TAG_NAME" jdbcType="VARCHAR" property="tagName" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
  </resultMap>

  <select id="getCommunicateTags" resultMap="TagDtoMap">
    select
    b.*
    from
    T_TRADER_TAG a
    left join
    T_TAG b on a.TAG_ID=b.TAG_ID
    where
    a.TRADER_TYPE = 3
    and
    a.TRADER_ID =
      #{recordId}
  </select>
</mapper>