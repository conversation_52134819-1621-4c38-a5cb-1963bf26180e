package com.vedeng.crm.business.business.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.business.business.domain.dto.CrmBusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CrmBusinessChanceConvertor extends BaseMapStruct<BusinessChanceDto, CrmBusinessChanceDto> {

    @Mapping(target = "businessChanceId", source = "bussinessChanceId")
    @Mapping(target = "businessChanceNo", source = "bussinessChanceNo")
    CrmBusinessChanceDto toDto(BusinessChanceDto businessChanceDto);
}
