package com.vedeng.temporal.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BuyOrderResultDto {
    private Integer buyorderId;
    private String buyOrderNo;

    //收货地址ID，如果是直发的，对应销售订单表的收件地址，如果是普发，则对应是的我们仓库的地址，取T_ADDRESS
    private Integer takeTraderAddressId;

    //南京市栖霞区经天路6号中电熊猫物流园区4#库二层
    private String takeTraderAddress;

    //收件人姓名
    private String takeTraderContactName;

    //收件人手机号
    private String takeTraderContactMobile;

    //直普发，1直发 0普发
    private Integer deliveryDirect;

    //商品列表
    private List<BuyOrderGoodsResultDto> buyorderGoodsApiDtos ;



}
