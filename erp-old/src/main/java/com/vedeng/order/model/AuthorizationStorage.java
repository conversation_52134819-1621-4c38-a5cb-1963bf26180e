package com.vedeng.order.model;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class AuthorizationStorage {
    /**
     * 授权书暂存主键
     */
    private Integer temporaryStorageId;

    /**
     * 授权书申请号
     */
    private String authorizationApplyNum;

    /**
     * 报价单ID
     */
    private Integer quoteorderId;

    /**
     * SKU ID
     */
    private Integer skuId;

    /**
     * 采购单位/招标公司
     */
    private String purchaseOrBidding;

    /**
     * 生产厂家
     */
    private String productCompany;

    /**
     * 经营性质，1为生产，2为代理，3为销售
     */
    private Integer natureOfOperation;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 产品名称
     */
    private String skuName;

    /**
     * 产品型号
     */
    private String skuModel;

    /**
     * 经销类型，1为独家经销商，2为经销商，3为代理商
     */
    private Integer distributionsType;

    /**
     * 授权公司
     */
    private String authorizedCompany;

    /**
     * 采购项目全程
     */
    private String purchaseProjectName;

    /**
     * 采购项目编号
     */
    private String purchaseProjectNum;

    /**
     * 文件类型，1为投标，2为响应
     */
    private Integer fileType;

    /**
     * 售后公司全称
     */
    private String aftersalesCompany;

    /**
     * 授权有效期开始日期
     */
    private String beginTime;

    /**
     * 授权有效期结束日期
     */
    private String endTime;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 修改时间
     */
    private Long modTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 修改人
     */
    private Integer updator;

    /**
     * 描述
     */
    private String described;

    /**
     * 份数
     */
    private Integer num;

    /**
     * 是否被删除
     */
    private Integer isDeleted;

    /**
     * 申请年份
     */
    private String applyYear;

    /**
     * 申请月份
     */
    private String applyMonth;

    /**
     * 申请日
     */
    private String applyDay;

    /**
     * 是否标准模板
     */
    private Integer standardTemplate;

    /**
     * 非标授权书附件链接
     */
    private String nonStandardAuthorizationUrl;

    /**
     * 非标授权书附件名称
     */
    private String nonStandardAuthorizationName;

    /**
     * 附件是否可签章
     */
    private Integer whetherSign;

    /**
     * 0.项目授权 1.经销授权
     */
    private Integer authType;

    /**
     * 公章类型 1.南京贝登医疗股份有限公司 2.南京医购优选供应链管理有限公司
     */
    private Integer sealType;
}

