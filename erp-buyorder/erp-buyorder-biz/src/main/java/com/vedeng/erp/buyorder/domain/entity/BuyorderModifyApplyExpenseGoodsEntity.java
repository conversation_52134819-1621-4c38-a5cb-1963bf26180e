package com.vedeng.erp.buyorder.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 采购单修改费用商品
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BuyorderModifyApplyExpenseGoodsEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer buyorderModifyApplyExpenseGoodsId;

    /**
     * 修改申请id
     */
    private Integer buyorderModifyApplyId;

    /**
     * 采购订单产品ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 商品备注
     */
    private String insideComments;

    /**
     * 商品原备注
     */
    private String oldInsideComments;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

}