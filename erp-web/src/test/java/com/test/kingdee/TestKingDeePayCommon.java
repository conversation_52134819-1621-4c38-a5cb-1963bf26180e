package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeePayCommonCommand;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDetailLinkDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayCommonMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayCommonCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayCommonConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 应付单：标准应付单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeePayCommon {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePayCommonCommandConvertor commandConvertor;

    @Autowired
    private KingDeePayCommonConvertor kingDeePayCommonConvertor;

    @Autowired
    private KingDeePayCommonMapper kingDeePayCommonMapper;


    @Test
    public void kingDeePayCommon() {
        //一级dto
        KingDeePayCommonDto kingDeePayCommonDto = new KingDeePayCommonDto();
        kingDeePayCommonDto.setFId("0");
        kingDeePayCommonDto.setFBillTypeID("YFD01_SYS");
        kingDeePayCommonDto.setFDate("2022-09-17");
        kingDeePayCommonDto.setFQzokBddjtId( "贝登erp对应的单据头ID");
        kingDeePayCommonDto.setFSupplierId("ven0006");
        kingDeePayCommonDto.setFBusinessType("CG");
        kingDeePayCommonDto.setFInStockBusType("CG");
        kingDeePayCommonDto.setFSettleOrgId("101");
        kingDeePayCommonDto.setFPayOrgId("101");
        kingDeePayCommonDto.setFSetAccountType("1");

        //二级dto
        List<KingDeePayCommonDetailDto> kingDeePayCommonDetailDtoList = new ArrayList<>();
        KingDeePayCommonDetailDto  kingDeePayCommonDetailDto = new KingDeePayCommonDetailDto();
        kingDeePayCommonDetailDto.setFMaterialId("sku0001");
        kingDeePayCommonDetailDto.setFPriceQty("40.0");
        kingDeePayCommonDetailDto.setFTaxPrice("113.0");
        kingDeePayCommonDetailDto.setFEntryTaxRate("13.0");
        kingDeePayCommonDetailDto.setF_QZOK_BDDJHID("贝登订单行ID");
        kingDeePayCommonDetailDto.setFSourceType("STK_InStock");
        kingDeePayCommonDetailDto.setFNoTaxAmountFor_D("100.0");
        kingDeePayCommonDetailDto.setFTaxAmountFor_D("13.0");
        kingDeePayCommonDetailDto.setFAllAmountFor_D("113.0");
        //三级dto
        List<KingDeePayCommonDetailLinkDto> kingDeePayCommonDetailLinkDtoList = new ArrayList<>();
        KingDeePayCommonDetailLinkDto kingDeePayCommonDetailLinkDto = new KingDeePayCommonDetailLinkDto();
        kingDeePayCommonDetailLinkDto.setFLinkId("0");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FRuleId("AP_InStockToPayableMap");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FFlowLineId("0");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableId("0");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSTableName("T_STK_INSTOCKENTRY");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSBillId("100042");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FSId("100052");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTYOld("40");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FBASICUNITQTY("40");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQty("40");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FStockBaseQtyOld("40");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_DOld("4520");
        kingDeePayCommonDetailLinkDto.setFEntityDetail_Link_FALLAMOUNTFOR_D("4520");
        kingDeePayCommonDetailLinkDtoList.add(kingDeePayCommonDetailLinkDto);

        kingDeePayCommonDetailDto.setFEntityDetail_Link(kingDeePayCommonDetailLinkDtoList);
        kingDeePayCommonDetailDtoList.add(kingDeePayCommonDetailDto);
        kingDeePayCommonDto.setFEntityDetail(kingDeePayCommonDetailDtoList);


        KingDeePayCommonCommand command = commandConvertor.toCommand(kingDeePayCommonDto);

        System.out.println("入参："+JSON.toJSONString(command));
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, kingDeePayCommonDto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
        System.out.println("金蝶返回："+JSON.toJSONString(successEntities));
        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            System.out.println("数据库入参："+JSON.toJSONString(kingDeePayCommonConvertor.toEntity(kingDeePayCommonDto)));
            kingDeePayCommonMapper.insertSelective(kingDeePayCommonConvertor.toEntity(kingDeePayCommonDto));
        }
    }
}
