<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.UserDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.authorization.model.UserDetail" >
    <id column="USER_DETAIL_ID" property="userDetailId" jdbcType="INTEGER" />
    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
    <result column="REAL_NAME" property="realName" jdbcType="VARCHAR" />
    <result column="EMAIL" property="email" jdbcType="VARCHAR" />
    <result column="SEX" property="sex" jdbcType="INTEGER" />
    <result column="BIRTHDAY" property="birthday" jdbcType="DATE" />
    <result column="MOBILE" property="mobile" jdbcType="VARCHAR" />
    <result column="TELEPHONE" property="telephone" jdbcType="VARCHAR" />
    <result column="FAX" property="fax" jdbcType="VARCHAR" />
    <result column="QQ" property="qq" jdbcType="VARCHAR" />
    <result column="CC_NUMBER" property="ccNumber" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    USER_DETAIL_ID, USER_ID, REAL_NAME, EMAIL, SEX, BIRTHDAY, MOBILE, TELEPHONE, FAX, 
    QQ, CC_NUMBER
  </sql>
  <!-- 添加员工详情 -->
  <insert id="insert" parameterType="com.vedeng.authorization.model.UserDetail" >
    insert into T_USER_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="userDetailId != null" >
        USER_DETAIL_ID,
      </if>
      <if test="userId != null" >
        USER_ID,
      </if>
      <if test="realName != null" >
        REAL_NAME,
      </if>
      <if test="email != null" >
        EMAIL,
      </if>
      <if test="sex != null" >
        SEX,
      </if>
      <if test="birthday != null" >
        BIRTHDAY,
      </if>
      <if test="mobile != null" >
        MOBILE,
      </if>
      <if test="telephone != null" >
        TELEPHONE,
      </if>
      <if test="fax != null" >
        FAX,
      </if>
      <if test="qq != null" >
        QQ,
      </if>
      <if test="ccNumber != null" >
        CC_NUMBER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="userDetailId != null" >
        #{userDetailId,jdbcType=INTEGER},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="realName != null" >
        #{realName,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="sex != null" >
        #{sex,jdbcType=INTEGER},
      </if>
      <if test="birthday != null" >
        #{birthday,jdbcType=DATE},
      </if>
      <if test="mobile != null" >
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        #{fax,jdbcType=VARCHAR},
      </if>
      <if test="qq != null" >
        #{qq,jdbcType=VARCHAR},
      </if>
      <if test="ccNumber != null" >
        #{ccNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <!-- 编辑详情 -->
  <update id="update" parameterType="com.vedeng.authorization.model.UserDetail" >
    update T_USER_DETAIL
    <set >
      <if test="userId != null" >
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="realName != null" >
        REAL_NAME = #{realName,jdbcType=VARCHAR},
      </if>
      <if test="email != null" >
        EMAIL = #{email,jdbcType=VARCHAR},
      </if>
      <if test="sex != null" >
        SEX = #{sex,jdbcType=INTEGER},
      </if>
      <if test="birthday != null" >
        BIRTHDAY = #{birthday,jdbcType=DATE},
      </if>
      <if test="mobile != null" >
        MOBILE = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null" >
        TELEPHONE = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="fax != null" >
        FAX = #{fax,jdbcType=VARCHAR},
      </if>
      <if test="qq != null" >
        QQ = #{qq,jdbcType=VARCHAR},
      </if>
      <if test="ccNumber != null" >
        CC_NUMBER = #{ccNumber,jdbcType=VARCHAR},
      </if>
      <if test="aliasHeadPicture != null" >
        ALIAS_HEAD_PICTURE = #{aliasHeadPicture,jdbcType=VARCHAR},
      </if>
    </set>
    where USER_DETAIL_ID = #{userDetailId,jdbcType=INTEGER}
  </update>
  
  <!-- getUserDetail -->
  <select id="getUserDetail" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select 
  	<include refid="Base_Column_List" />
  	from T_USER_DETAIL
  	where USER_ID = #{userId,jdbcType=INTEGER}
  </select>
  
  <select id="getUserDetailByTraderId" resultType="com.vedeng.authorization.model.UserDetail">
  	select 
  		 a.USER_DETAIL_ID, a.USER_ID, a.REAL_NAME, a.EMAIL, a.SEX, a.BIRTHDAY, a.MOBILE, a.TELEPHONE, a.FAX, 
    	a.QQ, a.CC_NUMBER
  	from
  		T_USER_DETAIL a
 		left join T_R_TRADER_J_USER tr on tr.USER_ID =a.USER_ID
  		where tr.TRADER_ID = #{traderId,jdbcType=INTEGER}
  			and tr.TRADER_TYPE = #{type,jdbcType=INTEGER}
  </select>
</mapper>