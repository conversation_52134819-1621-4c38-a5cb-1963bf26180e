package com.vedeng.order.model.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @describe 航信采购商品录票信息
 * <AUTHOR>
 * @date 2020/6/3 16:30:05
 */
public class BuyorderGoodsRecordDTO implements Serializable {
    /**
     * 已录票数量（暂存）
     */
    private Integer recordedNum = 0;

    /**
     * 已录票金额（暂存）
     */
    private BigDecimal recordedAmount = new BigDecimal(0);

    public Integer getRecordedNum() {
        return recordedNum;
    }

    public void setRecordedNum(Integer recordedNum) {
        this.recordedNum = recordedNum;
    }

    public BigDecimal getRecordedAmount() {
        return recordedAmount;
    }

    public void setRecordedAmount(BigDecimal recordedAmount) {
        this.recordedAmount = recordedAmount;
    }
}
