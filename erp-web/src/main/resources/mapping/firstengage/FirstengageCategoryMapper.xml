<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.firstengage.dao.FirstengageCategoryMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.FirstengageCategory">
		<id column="CATEGORY_ID" property="categoryId" jdbcType="INTEGER" />
		<result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
		<result column="PARENT_ID" property="parentId" jdbcType="VARCHAR" />
		<result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
		<result column="TREENODES" property="treenodes" jdbcType="VARCHAR" />
		<result column="STATUS" property="status" jdbcType="VARCHAR" />
		<result column="LEVEL" property="level" jdbcType="INTEGER" />
		<result column="SORT" property="sort" jdbcType="INTEGER" />
		<result column="ICON_DOMAIN" property="iconDomain" jdbcType="BIGINT" />
		<result column="ICON_URI" property="iconUri" jdbcType="INTEGER" />
		<result column="DESCRIPTION" property="description" jdbcType="BIGINT" />
		<result column="ADD_TIME" property="addTime" jdbcType="INTEGER" />
		<result column="CREATOR" property="creator" jdbcType="VARCHAR"/>
		<result column="MOD_TIME" property="modTime" jdbcType="INTEGER" />
		<result column="UPDATER" property="updater" jdbcType="VARCHAR"/>
	</resultMap>
	
	<!-- 查询产品分类信息列表(分页) -->
	<select id="getCategorylistpage" parameterType="Map" resultMap="BaseResultMap">
		select
			*
		from T_CATEGORY a
		<where>
			STATUS = 1
			<if test="category.categoryId!=null and category.categoryId!=''">
				and a.CATEGORY_ID like CONCAT('%',#{category.categoryId,jdbcType=INTEGER},'%' )
			</if>
			<!-- <if test="category.categoryIdList!=null">
				AND a.CATEGORY_ID IN 
				<foreach item="cateId" index="index" collection="category.categoryIdList" separator="," open="(" close=")">
					#{cateId}
				</foreach>
			</if> -->
			<if test="category.categoryName!=null and category.categoryName!=''">
				and a.CATEGORY_NAME like CONCAT('%',#{category.categoryName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="category.companyId!=null and category.companyId!=''">
				and a.COMPANY_ID = #{category.companyId,jdbcType=VARCHAR}
			</if>
		</where>
		<choose>
			<when test="category.sortField!=null and category.sortField!='' and category.order!=null and category.order!=''">
				order by ${category.sortField} ${category.order}
			</when>
			<otherwise>
				order by a.SORT DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="getCategoryListByIdlist" parameterType="java.util.List" resultMap="BaseResultMap">
		SELECT  A.*
		FROM T_CATEGORY A
		<where>
			STATUS = 1
			<if test="categoryIdList != null">
				and A.CATEGORY_ID in
				<foreach collection="categoryIdList" item="categoryId" index="index" open="(" close=")" separator=",">
		            #{categoryId}
		        </foreach>
			</if>
		</where>
	</select>
	
	<select id="getRecursionCategoryList" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT *
		FROM (
			<foreach collection="categoryIds" item="categoryId" separator=" UNION ">
			  SELECT B.GOODS_NUM, A.*
		      FROM T_CATEGORY A
		           LEFT JOIN (SELECT A.CATEGORY_ID, COUNT(1) AS GOODS_NUM
		                      FROM T_GOODS A
		                      WHERE A.IS_DISCARD = 0
		                      GROUP BY A.CATEGORY_ID) B
		              ON A.CATEGORY_ID = B.CATEGORY_ID
		      WHERE FIND_IN_SET(A.CATEGORY_ID, getCateChildList(#{categoryId,jdbcType=INTEGER}))
		      	<if test="companyId!=null">
					AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
				</if>
			</foreach>
		   	) P
<!-- 		<choose>
			<when test="sortField!=null and sortField!='' and order!=null and order!=''">
				ORDER BY ${sortField} ${order}
			</when>
			<otherwise>
				ORDER BY P.CATEGORY_ID DESC
			</otherwise>
		</choose> -->
	</select>
	
	<!-- 查询产品分类信息列表(不分页) -->
	<select id="getCategoryList" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT A.*
		FROM T_CATEGORY A
		<where>
			STATUS = 1
			<if test="categoryId!=null and categoryId!=''">
				AND A.CATEGORY_ID like CONCAT('%',#{categoryId,jdbcType=INTEGER},'%' )
			</if>
			<if test="categoryName!=null and categoryName!=''">
				AND A.CATEGORY_NAME like CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="companyId != null">
				AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="parentId!=null"><!-- mybatis对于Integer类型，!=''即为!=0 -->
				AND A.PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
		</where>
		<choose>
			<when test="sortField!=null and sortField!='' and order!=null and order!=''">
				ORDER BY ${sortField} ${order}
			</when>
			<otherwise>
				ORDER BY A.CATEGORY_ID DESC
			</otherwise>
		</choose>
	</select>
	<!-- 根据主键查询产品信息 -->
	<select id="getCategoryById" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="com.vedeng.firstengage.model.FirstengageCategory">
		select
			*
		from T_CATEGORY a
		<where>
			a.CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
			<if test="companyId!=null">
				AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
		</where>
	</select>
	<!-- 用于获取 商品运营分类 -->
	<select id="getAllCategoryNameById" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.String">
		SELECT
			GROUP_CONCAT(c.name_level) AS C_NAMES
		FROM
		(
			SELECT
				GROUP_CONCAT(
					a.CATEGORY_NAME,
					';',
					a.`LEVEL`
				) AS name_level
			FROM
				T_CATEGORY a
			WHERE
				1 = 1
			AND FIND_IN_SET(
				a.CATEGORY_ID,
				(
					SELECT
						b.TREENODES
					FROM
						T_CATEGORY b
					<where>
			 			b.CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
						<if test="companyId!=null">
							AND b.COMPANY_ID = #{companyId,jdbcType=INTEGER}
						</if>
					</where>
				)
			)
		) c
		
	</select>
	
	<update id="updateCategoryTreenodes" parameterType="com.vedeng.firstengage.model.FirstengageCategory">
		UPDATE T_CATEGORY A 
			SET A.TREENODES = #{treenodes,jdbcType=VARCHAR}
			<if test="modTime != null">
				,MOD_TIME = #{modTime,jdbcType=BIGINT}
			</if>
			<if test="updater != null">
				,UPDATER = #{updater,jdbcType=INTEGER}
			</if>
		WHERE A.CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
		<if test="companyId!=null">
			AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
	</update>
	
	<update id="editCategoryTreenodes"  parameterType="com.vedeng.firstengage.model.FirstengageCategory">
		UPDATE T_CATEGORY A
		SET A.TREENODES =
		       IFNULL(CONCAT((SELECT C.TREENODES
		               FROM (SELECT B.TREENODES AS TREENODES
		                     FROM T_CATEGORY B
		                     WHERE B.CATEGORY_ID = #{parentId,jdbcType=INTEGER} 
		                     <if test="companyId!=null">
					            AND B.COMPANY_ID = #{companyId,jdbcType=INTEGER}
							</if>
							) C),',',
		              #{categoryId,jdbcType=INTEGER}),#{categoryId,jdbcType=INTEGER})
		WHERE A.CATEGORY_ID = #{categoryId,jdbcType=INTEGER}  
			<if test="companyId!=null">
				AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
	</update>
		
	<!-- 修改产品分类 -->
	<update id="updateGoodsCategory" parameterType="com.vedeng.firstengage.model.FirstengageCategory">
		UPDATE T_GOODS SET CATEGORY_ID = #{newCategoryId,jdbcType=INTEGER}
			<if test="modTime != null">
				,MOD_TIME = #{modTime,jdbcType=BIGINT}
			</if>
			<if test="updater != null">
				,UPDATER = #{updater,jdbcType=INTEGER}
			</if>
		 WHERE CATEGORY_ID = #{oldCategoryId,jdbcType=INTEGER}
		 <if test="companyId!=null">
			AND COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
	</update>
	
	<!-- 插入分类信息 -->
	<insert id="addCategory" parameterType="com.vedeng.firstengage.model.FirstengageCategory" useGeneratedKeys="true" keyProperty="categoryId">
		insert into T_CATEGORY
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="categoryId != null">
				CATEGORY_ID,
			</if>
			<if test="companyId != null">
				COMPANY_ID,
			</if>
			<if test="parentId != null">
				PARENT_ID,
			</if>
			<if test="categoryName != null">
				CATEGORY_NAME,
			</if>
			<if test="treenodes != null">
				TREENODES,
			</if>
			<if test="status != null">
				STATUS,
			</if>
			<if test="level != null">
				LEVEL,
			</if>
			<if test="sort != null">
				SORT,
			</if>
			<if test="iconDomain != null">
				ICON_DOMAIN,
			</if>
			<if test="iconUri != null">
				ICON_URI,
			</if>
			<if test="description != null">
				DESCRIPTION,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="categoryId != null">
				#{categoryId,jdbcType=INTEGER},
			</if>
			<if test="companyId != null">
				#{companyId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				#{parentId,jdbcType=INTEGER},
			</if>
			<if test="categoryName != null">
				#{categoryName,jdbcType=VARCHAR},
			</if>
			<if test="treenodes != null">
				#{treenodes,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				#{status,jdbcType=BIT},
			</if>
			<if test="level != null">
				#{level,jdbcType=BIT},
			</if>
			<if test="sort != null">
				#{sort,jdbcType=INTEGER},
			</if>
			<if test="iconDomain != null">
				#{iconDomain,jdbcType=VARCHAR},
			</if>
			<if test="iconUri != null">
				#{iconUri,jdbcType=VARCHAR},
			</if>
			<if test="description != null">
				#{description,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
		</trim>
	</insert>
	<update id="editCategory" parameterType="com.vedeng.firstengage.model.FirstengageCategory">
		update T_CATEGORY
		<set>
			<if test="companyId != null">
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="parentId != null">
				PARENT_ID = #{parentId,jdbcType=INTEGER},
			</if>
			<if test="categoryName != null">
				CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
			</if>
			<if test="treenodes != null">
				TREENODES = #{treenodes,jdbcType=VARCHAR},
			</if>
			<if test="status != null">
				STATUS = #{status,jdbcType=BIT},
			</if>
			<if test="level != null">
				LEVEL = #{level,jdbcType=BIT},
			</if>
			<if test="sort != null">
				SORT = #{sort,jdbcType=INTEGER},
			</if>
			<if test="iconDomain != null">
				ICON_DOMAIN = #{iconDomain,jdbcType=VARCHAR},
			</if>
			<if test="iconUri != null">
				ICON_URI = #{iconUri,jdbcType=VARCHAR},
			</if>
			<if test="description != null">
				DESCRIPTION = #{description,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
		</set>
		where CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
	</update>
	<select id="vailCateBottom" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.Integer">
		SELECT CATEGORY_ID FROM T_CATEGORY WHERE PARENT_ID = #{parentId,jdbcType=INTEGER}
		<if test="companyId!=null">
			AND COMPANY_ID = #{companyId,jdbcType=INTEGER}
		</if>
	</select>
	
	<!-- 根据主键获取层级 -->
	<select id="vailCategoryLevel" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.Integer">
		SELECT A.LEVEL
	        FROM T_CATEGORY A
	        WHERE A.CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
	        	<if test="companyId != null">
					AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
				</if>
	</select>
	
	<!-- 获取父节点层级 -->
	<select id="vailParentLevel" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.Integer">
		SELECT A.LEVEL
	        FROM T_CATEGORY A
	        WHERE A.CATEGORY_ID = #{parentId,jdbcType=INTEGER}
	        	<if test="companyId != null">
					AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
				</if>
	</select>
	
	<!-- 获取新节点父节点，级别（每个节点只有一个父级） -->
	<select id="getParentLevel" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.Integer">
		SELECT B.LEVEL
		FROM T_CATEGORY B
		WHERE B.PARENT_ID = (SELECT A.PARENT_ID
		                     FROM T_CATEGORY A
		                     WHERE A.CATEGORY_ID = #{parentId,jdbcType=INTEGER}
		                     	<if test="companyId != null">
									AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
								</if>
		                     )
			<if test="companyId != null">
				AND B.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
		GROUP BY B.PARENT_ID
	</select>
	<!-- 验证同级目录下是否存在重名 -->
	<select id="vailCategoryName" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM T_CATEGORY A
		WHERE A.PARENT_ID = #{parentId,jdbcType=INTEGER} 
			AND A.CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR}
			<if test="categoryId!=null and categoryName!=''">
				AND A.CATEGORY_ID <![CDATA[ <> ]]> #{categoryId,jdbcType=INTEGER}
			</if>
			AND STATUS = 1
			<if test="companyId != null">
				AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
	</select>
	
	<!-- 删除分类信息 -->
	<update id="delCategory" parameterType="com.vedeng.firstengage.model.FirstengageCategory">
		UPDATE T_CATEGORY SET STATUS = 0
			<if test="modTime != null">
				,MOD_TIME = #{modTime,jdbcType=BIGINT}
			</if>
			<if test="updater != null">
				,UPDATER = #{updater,jdbcType=INTEGER}
			</if>
		<where> 
			CATEGORY_ID = #{categoryId,jdbcType=INTEGER} AND STATUS = 1 
			<if test="companyId != null">
				AND COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
		</where>
	</update>
	
	<update id="batchUpdateLevel" parameterType="java.util.List">
		<foreach item="mapData" index="index" collection="list" separator=";">
			<foreach collection="mapData" index="key" item="value" >
				UPDATE T_CATEGORY SET LEVEL = #{value} WHERE CATEGORY_ID = #{key}
			</foreach>
		</foreach>
	</update>
	
	<update id="batchUpdateTreenodes" parameterType="java.util.List">
		<foreach item="mapData" index="index" collection="tree_list" separator=";">
			<foreach collection="mapData" index="key" item="value" >
				UPDATE T_CATEGORY SET TREENODES = #{value} WHERE CATEGORY_ID = #{key}
			</foreach>
		</foreach>
	</update>
	
	<select id="vailCategoryToGoods" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM T_GOODS
		WHERE CATEGORY_ID = #{categoryId,jdbcType=INTEGER} AND IS_DISCARD = 0
			<if test="companyId != null">
				AND COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
	</select>
	<!-- 根据节点ID查询父级列表 -->
	<select id="getParentCateList" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT *
		FROM T_CATEGORY A
		WHERE     A.STATUS = 1
			<if test="categoryId!=null and categoryId!=''">
		      AND A.PARENT_ID = (SELECT B.PARENT_ID
		                         FROM T_CATEGORY B
		                         WHERE B.CATEGORY_ID = #{categoryId,jdbcType=INTEGER} AND B.STATUS = 1)
			</if>
			<if test="parentId!=null">
		      AND A.PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
			<if test="companyId != null">
				AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
		order by A.CATEGORY_ID DESC
	</select>
	
	<select id="vailGoodsBindCate" parameterType="java.util.List" resultType="int">
		SELECT COUNT(*) FROM T_GOODS WHERE CATEGORY_ID IN 
		<foreach item="cate" index="index" collection="list" separator="," open="(" close=")">
			#{cate,jdbcType=INTEGER}
		</foreach>
		and IS_DISCARD = 0
	</select>
	
	<resultMap type="java.util.Map" id="categoryGoodsResult">
		<result column="CATEGORY_ID" property="categoryId"/>
		<result column="NUM" property="num"/>
	</resultMap>
	<select id="getCategoryGoodsNum" resultType="java.util.Map" resultMap="categoryGoodsResult">
		SELECT A.CATEGORY_ID,COUNT(1) AS NUM
		FROM T_GOODS A
		WHERE A.IS_DISCARD = 0
		GROUP BY A.CATEGORY_ID
	</select>
	<select id="getCategoryIndexList" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT B.GOODS_NUM, A.*
		FROM T_CATEGORY A
		     LEFT JOIN (SELECT A.CATEGORY_ID, COUNT(1) AS GOODS_NUM
		                FROM T_GOODS A
		                WHERE A.IS_DISCARD = 0
		                	<if test="companyId != null">
								AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
							</if>
		                GROUP BY A.CATEGORY_ID) B
		        ON A.CATEGORY_ID = B.CATEGORY_ID
		<where>
			STATUS = 1
			<if test="categoryId!=null and categoryId!=''">
				AND A.CATEGORY_ID like CONCAT('%',#{categoryId,jdbcType=INTEGER},'%' )
			</if>
			<if test="categoryName!=null and categoryName!=''">
				AND A.CATEGORY_NAME like CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="companyId!=null">
				AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			<if test="parentId!=null"><!-- mybatis对于Integer类型，!=''即为!=0 -->
				AND A.PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
			<if test="categoryIds != null">
				and A.CATEGORY_ID in
				<foreach collection="categoryIds" item="categoryId" index="index" open="(" close=")" separator=",">
		            #{categoryId}
		        </foreach>
			</if>
		</where>
		<!-- <choose>
			<when test="sortField!=null and sortField!='' and order!=null and order!=''">
				ORDER BY ${sortField} ${order}
			</when>
			<otherwise>
				ORDER BY A.CATEGORY_ID DESC
			</otherwise>
		</choose> -->
		ORDER BY A.LEVEL DESC
	</select>
	
	<select id="getIndexCategoryList" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT C.GOODS_NUM, B.*
		FROM T_CATEGORY B
		     LEFT JOIN (SELECT A.CATEGORY_ID, COUNT(1) AS GOODS_NUM
		                FROM T_GOODS A
		                WHERE A.IS_DISCARD = 0
		                GROUP BY A.CATEGORY_ID) C
		        ON B.CATEGORY_ID = C.CATEGORY_ID
		WHERE FIND_IN_SET(B.CATEGORY_ID,
		                  (SELECT GROUP_CONCAT(A.TREENODES)
		                   FROM T_CATEGORY A
		                   <where>
								STATUS = 1
								<if test="categoryId!=null and categoryId!=''">
									AND A.CATEGORY_ID LIKE CONCAT('%',#{categoryId,jdbcType=INTEGER},'%' )
								</if>
								<if test="categoryName!=null and categoryName!=''">
									AND A.CATEGORY_NAME LIKE CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
								</if>
								<if test="companyId!=null">
									AND A.COMPANY_ID = #{companyId,jdbcType=INTEGER}
								</if>
								<if test="parentId!=null"><!-- mybatis对于Integer类型，!=''即为!=0 -->
									AND A.PARENT_ID = #{parentId,jdbcType=INTEGER}
								</if>
								<if test="categoryIds != null">
									AND A.CATEGORY_ID IN
									<foreach collection="categoryIds" item="categoryId" index="index" open="(" close=")" separator=",">
							            #{categoryId}
							        </foreach>
								</if>
							</where>
		                   )
						)
		<choose>
			<when test="sortField!=null and sortField!='' and order!=null and order!=''">
				ORDER BY ${sortField} ${order}
			</when>
			<otherwise>
				ORDER BY B.CATEGORY_ID DESC
			</otherwise>
		</choose>
	</select>
	
	<select id="getCategoryListByParam" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT 
			a.CATEGORY_ID,a.COMPANY_ID,a.PARENT_ID,a.CATEGORY_NAME,IFNULL(b.TREENODES,a.TREENODES) as TREENODES,a.STATUS,a.LEVEL,a.SORT,a.OLD_CATEGORY_ID
		FROM T_CATEGORY a
		LEFT JOIN T_CATEGORY b ON a.CATEGORY_ID = b.PARENT_ID
	    WHERE a.STATUS = 1 AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
	        AND a.LEVEL = #{level,jdbcType=BIT}
	        <if test="categoryId!=null and categoryId!=0">
				AND a.PARENT_ID = #{categoryId,jdbcType=INTEGER}
			</if>
		group by a.CATEGORY_ID		
	</select>
	
	<select id="getCategoryListByName" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT 
			a.CATEGORY_ID,a.COMPANY_ID,a.PARENT_ID,a.CATEGORY_NAME,a.TREENODES,a.STATUS,a.LEVEL,a.SORT,a.OLD_CATEGORY_ID
		FROM T_CATEGORY a
	    WHERE a.STATUS = 1 AND a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
	        AND a.CATEGORY_NAME LIKE CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )	
	</select>
	
	<select id="getOneAndTwoCategoryList" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT 
			a.CATEGORY_ID,a.COMPANY_ID,a.PARENT_ID,a.CATEGORY_NAME,a.TREENODES,a.STATUS,a.LEVEL,a.SORT,a.OLD_CATEGORY_ID
		FROM T_CATEGORY a
	    WHERE a.STATUS = 1 AND a.LEVEL in(1,2) and a.COMPANY_ID = #{companyId,jdbcType=INTEGER}	
	</select>
	
	<select id="getThreeCategoryIdListByTwo"  resultType="java.lang.Integer">
		SELECT 
			a.CATEGORY_ID
		FROM T_CATEGORY a
	    WHERE a.STATUS = 1  and FIND_IN_SET(#{categoryId,jdbcType=INTEGER},a.TREENODES)
	        	
	</select>
	
		<select id="getCategoryByTreenodes" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT  A.*
		FROM T_CATEGORY A
		<where>
			STATUS = 1
			<if test="level == 1 and categoryId!=null and categoryId!=''">
				AND A.TREENODES like CONCAT('',#{categoryId,jdbcType=INTEGER},',%' )
			</if>
			<if test="level == 2 and categoryId!=null and categoryId!=''">
				AND A.TREENODES like CONCAT('%,',#{categoryId,jdbcType=INTEGER},',%' )
			</if>
			<if test="level!=null">
				AND A.LEVEL > #{level,jdbcType=INTEGER}
			</if>
		</where>
		ORDER BY A.LEVEL DESC
		</select>

	<!-- 根据名称查询 商品运营分类，显示到最小分类  by Franlin at 2018/06/04 -->
	<select id="getCategoryListByCategoryName" parameterType="com.vedeng.firstengage.model.FirstengageCategory" resultMap="BaseResultMap">
		SELECT 
			T.*
		FROM(
			SELECT 
				DISTINCT IFNULL(g.CATEGORY_ID, IFNULL(t.CATEGORY_ID, c.CATEGORY_ID)) as CATEGORY_ID, 
				IFNULL(g.`LEVEL`, IFNULL(t.`LEVEL`, c.`LEVEL`)) as `LEVEL`,
				IFNULL(g.SORT, IFNULL(t.SORT, c.SORT)) as SORT,
				CONCAT(IFNULL(c.CATEGORY_NAME, ""), "->", IFNULL(t.CATEGORY_NAME, ""), "->", IFNULL(g.CATEGORY_NAME, "")) as CATEGORY_NAME, 
				CONCAT(IFNULL(c.PARENT_ID, ""), " ", IFNULL(t.PARENT_ID, ""), " ", IFNULL(g.PARENT_ID, "")) as PARENT_ID,
				IFNULL(g.COMPANY_ID, IFNULL(t.COMPANY_ID, c.COMPANY_ID)) as COMPANY_ID
			FROM T_CATEGORY c
				LEFT JOIN T_CATEGORY t ON t.PARENT_ID = c.CATEGORY_ID AND t.`STATUS` = 1
				LEFT JOIN T_CATEGORY g ON g.PARENT_ID = t.CATEGORY_ID AND g.`STATUS` = 1
			WHERE  c.`LEVEL` = 1 AND c.STATUS = 1) T
		WHERE  1 = 1 
			<if test="categoryName != null and categoryName !='' ">
				AND T.CATEGORY_NAME like CONCAT('%',#{categoryName, jdbcType=VARCHAR},'%' )
			</if>
			<if test="categoryId != null and categoryId !='' ">
				AND T.CATEGORY_ID = #{categoryId, jdbcType=INTEGER}
			</if>
			<if test="level != null and level !='' ">
				AND T.LEVEL = #{level, jdbcType=INTEGER}
			</if>
			<if test="companyId != null">
				AND T.COMPANY_ID = #{companyId, jdbcType=INTEGER}
			</if>
		order by T.PARENT_ID, T.SORT
	</select>
	
</mapper>