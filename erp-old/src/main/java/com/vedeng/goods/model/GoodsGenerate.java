package com.vedeng.goods.model;

import java.math.BigDecimal;

public class GoodsGenerate {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer goodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.COMPANY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer companyId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PARENT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer parentId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.CATEGORY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer categoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.BRAND_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer brandId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.IS_ON_SALE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Byte isOnSale;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.IS_DISCARD
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer isDiscard;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.SKU
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String sku;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String goodsName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.ALIAS_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String aliasName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.MODEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String model;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String materialCode;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer baseUnitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer changeNum;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer unitId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal grossWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal netWeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal goodsLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal goodsWidth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal goodsHeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal packageLength;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal packageWidth;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal packageHeight;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_TYPE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer goodsType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.GOODS_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer goodsLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.MANAGE_CATEGORY
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer manageCategory;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.MANAGE_CATEGORY_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer manageCategoryLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PURCHASE_REMIND
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String purchaseRemind;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.LICENSE_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String licenseNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.FIRST_ENGAGE_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer firstEngageId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.RECORD_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String recordNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.REGISTRATION_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String registrationNumber;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.BEGINTIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Long begintime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.ENDTIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Long endtime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.AUTHORIZATION_CERTIFICATE_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String authorizationCertificateUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.OTHER_QUALIFICATION_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String otherQualificationUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.COLOR_PAGE_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String colorPageUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.TECHNICAL_PARAMETER_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String technicalParameterUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.INSTRUCTIONS_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String instructionsUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.BIDDING_DATA_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String biddingDataUrl;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String packingList;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.TOS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String tos;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String taxCategoryNo;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.MANUFACTURER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String manufacturer;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PRODUCTION_LICENSE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String productionLicense;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.DISCARD_REASON
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String discardReason;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.DISCARD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Long discardTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String supplyModel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.STANDARD_CATEGORY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer standardCategoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.STANDARD_CATEGORY_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer standardCategoryLevel;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.SPEC
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String spec;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.PRODUCT_ADDRESS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String productAddress;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.STORAGE_REQUIREMENTS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer storageRequirements;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer source;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.IS_RECOMMED
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Byte isRecommed;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.REGISTER_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String registerName;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.HREF
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private String href;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal jxMarketPrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private BigDecimal jxSalePrice;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Long modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer updater;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.TO_SKU_FLAG
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Integer toSkuFlag;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_GOODS.IS_NO_REASON_RETURN
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    private Byte isNoReasonReturn;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_ID
     *
     * @return the value of T_GOODS.GOODS_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getGoodsId() {
        return goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_ID
     *
     * @param goodsId the value for T_GOODS.GOODS_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.COMPANY_ID
     *
     * @return the value of T_GOODS.COMPANY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.COMPANY_ID
     *
     * @param companyId the value for T_GOODS.COMPANY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PARENT_ID
     *
     * @return the value of T_GOODS.PARENT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getParentId() {
        return parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PARENT_ID
     *
     * @param parentId the value for T_GOODS.PARENT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.CATEGORY_ID
     *
     * @return the value of T_GOODS.CATEGORY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getCategoryId() {
        return categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.CATEGORY_ID
     *
     * @param categoryId the value for T_GOODS.CATEGORY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.BRAND_ID
     *
     * @return the value of T_GOODS.BRAND_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getBrandId() {
        return brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.BRAND_ID
     *
     * @param brandId the value for T_GOODS.BRAND_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.IS_ON_SALE
     *
     * @return the value of T_GOODS.IS_ON_SALE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Byte getIsOnSale() {
        return isOnSale;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.IS_ON_SALE
     *
     * @param isOnSale the value for T_GOODS.IS_ON_SALE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setIsOnSale(Byte isOnSale) {
        this.isOnSale = isOnSale;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.IS_DISCARD
     *
     * @return the value of T_GOODS.IS_DISCARD
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getIsDiscard() {
        return isDiscard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.IS_DISCARD
     *
     * @param isDiscard the value for T_GOODS.IS_DISCARD
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setIsDiscard(Integer isDiscard) {
        this.isDiscard = isDiscard;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.SKU
     *
     * @return the value of T_GOODS.SKU
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getSku() {
        return sku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.SKU
     *
     * @param sku the value for T_GOODS.SKU
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_NAME
     *
     * @return the value of T_GOODS.GOODS_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getGoodsName() {
        return goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_NAME
     *
     * @param goodsName the value for T_GOODS.GOODS_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.ALIAS_NAME
     *
     * @return the value of T_GOODS.ALIAS_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getAliasName() {
        return aliasName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.ALIAS_NAME
     *
     * @param aliasName the value for T_GOODS.ALIAS_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.MODEL
     *
     * @return the value of T_GOODS.MODEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getModel() {
        return model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.MODEL
     *
     * @param model the value for T_GOODS.MODEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setModel(String model) {
        this.model = model;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.MATERIAL_CODE
     *
     * @return the value of T_GOODS.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getMaterialCode() {
        return materialCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.MATERIAL_CODE
     *
     * @param materialCode the value for T_GOODS.MATERIAL_CODE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setMaterialCode(String materialCode) {
        this.materialCode = materialCode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.BASE_UNIT_ID
     *
     * @return the value of T_GOODS.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getBaseUnitId() {
        return baseUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.BASE_UNIT_ID
     *
     * @param baseUnitId the value for T_GOODS.BASE_UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setBaseUnitId(Integer baseUnitId) {
        this.baseUnitId = baseUnitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.CHANGE_NUM
     *
     * @return the value of T_GOODS.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getChangeNum() {
        return changeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.CHANGE_NUM
     *
     * @param changeNum the value for T_GOODS.CHANGE_NUM
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setChangeNum(Integer changeNum) {
        this.changeNum = changeNum;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.UNIT_ID
     *
     * @return the value of T_GOODS.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getUnitId() {
        return unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.UNIT_ID
     *
     * @param unitId the value for T_GOODS.UNIT_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GROSS_WEIGHT
     *
     * @return the value of T_GOODS.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getGrossWeight() {
        return grossWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GROSS_WEIGHT
     *
     * @param grossWeight the value for T_GOODS.GROSS_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGrossWeight(BigDecimal grossWeight) {
        this.grossWeight = grossWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.NET_WEIGHT
     *
     * @return the value of T_GOODS.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getNetWeight() {
        return netWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.NET_WEIGHT
     *
     * @param netWeight the value for T_GOODS.NET_WEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setNetWeight(BigDecimal netWeight) {
        this.netWeight = netWeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_LENGTH
     *
     * @return the value of T_GOODS.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getGoodsLength() {
        return goodsLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_LENGTH
     *
     * @param goodsLength the value for T_GOODS.GOODS_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsLength(BigDecimal goodsLength) {
        this.goodsLength = goodsLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_WIDTH
     *
     * @return the value of T_GOODS.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getGoodsWidth() {
        return goodsWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_WIDTH
     *
     * @param goodsWidth the value for T_GOODS.GOODS_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsWidth(BigDecimal goodsWidth) {
        this.goodsWidth = goodsWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_HEIGHT
     *
     * @return the value of T_GOODS.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getGoodsHeight() {
        return goodsHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_HEIGHT
     *
     * @param goodsHeight the value for T_GOODS.GOODS_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsHeight(BigDecimal goodsHeight) {
        this.goodsHeight = goodsHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PACKAGE_LENGTH
     *
     * @return the value of T_GOODS.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getPackageLength() {
        return packageLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PACKAGE_LENGTH
     *
     * @param packageLength the value for T_GOODS.PACKAGE_LENGTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setPackageLength(BigDecimal packageLength) {
        this.packageLength = packageLength;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PACKAGE_WIDTH
     *
     * @return the value of T_GOODS.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getPackageWidth() {
        return packageWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PACKAGE_WIDTH
     *
     * @param packageWidth the value for T_GOODS.PACKAGE_WIDTH
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setPackageWidth(BigDecimal packageWidth) {
        this.packageWidth = packageWidth;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PACKAGE_HEIGHT
     *
     * @return the value of T_GOODS.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getPackageHeight() {
        return packageHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PACKAGE_HEIGHT
     *
     * @param packageHeight the value for T_GOODS.PACKAGE_HEIGHT
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setPackageHeight(BigDecimal packageHeight) {
        this.packageHeight = packageHeight;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_TYPE
     *
     * @return the value of T_GOODS.GOODS_TYPE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getGoodsType() {
        return goodsType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_TYPE
     *
     * @param goodsType the value for T_GOODS.GOODS_TYPE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsType(Integer goodsType) {
        this.goodsType = goodsType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.GOODS_LEVEL
     *
     * @return the value of T_GOODS.GOODS_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getGoodsLevel() {
        return goodsLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.GOODS_LEVEL
     *
     * @param goodsLevel the value for T_GOODS.GOODS_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setGoodsLevel(Integer goodsLevel) {
        this.goodsLevel = goodsLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.MANAGE_CATEGORY
     *
     * @return the value of T_GOODS.MANAGE_CATEGORY
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getManageCategory() {
        return manageCategory;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.MANAGE_CATEGORY
     *
     * @param manageCategory the value for T_GOODS.MANAGE_CATEGORY
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setManageCategory(Integer manageCategory) {
        this.manageCategory = manageCategory;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.MANAGE_CATEGORY_LEVEL
     *
     * @return the value of T_GOODS.MANAGE_CATEGORY_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getManageCategoryLevel() {
        return manageCategoryLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.MANAGE_CATEGORY_LEVEL
     *
     * @param manageCategoryLevel the value for T_GOODS.MANAGE_CATEGORY_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setManageCategoryLevel(Integer manageCategoryLevel) {
        this.manageCategoryLevel = manageCategoryLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PURCHASE_REMIND
     *
     * @return the value of T_GOODS.PURCHASE_REMIND
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getPurchaseRemind() {
        return purchaseRemind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PURCHASE_REMIND
     *
     * @param purchaseRemind the value for T_GOODS.PURCHASE_REMIND
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setPurchaseRemind(String purchaseRemind) {
        this.purchaseRemind = purchaseRemind;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.LICENSE_NUMBER
     *
     * @return the value of T_GOODS.LICENSE_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getLicenseNumber() {
        return licenseNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.LICENSE_NUMBER
     *
     * @param licenseNumber the value for T_GOODS.LICENSE_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setLicenseNumber(String licenseNumber) {
        this.licenseNumber = licenseNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.FIRST_ENGAGE_ID
     *
     * @return the value of T_GOODS.FIRST_ENGAGE_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getFirstEngageId() {
        return firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.FIRST_ENGAGE_ID
     *
     * @param firstEngageId the value for T_GOODS.FIRST_ENGAGE_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setFirstEngageId(Integer firstEngageId) {
        this.firstEngageId = firstEngageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.RECORD_NUMBER
     *
     * @return the value of T_GOODS.RECORD_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getRecordNumber() {
        return recordNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.RECORD_NUMBER
     *
     * @param recordNumber the value for T_GOODS.RECORD_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setRecordNumber(String recordNumber) {
        this.recordNumber = recordNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.REGISTRATION_NUMBER
     *
     * @return the value of T_GOODS.REGISTRATION_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getRegistrationNumber() {
        return registrationNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.REGISTRATION_NUMBER
     *
     * @param registrationNumber the value for T_GOODS.REGISTRATION_NUMBER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.BEGINTIME
     *
     * @return the value of T_GOODS.BEGINTIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Long getBegintime() {
        return begintime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.BEGINTIME
     *
     * @param begintime the value for T_GOODS.BEGINTIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setBegintime(Long begintime) {
        this.begintime = begintime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.ENDTIME
     *
     * @return the value of T_GOODS.ENDTIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Long getEndtime() {
        return endtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.ENDTIME
     *
     * @param endtime the value for T_GOODS.ENDTIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setEndtime(Long endtime) {
        this.endtime = endtime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.AUTHORIZATION_CERTIFICATE_URL
     *
     * @return the value of T_GOODS.AUTHORIZATION_CERTIFICATE_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getAuthorizationCertificateUrl() {
        return authorizationCertificateUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.AUTHORIZATION_CERTIFICATE_URL
     *
     * @param authorizationCertificateUrl the value for T_GOODS.AUTHORIZATION_CERTIFICATE_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setAuthorizationCertificateUrl(String authorizationCertificateUrl) {
        this.authorizationCertificateUrl = authorizationCertificateUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.OTHER_QUALIFICATION_URL
     *
     * @return the value of T_GOODS.OTHER_QUALIFICATION_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getOtherQualificationUrl() {
        return otherQualificationUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.OTHER_QUALIFICATION_URL
     *
     * @param otherQualificationUrl the value for T_GOODS.OTHER_QUALIFICATION_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setOtherQualificationUrl(String otherQualificationUrl) {
        this.otherQualificationUrl = otherQualificationUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.COLOR_PAGE_URL
     *
     * @return the value of T_GOODS.COLOR_PAGE_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getColorPageUrl() {
        return colorPageUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.COLOR_PAGE_URL
     *
     * @param colorPageUrl the value for T_GOODS.COLOR_PAGE_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setColorPageUrl(String colorPageUrl) {
        this.colorPageUrl = colorPageUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.TECHNICAL_PARAMETER_URL
     *
     * @return the value of T_GOODS.TECHNICAL_PARAMETER_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getTechnicalParameterUrl() {
        return technicalParameterUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.TECHNICAL_PARAMETER_URL
     *
     * @param technicalParameterUrl the value for T_GOODS.TECHNICAL_PARAMETER_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setTechnicalParameterUrl(String technicalParameterUrl) {
        this.technicalParameterUrl = technicalParameterUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.INSTRUCTIONS_URL
     *
     * @return the value of T_GOODS.INSTRUCTIONS_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getInstructionsUrl() {
        return instructionsUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.INSTRUCTIONS_URL
     *
     * @param instructionsUrl the value for T_GOODS.INSTRUCTIONS_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setInstructionsUrl(String instructionsUrl) {
        this.instructionsUrl = instructionsUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.BIDDING_DATA_URL
     *
     * @return the value of T_GOODS.BIDDING_DATA_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getBiddingDataUrl() {
        return biddingDataUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.BIDDING_DATA_URL
     *
     * @param biddingDataUrl the value for T_GOODS.BIDDING_DATA_URL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setBiddingDataUrl(String biddingDataUrl) {
        this.biddingDataUrl = biddingDataUrl;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PACKING_LIST
     *
     * @return the value of T_GOODS.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getPackingList() {
        return packingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PACKING_LIST
     *
     * @param packingList the value for T_GOODS.PACKING_LIST
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setPackingList(String packingList) {
        this.packingList = packingList;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.TOS
     *
     * @return the value of T_GOODS.TOS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getTos() {
        return tos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.TOS
     *
     * @param tos the value for T_GOODS.TOS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setTos(String tos) {
        this.tos = tos;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.TAX_CATEGORY_NO
     *
     * @return the value of T_GOODS.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getTaxCategoryNo() {
        return taxCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.TAX_CATEGORY_NO
     *
     * @param taxCategoryNo the value for T_GOODS.TAX_CATEGORY_NO
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setTaxCategoryNo(String taxCategoryNo) {
        this.taxCategoryNo = taxCategoryNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.MANUFACTURER
     *
     * @return the value of T_GOODS.MANUFACTURER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getManufacturer() {
        return manufacturer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.MANUFACTURER
     *
     * @param manufacturer the value for T_GOODS.MANUFACTURER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PRODUCTION_LICENSE
     *
     * @return the value of T_GOODS.PRODUCTION_LICENSE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getProductionLicense() {
        return productionLicense;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PRODUCTION_LICENSE
     *
     * @param productionLicense the value for T_GOODS.PRODUCTION_LICENSE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setProductionLicense(String productionLicense) {
        this.productionLicense = productionLicense;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.DISCARD_REASON
     *
     * @return the value of T_GOODS.DISCARD_REASON
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getDiscardReason() {
        return discardReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.DISCARD_REASON
     *
     * @param discardReason the value for T_GOODS.DISCARD_REASON
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setDiscardReason(String discardReason) {
        this.discardReason = discardReason;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.DISCARD_TIME
     *
     * @return the value of T_GOODS.DISCARD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Long getDiscardTime() {
        return discardTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.DISCARD_TIME
     *
     * @param discardTime the value for T_GOODS.DISCARD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setDiscardTime(Long discardTime) {
        this.discardTime = discardTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.SUPPLY_MODEL
     *
     * @return the value of T_GOODS.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getSupplyModel() {
        return supplyModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.SUPPLY_MODEL
     *
     * @param supplyModel the value for T_GOODS.SUPPLY_MODEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setSupplyModel(String supplyModel) {
        this.supplyModel = supplyModel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.STANDARD_CATEGORY_ID
     *
     * @return the value of T_GOODS.STANDARD_CATEGORY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getStandardCategoryId() {
        return standardCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.STANDARD_CATEGORY_ID
     *
     * @param standardCategoryId the value for T_GOODS.STANDARD_CATEGORY_ID
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setStandardCategoryId(Integer standardCategoryId) {
        this.standardCategoryId = standardCategoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.STANDARD_CATEGORY_LEVEL
     *
     * @return the value of T_GOODS.STANDARD_CATEGORY_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getStandardCategoryLevel() {
        return standardCategoryLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.STANDARD_CATEGORY_LEVEL
     *
     * @param standardCategoryLevel the value for T_GOODS.STANDARD_CATEGORY_LEVEL
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setStandardCategoryLevel(Integer standardCategoryLevel) {
        this.standardCategoryLevel = standardCategoryLevel;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.SPEC
     *
     * @return the value of T_GOODS.SPEC
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getSpec() {
        return spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.SPEC
     *
     * @param spec the value for T_GOODS.SPEC
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setSpec(String spec) {
        this.spec = spec;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.PRODUCT_ADDRESS
     *
     * @return the value of T_GOODS.PRODUCT_ADDRESS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getProductAddress() {
        return productAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.PRODUCT_ADDRESS
     *
     * @param productAddress the value for T_GOODS.PRODUCT_ADDRESS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setProductAddress(String productAddress) {
        this.productAddress = productAddress;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.STORAGE_REQUIREMENTS
     *
     * @return the value of T_GOODS.STORAGE_REQUIREMENTS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getStorageRequirements() {
        return storageRequirements;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.STORAGE_REQUIREMENTS
     *
     * @param storageRequirements the value for T_GOODS.STORAGE_REQUIREMENTS
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setStorageRequirements(Integer storageRequirements) {
        this.storageRequirements = storageRequirements;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.SOURCE
     *
     * @return the value of T_GOODS.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getSource() {
        return source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.SOURCE
     *
     * @param source the value for T_GOODS.SOURCE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.IS_RECOMMED
     *
     * @return the value of T_GOODS.IS_RECOMMED
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Byte getIsRecommed() {
        return isRecommed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.IS_RECOMMED
     *
     * @param isRecommed the value for T_GOODS.IS_RECOMMED
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setIsRecommed(Byte isRecommed) {
        this.isRecommed = isRecommed;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.REGISTER_NAME
     *
     * @return the value of T_GOODS.REGISTER_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getRegisterName() {
        return registerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.REGISTER_NAME
     *
     * @param registerName the value for T_GOODS.REGISTER_NAME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.HREF
     *
     * @return the value of T_GOODS.HREF
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public String getHref() {
        return href;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.HREF
     *
     * @param href the value for T_GOODS.HREF
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setHref(String href) {
        this.href = href;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.JX_MARKET_PRICE
     *
     * @return the value of T_GOODS.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getJxMarketPrice() {
        return jxMarketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.JX_MARKET_PRICE
     *
     * @param jxMarketPrice the value for T_GOODS.JX_MARKET_PRICE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setJxMarketPrice(BigDecimal jxMarketPrice) {
        this.jxMarketPrice = jxMarketPrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.JX_SALE_PRICE
     *
     * @return the value of T_GOODS.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public BigDecimal getJxSalePrice() {
        return jxSalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.JX_SALE_PRICE
     *
     * @param jxSalePrice the value for T_GOODS.JX_SALE_PRICE
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setJxSalePrice(BigDecimal jxSalePrice) {
        this.jxSalePrice = jxSalePrice;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.ADD_TIME
     *
     * @return the value of T_GOODS.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.ADD_TIME
     *
     * @param addTime the value for T_GOODS.ADD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.CREATOR
     *
     * @return the value of T_GOODS.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.CREATOR
     *
     * @param creator the value for T_GOODS.CREATOR
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.MOD_TIME
     *
     * @return the value of T_GOODS.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.MOD_TIME
     *
     * @param modTime the value for T_GOODS.MOD_TIME
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.UPDATER
     *
     * @return the value of T_GOODS.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getUpdater() {
        return updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.UPDATER
     *
     * @param updater the value for T_GOODS.UPDATER
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.TO_SKU_FLAG
     *
     * @return the value of T_GOODS.TO_SKU_FLAG
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Integer getToSkuFlag() {
        return toSkuFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.TO_SKU_FLAG
     *
     * @param toSkuFlag the value for T_GOODS.TO_SKU_FLAG
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setToSkuFlag(Integer toSkuFlag) {
        this.toSkuFlag = toSkuFlag;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_GOODS.IS_NO_REASON_RETURN
     *
     * @return the value of T_GOODS.IS_NO_REASON_RETURN
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public Byte getIsNoReasonReturn() {
        return isNoReasonReturn;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_GOODS.IS_NO_REASON_RETURN
     *
     * @param isNoReasonReturn the value for T_GOODS.IS_NO_REASON_RETURN
     *
     * @mbggenerated Sat Aug 10 17:18:06 CST 2019
     */
    public void setIsNoReasonReturn(Byte isNoReasonReturn) {
        this.isNoReasonReturn = isNoReasonReturn;
    }
}