package com.vedeng.common.core.utils;

import com.itextpdf.text.Document;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfImportedPage;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.parser.LocationTextExtractionStrategy;
import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.itextpdf.text.pdf.parser.PdfTextExtractor;
import com.itextpdf.text.pdf.parser.TextExtractionStrategy;
import com.vedeng.common.core.exception.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;

/**
 * pdf工具类
 *
 * <AUTHOR>
 * @date 2022/3/28 11:07
 **/
@Slf4j
public class PdfUtil {

    /**
     * 去除pdf空白页
     * 判断页面内容是否为空或空串
     *
     * @param inputStream
     * @param outputStream
     */
    public static void removeBlankPdfPages(InputStream inputStream, OutputStream outputStream) {
        PdfReader reader = null;
        Document document = null;
        PdfCopy pdfCopy = null;
        try {
            reader = new PdfReader(inputStream);
            document = new Document(reader.getPageSizeWithRotation(1));

            pdfCopy = new PdfCopy(document, outputStream);
            PdfImportedPage page = null;
            document.open();
            for (int i = 1; i <= reader.getNumberOfPages(); i++) {
                String textFromPage = PdfTextExtractor.getTextFromPage(reader, i);

                if (StringUtils.hasText(textFromPage)) {
                    page = pdfCopy.getImportedPage(reader, i);
                    pdfCopy.addPage(page);
                }
            }
        } catch (Exception e) {
            log.error("去除pdf空白页异常", e);
            throw new ServiceException("去除pdf空白页异常");
        } finally {
            // 关闭所有
            if (document != null) {
                document.close();
            }
            if (pdfCopy != null) {
                pdfCopy.close();
            }
            if (reader != null) {
                reader.close();
            }
        }
    }


    /**
     * 判断pdf内容是否包含文字
     *
     * @param ssoUrl 链接
     * @param text   文字
     * @return
     */
    public static boolean whetherIncludeText(String ssoUrl, String text) {
        if (org.springframework.util.StringUtils.isEmpty(ssoUrl)) {
            log.error("无效的URL！");
            throw new ServiceException("无效的URL！");
        }

        HttpURLConnection urlConnection = null;
        InputStream inputStream = null;
        PdfReader pdfReader = null;
        try {
            URL url = new URL(ssoUrl);
            urlConnection = (HttpURLConnection) url.openConnection();
            urlConnection.setConnectTimeout(15000);
            urlConnection.setReadTimeout(60000);
            urlConnection.connect();

            inputStream = urlConnection.getInputStream();
            pdfReader = new PdfReader(inputStream);

            for (int i = 1; i <= pdfReader.getNumberOfPages(); i++) {
                PdfReaderContentParser parser = new PdfReaderContentParser(pdfReader);
                TextExtractionStrategy strategy = parser.processContent(i, new LocationTextExtractionStrategy());
                String info = strategy.getResultantText();
                if (info.contains(text)) {
                    return true;
                }
            }

        } catch (MalformedURLException e) {
            log.error("无效的URL: {}", ssoUrl, e);
            throw new ServiceException("无效的URL: " + ssoUrl, e);
        } catch (IOException e) {
            log.error("读取PDF文件失败: {}", ssoUrl, e);
            throw new ServiceException("读取PDF文件失败: " + ssoUrl, e);
        }  finally {
            try {
                if (pdfReader != null) {
                    pdfReader.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
                if (urlConnection != null) {
                    urlConnection.disconnect();
                }
            } catch (IOException e) {
                log.error("关闭连接失败", e);
            }
        }
        return false;
    }

}
