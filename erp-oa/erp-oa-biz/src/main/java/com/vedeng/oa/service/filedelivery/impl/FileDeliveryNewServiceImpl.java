package com.vedeng.oa.service.filedelivery.impl;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.CustomUtils;
import com.vedeng.oa.dao.FileDeliveryAddressMapper;
import com.vedeng.oa.dao.FileDeliveryConfigMapper;
import com.vedeng.oa.dao.FileDeliveryNewMapper;
import com.vedeng.oa.model.FileDeliveryAddress;
import com.vedeng.oa.model.FileDeliveryAddressExample;
import com.vedeng.oa.model.FileDeliveryConfig;
import com.vedeng.oa.model.FileDeliveryNew;
import com.vedeng.oa.model.FileDeliveryNewExample;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryAddressApiService;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryNewApiService;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryImportResultVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryUploadVO;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class FileDeliveryNewServiceImpl implements FileDeliveryNewApiService {
    @Resource
    FileDeliveryConfigMapper fileDeliveryConfigMapper;
    @Resource
    FileDeliveryNewMapper fileDeliveryNewMapper;
    @Resource
    FileDeliveryAddressMapper fileDeliveryAddressMapper;
    @Resource
    private FileDeliveryAddressApiService fileDeliveryAddressApiService;
    
    /**
     * excel 最大数据量 10W
     */
    private final Integer MAX_DATA_LENGTH = 100000;
    
    @Override
    public String getConfig() {
        FileDeliveryConfig config= fileDeliveryConfigMapper.selectByPrimaryKey(1);
         return config.getDeliConfig();
    }

    @Override
    public FileDeliveryVO getDeliveryById(Integer fileDeliveryId) {
        if(fileDeliveryId==null){
            return null;
        }
        com.vedeng.oa.model.FileDeliveryNew fileDeliveryNew=fileDeliveryNewMapper.selectByPrimaryKey(fileDeliveryId);
        if(fileDeliveryNew==null){
            return null;
        }
        FileDeliveryVO result=new FileDeliveryVO();
        BeanUtils.copyProperties(fileDeliveryNew,result);
        return result;
    }

	@Override
	public FileDeliveryVO saveFileDeliveryNew(FileDeliveryVO fileDeliveryVO) {
		FileDeliveryNew fileDeliveryNew = new FileDeliveryNew();
		fileDeliveryNew.setCreator(fileDeliveryVO.getCreator());
		fileDeliveryNew.setAddTime(new Date());
		fileDeliveryNew.setIsDeleted(fileDeliveryVO.getIsDeleted());
		fileDeliveryNewMapper.insertSelective(fileDeliveryNew);
		fileDeliveryVO.setFileDeliveryId(fileDeliveryNew.getFileDeliveryId()); 
		return fileDeliveryVO;
	}

	@Override
	public int updateFileDeliveryNew(FileDeliveryVO fileDeliveryVO,Integer userId, String applyOrgName) {
		FileDeliveryNew insertRecord = new FileDeliveryNew();
		Integer fileDeliveryId = fileDeliveryVO.getFileDeliveryId();
		insertRecord.setFileDeliveryId(fileDeliveryVO.getFileDeliveryId());
		insertRecord.setFileDeliveryNo(fileDeliveryVO.getFileDeliveryNo());
		insertRecord.setApplyUserId(userId);
		//申请人部门，一级部门/二级部门
		insertRecord.setApplyOrgName(applyOrgName);
		insertRecord.setApplyTime(new Date());
		insertRecord.setVerifyStatus(0);
		//TODO Neil 寄送状态0未寄送1已寄送 打印操作执行更新该字段
		insertRecord.setDeliveryStatus(0);
		//TODO Neil 寄送时间，打印面单/10楼打印功能,更新该字段
		insertRecord.setDeliveryTime(null);
		FileDeliveryAddressExample example = new FileDeliveryAddressExample();
		example.createCriteria().andFileDeliveryIdEqualTo(fileDeliveryId).andIsDeleteEqualTo(0);
		List<FileDeliveryAddress> fileDeliveryAddressList = fileDeliveryAddressMapper.selectByExample(example);
		if(CollectionUtils.isNotEmpty(fileDeliveryAddressList)) {
			List<String> traderNameList = fileDeliveryAddressList.stream().map(FileDeliveryAddress::getTraderName).collect(Collectors.toList());
			String traderNames = String.join(",", traderNameList);
			insertRecord.setTraderName(traderNames);
		}
		//TODO Neil 快递公司名称，打印面单/10楼打印功能,更新该字段
		insertRecord.setLogisticsName(null);
		//TODO Neil 快递单号，打印面单/10楼打印功能,更新该字段
		insertRecord.setExpressNos(null);
		//寄送文件类型
		insertRecord.setDeliveryType(fileDeliveryVO.getDeliveryType());
		//承运部门
		insertRecord.setDeliveryDept(fileDeliveryVO.getDeliveryDept());
		//寄件总重
		insertRecord.setDeliveryTotalWeight(fileDeliveryVO.getDeliveryTotalWeight());
		//寄送名称
		insertRecord.setDeliveryProdName(fileDeliveryVO.getDeliveryProdName());
		insertRecord.setDeliveryExpressNum(fileDeliveryVO.getDeliveryExpressNum());
		insertRecord.setContent(fileDeliveryVO.getContent());
		//是否关闭0否1是
		insertRecord.setIsClosed(0);
		insertRecord.setClosedComments(fileDeliveryVO.getClosedComments());
		insertRecord.setIsDeleted(0);
		//更新新增时间
		insertRecord.setAddTime(new Date());
		insertRecord.setCreator(userId);
		insertRecord.setModTime(new Date());
		insertRecord.setSendUserType(fileDeliveryVO.getSendUserType());
		return fileDeliveryNewMapper.updateByPrimaryKeyWithBLOBs(insertRecord);
	}
@Autowired
com.vedeng.erp.system.service.RegionApiService regionApiService;
	public List<FileDeliveryAddressVO> getDeliveryAdressList(Integer fileDeliveryId){
		FileDeliveryAddressExample fileDeliveryAddressExample=new FileDeliveryAddressExample();
		fileDeliveryAddressExample.createCriteria().andIsDeleteEqualTo(0)
				.andFileDeliveryIdEqualTo(fileDeliveryId);
		fileDeliveryAddressExample.setOrderByClause("   id asc ");
		List<FileDeliveryAddress> list= fileDeliveryAddressMapper.selectByExample(fileDeliveryAddressExample);
		List<FileDeliveryAddressVO> result=new ArrayList<>();
		list.forEach(item->{
			FileDeliveryAddressVO vo=new FileDeliveryAddressVO();
			BeanUtils.copyProperties(item,vo);
			vo.setTraderContactMobileHidden(CustomUtils.hideMiddleFourDigits(item.getTraderContactMobile()));
			//组装其他信息
			result.add(vo);
		});
		return result;
	}



	@Override
	public FileDeliveryVO addEmptyDelivery() {
		FileDeliveryNew fileDeliveryNew=new FileDeliveryNew();
		fileDeliveryNew.setIsDeleted(1);
		fileDeliveryNew.setCreator(1);
		fileDeliveryNew.setAddTime(new Date());
		fileDeliveryNewMapper.insert(fileDeliveryNew);
		FileDeliveryVO result=new FileDeliveryVO();
		BeanUtils.copyProperties(fileDeliveryNew,result);
		return result;
	}
	@Override
	public void setDeliveryDelived(Integer fileDeliveryId, Integer userId, Integer logisticsName) {
		FileDeliveryNew fileDeliveryNew = new FileDeliveryNew();
		fileDeliveryNew.setFileDeliveryId(fileDeliveryId);
		fileDeliveryNew.setUpdater(userId);
		fileDeliveryNew.setModTime(new Date());
		fileDeliveryNew.setDeliveryStatus(1);
		fileDeliveryNew.setDeliveryTime(new Date());
		fileDeliveryNew.setLogisticsName(logisticsName);
		fileDeliveryNewMapper.updateByPrimaryKeySelective(fileDeliveryNew);
	}

	@Override
	public void fillExpressNoAndLabelUrlById(FileDeliveryAddressVO update) {
		FileDeliveryAddress updateLabel=new FileDeliveryAddress();
		updateLabel.setId(update.getId());
		updateLabel.setModTime(new Date());
		updateLabel.setUpdater(update.getUpdater());
		updateLabel.setExpressLabelurl(update.getExpressLabelurl());
		updateLabel.setLogisticsNo(update.getLogisticsNo());
		updateLabel.setDeliveryStatus(update.getDeliveryStatus());
		fileDeliveryAddressMapper.updateByPrimaryKeySelective(updateLabel);
	}

	@Override
	public void updateFileDeliveryVerifyStatus(Integer verifyStatus, Integer userId,Integer fileDeliveryId) {
		FileDeliveryNew fileDeliveryNew = new FileDeliveryNew();
		fileDeliveryNew.setFileDeliveryId(fileDeliveryId);
		fileDeliveryNew.setUpdater(userId);
		fileDeliveryNew.setModTime(new Date());
		fileDeliveryNew.setVerifyStatus(verifyStatus);
		updateFileDeliveryByParams(fileDeliveryNew);
	}

	private int updateFileDeliveryByParams(FileDeliveryNew fileDeliveryNew) {
		FileDeliveryNew record = new FileDeliveryNew();
		FileDeliveryNewExample example = new FileDeliveryNewExample();
		example.createCriteria().andFileDeliveryIdEqualTo(fileDeliveryNew.getFileDeliveryId());
		if(StringUtils.isNotEmpty(fileDeliveryNew.getApplyOrgName())) {
			record.setApplyOrgName(fileDeliveryNew.getApplyOrgName());
		}
		if(StringUtils.isNotEmpty(fileDeliveryNew.getClosedComments())) {
			record.setClosedComments(fileDeliveryNew.getClosedComments());
		}
		if(StringUtils.isNotEmpty(fileDeliveryNew.getContent())) {
			record.setContent(fileDeliveryNew.getContent());
		}
		if(StringUtils.isNotEmpty(fileDeliveryNew.getDeliveryProdName())) {
			record.setDeliveryProdName(fileDeliveryNew.getDeliveryProdName());
		}
		if(StringUtils.isNotEmpty(fileDeliveryNew.getExpressNos())) {
			record.setExpressNos(fileDeliveryNew.getExpressNos());
		}
		if(StringUtils.isNotEmpty(fileDeliveryNew.getTraderName())) {
			record.setTraderName(fileDeliveryNew.getTraderName());
		}
		if(StringUtils.isNotEmpty(fileDeliveryNew.getUpdateRemark())) {
			record.setUpdateRemark(fileDeliveryNew.getUpdateRemark());
		}
		if(Objects.nonNull(fileDeliveryNew.getApplyTime())) {
			record.setApplyTime(fileDeliveryNew.getApplyTime());
		}
		if(Objects.nonNull(fileDeliveryNew.getClosedComments())) {
			record.setClosedComments(fileDeliveryNew.getClosedComments());
		}
		if(Objects.nonNull(fileDeliveryNew.getDeliveryDept())) {
			record.setDeliveryDept(fileDeliveryNew.getDeliveryDept());
		}
		if(Objects.nonNull(fileDeliveryNew.getDeliveryExpressNum())) {
			record.setDeliveryExpressNum(fileDeliveryNew.getDeliveryExpressNum());
		}
		if(Objects.nonNull(fileDeliveryNew.getDeliveryStatus())) {
			record.setDeliveryStatus(fileDeliveryNew.getDeliveryStatus());
		}
		if(Objects.nonNull(fileDeliveryNew.getDeliveryTime())) {
			record.setDeliveryTime(fileDeliveryNew.getDeliveryTime());
		}
		if(Objects.nonNull(fileDeliveryNew.getDeliveryTotalWeight())) {
			record.setDeliveryTotalWeight(fileDeliveryNew.getDeliveryTotalWeight());
		}
		if(Objects.nonNull(fileDeliveryNew.getDeliveryType())) {
			record.setDeliveryType(fileDeliveryNew.getDeliveryType());
		}
		if(Objects.nonNull(fileDeliveryNew.getExpressNos())) {
			record.setExpressNos(fileDeliveryNew.getExpressNos());
		}
		if(Objects.nonNull(fileDeliveryNew.getIsClosed())) {
			record.setIsClosed(fileDeliveryNew.getIsClosed());
		}
		if(Objects.nonNull(fileDeliveryNew.getLogisticsName())) {
			record.setLogisticsName(fileDeliveryNew.getLogisticsName());
		}
		if(Objects.nonNull(fileDeliveryNew.getModTime())) {
			record.setModTime(fileDeliveryNew.getModTime());
		}
		if(Objects.nonNull(fileDeliveryNew.getUpdater())) {
			record.setUpdater(fileDeliveryNew.getUpdater());
		}
		if(Objects.nonNull(fileDeliveryNew.getUpdateRemark())) {
			record.setUpdateRemark(fileDeliveryNew.getUpdateRemark());
		}
		if(Objects.nonNull(fileDeliveryNew.getVerifyStatus())) {
			record.setVerifyStatus(fileDeliveryNew.getVerifyStatus());
		}
		return fileDeliveryNewMapper.updateByExampleSelective(record, example);
	}



	@Override
	public FileDeliveryVO getFileDeliveryNew(Integer fileDeliveryId) {
		FileDeliveryNew fileDeliveryNew = fileDeliveryNewMapper.selectByPrimaryKey(fileDeliveryId);
		FileDeliveryVO fileDeliveryVO = new FileDeliveryVO();
		fileDeliveryVO.setAddTime(fileDeliveryNew.getAddTime());
		fileDeliveryVO.setApplyOrgName(fileDeliveryNew.getApplyOrgName());
		fileDeliveryVO.setApplyTime(fileDeliveryNew.getApplyTime());
		fileDeliveryVO.setApplyUserId(fileDeliveryNew.getApplyUserId());
		fileDeliveryVO.setClosedComments(fileDeliveryNew.getClosedComments());
		fileDeliveryVO.setContent(fileDeliveryNew.getContent());
		fileDeliveryVO.setCreator(fileDeliveryNew.getCreator());
		fileDeliveryVO.setDeliveryType(fileDeliveryNew.getDeliveryType());
		fileDeliveryVO.setDeliveryDept(fileDeliveryNew.getDeliveryDept());
		fileDeliveryVO.setDeliveryExpressNum(fileDeliveryNew.getDeliveryExpressNum());
		fileDeliveryVO.setDeliveryProdName(fileDeliveryNew.getDeliveryProdName());
		fileDeliveryVO.setDeliveryStatus(fileDeliveryNew.getDeliveryStatus());
		fileDeliveryVO.setDeliveryTime(fileDeliveryNew.getDeliveryTime());
		fileDeliveryVO.setDeliveryTotalWeight(fileDeliveryNew.getDeliveryTotalWeight());
		fileDeliveryVO.setExpressNos(fileDeliveryNew.getExpressNos());
		fileDeliveryVO.setFileDeliveryId(fileDeliveryNew.getFileDeliveryId());
		fileDeliveryVO.setFileDeliveryNo(fileDeliveryNew.getFileDeliveryNo());
		fileDeliveryVO.setIsClosed(fileDeliveryNew.getIsClosed());
		fileDeliveryVO.setLogisticsName(fileDeliveryNew.getLogisticsName());
		fileDeliveryVO.setTraderName(fileDeliveryNew.getTraderName());
		fileDeliveryVO.setVerifyStatus(fileDeliveryNew.getVerifyStatus());
		fileDeliveryVO.setIsDeleted(fileDeliveryNew.getIsDeleted());
		fileDeliveryVO.setSendUserType(fileDeliveryNew.getSendUserType());
		return fileDeliveryVO;
	}

	@Override
	public void updateFileDeliveryColseStatus(int closeStatus,String closeComment, Integer userId, Integer fileDeliveryId) {
		FileDeliveryNew fileDeliveryNew = new FileDeliveryNew();
		fileDeliveryNew.setFileDeliveryId(fileDeliveryId);
		fileDeliveryNew.setUpdater(userId);
		fileDeliveryNew.setModTime(new Date());
		fileDeliveryNew.setIsClosed(closeStatus);
		fileDeliveryNew.setClosedComments(closeComment);
		updateFileDeliveryByParams(fileDeliveryNew);
		
	}

	@Override
	public void updateFileDeliveryDeliveryStatus(Integer fileDeliveryId,Integer deliveryStatus, Date deliveryTime, String expressNos,
			Integer userId,Integer logisticsType) {
		FileDeliveryNew fileDeliveryNew = new FileDeliveryNew();
		fileDeliveryNew.setFileDeliveryId(fileDeliveryId);
		fileDeliveryNew.setUpdater(userId);
		fileDeliveryNew.setModTime(new Date());
		fileDeliveryNew.setDeliveryStatus(deliveryStatus);
		fileDeliveryNew.setDeliveryTime(deliveryTime);
		fileDeliveryNew.setExpressNos(expressNos);
		fileDeliveryNew.setLogisticsName(logisticsType);
		updateFileDeliveryByParams(fileDeliveryNew);
	}

    @Override
    public void setFileDeliveryLoginsticsName(Integer fileDeliveryId, Integer printType) {
		FileDeliveryNew fileDeliveryNew = new FileDeliveryNew();
		fileDeliveryNew.setFileDeliveryId(fileDeliveryId);
		fileDeliveryNew.setModTime(new Date());
		fileDeliveryNew.setLogisticsName(printType);
		fileDeliveryNewMapper.updateByPrimaryKeySelective(fileDeliveryNew);
    }

	@Override
	public FileDeliveryUploadVO upload(MultipartFile file,Integer fileDeliveryId,Integer userId) throws IOException {
		FileDeliveryUploadVO fileDeliveryUploadVO = new FileDeliveryUploadVO();
		List<Integer> updateIdList = new ArrayList<>();
		List<FileDeliveryImportResultVO> fileDeliveryImportResultVOErrorResult = new ArrayList<>();
        AtomicInteger row = new AtomicInteger(1);
        EasyExcel.read(file.getInputStream(), FileDeliveryImportResultVO.class, new FileDeliveryExcelReadListener<FileDeliveryImportResultVO>(list -> {
            for (FileDeliveryImportResultVO data : list) {
                row.getAndIncrement();
                String prefix = "第"+(row.get())+"行";
                if (row.get() > MAX_DATA_LENGTH) {
                    throw new ServiceException(StrUtil.format("收件单位上传，数据量超过10W!"));
                }
                StringBuilder errorSb = new StringBuilder();
                if (StrUtil.isBlank(data.getTraderName()) || StrUtil.isBlank(data.getTraderName())) {
                    errorSb.append("【收件单位】数据不可为空  ");
                }else {
                	if (data.getTraderName().length()>=255) {
                		errorSb.append("【收件单位】数据过长，请小于255个字符  ");
                	}
                }
                if (StrUtil.isBlank(data.getTraderContractName()) || StrUtil.isBlank(data.getTraderContractName())) {
                	errorSb.append("【联系人】数据不可为空");
                }else {
                	if (data.getTraderContractName().length() >= 255) {
                		errorSb.append("【联系人】数据过长，请小于255个字符 ");
                	}
                }
                if (StrUtil.isBlank(data.getMobile()) || StrUtil.isBlank(data.getMobile())) {
                	errorSb.append("【联系电话】数据不可为空");
                }else {
                	if (data.getMobile().length() >= 50) {
                    	errorSb.append("【联系电话】数据过长，请小于50个字符 ");
                    }
                	if (!StringUtils.isNumeric(data.getMobile())) {
                		errorSb.append("【联系电话】数据异常，请输入纯数字且中间不要有任何空格 ");
                	}
                }
                if (StrUtil.isBlank(data.getAreaId()) || StrUtil.isBlank(data.getAreaId())) {
                	errorSb.append("【区代码】数据不可为空");
                }else {
                	if (data.getAreaId().length() >= 11) {
                		errorSb.append("【区代码】数据过长，请小于11个字符 ");
                	}
                	if (!StringUtils.isNumeric(data.getAreaId())) {
                		errorSb.append("【区代码】数据异常，请输入纯数字且中间不要有任何空格 ");
                	}
                }
                if (StrUtil.isBlank(data.getTraderAddressInfo()) || StrUtil.isBlank(data.getTraderAddressInfo())) {
                	errorSb.append("【联系地址】数据不可为空");
                }else {
                	if (data.getTraderAddressInfo().length() >= 255) {
                    	errorSb.append("【联系地址】数据过长，请小于255个字符 ");
                    }
                }
                if(StringUtils.isNotEmpty(errorSb.toString())) {
                	data.setErrorMessage(prefix + errorSb.toString());
                	fileDeliveryImportResultVOErrorResult.add(data);
                	continue;
                }
                
                FileDeliveryAddressVO vo = new FileDeliveryAddressVO();
                vo.setFileDeliveryId(fileDeliveryId);
                vo.setTraderType(3);
                vo.setTraderName(data.getTraderName());
                vo.setContactType(2);
                vo.setTraderContactName(data.getTraderContractName());
                vo.setTraderContactMobile(data.getMobile());
                vo.setTraderContactAddressInfo(data.getTraderAddressInfo());
                vo.setAreaId(Integer.parseInt(data.getAreaId()));
                fileDeliveryAddressApiService.saveFileDeliveryAddress(vo,userId);
                updateIdList.add(vo.getId());
            }
        })).sheet().doRead();
        if (CollUtil.isNotEmpty(updateIdList)) {
            log.info("客户账户上传，更新客户账户状态，更新数量:{},更新id:{}", updateIdList.size(), JSON.toJSONString(updateIdList));
        }
        //返回导入成功数据和导入失败数据
        List<FileDeliveryAddressVO>  list = null;
        if(CollectionUtils.isNotEmpty(updateIdList)) {
        	list =  fileDeliveryAddressApiService.getFileDeliveryAddressByIds(updateIdList);
        }
        fileDeliveryUploadVO.setFileDeliveryAddressVOList(list);
        fileDeliveryUploadVO.setFileDeliveryImportResultVOErrorResult(fileDeliveryImportResultVOErrorResult);
        return fileDeliveryUploadVO;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateLogisticNos(Integer addressId, String logisticNos, Integer userId) {
		FileDeliveryAddress fileDeliveryAddress = new FileDeliveryAddress();
		fileDeliveryAddress.setModTime(new Date());
		fileDeliveryAddress.setUpdater(userId);
		fileDeliveryAddress.setLogisticsNoEdit(logisticNos);
		fileDeliveryAddress.setId(addressId);
		fileDeliveryAddressMapper.updateByPrimaryKeySelective(fileDeliveryAddress);
	}

	
}
