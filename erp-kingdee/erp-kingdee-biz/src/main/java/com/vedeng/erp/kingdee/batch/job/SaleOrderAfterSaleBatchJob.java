package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVirtualWarehouseLogDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.tasklet.PushInvoiceVoucherTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.SaleBackOutInBindTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.SaleOrderBlueInvoiceWarehouseOutTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.SaleOrderInvoiceWarehouseOutInTasklet;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.KingDeeRedInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleReturnStockDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售单售后退货退票 job
 * @date 2023/1/6 10:30
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderAfterSaleBatchJob extends BaseJob {


    @Autowired
    private BatchSaleReturnStockProcessor batchSaleReturnStockProcessor;
    @Autowired
    private BatchSaleReturnStockWriter batchSaleReturnStockWriter;

    @Autowired
    private BatchSaleReturnInAcceptanceFormProcessor batchSaleReturnInAcceptanceFormProcessor;

    @Autowired
    private BatchSaleOrderAfterSaleReceivableProcessor batchSaleOrderAfterSaleReceivableProcessor;
    @Autowired
    private BatchSaleOrderAfterSaleReceivableWriter batchSaleOrderAfterSaleReceivableWriter;
    @Autowired
    private BatchSaleOrderAfterSaleInvoiceProcessor batchSaleOrderAfterSaleInvoiceProcessor;
    @Autowired
    private BatchSaleOrderAfterSaleInvoiceWriter batchSaleOrderAfterSaleInvoiceWriter;
    @Autowired
    private PushInvoiceVoucherTasklet pushInvoiceVoucherTasklet;
    @Autowired
    private SaleBackOutInBindTasklet saleBackOutInBindTasklet;
    @Autowired
    private BatchVirtualWarehouseLogProcessor batchVirtualWarehouseLogProcessorService;
    @Autowired
    private BatchVirtualWarehouseLogWriter batchVirtualWarehouseLogWriterService;

    @Autowired
    private BaseProcessListener baseProcessListener;
    @Autowired
    private BaseReadListener baseReadListener;
    @Autowired
    private BaseWriteListener baseWriteListener;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;
    @Autowired
    private BatchHistorySaleRedInvoiceCompositeProcessor batchHistorySaleRedInvoiceCompositeProcessor;


    @Value("${findGiftAndHaveBlueInvoiceTime}")
    private Long findGiftAndHaveBlueInvoiceTime;

    @Autowired
    private BatchGiftSaleOrderAfterSaleReceiveProcessor batchGiftSaleOrderAfterSaleReceiveProcessor;

    @Autowired
    private SaleOrderBlueInvoiceWarehouseOutTasklet saleOrderBlueInvoiceWarehouseOutTasklet;
    @Autowired
    private SaleOrderInvoiceWarehouseOutInTasklet saleOrderInvoiceWarehouseOutInTasklet;

    public Job saleOrderAfterSaleFlowJob() {
        return jobBuilderFactory.get("saleOrderAfterSaleFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(invoiceVirture())
                .next(saleBackOutInBind())
                .next(bindBlueInvoiceAndWarehouseInRelation())
                .next(bindRedInvoiceAndWarehouseInRelation())
                .next(saleBack())
                .next(acceptanceForm())
                .next(negativeAccountReceivable())
                .next(negativeRedInvoice())
                .next(saveInvoiceVoucher())
                .build();
    }

    public Job saleGiftPayAfterSale() {
        return jobBuilderFactory.get("销售赠品退货入库应付单推送")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(giftPayAfterSale())
                .build();
    }


    public Job saleOrderAfterSaleOnlyBackJob() {
        return jobBuilderFactory.get("销售退货入库推送")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(saleBackOutInBind())
                .build();
    }


    /**
     * 销售退货出入库单绑定
     */
    private Step saleBackOutInBind() {
        return stepBuilderFactory.get("销售退货出入库单绑定")
                .tasklet(saleBackOutInBindTasklet)
                .build();
    }

    /**
     * 销售退货红票和入库单关系绑定
     */
    private Step bindRedInvoiceAndWarehouseInRelation() {
        return stepBuilderFactory.get("销售退货红票和入库单关系绑定")
                .tasklet(saleOrderInvoiceWarehouseOutInTasklet)
                .build();
    }

    /**
     * 销售蓝票和出库单关系绑定
     */
    private Step bindBlueInvoiceAndWarehouseInRelation() {
        return stepBuilderFactory.get("销售蓝票和出库单关系绑定")
                .tasklet(saleOrderBlueInvoiceWarehouseOutTasklet)
                .build();
    }

    /**
     * 销售单售后退货入库
     */
    private Step saleBack() {
        return stepBuilderFactory.get("销售售后退货入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeSaleReturnStockDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchWarehouseGoodsOutInDtoSaleItemReader(null, null))
                .processor(batchSaleReturnStockProcessor)
                .writer(batchSaleReturnStockWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 退货入库验收报告附件
     */
    private Step acceptanceForm() {
        return stepBuilderFactory.get("退货入库验收报告附件")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchWarehouseGoodsOutInDtoSaleItemReader(null, null))
                .processor(batchSaleReturnInAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 赠品应付单推送
     * @return
     */
    private Step giftPayAfterSale() {
        return stepBuilderFactory.get("赠品退货应付单推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeReceiveCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(giftPayItemAfterSaleReader())
                .processor(batchGiftSaleOrderAfterSaleReceiveProcessor)
                .writer(batchSaleOrderAfterSaleReceivableWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 负数应收单
     */
    private Step negativeAccountReceivable() {
        return stepBuilderFactory.get("销售售后应收单")
                .<BatchInvoiceDto, KingDeeReceiveCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSaleOrderBackRedInvoiceDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryNegativeReceivableProcessor())
                .writer(batchSaleOrderAfterSaleReceivableWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 负数红票
     */
    private Step negativeRedInvoice() {
        return stepBuilderFactory.get("销售红票")
                .<BatchInvoiceDto, KingDeeRedInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchSaleOrderBackRedInvoiceDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryNegativeRedInvoiceProcessor())
                .writer(batchSaleOrderAfterSaleInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 赠品售后
     * @return CommonMybatisItemReader<BatchWarehouseGoodsOutInDto>
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> giftPayItemAfterSaleReader() {
        BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .findGiftAndHaveBlueInvoiceTime(findGiftAndHaveBlueInvoiceTime)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "findSaleOrderGiftGoodsAfterSaleAndHaveInvoice", batchWarehouseGoodsOutInDto);
    }

    /**
     * 21-22历史红票Excel处理及生成负数标准应收单
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveCommonDto> compositeItemExcelHistoryNegativeReceivableProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveCommonDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleRedInvoiceCompositeProcessor, batchSaleOrderAfterSaleReceivableProcessor));
        return compositeItemProcessor;
    }

    /**
     * 21-22历史红票Excel处理及生成负数红票
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeRedInvoiceDto> compositeItemExcelHistoryNegativeRedInvoiceProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeRedInvoiceDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleRedInvoiceCompositeProcessor, batchSaleOrderAfterSaleInvoiceProcessor));
        return compositeItemProcessor;
    }

    /**
     * 发票存入金蝶凭证信息表
     */
    private Step saveInvoiceVoucher() {
        return stepBuilderFactory.get("销售发票存入金蝶凭证信息表")
                .tasklet(pushInvoiceVoucherTasklet)
                .build();
    }

    private Step invoiceVirture() {
        return stepBuilderFactory.get("红票生成虚拟出入库单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchVirtualWarehouseLogDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(InvoiceVirtureReader(null, null))
                .processor(batchVirtualWarehouseLogProcessorService)
                .writer(batchVirtualWarehouseLogWriterService)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> InvoiceVirtureReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto invoiceDto = BatchInvoiceDto
                .builder()
                .colorType(ErpConstant.ONE)
                .isEnable(ErpConstant.ONE)
                .tag(ErpConstant.ONE)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "queryAllNeedVirturalInvoice", invoiceDto);
    }

    /**
     * 入库
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchWarehouseGoodsOutInDtoSaleItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                //5销售退货入库
                .outInTypeList(CollUtil.newArrayList(5))
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "saleOrderInFindByAll", warehouseGoodsOutInDto);
    }


    /**
     * 退票
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchSaleOrderBackRedInvoiceDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "saleorderRedInvoiceFindByAll", batchInvoiceDto);
    }
}

