<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.SkuTerminalLevelMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.SkuTerminalLevel">
    <!--@mbg.generated-->
    <!--@Table T_SKU_TERMINAL_LEVEL-->
    <id column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="TERMINAL_LEVEL" jdbcType="VARCHAR" property="terminalLevel" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SKU_NO, TERMINAL_LEVEL
  </sql>

<!--auto generated by MybatisCodeHelper on 2022-02-16-->
  <select id="findByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SKU_TERMINAL_LEVEL
    <where>
      <if test="skuNo != null">
        and SKU_NO=#{skuNo,jdbcType=VARCHAR}
      </if>
      <if test="terminalLevel != null">
        and TERMINAL_LEVEL=#{terminalLevel,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

<!--auto generated by MybatisCodeHelper on 2022-02-16-->
  <select id="getOneByAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SKU_TERMINAL_LEVEL
    <where>
      <if test="skuNo != null">
        and SKU_NO=#{skuNo,jdbcType=VARCHAR}
      </if>
      <if test="terminalLevel != null">
        and TERMINAL_LEVEL=#{terminalLevel,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>