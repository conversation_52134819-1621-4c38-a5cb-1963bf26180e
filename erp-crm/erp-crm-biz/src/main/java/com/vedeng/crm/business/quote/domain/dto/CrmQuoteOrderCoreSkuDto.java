package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

@Data
public class CrmQuoteOrderCoreSkuDto extends CrmCoreSkuInfoDto {

    /**
     * 报价商品id
     */
    private Integer quoteorderGoodsId;

    /**
     * 最新备注
     */
    private String remark;

    /**
     * 添加时间
     */
    private String remarkAddTime;

    /**
     * 添加人ID
     */
    private Integer remarkUserId;

    /**
     * 添加人
     */
    private String remarkUserName;

    /**
     * 最新备注
     */
    private Integer remarkCount;

    /**
     * SKU的审核状态，取自V_CORE_SKU里的checkStatus字段
     * 审核状态 0待完善 1审核中 2审核不通过 3审核通过 4删除 5 待提交审核
     */
    private Integer checkStatus;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;

    /**
     * 直发备注
     */
    private String deliveryDirectComments;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 参考价格
     */
    private String referencePrice;

    /**
     * 参考周期
     */
    private String referenceDeliveryCycle;

    /**
     * 是否包安装 0否 1是
     */
    private Integer haveInstallation;

    /**
     * 产品备注
     */
    private String goodsComments;

    /**
     * 内部备注
     */
    private String insideComments;
}
