<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">
    <el-card class="box-card J-block">
        <div class="block" style="text-align: center;">
            <el-autocomplete
                    class="inline-input"
                    v-model="tName"
                    :fetch-suggestions="tNameSearch"
<%--                    hide-loading="false"--%>
                    select-when-unmatched="true"
                    highlight-first-item="true"
                    placeholder="请输入精准终端名称，关键字需大于等于4个字"
                    :trigger-on-focus="false"
                    @select="tNameHandleSelect"
                    style="width:30%"
                    suffix-icon="el-icon-search"
            ></el-autocomplete>
        </div>
        <el-radio-group style="margin-bottom: 20px;" v-model="tabType" @change="tabTypeChange" >
            <el-radio-button label='t'>终端</el-radio-button>
            <el-radio-button label='d'>终端合作渠道商</el-radio-button>
        </el-radio-group>
        <template v-if="tabType == 't'">
            <el-form :inline="true" :model="t" :rules="searchRules" ref="form">
                <el-form-item label="所在地区">
                    <templete>
                        <el-cascader
                                style="width:300px"
                                size="mini"
                                :options="areaOptions"
                                v-model="areaOptionsSelected"
                                :props="{'multiple': true}"
                                placeholder="请输入"
                                collapse-tags
                                clearable
                        ></el-cascader>
                    </templete>
                </el-form-item>

                <el-form-item label="终端大类">
                    <templete>
                        <el-cascader
                                size="mini"
                                ref="categoriesTypesRef"
                                :options="categoriesTypesSelect"
                                :props="{ value: 'label', label: 'value', multiple: true }"
                                collapse-tags
                                @change="categoriesTypesChange"
                                clearable>
                        </el-cascader>
                    </templete>
                </el-form-item>

                <el-form-item label="机构评级">
                    <el-select v-model="institutionLevel"
                               multiple
                               collapse-tags
                               placeholder="请选择"
                               size="mini">
                        <el-option
                                v-for="item in institutionLevelOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="机构性质">
                    <el-select v-model="institutionNature" placeholder="请选择" size="mini">
                        <el-option
                                v-for="item in institutionNatureOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="科室">
                    <el-input v-model="department" placeholder="模糊搜索" size="mini"></el-input>
                </el-form-item>

                <el-form-item label="覆盖情况">
                    <el-select v-model="coverStatus" placeholder="请选择" size="mini">
                        <el-option
                                v-for="item in coverOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="合作情况">
                    <el-select v-model="tCooperationStatus" placeholder="请选择" size="mini">
                        <el-option
                                v-for="item in cooperationOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>

            </el-form>

            <el-row style="margin-bottom: 20px; text-align: center">
                <el-button type="primary" :loading="loading" @click="submitForm">搜索</el-button>
                <el-button size="medium" type="primary" plain @click="reset">重置</el-button>
            </el-row>

            <div style="margin: 10px 0px 10px 0px;">
                <span style="font-size: 16px; font-weight: bold;">为你找到相关的终端 {{tTotalLines}} 个</span>
            </div>
<%--            <div style="margin: 10px 0px 10px 0px;display: inline-block;">--%>
<%--                <span>已选择</span>--%>
<%--                <span style="font-size: 15px; font-weight: bold; color: #409EFF">{{tSelectionCount}}</span>--%>
<%--                <span>个  最多选择100个拜访单位</span>--%>
<%--            </div>--%>
<%--            <div style="display: inline-block; float: right; height: 54px">--%>
<%--                <el-row style="margin-bottom: 40px; text-align: right">--%>
<%--                    <el-button type="primary" @click="distributionVisitPlan()">批量派发拜访计划</el-button>--%>
<%--                </el-row>--%>
<%--            </div>--%>

            <el-table
                    :data="tList"
                    v-loading="loading"
                    ref="tTable"
                    :empty-text="tEmptyText"
                    border
                    fit
                    style="width: 100%; "
                    tooltip-effect="dark"
                    @sort-change="sort_change"
<%--                    :default-sort = "{prop: 'biddingCount', order: 'descending'}"--%>
<%--                    @selection-change="tHandleSelectionChange"--%>
            >
<%--                <el-table-column--%>
<%--                        :selectable="tSelectableMethod"--%>
<%--                        type="selection"--%>
<%--                        align="center">--%>
<%--                </el-table-column>--%>

                <el-table-column
                        align="center"
                        label="终端名称"
                        align="center"
                        width="260"
                        text-align="center">
                    <template v-if="tabType == 't'" slot-scope="scope">
                        <el-link type="primary"
                                 :underline="false"
                                 v-if="scope.row.isBelongCurrentSales == 1"
                                 @click="viewCustomerPortraitInfo(scope.row.terminalTraderId)">{{ scope.row.terminalName }}</el-link>
                        <el-link type="primary"
                                 :underline="false"
                                 v-else
                                 @click="viewTerminalPortraitInfo(scope.row.terminalName)">{{ scope.row.terminalName }}</el-link>
                    </template>
                </el-table-column>

                <el-table-column
                        align="center"
                        text-align="center"
                        prop="coverStatus"
                        width="80"
                        label="覆盖情况">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="cooperationStatus"
                        width="80"
                        label="合作情况">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="belongArea"
                        width="200"
                        label="所在地区">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="terminalCategories"
                        width="110"
                        label="终端大类">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="institutionLevel"
                        width="200"
                        label="机构评级">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="institutionNature"
                        width="100"
                        label="机构性质">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        width="300"
                        label="科室">
                    <template v-if="tabType == 't'" v-slot="scope">
                        <div class="truncate" :title="scope.row.department">
                            {{ truncate(scope.row.department, 37) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        sortable="custom"
                        width="130"
                        prop="distributionLinkCount"
                        label="合作渠道商">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        sortable="custom"
                        prop="biddingCount"
                        label="招标次数">
                </el-table-column>
            </el-table>

            <el-pagination
                    @size-change="tHandleSizeChange"
                    @current-change="tHandleCurrentChange"
                    :current-page="tCurrentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="tCurrentSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="tTotalLines">
            </el-pagination>
        </template>
        <template v-if="tabType == 'd'">
            <el-form :inline="true" :model="d" :rules="searchRules" ref="form">
                <el-form-item label="时间" size="mini">
                    <el-select v-model="d.cooperationTime" @change="dHandleCooperationTimeChange">
                        <el-option label="近一年" value="1"></el-option>
                        <el-option label="近二年" value="2"></el-option>
                        <el-option label="近三年" value="3"></el-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="注册地区" size="mini">
                    <templete v-if="tabType == 'd'">
                        <el-cascader
                                :options="allAreaOptions"
                                v-model="allAreaOptionsSelected"
                                :props="{'multiple': true}"
                                placeholder="请输入"
                                collapse-tags
                                filterable
                                clearable
                        ></el-cascader>
                    </templete>
                </el-form-item>

                <el-form-item label="最新中标时间" size="mini">
                    <el-date-picker
                            v-model="d.lastBiddingTime"
                            type="daterange"
                            ref="timeBox"
                            align="right"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="setDateRange" >
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="和贝登最新交易时间" size="mini">
                    <el-date-picker
                            v-model="d.lastSaleTime"
                            type="daterange"
                            ref="timeBox"
                            align="right"
                            unlink-panels
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="setDateRange"
                    >
                    </el-date-picker>
                </el-form-item>

                <el-form-item label="合作情况">
                    <el-select v-model="dCooperationStatus" placeholder="请选择" size="mini">
                        <el-option
                                v-for="item in cooperationOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>

            <el-row style="margin-bottom: 20px; text-align: center">
                <el-button type="primary" :loading="loading" @click="submitForm">搜索</el-button>
                <el-button size="medium" type="primary" plain @click="reset">重置</el-button>
            </el-row>

            <div style="margin: 10px 0px 10px 0px;">
                <span style="font-size: 16px; font-weight: bold;">为你找到相关的渠道商 {{dTotalLines}}个</span>
            </div>
<%--            <div style="margin: 10px 0px 10px 0px;display: inline-block;">--%>
<%--                <span>已选择</span>--%>
<%--                <span style="font-size: 15px; font-weight: bold; color: #409EFF">{{dSelectionCount}}</span>--%>
<%--                <span>个  最多选择100个拜访单位</span>--%>
<%--            </div>--%>
<%--            <div style="display: inline-block; float: right; height: 54px">--%>
<%--                <el-row style="margin-bottom: 40px; text-align: right">--%>
<%--                    <el-button type="primary" @click="distributionVisitPlan()">批量派发拜访计划</el-button>--%>
<%--                </el-row>--%>
<%--            </div>--%>

            <el-table
                    :data="dList"
                    ref="dTable"
                    :empty-text="dEmptyText"
                    v-loading="loading"
                    border
                    fit
                    style="width: 100%; "
                    tooltip-effect="dark"
                    @sort-change="sort_change"
<%--                    :default-sort = "{prop: 'lastBiddingTime', order: 'descending'}"--%>
<%--                    @selection-change="dHandleSelectionChange"--%>

            >
<%--                <el-table-column--%>
<%--                        :selectable="dSelectableMethod"--%>
<%--                        type="selection"--%>
<%--                        align="center">--%>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="渠道商名称"
                        align="center"
                        width="300"
                        text-align="center">
                    <template v-if="tabType == 'd'" slot-scope="scope">
                        <el-tag type="warning" size="mini" v-if="scope.row.directSale == 1">直销</el-tag>
                        <el-link type="primary"
                                 :underline="false"
                                 v-if="scope.row.isBelongCurrentSales == 1"
                                 @click="viewCustomerPortraitInfo(scope.row.traderId)">{{ scope.row.distributionLinkName }}</el-link>
                        <span v-else>{{ scope.row.distributionLinkName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="saleUser"
                        width="150"
                        label="归属销售">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        sortable="custom"
                        width="150"
                        prop="cooperationStatus"
                        label="合作情况">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="registerArea"
                        width="200"
                        label="注册地区">
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        prop="cooperationTerminal"
                        width="400"
                        label="合作终端">
                    <template slot-scope="scope" v-if="tabType == 'd'" >
                        <div class="truncate" :title="scope.row.cooperationTerminal">
                            <span v-html="highlightCooperationTerminal(truncate(scope.row.cooperationTerminal,53))"></span>
                        </div>

                    </template>

                </el-table-column>
                <el-table-column
                        align="center"
                        sortable="custom"
                        prop="lastBiddingTime"
                        width="200"
                        label="最新中标时间">
                    <template slot-scope="scope" v-if="tabType == 'd'" >
                        <span>{{parseTime(scope.row.lastBiddingTime, '{y}-{m}-{d}')}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        text-align="center"
                        sortable="custom"
                        prop="lastSaleTime"
                        label="和贝登最新交易时间">
                    <template slot-scope="scope" v-if="tabType == 'd'">
                        <span>{{parseTime(scope.row.lastSaleTime, '{y}-{m}-{d}')}}</span>
                    </template>
                </el-table-column>
            </el-table>

            <el-pagination
                    @size-change="dHandleSizeChange"
                    @current-change="dHandleCurrentChange"
                    :current-page="dCurrentPageNo"
                    :page-sizes="[10, 20, 50, 100]"
                    :page-size="dCurrentSize"
                    layout="->,total, sizes, prev, pager, next, jumper"
                    :total="dTotalLines">
            </el-pagination>
        </template>

    </el-card>

    <el-dialog
            title="批量派发拜访计划"
            :visible.sync="distributionVisitPlanFlag"
            width="60%">
        <div class="aligned-item">
            <span style="color: red;">*</span>
            <span class="demonstration">计划拜访时间</span>
            <el-date-picker
                    class="aligned-component"
                    v-model="visitTime"
                    type="month"
                    placeholder="选择月"
                    format="yyyy-MM-dd"
                    value-format="yyyy-MM-dd"
            ></el-date-picker>
        </div>

        <div class="aligned-item">
            <span style="color: red;">*</span>
            <span class="demonstration">拜访目标</span>
            <el-select class="aligned-component" v-model="visitTarget" multiple placeholder="请选择">
                <el-option
                        v-for="item in visitTargetOptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                </el-option>
            </el-select>
        </div>

        <div class="aligned-item">
            <span style="color: red;">*</span>
            <span class="demonstration">指派拜访人</span>
            <span>系统根据销售负责区域，自动分配拜访人。若无法分配的请手动选择。</span>
        </div>

        <div class="aligned-item" style="width: 83%;">
            <span class="demonstration"></span>
            <el-table
                    :data="terminalDistributionVisitPlanList"
                    max-height="350"
                    border
                    size="small">
                <el-table-column
                        label="拜访目标"
                        width="500">
                    <template slot-scope="scope">
                        {{ scope.row.terminalName }}
                    </template>
                </el-table-column>
                <el-table-column
                        label="指定拜访人"
                        width="200">
                    <template slot-scope="scope">
                        <el-tag size="medium" v-if="scope.row.visitorName && scope.row.visitorId">{{ scope.row.visitorName }}</el-tag>
                        <el-select v-else v-model="scope.row.visitorId" placeholder="请选择" size="mini">
                            <el-option
                                    v-for="item in visitorList"
                                    :key="item.userId"
                                    :label="item.username"
                                    :value="item.userId">
                            </el-option>
                        </el-select>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <span slot="footer" class="dialog-footer" style="display: flex; justify-content: center;">
            <el-button size="medium" type="success" plain @click="submitVisitPlan" :disabled="submitDisabled">确认</el-button>
        </span>
    </el-dialog>

</div>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/businessChance.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    const traderId = '${traderId}';
    const customerNature = '${customerNature}';
    let vmjsp = new Vue({
        el: '#app',
        data() {
            let that = this;
            return {
                tEmptyText: "请输入筛选条件后再进行搜索",
                dEmptyText: "请输入筛选条件后再进行搜索",
                loading: false,
                // 存储所有选中项的数组
                tSelected: [],
                dSelected: [],
                tSelectedKeys: [], // 存储所有选中项的唯一标识符
                dSelectedKeys: [], // 存储所有选中项的唯一标识符
                tCurrentSize: 10,
                tCurrentPageNo: 1,
                dCurrentSize: 10,
                dCurrentPageNo: 1,
                tabType: 't',
                dTotalLines: 0,
                tTotalLines:0,
                // 终端大类
                categoriesTypesSelect:[],
                categoriesTypesValue:[],
                department:'',
                tName : '',
                tList:[],
                dList:[],
                d: {
                    cooperationTime: '3',
                    traderName: '',
                    lastBiddingTime: [],
                    lastSaleTime: []
                },
                t:{
                    traderName: '',
                    lastBiddingTime: [],
                    lastSaleTime: []
                },
                // 选中的行
                dMultipleSelection: [],
                dSelectionCount: 0,
                tSelectionCount: 0,
                // 排序字段，排序方式,默认按招标次数降序排列；
                sortColumn: 1,
                sortType: 1,

                searchRules: {
                    traderName: [
                        {max: 50, message: "最多输入50个字符", trigger: "blur"}]
                },

                setDateRange: {
                    disabledDate: this.disabledDate
                },

                props: {
                    multiple: true
                },
                options:[],
                areaOptions:[],
                areaOptionsSelected:[],
                allAreaOptions:[],
                allAreaOptionsSelected:[],

                institutionLevel:[],
                institutionLevelOptions: [
                    {value: 0, label: "一级医院"},
                    {value: 1, label: "二级医院"},
                    {value: 2, label: "三级医院"},
                    {value: 3, label: "未定级医院"},
                    {value: 4, label: "社区卫生服务中心（站）"},
                    {value: 5, label: "乡镇卫生院"},
                    {value: 6, label: "诊所（医务室）"},
                    {value: 7, label: "村卫生室"},
                    {value: 8, label: "应急三级医院"},
                    {value: 9, label: "应急二级医院"},
                    {value: 10, label: "应急基层医疗"}
                ],
                institutionNature:-1,
                institutionNatureOptions:[
                    {value: -1, label: "全部"},
                    {value: 0, label: "公立"},
                    {value: 1, label: "非公"}
                ],
                coverStatus:-1,
                coverOptions:[
                    {value: -1, label: "全部"},
                    {value: 1, label: "已覆盖"},
                    {value: 0, label: "未覆盖"}
                ],
                tCooperationStatus :-1,
                dCooperationStatus :-1,
                cooperationOptions:[
                    {value: -1, label: "全部"},
                    {value: 1, label: "已合作"},
                    {value: 0, label: "未合作"}
                ],
                distributionVisitPlanFlag: false,
                visitorList: [],
                terminalDistributionVisitPlanList: [],
                visitTime: '',
                visitTargetOptions: [{
                    value: 'C',
                    label: '老客客情维护'
                }, {
                    value: 'A',
                    label: '新客开发'
                }, {
                    value: 'B',
                    label: '商机跟进'
                }, {
                    value: 'D',
                    label: '签约会员'
                }, {
                    value: 'E',
                    label: '产品推广'
                }],
                visitTarget: '',
                submitDisabled: false
            }
        },

        mounted() {
            loadingApp();
        },

        created() {
            this.initData();
        },

        methods: {
            dSelectableMethod(row, index) {
                console.log("canSelectRow called for row:", row, "index:", index);
                return row.isBelongCurrentSales !== 1;
            },
            tSelectableMethod(row, index) {
                console.log("canSelectRow called for row:", row, "index:", index);
                return row.isBelongCurrentSales !== 1;
            },
            tNameHandleSelect(item) {
                if (this.tabType == 't'){
                    this.doSearch();
                }
                if (this.tabType == 'd'){
                    this.doSearch2();
                }
            },

            tNameSearch(queryString, callback) {
                if(queryString.length > 50){
                    var results = [];
                    callback(results);
                    this.$message({
                        showClose: false,
                        message: '关键字需小于等于50个字',
                        type: 'warning',
                        duration: 1000,
                        offset:70
                    });
                    return
                }

                queryTerminalData({"name": queryString,"traderId":'',"pageSize": 20,"pageNum": 1}).then(res => {
                    if (res.data.code != 0){
                        return
                    }
                    var results = res.data.data.list.map(item =>({
                        value: item.hosName
                    }));
                    callback(results);
                })
            },

            tabTypeChange(val){
                this.tabType = val;
                if (val == 't'){
                    // 渠道商默认按中标次数降序展示
                    this.sortColumn = 1;
                    this.sortType = 1;
                    if (this.tName != ''){
                        this.doSearch();
                    }
                }
                if (val == 'd' && this.sortColumn == 1){
                    // 渠道商默认按中标时间降序展示
                    this.sortColumn = 5;
                    this.sortType = 1;
                    if (this.tName != ''){
                        this.doSearch2();
                    }
                }
            },
            getTRowKey(row) {
                return row.terminalName;
            },
            getDRowKey(row) {
                return row.distributionLinkName;
            },
            // tHandleSelectionChange(selectedRows) {
            //     // 获取当页面所有行的键值
            //     const currentPageKeys = selectedRows.map(item => this.getTRowKey(item));
            //     const notCurrentPageKeys = this.tSelectedKeys.filter(key => !currentPageKeys.includes(key));
            //     if (currentPageKeys.length + notCurrentPageKeys.length > 100){
            //         this.$message({
            //             showClose: false,
            //             message: '最多选择100个拜访单位',
            //             type: 'error',
            //             duration: 1000,
            //             offset:70
            //         });
            //     }
            //
            //     // 移除当前页面所有行的键值
            //     this.tSelectedKeys = this.tSelectedKeys.filter(key => !currentPageKeys.includes(key));
            //
            //     this.tSelected = this.tSelected.filter(item => !currentPageKeys.includes(this.getTRowKey(item)));
            //     // 获取当前页面选中的行的键值
            //     const selectedKeysOnCurrentPage = selectedRows.map(item => this.getTRowKey(item));
            //     // 添加当前选中的行的键值
            //     this.tSelectedKeys.push(...selectedKeysOnCurrentPage);
            //     this.tSelected.push(...selectedRows);
            //     this.tSelectionCount = this.tSelectedKeys.length;
            // },
            // dHandleSelectionChange(selectedRows) {
            //     // 更新选中项数组
            //     this.dSelected = selectedRows;
            //     // 获取当页面所有行的键值
            //     const currentPageKeys = selectedRows.map(item => this.getDRowKey(item));
            //     // 移除当前页面所有行的键值
            //     this.dSelectedKeys = this.dSelectedKeys.filter(key => !currentPageKeys.includes(key));
            //     // 获取当前页面选中的行的键值
            //     const selectedKeysOnCurrentPage = selectedRows.map(item => this.getDRowKey(item));
            //     // 添加当前选中的行的键值
            //     this.dSelectedKeys.push(...selectedKeysOnCurrentPage);
            //
            //     this.dSelectionCount = this.dSelectedKeys.length;
            //
            //     this.dMultipleSelection = this.$refs.dTable.selection.map((item) => item.terminalName);
            //     this.tList.forEach(row => {
            //         row.selected = selectedRows.includes(row);
            //     });
            // },
            selectAll(){
                const selectAllChildren = (options) => {
                    return result = options.reduce((acc, level1) => {
                        const level1Value = level1.label;
                        const level2Array = level1.children.reduce((acc2, level2) => {
                            const level2Value = level2.label;
                            const level3Array = level2.children.map(level3 => {
                                const level3Value = level3.label;
                                // 将三个标签组合成一个数组
                                return [level1Value, level2Value, level3Value];
                            });
                            // 将第三级数组合并到第二级的累加器中
                            return acc2.concat(level3Array);
                        }, []);
                        // 将第二级数组合并到第一级的累加器中
                        return acc.concat(level2Array);
                    }, []);
                };
                this.areaOptionsSelected = selectAllChildren(this.areaOptions);
            },
            submitForm() {

                this.$refs.form.validate(valid => {
                    if(valid) {
                        if (this.tabType == 't'){
                            this.tCurrentPageNo = 1;
                            this.doSearch();
                        }else {
                            this.dCurrentPageNo = 1;
                            this.doSearch2();
                        }

                    } else {
                        return false;
                    }
                })
            },
            truncate(text, length) {
                if (text.length > length) {
                    return text.substring(0, length) + '...';
                }
                return text;
            },

            // 批量派发拜访计划按钮
            distributionVisitPlan() {
                debugger
                if (this.tabType == 't'){
                    // 勾选的数据
                    var pageParam = this.tSelected;
                    if (pageParam.length == 0){
                        this.$message({
                            showClose: false,
                            message: '请选择拜访目标',
                            type: 'warning',
                            duration: 1000,
                            offset:70
                        });
                        return
                    }
                    if (this.tSelectionCount > 100){
                        this.$message({
                            showClose: false,
                            message: '最多选择100个拜访单位',
                            type: 'error',
                            duration: 1000,
                            offset:70
                        });
                    }
                    getDistributionVisitPlan(pageParam).then(res => {
                        this.distributionVisitPlanFlag = true;
                        this.visitorList = res.data.data.visitorList;
                        this.terminalDistributionVisitPlanList = res.data.data.terminalResponseDtoList;
                    })
                }
                if(this.tabType == 'd'){
                    // 勾选的数据
                    var pageParam = this.dSelected;
                    if (pageParam.length == 0){
                        this.$message({
                            showClose: false,
                            message: '请选择拜访目标',
                            type: 'warning',
                            duration: 1000,
                            offset:70
                        });
                        return
                    }
                    if (this.dSelectionCount > 100){
                        this.$message({
                            showClose: false,
                            message: '最多选择100个拜访单位',
                            type: 'error',
                            duration: 1000,
                            offset:70
                        });
                    }
                    console.log("==============dSelected");
                    console.log(pageParam);
                    var newpageParam = pageParam.map(item=>{
                        return {
                            terminalTraderId: item.traderId,
                            terminalName: item.traderName,
                            zoneId: item.countyCode,
                            provinceCode: item.provinceCode,
                            provinceName: item.provinceName,
                            cityCode: item.cityCode,
                            cityName: item.cityName,
                        }
                    })
                    getDistributionVisitPlan(newpageParam).then(res => {
                        this.distributionVisitPlanFlag = true;
                        this.visitorList = res.data.data.visitorList;
                        this.terminalDistributionVisitPlanList = res.data.data.terminalResponseDtoList;
                    })
                }

            },
            getTerminalAndVisitorNames() {
                var customerNature = 466;
                if(this.tabType == 'd'){
                    customerNature = 465;
                }
                debugger
                return this.terminalDistributionVisitPlanList.map(row => {
                    return {
                        customerName: row.terminalName,
                        traderId: row.terminalTraderId,
                        provinceCode: row.provinceCode,
                        provinceName: row.provinceName,
                        cityCode: row.cityCode,
                        cityName: row.cityName,
                        zoneId: row.zoneId,
                        visitorId: row.visitorId,
                        customerNature: customerNature,
                    };
                });
            },
            submitVisitPlan(){
                this.submitDisabled = true;

                if (!this.visitTime) {
                    this.$message({
                        showClose: false,
                        message: '请选择计划拜访时间',
                        type: 'warning',
                        duration: 1000,
                        offset:70
                    });
                    this.submitDisabled = false;
                    return
                }
                if (!this.visitTarget) {
                    this.$message({
                        showClose: false,
                        message: '请选择拜访目标',
                        type: 'warning',
                        duration: 1000,
                        offset:70
                    });
                    this.submitDisabled = false;
                    return
                }
                let checkFlag = this.terminalDistributionVisitPlanList.some(item => {
                    if (!item.visitorId) {
                        this.$message({
                            showClose: false,
                            message: '请选择拜访人:'+item.terminalName,
                            type: 'warning',
                            duration: 1000,
                            offset:70
                        });
                        return true;
                    }
                    return false;
                });
                if (checkFlag) {
                    this.submitDisabled = false;
                    return
                }

                var pageParam = {
                    "visitTime": this.visitTime,
                    "visitTarget": this.visitTarget,
                    "visitBatchDistributeDtoList": this.getTerminalAndVisitorNames()
                };
                submitVisitPlan(pageParam).then(res => {
                    if (res.data.code == 0) {
                        this.$message.success("提交成功");
                        this.submitDisabled = false;
                        this.distributionVisitPlanFlag = false
                    } else {
                        this.$message.error("提交失败");
                        this.submitDisabled = false;
                    }
                })
            },


            // 重置按钮
            reset() {
                if (this.tabType == 't'){
                    window.location.reload();
                }
                if(this.tabType == 'd'){
                    this.dSelected = [],
                    this.dSelectedKeys = [],
                    this.dCurrentSize = 10,
                    this.dCurrentPageNo = 1,
                    this.dTotalLines = 0,
                    this.tName = '',
                    this.dList = [],
                    this.d.cooperationTime = '3',
                    this.cooperationTime = '3',
                    this.d.lastBiddingTime=  [],
                    this.d.lastSaleTime = []
                    this.dMultipleSelection = [],
                    this.dSelectionCount = 0,
                    this.allAreaOptions
                    this.allAreaOptionsSelected = [],
                    this.dCooperationStatus = -1,
                    this.dEmptyText = "请输入筛选条件后再进行搜索"
                    this.sortColumn = 5;
                }
            },

            // 表头排序回调函数
            sort_change({ column }) {
                var sort = column.order;
                var property = column.property;
                if (sort == "descending") {
                    this.sortType = 1;
                }
                if (sort == "ascending") {
                    this.sortType = 0;
                }

                switch(property)
                {
                    case "biddingCount":
                        this.sortColumn = 1; // 1:中标次数
                        break;
                    case "saleCount":
                        this.sortColumn = 2;
                        break;
                    case "businessChanceCount":
                        this.sortColumn = 3;
                        break;
                    case "quoteCount":
                        this.sortColumn = 4;
                        break;
                    case "lastBiddingTime":
                        this.sortColumn = 5; //5:最近中标时间
                        break;
                    case "lastSaleTime":
                        this.sortColumn = 6;// 6:最近交易时间
                        break;
                    case "distributionLinkCount":
                        this.sortColumn = 7;// 7:合作渠道商数量
                        break;
                    default:
                        this.sortColumn = 0;
                }
                if (this.tabType == 't'){
                    this.doSearch();
                }else {
                    this.doSearch2();
                }

            },
            categoriesTypesChange(val){
                const node = this.$refs.categoriesTypesRef.getCheckedNodes(true);
                this.categoriesTypesValue = node.map(item => item.value);
            },

            doSearch() {
                if (this.tName.length > 0 && this.tName.length < 4){
                    this.$message({
                        showClose: false,
                        message: '至少输入四个关键字',
                        type: 'error',
                        duration: 1000,
                        offset:70
                    });
                    return
                }
                var pageParam = {
                    "pageSize": this.tCurrentSize,
                    "pageNum": this.tCurrentPageNo,
                    "param": {
                        terminalName: this.tName,
                        terminalAreaSearchList: this.areaOptionsSelected.map((item) => {
                            return {
                                province: item[0],
                                city: item[1],
                                county: item[2],
                            }
                        }),
                        sortColumn: this.sortColumn,
                        sortType: this.sortType,
                        terminalCategories: this.categoriesTypesValue,
                        institutionLevel: this.institutionLevel,
                        institutionNature: this.institutionNature,
                        department: this.department,
                        coverStatus: this.coverStatus,
                        cooperationStatus: this.tCooperationStatus,
                    }
                };

                console.log(pageParam)
                this.loading = true;
                getTerminalPage(pageParam).then(res => {
                    this.loading = false;
                    this.tList = res.data.data.list;
                    console.log("this.tList")
                    console.log(this.tList)
                    this.tTotalLines = res.data.data.total;
                    if (this.tTotalLines == 0){
                        this.tEmptyText = "暂无匹配结果";
                    }
                    this.tUpdateCurrentPageData();
                })
            },

            doSearch2() {
                if (this.tName.length == 0){
                    this.$message({
                        showClose: false,
                        message: '请输入精准终端名称',
                        type: 'warning',
                        duration: 1000,
                        offset:70
                    });
                    return;
                }
                if (this.tName.length < 4){
                    this.$message({
                        showClose: false,
                        message: '至少输入四个关键字',
                        type: 'error',
                        duration: 1000,
                        offset:70
                    });
                    return
                }
                var pageParam = {
                    "pageSize": this.dCurrentSize,
                    "pageNum": this.dCurrentPageNo,
                    "param": {
                        terminalName: this.tName,
                        cooperationTime: this.d.cooperationTime,
                        traderAreaSearchList: this.allAreaOptionsSelected.map((item) => {
                            return {
                                province: item[0],
                                city: item[1],
                                county: item[2],
                            }
                        }),
                        // elementui clear选中时间后，会把绑定对象置为null
                        lastBiddingTimeStart: this.d.lastBiddingTime == null || this.d.lastBiddingTime.length == 0 ? null : formatDate(this.d.lastBiddingTime[0]) + ' 00:00:00',
                        lastBiddingTimeEnd: this.d.lastBiddingTime == null ||  this.d.lastBiddingTime.length == 0 ? null : formatDate(this.d.lastBiddingTime[1])+ ' 23:59:59',
                        lastSaleTimeStart: this.d.lastSaleTime == null || this.d.lastSaleTime.length == 0 ? null : formatDate(this.d.lastSaleTime[0])+ ' 00:00:00',
                        lastSaleTimeEnd: this.d.lastSaleTime == null || this.d.lastSaleTime.length == 0 ? null : formatDate(this.d.lastSaleTime[1])+ ' 23:59:59',
                        cooperationStatus: this.dCooperationStatus,
                        sortColumn: this.sortColumn,
                        sortType: this.sortType
                    }
                };

                console.log(pageParam)
                this.loading = true;
                getCooperationDistributionLinkPage(pageParam).then(res => {
                    this.loading = false;
                    if (res.data.code != 0){
                        const message = res.data.message;
                        this.$message({
                            showClose: false,
                            message: message,
                            type: 'error',
                            duration: 3000,
                            offset:70
                        });
                        return;
                    }
                    this.dList = res.data.data.list;

                    this.dTotalLines = res.data.data.total;
                    if (this.dTotalLines == 0){
                        this.dEmptyText = "暂无匹配结果";
                    }
                    this.dUpdateCurrentPageData();
                })
            },
            formatDate(date) {
                if (!date) {
                    return null;
                }
                return moment(date).format('yyyy-MM-dd HH:mm:ss');
            },
            tHandleSizeChange(val) {
                this.tCurrentSize = val;
                this.tCurrentPageNo = 1;
                this.doSearch();
            },
            dHandleSizeChange(val) {
                this.dCurrentSize = val;
                this.dCurrentPageNo = 1;
                this.doSearch2();
            },
            tHandleCurrentChange(page) {
                this.tCurrentPageNo = page;
                this.doSearch();
            },
            dHandleCurrentChange(page) {
                this.dCurrentPageNo = page;
                this.doSearch2();
            },

            tUpdateCurrentPageData() {
                // 重置选中状态
                this.$nextTick(() => {
                    this.tList.forEach(row => {
                        const isSelected = this.tSelectedKeys.includes(this.getTRowKey(row));
                        this.$refs.tTable.toggleRowSelection(row, isSelected);
                    });
                });
            },

            dUpdateCurrentPageData() {
                // 重置选中状态
                this.$nextTick(() => {
                    this.dList.forEach(row => {
                        const isSelected = this.dSelectedKeys.includes(this.getDRowKey(row));
                        this.$refs.dTable.toggleRowSelection(row, isSelected);
                    });
                });
            },

            dHandleCooperationTimeChange() {
                // 时间改变后，清空日期组件的选中值
                this.d.lastBiddingTime = [];
                this.d.lastSaleTime = [];
            },

            // 改变日期可选范围
            disabledDate(time) {

                let nowDate = (new Date()).getTime();

                var today = new Date();
                today.setHours(23);
                today.setMinutes(59);
                today.setSeconds(59);
                if (this.d.cooperationTime == '3') {
                    let three = 1097 * 24 * 3600 * 1000;
                    let threeYears = nowDate - three;
                    return time.getTime() > today.getTime() || time.getTime() < threeYears;
                }

                if (this.d.cooperationTime == '2') {
                    let two = 732 * 24 * 3600 * 1000;
                    let twoYears = nowDate - two;
                    return time.getTime() > today.getTime() || time.getTime() < twoYears;
                }

                if (this.d.cooperationTime == '1') {
                    let one = 367 * 24 * 3600 * 1000;
                    let oneYears = nowDate - one;
                    return time.getTime() > today.getTime() || time.getTime() < oneYears;
                }
            },

            convertRegionData(listData) {

            },
            // 查看详情
            viewTerminalPortraitInfo(terminalName) {
                console.log('/trader/customer/new/terminalPortrait.do?searchName='+terminalName);
                openTab("终端详情", '/trader/customer/new/terminalPortrait.do?searchName=' + encodeURIComponent(terminalName));
            },
            // 查看详情
            viewCustomerPortraitInfo(terminalTraderId) {
                console.log('/trader/customer/new/portrait.do?traderId='+terminalTraderId);
                openTab("客户详情", '/trader/customer/new/portrait.do?traderId=' + terminalTraderId);
            },
            async initData() {
                await getBusinessTerminalCategoriesAndTypes().then(res => {
                    this.categoriesTypesSelect = res.data.data;
                    console.log(this.categoriesTypesSelect)
                });
                await getAreaSearchList().then(res => {
                    this.areaOptions = res.data.data;
                    this.selectAll();
                });

                await getAllAreaSearchList().then(res => {
                    const convertRegionData = (listData) => {
                        return result = listData.map(item => ({
                            value: item.regionName,
                            label: item.regionName,
                            children: item.child && item.child.length > 0 ? convertRegionData(item.child) : ''
                        }));
                    }
                    this.allAreaOptions= convertRegionData(res.data.listData);
                });
            },

            highlightCooperationTerminal(cooperationTerminal) {
                const highlightWord = this.tName;
                return cooperationTerminal.replace(new RegExp(highlightWord, 'g'), '<span style="color: red">' + highlightWord + '</span>');
            }
        },

        watch: {
            visitTarget: {
                immediate: true,
                deep: true,
                handler(newVal, oldVal) {
                    if (newVal.includes('A') && newVal.includes('C')) {
                        const indexToRemove = newVal.indexOf(oldVal.includes('A') ? 'C' : 'A');
                        if (indexToRemove !== -1) {
                            newVal.splice(indexToRemove, 1);
                        }
                    }
                }
            }
        }
    })
</script>

<style>
    .aligned-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    .demonstration {
        margin-left: 10px;
        margin-right: 10px;
        width: 120px; /* 根据需要调整这个宽度 */
    }
    .aligned-component {
        width: 220px;
    }
</style>