package com.vedeng.erp.finance.domain.dto;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.common.constant.TaxesConstant;
import lombok.Data;

/**
 * 销项票邮箱交付入参
 */
@Data
public class SaleInvoiceEmailRequestDto implements ITaxesParam {

    private static final long serialVersionUID = -1068013244712605664L;
    /**
     * 纳税人识别号
     */
    private String nsrsbh;

    /**
     * 发票号码
     */
    private String fphm;

    /**
     * 开票日期(YYYY-MM-DD HH:mm:ss)
     */
    private String kprq;

    /**
     * 文件格式
     */
    private String wjgs;

    /**
     * 购方邮箱地址
     */
    private String gfyxdz;

    public SaleInvoiceEmailRequestDto(){
        this.wjgs = TaxesConstant.PDF;
    }
}
