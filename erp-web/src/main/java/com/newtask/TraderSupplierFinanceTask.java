package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.trader.service.TraderSupplierFinanceService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 供应商信息财务初始化、每日凌晨2、3、4点执行昨日订单获取客户同步
 * 供应商 初始化
 * 获取历史采购单所有客户信息去重进行同步
 * 增量同步，获取销售单、采购单关联的客户，如果供应商财务不存在该供应商则同步，存在忽略
 * ERP订单太多，考虑分页处理
 * 每日凌晨2、3、4点执行昨日订单获取供应商同步
 * initType 为1，则直接全量，如果initType为2，按照initDate增量同步，如果入参为空，则默认前一天
 * params json参数格式：{"initType":1,"initDate":"2022-11-29"}  
 * @ClassName:  TraderSupplierFinanceTask   
 * @author: Neil.yang
 * @date:   2022年11月29日 下午6:35:44    
 * @Copyright:
 */
@Component
@JobHandler(value = "traderSupplierFinanceTask")
public class TraderSupplierFinanceTask extends AbstractJobHandler {

	private static final String INIT_DATE = "initDate";

	private static final String INIT_TYPE = "initType";

	private static final int CAPCITY = 16;
	
	@Autowired
	private TraderSupplierFinanceService traderSupplierFinanceService;

    @SuppressWarnings("unchecked")
	@Override
    public ReturnT<String> doExecute(String params) throws Exception {
        XxlJobLogger.log("===================供应商信息财务初始化===开始======================");
		Map<String,Object> paramMap =  new HashMap<>(CAPCITY);
    	try {
    		if(Objects.nonNull(params) && !"".equals(params)) {
    			paramMap =  (Map<String, Object>) JSON.parse(params);
    			if(Objects.isNull(paramMap.get(INIT_TYPE))) {
    				XxlJobLogger.log("入参异常：无initType字段，入参："+paramMap);
    				return FAIL;
    			}
    			if((int)paramMap.get(INIT_TYPE) != 2 && (int)paramMap.get(INIT_TYPE) !=1) {
					XxlJobLogger.log("入参异常：initType值为1或者2，入参："+paramMap);
    				return FAIL;
    			}
    			if((int)paramMap.get(INIT_TYPE)==2 && Objects.isNull(paramMap.get(INIT_DATE))) {
    				XxlJobLogger.log("入参异常：无initDate字段，入参："+paramMap);
    				return FAIL;
    			}
    		}
    	}catch(Exception e) {
    		XxlJobLogger.log("入参转化异常，请输入{\"initType\":1}，执行全量,或者{\"initType\":2,\"initDate\":\"2000-01-01\"}，执行指定日期的增量，入参：paramMap："+params,e);
    		return FAIL;
    	}
    	long startTime = System.currentTimeMillis();
        try {
        	//params为空，执行默认时间客户同步
        	if(Objects.isNull(params) || "".equals(params)) {
        		//默认前一天数据
        		long startDate = DateUtil.getDayStartTime(DateUtil.getPreviousDayByDateTime(new Date())).getTime();
            	long endDate = DateUtil.getDayEndTime(DateUtil.getPreviousDayByDateTime(new Date())).getTime();
        		syncTrader(startDate,endDate);
        	}else{
        		int initType = (int) paramMap.get(INIT_TYPE);
        		//执行全量
        		if(initType==1) {
        			//执行全量
        			syncTrader(null,null);
        		}
        		//执行指定日期的
        		else{
        			if(Objects.isNull(paramMap.get(INIT_DATE))) {
        				XxlJobLogger.log("请指定，initDate日期");
        				return FAIL;
        			}
        			String initDate = (String) paramMap.get(INIT_DATE);
        			//执行指定日期的增量
        			long startDate = DateUtil.getDayStartTime(DateUtil.StringToDate(initDate)).getTime();
        	    	long endDate = DateUtil.getDayEndTime(DateUtil.StringToDate(initDate)).getTime();
        			syncTrader(startDate,endDate);
        		}
        	}
        }catch(Exception e){
        	long endTime = System.currentTimeMillis();
        	XxlJobLogger.log("===================供应商信息财务初始化===失败===,执行时间:"+(endTime-startTime)/1000+"秒=============================",e);
        	return FAIL;
        }
        long endTime = System.currentTimeMillis();
        XxlJobLogger.log("===================供应商信息财务初始化===结束======================");
        XxlJobLogger.log("===================供应商信息财务初始化===成功===,执行时间:"+(endTime-startTime)/1000+"秒===================");
        return SUCCESS;
    }
    
    //执行同步操作,如果供应商已经存在供应商财务信息中，则不插入
    private void syncTrader(Long startDate, Long endDate) throws Exception{
    	XxlJobLogger.log("执行同步操作：入参:"+startDate+"=="+endDate);
    	traderSupplierFinanceService.syncTrader(startDate,endDate);
	}

}
