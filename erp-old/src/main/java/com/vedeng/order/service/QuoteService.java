package com.vedeng.order.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import com.newtask.model.BqoSysAutoCloseDto;
import com.vedeng.authorization.model.User;
import com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.ConsultGooodsVo;
import com.vedeng.order.model.vo.QuoteorderConsultContentVo;
import com.vedeng.order.model.*;
import com.vedeng.order.model.vo.AuthorizationApplyVo;
import com.vedeng.order.model.vo.QuoteorderConsultContentVo;
import com.vedeng.order.model.vo.QuoteorderVo;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.model.CommunicateRecord;
import org.apache.ibatis.annotations.Param;

public interface QuoteService extends BaseService{

	/**
	 * <b>Description:</b><br> 查询报价列表数据
	 * @param quoteorder
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月26日 上午11:35:17
	 */
	Map<String,Object> getQuoteListPage(Quoteorder quoteorder,Page page);

	/**
	 * <b>Description:</b><br> 根据报价ID和商机ID...获取沟通
	 * @param list
	 * @param communicateType 类型
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月26日 上午11:36:01
	 */
	Integer getCommunicateRecordCount(CommunicateRecord cr,Integer bussinessType,Integer quoteType);

	/**
	 * <b>Description:</b><br> 获取订单沟通次数（包含商机，报价，销售订单）
	 * @param saleIdList
	 * @param quoteIdList
	 * @param businessIdList
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年1月15日 下午1:28:10
	 */
	List<CommunicateRecord> getCommunicateNumList(List<Integer> saleIdList,List<Integer> quoteIdList, List<Integer> businessIdList);


	/**
	 * <b>Description:</b><br> 根据日期范围查询沟通记录
	 * @param beginDate
	 * @param endDate
	 * @param communicateType 沟通类型
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月26日 上午11:36:52
	 */
	List<Integer> getCommunicateRecordByDate(Long beginDate,Long endDate,String communicateType);

	/**
	 * <b>Description:</b><br> 保存报价信息
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:42:58
	 */
	ResultInfo<Quoteorder> saveQuote(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 查询报价详情信息， 已弃用，见 getQuoteOrderInfoById
	 * @param quoteorderId
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:43:14
	 */
	@Deprecated
	Quoteorder getQuoteInfoByKey(Integer quoteorderId);

	/**
	 * 根据报价单id查询报价信息， 重写 getQuoteInfoByKey
	 * @param quoteorderId 报价单id
	 * @return 报价信息
	 */
	Quoteorder getQuoteOrderInfoById(Integer quoteorderId);


	/**
	 * 系统自动关闭/联动关闭报价联动关闭商机
	 * @param quoteorderId
	 * @param optionType
	 * @return
	 */
	Quoteorder relateCloseQuote(Integer quoteorderId, String optionType);
	/**
	 * <b>Description:</b><br> 修改报价中客户信息
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:43:29
	 */
	ResultInfo<?> updateQuoteCustomer(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 修改报价终端信息
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年6月29日 下午5:43:41
	 */
	ResultInfo<?> updateQuoteTerminal(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 保存报价产品信息
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:17:49
	 */
	ResultInfo<?> saveQuoteGoods(QuoteorderGoods quoteGoods,Attachment ach);

	/**
	 * <b>Description:</b><br> 根据报价产品ID查询报价产品信息
	 * @param quoteGoodsId
	 * @param httpSession
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:28:22
	 */
	QuoteorderGoods getQuoteGoodsById(Integer quoteGoodsId, HttpSession httpSession);

	/**
	 * <b>Description:</b><br> 根据报价ID查询报价产品（全部）
	 * @param quoteorderId
	 * @param hs
	 * @param viewType
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:28:59
	 */
	Map<String, Object> getQuoteGoodsByQuoteId(Integer quoteorderId,Integer companyId, HttpSession hs, Integer viewType,Integer traderId);

	/**
	 * <b>Description:</b><br> 编辑保存报价产品信息
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:18:56
	 */
	ResultInfo<?> editQuoteGoods(QuoteorderGoods quoteGoods,Attachment ach);

	/**
	 * <b>Description:</b><br> 删除报价产品信息
	 * @param request
	 * @param quoteGoods
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月4日 上午10:19:35
	 */
	ResultInfo<?> delQuoteGoodsById(QuoteorderGoods quoteGoods);

	/**
	 * <b>Description:</b><br> 编辑报价付款金额等信息
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月5日 上午11:24:54
	 */
	ResultInfo<?> editQuoteAmount(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 根据外键查询沟通记录（分页）
	 * @param relatedIds
	 * @param communicateType
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月5日 上午11:36:22
	 */
	List<CommunicateRecord> getQuoteCommunicateListPage(String relatedIds,String communicateTypes,Page page);

	Map<Integer,String> getCommnuicateTraderTag(List<Integer> commnuicateIdList);

	/**
	 * <b>Description:</b><br> 修改报价是否生效
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午1:23:39
	 */
	ResultInfo<?> editQuoteValIdSave(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 保存报价咨询记录
	 * @param quoteConsult
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午2:32:06
	 */
	ResultInfo<?> addQuoteConsultSave(QuoteorderConsult quoteConsult, User user);

	/**
	 * <b>Description:</b><br> 根据报价ID查找咨询记录
	 * @param quoteorderId
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午3:02:21
	 */
	List<QuoteorderConsult> getQuoteConsultList(Integer quoteorderId);

	/**
	 * 获取报价商品的明细咨询-对应的最后一个回复用户
	 * @param quoteorderId
	 * @return
	 */
	List<QuoteorderGoods> getQuoteOrderGoodsConsultList(Integer quoteorderId);


	/**
	 * 根据报价ID查找归属销售
	 * @param quoteorderId
	 * @return
	 */
	List<Integer> findByQuoteorderIdGetUserId(Integer quoteorderId);

	/**
	 * <b>Description:</b><br> 修改报价主表信息(有沟通记录0无 1有)
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月7日 下午6:03:07
	 */
	ResultInfo<?> editQuoteHaveCommunicate(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 修改报价为失单状态（备注）
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月10日 上午11:04:36
	 */
	ResultInfo<?> editLoseOrderStatus(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 验证报价单中产品货期和报价是否为空
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月25日 下午5:23:57
	 */
	ResultInfo<?> getQuoteGoodsPriceAndCycle(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 查询报价咨询列表（包括销售人员）
	 * @param quote
	 * @param page
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月10日 下午3:32:50
	 */
	Map<String, Object> getQuoteConsultListPage(QuoteorderConsult quoteConsult, Page page,HttpSession session);

	/**
	 * <b>Description:</b><br> 咨询答复内容保存
	 * @param quoteConsult
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月11日 下午2:14:44
	 */
	ResultInfo<?> saveReplyQuoteConsult(QuoteorderConsult quoteConsult);

	/**
	 * <b>Description:</b><br> 修改报价咨询回复状态保存
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月11日 下午3:00:47
	 */
	ResultInfo<?> editConsultStatus(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 根据条件查询產品附件表信息
	 * @param ach
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月18日 下午6:42:24
	 */
	Attachment getQuoteGoodsAttachment(Attachment ach);

	/**
	 * <b>Description:</b><br> 根据条件查询报价记录数
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月24日 下午6:54:04
	 */
	Integer getQuoteListCount(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 获取指定记录报价信息条数
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月24日 下午6:56:15
	 */
	List<Quoteorder> getQuoteListSize(Quoteorder quote,Integer startSize,Integer endSize);

	/**
	 * <b>Description:</b><br> 验证客户是否被禁用
	 * @param traderCustomerId
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年7月26日 下午5:18:05
	 */
	ResultInfo<?> getTraderCustomerStatus(Integer traderCustomerId);

	/**
	 * <b>Description:</b><br> 验证报价中新添加的产品是否重复
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> Administrator
	 * <br><b>Date:</b> 2017年9月11日 下午6:55:10
	 */
	ResultInfo<?> vailQuoteGoodsRepeat(QuoteorderGoods quoteGoods);
	/**
	 *
	 * <b>Description:</b><br> 判断订单是否可以生效
	 * @param saleorder
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2017年11月10日 上午9:57:27
	 */
	ResultInfo<?> isvalidQuoteOrder(Quoteorder quote);
    /**
     *
     * <b>Description:</b><br> 打印报价单
     * @param quote
     * @return
     * @Note
     * <b>Author:</b> scott
     * <br><b>Date:</b> 2017年12月13日 上午10:27:00
     */
	QuoteorderVo getPrintInfo(Quoteorder quote);

	/**
	 * <b>Description:</b><br> 获取报价同步信息
	 * @param quoteorderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年3月16日 上午9:11:22
	 */
	Quoteorder getQuoteorderForSync(Integer quoteorderId);

	/**
	 * <b>Description:</b><br> 报价消息通知内容
	 * @param orderId
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2018年3月17日 下午3:02:07
	 */
	Quoteorder getMessageInfoForSync(Integer orderId);

	/**
	 * <b>Description:</b><br> 根据产品分类ID查询商品归属人员
	 * @param categoryIdList
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2018年5月3日 上午10:28:05
	 */
	List<User> getGoodsCategoryUserList(List<Integer> categoryIdList,Integer companyId);

	/**
	 *
	 * <b>Description:</b><br> 修改报价产品信息
	 * @param quote
	 * @return
	 * @Note
	 * <b>Author:</b> Michael
	 * <br><b>Date:</b> 2018年6月20日 下午3:29:46
	 */

	ResultInfo<?> editQuoteOrderGoods(Quoteorder quote);

	/**
	* @Description:  发送消息（您有一个待分配的注册用户，请查看）
	* @Param: []
	* @return: void
	* @Author: addis
	* @Date: 2019/7/30
	*/
	void sendAllocation(String mobile,Integer userId);

	/**
	* @Description: 发送消息（离职人员指：用户为禁用状态）
	* @Param: [mobile, userId]
	* @return: void
	* @Author: addis
	* @Date: 2019/7/30
	*/
	void getIsDisabled(String mobile,Integer userId,String saleorderNo,Integer saleorderId);

	/**
	* @Title: updateQuote
	* @Description: TODO(更新报价)
	* @param @param quoteorder    参数
	* @return void    返回类型
	* <AUTHOR>
	* @throws
	* @date 2019年8月12日
	*/
	void updateQuote(Quoteorder quoteorder);

	/**
	 * @Description: 保存报价时，报价列表是是否存在
	 * @Param: [bussinessChanceId]
	 * @return: java.lang.Integer
	 * @Author: addis
	 * @Date: 2019/8/30
	 */
	Integer isExistBussinessChanceId(int bussinessChanceId);

	/**
	 * 判断授权书申请表的授权产品数量
	 * @param quoteorderId
	 * @return
	 */
	int getAuthorizationSum(Integer quoteorderId,Integer applyStatus);

    AuthorizationStorage getAuthorizationStorageInfoByQuoteOrderId(Integer quoteorderId);


	List<AuthorizationStorage> getAuthorizationStorageInfoListByQuoteOrderId(Integer quoteorderId);


	void insertAuthorizationStorageInfo(AuthorizationStorage authorizationStorage);

	void updateAuthorizationStorageInfo(AuthorizationStorage authorizationStorage);

    AuthorizationApply getAuthorizationApplyInfoBySkuIdAndQuoteId(Integer quoteorderId, Integer skuId,Integer authorizationApplyId);

	List<String> getSqNumByYearAndMonth(String yearAndMonth);

	void insertAuthorizationApplyInfo(AuthorizationApply authorizationApply);

	void updateAuthorizationApplyInfo(AuthorizationApply authorizationApply);

	List<Integer> getQuoteGoodsByQuoteOrderId(Integer quoteorderId);

	AuthorizationApply getAutnorizationApplyByNum(String authorizationApplyNum);

	List<AuthorizationApply> getAuthorizationApplyByQuoteId(Integer quoteorderId);

	AuthorizationApply getAuthorizationIsRepeat(AuthorizationApply authorizationApply);

	AuthorizationApply getAuthorizationApplyByKeyId(Integer authorizationApplyId);

    void updateAuthorizationApplyModTimeAndStatus(Integer authorizationApplyId,Integer userId, Long time, Integer authorizationPass);

	List<AuthorizationApply> getAuthorizationApplyListByQuoteId(Integer quoteorderId);

    List<AuthorizationApplyDto> getAuthorizationApplylistpage(AuthorizationApplyVo authorizationApplyVo,Page page);

	void delAuthorizationStorageById(Integer temporaryStorageId,Integer userId,Long time);

	AuthorizationStorage getTemporaryStorageByNum(String authorizationApplyNum);

	AuthorizationApply getAuthorizationApplyByNum(String authorizationApplyNum);

	void updateAuthorizationApplyReviewer(Integer authorizationApplyId, String verifyUsers);

	void updateAuthorizationApplyComments(Integer authorizationApplyId, String comments);

	List<String> getApplyPreson();

    void updateAuthorizationStorage(String authorizationApplyNum);

    List<AuthorizationApply> getAuthorizationApplyListByQuoteIdAndPass(Integer quoteorderId);

    int getAuthorizationMaxId();

	AuthorizationStorage getAuthorizationStorageInfoByQuoteOrderIdAndNum(Integer quoteorderId, String authorizationApplyNum);

	void saveSupplyReplyOfQuoteorderConsult(QuoteorderConsultContentVo consultReply, Integer userId);

	void updateConsultStatusOfQuoteorder(Integer quoteorderId, Integer type);

	/**
	 * 设置报价单部门信息
	 * @param quote
	 * @param user
	 */
	void setQuteOrderUserInfo(QuoteorderVo quote, User user);

	/**
	 * 获取报价关闭原因信息
	 * @param closeReasonId
	 * @return
	 */
	String getCloseReasonInfo(Integer closeReasonId);

	ResultInfo<?> editApplyValidQuoteorder(HttpServletRequest request, Quoteorder quote, String taskId, HttpSession session);

	List<ConsultGooodsVo> getConsultGoodsInfoBySaleorderOrQuoteorder(Integer consultRelatedId, Integer consultType);

	Boolean saveQuoteorderConsult(QuoteorderConsultContentVo consultContentVo, Integer userId);

	List<QuoteorderGoods> getConsultResultOfQuoteorderGoods(List<QuoteorderGoods> quoteorderGoodsList, Integer viewType);

	List<SaleorderGoods> getConsultResultOfSaleorderGoods(List<SaleorderGoods> saleorderGoodsList);

	void saveAssignConsultReplier(QuoteorderConsultReply consultReply);

	void saveManagerReplyOfQuoteorderConsult(QuoteorderConsultContentVo consultReply, Integer userId);

	/**
	 * 根据商品订货号获取产品归属人id列表
	 *
	 * @param skuNo
	 * @return
	 */
	List<Integer> getAssignUserOfSku(String skuNo);

	/**
	 * 报价单查询报价商品（非临时）
	 *
	 * @param orderId
	 * @return
	 */
	List<QuoteorderGoods> getQuoteorderGoodsByOrderId(Integer orderId);

	/**
	 * 保存报价咨询状态(弃用)
	 *
	 * @param quoteorderId 报价单ID
	 * @param quoteorderConsultReply  需包包含报价商品ID 报备状态
	 * @return
	 */
	Integer saveQuoteConsultReplyStatus(Integer quoteorderId,QuoteorderConsultReply quoteorderConsultReply);

	/**
	 * 保存报价单的报备状态
	 * @param goodsId
	 * @param reportStatus
	 * @return
	 */
	Integer saveQuoteGoodsReportStatus(Integer goodsId, Integer reportStatus);

	/**
	 * 保存报价单是否需要人工回复信息
	 *
	 * @param quoteorderGoodsId
	 * @param isNeedReply
	 * @return
	 */
	Integer saveIsNeedReply(Integer quoteorderGoodsId, Integer isNeedReply);
	/**
	 * <b>Description:</b>查询报价关联bd订单日志<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/10/13
	 */
	QuoteLinkBdLog getQuoteLinkBdLog(Integer logId);

	/**
	 * <b>Description:</b>保存报价单关联bd订单日志表<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/10/13
	 */
	QuoteLinkBdLog saveQuoteLinkBdLog(QuoteLinkBdLog log);

	/**
	 * <b>Description:</b>保存报价单商品br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/10/16
	 */
	ResultInfo saveQuoteGoodsReal(QuoteorderGoods goods);

	/**
	 * <b>Description:</b>修改报价单商品<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/10/19
	 */
	Integer updateQuoteGoodsReal(QuoteorderGoods goods);

	QuoteLinkBdLog getQuoteLinkBdLogByInfo(QuoteLinkBdLog log);

	/**
	 * 处理报价商品报备结果信息
	 * key 报价商品ID  value 是否需要报备
	 *
	 * @param quoteorderId
	 * @return
	 * @throws Exception
	 */
	Map<Integer, Integer> dealWithQuoteOrderGoodsAuthorizationResult(Integer quoteorderId);


	/**
	 * 根据父类ID和optionType返回字典表主键
	 * @param parentId
	 * @param optionType
	 * @return
	 */
	Integer getSysOptionDefIdBypt(Integer parentId,String optionType);

	/**
	 * @Description 获取未成单的生效时间一个月内包含此sku的报价单号
	 * <AUTHOR>
	 * @Date 11:02 2021/8/31
	 * @Param [skuId]
	 * @return java.util.List<java.lang.Integer>
	 **/
    List<Integer> getAffectOrderBySkuId(Long skuId);

	/**
	 * 报价单获取条件信息
	 *
	 * @param quoteId
	 * @return
	 */
	QuoteLinkBdLog getQuoteLinkBdLogByQuoteId(Integer quoteId);

	/**
	 * 查询报价单商品
	 * @param quoteorderId
	 * @return
	 */
    List<QuoteorderGoods> getQuoteGoodsByQuoteId(Integer quoteorderId);

    void updateOnlineSharetime(Integer quoteorderId);

    QuoteorderVo getQuoteOrderInfoById2(Integer quoteorderId);

	/**
	 * 审核流待办提醒
	 * @param lendOutId
	 * @param taskId
	 * @param userName
	 */
	void sendWeixinNoticeForActiviti(Long authorizationApplyId, String businessKey, String taskId,String userName);


	/**
	 * 获取超时待提醒的任务 30分钟
	 * @return
	 */
	List<ActivitiTaskUnDoEntity> selectTimeoutTaskForNotice();
}
