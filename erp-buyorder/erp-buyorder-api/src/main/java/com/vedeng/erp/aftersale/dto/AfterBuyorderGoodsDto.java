package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 采购售后商品dto
 * <AUTHOR>
 */
@Data
public class AfterBuyorderGoodsDto {
    /**
     * 售后商品id
     */
    private Integer afterSalesGoodsId;
    /**
     * 售后单id
     */
    private Integer afterSalesId;
    /**
     * 产品ID
     */
    private Integer goodsId;
    /**
     * sku编号
     */
    private String sku;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 商品型号
     */
    private String model;
    /**
     * 单位
     */
    private String unitName;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 规格
     */
    private String spec;
    /**
     * 商品名称
     */
    private String skuName;
    /**
     * 采购价
     */
    private BigDecimal buyPrice;
    /**
     * 采购数量
     */
    private Integer buyNum;
    /**
     * 采购仅退票（退票数量）
     */
    private BigDecimal afterInvoiceNum;
}
