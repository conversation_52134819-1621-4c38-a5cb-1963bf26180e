<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.RoleUserRegionConfigMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity">
        <!--@mbg.generated-->
        <!--@Table ROLE_USER_REGION_CONFIG-->
        <id column="ID" jdbcType="BIGINT" property="id"/>
        <result column="ONLINE_SALES_ID" jdbcType="INTEGER" property="onlineSalesId"/>
        <result column="OFFLINE_SALES_ID" jdbcType="INTEGER" property="offlineSalesId"/>
        <result column="PRODUCTION_USER_ID" jdbcType="INTEGER" property="productionUserId"/>
        <result column="PROVINCE" jdbcType="VARCHAR" property="province"/>
        <result column="PROVINCE_ID" jdbcType="INTEGER" property="provinceId"/>
        <result column="CITY" jdbcType="VARCHAR" property="city"/>
        <result column="CITY_ID" jdbcType="INTEGER" property="cityId"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        ONLINE_SALES_ID,
        OFFLINE_SALES_ID,
        PRODUCTION_USER_ID,
        PROVINCE,
        PROVINCE_ID,
        CITY,
        CITY_ID,
        CREATOR,
        UPDATER,
        CREATOR_NAME,
        UPDATER_NAME,
        ADD_TIME,
        MOD_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from ROLE_USER_REGION_CONFIG
        where ID = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from ROLE_USER_REGION_CONFIG
        where ID = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ROLE_USER_REGION_CONFIG (ONLINE_SALES_ID, OFFLINE_SALES_ID, PRODUCTION_USER_ID,
                                             PROVINCE, PROVINCE_ID, CITY,
                                             CITY_ID, CREATOR, UPDATER,
                                             CREATOR_NAME, UPDATER_NAME, ADD_TIME,
                                             MOD_TIME)
        values (#{onlineSalesId,jdbcType=INTEGER}, #{offlineSalesId,jdbcType=INTEGER},
                #{productionUserId,jdbcType=INTEGER},
                #{province,jdbcType=VARCHAR}, #{provinceId,jdbcType=INTEGER}, #{city,jdbcType=VARCHAR},
                #{cityId,jdbcType=INTEGER}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER},
                #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP},
                #{modTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ROLE_USER_REGION_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="onlineSalesId != null">
                ONLINE_SALES_ID,
            </if>
            <if test="offlineSalesId != null">
                OFFLINE_SALES_ID,
            </if>
            <if test="productionUserId != null">
                PRODUCTION_USER_ID,
            </if>
            <if test="province != null and province != ''">
                PROVINCE,
            </if>
            <if test="provinceId != null">
                PROVINCE_ID,
            </if>
            <if test="city != null and city != ''">
                CITY,
            </if>
            <if test="cityId != null">
                CITY_ID,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME,
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="onlineSalesId != null">
                #{onlineSalesId,jdbcType=INTEGER},
            </if>
            <if test="offlineSalesId != null">
                #{offlineSalesId,jdbcType=INTEGER},
            </if>
            <if test="productionUserId != null">
                #{productionUserId,jdbcType=INTEGER},
            </if>
            <if test="province != null and province != ''">
                #{province,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="city != null and city != ''">
                #{city,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                #{cityId,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity">
        <!--@mbg.generated-->
        update ROLE_USER_REGION_CONFIG
        <set>
            <if test="onlineSalesId != null">
                ONLINE_SALES_ID = #{onlineSalesId,jdbcType=INTEGER},
            </if>
            <if test="offlineSalesId != null">
                OFFLINE_SALES_ID = #{offlineSalesId,jdbcType=INTEGER},
            </if>
            <if test="productionUserId != null">
                PRODUCTION_USER_ID = #{productionUserId,jdbcType=INTEGER},
            </if>
            <if test="province != null and province != ''">
                PROVINCE = #{province,jdbcType=VARCHAR},
            </if>
            <if test="provinceId != null">
                PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
            </if>
            <if test="city != null and city != ''">
                CITY = #{city,jdbcType=VARCHAR},
            </if>
            <if test="cityId != null">
                CITY_ID = #{cityId,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null and creatorName != ''">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="updaterName != null and updaterName != ''">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity">
        <!--@mbg.generated-->
        update ROLE_USER_REGION_CONFIG
        set ONLINE_SALES_ID    = #{onlineSalesId,jdbcType=INTEGER},
            OFFLINE_SALES_ID   = #{offlineSalesId,jdbcType=INTEGER},
            PRODUCTION_USER_ID = #{productionUserId,jdbcType=INTEGER},
            PROVINCE           = #{province,jdbcType=VARCHAR},
            PROVINCE_ID        = #{provinceId,jdbcType=INTEGER},
            CITY               = #{city,jdbcType=VARCHAR},
            CITY_ID            = #{cityId,jdbcType=INTEGER},
            CREATOR            = #{creator,jdbcType=INTEGER},
            UPDATER            = #{updater,jdbcType=INTEGER},
            CREATOR_NAME       = #{creatorName,jdbcType=VARCHAR},
            UPDATER_NAME       = #{updaterName,jdbcType=VARCHAR},
            ADD_TIME           = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME           = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update ROLE_USER_REGION_CONFIG
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ONLINE_SALES_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.onlineSalesId != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.onlineSalesId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="OFFLINE_SALES_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.offlineSalesId != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.offlineSalesId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PRODUCTION_USER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.productionUserId != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.productionUserId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PROVINCE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.province != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.province,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="PROVINCE_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.provinceId != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.provinceId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CITY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.city != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.city,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CITY_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.cityId != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.cityId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creatorName != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterName != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when ID = #{item.id,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into ROLE_USER_REGION_CONFIG
        (ONLINE_SALES_ID, OFFLINE_SALES_ID, PRODUCTION_USER_ID, PROVINCE, PROVINCE_ID, CITY,
         CITY_ID, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.onlineSalesId,jdbcType=INTEGER}, #{item.offlineSalesId,jdbcType=INTEGER},
             #{item.productionUserId,jdbcType=INTEGER}, #{item.province,jdbcType=VARCHAR},
             #{item.provinceId,jdbcType=INTEGER},
             #{item.city,jdbcType=VARCHAR}, #{item.cityId,jdbcType=INTEGER}, #{item.creator,jdbcType=INTEGER},
             #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR},
             #{item.updaterName,jdbcType=VARCHAR},
             #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!-- 查询人员与产线区域配置列表 -->
    <select id="listRoleUserRegionConfig" resultType="com.vedeng.erp.system.vo.RoleUserRegionConfigVO">
        SELECT r.ID,
               r.ONLINE_SALES_ID,
               r.OFFLINE_SALES_ID,
               r.PRODUCTION_USER_ID,
               r.PROVINCE,
               r.PROVINCE_ID,
               r.CITY,
               r.CITY_ID,
               CONCAT(r.PROVINCE, r.CITY) AS fullRegionName,
               r.ADD_TIME                 AS createTime,
               r.CREATOR_NAME             AS createBy,
               r.MOD_TIME                 AS updateTime,
               r.UPDATER_NAME             AS updateBy
        FROM ROLE_USER_REGION_CONFIG r
        <where>
            <if test="query != null">
                <if test="query.provinceIds != null and query.provinceIds.size() > 0">
                    AND r.PROVINCE_ID IN
                    <foreach collection="query.provinceIds" item="provinceId" open="(" separator="," close=")">
                        #{provinceId}
                    </foreach>
                </if>
                <if test="query.cityIds != null and query.cityIds.size() > 0">
                    AND r.CITY_ID IN
                    <foreach collection="query.cityIds" item="cityId" open="(" separator="," close=")">
                        #{cityId}
                    </foreach>
                </if>
                <if test="query.userIds != null and query.userIds.size() > 0">
                    AND (
                        r.ONLINE_SALES_ID IN
                    <foreach collection="query.userIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                    OR r.OFFLINE_SALES_ID IN
                    <foreach collection="query.userIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                    OR r.PRODUCTION_USER_ID IN
                    <foreach collection="query.userIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                    )
                </if>
                <if test="query.departmentUserIds != null and query.departmentUserIds.size() > 0">
                    AND (
                    r.ONLINE_SALES_ID IN
                    <foreach collection="query.departmentUserIds" item="userId" open="(" separator="," close=")">
                        #{userId}
                    </foreach>
                    )
                </if>
            </if>
        </where>
        ORDER BY r.ADD_TIME DESC
    </select>

    <!-- 检查配置是否存在重复 -->
    <select id="checkDuplicate" resultType="int"
            parameterType="com.vedeng.erp.system.domain.entity.RoleUserRegionConfigEntity">
        SELECT COUNT(1)
        FROM ROLE_USER_REGION_CONFIG
        <where>
            <if test="onlineSalesId != null">
                AND ONLINE_SALES_ID = #{onlineSalesId}
            </if>
            <if test="offlineSalesId != null">
                AND OFFLINE_SALES_ID = #{offlineSalesId}
            </if>
            <if test="productionUserId != null">
                AND PRODUCTION_USER_ID = #{productionUserId}
            </if>
            <if test="provinceId != null">
                AND PROVINCE_ID = #{provinceId}
            </if>
            <if test="province != null and province != ''">
                AND PROVINCE = #{province}
            </if>
            <if test="cityId != null">
                AND CITY_ID = #{cityId}
            </if>
            <if test="city != null and city != ''">
                AND CITY = #{city}
            </if>
        </where>
    </select>

    <!-- 批量删除配置 -->
    <delete id="batchDelete">
        DELETE
        FROM ROLE_USER_REGION_CONFIG
        WHERE ID IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 获取所有业务人员列表（线上/线下销售、产线人员） -->
    <select id="listAllBusinessUsers" resultType="com.vedeng.erp.system.vo.BusinessUserVO">
        SELECT DISTINCT t.user_id AS userId,
                        TU.USERNAME AS userName,
                        d.ALIAS_HEAD_PICTURE AS userAvatar
        FROM (
          -- 线上销售
          SELECT r.ONLINE_SALES_ID AS user_id
          FROM ROLE_USER_REGION_CONFIG r
          WHERE r.ONLINE_SALES_ID IS NOT NULL
          
          UNION
          
          -- 线下销售
          SELECT r.OFFLINE_SALES_ID AS user_id
          FROM ROLE_USER_REGION_CONFIG r
          WHERE r.OFFLINE_SALES_ID IS NOT NULL
          
          UNION
          
          -- 产线人员
          SELECT r.PRODUCTION_USER_ID AS user_id
          FROM ROLE_USER_REGION_CONFIG r
          WHERE r.PRODUCTION_USER_ID IS NOT NULL
        ) t
        LEFT JOIN T_USER_DETAIL d ON t.user_id = d.USER_ID
        LEFT JOIN T_USER TU ON TU.USER_ID = d.USER_ID
        <where>
          <if test="userName != null and userName != ''">
            AND TU.USERNAME LIKE CONCAT('%', #{userName}, '%')
          </if>
        </where>
        ORDER BY t.user_id
        LIMIT 100
    </select>
</mapper>