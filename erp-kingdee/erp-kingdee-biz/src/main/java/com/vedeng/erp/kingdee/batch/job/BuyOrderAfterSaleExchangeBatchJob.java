package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.BatchHandleGiftBuyOrderWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchPurchaseExchangeInWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchPurchaseExchangeOutWriter;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单售后换货job
 * @date 2022/10/25 16:00
 * 7采购换货出库 8采购换货入库
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderAfterSaleExchangeBatchJob extends BaseJob {



    @Autowired
    private BatchPurchaseExchangeInProcessor purchaseExchangeInProcessor;
    @Autowired
    private BatchPurchaseExchangeInWriter batchPurchaseExchangeInWriter;

    @Autowired
    private BatchPurchaseExchangeOutProcessor batchPurchaseExchangeOutProcessor;
    @Autowired
    private BatchPurchaseExchangeOutWriter batchPurchaseExchangeOutWriter;

    @Autowired
    private BatchBuyOrderExchangeAcceptanceFormProcessor buyOrderExchangeAcceptanceFormProcessor;

    @Autowired
    private BaseProcessListener baseProcessListener;
    @Autowired
    private BaseReadListener baseReadListener;
    @Autowired
    private BaseWriteListener baseWriteListener;
    @Autowired
    private BatchHandleGiftBuyOrderProcessor batchHandleGiftBuyOrderProcessor;

    @Autowired
    private BatchHandleGiftBuyOrderWriter batchHandleGiftBuyOrderWriter;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    @Autowired
    private BatchBuyOrderExchangeOutFormProcessor batchBuyOrderExchangeOutFormProcessor;

    public Job buyOrderAfterSaleExchangeFlowJob() {
        return jobBuilderFactory.get("buyOrderAfterSaleExchangeFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(exchangeInBill())
                .next(handleGiftBuyOrder())
                .next(acceptanceForm())
                .next(exchangeOutBill())
                .next(acceptanceOutForm())
                .build();
    }


    /**
     * 采购售后换货入库
     */
    private Step exchangeInBill() {
        return stepBuilderFactory.get("采购售后换货入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleExchangeInItemReader(null,null))
                .processor(purchaseExchangeInProcessor)
                .writer(batchPurchaseExchangeInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 采购售后换货出库
     */
    private Step exchangeOutBill() {
        return stepBuilderFactory.get("采购售后换货出库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleExchangeOutItemReader(null,null))
                .processor(batchPurchaseExchangeOutProcessor)
                .writer(batchPurchaseExchangeOutWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step acceptanceForm() {
        return stepBuilderFactory.get("采购换货入库单附件推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleExchangeInItemReader(null,null))
                .processor(buyOrderExchangeAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step acceptanceOutForm() {
        return stepBuilderFactory.get("采购换货出库单附件推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleExchangeOutItemReader(null,null))
                .processor(batchBuyOrderExchangeOutFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     *  单独处理
     *  采购售后退货出库(舍弃,已单独处理 giftBuyOrderAfterSale)、采购售后换货出库、采购售后换货入库
     *  关联的原始采购单为'赠品'的出入库数据
     *  对接金蝶的其他入库单
     */
    private Step handleGiftBuyOrder(){
        return stepBuilderFactory.get("handleGiftBuyOrder")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchHandleGiftBuyOrderReader(null,null))
                .processor(batchHandleGiftBuyOrderProcessor)
                .writer(batchHandleGiftBuyOrderWriter)
                .listener(baseProcessListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAfterSaleExchangeOutItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,7);
    }



    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchAfterSaleExchangeInItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime,8);
    }
//
//    @Bean
//    @Scope("prototype")
//    public ItemReader<BatchAttachmentDto> purchaseInAcceptanceFormReadService() {
//        return new PurchaseInAcceptanceFormRead();
//    }


    private CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(
            String beginTime, String endTime, Integer outInType) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInType(outInType)
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), warehouseGoodsOutInDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> batchHandleGiftBuyOrderReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                // 6采购退货出库(排除) 7采购换货出库 8采购换货入库
                .outInTypeList(CollUtil.newArrayList(7,8))
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()): DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()): DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(),"handleGiftBuyOrderFindAll",warehouseGoodsOutInDto);
    }
}

