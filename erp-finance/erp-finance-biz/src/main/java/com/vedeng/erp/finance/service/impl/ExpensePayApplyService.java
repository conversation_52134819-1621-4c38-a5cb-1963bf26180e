package com.vedeng.erp.finance.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.dto.BuyOrderInfoDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.service.AbstractPayApply;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 采购费用单
 */
@Slf4j
@Service
public class ExpensePayApplyService extends AbstractPayApply {

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private BuyorderApiService buyorderApiService;

    @Value("${mairui_trade_id}")
    private String maiRuiTradeId;

    @Override
    public void createBillBaseCheckValid(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        BuyorderExpenseDto dto = buyorderExpenseApiService.getOrderMainData(payApplyCreateBillDto.getRelatedId());
        if (Objects.isNull(dto)){
            log.info("未找到对应的采购费用单：{}", payApplyCreateBillDto.getRelatedId());
            throw new ServiceException("未找到对应的采购费用单");
        }
        //1. 单据进行中：归属业务单据的单据状态 = 进行中 、已完结
        if (dto.getStatus() != 1 && dto.getStatus() != 2){
            throw new ServiceException("归属业务单据的单据状态不是进行中、已完结，当前状态：" +dto.getStatus());
        }
        //2. 单据已生效：归属业务单据的生效状态 = 已生效
        if (dto.getValidStatus() != 1){
            throw new ServiceException("归属业务单据的生效状态不是已生效，当前状态：" +dto.getValidStatus());
        }
        //3. 单据未完成付款：归属业务单据的付款状态 ！= 全部付款
//        if (dto.getPaymentStatus() == 2){
//            throw new ServiceException("归属业务单据的付款状态是全部付款" );
//        }
        log.info("createBillBaseCheckValid校验完成");
    }

    /**
     * 业务检查
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    @Override
    public void createBillBusinessCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        super.businessCheck(payApplyCreateBillDto);
        BuyorderExpenseDto dto = buyorderExpenseApiService.getOrderMainData(payApplyCreateBillDto.getRelatedId());
        payApplyCreateBillDto.setTraderId(dto.getBuyorderExpenseDetailDto().getTraderId());
        if (Objects.equals(dto.getOrderType(),0)){
            // 直属订单
            BuyOrderInfoDto bDto = buyorderApiService.getBuyOrderInfo(dto.getBuyorderId());
            log.info("直属订单ID{},{}", payApplyCreateBillDto.getRelatedId(), JSONObject.toJSON(bDto));
            // 未查到对应直属订单
            if (Objects.isNull(bDto)){
                throw new ServiceException("未找到对应的直属订单");
            }
            // 关联采购单生效状态：未生效
            if (bDto.getValidStatus() != 1){
                throw new ServiceException("直属订单，归属业务单据的生效状态不是已生效，当前状态：" +bDto.getValidStatus());
            }
        }
    }

    @Override
    public void otherProcess(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
    }

    @Override
    public Integer choseBank(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException {
        log.info("选择银行：{}", JSON.toJSON(payApplyCreateBillDto));
        boolean withinWorkingHours = payRuleCheck();
        List<String> maiRuilist = Arrays.asList(maiRuiTradeId.split(","));
        boolean contains = maiRuilist.contains(String.valueOf(payApplyCreateBillDto.getTraderId()));
        // 民生
        Integer payVedengBankId = ErpConstant.SEVEN;
        if (withinWorkingHours && contains){
            // 周一到周五，采购付迈瑞款：付款银行 = 交通银行
            payVedengBankId = ErpConstant.THREE;
        }
        log.info("选择银行{},withinWorkingHours:{},contains:{}",payVedengBankId,withinWorkingHours,contains);
        return payVedengBankId;
    }
}
