.vd-ui-input {
  display: inline-block;
  vertical-align: top;
  position: relative;
}
.vd-ui-input .vd-ui-input__icon {
  font-size: 16px;
  color: #666666;
  position: relative;
  top: 2.5px;
  margin: 0 5px;
}
.vd-ui-input .vd-ui-input__icon::after {
  content: "";
  height: 100%;
  width: 0;
  display: inline-block;
  vertical-align: middle;
}
.vd-ui-input .icon-type {
  position: absolute;
  font-size: 16px;
  margin-left: 10px;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
}
.vd-ui-input .vd-ui-input__inner {
  color: #333333;
  background-color: #ffffff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  height: 30px;
  padding: 0px 10px;
  font-size: 12px;
  width: 100%;
  transition: border-color 0.1s linear;
}
.vd-ui-input .vd-ui-input__inner::placeholder {
  color: #999999;
  font-size: 12px;
}
.vd-ui-input .vd-ui-input__inner:hover {
  border-color: #969B9E;
}
.vd-ui-input.vd-ui-input--error > .vd-ui-input__inner {
  border-color: #e64545;
}
.vd-ui-input .vd-ui-input-text {
  color: #999;
  margin-top: 5px;
  position: absolute;
}
.vd-ui-input.vd-ui-input--large .vd-ui-input__inner {
  height: 42px;
  padding: 0px 10px;
  font-size: 16px;
}
.vd-ui-input.vd-ui-input--small .vd-ui-input__inner {
  height: 26px;
  padding: 0px 10px;
  font-size: 12px;
}
.vd-ui-input.is-disabled .vd-ui-input__inner {
  background-color: #F5F7FA;
  border-color: #D7DADE;
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-input.is-focus .vd-ui-input__inner {
  border-color: #0099FF;
}
.vd-ui-input.vd-ui-input-error .vd-ui-input__inner {
  border-color: #E64545;
}
.vd-ui-input.vd-ui-input--prefix .vd-ui-input__inner {
  padding-left: 36px;
}
.vd-ui-input.vd-ui-input--prefix .vd-ui-input__prefix {
  position: absolute;
  top: 0;
  left: 5px;
  max-height: 35px;
  height: 100%;
  pointer-events: none;
}
.vd-ui-input.vd-ui-input--suffix .vd-ui-input__inner {
  padding-right: 36px;
}
.vd-ui-input.vd-ui-input--suffix .vd-ui-input__suffix {
  position: absolute;
  top: 0;
  right: 5px;
  max-height: 35px;
  height: 100%;
  pointer-events: none;
}
.vd-ui-input.vd-ui-input--suffix .vd-ui-input__suffix .vd-ui-input__suffix-inner .vd-ui-input__icon {
  pointer-events: all;
  cursor: pointer;
}
.vd-ui-input.vd-ui-input-group {
  display: inline-table;
}
.vd-ui-input.vd-ui-input-group .vd-ui-input__inner {
  flex: 1;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--prepend .vd-ui-input-group__prepend {
  background-color: #F5F7FA;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #BABFC2;
  border-radius: 4px;
  padding: 0 10px;
  white-space: nowrap;
  border-right: 0;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  text-align: center;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--prepend .vd-ui-input__inner {
  display: table-cell;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--append .vd-ui-input-group__append {
  background-color: #F5F7FA;
  vertical-align: middle;
  display: table-cell;
  position: relative;
  border: 1px solid #BABFC2;
  border-radius: 4px;
  padding: 0 10px;
  white-space: nowrap;
  border-left: 0;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  text-align: center;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--append .vd-ui-input-group__append .icon {
  font-size: 16px;
  margin: 0 5px;
  position: relative;
  top: 1px;
}
.vd-ui-input.vd-ui-input-group.vd-ui-input-group--append .vd-ui-input__inner {
  display: table-cell;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.vd-ui-input .vd-ui-input--error {
  color: #E64545;
}
.vd-ui-input .vd-ui-input--error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  margin-top: 1px;
}
.vd-ui-input .vd-ui-input--error .vd-ui-input-error--errmsg {
  margin: 0px;
}
.vd-ui-textarea {
  position: relative;
  display: inline-block;
}
.vd-ui-textarea .vd-ui-textarea__inner {
  width: 100%;
  min-height: 30px;
  height: 30px;
  font-size: 12px;
  display: block;
  resize: vertical;
  color: #333333;
  background-color: #ffffff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  padding: 5px 10px;
  transition: border-color 0.1s linear;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-textarea .vd-ui-textarea__inner::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-textarea .vd-ui-textarea__inner::placeholder {
  color: #999999;
  font-size: 12px;
}
.vd-ui-textarea .vd-ui-textarea__inner:hover {
  border-color: #969B9E;
}
.vd-ui-textarea .vd-ui-textarea__inner:focus {
  border-color: #0099FF;
}
.vd-ui-textarea.is-disabled .vd-ui-textarea__inner {
  background-color: #F5F7FA;
  border-color: #D7DADE;
  color: #999999;
  cursor: not-allowed;
}
.vd-ui-textarea .vd-ui-textarea-place {
  position: relative;
  display: inline-block;
  vertical-align: top;
}
.vd-ui-textarea .vd-ui-textarea-place .vd-ui-input__count {
  color: #999999;
  position: absolute;
  bottom: 0;
  left: calc(100% + 5px);
  text-align: right;
  white-space: nowrap;
}
.vd-ui-textarea .vd-ui-textarea-place .vd-ui-input__count.upper-limit {
  color: #FF6600;
}
.vd-ui-number-range {
  display: flex;
  align-items: center;
}
.vd-ui-number-range .range-gap {
  margin: 0 5px 0 4px;
}
.vd-ui-number-input {
  display: flex;
  position: relative;
}
.vd-ui-number-input .vd-ui-button {
  width: 20px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  line-height: 1;
  color: #999;
  user-select: none;
}
.vd-ui-number-input .vd-ui-button.left {
  border-radius: 3px 0 0 3px;
}
.vd-ui-number-input .vd-ui-button.right {
  border-radius: 0 3px 3px 0;
}
.vd-ui-number-input .vd-ui-input {
  flex: 1;
  margin: 0 -1px;
  position: relative;
  z-index: 1;
}
.vd-ui-number-input .vd-ui-input .vd-ui-input__inner {
  border-radius: 0;
  text-align: center;
  padding: 0 5px;
}
.vd-ui-number-input .bubble-tip-wrap {
  bottom: 32px;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  z-index: -1;
  pointer-events: none;
  transition: opacity 0.22s ease;
}
.vd-ui-number-input .bubble-tip-wrap.show {
  opacity: 1;
  z-index: 11;
  pointer-events: all;
}
