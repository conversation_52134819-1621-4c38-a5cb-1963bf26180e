<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<meta charset="UTF-8">
<div id="app" style="display: none;">
    <vxe-grid ref='xGrid' v-bind="gridOptions" @checkbox-change="checkboxChangeEvent"
              @checkbox-all="checkboxChangeEvent">
        <%--    表单搜索    --%>

        <template #form_invoice_red_confirmation_no="{ data }">
            <vxe-input v-model="data.invoiceRedConfirmationNo" maxlength="50" placeholder="请输入" size="mini" clearable></vxe-input>
        </template>

        <template #form_business_order_no="{ data }">
            <vxe-input v-model="data.businessOrderNo" maxlength="50" placeholder="请输入" size="mini" clearable></vxe-input>
        </template>
        <template #form_after_sale_business_order_no="{ data }">
            <vxe-input v-model="data.afterSaleBusinessOrderNo" maxlength="50" size="mini" placeholder="请输入" clearable></vxe-input>
        </template>


        <template #form_buyer="{ data }">
            <vxe-input v-model="data.buyer" maxlength="50" placeholder="请输入" size="mini" clearable></vxe-input>
        </template>

        <template #form_taxes_red_confirmation_status="{ data }">
            <el-select v-model="data.taxesRedConfirmationStatus" filterable placeholder="请选择" size="mini"
                       prop="taxesRedConfirmationStatus"
                       style="width: 100%;">
                <el-option
                        v-for="item in confirmationStatusOptions"
                        :key="item.label"
                        :label="item.value"
                        :value="item.label">
                </el-option>
            </el-select>
        </template>

        <template #form_blue_invoice_no="{ data }">
            <vxe-input v-model="data.blueInvoiceNo" maxlength="50" placeholder="请输入" size="mini" clearable></vxe-input>
        </template>


        <template #form_red_invoice_no="{ data }">
            <vxe-input v-model="data.redInvoiceNo" maxlength="50" placeholder="请输入" size="mini" clearable></vxe-input>
        </template>

        <template #form_apply_reason="{ data }">
            <el-select v-model="data.applyReason" filterable placeholder="请选择" prop="applyReason"
                       style="width: 100%;" size="mini">
                <el-option
                        v-for="item in applyReasonOptions"
                        :key="item.label"
                        :label="item.value"
                        :value="item.label">
                </el-option>
            </el-select>
        </template>

        <template #form_red_confirmation_scope="{ data }">
            <el-select v-model="data.redConfirmationScope" filterable placeholder="请选择" prop="redConfirmationScope"
                       style="width: 100%;" size="mini">
                <el-option
                        v-for="item in redConfirmationScopeOptions"
                        :key="item.label"
                        :label="item.value"
                        :value="item.label">
                </el-option>
            </el-select>
        </template>

        <template #form_apply_time="{ data }">
            <el-date-picker
                            style="width: 100%;"
                            v-model="data.applyTimes"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            :default-time="['00:00:00', '23:59:59']"
                            :picker-options="pickerOptions" size="mini">
            </el-date-picker>
        </template>

        <template #form_remaining_time_of_void="{ data }">
            <el-select v-model="data.remainingTimeOfVoid" filterable placeholder="请选择" prop="remainingTimeOfVoid" size="mini"
                       style="width: 100%;">
                <el-option
                        v-for="item in remainingTimeOfVoidOptions"
                        :key="item.label"
                        :label="item.value"
                        :value="item.label">
                </el-option>
            </el-select>
        </template>
        <template #form_input_person_type="{ data }">
            <el-select v-model="data.inputPersonType" filterable placeholder="请选择" prop="inputPersonType" size="mini"
                       style="width: 100%;">
                <el-option
                        v-for="item in inputPersonTypeOptions"
                        :key="item.label"
                        :label="item.value"
                        :value="item.label">
                </el-option>
            </el-select>
        </template>
        <%--    工具栏--%>
        <template #toolbar_buttons>

            <el-tabs v-model="activeName" style="margin-left: 10px" @tab-click="changePane">
                <el-tab-pane name="pane-all">
                    <template slot="label">
                        <el-badge>
                            <span>全部</span>
                        </el-badge>

                    </template>
                </el-tab-pane>
                <el-tab-pane name="pane-init">
                    <template slot="label">
                        <template v-if="pane_init_num!=0">
                            <el-badge :value="pane_init_num" :max="99" class="badge-item">
                                初始化
                            </el-badge>
                        </template>
                        <template v-else>
                            已申请
                        </template>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="pane-applied">
                    <template slot="label">
                        <template v-if="pane_applied_num!=0">
                            <el-badge :value="pane_applied_num" :max="99" class="badge-item">
                                已申请
                            </el-badge>
                        </template>
                        <template v-else>
                            已申请
                        </template>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="pane-confirmed">
                    <template slot="label">
                        <template v-if="pane_confirmed_num!=0">
                            <el-badge :value="pane_confirmed_num" :max="99" class="badge-item">
                                已确认
                            </el-badge>
                        </template>
                        <template v-else>
                                已确认
                        </template>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="pane-invoiced">
                    <template slot="label">
                        <el-badge>已开票</el-badge>
                    </template>
                </el-tab-pane>
                <el-tab-pane name="pane-voided">
                    <template slot="label">
                        <el-badge>已作废</el-badge>
                    </template>
                </el-tab-pane>
            </el-tabs>

        </template>

        <%--     表格数据   --%>
        <template #invoice_red_confirmation_no="{ row }">
            <el-link type="primary"  @click="viewDetail(row)">
                {{row.invoiceRedConfirmationNo}}
            </el-link>
        </template>

        <template #business_order_no="{ row }">
            <span v-if="row.businessOrderId == 0">{{row.businessOrderNo}}</span>
            <el-link type="primary" v-if="row.businessOrderId != 0" @click="viewOrderDetail(row)">
                {{row.businessOrderNo}}
            </el-link>
        </template>
        <template #aftersale_business_order_no="{ row }">
            <span v-if="row.afterSaleBusinessOrderId == 0">{{row.afterSaleBusinessOrderNo}}</span>
            <el-link type="primary" v-if="row.afterSaleBusinessOrderId != 0" @click="viewAfterSalesDetail(row)">
                {{row.afterSaleBusinessOrderNo}}
            </el-link>
        </template>

        <template #aftersale_business_order_type="{ row }">
                {{row.afterSaleTitle}}
        </template>
            <template #remaining_time="{ row }">
                <span v-if="row.applyTime!=null">{{formatRemainingTime(row.remainingTime)}}</span>
        </template>

        <template #red_confirmation_status="{ row }">
            <span v-if="row.redConfirmationStatus==0">初始化</span>
            <span v-if="row.redConfirmationStatus==1">已申请</span>
            <span v-if="row.redConfirmationStatus==2">已确认</span>
            <span v-if="row.redConfirmationStatus==3">已开票</span>
            <span v-if="row.redConfirmationStatus==4">已作废</span>
        </template>

        <template #taxes_red_confirmation_status="{ row }">
            <span v-if="row.taxesRedConfirmationStatus=='01'">无需确认</span>
            <span v-if="row.taxesRedConfirmationStatus=='02'">销方录入待购方确认</span>
            <span v-if="row.taxesRedConfirmationStatus=='03'">购方录入待销方确认</span>
            <span v-if="row.taxesRedConfirmationStatus=='04'">购销双方已确认</span>
            <span v-if="row.taxesRedConfirmationStatus=='05'">作废（销方录入购方否认）</span>
            <span v-if="row.taxesRedConfirmationStatus=='06'">作废（购方录入销方否认）</span>
            <span v-if="row.taxesRedConfirmationStatus=='07'">作废（超 72 小时未确认）</span>
            <span v-if="row.taxesRedConfirmationStatus=='08'">作废（发起方已撤销）</span>
            <span v-if="row.taxesRedConfirmationStatus=='09'">作废（确认后撤销）</span>
        </template>
        <template #apply_reason="{ row }">
            <span v-if="row.applyReason=='01'">开票有误</span>
            <span v-if="row.applyReason=='02'">销货退回</span>
            <span v-if="row.applyReason=='03'">服务中止</span>
            <span v-if="row.applyReason=='04'">销售折让</span>
        </template>

        <template #red_confirmation_scope="{ row }">
            <span v-if="row.redConfirmationScope==0">全部红冲</span>
            <span v-if="row.redConfirmationScope==1">部分红冲</span>
        </template>

        <template #seller_buyer_type="{ row }">
            <span v-if="row.sellerBuyerType==0">销售方</span>
            <span v-if="row.sellerBuyerType==1">购买方</span>
        </template>
        <template #input_person_type="{ row }">
            <span v-if="row.inputPersonType==0">销方</span>
            <span v-if="row.inputPersonType==1">购方</span>
        </template>
        <template #confirm_person_type="{ row }">
            <span v-if="row.confirmPersonType==0">销方</span>
            <span v-if="row.confirmPersonType==1">购方</span>
        </template>
        <template #enter_account_status="{ row }">
            <span v-if="row.enterAccountStatus=='00'">未入账</span>
            <span v-if="row.enterAccountStatus=='01'">已入账</span>
        </template>
        <template #value_added_tax_status="{ row }">
            <span v-if="row.valueAddedTaxStatus=='00'">已勾选未确认</span>
            <span v-if="row.valueAddedTaxStatus=='01'">已确认</span>
            <span v-if="row.valueAddedTaxStatus=='03'">未勾选</span>
        </template>

        <template #operate="{ row }">
            <template v-for="item in row.buttonList">
                <template v-if="item.show">
                    <template v-if="!item.needConfirm">
                        <vxe-button  @click="doButton(row,item)" size="small" type="text" status="primary" >{{item.buttonName}}</vxe-button>
                    </template>
                    <template v-if="item.needConfirm">
                        <vxe-button  @click="confirmDoButton(row,item)" size="small" type="text" status="primary">{{item.buttonName}}</vxe-button>

                    </template>

                </template>

            </template>
        </template>



        <template #pager_left>
            <span class="page-left">
              <%--<vxe-checkbox v-model="isAllChecked" :indeterminate="isIndeterminate"--%>
              <%--              @change="changeAllEvent"></vxe-checkbox>--%>
              <span class="select-count">已选中 {{ selectRecords.length }} 条</span>
              <vxe-button @click="beforeBatchApply">申请</vxe-button>
              <vxe-button @click="batchAgree">同意</vxe-button>
              <vxe-button @click="batchReject">拒绝</vxe-button>
              <vxe-button @click="batchInvoice">开票</vxe-button>
              <vxe-button @click="batchCancel">撤销</vxe-button>
              <vxe-button @click="batchInvalid">作废</vxe-button>

            </span>
        </template>

    </vxe-grid>

    <el-dialog title="操作失败信息" :visible.sync="errorTable">
        <el-table :data="errorTableData" border>
            <el-table-column property="invoiceRedConfirmationId" label="ID" width="150"></el-table-column>
            <el-table-column property="failReason" label="失败原因"></el-table-column>
        </el-table>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="allReversalShow" width="30%">
        批量申请，只支持全部红冲，确认申请？<br>
        <el-checkbox v-model="allReversal"><span style="color: red">全部红冲</span></el-checkbox>

        <span slot="footer" class="dialog-footer">
            <el-button :disabled="!allReversal" :loading="allReversalLoading" type="primary" @click="allReversalShowTrue">确 定</el-button>
            <el-button @click="allReversalShowFalse">取 消</el-button>
        </span>

    </el-dialog>



</div>
<script src="${pageContext.request.contextPath}/static/api/invoiceredconfirmation/red_confirm_list.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/user.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/js/moment/moment.js?rnd=${resourceVersionKey}"></script>


<script type="text/javascript">

    let vm0 = null;
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    let afterSaleBusinessOrderNo_view='${viewData.afterSaleBusinessOrderNo}'
    new Vue({
        el: '#app',

        data() {

            return {
                globalUpdateLoading:false,
                allReversalLoading: false,
                allReversalShow:false,
                allReversal:false,
                activeName: 'pane-all',
                loading: false,
                errorTable:false,
                errorTableData: [],
                confirmationStatusOptions: [
                    {label: '00', value: '全部'},
                    {label: '01', value: '无需确认'},
                    {label: '02', value: '销方录入待购方确认'},
                    {label: '03', value: '购方录入待销方确认'},
                    {label: '04', value: '购销双方已确认'},
                    {label: '05', value: '作废（销方录入购方否认）'},
                    {label: '06', value: '作废（购方录入销方否认）'},
                    {label: '07', value: '作废（超 72 小时未确认）'},
                    {label: '08', value: '作废（发起方已撤销）'},
                    {label: '09', value: '作废（确认后撤销）'}
                ],
                applyReasonOptions: [
                    {label: '00', value: '全部'},
                    {label: '01', value: '开票有误'},
                    {label: '02', value: '销货退回'},
                    {label: '03', value: '服务中止'},
                    {label: '04', value: '销售折让'}
                ],
                redConfirmationScopeOptions: [
                    {label: '-1', value: '全部'},
                    {label: '0', value: '全部红冲'},
                    {label: '1', value: '部分红冲'},
                ],
                remainingTimeOfVoidOptions: [
                    {label: '0', value: '全部'},
                    {label: '1', value: '0-1'},
                    {label: '2', value: '1-2'},
                    {label: '3', value: '2-3'},
                ],
                inputPersonTypeOptions: [
                    {label: '-1', value: '全部'},
                    {label: '0', value: '销方'},
                    {label: '1', value: '购方'},
                ],
                currentRow: {},
                isAllChecked: false,
                isIndeterminate: false,
                selectRecords: [],
                pane_init_num:0,
                pane_applied_num:0,
                pane_confirmed_num:0,
                pickerOptions: {
                    shortcuts: [{
                        text: '今天',
                        onClick(picker) {
                            let end = new Date();
                            end.setHours(23, 59, 59, 999);
                            let start = new Date();
                            start.setHours(0, 0, 0, 0);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '昨天',
                        onClick(picker) {
                            let end = new Date();
                            end.setHours(23, 59, 59, 999);
                            let start = new Date();
                            start.setHours(0, 0, 0, 0);
                            start.setTime(start.getTime() - 3600 * 1000 * 24);
                            end.setTime(end.getTime() - 3600 * 1000 * 24);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近7天',
                        onClick(picker) {
                            let end = new Date();
                            end.setHours(23, 59, 59, 999);
                            let start = new Date();
                            start.setHours(0, 0, 0, 0);
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 6);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '最近30天',
                        onClick(picker) {
                            let end = new Date();
                            end.setHours(23, 59, 59, 999);
                            let start = new Date();
                            start.setHours(0, 0, 0, 0);
                            start.setTime(start.getTime() - 3600 * 1000 * 24 * 29);
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '这个月',
                        onClick(picker) {
                            debugger
                            const start = moment().startOf('month').format('YYYY-MM-DD HH:mm:ss');
                            const end = moment().endOf('month').format('YYYY-MM-DD HH:mm:ss');
                            picker.$emit('pick', [start, end]);
                        }
                    }, {
                        text: '上个月',
                        onClick(picker) {
                            debugger
                            const start = moment().subtract(1, 'month').startOf('month').format('YYYY-MM-DD HH:mm:ss');
                            const end = moment().subtract(1, 'month').endOf('month').format('YYYY-MM-DD HH:mm:ss');
                            picker.$emit('pick', [start, end]);
                        }
                    }]
                },
                gridOptions: {
                    height: 800,
                    size:'mini',
                    align: 'center',
                    border: true,
                    showHeaderOverflow: true,
                    showOverflow: true,
                    keepSource: true,
                    id: 'red_confirm_list',
                    rowId: 'id',
                    rowConfig: {
                        isHover: true
                    },
                    columnConfig: {
                        resizable: true
                    },
                    customConfig: {
                        storage: true,
                    },
                    pagerConfig: {
                        pageSize: 10,
                        pageSizes: [10, 20, 50, 100, 200],
                        slots: {left: 'pager_left'}
                    },

                    // 列表搜索区域
                    formConfig: {
                        size:'mini',
                        titleWidth: 150,
                        titleAlign: 'right',
                        items: [
                            {
                                field: 'invoiceRedConfirmationNo',
                                title: '单据编号',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_invoice_red_confirmation_no'}
                            },
                            {
                                field: 'businessOrderNo',
                                title: '销售单号',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_business_order_no'}
                            },
                            {
                                field: 'afterSaleBusinessOrderNo',
                                title: '售后单号',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_after_sale_business_order_no'}
                            },
                            {
                                field: 'buyer',
                                title: '购方名称',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_buyer'}
                            },
                            {
                                field: 'taxesRedConfirmationStatus',
                                title: '红字确认信息状态',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_taxes_red_confirmation_status'}
                            },
                            {
                                field: 'blueInvoiceNo',
                                title: '蓝票号码',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_blue_invoice_no'}
                            },
                            {
                                field: 'redInvoiceNo',
                                title: '红票号码',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_red_invoice_no'}
                            },
                            {
                                field: 'applyReason',
                                title: '红字申请原因',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_apply_reason'}
                            },

                            {
                                field: 'redConfirmationScope',
                                title: '红冲范围',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_red_confirmation_scope'}
                            },
                            {
                                field: 'applyTimes',
                                title: '红票申请日期',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_apply_time'}
                            }, {
                                field: 'remainingTimeOfVoid',
                                title: '作废剩余时间',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_remaining_time_of_void'}
                            },
                            {
                                field: 'inputPersonType',
                                title: '申请方',
                                span: 6,
                                itemRender: {},
                                folding: true,
                                slots: {default: 'form_input_person_type'}
                            },
                            {
                                span: 24,
                                align: 'center',
                                collapseNode: true,
                                itemRender: {
                                    name: '$buttons',
                                    children: [
                                        {
                                            props: {
                                                type: 'submit',
                                                content: '搜索',
                                                status: 'primary'
                                            }
                                        },
                                        {
                                            props: {
                                                type: 'reset',
                                                content: '重置'
                                            }
                                        }
                                        // ,
                                        // {
                                        //     props: {
                                        //         type: 'button',
                                        //         content: '更新',
                                        //         loading:this.globalUpdateLoading
                                        //
                                        //     },
                                        //     events:{
                                        //         click: () => {
                                        //             // 执行取消操作
                                        //             this.globalUpdate();
                                        //         }
                                        //     }
                                        // }
                                    ]
                                }
                            }
                        ]
                    },

                    toolbarConfig: {
                        slots: {buttons: 'toolbar_buttons'},
                        custom: true
                    },
                    proxyConfig: {
                        seq: false, //启用动态序号代理，每一页的序号会根据当前页数变化
                        form: true, //启用表单代理，当点击表单提交按钮时会自动触发 reload 行为
                        props: {
                            result: 'list', // 配置响应结果列表字段
                            total: 'total' // 配置响应结果总页数字段
                        },
                        // 只接收Promise，具体实现自由发挥
                        ajax: {
                            // 当点击工具栏查询按钮或者手动提交指令 query或reload 时会被触发
                            query: ({page, sorts, filters, form}) => {
                                let that = this;
                                const queryParams = Object.assign({}, form);

                                // 清空选中行
                                this.selectRecords = [];
                                if (queryParams.applyTimes != null && queryParams.applyTimes.length > 0) {
                                    queryParams.beginTime = queryParams.applyTimes[0]
                                    queryParams.endTime = queryParams.applyTimes[1]
                                }
                                if (that.activeName == 'pane-all') {
                                    queryParams.redConfirmationStatus = null;
                                }
                                if (that.activeName == 'pane-init') {
                                    queryParams.redConfirmationStatus = 0;
                                }
                                if (that.activeName == 'pane-applied') {
                                    queryParams.redConfirmationStatus = 1;
                                }
                                if (that.activeName == 'pane-confirmed') {
                                    queryParams.redConfirmationStatus = 2;
                                }
                                if (that.activeName == 'pane-invoiced') {
                                    queryParams.redConfirmationStatus = 3;
                                }
                                if (that.activeName == 'pane-voided') {
                                    queryParams.redConfirmationStatus = 4;
                                }
                                let pageParams = {
                                    pageNum: page.currentPage,
                                    pageSize: page.pageSize,
                                    param: queryParams
                                };
                                return axios({
                                    url: '/redConfirm/api/page.do',
                                    method: 'post',
                                    data: pageParams
                                }).then(response => {
                                    return response.data.data
                                });
                            }
                        }
                    },
                    columns: [
                        {type: 'checkbox', title: '',fixed: "left", width: 50},
                        {
                            field: 'invoiceRedConfirmationId',
                            title: 'ID',
                            // width: 250,
                            // slots: {default: 'trader_name'},
                        },
                        {
                            field: 'invoiceRedConfirmationNo',
                            title: '单据编号',
                            width: 180,
                            slots: {default: 'invoice_red_confirmation_no'},
                            visible: true
                        },
                        {
                            field: 'businessOrderNo',
                            title: '销售单号',
                            width: 200,
                            slots: {default: 'business_order_no'},
                            visible: true
                        },
                        {
                            field: 'afterSaleBusinessOrderNo',
                            title: '售后单号',
                            width: 200,
                            slots: {default: 'aftersale_business_order_no'},
                            visible: true
                        },
                        {
                            title: '售后单类型',
                            width: 100,
                            slots: {default: 'aftersale_business_order_type'},
                            visible: true
                        },
                        {
                            field: 'buyer',
                            title: '购方名称',
                            width: 300,
                            visible: true
                        },
                        {
                            field: 'remainingTime',
                            title: '作废剩余时间',
                            width: 110,
                            // formatter: this.formatRemainingTime,
                            slots: {default: 'remaining_time'},
                            visible: true
                        },
                        {
                            field: 'redConfirmationStatus',
                            title: '单据状态',
                            width: 100,
                            slots: {default: 'red_confirmation_status'},
                            visible: true
                        },
                        {
                            field: 'taxesRedConfirmationStatus',
                            title: '红字确认信息状态',
                            width: 200,
                            slots: {default: 'taxes_red_confirmation_status'},
                            visible: true
                        },
                        {
                            field: 'applyAmount',
                            title: '红字申请金额',
                            width: 100,
                            visible: true
                        },
                        {
                            field: 'applyTaxAmount',
                            title: '红字申请税额',
                            width: 100,
                            visible: true
                        },
                        {
                            field: 'applyPricePlusTaxes',
                            title: '红字申请价税合计',
                            width: 130,
                            visible: true
                        },
                        {
                            field: 'applyReason',
                            title: '红字申请原因',
                            slots: {default: 'apply_reason'},
                            width: 100,
                            visible: true
                        },
                        {
                            field: 'redConfirmationScope',
                            title: '红冲范围',
                            slots: {default: 'red_confirmation_scope'},
                            width: 90,
                            visible: true
                        },
                        {
                            field: 'sellerBuyerType',
                            title: '购销方',
                            slots: {default: 'seller_buyer_type'},
                            width: 70,
                            visible: true
                        },

                        {
                            field: 'inputPersonType',
                            title: '申请方',
                            width: 70,
                            slots: {default: 'input_person_type'},
                            visible: true
                        },
                        {
                            field: 'confirmPersonType',
                            title: '确认方',
                            width: 70,
                            slots: {default: 'confirm_person_type'},
                            visible: true
                        },
                        {
                            field: 'redInvoiceNo',
                            title: '红票号码',
                            width: 180,
                            visible: true
                        },
                        {
                            field: 'blueInvoiceNo',
                            title: '蓝票号码',
                            width: 180,
                            visible: true
                        },

                        {
                            field: 'surplusAmount',
                            title: '剩余可红冲金额',
                            width: 150,
                            visible: true
                        },
                        {
                            field: 'surplusUaxAmount',
                            title: '剩余可红冲税额',
                            width: 150,
                            visible: true
                        },
                        {
                            field: 'surplusPricePlusTaxes',
                            title: '剩余可红冲价税合计',
                            width: 150,
                            visible: true
                        },
                        {
                            field: 'enterAccountStatus',
                            title: '蓝票入账状态',
                            width: 100,
                            slots: {default: 'enter_account_status'},
                            visible: true
                        },
                        {
                            field: 'valueAddedTaxStatus',
                            title: '蓝票确认状态',
                            width: 100,
                            slots: {default: 'value_added_tax_status'},
                            visible: true
                        },
                        {
                            field: 'applyTime',
                            title: '申请日期',
                            formatter: this.formatDate,
                            width: 160,
                            visible: true
                        },
                        {
                            field: 'confirmTime',
                            title: '确认日期',
                            formatter: this.formatDate,
                            width: 160,
                            visible: true
                        },
                        {
                            field: 'openRedInvoiceTime',
                            title: '开票日期',
                            formatter: this.formatDate,
                            width: 160,
                            visible: true
                        },
                        {
                            field: 'apiDataUpdateTime',
                            title: '更新时间',
                            formatter: this.formatDate,
                            width: 160,
                            visible: true
                        },

                        {title: '操作', width: 250, fixed: "right", slots: {default: 'operate'}}
                    ],
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    },
                    checkboxConfig: {
                        reserve: true,
                        highlight: true,
                        range: true
                    }
                },
            }
        },

        watch: {
        },

        created() {
            getSaleUserList().then(res => {
                this.belongerOptions = res.data.data;
            });
            this.getDict()
            sendThis0(this);
            this.getTypeNum();
        },

        mounted() {
            loadingApp()
            const $grid = this.$refs.xGrid
            if (afterSaleBusinessOrderNo_view != '' && afterSaleBusinessOrderNo_view != undefined) {
                $grid.formData.afterSaleBusinessOrderNo = afterSaleBusinessOrderNo_view;
            }
        },

        methods: {
            async getDict() {

                this.findSelectList()
            },
            changePane() {
                // this.changePaneFlag = true;
                debugger
                this.$refs.xGrid.tablePage.currentPage=1;
                this.reloadData();
                // this.changePaneFlag = false;
            },
            doButton(row,button) {
                let pageParams=[];
                pageParams.push(row.invoiceRedConfirmationId)
                if (button.viewType == 1) {
                    openModelParam( button.url, button.buttonName, "70%", "100%");
                }
                if (button.viewType == 0) {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    axios({
                        url: button.url,
                        method: 'post',
                        data: {invoiceRedConfirmationIds:pageParams}
                    }).then(res => {
                        loading.close();
                        if (!res.data.success) {
                            VXETable.modal.alert({ content:res.data.message, title:'操作失败信息', status: 'error' });
                        } else {
                            debugger
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                VXETable.modal.alert({ content:res.data.data[0].failReason, title:'操作失败信息', status: 'error' });
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                this.reloadData();
                            }

                        }

                    }).catch(res=>{
                        loading.close();
                    });
                }

            },
            async confirmDoButton(row, button) {
                const type = await VXETable.modal.confirm('确定'+button.buttonName+'吗？')
                if (type == 'confirm') {

                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    let pageParams=[];
                    pageParams.push(row.invoiceRedConfirmationId)
                    let params = {invoiceRedConfirmationIds:pageParams};
                    if (button.button == 1) {
                        params = {invoiceRedConfirmationIds:pageParams,status:'Y'};
                    }
                    if (button.button == 2) {
                        params = {invoiceRedConfirmationIds:pageParams,status:'N'};
                    }

                    axios({
                        url: button.url,
                        method: 'post',
                        data: params
                    }).then(res => {
                        loading.close();
                        if (res.data.data != undefined && res.data.data.length > 0) {
                            VXETable.modal.alert({ content:res.data.data[0].failReason, title:'操作失败信息', status: 'error' });
                        } else {
                            VXETable.modal.message({content: '操作成功', status: 'success'});
                            this.reloadData();
                        }

                    }).catch(res=>{
                        loading.close();
                    });
                }
            },
            getTypeNum() {
                let that = this;
                getTypeNum().then(res => {
                    that.pane_confirmed_num = res.data.data.confirmedNum;
                    that.pane_applied_num = res.data.data.appliedNum;
                    that.pane_init_num = res.data.data.initNum;
                })
            },
            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
            },
            formatRemainingTime(cellValue) {
                if (cellValue <= 0) {
                    return "0小时";
                } else {
                    let day =  Math.floor(cellValue / 24);
                    let hour = cellValue % 24;
                    return day+"天"+hour+"小时";
                }
            },

            selectBlur(e) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = e.target.value;
            },

            clickTrader(item) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = item.traderName;
            },

            findSelectList() {
                const $grid = this.$refs.xGrid
                if ($grid) {
                    // const levelColumn = $grid.getColumnByField('bussinessLevel')
                    // levelColumn.editRender.options = this.businessChanceLevelList
                    // const tagColumn = $grid.getColumnByField('tagIdList')
                    // tagColumn.editRender.options = this.tagList
                    // const orderRateColumn = $grid.getColumnByField('orderRate')
                    // orderRateColumn.editRender.options = this.orderRateList

                }
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
            },
            beforeBatchApply() {
                let that = this;
                debugger
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }
                this.allReversalShow = true;
            },
            async allReversalShowTrue() {
                this.allReversalShow = false;
                this.allReversalLoading = true;
                await this.batchApply();
                this.allReversalLoading = false;
                this.allReversal=false
            },
            allReversalShowFalse() {
                this.allReversalShow = false;
                this.allReversal=false
            },
            async batchApply() {
                const loading = this.$loading({
                    lock: true,
                    text: 'Loading',
                    spinner: 'el-icon-loading',
                    background: 'rgba(0, 0, 0, 0.7)'
                });

                let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                await batchApplyApi({invoiceRedConfirmationIds:arr,redConfirmationScope:0}).then(res=>{
                    loading.close();
                    if (res.data.success) {
                        debugger;
                        if (res.data.data != undefined && res.data.data.length > 0) {
                            that.errorTable = true;
                            that.errorTableData = res.data.data;
                        } else {
                            VXETable.modal.message({content: '操作成功', status: 'success'});
                            this.reloadData();
                        }
                    } else {
                        VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                    }

                });
            },
            async batchAgree() {
                let that = this;
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }

                const type = await VXETable.modal.confirm('确认同意？');
                if (type == 'confirm') {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                    await batchAgreeApi({invoiceRedConfirmationIds:arr,status:"Y"}).then(res=>{
                        loading.close();
                        if (res.data.success) {
                            debugger;
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                that.errorTable = true;
                                that.errorTableData = res.data.data;
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                this.reloadData();
                            }
                        } else {
                            VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                        }

                    }).catch(res=>{
                        loading.close();
                    });;
                }

            },
            async batchReject() {
                let that = this;
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }

                const type = await VXETable.modal.confirm('确认拒绝？');
                let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                if (type == 'confirm') {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    batchAgreeApi({invoiceRedConfirmationIds:arr,status:"N"}).then(res=>{
                        loading.close();
                        if (res.data.success) {
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                that.errorTable = true;
                                that.errorTableData = res.data.data;
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                this.reloadData();
                            }
                        } else {
                            VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                        }

                    }).catch(res=>{
                        loading.close();
                    });
                }

            },
            async batchInvoice() {
                let that = this;
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }
                const type = await VXETable.modal.confirm('确认开票？');
                let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                if (type == 'confirm') {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    batchInvoiceApi({invoiceRedConfirmationIds:arr}).then(res=>{
                        loading.close();
                        if (res.data.success) {
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                that.errorTable = true;
                                that.errorTableData = res.data.data;
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                this.reloadData();
                            }
                        } else {
                            VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                        }

                    }).catch(res=>{
                        loading.close();
                    });
                }


            },
            async batchCancel() {
                let that = this;
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }
                const type = await VXETable.modal.confirm('确认撤销？');
                let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                if (type == 'confirm') {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    batchCancelApi({invoiceRedConfirmationIds:arr}).then(res=>{
                        loading.close();
                        if (res.data.success) {
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                that.errorTable = true;
                                that.errorTableData = res.data.data;
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                this.reloadData();
                            }
                        } else {
                            VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                        }

                    }).catch(res=>{
                        loading.close();
                    });;
                }

            },
            async batchInvalid() {
                let that = this;
                if (this.selectRecords.length == 0) {
                    VXETable.modal.message({ content: '请至少选中一条数据', status: 'warning' })
                    return;
                }
                const type = await VXETable.modal.confirm('确认作废？');
                let arr = this.selectRecords.map(obj => obj.invoiceRedConfirmationId);
                if (type == 'confirm') {
                    const loading = this.$loading({
                        lock: true,
                        text: 'Loading',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    batchInvalidApi({invoiceRedConfirmationIds:arr}).then(res=>{
                        loading.close();
                        if (res.data.success) {
                            if (res.data.data != undefined && res.data.data.length > 0) {
                                that.errorTable = true;
                                that.errorTableData = res.data.data;
                            } else {
                                VXETable.modal.message({content: '操作成功', status: 'success'});
                                this.reloadData();
                            }
                        } else {
                            VXETable.modal.message({ content: res.data.message, status: 'error' })
                        }

                    }).catch(res=>{
                        loading.close();
                    });;
                }

            },
            // 红字确认单-详情
            viewDetail(row) {
                if (row.inputPersonType == 1 && row.afterSaleBusinessOrderNo =='') {
                    VXETable.modal.message({ content: '请先关联售后单', status: 'error' })
                } else {
                    openModelParam("/invoice/redConfirmation/detail.do?invoiceRedConfirmationId=" + row.invoiceRedConfirmationId, "红字确认单详情", "70%", "100%");
                }
            },

            viewOrderDetail(row) {
                openTab("订单详情", '/order/saleorder/view.do?saleorderId=' + row.businessOrderId);
            },

            viewAfterSalesDetail(row) {
                openTab("售后单详情", '/order/aftersalesUpgrade/viewAfterSalesDetail.do?afterSalesId=' + row.afterSaleBusinessOrderId);
            },
            globalUpdate() {
                let that = this;
                that.globalUpdateLoading = !that.globalUpdateLoading;
                globalUpdateApi().then(res=>{
                    that.globalUpdateLoading = !that.globalUpdateLoading;
                    if (!res.data.success) {
                        VXETable.modal.alert({content: res.data.message, title: '操作失败信息', status: 'error'});
                    } else {
                        VXETable.modal.message({content: '操作成功', status: 'success'});
                        that.reloadData()
                    }
                });
            },
            reloadData() {
                const $table = this.$refs.xGrid;
                $table.commitProxy('query');
                this.getTypeNum();
            },
            checkboxChangeEvent() {
                const $grid = this.$refs.xGrid;
                this.isAllChecked = $grid.isAllCheckboxChecked()
                this.isIndeterminate = $grid.isAllCheckboxIndeterminate()
                this.selectRecords = $grid.getCheckboxRecords()
            },
            changeAllEvent() {
                const $grid = this.$refs.xGrid;
                $grid.setAllCheckboxRow(this.isAllChecked)
                this.selectRecords = $grid.getCheckboxRecords()
            }


        }
    });


</script>

<style>
    /*.vxe-row > .vxe-col--6 {*/
    /*    float: none;*/
    /*}*/

    .page-left {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 10;
    }

    .badge-item {
        margin-top: 10px;
        /*margin-right: 15px;*/
    }

    .el-tabs__header {
        padding: 0;
        position: relative;
        margin: 0 0 0px !important;
    }

    /*.vxe-button + .vxe-button, .vxe-button + .vxe-button--dropdown, .vxe-input + .vxe-button, .vxe-input + .vxe-button--dropdown {*/
    /*    margin-left: 0px !important;*/
    /*}*/

</style>


</body>
</html>
