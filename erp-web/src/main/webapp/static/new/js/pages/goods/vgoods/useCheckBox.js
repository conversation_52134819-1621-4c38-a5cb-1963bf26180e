layui.use(['form','layer'],function(){
    var form = layui.form;

    layer = parent.layer === undefined ? layui.layer : top.layer,
    $ = layui.$;

    form.render();
    // 实现 全选 反选
    form.on('checkbox(check_all)',function(data){
        $(this).parent().siblings("div").find("input").prop("checked",this.checked);
        form.render('checkbox');
    });
    /**
     * 以下代码实现:
     * 1 当子项全部选中时,全选被选中
     * 2 当子项未全部选中时,全选不选中
     */
    form.on('checkbox(authority)',function(data){
        if(this.checked){//当子项全部选中时,全选被选中
            if($(this).parent().siblings("div").children().filter(".authority").not("input:checked").length === 0){
                $(this).parent().parent().parent().siblings("div").children('input').prop("checked",true);
                form.render('checkbox');
            }
        } else { // 子项未选中 全选不选中
            $(this).parent().parent().parent().siblings("div").children('input').prop("checked",false);
            form.render('checkbox');
        }
    })


})