<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.settlement.mapper.SettlementBillMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.settlement.domain.entity.SettlementBillEntity">
    <!--@mbg.generated-->
    <!--@Table T_SETTLEMENT_BILL-->
    <id column="SETTLE_BILL_ID" jdbcType="INTEGER" property="settleBillId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SETTLE_BILL_NO" jdbcType="VARCHAR" property="settleBillNo" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_SUBJECT_TYPE" jdbcType="INTEGER" property="traderSubjectType" />
    <result column="TRADER_SUBJECT_ID" jdbcType="INTEGER" property="traderSubjectId" />
    <result column="SOURCE_TYPE" jdbcType="VARCHAR" property="sourceType" />
    <result column="BUSINESS_SOURCE_TYPE_NO" jdbcType="VARCHAR" property="businessSourceTypeNo" />
    <result column="BUSINESS_SOURCE_TYPE_ID" jdbcType="INTEGER" property="businessSourceTypeId" />
    <result column="SETTLE_AMOUNT" jdbcType="DECIMAL" property="settleAmount" />
    <result column="ALREADY_SETTLE_AMOUNT" jdbcType="DECIMAL" property="alreadySettleAmount" />
    <result column="ACCOUNT_PERIOD" jdbcType="INTEGER" property="accountPeriod" />
    <result column="ACCOUNT_PERIOD_AMOUNT" jdbcType="DECIMAL" property="accountPeriodAmount" />
    <result column="ALREADY_REPAID_AMOUNT" jdbcType="DECIMAL" property="alreadyRepaidAmount" />
    <result column="TRANSACTION_COUNT" jdbcType="INTEGER" property="transactionCount" />
    <result column="SETTLEMENT_STATUS" jdbcType="INTEGER" property="settlementStatus" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    SETTLE_BILL_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME,
    SETTLE_BILL_NO, TRADER_ID, TRADER_SUBJECT_TYPE, TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO,
    BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT, ACCOUNT_PERIOD, ACCOUNT_PERIOD_AMOUNT,
    ALREADY_REPAID_AMOUNT, TRANSACTION_COUNT, SETTLEMENT_STATUS, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_SETTLEMENT_BILL
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SETTLEMENT_BILL
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="SETTLE_BILL_ID" keyProperty="settleBillId" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL (ADD_TIME, MOD_TIME, CREATOR,
      UPDATER, CREATOR_NAME, UPDATER_NAME,
      SETTLE_BILL_NO, TRADER_ID, TRADER_SUBJECT_TYPE,
      TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO,
      BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT,
      ACCOUNT_PERIOD, ACCOUNT_PERIOD_AMOUNT, ALREADY_REPAID_AMOUNT,
      TRANSACTION_COUNT, SETTLEMENT_STATUS, IS_DELETE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR},
      #{settleBillNo,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}, #{traderSubjectType,jdbcType=INTEGER},
      #{traderSubjectId,jdbcType=INTEGER}, #{sourceType,jdbcType=VARCHAR}, #{businessSourceTypeNo,jdbcType=VARCHAR},
      #{businessSourceTypeId,jdbcType=INTEGER}, #{settleAmount,jdbcType=DECIMAL}, #{alreadySettleAmount,jdbcType=DECIMAL},
      #{accountPeriod,jdbcType=INTEGER}, #{accountPeriodAmount,jdbcType=DECIMAL}, #{alreadyRepaidAmount,jdbcType=DECIMAL},
      #{transactionCount,jdbcType=INTEGER}, #{settlementStatus,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="SETTLE_BILL_ID" keyProperty="settleBillId" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="settleBillNo != null and settleBillNo != ''">
        SETTLE_BILL_NO,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderSubjectType != null">
        TRADER_SUBJECT_TYPE,
      </if>
      <if test="traderSubjectId != null">
        TRADER_SUBJECT_ID,
      </if>
      <if test="sourceType != null and sourceType != ''">
        SOURCE_TYPE,
      </if>
      <if test="businessSourceTypeNo != null and businessSourceTypeNo != ''">
        BUSINESS_SOURCE_TYPE_NO,
      </if>
      <if test="businessSourceTypeId != null">
        BUSINESS_SOURCE_TYPE_ID,
      </if>
      <if test="settleAmount != null">
        SETTLE_AMOUNT,
      </if>
      <if test="alreadySettleAmount != null">
        ALREADY_SETTLE_AMOUNT,
      </if>
      <if test="accountPeriod != null">
        ACCOUNT_PERIOD,
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT,
      </if>
      <if test="alreadyRepaidAmount != null">
        ALREADY_REPAID_AMOUNT,
      </if>
      <if test="transactionCount != null">
        TRANSACTION_COUNT,
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillNo != null and settleBillNo != ''">
        #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectType != null">
        #{traderSubjectType,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectId != null">
        #{traderSubjectId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null and sourceType != ''">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeNo != null and businessSourceTypeNo != ''">
        #{businessSourceTypeNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeId != null">
        #{businessSourceTypeId,jdbcType=INTEGER},
      </if>
      <if test="settleAmount != null">
        #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadySettleAmount != null">
        #{alreadySettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriod != null">
        #{accountPeriod,jdbcType=INTEGER},
      </if>
      <if test="accountPeriodAmount != null">
        #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyRepaidAmount != null">
        #{alreadyRepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionCount != null">
        #{transactionCount,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillEntity">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="settleBillNo != null and settleBillNo != ''">
        SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectType != null">
        TRADER_SUBJECT_TYPE = #{traderSubjectType,jdbcType=INTEGER},
      </if>
      <if test="traderSubjectId != null">
        TRADER_SUBJECT_ID = #{traderSubjectId,jdbcType=INTEGER},
      </if>
      <if test="sourceType != null and sourceType != ''">
        SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeNo != null and businessSourceTypeNo != ''">
        BUSINESS_SOURCE_TYPE_NO = #{businessSourceTypeNo,jdbcType=VARCHAR},
      </if>
      <if test="businessSourceTypeId != null">
        BUSINESS_SOURCE_TYPE_ID = #{businessSourceTypeId,jdbcType=INTEGER},
      </if>
      <if test="settleAmount != null">
        SETTLE_AMOUNT = #{settleAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadySettleAmount != null">
        ALREADY_SETTLE_AMOUNT = #{alreadySettleAmount,jdbcType=DECIMAL},
      </if>
      <if test="accountPeriod != null">
        ACCOUNT_PERIOD = #{accountPeriod,jdbcType=INTEGER},
      </if>
      <if test="accountPeriodAmount != null">
        ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="alreadyRepaidAmount != null">
        ALREADY_REPAID_AMOUNT = #{alreadyRepaidAmount,jdbcType=DECIMAL},
      </if>
      <if test="transactionCount != null">
        TRANSACTION_COUNT = #{transactionCount,jdbcType=INTEGER},
      </if>
      <if test="settlementStatus != null">
        SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.settlement.domain.entity.SettlementBillEntity">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      SETTLE_BILL_NO = #{settleBillNo,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_SUBJECT_TYPE = #{traderSubjectType,jdbcType=INTEGER},
      TRADER_SUBJECT_ID = #{traderSubjectId,jdbcType=INTEGER},
      SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR},
      BUSINESS_SOURCE_TYPE_NO = #{businessSourceTypeNo,jdbcType=VARCHAR},
      BUSINESS_SOURCE_TYPE_ID = #{businessSourceTypeId,jdbcType=INTEGER},
      SETTLE_AMOUNT = #{settleAmount,jdbcType=DECIMAL},
      ALREADY_SETTLE_AMOUNT = #{alreadySettleAmount,jdbcType=DECIMAL},
      ACCOUNT_PERIOD = #{accountPeriod,jdbcType=INTEGER},
      ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
      ALREADY_REPAID_AMOUNT = #{alreadyRepaidAmount,jdbcType=DECIMAL},
      TRANSACTION_COUNT = #{transactionCount,jdbcType=INTEGER},
      SETTLEMENT_STATUS = #{settlementStatus,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER}
    where SETTLE_BILL_ID = #{settleBillId,jdbcType=INTEGER}
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_SETTLEMENT_BILL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLE_BILL_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settleBillNo != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.settleBillNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_SUBJECT_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderSubjectType != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.traderSubjectType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_SUBJECT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderSubjectId != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.traderSubjectId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SOURCE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.sourceType != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.sourceType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_SOURCE_TYPE_NO = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessSourceTypeNo != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.businessSourceTypeNo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_SOURCE_TYPE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessSourceTypeId != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.businessSourceTypeId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settleAmount != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.settleAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ALREADY_SETTLE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.alreadySettleAmount != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.alreadySettleAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_PERIOD = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountPeriod != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.accountPeriod,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ACCOUNT_PERIOD_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.accountPeriodAmount != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.accountPeriodAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="ALREADY_REPAID_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.alreadyRepaidAmount != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.alreadyRepaidAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRANSACTION_COUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.transactionCount != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.transactionCount,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="SETTLEMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.settlementStatus != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.settlementStatus,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when SETTLE_BILL_ID = #{item.settleBillId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where SETTLE_BILL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.settleBillId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="SETTLE_BILL_ID" keyProperty="settleBillId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SETTLEMENT_BILL
    (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, SETTLE_BILL_NO,
      TRADER_ID, TRADER_SUBJECT_TYPE, TRADER_SUBJECT_ID, SOURCE_TYPE, BUSINESS_SOURCE_TYPE_NO,
      BUSINESS_SOURCE_TYPE_ID, SETTLE_AMOUNT, ALREADY_SETTLE_AMOUNT, ACCOUNT_PERIOD,
      ACCOUNT_PERIOD_AMOUNT, ALREADY_REPAID_AMOUNT, TRANSACTION_COUNT, SETTLEMENT_STATUS,
      IS_DELETE)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR},
        #{item.settleBillNo,jdbcType=VARCHAR}, #{item.traderId,jdbcType=INTEGER}, #{item.traderSubjectType,jdbcType=INTEGER},
        #{item.traderSubjectId,jdbcType=INTEGER}, #{item.sourceType,jdbcType=VARCHAR},
        #{item.businessSourceTypeNo,jdbcType=VARCHAR}, #{item.businessSourceTypeId,jdbcType=INTEGER},
        #{item.settleAmount,jdbcType=DECIMAL}, #{item.alreadySettleAmount,jdbcType=DECIMAL},
        #{item.accountPeriod,jdbcType=INTEGER}, #{item.accountPeriodAmount,jdbcType=DECIMAL},
        #{item.alreadyRepaidAmount,jdbcType=DECIMAL}, #{item.transactionCount,jdbcType=INTEGER},
        #{item.settlementStatus,jdbcType=INTEGER}, #{item.isDelete,jdbcType=INTEGER})
    </foreach>
  </insert>


<!--auto generated by MybatisCodeHelper on 2023-11-29-->
  <update id="updateIsDelete">
    update T_SETTLEMENT_BILL
    set IS_DELETE=#{updatedIsDelete,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-11-29-->
  <update id="updateIsDeleteBySettleBillId">
    update T_SETTLEMENT_BILL
    set IS_DELETE=#{updatedIsDelete,jdbcType=INTEGER}
    where SETTLE_BILL_ID=#{settleBillId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-12-09-->
  <select id="findByBusinessSourceTypeIdAndSourceType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SETTLEMENT_BILL
    where BUSINESS_SOURCE_TYPE_ID = #{businessSourceTypeId,jdbcType=INTEGER}
      and SOURCE_TYPE = #{sourceType,jdbcType=VARCHAR}
      and IS_DELETE = 0
  </select>
</mapper>