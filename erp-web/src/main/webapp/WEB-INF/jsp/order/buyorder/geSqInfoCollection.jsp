<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2020/11/17
  Time: 11:29
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="GE主机序列号和探头序列号录入" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%= basePath %>static/js/order/buyorder/geSqInfoCollection.js?rnd=${resourceVersionKey}'>
</script>
<style>
    #addMimg{
        margin-top: 21px;
        margin-left: 3px;
        margin-left: 3px;
        width: 20px;
        height: 20px;
    }
</style>

<body>
    <div style="margin: 20px">
        <form >
            <div class="title-container mb10">
                <div class="table-title nobor">序列号</div>
                <div style="float: right;margin-right: 20px">
                    <input type="button" onclick="addMaster()" value="添加主机">
                </div>
            </div>

            <table class="table table-base table-hover base-form J-table-wrap" id="myTable">
                <colgroup>
                    <col width="10%">
                    <col width="30%">
                    <col width="">
                    <col width="15%">
                </colgroup>
                <tbody id="num">
                <tr>
                    <th>编号</th>
                    <th>主机序列号</th>
                    <th>探头序列号</th>
                    <th>操作</th>
                </tr>
                <c:if test="${not empty masterSlaveLists}">
                    <c:forEach var="masterSlaveLists" items="${masterSlaveLists}">
                        <tr>
                            <td></td>
                            <td>
                                <input type="text" name="masterSno"
                                       value="${masterSlaveLists.vBuyorderSncodeMaster.masterSncode}">
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${ empty  masterSlaveLists.vBuyorderSncodeSlaves}">
                                    <div style="text-align: left">
                                        <input style="width: 200px;margin: 5px auto" type="text" name="slaveSno">
                                        <img src="/static/images/addicon.png" onclick="addSlave(this)" style="width: 18px;height: 18px;margin-left: 10px">
                                        <img src="/static/images/subicon.png" onclick="delSlave(this)" style="width: 20px;height: 20px;margin-left: 10px">
                                    </div>
                                    </c:when>
                                    <c:otherwise>
                                        <c:set var="num" value="0"/>
                                        <c:forEach var="slave" items="${masterSlaveLists.vBuyorderSncodeSlaves}">
                                            <div style="text-align: left">
                                                <input style="width: 200px;margin: 5px auto" type="text" name="slaveSno"
                                                       value=" ${slave.slaveSncode}">
                                                <c:if test="${num eq 0}">
                                                    <img src="/static/images/addicon.png" onclick="addSlave(this)" style="width: 18px;height: 18px;margin-left: 10px">
                                                    <c:set var="num" value="${num+1}"/>
                                                </c:if>
                                                     <img src="/static/images/subicon.png" onclick="delSlave(this)" style="width: 20px;height: 20px;margin-left: 10px">
                                            </div>
                                        </c:forEach>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td><input type="button" onclick="delMaster(this)" value="删除主机"></td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
            <div style="margin-top: 30px">
                <div style="float: left;margin-left: 260px;margin-right: 100px">
                    <input type="button" onclick="giveUp(this)" value="取消" style="width: 60px;color: white;background-color: #7a8a99">
                </div>
                <div style="margin-bottom: 30px">
                    <input type="button" onclick="sureEdit('${sku}',${buyorderGoodsId},${goodsId})" value="确定"
                           style="background-color: #1E9FFF;color:white;width: 60px">
                </div>
            </div>
        </form>
    </div>
</body>



