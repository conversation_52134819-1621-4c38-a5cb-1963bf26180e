package com.vedeng.erp.kingdee.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeStorageInCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeStorageInEntity;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeStorageInQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeStorageInConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeStorageInRepository;
import com.vedeng.erp.kingdee.service.KingDeeStorageInApiService;
import com.vedeng.erp.kingdee.service.KingDeeStorageInService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description 其他入库单接口实现类
 * @date 2023/3/6 9:00
 **/
@Service
@Slf4j
public class KingDeeStorageInServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeStorageInEntity,
        KingDeeStorageInDto,
        KingDeeStorageInCommand,
        KingDeeStorageInRepository,
        KingDeeStorageInConvertor,
        KingDeeStorageInCommandConvertor> implements KingDeeStorageInApiService, KingDeeStorageInService {

    @Override
    public List<KingDeeStorageInQueryResultDto> getKingDeeStorageIn(String f_qzok_bddjtid, String f_qzok_bddjhid) {
        // 调用查询金蝶接口
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.STK_MISCELLANEOUS);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJTID")
                .value(f_qzok_bddjtid).build());
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName("F_QZOK_BDDJHID")
                .value(f_qzok_bddjhid).build());
        queryParam.setFilterString(queryFilterDtos);
        log.info("金蝶库单查询入参：{}", JSON.toJSONString(queryParam));
        return kingDeeBaseApi.query(queryParam, KingDeeStorageInQueryResultDto.class);
    }


}
