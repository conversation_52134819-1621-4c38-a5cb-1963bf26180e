Vue.component('ui-upload', {
    template: `<div class="vd-ui-upload-wrap">
        <div class="vd-ui-upload-img-wrap" v-if="type=='img'">
            <template v-if="files && files.length">
                <div class="vd-ui-upload-img-item" v-for="(item, index) in files">
                    <img :src="item.url"/>
                    <div class="vd-ui-upload-img-options">
                        <div class="vd-ui-upload-img-option-item" @click="previewImg(item.url)">查看</div>
                        <div class="vd-ui-upload-img-option-item" @click="deleteItem(index)">删除</div>
                    </div>
                </div>
            </template>
            <div class="vd-ui-upload-img-btn" @click="triggerInputClick" v-if="!tempFile && files.length < limit">
                <i class="vd-ui_icon icon-add"></i>
            </div>
            <div class="vd-ui-upload-img-loading" v-if="tempFile">
                <i class="vd-ui_icon icon-loading"></i>
            </div>
        </div>
        <div class="vd-ui-upload-file-wrap" :class="{'no-info': noInfo}" v-if="type=='file'">
            <ui-button @click="triggerInputClick" :type="btnType" :disabled="!!tempFile">{{ label }}</ui-button>
            <template v-if="!noInfo">
                <div class="vd-ui-file-list" v-if="tempFile || files.length">
                    <div class="vd-ui-file-item" v-for="(item, index) in files" :key="index">
                        <div class="vd-ui-file-info">
                            <div class="vd-ui-file-icon">
                                <img :src="'/static/image/upload-icon/' + (typeParse[item.type] || 'other') + '.svg'"></img>
                            </div>
                            <div class="vd-ui-file-name">{{item.name}}</div>
                            <div class="vd-ui-file-size">{{item.size}}</div>
                            <div class="vd-ui-file-option">
                                <i class="vd-ui_icon icon-recycle"></i>
                            </div>
                        </div>
                    </div>
                    <div class="vd-ui-file-item" v-if="tempFile">
                        <div class="vd-ui-file-info">
                            <div class="vd-ui-file-icon">
                                <img :src="'/static/image/upload-icon/' + (typeParse[tempFile.type] || 'other') + '.svg'"></img>
                            </div>
                            <div class="vd-ui-file-name text-line-1">{{tempFile.name}}</div>
                        </div>
                        <div class="vd-ui-file-progress"></div>
                    </div>
                </div>
            </template>
        </div>
        <!-- 提示 -->
        <div class="vd-ui-upload-tips"> 
            <slot name="tips"></slot>
        </div>
        <input 
            type="file" 
            ref="fileInput" 
            :accept="accept" 
            @change="handlerFileInputChange" 
            style="display:none"/>
    </div>`,
    props: {
        label: {
            type: String,
            default: '本地上传'
        },
        type: {
            type: String,
            default: 'file'
        },
        limitType: {
            type: String,
            default: ''
        },
        limitSize: {
            type: Number,
            default: 2
        },
        limit: {
            type: Number,
            default: 1
        },
        errorMsg: {
            type: Object,
            default: {
                limitType: '格式不对',
                limitSize: '图片大小不超过{}MB'
            }
        },
        action: {
            type: String,
            default: '/crm/common/public/uploadFile'
        },
        needValid: {
            type: Boolean,
            default: true
        },
        noInfo: {
            type: Boolean,
            default: false
        },
        btnType: {
            type: String,
            default: ''
        },
        multi: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default() {
                return []
            }
        }
    },
    data() {
        return {
            accept: '',
            acceptParse: {
                gif: 'image/gif',
                jpg: 'image/jpeg',
                jpeg: 'image/jpeg',
                png: 'image/png',
                pdf: 'application/pdf',
                doc: 'application/msword',
                xls: 'application/vnd.ms-excel',
                xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            },
            typeParse: {
                xls: 'excel',
                xlsx: 'excel',
                pdf: 'pdf',
                png: 'pic',
                jpg: 'pic',
                jpeg: 'pic',
                bmp: 'pic',
                gif: 'pic',
                ppt: 'ppt',
                pptx: 'ppt',
                doc: 'word',
                docx: 'word',
            },
            files: [],
            tempFile: null
        }
    },
    watch: {

    },
    mounted() {
        this.init()
    },
    methods: {
        init() {
            if (this.limitType) {
                let acceptList = [];
                let isAll = false;
                this.limitType.split(',').forEach(item => {
                    if (!this.acceptParse[item]) {
                        isAll = true;
                    } else {
                        acceptList.push(this.acceptParse[item]);
                    }
                })

                if (!isAll) {
                    this.accept = acceptList.join(',')
                } else if (this.type == 'img') {
                    this.accept = 'image/*';
                }
            }

            if(this.list && this.list.length) {
                this.files = this.list;
            }
        },
        triggerInputClick() {
            if (!this.tempFile && this.limit > this.files.length) {
                this.$refs.fileInput.click();
            }
        },
        getSize(size) {
            if (size > 1024 * 1024) {
                return parseFloat((size / 1024 / 1024).toFixed(1)) + 'MB';
            } else {
                return parseInt(size / 1024) + 'KB';
            }
        },
        handlerFileInputChange() {
            let files = this.$refs.fileInput.files;

            if (files.length) {
                let file = files[0];

                if (this.multi) {

                }
                let fileName = file.name;
                let fileNameTxt = fileName.substring(0, fileName.lastIndexOf('.'));
                let fileType = fileName.substring(fileName.lastIndexOf('.') + 1, fileName.length);

                if (this.needValid) {
                    if (this.limitType.split(',').indexOf(fileType) === -1) {
                        if (!this.customError) {
                            this.showError(this.errorMsg.limitType);
                        }

                        return false;
                    }

                    if (file.size > this.limitSize * 1024 * 1024) {
                        if (!this.customError) {
                            this.showError(this.errorMsg.limitSize.replace('{}', this.limitSize));
                        }

                        return false;
                    }
                }

                let form = new FormData();
                form.append('file', file);

                this.tempFile = {
                    name: fileNameTxt,
                    type: fileType,
                    fullName: fileName
                }

                this.$axios.post(this.action, form, {
                    headers: { 'Content-Type': 'multipart/form-data' }
                }).then(({ data }) => {
                    this.tempFile = null;
                    if (data.code === 0) {
                        if (this.type === 'file') {
                            this.files.push({
                                name: fileNameTxt,
                                type: fileType,
                                size: this.getSize(file.size),
                                fullName: fileName,
                            })
                        } else {
                            this.files.push({
                                url: data.scheme + data.ossUrl
                            })
                        }

                        this.$emit('change', this.files);
                        this.clear();

                        // if (this.limit == 1 && this.files.length == 1) {
                        //     this.label = '重新上传';
                        // }
                    } else {
                        this.showError(data.message || '上传失败');
                        this.$emit('onerror', data)
                    }
                })
            }
        },
        clear() {
            this.$refs.fileInput.value = "";
        },
        showError(txt) {
            this.$popup.warn({
                message: txt,
                buttons: [{
                    txt: '我知道了',
                    btnClass: 'confirm',
                }]
            })

            this.clear();
        },
        deleteItem(index) {
            this.files.splice(index, 1);
            
            this.$emit('change', this.files);
        },
        previewImg(url) {
            window.open(url)
        }
    }
})

Vue.component('ui-upload-attachment', {
    template: `<div class="vd-ui-upload-wrap">
        <div class="vd-ui-upload-file-wrap no-info">
            <ui-button @click="showConfirmDialog" type="primary">{{ label }}</ui-button>
        </div>

        <input 
            type="file" 
            ref="fileInput" 
            :accept="accept" 
            multiple
            @change="handlerFileInputChange" 
            style="display:none"/>
    </div>`,
    props: {
        label: {
            type: String,
            default: '添加附件'
        },
        limitType: {
            type: String,
            default: ''
        },
        limitNumber: {
            type: Number,
            default: 20
        },
        limitSize: {
            type: Number,
            default: 10
        },
        bizType: {
            type: String,
            default: '03'
        },
        bizId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            accept: '',
            files: [],
            errorSize: false,
            errorNumber: false,
            uploadList: [],
            isShowConfirmDialog: false,
            onUploading: false,
            stopTxt: '', //是否有中断信息
        }
    },
    watch: {

    },
    mounted() {
        // this.init()
    },
    methods: {
        init() {
            // if (this.limitType) {
            //     let acceptList = [];
            //     let isAll = false;
            //     this.limitType.split(',').forEach(item => {
            //         if (!this.acceptParse[item]) {
            //             isAll = true;
            //         } else {
            //             acceptList.push(this.acceptParse[item]);
            //         }
            //     })

            //     if (!isAll) {
            //         this.accept = acceptList.join(',')
            //     } else if (this.type == 'img') {
            //         this.accept = 'image/*';
            //     }
            // }
        },
        showConfirmDialog() {
            let _this = this;

            this.$popup.info({
                message: `支持批量添加文件，单个大小不超过${this.limitSize}MB，最多添加${this.limitNumber}个文件。`,
                buttons: [{
                    txt: '本地上传',
                    btnClass: 'confirm',
                    callback() {
                       _this.triggerInputClick();
                    }
                }, {
                    txt: '取消'
                }]
            })
        },
        triggerInputClick() {
            if(!this.onUploading) {
                this.$refs.fileInput.click();
            }
        },
        handlerFileInputChange() {
            let files = this.$refs.fileInput.files;

            if (files.length) {
                GLOBAL.showGlobalLoading('文件上传中，请不要关闭当前页面');
                this.onUploading = true;
                this.upload(files, 0);
            }
        },
        upload(files, index) {
            let file = files[index];

            if (file.size > this.limitSize * 1024 * 1024) {
                this.errorSize = true;

                this.checkFinish(files, index);

                return false;
            }
            console.log(file)
            let form = new FormData();
            form.append('bizId', this.bizId);
            form.append('bizType', this.bizType);
            form.append('file', file);
            this.$axios.post('/crm/common/public/uploadBusinessFile', form, {
                headers: { 'Content-Type': 'multipart/form-data' }
            }).then(({ data }) => {
                if (data.success) {
                    this.uploadList.push(data.data);
                } else {
                    index = files.length - 1;
                    if (data.code == '50001') {
                        this.errorNumber = true;
                    } else {
                        this.stopTxt = data.message;
                        // this.errorSize = true
                    }
    
                }
                this.checkFinish(files, index)
            })
        },
        checkFinish(files, index) {
            if(this.stopTxt) {
                this.$popup.warn({
                    message: this.stopTxt,
                    buttons: [{
                        txt: '我知道了',
                        btnClass: 'confirm'
                    }]
                });
                GLOBAL.hideGlobalLoading();
                this.onUploading = false;
                this.clear();
            } else if (index < files.length - 1) {
                this.upload(files, index + 1)
            } else {
                GLOBAL.hideGlobalLoading();
                this.onUploading = false;
                let errMsg = [];

                if (this.errorNumber) {
                    errMsg.push(`最多上传${this.limitNumber}个附件；`)
                }


                if (this.errorSize) {
                    errMsg.push(`单个文件大小不能超过${this.limitSize}MB；`)
                }

                if (errMsg.length) {
                    this.$popup.warn({
                        message: errMsg.join('<br/>'),
                        buttons: [{
                            txt: '我知道了',
                            btnClass: 'confirm'
                        }]
                    });
                }

                this.$emit('finish', this.uploadList);

                if (this.uploadList.length) {
                    let reqData = [];

                    this.uploadList.forEach(item => {
                        reqData.push({
                            attachmentId: item.attachmentId,
                            bizType: this.bizType,
                            bizId: this.bizId
                        })
                    })
                    this.uploadList = [];

                    this.$axios.post('/crm/common/public/saleUploadFileLog', reqData)
                }

                this.clear();
            }
        },
        clear() {
            this.$refs.fileInput.value = "";
        }
    }
})