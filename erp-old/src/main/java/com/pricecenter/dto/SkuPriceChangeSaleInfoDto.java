package com.pricecenter.dto;

import java.math.BigDecimal;

public class SkuPriceChangeSaleInfoDto {

    private Long id;

    private Long skuPriceChangeApplyId;

    private Long skuId;

    private BigDecimal marketPrice;

    private BigDecimal terminalPrice;

    private BigDecimal distributionPrice;
    
    private BigDecimal originalDistributionPrice;

    private BigDecimal groupPrice;

    private String marketPriceChangeReason;

    private String distributionPriceChangeReason;

    private String terminalPriceChangeReason;

    private String groupPriceChangeReason;

    private BigDecimal electronicCommercePrice;

    private String electronicCommercePriceChangeReason;

    private BigDecimal researchTerminalPrice;

    private String researchTerminalChangeReason;

    private Integer purchaseContainsFee;

    private Integer saleContainsFee;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuPriceChangeApplyId() {
        return skuPriceChangeApplyId;
    }

    public void setSkuPriceChangeApplyId(Long skuPriceChangeApplyId) {
        this.skuPriceChangeApplyId = skuPriceChangeApplyId;
    }

    public Long getSkuId() {
        return skuId;
    }

    public void setSkuId(Long skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getMarketPrice() {
        return marketPrice;
    }

    public void setMarketPrice(BigDecimal marketPrice) {
        this.marketPrice = marketPrice;
    }

    public BigDecimal getDistributionPrice() {
        return distributionPrice;
    }

    public void setDistributionPrice(BigDecimal distributionPrice) {
        this.distributionPrice = distributionPrice;
    }

    public BigDecimal getTerminalPrice() {
        return terminalPrice;
    }

    public void setTerminalPrice(BigDecimal terminalPrice) {
        this.terminalPrice = terminalPrice;
    }

    public String getMarketPriceChangeReason() {
        return marketPriceChangeReason;
    }

    public void setMarketPriceChangeReason(String marketPriceChangeReason) {
        this.marketPriceChangeReason = marketPriceChangeReason;
    }

    public String getDistributionPriceChangeReason() {
        return distributionPriceChangeReason;
    }

    public void setDistributionPriceChangeReason(String distributionPriceChangeReason) {
        this.distributionPriceChangeReason = distributionPriceChangeReason;
    }

    public String getTerminalPriceChangeReason() {
        return terminalPriceChangeReason;
    }

    public void setTerminalPriceChangeReason(String terminalPriceChangeReason) {
        this.terminalPriceChangeReason = terminalPriceChangeReason;
    }

    public BigDecimal getGroupPrice() {
        return groupPrice;
    }

    public void setGroupPrice(BigDecimal groupPrice) {
        this.groupPrice = groupPrice;
    }

    public String getGroupPriceChangeReason() {
        return groupPriceChangeReason;
    }

    public void setGroupPriceChangeReason(String groupPriceChangeReason) {
        this.groupPriceChangeReason = groupPriceChangeReason;
    }

    public BigDecimal getElectronicCommercePrice() {
        return electronicCommercePrice;
    }

    public void setElectronicCommercePrice(BigDecimal electronicCommercePrice) {
        this.electronicCommercePrice = electronicCommercePrice;
    }

    public String getElectronicCommercePriceChangeReason() {
        return electronicCommercePriceChangeReason;
    }

    public void setElectronicCommercePriceChangeReason(String electronicCommercePriceChangeReason) {
        this.electronicCommercePriceChangeReason = electronicCommercePriceChangeReason;
    }

    public BigDecimal getResearchTerminalPrice() {
        return researchTerminalPrice;
    }

    public void setResearchTerminalPrice(BigDecimal researchTerminalPrice) {
        this.researchTerminalPrice = researchTerminalPrice;
    }

    public String getResearchTerminalChangeReason() {
        return researchTerminalChangeReason;
    }

    public void setResearchTerminalChangeReason(String researchTerminalChangeReason) {
        this.researchTerminalChangeReason = researchTerminalChangeReason;
    }

    public Integer getPurchaseContainsFee() {
        return purchaseContainsFee;
    }

    public void setPurchaseContainsFee(Integer purchaseContainsFee) {
        this.purchaseContainsFee = purchaseContainsFee;
    }

    public Integer getSaleContainsFee() {
        return saleContainsFee;
    }

    public void setSaleContainsFee(Integer saleContainsFee) {
        this.saleContainsFee = saleContainsFee;
    }

	public BigDecimal getOriginalDistributionPrice() {
		return originalDistributionPrice;
	}

	public void setOriginalDistributionPrice(BigDecimal originalDistributionPrice) {
		this.originalDistributionPrice = originalDistributionPrice;
	}
    
    
}
