package com.vedeng.goods.dao;

import com.vedeng.goods.model.GoodsExtendGenerate;
import com.vedeng.goods.model.GoodsExtendGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface GoodsExtendGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer goodsExtendId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(GoodsExtendGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(GoodsExtendGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<GoodsExtendGenerate> selectByExampleWithBLOBs(GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<GoodsExtendGenerate> selectByExample(GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    GoodsExtendGenerate selectByPrimaryKey(Integer goodsExtendId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") GoodsExtendGenerate record, @Param("example") GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleWithBLOBs(@Param("record") GoodsExtendGenerate record, @Param("example") GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") GoodsExtendGenerate record, @Param("example") GoodsExtendGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(GoodsExtendGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeyWithBLOBs(GoodsExtendGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_GOODS_EXTEND
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(GoodsExtendGenerate record);
}