package com.vedeng.erp.buyorder.domain.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * T_GE_AUTHORIZATION_CERTIFICATE
 * <AUTHOR>
public class GeAuthorizationCertificate implements Serializable {
    /**
     * 主键
     */
    private Integer authorizationCertificateId;

    /**
     * 授权书主键 T_GE_AUTHORIZATION AUTHORIZATION_ID
     */
    private Integer authorizationId;

    /**
     * 文件类型 1 营业执照 2 二类医疗资质 3 三类医疗资质 4 诚信声明 5 使用二级分销商声明 6 信用查询结果 7 招标文件 8 其他附件
     */
    private Integer type;

    /**
     * 附件名称
     */
    private String name;

    /**
     * 证书域名
     */
    private String domain;

    /**
     * 文件地址
     */
    private String uri;

    /**
     * 扩展信息
     */
    private String extra;

    /**
     * OSS的唯一id
     */
    private String ossResourceId;

    /**
     * 是否删除 0否1是
     */
    private Integer isDelete;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 添加人id
     */
    private Integer creator;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 修改人id
     */
    private Integer updater;

    /**
     * 修改时间
     */
    private Date modTime;

    private static final long serialVersionUID = 1L;

    public Integer getAuthorizationCertificateId() {
        return authorizationCertificateId;
    }

    public void setAuthorizationCertificateId(Integer authorizationCertificateId) {
        this.authorizationCertificateId = authorizationCertificateId;
    }

    public Integer getAuthorizationId() {
        return authorizationId;
    }

    public void setAuthorizationId(Integer authorizationId) {
        this.authorizationId = authorizationId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri;
    }

    public String getExtra() {
        return extra;
    }

    public void setExtra(String extra) {
        this.extra = extra;
    }

    public String getOssResourceId() {
        return ossResourceId;
    }

    public void setOssResourceId(String ossResourceId) {
        this.ossResourceId = ossResourceId;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Date getModTime() {
        return modTime;
    }

    public void setModTime(Date modTime) {
        this.modTime = modTime;
    }
}