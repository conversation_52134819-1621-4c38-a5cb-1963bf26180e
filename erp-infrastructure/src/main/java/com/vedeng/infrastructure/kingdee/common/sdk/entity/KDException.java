package com.vedeng.infrastructure.kingdee.common.sdk.entity;

import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/KDException.class */
@Slf4j
public class KDException {
    String Message;
    KDException InnerExWrapper;
    KDException InnerException;

    public static KDException parse(String json) {
        try {
            int index = json.indexOf("{");
            if (index < 0) {
                json = json.substring(index);
            }
            return (KDException) new Gson().fromJson(json, KDException.class);
        } catch (Exception e) {
            log.error("【parse】处理异常",e);
            return null;
        }
    }

    public String getMessage() {
        return this.Message;
    }

    public void setMessage(String message) {
        this.Message = message;
    }

    public KDException getInnerExWrapper() {
        return this.InnerExWrapper;
    }

    public void setInnerExWrapper(KDException innerExWrapper) {
        this.InnerExWrapper = innerExWrapper;
    }

    public KDException getInnerException() {
        return this.InnerException;
    }

    public void setInnerException(KDException innerException) {
        this.InnerException = innerException;
    }
}
