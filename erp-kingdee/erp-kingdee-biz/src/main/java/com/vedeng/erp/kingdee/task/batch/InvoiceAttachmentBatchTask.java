package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.AllInvoiceAttachmentJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 发票附件推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/15 14:10
 */
@JobHandler("InvoiceAttachmentBatchTask")
@Component
@Slf4j
public class InvoiceAttachmentBatchTask extends AbstractJobHandler {
    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private AllInvoiceAttachmentJob allInvoiceAttachmentJob;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = allInvoiceAttachmentJob.invoiceAttachmentJob();
        jobLauncher.run(job, jobParameters);
        return SUCCESS;
    }
}
