package com.vedeng.erp.system.service.impl;

import com.vedeng.erp.system.mapper.NewMessageMapper;
import com.vedeng.erp.system.service.NewMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2022/8/15 14:45
 */
@Service
public class NewMessageServiceImpl implements NewMessageService {

    @Autowired
    private NewMessageMapper newMessageMapper;

    @Override
    public Integer getPosterCenterUnReadMessage(Integer userId) {
        return newMessageMapper.getPosterCenterUnReadMessage(userId);
    }
}