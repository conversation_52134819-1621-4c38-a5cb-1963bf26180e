package com.vedeng.goods.model.vo;

import com.vedeng.goods.model.CoreOperateInfoGenerate;
import com.vedeng.goods.model.GoodsAttachment;

import java.util.List;

public class CoreOperateInfoGenerateVo extends CoreOperateInfoGenerate {
    private String detailType;
    /**商品名称 PRODUCT_NAME**/
    private String productName;

    /**商品图片路径 GOODS_IMAGE**/
    private String goodsImage[];

    /**seo关键词数组 GOODS_IMAGE**/
    private String[] seoKeyWordsArray;

    /**商品图片列表**/
    private List<GoodsAttachment> goodsAttachmentList;

    /**sku对应的spuID**/
    private Integer upSpuId;

    /**
     * 上下架状态
     * @since ERP_LV_2020_56
     */
    private String sellingStatus;

    /**
     * 马良商品图文详情
     *
     * @since OMS_ERP_LV_2020_33
     */
    private String mlSkuGraphicDetail;

    /**
     * 马良商品图文详情完整度得分
     *
     * @since OMS_ERP_LV_2020_33
     */
    private Integer mlSkuScore;

    /**
     * 马良商品图文详情完整度百分比 如： 10%
     *
     * @since OMS_ERP_LV_2020_33
     */
    private String mlDegreeOfCompletion;

    /**
     * 是否可售
     */
    private Integer isAvailableSale;

    private List<Integer> orgIdArray;


    public String getSellingStatus() {
        return sellingStatus;
    }

    public void setSellingStatus(String sellingStatus) {
        this.sellingStatus = sellingStatus;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String[] getGoodsImage() {
        return goodsImage;
    }

    public void setGoodsImage(String[] goodsImage) {
        this.goodsImage = goodsImage;
    }

    public String[] getSeoKeyWordsArray() {
        return seoKeyWordsArray;
    }

    public void setSeoKeyWordsArray(String[] seoKeyWordsArray) {
        this.seoKeyWordsArray = seoKeyWordsArray;
    }

    public List<GoodsAttachment> getGoodsAttachmentList() {
        return goodsAttachmentList;
    }

    public void setGoodsAttachmentList(List<GoodsAttachment> goodsAttachmentList) {
        this.goodsAttachmentList = goodsAttachmentList;
    }

    public Integer getUpSpuId() {
        return upSpuId;
    }

    public void setUpSpuId(Integer upSpuId) {
        this.upSpuId = upSpuId;
    }

    public String getMlSkuGraphicDetail() {
        return mlSkuGraphicDetail;
    }

    public void setMlSkuGraphicDetail(String mlSkuGraphicDetail) {
        this.mlSkuGraphicDetail = mlSkuGraphicDetail;
    }

    public Integer getMlSkuScore() {
        return mlSkuScore;
    }

    public void setMlSkuScore(Integer mlSkuScore) {
        this.mlSkuScore = mlSkuScore;
    }

    public String getMlDegreeOfCompletion() {
        return mlDegreeOfCompletion;
    }

    public void setMlDegreeOfCompletion(String mlDegreeOfCompletion) {
        this.mlDegreeOfCompletion = mlDegreeOfCompletion;
    }

    public String getDetailType() {
        return detailType;
    }

    public void setDetailType(String detailType) {
        this.detailType = detailType;
    }

    public Integer getIsAvailableSale() {
        return isAvailableSale;
    }

    public void setIsAvailableSale(Integer isAvailableSale) {
        this.isAvailableSale = isAvailableSale;
    }

    public List<Integer> getOrgIdArray() {
        return orgIdArray;
    }

    public void setOrgIdArray(List<Integer> orgIdArray) {
        this.orgIdArray = orgIdArray;
    }
}
