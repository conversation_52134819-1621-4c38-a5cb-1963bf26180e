package com.pricecenter.dto;

import java.io.Serializable;

public class ContractPriceListPageQueryDto extends AbstractPageDto implements Serializable {
    //商品名称
    private String googsName;
    //客户名称
    private String customName;
    //商品合约类别
    private String contractContegory;

    //归属平台
    private Integer belongPlatform;

    private Integer isMaintain;

    public Integer getIsMaintain() {
        return isMaintain;
    }

    public void setIsMaintain(Integer isMaintain) {
        this.isMaintain = isMaintain;
    }

    public String getGoogsName() {
        return googsName;
    }

    public void setGoogsName(String googsName) {
        this.googsName = googsName;
    }

    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getContractContegory() {
        return contractContegory;
    }

    public void setContractContegory(String contractContegory) {
        this.contractContegory = contractContegory;
    }

    public Integer getBelongPlatform() {
        return belongPlatform;
    }

    public void setBelongPlatform(Integer belongPlatform) {
        this.belongPlatform = belongPlatform;
    }
}
