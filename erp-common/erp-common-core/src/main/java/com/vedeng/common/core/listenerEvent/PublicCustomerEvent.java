package com.vedeng.common.core.listenerEvent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 公海划拨
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PublicCustomerEvent {
    private Integer type;
    private Integer traderId;
    private Integer single_to_user;
    private Integer from_user;
    private Integer batch_to_user;
    private Integer provinceId;
    private Integer cityId;
    private Integer zoneId;
}
