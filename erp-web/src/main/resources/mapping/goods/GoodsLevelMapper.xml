<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsLevelMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.entity.GoodsLevelDo">
        <id column="ID" jdbcType="INTEGER" property="id" />
        <result column="UNIQUE_IDENTIFIER" jdbcType="CHAR" property="uniqueIdentifier" />
        <result column="LEVEL_NAME" jdbcType="VARCHAR" property="levelName" />
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
        <result column="ORDINAL" jdbcType="INTEGER" property="ordinal" />
        <result column="IS_DELETED" jdbcType="BIT" property="isDeleted" />
        <result column="ALLOW_SYNC_FRONTEND" jdbcType="TINYINT" property="allowSyncFrontend" />
        <result column="CREATOR_ID" jdbcType="INTEGER" property="creatorId" />
        <result column="UPDATER_ID" jdbcType="INTEGER" property="updaterId" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="SPU_VALIDATION_RULES" jdbcType="VARCHAR" property="spuValidationRules" />
        <result column="SKU_VALIDATION_RULES" jdbcType="VARCHAR" property="skuValidationRules" />
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from V_GOODS_LEVEL
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.vedeng.goods.model.entity.GoodsLevelDo">
        insert into V_GOODS_LEVEL (ID, UNIQUE_IDENTIFIER, LEVEL_NAME,
                                   DESCRIPTION, ORDINAL, IS_DELETED,
                                   ALLOW_SYNC_FRONTEND, CREATOR_ID, UPDATER_ID,
                                   ADD_TIME, MOD_TIME)
        values (#{id,jdbcType=INTEGER}, #{uniqueIdentifier,jdbcType=CHAR}, #{levelName,jdbcType=VARCHAR},
                #{description,jdbcType=VARCHAR}, #{ordinal,jdbcType=INTEGER}, #{isDeleted,jdbcType=BIT},
                #{allowSyncFrontend,jdbcType=TINYINT}, #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER},
                #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.entity.GoodsLevelDo">
        update V_GOODS_LEVEL
        set UNIQUE_IDENTIFIER = #{uniqueIdentifier,jdbcType=CHAR},
            LEVEL_NAME = #{levelName,jdbcType=VARCHAR},
            DESCRIPTION = #{description,jdbcType=VARCHAR},
            ORDINAL = #{ordinal,jdbcType=INTEGER},
            IS_DELETED = #{isDeleted,jdbcType=BIT},
            ALLOW_SYNC_FRONTEND = #{allowSyncFrontend,jdbcType=TINYINT},
            CREATOR_ID = #{creatorId,jdbcType=INTEGER},
            UPDATER_ID = #{updaterId,jdbcType=INTEGER},
            ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select ID, UNIQUE_IDENTIFIER, LEVEL_NAME, DESCRIPTION, ORDINAL, IS_DELETED, ALLOW_SYNC_FRONTEND,
               CREATOR_ID, UPDATER_ID, ADD_TIME, MOD_TIME
        from V_GOODS_LEVEL
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select ID, UNIQUE_IDENTIFIER, LEVEL_NAME, DESCRIPTION, ORDINAL, IS_DELETED, ALLOW_SYNC_FRONTEND,
               CREATOR_ID, UPDATER_ID, ADD_TIME, MOD_TIME
        from V_GOODS_LEVEL
    </select>
    <select id="getGoodsLevelVoBySkuId" resultType="com.vedeng.goods.model.vo.GoodsLevelVo">
        SELECT
            T2.ID,
            T2.UNIQUE_IDENTIFIER,
            T2.LEVEL_NAME,
            T2.DESCRIPTION,
            T2.ORDINAL,
            T2.IS_DELETED,
            T2.ALLOW_SYNC_FRONTEND,
            T2.CREATOR_ID,
            T2.UPDATER_ID,
            T2.ADD_TIME,
            T2.MOD_TIME
        FROM
            V_CORE_SKU T1
            INNER JOIN V_GOODS_LEVEL T2 ON T1.GOODS_LEVEL_NO = T2.ID
        WHERE
            T2.IS_DELETED = 0
            AND T1.SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>
    <select id="countGoodsByGoodsLevelNo" parameterType="map" resultType="integer">
        SELECT count(*) FROM V_CORE_SKU WHERE GOODS_LEVEL_NO = #{goodsLevelNo, jdbcType=INTEGER} AND `STATUS` = 1
        UNION ALL
        SELECT count(*) FROM V_CORE_SKU
        WHERE
              SKU_ID IN (
                SELECT BUZ_ID FROM T_TODO_LIST WHERE BUZ_TYPE IN
                <foreach item="item" collection="todoTypeList" separator="," open="(" close=")" index="">
                    #{item,jdbcType=INTEGER}
                </foreach>
                AND STATUS = 0
        )
        AND GOODS_LEVEL_NO = ${goodsLevelNo} AND `STATUS` = 1
    </select>
    <select id="countGoodsByPositionNo" parameterType="map" resultType="integer">
        SELECT count(*) FROM V_CORE_SKU WHERE GOODS_POSITION_NO = #{goodsPositionNo, jdbcType=INTEGER} AND `STATUS` = 1
        <foreach item="goodsLevelNo" collection="goodsLevelNoList" separator="" open="" close="" index="">
        UNION ALL
        SELECT count(*)
        FROM V_CORE_SKU
        WHERE GOODS_POSITION_NO = #{goodsPositionNo,jdbcType=INTEGER} AND GOODS_LEVEL_NO = #{goodsLevelNo,jdbcType=INTEGER} AND `STATUS` = 1
        </foreach>
    </select>
    <select id="selectByPrimaryKeyWithBlob" resultType="com.vedeng.goods.model.entity.GoodsLevelDo">
        select ID, UNIQUE_IDENTIFIER, LEVEL_NAME,
               DESCRIPTION,
               ORDINAL,
               IS_DELETED,
               ALLOW_SYNC_FRONTEND ,SKU_VALIDATION_RULES,SPU_VALIDATION_RULES
        from V_GOODS_LEVEL
        where ID = #{id,jdbcType=INTEGER} and IS_DELETED=0
    </select>
</mapper>