<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.system.dao.StandardCategoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.StandardCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    <id column="STANDARD_CATEGORY_ID" jdbcType="INTEGER" property="standardCategoryId" />
    <result column="PARENT_ID" jdbcType="INTEGER" property="parentId" />
    <result column="CATEGORY_NAME" jdbcType="VARCHAR" property="categoryName" />
    <result column="TREENODES" jdbcType="VARCHAR" property="treenodes" />
    <result column="STATUS" jdbcType="BIT" property="status" />
    <result column="LEVEL" jdbcType="BIT" property="level" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="ICON_DOMAIN" jdbcType="VARCHAR" property="iconDomain" />
    <result column="ICON_URI" jdbcType="VARCHAR" property="iconUri" />
    <result column="DESCRIPTION" jdbcType="VARCHAR" property="description" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    
   	<collection property="standardCategoryList" ofType="com.vedeng.goods.model.StandardCategory">
   		<id column="STANDARD_CATEGORY_ID_TWO" jdbcType="INTEGER" property="standardCategoryId"/>
   		<result column="CATEGORY_NAME_TWO" jdbcType="VARCHAR" property="categoryName" />
   		<result column="LEVEL_TWO" jdbcType="BIT" property="level" />
   		<collection property="standardCategoryList" ofType="com.vedeng.goods.model.StandardCategory">
	   		<id column="STANDARD_CATEGORY_ID_THREE" jdbcType="INTEGER" property="standardCategoryId"/>
	   		<result column="CATEGORY_NAME_THREE" jdbcType="VARCHAR" property="categoryName" />
	   		<result column="LEVEL_THREE" jdbcType="BIT" property="level" />
   		</collection>
   	</collection>
    
    
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    STANDARD_CATEGORY_ID, PARENT_ID, CATEGORY_NAME, TREENODES, STATUS, LEVEL, SORT, ICON_DOMAIN, 
    ICON_URI, DESCRIPTION, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_STANDARD_CATEGORY
    where STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    delete from T_STANDARD_CATEGORY
    where STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.StandardCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    insert into T_STANDARD_CATEGORY (STANDARD_CATEGORY_ID, PARENT_ID, CATEGORY_NAME, 
      TREENODES, STATUS, LEVEL, SORT, 
      ICON_DOMAIN, ICON_URI, DESCRIPTION, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER)
    values (#{standardCategoryId,jdbcType=INTEGER}, #{parentId,jdbcType=INTEGER}, #{categoryName,jdbcType=VARCHAR}, 
      #{treenodes,jdbcType=VARCHAR}, #{status,jdbcType=BIT}, #{level,jdbcType=BIT}, #{sort,jdbcType=INTEGER}, 
      #{iconDomain,jdbcType=VARCHAR}, #{iconUri,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.StandardCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    insert into T_STANDARD_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="standardCategoryId != null">
        STANDARD_CATEGORY_ID,
      </if>
      <if test="parentId != null">
        PARENT_ID,
      </if>
      <if test="categoryName != null">
        CATEGORY_NAME,
      </if>
      <if test="treenodes != null">
        TREENODES,
      </if>
      <if test="status != null">
        STATUS,
      </if>
      <if test="level != null">
        LEVEL,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="iconDomain != null">
        ICON_DOMAIN,
      </if>
      <if test="iconUri != null">
        ICON_URI,
      </if>
      <if test="description != null">
        DESCRIPTION,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="standardCategoryId != null">
        #{standardCategoryId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="treenodes != null">
        #{treenodes,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=BIT},
      </if>
      <if test="level != null">
        #{level,jdbcType=BIT},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="iconDomain != null">
        #{iconDomain,jdbcType=VARCHAR},
      </if>
      <if test="iconUri != null">
        #{iconUri,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.StandardCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    update T_STANDARD_CATEGORY
    <set>
      <if test="parentId != null">
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="categoryName != null">
        CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="treenodes != null">
        TREENODES = #{treenodes,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="level != null">
        LEVEL = #{level,jdbcType=BIT},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="iconDomain != null">
        ICON_DOMAIN = #{iconDomain,jdbcType=VARCHAR},
      </if>
      <if test="iconUri != null">
        ICON_URI = #{iconUri,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        DESCRIPTION = #{description,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.StandardCategory">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Mar 28 14:00:11 CST 2019.
    -->
    update T_STANDARD_CATEGORY
    set PARENT_ID = #{parentId,jdbcType=INTEGER},
      CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      TREENODES = #{treenodes,jdbcType=VARCHAR},
      STATUS = #{status,jdbcType=BIT},
      LEVEL = #{level,jdbcType=BIT},
      SORT = #{sort,jdbcType=INTEGER},
      ICON_DOMAIN = #{iconDomain,jdbcType=VARCHAR},
      ICON_URI = #{iconUri,jdbcType=VARCHAR},
      DESCRIPTION = #{description,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER}
  </update>
  
  
  <select id="getNewStandardCategory" resultMap="BaseResultMap">
	SELECT
		a.STANDARD_CATEGORY_ID,
		a.CATEGORY_NAME,
		a.`LEVEL`,
		a.PARENT_ID,
		b.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID_TWO,
		b.CATEGORY_NAME AS CATEGORY_NAME_TWO,
		b.`LEVEL` AS LEVEL_TWO,
		c.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID_THREE,
		c.CATEGORY_NAME AS CATEGORY_NAME_THREE,
		c.`LEVEL` AS LEVEL_THREE
	FROM
		T_STANDARD_CATEGORY a
	LEFT JOIN T_STANDARD_CATEGORY b ON a.STANDARD_CATEGORY_ID = b.PARENT_ID AND b.`STATUS` = #{status, jdbcType=INTEGER}
	LEFT JOIN T_STANDARD_CATEGORY c ON b.STANDARD_CATEGORY_ID = c.PARENT_ID AND c.`STATUS` = #{status, jdbcType=INTEGER}
	WHERE
		a.`STATUS` = #{status, jdbcType=INTEGER}

	AND a.PARENT_ID = #{parentId, jdbcType=INTEGER}

	ORDER BY a.STANDARD_CATEGORY_ID DESC, b.STANDARD_CATEGORY_ID DESC, c.STANDARD_CATEGORY_ID DESC
  </select>
  
  <select id="getParentStandardCateList" parameterType="com.vedeng.goods.model.StandardCategory" resultMap="BaseResultMap">
		SELECT *
		FROM T_STANDARD_CATEGORY A
		WHERE     A.STATUS = 1
			<if test="standardCategoryId!=null and standardCategoryId!=''">
		      AND A.PARENT_ID = (SELECT B.PARENT_ID
		                         FROM T_STANDARD_CATEGORY B
		                         WHERE B.STANDARD_CATEGORY_ID = #{standardCategoryId,jdbcType=INTEGER} AND B.STATUS = 1)
			</if>
			<if test="parentId!=null">
		      AND A.PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
		order by A.STANDARD_CATEGORY_ID DESC
	</select>
	
	<!-- 获取新国标分类的所有分类的名称，显示到最小分类  by Franlin at 2018/06/01 -->
	<select id="getNewStandardCategoryList" parameterType="com.vedeng.goods.model.StandardCategory" resultMap="BaseResultMap">
		SELECT 
			T.*
		FROM(
			SELECT 
				DISTINCT IFNULL(g.STANDARD_CATEGORY_ID, IFNULL(t.STANDARD_CATEGORY_ID, c.STANDARD_CATEGORY_ID)) as STANDARD_CATEGORY_ID, 
				IFNULL(g.`LEVEL`, IFNULL(t.`LEVEL`, c.`LEVEL`)) as `LEVEL`,
				IFNULL(g.SORT, IFNULL(t.SORT, c.SORT)) as SORT,
				CONCAT(IFNULL(c.CATEGORY_NAME, ""), "->", IFNULL(t.CATEGORY_NAME, ""), "->", IFNULL(g.CATEGORY_NAME, "")) as CATEGORY_NAME, 
				CONCAT(IFNULL(c.PARENT_ID, ""), " ", IFNULL(t.PARENT_ID, ""), " ", IFNULL(g.PARENT_ID, "")) as PARENT_ID
			FROM T_STANDARD_CATEGORY c
				LEFT JOIN T_STANDARD_CATEGORY t ON t.PARENT_ID = c.STANDARD_CATEGORY_ID AND t.`STATUS` = 1
				LEFT JOIN T_STANDARD_CATEGORY g ON g.PARENT_ID = t.STANDARD_CATEGORY_ID AND g.`STATUS` = 1
			WHERE  c.`LEVEL` = 1 AND c.STATUS = 1) T
		WHERE  1 = 1 
			<if test="categoryName != null and categoryName !='' ">
				AND T.CATEGORY_NAME like CONCAT('%',#{categoryName, jdbcType=VARCHAR},'%' )
			</if>
			<if test="standardCategoryId != null and standardCategoryId !='' ">
				AND T.STANDARD_CATEGORY_ID = #{standardCategoryId, jdbcType=INTEGER}
			</if>
			<if test="level != null and level !='' ">
				AND T.LEVEL = #{level, jdbcType=INTEGER}
			</if>
		order by T.PARENT_ID, T.SORT
	</select>
	
	<!-- 查询产品分类信息列表(不分页) -->
	<select id="getStandardCategoryList" parameterType="com.vedeng.goods.model.StandardCategory" resultMap="BaseResultMap">
		SELECT A.*
		FROM T_STANDARD_CATEGORY A
		<where>
			STATUS = 1
			<if test="standardCategoryId!=null and standardCategoryId!=''">
				AND A.STANDARD_CATEGORY_ID like CONCAT('%',#{standardCategoryId,jdbcType=INTEGER},'%' )
			</if>
			<if test="categoryName!=null and categoryName!=''">
				AND A.CATEGORY_NAME like CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
			</if>
			<if test="parentId!=null"><!-- mybatis对于Integer类型，!=''即为!=0 -->
				AND A.PARENT_ID = #{parentId,jdbcType=INTEGER}
			</if>
		</where>
		ORDER BY A.STANDARD_CATEGORY_ID DESC
	</select>
	
	<select id="selectByParentId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
	    SELECT 
	    	<include refid="Base_Column_List" />
	    FROM T_STANDARD_CATEGORY
	    WHERE
	       	STATUS = 1
	    	AND PARENT_ID = #{parentId, jdbcType=INTEGER}
	    <!-- 主键升序，排序降序 -->
	    ORDER BY STANDARD_CATEGORY_ID, SORT DESC
	</select>

    <select id="getNewStandardCategoryByName" resultType="com.vedeng.goods.model.StandardCategory">
        SELECT * FROM (
        SELECT
            IF(
                c.CATEGORY_NAME = '' OR c.CATEGORY_NAME IS NULL,
              IF(
                  b.CATEGORY_NAME = '' OR b.CATEGORY_NAME IS NULL,
                IF(a.CATEGORY_NAME = '' OR a.CATEGORY_NAME IS NULL, '--', a.CATEGORY_NAME),
                CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME)
              ),
              CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME, '>', c.CATEGORY_NAME)
            ) AS categoryName,
            a.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID3,
            b.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID1,
            c.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID2,
            IF(
                c.STANDARD_CATEGORY_ID IS NULL,
              IF(
                  b.STANDARD_CATEGORY_ID IS NULL,
                IF(a.STANDARD_CATEGORY_ID IS NULL, 0, a.STANDARD_CATEGORY_ID),
                b.STANDARD_CATEGORY_ID
              ),
              c.STANDARD_CATEGORY_ID
            ) AS standardCategoryId
        FROM
            T_STANDARD_CATEGORY a
            LEFT JOIN T_STANDARD_CATEGORY b ON b.PARENT_ID = a.STANDARD_CATEGORY_ID AND b.`STATUS`= #{status, jdbcType=INTEGER}
            LEFT JOIN T_STANDARD_CATEGORY c ON c.PARENT_ID = b.STANDARD_CATEGORY_ID AND c.`STATUS`= #{status, jdbcType=INTEGER}
        WHERE
        a.CATEGORY_NAME LIKE CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
        AND a.`LEVEL` = 1
        AND a.`STATUS` = #{status, jdbcType=INTEGER}
        UNION ALL
        SELECT
            IF(
                c.CATEGORY_NAME = '' OR c.CATEGORY_NAME IS NULL,
              IF(
                  b.CATEGORY_NAME = '' OR b.CATEGORY_NAME IS NULL,
                IF(a.CATEGORY_NAME = '' OR a.CATEGORY_NAME IS NULL, '--', a.CATEGORY_NAME),
                CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME)
              ),
              CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME, '>', c.CATEGORY_NAME)
            ) AS categoryName,
            a.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID3,
            b.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID1,
            c.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID2,
            IF(
                c.STANDARD_CATEGORY_ID IS NULL,
              IF(
                  b.STANDARD_CATEGORY_ID IS NULL,
                IF(a.STANDARD_CATEGORY_ID IS NULL, 0, a.STANDARD_CATEGORY_ID),
                b.STANDARD_CATEGORY_ID
              ),
              c.STANDARD_CATEGORY_ID
            ) AS standardCategoryId
        FROM
            T_STANDARD_CATEGORY b
            LEFT JOIN T_STANDARD_CATEGORY a ON a.STANDARD_CATEGORY_ID = b.PARENT_ID AND a.`STATUS`= #{status, jdbcType=INTEGER}
            LEFT JOIN T_STANDARD_CATEGORY c ON c.PARENT_ID = b.STANDARD_CATEGORY_ID AND c.`STATUS`= #{status, jdbcType=INTEGER}
        WHERE
        b.CATEGORY_NAME LIKE CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
        AND b.`LEVEL` = 2
        AND b.`STATUS`= #{status, jdbcType=INTEGER}
        UNION ALL
        SELECT
            IF(
                c.CATEGORY_NAME = '' OR c.CATEGORY_NAME IS NULL,
              IF(
                  b.CATEGORY_NAME = '' OR b.CATEGORY_NAME IS NULL,
                IF(a.CATEGORY_NAME = '' OR a.CATEGORY_NAME IS NULL, '--', a.CATEGORY_NAME),
                CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME)
              ),
              CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME, '>', c.CATEGORY_NAME)
            ) AS categoryName,
            a.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID3,
            b.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID1,
            c.STANDARD_CATEGORY_ID AS STANDARD_CATEGORY_ID2,
            IF(
                c.STANDARD_CATEGORY_ID IS NULL,
              IF(
                  b.STANDARD_CATEGORY_ID IS NULL,
                IF(a.STANDARD_CATEGORY_ID IS NULL, 0, a.STANDARD_CATEGORY_ID),
                b.STANDARD_CATEGORY_ID
              ),
              c.STANDARD_CATEGORY_ID
            ) AS standardCategoryId
        FROM
            T_STANDARD_CATEGORY c
            LEFT JOIN T_STANDARD_CATEGORY b ON b.STANDARD_CATEGORY_ID = c.PARENT_ID AND b.`STATUS`= #{status, jdbcType=INTEGER}
            LEFT JOIN T_STANDARD_CATEGORY a ON b.PARENT_ID = a.STANDARD_CATEGORY_ID AND a.`STATUS`= #{status, jdbcType=INTEGER}
        WHERE
        c.CATEGORY_NAME LIKE CONCAT('%',#{categoryName,jdbcType=VARCHAR},'%' )
        AND c.`LEVEL` = 3
        AND c.`STATUS`= #{status, jdbcType=INTEGER}
        ) tt
        GROUP BY tt.categoryName
        ORDER BY tt.STANDARD_CATEGORY_ID3 DESC, tt.STANDARD_CATEGORY_ID1 DESC, tt.STANDARD_CATEGORY_ID2 DESC
    </select>


    <select id="getStandardCategoryStrMap" resultType="java.util.Map">
        SELECT
        a.STANDARD_CATEGORY_ID AS standardCategoryId,
        IF(
        c.CATEGORY_NAME = '' OR c.CATEGORY_NAME IS NULL,
        IF(
        b.CATEGORY_NAME = '' OR b.CATEGORY_NAME IS NULL,
        IF(a.CATEGORY_NAME = '' OR a.CATEGORY_NAME IS NULL, '--', a.CATEGORY_NAME),
        CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME)
        ),
        CONCAT(c.CATEGORY_NAME, '>', b.CATEGORY_NAME, '>', a.CATEGORY_NAME)
        ) AS categoryName
        FROM
        T_STANDARD_CATEGORY a
        LEFT JOIN T_STANDARD_CATEGORY b ON a.PARENT_ID = b.STANDARD_CATEGORY_ID AND b.`STATUS` = 1
        LEFT JOIN T_STANDARD_CATEGORY c ON b.PARENT_ID = c.STANDARD_CATEGORY_ID AND c.`STATUS` = 1
        WHERE
        a.STANDARD_CATEGORY_ID IN
        <foreach collection="list" item="first" open="(" close=")" separator=",">
            #{first.newStandardCategoryId, jdbcType=INTEGER}
        </foreach>
        ORDER BY a.STANDARD_CATEGORY_ID DESC
    </select>

    <resultMap id="TraderMedicalCategoryMap" type="com.vedeng.trader.model.TraderMedicalCategory">
        <result column="SYS_OPTION_DEFINITION_ID" jdbcType="INTEGER" property="traderMedicalCategoryId" />
    </resultMap>

    <select id="getCategoryByChnMedical" resultMap="TraderMedicalCategoryMap">
        SELECT SYS_OPTION_DEFINITION_ID from T_STANDARD_CATEGORY where CATEGORY_NAME LIKE CONCAT(#{chnMedical,jdbcType=INTEGER},'%')
    </select>

  <select id="selectNameById" resultType="java.lang.String">
      SELECT
      IF(
      c.CATEGORY_NAME = '' OR c.CATEGORY_NAME IS NULL,
      IF(
      b.CATEGORY_NAME = '' OR b.CATEGORY_NAME IS NULL,
      IF(a.CATEGORY_NAME = '' OR a.CATEGORY_NAME IS NULL, '--', a.CATEGORY_NAME),
      CONCAT(a.CATEGORY_NAME, '>', b.CATEGORY_NAME)
      ),
      CONCAT(c.CATEGORY_NAME, '>', b.CATEGORY_NAME, '>', a.CATEGORY_NAME)
      ) AS categoryName
      FROM
      T_STANDARD_CATEGORY a
      LEFT JOIN T_STANDARD_CATEGORY b ON a.PARENT_ID = b.STANDARD_CATEGORY_ID AND b.`STATUS` = 1
      LEFT JOIN T_STANDARD_CATEGORY c ON b.PARENT_ID = c.STANDARD_CATEGORY_ID AND c.`STATUS` = 1
      WHERE
      a.STANDARD_CATEGORY_ID =
          #{first.newStandardCategoryId, jdbcType=INTEGER}
    </select>
</mapper>