package com.vedeng.trader.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * 账期申请检索条件dto
 */
@Data
public class CustomerBillPeriodApplyListQueryDto {

    /**
     * 客户ID
     */
    private List<Long> customerIdList;

    /**
     * 账期申请审核状态，0审核中，1审核通过，2审核不通过
     */
    private Integer checkStatus;

    /**
     * 账期有效期开始时间
     */
    private Long billPeriodStart;

    /**
     * 账期有效期截止时间
     */
    private Long billPeriodEnd;


    /**
     * 账期类型  @CustomerBillPeriodTypeEnum
     */
    private Integer billPeriodType;

    //创建人Id
    private Integer creatorId;

    /**
     * 客户账期申请id集合
     */
    private List<Long> customerBillPeriodApplyIdList;
}
