// 手机联想 联系人
Vue.component('ui-phone-related', {
    template: `
        <div class="vd-ui-phone-related">
            <div class="vd-ui-input" ref="input-wrap" :style="{'width': width}">
                <template v-if="novalid">
                    <ui-input
                        :type="inputType"
                        :maxlength="maxlength"
                        :placeholder="placeholder"
                        v-bind="$attrs"
                        v-model="inputValue"
                        @blur="handlerBlur"
                        @input.native="handleInput"
                        @focus="handleFocus"
                        width="100%"
                        autocomplete="off"
                    ></ui-input>
                </template>
                <template v-else>
                    <ui-input
                        :type="inputType"
                        :maxlength="maxlength"
                        :placeholder="placeholder"
                        v-bind="$attrs"
                        v-model="inputValue"
                        @blur="handlerBlur"
                        @input.native="handleInput"
                        @focus="handleFocus"
                        width="100%"
                        autocomplete="off"
                        :errorable="!!errorMsg" 
                        :error-msg="errorMsg" 
                    ></ui-input>
                </template>
            </div>

            <transition>
                <ul
                    class="vd-ui-search-related"
                    @click.stop
                    v-show="rotate"
                    :style="{'width': width}"
                >
                    <template v-if="loading">
                        <li class="loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </li>
                    </template>

                    <template v-else-if="loadingFail">
                        <li class="failed-li">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                            <span class="reload" @click="handleReload">重新加载</span>
                        </li>
                    </template>

                    <template v-else>
                        <div class="search-list" v-if="relateList.length">
                            <template v-for="(item, index) in relateList">
                                <div 
                                    class="sr-item" 
                                    @click.stop="chooseErp(item)"
                                    :key="index"
                                    v-if="item.name && item.mobile"
                                >
                                    <template v-if="type==='contact'">
                                        <div class="name text-line-1" v-html="light(item.name)"></div>
                                        <div class="mobile">{{ item.mobile }}</div>
                                    </template>
                                    <template v-else>
                                        <div class="name text-line-1">{{item.name}}</div>
                                        <div class="mobile" v-html="light(item.mobile)"></div>
                                    </template>
                                </div>
                            </template>
                        </div>
                        <li class="empty-li" v-else>
                            <p>暂无数据</p>
                        </li>
                    </template>
                </ul>
            </transition>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        maxlength: {
            type: Number,
            default: 11
        },
        // 已建档客户id： 有id才联想，否则只做简单输入
        traderId: {
            type: [String, Number],
            default: ''
        },

        placeholder: {
            type: String,
            default: '手机：仅支持11位手机号'
        },
        width: {
            type: String,
            default: '300px'
        },
        errorMsg: {
            type: String
        },
        // 是否需要校验
        novalid: {
            type: Boolean,
            default: false
        },
        // 是否精确匹配:  true精确 [输入满11位才查询]  false非精确
        accurateMatch: {
            type: Boolean,
            default: true,
        },
        //这个组件其实应该是个输入推荐的，目前封装成手机号特有的，和业务结合太深，开发时间有限，联系人推荐组件通过传参类型来判断
        type: {
            type: String,
            default: '',  //contact:联系人
        }
    },
    data() {
        return {
            inputValue: '',
            // erp本地匹配
            rotate: false,
            relateList: [], // 搜索联想
            loading: false,
            loadingFail: false,
            timer: null,
            inputType: 'number'
        }
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        rotate (newV) {
            if (!newV) {
                this.loading = false;
                this.loadingFail = false;
            }
        }
    },
    computed: {
        light () {
            return (name) => {
                if (!this.inputValue) return name;
                const regExp = new RegExp(this.inputValue, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.inputValue}</font>`);
                return name;
            }
        },
        tags () {
            let arr = this.companyInfo.tags.split(';') || [];
            return arr
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    created() {
        if(this.type === 'contact') {
            this.inputType = 'text';
        }
    },
    mounted() {
        this.inputValue = this.value;
        // document.addEventListener('click', (e)=>{
        //     if (!this.$el.contains(e.target)) {
        //         console.log('rotate:', this.rotate);
        //         this.rotate = false;
        //     }
        // })

        let _this = this;
        document.addEventListener('click', (e) => {
            let inputWrap1 = _this.$refs['input-wrap']; // input
            let panelWrap = _this.$refs['pickers']; // 日期panel层
            let include1 = false; // input 是否包含点击元素
            let include2 = false; // 面板是否包含点击元素

            if (inputWrap1 && inputWrap1.contains(e.target)) {
                include1 = true;
            }
            if (panelWrap && panelWrap.contains(e.target)) {
                include2 = true;
            }

            if (_this.rotate && !include1 && !include2) {
                _this.rotate = false;
            }
        }, true); // true: 从外向内 由根节点向点击元素执行
    },
    methods: {
        handlerBlur () {
            this.$emit('blur');
        },
        // 输入停顿300毫秒后搜素
        handleInput (event) {
            // if (event.inputType != 'insertCompositionText') {
                let val = event.target.value.trim();
                this.$emit('input', val);
                this.$emit('change', {});

                this.timer && clearTimeout(this.timer);
                if (this.traderId) {
                    this.timer = setTimeout(()=> {
                        this.getRelatelist(val);
                    }, 300)
                }
            // }
        },
        handleFocus (event) {
            let val = event.target.value.trim();

            this.timer && clearTimeout(this.timer);
            if (this.traderId) {
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300)
            }
        },
        getRelatelist (val) {
            this.rotate = true;
            this.loading = true;
            this.loadingFail = false;

            let valueParams = `&mobile=${val}`;

            if(this.type === 'contact') {
                valueParams = `&name=${val}`;
            }

            let query = `?traderId=${this.traderId}${valueParams}&pageSize=100&accurateMatch=${this.accurateMatch? 1: 0}`;
            this.$axios.post(`/crm/traderContact/profile/page${query}`).then(({data}) => {
            // this.$axios.post(`/crm/traderContact/profile/page?traderId=${this.traderId}&mobile=${val}&pageSize=100`, {
            //     headers: {
            //         timeStramp: new Date().getTime()
            //     }
            // }).then(({data}) => {
                this.loading = false;
                console.log('data:', data);
                if (data.success) {
                    this.relateList = data.data.list || [];
                } else {
                    this.loadingFail = true;
                }
            }).catch(err=> {
                this.loading = false;
                this.loadingFail = true;
            })
        },

        // 重新加载
        handleReload () {
            this.getRelatelist(this.inputValue);
        },

        // 选择已建档客户
        async chooseErp (item) {
            console.log('item：', item);
            let value = (this.type === 'contact' ? item.name : item.mobile) || '';
            this.inputValue = value;
            this.rotate = false;

            this.$emit("input", value);
            this.$emit('change', {
                traderId: item.traderId,
                mobile: item.mobile,
                traderContactId: item.traderContactId,
                traderContactName: item.name,
                choosed: true
            });
        },
    }
})