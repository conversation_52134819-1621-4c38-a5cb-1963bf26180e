package com.vedeng.erp.system.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.system.domain.dto.FlowNodeDto;
import com.vedeng.erp.system.domain.dto.FlowOrderDto;
import com.vedeng.erp.system.domain.entity.FlowNodeEntity;
import com.vedeng.erp.system.domain.entity.FlowOrderEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @description: dto entity转换类
 * @date 2022/7/21
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface FlowNodeConvertor extends BaseMapStruct<FlowNodeEntity, FlowNodeDto> {
}
