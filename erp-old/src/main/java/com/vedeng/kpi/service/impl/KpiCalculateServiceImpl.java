package com.vedeng.kpi.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.common.util.DateUtil;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.kpi.dao.KpiOrderLogMapper;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.replica.dao.*;
import com.vedeng.kpi.service.KpiCalculateService;
import com.vedeng.kpi.share.KpiUtils;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.saleperformance.model.SalesPerformanceDept;
import com.vedeng.trader.model.RTraderJUser;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date created in 2020/6/15 10:05
 */
@Service
public class KpiCalculateServiceImpl implements KpiCalculateService {

    private final static Logger logger = LoggerFactory.getLogger(KpiCalculateServiceImpl.class);

    @Value("${kpi.specialSku}")
    private String specialSku;

    @Autowired
    @Qualifier("saleorderReplicaMapper")
    private SaleorderReplicaMapper saleorderReplicaMapper;

    @Autowired
    @Qualifier("capitalBillReplicaMapper")
    private CapitalBillReplicaMapper capitalBillReplicaMapper;

    @Resource
    private KpiOrderLogMapper kpiOrderLogMapper;

    @Resource
    private QuoteorderMapper quoteorderMapper;

    @Autowired
    @Qualifier("afterSalesReplicaMapper")
    private AfterSalesReplicaMapper afterSalesReplicaMapper;

    @Autowired
    @Qualifier("salesPerformanceDeptReplicaMapper")
    private SalesPerformanceDeptReplicaMapper salesPerformanceDeptReplicaMapper;

    @Autowired
    @Qualifier("RTraderJUserReplicaMapper")
    private RTraderJUserReplicaMapper rTraderJUserReplicaMapper;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Value("${standard_rate_start_time}")
    private Long standardRateStartTime;

    @Override
    public void monitorDataChange(CanalEntry.RowChange rowChange, String tableName, Map<String, String> columnValueMap, Long executeTime) {
        logger.info("五行数据monitorDataChange：rowChange:{},tableName:{},columnValueMap:{},executeTime:{}",new Object[]{rowChange,tableName,columnValueMap,executeTime});
        //新增订单流水
        boolean addCapitalBill = rowChange.getEventType().equals(CanalEntry.EventType.INSERT) && "T_CAPITAL_BILL_DETAIL".equalsIgnoreCase(tableName) && "1".equals(columnValueMap.get("ORDER_TYPE"));

        //订单修改手填成本价
        boolean costPriceModified = rowChange.getEventType().equals(CanalEntry.EventType.UPDATE) && "T_SALEORDER_GOODS".equalsIgnoreCase(tableName) && columnValueMap.containsKey("REFERENCE_COST_PRICE");

        //售后单态更新为已完结
        boolean hasOverOfAfterSales = rowChange.getEventType().equals(CanalEntry.EventType.UPDATE) && "T_AFTER_SALES".equalsIgnoreCase(tableName) && "2".equals(columnValueMap.get("ATFER_SALES_STATUS"));

        //售后单审核状态更新为审核通过，并且售后单退款状态为无退款
        //考虑到退货无需退款的状态，需要重新计算业绩
        boolean noRefundOfAfterSales = rowChange.getEventType().equals(CanalEntry.EventType.UPDATE) && "T_AFTER_SALES".equalsIgnoreCase(tableName) && "2".equals(columnValueMap.get("STATUS"));

        if (noRefundOfAfterSales){
            logger.info("noRefundOfAfterSales");
            Integer refundAmountStatus = afterSalesReplicaMapper.getRefundStatusOfAfterSales(Integer.valueOf(columnValueMap.get("AFTER_SALES_ID")));
            if (refundAmountStatus != null && refundAmountStatus == 0){
                //无退款
                Optional.ofNullable(saleorderReplicaMapper.getSaleorderByAfterSalesId(Integer.valueOf(columnValueMap.get("AFTER_SALES_ID"))))
                        .ifPresent(this::checkSaleorderPerformanceOfSaleorder);
            }
        }

        if (addCapitalBill){
            logger.info("addCapitalBill");
            //销售订单流水
            String saleorderNo = columnValueMap.get("ORDER_NO");
            logger.info("五行业绩计算---订单：{}新增一条流水",saleorderNo);
            if (saleorderReplicaMapper.getSaleorderDetails(saleorderNo) == null) {
                return;
            }
            checkSaleorderPerformanceOfSaleorder(saleorderReplicaMapper.getSaleorderDetails(saleorderNo));
        }

        if (costPriceModified){
            logger.info("costPriceModified");
            Integer saleorderGoodsId = Integer.valueOf(columnValueMap.get("SALEORDER_GOODS_ID"));
            Saleorder saleorder = saleorderReplicaMapper.getSaleorderDetails(saleorderReplicaMapper.getSaleorderNoBySaleorderGoodsId(saleorderGoodsId));
            if (saleorder == null) {
                return;
            }
            logger.info("五行业绩计算---订单：{}修改订单商品成本价",saleorder.getSaleorderNo());
            //VDERP-3397 判断订单是否计入业绩，如果已经计入业绩，并且计入时间为当月，那么删除该业绩Log，重新计算
            List<KpiOrderLogDo> kpiOrderLogDoList = kpiOrderLogMapper.getKpiOrderOfSaleorderNo(saleorder.getSaleorderNo());
            if (kpiOrderLogDoList.size() > 0 && DateUtil.compareYearAndMonthWithNow(kpiOrderLogDoList.get(0).getKpiDate())){
                logger.info("五行业绩计算---订单：{}修改成本价，重新计算五行业绩",saleorder.getSaleorderNo());
                kpiOrderLogDoList.forEach(item -> kpiOrderLogMapper.deleteByPrimaryKey(item.getId()));
                saleorderMapper.updateNotSalesPerformanceOfSaleorder(saleorder.getSaleorderId());
                saleorder.setIsSalesPerformance(0);
            }
            //修改手填成本，业绩时间按订单流水来计算
            long salesPerformanceTime = checkSaleorderPerformanceOfSaleorder(saleorder);
            calculateAfterSalesKpiBySaleorder(saleorder.getSaleorderNo(),salesPerformanceTime);
        }

        //售后单态更新为已完结
        if (hasOverOfAfterSales){
            logger.info("hasOverOfAfterSales");
            AfterSales afterSales = afterSalesReplicaMapper.getAfterSalesByAfterSalesId(Integer.valueOf(columnValueMap.get("AFTER_SALES_ID")));
            if (afterSales == null){
                return;
            }
            if (saleorderReplicaMapper.getSaleorderDetails(afterSales.getOrderNo()) == null) {
                return;
            }
            logger.info("五行业绩计算---订单：{}进行售后单：{}业绩计算",afterSales.getOrderNo(),afterSales.getAfterSalesNo());
            checkSalesPerformanceOfAfterSales(afterSales,executeTime);
        }


        //当售后退款状态由已退款更新为退款中时，需要删除对应的售后业绩记录
        boolean modifyRefundStatus = rowChange.getEventType().equals(CanalEntry.EventType.UPDATE) && "T_AFTER_SALES_DETAIL".equalsIgnoreCase(tableName) && "4".equals(columnValueMap.get("REFUND_AMOUNT_STATUS"));

        //售后单退款状态更新为退款中
        if (modifyRefundStatus){
            logger.info("modifyRefundStatus");
            //如果该售后单已经计入业绩，那么删除该售后单
            Optional.ofNullable(afterSalesReplicaMapper.getAfterSalesByAfterSalesDetailId(Integer.valueOf(columnValueMap.get("AFTER_SALES_DETAIL_ID"))))
                    .ifPresent(afterSales -> {
                        logger.info("五行业绩计算---订单：{}售后单：{}退款状态更新为退款中，删除原售后单业绩",afterSales.getOrderNo(),afterSales.getAfterSalesNo());
                        kpiOrderLogMapper.getKpiLogByAfterSalesNo(afterSales.getAfterSalesNo()).forEach(
                                item -> kpiOrderLogMapper.deleteByPrimaryKey(item.getId()));
                    });
        }
    }


    private void calculateAfterSalesKpiBySaleorder(String saleorderNo, Long executeTime){
        afterSalesReplicaMapper.getAfterSalesBySaleorderNo(saleorderNo,executeTime)
                .forEach(afterSales -> {
                    if (afterSales.getType() == 539){
                        Integer refundStatus = afterSales.getAfterSalesDetail().getRefundAmountStatus();
                        if (refundStatus != null && refundStatus == 3){
                            //获取售后单的最新一条流水
                            Optional.ofNullable(capitalBillReplicaMapper.getFirstCapitalBillByOrder(afterSales.getAfterSalesNo()))
                                    .ifPresent(capitalBill -> checkSalesPerformanceOfAfterSales(afterSales,capitalBill.getAddTime()));
                        }
                    }
                });
    }

    /**
     * 检查订单是否为特殊订单
     * 订单列表里面的订单同时满足以下两个条件时，就是特殊订单：
     * 1、订单实际金额≥50万；
     * 2、除去AED商品（V276521 ，V500037，V271862，V500926，需要可配置）的总额之后的订单毛利率<5%；
     * @param saleorder 订单
     * @param salePerformanceTime 业绩计入时间
     * @return 结果
     */
    private Boolean checkSpecialSaleOrder(Saleorder saleorder, Long salePerformanceTime) {
        List<String> specialSkuList = Arrays.asList(specialSku.split(","));

        BigDecimal costTotalExceptSpecialSku = BigDecimal.ZERO;
        BigDecimal priceTotalExceptSpecialSku = BigDecimal.ZERO;
        BigDecimal saleorderAmount = BigDecimal.ZERO;

        //VDERP-3397 订单原始毛利率=订单审核通过后，订单中所有产品（包括退货产品）的毛利/订单原金额

        //除去AED商品的总额之后的订单毛利率
        for (SaleorderGoods goods : saleorder.getGoodsList()){

            //如果订单商品手填成本缺失，则不进行业绩计算
            if (goods.getReferenceCostPrice() == null || goods.getReferenceCostPrice().compareTo(new BigDecimal("0")) <= 0){
                logger.info("五行业绩计算---订单：{}中存在商品：{}没有成本价",saleorder.getSaleorderNo(),goods.getSku());
                return true;
            }

            BigDecimal realPrice = getRealPriceOfSku(goods);
            saleorderAmount = saleorderAmount.add(realPrice.multiply(new BigDecimal(goods.getNum())));

            if (!specialSkuList.contains(goods.getSku())){
                costTotalExceptSpecialSku = costTotalExceptSpecialSku.add(goods.getReferenceCostPrice().multiply(new BigDecimal(goods.getNum())));
                priceTotalExceptSpecialSku = priceTotalExceptSpecialSku.add(realPrice.multiply(new BigDecimal(goods.getNum())));
            }
        }

        BigDecimal profitRate = BigDecimal.ZERO;
        if (priceTotalExceptSpecialSku.compareTo(BigDecimal.ZERO) > 0){
            profitRate = priceTotalExceptSpecialSku.subtract(costTotalExceptSpecialSku).divide(priceTotalExceptSpecialSku,8,BigDecimal.ROUND_HALF_UP);
        }
        return saleorderAmount.compareTo(new BigDecimal("500000")) >= 0 &&
                profitRate.compareTo(getStandardRate(salePerformanceTime)) < 0;
    }


    /**
     * VDERP-6330：从2021-05-01开始，五行毛利率计算规则从0.05改为0.06
     * @param salePerformanceTime 业绩计入时间
     * @return 基本毛利率
     */
    private BigDecimal getStandardRate(Long salePerformanceTime){
        BigDecimal rate = new BigDecimal("0.05");
        if (salePerformanceTime > standardRateStartTime){
            rate = new BigDecimal("0.06");
        }
        return rate;
    }

    /**
     * 获取订单商品的实际成交价
     * @param goods 订单商品
     * @return 成交价
     */
    private BigDecimal getRealPriceOfSku(SaleorderGoods goods){
//        if (goods.getPrice() != null && goods.getPrice().compareTo(BigDecimal.ZERO) > 0){
//            return goods.getPrice();
//        } else {
//            return goods.getRealPrice();
//        }
        return goods.getPrice();
    }


    /**
     * 获取订单的实际订单商品信息（除去售后退货情况）
     * @param saleorder 订单
     * @return 订单商品
     */
    private List<SaleorderGoods> getRealSaleorderGoods(Saleorder saleorder, Long executeTime){
        //存在订单没有商品的特殊情况，所以要过滤掉saleorderGoodsId为空的情况
        List<SaleorderGoods> realSaleorderGoodsList = saleorder.getGoodsList().stream()
                .filter(saleorderGoods -> saleorderGoods.getSaleorderGoodsId() != null)
                .map(SaleorderGoods::clone).collect(Collectors.toList());

        //售后退款或者完结的sku数量
        Map<Integer, Integer> afterSalesGoodsMap = afterSalesReplicaMapper.getAfterSalesGoodIdBySaleorderBeforeExecuteTime(saleorder.getSaleorderNo(),executeTime)
                .stream()
                .collect(Collectors.toMap(AfterSalesGoods::getGoodsId,AfterSalesGoods::getNum));

        //除去售后的实际订单商品信息
        realSaleorderGoodsList.forEach(goods -> {
            int countOfSku = goods.getNum();
            if (afterSalesGoodsMap.containsKey(goods.getGoodsId())){
                countOfSku = countOfSku - afterSalesGoodsMap.get(goods.getGoodsId());
            }
            goods.setNum(countOfSku);
        });
        return realSaleorderGoodsList;
    }

    /**
     * 获取订单的毛利率
     * VDERP-3397 订单原始毛利率=订单审核通过后，订单中所有产品（包括退货产品）的毛利/订单原金额*100%
     * @param saleorder 订单详情
     * @return 订单毛利率
     */
    private BigDecimal getProfitRateOfSaleorder(Saleorder saleorder){
        BigDecimal costTotal = new BigDecimal("0");
        BigDecimal priceTotal = new BigDecimal("0");
        for (SaleorderGoods goods : saleorder.getGoodsList()){
            costTotal = costTotal.add(goods.getReferenceCostPrice().multiply(BigDecimal.valueOf(goods.getNum())));
            BigDecimal realPrice = getRealPriceOfSku(goods);
            priceTotal = priceTotal.add(realPrice.multiply(BigDecimal.valueOf(goods.getNum())));
        }
        BigDecimal rossMargin = new BigDecimal("0");
        if (priceTotal.compareTo(BigDecimal.ZERO) > 0){
            rossMargin = priceTotal.subtract(costTotal).divide(priceTotal,8,BigDecimal.ROUND_HALF_UP);
        }
        return rossMargin;
    }


    /**
     * 获取订单指定时间之前的实际订单金额（除去售后）
     * @param saleorder 订单
     * @param executime 时间
     * @return 订单金额
     */
    private BigDecimal getRealTotalAmountOfSaleorder(Saleorder saleorder, Long executime){
        return getRealSaleorderGoods(saleorder,executime).stream()
                .map(goods -> getRealPriceOfSku(goods).multiply(BigDecimal.valueOf(goods.getNum())))
                .reduce(BigDecimal.ZERO,BigDecimal::add);
    }

    /**
     * 获取订单的业绩额
     * a）当毛利率>=5%时，订单业绩额=订单实际金额；
     * b）当0<毛利率<5%时，订单业绩额=(毛利率/5%) * 订单实际金额；
     * c）当毛利率<=0时，订单业绩额=0；
     * @param profitRate 毛利率
     * @param amountOfSaleorder 订单总金额
     * @param salePerformanceTime 业绩计入时间
     * @return 业绩额
     */
    private BigDecimal  getKpiAmountOfSaleorder(BigDecimal profitRate, BigDecimal amountOfSaleorder, Long salePerformanceTime) {
        BigDecimal standardProfitRate = getStandardRate(salePerformanceTime);
        BigDecimal zero = new BigDecimal("0");
        if (profitRate.compareTo(standardProfitRate) >= 0){
            return amountOfSaleorder;
        } else if (profitRate.compareTo(zero) <= 0){
            return zero;
        } else {
            return profitRate.divide(standardProfitRate,8,BigDecimal.ROUND_HALF_UP).multiply(amountOfSaleorder);
        }
    }


    /**
     * 计算订单的业绩
     * @param saleorder 订单
     */
    @Override
    public Long checkSaleorderPerformanceOfSaleorder(Saleorder saleorder){
        logger.info("五行业绩计算---订单：{}的saleorder：{}",saleorder.getSaleorderNo(), JSONObject.toJSONString(saleorder));
        //只计算BD、VS、HC订单
//        if (saleorder.getOrderType() != 0 && saleorder.getOrderType() != 1 && saleorder.getOrderType() != 5){
//            return 0L;
//        }

        //walker要求，如果在老五行计算该订单，那么在新五行中不在重复计算
        if (saleorder.getIsSalesPerformance() == 1){
            logger.info("五行业绩计算---订单：{}在老五行中已计算过，不再重复计算",saleorder.getSaleorderNo());
            return 0L;
        }

        //根据订单客户的归属销售获取订单归属销售
        RTraderJUser rTraderJUser = rTraderJUserReplicaMapper.getUserIdByTraderId(saleorder.getTraderId());
        if (rTraderJUser == null){
            logger.info("五行业绩计算---订单：{}的客户查询不到归属销售",saleorder.getSaleorderNo());
            return 0L;
        }
        //判断订单归属销售是否在五行考核
        SalesPerformanceDept dept = salesPerformanceDeptReplicaMapper.getDeptByUserId(rTraderJUser.getUserId());

        if (dept == null) {
            logger.info("五行业绩计算---订单：{}的归属销售不在五行考核范围",saleorder.getSaleorderNo());
            return 0L;
        }

        //防止订单重复计算业绩
        if (kpiOrderLogMapper.getKpiLogByOrderNoAndOperation(saleorder.getSaleorderNo(),1).size() > 0){
            return 0L;
        }

        CapitalBill capitalBill = capitalBillReplicaMapper.getLatestCapitalBillByOrder(saleorder.getSaleorderNo());
        if (capitalBill == null){
            logger.info("五行业绩计算---订单：{}没有有效付款流水",saleorder.getSaleorderNo());
            return 0L;
        }

        Long salesPerformanceTime = getSalesPerformanceTimeOfSaleorder(saleorder);
        if (salesPerformanceTime == 0){
            logger.info("五行业绩计算---订单：{}的实际支付金额小于95%",saleorder.getSaleorderNo());
            return 0L;
        }

        if (checkSpecialSaleOrder(saleorder,salesPerformanceTime)){
            logger.info("五行业绩计算---订单：{}检查为特殊订单",saleorder.getSaleorderNo());
            return 0L;
        }

        KpiOrderLogDo kpiOrderLogDo = new KpiOrderLogDo();
        BigDecimal receivedAmountOfSaleorder = getRealReceivedAmountOfSaleorderBeforeExecuteTime(saleorder.getSaleorderNo(),salesPerformanceTime);

        kpiOrderLogDo.setUserId(rTraderJUser.getUserId());
        kpiOrderLogDo.setCompanyId(saleorder.getCompanyId());
        kpiOrderLogDo.setTeamId(dept.getSalesPerformanceDeptId());
        kpiOrderLogDo.setGroupId(dept.getSalesPerformanceGroupId());
        kpiOrderLogDo.setOrderId(saleorder.getSaleorderId());
        kpiOrderLogDo.setOrderNo(saleorder.getSaleorderNo());
        kpiOrderLogDo.setOrderType(saleorder.getOrderType());
        kpiOrderLogDo.setIsSpecOrder(0);
        BigDecimal rossMargin = getProfitRateOfSaleorder(saleorder);
        BigDecimal realAmountOfSaleorder = getRealTotalAmountOfSaleorder(saleorder,salesPerformanceTime);
        kpiOrderLogDo.setKpiAmount(getKpiAmountOfSaleorder(rossMargin,realAmountOfSaleorder,salesPerformanceTime));
        kpiOrderLogDo.setOperation(1);
        kpiOrderLogDo.setKpiDate(new Date(salesPerformanceTime));
        kpiOrderLogDo.setPaymentType(capitalBill.getPaymentType());
        kpiOrderLogDo.setPaymentComments(capitalBill.getComments());
        kpiOrderLogDo.setTraderId(saleorder.getTraderId());
        kpiOrderLogDo.setTraderName(saleorder.getTraderName());
        kpiOrderLogDo.setTraderSubject(capitalBill.getTraderSubject());
        kpiOrderLogDo.setRossMargin(rossMargin);
        kpiOrderLogDo.setAmount(receivedAmountOfSaleorder);
        kpiOrderLogDo.setOrderSumAmount(receivedAmountOfSaleorder);
        kpiOrderLogDo.setOrderRealAmount(realAmountOfSaleorder);
        if (saleorder.getQuoteorderId() != null && saleorder.getQuoteorderId() > 0){
            BussinessChance bussinessChance = quoteorderMapper.getBussinessChanceByQuoteOrderId(saleorder.getQuoteorderId());
            if (bussinessChance != null) {
                kpiOrderLogDo.setChanceNo(bussinessChance.getBussinessChanceNo());
                kpiOrderLogDo.setChanceId(bussinessChance.getBussinessChanceId());
                kpiOrderLogDo.setChanceType(bussinessChance.getType());
                kpiOrderLogDo.setChanceSource(bussinessChance.getSource());
                if (checkNewBusinessChance(bussinessChance,saleorder,capitalBill,receivedAmountOfSaleorder,salesPerformanceTime)){
                    kpiOrderLogDo.setChanceTransFlag(1);
                    kpiOrderLogDo.setChanceTransDays((int) Duration.ofMillis(salesPerformanceTime - bussinessChance.getAddTime()).toDays());
                }
            }
        }

        //保存kpi日志信息
        try {
            kpiOrderLogMapper.insertSelective(kpiOrderLogDo);
        } catch (Exception e){
            logger.error("保存KPI日志：{}失败，失败信息：",kpiOrderLogDo.toString(),e);
            throw e;
        }

        //更新订单计入业绩信息
        Saleorder updateSalesPerformance = new Saleorder();
        updateSalesPerformance.setSaleorderId(saleorder.getSaleorderId());
        updateSalesPerformance.setIsSalesPerformance(1);
        updateSalesPerformance.setSalesPerformanceTime(salesPerformanceTime);
        saleorderMapper.updateByPrimaryKeySelective(updateSalesPerformance);
        return salesPerformanceTime;
    }



    @Override
    public void checkSalesPerformanceOfAfterSales(AfterSales afterSales, Long executeTime){
        //只有售后单类型为销售订单退货、退款时，才计算售后单业绩
        if (afterSales.getType() != 539){
            logger.info("五行业绩计算---售后单：{}的类型为：{}",afterSales.getAfterSalesNo(),afterSales.getType());
            return;
        }

        //将executeTime与订单计入业绩时间作比较，防止售后退款重复计算
        Saleorder saleorder = saleorderReplicaMapper.getSaleorderByAfterSalesId(afterSales.getAfterSalesId());
        if (saleorder != null && saleorder.getIsSalesPerformance() == 1 && saleorder.getSalesPerformanceTime() > executeTime){
            logger.info("五行业绩计算---售后单：{}，执行时间：{}在对应订单计入业绩时间：{}之前",afterSales.getAfterSalesNo(),executeTime,saleorder.getSalesPerformanceTime());
            return;
        }

        //防止重复计算同一个售后单
        if (kpiOrderLogMapper.getKpiLogByAfterSalesNo(afterSales.getAfterSalesNo()).size() > 0) {
            logger.info("五行业绩计算---售后单：{}已经计算过",afterSales.getAfterSalesNo());
            return;
        }

        List<KpiOrderLogDo> kpiOrderLogDoList = kpiOrderLogMapper.getKpiLogByOrderNoAndOperation(afterSales.getOrderNo(),1);
        if (kpiOrderLogDoList.size() == 0) {
            //当计算售后业绩时，如果对应销售订单没有计入业绩，也要重新计算下销售业绩
            Optional.ofNullable(saleorderReplicaMapper.getSaleorderByAfterSalesId(afterSales.getAfterSalesId()))
                    .ifPresent(this::checkSaleorderPerformanceOfSaleorder);
        }

        //重新计算完销售订单后，再次检查是否有销售订单业绩
        List<KpiOrderLogDo> newKpiOrderLogDoList = kpiOrderLogMapper.getKpiLogByOrderNoAndOperation(afterSales.getOrderNo(),1);

        if (newKpiOrderLogDoList.size() == 0){
            logger.info("五行业绩计算---售后单：{}对应的订单没有计入业绩",afterSales.getAfterSalesNo());
            return;
        }

        //重新计算完销售订单后，再次检查有销售订单业绩时，就不需要再计算售后单了
        if (kpiOrderLogDoList.size() == 0 && newKpiOrderLogDoList.size() == 1){
            return;
        }

        KpiOrderLogDo kpiOfSaleorder = newKpiOrderLogDoList.get(0);
        
        KpiOrderLogDo afterSaleKpi = new KpiOrderLogDo();
        afterSaleKpi.setUserId(kpiOfSaleorder.getUserId());
        afterSaleKpi.setCompanyId(kpiOfSaleorder.getCompanyId());
        afterSaleKpi.setTeamId(kpiOfSaleorder.getTeamId());
        afterSaleKpi.setGroupId(kpiOfSaleorder.getGroupId());
        afterSaleKpi.setOrderId(kpiOfSaleorder.getOrderId());
        afterSaleKpi.setOrderNo(kpiOfSaleorder.getOrderNo());
        afterSaleKpi.setOrderType(kpiOfSaleorder.getOrderType());
        afterSaleKpi.setPaymentType(kpiOfSaleorder.getPaymentType());
        afterSaleKpi.setPaymentComments(kpiOfSaleorder.getPaymentComments());
        afterSaleKpi.setTraderId(kpiOfSaleorder.getTraderId());
        afterSaleKpi.setTraderName(kpiOfSaleorder.getTraderName());
        afterSaleKpi.setTraderSubject(kpiOfSaleorder.getTraderSubject());
        afterSaleKpi.setRossMargin(kpiOfSaleorder.getRossMargin());
        afterSaleKpi.setIsSpecOrder(kpiOfSaleorder.getIsSpecOrder());
        afterSaleKpi.setAfterSaleId(afterSales.getAfterSalesId());
        afterSaleKpi.setAfterSaleNo(afterSales.getAfterSalesNo());
        afterSaleKpi.setAmount(afterSales.getAfterSalesDetail().getRefundAmount().multiply(new BigDecimal("-1")));
        BigDecimal kpiAmount = new BigDecimal("0");
        if (kpiOfSaleorder.getOrderRealAmount().compareTo(BigDecimal.ZERO) > 0){
            kpiAmount = kpiOfSaleorder.getKpiAmount().multiply(afterSaleKpi.getAmount()).divide(kpiOfSaleorder.getOrderRealAmount(),
                    BigDecimal.ROUND_HALF_UP);
        }
        afterSaleKpi.setKpiAmount(kpiAmount);
        afterSaleKpi.setKpiDate(new Date(executeTime));
        afterSaleKpi.setOrderRealAmount(kpiOfSaleorder.getOrderRealAmount());
        afterSaleKpi.setChanceId(kpiOfSaleorder.getChanceId());
        afterSaleKpi.setChanceNo(kpiOfSaleorder.getChanceNo());
        afterSaleKpi.setChanceType(kpiOfSaleorder.getChanceType());
        afterSaleKpi.setChanceTransDays(kpiOfSaleorder.getChanceTransDays());

        BigDecimal realAmountOfSaleorder = getRealTotalAmountOfSaleorder(saleorder,executeTime);
        afterSaleKpi.setOrderSumAmount(realAmountOfSaleorder);
        if (realAmountOfSaleorder.compareTo(BigDecimal.ZERO) <= 0){
            afterSaleKpi.setOperation(3);
            if (kpiOfSaleorder.getChanceTransFlag() != null && kpiOfSaleorder.getChanceTransFlag() == 1) {
                afterSaleKpi.setChanceTransFlag(0);
            }
        } else {
            afterSaleKpi.setOperation(2);
        }

        //保存kpi日志信息
        kpiOrderLogMapper.insertSelective(afterSaleKpi);
    }



    /**
     * 获取订单计入业绩时间之前的已支付金额，订单支付金额-售后退款金额（去除账期支付的场景）
     * @param saleorderNo 订单号
     * @param executeTime 计入业绩的时间
     * @return 实际支付金额
     */
    private BigDecimal getRealReceivedAmountOfSaleorderBeforeExecuteTime(String saleorderNo, Long executeTime) {
        return capitalBillReplicaMapper.getRealReceivedAmountOfSaleorderBeforeExecuteTime(saleorderNo,executeTime);
    }

    /**
     * 获取订单的触发计入业绩计算的时间
     * @param saleorder 订单
     * @return 计入业绩的时间
     */
    private Long getSalesPerformanceTimeOfSaleorder(Saleorder saleorder){
        List<CapitalBill> capitalBills = capitalBillReplicaMapper.getAllRealCapitalBillOfSaleorder(saleorder.getSaleorderNo());
        BigDecimal amount = BigDecimal.ZERO;
        for (CapitalBill item : capitalBills){
            amount = amount.add(item.getAmount());
            //获取该支付流水之前订单的实际金额
            BigDecimal totalAmountOfSaleorder = getRealTotalAmountOfSaleorder(saleorder,item.getAddTime());
            if (amount.compareTo(totalAmountOfSaleorder.multiply(new BigDecimal("0.95"))) >= 0){
                return item.getAddTime();
            }
        }
        return 0L;
    }


    /**
     * 校准订单计入业绩的时间
     * @param saleorderNo 订单
     */
    @Override
    public void checkKpiDateOfKpiOrderLog(String saleorderNo){
        Saleorder saleorder = saleorderReplicaMapper.getSaleorderDetails(saleorderNo);
        List<KpiOrderLogDo> kpiOrderLogDoList = kpiOrderLogMapper.getKpiOrderOfSaleorderNo(saleorderNo);
        if (kpiOrderLogDoList.size() == 0){
            return;
        }
        BussinessChance bussinessChance = quoteorderMapper.getBussinessChanceByQuoteOrderId(saleorder.getQuoteorderId());
        Long salesPerformanceTime = getSalesPerformanceTimeOfSaleorder(saleorder);
        kpiOrderLogDoList.stream().filter(kpiOrderLogDo -> kpiOrderLogDo.getOperation() == 1).forEach(
                item -> {
                    KpiOrderLogDo kpiOrderLogDo = new KpiOrderLogDo();
                    kpiOrderLogDo.setId(item.getId());
                    kpiOrderLogDo.setKpiDate(new Date(salesPerformanceTime));
                    if (item.getChanceTransFlag() != null && item.getChanceTransFlag() == 1){
                        kpiOrderLogDo.setChanceTransDays((int) Duration.ofMillis(salesPerformanceTime - bussinessChance.getAddTime()).toDays());
                    }
                    kpiOrderLogMapper.updateByPrimaryKeySelective(kpiOrderLogDo);
                }
        );
        //更新订单计入业绩信息
        Saleorder updateSalesPerformance = new Saleorder();
        updateSalesPerformance.setSaleorderId(saleorder.getSaleorderId());
        updateSalesPerformance.setSalesPerformanceTime(salesPerformanceTime);
        saleorderMapper.updateByPrimaryKeySelective(updateSalesPerformance);
        //更新售后单计入业绩时间
        kpiOrderLogDoList.stream().filter(kpiOrderLogDo -> kpiOrderLogDo.getOperation() > 1).forEach(
                item -> checkKpiDateOfAfterSales(item.getAfterSaleNo())
        );
    }


    @Override
    public void checkKpiDateOfAfterSales(String afterSalesNo){
        //获取售后单的流水
        CapitalBill capitalBill = capitalBillReplicaMapper.getLatestCapitalBillByOrder(afterSalesNo);
        KpiOrderLogDo kpiOrderLogDo = new KpiOrderLogDo();
        kpiOrderLogDo.setAfterSaleNo(afterSalesNo);
        kpiOrderLogMapper.updateKpiDateByAfterSalesNo(afterSalesNo,new Date(capitalBill.getAddTime()));
    }

    /**
     * 新成交询价：
     * a) 该商机分发时间在180天内
     * b) 商机类型为总机询价和自主询价
     * c) 该商机转化的订单计入业绩时间在本月
     * d) 该商机转化的订单是该客户第一个计入业绩的订单
     * e) 由商机转化的订单在订单详情页中 交易信息 - 交易主体为对公，交易金额>0，交易方式不是信用支付 或 交易信息 - 交易主体为对私，且交易金额>200，交易方式不是信用支付
     * @param bussinessChance 商机
     * @param saleorder 订单
     * @param capitalBill 流水
     * @param receivedTotalAmount 订单总付款
     * @return 结果
     */
    private Boolean checkNewBusinessChance(BussinessChance bussinessChance, Saleorder saleorder, CapitalBill capitalBill, BigDecimal receivedTotalAmount, Long executeTime){
        if (bussinessChance.getAssignTime() != null) {
            long days = Duration.ofMillis(DateUtil.sysTimeMillis() - bussinessChance.getAssignTime()).toDays();
            if (days > 180){
                logger.info("五行业绩计算---订单：{}关联的商机分发时间大于180天，不是新商机",saleorder.getSaleorderNo());
                return false;
            }
        }

        if (bussinessChance.getType() != 391 && bussinessChance.getType() != 394){
            logger.info("五行业绩计算---订单：{}关联的商机的类型不是总机询价和自主询价，不是新商机",saleorder.getSaleorderNo());
            return false;
        }
        //获取客户之前计入业绩的订单个数
        int countOfSaleorder = saleorderReplicaMapper.getCountOfSaleorderInSalePerformanceByTraderId(saleorder.getTraderId(),executeTime,saleorder.getSaleorderNo());
        if (countOfSaleorder > 0){
            logger.info("五行业绩计算---订单：{}关联的客户之前有订单计入业绩，不是新商机",saleorder.getSaleorderNo());
            return false;
        }
        //非信用支付
        if (capitalBill.getTraderMode() == 527){
            logger.info("五行业绩计算---订单：{}的支付方式是信用支付，不是新商机",saleorder.getSaleorderNo());
            return false;
        } else {
            //判断订单关联的流水是否存在对公的情况
            Integer count = capitalBillReplicaMapper.getCapitalBillCountOfSaleorderByTraderSubjectBeforeExecuteTime(saleorder.getSaleorderNo(),executeTime);
            if (count > 0 && receivedTotalAmount.compareTo(BigDecimal.ZERO) > 0){
                return true;
            }
            if (count == 0 && receivedTotalAmount.compareTo(new BigDecimal("200")) > 0){
                return true;
            }
            logger.info("五行业绩计算---订单：{}不存在对公交易，且对私交易小于200，不是新商机",saleorder.getSaleorderNo());
            return false;
        }
    }


    @Override
    public void checkNewBusinessChance(KpiOrderLogDo kpiOrderLogDo) {
        boolean isNewBusinessChance = false;
        Saleorder saleorder = saleorderReplicaMapper.getSaleorderDetails(kpiOrderLogDo.getOrderNo());
        if (saleorder == null) {
            return;
        }
        if (saleorder.getQuoteorderId() != null && saleorder.getQuoteorderId() > 0){
            BussinessChance bussinessChance = quoteorderMapper.getBussinessChanceByQuoteOrderId(saleorder.getQuoteorderId());
            if (bussinessChance != null) {
                CapitalBill capitalBill = capitalBillReplicaMapper.getLatestCapitalBillByOrder(saleorder.getSaleorderNo());
                BigDecimal receivedTotalAmount = getRealReceivedAmountOfSaleorderBeforeExecuteTime(kpiOrderLogDo.getOrderNo(),capitalBill.getAddTime());
                isNewBusinessChance = checkNewBusinessChance(bussinessChance,saleorder,capitalBill,receivedTotalAmount,saleorder.getSalesPerformanceTime());
            }
        }
        if ((kpiOrderLogDo.getChanceTransFlag() != null && kpiOrderLogDo.getChanceTransFlag() == 1) && !isNewBusinessChance){
            kpiOrderLogMapper.updateChanceTransferFlag2Null(kpiOrderLogDo.getOrderNo());
        }
        if (kpiOrderLogDo.getChanceTransFlag() == null && isNewBusinessChance){
            kpiOrderLogMapper.updateChanceTransferFlagByOrderNo(kpiOrderLogDo.getOrderNo(),1,1);
            kpiOrderLogMapper.updateChanceTransferFlagByOrderNo(kpiOrderLogDo.getOrderNo(),3,0);
        }
    }
}
