Vue.component('vd-ui-address', {
    template: `
        <div class="vd-ui-address">
            <div class="slide-dialog-input-wrap">
                <ui-input 
                    class="search-input"
                    :border="true"
                    v-model="inputValue"
                    :maxlength="maxlength"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                ></ui-input>
            </div>

            <div class="vd-ui-address-panel">

                <div class="address-search-wrap" v-if="filtering">
                    <div v-if="filterResult.length" class="filter-list">
                        <div 
                            class="filter-item" 
                            v-for="(item, index) in filterResult" :key="index"
                            @click.stop="handleSuggestionClick(item)"
                            :class="{'active': isCheck(item)}"
                        >
                            <div class="filter-selected">
                                <i class="vd-ui_icon icon-radio3" v-if="isCheck(item)"></i>
                                <i class="vd-ui_icon icon-radio1" v-else></i>
                            </div>
                            <div v-html="suggestShow(item)"></div>
                        </div>
                    </div>
                    
                    <div class="erp-load-empty" v-else>
                        <i class="vd-ui_icon icon-info1"></i>
                        <p>无匹配数据</p>
                    </div>
                </div>

                <div class="address-choose-wrap">
                    <div class="vd-ui-address-nav">
                        <div 
                            class="nav-item text-line-1 pro" 
                            :class="{'active': showTab == 1}"
                            @click="changeTab(1)"
                        >{{ selectedProData.label || '省' }}</div>
                        <div 
                            class="nav-item text-line-1 city" 
                            :class="{'active': showTab == 2}"
                            @click="changeTab(2)"
                        >{{ selectedCityData.label || '市' }}</div>
                        <div 
                            class="nav-item text-line-1 area" 
                            :class="{'active': showTab == 3}"
                            @click="changeTab(3)"
                        >{{ selectedAreaData.label || '区' }}</div>
                    </div>

                    <div class="address-choose-panel" >
                        <div class="ap-list" v-show="showTab == 1">
                            <div 
                                class="ap-item" 
                                :class="{'active': item.value == selectedProData.value}"
                                v-for="(item, index) in addressData" :key="index"
                                @click="chooseProvince(item)"
                            >
                                <span class="selected">
                                    <i v-if="item.value == selectedProData.value" class="vd-ui_icon icon-radio3"></i>
                                    <i v-else class="vd-ui_icon icon-radio1"></i>
                                </span>
                                <div class="ap-content">{{ item.label }}</div>
                            </div>
                        </div>
                        <div class="ap-list" v-show="showTab == 2">
                            <div 
                                class="ap-item" 
                                :class="{'active': item.value == selectedCityData.value}"
                                v-for="(item, index) in cityList" :key="index"
                                @click="chooseCity(item)"
                            >
                                <span class="selected">
                                    <i v-if="item.value == selectedCityData.value" class="vd-ui_icon icon-radio3"></i>
                                    <i v-else class="vd-ui_icon icon-radio1"></i>
                                </span>
                                <div class="ap-content">{{ item.label }}</div>
                            </div>
                        </div>
                        <div class="ap-list" v-show="showTab == 3">
                            <div 
                                class="ap-item" 
                                :class="{'active': item.value == selectedAreaData.value}"
                                v-for="(item, index) in areaList" :key="index"
                                @click="chooseArea(item)"
                            >
                                <span class="selected">
                                    <i v-if="item.value == selectedAreaData.value" class="vd-ui_icon icon-radio3"></i>
                                    <i v-else class="vd-ui_icon icon-radio1"></i>
                                </span>
                                <div class="ap-content">{{ item.label }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    props: {
        value: {}, // 传value值
        data: {
            type: Array,
        },
        // true:需选到最后一层数据  false:可选到任意层级
        eachLevel: {
            type: Boolean,
            default: true
        },
        maxlength: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            showTab: 1, // 展示选择面板 1:省 2:市 3:区
            addressData: [],
            cityList: [],
            areaList: [],

            // 已选省市区
            selectedProData: {},
            selectedCityData: {},
            selectedAreaData: {},

            // 搜索
            inputValue: '',
            filtering: false,
            filterResult: [],
        };
    },
    watch: {
        data: {
            handler (newV) {
                this.addressData = newV || [];
                this.init();
            },
            deep: true,
            immediate: true,
        },
        value: {
            handler () {
                this.init();
            },
            deep: true,
            immediate: true,
        },
    },
    computed: {
        // 已选省
        hasPro () {
            return Object.keys(this.selectedProData).length;
        },
        // 已选市
        hasCity () {
            return Object.keys(this.selectedCityData).length;
        },
        // 已选区
        hasArea () {
            return Object.keys(this.selectedAreaData).length;
        },
        // 已选字符串 格式=>"江苏省 / 南京市 / 秦淮区"
        selectStr () {
            let str = '';
            if (this.hasPro) {
                str = this.selectedProData.label;
            }
            if (this.hasCity) {
                str += str? ' / ' + this.selectedCityData.label : this.selectedCityData.label;
            }
            if (this.hasArea) {
                str += str? ' / ' + this.selectedAreaData.label : this.selectedAreaData.label;
            }
            return str || '';
        }
    },
    mounted() {
    },
    methods: {
        clearData() {
            this.selectedProData = {};
            this.selectedCityData = {};
            this.selectedAreaData = {};
            this.cityList = [];
            this.areaList = [];
            this.showTab = 1;
        },

        /* 选择面板 Start */
        // 切换Tab
        changeTab(i) {
            // 点一个级  必须上一级有值
            if ((i == 3 && !this.hasCity) || (i == 2 && !this.hasPro)) {
                return;
            }
            this.showTab = i;
        },
        // 选择省
        setPro (item) {
            this.selectedProData = item || {};
            this.selectedCityData = {};
            this.selectedAreaData = {};

            this.showTab = 2;
            this.cityList = this.selectedProData.children || [];
            this.areaList = [];
        },
        chooseProvince (item) {
            this.setPro(item);

            if (!this.eachLevel) { // 需要选到最后一个层级
                this.handlerEmit();
            }
        },
        // 选择市
        setCity (item) {
            this.selectedCityData = item;
            this.selectedAreaData = {};

            this.showTab = 3;
            this.areaList = item.children || [];
        },
        chooseCity (item) {
            this.setCity(item);

            if (!this.eachLevel) { // 需要选到最后一个层级
                this.handlerEmit();
            }
        },
        // 选择区
        chooseArea (item) {
            this.selectedAreaData = item;
            this.handlerEmit();
        },
        /* 选择面板 End */


        /* 搜索面板 End */
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                let val = e.target.value.trim();
                this.filterSearch(val);
            }
        },
        // 中文结束
        commentPress(e) {
            let val = e.target.value.trim();
            this.filterSearch(val);
        },
        filterSearch (val) {
            if (!val) {
                this.filterResult = [];
                this.filtering = false;
                return;
            }
            if (val == this.selectStr) return;

            this.filterResult = this.getSuggestions(val);
            this.filtering = true;
        },
        getSuggestionWord (val) {
            let splitArr = val.split('/');
            let words = [];
            if (splitArr.length > 1) {
                splitArr.forEach(item=> {
                    if (item.trim()) {
                        words.push(item.trim());
                    }
                })
            } else {
                words.push(splitArr[0].trim());
            }
            return words;
        },
        getSuggestions (val) {
            let words = this.getSuggestionWord(val);
            let searchRes = []; // 匹配结果

            if (words.length > 1) {
                this.addressData.forEach(L1 => {
                    // 匹配到第一级， 注: words第一级 不一定是 list第一级别
                    let level = words.length;
                    if (L1.label.includes(words[0])) {
                        // 继续匹配下一级
                        level--;
                        if (level && L1.children && L1.children.length) {
                            L1.children.forEach(L2 => {
                                if (L2.label.includes(words[words.length - level])) {
                                    level--;

                                    if (L2.children && L2.children.length) {
                                        if (level) {
                                            L2.children.forEach(L3=> {
                                                if (L3.label.includes(words[words.length - level])) {
                                                    searchRes.push({
                                                        l1: L1,
                                                        l2: L2,
                                                        l3: L3
                                                    })
                                                }
                                            })
                                        } else {
                                            if(this.each) {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                })
                                            }

                                            L2.children.forEach(L3=> {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                    l3: L3
                                                })
                                            })
                                        }
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                        })
                                    }
                                }
                            })
                        }
                    } else {
                        // 一级没匹配到, 继续从第二级比较
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(words[0])) {
                                level--;
                                if (level && L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        if (L3.label.includes(words[words.length - level])) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        }
                                    })
                                }
                            }
                        })
                    }
                })
            } else if (words.length == 1) {
                let word = words[0].trim();

                this.addressData.forEach(L1 => {
                    // 一级匹配, 则匹配结果包含所有子集
                    if (L1.label.includes(word)) {
                        if(this.each) {
                            searchRes.push({
                                l1: L1
                            })
                        }
                        if (L1.children) {
                            if (L1.children && L1.children.length) {
                                L1.children.forEach(L2 => {
                                    if (L2.children && L2.children.length) {
                                        if(this.each) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2
                                            })
                                        }

                                        L2.children.forEach(L3 => {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        })
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                })
                            } else {
                                searchRes.push({
                                    l1: L1,
                                })
                            }
                        }
                    }
                    // 一级不匹配, 继续轮循下面二级
                    else {
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(word)) {
                                if (L2.children && L2.children.length) {
                                    if(this.each) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                    L2.children.forEach(L3 => {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    })
                                } else {
                                    searchRes.push({
                                        l1: L1,
                                        l2: L2
                                    })
                                }
                            } 
                            // 二级不匹配, 继续轮循下面三级
                            else {
                                L2.children && L2.children.length && L2.children.forEach(L3 => {
                                    if (L3.label.includes(word)) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            }

            if (searchRes.length > 100) {
                searchRes = searchRes.slice(0, 100);
            }

            return searchRes;
        },
        suggestShow(item) {
            let str = '';
            if (item.l1) {
                str += item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }

            const keywords = this.getSuggestionWord(this.inputValue);
            keywords.sort((a, b) => b.length - a.length);
            for (const keyword of keywords) {
                const regExp = new RegExp(keyword, 'g');
                str = str.replace(regExp, `<font color='#FF6600'">${keyword}</font>`);
            }
            return str;
        },
        // 搜索面板选中
        handleSuggestionClick (item) {
            if (item.l1 && Object.keys(item.l1)) {
                this.setPro(item.l1);
            }
            if (item.l2 && Object.keys(item.l2)) {
                this.setCity(item.l2);
            }
            if (item.l3 && Object.keys(item.l3)) {
                this.chooseArea(item.l3);
            }
        },
        isCheck (item) {
            if (this.getStr(item) == this.selectStr) {
                return true;
            } else {
                return false;
            }
        },
        getStr (item) {
            let str = '';
            if (item.l1) {
                str = item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }
            return str;
        },
        clearFilter() {
            this.filtering = false;
            this.filterResult = [];
            this.inputValue = '';
        },
        /* 搜索面板 End */



        handlerEmit () {
            let proValue = this.selectedProData.value || '';
            let cityValue = this.selectedCityData.value || '';
            let areaValue = this.selectedAreaData.value || '';

            let values = [proValue, cityValue, areaValue];
            this.$emit('input', values);

            this.$emit('change', [
                this.selectedProData,
                this.selectedCityData,
                this.selectedAreaData
            ])
        },
        // 初始化
        init() {
            if (!this.value.length && !this.addressData.length) {
                return;
            }

            try {
                let level = this.value.length;
                this.addressData.forEach(levelPro => {
                    if (levelPro.value == this.value[0]) {
                        this.selectedProData = levelPro;
                        this.cityList = levelPro.children || [];
                        this.areaList = [];
                        level--;

                        // 市级
                        if (level) {
                            levelPro.children.forEach(levelCity => {
                                if (levelCity.value == this.value[1]) {
                                    this.selectedCityData = levelCity;
                                    this.areaList = levelCity.children || [];
                                    this.showTab = 2;
                                    level--;
    
                                    // 区级
                                    if (level) {
                                        levelCity.children.forEach(levelArea => {
                                            if (levelArea.value == this.value[2]) {
                                                this.selectedAreaData = levelArea;
                                                this.showTab = 3;
                                            }
                                        })
                                    }
                                }
                            })
                        } 
                    }
                });
            } catch (err) {
                console.log('errrrr:', err);
            }
        },
    }
})
/* 表单地址弹层 */
Vue.component('ui-form-address', {
    template: `
        <div class="ui-form-address-wrap">
            <div class="form-address-view" @click="openSlideDialog">
                <ui-placeholder v-if="!displayVal">{{ placeholder }}</ui-placeholder>
                <div v-else class="form-address-show">
                    <div class="value text-line-1">{{ displayVal }}</div>
                    <i @click.stop="handlerClear" class="vd-ui_icon icon-error2"></i>
                </div>
            </div>

            <crm-slide-dialog ref="slideDialog" title="地区选择">
                <div class="form-address-panel">
                    <vd-ui-address
                        ref="address"
                        class="panel-inner"
                        v-model="tempValue"
                        :data="addressData"
                        v-$attrs
                        @change="handlerChange"
                    ></vd-ui-address>
                    <div class="slide-dialog-default-footer">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,
    props: {
        // 省市区数据
        data: Array,
        value: Array,
        placeholder: {
            type: String
        },
        needTrigger: {
            type: Boolean,
            default: false, //值改变后是否重新触发change
        },

        // Address组件参数
        eachLevel: {
            type: Boolean,
            default: true
        },
        maxHeight: Number,
        maxlength: {
            type: String,
            default: ''
        },
    },
    data () {
        return {
            addressData: [],
            // 已选地区
            areaInfo: [],

            // temp
            tempData: [],
            tempValue: [],
        }
    },
    watch: {
        data: {
            handler (newV) {
                if (newV.length) {
                    this.addressData = newV || [];
                    this.init(this.value);
                }
            },
            deep: true,
            immediate: true,
        },
        value: {
            handler (newV, oldV) {
                this.tempValue = JSON.parse(JSON.stringify(newV));

                if (newV && newV.length) {
                    let valchange = !oldV || newV.join(',') !== oldV.join(',');
                    this.init(newV, valchange);
                }
            },
            deep: true,
            immediate: true
        },
    },
    computed: {
        displayVal () {
            arr = [];
            this.areaInfo.forEach(item => {
                arr.push(item.label);
            })
            return arr.join(' / ');
        }
    },
    mounted() {
    },
    methods: {
        init ( defaultValues, valchange) {
            if (defaultValues.length && this.addressData.length) {
                this.areaInfo = [];
                let level = defaultValues.length;
                this.addressData.forEach(levelPro => {
                    if (levelPro.value == defaultValues[0]) {
                        this.areaInfo.push(levelPro);
                        level--;

                        // 市级
                        if (level) {
                            levelPro.children.forEach(levelCity => {
                                if (levelCity.value == defaultValues[1]) {
                                    this.areaInfo.push(levelCity);
                                    level--;

                                    // 区级
                                    if (level) {
                                        levelCity.children.forEach(levelArea => {
                                            if (levelArea.value == defaultValues[2]) {
                                                this.areaInfo.push(levelArea);
                                            }
                                        })
                                    }
                                }
                            })
                        } 
                    }
                });

                if (this.eachLevel && this.areaInfo.length < 3) {
                    this.handlerClear();
                }
                else if (this.needTrigger && valchange) {
                    this.$emit('change', this.areaInfo);
                }
            }
        },

        openSlideDialog () {
            this.tempValue = JSON.parse(JSON.stringify(this.value));
            this.$refs.slideDialog.show();
        },
        handlerChange (data) {
            this.tempData = data || [];
        },
        handlerClear () {
            this.$refs.address.clearData(); // 清空内层组件值
            this.areaInfo = [];
            this.tempData = [];
            this.tempValue = [];
            this.$emit("input", []); // 修改外层v-model值
            this.$emit('change', []);
        },


        // 确定
        handlerConfirm () {
            if (!this.tempValue.length) {
                this.$message({
                    type: 'error',
                    message: '请选择地址',
                })
                return;
            }

            let emitVal = JSON.parse(JSON.stringify(this.tempValue));
            this.areaInfo = this.tempData;
            let emitData = Object.assign([], this.areaInfo);
            this.$emit("input", emitVal); // 修改外层v-model值
            this.$emit('change', emitData);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.address.clearFilter();
            this.$refs.slideDialog.hide();
            this.tempData = [];
            this.tempValue = [];
        },
    }
})
Vue.component('ui-user', {
    template: `
        <div class="vd-ui-user-wrap" @click="handlerClick">
            <img class="vd-ui-user-avatar" :src="img" alt="" @error="imgError">
            <div class="vd-ui-user-name" :style="{'color': color}">{{ name }}</div>
        </div>
    `,
    props: {
        avatar: {
            type: String,
            default: ''
        },
        name: {
            type: String,
            default: ''
        },
        color: {
            type: String,
            default: '#09F'
        }
    },
    data() {
        return {
            img: '',
            defaultImg: '/mstatic/mui/image/crm-user-avatar.svg',
            isError: false
        };
    },
    computed: {

    },
    mounted() {
        this.img = this.avatar || this.defaultImg;
    },
    methods: {
        imgError() {
            this.img = this.defaultImg;
        },
        handlerClick () {
            this.$emit('click');
        }
    }
})
Vue.component('vd-ui-button', {
    template: `<div class="vd-ui-button" :class="[btnClass, {'button-loading': loading}]" @click="handlerClick">
        <slot></slot>
    </div>`,
    props: {
        type: {
            type: String,
            default: ''
        },
        loading: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
        };
    },
    computed: {
        btnClass() {
            return {
                primary: 'button-primary',
                'primary-line': 'button-primary-line',
                danger: 'button-danger'
            }[this.type] || '';
        }
    },
    mounted() {

    },
    methods: {
        handlerClick(e) {
            this.$emit('click', e);
        }
    }
})
Vue.component('ui-checkbox', {
    template: `
        <div
            class="vd-ui-checkbox-item"
            :class="{
                'single-row': singleRow,
                'vd-ui-checkbox-item-checked': currentChecked,
                'vd-ui-checkbox-item-disabled': disabled,
                'vd-ui-checkbox-item-progress': isSelectedAll && onProgress === 2,
                'vd-ui-checkbox-item-labeltype': type === 'label'
            }"
            @click="handlerClick(!currentChecked)"
            :title="label"
        >
            <div class="vd-ui-checkbox-inner">
                <div class="vd-ui-checkbox-icon">
                    <div class="vd-ui-checkbox-icon-selected2">
                        <i class="vd-ui_icon icon-selected2"></i>
                    </div>
                </div>
                <span v-html="label" class="vd-ui-checkbox-label"></span>
            </div>
        </div>
    `,
    props: {
        label: {
            type: String,
            default: "",
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        isSelectedAll: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: "", //label
        },
        // 单行展示
        singleRow: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        onProgress() {
            this.currentChecked = this.onProgress === 3;
            this.$emit("update:checked", this.onProgress === 3);
        },
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (this.isSelectedAll) {
                if (this.onProgress === 3) {
                    this.$emit("update:onProgress", 1);
                } else {
                    this.$emit("update:onProgress", 3);
                }
            }
            
            if (!this.disabled) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})

Vue.component('ui-checkbox-group', {
    template: `
        <div 
            class="vd-ui-checkbox-group"
            :class="{
                'is-label': type==='label',
                'is-margin-': !singleRow
            }"
        >
            <template v-for="(item, index) in boxList">
                <ui-checkbox
                    :key="index"
                    :label="item.label"
                    :checked.sync="item.checked"
                    :disabled="item.disabled"
                    @change="handlerChange"
                    :type="type"
                    :singleRow="singleRow"
                ></ui-checkbox>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    watch: {
        list() {
            this.setList();
        },
        onProgress() {
            this.boxList.forEach((item, index) => {
                if (this.onProgress === 3) {
                    this.$set(this.boxList[index], "checked", true);
                } else if (this.onProgress === 1) {
                    this.$set(this.boxList[index], "checked", false);
                }
            });

            if (this.onProgress !== 2) {
                this.handlerChange(null, true);
            }
        },
        value() {
            this.setList();
        }
    },
    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: {
            type: Array,
            default() {
                return [];
            },
        },
        onProgress: {
            type: Number,
            default: 1,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: '' //label
        },
        singleRow: {
            type: Boolean,
            default: false
        }
    },
    mounted() {
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                this.value.forEach((value) => {
                    if (item.value == value) {
                        item.checked = true;
                    }
                });
            });

            this.checkOnProgress();
        },
        handlerChange(data, silent) {
            console.log('boxList:', this.boxList);
            let values = [];
            this.boxList.forEach((item) => {
                if (item.checked) {
                    values.push(item.value);
                }
            });

            if (values.join('__') !== this.value.join('__')) {
                this.$emit("input", values);
                this.$emit("change", values);
                
                this.checkValid(values);

                if (!silent) {
                    this.$nextTick(() => {
                        this.checkOnProgress();
                    });
                }
            }
        },
        checkOnProgress() {
            if (
                this.value.length &&
                this.value.length === this.boxList.length
            ) {
                this.$emit("update:onProgress", 3);
            } else if (!this.value.length) {
                this.$emit("update:onProgress", 1);
            } else {
                this.$emit("update:onProgress", 2);
            }
        },
        selectAll() {
            this.boxList.forEach((item) => {
                item.checked = true;
            });

            this.handlerChange();
        },
        checkValid(newValue) {
            // if(this.validKey && this.validValue) {
            //     if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
            //         let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

            //         this.triggerError(validData);
            //     }
            // }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    },
})
Vue.component('ui-custom-dialog', {
    template: `
            <div class="mui-custom-dialog-container" v-show="isShow" @click="handlerMaskClick">
                <div class="nui-custom-dislog-center">
                    <div class="mui-custom-dialog-wrapper" :style="{'width': width}" v-show="isShow" @click.stop>
                        <div class="mui-custom-dialog-content">
                            <div class="mui-custom-dialog-title" v-if="title" v-html="title"></div>
                            <div class="mui-custom-dialog-msg" v-if="message" v-html="message"></div>
                            <slot></slot>
                        </div>

                        <div class="mui-custom-dialog-button-choice">
                            <slot name="footer">
                                <button 
                                    :class="['mui-cdb', leftBtn.class]" 
                                    class="mui-cdb"
                                    @click="clickLeftBtn"
                                >{{ leftBtn.txt }}</button>
                                <button 
                                    :class="['mui-cdb', rightBtn.class, rightBtnDisable ? 'disabled' : 'no-no']"
                                    @click="clickRightBtn"
                                >{{ rightBtn.txt }}</button>
                            </slot>
                        </div>
                    </div>
                </div>
            </div>
    `,
    props: {
        isShow: {
            type: Boolean,
            default: false
        },
        lock: {
            type: Boolean,
            default: false //点击遮罩是否关闭弹层
        },
        title: String,
        message: String,
        width: {
            type: String,
            default: '270px'
        },
        leftBtn: {
            type: Object,
            default: ()=> {
                return {
                    txt: '取消',
                    class: 'cancel'
                }
            }
        },
        rightBtn: {
            type: Object,
            default: ()=> {
                return {
                    txt: '保存',
                    class: 'confirm',
                }
            }
        },
        // 右侧按钮置灰
        rightBtnDisable: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        isShow(newV) {
            console.log(' isShow ===>', newV);
        },
        rightBtnDisable (newV) {
            console.log(' rightBtnDisable ===>', newV);
        }
    },
    data() {
        return {
            // isShow: false,
            type: '',
            title: '',
            message: '',
            buttons: [],
            defaultClass: '',  // 按钮默认class
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            }
            // btnClass： disabled:置灰色  confirm:蓝色  delete:红色  cannel:默认色
        }
    },
    methods: {
        handlerMaskClick() {
            if(this.lock) {
                return;
            }
            this.hide();
        },
        hide() {
            this.$emit('update:isShow', false);
        },
        clickLeftBtn () {
            this.hide();
            this.$emit('handleLeftBtn');
        },
        clickRightBtn () {
            this.hide();
            this.$emit('handleRightBtn');
        }
    }
})

Vue.component('ui-daterange-picker', {
    template: `
        <div class="vd-ui-daterange-wrapper">
            <div class="vd-ui-daterange-input-wrap">
                <div class="vd-ui-daterange-input" :class="{'active': focusInput == 1}">
                    <input
                        type="text"
                        :readonly="true"
                        :placeholder="startPlaceholder"
                        v-model="startTime"
                        @click="toggleFocus(1)"
                    />
                </div>
                <div class="vd-ui-daterange-gap">-</div>
                <div class="vd-ui-daterange-input" :class="{'active': focusInput == 2}">
                    <input
                        type="text"
                        :readonly="true"
                        :placeholder="endPlaceholder"
                        v-model="endTime"
                        @click="toggleFocus(2)"
                    />
                </div>
            </div>

            <div class="vd-ui-daterange-panel">
                <div class="vd-ui-range-item" v-show="focusInput == 1">
                    <ui-date-picker
                        type="date"
                        v-model="startTime"
                        :max="endTime"
                        @input="handlerChange"
                    ></ui-date-picker>
                </div>
                <div class="vd-ui-range-item" v-show="focusInput == 2">
                    <ui-date-picker
                        type="date"
                        v-model="endTime"
                        :min="startTime"
                        @input="handlerChange"
                    ></ui-date-picker>
                </div>
            </div>
        </div>
    `,
    data() {
        return {
            startTime: '',
            endTime: '',
            focusInput: 1, // 1开始 2结束
        }
    },
    props: {
        value: {
            type: Array,
            default: () => ([])
        },
        startPlaceholder: String,
        endPlaceholder: String,
    },
    computed: {

    },
    watch: {
        "value": {
            handler (newV) {
                this.startTime = newV[0] || '';
                this.endTime = newV[1] || '';

                if (!this.startTime) {
                    this.toggleFocus(1);
                } else if (!this.endTime) {
                    this.toggleFocus(2);
                } else {
                    this.toggleFocus(2);
                }
            },
            deep: true,
            immediate: true
        }
    },
    mounted() {
    },
    methods: {
        toggleFocus (flag) {
            this.focusInput = flag;
        },
        handlerChange() {
            // 选择开始日期后,自动获焦结束时间
            if (this.startTime && this.focusInput == 1) {
                this.toggleFocus(2);
            }

            let value = [this.startTime, this.endTime];
            this.value = value;
            this.$emit('input', value);
            this.$emit('change', value);
        }
    }
})

Vue.component('ui-date-picker', {
    template: `
        <div class="vd-ui-date">
            <div 
                class="vd-ui-date-wrapper"
                :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                ref="pickers"
                @click.stop
            >
                <!-- 时间 -->
                <ui-time-panel
                    v-if="type === 'time'"
                    :type="type"
                    :value="baseValue"
                    @pick="pickTime"
                    :disabled-date="disabledDate"
                    v-bind="pickerOptions"
                ></ui-time-panel>

                <!-- 日期时间 -->
                <ui-date-time-panel
                    v-else-if="type === 'datetime'"
                    :firstDayOfWeek="firstDayOfWeek"
                    :type="type"
                    :value="baseValue"
                    @pick="pickDateTime"
                    :disabled-date="disabledDate"
                    :pickerOptions="pickerOptions"
                    :selectionMode="selectionMode"
                ></ui-date-time-panel>

                <ui-date-panel
                    v-else
                    :type="type"
                    :value="baseValue"
                    :firstDayOfWeek="firstDayOfWeek"
                    :shortcuts="shortcuts"
                    @pick="pickDate"
                    :disabled-date="disabledDate"
                    :selectionMode="selectionMode"
                    v-bind="pickerOptions"
                    :max="max"
                    :min="min"
                ></ui-date-panel>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        width: {
            type: String,
            default: '100%'
        },
        size: String,   // large, small, mini
        placeholder: String,

        type: {   // date, year, month, time, datetime
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        disabledDate: Function,
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        valid: String,
        zindex: String,
        calcerror: {
            type: Boolean,
            default: true
        },
        min: '',
        max: '',
    },

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
        }
    },
    watch: {
        value () {
            this.initDate();
        },
    },
    computed: {
        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            // if (Array.isArray(this.baseValue)) {
            //     return [
            //         util_date.formatDate(this.baseValue[0]),
            //         util_date.formatDate(this.baseValue[1])
            //     ];
            // }
            if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.dealBlur();
        },
        triggerFocus() {
            setTimeout(() => {
                this.$refs.vdInput.focus();
                this.showPanel();
            }, 10)
        },
        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // input 失焦
        handleBlur () {
            // this.pickerVisible = false;
            // this.dealBlur();
        },
        // 主动 失去焦点
        dealBlur() {
            console.log('blur');
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
            this.emitChange(null);
        },
        // change
        handleChange () {
            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                this.handleChange();
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                // this.checkValid(util_date.format(val, this.type));
            }
        },
        // input事件
        emitInput(val) {
            this.$emit('input', util_date.format(val, this.type));
            // this.checkValid(util_date.format(val, this.type));
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            this.emitInput(val);
            this.emitChange(val);
            if (isFinal) {
                this.hidePanel();
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            this.emitInput(val);
            this.emitChange(val);
            if (isFinal) {
                this.hidePanel();
            }
        },
    }
})
// 表单日期 -> 点击打开弹层选择时间
Vue.component('ui-form-date-picker', {
    template: `
        <div class="vd-ui-form-date-wrapper">
            <div class="vd-ui-form-date-input-wrap" @click="showDialog">
                <ui-placeholder v-if="!displayValue">{{ placeholder }}</ui-placeholder>
                <div v-else class="date-input-show">
                    <div class="value text-line-1">{{ displayValue }}</div>
                    <i @click.stop="handlerClear" class="vd-ui_icon icon-error2"></i>
                </div>
            </div>

            <crm-slide-dialog ref="dateDialog" :title="title" :maskHide="false">
                <div class="vd-ui-form-date-panel" v-if="type == 'date'">
                    <ui-date-picker
                        type="date"
                        v-model="tempDateTime"
                        :disabled-date="disabledDate"
                        :max="maxTime"
                        :min="minTime"
                        @input="handlerChange"
                    ></ui-date-picker>
                </div>

                <!-- 再次打开面板时， 默认到开始时间??? -->
                <div class="vd-ui-form-date-panel" v-else-if="type == 'range'">
                    <ui-daterange-picker
                        v-model="tempDateTime"
                        :disabled-date="disabledDate"
                        :start-placeholder="startPlaceholder"
                        :end-placeholder="endPlaceholder"
                        @change="handlerRangeChange"
                    ></ui-daterange-picker>
                </div>
                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,
    data() {
        return {
            dateTime: '', // 原值记录
            tempDateTime: '', // 传到下一层组件的值，
        }
    },
    props: {
        value: {},
        // 弹窗title
        title: String,
        // 范围:range   默认单个日期:date
        type: {
            type: String,
            default: 'date',
        },
        disabledDate: Function,

        /* 单选入参 */
        placeholder: String,
        maxTime: [String, Date],
        minTime: [String, Date],

        /* 范围入参 */
        startPlaceholder: {
            type: String,
            default: '开始时间'
        },
        endPlaceholder: {
            type: String,
            default: '结束时间'
        }
    },
    computed: {
        displayValue () {
            let str = '';
            if (Object.prototype.toString.call(this.value) == '[object Array]') {
                str = this.value[0] || '';
                this.value[1] && (str += ` - ${this.value[1]}`);
            } else {
                str = this.value || '';
            }
            return str;
        },
    },
    watch: {
        "value": {
            handler () {
                if (this.value) {
                    let defaultVal = this.type == 'range' ? [] : '';
                    this.dateTime = this.value || defaultVal;
                    this.tempDateTime = this.value || defaultVal;
                }
            },
            deep: true,
            immediate: true,
        }
    },
    mounted() {
    },
    methods: {
        showDialog() {
            this.tempDateTime = this.dateTime;
            this.$refs.dateDialog.show();
        },
        handlerClear () {
            if (this.type == 'range') {
                this.dateTime = [];
            } else {
                this.dateTime = '';
            }
            this.tempDateTime = this.dateTime;
            this.handlerConfirm();
        },

        // Slide-Dialog ↓↓↓
        handlerChange(data) {
            console.log('change', data, this.tempDateTime);
        },
        handlerRangeChange (data) {
            console.log('range change:', data, this.tempDateTime);
        },
        // 取消
        handlerCancel () {
            this.$refs.dateDialog.hide();
            this.tempDateTime = this.dateTime; // 取消后·面板恢复原值
        },
        // 确定
        handlerConfirm () {
            let value = JSON.parse(JSON.stringify(this.tempDateTime));
            this.$emit('input', value);
            this.$emit('change', value);
            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
    }
})


// 日期公共方法
const util_date = {
    DEFAULT_FORMATS: {
        date: 'yyyy-MM-dd',
        month: 'yyyy-MM',
        datetime: 'yyyy-MM-dd HH:mm:ss',
        time: 'HH:mm:ss',
        week: 'yyyywWW',
        timerange: 'HH:mm:ss',
        daterange: 'yyyy-MM-dd',
        monthrange: 'yyyy-MM',
        datetimerange: 'yyyy-MM-dd HH:mm:ss',
        year: 'yyyy'
    },

    HAVE_TRIGGER_TYPES: [
        'date',
        'datetime',
        'time',
        'time-select',
        'week',
        'month',
        'year',
        'daterange',
        'monthrange',
        'timerange',
        'datetimerange',
        'dates'
    ],

    /* 日期部分 */
    weeks: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
    months: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
    monthView: ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'],
    regDatetime: /^\s*[0-9]{1,4}-[0-9]{1,2}-[0-9]{1,2}\s*[0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}\s*$/,

    // 小于10拼接0
    beautify: (str, max = 10000, min = 1)=> {
        if (!str) return '';
        str = parseInt(str);
        if (isNaN(str)) return '';
        if (max && typeof(max) != 'number') throw new Error('type error')

        if (str < 10) {
            return '0' + str
        } else if (str > max) {
            return ''
        } else {
            return str
        }
    },

    // 数组转化时间
    arrayToDate: (arr) => {
        if (!Object.prototype.toString.call(arr)) {
            return null;
        }
        let finalArr = arr.map(item => {
            if (util_date.isDate(item)) {
                return item
            }
            let d1 = util_date.toDate(item);
            if (util_date.isDate(d1) && d1 != 'Invalid Date') {
                return d1
            } else {
                return null
            }
        });

        return finalArr;
    },

    // 转化成日期格式
    toDate: function(date) {
        typeof(date) !== 'string' && ( date += '' );
        let val = new Date(date) || null;
        return val
    },

    // 判断是否是日期格式
    isDate: (val) =>  {
        return Object.prototype.toString.call(val) == '[object Date]';
    },

    // 返回默认当天的时间格式
    timeToDate: function (time) {
        if (!util_date.checkToTime(time)) return '';
        let date = new Date();
        let Y = date.getFullYear();
        let M = date.getMonth() + 1;
        let D = date.getDate();

        return new Date(`${ Y }-${ M }-${ D } ${ time }`)
    },

    // 返回日期事件格式
    timeToDatetime: (val) => {
        if (!util_date.checkToDatetime(val)) return '';

        let t = new Date(val);
        if (t == 'Invalid Date') return '';
        
        return new Date(`${ util_date.getDateStr(t) } ${ util_date.getTimeStr(t) }`);
    },

    // 获取当前月份 有几天
    getDayCountOfMonth: function(year, month) {
        if (month === 3 || month === 5 || month === 8 || month === 10) {
            return 30;
        }
        if (month === 1) {
            if (year % 4 === 0 && year % 100 !== 0 || year % 400 === 0) {
                return 29;
            } else {
                return 28;
            }
        }
        return 31;
    },

    getDayCountOfYear: function(year) {
        const isLeapYear = year % 400 === 0 || (year % 100 !== 0 && year % 4 === 0);
        return isLeapYear ? 366 : 365;
    },

    getFirstDayOfMonth: function(date) {
        const temp = new Date(date.getTime());
        temp.setDate(1);
        return temp.getDay();
    },

    prevDate: function(date, amount = 1) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() - amount);
    },

    nextDate: function(date, amount = 1) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate() + amount);
    },

    getStartDateOfMonth: function(year, month) {
        const result = new Date(year, month, 1);
        const day = result.getDay();
    
        if (day === 0) {
            return util_date.prevDate(result, 7);
        } else {
            return util_date.prevDate(result, day);
        }
    },

    range: function(n) {
        return Array.apply(null, {length: n}).map((_, n) => n);
    },

    modifyDate: function(date, y, m, d) {
        return new Date(y, m, d, date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds());
    },

    modifyTime: function(date, h, m, s) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate(), h, m, s, date.getMilliseconds());
    },

    clearTime: function(date) {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    },

    changeYearMonthAndClampDate: function(date, year, month) {
        // clamp date to the number of days in `year`, `month`
        // eg: (2010-1-31, 2010, 2) => 2010-2-28
        const monthDate = Math.min(date.getDate(), util_date.getDayCountOfMonth(year, month));
        return util_date.modifyDate(date, year, month, monthDate);
    },

    prevMonth: function(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return month === 0
            ? util_date.changeYearMonthAndClampDate(date, year - 1, 11)
            : util_date.changeYearMonthAndClampDate(date, year, month - 1);
    },

    nextMonth: function(date) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return month === 11
            ? util_date.changeYearMonthAndClampDate(date, year + 1, 0)
            : util_date.changeYearMonthAndClampDate(date, year, month + 1);
    },

    prevYear: function(date, amount = 1) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return util_date.changeYearMonthAndClampDate(date, year - amount, month);
    },

    nextYear: function(date, amount = 1) {
        const year = date.getFullYear();
        const month = date.getMonth();
        return util_date.changeYearMonthAndClampDate(date, year + amount, month);
    },


    // 自定义方法
    // 验证用户输入 能否转化成日期
    checkStringCanToDate: (value, type )=> {
        if (!value) return '';
        if (util_date.isDate(value)) return value;

        if ( type == 'date' ) {
            return util_date.checkToDate(value);
        }
        else if (type == 'year') {
            return util_date.checkToYear(value);
        }
        else if (type == 'month') {
            return util_date.checkToMonth(value);
        }
        else if (type == 'time') {
            return util_date.checkToTime(value);
        }
        else if (type == 'datetime') {
            return util_date.checkToDatetime(value);
        }
        return '';
    },

    // 验证用户输入是否符合 日期格式
    checkToDate: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf('-')) return '';            // 不含关键字符

        let arr = value.split('-');
        let getY = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let arrD = parseInt(arr[2]);
        let year = util_date.beautify(getY, 10000, 1970);
        let month = util_date.beautify(arrM, 31);
        let day = util_date.beautify(arrD, 31);
        if (year && month && day) {
            return `${ year }-${ month }-${ day }`;
        }
        else {
            return '';
        }
    },

    // 验证用户输入是否符合年份格式
    checkToYear: (value) => {
        if (!value) return '';
        if (util_date.isDate(value)) return value;   // 如果已经是时间格式: 不处理
        let intY = parseInt(value);
        let year = intY >= 1970 && intY < 10000 ? intY : '';
        return `${ year }`;
    },

    // 验证用户输入是否符合月份格式
    checkToMonth: (value) => {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf('-')) return '';            // 不含关键字符

        let arr = value.split('-');
        let arrY = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let year = arrY >= 1970 && arrY < 10000 ? arrY : '';
        let month = arrM > 0 && arrM < 10 ? '0' + arrM : (arrM >= 10 && arrM <= 12 ? arrM : '');
        if (year && month) {
            return `${ year }-${ month }`;
        }
        else {
            return '';
        }
    },

    checkToTime: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!~value.indexOf(':')) return '';            // 不含关键字符

        let arr = value.split(':');
        let arrH = parseInt(arr[0]);
        let arrM = parseInt(arr[1]);
        let arrS = parseInt(arr[2]);
        let hour = arrH > 0 && arrH < 10 ? '0' + arrH : (arrH >= 10 && arrH <= 23 ? arrH : '');
        let minute = arrM > 0 && arrM < 10 ? '0' + arrM : (arrM >= 10 && arrM <= 59 ? arrM : '');
        let second = arrS > 0 && arrS < 10 ? '0' + arrS : (arrS >= 10 && arrS <= 59 ? arrS : '');

        if (hour && minute && second) {
            return `${ hour }:${ minute }:${ second }`;
        }
        else {
            return '';
        }
    },

    checkToDatetime: (value)=> {
        typeof(value) != 'string' && ( value += '' );   // 转化字符串
        if (!util_date.regDatetime.test(value)) return '';

        let step = value.split(' ');
        let date = step[0].split('-');
        let time = step[1].split(':');

        let Y = parseInt(date[0]);
        let M = parseInt(date[1]);
        let D = parseInt(date[2]);
        let h = parseInt(time[0]);
        let m = parseInt(time[1]);
        let s = parseInt(time[2]);

        Y = (Y >= 1970 && Y < 10000) ? Y : '';
        M = M > 0 && M < 10 ? '0' + M : (M >= 10 && M <= 12 ? M : '');
        D = D > 0 && D < 10 ? '0' + D : (D >= 10 && D <= 31 ? D : '');
        h = h > 0 && h < 10 ? '0' + h : (h >= 10 && h <= 23 ? h : '');
        m = m > 0 && m < 10 ? '0' + m : (m >= 10 && m <= 59 ? m : '');
        s = s > 0 && s < 10 ? '0' + s : (s >= 10 && s <= 59 ? s : '');

        if (Y && M && D && h && m && s) {
            return `${ Y }-${ M }-${ D } ${ h }:${ m }:${ s }`;
        }
        else {
            return ''
        }
    },


    // 格式化日期
    format: (val, type, ) => {
        if (type == 'year') {
            return util_date.formatYear(val);
        }
        else if (type == 'month') {
            return util_date.formatMonth(val);
        }
        else if (type == 'date') {
            return util_date.formatDate(val);
        }
        else if ( type == 'datetime' ) {
            return util_date.formatDatetime(val);
        }
        else if ( type == 'time' ) {
            return util_date.formatTime(val);
        }
        else if ( type == 'daterange' ) {
            return util_date.formatDateRange(val);
        }
    },

    // 格式化日期
    formatDate: (val)=> {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';
        return util_date.getDateStr(t);
    },

    // 格式化年份
    formatYear: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        let Y = t.getFullYear();
        return Y;
    },

    // 格式化月份
    formatMonth: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        let M = t.getMonth() + 1;
        M < 10 && ( M = '0' + M);

        return `${ t.getFullYear() }-${ M }`
    },

    // 格式化日期时间
    formatDatetime: (val) => {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        return `${ util_date.getDateStr(t) } ${ util_date.getTimeStr(t) }`
    },

    formatTime: (val)=> {
        if (!val) return '';
        let t = util_date.isDate(val) ? val : new Date(val);
        if (t == 'Invalid Date') return '';

        return util_date.getTimeStr(t);
    },

    formatDateRange: (val)=> {
        if (!val.length) return [];
        let D1 = util_date.formatDate(val[0]) || '';
        let D2 = util_date.formatDate(val[1]) || '';
        console.log('D1:', D1);
        console.log('D2:', D2);
        return [D1, D2];
    },

    // 获取年月日 （val一定是date类型）
    getDateStr: (val) => {
        if (!val) return '';
        if (!util_date.isDate(val)) return '';

        let date = new Date(val);
        let Y = date.getFullYear();
        let M = date.getMonth() + 1;
        let D = date.getDate();

        M < 10 && ( M = '0' + M);
        D < 10 && ( D = '0' + D);

        return `${ Y }-${ M }-${ D }`;
    },

    // 获取时分秒 (val必须是date类型)
    getTimeStr: (val) => {
        if (!val) return '';
        if (!util_date.isDate(val)) return '';
        let time = new Date(val);

        let h = time.getHours();
        let m = time.getMinutes();
        let s = time.getSeconds();

        h < 10 && ( h = '0' + h);
        m < 10 && ( m = '0' + m);
        s < 10 && ( s = '0' + s);

        return `${ h }:${ m }:${ s }`;
    },

    modifyWithTimeString: (date, time) => {
        if (date == null || !time) {
            return date;
        }

        if (!util_date.isDate(time)) return '';
        time = new Date(time);
        return util_date.modifyTime(date, time.getHours(), time.getMinutes(), time.getSeconds());
    },

    valueEquals: function(a, b) {
        const dateEquals = function(a, b) {
            const aIsDate = a instanceof Date;
            const bIsDate = b instanceof Date;
            if (aIsDate && bIsDate) {
                return a.getTime() === b.getTime();
            }
            if (!aIsDate && !bIsDate) {
                return a === b;
            }
            return false;
        };
    
        const aIsArray = a instanceof Array;
        const bIsArray = b instanceof Array;
        if (aIsArray && bIsArray) {
            if (a.length !== b.length) {
            return false;
            }
            return a.every((item, index) => dateEquals(item, b[index]));
        }
        if (!aIsArray && !bIsArray) {
            return dateEquals(a, b);
        }
        return false;
    },

    isString: function(val) {
        return typeof val === 'string' || val instanceof String;
    },

    arrayFindIndex: function(arr, pred) {
        for (let i = 0; i !== arr.length; ++i) {
            if (pred(arr[i])) {
                return i;
            }
        }
        return -1;
    },

    arrayFind: function(arr, pred) {
        const idx = util_date.arrayFindIndex(arr, pred);
        return idx !== -1 ? arr[idx] : undefined;
    },

    coerceTruthyValueToArray: function(val) {
        if (Array.isArray(val)) {
            return val;
        } else if (val) {
            return [val];
        } else {
            return [];
        }
    },
    getDateTimestamp: function(time) {
        if (typeof time === 'number' || typeof time === 'string') {
            return util_date.clearTime(new Date(time)).getTime();
        } else if (time instanceof Date) {
            return util_date.clearTime(time).getTime();
        } else {
            return NaN;
        }
    },
    hasClass (el, cls) {
        if (!el || !cls) return false;

        if (cls.indexOf(' ') !== -1) throw new Error('className should not contain space.');
    
        if (el.classList) {
            return el.classList.contains(cls);
        } else {
            return (' ' + el.className + ' ').indexOf(' ' + cls + ' ') > -1;
        }
    },
}

const validator = function(val) {
    // either: String, Array of String, null / undefined
    return (
        val === null || val === undefined || util_date.isString(val) || 
        (Array.isArray(val) && val.length === 2 && val.every(util_date.isString))
    );
};

const Mixin_uiDate_transition = {
    mounted () {
        window.addEventListener('scroll', this.pageScroll, true);
        window.addEventListener('resize', this.pageScroll);
    },
    methods: {
        enter (el) {
            el.style.transition = '0.19s all ease-in';
            el.style.webkitTransform = 'scale(1, 0)';
            el.style.opacity = 0;
        },
        afterEnter (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },

        beforeLeave (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },
        leave (el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1, 0)';
                el.style.opacity = 0;
            }
        },
        afterLeave (el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = ''
        },
        
        pageScroll () {
            if (this.$refs.pickers) {
                this.topScroll();
            }
        },
        topScroll () {           
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.pickers.clientHeight;
            let clientHeight = document.body.clientHeight;
            if ( client.bottom + height + 7 > clientHeight && client.top >= height + 2 ) {
                this.animation = 'appear-up';
                this.$refs.pickers.style.top = `-${height+2}px`;
                this.$refs.pickers.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.pickers.style.top = "";
                this.$refs.pickers.style.boxShadow = '';
            }
        }
    }
}

Vue.component('ui-date-picker1', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>


            <transition
                @enter="enter"
                @after-enter="afterEnter"
                @before-leave="beforeLeave"
                @leave="leave"
                @after-leave="afterLeave"
            >
                <div 
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    v-show="pickerVisible"
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </transition>

            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mixins: [ Mixin_uiDate_transition ],

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        pickerVisible (val) {
            if (this.readonly || this.pickerDisabled) return;
            if (val) {
                this.$nextTick(()=>{
                    this.pageScroll();   // 下拉展开动画
                })
            }
        },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
           if (!this.$el.contains(e.target)) _this.pickerVisible = false;
        })

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.blur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // 主动 失去焦点
        blur() {
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
        },
        // change
        handleChange () {

            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    // user may change focus between two input
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            // if user is typing, do not let picker handle key input
            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            // determine user real change only
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                // this.$emit('change', val);
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(val);
            }
        },
        // input事件
        emitInput(val) {
            console.log('util_date.format(val, this.type):::', util_date.format(val, this.type));
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(val);
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        },
    }
})

const Mixin_uiDate_transition2 = {
    mounted () {
        window.addEventListener('scroll', this.pageScroll, true);
        window.addEventListener('resize', this.pageScroll);
    },
    methods: {
        enter (el) {
            el.style.transition = '0.19s all ease-in';
            el.style.webkitTransform = 'scale(1, 0)';
            el.style.opacity = 0;
        },
        afterEnter (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },

        beforeLeave (el) {
            el.style.webkitTransform = 'scale(1, 1)';
            el.style.opacity = 1;
        },
        leave (el) {
            if (el.scrollHeight !== 0) {
                el.style.transition = '0.19s all ease-in';
                el.style.webkitTransform = 'scale(1, 0)';
                el.style.opacity = 0;
            }
        },
        afterLeave (el) {
            el.style.transition = '';
            el.style.webkitTransform = '';
            el.style.opacity = ''
        },
        
        pageScroll () {
            if (this.$refs.pickers) {
                this.topScroll();
            }
        },
        topScroll () {           
            let client = this.$el.getBoundingClientRect();
            let height = this.$refs.pickers.clientHeight;
            let clientHeight = document.body.clientHeight;
            if ( client.bottom + height + 7 > clientHeight && client.top >= height + 2 ) {
                this.animation = 'appear-up';
                this.$refs.pickers.style.top = `-${height+2}px`;
                this.$refs.pickers.style.boxShadow = 'rgba(0, 0, 0, 0.1) 0px -5px 10px';
            } else {
                this.animation = 'appear-down';
                this.$refs.pickers.style.top = "";
                this.$refs.pickers.style.boxShadow = '';
            }
        }
    }
}

Vue.component('ui-date-picker2', {
    template: `
        <div class="vd-ui-date">
            <div 
                v-if="!ranged"
                ref="input-wrap1"
                class="vd-ui-date-editor"
                :class="'vd-ui-date-editor--' + type"
            >
                <ui-input
                    :placeholder="placeholder"
                    v-bind="$attrs"
                    ref="vdInput"
                    :disabled="disabled"
                    :value="displayValue"
                    @input="value => userInput = value"

                    @focus="handleFocus"
                    @blur="handleBlur"
                    @clear="handleClear"
                    @keydown.native="handleKeydown"
                    @change="handleChange"

                    :size="size"
                    width="100%"
                    :readonly="!editable || readonly"
                    autocomplete="off"
                    :clearable="clearValue"
                >
                    <i v-if="type=='time'" slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-time"></i>
                    <i v-else slot="prefix" class="vd-ui-input__icon vd-ui_icon icon-date"></i>
                </ui-input>
            </div>

            <!-- 日期范围选择 -->
            <div
                v-else 
                ref="input-wrap2"
                class="vd-ui-range"
                @mouseenter="rangeMouseenter"
                @mouseleave="rangeMouseleave"
                @click="handleRangeClick"
            >
                <i class="vd-ui_icon icon-date"></i>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="startPlaceholder"
                        :value="displayValue && displayValue[0]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <span class="split">至</span>
                <div class="input-box">
                    <input type="text" 
                        autocomplete="off"
                        :placeholder="endPlaceholder"
                        :value="displayValue && displayValue[1]"
                        :readonly="!editable || readonly"
                    />
                </div>
                <div class="range-error2">
                    <i 
                        v-show="haveTrigger"
                        class="vd-ui_icon icon-error2" 
                        @click.stop="handleRangeClear"
                    ></i>
                </div>
            </div>

            <ui-poper :show="pickerVisible" :position="animation === 'appear-up' ? 'top' : ''" ref="dropwrap" :errorable="errorable">
                <div 
                    v-show="pickerVisible"
                    class="vd-ui-date-wrapper"
                    :class="[animation ? animation : '', 'vd-ui-date-wrapper--' + type]" 
                    ref="pickers"
                    @click.stop
                >
                    <!-- 范围 -->
                    <ui-date-range-panel
                        v-if="type === 'daterange'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :value="baseValue"
                        :unlinkPanels="unlinkPanels"
                        :shortcuts="shortcuts"
                        v-bind="pickerOptions"
                        :showTime="type === 'datetime'"
                        @pick="pickDateRange"
                    ></ui-date-range-panel>

                    <!-- 时间 -->
                    <ui-time-panel
                        v-else-if="type === 'time'"
                        :type="type"
                        :value="baseValue"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>

                    <!-- 日期时间 -->
                    <ui-date-time-panel
                        v-else-if="type === 'datetime'"
                        :firstDayOfWeek="firstDayOfWeek"
                        :type="type"
                        :value="baseValue"
                        @pick="pickDateTime"
                        :pickerOptions="pickerOptions"
                        :selectionMode="selectionMode"
                    ></ui-date-time-panel>

                    <ui-date-panel
                        v-else
                        :type="type"
                        :value="baseValue"
                        :firstDayOfWeek="firstDayOfWeek"
                        :shortcuts="shortcuts"
                        @pick="pickDate"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
            </ui-poper>

            <!-- 表单校验报错 -->
            <div class="vd-ui-input-error" v-if="errorable && !disabled">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,
    props: {
        value: {},
        readonly: Boolean,   // 是否只读
        disabled: {  // 是否禁用
            type: Boolean,
            default: false
        },
        editable: {  // 是否显示清除按钮
            type: Boolean,
            default: true
        },
        clearable: {
            type: Boolean,
            default: true
        },
        size: String,   // large, small, mini
        placeholder: String,
        startPlaceholder: String,
        endPlaceholder: String,
        type: {   // date, year, month, time, datetime, daterange
            type: String,
            default: 'date'
        },
        appendToBody: {
            type: Boolean,
            default: false,
        },
        format: String,
        popperClass: String,
        pickerOptions: {},
        rangeSeparator: {
            type: String,
            default: '-'
        },
        defaultValue: {}, // 可选，选择器打开时默认显示的时间
        defaultTime: {},  // 范围选择时选中日期所使用的当日内具体时刻
        valueFormat: String,   // 绑定值的格式。不指定则绑定值为 Date 对象
        name: {
            default: '',
            validator
        },
        unlinkPanels: {   // 在范围选择器里取消两个日期面板之间的联动
            type: Boolean,
            default: false
        },
        prefixIcon: String,
        clearIcon: {
            type: String,
            default: 'vd-ui-icon-circle-close'
        },
        validateEvent: {    // 输入时是否触发表单的校验
            type: Boolean,
            default: true
        },
        id: {
            default: '',
            validator
        },
        shortcuts: Array, // 快捷选项
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
    },
    mixins: [ Mixin_uiDate_transition2 ],

    data () {
        return {
            pickerVisible: false,   // panel是否展示
            showClose: false,
            valueOnOpen: null, // 选择器打开时的值，用于确认是否发生变化
            unwatchPickerOptions: null,

            baseValue: null,    // 真实选中的日期 【一定是日期格式】 【可能是数组中多个日期】 同 ele parsedValue
            userInput: null,    // 输入框实时输入值
            animation: '',
            realDate: '',    // 实际上选择的时间（日期，年月，月都放在这里），显示都根据这个来
            rangeFocus: false,  // 范围选择器是否获取焦点
        }
    },
    watch: {
        pickerVisible (val) {
            if (this.readonly || this.pickerDisabled) return;
            if (val) {
                this.$nextTick(()=>{
                    this.pageScroll();   // 下拉展开动画
                })
            }
        },
    },
    computed: {
        // 是否范围选择
        ranged () {
            return this.type.indexOf('range') > -1;
        },

        // 同 ele reference
        vdInput () {
            const vdInput = this.$refs.vdInput;
            if (vdInput) return vdInput.$el || vdInput;
            else return '';
        },

        refInput () {
            if (this.vdInput) {
                return [].slice.call(this.vdInput.querySelectorAll('input'));
            }
            return [];
        },

        // 是否显示清除按钮【需要显示 且 有值】
        clearValue () {
            return this.clearable && this.valueOnOpen && true;
        },

        selectionMode () {
            if (this.type == 'month') {
                return 'month';
            } else if (this.type == 'year') {
                return 'year';
            } else if (this.type === 'dates') {
                return 'dates';
            } else if (this.type === 'week') {
                return 'week';
            }
            return 'day';
        },

        // 显示范围清除按钮
        haveTrigger () {
            // 框子获焦 + 至少有一个值
            let zhi = this.baseValue && this.baseValue[0] && this.baseValue[1];
            return zhi && this.rangeFocus
        },

        // 回显示在input中的值 【需要处理显示格式】
        displayValue () {
            if (this.userInput !== null) {
                return this.userInput;
            }
            if (Array.isArray(this.baseValue)) {
                return [
                    util_date.formatDate(this.baseValue[0]),
                    util_date.formatDate(this.baseValue[1])
                ];
            }
            else if (this.baseValue !== null) {
                return util_date.format(this.baseValue, this.type);
            }
            else {
                return ''
            }
        },

    },
    mounted () {
        this.$form.setValidEl(this);

        // 关闭 当前日期窗口之外的其他日期窗口（全局一次只能打开一个）
        let _this = this;
        document.addEventListener('click', (e) => {
            let inputWrap1 = _this.$refs['input-wrap1']; // input
            let inputWrap2 = _this.$refs['input-wrap2']; // 范围input
            let panelWrap = _this.$refs['pickers']; // 日期panel层
            let include1 = false; // input 是否包含点击元素
            let include2 = false; // 范围input 是否包含点击元素
            let include3 = false; // 面板是否包含点击元素

            if (inputWrap1 && inputWrap1.contains(e.target)) {
                include1 = true;
            }
            if (inputWrap2 && inputWrap2.contains(e.target)) {
                include2 = true;
            }
            if (panelWrap && panelWrap.contains(e.target)) {
                include3 = true;
            }

            if (_this.pickerVisible && !include1 && !include2 && !include3) {
                _this.pickerVisible = false;
            }
        }, true); // true: 从外向内 由根节点向点击元素执行

        this.initDate();
    },
    methods: {
        // 初始化日期
        initDate () {
            if (this.value) {
                if (util_date.isDate(this.value)) {
                    this.baseValue = this.value;
                }
                else if (this.type == 'daterange') {
                    let test = util_date.arrayToDate(this.value)
                    this.baseValue = test;
                }
                else if (typeof(this.value) == 'string' && util_date.checkStringCanToDate(this.value, this.type)) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(this.value);
                    } else if (this.type == 'datetime') {
                        this.baseValue = util_date.timeToDatetime(this.value);
                    } else {
                        this.baseValue = util_date.toDate(this.value);
                    }
                }
            } else {
                this.baseValue = null;
            }
        },

        // 展示panel
        showPanel () {
            if (this.readonly || this.pickerDisabled) return;
            this.pickerVisible = true;
            this.valueOnOpen = Array.isArray(this.baseValue) ? [...this.baseValue] : this.baseValue;
        },
        // 关闭panel
        hidePanel () {
            this.pickerVisible = false;
            this.userInput = null;
            this.dealBlur();
        },

        // input获焦
        handleFocus (e) {
            // const type = this.type;
            if (util_date.HAVE_TRIGGER_TYPES.indexOf('date') !== -1 && !this.pickerVisible) {
                this.showPanel();
            }
            this.$emit('focus', e);
        },
        // input 失焦
        handleBlur () {
            // this.pickerVisible = false;
            // this.dealBlur();
        },
        // 主动 失去焦点
        dealBlur() {
            console.log('blur');
            this.refInput.forEach(input => input.blur());
            this.$emit('blur', this);
        },
        // 单选-清除
        handleClear () {
            this.emitInput(null);
            this.emitChange(null);
        },
        // 范围清除
        handleRangeClear (event) {
            this.baseValue = null;
            this.userInput = null;
            this.emitInput('');
            this.emitChange('');
        },
        // change
        handleChange () {

            // 如果有值
            if (this.userInput) {

                const value = util_date.checkStringCanToDate(this.userInput, this.type);
                // 验证合法性
                if (value) {
                    if (this.type == 'time') {
                        this.baseValue = util_date.timeToDate(value);   // 年和月是否合法
                    } else {
                        this.baseValue = util_date.toDate(value);   // 年和月是否合法
                    }
                    this.emitInput(this.baseValue);
                    this.emitChange(this.baseValue);
                }
                else {
                    // 年份，月份输入时，重新处理把时间给user-input，不然后被赋一次年月或月份后，不好对比时间
                    this.baseValue = this.valueOnOpen || null;
                }

                this.hidePanel();   // 关闭弹窗
            }

            if (this.userInput === '') {
                this.emitInput(null);
                this.emitChange(null);
                this.baseValue = null;
                this.userInput = null;
            }


        },
        // input 触发按键
        handleKeydown (event) {
            const keyCode = event.keyCode;

            // ESC
            if (keyCode === 27) {
                this.hidePanel();
                event.stopPropagation();
                return;
            }

            // Tab
            if (keyCode === 9) {
                if (!this.ranged) {
                    this.handleChange();
                    this.hidePanel();
                    event.stopPropagation();
                } else {
                    setTimeout(() => {
                        if (this.refInput.indexOf(document.activeElement) === -1) {
                            this.hidePanel();
                            event.stopPropagation();
                        }
                    }, 0);
                }
                return;
            }

            if (keyCode === 13) {
                if (this.userInput) {
                    this.handleChange();
                    this.hidePanel();
                }
                event.stopPropagation();
                return;
            }

            if (this.userInput) {
                event.stopPropagation();
                return;
            }
        },
        // change事件
        emitChange(val) {
            if (!util_date.valueEquals(val, this.valueOnOpen)) {
                this.$emit('change', util_date.format(val, this.type));
                this.valueOnOpen = val;
                this.checkValid(val);
            }
        },
        // input事件
        emitInput(val) {
            this.$emit('input', util_date.format(val, this.type));
            this.checkValid(val);
        },
        // 选中日期  [关闭panel => emit值]
        pickDate (val) {
            this.baseValue = val;
            this.userInput = null;
            this.hidePanel();
            this.emitInput(val);
            this.emitChange(val);
        },
        // 选中时间  [关闭panel => emit值]
        pickTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期时间
        pickDateTime (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
            }
        },
        // 选中日期范围  [关闭panel => emit值]
        pickDateRange (val, isFinal = false) {
            this.baseValue = val;
            this.userInput = null;
            if (isFinal) {
                this.hidePanel();
                this.emitInput(val);
                this.emitChange(val);
            }
        },
        handleRangeClick() {
            const type = this.type;

            if (util_date.HAVE_TRIGGER_TYPES.indexOf(type) !== -1 && !this.pickerVisible) {
                this.pickerVisible = true;
            }
            this.$emit('focus', this);
        },
        
        rangeMouseenter () {
            this.rangeFocus = true;
        },
        rangeMouseleave () {
            this.rangeFocus = false;
        },
        checkValid(val) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, val, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
            this.$refs.dropwrap.calcPosition();
        },
    }
})
// 日期面板
Vue.component('ui-date-panel', {
    template: `
        <div class="vd-ui-panel">
            <!-- 头部控制器 -->
            <div class="vd-ui-panel-header">
                <ul>
                    <li 
                        label="前一年"
                        class="arrow arrow-left1"
                        @click="prevYear">
                        <i class="vd-ui_icon icon-slide-up"></i>
                    </li>

                    <li label="上个月"
                        class="arrow"
                        v-show="currentView === 'date'"
                        @click="prevMonth">
                        <i class="vd-ui_icon icon-app-left"></i>
                    </li>

                    <li class="year-month">
                        <span
                            label="年份"
                            class="choose-year"
                            @click="showYearPicker"
                        >
                            {{ yearLabel }}
                        </span>

                        <span
                            label="月份"
                            v-show="currentView === 'date'"
                            class="choose-month"
                            @click="showMonthPicker"
                        >
                            {{ month + 1 }}月
                        </span>
                    </li>

                    <li 
                        label="下个月"
                        class="arrow"
                        v-show="currentView === 'date'"
                        @click="nextMonth">
                        <i class="vd-ui_icon icon-app-right"></i>
                    </li>

                    <li 
                        label="后一年"
                        class="arrow arrow-right1"
                        @click="nextYear">
                        <i class="vd-ui_icon icon-slide-up"></i>
                    </li>
                </ul>
            </div>

            <!-- table面板 -->
            <div class="vd-ui-panel-body">
                <ui-date-table
                    v-show="currentView == 'date'"
                    @pick="handleDatePick"
                    :selection-mode="selectionMode"
                    :firstDayOfWeek="firstDayOfWeek"
                    :value="value"
                    :defaultValue="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :cell-class-name="cellClassName"
                    :disabled-date="disabledDate"
                    :max="max"
                    :min="min"
                ></ui-date-table>
                
                <ui-year-table
                    v-show="currentView === 'year'"
                    @pick="handleYearPick"
                    :value="value"
                    :default-value="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :disabled-date="disabledDate"
                ></ui-year-table>
                
                <ui-month-table
                    v-show="currentView === 'month'"
                    @pick="handleMonthPick"
                    :value="value"
                    :default-value="defaultValue ? new Date(defaultValue) : null"
                    :date="date"
                    :disabled-date="disabledDate"
                ></ui-month-table>
            </div>

            <div class="date-shortcuts" v-if="shortcuts && shortcuts.length">
                <span 
                    v-for="(item, key) in shortcuts" 
                    :key="key" 
                    class="item-sc"
                    @click="handleShortcutClick(item)"
                >{{ item.text }}</span>
            </div>

        </div>
    `,
    props: {
        type: {
            type: String,
            default: 'date'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        firstDayOfWeek: Number,
        disabledDate: {},
        cellClassName: {},
        rangeState: {
            default() {
                return {
                    endDate: null,
                    selecting: false
                };
            }
        },
        defaultValue: {},
        // week dates range
        selectionMode: {
            type: String,
            default: 'day'
        },
        shortcuts: {
            type: Array,
            default: ()=> {
                return []
            }
        },
        min: '',
        max: '',
    },
    data () {
        return { 
            date: new Date(),
            tableRows: [ [], [], [], [], [], [] ],
            lastRow: null,
            lastColumn: null,
            
            currentView: "date"
        }
    },
    watch: {
        value: {
            handler (val) {
                if (this.selectionMode === 'dates' && this.value) return;

                if (util_date.isDate(val)) {
                    this.date = val;
                }
                else if (typeof(val) == 'string' && util_date.checkToDate(val)) {
                    this.date = new Date(val);
                } else {
                    this.date = this.getDefaultValue();
                }
            },
            immediate: true
        },
        selectionMode (newVal) {
            if (newVal === 'month') {
                if (this.currentView !== 'year' || this.currentView !== 'month') {
                    this.currentView = 'month';
                }
            } else if (newVal === 'dates') {
                this.currentView = 'date';
            }
        }
    },
    computed: {
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },

        yearLabel () {
            const yearTranslation = '年';
            if (this.currentView === 'year') {
                const startYear = Math.floor(this.year / 12) * 12;
                if (yearTranslation) {
                    return startYear + ' ' + yearTranslation + ' - ' + (startYear + 11) + ' ' + yearTranslation;
                }
                return startYear + ' - ' + (startYear + 11);
            }
            return this.year + ' ' + yearTranslation;
        },
    },
    mounted () {
        this.resetView(); // 初始化类型
    },
    methods: {
        prevMonth () {
            this.date = util_date.prevMonth(this.date);
        },
        nextMonth () {
            this.date = util_date.nextMonth(this.date);
        },
        prevYear () {
            if (this.currentView === 'year') {
                this.date = util_date.prevYear(this.date, 12);
            } else {
                this.date = util_date.prevYear(this.date);
            }
        },
        nextYear() {
            if (this.currentView === 'year') {
                this.date = util_date.nextYear(this.date, 12);
            } else {
                this.date = util_date.nextYear(this.date);
            }
        },
        showYearPicker() {
            this.currentView = 'year';
        },
        showMonthPicker() {
            this.currentView = 'month';
        },

        // 处理点击日期 emit
        handleDatePick (val) {
            if (this.selectionMode === 'day') {
                this.date = val;
                this.$emit('pick', val);
            }
        },
        // 处理点击年 emit
        handleYearPick (year) {
            if (this.selectionMode === 'year') {
                this.date = util_date.modifyDate(this.date, year, 0, 1);
                this.$emit('pick', this.date);
            } else {
                // 是点击日期头部 弹出的年份选择，应该继续选择月份，而不是emit年份
                this.date = util_date.changeYearMonthAndClampDate(this.date, year, this.month);
                this.currentView = 'month';
            }
        },

        handleMonthPick (month) {
            if (this.selectionMode === 'month') {
                this.date = util_date.modifyDate(this.date, this.year, month, 1);
                this.$emit('pick', this.date);
            } else {
                this.date = util_date.changeYearMonthAndClampDate(this.date, this.year, month);
                this.currentView = 'date';
            }
        },

        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },

        resetView () {
            if (this.selectionMode === 'month') {
                this.currentView = 'month';
            } else if (this.selectionMode === 'year') {
                this.currentView = 'year';
            } else {
                this.currentView = 'date';
            }
        },

        handleShortcutClick (shortcut) {
            if (shortcut.onClick) {
                shortcut.onClick(this);
            }
        },
    }
})
Vue.component('ui-date-time-panel', {
    template: `
        <div class="vd-ui-datetime-panel">
            <div class="datetime-content">
                <div class="datetime-date-panel">
                    <ui-date-panel
                        :type="type"
                        :value="value"
                        @pick="pickDate"
                        :firstDayOfWeek="firstDayOfWeek"
                        :selectionMode="selectionMode"
                        v-bind="pickerOptions"
                    ></ui-date-panel>
                </div>
                <div class="datetime-time-panel">
                    <ui-time-panel
                        :type="type"
                        :value="value"
                        @pick="pickTime"
                        v-bind="pickerOptions"
                    ></ui-time-panel>
                </div>
            </div>

            <div class="datetime-btn">
                <button
                    type="button"
                    class="time-cancel"
                    @click="handleNow"
                >此时</button>
                <button
                    type="button"
                    class="time-confirm"
                    @click="handleConfirm"
                >确定</button>
            </div>
        </div>
    `,

    props: {
        type: {
            type: String,
            default: 'date'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        selectionMode: String,
        firstDayOfWeek: Number,
        pickerOptions: {},
    },
    data() {
        return {
            datetime: '',
        }
    },
    watch: {
        value: {
            handler (val) {
                if (util_date.isDate(val)) {
                    this.datetime = val;
                }
                else if (typeof(val) == 'string' &&  util_date.checkToDatetime(val)) {
                    this.datetime = util_date.timeToDatetime(val);
                } else {
                    this.datetime = this.getDefaultValue();
                }
            },
            immediate: true
        },
    },
    computed: {
        date () {
            let t = new Date(this.datetime);
            let Y = t.getFullYear();
            let M = t.getMonth();
            let D = t.getDate();
            return `${ Y }-${ M }-${ D }`;
        },
        time () {
            let t = new Date(this.datetime);
            let h = t.getHours();
            let m = t.getMinutes();
            let s = t.getSeconds();
            return `${ h }-${ m }-${ s }`;
        }
    },
    created () {
    },
    mounted () {
    },
    methods:{
        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },
        pickDate (val) {
            let pVal = new Date(`${ util_date.getDateStr(val) } ${ util_date.getTimeStr(this.value) }`);
            this.$emit('pick', pVal);
        },
        pickTime (val) {
            let date = util_date.getDateStr(this.value);
            !date && (date = util_date.getDateStr(new Date()));

            let pVal = new Date(`${ date } ${ util_date.getTimeStr(val) }`);
            this.$emit('pick', pVal);
        },
        handleNow () {
            let pVal = new Date();
            this.$emit('pick', pVal, true);
        },
        handleConfirm () {
            let pVal = this.datetime || null;
            this.$emit('pick', pVal, true);  // 最后一个true 标识，关闭弹窗
        }
    }
})
Vue.component('ui-time-panel', {
    template: `
        <div class="vd-ui-time-panel">
            <!-- 头 只在日期+时间选择器显示 -->
            <div class="vd-ui-time-panel__header" v-if="type=='datetime'">
                {{ showTime }}
            </div>

            <!-- 内容 -->
            <div 
                class="vd-ui-time-panel__content"
                :style="{borderBottom: type=='time'? 'solid 1px #E1E5E8' : 'none'}"
            >
                <ui-time-table
                    :type="type"
                    :date="date"
                    @pick="pickTime"
                ></ui-time-table>
            </div>

            <!-- 脚 -->
            <div class="vd-ui-time-panel__footer" v-if="type=='time'">
                <button
                    type="button"
                    class="time-cancel"
                    @click="handleNow"
                >此时</button>
                <button
                    type="button"
                    class="time-confirm"
                    @click="handleConfirm"
                >确定</button>
            </div>
        </div>
    `,

    props: {
        type: {
            type: String,
            default: 'time'
        },
        // 此层value接收到的是上层 userInput
        value: {
            type: [Date, String]
        },
        selectableRange: String,   // 时间范围
        defaultValue: {},


        disabledDate: {},
        cellClassName: {},
        minDate: {},
        maxDate: {},
    },

    data() {
        return {
            val: '',
            // format: 'HH:mm:ss',
            selectionRange: [0, 2],
            needInitAdjust: true,

            date: new Date(),
        };
    },

    computed: {
        // 整体时间
        showTime () {
            return util_date.format(this.date, 'time');   // 这里要用点击后随着动的值，不能用选中的固定value值
        },
    },

    watch: {
        value: {
            handler (val) {
                if (util_date.isDate(val)) {
                    this.date = val;
                }
                else if (typeof(val) == 'string' && util_date.checkToTime(val)) {
                    this.date = util_date.timeToDate(val);
                } else {
                    this.date = this.getDefaultValue();
                }
            },
            immediate: true
        },

        defaultValue(val) {
            if (!util_date.isDate(this.value)) {
                this.date = val ? new Date(val) : new Date();
            }
        }
    },

    created () {
    },

    methods: {
        getDefaultValue () {
            return this.defaultValue ? new Date(this.defaultValue) : new Date();
        },
        // 选中时分秒
        pickTime (val) {
            this.date = val;
            this.$emit('pick', this.date);
        },
        // 此刻
        handleNow () {
            let date = new Date();
            this.$emit('pick', date, true);
        },
        // 确认
        handleConfirm () {
            let date = this.date || null;
            this.$emit('pick', date, true);  // 最后一个true 标识，关闭弹窗
        },
    },
})
const getDateTimestamp = function(time) {
    if (typeof time === 'number' || typeof time === 'string') {
        return util_date.clearTime(new Date(time)).getTime();
    } else if (time instanceof Date) {
        return util_date.clearTime(time).getTime();
    } else {
        return NaN;
    }
};

// 日期表格
Vue.component('ui-date-table', {
    template: `
        <table
            cellspacing="0"
            cellpadding="0"
            class="vd-ui-date-table "
            @click="handleClick"
            @mousemove="handleMouseMove"
            :class="{'is-week-mode': selectionMode === 'week'}"
        >
            <tbody>
                <tr>
                    <th v-for="(week, key) in WEEKS" :key="key">{{ WEEKS_SHOW[week] }}</th>
                </tr>
                <tr
                    class="vd-ui-date-table__row"
                    v-for="(row, key) in rows"
                    :key="key">
                    <td
                        v-for="(cell, key) in row"
                        :class="getCellClasses(cell)"
                        :key="key">
                        <div>
                            <span>{{ cell.text }}</span>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    `,
    props: {
        firstDayOfWeek: {
            type: Number,
            default: 1,
            validator: val => val >= 1 && val <= 7
        },
        value: {},
        defaultValue: {
            validator(val) {
                return val === null || util_date.isDate(val) || (Array.isArray(val) && val.every(util_date.isDate));
            }
        },
        date: {},
        // week  dates
        selectionMode: {
            default: 'day'
        },
        disabledDate: {},
        cellClassName: {},
        minDate: {},
        maxDate: {},
        // 范围
        rangeState: {
            default () {
                return {
                    endDate: null,
                    selecting: false
                };
            }
        },
        min: '',
        max: '',
    },
    data () {
        return {
            WEEKS_: ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'],
            WEEKS_SHOW: {
                sun: '日',
                mon: '一',
                tue: '二',
                wed: '三',
                thu: '四',
                fri: '五',
                sat: '六',
            },
            tableRows: [ [], [], [], [], [], [] ],
            lastRow: null,
            lastColumn: null,
        }
    },
    watch: {
        'rangeState.endDate'(newVal) {
            this.markRange(this.minDate, newVal);
        },
        minDate(newVal, oldVal) {
            if (getDateTimestamp(newVal) !== getDateTimestamp(oldVal)) {
                this.markRange(this.minDate, this.maxDate);
            }
        },

        maxDate(newVal, oldVal) {
            if (getDateTimestamp(newVal) !== getDateTimestamp(oldVal)) {
                this.markRange(this.minDate, this.maxDate);
            }
        }
    },
    mounted() {
        console.log(this.maxDate)
    },
    computed: {
        offsetDay () {
            const week = this.firstDayOfWeek;
            // 周日为界限，左右偏移的天数，3217654 例如周一就是 -1，目的是调整前两行日期的位置
            return week > 3 ? 7 - week : -week;
        },
        WEEKS () {
            const week = this.firstDayOfWeek;
            return this.WEEKS_.concat(this.WEEKS_).slice(week, week + 7);
        },
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },

        startDate () {
            return util_date.getStartDateOfMonth(this.year, this.month);
        },

        // 面板日期数据
        rows () {
            try {
                const date = new Date(this.year, this.month, 1);
                let day = util_date.getFirstDayOfMonth(date); // day of first day   获取每月第一天
                const dateCountOfMonth = util_date.getDayCountOfMonth(date.getFullYear(), date.getMonth());   // 当月有几天
                const dateCountOfLastMonth = util_date.getDayCountOfMonth(date.getFullYear(), (date.getMonth() === 0 ? 11 : date.getMonth() - 1));  // 上个月有多少天

                day = (day === 0 ? 7 : day);
                const offset = this.offsetDay;
                const rows = this.tableRows;
                let count = 1;

                const startDate = this.startDate;
                const disabledDate = this.disabledDate;
                const cellClassName = this.cellClassName;
                const selectedDate = [];
                const now = getDateTimestamp(new Date());

                for (let i = 0; i < 6; i++) {
                    const row = rows[i];

                    for (let j = 0; j < 7; j++) {
                        let cell = row[j];
                        if (!cell) {
                            cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
                        }

                        cell.type = 'normal';

                        const index = i * 7 + j;

                        const time = util_date.nextDate(startDate, index - offset).getTime();

                        cell.inRange = time >= getDateTimestamp(this.minDate) && time <= getDateTimestamp(this.maxDate);
                        cell.start = this.minDate && time === getDateTimestamp(this.minDate);
                        cell.end = this.maxDate && time === getDateTimestamp(this.maxDate);

                        const isToday = time === now;

                        if (isToday) {
                            cell.type = 'today';
                        }

                        if (i >= 0 && i <= 1) {
                            const numberOfDaysFromPreviousMonth = day + offset < 0 ? 7 + day + offset : day + offset;

                            if (j + i * 7 >= numberOfDaysFromPreviousMonth) {
                                cell.text = count++;
                            } else {
                                cell.text = dateCountOfLastMonth - (numberOfDaysFromPreviousMonth - j % 7) + 1 + i * 7;
                                cell.type = 'prev-month';
                            }
                        } else {
                            if (count <= dateCountOfMonth) {
                                cell.text = count++;
                            } else {
                                cell.text = count++ - dateCountOfMonth;
                                cell.type = 'next-month';
                            }
                        }

                        let cellDate = new Date(time);
                        cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);

                        if((this.min && time < getDateTimestamp(this.min)) || (this.max && time > getDateTimestamp(this.max))) {
                            cell.disabled = true;
                        }

                        cell.selected = util_date.arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());
                        cell.customClass = typeof cellClassName === 'function' && cellClassName(cellDate);
                        this.$set(row, j, cell);
                    }
                }
                return rows;
            } catch (err) {
                console.log('table数据 rowsErr', err);
            }
        }
    },
    methods: {
        cellMatchesDate(cell, date) {
            const value = new Date(date);
            return (this.year === value.getFullYear() && this.month === value.getMonth() && Number(cell.text) === value.getDate());
        },

        getCellClasses (cell) {
            const selectionMode = this.selectionMode;
            const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];

            let classes = [];
            if ((cell.type === 'normal' || cell.type === 'today') && !cell.disabled) {
                classes.push('available');
                if (cell.type === 'today') {
                    classes.push('today');
                }
            } else {
                classes.push(cell.type);
            }

            if (cell.type === 'normal' && defaultValue.some(date => this.cellMatchesDate(cell, date))) {
                classes.push('default');
            }

            if (selectionMode === 'day' && (cell.type === 'normal' || cell.type === 'today') && this.cellMatchesDate(cell, this.value)) {
                classes.push('current');
            }

            if (cell.inRange && ((cell.type === 'normal' || cell.type === 'today') || this.selectionMode === 'week')) {
                classes.push('in-range');

                if (cell.start) {
                    classes.push('start-date');
                }

                if (cell.end) {
                    classes.push('end-date');
                }
            }

            if (cell.disabled) {
                classes.push('disabled');
            }

            if (cell.selected) {
                classes.push('selected');
            }

            if (cell.customClass) {
                classes.push(cell.customClass);
            }

            return classes.join(' ');
        },

        getDateOfCell (row, column) {
            const offsetFromStart = row * 7 + (column - (this.showWeekNumber ? 1 : 0)) - this.offsetDay;
            return util_date.nextDate(this.startDate, offsetFromStart);
        },

        markRange (minDate, maxDate) {
            minDate = getDateTimestamp(minDate);
            maxDate = getDateTimestamp(maxDate) || minDate;
            [minDate, maxDate] = [Math.min(minDate, maxDate), Math.max(minDate, maxDate)];

            const startDate = this.startDate;
            const rows = this.rows;
            for (let i = 0, k = rows.length; i < k; i++) {
                const row = rows[i];
                for (let j = 0, l = row.length; j < l; j++) {
                    if (this.showWeekNumber && j === 0) continue;

                    const cell = row[j];
                    const index = i * 7 + j + (this.showWeekNumber ? -1 : 0);
                    const time = util_date.nextDate(startDate, index - this.offsetDay).getTime();

                    cell.inRange = minDate && time >= minDate && time <= maxDate;
                    cell.start = minDate && time === minDate;
                    cell.end = maxDate && time === maxDate;
                }
            }
        },

        // 鼠标移动，针对范围选择
        handleMouseMove (event) {
            if (!this.rangeState.selecting) return;   // 只针对range范围类型

            let target = event.target;
            if (target.tagName === 'SPAN') {
                target = target.parentNode.parentNode;
            }
            if (target.tagName === 'DIV') {
                target = target.parentNode;
            }
            if (target.tagName !== 'TD') return;

            const row = target.parentNode.rowIndex - 1;
            const column = target.cellIndex;

            // can not select disabled date
            if (this.rows[row][column].disabled) return;

            // only update rangeState when mouse moves to a new cell
            // this avoids frequent Date object creation and improves performance
            if (row !== this.lastRow || column !== this.lastColumn) {
                this.lastRow = row;
                this.lastColumn = column;
                this.$emit('changerange', {
                    minDate: this.minDate,
                    maxDate: this.maxDate,
                    rangeState: {
                        selecting: true,
                        endDate: this.getDateOfCell(row, column)
                    }
                });
            }
        },

        // 点击每个日期
        handleClick (event) {
            try {
                let target = event.target;
                if (target.tagName === 'SPAN') {
                    target = target.parentNode.parentNode;
                }
                if (target.tagName === 'DIV') {
                    target = target.parentNode;
                }

                if (target.tagName !== 'TD') return;   // 如果点击的不是每一个小格子 td，不处理

                const row = target.parentNode.rowIndex - 1;
                const column = this.selectionMode === 'week' ? 1 : target.cellIndex;
                const cell = this.rows[row][column];

                if (cell.disabled || cell.type === 'week') return;

                const newDate = this.getDateOfCell(row, column);

                if (this.selectionMode === 'range') {
                    // 范围选择点击
                    if (!this.rangeState.selecting) {
                        this.$emit('pick', { minDate: newDate, maxDate: null });
                        this.rangeState.selecting = true;
                    } else {
                        if (newDate >= this.minDate) {
                            this.$emit('pick', { minDate: this.minDate, maxDate: newDate });
                        } else {
                            this.$emit('pick', { minDate: newDate, maxDate: this.minDate });
                        }
                        this.rangeState.selecting = false;
                    }
                } else if (this.selectionMode === 'day') {
                    this.$emit('pick', newDate);
                }
            } catch (err) {
                console.log('表格内数据点击 handleClick ', err);
            }
        },
    }
})
// 月份表格
Vue.component('ui-month-table', {
    template: `
        <table @click="handleMonthTableClick" class="vd-ui-month-table">
            <tbody>
                <tr v-for="(row, key) in rows" :key="key">
                    <td :class="getCellStyle(cell)" v-for="(cell, key) in row" :key="key">
                        <div>
                            <a class="cell">{{ monthsShow[cell.text] }}</a>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    `,

    props: {
        disabledDate: {},
        value: {},
        selectionMode: {
            default: 'month'
        },
        defaultValue: {
            validator(val) {
                // null or valid Date Object
                return val === null || util_date.isDate(val) || (Array.isArray(val) && val.every(util_date.isDate));
            }
        },
        date: {},
    },

    data() {
        return {
            months: ['jan', 'feb', 'mar', 'apr', 'may', 'jun', 'jul', 'aug', 'sep', 'oct', 'nov', 'dec'],
            monthsShow: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
            tableRows: [ [], [], [] ],
            lastRow: null,
            lastColumn: null
        };
    },

    computed: {
        rows() {
            // TODO: refactory rows / getCellClasses
            const rows = this.tableRows;
            const disabledDate = this.disabledDate;
            const selectedDate = [];
            const now = this.getMonthTimestamp(new Date());

            for (let i = 0; i < 3; i++) {
                const row = rows[i];
                for (let j = 0; j < 4; j++) {
                    let cell = row[j];
                    if (!cell) {
                        cell = { row: i, column: j, type: 'normal', inRange: false, start: false, end: false };
                    }

                    cell.type = 'normal';

                    const index = i * 4 + j;
                    const time = new Date(this.date.getFullYear(), index).getTime();
                    // cell.inRange = time >= this.getMonthTimestamp(this.minDate) && time <= this.getMonthTimestamp(this.maxDate);
                    // cell.start = this.minDate && time === this.getMonthTimestamp(this.minDate);
                    // cell.end = this.maxDate && time === this.getMonthTimestamp(this.maxDate);
                    const isToday = time === now;

                    if (isToday) {
                        cell.type = 'today';
                    }
                    cell.text = index;
                    let cellDate = new Date(time);
                    cell.disabled = typeof disabledDate === 'function' && disabledDate(cellDate);
                    cell.selected = util_date.arrayFind(selectedDate, date => date.getTime() === cellDate.getTime());

                    this.$set(row, j, cell);
                }
            }
            return rows;
        }
    },

    methods: {
        datesInMonth (year, month) {
            const numOfDays = util_date.getDayCountOfMonth(year, month);
            const firstDay = new Date(year, month, 1);
            return util_date.range(numOfDays).map(n => util_date.nextDate(firstDay, n));
        },
        clearDate (date) {
            return new Date(date.getFullYear(), date.getMonth());
        },
        getMonthTimestamp (time) {
            if (typeof time === 'number' || typeof time === 'string') {
                return this.clearDate(new Date(time)).getTime();
            } else if (time instanceof Date) {
                return this.clearDate(time).getTime();
            } else {
                return NaN;
            }
        },

        cellMatchesDate(cell, date) {
            const value = new Date(date);
            return this.date.getFullYear() === value.getFullYear() && Number(cell.text) === value.getMonth();
        },
        getCellStyle(cell) {
            const style = {};
            const year = this.date.getFullYear();
            const today = new Date();
            const month = cell.text;
            const defaultValue = this.defaultValue ? Array.isArray(this.defaultValue) ? this.defaultValue : [this.defaultValue] : [];
            style.disabled = typeof this.disabledDate === 'function'
                ? this.datesInMonth(year, month).every(this.disabledDate)
                : false;
            style.current = util_date.arrayFindIndex(util_date.coerceTruthyValueToArray(this.value), date => date.getFullYear() === year && date.getMonth() === month) >= 0;
            style.today = today.getFullYear() === year && today.getMonth() === month;
            style.default = defaultValue.some(date => this.cellMatchesDate(cell, date));

            if (cell.inRange) {
                style['in-range'] = true;

                if (cell.start) {
                    style['start-date'] = true;
                }

                if (cell.end) {
                    style['end-date'] = true;
                }
            }
            return style;
        },
        getMonthOfCell(month) {
            const year = this.date.getFullYear();
            return new Date(year, month, 1);
        },

        handleMonthTableClick (event) {
            let target = event.target;
            if (target.tagName === 'A') {
                target = target.parentNode.parentNode;
            }
            if (target.tagName === 'DIV') {
                target = target.parentNode;
            }
            if (target.tagName !== 'TD') return;
            if (util_date.hasClass(target, 'disabled')) return;


            const column = target.cellIndex;
            const row = target.parentNode.rowIndex;
            const month = row * 4 + column;
            this.$emit('pick', month);
        }
    },
})
Vue.component('ui-time-table', {
    template: `
        <div class="spinner-list">
            <div class="spinner-item">
                <ul class="">
                    <li
                        v-for="t in arrY" :key="t"
                        :class="{ 'active': hour == t }"
                        @click="chooseHour(t)"
                    >{{ t }}</li>
                </ul>
            </div>
            <div class="spinner-item">
                <ul class="">
                    <li
                        v-for="t in arrMD" :key="t"
                        :class="{ 'active': minute == t }"
                        @click="chooseMinute(t)"
                        >{{ t }}</li>
                    </ul>
                </div>
                <div class="spinner-item">
                    <ul class="">
                        <li
                        v-for="t in arrMD" :key="t"
                        :class="{ 'active': second == t }"
                        @click="chooseSecond(t)"
                    >{{ t }}</li>
                </ul>
            </div>
        </div>
    `,
    props: {
        type: String,
        date: Date
    },
    data () {
        return {
            arrY: [
                '00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11',
                '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'
            ],
            arrMD: [
                '00', '01', '02', '03', '04', '05', '06', '07', '08', '09',
                '10', '11', '12', '13', '14', '15', '16', '17', '18', '19',
                '20', '21', '22', '23', '24', '25', '26', '27', '28', '29',
                '30', '31', '32', '33', '34', '35', '36', '37', '38', '39',
                '40', '41', '42', '43', '44', '45', '46', '47', '48', '49',
                '50', '51', '52', '53', '54', '55', '56', '57', '58', '59',
            ]
        }
    },
    computed: {
        year () {
            return this.date.getFullYear();
        },
        month () {
            return this.date.getMonth();
        },
        day () {
            return this.date.getDate();
        },
        // 时
        hour () {
            let h = this.date.getHours();
            return h < 10 ? '0' + h : h
        },
        // 分
        minute () {
            let m = this.date.getMinutes();
            return m < 10 ? '0' + m : m;
        },
        // 秒
        second () {
            let s = this.date.getSeconds();
            return s < 10 ? '0' + s : s;
        },
    },
    methods: {
        chooseHour (t) {
            let date = new Date(this.year, this.month, this.day, t, this.minute, this.second);
            this.$emit('pick', date);
        },
        chooseMinute (t) { 
            let date = new Date(this.year, this.month, this.day, this.hour, t, this.second);
            this.$emit('pick', date);
        },
        chooseSecond (t) { 
            let date = new Date(this.year, this.month, this.day, this.hour, this.minute, t);
            this.$emit('pick', date);
        }
    }

})

// 年份表格
Vue.component('ui-year-table', {
    template: `
        <table @click="handleYearTableClick" class="vd-ui-year-table">
            <tbody>
                <tr>
                    <td class="available" :class="getCellStyle(startYear + 0)">
                        <a class="cell">{{ startYear }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 1)">
                        <a class="cell">{{ startYear + 1 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 2)">
                        <a class="cell">{{ startYear + 2 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 3)">
                        <a class="cell">{{ startYear + 3 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 4)">
                        <a class="cell">{{ startYear + 4 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 5)">
                        <a class="cell">{{ startYear + 5 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 6)">
                        <a class="cell">{{ startYear + 6 }}</a>
                    </td>
                    <td class="available" :class="getCellStyle(startYear + 7)">
                        <a class="cell">{{ startYear + 7 }}</a>
                    </td>

                    <td class="available" :class="getCellStyle(startYear + 8)">
                        <a class="cell">{{ startYear + 8 }}</a>
                    </td>
                </tr>

                <tr>
                    <td class="available" :class="getCellStyle(startYear + 9)">
                        <a class="cell">{{ startYear + 9 }}</a>
                    </td>
                    <td  class="available" :class="getCellStyle(startYear + 10)">
                        <a class="cell">{{ startYear + 10 }}</a>
                    </td>
                    <td  class="available" :class="getCellStyle(startYear + 11)">
                        <a class="cell">{{ startYear + 11 }}</a>
                    </td>
                </tr>
            </tbody>
        </table>
    `,

    props: {
        disabledDate: {},
        value: {},
        defaultValue: {
            validator(val) {
                return val === null || (val instanceof Date && util_date.isDate(val));
            }
        },
        date: {}
    },

    computed: {
        startYear () {
            return Math.floor(this.date.getFullYear() / 12) * 12;
        }
    },

    methods: {
        datesInYear (year) {
            const numOfDays = util_date.getDayCountOfYear(year);
            const firstDay = new Date(year, 0, 1);
            return util_date.range(numOfDays).map(n => util_date.nextDate(firstDay, n));
        },

        getCellStyle (year) {
            const style = {};
            const today = new Date();

            style.disabled = typeof this.disabledDate === 'function'
                ? this.datesInYear(year).every(this.disabledDate)
                : false;

            style.current = util_date.arrayFindIndex(util_date.coerceTruthyValueToArray(this.value), date => date.getFullYear() === year ) >= 0;
            style.today = today.getFullYear() === year;
            style.default = this.defaultValue && this.defaultValue.getFullYear() === year;

            return style;
        },

        handleYearTableClick (event) {
            const target = event.target;

            if (target.tagName === 'A') {

                if (util_date.hasClass(target.parentNode, 'disabled')) return;

                const year = target.textContent || target.innerText;
                this.$emit('pick', Number(year));
            }
        }
    }
})
let uiDialogVue = Vue.component('ui-dialog', {
    template: `
        <transition name="dialog-fade">
            <div class="mui-dialog-container" v-show="isShow">
                <div class="nui-custom-dislog-center">
                    <transition name="dialog-move">
                        <div class="mui-dialog-wrapper" v-show="isShow" :class="[type]">
                            <div class="mui-dialog-content">
                                <div class="mui-dialog-icon" v-if="type">
                                    <i class="vd-ui_icon" :class='icon'></i>
                                </div>
                                <div class="mui-dialog-title" v-if="title" v-html="title"></div>
                                <div class="mui-dialog-middle" v-if="message" v-html="message"></div>
                            </div>

                            <div class="mui-dialog-button-choice">
                                <div
                                    class='mui-dialog-button'
                                    v-for='(item, index) in buttons'
                                    :class='item.btnClass'
                                    @click="handleButton(item)"
                                    :key="index"
                                >{{ item.txt }}</div>
                            </div>
                        </div>
                    </transition>

                </div>
            </div>
        </transition>
    `,
    data() {
        return {
            isShow: false,
            type: '',
            title: '',
            message: '',
            buttons: [],
            defaultClass: '',  // 按钮默认class
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            }
            // btnClass： disabled:置灰色  confirm:蓝色  delete:红色  cancel:默认色
        }
    },
    computed: {
        icon() {
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        },
        handleButton(item) {
            this.isShow = false;
            if (item.callback) {
                item.callback();
            }
        },
        deleteButton() {
            this.isShow = false;
            this.handleDelete && this.handleDelete();
        }
    }
})

let installDialogComponents = () => {
    const VdPop = Vue.extend(uiDialogVue);
    let vm = null;

    const showDialog = ({type, title, message, ...opt}) => {
        return new Promise((resolve, reject) => {
            if (!vm) {
                vm = new VdPop();
                vm.$mount();
                document.body.appendChild(vm.$el);
            } else {
                vm.hide();
            }

            vm.type = type || '';
            vm.title = title || '';
            vm.message = message || '';
            vm.buttons = opt.buttons || vm.buttons;
            vm.handleDelete = opt.handleDelete || vm.handleDelete;
            vm.defaultClass = opt.defaultClass || vm.defaultClass;

            vm.show();
        });
    }

    const fn = (type, opt) => {
        return Promise.resolve(showDialog(type, opt));
    };

    showDialog.success = fn.bind(null, 'success');
    showDialog.error = fn.bind(null, 'error');
    showDialog.warn = fn.bind(null, 'warn');
    showDialog.info = fn.bind(null, 'info');

    Vue.prototype.$dialog = showDialog;
}

installDialogComponents();
/* 拜访目标 */
Vue.component('ui-form-checkbox', {
    template: `
        <div class="ui-form-checkbox-wrap">
            <div class="form-checkbox-inner" @click="openSlideDialog">
                <ui-placeholder v-if="!labelsShow">{{ placeholder }}</ui-placeholder>
                <div v-else class="form-checkbox-show">
                    <div class="value">{{ labelsShow }}</div>
                </div>
            </div>

            <crm-slide-dialog ref="slideDialog" :title="title">
                <div class="form-checkbox-panel">
                    <ui-checkbox-group
                        :list="list"
                        v-model="formValue"
                        :single-row="true"
                        @change="handlerChange"
                    ></ui-checkbox-group>
                    <div class="slide-dialog-default-footer">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,
    props: {
        // 外层确定的数据，仅展示及初始化
        value: {
            type: Array,
            default: () => ([])
        },
        // 
        list: Array,
        placeholder: {
            type: String
        },

        // slide query
        title: String
    },
    data () {
        return {
            formValue: [], // 组件内value
        }
    },
    watch: {
        // value: {
        //     handler (newV) {
        //         this.formValue = newV;
        //     },
        //     deep: true,
        // },
    },
    computed: {
        labelsShow () {
            let labels = [];
            this.list.forEach(item => {
                if (this.value.includes(item.value)) {
                    labels.push(item.label);
                }
            })
            return labels.join('、');
        }
    },
    mounted() {
       
    },
    methods: {
        openSlideDialog () {
            this.formValue = JSON.parse(JSON.stringify(this.value));
            this.$refs.slideDialog.show();
        },

        handlerChange (data) {
            console.log('formValue:', this.formValue);
        },

        // 确定
        handlerConfirm () {
            // if (!this.formValue.length) {
            //     this.$message({
            //         type: 'error',
            //         message: '请选择地址',
            //     })
            //     return;
            // }
            
            let emitData = Object.assign([], this.formData);
            this.$emit("input", this.formValue); // 修改外层v-model值
            this.$emit('change', emitData);
            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();
        },
    }
})
Vue.component('ui-form-item', {
    template: `
        <div class="form-item" :class="{'vertical': vertical}">
            <div
                class="form-label" 
                :class="{'middle': labelMiddle}"
                :style="{'width': labelWidth}"
            ><span class="must" v-if="must">*</span><span v-html="label"></span></div>
            <div class="form-fields" :class="{'no-padding': noPadding}">
                <slot></slot>
            </div>
        </div>
    `,
    props: {
        label: {
            type: String,
            default: ''
        },
        labelMiddle: {
            type: Boolean,
            default: false,
        },
        must: {
            type: Boolean,
            default: false
        },
        noPadding: {
            type: Boolean,
            default: false
        },
        labelWidth: {
            type: String,
            default: ''
        },
        // 内容是否换行
        vertical: {
            type: Boolean,
            default: false
        },
    },
    mounted() {
        this.label = this.label.replace(/\n/g, '<br/>')
    },
    methods: {
       
    }
})

Vue.component('ui-placeholder', {
    template: `
        <div class="form-placeholder" @click="handlerClick">
            <span>
                <slot></slot>
            </span>
            <i :class="['vd-ui_icon', icon]"></i>
        </div>
    `,
    props: {
        icon: {
            type: String,
            default: 'icon-right'
        },
    },
    data() {
        return {
        };
    },
    mounted() {
    },
    methods: {
        handlerClick () {
            this.$emit('click');
        }
    }
})


Vue.component('ui-tip', {
    template: `
        <div
            class="form-tip"
            :class="[type, {
                'style2': styles == 2
            }]"
            :style="{
                'margin-top': marginTop,
                'margin-bottom': marginBottom,
            }"
            @click="handlerClick"
        >
            <i :class="['vd-ui_icon', Icons[type]]"></i>
            <span>
                <slot></slot>
            </span>
        </div>
    `,
    props: {
        type: {
            type: String,
            default: 'warn'
        },
        styles: {
            type: String,
            default: '1', // 1::默认提示文案  2:底色提示文案
        },
        marginTop: {
            type: String,
            default: '0'
        },
        marginBottom: {
            type: String,
            default: '0'
        },

        // 逻辑相关
        tel: {
            type: String,
        }
    },
    data() {
        return {
            Icons: {
                // 提示相关
                'warn': 'icon-caution1',
                'success': 'icon-yes1',
                'error': 'icon-error2',
                'info': 'icon-info2',

                // 业务逻辑相关
                'map': 'icon-address',
                'tel': 'icon-call2'
            }
        };
    },
    mounted() {
    },
    methods: {
        handlerClick () {
            if (this.type == 'tel') {
                window.location.href = 'tel:'+this.tel;
            }
            this.$emit('click');
        }
    }
})
// 获取联系人·企微userid， 并打开企微聊天窗口
let GLOBAL = {
    // 复制
    copyTextToClipboard(text, queryThis, duration=800) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text);
        } else {
            let input = document.createElement('textarea');
            input.value = text;
            document.body.appendChild(input);
            input.select();
            document.execCommand('copy');
            document.body.removeChild(input);
        }

        queryThis.$message({
            message: '复制成功',
            duration: duration
        });
    },
    // 全局加载状态
    showGlobalLoading(font) {
        console.log('font:', font);
        let loadingEle = document.createElement('div');
        loadingEle.setAttribute('class', 'global__loading__wrap');
        loadingEle.setAttribute('id', 'J-global-loading-wrap');
        loadingEle.innerHTML = `<i class="vd-ui_icon icon-loading"></i>`;
        if (font) {
            let fontEle = document.createElement('p');
            fontEle.setAttribute('class', 'global__loading__p');
            fontEle.innerText = font;
            loadingEle.appendChild(fontEle);
        }
        document.body.appendChild(loadingEle);
    },
    hideGlobalLoading() {
        var element = document.getElementById('J-global-loading-wrap');
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
        }
    },
    //获取路由参数
    getQuery(key) {
        let params = new URLSearchParams(window.location.search);
        return params.get(key) || '';
    },
    defaultAvatar: '/static/image/crm-user-avatar.svg',
    avatarerror(e) {
        console.log(e)
    },
    BUSINESSLEADS_STATUS: {
        0: "待分配",
        1: "待跟进",
        2: "跟进中",
        3: "已关闭",
        4: "已商机"
    },
    BUSINESSCHANCE_STAGE: {
        1: "初步洽谈",
        2: "商机验证",
        3: "初步方案",
        4: "最终方案",
        5: "赢单",
        6: "关闭"
    },
    auth(key) {
        if (!key) {
            return false;
        }

        let auths = document.querySelector('#golbal_permissions') ? document.querySelector('#golbal_permissions').value : '';
        let authList = auths.split(',');

        if (authList.indexOf(key) !== -1) {
            return true;
        } else {
            return false;
        }
    },
    showNoAuth() {
        if (Vue) {
            Vue.prototype.$popup.warn({
                title: '抱歉，当前您没有访问权限',
                message: '权限开通：请企业微信联系研发部Aadi。',
                buttons: [{
                    txt: '我知道了',
                    btnClass: 'confirm',
                }]
            })
        }
    },
    download(url, name) {
        axios.get(url, {
            responseType: 'blob',
        }).then(res => {
            let blob = new Blob([res.data]);
            let objectUrl = URL.createObjectURL(blob) // 创建URL

            let link = document.createElement('a');
            link.href = objectUrl;
            link.download = name;
            document.body.appendChild(link)

            let event = new MouseEvent('click')
            link.dispatchEvent(event)
            setTimeout(() => {
                document.body.removeChild(link)
            }, 500)
        }).catch(() => { })
    },
    wxregister(jsApiList, callback) {
        axios.get('/crm/wx/getWeiXinPermissionsValidationConfig?url=' + encodeURIComponent(window.location.href.split("#")[0]))
            .then(({ data }) => {
                if (data.success) {
                    try {
                        ww.register({
                            agentId: data.data.agentid,
                            corpId: data.data.corpid,
                            jsApiList: jsApiList,

                            // crm企微应用ticket
                            // getAgentConfigSignature() {
                            //     return {
                            //         nonceStr: data.data.nonceStr,
                            //         timestamp: data.data.timestamp,
                            //         signature: data.data.signature
                            //     };
                            // },
                            // async onAgentConfigSuccess(ee) {
                            //     console.log(' --------------- register success --------------- ');
                            // },
                            // onAgentConfigFail(ee) {
                            //     console.log('register Fail', ee);
                            // },

                            // 企微企业ticket信息
                            getConfigSignature() {
                                return {
                                    nonceStr: data.data.nonceStr,
                                    timestamp: data.data.timestamp,
                                    signature: data.data.signature
                                };
                            },
                            onConfigSuccess(ee) {
                                console.log('register success 2 =====>', ee);
                                if (callback) {
                                    callback();
                                }
                            },
                            onConfigFail (ee) {
                                console.log('register Fail 2 =====>', ee);
                            }
                        })

                        ww.checkJsApi({
                            jsApiList: jsApiList,
                            success(res) {
                                console.log('checkJsApi res:', res);
                            },
                            fail(err) {
                                console.log('checkJsApi err:', err);
                            }
                        })
                    } catch (errr) {
                        console.log('errr:', errr);
                    }
                }
            })
    },

    /* 打开企微会话
     * queryThis: 必须   调用处this指向
     * userId     必须   用户id
     * isQwId     非必须 是否是企微id
    */
    openQwChat(queryThis, userId, isQwId) {
        if (!userId) return;
        userId = userId + '';

        if (isQwId) {
            ww.openEnterpriseChat({
                userIds: [userId],
            })
        }
        else {
            axios.get('/crm/visitrecord/m/getUserInfo?userId='+userId)
            .then(({data}) => {
                if (data.success) {
                    let qwId = data.data.number + '';
                    ww.openEnterpriseChat({
                        userIds: [qwId],
                    })
                } else {
                    queryThis.$message({
                        message: data.message,
                        duration: 800
                    });
                }
            }).catch(() => { })
        }
    },
};

window.onload = () => {
    document.querySelector('#page-container').classList.add('show');
}

Vue.component('ui-input', {
    template: `<div class="vd-ui-input-wrap">
        <input class="vd-ui-input" 
            :type="inputType" 
            :class="{
                'border': border
            }"
            v-model="inputValue" 
            :placeholder="placeholder" 
            :disabled="disabled"
            :readonly="readonly"
            :maxlength="maxlength"
            @input="handlerInput"
            @change="handlerChange"
            @focus="handlerFocus"
            @blur="handlerBlur"
            @keydown="handlerKeydown"
            @keyup="handlerKeyup"
            @compositionend="commentPressEnd"
            @compositionstart="commentPressStart"
            autocomplete="off"
        />
        <i class="vd-ui_icon icon icon-error2" v-if="clear && isFocus && inputValue" @click="handlerClear"></i>
        <slot></slot>
    </div>`,
    props: {
        border: {
            type: Boolean,
            default: false
        },
        size: {
            type: String,
            default: '' //small
        },
        placeholder: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: 'text'
        },
        maxlength: {
            type: String,
            default: ''
        },
        value: {
            type: [String, Number],
            default: ''
        },
        clear: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            inputValue: '',
            inputType: '',
            isFocus: false,
            onCompressing: false
        };
    },
    computed: {

    },
    watch: {
        value: {
            handler (newV) {
                this.inputValue = newV;
            },
            immediate: true
        }
    },
    mounted() {
        if (this.type === 'number') {
            this.inputType = 'text';
        } else {
            this.inputType = this.type;
        }
    },
    methods: {
        handlerInput(e) {
            let targetValue = e && e.target && e.target.value || ''
            if (this.type == 'tel') {
                let val = targetValue.replace(/[^\d]/g, '');
                this.inputValue = val;
            }

            if (this.onCompressing) {
                return;
            }

            this.$emit('input', this.inputValue);
            this.$emit('change', this.inputValue);
        },
        handlerChange(e) {
            this.$emit('change', e);
        },
        handlerFocus(e) {
            this.isFocus = true;
            this.$emit('focus', e);
        },
        handlerBlur(e) {
            setTimeout(() => {
                this.isFocus = false;
                this.$emit('blur', e);
            }, 100)
        },
        handlerKeydown(e) {
            this.$emit('keydown', e);
        },
        handlerKeyup(e) {
            this.$emit('keyup', e);
        },
        handlerClear() {
            this.inputValue = "";
            this.handlerInput();
        },
        commentPressStart() {
            this.onCompressing = true;
        },
        commentPressEnd(e) {
            this.onCompressing = false;
            this.handlerInput(e);
        }
    }
})
Vue.component('crm-m-search-item', {
    template: `<div class="crm-m-search-item" v-show="isShow" :class="{vertical: type === 'vertical' || (type === 'toggle' && active), active: active}">
        <div class="crm-m-search-item-title">{{ title }}</div>
        <div class="crm-m-search-item-cnt">
            <slot></slot>
        </div>
    </div>`,
    name: 'crm-m-search-item',
    props: {
        title: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: '' //默认左右,vertical:上下结构,toggle:无值左右，有值上下结构
        },
        value: {
            type: String | Number | Array | Object,
        },
        lock: {
            type: Boolean,
            default: false
        },
        vkey: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isShow: true
        };
    },
    computed: {
        active() {
            let flag = false;
            let value = this.$parent.$parent.value[this.vkey];

            console.log(this.value)

            switch (typeof value) {
                case 'string': {
                    if (value && value.trim()) {
                        flag = true;
                    }
                    break;
                }
                case 'number': {
                    if (value) {
                        flag = true;
                    }
                    break;
                }
                case 'object': {
                    if (value) {
                        if (Array.isArray(value) && value.length) {
                            flag = true;
                        } else if (Object.keys(value).length) {
                            flag = true;
                        }
                    }
                    break;
                }
            }

            return flag;
        }
    },
    mounted() {
    },
    methods: {
        toggleCheck() {
            this.checked = !this.checked;
            this.$emit('update:checked', this.checked);
            this.$emit('change', this.checked);
        }
    }
})

Vue.component('crm-list-container', {
    template: `<div class="crm-m-list-layout">
        <div class="crm-m-list-header">
            <div class="crm-m-list-tab" @click="toggleShowTabList">
                <div class="crm-m-list-tab-txt" :class="{show: isShowTab}">{{ tabTxt }}<i class="vd-ui_icon icon-down" v-if="tabList.length"></i></div>
            </div>
            <div class="crm-m-list-header-options">
                <div class="header-option-item option-sort" :class="{show: isShowSort}" v-if="!(!filterNum && !(list && list.length)) && filterSortList && filterSortList.length" @click="toggleShowSort">
                    <i class="vd-ui_icon icon-paixu"></i>
                </div>
                <div class="header-option-item option-filter" @click="showFilterList">
                    <i class="vd-ui_icon icon-filter"></i>
                    <div class="header-option-filter-num" v-if="filterNum">
                        <div class="num-txt">{{ filterNum }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="crm-m-list-cnt" ref="listContainer" v-show="!isFirstloading">
            <template v-if="list && list.length"> 
                <div class="crm-m-list-inner">
                    <template v-for="(item, index) in list">
                        <slot name="list-item" v-bind:item="item"></slot>
                    </template>
                </div>
                <div class="crm-m-list-loading" v-show="list.length < total">
                    <i class="vd-ui_icon icon-loading"></i>加载中...
                </div>
            </template>
            <template v-else>
                <div class="crm-m-list-empty">
                    <template v-if="!filterNum">
                        <div class="crm-m-list-empty-pic">
                            <img src="/mstatic/image/empty/list-empty.svg" />
                        </div>
                        <div class="crm-m-list-empty-txt">暂无数据</div>
                    </template>
                    <template v-else>
                        <div class="crm-m-list-empty-pic">
                            <img src="/mstatic/image/empty/search-empty.svg" />
                        </div>
                        <div class="crm-m-list-empty-txt">抱歉，没有找到您想搜索的结果</div>
                    </template>
                </div>
            </template>
        </div>
        <crm-slide-dialog ref="listTab" :isShow.sync="isShowTab" v-if="tabList.length" zindex="10" type="down">
            <div class="crm-m-tab-wrap">
                <div class="crm-m-tab-item" @click="changeTab({})" :class="{active: !customSearchId && customSearchId !== 0}">
                    <div class="crm-m-tab-item-txt">{{ title }}</div>
                </div>
                <div class="crm-m-tab-item" @click="changeTab(item)" v-for="(item, index) in tabList" :class="{active: customSearchId == item.id}">
                    <div class="crm-m-tab-item-txt">{{ item.label }}</div>
                    <i class="vd-ui_icon icon-edit" @click.stop="editCustomFilter(item)"></i>
                    <i class="vd-ui_icon icon-delete" @click.stop="deleteCustomSearch(item)"></i>
                </div>
            </div>
        </crm-slide-dialog>
        <crm-slide-dialog ref="listSort" :isShow.sync="isShowSort" v-if="filterSortList.length" zindex="10" type="down">
            <div class="crm-m-sort-wrap">
                <div class="crm-m-sort-item" @click="changeSort(item)" v-for="(item, index) in filterSortList" :class="{active: sortOrder == item.order}">
                    {{ item.label }}
                </div>
            </div>
        </crm-slide-dialog>
        <crm-slide-dialog ref="filterList" title="筛选" topOptionTxt="设置" :maskHide="false" @topOption="showFilterSetting">
            <div class="crm-list-filter-wrap">
                <div class="crm-list-filter-cnt" ref="filterContent">
                    <slot name="filter"></slot>
                </div>
                <div class="crm-list-filter-footer">
                    <div class="filter-footer-option" :class="{disabled: tabList.length >= 5}" @click="addCustomFilter">
                        <i class="option-icon option-add"></i>
                        <div class="option-txt">自定义</div>
                    </div>
                    <div class="filter-footer-option" @click="resetSearchParams">
                        <i class="option-icon option-reset"></i>
                        <div class="option-txt">重置</div>
                    </div>
                    <div class="filter-footer-btns">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="hideFilterList">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerSearch">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
        <crm-slide-dialog ref="filterSetting" title="搜索筛选设置" :maskHide="false">
            <div class="crm-list-filter-setting">
                <draggable v-model="filterSettingShowList" class="filter-setting-list" ghost-class="placehodler" @sort="handlerSettingSort">
                    <div class="filter-setting-item" :class="{active: item.isShow || item.disabled, disabled: item.disabled}" v-for="(item, index) in filterSettingShowList">
                        <div class="filter-setting-item-txt">{{ item.label }}</div>
                        <div class="filter-setting-item-option">
                            <vd-ui-toggle :checked.sync="item.isShow" :disabled="item.disabled"></vd-ui-toggle>
                        </div>
                    </div>
                </draggable>
                <div class="filter-setting-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="hideFilterSetting">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="saveSettingData">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
        <ui-custom-dialog :isShow.sync="isShowCustomFilterDialog" title="自定义筛选" :lock="true">
            <div class="crm-filter-add-wrap">
                <div class="crm-filter-form-wrap">
                    <div class="crm-filter-form-label">
                        <span class="must">*</span>
                        <div>标题名称：</div>
                    </div>
                    <div class="crm-filter-form-cnt">
                        <ui-input maxlength="7" v-model="customFilterName"></ui-input>
                    </div>
                </div>
                <div class="crm-filter-form-tip">建议标题设置简约、直接、准确，最多可输入7个字</div>
            </div>
            <template v-slot:footer>
                <button 
                    class="mui-cdb"
                    @click="isShowCustomFilterDialog = false"
                >取消</button>
                <button 
                    class="mui-cdb confirm"
                    :class="{disabled: !customFilterName.trim()}"
                    @click="saveCustomFilter"
                >保存</button>
            </template>
        </ui-custom-dialog>
        <a class="crm-list-add" v-if="addLink" :href="addLink">
            <i class="vd-ui_icon icon-add"></i>
        </a>
    </div>`,
    props: {
        title: '',
        filterSortList: {
            type: Array,
            default() {
                return [];
            }
        },
        addLink: '',
        value: {},
        listKey: '',
        listUrl: ''
    },
    data() {
        return {
            tabList: [],
            customSearchId: '',
            isShowTab: false,
            tabTxt: '',
            isShowSort: false,
            sortOrder: '',
            filterSettingList: [],
            filterSettingShowList: [],
            list: [],
            total: 0,
            isFirstloading: true, //首次加载隐藏页面
            isloading: false,
            pageNum: 1,
            searchParams: {},
            querySettingId: '', //参数保存id 
            isShowCustomFilterDialog: false,
            customFilterName: '',
            customEditId: '',
            filterNum: 0,
            filterKeys: []
        };
    },
    computed: {

    },
    mounted() {
        this.getFilterKeys();
        this.tabTxt = this.title;
        this.$nextTick(() => {
            this.getSettingInfo();
        })
        this.getList();
        window.addEventListener('scroll', this.checkScroll);

        this.searchParams = JSON.parse(JSON.stringify(this.value));
    },
    methods: {
        getFilterKeys() {
            this.$refs.filterList.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name === 'crm-m-search-item' && searchItem.vkey) {
                    this.filterKeys.push(searchItem.vkey)
                }
            })

            console.log(this.filterKeys)
        },
        getList() {
            this.isloading = true;
            if(this.pageNum == 1) {
                GLOBAL.showGlobalLoading();
            }

            if(this.isFirstloading) {
                this.getFilterNum();
            }

            axios.post(this.listUrl, {
                pageNum: this.pageNum,
                pageSize: 100,
                orderBy: this.sortOrder,
                param: this.value
            }).then(({ data }) => {
                this.isloading = false;
                GLOBAL.hideGlobalLoading();
                
                this.isFirstloading = false;
                if (data.code === 0) {
                    if(this.pageNum == 1) {
                        this.list = data.data.list;
                    } else {
                        this.list = this.list.concat(data.data.list);
                    }
                    this.total = data.data.total;
                }
            })
        },
        checkScroll() {
            let winScroll = window.scrollY;
            let containerHeight = this.$refs.listContainer.offsetHeight;
            let pageHeight = window.screen.height;

            if (!this.isloading && this.list.length < this.total && pageHeight + winScroll > containerHeight - 50) {
                this.pageNum++;
                this.getList();
            }
        },
        getSettingInfo() {
            this.$axios.post('/crm/common/m/searchList', {
                searchFromEnum: this.listKey
            }).then(({ data }) => {
                if (data.code === 0) {
                    let tabList = data.data.conditionList || [];
                    let settingList = data.data.queryMap ? JSON.parse(data.data.queryMap.searchContent || '[]') : [];
                    this.querySettingId = data.data.queryMap ? data.data.queryMap.id : '';

                    tabList.forEach(item => {
                        this.tabList.push({
                            label: item.searchName,
                            id: item.id,
                            isCustom: true,
                            customData: JSON.parse(item.searchContent)
                        })
                    })

                    let filterSettinglist = [];
                    this.$refs.filterList.$children.forEach((item, index) => {
                        if(item.$options.name === 'crm-m-search-item') {
                            let flag = false;
                            settingList.forEach(settingItem => {
                                if(item.title === settingItem.label) {
                                    filterSettinglist.push(settingItem);
                                    flag = true;
                                }
                            })

                            console.log(item.$options.propsData.lock)

                            if(!flag) {
                                filterSettinglist.push({
                                    label: item.title,
                                    sort: index,
                                    isShow: true,
                                    disabled: !!item.$options.propsData.lock
                                });
                            }
                        }
                    })

                    this.filterSettingList = filterSettinglist;

                    this.checkSettingShow();
                }
            })
        },
        toggleShowTabList() {
            if (this.$refs.listTab) {
                this.isShowTab = !this.isShowTab;
                this.isShowSort = false;
            }
        },
        toggleShowSort() {
            if (this.$refs.listSort) {
                this.isShowSort = !this.isShowSort;
                this.isShowTab = false;
            }
        },
        changeTab(item) {
            if (item.id) {
                this.customSearchId = item.id;
                this.tabTxt = item.label;
                this.value = JSON.parse(JSON.stringify(item.customData));
                this.searchParams = JSON.parse(JSON.stringify(this.value));
            } else {
                this.customSearchId = '';
                this.tabTxt = this.title;
                this.clearParams();
                this.searchParams = JSON.parse(JSON.stringify(this.value));
            }

            this.getList();

            this.$nextTick(() => {
                this.getFilterNum();
            })
            this.isShowTab = false;
        },
        changeSort(item) {
            this.sortOrder = item.order || '';
            this.isShowSort = false;
            this.getList();
        },
        showFilterList() {
            let params = JSON.parse(JSON.stringify(this.searchParams));
            this.$emit('input', params);
            this.$refs.filterList.show();
            this.isShowTab = false;
            this.isShowSort = false;
        },
        hideFilterList() {
            this.$refs.filterList.hide();
        },
        showFilterSetting() {
            this.filterSettingShowList = JSON.parse(JSON.stringify(this.filterSettingList));
            this.filterSettingShowList.sort((a, b) => {
                return a.sort - b.sort;
            })
            this.$refs.filterSetting.show();
        },
        hideFilterSetting() {
            this.$emit('input', this.searchParams);
            this.$refs.filterSetting.hide();
        },
        checkSettingShow() {
            let list = [];
            let num = 0;

            this.$refs.filterList.$children.forEach((searchItem, index) => {
                if (searchItem.$options.name === 'crm-m-search-item' && searchItem.title) {
                    let filterItemInfo = null;

                    this.filterSettingList.forEach(filterItem => {
                        if (searchItem.title === filterItem.label) {
                            filterItemInfo = filterItem;
                            
                            if(!filterItem.isShow) {
                                searchItem.isShow = false;
                            } else {
                                searchItem.isShow = true;
                            }
                        }
                    })

                    list.push({
                        el: searchItem.$el,
                        sort: filterItemInfo ? filterItemInfo.sort : index
                    })

                    num++;
                }
            });

            console.log(list)

            list.sort((a, b) => {
                return a.sort - b.sort;
            })

            list.forEach(item => {
                this.$refs.filterContent.append(item.el);
            })
        },
        handlerSettingSort() {
            this.filterSettingShowList.forEach((item, index) => {
                item.sort = index;
            })
        },
        saveSettingData() {
            this.filterSettingList = JSON.parse(JSON.stringify(this.filterSettingShowList));
            this.checkSettingShow();
            this.hideFilterSetting();

            //将修改的设置存储到后台
            this.$axios.post('/crm/common/m/saveSearchConfig', {
                searchFromEnum: this.listKey,
                searchType: 'QUERY',
                searchContent: JSON.stringify(this.filterSettingList),
                id: this.querySettingId
            }).then(({ data }) => {
                
            })
        },
        deleteCustomSearch(item) {
            this.customEditId = item.id;
            let _this = this;
            this.$dialog({
                type: 'warn',
                message: '删除后将无法恢复，您确定需要删除吗?',
                buttons: [{
                    txt: '取消'
                }, {
                    txt: '删除',
                    btnClass: 'delete',
                    callback() {
                        _this.$axios.post('/crm/common/m/deleteSearchConfig', {
                            id: _this.customEditId
                        }).then(({ data }) => {
                            if (data.success) {
                                _this.tabList.forEach((item, index) => {
                                    if (item.id == _this.customEditId) {
                                        _this.tabList.splice(index, 1);
                                        if (_this.customSearchId == item.id) {
                                            _this.changeTab({});
                                        }
                                    }
                                })

                                _this.$message.success('删除成功');
                            } else {
                                _this.$message.warn(data.message);
                            }
                        })
                    }
                }]
            })
        },
        handlerSearch() {
            this.pageNum == 1;
            this.getList();
            this.searchParams = JSON.parse(JSON.stringify(this.value));
            console.log(this.searchParams)
            this.hideFilterList();

            this.getFilterNum();
        },
        clearParams() {
            for (let item in this.value) {
                if (typeof this.value[item] === 'string' || typeof this.value[item] === 'number') {
                    this.value[item] = '';
                } else if (Array.isArray(this.value[item])) {
                    this.value[item] = [];
                } else if (typeof this.value[item] === 'object') {
                    this.value[item] = {};
                }
            }

            this.$emit('input', this.value);
        },
        resetSearchParams() {
            this.clearParams();
        },
        getFilterNum() {
            let num = 0;

            for (let item in this.value) {
                if(this.filterKeys.indexOf(item) !== -1) {
                    if (typeof this.value[item] === 'string' || typeof this.value[item] === 'number') {
                        if(this.value[item]) {
                            num++;
                        }
                    } else if (Array.isArray(this.value[item])) {
                        if(this.value[item].length) {
                            num++;
                        }
                    } else if (typeof this.value[item] === 'object') {
                        if(Object.keys(this.value[item]).length) {
                            num++;
                        }
                    }
                }
            }

            this.filterNum = num;
        },
        addCustomFilter() {
            if(this.tabList.length > 5) {
                return;
            }

            this.isShowCustomFilterDialog = true;
            this.customFilterName = '';
            this.customEditId = '';
        },
        editCustomFilter(item) {
            this.isShowCustomFilterDialog = true;
            this.customFilterName = item.label;
            this.customEditId = item.id;
        },
        saveCustomFilter() {
            this.$axios.post('/crm/common/m/saveSearchConfig', {
                searchName: this.customFilterName,
                searchFromEnum: this.listKey,
                searchType: "LIST",
                searchContent: JSON.stringify(this.value),
                id: this.customEditId || ''
            }).then(({ data }) => {
                if (data.success) {
                    this.$message.success(this.customEditId ? '操作成功' : '新增成功');
                    this.isShowCustomFilterDialog = false;

                    if (this.customEditId) {
                        this.tabList.forEach(item => {
                            if (item.id == this.customEditId) {
                                item.label = this.customFilterName;

                                if(this.customSearchId == item.id) {
                                    this.tabTxt = this.customFilterName;
                                }
                            }
                        })
                    } else {
                        this.tabList.push({
                            label: this.customFilterName,
                            id: data.data,
                            isCustom: true,
                            customData: this.value
                        })
                    }

                    this.isShowCustomDialog = false;
                } else {
                    this.$message.warn(data.message);
                }
            })
        }
    }
})
let messageVue = Vue.component('ui-message', {
    template: `<transition name='globalToast'>
        <div class='ui-global-toast' v-if='isShow'>
            <div class="ui-global-toast-inner" :class="'ui-message-' + type">
                <i class='vd-ui_icon' :class='icon'></i>
                <span v-html="message"></span>
            </div>
        </div>
    </transition>`,
    data() {
        return {
            isShow: false,
            type: 'success',
            message: '暂无提示弹框类型',
            duration: 3000,
            iconType: {
                'success': 'icon-yes2',
                'error': 'icon-error2',
                'warn': 'icon-caution2',
                'info': 'icon-info2'
            },
        }
    },
    computed: {
        icon(){
            return this.iconType[this.type];
        }
    },
    methods: {
        show() {
            this.isShow = true;
        },
        hide() {
            this.isShow = false;
        }
    }
})

let installMessageComponents = () => {
    const VdMsg = Vue.extend(messageVue);
        let vm = null;
        
        const showMessage = ({message, type, duration, callback}) => {
            //console.log(type,opt);
            return new Promise((resolve, reject) => {
                if(!vm){
                    vm = new VdMsg();
                    vm.$mount();
                    document.body.appendChild(vm.$el);
                }else{
                    vm.hide();
                    clearTimeout(showMessage.hideTimer,showMessage.showTimer);
                }
                
                vm.message = message || vm.message;
                vm.type = type || vm.type;
                vm.duration = duration || vm.duration;
    
                showMessage.showTimer = setTimeout(() => {
                    vm.show();
                });
                
                showMessage.hideTimer = setTimeout(() => {
                    vm.hide();
                    clearTimeout(showMessage.hideTimer,showMessage.showTimer);
                    callback && callback();
                    resolve();
                },vm.duration);
            });
        }
        
        const fn = (type,message,duration,callback) => {
            return Promise.resolve(showMessage({
                type,
                message,
                duration,
                callback
            }));
        };
    
        // const fn = (type,opt) => {
        //     return Promise.resolve(showMessage(type,opt));
        // };
        
        showMessage.success = fn.bind(null,'success');
        showMessage.error = fn.bind(null,'error')
        showMessage.warn = fn.bind(null,'warn')
        showMessage.info = fn.bind(null,'info')
    
        Vue.prototype.$message = showMessage;
}

installMessageComponents();
// 其他联系方式
Vue.component('ui-other-contact-dialog', {
    template: `
        <crm-slide-dialog ref="slideDialog" title="添加联系方式" :refresh="true" @hidn="handlerCancel">
            <div class="other-contact-panel">
                <ui-tip type="info" styles="2">最多可添加 5 个联系方式</ui-tip>
                <div class="other-contact-list" @click.stop v-if="contactList.length">
                    <div 
                        class="other-contact-item" 
                        v-for="(item, index) in contactList" :key="index"
                        @click.stop="chooseThis(item)"
                    >
                        <div class="select">
                            <i v-if="item.value == selected.value" class="vd-ui_icon icon-radio3"></i>
                            <i v-else class="vd-ui_icon icon-radio1"></i>
                        </div>
                        <div class="label">{{ item.label }}</div>
                        <div class="input-">
                            <ui-input 
                                v-if="item.value == 3 && selected.value == 3" 
                                v-model="otherContactName"
                                maxlength="7"
                                placeholder="请输入联系方式名称，最多7个字"
                            ></ui-input>
                        </div>
                    </div>
                </div>
                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    data() {
        return {
            contactList: [
                { label: '微信', value: 1 },
                { label: 'QQ', value: 2 },
                { label: '其他联系方式', value: 3 }
            ],
            selected: {},
            otherContactName: '', // 其他方式Label
        }
    },
    mounted() {
    },
    methods: {
        show() {
            this.$refs.slideDialog.show();
        },

        // 选择已建档客户
        async chooseThis (item) {
            this.selected = item;
        },

        // 确定
        handlerConfirm () {
            if (!this.selected && this.selected.value) {
                this.$message({
                    type: 'warn',
                    message: '请选择要添加的联系方式'
                })
                return;
            }
            else if (this.selected.value == 3 && !this.otherContactName) {
                this.$message({
                    type: 'warn',
                    message: '请输入联系方式名称'
                })
                return;
            }

            let emitLabel = '';
            if (this.selected.value == 3) {
                emitLabel = this.otherContactName;
            } else {
                emitLabel = this.selected.label;
            }

            this.$emit('change', emitLabel);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();

            this.selected = {},
            this.otherContactName = '';
        }
    }
})
/* 线索/商机 卡片 */

Vue.component('ui-business-card', {
    template: `
        <div class="business-card">
            <!-- 线索 -->
            <div class="business-leads" v-if="type == 1">
                <div class="business-top">
                    <div class="row">
                        <div class="business-trader">
                            <div class="trader-name">{{ businessInfo.traderName }}</div>
                            <i v-if="businessInfo.tycFlag == 'Y'" @click.stop="openTyc" class="vd-ui_icon icon-tianyancha"></i>
                        </div>
                        <div class="business-status">
                            <div class="leads-status" :class="'s'+businessInfo.followStatus">{{ businessInfo.followStatusStr }}</div>
                        </div>
                    </div>
                    <div class="row mt">
                        <div class="business-no">{{ businessInfo.leadsNo }}</div>
                        <div class="business-time">{{ businessInfo.createTime }}</div>
                    </div>
                </div>
                <div class="business-bottom">
                    <div class="business-attrs">
                        <div class="item-attr">
                            <span class="label">归属销售：</span>
                            <span class="value" v-if="businessInfo.belonger || businessInfo.belongerPic">
                                <ui-user 
                                    :name="businessInfo.belonger" :avatar="businessInfo.belongerPic" 
                                    @click="openChat(businessInfo.belongerId)"
                                ></ui-user>
                            </span>
                            <span class="value" v-else>-</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">来源：</span>
                            <span class="value">{{ businessInfo.clueTypeName || '-' }}</span>
                        </div>
                    </div>
                    <div class="trader-check" v-if="traderCheck && traderName !== businessInfo.traderName">
                        <ui-tip>线索客户与当前拜访客户不一致</ui-tip>
                    </div>
                </div>
            </div>

            <!-- 商机 -->
            <div class="business-change" v-else-if="type == 2">
                <div class="business-top">
                    <div class="row">
                        <div class="business-trader">
                            <div class="trader-name">{{ businessInfo.traderName }}</div>
                            <i v-if="businessInfo.tycFlag == 'Y'" @click.stop="openTyc" class="vd-ui_icon icon-tianyancha"></i>
                        </div>
                        <div class="business-status">
                            <div class="change-status" :class="'s'+businessInfo.stage">{{ businessInfo.stageStr }}</div>
                        </div>
                    </div>
                    <div class="row mt">
                        <div class="business-no">{{ businessInfo.bussinessChanceNo }}</div>
                        <div class="business-time">{{ businessInfo.createTime }}</div>
                    </div>
                </div>
                <div class="business-bottom">
                    <div class="business-attrs">
                        <div class="item-attr">
                            <span class="label">归属销售：</span>
                            <span class="value" v-if="businessInfo.belonger || businessInfo.belongPic">
                                <ui-user 
                                    :name="businessInfo.belonger" :avatar="businessInfo.belongPic"
                                    @click="openChat(businessInfo.belongerId)"
                                ></ui-user>
                            </span>
                            <span class="value" v-else>-</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">业务类型：</span>
                            <span class="value">{{ businessInfo.businessTypeName || '-' }}</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">预计金额：</span>
                            <span class="value" :class="{'price-light': businessInfo.amount}">{{ businessInfo.amount || '-' }}</span>
                        </div>
                        <div class="item-attr">
                            <span class="label">预计成单：</span>
                            <span  class="value">{{ businessInfo.orderTime || '-' }}</span>
                        </div>
                    </div>
                    <div class="pro-name" v-if="businessInfo.productCommentsSale">{{ businessInfo.productCommentsSale }}</div>
                    <div class="trader-check" v-if="traderCheck && traderName !== businessInfo.traderName">
                        <ui-tip>商机客户与当前拜访客户不一致</ui-tip>
                    </div>
                </div>
            </div>
            
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        businessInfo: {
            type: Object
        },
        type: {
            type: String,
            default: '1' // 1:线索 2:商机
        },
        // 验证客户是否一致
        traderCheck: {
            type: Boolean,
            default: false,
        },
        traderName: String,
    },
    data() {
        return {
           
        }
    },
    created() {
    },
    methods: {
        openTyc () {
            this.$refs.tycDetail.show(this.businessInfo.traderName);
        },
        /* 打开企微聊天框 */
        openChat (userid, qwUserid) {
            if (qwUserid) {
                GLOBAL.openQwChat(this, qwUserid, true);
            } else {
                GLOBAL.openQwChat(this, userid, false);
            }
        },
    }
})
Vue.component('ui-step', {
    template: `<div class="ui-step-wrap">
        <div class="ui-step-list" :class="status">
            <div class="ui-step-item" :class="{active: item.active}" v-for="(item, index) in list" :key="index">
                <div class="ui-step-item-icon"></div>
                <div class="ui-step-item-txt">{{ item.label }}</div>
            </div>
        </div>
    </div>`,
    props: {
        list: {
            type: Array,
            default() {
                return [];
            }
        },
        active: {
            type: String | Number,
            default: ''
        },
        status: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
           
        }
    },
    created() {
        this.checkStepStatus();
    },
    methods: {
        checkStepStatus() {
            let stop = false;
            this.list.forEach(item => {
                if ((item.id != this.active && !stop) || item.id == this.active) {
                    item.active = true;

                    if (item.id == this.active) {
                        stop = true;
                    }
                }  
            });

            console.log(this.list, this.active)
        }
    }
})
// 手机联想 联系人
Vue.component('ui-phone-related', {
    template: `
        <div class="vd-ui-phone-related">
            <div class="phone-related-show-wrap" @click="openErpSearch">
                <ui-placeholder v-if="!phoneValue">{{ placeholder }}</ui-placeholder>
                <div v-else class="phone-related-show" :class="{'pb': mobileStatus == 1 || mobileStatus == 2}">
                    <div class="phone-value">{{ phoneValue }}</div>
                    <ui-tip v-if="mobileStatus == 1" type="success">该手机号已注册贝登商城</ui-tip>
                    <ui-tip v-if="mobileStatus == 2">该手机号未注册贝登商城</ui-tip>
                </div>
            </div>

            <!-- 手机联想弹层 -->
            <crm-slide-dialog ref="slideDialog" title="手机" @hidn="handlerCancel">
                <div class="phone-related-panel">
                    <div class="slide-dialog-input-wrap">
                        <ui-input
                            v-model="dialogInput"
                            v-bind="$attrs"
                            type="tel"
                            maxlength="11"
                            @blur="handlerBlur"
                            @focus="handlerFocus"
                            @input.native="handlerInput"
                            @change="handlerChange"
                            border
                            clear
                            autocomplete="off"
                        />
                    </div>

                    <div class="phone-related-ul" @click.stop>
                        <template v-if="axiosSearchKey">
                            <div class="erp-loading" v-if="loading">
                                <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                <span>加载中...</span>
                            </div>
                            <div class="related-list" v-else-if="relateList.length">
                                <template v-for="(item, index) in relateList">
                                    <div 
                                        :key="index"
                                        class="pr-item" :class="{'active': item.traderContactId == tempChoose.traderContactId}"
                                        v-if="item.name && item.mobile"
                                        @click.stop="chooseThis(item)"
                                    >
                                        <div class="name text-line-1">{{item.name}}</div>
                                        <div class="mobile" v-html="lightName(item.mobile)"></div>
                                    </div>
                                </template>
                            </div>
                            <div class="erp-load-empty" v-else>
                                <img src="/mstatic/image/empty/search-empty.svg"/>
                                <p>抱歉，没有找到符合条件的结果，</p>
                                <p>建议您换个关键词</p>
                            </div>
                        </template>
                    </div>

                    <div class="slide-dialog-default-footer">
                        <div class="btn-cancel-flex">
                            <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                        </div>
                        <div class="btn-confirm-flex">
                            <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                        </div>
                    </div>
                </div>
            </crm-slide-dialog>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        // 已建档客户id： 有id才联想，否则只做简单输入
        traderId: {
            type: [String, Number],
            default: ''
        },
        // 是否精确匹配:  true:精确[输入满11位才查询]  false:非精确
        accurateMatch: {
            type: Boolean,
            default: false,
        },
        maxlength: {
            type: Number,
            default: 11
        },
        placeholder: {
            type: String,
            default: ''
        },
        errorMsg: {
            type: String
        },
    },
    data() {
        return {
            phoneValue: '',
            mobileStatus: 0, // 手机号是否注册过贝登商城 1注册过 0未
            choosePhone: {}, // 选中项

            // 联想
            dialogInput: '',
            loading: false,
            axiosSearchKey: '', // 记录接口搜索词·高亮  有此值，代表已经触发过搜索
            timer: null,
            relateList: [], // 搜索联想
            tempChoose: {}, // 临时选中项
        }
    },
    watch: {
        value: {
            handler (newV) {
                this.phoneValue = newV;
                this.checkMobileStatus();
            },
            immediate: true
        },
    },
    computed: {
        lightName () {
            return (name) => {
                if (!this.axiosSearchKey) return name;
                const regExp = new RegExp(this.axiosSearchKey, 'g');
                name = name.replace(regExp, `<font color='#FF6600'">${this.axiosSearchKey}</font>`);
                return name;
            }
        },
        tags () {
            let arr = this.companyInfo.tags.split(';') || [];
            return arr
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category); 
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig); 
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle); 
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    mounted() {
    },
    methods: {
        handlerBlur () {
            this.$emit('blur');
        },
        // 输入停顿300毫秒后搜素
        handlerInput (event) {
            let val = event.target.value.trim();
            this.handlerSearch(val);
        },
        handlerChange () {
            let val = this.dialogInput.trim();
            this.handlerSearch(val);
        },
        handlerFocus (event) {
            let val = event.target.value.trim();
            this.handlerSearch(val);
        },
        handlerSearch (val) {
            val = val.trim();
            if (!val) {
                this.axiosSearchKey = '';
                this.relateList = [];
                this.loading = false;
                this.timer = null;
                this.tempChoose = {};
                return;
            };
            if (val == this.axiosSearchKey) return;

            this.timer && clearTimeout(this.timer);
            if (this.traderId) {
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300)
            }
        },

        openErpSearch () {
            if (this.phoneValue) {
                this.dialogInput = this.phoneValue;
                this.tempChoose = JSON.parse(JSON.stringify(this.choosePhone));
                this.getRelatelist(this.phoneValue);
            }

            this.$refs.slideDialog.show();
        },

        getRelatelist (val) {
            this.loading = true;
            this.axiosSearchKey = val;

            let query = `?traderId=${this.traderId}&mobile=${val}&pageSize=100&accurateMatch=${this.accurateMatch? 1: 0}`;
            this.$axios.post(`/crm/traderContact/m/page${query}`).then(({data}) => {
                this.loading = false;
                if (data.success) {
                    this.relateList = data.data.list || [];
                    console.log('phone related:', this.relateList);
                }
            }).catch(err=> {
                this.loading = false;
            })
        },

        // 选择已建档客户
        async chooseThis (item) {
            this.tempChoose = item;
        },

        // 确定
        handlerConfirm () {
            let mobile = this.tempChoose.mobile || this.dialogInput || '';
            if (!mobile) {
                this.$message({
                    type: 'error',
                    message: '请输入手机号',
                })
                return;
            } else if (mobile.length !== 11) {
                this.$message({
                    type: 'error',
                    message: '请输入11位手机号码',
                })
                return;
            }

            this.$emit("input", mobile);
            this.checkMobileStatus();
            this.choosePhone = JSON.parse(JSON.stringify(this.tempChoose));

            let emitData = Object.assign({}, this.choosePhone, {
                choosed: true,
                traderId: this.choosePhone.traderId || '',
                mobile: this.choosePhone.mobile || '',
                traderContactId: this.choosePhone.traderContactId || '',
                traderContactName: this.choosePhone.name || '',
            });
            this.$emit('change', emitData);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();

            this.dialogInput = '';
            this.axiosSearchKey = '';
            this.relateList = [];
            this.loading = false;
            this.timer = null;
            this.tempChoose = {};
        },


        // 手机号是否注册过贝登
        checkMobileStatus() {
            if(this.phoneValue) {
                this.$axios.post('/crm/visitrecord/profile/checkMobileExists?mobile=' + this.phoneValue).then(({ data }) => {
                    if (data.success) {
                        this.mobileStatus = data.data ? 1 : 2
                    }
                })
            } else {
                this.mobileStatus = 0
            }
        },
    }
})


Vue.component('ui-radio', {
    template: `
        <div
            class="vd-ui-radio-item"
            :class="{
                'vd-ui-radio-item-checked': currentChecked,
                'vd-ui-radio-item-disabled': disabled,
                'vd-ui-radio-item-labeltype': type === 'label'
            }"
            @click="handlerClick()"
        >
            <div class="vd-ui-radio-inner">
                <div class="vd-ui-radio-icon">
                    <div class="vd-ui-radio-icon-selected"></div>
                </div>
                <span class="vd-ui-radio-label">{{ label }}</span>
                <span class="vd-ui-radio-tip">{{ tip }}</span>
            </div>
        </div>
    `,

    props: {
        label: {
            type: String,
            default: "",
        },
        value: {
            type: String,
        },
        tip: {
            type: String,
        },
        checked: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        clearable: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: '' //label
        }
    },
    data() {
        return {
            currentChecked: false,
        };
    },
    mounted() {
        this.currentChecked = this.checked;
    },
    watch: {
        checked() {
            this.currentChecked = this.checked;
        },
    },
    methods: {
        handlerClick() {
            if (!this.disabled && (this.clearable || !this.currentChecked)) {
                this.currentChecked = !this.currentChecked;
                this.$emit("update:checked", this.currentChecked);
                this.$emit("change", this.currentChecked);
            }
        },
    },
})

Vue.component('ui-radio-group', {
    template: `
        <div class="vd-ui-radio-group" :class="{'is-label': type==='label'}">
            <template v-for="(item, index) in boxList">
                <ui-radio
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                    :tip="item.tip"
                    :checked.sync="item.checked"
                    :disabled="disabled || item.disabled"
                    :clearable="clearable"
                    @change="handlerChange(index, $event)"
                    :type="type"
                ></ui-radio>
            </template>
            <div class="vd-ui-input-error" v-if="errorable">
                <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i>
                <p class="vd-ui-input-error--errmsg">{{errorMsg}}</p>
            </div>
        </div>
    `,

    watch: {
        list: {
            handler () {
                this.setList();
            },
            deep: true,
            immediate: true
        },
        value (newV) {
            this.setList();
        },
    },

    data() {
        return {
            boxList: [],
        };
    },
    props: {
        list: {
            type: Array,
            default() {
                return [];
            },
        },
        value: "",
        clearable: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        errorMsg: {
            type: String,
            default: ''
        },
        errorable: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: '' //label
        }
    },
    mounted() {
        // this.$form.setValidEl(this);
        this.setList();
    },
    methods: {
        setList() {
            this.boxList = JSON.parse(JSON.stringify(this.list));

            this.boxList.forEach((item) => {
                if (item.value == this.value) {
                    item.checked = true;
                }
            });
        },
        handlerChange(index, data) {
            let valueItem = null;
            this.boxList.forEach((item, i) => {
                if(index === i) {
                    this.$set(this.boxList[i], "checked", data);
                } else {
                    this.$set(this.boxList[i], "checked", false);
                }
                if (item.checked) {
                    valueItem = item;
                }
            });

            let value = valueItem ? valueItem.value : '';

            if (value !== this.value) {
                this.checkValid(value);
                this.$emit("input", value);
                this.$emit("change", valueItem);
            }
        },
        checkValid(newValue) {
            if(this.validKey && this.validValue) {
                if(this.$form.validList[this.validKey].indexOf(this.validValue) !== -1) {
                    let validData = this.$form.checkValid(this.validValue, newValue, this.validKey);

                    this.triggerError(validData);
                }
            }
        },
        triggerError(validData) {
            if(validData.result) {
                this.errorMsg = '';
                this.errorable = false;
            } else {
                this.errorMsg = validData.message;
                this.errorable = true;
            }
        }
    }
})

Vue.component('ui-select', {
    template: `<div class="vd-ui-select">
        <div class="vd-ui-select-trigger" :style="'--line:' + line" v-if="!multi" @click="showOptionDialog">
            <div class="vd-ui-select-trigger-txt" :class="{'placeholder': !selectedItem.label}">{{ selectedItem.label || placeholder }}</div>
            <i class="vd-ui_icon icon-app-right"></i>
        </div>
        <template v-else> 
            <div class="vd-ui-select-trigger multi-select" :style="'--line:' + line" v-if="selectedItemList.length">
                <div class="vd-ui-select-multi-tags">
                    <div class="vd-ui-select-multi-tags-inner">
                        <template v-for="(item, index) in selectedItemList">
                            <div class="vd-ui-select-tag" :class="{'tag-avatar': avatar}" v-if="!showLength || index < showLength" ref="tagItem" :key="index">
                                <template>
                                    <div class="vd-ui-select-tag-avatar" v-if="avatar">
                                        <img :src="item.avatar || '/mstatic/mui/image/crm-user-avatar.svg'" />
                                    </div>
                                    <div class="vd-ui-select-tag-txt">{{ item.label }}</div>
                                    <i class="vd-ui_icon icon-delete" @click="deleteMultiItem(item, index)"></i>
                                </template>
                            </div>
                        </template>
                        <div class="vd-ui-select-tag-more" v-if="showLength">+{{selectedItemList.length - showLength}}</div>
                    </div>
                </div>
                <div class="vd-ui-icon-trigger">
                    <i class="vd-ui_icon icon-app-right" @click="showOptionDialog"></i>
                </div>
            </div>
            <div class="vd-ui-select-trigger" :style="'--line:' + line" v-else @click="showOptionDialog">
                <div class="vd-ui-select-trigger-txt placeholder">{{ placeholder }}</div>
                <i class="vd-ui_icon icon-app-right"></i>
            </div>
        </template>

        <crm-slide-dialog ref="selectOptions" :title="title" :topOptionTxt="multi ? '清空' : ''" :maskHide="false" @topOption="clear">
            <div class="vd-ui-select-options">
                <div class="vd-ui-select-options-search" v-if="filter">
                    <ui-input placeholder="请输入" v-model="searchValue" @input="filterSearch" :border="true" :clear="true"></ui-input>
                </div>
                <template v-if="!isloading">
                    <div class="vd-ui-select-options-list" :class="{filter: filter}" v-if="showListData.length">
                        <div class="vd-ui-select-options-item" :class="{selected: item.checked}" v-for="(item, index) in showListData" @click="handlerOptionChange(item)">
                            <template v-if="!multi">
                                <i class="vd-ui_icon icon-radio1" v-if="!item.checked"></i>
                                <i class="vd-ui_icon icon-radio3" v-else></i>
                            </template>
                            <template v-else>
                                <i class="vd-ui_icon icon-checkbox1" v-if="!item.checked"></i>
                                <i class="vd-ui_icon icon-checkbox2" v-else></i>
                            </template>
                            <div class="vd-ui-select-options-avatar" v-if="avatar">
                                <img :src="item.avatar || '/mstatic/mui/image/crm-user-avatar.svg'"/>
                            </div>
                            <div class="vd-ui-select-options-txt" v-html="item.showLabel || item.label"></div>
                        </div>
                    </div>
                    <div class="vd-ui-select-options-empty" v-else>
                        <div class="empty-img">
                            <img src="/mstatic/image/empty/search-empty.svg" v-if="searchValue"/>
                            <img src="/mstatic/image/empty/list-empty.svg" v-else/>
                        </div>
                        <div class="empty-txt">
                            {{ searchValue ? '抱歉，没有找到您想搜索的结果' : '暂无数据'  }}
                        </div>
                    </div>
                </template>
                <div v-else class="vd-ui-select-loading">
                    <div class="vd-ui-select-loading-icon">
                        <i class="vd-ui_icon icon-loading"></i>
                    </div>
                    <div class="vd-ui-select-loading-txt">加载中...</div>
                </div>
                <div class="vd-ui-select-options-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="hideOptions">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerSelect">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    </div>`,
    props: {
        value: {
            type: String | Number | Array
        },
        type: {
            type: String,
            default: '' // multi:多选
        },
        title: {
            type: String,
            default: ''
        },
        line: {
            type: Number,
            default: 1 // 回显默认行数
        },
        search: {
            type: Boolean,
            default: false
        },
        remote: {
            type: Boolean,
            default: false
        },
        remoteInfo: {

        },
        filter: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        },
        multi: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: ''
        },
        defaultItems: {
            type: Array | Object,
        },
        avatar: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            listData: [],
            showListData: [],
            listTempData: [],
            selectedItem: {},
            selectedItemList: [],
            tempValue: '',
            showLength: 0,
            searchValue: '',
            isloading: false,
            timeout: null
        };
    },
    computed: {

    },
    watch: {
        list() {
            this.listData = JSON.parse(JSON.stringify(this.list));
            if (this.value && this.value.length) {
                this.initValue();
                this.$nextTick(() => {
                    this.calcShowTags();
                })
            }
        },
        value(newVal, oldVal) {
            this.selectedItemList = [];
            this.selectedItem = {};

            if (this.value && this.value.length) {
                this.initValue();
                this.$nextTick(() => {
                    this.calcShowTags();
                })
            } 
        }
    },
    mounted() {
        this.listData = JSON.parse(JSON.stringify(this.list));
        if (this.value && this.value.length) {
            this.initValue();
            this.$nextTick(() => {
                this.calcShowTags();
            })
        }
    },
    methods: {
        initValue() {
            if (this.listData.length) {
                this.listData.forEach(item => {
                    if (this.multi) {
                        this.value.forEach(val => {
                            if(val == item.value) {
                                this.selectedItemList.push(item);
                            }
                        })
                    } else if (this.value == item.value) {
                        this.selectedItem = item;
                    }
                });
            } else if (this.multi && this.defaultItems.length) {
                this.selectedItemList = this.defaultItems;
            } else if (!this.multi && this.defaultItems && Object.keys(this.defaultItems)) {
                this.selectedItem = this.defaultItems;
            }
        },
        getRemoteData(data, callback) {
            this.timeout && clearTimeout(this.timeout);
            this.timeout = setTimeout(() => {
                this.isloading = true;
                let reqMethod = this.remoteInfo.paramsMethod || 'get';

                let reqData = {
                    url: this.remoteInfo.url,
                    type: reqMethod,
                }

                if (reqMethod == 'get') {
                    reqData.params = data
                } else if (reqMethod == 'post') {
                    reqData.data = data;
                }

                this.$axios(reqData).then(({ data }) => {
                    this.isloading = false;

                    if (data.success) {
                        callback && callback(data.data);
                    }
                })
            }, 300)
        },
        showOptionDialog() {
            if(this.multi) {
                this.tempValue = JSON.parse(JSON.stringify(this.value || []));
            } else {
                this.tempValue = this.value;
            }
            if (this.remote && !this.listData.length) {
                this.getRemoteData(null, (data) => {
                    let list = [];

                    data.forEach(item => {
                        list.push({
                            label: item[this.remoteInfo.label],
                            value: item[this.remoteInfo.value],
                            checked: false
                        })
                    })
                    this.listData = list;
                    this.listTempData = JSON.parse(JSON.stringify(this.listData));
                    this.showListData = this.listTempData;

                    this.checkTempSelected();
                })
            } else {
                this.listTempData = JSON.parse(JSON.stringify(this.listData));
                this.showListData = this.listTempData;
                this.checkTempSelected();
            }
            this.searchValue = "";
            console.log(this.showListData);
            this.$refs.selectOptions.show();
        },
        hideOptions() {
            this.$refs.selectOptions.hide();
        },
        clear() {
            if(this.multi) {
                this.tempValue = [];
            } else {
                this.tempValue = '';
            }

            this.checkTempSelected();
        },
        handlerOptionChange(item) {
            console.log(item)
            if (this.multi) {
                // this.showListData.forEach((listItem, index) => {
                //     if (item.value == listItem.value) {
                //         listItem.checked = !item.checked;
                //     }
                // })
                // this.$set(this.showListData, this.showListData);

                // console.log(this.showListData)

                // this.listTempData.forEach((listItem, index) => {
                //     if (item.value == listItem.value) {
                //         listItem.checked = item.checked;
                //     }
                // })

                if (this.tempValue.indexOf(item.value) !== -1) {
                    this.tempValue.forEach((val, index) => {
                        if (val == item.value) {
                            this.tempValue.splice(index, 1);
                        }
                    })
                } else {
                    this.tempValue.push(item.value);
                }
            } else {
                // this.listTempData.forEach((listItem, index) => {
                //     listItem.checked = item.value == listItem.value
                // })

                // this.showListData.forEach((listItem, index) => {
                //     listItem.checked = item.value == listItem.value
                //     this.$set(this.showListData, index, listItem);
                // })
                this.tempValue = item.value;
            }

            // this.$forceUpdate();

            this.checkTempSelected();
        },
        checkTempSelected() {
            this.showListData.forEach(item => {
                if (this.multi) {
                    if (this.tempValue.indexOf(item.value) !== -1) {
                        item.checked = true;
                    } else {
                        item.checked = false;
                    }
                } else {
                    item.checked = this.tempValue == item.value;
                }
            })

            this.$forceUpdate();
        },
        handlerSelect() {
            if (this.multi) {
                this.selectedItemList = [];
                this.listTempData.forEach(listItem => {
                    if (this.tempValue.indexOf(listItem.value) !== -1) {
                        this.selectedItemList.push(listItem);
                    }
                })

                this.calcShowTags();
            } else {
                this.listTempData.forEach(listItem => {
                    if (this.tempValue == listItem.value) {
                        this.selectedItem = listItem;
                    }
                })
            }

            this.triggerChange(this.tempValue);

            this.listData = JSON.parse(JSON.stringify(this.listTempData))
            this.hideOptions();
        },
        calcShowTags() {
            this.showLength = 0;

            if (this.selectedItemList.length) {
                this.$nextTick(() => {
                    setTimeout(() => {
                        let hasHidden = false;
                        let showNum = 0;
                        this.$refs.tagItem.forEach(item => {
                            console.log(item.offsetTop)
                            if (item.offsetTop - 10 < 35 * (this.line - 1)) {
                                showNum++;
                            } else {
                                hasHidden = true;
                            }
                        })

                        if (hasHidden) {
                            this.showLength = showNum - 1;
                            console.log(this.showLength)
                        }
                    })
                })
            }

        },
        deleteMultiItem(item, index) {
            this.selectedItemList.splice(index, 1);
            this.value.splice(index, 1);

            this.listData.forEach(listItem => {
                if (listItem.value == item.value) {
                    listItem.checked = false;
                }
            })

            this.$nextTick(() => {
                this.calcShowTags();
            })
        },
        triggerChange(value) {
            this.$emit('input', value);
            if (this.multi) {
                this.$emit('change', this.selectedItemList);
            } else {
                this.$emit('change', this.selectedItem);
            }
        },
        filterSearch() {
            let searchValue = this.searchValue.trim();

            if (this.remoteInfo && this.remoteInfo.search) {
                let reqData = {};
                reqData[this.remoteInfo.searchKey] = searchValue;
                this.getRemoteData(reqData, (data) => {
                    let list = [];
                    let reg = new RegExp('(' + searchValue + ')', 'ig');

                    data.forEach(item => {
                        list.push({
                            label: item[this.remoteInfo.label],
                            value: item[this.remoteInfo.value],
                            showLabel: item[this.remoteInfo.label].replace(reg, '<span class="strong">$1</span>'),
                            checked: false
                        })
                    })

                    this.showListData = list;

                    this.checkTempSelected();
                })
            } else {
                if (searchValue) {
                    let list = [];
                    let reg = new RegExp('(' + searchValue + ')', 'ig');

                    this.listTempData.forEach(item => {
                        if (item.label.toUpperCase().indexOf(searchValue.toUpperCase()) !== -1) {
                            let itemData = JSON.parse(JSON.stringify(item))
                            itemData.showLabel = itemData.label.replace(reg, '<span class="strong">$1</span>');
                            list.push(itemData);
                        }
                    })

                    this.showListData = list;
                    this.checkTempSelected()
                } else {
                    this.showListData = this.listTempData;
                    this.checkTempSelected()
                }
            }

        }
    }
})
Vue.component('ui-wxuser-select', {
    template: `<div class="vd-ui-wxuser-select">
        <div class="vd-ui-select-trigger" v-if="false" @click="showSelect">
            <div class="vd-ui-select-trigger-txt">{{ selectedItem.name || placeholder }}</div>
            <i class="vd-ui_icon icon-app-right"></i>
        </div>
        <template v-else> 
            <div class="vd-ui-select-trigger multi-select" :style="'--line:' + line" @click="showSelect">
                <div class="vd-ui-select-multi-tags" v-if="selectedItemList.length">
                    <div class="vd-ui-select-multi-tags-inner">
                        <template v-for="(item, index) in selectedItemList">
                            <div class="vd-ui-select-tag tag-avatar" ref="tagItem" :key="index">
                                <template>
                                    <div class="vd-ui-select-tag-avatar">
                                        <img :src="item.avatar || '/mstatic/mui/image/crm-user-avatar.svg'" />
                                    </div>
                                    <div class="vd-ui-select-tag-txt">{{ item.name }}</div>
                                    <i class="vd-ui_icon icon-delete" @click.stop="deleteItem(index)"></i>
                                </template>
                            </div>
                        </template>
                    </div>
                </div>
                <div class="vd-ui-select-trigger-txt placeholder" v-else>{{ placeholder }}</div>
                <i class="vd-ui_icon icon-app-right"></i>
            </div>
        </template>
    </div>`,
    props: {
        value: {
            type: Array,
            default() {
                return [];
            }
        },
        type: {
            type: String,
            default: 'single' // multi:多选
        },
        line: {
            type: Number,
            default: 100 // 回显默认行数
        },
        multi: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择'
        },
        defaultItems: {
            type: Array | Object,
        }
    },
    data() {
        return {
            selectedItemList: [],
        };
    },
    watch: {
        defaultItems: {
            handler (newV) {
                if (Object.prototype.toString.call(newV) == '[object Array]') {
                    this.selectedItemList = newV;
                } else if (Object.prototype.toString.call(newV) == '[object Object]' && Object.keys(newV).length) {
                    this.selectedItemList = [newV];
                }
            },
            immediate: true,
            deep: true,
        }
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        async showSelect() {
            let res = await ww.selectEnterpriseContact({
                fromDepartmentId: 0,
                mode: this.type,
                type: ['user'],
                selectedUserIds: this.value
            })

            console.log('  =============== value ===> ', this.value)
            console.log(' select res ===> ', res)

            if (res.result && res.result.userList) {
                this.selectedItemList = res.result.userList || [];
                let ids = [];
                this.selectedItemList.forEach(item => {
                    ids.push(item.id)
                })

                this.value = ids;
            }

            this.triggerChange();
        },
        deleteItem(index) {
            this.value.splice(index, 1);
            this.selectedItemList.splice(index, 1);
            this.triggerChange();
        },
        triggerChange() {
            this.$emit('input', this.value);
            this.$emit('change', {
                value: this.value,
                list: this.selectedItemList
            });
        }
    }
})
Vue.component('crm-slide-dialog', {
    template: `<div class="crm-slide-dialog-wrap" ref="wrap" :class="{show: isShow}" :style="'z-index:' + (isShow ? zindex : '-1')">
        <div class="crm-slide-dialog-mask" @click="handlerMaskClick"></div>
        <div class="crm-slide-dialog-cnt" :class="{'drop-down': type==='down'}">
            <div class="crm-slide-dialog-header" v-if="title">
                <div class="slide-dialog-header-option" @click="handlerTopOption">{{ topOptionTxt }}</div>
                <div class="slide-dialog-header-title">{{ title }}</div>
                <div class="slide-dialog-header-close" v-if="closeable" @click="hide">
                    <i class="vd-ui_icon icon-delete"></i>
                </div>
            </div>
            <div ref="dialogContent">
                <template v-if="refresh">
                    <div v-if="isShow">
                        <slot></slot>
                    </div>
                </template>
                <template v-else>
                    <slot></slot>
                </template>
            </div>
        </div>
    </div>`,
    props: {
        type: {
            type: String,
            default: 'up', //'up' or 'down' 向上或者向下弹出，默认向上
        },
        zindex: {
            type: String,
            default: '999'
        },
        //点击遮罩隐藏弹层
        maskHide: {
            type: Boolean,
            default: true
        },
        isShow: {
            type: Boolean,
            default: false
        },
        refresh: {
            type: Boolean,
            default: false
        },
        title: {
            type: String,
            default: '',
        },
        closeable: {
            type: Boolean,
            default: true,
        },
        topOptionTxt: {
            type: String,
            default: '',
        }
    },
    watch: {
        isShow() {
            if(this.isShow) {
                document.body.style.overflow = 'hidden'
            } else {
                document.body.style.overflow = ''
            }
        }
    },
    data() {
        return {};
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        show() {
            document.body.append(this.$refs.wrap);
            setTimeout(() => {
                this.isShow = true;
            }, 100)
        },
        hide() {
            this.isShow = false;
        },
        handlerMaskClick() {
            if(this.maskHide) {
                this.isShow = false;
                this.$emit('update:isShow', false);
                this.$emit('hidn');
            }
        },
        handlerTopOption() {
            this.$emit('topOption')
        }
    }
})
Vue.component('ui-textarea', {
    template: `
        <div class="vd-ui-textarea-wrap">
            <textarea
                class="vd-ui-textarea" 
                ref="textarea"
                v-bind="$attrs"
                v-model="value" 
                :readonly="readonly"
                :disabled="disabled"
                :placeholder="placeholder" 
                :class="{'border': border}" 
                :style="{'height': height}"
                :maxlength="maxlength"
                @input="handlerInput"
                @change="handlerChange"
                @focus="handlerFocus"
                @blur="handlerBlur"
                @keydown="handlerKeydown"
                @keyup="handlerKeyup"
            />

            <span
                v-if="showWordLimit && maxlength"
                class="vd-ui-textarea-count"
                :class="{
                    'upper-limit': value.length == maxlength
                }"
            >
                {{ value.length }}/{{ maxlength }}
            </span>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        showWordLimit: {
            type: Boolean,
            default: false,
        },
        clear: {
            type: Boolean,
            default: false
        },

        placeholder: String,
        maxlength: String,
        border: {
            type: Boolean,
            default: false
        },
        height: {
            type: String,
            default: '74px'
        },
        maxheight: {
            type: String
        },
        // 高度自适应
        heightAuto: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            onCompressing: false
        };
    },
    watch: {
        value (newV) {
            this.computedHeight();
        }
    },
    mounted() {
    },
    methods: {
        handlerInput(e) {
            this.$emit('input', e.target.value);
        },
        handlerChange(e) {
            this.$emit('change', e);
        },

        handlerFocus(e) {
            this.$emit('focus', e);
        },
        handlerBlur(e) {
            setTimeout(() => {
                this.$emit('blur', e);
            }, 100)
        },
        handlerKeydown(e) {
            this.$emit('keydown', e);
        },
        handlerKeyup(e) {
            this.$emit('keyup', e);
        },
        handlerClear() {
            this.value = "";
            this.handlerInput();
        },

        // 高度自适应
        computedHeight() {
            if (this.heightAuto) {
                let Element = this.$refs.textarea;
                if (this.value) {
                    let scrollHeight = this.$refs.textarea.scrollHeight;
                    if (scrollHeight > this.maxheight) {
                        console.log('大于 maxheight', scrollHeight, this.maxheight);
                        Element.style.height = this.maxheight
                    } else {
                        console.log('小于 maxheight', scrollHeight, this.maxheight);
                        Element.style.height = scrollHeight + 'px' || this.height || '';
                    }
                } else {
                    Element.style.height = this.height || '';
                }
            }
        }
    }
})
Vue.component('vd-ui-toggle', {
    template: `<div class="vd-ui-toggle-wrap" @click="toggleCheck" :class="{active: checked, disabled: disabled}">
        <div class="vd-ui-toggle-inner"><div>
    </div>`,
    props: {
        checked: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        toggleCheck() {
            if(!this.disabled) {
                this.checked = !this.checked;
                this.$emit('update:checked', this.checked);
                this.$emit('change', this.checked);
            }
        }
    }
})
/* 客户名称 */

Vue.component('ui-erp-search', {
    template: `
        <!-- 客户名称弹层 -->
        <crm-slide-dialog ref="slideDialog" title="客户名称" @hidn="handlerCancel">
            <div class="trader-erp-panel">
                <div class="slide-dialog-input-wrap">
                    <ui-input
                        v-model="dialogInput" 
                        border
                        clear
                        :maxlength="maxlength"
                        @focus="handlerFocus"
                        @input.native="handleInput"
                        @change="handlerChange"
                        @compositionend.native="commentPress"
                    />
                </div>
                <div class="erp-search-list" @click.stop>
                    <template v-if="loading">
                        <div class="erp-loading">
                            <i class="vd-ui_icon icon-loading" ref="loading"></i>
                            <span>加载中...</span>
                        </div>
                    </template>
                    <template v-else-if="loadingFail">
                        <div class="erp-load-fail">
                            <i class="vd-ui_icon icon-error2"></i>
                            <span>加载失败</span>
                        </div>
                    </template>
                    <div class="search-related" v-else-if="axiosSearchKey">
                        <div class="related-list" v-if="relateList.length">
                            <div class="local-data">本地数据匹配</div>
                            <!-- 'disabled': needDisable && !item.belong && !item.share, -->
                            <div 
                                class="related-item" 
                                :class="{
                                    'active': item.traderId == chooseTrader.traderId
                                }"
                                v-for="(item, index) in relateList" :key="index"
                                @click.stop="changeErp(item)"
                            >
                                <div class="related-item-left">
                                    <span class="trader-select">
                                        <i v-if="item.traderId == chooseTrader.traderId" class="vd-ui_icon icon-radio3"></i>
                                        <i v-else class="vd-ui_icon icon-radio1"></i>
                                    </span>
                                    <!-- <p v-if="needDisable && (!item.belong && !item.share)">{{ item.traderName }}</p>
                                    <p v-else v-html="lightName(item)"></p> -->
                                    <p class="name text-line-1" v-html="lightName(item)"></p>
                                    <i class="vd-ui_icon icon-tianyancha icon" v-if="item.tycFlag == 'Y'"></i>
                                </div>
                                <div class="related-item-right text-line-1">{{ item.saleName }}</div>
                            </div>
                        </div>
                        <div class="erp-load-empty" v-else>
                            <img src="/mstatic/image/empty/search-empty.svg"/>
                            <p>抱歉，没有找到符合条件的结果，</p>
                            <p>建议您换个关键词</p>
                        </div>
                    </div>
                </div>
                <!-- slide-dialog-default-footer -->
                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        traderId: {
            type: [String, Number],
        },
        tycFlag: String,

        // 是否启用天眼查
        needTyc: {
            type: Boolean,
            default: true
        },
        // 选项中 启用禁用
        needDisable: {
            type: Boolean,
            default: false
        },

        placeholder: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        maxlength: {
            type: [Number, String],
            default: 1000,
        },
        saleName: String,
    },
    data() {
        return {
            dialogInput: '',
            // 建档客户-基本信息
            chooseTrader: {
                traderId: '',
                tycFlag: 'N',
                saleName: '',
            },
            traderDetail: {}, // 建档客户-交易信息

            // search
            loading: false,
            loadingFail: false,
            axiosSearchKey: '', // 记录接口搜索词·高亮  有此值，代表已经触发过搜索
            relateList: [], // 搜索联想
            timer: null,
        };
    },
    computed: {
        // 高亮
        lightName () {
            return (item) => {
                let name = item.traderName;
                if (!this.axiosSearchKey) return name;
                const regExp = new RegExp(this.axiosSearchKey, 'g');
                name = name.replace(regExp, `<font color='#FF6600'>${this.axiosSearchKey}</font>`);

                if (item.share) {
                    name = '[分享] ' + name;
                }

                return name;
            }
        },
    },
    mounted() {
        // 有默认值 触发搜索
            // 搜索时 不清空原选中信息, 修改input时清空原选中信息
        // 无默认值 面板不展示内容， 触发搜索后展示内容
    },
    methods: {
        hide() {
            this.$refs.slideDialog.hide();
            this.dialogInput = '';
            this.axiosSearchKey = '';
            this.relateList = [];
            this.loading = false;
            this.loadingFail = false;
        },
        show (name, chooseTrader, traderDetail) {
            this.dialogInput = name;
            this.chooseTrader = chooseTrader;
            this.traderDetail = traderDetail;

            if (name) {
                this.getRelatelist(name);
            }
            this.$refs.slideDialog.show();
        },

        handlerChange () {
            console.log('change', this.dialogInput);
            let val = this.dialogInput.trim();
            this.handlerSearch(val);
        },
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                let val = e.target.value.trim();
                this.handlerSearch(val);
            }
        },
        // 中文结束
        commentPress(e) {
            let val = e.target.value.trim();
            this.handlerSearch(val);
        },
        handlerFocus () {
            this.handlerSearch(this.dialogInput);
        },
        handlerSearch (val) {
            val = val.trim();
            if (!val) {
                this.axiosSearchKey = '';
                this.relateList = [];
                this.loading = false;
                this.loadingFail = false;
                this.timer = null;
                this.clearCompany();
                return;
            };
            if (val == this.axiosSearchKey) return;

            this.timer && clearTimeout(this.timer);
            this.timer = setTimeout(()=> {
                this.clearCompany();
                this.getRelatelist(val);
            }, 300);
        },
        // 搜索联想
        getRelatelist (name) {
            this.loading = true;
            this.loadingFail = false;
            this.axiosSearchKey = name;

            this.$axios.post(`/crm/trader/m/queryTrader?name=${name}`).then(({data}) => {
                this.loading = false;

                if (data.success) {
                    this.relateList = data.data || [];
                } else {
                    this.loadingFail = true;
                }
            }).catch(err=> {
                this.loading = false;
                this.loadingFail = true;
            })
        },
        
        // 清空客户信息
        clearCompany () {
            this.chooseTrader = {
                traderId: '',
                tycFlag: 'N',
                saleName: '',
            }
            this.traderDetail = {};
        },
        // 选择已建档客户
        changeErp (item) {
            this.clearCompany(); // 清空原本选项
            this.chooseTrader = item;
        },

        // 确定
        async handlerConfirm () {
            let name = this.chooseTrader.traderName || this.dialogInput || '';
            if (!name) {
                this.$message({
                    type: 'error',
                    message: '请输入客户名称',
                })
                return;
            }

            // 交易信息
            if (this.chooseTrader.traderId) {
                await this.$axios.post(`/crm/trader/profile/queryTraderDetail?traderId=${this.chooseTrader.traderId}`)
                    .then(({data}) => {
                        if (data.success) {
                            this.traderDetail = data.data || {};
                        }
                    }).catch(err=> {});
            }

            // 确定时 如果修改了值，取输入框
            this.$emit('change', {
                traderName: name,
                chooseTrader: this.chooseTrader,
                traderDetail: this.traderDetail
            });

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.slideDialog.hide();
            this.hasChoose = false;
            this.dialogInput = '';
            this.axiosSearchKey = '';
            this.relateList = [];
            this.loading = false;
            this.loadingFail = false;
        },
    }
})
/* 客户名称 */

Vue.component('ui-trader', {
    template: `
        <div class="ui-trader-name">
            <!-- 客户名称回显 -->
            <div class="trader-show-wrap">
                <ui-placeholder v-if="!inputValue" @click="openDialog">{{ placeholder }}</ui-placeholder>
                <div v-else class="trader-show">
                    <div class="input-value" @click="openDialog">{{ inputValue }}</div>
                    <template v-if="chooseTrader.traderId">
                        <template v-if="needTraderStatus">
                            <template v-if="!chooseTrader.belong">
                                <ui-tip v-if="!chooseTrader.share" margin-bottom="10px">该客户的归属销售是{{chooseTrader.saleName}}，建议您拜访前先与归属销售沟通</ui-tip>
                                <ui-tip type="success" v-else margin-bottom="10px">已选择建档客户</ui-tip>
                            </template>
                            <ui-tip type="success" v-else margin-bottom="10px">已选择建档客户</ui-tip>
                        </template>
                        <div class="other-info" v-if="needTraderDetail">
                            <div class="other-item">交易总额：{{ traderDetail.historyTransactionAmount || '-' }}</div>
                            <div class="other-item">交易次数：{{ traderDetail.historyTransactionNum || '-' }}</div>
                            <div class="other-item">最近下单：{{ traderDetail.lastOrderTime || '-' }}</div>
                        </div>
                    </template>
                    <ui-tip v-else-if="needTraderStatus" margin-bottom="10px">当前录入客户为非建档客户</ui-tip>
                </div>

                <div class="trader-icon" v-if="needTyc && inputValue">
                    <template v-if="chooseTrader.tycFlag != 'Y'">
                        <i class="vd-ui_icon icon-search" @click.stop="handlerSearch"></i>
                    </template>
                    <i class="vd-ui_icon icon-tianyancha" v-else @click.stop="showDetail"></i>
                </div>
            </div>

            <!-- erp联想 -->
            <ui-erp-search 
                ref="traderList" 
                v-$attrs 
                @change="handlerTrader"
            ></ui-erp-search>

            <!-- 天眼查列表 -->
            <ui-tyc-list ref="tycList" @change="handlerChangeTyc"></ui-tyc-list>

            <!-- 天眼查详情 -->
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        value: {
            type: String,
            default: ''
        },
        traderInfo: {
            type: Object,
            default: () => ({})
        },
        // 客户交易信息-回显
        traderDetailInfo: {
            type: Object,
            default: () => ({})
        },

        // traderId: {
        //     type: [String, Number],
        // },
        // tycFlag: String,
        // saleName: String,
        // belong: '', // 客户·归属销售
        // share: '', // 客户·是否分享给当前登录人

        // 是否启用天眼查
        needTyc: {
            type: Boolean,
            default: true
        },
        // 是否展示客户建档状态
        needTraderStatus: {
            type: Boolean,
            default: true
        },
        // 是否展示客户交易信息
        needTraderDetail: {
            type: Boolean,
            default: true
        },
        // 选项中 启用禁用
        needDisable: {
            type: Boolean,
            default: false
        },

        placeholder: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        maxlength: {
            type: [Number, String],
            default: 1000,
        },
    },
    data() {
        return {
            inputValue: this.value || '',
            // 建档客户-基本信息
            chooseTrader: {
                traderId: '',
                tycFlag: 'N', // Y:天眼查客户  N:不是天眼查客户 
                saleName: '',
                belong: '',
                share: '',
            },
            // 建档客户-交易信息
            traderDetail: {},
            // 天眼查公司信息
            tycInfo: null,
        };
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        traderInfo: {
            handler (newV) {
                this.chooseTrader = newV;
                if (this.chooseTrader.traderId && this.needTraderDetail) {
                    this.getTraderDetail();
                }
            },
            deep: true,
            immediate: true
        },
        traderDetailInfo: {
            handler (newV) {                
                this.traderDetail = newV;
            },
            deep: true,
            immediate: true
        }
    },
    mounted () {
    },
    methods: {
        async getTraderDetail() {
            if (!Object.keys(this.traderDetail)) {
                await this.$axios.post(`/crm/trader/m/queryTraderDetail?traderId=${this.chooseTrader.traderId}`)
                .then(({data}) => {
                    if (data.success) {
                        this.traderDetail = data.data || {};
                    }
                }).catch(err=> {});
            }
        },

        // ERP建档客户查询
        openDialog () {
            this.$refs.traderList.show(this.value, this.chooseTrader, this.traderDetail);
        },

        clearTyc () {
            this.tycInfo = {};
        },
        clearTrader () {
            this.chooseTrader = {
                traderId: '',
                tycFlag: 'N', // Y:天眼查客户  N:不是天眼查客户 
                saleName: '',
            };
            this.traderDetail = {};
        },

        // 确定
        handlerTrader (data) {
            this.clearTyc();
            this.chooseTrader = data.chooseTrader || {};
            this.traderDetail = data.traderDetail || {};
            console.log('chooseTrader:', JSON.parse(JSON.stringify(data.chooseTrader)));
            console.log('traderDetail:', JSON.parse(JSON.stringify(data.traderDetail)));

            let emitData = Object.assign({}, this.chooseTrader, this.traderDetail);
            this.$emit("input", data.traderName); // 修改外层v-model值
            this.$emit('change', emitData);
        },

        // 天眼查列表
        handlerSearch () {
            if (!this.inputValue) {
                this.$message({
                    type: 'warn',
                    message: '请输入公司名称后进行查询'
                })
                return false;
            }
            this.$refs.tycList.show(this.inputValue);
        },
        // 选择·天眼查客户
        handlerChangeTyc (data) {
            this.clearTrader();
            this.inputValue = data.name;
            this.chooseTrader.tycFlag = 'Y';

            let emitData = Object.assign({}, this.chooseTrader, data);
            this.$emit("input", data.name); // 修改外层v-model值
            this.$emit('change', emitData);
        },
        // 天眼查详情
        async showDetail () {
            this.$refs.tycDetail.show(this.inputValue, this.tycInfo);
        },
    }
})
/* 客户等级 */
Vue.component('ui-trader-level', {
    template: `
        <div class="ui-trader-level-icon" v-if="level"
            :class="['level-' + level]"
            :style="{
                'width': size,
                'height': size,
                'margin': margin
            }"
        ></div>
    `,
    
    props: {
        level: {
            type: String,
        },
        size: {
            type: String,
            default: '20px'
        },
        margin: {
            type: String,
            default: '0'
        }
    },
    watch: {
    },
    data() {
        return {};
    },
    mounted() {
        
    },
    methods: {
    }
})
// 详情回显
Vue.component('ui-trader-name', {
    template: `
        <div class="vd-ui-trader-name-wrap">
            <template v-if="info.traderName">
                <div class="trader-name-wrap">
                    <span class="vd-ui-trader-txt" :class="{ 'text-line-1': info.nameFlex }">{{info.traderName}}</span>
                    <i v-if="info.tycFlag == 'Y'" @click.stop="openTyc" class="icon-tyc vd-ui_icon icon-tianyancha"></i>
                    <i v-if="info.baidu" @click.stop="openBaidu" class="icon-baidu vd-ui_icon icon-baidu2" title="百度搜索客户信息"></i>
                </div>
                <!-- <template v-if="info.traderStatus">
                    <ui-tip v-if="info.traderId">已选择建档客户</ui-tip>
                    <ui-tip>当前录入客户为非建档客户</ui-tip>
                </template> -->
            </template>
            <template v-else>-</template>
            <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        </div>
    `,
    props: {
        info: {
            type: Object,
            default() {
                return {};
            }
        },
        // traderStatus 是否展示客户建档状态
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {

    },
    methods: {
        openTyc() {
            this.$refs.tycDetail.show(this.info.traderName);
        },
        openBaidu() {
            window.open('https://www.baidu.com/s?wd=' + this.info.traderName)
        }
    }
});

/* 天眼查详情 */
Vue.component('ui-tyc-detail', {
    template: `
        <crm-slide-dialog ref="tycDetailDialog" title="天眼查公司信息">
            <div class="tyc-detail-panel">
                <template v-if="tycInfo && Object.keys(tycInfo).length">
                    <div class="tyc-detail-top">
                        <div class="tyc-name">{{ tycInfo.name }}</div>
                        <div class="tyc-tags">
                            <span class="tag" v-for="(tag, tind) in tycInfo.tags" :key="tind">{{ tag }}</span>
                        </div>
                    </div>
                    <div class="tyc-detail-bottom">
                        <div class="tyc-attr">
                            <ui-form-item label="地区">
                                <template v-if="tycInfo.base">{{ tycInfo.base }}</template>
                                <template v-if="tycInfo.base && tycInfo.city"> / </template>
                                <template v-if="tycInfo.city">{{ tycInfo.city }}</template>
                                <template v-if="tycInfo.city && tycInfo.district"> / </template>
                                <template v-if="tycInfo.district">{{ tycInfo.district }}</template>
                            </ui-form-item>
                            <ui-form-item label="注册地址">{{ tycInfo.regLocation || '-' }}</ui-form-item>
                            <ui-form-item label="曾用名">{{ tycInfo.historyNames || '-' }}</ui-form-item>
                            <ui-form-item label="法人">{{ tycInfo.legalPersonName || '-' }}</ui-form-item>
                            <ui-form-item label="注册资本">{{ tycInfo.regCapital || '-' }}</ui-form-item>
                            <ui-form-item label="纳税人识别号">{{ tycInfo.taxNumber || '-' }}</ui-form-item>
                            <ui-form-item label="企业联系方式">{{ tycInfo.phoneNumber || '-' }}</ui-form-item>
                            <ui-form-item label="行业">{{ tycInfo.industry || '-' }}</ui-form-item>
                            <ui-form-item label="国民经济行业分\n类">{{ category_Big_Middle_Small || '-' }}</ui-form-item>
                            <ui-form-item label="成立日期">{{ tycInfo.estiblishTime | filterDetailDateTime }}</ui-form-item>
                            <ui-form-item label="网址">{{ tycInfo.websiteList || '-' }}</ui-form-item>
                            <ui-form-item label="经营范围">{{ tycInfo.businessScope || '-' }}</ui-form-item>
                        </div>
                    </div>
                </template>
                <div class="erp-load-empty" v-else>
                    <img src="/mstatic/image/empty/search-empty.svg"/>
                    <p>抱歉，未能匹配到天眼查公司数据，</p>
                    <p>建议您检查关键词重新撞索</p>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    data() {
        return {
            traderName: '', // 客户名称
            tycInfo: {}, // 天眼查公司信息
        };
    },
    mounted() {
        
    },
    filters: {
        filterDetailDateTime (val) {
            if (/\d{10,}/.test(val)) {
                return moment(val).format('YYYY-MM-DD');
            } else {
                return '-'
            }
        },
    },
    computed: {
        category_Big_Middle_Small () {
            let arr = [];
            this.tycInfo.category && arr.push(this.tycInfo.category);
            this.tycInfo.categoryBig && arr.push(this.tycInfo.categoryBig);
            this.tycInfo.categoryMiddle && arr.push(this.tycInfo.categoryMiddle);
            this.tycInfo.categorySmall && arr.push(this.tycInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    methods: {
        async show (traderName, tycInfo) {
            this.traderName = traderName || '';
            this.tycInfo = tycInfo || {};
            if (!(this.tycInfo && Object.keys(this.tycInfo).length)) {
                let fetxhDetail = await this.getDetail(this.traderName);
                if (fetxhDetail.success) {
                    this.tycInfo = fetxhDetail.data || {};
                }
            }
            this.$refs.tycDetailDialog.show();
        },

        // 获取详情信息
        getDetail (name) {
            return new Promise((resolve, reject) => {
                this.$axios.post(`/crm/trader/profile/queryTycDetail?traderName=${name}`).then(({data}) => {
                    resolve(data || {});
                }).catch(errr=> {
                    reject(errr || {});
                    console.log('fetch error', errr);
                })
            })
        },
    }
})
/* 天眼查列表 */
Vue.component('ui-tyc-list', {
    template: `
        <crm-slide-dialog ref="tycListDialog" title="客户名称" :refresh="true">
            <div class="tyc-list-panel">

                <div class="tyc-list-wrap" @click.stop>
                    <div class="tyc-list" v-if="tycList.length">
                        <div 
                            class="tyc-item" 
                            v-for="(item, index) in tycList" :key="index"
                            @click.stop="chooseThis(item)"
                            :class="{'active': item.id == chooseTyc.id }"
                        >
                            <div class="tyc-select">
                                <i v-if="item.id == chooseTyc.id" class="vd-ui_icon icon-radio3"></i>
                                <i v-else class="vd-ui_icon icon-radio1"></i>
                            </div>
                            <div class="item-right">
                                <p class="name" v-html="lightName(item)"></p>
                                <div class="attrs">
                                    <div class="attr-item">
                                        <font class="label">机构类型：</font>{{ item.companyType | filterCompanyType }}
                                    </div>
                                    <div class="attr-item">
                                        <font class="label">企业法人：</font>{{ item.legalPersonName }}
                                    </div>
                                    <div class="attr-item">
                                        <font class="label">地区省份：</font>{{ item.base }}
                                    </div>
                                    <div class="attr-item">
                                        <font class="label">成立日期：</font>{{ item.estiblishTime | filterDateTime }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="erp-load-empty" v-else>
                        <img src="/mstatic/image/empty/search-empty.svg"/>
                        <p>抱歉，未能匹配到天眼查公司数据，</p>
                        <p>建议您检查关键词重新撞索</p>
                    </div>
                </div>

                <div class="slide-dialog-default-footer">
                    <div class="btn-cancel-flex">
                        <vd-ui-button @click="handlerCancel">取消</vd-ui-button>
                    </div>
                    <div class="btn-confirm-flex">
                        <vd-ui-button type="primary" @click="handlerConfirm">确定</vd-ui-button>
                    </div>
                </div>
            </div>
        </crm-slide-dialog>
    `,
    props: {
       
    },
    data() {
        return {
            searchName: '', // 客户名称
            tycList: [], // 天眼查列表
            chooseTyc: {}, // 选择的公司
            canAjax: true,
            loading: false,
        };
    },
    
    filters: {
        filterDateTime (val) {
            if (/^\d{4}-\d{2}-\d{2}/.test(val)) {
                return val.substr(0, 10);
            } else {
                return '-'
            }
        },
        filterCompanyType (val) {
            let TYPES = {
                1: '公司',
                2: '香港企业',
                3: '社会组织',
                4: '律所',
                5: '事业单位',
                6: '基金会',
                7: '不存在法人、注册资本、统一社会信用代码、经营状态',
                8: '台湾企业',
                9: '新机构',
            }
            return TYPES[val] || ''
        }
    },
    computed: {
        // 高亮
        lightName () {
            return (item) => {
                let name = item.name;
                if (!this.searchName) return name;
                const regExp = new RegExp(this.searchName, 'g');
                name = name.replace(regExp, `<font color='#FF6600'>${this.searchName}</font>`);
                return name;
            }
        },
    },
    mounted() {
        
    },
    methods: {
        show (key) {
            this.searchName = key || '';
            this.getList();
            this.$refs.tycListDialog.show();
        },

        getList() {
            this.canAjax = false;
            this.loading = true;

            this.$axios.post(`/crm/trader/m/queryTycList?traderName=${this.searchName}`).then(({data}) => {
                if (data.success) {
                    this.canAjax = true;
                    this.tycList = data.data || [];
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
                this.loading = false;
            }).catch(err=> {
                this.loading = false;
            })
        },

        // 选择天眼查公司
        async chooseThis(item) {
            this.chooseTyc = item;
        },

        // 确定
        handlerConfirm () {
            let emitData = Object.assign({}, this.chooseTyc, {
                tycFlag: 'Y',
                traderId: '',
                saleName: '',
                traderName: this.chooseTyc.name || ''
            });
            this.$emit('change', emitData);

            setTimeout(()=> {
                this.handlerCancel();
            }, 30);
        },
        // 取消
        handlerCancel () {
            this.$refs.tycListDialog.hide();
            this.tycList = [];
            this.chooseTyc = {};
        },
    }
})
Vue.component('ui-map-view', {
    template: `<div class="vd-ui-map-wrap">
        <div class="vd-ui-map-trigger" @click="showMap">
            <slot></slot>
        <div>
        <crm-slide-dialog ref="mapDialog" title="查看地图" zindex="9999">
            <div class="ui-map-iframe-wrap">
                <iframe :src="url"></iframe>
            </div>
        </crm-slide-dialog>
    </div>`,
    props: {
        address: {
            type: String,
            default: ''
        },
        // 省市区名称
        area: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            isShow: false,
            url: ''
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        showMap() {
            let addressStr = this.area + this.address;
            this.url = 'https://m.amap.com/search/mapview/keywords=' + encodeURI(addressStr) + '&cluster_state=5&pagenum=1';
            this.$refs.mapDialog.show();
        }
    }
})

Vue.component('ui-location-view', {
    template: `<div class="vd-ui-map-wrap">
        <div class="vd-ui-map-trigger" @click="showMap">
            <slot></slot>
        <div>
    </div>`,
    props: {
        location: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        showMap() {
            if(!this.location) {
                this.$message.warn('历史打卡数据，无法地图查看');
                return;
            }
            ww.openLocation({
                longitude: this.location.split(',')[0],
                latitude: this.location.split(',')[1],
                scale: 16
            })
        }
    }
})
Vue.component('ui-wx-upload', {
    template: `<div class="ui-wx-upload-wrap">
        <div class="ui-wx-upload-item" @click="prevImg(index)" v-for="(item, index) in picList" :key="index">
            <img :src="item">
            <div class="ui-wx-upload-item-del" @click.stop="delItem(index)">
                <i class="vd-ui_icon icon-recycle"></i>
            </div>
        </div>
        <div class="ui-wx-upload-btn" @click="chooseImg" v-show="picList.length < limit">
            <div class="ui-wx-upload-btn-inner" v-if="!isloading"></div>
            <i class="vd-ui_icon icon-loading" v-if="isloading"></i>
        </div>
    </div>`,
    props: {
        limit: {
            type: Number,
            default: 5
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            isloading: false,
            picList: []
        };
    },
    computed: {

    },
    mounted() {
        this.picList = this.list;
    },
    methods: {
        async chooseImg() {
            if(this.picList.length >= this.limit || this.isloading) {
                return;
            }

            this.isloading = true;
            let _this = this;
            let img = await ww.chooseImage({
                count: 1,
                sizeType: ['original'],
                sourceType: ['camera'],
                defaultCameraMode: 'normal',
                cancel: function(){
                    _this.isloading = false;
                }
            });

            if(img.errCode === 0 && img.localIds && img.localIds.length) {
                let uploadInfo = await ww.uploadImage({
                    localId: img.localIds[0],
                    isShowProgressTips: true
                })

                if(uploadInfo.errCode === 0 && uploadInfo.serverId) {
                    this.$axios.get('/crm/common/public/uploadWxFileByMediaId?mediaId=' + uploadInfo.serverId).then(({data}) => {
                        this.isloading = false;
                        if(data.code === 0) {
                            this.picList.push(data.httpUrl);
                            this.$emit('change', this.picList);
                        }
                    })
                }
            } 
        },
        delItem(index) {
            this.picList.splice(index, 1);
            this.$emit('change', this.picList);
        },
        prevImg(index) {
            ww.previewImage({
                current: this.picList[index],
                urls: this.picList
            });
        }
    }
})
