<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.VHostWordMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.dto.VHostWordDTO">
    <id column="HOST_WORD_ID" jdbcType="INTEGER" property="hostWordId" />
    <result column="OP_HOST_WORD_ID" jdbcType="INTEGER" property="opHostWordId" />
    <result column="WORD_NAME" jdbcType="VARCHAR" property="wordName" />
    <result column="OPERATE_STATUS" jdbcType="INTEGER" property="operateStatus" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="CREATOR" jdbcType="VARCHAR" property="creator" />
    <result column="UPDATER" jdbcType="VARCHAR" property="updater" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    HOST_WORD_ID, OP_HOST_WORD_ID, WORD_NAME, OPERATE_STATUS, ADD_TIME, MODE_TIME, IS_DELETE,
    CREATOR, UPDATER
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.dto.VHostWordDTOExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from V_HOST_WORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_HOST_WORD
    where HOST_WORD_ID = #{hostWordId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from V_HOST_WORD
    where HOST_WORD_ID = #{hostWordId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.dto.VHostWordDTOExample">
    delete from V_HOST_WORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="HOST_WORD_ID" keyProperty="hostWordId" parameterType="com.vedeng.goods.dto.VHostWordDTO" useGeneratedKeys="true">
    insert into V_HOST_WORD (OP_HOST_WORD_ID, WORD_NAME, OPERATE_STATUS,
      ADD_TIME, MODE_TIME, IS_DELETE,
      CREATOR, UPDATER)
    values (#{opHostWordId,jdbcType=INTEGER}, #{wordName,jdbcType=VARCHAR}, #{operateStatus,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{modeTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER},
      #{creator,jdbcType=VARCHAR}, #{updater,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="HOST_WORD_ID" keyProperty="hostWordId" parameterType="com.vedeng.goods.dto.VHostWordDTO" useGeneratedKeys="true">
    insert into V_HOST_WORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="opHostWordId != null">
        OP_HOST_WORD_ID,
      </if>
      <if test="wordName != null">
        WORD_NAME,
      </if>
      <if test="operateStatus != null">
        OPERATE_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="opHostWordId != null">
        #{opHostWordId,jdbcType=INTEGER},
      </if>
      <if test="wordName != null">
        #{wordName,jdbcType=VARCHAR},
      </if>
      <if test="operateStatus != null">
        #{operateStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.dto.VHostWordDTOExample" resultType="java.lang.Long">
    select count(*) from V_HOST_WORD
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
    <update id="updateByExampleSelective" parameterType="map">
    update V_HOST_WORD
    <set>
      <if test="record.hostWordId != null">
        HOST_WORD_ID = #{record.hostWordId,jdbcType=INTEGER},
      </if>
      <if test="record.opHostWordId != null">
        OP_HOST_WORD_ID = #{record.opHostWordId,jdbcType=INTEGER},
      </if>
      <if test="record.wordName != null">
        WORD_NAME = #{record.wordName,jdbcType=VARCHAR},
      </if>
      <if test="record.operateStatus != null">
        OPERATE_STATUS = #{record.operateStatus,jdbcType=INTEGER},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modeTime != null">
        MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=INTEGER},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update V_HOST_WORD
    set HOST_WORD_ID = #{record.hostWordId,jdbcType=INTEGER},
      OP_HOST_WORD_ID = #{record.opHostWordId,jdbcType=INTEGER},
      WORD_NAME = #{record.wordName,jdbcType=VARCHAR},
      OPERATE_STATUS = #{record.operateStatus,jdbcType=INTEGER},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{record.modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{record.isDelete,jdbcType=INTEGER},
      CREATOR = #{record.creator,jdbcType=VARCHAR},
      UPDATER = #{record.updater,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.dto.VHostWordDTO">
    update V_HOST_WORD
    <set>
      <if test="opHostWordId != null">
        OP_HOST_WORD_ID = #{opHostWordId,jdbcType=INTEGER},
      </if>
      <if test="wordName != null">
        WORD_NAME = #{wordName,jdbcType=VARCHAR},
      </if>
      <if test="operateStatus != null">
        OPERATE_STATUS = #{operateStatus,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=VARCHAR},
      </if>
    </set>
    where HOST_WORD_ID = #{hostWordId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.dto.VHostWordDTO">
    update V_HOST_WORD
    set OP_HOST_WORD_ID = #{opHostWordId,jdbcType=INTEGER},
      WORD_NAME = #{wordName,jdbcType=VARCHAR},
      OPERATE_STATUS = #{operateStatus,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MODE_TIME = #{modeTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=VARCHAR}
    where HOST_WORD_ID = #{hostWordId,jdbcType=INTEGER}
  </update>

  <select id="getHostWordByOpHostId" resultType="com.vedeng.goods.dto.VHostWordDTO">
    SELECT * FROM V_HOST_WORD A WHERE A.OP_HOST_WORD_ID = #{opHostWordId,jdbcType=INTEGER}
  </select>

</mapper>
