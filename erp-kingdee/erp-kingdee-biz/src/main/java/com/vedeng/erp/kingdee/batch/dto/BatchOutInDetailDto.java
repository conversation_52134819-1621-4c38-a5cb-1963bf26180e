package com.vedeng.erp.kingdee.batch.dto;

/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/16
 */
public class BatchOutInDetailDto {
    /**
     * 主键
     */
    private Long warehouseGoodsOutInId;
	private Integer firstEngageId;
    private String outInNo;
    private Integer goodsId;
    private String skuNo;
    private String skuName;
    private Integer skuId;
    private Integer spuId;
    private String brandName;
    private String model;
    private String spec;
    private Integer num;

    /**关联的订单明细*/
    private Integer relatedId;

    /**是否为赠品*/
    private Integer isGift;

    /**
     * 单位
     */
    private String unitName;
    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;
    /**
     * 生产日期
     */
    private Long productDate;
    private String productDateStr;
    /**
     * 有效期
     */
    private Long expirationDate;
    private String expirationDateStr;

    /**
     * 入库时间
     */
    private Long checkStatusTime;

    private String checkStatusTimeStr;

    /**
     * 批次号
     */
    private String batchNumber;
    /**
     *SN码
     */
    private String barcodeFactory;
    /**
     * 授权书类型
     */
    private String authType;
    /**
     * 灭菌编号
     */
    private String sterilizationBatchNo;

    /**
     * 注册证编号
     */
    private String registrationNumber;

    private boolean is6840 = false;

    public boolean isIs6840() {
        return is6840;
    }

    public void setIs6840(boolean is6840) {
        this.is6840 = is6840;
    }

    /**
     * 类型
     */
    private Integer spuType;

    public Integer getSpuType() {
        return spuType;
    }

    public void setSpuType(Integer spuType) {
        this.spuType = spuType;
    }

    public Long getWarehouseGoodsOutInId() {
        return warehouseGoodsOutInId;
    }

    public void setWarehouseGoodsOutInId(Long warehouseGoodsOutInId) {
        this.warehouseGoodsOutInId = warehouseGoodsOutInId;
    }

    public String getCheckStatusTimeStr() {
        return checkStatusTimeStr;
    }

    public void setCheckStatusTimeStr(String checkStatusTimeStr) {
        this.checkStatusTimeStr = checkStatusTimeStr;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    public String getVedengBatchNumer() {
        return vedengBatchNumer;
    }

    public void setVedengBatchNumer(String vedengBatchNumer) {
        this.vedengBatchNumer = vedengBatchNumer;
    }

    public Long getProductDate() {
        return productDate;
    }

    public void setProductDate(Long productDate) {
        this.productDate = productDate;
    }

    public Long getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(Long expirationDate) {
        this.expirationDate = expirationDate;
    }

    public Long getCheckStatusTime() {
        return checkStatusTime;
    }

    public void setCheckStatusTime(Long checkStatusTime) {
        this.checkStatusTime = checkStatusTime;
    }

    public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public String getBarcodeFactory() {
        return barcodeFactory;
    }

    public void setBarcodeFactory(String barcodeFactory) {
        this.barcodeFactory = barcodeFactory;
    }

    public String getAuthType() {
        return authType;
    }

    public void setAuthType(String authType) {
        this.authType = authType;
    }

    public String getSterilizationBatchNo() {
        return sterilizationBatchNo;
    }

    public void setSterilizationBatchNo(String sterilizationBatchNo) {
        this.sterilizationBatchNo = sterilizationBatchNo;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getProductDateStr() {
        return productDateStr;
    }

    public void setProductDateStr(String productDateStr) {
        this.productDateStr = productDateStr;
    }

    public String getExpirationDateStr() {
        return expirationDateStr;
    }

    public void setExpirationDateStr(String expirationDateStr) {
        this.expirationDateStr = expirationDateStr;
    }

    public String getOutInNo() {
        return outInNo;
    }

    public void setOutInNo(String outInNo) {
        this.outInNo = outInNo;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public Integer getSpuId() {
        return spuId;
    }

    public void setSpuId(Integer spuId) {
        this.spuId = spuId;
    }

	public Integer getFirstEngageId() {
		return firstEngageId;
	}

	public void setFirstEngageId(Integer firstEngageId) {
		this.firstEngageId = firstEngageId;
	}

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    public Integer getIsGift() {
        return isGift;
    }

    public void setIsGift(Integer isGift) {
        this.isGift = isGift;
    }
}
