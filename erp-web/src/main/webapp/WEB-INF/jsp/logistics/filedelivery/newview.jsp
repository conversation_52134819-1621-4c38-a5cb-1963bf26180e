<!DOCTYPE html>
<html lang="en">
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增寄送文件" scope="application" />
 <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
<%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

<%@ page trimDirectiveWhitespaces="true" %>

<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<head>
<c:set var="path" value="<%=basePath%>" scope="application" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<meta name="renderer" content="webkit|ie-comp|ie-stand">
<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
 <title>${title}</title>
<link data-n-head="ssr" rel="icon" type="image/x-icon" href="<%=basePath%>static/favicon.ico">

<link  rel="stylesheet" href='<%= basePath %>webjars/layuidist/css/layui.css?rnd=${resourceVersionKey}'></link>
<link  rel="stylesheet" href='<%= basePath %>webjars/topezadmin/layui/css/ezform.css?rnd=${resourceVersionKey}'></link>
<script src="<%= basePath %>webjars/layuidist/layui.js?rnd=${resourceVersionKey}"></script>
<script src="<%= basePath %>webjars/jquery/1.12.4/jquery.min.js?rnd=${resourceVersionKey}"></script>

</head>
<body>
<object  id="LODOP_OB" classid="clsid:2105C259-1E0C-4534-8141-A753534CB4CA" width=0 height=0>
    <embed id="LODOP_EM" type="application/x-print-lodop" width=0 height=0></embed>
</object>

<script>
        var LODOP;
</script>
<style>
    .pageheight{
        height: calc(100vh - 75px);
        overflow-y: scroll;
        background: #fff;
    }
</style>

<div class="layui-fluid"  >
    <div class="layui-col-space10 layui-row"  >
        <div class="layui-col-md12 layui-col-lg7 ">
            <input type="hidden" name="fileDeliveryId" id="fileDeliveryId" value="${deliveryVO.fileDeliveryId}">
            <input type="hidden" name="taskInfoId" id="taskInfoId" value="${taskInfo.id}">
            <div class="layui-card  " >
                <div class="layui-card-body"  >
            <form class="layui-form   "    >
                <div class="layui-col-space10 layui-row pageheight">
                    <div class="layui-col-xs12  pageformheight">
                        <div class="layui-card">
<%--                            <div class="layui-card-header">基本信息</div>--%>
                            <div class="layui-card-body">
                                <div class="layui-row">
                                    <div class="selector layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">寄件类型 :</label>
                                            <div class="layui-input-block">
                                                <input type="radio" disabled lay-filter="deliveryType" name="deliveryType" value="1" title="文件资料类" <c:if test="${deliveryVO.deliveryType ne 2}">checked </c:if>>
                                                <input type="radio" disabled lay-filter="deliveryType" name="deliveryType" value="2" title="非文件资料类" <c:if test="${deliveryVO.deliveryType eq 2}">checked </c:if>>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">
                                               
                                                寄件总重 :</label>
                                            <div class="layui-input-block">
                                                <div class="layui-input-wrap">
                                                    <c:choose>
                                                        <c:when test="${empty deliveryVO or empty deliveryVO.deliveryTotalWeight}">
                                                            <c:set var="deliveryTotalWeight" value="1" />
                                                        </c:when>
                                                        <c:otherwise>
                                                            <c:set var="deliveryTotalWeight" value="${deliveryVO.deliveryTotalWeight}" />
                                                        </c:otherwise>
                                                    </c:choose>
                                                    <input type="number" disabled name="deliveryTotalWeight" value="${deliveryTotalWeight}" max="99" required lay-verify="decimal"  class="layui-input" >
                                                    <div class="layui-input-split layui-input-suffix">
                                                        KG
                                                    </div>
                                                </div>
<%--                                                <small class="layui-form-mid layui-text-em item_desc">- 将通过重量动态选择承运部门与快递公司</small>--%>
                                            </div>
<%--                                            <div class="layui-form-mid layui-text-em">--%>
<%--                                                KG--%>
<%--                                            </div>--%>
<%--                                            <div class="layui-form-mid layui-text-em">--%>
<%--                                                <i class="layui-icon layui-icon-tips"  lay-on="test-tips-top"--%>
<%--                                                 ></i>--%>
<%--                                            </div>--%>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item" >
                                            <label class="layui-form-label">
                                               
                                                物品名称 :</label>
                                            <div class="layui-input-block">

                                                    <input disabled type="text" name="deliveryProdName" value="${deliveryVO.deliveryProdName}" maxlength="20" required   class="layui-input" >

                                                <!--                                <small class="layui-form-mid layui-text-em item_desc">- 标题应简洁、吸引人，并包含品牌名称。</small>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12 layui-col-xl6 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">
                                               
                                                包裹总数 :</label>
                                            <div class="layui-input-block">

                                                    <input disabled type="number" name="deliveryExpressNum" value="${deliveryVO.deliveryExpressNum}"  maxlength="5" required    class="layui-input" >

                                                <!--                                <small class="layui-form-mid layui-text-em item_desc">- 标题应简洁、吸引人，并包含品牌名称。</small>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="layui-col-xs12   layui-col-space20">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">
                                               
                                                寄件说明 :</label>
                                            <div class="layui-input-block">

                                                    <textarea disabled  name="content"  maxlength="100" required    class="layui-textarea" >${deliveryVO.content}</textarea>

                                                <!--                                <small class="layui-form-mid layui-text-em item_desc">- 标题应简洁、吸引人，并包含品牌名称。</small>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="selector layui-col-xs12 layui-col-xl12 layui-col-space10">
                                        <div class="layui-form-item"  >
                                            <label class="layui-form-label">  承运部门 :</label>
                                            <div class="layui-input-block">
                                                <input type="radio" disabled lay-filter="deliveryDept"   name="deliveryDept" value="1" title="财务部" <c:if test="${empty deliveryVO or empty deliveryVO.deliveryDept or deliveryVO.deliveryDept eq 1}">checked </c:if>  >
                                                <input type="radio" disabled lay-filter="deliveryDept"   name="deliveryDept" value="2" title="物流部" <c:if test="${deliveryVO.deliveryDept eq 2}">checked </c:if> >
                                                <input type="radio" disabled lay-filter="deliveryDept"   name="deliveryDept" value="3" title="申请人所在部门" <c:if test="${deliveryVO.deliveryDept eq 3}">checked </c:if>  >
                                            </div>
<%--                                            <small class="layui-form-mid layui-text-em item_desc">- 根据寄件类型与重量自动选择，申请人所在部门职能寄送EMS和电商标快。</small>--%>
                                        </div>
                                    </div>

                                    <div class="selector layui-col-xs12 layui-col-xl12 layui-col-space10">
                                        <div class="layui-form-item"   >
                                            <label class="layui-form-label">  寄件人 :</label>
                                            <div class="layui-input-block">
                                                <input type="radio" disabled lay-filter="sendUserType"   name="sendUserType" value="1" title="南京贝登医疗股份有限公司" <c:if test="${empty deliveryVO or empty deliveryVO.sendUserType or deliveryVO.sendUserType eq 1}">checked </c:if>  >
                                                <input type="radio" disabled lay-filter="sendUserType"   name="sendUserType" value="2" title="申请人" <c:if test="${deliveryVO.sendUserType eq 2}">checked </c:if> >
                                            </div>
                                            <%--                                            <small class="layui-form-mid layui-text-em item_desc" style="padding:0px !important;">- 根据寄件类型与重量自动选择，申请人所在部门只能寄送EMS和电商标快。</small>--%>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-xs12  ">
                        <div class="layui-card">
                            <div class="layui-card-header">收件单位
                                <div class=" " style="float:right">
                                    快递公司:
                                    <c:choose>
                                        <c:when test="${deliveryVO.logisticsName eq 1}">
                                            邮政EMS
                                        </c:when>
                                        <c:when test="${deliveryVO.logisticsName eq 2}">
                                            邮政电商标快
                                        </c:when>
                                        <c:when test="${deliveryVO.logisticsName eq 3}">
                                            顺丰
                                        </c:when>
                                        <c:when test="${deliveryVO.logisticsName eq 5}">
                                            中通
                                        </c:when>
                                        <c:when test="${deliveryVO.logisticsName eq 4}">
                                            其他
                                        </c:when>
                                        <c:otherwise>
                                            -
                                        </c:otherwise>
                                    </c:choose>
                                    
<%--                                <a id="addmodel" class="layui-btn layui-btn-sm layui-btn-primary"   style="color:#16baaa">--%>
<%--                                    <i class="layui-icon layui-icon-add-1"></i>添加收件单位</a>--%>
<%--                                <a id="import"  class="layui-btn layui-btn-sm layui-btn-primary" style="color:#16baaa" >--%>
<%--                                    <i class="layui-icon layui-icon-upload-drag"></i>--%>
<%--                                    导入收件单位</a>--%>
                                </div>
                            </div>
                            <div class="layui-card-body table-c" style="padding:0px" >
                                <table class="layui-table" lay-filter="addresstable">
                                    <thead>

                                        <tr>
                                            <th lay-data="{field:'ID',hide:true}">ID</th>
                                            <th lay-data="{field:'name1', width:120}">收件单位类型</th>
                                            <th lay-data="{field:'name2', width:150}">收件单位</th>
                                            <th lay-data="{field:'name3', width:80}">联系人</th>
                                            <th lay-data="{field:'name4', width:120}">联系电话</th>
                                            <th lay-data="{field:'name5', minWidth:200}">联系地址</th>
                                            <th lay-data="{field:'name6', minWidth:151,escape:false }">快递单号</th>


                                            <c:choose>
                                                <c:when test="${deliveryVO.deliveryStatus eq 1}">
                                                    <th lay-data="{ field:'name7',style:'padding:0px !important', minWidth:135, templet: '#ID-templet-logisticsNoEdit'}">变更后快递单号</th>
                                                </c:when>
                                                <c:otherwise>

                                                </c:otherwise>
                                            </c:choose>


                                             <th lay-data="{field:'name8', width:120, escape:false}" >寄送状态</th>
                                        </tr>

                                    </thead>
                                    <tbody>
                                    <c:forEach items="${deliveryAddressVO}" var="addressVo" varStatus="status">
                                        <tr >
                                        <td  >${addressVo.id}</td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${addressVo.traderType eq 1}">
                                                    客户
                                                </c:when>
                                                <c:when test="${addressVo.traderType eq 2}">
                                                    供应商
                                                </c:when>
                                                <c:otherwise>
                                                    其他
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td>
                                          ${addressVo.traderName}
                                        </td>
                                        <td>
                                          ${addressVo.traderContactName}
                                        </td>
                                        <td>${addressVo.traderContactMobileHidden}</td>
                                        <td>
                                                ${addressVo.traderContactAddress}
                                        </td>
                                            <td> ${addressVo.logisticsNo}</td>

                                            <c:choose>
                                                <c:when test="${deliveryVO.deliveryStatus eq 1}">
                                                    <td> ${addressVo.logisticsNoEdit}</td>
                                                </c:when>
                                                <c:otherwise>

                                                </c:otherwise>
                                            </c:choose>

                                        <td>
                                            <c:choose>
                                                <c:when test="${addressVo.deliveryStatus eq 1}">
                                                    已寄送
                                                </c:when>
                                                <c:otherwise>
                                                    未寄送
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                    </tr>
                                    </c:forEach>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>


            </form>
                </div>
            </div>
            <div class="layui-row layui-panel formtopfix formtopfixbuttons" style="margin-left: -16px;"  >
                <div style="margin: 10px 0px 0px 10px;float:left" class="
                    layui-col-md9 layui-col-md-offset3 layui-btn-container" id="submitButtonContainer">
                                            <c:if test="${fileDeliveryButtonVO.printButton  eq 1}">
                    <button   class="print layui-btn layui-bg-blue " >打印面单</button>
                                            </c:if>

                                            <c:if test="${fileDeliveryButtonVO.passButton  eq 1}">
                    <button   class="approve layui-btn layui-bg-blue " >通过</button>
                                            </c:if>
                                            <c:if test="${fileDeliveryButtonVO.noPassButton  eq 1}">
                    <button   class="reject layui-btn layui-bg-blue " >驳回</button>
                                           </c:if>
                    <c:if test="${fileDeliveryFlowVOList.size()>0}">
                        <button   class="checklogbtn layui-btn layui-bg-blue " >审批信息</button>
                    </c:if>
                                            <c:if test="${fileDeliveryButtonVO.closeButton  eq 1}">
                    <button   class="close layui-btn layui-bg-blue " >关闭寄送</button>
                                            </c:if>

                </div>
            </div>
        </div>
        <div class="layui-col-md12  layui-col-lg5  ">

            <div class="layui-card pageheight" >
<%--                <div class="layui-card-header">说明</div>--%>
                <div class="layui-card-body"  >
                    <div class="layui-row">
                          ${config}
                    </div>
                </div>
            </div>

        </div>

    </div>
</div>
<input type="hidden" id="emsprintname" value="${emsprintname}">
<input type="hidden" id="sfprintname" value="${sfprintname}">
<input type="hidden" id="ztprintname" value="${ztprintname}">
<input type="hidden" id="deliveryStatus" value="${deliveryVO.deliveryStatus}">


<script type="text/html" id="printform">
    <div class="layui-container" style="width:450px">
        <form class="layui-form" style="padding-top:15px" >
            <div class="layui-form-item">
                <label class="layui-form-label">快递公司</label>
                <div class="layui-input-block" style="width:150px">
                    <select name="EXPRESS_COMPANY" lay-filter="EXPRESS_COMPANY" style="width:200px">
                        <option value="1"  <c:if test="${deliveryVO.logisticsName eq 1}"> selected </c:if>>邮政EMS</option>
                        <option value="2" <c:if test="${deliveryVO.logisticsName eq 2}"> selected </c:if>>邮政电商标快</option>
                        <option value="3" <c:if test="${deliveryVO.logisticsName eq 3}"> selected </c:if>>顺丰</option>
                        <option value="5" <c:if test="${deliveryVO.logisticsName eq 5}"> selected </c:if>>中通</option>
                        <option value="4" <c:if test="${deliveryVO.logisticsName eq 4}"> selected </c:if>>其他</option>
                    </select>
                </div>
            </div>
        </form>
    </div>
</script>

<c:if test="${fileDeliveryFlowVOList.size()>0}">
<%--    verifyStatus--%>
<script type="text/html" id="checklog">
    <div class="layui-timeline  layui-padding-3">
        <c:forEach items="${fileDeliveryFlowVOList}" var="flow" varStatus="status">
                <div class="layui-timeline-item">
                    <i class="layui-icon layui-timeline-axis
                     <c:choose>
                        <c:when test="${ flow.flowName eq '驳回' or (flow.flowName eq '上级审批' and deliveryVO.verifyStatus eq 3)}">
                            layui-icon-error  layui-font-red
                        </c:when>
                        <c:when test="${ flow.flowName eq '上级审批' and deliveryVO.verifyStatus eq 1}">
                            layui-icon-circle layui-bg-gray
                        </c:when>
                        <c:otherwise>
                            layui-icon-ok-circle
                        </c:otherwise>
                    </c:choose>
                    "></i>
                    <div class="layui-timeline-content layui-text">
                        <h3 class="layui-timeline-title">${flow.flowName}
                            <span style="float:right;color:gray;font-size:14px">${flow.operatorTime}</span></h3>
                        <c:if test="${flow.flowName ne '驳回' and flow.flowName ne '结束'}">
                            <p >
                                <i class="layui-icon-friends layui-icon"></i> ${flow.operatorName}
                            </p>
                            <c:if test="${not empty flow.remark}">
                                <p >
                                    <textarea disabled class=layui-textarea>${flow.remark}</textarea>
                                </p>
                            </c:if>
                        </c:if>
                    </div>
                </div>
        </c:forEach>
    </div>
</script>
</c:if>

<script>
    // Usage
    layui.use(function(){
        let layer = layui.layer;
        let form=layui.form;
        let table=layui.table;
        var util = layui.util;
        // Welcome
        // layer.msg('Hello World', {icon: 6});
        var hh= $(window).height()-$(".pageformheight").height()-140;
        var cg={};
        if(hh>200){
            cg.height=hh;
        }else{
            cg.height=200;
        }
        table.init('addresstable',cg);
        if($("#checklog").length>0){
            var xindexlog=0;
            $(".checklogbtn").click(function(){
                xindexlog=layer.open({
                    type: 1,
                    offset: 'r',
                    resize: false,
                   // maxmin: true,
                    title: '审批信息',
                    anim: 'slideLeft', // 从右往左
                    area: ['320px', '100%'],
                    shade: 0,
                    zIndex: 99,
                    id: 'ID-demo-layer-direction-r',
                    content:  $("#checklog").html(),
                    success: function(){
                        //$(".checklogbtn").hide();
                    },
                    end: function(){
                       // $(".checklogbtn").show();
                    }
                });
                return false;
            })
            $(".checklogbtn").click();
        }


        $(".approve").click(function(){
            var loadIndex = layer.load(0);
            $.post( '/logistics/filedeliveryNew/complementTask.do',
                {
                    fileDeliveryId:$("#fileDeliveryId").val(),
                    pass:true,
                    taskId:$("#taskInfoId").val()
                }, function(response) {
                  layer.close(loadIndex);
                    if(response.success){
                        layui.layer.alert("操作成功");
                        location.reload();
                    }else{
                        layui.layer.alert(response.message);
                    }

                }, 'json').fail(function () {
                layer.close(loadIndex);
                layui.layer.alert("系统异常");
            });
            return false;
        })
        $(".reject").click(function(){

            layer.prompt({title: '请输入审核备注', formType: 2}, function(value, index, elem){
                if(value === '') return elem.focus();
                var loadIndex = layer.load(0);
               // layer.msg('获得：'+ util.escape(value)); // 显示 value
                $.post(  '/logistics/filedeliveryNew/complementTask.do',
                    {
                        fileDeliveryId:$("#fileDeliveryId").val(),
                        pass:false,
                        taskId:$("#taskInfoId").val(),
                        comment: util.escape(value)
                    }, function(response) {
                        layer.close(loadIndex);
                        if(response.success){
                            layui.layer.alert("操作成功");
                            location.reload();
                        }else{
                            layui.layer.alert(response.message);
                        }

                    }, 'json').fail(function () {
                        layer.close(loadIndex);
                    layui.layer.alert("系统异常");
                });
                // 关闭 prompt
                layer.close(index);
            });
            return false;
        })
        $(".close").click(function(){
            var loadIndex = layer.load(0);
            $.post(  '/logistics/filedeliveryNew/closeFileDelivery.do',
                {
                    fileDeliveryId:$("#fileDeliveryId").val()
                }, function(response) {
                    layer.close(loadIndex);
                    if(response.success){
                        layui.layer.alert("操作成功");
                        location.reload();
                    }else{
                        layui.layer.alert(response.message);
                    }

                }, 'json').fail(function () {
                layer.close(loadIndex);
                layui.layer.alert("系统异常");
            });

            return false;
        })




        // 单元格编辑事件
        table.on('edit(addresstable)', function(obj){
            var field = obj.field; // 得到修改的字段
            var value = obj.value // 得到修改后的值
            var oldValue = obj.oldValue // 得到修改前的值 -- v2.8.0 新增
            var data = obj.data // 得到所在行所有键值
            var col = obj.getCol(); // 得到当前列的表头配置属性 -- v2.8.0 新增
            if(value.length>100){
                layui.layer.alert("单号过长")
                return;
            }
            console.log("addresstable>>>"+JSON.stringify(data)+value); // 查看对象所有成员
            $.post( '/logistics/filedeliveryNew/updateLogisticNos.do' ,
                {
                    addressId:data.ID,
                    logisticNos:value
                }, function(response) {
                    if(response.success){
                        var update = {};
                        update[field] = value;
                        obj.update(update, true);
                    }else{
                        layui.layer.alert(response.message);
                    }

                }, 'json').fail(function () {
                layer.close(loadIndex);
                layui.layer.alert("系统异常");
            });
        });


        $("body").on("focusout","[name=item-logisticsNoEdit]",function(){
            var value=$(this).val();
            var ID=$(this).attr("data-id");
            if(value.length>100){
                layui.layer.alert("单号过长")
                return;
            }
            $.post( '/logistics/filedeliveryNew/updateLogisticNos.do' ,
                {
                    addressId:ID,
                    logisticNos:value
                }, function(response) {
                    if(response.success){
                        var update = {};
                        update[field] = value;
                        obj.update(update, true);
                    }else{
                        layui.layer.alert(response.message);
                    }

                }, 'json').fail(function () {
                layer.close(loadIndex);
                layui.layer.alert("系统异常");
            });
        })



        $(".print").click(function(){
            try{
               // if(!LODOP){
                    LODOP=getLodop(document.getElementById('LODOP_OB'),document.getElementById('LODOP_EM'));
              //  }
            }catch (e) {
            }
            layer.open({
                type: 1, // page 层类型
                area: ['550px', '500px'],
                title: '打印面单',
                shade: 0, // 遮罩透明度
                shadeClose: false, // 点击遮罩区域，关闭弹层
               // maxmin: true, // 允许全屏最小化
                anim: 0, // 0-6 的动画形式，-1 不开启
                content: $("#printform").html(),
                id: 'print_id',
                success: function(){
                    //根据重量选中
                    if($("[name=deliveryTotalWeight]").val()>=5 && $("#deliveryStatus").val()!=1 ){
                        $('[name=EXPRESS_COMPANY]').val(2);
                    }
                    form.render();
                   // layui.form.render();
                }
                ,btn: ['打印', '取消'],
                btnAlign: 'c', // 按钮居中显示
                btn1: function(){
                    var loadIndex = layer.load(0);
                    //获取所有地址的面单
                    $.post( '/logistics/filedeliveryNew/print.do' ,
                        {
                            fileDeliveryId:$("#fileDeliveryId").val(),
                            printType: $("[name=EXPRESS_COMPANY]").val()
                        }, function(response) {
                            if(response.success){
                                if($("[name=EXPRESS_COMPANY]").val()==4){
                                    layer.close(loadIndex);
                                    layer.alert("操作成功",function(){
                                        location.reload();
                                    })
                                    return;
                                }
                                for(var  i=0;i<response.data.length;i++){
                                    var url=response.data[i];
                                    LODOP.PRINT_INIT("打印控件功能演示_Lodop功能_打印图片1");
                                    if($("[name=EXPRESS_COMPANY]").val()==1||$("[name=EXPRESS_COMPANY]").val()==2){
                                        LODOP.SET_PRINTER_INDEX($("#emsprintname").val());
                                    }else if($("[name=EXPRESS_COMPANY]").val()==3){
                                        LODOP.SET_PRINTER_INDEX($("#sfprintname").val());
                                    }else if($("[name=EXPRESS_COMPANY]").val()==5){
                                        LODOP.SET_PRINTER_INDEX($("#ztprintname").val());
                                    }
                                    LODOP.SET_PRINT_PAGESIZE(1, 760, 1300, "");
                                   // LODOP.ADD_PRINT_IMAGE(1,1,"100%","100%","<IMG style='width: 287.24352px; height: 491.3376px;' SRC='"+url+"'>");
                                    LODOP.ADD_PRINT_URL(0,0,"100%","100%",url);
                                    LODOP.PRINT();
                                }
                                layer.close(loadIndex);
                                layer.alert('打印任务已成功发送给打印机,请在打印完成之后再关闭此窗口',  function(){
                                        location.reload();
                                });
                            }else{
                                layer.close(loadIndex);
                                layui.layer.alert(response.message);
                            }

                        }, 'json').fail(function () {
                        layer.close(loadIndex);
                        layui.layer.alert("系统异常");
                    });
                },
                btn2: function(index, layero, that){
                    layer.close(index);
                    location.reload();
                }
            });
            return false;
        })

    });

</script>
<script type="text/html" id="ID-templet-logisticsNoEdit">
    <div class="layui-input-wrap" style="margin-top: -6px;
    margin-left: -15px;
    margin-right: -15px;">
        <input type="text" data-id="{{= d.ID }}" name="item-logisticsNoEdit" maxlength="100" value="{{= d.name7 }}"  class="layui-input">
        <div class="layui-input-suffix">
            <i class="layui-icon layui-icon-edit"></i>
        </div>
    </div>
</script>

<script src='<%= basePath %>static/js/LodopFuncs.js?rnd1=${resourceVersionKey}'></script>

</body>
</html>