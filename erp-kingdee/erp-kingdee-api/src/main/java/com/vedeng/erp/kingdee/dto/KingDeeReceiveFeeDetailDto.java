package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 费用应收单-明细
 *
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @see KingDeeReceiveFeeDto
 */
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class KingDeeReceiveFeeDetailDto {
    /**
     * 计价数量 填写数量（退货负数）
     */
    private String fPriceQty;
    /**
     * 含税单价 填写单价
     */
    private String fTaxPrice;
    /**
     * 税率%
     */
    private String fEntryTaxRate;
    /**
     * 贝登单据行ID
     */
    private String fQzokBddjhid;

    /**
     * 费用项目编码 填写金蝶的费用项目编码
     */
    private String fcostid;

    /**
     * 金蝶费用应收单行ID
     */
    @WriteBackField(value = "FEntryID", backWriteByErpField = "fQzokBddjhId", backWriteToKingDeeField = "F_QZOK_BDDJHID")
    private String fEntryId;
}
