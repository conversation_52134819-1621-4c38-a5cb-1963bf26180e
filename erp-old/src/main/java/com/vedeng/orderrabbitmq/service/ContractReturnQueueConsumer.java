package com.vedeng.orderrabbitmq.service;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.finance.model.vo.ContractReturnDto;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleContractAutoAuditService;
import com.vedeng.order.service.SaleorderDataSyncService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.model.RTraderJUser;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: daniel
 * @Date: 2020/11/6 09 48
 * @Description:
 */
@Component
public class ContractReturnQueueConsumer extends AbstractMessageListener {

    public static final Logger LOGGER = LoggerFactory.getLogger(ContractReturnQueueConsumer.class);


    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private RTraderJUserMapper rTraderJUserMapper;
    @Autowired
    private AttachmentMapper attachmentMapper;

    @Resource
    private SaleorderDataSyncService saleorderDataSyncService;

    @Resource
    private SaleContractAutoAuditService saleContractAutoAuditService;

    @Resource
    private UserMapper userMapper;

    @Value("${oss_url}")
    private String ossUrl;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        LOGGER.info("前台回传的合同信息：{}",messageBody);
        try {
            ContractReturnDto contractReturnDto = JSONObject.parseObject(messageBody,ContractReturnDto.class);
            if (contractReturnDto == null || StringUtils.isBlank(contractReturnDto.getOrderNo()) ||
                    contractReturnDto.getContractFileList() == null || contractReturnDto.getContractFileList().size() == 0){
                LOGGER.error("前台回传的合同信息有误，合同信息为：{}",messageBody);
                throw new Exception("前台回传的合同信息有误");
            }

            //保存合同回传附件
            Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(contractReturnDto.getOrderNo());
            Integer saleorderId = saleorder.getSaleorderId();
            Saleorder baseSaleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorderId);
            if (saleorder != null) {
                RTraderJUser rTraderJUser = rTraderJUserMapper.getUserByTraderId(saleorder.getTraderId());
                int userId = rTraderJUser == null ? 2 : rTraderJUser.getUserId();
                boolean validAttachment = false;
                LOGGER.info("消费前台回传的合同信息回传合同单号状态:{}",baseSaleorderInfo.getSaleorderNo()+" : "+baseSaleorderInfo.getContractStatus());
                if(baseSaleorderInfo.getContractStatus() != null && baseSaleorderInfo.getContractStatus() == 2){
                    delContract(saleorderId);
                }
                if (baseSaleorderInfo.getContractStatus() == null || baseSaleorderInfo.getContractStatus() == 2){
                    for (String url : contractReturnDto.getContractFileList()){
                        if (saveAttachmentOfContractReturn(url,contractReturnDto.getOrderNo(), saleorderId,messageBody,userId)){
                            validAttachment = true;
                        }
                    }
                    saleorderDataSyncService.syncContractStatusBySaleorderId(saleorderId,"queue");
                    //发送站内信给订单归属销售
                    if (rTraderJUser != null && validAttachment) {
                        Map<String,String> map = new HashMap<>(1);
                        map.put("saleorderNo",saleorder.getSaleorderNo());
                        MessageUtil.sendMessage(144, Collections.singletonList(rTraderJUser.getUserId()),map,"./order/saleorder/view.do?saleorderId="+ saleorderId);
                    }
                    User user = userMapper.getUserByName("njadmin");
                    //开启流程实例
                    saleContractAutoAuditService.startProcessInstance(baseSaleorderInfo,user);
                }

            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
        }catch (Exception e){
            LOGGER.error("消费前台回传的合同信息发生错误，回传合同内容：{}，错误信息：",messageBody,e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
            } catch (IOException ex) {
                LOGGER.error("前台回传的合同信息消费失败，将消息返回给rabbitmq错误：",ex);
            }
        }
    }

    private Boolean saveAttachmentOfContractReturn(String attachmentUrl, String orderNo, Integer saleorderId, String message, Integer userId){
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(461);
        attachment.setAttachmentFunction(492);
        attachment.setRelatedId(saleorderId);
        attachment.setName("销售合同" + orderNo);
        attachment.setDomain(ossUrl);
        String[] uriArray = attachmentUrl.split(ossUrl);
        if (uriArray.length != 2){
            LOGGER.error("前台回传合同解析有误，回传合同信息为：{}",message);
            return false;
        }
        attachment.setUri(uriArray[1]);
        attachment.setAlt("客户回传合同");
        attachment.setAddTime(DateUtil.sysTimeMillis());
        attachment.setCreator(userId);
        int i = attachmentMapper.insertSelective(attachment);
        if(i > 0){
            return true;
        }else {
            return false;
        }
    }

    private void delContract(Integer saleorderId) {
        Attachment update = new Attachment();
        update.setRelatedId(saleorderId);
        update.setAttachmentFunction(SysOptionConstant.ID_492);
        attachmentMapper.updateByAttachment(update);
        if(saleorderId !=null){
            // 将T_VERIFIES_INFO表的数据删除
            LOGGER.info("消费前台回传的合同信息，删除T_VERIFIES_INFO表的数据，saleorderId:{}", saleorderId);
            attachmentMapper.delVerifiesInfo(update);
        }
    }
}
