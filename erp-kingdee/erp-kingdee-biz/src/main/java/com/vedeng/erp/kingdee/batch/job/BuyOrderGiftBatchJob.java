package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.processor.BuyOrderGiftInAcceptanceFormProcessor;
import com.vedeng.erp.kingdee.batch.processor.PurchaseGiftInProcessor;
import com.vedeng.erp.kingdee.batch.writer.CommonFileDataWriter;
import com.vedeng.erp.kingdee.batch.writer.PurchaseGiftInWriter;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageInDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 赠品采购单正向入库流程job
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderGiftBatchJob extends BaseJob {


    @Autowired
    private PurchaseGiftInProcessor purchaseGiftInProcessor;
    @Autowired
    private PurchaseGiftInWriter purchaseGiftInWriter;
    @Autowired
    private BuyOrderGiftInAcceptanceFormProcessor buyOrderGiftInAcceptanceFormProcessor;

    @Autowired
    private BaseProcessListener baseProcessListener;
    @Autowired
    private BaseReadListener baseReadListener;
    @Autowired
    private BaseWriteListener baseWriteListener;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    public Job buyOrderGiftBuyOrderBatchJob() {
        return jobBuilderFactory.get("buyOrderGiftBuyOrderBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(giftBuyOrderIn())
                .next(acceptanceForm())
                .build();
    }


    /**
     * 采购赠品入库
     */
    private Step giftBuyOrderIn() {
        return stepBuilderFactory.get("采购赠品入库")
                .<BatchWarehouseGoodsOutInDto, KingDeeStorageInDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(giftBuyOrderInItemReader(null, null))
                .processor(purchaseGiftInProcessor)
                .writer(purchaseGiftInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step acceptanceForm() {
        return stepBuilderFactory.get("采购赠品入库附件推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(giftBuyOrderInItemReader(null, null))
                .processor(buyOrderGiftInAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> giftBuyOrderInItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(beginTime, endTime, 17);
    }



    private CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> getBatchWarehouseGoodsOutInDtoCommonMybatisItemReader(
            String beginTime, String endTime, Integer outInType) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInType(outInType)
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "giftBuyOrderFindByAll", warehouseGoodsOutInDto);
    }


}

