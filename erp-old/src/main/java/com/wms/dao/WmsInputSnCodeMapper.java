package com.wms.dao;

import com.wms.model.WmsInSnCodeOrder;
import com.wms.model.generate.VWmsInSncodeOrder;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @description:
 * @date 2020/11/1614:32
 */
public interface WmsInputSnCodeMapper{

    int deleteByPrimaryKey(Integer inSncodeOrderId);

    int insert(VWmsInSncodeOrder record);

    int insertSelective(VWmsInSncodeOrder record);

    VWmsInSncodeOrder selectByPrimaryKey(Integer inSncodeOrderId);

    int updateByPrimaryKeySelective(VWmsInSncodeOrder record);

    int updateByPrimaryKey(VWmsInSncodeOrder record);

    WmsInSnCodeOrder getInputSnCode(WmsInSnCodeOrder snCodeOrder);

    int getSnNum(@Param("operateType") Integer operateType, @Param("orderNo") String orderNo);
}
