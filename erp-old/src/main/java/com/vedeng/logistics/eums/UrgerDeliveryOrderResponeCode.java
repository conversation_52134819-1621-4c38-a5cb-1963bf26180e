package com.vedeng.logistics.eums;

/**
 * <AUTHOR>
 * @Description com.vedeng.logistics.exception
 * @Date 2022/4/14 19:54
 */
public enum  UrgerDeliveryOrderResponeCode {


    DISSATISFY_CONDITION(2001,"不满足催办条件"),

    JUST_CAN_CLICK_ONCE(2002,"只可点击一次")

;



    private final int code;

    private final String msg;

    UrgerDeliveryOrderResponeCode(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public int getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }}
