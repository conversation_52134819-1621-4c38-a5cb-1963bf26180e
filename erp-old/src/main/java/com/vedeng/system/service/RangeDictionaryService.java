package com.vedeng.system.service;

import com.vedeng.system.model.RangeDictionary;
import java.util.List;


/**
 * @description: RangeDictionaryService.
 * @notes: VDERP-2336 预计可发货时间接口.
 * @version: 1.0.
 * @date: 2020/5/9 4:24 下午.
 * @author: Tomcat.Hui.
 */
public interface RangeDictionaryService {

    /**
     * @description: 获取所有字典.
     * @jira: VDERP-2336 预计可发货时间接口.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/9 4:24 下午.
     * @author: Tomcat.Hui.

     * @return: java.util.List<com.vedeng.order.domain.model.RangeDictionary>.
     * @throws: .
     */
    public List<RangeDictionary> getAllDicts();
}
