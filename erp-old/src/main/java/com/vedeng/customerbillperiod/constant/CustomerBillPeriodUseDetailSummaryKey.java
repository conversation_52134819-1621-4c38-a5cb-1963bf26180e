package com.vedeng.customerbillperiod.constant;

/**
 * @Author: daniel
 * @Date: 2021/7/30 10 37
 * @Description: 客户账期使用情况的维度
 */
public class CustomerBillPeriodUseDetailSummaryKey {

    /**
     * 可用余额
     */
    public static final Integer AVAILABLE_AMOUNT = 1;

    /**
     * 未归还金额
     */
    public static final Integer UNRETURNED_AMOUNT = 2;

    /**
     * 逾期金额
     */
    public static final Integer OVERDUE_AMOUNT = 3;

    /**
     * 使用次数
     */
    public static final Integer COUNT_OF_USED = 4;

    /**
     * 逾期次数
     */
    public static final Integer COUNT_OF_OVERDUE = 5;
}
