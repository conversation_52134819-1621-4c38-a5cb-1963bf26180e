package com.vedeng.order.model;

import java.io.Serializable;
import java.util.Objects;

/**
 * T_SALEORDER_DELIVERY_NOTICE_GOODS
 * <AUTHOR>
public class SaleorderDeliveryNoticeGoods implements Serializable {
    /**
     * 发货通知单产品id
     */
    private Integer deliveryNoticeGoodsId;

    /**
     * 发货通知单ID
     */
    private Integer deliveryNoticeId;

    /**
     * 订单商品表主键id
     */
    private Integer saleorderGoodsId;

    /**
     * sku
     */
    private String sku;

    /**
     * 产品ID
     */
    private Integer goodsId;


    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 本次发货数量
     */
    private Integer num;

    /**
     * 占用数量
     */
    private Integer occupyNum;

    /**
     * 收货数量
     */
    private Integer arrivalNum;

    /**
     * 删除状态
     */
    private Integer deleteState;

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Integer getSaleorderGoodsId() {
        return saleorderGoodsId;
    }

    public void setSaleorderGoodsId(Integer saleorderGoodsId) {
        this.saleorderGoodsId = saleorderGoodsId;
    }

    public Integer getOccupyNum() {
        return occupyNum;
    }

    public void setOccupyNum(Integer occupyNum) {
        this.occupyNum = occupyNum;
    }

    public Integer getArrivalNum() {
        return arrivalNum;
    }

    public void setArrivalNum(Integer arrivalNum) {
        this.arrivalNum = arrivalNum;
    }

    public Integer getDeleteState() {
        return deleteState;
    }

    public void setDeleteState(Integer deleteState) {
        this.deleteState = deleteState;
    }

    private static final long serialVersionUID = 1L;

    public Integer getDeliveryNoticeGoodsId() {
        return deliveryNoticeGoodsId;
    }

    public void setDeliveryNoticeGoodsId(Integer deliveryNoticeGoodsId) {
        this.deliveryNoticeGoodsId = deliveryNoticeGoodsId;
    }

    public Integer getDeliveryNoticeId() {
        return deliveryNoticeId;
    }

    public void setDeliveryNoticeId(Integer deliveryNoticeId) {
        this.deliveryNoticeId = deliveryNoticeId;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }
}