
Vue.component('rank-card', {
    template: `<div class="vd-ui-rank-card">
        <div class="rank-card-top">
            <div class="card-title">{{ title }}</div>
            <div class="card-filter" v-if="filter">
                <div 
                    class="filter-item" 
                    :class="{'active': group == item.id}"
                    v-for="(item, index) in Groups"
                    @click="filterChange(item)"
                >
                    {{ item.name }}
                </div>
            </div>
        </div>

        <div class="card-empty" v-show="isEmpty">
            <i class="vd-ui_icon icon-info1"></i>
            <div class="card-empty-txt">暂无数据</div>
        </div>
        <div v-show="!isEmpty">
            <div class="rank-card-signature">
                <img :src="bannerImg || '/static/image/broadcast/sign-bg.svg'" />
            </div>
            <div class="rank-card-list" v-if="group == 1">
                <div class="rank_front-three" :class="{center: personalTop3.length == 1}">
                    <template v-for="(item, index) in personalTop3">
                        <div class="front-three-item" :class="'level' + item.level" :key="index">
                            <div class="avatar-wrap">
                                <img :src="item.userAliasHeadPic || '/static/image/crm-user-avatar.svg'" class="img" />
                                <img :src="'/static/image/broadcast/top' + item.level + '.svg'" class="level" />
                            </div>
                            <div class="front-three-cup">
                                <p>{{ item.userName }}</p>
                                <p class="group">{{ item.teamName }}</p>
                                <p class="price">{{ priceKey ? item[priceKey] : item.amount }}</p>
                            </div>
                        </div>
                    </template>
                </div>
                <div class="user-rank-list">
                    <template v-for="(item, index) in data.personal.list">
                        <div class="rank-item" v-if="index >= 3">
                            <div class="num">{{ index + 1 }}</div>
                            <div class="name">{{ item.userName }}</div>
                            <div class="group">{{ item.teamName }}</div>
                            <div class="price">{{ priceKey ? item[priceKey] : item.amount }}</div>
                        </div>
                    </template>
                </div>
            </div>
            <div v-if="group == 2">
                <div class="group-rank-list" :class="{'rank-percent': needPercent}">
                    <template v-for="(item, index) in data.group.list">
                        <div class="rank-item" :class="[{'before-three': index < 3}, 'bg' + index]">
                            <img :src="'/static/image/broadcast/rank' + index + '.svg'" class="rank" v-if="index < 3" />
                            <div class="num" v-else>{{ index + 1 }}</div>
                            <div class="group">{{ item.teamName }}</div>
                            <div class="process-wrap" v-if="needPercent">
                                <div class="process-line">
                                    <div class="process" :style="{width: item.archievedPrecent + '%'}"></div>
                                </div>
                                <div class="process-num">{{ item.archievedPrecent }}%</div> 
                            </div>
                            <div class="price">{{ priceKey ? item[priceKey] : item.amount }}</div>
                        </div>
                    </template>
                </div>
            </div>
            <div  v-if="group == 3">
                <div class="group-rank-list" :class="{'rank-percent': needPercent}">
                    <template v-for="(item, index) in data.department.list">
                        <div class="rank-item" :class="[{'before-three': index < 3}, 'bg' + index]">
                            <img :src="'/static/image/broadcast/rank' + index + '.svg'" class="rank" v-if="index < 3" />
                            <div class="num" v-else>{{ index + 1 }}</div>
                            <div class="group">{{ item.deptName }}</div>
                            <div class="process-wrap" v-if="needPercent">
                                <div class="process-line">
                                    <div class="process" :style="{width: item.archievedPrecent + '%'}"></div>
                                </div>
                                <div class="process-num">{{ item.archievedPrecent }}%</div>
                            </div>
                            <div class="price">{{ priceKey ? item[priceKey] : item.amount }}</div>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>`,
    props: {
        title: {
            type: String,
            default: ''
        },
        filter: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default() {
                return {
                    personal: {
                        picUrl: '',
                        list: []
                    },
                    group: {
                        picUrl: '',
                        list: []
                    },
                    department: {
                        picUrl: '',
                        list: []
                    }
                }
            }
        },
        signature: {
            type: String,
            default: '“客户拒绝只是开始，坚持才能赢得订单”'
        },
        user: {
            type: String,
            default: 'Tina'
        },
        priceKey: {
            type: String,
            default: '' //金额位置展示字段，默认取‘amount’
        },
        needPercent: {
            type: Boolean,
            default: false
        }
    },
    watch: {
    },
    data() {
        return {
            Groups: [
                { id: 1, name: '个人' },
                { id: 2, name: '小组' },
                { id: 3, name: '部门' }
            ],
            group: 1,
            personalTop3: [],
            bannerImg: '',
            isEmpty: false
        };
    },
    computed: {

    },
    created() {
        if (this.data.personal.list && this.data.personal.list.length) {
            if (this.data.personal.list[1]) {
                this.personalTop3.push({
                    level: 2,
                    ...this.data.personal.list[1]
                })
            }

            if (this.data.personal.list[0]) {
                this.personalTop3.push({
                    level: 1,
                    ...this.data.personal.list[0]
                })
            }

            if (this.data.personal.list[2]) {
                this.personalTop3.push({
                    level: 3,
                    ...this.data.personal.list[2]
                })
            }
        }

        this.bannerImg = this.data.personal.picUrl || '';

        if(!(this.data.personal.list && this.data.personal.list.length)) {
            this.isEmpty = true;
        }
    },
    methods: {
        filterChange(item) {
            this.group = item.id;

            let data = this.data[{
                1: 'personal',
                2: 'group',
                3: 'department'
            }[this.group]];

            this.bannerImg = data.picUrl || '';

            if(!(data.list && data.list.length)) {
                this.isEmpty = true;
            } else {
                this.isEmpty = false;
            }
        }
    }
})
