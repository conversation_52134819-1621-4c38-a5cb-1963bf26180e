package com.vedeng.goods.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.SnowFlakeUtils;
import com.vedeng.goods.api.dto.SysOptionDefinition;
import com.vedeng.goods.dao.SkuAuthorizationMapper;
import com.vedeng.goods.model.dto.SkuAuthrizationItemDto;
import com.vedeng.goods.model.dto.SkuAuthrizationRequest;
import com.vedeng.goods.model.entity.SkuAuthorizationRegion;
import com.vedeng.goods.model.entity.SkuAuthorizationTerminalType;
import com.vedeng.goods.model.vo.SkuAuthorizationItemVo;
import com.vedeng.goods.model.vo.SkuAuthorizationVo;
import com.vedeng.goods.service.SkuAuthorizationService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * SKU报备信息
 *
 * <AUTHOR>
 * @date 2020/9/17
 */
@Service("skuAuthorizationService")
public class SkuAuthorizationServiceImpl implements SkuAuthorizationService {
    Logger logger = LoggerFactory.getLogger(SkuAuthorizationServiceImpl.class);
    @Resource
    private SkuAuthorizationMapper skuAuthorizationMapper;

    /**
     * 获取所有的报备信息终端类型
     *
     * @return
     */
    @Override
    public List<SysOptionDefinition> getAllTerminalTypes() {
        return skuAuthorizationMapper.getAllTerminalTypes();
    }

    /**
     * 通过SKU的ID获取sku的报备信息
     *
     * @param skuId
     * @return
     */
    @Override
    public SkuAuthorizationVo getSkuAuthorizationInfoBySkuId(Integer skuId) {
        if (skuId == null) {
            return null;
        }
        HashMap<Long, SkuAuthorizationItemVo> snowFlakeIdAuthrizationHashMap = getLongSkuAuthorizationItemVoHashMap(skuId);

        if (MapUtils.isEmpty(snowFlakeIdAuthrizationHashMap)) {
            return null;
        }

        return new SkuAuthorizationVo(skuId,
                snowFlakeIdAuthrizationHashMap
                .values()
                .stream()
                .sorted(Comparator.comparing(SkuAuthorizationItemVo :: getSnowFlakeId))
                .collect(Collectors.toList()));
    }

    /**
     * 获取SKU的报备对应信息
     *
     * @param skuId
     * @return
     */
    private HashMap<Long, SkuAuthorizationItemVo> getLongSkuAuthorizationItemVoHashMap(Integer skuId) {
        List<SkuAuthrizationItemDto> skuAuthrizationItemDtos = skuAuthorizationMapper.getSkuAuthrizationItemBySkuId(skuId);
        if (CollectionUtils.isEmpty(skuAuthrizationItemDtos)) {
            logger.info("当前SKU不存在报备信息 skuId:{}", skuId);
            return null;
        }

        HashMap<Long, SkuAuthorizationItemVo> snowFlakeIdAuthrizationHashMap = new HashMap<>(16);
        skuAuthrizationItemDtos.stream().forEach(skuAuthrizationItemDto -> {
            if (snowFlakeIdAuthrizationHashMap.containsKey(skuAuthrizationItemDto.getSnowFlakeId())) {
                SkuAuthorizationItemVo skuAuthorizationItemInfo = snowFlakeIdAuthrizationHashMap.get(skuAuthrizationItemDto.getSnowFlakeId());
                if (!skuAuthorizationItemInfo.getRegionIds().contains(skuAuthrizationItemDto.getRegionId())) {
                    skuAuthorizationItemInfo.getRegionIds().add(skuAuthrizationItemDto.getRegionId());
                }
                if (!skuAuthorizationItemInfo.getTerminalTypeIds().contains(skuAuthrizationItemDto.getTerminalTypeId())) {
                    skuAuthorizationItemInfo.getTerminalTypeIds().add(skuAuthrizationItemDto.getTerminalTypeId());
                }
            } else {
                SkuAuthorizationItemVo skuAuthorizationItemVo = new SkuAuthorizationItemVo();
                skuAuthorizationItemVo.setSnowFlakeId(skuAuthrizationItemDto.getSnowFlakeId());
                ArrayList<Integer> regions = new ArrayList<>();
                regions.add(skuAuthrizationItemDto.getRegionId());
                skuAuthorizationItemVo.setRegionIds(regions);

                ArrayList<Integer> terminalTypes = new ArrayList<>();
                terminalTypes.add(skuAuthrizationItemDto.getTerminalTypeId());
                skuAuthorizationItemVo.setTerminalTypeIds(terminalTypes);
                snowFlakeIdAuthrizationHashMap.put(skuAuthrizationItemDto.getSnowFlakeId(), skuAuthorizationItemVo);
            }
        });
        return snowFlakeIdAuthrizationHashMap;
    }

    /**
     * 保存SKU报备信息
     *
     * @param skuAuthrizationRequest
     * @return
     */
    @Override
    public ResultInfo saveSkuAuthrizationInfo(SkuAuthrizationRequest skuAuthrizationRequest, User user) {
        logger.info("保存sku报备信息 skuAuthrizationRequest:{}", JSON.toJSONString(skuAuthrizationRequest));
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        if (skuAuthrizationRequest == null || CollectionUtils.isEmpty(skuAuthrizationRequest.getSkuIds())) {
            logger.error("保存SKU报备信息请求信息不全 skuAuthrizationRequest:{}", skuAuthrizationRequest);
            resultInfo.setMessage("保存SKU报备信息请求信息不全!");
            return resultInfo;
        }

        skuAuthrizationRequest.getSkuIds().forEach(skuId -> {
            HashMap<Long, SkuAuthorizationItemVo> skuAuthorizationItemVoMapBefore = getLongSkuAuthorizationItemVoHashMap(skuId);

            Map<Long, SkuAuthorizationItemVo> skuAuthorizationItemVoMapRequest = null;
            if (CollectionUtils.isNotEmpty(skuAuthrizationRequest.getSkuAuthrizationItems())) {
                skuAuthorizationItemVoMapRequest = skuAuthrizationRequest.getSkuAuthrizationItems().stream()
                        .collect(Collectors.toMap(SkuAuthorizationItemVo::getSnowFlakeId, skuAuthorizationItemVo -> skuAuthorizationItemVo));
            }

            // I 删除所有报备信息情况
            if (MapUtils.isEmpty(skuAuthorizationItemVoMapRequest) && MapUtils.isNotEmpty(skuAuthorizationItemVoMapBefore)) {
                logger.info("删除SKU关联报备信息 skuId:{}", skuId);
                List<Long> snowFlakeIds = skuAuthorizationItemVoMapBefore.values()
                        .stream()
                        .map(item -> item.getSnowFlakeId())
                        .distinct()
                        .collect(Collectors.toList());
                disabledSkuAuthorizationBySnowFlakeIds(snowFlakeIds, user.getUserId());
                return;
            }

            // II 修改或者添加报备信息情况
            for (Map.Entry<Long, SkuAuthorizationItemVo> skuAuthorizationItemVoEntry : skuAuthorizationItemVoMapRequest.entrySet()) {
                if (skuAuthorizationItemVoEntry.getKey() != null && MapUtils.isNotEmpty(skuAuthorizationItemVoMapBefore) &&
                        skuAuthorizationItemVoMapBefore.containsKey(skuAuthorizationItemVoEntry.getKey())) {
                    //1.处理对比地区信息
                    List<Integer> regionIdsBefore = skuAuthorizationItemVoMapBefore.get(skuAuthorizationItemVoEntry.getKey()).getRegionIds();
                    ArrayList<Integer> regionIdsReduce = new ArrayList<>();
                    regionIdsReduce.addAll(regionIdsBefore);

                    List<Integer> regionIdsRequest = skuAuthorizationItemVoEntry.getValue().getRegionIds();
                    ArrayList<Integer> regionIdsAdd = new ArrayList<>();
                    regionIdsAdd.addAll(regionIdsRequest);

                    regionIdsReduce.removeAll(regionIdsRequest);
                    regionIdsAdd.removeAll(regionIdsBefore);

                    regionIdsAdd.forEach(regionIdAdd -> {
                        SkuAuthorizationRegion skuAuthorizationRegion = new SkuAuthorizationRegion(skuId, regionIdAdd,
                                skuAuthorizationItemVoEntry.getKey(), DateUtil.sysTimeMillis(), user.getUserId());
                        skuAuthorizationMapper.insertSkuAuthrizationRegion(skuAuthorizationRegion);
                    });
                    if (CollectionUtils.isNotEmpty(regionIdsReduce)) {
                        skuAuthorizationMapper.deleteSkuAuthrizationRegionByCondition(regionIdsReduce, skuAuthorizationItemVoEntry.getKey(), user.getUserId());
                    }

                    //2.处理报备类型信息
                    List<Integer> terminalTypeIdsBefore = skuAuthorizationItemVoMapBefore.get(skuAuthorizationItemVoEntry
                            .getKey()).getTerminalTypeIds();
                    ArrayList<Integer> terminalTypeIdsReduce = new ArrayList<>();
                    terminalTypeIdsReduce.addAll(terminalTypeIdsBefore);

                    List<Integer> terminalTypeIdsRequest = skuAuthorizationItemVoEntry.getValue().getTerminalTypeIds();
                    ArrayList<Integer> terminalTypeIdsAdd = new ArrayList<>();
                    terminalTypeIdsAdd.addAll(terminalTypeIdsRequest);

                    terminalTypeIdsReduce.removeAll(terminalTypeIdsRequest);
                    terminalTypeIdsAdd.removeAll(terminalTypeIdsBefore);

                    terminalTypeIdsAdd.forEach(terminalTypeIdAdd -> {
                        SkuAuthorizationTerminalType skuAuthorizationTerminalTypePo = new SkuAuthorizationTerminalType(skuId, terminalTypeIdAdd,
                                skuAuthorizationItemVoEntry.getKey(), DateUtil.sysTimeMillis(), user.getUserId());
                        skuAuthorizationMapper.insertSkuAuthrizationTerminalType(skuAuthorizationTerminalTypePo);
                    });

                    if (CollectionUtils.isNotEmpty(terminalTypeIdsReduce)) {
                        skuAuthorizationMapper.deleteSkuAuthrizationTerminalTypeByCondition(terminalTypeIdsReduce,
                                skuAuthorizationItemVoEntry.getKey(), user.getUserId());
                    }
                } else {
                    long snowFlakeId = SnowFlakeUtils.uniqueLong();
                    skuAuthorizationItemVoEntry.getValue().getRegionIds().forEach(regionId -> {
                        SkuAuthorizationRegion skuAuthorizationRegionPo = new SkuAuthorizationRegion(skuId, regionId, snowFlakeId,
                                DateUtil.sysTimeMillis(), user.getUserId());
                        skuAuthorizationMapper.insertSkuAuthrizationRegion(skuAuthorizationRegionPo);
                    });

                    skuAuthorizationItemVoEntry.getValue().getTerminalTypeIds().forEach(terminalTypeId -> {
                        SkuAuthorizationTerminalType skuAuthorizationTerminalTypePo = new SkuAuthorizationTerminalType(skuId, terminalTypeId,
                                snowFlakeId, DateUtil.sysTimeMillis(), user.getUserId());
                        skuAuthorizationMapper.insertSkuAuthrizationTerminalType(skuAuthorizationTerminalTypePo);
                    });
                }
            }

            //III item减少的情况
            if (MapUtils.isEmpty(skuAuthorizationItemVoMapBefore)){
                return;
            }
            ArrayList<Long> snowFlakesReduce = new ArrayList<>(skuAuthorizationItemVoMapBefore.keySet());
            snowFlakesReduce.removeAll(new ArrayList<>(skuAuthorizationItemVoMapRequest.keySet()));

            if (CollectionUtils.isNotEmpty(snowFlakesReduce)) {
                skuAuthorizationMapper.disabledSkuAuthrizationRegionBySnowFlakeIds(snowFlakesReduce, user.getUserId());
                skuAuthorizationMapper.disabledSkuAuthrizationTerminalTypeBySnowFlakeIds(snowFlakesReduce, user.getUserId());
            }
        });

        resultInfo.setCode(0);
        return resultInfo;
    }

    /**
     * 通过雪花ID关联删除报备信息中的项目
     *
     * @param snowFlakeIds
     * @param userId
     */
    private void disabledSkuAuthorizationBySnowFlakeIds(List<Long> snowFlakeIds, Integer userId) {
        skuAuthorizationMapper.disabledSkuAuthrizationRegionBySnowFlakeIds(snowFlakeIds, userId);
        skuAuthorizationMapper.disabledSkuAuthrizationTerminalTypeBySnowFlakeIds(snowFlakeIds, userId);
    }

}
