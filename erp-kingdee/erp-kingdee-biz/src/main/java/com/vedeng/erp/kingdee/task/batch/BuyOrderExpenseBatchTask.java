package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BuyOrderExpenseBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 采购费用单正向入库流程job
 * <AUTHOR>
 */
@JobHandler(value = "BuyOrderExpenseBatchTask")
@Component
public class BuyOrderExpenseBatchTask extends AbstractJobHandler {

    @Autowired
    private BuyOrderExpenseBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================BuyOrderExpenseBatchTask开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.buyOrderExpenseFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================BuyOrderExpenseBatchTask结束====================");
        return SUCCESS;
    }


}

