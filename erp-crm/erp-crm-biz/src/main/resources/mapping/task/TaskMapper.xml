<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.task.mapper.TaskMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.crm.task.domain.entity.TaskEntity">
    <!--@mbg.generated-->
    <!--@Table T_TASK-->
    <id column="TASK_ID" jdbcType="BIGINT" property="taskId" />
    <result column="TASK_CONTENT" jdbcType="VARCHAR" property="taskContent" />
    <result column="BIZ_TYPE" jdbcType="INTEGER" property="bizType" />
    <result column="BIZ_ID" jdbcType="INTEGER" property="bizId" />
    <result column="MAIN_TASK_TYPE" jdbcType="INTEGER" property="mainTaskType" />
    <result column="SUB_TASK_TYPE" jdbcType="VARCHAR" property="subTaskType" />
    <result column="DONE_STATUS" jdbcType="INTEGER" property="doneStatus" />
    <result column="COMMIT_TIME" jdbcType="TIMESTAMP" property="commitTime" />
    <result column="DEADLINE" jdbcType="TIMESTAMP" property="deadline" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="BIZ_NO" jdbcType="VARCHAR" property="bizNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TASK_ID, TASK_CONTENT, BIZ_TYPE, BIZ_ID, MAIN_TASK_TYPE, SUB_TASK_TYPE, DONE_STATUS,
    COMMIT_TIME, DEADLINE, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER,
    UPDATER_NAME, UPDATE_REMARK, BIZ_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_TASK
    where TASK_ID = #{taskId,jdbcType=BIGINT}
  </select>




    <select id="findTaskEntityForVisit"   resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_TASK
        where BIZ_ID=#{bizId,jdbcType=INTEGER} and MAIN_TASK_TYPE=#{mainTaskType,jdbcType=INTEGER}
        limit 1
    </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_TASK
    where TASK_ID = #{taskId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="TASK_ID" keyProperty="taskId" parameterType="com.vedeng.crm.task.domain.entity.TaskEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TASK (TASK_CONTENT, BIZ_TYPE, BIZ_ID,
      MAIN_TASK_TYPE, SUB_TASK_TYPE, DONE_STATUS,
      COMMIT_TIME, DEADLINE, IS_DELETE,
      ADD_TIME, MOD_TIME, CREATOR,
      CREATOR_NAME, UPDATER, UPDATER_NAME,
      UPDATE_REMARK, BIZ_NO)
    values (#{taskContent,jdbcType=VARCHAR}, #{bizType,jdbcType=INTEGER}, #{bizId,jdbcType=INTEGER},
      #{mainTaskType,jdbcType=INTEGER}, #{subTaskType,jdbcType=VARCHAR}, #{doneStatus,jdbcType=INTEGER},
      #{commitTime,jdbcType=TIMESTAMP}, #{deadline,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=INTEGER},
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR}, #{bizNo,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TASK_ID" keyProperty="taskId" parameterType="com.vedeng.crm.task.domain.entity.TaskEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TASK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="taskContent != null and taskContent != ''">
        TASK_CONTENT,
      </if>
      <if test="bizType != null">
        BIZ_TYPE,
      </if>
      <if test="bizId != null">
        BIZ_ID,
      </if>
      <if test="mainTaskType != null">
        MAIN_TASK_TYPE,
      </if>
      <if test="subTaskType != null and subTaskType != ''">
        SUB_TASK_TYPE,
      </if>
      <if test="doneStatus != null">
        DONE_STATUS,
      </if>
      <if test="commitTime != null">
        COMMIT_TIME,
      </if>
      <if test="deadline != null">
        DEADLINE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
      <if test="bizNo != null and bizNo != ''">
        BIZ_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="taskContent != null and taskContent != ''">
        #{taskContent,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        #{bizId,jdbcType=INTEGER},
      </if>
      <if test="mainTaskType != null">
        #{mainTaskType,jdbcType=INTEGER},
      </if>
      <if test="subTaskType != null and subTaskType != ''">
        #{subTaskType,jdbcType=VARCHAR},
      </if>
      <if test="doneStatus != null">
        #{doneStatus,jdbcType=INTEGER},
      </if>
      <if test="commitTime != null">
        #{commitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadline != null">
        #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null and bizNo != ''">
        #{bizNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.crm.task.domain.entity.TaskEntity">
    <!--@mbg.generated-->
    update T_TASK
    <set>
      <if test="taskContent != null and taskContent != ''">
        TASK_CONTENT = #{taskContent,jdbcType=VARCHAR},
      </if>
      <if test="bizType != null">
        BIZ_TYPE = #{bizType,jdbcType=INTEGER},
      </if>
      <if test="bizId != null">
        BIZ_ID = #{bizId,jdbcType=INTEGER},
      </if>
      <if test="mainTaskType != null">
        MAIN_TASK_TYPE = #{mainTaskType,jdbcType=INTEGER},
      </if>
      <if test="subTaskType != null and subTaskType != ''">
        SUB_TASK_TYPE = #{subTaskType,jdbcType=VARCHAR},
      </if>
      <if test="doneStatus != null">
        DONE_STATUS = #{doneStatus,jdbcType=INTEGER},
      </if>
      <if test="commitTime != null">
        COMMIT_TIME = #{commitTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deadline != null">
        DEADLINE = #{deadline,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="bizNo != null and bizNo != ''">
        BIZ_NO = #{bizNo,jdbcType=VARCHAR},
      </if>
    </set>
    where TASK_ID = #{taskId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.crm.task.domain.entity.TaskEntity">
    <!--@mbg.generated-->
    update T_TASK
    set TASK_CONTENT = #{taskContent,jdbcType=VARCHAR},
      BIZ_TYPE = #{bizType,jdbcType=INTEGER},
      BIZ_ID = #{bizId,jdbcType=INTEGER},
      MAIN_TASK_TYPE = #{mainTaskType,jdbcType=INTEGER},
      SUB_TASK_TYPE = #{subTaskType,jdbcType=VARCHAR},
      DONE_STATUS = #{doneStatus,jdbcType=INTEGER},
      COMMIT_TIME = #{commitTime,jdbcType=TIMESTAMP},
      DEADLINE = #{deadline,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      BIZ_NO = #{bizNo,jdbcType=VARCHAR}
    where TASK_ID = #{taskId,jdbcType=BIGINT}
  </update>

    <!--auto generated by MybatisCodeHelper on 2024-07-30-->
    <select id="findMyTask" resultType="com.vedeng.crm.task.domain.vo.MyTaskVo">
        select TTI.TASK_ITEM_ID,
               TTI.TASK_USER taskUserId,
               TTI.DONE_USER doneUserId,
               TTI.DONE_TIME,
               TTI.DONE_REMARK,
               CASE
                   WHEN #{listType} = 1 THEN TTI.DONE_STATUS
                   ELSE TT.DONE_STATUS
                   END as    DONE_STATUS,
               TT.TASK_ID,
               TT.TASK_CONTENT,
               TT.MAIN_TASK_TYPE,
               TT.SUB_TASK_TYPE,
               TT.COMMIT_TIME,
               TT.DEADLINE,
               TT.BIZ_ID,
               TT.BIZ_NO,
               TT.BIZ_TYPE,
               TT.CREATOR    creatorId
        from T_TASK_ITEM TTI
                 left join T_TASK TT on TTI.TASK_ID = TT.TASK_ID
        <where>
            TTI.IS_DELETE=0
            <if test="taskId != null">
                and TT.TASK_ID = #{taskId}
            </if>
            <if test="taskItemId != null">
                and TTI.TASK_ITEM_ID = #{taskItemId}
            </if>

            <if test="bizList != null and bizList.size() != 0">
                and (BIZ_TYPE, BIZ_ID) IN
                <foreach close=")" collection="bizList" item="biz" open="(" separator=",">
                    (#{biz.bizType}, #{biz.bizId})
                </foreach>
            </if>

            <if test="bizType != null">
                and TT.BIZ_TYPE = #{bizType}
            </if>
            <if test="bizId != null">
                and TT.BIZ_ID = #{bizId}
            </if>


            <if test="bizNo != null and bizNo != ''">
                and TT.BIZ_NO like concat('%', #{bizNo}, '%')
            </if>
            <if test="creatorIdList != null and creatorIdList.size() != 0">
                and TT.CREATOR in
                <foreach close=")" collection="creatorIdList" item="creatorId" open="(" separator=",">
                    #{creatorId}
                </foreach>
            </if>
            <if test="todoUserIdList != null and todoUserIdList.size() != 0">
                and TTI.TASK_USER in
                <foreach close=")" collection="todoUserIdList" item="todoUserId" open="(" separator=",">
                    #{todoUserId}
                </foreach>
            </if>
            <if test="mainTaskType != null">
                and TT.MAIN_TASK_TYPE = #{mainTaskType}
            </if>
            <if test="mainTaskTypeList != null and mainTaskTypeList.size() != 0">
                and TT.MAIN_TASK_TYPE in
                <foreach close=")" collection="mainTaskTypeList" item="mainTaskType" open="(" separator=",">
                    #{mainTaskType}
                </foreach>
            </if>
            <if test="subTaskTypListe != null and subTaskTypListe.size() != 0">
                and TT.SUB_TASK_TYPE in
                <foreach close=")" collection="subTaskTypListe" item="subTaskType" open="(" separator=",">
                    #{subTaskType}
                </foreach>
            </if>
            <if test="subTaskType != null">
                and TT.SUB_TASK_TYPE = #{subTaskType}
            </if>

            <if test="listType != null and listType == 2">
                <if test="doneStatus != null">
                    and TT.DONE_STATUS = #{doneStatus}
                </if>
            </if>
            <if test="listType != null and listType == 1">
                <if test="doneStatus != null">
                    and TTI.DONE_STATUS = #{doneStatus}
                </if>
            </if>

            <if test="taskContent != null and taskContent != ''">
                and TT.TASK_CONTENT like concat('%', #{taskContent}, '%')
            </if>



            <if test="startDeadline != null">
                and TT.DEADLINE <![CDATA[ >= ]]> #{startDeadline}
            </if>
            <if test="endDeadline != null">
                and TT.DEADLINE <![CDATA[ <= ]]> #{endDeadline}
            </if>

            <if test="startCommitTime != null">
                and TT.COMMIT_TIME <![CDATA[ >= ]]> #{startCommitTime}
            </if>
            <if test="endCommitTime != null">
                and TT.COMMIT_TIME <![CDATA[ <= ]]> #{endCommitTime}
            </if>

            <if test="overdueStatus != null">
                <!-- 超时状态 -->
                <if test="overdueStatus == 1">
                    and TT.DEADLINE &gt; NOW()
                </if>
                <if test="overdueStatus == 2">
                    and TT.DEADLINE &lt; NOW()
                </if>
            </if>
        </where>
        <if test="listType != null and listType == 2">
            group by TT.TASK_ID
        </if>
    </select>

    <!--auto generated by MybatisCodeHelper on 2024-07-30-->
    <select id="findBussinessChanceProcessTask" resultType="com.vedeng.crm.task.domain.vo.TaskGroupSub">
        <![CDATA[
        SELECT ti.TASK_USER as userId,U.USERNAME, COUNT(t.TASK_ID) AS taskCount, U.NUMBER
        FROM T_TASK t
                 JOIN T_TASK_ITEM ti ON t.TASK_ID = ti.TASK_ID
                 JOIN T_USER U ON ti.TASK_USER = U.USER_ID
        WHERE t.DEADLINE <= CURDATE()
          AND t.DONE_STATUS = 0
          AND t.IS_DELETE = 0
          AND t.MAIN_TASK_TYPE = 5
        GROUP BY ti.TASK_USER
        ]]>
    </select>


    <select id="getTaskForBussinessChance" resultType="java.lang.Integer">
        <![CDATA[
        SELECT COUNT(1)
        from T_TASK
        WHERE BIZ_ID=#{bizId,jdbcType=INTEGER}
            AND DONE_STATUS = 0
            AND IS_DELETE = 0
            and MAIN_TASK_TYPE in (2,3,4)
        ]]>
    </select>




    <select id="getTaskCount" resultType="java.lang.Integer">
        select count(1)
        from T_TASK_ITEM a
                 left join T_TASK b on a.TASK_ID = b.TASK_ID
        where b.IS_DELETE = 0
          and a.IS_DELETE = 0
          and a.DONE_STATUS = 0
          and b.BIZ_ID = #{bizId,jdbcType=INTEGER}
          and b.BIZ_TYPE = #{bizType,jdbcType=INTEGER}
          and a.TASK_USER = #{todoUserId,jdbcType=INTEGER}
    </select>
</mapper>