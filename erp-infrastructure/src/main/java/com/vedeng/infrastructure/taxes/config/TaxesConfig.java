package com.vedeng.infrastructure.taxes.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 税金相关配置
 */
@Component
@Data
public class TaxesConfig {

    /**
     * 企业密钥 由江苏航信分配
     */
    @Value("${taxes.appSecret}")
    public String appSecret;

    /**
     * 企业唯一标识 由江苏航信分配
     */
    @Value("${taxes.appId}")
    public String appId;

    /**
     * 	应用代码
     */
    @Value("${taxes.appCode}")
    public String appCode;

    /**
     * 税金系统访问路径
     */
    @Value("${taxes.url}")
    public String taxesUrl;

    /**
     * 纳税人识别号
     */
    @Value("${taxes.taxNo}")
    public String taxNo;

    /**
     * 企业名称
     */
    @Value("${taxes.taxName}")
    public String taxName;

    /**
     * 版本号
     */
    @Value("${taxes.taxVersion}")
    public String taxVersion;

    /**
     * 超时时间
     */
    @Value("${taxes.ConnectTimeout}")
    public int taxesConnectTimeout;

    /**
     * 从连接池获取连接的超时时间
     */
    @Value("${taxes.ConnectionRequestTimeout}")
    public int taxesConnectionRequestTimeout;

    /**
     * 客户端从服务器读取数据的超时时间
     */
    @Value("${taxes.SocketTimeout}")
    public int taxesSocketTimeout;

    /**
     * 重试次数
     */
    @Value("${taxes.RetryNum}")
    public int retryNum;

    /**
     * 开启邮件
     */
    @Value("${taxes.openInvoiceSendEmail}")
    public Boolean openInvoiceSendEmail;

    /**
     * 销项票拼接路径
     */
    public String saleUrl = "openapi/allElectricInfo";

    /**
     * 公共拼接路径
     */
    public String commonUrl = "openapi/common/commonapi";

    /**
     * 单位地址
     */
    public String address = "南京市秦淮区永顺路2号2幢三楼301室";

    /**
     *  电话号码
     */
    public String telephone = "025-********";

    /**
     * 开户银行
     */
    public String bankName = "建设银行南京市中山南路支行";

    /**
     * 银行账户
     */
    public String bankAccount = "32001881236052503686";


    /**
     * 邮件模板
     */
    public String invoiceEmailTemple ="<style class=\"fox_global_style\">div.fox_html_content {\n" +
            "    line-height: 1.5;\n" +
            "}</style>\n" +
            "<div style=\"text-align: center;\"><b style=\"background-color: transparent; font-size: 24px;\">您的发票已开出</b></div>\n" +
            "<div style=\"text-align: center;\"><span style=\"background-color: transparent;\">请点击下方链接查看并下载</span></div>\n" +
            "<div style=\"text-align: left;\">尊敬的客户：</div>\n" +
            "<div style=\"text-align: left;\"><br></div>\n" +
            "<div style=\"text-align: left;\">感谢您选择贝登，您的订单已开出发票</div>\n" +
            "<div style=\"text-align: left;\">订单单号：{}</div>\n" +
            "<div style=\"text-align: left;\">发票链接：<span style=\"background-color: transparent;\"><a\n" +
            "        href=\"{}\" class=\"\">{}</a></span>\n" +
            "</div>\n" +
            "<div style=\"text-align: left;\">发票号码：{}，开票日期：{}</div>\n" +
            "<div style=\"text-align: left;\"><br></div>\n" +
            "<div style=\"text-align: left;\">若以上链接失效，请登录：<span style=\"background-color: rgba(0, 0, 0, 0); color: rgb(0, 0, 0);\"><a\n" +
            "        href=\"https://inv-veri.chinataxgov.cn/\">https://inv-veri.chinataxgov.cn/</a></span><span\n" +
            "        style=\"background-color: transparent;\">查询</span></div>\n" +
            "<div style=\"text-align: left;\"><span microsoft=\"\" yahei\";=\"\" font-size:=\"\" medium;=\"\" color:=\"\" rgb(0,=\"\" 0,=\"\" 0);=\"\"\n" +
            "    background-color:=\"\" rgba(0,=\"\" font-weight:=\"\" normal;=\"\" font-style:=\"\" normal;text-decoration:=\"\" none;\"=\"\">&nbsp;\n" +
            "    &nbsp;&nbsp;</span></div>\n" +
            "<div style=\"text-align: left;\"><img style=\"width: 200px;\" src=\"https://file.vedeng.com/file/961456859a4beb942d9f7c30106a6dfc.jpg\" alt=\"贝登医疗-医疗器械B2B电商平台\" data-v-b75d21c0=\"\"></div>\n" +
            "<div style=\"text-align: left;\">\n" +
            "    <div style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, sans-serif;\">\n" +
            "        <span style=\"line-height: 1.6; color: navy; font-family: Arial, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, &quot;Microsoft YaHei&quot;, 微软雅黑, sans-serif;\"><span\n" +
            "                style=\"line-height: 1.5;\"><span style=\"font-weight: bolder;\">南京贝登医疗股份有限公司</span></span></span></div>\n" +
            "    <div style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, sans-serif;\">\n" +
            "        <span class=\"\"\n" +
            "              style=\"background-color: window; color: navy; font-family: Arial, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, &quot;Microsoft YaHei&quot;, 微软雅黑, sans-serif; height: auto !important;\">地址：</span><span\n" +
            "            class=\"\"\n" +
            "            style=\"background-color: window; color: navy; font-family: Arial, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, &quot;Microsoft YaHei&quot;, 微软雅黑, sans-serif; line-height: 1.5;\">江苏省南京市秦淮区白下高新技术产业园</span><span\n" +
            "            class=\"\"\n" +
            "            style=\"background-color: window; color: navy; font-family: Arial, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, &quot;Microsoft YaHei&quot;, 微软雅黑, sans-serif; line-height: 1.5;\">紫霞路斯坦德电子商务大厦北楼3楼</span>\n" +
            "    </div>\n" +
            "    <div style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, sans-serif;\">\n" +
            "        <span class=\"\"\n" +
            "              style=\"background-color: window; color: navy; font-family: Arial, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, &quot;Microsoft YaHei&quot;, 微软雅黑, sans-serif; line-height: 1.5;\">电话：</span><span\n" +
            "            class=\"\"\n" +
            "            style=\"background-color: window; color: navy; font-family: Arial, -apple-system, BlinkMacSystemFont, &quot;PingFang SC&quot;, 苹方-简, &quot;Microsoft YaHei&quot;, 微软雅黑, sans-serif; line-height: 22px;\">4006-999-569</span>\n" +
            "    </div>\n" +
            "</div>";

    public  final String INVOICE_WX_MSG_TEMPLATE = "<font color=\"warning\">** 开票完成通知 **</font>\n您申请的订单{}的开票申请已开票完成，并已通过短信和邮件方式交付客户\n发票号码：{}\n收票联系人：{}\n收票手机：{}\n收票邮箱：{}";


    public  final String TAX_OFFICE_LOGIN_STATUS_CHECK_TEMPLATE = "<font color=\"warning\">** 税务局登录失效通知 **</font>\n税务局登录状态已失效\n税号：{}";
}
