package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SerialNumberTraceBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 换货、安调更新金蝶sn码
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/28 22:11
 */
@JobHandler(value = "SerialNumberTraceBatchTask")
@Component
public class SerialNumberTraceBatchTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private SerialNumberTraceBatchJob serialNumberTraceBatchJob;

    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================sn码关联表数据调用金蝶流程batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = serialNumberTraceBatchJob.serialNumberTraceSnBatchJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================sn码关联表数据调用金蝶流程batch结束====================");
        return SUCCESS;
    }
}
