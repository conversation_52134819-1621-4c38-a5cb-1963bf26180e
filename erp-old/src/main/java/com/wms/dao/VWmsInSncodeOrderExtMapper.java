package com.wms.dao;

import com.wms.dao.generate.VWmsInSncodeOrderMapper;
import com.wms.model.ddi.DdiBuyorderExtDto;
import com.wms.model.ddi.VWmsInSncodeOrderExtDto;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

public interface VWmsInSncodeOrderExtMapper extends VWmsInSncodeOrderMapper {

    List<VWmsInSncodeOrderExtDto> getAlltodayOrder(@Param("skuList") List<String> skuList , @Param("startTime") Timestamp startTime , @Param("endTime") Timestamp endTime);

    List<DdiBuyorderExtDto> getSingleOrder(VWmsInSncodeOrderExtDto VWmsInSncodeOrderExtDto);
}