package com.vedeng.infrastructure.kingdee.common.sdk.entity;


/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/RepoRet.class */
public class RepoRet {
    RepoResult Result;

    public boolean isSuccessfully() {
        if (this.Result == null || this.Result.getResponseStatus() == null) {
            return false;
        }
        return this.Result.getResponseStatus().isIsSuccess();
    }

    public RepoResult getResult() {
        return this.Result;
    }

    public void setResult(RepoResult result) {
        this.Result = result;
    }
}
