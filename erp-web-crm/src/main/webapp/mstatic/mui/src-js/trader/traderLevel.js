/* 客户等级 */
Vue.component('ui-trader-level', {
    template: `
        <div class="ui-trader-level-icon" v-if="level"
            :class="['level-' + level]"
            :style="{
                'width': size,
                'height': size,
                'margin': margin
            }"
        ></div>
    `,
    
    props: {
        level: {
            type: String,
        },
        size: {
            type: String,
            default: '20px'
        },
        margin: {
            type: String,
            default: '0'
        }
    },
    watch: {
    },
    data() {
        return {};
    },
    mounted() {
        
    },
    methods: {
    }
})