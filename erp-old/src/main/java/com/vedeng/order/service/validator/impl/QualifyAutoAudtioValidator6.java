package com.vedeng.order.service.validator.impl;

import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.service.validator.dto.ManageCategoryLevel;
import com.vedeng.order.service.validator.dto.QualifyAutoAudtioException;
import com.vedeng.order.service.validator.dto.RegistrationCategory;
import org.springframework.stereotype.Service;

/**
 * match:
 * 若采购订单产品【"是否有注册证/备案凭证"字段为"有"，注册证类型为"国产备案"，注册人名称与供货商名称不一致，管理类别为"一类"】
 * validator:
 * 该供应商须满足以下资质条件才能自动审核通过：
 * 1）	营业执照已上传，
 * 2）	执照在有效期内
 * 3）	营业执照经营范围含医疗器械
 * 备注输出为：订单中产品为一类医疗器械，在供应商营业执照经营范围内
 */
@Service
public class QualifyAutoAudtioValidator6 extends AbstractQualifyAutoAudtioValidator{

    @Override
    public boolean isMatch(BuyorderGoodsVo buyOrderGoods) {

        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(buyOrderGoods.getSku());

        //获取SKU的注册证信息
        RegistrationNumber registrationNumber = super.getRegisterCertificateInfo(buyOrderGoods.getSku());

        boolean isNameMatch = super.isRegisterNameMatchTraderName(buyOrderGoods.getSku());

        return hasRegistrationCert
                && registrationNumber.getCategory() == RegistrationCategory.DOMESTIC_BACKUP
                && !isNameMatch
                && registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.FIRST_CATEGORY;
    }

    @Override
    public String validator(BuyorderGoodsVo buyOrderGoods) throws QualifyAutoAudtioException {

        Integer traderId = super.getTradeInfo(buyOrderGoods).getTraderId();

        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        if(!super.isBusinessMedical(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照不包含医疗器械经营范围");
        }

        return "订单中产品为一类医疗器械，在供应商营业执照经营范围内";
    }
    
    @Override
    public boolean isMatch(Integer traderId, String skuNo) {
        //是否有注册证
        boolean hasRegistrationCert = super.hasRegistrationCert(skuNo);

        //获取SKU的注册证信息
        RegistrationNumber registrationNumber = super.getRegisterCertificateInfo(skuNo);

        boolean isNameMatch = super.isRegisterNameMatchTraderName(skuNo);

        return hasRegistrationCert
                && registrationNumber.getCategory() == RegistrationCategory.DOMESTIC_BACKUP
                && !isNameMatch
                && registrationNumber.getManageCategoryLevel() == ManageCategoryLevel.FIRST_CATEGORY;
    }

    @Override
    public String validator(Integer traderId, String skuNo) throws QualifyAutoAudtioException {
        if(!super.isBusinessLicenseUpload(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照缺失");
        }

        if(!super.isBusinessLicenseInValidityPeriod(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照过期");
        }

        if(!super.isBusinessMedical(traderId)){
            throw new QualifyAutoAudtioException("该供应商的营业执照不包含医疗器械经营范围");
        }

        return "订单中产品为一类医疗器械，在供应商营业执照经营范围内";
    }
}
