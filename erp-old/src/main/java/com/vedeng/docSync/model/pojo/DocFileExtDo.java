package com.vedeng.docSync.model.pojo;

import com.vedeng.docSync.model.pojo.generate.DocFileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * T_DOC_FILE
 * <AUTHOR>
@Data
public class DocFileExtDo extends DocFileDo {

    private String originLink;

    private Long syncTime;

    /**
     * 同步掌上小贝链接，从erp传入的直接取originLink
     */
    private String ossLinkMobile;

    /**
     * 是否已上传至oss 1:已上传 0:未上传（掌上小贝）
     */
    private Boolean syncOssMobile;

}