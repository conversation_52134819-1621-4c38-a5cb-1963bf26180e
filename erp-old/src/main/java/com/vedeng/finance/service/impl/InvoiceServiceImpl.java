package com.vedeng.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.fasterxml.jackson.core.type.TypeReference;
import com.newtask.data.dao.SaleorderDataMapper;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.key.CryptBase64Tool;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.*;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodOverdueManageDetailTypeEnum;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.dto.ReturnBuyorderInvoiceDto;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.dto.InvoiceSaveSearchDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.service.*;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatSpecialInvoiceDto;
import com.vedeng.erp.kingdee.service.KingDeeIFPInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeIFSInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeePVPInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeePVSInvoiceApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.finance.constant.HxInvoiceConstant;
import com.vedeng.finance.dao.*;
import com.vedeng.finance.dto.*;
import com.vedeng.finance.dto.InvoiceApplyReasonSnapshotDto;
import com.vedeng.finance.dto.InvoiceDetailDto;
import com.vedeng.finance.enums.HxInvoiceStatusEnum;
import com.vedeng.finance.enums.InvoiceAuthStatusEnum;
import com.vedeng.finance.enums.InvoiceTypeEnum;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.po.HxInvoiceJBuyorderGoodsPO;
import com.vedeng.finance.model.vo.HxInvoiceConfigVo;
import com.vedeng.finance.model.vo.InvoiceApplyError;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.vo.HxInvoiceDetailVo;
import com.vedeng.finance.vo.HxInvoiceVo;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;
import com.vedeng.goods.model.GoodsAttachment;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.vedeng.infrastructure.sms.constants.SmsTmpConstant;
import com.vedeng.infrastructure.sms.service.SmsService;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.dto.BuyorderGoodsRecordDTO;
import com.vedeng.order.model.dto.BuyorderSearchDTO;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.OrderAccountPeriodService;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.wms.dto.WmsInvoiceInfoDto;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;
import org.thymeleaf.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.collectingAndThen;
import static java.util.stream.Collectors.toCollection;

@Service("invoiceService")
public class InvoiceServiceImpl extends BaseServiceimpl implements InvoiceService {

    @Resource
    private UserMapper userMapper;

    @Autowired
    @Qualifier("invoiceMapper")
    private InvoiceMapper invoiceMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private WebAccountMapper webAccountMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderDataMapper saleorderDataMapper;

    @Autowired
    @Qualifier("invoiceApplyMapper")
    private InvoiceApplyMapper invoiceApplyMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    @Autowired
    private AfterSalesService afterSalesService;

    @Autowired
    private InvoiceDetailMapper invoiceDetailMapper;

    @Autowired
    private HxInvoiceMapper hxInvoiceMapper;

    @Value("${invoice_goods_orderType}")
    private String invoiceGoodsOrderType;

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;

    @Autowired
    private TaxesConfig taxesConfig;
    @Resource
    private HxInvoiceConfigMapper hxInvoiceConfigMapper;

    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Autowired
    private ErpMsgProducer erpMsgProducer;

    @Autowired
    private InvoiceReversalApiService invoiceReversalApiService;

    @Autowired
    private BuyorderInfoSyncService buyorderInfoSyncService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private TraderMapper traderMapper;

    @Autowired
    private KingDeePVSInvoiceApiService kingDeePVSInvoiceApiService;

    @Autowired
    private KingDeePVPInvoiceApiService kingDeePVPInvoiceApiService;

    @Autowired
    private KingDeeIFSInvoiceApiService kingDeeIFSInvoiceApiService;

    @Autowired
    private KingDeeIFPInvoiceApiService kingDeeIFPInvoiceApiService;

    @Autowired
    private InvoiceVoucherApiService invoiceVoucherApiService;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Value("${is_production_environment}")
    private Integer isProductionEnvironment;

    @Autowired
    private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;


    @Autowired
    private UserWorkApiService userWorkApiService;

    /**
     * 记录日志
     */
    public static Logger LOG = LoggerFactory.getLogger(InvoiceServiceImpl.class);

    @Resource
    private AfterSalesMapper afterSalesMapper;


    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;
    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Value("${invoice_position_limit}")
    private String invoicePositionLimit;

    @Autowired
    private OrderPeerApiService orderPeerApiService;

    @Autowired
    private InvoiceCheckApiService invoiceCheckApiService;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;


    @Override
    public Map<String, Object> getInvoiceListByBuyorder(BuyorderVo bo, Invoice invoice, Integer inputInvoiceType)
            throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        if (inputInvoiceType.equals(2)) {// 售后
            invoice.setType(SysOptionConstant.ID_504);
            AfterSalesGoodsVo asv = new AfterSalesGoodsVo();
            asv.setCompanyId(bo.getCompanyId());
            asv.setModel(bo.getModel());
            asv.setTraderName(bo.getTraderName());
            asv.setGoodsName(bo.getGoodsName());
            asv.setBrandName(bo.getBrandName());
            asv.setBuyorderNo(bo.getBuyorderNo());
            asv.setInvoiceType(bo.getInvoiceType());
            List<AfterSalesVo> afterOrderList = null;
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<AfterSalesVo>>> TypeRef =
                    new TypeReference<ResultInfo<List<AfterSalesVo>>>() {};
            String url = httpUrl + "finance/invoice/getafterinvoiceorderlist.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, asv, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                afterOrderList = (List<AfterSalesVo>) result.getData();
            }
            List<Integer> list = new ArrayList<>();
            if (afterOrderList != null && !afterOrderList.isEmpty()) {
                for (int i = 0; i < afterOrderList.size(); i++) {
                    list.add(afterOrderList.get(i).getAfterSalesId());
                }
                invoice.setRelatedIdList(list);
                // 定义反序列化 数据格式
                final TypeReference<ResultInfo<List<InvoiceDetail>>> TypeRef2 =
                        new TypeReference<ResultInfo<List<InvoiceDetail>>>() {};
                String url2 = httpUrl + "finance/invoice/getinvoicelistbyrelatedid.htm";
                invoice.setTag(2);// 录票/开票 1开票 2录票
                ResultInfo<?> result2 =
                        (ResultInfo<?>) HttpClientUtils.post(url2, invoice, clientId, clientKey, TypeRef2);
                if (result2 != null && result2.getCode() == 0) {
                    List<InvoiceDetail> invoiceDetailList = (List<InvoiceDetail>) result2.getData();
                    map.put("invoiceDetailList", invoiceDetailList);
                    if (invoiceDetailList != null && afterOrderList != null) {
                        for (int i = 0; i < invoiceDetailList.size(); i++) {
                            for (int j = afterOrderList.size() - 1; j >= 0; j--) {
                                int size = afterOrderList.get(j).getAfterSalesGoodsList().size();
                                for (int m = 0; m < size; m++) {
                                    if (invoiceDetailList.get(i).getRelatedId().intValue() == afterOrderList.get(j)
                                            .getAfterSalesId().intValue()
                                            && invoiceDetailList.get(i).getDetailgoodsId().intValue() == afterOrderList
                                                    .get(j).getAfterSalesGoodsList().get(m).getAfterSalesGoodsId()
                                                    .intValue()) {
                                        if (invoiceDetailList.get(i).getNum().doubleValue() == afterOrderList.get(j)
                                                .getAfterSalesGoodsList().get(m).getNum().doubleValue()) {
                                            afterOrderList.remove(j);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    //采购售后费用单逻辑
                    //判断是否是采购售后单走手续费逻辑
                    List<Integer> v = list.stream().distinct().collect(Collectors.toList());
                    if (v.size()>0&&v.size()==1){
                        Integer relatedIdGreat = list.get(0);
                        AfterSales afterSalesById = afterSalesService.getAfterSalesById(relatedIdGreat);
                        if (afterSalesById.getSubjectType().equals(SysOptionConstant.ID_536)) {
                            BuyorderExpenseItemDto bs = buyorderExpenseApiService.getBuyorerExpenseByAfterSalesId(relatedIdGreat);
                            if (bs != null) {
                                Integer buyorderExpenseId = bs.getBuyorderExpenseId();
                                List<AfterSalesInvoiceVo> buyorderExpenseSalesByRelatedId = invoiceMapper.getBuyorderExpenseSalesByRelatedId(buyorderExpenseId);
                                if (buyorderExpenseSalesByRelatedId.size()>0){
                                    afterOrderList = new ArrayList<>();
                                }
                            }
                        }
                    }

                    map.put("afterOrderList", afterOrderList);
                }
            }
        }
        return map;
    }

    /**
     * 采购费用单录票信息
     * @param invoiceSaveSearchDto
     * @return
     */
    private List<BuyorderVo> getAllExpenseOrderInfo(InvoiceSaveSearchDto invoiceSaveSearchDto) {
        List<BuyorderExpenseItemDto> expenseGoodsList4InvoiceSave = buyorderExpenseApiService.getExpenseGoodsList4InvoiceSave(invoiceSaveSearchDto);
        if (CollectionUtils.isEmpty(expenseGoodsList4InvoiceSave)){
            return null;
        }

        List<Integer> buyOrderIds = expenseGoodsList4InvoiceSave
                .stream().map(BuyorderExpenseItemDto::getBuyOrderId)
                .filter(Objects::nonNull)
                .filter(integer -> integer > 0)
                .distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(buyOrderIds)){
            List<Integer> excludesOrderIds = buyorderMapper.getBuyOrderInfoByOrderIds(buyOrderIds)
                    .stream().filter(item -> ErpConst.ZERO.equals(item.getArrivalStatus()))
                    .map(BuyorderVo::getBuyorderId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(excludesOrderIds)){
                expenseGoodsList4InvoiceSave = expenseGoodsList4InvoiceSave.stream().filter(item -> !excludesOrderIds.contains(item.getBuyOrderId())).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(expenseGoodsList4InvoiceSave)){
            return null;
        }

        HashMap<Integer, List<BuyorderExpenseItemDto>> orderIdExpenseGoodsMap = new HashMap<>(16);
        expenseGoodsList4InvoiceSave.forEach(item -> {
            Integer key = item.getBuyorderExpenseId();
            if (orderIdExpenseGoodsMap.containsKey(key)){
                orderIdExpenseGoodsMap.get(key).add(item);
            } else {
                ArrayList<BuyorderExpenseItemDto> expenseItems = new ArrayList<>();
                expenseItems.add(item);
                orderIdExpenseGoodsMap.put(key, expenseItems);
            }
        });

        ArrayList<BuyorderVo> buyOrderInfo = new ArrayList<>();

        buyorderExpenseApiService.getOrderExpenseListByOrderIds(
                expenseGoodsList4InvoiceSave.stream().map(BuyorderExpenseItemDto::getBuyorderExpenseId)
                        .distinct().collect(Collectors.toList()))
                .forEach(orderInfo -> {
            BuyorderVo buyorderVo = new BuyorderVo();
            buyorderVo.setBuyorderId(orderInfo.getBuyorderExpenseId());
            buyorderVo.setBuyorderNo(orderInfo.getBuyorderExpenseNo());
            buyorderVo.setCompanyId(ErpConst.ONE);
            buyorderVo.setTraderId(orderInfo.getTraderId());
            buyorderVo.setTraderName(orderInfo.getTraderName());
            buyorderVo.setTotalAmount(orderInfo.getTotalAmount());
            buyorderVo.setInvoiceType(orderInfo.getInvoiceType());
            buyorderVo.setLockedStatus(orderInfo.getLockedStatus());
            buyorderVo.setInvoiceComments(orderInfo.getInvoiceComments());
            buyorderVo.setValidTime(orderInfo.getValidTime() != null ? orderInfo.getValidTime().getTime() : 0L);
            buyorderVo.setPaymentTime(orderInfo.getPaymentTime() != null ? orderInfo.getPaymentTime().getTime() : 0L);
            buyorderVo.setBuyorderExpenseItemDtos(orderIdExpenseGoodsMap.get(orderInfo.getBuyorderExpenseId()));
            buyOrderInfo.add(buyorderVo);
        });
        return buyOrderInfo.stream().sorted(Comparator.comparing(BuyorderVo :: getPaymentTime).reversed()).collect(Collectors.toList());
    }


    @Override
    public ResultInfo<?> saveInvoice(Invoice invoice) throws Exception {
        ResultInfo<?> result = null;

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> TypeRef = new TypeReference<ResultInfo<Saleorder>>() {};
        String url = httpUrl + "finance/invoice/saveinvoice.htm";
        try {
            // 纠正发票税率
            if (invoice.getInvoiceType() != null && HxInvoiceConstant.SPECIAL_INVOICE_TYPE_.contains(invoice.getInvoiceType())) {
                List<SysOptionDefinition> invoiceTypeList = getSysOptionDefinitionList(SysOptionConstant.ID_428);
                for (SysOptionDefinition sysOption : invoiceTypeList) {
                    if (invoice.getInvoiceType().equals(sysOption.getSysOptionDefinitionId())) {
                        invoice.setRatio(!StringUtils.isEmpty(sysOption.getComments()) ? new BigDecimal(sysOption.getComments()) : new BigDecimal("0.00"));
                        break;
                    }
                }
            }

            result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null) {
                if (result.getCode() == 0 && result.getData() != null && invoice.getType().intValue() == 505) {// 销售
                    Saleorder saleorder = (Saleorder) result.getData();

                    if (ErpConst.THREE.equals(invoice.getInvoiceProperty())) {
                        // 同时保存下载发票任务
                        DownloadInvoice data = new DownloadInvoice();
                        data.setInvoiceNo(invoice.getInvoiceNo());
                        data.setInvoiceApplyId(invoice.getInvoiceApplyId().toString());
                        data.setInvoiceDate(invoice.getOpenInvoiceTimeStr());
                        data.setSkipCheckExtend(Boolean.TRUE);
                        fullyDigitalInvoiceApiService.downloadInvoice(data);
                    }


                    logger.info("销售订单开票处理订单生成账期逾期编码 order:{}", JSON.toJSONString(saleorder));
                    saleorder.getInvoiceId().forEach(invoiceId -> {
                        try {
                            Invoice invoiceInfo = invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoiceId);
                            if (invoiceInfo == null) {
                                logger.warn("开票生成账期逾期编码时发票信息不存在 error invoiceId:{}", invoiceId);
                                throw new CustomerBillPeriodException("开票生成账期逾期编码时发票信息不存在 invoiceId:" + invoiceId);
                            }
                            orderAccountPeriodService.dealCustomerBillPeriodManagement(
                                    saleorder.getSaleorderId(),
                                    CustomerBillPeriodOverdueManageDetailTypeEnum.ORDER_INVOICE.getCode(),
                                    invoiceId,
                                    invoiceInfo.getAmount());
                        } catch (CustomerBillPeriodException e) {
                            logger.error("销售订单开票处理订单生成账期逾期编码 error", e);
                        }
                    });

                    // 根据客户Id查询客户负责人
                    List<Integer> userIdList =
                            userMapper.getUserIdListByTraderId(saleorder.getTraderId(), ErpConst.ONE);
                    Map<String, String> map = new HashMap<>();
                    map.put("saleorderNo", saleorder.getSaleorderNo());
                    // 銷售開票后發送消息給銷售負責人
                    MessageUtil.sendMessage(11, userIdList, map,
                            "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId());
                    //更新订单updateDataTime
                    orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_INVOICE);

                    try {
                        /**
                         * 销售订单手动录票处理在线开票信息结果下传
                         */
                        logger.info("销售订单手动录票处理在线开票信息下传 invoice:{}", JSON.toJSONString(invoice));
                        InvoiceApply invoiceApplyInfo = invoiceApplyMapper.selectByPrimaryKey(invoice.getInvoiceApplyId());
                        if (InvoiceApplyMethodEnum.ONLINE_INVOICE_OPEN.getApplyMethodCode().equals(invoiceApplyInfo.getApplyMethod())){

                            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(invoiceApplyInfo.getRelatedId());

                            InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
                            invoiceOpenResultDto.setOrderNo(saleOrderById.getSaleorderNo());
                            invoiceOpenResultDto.setInvoiceApplyId(invoice.getInvoiceApplyId());
                            invoiceOpenResultDto.setCode(result.getCode());
                            if (ErpConst.ERROR_CODE.equals(invoiceOpenResultDto.getCode())){
                                invoiceOpenResultDto.setMessage(result.getMessage());
                            }
                            logger.info("销售订单手动录票进行开票结果下传 invoiceOpenResultDto:{}", JSON.toJSONString(invoiceOpenResultDto));
                            erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));
                        }
                    } catch (Exception e) {
                        logger.error("销售订单手动录票处理在线开票信息结果下传异常", e);
                    }
                }

                if(result != null && result.getCode().equals(0) && invoice.getType() != null){
                    List<Integer> relatedIdList = invoice.getRelatedIdList();
                    //售后
                    if(invoice.getType().equals(504)){

                        Saleorder saleorder = (Saleorder) result.getData();
                        List<Integer> invoiceIds = saleorder.getInvoiceId();
                        Integer invoiceId = 0;
                        if (invoiceIds.size()>0){
                            invoiceId = invoiceIds.get(0);
                            invoice.setInvoiceId(invoiceId);
                        }
                        //判断是否是采购售后单走手续费逻辑
                        Integer relatedIdGreat = relatedIdList.get(0);
                        AfterSales afterSalesById = afterSalesService.getAfterSalesById(relatedIdGreat);
                        if (afterSalesById.getSubjectType().equals(SysOptionConstant.ID_536)) {
                            BuyorderExpenseItemDto buyorerExpenseByAfterSalesId = buyorderExpenseApiService.getBuyorerExpenseByAfterSalesId(relatedIdGreat);
                            if (buyorerExpenseByAfterSalesId.getBuyorderExpenseId() != null && buyorerExpenseByAfterSalesId.getBuyorderExpenseItemId() != null){
                                    Integer buyorderExpenseId = buyorerExpenseByAfterSalesId.getBuyorderExpenseId();
                                    invoiceMapper.updateRelatedIdByPrimaryKey(invoiceId,buyorderExpenseId);
                                    buyorderExpenseApiService.updateStatusByBuyorderIdWithTicket(buyorderExpenseId,Constants.TWO);
                                    List<InvoiceDetail> invoiceDetails = invoiceDetailMapper.getInvoiceDetails(invoice);
                                    for (InvoiceDetail detail : invoiceDetails) {
                                        detail.setDetailgoodsId(buyorerExpenseByAfterSalesId.getBuyorderExpenseItemId());
                                        invoiceDetailMapper.updateByPrimaryKeySelective(detail);
                                    }
                            }
                        }

                        for (Integer relatedId : relatedIdList) {
                            orderCommonService.updateAfterOrderDataUpdateTime(relatedId,null,OrderDataUpdateConstant.AFTER_ORDER_INVOICE);
                        }

                    }
                    //采购
                    if(invoice.getType().equals(503)){
                        for (Integer relatedId : relatedIdList) {
                            orderCommonService.updateBuyOrderDataUpdateTime(relatedId,null,OrderDataUpdateConstant.BUY_ORDER_INVOICE);
                        }
                    }
                }

            }
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>();
        }
        return result;

    }

    @Override
    public Map<String, Object> getInvoiceAuditListByInvoiceNo(Invoice invoice) {
        Map<String, Object> map = new HashMap<>();
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {};
        String url = httpUrl + "finance/invoice/getinvoiceauditlistbyinvoiceno.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null) {
                    net.sf.json.JSONArray json = null;
                    if (result_map.get("goods_list") != null) {
                        String goodsListStr = result_map.get("goods_list").toString();
                        json = net.sf.json.JSONArray.fromObject(goodsListStr);
                        List<InvoiceDetail> invoiceGoodsList = (List<InvoiceDetail>) json.toCollection(json, InvoiceDetail.class);
                        
                        json = null;
                        String attachmentStr = result_map.get("attachmentList").toString();
                        json = net.sf.json.JSONArray.fromObject(attachmentStr);
                        List<GoodsAttachment> attachmentList = (List<GoodsAttachment>) json.toCollection(json, GoodsAttachment.class);
                        map.put("attachmentList", attachmentList);
                        /*List<orderInfo> orderList = null;
                        for (int i = 0; i < invoiceGoodsList.size(); i++) {
                            json = net.sf.json.JSONArray.fromObject(result_map.get(invoiceGoodsList.get(i).getInvoiceDetailId().toString()));
                            orderList = (List<orderInfo>) json.toCollection(json, orderInfo.class);
                            if (orderList != null && !orderList.isEmpty()) {
                                for (int j = 0; j < orderList.size(); j++) {
                                    orderList.get(j)
                                            .setUserName(userMapper.getUserNameByUserId(orderList.get(j).getUserId()));
                                }
                                invoiceGoodsList.get(i).setOrderList(orderList);
                            }
                        }*/
                        map.put("invoiceGoodsList", invoiceGoodsList);
                    }

                    invoice = (Invoice) JSONObject.toBean(JSONObject.fromObject(result_map.get("invoice")), Invoice.class);
                    map.put("invoice", invoice);
                }
            }
            return map;
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public ResultInfo<?> saveInvoiceAudit(Invoice invoice) {
        logger.info("收票审核服务层开始 invoice:{}", JSON.toJSONString(invoice));
        List<Invoice> validInvoiceListThisAudit = invoiceMapper.getValidInvoiceListThisAudit(invoice.getInvoiceId());

        try {
            if (!relationOperateLog(invoice, validInvoiceListThisAudit)){
                return ResultInfo.error("当前发票所录商品无匹配的入库单，请检查是否重复录入或者入库单尚未生成，请将发票审核驳回！");
            }
        } catch (Exception e) {
            logger.error("要处理，发票审核关联入库日志信息错误 invoice:{}", JSON.toJSONString(invoice), e);
            return ResultInfo.error("发票审核关联入库日志信息错误");
        }

        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
        String url = httpUrl + "finance/invoice/saveinvoiceaudit.htm";
        ResultInfo<?> resultInfo;
        try {
            resultInfo =  (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
        }
        catch (Exception e) {
            LOG.error("收票审核db保存端错误", e);
            return ResultInfo.error("收票审核db保存端错误");
        }

        try {
            dealInvoiceAuditBusiness(validInvoiceListThisAudit);
        } catch (Exception e) {
            logger.error("要处理，收票审核后置业务处理告警 invoice:{}", JSON.toJSONString(invoice), e);
        }

        try {
            sendInvoiceAuditMessage(validInvoiceListThisAudit, invoice);
        } catch (Exception e) {
            logger.error("发送发票审核站内信告警 invoice:{}", JSON.toJSONString(invoice));
        }
        return resultInfo;
    }

    /**
     * 发票审核关联入库日志信息
     * @param invoice
     * @param validInvoiceListThisAudit
     * @throws Exception
     */
    private boolean relationOperateLog(Invoice invoice, List<Invoice> validInvoiceListThisAudit) throws Exception {
        logger.info("收票审核处理关联入库日志记录信息 validInvoiceListThisAudit:{}, invoice:{}", JSON.toJSONString(validInvoiceListThisAudit), JSON.toJSONString(invoice));
        if (ErpConst.TWO.equals(invoice.getValidStatus()) || !SysOptionConstant.ID_503.equals(invoice.getType())){
            return true;
        }

        ArrayList<Integer> invoiceIdList = new ArrayList<>();

        validInvoiceListThisAudit.forEach(invoiceInfo -> {
            Buyorder buyOrderInfo = buyorderMapper.selectByPrimaryKey(invoiceInfo.getRelatedId());
            if (!Objects.nonNull(buyOrderInfo)){
                logger.warn("发票审核关联入库日志信息采购单信息错误告警 invoice:{}", JSON.toJSONString(invoice));
                return;
            }
            if (buyOrderInfo.getValidTime() < 1672502400000L){
                return;
            }
            invoiceIdList.add(invoiceInfo.getInvoiceId());
        });

        if (CollectionUtils.isEmpty(invoiceIdList)){
            logger.info("发票审核关联入库日志信息无需处理关联发票 validInvoiceListThisAudit:{}", JSON.toJSONString(validInvoiceListThisAudit));
            return true;
        }

        return invoiceApiService.saveRelatedWarehousingLogId(invoiceIdList);
    }

    /**
     * 收票审核后置业务处理
     * @param validInvoiceListThisAudit
     */
    private void dealInvoiceAuditBusiness(List<Invoice> validInvoiceListThisAudit) {
        logger.info("收票审核后置业务处理 validInvoiceListThisAudit:{}", JSON.toJSONString(validInvoiceListThisAudit));
        if (CollectionUtils.isEmpty(validInvoiceListThisAudit)){
            return;
        }

        validInvoiceListThisAudit.forEach(invoiceBeforeInfo -> {
            Invoice invoice = invoiceMapper.selectByPrimaryKey(invoiceBeforeInfo.getInvoiceId());
            //处理订单收票状态信息
            try {
                dealOrderInvoiceStatus(invoice);
            } catch (Exception e) {
                logger.error("要处理， 处理订单收票状态信息error invoice：{}", JSON.toJSONString(invoice), e);
            }

            //处理采购订单催票预警信息
            try {
                dealEarlyWarningTicketTask(invoice);
            } catch (Exception e) {
                logger.error("处理采购订单催票预警信息error invoice：{}", JSON.toJSONString(invoice), e);
            }

            try {
                if (SysOptionConstant.ID_503.equals(invoice.getType())){
                    orderCommonService.updateBuyOrderDataUpdateTime(invoice.getRelatedId(),null,OrderDataUpdateConstant.BUY_ORDER_INVOICE);
                }
            } catch (Exception e) {
                logger.error("处理采购订单时间信息error invoice：{}", JSON.toJSONString(invoice), e);
            }

            //航信发票流转信息处理
            try {
                logger.info("保存发票审核状态航信发票信息处理 invoice:{}", JSON.toJSONString(invoice));
                refreshHxInvoiceStatus(invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoice.getInvoiceId()).getHxInvoiceId());
            } catch (Exception e) {
                logger.error("保存发票审核状态航信发票信息处理失败 invoice:{}", JSON.toJSONString(invoice), e);
            }
        });
    }

    /**
     * 发送发票审核站内信
     * @param validInvoiceListThisAudit
     * @param invoice
     */
    private void sendInvoiceAuditMessage(List<Invoice> validInvoiceListThisAudit, Invoice invoice) {
        if (!ErpConst.TWO.equals(invoice.getValidStatus())){
            return;
        }
        List<Integer> invoiceSaverList = validInvoiceListThisAudit.stream().map(Invoice::getCreator)
                .filter(integer -> integer > 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(invoiceSaverList)){
            return;
        }

        logger.info("审核不通过对于录票人员发送站内信 invoiceSaverList:{}", JSON.toJSONString(invoiceSaverList));
        Map<String, String> mapParams = new HashMap<>(8);
        mapParams.put("invoiceNo", invoice.getInvoiceNo());
        User validUser = userMapper.getUserByUserId(invoice.getValidUserId());
        MessageUtil.sendMessage2(191, invoiceSaverList, mapParams, "./finance/invoice/getInvoiceListPage.do",
                validUser != null ? validUser.getUsername() : "njadmin");
    }

    /**
     * 审核环节航信信息处理
     *
     * @param invoice
     */
    private void dealHxInvoiceInfo(Invoice invoice) {
        logger.info("发票审核环节开始处理航信信息 invoice:{}" , JSON.toJSONString(invoice));
        if (invoice == null || invoice.getInvoiceId() == null){
            logger.warn("发票信息不完整 invoice:{}" , JSON.toJSONString(invoice));
            return;
        }
        Invoice invoiceBaseInfo = invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoice.getInvoiceId());
        if (invoiceBaseInfo == null || invoiceBaseInfo.getInvoiceFrom() == null){
            logger.warn("发票的基础信息不完整,请检查 invoiceBaseInfo:{}", JSON.toJSONString(invoiceBaseInfo));
            return;
        }
        if (invoiceBaseInfo.getInvoiceFrom() == 0 || invoiceBaseInfo.getHxInvoiceId() == null){
            logger.info("该发票来源为非航信推送 invoice:{}" , JSON.toJSONString(invoice));
            return;
        }

        Integer hxInvoiceStatus  = null;
        switch (invoice.getValidStatus()) {
            case 0 :{
                logger.info("发票状态为正在审核中 航信流转信息不做处理 invoiceNo:{}" , invoice.getInvoiceNo());
                return;
            }
            case 1 :{
                logger.info("航信发票录票之后审核通过航信发票的状态修改为暂不做处理 hxInvoiceId:{}", invoiceBaseInfo.getHxInvoiceId());
                return;
            }
            case 2:{
                logger.info("发票状态为审核不通过 航信发票状态流转为待录票 hxInvoiceId:{}" ,invoiceBaseInfo.getHxInvoiceId());
                hxInvoiceStatus = HxInvoiceStatusEnum.WAIT_RECORD.getStatus();
                break;
            }
            default:{
                logger.warn("发票审核状态异常 invoiceNo:{}" ,invoiceBaseInfo.getInvoiceNo());
                return;
            }
        }

        logger.info("发票审核开始更新航信发票状态信息 getHxInvoiceId:{},hxInvoiceStatus:{}" ,invoiceBaseInfo.getHxInvoiceId(),hxInvoiceStatus);
        hxInvoiceMapper.saveHxInvoiceStatus(invoiceBaseInfo.getHxInvoiceId(), hxInvoiceStatus);
    }
    /**
     * @Description 更新催票任务
     * <AUTHOR>
     * @Date 10:25 2021/6/1
     * @Param [thisInvoiceTicketCount, buyorderGoodsId]
     * @return void
     **/
    @Override
    public void dealEarlyWarningTicketTask(Invoice invoice) {
        logger.info("更新催票任务start invoice:{}", JSON.toJSONString(invoice));
        if (StringUtil.isBlank(invoice.getInvoiceNo()) ||
                ( !SysOptionConstant.ID_503.equals(invoice.getType()) &&  !SysOptionConstant.ID_4126.equals(invoice.getType())) ||
                !ErpConst.ONE.equals(invoice.getValidStatus())){
            return;
        }
        List<InvoiceDetail> invoiceDetailsThisRecord = invoiceMapper.getInvoiceListByInvoiceId(invoice.getInvoiceId());
        if (CollectionUtils.isEmpty(invoiceDetailsThisRecord)){
            logger.info("本次审核暂无需要处理的更新催票任务信息 invoice:{}", JSON.toJSONString(invoice));
            return;
        }
        logger.info("更新催票任务录票详情信息 invoiceDetailsThisRecord:{}", JSON.toJSONString(invoiceDetailsThisRecord));

        HashMap<Integer, BigDecimal> buyOrderGoodsRecordNumMap = new HashMap<>();
        invoiceDetailsThisRecord.forEach(item -> {
            buyOrderGoodsRecordNumMap.put(item.getDetailgoodsId(),
                    ! buyOrderGoodsRecordNumMap.containsKey(item.getDetailgoodsId()) ? item.getNum():
                            buyOrderGoodsRecordNumMap.get(item.getDetailgoodsId()).add(item.getNum()));
        });

        if (MapUtils.isEmpty(buyOrderGoodsRecordNumMap)){
            logger.warn("聚合录票商品数量错误告警 invoice:{}", JSON.toJSONString(invoice));
            return;
        }
        logger.info("处理录票商品预警信息 buyOrderGoodsRecordNumMap:{}", JSON.toJSONString(buyOrderGoodsRecordNumMap));
        buyOrderGoodsRecordNumMap.forEach((detailGoodsId, invoiceNum) -> expeditingTicketsService.updateEarlyWarningTicketTask(invoiceNum, detailGoodsId));
    }

    @Override
    public void checkInvoiceStatus(Invoice invoice) {

        if (invoice.getAfterSalesId() == null || invoice.getInvoiceId() == null) {
            throw new RuntimeException("Lack of necessary data");
        }
        AfterSalesInvoice afterSalesInvoice = afterSalesInvoiceMapper.selectOneByRelatedId(invoice.getAfterSalesId(),invoice.getInvoiceId());
        if (afterSalesInvoice == null) {
            return;
        }
        if (Integer.valueOf(1).equals(afterSalesInvoice.getStatus())) {
            throw new RuntimeException("Ticket refunded(已退票)");
        }

    }

    /**
     * 处理订单收票状态信息
     * 包含采购单费用单
     *
     * @param invoice
     */
    @Override
    public void dealOrderInvoiceStatus(Invoice invoice) {
        logger.info("发票审核处理订单收票信息 invoice:{}", JSON.toJSONString(invoice));
        if (!ErpConst.ONE.equals(invoice.getValidStatus())){
            logger.info("发票审核不通过无需处理流转订单收票状态 invoiceNo:{}", invoice.getInvoiceNo());
            return;
        }

        if (SysOptionConstant.ID_503.equals(invoice.getType())) {
            logger.info("收票审核通过 发票为采购订单 invoice:{}", JSON.toJSONString(invoice));
            saveBuyorderInvoiceStatus(invoice.getRelatedId());
        } else if (SysOptionConstant.ID_4126.equals(invoice.getType())){
            logger.info("收票审核处理采购费用单收票状态 invoice:{}", JSON.toJSONString(invoice));
            buyorderExpenseApiService.doBuyOrderExpenseInvoiceStatus(invoice.getRelatedId());
        }
    }

    @Override
    public Map<String, Object> getInvoiceListPage(Invoice invoice, Page page) {
        Map<String, Object> map = new HashMap<>();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {};
        String url = httpUrl + "finance/invoice/getinvoicelistpage.htm";
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                map.put("page", result.getPage());

                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null && result_map.get("invoiceList") != null) {

                    net.sf.json.JSONArray json = null;
                    String invoiceListStr = result_map.get("invoiceList").toString();
                    json = net.sf.json.JSONArray.fromObject(invoiceListStr);
                    List<Invoice> list = (List<Invoice>) JSONArray.toCollection(json, Invoice.class);

                    invoice = (Invoice) JSONObject.toBean(JSONObject.fromObject(result_map.get("invoice")),
                            Invoice.class);
                    map.put("invoice", invoice);
                    if (CollectionUtils.isNotEmpty(list)) {// 查询录票人员和审核人
                        List<Integer> inValidList = hxInvoiceMapper.getInValidList();

                        map.put("invoiceList", list);
                        List<Integer> inputUserIdList = new ArrayList<>();
                        List<Integer> auditUserIdList = new ArrayList<>();
                        for (Invoice value : list) {
                            // 添加发票状态
                            if (inValidList.contains(value.getHxInvoiceId())) {
                                value.setIsInValid(1);
                            }
                            inputUserIdList.add(value.getCreator());
                            auditUserIdList.add(value.getUpdater());
                            auditUserIdList.add(value.getValidUserId());
                        }
                        List<User> inputUserList = userMapper.getUserByUserIds(inputUserIdList);
                        List<User> auditUserList = userMapper.getUserByUserIds(auditUserIdList);
                        map.put("inputUserList", inputUserList);
                        map.put("auditUserList", auditUserList);
                    }

                    setInvoiceVoucherInfo(list);
                }
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }


    /**
     * 金蝶凭证号相关信息
     *
     * @param list
     */
    private void setInvoiceVoucherInfo(List<Invoice> list) {
        try {
            if (CollectionUtils.isEmpty(list)){
                return;
            }
            List<InvoiceVoucherDto> invoiceVoucherInfo = invoiceMapper.getInvoiceVoucherInfo(list.stream().map(Invoice::getInvoiceId).distinct().collect(Collectors.toList()));

            if (CollectionUtils.isEmpty(invoiceVoucherInfo)){
                return;
            }

            Map<Integer, InvoiceVoucherDto> invoiceVoucherInfoMap = invoiceVoucherInfo.stream().collect(Collectors.toMap(InvoiceVoucherDto::getInvoiceId, Function.identity()));
            list.forEach(invoice -> invoice.setInvoiceVoucherDto(invoiceVoucherInfoMap.getOrDefault(invoice.getInvoiceId(), null)));
        } catch (Exception e) {
            logger.error("setInvoiceVoucherInfo warn list:{}", JSON.toJSONString(list), e);
        }
    }

    @Override
    public Map<String, Object> exportIncomeInvoiceList(Invoice invoice) {
        Map<String, Object> map = new HashMap<>();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {};
        String url = httpUrl + "finance/invoice/exportincomeinvoicelist.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                map.put("page", result.getPage());

                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null && result_map.get("invoiceList") != null) {

                    net.sf.json.JSONArray json = null;
                    String invoiceListStr = result_map.get("invoiceList").toString();
                    json = net.sf.json.JSONArray.fromObject(invoiceListStr);
                    List<Invoice> list = (List<Invoice>) json.toCollection(json, Invoice.class);

                    invoice = (Invoice) JSONObject.toBean(JSONObject.fromObject(result_map.get("invoice")),
                            Invoice.class);
                    map.put("invoice", invoice);
                    if (list != null && !list.isEmpty()) {// 查询录票人员和审核人
                        map.put("invoiceList", list);
                        List<Integer> inputUserIdList = new ArrayList<>();
                        List<Integer> auditUserIdList = new ArrayList<>();
                        for (int i = 0; i < list.size(); i++) {
                            inputUserIdList.add(list.get(i).getCreator());
                            auditUserIdList.add(list.get(i).getCreator());
                        }
                        List<User> inputUserList = userMapper.getUserByUserIds(inputUserIdList);
                        List<User> auditUserList = userMapper.getUserByUserIds(auditUserIdList);
                        map.put("inputUserList", inputUserList);
                        map.put("auditUserList", auditUserList);
                    }
                }
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public ResultInfo<?> saveOpenInvoceApply(InvoiceApply invoiceApply) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Integer>> TypeRef = new TypeReference<ResultInfo<Integer>>() {};
            String url = httpUrl + "finance/invoice/saveopeninvoceapply.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef);
            if (result != null) {
                return result;
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>(-1, "操作异常");
        }
        return new ResultInfo<>(-1, "操作失败");
    }

    @Override
    public Map<String, Object> getSaleInvoiceApplyListPage(InvoiceApply invoiceApply, Page page) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                    new TypeReference<ResultInfo<Map<String, Object>>>() {};
            String url = httpUrl + "finance/invoice/getsaleinvoiceapplylistpage.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef, page);

            logger.info("请求API:{},响应结果:{}","finance/invoice/getsaleinvoiceapplylistpage.htm",JSON.toJSONString(result));

            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null && result_map.size() > 0) {

                    map = new HashMap<String, Object>();
                    net.sf.json.JSONArray json = null;
                    String openInvoiceApplyStr = result_map.get("openInvoiceApplyList").toString();
                    json = net.sf.json.JSONArray.fromObject(openInvoiceApplyStr);

                    List<InvoiceApply> openInvoiceApplyList =
                            (List<InvoiceApply>) json.toCollection(json, InvoiceApply.class);

                    map.put("openInvoiceApplyList", openInvoiceApplyList);



                    //计算订单总额
                    net.sf.json.JSONArray json1 = null;
                    String applyAmountListStr = result_map.get("applyAmountList").toString();
                    json1 = net.sf.json.JSONArray.fromObject(applyAmountListStr);

                    List<InvoiceApply> applyAmountList =
                            (List<InvoiceApply>) json1.toCollection(json1, InvoiceApply.class);
                    map.put("applyAmountMoney",result_map.get("applyAmountMoney"));
                    //
                    //根据条件查询所有数据
                    map.put("applyAmountList",applyAmountList);

                    invoiceApply = (InvoiceApply) JSONObject
                            .toBean(JSONObject.fromObject(result_map.get("invoiceApply")), InvoiceApply.class);
                    map.put("invoiceApply", invoiceApply);

                    map.put("page", result.getPage());

                    // map.put("totalMoney", result_map.get("totalMoney"));

                    List<Integer> userIdList = new ArrayList<>();
                    // List<Integer> orgIdList = new ArrayList<>();
                    List<Integer> traderIdList = new ArrayList<>();
                    if (openInvoiceApplyList != null && !openInvoiceApplyList.isEmpty()) {
                        for (int i = 0; i < openInvoiceApplyList.size(); i++) {
                            userIdList.add(openInvoiceApplyList.get(i).getCreator());
                            userIdList.add(openInvoiceApplyList.get(i).getValidUserId());
                            userIdList.add(openInvoiceApplyList.get(i).getUpdater());

                            // orgIdList.add(openInvoiceApplyList.get(i).getOrgId());

                            traderIdList.add(openInvoiceApplyList.get(i).getTraderId());
                            if (FinanceConstant.ID_504.equals(openInvoiceApplyList.get(i).getType())){
                                InvoiceApply apply = openInvoiceApplyList.get(i);
                                AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(apply.getRelatedId());
                                apply.setAfterSaleType(afterSales.getType());
                                apply.setAfterSaleSubjectType(afterSales.getSubjectType());
                            }

                        }
                        userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
                        List<User> userList = userMapper.getUserByUserIds(userIdList);
                        map.put("userList", userList);// 申请人，审核人

                        // List<Organization> orgList = organizationMapper.getOrgByList(orgIdList);
                        // map.put("orgList", orgList);//销售部门

                        List<User> traderUserList = userMapper.getTraderUserAndOrgList(traderIdList);// 1客户，2供应商
                        map.put("traderUserList", traderUserList);// 归属销售
                    }
                    return map;
                }

            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public Map<String, Object> getInvoiceApplyVerifyListPage(InvoiceApply invoiceApply, Page page) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<InvoiceApply>>> TypeRef =
                    new TypeReference<ResultInfo<List<InvoiceApply>>>() {};
            String url = httpUrl + "finance/invoice/getinvoiceapplyverifylistpage.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                List<InvoiceApply> openInvoiceApplyList = (List<InvoiceApply>) result.getData();
                if (openInvoiceApplyList != null && openInvoiceApplyList.size() > 0) {
                    map = new HashMap<String, Object>();
                    map.put("page", result.getPage());

                    List<Integer> userIdList = new ArrayList<>();
                    List<Integer> traderIdList = new ArrayList<>();
                    if (openInvoiceApplyList != null && !openInvoiceApplyList.isEmpty()) {
                        for (int i = 0; i < openInvoiceApplyList.size(); i++) {
                            userIdList.add(openInvoiceApplyList.get(i).getCreator());
                            userIdList.add(openInvoiceApplyList.get(i).getValidUserId());
                            userIdList.add(openInvoiceApplyList.get(i).getUpdater());

                            traderIdList.add(openInvoiceApplyList.get(i).getTraderId());
                        }
                        userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
                        List<User> userList = userMapper.getUserByUserIds(userIdList);
                        map.put("userList", userList);// 申请人，审核人
                        List<User> traderUserList = userMapper.getTraderUserAndOrgList(traderIdList);// 1客户，2供应商
                        map.put("traderUserList", traderUserList);// 归属销售

                        map.put("openInvoiceApplyList", openInvoiceApplyList);// 归属销售
                    }
                    return map;
                }
                else {
                    map = new HashMap<String, Object>();
                    map.put("page", result.getPage());
                    map.put("openInvoiceApplyList", new ArrayList<>());// 归属销售
                }

            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Autowired
    private SmsService smsService;

    @Override
    public List<InvoiceApplyError> advanceInvoiceAudit(InvoiceApply invoiceApply) {

        LOG.info("advanceInvoiceAudit，入参:{}",JSON.toJSONString(invoiceApply));
        String key = ErpConstant.REVIEW_OF_ADVANCE_INVOICING + invoiceApply.getInvoiceApplyId();
        // 加锁
        boolean lock = RedissonLockUtils.tryLock(key);
        InvoiceApply data = invoiceApplyMapper.selectByPrimaryKey(invoiceApply.getInvoiceApplyId());
        Integer type = Optional.ofNullable(data.getType()).orElse(ErpConst.ZERO);
        // 销售和售后的
        String orderNo = "";
        Integer userId = 0;
        String invoiceTraderContactMobile = "";
        if (type.equals(504) || type.equals(505)) {

            userId = data.getCreator();

            if (type.equals(504)) {
                AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(data.getRelatedId());
                orderNo = afterSalesById.getAfterSalesNo();
            }
            if (type.equals(505)) {
                Saleorder saleorder = saleorderMapper.selectByPrimaryKey(data.getRelatedId());

                invoiceTraderContactMobile= saleorder.getInvoiceTraderContactMobile();
                orderNo = saleorder.getSaleorderNo();
            }
        }

        if (!lock) {
            LOG.info("加锁失败，invoiceApplyId:{},存在处理中的审核业务", invoiceApply.getInvoiceApplyId());

            InvoiceApplyError err = new InvoiceApplyError();
            err.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
            err.setOrderNo(orderNo);
            err.setMsg("存在处理中的审核业务,请稍后再试");
            return CollUtil.newLinkedList(err);
        }
        try {


            CurrentUser currentUser = CurrentUser.getCurrentUser();
            invoiceApply.setAdvanceValidTime(DateUtil.gainNowDate());
            invoiceApply.setAdvanceValidUserid(currentUser.getId());
            if (data.getAdvanceValidStatus() != 0) {
                String str = ErpConst.ONE.equals(data.getAdvanceValidStatus()) ? "通过" : "驳回";
                InvoiceApplyError err = new InvoiceApplyError();
                err.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
                err.setOrderNo(orderNo);
                err.setMsg("提前开票审核状态为"+str+"，请刷新页面");
                return CollUtil.newLinkedList(err);
            }
            invoiceApply.setUpdater(currentUser.getId());
            invoiceApply.setModTime(DateUtil.gainNowDate());
            invoiceApplyMapper.update(invoiceApply);

            try {
                if (ErpConst.TWO.equals(invoiceApply.getAdvanceValidStatus())) {
                    // 驳回发消息
                    if (type.equals(504) || type.equals(505)) {

                        Integer applyMethod = data.getApplyMethod();
                        if (ErpConstant.THREE.equals(applyMethod)) {
                            if (StrUtil.isNotEmpty(invoiceTraderContactMobile)) {
                                smsService.sendTplSms(invoiceTraderContactMobile, SmsTmpConstant.OPEN_INVOICE_REJECTED,
                                        StrUtil.format("@1@={}", orderNo));
                            }
                        } else {
                            LOG.info("驳回发消息:{}，{}", userId, orderNo);
                            if (StrUtil.isNotEmpty(orderNo) && Objects.nonNull(userId)) {
                                if (!ErpConst.ONE.equals(userId) && !ErpConst.TWO.equals(userId)) {
                                    String format = StrUtil.format(ErpConstant.INVOICE_APPLY_REJECTED_MSG_TEMPLATE, orderNo, invoiceApply.getAdvanceValidComments());
                                    userWorkApiService.sendInvoiceMsg(userId, format);
                                }
                            }
                        }


                    }


                }

                if (ErpConst.ONE.equals(invoiceApply.getAdvanceValidStatus())) {
                    InvoiceApplyDto invoiceApplyDto = invoiceApplyApiService.getInvoiceApply(invoiceApply.getInvoiceApplyId());
                    InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
                    BeanUtil.copyProperties(invoiceApplyDto, invoiceCheckRequestDto);
                    List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList = invoiceApplyDto.getInvoiceApplyDetailDtoList();
                    List<InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto> invoiceCheckRequestDetailDtoList = invoiceApplyDetailDtoList.stream().map(a -> {
                        InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto detailDto = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
                        detailDto.setDetailGoodsId(a.getDetailgoodsId());
                        detailDto.setInvoiceApplyDetailId(a.getInvoiceApplyDetailId());
                        detailDto.setNum(a.getNum());
                        detailDto.setPrice(a.getPrice());
                        detailDto.setTotalAmount(a.getTotalAmount());
                        return detailDto;
                    }).collect(Collectors.toList());
                    invoiceCheckRequestDto.setDetailList(invoiceCheckRequestDetailDtoList);
                    // 审核通过后触发开票配置-开票规则校验
                    invoiceCheckRequestDto.setCheckChainEnum(Integer.valueOf(504).equals(invoiceApplyDto.getType()) ? CheckChainEnum.INVOICE_OPEN_AFTER : CheckChainEnum.INVOICE_OPEN_SALES);
                    invoiceCheckApiService.openCheck(invoiceCheckRequestDto);
                }
            } catch (Exception e) {
                LOG.error(Contant.ERROR_MSG, e);
            }

        } catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            InvoiceApplyError err = new InvoiceApplyError();
            err.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
            err.setOrderNo(orderNo);
            err.setMsg("系统异常");
            return CollUtil.newLinkedList(err);
        }finally {
            RedissonLockUtils.unlock(key);
        }
        return null;
    }



    @Override
    public List<InvoiceApplyError> invoiceAudit(InvoiceApply invoiceApply) {

        LOG.info("invoiceAudit，入参:{}",JSON.toJSONString(invoiceApply));
        String key = ErpConstant.REVIEW_OF_ADVANCE_INVOICING + invoiceApply.getInvoiceApplyId();
        // 加锁
        boolean lock = RedissonLockUtils.tryLock(key);
        InvoiceApply data = invoiceApplyMapper.selectByPrimaryKey(invoiceApply.getInvoiceApplyId());
        Integer type = Optional.ofNullable(data.getType()).orElse(ErpConst.ZERO);
        // 销售和售后的
        String orderNo = "";
        Integer userId = 0;
        String invoiceTraderContactMobile = "";
        if (type.equals(504) || type.equals(505)) {

            userId = data.getCreator();

            if (type.equals(504)) {
                AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(data.getRelatedId());
                orderNo = afterSalesById.getAfterSalesNo();
            }
            if (type.equals(505)) {
                Saleorder saleorder = saleorderMapper.selectByPrimaryKey(data.getRelatedId());
                invoiceTraderContactMobile = saleorder.getInvoiceTraderContactMobile();
                orderNo = saleorder.getSaleorderNo();
            }
        }

        if (!lock) {
            LOG.info("加锁失败，invoiceApplyId:{},存在处理中的审核业务", invoiceApply.getInvoiceApplyId());

            InvoiceApplyError err = new InvoiceApplyError();
            err.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
            err.setOrderNo(orderNo);
            err.setMsg("存在处理中的审核业务,请稍后再试");
            return CollUtil.newLinkedList(err);
        }
        try {

            CurrentUser currentUser = CurrentUser.getCurrentUser();
            invoiceApply.setValidTime(DateUtil.gainNowDate());
            invoiceApply.setValidUserId(currentUser.getId());
            if (data.getValidStatus() != 0) {
                String str = ErpConst.ONE.equals(data.getValidStatus()) ? "通过" : "驳回";
                InvoiceApplyError err = new InvoiceApplyError();
                err.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
                err.setOrderNo(orderNo);
                err.setMsg("开票申请状态为"+str+"，请刷新页面");
                return CollUtil.newLinkedList(err);
            }
            invoiceApply.setUpdater(currentUser.getId());
            invoiceApply.setModTime(DateUtil.gainNowDate());
            invoiceApplyMapper.update(invoiceApply);

            if (type.equals(505)) {
                try {
                    /**
                     * 开票审核时前台发起的开票申请进行下传推送 原始逻辑
                     */
                    logger.info("开票审核时前台发起的开票申请进行下传推送 invoiceApply:{}", JSON.toJSONString(invoiceApply));
                    if (InvoiceApplyMethodEnum.ONLINE_INVOICE_OPEN.getApplyMethodCode().equals(data.getApplyMethod())){

                        Saleorder saleOrderById = saleorderMapper.getSaleOrderById(data.getRelatedId());

                        InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
                        invoiceOpenResultDto.setOrderNo(saleOrderById.getSaleorderNo());
                        invoiceOpenResultDto.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
                        invoiceOpenResultDto.setCode(ErpConst.ONE.equals(invoiceApply.getValidStatus()) ? ErpConst.ZERO : ErpConst.ERROR_CODE);
                        if (ErpConst.ERROR_CODE.equals(invoiceOpenResultDto.getCode())){
                            invoiceOpenResultDto.setMessage(invoiceApply.getValidComments());
                        }
                        logger.info("发票申请审核时进行开票结果下传 invoiceOpenResultDto:{}", JSON.toJSONString(invoiceOpenResultDto));
                        erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));
                    }
                } catch (Exception e) {
                    logger.error("保存发票申请审核记录处理异常", e);
                }
            }


            try {
                if (ErpConst.TWO.equals(invoiceApply.getValidStatus())) {
                    // 驳回发消息
                    if (type.equals(504) || type.equals(505)) {

                        Integer applyMethod = data.getApplyMethod();
                        if (ErpConstant.THREE.equals(applyMethod)) {
                            if (StrUtil.isNotEmpty(invoiceTraderContactMobile)) {
                                smsService.sendTplSms(invoiceTraderContactMobile, SmsTmpConstant.OPEN_INVOICE_REJECTED,
                                        StrUtil.format("@1@={}", orderNo));
                            }
                        } else {
                            LOG.info("驳回发消息:{}，{}", userId, orderNo);
                            if (StrUtil.isNotEmpty(orderNo) && Objects.nonNull(userId)) {

                                if (!ErpConst.ONE.equals(userId)&&!ErpConst.TWO.equals(userId)) {
                                    String format = StrUtil.format(ErpConstant.INVOICE_APPLY_REJECTED_MSG_TEMPLATE, orderNo, invoiceApply.getValidComments());
                                    userWorkApiService.sendInvoiceMsg(userId, format);
                                }

                            }
                        }

                    }

                }
            } catch (Exception e) {
                LOG.error(Contant.ERROR_MSG, e);
            }

        } catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            InvoiceApplyError err = new InvoiceApplyError();
            err.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
            err.setOrderNo(orderNo);
            err.setMsg("系统异常");
            return CollUtil.newLinkedList(err);
        }finally {
            RedissonLockUtils.unlock(key);
        }
        return null;
    }

    @Override
    public Map<String, Object> getAdvanceInvoiceApplyListPage(InvoiceApply invoiceApply, Page page) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                    new TypeReference<ResultInfo<Map<String, Object>>>() {};
            String url = httpUrl + "finance/invoice/getadvanceinvoiceapplylistpage.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                if (result_map != null && result_map.size() > 0) {

                    map = new HashMap<String, Object>();
                    net.sf.json.JSONArray json = null;
                    String openInvoiceApplyStr = result_map.get("openInvoiceApplyList").toString();
                    json = net.sf.json.JSONArray.fromObject(openInvoiceApplyStr);

                    List<InvoiceApply> openInvoiceApplyList =
                            (List<InvoiceApply>) json.toCollection(json, InvoiceApply.class);

                    invoiceApply = (InvoiceApply) JSONObject
                            .toBean(JSONObject.fromObject(result_map.get("invoiceApply")), InvoiceApply.class);
                    map.put("invoiceApply", invoiceApply);

                    map.put("page", result.getPage());

                    // map.put("totalMoney", result_map.get("totalMoney"));

                    List<Integer> userIdList = new ArrayList<>();
                    List<Integer> orgIdList = new ArrayList<>();
                    List<Integer> traderIdList = new ArrayList<>();
                    if (openInvoiceApplyList != null && !openInvoiceApplyList.isEmpty()) {
                        for (int i = 0; i < openInvoiceApplyList.size(); i++) {
                            userIdList.add(openInvoiceApplyList.get(i).getCreator());
                            // userIdList.add(openInvoiceApplyList.get(i).getUpdater());

                            orgIdList.add(openInvoiceApplyList.get(i).getOrgId());

                            traderIdList.add(openInvoiceApplyList.get(i).getTraderId());
                            if (FinanceConstant.ID_504.equals(openInvoiceApplyList.get(i).getType())){
                                InvoiceApply apply = openInvoiceApplyList.get(i);
                                AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(apply.getRelatedId());
                                apply.setAfterSaleType(afterSales.getType());
                                apply.setAfterSaleSubjectType(afterSales.getSubjectType());
                            }
                        }
                        map.put("openInvoiceApplyList", openInvoiceApplyList);
                        userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
                        List<User> userList = userMapper.getUserByUserIds(userIdList);
                        map.put("userList", userList);// 申请人，审核人

                        // List<Organization> orgList = organizationMapper.getOrgByList(orgIdList);
                        // map.put("orgList", orgList);//销售部门

                        List<User> traderUserList = userMapper.getTraderUserAndOrgList(traderIdList);
                        //根据getTraderId去重
                        List buyNewList = traderUserList.stream().collect(collectingAndThen(
                                toCollection(()->new TreeSet<>(Comparator.comparing(User::getTraderId))),ArrayList::new));//list去重

                        map.put("traderUserList", buyNewList);// 归属销售
                    }
                    return map;
                }

            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public Map<String, Object> getSaleInvoiceList(Invoice invoice) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                    new TypeReference<ResultInfo<Map<String, Object>>>() {};
            String url = httpUrl + "finance/invoice/getsaleinvoicelist.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                map = new HashMap<String, Object>();

                net.sf.json.JSONArray json = null;
                String saleorderGoodsStr = result_map.get("saleorderGoodsList").toString();
                json = net.sf.json.JSONArray.fromObject(saleorderGoodsStr);

                List<InvoiceDetail> saleorderGoodsList =
                        (List<InvoiceDetail>) json.toCollection(json, InvoiceDetail.class);
                map.put("saleorderGoodsList", saleorderGoodsList);

                String invoiceDetailStr = result_map.get("invoiceDetailList").toString();
                json = net.sf.json.JSONArray.fromObject(invoiceDetailStr);
                List<InvoiceDetail> invoiceDetailList =
                        (List<InvoiceDetail>) json.toCollection(json, InvoiceDetail.class);
                map.put("invoiceDetailList", invoiceDetailList);

                return map;
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public Map<String, Object> getSaleInvoiceListPage(Invoice invoice, Page page) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                    new TypeReference<ResultInfo<Map<String, Object>>>() {};
            String url = httpUrl + "finance/invoice/getsaleinvoicelistpage.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                Map<String, Object> result_map = (Map<String, Object>) result.getData();
                map = new HashMap<String, Object>();
                net.sf.json.JSONArray json = null;
                String saleInvoiceStr = result_map.get("saleInvoiceList").toString();
                json = net.sf.json.JSONArray.fromObject(saleInvoiceStr);
                List<Invoice> saleInvoiceList = (List<Invoice>) json.toCollection(json, Invoice.class);
                map.put("saleInvoiceList", saleInvoiceList);
                map.put("invoiceNos",result_map.get("invoiceNos"));
                List<Integer> userIdList = new ArrayList<>();
                List<Integer> traderIdList = new ArrayList<>();
                for (int i = 0; i < saleInvoiceList.size(); i++) {
                    userIdList.add(saleInvoiceList.get(i).getCreator());
                    userIdList.add(saleInvoiceList.get(i).getSendUserId());
                    traderIdList.add(saleInvoiceList.get(i).getTraderId());
                    if (saleInvoiceList.get(i).getExpressId() != null
                            && !saleInvoiceList.get(i).getExpressId().equals(0)) {
                        Express express = new Express();
                        express.setExpressId(saleInvoiceList.get(i).getExpressId());
                        express =  getExpressInfoById(express);
                        saleInvoiceList.get(i).setExpress(express);
                    }
                    if(!StringUtils.isEmpty(saleInvoiceList.get(i).getInvoiceHref())&&!"".equals(saleInvoiceList.get(i).getInvoiceHref())){
                        if(!StringUtils.startsWith(saleInvoiceList.get(i).getInvoiceHref(),"/goods")
                                &&!StringUtils.startsWith(saleInvoiceList.get(i).getInvoiceHref(),"https://open.jsaisino.com/dzfpPdfCx")){
                            saleInvoiceList.get(i).setInvoiceHref("/goods/goods/static/redirect.do?url="+saleInvoiceList.get(i).getInvoiceHref());
                        }
                    }


                }
                if (userIdList.size() > 0) {
                    userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
                    List<User> userList = userMapper.getUserByUserIds(userIdList);
                    map.put("userList", userList);
                }
                if (traderIdList.size() > 0) {
                    traderIdList = new ArrayList<Integer>(new HashSet<Integer>(traderIdList));
                    List<User> traderList = userMapper.getUserByTraderIdList(traderIdList, 1);
                    map.put("traderList", traderList);
                }

                map.put("page", (Page) result.getPage());

                invoice = (Invoice) JSONObject.toBean(JSONObject.fromObject(result_map.get("invoice")), Invoice.class);
                map.put("invoice", invoice);

                if (result_map.get("saleAfterList") != null) {
                    String saleAfterListStr = result_map.get("saleAfterList").toString();
                    json = net.sf.json.JSONArray.fromObject(saleAfterListStr);
                    List<AfterSales> saleAfterList = (List<AfterSales>) json.toCollection(json, AfterSales.class);
                    map.put("saleAfterList", saleAfterList);
                }

                setInvoiceVoucherInfo(saleInvoiceList);

                return map;
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public List<Invoice> getInvoiceInfoByRelatedId(Integer relatedId, Integer id) {
        try {
            Invoice invoice = new Invoice();
            invoice.setRelatedId(relatedId);
            invoice.setType(id);
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<Invoice>>> TypeRef = new TypeReference<ResultInfo<List<Invoice>>>() {};
            String url = httpUrl + "finance/invoice/getinvoiceinfobyrelatedid.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                List<Invoice> list = (List<Invoice>) result.getData();
                for (int i = 0; i < list.size(); i++) {
                    list.get(i).setCreatorName(userMapper.getUserNameByUserId(list.get(i).getCreator()));
                    list.get(i).setValidUserName(userMapper.getUserNameByUserId(list.get(i).getValidUserId()));
                    if(!StringUtils.isEmpty(list.get(i).getInvoiceHref())&&!"".equals(list.get(i).getInvoiceHref())){
                        if(!StringUtils.startsWith(list.get(i).getInvoiceHref(),"/goods")
                                &&!StringUtils.startsWith(list.get(i).getInvoiceHref(),"https://open.jsaisino.com/dzfpPdfCx")){
                            list.get(i).setInvoiceHref("/goods/goods/static/redirect.do?url="+list.get(i).getInvoiceHref());
                        }
                    }
                    // 更新贝登OSS发票图片地址链接
                    if(!StringUtils.isEmpty(list.get(i).getOssFileUrl())&&!"".equals(list.get(i).getOssFileUrl())){
                        if(!StringUtils.startsWith(list.get(i).getOssFileUrl(),"/goods")
                                &&!StringUtils.startsWith(list.get(i).getOssFileUrl(),"https://open.jsaisino.com/dzfpPdfCx")){
                            list.get(i).setOssFileUrl("/goods/goods/static/redirect.do?url="+list.get(i).getOssFileUrl());
                        }
                    }

                }
                return list;
            }
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return null;
    }

    @Override
    public List<InvoiceApply> getSaleInvoiceApplyList(Integer saleorderId, Integer id505, Integer isAdvance) {
        try {
            InvoiceApply invoiceApply = new InvoiceApply();
            invoiceApply.setRelatedId(saleorderId);
            invoiceApply.setType(id505);
            if (isAdvance != -1) {
                invoiceApply.setIsAdvance(isAdvance);
            }
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<InvoiceApply>>> TypeRef =
                    new TypeReference<ResultInfo<List<InvoiceApply>>>() {};
            String url = httpUrl + "finance/invoice/getsaleinvoiceapplylist.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                List<InvoiceApply> list = (List<InvoiceApply>) result.getData();
                for (int i = 0; i < list.size(); i++) {
                    list.get(i).setUserName(userMapper.getUserNameByUserId(list.get(i).getUpdater()));
                    list.get(i).setCreatorNm(userMapper.getUserNameByUserId(list.get(i).getCreator()));
                }
                return list;
            }
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return null;
    }

    @Override
    public InvoiceApply getInvoiceApplyByRelatedId(Integer saleorderId, Integer type, Integer companyId) {
        try {
            InvoiceApply invoiceApply = new InvoiceApply();
            invoiceApply.setRelatedId(saleorderId);
            invoiceApply.setType(type);
            invoiceApply.setCompanyId(companyId);
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<InvoiceApply>> TypeRef = new TypeReference<ResultInfo<InvoiceApply>>() {};
            String url = httpUrl + "finance/invoice/getinvoiceapplybyrelatedid.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (InvoiceApply) result.getData();
            }
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return null;
    }

    @Override
    public ResultInfo<?> saveExpressInvoice(Express express) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Express>> TypeRef = new TypeReference<ResultInfo<Express>>() {};
            String url = httpUrl + "finance/invoice/saveexpressinvoice.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, express, clientId, clientKey, TypeRef);
            if (result != null) {
                return result;
            }
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>(-1, "操作异常");
        }
        return new ResultInfo<>();
    }

    @Override
    public Map<String, Object> getBuyInvoiceAuditList(Invoice invoice, Page page, List<SysOptionDefinition> invoiceTypeList) {
        Map<String, Object> map = null;
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                    new TypeReference<ResultInfo<Map<String, Object>>>() {};
            String url = httpUrl + "finance/invoice/getbuyinvoiceauditlist.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                map = new HashMap<>();
                Map<String, Object> result_map = (Map<String, Object>) result.getData();

                net.sf.json.JSONArray json = null;
                String invoiceStr = result_map.get("invoiceList").toString();
                json = net.sf.json.JSONArray.fromObject(invoiceStr);
                List<Invoice> invoiceList = (List<Invoice>) JSONArray.toCollection(json, Invoice.class);

                // 纠正税率
                for (Invoice item : invoiceList) {
                    if (HxInvoiceConstant.SPECIAL_INVOICE_TYPE_.contains(item.getInvoiceType())) {
                        for (SysOptionDefinition sys : invoiceTypeList) {
                            if (item.getInvoiceType().equals(sys.getSysOptionDefinitionId())) {
                                item.setRatio(new BigDecimal(sys.getComments()));
                                break;
                            }
                        }
                    }
                }

                map.put("invoiceList", invoiceList);

                invoice = (Invoice) JSONObject.toBean(JSONObject.fromObject(result_map.get("invoice")), Invoice.class);
                map.put("invoice", invoice);
                map.put("page", result.getPage());
            }
        } catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public Invoice getInvoiceByNo(Invoice ic) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Invoice>> TypeRef = new TypeReference<ResultInfo<Invoice>>() {};
            String url = httpUrl + "finance/invoice/getinvoicebyno.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, ic, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (Invoice) result.getData();
            }
        }
        catch (IOException e) {
            logger.error("getInvoiceByNo 错误",e);
            return null;
        }
        return null;
    }

    @Override
    public Map<String, Object> getAfterOpenInvoiceListPage(InvoiceApply invoiceApply, Page page) {
        Map<String, Object> map = new HashMap<>();
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<InvoiceApply>>> TypeRef =
                    new TypeReference<ResultInfo<List<InvoiceApply>>>() {};
            String url = httpUrl + "finance/invoice/getafteropeninvoicelistpage.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                List<InvoiceApply> openInvoiceApplyList = (List<InvoiceApply>) result.getData();
                List<Integer> userIdList = new ArrayList<>();
                for (int i = 0; i < openInvoiceApplyList.size(); i++) {
                    userIdList.add(openInvoiceApplyList.get(i).getCreator());
                }
                if (userIdList != null && userIdList.size() > 0) {
                    userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
                    List<User> userList = userMapper.getUserByUserIds(userIdList);
                    map.put("userList", userList);
                }
                map.put("openInvoiceApplyList", openInvoiceApplyList);
                map.put("page", result.getPage());
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public Map<String, Object> getAfterInvoiceApplyVerifyListPage(InvoiceApply invoiceApply, Page page) {
        Map<String, Object> map = new HashMap<>();
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<InvoiceApply>>> TypeRef =
                    new TypeReference<ResultInfo<List<InvoiceApply>>>() {};
            String url = httpUrl + "finance/invoice/getafterinvoiceapplyverifylistpage.htm";
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef, page);
            if (result != null && result.getCode() == 0) {
                List<InvoiceApply> openInvoiceApplyList = (List<InvoiceApply>) result.getData();
                List<Integer> userIdList = new ArrayList<>();
                for (int i = 0; i < openInvoiceApplyList.size(); i++) {
                    userIdList.add(openInvoiceApplyList.get(i).getCreator());
                }
                if (userIdList != null && userIdList.size() > 0) {
                    userIdList = new ArrayList<Integer>(new HashSet<Integer>(userIdList));
                    List<User> userList = userMapper.getUserByUserIds(userIdList);
                    map.put("userList", userList);
                }
                map.put("openInvoiceApplyList", openInvoiceApplyList);
                map.put("page", result.getPage());
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @Override
    public Map<String, Object> getBuyInvoiceConfirmListPage(Invoice invoice, Page page) {
        Map<String, Object> map = new HashMap<>();
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<Invoice>>> typeRef = new TypeReference<ResultInfo<List<Invoice>>>() {};
        String url = httpUrl + "finance/invoice/getbuyinvoiceconfirmlistpage.htm";
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, typeRef, page);
            if (result != null && result.getCode() == 0) {
                map.put("invoiceList", (List<Invoice>) result.getData());
                map.put("page", result.getPage());
            }
        }
        catch (Exception e) {
            LOG.error(Contant.ERROR_MSG, e);
            return null;
        }
        return map;
    }

    @SuppressWarnings("unchecked")
    @Override
    public ResultInfo sendOpenInvoicelist(Invoice invoice, Page page, HttpSession session) throws Exception {
        ResultInfo result = new ResultInfo();
        List<Invoice> mapInvoiceList = null;
        // 获取发送至金蝶银行流水
        ResultInfo<List<Invoice>> resultMap = (ResultInfo<List<Invoice>>) this.sendOpenInvoicelistPage(invoice, page);

        mapInvoiceList = (List<Invoice>) resultMap.getData();
        if (null != mapInvoiceList && mapInvoiceList.size() > 0) {
            // 根据客户id获取归属销售
            getUserByTraderIds(invoice, mapInvoiceList);
        }

        Integer pageCount = resultMap.getPage().getTotalPage();
        for (int i = 2; i <= pageCount; i++) {
            page.setPageNo(i);
            ResultInfo<List<Invoice>> listMap = (ResultInfo<List<Invoice>>) this.sendOpenInvoicelistPage(invoice, page);
            List<Invoice> list = (List<Invoice>) listMap.getData();
            if (null != list && list.size() > 0) {
                getUserByTraderIds(invoice, list);
                // 根据客户id获取归属销售
                mapInvoiceList.addAll(list);
            }
        }

        // 保存发送至金蝶数据至本地库
        if (null != mapInvoiceList && mapInvoiceList.size() > 0) {

            XmlExercise xmlExercise = new XmlExercise();
            Map data = new LinkedHashMap<>();
            data.put("companyid", 1);

            List<Map<String, Object>> dataList = new ArrayList<>();
            for (Invoice invoice2 : mapInvoiceList) {
                // 不允许traderId为null
                if (null == invoice2.getTraderId() || invoice2.getTraderId() == 0) {
                    result.setMessage("推送失败，不允许存在traderId为空的值！");
                    result.setCode(-1);
                    return result;
                }
                invoice2.setUserId(invoice.getUserId());
                Map data1 = new LinkedHashMap<>();
                Map data2 = new HashMap<>();
                data1.put("id", invoice2.getInvoiceId());
                data1.put("date", DateUtil.convertString(invoice2.getAddTime(), "yyyy-MM-dd"));
                invoice2.setAddTimeStr(DateUtil.convertString(invoice2.getAddTime(), "yyyy-MM-dd"));
                data1.put("orderNo", invoice2.getSaleorderNo());
                data1.put("invoiceNo", invoice2.getInvoiceNo());
                data1.put("saler", invoice2.getUserName());
                data1.put("invoiceAmount", invoice2.getAmount());
                data1.put("taxAmount", invoice2.getRatioAmount());
                data1.put("amount", invoice2.getNoRatioAmount());
                data1.put("traderName", invoice2.getTraderName());
                data1.put("traderId", invoice2.getTraderId());
                data1.put("remark", invoice2.getComments());
                data1.put("addTime",DateUtil.convertString(invoice2.getAddTime(), "yyyy-MM-dd"));
                data2.put("info", data1);

                dataList.add(data2);
            }
            data.put("list", dataList);
            String dataXmlStr = xmlExercise.mapToListXml(data, "data");
            String params = CryptBase64Tool.desEncrypt(dataXmlStr, kingdleeKey);
            try {
                logger.info(DateUtil.getNowDate("yyyy-MM-dd HH:mm:ss") + "推送金蝶数据:" + URLEncoder.encode(params, "utf-8"));

                String sendPost = HttpRequest.sendPost(kingdleeOpeninvoiceUrl, "msg=" + URLEncoder.encode(params, "utf-8"));
                Map info = xmlExercise.xmlToMap(sendPost);
                logger.info(DateUtil.getNowDate("yyyy-MM-dd HH:mm:ss") + "推送金蝶数据返回结果:" + JSON.toJSONString(info));
                if (null != info && info.get("code").equals("0")) {
                    // 分批保存，防止数据过多，保存失败2019-08-02
                    if(mapInvoiceList.size() > 500){
                        int size = mapInvoiceList.size();
                        int toIndex = 500;
                        for(int i = 0;i < size;i += 500){
                            if(i + 500 > size){//作用为toIndex最后没有500条数据则剩余几条newList中就装几条
                                toIndex = size - i;
                            }
                            List<Invoice> newList = mapInvoiceList.subList(i, i + toIndex);
                            result = this.saveInvoiceList(newList);
                        }
                    } else {
                        result = this.saveInvoiceList(mapInvoiceList);
                    }
                    page.setTotalRecord(mapInvoiceList.size());
                    result.setPage(page);

                    /*page.setTotalRecord(mapInvoiceList.size());
                    result = this.saveInvoiceList(mapInvoiceList);
                    result.setPage(page);*/
                }
            } catch (Exception e) {
                logger.error("推送失败，对方接口异常！",e);
                result.setCode(-1);
                result.setMessage("推送失败，对方接口异常！");
                return result;
            }
        }
        return result;
    }

    // 根据客户id获取归属销售
    private void getUserByTraderIds(Invoice invoice, List<Invoice> mapInvoiceList) {
        List<Integer> traderIds = new ArrayList<>();
        for (Invoice invoice2 : mapInvoiceList) {
            invoice2.setCompanyId(invoice.getCompanyId());

            // 设置状态
            String status = "";
            switch (invoice2.getColorType()) {
                case 1 :
                    status = "红字";
                    break;
                case 2 :
                    status = "蓝字";
                    break;
            }
            switch (invoice2.getIsEnable()) {
                case 1 :
                    status += "有效";
                    break;
                case 0 :
                    status += "作废";
                    break;
            }
            // 备注
            // VDERP-9644 金蝶推送摘要增加字段（开票摘要添加 开票#+）
            invoice2.setComments("开票#+" + DateUtil.convertString(invoice2.getAddTime(), "yyMMdd")+"#+"+invoice2.getSaleorderNo() + "+" + invoice2.getInvoiceNo() + "#+" + status);

            // 根据traderId查询销售人员
            if (invoice2.getTraderId() != null && invoice2.getTraderId() > 0) {
                traderIds.add(invoice2.getTraderId());
            }
        }
        if (traderIds.size() > 0) {
            List<User> traderUserAndOrgList = userMapper.getTraderUserAndOrgList(traderIds);
            if (null != traderUserAndOrgList && traderUserAndOrgList.size() > 0) {
                for (Invoice invoice2 : mapInvoiceList) {
                    for (User user : traderUserAndOrgList) {
                        if (invoice2.getTraderId() != null) {
                            if (invoice2.getTraderId().equals(user.getTraderId())) {
                                invoice2.setUserName(user.getUsername());
                                // 设置状态
                                String status = "";
                                switch (invoice2.getColorType()) {
                                    case 1 :
                                        status = "红字";
                                        break;
                                    case 2 :
                                        status = "蓝字";
                                        break;
                                }
                                switch (invoice2.getIsEnable()) {
                                    case 1 :
                                        status += "有效";
                                        break;
                                    case 0 :
                                        status += "作废";
                                        break;
                                }
                                // 备注
                                // VDERP-9644 金蝶推送摘要增加字段（开票摘要添加 开票#+）
                                invoice2.setComments("开票#+" + DateUtil.convertString(invoice2.getAddTime(), "yyMMdd")+"#+" +invoice2.getSaleorderNo() + "+" + invoice2.getInvoiceNo() + "#+"
                                        + status + "+" + invoice2.getUserName());
                            }
                        }
                    }
                }
            }
        }
    }

    // 保存发送至金蝶数据至本地库
    private ResultInfo saveInvoiceList(List<Invoice> mapInvoiceList) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {};
        String url = httpUrl + "finance/invoice/saveinvoicelist.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, mapInvoiceList, clientId, clientKey, TypeRef);
            if (result != null) {
                if (result.getCode() == 0) {
                    return result;
                }
            }
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
        }
        return result;
    }

    private ResultInfo<?> sendOpenInvoicelistPage(Invoice invoice, Page page) {
        String url = httpUrl + "finance/invoice/getsendopeninvoicelistpage.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<Invoice>>> TypeRef = new TypeReference<ResultInfo<List<Invoice>>>() {};
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef, page);

            return result;
        }
        catch (IOException e) {
            LOG.error(Contant.ERROR_MSG, e);
        }
        return new ResultInfo<>();
    }

    @SuppressWarnings("unchecked")
    @Override
    public ResultInfo sendIncomeInvoiceList(Invoice invoice, Page page, HttpSession session) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyMMdd");
        ResultInfo result = new ResultInfo();
        List<Invoice> mapInvoiceList = null;

        // 获取发送至金蝶银行流水
        ResultInfo<List<Invoice>> resultMap = (ResultInfo<List<Invoice>>) this.sendIncomeInvoicelistPage(invoice, page);
        mapInvoiceList = (List<Invoice>) resultMap.getData();
        if (null != mapInvoiceList && mapInvoiceList.size() > 0) {
            for (Invoice invoice2 : mapInvoiceList) {
                String createTimeStr="";
                Date date = new Date();
                String[] noString = invoice2.getBuyorderNo().split(",");

                if(invoice2.getCreateTime()!=null && invoice2.getCreateTime()!=0 ){
                    date.setTime(invoice2.getCreateTime());
                    createTimeStr = sdf.format(date);

                }

                if (noString.length > 1) {
                    String buyorderNo = "";
                    buyorderNo = noString[0]+ "等";
//                    buyorderNo += noString[0] + "/";
//                    buyorderNo += noString[1];
                    invoice2.setBuyorderNo(buyorderNo);


                    invoice2.setComments("收票#+" + createTimeStr +"#+" +  buyorderNo + "+" + invoice2.getInvoiceNo() + "#");
                }
                else {
                    // 备注
                    invoice2.setComments("收票#+" + createTimeStr +"#+" + invoice2.getBuyorderNo() + "+" + invoice2.getInvoiceNo() + "#");
                }
            }
        }
        Integer pageCount = resultMap.getPage().getTotalPage();
        for (int i = 2; i <= pageCount; i++) {
            page.setPageNo(i);
            ResultInfo<List<Invoice>> listMap =
                    (ResultInfo<List<Invoice>>) this.sendIncomeInvoicelistPage(invoice, page);
            List<Invoice> list = (List<Invoice>) listMap.getData();
            if (null != list && list.size() > 0) {
                for (Invoice invoice2 : list) {

                    String createTimeStr="";
                    Date date = new Date();
                    if(invoice2.getCreateTime()!=null && invoice2.getCreateTime()!=0 ){
                        date.setTime(invoice2.getCreateTime());
                        createTimeStr = sdf.format(date);

                    }
                    String[] noString = invoice2.getBuyorderNo().split(",");
                    if (noString.length > 1) {
                        String buyorderNo = "";
                        buyorderNo = noString[0]+ "等";
//                        buyorderNo += noString[0] + "/";
//                        buyorderNo += noString[1];
                        invoice2.setBuyorderNo(buyorderNo);

                        invoice2.setComments("收票#+" + createTimeStr +"#+" + buyorderNo + "+" + invoice2.getInvoiceNo() + "#");
                    }
                    else {
                        // 备注
                        invoice2.setComments("收票#+" + createTimeStr +"#+" + invoice2.getBuyorderNo() + "+" + invoice2.getInvoiceNo() + "#");
                    }
                }
                mapInvoiceList.addAll(list);
            }
        }

        // 保存发送至金蝶数据至本地库
        if (null != mapInvoiceList && mapInvoiceList.size() > 0) {

            XmlExercise xmlExercise = new XmlExercise();
            Map data = new LinkedHashMap<>();
            data.put("companyid", 1);

            List<Map<String, Object>> dataList = new ArrayList<>();

            for (Invoice invoice2 : mapInvoiceList) {
                Date date = new Date();
                String createTimeStr="";
                // 不允许traderId为null
                if (null == invoice2.getTraderId() || invoice2.getTraderId() == 0) {
                    result.setMessage("推送失败，不允许存在traderId为空的值！");
                    result.setCode(-1);
                    return result;
                }
                invoice2.setUserId(invoice.getUserId());
                Map data1 = new LinkedHashMap<>();
                Map data2 = new HashMap<>();
                data1.put("id", invoice2.getInvoiceId());
                data1.put("date", DateUtil.convertString(invoice2.getValidTime(), "yyyy-MM-dd"));
                data1.put("type", invoice2.getType());
                invoice2.setAddTimeStr(DateUtil.convertString(invoice2.getValidTime(), "yyyy-MM-dd"));
                data1.put("orderNo", invoice2.getBuyorderNo());
                data1.put("invoiceNo", invoice2.getInvoiceNo());
                data1.put("invoiceAmount", invoice2.getAmount());
                data1.put("taxAmount", invoice2.getRatioAmount());
                data1.put("amount", invoice2.getNoRatioAmount());
                data1.put("traderName", invoice2.getTraderName().replaceAll(" ", ""));
                data1.put("traderId", invoice2.getTraderId());
                data1.put("remark", invoice2.getComments());
                data1.put("ismark", invoice2.getIsAuth());
                data2.put("info", data1);

                dataList.add(data2);
            }

            data.put("list", dataList);
            String dataXmlStr = xmlExercise.mapToListXml(data, "data");
            String params = CryptBase64Tool.desEncrypt(dataXmlStr, kingdleeKey);

            try {
                String sendPost =
                        HttpRequest.sendPost(kingdleeReceinvoiceUrl, "msg=" + URLEncoder.encode(params, "utf-8"));
                Map info = xmlExercise.xmlToMap(sendPost);
                if (null != info && info.get("code").equals("0")) {
                    page.setTotalRecord(mapInvoiceList.size());
                    result = this.saveIncomeInvoicelist(mapInvoiceList);
                    result.setPage(page);
                }
            }
            catch (Exception e) {
                result.setCode(-1);
                result.setMessage("推送失败，对方接口异常！");
                return result;
            }
        }
        return result;
    }

    private ResultInfo saveIncomeInvoicelist(List<Invoice> mapInvoiceList) {
        ResultInfo<?> result = null;
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef =
                new TypeReference<ResultInfo<Map<String, Object>>>() {};
        String url = httpUrl + "finance/invoice/savereceiveinvoicelist.htm";
        try {
            result = (ResultInfo<?>) HttpClientUtils.post(url, mapInvoiceList, clientId, clientKey, TypeRef);
            if (result != null) {
                if (result.getCode() == 0 && result.getData() != null) {
                    return result;
                }
            }
        }
        catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return result;
    }

    private ResultInfo<?> sendIncomeInvoicelistPage(Invoice invoice, Page page) {
        String url = httpUrl + "finance/invoice/getsendreceiveinvoicelistpage.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<Invoice>>> TypeRef = new TypeReference<ResultInfo<List<Invoice>>>() {};
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef, page);

            return result;
        }
        catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return new ResultInfo<>();
    }

    /**
     * <b>Description:</b><br>
     * 保存批量认证
     * 
     * @param invoices
     * @return
     * @Note <b>Author:</b> east <br>
     *       <b>Date:</b> 2018年5月28日 下午4:56:51
     */
    @Override
    public ResultInfo<?> saveBatchAuthentication(List<Invoice> invoices) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<List<Invoice>>> typeRef = new TypeReference<ResultInfo<List<Invoice>>>() {};
        String url = httpUrl + "finance/invoice/savebatchauthentication.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoices, clientId, clientKey, typeRef);
            return result;
        }
        catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public ResultInfo<?> openEInvoicePush(InvoiceApply invoiceApply) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Saleorder>> typeRef = new TypeReference<ResultInfo<Saleorder>>() {};
        String url = httpUrl + "finance/sheinvoice/openeinvoicepush.htm";
        try {
            ResultInfo<?> result =
                    (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, typeRef);
            if (result != null) {
                if (result.getCode() == 0 && result.getData() != null) {// 销售
                    Saleorder saleorder = (Saleorder) result.getData();
                    // 根据客户Id查询客户负责人..
                    List<Integer> userIdList =
                            userMapper.getUserIdListByTraderId(saleorder.getTraderId(), ErpConst.ONE);
                    Map<String, String> map = new HashMap<>();
                    map.put("saleorderNo", saleorder.getSaleorderNo());
                    // 銷售開票后發送消息給銷售負責人
                    MessageUtil.sendMessage(11, userIdList, map,
                            "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId());
                    //更新订单updateDataTime
                    orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_INVOICE);
                }

                try {
                    /**
                     * 前台客户发起的在线开票申请推送开票结果
                     */
                    logger.info("前台客户发起的在线开票申请推送开票结果 invoiceApply:{}", JSON.toJSONString(invoiceApply));
                    InvoiceApply invoiceApplyInfo = invoiceApplyMapper.selectByPrimaryKey(invoiceApply.getInvoiceApplyId());
                    if(InvoiceApplyMethodEnum.ONLINE_INVOICE_OPEN.getApplyMethodCode().equals(invoiceApplyInfo.getApplyMethod())){
                        Saleorder saleOrderById = saleorderMapper.getSaleOrderById(invoiceApplyInfo.getRelatedId());

                        InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
                        invoiceOpenResultDto.setOrderNo(saleOrderById.getSaleorderNo());
                        invoiceOpenResultDto.setInvoiceApplyId(invoiceApply.getInvoiceApplyId());
                        invoiceOpenResultDto.setCode(result.getCode());
                        if (ErpConst.ERROR_CODE.equals(invoiceOpenResultDto.getCode())){
                            invoiceOpenResultDto.setMessage(result.getMessage());
                        }
                        logger.info("前台客户发起的在线开票申请推送开票结果下传 invoiceOpenResultDto:{}", JSON.toJSONString(invoiceOpenResultDto));
                        erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));
                    }
                } catch (Exception e) {
                    logger.error("前台申请电子发票处理推送异常", e);
                }

            }
            return result;
        }
        catch (Exception e) {
            try {
                logger.error(Contant.ERROR_MSG+JsonUtils.translateToJson(invoiceApply), e);
            } catch (Exception ioException) {
               //ignor
            }
            ResultInfo  result=new ResultInfo<>();
            result.setMessage(ExceptionUtils.getFullStackTrace(e));
            return result;
        }
    }

    @Override
    public ResultInfo<?> batchDownEInvoice(Invoice invoice) {
        //用于定义返回的数据存储位置
        invoice.setOnlineInvoicing(1);
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> typeRef = new TypeReference<ResultInfo<?>>() {};
        String url = httpUrl + "finance/sheinvoice/batchdowneinvoice.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, typeRef);
            // VDERP-10439
            if (result != null &&!result.getCode().equals(-1)) {
                LinkedHashMap linkedHashMap = (LinkedHashMap) result.getData();
                List<Map<String, Object>> invoiceInfoList = (List<Map<String, Object>>) linkedHashMap.get("invoiceInfoList");
                List<Integer> invoiceIds = (List<Integer>) linkedHashMap.get("invoiceIds");
                if (!Objects.isNull(invoiceInfoList)) {
                    int templateId;
                    String jumpUrl = "./order/saleorder/view.do?saleorderId=";
                    for (Map<String, Object> stringObjectMap : invoiceInfoList) {
                        //打印日志
                        logger.info("电子发票下载推送消息入参:{}", JSON.toJSONString(stringObjectMap));
                        templateId = result.getCode() == 0 && stringObjectMap.get("status").equals("success") ? 222 : 223;
                        List<Integer> userIds = new ArrayList<>();
                        userIds.add(Integer.valueOf(stringObjectMap.get("userId").toString()));
                        Map<String, String> map = new HashMap<>();
                        map.put("saleorderNo", stringObjectMap.get("saleOrderNo").toString());
                        map.put("invoiceNo", stringObjectMap.get("invoiceNo").toString());
                        //打印日志
                        logger.info("电子发票下载推送消息参数:{}", JSON.toJSONString(map));
                        MessageUtil.sendMessage(templateId, userIds, map, jumpUrl + stringObjectMap.get("saleorderId"));
                    }

                }
                 result = new ResultInfo<>(200, "success", invoiceIds);
            }
            return result;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>();
        }
    }

    @Override
    public ResultInfo<?> cancelEInvoicePush(Invoice invoice) {
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<?>> typeRef = new TypeReference<ResultInfo<?>>() {};
        String url = httpUrl + "finance/sheinvoice/canceleinvoicepush.htm";
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, typeRef);

            logger.info("作废电子发票处理账期逾期编码 invoice:{}", JSON.toJSONString(invoice));
            orderAccountPeriodService.dealAccountPeriodOverdueCodeByRefundInvoice(invoice);
            return result;
        }
        catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>();
        }
    }

    @Override
    public ResultInfo<?> updateExpressInvoice(Express express) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Express>> TypeRef = new TypeReference<ResultInfo<Express>>() {};
            String url = httpUrl + "finance/invoice/updateexpressinvoice.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, express, clientId, clientKey, TypeRef);
            if (result != null) {
                return result;
            }
        }
        catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>(-1, "操作异常");
        }
        return new ResultInfo<>();
    }

    @Override
    public ResultInfo<?> sendInvoiceSmsAndMail(Invoice invoice) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<Map<String, Object>>>> TypeRef = new TypeReference<ResultInfo<List<Map<String, Object>>>>() {};
            String url = httpUrl + "finance/sheinvoice/sendinvoicesmsandmail.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            // VDERP-10439
            if (result != null) {
                List<Map<String, Object>> invoiceInfoList = (List<Map<String, Object>>) result.getData();
                if (!Objects.isNull(invoiceInfoList)) {
                    int templateId;
                    String jumpUrl = "./order/saleorder/view.do?saleorderId=";
                    for (Map<String, Object> stringObjectMap : invoiceInfoList) {
                        templateId = result.getCode() == 0 && stringObjectMap.get("status").equals("success") ? 222 : 223;
                        List<Integer> userIds = new ArrayList<>();
                        userIds.add(Integer.valueOf(stringObjectMap.get("userId").toString()));
                        Map<String, String> map = new HashMap<>();
                        map.put("saleorderNo", stringObjectMap.get("saleOrderNo").toString());
                        map.put("invoiceNo", stringObjectMap.get("invoiceNo").toString());
                        MessageUtil.sendMessage(templateId, userIds, map, jumpUrl + stringObjectMap.get("saleorderId"));
                    }

                }
            }
            return result;
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>(-1, "操作异常");
        }
    }

    @Override
    public List<TraderCustomerVo> getCollectInvoiceTraderName(String traderName) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<TraderCustomerVo>>> TypeRef = new TypeReference<ResultInfo<List<TraderCustomerVo>>>() {};
            String url = httpUrl + "finance/invoice/getcollectinvoicetradername.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderName, clientId, clientKey, TypeRef);
            if (result != null) {
                List<TraderCustomerVo> traderCustomerList = (List<TraderCustomerVo>) result.getData();
                return traderCustomerList;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
        }
        return null;
    }

    @Override
    public ResultInfo<?> updateCollectInvoiceTrader(List<Trader> list) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
            String url = httpUrl + "finance/invoice/updatecollectinvoicetrader.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, list, clientId, clientKey, TypeRef);
            if (result != null) {
                return result;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>(-1, "操作异常");
        }
        return new ResultInfo<>();
    }

    @Override
    public ResultInfo<?> delCollectInvoiceTrader(TraderCustomerVo traderCustomerVo) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
            String url = httpUrl + "finance/invoice/delcollectinvoicetrader.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, traderCustomerVo, clientId, clientKey, TypeRef);
            if (result != null) {
                return result;
            }
        } catch (IOException e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo<>(-1, "操作异常");
        }
        return new ResultInfo<>();
    }

    @Override
    public ResultInfo<?> updateInvoiceApplySign(InvoiceApply invoiceApply) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<?>> TypeRef = new TypeReference<ResultInfo<?>>() {};
            String url = httpUrl + "finance/invoice/updateinvoiceapplysign.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef);
            if (result != null) {
                return result;
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return new ResultInfo<>(-1, "操作异常");
        }
        return new ResultInfo<>();
    }
@Deprecated  //php网站下线
    @Override public ResultInfo<?> sendWxMessageForInvoice(List<Invoice> invoiceList) {
        ResultInfo resultInfo = new ResultInfo();
        try {
            if(null != invoiceList && invoiceList.size() > 0){
              //  LOG.info("发票寄送发送微信模版消息开始....start....invoiceIdList="+ JSONArray.fromObject(invoiceList).toString());
                for(Invoice invoice:invoiceList){
                    // 存在 traderContactId
                    if(null == invoice || null == invoice.getTraderContactId() || CommonConstants.ZERO.equals(invoice.getTraderContactId())) {
                        continue;
                    }
                    WebAccount webaccount = new WebAccount();
                    webaccount.setTraderContactId(invoice.getTraderContactId()==null||invoice.getTraderContactId()==0
                    ?-1:invoice.getTraderContactId());
                    //获取销售单联系人关联的注册用户信息
                    List<WebAccount> webAccountList=webAccountMapper.getWebAccountListByParam(webaccount);
                        //    webAccountService.getWebAccountByTraderContactId(webaccount);
                    for(WebAccount wa:webAccountList){
                        Map data = new HashMap<>();//消息数据
                        //公司名称
                        data.put("companyName",invoice.getTraderName());
                        //开票金额
                        data.put("invoiceAmount",invoice.getAmount());
                        //商品总数量（除去售后数量）
                        data.put("logisticsNo",invoice.getLogisticsNo());
                        data.put("invoiceNo",invoice.getInvoiceNo());

//                        //推送微信消息
//                        ResultInfo res = vedengSoapService.sendWxMessage(data, CommonConstants.WX_TEMPLATE_NO_FP,wa.getUuid());
//                        //如果推送成功则回写T_SALEORDER中的LOGISTICS_WXSEND_SYNC字段
//                        if(res.getCode().equals(0)){
//                            LOG.info("发票寄送发送微信模版消息发送成功.......InvoiceId="+invoice.getInvoiceId());
//                        }else{
//                            LOG.warn("发票寄送发送微信模版消息发送失败.......InvoiceId="+invoice.getInvoiceId());
//                        }
                    }
                }
                resultInfo.setData(invoiceList);
              //  LOG.info("发票寄送发送微信模版消息结束....end....");
            }

            return resultInfo;
        }catch (Exception e){
            LOG.error("发票寄送发送微信模版消息失败 ", e);
            return resultInfo;
        }
    }

    @Override
    public List<Invoice> getInvoiceListByInvoiceIdList(List<Integer> invoiceIdLis) {
        if(null == invoiceIdLis || invoiceIdLis.size() == 0) {
            return null;
        }
        return invoiceMapper.getInvoiceListByInvoiceIdList(invoiceIdLis);
    }

    /** @description: getInvoiceNums.
     * @notes: VDERP-1325 分批开票 获取已开票数量和未开票数量.
     * @author: Tomcat.Hui.
     * @date: 2019/11/8 15:15.
     * @param saleorderNo
     * @return: java.util.Map<java.lang.Integer , java.util.Map < java.lang.String , java.math.BigDecimal>>.
     * @throws: .
     */
    @Override
    public Map<Integer, Map<String, Object>> getInvoiceNums(Saleorder saleorder) {

        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<Integer, Map<String, Object>>>> TypeRef = new TypeReference<ResultInfo<Map<Integer, Map<String, Object>>>>() {
            };
            String url = httpUrl + "finance/invoice/getInvoiceNums.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (Map<Integer, Map<String, Object>>)result.getData();
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return null;
        }
        return null;
    }

    /** @description: getApplyDetailById.
     * @notes: VDERP-1325 分批开票 获取开票申请详情.
     * @author: Tomcat.Hui.
     * @date: 2019/11/21 9:58.
     * @param invoiceApply
     * @return: com.vedeng.finance.model.InvoiceApply.
     * @throws: .
     */
    @Override
    public InvoiceApply getApplyDetailById(InvoiceApply invoiceApply) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<InvoiceApply>> TypeRef = new TypeReference<ResultInfo<InvoiceApply>>() {
            };
            String url = httpUrl + "finance/invoice/getApplyDetailById.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (InvoiceApply)result.getData();
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return null;
        }
        return null;
    }

    /** @description: getInvoiceDetailById.
     * @notes:  VDERP-1325 分批开票 获取发票明细详情.
     * @author: Tomcat.Hui.
     * @date: 2019/11/21 10:03.
     * @param invoice
     * @return: com.vedeng.finance.model.Invoice.
     * @throws: .
     */
    @Override
    public Invoice getInvoiceDetailById(Invoice invoice){
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Invoice>> TypeRef = new TypeReference<ResultInfo<Invoice>>() {
            };
            String url = httpUrl + "finance/invoice/getInvoicedDetailById.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (Invoice)result.getData();
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return null;
        }
        return null;
    }
    /**
     *获取当前订单开票审核状态 1 存在审核中 0 不存在
     * @Author:strange
     * @Date:10:25 2019-11-23
     */
    @Override
    public Integer getInvoiceApplyNowAuditStatus(Integer saleorderId) {
        List<Integer> invoiceApallyIdList = invoiceMapper.getInvoiceApllyNum(saleorderId);
        if(invoiceApallyIdList!=null && invoiceApallyIdList.size()>0){
            return 1;
        }
        return 0;
    }

    public Express getExpressInfoById(Express ex) {
        // 接口调用
        String url = httpUrl + "logistics/express/getexpressinfobyid.htm";
        // 定义反序列化 数据格式
        final TypeReference<ResultInfo<Map<String, Object>>> TypeRef = new TypeReference<ResultInfo<Map<String, Object>>>() {
        };
        try {
            ResultInfo<Express> result2 = (ResultInfo<Express>) HttpClientUtils.post(url, ex, clientId, clientKey,
                    TypeRef);
            if (null == result2) {
                return null;
            }
            Map<String, Object> res = (Map<String, Object>) result2.getData();
            JSONObject jsonObject = JSONObject.fromObject(res.get("express"));
            Express expressInfo = (Express) JSONObject.toBean(jsonObject, Express.class);
            JSONArray jsonArray = JSONArray.fromObject(res.get("expressDetails"));
            List<ExpressDetail> expressDetails = (List<ExpressDetail>) JSONArray.toCollection(jsonArray,
                    ExpressDetail.class);
            expressInfo.setExpressDetail(expressDetails);
            return expressInfo;
        } catch (IOException e) {
            return null;
        }
    }
    @Override
    public InvoiceApply getInvoiceApply(Integer InvoiceApplyId) {
        return invoiceApplyMapper.selectByPrimaryKey(InvoiceApplyId);
    }

    @Override
    public ResultInfo<?> openEInvoiceBatchPush(InvoiceApply invoiceApply, List<Integer> invoiceApplyIdList) {

        final TypeReference<ResultInfo<Saleorder>> typeRef = new TypeReference<ResultInfo<Saleorder>>() {};
        String url = httpUrl + "finance/sheinvoice/openeinvoicepush.htm";
        ResultInfo<?> result =null;
        try {
            for (Integer invoiceApplyId:invoiceApplyIdList ) {
                InvoiceApply invoiceBatchApply=this.getInvoiceApply(invoiceApplyId);
                invoiceApply.setInvoiceApplyId(invoiceBatchApply.getInvoiceApplyId());
                invoiceApply.setRelatedId(invoiceBatchApply.getRelatedId());

                result =
                        (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, typeRef);
                if (result != null) {
                    if (result.getCode() == 0 && result.getData() != null) {// 销售
                        Saleorder saleorder = (Saleorder) result.getData();
                        // 根据客户Id查询客户负责人
                        List<Integer> userIdList =
                                userMapper.getUserIdListByTraderId(saleorder.getTraderId(), ErpConst.ONE);
                        Map<String, String> map = new HashMap<>();
                        map.put("saleorderNo", saleorder.getSaleorderNo());
                        // 銷售開票后發送消息給銷售負責人
                        MessageUtil.sendMessage(11, userIdList, map,
                                "./order/saleorder/view.do?saleorderId=" + saleorder.getSaleorderId());
                        //更新订单updateDataTime
                        orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null,OrderDataUpdateConstant.SALE_ORDER_INVOICE);
                    }

                    try {
                        logger.info("批量前台客户发起的在线开票申请推送开票结果 invoiceApplyId:{}", invoiceApplyId);
                        InvoiceApply invoiceApplyInfo = invoiceApplyMapper.selectByPrimaryKey(invoiceApplyId);
                        if(InvoiceApplyMethodEnum.ONLINE_INVOICE_OPEN.getApplyMethodCode().equals(invoiceApplyInfo.getApplyMethod())){
                            Saleorder saleOrderById = saleorderMapper.getSaleOrderById(invoiceApplyInfo.getRelatedId());

                            InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
                            invoiceOpenResultDto.setOrderNo(saleOrderById.getSaleorderNo());
                            invoiceOpenResultDto.setInvoiceApplyId(invoiceApplyId);
                            invoiceOpenResultDto.setCode(result.getCode());
                            if (ErpConst.ERROR_CODE.equals(invoiceOpenResultDto.getCode())){
                                invoiceOpenResultDto.setMessage(result.getMessage());
                            }
                            logger.info("批量前台客户发起的在线开票申请推送开票结果下传 invoiceOpenResultDto:{}", JSON.toJSONString(invoiceOpenResultDto));
                            erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));
                        }
                    } catch (Exception e) {
                        logger.error("批量开票下传前台客户在线开票异常", e);
                    }

                }
            }
        }
        catch (Exception e) {
            logger.error("批量打印发票操作失败",e);
            return new ResultInfo<>();
        }
        return result;
    }

    /**
     * 更新开票申请信息
     * @Author:strange
     * @Date:15:40 2019-12-30
     */
    @Override
    public InvoiceApply updateInvoiceApplyInfo(InvoiceApply invoiceApply) {
        try {
        logger.info("updateInvoiceApplyInfo start");
        List<InvoiceApplyDetail> invoiceApplyDetailList = invoiceApply.getInvoiceApplyDetails();
        Saleorder saleorder = saleorderMapper.getSaleOrderById(invoiceApply.getRelatedId());
        //开票申请备注
        invoiceApply.setComments(saleorder.getSaleorderNo());
        saleorder.setSaleorderId(invoiceApply.getRelatedId());
        List<SaleorderGoods> saleorderGoodsList =  saleorderGoodsMapper.getSaleorderGoodsById(saleorder);

        //获取商品信息
        Map<Integer,SaleorderGoods> saleorderGoodsMap = saleorderGoodsList.stream().collect(Collectors.toMap(SaleorderGoods::getSaleorderGoodsId, goods->goods));
        Map<Integer, Map<String, Object>> taxNumsMap = getInvoiceNums(saleorder);
        saleorder.setExpressId(invoiceApply.getExpressId());

            //已申请开票数量
            HashMap<Integer, Integer> detailGoodsInvocieAppliesMap = new HashMap<>(16);
            invoiceApplyMapper.getInvoiceAppliesBySaleorderId(saleorder.getSaleorderId()).forEach(invoiceApplyDetail -> {
                detailGoodsInvocieAppliesMap.put(invoiceApplyDetail.getDetailgoodsId(),
                        detailGoodsInvocieAppliesMap.get(invoiceApplyDetail.getDetailgoodsId()) == null ? invoiceApplyDetail.getNum().intValue() :
                                detailGoodsInvocieAppliesMap.get(invoiceApplyDetail.getDetailgoodsId()) + invoiceApplyDetail.getNum().intValue());
            });

        //首次开票申请将运费加入开票申请内
        Integer expressMenonySkuId = addFirstFreightInvoiceApply(taxNumsMap,saleorderGoodsMap,invoiceApplyDetailList);

        for (int i = 0; i < invoiceApplyDetailList.size(); i++) {
            InvoiceApplyDetail invoiceApplyDetail = invoiceApplyDetailList.get(i);
            //跳过运费
            if(invoiceApplyDetail.getDetailgoodsId().equals(expressMenonySkuId)){
                continue;
            }
            //本次申请数量
            BigDecimal nowNum = invoiceApplyDetail.getNum();
            //已申请数量
            Integer oldN = detailGoodsInvocieAppliesMap.get(invoiceApplyDetail.getDetailgoodsId());
            if(oldN == null){
                oldN = 0;
            }
            BigDecimal oldNum = new BigDecimal(oldN);
            SaleorderGoods saleorderGoods = saleorderGoodsMap.get(invoiceApplyDetail.getDetailgoodsId());
            invoiceApplyDetail.setPrice(saleorderGoods.getPrice());
            //总商品数
            BigDecimal allNum = new BigDecimal(saleorderGoods.getNum());
            //本次申请数量+已申请数量>=总商品数   总价 = axSkuRefundAmount-(已申请数量*单价)
            if(nowNum.add(oldNum).compareTo(allNum) >= 0){
                BigDecimal totalAmount = saleorderGoods.getMaxSkuRefundAmount().subtract(oldNum.multiply(saleorderGoods.getPrice()));
                invoiceApplyDetail.setTotalAmount(totalAmount);
            }else{
                //否则  总价 = 单价*本次申请数量
                BigDecimal totalAmount = nowNum.multiply(saleorderGoods.getPrice());
                invoiceApplyDetail.setTotalAmount(totalAmount);
            }

            //判断以申请已开金额+当前申请金额不得大于订单总金额
            Map<String, Object> map = taxNumsMap.get(invoiceApplyDetail.getDetailgoodsId());
            BigDecimal invoiceAmount = new BigDecimal(map.get("INVOICE_AMOUNT").toString());
            BigDecimal applyAmount = new BigDecimal(map.get("APPLY_AMOUNT").toString());
            if(invoiceAmount.add(applyAmount).add(invoiceApplyDetail.getTotalAmount()).compareTo(saleorderGoods.getMaxSkuRefundAmount()) > 0){
                return null;
            }
        }
        Iterator<InvoiceApplyDetail> iterator = invoiceApplyDetailList.iterator();
        while(iterator.hasNext()){
            InvoiceApplyDetail invoiceApplyDetail = iterator.next();
            //如果申请总价为0,则不加入申请中
            if(invoiceApplyDetail.getTotalAmount().compareTo(BigDecimal.ZERO) == 0 ){
                iterator.remove();
            }
            logger.info("updateInvoiceApplyInfo end 开票申请详情:{}",invoiceApplyDetail.toString());
        }
        }catch (Exception e){
            logger.error("updateInvoiceApplyInfo expressId:{} ,error",invoiceApply.getExpressId(),e);
        }
        logger.info("updateInvoiceApplyInfo end expressId:{}",invoiceApply.getExpressId());
        return invoiceApply;
    }
    /**
    * 首次开票申请将运费加入开票申请内
    * @Author:strange
    * @Date:10:04 2020-01-14
    */
    private Integer addFirstFreightInvoiceApply(Map<Integer, Map<String, Object>> taxNumsMap, Map<Integer, SaleorderGoods> saleorderGoodsMap,
                                                List<InvoiceApplyDetail> invoiceApplyDetailList) {
        Integer expressMenonySkuId = 0;
        if(CollectionUtils.isNotEmpty(Collections.singleton(taxNumsMap))){
            //首次开票申请将运费加入开票申请内
            Set<Integer> integers = saleorderGoodsMap.keySet();
            for (Integer saleorderGoodsId : integers) {
                SaleorderGoods saleorderGoods = saleorderGoodsMap.get(saleorderGoodsId);
                if(saleorderGoods.getSku().equals("V127063")){
                    expressMenonySkuId = saleorderGoods.getSaleorderGoodsId();
                    break;
                }
            }
            if(expressMenonySkuId != 0) {
                Map<String, Object> map = taxNumsMap.get(expressMenonySkuId);
                if(CollectionUtils.isNotEmpty(Collections.singleton(map))){
                BigDecimal applyNum = new BigDecimal(map.get("APPLY_NUM").toString());
                BigDecimal invoiceNum = new BigDecimal(map.get("INVOICE_NUM").toString());
                if(applyNum.add(invoiceNum).compareTo(BigDecimal.ZERO) == 0) {
                    InvoiceApplyDetail invoiceApplyDetail = new InvoiceApplyDetail();
                    invoiceApplyDetail.setNum(new BigDecimal(1));
                    invoiceApplyDetail.setDetailgoodsId(expressMenonySkuId);
                    SaleorderGoods saleorderGoods = saleorderGoodsMap.get(expressMenonySkuId);
                    invoiceApplyDetail.setPrice(saleorderGoods.getPrice());
                    invoiceApplyDetail.setTotalAmount(saleorderGoods.getMaxSkuRefundAmount());
                    if (invoiceApplyDetail.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
                        invoiceApplyDetailList.add(invoiceApplyDetail);
                    }
                }
                }
            }
        }
        return expressMenonySkuId;
    }

    /**
     * 票货同行快递单是否可编辑
     * @Author:strange
     * @Date:09:59 2020-01-06
     */
    @Override
    public Boolean getInvoiceApplyByExpressId(Integer expressId) {
      List<InvoiceApply> list =   invoiceApplyMapper.getInvoiceApplyByExpressId(expressId);
      if(CollectionUtils.isNotEmpty(list)) {
          for (InvoiceApply invoiceApply : list) {
              if (invoiceApply.getValidStatus().equals(1)){
                  return true;
              }
          }
      }
        return false;
    }
    /**
     *驳回票货同行未开票申请
     * @Author:strange
     * @Date:10:01 2020-01-06
     */
    @Override
    public void updateInvoiceApplyByExpressId(Integer expressId) {
        logger.info("delInvoiceApplyByExpressId 驳回开票申请expressId:{}",expressId);
        List<InvoiceApply> list =   invoiceApplyMapper.getInvoiceApplyByExpressId(expressId);
        if(CollectionUtils.isNotEmpty(list)) {
            for (InvoiceApply invoiceApply : list) {
                logger.info("delInvoiceApplyByExpressId 驳回开票申请expressId:{},InvoiceApplyId:{}",expressId,invoiceApply.getInvoiceApplyId());
                invoiceApply.setValidStatus(2);
                invoiceApplyMapper.update(invoiceApply);
            }
        }
    }

    /** @description: getInvoiceApplyByExpressId.
     * @notes: VDERP-1039 票货同行 根据快递单ID查询开票申请.
     * @author: Tomcat.Hui.
     * @date: 2020/1/3 17:13.
     * @param expressId
     * @return: com.vedeng.model.finance.InvoiceApply.
     * @throws: .
     */
    @Override
    public List<InvoiceApply> getInvoiceApplyInfoByExpressId(InvoiceApply invoiceApply) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<InvoiceApply>>> TypeRef = new TypeReference<ResultInfo<List<InvoiceApply>>>() {
            };
            String url = httpUrl + "finance/invoice/getInvoiceApplyByExpressId.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoiceApply, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (List<InvoiceApply>)result.getData();
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return null;
        }
        return null;
    }

    /** @description: getInvoiceByApplyId.
     * @notes: VDERP-1039 票货同行 根据开票申请ID获取发票信息.
     * @author: Tomcat.Hui.
     * @date: 2020/1/3 18:09.
     * @param invoiceApplyId
     * @return: com.vedeng.finance.model.InvoiceApply.
     * @throws: .
     */
    @Override
    public List<Invoice> getInvoiceByApplyId(Integer invoiceApplyId) {
        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<List<Invoice>>> TypeRef = new TypeReference<ResultInfo<List<Invoice>>>() {
            };
            String url = httpUrl + "finance/invoice/getInvoiceByApplyId.htm";
            Invoice invoice = new Invoice();
            invoice.setInvoiceApplyId(invoiceApplyId);
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, invoice, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (List<Invoice>)result.getData();
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return null;
        }
        return null;
    }

    @Override
    public boolean isDelExpressByExpressId(Express express) {
        // 496 销售订单
        if(express.getSaleorderId() != null && express.getBusinessType() != null && express.getBusinessType() == 496){
            boolean orderIsGoodsPeer = orderPeerApiService.getOrderIsGoodsPeer(express.getSaleorderId());
            if(orderIsGoodsPeer) {
                List<Express> expressList = new ArrayList<>();
                expressList.add(express);
                List<Express> expressInvoiceInfo = getExpressInvoiceInfo(expressList);
                if (CollectionUtils.isNotEmpty(expressInvoiceInfo)) {
                    for (Express express1 : expressInvoiceInfo) {
                        //以开票则不可删除
                        if (CollectionUtils.isNotEmpty(express1.getInvoiceList())
                                || (express1.getInvoiceApply() != null &&
                                express1.getInvoiceApply().getValidStatus() != null && express1.getInvoiceApply().getValidStatus().equals(1))) {
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }


    /** @description: getInvoicedData.
     * @notes: VDERP-1874 开票中和已开票数量、金额的计算规则变更 获取已被占用数量(已申请+已开票).
     * @author: Tomcat.Hui.
     * @date: 2020/1/16 19:23.
     * @param saleorder
     * @return: java.util.Map<java.lang.Integer , java.math.BigDecimal>.
     * @throws: .
     */
    @Override
    public Map<Integer, BigDecimal> getInvoiceOccupiedAmount(Saleorder saleorder) {

        try {
            // 定义反序列化 数据格式
            final TypeReference<ResultInfo<Map<Integer, BigDecimal>>> TypeRef = new TypeReference<ResultInfo<Map<Integer, BigDecimal>>>() {
            };
            String url = httpUrl + "finance/invoice/getInvoiceOccupiedAmount.htm";
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url, saleorder, clientId, clientKey, TypeRef);
            if (result != null && result.getCode() == 0) {
                return (Map<Integer, BigDecimal>)result.getData();
            }
        } catch (IOException e) {
            logger.error("操作失败", e);
            return null;
        }
        return null;
    }
    @Override
    public List<Express> getExpressInvoiceInfo(List<Express> expressList) {
        if (CollectionUtils.isNotEmpty(expressList)) {
            //获得开票申请
            expressList.stream().forEach( e -> {
                InvoiceApply query = new InvoiceApply();
                query.setExpressId(e.getExpressId());
                List<InvoiceApply> invoiceApplyList = this.getInvoiceApplyInfoByExpressId(query);
                if (null != invoiceApplyList && invoiceApplyList.size() == 1) {
                    InvoiceApply invoiceApply = invoiceApplyList.get(0);
                    e.setInvoiceApply(invoiceApply);
                    if (null != invoiceApply && invoiceApply.getValidStatus().equals(1)){
                        //查询发票信息
                        List<Invoice> invoiceList = this.getInvoiceByApplyId(invoiceApply.getInvoiceApplyId());
                        if (CollectionUtils.isNotEmpty(invoiceList)) {
                            e.setInvoiceList(invoiceList);
                        }
                    }
                    e.setInvoiceApply(invoiceApply);
                }
            });
        }
        return expressList;
    }

    /**
     * 快递是否关联发票
     * @param expressId
     * @return
     */
    @Override
    public Boolean getInvoiceApplyByExpressIdNo(Integer expressId) {
        List<InvoiceApply> list =   invoiceApplyMapper.getInvoiceApplyByExpressId(expressId);
        if(list==null || list.size()==0) {
            return true;
        }
        return false;
    }

    /**
     * 快递是否关联都为不通过发票
     * @param expressId
     * @return
     */
    @Override
    public Boolean getInvoiceApplyByExpressIdFaile(Integer expressId) {
        List<InvoiceApply> list =   invoiceApplyMapper.getInvoiceApplyByExpressIdFaile(expressId);
        if(CollectionUtils.isEmpty(list)) {
            return true;
        }
        return false;
    }

    /**
     * 判断是否为待审核
     * @param expressId
     * @return
     */
    @Override
    public Boolean getInvoiceApplyByExpressIdByValidIsZero(Integer expressId) {
        List<InvoiceApply> list =   invoiceApplyMapper.getInvoiceApplyByExpressId(expressId);
        if(CollectionUtils.isNotEmpty(list)) {
            for (InvoiceApply invoiceApply : list) {
                if (invoiceApply.getValidStatus().equals(0)){
                    return true;
                }
            }
        }
        return false;
    }
    //查询所有开票申请
    @Override
    public List<InvoiceApply> getAllSaleInvoiceApplyList() {

        return invoiceApplyMapper.getAllSaleInvoiceApplyList();
    }
    //根据订单id查找开票记录
    @Override
    public List<Integer> getInvoiceApplyIdsBySaleOrderIds(List saleOrderNoList) {
        return invoiceApplyMapper.getInvoiceApplyIdsBySaleOrderIds(saleOrderNoList);
    }
    //改变标记状态
    @Override
    public int changeIsSign(List<Integer> endInvoiceApplyIds) {
        return invoiceApplyMapper.changeIsSign(endInvoiceApplyIds);
    }

    @Override
    public List<InvoiceDetail> getInvoiceGoodsList(Integer invoiceId) {
        return invoiceDetailMapper.getInvoiceGoodsList(invoiceId);
    }

    @Override
    public boolean checkInvoiceExpress(List<Integer> invoiceIdList) {
      List<Invoice> invoiceList = invoiceMapper.getInvoiceExpress(invoiceIdList);
        for (Invoice invoice : invoiceList) {
            if(invoice.getExpressId() != null){
                return true;
            }
        }
        return false;
    }

    @Override
    public Map<String, Object> getHxInvoiceByFinanceListPage(HxInvoiceSearchDTO searchDTO, Page page, Integer flag) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("page",page);
        map.put("searchDTO",searchDTO);
        List<HxInvoiceVo> hxInvoiceVos = new ArrayList<>();
        Map<String, Object> result = new HashMap<>(16);
        switch (flag){
            case 0:
                setInvoiceTaxRateSearchInfo(searchDTO);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountWithAllPage(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceAllListPage(map);
                hxInvoiceVos.forEach(item -> {
                    BigDecimal recordAmount = hxInvoiceMapper.getRecordedAmountByHxInvoiceId(item.getHxInvoiceId());
                    item.setRecordedAmount(recordAmount == null ? BigDecimal.ZERO : recordAmount);
                });
                break;
            case 1:
                setInvoiceTaxRateSearchInfo(searchDTO);
                searchDTO.setInvoiceStatus(4);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountCommonInfo(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceWaitListPage(map);
                break;
            case 2:
                setInvoiceTaxRateSearchInfo(searchDTO);
                searchDTO.setInvoiceStatus(5);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountCommonInfo(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceCostListPage(map);
                break;
            case 3:
                searchDTO.setInvoiceStatus(6);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountCommonInfo(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceExceptionListPage(map);
                break;
            case 4:
                setInvoiceTaxRateSearchInfo(searchDTO);
                searchDTO.setInvoiceStatus(7);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountCommonInfo(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceNegativeListPage(map);
                break;
            case 5:
                setInvoiceTaxRateSearchInfo(searchDTO);
                searchDTO.setInvoiceStatus(12);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountCommonInfo(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceInvalidListPage(map);
                break;
            default:
                break;
        }
        hxInvoiceVos.parallelStream()
                .forEach(item -> {
                    if (item.getFinanceVoucherNo() != null) {
                        item.setSendResult(1);
                    } else {
                        item.setSendResult(2);
                    }
                    if (item.getValidUserId() != null) {
                        item.setValidUser(userMapper.getUserNameByUserId(item.getValidUserId()));
                    }
                    if (item.getEntryUserId() != null) {
                        item.setEntryUser(userMapper.getUserNameByUserId(item.getEntryUserId()));
                    }
                });
        result.put("page",page);
        result.put("list",hxInvoiceVos);
        return result;
    }

    /**
     * @describe 供应链管理查询航信推送的进项票列表
     * @param searchDTO
     * @param page
     * @param idFlag
     * @param user
     * @return
     * @author: hugo
     * @date: 2020/5/24 19:23
     */
    @Override
    public Map<String, Object> getSupplyHxInvoiceWaitListPage(HxInvoiceSearchDTO searchDTO, Page page,Integer idFlag, User user) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("page",page);
        map.put("searchDTO",searchDTO);

        HashMap<String, Object> result = new HashMap<>(4);
        List<HxInvoiceVo> hxInvoiceVos = null;
        switch (idFlag){
            case 0 :
                //条件查询所有待录票的进项票
                setInvoiceTaxRateSearchInfo(searchDTO);
                hxInvoiceVos = hxInvoiceMapper.getSupplyHxInvoiceWaitOldListPage(map);
                //查询所有采购订单的归属人员
                getAllBuyorderUsers(result, userMapper.getAllBuyorderUser(), "userList");
                break;
            case 1 :
                //条件查询所有审核中的进项票
                if (searchDTO != null && searchDTO.getEntryUser() == null){
                    searchDTO.setEntryUser(user.getUserId());
                }
                hxInvoiceVos = hxInvoiceMapper.getSupplyHxInvoiceVerifyingListPage(map);
                result.put("invoice", hxInvoiceMapper.getSupplyHxInvoiceVerifingCount(map));
                //关联相关的录票人员
                getAllBuyorderUsers(result, userMapper.getAllEntryUser(), "entryUsers");
                break;
            case 2:
                //条件查询所有已审核的进项票
                if (searchDTO != null && searchDTO.getValidStatus() == null){
                    searchDTO.setValidStatus(1);
                }
                hxInvoiceVos = hxInvoiceMapper.getSupplyHxInvoiceVerifiedListPage(map);
                result.put("invoice", hxInvoiceMapper.getSupplyHxInvoiceVerifiedCount());
                //关联相关的录票人员
                getAllBuyorderUsers(result, userMapper.getAllEntryUser(), "entryUsers");
                break;
            case 3:
                //条件查询所有待退票的进项票
                setInvoiceTaxRateSearchInfo(searchDTO);
                hxInvoiceVos = hxInvoiceMapper.getSupplyHxInvoiceWaitRetuenListPage(map);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCount(9));
                //查询所有采购订单的归属人员
                getAllBuyorderUsers(result, userMapper.getAllBuyorderUser(), "userList");
                break;
            case 4:
                //条件查询所有无效票
                setInvoiceTaxRateSearchInfo(searchDTO);
                searchDTO.setInvoiceStatus(12);
                result.put("invoice", hxInvoiceMapper.getHxInvoiceCountCommonInfo(searchDTO));
                hxInvoiceVos = hxInvoiceMapper.getHxInvoiceInvalidListPage(map);
                break;
            default:
                break;
        }
        result.put("list",hxInvoiceVos);
        result.put("page",page);
        return result;
    }

    /**
     * @describe 添加页面相关人员
     * @param result
     * @param allUser
     * @param objectName
     * @author: hugo
     * @date: 2020/5/24 19:23
     */
    private void getAllBuyorderUsers(HashMap<String, Object> result, List<User> allUser, String objectName) {
        List<User> users = allUser;
        result.put(objectName, users);
    }

    /**
     * @describr 保存更新航信的发票状态信息
     * @param hxInvoiceId 航信发票的ID
     * @param invoiceStatus 发票的状态
     * @return
     * @author: hugo
     * @date: 2020/5/26 19:23:18
     */
    @Override
    public Integer saveHxInvoiceStatus(Integer hxInvoiceId, Integer invoiceStatus) {
        if (hxInvoiceId == null || invoiceStatus == null){
            return 0;
        }
        logger.info("保存更新航信的发票状态信息 hxInvoiceId:{},invoiceStatus:{}",hxInvoiceId,invoiceStatus);
        return hxInvoiceMapper.saveHxInvoiceStatus(hxInvoiceId,invoiceStatus);
    }

    /**
     * @describe 获取航信进项票相关商品信息
     * @param hxInvoiceId 航信进项票ID
     * @return
     * @author: hugo
     * @date 2020/5/27 13:30:20
     */
    @Override
    public Map<String, Object> getHxInvoiceById(Integer hxInvoiceId) {
        HashMap<String, Object> params = new HashMap<>(2);
        params.put("hxInvoiceId", hxInvoiceId);

        HashMap<String, Object> result = new HashMap<>(2);
        HxInvoiceVo hxInvoiceVo = hxInvoiceMapper.getHxInvoiceInfoByIdNew(hxInvoiceId);
        hxInvoiceVo.setCanRecordAmount(hxInvoiceVo.getAmount().subtract(hxInvoiceVo.getRecordedAmount()));
        result.put("hxInvoiceVo", hxInvoiceVo);

        List<HxInvoiceDetailVo> hxInvoiceDetailVos = hxInvoiceMapper.getHxInvoiceDetailsByHxInvoiceId(params);
        if (CollectionUtils.isNotEmpty(hxInvoiceDetailVos)) {
            hxInvoiceDetailVos.forEach(
                    hxInvoiceDetailVo -> {
                        hxInvoiceDetailVo.setCanRecordAmount(
                                hxInvoiceDetailVo.getAmount().add(hxInvoiceDetailVo.getTaxAmount()).subtract(hxInvoiceDetailVo.getRecordedAmount()));
                        HashMap<String, Object> queryParams = new HashMap<>(1);
                        queryParams.put("hxInvoiceDetailId", hxInvoiceDetailVo.getHxInvoiceDetailId());
                        hxInvoiceDetailVo.setInvoiceEntryStashVos(hxInvoiceMapper.getInvoiceEntryStashsByDetailId(queryParams));
                    }
            );
        }
        result.put("hxInvoiceDetailVos", hxInvoiceDetailVos);
        return result;
    }

    /**
     * @describe 暂存航信录票信息
     * @param invoiceEntryStash 录票信息
     * @return
     * <AUTHOR>
     * @date 2020/5/27 16:03
     */
    @Override
    public Integer saveInvoiceEntryStash(InvoiceEntryStash invoiceEntryStash) {
        return hxInvoiceMapper.insertInvoiceEntryStash(invoiceEntryStash);
    }

    /**
     * @describe 更新暂存航信发票录票信息
     * @param invoiceEntryStash 录票信息
     * @return
     * <AUTHOR>
     * @date 2020/6/2 9:13:13
     */
    @Override
    public Integer updtateInvoiceEntryStash(InvoiceEntryStash invoiceEntryStash) {
        logger.info("更新暂存航信发票录票信息 invoiceEntryStash:{}" + JSON.toJSONString(invoiceEntryStash));
        return hxInvoiceMapper.updateInvoiceEntryStash(invoiceEntryStash);
    }

    /**
     * @describe 通过ID获取航信发票详情
     * @param hxInvoiceDetailId
     * @return
     * <AUTHOR>
     * @date 2020/6/2 13:05:10
     */
    @Override
    public HxInvoiceDetail getHxInvoiceDetailById(Integer hxInvoiceDetailId) {
        return hxInvoiceMapper.getHxInvoiceDetailById(hxInvoiceDetailId);
    }

    /**
     * @describe 获取航信发票相关信息
     * @param hxInvoiceId 发票ID
     * @return
     * <AUTHOR>
     * @date 2020/6/3 13:17:17
     */
    @Override
    public HxInvoiceVo getHxInvoiceInfoByHxInvocieId(Integer hxInvoiceId) {
        HashMap<String, Object> queryParams = new HashMap<>(1);
        queryParams.put("hxInvoiceId", hxInvoiceId);
        return hxInvoiceMapper.getHxInvoiceInfoById(queryParams);
    }

    /**
     * @describe 根据采购商品ID获取其录票信息
     * @param buyorderGoodsId
     * @return
     * <AUTHOR>
     * @date 2020/6/3 16:38:48
     */
    @Override
    public BuyorderGoodsRecordDTO getBuyorderGoodsRecordDTOByGoodsId(Integer buyorderGoodsId) {
        return hxInvoiceMapper.getBuyorderGoodsRecordDTOByGoodsId(buyorderGoodsId);
    }

    /**
     * @describe 标记航信发票的退票处理状态
     * @param hxInvoice
     * @return
     * <AUTHOR>
     * @date 2020/6/9 15:40:01
     */
    @Override
    public Integer updateInvoiceRefundStatus(HxInvoice hxInvoice) {
        logger.info("标记航信发票的退票处理状态 hxInvoice:{}" + JSON.toJSONString(hxInvoice));
        return hxInvoiceMapper.updateInvoiceRefundStatus(hxInvoice);
    }

    /**
     * @describe 获取航信发票基础信息
     * @param hxInvoiceId 航信发票ID
     * @return
     * <AUTHOR>
     * @date 2020/6/9 17:16:15
     */
    @Override
    public HxInvoice getHxInvocieBaseInfoById(Integer hxInvoiceId) {
        return hxInvoiceMapper.getHxInvocieBaseInfoById(hxInvoiceId);
    }

    /**
     * @describe 删除一条录票暂存信息
     * @param invoiceEntryStashId 录票暂存ID
     * @return
     * <AUTHOR>
     * @date 2020/5/28 9:44:36
     */
    @Override
    public ResultInfo deleteInvoiceEntryStash(Integer invoiceEntryStashId) {
        logger.info("删除一条录票暂存信息 invoiceEntryStashId:{}" + invoiceEntryStashId);
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            if (invoiceEntryStashId != null){
                InvoiceEntryStash invoiceEntryStash = new InvoiceEntryStash();
                invoiceEntryStash.setInvoiceEntryStashId(invoiceEntryStashId);
                invoiceEntryStash.setIsDelete(1);

                if (hxInvoiceMapper.updateInvoiceEntryStash(invoiceEntryStash) > 0){
                    resultInfo.setCode(0);
                    resultInfo.setMessage("操作成功");
                }
            }
        } catch (Exception e) {
            logger.error("deleteInvoiceEntryStash",e);
            resultInfo.setMessage("操作失败");
        }
        return resultInfo;
    }

    /**
     * @describe 条件获取待录票订单相关信息
     * @param buyorderSearchDTO
     * @return
     * <AUTHOR>
     * @date 2020/5/28 15:53:18
     */
    @Override
    public List<BuyorderVo> getInvoiceBuyorderList(BuyorderSearchDTO buyorderSearchDTO) {
        HashMap<String, Object> params = new HashMap<>(1);
        params.put("buyorderSearchDTO",buyorderSearchDTO);
        return hxInvoiceMapper.getInvoiceBuyorderList(params);
    }

    /**
     * @describe 保存航信发票录票详情信息
     * @param currentUser 当前登陆用户
     * @param invoiceEntryStashs 录票详情信息
     * @return
     * <AUTHOR>
     * @date 2020/6/1 11:22:13
     */
    @Override
    public ResultInfo saveHxInvoiceDetails(User currentUser, List<InvoiceEntryStash> invoiceEntryStashs) {
        logger.info("开始进行航信发票录票 invoiceEntryStashs:{}" , JSON.toJSONString(invoiceEntryStashs));
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            //1.同步发票信息到T_INVOICE表
            if (CollectionUtils.isEmpty(invoiceEntryStashs)){
                logger.warn("录票详情信息不全 invoiceEntryStashs:{}", JSON.toJSONString(invoiceEntryStashs));
                resultInfo.setMessage("录票详情信息不全");
                return resultInfo;
            }

            //1.1 获取发票需要维护相关航信发票字段信息
            Integer hxInvoiceId = hxInvoiceMapper.getHxInvoiceDetailById(invoiceEntryStashs.get(0)
                    .getHxInvoiceDetailId()).getHxInvoiceId();
            HxInvoice hxInvoiceInfo = hxInvoiceMapper.getHxInvocieBaseInfoById(hxInvoiceId);
            if (hxInvoiceInfo.getInvoiceStatus() != 1){
                logger.info("同步航信发票信息失败，只有待录票的发票才可以同步 invoiceId:" + hxInvoiceInfo.getHxInvoiceId());
                resultInfo.setMessage("同步航信发票信息失败，只有待录票的发票才可以同步");
                return resultInfo;
            }

            //1.2 获取发票需要相关采购单信息
            BuyorderGoods buyorderInfo = hxInvoiceMapper
                    .getBuyorderGoodsBaseInfoByInvoiceEntryStashId(invoiceEntryStashs.get(0).getInvoiceEntryStashId());
            //1.3 同步发票信息表
            Invoice invoice = getInvoiceFromHxInvoice(currentUser, hxInvoiceInfo, buyorderInfo);
            logger.info("同步发票信息表 invoice:{}" ,JSON.toJSONString(invoice));
            invoiceMapper.insertSelective(invoice);

            //2.同步录票详情信息到T_INVOICE_DETAIL表
            logger.info("同步录票详情信息到T_INVOICE_DETAIL表 invoiceEntryStashs:{}" , JSON.toJSONString(invoiceEntryStashs));
            invoiceEntryStashs.stream().forEach(invoiceEntryStash -> {
                //2.1 获取采购商品信息
                BuyorderGoods buyorderGoodsBaseInfo = hxInvoiceMapper
                        .getBuyorderGoodsBaseInfoByInvoiceEntryStashId(invoiceEntryStash.getInvoiceEntryStashId());
                //2.2 同步发票详情表
                insertInvoiceDetail(invoice, invoiceEntryStash, buyorderGoodsBaseInfo);

                //3.保存航信发票商品和采购单商品关联对照表
                insertHxInvoiceJBuyorderGoodsPO(hxInvoiceId, invoiceEntryStash, buyorderGoodsBaseInfo);
            });

            //4.更新航信发票状态
            logger.info("更新航信发票状态 hxInvoiceInfo:{}" , JSON.toJSONString(hxInvoiceInfo));
            uptdaeHxInvocieStatus(currentUser, hxInvoiceInfo);

            //5.清空航信发票暂存信息
            logger.info("清空航信发票暂存信息 hxInvoiceId:" + hxInvoiceId);
            hxInvoiceMapper.deleteInvoiceEntryStash(hxInvoiceId);
            resultInfo.setCode(0);
        } catch (NumberFormatException e) {
            logger.error("saveHxInvoiceDetails",e);
            resultInfo.setMessage("同步航信发票信息失败");
        }
        return resultInfo;
    }

    /**
     * @describe 更新航信发票状态信息
     * @param currentUser 当前登录用户
     * @param hxInvoiceInfo 航信发票信息
     * <AUTHOR>
     * @date 2020/6/85 14:17:41
     */
    private void uptdaeHxInvocieStatus(User currentUser, HxInvoice hxInvoiceInfo) {
        HxInvoice hxInvoice = new HxInvoice();
        hxInvoice.setHxInvoiceId(hxInvoiceInfo.getHxInvoiceId());
        //发票状态，1待录票，2审核中，3已审核，4待认领，5费用票，6异常票，7负数票，8其他，9：待退票，10:已退票
        hxInvoice.setInvoiceStatus(HxInvoiceStatusEnum.CHECKING.getStatus());
        hxInvoice.setUpdater(currentUser.getUserId());
        hxInvoice.setUpdateTime(System.currentTimeMillis());
        hxInvoiceMapper.updateHxInvoice(hxInvoice);
    }

    /**
     * @describe 保存航信发票商品与采购单商品的关联关系
     * @param hxInvoiceId 航信发票ID
     * @param invoiceEntryStash 发票暂存记录
     * @param buyorderGoodsBaseInfo 采购单商品信息
     * <AUTHOR>
     * @date 2020/6/5 14:10:01
     */
    private void insertHxInvoiceJBuyorderGoodsPO(Integer hxInvoiceId, InvoiceEntryStash invoiceEntryStash, BuyorderGoods buyorderGoodsBaseInfo) {
        HxInvoiceJBuyorderGoodsPO hxInvoiceJBuyorderGoodsPO = new HxInvoiceJBuyorderGoodsPO();
        hxInvoiceJBuyorderGoodsPO.setHxInvoiceId(hxInvoiceId);
        hxInvoiceJBuyorderGoodsPO.setHxInvoiceDetailId(invoiceEntryStash.getHxInvoiceDetailId());
        hxInvoiceJBuyorderGoodsPO.setBuyorderGoodsId(buyorderGoodsBaseInfo.getBuyorderGoodsId());
        hxInvoiceJBuyorderGoodsPO.setCreateTime(System.currentTimeMillis());
        hxInvoiceMapper.insertHxInvoiceJBuyorderGoods(hxInvoiceJBuyorderGoodsPO);
    }

    /**
     * @describe 通过航信发票信息推送发票详情信息
     * @param invoice 发票
     * @param invoiceEntryStash 暂存信息
     * @param buyorderGoodsBaseInfo 采购单商品基础信息
     * <AUTHOR>
     * @date 2020/6/5 14:11:18
     */
    private void insertInvoiceDetail(Invoice invoice, InvoiceEntryStash invoiceEntryStash, BuyorderGoods buyorderGoodsBaseInfo) {
        InvoiceDetail invoiceDetail = new InvoiceDetail();
        invoiceDetail.setInvoiceId(invoice.getInvoiceId());
        invoiceDetail.setDetailgoodsId(buyorderGoodsBaseInfo.getBuyorderGoodsId());
        invoiceDetail.setPrice(buyorderGoodsBaseInfo.getPrice());
        invoiceDetail.setNum(new BigDecimal(invoiceEntryStash.getHasEntryCount()));
        invoiceDetail.setTotalAmount(invoiceDetail.getPrice().multiply(invoiceDetail.getNum()));
        invoiceDetailMapper.insertSelective(invoiceDetail);
    }

    /**
     * 通过航信发票信息封装发票
     * @param currentUser 当前登录用户
     * @param hxInvoiceInfo 航信发票信息
     * @param buyorderInfo 采购单信息
     * @return
     * <AUTHOR>
     * @date 2020/6/5 14:13:28
     */
    private Invoice getInvoiceFromHxInvoice(User currentUser, HxInvoice hxInvoiceInfo, BuyorderGoods buyorderInfo) {
        Invoice invoice = new Invoice();
        invoice.setCompanyId(currentUser.getCompanyId());
        //发票代码
        invoice.setInvoiceCode(hxInvoiceInfo.getInvoiceCode());
        //发票号
        invoice.setInvoiceNum(Integer.parseInt(hxInvoiceInfo.getInvoiceNum()));
        //电子发票 1纸质发票2电子发票
        invoice.setInvoiceProperty(1);
        //发票链接地址
        invoice.setInvoiceHref(hxInvoiceInfo.getAttachment());
        //字典库开票申请类型 采购开票503
        invoice.setType(ErpConst.BUY_ORDER_INVOICE);
        //录票 开票 1开票 2录票
        invoice.setTag(2);
        //关联表ID
        invoice.setRelatedId(buyorderInfo.getBuyorderId());
        //发票号码
        invoice.setInvoiceNo(hxInvoiceInfo.getInvoiceNum());

        //税率
        if (hxInvoiceInfo.getTaxRate() != null){
            invoice.setRatio(new BigDecimal(hxInvoiceInfo.getTaxRate())
                    .divide(new BigDecimal(100))
                    .setScale(2,BigDecimal.ROUND_HALF_DOWN));
            List<SysOptionDefinition> sysOptionDefinitionList = getSysOptionDefinitionList(428);
            for (SysOptionDefinition sysOptionDefinition : sysOptionDefinitionList) {
                if (new BigDecimal(sysOptionDefinition.getComments()).compareTo(invoice.getRatio()) == 0) {
                    switch (hxInvoiceInfo.getInvoiceCategory()){

                        case "0" :{
                            //普通发票
                            if (sysOptionDefinition.getTitle().contains("普通")){
                                //发票类型（字典库）
                                invoice.setInvoiceType(sysOptionDefinition.getSysOptionDefinitionId());
                            }
                            break;
                        }
                        case "1":{
                            //专用发票
                            if (sysOptionDefinition.getTitle().contains("专用")){
                                //发票类型（字典库）
                                invoice.setInvoiceType(sysOptionDefinition.getSysOptionDefinitionId());
                            }
                            break;
                        }
                        default:{
                            logger.warn("航信发票分类匹配错误 hxInvoiceId:{},invoiceCategory:{}",
                                    hxInvoiceInfo.getHxInvoiceId(),hxInvoiceInfo.getInvoiceCategory());
                            break;
                        }
                    }
                }

            }

        }
        //红蓝子  1红  2 蓝
        invoice.setColorType(hxInvoiceInfo.getColorType() != null && hxInvoiceInfo.getColorType() == 2 ? 1 : 2);
        //发票金额
        invoice.setAmount(hxInvoiceInfo.getAmount());
        //是否有效
        invoice.setIsEnable(1);
        //审核 0待审核 1通过 不通过
        invoice.setValidStatus(0);
        //审核时间
        invoice.setValidTime(System.currentTimeMillis());
        //创建信息
        invoice.setCreator(currentUser.getUserId());
        invoice.setAddTime(System.currentTimeMillis());
        //发票来源 默认 0   航信 1
        invoice.setInvoiceFrom(1);
        //航信发票ID
        invoice.setHxInvoiceId(hxInvoiceInfo.getHxInvoiceId());
        return invoice;
    }

    /**
     * @param hxInvoiceId
     * @return
     * @describe 清空当前发票的暂存录票信息
     * <AUTHOR>
     * @date 2020/6/1 15:25:05
     */
    @Override
    public ResultInfo deleteInvoiceDetails(Integer hxInvoiceId) {
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            hxInvoiceMapper.deleteInvoiceEntryStash(hxInvoiceId);
            resultInfo.setCode(0);
        } catch (Exception e) {
            resultInfo.setMessage("删除发票暂存信息异常");
        }
        return resultInfo;
    }

    /**
     * @describe 批量标记航信发票
     * @param hxInvoiceIds 航信发票的ID集合
     * @param invoiceStatus 发票的状态
     * @return
     * <AUTHOR>
     * @date 2020/6/17 20:01:22
     */
    @Override
    public Integer batchSaveHxInvoiceStatus(List<Integer>  hxInvoiceIds, Integer invoiceStatus) {
        HashMap<String, Object> params = new HashMap<>(2);
        params.put("hxInvoiceIds", hxInvoiceIds);
        params.put("invoiceStatus", invoiceStatus);
        return hxInvoiceMapper.batchSaveHxInvoiceStatus(params);
    }

    /**
     * @desc 根据条件查询航信发票（手动录票时使用）
     * @param hxInvoice 航信发票
     * @return
     * <AUTHOR>
     * @date 2020/6/30 16:52:20
     */
    @Override
    public List<HxInvoice> getHxInvoiceInfoByCondition(HxInvoice hxInvoice) {
        if (hxInvoice == null || StringUtil.isBlank(hxInvoice.getInvoiceNum()) ||
                StringUtil.isBlank(hxInvoice.getInvoiceCode())){
           return null;
        }
        return hxInvoiceMapper.getHxInvoiceInfoByCondition(hxInvoice);
    }

    /**
     * 根据发票ID获取发票基本信息
     *
     * @param invoiceIds
     * @return
     */
    @Override
    public List<Invoice> getInvoiceBaseInfoByInvoiceIds(List<Integer> invoiceIds, Integer isHxInvoiceFlag) {
        if (CollectionUtils.isEmpty(invoiceIds)){
            return null;
        }
        List<Invoice> invoices = null;
        switch (isHxInvoiceFlag) {
            case 0 :{
                invoices= invoiceMapper.getInvoiceBaseInfoFromHxByInvoiceIds(invoiceIds);
                break;
            }
            case 1:{
                invoices= hxInvoiceMapper.getHxInvoiceBaseInfoByInvoiceIds(invoiceIds);
                break;
            }
            default:{
                logger.warn("批量认证发票标记存在问题 isHxInvoiceFlag:{}", isHxInvoiceFlag);
                return null;
            }
        }
        return invoices;
    }

    /**
     * 条件获取发票基本信息（发票认证使用）
     *
     * @param invoice
     * @return
     */
    @Override
    public Invoice getInvoiceBaseInfoByCondition(Invoice invoice) {
        logger.info("批量上传发票认证参数信息 invoice:{}", JSON.toJSONString(invoice));
        if (invoice == null || invoice.getInvoiceNo() == null || invoice.getInvoiceCode() == null){
            return null;
        }
        return invoiceMapper.getHxInvoiceBaseInfoByCondition(invoice);
    }

    /**
     * 保存发票接口认证结果信息
     *
     * @param invoice
     * @param hxInvoiceAuthResponse
     * @param isHxInvoiceFlag
     * @return
     */
    @Override
    public int saveInvoiceAuthInfo(Invoice invoice, HxInvoiceAuthResponse hxInvoiceAuthResponse, Integer isHxInvoiceFlag) {
        if (invoice == null || hxInvoiceAuthResponse == null || invoice.getHxInvoiceId() == null ||
                hxInvoiceAuthResponse.getAuthResponse() == null){
            logger.warn("保存认证信息时参数不全 invoice:{},hxInvoiceAuthResponse:{},isHxInvoiceFlag:{}",
                    JSON.toJSONString(invoice), JSON.toJSONString(hxInvoiceAuthResponse), isHxInvoiceFlag);
            return 0;
        }
        logger.info("开始保存发票认证结果，hxInvoiceId:{}, hxInvoiceAuthResponse:{}" , invoice.getHxInvoiceId() ,
                JSON.toJSONString(hxInvoiceAuthResponse));

        Invoice invoiceParam = new Invoice();
        invoiceParam.setHxInvoiceId(invoice.getHxInvoiceId());
        invoiceParam.setAuthMode(1);
        invoiceParam.setAuthTime(System.currentTimeMillis());
        invoiceParam.setModTime(System.currentTimeMillis());
        switch (hxInvoiceAuthResponse.getAuthResponse()){
            case -1 :{
                invoiceParam.setIsAuth(2);
                //航信返回XML过长时直接保存系统异常信息
                if (hxInvoiceAuthResponse.getAuthFailReason().length() > 512){
                    logger.warn("航信发票认证异常  返回失败结果过长! invoiceNo:{}", invoice.getInvoiceNo());
                    invoiceParam.setAuthFailReason("航信发票认证异常  返回失败结果过长! invoiceNo:{}" + invoice.getInvoiceNo());
                    logger.warn("航信发票认证失败原因", hxInvoiceAuthResponse.getAuthFailReason());
                    break;
                }
                invoiceParam.setAuthFailReason(hxInvoiceAuthResponse.getAuthFailReason());
                break;
            }
            case 1:{
                invoiceParam.setIsAuth(1);
                break;
            }
            default:{
                logger.warn("航信认证结果枚举匹配失败 invoiceNo:{}", invoice.getInvoiceNo());
                return 0;
            }
        }
        logger.info("更新发票认证相关信息 invoiceParam:{}, isHxInvoiceFlag:{}", JSON.toJSONString(invoiceParam), isHxInvoiceFlag);


        switch (isHxInvoiceFlag){
            case 0:{
                logger.info("保存发票认证结果为非航信发票 invoiceParam :{}", invoiceParam);
                return invoiceMapper.saveInvoiceAuthInfo(invoiceParam);
            }
            case 1:{
                logger.info("保存发票认证结果航信发票 invoiceParam :{}", invoiceParam);
                return invoiceMapper.saveHxInvoiceAuthInfo(invoiceParam);
            }
            default:{
                logger.error("发票认证标记枚举匹配失败 isHxInvoiceFlag:{}", isHxInvoiceFlag);
               throw new RuntimeException("发票认证标记枚举匹配失败 isHxInvoiceFlag:{}" +  isHxInvoiceFlag);
            }
        }
    }

    /**
     * 根据发票ID检索发票申请相关信息
     *
     * @param invoiceId
     * @return
     */
    @Override
    public InvoiceApply getInvoiceApplyInfoByInvoiceId(Integer invoiceId) {
        return invoiceApplyMapper.getInvoiceApplyInfoByInvoiceId(invoiceId);
    }

    /**
     * 标记发票申请以下传WMS
     *
     * @param invoiceApplyId
     * @return
     */
    @Override
    public int signIsSendWmsFlag(Integer invoiceApplyId) {
        return invoiceApplyMapper.signIsSendWmsFlag(invoiceApplyId);
    }

    /**
     * 获取满足票货同行并且没有推送的发票信息
     *
     * @param invoiceGoodsOrderTypes
     * @return
     */
    @Override
    public List<WmsInvoiceInfoDto> getWmsInvoiceInfoDtos(List<Integer> invoiceGoodsOrderTypes) {
        if (CollectionUtils.isEmpty(invoiceGoodsOrderTypes)){
            logger.info("检索票货同行发票时发票类型不能为空 invoiceGoodsOrderTypes:{}", JSON.toJSONString(invoiceGoodsOrderTypes));
        }
        return invoiceMapper.getWmsInvoiceInfoDtos(invoiceGoodsOrderTypes);
    }

    /**
     * 标记发票是否正在认证的状态
     *
     * @param hxInvoiceIds 发票IDS
     * @param isAuthStatus 认证状态
     * @return
     */
    @Override
    public int signInvoiceAuthStatus(List<Integer> hxInvoiceIds, Integer isAuthStatus, Integer isHxInvoiceFlag) {
        logger.info("标记发票认证状态 hxInvoiceIds:{}, isAuthStatus:{} ,isHxInvoiceFlag:{}" , hxInvoiceIds, isAuthStatus, isHxInvoiceFlag);
        if (CollectionUtils.isEmpty(hxInvoiceIds) || isAuthStatus == null || isHxInvoiceFlag == null){
            return 0;
        }
        switch (isHxInvoiceFlag){
            case 0 :{
                logger.info("标记发票为非航信发票 hxInvoiceIds:{}, isAuthStatus:{}", hxInvoiceIds, isAuthStatus);
                return invoiceMapper.signInvoiceAuthStatus(hxInvoiceIds,isAuthStatus);
            }
            case 1:{
                logger.info("标记发票为航信发票 hxInvoiceIds:{}, isAuthStatus:{}", hxInvoiceIds, isAuthStatus);
                return invoiceMapper.signHxInvoiceAuthStatus(hxInvoiceIds, isAuthStatus);
            }
            default:{
                logger.warn("标记航信发票状态时发票标记异常 isHxInvoiceFlag:{}", isHxInvoiceFlag);
                break;
            }

        }
        return 0;
    }

    /**
     * 保存发票地址信息
     *
     * @param invoiceHrefDownDto
     * @param session
     * @param invoiceHref
     * @return
     */
    @Override
    public int saveInvoiceHref(InvoiceHrefDownDto invoiceHrefDownDto, HttpSession session, String invoiceHref) {
        Invoice invoice = new Invoice();
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        invoice.setInvoiceId(invoiceHrefDownDto.getInvoiceId());
        invoice.setUpdater(user.getUserId());
        invoice.setInvoiceHref(invoiceHref);
        invoice.setModTime(System.currentTimeMillis());

        switch (invoiceHrefDownDto.getInvoiceType()) {
            case "0":{
                logger.info("发票下载图片为销项发票图片 invoice:{}", JSON.toJSONString(invoice));
                return invoiceMapper.saveInvoiceHref(invoice);
            }
            case "1":{
                logger.info("发票下载图片为进项发票图片 invoice:{}", JSON.toJSONString(invoice));
                return  hxInvoiceMapper.saveHxInvoiceHref(invoice);
            }
            default:{
                logger.warn("发票下载图片类型匹配失败 invoiceType:{}", invoiceHrefDownDto.getInvoiceType());
                return 0;
            }
        }
    }

    @Override
    public List<InvoiceDetail> getInvoiceListByRelatedId(List<Integer> relatedIdList) {
        if (CollectionUtils.isEmpty(relatedIdList)){
            return null;
        }
        Invoice invoice = new Invoice();
        invoice.setCompanyId(1);
        invoice.setRelatedIdList(relatedIdList);
        invoice.setType(503);
        return invoiceMapper.getInvoiceListByRelatedId(invoice);
    }

    @Override
    public void dealHxInvoiceInfoByInvoice(Invoice invoice) {
        if (invoice == null) {
            return;
        }
        logger.info("发票录票时开始处理航信发票信息流转状态 invoice:{}", JSON.toJSONString(invoice));

        if (invoice.getInvoiceFrom() == null || invoice.getInvoiceFrom() == 0 || invoice.getHxInvoiceId() == null) {
            logger.info("该发票来源为非航信推送 invoice:{}", JSON.toJSONString(invoice));
            return;
        }
        HxInvoiceVo invoiceInfo = hxInvoiceMapper.getRecordedAmountByInvoiceInfo(invoice.getInvoiceNo(),
                invoice.getHxInvoiceId());
        if (invoiceInfo == null || invoiceInfo.getAmount() == null || invoiceInfo.getRecordedAmount() == null){
            logger.warn("发票审核检索累计录票金额时数据异常 invoiceBaseInfo:{}", JSON.toJSONString(invoice));
            return;
        }
        //累计录票金额大于等于航信发票金额时 —> 已录票 ELSE --> 待录票
        Integer hxInvoiceStatus = null;
        /**
         * VDERP-6585  关于航信发票比对时支持微小差异
          */
        logger.info("录票信息为航信发票 invoiceInfo:{}", JSON.toJSONString(invoiceInfo));
        if (invoiceInfo.getRecordedAmount().compareTo(invoiceInfo.getAmount().subtract(BigDecimal.ONE)) > -1
                && invoiceInfo.getAmount().compareTo(invoiceInfo.getRecordedAmount().subtract(BigDecimal.ONE)) > -1){
            logger.info("发票录票时累计录票金额符合全部录票条件 航信发票状态流转为已录票 hxInvoiceId:{}", invoice.getHxInvoiceId());
            hxInvoiceStatus = HxInvoiceStatusEnum.RECORDED.getStatus();
        } else {
            logger.info("发票录票时累计录票金额不符合全部录票条件 航信发票状态流转为待录票 hxInvoiceId:{}", invoice.getHxInvoiceId());
            hxInvoiceStatus = HxInvoiceStatusEnum.WAIT_RECORD.getStatus();
        }
        logger.info("发票录票时更新航信发票状态信息 getHxInvoiceId:{},hxInvoiceStatus:{}" ,invoice.getHxInvoiceId(),hxInvoiceStatus);
        hxInvoiceMapper.saveHxInvoiceStatus(invoice.getHxInvoiceId(), hxInvoiceStatus);
    }

    /**
     * 处理航信发票拉取处理蓝字作废票
     *
     * @param hxInvoice 已有的蓝字有效票
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void rollBackForBlueValidTicket(HxInvoice hxInvoice) {
        logger.info("处理航信发票拉取处理蓝字作废票 hxInvoice:{}", JSON.toJSONString(hxInvoice));
        if (!Objects.nonNull(hxInvoice)){
            return;
        }

        /**
         * 处理原始录票发票信息
         */
        dealOriginInvoiceInfo(hxInvoice);

        /**
         * 新蓝字作废处理生成负向发票
         */
        rollBackForBlueValidTicketWithPass(hxInvoice);

        /**
         * 处理关联所有订单的收票状态问题
         */
        dealOrderInvoiceStatus(hxInvoice);

        /**
         * 处理进行中的冲销问题
         */
        dealReviewInvoiceReversalInfo(hxInvoice);

        /**
         *  作废发票处理售后信息
         */
        dealAfterSalesByRejectInvoice(hxInvoice);

        /**
         * 处理原始航信发票信息
         */
        dealOriginHxInvoiceInfo(hxInvoice);
    }

    /**
     * 处理关联所有订单的收票状态问题
     *
     * @param hxInvoice
     */
    private void dealOrderInvoiceStatus(HxInvoice hxInvoice) {
        logger.info("蓝字作废处理关联所有订单的收票状态问题 hxInvoice:{}", JSON.toJSONString(hxInvoice));
        try {
            List<Invoice> allRelationInvoiceList = invoiceMapper.getAllRelationInvoiceByInvoiceNoAndCode(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode());

            if (CollectionUtils.isEmpty(allRelationInvoiceList)){
                return;
            }
            logger.info("开始处理关联所有订单的收票状态 allRelationInvoiceList:{}", JSON.toJSONString(allRelationInvoiceList));
            allRelationInvoiceList.forEach(negateInvoiceTarget -> {
                if (InvoiceTypeEnum.BUY_ORDER.getType().equals(negateInvoiceTarget.getType())) {
                    saveBuyorderInvoiceStatus(negateInvoiceTarget.getRelatedId());
                } else if (InvoiceTypeEnum.AFTER_SALE.getType().equals(negateInvoiceTarget.getType())) {
                    restoreAfterSaleInvoice(negateInvoiceTarget);
                } else if (InvoiceTypeEnum.BUY_EXPENSE_ORDER.getType().equals(negateInvoiceTarget.getType())){
                    logger.info("蓝字作废处理费用单售后状态信息 orderId:{}", negateInvoiceTarget.getRelatedId());
                    buyorderExpenseApiService.doBuyOrderExpenseInvoiceStatus(negateInvoiceTarget.getRelatedId());
                }
            });
        } catch (Exception e) {
            logger.error("要处理，蓝字作废处理费用单售后状态信息error hxInvoice:{}", JSON.toJSONString(hxInvoice), e);
        }
    }

    /**
     * 处理进行中的冲销问题
     * @param hxInvoice
     */
    private void dealReviewInvoiceReversalInfo(HxInvoice hxInvoice) {
        try {
            List<Integer> inReviewInvoiceReversalIds = invoiceMapper.getInReviewInvoiceReversalByInvoiceNoAndCode(
                    hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode());

            logger.info("航信作废发票需要驳回的冲销审核ID信息 inReviewInvoiceReversalIds:{}", JSON.toJSONString(inReviewInvoiceReversalIds));
            if (CollectionUtils.isEmpty(inReviewInvoiceReversalIds)){
                return;
            }

            inReviewInvoiceReversalIds.forEach(invoiceReversalId -> {
                InvoiceReversalDto invoiceReversalDto = new InvoiceReversalDto();
                //申请冲销信息
                invoiceReversalDto.setInvoiceReversalId(invoiceReversalId);
                invoiceReversalDto.setAuditComments("此票已作废");
                invoiceReversalDto.setReversalAuditStatus(ErpConst.TWO);
                invoiceReversalDto.setReversalAuditUser(ErpConst.NJ_ADMIN_ID);
                logger.info("航信作废发票驳回冲销审核详情 invoiceReversalDto:{}", JSON.toJSONString(invoiceReversalDto));
                invoiceReversalApiService.updateByPrimaryKeySelective(invoiceReversalDto);
            });
        } catch (Exception e) {
            logger.error("要处理，处理进行中的冲销问题 hxInvoice:{}", JSON.toJSONString(hxInvoice), e);
        }
    }

    /**
     * 处理原始航信发票信息
     * @param hxInvoice
     */
    private void dealOriginHxInvoiceInfo(HxInvoice hxInvoice) {
        logger.info("处理原始航信发票信息 hxInvoice:{}", JSON.toJSONString(hxInvoice));
        HxInvoice updateHxInvoiceTarget = new HxInvoice();
        updateHxInvoiceTarget.setHxInvoiceId(hxInvoice.getHxInvoiceId());

        if (ErpConst.ONE.equals(hxInvoice.getIsAuth())) {
            updateHxInvoiceTarget.setIsAuth(ErpConst.ZERO);
            updateHxInvoiceTarget.setIsAuthing(InvoiceAuthStatusEnum.COMPLETE.getCode());
            updateHxInvoiceTarget.setAuthFailReason("发票已作废，自动修改认证状态");
            updateHxInvoiceTarget.setAuthTime(0L);
        }

        updateHxInvoiceTarget.setInvoiceStatus(
                HxInvoiceStatusEnum.NEGATIVE_NUMBER.getStatus().equals(hxInvoice.getInvoiceStatus()) ?
                        HxInvoiceStatusEnum.NEGATIVE_NUMBER.getStatus() : HxInvoiceStatusEnum.INVALID.getStatus());
        logger.info("最终更新航信蓝色有效票 >> 更新 hxInvoice :{}", JSON.toJSONString(updateHxInvoiceTarget));
        hxInvoiceMapper.updateByPrimaryKeySelective(updateHxInvoiceTarget);
    }

    /**
     * 处理原始录票发票信息
     * @param hxInvoice
     */
    private void dealOriginInvoiceInfo(HxInvoice hxInvoice) {
        //VDERP-10306 【进项票】发票自动作废后需要将已完结采购单更新为进行中
        List<Invoice> invoiceListQuery = invoiceMapper.listInvoiceByInvoiceNoAndCode(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode());
        logger.info("作废当前录票情况汇总 invoiceListQuery:{}", JSON.toJSONString(invoiceListQuery));

        if (CollectionUtils.isEmpty(invoiceListQuery)) {
            return;
        }

        logger.info("发票自动作废后处理订单状态信息 invoiceListQuery:{}", JSON.toJSONString(invoiceListQuery));
        invoiceListQuery.stream().filter(item -> item != null && ErpConst.TWO.equals(item.getTag()) &&
                        (SysOptionConstant.ID_503.equals(item.getType()) || SysOptionConstant.ID_4126.equals(item.getType())))
                .forEach(item -> {
                    if (SysOptionConstant.ID_503.equals(item.getType())){
                        Buyorder buyOrderInfo = buyorderMapper.selectByPrimaryKey(item.getRelatedId());
                        if (buyOrderInfo == null || !ErpConst.TWO.equals(buyOrderInfo.getStatus())) {
                            return;
                        }
                        Buyorder buyOrder4Update = new Buyorder();
                        buyOrder4Update.setBuyorderId(item.getRelatedId());
                        buyOrder4Update.setStatus(ErpConst.ONE);
                        buyorderMapper.updateByPrimaryKeySelective(buyOrder4Update);
                    } else{
                        BuyorderExpenseDto orderMainData = buyorderExpenseApiService.getOrderMainData(item.getRelatedId());
                        if (orderMainData == null || !ErpConst.TWO.equals(orderMainData.getStatus())) {
                            return;
                        }
                        BuyorderExpenseDto buyOrderExpense4Update = new BuyorderExpenseDto();
                        buyOrderExpense4Update.setBuyorderExpenseId(item.getRelatedId());
                        buyOrderExpense4Update.setStatus(ErpConst.ONE);
                        buyorderExpenseApiService.updateBuyorderExpenseStatusByVerity(buyOrderExpense4Update);
                    }
                });

        invoiceListQuery.forEach(invoice -> {
            if (!ErpConst.TWO.equals(invoice.getColorType()) || !ErpConst.ONE.equals(invoice.getIsEnable())) {
                logger.info("蓝字作废处理原始录票信息不为蓝字有效所以跳过 invoice:{}", JSON.toJSONString(invoice));
                return;
            }

            switch (invoice.getValidStatus()){
                // 待审核状态 进行驳回
                case 0:{
                    rejectInvoiceAudit(invoice);
                    break;
                }
                case 1:{
                    Invoice invoiceUpdate = new Invoice();
                    invoiceUpdate.setValidComments("已作废");
                    invoiceUpdate.setInvoiceId(invoice.getInvoiceId());
                    invoiceUpdate.setModTime(new Date().getTime());
                    if (ErpConst.ONE.equals(invoice.getIsAuth())) {
                        invoiceUpdate.setIsAuth(ErpConst.ZERO);
                        invoiceUpdate.setIsAuthing(InvoiceAuthStatusEnum.COMPLETE.getCode());
                        invoiceUpdate.setAuthFailReason("发票已作废，自动修改认证状态");
                        invoiceUpdate.setAuthTime(0L);
                    }
                    logger.info("蓝字作废更新原始录票信息 invoiceUpdate:{}", JSON.toJSONString(invoiceUpdate));
                    invoiceMapper.updateInvoiceByKey(invoiceUpdate);
                    break;
                }
                default:
            }
        });
    }

    /**
     * 作废发票处理售后信息
     *
     * @param hxInvoice
     */
    private void dealAfterSalesByRejectInvoice(HxInvoice hxInvoice){
        if (!Objects.nonNull(hxInvoice)){
            return;
        }
        logger.info("作废发票处理售后信息 hxInvoice:{}", JSON.toJSONString(hxInvoice));

        try {
            //1.采购售后处理
            List<AfterSalesInvoice> unHandleRecordFromHxInvoice = afterSalesInvoiceMapper.getUnHandleRecordByInvoiceNoAndCode(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode());
            if (CollectionUtils.isNotEmpty(unHandleRecordFromHxInvoice)){
                unHandleRecordFromHxInvoice.stream().map(AfterSalesInvoice::getAfterSalesInvoiceId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
                        .forEach(afterSaleInvoiceId -> {
                            AfterSalesInvoice afterSalesInvoice4Update = new AfterSalesInvoice();
                            afterSalesInvoice4Update.setAfterSalesInvoiceId(afterSaleInvoiceId);
                            afterSalesInvoice4Update.setStatus(ErpConst.ONE);
                            logger.info("作废发票处理采购售后记录信息 afterSalesInvoice4Update:{}", JSON.toJSONString(afterSalesInvoice4Update));
                            afterSalesInvoiceMapper.update(afterSalesInvoice4Update);
                        });
                unHandleRecordFromHxInvoice.stream().map(AfterSalesInvoice::getAfterSalesId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
                        .forEach(afterSalesId -> {
                            logger.info("作废发票处理采购售后退票信息 afterSalesId:{}", afterSalesId);
                            buyorderInfoSyncService.calculateAndUpdateInvoiceRefundStatus(afterSalesId);
                        });
            }

            //2.采购费用售后处理
            List<Integer> invoiceIds = invoiceMapper.listInvoiceByInvoiceNoAndCode(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode())
                    .stream().filter(item -> InvoiceTypeEnum.BUY_EXPENSE_ORDER.getType().equals(item.getType()))
                    .map(Invoice::getInvoiceId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(invoiceIds)){
                logger.info("蓝字作废无原始航信发票没有进行录票 invoiceIds:{}", JSON.toJSONString(invoiceIds));
                return;
            }
            logger.info("作废处理原始发票的售后信息 invoiceIds:{}", JSON.toJSONString(invoiceIds));
            expenseAfterSalesApiService.doExpenseAfterSalesInvoiceStatusByInvalid(invoiceIds);
        } catch (Exception e) {
            logger.error("作废发票处理售后信息error hxInvoice:{}", JSON.toJSONString(hxInvoice), e);
        }
    }

    /**
     * 驳回录票审核申请
     * @param invoiceQuery
     */
    private void rejectInvoiceAudit(Invoice invoiceQuery) {
        Objects.requireNonNull(invoiceQuery.getInvoiceId(), "自动驳回发票失败！");

        Invoice invoiceParam = new Invoice();
        invoiceParam.setInvoiceId(invoiceQuery.getInvoiceId());
        invoiceParam.setInvoiceNo(invoiceQuery.getInvoiceNo());
        invoiceParam.setType(invoiceQuery.getType());
        invoiceParam.setIsEnable(invoiceQuery.getIsEnable());
        invoiceParam.setValidStatus(2);
        invoiceParam.setValidComments("发票已作废,自动审核不通过");
        invoiceParam.setColorType(invoiceQuery.getColorType());
        invoiceParam.setUpdater(ErpConst.ADMIN_ID);
        invoiceParam.setModTime(System.currentTimeMillis());
        invoiceParam.setValidTime(System.currentTimeMillis());
        invoiceParam.setValidUserId(ErpConst.ADMIN_ID);

        this.saveInvoiceAudit(invoiceParam);
        // 通知采购
        sendMessageWhenRollBackInvoice(invoiceQuery, 191);
    }


    /**
     * 新蓝字作废处理生成负向发票
     * @param hxInvoice
     */
    private void rollBackForBlueValidTicketWithPass(HxInvoice hxInvoice) {
        if (!Objects.nonNull(hxInvoice)){
            return;
        }

        logger.info("新蓝字作废处理生成负向发票 hxInvoice:{}", JSON.toJSONString(hxInvoice));
        List<InvoiceDetailDto> validInvoiceDetail = invoiceMapper.getValidInvoiceDetailByInvoiceNoAndCode(hxInvoice.getInvoiceNum(), hxInvoice.getInvoiceCode());

        if (CollectionUtils.isEmpty(validInvoiceDetail)){
            logger.info("新蓝字作废生成发票详情信息为空无需生成新发票 hxInvoice:{}", JSON.toJSONString(hxInvoice));
            return;
        }

        logger.info("作废生成负向发票原始数据 validInvoiceDetail:{}", JSON.toJSONString(validInvoiceDetail));

        HashMap<Integer, List<InvoiceDetailDto>> invoiceDetailMap = new HashMap<>(16);
        validInvoiceDetail.forEach(item -> {
            item.setPrice(item.getPrice().abs().negate().stripTrailingZeros());
            item.setTotalAmount(item.getPrice().multiply(item.getInvoiceNum()).stripTrailingZeros());
            if (invoiceDetailMap.containsKey(item.getInvoiceId())){
                invoiceDetailMap.get(item.getInvoiceId()).add(item);
            } else {
                ArrayList<InvoiceDetailDto> invoiceDetails = new ArrayList<>();
                invoiceDetails.add(item);
                invoiceDetailMap.put(item.getInvoiceId(), invoiceDetails);
            }
        });

        if (MapUtils.isEmpty(invoiceDetailMap)){
            logger.warn("作废生成新发票详情集合为空 validInvoiceDetail:{}", JSON.toJSONString(validInvoiceDetail));
            return;
        }

        invoiceDetailMap.forEach((k,v) -> {
            if (CollectionUtils.isEmpty(v)){
                logger.info("有效发票详情为空，本次跳过 k:{}", k);
                return;
            }

            Invoice negateInvoiceTarget = new Invoice();
            BeanUtils.copyProperties(invoiceMapper.selectByPrimaryKey(k), negateInvoiceTarget);
            negateInvoiceTarget.setInvoiceId(null);
            negateInvoiceTarget.setColorType(ErpConst.TWO);
            negateInvoiceTarget.setIsEnable(ErpConst.ZERO);
            negateInvoiceTarget.setAmount(BigDecimal.ZERO);
            negateInvoiceTarget.setIsAuth(ErpConst.ZERO);
            negateInvoiceTarget.setIsAuthing(InvoiceAuthStatusEnum.COMPLETE.getCode());
            negateInvoiceTarget.setAuthFailReason("");
            negateInvoiceTarget.setAuthTime(0L);
            negateInvoiceTarget.setAddTime(System.currentTimeMillis());
            negateInvoiceTarget.setCreator(ErpConst.NJ_ADMIN_ID);
            negateInvoiceTarget.setModTime(System.currentTimeMillis());
            negateInvoiceTarget.setUpdater(ErpConst.NJ_ADMIN_ID);
            negateInvoiceTarget.setValidComments("自动生产蓝字作废票");
            negateInvoiceTarget.setInvoiceFrom(ErpConst.TWO);

            relationAfterSalesId(negateInvoiceTarget);

            ArrayList<InvoiceDetail> invoiceDetails4Save = new ArrayList<>();

            v.forEach(invoiceDetailDto -> {
                InvoiceDetail invoiceDetailTarget = new InvoiceDetail();
                invoiceDetailTarget.setPrice(invoiceDetailDto.getPrice());
                invoiceDetailTarget.setNum(invoiceDetailDto.getInvoiceNum());
                invoiceDetailTarget.setTotalAmount(invoiceDetailDto.getTotalAmount());
                invoiceDetailTarget.setDetailgoodsId(invoiceDetailDto.getDetailGoodsId());
                invoiceDetails4Save.add(invoiceDetailTarget);
                negateInvoiceTarget.setAmount(negateInvoiceTarget.getAmount().add(invoiceDetailDto.getTotalAmount()));
            });

            logger.info("航信蓝字作废生成作废订单发票 invoice :{}", JSON.toJSONString(negateInvoiceTarget));
            invoiceMapper.insertSelective(negateInvoiceTarget);

            invoiceDetails4Save.forEach(invoiceDetail -> {
                invoiceDetail.setInvoiceId(negateInvoiceTarget.getInvoiceId());
                invoiceDetailMapper.insertSelective(invoiceDetail);
            });

            //进项票蓝字作废票在采购售后退票后，数据重复问题解决
            dealBuyOrderBlueInvalidInvoice(negateInvoiceTarget);
        });

        // 消息通知
        invoiceDetailMap.keySet().stream().distinct().forEach(item -> {
            sendMessageWhenRollBackInvoice(invoiceMapper.selectByPrimaryKey(item), 192);
        });
    }

    /**
     * 关联售后单ID
     * @param negateInvoiceTarget
     */
    private void relationAfterSalesId(Invoice negateInvoiceTarget) {
        logger.info("作废关联售后单ID negateInvoiceTarget:{}", JSON.toJSONString(negateInvoiceTarget));
        try {
            Integer afterSalesId = 0;
            if (SysOptionConstant.ID_503.equals(negateInvoiceTarget.getType())){
                afterSalesId = afterSalesMapper.getExpenseAfterSalesIdByCondition(negateInvoiceTarget.getRelatedId(),
                        negateInvoiceTarget.getInvoiceNo(), negateInvoiceTarget.getInvoiceCode());
            } else if (SysOptionConstant.ID_4126.equals(negateInvoiceTarget.getType())){
                afterSalesId = afterSalesMapper.getAfterSalesIdByCondition(negateInvoiceTarget.getRelatedId(),
                        negateInvoiceTarget.getInvoiceNo(), negateInvoiceTarget.getInvoiceCode());
            }
            negateInvoiceTarget.setAfterSalesId(afterSalesId != null ? afterSalesId : 0);
        } catch (Exception e) {
            logger.error("要处理,作废关联售后单ID错误 negateInvoiceTarget:{}", JSON.toJSONString(negateInvoiceTarget), e);
        }
    }

    /**
     * 处理采购订单蓝字作废信息
     *
     * @param negateInvoiceTarget
     */
    private void dealBuyOrderBlueInvalidInvoice(Invoice negateInvoiceTarget) {
        logger.info("处理采购订单蓝字作废信息 negateInvoiceTarget:{}", JSON.toJSONString(negateInvoiceTarget));
        if (!InvoiceTypeEnum.BUY_ORDER.getType().equals(negateInvoiceTarget.getType()) ||
                !ErpConst.TWO.equals(negateInvoiceTarget.getColorType()) ||
                !ErpConst.ZERO.equals(negateInvoiceTarget.getIsEnable())) {
            return;
        }


        List<Invoice> blueInvalidInvoiceByCondition = invoiceMapper.getBlueInvalidInvoiceByCondition(
                negateInvoiceTarget.getInvoiceNo(), negateInvoiceTarget.getInvoiceCode(), negateInvoiceTarget.getRelatedId(), ErpConst.ZERO);
        if (CollectionUtils.isEmpty(blueInvalidInvoiceByCondition)) {
            return;
        }
        logger.info("蓝字作废相关发票信息 blueInvalidInvoiceByCondition:{}", JSON.toJSONString(blueInvalidInvoiceByCondition));
        blueInvalidInvoiceByCondition.forEach(invoiceInfo -> {
            Invoice invoiceUpdate = new Invoice();
            invoiceUpdate.setInvoiceId(invoiceInfo.getInvoiceId());
            invoiceUpdate.setCompanyId(ErpConst.SIX);
            invoiceUpdate.setModTime(new Date().getTime());
            logger.info("蓝字作废相关发票信息 修改发票：{}",JSON.toJSONString(invoiceUpdate));
            invoiceMapper.updateInvoiceByKey(invoiceUpdate);
        });
    }

    /**
     *  触发站内信消息
     * @param invoiceQuery
     * @param messageTemplateId 类型id
     */
    private void sendMessageWhenRollBackInvoice(Invoice invoiceQuery, Integer messageTemplateId) {
        logger.info("作废触发站内信消息 invoiceQuery:{}", JSON.toJSONString(invoiceQuery));
        List<Integer> userIdList = new ArrayList<>();
        if (invoiceQuery.getUpdater() != null && invoiceQuery.getUpdater() > 0) {
            userIdList.add(invoiceQuery.getUpdater());
        }
        if (invoiceQuery.getCreator() != null && invoiceQuery.getCreator() > 0) {
            userIdList.add(invoiceQuery.getCreator());
        }
        if (invoiceQuery.getValidUserId() != null && invoiceQuery.getValidUserId() > 0) {
            userIdList.add(invoiceQuery.getValidUserId());
        }

        if (CollectionUtils.isEmpty(userIdList)) {
            return;
        }
        Map<String, String> paramMap = new HashMap<>(4);
        paramMap.put("invoiceNo", invoiceQuery.getInvoiceNo());

        MessageUtil.sendMessage(messageTemplateId, userIdList, paramMap, "./finance/invoice/getInvoiceListPage.do?searchDateType=1&showValid=2&invoiceNo=" + invoiceQuery.getInvoiceNo());
    }

    /**
     * 处理
     *
     * @param invoice
     */
    private void restoreBuyOrderInvoice(Invoice invoice) {
        if (Objects.isNull(invoice) || Objects.isNull(invoice.getRelatedId())) {
            return;
        }

        int buyOrderId = invoice.getRelatedId();

        Buyorder buyOrderQuery = buyorderMapper.selectByPrimaryKey(buyOrderId);
        if (Objects.isNull(buyOrderQuery) || Objects.equals(buyOrderQuery.getInvoiceStatus(), 0)) {
            return;
        }

        List<Invoice> invoicesByRelatedId = invoiceMapper.listByRelatedId(buyOrderId, InvoiceTypeEnum.BUY_ORDER.getType());

        if (invoicesByRelatedId.size() == 1) {
            // 如果单据详情的发票列表只有当前一张发票，将收票状态改为未收票
            Buyorder updateBuyOrder = new Buyorder();
            updateBuyOrder.setBuyorderId(buyOrderId);
            updateBuyOrder.setInvoiceStatus(0);
            buyorderMapper.updateByPrimaryKeySelective(updateBuyOrder);
        } else if (invoicesByRelatedId.size() > 1 && buyOrderQuery.getInvoiceStatus().equals(2)) {
            // 如果有多张发票，且当前收票状态为全部收票，则将收票状态改为部分收票。
            Buyorder updateBuyOrder = new Buyorder();
            updateBuyOrder.setBuyorderId(buyOrderId);
            updateBuyOrder.setInvoiceStatus(1);
            buyorderMapper.updateByPrimaryKeySelective(updateBuyOrder);
        }
    }

    private void restoreAfterSaleInvoice(Invoice invoice) {
        if (Objects.isNull(invoice) || Objects.isNull(invoice.getRelatedId())) {
            return;
        }

        AfterSales afterSalesQuery = afterSalesMapper.selectByPrimaryKey(invoice.getRelatedId());

        if (Objects.isNull(afterSalesQuery)) {
            return;
        }

        AfterSales queryParam = new AfterSales();
        queryParam.setAfterSalesId(afterSalesQuery.getAfterSalesId());

        AfterSalesDetail afterSalesDetailQuery = afterSalesDetailMapper.selectadtbyid(queryParam);

        if (Objects.isNull(afterSalesDetailQuery)) {
            return;
        }

        List<Invoice> invoicesByRelatedId = invoiceMapper.listByRelatedId(invoice.getRelatedId(), InvoiceTypeEnum.AFTER_SALE.getType());
        if (invoicesByRelatedId.size() == 1) {
            // 如果单据详情的发票列表只有当前一张发票，将收票状态改为未收票
            AfterSalesDetail updateAfterSalesDetail = new AfterSalesDetail();
            updateAfterSalesDetail.setAfterSalesDetailId(afterSalesDetailQuery.getAfterSalesDetailId());
            updateAfterSalesDetail.setReceiveInvoiceStatus(0);
            afterSalesDetailMapper.updateByPrimaryKeySelective(updateAfterSalesDetail);
        } else if (invoicesByRelatedId.size() > 1 && Objects.equals(afterSalesDetailQuery.getReceiveInvoiceStatus(), 2)) {
            // 如果有多张发票，且当前收票状态为全部收票，则将收票状态改为部分收票。
            AfterSalesDetail updateAfterSalesDetail = new AfterSalesDetail();
            updateAfterSalesDetail.setAfterSalesDetailId(afterSalesDetailQuery.getAfterSalesDetailId());
            updateAfterSalesDetail.setReceiveInvoiceStatus(1);
            afterSalesDetailMapper.updateByPrimaryKeySelective(updateAfterSalesDetail);
        }

    }


    @Override
    public List<HxInvoiceConfigVo> getHxInvoiceConfig() {
        List<HxInvoiceConfig> hxInvoiceConfigs = hxInvoiceConfigMapper.getHxInvoiceConfig();
        if (CollectionUtils.isNotEmpty(hxInvoiceConfigs)){
            List<HxInvoiceConfigVo> hxInvoiceConfigVoList = new ArrayList<>();
            hxInvoiceConfigs.stream().forEach(item -> {
                HxInvoiceConfigVo hxInvoiceConfigVo = new HxInvoiceConfigVo();
                BeanUtils.copyProperties(item,hxInvoiceConfigVo);
                hxInvoiceConfigVoList.add(hxInvoiceConfigVo);
            });
            return hxInvoiceConfigVoList;
        }
        return new ArrayList<>();
    }

    @Override
    public ResultInfo<?> getHxInvoiceInfo(Invoice invoice) {
        logger.info("手动录票检查发票信息 invoice:{}", JSON.toJSONString(invoice));
        HxInvoice hxInvoice = hxInvoiceMapper.getRecentHxInvoiceInfoByCondition(invoice.getInvoiceCode(), invoice.getInvoiceNo());
        if (hxInvoice == null){
            return null;
        }
        //维护ERP发票信息和航信发票信息关联关系
        invoice.setHxInvoiceId(hxInvoice.getHxInvoiceId());
        invoice.setInvoiceFrom(ErpConst.ONE);

        switch (hxInvoice.getInvoiceStatus()) {
            case 3: {
                return new ResultInfo<>(-1, "该发票航信已审核通过，无法再次录票。");
            }
            case 4: {
                return new ResultInfo<>(-1, "该发票航信已推送，处于待认领环节，请联系财务处理。");
            }
            case 6: {
                return new ResultInfo<>(-1, "该发票航信已推送，已被标记为异常发票，请联系财务处理。");
            }
            case 7: {
                return new ResultInfo<>(-1, "该发票为负数票，无法录票。");
            }
            case 12:{
                return new ResultInfo<>(-1, "当前发票已经作废，不可录入！");
            }
        }
        return null;
    }

    @Override
    public List<Invoice> getInvoiceExpressInfo(List<Integer> invoiceIdList) {
        return invoiceMapper.getInvoiceExpressInfo(invoiceIdList);
    }

    @Override
    public void refreshHxInvoiceStatus(Integer hxInvoiceId) {
        if (hxInvoiceId == null || ErpConst.ZERO.equals(hxInvoiceId)){
            return;
        }
        logger.info("发票审核状态航信发票信息处理 hxInvoiceId:{}", hxInvoiceId);
        HxInvoiceVo hxInvoiceRecordInfo = hxInvoiceMapper.getHxInvoiceRecordInfoByHxInvoiceId(hxInvoiceId);
        if (hxInvoiceRecordInfo == null){
            logger.warn("刷新航信发票状态已录信息异常 hxInvoiceId:{}", hxInvoiceId);
            return;
        }

        Integer invoiceStatus = hxInvoiceRecordInfo.getRecordedAmount().add(BigDecimal.ONE).compareTo(hxInvoiceRecordInfo.getAmount()) > -1 ?
                HxInvoiceStatusEnum.RECORDED.getStatus() : HxInvoiceStatusEnum.WAIT_RECORD.getStatus();
        if (HxInvoiceStatusEnum.RECORDED.getStatus().equals(invoiceStatus) && HxInvoiceStatusEnum.RECORDED.getStatus() > hxInvoiceRecordInfo.getInvoiceStatus()){
            logger.info("流转航信发票录票信息 getHxInvoiceId:{},hxInvoiceStatus:{}" ,hxInvoiceId, invoiceStatus);
            hxInvoiceMapper.saveHxInvoiceStatus(hxInvoiceId, invoiceStatus);
        } else if (HxInvoiceStatusEnum.RECORDED.getStatus().equals(hxInvoiceRecordInfo.getInvoiceStatus()) && HxInvoiceStatusEnum.RECORDED.getStatus() > invoiceStatus){
            logger.info("流转航信发票录票信息 getHxInvoiceId:{},hxInvoiceStatus:{}" ,hxInvoiceId, invoiceStatus);
            hxInvoiceMapper.saveHxInvoiceStatus(hxInvoiceId, invoiceStatus);
        }

    }

    @Override
    public List<BuyorderVo> getInvoiceBuyOrderList(BuyorderVo buyorderVo, Invoice invoice) {
        InvoiceSaveSearchDto invoiceSaveSearchDto = new InvoiceSaveSearchDto();
        invoiceSaveSearchDto.setOrderNo(buyorderVo.getBuyorderNo());
        invoiceSaveSearchDto.setModel(buyorderVo.getModel());
        invoiceSaveSearchDto.setGoodsName(buyorderVo.getGoodsName());
        invoiceSaveSearchDto.setBrandName(buyorderVo.getBrandName());
        invoiceSaveSearchDto.setTraderName(buyorderVo.getTraderName());
        invoiceSaveSearchDto.setInvoiceType(buyorderVo.getInvoiceType());
        invoiceSaveSearchDto.setInsideComments(buyorderVo.getInsideComments());

        switch (HxInvoiceStatusEnum.getInstanceByCode(buyorderVo.getSearchInvoiceType())){
            /**
             * I 采购票
             * 1.采购商品；
             * 2.采购费用商品
             * 3.处理整合并整合采购单信息
             */
            case WAIT_RECORD:{
                List<Integer> buyOrderIdsWithEligible = buyorderMapper.getBuyOrderIdsWithEligible(invoiceSaveSearchDto);
                if (CollectionUtils.isEmpty(buyOrderIdsWithEligible)){
                    logger.info("采购录票条件筛选后没有符合条件的采购单 invoiceSaveSearchDto:{}", JSON.toJSONString(invoiceSaveSearchDto));
                    return null;
                }

                invoiceSaveSearchDto.setIsBelongBuyOrder(ErpConst.ONE);
                List<BuyorderGoodsVo> buyOrderGoodsList4InvoiceSave = buyorderMapper.getBuyOrderGoodsList4InvoiceSave(invoiceSaveSearchDto);

                InvoiceSaveSearchDto invoiceSaveSearch4Expense = new InvoiceSaveSearchDto();
                invoiceSaveSearch4Expense.setIsBelongBuyOrder(ErpConst.ONE);
                List<BuyorderExpenseItemDto> expenseGoodsList4InvoiceSave = buyorderExpenseApiService.getExpenseGoodsList4InvoiceSave(invoiceSaveSearch4Expense);

                // VDERP-16204 已入库数量 - 已录票数量 - 返利数量; 返利数量=入库数量*(返利单价/单价)
//                buyOrderGoodsList4InvoiceSave = buyOrderGoodsList4InvoiceSave.stream().filter(this::calculateQuantity).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(buyOrderGoodsList4InvoiceSave) && CollectionUtils.isEmpty(expenseGoodsList4InvoiceSave)){
                    return null;
                }

                List<Integer> orderIds = new ArrayList<>();
                HashMap<Integer, List<BuyorderGoodsVo>> buyOrderGoodsInfoMap = new HashMap<>(16);
                HashMap<Integer, List<BuyorderExpenseItemDto>> expenseGoodsInfoMap = new HashMap<>(16);

                if (CollectionUtils.isNotEmpty(buyOrderGoodsList4InvoiceSave)){
                    orderIds.addAll(buyOrderGoodsList4InvoiceSave.stream().map(BuyorderGoodsVo::getBuyorderId).collect(Collectors.toList()));
                    buyOrderGoodsList4InvoiceSave.forEach(buyOrderGoodsVo -> {
                        Integer buyOrderId = buyOrderGoodsVo.getBuyorderId();
                        if (buyOrderGoodsInfoMap.containsKey(buyOrderId)){
                            buyOrderGoodsInfoMap.get(buyOrderId).add(buyOrderGoodsVo);
                        } else {
                            ArrayList<BuyorderGoodsVo> buyOrderGoodsVos = new ArrayList<>();
                            buyOrderGoodsVos.add(buyOrderGoodsVo);
                            buyOrderGoodsInfoMap.put(buyOrderId, buyOrderGoodsVos);
                        }
                    });
                }
                if (CollectionUtils.isNotEmpty(expenseGoodsList4InvoiceSave)){
                    orderIds.addAll(expenseGoodsList4InvoiceSave.stream().map(BuyorderExpenseItemDto :: getBuyOrderId).collect(Collectors.toList()));
                    expenseGoodsList4InvoiceSave.forEach(expenseGoods -> {
                                Integer buyOrderId = expenseGoods.getBuyOrderId();
                                if (expenseGoodsInfoMap.containsKey(buyOrderId)){
                                    expenseGoodsInfoMap.get(buyOrderId).add(expenseGoods);
                                } else {
                                    ArrayList<BuyorderExpenseItemDto> buyOrderExpenseItemList = new ArrayList<>();
                                    buyOrderExpenseItemList.add(expenseGoods);
                                    expenseGoodsInfoMap.put(buyOrderId, buyOrderExpenseItemList);
                                }
                            }
                    );
                }

                List<Integer> buyOrderIdList = orderIds.stream().filter(buyOrderId -> buyOrderId != null && buyOrderId > 0 &&
                        buyOrderIdsWithEligible.contains(buyOrderId)).distinct().collect(Collectors.toList());

                if (CollectionUtils.isEmpty(buyOrderIdList)){
                    return null;
                }

                List<BuyorderVo> buyOrderInfo = buyorderMapper.getBuyOrderInfoByOrderIds(buyOrderIdList);


                if (CollectionUtils.isEmpty(buyOrderInfo)){
                    return  null;
                }

                buyOrderInfo.forEach(item -> {
                    Integer key = item.getBuyorderId();
                    if (buyOrderGoodsInfoMap.containsKey(key)){
                        item.setBuyorderGoodsVoList(buyOrderGoodsInfoMap.get(key));
                    }
                    if (expenseGoodsInfoMap.containsKey(key)){
                        item.setBuyorderExpenseItemDtos(expenseGoodsInfoMap.get(key));
                    }
                });
                return buyOrderInfo.stream()
                        .filter(item -> CollectionUtils.isNotEmpty(item.getBuyorderGoodsVoList()) ||
                                CollectionUtils.isNotEmpty(item.getBuyorderExpenseItemDtos()))
                        .sorted(Comparator.comparing(BuyorderVo :: getPaymentTime).reversed())
                        .collect(Collectors.toList());
            }

            /**
             * II 费用票
             * 条件筛选费用单以及费用单商品信息
             */

            case COST:{
                return getAllExpenseOrderInfo( invoiceSaveSearchDto);
            }
            /**
             * 手工录票部分
             */
            default:{
                /**
                 * 采购订单录票部分
                 */
                if (SysOptionConstant.ID_503.equals(buyorderVo.getSearchOrderType())){
                    List<BuyorderGoodsVo> buyOrderGoodsList4InvoiceSave = buyorderMapper.getBuyOrderGoodsList4InvoiceSave(invoiceSaveSearchDto);
//                    buyOrderGoodsList4InvoiceSave = buyOrderGoodsList4InvoiceSave.stream().filter(this::calculateQuantity).collect(Collectors.toList());

                    if (CollectionUtils.isEmpty(buyOrderGoodsList4InvoiceSave)){
                        return null;
                    }
                    HashMap<Integer, List<BuyorderGoodsVo>> buyOrderGoodsInfoMap = new HashMap<>(16);
                    buyOrderGoodsList4InvoiceSave.forEach(buyOrderGoodsVo -> {
                        Integer buyOrderId = buyOrderGoodsVo.getBuyorderId();
                        if (buyOrderGoodsInfoMap.containsKey(buyOrderId)){
                            buyOrderGoodsInfoMap.get(buyOrderId).add(buyOrderGoodsVo);
                        } else {
                            ArrayList<BuyorderGoodsVo> buyOrderGoodsVos = new ArrayList<>();
                            buyOrderGoodsVos.add(buyOrderGoodsVo);
                            buyOrderGoodsInfoMap.put(buyOrderId, buyOrderGoodsVos);
                        }
                    });

                    List<BuyorderVo> buyOrderInfo = buyorderMapper.getBuyOrderInfoByOrderIds(
                            buyOrderGoodsList4InvoiceSave.stream().map(BuyorderGoodsVo::getBuyorderId)
                                    .filter(Objects::nonNull).distinct().collect(Collectors.toList()));
                    buyOrderInfo.forEach(buyOrderVo -> {
                        if (buyOrderGoodsInfoMap.containsKey(buyOrderVo.getBuyorderId())){
                            buyOrderVo.setBuyorderGoodsVoList(buyOrderGoodsInfoMap.get(buyOrderVo.getBuyorderId()));
                        }
                    });
                    return buyOrderInfo.stream().sorted(Comparator.comparing(Buyorder::getPaymentTime).reversed()).collect(Collectors.toList());
                }
                return getAllExpenseOrderInfo( invoiceSaveSearchDto);
            }
        }
    }
    
    @Override
    public List<BuyorderGoodsVo> getBuyOrderInvoiceInfo(InvoiceSaveSearchDto invoiceSaveSearchDto){
        List<BuyorderGoodsVo> buyOrderGoodsList4InvoiceSave = buyorderMapper.getBuyOrderGoodsList4InvoiceSave(invoiceSaveSearchDto);
        return buyOrderGoodsList4InvoiceSave;
    }

    /**
     * 判断 入库数量 - 已录票数量 - 返利数量 > 0 是否成立
     *
     * @param buyorderGoodsVo
     * @return
     */
    private Boolean calculateQuantity(BuyorderGoodsVo buyorderGoodsVo) {
        // 返利数量
        BigDecimal rebateNum = buyorderGoodsVo.getRebatePrice().multiply(new BigDecimal(buyorderGoodsVo.getArrivalNum())).divide(buyorderGoodsVo.getPrice(), 2, BigDecimal.ROUND_HALF_UP);
        // 入库数量 - 已录票数量 - 返利数量 > 0
        return new BigDecimal(buyorderGoodsVo.getArrivalNum()).subtract(buyorderGoodsVo.getInvoicedNum()).subtract(rebateNum).compareTo(BigDecimal.ZERO) > 0;
    }


    @Override
    public Invoice getInvoiceBaseInfoById(Integer invoiceId) {
        return invoiceMapper.selectByPrimaryKey(invoiceId);
    }

    @Override
    public ModelAndView invoiceConfirm(ModelAndView mv, SaleorderUserInfoDto userInfo, Integer saleOrderId) {
        //订单合同回传是否审核通过
        Integer contractVerifyStatus = this.contractVerifyStatus(saleOrderId);
        String contractVerifyInfo;
        boolean firstWarn = false;
        if (null == contractVerifyStatus){
            contractVerifyInfo = "当前订单回传合同未上传。";
        }else {
            switch (contractVerifyStatus){
                case 5:
                    contractVerifyInfo = "当前订单回传合同未上传。";
                    break;
                case 4:
                    contractVerifyInfo = "回传合同已上传，未提交审核。";
                    break;
                case 0:
                    contractVerifyInfo = "回传合同正在审核中。";
                    break;
                case 1:
                    contractVerifyInfo = "回传合同已审核通过。";
                    firstWarn = true;
                    break;
                case 2:
                    contractVerifyInfo = "回传合同审核不通过。";
                    break;
                default:
                    contractVerifyInfo = "当前订单回传合同未上传。";
            }
        }
        mv.addObject("contractVerifyInfo",contractVerifyInfo);
        mv.addObject("firstWarn",firstWarn);
        //确认单是否审核通过
        boolean secondWarn = this.secondWarn(saleOrderId);
        mv.addObject("secondWarn",secondWarn);
        //当前帐号职位是否为'销售工程师'(阿波罗管理职位)
        boolean positionMatch = this.positionMatch(userInfo);
        //能否继续申请开票
        mv.addObject("invoiceConfirm", (firstWarn && secondWarn) || !positionMatch);
        return mv;
    }

    private Integer contractVerifyStatus(Integer saleOrderId){
        return saleorderDataMapper.contractVerifyStatusBySaleOrderId(saleOrderId);
    }

    private boolean secondWarn(Integer saleOrderId){
        Integer confirmationFormAudit =saleorderMapper.confirmationFormAuditBySaleOrderId(saleOrderId);
        return "2".equals(String.valueOf(confirmationFormAudit));
    }

    private boolean positionMatch(SaleorderUserInfoDto userInfo) {
        if (StringUtil.isNotBlank(invoicePositionLimit) && StringUtil.isNotBlank(userInfo.getPositionName())) {
            String[] split = invoicePositionLimit.split(",");
            for (String s : split) {
                if (s.trim().equals(userInfo.getPositionName())) {
                    logger.info("查询用户职位，用户id:{},职位:{}",userInfo.getUserId(),userInfo.getPositionName());
                    return true;
                }else {
                    logger.info("查询用户职位，未查询到结果，用户id:{}",userInfo.getUserId());
                    return false;
                }
            }
        }
        return false;
    }

    @Override
    public InvoiceTimeDto getInvoiceInfoByInvoiceId(Integer invoiceId){
        return invoiceMapper.getInvoiceInfoByInvoiceId(invoiceId);
    }

    @Override
    public String getTraderName(Integer traderId){
        Trader trader = traderMapper.getTraderByTraderId(traderId);
        return trader.getTraderName();
    }

    @Override
    public void saveCheckRule(SaveCheckRuleDto saveCheckRuleDto) {
        Integer salesOrderAutoInvoice = saveCheckRuleDto.getSalesOrderAutoInvoice();
        if (ObjectUtil.isNotEmpty(salesOrderAutoInvoice)) {
            SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
            sysOptionDefinition.setSysOptionDefinitionId(InvoiceApplyCheckRuleEnum.SALES_ORDER_AUTO_INVOICE.getCode());
            sysOptionDefinition.setStatus(salesOrderAutoInvoice);
            logger.info("保存开票规则，入参:{}",JSON.toJSONString(sysOptionDefinition));
            sysOptionDefinitionMapper.updateByPrimaryKeySelective(sysOptionDefinition);
        }
        Integer afterSalesOrderAutoInvoice = saveCheckRuleDto.getAfterSalesOrderAutoInvoice();
        if (ObjectUtil.isNotEmpty(afterSalesOrderAutoInvoice)) {
            SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
            sysOptionDefinition.setSysOptionDefinitionId(InvoiceApplyCheckRuleEnum.AFTER_SALES_ORDER_AUTO_INVOICE.getCode());
            sysOptionDefinition.setStatus(afterSalesOrderAutoInvoice);
            logger.info("保存开票规则，入参:{}",JSON.toJSONString(sysOptionDefinition));
            sysOptionDefinitionMapper.updateByPrimaryKeySelective(sysOptionDefinition);
        }
        Integer confirmationEffectiveOnlyLast = saveCheckRuleDto.getConfirmationEffectiveOnlyLast();
        if (ObjectUtil.isNotEmpty(confirmationEffectiveOnlyLast)) {
            SysOptionDefinition sysOptionDefinition = new SysOptionDefinition();
            sysOptionDefinition.setSysOptionDefinitionId(InvoiceApplyCheckRuleEnum.CONFIRMATION_EFFECTIVE_ONLY_LAST.getCode());
            sysOptionDefinition.setStatus(confirmationEffectiveOnlyLast);
            logger.info("保存开票规则，入参:{}",JSON.toJSONString(sysOptionDefinition));
            sysOptionDefinitionMapper.updateByPrimaryKeySelective(sysOptionDefinition);
        }
    }

    @Override
    public Boolean updateInvoiceInfo(InvoiceTimeDto invoice) {
        // 财务修改的日期落表
        Invoice updateInvoice = new Invoice();
        updateInvoice.setInvoiceId(invoice.getInvoiceId());
        updateInvoice.setAddTime(invoice.getAddTime());
        updateInvoice.setAuthTime(invoice.getAuthTime());
        updateInvoice.setValidTime(invoice.getValidTime());
        updateInvoice.setModTime(new Date().getTime());
        try {
            invoiceMapper.updateInvoiceByKey(updateInvoice);
            logger.info("修改发票申请、认证、审核日期落表{}", JSON.toJSONString(updateInvoice));
        } catch (Exception e) {
            logger.error("修改发票日期异常：updateInvoiceInfo ", e);
            return false;
        }
        // 是否推送过金蝶
        boolean isPushed = invoiceVoucherApiService.isPushedKingdee(invoice.getInvoiceId());
        // 推送过金蝶，申请时间修改，可以推送金蝶
        if (isPushed && ObjectUtils.notEmpty(invoice.getAddTimeLabel()) && !invoice.getAddTimeLabel().equals(invoice.getAddTimeStr())) {
            updateInvoice = invoiceMapper.getInvoiceBaseInfoByInvoiceId(invoice.getInvoiceId());
            invoice.setRelatedId(updateInvoice.getRelatedId());
            invoice.setInvoiceNo(updateInvoice.getInvoiceNo());
            invoice.setType(updateInvoice.getType());
            // 反审核，修改数据，推送金蝶
            updatePushKingdee(invoice);
        }
        return true;
    }


    /**
     * 云星空接口调用的前置条件：
     * 1、收票记录”是否推送金蝶（云星空）“为”是“
     * 2、该条收票记录的审核日期发生了修改。
     */
    private void updatePushKingdee(InvoiceTimeDto invoice) {
        // 四种订单发票类型需要请求金蝶接口
        // 发票类型
        Integer type = null;
        // 专票类型
        final List<Integer> SPECIAL_INVOICE_TYPE = Arrays.asList(688, 249, 682, 684, 686, 972, 1758);
        // 售后订单
        Saleorder saleorder = saleorderMapper.selectByPrimaryKey(invoice.getRelatedId());
        if (saleorder != null) {
            type = saleorder.getInvoiceType();
        }
        if (InvoiceTypeEnum.AFTER_SALE.getType().equals(invoice.getType())) {
            AfterSalesDto afterSalesDto = afterSalesMapper.queryAftersalesInfoById(invoice.getAfterSalesId());
            // 采购售后退票类型
            if(afterSalesDto != null && SysOptionConstant.ID_548.equals(afterSalesDto.getType())){
                List<ReturnBuyorderInvoiceDto> returnBuyorderInvoiceDtos = afterSalesInvoiceMapper.queryAfterBuyorderInvoice(invoice.getAfterSalesId());
                for (ReturnBuyorderInvoiceDto returnBuyorderInvoiceDto : returnBuyorderInvoiceDtos) {
                    if (returnBuyorderInvoiceDto != null && invoice.getInvoiceNo().equals(returnBuyorderInvoiceDto.getInvoiceNo())) {
                        if (SPECIAL_INVOICE_TYPE.contains(type)) {
                            // 采购增值税专用发票接口
                            PurchaseVatSpecialInvoiceDto invoiceDto =
                                    new PurchaseVatSpecialInvoiceDto(KingDeeBizEnums.updatePVSInvoice);
                            invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                            invoiceDto.setFdate(invoice.getAddTimeLabel());
                            // 反审核、修改及推送数据
                            kingDeePVSInvoiceApiService.register(invoiceDto);
                        } else if (type != null) {
                            // 采购增值税普通发票接口
                            PurchaseVatPlainInvoiceDto invoiceDto = new PurchaseVatPlainInvoiceDto(KingDeeBizEnums.updatePVPInvoice);
                            invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                            invoiceDto.setFdate(invoice.getAddTimeLabel());
                            // 反审核、修改及推送数据
                            kingDeePVPInvoiceApiService.register(invoiceDto);
                        }
                        return;
                    }
                }
            }
            // 其他售后发票类型
            if (SPECIAL_INVOICE_TYPE.contains(type)) {
                // 进项费用专用发票接口
                InPutFeeSpecialInvoiceDto invoiceDto = new InPutFeeSpecialInvoiceDto(KingDeeBizEnums.updateIFSInvoice);
                invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                invoiceDto.setFdate(invoice.getAddTimeLabel());
                // 反审核、修改及推送数据
                kingDeeIFSInvoiceApiService.register(invoiceDto);
            } else if (type != null) {
                // 进项费用普通发票接口
                InPutFeePlainInvoiceDto invoiceDto = new InPutFeePlainInvoiceDto(KingDeeBizEnums.updateIFPInvoice);
                invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                invoiceDto.setFdate(invoice.getAddTimeLabel());
                // 反审核、修改及推送数据
                kingDeeIFPInvoiceApiService.register(invoiceDto);
            }

            // 采购订单
        } else if (InvoiceTypeEnum.BUY_ORDER.getType().equals(invoice.getType())) {
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(invoice.getRelatedId());
            if (buyorder != null) {
                type = buyorder.getInvoiceType();
                if (SPECIAL_INVOICE_TYPE.contains(type)) {
                    // 采购增值税专用发票接口
                    PurchaseVatSpecialInvoiceDto invoiceDto = new PurchaseVatSpecialInvoiceDto(KingDeeBizEnums.updatePVSInvoice);
                    invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                    invoiceDto.setFdate(invoice.getAddTimeLabel());
                    kingDeePVSInvoiceApiService.register(invoiceDto);
                } else if (type != null) {
                    // 采购增值税普通发票接口
                    PurchaseVatPlainInvoiceDto invoiceDto = new PurchaseVatPlainInvoiceDto(KingDeeBizEnums.updatePVPInvoice);
                    invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                    invoiceDto.setFdate(invoice.getAddTimeLabel());
                    kingDeePVPInvoiceApiService.register(invoiceDto);
                }
            }

            // 采购费用订单
        } else if (InvoiceTypeEnum.BUY_EXPENSE_ORDER.getType().equals(invoice.getType())) {
            BuyorderExpenseDto buyorderExpense = buyorderExpenseApiService.getOrderMainData(invoice.getRelatedId());
            if (buyorderExpense != null) {
                type = buyorderExpense.getInvoiceType();
                if (SPECIAL_INVOICE_TYPE.contains(type)) {
                    // 进项费用专用发票接口
                    InPutFeeSpecialInvoiceDto invoiceDto = new InPutFeeSpecialInvoiceDto(KingDeeBizEnums.updateIFSInvoice);
                    invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                    invoiceDto.setFdate(invoice.getAddTimeLabel());
                    kingDeeIFSInvoiceApiService.register(invoiceDto);
                } else if (type != null) {
                    // 进项费用普通发票接口
                    InPutFeePlainInvoiceDto invoiceDto = new InPutFeePlainInvoiceDto(KingDeeBizEnums.updateIFPInvoice);
                    invoiceDto.setFQzokBddjtid(invoice.getInvoiceId().toString());
                    invoiceDto.setFdate(invoice.getAddTimeLabel());
                    kingDeeIFPInvoiceApiService.register(invoiceDto);
                }
            }
        }
    }

    @Override
    public void insertApplyReasonSnapshot(InvoiceApplyReasonSnapshotDto dto) {
        invoiceApplyMapper.insertApplyReasonSnapshot(dto);
    }
}
