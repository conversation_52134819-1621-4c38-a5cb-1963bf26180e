package com.wms.service.processor.output;

import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.model.OssInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.impl.OssUtilsServiceImpl;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.WmsImagesService;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.stockcalculate.SaleorderOutCaculateImpl;
import com.wms.service.util.WMSFTPUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName AfterSaleOutProcessor.java
 * @Description TODO 销售售后换货单出库处理器
 * @createTime 2020年08月19日 17:54:00
 */
@Service
public class AfterSaleOutPutProcessor extends AbstractOutputOrderProcessor {
    Logger logger= LoggerFactory.getLogger(AfterSaleOutPutProcessor.class);

    @Autowired
    @Qualifier("saleorderOutCaculateImpl")
    private SaleorderOutCaculateImpl stockinfoCaculateInterface;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private WMSFTPUtil wmsftpUtil;

    @Autowired
    private WmsImagesService wmsImagesService;

    @Override
    protected String getBusinessKey(OutputDto requestBean) {
        return requestBean.getOrderNo();
    }
    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.EXCHANG_SALEORDER.equals(requestBean.getOrderType())){
            throw new Exception("出库单:"+requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"的类型错误!");
        }
        if(CollectionUtils.isEmpty(requestBean.getDetails())){
            throw new Exception("出库单:"+requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"的详情为空!");
        }
    }
    @Override
    protected void updateOrderData(OutputDto requestBean) {
        logger.info("销售售后换货单出库 info:{}",requestBean.toString());
        String afterNo = requestBean.getSOReference1();
        List<OutputGoodDto> inputOrderGoodsList = requestBean.getDetails();

        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterNo);
        AfterSalesGoods afterGoods = new AfterSalesGoods();
        afterGoods.setAfterSalesId(afterSales.getAfterSalesId());

       Map<Integer,AfterSalesGoodsVo> afterSalesGoodsVoMap = afterSalesGoodsMapper.getAftersalesGoodsList(afterGoods)
               .stream()
               .collect(Collectors.toMap(AfterSalesGoodsVo::getAfterSalesGoodsId,item -> item));
       Map<Integer,Integer> outNumMap = new HashMap<>();
        for (OutputGoodDto outputGoodDto : inputOrderGoodsList) {
            Integer detailId = Integer.valueOf(outputGoodDto.getUserDefine1());
            Integer outNum = outNumMap.get(detailId);
            if(outNum == null){
                outNum = 0;
            }
            outNumMap.put(detailId,outputGoodDto.getQtyShipped().intValue()+outNum);

        }
        for (Integer detailId : outNumMap.keySet()) {
            AfterSalesGoodsVo afterSalesGoodsVo = afterSalesGoodsVoMap.get(detailId);
            Integer outNum = outNumMap.get(detailId);
            AfterSalesGoods updateGoods = new AfterSalesGoods();
            updateGoods.setAfterSalesGoodsId(afterSalesGoodsVo.getAfterSalesGoodsId());
            updateGoods.setDeliveryNum(afterSalesGoodsVo.getDeliveryNum() + outNum);
            updateGoods.setDeliveryStatus(afterSalesGoodsVo.getNum() <= updateGoods.getDeliveryNum() ? 2 : 1);
            afterSalesGoodsMapper.updateByPrimaryKeySelective(updateGoods);
        }

    }
    /**
     * 自定义的相关处理
     * @param requestBean
     */
    @Override
    protected void customHandle(OutputDto requestBean) {
        String afterNo = requestBean.getSOReference1();

        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterNo);
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(afterSales.getOrderId());
        warehouseStockService.updateOccupyStockService(saleorder,0);

        dealReportImage(requestBean,afterSales);
    }

    private void dealReportImage(OutputDto requestBean, AfterSales afterSales) {
        try {
            wmsImagesService.dealReportImage(requestBean, SysOptionConstant.QUALITY_REPORT_AFTERORDER);
        }catch (Exception e){
            logger.error("WMS 推送质检报告下载error 售后单号:"+afterSales.getAfterSalesNo(),e);
        }
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.ORDER_WAREHOUSE_CHANGE_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.AFTER_SALEORDER_TYPE.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.valueOf(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }


}
