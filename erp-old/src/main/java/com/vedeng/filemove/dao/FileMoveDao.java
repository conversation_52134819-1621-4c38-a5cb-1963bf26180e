package com.vedeng.filemove.dao;

import com.vedeng.goods.model.Brand;
import com.vedeng.goods.model.GoodsAttachment;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.model.TraderCertificate;
import com.vedeng.trader.model.TraderFinance;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FileMoveDao {

    /**
     * 客户资质相关sql
     * @param start
     * @param end
     * @return
     */
    List<TraderCertificate> getTraderCertificateByParam(@Param("start") Integer start,@Param("end") Integer end);

    List<TraderCertificate> getTraderCertificatesByPage(@Param("start") Integer start, @Param("limit") Integer limit);

    void updateTraderCertificate(TraderCertificate certificate);


    /**
     * 品牌相关sql
     * @param start
     * @param end
     * @return
     */
    List<Brand> getBrandByParam(@Param("start") Integer start,@Param("end") Integer end);

    List<Brand> getBrandByPage(@Param("start") Integer start, @Param("limit") Integer limit);

    void updateBrand(Brand brand);

    /**
     * 更新商品附件的SQL
     * @param goodsAttachment
     */
    void updateGoodsAttachment(GoodsAttachment goodsAttachment);

    List<GoodsAttachment> getGoodsAttachmentByPage(@Param("start") Integer start, @Param("limit") Integer limit);

    List<GoodsAttachment> getGoodsAttachmentByParam(@Param("start") Integer start,@Param("end") Integer end);


    /**
     * 附件信息
     * @param start
     * @param limit
     * @return
     */
    List<Attachment> getAttachmentByPage(@Param("start") Integer start, @Param("limit") Integer limit);

    List<Attachment> getAttachmentByParam(@Param("start") Integer start,@Param("end") Integer end);

    void updateAttachment(Attachment attachment);


    /**
     * 客户财务信息
     * @param start
     * @param limit
     * @return
     */
    List<TraderFinance> getTraderFinanceByPage(@Param("start") Integer start, @Param("limit") Integer limit);

    List<TraderFinance> getTraderFinanceByParam(@Param("start") Integer start,@Param("end") Integer end);

    void updateTraderFinance(TraderFinance traderFinance);

}
