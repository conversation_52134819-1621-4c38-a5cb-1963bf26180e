package com.vedeng.erp.trader.service;

import com.vedeng.erp.business.dto.PublicCustomerRecordDto;

import java.util.List;

/**
 * api接口
 */
public interface PublicCustomerRecordApiService {

    /**
     * <AUTHOR>
     * @desc 创建商机/新增沟通记录解锁客户
     * @param traderId
     * @param relatedId
     * @param relatedType 1 商机 2 沟通记录
     * @param updater
     */
    void unLockTrader(Integer traderId,Integer relatedId,Integer relatedType, Integer updater);

    PublicCustomerRecordDto getPublicCustomerRecordDto(Integer traderCustomerId);
    
    Integer getPublicNum(Integer userId,Integer areaId);

    List<PublicCustomerRecordDto> getTopOne(List<Integer> traderId);

    void updateOrgUserId(Long publicCustomerRecordId, Integer originUserId);
}
