package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderGoodsDto;
import java.util.Collection;import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/2/6
 * @apiNote
 */
public interface BatchSaleorderGoodsDtoMapper {
    int deleteByPrimaryKey(Integer saleorderGoodsId);

    int insert(BatchSaleorderGoodsDto record);

    int insertOrUpdate(BatchSaleorderGoodsDto record);

    int insertOrUpdateSelective(BatchSaleorderGoodsDto record);

    int insertSelective(BatchSaleorderGoodsDto record);

    BatchSaleorderGoodsDto selectByPrimaryKey(Integer saleorderGoodsId);

    int updateByPrimaryKeySelective(BatchSaleorderGoodsDto record);

    int updateByPrimaryKey(BatchSaleorderGoodsDto record);

    int updateBatch(List<BatchSaleorderGoodsDto> list);

    int updateBatchSelective(List<BatchSaleorderGoodsDto> list);

    int batchInsert(@Param("list") List<BatchSaleorderGoodsDto> list);

    List<BatchSaleorderGoodsDto> findBySaleorderId(@Param("saleorderId") Integer saleorderId);

    List<BatchSaleorderGoodsDto> getBySaleorderId(@Param("saleorderId") Integer saleorderId);

    List<BatchSaleorderGoodsDto> findBySaleorderGoodsIdIn(@Param("saleorderGoodsIdCollection") Collection<Integer> saleorderGoodsIdCollection);
    BatchSaleorderGoodsDto selectBysaleorderGoodsId(Integer saleorderGoodsId);

    /**
     * 查询商品是否是虚拟商品
     * @param goodsId
     * @return
     */
    Integer selectIsVirtualGoods(Integer goodsId);

    List<BatchSaleorderGoodsDto> findBySkuInAndSaleorderId(@Param("skuCollection") Collection<String> skuCollection, @Param("saleorderNo") String saleorderNo);


}