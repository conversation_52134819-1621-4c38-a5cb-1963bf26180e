<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.AuthorizationMapper">

	<resultMap id="BaseResultMap" type="com.vedeng.order.model.AuthorizationApply" >
		<id column="AUTHORIZATION_APPLY_ID" property="authorizationApplyId" jdbcType="INTEGER" />
		<result column="AUTHORIZATION_APPLY_NUM" property="authorizationApplyNum" jdbcType="VARCHAR" />
		<result column="QUOTEORDER_ID" property="quoteorderId" jdbcType="INTEGER" />
		<result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
		<result column="PURCHASE_OR_BIDDING" property="purchaseOrBidding" jdbcType="VARCHAR" />
		<result column="PRODUCT_COMPANY" property="productCompany" jdbcType="VARCHAR" />
		<result column="NATURE_OF_OPERATION" property="natureOfOperation" jdbcType="INTEGER" />
		<result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
		<result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
		<result column="SKU_MODEL" property="skuModel" jdbcType="VARCHAR" />
		<result column="DISTRIBUTIONS_TYPE" property="distributionsType" jdbcType="INTEGER" />
		<result column="AUTHORIZED_COMPANY" property="authorizedCompany" jdbcType="VARCHAR" />
		<result column="PURCHASE_PROJECT_NAME" property="purchaseProjectName" jdbcType="VARCHAR" />
		<result column="PURCHASE_PROJECT_NUM" property="purchaseProjectNum" jdbcType="VARCHAR" />
		<result column="FILE_TYPE" property="fileType" jdbcType="INTEGER" />
		<result column="AFTERSALES_COMPANY" property="aftersalesCompany" jdbcType="VARCHAR" />
		<result column="BEGIN_TIME" property="beginTime" jdbcType="BIGINT" />
		<result column="END_TIME" property="endTime" jdbcType="BIGINT" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT"/>
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT"/>
		<result column="CREATOR" property="creator" jdbcType="INTEGER"/>
		<result column="UPDATOR" property="updator" jdbcType="INTEGER" />
		<result column="APPLY_PERSON" property="applyPerson" jdbcType="VARCHAR" />
		<result column="REVIEWER" property="reviewer" jdbcType="VARCHAR" />
		<result column="DESCRIBED" property="described" jdbcType="VARCHAR" />
		<result column="NUM" property="num" jdbcType="INTEGER" />
		<result column="APPLY_STATUS" property="applyStatus" jdbcType="INTEGER" />
		<result column="YEAR_AND_MONTH" property="yearAndMonth" jdbcType="VARCHAR" />
		<result column="STANDARD_TEMPLATE" property="standardTemplate" jdbcType="INTEGER" />
		<result column="COMMENT" property="comment" jdbcType="VARCHAR" />
		<result column="APPLY_YEAR" property="applyYear" jdbcType="VARCHAR" />
		<result column="APPLY_MONTH" property="applyMonth" jdbcType="VARCHAR" />
		<result column="APPLY_DAY" property="applyDay" jdbcType="VARCHAR" />
		<result column="NON_STANDARD_AUTHORIZATION_SIGN_NAME" property="nonStandardAuthorizationSignName" jdbcType="VARCHAR" />
		<result column="NON_STANDARD_AUTHORIZATION_SIGN_URL" property="nonStandardAuthorizationSignUrl" jdbcType="VARCHAR" />
		<result column="NON_STANDARD_AUTHORIZATION_URL" property="nonStandardAuthorizationUrl" jdbcType="VARCHAR" />
		<result column="NON_STANDARD_AUTHORIZATION_NAME" property="nonStandardAuthorizationName" jdbcType="VARCHAR" />
		<result column="WHETHER_SIGN" property="whetherSign" jdbcType="INTEGER" />
        <result column="AUTH_TYPE" property="authType" javaType="INTEGER" />
        <result column="SEAL_TYPE" property="sealType" javaType="INTEGER" />
	</resultMap>

	<resultMap id="DtoResultMap" type="com.vedeng.order.model.AuthorizationApplyDto" extends="BaseResultMap">
		<result column="BUSSINESS_CHANCE_NO" property="sjNum" jdbcType="VARCHAR" />
		<result column="QUOTEORDER_NO" property="bjNum" jdbcType="VARCHAR" />
		<result column="SALEORDER_NO" property="ddNum" jdbcType="VARCHAR" />
		<result column="AUTHORIZATIONDAY" property="authorizationDay" />
	</resultMap>

	<sql id="Base_Column_List">
		AUTHORIZATION_APPLY_ID,
		AUTHORIZATION_APPLY_NUM,
		QUOTEORDER_ID,
		SKU_ID,
		PURCHASE_OR_BIDDING,
		PRODUCT_COMPANY,
		NATURE_OF_OPERATION,
		BRAND_NAME,
		SKU_NAME,
		SKU_MODEL,
		DISTRIBUTIONS_TYPE,
		AUTHORIZED_COMPANY,
		PURCHASE_PROJECT_NAME,
		PURCHASE_PROJECT_NUM,
		FILE_TYPE,
		AFTERSALES_COMPANY,
		BEGIN_TIME,
		END_TIME,
		ADD_TIME,
		MOD_TIME,
		CREATOR,
		UPDATOR,
		APPLY_PERSON,
		REVIEWER,
		DESCRIBED,
		NUM,
		APPLY_STATUS,
		YEAR_AND_MONTH,
		STANDARD_TEMPLATE,
		COMMENT,
		APPLY_YEAR,
		APPLY_MONTH,
		APPLY_DAY,
		STANDARD_TEMPLATE,
		NON_STANDARD_AUTHORIZATION_SIGN_NAME,
		NON_STANDARD_AUTHORIZATION_SIGN_URL,
		NON_STANDARD_AUTHORIZATION_NAME,
		NON_STANDARD_AUTHORIZATION_URL,
		WHETHER_SIGN,
        AUTH_TYPE,
        SEAL_TYPE
	</sql>


	<select id="getAuthorizationSum" resultType="int">
		select count(QUOTEORDER_ID) from T_AUTHORIZATION_APPLY  where QUOTEORDER_ID=#{quoteorderId,jdbcType=INTEGER} and APPLY_STATUS = #{applyStatus,jdbcType=INTEGER}
	</select>


	<select id="getAuthorizationApplyInfoBySkuIdAndQuoteId" resultMap="BaseResultMap">
			select <include refid="Base_Column_List" /> from
			T_AUTHORIZATION_APPLY
			where  QUOTEORDER_ID=#{quoteorderId,jdbcType=INTEGER}
			and  SKU_ID = #{skuId,jdbcType=INTEGER}
			and APPLY_STATUS not in (4,5)
			<if test="authorizationApplyId != null">
				and AUTHORIZATION_APPLY_ID <![CDATA[ <> ]]> #{authorizationApplyId,jdbcType=INTEGER}
			</if>
	</select>

	<select id="getSqNumByYearAndMonth" resultType="java.lang.String">
		select AUTHORIZATION_APPLY_NUM from T_AUTHORIZATION_APPLY where YEAR_AND_MONTH = #{yearAndMonth,jdbcType=VARCHAR}
	</select>

	<insert id="insertAuthorizationApplyInfo" parameterType="com.vedeng.order.model.AuthorizationApply"
			useGeneratedKeys="true" keyProperty="authorizationApplyId">
		insert into T_AUTHORIZATION_APPLY(AUTHORIZATION_APPLY_NUM, QUOTEORDER_ID, SKU_ID, PURCHASE_OR_BIDDING,
										  PRODUCT_COMPANY,
										  NATURE_OF_OPERATION, BRAND_NAME, SKU_NAME, SKU_MODEL, DISTRIBUTIONS_TYPE,
										  AUTHORIZED_COMPANY, PURCHASE_PROJECT_NAME,
										  PURCHASE_PROJECT_NUM, FILE_TYPE, AFTERSALES_COMPANY, BEGIN_TIME, END_TIME,
										  ADD_TIME, MOD_TIME, CREATOR, UPDATOR, APPLY_PERSON, REVIEWER, DESCRIBED, NUM,
										  YEAR_AND_MONTH, APPLY_STATUS, STANDARD_TEMPLATE, APPLY_YEAR, APPLY_MONTH,
										  APPLY_DAY,
										  NON_STANDARD_AUTHORIZATION_SIGN_NAME,
										  NON_STANDARD_AUTHORIZATION_SIGN_URL,
										  NON_STANDARD_AUTHORIZATION_NAME,
										  NON_STANDARD_AUTHORIZATION_URL, WHETHER_SIGN, AUTH_TYPE, SEAL_TYPE)
		values (#{authorizationApplyNum,jdbcType=VARCHAR}, #{quoteorderId,jdbcType=INTEGER}, #{skuId,jdbcType=INTEGER},
				#{purchaseOrBidding,jdbcType=VARCHAR},
				#{productCompany,jdbcType=VARCHAR}, #{natureOfOperation,jdbcType=INTEGER},
				#{brandName,jdbcType=INTEGER}, #{skuName,jdbcType=INTEGER},
				#{skuModel,jdbcType=INTEGER}, #{distributionsType,jdbcType=INTEGER},
				#{authorizedCompany,jdbcType=VARCHAR}, #{purchaseProjectName,jdbcType=VARCHAR},
				#{purchaseProjectNum,jdbcType=VARCHAR}, #{fileType,jdbcType=INTEGER},
				#{aftersalesCompany,jdbcType=VARCHAR},
				#{beginTime,jdbcType=VARCHAR}, #{endTime,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT},
				#{modTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER},
				#{updator,jdbcType=INTEGER}, #{applyPerson,jdbcType=VARCHAR}, #{reviewer,jdbcType=VARCHAR},
				#{described,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER},
				#{yearAndMonth,jdbcType=VARCHAR}, #{applyStatus,jdbcType=INTEGER}, #{standardTemplate,jdbcType=INTEGER},
				#{applyYear,jdbcType=VARCHAR}, #{applyMonth,jdbcType=VARCHAR}, #{applyDay,jdbcType=VARCHAR},
				#{nonStandardAuthorizationSignName,jdbcType=VARCHAR},
				#{nonStandardAuthorizationSignUrl,jdbcType=VARCHAR},
				#{nonStandardAuthorizationName,jdbcType=VARCHAR},
				#{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
				#{whetherSign,jdbcType=INTEGER},
                #{authType,jdbcType=INTEGER},
                #{sealType,jdbcType=INTEGER}
		)
	</insert>

	<update id="updateAuthorizationApplyInfo" parameterType="com.vedeng.order.model.AuthorizationApply">
		update T_AUTHORIZATION_APPLY set
		QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
		SKU_ID = #{skuId,jdbcType=INTEGER},
		PURCHASE_OR_BIDDING = #{purchaseOrBidding,jdbcType=VARCHAR},
		PRODUCT_COMPANY = #{productCompany,jdbcType=VARCHAR},
		NATURE_OF_OPERATION = #{natureOfOperation,jdbcType=INTEGER},
		BRAND_NAME = #{brandName,jdbcType=INTEGER},
		SKU_NAME = #{skuName,jdbcType=INTEGER},
		SKU_MODEL = #{skuModel,jdbcType=INTEGER},
		DISTRIBUTIONS_TYPE = #{distributionsType,jdbcType=INTEGER},
		AUTHORIZED_COMPANY = #{authorizedCompany,jdbcType=VARCHAR},
		PURCHASE_PROJECT_NAME = #{purchaseProjectName,jdbcType=VARCHAR},
		PURCHASE_PROJECT_NUM = #{purchaseProjectNum,jdbcType=VARCHAR},
		FILE_TYPE = #{fileType,jdbcType=INTEGER},
		AFTERSALES_COMPANY = #{aftersalesCompany,jdbcType=VARCHAR},
		BEGIN_TIME = #{beginTime,jdbcType=VARCHAR},
		END_TIME = #{endTime,jdbcType=VARCHAR},
		MOD_TIME = #{modTime,jdbcType=BIGINT},
		UPDATOR = #{updator,jdbcType=INTEGER},
		REVIEWER = #{reviewer,jdbcType=VARCHAR},
		DESCRIBED = #{described,jdbcType=VARCHAR},
		NUM = #{num,jdbcType=INTEGER},
		APPLY_STATUS = #{applyStatus,jdbcType=INTEGER},
		STANDARD_TEMPLATE = #{standardTemplate,jdbcType=INTEGER},
		APPLY_YEAR = #{applyYear,jdbcType=VARCHAR},
		APPLY_MONTH = #{applyMonth,jdbcType=VARCHAR},
		APPLY_DAY = #{applyDay,jdbcType=VARCHAR},
		NON_STANDARD_AUTHORIZATION_SIGN_NAME = #{nonStandardAuthorizationSignName,jdbcType=VARCHAR},
		NON_STANDARD_AUTHORIZATION_SIGN_URL = #{nonStandardAuthorizationSignUrl,jdbcType=VARCHAR},
		NON_STANDARD_AUTHORIZATION_NAME = #{nonStandardAuthorizationName,jdbcType=VARCHAR},
		NON_STANDARD_AUTHORIZATION_URL = #{nonStandardAuthorizationUrl,jdbcType=VARCHAR},
		WHETHER_SIGN = #{whetherSign,jdbcType=INTEGER},
        AUTH_TYPE = #{authType,jdbcType=INTEGER},
        SEAL_TYPE = #{sealType,jdbcType=INTEGER}
		where AUTHORIZATION_APPLY_NUM  =  #{authorizationApplyNum,jdbcType=VARCHAR}
	</update>

	<select id="getAutnorizationApplyByNum" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where AUTHORIZATION_APPLY_NUM=#{authorizationApplyNum,jdbcType=VARCHAR}
	</select>

	<select id="getAuthorizationApplyByQuoteId" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where QUOTEORDER_ID=#{quoteorderId,jdbcType=VARCHAR} and APPLY_STATUS not in (4,5)
	</select>

	<select id="getAuthorizationIsRepeat" resultType="com.vedeng.order.model.AuthorizationApply" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where APPLY_STATUS not in (4,5)
		<if test="purchaseProjectName != null and purchaseProjectName != ''">
			and PURCHASE_PROJECT_NAME=#{purchaseProjectName,jdbcType=VARCHAR}
		</if>
		<if test="purchaseProjectName == null or purchaseProjectName == ''">
			and ((PURCHASE_PROJECT_NAME is not null) or (PURCHASE_PROJECT_NAME != ''))
		</if>
		<if test="skuId != null">
			and SKU_ID=#{skuId,jdbcType=INTEGER}
		</if>
		<if test="purchaseProjectNum != null and purchaseProjectNum != ''">
			and PURCHASE_PROJECT_NUM=#{purchaseProjectNum,jdbcType=VARCHAR}
		</if>

		<if test="purchaseProjectNum == null or purchaseProjectNum == ''">
			and ((PURCHASE_PROJECT_NUM is not null) or (PURCHASE_PROJECT_NUM != ''))
		</if>
		<if test="beginTime != null and endTime != null and beginTime != '' and endTime != ''">
			and (
			#{beginTime,jdbcType=VARCHAR} <![CDATA[  <= ]]> END_TIME
			and
			#{endTime,jdbcType=VARCHAR}   <![CDATA[  >= ]]> BEGIN_TIME
			)
		</if>
		<if test="authorizationApplyId != null">
			and AUTHORIZATION_APPLY_ID <![CDATA[ <> ]]> #{authorizationApplyId,jdbcType=VARCHAR}
		</if>
		limit 1
	</select>

	<select id="getAuthorizationApplyByKeyId" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where AUTHORIZATION_APPLY_ID=#{authorizationApplyId,jdbcType=INTEGER}
	</select>

	<update id="updateAuthorizationApplyModTimeAndStatus">
		update T_AUTHORIZATION_APPLY set UPDATOR = #{userId,jdbcType=INTEGER},
		MOD_TIME = #{time,jdbcType=BIGINT}
		<if test="applyStatus != null">
		,APPLY_STATUS = #{applyStatus,jdbcType=INTEGER}
		</if>
		where AUTHORIZATION_APPLY_ID = #{authorizationApplyId,jdbcType=INTEGER}
	</update>

	<select id="getAuthorizationApplyListByQuoteId" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where QUOTEORDER_ID=#{quoteorderId,jdbcType=INTEGER}
		order by ADD_TIME desc
	</select>


	<select id="getAuthorizationApplyByNum" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where AUTHORIZATION_APPLY_NUM=#{authorizationApplyNum,jdbcType=VARCHAR}
	</select>

	<update id="updateAuthorizationApplyReviewer">
		update T_AUTHORIZATION_APPLY set REVIEWER = #{verifyUsers,jdbcType=VARCHAR} where AUTHORIZATION_APPLY_ID=#{authorizationApplyId,jdbcType=INTEGER}
	</update>

	<update id="updateAuthorizationApplyComments">
		update T_AUTHORIZATION_APPLY set COMMENT = #{comments,jdbcType=VARCHAR} where AUTHORIZATION_APPLY_ID=#{authorizationApplyId,jdbcType=INTEGER}
	</update>

	<select id="getAuthorizationApplylistpage" parameterType="Map" resultMap="DtoResultMap">
		select
		A.AUTHORIZATION_APPLY_ID,
		A.AUTHORIZATION_APPLY_NUM,
		A.QUOTEORDER_ID, SKU_ID,
		A.PURCHASE_OR_BIDDING,
		A.PRODUCT_COMPANY,
		A.NATURE_OF_OPERATION,
		A.BRAND_NAME,
		A.SKU_NAME,
		A.SKU_MODEL,
		A.DISTRIBUTIONS_TYPE,
		A.AUTHORIZED_COMPANY,
		A.PURCHASE_PROJECT_NAME,
		A.PURCHASE_PROJECT_NUM,
		A.FILE_TYPE,
		A.AFTERSALES_COMPANY,
		A.BEGIN_TIME,
		A.END_TIME,
		A.ADD_TIME,
		A.MOD_TIME,
		A.CREATOR,
		A.UPDATOR,
		A.APPLY_PERSON,
		A.REVIEWER,
		A.DESCRIBED,
		A.NUM,
		A.APPLY_STATUS,
		A.YEAR_AND_MONTH,
		A.STANDARD_TEMPLATE,
		A.COMMENT,
		A.APPLY_YEAR,
		A.APPLY_MONTH,
		A.APPLY_DAY,
		A.AUTH_TYPE,
		A.SEAL_TYPE,
		B.BUSSINESS_CHANCE_NO,
		Q.QUOTEORDER_NO,
		S.SALEORDER_NO,
		timestampdiff(day,A.BEGIN_TIME,A.END_TIME) AS AUTHORIZATIONDAY
		from
		T_AUTHORIZATION_APPLY A
		LEFT JOIN T_QUOTEORDER Q on A.QUOTEORDER_ID=Q.QUOTEORDER_ID
		LEFT JOIN T_SALEORDER S on Q.QUOTEORDER_ID=S.QUOTEORDER_ID
		LEFT JOIN T_BUSSINESS_CHANCE B on Q.BUSSINESS_CHANCE_ID = B.BUSSINESS_CHANCE_ID
		LEFT JOIN T_R_TRADER_J_USER TRTJU ON TRTJU.TRADER_ID=Q.TRADER_ID and TRTJU.TRADER_TYPE = 1
		where 1=1
		<if test="authorizationApplyVo.authorizationApplyNum != null and authorizationApplyVo.authorizationApplyNum != ''">
			and A.AUTHORIZATION_APPLY_NUM LIKE CONCAT('%',#{authorizationApplyVo.authorizationApplyNum,jdbcType=VARCHAR},'%')
		</if>
		<if test="authorizationApplyVo.applyPerson != null and authorizationApplyVo.applyPerson != ''">
			and A.APPLY_PERSON=#{authorizationApplyVo.applyPerson,jdbcType=VARCHAR}
		</if>
		<if test="authorizationApplyVo.beginTime != null and authorizationApplyVo.beginTime != ''">
			AND A.ADD_TIME <![CDATA[>=]]> UNIX_TIMESTAMP(#{authorizationApplyVo.beginTime,jdbcType=VARCHAR})*1000
		</if>
		<if test="authorizationApplyVo.endTime != null and authorizationApplyVo.endTime != ''">
			AND A.ADD_TIME <![CDATA[<=]]> UNIX_TIMESTAMP(CONCAT(#{authorizationApplyVo.endTime,jdbcType=VARCHAR},' 23:59:59'))*1000+999
		</if>
		<if test="authorizationApplyVo.applyStatus != null">
			and A.APPLY_STATUS=#{authorizationApplyVo.applyStatus,jdbcType=INTEGER}
		</if>
		<if test="authorizationApplyVo.company != null and authorizationApplyVo.company != ''">
			and A.PURCHASE_OR_BIDDING LIKE CONCAT('%',#{authorizationApplyVo.company,jdbcType=VARCHAR},'%')
		</if>
		<if test="authorizationApplyVo.applyName != null and authorizationApplyVo.applyName != ''">
			and A.PURCHASE_PROJECT_NAME LIKE CONCAT('%',#{authorizationApplyVo.applyName,jdbcType=VARCHAR},'%')
		</if>
		<if test="authorizationApplyVo.applyNum != null and authorizationApplyVo.applyNum != ''">
			and A.PURCHASE_PROJECT_NUM LIKE CONCAT('%',#{authorizationApplyVo.applyNum,jdbcType=VARCHAR},'%')
		</if>
		<if test="authorizationApplyVo.authorizationCompany != null and authorizationApplyVo.authorizationCompany != ''">
			and A.AUTHORIZED_COMPANY LIKE CONCAT('%',#{authorizationApplyVo.authorizationCompany,jdbcType=VARCHAR},'%')
		</if>
		<if test="authorizationApplyVo.authType != null">
			and A.AUTH_TYPE=#{authorizationApplyVo.authType,jdbcType=INTEGER}
		</if>
		<if test="authorizationApplyVo.sealType != null">
			and A.SEAL_TYPE=#{authorizationApplyVo.sealType,jdbcType=INTEGER}
		</if>
		<if test="authorizationApplyVo.isSelf != null">
			<if test="authorizationApplyVo.isSelf == 0">
				and NOT find_in_set(#{authorizationApplyVo.userName,jdbcType=VARCHAR}, REVIEWER)
			</if>
			<if test="authorizationApplyVo.isSelf == 1">
				and find_in_set(#{authorizationApplyVo.userName,jdbcType=VARCHAR}, REVIEWER)
			</if>
		</if>

		<if test="authorizationApplyVo.userIds != null and authorizationApplyVo.userIds.size > 0">
			and TRTJU.USER_ID in
			<foreach collection="authorizationApplyVo.userIds" item="userId" separator="," open="(" close=")">
				#{userId}
			</foreach>
		</if>
		 order by A.ADD_TIME desc
	</select>

	<select id="getApplyPreson" resultType="java.lang.String">
		select distinct APPLY_PERSON from T_AUTHORIZATION_APPLY order by APPLY_PERSON
	</select>

	<select id="getAuthorizationApplyListByQuoteIdAndPass" resultMap="BaseResultMap">
		select <include refid="Base_Column_List" /> from
		T_AUTHORIZATION_APPLY
		where QUOTEORDER_ID=#{quoteorderId,jdbcType=INTEGER} and APPLY_STATUS=3
		order by ADD_TIME desc
	</select>

	<select id="getAuthorizationMaxId" resultType="int">
		select if(MAX(AUTHORIZATION_APPLY_ID) is null,0,MAX(AUTHORIZATION_APPLY_ID)) FROM T_AUTHORIZATION_APPLY
	</select>

	<select id="selectTimeoutTaskForNotice" resultType="com.vedeng.common.activiti.entity.ActivitiTaskUnDoEntity">
		<![CDATA[
        SELECT
        t.ID_ AS taskId,
        t.NAME_ AS taskName,
        DATE_FORMAT(t.CREATE_TIME_, '%Y-%m-%d %H:%i:%s') AS createTime,
        e.BUSINESS_KEY_ AS businessKey,
        case when idlink.USER_ID_  IS NULL THEN apply.REVIEWER
        else
        idlink.USER_ID_ end AS "userName"
        FROM
        ACT_RU_TASK t
        JOIN
        ACT_RU_EXECUTION e ON t.PROC_INST_ID_ = e.PROC_INST_ID_
        LEFT JOIN ACT_RU_IDENTITYLINK idlink on t.ID_=idlink.TASK_ID_
        LEFT JOIN ACT_NOTICE_INFO notice on t.ID_ = notice.TASK_ID
         LEFT JOIN T_AUTHORIZATION_APPLY apply on  e.BUSINESS_KEY_  = CONCAT('authorizationApply_',apply.AUTHORIZATION_APPLY_ID)
        WHERE

        t.CREATE_TIME_ < DATE_SUB(NOW(), INTERVAL 30 MINUTE)
          and   t.CREATE_TIME_ > DATE_SUB(NOW(), INTERVAL 24 HOUR)
        and notice.NOTICE_INFO_ID is null
        AND NOT EXISTS (
        SELECT 1
        FROM ACT_HI_TASKINST h
        WHERE h.ID_ = t.ID_
        AND h.END_TIME_ IS NOT NULL
        )
        and e.BUSINESS_KEY_ like 'authorizationApply_%'
        ORDER BY
        t.CREATE_TIME_ DESC
        ]]>
	</select>

</mapper>