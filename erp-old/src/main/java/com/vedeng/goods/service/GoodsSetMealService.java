package com.vedeng.goods.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.goods.model.SetMeal;
import com.vedeng.goods.model.vo.SetMealSkuMappingVo;
import com.vedeng.goods.model.vo.SetMealVo;

import java.util.List;

public interface GoodsSetMealService {


    /**
     *  获取套餐列表
     * @param setMealVo
     * @param page
     * <AUTHOR>
     * @return
     */
    List<SetMeal> getSetMealListPage(SetMealVo setMealVo, Page page);

    /**
     *  根据主键获取套餐信息
     * @param setMealId
     * <AUTHOR>
     * @return
     */
    SetMeal getSetMealById(Integer setMealId);

    /**
     *  根据套餐Id获取商品关联信息列表
     * @param setMealSkuMappingVo
     * <AUTHOR>
     * @return
     */
    List<SetMealSkuMappingVo> getSetMealSkuMappingVoList(SetMealSkuMappingVo setMealSkuMappingVo);

    /**
     *  保存套餐信息
     * @param setMealVo
     * <AUTHOR>
     * @return
     */
    Integer saveSetMeal(SetMealVo setMealVo, User user);

    /**
     *  删除套餐信息
     * @param setMealIds
     * <AUTHOR>
     * @return
     */
    Integer deleteSetMeal(String setMealIds,String deletedReason, User user);

    /**
     *  查询套餐信息
     * @param setMealIds
     * <AUTHOR>
     * @return
     */
    List<SetMeal> getSetMealByIds(String setMealIds);

    /**
     *  根据套餐绑定的商品ID获取该商品所有的科室信息
     * @param setMealSkuMappingVoList
     * <AUTHOR>
     * @return
     */
    void getDepartmentByskuIds(List<SetMealSkuMappingVo> setMealSkuMappingVoList);

    /**
     *  根据名称查询套餐信息，精确查找
     * @param setMealName
     * <AUTHOR>
     * @return
     */
    SetMeal getSetMealByName(String setMealName, Integer setMealId);
}
