package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeFileDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeFileCommandConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeAliReceiveBillRepository;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoRet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
public class AlipayReceiptPushKingdeeFormWriter extends BaseWriter<KingDeeFileDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private KingDeeFileCommandConvertor kingDeeFileCommandConvertor;
    @Autowired
    private KingDeeAliReceiveBillRepository kingDeeAliReceiveBillRepository;

    @Override
    public void doWrite(KingDeeFileDto item, JobParameters params, ExecutionContext stepContext) throws Exception {

//        log.info("支付宝收款回单附件上传{}", JSON.toJSONString(item));
//        RepoRet fileResult = kingDeeBaseApi.attachmentUpload(kingDeeFileCommandConvertor.toCommand(item));
//
//        if (fileResult == null || !fileResult.getResult().getResponseStatus().isIsSuccess()) {
//            String errorMsg = "支付宝收款回单附件上传金蝶失败";
//            log.error("{}，金蝶反馈{}", errorMsg, JSON.toJSONString(fileResult));
//            throw new ServiceException(errorMsg);
//        }
//
//        log.info("支付宝收款回单附件上传金蝶成功{},", JSON.toJSONString(fileResult));
//
//        KingDeeReceiveBillEntity entity = new KingDeeReceiveBillEntity();
//        entity.setFBillNo(item.getBillNo());
//        List<KingDeeReceiveBillEntity> entities = kingDeeAliReceiveBillRepository.queryByObject(entity);
//        if (CollUtil.isNotEmpty(entities)) {
//            KingDeeReceiveBillEntity first = CollUtil.getFirst(entities);
//            first.setFileIsPush(1);
//            kingDeeAliReceiveBillRepository.update(first);
//        }
    }
}
