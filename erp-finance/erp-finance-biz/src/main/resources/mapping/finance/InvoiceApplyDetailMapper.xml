<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceApplyDetailMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailEntity">
        <!--@mbg.generated-->
        <!--@Table T_INVOICE_APPLY_DETAIL-->
        <id column="INVOICE_APPLY_DETAIL_ID" jdbcType="INTEGER" property="invoiceApplyDetailId"/>
        <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId"/>
        <result column="DETAILGOODS_ID" jdbcType="INTEGER" property="detailgoodsId"/>
        <result column="PRICE" jdbcType="DECIMAL" property="price"/>
        <result column="NUM" jdbcType="DECIMAL" property="num"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="CHANGED_GOODS_NAME" jdbcType="VARCHAR" property="changedGoodsName"/>
        <result column="PRODUCT_NAME" jdbcType="VARCHAR" property="productName"/>
        <result column="SPEC_MODEL" jdbcType="VARCHAR" property="specModel"/>
        <result column="UNIT"  jdbcType="VARCHAR" property="unit"/>
        <result column="TAX_RATE" jdbcType="DECIMAL" property="taxRate"/>
        <result column="TAX_AMOUNT" jdbcType="DECIMAL" property="taxAmount"/>
        <result column="TAX_EXCLUSIVE_PRICE" jdbcType="DECIMAL" property="taxExclusivePrice"/>
        <result column="TAX_EXCLUSIVE_AMOUNT" jdbcType="DECIMAL" property="taxExclusiveAmount"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        INVOICE_APPLY_DETAIL_ID,
        INVOICE_APPLY_ID,
        DETAILGOODS_ID,
        PRICE,
        NUM,
        TOTAL_AMOUNT,
        CHANGED_GOODS_NAME,
        PRODUCT_NAME,
        SPEC_MODEL,
        UNIT,
        TAX_RATE,
        TAX_AMOUNT,
        TAX_EXCLUSIVE_PRICE,
        TAX_EXCLUSIVE_AMOUNT
    </sql>

    <!--auto generated by MybatisCodeHelper on 2023-09-20-->
    <select id="findByInvoiceApplyIdIn" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDetailDto">
        select TIAD.INVOICE_APPLY_ID,
               TIAD.INVOICE_APPLY_DETAIL_ID,
               VCS.TAX_CATEGORY_NO taxCategoryNo,
               VCS.SKU_NO          skuNo,
               VCS.SKU_NAME        skuName,
               TIAD.PRICE,
               TIAD.NUM,
               TIAD.TOTAL_AMOUNT,
               TIAD.DETAILGOODS_ID,
               TSG.UNIT_NAME       unitName,
               case
                   when VCP.SPU_TYPE = 316 or VCP.SPU_TYPE = 1008 then TSG.MODEL
                   end             model,
               case
                   when VCP.SPU_TYPE = 317 or VCP.SPU_TYPE = 318 then TSG.SPEC
                   end             spec,
               TIAD.PRODUCT_NAME,TIAD.SPEC_MODEL,TIAD.UNIT,TIAD.TAX_RATE,TIAD.TAX_AMOUNT,TIAD.TAX_EXCLUSIVE_PRICE,TIAD.TAX_EXCLUSIVE_AMOUNT
        from T_INVOICE_APPLY_DETAIL TIAD
                 left join T_SALEORDER_GOODS TSG on TIAD.DETAILGOODS_ID = TSG.SALEORDER_GOODS_ID
                 left join V_CORE_SKU VCS on VCS.SKU_ID = TSG.GOODS_ID
                 left join V_CORE_SPU VCP on VCP.SPU_ID = VCS.SPU_ID
        where INVOICE_APPLY_ID in
        <foreach item="item" index="index" collection="invoiceApplyIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and TIAD.TOTAL_AMOUNT > 0
    </select>

    <select id="findAtByInvoiceApplyIdIn" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDetailDto">
        select TIAD.INVOICE_APPLY_ID,
            TIAD.INVOICE_APPLY_DETAIL_ID,
            VCS.TAX_CATEGORY_NO taxCategoryNo,
            VCS.SKU_NO          skuNo,
            VCS.SKU_NAME        skuName,
            TIAD.PRICE,
            TIAD.NUM,
            TIAD.TOTAL_AMOUNT,
            TIAD.DETAILGOODS_ID,
            TU.UNIT_NAME       unitName,
            case
            when VCP.SPU_TYPE = 316 or VCP.SPU_TYPE = 1008 then VCS.MODEL
            end             model,
            case
            when VCP.SPU_TYPE = 317 or VCP.SPU_TYPE = 318 then VCS.SPEC
            end             spec,
            TIAD.PRODUCT_NAME,TIAD.SPEC_MODEL,TIAD.UNIT,TIAD.TAX_RATE,TIAD.TAX_AMOUNT,TIAD.TAX_EXCLUSIVE_PRICE,TIAD.TAX_EXCLUSIVE_AMOUNT
        from T_INVOICE_APPLY_DETAIL TIAD
                left join T_AFTER_SALES_GOODS TSG on TIAD.DETAILGOODS_ID = TSG.AFTER_SALES_GOODS_ID
                left join V_CORE_SKU VCS on VCS.SKU_ID = TSG.GOODS_ID
                left join T_UNIT TU on TU.UNIT_ID = VCS.BASE_UNIT_ID
                left join V_CORE_SPU VCP on VCP.SPU_ID = VCS.SPU_ID
        where INVOICE_APPLY_ID in
        <foreach item="item" index="index" collection="invoiceApplyIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        and TIAD.TOTAL_AMOUNT > 0
    </select>

    <select id="getByInvoiceApplyDetailId" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDetailDto">
        select
        <include refid="Base_Column_List" />
        from T_INVOICE_APPLY_DETAIL
        where INVOICE_APPLY_DETAIL_ID = #{invoiceApplyDetailId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-10-31-->
    <select id="selectByInvoiceApplyIdIn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_INVOICE_APPLY_DETAIL
        where INVOICE_APPLY_ID in
        <foreach item="item" index="index" collection="invoiceApplyIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="getDetailByInvoiceApplyId" resultType="com.vedeng.erp.finance.dto.InvoiceApplyDetailDto">
        select * from T_INVOICE_APPLY_DETAIL where INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER}
    </select>

<!--auto generated by MybatisCodeHelper on 2023-11-15-->
    <insert id="insertSelective">
        INSERT INTO T_INVOICE_APPLY_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                INVOICE_APPLY_ID,
            </if>
            <if test="detailgoodsId != null">
                DETAILGOODS_ID,
            </if>
            <if test="price != null">
                PRICE,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="changedGoodsName != null">
                CHANGED_GOODS_NAME,
            </if>
            <if test="productName != null">
                PRODUCT_NAME,
            </if>
            <if test="specModel != null">
                SPEC_MODEL,
            </if>
            <if test="unit != null">
                UNIT,
            </if>
            <if test="taxRate != null">
                TAX_RATE,
            </if>
            <if test="taxAmount != null">
                TAX_AMOUNT,
            </if>
            <if test="taxExclusivePrice != null">
                TAX_EXCLUSIVE_PRICE,
            </if>
            <if test="taxExclusiveAmount != null">
                TAX_EXCLUSIVE_AMOUNT
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="invoiceApplyId != null">
                #{invoiceApplyId,jdbcType=INTEGER},
            </if>
            <if test="detailgoodsId != null">
                #{detailgoodsId,jdbcType=INTEGER},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="num != null">
                #{num,jdbcType=DECIMAL},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="changedGoodsName != null">
                #{changedGoodsName,jdbcType=VARCHAR},
            </if>
            <if test="productName != null">
                #{productName,jdbcType=VARCHAR},
            </if>
            <if test="specModel != null">
                #{specModel,jdbcType=VARCHAR},
            </if>
            <if test="unit != null">
                #{unit,jdbcType=VARCHAR},
            </if>
            <if test="taxRate != null">
                #{taxRate,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxExclusivePrice != null">
                #{taxExclusivePrice,jdbcType=DECIMAL},
            </if>
            <if test="taxExclusiveAmount != null">
                #{taxExclusiveAmount,jdbcType=DECIMAL}
            </if>
        </trim>
    </insert>

    <insert id="batchInsert" keyColumn="INVOICE_APPLY_DETAIL_ID" keyProperty="invoiceApplyDetailId" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_INVOICE_APPLY_DETAIL
        (INVOICE_APPLY_ID, DETAILGOODS_ID, PRICE, NUM, TOTAL_AMOUNT, CHANGED_GOODS_NAME,
        PRODUCT_NAME, SPEC_MODEL, UNIT, TAX_RATE, TAX_AMOUNT, TAX_EXCLUSIVE_PRICE, TAX_EXCLUSIVE_AMOUNT
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.invoiceApplyId,jdbcType=INTEGER}, #{item.detailgoodsId,jdbcType=INTEGER},
            #{item.price,jdbcType=DECIMAL}, #{item.num,jdbcType=DECIMAL}, #{item.totalAmount,jdbcType=DECIMAL},
            #{item.changedGoodsName,jdbcType=VARCHAR}, #{item.productName,jdbcType=VARCHAR},
            #{item.specModel,jdbcType=VARCHAR}, #{item.unit,jdbcType=VARCHAR}, #{item.taxRate,jdbcType=DECIMAL},
            #{item.taxAmount,jdbcType=DECIMAL}, #{item.taxExclusivePrice,jdbcType=DECIMAL},
            #{item.taxExclusiveAmount,jdbcType=DECIMAL})
        </foreach>
    </insert>
</mapper>