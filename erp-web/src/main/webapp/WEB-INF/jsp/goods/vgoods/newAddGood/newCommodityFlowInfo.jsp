<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>查看SKU</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">
    <style>
        .detail-block {
            position: relative;
        }
        .detail-optional {
            position: absolute;
            right: 10px;
            top: 0;
            color:#333
        }
        .table-scroll-wrap {
            max-height: 500px;
            overflow-y: auto;
        }
        .mytitle{
            background: #c5ddfb;
            color: #333
        }
    </style>
</head>

<body>

<div class="detail-wrap">
    <input type="hidden" name="spuLevel" value="${coreSpuDto.spuLevel}">
    <input type="hidden" name="skuType" value="${command.skuType}">
    <input type="hidden" id="isSupplyAssistant" value="${isSupplyAssistant}">
    <input type="hidden" id="checkStatus" value="${skuGenerate.checkStatus}">
    <div class="detail-title">查看SKU：${skuGenerate.showName}
    </div>

<div class="tab-nav">
<c:if test="${skuGenerate.skuId == null}">
    <a class="tab-item" href="${pageContext.request.contextPath}/goods/vgoods/viewSpu.do?spuId=${skuGenerate.spuId}&&pageType=0">基本信息</a>
    <a class="tab-item" href="/vgoods/operate/viewOperate.do?spuId=${skuGenerate.spuId}">运营信息</a>
</c:if>
<c:if test="${skuGenerate.skuId != null}">
    <a class="tab-item" href="${pageContext.request.contextPath}/goods/vgoods/viewSku.do?skuId=${skuGenerate.skuId}&spuId=${coreOperateInfoGenerateVo.upSpuId}&&pageType=0">基本信息</a>
    <a class="tab-item" href="/vgoods/operate/viewOperate.do?skuId=${skuGenerate.skuId}">运营信息</a>
    <a class="tab-item current"   href="/goods/vgoods/viewSku.do?skuId=${skuGenerate.skuId}&spuId=${skuGenerate.spuId}&&pageType=2">商品信息整合</a>
</c:if>


</div>

    <div class="detail-block">
        <div class="block-title mytitle">商品基础信息</div>
        <div class="detail-optional on-hide J-toggle-show" style="display：inline-block">
            <span class="toggle-txt J-more">展开更多<i class="vd-icon icon-down"></i></span>
            <span class="toggle-txt J-less" style="display: none;">收起<i class="vd-icon icon-up"></i></span>
        </div>
        <div class="goodDetails J-toggle-show-cnt" style="display :none;">
            <div class="block-title">基本信息</div>
            <div class="detail-table">
                <c:if test="${not empty coreSpuDto.spuName}">
                    <div class="table-item">
                        <div class="table-th">产品名称（注册证/备案凭证）：</div>
                        <div class="table-td">
                                ${coreSpuDto.spuName}
                        </div>
                    </div>
                </c:if>
                <c:if test="${not empty coreSpuDto.specsModel}">
                    <div class="table-item">
                        <div class="table-th">规格、型号（注册证/备案凭证）：</div>
                        <div class="table-td">
                                ${coreSpuDto.specsModel}
                        </div>
                    </div>
                </c:if>
                <div class="table-item">
                    <div class="table-th">订货号：</div>
                    <div class="table-td">
                        ${skuGenerate.skuNo}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">制造商型号：</div>
                    <div class="table-td">
                        ${skuGenerate.model}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">规格：</div>
                    <div class="table-td">
                        ${skuGenerate.spec}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">商品名称：</div>
                    <div class="table-td">
                        ${skuGenerate.skuName}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">物料编码：</div>
                    <div class="table-td">
                        ${skuGenerate.materialCode}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">供应商型号：</div>
                    <div class="table-td">
                        ${skuGenerate.supplyModel}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">是否备货：</div>
                    <div class="table-td">
                        <c:if test="${skuGenerate.isStockup==1}">
                            是
                        </c:if>
                        <c:if test="${skuGenerate.isStockup!=1}">
                            否
                        </c:if>
                    </div>
                </div>
            </div>

            <c:if test="${not empty baseAttributeVoList}" >
            <div class="detail-block">
                <div class="block-title">商品属性</div>
                <div class="detail-table">

                    <div class="table-item">
                        <div class="table-th">选择带入属性：</div>
                        <div class="table-td J-spring-filter">
                            <c:forEach items="${baseAttributeVoList}" var="attr">
                                ${attr.baseAttributeName}、
                            </c:forEach>
                        </div>
                    </div>
                    <c:forEach items="${baseAttributeVoList}" var="attr">
                        <div class="table-item">
                            <div class="table-th">${attr.baseAttributeName}：</div>
                            <div class="table-td">
                                <c:forEach items="${attr.attrValue}" var="attrVal">
                                    <c:if test="${attrVal.selected}">
                                        ${attrVal.attrValue}${attrVal.unitName}
                                    </c:if>
                                </c:forEach>
                            </div>
                        </div>
                    </c:forEach>
                </div>
            </div>
            </c:if>
            <c:if test="${not empty command.paramsName1}">
            <div class="detail-block">
                <div class="block-title">参数信息</div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">技术参数：</div>
                        <div class="table-td">
                            <c:forEach items="${command.paramsName1}" var="params" varStatus="status">
                                ${command.paramsName1[status.index]} : ${command.paramsValue1[status.index]} <br>
                            </c:forEach>
                        </div>
                    </div>
                </div>
            </div>
            </c:if>

            <div class="detail-block">
                <div class="block-title">物流和包装</div>
                <div class="detail-table">
                    <div class="table-item">
                        <div class="table-th">SKU商品单位：</div>
                        <div class="table-td">
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }"> ${unit.unitName}</c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>

                    <%--VDERP-2212 ERP新商品流-新增/编辑SKU（器械设备、配件），增加最小起订量字段--%>
                    <c:if test="${coreSpuDto.spuType == 316 || coreSpuDto.spuType == 1008}">
                        <div class="table-item">
                            <div class="table-th">最小起订量：</div>
                            <div class="table-td">
                                <c:if test="${not empty unitList }">
                                    <c:forEach var="unit" items="${unitList}">
                                        <c:if test="${ skuGenerate.baseUnitId == unit.unitId  }">
                                            <fmt:formatNumber value="${ skuGenerate.minOrder}" pattern="0"/>
                                            ${unit.unitName}
                                        </c:if>
                                    </c:forEach>
                                </c:if>
                            </div>
                        </div>
                    </c:if>


                    <div class="table-item">
                        <div class="table-th">商品最小单位：</div>
                        <div class="table-td">
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${unit.unitName}</c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">内含最小商品数量：</div>
                        <div class="table-td">
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.unitId ==unit.unitId  }"> ${ skuGenerate.changeNum}
                                        ${unit.unitName}
                                    </c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">最小起订量：</div>
                        <div class="table-td">

                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }">
                                        <fmt:formatNumber value="${ skuGenerate.minOrder}" pattern="0"/>
                                        ${unit.unitName}
                                    </c:if>
                                </c:forEach>
                            </c:if>
                        </div>
                    </div>


                    <div class="table-item">
                        <div class="table-th">包装体积：</div>
                        <div class="table-td">
                            长度 ${skuGenerate.packageLength}cm 宽度 ${skuGenerate.packageWidth}cm 高度
                            ${skuGenerate.packageHeight}cm
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-th">商品体积：</div>
                        <div class="table-td">
                            长度 ${skuGenerate.goodsLength}cm 宽度 ${skuGenerate.goodsWidth}cm 高度 ${skuGenerate.goodsHeight}cm

                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-th">毛重：</div>
                        <div class="table-td">
                            ${skuGenerate.grossWeight}kg
                        </div>
                    </div>

                    <div class="table-item">
                        <div class="table-th">净重：</div>
                        <div class="table-td">
                            ${skuGenerate.netWeight}kg
                        </div>
                    </div>


                    <div class="table-item">
                        <div class="table-th">包装清单：</div>
                        <div class="table-td">
                            ${skuGenerate.packingList}
                        </div>
                    </div>

                </div>

            </div>

            <div class="detail-block">
                <div class="block-title">存储与效期</div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">存储条件（温度）：</div>
                        <div class="table-td">
                            <c:if test="${ skuGenerate.storageConditionOne ==1 }"> 常温0-30℃ </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==2 }"> 阴凉0-20℃ </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==3 }"> 冷藏2-10℃   </c:if>
                            <c:if test="${ skuGenerate.storageConditionOne ==4 }">
                                <c:if test="${not empty skuGenerate.storageConditionOneLowerValue and not empty skuGenerate.storageConditionOneUpperValue}">
                                    其他温度<fmt:formatNumber value="${skuGenerate.storageConditionOneLowerValue}" type="number"/>℃
                                    -
                                    <fmt:formatNumber value="${skuGenerate.storageConditionOneUpperValue}" type="number"/>℃
                                </c:if>
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">存储条件(湿度）：</div>
                        <div class="table-td">
                            <c:if test="${not empty skuGenerate.storageConditionHumidityLowerValue and not empty skuGenerate.storageConditionHumidityUpperValue}">
                                <fmt:formatNumber value="${skuGenerate.storageConditionHumidityLowerValue}" type="number"/>%
                                -
                                <fmt:formatNumber value="${skuGenerate.storageConditionHumidityUpperValue}" type="number"/>%
                            </c:if>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">存储条件（其他）：</div>
                        <div class="table-td">
                            <c:forEach items="${skuGenerate.storageConditionTwo}" var="item">
                                <c:if test="${fn:contains(item, '1') }"> 通风 </c:if>
                                <c:if test="${fn:contains(item, '2')}"> 干燥 </c:if>
                                <c:if test="${fn:contains(item, '3') }"> 避光 </c:if>
                                <c:if test="${fn:contains(item, '4') }"> 防潮 </c:if>
                                <c:if test="${fn:contains(item, '5') }"> 避热 </c:if>
                                <c:if test="${fn:contains(item, '6')}"> 密封 </c:if>
                                <c:if test="${fn:contains(item, '7') }"> 密闭 </c:if>
                                <c:if test="${fn:contains(item, '8') }"> 严封 </c:if>
                                <c:if test="${fn:contains(item, '9') }"> 遮光 </c:if>
                            </c:forEach>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">有效期：</div>
                        <div class="table-td">
                            <c:if test="${ skuGenerate.effectiveDayUnit==1 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}天 </c:if>
                            <c:if test="${ skuGenerate.effectiveDayUnit==2 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}月 </c:if>
                            <c:if test="${ skuGenerate.effectiveDayUnit==3 and skuGenerate.effectiveDays > 0}"> ${skuGenerate.effectiveDays}年 </c:if>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-block">
                <div class="block-title">产品资料 </div>
                <div class="detail-table">
                    <div class="table-item item-col">
                        <div class="table-th">wiki链接：</div>
                        <div class="table-td">
                            <a href="${skuGenerate.wikiHref}" target="_blank">${skuGenerate.wikiHref}</a>
                        </div>
                    </div>
                    <div class="table-item item-col">
                        <div class="table-th">检测报告：</div>
                        <div class="table-td">
                            <div class="info-pic">
                                <c:forEach items="${command.skuCheck}" var="item">
                                    <c:if test="${not item.pdfFlag}">
                                        <div style="width: 50px;height:55px;margin-right: 20px;">
                                            <div class="info-pic-item J-show-big" data-src="${item.ossUrl}">
                                                <img src="${item.ossUrl}">
                                            </div>
                                            <div style="width: 50px;white-space:nowrap;overflow:scroll; text-overflow:string;">
                                                <span>${not empty item.fileName?item.fileName:'检查报告'}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                </c:forEach>
                                <c:forEach items="${command.skuCheck}" var="item">
                                    <c:if test="${item.pdfFlag}">
                                        <div style="width: 50px;height:80px;margin-right: 20px; ">
                                            <div class="info-pic-item J-show-file"  data-src="${item.ossUrl}">
                                                <img src="${pageContext.request.contextPath}/static/images/pdf_icon.png">
                                            </div>
                                            <div style="width: 50px;white-space:nowrap;overflow:scroll; text-overflow:string;">
                                                <span>${not empty item.fileName?item.fileName:'检查报告'}</span>
                                            </div>
                                        </div>
                                    </c:if>
                                </c:forEach>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="detail-block">
            <div class="block-title">商品说明</div>
            <div class="detail-table">
                <div class="table-item item-col">
                    <div class="table-th">商品备注：</div>
                    <div class="table-td">
                        ${skuGenerate.goodsComments}
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <div class="detail-block">
        <div class="block-title mytitle">
            注册证/备案证
        </div>
<div class="detail-optional J-toggle-show" style="display：inline-block">
<span class="toggle-txt J-more"  style="display: none;">展开更多<i class="vd-icon icon-down"></i></span>
<span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
</div>
<div class="goodDetails J-toggle-show-cnt">
        <div class="detail-table">
            <div class="table-item">
                <div class="table-th">注册证号/备案凭证号：</div>
                <div class="table-td">
                    <a href="javascript:void(0);"
                       tabTitle='{"num":"firstengage${firstEngage.firstEngageId}","link":"/firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${firstEngage.firstEngageId}","title":"查看注册证/备案证"}'>${firstEngage.registrationNumber}</a>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">注册证附件/备案凭证附件：</div>
                <div class="table-td">
                    <div class="info-pic">
                        <c:if test="${not empty firstEngage.registration.zczAttachments }">
                            <c:forEach var="attachments" items="${firstEngage.registration.zczAttachments }">
                                <div class="info-pic-item J-show-big"
                                     data-src="${api_http}${attachments.domain }${attachments.uri }">
                                    <img style="width:100%;height:100%"
                                         src="${api_http}${attachments.domain }${attachments.uri }" alt="">
                                </div>
                                <a class="printAtta" href="javascript:;">打印</a>
                            </c:forEach>
                        </c:if>
                    </div>
                </div>
            </div>

            <div class="table-item">
                <div class="table-th">证件有效期：</div>
                <div class="table-td">
                    ${firstEngage.effectStartDate} 至 ${firstEngage.effectEndDate}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">管理类别：</div>
                <div class="table-td">
                    ${firstEngage.manageCategoryLevelShow}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">生产厂商：</div>
                <div class="table-td">
                    ${firstEngage.productCompanyChineseName}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">旧国标分类：</div>
                <div class="table-td">
                    ${firstEngage.oldStandardCategoryName}
                </div>
            </div>
            <div class="table-item">
                <div class="table-th">新国标分类：</div>
                <div class="table-td">
                    ${firstEngage.newStandardCategoryName}
                </div>
            </div>
            <div class="table-item  ">
                <div class="table-th">审核状态：</div>
                <div class="table-td">
                    <c:if test="${firstEngage.status==2}">
                        <span class="title-status status-red">审核不通过</span>
                    </c:if>
                    <c:if test="${firstEngage.status==1}">
                        <span class="title-status status-yellow">审核中</span>
                    </c:if>
                    <c:if test="${firstEngage.status==3}">
                        <span class="title-status status-green">审核通过</span>
                    </c:if>
                </div>
            </div>
        </div>
    </div>
</div>
    <shiro:hasPermission name="/jsp/newAddGood/newAddGoodDetails.do">
        <div class="detail-block block-nohidden">
            <div class="block-title mytitle">库存信息</div>
        <div class="detail-optional  J-toggle-show" style="display：inline-block">
        <span class="toggle-txt J-more" style="display: none;">展开更多<i class="vd-icon icon-down"></i></span>
        <span class="toggle-txt J-less" >收起<i class="vd-icon icon-up"></i></span>
        </div>
        <div class="goodDetails J-toggle-show-cnt">
            <div class="detail-table">
                <table class="table table-base">
                    <colgroup>
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                        <col width="">
                    </colgroup>
                    <tbody>
                    <tr>
                        <th>产品名称</th>
                        <th>单位</th>
                        <th>可用库存</th>
                        <th>库存量</th>
                        <th>占用库存</th>
                        <th>活动锁定库存</th>
                        <th>在途（订单/数量/预计）</th>
                    </tr>
                    <tr>
                        <td>${skuGenerate.skuName}</td>
                        <td>
                            <c:if test="${not empty unitList }">
                                <c:forEach var="unit" items="${unitList}">
                                    <c:if test="${ skuGenerate.baseUnitId ==unit.unitId  }"> ${unit.unitName}</c:if>
                                </c:forEach>
                            </c:if>
                        </td>
                        <td>${stockInfo.availableStockNum}</td>
                        <td>${stockInfo.stockNum}</td>
                        <td>${stockInfo.occupyNum}</td>
                        <td>${stockInfo.actionLockNum}</td>
                        <td>
                            <c:forEach var="listw" items="${buyorderVos}" varStatus="n">
                                ${listw.buyorderNo}&nbsp;/&nbsp;${listw.onWayNum}&nbsp;/&nbsp;
                                <c:if test="${listw.estimateArrivalTime eq 0 }">--<br/></c:if>
                                <c:if test="${listw.estimateArrivalTime != 0 }">
                                    <date:date value="${listw.estimateArrivalTime}" format="yyyy-MM-dd"/>
                                    <br/>
                                </c:if>
                            </c:forEach>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        </div>
    </shiro:hasPermission>
    <shiro:hasPermission name="/jsp/newAddGood/nuclearPrice.do">
        <div class="detail-block block-nohidden">
            <div class="block-title mytitle">核价信息</div>

        <div class="detail-optional J-toggle-show" style="display: inline-block">
            <span class="toggle-txt J-more"  style="display: none;">展开更多<i class="vd-icon icon-down"></i></span>
            <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
        </div>
        <div class="goodDetails J-toggle-show-cnt">
        <shiro:hasPermission name="/jsp/newAddGood/procurementCost.do">
                <div class="detail-table">
                    <div class="detail-table block-title">采购成本</div>
                    <table class="table table-base">
                        <colgroup>
                            <col width="">
                            <col width="">
                            <col width="">
                        </colgroup>
                        <tbody>

                        <tr>
                            <th>供应商名称</th>
                            <th>采购成本</th>
                            <th>生效时间</th>
                        </tr>
                        <c:forEach var="purchase" items="${skuPriceInfoDetailResponseDto.purchaseList}">
                            <tr>
                                <td>${purchase.traderName}</td>
                                <td>${purchase.purchasePrice}</td>
                                <td>${purchase.modTime}</td>
                            </tr>
                        </c:forEach>
                        </tbody>
                    </table>
                </div>
            </shiro:hasPermission>

        <shiro:hasPermission name="/jsp/newAddGood/salesPrice.do">
            <div class="detail-table">
                <div class="detail-table block-title">销售价</div>
                <table class="table table-base">
                    <colgroup>
                        <col width="">
                        <col width="">
                        <col width="">
                    </colgroup>
                    <tbody>
                    <tr>
                        <th>市场价</th>
                        <th>终端价</th>
                        <th>经销价</th>
                    </tr>
                    <c:if test="${skuPriceInfoDetailResponseDto!=null}">
                    <tr>
                        <td>${skuPriceInfoDetailResponseDto.marketPrice}</td>
                        <td>${skuPriceInfoDetailResponseDto.terminalPrice}</td>
                        <td>${skuPriceInfoDetailResponseDto.distributionPrice}</td>
                    </tr>
                    </c:if>
                    </tbody>
                </table>
            </div>
        </shiro:hasPermission>
        <shiro:hasPermission name="/jsp/newAddGood/installationCharge.do">
            <div class="detail-table">
                <div class="detail-table block-title">安装费用</div>
                <table class="table table-base">
                    <colgroup>
                        <col width="">
                        <col width="">
                    </colgroup>
                    <tbody>
                    <tr>
                        <th>安装区域</th>
                        <th>安装费合计</th>
                    </tr>
                    <tr>
                        <td>---</td>
                        <td>---</td>
                    </tr>
                    </tbody>
                </table>

                    <div class="table-item item-col">
                        <div class="table-td" >
            注：以上区域之外的地区，不提供安装服务
                        </div>
                     </div>
            </div>
        </shiro:hasPermission>
        </div>
        </div>
    </shiro:hasPermission>

    <shiro:hasPermission name="/jsp/newAddGood/submitData.do">
        <div class="detail-block block-nohidden">
            <div class="block-title mytitle">近一年成交数据</div>
        <div class="detail-optional J-toggle-show" style="display：inline-block">
        <span class="toggle-txt J-more" style="display: none;">展开更多<i class="vd-icon icon-down"></i></span>
        <span class="toggle-txt J-less">收起<i class="vd-icon icon-up"></i></span>
        </div>
        <div class="goodDetails J-toggle-show-cnt">
            <div class="detail-table">
                <div class="detail-table block-title">最近一年的成交均价：${price}</div>
                <div class="table-scroll-wrap">
                    <table class="table table-base">
                        <colgroup>
                            <col width="">
                            <col width="">
                            <col width="">
                            <col width="">
                            <col width="">
                            <col width="">
                        </colgroup>
                        <tbody>
                            <tr>
                                <th>订单号</th>
                                <th>客户性质</th>
                                <th>数量</th>
                                <th>单价</th>
                                <th>归属销售</th>
                                <th>生效时间</th>
                            </tr>
                            <c:forEach items="${saleorderGoodsList}" var="saleorderGoods">
                                <tr>
                                    <td>${saleorderGoods.saleorderNo}</td>
                                    <td>${saleorderGoods.title}</td>
                                    <td>${saleorderGoods.num}</td>
                                    <td>${saleorderGoods.price}</td>
                                    <td>${saleorderGoods.userName}</td>
                                    <td>
                                    <date:date value="${saleorderGoods.validTime}" format="yyyy-MM-dd hh:mm:ss"/>
                                    </td>
                                </tr>
                            </c:forEach>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        </div>
    </shiro:hasPermission>
</div>


<script type="text/tmpl" class="J-dlg-tmpl">
        <div class="del-wrap">
            <div class="del-tip">
                <i class="vd-icon icon-caution2"></i><span class="J-dlg-tip"></span>
            </div>
            <form class="J-dlg-form">
                <div class="del-cnt base-form">
                    <textarea name="content" id="" cols="30" rows="10" class="input-textarea J-dlg-cnt" placeholder=""></textarea>
                </div>
            </form>
        </div>

</script>
<script type="text/tmpl" class="J-price-one-tmpl">
        <div class="dlg-price-wrap">
            <div class="dlg-loading J-dlg-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
            <div class="dlg-cnt J-dlg-cnt" style="display: none;">
                <table class="table table-normal">
                    <colgroup>
                        <col width="50%">
                        <col width="50%">
                    </colgroup>
                    <tbody class="J-dlg-list">
                        <tr>
                            <th>时间</th>
                            <th>价格</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

</script>

<script type="text/tmpl" class="J-price-more-tmpl">
        <div class="dlg-price-wrap">
            <div class="dlg-loading J-dlg-loading">
                <img src="${pageContext.request.contextPath}/static/new/img/loading.gif" alt="">
            </div>
            <div class="dlg-cnt J-dlg-cnt" style="display: none;">
                <table class="table table-normal">
                    <colgroup>
                        <col width="50%">
                        <col width="50%">
                    </colgroup>
                    <tbody class="J-dlg-list">
                        <tr>
                            <th>数量</th>
                            <th>价格</th>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

</script>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/jquery.PrintArea.js'></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/newAddGoodDetails2.js?rnd=${resourceVersionKey}"></script>

</body>