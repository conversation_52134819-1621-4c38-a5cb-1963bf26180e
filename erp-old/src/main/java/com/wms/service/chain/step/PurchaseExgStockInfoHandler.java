package com.wms.service.chain.step;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.dto.ExgOrderGoodDto;
import com.wms.dto.StockAllocationRequest;
import com.wms.dto.StockAllocationResult;
import com.wms.dto.StockCalculateDto;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.stockallocation.PurchaseStockAllocationStrategy;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 同步库存信息处理器
 */
@Service
public class PurchaseExgStockInfoHandler extends AbstractHandlerStep {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseExgStockInfoHandler.class);

    @Autowired
    @Qualifier("purchaseExgCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private PurchaseStockAllocationStrategy stockAllocationStrategy;

    @Override
    protected void doDealWith(HandlerStepContext context) throws Exception {

        AfterSalesVo afterSalesInfo = context.get("afterSalesInfo");

        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();

        getDetails(afterSalesInfo);

        List<ExgOrderGoodDto> exgOrderGoodList = ThreadLocalContext.get("orderGoodList");

        exgOrderGoodList.stream().forEach(exgOrderGood -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(exgOrderGood.getSKU());
            stockCalculateDto.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(exgOrderGood.getLotAtt08()));
            stockCalculateDto.setOccupyNum(exgOrderGood.getQty().intValue());
            stockCalculateList.add(stockCalculateDto);
        });

        //采购换货的库存计算接口
        List<WarehouseDto> warehouseList = stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);

        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setRelatedNo(afterSalesInfo.getAfterSalesNo());
        stockInfoDto.setWarehouseStockList(warehouseList);

        LOGGER.info("换货单库存信息接口start==================,参数:" + JSON.toJSONString(stockInfoDto));

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);
    }
    private List<ExgOrderGoodDto> getDetails(AfterSalesVo afterSalesInfo) throws Exception{

        List<ExgOrderGoodDto> orderGoodList = new ArrayList<>();

        List<AfterSalesGoodsVo> afterSalesGoodList =
                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSalesInfo.getAfterSalesId());

        for(AfterSalesGoodsVo afterSaleGood : afterSalesGoodList){
            if(ErpConst.ONE.equals(afterSaleGood.getDeliveryDirect())){
                //直发换货商品不下发wms
                continue;
            }
            StockAllocationRequest stockAllocationRequest = new StockAllocationRequest();
            stockAllocationRequest.setNum(afterSaleGood.getNum());
            stockAllocationRequest.setBuyOrderGoodId(afterSaleGood.getOrderDetailId());
            stockAllocationRequest.setSku(afterSaleGood.getSku());

            List<StockAllocationResult> allocationResultList = stockAllocationStrategy.stockAllocation(stockAllocationRequest);

            //转换成出库单dto
            List<ExgOrderGoodDto> exgOrderGoodList = convertAllocationResult(afterSaleGood.getAfterSalesGoodsId(),allocationResultList);
            orderGoodList.addAll(exgOrderGoodList);
        }

        ThreadLocalContext.put("orderGoodList",orderGoodList);

        return orderGoodList;
    }

    private List<ExgOrderGoodDto> convertAllocationResult(Integer afterSaleGoodId,
                                                          List<StockAllocationResult> allocationResultList) {

        if(CollectionUtils.isEmpty(allocationResultList)){
            return null;
        }

        return allocationResultList
                .stream()
                .map(allocationResult -> {
                    ExgOrderGoodDto exgOrderGoodDto = new ExgOrderGoodDto();
                    exgOrderGoodDto.setSKU(allocationResult.getSku());
                    exgOrderGoodDto.setQty(BigDecimal.valueOf(allocationResult.getNum()));
                    exgOrderGoodDto.setD_EDI_07(afterSaleGoodId + StringUtils.EMPTY);
                    //采购单售后传值*
                    exgOrderGoodDto.setLotAtt07("*");
                    exgOrderGoodDto.setLotAtt08(LogicalEnum.getLogicalWarehouseCode(allocationResult.getLogicalWarehouseId()));
                    return exgOrderGoodDto;
                })
                .collect(Collectors.toList());
    }
}
