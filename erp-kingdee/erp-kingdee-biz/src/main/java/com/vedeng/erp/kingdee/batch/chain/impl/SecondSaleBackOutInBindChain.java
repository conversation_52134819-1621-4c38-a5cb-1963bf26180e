package com.vedeng.erp.kingdee.batch.chain.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.chain.CommonSaleBackOutInBindMethod;
import com.vedeng.erp.kingdee.batch.chain.SaleBackOutInBindChain;
import com.vedeng.erp.kingdee.batch.chain.context.SaleBackOutInBindContext;
import com.vedeng.erp.kingdee.batch.dto.BatchRWarehouseGoodsOutJWarehouseGoodsInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/01/15 14:17
 * @description 第二步销售退货出入库单绑定逻辑：第一步未绑定成功，尝试与无SN且无批次号的出库单商品绑定
 */
@Service
@Slf4j
@Order(2)
public class SecondSaleBackOutInBindChain extends CommonSaleBackOutInBindMethod implements SaleBackOutInBindChain {

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    @Autowired
    private ThirdSaleBackOutInBindChain thirdBindChain;

    // SN未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snList;
    // 批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> batchList;
    // SN和批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snAndBatchList;
    // 第二次SN未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snList2;
    // 第二次批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> batchList2;
    // 第二次SN和批次号未匹配List
    private List<BatchWarehouseGoodsOutInItemDto> snAndBatchList2;

    // 匹配后剩余未匹配SN的出库商品，第二次匹配时需清空
    private List<BatchWarehouseGoodsOutInItemDto> remainSnOutGoods;
    // 匹配后剩余未匹配批次号的出库商品，第二次匹配时需清空
    private List<BatchWarehouseGoodsOutInItemDto> remainBatchOutGoods;
    // 匹配后剩余未匹配SN和批次号的出库商品
    private List<BatchWarehouseGoodsOutInItemDto> remainSnBatchOutGoods;

    // 上一次遍历的入库单单号
    private String outInNo;

    // 匹配SN后剩余未匹配的入库商品数量
    private BigDecimal remainSnInNum = BigDecimal.ZERO ;
    // 匹配批次号后剩余未匹配的入库商品数量
    private BigDecimal remainBatchInNum =BigDecimal.ZERO;
    // 匹配SN和批次号后剩余未匹配的入库商品数量
    private BigDecimal remainSnBatchInNum = BigDecimal.ZERO;

    private Long validTime;

    @Override
    public void handler(SaleBackOutInBindContext context) {
        setBase(context);

        // 3.第一次绑定完成，剩余未绑定的List继续处理
        // sn未匹配的入库单商品
        if (snList.size() > 0) {
            remainSnOutGoods.clear();
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : snList) {
                List<BatchWarehouseGoodsOutInItemDto> noSnAndBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, false, false, validTime);
                if (CollUtil.isEmpty(noSnAndBatchOutItems)) {
                    // 第二次绑定失败，加入snList2后面再处理
                    snList2.add(inGoods);
                    log.info("第二次绑定，有SN入库商品匹配无SN无批次号出库单商品失败，未有对应的出库商品，随后尝试匹配有SN的出库单商品：{}", JSON.toJSONString(inGoods));
                    continue;
                }

                // 匹配SN后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainSnInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);
                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    matchFirst(inGoods, remainSnOutGoods);
                    for (BatchWarehouseGoodsOutInItemDto snOutGoods : remainSnOutGoods) {
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snOutGoods.getOutInNo(), snOutGoods.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 对应出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snOutGoods);

                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snOutGoods.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(1, inGoods.getNum(), snOutGoods.getNum(), bindOutIn, snOutGoods, false);
                                if (inGoods.getNum().compareTo(snOutGoods.getNum()) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(1, remainSnInNum, snOutGoods.getNum(), bindOutIn, snOutGoods, true);
                                if (remainSnInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                }
                outInNo = inGoods.getOutInNo();

                // 遍历次数
                int index = 0;
                matchFirst(inGoods, noSnAndBatchOutItems);
                for (BatchWarehouseGoodsOutInItemDto noSnAndBatchOutItem : noSnAndBatchOutItems) {
                    index++;
                    // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                    // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                    // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                    // 剩余数量 > 0 表示该商品有可以被绑定的数量
                    BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(), true);
                    // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                    BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                    if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                        // 根据出库单商品找到出库单
                        BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noSnAndBatchOutItem.getOutInNo(), 2);

                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noSnAndBatchOutItem.getOutInNo(), noSnAndBatchOutItem.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 基础插入数据
                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(1, inGoods.getNum(), remainOutNum, bindOutIn, noSnAndBatchOutItem, false);
                                if (inGoods.getNum() .compareTo(remainOutNum)  <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(1, remainSnInNum, remainOutNum, bindOutIn, noSnAndBatchOutItem, true);
                                if (remainSnInNum .compareTo(BigDecimal.ZERO)  <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                    // 一次都没有匹配上，且可用的出库单数量都为0
                    if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainSnInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnAndBatchOutItems.size()) {
                        remainSnInNum = inGoods.getNum();
                        log.info("第二次绑定，符合条件的出库单商品数量为0，随后尝试匹配有SN的出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                    // 一次都没有匹配上正确的SKU的情况
                    else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainSnInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnAndBatchOutItems.size()) {
                        remainSnInNum = inGoods.getNum();
                        log.info("第二次绑定，未匹配上正确的或SKU，随后尝试匹配有SN的出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                }

                // 第二次遍历后有SN的入库单商品依然有未匹配上的情况:加入snList2，后面再处理
                if (remainSnInNum.compareTo(BigDecimal.ZERO) > 0) {
                    BatchWarehouseGoodsOutInItemDto snInGoods = new BatchWarehouseGoodsOutInItemDto();
                    BeanUtils.copyProperties(inGoods, snInGoods);
                    snInGoods.setNum(remainSnInNum);
                    snList2.add(snInGoods);
                }
            }
        }

        // 批次号未匹配的入库单商品
        if (batchList.size() > 0) {
            remainBatchOutGoods.clear();
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : batchList) {
                List<BatchWarehouseGoodsOutInItemDto> noSnAndBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, false, false, validTime);
                if (CollUtil.isEmpty(noSnAndBatchOutItems)) {
                    // 第二次绑定失败，加入batchList2后面再处理
                    batchList2.add(inGoods);
                    log.info("第二次绑定，有批次号入库商品匹配无SN无批次号出库单商品失败，无SN无批次号出库商品数量为0，随后尝试匹配有批次号的出库单商品：{}", JSON.toJSONString(inGoods));
                    continue;
                }
                // 匹配批次号后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainBatchInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);

                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    matchFirst(inGoods, remainBatchOutGoods);
                    for (BatchWarehouseGoodsOutInItemDto batchOutGoods : remainBatchOutGoods) {
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(batchOutGoods.getOutInNo(), batchOutGoods.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 对应出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(batchOutGoods);

                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), batchOutGoods.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(2, inGoods.getNum(), batchOutGoods.getNum(), bindOutIn, batchOutGoods, false);
                                if (inGoods.getNum().compareTo(batchOutGoods.getNum()) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(2, remainBatchInNum, batchOutGoods.getNum(), bindOutIn, batchOutGoods, true);
                                if (remainBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                }
                outInNo = inGoods.getOutInNo();

                int index = 0;
                matchFirst(inGoods, noSnAndBatchOutItems);
                for (BatchWarehouseGoodsOutInItemDto noSnAndBatchOutItem : noSnAndBatchOutItems) {
                    index ++;
                    // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                    // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                    // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                    // 剩余数量 > 0 表示该商品有可以被绑定的数量
                    BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(), true);
                    // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                    BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                    if (remainOutNum .compareTo(BigDecimal.ZERO) > 0) {

                        // 根据出库单商品找到出库单
                        BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noSnAndBatchOutItem.getOutInNo(), 2);

                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noSnAndBatchOutItem.getOutInNo(), noSnAndBatchOutItem.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 基础插入数据
                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(2, inGoods.getNum(), remainOutNum, bindOutIn, noSnAndBatchOutItem, false);
                                if (inGoods.getNum().compareTo(remainOutNum) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(2, remainBatchInNum, remainOutNum, bindOutIn, noSnAndBatchOutItem, true);
                                if (remainBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                    // 一次都没有匹配上，且可用的出库单数量都为0
                    if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnAndBatchOutItems.size()) {
                        remainBatchInNum = inGoods.getNum();
                        log.info("第二次绑定，符合条件的出库单商品数量为0，随后尝试匹配有批次号的出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                    // 一次都没有匹配上正确的SKU的情况
                    else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnAndBatchOutItems.size()) {
                        remainBatchInNum = inGoods.getNum();
                        log.info("第二次绑定，未匹配上正确的或SKU，随后尝试匹配有批次号的出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                }

                // 第二次遍历后有批次号的入库单商品依然有未匹配上的情况:加入batchList2，后面再处理
                if (remainBatchInNum.compareTo(BigDecimal.ZERO) > 0) {
                    BatchWarehouseGoodsOutInItemDto batchInGoods = new BatchWarehouseGoodsOutInItemDto();
                    BeanUtils.copyProperties(inGoods, batchInGoods);
                    batchInGoods.setNum(remainBatchInNum);
                    batchList2.add(batchInGoods);
                }
            }
        }

        // 既没有SN也没有批次号的入库单商品
        if (snAndBatchList.size() > 0) {
            outInNo = null;

            in:
            for (BatchWarehouseGoodsOutInItemDto inGoods : snAndBatchList) {
                // 对应的所有无SN和批次号的出库单商品
                List<BatchWarehouseGoodsOutInItemDto> noSnAndBatchOutItems = batchWarehouseGoodsOutInItemDtoMapper.findAllOutByIn(inGoods, false, false, validTime);
                if (CollUtil.isEmpty(noSnAndBatchOutItems)) {
                    // 第二次绑定失败，加入snAndBatchList2后面再处理
                    snAndBatchList2.add(inGoods);
                    log.info("第二次绑定，无SN无批次号入库商品匹配无SN无批次号出库单商品失败，未有对应的出库商品，随后尝试根据商品类型匹配出库单商品：{}", JSON.toJSONString(inGoods));
                    continue;
                }
                // 匹配SN和批次号后剩余未匹配的入库商品数量，不为0时需要继续遍历其他出库单商品
                remainSnBatchInNum = BigDecimal.ZERO;

                // 该入库商品的SKU
                String inSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByIn(inGoods.getOutInNo(), inGoods.getRelatedId());
                // 对应的入库单
                BatchWarehouseGoodsOutInDto oneInOrder = getOutInOrder(inGoods);

                if (StringUtils.isNotEmpty(outInNo) && outInNo.equals(inGoods.getOutInNo())) {
                    matchFirst(inGoods, remainSnBatchOutGoods);
                    for (BatchWarehouseGoodsOutInItemDto snBatchOutGoods : remainSnBatchOutGoods) {
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(snBatchOutGoods.getOutInNo(), snBatchOutGoods.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 对应出库单
                            BatchWarehouseGoodsOutInDto oneOutOrder = getOutInOrder(snBatchOutGoods);

                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(oneInOrder.getRelateNo(), inGoods.getRelatedId(), oneOutOrder.getWarehouseGoodsOutInId(), snBatchOutGoods.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(), inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(3, inGoods.getNum(), snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, false);
                                if (inGoods.getNum().compareTo(snBatchOutGoods.getNum()) <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(3, remainSnBatchInNum, snBatchOutGoods.getNum(), bindOutIn, snBatchOutGoods, true);
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                }
                outInNo = inGoods.getOutInNo();

                int index = 0;
                matchFirst(inGoods, noSnAndBatchOutItems);
                for (BatchWarehouseGoodsOutInItemDto noSnAndBatchOutItem : noSnAndBatchOutItems) {
                    index ++;
                    // 假设此时是第二个入库商品，考虑的情况：一个出库商品被完全绑定，一个出库商品有剩余的数量可以绑定
                    // 入库单商品数量 - 关系表中已绑定的数量 = 剩余数量
                    // 剩余数量 = 0 表示该商品已被全部绑定，直接continue
                    // 剩余数量 > 0 表示该商品有可以被绑定的数量
                    BigDecimal remainOutNum = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.getUnboundOutInAmountByItemId(noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(), true);
                    // remainOutNum的备份，用于对比remainOutNum数量是否变化，未变化时是所有出库单商品都未匹配上的情况
                    BigDecimal copyRemainOutNum = new BigDecimal(String.valueOf(remainOutNum));
                    if (remainOutNum.compareTo(BigDecimal.ZERO) > 0) {

                        // 根据出库单商品找到出库单
                        BatchWarehouseGoodsOutInDto oneOutOrder = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(noSnAndBatchOutItem.getOutInNo(), 2);

                        if(oneOutOrder==null){
                            continue;
                        }
                        // 该出库商品的SKU
                        String outSku = batchWarehouseGoodsOutInItemDtoMapper.getSkuByOut(noSnAndBatchOutItem.getOutInNo(), noSnAndBatchOutItem.getRelatedId());
                        // SKU匹配上
                        if (StringUtils.isNotEmpty(outSku) && outSku.equals(inSku)) {
                            // 基础插入数据
                            BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn = buildBaseDto(
                                    oneInOrder.getRelateNo(),
                                    inGoods.getRelatedId(),
                                    oneOutOrder.getWarehouseGoodsOutInId(),
                                    noSnAndBatchOutItem.getWarehouseGoodsOutInDetailId(),
                                    oneInOrder.getWarehouseGoodsOutInId(),
                                    inGoods.getWarehouseGoodsOutInDetailId());

                            // 前入库单商品绑定中未绑定的数量为0时，从当前商品扣除数量
                            if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0) {
                                bind(3, inGoods.getNum(), remainOutNum, bindOutIn, noSnAndBatchOutItem, false);
                                if (inGoods.getNum().compareTo(remainOutNum)  <= 0) {
                                    continue in;
                                }
                                // 不为0时，从先前未被绑定完的入库单商品数量中扣除数量
                            } else {
                                bind(3, remainSnBatchInNum, remainOutNum, bindOutIn, noSnAndBatchOutItem, true);
                                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) <= 0) {
                                    continue in;
                                }
                            }
                        }
                    }
                    // 一次都没有匹配上，且可用的出库单数量都为0
                    if (remainOutNum.compareTo(BigDecimal.ZERO) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnAndBatchOutItems.size()) {
                        remainSnBatchInNum = inGoods.getNum();
                        log.info("第二次绑定，符合条件的出库单商品数量为0，随后尝试根据商品类型匹配出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                    // 一次都没有匹配上正确的SKU的情况
                    else if (copyRemainOutNum.compareTo(remainOutNum) == 0 && remainSnBatchInNum.compareTo(BigDecimal.ZERO) == 0 && index == noSnAndBatchOutItems.size()) {
                        remainSnBatchInNum = inGoods.getNum();
                        log.info("第二次绑定，未匹配上正确的SKU，随后尝试根据商品类型匹配出库单商品：{}", JSON.toJSONString(inGoods));
                    }
                }

                // 第二次遍历后无SN无批次号的入库单商品依然有未匹配上的情况:加入snAndBatchList2，后面再处理
                if (remainSnBatchInNum.compareTo(BigDecimal.ZERO) > 0) {
                    BatchWarehouseGoodsOutInItemDto snBatchInGoods = new BatchWarehouseGoodsOutInItemDto();
                    BeanUtils.copyProperties(inGoods, snBatchInGoods);
                    snBatchInGoods.setNum(remainSnBatchInNum);
                    snAndBatchList2.add(snBatchInGoods);
                }
            }
        }

        this.doNext(context);
    }

    @Override
    public void doNext(SaleBackOutInBindContext context) {
        thirdBindChain.handler(context);
    }

    @Override
    public void setBase(SaleBackOutInBindContext context) {
        snList = context.getSnList();
        batchList = context.getBatchList();
        snAndBatchList = context.getSnAndBatchList();
        snList2 = context.getSnList2();
        batchList2 = context.getBatchList2();
        snAndBatchList2 = context.getSnAndBatchList2();
        remainSnOutGoods = context.getRemainSnOutGoods();
        remainBatchOutGoods = context.getRemainBatchOutGoods();
        remainSnBatchOutGoods = context.getRemainSnBatchOutGoods();
        outInNo = context.getOutInNo();
        validTime = context.getValidTime();
    }

    @Override
    public void bind(int type, BigDecimal inGoodsNum, BigDecimal outGoodsNum, BatchRWarehouseGoodsOutJWarehouseGoodsInDto bindOutIn, BatchWarehouseGoodsOutInItemDto outGoods, boolean isRemain) {
        if (inGoodsNum .compareTo(outGoodsNum)  == 0) {
            // 数量正好能匹配上
            bindOutIn.setNum(inGoodsNum);
            if (type == 1) {
                if (isRemain) {
                    this.remainSnInNum =  BigDecimal.ZERO;
                }
                log.info("第二次绑定，有SN入库商品匹配无SN无批次号出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                if (isRemain) {
                    this.remainBatchInNum =  BigDecimal.ZERO;
                }
                log.info("第二次绑定，有批次号入库商品匹配无SN无批次号出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 3) {
                if (isRemain) {
                    this.remainSnBatchInNum = BigDecimal.ZERO;
                }
                log.info("第二次绑定，无SN无批次号入库商品匹配无SN无批次号出库商品数量正好，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库小于出库，出库商品多余的数量保存到list中下次使用
        } else if (inGoodsNum .compareTo(outGoodsNum)  < 0) {
            bindOutIn.setNum(inGoodsNum);
            BatchWarehouseGoodsOutInItemDto remainGoods = new BatchWarehouseGoodsOutInItemDto();
            BeanUtils.copyProperties(outGoods, remainGoods);
            remainGoods.setNum(outGoodsNum .subtract(inGoodsNum));
            this.remainSnOutGoods.add(remainGoods);
            if (type == 1) {
                if (isRemain) {
                    this.remainSnInNum =  BigDecimal.ZERO;
                }
                log.info("第二次绑定，有SN入库商品匹配无SN无批次号出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                if (isRemain) {
                    this.remainBatchInNum =  BigDecimal.ZERO;
                }
                log.info("第二次绑定，有批次号入库商品匹配无SN无批次号出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 3) {
                if (isRemain) {
                    this.remainSnBatchInNum =  BigDecimal.ZERO;
                }
                log.info("第二次绑定，无SN无批次号入库商品匹配无SN无批次号出库商品，入库小于出库，{}", JSON.toJSONString(bindOutIn));
            }
            // 入库大于出库，多余的数量保存到list中继续下次遍历
        } else if (inGoodsNum .compareTo(outGoodsNum)  > 0) {
            bindOutIn.setNum(outGoodsNum);
            if (type == 1) {
                this.remainSnInNum = inGoodsNum .subtract(outGoodsNum) ;
                log.info("第二次绑定，有SN入库商品匹配无SN无批次号出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 2) {
                this.remainBatchInNum = inGoodsNum .subtract(outGoodsNum) ;
                log.info("第二次绑定，有批次号入库商品匹配无SN无批次号出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
            if (type == 3) {
                this.remainSnBatchInNum = inGoodsNum .subtract(outGoodsNum) ;
                log.info("第二次绑定，无SN无批次号入库商品匹配无SN无批次号出库商品，入库大于出库，{}", JSON.toJSONString(bindOutIn));
            }
        }
        batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.insertSelective(bindOutIn);
    }
}
