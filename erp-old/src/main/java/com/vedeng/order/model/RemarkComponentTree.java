package com.vedeng.order.model;

import com.vedeng.common.model.TreeNode;
import com.vedeng.order.model.vo.SkuVo;
import lombok.*;

import java.util.List;

/**
 * @Description:  备注组件树形结构
 * @Author:       davis
 * @Date:         2021/4/14 下午3:02
 * @Version:      1.0
 */
@Getter
@Setter
@ToString
public class RemarkComponentTree extends TreeNode {

    /**
     * 展示名称
     */
    private String label;

    /**
     * 是否选中
     */
    private Boolean selected;

    /**
     * 是否需要展示原因框
     */
    private Boolean needReason;

    /**
     * 是否需要展示日期框
     */
    private Boolean needDate;

    /**
     * 是否禁用
     */
    private Boolean disabled;

    /**
     * 产品数据源
     */
    private List<SkuVo> skuList;

    /**
     * 日期
     */
    private String date;

    /**
     * 原因
     */
    private String reason;
}
