package com.wms.task;

import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.model.OssInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.impl.OssUtilsServiceImpl;
import com.wms.service.util.WMSFTPUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName WMSImageTask.java
 * @Description TODO  资质图片处理
 * @createTime 2022年01月17日 15:04:00
 */
@Component
@JobHandler(value="WMSImageTask")
public class WMSImageTask extends AbstractJobHandler {

    @Resource
    private AttachmentMapper attachmentMapper;

    @Autowired
    private WMSFTPUtil wmsftpUtil;


    @Autowired
    private OssUtilsServiceImpl ossUtilsService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<Attachment> list = new ArrayList<>();
        if(StringUtil.isBlank(param)){
            list = attachmentMapper.getWMSImageList();
        }else {
            Attachment search = new Attachment();
            search.setOriginalFilepath(param);
            search.setAttachmentType(SysOptionConstant.QUALITY_REPORT);
            Attachment attachment = attachmentMapper.getAttachmentsByOriginalFilepath(search);
            list.add(attachment);
        }

        Attachment search = new Attachment();
        Attachment update = new Attachment();
        for (Attachment attachment : list) {
            FTPClient ftpClient = wmsftpUtil.getFTPClient();
            String path = attachment.getOriginalFilepath();
            search.setAttachmentType(SysOptionConstant.QUALITY_REPORT);
            search.setOriginalFilepath(path);
            List<Attachment> oldList = attachmentMapper.getAttachmentsByParams(search);

            int index = path.lastIndexOf("/", path.length())+1;
            String filePath = path.substring(0, index);
            String fileName = path.substring(index, path.length());

            InputStream inputStream = wmsftpUtil.downLoadFile(ftpClient,filePath,fileName);
            OssInfo ossInfo = ossUtilsService.sendFile2Oss(path, inputStream,true);

            if(ossInfo != null && ossInfo.getCode().equals(0)){
                for (Attachment attachment1 : oldList) {
                    XxlJobLogger.log("oldId:{},oldOssId:{},newOssId:{}",attachment1.getAttachmentId(),attachment1.getOssResourceId(),ossInfo.getResourceId());
                    update.setAttachmentId(attachment1.getAttachmentId());
                    update.setDomain(ossInfo.getDomain());
                    update.setUri(ossInfo.getUri());
                    update.setOssResourceId(ossInfo.getResourceId());
                    update.setSynSuccess(1);
                    attachmentMapper.updateByPrimaryKeySelective(update);
                }
            }
            wmsftpUtil.closeFTP(ftpClient);
        }

        return SUCCESS;
    }
}
