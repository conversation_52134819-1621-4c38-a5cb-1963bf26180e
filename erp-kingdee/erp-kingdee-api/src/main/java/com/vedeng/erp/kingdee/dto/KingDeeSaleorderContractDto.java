package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@WriteBackField(needBackField = {"FID"})
public class KingDeeSaleorderContractDto extends KingDeeMqBaseDto {
    /**
     * 主键
     */
    private Integer kingDeeSaleorderContractId;

    /**
     * 云星空系统单据FID值
     */
    @WriteBackField
    @KingDeeID
    private Integer fId;

    /**
     * 组织
     */
    private String fQzokOrgid;

    /**
     * 合同号
     */
    private String fQzokHth;

    /**
     * 合同日期
     */
    private String fQzokHtrq;

    /**
     * 合同金额
     */
    private String fQzokHtje;

    /**
     * 税率
     */
    private String fQzokSll;

    /**
     * 销售订单
     */
    @BusinessID("F_QZOK_DDH")
    private String fQzokDdh;

    private String FBillNo;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.SALEORDER_CONTRACT;
    }


}
