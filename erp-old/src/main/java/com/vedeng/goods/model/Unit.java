package com.vedeng.goods.model;

import java.io.Serializable;

public class Unit implements Serializable{
    private Integer unitId;

    private Integer unitGroupId;

    private String unitName;

    private String unitNameEn;

    private Integer isDel;

    private Integer sort;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;
    
    private Integer companyId;

    private String unitKingDeeNo;

    private String unitKingDeeName;

    public String getUnitKingDeeName() {
        return unitKingDeeName;
    }

    public void setUnitKingDeeName(String unitKingDeeName) {
        this.unitKingDeeName = unitKingDeeName;
    }

    public String getUnitKingDeeNo() {
        return unitKingDeeNo;
    }

    public void setUnitKingDeeNo(String unitKingDeeNo) {
        this.unitKingDeeNo = unitKingDeeNo;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getUnitGroupId() {
        return unitGroupId;
    }

    public void setUnitGroupId(Integer unitGroupId) {
        this.unitGroupId = unitGroupId;
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public String getUnitNameEn() {
        return unitNameEn;
    }

    public void setUnitNameEn(String unitNameEn) {
        this.unitNameEn = unitNameEn == null ? null : unitNameEn.trim();
    }

    public Integer getIsDel() {
        return isDel;
    }

    public void setIsDel(Integer isDel) {
        this.isDel = isDel;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}
}