<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="打印报价单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<head>
	<script type="text/javascript"
	src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
	<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}">
    <link  type="text/css" rel="stylesheet" href="<%=basePath%>static/css/manage.css" />

</head>
<body>
  <div id="print-contract">
       <div class="">
              <div class="print-quote-list">
                  <div class="print-quote-logo">
                      <c:if test="${belongOrg == 1}">
                          <img src="<%=basePath%>static/images/quotelogo.jpg" />
                      </c:if>
                      <c:if test="${belongOrg == 2}">
                          <img src="<%=basePath%>static/images/KYG.jpg" />
                      </c:if>
                      <c:if test="${belongOrg == 3}">
                          <img src="<%=basePath%>static/images/YXG.jpg" />
                      </c:if>
                  </div>
                  <div class="print-quote-title">
                      <div class="quote-list-title">报价单</div>
                      <div class="quote-list-number">
                          <div><span>报价单号：</span><span>${quoteVo.quoteorderNo }</span></div>
                          <div><span>报价日期：</span><span>${currTime }</span></div>
                      </div>
                  </div>
              </div>
              <div class="print-quote-part1">
                      <ul class="f_left">
                          <li><span>甲方：</span><span>${quoteVo.traderName }</span></li>
                          <li><span>联系人：</span><span>${quoteVo.name }
                          </span></li>
                          <li><span>电话：</span><span>${quoteVo.telephone }</span></li>
                          <li><span>手机：</span><span>${quoteVo.mobile }</span></li>
                          <li><span>邮箱：</span><span>${quoteVo.email }</span></li>
                      </ul>
                       <ul class=" f_right">
                          <li><span>乙方：</span><span>南京贝登医疗股份有限公司</span></li>
                          <li><span>联系人：</span><span>${detailUser.realName }
                          </span></li>
                          <li><span>电话：</span><span>4006-999-569</span></li>
                          <li><span>手机：</span><span>${detailUser.mobile }</span></li>
                          <li><span>邮箱：</span><span>${detailUser.email }</span></li>
                      </ul>
               </div>
              <div class="print-quote-part2" style="">
                        尊敬的 ${quoteVo.name } ：<br/>
                        非常感谢您选择贝登 ，作为领先的<c:if test="${ quoteVo.customerType == 426}">科研仪器</c:if><c:if test="${ quoteVo.customerType == 427 or quoteVo.customerType ==0}">医疗器械</c:if>一站式采购服务商，我们坚持以“客户第一”的经营理念，为您提供专业的选购服务。
                        相信这次报价是我们进一步合作的开始。
              </div>
              <div class="print-quote-part3 contract-print-table">
                    <table class="print-product-table" style="margin-bottom: 0;">
                        <tbody>
                           <tr class="font-bold tdsmall">
                            <td style="width:26px;">序号</td>
                            <td style="width:94px">图片</td>
                            <td style="width:33%;">产品信息</td>
                            <td style="width:9%;">数量/单位</td>
                            <td style="width:10%;">单价(元)</td>
                            <td style="width:11%;">金额(元)</td>
                        <td style="width:50px;">货期<br><span style="font-size: 0.75em;font-weight: 500;">（工作日）</span></td>
                               <td style="width:5%;">含<br/>安调</td>
                            <td>备注</td>
                          </tr>
                           <c:set var="count" value="1"></c:set>
                           <c:forEach var="list" items="${quoteGoodsList}" varStatus="num">
                               <c:if test="${list.isDelete eq 0 }">
                                   <tr class="border">
                                      <td  align="center">${count}</td>
                                        <c:set var="count" value="${count+1}"></c:set>
                                      <td align="center" nowrap="">
                                      <c:if test="${empty list.domain or empty list.uri}">
                                      <img style="width:90px;height: 70px;"  src="<%=basePath%>static/images/nopic.jpg">
                                      </c:if>
                                      <c:if test="${not empty list.domain and not empty list.uri}">
                                      <img style="width:90px;height: 70px;" src="http://${list.domain}${list.uri}" onerror="javascript:this.src='<%=basePath%>static/images/nopic.jpg'" >
                                      </c:if>
                                      </td>
                                       <td style="text-align: left;">
                                           <b>产品名称：</b>${list.goodsName}<br/>
                                           <b>订货号：</b>${list.sku}<br/>
                                           <b>品牌：</b>${list.brandName}<br/>
                                           <b>型号：</b>${list.model}
                                       </td>
                                       <td align="center" nowrap="">${list.num }/${list.unitName }</td>
                                       <td align="center" nowrap="">${list.price }</td>
                                       <td align="center" nowrap="">${list.price*list.num }</td>
                                       <td class="center" nowrap="">${list.deliveryCycle }</td>
                                       <td class="center" nowrap=""><c:if test="${list.haveInstallation == 0}">否</c:if>
                                                                    <c:if test="${list.haveInstallation == 1}">是</c:if>
                                       </td>
                                       <td class="center" style=" word-wrap:break-word" >
                                           <c:if test="${empty list.goodsComments}">-</c:if>
                                           <c:if test="${not empty list.goodsComments}">${list.goodsComments}</c:if>
                                       </td>
                                   </tr>
                              </c:if>
                            </c:forEach>
                           <tr>
                               <td colspan="2" ><div style="padding-bottom: 10px;padding-top: 10px"><b >合计（元）</b></div></td>
                               <td colspan="7" style="text-align: right"><div style="padding-right: 20px">￥：${totalAmount}</div></td>
                           </tr>
                        </tbody>
                   </table>
                    <table class="print-product-table print-product-table1" style="border-top: none;">
                        <tbody>
                        <tr class="font-bold">
                                <td style="text-align: left;padding-top: 10px;padding-bottom: 10px">
                                    <div>
                                        <div style="width: 50%;display:inline-block;padding-left: 27px">
                                            <div >
                                                <span style="width: 75px">
                                                    <b>价格有效期：</b>
                                                </span>
                                                <span style="font-weight: normal">
                                                    ${quoteVo.period}天
                                                </span>
                                            </div>
                                            <div style="">
                                                <span style="width: 75px">
                                                    <b>发票类型：</b>
                                                </span>
                                                <span style="font-weight: normal">
                                                    <c:forEach var="list" items="${invoiceTypes}">
                                                        <c:if test="${quoteVo.invoiceType == list.sysOptionDefinitionId}">${list.title}</c:if>
                                                    </c:forEach>
                                                </span>

                                            </div>
                                        </div>
                                        <div style="display:inline-block">
                                            <div style="">
                                                <span style="width: 75px">
                                                    <b>付款方式：</b>
                                                </span>
                                                <span style="font-weight: normal">
                                                   <c:if test="${quoteVo.paymentType == 419}">
                                                       先款后货，预付100%
                                                   </c:if>
                                                   <c:if test="${quoteVo.paymentType == 420}">
                                                       先货后款，预付80%
                                                   </c:if>
                                                   <c:if test="${quoteVo.paymentType == 421}">
                                                       先货后款，预付50%
                                                   </c:if>
                                                   <c:if test="${quoteVo.paymentType == 422}">
                                                       先货后款，预付30%
                                                   </c:if>
                                                   <c:if test="${quoteVo.paymentType == 423}">
                                                       先货后款，预付0%
                                                   </c:if>
                                                   <c:if test="${quoteVo.paymentType == 424}">
                                                       自定义
                                                   </c:if>
                                               </span>
                                            </div>
                                            <div style="clear:both">
                                                <span style="width: 75px">
                                                    <b>产品配送费：</b>
                                                </span>
                                                <span style="font-weight: normal">
                                                    <c:forEach var="list" items="${yfTypes}">
                                                        <c:if test="${quoteVo.freightDescription == list.sysOptionDefinitionId}">${list.title}</c:if>
                                                    </c:forEach>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                        </tr>
                        </tbody>
                   </table>
              </div>
              <div class="print-quote-part4">
                  一、包装方式：纸箱（特殊产品除外）<br/>
                  二、质保期限：按照产品的质保条件及质保期对产品提供质保承诺。<br/>
                  <div style="width: 800px;">
                       <span style="float: left;width: 25px;">三、</span><span  style="width:775px;">
                          由于贝登库存变化较大，为了使您如期收到产品，请在您订购前再次向为您服务的产品工程师确认货期。如有其他任何问题或任何我司协助的事宜 , 敬请随时与贝登客户服务中心联系,谢谢!服务热线4006-999-569
                  若需了解更多产品信息，请登入
                      <c:if test="${belongOrg == 1}">
                          贝登网站 https://www.vedeng.com
                      </c:if>
                      <c:if test="${belongOrg == 2}">
                          科研购网站 https://www.kyangou.com
                      </c:if>
                      <c:if test="${belongOrg == 3}">
                          医械购网站 https://www.go.vedeng.com
                      </c:if>
                      <br/>
                     </span>
                  </div>
                    <c:if test="${!empty quoteVo.additionalClause}">
                        <div style="clear: both;width: 800px;">
                            <span style="float: left;width: 25px;">
                            四、</span><span  style=" width:775px;">补充条款：${quoteVo.additionalClause }</span>
                        </div>
                  </c:if>
                  <div style="clear: both;">
                  备注：贵司同意上述价格和条款，请及时与为您服务的产品工程师联系，签订正式的合同。
                  </div>
              </div>
                <div class="print-quote-part5" style="page-break-inside: avoid;">
                <ul class="">
                  <li class="f_left">
                     <div class="parter-a">甲方：${quoteVo.traderName }</div>
                     <div class="print-quote-sign">
                         <div style="margin-top:60px;">授权人签字盖章</div>
                         <div style="margin-top:60px;">日期</div>
                     </div>
                  </li>
                  <li class="f_right ">
                     <div class="pos_rel" style="padding-bottom:10px;">
                     	 <div class="parter-a">乙方：南京贝登医疗股份有限公司</div>
                         <c:if test="${haveChapter}">
                             <img src="<%=basePath%>static/images/bj_pic.png" style="position: absolute;z-index: 13;top: 0;left: 0;">
                         </c:if>

                     
                     <div class="print-quote-sign">
                    
                         <div class="hetongdayinzhang ">
                           
                          <div style="margin-top:34px;border:none;">${detailUser.realName }</div>      
                          <div style="margin-top:0px;">授权人签字盖章</div>
                         </div>
                         <div style="margin-top:40px;border:none;padding-left:34px;">${currTime }</div>
                         <div style="margin-top:0;">日期</div>
                     </div>
                     </div>
                  </li>
                </ul>
               </div>
              <div class="print-quote-part1 print-quote-part6" style="page-break-inside: avoid;">
                      <ul class="f_left">
                          <li><span><b>账户名称：</b></span><span style="font-weight: normal;">${quoteVo.traderName } </span></li>
                          <li><span><b>注册地址电话：</b> </span><span>${quoteVo.regaddress }/${quoteVo.regtel }</span></li>
                          <li><span><b>开户行及账号：</b></span><span>${quoteVo.bank }/${quoteVo.bankAccount }</span></li>
                          <li><span><b>税号： </b></span><span>${quoteVo.taxNum }</span></li>
                      </ul>
                       <ul class=" f_right">
                          <li><span><b>账户名称：</b></span><span style="font-weight: normal;">南京贝登医疗股份有限公司 </span></li>
                          <li><span><b>开户行：</b></span><span> 中国建设银行股份有限公司南京中山南路支行 </span></li>
                          <li><span><b>帐号：</b></span><span> 32001881236052503686  </span></li>
                          <li><span><b>税号：</b></span><span> 91320100589439066H </span></li>
                      </ul>
               </div>
       </div>
        <div class='tcenter mb15'>
            <c:if test="${!print}">
                <span class=" bt-small bt-bg-style bg-light-blue"  id="btnPrint">打印</span>
            </c:if>

    	</div> 
   </div>
   <script type="text/javascript">
       $("#btnPrint").click(function() {
           $("#btnPrint").hide();
           if (window.ActiveXObject || "ActiveXObject" in window){
               $("#print-contract").printArea({
                   mode:'popup'
               });
           }else{
               $("#print-contract").printArea();
           }
           $("#btnPrint").show();
       });
   </script>
<%@ include file="../../common/footer.jsp"%>