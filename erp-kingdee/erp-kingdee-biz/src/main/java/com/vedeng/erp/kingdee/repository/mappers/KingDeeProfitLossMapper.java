package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeProfitLossEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 
 * @author: yana.jiang
 * @date: 2022/11/10
 */
public interface KingDeeProfitLossMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeProfitLossEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeProfitLossEntity record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    KingDeeProfitLossEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeeProfitLossEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeeProfitLossEntity record);

    int updateBatchSelective(List<KingDeeProfitLossEntity> list);

    int batchInsert(@Param("list") List<KingDeeProfitLossEntity> list);
}