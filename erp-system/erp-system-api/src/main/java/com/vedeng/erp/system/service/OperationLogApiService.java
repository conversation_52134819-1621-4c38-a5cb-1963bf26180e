package com.vedeng.erp.system.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.erp.system.dto.OperationLogDto;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/22
 */
public interface OperationLogApiService {

    /**
     * 新增操作日志
     *
     * @param operationLog operationLog
     * @param bizLogEnum   bizLogEnum
     */
    void save(OperationLogDto operationLog, BizLogEnum bizLogEnum);

    /**
     * 分页获取业务操作日志
     */
    PageInfo<OperationLogDto> page(PageParam<OperationLogDto> operationLogDto);
}
