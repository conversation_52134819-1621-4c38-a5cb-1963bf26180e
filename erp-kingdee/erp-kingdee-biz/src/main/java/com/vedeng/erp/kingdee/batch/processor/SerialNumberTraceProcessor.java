package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.SerialNumberTraceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockUpdateDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleOutStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;


/**
 * @Author: Patric.Cheng
 * @CreateTime: 2023-06-28
 * @Description: 换货安调sn更新金蝶Processor
 * @Version: 1.0
 */
@Slf4j
@Service
public class SerialNumberTraceProcessor extends BaseProcessor<SerialNumberTraceDto, KingDeeSaleOutStockUpdateDto> {

    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockService;

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeSaleOutStockUpdateDto doProcess(SerialNumberTraceDto serialNumberTrace, JobParameters params, ExecutionContext stepContext) throws Exception {
        //2.销售换货出库,3.售后安调补充码
        Integer source = serialNumberTrace.getSerialNumberTraceSource();
        Integer warehouseGoodsOutInId=serialNumberTrace.getWarehouseGoodsOutInId();
        if (ErpConstant.TWO.equals(source)){
            warehouseGoodsOutInId = batchWarehouseGoodsOutInItemDtoMapper.findWarehouseGoodsOutInIdByItemId(serialNumberTrace.getChangeWarehouseGoodsOutInDetailId());
            if (Objects.isNull(warehouseGoodsOutInId)){
                log.error("换货未查询到出库单主表id，不推送金蝶serialNumberTraceId:{}",serialNumberTrace.getSerialNumberTraceId());
                return null;
            }
        }
        List<KingDeeSaleOutStockQueryResultDto> kingDeeSaleOutStock = kingDeeSaleOutStockService.getKingDeeSaleOutStock(
                warehouseGoodsOutInId.toString(),
                ErpConstant.TWO.equals(source) ? serialNumberTrace.getChangeWarehouseGoodsOutInDetailId().toString() : serialNumberTrace.getWarehouseGoodsOutInDetailId().toString());
        //开始推送金蝶
        if (CollUtil.isEmpty(kingDeeSaleOutStock)) {
            log.info("没有未推送金蝶的换货安调sn码，不推送金蝶serialNumberTraceId:{}",serialNumberTrace.getSerialNumberTraceId());
            return null;
        }
        KingDeeSaleOutStockQueryResultDto kingDeeSaleOutStockQueryResultDto = CollUtil.getFirst(kingDeeSaleOutStock);
        log.info("开始修改金蝶销售出库单{}", JSON.toJSONString(kingDeeSaleOutStockQueryResultDto));
        KingDeeSaleOutStockDto kingDeeSaleOutStockDto = new KingDeeSaleOutStockDto();
        kingDeeSaleOutStockDto.setFid(kingDeeSaleOutStockQueryResultDto.getFID());
        kingDeeSaleOutStockDto.setF_qzok_bddjtid(warehouseGoodsOutInId.toString());
        KingDeeSaleOutStockDetailDto stockDetailDto = new KingDeeSaleOutStockDetailDto();
        stockDetailDto.setFEntryId(kingDeeSaleOutStockQueryResultDto.getFEntity_FENTRYID());
        stockDetailDto.setF_QZOK_XLH(serialNumberTrace.getSerialNumber());
        kingDeeSaleOutStockDto.getFEntity().add(stockDetailDto);
        KingDeeSaleOutStockUpdateDto kingDeeSaleOutStockUpdateDto = new KingDeeSaleOutStockUpdateDto();
        kingDeeSaleOutStockUpdateDto.setKingDeeSaleOutStockDto(kingDeeSaleOutStockDto);
        kingDeeSaleOutStockUpdateDto.setSerialNumberTraceId(serialNumberTrace.getSerialNumberTraceId());
        return kingDeeSaleOutStockUpdateDto;
    }
}
