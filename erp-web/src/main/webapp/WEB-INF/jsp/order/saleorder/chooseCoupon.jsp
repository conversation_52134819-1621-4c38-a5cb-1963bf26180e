<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="选择优惠券" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">

    //查看客户所有的优惠券
    function viewCustomAllCoupons() {
        $("#close-layer").click();
        window.parent.viewCustomAllCoupons();
    }

    /**
     * 提交按钮事件
     */
    function submitSelectCoupon(){

        var couponId = $("input[type='radio']:checked").val();

        if(couponId == '' || couponId == null || couponId == undefined){
            layer.alert("请选择一个优惠券");
            return;
        }

        if(couponId == "${saleorderCoupon.couponId}"){
            layer.alert("选择的优惠券与已使用的优惠券相同，请重新选择");
            return;
        }

        var denomination = $("#coupontr" + couponId).find("input").eq(0).val();
        var useThreshold = $("#coupontr" + couponId).find("input").eq(1).val();
        var limitTypeStr = $("#coupontr" + couponId).find("input").eq(2).val();
        var effevtiveStartTime = $("#coupontr" + couponId).find("input").eq(3).val();
        var effevtiveEndTime = $("#coupontr" + couponId).find("input").eq(4).val();
        var couponCode = $("#coupontr" + couponId).find("input").eq(5).val();

        var couponInfo = {
            "couponId":couponId,
            "couponCode":couponCode,
            "denomination":denomination,
            "useThreshold":useThreshold,
            "limitTypeStr":limitTypeStr,
            "effevtiveStartTime":effevtiveStartTime,
            "effevtiveEndTime":effevtiveEndTime
        };

        window.parent.selectCoupon(couponInfo);

        $("#close-layer").click();
    }

</script>
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>finance/invoice/getCollectInvoiceTraderName.do">
            <div style="text-align: left;">
                有效优惠券
                <c:forEach begin="0" end="60" var="i">
                    &nbsp;
                </c:forEach>
                <span class="bt-small bg-light-blue bt-bg-style" onclick="viewCustomAllCoupons()">查看该客户所有的优惠券</span>
            </div>
        </form>
    </div>
    <div class="list-page">
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
                <c:forEach var="availableCoupon" items="${myCouponSkuResponse.availableCoupons}" varStatus="num">
                    <tr id="coupontr${availableCoupon.couponId}">

                        <input type="hidden" value="${availableCoupon.denomination}"/>
                        <input type="hidden" value="${availableCoupon.useThreshold}"/>
                        <input type="hidden" value="${couponIdAndLimitDescMap[availableCoupon.couponCode]}"/>
                        <input type="hidden" value="<fmt:formatDate value="${availableCoupon.effevtiveBeginTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />"/>
                        <input type="hidden" value="<fmt:formatDate value="${availableCoupon.effevtiveEndTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />"/>
                        <input type="hidden" value="${availableCoupon.couponCode}"/>

                        <td width="20%"><input type="radio" name="couponRadio" value="${availableCoupon.couponId}" <c:if test="${availableCoupon.couponCode == saleorderCoupon.couponCode}">checked</c:if> /></td>
                        <td width="80%">
                            ￥${availableCoupon.denomination} &nbsp;&nbsp;&nbsp;
                            满${availableCoupon.useThreshold}可用  &nbsp;&nbsp;&nbsp;
                            ${couponIdAndLimitDescMap[availableCoupon.couponCode]}
                            <br>
                            <fmt:formatDate value="${availableCoupon.effevtiveBeginTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                            -
                            <fmt:formatDate value="${availableCoupon.effevtiveEndTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                        </td>
                    </tr>
                </c:forEach>

                <c:forEach var="unAvailableCoupon" items="${myCouponSkuResponse.unAvailableCoupons}" varStatus="num">
                    <tr>
                        <td width="20%"></td>
                        <td width="80%">
                            ￥${unAvailableCoupon.denomination} &nbsp;&nbsp;&nbsp;
                            满${unAvailableCoupon.useThreshold}可用 &nbsp;&nbsp;&nbsp;
                                ${couponIdAndLimitDescMap[unAvailableCoupon.couponCode]}
                            <br>
                            <fmt:formatDate value="${unAvailableCoupon.effevtiveBeginTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                            -
                            <fmt:formatDate value="${unAvailableCoupon.effevtiveEndTime}" type="date" pattern="yyyy-MM-dd HH:mm:ss" />
                            <br>
                            不可用原因:
                                <c:choose>
                                    <c:when test="${unAvailableCoupon.unAvailableCode == 1}">
                                        无符合商品要求的优惠券
                                    </c:when>
                                    <c:when test="${unAvailableCoupon.unAvailableCode == 2}">
                                        优惠券未开始
                                    </c:when>
                                    <c:when test="${unAvailableCoupon.unAvailableCode == 3}">
                                        优惠券已结束
                                    </c:when>
                                    <c:when test="${unAvailableCoupon.unAvailableCode == 4}">
                                        商品价格未达到优惠券使用门槛
                                    </c:when>
                                    <c:otherwise>
                                        终端限制仅APP使用
                                    </c:otherwise>
                                </c:choose>
                        </td>
                    </tr>
                </c:forEach>

                <tr>
                    <td colspan="2">
                        <div class="add-tijiao tcenter">
                            <button class="dele" type="button" id="close-layer">取消</button>
                            &nbsp;&nbsp;&nbsp;
                            <button type="button" class="bt-bg-style bg-deep-green" onclick="submitSelectCoupon();">提交</button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
<%@ include file="../../common/footer.jsp" %>
