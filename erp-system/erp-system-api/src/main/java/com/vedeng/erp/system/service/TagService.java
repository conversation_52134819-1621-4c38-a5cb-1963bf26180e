package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.TagDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 系统老标签服务接口
 * @date 2022/7/21 12:18
 **/
public interface TagService {

    /**
     * 查询所有的想要的类型的标签
     * @param tagDto 条件
     * @return 数据集合
     */
    List<TagDto> getTypeTags(TagDto tagDto);

    /**
     * 批量插入标签 返回 包含 主键id的对象
     * @param tagDtos 对象
     * @return List<TagDto> 包含 主键id的对象
     */
    List<TagDto> batchAdd(List<TagDto> tagDtos);
}
