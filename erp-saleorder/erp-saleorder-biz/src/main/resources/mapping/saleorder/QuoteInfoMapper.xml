<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.saleorder.dao.QuoteInfoMapper" >

    <select id="queryInfoByNo" resultType="com.vedeng.erp.saleorder.dto.QuoteInfoDto">
        SELECT *
        FROM T_QUOTEORDER
        WHERE QUOTEORDER_NO = #{quoteorderNo,jdbcType=VARCHAR}
    </select>

    <select id="queryGoodsInfoById" resultType="com.vedeng.erp.saleorder.dto.QuoteGoodsInfoDto">
        SELECT A.*,B.SKU_NAME
        FROM T_QUOTEORDER_GOODS A
        LEFT JOIN V_CORE_SKU B ON A.GOODS_ID = B.SKU_ID
        WHERE QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
        AND IS_DELETE = 0
    </select>

    <select id="queryTraderInfoByQuoteId" resultType="com.vedeng.erp.saleorder.dto.QuoteGoodsInfoDto">
        SELECT A.SKU, A.PRICE, C.TRADER_ID,C.QUOTEORDER_NO,A.QUOTEORDER_GOODS_ID
        FROM T_QUOTEORDER_GOODS A
        LEFT JOIN T_QUOTEORDER C on A.QUOTEORDER_ID = C.QUOTEORDER_ID
        LEFT JOIN V_CORE_SKU B ON A.GOODS_ID = B.SKU_ID
        WHERE A.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
        AND A.IS_DELETE = 0
    </select>
</mapper>