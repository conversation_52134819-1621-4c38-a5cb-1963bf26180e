package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

@Data
public class AppChatUserDto {

    /**
     * 用户Id
     */
    private Integer userId;

    /**
     * 工号
     */
    private String number;

    /**
     * 产品经理
     */
    private String userName;

    /**
     * 所属部门
     */
    private String department;

    /**
     * 职位
     */
    private String position;

    /**
     * 在职状态，来自UAC T_WX_USER表，STATUS为5表示已离职
     * 此字段仅为Y和N，Y在职，N离职
     */
    private String workStatus;

    /**
     * 头像
     */
    private String aliasHeadPicture;
}
