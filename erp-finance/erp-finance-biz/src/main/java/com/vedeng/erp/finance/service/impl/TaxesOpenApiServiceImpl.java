package com.vedeng.erp.finance.service.impl;

import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.erp.finance.enums.TaxesInterfaceCodeEnum;
import com.vedeng.erp.finance.enums.TaxesInterfaceMappingEnum;
import com.vedeng.erp.finance.service.AbstractTaxesHandler;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 统一入口实现类
 */
@Service
@Slf4j
public class TaxesOpenApiServiceImpl implements TaxesOpenApiService {

    @Override
    public ITaxesResult openapi(ITaxesParam taxParam, TaxesInterfaceCodeEnum interfaceCodeEnum) {
        // 根据映射匹配具体实现
        Class<? extends AbstractTaxesHandler> clazz = TaxesInterfaceMappingEnum.getClazzByInterfaceEnum(interfaceCodeEnum);
        AbstractTaxesHandler abstractTaxesHandler = ErpSpringBeanUtil.getBean(clazz);
        if (Objects.isNull(abstractTaxesHandler)){
            log.error("根据映射未匹配具体实现类，请检查配置TaxesInterfaceMappingEnum");
            return null;
        }
        return abstractTaxesHandler.openapi(taxParam, interfaceCodeEnum);
    }
}
