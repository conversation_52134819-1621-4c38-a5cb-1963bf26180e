package com.vedeng.erp.aftersale.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.erp.aftersale.domain.dto.AfterSalesToYxbDto;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesToYxbEntity;
import com.vedeng.erp.aftersale.service.AfterSalesToYxbApiService;
import com.vedeng.erp.aftersale.service.AfterSalesUpgradeService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.SaleorderDataService;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.orderstream.aftersales.constant.AfterSalesStatusEnum;
import com.vedeng.orderstream.aftersales.dao.AfterSalesDirectInfoMapper;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 订单流升级-售后service
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.erp.aftersale.service.Impl <br>
 * <b>ClassName:</b> AfterSalesUpgradeService <br>
 * <b>Date:</b> 2021/10/11 20:00 <br>
 */
@Slf4j
@Service
public class AfterSalesUpgradeServiceImpl implements AfterSalesUpgradeService {

    public Logger logger = LoggerFactory.getLogger(AfterSalesUpgradeServiceImpl.class);

    @Autowired
    private AfterSalesDirectInfoMapper afterSalesDirectInfoMapper;

    @Autowired
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private AfterSalesService afterSalesService;

    @Resource
    private SaleorderDataService saleorderDataService;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;
    @Autowired
    private AfterSalesToYxbApiService afterSalesToYxbApiService;


    @Override
    public List<Integer> getAfterSaleNowStatus(AfterSalesVo afterSalesVo) {

        List<Integer> topStatusList = new ArrayList<>();
        if(ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())){
            //已关闭的订单全部为灰
            topStatusList = new ArrayList<>(Arrays.asList(ErpConst.ONE,ErpConst.ONE,ErpConst.ONE,ErpConst.ONE,ErpConst.ONE,ErpConst.ONE,ErpConst.ONE));
        }else {
            switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
                case AFTERSALES_TH:
                    calculateInStockStatus(afterSalesVo);
                    //退货售后单的状态判断
                    //审核通过--确认
                    topStatusList.add((ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.ONE : ErpConst.TWO);
                    //审核通过，入库状态为“无入库”或“全部入库 -- 入库
                    topStatusList.add(((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            && (AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            || AfterSalesStatusEnum.NONE_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus()))) ? ErpConst.TWO : ErpConst.ONE);
                    //售后状态为“进行中”,入库状态为“无入库”或“全部入库”，退票状态为“无退票”或“全部退票” --退票
                    topStatusList.add(((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            &&(AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            || AfterSalesStatusEnum.NONE_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus()))
                            && (ErpConst.ZERO.equals(afterSalesVo.getInvoiceRefundStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getInvoiceRefundStatus()))) ? ErpConst.TWO : ErpConst.ONE);
                    //售后状态为“进行中”,入库状态为“无入库”或“全部入库”，退票状态为“无退票”或“全部退票”退款状态为“无退款”或“全部退款 -- 退款
                    topStatusList.add((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            &&(AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            || AfterSalesStatusEnum.NONE_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus()))
                            &&(ErpConst.ZERO.equals(afterSalesVo.getInvoiceRefundStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getInvoiceRefundStatus()))
                            && (ErpConst.ZERO.equals(afterSalesVo.getAmountRefundStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAmountRefundStatus())) ? ErpConst.TWO : ErpConst.ONE);
                    //后状态为“进行中”,入库状态为“无入库”或“全部入库”，退票状态为“无退票”或“全部退票”退款状态为“无退款”或“全部退款”，开票状态为“无开票”或“全部开票” -- 开票
                    topStatusList.add((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            &&(AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            || AfterSalesStatusEnum.NONE_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus()))
                            &&(ErpConst.ZERO.equals(afterSalesVo.getInvoiceRefundStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getInvoiceRefundStatus()))
                            &&(ErpConst.ZERO.equals(afterSalesVo.getAmountRefundStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAmountRefundStatus()))
                            && (ErpConst.ZERO.equals(afterSalesVo.getInvoiceMakeoutStatus())
                            || ErpConst.TWO.equals(afterSalesVo.getInvoiceMakeoutStatus())) ? ErpConst.TWO : ErpConst.ONE);
                    //完结审核通过后 --完结
                    topStatusList.add(ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    break;
                case AFTERSALES_HH:
                    calculateInStockStatus(afterSalesVo);
                    calculateOutStockStatus(afterSalesVo);
                    //1.提交审核通过后改为蓝色
                    topStatusList.add((ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.ONE : ErpConst.TWO);
                    //审核通过，入库状态为“全部入库”
                    topStatusList.add((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            && AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    //售后状态为“进行中”,入库状态为“全部入库”，出库状态为“全部出库”
                    topStatusList.add((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            && AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            && AfterSalesStatusEnum.ALL_OUT_STOCK.getCode().equals(afterSalesVo.getOutStockStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    //售后状态为“进行中”,入库状态为“全部入库”，出库状态为“全部出库”收款状态为“无收款”或“全部收款
                    topStatusList.add((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            && AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            && AfterSalesStatusEnum.ALL_OUT_STOCK.getCode().equals(afterSalesVo.getOutStockStatus())
                            && (ErpConst.ZERO.equals(afterSalesVo.getAmountCollectionStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAmountCollectionStatus())) ? ErpConst.TWO : ErpConst.ONE);
                    //收款状态为“无收款”或“全部收款”，开票状态为“无开票”或“全部开票”
                    topStatusList.add((ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()))
                            && AfterSalesStatusEnum.ALL_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                            && AfterSalesStatusEnum.ALL_OUT_STOCK.getCode().equals(afterSalesVo.getOutStockStatus())
                            && (ErpConst.ZERO.equals(afterSalesVo.getAmountCollectionStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAmountCollectionStatus()))
                            && (ErpConst.ZERO.equals(afterSalesVo.getInvoiceMakeoutStatus())
                            || ErpConst.TWO.equals(afterSalesVo.getInvoiceMakeoutStatus())) ? ErpConst.TWO : ErpConst.ONE);
                    //完结审核通过
                    topStatusList.add(ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    break;
                case AFTERSALES_TP:
                    //计算退票状态
                    calculationRefundInvoiceStatus(afterSalesVo);

                    //1.提交审核通过后图标填充色改为蓝色，否则置灰
                    topStatusList.add((ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.ONE : ErpConst.TWO);
                    //2.审核通过，退票状态为“全部退票”后图标填充改为蓝色，否则置灰
                    topStatusList.add(ErpConst.TWO.equals(afterSalesVo.getStatus())  &&
                            AfterSalesStatusEnum.ALL_REFUND_INVOICE.getCode().equals(afterSalesVo.getInvoiceRefundStatus()) ?
                            ErpConst.TWO : ErpConst.ONE);
                    //3.退票状态为“全部退票”，申请完结审核通过后后图标填充改为蓝色，否则置灰
                    topStatusList.add(AfterSalesStatusEnum.ALL_REFUND_INVOICE.getCode().equals(afterSalesVo.getInvoiceRefundStatus()) &&
                            ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    break;
                case LOST_TICKET:{
                    //1.提交审核通过后图标填充色改为蓝色，否则置灰
                    topStatusList.add((ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.ONE : ErpConst.TWO);
                    //2.审核通过，处理状态为“全部处理”后图标填充改为蓝色，否则置灰
                    topStatusList.add(ErpConst.TWO.equals(afterSalesVo.getStatus()) &&
                            ErpConst.THREE.equals(afterSalesVo.getHandleStatus()) ?
                            ErpConst.TWO : ErpConst.ONE);
                    //3.审核通过，处理状态为“已处理”，申请完结审核通过后后图标填充改为蓝色，否则置灰
                    topStatusList.add(ErpConst.THREE.equals(afterSalesVo.getHandleStatus()) &&
                            ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    break;
                }
                case AFTERSALES_TK:{
                    //1.提交审核通过后图标填充色改为蓝色，否则置灰
                    topStatusList.add((ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.ONE : ErpConst.TWO);
                    //2.审核通过，退款状态为“全部退款”后图标填充改为蓝色，否则置灰
                    topStatusList.add(ErpConst.TWO.equals(afterSalesVo.getStatus()) &&
                            ErpConst.THREE.equals(afterSalesVo.getAmountRefundStatus()) ?
                            ErpConst.TWO : ErpConst.ONE);
                    //3.审核通过，退款状态为“全部退款”，申请完结审核通过后图标填充改为蓝色，否则置灰
                    topStatusList.add(ErpConst.THREE.equals(afterSalesVo.getAmountRefundStatus()) &&
                            ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ?
                            ErpConst.TWO : ErpConst.ONE);
                    break;
                }
                case AFTERASALES_THIRD_AT:
                case AFTERASALES_THIRD_WX:
                case AFTERSALES_WX:
                case AFTERSALES_AT:
                case AFTERSALES_ATY:
                case AFTERSALES_ATN:{
                    //1.提交审核通过后图标填充色改为蓝色，否则置灰
                    if (null != afterSalesVo && !ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) {
                        topStatusList.add((ErpConst.TWO.equals(afterSalesVo.getStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.TWO : ErpConst.ONE);
                        //判断处理状态，计算付款状态及收款开票状态
                        Boolean currentStatus = calculatePaymentReceiptInvoiceStatus(afterSalesVo);
                        //2.审核通过，付款状态为“无付款”或“全部付款”、收款状态为“无收款”或“全部收款”、开票状态为“无开票”或“全部开票”后图标填充改为蓝色，否则置灰
                        /*topStatusList.add(currentStatus ? ErpConst.TWO : ErpConst.ONE);*/
                        /*变更,+完结状态+工程师校验*/
                        topStatusList.add(((currentStatus  && !CollectionUtils.isEmpty(afterSalesVo.getAfterSalesInstallstionVoList())) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.TWO : ErpConst.ONE);
                        //3.付款状态为“无付款”或“全部付款”、收款状态为“无收款”或“全部收款”、开票状态为“无开票”或“全部开票”，且申请完结审核通过后后图标填充改为蓝色，否则置灰
                        topStatusList.add((currentStatus && ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.TWO : ErpConst.ONE);
                    } else {
                        topStatusList.add(ErpConst.ONE);
                        topStatusList.add(ErpConst.ONE);
                        topStatusList.add(ErpConst.ONE);
                    }
                    break;
                }
                case OTHER: {
                    //1.提交审核通过后图标填充色改为蓝色，否则置灰
                    if (null != afterSalesVo && !ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) {
                        topStatusList.add((ErpConst.TWO.equals(afterSalesVo.getStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.TWO : ErpConst.ONE);
                        //2.完结
                        topStatusList.add((ErpConst.TWO.equals(afterSalesVo.getStatus()) && ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.TWO : ErpConst.ONE);
                    } else {
                        topStatusList.add(ErpConst.ONE);
                        topStatusList.add(ErpConst.ONE);
                    }
                    break;
                }
                case AFTERSALES_JZ:
                    topStatusList.add((ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())
                            || ErpConst.THREE.equals(afterSalesVo.getAtferSalesStatus())) ? ErpConst.ONE : ErpConst.TWO);
                    topStatusList.add(ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ? ErpConst.TWO : ErpConst.ONE);
                    break;
                default:
                    break;
            }
        }
        return topStatusList;
    }

    @Override
    public void setAfterSalesTHInfo(AfterSalesVo afterSalesVo) {
        //最终入库记录列表
        //普发入库记录
        setNoralStockInfo(afterSalesVo);
        //直发入库记录
        setDirectStockInfo(afterSalesVo);
    }

    @Override
    public void setAfterSalesHHInfo(AfterSalesVo afterSalesVo) {
        //普发入库记录
        //普发出库记录
        setNoralStockInfo(afterSalesVo);
        //直发入库记录
        //直发出库记录
        setDirectStockInfo(afterSalesVo);
        //计算已收款信息
        BigDecimal receiveAmount = new BigDecimal(0);
        List<CapitalBill> capitalBillList = afterSalesVo.getAfterCapitalBillList();
        if(!CollectionUtils.isEmpty(capitalBillList)){
            for(CapitalBill capitalBill : capitalBillList){
                receiveAmount = receiveAmount.add(capitalBill.getAmount());
            }
        }
        afterSalesVo.setReceiveAmount(receiveAmount);
    }

    /**
     * <b>Description:</b><br>
     * 统计普发出入库记录
     *
     * @param
     * @return java.util.List<com.vedeng.aftersales.model.vo.AfterSalesGoodsVo>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/20 16:53
     */
    private void setNoralStockInfo(AfterSalesVo afterSalesVo){
        List<AfterSalesGoodsVo> normalGoodsList = afterSalesVo.getAfterSalesGoodsList().stream()
                .filter(item -> item.getDeliveryDirect().equals(0)).collect(Collectors.toList());
        //过滤出需实际退换货入库的商品->关联入库记录
        if(!CollectionUtils.isEmpty(normalGoodsList)){
            Integer deliveryNum = 0;
            Integer arrivalNum = 0;
            for(AfterSalesGoodsVo afterSalesGoodsVo : normalGoodsList){
                deliveryNum = afterSalesGoodsVo.getDeliveryNum();
                arrivalNum = afterSalesGoodsVo.getArrivalNum();
                //入库记录处理
                List<AfterSalesGoodsVo> finalInList = new ArrayList<>();
                if(ErpConst.ZERO.equals(afterSalesGoodsVo.getRknum()) && AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSalesVo.getType())){
                    //退货判断是否无入库
                    afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.NONE_IN_STOCK.getCode());
                    afterSalesGoodsVo.setArrivalNum(0);
                    finalInList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                    afterSalesGoodsVo.setAftersalesGoodsNormalDeliveryInList(finalInList);
                } else {
                    List<WarehouseGoodsOperateLog> inStockInfoList = afterSalesVo.getAfterReturnInstockList();
                    List<WarehouseGoodsOperateLog> outStockInfoList = afterSalesVo.getAfterReturnOutstockList();
                    if(!CollectionUtils.isEmpty(inStockInfoList)){
                        int nowInStockSum = inStockInfoList.stream()
                                .filter(item -> item.getRelatedId().equals(afterSalesGoodsVo.getAfterSalesGoodsId()))
                                .collect(Collectors.summingInt(WarehouseGoodsOperateLog :: getNum));
                        if(nowInStockSum != 0){
                            for(WarehouseGoodsOperateLog inStock : inStockInfoList){
                                if(inStock.getRelatedId().equals(afterSalesGoodsVo.getAfterSalesGoodsId())){
                                    afterSalesGoodsVo.setArrivalNum(inStock.getNum());
                                    if(afterSalesGoodsVo.getRknum().equals(nowInStockSum)){
                                        afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.ALL_IN_STOCK.getCode());
                                    }else {
                                        afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.PART_IN_STOCK.getCode());
                                    }
                                    afterSalesGoodsVo.setInStockTime(inStock.getAddTime());
                                    afterSalesGoodsVo.setFirstEngageId(inStock.getFitstEngageId());
                                    afterSalesGoodsVo.setSterilizationBatchNo(inStock.getSterilizationBatchNo());
                                    afterSalesGoodsVo.setVedengBatchNumer(inStock.getVedengBatchNumer());
                                    afterSalesGoodsVo.setProductDate(inStock.getProductDate());
                                    afterSalesGoodsVo.setBatchNumber(inStock.getBatchNumber());
                                    afterSalesGoodsVo.setExpirationDate(inStock.getExpirationDate());
                                    afterSalesGoodsVo.setBatchNumber(inStock.getBatchNumber());
                                    finalInList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                                }
                            }
                        }
                    }else {
                        afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.NEED_IN_STOCK.getCode());
                        afterSalesGoodsVo.setArrivalNum(0);
                        finalInList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                    }
                    afterSalesGoodsVo.setAftersalesGoodsNormalDeliveryInList(finalInList);
                    afterSalesGoodsVo.setArrivalNum(arrivalNum);
                    ////过滤出需实际退换货入库的商品->关联出库记录
                    List<AfterSalesGoodsVo> finalOutList = new ArrayList<>();
                    if(!CollectionUtils.isEmpty(outStockInfoList)){
                        int nowOutStockSum = Math.abs(outStockInfoList.stream()
                                .filter(item -> item.getRelatedId().equals(afterSalesGoodsVo.getAfterSalesGoodsId()))
                                .collect(Collectors.summingInt(WarehouseGoodsOperateLog :: getNum)));
                        for(WarehouseGoodsOperateLog outStock : outStockInfoList){
                            if(outStock.getRelatedId().equals(afterSalesGoodsVo.getAfterSalesGoodsId())){
                                afterSalesGoodsVo.setDeliveryNum(outStock.getNum());
                                if(afterSalesGoodsVo.getRknum().equals(nowOutStockSum)){
                                    //实际数量=出库数量---全部出库
                                    afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.ALL_OUT_STOCK.getCode());
                                }else {
                                    afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.PART_OUT_STOCK.getCode());
                                }
                                afterSalesGoodsVo.setSterilizationBatchNo(outStock.getSterilizationBatchNo());
                                afterSalesGoodsVo.setOutStockTime(outStock.getAddTime());
                                afterSalesGoodsVo.setFirstEngageId(outStock.getFitstEngageId());
                                afterSalesGoodsVo.setVedengBatchNumer(outStock.getVedengBatchNumer());
                                afterSalesGoodsVo.setBatchNumber(outStock.getBatchNumber());
                                afterSalesGoodsVo.setProductDate(outStock.getProductDate());
                                afterSalesGoodsVo.setExpirationDate(outStock.getExpirationDate());
                                afterSalesGoodsVo.setBatchNumber(outStock.getBatchNumber());
                                finalOutList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                            }
                        }
                    }else {
                        afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.NEED_OUT_STOCK.getCode());
                        afterSalesGoodsVo.setVedengBatchNumer("");
                        afterSalesGoodsVo.setBatchNumber("");
                        afterSalesGoodsVo.setProductDate(null);
                        afterSalesGoodsVo.setExpirationDate(null);
                        afterSalesGoodsVo.setBatchNumber("");
                        afterSalesGoodsVo.setSterilizationBatchNo("");
                        finalOutList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                    }
                    afterSalesGoodsVo.setAftersalesGoodsNormalDeliveryOutList(finalOutList);
                    afterSalesGoodsVo.setDeliveryNum(deliveryNum);
                }
            }
            afterSalesVo.setNormalGoodsList(normalGoodsList);
        }
    }

    /**
     * <b>Description:</b><br>
     * 统计直发出入库记录
     *
     * @param afterSalesVo
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/20 18:50
     */
    private void setDirectStockInfo(AfterSalesVo afterSalesVo){
        //过滤直发售后商品
        List<AfterSalesGoodsVo> directGoodsList = afterSalesVo.getAfterSalesGoodsList().stream()
                .filter(item -> item.getDeliveryDirect().equals(1)).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(directGoodsList)){
            Integer deliveryNum = 0;
            Integer arrivalNum = 0;
            for(AfterSalesGoodsVo afterSalesGoodsVo : directGoodsList){
                deliveryNum = afterSalesGoodsVo.getDeliveryNum();
                arrivalNum = afterSalesGoodsVo.getArrivalNum();
                List<AfterSalesGoodsVo> finalInList = new ArrayList<>();
                //查询直发入库记录
                List<AfterSalesDirectInfo> inStockInfo =  afterSalesDirectInfoMapper.selectStockList(afterSalesGoodsVo.getAfterSalesGoodsId(),ErpConst.TWO);
                //查询直发出库记录
                List<AfterSalesDirectInfo> outStockInfo =  afterSalesDirectInfoMapper.selectStockList(afterSalesGoodsVo.getAfterSalesGoodsId(),ErpConst.ONE);
                //处理入库
                if(!CollectionUtils.isEmpty(inStockInfo)){
                    //合计入库数量
                    int nowInStockSum = inStockInfo.stream().collect(Collectors.summingInt(AfterSalesDirectInfo :: getNum));
                    for(AfterSalesDirectInfo afterSalesDirectInfo : inStockInfo){
                        if(nowInStockSum == afterSalesGoodsVo.getRknum()){
                            //需退货数=直发退货合计数----全部入库
                            afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.ALL_IN_STOCK.getCode());
                        }else{
                            afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.PART_IN_STOCK.getCode());
                        }
                        afterSalesGoodsVo.setArrivalNum(afterSalesDirectInfo.getNum());
                        afterSalesGoodsVo.setProductDate(afterSalesDirectInfo.getGoodCreateTime());
                        afterSalesGoodsVo.setExpirationDate(afterSalesDirectInfo.getGoodVaildTime());
                        afterSalesGoodsVo.setBatchNumber(afterSalesDirectInfo.getFactoryCode());
                        finalInList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                    }
                }else {
                    if(ErpConst.ZERO.equals(afterSalesGoodsVo.getRknum())){
                        afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.NONE_IN_STOCK.getCode());
                    }else {
                        afterSalesGoodsVo.setInStockStatus(AfterSalesStatusEnum.NEED_IN_STOCK.getCode());
                    }
                    afterSalesGoodsVo.setArrivalNum(0);
                    afterSalesGoodsVo.setProductDate(null);
                    afterSalesGoodsVo.setExpirationDate(null);
                    afterSalesGoodsVo.setBatchNumber("");
                    finalInList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                }
                afterSalesGoodsVo.setAftersalesGoodsDirectDeliveryInList(finalInList);
                afterSalesGoodsVo.setArrivalNum(arrivalNum);
                //处理出库
                List<AfterSalesGoodsVo> finalOutList = new ArrayList<>();
                if(!CollectionUtils.isEmpty(outStockInfo)){
                    //合计出库数量
                    int nowOutStockSum = outStockInfo.stream().collect(Collectors.summingInt(AfterSalesDirectInfo :: getNum));
                    for(AfterSalesDirectInfo afterSalesDirectInfo : outStockInfo){
                        if(nowOutStockSum == afterSalesGoodsVo.getRknum()){
                            //需退货数=直发发货合计数----全部发货
                            afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.ALL_OUT_STOCK.getCode());
                        }else{
                            afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.PART_OUT_STOCK.getCode());
                        }
                        afterSalesGoodsVo.setDeliveryNum(afterSalesDirectInfo.getNum());
                        afterSalesGoodsVo.setProductDate(afterSalesDirectInfo.getGoodCreateTime());
                        afterSalesGoodsVo.setExpirationDate(afterSalesDirectInfo.getGoodVaildTime());
                        afterSalesGoodsVo.setBatchNumber(afterSalesDirectInfo.getFactoryCode());
                        finalOutList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                    }
                }else {
                    if(ErpConst.ZERO.equals(afterSalesGoodsVo.getRknum())){
                        afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.NONE_OUT_STOCK.getCode());
                    }else {
                        afterSalesGoodsVo.setOutStockStatus(AfterSalesStatusEnum.NEED_OUT_STOCK.getCode());
                    }
                    afterSalesGoodsVo.setDeliveryNum(0);
                    afterSalesGoodsVo.setProductDate(null);
                    afterSalesGoodsVo.setExpirationDate(null);
                    afterSalesGoodsVo.setBatchNumber("");
                    finalOutList.add(JSONObject.toJavaObject((JSONObject)JSONObject.toJSON(afterSalesGoodsVo),AfterSalesGoodsVo.class));
                }
                afterSalesGoodsVo.setAftersalesGoodsDirectDeliveryOutList(finalOutList);
                afterSalesGoodsVo.setDeliveryNum(deliveryNum);
            }
            afterSalesVo.setDirectGoodsList(directGoodsList);
        }
    }

    /**
     * 计算退票状态
     * @param afterSalesVo
     */
    private void calculationRefundInvoiceStatus (AfterSalesVo afterSalesVo){
        if (afterSalesVo == null){
            return;
        }
        if (CollectionUtils.isEmpty(afterSalesVo.getAfterSalesInvoiceVoList())){
            afterSalesVo.setRefundInvoiceStatus(AfterSalesStatusEnum.NONE_REFUND_INVOICE.getCode());
            return;
        }
        if (CollectionUtils.isEmpty(afterSalesVo.getAfterSalesInvoiceVoList().stream()
                .filter(item -> item.getIsRefundInvoice().equals(1)).collect(Collectors.toList()))){
            afterSalesVo.setRefundInvoiceStatus(AfterSalesStatusEnum.NEED_REFUND_INVOICE.getCode());
            return;
        }
        if (CollectionUtils.isEmpty(afterSalesVo.getAfterSalesInvoiceVoList().stream()
                .filter(item -> item.getIsRefundInvoice().equals(0)).collect(Collectors.toList()))){
            afterSalesVo.setRefundInvoiceStatus(AfterSalesStatusEnum.ALL_REFUND_INVOICE.getCode());
            return;
        }
        afterSalesVo.setRefundInvoiceStatus(AfterSalesStatusEnum.PART_REFUND_INVOICE.getCode());
    }

    /**
     * <b>Description:</b><br>
     * 计算退货入库状态
     *
     * @param afterSalesVo
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/13 20:26
     */
    private void calculateInStockStatus(AfterSalesVo afterSalesVo){
        List<AfterSalesGoodsVo> afterSalesGoodsVos = afterSalesVo.getAfterSalesGoodsList();
        int needNum = 0;//未退货商品数
        int partNum = 0;//部分退货商品数
        int allNum = 0;//全部退货商品数
        int noneNum = 0;//无退货商品数
        for(AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVos){
            if(!ErpConst.ZERO.equals(afterSalesGoodsVo.getRknum())){
                //实际应退数量不为0的情况下
                if(ErpConst.ZERO.equals(afterSalesGoodsVo.getArrivalStatus())){
                    //未退货
                    needNum++;
                }else if(ErpConst.ONE.equals(afterSalesGoodsVo.getArrivalStatus())){
                    //部分退货
                    partNum++;
                }else {
                    //全部退货
                    allNum++;
                }
            }else {
                noneNum ++;
            }
        }
        if(noneNum == afterSalesGoodsVos.size()){
            //如果所有退货sku实际应退数量为0->无入库
            afterSalesVo.setInStockStatus(AfterSalesStatusEnum.NONE_IN_STOCK.getCode());
        }else if(needNum == afterSalesGoodsVos.size() || ((needNum + noneNum) == afterSalesGoodsVos.size())){
            //未退货数=商品数->未入库
            afterSalesVo.setInStockStatus(AfterSalesStatusEnum.NEED_IN_STOCK.getCode());
        }else if(allNum == afterSalesGoodsVos.size() || ((allNum + noneNum) == afterSalesGoodsVos.size())){
            //全部收货商品数=退货商品数->全部入库
            afterSalesVo.setInStockStatus(AfterSalesStatusEnum.ALL_IN_STOCK.getCode());
        }else {
            afterSalesVo.setInStockStatus(AfterSalesStatusEnum.PART_IN_STOCK.getCode());
        }
    }

    /**
     * <b>Description:</b><br>
     * 计算换货出入库状态
     *
     * @param afterSalesVo
     * @return void
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/20 14:42
     */
    private void calculateOutStockStatus(AfterSalesVo afterSalesVo){
        List<AfterSalesGoodsVo> afterSalesGoodsVos = afterSalesVo.getAfterSalesGoodsList();
        int needNum = 0;//未发货商品数
        int partNum = 0;//部分发货商品数
        int allNum = 0;//全部发货商品数
        int noneNum = 0;//无发货商品数
        for(AfterSalesGoodsVo afterSalesGoodsVo : afterSalesGoodsVos){
            if(!ErpConst.ZERO.equals(afterSalesGoodsVo.getRknum())){
                //实际应发数量不为0的情况下
                if(ErpConst.ZERO.equals(afterSalesGoodsVo.getDeliveryStatus())){
                    //未发货
                    needNum++;
                }else if(ErpConst.ONE.equals(afterSalesGoodsVo.getDeliveryStatus())){
                    //部分发货
                    partNum++;
                }else {
                    //全部发货
                    allNum++;
                }
            }else {
                noneNum ++;
            }
        }
        if(noneNum == afterSalesGoodsVos.size()){
            //如果所有发货sku实际应退数量为0->无出库
            afterSalesVo.setOutStockStatus(AfterSalesStatusEnum.NONE_OUT_STOCK.getCode());
        }else if(needNum == afterSalesGoodsVos.size() || ((needNum + noneNum) == afterSalesGoodsVos.size())){
            //未发货数=商品数->未出库
            afterSalesVo.setOutStockStatus(AfterSalesStatusEnum.NEED_OUT_STOCK.getCode());
        }else if(allNum == afterSalesGoodsVos.size() || ((allNum + noneNum) == afterSalesGoodsVos.size())){
            //全部发货商品数=退货商品数->全部出库
            afterSalesVo.setOutStockStatus(AfterSalesStatusEnum.ALL_OUT_STOCK.getCode());
        }else {
            afterSalesVo.setOutStockStatus(AfterSalesStatusEnum.PART_OUT_STOCK.getCode());
        }
    }

    /**
     * <b>Description:</b><br>
     * 设置返回前端展示头部流程栏状态信息
     *
     * @param statusNum 状态数, nowStatus 当前状态的位置, isEnd 当前是否为结束的节点
     * @return java.util.List<java.lang.Integer>
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/12 9:52
     */
    public List<Integer> setTopStatus(int statusNum,int nowStatus,boolean isEnd){
        List<Integer> topStatusList = new ArrayList<>();
        for(int i = 0;i < statusNum;i++){
            //当前的状态，默认是0  0：未进行 1：已进行 2：正在进行 3：正在同步进行
            if(i < nowStatus || (i == nowStatus && isEnd)){
//                topStatusList.add(1);//产品要求，此处暂不设置为1，统一全部展示为进行中
                topStatusList.add(2);
            }else if(i == nowStatus && !isEnd){
                topStatusList.add(2);
            }else {
                topStatusList.add(0);
            }
        }
        return topStatusList;
    }

    /**
     * 判断确认-处理-完结状态，计算付款状态及收款开票状态
     * @param afterSalesVo
     */
    public Boolean calculatePaymentReceiptInvoiceStatus(AfterSalesVo afterSalesVo){

        //审核通过，付款状态为“无付款”或“全部付款”、收款状态为“无收款”或“全部收款”、开票状态为“无开票”或“全部开票”后图标填充改为蓝色，否则置灰
        /*if (ErpConst.TWO.equals(afterSalesVo.getStatus())){*/
        if (ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.TWO.equals(afterSalesVo.getAtferSalesStatus()) ){
            if (ObjectUtils.isEmpty(afterSalesVo.getAmountPayStatus()) ||ErpConst.THREE.equals(afterSalesVo.getAmountPayStatus())){
                if (ObjectUtils.isEmpty(afterSalesVo.getAmountCollectionStatus())||ErpConst.THREE.equals(afterSalesVo.getAmountCollectionStatus())){
                    if (ObjectUtils.isEmpty(afterSalesVo.getInvoiceMakeoutStatus())||ErpConst.TWO.equals(afterSalesVo.getInvoiceMakeoutStatus())){
                        return true;
                    }
                }
            }
        }
        logger.info("calculatePaymentReceiptInvoiceStatus amountPayStatus:{},amountCollectionStatus:{},invoiceMakeoutStatus:{}",afterSalesVo.getAmountPayStatus(),afterSalesVo.getAmountCollectionStatus(),afterSalesVo.getInvoiceMakeoutStatus());
        return false;
    }


    @Override
    public void updateBackInvoiceStatus(Integer afterSalesId,Integer status) {
        afterSalesMapper.saveOrderInvoiceRefundStatus(afterSalesId,status);
    }

    @Override
    public void setIsCloseStatus(AfterSalesVo afterSalesVo) {
        //计算售后单的是否可关闭状态
        if(afterSalesVo == null){
            logger.info("售后信息为空，无法计算售后单是否可关闭状态");
            return;
        }
        if(afterSalesVo.getType() == null ){
            logger.info("售后单类型为空，无法计算售后单是否可关闭状态");
            return;
        }
        afterSalesVo.setCloseStatus(ErpConst.ZERO);
        switch (AfterSalesProcessEnum.getInstance(afterSalesVo.getType())){
            case AFTERSALES_TH:{
                if(ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())){
                    //待确认--可关闭
                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                    return;
                }else if(ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus())){
                    //进行中-判断入库，退款，退票，开票状态
                    if(AfterSalesStatusEnum.NONE_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())
                    || AfterSalesStatusEnum.NEED_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())){
                        //无入库，未入库
                        if((afterSalesVo.getAmountRefundStatus() == null
                                || ErpConst.ZERO.equals(afterSalesVo.getAmountRefundStatus())
                                || ErpConst.ONE.equals(afterSalesVo.getAmountRefundStatus()))
                                && CollectionUtils.isEmpty(afterSalesVo.getAfterCapitalBillList())){
                            //无退款，未退款 且不存在流水
                            if(afterSalesVo.getInvoiceRefundStatus() == null
                                    || ErpConst.ZERO.equals(afterSalesVo.getInvoiceRefundStatus())
                                    || ErpConst.ONE.equals(afterSalesVo.getInvoiceRefundStatus())){
                                //无退票，未退票
                                if(afterSalesVo.getInvoiceMakeoutStatus() == null
                                        || ErpConst.ZERO.equals(afterSalesVo.getInvoiceMakeoutStatus())
                                        || ErpConst.ONE.equals(afterSalesVo.getInvoiceMakeoutStatus())){
                                    //未开票，未开票
                                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                                    return;
                                }
                            }
                        }
                    }
                }
                break;
            }
            case AFTERSALES_HH:{
                if(ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())){
                    //待确认--可关闭
                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                    return;
                }else if(ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus())){
                    //进行中--判断入库，出库，收款，开票状态
                    if(AfterSalesStatusEnum.NEED_IN_STOCK.getCode().equals(afterSalesVo.getInStockStatus())) {
                        //未入库
                        if(AfterSalesStatusEnum.NEED_OUT_STOCK.getCode().equals(afterSalesVo.getOutStockStatus())){
                            //未出库
                            if(afterSalesVo.getAmountCollectionStatus() == null
                                    || ErpConst.ZERO.equals(afterSalesVo.getAmountCollectionStatus())){
                                //无收款
                                if(afterSalesVo.getInvoiceMakeoutStatus() == null
                                        || ErpConst.ZERO.equals(afterSalesVo.getInvoiceMakeoutStatus())){
                                    //无开票
                                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                                    return;
                                }
                            }
                        }
                    }
                }
                break;
            }
            case AFTERSALES_TP:{
                afterSalesVo.setCloseStatus(afterSalesVo.getInvoiceRefundStatus() != null &&
                        afterSalesVo.getInvoiceRefundStatus() > AfterSalesStatusEnum.NEED_REFUND_INVOICE.getCode() ?
                        ErpConst.ZERO : ErpConst.ONE);
                break;
            }
            case LOST_TICKET:{
                afterSalesVo.setCloseStatus(afterSalesVo.getHandleStatus() > ErpConst.ONE ? ErpConst.ZERO : ErpConst.ONE);
                break;
            }
            case AFTERSALES_TK:{
                afterSalesVo.setCloseStatus(afterSalesVo.getAmountRefundStatus() != null &&
                        afterSalesVo.getAmountRefundStatus() > ErpConst.ONE ? ErpConst.ZERO : ErpConst.ONE);
                break;
            }
            case AFTERASALES_THIRD_AT:
            case AFTERASALES_THIRD_WX:
            case AFTERSALES_WX:
            case AFTERSALES_AT:
            case AFTERSALES_ATY:
            case AFTERSALES_ATN:{
                if (ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus())){
                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                    return;
                }else if ( ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus())){
                    //收款状态为“无收款”付款状态为“未付款/无付款”开票状态为“无开票”
                    Boolean amountCollectionStatus = afterSalesVo.getAmountCollectionStatus() == null || ErpConst.ZERO.equals(afterSalesVo.getAmountCollectionStatus());
                    Boolean payAmountStatus = afterSalesVo.getAmountPayStatus() == null || ErpConst.ZERO.equals(afterSalesVo.getAmountPayStatus())|| ErpConst.ONE.equals(afterSalesVo.getAmountPayStatus());
                    Boolean makeoutInvoiceStatus = afterSalesVo.getInvoiceMakeoutStatus() == null || ErpConst.ZERO.equals(afterSalesVo.getInvoiceMakeoutStatus());
                    if(amountCollectionStatus && payAmountStatus && makeoutInvoiceStatus){
                        afterSalesVo.setCloseStatus(ErpConst.ONE);
                        return;
                    }
                }
                break;
            }
            case AFTERSALES_JZ:{
                //技术咨询--待确认和进行中---
                if(ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus())){
                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                    return;
                }
                break;
            }
            case OTHER:{
                if(ErpConst.ZERO.equals(afterSalesVo.getAtferSalesStatus()) || ErpConst.ONE.equals(afterSalesVo.getAtferSalesStatus())){
                    afterSalesVo.setCloseStatus(ErpConst.ONE);
                    return;
                }
                break;
            }
            default:{
                break;
            }
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public ResultInfo<?> editApplyAudit(AfterSalesVo afterSales) {

        try{

            int res  = afterSalesMapper.updateByPrimaryKeySelective(afterSales);

            if(ErpConst.TWO.equals(afterSales.getAtferSalesStatus())){

                //确认售后完成
                if (SysOptionConstant.ID_535.equals(afterSales.getSubjectType()) && ( SysOptionConstant.ID_540.equals(afterSales.getType()) || SysOptionConstant.ID_543.equals(afterSales.getType())) ){

                    //销售
                    Saleorder saleorder = new Saleorder();
                    saleorder.setSaleorderId(afterSales.getOrderId());
                    saleorder.setServiceStatus(ErpConst.TWO);//售后关闭

                    if(SysOptionConstant.ID_535.equals(afterSales.getSubjectType()) && SysOptionConstant.ID_543.equals(afterSales.getSubjectType())){

                        //查询当前销售订单下面是否还有退换货的售后单
                        List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

                        if (!CollectionUtils.isEmpty(sgvList)){
                            sgvList = sgvList.stream().filter(Objects::nonNull).filter(s->!ErpConst.ZERO.equals(s.getLockedStatus())).collect(Collectors.toList());
                        }

                        AfterSales afterSales1 = new AfterSales();

                        afterSales1.setOrderId(afterSales.getOrderId());

                        List<AfterSalesVo> afterSalesVoList = afterSalesMapper.getReturnBackMoneyByOrderId(afterSales1);

                        //如果没有，则解锁销售订单
                        if (CollectionUtils.isEmpty(sgvList) && CollectionUtils.isEmpty(afterSalesVoList)){
                            saleorder.setLockedStatus(0);//0未锁定，1已锁定
                        }

                    }

                    //	saleorder.setLockedStatus(0);
                    res = saleorderMapper.updateByPrimaryKeySelective(saleorder);

                    if(saleorder.getLockedStatus() != null && saleorder.getLockedStatus()==0){

                        updateUnlockSaleOrderWarning(saleorder.getSaleorderId());

                    }

                }else if(SysOptionConstant.ID_536.equals(afterSales.getSubjectType()) && SysOptionConstant.ID_547.equals(afterSales.getSubjectType()) ){

                    long count = 0;

                    AfterSalesVo afterSalesVo = new AfterSalesVo();

                    afterSalesVo.setOrderId(afterSales.getOrderId());

                    List<AfterSalesVo> byOrderLists = afterSalesMapper.getAfterSalesVoListByOrderId(afterSalesVo);

                    if(!CollectionUtils.isEmpty(byOrderLists)){

                       count =  byOrderLists.stream().filter(Objects::nonNull).filter(s->(ErpConst.ZERO.equals(s.getAtferSalesStatus()) || ErpConst.ONE.equals(s.getAtferSalesStatus()))).count();

                    }

                    if(0 == count){

                        //采购
                        Buyorder buyorder = new Buyorder();
                        buyorder.setBuyorderId(afterSales.getOrderId());
                        buyorder.setServiceStatus(ErpConst.TWO);
                        buyorder.setLockedStatus(ErpConst.ZERO);

                        res = buyorderMapper.updateByPrimaryKeySelective(buyorder);

                    }
                    //采购
    //				Buyorder buyorder = new Buyorder();
    //				buyorder.setBuyorderId(afterSales.getOrderId());
    //				buyorder.setServiceStatus(2);
    //				buyorder.setLockedStatus(0);
    //				res = buyorderMapper.updateByPrimaryKeySelective(buyorder);
    //				if(res == 0){
    //					return new ResultInfo();
    //				}
    //				//采购售后完成后需刷新销售单的采购状态
    //				AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
    //				afterSalesGoods.setAfterSalesId(afterSales.getAfterSalesId());
    //				List<AfterSalesGoodsVo> list = afterSalesGoodsMapper.getBuyorderAfterSalesGoodsVosList(afterSalesGoods);
    //				if(list != null && list.size() > 0){
    //					for (AfterSalesGoodsVo asgv : list) {
    //						//查询采购数量
    //						Integer buyNum = rBuyorderSaleorderMapper.getBuyorderGoodsNumByParam(asgv.getOrderDetailId());
    //						if(buyNum.intValue() >= asgv.getNum().intValue()){//采购数量大于采购售后数量，销售单采购状态肯定为部分采购
    //							//最简单情况--同类产品售后全退
    //							List<SaleorderGoods> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoByBuyorderGoodsId(asgv.getOrderDetailId());
    //							if(sgvList != null && sgvList.size() == 1){
    //								//如果当前销售订单产品有多个，且已全部采购，则本次修改为部分采购，&& sgvList.get(0).getNum().intValue() == buyNum.intValue()
    //								Saleorder saleorder = new Saleorder();
    //								saleorder.setSaleorderId(sgvList.get(0).getSaleorderId());
    //								List<SaleorderGoodsVo> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
    //								if(saleorderGoodsList!= null && saleorderGoodsList.size() > 0 && saleorder.getSaleorderId() > 0){
    //									saleorder.setPurchaseStatus(0);
    //									for (SaleorderGoodsVo sgv : saleorderGoodsList) {
    //										Integer everybuyNum = rBuyorderSaleorderMapper.getBuyorderGoodsNum(sgv.getSaleorderGoodsId());//每个销售商品的采购数量
    //										Integer everybuyafterNum = afterSalesGoodsMapper.getBuyorderAftersaleReturnGoodsBySaleorderGoodsId(sgv.getSaleorderGoodsId());//每个销售商品对应的采购商品的采购退货数量
    //										if(everybuyNum - everybuyafterNum > 0 ){
    //											saleorder.setPurchaseStatus(1);
    //											break;
    //										}
    //									}
    //									res = saleorderMapper.updateByPrimaryKeySelective(saleorder);
    //								}
    //
    //							}
    //						}
    //
    //					}
    //				}


                }
            }else if(ErpConst.TWO.equals(afterSales.getStatus()) && ErpConst.ONE.equals(afterSales.getAtferSalesStatus())){
                //审核通过，退余额，退账期

                if(SysOptionConstant.ID_535.equals(afterSales.getSubjectType()) && SysOptionConstant.ID_539.equals(afterSales.getType())){
//				res = customerPayBill(afterSales);
//				if(res > 0){
//					AfterSalesDetailVo new_afterSales = invoiceAfterMapper.getAfterSalesDetail(afterSales.getAfterSalesId());
//					return new ResultInfo(0,"操作成功",new_afterSales);
//				}
                }else if(SysOptionConstant.ID_536.equals(afterSales.getSubjectType()) && SysOptionConstant.ID_546.equals(afterSales.getType())){
                    //res = supplierPayBill(afterSales);
                }else if(SysOptionConstant.ID_535.equals(afterSales.getSubjectType()) && SysOptionConstant.ID_543.equals(afterSales.getType())){
                    //return customerReturnMoney(afterSales);//售后退款
				/*if(result.getCode() == 0){
					AfterSalesDetailVo new_afterSales = invoiceAfterMapper.getAfterSalesDetail(afterSales.getAfterSalesId());
					return new ResultInfo(0,"操作成功",new_afterSales);
				}*/

                }

            }

            if (res > 0 ){

                if (SysOptionConstant.ID_2.equals(afterSales.getAtferSalesStatus()) ){

                    if(SysOptionConstant.ID_539.equals( afterSales.getType())){
                        //销售退货刷销售单的票货款
                        afterSalesService.updateSaleorderAllStatus(afterSales.getAfterSalesId(), afterSales.getOrderId());
                        saleorderDataService.getIsEnd(afterSales.getOrderId());
                    }else if(afterSales.getType()==546){
                        //采购退货刷采购单的票货款
                        afterSalesService.updateBuyorderAllStatus(afterSales.getAfterSalesId(), afterSales.getOrderId());
                    }

                }

                //审核通过--进行中
                if(SysOptionConstant.ID_2.equals(afterSales.getStatus()) && SysOptionConstant.ID_1.equals(afterSales.getAtferSalesStatus())){
                    //审核通过，退余额，退账期

                    //销售--退货及退款
                    if(SysOptionConstant.ID_535.equals(afterSales.getSubjectType()) && (SysOptionConstant.ID_539.equals(afterSales.getType()) || SysOptionConstant.ID_543.equals(afterSales.getType()))){

                        //发送消息--售后销售订单退货-退款审核通过（钱退到账户余额）
                        /*AfterSalesDetailVo new_afterSales = (AfterSalesDetailVo)result.getData();

                        if(new_afterSales != null && new_afterSales.getTraderId() != null){
                            //根据客户Id查询客户负责人
                            List<Integer> userIdList = userMapper.getUserIdListByTraderId(new_afterSales.getTraderId(),ErpConst.ONE);
                            Map<String,String> map = new HashMap<>();
                            map.put("afterorderNo", new_afterSales.getAfterSalesNo());
                            MessageUtil.sendMessage(40, userIdList, map,
                                    "./order/saleorder/viewAfterSalesDetail.do?afterSalesId="+new_afterSales.getAfterSalesId());//
                        }*/

                    }

                }


                return ResultInfo.success();
            }

            return ResultInfo.error();

        } catch (Exception e){
            logger.error("editApplyAudit error:{}",e);
            return null;
        }

    }

    @Override
    public ResultInfo<?> updateUnlockSaleOrderWarning(Integer saleorderId) {

        try{

            long nowTime = DateUtil.gainNowDate();

            List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);

            if(!CollectionUtils.isEmpty(saleorderGoodsList)){

                // 时效监控开始时间不为空，才去更新，不然不需要重置
                saleorderGoodsList.stream().filter(Objects::nonNull).filter(s->(null!=s.getAgingTime() && s.getAgingTime() > 0)).forEach(s->{

                    SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();

                    saleorderGoodsVo.setSaleorderGoodsId(s.getSaleorderGoodsId());
                    saleorderGoodsVo.setWarnLevel(null);
                    saleorderGoodsVo.setAging(ErpConst.ZERO);
                    saleorderGoodsVo.setAgingTime(nowTime);

                    saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);

                });

            }

        }catch (Exception e){
            logger.error("解锁时,更新销售单预警状态失败，销售单id:{},错误：{}",saleorderId,e);
            return ResultInfo.error("解锁更新销售单预警失败");
        }

        return ResultInfo.success("解锁更新销售单预警成功");

    }

    @Override
    public ResultInfo<?> checkAtGoodsInfo(AfterSales afterSales) {
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        AfterSales afterSale = afterSalesMapper.selectByPrimaryKey(afterSales.getAfterSalesId());
        List<AfterSalesGoodsVo> afterSalesGoodsList = afterSalesGoodsMapper.getAtGoodsInfoList(afterSales.getAfterSalesId());
        if(!CollectionUtils.isEmpty(afterSalesGoodsList)){
            List<AfterSalesGoodsVo> haveInstall = afterSalesGoodsList.stream().filter(item -> ErpConst.ONE.equals(item.getHaveInstallation())).collect(Collectors.toList());
            List<AfterSalesGoodsVo> noneInstall = afterSalesGoodsList.stream().filter(item -> ErpConst.ZERO.equals(item.getHaveInstallation())).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(haveInstall) && CollectionUtils.isNotEmpty(noneInstall)){
                //一个售后安调单中同时存在含安调和不含安调的商品，则拦截申请审核
                resultInfo = new ResultInfo(-1,"安调商品与所选售后类型不符，请重新编辑选择");
            }
            if(CollectionUtils.isNotEmpty(noneInstall) && AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(afterSale.getType())){
                //售后合同安调，存在不含安调的商品，则拦截申请审核
                resultInfo = new ResultInfo(-1,"安调商品与所选售后类型不符，请重新编辑选择");
            }
            if(CollectionUtils.isNotEmpty(haveInstall) && AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(afterSale.getType())){
                //售后非合同安调，存在含安调的商品，则拦截申请审核
                resultInfo = new ResultInfo(-1,"安调商品与所选售后类型不符，请重新编辑选择");
            }
        }
        return resultInfo;
    }

    @Override
    public Integer verifyCloseStatusByBuyorderAfter(AfterSalesVo afterSalesVo) {
        //查询关联采购售后单据
        List<AfterSales> buyorderAfterList = afterSalesMapper.queryBuyorderAfterByAfterSalesId(afterSalesVo.getAfterSalesId());
        List<AfterSalesGoodsVo> afterSalesGoodsList = afterSalesVo.getAfterSalesGoodsList();
        List<Integer> expenseStatusList = new ArrayList<>();
        logger.info("销售售后关闭按钮条件判断开始 AfterSalesId:{}",afterSalesVo.getAfterSalesId());
        if(CollectionUtils.isNotEmpty(afterSalesGoodsList)) {
            //查询关联采购费用售后单据
           expenseStatusList = afterSalesGoodsMapper.getExpenseAfterSalesStatusBySaleGoodsIds(afterSalesGoodsList);
        }
        if(CollectionUtils.isEmpty(buyorderAfterList)&&CollectionUtils.isEmpty(expenseStatusList)){
            //不存在自动创建采购售后单或采购费用售后单
            logger.info("不存在自动创建采购售后单或采购费用售后单可关闭，售后单号:{}",afterSalesVo.getAfterSalesNo());
            return ErpConst.ONE;
        }else {
            //存在自动创建的采购售后单
            for(AfterSales afterSales : buyorderAfterList){
                if(ErpConst.ZERO.equals(afterSales.getAtferSalesStatus()) || ErpConst.THREE.equals(afterSales.getAtferSalesStatus())){
                    //符合待确认，已关闭的条件
                    continue;
                }else {
                    //不符合条件，不可关闭
                    return ErpConst.ZERO;
                }
            }
            //存在采购费用售后单
            for (Integer status : expenseStatusList) {
                if(ErpConst.ZERO.equals(status) || ErpConst.THREE.equals(status)){
                    //符合待确认，已关闭的条件
                    continue;
                }else {
                    //不符合条件，不可关闭
                    logger.info("不符合待确认已关闭条件，不可关闭，售后单号:{}",afterSalesVo.getAfterSalesNo());
                    return ErpConst.ZERO;
                }
            }
            return ErpConst.ONE;
        }
    }

    @Override
    public void setIsDispatchYXB(AfterSalesVo afterSalesVo) {
        logger.info("判断医修帮下派按钮,开始 afterSalesVo:{}", JSON.toJSONString(afterSalesVo));

        //判断订单状态是不是进行中
        Integer atferSalesStatus = afterSalesVo.getAtferSalesStatus();
        if (Objects.isNull(atferSalesStatus) || !ErpConst.ONE.equals(atferSalesStatus)) {
            logger.info("判断医修帮下派按钮,订单状态为空或者不是进行中 AfterSalesId:{}");
            return;
        }
        //有无付款就记录
        Boolean empty = CollectionUtils.isEmpty(afterSalesVo.getAfterPayApplyList());

        //判断是否已经下派了
        AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder().afterSalesOrderId(afterSalesVo.getAfterSalesId()).interfaceType(ErpConst.ZERO).success(ErpConst.ONE).isDelete(ErpConst.ZERO).build();
        List<AfterSalesToYxbDto> afterSalesToYxbDtos = afterSalesToYxbApiService.selectByConditionDispatch(afterSalesToYxbEntity);
        Boolean isSuccessful = afterSalesToYxbDtos.stream().sorted(Comparator.comparing(AfterSalesToYxbDto::getAddTime).reversed()).findFirst().map(dto -> ErpConst.ONE == dto.getSuccess()).orElse(false);

        //是否是技术资讯类
        AfterSalesProcessEnum instance = AfterSalesProcessEnum.getInstance(afterSalesVo.getType());
        Boolean isTechnicalConsultation = Objects.nonNull(instance) && instance.equals(AfterSalesProcessEnum.AFTERSALES_JZ);
        //是否是销售售后安调,如果是，直接返回，不显示医修帮按钮
        Boolean isAfterSalesAt = Objects.nonNull(instance) && instance.equals(AfterSalesProcessEnum.AFTERSALES_AT);
        if (isAfterSalesAt) {
            return;
        }
        //记录为空,没下派
        if (CollectionUtils.isEmpty(afterSalesToYxbDtos)) {
            if (empty) {
                afterSalesVo.setIsShowDispatchButton(ErpConst.ONE);
            }
            if(isTechnicalConsultation){
                afterSalesVo.setIsShowDispatchButton(ErpConst.THREE);
            }
        } else {
            //有记录,显示成功,取消下派按钮
            if (isSuccessful) {
                if (empty) {
                    afterSalesVo.setIsShowDispatchButton(ErpConst.ZERO);
                }
                if(isTechnicalConsultation){
                    afterSalesVo.setIsShowDispatchButton(ErpConst.TWO);
                }
            } else {
                if (empty) {
                    afterSalesVo.setIsShowDispatchButton(ErpConst.ONE);
                }
                if(isTechnicalConsultation){
                    afterSalesVo.setIsShowDispatchButton(ErpConst.THREE);
                }
            }
        }

    }

}
