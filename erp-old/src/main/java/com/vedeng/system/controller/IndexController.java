package com.vedeng.system.controller;

import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.ezadmin.biz.base.controller.SysNavVO;
import com.ezadmin.web.EzResult;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.RoleConstans;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.common.shiro.constant.SecurityConstants;
import com.vedeng.common.util.RequestUtils;
import com.vedeng.dwh.service.DwhThreadLocalService;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.model.Notice;
import com.vedeng.system.model.vo.AdVo;
import com.vedeng.system.model.vo.MenuVo;
import com.vedeng.system.service.*;
import com.vedeng.util.JwtUtilForAi;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;

/**
 * <b>Description:</b><br> 主框架控制器
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.system.controller
 * <br><b>ClassName:</b> IndexController
 * <br><b>Date:</b> 2017年4月25日 上午11:16:56
 */
@Controller()
public class IndexController extends BaseController {
	public static Logger logger = LoggerFactory.getLogger(IndexController.class);

	@Autowired
	@Qualifier("actionService")
	private ActionService actionService;
	@Autowired
	@Qualifier("roleService")	
	private RoleService roleService;
	@Autowired
	@Qualifier("positService")
	private PositService positService;
	@Autowired
	@Qualifier("noticeService")
	private NoticeService noticeService;
	@Autowired
	@Qualifier("noticeUserService")
	private NoticeUserService noticeUserService;
	@Autowired
	@Qualifier("actiongroupService")
	private ActiongroupService actiongroupService;

	// add by Randy.Xu 2020/11/8 16:17 .Desc: . begin
	@Autowired
	DwhThreadLocalService dwhThreadLocalService;
	// add by Randy.Xu 2020/11/8 16:17 .Desc: . end

	@Value("${crm.admin.url}")
	private String crmUrl;

	@Value("${lxcrmUrl}")
	protected  String lxcrmUrl;

	@Autowired
	@Qualifier("userService")
	private UserService userService;

	@Resource
	private AdService adService;
	
	@Resource
	private ReadService readService;
	@Resource
	private VedengSoapService vedengSoapService;

	@Resource
 	private MenuService menuService;

	@Value("${ORG_IDS}")
	private String orgIds;

	@RequestMapping("/publishmonitorlogin")
	@ResponseBody
	public Object publishmonitor(){
		return "OK";
	}
 

	@RequestMapping("/checkInnerWeb.html")
	@ResponseBody
	public String checkInnerWeb(HttpServletResponse response) throws IOException {
		response.addHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        response.setHeader("Access-Control-Allow-Methods", "GET, PUT, OPTIONS, POST");
		return "jsoncallBack("+JSON.toJSONString(R.success("当前是ERP内网环境"))+")";
	}

	@RequestMapping("/checkpreload.html")
	@ResponseBody
	public void checkpreload(HttpServletResponse response) throws IOException {
		User user = userService.getUserById(2);
		response.getWriter().write("success");
	}

	@Value("${noPowerContract:Aadi}")
	private String noPowerContract;

	/**
	 * 无权访问页面
	 *
	 * @return
	 */
	@RequestMapping(value= SecurityConstants.NO_UNAUTHORIZED_PATH)
	public ModelAndView nopower() {
		ModelAndView openOperateMv = new ModelAndView();
		openOperateMv.addObject("noPowerContract",noPowerContract);
		openOperateMv.setViewName("common/nopower");
		return openOperateMv;
	}

	@RequestMapping("/batSyncToPhp.html")
	@ResponseBody
	public String checkpreload(String goodIds){
		String s="";
		for(String skuId:goodIds.split(",")){

			try{
				vedengSoapService.goodsSync(Integer.parseInt(skuId));
			}catch (Exception e){
				logger.error(Contant.ERROR_MSG, e);
				s+=skuId+",";
			}
		}
		return "success"+s;
	}

	@Value("${ai.jumpAiUrl}")
	private String jumpAiUrl;
    @Value("${ai.aiUserIds:1627}")
	private String aiUserIds;

	@NoNeedAccessAuthorization
	@RequestMapping("/jumpAi")
	public ModelAndView jumpAi() {
		HttpSession session = RequestUtils.getRequest().getSession();
		if (session == null) {
			return new ModelAndView("common/404");
		}
		ModelAndView mv = new ModelAndView("index");
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if (user != null && StringUtils.isNotBlank(user.getNumber())) {
			String token = JwtUtilForAi.createJWT(user.getNumber(), "erp", 20L);
			return new ModelAndView("redirect:"+jumpAiUrl+token);
		}else{
			return new ModelAndView("common/404");
		}
	}

//	@Value(value = "${titleJson:[{'code':'1','value':'任务列表'},{'code':'2','value':'线索列表'}]}")
//	private String titleJson;

	@Value("${logoUrl:}")
	private String logoUrl ;

	/**
	 * <b>Description:</b><br> 
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午11:17:22
	 */
	@RequestMapping("/index")
	public ModelAndView index(){
 		HttpSession session=RequestUtils.getRequest().getSession();
		if(session==null){
			return new ModelAndView("login");
		}
		ModelAndView mv = new ModelAndView("index");
		User user = (User) session.getAttribute(Consts.SESSION_USER);
		if(user==null){
			return new ModelAndView("redirect:/login.do");
		}

		mv.addObject("user", user);
        mv.addObject("aiUserIds", aiUserIds);
		List<AdVo> list=new ArrayList<AdVo>();
		if(user.getPositType()!=null){
			//查询最新的发版公告
			Notice notice=noticeService.getNotice(user.getCompanyId(),458);
			//查询当前用户是否存在比该发版公告的查看日志
			int count=(int)noticeUserService.getNoticeUserCount(notice.getNoticeId(), user.getUserId());
			//如果有,且用户是销售的话需要做区别处理
			if(310==user.getPositType()){
				//查询广告列表
				list = getAdVoList(user);
			}
			mv.addObject("notice",notice);
			mv.addObject("count",count);
			mv.addObject("list", list);

		}
		mv.addObject("wsUrl", wsUrl);//此变量一定要传到页面
		if (checkUser(user)){
			mv.addObject("crm", crmUrl);
		}
		mv.addObject("imUrl",imUrl);
		mv.addObject("ssoForCrmJumpUrl",ssoForCrmJumpUrl);
		mv.addObject("logoUrl",logoUrl);
		List<Map<String,Object>> titlelist = new ArrayList<Map<String,Object>>();

		for (JumpErpTitleEnum enumValue : JumpErpTitleEnum.values()) {
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("code",enumValue.getCode());
			map.put("value",enumValue.getTitle());
			titlelist.add(map);
		}
		String titleJson = JSON.toJSONString(titlelist);
		mv.addObject("titleJson",titleJson);
		mv.addObject("lxcrmUrl",lxcrmUrl);
		return mv;
	}

	public Boolean checkUser(User user){
		return userService.checkRole(user, RoleConstans.SALE_COMMISSIONER)
				|| userService.checkRole(user, RoleConstans.SALE_MANAGER)
				|| userService.checkRole(user, RoleConstans.OPERATE_MANAGER)
				|| userService.checkRole(user, RoleConstans.SAFETY_OFFICER) || user.getUserId().equals(1) || user.getUserId().equals(2);
	}

	/**
	 * <b>Description:</b><br> 获取广告列表
	 * @param user
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2018年7月5日 上午10:57:03
	 */
	private List<AdVo> getAdVoList(User user){
		AdVo adVo = new AdVo();
		adVo.setCompanyId(user.getCompanyId());
		adVo.setBannerName("销售首页广告位");
		return adService.getAdVoList(adVo);
	}
	
	/**
	 * <b>Description:</b><br> 框架右侧
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午11:17:30
	 */
	@RequestMapping("/main")
	public String main(){
		return "main";
	}

	/**
	 * <b>Description:</b><br> 菜单
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午11:17:32
	 */
	@RequestMapping("/menu")
	public String menu(Model model,HttpSession session){
		User user =(User)session.getAttribute(ErpConst.CURR_USER);
		if(user!=null){
			//当前用户所在部门
			List<Position> position = positService.getPositionByUserId(user.getUserId());

			model.addAttribute("user", user);

			model.addAttribute("position", position);

			List<MenuVo> menuList = menuService.listMenu(user);

			model.addAttribute("menuList", menuList);

			List<MenuVo> menu2List = getSecondMenuList(menuList);
			if(menu2List.size() > 0){
				boolean flag = false;
				for (MenuVo menuVo : menu2List) {
					// 遍历树 判断是否有缓存

					if("产品检索".equals(menuVo.getName())){
						flag = true;
						break;
					}
				}
				if(flag){
					List<AdVo> list = getAdVoList(user);
					if(CollectionUtils.isNotEmpty(list)){
						//有广告
						model.addAttribute("haveAd", 1);
					}else{
						//无广告
						model.addAttribute("haveAd", 0);
					}
					Integer count = readService.getReadByUserId(user);
					if(count > 0 && list != null && list.size() > 0){
						//点击过
						model.addAttribute("isClick", 1);
					}else{
						//未点击过
						model.addAttribute("isClick", 0);
					}
				}
			}
			model.addAttribute("logoUrl",logoUrl);
			model.addAttribute("lxcrmUrl",lxcrmUrl);
			return "common/side-bar";
		}
		return "login";
	}



	/**
	 *  获取二级菜单
	 * @param menuList
	 * @return
	 */
	private List<MenuVo> getSecondMenuList(List<MenuVo> menuList) {
		if (CollectionUtils.isEmpty(menuList)) {
			return new ArrayList<>();
		}

		List<MenuVo> result = new LinkedList<>();
		for (MenuVo menuVo : menuList) {
			List<MenuVo> children = menuVo.getChildren();
			if (CollectionUtils.isNotEmpty(children)) {
				result.addAll(children);
			}
		}
		return result;
	}

	/**
	 * <b>Description:</b><br> 顶部
	 * @return
	 * @Note
	 * <b>Author:</b> Jerry
	 * <br><b>Date:</b> 2017年4月25日 上午11:17:34
	 */
	@RequestMapping("/top")
	public String top(){
		return "common/top-bar";
	}



	@RequestMapping("/welcome")
	public ModelAndView welcome(HttpServletRequest request) {
		//修改指定页面
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		ModelAndView mav = new ModelAndView("redirect:/home/<USER>/index.do");
		if(user==null){
			return mav;
		}
		if (user.getPositType() != null && user.getPositType() == 310) {// 销售
			//VDERP-10268 登录首页跳转工作台进行人员最部门校验--即所属部门在apollo配置中的部门及其所有子集部门下即跳转工作台新页面start
				// add by Randy.Xu 2020/11/8 4:45 .Desc: . begin
				//判断是否是B2B事业部 或者AED
				/**DwhErpUserDto userInfo = dwhThreadLocalService.getUserInfo(user.getUserId());
				if(null != userInfo && userInfo.getL1Id() != null &&
						(userInfo.getL1Id() == 38||  userInfo.getL1Id()==199 )){
					if( userInfo.getL2Id() == null || !userInfo.getL2Id() .equals(136))
					mav.setViewName("redirect:/workbench/bussinesschance/index.do");
				}**/

			//VDERP-8976 销售跳转工作台
			if(userService.handleOrgIds(user)){
				//配置为空，则所有部门都走新订单流
				mav.setViewName("redirect:/orderstream/saleorder/workbench.do");;
			}

			//VDERP-10268 登录首页跳转工作台进行人员部门校验--即所属部门在apollo配置中的部门及其所有子集部门下即跳转工作台新页面end
		} else if (userService.getSupplyChainFlagByUserId(user.getUserId()) || userService.getAEDFlagByUserId(user.getUserId())) {// 供应链首页默认是供应链工作台
			//VDERP-17504 现需改为配置，以便后期扩展，同时增加几个部门：
			//AED事业部——产品运营部
			//营销中心——产品方案部
			mav.setViewName("redirect:/todolist/supplyChain/welcome.do");
		}
		return mav;
	}


	@RequestMapping("/sys/navs")
	@ResponseBody
	public void navs(Model model,  HttpServletRequest request, HttpServletResponse response) throws IOException {
		User user =getSessionUser(request);
		SysNavVO systemNav = new SysNavVO();
		systemNav.setNavGroup("ERP");
		List<SysNavVO> groupList = new ArrayList<>();
		if(user!=null) {
			List<MenuVo> menuList = menuService.listMenu(user);
			menuList.forEach(item->{
				SysNavVO level1=systemNav.addReturnChild(item.getName(),"","");
				if(CollectionUtils.isNotEmpty(item.getChildren())){
					item.getChildren().forEach(item2->{
						level1.addReturnCurrent(NumberUtils.toLong(item2.getMenuId()+""),item2.getName(),item2.getIconStyle(),item2.getLink());
					});
				}
			});
		}
		groupList.add(systemNav);
		EzResult.instance().code("200").count(29).data(groupList).printJSONUtils(response);
	}
}