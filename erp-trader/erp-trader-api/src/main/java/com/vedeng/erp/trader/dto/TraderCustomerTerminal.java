package com.vedeng.erp.trader.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @description 大数据终端查询接口对象
 * @date 2023/8/10 15:07
 **/
@Setter
@Getter
@NoArgsConstructor
public class TraderCustomerTerminal {

    /**
     * 终端名
     */
    private String hosName;

    /**
     * 唯一id
     */
    private String uniqueId;

    /**
     * 机构类型子集
     */
    private String traderCustomerMarketingType;

    /**
     * 机构性质
     */
    private String institutionNature;

    /**
     * 机构评级
     */
    private String institutionLevel;

    /**
     * 机构类型
     */
    private String institutionType;

    /**
     * 机构类型子集
     */
    private String institutionTypeChild;


    /**
     * 经营状态
     */
    private String managementForms;

    /**
     * 法人代表
     */
    private String legalRepresentative;

    /**
     * 床位数
     */
    private String bedNumber;

    /**
     * 科室
     */
    private String hospitalDepartment;

    /**
     * 终端地区
     */
    private String hosArea = "";

    /**
     * 省
     */
    private String province = "";

    /**
     * 市
     */
    private String city = "";

    /**
     * 区
     */
    private String area = "";

    /**
     * 统一社会信用代码
     */
    private String unifiedSocialCreditCode = "";

    /**
     * 是否选中
     */
    private boolean checked;



}
