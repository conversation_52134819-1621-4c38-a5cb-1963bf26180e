<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>退还款项分配</title>
</head>

<style>
    .el-input.is-disabled .el-input__inner {
        color: #696B6F;
    }
    .el-textarea.is-disabled .el-textarea__inner {
        color: #696B6F;
    }
</style>
<body>
<div id="app">
    <div v-if="!isDetail">
    <div class="refund_info_body" style="font-weight: bold;">退还路径</div>
    <div class="refund_info_body" style="display: flex; align-items: center;">
        <div>
            <span style="color: red;">*</span>
            款项退还：
        </div>
        <el-radio v-model="afterSalesRefundInfoDto.refund" :label="1">退到余额<span style="color: #a6a5a5;">（可用于支付该客户其他订单）</span></el-radio>
        <el-radio v-model="afterSalesRefundInfoDto.refund" :label="2">退回账户<span style="color: #a6a5a5;">（由财务退还到客户账户）</span></el-radio>
<%--        <el-radio v-if="type !== 543" v-model="afterSalesRefundInfoDto.refund" :label="0">无<span style="color: #a6a5a5;">（请在销售订单无任何交易的情况下选择）</span></el-radio>--%>
    </div>
<%--    <div class="refund_info_body" style="font-weight: bold;">退还路径</div>--%>
<%--    <div class="refund_info_table">--%>
<%--        <div>--%>
<%--            <span v-if="refund === 0">款项退还：无</span>--%>
<%--            <span v-else-if="refund === 1">款项退还：退到客户余额</span>--%>
<%--            <span v-else-if="refund === 2">款项退还：退给客户</span>--%>
<%--        </div>--%>
<%--    </div>--%>
    <br>
    <br>

    <div v-if="showRefundInfo">
        <div class="refund_info_table" style="display: flex; justify-content: space-between; font-weight: bold;">
            <div>退款信息</div>
            <div style="padding-right: 20px;">应退金额：{{afterSalesRefundInfoDto.finalRefundableAmount}}</div>
        </div>
        <div class="refund_info_table" style="position: fixed;overflow: auto;height: 80%;width: 95%;">
            <el-table
                    :data="filteredRefundDetailDtoList"
                    border
                    empty-text="暂无信息"
                    fit
                    show-summary
                    :header-cell-style="{background: '#E3ECD9FF'}">
                <el-table-column align="center" prop="traderSubjectName" label="主体交易方式" width="150"></el-table-column>
                <el-table-column align="center" prop="receivedAmount" label="收款金额" width="200"></el-table-column>
                <el-table-column align="center" prop="refundedAmount" label="已退金额" width="200">
                    <template slot="header">
                        <span>已退金额
                            <el-tooltip class="item" effect="dark" content="已完成的售后退货/退款的退款金额" placement="top">
                                <i class="el-icon-warning-outline"></i>
                            </el-tooltip>
                        </span>
                    </template>
                    <template slot-scope="scope">
                        {{ scope.row.refundedAmount }}
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="occupiedAmount" label="占用金额" width="200">
                    <template slot="header">
                        <span>占用金额
                            <el-tooltip class="item" effect="dark" content="进行中的售后退货/退款的退款金额" placement="top">
                                <i class="el-icon-warning-outline"></i>
                            </el-tooltip>
                        </span>
                    </template>
                    <template slot-scope="scope">
                        {{ scope.row.occupiedAmount }}
                    </template>
                </el-table-column>
                <el-table-column align="center" prop="refundableAmount" label="可退金额" width="200"></el-table-column>
                <el-table-column align="center" prop="refundAmount" label="本次退款金额">
                    <template slot="header">
                        <span style="color: red;">*</span>本次退款金额
                    </template>
                    <template slot-scope="scope">
                        <el-input 
                                size="small" 
                                v-model="scope.row.refundAmount" 
                                placeholder="请输入本次退款金额" 
                                maxlength="10"
                                @change="validateInput(scope.row)"></el-input>
                    </template>
                </el-table-column>
            </el-table>
            <el-divider></el-divider>
        </div>
    </div>
    <div style="position: fixed;bottom: 10px;right: 30px">
        <el-button type="success" plain @click="submit()" :disabled="submitDisabled">确定</el-button>
        <el-button type="primary" plain @click="cancel()">取消</el-button>
    </div>
    </div>
    <div v-else>
        <!-- 退款信息查看页面 -->
        <div class="refund_info_body" style="font-weight: bold;">退还路径</div>
        <div class="refund_info_table">
            <div>
                <span v-if="refund === 0">款项退还：无</span>
                <span v-else-if="refund === 1">款项退还：退到余额</span>
                <span v-else-if="refund === 2">款项退还：退回账户</span>
            </div>
        </div>
        <div class="refund_info_table" style="display: flex; justify-content: space-between; font-weight: bold;">
            <div>退款信息</div>
            <div style="padding-right: 20px;">应退金额：{{afterSalesRefundInfoDto.finalRefundableAmount}}</div>
        </div>
        <div class="refund_info_table" style="position: fixed;overflow: auto;height: 80%;width: 95%;">
            <el-table
                    :data="afterSalesRefundInfoDto.refundDetailDtoList"
                    border
                    empty-text="暂无信息"
                    fit
                    show-summary
                    :header-cell-style="{background: '#E3ECD9FF'}">
                <el-table-column align="center" prop="traderSubjectName" label="主体交易方式"></el-table-column>
                <el-table-column align="center" prop="receivedAmount" label="收款金额"></el-table-column>
                <el-table-column align="center" prop="refundAmount" label="本次退款金额"></el-table-column>
            </el-table>
        </div>
    </div>
</div>
</body>

<script src="${pageContext.request.contextPath}/static/api/afterSalesOrder/afterSalesOrder.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    let afterSalesId = ${afterSalesId};
    let type = ${type};
    let refund = ${refund};
    let finalRefundableAmount = ${finalRefundableAmount};
    let isDetail = ${isDetail};
    let vm = new Vue({
        el: '#app',
        data() {
            return {
                afterSalesId: afterSalesId,
                type: type,
                afterSalesRefundInfoDto: {},
                submitDisabled: false,
                refund: refund,
                finalRefundableAmount: finalRefundableAmount
            }
        },
        mounted() {
            loadingApp();
            this.initData(afterSalesId)
        },
        methods: {
            
            initData(afterSalesId) {
                afterSaleRefundInfo({"afterSalesId": afterSalesId})
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            return;
                        }

                        this.afterSalesRefundInfoDto = res.data.data;
                        this.initializeRefundAmounts(); // 初始化退款金额
                    })
                    .catch(err => {
                        this.$message.error("系统异常：" + err.message);
                    });
            },

            cancel() {
                layer.closeAll();
                parent.layer.closeAll();
            },

            submit() {
                //校验
                let totalRefundAmount = 0; // 退款金额合计
                this.afterSalesRefundInfoDto.afterSalesId = this.afterSalesId;
                if (this.afterSalesRefundInfoDto.refund === 2) {  //退还到账户
                    this.afterSalesRefundInfoDto.refundDetailDtoList.forEach(g => {
                        totalRefundAmount += parseFloat(g.refundAmount) || 0; // 计算退款金额合计
                    });


                }else{  //退还到余额
                    this.afterSalesRefundInfoDto.refundDetailDtoList.forEach(row => {
                        if (row.traderSubjectName === '对私') {
                            //do nothing ，对私的金额不能统计在内
                        } else if (row.traderSubjectName === '对公') {
                            totalRefundAmount += parseFloat(row.refundAmount) || 0; // 计算退款金额合计
                        }
                    });

                }
                // 校验必填项
                if (!this.afterSalesRefundInfoDto.refund) {
                    this.$message.error('请填写 款项退还');
                    return;
                }
                if (totalRefundAmount <= 0) {
                    this.$message.error('请填写 本次退款金额');
                    return;
                }
                // 校验金额一致性
                if (totalRefundAmount !== this.finalRefundableAmount) {
                    this.$message.error('本次退款金额合计需等于应退金额');
                    return;
                }
                // 进入 loading 状态
                this.submitDisabled = true;
                this.saveRefund();
            },

            saveRefund(){
                //组装参数
                let afterSalesRefundInfoDto = this.afterSalesRefundInfoDto;
                //发送请求
                this.submitDisabled = true;
                updateRefundInfo(afterSalesRefundInfoDto)
                    .then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            this.submitDisabled = false;
                            return;
                        }
                        this.$message.success("提交成功");
                        // 设置延时关闭弹窗和刷新页面
                        setTimeout(() => {
                            this.submitDisabled = false;
                            parent.layer.closeAll();
                            parent.window.location.reload();
                        }, 2000); // 3000 毫秒后执行
                    })
                    .catch(err => {
                        this.submitDisabled = false;
                        this.$message.error("系统异常：" + err.message);
                    });
            },
            
            validateInput(row) {
                // 将输入值转换为数字
                const value = parseFloat(row.refundAmount);

                // 检查输入值是否有效
                if (isNaN(value) || value < 0) {
                    row.refundAmount = '';
                } else if (value > row.refundableAmount) {
                    row.refundAmount = row.refundableAmount;
                } else {
                    // 检查小数位并格式化
                    row.refundAmount = value % 1 === 0 ? value.toString() : value.toFixed(2);
                }
            },

            initializeRefundAmounts() {
                if (this.afterSalesRefundInfoDto.refund === 2) {
                    let privateRefundableAmount = 0;
                    let publicRefundableAmount = 0;
                    let privateRow = null;
                    let publicRow = null;

                    this.afterSalesRefundInfoDto.refundDetailDtoList.forEach(row => {
                        if (row.traderSubjectName === '对私') {
                            privateRefundableAmount = row.refundableAmount;
                            privateRow = row;
                        } else if (row.traderSubjectName === '对公') {
                            publicRefundableAmount = row.refundableAmount;
                            publicRow = row;
                        }
                    });

                    if (privateRefundableAmount >= this.finalRefundableAmount) {
                        privateRow.refundAmount = this.finalRefundableAmount;
                        publicRow.refundAmount = 0;
                    } else {
                        privateRow.refundAmount = privateRefundableAmount;
                        publicRow.refundAmount = this.finalRefundableAmount - privateRefundableAmount;
                    }
                }
            },

            handleRefundChange(value) { // 新增方法
                if (this.afterSalesRefundInfoDto.refund === 0 && value !== 0) {
                    this.initializeRefundAmounts();
                }
            }

        },
        computed: {
            showRefundInfo() {
                return this.afterSalesRefundInfoDto.refund === 1 || this.afterSalesRefundInfoDto.refund === 2;
            },
            filteredRefundDetailDtoList() {
                if (this.afterSalesRefundInfoDto.refund === 1) {
                    return this.afterSalesRefundInfoDto.refundDetailDtoList.filter(item => item.traderSubjectName === '对公');
                }
                return this.afterSalesRefundInfoDto.refundDetailDtoList; // 其他情况下不进行过滤
            }
        }
    });
</script>
<style>
    #app {
        font-size: 14px;
        font-family: "Helvetica Neue",Helvetica,"PingFang SC","Hiragino Sans GB","Microsoft YaHei","微软雅黑",Arial,sans-serif;
        color: #606266;
    }
    .refund_info_body,.refund_info_table {
        margin-top: 20px;
        padding: 0 20px;
    }
    .advance_class .el-dialog__header .el-dialog__title {
        font-size: 14px;
        font-weight: bold;
    }
    .el-input--small .el-input__inner {
        text-align: center;
    }
</style>
</html>
