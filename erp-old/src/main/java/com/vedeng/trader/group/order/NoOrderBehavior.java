package com.vedeng.trader.group.order;

import com.vedeng.trader.group.BaseBehavior;

import java.util.BitSet;

/**
 * <b>Description:</b>最近无下单<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class NoOrderBehavior extends OrderBaseBehavior {
    @Override
    public BitSet calculate() {
        BitSet res=getBaseTraderSet();
        BitSet orderSet=getOrderData();
        res.andNot(orderSet);
        return res;
    }
}
