<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.buyorder.mapper.BuyorderRebateChargeApplyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity">
    <id column="BUY_ORDER_REBATE_CHARGE_ID" jdbcType="INTEGER" property="buyOrderRebateChargeId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="BUY_ORDER_REBATE_CHARGE_NO" jdbcType="VARCHAR" property="buyOrderRebateChargeNo" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_SUPPLIER_ID" jdbcType="INTEGER" property="traderSupplierId" />
    <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount" />
    <result column="CYCLE_BEGIN_TIME" jdbcType="TIMESTAMP" property="cycleBeginTime" />
    <result column="CYCLE_END_TIME" jdbcType="TIMESTAMP" property="cycleEndTime" />
    <result column="USE_PERIOD_BEGIN_TIME" jdbcType="TIMESTAMP" property="usePeriodBeginTime" />
    <result column="USE_PERIOD_END_TIME" jdbcType="TIMESTAMP" property="usePeriodEndTime" />
    <result column="BILL_URL_IDS" jdbcType="VARCHAR" property="billUrlIds" />
    <result column="DETAIL_URL_IDS" jdbcType="VARCHAR" property="detailUrlIds" />
    <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
    <result column="AUDIT_TIME" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="BRAND_IDS" jdbcType="VARCHAR" property="brandIds" />
    <result column="HEAD_USER_ID" jdbcType="INTEGER" property="headUserId" />
    <result column="HEAD_USER_NAME" jdbcType="VARCHAR" property="headUserName" />
    <result column="POLICY_TERMS" jdbcType="VARCHAR" property="policyTerms" />
  </resultMap>
  <sql id="Base_Column_List">
    BUY_ORDER_REBATE_CHARGE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    BUY_ORDER_REBATE_CHARGE_NO, TRADER_ID, TRADER_SUPPLIER_ID, TOTAL_AMOUNT, CYCLE_BEGIN_TIME, 
    CYCLE_END_TIME, USE_PERIOD_BEGIN_TIME, USE_PERIOD_END_TIME, BILL_URL_IDS, DETAIL_URL_IDS, 
    AUDIT_STATUS, AUDIT_TIME, REMARK, IS_DELETE, BRAND_IDS, HEAD_USER_ID, HEAD_USER_NAME, POLICY_TERMS
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_BUYORDER_REBATE_CHARGE_APPLY
    where BUY_ORDER_REBATE_CHARGE_ID = #{buyOrderRebateChargeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_BUYORDER_REBATE_CHARGE_APPLY
    where BUY_ORDER_REBATE_CHARGE_ID = #{buyOrderRebateChargeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="BUY_ORDER_REBATE_CHARGE_ID" keyProperty="buyOrderRebateChargeId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity" useGeneratedKeys="true">
    insert into T_BUYORDER_REBATE_CHARGE_APPLY (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      BUY_ORDER_REBATE_CHARGE_NO, TRADER_ID, TRADER_SUPPLIER_ID, 
      TOTAL_AMOUNT, CYCLE_BEGIN_TIME, CYCLE_END_TIME, 
      USE_PERIOD_BEGIN_TIME, USE_PERIOD_END_TIME, 
      BILL_URL_IDS, DETAIL_URL_IDS, AUDIT_STATUS, 
      AUDIT_TIME, REMARK, IS_DELETE, BRAND_IDS, HEAD_USER_ID, HEAD_USER_NAME, POLICY_TERMS
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{buyOrderRebateChargeNo,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}, #{traderSupplierId,jdbcType=INTEGER}, 
      #{totalAmount,jdbcType=DECIMAL}, #{cycleBeginTime,jdbcType=TIMESTAMP}, #{cycleEndTime,jdbcType=TIMESTAMP}, 
      #{usePeriodBeginTime,jdbcType=TIMESTAMP}, #{usePeriodEndTime,jdbcType=TIMESTAMP}, 
      #{billUrlIds,jdbcType=VARCHAR}, #{detailUrlIds,jdbcType=VARCHAR}, #{auditStatus,jdbcType=INTEGER}, 
      #{auditTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER},
      #{brandIds,jdbcType=VARCHAR}, #{headUserId,jdbcType=INTEGER}, #{headUserName,jdbcType=VARCHAR}, #{policyTerms,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="BUY_ORDER_REBATE_CHARGE_ID" keyProperty="buyOrderRebateChargeId" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity" useGeneratedKeys="true">
    insert into T_BUYORDER_REBATE_CHARGE_APPLY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="buyOrderRebateChargeNo != null">
        BUY_ORDER_REBATE_CHARGE_NO,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderSupplierId != null">
        TRADER_SUPPLIER_ID,
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT,
      </if>
      <if test="cycleBeginTime != null">
        CYCLE_BEGIN_TIME,
      </if>
      <if test="cycleEndTime != null">
        CYCLE_END_TIME,
      </if>
      <if test="usePeriodBeginTime != null">
        USE_PERIOD_BEGIN_TIME,
      </if>
      <if test="usePeriodEndTime != null">
        USE_PERIOD_END_TIME,
      </if>
      <if test="billUrlIds != null">
        BILL_URL_IDS,
      </if>
      <if test="detailUrlIds != null">
        DETAIL_URL_IDS,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="auditTime != null">
        AUDIT_TIME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="brandIds != null">
        BRAND_IDS,
      </if>
      <if test="headUserId != null">
        HEAD_USER_ID,
      </if>
      <if test="headUserName != null">
        HEAD_USER_NAME,
      </if>
      <if test="policyTerms != null">
        POLICY_TERMS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="buyOrderRebateChargeNo != null">
        #{buyOrderRebateChargeNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSupplierId != null">
        #{traderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="cycleBeginTime != null">
        #{cycleBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleEndTime != null">
        #{cycleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodBeginTime != null">
        #{usePeriodBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodEndTime != null">
        #{usePeriodEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billUrlIds != null">
        #{billUrlIds,jdbcType=VARCHAR},
      </if>
      <if test="detailUrlIds != null">
        #{detailUrlIds,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="brandIds != null">
        #{brandIds,jdbcType=VARCHAR},
      </if>
      <if test="headUserId != null">
        #{headUserId,jdbcType=INTEGER},
      </if>
      <if test="headUserName != null">
        #{headUserName,jdbcType=VARCHAR},
      </if>
      <if test="policyTerms != null">
        #{policyTerms,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity">
    update T_BUYORDER_REBATE_CHARGE_APPLY
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="buyOrderRebateChargeNo != null">
        BUY_ORDER_REBATE_CHARGE_NO = #{buyOrderRebateChargeNo,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderSupplierId != null">
        TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER},
      </if>
      <if test="totalAmount != null">
        TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      </if>
      <if test="cycleBeginTime != null">
        CYCLE_BEGIN_TIME = #{cycleBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cycleEndTime != null">
        CYCLE_END_TIME = #{cycleEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodBeginTime != null">
        USE_PERIOD_BEGIN_TIME = #{usePeriodBeginTime,jdbcType=TIMESTAMP},
      </if>
      <if test="usePeriodEndTime != null">
        USE_PERIOD_END_TIME = #{usePeriodEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="billUrlIds != null">
        BILL_URL_IDS = #{billUrlIds,jdbcType=VARCHAR},
      </if>
      <if test="detailUrlIds != null">
        DETAIL_URL_IDS = #{detailUrlIds,jdbcType=VARCHAR},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      </if>
      <if test="auditTime != null">
        AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="brandIds != null">
        BRAND_IDS = #{brandIds,jdbcType=VARCHAR},
      </if>
      <if test="headUserId != null">
        HEAD_USER_ID = #{headUserId,jdbcType=INTEGER},
      </if>
      <if test="headUserName != null">
        HEAD_USER_NAME = #{headUserName,jdbcType=VARCHAR},
      </if>
      <if test="policyTerms != null">
        POLICY_TERMS = #{policyTerms,jdbcType=VARCHAR},
      </if>
    </set>
    where BUY_ORDER_REBATE_CHARGE_ID = #{buyOrderRebateChargeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.buyorder.domain.entity.BuyOrderRebateChargeApplyEntity">
    update T_BUYORDER_REBATE_CHARGE_APPLY
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      BUY_ORDER_REBATE_CHARGE_NO = #{buyOrderRebateChargeNo,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_SUPPLIER_ID = #{traderSupplierId,jdbcType=INTEGER},
      TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
      CYCLE_BEGIN_TIME = #{cycleBeginTime,jdbcType=TIMESTAMP},
      CYCLE_END_TIME = #{cycleEndTime,jdbcType=TIMESTAMP},
      USE_PERIOD_BEGIN_TIME = #{usePeriodBeginTime,jdbcType=TIMESTAMP},
      USE_PERIOD_END_TIME = #{usePeriodEndTime,jdbcType=TIMESTAMP},
      BILL_URL_IDS = #{billUrlIds,jdbcType=VARCHAR},
      DETAIL_URL_IDS = #{detailUrlIds,jdbcType=VARCHAR},
      AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER},
      AUDIT_TIME = #{auditTime,jdbcType=TIMESTAMP},
      REMARK = #{remark,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      BRAND_IDS = #{brandIds,jdbcType=VARCHAR},
      HEAD_USER_ID = #{headUserId,jdbcType=INTEGER},
      HEAD_USER_NAME = #{headUserName,jdbcType=VARCHAR},
      POLICY_TERMS = #{policyTerms,jdbcType=VARCHAR}
    where BUY_ORDER_REBATE_CHARGE_ID = #{buyOrderRebateChargeId,jdbcType=INTEGER}
  </update>

  <select id="findByAll" resultType="com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto">
    select
      tbrca.BUY_ORDER_REBATE_CHARGE_ID,
      tbrca.BUY_ORDER_REBATE_CHARGE_NO,
      tt.TRADER_NAME AS traderSupplierName,
      tbrca.TOTAL_AMOUNT,
      tbrca.CYCLE_BEGIN_TIME,
      tbrca.CYCLE_END_TIME,
      tbrca.USE_PERIOD_BEGIN_TIME,
      tbrca.USE_PERIOD_END_TIME,
      tbrca.BILL_URL_IDS,
      tbrca.DETAIL_URL_IDS,
      tbrca.AUDIT_STATUS,
      tbrca.REMARK,
      tbrca.CREATOR_NAME,
      tbrca.ADD_TIME,
      tbrca.BRAND_IDS,
      tbrca.HEAD_USER_ID,
      tbrca.HEAD_USER_NAME,
      tbrca.POLICY_TERMS
    from
      T_BUYORDER_REBATE_CHARGE_APPLY tbrca
        LEFT JOIN T_TRADER tt ON
        tbrca.TRADER_ID = tt.TRADER_ID
    WHERE
      tbrca.IS_DELETE = 0
      <if test="keyWords != null and keyWords != '' ">
        AND (tbrca.BUY_ORDER_REBATE_CHARGE_NO LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%' )
            OR tt.TRADER_NAME LIKE CONCAT('%',#{keyWords,jdbcType=VARCHAR},'%' )
        )
      </if>
      <if test="usePeriodStatus != null and usePeriodStatus == 1">
        AND DATE_FORMAT(tbrca.USE_PERIOD_END_TIME, '%Y-%m-%d') <![CDATA[<]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
      </if>
      <if test="usePeriodStatus != null and usePeriodStatus == 2">
        AND DATE_FORMAT(tbrca.USE_PERIOD_END_TIME, '%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(NOW(), '%Y-%m-%d')
      </if>
      <if test="headUserId != null and headUserId > 0">
        AND tbrca.HEAD_USER_ID = #{headUserId,jdbcType=INTEGER}
      </if>
      <if test="auditStatus != null and auditStatus != -1">
        AND tbrca.AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER}
      </if>
      <if test="brandIds != null and brandIds != '' ">
        AND find_in_set (tbrca.BRAND_IDS, #{brandIds,jdbcType=VARCHAR})
      </if>
      order by tbrca.ADD_TIME desc
  </select>
  <select id="findById" resultType="com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyDto">
    select
      tbrca.*,
      tt.TRADER_NAME AS traderSupplierName
    from
      T_BUYORDER_REBATE_CHARGE_APPLY tbrca
        LEFT JOIN T_TRADER tt ON
        tbrca.TRADER_ID = tt.TRADER_ID
    WHERE
      tbrca.BUY_ORDER_REBATE_CHARGE_ID = #{buyOrderRebateChargeId,jdbcType=INTEGER}
  </select>

  <update id="updateStatus">
    update T_BUYORDER_REBATE_CHARGE_APPLY set AUDIT_STATUS = #{status,jdbcType=INTEGER}
    where BUY_ORDER_REBATE_CHARGE_ID = #{rebateChargeId,jdbcType=INTEGER}
    </update>
</mapper>