package com.vedeng.goods.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.goods.dto.BrandFrontDto;
import com.vedeng.goods.mapper.BrandMapper;
import com.vedeng.goods.service.BrandApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class BrandApiServiceImpl implements BrandApiService {
    @Autowired
    private BrandMapper brandMapper;

    @Override
    public String queryBrandNameById(Integer brandId) {
        String brandName = brandMapper.queryBrandNameById(brandId);
        return brandName == null ? "" : brandName;
    }

    @Override
    public List<BrandFrontDto> queryBrand(String brandName, Integer limit) {

        if (StrUtil.isEmpty(brandName)) {
            return Collections.emptyList();
        }
        return brandMapper.selectByBrandNameLike(brandName, limit);
    }

    @Override
    public List<BrandFrontDto> getByBrandIdList(List<Integer> brandIdList) {
        return CollectionUtils.isEmpty(brandIdList) ? new ArrayList<>() : brandMapper.getByBrandIdList(brandIdList);
    }

    @Override
    public List<BrandFrontDto> getBrand(List<Integer> brandIds) {

        if (CollUtil.isEmpty(brandIds)) {
            return Collections.emptyList();
        }

        return brandMapper.selectByBrandIds(brandIds);
    }

    @Override
    public List<BrandFrontDto> queryTransactionBrandDropdown() {
        List<BrandFrontDto> brandFrontDtos = brandMapper.queryAllBrand();
        return brandFrontDtos;
    }
}
