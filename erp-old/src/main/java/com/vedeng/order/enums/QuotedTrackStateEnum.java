package com.vedeng.order.enums;

import lombok.Getter;

/**
 * 报价单的咨询答复状态枚举
 *
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum QuotedTrackStateEnum {

    DOING(0, "跟单中"),
    DONE(1, "成单"),
    MISSED(2, "失单")
    ;

    private Integer state;
    private String message;

    /**
     * Constructor with state and message.
     *
     * @param state
     * @param message
     */
    QuotedTrackStateEnum(Integer state, String message) {
        this.state = state;
        this.message = message;
    }

    /**
     * Checks.
     *
     * @param state
     * @return
     */
    public static boolean checkState(Integer state) {
        for (QuotedTrackStateEnum stateEnum : values()) {
            if (stateEnum.getState().equals(state)) {
                return true;
            }
        }
        return false;
    }
}
