package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveRefundBill;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeeSaleOutStockDetailDto;
import org.mapstruct.*;

import java.util.List;


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface KingDeeReceiveRefundBillConvertor extends BaseMapStruct<KingDeeReceiveRefundBill, KingDeeReceiveRefundBillDto> {
    @Mapping(target = "kingDeeReceiveRefundBillId", source = "kingDeeReceiveRefundBillId")
    @Mapping(target = "FId", source = "FID")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FBillTypeId", source = "FBillTypeID")
    @Mapping(target = "FDate", source = "FDATE")
    @Mapping(target = "FContactUnitType", source = "FCONTACTUNITTYPE")
    @Mapping(target = "FContactUnit", source = "FCONTACTUNIT")
    @Mapping(target = "FRectUnitType", source = "FCONTACTUNITTYPE")
    @Mapping(target = "FRectUnit", source = "FRECTUNIT")
    @Mapping(target = "FSettleOrgId", source = "FSETTLEORGID")
    @Mapping(target = "FSaleOrgId", source = "FSALEORGID")
    @Mapping(target = "FPayOrgId", source = "FPAYORGID")
    @Mapping(target = "FBusinessType", source = "FBUSINESSTYPE")
    @Mapping(target = "FQzokLsh", source = "f_QZOK_LSH")
    @Mapping(target = "FQzokBddjtid", source = "f_QZOK_BDDJTID")
    @Mapping(target = "FQzokZdtjyh", source = "FQzokZdtjyh")
    @Mapping(target = "FRefundBillEntry", source = "FREFUNDBILLENTRY",qualifiedByName="fEntityListToJsonArray")
    @Override
    KingDeeReceiveRefundBill toEntity(KingDeeReceiveRefundBillDto dto);


    @Named("fEntityListToJsonArray")
    default String entryListToJsonArray(List<KingDeeReceiveRefundEntryDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONUtil.toJsonStr(source);
    }
}
