package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeExpressReceiptEntity;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收单 entity 、dto 互转
 * @date
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeExpressReceiptConvertor extends BaseMapStruct<KingDeeExpressReceiptEntity, KingDeeExpressReceiptDto> {

	/**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "fBillNo", source = "FBillNo")
    @Mapping(target = "fQzokOrgId", source = "FQzokOrgid")
    @Mapping(target = "fQzokYsddh", source = "FQzokYsddh")
    @Mapping(target = "fQzokGsywdh", source = "FQzokGsywdh")
    @Mapping(target = "fQzokCrkdh", source = "FQzokCrkdh")
    @Mapping(target = "fQzokKdh", source = "FQzokKdh")
    @Mapping(target = "fQzokYwlx", source = "FQzokYwlx")
    @Mapping(target = "fQzokQssj", source = "FQzokQssj")
    @Mapping(target = "fQzokWlgs", source = "FQzokWlgs")
    @Mapping(target = "fQzokWlbm", source = "FQzokWlbm")
    @Mapping(target = "fQzokXlh", source = "FQzokXlh")
    @Mapping(target = "fQzokPch", source = "FQzokPch")
    @Mapping(target = "fQzokFhsl", source = "FQzokFhsl")
    @Mapping(target = "fQzokSjr", source = "FQzokSjr")
    @Mapping(target = "fQzokDh", source = "FQzokDh")
    @Mapping(target = "fQzokDz", source = "FQzokDz")
    @Mapping(target = "fQzokBddjbh", source = "FQzokBddjbh")
    @Mapping(target = "fQzokSfsc", source = "FQzokSfsc")
    @Mapping(target = "fQzokSfjrcb", source = "FQzokSfjrcb")
    @Mapping(target = "fQzokSfzp", source = "FQzokSfzp")
    @Override
    KingDeeExpressReceiptEntity toEntity(KingDeeExpressReceiptDto dto);

    /**
     * Entity转DTO
     *
     * @param entity
     * @return
     */
    @Mapping(target = "id", source = "id")
    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokOrgid", source = "FQzokOrgId")
    @Mapping(target = "FQzokYsddh", source = "FQzokYsddh")
    @Mapping(target = "FQzokGsywdh", source = "FQzokGsywdh")
    @Mapping(target = "FQzokCrkdh", source = "FQzokCrkdh")
    @Mapping(target = "FQzokKdh", source = "FQzokKdh")
    @Mapping(target = "FQzokYwlx", source = "FQzokYwlx")
    @Mapping(target = "FQzokQssj", source = "FQzokQssj")
    @Mapping(target = "FQzokWlgs", source = "FQzokWlgs")
    @Mapping(target = "FQzokWlbm", source = "FQzokWlbm")
    @Mapping(target = "FQzokXlh", source = "FQzokXlh")
    @Mapping(target = "FQzokPch", source = "FQzokPch")
    @Mapping(target = "FQzokFhsl", source = "FQzokFhsl")
    @Mapping(target = "FQzokSjr", source = "FQzokSjr")
    @Mapping(target = "FQzokDh", source = "FQzokDh")
    @Mapping(target = "FQzokDz", source = "FQzokDz")
    @Mapping(target = "FQzokBddjbh", source = "FQzokBddjbh")
    @Mapping(target = "FQzokSfsc", source = "FQzokSfsc")
    @Mapping(target = "FQzokSfjrcb", source = "FQzokSfjrcb")
    @Mapping(target = "FQzokSfzp", source = "FQzokSfzp")
    @Override
    KingDeeExpressReceiptDto toDto(KingDeeExpressReceiptEntity entity);

    
}
