package com.vedeng.erp.aftersale.buzlogic.create.Impl;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.aftersale.buzlogic.create.AfterSalesBuzLogic;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.service.SaleorderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <b>Description:</b><br>
 * 类解释
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.erp.aftersale.buzlogic.create.Impl <br>
 * <b>ClassName:</b> HhAfterSalesBuzLogic <br>
 * <b>Date:</b> 2021/10/11 11:24 <br>
 */
@Service
public class HhAfterSalesBuzLogic extends AfterSalesBuzLogic {

    @Autowired
    private SaleorderService saleorderService;
    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    /**
     * <b>Description:</b><br>
     * 订单未全部付款前不允许创建换货单
     * 
     * @param afterSalesVo
     * @return com.vedeng.common.model.ResultInfo
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/10/11 13:54
     */
    public ResultInfo hhVerifyAfterSales(AfterSalesVo afterSalesVo){
        ResultInfo resultInfo = new ResultInfo(0,"校验无误");
        if (StockOperateTypeConst.AFTERORDER_CHANGE_FINSH.equals(afterSalesVo.getType())) {
            Saleorder saleorder = saleorderService.getsaleorderbySaleorderId(afterSalesVo.getOrderId());
            if (!OrderConstant.ORDER_ALL_PAYMENT.equals(saleorder.getPaymentStatus())) {
                resultInfo = new ResultInfo(-1, "订单未全部付款前不允许创建换货单");
            }
            if (afterSalesVo.getAfterSalesNum() == null) {
                resultInfo = new ResultInfo(-1, "销售售后换货单不得没有商品");
            }
            for (String afterSaleNum : afterSalesVo.getAfterSalesNum()) {
                if (!"".equals(afterSaleNum)) {
                    Integer saleorderGoodsId = Integer.valueOf(afterSaleNum.split("\\|")[0]);
                    SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);
                    if (saleorderGoods.getDeliveryNum() == null || saleorderGoods.getDeliveryNum().equals(0)) {
                        resultInfo = new ResultInfo(-1, saleorderGoods.getSku() + "未发货的商品不得申请换货");
                    }
                }
            }
        }
        return resultInfo;
    }
    @Override
    public ResultInfo run(List<String> sequence, AfterSalesVo afterSalesVo, User user){
        super.init(sequence,afterSalesVo,user);
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "hhVerifyAfterSales":
                    if(resultInfo.getCode().equals(0)) {
                        resultInfo = this.hhVerifyAfterSales(super.getAfterSalesVo());
                    }
                    break;
                case "saveAfterSale":
                    if(resultInfo.getCode().equals(0)) {
                        resultInfo = super.saveAfterSale(super.getAfterSalesVo(),super.getUser());
                    }
                    break;
                default:
                    break;
            }
        }
        return resultInfo;
    }
}
