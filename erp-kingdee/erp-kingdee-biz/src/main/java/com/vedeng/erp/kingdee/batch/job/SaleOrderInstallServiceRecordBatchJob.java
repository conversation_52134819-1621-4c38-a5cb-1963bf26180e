package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesInstallServiceRecordDetailDto;
import com.vedeng.erp.kingdee.batch.processor.BatchInstallServiceRecordProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchInstallServiceRecordWriter;
import com.vedeng.erp.kingdee.dto.KingDeeInstallServiceRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.task.TaskExecutor;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 安调记录推送金蝶job
 * @date 2023/02/17 10:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderInstallServiceRecordBatchJob extends BaseJob {


    @Autowired
    private BatchInstallServiceRecordProcessor batchInstallServiceRecordProcessor;

    @Autowired
    private BatchInstallServiceRecordWriter batchInstallServiceRecordWriter;



    public Job installServiceRecordFlowJob() {
        return jobBuilderFactory.get("installServiceRecordFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(installServiceRecord())
                .build();
    }


    private Step installServiceRecord() {
        return stepBuilderFactory.get("销售售后安调记录推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchAfterSalesInstallServiceRecordDetailDto, KingDeeInstallServiceRecordDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSalesInstallServiceRecordDetailDtoReader(null,null))
                .processor(batchInstallServiceRecordProcessor)
                .writer(batchInstallServiceRecordWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchAfterSalesInstallServiceRecordDetailDto> batchAfterSalesInstallServiceRecordDetailDtoReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchAfterSalesInstallServiceRecordDetailDto installServiceRecordDetailDto = BatchAfterSalesInstallServiceRecordDetailDto
                .builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchAfterSalesInstallServiceRecordDetailDto.class.getSimpleName(),"findByAddTime",installServiceRecordDetailDto);
    }
}

