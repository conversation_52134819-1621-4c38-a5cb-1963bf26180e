package com.vedeng.api.standard.adapter.express.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ExpressCreateItemDto implements Serializable {

    private String logisticsNo;

    private Integer logisticsId;
    
    private String logisticsComments;
    
    // 运费
    private BigDecimal amount;

    private Long deliveryTime;
    
    private Integer buyOrderGoodsId;
    
    private Integer buyOrderId;
    
    private String sku;
    
    private BigDecimal price;
    
    private BigDecimal num;
    
    private BigDecimal sendNum;
    
}
