<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="选择禁用原因" scope="application" />
<%@ include file="../common/common.jsp"%>
<script type="text/javascript">
    
    function changeDiv(selectValue) {
        if(selectValue == '5'){
            $("#couponDiv").show();
            $("#disableReasonHidden").val($("#disableReason").val());
        }else{
            $("#couponDiv").hide();
            $("#disableReasonHidden").val(selectValue);
        }
    }
    function subForm(){


        if($("#reasonSelect").val() == "0"){
            layer.alert("请填写禁用原因");
            return;
        }

        if($("#reasonSelect").val() == "5" && $("#disableReason").val() == ""){
            layer.alert("请填写禁用原因");
            return;
        }

        if($("#reasonSelect").val() == "5"){
            $("#disableReasonHidden").val($("#disableReason").val());
        }

        var onSaleStatus = '${onSaleStatus}';
        //未上架
        if(onSaleStatus == 0){
            disablePrice();
            return;
        }

        layer.confirm("该商品已在" + "${platStr}" + "平台上架。禁用后，已核价数据将无法被其他环节使用，确认继续操作吗？？", {
            btn : [ '确定', '取消' ]
            //按钮
        }, function() {
            disablePrice();
        }, function() {
        });

    }
    
    function disablePrice() {
        $.ajax({
            type: "POST",
            url: "./disablePrice.do",
            data: $('#editPurchasePrice').serialize(),
            dataType:'json',
            success: function(data){
                if (data.code == 0) {
                    layer.close(index);
                    window.parent.location.reload();
                } else {
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }
</script>
<div class="form-list">
    <form id="editPurchasePrice" method="post" enctype="multipart/form-data">

        <input type="hidden" name="skuPriceChangeApplyId" value="${skuPriceChangeApplyId}"/>
        <input type="hidden" name="verifyStatus" value="${verifyStatus}"/>
        <input type="hidden" name="skuNo" value="${skuNo}"/>
        <ul>
            <li>
                <div class="form-tips">
                    <lable><font color="red">*</font> 禁用原因:</lable>
                </div>
                <div class='f_left'>
                    <div class="form-blanks">
                        <div class="pos_rel">
                            <select class="input-middle" onchange="changeDiv(this.value)" id="reasonSelect">
                                <option value="0">请选择</option>
                                <option value="运费问题">运费问题</option>
                                <option value="需报单">需报单</option>
                                <option value="价格不稳定">价格不稳定</option>
                                <option value="厂家禁止展示价格">厂家禁止展示价格</option>
                                <option value="不合作">不合作</option>
                                <option value="5">其他</option>
                            </select>
                        </div>
                        <br/>
                        <br/>
                        <div id="couponDiv" style="display: none;">
                            <input type="text" class="input-middle" id="disableReason" placeholder="请填写禁用原因">
                        </div>
                        <input type="hidden" name="disableReason" id="disableReasonHidden">
                    </div>
                </div>
            </li>

        </ul>

        <div class="add-tijiao tcenter">
            <button class="dele" type="button" id="close-layer">取消</button>
            <button type="button" class="bt-bg-style bg-deep-green" onclick="subForm();">提交</button>
        </div>
    </form>
</div>
<%@ include file="../common/footer.jsp"%>