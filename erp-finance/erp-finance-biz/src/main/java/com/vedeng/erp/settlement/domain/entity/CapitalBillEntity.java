package com.vedeng.erp.settlement.domain.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/27 13:18
 **/
/**
    * 资金流水
    */
@Data
public class CapitalBillEntity {
    private Integer capitalBillId;

    private Integer bankBillId;

    /**
    * 记账编号
    */
    private String capitalBillNo;

    /**
    * ERP公司ID(T_COMPANY)
    */
    private Integer companyId;

    /**
    * 交易流水号
    */
    private String tranFlow;

    /**
    * 交易时间
    */
    private Long traderTime;

    /**
    * 交易主体1对公2对私
    */
    private Integer traderSubject;

    /**
    * 交易类型1收入2支出3转移4转入5转出
    */
    private Integer traderType;

    /**
    * 交易方式字典库
    */
    private Integer traderMode;

    /**
    * 付款途径字典库ID
    */
    private Integer paymentType;

    private BigDecimal amount;

    /**
    * 货币单位ID
    */
    private Integer currencyUnitId;

    /**
    * 付款方
    */
    private String payer;

    /**
    * 付款方银行账号
    */
    private String payerBankAccount;

    /**
    * 付款方银行名称
    */
    private String payerBankName;

    /**
    * 收款方
    */
    private String payee;

    /**
    * 收款方银行账号
    */
    private String payeeBankAccount;

    /**
    * 收款方银行名称
    */
    private String payeeBankName;

    /**
    * 备注
    */
    private String comments;

    /**
    * 添加时间
    */
    private Long addTime;

    /**
    * 添加人
    */
    private Integer creator;

    /**
    * 财务添加的用于匹配的流水号
    */
    private String capitalSearchFlow;
}