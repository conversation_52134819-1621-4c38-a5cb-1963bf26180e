<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="申请修改" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ include file="../../component/remarkComponent.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/orderstream/saleorder/modify_apply_page.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="<%= basePath %>static/css/select2.css?rnd=${resourceVersionKey}" />
<script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/style.css">
<script>
	function setContentSelect(content,contact){
		$(contact).select2().val(content).trigger("change");
		$(contact).select2()
	}
</script>
<div class="content mt10 ">

	<div style="height: 60px;text-align: center;margin-bottom: 20px;margin-top: 30px;margin-left: 8%;">
		<div class="t-line-wrap J-line-wrap" data-json='[{"label":"待审核","status":2,"tip":"申请者：${user.username}"},{"label":"审核中","status":0},{"label":"审核通过/审核不通过","status":0}]'></div>
	</div>

	<form action="${pageContext.request.contextPath}/orderstream/saleorder/modifyApplySave.do" method="post" id="editForm">
		<!--  add by Tomcat.Hui 2019/12/3 19:28 .Desc: VDERP-1325 分批开票 有条件展示 start -->
		<input type="hidden" id="invoiceModifyflag" value="${invoiceModifyflag}" >
		<input type="hidden" id="deliveryStatus" value="${saleorder.deliveryStatus}" >
		<input type="hidden" id="orderStreamStatus" value="${saleorder.orderStreamStatus}" >

		<div class="parts content1 " <c:if test="${invoiceModifyflag eq 1 || saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">style="display: none" </c:if>>
			<div class="title-container title-container-blue">
				<div class="table-title nobor">收货信息
				</div>
			</div>
			<div style="border: 1px solid #ddd;padding-top:10px">
				<ul class="payplan">
					<!-- 收款状态-->
					<input type="hidden"  value="${saleorder.paymentStatus}" id="paymentStatusInput">
					<input type="hidden"  value="${saleorder.orderType}" id="orderTypeInput">

					<li>
						<div class="infor_name infor_name96">
							<span>*</span>
							<label>收货客户：</label>
						</div>
						<div class="f_left  customername pos_rel">
							<input type="hidden" name="takeTraderId" id="trader_id_1" value="${saleorder.takeTraderId}">
							<input type="hidden" name="takeTraderName" id="trader_name_1" value="${saleorder.takeTraderName}">
							<span class="font-blue">
                                    <a class="addtitle" href="javascript:void(0);"
									   tabTitle='{"num":"viewcustomer${takeCustomer.traderCustomerId}",
                                                "link":"./trader/customer/baseinfo.do?traderCustomerId=${takeCustomer.traderCustomerId}&traderId=${(saleorder.takeTraderId == null || saleorder.takeTraderId eq 0)?saleorder.traderId:saleorder.takeTraderId}",
                                                "title":"客户信息"}'>
										${saleorder.takeTraderName}
									</a>
                                </span>
						</div>
					</li>
					<c:choose>
						<c:when test="${saleorder.orderType eq 5}">
							<li>
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收货联系人：</label>
								</div>
								<div class="f_left">
									<input maxLength="64" type="text" id="takeTraderContactName_5" name="takeTraderContactName"
											<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>
										   value="${saleorder.takeTraderContactName}" placeholder="请输入收货联系人" />
									<div id="5_takeTraderContactName" style="clear:both"></div>
								</div>
							</li>
							<li>
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收货手机号：</label>
								</div>
								<div class="f_left">
									<input maxLength="11" type="text" id="takeTraderContactMobile_5" name="takeTraderContactMobile"
											<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>
										   value="${saleorder.takeTraderContactMobile}" placeholder="请输入收货手机号" />
									<div id="5_takeTraderContactMobile" style="clear:both"></div>
								</div>
							</li>
							<li>
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收货地址：</label>
								</div>
								<div class="f_left">
									<select  id="takeTraderAddressId-province" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 1)"
											 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
										<option value="0">请选择</option>
										<c:if test="${not empty provinceList }">
											<c:forEach items="${provinceList }" var="prov">
												<option value="${prov.regionId }" <c:if test="${takeTraderAddressIdProvince eq prov.regionId }">selected="selected"</c:if> >${prov.regionName }</option>
											</c:forEach>
										</c:if>
									</select>
									<select  id="takeTraderAddressId-city" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 2)"
											 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
										<option value="0">请选择</option>
										<c:if test="${not empty takeTraderAddressCityList }">
											<c:forEach items="${takeTraderAddressCityList }" var="cy">
												<option value="${cy.regionId }" <c:if test="${takeTraderAddressIdCity eq cy.regionId }">selected="selected"</c:if> >${cy.regionName }</option>
											</c:forEach>
										</c:if>
									</select>
									<select  id="takeTraderAddressId-zone" onchange="changeArea('takeTraderAddressId', 'takeTraderArea_5', 3)"
											 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
										<option value="0">请选择</option>
										<c:if test="${not empty takeTraderAddressZoneList }">
											<c:forEach items="${takeTraderAddressZoneList }" var="zo">
												<option value="${zo.regionId }" <c:if test="${takeTraderAddressIdZone eq zo.regionId }">selected="selected"</c:if> >${zo.regionName }</option>
											</c:forEach>
										</c:if>
									</select>
									<input type="hidden" id="takeTraderArea_5" name="takeTraderArea" value="${saleorder.takeTraderArea}" />
									<input type="hidden" id="takeTraderAddressId" name="takeTraderAreaId" value="${takeTraderAddressIdZone}" />
									<input maxLength="256" class="input-xx" id="takeTraderAddress_5" type="text" name="takeTraderAddress" value="${saleorder.takeTraderAddress}" placeholder="请输入收货地址" />
									<div id="5_takeTraderAddress" style="clear:both"></div>
								</div>
							</li>
						</c:when>
						<c:otherwise>
							<li>
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收货联系人：</label>
								</div>
								<div class="f_left">
									<select class="input-xx" id="trader_contact_1" name="takeTraderContactId"
											<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
										<option value="0">请选择</option>
										<c:if test="${not empty takeTraderContactList}">
											<c:forEach items="${takeTraderContactList}" var="list" varStatus="status">
												<option value="${list.traderContactId}" <c:if test="${list.traderContactId eq saleorder.takeTraderContactId}">selected="selected"</c:if>>${list.name}/${list.telephone}/${list.mobile}</option>
											</c:forEach>
										</c:if>
									</select>
									<span class="mt4 font-blue" id="add_contact_1"  style="cursor: pointer;" onclick="addContact('./addContact.do?traderId=${saleorder.traderId}&traderCustomerId=${customer.traderCustomerId}&indexId=1')">添加联系人</span>
									<input type="hidden" name="takeTraderContactName">
									<input type="hidden" name="takeTraderContactTelephone">
									<input type="hidden" name="takeTraderContactMobile">
									<input type="hidden" name="oldTakeTraderContactId" value="${saleorder.takeTraderContactId}">
									<input type="hidden" name="oldTakeTraderContactName" value="${saleorder.takeTraderContactName}">
									<input type="hidden" name="oldTakeTraderContactTelephone" value="${saleorder.takeTraderContactTelephone}">
									<input type="hidden" name="oldTakeTraderContactMobile" value="${saleorder.takeTraderContactMobile}">
									<div id="takeTraderContactIdMsg" style="clear:both"></div>
								</div>
							</li>
							<li>
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收货地址：</label>
								</div>
									<%--<div class="f_left inputfloat">--%>
								<div class="f_left">
									<c:choose>
										<c:when test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">
											<select class="input-xx" id="address_1" name="takeTraderAddressId" disabled>
												<option value="0">请选择</option>
												<c:if test="${not empty takeTraderAddressList}">
													<c:forEach items="${takeTraderAddressList}" var="list" varStatus="status">
														<option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.takeTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
													</c:forEach>
												</c:if>
											</select>
											<span class="mt4  font-blue" id="add_address_1" onclick="addAddress('./addAddress.do?traderId=${saleorder.takeTraderId}&indexId=1')" style="cursor: pointer;">添加地址</span>

										</c:when>
										<c:otherwise>
											<select class="input-xx" id="address_1" name="takeTraderAddressId">
												<option value="0">请选择</option>
												<c:if test="${not empty takeTraderAddressList}">
													<c:forEach items="${takeTraderAddressList}" var="list" varStatus="status">
														<c:if test="${list.isEnable eq 1}">
															<option value="${list.traderAddress.traderAddressId}" <c:if test="${list.traderAddress.traderAddressId eq saleorder.takeTraderAddressId}">selected="selected"</c:if>>${list.area}/${list.traderAddress.address}</option>
														</c:if>
													</c:forEach>
												</c:if>
											</select>
											<span class="mt4  font-blue" id="add_address_1" onclick="addAddress('./addAddress.do?traderId=${saleorder.takeTraderId}&indexId=1')" style="cursor: pointer;">添加地址</span>

										</c:otherwise>
									</c:choose>

									<input type="hidden" name="takeTraderArea">
									<input type="hidden" name="takeTraderAddress">
									<input type="hidden" name="oldTakeTraderAddressId" value="${saleorder.traderAddressId}">
									<input type="hidden" name="oldTakeTraderArea" value="${saleorder.takeTraderArea}">
									<input type="hidden" name="oldTakeTraderAddress" value="${saleorder.takeTraderAddress}">
									<div id="takeTraderAddressIdMsg" style="clear:both"></div>
								</div>
							</li>
						</c:otherwise>
					</c:choose>
					<li>
						<div class="infor_name infor_name96">
							<span>*</span>
							<label>发货方式：</label>
						</div>
						<div class="f_left inputfloat">
							<select class="input-middle" name="deliveryType" id="deliveryType"
									<c:if test="${(saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5)
									or (saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)
									and saleorder.deliveryType != 481)}">disabled</c:if>>
								<c:forEach var="list" items="${deliveryTypes}">
									<option value="${list.sysOptionDefinitionId}"
											<c:if test="${saleorder.deliveryType == list.sysOptionDefinitionId}">selected="selected"</c:if>
											<c:if test="${(saleorder.deliveryType == 0 or saleorder.deliveryType == null) and list.title=='分批发货'}">selected="selected"</c:if>
									>${list.title}</option>
								</c:forEach>
							</select>
							<input type="hidden" name="oldDeliveryType" value="${saleorder.deliveryType}">
						</div>
					</li>
					<li>
						<div class="infor_name infor_name96">
							<span>*</span>
							<label>发货要求：</label>
						</div>
						<div class="f_left inputfloat">
							<select class="input-middle" name="deliveryClaim" id="deliveryClaimSelect" onchange="deliveryClaimChange();"
									<c:if test="${(saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5) or
								(saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0)
								and saleorder.deliveryClaim == 0)}">disabled</c:if>>
								<option value="0"
										<c:if test="${saleorder.deliveryClaim == 0}">selected="selected"</c:if>>立即发货</option>
								<option value="1"
										<c:if test="${saleorder.deliveryClaim == 1}">selected="selected"</c:if>>等通知发货</option>
							</select>
							<input type="hidden" name="oldDeliveryClaim" value="${saleorder.deliveryClaim}">
						</div>
						<div id = "waitDeadlineDiv">
							<div class="infor_name infor_name96">
								<span>*</span>
								<label>等待截止日</label>
							</div>
							<div class="f_left inputfloat">
								<input class="Wdate f_left input-smaller96 mr5" autocomplete="off" type="text" placeholder="请选择日期"
								<c:if test="${saleorder.status == 1 and (saleorder.deliveryStatus != 0 or saleorder.arrivalStatus != 0) and saleorder.deliveryClaim == 0}"> disabled="disabled" </c:if>
								<c:if test="${deliveryDelayTimeFlag}"> readonly </c:if>
								<c:if test="${!deliveryDelayTimeFlag}">onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'${datePickerMinDate}',maxDate:'${datePickerMaxDate}'})"</c:if>
									   autocomplete="off"
									   name="deliveryDelayTimeStr" id="deliveryDelayTimeStr" value="${saleorder.deliveryDelayTimeStr}" style="width:240px;">
								<span class="reminder" style="color: red;margin-top: 5px;margin-left: 15px;">最长可修改至付款后90天，到期后库位释放</span>
							</div>
							<div id="deliveryDelayTimeStrMsg" style="clear:both;"></div>
							<input type="hidden" name="oldDeliveryDelayTimeStr" value="${saleorder.deliveryDelayTimeStr}">
						</div>
					</li>

<%--					<c:if test="${saleorder.orderType != 5}">--%>
					<c:choose>
						<c:when test="${saleorder.isPrintout == -1}">
							<li id="is_print_li">
								<di id="print_out_order">
									<div class="infor_name infor_name96">
										<span>*</span>
										<label>随货出库单：</label>
									</div>
									<div  style="display: inline-block">
										<select  class="input-middle" id="is_print" onchange="changeIsPrintout()"
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="-1">请选择</option>
											<option value="1">打印</option>
											<option value="2" selected = selected>不打印</option>
										</select>
									</div>
								</di>
								<input type="hidden" id="is_scientificDept" value="${isScientificDep}">
								<input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">

								<div id="isPrintoutMsg" style="clear:both;"></div>
								<div id="isPriceMsg" ></div>
								<input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
								<input id="oldIsPrintout" type="hidden" name="oldIsPrintout" value="${saleorder.isPrintout}">
							</li>
						</c:when>

						<c:when test="${saleorder.isPrintout == 0}">
							<li id="is_print_li">
								<di id="print_out_order">
									<div class="infor_name infor_name96">
										<span>*</span>
										<label>随货出库单：</label>
									</div>
									<div  style="display: inline-block">
										<select  class="input-middle" id="is_print" onchange="changeIsPrintout()"
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="-1">请选择</option>
											<option value="1">打印</option>
											<option value="2" selected = selected>不打印</option>
										</select>
									</div>
								</di>
								<input type="hidden" id="is_scientificDept" value="${isScientificDep}">
								<input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
								<div id="isPrintoutMsg"></div>
								<div id="isPriceMsg" ></div>
								<input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
								<input id="oldIsPrintout" type="hidden" name="oldIsPrintout" value="${saleorder.isPrintout}">
							</li>
						</c:when>
						<c:when test="${saleorder.isPrintout == 1}">

							<li id="is_print_li">
								<div id="print_out_order">
									<div class="infor_name infor_name96">
										<span>*</span>
										<label>随货出库单：</label>
									</div>
									<div  style="display: inline-block">
										<select  class="input-middle" id="is_print" onchange="changeIsPrintout()"
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="-1">请选择</option>
											<option value="1" selected = selected>打印</option>
											<option value="2" >不打印</option>
										</select>
									</div>
									<div id = "print_price" style="display: inline-block">
										<select  id='is_price' name = "isPrintout" class="input-middle" style='height: auto' onchange='changeIsPrice()'
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="0">请选择</option>
											<option value="1" selected = selected>含价格</option>
											<option value="2">不含价格</option>
										</select>
									</div>

								</div>
							</li>
							<input type="hidden" id="is_scientificDept" value="${isScientificDep}">
							<input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
							<div id="isPrintoutMsg"></div>
							<div id="isPriceMsg"></div>
							<input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
							<input id="oldIsPrintout" type="hidden" name="oldIsPrintout" value="${saleorder.isPrintout}">
						</c:when>

						<c:when test="${saleorder.isPrintout == 2}">

							<li id="is_print_li">
								<div id="print_out_order">
									<div class="infor_name infor_name96">
										<span>*</span>
										<label>随货出库单：</label>
									</div>
									<div  style="display: inline-block">
										<select  class="input-middle" id="is_print" onchange="changeIsPrintout()"
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="-1">请选择</option>
											<option value="1" selected = selected>打印</option>
											<option value="2" >不打印</option>
										</select>
									</div>
									<div id = "print_price" style="display: inline-block">
										<select  id='is_price' name = "isPrintout" class="input-middle" style='height: auto' onchange='changeIsPrice()'
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="0">请选择</option>
											<option value="1" >含价格</option>
											<option value="2" selected = selected>不含价格</option>
										</select>
									</div>

								</div>
								<input type="hidden" id="is_scientificDept" value="${isScientificDep}">
								<input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
								<div id="isPrintoutMsg"></div>
								<div id="isPriceMsg" ></div>
								<input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
								<input id="oldIsPrintout" type="hidden" name="oldIsPrintout" value="${saleorder.isPrintout}">
							</li>
						</c:when>

						<c:when test="${saleorder.isPrintout == 3}">

							<li id="is_print_li">
								<div id="print_out_order">
									<div class="infor_name infor_name96">
										<span>*</span>
										<label>随货出库单：</label>
									</div>
									<div  style="display: inline-block">
										<select  class="input-middle" id="is_print" onchange="changeIsPrintout()"
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="-1">请选择</option>
											<option value="1" selected = selected>打印</option>
											<option value="2" >不打印</option>
										</select>
									</div>
								</div>
								<input type="hidden" id="is_scientificDept" value="${isScientificDep}">
								<input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
								<div id="isPrintoutMsg" style="clear:both;"></div>
								<input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
								<input id="oldIsPrintout" type="hidden" name="oldIsPrintout" value="${saleorder.isPrintout}">
							</li>
						</c:when>
						<c:otherwise>
							<li id="is_print_li">
								<div id="print_out_order">
									<div class="infor_name infor_name96">
										<span>*</span>
										<label>随货出库单：</label>
									</div>
									<div  style="display: inline-block">
										<select  class="input-middle" id="is_print" onchange="changeIsPrintout()"
												 <c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>
											<option value="-1">请选择</option>
											<option value="1" >打印</option>
											<option value="2" >不打印</option>
										</select>
									</div>
								</div>
								<input type="hidden" id="is_scientificDept" value="${isScientificDep}">
								<input type="hidden" id="isYxgOrgFlag" value="${isYxgOrgFlag}">
								<div id="isPrintoutMsg" style="clear:both;"></div>
								<div id="isPriceMsg" ></div>
								<input id="is_printout" type="hidden" name="isPrintout" value="${saleorder.isPrintout}">
								<input id="oldIsPrintout" type="hidden" name="oldIsPrintout" value="${saleorder.isPrintout}">
							</li>
						</c:otherwise>
					</c:choose>
<%--					</c:if>--%>
<%--					<li>--%>
<%--						<div class="infor_name infor_name96">--%>
<%--							<label>指定物流公司：</label>--%>
<%--						</div>--%>
<%--						<div class="f_left inputfloat">--%>
<%--							<select class="input-middle" name="logisticsId"--%>
<%--									<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>--%>
<%--								<option value="0">请选择</option>--%>
<%--								<c:forEach var="list" items="${logisticsList}">--%>
<%--									<c:if test="${list.isEnable == 1}">--%>
<%--										<option value="${list.logisticsId}" <c:if test="${saleorder.logisticsId == list.logisticsId}">selected="selected"</c:if> >${list.name}</option>--%>
<%--									</c:if>--%>
<%--								</c:forEach>--%>
<%--							</select>--%>
<%--							<input id="oldLogisticsId" type="hidden" name="oldLogisticsId" value="${saleorder.logisticsId}">--%>
<%--						</div>--%>
<%--					</li>--%>
<%--					<c:if test="${saleorder.orderType eq 0 || saleorder.orderType eq 1 || saleorder.orderType eq 9}">--%>
<%--						<li>--%>
<%--							<div class="infor_name infor_name96">--%>
<%--								<label>运费说明：</label>--%>
<%--							</div>--%>
<%--							<div class="f_left inputfloat">--%>
<%--								<select class="input-middle" name="freightDescription"--%>
<%--										<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">disabled</c:if>>--%>
<%--									<option value="0">请选择</option>--%>
<%--									<c:forEach var="list" items="${freightDescriptions}">--%>
<%--										<option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.freightDescription == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>--%>
<%--									</c:forEach>--%>
<%--								</select>--%>
<%--								<input id="oldFreightDescription" type="hidden" name="oldFreightDescription" value="${saleorder.freightDescription}">--%>
<%--							</div>--%>
<%--						</li>--%>
<%--					</c:if>--%>
					<li>
						<div class="infor_name infor_name96">
							<label>物流备注：</label>
						</div>
						<div class="f_left inputfloat">
							<input type="text"
									<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.orderStreamStatus eq 5}">
										disabled
									</c:if>
								   name="logisticsComments" id="logisticsComments" value="${saleorder.logisticsComments}" placeholder="对内使用，适用于向物流部同事告知发货要求，默认同步客户信息中物流备注" class="input-xx" />
							<input id="oldLogisticsComments" type="hidden" name="oldLogisticsComments" value="${saleorder.logisticsComments}">
						</div>
					</li>
				</ul>
			</div>
		</div>
		<!--  add by Tomcat.Hui 2019/12/3 19:28 .Desc: VDERP-1325 分批开票 有条件展示 end. -->

		<div class="parts content1 content2"  <c:if test="${invoiceAllSend ne null && invoiceAllSend eq true}">
			style="padding-top: 15px;display: none"
		</c:if>>
			<div class="title-container title-container-blue">
				<div class="table-title nobor">
					收票信息
				</div>
			</div>

			<div style="border: 1px solid #ddd;padding-top:10px">
				<ul class="payplan">
<%--					<li>--%>
<%--						<div class="infor_name infor_name96">--%>
<%--							<span>*</span>--%>
<%--							<label>发票是否寄送：</label>--%>
<%--						</div>--%>
<%--						<div class="f_left inputfloat" >--%>
<%--							<input type="radio" name="isSendInvoice" onclick="isSendInvoiceChecked('${saleorder.orderType}', 1)"--%>
<%--								   <c:if test="${saleorder.isSendInvoice eq 1}">checked</c:if> value="1"--%>
<%--								   <c:if test="${saleorder.isHaveInvoice eq 1 && isPartDelive eq 0}">disabled="disabled"</c:if>--%>
<%--								   <c:if test="${saleorder.deliveryStatus ne 0 && saleorder.phtxFlag}">disabled="disabled"</c:if>--%>
<%--							<c:if test="${saleorder.isHaveInvoice eq 1 && isPartDelive eq 1 && saleorder.phtxFlag}">disabled="disabled"</c:if>>--%>
<%--							<label>寄送</label> &nbsp;&nbsp;--%>
<%--							<input type="radio" name="isSendInvoice" onclick="isSendInvoiceChecked('${saleorder.orderType}',0)"--%>
<%--								   <c:if test="${saleorder.isSendInvoice eq 0}">checked</c:if> value="0"--%>
<%--								   <c:if test="${saleorder.isHaveInvoice eq 1 && isPartDelive eq 0}">disabled="disabled"</c:if>--%>
<%--								   <c:if test="${saleorder.deliveryStatus ne 0 && saleorder.phtxFlag}">disabled="disabled"</c:if>--%>
<%--							<c:if test="${saleorder.isHaveInvoice eq 1 && isPartDelive eq 1 && saleorder.phtxFlag}">disabled="disabled"</c:if>>--%>
<%--							<label>不寄送</label>--%>
<%--							<input id="oldIsSendInvoice" type="hidden" name="oldIsSendInvoice" value="${saleorder.isSendInvoice}">--%>
<%--						</div>--%>
<%--					</li>--%>
					<li>
						<input id="isSendInvoice" type="hidden" name="isSendInvoice" value="${saleorder.isSendInvoice}">
						<input id="oldIsSendInvoice" type="hidden" name="oldIsSendInvoice" value="${saleorder.isSendInvoice}">
					</li>

					<c:choose>
						<c:when test="${(saleorder.orderType eq 5 || saleorder.orderType eq 7 || saleorder.orderType eq 8 || saleorder.orderType eq 9)
						&& saleorder.deliveryStatus ne 2}">
							<%--集采线下和线下直销，且发货状态不为全部发货，新增是否票货同行选项--%>
							<li id="isSameAddressLi">
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>是否票货同行</label>
								</div>
								<div class="f_left inputfloat customername pos_rel">
									<input type="radio" name="isSameAddress" onclick="isSameAddressChecked(1)"
										   <c:if test="${saleorder.deliveryStatus eq 1 || saleorder.isHaveInvoiceApply == 1}">disabled="disabled"</c:if>
										   <c:if test="${saleorder.phtxFlag}">checked="checked"</c:if> value="1" lay-ignore>
									<label>票货同行</label> &nbsp;&nbsp;
									<i class="iconbluesigh ml4 contorlIcon"></i>
									<div class="pos_abs customernameshow">
										票货同行规则：<br>
										1、发票是否寄送：寄送<br>
										2、是否票货同行：票货同行<br>
										3、开票方式：自动电子发票/自动数电发票<br>
										4、订单中全部商品的发货方式为“普发”<br>
										5、订单无退票、退货售后单<br>
									</div>
									<input type="radio" name="isSameAddress" onclick="isSameAddressChecked(0)"
										   <c:if test="${(saleorder.deliveryStatus eq 1 && saleorder.phtxFlag) || saleorder.isHaveInvoiceApply == 1}">disabled="disabled"</c:if>
										   <c:if test="${saleorder.isSameAddress eq 0 or saleorder.isSameAddress == null}">checked="checked"</c:if> value="0" lay-ignore>
									<label>票货不同行</label>
									<div id="isSameAddressMsg" style="clear:both"></div>
								</div>
							</li>
							<li id="invoiceSendNodeLi" <c:if test="${saleorder.isSameAddress eq 0}">style="display: none" </c:if>>
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>发票寄送节点</label>
								</div>
								<div class="f_left inputfloat">
									<input type="radio" name="invoiceSendNode" onclick=""
										   <c:if test="${saleorder.invoiceSendNode eq 0}">checked="checked"</c:if>
										   <c:if test="${saleorder.deliveryStatus eq 1 && saleorder.phtxFlag}">disabled="disabled"</c:if>
										   value="0" lay-ignore>
									<label>全部发货时一次寄送</label> &nbsp;
									<input type="radio" name="invoiceSendNode" onclick=""
										   <c:if test="${saleorder.invoiceSendNode eq 1}">checked="checked"</c:if>
										   <c:if test="${saleorder.deliveryStatus eq 1 && saleorder.phtxFlag}">disabled="disabled"</c:if>
										   value="1" lay-ignore>
									<label>每次发货时分别寄送</label>
									<div id="invoiceSendNodeMsg" style="clear:both"></div>
								</div>
								<label style="color: red; font-weight:bold">（只针对"票货同行"订单）</label>
							</li>
						</c:when>
						<c:otherwise>
							<li>
								<input type="hidden" name="isSameAddress" value="${saleorder.isSameAddress}">
								<input type="hidden" name="invoiceSendNode" value="${saleorder.invoiceSendNode}">
							</li>
						</c:otherwise>
					</c:choose>
					<li style="display: none">
						<input type="hidden" name="oldIsSameAddress" value="${saleorder.isSameAddress}">
						<input type="hidden" name="oldInvoiceSendNode" value="${saleorder.invoiceSendNode}">
					</li>
					<li id="invoiceCustomerLi">

						<div class="infor_name infor_name96">
							<span>*</span>
							<label>收票客户：</label>
						</div>
						<div class="f_left  customername pos_rel">
							<input type="hidden" name="invoiceTraderId" id="trader_id_2" value="${saleorder.invoiceTraderId}">
							<input type="hidden" name="invoiceTraderName" id="trader_name_2" value="${saleorder.invoiceTraderName}">
							<span class="font-blue">

                                    <a class="addtitle" href="javascript:void(0);"
									   tabTitle='{"num":"viewcustomer${invoiceCustomer.traderCustomerId}",
                                                "link":"./trader/customer/baseinfo.do?traderCustomerId=${invoiceCustomer.traderCustomerId}&traderId=${(saleorder.invoiceTraderId == null || saleorder.invoiceTraderId eq 0)?saleorder.traderId:saleorder.invoiceTraderId}",
                                                "title":"客户信息"}'>
										${saleorder.invoiceTraderName}
									</a>
                                </span>
						</div>
					</li>
					<c:choose>
						<c:when test="${saleorder.orderType eq 5}">
							<li id="invoiceTraderContactLi">
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收票联系人：</label>
								</div>
								<div class="f_left">
									<input maxLength="64" type="text" id="invoiceTraderContactName_5"
										   <c:if test="${(saleorder.deliveryStatus eq 2 && saleorder.phtxFlag)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)}">readonly</c:if>
										   name="invoiceTraderContactName" value="${saleorder.invoiceTraderContactName}" placeholder="请输入收票联系人" />
									<div id="5_invoiceTraderContactName" style="clear:both"></div>
								</div>
							</li>
							<li  id="invoiceTraderContactMobileLi">
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收票手机号：</label>
								</div>
								<div class="f_left">
									<input maxLength="11" type="text" id="invoiceTraderContactMobile_5"
										   <c:if test="${(saleorder.deliveryStatus eq 2 && saleorder.phtxFlag)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)}">readonly</c:if>
										   name="invoiceTraderContactMobile" value="${saleorder.invoiceTraderContactMobile}" placeholder="请输入收票手机号" />
									<div id="5_invoiceTraderContactMobile" style="clear:both"></div>
								</div>
							</li>
							<li  id="invoiceTraderAddressLi" style="display: none;">
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收票地址：</label>
								</div>
								<div class="f_left">
									<select id="invoiceTraderAddressId-province"
											<c:if test="${((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)}">disabled</c:if>
											onchange="changeArea('invoiceTraderAddressId', 'invoiceTraderArea_5', 1)" >
										<option value="0">请选择</option>
										<c:if test="${not empty provinceList }">
											<c:forEach items="${provinceList }" var="prov">
												<option value="${prov.regionId }" <c:if test="${invoiceTraderAddressIdProvince eq prov.regionId }">selected="selected"</c:if> >${prov.regionName }</option>
											</c:forEach>
										</c:if>
									</select>
									<select id="invoiceTraderAddressId-city"
											<c:if test="${((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)}">disabled</c:if>
											onchange="changeArea('invoiceTraderAddressId', 'invoiceTraderArea_5', 2)" >
										<option value="0">请选择</option>
										<c:if test="${not empty invoiceCityList }">
											<c:forEach items="${invoiceCityList }" var="cy">
												<option value="${cy.regionId }" <c:if test="${invoiceTraderAddressIdCity eq cy.regionId }">selected="selected"</c:if> >${cy.regionName }</option>
											</c:forEach>
										</c:if>
									</select>
									<select id="invoiceTraderAddressId-zone"
											<c:if test="${((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)}">disabled</c:if>
											onchange="changeArea('invoiceTraderAddressId', 'invoiceTraderArea_5', 3)" >
										<option value="0">请选择</option>
										<c:if test="${not empty invoiceZoneList }">
											<c:forEach items="${invoiceZoneList }" var="zo">
												<option value="${zo.regionId }" <c:if test="${invoiceTraderAddressIdZone eq zo.regionId }">selected="selected"</c:if> >${zo.regionName }</option>
											</c:forEach>
										</c:if>
									</select>
									<input type="hidden" name="invoiceTraderArea" id="invoiceTraderArea_5" value="${saleorder.invoiceTraderArea}"/>
									<input type="hidden" name="invoiceTraderAreaId" id="invoiceTraderAddressId" value="${invoiceTraderAddressIdZone}"/>
									<input maxLength="256" type="text" id="invoiceTraderAddress_5"
										   <c:if test="${((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)}">readonly</c:if>
										   class="input-xx" name="invoiceTraderAddress" value="${saleorder.invoiceTraderAddress}" placeholder="请输入收票地址" />
									<div id="5_invoiceTraderAddress" style="clear:both"></div>
								</div>
							</li>
						</c:when>
						<c:otherwise>
							<li id="invoiceTraderContactLi">
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收票联系人：</label>
								</div>
								<div class="f_left  ">
									<select class="input-xx" id="trader_contact_2" name="invoiceTraderContactId"
											<c:if test="${saleorder.isHaveInvoice eq 1 && isPartDelive eq 0}">disabled</c:if>>
										<option value="0">请选择</option>
										<c:if test="${not empty invoiceTraderContactList}">
											<c:forEach items="${invoiceTraderContactList}" var="list" varStatus="status">
												<option value="${list.traderContactId}"
														<c:if test="${list.traderContactId eq saleorder.invoiceTraderContactId}">selected="selected"</c:if>>
														${list.name}/${list.telephone}/${list.mobile}
												</option>
											</c:forEach>
										</c:if>
									</select>
									<span class="mt4 font-blue" id="add_contact_2"  style="cursor: pointer;" onclick="addContact('./addContact.do?traderId=${saleorder.traderId}&traderCustomerId=${customer.traderCustomerId}&indexId=2')">添加联系人</span>
									<input type="hidden" name="invoiceTraderContactName">
									<input type="hidden" name="invoiceTraderContactTelephone">
									<input type="hidden" name="invoiceTraderContactMobile">
									<input type="hidden" name="oldInvoiceTraderContactId" value="${saleorder.invoiceTraderContactId}">
									<input type="hidden" name="oldInvoiceTraderContactName" value="${saleorder.invoiceTraderContactName}">
									<input type="hidden" name="oldInvoiceTraderContactTelephone" value="${saleorder.invoiceTraderContactTelephone}">
									<input type="hidden" name="oldInvoiceTraderContactMobile" value="${saleorder.invoiceTraderContactMobile}">
									<div id="invoiceTraderContactIdMsg" style="clear:both"></div>
								</div>
							</li>
							<li id="invoiceTraderAddressLi" style="display: none;">
								<div class="infor_name infor_name96">
									<span>*</span>
									<label>收票地址：</label>
								</div>
								<div class="f_left ">
									<select class="input-xx" id="address_2" name="invoiceTraderAddressId"
											<c:if test="${saleorder.isHaveInvoice eq 1 && isPartDelive eq 0}">disabled</c:if>>
										<option value="0">请选择</option>
										<c:if test="${not empty invoiceTraderAddressList}">
											<c:forEach items="${invoiceTraderAddressList}" var="list" varStatus="status">
												<option value="${list.traderAddress.traderAddressId}"
														<c:if test="${list.traderAddress.traderAddressId eq saleorder.invoiceTraderAddressId}">selected="selected"</c:if>>
														${list.area}/${list.traderAddress.address}
												</option>
											</c:forEach>
										</c:if>
									</select>
									<span class="mt4  font-blue" id="add_address_2" onclick="addAddress('./addAddress.do?traderId=${saleorder.takeTraderId}&indexId=2')" style="cursor: pointer;">添加地址</span>

									<input type="hidden" name="invoiceTraderArea">
									<input type="hidden" name="invoiceTraderAddress">
									<input type="hidden" name="oldInvoiceTraderAddressId" value="${saleorder.invoiceTraderAddressId}">
									<input type="hidden" name="oldInvoiceTraderArea" value="${saleorder.invoiceTraderArea}">
									<input type="hidden" name="oldInvoiceTraderAddress" value="${saleorder.invoiceTraderAddress}">

									<div id="invoiceTraderAddressIdMsg" style="clear:both"></div>
								</div>
							</li>
						</c:otherwise>
					</c:choose>
<%--					<c:choose>--%>
<%--						<c:when test="${saleorder.orderType eq 5 || saleorder.orderType eq 7 || saleorder.orderType eq 8 || saleorder.orderType eq 9}">--%>
<%--						<div id="invoiceEmailPart">--%>
<%--							<li>--%>
<%--								<div class="infor_name infor_name96">--%>
<%--									<label>收票邮箱：</label>--%>
<%--								</div>--%>
<%--								<div class="f_left inputfloat">--%>
<%--									<input type="text" name="invoiceEmail"--%>
<%--										   <c:if test="${(saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag}">readonly="readonly"</c:if>--%>
<%--										   value="${saleorder.invoiceEmail}" placeholder="请输入收票邮箱" />--%>
<%--									<input type="hidden" name="oldInvoiceEmail" value="${saleorder.invoiceEmail}" />--%>
<%--								</div>--%>
<%--							</li>--%>
<%--						</div>--%>
<%--						</c:when>--%>
<%--						<c:otherwise></c:otherwise>--%>
<%--					</c:choose>--%>
					<li>
<%--						<c:if test="${invoiceapplyFlag eq 0}">--%>
							<div class="infor_name infor_name96">
								<span>*</span>
								<label>发票类型：</label>
							</div>
<%--						</c:if>--%>
						<!-- 获取当前日期 -->
						<jsp:useBean id="now" class="java.util.Date" />
						<fmt:formatDate value="${now}" type="both" dateStyle="long" var="today" pattern="yyyy-MM-dd"/>
						<div class="f_left inputfloat">
								<c:choose>
									<c:when test="${(saleorder.isHaveInvoiceApply eq 1 || saleorder.isHaveInvoice eq 1 )
									|| ((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)}">
										<select class="input-middle" name="invoiceType" disabled="disabled">
											<c:forEach var="list" items="${invoiceTypes}">
												<c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">
													<option value="${list.sysOptionDefinitionId}">${list.title}</option>
												</c:if>
											</c:forEach>
										</select>
									</c:when>
									<c:otherwise>
										<select class="input-middle" name="invoiceType">
											<option value="0">请选择</option>
											<!-- 4月1号后税率只有13% -->
											<c:choose>
												<c:when test="${today >= '2019-04-01'}">
													<c:forEach var="list" items="${invoiceTypes}">
														<c:if test="${list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
															<option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
														</c:if>
													</c:forEach>
												</c:when>
												<c:otherwise>
													<c:forEach var="list" items="${invoiceTypes}">
														<c:if test="${list.sysOptionDefinitionId eq 681 or list.sysOptionDefinitionId eq 682 or list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
															<option value="${list.sysOptionDefinitionId}" <c:if test="${saleorder.invoiceType == list.sysOptionDefinitionId}">selected="selected"</c:if> >${list.title}</option>
														</c:if>
													</c:forEach>
												</c:otherwise>
											</c:choose>
										</select>
									</c:otherwise>
								</c:choose>
							<input type="hidden" id="invoiceapplyFlag" name="invoiceapplyFlag" value="${invoiceapplyFlag}">
							<input type="hidden" name="oldInvoiceType" value="${saleorder.invoiceType}">
							<div id="invoiceTypeMsg" style="clear:both"></div>
						</div>
					</li>
					<li>
						<div class="infor_name infor_name96">
							<label>收票邮箱：</label>
						</div>
						<div class="f_left inputfloat">
							<input class="input-middle" type="text" id="invoiceEmail" name="invoiceEmail"
								   <c:if test="${(saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag}">readonly="readonly"</c:if>
								   value="${saleorder.invoiceEmail}" placeholder="输入后发票会发送至该邮箱" />
							<input type="hidden" name="oldInvoiceEmail" value="${saleorder.invoiceEmail}" />
						</div>
					</li>
					<li>
						<select class="input-middle" name="invoiceMethod" disabled="disabled" style="display: none;">
							<option value="4" selected></option>
						</select>
						<input type="hidden" name="invoiceMethod">
					</li>
					<!--  add by Tomcat.Hui 2019/12/3 19:28 .Desc: VDERP-1325 分批开票 -->
<%--					<div id="invoiceMethodDiv" >--%>

<%--						<li >--%>
<%--							<div class="infor_name infor_name96">--%>
<%--								<span>*</span>--%>
<%--								<label>开票方式：</label>--%>
<%--							</div>--%>
<%--							<div class="f_left inputfloat tips-all">--%>
<%--								<c:choose>--%>
<%--									<c:when test="${(saleorder.isHaveInvoiceApply ==1 || saleorder.isHaveInvoice eq 1 ) && invoiceApply !=null }">--%>
<%--										<select class="input-middle" name="invoiceMethod" disabled="disabled">--%>
<%--											<c:if test="${invoiceApply.isAuto == 1}">--%>
<%--												<option value="1">手动纸质开票</option>--%>
<%--											</c:if>--%>
<%--											<c:if test="${invoiceApply.isAuto == 2}">--%>
<%--												<option value="2">自动纸质开票</option>--%>
<%--											</c:if>--%>
<%--											<c:if test="${invoiceApply.isAuto == 3}">--%>
<%--												<option value="3">自动电子发票</option>--%>
<%--											</c:if>--%>
<%--											<c:if test="${invoiceApply.isAuto == 4}">--%>
<%--												<option value="4">自动数电发票</option>--%>
<%--											</c:if>--%>
<%--										</select>--%>
<%--									</c:when>--%>
<%--									<c:otherwise>--%>
<%--										<select class="input-middle" name="invoiceMethod" disabled="disabled">--%>
<%--											<option value="0">请选择</option>--%>
<%--											<c:if test="${saleorder.invoiceType == 681 or saleorder.invoiceType == 971}">--%>
<%--												<option value="1" <c:if test="${saleorder.invoiceMethod == 1}">selected</c:if>>手动纸质开票</option>--%>
<%--												<option value="2" <c:if test="${saleorder.invoiceMethod == 2}">selected</c:if>>自动纸质开票</option>--%>
<%--												<option value="3" <c:if test="${saleorder.invoiceMethod == 3}">selected</c:if>>自动电子发票</option>--%>
<%--												<option value="4" <c:if test="${saleorder.invoiceMethod == 4}">selected</c:if>>自动数电发票</option>--%>
<%--											</c:if>--%>
<%--											<c:if test="${saleorder.invoiceType == 682  or saleorder.invoiceType == 972}">--%>
<%--												<option value="1" <c:if test="${saleorder.invoiceMethod == 1}">selected</c:if>>手动纸质开票</option>--%>
<%--												<option value="2" <c:if test="${saleorder.invoiceMethod == 2}">selected</c:if>>自动纸质开票</option>--%>
<%--												<option value="4" <c:if test="${saleorder.invoiceMethod == 4}">selected</c:if>>自动数电发票</option>--%>
<%--											</c:if>--%>
<%--										</select>--%>
<%--									</c:otherwise>--%>
<%--								</c:choose>--%>

<%--								<div id="invoiceMethodMsg" style="clear:both"></div>--%>
<%--								<input type="hidden" name="oldInvoiceMethod" value="${saleorder.invoiceMethod}">--%>
<%--								<input type="hidden" name="invoiceMethod">--%>
<%--							</div>--%>
<%--							<div class="tips-error" style="display: none;color:red;line-height: 26px;">--%>
<%--								“手动纸质开票”的订单，不在自动开票推送的范围内，后期需要手动申请开票。--%>
<%--							</div>--%>
<%--						</li>--%>
<%--					</div>--%>

<%--					<li>--%>
<%--						<div class="infor_name infor_name96">--%>
<%--							<label>开票备注：</label>--%>
<%--						</div>--%>
<%--						<div class="f_left inputfloat">--%>
<%--							<c:choose>--%>
<%--								<c:when test="${(saleorder.isHaveInvoiceApply eq 1 and saleorder.orderStreamStatus eq 5 and (saleorder.phtxFlag || saleorder.isSameAddress == null))--%>
<%--										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0) || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 1 && saleorder.phtxFlag)--%>
<%--										   || ((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)}">--%>
<%--									<input type="text" name="invoiceComments" id="invoiceComments" value="${saleorder.invoiceComments}" placeholder="对内使用，适用于向财务部同事告知开票要求" class="input-xx" disabled/>--%>
<%--								</c:when>--%>
<%--								<c:otherwise>--%>
<%--									<input type="text" name="invoiceComments" id="invoiceComments" value="${saleorder.invoiceComments}" placeholder="对内使用，适用于向财务部同事告知开票要求" class="input-xx" />--%>
<%--								</c:otherwise>--%>
<%--							</c:choose>--%>
<%--							<input type="hidden" name="oldInvoiceComments" value="${saleorder.invoiceComments}">--%>
<%--						</div>--%>
<%--					</li>--%>

					<li>
						<div class="infor_name infor_name96">
							<label>暂缓开票：</label>
						</div>
						<div class="f_left inputfloat">
							<c:choose>
								<c:when test="${(saleorder.isHaveInvoiceApply eq 1 and saleorder.orderStreamStatus eq 5 and (saleorder.phtxFlag || saleorder.isSameAddress == null)) || (saleorder.invoiceStatus eq 2)
										   || (saleorder.isHaveInvoice eq 1 && isPartDelive eq 0)
										   || ((saleorder.deliveryStatus eq 1 || saleorder.deliveryStatus eq 2) && saleorder.phtxFlag)}">
									<input type="checkbox" name="isDelayInvoiceCheckbox" class="mt5" onclick="isDelayInvoiceChecked();"<c:if test="${saleorder.isDelayInvoice == 1}">checked="checked"</c:if> disabled>
								</c:when>
								<c:otherwise>
									<input type="checkbox" name="isDelayInvoiceCheckbox" class="mt5" onclick="isDelayInvoiceChecked();"<c:if test="${saleorder.isDelayInvoice == 1}">checked="checked"</c:if>>

								</c:otherwise>
							</c:choose>
							<input type="hidden" name="isDelayInvoice" value="${saleorder.isDelayInvoice}">
							<input type="hidden" name="oldIsDelayInvoice" value="${saleorder.isDelayInvoice}">
						</div>
					</li>

				</ul>
			</div>
		</div>

		<c:if test="${invoiceModifyflag ne 1}">
			<div id="productShow" show-flag="<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.arrivalStatus eq 2 || saleorder.orderStreamStatus eq 5}">1</c:if>"
				 class="parts" style="padding-top: 15px;<c:if test="${saleorder.deliveryStatus eq 2 || saleorder.arrivalStatus eq 2 || saleorder.orderStreamStatus eq 5}">display: none</c:if>">
				<div class="title-container title-container-blue">
					<div class="table-title nobor">产品信息</div>

						<span style="display: none;"> <!-- 弹框 -->
										<div class="title-click nobor  pop-new-data" id="saleGoodsDeliveryDirect"></div>
									</span>
					<%--<c:if test="${((saleorderGoods eq null or empty saleorderGoods) and saleorder.paymentStatus eq 2) or saleorder.paymentStatus eq 1}">
						<a class="title-click nobor" href="javascript:void(0);" onclick="updateSaleGoodsInit(${saleorder.saleorderId},1);">批量修改</a>
					</c:if>--%>
				</div>
				<table class="table  table-bordered table-striped table-condensed table-centered">
					<thead>
					<tr>

						<th style="width:80px">
							<c:if test="${((saleorderGoods eq null or empty saleorderGoods) and saleorder.paymentStatus eq 2) or saleorder.paymentStatus eq 1 or saleorder.paymentStatus eq 0}">
								<input type="checkbox" name="goodsCheckAllName" id="goodsCheckAllName" onclick="goodsCheckAllClick(this);" autocomplete="off"/>
							</c:if>
							序号
						</th>
						<th style="width:140px">产品名称</th>
						<th style="width:150px">发货方式
							<div class="customername pos_rel" style="display: inline-block">
								<i class="iconbluesigh ml4"></i>
								<div class="pos_abs customernameshow mouthControlPos" style="width: 230px;line-height: normal;color: black">
									仅限未采购商品可以修改普发/直发！<br>
								</div>
							</div>
						</th>
						<th>产品备注</th>
						<th>内部备注</th>
					</tr>
					</thead>
					<tbody id="modifyGoods">
					<c:set var="num" value="0"></c:set>
					<c:set var="count" value="0"></c:set>
					<c:set var="totleMoney" value="0.00"></c:set>
					<c:set var="id_str" value=""></c:set>
					<c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
						<c:if test="${list.isDelete eq 0}">
							<c:set var="count" value="${count+1}"></c:set>
							<c:set var="num" value="${num + list.num}"></c:set>
							<c:set var="totleMoney" value="${totleMoney + (list.price * list.num)}"></c:set>
							<c:set var="id_str" value="${id_str}_${list.saleorderGoodsId}"></c:set>
							<tr>
								<c:set var="lwNum" value="0"></c:set>
								<input type="hidden" name="orderGoodsId" value="${list.saleorderGoodsId}">
								<td style="overflow:hidden;padding-left:12px;">
									<c:if test="${((empty list.buyNum) or (list.buyNum eq 0)) and list.deliveryStatus eq 0}">
										<c:set var="lwNum" value="${lwNum + 1}"></c:set>
										<input type="checkbox" name="goodsCheckName" onclick="goodsCheckClick(this);" value="${list.saleorderGoodsId}" skuNo="${list.sku}" skuId="${list.goodsId}" skuName="${list.goodsName}" class="f_left" autocomplete="off"/>
									</c:if>
									<span class="f_left">${staut.count}</span>
								</td>
								<td class="text-left">
									<div class="customername pos_rel">
										<c:choose>
											<c:when test="${list.isDelete eq 1}">
												<span>${list.goodsName}<br/></span>
												<span>${newSkuInfosMap[list.sku].SKU_NO} <br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>
											</c:when>
											<c:otherwise>
												<c:if test="${list.isGift == 1}"><img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" /></c:if>
												<c:if test="${list.isDirectPurchase == 1}">
													<img id="isDirectPurchase" src="<%= basePath %>static/images/promote_icon.svg" style="width: 15px" title="商城&quot;现货现价&quot;促销"/>
												</c:if>
												<span class="font-blue" <c:if test="${list.isDirectPurchase == 1}">title="商城&quot;现货现价&quot;促销"</c:if>><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>${list.goodsName}</a>&nbsp;<i class="iconbluemouth contorlIcon"></i><br/></span>
												<span>${newSkuInfosMap[list.sku].SKU_NO} <br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>
												<c:set var="skuNo" value="${list.sku}"></c:set>
												<%@ include file="../../common/new_sku_common_tip.jsp" %>
											</c:otherwise>
										</c:choose>
									</div>
								</td>
								<td  buyNum="${list.buyNum}"  deliveryStatus="${list.deliveryStatus}">
									<c:choose>
										<c:when test="${((empty list.buyNum) or (list.buyNum eq 0))  }">
											<div class="f_left">
												<input type="radio" name="deliveryDirect_${list.saleorderGoodsId}"  id="deliveryDirect_${list.saleorderGoodsId}"
													   <c:if test="${list.deliveryDirect eq 0}">checked="checked"</c:if>
													   <c:if test="${list.isDirectPurchase eq 1}">style="cursor: not-allowed; opacity: 0.6;" disabled="disabled" title="商城'现货现价'促销，无法修改"</c:if> value="0"/>普发
												&nbsp;&nbsp;&nbsp;
												<input type="radio" name="deliveryDirect_${list.saleorderGoodsId}" id="deliveryDirect_${list.saleorderGoodsId}"
													   <c:if test="${list.deliveryDirect eq 1}">checked="checked"</c:if>
													   <c:if test="${list.isDirectPurchase eq 1}">style="cursor: not-allowed; opacity: 0.6;" disabled="disabled" title="商城'现货现价'促销，无法修改"</c:if> value="1"/>直发
											</div>
<%--											<select class="checkDeliveryDirect" style="width:50px;" name="deliveryDirect_${list.saleorderGoodsId}" id="deliveryDirect_${list.saleorderGoodsId}" autocomplete="off">--%>
<%--												<option <c:if test="${list.deliveryDirect eq 1}">selected="selected"</c:if> value="1">是</option>--%>
<%--												<option <c:if test="${list.deliveryDirect eq 0}">selected="selected"</c:if> value="0">否</option>--%>
<%--											</select>--%>
											<input style="margin-top:10px;" type="text" name="deliveryDirectComments_${list.saleorderGoodsId}"  id="deliveryDirectComments_${list.saleorderGoodsId}" value="${list.deliveryDirectComments}" autocomplete="off">
											<div id="commentsMsg_${list.saleorderGoodsId}"></div>
										</c:when>
										<c:otherwise>
											<div class="f_left">
												<input type="radio" name="deliveryDirect_${list.saleorderGoodsId}" disabled id="deliveryDirect_${list.saleorderGoodsId}" <c:if test="${list.deliveryDirect eq 0}">checked</c:if> value="0"/>普发
												&nbsp;&nbsp;&nbsp;
												<input type="radio" name="deliveryDirect_${list.saleorderGoodsId}" disabled id="deliveryDirect_${list.saleorderGoodsId}" <c:if test="${list.deliveryDirect eq 1}">checked</c:if> value="1"/>直发
											</div>
											<input style="margin-top:10px;" type="text"  name="deliveryDirectComments_${list.saleorderGoodsId}" disabled id="deliveryDirectComments_${list.saleorderGoodsId}" value="${list.deliveryDirectComments}" autocomplete="off">
											<div id="commentsMsg_${list.saleorderGoodsId}"></div>
										</c:otherwise>
									</c:choose>
									<input type="hidden" name="oldDeliveryDirect_${list.saleorderGoodsId}" value="${list.deliveryDirect}">
									<input type="hidden" name="oldDeliveryDirectComments_${list.saleorderGoodsId}" value="${list.deliveryDirectComments}">
								</td>

								<td>
                                    <c:choose>
                                        <c:when test="${saleorder.deliveryStatus eq 2}">
                                            <textarea rows="3" cols="100" name="goodsComments_${list.saleorderGoodsId}"  maxlength="60" disabled style="width:100%;height:100%">${list.goodsComments}</textarea>
                                        </c:when>
                                        <c:otherwise>
                                            <textarea rows="3" cols="100" name="goodsComments_${list.saleorderGoodsId}"  maxlength="60" style="width:100%;height:100%">${list.goodsComments}</textarea>

                                        </c:otherwise>
                                    </c:choose>
									<input type="hidden" name="oldGoodsComments_${list.saleorderGoodsId}" value="${list.goodsComments}">
								</td>
								<td class="c-comments">
                                    <c:choose>
                                        <c:when test="${((empty list.buyNum) or (list.buyNum eq 0)) and list.deliveryStatus eq 0}">
											<div class="customername pos_rel lm-main-item">
												<textarea class="input-largest textarea-smallest" placeholder="内部备注不对外显示，最多支持512个字符。" name="insideComments_${list.saleorderGoodsId}"
													   id="insideComments${list.saleorderGoodsId}" label_data="" scene="${scene}" style="width:100%;height:100%"
														  value="${list.insideComments}">${list.insideComments}</textarea>
												<input type="hidden" name="oldInsideComments_${list.saleorderGoodsId}" value="${list.insideComments}">

												<div class="pos_abs customernameshow" label_left="100" style="width: 500px; top: 25px;background-color: #00CD66;">
													${list.componentHtml}</div>
												<input type="hidden" name="label_data_${list.saleorderGoodsId}" value="" style="display:none;">
												<div id="insideComments_${list.saleorderGoodsId}"></div>
											</div>
                                        </c:when>
                                        <c:otherwise>

											<div class="customername pos_rel lm-main-item">
													${list.insideComments}
												<c:if test="${list.componentHtml ne ''}">
													<div class="pos_abs customernameshow" label_left="100" style="width: 500px; top: 25px;background-color: #00CD66;" >
															${list.componentHtml}
													</div>
												</c:if>
											</div>
											<input type="hidden" name="label_data_${list.saleorderGoodsId}" id="label_data_${list.saleorderGoodsId}" value="" style="display:none;">
                                        </c:otherwise>
                                    </c:choose>
								</td>
							</tr>
						</c:if>
					</c:forEach>
					</tbody>
				</table>
			</div>
		</c:if>
		<div class="add-tijiao" style="margin-bottom:50px;">
			<input type="hidden" name="saleorderId" id="saleorderId" value="${saleorder.saleorderId}">
			<input type="hidden" id="id_str" name="id_str" value="${id_str}">
			<input type="hidden" name="formToken" value="${formToken}"/>
			<button type="button" class="bt-bg-style bg-deep-green" onclick="submitCheck(${saleorder.orderType});">提交审核</button>
			<button class="dele" id="close-layer" type="button" onclick="closeGoBack();">取消</button>
			<a id="toCustomerInfo" class="addtitle" href="javascript:void(0);" tabtitle=""></a>
		</div>
	</form>
</div>
<%@ include file="../../common/footer.jsp"%>
