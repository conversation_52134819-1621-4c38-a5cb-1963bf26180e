package com.vedeng.erp.buyorderexpense.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.common.dto.StepsNodeDto;
import com.common.enums.StepsTypeEnum;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.MethodLockParam;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.BigScaleUtils;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.aftersale.domain.entity.ExpenseReturnEarlyWarnEntity;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto;
import com.vedeng.erp.aftersale.service.AfterSaleOrder2ExpenseApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesService;
import com.vedeng.erp.buyorder.domain.entity.Buyorder;
import com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import com.vedeng.erp.buyorder.mapper.BuyorderModifyApplyExpenseGoodsMapper;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.buyorderexpense.common.constant.BuyorderExpenseConstant;
import com.vedeng.erp.buyorderexpense.common.enums.AuditEnum;
import com.vedeng.erp.buyorderexpense.common.enums.BuyOrderExpenseBusinessTypeEnum;
import com.vedeng.erp.buyorderexpense.domain.entity.*;
import com.vedeng.erp.buyorderexpense.manager.esign.BuyOrderExpenseElectronicSignHandle;
import com.vedeng.erp.buyorderexpense.mapper.*;
import com.vedeng.erp.buyorderexpense.mapstruct.*;
import com.vedeng.erp.buyorderexpense.service.BuyorderExpenseService;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.finance.dto.InvoiceByGoodsDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderContactDto;
import com.vedeng.erp.trader.service.TraderSupplierApiService;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购费用单服务类
 * @date 2022/8/22 15:11
 **/
@Service
@Slf4j
public class BuyorderExpenseServiceImpl implements BuyorderExpenseService, BuyorderExpenseApiService {

    @Autowired
    private BuyorderExpenseMapper buyorderExpenseMapper;

    @Autowired
    private BuyorderExpenseDetailMapper buyorderExpenseDetailMapper;

    @Autowired
    private BuyorderExpenseItemMapper buyorderExpenseItemMapper;

    @Autowired
    private BuyorderExpenseItemDetailMapper buyorderExpenseItemDetailMapper;

    @Autowired
    private BuyorderExpenseConvertor buyorderExpenseConvertor;

    @Autowired
    private BuyorderExpenseDetailConvertor buyorderExpenseDetailConvertor;

    @Autowired
    private BuyorderExpenseItemConvertor buyorderExpenseItemConvertor;

    @Autowired
    private BuyorderExpenseItemDetailConvertor buyorderExpenseItemDetailConvertor;

    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;

    @Autowired
    private TraderSupplierApiService traderSupplierApiService;

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Autowired
    private ExpenseAfterSalesService expenseAfterSalesService;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private BuyOrderExpenseInvoiceConvertor buyOrderExpenseInvoiceConvertor;

    @Autowired
    private CoreSkuApiService coreSkuApiService;

    @Autowired
    private ExpenseAfterSalesApiService expenseAfterSalesApiService;


    @Autowired
    private CapitalBillApiService capitalBillApiService;

    @Autowired
    private BuyOrderExpenseElectronicSignHandle buyOrderExpenseElectronicSignHandle;

    @Autowired
    private BuyorderModifyApplyExpenseGoodsMapper buyorderModifyApplyExpenseGoodsMapper;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private RBuyorderExpenseJSaleorderMapper rBuyorderExpenseJSaleorderMapper;

    @Autowired
    private AfterSaleOrder2ExpenseApiService afterSaleOrder2ExpenseApiService;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private SaleOrderGoodsDetailConvertor saleOrderGoodsDetailConvertor;

    @Autowired
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;



    @Override
    public BuyorderExpenseDto viewDetail(Integer buyorderExpenseId) {
        if (Objects.isNull(buyorderExpenseId) || buyorderExpenseId.equals(0)) {
            return null;
        }
        BuyorderExpenseDto buyorderExpenseDto = getBuyorderExpenseDto(buyorderExpenseId);
        if (Objects.nonNull(buyorderExpenseDto) && Objects.nonNull(buyorderExpenseDto.getBuyorderId()) && buyorderExpenseDto.getBuyorderId() != 0) {

            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderExpenseDto.getBuyorderId());
            if (Objects.nonNull(buyorder)) {
                buyorderExpenseDto.setBuyorderPaymentStatus(buyorder.getPaymentStatus());
            }
        }
        // 付款计划
        bindPaymentPlan(buyorderExpenseDto);
        // 获取销售单和费用单关联信息
        this.getSaleorderBuyorderExpenseRelationDto(buyorderExpenseDto);
        return buyorderExpenseDto;
    }

    private void getSaleorderBuyorderExpenseRelationDto(BuyorderExpenseDto buyorderExpenseDto) {
        List<RBuyorderExpenseJSaleorderDto> relatedDetail = rBuyorderExpenseJSaleorderService.getRelatedDetail(buyorderExpenseDto.getBuyorderExpenseItemDtos());
        if (CollUtil.isEmpty(relatedDetail)) {
            return;
        }
        Set<Integer> saleOrderGoodsIdList = relatedDetail.stream().map(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId).collect(Collectors.toSet());
        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsApiService.getRelatedDetail(CollUtil.newArrayList(saleOrderGoodsIdList));
        if (CollUtil.isEmpty(saleOrderGoodsDetailDtos)) {
            log.error("获取采购费用单明细，未找到对应销售单数据{}", JSON.toJSONString(saleOrderGoodsIdList));
            return;
        }
        // 将销售单数据组装成关系数据
        List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = saleOrderGoodsDetailDtos.stream()
                .map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());
        if (CollUtil.isEmpty(buyOrderSaleOrderGoodsDetailDtoList)) {
            log.error("获取采购费用单明细，未找到对应销售单和费用单关系{}", JSON.toJSONString(saleOrderGoodsIdList));
            return;
        }
        this.getNeedBuyNum(buyOrderSaleOrderGoodsDetailDtoList);
        Map<Integer, Integer> numMap = relatedDetail.stream().collect(Collectors.toMap(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId, RBuyorderExpenseJSaleorderDto::getNum));
        Map<Integer, Integer> buyorderExpenseIdMap = relatedDetail.stream().collect(Collectors.toMap(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId, RBuyorderExpenseJSaleorderDto::getBuyorderExpenseId));
        Map<Integer, Integer> tRBuyorderExpenseJSaleorderIdMap = relatedDetail.stream().collect(Collectors.toMap(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId, RBuyorderExpenseJSaleorderDto::getTRBuyorderExpenseJSaleorderId));
        Map<Integer, Integer> buyorderExpenseItemIdMap = relatedDetail.stream().collect(Collectors.toMap(RBuyorderExpenseJSaleorderDto::getSaleorderGoodsId, RBuyorderExpenseJSaleorderDto::getBuyorderExpenseItemId));
        buyOrderSaleOrderGoodsDetailDtoList.forEach(d -> {
            d.setBuyNum(numMap.get(d.getSaleorderGoodsId()));
            d.setBuyorderExpenseJSaleorderId(tRBuyorderExpenseJSaleorderIdMap.get(d.getSaleorderGoodsId()));
            d.setBuyorderExpenseId(buyorderExpenseIdMap.get(d.getSaleorderGoodsId()));
            d.setBuyorderExpenseItemId(buyorderExpenseItemIdMap.get(d.getSaleorderGoodsId()));
        });
        buyorderExpenseDto.getBuyorderExpenseItemDtos().forEach(s -> {
            List<BuyOrderSaleOrderGoodsDetailDto> detailDtos = buyOrderSaleOrderGoodsDetailDtoList.stream()
                    .filter(b -> b.getBuyorderExpenseItemId().equals(s.getBuyorderExpenseItemId())).collect(Collectors.toList());
            s.setBuyOrderSaleOrderGoodsDetailDtos(detailDtos);
        });
    }

    /**
     * 组装 采购单+商品+详情+售后数量+收票数量数据
     *
     * @param buyorderExpenseId 采购费用单id
     * @return 所有信息
     */
    private BuyorderExpenseDto getBuyorderExpenseDto(Integer buyorderExpenseId) {

        if (Objects.isNull(buyorderExpenseId) || buyorderExpenseId.equals(0)) {
            return null;
        }
        List<BuyorderExpenseItemDto> buyorderExpenseGoodsDtos = returnNumAndInvoiceNum(buyorderExpenseId);
        BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
        // 防止其他地方漏调用
        bean.calcInvoiceStatus(buyorderExpenseGoodsDtos, buyorderExpenseId);
        bean.directPaymentStatus(buyorderExpenseId);
        bean.completeBuyOrderExpense(buyorderExpenseId);

        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyorderExpenseId);

        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseConvertor.toDto(buyorderExpenseEntity);
        BuyorderExpenseDetailDto buyorderExpenseDetailDto = buyorderExpenseDetailConvertor.toDto(buyorderExpenseDetailEntity);
        buyorderExpenseDto.setBuyorderExpenseDetailDto(buyorderExpenseDetailDto);
        BigScaleUtils.coverBigDecimalScale(buyorderExpenseDetailDto);

        if (buyorderExpenseEntity != null && Objects.nonNull(buyorderExpenseEntity.getBuyorderId()) && buyorderExpenseEntity.getBuyorderId() != 0) {
            BuyOrderDto buyOrderDto = buyorderExpenseMapper.selectBuyorderData(buyorderExpenseEntity.getBuyorderId());
            buyorderExpenseDto.setBuyOrderDto(buyOrderDto);
        }

        if (buyorderExpenseDetailEntity != null && Objects.nonNull(buyorderExpenseDetailEntity.getInvoiceType())) {
            SysOptionDefinitionDto optionDefinitionById = sysOptionDefinitionApiService.getOptionDefinitionById(buyorderExpenseDetailEntity.getInvoiceType());
            buyorderExpenseDetailDto.setInvoiceTypeStr(optionDefinitionById.getTitle());
        }


        if (buyorderExpenseDetailEntity != null) {
            OrganizationDto organizationById = organizationApiService.getOrganizationById(buyorderExpenseDetailEntity.getOrgId());
            buyorderExpenseDto.setOrgName(organizationById.getOrgName());
        }
        buyorderExpenseDto.setBuyorderExpenseItemDtos(buyorderExpenseGoodsDtos);

        // 获取销售单和费用单关联信息
        this.getSaleorderBuyorderExpenseRelationDto(buyorderExpenseDto);

        return buyorderExpenseDto;
    }

    private List<BuyorderExpenseItemDto> returnNumAndInvoiceNum(Integer buyorderExpenseId) {
        Map<Integer, Integer> integerIntegerMap = calcReturnNum(buyorderExpenseId);
        Map<Integer, List<InvoiceByGoodsDto>> integerListMap = calcInvoiceNum(buyorderExpenseId);
        List<BuyorderExpenseItemDto> buyorderExpenseGoodsDtos = buyorderExpenseItemMapper.selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(buyorderExpenseId, 0);
        buyorderExpenseGoodsDtos.forEach(c -> {

            Integer returnNum = integerIntegerMap.get(c.getBuyorderExpenseItemId());
            c.setReturnNum(returnNum == null ? 0 : returnNum);

            List<InvoiceByGoodsDto> invoiceByGoodsDtos = integerListMap.get(c.getBuyorderExpenseItemId());
            if (CollUtil.isNotEmpty(invoiceByGoodsDtos)) {
                BigDecimal invoiceNum = calcRealInvoiceNum(invoiceByGoodsDtos);
                c.setInvoicedNum(invoiceNum);
                c.setInvoiceByGoodsDtos(buyOrderExpenseInvoiceConvertor.toEntity(invoiceByGoodsDtos));
            } else {
                c.setInvoicedNum(BigDecimal.ZERO);
            }
        });
        return buyorderExpenseGoodsDtos;
    }

    /**
     * 蓝字有效-蓝字作废-红字冲销-红字有效
     *
     * @param invoiceByGoodsDtos 票号+商品 维度聚合
     * @return 实际收票数量
     */
    private BigDecimal calcRealInvoiceNum(List<InvoiceByGoodsDto> invoiceByGoodsDtos) {
        if (CollUtil.isEmpty(invoiceByGoodsDtos)) {
            return BigDecimal.ZERO;
        }
        List<InvoiceByGoodsDto> blueValid = invoiceByGoodsDtos.stream().filter(c -> Objects.equals(c.getColorType(), 2) && Objects.equals(c.getIsEnable(), 1)).collect(Collectors.toList());
        BigDecimal blueValidNum = blueValid.stream().map(InvoiceByGoodsDto::getNum).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (blueValidNum.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        List<InvoiceByGoodsDto> blueInvalid = invoiceByGoodsDtos.stream().filter(c -> Objects.equals(c.getColorType(), 2) && Objects.equals(c.getIsEnable(), 0)).collect(Collectors.toList());
        BigDecimal blueInvalidNum = blueInvalid.stream().map(InvoiceByGoodsDto::getNum).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 红票 冲销 + 红字 有效
        List<InvoiceByGoodsDto> redInvalid = invoiceByGoodsDtos.stream().filter(c -> Objects.equals(c.getColorType(), 1)).collect(Collectors.toList());
        BigDecimal redInvalidNum = redInvalid.stream().map(InvoiceByGoodsDto::getNum).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal result = blueValidNum.subtract(blueInvalidNum).subtract(redInvalidNum);
        if (result.compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        return result;

    }

    private Map<Integer, List<InvoiceByGoodsDto>> calcInvoiceNum(Integer buyorderExpenseId) {
        Assert.notNull(buyorderExpenseId, "查询采购费用单票时候relatedId 采购费用单号不可为空");

        InvoiceDto query = InvoiceDto.builder().type(4126).relatedId(buyorderExpenseId).validStatus(1).build();
        return invoiceApiService.getInvoiceListByRelatedIdGroupByInvoiceNoAndColorAndOrderGoodsItemId(query);
    }

    @Override
    public Map<Integer, Integer> calcReturnNum(Integer buyorderExpenseId) {

        List<ExpenseAfterSalesItemDto> afterSaleItems = expenseAfterSalesService.getExpenseAfterSalesReturnGoodsBybuyorderExpenseId(buyorderExpenseId);
        // 聚合 求和
        Map<Integer, Integer> afterSaleItems2Map = new HashMap<>(2);
        if (CollUtil.isNotEmpty(afterSaleItems)) {
            afterSaleItems2Map = afterSaleItems.stream().collect(
                    Collectors.toMap(
                            ExpenseAfterSalesItemDto::getBuyorderExpenseItemId,
                            c -> c.getReturnNum() == null ? 0 : c.getReturnNum(),
                            Integer::sum)
            );
        }
        return afterSaleItems2Map;

    }

    @Override
    public Map<Integer, Integer> calcReturnNumWithSalesOrder(Integer buyorderExpenseId, Integer saleOrderId) {
        return expenseAfterSalesService.getExpenseAfterSalesReturnGoodsBybuyorderExpenseIdAndSaleOrderId(buyorderExpenseId, saleOrderId);
    }

    /**
     * 付款计划数据封装
     *
     * @param buyorderExpenseDto 数据体
     */
    private void bindPaymentPlan(BuyorderExpenseDto buyorderExpenseDto) {
        List<PaymentPlanDto> result = new ArrayList<>();

        Integer paymentType = buyorderExpenseDto.getBuyorderExpenseDetailDto().getPaymentType();
        SysOptionDefinitionDto optionDefinitionById = sysOptionDefinitionApiService.getOptionDefinitionById(paymentType);

        PaymentPlanDto paymentPlanDto = new PaymentPlanDto();
        paymentPlanDto.setPlan(BuyorderExpenseConstant.PLAY_ONE);
        paymentPlanDto.setPlanContent(BuyorderExpenseConstant.PLAY_CONTENT_PREPAID);
        paymentPlanDto.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getPrepaidAmount());
        paymentPlanDto.setRemark(optionDefinitionById.getTitle());
        result.add(paymentPlanDto);

        if (!BuyorderExpenseConstant.PAYMENT_TYPE_419.equals(paymentType)
                && (!BuyorderExpenseConstant.PAYMENT_TYPE_424.equals(paymentType)
                || !BuyorderExpenseConstant.ORDER_TYPE_ZERO.equals(buyorderExpenseDto.getOrderType()))) {
            PaymentPlanDto data = new PaymentPlanDto();
            data.setPlan(BuyorderExpenseConstant.PLAY_TWO);
            data.setPlanContent(BuyorderExpenseConstant.PLAY_CONTENT_PERIOD);
            data.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
            data.setRemark("收货且收票后" + buyorderExpenseDto.getBuyorderExpenseDetailDto().getPeriodDay() + "天内支付");
            result.add(data);
        }

        if (BuyorderExpenseConstant.PAYMENT_TYPE_424.equals(paymentType)) {
            if (!BuyorderExpenseConstant.ORDER_TYPE_ZERO.equals(buyorderExpenseDto.getOrderType())) {
                PaymentPlanDto data = new PaymentPlanDto();
                data.setPlan(BuyorderExpenseConstant.PLAY_THREE);
                data.setPlanContent(BuyorderExpenseConstant.PLAY_CONTENT_FINAL);
                data.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getRetainageAmount());
                data.setRemark("到货后" + buyorderExpenseDto.getBuyorderExpenseDetailDto().getRetainageAmountMonth() + "个月内支付");
                result.add(data);
            }
        }

        PaymentPlanDto data = new PaymentPlanDto();
        data.setPlan("付款备注");
        data.setPlanContent(buyorderExpenseDto.getBuyorderExpenseDetailDto().getPaymentComments());
        result.add(data);
        buyorderExpenseDto.setPaymentPlans(result);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Integer add(BuyorderExpenseDto buyorderExpenseDto) {
        //保存采购费用单
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseConvertor.toEntity(buyorderExpenseDto);
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailConvertor.toEntity(buyorderExpenseDto.getBuyorderExpenseDetailDto());
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.BUY_ORDER_EXPENSE);
        String code = new BillNumGenerator().distribution(billGeneratorBean);
        buyorderExpenseEntity.setBuyorderExpenseNo(code);
        buyorderExpenseDto.setBuyorderExpenseNo(code);
        buyorderExpenseDetailEntity.setOrgId(Objects.isNull(buyorderExpenseDetailEntity.getOrgId()) ? CurrentUser.getCurrentUser().getOrgId() : buyorderExpenseDetailEntity.getOrgId());
        buyorderExpenseDetailEntity.setCreator(Objects.isNull(buyorderExpenseDetailEntity.getCreator()) ? CurrentUser.getCurrentUser().getId() : buyorderExpenseDetailEntity.getCreator());
        buyorderExpenseMapper.insertSelective(buyorderExpenseEntity);
        buyorderExpenseDetailEntity.setBuyorderExpenseId(buyorderExpenseEntity.getBuyorderExpenseId());
        buyorderExpenseDetailMapper.insertSelective(buyorderExpenseDetailEntity);
        //保存采购费用单商品
        if (CollUtil.isNotEmpty(buyorderExpenseDto.getBuyorderExpenseItemDtos())) {
            List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = buyorderExpenseDto.getBuyorderExpenseItemDtos();
            buyorderExpenseItemDtos.forEach(c -> {

                BuyorderExpenseItemEntity buyorderExpenseItemEntity = buyorderExpenseItemConvertor.toEntity(c);
                buyorderExpenseItemEntity.setBuyorderExpenseId(buyorderExpenseEntity.getBuyorderExpenseId());
                c.setBuyorderExpenseId(buyorderExpenseEntity.getBuyorderExpenseId());
                BuyorderExpenseItemDetailEntity buyorderExpenseItemDetailEntity = buyorderExpenseItemDetailConvertor.toEntity(c.getBuyorderExpenseItemDetailDto());

                // 绑定sku 相关信息 品牌 规格 单位
                bindCoreSkuInfo(c.getGoodsId(), buyorderExpenseItemDetailEntity);
                buyorderExpenseItemMapper.insertSelective(buyorderExpenseItemEntity);
                c.setBuyorderExpenseItemId(buyorderExpenseItemEntity.getBuyorderExpenseItemId());
                buyorderExpenseItemDetailEntity.setBuyorderExpenseItemId(buyorderExpenseItemEntity.getBuyorderExpenseItemId());
                buyorderExpenseItemDetailMapper.insertSelective(buyorderExpenseItemDetailEntity);
                c.getBuyorderExpenseItemDetailDto().setBuyorderExpenseItemDetailId(buyorderExpenseItemDetailEntity.getBuyorderExpenseItemDetailId());
            });
        }
        buyorderExpenseDto.setBuyorderExpenseId(buyorderExpenseEntity.getBuyorderExpenseId());
        log.info("保存采购费用单成功：{}", JSON.toJSONString(buyorderExpenseDto));
        //建立销售单和采购费用单关联关系 非自动创建的
        if (Objects.isNull(buyorderExpenseDto.getIsAuto()) || Objects.equals(buyorderExpenseDto.getIsAuto(), 0)) {
            rBuyorderExpenseJSaleorderService.addExpenseJ(buyorderExpenseDto);
        }
        return buyorderExpenseEntity.getBuyorderExpenseId();
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addBuyOrderAfterSalesExpense(BuyOrderAfterSalesHandlingFeeExpenseDto data) {
        log.info("采购售后单：{}产生的手续费：{}", JSON.toJSONString(data.getAfterSalesNo()), JSON.toJSONString(data));
        validHandlingFeeData(data);

        // 保存售后生成的费用单
        BuyorderExpenseDto buyorderExpenseDto = bulidBuyOrderAfterSaleExpense(data);
        log.info("构建的采购费用售后：{}的费用单：{}", JSON.toJSONString(data.getAfterSalesNo()), JSON.toJSONString(buyorderExpenseDto));
        Integer id = add(buyorderExpenseDto);
        buyorderExpenseDto.setBuyorderExpenseId(id);
        // 去生成流水 订单付款 转出 对公 余额支付
        saveHandlingFeeCapitalBill(data, id);
    }

    private void saveHandlingFeeCapitalBill(BuyOrderAfterSalesHandlingFeeExpenseDto data, Integer buyOrderExpenseId) {
        log.info("开始采购费用售后:{}保存交易流水BuyOrderAfterSalesHandlingFeeExpenseDto:{},buyOrderExpenseId:{}", JSON.toJSONString(data.getAfterSalesNo()), JSONObject.toJSONString(data), JSON.toJSONString(buyOrderExpenseId));
        BuyorderExpenseDto orderMainData = getOrderMainData(buyOrderExpenseId);
        CapitalBillDto cb = new CapitalBillDto();
        cb.setAmount(orderMainData.getBuyorderExpenseDetailDto().getTotalAmount());
        // 余额支付
        cb.setTraderMode(528);
        //转出
        cb.setTraderType(5);
        cb.setPayee(orderMainData.getBuyorderExpenseDetailDto().getTraderName());
        cb.setPayer(BuyorderExpenseConstant.VEDENG);
        CapitalBillDetailDto cbd = new CapitalBillDetailDto();
        //订单支付
        cbd.setBussinessType(525);
        cbd.setUserId(CurrentUser.getCurrentUser().getId());
        cbd.setOrgId(CurrentUser.getCurrentUser().getOrgId());
        cbd.setOrgName(CurrentUser.getCurrentUser().getOrgName());

        SpringUtil.getBean(BuyorderExpenseServiceImpl.class).saveCapitalBill(orderMainData, cb, cbd, 2);

    }

    /**
     * 保存交易流水
     *
     * @param data
     * @param cb
     * @param cbd
     * @param type
     */
    @Transactional(rollbackFor = Throwable.class)
    public void saveCapitalBill(BuyorderExpenseDto data, CapitalBillDto cb, CapitalBillDetailDto cbd, Integer type) {
        log.info("开始保存交易流水【BuyorderExpenseDto】:{}", JSONObject.toJSONString(data));
        CapitalBillDto capitalBill = new CapitalBillDto();
        capitalBill.setCreator(BuyorderExpenseConstant.NJADMIN_ID);
        capitalBill.setAddTime(DateUtil.sysTimeMillis());
        capitalBill.setCurrencyUnitId(1);
        capitalBill.setTraderTime(DateUtil.sysTimeMillis());
        capitalBill.setTraderType(cb.getTraderType());
        capitalBill.setTraderSubject(1);
        capitalBill.setTraderMode(cb.getTraderMode());
        capitalBill.setAmount(cb.getAmount());
        capitalBill.setPayee(cb.getPayee());//收款方
        capitalBill.setPayer(cb.getPayer());//付款方
        capitalBill.setCompanyId(BuyorderExpenseConstant.VEDENG_ID);
        capitalBillApiService.insertCapitalBill(capitalBill);
        Integer capitalBillId = capitalBill.getCapitalBillId();
        CapitalBillDto capitalBillExtra = new CapitalBillDto();
        capitalBillExtra.setCapitalBillId(capitalBillId);
        capitalBillExtra.setCapitalBillNo(generatorNum(capitalBillId));
        capitalBillApiService.updateCapitalBillNo(capitalBillExtra);


        List<CapitalBillDetailDto> capitalBillDetailDtoList = new ArrayList<>();
        CapitalBillDetailDto capitalBillDetailDto = new CapitalBillDetailDto();
        capitalBillDetailDto.setAmount(capitalBill.getAmount());
        capitalBillDetailDto.setBussinessType(cbd.getBussinessType());//订单支付
        capitalBillDetailDto.setOrderType(4);//费用
        capitalBillDetailDto.setRelatedId(data.getBuyorderExpenseId());
        capitalBillDetailDto.setOrderNo(data.getBuyorderExpenseNo());
        capitalBillDetailDto.setUserId(cbd.getUserId());
        capitalBillDetailDto.setOrgId(cbd.getOrgId());
        capitalBillDetailDto.setOrgName(cbd.getOrgName());
        capitalBillDetailDto.setTraderId(data.getBuyorderExpenseDetailDto().getTraderId());//交易者id
        capitalBillDetailDto.setTraderType(type);//客户/供应商
        capitalBillDetailDto.setCapitalBillId(capitalBillId);
        capitalBillDetailDtoList.add(capitalBillDetailDto);
        capitalBillApiService.insertCapitalBillDetail(capitalBillDetailDtoList);
    }

    /**
     * 生成资金流水号
     *
     * @param capitalBillId
     * @return
     */
    private String generatorNum(Integer capitalBillId) {
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.CAPITAL_NO, NoGeneratorBean.builder().dateFormat(ErpConstant.YYYYMMDDHHMMSS).id(capitalBillId).numberOfDigits(9).build());
        return new BillNumGenerator().distribution(billGeneratorBean);
    }


    /**
     * 数据校验
     *
     * @param data
     */
    private void validHandlingFeeData(BuyOrderAfterSalesHandlingFeeExpenseDto data) {
        Assert.notNull(data, "入参对象不可为空");
        Assert.notNull(data.getAfterSalesId(), "入参对象售后单id不可为空");
        Assert.notNull(data.getAfterSalesNo(), "入参对象售后单no不可为空");
        Assert.notNull(data.getBuyorderNo(), "入参对象采购单no不可为空");
        Assert.notNull(data.getBuyorderId(), "入参对象采购单id不可为空");
        Assert.notNull(data.getPrice(), "入参对象单价不可为空");
        Assert.notNull(data.getNum(), "入参对象数量不可为空");
        Assert.notNull(data.getGoodsId(), "入参对象商品id不可为空");
        Assert.notNull(data.getSku(), "入参对象sku不可为空");
        Assert.notNull(data.getInvoiceType(), "入参对象票种不可为空");
    }

    private BuyorderExpenseDto bulidBuyOrderAfterSaleExpense(BuyOrderAfterSalesHandlingFeeExpenseDto data) {
        // 查询必要信息
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(data.getBuyorderId());
        if (Objects.isNull(buyorder)) {
            throw new ServiceException("未查到次采购" + data.getBuyorderNo() + "单信息");
        }
        Date now = new Date();
        BigDecimal totalAmount = data.getPrice().multiply(BigDecimal.valueOf(data.getNum()));
        // 费用单订单详情
        BuyorderExpenseDetailDto buyorderExpenseDetailDto = BuyorderExpenseDetailDto.builder()
                .paymentType(419)
                .totalAmount(totalAmount)
                .prepaidAmount(totalAmount)
                // 供应商信息
                .traderId(buyorder.getTraderId())
                .traderArea(buyorder.getTraderArea())
                .traderName(buyorder.getTraderName())
                .traderAddress(buyorder.getTraderAddress())
                .traderComments(buyorder.getTraderComments())
                .traderContactId(buyorder.getTraderContactId())
                .traderContactMobile(buyorder.getTraderContactMobile())
                .traderContactName(buyorder.getTraderContactName())
                .traderContactTelephone(buyorder.getTraderContactTelephone())
                .traderAddressId(buyorder.getTraderAddressId())
                .invoiceType(data.getInvoiceType())
                .build();
        CoreSkuDto coreSkuDtoBySkuId = coreSkuApiService.getCoreSkuDtoBySkuId(data.getGoodsId());
        if (Objects.isNull(coreSkuDtoBySkuId)) {
            throw new ServiceException("所选择的是续费商品不存在");
        }
        // 商品信息
        BuyorderExpenseItemDetailDto buyorderExpenseItemDetailDto = BuyorderExpenseItemDetailDto.builder()
                .sku(coreSkuDtoBySkuId.getSku())
                .goodsName(coreSkuDtoBySkuId.getSkuName())
                .brandName(coreSkuDtoBySkuId.getBrandName())
                .unitName(coreSkuDtoBySkuId.getUnitName())
                .price(data.getPrice())
                .build();
        BuyorderExpenseItemDto buyorderExpenseItemDto = BuyorderExpenseItemDto.builder()
                .arrivalStatus(BuyorderExpenseConstant.ARRIVAL_STATUS_ALL)
                .arrivalTime(now)// 默认送达 收到
                .deliveryStatus(BuyorderExpenseConstant.DELIVERY_STATUS_ALL)
                .deliveryTime(now)
                .goodsId(data.getGoodsId())
                .num(data.getNum())
                .buyorderExpenseItemDetailDto(buyorderExpenseItemDetailDto)
                .build();
        return BuyorderExpenseDto.builder()
                // 手续费的费用类型 售后的
                .businessType(BuyOrderExpenseBusinessTypeEnum.AFTERSALE.getCode())
                .buyorderNo(data.getBuyorderNo())
                .buyorderId(data.getBuyorderId())
                .afterSalesId(data.getAfterSalesId())
                .afterSalesNo(data.getAfterSalesNo())
                // 送到 到达 审核通过 状态进行中
                .arrivalStatus(BuyorderExpenseConstant.ARRIVAL_STATUS_ALL)
                .deliveryStatus(BuyorderExpenseConstant.DELIVERY_STATUS_ALL)
                .auditStatus(BuyorderExpenseConstant.AUDIT_STATUS_SUCCESS)
                .paymentStatus(BuyorderExpenseConstant.PAYMENT_STATUS_ALL)
                .auditTime(now)
                .validTime(now)
                .validStatus(BuyorderExpenseConstant.VALID_STATUS)
                .status(BuyorderExpenseConstant.STATUS_PROCESS)
                .buyorderExpenseDetailDto(buyorderExpenseDetailDto)
                .buyorderExpenseItemDtos(CollUtil.toList(buyorderExpenseItemDto))
                .build();
    }

    /**
     * 绑定sku 相关信息 品牌 规格 单位
     *
     * @param skuId                           sku id
     * @param buyorderExpenseItemDetailEntity 组装体
     */
    private void bindCoreSkuInfo(Integer skuId, BuyorderExpenseItemDetailEntity buyorderExpenseItemDetailEntity) {
        CoreSkuDto coreSkuDtoBySkuId = coreSkuApiService.getCoreSkuDtoBySkuId(skuId);
        if (Objects.nonNull(coreSkuDtoBySkuId)) {
            buyorderExpenseItemDetailEntity.setBrandName(coreSkuDtoBySkuId.getBrandName());
            buyorderExpenseItemDetailEntity.setUnitName(coreSkuDtoBySkuId.getUnitName());
            buyorderExpenseItemDetailEntity.setExpenseCategoryId(coreSkuDtoBySkuId.getCostCategoryId());
            buyorderExpenseItemDetailEntity.setExpenseCategoryName(coreSkuDtoBySkuId.getCategoryName());
            String model = "";
            if (Objects.nonNull(coreSkuDtoBySkuId.getSpuType())) {
                if (coreSkuDtoBySkuId.getSpuType().equals(316) || coreSkuDtoBySkuId.getSpuType().equals(1008)) {
                    model = coreSkuDtoBySkuId.getModel();
                } else {
                    model = coreSkuDtoBySkuId.getSpec();
                }
            }
            buyorderExpenseItemDetailEntity.setModel(model);
        }
    }

    @Override
    public BuyorderExpenseDto getBuyorderExpenseEditInfo(Integer buyorderExpenseId) {
        if (Objects.isNull(buyorderExpenseId) || buyorderExpenseId.equals(0)) {
            return null;
        }
        return getBuyorderExpenseDto(buyorderExpenseId);
    }


    @Override
    @Transactional
    public void update(BuyorderExpenseDto buyorderExpenseDto) {
        //更新采购费用单
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseConvertor.toEntity(buyorderExpenseDto);
        buyorderExpenseMapper.updateByPrimaryKeySelective(buyorderExpenseEntity);

        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailConvertor.toEntity(buyorderExpenseDto.getBuyorderExpenseDetailDto());
        if (Objects.nonNull(buyorderExpenseDetailEntity)) {
            // 编辑 采购单时触发修改直属费用单，取不到expenseDetail信息，所以这边只能查一下
            if (buyorderExpenseDetailEntity.getBuyorderExpenseDetailId() == null) {
                buyorderExpenseDetailEntity.setBuyorderExpenseDetailId(buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyorderExpenseDto.getBuyorderExpenseId()).getBuyorderExpenseDetailId());
            }
            buyorderExpenseDetailMapper.updateByPrimaryKeySelective(buyorderExpenseDetailEntity);
        }
        //更新采购费用单商品
        if (CollUtil.isNotEmpty(buyorderExpenseDto.getBuyorderExpenseItemDtos())) {
            //新商品list
            List<BuyorderExpenseItemDto> newGoodsList = buyorderExpenseDto.getBuyorderExpenseItemDtos();
            //旧商品list
            List<BuyorderExpenseItemDto> oldGoodsList = buyorderExpenseItemMapper.selectByBuyorderExpenseIdAndIsDelete(buyorderExpenseDto.getBuyorderExpenseId(), 0);
            List<Integer> oldItemsIds = oldGoodsList.stream().map(BuyorderExpenseItemDto::getBuyorderExpenseItemId).collect(Collectors.toList());
            for (BuyorderExpenseItemDto item : newGoodsList) {
                //如果有ID更新
                if (Objects.nonNull(item.getBuyorderExpenseItemId())) {
                    buyorderExpenseItemMapper.updateByPrimaryKeySelective(buyorderExpenseItemConvertor.toEntity(item));
                    buyorderExpenseItemDetailMapper.updateByPrimaryKeySelective(buyorderExpenseItemDetailConvertor.toEntity(item.getBuyorderExpenseItemDetailDto()));
                }
                //ID为空
                if (Objects.isNull(item.getBuyorderExpenseItemId())) {
                    item.setBuyorderExpenseId(buyorderExpenseEntity.getBuyorderExpenseId());
                    addBuyorderExpenseItem(item);
                }
            }
            List<BuyorderExpenseItemDto> updateBuyorderExpenseItemDtoList = newGoodsList.stream().filter(dto -> Objects.nonNull(dto.getBuyorderExpenseItemId()))
                    .collect(Collectors.toList());
            // 更新虚拟商品销售采购关联单信息
            rBuyorderExpenseJSaleorderService.updateBuyNum(updateBuyorderExpenseItemDtoList);

            // 删除
            List<Integer> newGoodsIds = newGoodsList.stream().filter(c -> Objects.nonNull(c.getBuyorderExpenseItemId())).map(BuyorderExpenseItemDto::getBuyorderExpenseItemId).collect(Collectors.toList());
            List<Integer> needDeleteIds = oldItemsIds.stream().filter(a -> !newGoodsIds.contains(a)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(needDeleteIds)) {
                needDeleteIds.forEach(this::logicDelete);
            }
        }
    }

    /**
     * 添加 2表一起
     *
     * @param buyorderExpenseItemDto 商品数据
     */
    @Transactional(rollbackFor = Throwable.class)
    public void addBuyorderExpenseItem(BuyorderExpenseItemDto buyorderExpenseItemDto) {
        BuyorderExpenseItemEntity buyorderExpenseItemEntity = buyorderExpenseItemConvertor.toEntity(buyorderExpenseItemDto);
        buyorderExpenseItemMapper.insertSelective(buyorderExpenseItemEntity);
        BuyorderExpenseItemDetailEntity buyorderExpenseItemDetailEntity = buyorderExpenseItemDetailConvertor.toEntity(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto());
        buyorderExpenseItemDetailEntity.setBuyorderExpenseItemId(buyorderExpenseItemEntity.getBuyorderExpenseItemId());
        bindCoreSkuInfo(buyorderExpenseItemDto.getGoodsId(), buyorderExpenseItemDetailEntity);
        buyorderExpenseItemDetailMapper.insertSelective(buyorderExpenseItemDetailEntity);
    }

    /**
     * 删除 2 表一起
     *
     * @param buyorderExpenseItemId 商品主表id
     */
    @Transactional(rollbackFor = Throwable.class)
    public void logicDelete(Integer buyorderExpenseItemId) {
        BuyorderExpenseItemEntity buyorderExpenseItemEntity = new BuyorderExpenseItemEntity();
        buyorderExpenseItemEntity.setIsDelete(1);
        buyorderExpenseItemEntity.setBuyorderExpenseItemId(buyorderExpenseItemId);
        buyorderExpenseItemMapper.updateByPrimaryKeySelective(buyorderExpenseItemEntity);
        BuyorderExpenseItemDetailEntity buyorderExpenseItemDetailEntity = new BuyorderExpenseItemDetailEntity();
        buyorderExpenseItemDetailEntity.setIsDelete(1);
        buyorderExpenseItemDetailEntity.setBuyorderExpenseItemId(buyorderExpenseItemId);
        buyorderExpenseItemDetailMapper.updateByBuyorderExpenseItemIdSelective(buyorderExpenseItemDetailEntity);
    }

    @Override
    public BuyorderExpenseDto printDetail(Integer buyorderExpenseId) {
        BuyorderExpenseDto buyorderExpenseDto = getBuyorderExpenseDto(buyorderExpenseId);
        List<TraderContactDto> traderContactDtoList = traderSupplierApiService.getSupplierContact(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
        Optional<TraderContactDto> first = traderContactDtoList.stream().filter(e -> e.getTraderContactId().equals(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderContactId())).findFirst();
        TraderContactDto traderContactDto = new TraderContactDto();
        if (first.isPresent()) {
            traderContactDto = first.get();
        }

        buyorderExpenseDto.setBank(traderContactDto.getBank());
        buyorderExpenseDto.setBankAccount(traderContactDto.getBankAccount());
        buyorderExpenseDto.setTaxNum(traderContactDto.getTaxNum());
        return buyorderExpenseDto;
    }


    @Override
    public BuyorderExpenseDto queryAttributiveExpenseInfoByBuyorderId(Integer buyorderId) {
        return buyorderExpenseMapper.queryExpenseInfoByType(buyorderId, ErpConst.ZERO);
    }


    @Override
    public int doAudit(Integer buyorderExpenseId) {
        log.info("doAudit 采购费用单发起审核:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "采购费用单id不可为空");

        BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
        buyorderExpenseDto.setBuyorderExpenseId(buyorderExpenseId);
        buyorderExpenseDto.setAuditStatus(AuditEnum.PROCESS.getAuditStatus());
        buyorderExpenseDto.setAuditTime(new Date());

        return buyorderExpenseMapper.updateByPrimaryKeySelective(buyorderExpenseConvertor.toEntity(buyorderExpenseDto));
    }

    @Override
    public int audit(Integer buyorderExpenseId) {
        log.info("audit 采购费用单审核通过:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "采购费用单id不可为空");

        BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
        buyorderExpenseDto.setBuyorderExpenseId(buyorderExpenseId);
        buyorderExpenseDto.setAuditStatus(AuditEnum.SUCCESS.getAuditStatus());
        Date date = new Date();
        buyorderExpenseDto.setAuditTime(date);
        buyorderExpenseDto.setValidTime(date);
        buyorderExpenseDto.setValidStatus(BuyorderExpenseConstant.VALID_STATUS);
        return buyorderExpenseMapper.updateByPrimaryKeySelective(buyorderExpenseConvertor.toEntity(buyorderExpenseDto));
    }

    @Override
    public int unAudit(Integer buyorderExpenseId) {
        log.info("unAudit 采购费用单审核不通过:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "采购费用单id不可为空");
        BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
        buyorderExpenseDto.setBuyorderExpenseId(buyorderExpenseId);
        buyorderExpenseDto.setAuditStatus(AuditEnum.ERROR.getAuditStatus());
        buyorderExpenseDto.setAuditTime(new Date());

        return buyorderExpenseMapper.updateByPrimaryKeySelective(buyorderExpenseConvertor.toEntity(buyorderExpenseDto));
    }

    @Override
    public void updateStatusByBuyorderId(Integer buyorderId, Integer status) {
        buyorderExpenseMapper.updateStatusByBuyorderId(buyorderId, status);
    }

    @Override
    public void updateStatusByBuyorderIdWithTicket(Integer buyOrderExpenseId, Integer status) {
        buyorderExpenseMapper.updateStatusByBuyorderIdWithTicket(buyOrderExpenseId, status);
    }

    @Override
    public void updateBuyorderExpenseStatusByVerity(BuyorderExpenseDto update) {

        if (update == null || update.getBuyorderExpenseId() == null) {
            log.error("更新采购费用单数据缺少信息：{}", JSON.toJSONString(update));
            throw new ServiceException("更新采购费用单数据缺少信息");
        }
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseConvertor.toEntity(update);
        buyorderExpenseMapper.updateByPrimaryKeySelective(buyorderExpenseEntity);
    }

    @Override
    public BuyorderExpenseDto getOrderMainData(Integer buyorderExpenseId) {

        if (Objects.isNull(buyorderExpenseId)) {
            throw new ServiceException("查询采购费用单数据缺少信息");
        }
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseConvertor.toDto(buyorderExpenseEntity);
        BuyorderExpenseDetailEntity buyorderExpenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderExpenseId(buyorderExpenseEntity.getBuyorderExpenseId());
        BuyorderExpenseDetailDto buyorderExpenseDetailDto = buyorderExpenseDetailConvertor.toDto(buyorderExpenseDetailEntity);
        buyorderExpenseDto.setBuyorderExpenseDetailDto(buyorderExpenseDetailDto);
        List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = buyorderExpenseItemMapper.selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(buyorderExpenseId, 0);

        if (CollectionUtils.isNotEmpty(buyorderExpenseItemDtos)) {
            buyorderExpenseDto.setBuyorderExpenseItemDtos(buyorderExpenseItemDtos);
        }
        return buyorderExpenseDto;
    }

    @Override
    public List<String> getProductManageAndAsistNameList(Integer buyorderExpenseId) {
        List<BuyorderExpenseItemDto> data = buyorderExpenseItemMapper.selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(buyorderExpenseId, 0);
        return data.stream().filter(c -> Objects.nonNull(c.getBuyorderExpenseItemDetailDto()) && StrUtil.isNotEmpty(c.getBuyorderExpenseItemDetailDto().getSku())).map(c -> c.getBuyorderExpenseItemDetailDto().getSku()).collect(Collectors.toList());
    }


    @Override
    public Integer checkUser(Integer buyorderExpenseId) {

        if (Objects.isNull(buyorderExpenseId)) {
            throw new ServiceException("采购费用单id为空");
        }

        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);

        if (buyorderExpenseEntity == null) {
            throw new ServiceException("采购费用单：" + buyorderExpenseId + "未查到");
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        if (currentUser.getId().equals(buyorderExpenseEntity.getCreator())) {
            return 1;
        }
        UserDto userById = userApiService.getUserById(buyorderExpenseEntity.getCreator());
        if (userById != null && currentUser.getId().equals(userById.getParentId())) {
            return 2;
        }
        return 0;
    }

    @Override
    public List<BuyorderExpenseItemDto> getByBuyOrderId(Integer buyOrderId) {
        List<BuyorderExpenseItemDto> expenseItemDtos = buyorderExpenseItemMapper.getByBuyOrderId(buyOrderId);
        expenseItemDtos.forEach(item -> {
            if ("0E-8".equals(item.getBuyorderExpenseItemDetailDto().getPrice().toString())) {
                item.getBuyorderExpenseItemDetailDto().setPrice(new BigDecimal("0.00"));
            }
        });
        return expenseItemDtos;
    }


    @Override
    public List<BuyorderExpenseDto> getBuyOrderExpenseDtoListByBuyOrderId(Integer buyOrderId) {
        List<BuyorderExpenseDto> expenseList = buyorderExpenseMapper.getBuyOrderExpenseListByBuyOrderId(buyOrderId);
        expenseList.forEach(item -> {
            // 计算收票金额
            item.setInvoiceAmount(invoiceApiService.getInvoiceTotalAmountByRelatedId(item.getBuyorderExpenseId(), 4126));
        });
        return expenseList;
    }

    @Override
    public List<BuyorderExpenseItemDto> getExpenseGoodsList4InvoiceSave(InvoiceSaveSearchDto searchDto) {
        if (searchDto == null) {
            return null;
        }
        return buyorderExpenseMapper.getExpenseGoodsList4InvoiceSave(searchDto);
    }

    @Override
    public List<BuyorderExpenseDto> getOrderExpenseListByOrderIds(List<Integer> orderIds) {
        if (CollectionUtils.isEmpty(orderIds)) {
            return null;
        }
        return buyorderExpenseMapper.getOrderExpenseListByOrderIds(orderIds);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int closeBuyOrderExpense(Integer buyorderExpenseId) {
        log.info("closeBuyOrderExpense 采购费用关闭:{}", JSON.toJSONString(buyorderExpenseId));
        if (Objects.isNull(buyorderExpenseId)) {
            throw new ServiceException("采购费用单id为空");
        }
        BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
        bean.freeReturnAlert(buyorderExpenseId);
        BuyorderExpenseEntity update = new BuyorderExpenseEntity();
        update.setBuyorderExpenseId(buyorderExpenseId);
        update.setStatus(3);
        int i = buyorderExpenseMapper.updateByPrimaryKeySelective(update);
        rBuyorderExpenseJSaleorderMapper.deleteByBuyorderExpenseId(buyorderExpenseId);
        return i;
    }

    /**
     * 解除预警
     * @param buyorderExpenseId 费用单id
     */
    @Transactional(rollbackFor = Throwable.class)
    public void freeReturnAlert(Integer buyorderExpenseId) {
        //解除预警
        Integer returnEarlyWarn = getReturnEarlyWarnByBuyorderExpenseId(buyorderExpenseId);
        if (ErpConstant.ONE.equals(returnEarlyWarn)) {
            log.info("采购费用单关闭,退货预警解除开始，采购费用单id：{}", buyorderExpenseId);
            freeReturnAlertBuyOrderExpenseGoods(buyorderExpenseId);
        }
    }

    @Override
    public void signature(Integer buyorderExpenseId) {

        BusinessInfo businessInfo = new BusinessInfo();
        // 操作人
        businessInfo.setOperator(CurrentUser.getCurrentUser().getUsername());
        ElectronicSignParam electronicSignParam = ElectronicSignParam
                .builder()
                .buyOrderExpenseId(buyorderExpenseId)
                .flowType(2)
                .businessInfo(businessInfo)
                .electronicSignBusinessEnums(ElectronicSignBusinessEnums.BUY_ORDER_EXPENSE)
                .build();
        buyOrderExpenseElectronicSignHandle.electronicSign(electronicSignParam);
    }

    @Override
    public List<StepsNodeDto> getOrderStepsNode(Integer buyorderExpenseId) {
        BuyorderExpenseDto data = this.getOrderMainData(buyorderExpenseId);
        List<StepsNodeDto> stepsNodes = new ArrayList<>();

        StepsNodeDto valid = StepsNodeDto.builder().title("待确认").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto verify = StepsNodeDto.builder().title("待审核").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto payment = StepsNodeDto.builder().title("待付款").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto invoice = StepsNodeDto.builder().title("待收票").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto end = StepsNodeDto.builder().title("已完结").type(StepsTypeEnum.wait.getType()).build();
        StepsNodeDto close = StepsNodeDto.builder().title("已关闭").type(StepsTypeEnum.error.getType()).build();
        // 节点对应规则: 1待确认、2审核中、3待付款、4待收票、5已完结、6已关闭
        if (data.getStatus() == 0) {
            if (data.getAuditStatus() != null && data.getAuditStatus() == 1) {// 审核中
                stepsStatus(stepsNodes, valid, "已确认", StepsTypeEnum.success);
                stepsStatus(stepsNodes, verify, "审核中", StepsTypeEnum.process);
                stepsNodes.add(payment);
                stepsNodes.add(invoice);
                stepsNodes.add(end);
                data.setStepsNodes(stepsNodes);
            }
            if (data.getAuditStatus() != null && data.getAuditStatus() == 0) {
                stepsNodes.add(valid);
                stepsNodes.add(verify);
                stepsNodes.add(payment);
                stepsNodes.add(invoice);
                stepsNodes.add(end);
                data.setStepsNodes(stepsNodes);
            }

            if (data.getAuditStatus() != null && data.getAuditStatus() == 3) {
                stepsStatus(stepsNodes, valid, "已确认", StepsTypeEnum.success);
                stepsStatus(stepsNodes, verify, "审核不通过", StepsTypeEnum.error);
                stepsNodes.add(payment);
                stepsNodes.add(invoice);
                stepsNodes.add(end);
                data.setStepsNodes(stepsNodes);
            }
        }
        if (data.getStatus() == 1) {// 订单进行中
            stepsStatus(stepsNodes, valid, "已确认", StepsTypeEnum.success);
            stepsStatus(stepsNodes, verify, "审核通过", StepsTypeEnum.success);

            // 付款状态
            bindPaymentAndInvoiceStepsStatus(data, stepsNodes, payment, invoice);
            stepsNodes.add(end);

        }
        if (data.getStatus() == 2) {

            stepsStatus(stepsNodes, valid, "已确认", StepsTypeEnum.success);
            stepsStatus(stepsNodes, verify, "审核通过", StepsTypeEnum.success);
            // 付款状态
            bindPaymentAndInvoiceStepsStatus(data, stepsNodes, payment, invoice);
            end.setType(StepsTypeEnum.success.getType());
            stepsNodes.add(end);
        }
        if (data.getStatus() == 3) {
            if (data.getAuditStatus() == 0) {
                stepsNodes.add(close);
            }
            if (data.getAuditStatus() == 2) {
                stepsStatus(stepsNodes, valid, "已确认", StepsTypeEnum.success);
                stepsStatus(stepsNodes, verify, "审核通过", StepsTypeEnum.success);
                stepsNodes.add(close);
            }
            if (data.getAuditStatus() == 3) {
                stepsStatus(stepsNodes, valid, "已确认", StepsTypeEnum.success);
                stepsStatus(stepsNodes, verify, "审核不通过", StepsTypeEnum.error);
                stepsNodes.add(close);
            }

        }
        return stepsNodes;
    }

    /**
     * 改变进度条节点状态
     *
     * @param results  结果集
     * @param nodeDto  要改变节点
     * @param title    节点名
     * @param typeEnum 节点状态
     */
    private void stepsStatus(List<StepsNodeDto> results, StepsNodeDto nodeDto, String title, StepsTypeEnum typeEnum) {
        nodeDto.setTitle(title);
        nodeDto.setType(typeEnum.getType());
        results.add(nodeDto);
    }

    /**
     * 收票 付款节点
     *
     * @param data       采购费用单
     * @param stepsNodes 进度条集合
     * @param payment    付款状态节点
     * @param invoice    开票状态节点
     */
    private void bindPaymentAndInvoiceStepsStatus(BuyorderExpenseDto data, List<StepsNodeDto> stepsNodes, StepsNodeDto payment, StepsNodeDto invoice) {
        int paymentStatus = data.getPaymentStatus();
        if (paymentStatus == 1) {
            payment.setTitle("部分付款");
        } else if (paymentStatus == 2) {
            payment.setTitle("全部付款");
            payment.setType(StepsTypeEnum.success.getType());
        }
        stepsNodes.add(payment);

        // 收票状态
        int invoiceStatus = data.getInvoiceStatus();
        if (invoiceStatus == 1) {
            invoice.setTitle("部分收票");
        } else if (invoiceStatus == 2) {
            invoice.setTitle("全部收票");
            invoice.setType(StepsTypeEnum.success.getType());
        }
        stepsNodes.add(invoice);
    }

    @Override
    @MethodLock(field = "buyOrderExpenseId", className = Integer.class)
    public boolean doBuyOrderExpenseInvoiceStatus(@MethodLockParam Integer buyOrderExpenseId) {
        Assert.notNull(buyOrderExpenseId, "计算采购费用单收票状态，采购费用单id不可为空");

        List<BuyorderExpenseItemDto> buyorderExpenseGoodsDtos = buyorderExpenseItemMapper.selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(buyOrderExpenseId, 0);

        Map<Integer, List<InvoiceByGoodsDto>> invoiceListMap = calcInvoiceNum(buyOrderExpenseId);
        Map<Integer, Integer> returnNumMap = calcReturnNum(buyOrderExpenseId);

        // 计算售后数量
        buyorderExpenseGoodsDtos.forEach(c -> {

            Integer returnNum = returnNumMap.get(c.getBuyorderExpenseItemId());
            c.setReturnNum(returnNum == null ? 0 : returnNum);

            List<InvoiceByGoodsDto> invoiceByGoodsDtos = invoiceListMap.get(c.getBuyorderExpenseItemId());
            if (CollUtil.isNotEmpty(invoiceByGoodsDtos)) {
                BigDecimal invoiceNum = calcRealInvoiceNum(invoiceByGoodsDtos);
                c.setInvoicedNum(invoiceNum);
            }
        });

        BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
        try {
            bean.calcInvoiceStatus(buyorderExpenseGoodsDtos, buyOrderExpenseId);
            bean.completeBuyOrderExpense(buyOrderExpenseId);
            return true;
        } catch (Exception e) {
            log.error("计算采购费用单：{} 收票状态错误：{}", buyOrderExpenseId, e);
        }
        return false;


    }


    /**
     * 计算并更新采购费用单的收票状态
     * 当有效数量为0 则商品状态置为全部收票
     *
     * @param buyorderExpenseGoodsDtos 封装好售后数量和收票数量的商品对象
     * @param buyOrderExpenseId        费用单id
     */
    @Transactional(rollbackFor = Throwable.class)
    public void calcInvoiceStatus(List<BuyorderExpenseItemDto> buyorderExpenseGoodsDtos, Integer buyOrderExpenseId) {

        log.info("calcInvoiceStatus 费用单id:{},商品信息：{}", buyOrderExpenseId, JSON.toJSONString(buyorderExpenseGoodsDtos));
        buyorderExpenseGoodsDtos.forEach(c -> {

            int realNum = c.getNum() - c.getReturnNum();
            BigDecimal realNumBigDecimal = new BigDecimal(realNum);
            c.setInvoicedNum(c.getInvoicedNum() == null ? BigDecimal.ZERO : c.getInvoicedNum());
            log.info("calcInvoiceStatus realNum:{},realNumBigDecimal:{},invoiceNum:{}", JSON.toJSONString(realNum), JSON.toJSONString(realNumBigDecimal), JSON.toJSONString(c.getInvoicedNum()));
            // 实际数量小于等于0 或者 实际数量小于或等于实际收票数量
            if (realNum <= 0 || realNumBigDecimal.compareTo(c.getInvoicedNum()) <= 0) {

                BuyorderExpenseItemEntity update = new BuyorderExpenseItemEntity();
                update.setBuyorderExpenseItemId(c.getBuyorderExpenseItemId());
                update.setInvoiceStatus(BuyorderExpenseConstant.INVOICE_STATUS_ALL);
                update.setInvoiceTime(new Date());
                buyorderExpenseItemMapper.updateByPrimaryKeySelective(update);
                return;
            }
            // 收票数量为0 未收票
            if (c.getInvoicedNum().compareTo(BigDecimal.ZERO) == 0) {
                BuyorderExpenseItemEntity update = new BuyorderExpenseItemEntity();
                update.setBuyorderExpenseItemId(c.getBuyorderExpenseItemId());
                update.setInvoiceStatus(BuyorderExpenseConstant.INVOICE_STATUS_NONE);
                buyorderExpenseItemMapper.updateByPrimaryKeySelective(update);
                return;
            }
            // 实际数量大于实际收票数量
            if (realNumBigDecimal.compareTo(c.getInvoicedNum()) > 0 && c.getInvoicedNum().compareTo(BigDecimal.ZERO) > 0) {
                BuyorderExpenseItemEntity update = new BuyorderExpenseItemEntity();
                update.setBuyorderExpenseItemId(c.getBuyorderExpenseItemId());
                update.setInvoiceStatus(BuyorderExpenseConstant.INVOICE_STATUS_PART);
                update.setInvoiceTime(new Date());
                buyorderExpenseItemMapper.updateByPrimaryKeySelective(update);
            }

        });

        List<BuyorderExpenseItemDto> newData = buyorderExpenseItemMapper.selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(buyOrderExpenseId, 0);

        Optional<BuyorderExpenseItemDto> all = newData.stream().filter(c -> Objects.equals(c.getInvoiceStatus(), BuyorderExpenseConstant.INVOICE_STATUS_ALL)).findAny();
        Optional<BuyorderExpenseItemDto> anyOne = newData.stream().filter(c -> Objects.equals(c.getInvoiceStatus(), BuyorderExpenseConstant.INVOICE_STATUS_PART)).findAny();
        Optional<BuyorderExpenseItemDto> no = newData.stream().filter(c -> Objects.equals(c.getInvoiceStatus(), BuyorderExpenseConstant.INVOICE_STATUS_NONE)).findAny();
        // 未开票
        if (!all.isPresent() && !anyOne.isPresent()) {
            BuyorderExpenseEntity update = new BuyorderExpenseEntity();
            update.setInvoiceStatus(BuyorderExpenseConstant.INVOICE_STATUS_NONE);
            update.setBuyorderExpenseId(buyOrderExpenseId);
            buyorderExpenseMapper.updateByPrimaryKeySelective(update);
            return;
        }

        // 全部
        if (!anyOne.isPresent() && !no.isPresent()) {
            BuyorderExpenseEntity update = new BuyorderExpenseEntity();
            update.setInvoiceStatus(BuyorderExpenseConstant.INVOICE_STATUS_ALL);
            update.setInvoiceTime(new Date());
            update.setBuyorderExpenseId(buyOrderExpenseId);
            buyorderExpenseMapper.updateByPrimaryKeySelective(update);
            return;
        }

        // 部分
        if (anyOne.isPresent() || no.isPresent()) {
            BuyorderExpenseEntity update = new BuyorderExpenseEntity();
            update.setInvoiceStatus(BuyorderExpenseConstant.INVOICE_STATUS_PART);
            update.setInvoiceTime(new Date());
            update.setBuyorderExpenseId(buyOrderExpenseId);
            buyorderExpenseMapper.updateByPrimaryKeySelective(update);
        }


    }

    /**
     * 这个方法也是一坨屎，因为业务的需要全退完的要回退为已关闭
     * @param buyorderExpenseId 费用单id
     * @return boolean
     */
    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean completeBuyOrderExpense(Integer buyorderExpenseId) {

        log.info("completeBuyOrderExpense 采购费用单发起完结:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "完结采购单时候费用单不可为空");
        // 检测条件
        try {
            //  直属分费用单关闭状态 单独走
            boolean checkBuyOrderExpenseOrderTypeZero = checkBuyOrderExpenseOrderTypeZero(buyorderExpenseId);
            if (checkBuyOrderExpenseOrderTypeZero) {
                boolean isAllReturn = checkAllGoodsReturnAndNoInvoice(buyorderExpenseId);
                if (isAllReturn) {
                    BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
                    bean.freeReturnAlert(buyorderExpenseId);
                    BuyorderExpenseEntity update = new BuyorderExpenseEntity();
                    update.setBuyorderExpenseId(buyorderExpenseId);
                    update.setStatus(3);
                    buyorderExpenseMapper.updateByPrimaryKeySelective(update);
                    return true;
                }
            }
            if (checkBuyOrderExpense(buyorderExpenseId)) {
                BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);

                // 全部退了状态改为关闭
                boolean isAllReturn = checkAllGoodsReturnAndNoInvoice(buyorderExpenseId);
                if (isAllReturn) {
                    BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
                    bean.freeReturnAlert(buyorderExpenseId);
                    BuyorderExpenseEntity update = new BuyorderExpenseEntity();
                    update.setBuyorderExpenseId(buyorderExpenseId);
                    update.setStatus(3);
                    buyorderExpenseMapper.updateByPrimaryKeySelective(update);
                }else {
                    if (BuyorderExpenseConstant.STATUS_PROCESS.equals(buyorderExpenseEntity.getStatus())) {
                        BuyorderExpenseEntity update = new BuyorderExpenseEntity();
                        update.setBuyorderExpenseId(buyorderExpenseId);
                        update.setStatus(2);
                        buyorderExpenseMapper.updateByPrimaryKeySelective(update);
                        return true;
                    }
                }
                return false;

            } else {
                BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
                if (Objects.nonNull(buyorderExpenseEntity)) {
                    if (buyorderExpenseEntity.getStatus().equals(2)) {
                        log.info("completeBuyOrderExpense 完结状态回退:{}", JSON.toJSONString(buyorderExpenseId));
                        BuyorderExpenseEntity update = new BuyorderExpenseEntity();
                        update.setBuyorderExpenseId(buyorderExpenseId);
                        update.setStatus(1);
                        buyorderExpenseMapper.updateByPrimaryKeySelective(update);
                    }
                }
            }


        } catch (Exception e) {
            log.error("不符合采购费用单关闭条件：{}", buyorderExpenseId);
        }
        return false;
    }

    /**
     * 直属单判断是否可以关闭
     * @param buyorderExpenseId
     * @return
     */
    private boolean checkBuyOrderExpenseOrderTypeZero(Integer buyorderExpenseId) {
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
        if (!BuyorderExpenseConstant.ORDER_TYPE_ZERO.equals(buyorderExpenseEntity.getOrderType())) {
            return false;
        }
        if (BuyorderExpenseConstant.AUDIT_STATUS_SUCCESS.equals(buyorderExpenseEntity.getAuditStatus()) &&
                (BuyorderExpenseConstant.PAYMENT_STATUS_ALL.equals(buyorderExpenseEntity.getPaymentStatus()) ||
                BuyorderExpenseConstant.DELIVERY_STATUS_ALL.equals(buyorderExpenseEntity.getDeliveryStatus()) ||
                BuyorderExpenseConstant.ARRIVAL_STATUS_ALL.equals(buyorderExpenseEntity.getArrivalStatus()) )&&
                BuyorderExpenseConstant.LOCKED_STATUS_UN.equals(buyorderExpenseEntity.getLockedStatus())) {

        } else {
            log.info("采购费用单想关闭不符合订单主体条件：{}", JSON.toJSONString(buyorderExpenseEntity));
            return false;
        }

        List<ExpenseAfterSalesDto> afterOrders = expenseAfterSalesService.getExpenseAfterSalesBybuyorderExpenseId(buyorderExpenseId);
        Optional<ExpenseAfterSalesDto> any = afterOrders.stream().filter(c -> Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 1) || Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 0)).findAny();
        if (any.isPresent()) {
            log.info("采购费用单想关闭均为已完结或者已关闭的售后单：{}", JSON.toJSONString(any.get()));
            return false;
        }
        return true;
    }

    /**
     * 校验费用单里所有的商品和票是否都退
     * @param buyorderExpenseId 采购费用单id
     * @return boolean
     */
    private boolean checkAllGoodsReturnAndNoInvoice(Integer buyorderExpenseId) {

        List<BuyorderExpenseItemDto> buyorderExpenseItemDtos = returnNumAndInvoiceNum(buyorderExpenseId);
        if (CollUtil.isEmpty(buyorderExpenseItemDtos)) {
            return true;
        }
        // 存在未退完的数量 以及 开票数量
        Optional<BuyorderExpenseItemDto> any = buyorderExpenseItemDtos.stream().filter(c -> BigDecimal.ZERO.compareTo(c.getInvoicedNum()) < 0 || (c.getNum() - c.getReturnNum()) > 0).findAny();
        return !any.isPresent();

    }

    @Override
    @Transactional
    public boolean doDeliveryStatus(Integer buyorderExpenseId) {

        log.info("doDeliveryStatus 采购费用单发起全部发货:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "更新发货状态采购费用单id不可为空");

        BuyorderExpenseEntity update = new BuyorderExpenseEntity();
        update.setBuyorderExpenseId(buyorderExpenseId);
        update.setDeliveryStatus(BuyorderExpenseConstant.DELIVERY_STATUS_ALL);
        buyorderExpenseMapper.updateByPrimaryKeySelective(update);
        buyorderExpenseItemMapper.deliveryAllByBuyorderExpenseId(buyorderExpenseId);
        return true;
    }

    @Override
    @Transactional
    public boolean doArrivalStatus(Integer buyorderExpenseId) {

        log.info("doArrivalStatus 采购费用单发起全部收货:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "更新收货状态采购费用单id不可为空");

        BuyorderExpenseEntity update = new BuyorderExpenseEntity();
        update.setBuyorderExpenseId(buyorderExpenseId);
        update.setArrivalStatus(BuyorderExpenseConstant.ARRIVAL_STATUS_ALL);
        buyorderExpenseMapper.updateByPrimaryKeySelective(update);
        buyorderExpenseItemMapper.arrivalAllByBuyorderExpenseId(buyorderExpenseId);
        return true;
    }

    @Override
    public boolean doAfterSaleStatus(Integer buyorderExpenseId) {
        Assert.notNull(buyorderExpenseId, "更新售后状态采购费用单id不可为空");

        List<ExpenseAfterSalesDto> data = expenseAfterSalesService.getExpenseAfterSalesBybuyorderExpenseId(buyorderExpenseId);
        if (CollUtil.isEmpty(data)) {
            return true;
        }
        Optional<ExpenseAfterSalesDto> process = data.stream().filter(c -> c.getExpenseAfterSalesStatusDto() != null).filter(c -> c.getExpenseAfterSalesStatusDto().getAfterSalesStatus().equals(1)).findAny();
        if (process.isPresent()) {
            BuyorderExpenseEntity update = new BuyorderExpenseEntity();
            update.setBuyorderExpenseId(buyorderExpenseId);
            update.setServiceStatus(BuyorderExpenseConstant.SERVICE_STATUS_PROCESS);
            buyorderExpenseMapper.updateByPrimaryKeySelective(update);
        } else {

            // 以最后一个售后单状态为准
            ExpenseAfterSalesDto expenseAfterSalesDto = data.get(data.size() - 1);

            if (Objects.nonNull(expenseAfterSalesDto.getExpenseAfterSalesStatusDto())) {
                BuyorderExpenseEntity update = new BuyorderExpenseEntity();
                update.setBuyorderExpenseId(buyorderExpenseId);
                update.setServiceStatus(expenseAfterSalesDto.getExpenseAfterSalesStatusDto().getAfterSalesStatus());
                buyorderExpenseMapper.updateByPrimaryKeySelective(update);
            }

        }
        return true;
    }

    /**
     * 完结前条件
     * （订单审核状态！=审核中）&（收票状态=全部收票）&（收款状态=全部收款）&（订单发货状态=全部发货）
     * &（订单收货状态=全部收货）&（订单关联的售后单均为已完结或者已关闭）&（订单锁定状态=未锁定）
     *
     * @param buyorderExpenseId 采购费用单id
     * @return 是否通过校验
     */
    private boolean checkBuyOrderExpense(Integer buyorderExpenseId) {
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
        if (BuyorderExpenseConstant.AUDIT_STATUS_SUCCESS.equals(buyorderExpenseEntity.getAuditStatus()) &&
                BuyorderExpenseConstant.INVOICE_STATUS_ALL.equals(buyorderExpenseEntity.getInvoiceStatus()) &&
                BuyorderExpenseConstant.PAYMENT_STATUS_ALL.equals(buyorderExpenseEntity.getPaymentStatus()) &&
                BuyorderExpenseConstant.DELIVERY_STATUS_ALL.equals(buyorderExpenseEntity.getDeliveryStatus()) &&
                BuyorderExpenseConstant.ARRIVAL_STATUS_ALL.equals(buyorderExpenseEntity.getArrivalStatus()) &&
                BuyorderExpenseConstant.LOCKED_STATUS_UN.equals(buyorderExpenseEntity.getLockedStatus())) {

        } else {
            log.info("采购费用单想关闭不符合订单主体条件：{}", JSON.toJSONString(buyorderExpenseEntity));
            return false;
        }

        List<ExpenseAfterSalesDto> afterOrders = expenseAfterSalesService.getExpenseAfterSalesBybuyorderExpenseId(buyorderExpenseId);
        Optional<ExpenseAfterSalesDto> any = afterOrders.stream().filter(c -> Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 1) || Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 0)).findAny();
        if (any.isPresent()) {
            log.info("采购费用单想关闭均为已完结或者已关闭的售后单：{}", JSON.toJSONString(any.get()));
            return false;
        }
        return true;
    }

    /**
     * 非直属采购费用订单收货状态为全部收货；直属采购费用订单，所属采购订单或直属采购费用订单部分付款或全部付款
     * 且订单状态非已关闭
     * 且订单未锁定
     * 且没有订单状态为待确认或进行中的售后单
     *
     * @param buyorderExpenseId 费用单id
     * @return boolean
     */
    @Override
    public boolean checkExpenseCanAfterSales(Integer buyorderExpenseId) {

        log.info("checkExpenseCanAfterSales 入参:{}", JSON.toJSONString(buyorderExpenseId));
        Assert.notNull(buyorderExpenseId, "checkExpenseCanAfterSales 费用单id不可为空");
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
        Assert.notNull(buyorderExpenseEntity, "checkExpenseCanAfterSales 费用单未查到信息");
        log.info("checkExpenseCanAfterSales 当前费用单数据：{}", JSON.toJSONString(buyorderExpenseEntity));

        // 直费用单
        if (Objects.equals(BuyorderExpenseConstant.ORDER_TYPE_ZERO, buyorderExpenseEntity.getOrderType())) {
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderExpenseEntity.getBuyorderId());
            Assert.notNull(buyorder, "checkExpenseCanAfterSales 直属费用单未查到关联的采购单");
            if (BuyorderExpenseConstant.PAYMENT_STATUS_ALL.equals(buyorderExpenseEntity.getPaymentStatus())
                    || BuyorderExpenseConstant.PAYMENT_STATUS_PART.equals(buyorderExpenseEntity.getPaymentStatus())
                    || BuyorderExpenseConstant.PAYMENT_STATUS_ALL.equals(buyorder.getPaymentStatus())
                    || BuyorderExpenseConstant.PAYMENT_STATUS_PART.equals(buyorder.getPaymentStatus())) {

            } else {
                log.info("直属采购费用订单 付款状态不满足");
                return false;
            }
        } else {
            // 非直属
            // 全部收货
            if (!BuyorderExpenseConstant.ARRIVAL_STATUS_ALL.equals(buyorderExpenseEntity.getArrivalStatus())) {
                log.info("非直属采购费用单未全部发货");
                return false;
            }
        }

        // 关闭  锁定
        if (buyorderExpenseEntity.getStatus().equals(BuyorderExpenseConstant.STATUS_CLOSE) ||
                buyorderExpenseEntity.getLockedStatus().equals(BuyorderExpenseConstant.LOCKED_STATUS_ON)
        ) {
            log.info("采购费用单状态未关闭或已锁定");
            return false;
        }
        List<ExpenseAfterSalesDto> afterOrders = expenseAfterSalesService.getExpenseAfterSalesBybuyorderExpenseId(buyorderExpenseId);
        Optional<ExpenseAfterSalesDto> any = afterOrders.stream().filter(c -> Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 1) || Objects.equals(c.getExpenseAfterSalesStatusDto().getAfterSalesStatus(), 0)).findAny();
        if (any.isPresent()) {
            log.info("采购费用单想关闭均为已完结或者已关闭的售后单：{}", JSON.toJSONString(any.get()));
            return false;
        }
        return true;
    }

    @Override
    public BuyorderExpenseDto byPreBuyorderGetDetail(List<Integer> saleOrderGoodsIdList) {
        BuyorderExpenseDto buyorderExpenseDto = new BuyorderExpenseDto();
        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsApiService.getRelatedDetail(saleOrderGoodsIdList);
        if (CollUtil.isEmpty(saleOrderGoodsDetailDtos)) {
            return buyorderExpenseDto;
        }
        List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = saleOrderGoodsDetailDtos.stream()
                .map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());
        if (CollUtil.isEmpty(buyOrderSaleOrderGoodsDetailDtoList)) {
            return buyorderExpenseDto;
        }
        this.getNeedBuyNum(buyOrderSaleOrderGoodsDetailDtoList);
        List<BuyOrderSaleOrderGoodsDetailDto> detailDtos = buyOrderSaleOrderGoodsDetailDtoList.stream().filter(b -> b.getNum() > 0).collect(Collectors.toList());
        List<BuyorderExpenseItemDto> buyorderExpenseItemDtoList = new ArrayList<>();
        ArrayList<SaleOrderGoodsDetailDto> collect = saleOrderGoodsDetailDtos.stream().collect(Collectors.collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SaleOrderGoodsDetailDto::getGoodsId))), ArrayList::new));
        collect.forEach(s -> {
            CoreSkuDto coreSkuDto = coreSkuApiService.getCoreSkuDtoBySkuId(s.getGoodsId());
            if (coreSkuDto != null) {
                BuyorderExpenseItemDto buyorderExpenseItemDto = new BuyorderExpenseItemDto();
                buyorderExpenseItemDto.setNum(s.getNum());
                BuyorderExpenseItemDetailDto buyorderExpenseItemDetailDto = new BuyorderExpenseItemDetailDto();
                buyorderExpenseItemDetailDto.setSku(coreSkuDto.getSku());
                buyorderExpenseItemDetailDto.setGoodsName(coreSkuDto.getSkuName());
                buyorderExpenseItemDetailDto.setExpenseCategoryName(coreSkuDto.getCategoryName());
                buyorderExpenseItemDetailDto.setHaveStockManage(coreSkuDto.getHaveStockManage());
                buyorderExpenseItemDto.setBuyorderExpenseItemDetailDto(buyorderExpenseItemDetailDto);
                if (CollUtil.isNotEmpty(detailDtos)) {
                    List<BuyOrderSaleOrderGoodsDetailDto> dtoList = detailDtos
                            .stream().filter(b -> b.getGoodsId().equals(s.getGoodsId())).collect(Collectors.toList());
                    buyorderExpenseItemDto.setBuyOrderSaleOrderGoodsDetailDtos(dtoList);
                }
                buyorderExpenseItemDtoList.add(buyorderExpenseItemDto);
            }
        });
        buyorderExpenseDto.setBuyorderExpenseItemDtos(buyorderExpenseItemDtoList);
        return buyorderExpenseDto;
    }

    @Override
    public void lockBuyOrderExpense(Integer buyOrderExpenseId) {
        log.info("lockBuyOrderExpense 采购费用单发起锁定:{}", JSON.toJSONString(buyOrderExpenseId));
        BuyorderExpenseEntity lockEntity = new BuyorderExpenseEntity();
        lockEntity.setBuyorderExpenseId(buyOrderExpenseId);
        lockEntity.setLockedStatus(BuyorderExpenseConstant.LOCKED_STATUS_ON);
        buyorderExpenseMapper.updateByPrimaryKeySelective(lockEntity);
    }

    @Override
    public void unlockBuyOrderExpense(Integer buyOrderExpenseId) {
        log.info("unlockBuyOrderExpense 采购费用单发起解锁:{}", JSON.toJSONString(buyOrderExpenseId));
        BuyorderExpenseEntity unlockEntity = new BuyorderExpenseEntity();
        unlockEntity.setBuyorderExpenseId(buyOrderExpenseId);
        unlockEntity.setLockedStatus(BuyorderExpenseConstant.LOCKED_STATUS_UN);
        buyorderExpenseMapper.updateByPrimaryKeySelective(unlockEntity);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean paymentStatus(Integer buyOrderExpenseId) {
        log.info("paymentStatus 非直属采购费用单发起付款状态变更:{}", JSON.toJSONString(buyOrderExpenseId));
        BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
        boolean b = bean.directPaymentStatus(buyOrderExpenseId);
        if (b) {
            bean.doDeliveryStatus(buyOrderExpenseId);
            bean.doArrivalStatus(buyOrderExpenseId);
            saleOrderGoodsApiService.pay4SaleStatus(buyOrderExpenseId);
        }
        return true;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void updatePaymentStatus(Integer buyOrderExpenseId, int paymentStatus, BuyorderExpenseEntity update) {
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyOrderExpenseId);
        if (Objects.nonNull(buyorderExpenseEntity) && buyorderExpenseEntity.getPaymentStatus() != paymentStatus) {
            update.setPaymentTime(new Date());
        }
        buyorderExpenseMapper.updateByPrimaryKeySelective(update);
    }

    /**
     * 计算状态
     *
     * @param payAll   已付
     * @param subtract 已付和需要付款相减结果
     * @return 付款状态
     */
    private int calcPaymentStatus(BigDecimal payAll, BigDecimal subtract) {
        int payStatus = BuyorderExpenseConstant.PAYMENT_STATUS_NO;
        if (payAll.compareTo(BigDecimal.ZERO) <= 0) {
            if (subtract.compareTo(BigDecimal.ZERO) == 0) {
                return BuyorderExpenseConstant.PAYMENT_STATUS_ALL;
            }
            return payStatus;
        }
        // 1元内的差距 全部付款
        if (subtract.abs().compareTo(BigDecimal.ONE) < 1) {
            payStatus = BuyorderExpenseConstant.PAYMENT_STATUS_ALL;
            return payStatus;
        }
        // 不足部分付款
        if (subtract.compareTo(BigDecimal.ZERO) < 0) {
            payStatus = BuyorderExpenseConstant.PAYMENT_STATUS_PART;
        } else {
            // 全部付款
            payStatus = BuyorderExpenseConstant.PAYMENT_STATUS_ALL;
        }
        return payStatus;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public boolean directPaymentStatus(Integer buyOrderExpenseId) {
        // 捞此单的审核通过的付款申请的流水

        CapitalBillDetailDto query = CapitalBillDetailDto.builder()
                .orderType(4)
                .relatedId(buyOrderExpenseId)
                .bussinessType(525)
                .traderType(2)
                .build();

        // 含账期
        List<CapitalBillDto> capitalBillData = capitalBillApiService.getCapitalBillData(query, null);
        log.info("查到的采购费用单{}付款流水信息：{}", buyOrderExpenseId, JSON.toJSONString(capitalBillData));
        if (CollUtil.isEmpty(capitalBillData)) {
            return false;
        }

        BigDecimal payAll = capitalBillData.stream().map(CapitalBillDto::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        // 售后真实退款 含退还账期
        BigDecimal refundMoney = expenseAfterSalesApiService.getRefundMoney(buyOrderExpenseId);
        payAll = payAll.subtract(refundMoney);


        log.info("查到的采购费用单{}已经付款：{}", buyOrderExpenseId, JSON.toJSONString(payAll));
        List<BuyorderExpenseItemDto> buyorderExpenseGoodsDtos = buyorderExpenseItemMapper.selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(buyOrderExpenseId, 0);

        Map<Integer, Integer> returnNumMap = calcReturnNum(buyOrderExpenseId);
        log.info("查到的采购费用单{}售后：{}", buyOrderExpenseId, JSON.toJSONString(returnNumMap));
        // 计算售后数量
        buyorderExpenseGoodsDtos.forEach(c -> {
            Integer returnNum = returnNumMap.get(c.getBuyorderExpenseItemId());
            c.setReturnNum(returnNum == null ? 0 : returnNum);
        });
        // 计算需要支付的
        BigDecimal needPay = buyorderExpenseGoodsDtos.stream().map(c -> c.getBuyorderExpenseItemDetailDto().getPrice().multiply(new BigDecimal(Math.max(c.getNum() - c.getReturnNum(), 0)))).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        log.info("查到的采购费用单{}需要付款：{}", buyOrderExpenseId, JSON.toJSONString(needPay));
        BigDecimal subtract = payAll.subtract(needPay);
        int paymentStatus = calcPaymentStatus(payAll, subtract);
        log.info("查到的采购费用单{}计算出的付款状态：{}", buyOrderExpenseId, paymentStatus);

        BuyorderExpenseEntity update = new BuyorderExpenseEntity();
        update.setBuyorderExpenseId(buyOrderExpenseId);
        update.setPaymentStatus(paymentStatus);
        BuyorderExpenseServiceImpl bean = SpringUtil.getBean(BuyorderExpenseServiceImpl.class);
        bean.updatePaymentStatus(buyOrderExpenseId, paymentStatus, update);
        return true;

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExpenseInfoByExpenseId(Integer buyOrderExpenseId) {
        BuyorderExpenseEntity deleteExpense = new BuyorderExpenseEntity();
        deleteExpense.setBuyorderExpenseId(buyOrderExpenseId);
        deleteExpense.setIsDelete(BuyorderExpenseConstant.IS_DELETED);
        buyorderExpenseMapper.updateByPrimaryKeySelective(deleteExpense);

        BuyorderExpenseDetailEntity deleteDetail = new BuyorderExpenseDetailEntity();
        deleteDetail.setBuyorderExpenseId(buyOrderExpenseId);
        deleteDetail.setIsDelete(BuyorderExpenseConstant.IS_DELETED);
        buyorderExpenseDetailMapper.deleteExpenseDetailByExpenseId(deleteDetail);

        // 先查出所有expenseItem信息
        List<BuyorderExpenseItemEntity> itemEntities = buyorderExpenseItemMapper.getByBuyorderExpenseId(buyOrderExpenseId).stream().peek(item -> item.setIsDelete(BuyorderExpenseConstant.IS_DELETED)).collect(Collectors.toList());
        buyorderExpenseItemMapper.batchUpdate(itemEntities);

        // 再查出关联的expenseItemDetail信息
        List<Integer> itemIdList = itemEntities.stream().map(BuyorderExpenseItemEntity::getBuyorderExpenseItemId).collect(Collectors.toList());
        List<BuyorderExpenseItemDetailEntity> detailEntities = buyorderExpenseItemDetailMapper.getAllByItemIdList(itemIdList).stream().peek(item -> item.setIsDelete(BuyorderExpenseConstant.IS_DELETED)).collect(Collectors.toList());
        buyorderExpenseItemDetailMapper.batchUpdate(detailEntities);
    }

    @Override
    public void updateDirectExpenseInfo(Integer buyorderId, Integer buyorderModifyApplyId, BuyorderExpenseDetailDto buyorderExpenseDetailDto) {
        // 先查出是否存在虚拟商品修改单信息
        List<BuyorderModifyApplyExpenseGoodsEntity> expenseGoodsEntities = buyorderModifyApplyExpenseGoodsMapper.getByBuyorderModifyApplyId(buyorderModifyApplyId);
        if (expenseGoodsEntities.size() > 0) {
            // 查出对应的直属费用单信息
            BuyorderExpenseDetailEntity expenseDetailEntity = buyorderExpenseDetailMapper.selectByBuyorderId(buyorderId);
            expenseDetailEntity.setInvoiceType(buyorderExpenseDetailDto.getInvoiceType());
            expenseDetailEntity.setInvoiceComments(buyorderExpenseDetailDto.getInvoiceComments());
            buyorderExpenseDetailMapper.updateByPrimaryKeySelective(expenseDetailEntity);

            // 更新费用单商品信息
            expenseGoodsEntities.forEach(item -> {
                BuyorderExpenseItemDetailEntity update = new BuyorderExpenseItemDetailEntity();
                update.setBuyorderExpenseItemId(item.getBuyorderExpenseItemId());
                update.setInsideComments(item.getInsideComments());
                buyorderExpenseItemDetailMapper.updateByBuyorderExpenseItemIdSelective(update);
            });
            log.info("采购单申请修改审核通过更新直属非要用单相关信息，采购单id：{}， 采购修改单id：{}， 费用商品信息：{}", buyorderId, buyorderModifyApplyId, JSONArray.toJSON(expenseGoodsEntities));
        }
    }

    @Override
    public List<BuyorderExpenseItemDto> getExpenseItemModifyInfoList(Integer buyorderModifyApplyId) {
        return buyorderExpenseItemMapper.getExpenseItemModifyInfo(buyorderModifyApplyId);
    }

    @Override
    public BuyorderExpenseItemDto getBuyorerExpenseByAfterSalesId(Integer afterSalesId) {

        BuyorderExpenseItemDto buyorderExpenseItemDto = new BuyorderExpenseItemDto();
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseMapper.getBuyorerExpenseByAfterSalesId(afterSalesId);
        if (buyorderExpenseDto == null) {
            return buyorderExpenseItemDto;
        }
        Integer buyorderExpenseId = buyorderExpenseDto.getBuyorderExpenseId();
        buyorderExpenseItemDto.setBuyorderExpenseId(buyorderExpenseId);
        List<BuyorderExpenseItemEntity> byBuyorderExpenseId = buyorderExpenseItemMapper.getByBuyorderExpenseId(buyorderExpenseId);
        if (byBuyorderExpenseId != null && byBuyorderExpenseId.size() > 0) {
            BuyorderExpenseItemEntity buyorderExpenseItemEntity = byBuyorderExpenseId.get(0);
            buyorderExpenseItemDto.setBuyorderExpenseItemId(buyorderExpenseItemEntity.getBuyorderExpenseItemId());
        }

        return buyorderExpenseItemDto;
    }


    @Override
    public List<Integer> returnAlertBuyOrderExpenseGoodsShare(ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarn) {
        log.info("returnAlertBuyOrderExpenseGoodsShare 退货触发采购预警,  data -----> {}", JSON.toJSONString(expenseReturnEarlyWarn));
        // 根据采购费用单 ID 查询退货预警
        List<Integer> buyOrderExpenseIdList = expenseReturnEarlyWarn.getBuyorderExpenseIdList();
        Integer saleorderId = expenseReturnEarlyWarn.getSaleorderId();
        Integer saleorderGoodsId = expenseReturnEarlyWarn.getSaleorderGoodsId();
        Integer skuId = expenseReturnEarlyWarn.getSkuId();
        String skuNo = expenseReturnEarlyWarn.getSkuNo();
        Integer num = expenseReturnEarlyWarn.getNum();
        Integer afterSalesOrderId = expenseReturnEarlyWarn.getAfterSalesOrderId();

        Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleorderId);

        // 因为ORDER_DESC 是 JSONArray 无法直接转 BuyorderExpenseDto 需要先查出 Entity 把 Entity 转成 Dto
        // 因为虚拟商品数据可能不是很多 所以 为了方便 这里直接使用 getOrderMainData 循环查询单条数据
        List<BuyorderExpenseDto> buyOrderExpenseList = new ArrayList<>();
        for (Integer buyOrderExpenseId : buyOrderExpenseIdList) {
            BuyorderExpenseDto orderMainData = this.getOrderMainData(buyOrderExpenseId);
            buyOrderExpenseList.add(orderMainData);
        }
        buyOrderExpenseList.sort(Comparator.comparing(BaseDto::getAddTime).reversed());
        // 记录初始数据
        Integer originNum = num;
        // 记录触发预警的 采购费用单，用于发送站内信
        List<Integer> notifyBuyorderExpenseIdList = new ArrayList<>();

        for (BuyorderExpenseDto buyorderExpenseDto : buyOrderExpenseList) {
            log.info("returnAlertBuyOrderExpenseGoodsShare 销售单 {} skuId {} 的售后单ID {}, 开始对 费用单 {} 进行分摊, 待分摊数量 {}", saleorder.getSaleorderNo(), skuId,afterSalesOrderId, buyorderExpenseDto.getBuyorderExpenseNo(), num);
            // 如果 退货数量分摊完， 则不再分摊
            // 如果数量还有 继续分摊到下一个 采购费用单
            if (num.compareTo(0) <= 0) {
                break;
            }
            RBuyorderExpenseJSaleorderEntity totalNumBySaleorderGoodsIdAndBuyorderExpenseId = rBuyorderExpenseJSaleorderMapper.getTotalNumBySaleorderGoodsIdAndBuyorderExpenseId(saleorderGoodsId, buyorderExpenseDto.getBuyorderExpenseId());
            RBuyorderExpenseJSaleorderEntity returnNumByBuyorderExpenseIdAndSaleorderGoodsId = rBuyorderExpenseJSaleorderMapper.getReturnNumByBuyorderExpenseIdAndSaleorderGoodsId(saleorderGoodsId, buyorderExpenseDto.getBuyorderExpenseId());
            Integer totalNum = totalNumBySaleorderGoodsIdAndBuyorderExpenseId == null ? 0 : totalNumBySaleorderGoodsIdAndBuyorderExpenseId.getNum();
            Integer returnNum = returnNumByBuyorderExpenseIdAndSaleorderGoodsId == null ? 0 : returnNumByBuyorderExpenseIdAndSaleorderGoodsId.getNum();
            Integer realNum = totalNum - returnNum;
            log.info("returnAlertBuyOrderExpenseGoodsShare 对销售单 {} skuId {}, 费用单 {} 的实际采购数量 {}", saleorder.getSaleorderNo(), skuId, buyorderExpenseDto.getBuyorderExpenseNo(), realNum);
            if(realNum.compareTo(0) <= 0){
                log.info("returnAlertBuyOrderExpenseGoodsShare 对销售单 {} skuId {}, 费用单 {} 的实际采购数量为 0, 跳过不触发预警", saleorder.getSaleorderNo(), skuId, buyorderExpenseDto.getBuyorderExpenseNo());
                break;
            }
            BuyorderExpenseDetailDto buyorderExpenseDetailDto = buyorderExpenseDto.getBuyorderExpenseDetailDto();
            List<OrderRemarkDto> orderRemarkDtoList = buyorderExpenseDetailDto.getOrderDesc();
            log.info("returnAlertBuyOrderExpenseGoodsShare 费用单 {} 的原订单说明 {}", buyorderExpenseDto.getBuyorderExpenseNo(), JSON.toJSONString(orderRemarkDtoList));
            // 此处从 数据库中拿到的 订单说明可能为 NULL, 此处 初始化一个 EMPTY LIST
            if (CollUtil.isEmpty(orderRemarkDtoList)) {
                orderRemarkDtoList = new ArrayList<>();
            }
            // 已预警数量
            Integer alreadyAlertNum = 0;
            log.info("returnAlertBuyOrderExpenseGoodsShare 开始计算 费用单 {} 对销售单ID {} 的商品 {} 已分摊的预警数量", buyorderExpenseDto.getBuyorderExpenseNo(), saleorderId, skuNo);
            for (OrderRemarkDto alreadyOrderRemark : orderRemarkDtoList) {
                if(BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE.equals(alreadyOrderRemark.getType())){
                    OriginalOrderDto alreadyOriginalOrder = alreadyOrderRemark.getOriginalOrderDto();
                    if(saleorderId.equals(alreadyOriginalOrder.getOrderId())){
                        List<OriginalOrderGoodsDto> alreadyGoodsDtos = alreadyOriginalOrder.getGoodsDtos();
                        for (OriginalOrderGoodsDto alreadyGoodsDto : alreadyGoodsDtos) {
                            if(skuNo.equals(alreadyGoodsDto.getSku())){
                                Integer alreadyY = alreadyGoodsDto.getY() == null ? 0 : alreadyGoodsDto.getY();
                                alreadyAlertNum = alreadyAlertNum + alreadyY;
                            }
                        }

                    }
                }
            }
            log.info("returnAlertBuyOrderExpenseGoodsShare 费用单 {} 对销售单ID {} 的商品 {} 已分摊的预警数量 {}", buyorderExpenseDto.getBuyorderExpenseNo(), saleorderId, skuNo, alreadyAlertNum);
            // 比较实际采购数量 和 已分摊的预警数量
            if(realNum.compareTo(alreadyAlertNum) <= 0){
                // 如果 realNum 小于 已分摊的预警数量
                realNum = realNum - alreadyAlertNum;
                log.info("returnAlertBuyOrderExpenseGoodsShare 费用单 {} 对销售单ID {} 的商品 {} 的实际采购数量 小于 已分摊的预警数量 不再进行分摊", buyorderExpenseDto.getBuyorderExpenseNo(), saleorderId, skuNo);
                break;
            }else {
                realNum = realNum - alreadyAlertNum;
                log.info("returnAlertBuyOrderExpenseGoodsShare 费用单 {} 对销售单ID {} 的商品 {} 的实际采购数量 小于 已分摊的预警数量 后的数量为 {}", buyorderExpenseDto.getBuyorderExpenseNo(), saleorderId, skuNo, realNum);
            }


            log.info("returnAlertBuyOrderExpenseGoodsShare 销售单 {} skuId {} 的售后单ID {}, 对 费用单 {} 触发退货预警", saleorder.getSaleorderNo(), skuId,afterSalesOrderId, buyorderExpenseDto.getBuyorderExpenseNo());
            OrderRemarkDto newOrderRemark = new OrderRemarkDto();
            newOrderRemark.setType(BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE);

            ResultOrderDto expenseOrder = new ResultOrderDto();
            expenseOrder.setOrderId(buyorderExpenseDto.getBuyorderExpenseId());
            expenseOrder.setNo(buyorderExpenseDto.getBuyorderExpenseNo());
            newOrderRemark.setResultOrderDto(expenseOrder);

            OriginalOrderDto originalSaleOrderDto = new OriginalOrderDto();
            originalSaleOrderDto.setOrderId(saleorderId);
            originalSaleOrderDto.setNo(saleorder.getSaleorderNo());
            originalSaleOrderDto.setAfterSalesOrderId(afterSalesOrderId);
            List<OriginalOrderGoodsDto> newGoodsDtoList = new ArrayList<>();
            OriginalOrderGoodsDto newGoodsDto = new OriginalOrderGoodsDto();
            newGoodsDto.setGoodsId(skuId);
            newGoodsDto.setSku(skuNo);
            // 设置退货数量 X
            newGoodsDto.setX(originNum);
            // 比较可采购数量 和 退货数量
            // 如果 退货数量 小于 可采购数量  则全部分摊到 该费用单中
            if (realNum.compareTo(num) > 0) {
                newGoodsDto.setY(num);
                num = 0;
            } else {
                newGoodsDto.setY(realNum);
                num -= realNum;
            }
            newGoodsDtoList.add(newGoodsDto);
            originalSaleOrderDto.setGoodsDtos(newGoodsDtoList);
            newOrderRemark.setOriginalOrderDto(originalSaleOrderDto);
            orderRemarkDtoList.add(newOrderRemark);

            // 更新预警状态
            updateBuyorderExpenseNoReturnEarlyWarn(buyorderExpenseDto.getBuyorderExpenseId(), BuyorderExpenseConstant.RETURN_EARLY_WARN);
            // 更新订单说明
            log.info("returnAlertBuyOrderExpenseGoodsShare 销售单 {} skuId {}, 对 费用单 {} ,更新预警说明 {}", saleorder.getSaleorderNo(), skuId, buyorderExpenseDto.getBuyorderExpenseNo(), JSON.toJSONString(orderRemarkDtoList));
            updateOrderDescByBuyorderExpenseIdAndList(buyorderExpenseDto.getBuyorderExpenseId(), orderRemarkDtoList);
            notifyBuyorderExpenseIdList.add(buyorderExpenseDto.getBuyorderExpenseId());
        }
        return notifyBuyorderExpenseIdList;
    }

    @Override
    public void freeReturnAlertBuyOrderExpenseGoods(Integer buyorderExpenseId) {
        log.info("freeReturnAlertBuyOrderExpenseGoods 解除全部预警,  data -----> {}", buyorderExpenseId);
        // 解除预警
        updateBuyorderExpenseNoReturnEarlyWarn(buyorderExpenseId, BuyorderExpenseConstant.NO_RETURN_EARLY_WARN);
        // 更新订单说明
        updateOrderDescByBuyorderExpenseIdAndList(buyorderExpenseId, Collections.emptyList());

    }

    @Override
    public void freePartReturnAlertBuyOrderExpenseGoods(ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarn) {
        log.info("freePartReturnAlertBuyOrderExpenseGoods 解除采购预警,  data -----> {}", JSON.toJSONString(expenseReturnEarlyWarn));
        // 根据采购费用单 ID 查询退货预警
        List<Integer> buyOrderExpenseIdList = expenseReturnEarlyWarn.getBuyorderExpenseIdList();
        Integer saleorderId = expenseReturnEarlyWarn.getSaleorderId();
        Integer skuId = expenseReturnEarlyWarn.getSkuId();
        log.info("freePartReturnAlertBuyOrderExpenseGoods 费用单ID列表 {} 解除 销售单ID {} 商品 {} 的预警", JSON.toJSONString(buyOrderExpenseIdList) ,saleorderId, skuId);

        List<BuyorderExpenseDto> buyOrderExpenseList = new ArrayList<>();
        for (Integer buyOrderExpenseId : buyOrderExpenseIdList) {
            BuyorderExpenseDto orderMainData = this.getOrderMainData(buyOrderExpenseId);
            buyOrderExpenseList.add(orderMainData);
        }
        for (BuyorderExpenseDto buyorderExpenseDto : buyOrderExpenseList) {
            BuyorderExpenseDetailDto buyorderExpenseDetailDto = buyorderExpenseDto.getBuyorderExpenseDetailDto();
            List<OrderRemarkDto> totalOrderDesc = buyorderExpenseDetailDto.getOrderDesc();
            log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 当前费用单 {} 的解除预警前的订单说明 {}", buyorderExpenseDto.getBuyorderExpenseId(), JSON.toJSONString(totalOrderDesc));
            // 如果 订单说明为空 直接返回 当前费用订单不用继续解除预警, 继续解除下个费用订单的预警情况
            if (CollUtil.isEmpty(totalOrderDesc)) {
                // 二次校验 如果 订单说明为空 时 预警状态自动解除
                if (!BuyorderExpenseConstant.NO_RETURN_EARLY_WARN.equals(buyorderExpenseDto.getIsReturnEarlyWarn())) {
                    log.info("freePartReturnAlertBuyOrderExpenseGoods 当前费用单 {} 的订单说明为空, 但仍在预警状态，自动解除预警", buyorderExpenseDto.getBuyorderExpenseId());
                    updateBuyorderExpenseNoReturnEarlyWarn(buyorderExpenseDto.getBuyorderExpenseId(), BuyorderExpenseConstant.NO_RETURN_EARLY_WARN);
                }
                break;
            }

            // 新预警信息
            List<OrderRemarkDto> newOrderDesc = new ArrayList<>();
            for (OrderRemarkDto orderRemarkDto : totalOrderDesc) {
                OriginalOrderDto originalSaleOrderDto = orderRemarkDto.getOriginalOrderDto();
                // 直接跳过非预警
                if (!BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE.equals(orderRemarkDto.getType())){
                    newOrderDesc.add(orderRemarkDto);
                    continue;
                }
                // 其他销售单直接放过
                if (!saleorderId.equals(originalSaleOrderDto.getOrderId())){
                    newOrderDesc.add(orderRemarkDto);
                    continue;
                }
                List<OriginalOrderGoodsDto> goodsDtos = originalSaleOrderDto.getGoodsDtos();
                List<OriginalOrderGoodsDto> newGoodsDtoList = new ArrayList<>();
                // 根据 SKU ID 获取新的商品列表
                for (OriginalOrderGoodsDto goodsDto : goodsDtos) {
                    // 判断SKU 和 SALEORDER ID
                    if(!skuId.equals(goodsDto.getGoodsId())){
                        newGoodsDtoList.add(goodsDto);
                    }
                }
                //如果不符合 则新 LIST 为空， 该条预警不会加入新预警
                if(!CollUtil.isEmpty(newGoodsDtoList)){
                    originalSaleOrderDto.setGoodsDtos(newGoodsDtoList);
                    newOrderDesc.add(orderRemarkDto);
                }
            }

            log.info("freePartReturnAlertBuyOrderExpenseGoods 当前费用单 {} 的订单说明 发生变化, 解除部分预警, 新订单说明为 {}",buyorderExpenseDto.getBuyorderExpenseId(), JSON.toJSONString(newOrderDesc));
            updateOrderDescByBuyorderExpenseIdAndList(buyorderExpenseDto.getBuyorderExpenseId(), newOrderDesc);
            // 如果没有预警信息 则说明预警已经全部解除
            // 查询出来过滤掉 type != 1 的数据
            List<OrderRemarkDto> orderDesc = newOrderDesc.stream().filter(o -> BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE.equals(o.getType())).collect(Collectors.toList());
            if (CollUtil.isEmpty(orderDesc)) {
                // 二次校验 如果 订单说明为空 时 预警状态自动解除
                if (!BuyorderExpenseConstant.NO_RETURN_EARLY_WARN.equals(buyorderExpenseDto.getIsReturnEarlyWarn())) {
                    log.info("freePartReturnAlertBuyOrderExpenseGoods 当前费用单 {} 的订单说明为空, 但仍在预警状态，自动解除预警", buyorderExpenseDto.getBuyorderExpenseId());
                    updateBuyorderExpenseNoReturnEarlyWarn(buyorderExpenseDto.getBuyorderExpenseId(), BuyorderExpenseConstant.NO_RETURN_EARLY_WARN);
                }
            }
        }

    }

    @Override
    public void freePartReturnAlertBuyOrderExpenseGoodsByCompute(ExpenseReturnEarlyWarnEntity expenseReturnEarlyWarn){
        log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 解除采购预警,  data -----> {}", JSON.toJSONString(expenseReturnEarlyWarn));
        // 根据采购费用单 ID 查询退货预警
        List<Integer> buyOrderExpenseIdList = expenseReturnEarlyWarn.getBuyorderExpenseIdList();
        Integer saleorderId = expenseReturnEarlyWarn.getSaleorderId();
        Integer saleorderGoodsId = expenseReturnEarlyWarn.getSaleorderGoodsId();
        Integer afterSalesOrderId = expenseReturnEarlyWarn.getAfterSalesOrderId();
        // 费用售后 自主退货数量
        Integer buyOrderExpenseAfterNum = expenseReturnEarlyWarn.getNum() == null ? 0 : expenseReturnEarlyWarn.getNum();
        Integer originNum = buyOrderExpenseAfterNum;
        Integer skuId = expenseReturnEarlyWarn.getSkuId();

        List<BuyorderExpenseDto> buyOrderExpenseList = new ArrayList<>();

        for (Integer buyOrderExpenseId : buyOrderExpenseIdList) {
            BuyorderExpenseDto orderMainData = this.getOrderMainData(buyOrderExpenseId);
            buyOrderExpenseList.add(orderMainData);
        }

        for (BuyorderExpenseDto buyorderExpenseDto : buyOrderExpenseList) {
            BuyorderExpenseDetailDto buyorderExpenseDetailDto = buyorderExpenseDto.getBuyorderExpenseDetailDto();
            Integer buyorderExpenseId = buyorderExpenseDetailDto.getBuyorderExpenseId();
            List<OrderRemarkDto> totalOrderDesc = buyorderExpenseDetailDto.getOrderDesc();
            log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 当前费用单 {} 的解除预警前的订单说明 {}", buyorderExpenseDto.getBuyorderExpenseId(), JSON.toJSONString(totalOrderDesc));
            // 如果 订单说明为空 直接返回 当前费用订单不用继续解除预警, 继续解除下个费用订单的预警情况
            if (CollUtil.isEmpty(totalOrderDesc)) {
                // 二次校验 如果 订单说明为空 时 预警状态自动解除
                if (!BuyorderExpenseConstant.NO_RETURN_EARLY_WARN.equals(buyorderExpenseDto.getIsReturnEarlyWarn())) {
                    log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 当前费用单 {} 的订单说明为空, 但仍在预警状态，自动解除预警", buyorderExpenseDto.getBuyorderExpenseId());
                    updateBuyorderExpenseNoReturnEarlyWarn(buyorderExpenseDto.getBuyorderExpenseId(), BuyorderExpenseConstant.NO_RETURN_EARLY_WARN);
                }
                break;
            }
            // 新预警信息
            List<OrderRemarkDto> newOrderDesc = new ArrayList<>();

            if(afterSalesOrderId == null){
                log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 采购费用售后 商品 {} 触发解除 费用单 {} 退货预警, 待分摊数量 {}",skuId, buyorderExpenseId, buyOrderExpenseAfterNum);
                if(buyOrderExpenseAfterNum.compareTo(0) <= 0){
                    log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 采购费用售后 商品 {} 触发解除 费用单 {} 退货预警, 分摊数量为 0 不再分摊",skuId, buyorderExpenseId);
                    break;
                }
                // 拿到采购费用单 自主退货的 数量以后， 对预警中的数量进行扣减
                for (OrderRemarkDto orderRemarkDto : totalOrderDesc) {
                    OriginalOrderDto originalOrderDto = orderRemarkDto.getOriginalOrderDto();
                    if(BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE.equals(orderRemarkDto.getType())){
                        List<OriginalOrderGoodsDto> goodsDtos = originalOrderDto.getGoodsDtos();
                        // 其他预警直接放过 不做分摊
                        if(!saleorderId.equals(originalOrderDto.getOrderId())){
                            newOrderDesc.add(orderRemarkDto);
                            continue;
                        }
                        // 新订单说明中的商品 判断 SKU
                        List<OriginalOrderGoodsDto> newGoodsDtosList = new ArrayList<>();
                        for (OriginalOrderGoodsDto goodsDto : goodsDtos) {
                            // 退货数量
                            Integer x = goodsDto.getX() == null ? 0 : goodsDto.getX();
                            // 预警数量
                            Integer y = goodsDto.getY() == null ? 0 : goodsDto.getY();
                            String sku = goodsDto.getSku();
                            Integer goodsId = goodsDto.getGoodsId();
                            if(skuId.equals(goodsId)){
                                log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 采购费用售后 商品 {} 触发解除 费用单 {} 退货预警, 开始扣减预警数量, 原预警数量 {}, 待分摊数量 {}",skuId, buyorderExpenseId, y, buyOrderExpenseAfterNum);
                                // 比较 自主退货数量 和 预警数量
                                // 如果 自主退货数量 小于 预警数量 把 预警数量 减去退货数量
                                // 如果 自主退货数量 大于等于 预警数量 把 预警数量 减去退货数量
                                if(buyOrderExpenseAfterNum.compareTo(y) < 0){
                                    Integer alertNum = y - buyOrderExpenseAfterNum;
                                    goodsDto.setY(alertNum);
                                    newGoodsDtosList.add(goodsDto);
                                    buyOrderExpenseAfterNum = 0;
                                    log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 采购费用售后 商品 {} 触发解除 费用单 {} 退货预警, 扣减预警数量, 扣减后预警数量 {} 分摊完成",skuId, buyorderExpenseId, alertNum);
                                }else {
                                    buyOrderExpenseAfterNum -= y;
                                    log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 采购费用售后 商品 {} 触发解除 费用单 {} 退货预警, 扣减预警数量, 预警数量扣完, 待分摊数量 {}",skuId, buyorderExpenseId,buyOrderExpenseAfterNum);
                                }
                            }else{
                                newGoodsDtosList.add(goodsDto);
                            }
                        }
                        // 如果有商品 则说明 这个商品 还有预警 需要加入 订单说明
                        if(!CollUtil.isEmpty(newGoodsDtosList)){
                            originalOrderDto.setGoodsDtos(newGoodsDtosList);
                            newOrderDesc.add(orderRemarkDto);
                        }
                    }else{
                        newOrderDesc.add(orderRemarkDto);
                    }
                }
            }else {
                log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 销售单ID {} 对应的 销售售后单ID {} 关闭 触发解除 费用单 {} 退货预警", saleorderId, afterSalesOrderId, buyorderExpenseId);
                for (OrderRemarkDto orderRemarkDto : totalOrderDesc) {
                    OriginalOrderDto originalOrderDto = orderRemarkDto.getOriginalOrderDto();
                    if(!BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE.equals(orderRemarkDto.getType()) || !afterSalesOrderId.equals(originalOrderDto.getAfterSalesOrderId())){
                        newOrderDesc.add(orderRemarkDto);
                    }
                }
            }

            log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 当前费用单 {} 的订单说明 发生变化, 解除部分预警, 新订单说明为 {}",buyorderExpenseDto.getBuyorderExpenseId(), JSON.toJSONString(newOrderDesc));
            updateOrderDescByBuyorderExpenseIdAndList(buyorderExpenseDto.getBuyorderExpenseId(), newOrderDesc);
            // 查询出来过滤掉 type != 1 的数据
            List<OrderRemarkDto> orderDesc = newOrderDesc.stream().filter(o -> BuyorderExpenseConstant.ORDER_REMARK_TYPE_EXPENSE.equals(o.getType())).collect(Collectors.toList());
            // 如果 预警的订单说明为空 直接返回 当前费用订单不用继续解除预警
            if (CollUtil.isEmpty(orderDesc)) {
                // 二次校验 如果 订单说明为空 时 预警状态自动解除
                if (!BuyorderExpenseConstant.NO_RETURN_EARLY_WARN.equals(buyorderExpenseDto.getIsReturnEarlyWarn())) {
                    log.info("freePartReturnAlertBuyOrderExpenseGoodsNew 当前费用单 {} 的订单说明为空, 但仍在预警状态，自动解除预警", buyorderExpenseDto.getBuyorderExpenseId());
                    updateBuyorderExpenseNoReturnEarlyWarn(buyorderExpenseDto.getBuyorderExpenseId(), BuyorderExpenseConstant.NO_RETURN_EARLY_WARN);
                }
            }

        }
    }


    /**
     * 更新 采购费用单ID 的 订单说明
     *
     * @param buyorderExpenseId  采购费用单ID
     * @param orderRemarkDtoList 订单说明
     */
    public void updateOrderDescByBuyorderExpenseIdAndList(Integer buyorderExpenseId, List<OrderRemarkDto> orderRemarkDtoList) {
        // 更新订单说明
        BuyorderExpenseDetailDto updateBuyorderExpenseOrderDescDto = new BuyorderExpenseDetailDto();
        updateBuyorderExpenseOrderDescDto.setOrderDesc(orderRemarkDtoList);
        updateBuyorderExpenseOrderDescDto.setBuyorderExpenseId(buyorderExpenseId);
        BuyorderExpenseDetailEntity updateBuyorderExpenseOrderDescEntity = buyorderExpenseDetailConvertor.toEntity(updateBuyorderExpenseOrderDescDto);
        buyorderExpenseDetailMapper.updateOrderDescByBuyorderExpenseId(updateBuyorderExpenseOrderDescEntity);
    }

    /**
     * 更新费用单 退货预警状态
     *
     * @param buyorderExpenseId     采购费用单ID
     * @param returnEarlyWarnStatus 退货预警状态
     */
    public void updateBuyorderExpenseNoReturnEarlyWarn(Integer buyorderExpenseId, Integer returnEarlyWarnStatus) {
        BuyorderExpenseEntity buyorderExpenseEntity = new BuyorderExpenseEntity();
        buyorderExpenseEntity.setBuyorderExpenseId(buyorderExpenseId);
        buyorderExpenseEntity.setIsReturnEarlyWarn(returnEarlyWarnStatus);
        buyorderExpenseMapper.updateByPrimaryKeySelective(buyorderExpenseEntity);
    }


    @Override
    public void getNeedBuyNum(List<BuyOrderSaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos) {
        //查询已采购数量
        log.info("开始查询已采购数量:{}", saleOrderGoodsDetailDtos);
        List<RBuyorderExpenseJSaleorderDto> saleorderHistoryBuyNumList = rBuyorderExpenseJSaleorderMapper.historyBuyNum(saleOrderGoodsDetailDtos);
        //查询虚拟商品销售退货数量
        log.info("开始查询销售退货数量:{}", saleOrderGoodsDetailDtos);
        List<RBuyorderExpenseJSaleorderDto> saleorderAfterSaleNumList = afterSaleOrder2ExpenseApiService.saleorderAfterSaleNum(saleOrderGoodsDetailDtos);
        //查询虚拟商品采购售后数量
        log.info("开始查询采购退货数量:{}", saleOrderGoodsDetailDtos);
        List<RBuyorderExpenseJSaleorderDto> expenseBuyorderAfterSaleNumList = rBuyorderExpenseJSaleorderMapper.expenseBuyorderAfterSaleNum(saleOrderGoodsDetailDtos);
        log.info("开始计数虚拟商品需采数量！");
        for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : saleOrderGoodsDetailDtos) {
            if (saleorderAfterSaleNumList.size() > 0) {
                for (RBuyorderExpenseJSaleorderDto saleorderAfterSale : saleorderAfterSaleNumList) {
                    if (saleOrderGoodsDetailDto.getSaleorderGoodsId().equals(saleorderAfterSale.getSaleorderGoodsId())) {
                        saleOrderGoodsDetailDto.setNum(saleOrderGoodsDetailDto.getNum() - saleorderAfterSale.getNum());
                        break;
                    }
                }
            }
            if (saleorderHistoryBuyNumList.size() > 0) {
                for (RBuyorderExpenseJSaleorderDto buyHistory : saleorderHistoryBuyNumList) {
                    if (saleOrderGoodsDetailDto.getSaleorderGoodsId().equals(buyHistory.getSaleorderGoodsId())) {
                        saleOrderGoodsDetailDto.setNum(saleOrderGoodsDetailDto.getNum() - buyHistory.getNum());
                        break;
                    }
                }
            }
            if (expenseBuyorderAfterSaleNumList.size() > 0) {
                for (RBuyorderExpenseJSaleorderDto buyOrderAfter : expenseBuyorderAfterSaleNumList) {
                    if (saleOrderGoodsDetailDto.getSaleorderGoodsId().equals(buyOrderAfter.getSaleorderGoodsId())) {
                        saleOrderGoodsDetailDto.setNum(saleOrderGoodsDetailDto.getNum() + buyOrderAfter.getNum());
                        break;
                    }
                }
            }
        }

    }

    @Override
    public void getExpenseBuyNum(List<BuyOrderSaleOrderGoodsDetailDto> esgList, List<RBuyorderExpenseJSaleorderDto> buyOrderExpenseJSaleList, String page) {
        if (esgList.size() > 0) {
            getNeedBuyNum(esgList);
            switch (page) {
                case "edit":
                    for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : esgList) {
                        buyOrderExpenseJSaleList.forEach(o -> {
                            if (o.getSaleorderGoodsId().equals(saleOrderGoodsDetailDto.getSaleorderGoodsId())) {
                                saleOrderGoodsDetailDto.setBuyorderExpenseJSaleorderId(o.getTRBuyorderExpenseJSaleorderId());
                                saleOrderGoodsDetailDto.setBuyNum(Math.max(saleOrderGoodsDetailDto.getNum() + o.getNum(), 0));//计算需采数量用
                                saleOrderGoodsDetailDto.setNum(o.getNum());//采购数量
                            }
                        });
                    }
                    break;
                case "detail":
                    for (BuyOrderSaleOrderGoodsDetailDto saleOrderGoodsDetailDto : esgList) {
                        buyOrderExpenseJSaleList.forEach(o -> {
                            if (o.getSaleorderGoodsId().equals(saleOrderGoodsDetailDto.getSaleorderGoodsId())) {
                                saleOrderGoodsDetailDto.setBuyorderExpenseJSaleorderId(o.getTRBuyorderExpenseJSaleorderId());
                                saleOrderGoodsDetailDto.setBuyNum(Math.max(saleOrderGoodsDetailDto.getNum(), 0));//计算需采数量用
                                saleOrderGoodsDetailDto.setNum(o.getNum());//采购数量
                            }
                        });
                    }
                    break;
            }
        }
    }

    @Override
    public List<BuyOrderSaleOrderGoodsDetailDto> waitBuyExpenseNeed(List<Integer> saleorderIds) {
        log.info("获取虚拟商品销售详情信息");
        List<Integer> expenseSaleorderIds = rBuyorderExpenseJSaleorderMapper.findExpenseSaleorderIds(saleorderIds);
        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsApiService.getRelatedDetail(expenseSaleorderIds);
        List<BuyOrderSaleOrderGoodsDetailDto> esgList = saleOrderGoodsDetailDtos.stream().map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());
        if (esgList.size() > 0) {
            getNeedBuyNum(esgList);
        }
        return esgList;
    }

    @Override
    public Integer getReturnEarlyWarnByBuyorderExpenseId(Integer buyorderExpenseId) {
        BuyorderExpenseEntity buyorderExpenseEntity = buyorderExpenseMapper.selectByPrimaryKey(buyorderExpenseId);
        return buyorderExpenseEntity.getIsReturnEarlyWarn();
    }

    @Override
    public List<RBuyorderExpenseJSaleorderDto> findByBuyorderExpenseId(Integer buyorderExpenseId) {
        return rBuyorderExpenseJSaleorderMapper.findByBuyorderExpenseId(buyorderExpenseId);
    }

    @Override
    public BuyorderExpenseDto getBuyOrderExpenseByBuyorderExpenseNo(String buyorderExpenseNo) {
        BuyorderExpenseEntity buyOrderExpense = buyorderExpenseMapper.getBuyorerExpenseByOrderNo(buyorderExpenseNo);
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseConvertor.toDto(buyOrderExpense);
        List<BuyorderExpenseItemEntity> buyorderExpenseItemEntityList = buyorderExpenseItemMapper.getByBuyorderExpenseId(buyOrderExpense.getBuyorderExpenseId());
        List<BuyorderExpenseItemDto> dtoList = buyorderExpenseItemConvertor.toDto(buyorderExpenseItemEntityList);
        dtoList.forEach(d -> d.setSku(buyorderExpenseItemDetailMapper.selectByPrimaryKey(d.getBuyorderExpenseItemId()).getSku()));
        buyorderExpenseDto.setBuyorderExpenseItemDtos(dtoList);
        return buyorderExpenseDto;
    }

}
