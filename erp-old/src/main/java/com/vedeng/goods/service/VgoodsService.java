package com.vedeng.goods.service;

import com.vedeng.activiti.model.HistoryVerfiyRecord;
import com.vedeng.authorization.model.User;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.docSync.model.pojo.generate.DocOfGoodsDo;
import com.vedeng.goods.command.SkuAddCommand;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.command.SpuSearchCommand;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.CoreSkuGenerate;
import com.vedeng.goods.model.CoreSpuGenerate;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.goods.model.dto.SpuUpdatedResultDto;
import com.vedeng.goods.model.dto.VirtualSkuDto;
import com.vedeng.goods.model.vo.BaseAttributeVo;
import com.vedeng.goods.model.vo.CoreSkuBaseVO;
import com.vedeng.goods.model.vo.CoreSpuBaseVO;
import com.vedeng.goods.model.vo.GoodsStorageConditionVo;
import com.vedeng.op.api.dto.sku.SkuOnSaleDto;
import org.activiti.engine.task.Task;
import org.springframework.ui.Model;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface VgoodsService {


	CoreSkuGenerate getSkuDoBySkuId(Integer skuId);

	void updateSpuOperateInfoFlag(Integer optInfoId,Integer spuId);
	void updateSkuOperateInfoFlag(Integer optInfoId,Integer skuId);
	/**
	 * SKU每页的个数
	 */
	final static int MAX_SKU_PAGE_SIZE = 5;

	/**
	 * 修改Spu
	 * 
	 * @param spuCommand
	 * @param goodsStorageConditionVo spu的存储条件
	 * @throws ShowErrorMsgException
	 */
	SpuUpdatedResultDto saveSpu(HttpServletRequest request, SpuAddCommand spuCommand, final GoodsStorageConditionVo goodsStorageConditionVo) throws ShowErrorMsgException;

	/**
	 * spu列表
	 * 
	 * @param spuCommand
	 * @param page
	 */
	List<CoreSpuBaseVO> selectSpuListPage(SpuSearchCommand spuCommand, Page page) throws Exception;

	/**
	 * SPU详情页初始化
	 * 
	 * @param spuCommand
	 * @throws ShowErrorMsgException
	 */
	void initSpu(SpuAddCommand spuCommand) throws Exception;

	/**
	 * SKU详情页初始化
	 * 
	 * @param skuAddCommand
	 * @param editable 是否为Sku编辑页面
	 * @throws ShowErrorMsgException
	 */
	CoreSkuGenerate initSku(SkuAddCommand skuAddCommand , boolean editable) throws Exception;

	/**
	 * 提交审核操作
	 * 
	 * @param spuCommand
	 * @throws ShowErrorMsgException
	 */
	void submitToCheck(SpuAddCommand spuCommand) throws ShowErrorMsgException;

	/**
	 * 审核商品
	 * 
	 * @param spuCommand
	 * @throws ShowErrorMsgException
	 */
	void checkSpu(HttpServletRequest request,SpuAddCommand spuCommand) throws ShowErrorMsgException;

	/**
	 * 按照商品的审核状态查询商品
	 * 
	 * @param checkStatus
	 * @return
	 */
	Integer countSpuByCheckStatus(Integer checkStatus);

	/**
	 * 删除spu
	 * 
	 * @throws ShowErrorMsgException
	 */
	void deleteSpu(SpuSearchCommand spuCommand) throws ShowErrorMsgException;

	/**
	 * 删除sku
	 * 
	 * @throws ShowErrorMsgException
	 */
	void deleteSku(SpuSearchCommand spuCommand) throws ShowErrorMsgException;

	/**
	 * ajax获取sku
	 * 
	 * @param page
	 * @return
	 * @throws Exception
	 */
	List<CoreSkuBaseVO> selectSkuListPage(SpuSearchCommand spuCommand,  Page page) throws Exception;

	/**
	 * 导出
	 * 
	 * @param spuIds
	 * @return
	 * @throws Exception
	 */
	List<CoreSpuBaseVO> exportSpuList(String spuIds) throws Exception;

	/**
	 *获取所有spu下待选择的属性与属性值
	 * @param spuId
	 * @return
	 */
	List<BaseAttributeVo> selectAllAttributeBySkuId(Integer spuId,Integer skuId) ;


	void saveSku(SkuAddCommand command, CoreSkuGenerate skuGenerate) throws UnsupportedEncodingException;

    List<BaseAttributeVo> getAttributeInfoByCategoryId(Integer categoryId);

    List<LogCheckGenerate> listSpuCheckLog(Integer spuId);

    void saveTempSku(SkuAddCommand spuCommand);


	List<Map<String, Object>>  searchFirstEngageListPage(String searchValue, Page page);

	/**
	 * 审核sku
	 * @param command
	 */
	void checkSku(HttpServletRequest request, SkuAddCommand command);

	List<LogCheckGenerate> listSkuCheckLog(Integer skuId);

	/**
	 * 获取商品附件并转为FileInfo对象列表
	 *
	 * @param attacheType
	 * @param goodsId
	 * @return
	 */
	List<FileInfo> listGoodsAttachmentAsFileInfo(Integer attacheType, Integer goodsId);

	/**
	 * 批量设置备货
	 * @param command
	 */
    void backupSku(SkuAddCommand command);

	/**
	 * 分类修改之后修改spu的状态
	 * @param categoryId
	 */
	void changeSpuStatusByCategoryChange(HttpServletRequest request, Integer categoryId, User user);

    List<Map<String, Object>> searchSkuWithDepartment(String skuName);

	/**
	 * 查询sku信息，仅限页面展示，会缓存5分钟，请不要使用在其他地方
	 * @param skuId
	 * @return
	 */
    Map<String,Object> skuTip(Integer skuId);

	/**
	 *获取订单的某个商品的成本价
	 * @param orderDetailId
	 * @return
	 */
    Map<String, Object> getCostPrice(Integer orderDetailId);
	List<Map<String, Object>> skuTipList(List<Integer> skuIds);

	List<CoreSpuGenerate> findSpuNamesBySpuIds(List<Integer> goodsIds);

	/**
	 * @description: .
	 * @jira: VDERP-2217 提供预计发货时间给前台.
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2020/5/15 5:00 下午.
	 * @author: Tomcat.Hui.
	 * @param skuNoList: .
	 * @return: java.util.List<com.vedeng.goods.model.CoreSkuGenerate>.
	 * @throws: .
	 */
	List<CoreSkuGenerate> getSkuListByNo(List<String> skuNoList);

	/**
	 * @description: 更新填报预计可发货时间.
	 * @jira: VDERP-2217 提供预计发货时间给前台.
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2020/5/15 8:36 下午.
	 * @author: Tomcat.Hui.
	 * @param skuList: .
	 * @return: java.lang.Integer.
	 * @throws: .
	 */
	Integer updateSkuDeliveryRange(List<List<CoreSkuGenerate>> skuList);

	/**
	 * <b>Description:</b>更新sku的上架状态<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/7/28
	 */
	Integer updateSkuOnSale(SkuOnSaleDto dto);
	/**
	 * <b>Description:</b>更新sku推送状态<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/7/30
	 */
	Integer updateSkuPushStatus(SkuOnSaleDto dto);

	CoreSpuGenerate findSpuInfoBySkuNo(String skuNo);

	Integer getSpuIdBySkuId(Integer skuId);

    Integer getSkuIdBySkuNo(String skuNo);

	List<Map<String, Object>> skuTipListInfo(List<Integer> quoteorderGoodsIds);


	/**
	 * 获取关联首营信息的spu的数量
	 *
	 * @param firstEngageId
	 * @return
	 */
	Integer countSpuByFirstEngageId(Integer firstEngageId);

	/**
	 * 根据skuId获取商品报备信息
	 * @param skuId
	 * @return
	 */
	CoreSkuGenerate getSkuAuthotizationInfoBySku(Long skuId);

	/**
	 * 批量维护SKU报备信息
	 *
	 * @param skuIds
	 * @param coreSkuGenerate
	 * @return
	 */
	Integer batchSaveSkuAuthorization(List<Integer> skuIds, CoreSkuGenerate coreSkuGenerate);


	List<Integer> listSkuNameForSaleContract(Collection<Integer> skuIdList);

	/**
	 * 首营、SPU、SKU审核日志生成
	 * @param logBizId 审核日志涉及的业务Id
	 * @param checkStatus 审核状态
	 * @param logMessage 日志备注
	 * @param logType 日志类型，LogTypeEnum
	 */
	void generateCheckLogAndSave(Integer logBizId, Integer checkStatus, String logMessage, Integer logType);

	/**
	 * 获取SKU推送相关信息
	 *
	 * @param skuId
	 * @return
	 */
	CoreSkuGenerate getPushInfoBySkuId(Integer skuId);

    /**
     * 获取Spu下审核通过的sku
	 * (2020/12/24 应ken要求新增规则 D级商品在SPU审核通过之后不修改推送状态并且不推送)
     *
     * @param spuId
     * @return
     */
	List<SkuAddCommand> getValidedSkuInfoBySpuId(Integer spuId);

	/**
	 * 获取sku的归属责任人
	 *
	 * @param skuId
	 * @return
	 */
	List<User> getAssignmentersBySkuId(Integer skuId);

	/**
	 * 判断spu下的sku是否推送过OP
	 *
	 * @param spuId
	 * @return
	 */
	boolean isPushedSpuBySpuId(Integer spuId);


	List<BaseAttributeVo> selectAllAttributeBySpuId(Integer spuId);

	CoreSkuGenerate selectSkuConfigBySkuId(Integer skuId);

	DocOfGoodsDo getDocOfGoodsBySkuId(Integer skuId);

	/**
	 * SKU/SPU启用/禁用
	 * @param type 判断是sku还是spu
	 * @param id skuId/spuId
	 * @param status  需要变更为的状态 0 禁用， 1 启用
	 * @param reason 禁用原因
	 */
	void enableSkuOrSpu(Integer type, Integer id, Integer status, String reason) throws ParseException;

    /**
     * 向其他平台推送SKU信息 （VDERP-7794 接口入口增加，因此将此方法从controller移到service层便于调用）
     *
     * @param platfromIds
     * @param skuId
     * @param spuId
     * @return
     */
	boolean pushSkuInfo(String platfromIds, Integer skuId, Integer spuId, String pushOrgIdList);

	/**
	 * 商品禁用审核操作
	 * @param taskId
	 * @param userId
	 * @param comment
	 * @param pass
	 */
	void completeDisableTask(String taskId, Integer userId, String comment, Boolean pass) throws ParseException;

	/**
	 * 商品禁用开启审核
	 * @param relatedId
	 * @param disableReason
	 * @param userId
	 * @param goodsType
	 */
	void disableGoodsApplyVerify(Integer relatedId, String disableReason, Integer userId, Integer goodsType);

	/**
	 * 获取当前商品禁用task
	 * @param relatedId
	 * @param goodsType
	 * @return
	 */
	Task getDisableGoodsTask(Integer relatedId, Integer goodsType);

	/**
	 * 获取商品禁用审核记录
	 * @param user
	 * @param related
	 * @param goodsType
	 */
	List<HistoryVerfiyRecord> getDisableGoodsRecord(Integer related, Integer goodsType, User user);

	/**
	 * 启用商品
	 * @param relatedId
	 * @param userId
	 * @param goodsType
	 */
	void enbleGoodsApplyVerify(Integer relatedId, Integer userId, Integer goodsType) throws ParseException;

	/**
	 * 判断当前用户是否属于任务候选人
	 * @param taskId
	 * @param user
	 * @return
	 */
	Boolean getTaskCandidate(String taskId, User user);

	/**
	 * 查询sku对应的采购到货时长
	 */
	Map<String, Integer> getPurchaseTime(List<String> skuNoList);

	/**
	 * 查询cityId、areaLevel对应的物流发货时长
	 */
	Map<String, Integer> getDeliverTime(List<String> skuNoList, Integer cityId, Integer areaLevel);

    /**
     * 获取全部Sku
     */
    List<String> getAllSkuNo();

	/**
	 * 获取sku已经推送过的 区域商城id
	 */
	String getPushedOrgIdList(Integer skuId);


	/**
	 * <b>Description:</b>获取上架状态字符串<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/7/30
	 */
	String getSaleStr(Integer pushStatus, Integer onSale);



	/**
	 * 根据orgName补充sku 区域商城名称集合信息
	 * @param model 视图
	 * @param skuGenerate sku信息
	 */
	void getSkuOrgName(Model model,CoreSkuGenerate skuGenerate);

	/**
	 * 近一年有效成交数据
	 * @param model 视图
	 * @param skuGenerate sku
	 * @param type
	 */
	void getOneYearEffectiveTransactionData(Model model,CoreSkuGenerate skuGenerate,Integer type);

	/**
	 * 初始化sku
	 * @param model 视图
	 * @param command 参数
	 */
	void initSkuPage(Model model, SkuAddCommand command);


	/**
	 * 获取报备信息
	 *
	 * @param model 视图
	 * @param skuId skuId
	 */
	void getAuthorizationInfo(Model model, Integer skuId);

	/**
	 * 获取商品审核信息异常
	 *
	 * @param model 视图
	 * @param skuId skuId
	 * @param sessUser 登录用户
	 */
	void getGoodsAuditInfo(Model model, Integer skuId, User sessUser);

	/**
	 * 根据不同页面类型返回数据
	 * 0 新商品流原页面 1 商品整合查询默认页面 2 新商品流中整合页面
	 *
	 * @param model 视图
	 * @param pageType  页面类型
	 * @param skuGenerate sku
	 * @return 跳转路径
	 */
	String returnView(Model model, String pageType, CoreSkuGenerate skuGenerate);

	/**
	 * @desc 根据主键Id更新商品信息
	 * <AUTHOR>
	 * @param coreSku
	 */
	void updateByPrimaryKeySelective(CoreSku coreSku);

	/**
	 * @desc 保存新增虚拟商品
	 * @param virtualSkuDto
	 * @param user
	 * @return
	 */
	ResultInfo saveVirtureSku(VirtualSkuDto virtualSkuDto, User user);

	CoreSkuGenerate getSkuGoodesUnrealfoBySku(Long relatedId);

	void updateVirtureSku(VirtualSkuDto virtualSkuDto,User user);

	/**
	 * 获取启用的虚拟商品 SKU ID 列表
	 * @return 虚拟商品 SKU ID 列表
	 */
	List<Integer> getVirtureSkuIdList();

	String getTaxCategoryNo(Integer goodsId);

	/**
	 * 根据关键字搜索SKU 100条
	 * @param keyword
	 * @return
	 */
    List<CoreSku> searchSku(String keyword);
}
