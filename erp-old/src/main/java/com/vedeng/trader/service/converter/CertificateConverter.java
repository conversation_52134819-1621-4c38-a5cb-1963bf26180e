package com.vedeng.trader.service.converter;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.manufacturer.constants.ManufacturerConstants;
import com.vedeng.goods.manufacturer.dto.TransCertificateDto;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.system.model.Attachment;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/10 22:31
 */
public class CertificateConverter {


    /**
     *  覆盖过程中，首营-生产企业资质中的部分参数为空时，不覆盖供应商原有资质信息
     * @param haveBase 生产企业编辑是否有基本信息的编辑
     * @param attachments 文件
     * @return
     */
    private static Integer buildSyncStatus(boolean haveBase, List<Attachment> attachments) {
        if (CollectionUtils.isEmpty(attachments)) {
            // 不同步文件
            if (!haveBase) {
                // 为空时 不覆盖
                return ManufacturerConstants.NOT_SYNC;
            }else {
                // 只覆盖base,没有则新增
                return ManufacturerConstants.SYNC_BASE_NOT_FILE;
            }
        }

        return ManufacturerConstants.SYNC_FILE;
    }


    /**
     *  营业执照
     * @param manufacturer
     * @param traderCertificateMapByType
     * @return
     */
    public static TransCertificateDto convertBusiness(Manufacturer manufacturer, Map<Integer, List<TraderCertificateVo>> traderCertificateMapByType) {
        List<TraderCertificateVo> businessVo = traderCertificateMapByType.get(SysOptionConstant.ID_25);

        TraderCertificateVo traderCertificateVo = new TraderCertificateVo();

        if (CollectionUtils.isNotEmpty(businessVo)) {
            traderCertificateVo = businessVo.get(0);
        }

        if ((manufacturer.getBcStartTime() != null) && manufacturer.getBcStartTime().getTime()>0) {
            traderCertificateVo.setBegintime(manufacturer.getBcStartTime().getTime());
        }
        if (manufacturer.getBcEndTime() != null && manufacturer.getBcEndTime().getTime()>0) {
            traderCertificateVo.setEndtime(manufacturer.getBcEndTime().getTime());
        }
        if (manufacturer.getBcIssueDate() != null && manufacturer.getBcIssueDate().getTime()>0) {
            traderCertificateVo.setIssueDate(manufacturer.getBcIssueDate().getTime());
        }

        boolean isSyncBase = manufacturer.getBcStartTime() != null
                || manufacturer.getBcEndTime() != null
                || manufacturer.getBcIssueDate() != null;

        List<Attachment> yzAttachments = manufacturer.getYzAttachments();

        TransCertificateDto transCertificateDto = new TransCertificateDto();
        transCertificateDto.setSyncType(buildSyncStatus(isSyncBase, yzAttachments));
        transCertificateDto.setBaseInfo(traderCertificateVo);
        transCertificateDto.setSourceList(yzAttachments);
        transCertificateDto.setSysOptionDefinitionId( SysOptionConstant.ID_25);
        transCertificateDto.setOldInfoList(businessVo);

        return transCertificateDto;
    }


    /**
     *  第一类医疗器械生产备案凭证
     * @param manufacturer
     * @param traderCertificateMapByType
     * @return
     */
    public static TransCertificateDto convertRecordCertificate(Manufacturer manufacturer, Map<Integer, List<TraderCertificateVo>> traderCertificateMapByType) {
        List<TraderCertificateVo> businessVo = traderCertificateMapByType.get(SysOptionConstant.ID_1101);

        TraderCertificateVo traderCertificateVo = new TraderCertificateVo();

        if (CollectionUtils.isNotEmpty(businessVo)) {
            traderCertificateVo = businessVo.get(0);
        }

        if (manufacturer.getRcStartTime() != null && manufacturer.getRcStartTime().getTime()>0 ) {
            traderCertificateVo.setBegintime(manufacturer.getRcStartTime().getTime());
        }

        if (manufacturer.getRcEndTime() != null && manufacturer.getRcEndTime().getTime()>0 ) {
            traderCertificateVo.setEndtime(manufacturer.getRcEndTime().getTime());
        }

        traderCertificateVo.setRecordNo(StringUtil.isNotBlank(manufacturer.getRecordCertificateLicence()) ? manufacturer.getRecordCertificateLicence() : traderCertificateVo.getRecordNo());

        boolean isSyncBase = manufacturer.getRcStartTime() != null
                || manufacturer.getRcEndTime() != null
                || manufacturer.getRecordCertificateLicence() != null;

        List<Attachment> rcAttachments = manufacturer.getRcAttachments();

        TransCertificateDto transCertificateDto = new TransCertificateDto();
        transCertificateDto.setSyncType(buildSyncStatus(isSyncBase, rcAttachments));
        transCertificateDto.setBaseInfo(traderCertificateVo);
        transCertificateDto.setSourceList(rcAttachments);
        transCertificateDto.setSysOptionDefinitionId( SysOptionConstant.ID_1101);
        transCertificateDto.setOldInfoList(businessVo);

        return transCertificateDto;
    }


    /**
     *  生产企业生产产品登记表
     * @param manufacturer
     * @param traderCertificateMapByType
     * @return
     */
    public static TransCertificateDto convertRegistrationForm(Manufacturer manufacturer, Map<Integer, List<TraderCertificateVo>> traderCertificateMapByType) {
        List<TraderCertificateVo> registrationFormVo = traderCertificateMapByType.get(SysOptionConstant.ID_1102);

        TraderCertificateVo traderCertificateVo = new TraderCertificateVo();

        if (CollectionUtils.isNotEmpty(registrationFormVo)) {
            traderCertificateVo = registrationFormVo.get(0);
        }
        if (manufacturer.getPepStartTime() != null && manufacturer.getPepStartTime().getTime()>0 ) {
            traderCertificateVo.setBegintime(manufacturer.getPepStartTime().getTime() < 0 ? ErpConst.ZERO.longValue():manufacturer.getPepStartTime().getTime());
        }
        if (manufacturer.getPepEndTime() != null && manufacturer.getPepEndTime().getTime()>0 ) {
            traderCertificateVo.setEndtime(manufacturer.getPepEndTime().getTime() < 0 ? ErpConst.ZERO.longValue() : manufacturer.getPepEndTime().getTime());
        }

        List<Attachment> djAttachments = manufacturer.getDjbAttachments();
        boolean isSyncBase = manufacturer.getPepStartTime() != null
                || manufacturer.getPepEndTime() != null;

        TransCertificateDto transCertificateDto = new TransCertificateDto();
        transCertificateDto.setSyncType(buildSyncStatus(isSyncBase, djAttachments));
        transCertificateDto.setBaseInfo(traderCertificateVo);
        transCertificateDto.setSourceList(djAttachments);
        transCertificateDto.setSysOptionDefinitionId( SysOptionConstant.ID_1102);
        transCertificateDto.setOldInfoList(registrationFormVo);

        return transCertificateDto;
    }


    /**
     *  医疗器械生产许可证
     * @param manufacturer
     * @param traderCertificateMapByType
     * @return
     */
    public static TransCertificateDto convertProduction(Manufacturer manufacturer, Map<Integer, List<TraderCertificateVo>> traderCertificateMapByType) {
        List<TraderCertificateVo> businessVo = traderCertificateMapByType.get(SysOptionConstant.ID_439);

        TraderCertificateVo traderCertificateVo = new TraderCertificateVo();

        if (CollectionUtils.isNotEmpty(businessVo)) {
            traderCertificateVo = businessVo.get(0);
        }

        if (manufacturer.getPeStartTime() != null && manufacturer.getPeStartTime().getTime()>0 ) {
            traderCertificateVo.setBegintime(manufacturer.getPeStartTime().getTime());
        }
        if (manufacturer.getPeEndTime() != null && manufacturer.getPeEndTime().getTime()>0 ) {
            traderCertificateVo.setEndtime(manufacturer.getPeEndTime().getTime());
        }
        traderCertificateVo.setSn(StringUtil.isNotBlank(manufacturer.getProductCompanyLicence()) ? manufacturer.getProductCompanyLicence() : traderCertificateVo.getRecordNo());

        boolean isSyncBase = manufacturer.getPeStartTime() != null
                || manufacturer.getPeEndTime() != null
                || StringUtil.isNotBlank(manufacturer.getProductCompanyLicence());

        List<Attachment> scAttachments = manufacturer.getScAttachments();

        TransCertificateDto transCertificateDto = new TransCertificateDto();
        transCertificateDto.setSyncType(buildSyncStatus(isSyncBase, scAttachments));
        transCertificateDto.setBaseInfo(traderCertificateVo);
        transCertificateDto.setSourceList(scAttachments);
        transCertificateDto.setSysOptionDefinitionId( SysOptionConstant.ID_439);
        transCertificateDto.setOldInfoList(businessVo);

        return transCertificateDto;
    }



}
