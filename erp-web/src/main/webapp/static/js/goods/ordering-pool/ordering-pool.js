
$(function () {

    //搜索建议词
    $('.J-suggest-input').each(function () {
        let _this = this;
        new Suggest({
            el: this,
            url: page_url + $(this).data('url'),
            params: $(this).attr('name'),
            parseData: function (data) {
                return parseData[$(_this).attr('name')](data);
            }
        });
    })

    let parseData = {
        brandName: function (data) {
            let list = data.listData || [];
            let reslist = [];
            $.each(list, function (i, item) {
                reslist.push({
                    word: item.brandName,
                })
            })
            return reslist;
        }
    };

    let checkSelectAll = function () {
        let selectFlag = true;

        $('.J-select-sku').each(function () {
            if (!$(this)[0].checked) {
                selectFlag = false;
            }
        })

        $('.J-select-list-all')[0].checked = selectFlag;
    };

    //表格全选复选框
    $('.J-select-list-all').on("click",function () {

        let $this = $(this);

        $('.J-select-sku').each(function () {
            $(this)[0].checked = $this[0].checked;
        })

    });

    $('.J-select-sku').on("click",function () {
        checkSelectAll();
    })

    // 获取所有销量
    let $input = $('input:checkbox[name="skuId"]');
    let skuIdArray = new Array($input.length);
    for(let i=0;i<$input.length;i++){
        skuIdArray[i]=$input[i].value;
    }
    console.log(skuIdArray);
    $.ajax({
        async:true,
        url:'./getOneThreeMonthLastYearSaleNum.do',
        data: JSON.stringify(skuIdArray),
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            console.log(data);
            for(let j=0;j<data.length;j++) {
                // console.log($("#lastYearSum"+data[j].skuId));
                // console.log(data[j].skuId);
                $('#lastYearSum'+data[j].skuId).text(data[j].lastYearSum);
                $('#lastYearPart'+data[j].skuId).text(data[j].lastYearPart);
                $('#threeMonthSum'+data[j].skuId).text(data[j].threeMonthSum);
                $('#threeMonthPart'+data[j].skuId).text(data[j].threeMonthPart);
                $('#oneMonthSum'+data[j].skuId).text(data[j].oneMonthSum);
                $('#oneMonthPart'+data[j].skuId).text(data[j].oneMonthPart);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })

});


function getChildrenCategory(obj) {

    let optId = $(obj).attr("id");
    let val = $(obj).val();
    if(optId == "categoryOpt0"){
        if(val != "-1" && val != -1) {
            $.ajax({
                async: false,
                url: page_url + '/goods/category/getCategoryList0.do',
                data: {"parentId": val},
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data.code == 0) {
                        let list = data.listData;
                        if (list != null && list.length > 0) {
                            let ht = "<option value='-1'>请选择</option>";
                            for (let i = 0; i < list.length; i++) {
                                ht = ht + "<option value='" + list[i].categoryId + "'>" + list[i].categoryName + "</option>";
                            }
                            $("#categoryOpt1").empty();
                            $("#categoryOpt1").append(ht);
                            let ht1 = "<option value='-1'>请选择</option>";
                            $("#categoryOpt2").empty();
                            $("#categoryOpt2").append(ht1);
                        }else {
                            let ht = "<option value='-1'>请选择</option>";
                            $("#categoryOpt1").empty();
                            $("#categoryOpt1").append(ht);
                            let ht1 = "<option value='-1'>请选择</option>";
                            $("#categoryOpt2").empty();
                            $("#categoryOpt2").append(ht1);
                        }
                    }
                },
                error: function (data) {
                    if (data.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }else{
            let ht1 = "<option value='-1'>请选择</option>";
            $("categoryOpt1").empty();
            $("categoryOpt1").append(ht1);
            let ht2 = "<option value='-1'>请选择</option>";
            $("categoryOpt2").empty();
            $("categoryOpt2").append(ht2);
        }
        $("#categoryId").val(0);

    }else if(optId == "categoryOpt1"){
        if(val != "-1" && val != -1) {
            $.ajax({
                async: false,
                url: page_url + '/goods/category/getCategoryList0.do',
                data: {"parentId": val},
                type: "POST",
                dataType: "json",
                success: function (data) {
                    if (data.code == 0) {
                        let list = data.listData;
                        if (list != null && list.length > 0) {
                            let ht111 = "<option value='-1'>请选择</option>";
                            for (let i = 0; i < list.length; i++) {
                                ht111 = ht111 + "<option value='" + list[i].categoryId + "'>" + list[i].categoryName + "</option>";
                            }
                            $("#categoryOpt2").empty();
                            $("#categoryOpt2").append(ht111);
                        }
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }else{
            let ht2 = "<option value='-1'>请选择</option>";
            $("#categoryOpt2").empty();
            $("#categoryOpt2").append(ht2);
        }
        $("#categoryId").val(0);
    }else{
        $("#categoryId").val(val);
    }
}

function showOrHiddenBatchDelete() {
    let val = $("select[name='skuStatus']").val();
    // 删除时隐藏按钮
    if(val == 1){
        $("#batchDelete").hide();
    }else{
        $("#batchDelete").show();
    }
}

function removePoolCause(obj) {

    console.log(obj);
    if(obj.length == 0){
        console.log("结束了..");
        layer.alert("请选择要查询删除原因的商品")
        return;
    }

    layer.open({
        type: 2,
        title:'删除原因',
        closeBtn: 1,
        anim: 2,
        area: ['50%', '400px'],
        btn: ['确定'],
        shadeClose: false,
        content: './removePoolCause.do?skuId='+obj,
        btnAlign: 'c',
        fixed: true,
        yes:function (index, layero){
            // console.log(index,layero);
            layer.close(index);
        }

    });

}

function removePool(obj) {

    console.log(obj);
    if(obj.length == 0){
        console.log("结束了..");
        layer.alert("请选择需要删除的商品")
        return;
    }

    let skuNoList = '';
    for(let i=0;i<obj.length;i++){
        if(i == obj.length-1){
            skuNoList += 'skuNoList='+obj[i];
            continue;
        }
        skuNoList += 'skuNoList='+obj[i]+'&';
    }
    let title= '删除';
    if(skuNoList.length>1){
        title='批量删除';
    }

    layer.open({
        type: 2,
        title:title,
        closeBtn: 1,
        btn: ['取消', '确定'],
        anim: 2,
        area: ['50%', '400px'],
        shadeClose: false,
        skin: 'demo-class',
        content: './removeOrderingPool.do?'+skuNoList,
        btnAlign: 'c',
        fixed: true,
        yes:function (index, layero){
            // console.log(index,layero);
            layer.close(index);
        },
        btn2:function (index, layero) {
            //获得弹出层iframe
            let iframe = window["layui-layer-iframe" + index].callbackdata3();
            if(iframe == true){
                search();
                return true;
            }
            // search();
            return false;
        }

    });

}

function batchRemovePool() {
    // 获取所有销量
    let $input = $('input:checkbox:checked[name="skuId"]');
    let skuNoArray = new Array();
    for(let i=0;i<$input.length;i++){
        skuNoArray[i] = $($input[i]).parent().parent().parent().parent().next().children().children().text();
    }

    // console.log(skuNoArray);
    // 从定品池移除
    removePool(skuNoArray);
}

function joinPool(obj) {

    // obj -- [skuId]
    console.log(obj);
    if(obj.length == 0){
        console.log("结束了..");
        layer.alert("请选择需要加入定品池的商品")
        return;
    }

    let array = new Array();
    for(let i=0;i<obj.length;i++){
        array[i] = {skuId:obj[i]};
    }

    let load = layer.msg('处理中', {
        icon: 16
        ,shade: 0.1
    });

    $.ajax({
        async:false,
        url:'./joinOrderingPool.do',
        data: JSON.stringify({
            idFlag:1,
            joinOrderingPoolOperateTempList:array
        }),
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            layer.close(load);
            console.log(data);
            search();
        },
        error:function(data){
            layer.close(load);
            console.log(data);
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
            if(data.status ==400){
                layer.alert("当前操作失败")
            }
        }
    })

}

