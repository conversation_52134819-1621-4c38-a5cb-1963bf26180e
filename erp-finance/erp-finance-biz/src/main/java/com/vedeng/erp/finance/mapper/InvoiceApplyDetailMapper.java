package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.InvoiceApplyDetailEntity;
import com.vedeng.erp.finance.dto.InvoiceApplyDetailDto;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface InvoiceApplyDetailMapper {


    /**
     * 根据开票申请id集合查询
     * @param invoiceApplyIdCollection
     * @return
     */
    List<InvoiceApplyDetailDto> findByInvoiceApplyIdIn(@Param("invoiceApplyIdCollection") Collection<Integer> invoiceApplyIdCollection);

    /**
     * 根据开票申请id集合查询
     * @param invoiceApplyIdCollection
     * @return
     */
    List<InvoiceApplyDetailDto> findAtByInvoiceApplyIdIn(@Param("invoiceApplyIdCollection") Collection<Integer> invoiceApplyIdCollection);

    /**
     * 根据主键查询
     * @param invoiceApplyDetailId
     * @return
     */
    InvoiceApplyDetailDto getByInvoiceApplyDetailId(@Param("invoiceApplyDetailId") Integer invoiceApplyDetailId);

    /**
     * 根据开票申请id集合查询
     * @param invoiceApplyIdCollection
     * @return
     */
    List<InvoiceApplyDetailEntity> selectByInvoiceApplyIdIn(@Param("invoiceApplyIdCollection")Collection<Integer> invoiceApplyIdCollection);

    List<InvoiceApplyDetailDto> getDetailByInvoiceApplyId(Integer invoiceApplyId);

    int insertSelective(InvoiceApplyDetailEntity invoiceApplyDetailEntity);

    int batchInsert(@Param("list") List<InvoiceApplyDetailEntity> list);

}