package com.vedeng.order.dao;

import com.newtask.model.SkuSaleNum;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultAssist;
import com.vedeng.finance.model.SaleorderData;
import com.vedeng.order.model.*;
import com.vedeng.order.model.coupon.CouponOrderData;
import com.vedeng.order.model.coupon.CouponOrderDetailData;
import com.vedeng.order.model.dto.*;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.trader.group.model.GoodsQueryParam;
import com.vedeng.trader.group.model.SaleorderSum;
import com.vedeng.trader.model.Period;
import com.vedeng.trader.model.TraderOrderGoods;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 销售订单
 *
 * <AUTHOR>
 */
@Named
public interface SaleorderMapper {

	int insertSelective(Saleorder record);

	/**
	 * <b>Description:</b>获取一定时间段内下单天数和订单总额<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2019/10/30
	 */
	SaleorderCountResult getDaysCountSum(SaleorderCountParam param);

	/**
	 * <b>Description:</b>根据客户标识查询订单所属商品的sku<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> ${date} ${time}
	 */
	List<String> getOrderGoodsSkuByTraderId(TraderOrderGoods param);

	/**
	 * <b>Description:</b>根据订单时间段和TraderId查询订单个数<br>
	 *
	 * @param
	 * @return
	 * @Note <b>Author:calvin</b>
	 * <br><b>Date:</b> 2019/10/29
	 */
	Integer getSaleorderCountByTime(SaleorderCountParam saleorder);

	/**
	 * 查询销售订单id
	 * <b>Description:</b><br>
	 *
	 * @param request
	 * @param session
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> bill.bo
	 * <br><b>Date:</b> 2019年2月27日
	 */
	List<Integer> getSaleOrderIdListByParam(Map<String, Object> paraMap);

	/**
	 * 根据订单id查询订单信息
	 * <b>Description:</b><br>
	 *
	 * @param request
	 * @param session
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> bill.bo
	 * <br><b>Date:</b> 2019年2月27日
	 */
	List<Saleorder> getOrderListInfoById(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传相关信息
	 *
	 * @param contract
	 * @return List<SaleorderContract>
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月5日 下午3:31:09
	 */
	List<SaleorderContract> getContractReturnOrderListPage(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传总数
	 *
	 * @param paraMap
	 * @return Integer
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月12日 下午1:24:19
	 */
	Integer getContractReturnOrderListCount(Map<String, Object> paraMap);

	/**
	 * 获取销售工作台页面待合同回传订单 金额
	 * @param paraMap
	 * @return
	 */
	BigDecimal getContractReturnOrderListRealityMoney(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传不合格
	 *
	 * @param paraMap
	 * @return List<SaleorderContract>
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月12日 下午1:25:03
	 */
	List<SaleorderContract> getContractReturnOrderNoqualityListPage(Map<String, Object> paraMap);

	/**
	 * <b>Description:</b>查询销售订单合同回传不合格总数
	 *
	 * @param paraMap
	 * @return Integer
	 * @Note <b>Author：</b> chuck
	 * <b>Date:</b> 2019年3月12日 下午1:25:28
	 */
	Integer getContractReturnOrderNoqualityListCount(Map<String, Object> paraMap);

	/* *
	 * 功能描述: 修改订单税率保存
	 * @param: [orderId, invoiceApplyId, invoiceType]
	 * @return: int
	 * @auther: duke.li
	 * @date: 2019/3/29 14:47
	 */
	int saveOrderRatioEdit(@Param(value = "orderId") Integer orderId, @Param(value = "invoiceType") Integer invoiceType);

	/**
	 * @Description: 根据参数查询耗材订单数据
	 * @Param: [saleorder]
	 * @return: java.util.List<com.vedeng.order.model.Saleorder>
	 * @Author: scott.zhu
	 * @Date: 2019/5/14
	 */
    List<Saleorder> getHcOrderList(Saleorder saleorder);

	/**
	 * <b>Description:</b><br>根据字段更新销售订单
	 *
	 * @param :[record]
	 * @return :int
	 * @Note <b>Author:</b> Michael <br>
	 * <b>Date:</b> 2019/5/21 7:58 PM
	 */
	int updateByPrimaryKeySelective(Saleorder record);

	int saveSaleOrderPrice(SaleOrderPriceDto saleOrderPriceDto);

	/**
	 * @param @param  traderId
	 * @param @return 参数
	 * @return List<Saleorder>    返回类型
	 * @throws
	 * @Title: getSaleOrderlistByStatus
	 * @Description: TODO(获取客户订单状态带确认, 非审核中的订单)
	 * <AUTHOR>
	 * @date 2019年7月24日
	 */
	List<Saleorder> getSaleOrderlistByStatusTraderId(Integer traderId);

	/**
	 * @param @param  saleorderId
	 * @param @return 参数
	 * @return Saleorder    返回类型
	 * @throws
	 * @Title: getSaleOrderById
	 * @Description: TODO(订单id获取订单)
	 * <AUTHOR>
	 * @date 2019年7月24日
	 */
	Saleorder getSaleOrderById(Integer saleorderId);

	List<Saleorder> getSaleOrersByIdList(List<Integer> ids);

	/**
	 * @param @param  saleorder
	 * @param @return 参数
	 * @return Saleorder    返回类型
	 * @throws
	 * @Title: getSaleorderBySaleorderNo
	 * @Description: TODO(订单号获取订单信息)
	 * <AUTHOR>
	 * @date 2019年7月31日
	 */
	Saleorder getSaleorderBySaleorderNo(Saleorder saleorder);
	Saleorder getSaleorderBySaleorderNo2(String saleorderNo);

	/**
	 * @param @param  status
	 * @param @param  orderType
	 * @param @return 参数
	 * @return List<Saleorder>    返回类型
	 * @throws
	 * @Title: getSaleorderListByStatus
	 * @Description: TODO(获取待用户确认状态BD订单)
	 * <AUTHOR>
	 * @date 2019年8月1日
	 */
	List<Saleorder> getSaleorderListByStatus(@Param(value = "status") Integer status, @Param(value = "orderType") Integer orderType);

	/**
	 * @param @param  createMobile
	 * @param @return 参数
	 * @return List<Saleorder>    返回类型
	 * @throws
	 * @Title: getSaleOrderlistByStatusMobile
	 * @Description: TODO(获取客户订单状态带确认, 非审核中的订单)
	 * <AUTHOR>
	 * @date 2019年8月8日
	 */
	List<Saleorder> getSaleOrderlistByStatusMobile(@Param(value = "createMobile") String createMobile);

	/**
	 * @Description: 获取订单编号
	 * @Param: [saleorder]
	 * @return: com.vedeng.order.model.Saleorder
	 * @Author: addis
	 * @Date: 2019/8/13
	 */
	List<Saleorder> selectSaleorderNo(Saleorder saleorder);

	List<GoodsData> getGoodsOccupyNumList(@Param("goodsIds") List<Integer> goodsIds);

	Integer getGoodsOccupyNum(@Param("goodsId") Integer goodsId);

	/**
	 * @Description: 查询订单里是否有该报价存在
	 * @Param: [quoteorderId]
	 * @return: java.lang.Integer
	 * @Author: addis
	 * @Date: 2019/8/30
	 */
	Integer isExistQuoteorderId(Integer quoteorderId);

	/**
	 * <b>Description:</b><br>
	 * 剩余账期未还金额
	 * add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 .
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 * <b>Date:</b> 2017年10月16日 上午10:27:20
	 */
	BigDecimal getSaleorderLackAccountPeriodAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 获取订单已收款账期金额
	 * add by Tomcat.Hui 2019/9/5 10:44 .Desc: VDERP-1053 .
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 * <b>Date:</b> 2017年10月23日 上午11:06:47
	 */
	BigDecimal getPeriodAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * @param @param  orderNoList
	 * @param @return
	 * @return List<Saleorder>
	 * @throws
	 * @Description: TODO(订单号获取订单基本信息)
	 * <AUTHOR>
	 * @date 2019年10月12日
	 */
	List<Saleorder> getSaleorderBySaleorderNoList(ArrayList<String> orderNoList);

	/**
	 * @param @param  saleorder
	 * @param @return
	 * @return Integer
	 * @throws
	 * @Description: TODO(更改订单收货状态)
	 * <AUTHOR>
	 * @date 2019年10月14日
	 */
	Integer updateDeliveryStatusBySaleorderNo(Saleorder saleorder);

	/**
	 * 非备货非关闭订单id
	 *
	 * @Author:strange
	 * @Date:19:34 2019-11-11
	 */
	Integer getSaleorderidByStatus(Integer day);

	/**
	 * @Description: 根据订单id查询用户id
	 * @Param:
	 * @return:
	 * @Author: addis
	 * @Date: 2019/11/8
	 */
	Saleorder getWebAccountId(Integer saleorderId);

	/**
	 * 分页查询订单id
	 *
	 * @Author:strange
	 * @Date:01:20 2019-11-28
	 */
	List<Integer> getSaleorderidByStatusLimit(@Param("day") Integer day, @Param("limit") Integer limit);

	void updateSaleorderStatusById(@Param("idList") List<Integer> idList, @Param("modTime") Long modTime, @Param("userId") Integer userId);

	/**
	 * 根据订单号查询Id
	 *
	 * @param saleorderNo
	 * @return
	 * @Author:Rock
	 */
    Saleorder getSaleOrderId(String saleorderNo);


	/**
	 * 订单商品id获取订单信息
	 *
	 * @Author:strange
	 * @Date:17:36 2019-12-04
	 */
    Saleorder getSaleorderBySaleorderGoodsId(Integer saleorderGoodsId);

	/**
	 * 分页获取活动订单id
	 *
	 * @Author:strange
	 * @Date:15:43 2019-12-05
	 */
	List<Integer> getActionOrderLimit(@Param("day") Integer day, @Param("limit") Integer limit);

	/**
	 * 根据订单号取消订单（用于耗材商城订单状态同步）
	 * <b>Description:</b>
	 *
	 * @param saleorder
	 * @return Integer
	 * @Note <b>Author：</b> barry.xu
	 * <b>Date:</b> 2018年11月27日 上午10:05:32
	 */
    int updateOrderStatusByOrderNo(Saleorder saleorder);

	/**
	 * 获取所有订单活动id
	 *
	 * @Author:strange
	 * @Date:13:08 2019-12-23
	 */
	List<Integer> getAllActionId(Integer day);

	//第一次物流增加评论
    int updateLogisticsComments(@Param("saleorderId") Integer saleorderId, @Param("logisticsComments") String s);

    List<Saleorder> getSaleorderByExpressDetailId(@Param("list") List<Integer> list);

    Saleorder getSaleorderByOrderListId(Integer orderListId);

	List<SaleorderGoods> getSaleorderGoodsByOrderListId(List<Integer> orderListId);

	/**
	 * @param ownerUserId
	 * @return
	 */
    User getUserDetailInfoByUserId(Integer ownerUserId);

	User getUserInfoByTraderId(Integer traderId);

	/**
	 * 宝石花出库单列表
	 *
	 * @Author:strange
	 * @Date:16:58 2020-02-20
	 */
    List<Saleorder> getFlowerPrintOutListPage(Map<String, Object> map);

    int cleanSaleOrder(@Param("testUserNames") String testUserNames);
    int cleanSaleOrder2(@Param("testUserNames") String testUserNames);

	int cleanAfterSale(@Param("testUserNames") String testUserNames);


	int clearBuyorderInvoice(@Param("testUserNames") String testUserNames);
	int clearSaleorderInvoice(@Param("testUserNames") String testUserNames);
	int clearBuyorder(@Param("testUserNames") String testUserNames);



	/**
	 * 更新updateDataTime
	 *
	 * @Author:strange
	 * @Date:11:37 2020-04-06
	 */
    Integer updateDataTimeByOrderId(Integer orderId);

	/**
	 * 更新updateDataTime
	 *
	 * @Author:strange
	 * @Date:11:37 2020-04-06
	 */
	Integer updateDataTimeByDetailId(Integer orderDetailId);

    int clearBussiness(@Param("testUserNames") String testUserNames);

	/*校验锁的状态*/
	void updateLockedStatus(Integer saleorderId);

	/**
	 * 获取订单实付金额
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 11:26 上午 2020/5/19
	 * @Param
	 **/
    BigDecimal getPaymentAmount(Integer orderId);

	/**
	 * 获取订单退款金额
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 1:41 下午 2020/5/19
	 * @Param
	 **/
    BigDecimal getReturnAmount(Integer orderId);

	/**
	 * 更新订单金额信息
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 3:52 下午 2020/5/19
	 * @Param
	 **/
	int updateAmountInfo(Saleorder saleorder);

	/**
	 * 获取近两天修改过的订单
	 *
	 * @return
	 * <AUTHOR>
	 * @Date 6:16 下午 2020/5/19
	 * @Param
	 **/
    List<Integer> getrecentDayOrder(@Param("num") Integer num);

	int getSaleorderidAll();

	List<Integer> getSaleorderidAllLimit(int i);

	/**
	 *   根据手机 获取6月16日 0 点以后的订单
	 * @param mobile
	 * @return
	 */
	List<Saleorder>  selectLatestSaleOrderByMobile(String mobile);

	int findByElorderNo(String orderNumber);


	/**
	 * <b>Description:</b>最近下单<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdOrderedInPeriod(Period period);

	/**
	 * <b>Description:</b>最近购买<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdBuyInPeriod(Period period);
	/**
	 * <b>Description:</b>下单商品<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsOrderGoods(GoodsQueryParam param);
	/**
	 * <b>Description:</b>购买商品<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsBuyGoods(GoodsQueryParam param);
	/**
	 * <b>Description:</b>成交金额<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsByAmount(SaleorderSum saleorderSum);
	/**
	 * <b>Description:</b>成交次数<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsByTimes(SaleorderSum saleorderSum);
	/**
	 * <b>Description:</b>最近成交<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/5/27
	 */
	List<Integer> getTraderIdsByDealRecently(SaleorderSum saleorderSum);

//	/**
//	 * 获取实付金额大于0的有效状态订单
//	 * <AUTHOR>
//	 * @Date 2:43 下午 2020/5/26
//	 * @Param
//	 * @return
//	 **/
//    List<Saleorder> getSaleorderRealAmountByTraderId(@Param("traderId")Integer traderId);

	/**
	 * 获取使用优惠券订单统计数据
	 * <AUTHOR>
	 * @Date 7:53 下午 2020/6/3
	 * @Param
	 * @return
	 **/
    CouponOrderData getCouponOrderDataByCouponId(Integer couponId);

	/**
	 * 获取使用优惠券订单明细数据
	 * <AUTHOR>
	 * @Date 7:54 下午 2020/6/3
	 * @Param
	 * @return
	 **/
	List<CouponOrderDetailData> getCouponOrderDataDetailListByCouponId(Integer couponId);

	List<Saleorder> getBDSaleOrderAndHasCoupon();
	/**
	 * 获取使用优惠券订单明细数据
	 * <AUTHOR>
	 * @Date 7:54 下午 2020/6/3
	 * @Param
	 * @return
	 **/
	List<CouponOrderDetailData> getCouponOrderDataDetailListBycouponCodeList(List<String> couponCodeList);

	List<SaleorderGoods> getSaleorderGoodsById(Integer saleorderId);
	List<SaleorderGoods> getSaleorderGoodsByIdNoSpecial(Integer saleorderId);


	List<Saleorder> getSaleorderByQuoteorderId(Integer quoteorderId);

	void updateNotSalesPerformanceOfSaleorder(Integer saleorderId);

	/**
	 * @description: 获取生效销售单未指定逻辑仓数据
	 * @return:  List<Integer>
	 * @author: Strange
	 * @date: 2020/7/29
	 **/
    List<Integer> getVaildOrderAndNoChooseOrderId();

    /**
     * <b>Description:</b>根据订单编号更新订单删除状态<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/9/7
     */
    Integer updateIsDeleteByOrderNo(Saleorder saleorder);
	/**
	 * @description: 获取实付金额大于0的有效状态订单
	 * @return: List<Saleorder>
	 * @author: Strange
	 * @date: 2020/7/3
	 **/
	List<Saleorder> getSaleorderRealAmountByTraderId(Integer traderId);

	/**
	 * 根据发票ID获取票货同行订单信息
	 *
	 * @param invoiceIds
	 * @return
	 */
    List<Saleorder> getHcSaleorderInfoByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    List<String> findRelateSaleOrderNo(@Param("buyOrderGoodId") Integer buyOrderGoodId);

    List<Saleorder> getSaleorderRealAmountByTraderIdAndSaleorderId(Saleorder saleorder);

	/**
	 * @description: 获取订单实际金额
	 * @return: BigDecimal
	 * @author: Strange
	 * @date: 2020/9/24
	 **/
    BigDecimal getRealAmount(Integer saleorderId);


	/**
	 * 获取销售订单的实际金额（减去完结的售后单）
	 * @param saleorderId 订单id
	 * @return 订单实际金额
	 */
	BigDecimal getRealTotalAmountExceptAfterSalesFinished(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b> 订单付款信息修改（耗材商城）
	 *
	 * @param orderInfo
	 * @return int
	 * @Note <b>Author：</b> lijie <b>Date:</b> 2018年11月5日 下午3:19:42
	 */
    int updateSaleorderPayStatus(Saleorder orderInfo);

	/**
	 * 查询销售订单的销售部门是医械购及其子部门，且该订单是该客户非已关闭，且非全部退货的销售订单
	 * @param saleorder
	 * @return
	 */
    List<Saleorder> getHcOrgValidStatus(Saleorder saleorder);

	List<Saleorder> getHCSaleOrderAndHasCoupon();

	/**
	 * <b>Description:</b>分页获取bd订单，除去已完结和已关闭<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/9/28
	 */
	List<SimpleBdOrderChooseRes> getBdOrderListPage(Map<String,Object> param);

	/**
	 * @description: 获取订单账期未还金额
	 * @return: BigDecimal
	 * @author: Strange
	 * @date: 2020/11/4
	 **/
    BigDecimal getNoPayBackAmount(Integer orderId);

	List<Integer> getManageCategoryOfSkuBySaleorderId(Integer saleorderId);

	Saleorder getSaleorderByOrderNo(String saleorderNo);

	/**
	 * 查询订单信息，关联DATA表
	 * @param saleorderNo
	 * @return
	 */
	Saleorder getSaleorderDataByOrderNo(String saleorderNo);

	Saleorder getSaleorderBySaleorderId(Integer saleorderId);

	/**
	 * 更新盖贝登章合同url
	 * @param saleorderId 订单id
	 * @param contractUrl 合同url
	 */
	void updateContractUrlOfSaleorder(@Param("saleorderId") Integer saleorderId, @Param("contractUrl") String contractUrl);


	/**
	 * 更新盖贝登章合同url
	 * @param saleorderId 订单编号
	 * @param contractUrl 未盖章合同url
	 */
	void updateContractNoStampUrlOfSaleorder(@Param("saleorderId") Integer saleorderId, @Param("contractUrl") String contractUrl);

	/**
	 * 获取客户需要更新合同模板的订单
	 * @param traderId 客户id
	 * @return 订单id集合
	 */
	List<Integer> getSaleorderOfNeedUpdateContract(Integer traderId);

	Integer getSaleorderIdByModifyApplyId(Integer saleorderModifyApplyId);

	/**
	 * <b>Description:</b>分页获取近一年的销量<br>
	 * @param
	 * @return
	 * @Note
	 * <b>@Author:calvin</b>
	 * <br><b>@Date:</b> 2020/11/14
	 */
	List<SkuSaleNum> getOneYearSkuSaleNumListPage(Map<String,Object> param);

    List<SaleorderModifyApply> getSaleorderModifyApplyList(Integer saleorderId);

	/**
	 * 获取订单中提交审核但未审核通过的sku数量
	 * @param saleorderId 订单id
	 * @return 数量
	 */
	List<String> getUncheckedSkuCountOfSaleorder(Integer saleorderId);

    void updateHcOrderDefaultValue(Saleorder updateSaleorderInfo);

    Saleorder getSalerOfSaleorderByOrderNo(String saleorderNo);

    List<Saleorder> getFinishOrderStatus(Saleorder searchSaleorder);

	/**
	 *获取订单的审核状态
	 * @param saleorderId 订单id
	 * @return 0审核中1审核通过2审核不通过,-1或3为待审核状态
	 */
	Integer getSaleordergetVerifyStatus(Integer saleorderId);

	/**
	 * 根据订单关联业务更新时间获取IDs
	 */
	List<Integer> getLastWeekIdsByUpdateDateTime(@Param("lastWeek") String lastWeek);

	/**
	 *  获取所有IdS
	 * @return
	 */
	List<Integer> getAllSaleOrderIds(@Param("minId") Integer minId,@Param("limit") Integer limit);

    void clearBhOrderAuditInfo(@Param("saleOrderId") Integer saleOrderId);
	/**
	 * 根据查询条件获取集采订单列表(分页）
	 * @param paramMap 查询条件参数
	 * @return
	 */
	List<Saleorder> getJcOrderListPage(Map<String, Object> paramMap);

	/**
	 * 查询订单 是否维护了基础价格
	 * @param saleorderId
	 * @return
	 */
	Integer	queryWhetherMaintain(Integer saleorderId);

	/**
	 * 根据送货单是否回传，合同是否回传，查询订单列表
	 * @param map
	 * @return
	 */
	List<Integer> getSaleorderReturnOrNotList(Map<String, Object> map);

	/**
	 * 根据账期是否还清，查询订单
	 * @return
	 */
	List<Integer> getLackAccountPeriodOrderSaleOrderId();

	/**
	 * 获取集采订单列表总数
	 * @param map
	 * @return
	 */
	Map<String, Object> getSaleorderListCount(Map<String, Object> map);

	/**
	 * 查询记录总计及总金额
	 *
	 * @param map
	 * @return
	 */
	BigDecimal getSaleorderListSum(Map<String, Object> map);

	/**
	 * 归属ID检索订单ID
	 *
	 * @param productBelongUserId
	 * @return
	 */
	List<Integer> getSaleorderIdListBelongtoProductRole(@Param("productBelongUserId") Integer productBelongUserId);

	/**
	 * 获取销售订单总数和总金额数(根据订单类型)
	 *
	 * @param map
	 * @return
	 */
	Map<String, Object> getSaleorderListCountAndTotalCount(Map<String, Object> map);

	/**
	 * 获取集采直销线下订单列表（分页）
	 * @param map
	 * @return
	 */
	List<Saleorder> getOfflineDirectSalesOrderListPage(Map<String, Object> map);

	void updateByJCOrder(Saleorder saleorder);
	/**
	 *  获取直发销售与采购对应关系
	 * @param buyorderGoodIds
	 * @return
	 */
	List<RBuyorderSaleorder> getRBuyorderSaleorderByBuyOrderGoodIds(@Param("saleorderGoodIds") List<Integer> buyorderGoodIds);
	/**
	 * @Description 根据销售单获取订单归属销售ID
	 * <AUTHOR>
	 * @Date 19:58 2021/4/14
	 * @Param [saleorderId]
	 * @return java.lang.Integer
	 **/
    Integer getOwnerNameBySaleorderId(@Param("saleorderId")Integer saleorderId);

	/**
	 * 根据采购编码获取销售信息
	 * @param orderId
	 * @return
	 */
	List<Saleorder> selectSaleorderNoByBuyOrderId(@Param("buyOrderId") Integer orderId);
	/**
	 * 更新销售单产品明细采购要求
	 * @param componentRelation
	 */
	void updateSaleorderRemarkComponent(ComponentRelation componentRelation);
	/**
	 * 更新销售单产品明细采购要求
	 * @param componentRelation
	 */
	void updateSaleOrderFlagRemarkComponent(ComponentRelation componentRelation);
	/**
	 * 根据销售单ID得到销售物品（非直发）
	 * @param saleorderId
	 * @return
	 */
	List<SaleorderGoods> getSaleorderGoodsBySaleOrderId(Integer saleorderId);

	/**
	 * 获得采购要求信息
	 * @param componentRelationIsExist
	 * @return
	 */
    SaleorderGoodsVo getRemarkComponetInfo(ComponentRelation componentRelationIsExist);

	/**
	 * 更新时效时间
	 * @param componentRelationIsExist
	 */
	void updateWarnStatus(ComponentRelation componentRelationIsExist);
	/**
	 * <b>Description:</b><br>
	 * 修改订单表合同是否回传
	 * 0否1是
	 * @param isContactReturn,saleorderId
	 * @return int
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/5/10 17:08
	 */
	int uptContactStatus(@Param("isContactReturn") Integer isContactReturn,@Param("saleorderId")Integer saleorderId);
	/**
	 * <b>Description:</b><br>
	 * 修改订单表送货单是否回传
	 * 0否1是
	 * @param isDeliveryOrderReturn,saleorderId
	 * @return int
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/5/10 17:08
	 */
	int uptDeliveryStatus(@Param("isDeliveryOrderReturn") Integer isDeliveryOrderReturn,@Param("saleorderId")Integer saleorderId);

	/**
	 * 获得订单修改申请详情
	 * @jira: .
	 * @notes: .
	 * @version: 1.0.
	 * @date: 2021/5/27 9:59.
	 * @author: Randy.Xu.
	 * @param valueOf
	 * @return: com.vedeng.order.model.SaleorderModifyApply.
	 * @throws:  .
	 */
	SaleorderModifyApply getSaleorderModifyApplyBySaleorderModifyId(Integer saleorderModifyApplyId);
	/**
	 * <b>Description:</b><br>
	 * 根据类型和时间查询等通知发货超时订单信息
	 *
	 * @param compstartTime 比较开始时间,compendTime 比较结束时间
	 * @return java.util.List<com.vedeng.order.model.Saleorder>
	 * @Note <b>Author:</b> Thor <br>
	 *       <b>Date:</b> 2021/6/11 13:12
	 */
	List<Saleorder> selectTimeOutSales(@Param("compstartTime")Long compstartTime,@Param("compendTime")Long compendTime);

	/**
	 * @desc 获取专项发货所有已生效非关闭的采购单
	 * @param saleorderGoodsId
	 * @return
	 */
    List<RBuyorderSaleorderDto> getRBuyorderSaleorderBySaleOrderGoodsId(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	List<Integer> getRelateSaleOrderId(@Param("buyorderId") Integer buyorderId);

	Saleorder selectBySaleOrderId(@Param("relationId")Integer relationId);

	void updateSaleOrderFlagRemarkComponent0(ComponentRelation componentRelation);

	BigDecimal getPeriodOrderAmount(@Param("traderId") Integer traderId);

	/**
	 * 获取订单的客户ID信息
	 *
	 * @param orderId
	 * @return
	 */
	Long getCustomerIdByOrderId(Integer orderId);

	/**
	 * 检索销售订单所有修改申请ID
	 *
	 * @param orderId
	 * @return
	 */
    List<Integer> getOrderApplyIdsBySaleOrderId(Integer orderId);

	/**
	 * 状态检索销售订单ID
	 *
	 * @param status
	 * @return
	 */
	List<Integer> getSaleOrderIdsByStatus(Integer status);

	/**
	 * @description: 获取订单客户信息
	 * @return:
	 * @author: Strange
	 * @date: 2021/7/29
	 **/
    Saleorder getSaleOrderTraderById(@Param("saleorderId") Integer saleorderId);

	/**
	 * 查询订单集合
	 * @param saleorder
	 * @return
	 */
	List<Saleorder> getSaleOrderInfoBySaleOrderInfo(Saleorder saleorder);

	/**
	 * @description: 查询订单未还账期金额
	 * @return:
	 * @author: Strange
	 * @date: 2021/8/4
	 **/
    List<SaleorderData> getSaleorderLackAccountPeriodAmountDatas(@Param("saleorderIds") List<Integer> saleorderIdList);

	/**
	 * @description: 获取使用账期支付订单
	 * @return:
	 * @author: Strange
	 * @date: 2021/8/4
	 *
     * @param saleorderNoList*/
	List<Integer> getAccountPeriodSaleorderIdList(@Param("saleorderNoList") List<String> saleorderNoList);

	/**
	 * 订单已付款金额，不含账期
	 * @param saleorderId
	 * @return
	 */
	BigDecimal getPaymentAndPeriodAmount(@Param("saleorderId") Integer saleorderId);

	Saleorder getSaleorder(Saleorder saleOrder_lack);

	/**
	 * 获取订单特殊商品的金额
	 *
	 * @param orderId
	 * @return
	 */
	BigDecimal getSpecialGoodsAmountByOrderId(Integer orderId);


	/**
	 * 获取随机立减的金额
	 * @param orderId
	 * @return
	 */
	BigDecimal getSaleorderPriceInfo(Integer orderId);


	List<Integer> getPriceChangeAffectSaleorderBySkuId(@Param("skuId")Long skuId);

	/**
	 * 分页获取订单数据
	 * @param pageParam
	 * @return
	 */
	List<Saleorder> getSaleorderListPage(Map<String, Object> pageParam);

    List<Integer> getTraderIdList(Integer companyId);

    List<Saleorder> getRelatedSaleOrderInprocess(@Param("skuNo") String skuNo);

	/**
	 * 查询所有符合短信发送条件的订单信息
	 */
	List<SmsSaleorderDTO> getAllSmsSaleOrder(@Param("traderIds") List<Integer> traderIds);

	/**
	 * <b>Description:</b><br>
	 * 根据订单详情ID获取订单所有信息
	 *
	 * @param m
	 * @return
	 * @Note <b>Author:</b> Michael <br>
	 *       <b>Date:</b> 2017年9月6日 上午11:42:11
	 */
	Saleorder selectSumNumBySaleorderGoodsId(Map<String, Integer> m);
	/**
	 * <b>Description:</b><br>
	 * 获取订单基本信息
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> leo.yang <br>
	 *       <b>Date:</b> 2017年7月5日 下午2:06:36
	 */
	Saleorder getBaseSaleorderInfo(Integer saleorderId);

	/** <b>Description:</b><br>
	 * 获取销售产品已开票数量
	 *
	 * @param saleorderGoodsId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年11月15日 下午6:02:45
	 */
	BigDecimal getHaveInvoiceNums(Integer saleorderGoodsId);
	/**
	 * <b>Description:</b><br>
	 * 获取订单已收款金额(不含账期)
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年10月16日 上午10:26:59
	 */
	BigDecimal getSaleorderPaymentAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 主键查询
	 *
	 * @param saleOrderId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年10月10日 下午6:49:11
	 */
	SaleorderVo getSaleorderVoBySaleorderId(Integer saleOrderId);

	/**
	 * 获取订单打印信息
	 * @param saleorder
	 * @return
	 */
	SaleorderVo selectOrderInfo4PrintContractTemplate(Saleorder saleorder);

	/**
	 * 获取销售订单基本信息
	 *
	 * @param saleorderId
	 * @return
	 */
	Saleorder getSaleorderInfoById(Integer saleorderId);

	Saleorder selectByPrimaryKey(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 批量查询订单信息
	 *
	 * @param saleorderIds
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年10月18日 下午4:18:25
	 */
	List<SaleorderData> getSaleorderDatas(@Param("saleorderIds") List<Integer> saleorderIds);

	/**
	 * <b>Description:</b><br>
	 * 订单金额（总额-退款金额）--根据资金流水记录
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2018年9月10日 下午12:59:24
	 */
	List<ResultAssist> getNewRealAmount(@Param("saleOrderIdList") List<Integer> saleOrderIdList,
										@Param("companyId") Integer companyId);

	/**
	 * <b>Description:</b><br>
	 * 采购总额
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年10月30日 上午10:17:11
	 */
	BigDecimal getBuyAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 获取订单完结信息（货款票状态）
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年10月31日 下午3:38:37
	 */
	List<SaleorderVo> getSaleorderIsEnd(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 更改订单状态
	 *
	 * @param saleorderId
	 * @param status
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年10月31日 下午3:51:11
	 */
	Integer updateSaleorderStatus(@Param("saleorderId") Integer saleorderId, @Param("status") Integer status,
								  @Param("time") Long time);

	/**
	 * <b>Description:</b><br>
	 * 订单对公已收款金额(不含账期)
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2017年12月2日 下午1:12:01
	 */
	BigDecimal getPublicPaymentAmount(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 订单预付款金额（订单预付款金额-退款金额）-- 订单产品数量减去退货产品数量后，计算出订单预付款金额金额
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2018年1月30日 下午2:16:37
	 */
	BigDecimal getRealPreAmount(Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 通过关联表；根据销售单商品ID查询采购单商品单价
	 *
	 * @param saleorderGoodsId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2018年4月12日 下午2:13:02
	 */
	BigDecimal getBuyOrderGoodsPrice(@Param("saleorderGoodsId") Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br>
	 * 通过出入库记录查询销售单商品对应的备货单商品单价
	 *
	 * @param saleorderGoodsId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2018年4月12日 下午2:13:47
	 */
	BigDecimal getBhOrderGoodsPrice(@Param("saleorderGoodsId") Integer saleorderGoodsId);


	/**
	 * <b>Description:</b><br>
	 * 根据交易主体1对公2对私查询订单金额（减去退款的金额）
	 *
	 * @param saleorderId
	 * @param traderSubject
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2018年4月3日 下午1:23:04
	 */
	BigDecimal getAmountByTraderSubject(@Param("saleorderId") Integer saleorderId,
										@Param("traderSubject") Integer traderSubject);

	/**
	 * <b>Description:</b><br>
	 * 通过关联表；根据销售单商品ID查询采购单单号集合
	 *
	 * @param saleorderGoodsId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2018年4月12日 下午2:56:43
	 */
	List<Buyorder> getBuyOrderNoBySaleorderGoodsId(Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br>
	 * 通过出入库记录查询销售单商品对应的备货单单号集合
	 *
	 * @param saleorderGoodsId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2018年4月12日 下午2:56:57
	 */
	List<Buyorder> getBhOrderNoBySaleorderGoodsId(Integer saleorderGoodsId);

	/**
	 * <b>Description:</b><br>
	 * 销售订单详情页使用 订单已收款金额 {订单已收款金额 = （业务类型==订单收款）的流水总额-（业务类型==退款 &&
	 * 交易类型==转出）-支付宝提现的流水总额；}
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> leo.yang <br>
	 *       <b>Date:</b> 2018年6月26日 下午5:29:40
	 */
	BigDecimal getReceivedAmount(@Param("saleorderId") Integer saleorderId);

	List<BuyorderGoodsVo> getBuyPriceOrderAmount(@Param("saleOrderGoodsIdList") List<Integer> saleOrderGoodsIdList);

	/**
	 * 查询订单对公收款金额
	 *
	 * @param saleorderId
	 * @param traderSubject
	 * @return
	 */
	BigDecimal getPublicAmount(@Param("saleorderId") Integer saleorderId,
							   @Param("traderSubject") Integer traderSubject);

	/* * 判断是否能开票使用
	 * 功能描述: 实际已收款金额,对公+银行+（交易客户名称一致）
	 * @param: [saleorderId, traderName]
	 * @return: java.math.BigDecimal
	 * @auther: duke.li
	 * @date: 2019/3/28 11:10
	 */
	BigDecimal getOpenInvoiceOrderAmount(@Param("saleorderId") Integer saleorderId, @Param("traderName") String traderName);

	BigDecimal getRealPreAmountForHcOrder(Integer saleorderId);


	Saleorder getSaleOrderBaseInfo(@Param("saleOrderId") Integer saleOrderId);

	/**
	 *
	 * 查询销售单产生的售后单数
	 * @param saleorderId
	 */
	int getAfterSaleCnt(Integer saleorderId);

	/**
	 * 查询以采购商品的数量
	 * @param saleorderGoodsId
	 * @return
	 */
	Integer getBuyorderGoodsNum(@Param("saleorderGoodsId")Integer saleorderGoodsId);

	/**
	 * 订单号查询归属销售
	 * @param saleorderId
	 * @return
	 */
    List<User> selectAttributableSalesById(@Param("saleorderId") Integer saleorderId);


	/**
	 * 撤销销售订单生效状态
	 * @param saleorderId 订单id
	 */
	void cancelValidOfSaleOrder(@Param("saleorderId") Integer saleorderId, @Param("modTime") Long modTime);

	/**
	 *  根据销售单id查询销售单商品集合 不包括运费
	 * @param saleorderId
	 * @return 销售单商品
	 */
	List<SaleorderGoods> getSaleOrderGoodsExceptFreight(@Param("saleorderId") Integer saleorderId);

	/**
	 * 根据销售单商品id集合查询所属的销售单号集合
	 * @param saleorderGoodsIds 销售单商品id集合
	 * @return 销售单号集合
	 */
	List<String> getSaleorderNoList(@Param("saleorderGoodsIds") List<Integer> saleorderGoodsIds);

	/**
	 * 根据saleorderIds查询对应的销售单的锁定状态
	 */
	List<Integer> getLockedStatusBySaleorderIds(@Param("saleOrderIdList") List<Integer> saleOrderIdList);

	/**
	 * <b>Description:</b><br>
	 * 判断订单是否包含非全特殊产品
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> leo.yang <br>
	 *       <b>Date:</b> 2018年7月5日 上午8:57:10
	 */
	Integer isAllSpecialGoods(@Param("saleorderId") Integer saleorderId);

	/**
	 * <b>Description:</b><br>
	 * 获取销售订单财务信息
	 *
	 * @param saleorderId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2017年12月2日 下午4:23:42
	 */
	SaleorderVo getSaleorderFinanceInfo(@Param("saleorderId") Integer saleorderId);

    BigDecimal getSaleorderpayAmountByTime(@Param("subUserIdList") List<Integer> subUserIdList, @Param("startTime") long startTime, @Param("endTime") long endTime);

	/**
	 * 获取去重后的订单商品信息(过滤掉非审核通过和不可售)
	 * @param saleOrderNoList
	 * @return
	 */
	List<SaleorderGoods> getSaleOrderGoodsListByOrderIdList(List<Integer> saleOrderIdList);
	/**
	 * @Title: updateQuoteorderIdById
	 * @Description: TODO(根据saleOrderId更新报价单id)
	 * @param @param saleorder
	 * @param @return    参数
	 * @return int    返回类型
	 * <AUTHOR>
	 * @throws
	 * @date 2019年7月22日
	 */
	int updateQuoteorderIdById(Saleorder saleorder);

	Saleorder getSaleOrderByOrderNoAndTraderId(@Param("saleOrderNo") String saleOrderNo,@Param("traderId") Integer traderId);

    int clearPeriod(@Param("testUserNames") String testUserNames);

	/**
	 * 查询所有已关闭且审核中的备货单ID集合
	 */
	List<Integer> getCloseAndVerifyBh();

	List<Saleorder> getNotHcSaleOrderInfo(List<Integer> saleOrderIdList);

	BigDecimal getReceivedAmountByTime(@Param("subUserIdList") List<Integer> subUserIdList, @Param("starttime") long starttime, @Param("endtime") long endtime);

    int clearInvoice(@Param("testUserNames") String testUserNames);
	int clearReceiveInvoice(@Param("testUserNames") String testUserNames);
	BigDecimal getReturnAmountByTime(@Param("subUserIdList") List<Integer> subUserIdList, @Param("starttime") long starttime, @Param("endtime") long endtime);

	/**
	 * 更新订单确认状态和时间
	 *
	 * @param saleOrderInfo
	 */
	void updateConfirmTimeAndStatus(Saleorder saleOrderInfo);

	/**
	 * 更新订单和url
	 *
	 * @param saleOrderInfo
	 */
	void updateSaleOrderContractUrl(Saleorder saleOrderInfo);



    int clearInvoice();

    List<SaleorderVo> getSaleorderGoodsId(Saleorder saleorder);

	/**
	 * 检索配置时间内客户的所有订单数
	 * @param customerIds
	 * @param validOrderDays
	 * @return
	 */
	List<CustomerCountDto> getTraderOrderCountByRecentDaysList(@Param("customerIds") List<Integer> customerIds,
															   @Param("validOrderDays") Integer validOrderDays);

	Saleorder getFirstOrderOfTraderAfterSomeTime(@Param("traderId") Integer traderId, @Param("addTime") Long addTime);

	/**
	 * 查询需要自动关闭的订单
	 * @param params 查询参数
	 * @return 订单列表
	 */
	List<Saleorder> getOrdersForAutoClose(Map<String, Object> params);

	/**
	 * 查询需要发送预警的订单
	 * @param params 查询参数
	 * @return 订单列表
	 */
	List<Saleorder> getOrdersForWarning(Map<String, Object> params);

	/**
	 * 查询需要处理的历史订单
	 * @param params 查询参数
	 * @return 订单列表
	 */
	List<Saleorder> getHistoricalOrdersToProcess(Map<String, Object> params);

	/**
	 * 根据订单号列表查询符合自动关闭条件的订单
	 * @param orderNoList 订单号列表
	 * @return 订单列表
	 */
	List<Saleorder> getOrdersByOrderNoList(@Param("orderNoList") List<String> orderNoList);

	/**
	 * 根据订单号列表查询符合预警条件的订单
	 * @param params 查询参数（包含订单号列表和状态条件）
	 * @return 订单列表
	 */
	List<Saleorder> getOrdersByOrderNoListForWarning(Map<String, Object> params);

	/**
	 * 根据订单ID查询是否有满足条件的采购订单
	 * @param saleorderId
	 * @return
	 */
    List<Buyorder> queryUrgeDeliveryOrder(Integer saleorderId);

	/**
	 * 直发同行单生成催办
	 * @param buyorderId
	 */
	void urgeDeliveryOrder(Integer buyorderId);


	BigDecimal getRealTotalAmountBySaleorderIdList(@Param("saleorderIdList") List<Integer> saleorderIdList);

	/**
	 * 获取关联订单列表信息
	 *
	 * @param param
	 * @return
	 */
	List<SimpleBdOrderChooseRes> getQuoteOrderListPage(Map<String,Object> param);

	/**
	 * 获取号码关联所有销售定点杆数量
	 * 获取号码关联所有销售订单数量
	 * @param phone
	 * @return
	 */
	Integer getOrderNumberByPhone(String phone);

	@Select("select * from T_SALEORDER where DELIVERY_STATUS != 0  AND ADD_TIME > 1652976000000")
    List<Saleorder> getSaleorderListByTimeLimit();

	/**
	 * 更具客户id 查询是否有成功交易的订单
	 * @param traderId
	 * @return
	 */
	Integer selectTraderHaveOrder(Integer traderId);

	/**
	 * 更新线签收ID
	 * @param orderId
	 * @param onlineReceiptStatus
	 * @return
	 */
	int updateOnlineReceiptStatusById(@Param("orderId") Integer orderId, @Param("onlineReceiptStatus") Integer onlineReceiptStatus);

	/**
	 * @Description 查询订单中的交易者是否在白名单中
	 * @Param traderId
	 * @Return {@link int}
	 */
	int getTraderPeriodWhiteByTraderId(@Param("traderId") Integer traderId);

	/**
	 * @Description 获取白名单中有效的客户id集合
	 * @Param
	 * @Return {@link List< Integer>}
	 */
	List<Integer> getTraderIdListInWhitelist();

	/**
	 * @Description 获取白名单中有效的客户的销售账期订单
	 * @Param traderIdList
	 * @Return {@link List< Saleorder>}
	 */
	List<Saleorder> getSaleOrderByTraderIdListInWhitelist(List<Integer> traderIdList);

	/**
	 * @Description 根据traderId集合更新白名单中的是否下发WMS状态
	 * @Param validTraderIdList
	 * @Return {@link int}
	 */
	int updateIssueWMSByTraderIdList(List<Integer> validTraderIdList);

	/**
	 * @Description 根据订单id查询订单合同回传审核状态
	 * @Param saleorderId
	 * @Return {@link int}
	 */
	List<Integer> findContractVerifyStatusBySaleorderId(@Param("saleorderId") Integer saleorderId);

	/**
	 * @Description 更新T_SALEORDER_DATA的合同审核状态
	 * @Param saleorderId 订单ID
	 * @Param status 合同审核状态
	 * @Return {@link Integer}
	 */
	Integer updateContractVerifyStatus(@Param("saleorderId") Integer saleorderId, @Param("status") Integer status);

	/**
	 * 获取正在确认的订单ID信息
	 * @return
	 */
	List<Integer> getReceiptSaleOrderIdList();

	/**
	 * 批量更新订单在线确认状态
	 * @param orderReceiptStatusList
	 */
	void batchUpdateOrderReceiptStatus(@Param("orderReceiptStatusList") List<OrderReceiptStatusDto> orderReceiptStatusList);

	Integer confirmationFormAuditBySaleOrderId(@Param("saleOrderId") Integer saleOrderId);

    void updateConfirmationFormAuditById(@Param("saleOrderId") Integer saleOrderId, @Param("confirmationFormAudit") Integer confirmationFormAudit);


	void updateConfirmationFormUploadById(@Param("saleOrderId") Integer saleOrderId, @Param("confirmationFormUpload") Integer confirmationFormUpload);

	/**
	 * 根据订单创建时间，获取订单客户信息
	 * @param queryParams
	 * @return
	 */
	List<Integer> selectByAddTime(Map<String, Object> queryParams);

	/**
	 * 获取销售单
	 *
	 * @param orderId
	 * @return
	 */
	Saleorder selectSaleOrderByOrderId(@Param("orderId") Integer orderId);

	void updateContractReas(@Param("orderId") Integer orderId, @Param("comment") String comment);

	/**
	 * 获取确认单功能上线到目前为止，订单批次都审核通过，但订单确认单审核状态为待审核或驳回的订单
	 */
	List<Integer> selectConfirmOrderHistoryData();

	/**
	 * 获取确认单功能上线到目前为止，订单批次都审核通过，但订单确认单审核状态为审核中的订单
	 */
	List<Integer> selectReviewConfirmOrderHistoryData();

    Integer getSaleorderStatusBySaleorderId(Integer saleorderId);

	SaleorderAttributionInfoDto getSaleOrderAttributionInfo(@Param("saleOrderId") Integer saleOrderId);



	List<GoodsBelong> getGoodsBelong(List<Integer> goodsIds);

	List<SaleorderModifyApplyGoods> getModifyApplyList(Integer saleorderModifyApplyId);

	Saleorder getByBusinessChanceId(@Param("businessChanceId") Integer businessChanceId);

	Integer isExistSaleOrder(@Param("quoteOrderId") Integer quoteOrderId);

	/**
	 * 通话时，多个客户ID。同时查询销售订单，根据可发货时间，取最新的一个订单的客户ID返回
	 * @param traderIdList
	 * @return
	 */
	Integer queryNewestTraderIdBySaleOrder(@Param("traderIdList")List<Integer> traderIdList);

}

