package com.vedeng.logistics.model;

import com.vedeng.logistics.model.dto.LogisticsInfoDto;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class LogisticsDetail implements Serializable {

    private Integer logisticsDetailId;

    /**
     * 快递公司id
     */
    private Integer logisticsId;

    private String logisticsNo;

    private String content;

    private Long modTime;

    private String dateTime;//日期

    private String timeMillis;//时分秒

    private String detail;//详情

    private LogisticsInfoDto logisticsInfoDto = new LogisticsInfoDto();


}