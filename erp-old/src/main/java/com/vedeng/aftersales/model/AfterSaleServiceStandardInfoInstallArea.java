package com.vedeng.aftersales.model;

public class AfterSaleServiceStandardInfoInstallArea {
    private Long installAreaId;

    private Long serviceStandardInfoId;

    private String provinceCityJsonvalue;

    private Integer creator;

    private Integer updator;

    private String addTime;

    private String modTime;

    public Long getInstallAreaId() {
        return installAreaId;
    }

    public void setInstallAreaId(Long installAreaId) {
        this.installAreaId = installAreaId;
    }

    public Long getServiceStandardInfoId() {
        return serviceStandardInfoId;
    }

    public void setServiceStandardInfoId(Long serviceStandardInfoId) {
        this.serviceStandardInfoId = serviceStandardInfoId;
    }

    public String getProvinceCityJsonvalue() {
        return provinceCityJsonvalue;
    }

    public void setProvinceCityJsonvalue(String provinceCityJsonvalue) {
        this.provinceCityJsonvalue = provinceCityJsonvalue;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdator() {
        return updator;
    }

    public void setUpdator(Integer updator) {
        this.updator = updator;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime == null ? null : addTime.trim();
    }

    public String getModTime() {
        return modTime;
    }

    public void setModTime(String modTime) {
        this.modTime = modTime == null ? null : modTime.trim();
    }
}