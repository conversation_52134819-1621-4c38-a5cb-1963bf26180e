package com.wms.service.listenner;

import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SpecialDeliveryEnum;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.BuyorderService;
import com.wms.constant.CancelReasonConstant;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.service.*;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 采购退货单确认完成的监听器
 */
@Service
public class PurchaseReturnConfirmListener extends AbstractErpListenner{

    public static Logger LOGGER = LoggerFactory.getLogger(PurchaseReturnConfirmListener.class);

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private AfterSalesService afterSalesService;

    @Autowired
    @Qualifier("afterSalesMapper")
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseService;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Autowired
    private DoPutService doPutService;

    @Override
    @Transactional
    protected void doOnActionHappen(Object[] params) throws Exception {


        Integer afterSaleId = (Integer) params[0];

        AfterSales afterSales = afterSalesService.getAfterSalesById(afterSaleId);

        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSales.getOrderId());

        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+"的退货单:"+afterSales.getAfterSalesNo()+"确认完成,start========================");


        /*//如果有出单 还得取消对应的出库单
        CancelPoDto cancelPoDto = new CancelPoDto();
        cancelPoDto.setDocNo(afterSales.getAfterSalesNo());
        cancelPoDto.setPoType(WmsInterfaceOrderType.OUT_PURCHASE_RETURN);
        cancelPoDto.setErpCancelReason("售后单确认完成,请求取消");

        //取消出库单  HOLLIS ok
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.CANCEL_ORGGINCAL_SALESORDER);
        WmsResponse wmsResponse = wmsInterface.request(cancelPoDto);*/
        //如果有出单 还得取消对应的出库单
        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+"的退货单:"+afterSales.getAfterSalesNo()+"确认完成,取消原先的出库单=======");

        if(!cancelTypeService.cancelOutPurchaseReturnMethod(afterSales.getAfterSalesNo(),"售后单确认完成,请求取消")){
            throw new Exception("售后单对应的出库单在WMS作业中，无法确认完成");
        }

        // 获取专项发货的SKU关联的销售单 VDERP-7133
        List<Saleorder> saleorderList = buyorderGoodsMapper.getRelationSaleorderInfo(afterSales.getAfterSalesId());
        if (CollectionUtils.isNotEmpty(saleorderList)) {
            for (Saleorder saleorder:saleorderList) {
                if(!cancelTypeService.cancelOutSaleOutMethod(saleorder.getSaleorderNo(), CancelReasonConstant.SALE_DING_ORDER)){
                    LOGGER.info("wms不得取消下发失败 单号:{}",saleorder.getSaleorderNo());
                    throw new Exception("售后单对应的销售单在WMS作业中，无法确认完成");
                }
            }
        }

        //将售后单的状态改成已完结
        AfterSales afterSaleUpdate = new AfterSales();
        afterSaleUpdate.setAfterSalesId(afterSaleId);
        afterSaleUpdate.setAtferSalesStatus(2);
        afterSaleUpdate.setModTime(DateUtil.sysTimeMillis());
        afterSalesMapper.updateByPrimaryKeySelective(afterSaleUpdate);

        List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();
        //还需入库数量
        List<Integer> needInNumList = new ArrayList<>();
        buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(afterSales.getOrderId())
                           .stream()
                           .forEach(buyOrderGood -> {

                                PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                                detailItem.setSku(buyOrderGood.getSku());
                                detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);

                                //采购单数量 - 到货数量 - 已完结售后数量
                                detailItem.setOrderedQty(buyOrderGood.getNum()
                                                            - buyOrderGood.getArrivalNum()
                                                            - afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId()));
                               needInNumList.add(detailItem.getOrderedQty());

                               if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                                       buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                                   detailItem.setLotAtt07(buyorder.getBuyorderNo());
                               }

                               if(detailItem.getOrderedQty() > 0){
                                   details.add(detailItem);
                               }
                            });

        LOGGER.info("采购单:"+buyorder.getBuyorderNo()+"的退货单:"+afterSales.getAfterSalesNo()+"确认完成,下发原先的采购单");

        //更新采购单收货状态  VDERP-4222
        updateBuyorderStatus(buyorder,needInNumList);

        //下发原先的采购单
//        sendPurchaseOrder(buyorder,details);
        doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(),buyorder.getLogisticsComments());

        // 再次下发销售出库单 VDERP-7133
        if (CollectionUtils.isNotEmpty(saleorderList)) {
            saleorderList.stream().forEach(saleorder -> {
                User user = userMapper.getUserInfoByUserId(saleorder.getCreator());
                try {
                    logicalSaleorderChooseService.chooseLogicalSaleorder(saleorder, user);
                } catch (Exception e) {
                    LOGGER.info("销售单：{}", saleorder.getSaleorderNo()+"出库下传异常");
                }
            });
        }

        buyorderService.saveBuyorderInvoiceStatus(buyorder.getBuyorderId());
    }

    private void updateBuyorderStatus(Buyorder buyorder, List<Integer> needInNumList) {
        Optional<Integer> first = needInNumList.stream().filter(needNum -> needNum > 0).findFirst();
        if(!first.isPresent()){
            Buyorder update = new Buyorder();
            update.setBuyorderId(buyorder.getBuyorderId());
            update.setModTime(System.currentTimeMillis());
            update.setArrivalStatus(2);
            buyorderMapper.updateByPrimaryKeySelective(update);
        }
    }

    /**
     * 下发采购入库单
     * @param buyorder
     * @return
     */
    /*private void sendPurchaseOrder(Buyorder buyorder, List<PutPurchaseOrderGoodsDto> details) throws Exception{

        if(CollectionUtils.isEmpty(details)){
            return;
        }

        //若采购单是直发就不下发采购单
        if(buyorder.getDeliveryDirect() == 1){
            return;
        }

        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
        putPurchaseOrderDto.setDocNo(WmsCommonUtil.addTimestampForOrderNo(buyorder.getBuyorderNo()));
        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userMapper.getUserInfoByUserId(buyorder.getCreator());
        putPurchaseOrderDto.setPoReferenceA(StringUtil.isEmpty(user.getOrgName()) ? "/" : user.getOrgName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());
        putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());

        //入库单的详情
        putPurchaseOrderDto.setDetails(details);

        //wms采购入库单
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);
        wmsInterface.request(putPurchaseOrderDto);
    }*/

}
