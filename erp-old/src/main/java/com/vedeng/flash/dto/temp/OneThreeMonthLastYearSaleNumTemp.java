package com.vedeng.flash.dto.temp;

import lombok.*;

import java.math.BigDecimal;

/**
 * @program: erp.vedeng.com
 * @description: OneThreeMonthLastYearSaleNumTemp
 * @author: Pusan
 * @create: 2021-05-21 15:46
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class OneThreeMonthLastYearSaleNumTemp {

    /**
     * SkuId
     */
    private Integer skuId;

    /** 去年销售额 - 总 */
    private BigDecimal lastYearSum;

    /** 去年销售额 - 医械购 */
    private BigDecimal lastYearPart;

    /** 近三个月销售额 - 总 */
    private BigDecimal threeMonthSum;

    /** 近三个月销售额 - 医械购 */
    private BigDecimal threeMonthPart;

    /** 近一个月销售额 - 总 */
    private BigDecimal oneMonthSum;

    /** 近一个月销售额 - 医械购 */
    private BigDecimal oneMonthPart;
}
