package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.erp.buyorder.domain.entity.GeBusinessChance;
import com.vedeng.erp.buyorder.mapper.GeBusinessChanceMapper;
import com.vedeng.erp.buyorder.service.GeBusinessChanceInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class GeBusinessChanceInfoServiceImpl implements GeBusinessChanceInfoService {

    @Resource
    private GeBusinessChanceMapper geBusinessChanceMapper;

    @Override
    public boolean geBusinessChanceNoIsExist(String geBussinessChanceNo) {
        GeBusinessChance geBusinessChance = geBusinessChanceMapper.queryByNo(geBussinessChanceNo);
        if(geBusinessChance != null){
            return true;
        }else {
            return false;
        }
    }
}
