package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.model.po.WmsOutputOrder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;
/**
 * .该SKU没有审核中的外借单；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:00.
 * @author: Randy.Xu.
 */
@Service
public class SkuNoRelatedValidLendOutInvalitor extends GoodsInvalidatorChain {

    @Resource
    WmsOutputOrderMapper wmsOutputOrderMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        List<WmsOutputOrder> lendOutList = wmsOutputOrderMapper.getLendoutValidingBySkuNo(skuNo);
        if(CollectionUtils.isNotEmpty(lendOutList)){
            String collect = lendOutList.stream().map(e -> e.getOrderNo()).collect(Collectors.joining(","));
            String orderNo = SkuNoRelatedValidLendOutInvalidatorError.getMessage().replace("orderNo", collect);
            throw new InvalidatorChainException(SkuNoRelatedValidLendOutInvalidatorError.getCode(),orderNo);
        }
    }
}
