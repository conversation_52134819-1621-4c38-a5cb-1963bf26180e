package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeSupplierCommand;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierBankDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * dto 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeSupplierCommandConvertor extends BaseCommandMapStruct<KingDeeSupplierCommand,KingDeeSupplierDto> {

    /**
     * 转换
     *
     * @param dto KingDeeSupplierDto
     * @return KingDeeSupplierCommand
     */
    @Mapping(target = "FCreateOrgId.FNumber", source = "FCreateOrgId")
    @Mapping(target = "FUseOrgId.FNumber", source = "FUseOrgId")
    @Mapping(target = "FBaseInfo.FSupplyClassify", source = "FBaseInfo")
    @Mapping(target = "FSupplierClassify.FNumber", source = "FSupplierClassify")
    @Override
    KingDeeSupplierCommand toCommand(KingDeeSupplierDto dto);

    @Mapping(target = "FBankCode", source = "FBankCode")
    @Mapping(target = "FBankHolder", source = "FBankHolder")
    @Mapping(target = "FOpenBankName", source = "FOpenBankName")
    @Mapping(target = "FCNAPS", source = "FCNAPS")
    KingDeeSupplierCommand.KingDeeSupplierBankCommand toCommand(KingDeeSupplierBankDto dto);
}

