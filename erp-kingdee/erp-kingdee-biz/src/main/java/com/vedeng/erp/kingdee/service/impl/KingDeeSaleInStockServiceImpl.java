package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.convert.Convert;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeOutPutFeeResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleInStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeSaleInStockService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 销售增值税专票
 * @author: Jez
 * @date: 2022/12/2 15:46
 **/
@Service
@Slf4j
public class KingDeeSaleInStockServiceImpl extends KingDeeMqBaseServiceImpl<KingDeeReceiveCommonDto> implements KingDeeSaleInStockService {


    @Override
    public List<KingDeeSaleInStockQueryResultDto> getKingDeeSaleInStocks(String outInNo) {
        // 查询金蝶销售退货入库单
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.SAL_RETURN_STOCK);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBillNo")
                .value(outInNo).build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.query(queryParam, KingDeeSaleInStockQueryResultDto.class);
    }

    @Override
    public List<KingDeeOutPutFeeResultDto> getKingDeeSaleInvoiceFid(String formId, Integer invoiceId) {
        // 调用金蝶查询发票Fid
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(formId);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("F_QZOK_BDDJTID")
                .value(Convert.toStr(invoiceId)).build());
        queryParam.setFilterString(queryFilterDtos);
        List<KingDeeOutPutFeeResultDto> queryResult = kingDeeBaseApi.query(queryParam, KingDeeOutPutFeeResultDto.class);
        return queryResult;
    }

}
