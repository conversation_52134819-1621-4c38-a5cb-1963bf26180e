package com.vedeng.erp.system.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 修改日志注解
 * <AUTHOR>
 */
@Target({ElementType.TYPE,ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ChangeLogAnnotation {

    boolean isKey() default false;

    String compareName() default "";
}
