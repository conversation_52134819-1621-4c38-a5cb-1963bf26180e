package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleorderContractMapper;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class ConfirmationFileProcessor extends BaseProcessor<BatchSaleorderContractDto, KingDeeFileDataDto> {
    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;


    @Override
    public KingDeeFileDataDto doProcess(BatchSaleorderContractDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始推送确认单附件,dto:{}", JSONUtil.toJsonStr(dto));
        if(ObjectUtil.isNull(dto.getDataId())){
            log.info("确认单附件推送金蝶，订单数据不存在:{}",JSON.toJSONString(dto));
            return null;
        }
        //通过附件回写表判断该附件是否推送过
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.SALEORDER_CONTRACT)
                .erpId(dto.getAttachmentId().toString())
                .url(dto.getUrl())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }
        return KingDeeFileDataDto.builder()
                .fileName("确认单_"+dto.getConfirmationName()+"_"+dto.getAttachmentId()+"."+dto.getSuffix())
                .aliasFileName("确认单_"+dto.getConfirmationName()+"_"+dto.getAttachmentId()+"."+dto.getSuffix())
                .billNo(dto.getSaleorderNo())
                .formId(KingDeeFormConstant.SALEORDER_CONTRACT)
                .isLast(true)
                .fId(dto.getDataId().toString())
                .url(dto.getUrl())
                .erpId(dto.getAttachmentId().toString())
                .businessId(KingDeeFormConstant.SALEORDER_CONTRACT+dto.getAttachmentId().toString())
                .build();
    }
}
