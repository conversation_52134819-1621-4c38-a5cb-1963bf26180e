package com.vedeng.goods.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 14:17
 */
@Getter
@Setter
public class FairValueDto extends BaseDto {

    /**
     * 主键
     */
    private Integer fairValueId;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 公允价
     */
    private BigDecimal price;

    /**
     * 该sku对应的公允价信息集合
     */
    private List<HistoryDataDto> historyDataDtoList;

    /**
     * 最近的一次公允价对应的 销售单信息集合
     */
    private List<SaleOrderDataDto> saleOrderDataDtoList;
}