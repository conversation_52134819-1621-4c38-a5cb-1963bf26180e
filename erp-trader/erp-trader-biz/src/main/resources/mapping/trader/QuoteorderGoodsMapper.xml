<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.QuoteorderGoodsMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity">
    <!--@mbg.generated-->
    <!--@Table T_QUOTEORDER_GOODS-->
    <id column="QUOTEORDER_GOODS_ID" jdbcType="INTEGER" property="quoteorderGoodsId" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="IS_TEMP" jdbcType="BOOLEAN" property="isTemp" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="GOODS_NAME" jdbcType="VARCHAR" property="goodsName" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="CURRENCY_UNIT_ID" jdbcType="INTEGER" property="currencyUnitId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="DELIVERY_CYCLE" jdbcType="VARCHAR" property="deliveryCycle" />
    <result column="DELIVERY_DIRECT" jdbcType="BOOLEAN" property="deliveryDirect" />
    <result column="DELIVERY_DIRECT_COMMENTS" jdbcType="VARCHAR" property="deliveryDirectComments" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="SUPPLIER_NAME" jdbcType="VARCHAR" property="supplierName" />
    <result column="REFERENCE_COST_PRICE" jdbcType="DECIMAL" property="referenceCostPrice" />
    <result column="REFERENCE_PRICE" jdbcType="VARCHAR" property="referencePrice" />
    <result column="REFERENCE_DELIVERY_CYCLE" jdbcType="VARCHAR" property="referenceDeliveryCycle" />
    <result column="REPORT_STATUS" jdbcType="BOOLEAN" property="reportStatus" />
    <result column="REPORT_COMMENTS" jdbcType="VARCHAR" property="reportComments" />
    <result column="HAVE_INSTALLATION" jdbcType="BOOLEAN" property="haveInstallation" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="INSIDE_COMMENTS" jdbcType="VARCHAR" property="insideComments" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="LAST_REFERENCE_USER" jdbcType="INTEGER" property="lastReferenceUser" />
    <result column="IS_NEED_REPLY" jdbcType="BOOLEAN" property="isNeedReply" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    QUOTEORDER_GOODS_ID, QUOTEORDER_ID, IS_TEMP, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, 
    MODEL, UNIT_NAME, PRICE, CURRENCY_UNIT_ID, NUM, DELIVERY_CYCLE, DELIVERY_DIRECT, 
    DELIVERY_DIRECT_COMMENTS, REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, 
    REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, 
    GOODS_COMMENTS, INSIDE_COMMENTS, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER, 
    LAST_REFERENCE_USER, IS_NEED_REPLY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_GOODS
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_QUOTEORDER_GOODS
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="QUOTEORDER_GOODS_ID" keyProperty="quoteorderGoodsId" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_GOODS (QUOTEORDER_ID, IS_TEMP, GOODS_ID, 
      SKU, GOODS_NAME, BRAND_NAME, 
      MODEL, UNIT_NAME, PRICE, 
      CURRENCY_UNIT_ID, NUM, DELIVERY_CYCLE, 
      DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS, 
      REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, 
      REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, 
      REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, 
      GOODS_COMMENTS, INSIDE_COMMENTS, IS_DELETE, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, LAST_REFERENCE_USER, IS_NEED_REPLY
      )
    values (#{quoteorderId,jdbcType=INTEGER}, #{isTemp,jdbcType=BOOLEAN}, #{goodsId,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, 
      #{model,jdbcType=VARCHAR}, #{unitName,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, 
      #{currencyUnitId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER}, #{deliveryCycle,jdbcType=VARCHAR}, 
      #{deliveryDirect,jdbcType=BOOLEAN}, #{deliveryDirectComments,jdbcType=VARCHAR}, 
      #{registrationNumber,jdbcType=VARCHAR}, #{supplierName,jdbcType=VARCHAR}, #{referenceCostPrice,jdbcType=DECIMAL}, 
      #{referencePrice,jdbcType=VARCHAR}, #{referenceDeliveryCycle,jdbcType=VARCHAR}, 
      #{reportStatus,jdbcType=BOOLEAN}, #{reportComments,jdbcType=VARCHAR}, #{haveInstallation,jdbcType=BOOLEAN}, 
      #{goodsComments,jdbcType=VARCHAR}, #{insideComments,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{lastReferenceUser,jdbcType=INTEGER}, #{isNeedReply,jdbcType=BOOLEAN}
      )
  </insert>
  <insert id="insertSelective" keyColumn="QUOTEORDER_GOODS_ID" keyProperty="quoteorderGoodsId" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="isTemp != null">
        IS_TEMP,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="goodsName != null">
        GOODS_NAME,
      </if>
      <if test="brandName != null">
        BRAND_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="unitName != null">
        UNIT_NAME,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE,
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT,
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME,
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE,
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE,
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE,
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS,
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS,
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION,
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS,
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="lastReferenceUser != null">
        LAST_REFERENCE_USER,
      </if>
      <if test="isNeedReply != null">
        IS_NEED_REPLY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="isTemp != null">
        #{isTemp,jdbcType=BOOLEAN},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDirectComments != null">
        #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        #{reportStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportComments != null">
        #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        #{haveInstallation,jdbcType=BOOLEAN},
      </if>
      <if test="goodsComments != null">
        #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="lastReferenceUser != null">
        #{lastReferenceUser,jdbcType=INTEGER},
      </if>
      <if test="isNeedReply != null">
        #{isNeedReply,jdbcType=BOOLEAN},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER_GOODS
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="isTemp != null">
        IS_TEMP = #{isTemp,jdbcType=BOOLEAN},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="currencyUnitId != null">
        CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="deliveryCycle != null">
        DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="deliveryDirect != null">
        DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
      </if>
      <if test="deliveryDirectComments != null">
        DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="supplierName != null">
        SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      </if>
      <if test="referenceCostPrice != null">
        REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      </if>
      <if test="referencePrice != null">
        REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycle != null">
        REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      </if>
      <if test="reportStatus != null">
        REPORT_STATUS = #{reportStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportComments != null">
        REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      </if>
      <if test="haveInstallation != null">
        HAVE_INSTALLATION = #{haveInstallation,jdbcType=BOOLEAN},
      </if>
      <if test="goodsComments != null">
        GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      </if>
      <if test="insideComments != null">
        INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="lastReferenceUser != null">
        LAST_REFERENCE_USER = #{lastReferenceUser,jdbcType=INTEGER},
      </if>
      <if test="isNeedReply != null">
        IS_NEED_REPLY = #{isNeedReply,jdbcType=BOOLEAN},
      </if>
    </set>
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.QuoteorderGoodsEntity">
    <!--@mbg.generated-->
    update T_QUOTEORDER_GOODS
    set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      IS_TEMP = #{isTemp,jdbcType=BOOLEAN},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      PRICE = #{price,jdbcType=DECIMAL},
      CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
      DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BOOLEAN},
      DELIVERY_DIRECT_COMMENTS = #{deliveryDirectComments,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
      REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
      REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
      REFERENCE_DELIVERY_CYCLE = #{referenceDeliveryCycle,jdbcType=VARCHAR},
      REPORT_STATUS = #{reportStatus,jdbcType=BOOLEAN},
      REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
      HAVE_INSTALLATION = #{haveInstallation,jdbcType=BOOLEAN},
      GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
      INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      LAST_REFERENCE_USER = #{lastReferenceUser,jdbcType=INTEGER},
      IS_NEED_REPLY = #{isNeedReply,jdbcType=BOOLEAN}
    where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
  </update>

  <insert id="batchInsert" keyColumn="QUOTEORDER_GOODS_ID" keyProperty="quoteorderGoodsId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_QUOTEORDER_GOODS
    (QUOTEORDER_ID, IS_TEMP, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME, MODEL, UNIT_NAME,
    PRICE, CURRENCY_UNIT_ID, NUM, DELIVERY_CYCLE, DELIVERY_DIRECT, DELIVERY_DIRECT_COMMENTS,
    REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE, REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE,
    REPORT_STATUS, REPORT_COMMENTS, HAVE_INSTALLATION, GOODS_COMMENTS, INSIDE_COMMENTS,
    IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER, LAST_REFERENCE_USER, IS_NEED_REPLY
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.quoteorderId,jdbcType=INTEGER}, #{item.isTemp,jdbcType=BOOLEAN}, #{item.goodsId,jdbcType=INTEGER},
      #{item.sku,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR}, #{item.brandName,jdbcType=VARCHAR},
      #{item.model,jdbcType=VARCHAR}, #{item.unitName,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL},
      #{item.currencyUnitId,jdbcType=INTEGER}, #{item.num,jdbcType=INTEGER}, #{item.deliveryCycle,jdbcType=VARCHAR},
      #{item.deliveryDirect,jdbcType=BOOLEAN}, #{item.deliveryDirectComments,jdbcType=VARCHAR},
      #{item.registrationNumber,jdbcType=VARCHAR}, #{item.supplierName,jdbcType=VARCHAR},
      #{item.referenceCostPrice,jdbcType=DECIMAL}, #{item.referencePrice,jdbcType=VARCHAR},
      #{item.referenceDeliveryCycle,jdbcType=VARCHAR}, #{item.reportStatus,jdbcType=BOOLEAN},
      #{item.reportComments,jdbcType=VARCHAR}, #{item.haveInstallation,jdbcType=BOOLEAN},
      #{item.goodsComments,jdbcType=VARCHAR}, #{item.insideComments,jdbcType=VARCHAR},
      #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER},
      #{item.modTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, #{item.lastReferenceUser,jdbcType=INTEGER},
      #{item.isNeedReply,jdbcType=BOOLEAN})
    </foreach>
  </insert>
</mapper>