package com.vedeng.goods.domain.entity;


import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 公允价
 * @date 2022/12/12 18:57
 */
@Getter
@Setter
public class FairValueEntity extends BaseEntity {

    /**
     * 主键
     */
    private Integer fairValueId;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String skuName;

    /**
     * 商品id
     */
    private Integer goodsId;

    /**
     * 公允价
     */
    private BigDecimal price;

    /**
     * 当前sku历史数据
     */
    private JSONArray historyData;

    /**
     * 当前sku计算的销售订单数据
     */
    private JSONArray saleOrderData;

}
