<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="售后安调服务记录" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript"
        src='<%=basePath%>static/js/orderstream/aftersales/add_afterSales_at_service.js?rnd=${resourceVersionKey}'></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">

<form method="post" id="myform" class="layui-form">
    <div class="formpublic">
        <ul>
            <li>
                <div class="infor_name">
                    <lable>本次验收时间：</lable>
                </div>
                <div class="f_left  ">
                    <div class="form-blanks">
                        <input style="width: 210px" class="Wdate f_left input-smaller96 mr5 layui-input" type="text"
                               name="checkTimes" id="checkTimes"
                               autocomplete="off" placeholder="请选择日期" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               value="${nowTime}" format="yyyy-MM-dd"/>
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <lable>验收方式：</lable>
                </div>
                <div class="f_left">
                    <div>
                        <select name="checkType" id="checkType">
                            <option value="1" selected="selected">电话回访</option>
                            <option value="2">纸单验收</option>
                            <option value="3">短信通知</option>
                        </select>
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <lable>录音ID：</lable>
                </div>
                <div class="f_left">
                    <div>
                        <input style="width: 210px" class="layui-input" type="text" name="recordId" id="recordId">
                    </div>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <lable>验收结论：</lable>
                </div>
                <div class="f_left">
                    <div>
                        <select name="checkConclusion" id="checkConclusion">
                            <c:forEach items="${checkConclusionList}" var="sys">
                                <option value="${sys.sysOptionDefinitionId}" ${sys.sysOptionDefinitionId eq 4085 ? "selected='selected'" : ""}>${sys.title}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </li>
        </ul>
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    安调服务与商品信息 <span class="layui-badge layui-bg-blue">销售订单中未收货商品无法添加服务记录</span>
                </div>
            </div>
            <table class="table">
                <thead>
                <tr>
                    <th class="wid5">选择</th>
                    <th class="wid15">订货号</th>
                    <th>产品名称</th>
                    <th>品牌</th>
                    <th>型号</th>
                    <th>本次服务数量</th>
                    <th style="width: 200px">服务商品序列号</th>
                    <th>补充码</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach var="dto" items="${detailDtoList}" varStatus="status">
                    <tr>
                        <td>
                            <input type="checkbox" name="b_checknox" onclick="canelAll(this)" value="${status.count}" lay-ignore >
                            <input type="hidden" name="afterSalesGoodsId" id="afterSalesGoodsId_${status.count}"
                                   value="${dto.afterSalesGoodsId}">
                        </td>
                        <td>
                                ${dto.sku}
                            <input type="hidden" name="sku" id="sku_${status.count}" value="${dto.sku}"/>
                            <input type="hidden" name="deliveryTime" id="deliveryTime_${status.count}"
                                   value="${dto.deliveryTime}"/>
                            <input type="hidden" name="arrivalTime" id="arrivalTime_${status.count}"
                                   value="${dto.arrivalTime}"/>
                        </td>
                        <td class="text-left">
                                ${dto.skuName}
                            <input type="hidden" name="skuName" id="skuName_${status.count}" value="${dto.skuName}"/>
                        </td>
                        <td>
                                ${dto.brand}
                            <input type="hidden" name="brand" id="brand_${status.count}" value="${dto.brand}"/>
                        </td>
                        <td>
                                ${dto.model}
                            <input type="hidden" name="model" id="model_${status.count}" value="${dto.model}"/>
                        </td>
                        <td>
                                ${dto.num}
                            <input type="hidden" name="num" value="${dto.num}"/>
                        </td>
                        <td>
                            <select id="serialNumber_${status.count}"
                                    onchange="chooseSnInfo(${status.count})" lay-search lay-ignore
                                    <c:if test="${ dto.snSelectType eq -2 ||  dto.snSelectType eq -3}">disabled</c:if>>
                                <option value="0"
                                        <c:if test="${ dto.snSelectType eq 0 }">selected="selected"</c:if>
                                        <c:if test="${ dto.snSelectType eq -1}">hidden</c:if>>下拉选择
                                </option>
                                <option value="-1"
                                        <c:if test="${ dto.snSelectType  eq -1}">selected="selected"</c:if>
                                        <c:if test="${ dto.snSelectType  eq 0}">hidden</c:if>>自定义补充码
                                </option>
                                <option value="-2"
                                        <c:if test="${ dto.snSelectType  eq -2}">selected="selected"</c:if>
                                        <c:if test="${ dto.snSelectType  eq 0 || dto.snSelectType eq -1}">hidden</c:if>>直发手填补充码
                                </option>
                                <option value="-3"
                                        <c:if test="${ dto.snSelectType  eq -3}">selected="selected"</c:if>
                                        <c:if test="${ dto.snSelectType  eq 0 || dto.snSelectType eq -1}">hidden</c:if>>历史出库手填补充码
                                </option>

                                <c:forEach items="${dto.snList}" var="snItem">
                                    <c:if test="${!empty snItem}">
                                        <option value="${snItem}">${snItem}</option>
                                    </c:if>
                                </c:forEach>

                            </select>
                        </td>
                        <td>
                            <input lay-ignore type="text" name="supple_code" id="supple_code_${status.count}"
                                   value="" placeholder="请填写补充码" <c:if test="${ dto.snSelectType eq 0}">disabled</c:if>>
                        </td>
                    </tr>
                </c:forEach>
                </tbody>
            </table>
            <div class="table-style4">
                    <input type="checkbox" name="allcheck" lay-ignore onclick="selectall(this)">
                    <span>全选</span>
            </div>
        </div>
        <div class="tcenter">
            <input type="hidden" id="afterSalesId" name="afterSalesId" value="${aftersalesId}">
            <input type="hidden" name="fileNames" id="fileNames">
            <input type="hidden" name="fileUris" id="fileUris">
            <div>
                <input style="width: 90px" class="layui-btn layui-btn-normal" onclick="submitInfo()" value="提  交"/>
                <span  class="layui-btn layui-btn-normal pop-new-data" layerparams='{"width":"500px","height":"300px","title":"上传凭证",
                                                    "link":"/order/afterSalesCommon/preAddServiceRecordAttachment.do"}'>上传凭证</span>
            </div>
        </div>
    </div>
</form>
<script>
    layui.use('form', function () {
    });
</script>
<%@ include file="../../common/footer.jsp" %>
