<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.billsync.dao.BankBillExtMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.billsync.task.model.entity.BankBillExtDo">
        <id column="BANK_BILL_ID" jdbcType="INTEGER" property="bankBillId" />
        <result column="COMPANY_ID" jdbcType="INTEGER" property="companyId" />
        <result column="BANK_TAG" jdbcType="TINYINT" property="bankTag" />
        <result column="TRAN_FLOW" jdbcType="VARCHAR" property="tranFlow" />
        <result column="TRANDATE" jdbcType="DATE" property="trandate" />
        <result column="TRANTIME" jdbcType="TIME" property="trantime" />
        <result column="REAL_TRANDATE" jdbcType="DATE" property="realTrandate" />
        <result column="REAL_TRANDATETIME" jdbcType="TIMESTAMP" property="realTrandatetime" />
        <result column="CRE_TYP" jdbcType="VARCHAR" property="creTyp" />
        <result column="CRE_NO" jdbcType="VARCHAR" property="creNo" />
        <result column="MESSAGE" jdbcType="VARCHAR" property="message" />
        <result column="AMT" jdbcType="DECIMAL" property="amt" />
        <result column="AMT1" jdbcType="VARCHAR" property="amt1" />
        <result column="FLAG1" jdbcType="TINYINT" property="flag1" />
        <result column="ACCNO2" jdbcType="VARCHAR" property="accno2" />
        <result column="ACC_BANKNO" jdbcType="VARCHAR" property="accBankno" />
        <result column="ACC_NAME1" jdbcType="VARCHAR" property="accName1" />
        <result column="FLAG2" jdbcType="TINYINT" property="flag2" />
        <result column="BFLOW" jdbcType="VARCHAR" property="bflow" />
        <result column="DET_NO" jdbcType="VARCHAR" property="detNo" />
        <result column="DET" jdbcType="VARCHAR" property="det" />
        <result column="RLTV_ACCNO" jdbcType="VARCHAR" property="rltvAccno" />
        <result column="CADBANK_NM" jdbcType="VARCHAR" property="cadbankNm" />
        <result column="STATUS" jdbcType="TINYINT" property="status" />
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
        <result column="MATCHED_AMOUNT" jdbcType="DECIMAL" property="matchedAmount" />
        <result column="OVRLSTTN_TRCK_NO" jdbcType="VARCHAR" property="ovrlsttnTrckNo" />
        <result column="IS_CONSISTENCY" jdbcType="TINYINT" property="isConsistency" />
        <result column="MATCHED_OBJECT" jdbcType="INTEGER" property="matchedObject" />
        <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
        <result column="RELATED_BILL" jdbcType="VARCHAR" property="relatedBill" />
        <result column="IS_FEE" jdbcType="TINYINT" property="isFee" />
        <result column="SYNC_DATE" jdbcType="DATE" property="syncDate" />
        <result column="CAPITAL_SEARCH_FLOW" jdbcType="VARCHAR" property="capitalSearchFlow" />
        <result column="RECEIPT_NAME" jdbcType="VARCHAR" property="receiptName"/>
        <result column="RECEIPT_URL" jdbcType="VARCHAR" property="receiptUrl"/>
    </resultMap>
    <sql id="Base_Column_List">
    BANK_BILL_ID, COMPANY_ID, BANK_TAG, TRAN_FLOW, TRANDATE, TRANTIME, REAL_TRANDATE,
    REAL_TRANDATETIME, CRE_TYP, CRE_NO, MESSAGE, AMT, AMT1, FLAG1, ACCNO2, ACC_BANKNO,
    ACC_NAME1, FLAG2, BFLOW, DET_NO, DET, RLTV_ACCNO, CADBANK_NM, `STATUS`, COMMENTS,
    MATCHED_AMOUNT, OVRLSTTN_TRCK_NO, IS_CONSISTENCY, MATCHED_OBJECT, ORDER_NO, RELATED_BILL,
    IS_FEE, SYNC_DATE,CAPITAL_SEARCH_FLOW,RECEIPT_NAME,RECEIPT_URL
  </sql>
    <insert id="insertSingleByObject" keyColumn="BANK_BILL_ID" keyProperty="bankBillId" parameterType="com.vedeng.billsync.task.model.entity.BankBillExtDo" useGeneratedKeys="true">
        insert into T_BANK_BILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                COMPANY_ID,
            </if>
            <if test="bankTag != null">
                BANK_TAG,
            </if>
            <if test="tranFlow != null">
                TRAN_FLOW,
            </if>
            <if test="trandate != null">
                TRANDATE,
            </if>
            <if test="trantime != null">
                TRANTIME,
            </if>
            <if test="realTrandate != null">
                REAL_TRANDATE,
            </if>
            <if test="realTrandatetime != null">
                REAL_TRANDATETIME,
            </if>
            <if test="creTyp != null">
                CRE_TYP,
            </if>
            <if test="creNo != null">
                CRE_NO,
            </if>
            <if test="message != null">
                MESSAGE,
            </if>
            <if test="amt != null">
                AMT,
            </if>
            <if test="amt1 != null">
                AMT1,
            </if>
            <if test="flag1 != null">
                FLAG1,
            </if>
            <if test="accno2 != null">
                ACCNO2,
            </if>
            <if test="accBankno != null">
                ACC_BANKNO,
            </if>
            <if test="accName1 != null">
                ACC_NAME1,
            </if>
            <if test="flag2 != null">
                FLAG2,
            </if>
            <if test="bflow != null">
                BFLOW,
            </if>
            <if test="detNo != null">
                DET_NO,
            </if>
            <if test="det != null">
                DET,
            </if>
            <if test="rltvAccno != null">
                RLTV_ACCNO,
            </if>
            <if test="cadbankNm != null">
                CADBANK_NM,
            </if>
            <if test="status != null">
                `STATUS`,
            </if>
            <if test="comments != null">
                COMMENTS,
            </if>
            <if test="matchedAmount != null">
                MATCHED_AMOUNT,
            </if>
            <if test="ovrlsttnTrckNo != null">
                OVRLSTTN_TRCK_NO,
            </if>
            <if test="isConsistency != null">
                IS_CONSISTENCY,
            </if>
            <if test="matchedObject != null">
                MATCHED_OBJECT,
            </if>
            <if test="orderNo != null">
                ORDER_NO,
            </if>
            <if test="relatedBill != null">
                RELATED_BILL,
            </if>
            <if test="isFee != null">
                IS_FEE,
            </if>
            <if test="syncDate != null">
                SYNC_DATE,
            </if>
            <if test="capitalSearchFlow != null">
                CAPITAL_SEARCH_FLOW,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyId != null">
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="bankTag != null">
                #{bankTag,jdbcType=TINYINT},
            </if>
            <if test="tranFlow != null">
                #{tranFlow,jdbcType=VARCHAR},
            </if>
            <if test="trandate != null">
                #{trandate,jdbcType=DATE},
            </if>
            <if test="trantime != null">
                #{trantime,jdbcType=TIME},
            </if>
            <if test="realTrandate != null">
                #{realTrandate,jdbcType=DATE},
            </if>
            <if test="realTrandatetime != null">
                #{realTrandatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creTyp != null">
                #{creTyp,jdbcType=VARCHAR},
            </if>
            <if test="creNo != null">
                #{creNo,jdbcType=VARCHAR},
            </if>
            <if test="message != null">
                #{message,jdbcType=VARCHAR},
            </if>
            <if test="amt != null">
                #{amt,jdbcType=DECIMAL},
            </if>
            <if test="amt1 != null">
                #{amt1,jdbcType=VARCHAR},
            </if>
            <if test="flag1 != null">
                #{flag1,jdbcType=TINYINT},
            </if>
            <if test="accno2 != null">
                #{accno2,jdbcType=VARCHAR},
            </if>
            <if test="accBankno != null">
                #{accBankno,jdbcType=VARCHAR},
            </if>
            <if test="accName1 != null">
                #{accName1,jdbcType=VARCHAR},
            </if>
            <if test="flag2 != null">
                #{flag2,jdbcType=TINYINT},
            </if>
            <if test="bflow != null">
                #{bflow,jdbcType=VARCHAR},
            </if>
            <if test="detNo != null">
                #{detNo,jdbcType=VARCHAR},
            </if>
            <if test="det != null">
                #{det,jdbcType=VARCHAR},
            </if>
            <if test="rltvAccno != null">
                #{rltvAccno,jdbcType=VARCHAR},
            </if>
            <if test="cadbankNm != null">
                #{cadbankNm,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="comments != null">
                #{comments,jdbcType=VARCHAR},
            </if>
            <if test="matchedAmount != null">
                #{matchedAmount,jdbcType=DECIMAL},
            </if>
            <if test="ovrlsttnTrckNo != null">
                #{ovrlsttnTrckNo,jdbcType=VARCHAR},
            </if>
            <if test="isConsistency != null">
                #{isConsistency,jdbcType=TINYINT},
            </if>
            <if test="matchedObject != null">
                #{matchedObject,jdbcType=INTEGER},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="relatedBill != null">
                #{relatedBill,jdbcType=VARCHAR},
            </if>
            <if test="isFee != null">
                #{isFee,jdbcType=TINYINT},
            </if>
            <if test="syncDate != null">
                #{syncDate,jdbcType=DATE},
            </if>
            <if test="capitalSearchFlow != null">
                #{capitalSearchFlow,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getAliBankBillExtDoByTranFlow" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_BANK_BILL
        where TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
        and BANK_TAG = 4
        and COMPANY_ID =1
    </select>
    <select id="getWechatBankBillExtDoByTranFlow"
            resultType="com.vedeng.billsync.task.model.entity.BankBillExtDo">
        select
        <include refid="Base_Column_List" />
        from T_BANK_BILL
        where TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR}
        And BANK_TAG = 5
        AND COMPANY_ID = 1
    </select>
    <select id="getBankBillById" resultType="com.vedeng.billsync.task.model.entity.BankBillExtDo">
        select
        <include refid="Base_Column_List"/>
        from T_BANK_BILL
        where BANK_BILL_ID = #{bankBillId,jdbcType = INTEGER}
    </select>

    <update id="deleteByDate">
        update T_BANK_BILL
        set COMPANY_ID = 0
        WHERE SYNC_DATE = #{queryDate,jdbcType=DATE}
        AND BANK_TAG = #{tag,jdbcType=TINYINT}
    </update>
    <update id="updateSingleByObjectSelective">
        update T_BANK_BILL
        <set>
            <if test="companyId != null">
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="bankTag != null">
                BANK_TAG = #{bankTag,jdbcType=BOOLEAN},
            </if>
            <if test="tranFlow != null">
                TRAN_FLOW = #{tranFlow,jdbcType=VARCHAR},
            </if>
            <if test="trandate != null">
                TRANDATE = #{trandate,jdbcType=DATE},
            </if>
            <if test="trantime != null">
                TRANTIME = #{trantime,jdbcType=TIME},
            </if>
            <if test="realTrandate != null">
                REAL_TRANDATE = #{realTrandate,jdbcType=DATE},
            </if>
            <if test="realTrandatetime != null">
                REAL_TRANDATETIME = #{realTrandatetime,jdbcType=TIMESTAMP},
            </if>
            <if test="creTyp != null">
                CRE_TYP = #{creTyp,jdbcType=VARCHAR},
            </if>
            <if test="creNo != null">
                CRE_NO = #{creNo,jdbcType=VARCHAR},
            </if>
            <if test="message != null">
                MESSAGE = #{message,jdbcType=VARCHAR},
            </if>
            <if test="amt != null">
                AMT = #{amt,jdbcType=DECIMAL},
            </if>
            <if test="amt1 != null">
                AMT1 = #{amt1,jdbcType=VARCHAR},
            </if>
            <if test="flag1 != null">
                FLAG1 = #{flag1,jdbcType=BOOLEAN},
            </if>
            <if test="accno2 != null">
                ACCNO2 = #{accno2,jdbcType=VARCHAR},
            </if>
            <if test="accBankno != null">
                ACC_BANKNO = #{accBankno,jdbcType=VARCHAR},
            </if>
            <if test="accName1 != null">
                ACC_NAME1 = #{accName1,jdbcType=VARCHAR},
            </if>
            <if test="flag2 != null">
                FLAG2 = #{flag2,jdbcType=BOOLEAN},
            </if>
            <if test="bflow != null">
                BFLOW = #{bflow,jdbcType=VARCHAR},
            </if>
            <if test="detNo != null">
                DET_NO = #{detNo,jdbcType=VARCHAR},
            </if>
            <if test="det != null">
                DET = #{det,jdbcType=VARCHAR},
            </if>
            <if test="rltvAccno != null">
                RLTV_ACCNO = #{rltvAccno,jdbcType=VARCHAR},
            </if>
            <if test="cadbankNm != null">
                CADBANK_NM = #{cadbankNm,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `STATUS` = #{status,jdbcType=BOOLEAN},
            </if>
            <if test="comments != null">
                COMMENTS = #{comments,jdbcType=VARCHAR},
            </if>
            <if test="ovrlsttnTrckNo != null">
                OVRLSTTN_TRCK_NO = #{ovrlsttnTrckNo,jdbcType=VARCHAR},
            </if>
            <if test="isConsistency != null">
                IS_CONSISTENCY = #{isConsistency,jdbcType=BOOLEAN},
            </if>
            <if test="orderNo != null">
                ORDER_NO = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="relatedBill != null">
                RELATED_BILL = #{relatedBill,jdbcType=VARCHAR},
            </if>
            <if test="isFee != null">
                IS_FEE = #{isFee,jdbcType=BOOLEAN},
            </if>
            <if test="syncDate != null">
                SYNC_DATE = #{syncDate,jdbcType=DATE},
            </if>
            <if test="capitalSearchFlow != null">
                CAPITAL_SEARCH_FLOW = #{capitalSearchFlow}
            </if>
        </set>
        where BANK_BILL_ID = #{bankBillId,jdbcType=INTEGER}
    </update>
    <update id="updataBankBillMatchedAmount">
        update T_BANK_BILL
        set MATCHED_AMOUNT = #{newAmount,jdbcType=DECIMAL}
        where BANK_BILL_ID =  #{bankBillId,jdbcType=INTEGER}
    </update>

    <update id="updataBankBillReceiptUrl">
        update T_BANK_BILL
        set RECEIPT_URL = #{receiptUrl,jdbcType=VARCHAR}
        where BANK_BILL_ID =  #{bankBillId,jdbcType=INTEGER}
    </update>

    <select id="queryNeedDownLoadBankBill" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        WHERE
        TRANDATE <![CDATA[>=]]> #{beginTime,jdbcType=DATE}
        AND TRANDATE <![CDATA[<=]]> #{endTime,jdbcType=DATE}
        AND BANK_TAG = #{bankTag,jdbcType=INTEGER}
        AND RECEIPT_URL is null
    </select>

    <select id="queryBankBillOfNoReceiptUrl" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        WHERE
        TRANDATE <![CDATA[>=]]> #{beginTime,jdbcType=VARCHAR}
        <if test="endTime != null">
            AND TRANDATE <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
        </if>
        AND BANK_TAG in
        <foreach collection="bankTagList" item="bankTag" separator="," open="(" close=")" index="index">
            #{bankTag,jdbcType=INTEGER}
        </foreach>
        AND RECEIPT_URL is null
        AND BANK_BILL_ID >  #{bankBillId,jdbcType=INTEGER} order by BANK_BILL_ID ASC limit 1000
    </select>

    <select id="queryBankBillByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where BANK_TAG = 1
        AND BANK_BILL_ID in
        <foreach collection="bankBillIds" item="item" separator="," open="(" close=")" index="index">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

    <select id="findBankByTranFlow" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where
        FLAG1 = 1
        AND (IS_FEE = 0 OR IS_FEE IS NULL)
        AND STATUS = 0
        AND (AMT - MATCHED_AMOUNT) > 0
        AND BANK_TAG not in (4,5)
        AND TRAN_FLOW = #{receiveRecordBankNum,jdbcType=VARCHAR}
    </select>

    <select id="findAlipayByCapitalSearchFlow" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM T_BANK_BILL
        where
        FLAG1 = 1
        AND (IS_FEE = 0 OR IS_FEE IS NULL)
        AND STATUS = 0
        AND (AMT - MATCHED_AMOUNT) > 0
        AND BANK_TAG = 4
        AND CAPITAL_SEARCH_FLOW = #{receiveRecordAlipayNum,jdbcType=VARCHAR}
    </select>

    <update id="updataReceiptUrl">
        update T_BANK_BILL
        set RECEIPT_URL = #{receiptUrl,jdbcType=DECIMAL}
        where BANK_BILL_ID =  #{bankBillId,jdbcType=INTEGER}
    </update>

    <select id="getMatchInfo" resultType="com.vedeng.order.model.Saleorder">
        select a.*
        from (
                 SELECT s.*,sinfo.RECEIVED_AMOUNT FROM (SELECT
                                                            if(COALESCE(e.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0) - IFNULL(abs(f.gtk_amount),0),0) <![CDATA[<]]> 0
                                                                ,0,COALESCE(e.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0) - IFNULL(abs(f.gtk_amount),0),0)) as TOTAL_AMOUNT
                                                                ,e.SALEORDER_ID,
                                                            e.SALEORDER_NO,
                                                            e.PREPAID_AMOUNT,
                                                            e.HAVE_ACCOUNT_PERIOD,
                                                            e.PAYMENT_STATUS,
                                                            e.ACCOUNT_PERIOD_AMOUNT,
                                                            e.TRADER_ID,

                                                            e.TRADER_NAME,
                                                            e.RETAINAGE_AMOUNT,
                                                            e.TRADER_CONTACT_NAME,
                                                            e.VALID_STATUS,
                                                            e.IS_PAYMENT,
                                                            e.VALID_TIME,
                                                            e.STATUS
                                                        FROM
                                                            T_SALEORDER e
                                                                left JOIN
                                                            (
                                                                SELECT
                                                                    sum(bb.NUM*cc.PRICE) as tk_amount,aa.ORDER_ID
                                                                FROM
                                                                    T_AFTER_SALES aa
                                                                        left JOIN
                                                                    T_AFTER_SALES_GOODS bb
                                                                    ON
                                                                        aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
                                                                        left JOIN
                                                                    T_SALEORDER_GOODS cc
                                                                    ON
                                                                        bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
                                                                WHERE
                                                                    aa.TYPE = 539
                                                                  AND
                                                                    aa.SUBJECT_TYPE = 535
                                                                  AND
                                                                    aa.VALID_STATUS = 1
                                                                  AND
                                                                    aa.ATFER_SALES_STATUS in (1,2)
                                                                GROUP BY
                                                                    aa.ORDER_ID
                                                            ) as b
                                                            ON
                                                                e.SALEORDER_ID = b.ORDER_ID

                                                                LEFT JOIN
                                                            (
                                                                select
                                                                    sum(ac.REAL_REFUND_AMOUNT) as gtk_amount,ab.ORDER_ID
                                                                from
                                                                    T_AFTER_SALES ab
                                                                        left join
                                                                    T_AFTER_SALES_DETAIL ac
                                                                    on
                                                                        ab.AFTER_SALES_ID = ac.AFTER_SALES_ID
                                                                where
                                                                    ab.SUBJECT_TYPE = 535
                                                                  and
                                                                    ab.TYPE = 543
                                                                  and
                                                                    ab.ATFER_SALES_STATUS = 2
                                                                group by
                                                                    ab.ORDER_ID

                                                            ) as f
                                                            on
                                                                e.SALEORDER_ID = f.ORDER_ID
                                                        WHERE
                                                            (e.TRADER_NAME =#{accName1,jdbcType=VARCHAR}
                                                                OR
                                                             e.TRADER_CONTACT_NAME =#{accName1,jdbcType=VARCHAR})
                                                       ) AS s
                                                           LEFT JOIN
                                                       (SELECT
                                                            c.SALEORDER_ID,
                                                            if(sum(IF(d.TRADER_TYPE = 1 or d.TRADER_TYPE=4,ABS(b.AMOUNT),if(d.TRADER_TYPE = 3,0,- (ABS(b.AMOUNT))))) is NULL,0,sum(IF(d.TRADER_TYPE = 1 or d.TRADER_TYPE=4,ABS(b.AMOUNT),if(d.TRADER_TYPE = 3,0,- (ABS(b.AMOUNT))))))AS RECEIVED_AMOUNT
                                                        FROM
                                                            T_SALEORDER c
                                                                LEFT JOIN T_CAPITAL_BILL_DETAIL b ON b.RELATED_ID = c.SALEORDER_ID and	 b.ORDER_TYPE = 1
                                                                LEFT JOIN T_CAPITAL_BILL d ON d.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
                                                        WHERE(c.TRADER_NAME =#{accName1,jdbcType=VARCHAR}
                                                            OR
                                                              c.TRADER_CONTACT_NAME =#{accName1,jdbcType=VARCHAR})
                                                        GROUP BY c.SALEORDER_ID)
                                                           sinfo on s.SALEORDER_ID = sinfo.SALEORDER_ID
             ) a
        where ((a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT = #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR})
            or (a.TOTAL_AMOUNT-a.RECEIVED_AMOUNT-a.RETAINAGE_AMOUNT != #{amt,jdbcType=DECIMAL} and a.TRADER_CONTACT_NAME = #{accName1,jdbcType=VARCHAR}))
          and a.VALID_STATUS=1 and a.IS_PAYMENT!=1 and a.STATUS!=3 and a.TOTAL_AMOUNT>0
        order by a.VALID_TIME desc
    </select>
    <select id="getMatchInfoByOrderNo" resultType="com.vedeng.order.model.Saleorder">
        select a.*
        from (
                 SELECT s.*,sinfo.RECEIVED_AMOUNT FROM (SELECT
                                                            if(COALESCE(e.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0) - IFNULL(abs(f.gtk_amount),0),0) <![CDATA[<]]> 0
                                                                ,0,COALESCE(e.TOTAL_AMOUNT - IFNULL(abs(b.tk_amount),0) - IFNULL(abs(f.gtk_amount),0),0)) as TOTAL_AMOUNT
                                                                ,e.SALEORDER_ID,
                                                            e.SALEORDER_NO,
                                                            e.PREPAID_AMOUNT,
                                                            e.HAVE_ACCOUNT_PERIOD,
                                                            e.PAYMENT_STATUS,
                                                            e.ACCOUNT_PERIOD_AMOUNT,
                                                            e.TRADER_ID,

                                                            e.TRADER_NAME,
                                                            e.RETAINAGE_AMOUNT,
                                                            e.TRADER_CONTACT_NAME,
                                                            e.VALID_STATUS,
                                                            e.IS_PAYMENT,
                                                            e.VALID_TIME,
                                                            e.STATUS
                                                        FROM
                                                            T_SALEORDER e
                                                                left JOIN
                                                            (
                                                                SELECT
                                                                    sum(bb.NUM*cc.PRICE) as tk_amount,aa.ORDER_ID
                                                                FROM
                                                                    T_AFTER_SALES aa
                                                                        left JOIN
                                                                    T_AFTER_SALES_GOODS bb
                                                                    ON
                                                                        aa.AFTER_SALES_ID = bb.AFTER_SALES_ID
                                                                        left JOIN
                                                                    T_SALEORDER_GOODS cc
                                                                    ON
                                                                        bb.ORDER_DETAIL_ID = cc.SALEORDER_GOODS_ID
                                                                WHERE
                                                                    aa.TYPE = 539
                                                                  AND
                                                                    aa.SUBJECT_TYPE = 535
                                                                  AND
                                                                    aa.VALID_STATUS = 1
                                                                  AND
                                                                    aa.ATFER_SALES_STATUS in (1,2)
                                                                GROUP BY
                                                                    aa.ORDER_ID
                                                            ) as b
                                                            ON
                                                                e.SALEORDER_ID = b.ORDER_ID

                                                                LEFT JOIN
                                                            (
                                                                select
                                                                    sum(ac.REAL_REFUND_AMOUNT) as gtk_amount,ab.ORDER_ID
                                                                from
                                                                    T_AFTER_SALES ab
                                                                        left join
                                                                    T_AFTER_SALES_DETAIL ac
                                                                    on
                                                                        ab.AFTER_SALES_ID = ac.AFTER_SALES_ID
                                                                where
                                                                    ab.SUBJECT_TYPE = 535
                                                                  and
                                                                    ab.TYPE = 543
                                                                  and
                                                                    ab.ATFER_SALES_STATUS = 2
                                                                group by
                                                                    ab.ORDER_ID

                                                            ) as f
                                                            on
                                                                e.SALEORDER_ID = f.ORDER_ID
                                                        WHERE
                                                            (e.SALEORDER_NO =#{saleorderNo,jdbcType=VARCHAR}) and e.STATUS NOT IN (0,3)
                                                       ) AS s
                                                           LEFT JOIN
                                                       (SELECT
                                                            c.SALEORDER_ID,
                                                            if(sum(IF(d.TRADER_TYPE = 1 or d.TRADER_TYPE=4,ABS(b.AMOUNT),if(d.TRADER_TYPE = 3,0,- (ABS(b.AMOUNT))))) is NULL,0,sum(IF(d.TRADER_TYPE = 1 or d.TRADER_TYPE=4,ABS(b.AMOUNT),if(d.TRADER_TYPE = 3,0,- (ABS(b.AMOUNT))))))AS RECEIVED_AMOUNT
                                                        FROM
                                                            T_SALEORDER c
                                                                LEFT JOIN T_CAPITAL_BILL_DETAIL b ON b.RELATED_ID = c.SALEORDER_ID and	 b.ORDER_TYPE = 1
                                                                LEFT JOIN T_CAPITAL_BILL d ON d.CAPITAL_BILL_ID = b.CAPITAL_BILL_ID
                                                        WHERE(c.SALEORDER_NO =#{saleorderNo,jdbcType=VARCHAR})
                                                        GROUP BY c.SALEORDER_ID)
                                                           sinfo on s.SALEORDER_ID = sinfo.SALEORDER_ID
             ) a
        order by a.VALID_TIME desc
    </select>

    <select id="queryCcbByTranDate" resultMap="BaseResultMap">
        SELECT *
        FROM T_BANK_BILL
        WHERE TRANDATE <![CDATA[>=]]> #{beginDate,jdbcType=VARCHAR}
          AND TRANDATE <![CDATA[<=]]> #{endDate,jdbcType=VARCHAR}
          AND BANK_TAG = 1
          AND RECEIPT_URL IS NULL
    </select>
</mapper>