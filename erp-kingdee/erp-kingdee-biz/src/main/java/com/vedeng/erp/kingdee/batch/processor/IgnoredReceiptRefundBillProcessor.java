package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveRefundEntryDto;
import com.vedeng.erp.kingdee.enums.KingDeePayBillUseTypeEnums;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 已忽略 收款退款单 构造器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/24 16:59
 */
@Service
@Slf4j
public class IgnoredReceiptRefundBillProcessor extends BaseProcessor<BatchBankBillDto, KingDeeReceiveRefundBillDto> {

    @Autowired
    private BankBillIgnoreRecordApiService bankBillIgnoreRecordApiService;

    @Autowired
    private PayVedengBankApiService payVedengBankApiService;

    private static final String BD_CUSTOMER = "BD_Customer"; //收款退款单

    private static final String FRECTYPE = "对公"; //收款方类型
    @Override
    public KingDeeReceiveRefundBillDto doProcess(BatchBankBillDto input, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("已忽略 收款退款单 组装数据{}", JSONObject.toJSONString(input));
        KingDeeReceiveRefundBillDto receiveRefundBillDto = new KingDeeReceiveRefundBillDto();
        KingDeeReceiveRefundEntryDto receiveRefundEntryDto = new KingDeeReceiveRefundEntryDto();
        // 查询忽略时的信息
        BankBillIgnoreRecordDto bankBillIgnoreRecordDto = bankBillIgnoreRecordApiService.selectByBankBillId(input.getBankBillId());
        if (Objects.isNull(bankBillIgnoreRecordDto) || !BD_CUSTOMER.equals(bankBillIgnoreRecordDto.getContactUnitType())) {
            log.info("查询忽略的信息为空{}", JSONObject.toJSONString(input));
            return null;
        }
        log.info("查询忽略的信息{}", JSONObject.toJSONString(bankBillIgnoreRecordDto));
        //单据内码
        receiveRefundBillDto.setFID("0");//默认为0
        //单据编号
        receiveRefundBillDto.setFBillNo(KingDeeConstant.IGNORE +input.getBankBillId().toString());
//        //单据日期
//        Date tranDate = input.getTranDate();
//        //转为yyyy-MM-dd格式
//        String tranTimeStr = DateUtil.formatDate(tranDate);
//        receiveRefundBillDto.setFDATE(tranTimeStr);
        receiveRefundBillDto.setFDATE(DateUtil.formatDateTime(input.getRealTrandatetime()));// VDERP-16088  款对接金蝶业务日期规则调整-精确到时分秒
        receiveRefundBillDto.setFQzokPzgsywdh(input.getOrderNo());
        //往来单位类型
        receiveRefundBillDto.setFCONTACTUNITTYPE(BD_CUSTOMER);
        //往来单位
        receiveRefundBillDto.setFCONTACTUNIT(bankBillIgnoreRecordDto.getContactUnitNo());
        //收款单位类型
        receiveRefundBillDto.setFRECTUNITTYPE(BD_CUSTOMER);
        //收款单位
        receiveRefundBillDto.setFRECTUNIT(bankBillIgnoreRecordDto.getContactUnitNo());
        //交易流水号
        receiveRefundBillDto.setF_QZOK_LSH(input.getTranFlow());
        //明细
        receiveRefundEntryDto.setFSETTLETYPEID(KingDeePayBillUseTypeEnums.WIRE_TRANSFER.getCode());
        switch (input.getBankTag()){
            case 4:
                receiveRefundEntryDto.setFSETTLETYPEID(KingDeePayBillUseTypeEnums.ALI_PAY.getCode());
                break;
            case 5:
                receiveRefundEntryDto.setFSETTLETYPEID(KingDeePayBillUseTypeEnums.WE_CHAT_PAY.getCode());
                break;
        }
        //收款金额
        receiveRefundEntryDto.setFRECTOTALAMOUNTFOR(input.getAmt().subtract(input.getMatchedAmount()));
        //实退金额
        receiveRefundEntryDto.setFREFUNDAMOUNTFOR(input.getAmt().subtract(input.getMatchedAmount()));
        //我方银行账号
        PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryBankInfo(input.getBankTag());
        if (Objects.isNull(payVedengBankDto)) {
            log.info("查询 我方银行账号 信息为空{}",JSONObject.toJSONString(input));
            return null;
        }
        receiveRefundEntryDto.setFACCOUNTID(payVedengBankDto.getPayBankNo());
        //收款账户账号
        receiveRefundEntryDto.setFOPPOSITEBANKACCOUNT(input.getAccno2());
        //收款账户名称
        receiveRefundEntryDto.setFOPPOSITECCOUNTNAME(input.getAccName1());
        //收款账户开户行
        receiveRefundEntryDto.setFOPPOSITEBANKNAME(input.getCadBankNm());
        //收款账户联行号 VDERP-15926不传联行号
        receiveRefundEntryDto.setFCNAPS("");
        //原始订单号
        receiveRefundEntryDto.setF_QZOK_YSDDH("");
        //归属业务单号
        receiveRefundEntryDto.setF_QZOK_GSYWDH("");
        //业务类型
        receiveRefundEntryDto.setF_QZOK_YWLX("其他业务付款");
        //收款方类型  对公：0（企业） 对私：1（个人）
        receiveRefundEntryDto.setFRecType(FRECTYPE.equals(bankBillIgnoreRecordDto.getTradeSubject()) ? "0" : "1");
        receiveRefundBillDto.getFREFUNDBILLENTRY().add(receiveRefundEntryDto);
        log.info("已忽略 收款退款单 组装数据完毕{}", JSONObject.toJSONString(receiveRefundEntryDto));
        return receiveRefundBillDto;
    }
}
