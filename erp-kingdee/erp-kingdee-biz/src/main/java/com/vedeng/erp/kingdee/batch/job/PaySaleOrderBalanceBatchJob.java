package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchCapitalBillDto;
import com.vedeng.erp.kingdee.batch.processor.AfterSalePayByBalanceNegativeProcessor;
import com.vedeng.erp.kingdee.batch.processor.AfterSalePayByBalanceProcessor;
import com.vedeng.erp.kingdee.batch.processor.SaleOrderPayByBalanceProcessor;
import com.vedeng.erp.kingdee.batch.writer.SaleOrderPayByBalanceWriter;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 销售订单客户余额支付
 * （应收单调整单）
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class PaySaleOrderBalanceBatchJob extends BaseJob {

    @Autowired
    private SaleOrderPayByBalanceProcessor saleOrderPayByBalanceProcessor;
    @Autowired
    private SaleOrderPayByBalanceWriter saleOrderPayByBalanceWriter;
    @Autowired
    private AfterSalePayByBalanceProcessor afterSalePayByBalanceProcessor;
    @Autowired
    private AfterSalePayByBalanceNegativeProcessor afterSalePayByBalanceNegativeProcessor;



    public Job saleOrderPayByBalanceBatchJob() {
        return jobBuilderFactory.get("saleOrderPayByBalanceBatchJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(saleOrderPayByBalanceStep())
                .next(afterSalePayByBalanceStep())
                .next(afterSalePayByBalanceNegativeStep())
                .build();
    }

    /**
     * 客户余额 支付销售订单
     */
    private Step saleOrderPayByBalanceStep() {
        return stepBuilderFactory.get("余额-销售订单收款流水推送")
                .<BatchCapitalBillDto, KingDeeNeedReceiveDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(saleOrderPayByBalanceReader(null,null))
                .listener(baseReadListener)
                .processor(saleOrderPayByBalanceProcessor)
                .writer(saleOrderPayByBalanceWriter)
                .listener(baseProcessListener)
                .build();
    }

    /**
     * 客户余额 支付售后手续费
     */
    private Step afterSalePayByBalanceStep() {
        return stepBuilderFactory.get("客户余额-销售售后手续费收款流水推送")
                .<BatchCapitalBillDto, KingDeeNeedReceiveDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(afterSalePayByBalanceReader(null,null))
                .listener(baseReadListener)
                .processor(afterSalePayByBalanceProcessor)
                .writer(saleOrderPayByBalanceWriter)
                .listener(baseProcessListener)
                .build();
    }

    /**
     * 客户余额 支付售后手续费（负向）
     * VDERP-14416
     */
    private Step afterSalePayByBalanceNegativeStep() {
        return stepBuilderFactory.get("客户余额-销售售后手续费收款流水推送（负向）")
                .<BatchCapitalBillDto, KingDeeNeedReceiveDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(afterSalePayByBalanceNegativeReader(null,null))
                .listener(baseReadListener)
                .processor(afterSalePayByBalanceNegativeProcessor)
                .writer(saleOrderPayByBalanceWriter)
                .listener(baseProcessListener)
                .build();
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> saleOrderPayByBalanceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                 @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto batchCapitalBillDto = new BatchCapitalBillDto();
        batchCapitalBillDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        batchCapitalBillDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(),"saleOrderPayByBalanceReader", batchCapitalBillDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> afterSalePayByBalanceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                    @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto batchCapitalBillDto = new BatchCapitalBillDto();
        batchCapitalBillDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        batchCapitalBillDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(),"afterSalePayByBalanceReader", batchCapitalBillDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchCapitalBillDto> afterSalePayByBalanceNegativeReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchCapitalBillDto batchCapitalBillDto = new BatchCapitalBillDto();
        batchCapitalBillDto.setBeginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime());
        batchCapitalBillDto.setEndTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime());
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchCapitalBillDto.class.getSimpleName(),"afterSalePayByBalanceNegativeReader", batchCapitalBillDto);
    }
}
