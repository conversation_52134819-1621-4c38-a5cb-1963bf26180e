package com.vedeng.goods.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @create 2022−11-01 下午7:11
 * @description
 */
@Data
public class RegistrationFeedbackDto {
    /**
     *  主键id
     */
    private Integer registrationFeedbackRecordId;
    /**
     *  注册证id
     */
    private Integer registrationNumberId;
    /**
     *  处理状态 0未处理 1已处理
     */
    private Integer state;
    /**
     *  问题类型：1：印章缺失/不清晰 2：打印内容模糊 3：水印内容不当 4：其他
     */
    private Integer problemType;
    /**
     *  问题备注
     */
    private String problemRemark;
    /**
     *  注册证号/备案凭证号
     */
    private String registrationNumber;
    /**
     *  商品等级
     */
    private Integer goodsLevelNo;
    /**
     *  商品档位
     */
    private Integer goodsPositionNo;
    /**
     *  文件类型：1：附件 2：源文件
     */
    private Integer fileType;
    /**
     *  使用目的类型：1：投标/陪标 2：客户询价 3：其他
     */
    private Integer purposeType;
    /**
     *  使用目的备注
     */
    private String purposeRemark;
    /**
     *  归属经理
     */
    private Integer assignmentManagerId;
    /**
     *  归属助理
     */
    private Integer assignmentAssistantId;
    private Long addTime;
    private Integer creator;
    private Long modTime;
    private Integer updater;
    /**
     * 处理人ID
     */
    private Integer dealerUserId;
    /**
     * 问题回复
     */
    private String messageReply;
    /**
     * 首营id
     */
    private Integer firstEngageId;

    private Date addTimeDate;

    /**
     * 反馈人(姓名)
     */
    private String creatorLabel;

    /**
     * 处理人(姓名)
     */
    private String dealerLabel;
}
