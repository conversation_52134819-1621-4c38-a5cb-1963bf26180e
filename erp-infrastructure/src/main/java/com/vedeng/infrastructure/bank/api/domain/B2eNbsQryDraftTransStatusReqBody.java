package com.vedeng.infrastructure.bank.api.domain;

import lombok.Data;

/**
 * @Description 查询贴现交易状态请求体
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/12
 */
@Data
public class B2eNbsQryDraftTransStatusReqBody {

    /**
     * 客户技术请求流水号，同一客户请勿重复
     */
    private String trnId;

    /**
     * 签约账号
     */
    private String custAccount;

    /**
     * 操作类型:
     * 1：申请
     * 2：签收
     */
    private String operType;

    /**
     * 原交易客户业务请求流水号
     */
    private String insId;

    /**
     * 业务类型
     * 【申请方】
     * 01: 出票交付（提示收票）
     * 02: 背书转出
     * 03: 申请贴现
     * 04: 申请质押
     * 05: 出票登记（提交出票）
     * 06: 提示承兑
     * 07: 发出托收（提示付款）
     * 08: 保证申请
     * 09: 解质押申请
     * 10: 未用退回（撤票）
     * 11: 发出追索
     * 12: 发出清偿
     * 13: 不得转让撤销申请
     * 【签收方】：
     * 01: 收票签收
     * 02: 转让签收（收到背书）
     * 03: 收到质押
     * 04: 收到承兑
     * 05: 收到托收（提示付款）
     * 06: 收到提示保证（保证申请）
     * 07: 收到解除质押
     * 08: 收到追索
     * 09: 收到清偿申请
     * 10: 收到不得转让撤销
     */
    private String busiStage;

    /**
     * 票据类型，不传为全部
     * AC01：银承
     * AC02：商承
     */
    private String billType;

    /**
     * 出票日起yyyy-MM-dd
     */
    private String beginAcptDt;

    /**
     * 出票日止yyyy-MM-dd
     */
    private String endAcptDt;

    /**
     * 票据到期日起yyyy-MM-dd
     */
    private String beginEndDate;

    /**
     * 票据到期日止yyyy-MM-dd
     */
    private String endDate;

    /**
     * 操作日期起yyyy-MM-dd
     */
    private String beginApplyDt;

    /**
     * 操作日期止yyyy-MM-dd
     */
    private String endApplyDt;

    /**
     * 票据（包）号码
     */
    private String billNo;

    /**
     * 当前页码(从1开始，不传默认为1）
     */
    private String pageNo;

    /**
     * 每页数据条数（默认10条，最大每页100条）
     */
    private String pageSize;
}