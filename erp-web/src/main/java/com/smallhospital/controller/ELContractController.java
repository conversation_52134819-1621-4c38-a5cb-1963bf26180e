package com.smallhospital.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.smallhospital.constant.HospitalConstant;
import com.smallhospital.dto.ELContractSkuDTO;
import com.smallhospital.dto.ElResultDTO;
import com.smallhospital.dto.ValidatorResult;
import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ELContractVO;
import com.smallhospital.model.vo.ElContractSkuVO;
import com.smallhospital.model.vo.ElTraderVo;
import com.smallhospital.service.ELContractService;
import com.smallhospital.service.ELContractSkuService;
import com.smallhospital.service.ELTraderService;
import com.smallhospital.service.ElSkuService;
import com.smallhospital.service.impl.remote.SynProductInfoService;
import com.smallhospital.service.impl.remote.SysContractInfoService;
import com.smallhospital.service.impl.validator.*;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.service.TraderCustomerService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *  小医院客户列表
 */
@Controller
@RequestMapping("/el/contract")
public class ELContractController extends BaseController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ELContractController.class);

    @Autowired
    private ELContractService contractService;

    @Autowired
    private ELContractSkuService contractSkuService;

    @Autowired
    private ELTraderService traderService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private ElSkuService skuService;

    @Autowired
    @Qualifier("userMapper")
    private UserMapper userMapper;

    @Autowired
    private SysContractInfoService sysContractInfoService;

    @Autowired
    private SynProductInfoService synProductInfoService;

    @Autowired
    private ContractEffectiveValidator contractEffectiveValidator;

    @Autowired
    private ContractProductSynValidator contractProductSynValidator;

    @Autowired
    private ContractHasProductValidator contractHasProductValidator;

    @Autowired
    private ContractProductNumOverFlowValidator contractProductNumOverFlowValidator;

    @Autowired
    private ContractSkuOverlapValidator contractSkuOverlapValidator;

    /**
     * 销售总监角色id
     */
    public static final Integer SALE_DIRECTOR_ROLE_ID = 4;

    /**
     * 销售主管角色id
     */
    public static final Integer SALE_CHARGE_ROLE_ID = 5;

    /**
     * 所有的sku
     */
    private List<Integer> elSkuIds;


    @ResponseBody
    @RequestMapping(value="/index")
    public ModelAndView index(HttpServletRequest request, ELContractVO contract,
                              @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                              @RequestParam(required = false) Integer pageSize,
                              HttpSession session){
        ModelAndView mv = new ModelAndView();
        User user =(User)session.getAttribute(ErpConst.CURR_USER);

        //查询当前操作员角色是否是 产品专员,产品主管,产品总监中的一个
        List<Integer> auditRoles = Arrays.asList(new Integer[]{SALE_DIRECTOR_ROLE_ID,SALE_CHARGE_ROLE_ID});
        List<Integer> userRoleIdLists = user.getRoles().stream().map(Role::getRoleId).collect(Collectors.toList());

        userRoleIdLists.retainAll(auditRoles);

        if(CollectionUtils.isNotEmpty(userRoleIdLists)){
//            contract.setAuditStatus(1);
            mv.addObject("auditFlag","true");
        }else{
            contract.setOwner(user.getUserId());
            mv.addObject("auditFlag","false");
        }

        //查询集合
        Page page = getPageTag(request,pageNo,pageSize);
        List<ELContractVO> contractList = contractService.querylistPage(contract, page);

        if(CollectionUtils.isNotEmpty(contractList)){
            contractList.stream().filter(contractVO->{

                return contractVO.getOwner() != 0;
            }).forEach((ELContractVO contractVO)->{
                if(contractVO.getModifyStatus()==1){
                    ELContractVO modifyContract=contractService.getContractModifyInfo(contractVO.getElContractId());
                    if(modifyContract!=null){
                        contractVO.setSignDate(modifyContract.getSignDate());
                        contractVO.setContractNumber(modifyContract.getContractNumber());
                        contractVO.setContractValidityDateEnd(modifyContract.getContractValidityDateEnd());
                        contractVO.setContractValidityDateStart(modifyContract.getContractValidityDateStart());
                    }
                }
                contractVO.setOwnerName(userMapper.selectByPrimaryKey(contractVO.getOwner()).getUsername());
            });
        }

        mv.addObject("contractList",contractList);
        mv.addObject("contract", contract);
        mv.addObject("page", page);
        mv.setViewName("el/contract/index");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/toAddContract")
    public ModelAndView toAddContract(HttpServletRequest request) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        //查出登录人所属的客户列表
        List<ElTraderVo> tradeList = this.traderService.findCustomerByLoginId(user.getUserId());
        mv.addObject("tradeList",tradeList);

        mv.setViewName("el/contract/addContract");
        return mv;
    }

    /**
     * 新增合同基本信息
     * @param request
     * @param contract
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addContract")
    public ModelAndView addContract(HttpServletRequest request,ELContractVO contract) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();
        try{

            contract.setOwner(user.getUserId());
            contract.setCreator(user.getUserId());
            contract.setUpdator(user.getUserId());
            contract.setSignDate(DateUtil.convertLong(contract.getSignDateStr(), DateUtil.DATE_FORMAT));
            contract.setContractValidityDateStart(DateUtil.convertLong(contract.getContractValidityDateStartStr(), DateUtil.DATE_FORMAT));
            contract.setContractValidityDateEnd(DateUtil.convertLong(contract.getContractValidityDateEndStr(), DateUtil.DATE_FORMAT));
            contract.setStatus(1);

            int contractId = contractService.saveContractInfo(contract);
            mv.addObject("url", "./toEditContract.do?contractId=" + contractId);
            return success(mv);
        }catch (Exception e){
            log.error("【addContract】处理异常",e);
            return fail(mv);
        }
    }



    /**
     * 编辑合同页面
     * @param request
     * @param contractId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/toEditContract")
    public ModelAndView toEditContract(HttpServletRequest request,@Param(value = "contractId") Integer contractId,Integer prePageType) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        //查出登录人所属的客户列表
        List<ElTraderVo> tradeList = this.traderService.findCustomerByLoginId(user.getUserId());
        mv.addObject("tradeList",tradeList);
        ELContractVO contractInfo = this.contractService.findById(contractId);
        mv.addObject("contractInfo", contractInfo);
        Trader trader=traderCustomerService.getBaseTraderByTraderId(contractInfo.getTraderId());
        mv.addObject("contractTrader",trader);
        List<ElContractSkuVO> skuList = this.contractSkuService.findByContractId(contractId);
        mv.addObject("skuList", skuList);
        if (prePageType == null) {
            mv.addObject("canEdit", true);
        } else if (prePageType == 1001) {
            mv.addObject("canEdit", false);
        }
        mv.setViewName("el/contract/editContract");
        return mv;
    }


    @ResponseBody
    @RequestMapping(value = "/toModifyContract")
    public ModelAndView toModifyContract(HttpServletRequest request,@Param(value = "contractId") Integer contractId,Integer prePageType) {
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        //查出登录人所属的客户列表
        List<ElTraderVo> tradeList = this.traderService.findCustomerByLoginId(user.getUserId());
        mv.addObject("tradeList", tradeList);

        ELContractVO contractInfo = this.contractService.findById(contractId);
        mv.addObject("modifyStatus", contractInfo.getModifyStatus());

        ELContractVO modifyContract = this.contractService.getContractModifyInfo(contractId);
        if (modifyContract != null) {
            List<ElContractSkuVO> skus = this.contractSkuService.getModifySkus(modifyContract.getElContractModifyId());
            mv.addObject("skuList", skus);
            mv.addObject("contractInfo", modifyContract);
            Trader trader=traderCustomerService.getBaseTraderByTraderId(modifyContract.getTraderId());
            mv.addObject("contractTrader",trader);
        }else{
            mv.addObject("contractInfo", contractInfo);
            Trader trader=traderCustomerService.getBaseTraderByTraderId(contractInfo.getTraderId());
            mv.addObject("contractTrader",trader);
            List<ElContractSkuVO> skuList = this.contractSkuService.findByContractId(contractId);
            mv.addObject("skuList", skuList);
        }


        if (prePageType == null) {
            mv.addObject("canEdit", true);
        } else if (prePageType == 1001) {
            mv.addObject("canEdit", false);
        }
        mv.setViewName("el/contract/modifyContract");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/toBatchAddContractSku")
    public ModelAndView toBatchAddContractSku(HttpServletRequest request,@Param(value = "contractId") Integer contractId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("contractId",contractId);
        mv.setViewName("el/contract/batchAddContractSkus");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/toBatchModifyContractSku")
    public ModelAndView toBatchAddContractModifySku(HttpServletRequest request,@Param(value = "contractId") Integer contractId,Integer contractModifyId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("contractId",contractId);
        mv.addObject("contractModifyId",contractModifyId);
        mv.setViewName("el/contract/batchModifyContractSku");
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/batchModifyContractSku")
    public ResultInfo<?> batchAddContractSkuModify(HttpServletRequest request, @Param(value = "contractId") Integer contractId
            ,Integer contractModifyId, @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<List<ElContractSku>> resultInfo = new ResultInfo<>();
        List<Integer> htSkuIds;
        Integer newModifyId=0;
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        List<ElContractSkuVO> preskus=null;
        FileInputStream fileInputStream=null;
        try {
//            if(contractModifyId==null){
//                ELContractVO elContractVO=contractService.findById(contractId);
//                contractService.saveModifyContract(elContractVO,user);
//                newModifyId=elContractVO.getElContractModifyId();
//            }
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);

            if(fileInfo.getCode() != 0){
                return resultInfo;
            }
            fileInputStream=new FileInputStream(new File(fileInfo.getFilePath()));
            // 获取excel路径
            Workbook workbook = WorkbookFactory.create(fileInputStream);
            // 获取第一面sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 起始行
            int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
            int endRowNum = sheet.getLastRowNum();// 结束行

            List<ElContractSku> skuLists = new ArrayList<ElContractSku>();// 保存Excel中读取的数据

            //查询库中所有的skuID
            elSkuIds = this.skuService.findAllSkuIds();
            //查询当前合同的sku
            if(contractModifyId==null) {
                preskus=this.contractSkuService.findByContractId(contractId);
                htSkuIds = preskus.stream().map(ElContractSkuVO::getSkuId).collect(Collectors.toList());
            }else{
                htSkuIds = this.contractSkuService.getModifySkus(contractModifyId).stream().map(ElContractSkuVO::getSkuId).collect(Collectors.toList());
            }
            //查询当前客户其他有效合同中的所有sku,保证sku不重叠
            ELContractVO contractVO = this.contractService.findById(contractId);
            List<Integer> otherContractSkuIds = this.contractService.findOtherValidSkus(contractVO);

            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                ElContractSku sku = parseRowContent(sheet,rowNum,resultInfo,contractModifyId,user,htSkuIds,otherContractSkuIds,2);
                skuLists.add(sku);
            }
//            if(CollectionUtils.isNotEmpty(preskus)){
//                for(ElContractSkuVO s:preskus){
//                    if(s==null){
//                        continue;
//                    }
//                    ElContractSku sku=new ElContractSku();
//                    BeanUtils.copyProperties(s,sku);
//                    sku.setContractModifyId(contractModifyId);
//                    skuLists.add(0,sku);
//                }
//            }
//            contractSkuService.batchAddContractSkus(skuLists);
            resultInfo.setData(skuLists);
            resultInfo.setCode(0);
            resultInfo.setMessage("批量导入成功");
        } catch (Exception e) {
            logger.error("saveBatchElSkus:", e);
        }finally {
            if(fileInputStream!=null){
                try {
                    fileInputStream.close();
                }catch (Exception ex){
                    logger.error("小医院上传合同关流失败：",ex);
                }
            }
        }
        return resultInfo;
    }


    @ResponseBody
    @RequestMapping(value = "/batchAddContractSku")
    public ResultInfo<?> batchAddContractSku(HttpServletRequest request, @Param(value = "contractId") Integer contractId,
                                             @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();

        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

            if(fileInfo.getCode() != 0){
                return resultInfo;
            }

            // 获取excel路径
            fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
            workbook = WorkbookFactory.create(fileInputStream);
            // 获取第一面sheet
            Sheet sheet = workbook.getSheetAt(0);
            // 起始行
            int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
            int endRowNum = sheet.getLastRowNum();// 结束行

            List<ElContractSku> skuLists = new ArrayList<ElContractSku>();// 保存Excel中读取的数据

            //查询库中所有的skuID
            elSkuIds = this.skuService.findAllSkuIds();
            //查询当前合同的sku
            List<Integer> htSkuIds = this.contractSkuService.findByContractId(contractId).stream().map(ElContractSkuVO::getSkuId).collect(Collectors.toList());

            //查询当前客户其他有效合同中的所有sku,保证sku不重叠
            ELContractVO contractVO = this.contractService.findById(contractId);
            List<Integer> otherContractSkuIds = this.contractService.findOtherValidSkus(contractVO);
//            ELContractSkuDto elContractSkuDto = new ELContractSkuDto();
            List<Integer> skuIds = new ArrayList<>();

            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                ElContractSku sku = parseRowContent(sheet,rowNum,resultInfo,contractId,user,htSkuIds,otherContractSkuIds,1);
                skuIds.add(sku.getSkuId());
                skuLists.add(sku);
            }
            ELContractSkuDTO elContractDTO = new ELContractSkuDTO();
            elContractDTO.setSkuIdList(skuIds);
            //OP校验产品上架
            List<ContractSku> skus = this.contractService.checkSkuIdsIsOnSale(elContractDTO);
//            List<Integer> skuIdList = this.contractService.findNotExitsIds(skuIds);
            //已上架的SKU
            List<Integer> contratSkus = skus.stream().map(ContractSku :: getProductId).collect(Collectors.toList());
            skuIds.removeAll(contratSkus);

            if(skuIds.size() !=0 ){
                resultInfo.setMessage("产品sku:"  + skuIds.toString() + "未在医械购上架");
            } else{
                contractSkuService.batchAddContractSkus(skuLists);
                contractSkuService.batchUpdateContractSkus(skus);
                resultInfo.setCode(0);
                resultInfo.setMessage("批量导入成功");
            }
        } catch (Exception e) {
            logger.error("saveBatchElSkus:", e);
        }finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    logger.error("【batchAddContractSku】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    logger.error("【batchAddContractSku】处理异常",e);
                }
            }
        }
        return resultInfo;
    }

    private ElContractSku parseRowContent(Sheet sheet,int rowNum,ResultInfo<?> resultInfo,
                                          Integer contractId,User user,List<Integer> htSkuIds,List<Integer> otherContractSkuIds,Integer type) throws Exception{

        // 获取excel的值
        ElContractSku sku = new ElContractSku();

        Row row = sheet.getRow(rowNum);

        for(int cellNum=0;cellNum <=1;cellNum++){

            Cell cell = row.getCell(cellNum);

            if(cellNum == 0){
                validatorSkuId(sku,cell,resultInfo,rowNum,cellNum,htSkuIds,otherContractSkuIds);
            }

            if(cellNum == 1){
                validatorSkuPrice(sku,cell,resultInfo,rowNum,cellNum);
            }
        }

        sku.setAddTime(DateUtil.gainNowDate());
        sku.setUpdateTime(DateUtil.gainNowDate());
        sku.setCreator(user.getUserId());
        sku.setUpdator(user.getUserId());
        if(type==1) {
            sku.setContractId(contractId);
        }else{
            sku.setContractModifyId(contractId);
        }
        return sku;
    }

    /**
     * 校验sku价格
     * @param sku
     * @param cell
     * @param resultInfo
     * @param rowNum
     * @param cellNum
     * @throws Exception
     */
    private void validatorSkuPrice(ElContractSku sku, Cell cell, ResultInfo<?> resultInfo, int rowNum, int cellNum) throws Exception{

        //非数字类型
        if (cell == null || cell.getCellType() != CellType.NUMERIC) {// cell==null单元格空白（无内容，默认空）
            resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
        }

        Double pice = cell.getNumericCellValue();
        if (!(pice.toString()).matches("[0-9]{1,14}\\.{0,1}[0-9]{0,2}")) {
            resultInfo.setMessage(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不符合规则，请验证！");
            throw new Exception(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不符合规则，请验证！");
        }

        BigDecimal bd = new BigDecimal(pice);
        sku.setContractPrice(bd.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    /**
     * 校验sku
     * @param sku
     * @param cell
     * @param resultInfo
     * @param rowNum
     * @param cellNum
     * @throws Exception
     */
    private void validatorSkuId(ElContractSku sku,Cell cell,ResultInfo resultInfo,int rowNum,int cellNum,List<Integer> htSkuIds,List<Integer> otherContractSkuIds) throws Exception{
        //非数字类型
        if (cell == null || cell.getCellType() != CellType.STRING) {// cell==null单元格空白（无内容，默认空）
            resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
        }

        String skuOrderNum = cell.getStringCellValue();
        int skuId = Integer.valueOf(skuOrderNum.substring(1));

        if (!(skuId + "").matches("^[1-9]+[0-9]*$")) {
            resultInfo.setMessage(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不是数字，请验证！");
            throw new Exception(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不是数字，请验证！");
        }

        //校验是否是小医院的sku
        if(!elSkuIds.contains(skuId)){
            resultInfo.setMessage(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：sku值非医疗的SKU，请验证！");
            throw new Exception(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：sku值非医疗的SKU，请验证！");
        }

        //合同已经存在该sku
        if(htSkuIds.contains(skuId)){
            resultInfo.setMessage(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：sku已经存在合同中，请验证！");
            throw new Exception(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：sku已经存在合同中，请验证！");
        }

        //sku有重叠
        if(otherContractSkuIds.contains(skuId)){
            resultInfo.setMessage(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：sku与其他合同的SKU有重叠，请验证！");
            throw new Exception(
                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：sku与其他合同的SKU有重叠，请验证！");
        }

        sku.setSkuId(skuId);
    }

    /**
     * <b>Description:</b>开始终止合同流程<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/4/17
     */
    @ResponseBody
    @RequestMapping(value="/startTermination")
    public ResultInfo terminate(HttpServletRequest request, Integer contractId){
        if(contractId==null){
            return new ResultInfo(-1,"合同id不得为空");
        }
        return contractService.startTermination(contractId);
    }

    @ResponseBody
    @RequestMapping(value="/manager/checkTermination")
    public ResultInfo checkTerminationByManager(HttpServletRequest request,ELContractVO contract){
        if(contract==null||contract.getElContractId()==null){
            return new ResultInfo(-1,"合同id不得为空");
        }
       return contractService.checkTermination(contract, HospitalConstant.MANAGER_TERMINATE_CHECK_TYPE);
    }

    @ResponseBody
    @RequestMapping(value="/customer/checkTermination")
    public ElResultDTO checkTerminationByCustomer(HttpServletRequest request, @RequestBody ELContractVO contract){
        if(contract==null||contract.getContractId()==null){
            return ElResultDTO.error("合同标识不得为空");
        }
        contract.setElContractId(contract.getContractId());
       ResultInfo resultInfo= contractService.checkTermination(contract, HospitalConstant.CUSTOMER_TERMINATE_CHECK_TYPE);
        if(resultInfo.getCode()==0){
            return ElResultDTO.ok();
        }else{
            return ElResultDTO.error(resultInfo.getMessage());
        }
    }

    @ResponseBody
    @RequestMapping(value="/delContractSku")
    public ResultInfo<Position> delContractSku(HttpServletRequest request, @Param(value="contactSkuId") Integer contactSkuId){
        ResultInfo<Position> resultInfo = new ResultInfo<Position>();
        try{
            contractSkuService.deleteById(contactSkuId);
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            log.error("【delContractSku】处理异常",e);
        }
        return resultInfo;
    }

    @ResponseBody
    @RequestMapping(value="/delContractSkuModify")
    public ResultInfo<Position> delContractSkuOfModify(HttpServletRequest request, @Param(value="contactSkuId") Integer contactSkuId){
        ResultInfo<Position> resultInfo = new ResultInfo<Position>();
        try{
            contractSkuService.deleteModifySkuById(contactSkuId);
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            log.error("【delContractSkuOfModify】处理异常",e);
        }
        return resultInfo;
    }

    /**
     * 编辑sku页面
     * @return 产品列表信息
     */
    @RequestMapping(method = RequestMethod.GET, value = "/toContractSkuEdit")
    @ResponseBody
    public ModelAndView toContractSkuEdit(@Param(value="contactSkuId") Integer contactSkuId){
        ModelAndView mv = new ModelAndView();
        ElContractSku sku = this.contractSkuService.findById(contactSkuId);
        mv.addObject("sku",sku);
        mv.setViewName("el/sku/editSku");
        return mv;
    }

    /**
     * 合同审核页面
     * @return
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET,value="/toConractAudit")
    public ModelAndView toConractAudit(@Param(value="contractId") Integer contractId,Integer type) {
        ModelAndView mv = new ModelAndView();
        ELContractVO contractInfo = this.contractService.findById(contractId);
        mv.addObject("contractInfo",contractInfo);
        mv.addObject("type",type);
        mv.setViewName("el/contract/contractAudit");
        return mv;
    }

    @ResponseBody
    @RequestMapping(method = RequestMethod.POST,value="/contractSkuEdit")
    public ResultInfo<?> contractSkuEdit(HttpServletRequest request,ElContractSku sku) {

        ResultInfo<Position> resultInfo = new ResultInfo<Position>();
        try{
            contractSkuService.modify(sku);
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            log.error("【contractSkuEdit】处理异常",e);
        }
        return resultInfo;
    }

    @ResponseBody
    @RequestMapping(value = "/editContract")
    public ModelAndView editContract(HttpServletRequest request, ELContractVO contract) {
        ModelAndView mv = new ModelAndView();
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        try {
            contract.setUpdator(user.getUserId());
            contract.setSignDate(DateUtil.convertLong(contract.getSignDateStr(), DateUtil.DATE_FORMAT));
            contract.setContractValidityDateStart(DateUtil.convertLong(contract.getContractValidityDateStartStr(), DateUtil.DATE_FORMAT));
            contract.setContractValidityDateEnd(DateUtil.convertLong(contract.getContractValidityDateEndStr(), DateUtil.DATE_FORMAT));

            contractService.updateContract(contract);

            return success(mv);

        } catch (Exception e) {
            logger.error("editContract:", e);
            return fail(mv);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/modifyContract")
    public ModelAndView modifyContract(HttpServletRequest request, ELContractVO contract) {
        ModelAndView mv = new ModelAndView();
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            String skuStr = request.getParameter("skuList");
            List<ElContractSku> skuList=null;
            if (StringUtil.isBlank(skuStr)) {
               skuList=new ArrayList<>();
            } else {
                TypeReference<List<ElContractSku>> typeReference = new TypeReference<List<ElContractSku>>() {
                };
                skuList = JsonUtils.readValueByType(skuStr, typeReference);
            }
            contractService.saveModifyContract(contract, user,skuList);
            return success(mv);
        }catch (Exception ex){
            log.error("保存修改合同失败",ex);
            return fail(mv);
        }
    }

    /**
     * 合同审核
     * @param request
     * @param contract
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/contractAudit")
    public ResultInfo contractAudit(HttpServletRequest request, ELContractVO contract) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        ELContractVO preContract=contractService.findById(contract.getElContractId());
        try {

            if(HospitalConstant.CONTRACT_UNMODIFY.equals(preContract.getModifyStatus())) {
                //审核通过 合同状态改为生效
                if (contract.getAuditStatus() == 2) {
                    contract.setEffctiveStatus(1);
                }

                contract.setAuditStatus(contract.getAuditStatus());
            }else if(HospitalConstant.CONTRACT_ONMODIFY.equals(preContract.getModifyStatus())){
                if (contract.getAuditStatus() == 2) {
                    contract.setEffctiveStatus(1);
                }
                 contractService.checkModify(contract);
            }
            contract.setAuditDesc(contract.getAuditDesc());
            contract.setAuditer(user.getUserId());
            contract.setAuditTime(System.currentTimeMillis());

            contractService.updateContract(contract);

            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            return resultInfo;
        } catch (Exception e) {
            logger.error("contractAudit:", e);
        }
        return resultInfo;
    }


    /**
     * 提交审核
     * @param request
     * @param contractId
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/submitContractValidator")
    public ResultInfo<?> submitContractValidator(HttpServletRequest request, @Param(value="contractId") Integer contractId){
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{

            //查询当前客户其他有效合同中的所有sku,保证sku不重叠
            ELContractVO contractVO = this.contractService.findById(contractId);

            ContractValidatorChain chain = ContractValidatorChainBuild.newBuild()
                    .setContractHasProductValidator(this.contractHasProductValidator)
                    .setContractProductNumOverFlowValidator(this.contractProductNumOverFlowValidator)
                    .setContractSkuOverlapValidator(this.contractSkuOverlapValidator)
                    .create();

            ValidatorResult validatorResult = chain.validator(contractVO);

            //检验不通过返回结果
            if(!validatorResult.getResult()){
                resultInfo.setMessage(validatorResult.getMessage());
                return resultInfo;
            }

            //更新审核状态为审核中
            ELContractVO contract = new ELContractVO();
            contract.setElContractId(contractId);
            contract.setAuditStatus(1);
            if (contractVO.getModifyStatus() == 0) {
                contract.setEffctiveStatus(0);
                contract.setProductSynStatus(0);
                contract.setContractSynStatus(0);
                contract.setConfirmStatus(0);
            }
            contractService.updateContract(contract);

            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            LOGGER.error("提交审核失败",e);
        }
        return resultInfo;
    }


    /**
     * 生效合同
     * @param request
     * @param contractId
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/effctiveContract")
    public ResultInfo<?> effctiveContract(HttpServletRequest request, @Param(value="contractId") Integer contractId){
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{

            //查询当前客户其他有效合同中的所有sku,保证sku不重叠
            ELContractVO contractVO = this.contractService.findById(contractId);

            ContractValidatorChain chain = ContractValidatorChainBuild.newBuild()
                    .setContractHasProductValidator(this.contractHasProductValidator)
                    .setContractProductNumOverFlowValidator(this.contractProductNumOverFlowValidator)
                    .create();

            ValidatorResult validatorResult = chain.validator(contractVO);

            //检验不通过返回结果
            if(!validatorResult.getResult()){
                resultInfo.setMessage(validatorResult.getMessage());
                return resultInfo;
            }

            //生效合同
            ELContractVO contract = new ELContractVO();
            contract.setElContractId(contractId);
            contract.setEffctiveStatus(1);
            contractService.updateContract(contract);

            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            LOGGER.error("合同生效失败",e);
        }
        return resultInfo;
    }


    /**
     * 同步合同产品给小医院
     * @param request
     * @param contractId
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/synContractSkuToEl")
    public ResultInfo<?> synContractSkuToEl(HttpServletRequest request, @Param(value="contractId") Integer contractId){
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{

            //查询当前客户其他有效合同中的所有sku,保证sku不重叠
            ELContractVO contractVO = this.contractService.findById(contractId);

            ContractValidatorChain chain = ContractValidatorChainBuild.newBuild()
                    .setContractEffectiveValidator(this.contractEffectiveValidator)
                    .setContractHasProductValidator(this.contractHasProductValidator)
                    .setContractProductNumOverFlowValidator(this.contractProductNumOverFlowValidator)
                    .setContractSkuOverlapValidator(this.contractSkuOverlapValidator)
                    .create();

            ValidatorResult validatorResult = chain.validator(contractVO);

            //检验不通过返回结果
            if(!validatorResult.getResult()){
                resultInfo.setMessage(validatorResult.getMessage());
                return resultInfo;
            }

            //同步给小医院合同的产品详情
            synProductInfoService.syncData(contractId);

            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            LOGGER.error("同步产品失败",e);
        }
        return resultInfo;
    }

    /**
     * 同步合同给小医院
     * @param request
     * @param contractId
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/synContractToEl")
    public ResultInfo<?> synContractToEl(HttpServletRequest request, @Param(value="contractId") Integer contractId){
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{

            //这边也可以用校验器链模式加构造器模式抽取下，代码不复杂，有空可以抽下
            //查询当前客户其他有效合同中的所有sku,保证sku不重叠
            ELContractVO contractVO = this.contractService.findById(contractId);

            ContractValidatorChain chain = ContractValidatorChainBuild.newBuild()
                    .setContractEffectiveValidator(this.contractEffectiveValidator)
                    .setContractProductSynValidator(this.contractProductSynValidator)
                    .setContractHasProductValidator(this.contractHasProductValidator)
                    .setContractProductNumOverFlowValidator(this.contractProductNumOverFlowValidator)
                    .setContractSkuOverlapValidator(this.contractSkuOverlapValidator)
                    .create();

            ValidatorResult validatorResult = chain.validator(contractVO);

            //检验不通过返回结果
            if(!validatorResult.getResult()){
                resultInfo.setMessage(validatorResult.getMessage());
                return resultInfo;
            }

            //同步给小医院合同信息
            sysContractInfoService.syncData(contractId);

            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            LOGGER.error("同步小医院失败",e);
        }
        return resultInfo;
    }


    /**
     * 失效合同
     * @param request
     * @param contractId
     * @return
     */
    @ResponseBody
    @RequestMapping(value="/invalidContract")
    public ResultInfo<?> invalidContract(HttpServletRequest request, @Param(value="contractId") Integer contractId){
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{
            ELContractVO contract = new ELContractVO();
            contract.setElContractId(contractId);
            contract.setEffctiveStatus(0);
            contractService.updateContract(contract);
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }catch (Exception e){
            log.error("【invalidContract】处理异常",e);
        }
        return resultInfo;
    }
}
