package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import lombok.*;

/**
 * 其他出库   https://www.yuque.com/manhuo/gf1570/ug85q9
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @see KingDeeStorageOutDto
 */
@Getter
@Setter
@NoArgsConstructor
public class KingDeeStorageOutDetailDto {

    /**
     * 金蝶行id
     */
    @WriteBackField(value = "FENTRYID", backWriteByErpField = "F_QZOK_BDDJHID", backWriteToKingDeeField = "F_QZOK_BDDJHID")
    private String fEntryId;
    /**
     * 实收数量
     */
    private String fQty;
    /**
     * 原始订单号
     */
    private String fQzokYsddh;
    /**
     * 归属业务单号
     */
    private String fQzokGsywdh;
    /**
     * 业务类型
     */
    private String fQzokYwlx;
    /**
     * 批次号
     */
    private String fQzokPch;
    /**
     * 序列号
     */
    private String fQzokXlh;
    /**
     * 授权类型
     */
    private String fQzokSqlx;
    /**
     * 是否直发
     */
    private String fQzokSfzf;
    /**
     * 贝登订单行ID
     */
    private String fQzokBddjhid;

    /**
     * 填写物料编码 SKU
     */
    private String fMaterialId;
    /**
     * 仓库 填写仓库编码，默认值 ：CK9999
     */
    private String fStockId;
}
