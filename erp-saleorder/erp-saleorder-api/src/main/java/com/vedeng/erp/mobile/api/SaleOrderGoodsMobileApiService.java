package com.vedeng.erp.mobile.api;

import com.vedeng.erp.mobile.dto.SaleOrderAttributionInfoDto;
import com.vedeng.erp.mobile.dto.SaleOrderGoodsListResultDto;
import com.vedeng.erp.mobile.dto.SaleOrderListQueryDto;
import com.vedeng.erp.mobile.dto.SaleOrderListResultDto;

import java.util.List;

public interface SaleOrderGoodsMobileApiService {

    /**
     * 获取销售订单下的商品信息
     */
    List<SaleOrderGoodsListResultDto> getSaleOrderGoodsBySaleorderIds(List<Integer> saleorderIds);

}
