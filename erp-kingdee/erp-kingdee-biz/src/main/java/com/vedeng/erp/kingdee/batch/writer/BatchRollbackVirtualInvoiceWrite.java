package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 回滚发票处理器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchRollbackVirtualInvoiceWrite extends BaseWriter<BatchRollbackInvoiceProcessDto> {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;


    @Autowired
    private BatchRVirtualInvoiceJWarehouseDtoMapper batchRVirtualInvoiceJWarehouseDtoMapper;

    @Autowired
    private BatchVirtualInvoiceItemDtoMapper batchVirtualInvoiceItemDtoMapper;

    @Autowired
    private BatchVirtualInvoiceDtoMapper batchVirtualInvoiceDtoMapper;

    @Autowired
    private BatchSettlementBillDtoMapper batchSettlementBillDtoMapper;

    @Autowired
    private BatchSettlementBillItemDtoMapper batchSettlementBillItemDtoMapper;

    @Autowired
    private BatchAfterSalesGoodsDtoMapper batchAfterSalesGoodsDtoMapper;


    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;


    @Override
    public void doWrite(BatchRollbackInvoiceProcessDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("金蝶回滚虚拟发票处理器 list:{}", JSON.toJSONString(item));

        OperateExtCommand operateExtCommand = new OperateExtCommand(item.getFormId(), item.getIds(), item.getOrgId(), null);
        kingDeeBaseApi.unAudit(operateExtCommand);
        ArrayList<SuccessEntity> delete = kingDeeBaseApi.delete(operateExtCommand);




        if (item.getIsPay()) {
            if (CollUtil.isNotEmpty(delete)) {

                List<BatchRVirtualInvoiceJWarehouseDto> byVirtualInvoiceId = batchRVirtualInvoiceJWarehouseDtoMapper.findByVirtualInvoiceId(item.getVirtualInvoiceId());

                BatchVirtualInvoiceDto batchVirtualInvoiceDto = batchVirtualInvoiceDtoMapper.selectByVirtualInvoiceId(item.getVirtualInvoiceId());

                List<BatchSettlementBillDto> buyOrder = batchSettlementBillDtoMapper.selectByBusinessSourceTypeIdAndSourceType(batchVirtualInvoiceDto.getBusinessOrderId(), "buyOrder");

                batchVirtualInvoiceDtoMapper.deleteByVirtualInvoiceId(item.getVirtualInvoiceId());
                batchVirtualInvoiceItemDtoMapper.deleteByVirtualInvoiceId(item.getVirtualInvoiceId());

                if (CollUtil.isNotEmpty(byVirtualInvoiceId)) {
                    log.info("金蝶回滚发票删除货票关系,invoiceId:{},data:{}",item.getInvoiceId(),JSON.toJSONString(byVirtualInvoiceId));
                    List<Integer> collect = byVirtualInvoiceId.stream().map(BatchRVirtualInvoiceJWarehouseDto::getVirtualInvoiceWarehouseId).collect(Collectors.toList());
                    batchRVirtualInvoiceJWarehouseDtoMapper.deleteByVirtualInvoiceWarehouseIds(collect);
                }
                checkSettlementBillItem(buyOrder.get(0).getSettleBillId(),ErpConstant.TWO);


            }
        }

    }


    private List<BatchBuyorderGoodsDto> getBuyOrderArrivalList(List<Integer> buyOrderGoodsIds, List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList) {

        List<BatchAfterSalesGoodsDto> batchAfterSalesGoodsDtos = batchAfterSalesGoodsDtoMapper.selectByOrderDetailIdsAndTotalAfterNum(buyOrderGoodsIds, 546, 536);

        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtos = batchWarehouseGoodsOutInItemDtoMapper.selectByRelatedIdsAndOperateType(buyOrderGoodsIds, ErpConstant.ONE);

        List<BatchBuyorderGoodsDto> batchBuyOrderGoodsArrivalList = buyOrderGoodsDtoList.stream().filter(x -> {
            BigDecimal realNum = new BigDecimal(x.getNum().toString());
            if (CollUtil.isNotEmpty(batchAfterSalesGoodsDtos)) {
                Optional<BatchAfterSalesGoodsDto> first = batchAfterSalesGoodsDtos.stream().filter(y -> y.getOrderDetailId().equals(x.getBuyorderGoodsId())).findFirst();
                if (first.isPresent()) {
                    realNum = realNum.subtract(new BigDecimal(first.get().getNum().toString()));
                }
            }
            BigDecimal arrivalNum = BigDecimal.ZERO;
            if (CollUtil.isNotEmpty(batchWarehouseGoodsOutInItemDtos)) {
                Optional<BatchWarehouseGoodsOutInItemDto> first = batchWarehouseGoodsOutInItemDtos.stream().filter(y -> y.getRelatedId().equals(x.getBuyorderGoodsId())).findFirst();
                if (first.isPresent()) {
                    arrivalNum = first.get().getNum();
                }
            }
            x.setRealNum(realNum);
            return realNum.compareTo(arrivalNum) <= 0;

        }).collect(Collectors.toList());
        return batchBuyOrderGoodsArrivalList;
    }


    private boolean checkSettlementBillItem(Integer settleBillId,Integer type) {

        List<BatchSettlementBillItemDto> batchSettlementBillItemDtos = batchSettlementBillItemDtoMapper.selectBySettleBillId(settleBillId);

        if (type.equals(ErpConstant.TWO)) {

            Map<Integer, BatchSettlementBillItemDto> batchSettlementBillItem2Map = batchSettlementBillItemDtos.stream().collect(Collectors.toMap(BatchSettlementBillItemDto::getBusinessItemId, x -> x, (k1, k2) -> k1));
            Set<Integer> integers = batchSettlementBillItem2Map.keySet();
            List<Integer> buyOrderGoodsIds = CollUtil.newArrayList(integers);

            List<BatchBuyorderGoodsDto> buyOrderGoodsDtoList = batchBuyorderGoodsDtoMapper.findByBuyorderGoodsIdInAndIsDelete(buyOrderGoodsIds, 0);
            List<BatchBuyorderGoodsDto> buyOrderArrivalList = getBuyOrderArrivalList(buyOrderGoodsIds, buyOrderGoodsDtoList);
            batchSettlementBillItemDtos.forEach(x->{
                Optional<BatchBuyorderGoodsDto> first = buyOrderArrivalList.stream().filter(a -> a.getBuyorderGoodsId().equals(x.getSettleItemBillId())).findFirst();
                first.ifPresent(y -> x.setNumber(y.getRealNum()));
                x.setAmount(x.getPrice().multiply(x.getNumber()));
            });
        }




        List<Integer> businessItemIds = batchSettlementBillItemDtos.stream().map(BatchSettlementBillItemDto::getBusinessItemId).collect(Collectors.toList());
        List<BatchVirtualInvoiceItemDto> virtualInvoiceItemDtoList = batchVirtualInvoiceItemDtoMapper.selectByBusinessOrderItemIds(businessItemIds,type);
        Map<Integer, List<BatchVirtualInvoiceItemDto>> virtualInvoiceItemDto2Map = virtualInvoiceItemDtoList.stream().collect(Collectors.groupingBy(BatchVirtualInvoiceItemDto::getBusinessOrderItemId));

        AtomicBoolean flag = new AtomicBoolean(false);

        batchSettlementBillItemDtos.forEach(x->{
            List<BatchVirtualInvoiceItemDto> data = virtualInvoiceItemDto2Map.get(x.getBusinessItemId());
            BatchSettlementBillItemDto update = new BatchSettlementBillItemDto();
            if (CollUtil.isNotEmpty(data)) {
                BigDecimal amount = data.stream().map(a -> a.getTotalAmount().abs()).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                update.setSettleItemBillId(x.getSettleItemBillId());
                if (amount.compareTo(BigDecimal.ZERO) == 0) {
                    update.setInvoiceStatus(0);
                }
                // 全部
                if (x.getAmount().compareTo(amount) <= 0) {
                    update.setInvoiceStatus(2);
                }
                // 部分
                if (amount.compareTo(BigDecimal.ZERO) > 0 && x.getAmount().compareTo(amount) > 0) {
                    update.setInvoiceStatus(1);
                }
            } else {
                update.setInvoiceStatus(0);
            }
            batchSettlementBillItemDtoMapper.updateByPrimaryKeySelective(update);
            flag.set(true);
        });

        return flag.get();


    }
}
