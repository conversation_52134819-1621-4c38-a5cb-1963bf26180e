<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerFinanceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerFinance">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_CUSTOMER_FINANCE-->
    <id column="TRADER_CUSTOMER_FINANCE_ID" jdbcType="INTEGER" property="traderCustomerFinanceId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="CUSTOMER_NAME_NEW" jdbcType="VARCHAR" property="customerNameNew" />
    <result column="CUSTOMER_NATURE" jdbcType="BOOLEAN" property="customerNature" />
    <result column="CUSTOMER_CLASS" jdbcType="BOOLEAN" property="customerClass" />
    <result column="GROUP_NAME" jdbcType="VARCHAR" property="groupName" />
    <result column="CUSTOMER_SECOND_TYPE" jdbcType="BOOLEAN" property="customerSecondType" />
    <result column="CUSTOMER_THIRD_TYPE" jdbcType="BOOLEAN" property="customerThirdType" />
    <result column="HOSPITAL_LEVER" jdbcType="BOOLEAN" property="hospitalLever" />
    <result column="HOSPITAL_NAME" jdbcType="VARCHAR" property="hospitalName" />
    <result column="IS_PUSH" jdbcType="BOOLEAN" property="isPush" />
    <result column="PUSH_TIME" jdbcType="TIMESTAMP" property="pushTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_CUSTOMER_FINANCE_ID, TRADER_ID, CUSTOMER_NAME_NEW, CUSTOMER_NATURE, CUSTOMER_CLASS, 
    GROUP_NAME, CUSTOMER_SECOND_TYPE, CUSTOMER_THIRD_TYPE, HOSPITAL_LEVER, HOSPITAL_NAME, 
    IS_PUSH, PUSH_TIME, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, 
    UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CUSTOMER_FINANCE
    where TRADER_CUSTOMER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_CUSTOMER_FINANCE
    where TRADER_CUSTOMER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CUSTOMER_FINANCE_ID" keyProperty="traderCustomerFinanceId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerFinance" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_FINANCE (TRADER_ID, CUSTOMER_NAME_NEW, CUSTOMER_NATURE, 
      CUSTOMER_CLASS, GROUP_NAME, CUSTOMER_SECOND_TYPE, 
      CUSTOMER_THIRD_TYPE, HOSPITAL_LEVER, HOSPITAL_NAME, 
      IS_PUSH, PUSH_TIME, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      REMARK, UPDATE_REMARK)
    values (#{traderId,jdbcType=INTEGER}, #{customerNameNew,jdbcType=VARCHAR}, #{customerNature,jdbcType=BOOLEAN}, 
      #{customerClass,jdbcType=BOOLEAN}, #{groupName,jdbcType=VARCHAR}, #{customerSecondType,jdbcType=BOOLEAN}, 
      #{customerThirdType,jdbcType=BOOLEAN}, #{hospitalLever,jdbcType=BOOLEAN}, #{hospitalName,jdbcType=VARCHAR}, 
      #{isPush,jdbcType=BOOLEAN}, #{pushTime,jdbcType=TIMESTAMP}, #{isDelete,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_FINANCE_ID" keyProperty="traderCustomerFinanceId" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerFinance" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_FINANCE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="customerNameNew != null">
        CUSTOMER_NAME_NEW,
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE,
      </if>
      <if test="customerClass != null">
        CUSTOMER_CLASS,
      </if>
      <if test="groupName != null">
        GROUP_NAME,
      </if>
      <if test="customerSecondType != null">
        CUSTOMER_SECOND_TYPE,
      </if>
      <if test="customerThirdType != null">
        CUSTOMER_THIRD_TYPE,
      </if>
      <if test="hospitalLever != null">
        HOSPITAL_LEVER,
      </if>
      <if test="hospitalName != null">
        HOSPITAL_NAME,
      </if>
      <if test="isPush != null">
        IS_PUSH,
      </if>
      <if test="pushTime != null">
        PUSH_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerNameNew != null">
        #{customerNameNew,jdbcType=VARCHAR},
      </if>
      <if test="customerNature != null">
        #{customerNature,jdbcType=BOOLEAN},
      </if>
      <if test="customerClass != null">
        #{customerClass,jdbcType=BOOLEAN},
      </if>
      <if test="groupName != null">
        #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="customerSecondType != null">
        #{customerSecondType,jdbcType=BOOLEAN},
      </if>
      <if test="customerThirdType != null">
        #{customerThirdType,jdbcType=BOOLEAN},
      </if>
      <if test="hospitalLever != null">
        #{hospitalLever,jdbcType=BOOLEAN},
      </if>
      <if test="hospitalName != null">
        #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="isPush != null">
        #{isPush,jdbcType=BOOLEAN},
      </if>
      <if test="pushTime != null">
        #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerFinance">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_FINANCE
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="customerNameNew != null">
        CUSTOMER_NAME_NEW = #{customerNameNew,jdbcType=VARCHAR},
      </if>
      <if test="customerNature != null">
        CUSTOMER_NATURE = #{customerNature,jdbcType=BOOLEAN},
      </if>
      <if test="customerClass != null">
        CUSTOMER_CLASS = #{customerClass,jdbcType=BOOLEAN},
      </if>
      <if test="groupName != null">
        GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      </if>
      <if test="customerSecondType != null">
        CUSTOMER_SECOND_TYPE = #{customerSecondType,jdbcType=BOOLEAN},
      </if>
      <if test="customerThirdType != null">
        CUSTOMER_THIRD_TYPE = #{customerThirdType,jdbcType=BOOLEAN},
      </if>
      <if test="hospitalLever != null">
        HOSPITAL_LEVER = #{hospitalLever,jdbcType=BOOLEAN},
      </if>
      <if test="hospitalName != null">
        HOSPITAL_NAME = #{hospitalName,jdbcType=VARCHAR},
      </if>
      <if test="isPush != null">
        IS_PUSH = #{isPush,jdbcType=BOOLEAN},
      </if>
      <if test="pushTime != null">
        PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CUSTOMER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderCustomerFinance">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_FINANCE
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      CUSTOMER_NAME_NEW = #{customerNameNew,jdbcType=VARCHAR},
      CUSTOMER_NATURE = #{customerNature,jdbcType=BOOLEAN},
      CUSTOMER_CLASS = #{customerClass,jdbcType=BOOLEAN},
      GROUP_NAME = #{groupName,jdbcType=VARCHAR},
      CUSTOMER_SECOND_TYPE = #{customerSecondType,jdbcType=BOOLEAN},
      CUSTOMER_THIRD_TYPE = #{customerThirdType,jdbcType=BOOLEAN},
      HOSPITAL_LEVER = #{hospitalLever,jdbcType=BOOLEAN},
      HOSPITAL_NAME = #{hospitalName,jdbcType=VARCHAR},
      IS_PUSH = #{isPush,jdbcType=BOOLEAN},
      PUSH_TIME = #{pushTime,jdbcType=TIMESTAMP},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where TRADER_CUSTOMER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
  </update>
</mapper>