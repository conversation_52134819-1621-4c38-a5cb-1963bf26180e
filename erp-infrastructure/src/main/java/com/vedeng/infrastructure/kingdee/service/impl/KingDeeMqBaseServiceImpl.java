package com.vedeng.infrastructure.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.ArgumentParser;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import com.vedeng.infrastructure.kingdee.enums.KingDeeMsgStatusEnums;
import com.vedeng.infrastructure.kingdee.service.KingDeeEventMsgService;
import com.vedeng.infrastructure.kingdee.service.KingDeeMqBaseService;
import com.vedeng.infrastructure.kingdee.service.KingDeeMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶基础消息
 * @date 2022/8/27 15:42
 */
@Slf4j
@Service("kingDeeMqBaseService")
public class KingDeeMqBaseServiceImpl<T extends KingDeeMqBaseDto> implements KingDeeMqBaseService<T> {

    private final static String JAVA_UTIL_ARRAY_LIST = "java.util.ArrayList";
    private final static String CLASS_PREFIX = "kingDee";
    private final static String CLASS_SUFFIX = "ServiceImpl";

    private final static long WAIT_TIME = 10;
    private final static long LOCK_SECONDS = 20;
    private final static String REDIS_KEY = "ERP:KINGDEE:MQMSG:";


    @Autowired
    protected KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeeEventMsgService kingDeeEventMsgService;

    @Autowired(required = false)
    private KingDeeMsgService kingDeeMsgService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void register(T t) {
        if (t == null) {
            throw new ServiceException("注册对象不能为空");
        }

        KingDeeEventMsgDto kingDeeEventMsg = this.saveLog(t);
        // 仅发送并消息消费相同数据第一次请求,后续直接通过定时任务按顺序完成
        if (kingDeeEventMsg != null && kingDeeEventMsg.getMsgOrder() == 1) {
            kingDeeMsgService.senMsg(kingDeeEventMsg);
        }
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void register(T t, boolean isSync) {
        if (t == null) {
            throw new ServiceException("注册对象不能为空");
        }
        KingDeeEventMsgDto kingDeeEventMsg = this.saveLog(t);
        // 仅发送并消息消费相同数据第一次请求,后续直接通过定时任务按顺序完成
        if (kingDeeEventMsg != null) {
            // 直接执行
            try {
                this.execute(kingDeeEventMsg);
            } catch (Exception e) {
                log.error("金蝶:同步注册执行事件异常", e);
            }
        }
    }


    @Override
    public void cancel(T t) {
        String businessId = this.getBusinessId(t);
        if (businessId == null) {
            log.error("金蝶注册对象主键不能为空{}", JSON.toJSONString(t));
            throw new ServiceException("金蝶注册对象主键不能为空");
        }
        List<KingDeeEventMsgDto> noConsumeMsgById = kingDeeEventMsgService.getNoConsumeMsgById(businessId, t.getEventType());
        noConsumeMsgById.forEach(no -> kingDeeEventMsgService.msgNoHandle(no));
    }

    /**
     * 存入本地消息表
     *
     * @param t 消息对象
     */
    @Transactional(rollbackFor = Throwable.class)
    public KingDeeEventMsgDto saveLog(T t) {
        log.info("注册存入本地消息表Data入参{}", JSON.toJSONString(t));
        KingDeeEventMsgDto kingDeeEventMsg;
        String businessId = this.getBusinessId(t);
        if (businessId == null) {
            log.error("金蝶注册对象主键不能为空{}", JSON.toJSONString(t));
            throw new ServiceException("金蝶注册对象主键不能为空");
        }
        try {

            String key = REDIS_KEY + t.getEventType() + businessId;
            if (RedissonLockUtils.tryLock(key, WAIT_TIME, LOCK_SECONDS, TimeUnit.SECONDS)) {
                log.info("金蝶异步执行器加锁成功, key = [{}]", key);
                try {
                    List<KingDeeEventMsgDto> noConsumeMsgById = kingDeeEventMsgService.getNoConsumeMsgById(businessId, t.getEventType());
                    kingDeeEventMsg = new KingDeeEventMsgDto();
                    kingDeeEventMsg.setMsgOrder(noConsumeMsgById.size() + 1);
                    kingDeeEventMsg.setBusinessId(businessId);
                    kingDeeEventMsg.setBody(JSONArray.parseArray(JSON.toJSONString(CollUtil.newArrayList(t))));
                    kingDeeEventMsg.setEventType(t.getEventType());
                    kingDeeEventMsg.setClassPath(t.getClass().getTypeName());
                    kingDeeEventMsgService.create(kingDeeEventMsg);
                } finally {
                    RedissonLockUtils.unlock(key);
                    log.info("金蝶异步执行器释放锁成功, key = [{}]", key);
                }
            } else {
                log.error("金蝶异步执行器获取锁失败,超出等待时长, key = [{}]", key);
                throw new ServiceException("金蝶异步执行器获取锁失败,超出等待时长");
            }
        } catch (Exception e) {
            log.error("注册对象异常", e);
            throw new ServiceException("注册对象异常");
        }
        return kingDeeEventMsg;
    }

    private String getBusinessId(T t) {
        Field[] field = ReflectUtil.getFields(t.getClass());
        if (ArrayUtil.isNotEmpty(field)) {
            for (Field fie : field) {
                Annotation[] declaredAnnotations = fie.getDeclaredAnnotations();
                for (Annotation declaredAnnotation : declaredAnnotations) {
                    if (declaredAnnotation.annotationType().equals(BusinessID.class)) {
                        return Convert.toStr(ReflectUtil.getFieldValue(t, fie));
                    }
                }
            }
        }
        return null;
    }


    @Override
    public void execute(KingDeeEventMsgDto msgDto) {
        log.info("金蝶异步执行参数[{}]", JSON.toJSONString(msgDto));
        Integer messageStatus = msgDto.getMessageStatus();
        JSONArray beanBody = msgDto.getBody();
        String classPath = msgDto.getClassPath();
        String eventType = msgDto.getEventType();
        KingDeeBizEnums kingDeeBizEnums = KingDeeBizEnums.getEnum(eventType);
        String body;
        if (!JAVA_UTIL_ARRAY_LIST.equals(classPath)) {
            body = JSON.toJSONString(CollUtil.getFirst(beanBody));
        } else {
            body = beanBody.toJSONString();
        }
        // 动态执行对应方法
        Object obj = SpringUtil.getBean(CLASS_PREFIX + kingDeeBizEnums.getBusiness() + CLASS_SUFFIX);
        Method method = this.getMethod(obj.getClass(), kingDeeBizEnums.getMethod());
        // 获取参数
        if (method == null) {
            throw new ServiceException(StrUtil.format("没有找到方法:[{}]", kingDeeBizEnums.getMethod()));
        }
        String[] params = new String[]{classPath + ArgumentParser.SEPARATOR + body};
        Object[] result = ArgumentParser.parse(params);
        try {
            method.invoke(obj, result);
            kingDeeEventMsgService.msgConsume(msgDto);
            kingDeeEventMsgService.msgNoHandleByLtId(msgDto);
        } catch (Exception e) {
            if (e instanceof InvocationTargetException) {
                msgDto.setErrorMsg(((InvocationTargetException) e).getTargetException().getMessage());
            } else {
                msgDto.setErrorMsg(e.getMessage());
            }
            kingDeeEventMsgService.msgRetry(msgDto);
            if (Objects.nonNull(messageStatus) && KingDeeMsgStatusEnums.RETRY.getStatus().equals(messageStatus)) {
                kingDeeEventMsgService.msgRetryNumAdd(msgDto);
            }

            log.error("金蝶：动态执行方法异常,method[{}]", CLASS_PREFIX + kingDeeBizEnums.getBusiness() + CLASS_SUFFIX + ArgumentParser.SEPARATOR + method.getName(), e);
            throw new ServiceException("金蝶：动态执行方法异常", e);
        }
    }

    /**
     * 获取目标方法
     *
     * @param clazz     clazz
     * @param methodStr methodStr
     * @return Method
     */
    private Method getMethod(Class<?> clazz, String methodStr) {
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getName().equalsIgnoreCase(methodStr)) {
                return method;
            }
        }
        return null;
    }

}
