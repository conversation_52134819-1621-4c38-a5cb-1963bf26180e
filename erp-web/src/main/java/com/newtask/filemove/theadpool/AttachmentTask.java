package com.newtask.filemove.theadpool;

import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.model.FileInfo;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.system.model.Attachment;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

import javax.annotation.Resource;
import java.util.List;

public class AttachmentTask extends FileMoveTask {

    @Resource
    private FileMoveDao fileMoveDao;

    public AttachmentTask(List<FileMoveDto> fileMoveList) {
        super(fileMoveList);
        // 运行时注入service
        WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
        fileMoveDao = (FileMoveDao) context.getBean("fileMoveDao");
    }

    @Override
    protected void updateErrorInfo(FileMoveDto fileMoveDto) {
        Attachment toUpdate = new Attachment();
        toUpdate.setAttachmentId(fileMoveDto.getId());
        toUpdate.setSynSuccess(2);
        fileMoveDao.updateAttachment(toUpdate);
    }

    @Override
    protected void updateOriginalFileInfo(FileMoveDto fileMoveDto, FileInfo fileInfo) {
        //更新uri、domain、resourceId
        Attachment toUpdate = new Attachment();
        toUpdate.setAttachmentId(fileMoveDto.getId());
        toUpdate.setUri(fileInfo.getFilePath());
        toUpdate.setDomain(fileInfo.getHttpUrl());
        toUpdate.setOriginalFilepath(fileMoveDto.getDomain() + fileMoveDto.getUri());
        toUpdate.setSynSuccess(1);
        toUpdate.setOssResourceId(fileInfo.getOssResourceId());

        if(fileInfo.getPrefix() != null){
            toUpdate.setCostTime(Long.valueOf(fileInfo.getPrefix()));
        }

        fileMoveDao.updateAttachment(toUpdate);
    }
}
