package com.vedeng.config.plugin;

/**
 * CAT监控埋点类型
 *
 * <AUTHOR>
 * @date 2020/10/28
 */
public interface MonitorTypeConstants {

    String URL = "URL";

    String SQL = "SQL";
    String SQL_DATABASE = "SQL.Database";
    String SQL_METHOD = "SQL.Method";

    /**
     * 与CAT内置保持一致，定义成Cache
     */
    String REDIS = "Cache";

    /**
     * feign调用定义与CAT内置保持一致
     * 服务提供方定义成Service.Feign
     * 服务调用方定义成Call.Feign
     */
    String FEIGN_PROVIDER = "Service.Feign";
    String FEIGN_CALLER = "Call.Feign";

    /**
     * 纯http调用定义与CAT内置保持一致
     * 服务提供方定义成Service.Http
     * 服务调用方定义成Call.Http
     */
    String HTTP_PROVIDER = "Service.Http";
    String HTTP_CALLER = "Call.Http";

    String ES = "ES";
    String FTP = "FTP";
    String SOLR = "SOLR";
}
