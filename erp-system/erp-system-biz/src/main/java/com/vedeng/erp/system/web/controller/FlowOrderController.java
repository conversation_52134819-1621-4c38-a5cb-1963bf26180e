package com.vedeng.erp.system.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 流转单路由接口
 * @date 2022/8/15 17:01
 */
@Controller
@RequestMapping("/flowOrder")
@Slf4j
public class FlowOrderController extends BaseController {


    @RequestMapping(value = "/index")
    @ExcludeAuthorization
    public String index() {
        return view();
    }

    @RequestMapping(value = "/editSaleOrder")
    @ExcludeAuthorization
    public ModelAndView editSaleOrder(@RequestParam(required = false) Long flowOrderId) {
        ModelAndView mv = new ModelAndView(view());
        mv.addObject("flowOrderId", flowOrderId);
        return mv;
    }

    @RequestMapping(value = "/editBuyOrder")
    @ExcludeAuthorization
    public ModelAndView editBuyOrder(@RequestParam(required = false) Long flowOrderId) {
        ModelAndView mv = new ModelAndView(view());
        mv.addObject("flowOrderId", flowOrderId);
        return mv;
    }

    @RequestMapping(value = "/buyOrderDetail")
    @ExcludeAuthorization
    public ModelAndView buyOrderDetail(@RequestParam Long flowOrderId) {
        ModelAndView mv = new ModelAndView(view());
        mv.addObject("flowOrderId", flowOrderId);
        return mv;
    }

    @RequestMapping(value = "/saleOrderDetail")
    @ExcludeAuthorization
    public ModelAndView saleOrderDetail(@RequestParam Long flowOrderId) {
        ModelAndView mv = new ModelAndView(view());
        mv.addObject("flowOrderId", flowOrderId);
        return mv;
    }

}
