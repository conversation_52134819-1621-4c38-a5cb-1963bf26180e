package com.vedeng.order.model;

import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.vo.SkuAuthorizationVo;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;

import com.vedeng.goods.model.Goods;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.model.coupon.LabelDataDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class SaleorderGoods implements Serializable, Cloneable {

	private static final Logger logger = LoggerFactory.getLogger(SaleorderGoods.class);

	// add by Tomcat.Hui 2019/11/12 13:32 .Desc: VDERP-1325 分批开票. start
	private BigDecimal appliedNum;

	private BigDecimal invoicedNum;

	private Integer canInvoicedNum;

	private BigDecimal appliedAmount;

	private BigDecimal invoicedAmount;

	private BigDecimal realTotalAmount;

	private Integer productAudit;

	private Integer productAuditUesrid;

	private Integer directSuperiorAudit;

	private String productBelongIdInfo;

	private String productBelongNameInfo;

	private List<SkuServiceTag> tagList;

	private Integer updateTotalAmoutType;

	private Date updateDataTime;

	/**
	 * 直发货期开始
	 */
	private String directDeliveryTimeStart;

	/**
	 * 直发货期结束
	 */
	private String directDeliveryTimeEnd;

	/**
	 * 普发货期开始
	 */
	private String commonDeliveryTimeStart;

	/**
	 * 普发货期结束
	 */
	private String commonDeliveryTimeEnd;

	private String taxCategoryNo;

	public String getTaxCategoryNo() {
		return taxCategoryNo;
	}

	public void setTaxCategoryNo(String taxCategoryNo) {
		this.taxCategoryNo = taxCategoryNo;
	}

	public String getDirectDeliveryTimeStart() {
		return directDeliveryTimeStart;
	}

	public void setDirectDeliveryTimeStart(String directDeliveryTimeStart) {
		this.directDeliveryTimeStart = directDeliveryTimeStart;
	}

	public String getDirectDeliveryTimeEnd() {
		return directDeliveryTimeEnd;
	}

	public void setDirectDeliveryTimeEnd(String directDeliveryTimeEnd) {
		this.directDeliveryTimeEnd = directDeliveryTimeEnd;
	}

	public String getCommonDeliveryTimeStart() {
		return commonDeliveryTimeStart;
	}

	public void setCommonDeliveryTimeStart(String commonDeliveryTimeStart) {
		this.commonDeliveryTimeStart = commonDeliveryTimeStart;
	}

	public String getCommonDeliveryTimeEnd() {
		return commonDeliveryTimeEnd;
	}

	public void setCommonDeliveryTimeEnd(String commonDeliveryTimeEnd) {
		this.commonDeliveryTimeEnd = commonDeliveryTimeEnd;
	}


	public Integer getUpdateTotalAmoutType() {
		return updateTotalAmoutType;
	}

	public void setUpdateTotalAmoutType(Integer updateTotalAmoutType) {
		this.updateTotalAmoutType = updateTotalAmoutType;
	}

	public Date getUpdateDataTime() {
		return updateDataTime;
	}

	public void setUpdateDataTime(Date updateDataTime) {
		this.updateDataTime = updateDataTime;
	}

	public List<SkuServiceTag> getTagList() {
		return tagList;
	}

	public void setTagList(List<SkuServiceTag> tagList) {
		this.tagList = tagList;
	}

	public BigDecimal getRealTotalAmount() {
		return realTotalAmount;
	}

	public void setRealTotalAmount(BigDecimal realTotalAmount) {
		this.realTotalAmount = realTotalAmount;
	}

	public BigDecimal getAppliedAmount() {
		return appliedAmount;
	}

	public void setAppliedAmount(BigDecimal appliedAmount) {
		this.appliedAmount = appliedAmount;
	}

	public BigDecimal getInvoicedAmount() {
		return invoicedAmount;
	}

	public void setInvoicedAmount(BigDecimal invoicedAmount) {
		this.invoicedAmount = invoicedAmount;
	}

	public Integer getCanInvoicedNum() {
		return canInvoicedNum;
	}

	public void setCanInvoicedNum(Integer canInvoicedNum) {
		this.canInvoicedNum = canInvoicedNum;
	}

	public BigDecimal getAppliedNum() {
		return appliedNum;
	}

	public void setAppliedNum(BigDecimal appliedNum) {
		this.appliedNum = appliedNum;
	}

	public BigDecimal getInvoicedNum() {
		return invoicedNum;
	}

	public void setInvoicedNum(BigDecimal invoicedNum) {
		this.invoicedNum = invoicedNum;
	}

	// add by Tomcat.Hui 2019/11/12 13:32 .Desc: VDERP-1325 分批开票. end

	/**
	 * serialVersionUID
	 */
	private static final long serialVersionUID = -7609000365294092689L;

	private Integer saleorderGoodsId;

	private Integer saleorderId;

	private String msaleorderNo;

	private Integer elOrderlistId;

	private Integer goodsId;

	private String sku;

	private String goodsName;

	private String brandName;

	private Integer brandId;

	private String model;

	private String unitName;


	/**
	 * 规格
	 */
	private String spec;

	/**
	 * spuType
	 */

	private String spuType;
	/**
	 * 通用名称
	 */
	private String productChineseName;

	/**
	 * 生产企业名称
	 */

	private String manufacturerName;


	/**
	 * 生产企业许可证号
	 */

	private String productCompanyLicence;


	/**
	 * `PRICE` decimal(10,2) DEFAULT '0.00' COMMENT '单价 在耗材商城订单中为优惠后单价' 订单中真实单价
	 */
	private BigDecimal price;

	/**
	 * `REAL_PRICE` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '实际单价 耗材商城实际单价',
	 * 即订单的原来的单价
	 */
	private BigDecimal realPrice;

	/**
	 * sku小计金额
	 */
	private BigDecimal maxSkuRefundAmount;
	/*
	 * V_CORE_SKU表贝登精选市场价
	 * JX_MARKET_PRICE
	 */
	private BigDecimal jxMarketPrice;
	/*
	 * V_CORE_SKU表贝登精销售价
	 * JX_SALE_PRICE
	 */
	private BigDecimal jxSalePrice;

	private Integer currencyUnitId;

	private Integer num;

	private Integer dbBuyNum;// 数据库中对应已采购数量字段

	private String deliveryCycle;

	private Integer deliveryDirect;

	private String deliveryDirectComments;

	private String registrationNumber;

	private String supplierName;

	private BigDecimal referenceCostPrice;

	private transient String referencePrice;

	private String referenceDeliveryCycle;

	private Integer reportStatus;

	private String reportComments;

	private Integer haveInstallation;

	private String goodsComments;

	private String insideComments;

	private Integer arrivalUserId;

	private Integer arrivalStatus;

	private Long arrivalTime;

	private Integer isDelete;

	private Long addTime;

	private Integer creator;

	private Long modTime;

	private Integer updater;

	private Integer deliveryNum;

	private Integer deliveryStatus;

	private Long deliveryTime;

	private Integer isIgnore;

	private Long ignoreTime;// 忽略时间

	private Integer ignoreUserId;// 忽略人

	private String purchasingPrice;

	private String goodsLevelName;// 产品级别

	private String goodsPositionName;

	private Goods goods;// 产品信息

	private String manageCategoryName;// 管理类别名称

	private String materialCode;// 物料编码

	private String storageAddress;// 存储位置

	private Integer noOutNum;// 拣货未发出产品数

	private Integer nowNum;// 本次拣货产品数

	private String allPrice;// 商品总价

	private String prices;// 单价

	private List<WarehouseGoodsOperateLog> wlist;// 商品入库详情列表

	private Integer pickCnt;// 拣货总数

	private Integer totalNum;// 库存总数

	private String isOut;// 是否是出库1是0否

	private List<String> whList;// 仓库名称列表

	private Integer eNum = 0;// 快递发货数

	private Integer buyNum;// 采购数量

	private Integer warehouseNum;// 入库数量

	private BigDecimal averagePrice;// 近半年销售均价

	private BigDecimal averageDeliveryCycle;// 近半年货期均值

	private Integer bussinessId;// 业务id（换货订单、样品外借订单）

	private Integer bussinessType = 0;// 业务类型：1销售入库 2销售出库3销售换货入库4销售换货出库5销售退货入库6采购退货出库7采购换货出库8采购换货入库

	private Integer companyId;

	private BigDecimal cgPrice;// 采购单价

	private Integer categoryId;// 分类Id

	private BigDecimal settlementPrice;// 结算价

	private Integer areaControl = 0;// 是否区域控制0：否；；1：是

	private BigDecimal channelPrice;// 核算价格

	private String channelDeliveryCycle, delivery;// 核价货期

	private String buyOrderNo;// 采购单号

	private Integer expressNum;// 快递已发送数量

	private BigDecimal expressPrice;// 快递已发送产品价格均价

	private String serviceNo;// 录保卡好
	private Integer pCountAll = 0;// 自动分配拣货数量总数
	private String creatorNm;

	private Saleorder saleorder;

	private BigDecimal settlePrice;// 产品结算价格
	private Integer barcodeId;// 二维码id

	private BigDecimal costPrice;// 成本价

	private List<Buyorder> buyorderList;// 对应采购订单列表

	private Long satisfyDeliveryTime;// 可发货时间

	private String satisfyDeliveryTimeStr;// 可发货时间

	private Integer buyorderStatus;// 当前销售产品的采购状态(0-未采购；1-部分采购；2-全部采购)

	private Integer afterReturnNum;
	private BigDecimal afterReturnAmount;

	private Integer warehouseReturnNum;

	private Integer terminalTraderId, terminalTraderType;// 终端客户ID和类型

	private String terminalTraderName;

	private Integer salesAreaId;// 报价区域Id

	private String salesArea;// H报价区域

	private Integer customerNature;// 报价客户性质

	private String goodsUserIdStr;// 产品负责人

	private List<Integer> saleorderGoodsIdList;

	private Integer lockedStatus;// 锁定状态0未锁定 1已锁定

	private  Integer occupyNum;//占用数量

	private Integer isActionGoods;//是否为活动商品   0否  1是

	private Integer actionOccupyNum;//活动占用库存数量

	private Integer actionLockCount;//活动锁定库存

	private Integer isCoupons;//是否使用优惠券

	private List<Integer> goodsIdList = new ArrayList<>();
	/*商品流切换*/
	private String checkStatus;
	private String assis;
	private String manager;
	private String skuNew,goodsNameNew,brandNameNew,modelNew,unitNameNew;

	private String saleorderNo;

	private Long validTime;

	private Integer paymentStatus;

	private String title;

	private String userName;

	/**
	 * '是否需报备 0否 1是'
	 */
	private Integer isNeedReport;

	/**
	 * 是否获得授权 0否 1是
	 */
	private  Integer isAuthorized;

	private SkuAuthorizationVo skuAuthorizationVo;

	/**
	 * 协议商品标示
	 */
	private Integer contractedGoodsFlag;

	/**
	 * 商品标签
	 */
	private String skuTags;

	/**
	 * 时效状态
	 */
	private Integer aging;

	/**
	 * 预警等级
	 */
	private Integer warnLevel;

	/**
	 * 时效监控开始时间
	 */
	private Long agingTime;

	private Integer shNum;//售后数量

	private Integer joinBuyorderUser;
	private Long joinBuyorderOrderTime;

	private Integer status;

	/**
	 * 商品级别
	 *
	 */
	private Integer goodsLevelNo;

	/**
	 * 商品档位
	 *
	 */
	private Integer goodsPositionNo;

	/**
	 *订单商品采购状态
	 */
	private Integer purchaseStatusData;

	/**
	 * 是否为改低价商品
	 */
	private Integer isLowerGoods;

	/**
	 * 核价经销价
	 */
	private BigDecimal checkPrice;

	/**
	 * 大数据终端id（通过大数据查询出对应的终端信息）
	 */
	private String dwhTerminalId;

	/**
	 * 统一社会信用代码
	 */
	private String unifiedSocialCreditIdentifier;

    /**
     * 组织机构代码
     */
    private String organizationCode;

	/**
	 * 省
	 */
	private Integer provinceId;

	/**
	 * 市
	 */
	private Integer cityId;

	/**
	 * 区
	 */
	private Integer areaId;

	/**
	 * 省名
	 */
	private String provinceName;

	/**
	 * 市名
	 */
	private String cityName;

	/**
	 * 区名
	 */
	private String areaName;

	/**
	 * 收货T+N未开票数量
	 */
	private BigDecimal TNNum;

	/**
	 * 满足收货T+N日期
	 */
	private String TNDateStr;

	/**
	 * 近期采购价-来自onedata
	 */
	private BigDecimal maoLiBuyPrice;

	/**
	 * 近期采购价的来源，近一个月还是近三个月等等
	 */
	private String maoLiBuyPriceDesc;


	/**
	 * 商品的主图
	 */
	private String imgUrl;

	public BigDecimal getMaoLiBuyPrice() {
		return maoLiBuyPrice;
	}

	public void setMaoLiBuyPrice(BigDecimal maoLiBuyPrice) {
		this.maoLiBuyPrice = maoLiBuyPrice;
	}

	public String getMaoLiBuyPriceDesc() {
		return maoLiBuyPriceDesc;
	}

	public void setMaoLiBuyPriceDesc(String maoLiBuyPriceDesc) {
		this.maoLiBuyPriceDesc = maoLiBuyPriceDesc;
	}



	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public BigDecimal getTNNum() {
		return TNNum;
	}

	public void setTNNum(BigDecimal TNNum) {
		this.TNNum = TNNum;
	}

	public String getTNDateStr() {
		return TNDateStr;
	}

	public void setTNDateStr(String TNDateStr) {
		this.TNDateStr = TNDateStr;
	}

	public Integer getIsLowerGoods() {
		return isLowerGoods;
	}

	public void setIsLowerGoods(Integer isLowerGoods) {
		this.isLowerGoods = isLowerGoods;
	}

	public BigDecimal getCheckPrice() {
		return checkPrice;
	}

	public void setCheckPrice(BigDecimal checkPrice) {
		this.checkPrice = checkPrice;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getGoodsLevelNo() {
		return goodsLevelNo;
	}

	public void setGoodsLevelNo(Integer goodsLevelNo) {
		this.goodsLevelNo = goodsLevelNo;
	}

	public Integer getGoodsPositionNo() {
		return goodsPositionNo;
	}

	public void setGoodsPositionNo(Integer goodsPositionNo) {
		this.goodsPositionNo = goodsPositionNo;
	}

	public Integer getJoinBuyorderUser() {
		return joinBuyorderUser;
	}

	public void setJoinBuyorderUser(Integer joinBuyorderUser) {
		this.joinBuyorderUser = joinBuyorderUser;
	}

	public Long getJoinBuyorderOrderTime() {
		return joinBuyorderOrderTime;
	}

	public void setJoinBuyorderOrderTime(Long joinBuyorderOrderTime) {
		this.joinBuyorderOrderTime = joinBuyorderOrderTime;
	}

	public Integer getPurchaseStatusData() {
		return purchaseStatusData;
	}

	public void setPurchaseStatusData(Integer purchaseStatusData) {
		this.purchaseStatusData = purchaseStatusData;
	}

	public String getSpec() {
		return spec;
	}

	public void setSpec(String spec) {
		this.spec = spec;
	}

	public String getSpuType() {
		return spuType;
	}

	public void setSpuType(String spuType) {
		this.spuType = spuType;
	}

	public String getProductChineseName() {
		return productChineseName;
	}

	public void setProductChineseName(String productChineseName) {
		this.productChineseName = productChineseName;
	}

	public String getManufacturerName() {
		return manufacturerName;
	}

	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}

	public String getProductCompanyLicence() {
		return productCompanyLicence;
	}

	public void setProductCompanyLicence(String productCompanyLicence) {
		this.productCompanyLicence = productCompanyLicence;
	}

	/**
	 * 采购到货时长（工作日）
	 */
	private Integer perchaseTime;

	private Integer perfermenceDeliveryTime;

	private String labelCodes;

	private String labelNames;

	public Integer getPerfermenceDeliveryTime() {
		return perfermenceDeliveryTime;
	}

	public void setPerfermenceDeliveryTime(Integer perfermenceDeliveryTime) {
		this.perfermenceDeliveryTime = perfermenceDeliveryTime;
	}

	public Integer getPerchaseTime() {
		return perchaseTime;
	}

	public void setPerchaseTime(Integer perchaseTime) {
		this.perchaseTime = perchaseTime;
	}

	public String getLabelCodes() {
		return labelCodes;
	}

	public void setLabelCodes(String labelCodes) {
		this.labelCodes = labelCodes;
	}

	public String getLabelNames() {
		return labelNames;
	}

	public void setLabelNames(String labelNames) {
		this.labelNames = labelNames;
	}

	public Integer getAging() {
		return aging;
	}

	public void setAging(Integer aging) {
		this.aging = aging;
	}

	public Integer getWarnLevel() {
		return warnLevel;
	}

	public void setWarnLevel(Integer warnLevel) {
		this.warnLevel = warnLevel;
	}

	public Long getAgingTime() {
		return agingTime;
	}

	public void setAgingTime(Long agingTime) {
		this.agingTime = agingTime;
	}

	public Integer getIsNeedReport() {
		return isNeedReport;
	}

	public void setIsNeedReport(Integer isNeedReport) {
		this.isNeedReport = isNeedReport;
	}

	public Integer getIsAuthorized() {
		return isAuthorized;
	}

	public void setIsAuthorized(Integer isAuthorized) {
		this.isAuthorized = isAuthorized;
	}

	public SkuAuthorizationVo getSkuAuthorizationVo() {
		return skuAuthorizationVo;
	}

	public void setSkuAuthorizationVo(SkuAuthorizationVo skuAuthorizationVo) {
		this.skuAuthorizationVo = skuAuthorizationVo;
	}

	private QuoteorderConsultReply quoteorderConsultReply;

	private String declareRange;//预计可发货时间参考值（天）

	private String declareDeliveryRange;//预计可发货时间填报值（天）

	public QuoteorderConsultReply getQuoteorderConsultReply() {
		return quoteorderConsultReply;
	}


	private Integer logicalWarehouseId;



	public void setQuoteorderConsultReply(QuoteorderConsultReply quoteorderConsultReply) {
		this.quoteorderConsultReply = quoteorderConsultReply;
	}


	//商品总额
	private BigDecimal skuAmount;

	//优惠后的价格
	private BigDecimal couponPrice;

	private Integer isRisk;

	/**
	 * 风控内容
	 */
	private String riskComments;

	private BigDecimal buyPrice;//成本价

	private BigDecimal historyAvgPrice; //历史成本价

	/**
	 * 内部备注标签HTML
	 */
	private String componentHtml;

	private String oldComponentHtml;

	/**
	 * 是否批量更新产品内部备注
	 */
	private Integer hasRemark;

	//采购对接人ID
	private Integer buyDockUserId;

	//采购状态更新时间
	private String buyProcessModTimeString;
	private Long buyProcessModTime;
	//采购要求变更理由
	private String buyProcessModReson;
	//是否绑定了采购单:1绑定了，0未绑定
	private Integer bindedBuyOrder;
	//组件ID，记录改条记录的采购要求对应在remakeComponent表中的ID
	private Integer componentId;
	//采购要求
	private String buyOrderDemand;
	//采购对接人英文名
	private String buyDockUserName;
	//销售单所绑定的采购单单号
	private String bindBuyOrderId;
	//销售单所绑定的采购单预计可发货时间
	private String sendGoodTimeString;
	//销售单所绑定的采购单预计可收货时间
	private String receiveGoodTimeString;

	private Integer specialDelivery ;

	private String deliveryCommentStr;

	private Integer isGift;

	/**
	 * 是否现货现价直购(0否，1是)
	 */
	private Integer isDirectPurchase;

	public Integer getIsDirectPurchase() {
		return isDirectPurchase;
	}

	public void setIsDirectPurchase(Integer isDirectPurchase) {
		this.isDirectPurchase = isDirectPurchase;
	}

	public Integer getIsGift() {
		return isGift;
	}

	public void setIsGift(Integer isGift) {
		this.isGift = isGift;
	}

	public String getOldComponentHtml() {
		return oldComponentHtml;
	}

	public void setOldComponentHtml(String oldComponentHtml) {
		this.oldComponentHtml = oldComponentHtml;
	}

	public String getDeliveryCommentStr() {
        return deliveryCommentStr;
    }

    public void setDeliveryCommentStr(String deliveryCommentStr) {
        this.deliveryCommentStr = deliveryCommentStr;
    }

    public Integer getSpecialDelivery() {
		return specialDelivery;
	}

	public void setSpecialDelivery(Integer specialDelivery) {
		this.specialDelivery = specialDelivery;
	}

	public String getBindBuyOrderId() {
		return bindBuyOrderId;
	}

	public void setBindBuyOrderId(String bindBuyOrderId) {
		this.bindBuyOrderId = bindBuyOrderId;
	}

	public String getSendGoodTimeString() {
		return sendGoodTimeString;
	}

	public void setSendGoodTimeString(String sendGoodTimeString) {
		this.sendGoodTimeString = sendGoodTimeString;
	}

	public String getReceiveGoodTimeString() {
		return receiveGoodTimeString;
	}

	public void setReceiveGoodTimeString(String receiveGoodTimeString) {
		this.receiveGoodTimeString = receiveGoodTimeString;
	}

	public String getBuyDockUserName() {
		return buyDockUserName;
	}

	public void setBuyDockUserName(String buyDockUserName) {
		this.buyDockUserName = buyDockUserName;
	}

	public String getBuyOrderDemand() {
		return buyOrderDemand;
	}


	public void setBuyOrderDemand(String buyOrderDemand) {
		this.buyOrderDemand = buyOrderDemand;
	}

	public Integer getComponentId() {
		return componentId;
	}

	public void setComponentId(Integer componentId) {
		this.componentId = componentId;
	}

	public Integer getBindedBuyOrder() {
		return bindedBuyOrder;
	}

	public void setBindedBuyOrder(Integer bindedBuyOrder) {
		this.bindedBuyOrder = bindedBuyOrder;
	}

	public Integer getBuyDockUserId() {
		return buyDockUserId;
	}

	public void setBuyDockUserId(Integer buyDockUserId) {
		this.buyDockUserId = buyDockUserId;
	}

	public String getBuyProcessModTimeString() {
		return buyProcessModTimeString;
	}

	public void setBuyProcessModTimeString(String buyProcessModTimeString) {
		this.buyProcessModTimeString = buyProcessModTimeString;
	}

	public Long getBuyProcessModTime() {
		return buyProcessModTime;
	}

	public void setBuyProcessModTime(Long buyProcessModTime) {
		this.buyProcessModTime = buyProcessModTime;
	}

	public String getBuyProcessModReson() {
		return buyProcessModReson;
	}

	public void setBuyProcessModReson(String buyProcessModReson) {
		this.buyProcessModReson = buyProcessModReson;
	}

	public BigDecimal getBuyPrice() {
		return buyPrice;
	}

	public void setBuyPrice(BigDecimal buyPrice) {
		this.buyPrice = buyPrice;
	}

	public BigDecimal getHistoryAvgPrice() {
		return historyAvgPrice;
	}

	public void setHistoryAvgPrice(BigDecimal historyAvgPrice) {
		this.historyAvgPrice = historyAvgPrice;
	}

	public String getRiskComments() {
		return riskComments;
	}

	public void setRiskComments(String riskComments) {
		this.riskComments = riskComments;
	}

	public Integer getIsRisk() {
		return isRisk;
	}

	public void setIsRisk(Integer isRisk) {
		this.isRisk = isRisk;
	}

	public Integer getLogicalWarehouseId() {
		return logicalWarehouseId;
	}

	public void setLogicalWarehouseId(Integer logicalWarehouseId) {
		this.logicalWarehouseId = logicalWarehouseId;
	}


	// add by Tomcat.Hui 2019/11/12 13:32 .Desc: VDERP-1325 分批开票. end

	public String getSkuNew() {
		return skuNew;
	}

	public void setSkuNew(String skuNew) {
		this.skuNew = skuNew;
	}

	public String getGoodsNameNew() {
		return goodsNameNew;
	}

	public void setGoodsNameNew(String goodsNameNew) {
		this.goodsNameNew = goodsNameNew;
	}

	public String getBrandNameNew() {
		return brandNameNew;
	}

	public void setBrandNameNew(String brandNameNew) {
		this.brandNameNew = brandNameNew;
	}

	public String getModelNew() {
		return modelNew;
	}

	public void setModelNew(String modelNew) {
		this.modelNew = modelNew;
	}

	public String getUnitNameNew() {
		return unitNameNew;
	}

	public void setUnitNameNew(String unitNameNew) {
		this.unitNameNew = unitNameNew;
	}

	public String getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(String checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getAssis() {
		return assis;
	}

	public void setAssis(String assis) {
		this.assis = assis;
	}

	public String getManager() {
		return manager;
	}

	public void setManager(String manager) {
		this.manager = manager;
	}

	public Integer getIsActionGoods() {
		return isActionGoods;
	}

	public void setIsActionGoods(Integer isActionGoods) {
		this.isActionGoods = isActionGoods;
	}

	public Integer getActionOccupyNum() {
		return actionOccupyNum;
	}

	public void setActionOccupyNum(Integer actionOccupyNum) {
		this.actionOccupyNum = actionOccupyNum;
	}

	public Integer getOccupyNum() {
		return occupyNum;
	}

	public void setOccupyNum(Integer occupyNum) {
		this.occupyNum = occupyNum;
	}

	public BigDecimal getJxMarketPrice() {
		return jxMarketPrice;
	}

	public void setJxMarketPrice(BigDecimal jxMarketPrice) {
		this.jxMarketPrice = jxMarketPrice;
	}

	public BigDecimal getJxSalePrice() {
		return jxSalePrice;
	}

	public void setJxSalePrice(BigDecimal jxSalePrice) {
		this.jxSalePrice = jxSalePrice;
	}

	public BigDecimal getAfterReturnAmount() {
		return afterReturnAmount;
	}

	public void setAfterReturnAmount(BigDecimal afterReturnAmount) {
		this.afterReturnAmount = afterReturnAmount;
	}
	public String getMsaleorderNo() {
		return msaleorderNo;
	}

	public void setMsaleorderNo(String msaleorderNo) {
		this.msaleorderNo = msaleorderNo;
	}
	public List<Integer> getGoodsIdList() {
		return goodsIdList;
	}

	public void setGoodsIdList(List<Integer> goodsIdList) {
		this.goodsIdList = goodsIdList;
	}

	public Integer getLockedStatus() {
		return lockedStatus;
	}

	public void setLockedStatus(Integer lockedStatus) {
		this.lockedStatus = lockedStatus;
	}

	public List<Integer> getSaleorderGoodsIdList() {
		return saleorderGoodsIdList;
	}

	public void setSaleorderGoodsIdList(List<Integer> saleorderGoodsIdList) {
		this.saleorderGoodsIdList = saleorderGoodsIdList;
	}

	public String getAllPrice() {
		return allPrice;
	}

	public void setAllPrice(String allPrice) {
		this.allPrice = allPrice;
	}

	public String getPrices() {
		return prices;
	}

	public void setPrices(String prices) {
		this.prices = prices;
	}

	public Integer getTerminalTraderId() {
		return terminalTraderId;
	}

	public void setTerminalTraderId(Integer terminalTraderId) {
		this.terminalTraderId = terminalTraderId;
	}

	public Integer getTerminalTraderType() {
		return terminalTraderType;
	}

	public void setTerminalTraderType(Integer terminalTraderType) {
		this.terminalTraderType = terminalTraderType;
	}

	public String getTerminalTraderName() {
		return terminalTraderName;
	}

	public void setTerminalTraderName(String terminalTraderName) {
		this.terminalTraderName = terminalTraderName == null ? null : terminalTraderName.replaceAll("\\(","（").replaceAll("\\)","）").trim();
	}

	public Integer getSalesAreaId() {
		return salesAreaId;
	}

	public void setSalesAreaId(Integer salesAreaId) {
		this.salesAreaId = salesAreaId;
	}

	public String getSalesArea() {
		return salesArea;
	}

	public void setSalesArea(String salesArea) {
		this.salesArea = salesArea;
	}

	public Integer getCustomerNature() {
		return customerNature;
	}

	public void setCustomerNature(Integer customerNature) {
		this.customerNature = customerNature;
	}

	public Integer getAfterReturnNum() {
		return afterReturnNum;
	}

	public void setAfterReturnNum(Integer afterReturnNum) {
		this.afterReturnNum = afterReturnNum;
	}

	public Integer getBuyorderStatus() {
		return buyorderStatus;
	}

	public void setBuyorderStatus(Integer buyorderStatus) {
		this.buyorderStatus = buyorderStatus;
	}

	public Long getSatisfyDeliveryTime() {
		return satisfyDeliveryTime;
	}

	public void setSatisfyDeliveryTime(Long satisfyDeliveryTime) {
		this.satisfyDeliveryTime = satisfyDeliveryTime;
	}

	public Integer getBarcodeId() {
		return barcodeId;
	}

	public void setBarcodeId(Integer barcodeId) {
		this.barcodeId = barcodeId;
	}

	public Saleorder getSaleorder() {
		return saleorder;
	}

	public void setSaleorder(Saleorder saleorder) {
		this.saleorder = saleorder;
	}

	public Integer getpCountAll() {
		return pCountAll;
	}

	public void setpCountAll(Integer pCountAll) {
		this.pCountAll = pCountAll;
	}

	public String getCreatorNm() {
		return creatorNm;
	}

	public void setCreatorNm(String creatorNm) {
		this.creatorNm = creatorNm;
	}

	public String getServiceNo() {
		return serviceNo;
	}

	public void setServiceNo(String serviceNo) {
		this.serviceNo = serviceNo;
	}

	public String getBuyOrderNo() {
		return buyOrderNo;
	}

	public void setBuyOrderNo(String buyOrderNo) {
		this.buyOrderNo = buyOrderNo;
	}

	public Integer getExpressNum() {
		return expressNum;
	}

	public void setExpressNum(Integer expressNum) {
		this.expressNum = expressNum;
	}

	public BigDecimal getExpressPrice() {
		return expressPrice;
	}

	public void setExpressPrice(BigDecimal expressPrice) {
		this.expressPrice = expressPrice;
	}

	public String getDelivery() {
		return delivery;
	}

	public void setDelivery(String delivery) {
		this.delivery = delivery;
	}

	public String getChannelDeliveryCycle() {
		return channelDeliveryCycle;
	}

	public void setChannelDeliveryCycle(String channelDeliveryCycle) {
		this.channelDeliveryCycle = channelDeliveryCycle;
	}

	public Integer getAreaControl() {
		return areaControl;
	}

	public void setAreaControl(Integer areaControl) {
		this.areaControl = areaControl;
	}

	public BigDecimal getChannelPrice() {
		return channelPrice;
	}

	public void setChannelPrice(BigDecimal channelPrice) {
		this.channelPrice = channelPrice;
	}

	public BigDecimal getCgPrice() {
		return cgPrice;
	}

	public void setCgPrice(BigDecimal cgPrice) {
		this.cgPrice = cgPrice;
	}

	private BigDecimal lastOrderPrice;// 上次购买价格

	private Integer lastReferenceUser;

	private String goodsUserNm;// 产品负责人

	private Integer tNum;// 退货数

	public Integer gettNum() {
		return tNum;
	}

	public void settNum(Integer tNum) {
		this.tNum = tNum;
	}

	public Integer getLastReferenceUser() {
		return lastReferenceUser;
	}

	public void setLastReferenceUser(Integer lastReferenceUser) {
		this.lastReferenceUser = lastReferenceUser;
	}

	public String getGoodsUserNm() {
		return goodsUserNm;
	}

	public void setGoodsUserNm(String goodsUserNm) {
		this.goodsUserNm = goodsUserNm;
	}

	public BigDecimal getLastOrderPrice() {
		return lastOrderPrice;
	}

	public void setLastOrderPrice(BigDecimal lastOrderPrice) {
		this.lastOrderPrice = lastOrderPrice;
	}

	public Integer getCompanyId() {
		return companyId;
	}

	public void setCompanyId(Integer companyId) {
		this.companyId = companyId;
	}

	public Integer getBussinessId() {
		return bussinessId;
	}

	public void setBussinessId(Integer bussinessId) {
		this.bussinessId = bussinessId;
	}

	public Integer getBussinessType() {
		return bussinessType;
	}

	public void setBussinessType(Integer bussinessType) {
		this.bussinessType = bussinessType;
	}

	public Integer getBuyNum() {
		return buyNum;
	}

	public void setBuyNum(Integer buyNum) {
		this.buyNum = buyNum;
	}

	public Integer getWarehouseNum() {
		return warehouseNum;
	}

	public void setWarehouseNum(Integer warehouseNum) {
		this.warehouseNum = warehouseNum;
	}

	public Integer geteNum() {
		return eNum;
	}

	public void seteNum(Integer eNum) {
		this.eNum = eNum;
	}

	public List<String> getWhList() {
		return whList;
	}

	public void setWhList(List<String> whList) {
		this.whList = whList;
	}

	public String getIsOut() {
		return isOut;
	}

	public void setIsOut(String isOut) {
		this.isOut = isOut;
	}

	public Integer getTotalNum() {
		return totalNum;
	}

	public void setTotalNum(Integer totalNum) {
		this.totalNum = totalNum;
	}

	public Integer getPickCnt() {
		return pickCnt;
	}

	public void setPickCnt(Integer pickCnt) {
		this.pickCnt = pickCnt;
	}

	public List<WarehouseGoodsOperateLog> getWlist() {
		return wlist;
	}

	public void setWlist(List<WarehouseGoodsOperateLog> wlist) {
		this.wlist = wlist;
	}

	public Integer getNoOutNum() {
		return noOutNum;
	}

	public void setNoOutNum(Integer noOutNum) {
		this.noOutNum = noOutNum;
	}

	public Integer getNowNum() {
		return nowNum;
	}

	public void setNowNum(Integer nowNum) {
		this.nowNum = nowNum;
	}

	public String getStorageAddress() {
		return storageAddress;
	}

	public void setStorageAddress(String storageAddress) {
		this.storageAddress = storageAddress;
	}

	private Integer LineNum;

	public Integer getLineNum() {
		return LineNum;
	}

	public void setLineNum(Integer lineNum) {
		LineNum = lineNum;
	}

	public String getMaterialCode() {
		return materialCode;
	}

	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode;
	}

	public Integer getSaleorderGoodsId() {
		return saleorderGoodsId;
	}

	public void setSaleorderGoodsId(Integer saleorderGoodsId) {
		this.saleorderGoodsId = saleorderGoodsId;
	}

	public Integer getSaleorderId() {
		return saleorderId;
	}

	public void setSaleorderId(Integer saleorderId) {
		this.saleorderId = saleorderId;
	}

	public Integer getGoodsId() {
		return goodsId;
	}

	public void setGoodsId(Integer goodsId) {
		this.goodsId = goodsId;
	}

	public String getSku() {
		return sku;
	}

	public void setSku(String sku) {
		this.sku = sku == null ? null : sku.trim();
	}

	public String getGoodsName() {
		return goodsName;
	}

	public void setGoodsName(String goodsName) {
		this.goodsName = goodsName == null ? null : goodsName.trim();
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName == null ? null : brandName.trim();
	}

	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model == null ? null : model.trim();
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName == null ? null : unitName.trim();
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	public Integer getCurrencyUnitId() {
		return currencyUnitId;
	}

	public void setCurrencyUnitId(Integer currencyUnitId) {
		this.currencyUnitId = currencyUnitId;
	}

	public Integer getNum() {
		return num;
	}

	public void setNum(Integer num) {
		this.num = num;
	}

	public String getDeliveryCycle() {
		return deliveryCycle;
	}

	public void setDeliveryCycle(String deliveryCycle) {
		this.deliveryCycle = deliveryCycle == null ? null : deliveryCycle.trim();
	}

	public Integer getDeliveryDirect() {
		return deliveryDirect;
	}

	public void setDeliveryDirect(Integer deliveryDirect) {
		this.deliveryDirect = deliveryDirect;
	}

	public String getDeliveryDirectComments() {
		return deliveryDirectComments;
	}

	public void setDeliveryDirectComments(String deliveryDirectComments) {
		this.deliveryDirectComments = deliveryDirectComments == null ? null : deliveryDirectComments.trim();
	}

	public String getRegistrationNumber() {
		return registrationNumber;
	}

	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber == null ? null : registrationNumber.trim();
	}

	public String getSupplierName() {
		return supplierName;
	}

    public void setSupplierName(String supplierName) {
        this.supplierName = supplierName == null ? null : supplierName.replaceAll("\\(","（").replaceAll("\\)","）").trim();
    }

	public BigDecimal getReferenceCostPrice() {
		return referenceCostPrice;
	}

	public void setReferenceCostPrice(BigDecimal referenceCostPrice) {
		this.referenceCostPrice = referenceCostPrice;
	}

	public String getReferencePrice() {
		return referencePrice;
	}

	public void setReferencePrice(String referencePrice) {
		this.referencePrice = referencePrice;
	}

	public String getReferenceDeliveryCycle() {
		return referenceDeliveryCycle;
	}

	public void setReferenceDeliveryCycle(String referenceDeliveryCycle) {
		this.referenceDeliveryCycle = referenceDeliveryCycle == null ? null : referenceDeliveryCycle.trim();
	}

	public Integer getReportStatus() {
		return reportStatus;
	}

	public void setReportStatus(Integer reportStatus) {
		this.reportStatus = reportStatus;
	}

	public String getReportComments() {
		return reportComments;
	}

	public void setReportComments(String reportComments) {
		this.reportComments = reportComments == null ? null : reportComments.trim();
	}

	public Integer getHaveInstallation() {
		return haveInstallation;
	}

	public void setHaveInstallation(Integer haveInstallation) {
		this.haveInstallation = haveInstallation;
	}

	public String getGoodsComments() {
		return goodsComments;
	}

	public void setGoodsComments(String goodsComments) {
		this.goodsComments = goodsComments == null ? null : goodsComments.trim();
	}

	public String getInsideComments() {
		return insideComments;
	}

	public void setInsideComments(String insideComments) {
		this.insideComments = insideComments == null ? null : insideComments.trim();
	}

	public Integer getArrivalUserId() {
		return arrivalUserId;
	}

	public void setArrivalUserId(Integer arrivalUserId) {
		this.arrivalUserId = arrivalUserId;
	}

	public Integer getArrivalStatus() {
		return arrivalStatus;
	}

	public void setArrivalStatus(Integer arrivalStatus) {
		this.arrivalStatus = arrivalStatus;
	}

	public Long getArrivalTime() {
		return arrivalTime;
	}

	public void setArrivalTime(Long arrivalTime) {
		this.arrivalTime = arrivalTime;
	}

	public Integer getIsDelete() {
		return isDelete;
	}

	public void setIsDelete(Integer isDelete) {
		this.isDelete = isDelete;
	}

	public Long getAddTime() {
		return addTime;
	}

	public void setAddTime(Long addTime) {
		this.addTime = addTime;
	}

	public Integer getCreator() {
		return creator;
	}

	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	public Long getModTime() {
		return modTime;
	}

	public void setModTime(Long modTime) {
		this.modTime = modTime;
	}

	public Integer getUpdater() {
		return updater;
	}

	public void setUpdater(Integer updater) {
		this.updater = updater;
	}

	public Integer getDeliveryNum() {
		return deliveryNum;
	}

	public void setDeliveryNum(Integer deliveryNum) {
		this.deliveryNum = deliveryNum;
	}

	public Integer getDeliveryStatus() {
		return deliveryStatus;
	}

	public void setDeliveryStatus(Integer deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}

	public Long getDeliveryTime() {
		return deliveryTime;
	}

	public void setDeliveryTime(Long deliveryTime) {
		this.deliveryTime = deliveryTime;
	}

	public Integer getIsIgnore() {
		return isIgnore;
	}

	public void setIsIgnore(Integer isIgnore) {
		this.isIgnore = isIgnore;
	}

	public String getPurchasingPrice() {
		return purchasingPrice;
	}

	public void setPurchasingPrice(String purchasingPrice) {
		this.purchasingPrice = purchasingPrice;
	}

	public String getGoodsLevelName() {
		return goodsLevelName;
	}

	public void setGoodsLevelName(String goodsLevelName) {
		this.goodsLevelName = goodsLevelName;
	}

	public Goods getGoods() {
		return goods;
	}

	public void setGoods(Goods goods) {
		this.goods = goods;
	}

	public String getManageCategoryName() {
		return manageCategoryName;
	}

	public void setManageCategoryName(String manageCategoryName) {
		this.manageCategoryName = manageCategoryName;
	}

	public Long getIgnoreTime() {
		return ignoreTime;
	}

	public void setIgnoreTime(Long ignoreTime) {
		this.ignoreTime = ignoreTime;
	}

	public Integer getIgnoreUserId() {
		return ignoreUserId;
	}

	public void setIgnoreUserId(Integer ignoreUserId) {
		this.ignoreUserId = ignoreUserId;
	}

	public Integer getDbBuyNum() {
		return dbBuyNum;
	}

	public void setDbBuyNum(Integer dbBuyNum) {
		this.dbBuyNum = dbBuyNum;
	}

	public BigDecimal getAveragePrice() {
		return averagePrice;
	}

	public void setAveragePrice(BigDecimal averagePrice) {
		this.averagePrice = averagePrice;
	}

	public BigDecimal getAverageDeliveryCycle() {
		return averageDeliveryCycle;
	}

	public void setAverageDeliveryCycle(BigDecimal averageDeliveryCycle) {
		this.averageDeliveryCycle = averageDeliveryCycle;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public BigDecimal getSettlementPrice() {
		return settlementPrice;
	}

	public void setSettlementPrice(BigDecimal settlementPrice) {
		this.settlementPrice = settlementPrice;
	}

	public BigDecimal getSettlePrice() {
		return settlePrice;
	}

	public void setSettlePrice(BigDecimal settlePrice) {
		this.settlePrice = settlePrice;
	}

	public BigDecimal getCostPrice() {
		return costPrice;
	}

	public void setCostPrice(BigDecimal costPrice) {
		this.costPrice = costPrice;
	}

	public List<Buyorder> getBuyorderList() {
		return buyorderList;
	}

	public void setBuyorderList(List<Buyorder> buyorderList) {
		this.buyorderList = buyorderList;
	}

	public String getGoodsUserIdStr() {
		return goodsUserIdStr;
	}

	public void setGoodsUserIdStr(String goodsUserIdStr) {
		this.goodsUserIdStr = goodsUserIdStr;
	}

	public BigDecimal getRealPrice() {
		return realPrice;
	}

	public void setRealPrice(BigDecimal realPrice) {
		this.realPrice = realPrice;
	}

	public BigDecimal getMaxSkuRefundAmount() {
		return maxSkuRefundAmount;
	}

	public void setMaxSkuRefundAmount(BigDecimal maxSkuRefundAmount) {
		this.maxSkuRefundAmount = maxSkuRefundAmount;
	}

	public Integer getWarehouseReturnNum() {
		return warehouseReturnNum;
	}

	public void setWarehouseReturnNum(Integer warehouseReturnNum) {
		this.warehouseReturnNum = warehouseReturnNum;
	}

	public Integer getActionLockCount() {
		return actionLockCount;
	}

	public void setActionLockCount(Integer actionLockCount) {
		this.actionLockCount = actionLockCount;
	}

	public Integer getIsCoupons() {
		return isCoupons;
	}

	public void setIsCoupons(Integer isCoupons) {
		this.isCoupons = isCoupons;
	}

	public BigDecimal getSkuAmount() {
		return skuAmount;
	}

	public void setSkuAmount(BigDecimal skuAmount) {
		this.skuAmount = skuAmount;
	}

	public BigDecimal getCouponPrice() {
		return couponPrice;
	}

	public void setCouponPrice(BigDecimal couponPrice) {
		this.couponPrice = couponPrice;
	}

	private Integer apparatusType;//是否医疗器械

	private String companyName;

	public Integer getApparatusType() {
		return apparatusType;
	}

	public void setApparatusType(Integer apparatusType) {
		this.apparatusType = apparatusType;
	}

	public String getCompanyName() {
		return companyName;
	}

	public void setCompanyName(String companyName) {
		this.companyName = companyName;
	}

	@Override
	public String toString() {
		return "SaleorderGoods{" +
				"saleorderGoodsId=" + saleorderGoodsId +
				", saleorderId=" + saleorderId +
				", msaleorderNo='" + msaleorderNo + '\'' +
				", goodsId=" + goodsId +
				", sku='" + sku + '\'' +
				", goodsName='" + goodsName + '\'' +
				", brandName='" + brandName + '\'' +
				", model='" + model + '\'' +
				", unitName='" + unitName + '\'' +
				", price=" + price +
				", realPrice=" + realPrice +
				", maxSkuRefundAmount=" + maxSkuRefundAmount +
				", jxMarketPrice=" + jxMarketPrice +
				", jxSalePrice=" + jxSalePrice +
				", currencyUnitId=" + currencyUnitId +
				", num=" + num +
				", dbBuyNum=" + dbBuyNum +
				", deliveryCycle='" + deliveryCycle + '\'' +
				", deliveryDirect=" + deliveryDirect +
				", deliveryDirectComments='" + deliveryDirectComments + '\'' +
				", registrationNumber='" + registrationNumber + '\'' +
				", supplierName='" + supplierName + '\'' +
				", referenceCostPrice=" + referenceCostPrice +
				", referencePrice=" + referencePrice +
				", referenceDeliveryCycle='" + referenceDeliveryCycle + '\'' +
				", reportStatus=" + reportStatus +
				", reportComments='" + reportComments + '\'' +
				", haveInstallation=" + haveInstallation +
				", goodsComments='" + goodsComments + '\'' +
				", insideComments='" + insideComments + '\'' +
				", arrivalUserId=" + arrivalUserId +
				", arrivalStatus=" + arrivalStatus +
				", arrivalTime=" + arrivalTime +
				", isDelete=" + isDelete +
				", addTime=" + addTime +
				", creator=" + creator +
				", modTime=" + modTime +
				", updater=" + updater +
				", deliveryNum=" + deliveryNum +
				", deliveryStatus=" + deliveryStatus +
				", deliveryTime=" + deliveryTime +
				", isIgnore=" + isIgnore +
				", ignoreTime=" + ignoreTime +
				", ignoreUserId=" + ignoreUserId +
				", purchasingPrice='" + purchasingPrice + '\'' +
				", goodsLevelName='" + goodsLevelName + '\'' +
				", goods=" + goods +
				", manageCategoryName='" + manageCategoryName + '\'' +
				", materialCode='" + materialCode + '\'' +
				", storageAddress='" + storageAddress + '\'' +
				", noOutNum=" + noOutNum +
				", nowNum=" + nowNum +
				", allPrice='" + allPrice + '\'' +
				", prices='" + prices + '\'' +
				", wlist=" + wlist +
				", pickCnt=" + pickCnt +
				", totalNum=" + totalNum +
				", isOut='" + isOut + '\'' +
				", whList=" + whList +
				", eNum=" + eNum +
				", buyNum=" + buyNum +
				", warehouseNum=" + warehouseNum +
				", averagePrice=" + averagePrice +
				", averageDeliveryCycle=" + averageDeliveryCycle +
				", bussinessId=" + bussinessId +
				", bussinessType=" + bussinessType +
				", companyId=" + companyId +
				", cgPrice=" + cgPrice +
				", categoryId=" + categoryId +
				", settlementPrice=" + settlementPrice +
				", areaControl=" + areaControl +
				", channelPrice=" + channelPrice +
				", channelDeliveryCycle='" + channelDeliveryCycle + '\'' +
				", delivery='" + delivery + '\'' +
				", buyOrderNo='" + buyOrderNo + '\'' +
				", expressNum=" + expressNum +
				", expressPrice=" + expressPrice +
				", serviceNo='" + serviceNo + '\'' +
				", pCountAll=" + pCountAll +
				", creatorNm='" + creatorNm + '\'' +
				", saleorder=" + saleorder +
				", settlePrice=" + settlePrice +
				", barcodeId=" + barcodeId +
				", costPrice=" + costPrice +
				", buyorderList=" + buyorderList +
				", satisfyDeliveryTime=" + satisfyDeliveryTime +
				", buyorderStatus=" + buyorderStatus +
				", afterReturnNum=" + afterReturnNum +
				", afterReturnAmount=" + afterReturnAmount +
				", warehouseReturnNum=" + warehouseReturnNum +
				", terminalTraderId=" + terminalTraderId +
				", terminalTraderType=" + terminalTraderType +
				", terminalTraderName='" + terminalTraderName + '\'' +
				", salesAreaId=" + salesAreaId +
				", salesArea='" + salesArea + '\'' +
				", customerNature=" + customerNature +
				", goodsUserIdStr='" + goodsUserIdStr + '\'' +
				", saleorderGoodsIdList=" + saleorderGoodsIdList +
				", lockedStatus=" + lockedStatus +
				", occupyNum=" + occupyNum +
				", isActionGoods=" + isActionGoods +
				", actionOccupyNum=" + actionOccupyNum +
				", actionLockCount=" + actionLockCount +
				", goodsIdList=" + goodsIdList +
				", lastOrderPrice=" + lastOrderPrice +
				", goodsUserNm='" + goodsUserNm + '\'' +
				", tNum=" + tNum +
				", LineNum=" + LineNum +
				'}';
	}
	public Integer getElOrderlistId() {
		return elOrderlistId;
	}

	public void setElOrderlistId(Integer elOrderlistId) {
		this.elOrderlistId = elOrderlistId;
	}

	public String getSaleorderNo() {
		return saleorderNo;
	}

	public void setSaleorderNo(String saleorderNo) {
		this.saleorderNo = saleorderNo;
	}

	public Long getValidTime() {
		return validTime;
	}

	public void setValidTime(Long validTime) {
		this.validTime = validTime;
	}

	public Integer getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(Integer paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getDeclareRange() {
		return declareRange;
	}

	public void setDeclareRange(String declareRange) {
		this.declareRange = declareRange;
	}

	public String getDeclareDeliveryRange() {
		return declareDeliveryRange;
	}

	public void setDeclareDeliveryRange(String declareDeliveryRange) {
		this.declareDeliveryRange = declareDeliveryRange;
	}

	public Integer getProductAudit() {
		return productAudit;
	}

	public void setProductAudit(Integer productAudit) {
		this.productAudit = productAudit;
	}

	public Integer getDirectSuperiorAudit() {
		return directSuperiorAudit;
	}

	public void setDirectSuperiorAudit(Integer directSuperiorAudit) {
		this.directSuperiorAudit = directSuperiorAudit;
	}

	public Integer getProductAuditUesrid() {
		return productAuditUesrid;
	}

	public void setProductAuditUesrid(Integer productAuditUesrid) {
		this.productAuditUesrid = productAuditUesrid;
	}

	public Integer getContractedGoodsFlag() {
		return contractedGoodsFlag;
	}

	public void setContractedGoodsFlag(Integer contractedGoodsFlag) {
		this.contractedGoodsFlag = contractedGoodsFlag;
	}

	public String getSkuTags() {
		return skuTags;
	}

	public void setSkuTags(String skuTags) {
		this.skuTags = skuTags;
	}

	public String getComponentHtml() {
		return componentHtml;
	}

	public void setComponentHtml(String componentHtml) {
		this.componentHtml = componentHtml;
	}

	public Integer getHasRemark() {
		return hasRemark;
	}

	public void setHasRemark(Integer hasRemark) {
		this.hasRemark = hasRemark;
	}

	public String getProductBelongIdInfo() {
		return productBelongIdInfo;
	}

	public void setProductBelongIdInfo(String productBelongIdInfo) {
		this.productBelongIdInfo = productBelongIdInfo;
	}

	public String getProductBelongNameInfo() {
		return productBelongNameInfo;
	}

	public void setProductBelongNameInfo(String productBelongNameInfo) {
		this.productBelongNameInfo = productBelongNameInfo;
	}

	@Override
	public SaleorderGoods clone() {
		SaleorderGoods goods = null;
		try {
			goods = (SaleorderGoods) super.clone();
		} catch (CloneNotSupportedException e){
			logger.error("saleorderGoods克隆失败，",e);
		}
		return goods;
	}

	public String getSatisfyDeliveryTimeStr() {
		return satisfyDeliveryTimeStr;
	}

	public void setSatisfyDeliveryTimeStr(String satisfyDeliveryTimeStr) {
		this.satisfyDeliveryTimeStr = satisfyDeliveryTimeStr;
	}

	public Integer getShNum() {
		return shNum;
	}

	public void setShNum(Integer shNum) {
		this.shNum = shNum;
	}

	public String getGoodsPositionName() {
		return goodsPositionName;
	}

	public void setGoodsPositionName(String goodsPositionName) {
		this.goodsPositionName = goodsPositionName;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	private Integer isVirtureSku;

	public Integer getIsVirtureSku() {
		return isVirtureSku;
	}

	public void setIsVirtureSku(Integer isVirtureSku) {
		this.isVirtureSku = isVirtureSku;
	}

	public String getDwhTerminalId() {
		return dwhTerminalId;
	}

	public void setDwhTerminalId(String dwhTerminalId) {
		this.dwhTerminalId = dwhTerminalId;
	}

	public String getUnifiedSocialCreditIdentifier() {
		return unifiedSocialCreditIdentifier;
	}

	public void setUnifiedSocialCreditIdentifier(String unifiedSocialCreditIdentifier) {
		this.unifiedSocialCreditIdentifier = unifiedSocialCreditIdentifier;
	}

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

	public Integer getProvinceId() {
		return provinceId;
	}

	public void setProvinceId(Integer provinceId) {
		this.provinceId = provinceId;
	}

	public Integer getCityId() {
		return cityId;
	}

	public void setCityId(Integer cityId) {
		this.cityId = cityId;
	}

	public Integer getAreaId() {
		return areaId;
	}

	public void setAreaId(Integer areaId) {
		this.areaId = areaId;
	}

	public String getProvinceName() {
		return provinceName;
	}

	public void setProvinceName(String provinceName) {
		this.provinceName = provinceName;
	}

	public String getCityName() {
		return cityName;
	}

	public void setCityName(String cityName) {
		this.cityName = cityName;
	}

	public String getAreaName() {
		return areaName;
	}

	public void setAreaName(String areaName) {
		this.areaName = areaName;
	}
}
