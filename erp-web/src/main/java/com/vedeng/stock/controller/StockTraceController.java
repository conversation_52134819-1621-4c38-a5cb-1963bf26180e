package com.vedeng.stock.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Opt;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.api.StockTraceService;
import com.vedeng.erp.saleorder.dto.StockTraceDto;
import com.vedeng.erp.saleorder.dto.StockTraceResult;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName StockController.java
 * @Description TODO 货流追踪
 * @createTime 2022年09月02日 16:25:00
 */
@Controller()
@RequestMapping("/stock")
public class StockTraceController {

    @Resource
    private StockTraceService stockTraceService;

    @NoNeedAccessAuthorization
    @RequestMapping("/trace")
    public ModelAndView trace(StockTraceDto stockTrace){
        ModelAndView view = new ModelAndView("/vue/view/stocktrace/index");
        if(BeanUtil.isEmpty(stockTrace)){
            return view;
        }
        List<StockTraceResult> list = stockTraceService.trace(stockTrace);
        view.addObject("list",list);
        return view;
    }

}
