package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.FirstEngageInfo;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 注册证管理类别为一类，删除国标分类、有效期至的值  VDERP-6760
 * @date 2021/6/8 17:06
 */
@Component
@JobHandler(value="registrationNumberTask")
public class RegistrationNumberTask extends AbstractJobHandler {

    private static final Logger LOGGER= LoggerFactory.getLogger(RegistrationNumberTask.class);

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        //获取注册证管理类别为一类的注册证
        List<FirstEngageInfo> firstEngages =  firstEngageMapper.getRegistrationNumber(968);
        LOGGER.info("注册证数据清理任务开始:" + JSON.toJSONString(firstEngages));
        if (CollectionUtils.isNotEmpty(firstEngages)){
            firstEngages.forEach(item ->{
                //删除有效期至的值
                RegistrationNumber registrationNumber = new RegistrationNumber();
                registrationNumber.setRegistrationNumberId(item.getRegistrationNumberId());
                registrationNumber.setEffectiveDate(new Long(0));
                registrationNumber.setUpdater(2);
                registrationNumberMapper.updateByPrimaryKeySelective(registrationNumber);
                //删除国标分类
                FirstEngage firstEngage = new FirstEngage();
                firstEngage.setFirstEngageId(item.getFirstEngageId());
                firstEngage.setStandardCategoryType(0);
                firstEngage.setNewStandardCategoryId(0);
                firstEngage.setOldStandardCategoryId(0);
                firstEngage.setUpdater(2);
                firstEngageMapper.updateSelective(firstEngage);
            });
        }
        LOGGER.info("注册证数据清理任务结束:"+ JSON.toJSONString(firstEngages));
        return SUCCESS;
    }
}
