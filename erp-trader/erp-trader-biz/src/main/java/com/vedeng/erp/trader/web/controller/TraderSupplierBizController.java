package com.vedeng.erp.trader.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.erp.trader.domain.dto.SkuSupplyAuthDetail;
import com.vedeng.erp.trader.domain.vo.SkuSupplyAuthVo;
import com.vedeng.erp.trader.service.TraderSupplierBizService;
import com.vedeng.goods.query.RelatedSkuQuery;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.goods.vo.CoreSkuVo;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.TraderSupplierService;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@RequestMapping("/trader/tradersupplier")
@Controller
public class TraderSupplierBizController  {


    @Value("${oss_http}")
    private String ossHttp;

    @Autowired
    private TraderSupplierService traderSupplierService;
    @Autowired
    private TraderSupplierBizService traderSupplierBizService;

    @Autowired
    private GoodsApiService goodsApiService;

    /**
     * @param request
     * @param traderId
     * @param traderSupplierId
     * @param pageNo
     * @param pageSize
     * @return
     * <AUTHOR>
     * @desc 供应商授权书列表
     */
    @RequestMapping("/authList")
    public ModelAndView supplyAuthList(HttpServletRequest request, Integer traderId, Integer traderSupplierId,
                                       @RequestParam(required = false, defaultValue = "1") Integer pageNo, Integer pageSize) {
        ModelAndView mv = new ModelAndView("trader/supplier/supplier_auth_list");
        //页头高亮
        mv.addObject("method", "authList");
        //获取供应商相关
        TraderSupplier traderSupplier = new TraderSupplier();
        traderSupplier.setTraderSupplierId(traderSupplierId);
        traderSupplier.setTraderId(traderId);
        TraderSupplierVo traderSupplierVo = traderSupplierService.getTraderSupplierBaseInfo(traderSupplier);
        mv.addObject("traderSupplier", traderSupplierVo);
        //获取授权书相关信息
        List<SkuSupplyAuthVo> skuSupplyAuthVoList = traderSupplierBizService.queryAuthInfoByTraderSupplierId(traderSupplierId);
        mv.addObject("oss_http", ossHttp);
        mv.addObject("skuSupplyAuthVoList", skuSupplyAuthVoList);
        mv.addObject("traderId", traderId);
        mv.addObject("traderSupplierId", traderSupplierId);
        return mv;
    }

    /**
     * @param request
     * @param traderId
     * @param skuSupplyAuthId
     * @param attachment
     * @return
     * <AUTHOR>
     * @desc 供应商授权书列表页新增附件图片
     */
    @RequestMapping("/authPictureAdd")
    @ResponseBody
    public ResultInfo authPictureAdd(HttpServletRequest request, Integer traderId, Integer skuSupplyAuthId, Attachment attachment) {
        if (attachment == null || traderId == null || skuSupplyAuthId == null) {
            return ResultInfo.error("必要参数不得为空");
        }
        User user = getSessionUser(request);
        attachment.setRelatedId(skuSupplyAuthId);
        return ResultInfo.success(traderSupplierBizService.addTraderSupplierAuthAttachment(attachment, user));
    }

    /**
     * @param request
     * @param traderId
     * @param skuSupplyAuthId
     * @param attachmentId
     * @return
     * <AUTHOR>
     * @desc 供应商授权书列表页删除附件图片
     */
    @RequestMapping("/authPictureDel")
    @ResponseBody
    public ResultInfo authPictureDel(HttpServletRequest request, Integer traderId, Integer skuSupplyAuthId, Integer attachmentId) {
        if (attachmentId == null || traderId == null || skuSupplyAuthId == null) {
            return ResultInfo.error("必要参数不得为空");
        }
        User user = getSessionUser(request);
        traderSupplierBizService.delAttachmentByPrimaryKey(attachmentId, user);
        return ResultInfo.success();
    }

    /**
     * @param request
     * @param skuSupplyAuthId
     * @return
     * <AUTHOR>
     * @desc 根据供应商授权书id作废授权书
     */
    @RequestMapping("/authRemove")
    @ResponseBody
    public ResultInfo authRemove(HttpServletRequest request, Integer skuSupplyAuthId) {
        User user = getSessionUser(request);
        traderSupplierBizService.delSkuSupplyAuthById(skuSupplyAuthId, user);
        return ResultInfo.success();
    }

    /**
     * 新增关联商品弹窗
     *
     * @param request         request
     * @param skuSupplyAuthId 授权书id
     * @param relatedSkuQuery 搜索框输入参数
     * @param pageSize        页面大小
     * @param pageNo          当前页
     * @param isInit          是否是初次进入页面（只有搜索按钮才会查询sku列表）
     * @return mv
     */
    @RequestMapping("/addRelatedSku")
    public ModelAndView addRelatedSku(HttpServletRequest request,
                                      Integer skuSupplyAuthId,
                                      RelatedSkuQuery relatedSkuQuery,
                                      @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                      @RequestParam(defaultValue = "0") Integer isInit) {
        ModelAndView mv = new ModelAndView("trader/supplier/add_related_sku");
        User sessionUser = getSessionUser(request);
        mv.addObject("userId", sessionUser.getUserId());
        mv.addObject("relatedSkuQuery", relatedSkuQuery);
        if (isInit.equals(1)) {
            com.github.pagehelper.Page<Object> page = PageHelper.startPage(pageNo, pageSize);
            List<CoreSkuVo> coreSkuVoList = goodsApiService.selectAllSkuByKeywords(relatedSkuQuery);
            mv.addObject("skuList", coreSkuVoList);
            Page resultPage = Page.newBuilder2(page.getPageNum(), page.getPageSize(),
                    "/trader/tradersupplier/addRelatedSku.do?isInit=1&skuName=" + relatedSkuQuery.getSkuName() + "&skuNo=" + relatedSkuQuery.getSkuNo() + "&brandName=" +
                            relatedSkuQuery.getBrandName() + "&specModel=" + relatedSkuQuery.getSpecModel() + "&skuSupplyAuthId=" + skuSupplyAuthId + "&userId="
                            + sessionUser.getUserId(), ((Long) page.getTotal()).intValue());
            mv.addObject("page", resultPage);
        }
        mv.addObject("skuSupplyAuthId", skuSupplyAuthId);
        return mv;
    }

    /**
     * 保存新增关联商品信息
     *
     * @param skuInfoListStr 选中的商品信息
     * @return ResultInfo
     */
    @RequestMapping("/saveAddRelatedSku")
    @ResponseBody
    public ResultInfo saveRelatedSku(@RequestParam String skuInfoListStr) {
        List<SkuSupplyAuthDetail> paramSkuList = JSONObject.parseArray(skuInfoListStr, SkuSupplyAuthDetail.class);
        Date addTime = new Date();
        paramSkuList.forEach(skuSupplyAuthDetail -> skuSupplyAuthDetail.setAddTime(addTime));
        int result = traderSupplierBizService.batchInsertRelatedSku(paramSkuList);
        return ResultInfo.success(result);
    }

    /**
     * 删除关联商品页面
     *
     * @param request         request
     * @param skuSupplyAuthId 授权书id
     * @param pageSize        pageSize
     * @param pageNo          pageNo
     * @return ModelAndView
     */
    @RequestMapping("/deleteRelatedSku")
    @ResponseBody
    public ModelAndView addRelatedSku(HttpServletRequest request, Integer skuSupplyAuthId, @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNo) {
        ModelAndView mv = new ModelAndView("trader/supplier/delete_related_sku");
        User sessionUser = getSessionUser(request);
        mv.addObject("userId", sessionUser.getUserId());

        com.github.pagehelper.Page<Object> page = PageHelper.startPage(pageNo, pageSize);
        List<SkuSupplyAuthDetail> relatedSku = traderSupplierBizService.selectRelatedSkuByAuthId(skuSupplyAuthId);
        mv.addObject("skuList", relatedSku);
        Page resultPage = Page.newBuilder2(page.getPageNum(), page.getPageSize(), "/trader/tradersupplier/deleteRelatedSku.do?skuSupplyAuthId=" + skuSupplyAuthId, ((Long) page.getTotal()).intValue());
        mv.addObject("page", resultPage);
        mv.addObject("skuSupplyAuthId", skuSupplyAuthId);
        return mv;
    }

    /**
     * 保存删除关联商品
     *
     * @param skuSupplyAuthDetailIdList 选中的要删除的关联商品
     * @param updater                   当前操作人
     * @return ResultInfo
     */
    @RequestMapping("/saveDeleteRelatedSku")
    @ResponseBody
    public ResultInfo saveDeleteRelatedSku(@RequestParam List<Integer> skuSupplyAuthDetailIdList, Integer updater) {
        List<Integer> collect = skuSupplyAuthDetailIdList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        traderSupplierBizService.saveDeleteRelatedSku(collect, updater);
        return ResultInfo.success();
    }

    /**
     * 获取登录用户对象，此时创建一个新的对象防止逻辑中对该对象进行修改
     * @param request
     * @return
     */
    protected User getSessionUser(HttpServletRequest request){
        try{
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            User returnUser = new User();
            BeanUtils.copyProperties(returnUser,user);
            return returnUser;
        }catch (Exception e){
            return null;
        }
    }
}
