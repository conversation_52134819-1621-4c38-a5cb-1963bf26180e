package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchBuyOrderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchSaleorderContractDto;
import com.vedeng.erp.kingdee.batch.dto.BatchVerifiesInfoDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeBuyOrderContractMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleorderContractMapper;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

@Slf4j
@Service
public class BuyOrderContractFileProcessor extends BaseProcessor<BatchBuyOrderContractDto, KingDeeFileDataDto> {
    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;


    @Value("${oss_http}")
    private String ossHttp;

    @Override
    public KingDeeFileDataDto doProcess(BatchBuyOrderContractDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始推送采购合同附件,dto:{}", JSONUtil.toJsonStr(dto));
        if(ObjectUtil.isNull(dto.getDataId())){
            log.info("采购合同附件推送金蝶，采购合同不存在:{}",JSONUtil.toJsonStr(dto));
            return null;
        }
        //通过附件回写表判断该附件是否推送过
        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(KingDeeFormConstant.BUYORDER_CONTRACT)
                .erpId(dto.getAttachmentId().toString())
                .url(ossHttp+dto.getDomain()+dto.getUri())
                .build();
        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }
        return KingDeeFileDataDto.builder()
                .fileName("合同_"+dto.getName())
                .aliasFileName("合同_"+dto.getName())
                .billNo(dto.getBuyOrderNo())
                .formId(KingDeeFormConstant.BUYORDER_CONTRACT)
                .isLast(true)
                .fId(dto.getDataId().toString())
                .url(ossHttp+dto.getDomain()+dto.getUri())
                .erpId(dto.getAttachmentId().toString())
                .businessId(KingDeeFormConstant.BUYORDER_CONTRACT+dto.getAttachmentId().toString())
                .build();
    }
}
