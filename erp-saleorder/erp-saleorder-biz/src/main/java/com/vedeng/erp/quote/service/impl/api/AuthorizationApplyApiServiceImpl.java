package com.vedeng.erp.quote.service.impl.api;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.base.api.dto.resParam.SignCompanyInfo;
import com.vedeng.erp.quote.domain.AuthorizationApplyEntity;
import com.vedeng.erp.quote.manager.esign.AuthorizationElectronicSignHandle;
import com.vedeng.erp.quote.mapper.AuthorizationApplyMapper;
import com.vedeng.erp.quote.service.AuthorizationApplyApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.BaseCompanyInfoSimpleDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.ElectronicSignBusinessEnums;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 授权书
 * @date 2024/6/26 10:10
 */
@Service
@Slf4j
public class AuthorizationApplyApiServiceImpl implements AuthorizationApplyApiService {

    @Autowired
    AuthorizationElectronicSignHandle authorizationElectronicSignHandle;
    @Autowired
    OssUtilsService ossUtilsService;
    @Autowired
    AuthorizationApplyMapper authorizationApplyMapper;

    @Value("${VD_AUTH_URL:https://m.vedeng.com/authorization}")
    private String vdAuthUrl;

    @Value("${oss_http}")
    public String OSS_HTTP;
    @Value("${oss_url}")
    public String OSS_URL;


    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;



    @Override
    public void electronicSign(Integer authorizationApplyId) {
        BusinessInfo businessInfo = new BusinessInfo();
        businessInfo.setOperator("系统");

        List<SignCompanyInfo> signCompanyInfoList = new ArrayList<>();
        AuthorizationApplyEntity byAuthorizationApplyId = authorizationApplyMapper.findByAuthorizationApplyId(authorizationApplyId);
        if (byAuthorizationApplyId != null) {
            // String companyName = byAuthorizationApplyId.getSealType() == 1 ? "南京贝登医疗股份有限公司" : "南京医购优选供应链管理有限公司";
            List<BaseCompanyInfoDto> allCompanyList =baseCompanyInfoApiService.findAll();
            String companyName = allCompanyList.stream()
                    .filter(company -> byAuthorizationApplyId.getSealType().equals(company.getFrontEndSeq()))
                    .findFirst()
                    .map(BaseCompanyInfoDto::getCompanyName)
                    .orElse("");


            SignCompanyInfo signCompanyInfo = new SignCompanyInfo();
            signCompanyInfo.setSignCompanyName(companyName);

            // 根据授权书是否为标准模板来设置不同的签章位置关键字
            if (byAuthorizationApplyId.getStandardTemplate() == 0) {
                // 标准模板使用隐藏关键字
                signCompanyInfo.setHideCompanyName("VEDENG_SIGN_POSITION");
            } else {
                // 非标准模板使用公司名称
                signCompanyInfo.setHideCompanyName(companyName);
            }

            signCompanyInfo.setSignSealName("公章");
            signCompanyInfoList.add(signCompanyInfo);
        }

        ElectronicSignParam electronicSignParam = ElectronicSignParam
                .builder()
                .flowType(3)
                .authorizationApplyId(authorizationApplyId)
                .businessInfo(businessInfo)
                .electronicSignBusinessEnums(ElectronicSignBusinessEnums.CROSS_SHOUQUANSHU)
                .signCompanyInfoList(signCompanyInfoList)
                .onlySignLast("Y")
                .build();
        authorizationElectronicSignHandle.electronicSign(electronicSignParam);
    }

    @Override
    public String createTraceCode(String ossUrl, Integer authorizationApplyId) {
        AuthorizationApplyEntity apply = authorizationApplyMapper.selectByPrimaryKey(authorizationApplyId);

        String uploadedPath = "";
        try {
            // 生成二维码配置
            QrConfig config = new QrConfig();
            config.setWidth(65);
            config.setHeight(65);
            config.setMargin(0);
            BufferedImage qrCodeImage = QrCodeUtil.generate(vdAuthUrl + "/" + apply.getAuthorizationApplyNum() + "-" + apply.getAddTime() + ".html", config);

            // 创建临时二维码文件
            File qrCodeFile = File.createTempFile("qrcode", ".png");
            ImageIO.write(qrCodeImage, "png", qrCodeFile);

            // 从OSS读取PDF文件
            PdfReader reader = new PdfReader(new URL(ossUrl));
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            PdfStamper stamper = new PdfStamper(reader, outputStream);

            // 设置二维码和文字位置
            int numberOfPages = reader.getNumberOfPages();
            Image qrImage = Image.getInstance(qrCodeFile.getAbsolutePath());
            float x = 60;
            float y = 5;
            qrImage.setAbsolutePosition(x, y);


            ClassPathResource fontResource = new ClassPathResource("fonts/simhei.ttf");
            BaseFont bf = BaseFont.createFont(fontResource.getFile().getAbsolutePath(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

            for (int i = 1; i <= numberOfPages; i++) {
                PdfContentByte content = stamper.getOverContent(i);
                content.addImage(qrImage);

                content.beginText();
                content.setFontAndSize(bf, 5);
                content.showTextAligned(PdfContentByte.ALIGN_LEFT, apply.getAuthorizationApplyNum(), x + 18, y + 55, 0);
                content.showTextAligned(PdfContentByte.ALIGN_LEFT, "贝登授权溯源,扫一扫查询真伪", x, y + 5, 0);
                content.endText();
            }

            // 关闭资源
            stamper.close();
            reader.close();
            // 删除临时二维码文件
            qrCodeFile.delete();

            // 上传PDF到OSS
            uploadedPath = ossUtilsService.upload2OssForInputStream(
                    "pdf", "授权书(无章)" + apply.getAuthorizationApplyNum(), new ByteArrayInputStream(outputStream.toByteArray()));
            log.info("二维码已成功添加到 PDF 的左下角并保存文件路径：{}", uploadedPath);
        } catch (Exception e) {
            log.error("生成二维码并添加到PDF时发生错误", e);
        }
        return uploadedPath;
    }

}
