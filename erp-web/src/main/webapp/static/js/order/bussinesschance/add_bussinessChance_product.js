/**
 * @description 选择商品分类信息
 * @param categoryNameStr 商品分类信息
 * <AUTHOR>
 * @date 2020/6/15 13:56:12
 */
function choiceProduct(categoryNameStr) {
    window.parent.addCategoryName(categoryNameStr);
}

$(function () {
    $('.tr-name-txt').each(function () {
       $(this).html(highLightKeywords($(this).html(),$('#baseCategoryName').val(),'font'));
    })
})

/**
 * 搜索结果高亮展示
 * @param text
 * @param words
 * @param tag
 * @returns {*}
 * <AUTHOR>
 * @date 2020/6/16 9:51:12
 */
function highLightKeywords(text, words, tag) {
    tag = tag || 'span';
    var i, len = words.length, re;
    for (i = 0; i < len; i++) {
        re = new RegExp(words[i], 'g');
        if (re.test(text)) {
            text = text.replace(re, '<' + tag + ' style="color: red">$&</' + tag + '>');
        }
    }
return text;
}

/**
 * @description 手动添加商品
 * <AUTHOR>
 * @date 2020/6/16 9:59:18
 */
function editBussinessChanceProduct() {
    window.location = '/order/bussinesschance/editBussinessChanceProduct.do';
}