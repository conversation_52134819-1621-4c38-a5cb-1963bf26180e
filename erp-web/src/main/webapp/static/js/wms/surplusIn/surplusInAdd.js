$(function () {
    $("[name=submit]").click(function () {
            checkLogin();
            if ($("#applyerDepartmentId").val() == '' || $("#applyerDepartmentId").val() == 0) {
                layer.alert("申请部门不能为空");
                return false;
            }
            if ($("#applyerUserid").val() == '' || $("#applyerUserid").val() == null || $("#applyerUserid").val() == 0) {
                layer.alert("申请人不能为空");
                return false;
            }
            if ($("#applyIntime").val() == '') {
                layer.alert("申请入库日期不能为空");
                return false;
            }
            if ($("#remark").val().length > 500) {
                layer.alert("备注超过500字符!");
                return false;
            }
            var trs = $('.J-prod-item');
            if (trs.length == 0) {
                layer.alert("已选入库产品不能为空");
                return false;
            }
            var re = /(^[1-9]\d*$)/;
            var submitFlag = true;
            for (var i = 0; i < trs.length; i++) {
                var period = $(".J-item-num").eq(i).val().trim();
                if(submitFlag){
                    if (period.length == 0) {
                        submitFlag = false;
                        layer.alert("入库数量不可为空!");
                    } else if (period.length > 0 && !re.test(period)) {
                        submitFlag = false;
                        layer.alert("入库数量不正确!");
                    }
                }
            }

            if(!submitFlag){
                return false;
            }

            var hasAuthority = true;
            var validataPass = true;
            var message = "";

            $.ajax({
                url:page_url+'/wms/surplusIn/validateApplyerInfo.do',
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    debugger;

                    if(data.code == -1){
                        validataPass = false;
                        message = data.message;
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        hasAuthority = false;
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });

            if(!hasAuthority){
                return false;
            }

            if(!validataPass){
                layer.alert(message);
                return false;
            }
        }
    )});

// 删除
function deleteRow(row){
    $(row).parent().parent().remove();
}

function resetName(){
    $('.J-prod-item').each(function(i){
        $(this).find('input').each(function () {
            var name = $(this).attr('name');
            name = name.replace(/wmsInputOrderGoods\[\S*\]/g, 'wmsInputOrderGoods[' + i + ']');
            $(this).attr('name', name);
        })
    });
}
function checkSku(sku) {
    var flag = false;
    $('.J-prod-item').each(function () {
        if ($(this).find('.J-sku').val() == sku) {
            flag = true;
        }
    });

    return flag;
}
function setGoodData(skuNo,showName,brandName,model,unitName,stockNum) {
    if (checkSku(skuNo)) {
        layer.alert('已选过该产品');
        return;
    }
    layer.closeAll();
    var prodTmpl = template($('.J-prod-tmpl').html());
    var goods = {
        goodsName: showName,
        sku:skuNo,
        brandName: brandName,
        model:model,
        unitName:unitName,
    };
    $('.J-prod-list').append(prodTmpl(goods));
    resetName();
    /*var tbody = $(".J-prod-list");
    var tr = $("<tr class=\"J-prod-item\">\n" +
        "     <td>\n" +
        "     " +skuNo+ "\n" +
        "       <input type=\"hidden\" name=\"skuNo\" value=\""+skuNo+"\">\n" +
        "     </td>\n" +
        "     <td>"+showName+"</td>\n" +
        "     <td>"+brandName+"</td>\n" +
        "     <td>"+model+"</td>\n" +
        "     <td>"+unitName+"</td>\n" +
        "     <td>"+"<input type=\"text\" class=\"input-middle J-item-num\" name=\"inputNum\">"+"</td>\n" +
        "     <td><a href=\"#\" onclick=\"deleteRow(this)\">删除</a></td>\n" +
        "     </tr>");
    tbody.append(tr);*/

}

function selectOrgUser() {
    checkLogin();
    debugger;
    var orgId = $("select[name='applyerDepartmentId']").val();
    if (orgId > 0) {
        $.ajax({
            type: "POST",
            url: page_url + '/wms/scrapOut/getUserNameByOrgId.do',
            data: {'orgId': orgId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    var posit = data.listData;
                    var option = "";
                    $.each(posit, function (i, n) {
                        option += "<option value='" + posit[i]['userId'] + "'>" + posit[i]['username'] + "</option>";
                    });
                    $("select[name='applyerUserid'] option:gt(0)").remove();
                    $("select[name='applyerUserid']").append(option);
                } else {
                    layer.msg(data.message);
                }
            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    } else {
        $("select[name='applyerUserid'] option:gt(0)").remove();
    }
}