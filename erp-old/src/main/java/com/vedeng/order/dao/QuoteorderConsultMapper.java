package com.vedeng.order.dao;

import com.vedeng.order.model.QuoteorderConsult;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date created in 2020/7/7 15:26
 */
public interface QuoteorderConsultMapper {

    int insert(QuoteorderConsult record);

    /**
     * 根据报价单id和咨询类型获取记录存在的数量
     *
     * @param quoteOrderId
     * @param consultType
     * @return
     */
    List<QuoteorderConsult> listQuotedConsult(@Param("quoteOrderId") Integer quoteOrderId, @Param("consultType") Integer consultType);

    List<QuoteorderConsult> getAllQuotedConsultlistpage(Map<String, Object> map);


}
