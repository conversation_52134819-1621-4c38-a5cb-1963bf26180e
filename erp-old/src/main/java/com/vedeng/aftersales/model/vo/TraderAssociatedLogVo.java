package com.vedeng.aftersales.model.vo;

import lombok.Data;

import java.util.Date;

@Data
public class TraderAssociatedLogVo {

    /**
     * 操作类型，1:绑定日志，2:解除绑定日志
     */
    private Integer operationType;

    /**
     * 操作描述
     */
    private String operationDescription;

    private String reason;


    private String remark;

    private String operatorName;

    /**
     * 操作人所在组织名称
     */
    private String organizationNameOfOperator;

    private String operationTime;

}