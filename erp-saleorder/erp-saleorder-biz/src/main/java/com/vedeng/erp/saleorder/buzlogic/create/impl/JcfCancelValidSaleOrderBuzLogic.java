package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.saleorder.buzlogic.create.CancelValidSaleOrderBuzLogic;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderCommonService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @desc jcf订单调用撤销生效执行器
 */
@Service
public class JcfCancelValidSaleOrderBuzLogic extends CancelValidSaleOrderBuzLogic {
    Logger logger= LoggerFactory.getLogger(JcfCancelValidSaleOrderBuzLogic.class);

    public JcfCancelValidSaleOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("cancelValidSaleOrder");
        sequence.add("pushFormal");
        super.setSequence(sequence);
    }
    @Autowired
    private OrderCommonService orderCommonService;
    @Resource
    private SaleorderMapper saleorderMapper;
    /**
     * @desc 撤销生效，需重新推送前台，待确认状态
     * @param saleorderId
     * @return
     */
    public ResultInfo pushFormal(Integer saleorderId){
        try {
            //将订单状态还原为待客户确认
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleorderId);
            saleorder.setStatus(ErpConst.FOUR);//待客户确认
            saleorderMapper.updateByPrimaryKeySelective(saleorder);
            orderCommonService.updateSaleOrderDataUpdateTime(saleorderId,null, OrderDataUpdateConstant.SALE_ORDER_GENERATE);
            return new ResultInfo(0,"推送撤销生效消息至前台成功");
        }catch (Exception e){
            logger.error("集采订单撤销生效推送前台消息失败，订单id{},错误信息{},",saleorderId,e.getMessage());
            return new ResultInfo(-1,"推送撤销生效消息至前台失败");
        }
    }
    @Override
    public ResultInfo run(Saleorder saleorder) {
        new JcfCreateSaleOrderBuzLogic();
        ResultInfo result = new ResultInfo(0, "操作成功");
        for (String methodName : getSequence()) {
            switch (methodName) {
                case "cancelValidSaleOrder":
                    if (ErpConst.ZERO.equals(result.getCode())) {
                        result = super.cancelValidSaleOrder(saleorder);
                    }
                    break;
                case "pushFormal":
                    if (ErpConst.ZERO.equals(result.getCode())){
                        result = this.pushFormal(saleorder.getSaleorderId());
                        logger.info("撤销生效订单{},推送消息至前台返回{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
