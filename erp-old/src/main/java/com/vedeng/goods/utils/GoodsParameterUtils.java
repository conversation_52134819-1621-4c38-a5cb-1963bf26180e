package com.vedeng.goods.utils;

import com.google.common.base.Splitter;
import com.google.common.collect.Sets;
import com.vedeng.common.constant.ErpConst;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.regex.Pattern;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class GoodsParameterUtils {

    private static final Splitter PARAM_NAME_SPLITTER = Splitter.on(Pattern.compile("[,，]")).trimResults();
    private static final Splitter ENTRY_SPLITTER = Splitter.on(Pattern.compile("[;；]")).trimResults();
    /**
     * VDERP-15028 只按第一个:切割
     */
    private static final Splitter KEY_VALUE_SPLITTER = Splitter.on(Pattern.compile("[:：]")).limit(2).trimResults();


    public static List<String> splitToList(String input) {
        if (input == null || input.length() == 0) {
            return Collections.emptyList();
        }
        List<String> result = new LinkedList<>();
        for (String slice : PARAM_NAME_SPLITTER.split(input)) {
            result.add(slice);
        }
        return result;
    }


    public static String mergeParameterNames(String target, String[] sources) {
        Set<String> targetSet = new LinkedHashSet<>(splitToList(target));

        if (sources != null && sources.length > 0) {
            for (String source : sources) {
                if (source != null && source.length() > 0) {
                    targetSet.add(source);
                }
            }
        }

        StringJoiner joiner = new StringJoiner(ErpConst.Symbol.COMMA);
        for (String paramName : targetSet) {
            joiner.add(paramName);
        }

        return joiner.toString();
    }


    public static Map<String, String> tokenizeParameterValues(String... parameterValues) {
        if (parameterValues == null || parameterValues.length == 0) {
            return Collections.emptyMap();
        }

        Map<String, String> keyValuePairs = new LinkedHashMap<>();
        for (String parameterValue : parameterValues) {
            if (parameterValue == null || parameterValue.length() == 0) {
                continue;
            }
            spiltToMap(parameterValue).forEach(keyValuePairs::putIfAbsent);
        }

        return keyValuePairs;
    }

    public static String paramArrayToString(String[] a, String[] b) {
        StringBuilder temp = new StringBuilder();
        Set<String> checkExist = Sets.newHashSet();
        if (ArrayUtils.isNotEmpty(a) && ArrayUtils.isNotEmpty(b)) {
            for (int i = 0; i < a.length; i++) {
                if (!checkExist.contains(a[i] + ":" + b[i])) {
                    checkExist.add(a[i] + ":" + b[i]);
                } else {
                    continue;
                }
                if (StringUtils.isNotBlank(a[i]) && StringUtils.isNotBlank(b[i]) && b.length > i) {
                    temp.append(a[i] + ":" + b[i] + ";");
                }
            }
        }
        return temp.toString();
    }


    private static Map<String, String> spiltToMap(String input) {
        Map<String, String> result = new LinkedHashMap<>();

        for (String entry : ENTRY_SPLITTER.split(input)) {
            Iterator<String> iterator = KEY_VALUE_SPLITTER.split(entry).iterator();
            if (!iterator.hasNext()) {
                continue;

            }
            String key = iterator.next();
            if (!iterator.hasNext()) {
                continue;
            }

            String value = iterator.next();

            result.put(key, value);
        }

        return result;
    }
}
