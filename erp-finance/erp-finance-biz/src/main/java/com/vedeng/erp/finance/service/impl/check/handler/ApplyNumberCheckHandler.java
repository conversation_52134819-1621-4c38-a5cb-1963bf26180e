package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.dto.InvoiceNumApiDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceNumApiService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 申请数量校验
 */
@Service
@Slf4j
public class ApplyNumberCheckHandler extends AbstractCheckHandler {

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private InvoiceNumApiService invoiceNumApiService;
    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("申请数量校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        if (CheckChainEnum.isOpen(invoiceCheckRequestDto.getCheckChainEnum())) {
            Integer invoiceApplyId = invoiceCheckRequestDto.getInvoiceApplyId();
            InvoiceApplyDto invoiceApply = invoiceApplyService.getInvoiceApply(invoiceApplyId);
            if (ErpConstant.THREE.equals(invoiceApply.getApplyMethod())) {
                log.info("申请数量校验-销售,开票方式为客户在线申请开票,不校验申请数量");
                return;
            }
            log.info("申请数量校验-销售,不满足开票方式客户在线申请开票,invoiceApply:{}", invoiceApply);
        }
        if (CollUtil.isNotEmpty(invoiceCheckRequestDto.getDetailList())) {
            SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
            if (Objects.isNull(saleOrder)) {
                throw new ServiceException("申请数量校验-销售,订单不存在");
            }
            List<InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto> detailList = invoiceCheckRequestDto.getDetailList();
            List<InvoiceNumApiDto.SalesOrderInvoiceDto> goodsList = invoiceNumApiService.getInvoiceNum(invoiceCheckRequestDto.getRelatedId());
            if (CollUtil.isEmpty(goodsList)) {
                throw new ServiceException("申请数量校验-销售,获取T+N数据失败");
            }
            Map<Integer, InvoiceNumApiDto.SalesOrderInvoiceDto> tnMap = goodsList.stream().collect(Collectors.toMap(InvoiceNumApiDto.SalesOrderInvoiceDto::getSalesOrderGoodsId, x -> x, (k1, k2) -> k1));
            List<String> noPassSkuList = new ArrayList<>();
            for (InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto detail : detailList) {
                InvoiceNumApiDto.SalesOrderInvoiceDto salesOrderInvoiceDto = tnMap.get(detail.getDetailGoodsId());
                if (Objects.isNull(salesOrderInvoiceDto)) {
                    throw new ServiceException("申请数量校验-销售,未获取到发票明细对应的T+N数据,goodsId:" + detail.getDetailGoodsId());
                }
                if (detail.getNum().compareTo(salesOrderInvoiceDto.getArrivalNum()) > 0) {
                    noPassSkuList.add(salesOrderInvoiceDto.getSku());
                }
            }
            if (CollUtil.isNotEmpty(noPassSkuList)) {
                CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
                String promptText = StrUtil.format(checkHandlerEnum.getPromptText(), String.join(",", noPassSkuList));
                buildResult(invoiceCheckResultDto, promptText);
            }
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
    }
}
