package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.order.model.Buyorder;
import com.vedeng.system.service.OrgService;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.DailyManagementTodoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.vedeng.todolist.model.OrganizationSelect;
import com.vedeng.todolist.model.TodoList;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理者页面->日常管理
 */
@Service
public class DailyManageTodoAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild>{

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private UserMapper userMapper;

    @Value("${peer_list_filter_time}")
    private Long peerListFilterTime;

    @Resource
    private OrgService orgService;

    List<Organization> orgaList = new ArrayList<>();

    private List<Integer> userIdList;

    private Map<Integer,List<Integer>> userIdByOrgId = new HashedMap();

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild todoDtoBuild) {

        Integer rootDepartMentId = this.orgService.getOrgIdByOrgName("供应链管理部");

        if(rootDepartMentId == null){
            todoDtoBuild.get().setDailyManagementTodoDto(new DailyManagementTodoDto());
            return;
        }

        orgaList = orgService.getChildrenOrgByParentId(rootDepartMentId,1);

        Set<Integer> userIdSet = new HashSet<>();

        orgaList.forEach(organization -> {
            List<Integer> userIds = userMapper.getUserIdListByOrgId(organization.getOrgId());
            userIdSet.addAll(userIds);
            userIdByOrgId.put(organization.getOrgId(),userIds);
        });

        userIdList = new ArrayList<>(userIdSet);

        DailyManagementTodoDto dailyManagementTodoDto = new DailyManagementTodoDto();

        //设置商品代办的相关数据
        setSkuAndSpuTodoData(dailyManagementTodoDto);

        //设置商品推送相关数据
        setSkuPushData(dailyManagementTodoDto);

        //设置同行单录入待办项相关数据
        setReceiptRecordData(dailyManagementTodoDto);
        todoDtoBuild.get().setDailyManagementTodoDto(dailyManagementTodoDto);

    }

    //设置sku代办数据
    private void setSkuAndSpuTodoData(DailyManagementTodoDto dailyManagementTodoDto) {

        //获取当前代办涉及的所有组
        Set<Integer> selectOrgIdSet = new HashSet<>();

        List<TodoList> maintainTodoList = todoListMapper.getUnHandledRiskCheckSkuTodoListByUserList(TodoListBuzSceneEnum.MAINTAIN_DATA_SKU.getBuzSceneId(), Arrays.asList());

        maintainTodoList.forEach(todoList -> {
            selectOrgIdSet.add(todoList.getManagerOrgId());
            selectOrgIdSet.add(todoList.getAssistantOrgId());
        });

        List<OrganizationSelect> selectOrga = convert(selectOrgIdSet);
        dailyManagementTodoDto.setSkuAndSpuSelectList(selectOrga);
        dailyManagementTodoDto.setSkuAndSpuSubordinateList(this.userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));

        Integer defaultOrgaId = selectOrga.stream().filter(OrganizationSelect::isSelected).map(OrganizationSelect::getOrgId).findFirst().orElse(null);

        if(defaultOrgaId != null){
            maintainTodoList = maintainTodoList.stream().filter(todo -> skuTodoBelongToOrga(todo,defaultOrgaId)).collect(Collectors.toList());
            dailyManagementTodoDto.setSkuAndSpuSubordinateList(this.userIdByOrgId.get(defaultOrgaId).stream().map(String::valueOf).collect(Collectors.joining(",")));
        }

        dailyManagementTodoDto.setSkuAndSpuCountGroupByGrade(
                maintainTodoList.stream()
                        .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
        );
    }

    //设置商品推送相关数据
    private void setSkuPushData(DailyManagementTodoDto dailyManagementTodoDto) {


        List<Integer> sceneIdList = Arrays.asList(TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME.getBuzSceneId(),
                                                  TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(),
                                                  TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY.getBuzSceneId(),
                                                  TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId(),
                                                  TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO.getBuzSceneId());

        List<TodoList> pushTodoList = todoListMapper.getUnHandledSkuPushTodoList(sceneIdList,userIdList);

        //获取当前代办涉及的所有组
        Set<Integer> selectOrgIdSet = new HashSet<>();

        pushTodoList.forEach(todoList -> {
            selectOrgIdSet.add(todoList.getManagerOrgId());
            selectOrgIdSet.add(todoList.getAssistantOrgId());
        });

        List<OrganizationSelect> selectOrga = convert(selectOrgIdSet);
        dailyManagementTodoDto.setSkuPushSelectList(selectOrga);
        dailyManagementTodoDto.setSkuPushSubordinateList(this.userIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));

        Integer defaultOrgaId = selectOrga.stream().filter(OrganizationSelect::isSelected).map(OrganizationSelect::getOrgId).findFirst().orElse(null);

        if(defaultOrgaId != null){
            pushTodoList = pushTodoList.stream().filter(todo -> skuTodoBelongToOrga(todo,defaultOrgaId)).collect(Collectors.toList());
            dailyManagementTodoDto.setSkuPushSubordinateList(this.userIdByOrgId.get(defaultOrgaId).stream().map(String::valueOf).collect(Collectors.joining(",")));
        }

        dailyManagementTodoDto.setDeliveryTimeCountGroupByGrade(
            pushTodoList.stream()
                        .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_DELIVERY_TIME.getBuzSceneId()))
                        .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
        );


        dailyManagementTodoDto.setPriceCountGroupByGrade(
            pushTodoList.stream()
                    .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId()))
                    .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
        );

        if (dailyManagementTodoDto.getPriceCountGroupByGrade().size() > 0){
            dailyManagementTodoDto.setPriceSkuList(getUnHandledMaintainPriceTodoListGroupByGrade(defaultOrgaId == null ? userIdList : userIdByOrgId.get(defaultOrgaId)));
        }


        dailyManagementTodoDto.setAftersalePolicyCountGroupByGrade(
                pushTodoList.stream()
                        .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_AFTER_SALE_POLICY.getBuzSceneId()))
                        .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
        );

        dailyManagementTodoDto.setSupplyAftersalePolicyCountGroupByGrade(
                pushTodoList.stream()
                        .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY.getBuzSceneId()))
                        .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
        );

        dailyManagementTodoDto.setOperationInfoCountGroupByGrade(
                pushTodoList.stream()
                        .filter(todo -> todo.getBuzType().equals(TodoListBuzSceneEnum.MAINTAIN_DATA_OPERATION_INFO.getBuzSceneId()))
                        .collect(Collectors.groupingBy(TodoList::getGoodsLevelNo, Collectors.counting()))
        );

    }


    /**
     * 设置同行单录入待办相关数据
     * @param dailyManagementTodoDto
     */
    private void setReceiptRecordData(DailyManagementTodoDto dailyManagementTodoDto) {
        //获取当前代办涉及的所有组
        Integer loginUserOrgId = ((User) ThreadLocalContext.get("userInfo")).getOrgId();
        Integer buyorderListAllCount = todoListMapper.getReceiptRecordCountGroupByCategoryCount(userIdList, peerListFilterTime, null, loginUserOrgId);
        Integer buyorderListIsUrgentCount = todoListMapper.getReceiptRecordCountGroupByCategoryCount(userIdList, peerListFilterTime, 1, loginUserOrgId);

        Map<Long, Long> returnMap = new HashMap<>(4);
        //总待办
        returnMap.put(1L, (long) buyorderListAllCount);
        //紧急待办
        returnMap.put(2L, (long) buyorderListIsUrgentCount);
        //日常待办
        returnMap.put(3L, (long) (buyorderListAllCount - buyorderListIsUrgentCount));

        Set<Integer> selectOrgIdSet = todoListMapper.byReceiptRecordCountGroupByCategoryGetOrgIds(userIdList, peerListFilterTime);
        List<OrganizationSelect> selectOrg = convert(selectOrgIdSet);
        dailyManagementTodoDto.setReceiptRecordSelectList(selectOrg);
        dailyManagementTodoDto.setReceiptRecordCountGroupByCategory(returnMap);
    }


    /**
     * 获取待维护价格的待办事项，按照商品等级统计sku
     * @param userList 用户集合
     * @return 结果
     */
    private Map<Long, String> getUnHandledMaintainPriceTodoListGroupByGrade(List<Integer> userList){
        Map<Integer, List<TodoList>> maintainPriceOfSku =
                todoListMapper.getUnHandledMaintainTodoListSkuByUserList(TodoListBuzSceneEnum.MAINTAIN_DATA_PRICE.getBuzSceneId(), userList)
                        .stream()
                        .collect(Collectors.groupingBy(TodoList::getBuzType));
        Map<Long, String> skuListGroupByGoodsLevel = new HashMap<>();
        for (Integer key : maintainPriceOfSku.keySet()){
            String skuListStr = maintainPriceOfSku.get(key).stream().map(TodoList::getBuzExtra).collect(Collectors.joining(","));
            skuListGroupByGoodsLevel.put(Long.valueOf(key),skuListStr);
        }
        return skuListGroupByGoodsLevel;
    }

    /**
     *
     * @param selectOrgIdSet
     * @return
     */
    private List<OrganizationSelect> convert(Set<Integer> selectOrgIdSet) {
        if(CollectionUtils.isEmpty(selectOrgIdSet)){
            return Collections.emptyList();
        }

        List<OrganizationSelect> organizationSelectList = new ArrayList<>();

        Integer loginUserOrgaId = ((User)ThreadLocalContext.get("userInfo")).getOrgId();
        selectOrgIdSet.forEach(selectOrgId -> {
            Organization org = orgaList.stream().filter(o->o.getOrgId().equals(selectOrgId)).findFirst().orElse(null);
            if(org == null){
                return;
            }

            OrganizationSelect organizationSelect = new OrganizationSelect();
            organizationSelect.setOrgId(org.getOrgId());
            organizationSelect.setOrgName(org.getOrgName());
            organizationSelect.setSelected(org.getOrgId().equals(loginUserOrgaId));

            organizationSelectList.add(organizationSelect);
        });

        return organizationSelectList;
    }

    /**
     * 判断一个sku的代办 是否属于某个组织机构
     * @param skuTodo
     * @return
     */
    private boolean skuTodoBelongToOrga(TodoList skuTodo,Integer orgaId){
        return orgaId.equals(skuTodo.getAssistantOrgId()) || orgaId.equals(skuTodo.getManagerOrgId());
    }
}
