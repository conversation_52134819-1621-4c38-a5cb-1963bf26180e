<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.FlowOrderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.FlowOrderEntity">
    <!--@mbg.generated-->
    <!--@Table T_FLOW_ORDER-->
    <id column="FLOW_ORDER_ID" property="flowOrderId" />
    <result column="FLOW_ORDER_NO" property="flowOrderNo" />
    <result column="BASE_ORDER_ID" property="baseOrderId" />
    <result column="BASE_ORDER_NO" property="baseOrderNo" />
    <result column="BASE_BUSINESS_TYPE" property="baseBusinessType" />
    <result column="AUDIT_STATUS" property="auditStatus" />
    <result column="AUDIT_USER_ID" property="auditUserId" />
    <result column="AUDIT_USERNAME" property="auditUsername" />
    <result column="AUDIT_TIME" property="auditTime" />
    <result column="IS_DELETE" property="isDelete" />
    <result column="CONTRACT_STATUS" property="contractStatus" />
    <result column="PUSH_DIRECTION" property="pushDirection" />
    <result column="SOURCE_ERP" property="sourceErp" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FLOW_ORDER_ID, FLOW_ORDER_NO, BASE_ORDER_ID, BASE_ORDER_NO, BASE_BUSINESS_TYPE, AUDIT_STATUS,
    AUDIT_USER_ID, AUDIT_USERNAME, AUDIT_TIME, IS_DELETE, CONTRACT_STATUS, PUSH_DIRECTION, SOURCE_ERP, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER
    where FLOW_ORDER_ID = #{flowOrderId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_FLOW_ORDER
    where FLOW_ORDER_ID = #{flowOrderId}
  </delete>
  <insert id="insert" keyColumn="FLOW_ORDER_ID" keyProperty="flowOrderId" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_ORDER (FLOW_ORDER_NO, BASE_ORDER_ID, BASE_ORDER_NO, BASE_BUSINESS_TYPE, AUDIT_STATUS,
      AUDIT_USER_ID, AUDIT_USERNAME, AUDIT_TIME, IS_DELETE, CONTRACT_STATUS, PUSH_DIRECTION, SOURCE_ERP, CREATOR, UPDATER,
      CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME)
    values (#{flowOrderNo}, #{baseOrderId}, #{baseOrderNo}, #{baseBusinessType}, #{auditStatus},
      #{auditUserId}, #{auditUsername}, #{auditTime}, #{isDelete}, #{contractStatus}, #{pushDirection}, #{sourceErp}, #{creator}, #{updater},
      #{creatorName}, #{updaterName}, #{addTime}, #{modTime})
  </insert>
  <insert id="insertSelective" keyColumn="FLOW_ORDER_ID" keyProperty="flowOrderId" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowOrderNo != null and flowOrderNo != ''">
        FLOW_ORDER_NO,
      </if>
      <if test="baseOrderId != null">
        BASE_ORDER_ID,
      </if>
      <if test="baseOrderNo != null and baseOrderNo != ''">
        BASE_ORDER_NO,
      </if>
      <if test="baseBusinessType != null">
        BASE_BUSINESS_TYPE,
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS,
      </if>
      <if test="auditUserId != null">
        AUDIT_USER_ID,
      </if>
      <if test="auditUsername != null and auditUsername != ''">
        AUDIT_USERNAME,
      </if>
      <if test="auditTime != null">
        AUDIT_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="contractStatus != null">
        CONTRACT_STATUS,
      </if>
      <if test="pushDirection != null">
        PUSH_DIRECTION,
      </if>
      <if test="sourceErp != null and sourceErp != ''">
        SOURCE_ERP,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowOrderNo != null and flowOrderNo != ''">
        #{flowOrderNo},
      </if>
      <if test="baseOrderId != null">
        #{baseOrderId},
      </if>
      <if test="baseOrderNo != null and baseOrderNo != ''">
        #{baseOrderNo},
      </if>
      <if test="baseBusinessType != null">
        #{baseBusinessType},
      </if>
      <if test="auditStatus != null">
        #{auditStatus},
      </if>
      <if test="auditUserId != null">
        #{auditUserId},
      </if>
      <if test="auditUsername != null and auditUsername != ''">
        #{auditUsername},
      </if>
      <if test="auditTime != null">
        #{auditTime},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="contractStatus != null">
        #{contractStatus},
      </if>
      <if test="pushDirection != null">
        #{pushDirection},
      </if>
      <if test="sourceErp != null and sourceErp != ''">
        #{sourceErp},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
      <if test="updater != null">
        #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName},
      </if>
      <if test="addTime != null">
        #{addTime},
      </if>
      <if test="modTime != null">
        #{modTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderEntity">
    <!--@mbg.generated-->
    update T_FLOW_ORDER
    <set>
      <if test="flowOrderNo != null and flowOrderNo != ''">
        FLOW_ORDER_NO = #{flowOrderNo},
      </if>
      <if test="baseOrderId != null">
        BASE_ORDER_ID = #{baseOrderId},
      </if>
      <if test="baseOrderNo != null and baseOrderNo != ''">
        BASE_ORDER_NO = #{baseOrderNo},
      </if>
      <if test="baseBusinessType != null">
        BASE_BUSINESS_TYPE = #{baseBusinessType},
      </if>
      <if test="auditStatus != null">
        AUDIT_STATUS = #{auditStatus},
      </if>
      <if test="auditUserId != null">
        AUDIT_USER_ID = #{auditUserId},
      </if>
      <if test="auditUsername != null and auditUsername != ''">
        AUDIT_USERNAME = #{auditUsername},
      </if>
      <if test="auditTime != null">
        AUDIT_TIME = #{auditTime},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete},
      </if>
      <if test="contractStatus != null">
        CONTRACT_STATUS = #{contractStatus},
      </if>
      <if test="pushDirection != null">
        PUSH_DIRECTION = #{pushDirection},
      </if>
      <if test="sourceErp != null and sourceErp != ''">
        SOURCE_ERP = #{sourceErp},
      </if>
      <if test="creator != null">
        CREATOR = #{creator},
      </if>
      <if test="updater != null">
        UPDATER = #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime},
      </if>
    </set>
    where FLOW_ORDER_ID = #{flowOrderId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.FlowOrderEntity">
    <!--@mbg.generated-->
    update T_FLOW_ORDER
    set FLOW_ORDER_NO = #{flowOrderNo},
      BASE_ORDER_ID = #{baseOrderId},
      BASE_ORDER_NO = #{baseOrderNo},
      BASE_BUSINESS_TYPE = #{baseBusinessType},
      AUDIT_STATUS = #{auditStatus},
      AUDIT_USER_ID = #{auditUserId},
      AUDIT_USERNAME = #{auditUsername},
      AUDIT_TIME = #{auditTime},
      IS_DELETE = #{isDelete},
      CONTRACT_STATUS = #{contractStatus},
      PUSH_DIRECTION = #{pushDirection},
      SOURCE_ERP = #{sourceErp},
      CREATOR = #{creator},
      UPDATER = #{updater},
      CREATOR_NAME = #{creatorName},
      UPDATER_NAME = #{updaterName},
      ADD_TIME = #{addTime},
      MOD_TIME = #{modTime}
    where FLOW_ORDER_ID = #{flowOrderId}
  </update>

<!--auto generated by MybatisCodeHelper on 2025-01-09-->
  <select id="findbyBaseOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER
    where BASE_ORDER_NO=#{baseOrderNo}
    and  IS_DELETE=0
  </select>

  <select id="findByFlowOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER
    where FLOW_ORDER_ID=#{flowOrderId}
    and  IS_DELETE=0
  </select>

  <select id="findByFlowOrderNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER
    where FLOW_ORDER_NO=#{flowOrderNo}
    and  IS_DELETE=0
  </select>

  <select id="findByBaseOrderIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_FLOW_ORDER
    where BASE_ORDER_ID=#{baseOrderId}
    and BASE_BUSINESS_TYPE=#{baseBusinessType}
    and IS_DELETE=0
  </select>

  <select id="findBuyGoodsNum" resultType="com.vedeng.erp.system.domain.dto.FlowSkuNumDto">
    select g.SKU,g.ARRIVAL_NUM as num
    from T_FLOW_ORDER o
    left join T_BUYORDER b on o.BASE_ORDER_ID = b.BUYORDER_ID
    left join T_BUYORDER_GOODS g on g.BUYORDER_ID = o.BASE_ORDER_ID
    where o.BASE_BUSINESS_TYPE = 1 and o.IS_DELETE = 0 and g.IS_DELETE =0 and o.FLOW_ORDER_ID =  #{flowOrderId}
  </select>

  <select id="findSaleGoodsNum" resultType="com.vedeng.erp.system.domain.dto.FlowSkuNumDto">
    select g.SKU,g.DELIVERY_NUM as num
    from T_FLOW_ORDER o
    left join T_SALEORDER b on o.BASE_ORDER_ID = b.SALEORDER_ID
    left join T_SALEORDER_GOODS g on g.SALEORDER_ID = o.BASE_ORDER_ID
    where o.BASE_BUSINESS_TYPE = 2 and b.IS_DELETE = 0 and g.IS_DELETE = 0 and o.FLOW_ORDER_ID =  #{flowOrderId}
  </select>

      <select id="getContractReturnStatusByBuyorderId" resultType="java.lang.Integer">
        SELECT COALESCE(IS_CONTRACT_RETURN_STATUS, 0)
        FROM T_BUYORDER_DATA
        WHERE BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
    </select>
</mapper>
