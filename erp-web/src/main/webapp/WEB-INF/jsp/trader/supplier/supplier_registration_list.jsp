<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="交易商品注册证列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%@ include file="supplier_tag.jsp"%>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="<%=basePath%>static/css/supplier/supplier_registration.css?rnd=${resourceVersionKey}" />

<div class="main-container">
    <input id="traderId" type="hidden" value="${traderId}">
    <input id="canEdit" type="hidden" value="${canEdit}">
    <input id="canCheck" type="hidden" value="${canCheck}">
    <input id="checker" type="hidden" value="${supplierRegistrationChecker}（周晓茹）">
    <div class="fixdiv">
        <div style="color: red">修改此页面内容不会触发供应商审核，或变更供应商审核状态，但会触发本页面的审核</div>
        <div class="superdiv">
            <table
                    class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="sorts">注册证号</th>
                    <th>效期截止日期</th>
                    <th>订货号</th>
                    <th>产品名称（注册证）</th>
                    <th>型号、规格（注册证）</th>
                    <th style="width: 490px">注册证附件</th>
                    <th>审核状态</th>
                    <th>操作</th>
                    <th>审核人</th>
                    <th>操作时间</th>
                </tr>
                </thead>

                <tbody class="employeestate">
                <c:if test="${not empty list}">
                    <c:forEach items="${list}" var="item"
                               varStatus="ostatus">
                        <c:forEach items="${item.skuList}" var="s" varStatus="istatus">
                        <tr class="p">
                            <c:if test="${istatus.count==1}">
                                <input type="hidden" class="register-number-id" value="${item.registrationNumberId}">
                            <td rowspan="${item.skuList.size()}">${item.registrationNumber}</td>
                            <td rowspan="${item.skuList.size()}">
                                <c:if test="${968 ne item.manageCategoryLevel}">
                                <date:date value="${item.effectiveDate} " format="yyyy/MM/dd"/>
                                </c:if>
                            </td>
                            </c:if>
                            <td>${s.skuNo}</td>
                            <td>${s.skuName}</td>
                            <td>${s.model}</td>
                            <c:if test="${istatus.count==1}">
                                <td rowspan="${item.skuList.size()}">
                                    <div>图上传支持上传不超过5M的JPG、PNG、JEPG、PDF格式。</div>
                                    <div class="form-col">
                                        <div class="J-upload"></div>
                                        <input type="hidden" class="J-upload-data" value='${item.attachmentMapStr}'>
                                            <div class="form-fields-tip">-最多上传5张。</div>
                                        <div class="feedback-block" wrapfor="upload0"></div>
                                        <div class="feedback-block J-upload-error">
                                            <label class="error" for="" style="display: none;"></label>
                                        </div>
                                    </div>
                                </td>
                                <td class="status-td" rowspan="${item.skuList.size()}">
                                    <c:if test="${null eq item.checkStatus || 0 eq item.checkStatus}">待审核</c:if>
                                    <c:if test="${1 eq item.checkStatus}">审核通过</c:if>
                                </td>
                                <td class="check_r" rowspan="${item.skuList.size()}">
                                    <c:if test="${null ne item.supplierRegistrationNumberId && 0 eq item.checkStatus && 1 eq canCheck}">
                                        <a href="javascript:void(0)" onclick="check(this)" class="btn btn-small btn-blue">审核通过</a>
                                    </c:if>
                                </td>
                                <td class="check-u" rowspan="${item.skuList.size()}">
                                    <c:if test="${1 eq item.checkStatus}">
                                   ${supplierRegistrationChecker}（周晓茹）
                                    </c:if>
                                </td>
                                <td class="time-td" rowspan="${item.skuList.size()}">
                                    <c:if test="${1 eq item.checkStatus}">
                                      <date:date value="${item.checkTime}" />
                                    </c:if>
                                </td>
                            </c:if>
                        </tr>
                        </c:forEach>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
            <c:if test="${empty list }">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
    </div>
    <tags:page page="${page}" />
</div>
<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/supplier/registration_list.js?rnd=${resourceVersionKey}"></script>