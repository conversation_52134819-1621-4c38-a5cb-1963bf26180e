package com.wms.task;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.StringUtil;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsSkuReg;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 下传注册证
 */
@Component
@JobHandler(value="putRegToWmsTask")
public class PutRegToWmsTask extends AbstractJobHandler {

    private static final Logger LOGGER = LoggerFactory.getLogger(PutRegToWmsTask.class);

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private RegistrationNumberMapper registrationNumberMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        //商品多注册证
        try {

            List<WmsSkuReg> wmsSkuRegList = null;

            if(StringUtil.isNotEmpty(param)){
                wmsSkuRegList=registrationNumberMapper.getWmsSkuRegListInSkuNoStr(param);
            }else{

                wmsSkuRegList=registrationNumberMapper.getWmsSkuRegList();
            }
            WmsInterface putSkuInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_REG);
            List<WmsSkuReg> wmsSkuRegListPage=new ArrayList<>();
            for (int i = 0; i < wmsSkuRegList.size(); i++) {
                wmsSkuRegListPage.add(wmsSkuRegList.get(i));
                //每次传递1000条
                if(i%1000==0){
                    XxlJobLogger.log("ERP下传商品多注册证至WMS的请求:" + JSON.toJSONString(wmsSkuRegListPage));
                    WmsResponse response = putSkuInterface.request(wmsSkuRegListPage.toArray());
                    wmsSkuRegListPage.clear();
                    LOGGER.info("ERP下传商品多注册证至WMS的响应:" + JSON.toJSONString(response));
                    XxlJobLogger.log("ERP下传商品多注册证至WMS的响应:"+JSON.toJSONString(response));
                }
            }
            XxlJobLogger.log("ERP下传商品多注册证至WMS的请求:" + JSON.toJSONString(wmsSkuRegListPage));
            WmsResponse response = putSkuInterface.request(wmsSkuRegListPage.toArray());
            LOGGER.info("ERP下传商品多注册证至WMS的响应:" + JSON.toJSONString(response));
            XxlJobLogger.log("ERP下传商品多注册证至WMS的响应:"+JSON.toJSONString(response));
            return SUCCESS;

        }catch (Exception e){
            LOGGER.error("Erp下传商品多注册证至WMS失败",e);
            XxlJobLogger.log("Erp下传商品多注册证至WMS失败:{}",e);
        }
        return FAIL;
    }
}
