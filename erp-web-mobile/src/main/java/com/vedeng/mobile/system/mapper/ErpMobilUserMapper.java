package com.vedeng.mobile.system.mapper;

import com.vedeng.uac.api.dto.UserLoginInfo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository("erpMobilUserMapper")
public interface ErpMobilUserMapper {
    /**
     * 获取业务手机号
     * @param userId
     * @return
     */
    String getMobileByUserId(@Param("userId") Integer userId);

    /**
     * 获取用户登录信息
     * @param userName
     * @return
     */
    UserLoginInfo userLoginInfo(@Param("userName") String userName);
}
