package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/14 13:58
 **/
@RequestMapping("/traderCustomer")
@Controller
public class TraderCustomerBizController {



    @RequestMapping(value = "/dealerEditView")
    @NoNeedAccessAuthorization
    public ModelAndView dealerEditView(Integer traderCustomerId) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_dealer_edit");
        mv.addObject("traderCustomerId",traderCustomerId);
        return mv;
    }


}
