<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.trader.dao.TraderCertificateHistoryMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.TraderCertificateHistory">
    <id column="TRADER_CERTIFICATE_HISTORY_ID" jdbcType="INTEGER" property="traderCertificateHistoryId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="SYS_OPTION_DEFINITION_ID" jdbcType="INTEGER" property="sysOptionDefinitionId" />
    <result column="BEGINTIME" jdbcType="BIGINT" property="begintime" />
    <result column="ENDTIME" jdbcType="BIGINT" property="endtime" />
    <result column="SN" jdbcType="VARCHAR" property="sn" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="DOMAIN" jdbcType="VARCHAR" property="domain" />
    <result column="URI" jdbcType="VARCHAR" property="uri" />
    <result column="EXTRA" jdbcType="VARCHAR" property="extra" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="RECORD_NO" jdbcType="VARCHAR" property="recordNo" />
  </resultMap>
  <sql id="Base_Column_List">
    TRADER_CERTIFICATE_HISTORY_ID, TRADER_ID, SYS_OPTION_DEFINITION_ID, BEGINTIME, ENDTIME, 
    SN, `NAME`, `DOMAIN`, URI, EXTRA, ADD_TIME, CREATOR, MOD_TIME, UPDATER, RECORD_NO
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_CERTIFICATE_HISTORY
    where TRADER_CERTIFICATE_HISTORY_ID = #{traderCertificateHistoryId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_TRADER_CERTIFICATE_HISTORY
    where TRADER_CERTIFICATE_HISTORY_ID = #{traderCertificateHistoryId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_CERTIFICATE_HISTORY_ID" keyProperty="traderCertificateHistoryId" parameterType="com.vedeng.trader.model.TraderCertificateHistory" useGeneratedKeys="true">
    insert into T_TRADER_CERTIFICATE_HISTORY (TRADER_ID, SYS_OPTION_DEFINITION_ID, 
      BEGINTIME, ENDTIME, SN, 
      `NAME`, `DOMAIN`, URI, 
      EXTRA, ADD_TIME, CREATOR, 
      MOD_TIME, UPDATER, RECORD_NO
      )
    values (#{traderId,jdbcType=INTEGER}, #{sysOptionDefinitionId,jdbcType=INTEGER}, 
      #{begintime,jdbcType=BIGINT}, #{endtime,jdbcType=BIGINT}, #{sn,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{domain,jdbcType=VARCHAR}, #{uri,jdbcType=VARCHAR}, 
      #{extra,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{recordNo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_CERTIFICATE_HISTORY_ID" keyProperty="traderCertificateHistoryId" parameterType="com.vedeng.trader.model.TraderCertificateHistory" useGeneratedKeys="true">
    insert into T_TRADER_CERTIFICATE_HISTORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="sysOptionDefinitionId != null">
        SYS_OPTION_DEFINITION_ID,
      </if>
      <if test="begintime != null">
        BEGINTIME,
      </if>
      <if test="endtime != null">
        ENDTIME,
      </if>
      <if test="sn != null">
        SN,
      </if>
      <if test="name != null">
        `NAME`,
      </if>
      <if test="domain != null">
        `DOMAIN`,
      </if>
      <if test="uri != null">
        URI,
      </if>
      <if test="extra != null">
        EXTRA,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="recordNo != null">
        RECORD_NO,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="sysOptionDefinitionId != null">
        #{sysOptionDefinitionId,jdbcType=INTEGER},
      </if>
      <if test="begintime != null">
        #{begintime,jdbcType=BIGINT},
      </if>
      <if test="endtime != null">
        #{endtime,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="recordNo != null">
        #{recordNo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.trader.model.TraderCertificateHistory">
    update T_TRADER_CERTIFICATE_HISTORY
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="sysOptionDefinitionId != null">
        SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER},
      </if>
      <if test="begintime != null">
        BEGINTIME = #{begintime,jdbcType=BIGINT},
      </if>
      <if test="endtime != null">
        ENDTIME = #{endtime,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        SN = #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        `NAME` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="domain != null">
        `DOMAIN` = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="extra != null">
        EXTRA = #{extra,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="recordNo != null">
        RECORD_NO = #{recordNo,jdbcType=VARCHAR},
      </if>
    </set>
    where TRADER_CERTIFICATE_HISTORY_ID = #{traderCertificateHistoryId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.trader.model.TraderCertificateHistory">
    update T_TRADER_CERTIFICATE_HISTORY
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER},
      BEGINTIME = #{begintime,jdbcType=BIGINT},
      ENDTIME = #{endtime,jdbcType=BIGINT},
      SN = #{sn,jdbcType=VARCHAR},
      `NAME` = #{name,jdbcType=VARCHAR},
      `DOMAIN` = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      EXTRA = #{extra,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      RECORD_NO = #{recordNo,jdbcType=VARCHAR}
    where TRADER_CERTIFICATE_HISTORY_ID = #{traderCertificateHistoryId,jdbcType=INTEGER}
  </update>

  <select id="getTraderCertificateHistoryList" parameterType="com.vedeng.trader.model.TraderCertificateHistory" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_TRADER_CERTIFICATE_HISTORY
    where TRADER_ID = #{traderId,jdbcType=INTEGER} and SYS_OPTION_DEFINITION_ID = #{sysOptionDefinitionId,jdbcType=INTEGER}
  </select>

  <select id="getTraderCertificateHistoryListPage" parameterType="java.util.Map" resultType="com.vedeng.trader.model.TraderCertificateHistory">
    select
    <include refid="Base_Column_List" />
    FROM T_TRADER_CERTIFICATE_HISTORY
    where TRADER_ID = #{traderCertificate.traderId,jdbcType=INTEGER} and SYS_OPTION_DEFINITION_ID = #{traderCertificate.sysOptionDefinitionId,jdbcType=INTEGER}
    order by ADD_TIME DESC
  </select>
</mapper>