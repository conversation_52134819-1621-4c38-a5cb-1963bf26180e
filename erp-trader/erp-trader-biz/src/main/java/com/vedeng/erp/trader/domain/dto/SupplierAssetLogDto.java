package com.vedeng.erp.trader.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商资产变动日志
 * @date 2023/11/16 20:00
 */
@Getter
@Setter
public class SupplierAssetLogDto extends BaseDto {

    /**
     * 主键
     */
    private Integer supplierAssetLogId;


    /**
     * 供应商id
     */
    private Integer traderSupplierId;

    /**
     * 供应商资产id
     */
    private Integer supplierAssetId;

    /**
     * 资产改变类型：0->增加；1->减少 2->不变（占用发生时）
     */
    private Integer changeType;

    /**
     * 改变资产数量
     */
    private BigDecimal changeCount;

    /**
     * 变化前总量
     */
    private BigDecimal changeBeforeTotal = BigDecimal.ZERO;
    /**
     * 变化后总量
     */
    private BigDecimal changeAfterTotal = BigDecimal.ZERO;
    /**
     * 变化总量
     */
    private BigDecimal changeCountTotal = BigDecimal.ZERO;


    /**
     * 变化前可用总量
     */
    private BigDecimal changeBeforeApply = BigDecimal.ZERO;
    /**
     * 变化后可用总量
     */
    private BigDecimal changeAfterApply = BigDecimal.ZERO;
    /**
     * 变化可用总量
     */
    private BigDecimal changeCountApply = BigDecimal.ZERO;


    /**
     * 变化前占用总量
     */
    private BigDecimal changeBeforeOccupy = BigDecimal.ZERO;
    /**
     * 变化后占用总量
     */
    private BigDecimal changeAfterOccupy = BigDecimal.ZERO;
    /**
     * 变化占用总量
     */
    private BigDecimal changeCountOccupy = BigDecimal.ZERO;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 来源：
     * saleOrder.销售单
     * buyOrder.采购单
     * buyOrderExpense.采购费用单
     * buyOrderAfterSale.采购售后单
     * buyOrderExpenseAfterSale.采购费用售后单
     * buyOrderRebateChargeApply.采购返利结算收款申请',
     */
    private String sourceType;

    /**
     * 资产类型
     * 1:返利
     * 2:余额
     * 3:信用
     */
    private Integer assetType;

    /**
     * 操作人员
     */
    private String operateUser;

    /**
     * 操作备注
     */
    private String operateNote;

}
