<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM-线索</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/common/selectProd.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/businessLeads.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <input type="hidden" id="isHiddenLayout" value="0">
    <!-- 线索id -->
    <input type="hidden" id="businessLeads-id" value="${param.id}" />
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main">

                <div class="businessLeads-detail-container" v-if="!pageLoading">
                    <div class="leadsDetail-header-wrap" :style="{'top': fixedTop+'px'}">
                        <div class="header-main">
                            <div class="header-content">
                                <div class="header-left">
                                    <div class="title">线索详情</div>
                                    <div class="status" :class="'status'+detail.followStatus">{{ STATUS[detail.followStatus] }}</div>
                                </div>
                                <div class="header-right">
                                    <!-- <ui-button type="primary" v-if="!hidnStatus" @click="toBusinessChance">转商机</ui-button> -->
                                    <ui-button @click="handlerEdit" v-if="!hidnStatus">编辑</ui-button>
                                    <ui-button @click="handlerBelonger" v-if="GLOBAL.auth('C0107') && !hidnStatus">分配</ui-button>
                                    <ui-button v-if="detail.followStatus != 3" @click="addVisitPlan">添加拜访</ui-button>
                                    <ui-button @click="showCloseLeadsDialog" type="danger" v-if="!hidnStatus">关闭</ui-button>
                                    <ui-card-switch name="businessleads_detail" v-model="cardLineNum"></ui-card-switch>
                                </div>
                            </div>
                            <div class="header-aside-wrap">
                                <!-- 大屏幕 -->
                                <div class="header-md-aside">
                                    <ui-title-tip :title="item.name" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23" v-for="item in asideList" :key="item.name">
                                        <div 
                                            class="h-a-item" 
                                            :class="[item.icon, {'active': item.id == asideIndex}]"
                                            @click="asideIndex = item.id"
                                        >
                                            <div class="h-a-item-num" v-if="item.id == 3 && taskNum">{{ taskNum }}</div>
                                        </div>
                                    </ui-title-tip>
                                </div>
                                <div class="header-xs-aside">
                                    <ui-title-tip :title="item.name" :position="layout_hidden_value ? 'bottom' : 'top'" :y="-23" v-for="item in asideList" :key="item.name">
                                        <div 
                                            :class="['h-a-item', item.icon]"
                                            @click="openDialog(item.id)"
                                        >
                                            <div class="h-a-item-num" v-if="item.id == 3 && taskNum">{{ taskNum }}</div>
                                        </div>
                                    </ui-title-tip>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="leadsDetail-page-wrap" :class="{'switch-line-2': cardLineNum == 2}">
                        <div class="main">
                            <div class="top-warn-tip no-top" v-if="detail.followStatus==3">
                                <i class="vd-ui_icon icon-caution2"></i>
                                <div class="tip-cnt">
                                    关闭原因：{{ detail.closeReasonTypeName }}<template v-if="detail.closeReason">（{{ detail.closeReason }}）</template>
                                </div>
                            </div>
                            <div class="detail-top-card">
                                <div class="detail-top-item">
                                    <div class="item-label">线索编号</div>
                                    <div class="item-txt"><i class="tag-icon-hebing" title="线索合并" v-if="LeadMergeList && LeadMergeList.length"></i>{{ detail.leadsNo || '-' }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">来源</div>
                                    <div class="item-txt">{{ {391: '总机', 392: '销售', 394: '自有商城', 6012: '拜访'}[detail.clueType] }}</div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">归属销售</div>
                                    <div class="item-txt">
                                        <template v-if="detail.belonger">
                                            <div class="user-avatar">
                                                <img :src="detail.belongPic || GLOBAL.defaultAvatar" alt="">
                                            </div>
                                            <div class="user-txt">{{ detail.belonger }}</div>
                                        </template>
                                        <template v-else>-</template>
                                    </div>
                                </div>
                                <div class="detail-top-item">
                                    <div class="item-label">创建时间</div>
                                    <div class="item-txt">{{ detail.addTime || '-' }}</div>
                                </div>
                            </div>
                            <div class="card card-line">
                                <div class="card-title">客户和终端信息</div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">客户名称：</div>
                                        <div class="content">
                                            <ui-trader-name :info="{
                                                traderName: detail.traderName,
                                                traderNameLink: detail.traderNameLink,
                                                traderNameInnerLink: detail.traderNameInnerLink,
                                                tycFlag: detail.tycFlag,
                                                baidu: true
                                            }"></ui-trader-name>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">联系人：</div>
                                        <div class="content">{{detail.contact || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">手机：</div>
                                        <div class="content">
                                            <span v-if="detail.phone" :class="{'highlight': layout_hidden_value && detail.phone}" class="call-span" @click="callNumber_(detail.phone)">
                                                <i class="vd-ui_icon icon-call2" ></i>
                                                <span>{{detail.phone}}</span>
                                            </span>
                                            <span v-else>-</span>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">固话：</div>
                                        <div class="content">
                                            <span v-if="detail.telephone" :class="{'highlight': layout_hidden_value && detail.telephone}" class="call-span" @click="callNumber_(detail.telephone)">
                                                <i class="vd-ui_icon icon-call2"></i>
                                                <span>{{detail.telephone}}</span>
                                            </span>
                                            <span v-else>-</span>
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.otherContactInfo">
                                        <div class="label">其他联系方式：</div>
                                        <div class="content">
                                            <div class="ui-col-10">
                                                <p v-for="p in detail.otherContactInfo.split('##')" :key="p" class="show-font">{{ p }}</p>
                                            </div>
                                            <template v-else>-</template>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">终端名称：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.terminalTraderName || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">终端性质：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.terminalTraderNatureStr || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">终端区域：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.terminalTraderRegionStr || '-' }}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card card-line">
                                <div class="card-title">线索详情</div>
                                <div class="card-title-ai">
                                    <ai-category-detail :data="defaultAiData" v-if="defaultAiData.list && defaultAiData.list.length"></ai-category-detail>
                                </div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">业务类型：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.businessTypeStr || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">产品信息：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.goodsInfo || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">预计成单金额：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.amount? detail.amount + '元' : '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">预计成单日期：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.orderTime || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">标签：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ tagsShow || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">客情关系：</div>
                                        <div class="content">
                                            <div class="ui-col-10" v-if="detail.customerRelationshipStr && detail.customerRelationshipStr.length">{{ detail.customerRelationshipStr.join(' / ') }}</div>
                                            <div class="ui-col-10" v-else>-</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">采购方式：</div>
                                        <div class="content">{{ detail.purchasingTypeStr || '-' }}</div>
                                    </div>
                                    <div class="info-item" v-if="detail.purchasingType == 406">
                                        <div class="label">招投标阶段：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.biddingPhaseStr || '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item" v-if="detail.purchasingType == 406">
                                        <div class="label">招标参数：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.biddingParameter == 1 ? '可调整' : detail.biddingParameter == 2 ? '不可调整' : '-' }}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">备注：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{ detail.remark || '-' }}</div>
                                        </div>
                                    </div>
                                    <!-- <div class="info-item">
                                        <div class="label">业务类型：</div>
                                        <div class="content">{{detail.clueTypeName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">企微提醒：</div>
                                        <div class="content">{{detail.sendVx == 'Y'? '提醒': detail.sendVx == 'N'? '不提醒': '-' }}</div>
                                    </div>
                                    
                                    <div class="info-item">
                                        <div class="label">线索创建人：</div>
                                        <div class="content">
                                            <div class="user-show" v-if="detail.creatorName">
                                                <img class="avatar" :src="detail.creatorPic || ''" onerror="this.src='/static/image/img-error.png'"/>
                                                <span class="name">{{ detail.creatorName }}</span>
                                            </div>
                                            <div v-else>-</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">创建时间：</div>
                                        <div class="content">{{detail.addTime || '-'}}</div>
                                    </div> -->
                                </div>
                            </div>
        
                            <div class="card card-line" v-if="detail.clueType == 391 || detail.clueType == 394">
                                <div class="card-title">渠道信息</div>
                                <div class="info-wrap">
                                    <div class="info-item">
                                        <div class="label">询价行为：</div>
                                        <div class="content">{{detail.inquiryName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">渠道类型：</div>
                                        <div class="content">{{detail.sourceName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">渠道名称：</div>
                                        <div class="content">{{detail.communicationName || '-'}}</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">地区：</div>
                                        <div class="content">{{ area || '-' }}</div>
                                    </div>
                                    <!-- <div class="info-item" >
                                        <div class="label">三级分类：</div>
                                        <div class="content">
                                            <div class="ui-col-10" v-if="detail.content">
                                                <p v-for="p in detail.content.split('&&')" :key="p" class="show-font">{{ p }}</p>
                                            </div>
                                            <template v-else>-</template>
                                        </div>
                                    </div> -->
                                    <div class="info-item">
                                        <div class="label">咨询入口：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{detail.entrancesName || '-'}}</div>
                                        </div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label">功能：</div>
                                        <div class="content">
                                            <div class="ui-col-10">{{detail.functionsName || '-'}}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- <div class="remark-wrap">
                                <div class="info-item all-line">
                                    <div class="label">线索备注：</div>
                                    <div class="content">
                                        <div class="ui-col-10">{{ detail.remark || '-' }}</div>
                                    </div>
                                </div>
                            </div> -->

                            <div class="card" v-if="LeadMergeList && LeadMergeList.length">
                                <div class="card-title">合并线索</div>
                                <div class="">
                                    <ui-table 
                                        :oneline="true"
                                        :width-border="true" 
                                        :auto-scroll="false" 
                                        :headers="leadsDetailHeaders" 
                                        :list="LeadMergeList"
                                    ></ui-table>
                                </div>
                            </div>
                        </div>
                        <div class="right-aside hidden-xs" :style="'top:' + (layout_hidden_value ? '0' : '50px')">
                            <div class="right-aside-inner">
                                <!-- 跟进记录面板 -->
                                <follow-record-list
                                    ref="followRecordList"
                                    v-if="asideIndex == 1"
                                    :communicate-type="4109"
                                    :related-id="businessLeadsId"
                                    :trader-id="detail.traderId"
                                    :trader-name="detail.traderName"
                                    :belonger-id="detail.belongerId"
                                    :belonger="detail.belonger"
                                    :belonger-pic="detail.belongPic"

                                    :trader-name-link="detail.traderNameLink"
                                    :trader-name-inner-link="detail.traderNameInnerLink"
                                    :tyc-flag="detail.tycFlag"

                                    :contact="detail.contact"
                                    :trader-contact-id="detail.traderContactId"
                                    :phone="detail.phone"
                                    :tele-phone="detail.telephone"
                                    @addrecord="handlerRecordAdd"
                                ></follow-record-list>
                                <!-- 任务记录 -->
                                <renwu-list
                                    ref="renwuList"
                                    v-if="asideIndex == 3"
                                    biz-type="2"
                                    :related-id="businessLeadsId"
                                    :disabled="detail.followStatus==3"
                                    @refresh="getTaskNum"
                                ></renwu-list>
                                <!-- 操作记录面板 -->
                                <operation-log 
                                    ref="operationLog" 
                                    v-if="asideIndex == 2"
                                    biz-type-enum="01"
                                    :related-id="businessLeadsId"
                                ></operation-log>
                                <!-- 协作人面板 -->
                                <partner-list
                                    ref="partnerList"
                                    v-if="asideIndex == 4"
                                    :belonger-id="detail.belongerId"
                                    :related-id="businessLeadsId"
                                    :business-type="5"
                                    :business-no="detail.leadsNo"
                                ></partner-list>
                            </div>
                        </div>
                    </div>
                </div>

                <ui-dialog
                    :visible.sync="isShowBelongerDialog"
                    title="分配线索"
                    width="720px"
                >
                    <div class="form-wrap label-width-2" v-if="isShowBelongerDialog">
                        <ui-form-item label="新归属人" :must="true">
                            <div class="ui-col-4">
                                <ui-select 
                                    :avatar="true" placeholder="请输入并选择归属人" clearable valid="belongerForm_belonger" 
                                    v-model="newBelonger" :remote="true" :remote-info="allUserRemoteInfo"
                                ></ui-select>
                            </div>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer">
                            <ui-button @click="setBelonger" type="primary">保存</ui-button>
                            <ui-button @click="isShowBelongerDialog=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <ui-dialog
                    :visible.sync="isShowCloseDialog"
                    title="关闭线索"
                    width="720px"
                >
                    <div class="form-wrap label-width-2">
                        <ui-form-item label="关闭原因" :must="true">
                            <div class="ui-col-4">
                                <ui-select
                                    width="323px"
                                    placeholder="请选择关闭原因"
                                    :data="CloseReasonTypes"
                                    v-model="closeReasonType"
                                    clearable
                                    @change="handlerCloseReasonTypeSelect"
                                    valid="CloseLeads_closeReasonType"
                                ></ui-select>
                            </div>
                        </ui-form-item>

                        <ui-form-item label="说明">
                            <div class="ui-col-4">
                                <ui-input
                                    type="textarea"
                                    width="440px"
                                    height="75px"
                                    show-word-limit
                                    maxlength="200"
                                    v-model="closeRemark"
                                ></ui-input>
                            </div>
                        </ui-form-item>
                    </div>
                    <template slot="footer">
                        <div class="dlg-form-footer">
                            <ui-button @click="closeTheLeads" type="primary">提交</ui-button>
                            <ui-button @click="isShowCloseDialog=false" class="close">取消</ui-button>
                        </div>
                    </template>
                </ui-dialog>

                <follow-record-list-dialog ref="followRecordListDialog" :communicate-type="4109" :refresh-detail-list="refreshFollowList" @addrecord="handlerRecordAdd"></follow-record-list-dialog>
                <operation-log-dialog ref="operationLogDialog"></operation-log-dialog>
                <partner-list-dialog ref="partnerListDialog" :refresh-panel="refreshPartnerList"></partner-list-dialog>

                <!-- 天眼查详情 -->
                <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
                 <!-- 任务 dialog -->
                 <renwu-dialog ref="renwuDialog" :refresh-panel="refreshRenwuPanel"></renwu-dialog>
            </div>
        </div>
    </div>

    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 跟进记录相关 -->
    <script src="/static/js/common/components/business/records.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 任务记录 -->
    <script src="/static/js/common/components/business/renwu.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 操作记录 -->
    <script src="/static/js/common/components/business/operationLog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/components/business/operationLog-dialog.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 协作人 -->
    <script src="/static/js/common/components/business/partner.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 选产品弹层 -->
    <script src="/static/js/common/components/selectProd.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <!-- 线索详情 -->
    <script src="/static/js/pages/businessleadsDetail.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
