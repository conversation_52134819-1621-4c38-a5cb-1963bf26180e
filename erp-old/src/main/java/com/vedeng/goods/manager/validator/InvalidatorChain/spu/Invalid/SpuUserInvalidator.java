package com.vedeng.goods.manager.validator.InvalidatorChain.spu.Invalid;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSpuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSpu;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SpuUserInvalidatorError;

@Service
public class SpuUserInvalidator extends GoodsInvalidatorChain {

    @Resource
    CoreSpuMapper coreSpuMapper;

    @Override
    public void doInvalid() {
        User user = getUser();
        String spuNo = getSpuNo();
        CoreSpu spuInfoBySpuNo = coreSpuMapper.getSpuInfoBySpuNo(spuNo);
        if(!(user.getUserId().equals(spuInfoBySpuNo.getAssignmentAssistantId()) || user.getUserId().equals(spuInfoBySpuNo.getAssignmentManagerId()))){
            throw new InvalidatorChainException(SpuUserInvalidatorError.getCode(),SpuUserInvalidatorError.getMessage());
        }
    }
}
