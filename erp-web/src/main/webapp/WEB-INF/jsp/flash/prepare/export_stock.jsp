<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="导出备货列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/flash/prepare/export_stock.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='/webjars/ezadmin/plugins/layui/layui.js'></script>
 <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<div class="content">
    <div class="sku-opr">
        <form id="export_stock">
            <ul class="lastResult">
                <li>
                    <div class="infor_name label-c">
                        <span>*</span><label class="info">请选择月份：</label>
                    </div>
                    <div class="f_left label-c">
<%--                        <input class="Wdate input-c label-c f_left input-smaller96 m0" type="text"--%>
<%--                               placeholder="请选择日期"--%>
<%--                               onClick="WdatePicker({dateFmt:'yyyy-MM'})"--%>
<%--                               name="exportMonth" id="exportMonth"--%>
<%--                               value="">--%>
                        <input class="input-c label-c f_left input-smaller96 m0" type="text"
                               placeholder="请选择日期"
                               name="exportMonth" id="exportMonth"
                               value="" lay-key ="5">
                    </div>
                </li>
                <li style="margin: 0;">
                    <div class="infor_name label-c">
                    </div>
                    <div class="f_left month_error" style="display:none;">
                        <span style="color:red;">请选择日期!</span>
                    </div>
                </li>
            </ul>
            <div class="submit-c tcenter">
                <button id="close-layer" type="button" class="bg-cancle-blue bt-bg-style bt-largest">取消</button>
                <button class="bt-largest bt-bg-style bg-light-blue" type="button" onclick="exportPrepareStock();">导出列表</button>
            </div>
        </form>
    </div>
</div>
<style>
    .sku-opr {
        padding: 20px;
        font-size: 15px;
    }

    .sku-opr ul li {
        margin-top: 20px;
    }

    .label-c {
        height: 40px;
        line-height: 40px;
    }

    .input-c {
        width: 200px;
        border-color: rgb(226, 226, 226);
        padding: 5px;
    }

    .submit-c {
        width: 90%;
        position: fixed;
        bottom: 20px;
    }

    .bg-cancle-blue {
        background: white;
        color: #1b76ed;
        border: 1px solid #1b76ed;
    }

    .layui-laydate {
        left: 340px !important;
        top: 35px !important;
    }

    i{
        background: none;
    }
</style>
<script>
    //年月选择器
    layui.use(['laydate'], function() {
        let render = laydate.render({
            elem: '#exportMonth'
            , type: 'month'
            , value: new Date()
            , show: false
            , trigger: 'click'
            , max: 0
        });
    });
</script>
<%@ include file="../../common/footer.jsp"%>
