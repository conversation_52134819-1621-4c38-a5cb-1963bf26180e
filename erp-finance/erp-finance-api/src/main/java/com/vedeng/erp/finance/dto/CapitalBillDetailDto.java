package com.vedeng.erp.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @description 资金流水详情
 * <AUTHOR>
 * @date 2022/8/27 13:20
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CapitalBillDetailDto {
    private Integer capitalBillDetailId;

    private Integer capitalBillId;

    /**
    * 业务类型 字典库
    */
    private Integer bussinessType;

    /**
    * 订单类型 1销售订单 2采购订单 3售后订单 4采购费用单 5采购费用单售后
    */
    private Integer orderType;

    /**
    * 订单单号
    */
    private String orderNo;

    /**
    * 关联表ID（销售订单、采购订单、售后订单等ID）
    */
    private Integer relatedId;

    /**
    * 安调维修工程师主键
    */
    private Integer afterSalesInstallstionId;

    private BigDecimal amount;

    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 所属类型 1::经销商（包含终端）2:供应商
    */
    private Integer traderType;

    /**
    * 用户ID(当前资金流水关联用户ID)
    */
    private Integer userId;

    /**
    * 当前资金流水关联用户部门ID
    */
    private Integer orgId;

    /**
    * 当前资金流水关联用户部门
    */
    private String orgName;

    /**
     * 交易方式
     */
    private Integer traderMode;
}