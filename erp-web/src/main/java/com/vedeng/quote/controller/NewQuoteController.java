package com.vedeng.quote.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.common.constants.SaleOrderConstants;
import com.vedeng.erp.quote.service.NewQuoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.quote.controller
 * @Date 2022/4/8 16:06
 */
@Slf4j
@Controller
@RequestMapping("newQuote")
public class NewQuoteController {

    @Autowired
    NewQuoteService newQuoteService;


    @Resource
    private AuthService authService;


    /**
     * 报价单-更新终端信息
     *
     * @param orderId
     * @return
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/updateTerminalInfo")
    @ResponseBody
    public ResultInfo<?> updateTerminalInfo(HttpServletRequest request, Integer orderId, Integer orderType){

        User currUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        //如果是销售订单页面进行权限校验
        if(SaleOrderConstants.SALEORDER_TYPE.equals(orderType)){
            List<User> userListByorderId = authService.getUserListByorderId(orderId, AuthService.SALEORDER_TYPE);
            if (authService.existOrNot(currUser, userListByorderId)){
                return  ResultInfo.error("您没有操作权限，申请开通权限请联系研发部Aadi");
            }
        }
        try {
            newQuoteService.updateTerminalInfo(orderId,orderType);
        } catch (Exception e) {
            log.info("更新终端信息失败，订单ID{},订单类型{}",orderId,orderType);
            return  ResultInfo.error();
        }
        return ResultInfo.success();
    }

}
