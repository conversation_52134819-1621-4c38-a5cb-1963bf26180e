package com.vedeng.activiti.taskassign;

import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.impl.persistence.entity.TaskEntity;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/19
 */
public class AutoTaskExecutionListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) throws Exception {
        TaskEntity task = (TaskEntity) execution.getEngineServices().getTaskService().createTaskQuery()
                .executionId(execution.getId())
                .singleResult();
        execution.getEngineServices().getTaskService().complete(task.getId());
    }
}
