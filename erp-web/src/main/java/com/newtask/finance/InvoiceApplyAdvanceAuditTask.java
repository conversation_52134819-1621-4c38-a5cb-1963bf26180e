package com.newtask.finance;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.finance.service.InvoiceApplyAuditService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/15 15:49
 **/
@JobHandler("InvoiceApplyAdvanceAuditTask")
@Component
@Slf4j
public class InvoiceApplyAdvanceAuditTask extends AbstractJobHandler {


    @Autowired
    private InvoiceApplyAuditService invoiceApplyAuditService;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        log.info("InvoiceApplyAdvanceAuditTask 定时任务开始，param:{}", JSON.toJSONString(param));

        boolean lock = RedissonLockUtils.tryLock(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK,0,2, TimeUnit.HOURS);

        if (!lock) {
            log.info("定时任务加锁失败，触发提前开票审核,存在处理中的提前开票审核业务");
            return ReturnT.SUCCESS;
        }

        log.info("提前开票审核加锁成功, key = [{}]", ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);

        try {
            invoiceApplyAuditService.doAdvancePageAudit(false);
        } catch (Exception e) {
            log.error("提前开票审核异常：", e);
        } finally {
            RedissonLockUtils.unlock(ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);
            log.info("提前开票审核解锁成功, key = [{}]", ErpConstant.REVIEW_OF_ADVANCE_INVOICING_TASK);
        }

        log.info("InvoiceApplyAdvanceAuditTask 定时任务结束");

        return ReturnT.SUCCESS;
    }
}
