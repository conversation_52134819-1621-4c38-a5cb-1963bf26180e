package com.vedeng.trader.dao;

import com.vedeng.trader.model.TraderCertificateHistory;

import java.util.List;
import java.util.Map;

public interface TraderCertificateHistoryMapper {
    int deleteByPrimaryKey(Integer traderCertificateHistoryId);

    int insert(TraderCertificateHistory record);

    int insertSelective(TraderCertificateHistory record);

    TraderCertificateHistory selectByPrimaryKey(Integer traderCertificateHistoryId);

    int updateByPrimaryKeySelective(TraderCertificateHistory record);

    int updateByPrimaryKey(TraderCertificateHistory record);

    /**
     * 获取历史资质信息
     * @param traderCertificate
     * @return
     */
    List<TraderCertificateHistory> getTraderCertificateHistoryList(TraderCertificateHistory traderCertificate);


    /**
     * 获取历史资质信息列表
     * @param map
     * @return
     */
    List<TraderCertificateHistory> getTraderCertificateHistoryListPage(Map<String, Object> map);
}