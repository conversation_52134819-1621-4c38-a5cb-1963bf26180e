package com.vedeng.crm.task.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:  任务人
 * @date 2024/7/30 18:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TaskUserDto {

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 头像
     */
    private String aliasHeadPicture;
}
