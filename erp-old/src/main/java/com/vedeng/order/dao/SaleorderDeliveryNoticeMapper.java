package com.vedeng.order.dao;


import com.vedeng.order.model.SaleorderDeliveryNotice;
import com.vedeng.order.model.SaleorderDeliveryNoticeExample;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SaleorderDeliveryNoticeMapper {
    long countByExample(SaleorderDeliveryNoticeExample example);

    int deleteByExample(SaleorderDeliveryNoticeExample example);

    int deleteByPrimaryKey(Integer deliveryNoticeId);

    int insert(SaleorderDeliveryNotice record);

    int insertSelective(SaleorderDeliveryNotice record);

    List<SaleorderDeliveryNotice> selectByExample(SaleorderDeliveryNoticeExample example);

    SaleorderDeliveryNotice selectByPrimaryKey(Integer deliveryNoticeId);

    int updateByExampleSelective(@Param("record") SaleorderDeliveryNotice record, @Param("example") SaleorderDeliveryNoticeExample example);

    int updateByExample(@Param("record") SaleorderDeliveryNotice record, @Param("example") SaleorderDeliveryNoticeExample example);

    int updateByPrimaryKeySelective(SaleorderDeliveryNotice record);

    int updateByPrimaryKey(SaleorderDeliveryNotice record);

    /**
     * 获取订单下的最新发货通知单的单号
     * @Param: [saleorderId]
     * @Return: java.lang.String
     * @Author: Rivan
     * @Date: 2020/7/21 16:50
     */
    String getMaxDeliveryNoticeNoBySaleorderId(Integer saleorderId);

    /**
     * 获取非已关闭的历史发货通知中的sku本次发货数量
     * @Param: [saleorderId, saleorderGoodsId]
     * @Return: int
     * @Author: Rivan
     * @Date: 2020/7/23 15:28
     */
    int getDeliveryNoticeGoodNum(@Param("saleorderId")Integer saleorderId, @Param("saleorderGoodsId")Integer saleorderGoodsId,@Param("deliveryNoticeId")Integer deliveryNoticeId);
}