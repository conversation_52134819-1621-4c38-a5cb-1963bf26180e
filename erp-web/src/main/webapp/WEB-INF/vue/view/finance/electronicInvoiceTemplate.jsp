<%--
  Created by IntelliJ IDEA.
  User: vedeng
  Date: 2024/1/16
  Time: 13:28
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <meta charset="utf-8">
    <title>发票模板</title>
</head>
<style>
    ul,
    ul li {
        list-style: none;
        margin: 0;
        padding: 0;
    }

    label {
        color: #9c5223;
    }

    .invoicMain {
        width: 920px;
        margin: 0 auto;
        font-size: 14px;
        color: #000;
    }

    .invoiceHeader {
        height: 126px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .headerLeft li:nth-child(1) {
        text-indent: 1em;
    }

    .headerLeft li img {
        width: 105px;
        height: 105px;

    }

    .headerMiddle h1 {
        font-size: 32px;
        color: #9c5223;
        font-family: 'kaiti';
        margin: 5px 12px;
    }

    .line {
        height: 2px;
        border-top: #9c5223 1px solid;
        border-bottom: #9c5223 1px solid;
    }

    .headerRight li {
        line-height: 24px;
    }

    .invoiceBody {
        border: 1px solid #9c5223;
    }
    #capture {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: #9c5223;
        color: white;
        border: none;
        padding: 10px 20px;
        font-size: 14px;
        cursor: pointer;
    }
    .userInfo {
        width: 100%;
        display: flex;
        align-items: center;
        height: 96px;
        border-bottom: 1px solid #9c5223;
    }

    .userInfo ul {
        width: 50%;
        margin: 0 5px;
        padding: 0;

    }

    .userInfo ul li {
        line-height: 24px;
    }

    .buy {
        width: 20px;
        border-right: 1px solid #9c5223;
        padding: 0px 10px;
        text-align: center;
        height: 100%;
        display: flex;
        align-items: center;
        color: #9c5223;
    }

    .password {
        width: 20px;
        padding: 0 10px;
        border-right: 1px solid #9c5223;
        border-left: 1px solid #9c5223;
        text-align: center;
        height: 100%;
        display: flex;
        align-items: center;
        color: #9c5223;
    }

    .pwdInfo {
        flex: 1;
        padding-left: 5px;
    }

    .goodsInfo {
        height: 210px;
        margin: 0;
        padding: 0;

    }

    .goodsInfo li {
        display: flex;
        color: #9c5223;
        text-align: center;
    }

    .name {
        width: 260px;
        border-right: 1px solid #9c5223;
    }

    .spec {
        width: 140px;
        border-right: 1px solid #9c5223;
    }

    .qty {
        width: 108px;
        border-right: 1px solid #9c5223;
    }

    .unit,
    .taxRate {
        width: 65px;
        border-right: 1px solid #9c5223;
    }

    .qty,
    .price {
        width: 160px;
        border-right: 1px solid #9c5223;
    }

    .money {
        flex: 1;
        border-right: 1px solid #9c5223;
    }

    .taxAmount {
        flex: 1;
    }

    .GoodsTable {
        height: 210px;
        width: 100%;
        border-collapse: collapse;
    }

    .GoodsTable td {
        border-right: 1px solid #9c5223;
        color: #9c5223;
    }

    .GoodsTable tr:nth-child(1),
    .GoodsTable tr:nth-last-child(2) {
        height: 24px;
    }

    .GoodsTable tr:nth-last-child(1) {
        height: 34px;
    }

    .GoodsTable tr:nth-child(1) td {
        text-align: center;
    }

    .GoodsTotal {
        border-top: 1px solid #9c5223;
        border-bottom: 1px solid #9c5223;
    }

    .total td:nth-child(1) {
        text-align: center;
    }

    .invoicetFooter {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: space-between;
    }
    .goodsInfo {
        margin: 0;
        padding: 0;
    }

    .GoodsTable {
        width: 100%;
        border-collapse: collapse;
        text-align: center; /* 这里添加text-align属性使表格内容居中 */
    }

    .invoicetFooter li {
        width: 25%;
    }
    .watermark {
        position: fixed;
        top: 35%;
        left: 40%;
        transform: translate(-50%, -50%);
        opacity: 0.1;
        font-size: 60px;
        color: #9c5223;
        font-family: 'kaiti';
        z-index: 999;
        pointer-events: none; /* 确保水印不会阻止用户与页面上的其他元素交互 */
    }
</style>
<body>
<div class="watermark">贝登医疗</div>
<div class="invoicMain">
    <div class="invoiceHeader">
        <ul class="headerLeft">
        </ul>
        <div class="headerMiddle">
            <h1>${title}</h1>
            <div class="line"></div>
        </div>
        <ul class="headerRight">
            <li>
                <label> 发票号码： </label><span>00000000000000000000</span>
            </li>
            <li>
                <label> 开票日期： </label><span>${date}</span>
            </li>
        </ul>
    </div>
    <div class="invoiceBody">
        <div class="userInfo">
            <div class="buy">购买方信息</div>
            <ul>
                <li>
                    <label>名称：</label><span>${traderName}</span>
                </li>
                <br>
                <li>
                    <label>统一社会信用代码/纳税人识别号：</label><span>${taxNum}</span>
                </li>
            </ul>
            <div class="password">销售方信息</div>
            <ul>
                <li>
                    <label>名称：</label><span>南京贝登医疗股份有限公司</span>
                </li>
                <br>
                <li>
                    <label>统一社会信用代码/纳税人识别号：</label><span>91320100589439066H</span>
                </li>
            </ul>
        </div>
        <div>
            <table class="GoodsTable" cellpadding="0" cellspacing="0">
                <tr>
                    <td style="width: 30%;border: none;">项目名称</td>
                    <td style="width: 16%;border: none;">规格型号</td>
                    <td style="width: 7%;border: none;">单位</td>
                    <td style="width: 5%;border: none;">数量</td>
                    <td style="width: 10%;border: none;">单价</td>
                    <td style="width: 10%;border: none;">金额</td>
                    <td style="width: 10%;border: none;">税率/征收率</td>
                    <td style="width: 10%; border-right: none;;border: none;">税额</td>
                </tr>

                <c:forEach var="goods" items="${goodsList}">
                    <tr style="height: 48px;">
                        <td style="color: black;border: none;text-align: left;padding-left: 8px;" >*${goods.taxCategoryName}*${goods.goodsName}</td>
                        <td style="color: black;border: none;">${goods.spec}</td>
                        <td style="color: black;border: none;">${goods.unitName}</td>
                        <td style="color: black;border: none;">${goods.applyNum}</td>
                        <td style="color: black;border: none;">${goods.price}</td>
                        <td style="color: black;border: none;">${goods.lastPrice}</td>
                        <td style="color: black;border: none;">${goods.taxRate}</td>
                        <td style="color: black;border: none;">${goods.taxAmount}</td>
                    </tr>
                </c:forEach>

                <tr> <td style="border: none;">&nbsp;</td> <td style="border: none;">&nbsp;</td> <td style="border: none;">&nbsp;</td> <td style="border: none;">&nbsp;</td> <td style="border: none;">&nbsp;</td> <td style="border: none;">&nbsp;</td> <td style="border: none;">&nbsp;</td></tr>
                <tr class="total">
                    <td style="border: none;">合计</td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="border: none;"></td>
                    <td style="color: black;border: none;">${totalAmount}</td>
                    <td style="border: none;"></td>
                    <td style="color: black;border: none;">${totalTaxAmount}</td>
                </tr>
                <tr class="GoodsTotal">
                    <td style="text-align: center">价税合计(大写)</td>
                    <td colspan="4" style="border: none;">
                        <div style="width: 100%;display:flex">
                            <div type="text" style="color: black;float: left;padding-left: 8px ">${totalAmountWithTaxWord}</div>
                        </div>
                    </td>
                    <td colspan="1" style="text-align: center;border: none;" >
                        <div type="text" style="color: black"> <span style="color: #9c5223">(小写)</span>&nbsp;&nbsp;${totalAmountWithTax}</div>

                    </td>
                </tr>
            </table>
            <div class="userInfo">
                <div class="password">备注</div>
                <div class="pwdInfo">${comments}</div>
            </div>
        </div>
    </div>
</div>
<!--<button id="capture">复制为图片</button>-->
</body>
</html>
