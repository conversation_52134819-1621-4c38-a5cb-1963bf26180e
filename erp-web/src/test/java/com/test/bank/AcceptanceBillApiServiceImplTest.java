package com.test.bank;

import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.test.BaseTestWithSpring;
import com.vedeng.infrastructure.bank.api.common.service.B2eBankApiService;
import com.vedeng.infrastructure.bank.api.domain.*;
import com.vedeng.infrastructure.bank.api.domain.api.B2eGetTokenApiReq;
import com.vedeng.infrastructure.bank.api.domain.dto.AcceptanceBillCreateDto;
import com.vedeng.infrastructure.bank.api.service.AcceptanceBillApiService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2024/10/11 16:50
 */
@Slf4j
public class AcceptanceBillApiServiceImplTest extends BaseTestWithSpring {

    @Autowired
    private B2eBankApiService b2eBankApiService;

    @Autowired
    private AcceptanceBillApiService acceptanceBillApiService;


    @Test
    public void b2eNbsAcceptanceBillAdd_ValidInput_Success() {
        // Arrange
        B2eGetTokenRes b2EGetTokenRes = null;
        try {
            B2eGetTokenReq b2EGetTokenReq = new B2eGetTokenReq(IdUtil.simpleUUID());
            B2eGetTokenApiReq request = new B2eGetTokenApiReq(b2EGetTokenReq);
            b2EGetTokenRes = b2eBankApiService.callRemote(request, B2eGetTokenRes.class);
            System.out.println(JSON.toJSONString(b2EGetTokenRes));
        } catch (Exception e) {
            log.error("获取文件token异常", e);
        }
    }


    @Test
    public void testB2eNbsAcceptanceBillCreate() {
        // 创建模拟数据
        AcceptanceBillCreateDto createDto = createMockCreateDto();
        // 调用被测试的方法
        B2eDraftApplyRes result = acceptanceBillApiService.b2eNbsAcceptanceBillCreate(createDto);

    }

    private AcceptanceBillCreateDto createMockCreateDto() {
        AcceptanceBillCreateDto createDto = new AcceptanceBillCreateDto();
        createDto.setFileUrl("http://file.ivedeng.com/file/display?resourceId=2a8c03895cc8447c92a47e1b0a5b4542");
        createDto.setPayAppId(16237);
        B2eBasicDraftApplyReq applyReq = new B2eBasicDraftApplyReq();
        applyReq.setTrnId(IdUtil.simpleUUID());
        applyReq.setOutApplicationNo("APP12349111117161811");
        applyReq.setProductCode("********");
        applyReq.setIsAllowSplitBill("0");
        applyReq.setCreditType("01");
        applyReq.setFinancingAmount(BigDecimal.TEN);
        applyReq.setValidFrom("2024-10-21");
        applyReq.setValidTo("2024-11-11");
        applyReq.setCustPhone("***********");
        applyReq.setIssuanceFlag("1");
        applyReq.setIsAutoDelPayeeInfo("0");
        applyReq.setIssueAccountNo("*********");
        applyReq.setEndoFlag("EM01");
        applyReq.setAccountType("02");
        applyReq.setRepayAccountNo("*********");
        applyReq.setFeeAccountNo("*********");


        // 添加 CounterPartyList
        B2eBasicDraftApplyReq.CounterParty counterParty = new B2eBasicDraftApplyReq.CounterParty();
        counterParty.setPartyName("许鱼鱼企业");
        counterParty.setTradeContractNo("1129");
        counterParty.setTradeContractAmount(BigDecimal.TEN);
        counterParty.setTradeValidFrom("2024-10-21");
        counterParty.setTradeValidTo("2024-11-11");
        counterParty.setIsValid("1");
        counterParty.setIsSelfBank("1");
        counterParty.setDepositBankNo("************");
        counterParty.setDepositBankName("中国民生银行股份有限公司广州环市支行");
        counterParty.setPayeeAccountNo("*********");
        counterParty.setPaymentAmount(BigDecimal.TEN);
        applyReq.getCounterPartyList().add(counterParty);

        // 添加 BailInfoList
        B2eBasicDraftApplyReq.BailInfo bailInfo = new B2eBasicDraftApplyReq.BailInfo();
        bailInfo.setAccountType("02");
        bailInfo.setBailAccountNo("*********");
        bailInfo.setAddAmount(BigDecimal.TEN);
        applyReq.getBailInfoList().add(bailInfo);

        createDto.setB2eBasicDraftApplyReq(applyReq);

        return createDto;
    }

}
