<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="提交接口认证" scope="application" />
<%@ include file="../../../common/common.jsp"%>
<script type="text/javascript">
	$(document).ready(function(){
    	$("input[name='rkfile']").on('change', function( e ){
            //e.currentTarget.files 是一个数组，如果支持多个文件，则需要遍历
            var name = e.currentTarget.files[0].name;
            $("#uri").val(name);
        });
    })
    function subForm(){
		if ($("input[name='operateType']:checked").val() == 0){
			var checkBox = parent.document.getElementsByName('ck');
			var params = [];
			for(var i=0; i<checkBox.length; i++){
				if (checkBox[i].checked){
					params.push(checkBox[i].getAttribute('invoiceId'))
				}
			}
			if (params.length < 1){
				layer.alert('请勾选需要认证的发票!');
				return;
			}
			$.ajax({
				url: '/finance/invoice/batchAuthInvoiceByIds.do?isHxInvoiceFlag=' + $('#isHxInvoiceFlag').val(),
				data: JSON.stringify(params),
				type: 'post',
				dataType: 'json',
				contentType: 'application/json;charset=utf-8',
				success: function (res) {
					if (res.code == 0) {
						layer.alert('发票认证成功', function () {
							window.parent.layer.closeAll();
							window.parent.location.reload();
						});
					} else {
						layer.alert(res.message);
					}
				},
				error:function(data){
					if(data.status ==1001){
						layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
					}
				}
			})
		} else {
			$("#addTj").attr('disabled',true);
			$("#errorTit").hide();
			if($("#rkfile").val() == undefined || $("#rkfile").val() == ""){
				layer.alert("请选择需要上传的文件！");$("#uri").focus();
				return;
			}
			$("#batchAuthInvoice").ajaxSubmit({
				async : false,
				url : '/finance/invoice/batchAuthInvoiceByFile.do?isHxInvoiceFlag=' + $('#isHxInvoiceFlag').val(),
				data : $("#batchAuthInvoice").serialize(),
				type : "POST",
				dataType : "json",
				success : function(data) {
					if(data.code==0){
						layer.alert('发票认证成功', function () {
							window.parent.layer.closeAll();
							window.parent.location.reload();
						});

					}else{
						$("#errorTit").html(data.message);
						$("#errorTit").show();
					}
				},
				error : function(XmlHttpRequest, textStatus, errorThrown) {
					layer.alert("操作失败");
				}
			});
		}
	}

	/**
	 * 切换认证操作类型
	 */
	function changeOperateType(operateType) {
		if (operateType == 0){
			$('#form_auth').hide();
		} else {
			$('#form_auth').show();
		}
	}
</script>
<div class="form-list  form-tips4 ml7">
	<form id="batchAuthInvoice" method="post" enctype="multipart/form-data">
		<ul>
			<li>
				<div class="pos_rel">
					<lable>提交认证方式:</lable>
					&nbsp;
					<input type="radio" name="operateType" checked onchange="changeOperateType(0)" value="0">
					认证已勾选数据
					&nbsp;	&nbsp;
					<input type="radio" name="operateType" onchange="changeOperateType(1)" value="1">
					上传表格认证
				</div>
			</li>
		</ul>
		<ul id="form_auth" style="display: none">
			<li style="margin-bottom:8px;">
				<div class="form-tips">
					<lable>请上传表格</lable>
				</div>
				<div class='f_left'>
					<div class="form-blanks">
						<div class="pos_rel">
							<input type="file" class="upload_file" style="display: none;" name="rkfile" id="rkfile">
							<input type="text" class="input-middle" id="uri" placeholder="请上传excel" name="uri" readonly="readonly">
							<label class="bt-bg-style bt-middle bg-light-blue" type="file" onclick="return $('#rkfile').click();">浏览</label>
						</div>
					</div>
					 <div class="pop-friend-tips" style="margin: 7px 0 0 0;">
						如果您没有标准模板，请<a href="<%= basePath %>static/template/接口认证模板.xlsx">下载标准模板</a>
					</div>
				</div>
			</li>
		</ul>
		<div class="font-red ml47 mb20" id="errorTit" style="display: none">表格项错误，第x行x列...</div>
		<div class="add-tijiao tcenter">
			<button type="button" class="bt-bg-style bg-deep-green" id="addTj" onclick="subForm();">提交</button>
			<button class="dele" type="button" id="close-layer">取消</button>
		</div>
		<input type="hidden"  id="isHxInvoiceFlag" value="${isHxInvoiceFlag}">
	</form>
</div>
<%@ include file="../../../common/footer.jsp"%>