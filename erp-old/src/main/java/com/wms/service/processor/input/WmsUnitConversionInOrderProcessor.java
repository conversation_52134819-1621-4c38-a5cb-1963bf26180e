package com.wms.service.processor.input;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dao.WmsInputOrderGoodsMapper;
import com.wms.dao.WmsInputOrderMapper;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsInputOrderGoods;
import com.wms.service.WmsSurplusinService;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import com.wms.service.util.WmsCommonUtil;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderItemMapper;
import com.wms.unitconversionorder.dao.WmsUnitConversionOrderMapper;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrder;
import com.wms.unitconversionorder.model.entity.WmsUnitConversionOrderItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 单位转换单入库回传
 * <AUTHOR>
 */
@Service
public class WmsUnitConversionInOrderProcessor extends AbstractInputOrderProcessor {

    private static final Logger logger = LoggerFactory.getLogger(WmsUnitConversionInOrderProcessor.class);

    @Autowired
    @Qualifier("inputOrderAuditPassCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private WmsUnitConversionOrderMapper wmsUnitConversionOrderMapper;

    @Autowired
    private WmsUnitConversionOrderItemMapper wmsUnitConversionOrderItemMapper;

    @Override
    protected void commonValidator(InputOrderDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.UNIT_CONVERSION_IN.equals(requestBean.getASNType())){
            throw new Exception("入库单:"+requestBean.getASNReference1()+"的类型错误!");
        }
    }
    @Override
    protected void updateOrderData(InputOrderDto inputOrderDto) throws Exception {
        String orderNo = WmsCommonUtil.getOriginalOrderNo(inputOrderDto.getASNReference1());
        logger.info("单位转换入库单  单号:{},入参:{}",orderNo, JSON.toJSONString(inputOrderDto));
        WmsUnitConversionOrder wmsUnitConversionOrder = wmsUnitConversionOrderMapper.selectByWmsUnitConversionOrderNo(orderNo);
        if (Objects.isNull(wmsUnitConversionOrder)) {
            throw new ServiceException("当前单位转换入库单未能找单位转换单：" + orderNo);
        }
        List<WmsUnitConversionOrderItem> wmsUnitConversionOrderItems = wmsUnitConversionOrderItemMapper.selectByWmsUnitConversionOrderId(wmsUnitConversionOrder.getWmsUnitConversionOrderId());
        Map<Integer,WmsUnitConversionOrderItem> wmsInputOrderGoodsMap = wmsUnitConversionOrderItems
                .stream()
                .collect(Collectors.toMap(WmsUnitConversionOrderItem::getWmsUnitConversionOrderItemId, item -> item));
        List<InputOrderGoodsDto> details = inputOrderDto.getDetails();

        Map<Integer,Integer> inNumMap = new HashMap<>();
        for (InputOrderGoodsDto inputOrderGoodsDto : details) {
            Integer detailId = Integer.valueOf(inputOrderGoodsDto.getUserDefine1());
            Integer inNum = inNumMap.get(detailId);
            if(inNum == null){
                inNum = 0;
            }
            inNumMap.put(detailId,inputOrderGoodsDto.getReceivedQty().intValue() + inNum);
        }

        for (Integer detailId : inNumMap.keySet()) {
            WmsUnitConversionOrderItem wmsInputOrderGoods = wmsInputOrderGoodsMap.get(detailId);
            Integer inNum = inNumMap.get(detailId);
            WmsUnitConversionOrderItem updateGoods = new WmsUnitConversionOrderItem();
            updateGoods.setWmsUnitConversionOrderItemId(wmsInputOrderGoods.getWmsUnitConversionOrderItemId());
            updateGoods.setWmsRealInNum(wmsInputOrderGoods.getWmsRealInNum().add(BigDecimal.valueOf(inNum)));
            updateGoods.setInStatus(wmsInputOrderGoods.getTargetNum().compareTo(updateGoods.getWmsRealInNum())<=0  ? 2 : 1);
            wmsUnitConversionOrderItemMapper.updateByPrimaryKeySelective(updateGoods);
        }
    }


    @Override
    protected int getWmsLogicalOperateType(InputOrderDto orderDto) {
        return WmsLogicalOperateTypeEnum.UNIT_CONVERSION_IN.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return Integer.valueOf(goodsDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return  stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 1;
    }

    @Override
    protected int getOperateType(InputOrderDto orderDto) {
        return StockOperateTypeConst.UNIT_CONVERSION_IN;
    }


}
