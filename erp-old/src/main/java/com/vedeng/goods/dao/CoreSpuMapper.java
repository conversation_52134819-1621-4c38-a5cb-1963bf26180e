package com.vedeng.goods.dao;

import com.vedeng.goods.model.CoreSpu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date created in 2020/7/11 11:34
 */
public interface CoreSpuMapper {

    CoreSpu getSpuBySku(String sku);

    /**
     * 获取spu下的sku推送平台总和
     *
     * @param spuId
     * @return
     */
    Integer getPushStatusCountBySpuId(Integer spuId);
    /**
     * 获取
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/5/26 15:53.
     * @author: <PERSON><PERSON>.
     * @param logBizId
     * @return: com.vedeng.goods.model.CoreSpu.
     * @throws:  .
     */
    CoreSpu getSpuinfoById(Integer logBizId);
    /**
     * <b>Description:</b><br>
     * 根据订货号查询产品对应的订单助理
     *
     * @param spu
     * @return java.lang.Integer
     * @Note <b>Author:</b> Thor <br>
     *       <b>Date:</b> 2021/6/19 11:12
     */
    List<Integer> selectOrderAssistantByNo(@Param("spu")String spu);

    List<CoreSpu> listSpu();

    List<CoreSpu> listSpuByModTime(String modTime);

    List<CoreSpu> getSpuInfoBySkuNos(@Param("skuNos") List<String> skuNos);

    List<CoreSpu> getValidingSpuInfoBySpuNo(String spuNo);

    CoreSpu getSpuInfoBySpuNo(String spuNo);

    /**
     * 禁用sku后更新对应的spu信息
     */
    void updateSpuAfterDisableSku(CoreSpu coreSpu);
}
