package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.trader.common.enums.TraderInstitutionNatureEnums;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingNodeDto;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingTerminalDto;
import com.vedeng.erp.trader.dto.TraderDealerFrontDto;
import com.vedeng.erp.trader.mapper.TraderCustomerMarketingTerminalMapper;
import com.vedeng.erp.trader.mapstruct.TraderCustomerMarketingConvertor;
import com.vedeng.erp.trader.service.TraderCustomerMarketingApiService;
import com.vedeng.erp.trader.service.TraderCustomerMarketingNodeApiService;
import com.vedeng.erp.trader.service.TraderCustomerTagChangeRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户营销属性实现类
 * @date 2023/8/7 10:17
 */
@Service
@Slf4j
public class TraderCustomerMarketingServiceImpl implements TraderCustomerMarketingApiService {


    @Autowired
    private TraderCustomerMarketingTerminalMapper traderCustomerMarketingMapper;

    @Autowired
    private TraderCustomerMarketingConvertor traderCustomerMarketingConvertor;

    @Autowired
    private TraderCustomerMarketingNodeApiService traderCustomerMarketingNodeApiService;

    @Autowired
    private TraderCustomerTagChangeRecordService traderCustomerTagChangeRecordService;

    @Override
    public List<TraderCustomerMarketingTerminalDto> getTraderCustomerMarketing(Integer traderCustomerId) {
        List<TraderCustomerMarketingTerminalEntity> traderCustomerMarketingTerminalEntityList = traderCustomerMarketingMapper.findByTraderCustomerId(traderCustomerId);
        List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtos = traderCustomerMarketingConvertor.toDto(traderCustomerMarketingTerminalEntityList);
        List<TraderCustomerMarketingNodeDto> traderCustomerMarketList = traderCustomerMarketingNodeApiService.getTraderCustomerMarketList(6, false);
        if (CollUtil.isEmpty(traderCustomerMarketingTerminalDtos)) {
            return new ArrayList<>();
        }
        Map<Integer, String> threeCustomerTypeMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getThreeCustomerType)
                .collect(Collectors.toMap(x -> Integer.parseInt(x.getLabel()), TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
        Map<String, String> institutionTypeMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getInstitutionType)
                .flatMap(List::stream)
                .collect(Collectors.toMap(TraderCustomerMarketingNodeDto.Node::getLabel, TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
        Map<String, String> institutionLevelMap = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getInstitutionLevel)
                .flatMap(List::stream)
                .collect(Collectors.toMap(TraderCustomerMarketingNodeDto.Node::getLabel, TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));
        Map<String, String> institutionTypeChild = traderCustomerMarketList.stream()
                .map(TraderCustomerMarketingNodeDto::getInstitutionType)
                .flatMap(List::stream)
                .map(TraderCustomerMarketingNodeDto.Node::getChildren).filter(CollUtil::isNotEmpty)
                .flatMap(List::stream)
                .collect(Collectors.toMap(TraderCustomerMarketingNodeDto.Node::getLabel, TraderCustomerMarketingNodeDto.Node::getValue, (k1, k2) -> k1));

        traderCustomerMarketingTerminalDtos.forEach(dto -> {
            dto.setTraderCustomerMarketingTypeName(threeCustomerTypeMap.get(dto.getTraderCustomerMarketingType()));
            dto.setInstitutionTypeName(StrUtil.split(dto.getInstitutionType(), ",").stream().map(institutionTypeMap::get).filter(StringUtils::isNotBlank).collect(Collectors.joining(StrUtil.COMMA)));
            dto.setInstitutionLevelName(StrUtil.split(dto.getInstitutionLevel(), ",").stream().map(institutionLevelMap::get).filter(StringUtils::isNotBlank).collect(Collectors.joining(StrUtil.COMMA)));
            dto.setInstitutionNatureName(StrUtil.split(dto.getInstitutionNature(), ",").stream().map(TraderInstitutionNatureEnums::getEnumName).filter(StringUtils::isNotBlank).collect(Collectors.joining(StrUtil.COMMA)));

            String child = Arrays.stream(dto.getInstitutionTypeChild().split(",")).filter(StringUtils::isNotBlank).map(institutionTypeChild::get).filter(StringUtils::isNotBlank).collect(Collectors.joining("、"));
            if (StringUtils.isNotBlank(child)) {
                dto.setInstitutionTypeName(dto.getInstitutionTypeName().replace("专科", "专科(" + child + ")"));
            }

            dto.setInstitutionTypeChildName(child);
            if (StringUtils.isNotBlank(dto.getOtherInstitutionType())) {
                dto.setInstitutionTypeName(StringUtils.isNotBlank(dto.getInstitutionTypeName()) ? dto.getInstitutionTypeName() + ",其他(" + dto.getOtherInstitutionType() + ")" : dto.getInstitutionTypeName() + "其他(" + dto.getOtherInstitutionType() + ")");
            }
        });
        return traderCustomerMarketingTerminalDtos;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void mergeNewData(TraderCustomerMarketingTerminalDto data) {

        List<TraderCustomerMarketingTerminalEntity> traderCustomerMarketingTerminalEntityList = traderCustomerMarketingMapper.findByTraderCustomerId(data.getTraderCustomerId());

        TraderCustomerMarketingTerminalEntity first = traderCustomerMarketingTerminalEntityList.stream()
                .filter(Objects::nonNull)
                .filter(x -> x.getTraderCustomerMarketingType().equals(data.getTraderCustomerMarketingType()))
                .findFirst().orElse(new TraderCustomerMarketingTerminalEntity());

        data.setInstitutionLevel(mergeNewAndOld(data.getInstitutionLevel(),first.getInstitutionLevel()));
        data.setInstitutionType(mergeNewAndOld(data.getInstitutionType(),first.getInstitutionType()));
        data.setInstitutionNature(mergeNewAndOld(data.getInstitutionNature(),first.getInstitutionNature()));
        data.setInstitutionTypeChild(mergeNewAndOld(data.getInstitutionTypeChild(),first.getInstitutionTypeChild()));

        List<TraderCustomerMarketingTerminalDto> oldData = traderCustomerMarketingConvertor.toDto(traderCustomerMarketingTerminalEntityList);
        // 具体的一个 删除 老数据，存新数据 更新记录
        List<TraderCustomerMarketingTerminalDto> ooldData = oldData.stream().filter(Objects::nonNull)
                .filter(x -> x.getTraderCustomerMarketingType().equals(data.getTraderCustomerMarketingType())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(ooldData)) {
            oldData.forEach(x-> traderCustomerMarketingMapper.deleteByPrimaryKey(x.getTraderCustomerMarketingId()));
        }
        List<TraderCustomerMarketingTerminalDto> newData = Collections.singletonList(data);
        if (CollUtil.isNotEmpty(newData)) {
            newData.forEach(x-> traderCustomerMarketingMapper.insertSelective(traderCustomerMarketingConvertor.toEntity(x)));
        }

        traderCustomerTagChangeRecordService.calcTraderCustomerMarketingTerminalDiffAndSave(1,ooldData,newData,1);

    }


    private String mergeNewAndOld(String newData, String oldData) {
        // 合并
        if (StrUtil.isEmpty(oldData)) {
            return newData;
        }

        if (StrUtil.isEmpty(newData)) {
            return oldData;
        }
        // 去重合并
        List<String> splitOld = StrUtil.split(oldData, StrUtil.COMMA);
        List<String> splitNew = StrUtil.split(newData, StrUtil.COMMA);
        HashSet<String> strings = CollUtil.newHashSet(splitNew);
        strings.addAll(splitOld);

        return String.join(StrUtil.COMMA, strings);
    }

    @Override
    public void add(TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto) {

        traderCustomerMarketingMapper.insertSelective(traderCustomerMarketingConvertor.toEntity(traderCustomerMarketingTerminalDto));

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void update(TraderCustomerMarketingTerminalDto traderCustomerMarketingTerminalDto) {

        traderCustomerMarketingMapper.deleteByTraderCustomerId(traderCustomerMarketingTerminalDto.getTraderCustomerId());
        traderCustomerMarketingMapper.insertSelective(traderCustomerMarketingConvertor.toEntity(traderCustomerMarketingTerminalDto));
    }

    @Override
    public List<TraderCustomerMarketingTerminalDto> processFrontData(TraderDealerFrontDto traderDealerFrontDto) {

        log.info("经销商,processFrontData,param:{}", JSON.toJSONString(traderDealerFrontDto));

        List<TraderDealerFrontDto.Terminal> terminal = traderDealerFrontDto.getTerminal();

        if (CollUtil.isEmpty(terminal)) {
            return Collections.emptyList();
        }
        List<TraderCustomerMarketingTerminalDto> collect = terminal.stream().map(x -> {
            TraderCustomerMarketingTerminalDto data = new TraderCustomerMarketingTerminalDto();
            data.setTraderId(traderDealerFrontDto.getTraderId());
            data.setTraderCustomerId(traderDealerFrontDto.getTraderCustomerId());
            data.setTraderCustomerMarketingType(Integer.valueOf(x.getTraderCustomerMarketingType()));
            data.setInstitutionNature(CollUtil.isNotEmpty(x.getInstitutionNatureChecked()) ? String.join(StrUtil.COMMA, x.getInstitutionNatureChecked()) : "");
            data.setInstitutionLevel(CollUtil.isNotEmpty(x.getInstitutionLevelChecked()) ? String.join(StrUtil.COMMA, x.getInstitutionLevelChecked()) : "");
            data.setInstitutionTypeChild(CollUtil.isNotEmpty(x.getInstitutionTypeChildChecked()) ? String.join(StrUtil.COMMA, x.getInstitutionTypeChildChecked()) : "");
            data.setInstitutionType(CollUtil.isNotEmpty(x.getInstitutionTypeChecked()) ? String.join(StrUtil.COMMA, x.getInstitutionTypeChecked()) : "");
            data.setOtherInstitutionType(x.getOtherInstitutionType());
            data.setTerminalIds(CollUtil.isNotEmpty(x.getTerminalDataChecked()) ? String.join(StrUtil.COMMA, x.getTerminalDataChecked()) : "");
            return data;
        }).collect(Collectors.toList());

        log.info("经销商,processFrontData,result:{}", JSON.toJSONString(collect));

        return collect;
    }

    @Override
    public List<TraderDealerFrontDto.Terminal> stealthFrontData(Integer traderCustomerId) {

        Assert.notNull(traderCustomerId, "客户id不可为空");

        List<TraderCustomerMarketingTerminalEntity> byTraderCustomerId = traderCustomerMarketingMapper.findByTraderCustomerId(traderCustomerId);
        if (CollUtil.isEmpty(byTraderCustomerId)) {
            return Collections.emptyList();
        }
        List<TraderDealerFrontDto.Terminal> terminalList = byTraderCustomerId.stream().map(x -> {
            TraderDealerFrontDto.Terminal terminal = new TraderDealerFrontDto.Terminal();
            terminal.setTraderCustomerMarketingType(x.getTraderCustomerMarketingType().toString());
            terminal.setInstitutionLevelChecked(StrUtil.isNotEmpty(x.getInstitutionLevel()) ? StrUtil.split(x.getInstitutionLevel(), StrUtil.COMMA) : Collections.emptyList());
            terminal.setInstitutionNatureChecked(StrUtil.isNotEmpty(x.getInstitutionNature()) ? StrUtil.split(x.getInstitutionNature(), StrUtil.COMMA) : Collections.emptyList());
            terminal.setInstitutionTypeChecked(StrUtil.isNotEmpty(x.getInstitutionType()) ? StrUtil.split(x.getInstitutionType(), StrUtil.COMMA) : Collections.emptyList());
            terminal.setInstitutionTypeChildChecked(StrUtil.isNotEmpty(x.getInstitutionTypeChild()) ? StrUtil.split(x.getInstitutionTypeChild(), StrUtil.COMMA) : Collections.emptyList());
            terminal.setTerminalDataChecked(StrUtil.isNotEmpty(x.getTerminalIds()) ? StrUtil.split(x.getTerminalIds(), StrUtil.COMMA) : Collections.emptyList());
            terminal.setOtherInstitutionType(x.getOtherInstitutionType());
            return terminal;
        }).collect(Collectors.toList());

        log.info("stealthFrontData,traderCustomerId：{}，转换终端表数据=》页面对象数据：{}",traderCustomerId,JSON.toJSONString(terminalList));

        return terminalList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addAll(List<TraderCustomerMarketingTerminalDto> traderCustomerMarketingTerminalDtoList) {

        if (CollUtil.isEmpty(traderCustomerMarketingTerminalDtoList)) {
            return;
        }
        List<TraderCustomerMarketingTerminalEntity> data = traderCustomerMarketingConvertor.toEntity(traderCustomerMarketingTerminalDtoList);
        data.forEach(x->{
            traderCustomerMarketingMapper.insertSelective(x);
        });
        Integer save = 1;
        traderCustomerTagChangeRecordService.calcTraderCustomerMarketingTerminalDiffAndSave(save,null,traderCustomerMarketingTerminalDtoList,0);

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateAll(List<TraderCustomerMarketingTerminalDto> terminalDtoList,Integer traderCustomerId) {

        List<TraderCustomerMarketingTerminalEntity> byTraderCustomerId = new ArrayList<>();
        if (Objects.nonNull(traderCustomerId)) {
            byTraderCustomerId = traderCustomerMarketingMapper.findByTraderCustomerId(traderCustomerId);
        }

        if (CollUtil.isNotEmpty(byTraderCustomerId)) {
            byTraderCustomerId.forEach(x-> traderCustomerMarketingMapper.deleteByPrimaryKey(x.getTraderCustomerMarketingId()));
        }

        if (CollUtil.isNotEmpty(terminalDtoList)) {
            terminalDtoList.forEach(x-> traderCustomerMarketingMapper.insertSelective(traderCustomerMarketingConvertor.toEntity(x)));
        }
        Integer update = 2;
        traderCustomerTagChangeRecordService.calcTraderCustomerMarketingTerminalDiffAndSave(update,traderCustomerMarketingConvertor.toDto(byTraderCustomerId),terminalDtoList,0);
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void clearByTraderCustomer(Integer traderCustomerId) {
        List<TraderCustomerMarketingTerminalEntity> data = traderCustomerMarketingMapper.findByTraderCustomerId(traderCustomerId);
        if (CollUtil.isNotEmpty(data)) {
            log.info("clearByTraderCustomer,清除，TraderCustomerMarketingTerminalEntity:{}",JSON.toJSONString(data));
            data.forEach(x-> traderCustomerMarketingMapper.deleteByPrimaryKey(x.getTraderCustomerMarketingId()));
        }

    }
    
    /**
     * 根据客户ID获取客户终端属性列表信息
     */
    @Override
    public List<TraderCustomerMarketingTerminalDto> getByTraderCustomerId(Integer traderCustomerId) {
        List<TraderCustomerMarketingTerminalEntity> traderCustomerMarketingTerminalEntityList = traderCustomerMarketingMapper.findByTraderCustomerIdSequence(traderCustomerId);
        return traderCustomerMarketingConvertor.toDto(traderCustomerMarketingTerminalEntityList);
    }
}
