package com.vedeng.erp.business.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 商机常量池类
 * @date 2022/7/12 15:17
 **/
public class BusinessChanceConstant {

    /**
     * 商机等级 计算时候满足条件之一的 满足的最小预付金额
     */
    public static final BigDecimal AMOUNT = new BigDecimal(100000);

    /**
     * 商机类型 销售新增商机
     */
    public static final Integer TYPE_SALE_ADD = 392;
    /**
     * 商机类型 线索转商机字典表
     */
    public static final Integer TYPE_LEADS = 4110;

    /**
     * 询价行为 销售新增商机
     */
    public static final Integer INQUIRY_SALE_ADD = 4062;

    /**
     * 渠道类型 智能客服
     */
    public static final Integer SMART_QUOTE_ADD = 4990;

    /**
     * 询价行为 线索转商机字典表 销售自拓线索
     */
    public static final Integer INQUIRY_LEADS = 4111;

    /**
     * 渠道类型 老客户
     */
    public static final Integer SOURCE_SALE_ADD_OLD = 366;

    /**
     * 渠道类型 新客户
     */
    public static final Integer SOURCE_SALE_ADD_NEW = 477;

    /**
     * 渠道类型 销售自拓线索
     */
    public static final Integer SOURCE_LEADS = 4112;

    /**
     * 渠道来源 销售自拓线索
     */
    public static final Integer COMMUNICATION_LEADS = 4113;

    /**
     * 商机等级 计算时候满足条件之一的 最少sku 数量
     */
    public static Integer SKU_NUM = 5;
    /**
     * 采购方式 直接采购
     */
    public static Integer PURCHASING_DIRECT = 405;
    /**
     * 采购方式 招标采购
     */
    public static Integer PURCHASING_TENDER = 406;

    /**
     * 采购时间 大于三个月
     */
    public static Integer MORE_THREE_MONTH = 417;

    /**
     * 边界值
     */
    public static Integer BOUNDARY_40 = 40;
    /**
     * 边界值
     */
    public static Integer BOUNDARY_60 = 60;
    /**
     * 边界值
     */
    public static Integer BOUNDARY_80 = 80;
    /**
     * 边界值
     */
    public static Integer BOUNDARY_100 = 100;

    /**
     * 终端性质-公立等级
     */
    public static final int ID_5601 = 5601;

    /**
     * 终端性质-公立基层
     */
    public static final int ID_5602 = 5602;

    /**
     * 终端性质-非公等级
     */
    public static final int ID_5603 = 5603;

    /**
     * 终端性质-非公基层
     */
    public static final int ID_5604 = 5604;

    /**
     * 终端性质-非公集团
     */
    public static final int ID_5605 = 5605;

    /**
     * 终端性质-应急
     */
    public static final int ID_5606 = 5606;

    /**
     * 终端性质-院外
     */
    public static final int ID_5607 = 5607;



    /**
     * 终端性质-专业公卫   VDERP-17862 Aadi添加
     */
    public static final int ID_6010 = 6010;



    /**
     * 商机类型-小产品
     */
    public static final int ID_5701 = 5701;

    /**
     * 商机类型-大单品
     */
    public static final int ID_5702 = 5702;

    /**
     * 商机类型-综合项目
     */
    public static final int ID_5703 = 5703;

    /**
     * 商机类型-AED
     */
    public static final int ID_5704 = 5704;

    /**
     * 商机类型-应急
     */
    public static final int ID_5705 = 5705;

    /**
     * 招投标阶段-提案咨询
     */
    public static final int ID_5801 = 5801;

    /**
     * 招投标阶段-立项论证
     */
    public static final int ID_5802 = 5802;

    /**
     * 招投标阶段-意向公示
     */
    public static final int ID_5803 = 5803;

    /**
     * 招投标阶段-公开招标
     */
    public static final int ID_5804 = 5804;

    /**
     * 招投标阶段-合同签署
     */
    public static final int ID_5805 = 5805;
    
}
