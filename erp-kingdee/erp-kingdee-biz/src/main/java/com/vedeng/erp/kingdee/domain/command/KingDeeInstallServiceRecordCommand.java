package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 销售安调单 dto
 * <AUTHOR>
 * @version 1.0
 * @description: 销售安调单 dto  由erp实际业务转换
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeInstallServiceRecordCommand {


    /**
     * fid
     */
    private Integer fid;
    /**
     * fBillNo
     */
    private String FBillNo;
    /**
     * fQzokOrgid
     */
    private KingDeeNumberCommand FQzokOrgId = new KingDeeNumberCommand();
    /**
     * fQzokYssj
     */
    private String f_QZOK_YSSJ;
    /**
     * fQzokDate
     */
    private String f_QZOK_Date;
    /**
     * fQzokBcfwsl
     */
    private BigDecimal f_QZOK_BCFWSL;
    /**
     * fQzokQssj
     */
    private String f_QZOK_QSSJ;
    /**
     * fQzokYsfs
     */
    private String f_QZOK_YSFS;
    /**
     * fQzokYsjl
     */
    private String f_QZOK_YSJL;
    /**
     * fQzokYsddh
     */
    private String f_QZOK_YSDDH;
    /**
     * fQzokGsywdh
     */
    private String f_QZOK_GSYWDH;
    /**
     * fQzokYwlx
     */
    private String f_QZOK_YWLX;
    /**
     * fQzokWlbm
     */
    private KingDeeNumberCommand f_QZOK_WLBM = new KingDeeNumberCommand();
    /**
     * fQzokXlh
     */
    private String f_QZOK_XLH;
    /**
     * fQzokFid
     */
    private String f_QZOK_FID;
    /**
     * fQzokFentryid
     */
    private String f_QZOK_FEntryId;
    /**
     * fQzokFentryid
     */
    private String f_QZOK_CKDH;
}
