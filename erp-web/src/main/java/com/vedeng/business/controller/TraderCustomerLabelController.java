package com.vedeng.business.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.trader.domain.vo.TraderCustomerTuokeLabelVo;
import com.vedeng.erp.trader.service.TraderCustomerLabelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * 标签
 * <AUTHOR>
 * @date 2022/3/10 17:12
 **/
@Controller
@RequestMapping("/tuoke/traderCustomerLabel")
public class TraderCustomerLabelController extends BaseController {

    @Autowired
    private TraderCustomerLabelService traderCustomerLabelService;

    @RequestMapping("/editView")
    public ModelAndView editView(HttpServletRequest request, TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo) {

        traderCustomerLabelService.getTraderCustomerLabel(traderCustomerTuokeLabelVo);
        ModelAndView mv = new ModelAndView("/tuoke/edit_trader_customer_label");
        mv.addObject("traderCustomerTuokeLabelVo", traderCustomerTuokeLabelVo);
        return mv;
    }

    @RequestMapping("/saveTraderCustomerLabel")
    @ResponseBody
    public ResultInfo saveTraderCustomerLabel(HttpServletRequest request, TraderCustomerTuokeLabelVo traderCustomerTuokeLabelVo) {
        try {
            User sessionUser = getSessionUser(request);
            if (Objects.isNull(sessionUser)) {
                throw new ServiceException("请重新登录");
            }
            if (Objects.isNull(traderCustomerTuokeLabelVo.getTraderId())) {
                throw new ServiceException("客户id不可为空");
            }
            if (Objects.isNull(traderCustomerTuokeLabelVo.getTraderCustomerTuokeLabelId())) {
                traderCustomerTuokeLabelVo.setCreator(sessionUser.getUserId());
                traderCustomerTuokeLabelVo.setAddTime(System.currentTimeMillis());
            }
            traderCustomerTuokeLabelVo.setUpdater(sessionUser.getUserId());
            traderCustomerTuokeLabelVo.setModTime(System.currentTimeMillis());

            traderCustomerLabelService.updateOrSaveTraderCustomerTuokeLabel(traderCustomerTuokeLabelVo);
            return ResultInfo.success();
        } catch (Exception e) {
            logger.error("saveTraderCustomerLabel error",e);
            return ResultInfo.error(e.getMessage());
        }

    }
}
