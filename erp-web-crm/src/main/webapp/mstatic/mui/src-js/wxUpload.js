Vue.component('ui-wx-upload', {
    template: `<div class="ui-wx-upload-wrap">
        <div class="ui-wx-upload-item" @click="prevImg(index)" v-for="(item, index) in picList" :key="index">
            <img :src="item">
            <div class="ui-wx-upload-item-del" @click.stop="delItem(index)">
                <i class="vd-ui_icon icon-recycle"></i>
            </div>
        </div>
        <div class="ui-wx-upload-btn" @click="chooseImg" v-show="picList.length < limit">
            <div class="ui-wx-upload-btn-inner" v-if="!isloading"></div>
            <i class="vd-ui_icon icon-loading" v-if="isloading"></i>
        </div>
    </div>`,
    props: {
        limit: {
            type: Number,
            default: 5
        },
        list: {
            type: Array,
            default() {
                return [];
            }
        }
    },
    data() {
        return {
            isloading: false,
            picList: []
        };
    },
    computed: {

    },
    mounted() {
        this.picList = this.list;
    },
    methods: {
        async chooseImg() {
            if(this.picList.length >= this.limit || this.isloading) {
                return;
            }

            this.isloading = true;
            let _this = this;
            let img = await ww.chooseImage({
                count: 1,
                sizeType: ['original'],
                sourceType: ['camera'],
                defaultCameraMode: 'normal',
                cancel: function(){
                    _this.isloading = false;
                }
            });

            if(img.errCode === 0 && img.localIds && img.localIds.length) {
                let uploadInfo = await ww.uploadImage({
                    localId: img.localIds[0],
                    isShowProgressTips: true
                })

                if(uploadInfo.errCode === 0 && uploadInfo.serverId) {
                    this.$axios.get('/crm/common/public/uploadWxFileByMediaId?mediaId=' + uploadInfo.serverId).then(({data}) => {
                        this.isloading = false;
                        if(data.code === 0) {
                            this.picList.push(data.httpUrl);
                            this.$emit('change', this.picList);
                        }
                    })
                }
            } 
        },
        delItem(index) {
            this.picList.splice(index, 1);
            this.$emit('change', this.picList);
        },
        prevImg(index) {
            ww.previewImage({
                current: this.picList[index],
                urls: this.picList
            });
        }
    }
})