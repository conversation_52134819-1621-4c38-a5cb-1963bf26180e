.vd-ui-cascader {
  position: relative;
  width: 300px;
  height: 100%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper {
  position: relative;
  width: 100%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon {
  position: absolute;
  right: 5px;
  top: 1px;
  font-size: 16px;
  color: #666666;
  cursor: pointer;
  pointer-events: none;
  line-height: 29px;
  transition: 0.19s;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon.large {
  line-height: 42px;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon.small {
  line-height: 26px;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .icon.rotate {
  transform: rotate(180deg);
  transition: 0.22s;
}
.vd-ui-cascader .vd-ui-cascader-wrapper input::-webkit-input-placeholder {
  color: #999999;
}
.vd-ui-cascader .vd-ui-cascader-wrapper /deep/ .vd-ui-input input {
  cursor: pointer;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap {
  position: relative;
  color: #333;
  background: #fff;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  height: 30px;
  padding: 0px 10px;
  font-size: 14px;
  padding: 0 40px 0 10px;
  display: flex;
  align-items: center;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  margin-right: 5px;
  max-width: 80%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag {
  position: relative;
  height: 22px;
  padding: 0 5px;
  background: #F5F7FA;
  border: solid 1px #E1E5E8;
  border-radius: 2px;
  font-size: 12px;
  line-height: 19px;
  color: #333;
  margin-right: 5px;
  padding-right: 22px;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag .tag-del {
  width: 20px;
  height: 21px;
  font-size: 14px;
  color: #999;
  cursor: pointer;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag .tag-del:hover {
  color: #f60;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .multiple-tags .tag-add {
  height: 22px;
  padding: 0 5px;
  background: #F5F7FA;
  border: solid 1px #E1E5E8;
  border-radius: 2px;
  font-size: 12px;
  line-height: 19px;
  color: #333;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .input {
  flex: 1;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .input > input {
  width: 100%;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix {
  position: absolute;
  height: 100%;
  right: 5px;
  top: 0!important;
  text-align: center;
  color: #c0c4cc;
  transition: all 0.3s;
  pointer-events: none;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix .vd-ui-input__suffix-inner .icon {
  top: 0!important;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix.clear {
  pointer-events: all;
  right: 0;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix.clear .icon-error2 {
  width: 33px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
.vd-ui-cascader .vd-ui-cascader-wrapper .multiple-wrap .vd-ui-input__suffix.clear .icon-error2:hover {
  color: #333;
}
.vd-ui-cascader .vd-ui-cascader-panel-wrap {
  display: inline-flex;
  position: absolute;
  z-index: 1500;
}
.vd-ui-cascader .vd-ui-cascader-menu {
  display: inline-flex;
  position: absolute;
  z-index: 1500;
}
.vd-ui-cascader .vd-ui-cascader-menu.width {
  width: 100%;
}
.vd-ui-cascader .vd-ui-cascader-menu.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader .vd-ui-cascader-menu.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li {
  box-sizing: border-box;
  width: 300px;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li p {
  padding-left: 10px;
  height: 29px;
  line-height: 29px;
}
.vd-ui-cascader .vd-ui-cascader-menu .loading-li p i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 2px;
  font-size: 16px;
  margin-right: 5px;
  color: #0099FF;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li {
  box-sizing: border-box;
  width: 300px;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li p {
  padding-left: 10px;
  height: 29px;
  line-height: 29px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li p i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.vd-ui-cascader .vd-ui-cascader-menu .failed-li p .reload {
  color: #0099FF;
  cursor: pointer;
}
.vd-ui-cascader .suggestion-list {
  width: 100%;
  box-sizing: border-box;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader .suggestion-list .filter-list {
  padding: 0 10px;
  max-height: 200px;
  overflow-y: auto;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader .suggestion-list .filter-list::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader .suggestion-list .filter-list .filter-item {
  height: 33px;
  line-height: 33px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.vd-ui-cascader .suggestion-list .filter-list .filter-item.active {
  color: #09f;
}
.vd-ui-cascader .suggestion-list .no-filter {
  padding: 5px 0;
  color: #999;
  text-align: center;
}
.vd-ui-cascader .menu-wrap {
  display: inline-flex;
}
@keyframes appear {
  0% {
    opacity: 0;
    -webkit-transform: scale(1, 0);
  }
  100% {
    -webkit-transform: scale(1, 1);
    opacity: 1;
  }
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-cascader-node {
  box-sizing: border-box;
  overflow: auto;
  border: solid 1px #BABFC2;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  background-color: #ffffff;
  padding: 5px 0px;
  list-style-type: none;
  margin: 0;
}
.vd-ui-cascader-node::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.vd-ui-cascader-node::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.vd-ui-cascader-node::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.vd-ui-cascader-node::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.vd-ui-cascader-node::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.vd-ui-cascader-node.multiple {
  width: 200px!important;
}
.vd-ui-cascader-node.appear-up {
  transform-origin: center bottom;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader-node.appear-down {
  transform-origin: center top;
  animation: appear 0.22s ease-out;
}
.vd-ui-cascader-node li {
  width: 100%;
  cursor: pointer;
  padding: 0px 10px;
  padding-right: 36px;
  height: 33px;
  box-sizing: border-box;
  line-height: 33px;
  position: relative;
  display: flex;
  align-items: center;
}
.vd-ui-cascader-node li.selected {
  color: #09f;
}
.vd-ui-cascader-node li p {
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
.vd-ui-cascader-node li .icon-right {
  position: absolute ;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
}
.vd-ui-cascader-node li .icon-radio1,
.vd-ui-cascader-node li .icon-radio3 {
  line-height: 1;
  margin-right: 5px;
}
.vd-ui-cascader-node li .icon-radio1 {
  color: #666;
}
.vd-ui-cascader-node li .icon-radio3 {
  color: #09f;
}
.ui-cascader-checkbox-wrap {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  border: solid 1px #969B9E;
  border-radius: 3px;
  margin-right: 5px;
}
.ui-cascader-checkbox-wrap.active {
  border: solid 1px #09f;
  background: #09f;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ui-cascader-checkbox-wrap.active .icon-selected2 {
  font-size: 12px;
  color: #fff;
}
.ui-cascader-checkbox-wrap.active .icon-deduct {
  font-size: 12px;
  color: #fff;
}
