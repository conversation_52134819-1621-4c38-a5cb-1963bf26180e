<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="invoiceApplyList" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>


<div class="parts" liname="开票申请" id="开票申请">
    <div class="title-container title-container-yellow">
        <div class="table-title nobor">
            开票申请
        </div>
    </div>
    <table class="table">

        <thead>
        <tr>
            <th>申请人</th>
            <th>申请时间</th>
            <th>申请方式</th>
            <th>开票方式</th>
            <th>申请结果</th>
            <th>操作</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="list" items="${invoiceApplyList}">
            <tr>
                <td>
                        ${list.creatorName}
                </td>
                <td><date:date value="${list.addTime}" format="yyyy.MM.dd HH:mm:ss"/></td>
                <td>
                    <c:if test="${list.applyMethod eq 0}">手动申请触发</c:if>
                    <c:if test="${list.applyMethod eq 1}">定时任务触发</c:if>
                    <c:if test="${list.applyMethod eq 2}">票货同行物流部申请</c:if>
                    <c:if test="${list.applyMethod eq 3}">客户在线申请开票</c:if>
                    <c:if test="${list.applyMethod eq 4}">售后手动申请</c:if>
                </td>
                <td>
                    <c:if test="${list.isAuto eq 1}">手动纸质开票</c:if>
                    <c:if test="${list.isAuto eq 2}">自动纸质开票</c:if>
                    <c:if test="${list.isAuto eq 3}">自动电子发票</c:if>
                    <c:if test="${list.isAuto eq 4}">自动数电发票</c:if>
                </td>
                <td>
                    <c:if test="${list.advanceValidStatus eq 0 and list.validStatus eq 0}">审核中</c:if>
                    <c:if test="${list.advanceValidStatus eq 1 and list.validStatus eq 0}">审核中</c:if>
                    <c:if test="${list.validStatus eq 1 and list.advanceValidStatus eq 1}">审核通过</c:if>
                    <c:if test="${list.validStatus eq 2 or list.advanceValidStatus eq 2}">审核不通过</c:if>
                </td>
                <td>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small  pop-new-data mr10" layerParams='{"width":"80%","height":"600px","title":"查看开票申请","link":"/invoice/invoiceApply/invoiceApplyDetail.do?invoiceApplyId=${list.invoiceApplyId}"}'>
                        查看开票申请
                    </button>
                </td>
            </tr>
        </c:forEach>
        <c:if test="${empty invoiceApplyList}">
            <tr>
                <td colspan="6">暂无开票申请。</td>
            </tr>
        </c:if>
        </tbody>
    </table>
</div>