package com.pricecenter.service.validator;

import com.pricecenter.service.Validator;
import com.pricecenter.dto.ValidatorResult;

import java.util.ArrayList;
import java.util.List;

public class ValidatorChain<T> implements Validator<T> {

    private List<Validator> validatorChains = new ArrayList<Validator>();

    public void add(Validator validator){
        validatorChains.add(validator);
    }

    @Override
    public ValidatorResult validator(T requestData) {

        if(validatorChains.size() == 0){
            return ValidatorResult.newBuild().setResult(true);
        }

        ValidatorResult validatorResult = ValidatorResult.newBuild().setResult(true);

        for(Validator validator : validator<PERSON>hains){
            validatorResult = validator.validator(requestData);
            if(validatorResult.getResult() == false){
                break;
            }
        }

        return validatorResult;
    }

}
