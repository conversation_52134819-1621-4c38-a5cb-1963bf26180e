package com.vedeng.temporal.validation;

import cn.hutool.core.util.StrUtil;
import com.vedeng.temporal.domain.dto.PollingRequest;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;

/**
 * 业务完成检查配置解析器（增强版）
 * 将字符串配置解析为 BusinessCompletionChecker 实例
 * 
 * 支持的配置语法：
 * - 基础比较: field:value, field:>value, field:>=value, field:<value, field:<=value, field:!=value
 * - 字符串操作: field:contains:value, field:startsWith:value, field:endsWith:value, field:matches:regex
 * - 空值检查: field:isNull, field:isNotNull, field:isBlank, field:isNotBlank
 * - 集合操作: field:in:value1,value2,value3, field:notIn:value1,value2,value3
 * - 集合大小: field.size:>0, field.length:>=10
 * - 嵌套字段: user.profile.status:ACTIVE, order.details[0].amount:>100
 * - 逻辑运算: condition1 & condition2, condition1 | condition2, !(condition)
 * - 括号分组: (condition1 | condition2) & condition3
 * - 日期比较: createTime:after:2025-01-01, updateTime:before:2025-12-31
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (增强版)
 * @since 2025-01-22
 */
@Slf4j
public class CompletionCheckConfigParser {
    
    /**
     * 配置解析缓存，避免重复解析相同配置
     */
    private static final Map<String, BusinessCompletionChecker> CACHE = new ConcurrentHashMap<>();
    
    /**
     * 操作符优先级定义
     */
    private static final Map<String, Integer> OPERATOR_PRECEDENCE = new HashMap<>();
    
    static {
        OPERATOR_PRECEDENCE.put("!", 3);  // 取反
        OPERATOR_PRECEDENCE.put("&", 2);  // AND
        OPERATOR_PRECEDENCE.put("|", 1);  // OR
    }
    
    /**
     * 解析配置字符串为 BusinessCompletionChecker
     * 
     * @param config 配置字符串
     * @return BusinessCompletionChecker 实例
     */
    public static BusinessCompletionChecker parseConfig(String config) {
        if (config == null || config.trim().isEmpty()) {
            throw new IllegalArgumentException("配置字符串不能为空");
        }
        
        String normalizedConfig = config.trim();
        
        // 使用缓存避免重复解析
        return CACHE.computeIfAbsent(normalizedConfig, configKey -> {
            try {
                return parseExpression(configKey);
            } catch (Exception e) {
                log.error("解析完成检查配置失败: {}", configKey, e);
                throw new RuntimeException("配置解析失败: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * 清理配置缓存
     */
    public static void clearCache() {
        CACHE.clear();
        log.info("配置解析缓存已清理");
    }
    
    /**
     * 获取缓存统计信息
     */
    public static Map<String, Object> getCacheStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("size", CACHE.size());
        stats.put("keys", new ArrayList<>(CACHE.keySet()));
        return stats;
    }
    
    // ========== 解析逻辑 ==========
    
    /**
     * 解析表达式
     */
    private static BusinessCompletionChecker parseExpression(String expression) {
        expression = expression.trim();
        
        // 处理取反操作符
        if (expression.startsWith("!")) {
            String innerExpression = expression.substring(1).trim();
            if (innerExpression.startsWith("(") && innerExpression.endsWith(")")) {
                innerExpression = innerExpression.substring(1, innerExpression.length() - 1);
            }
            return parseExpression(innerExpression).negate();
        }
        
        // 处理括号
        if (expression.startsWith("(") && expression.endsWith(")") && isValidParentheses(expression)) {
            return parseExpression(expression.substring(1, expression.length() - 1));
        }
        
        // 查找最低优先级的操作符（从右到左）
        int operatorPos = findMainOperator(expression);
        if (operatorPos > 0) {
            String leftExpr = expression.substring(0, operatorPos).trim();
            String operator = expression.substring(operatorPos, operatorPos + 1);
            String rightExpr = expression.substring(operatorPos + 1).trim();
            
            BusinessCompletionChecker leftChecker = parseExpression(leftExpr);
            BusinessCompletionChecker rightChecker = parseExpression(rightExpr);
            
            if ("&".equals(operator)) {
                return leftChecker.and(rightChecker);
            } else if ("|".equals(operator)) {
                return leftChecker.or(rightChecker);
            }
        }
        
        // 解析单个条件
        return parseCondition(expression);
    }
    
    /**
     * 查找主要操作符位置
     */
    private static int findMainOperator(String expression) {
        int minPrecedence = Integer.MAX_VALUE;
        int operatorPos = -1;
        int parenthesesLevel = 0;
        
        // 从右到左查找，确保左结合性
        for (int i = expression.length() - 1; i >= 0; i--) {
            char c = expression.charAt(i);
            
            if (c == ')') {
                parenthesesLevel++;
            } else if (c == '(') {
                parenthesesLevel--;
            } else if (parenthesesLevel == 0) {
                String op = String.valueOf(c);
                Integer precedence = OPERATOR_PRECEDENCE.get(op);
                if (precedence != null && precedence < minPrecedence) {
                    minPrecedence = precedence;
                    operatorPos = i;
                }
            }
        }
        
        return operatorPos;
    }
    
    /**
     * 验证括号是否匹配
     */
    private static boolean isValidParentheses(String expression) {
        int count = 0;
        for (char c : expression.toCharArray()) {
            if (c == '(') count++;
            else if (c == ')') count--;
            if (count < 0) return false;
        }
        return count == 0;
    }
    
    /**
     * 解析单个条件
     */
    private static BusinessCompletionChecker parseCondition(String condition) {
        condition = condition.trim();
        
        if (!condition.contains(":")) {
            throw new IllegalArgumentException("无效的条件格式，缺少冒号分隔符: " + condition);
        }
        
        String[] parts = condition.split(":", 3);  // 最多分割3部分，支持复杂操作
        
        if (parts.length < 2) {
            throw new IllegalArgumentException("无效的条件格式: " + condition);
        }
        
        String fieldPath = parts[0].trim();
        String operator = parts[1].trim().toLowerCase();
        String value = parts.length > 2 ? parts[2].trim() : "";
        
        return createChecker(fieldPath, operator, value);
    }
    
    /**
     * 创建检查器
     */
    private static BusinessCompletionChecker createChecker(String fieldPath, String operator, String value) {
        try {
            switch (operator) {
                // 空值检查
                case "isnull":
                    return CompletionCheckers.fieldIsNull(fieldPath);
                case "isnotnull":
                case "notnull":
                    return CompletionCheckers.fieldNotNull(fieldPath);
                case "isblank":
                    return (response, request) -> {
                        Object fieldValue = getNestedValue(response, fieldPath);
                        return fieldValue == null || (fieldValue instanceof String && StrUtil.isBlank((String) fieldValue));
                    };
                case "isnotblank":
                case "notblank":
                    return CompletionCheckers.fieldNotBlank(fieldPath);
                
                // 字符串操作
                case "contains":
                    return CompletionCheckers.fieldContains(fieldPath, value);
                case "startswith":
                    return CompletionCheckers.fieldStartsWith(fieldPath, value);
                case "endswith":
                    return CompletionCheckers.fieldEndsWith(fieldPath, value);
                case "matches":
                    return CompletionCheckers.fieldMatches(fieldPath, value);
                
                // 集合操作
                case "in":
                    String[] inValues = value.split(",");
                    Object[] trimmedInValues = Arrays.stream(inValues)
                            .map(String::trim)
                            .toArray();
                    return CompletionCheckers.fieldIn(fieldPath, trimmedInValues);
                case "notin":
                    String[] notInValues = value.split(",");
                    Object[] trimmedNotInValues = Arrays.stream(notInValues)
                            .map(String::trim)
                            .toArray();
                    return CompletionCheckers.fieldNotIn(fieldPath, trimmedNotInValues);
                
                // 数值比较
                case ">":
                    return CompletionCheckers.fieldGreaterThan(fieldPath, parseNumber(value));
                case ">=":
                    return CompletionCheckers.fieldGreaterThanOrEqual(fieldPath, parseNumber(value));
                case "<":
                    return CompletionCheckers.fieldLessThan(fieldPath, parseNumber(value));
                case "<=":
                    return CompletionCheckers.fieldLessThanOrEqual(fieldPath, parseNumber(value));
                case "!=":
                case "ne":
                    return CompletionCheckers.fieldNotEquals(fieldPath, parseValue(value));
                
                // 日期操作
                case "after":
                    return CompletionCheckers.dateAfter(fieldPath, parseDate(value));
                case "before":
                    return CompletionCheckers.dateBefore(fieldPath, parseDate(value));
                case "today":
                    return CompletionCheckers.dateIsToday(fieldPath);
                
                // 默认相等比较
                default:
                    // 处理复合条件（如 field:>value 的情况）
                    if (operator.startsWith(">")) {
                        String numValue = operator.length() > 1 ? operator.substring(1) + ":" + value : value;
                        return CompletionCheckers.fieldGreaterThan(fieldPath, parseNumber(numValue));
                    } else if (operator.startsWith(">=")) {
                        String numValue = operator.length() > 2 ? operator.substring(2) + ":" + value : value;
                        return CompletionCheckers.fieldGreaterThanOrEqual(fieldPath, parseNumber(numValue));
                    } else if (operator.startsWith("<")) {
                        String numValue = operator.length() > 1 ? operator.substring(1) + ":" + value : value;
                        return CompletionCheckers.fieldLessThan(fieldPath, parseNumber(numValue));
                    } else if (operator.startsWith("<=")) {
                        String numValue = operator.length() > 2 ? operator.substring(2) + ":" + value : value;
                        return CompletionCheckers.fieldLessThanOrEqual(fieldPath, parseNumber(numValue));
                    } else {
                        // 简单相等比较（兼容旧格式）
                        return CompletionCheckers.fieldEquals(fieldPath, parseValue(operator + (value.isEmpty() ? "" : ":" + value)));
                    }
            }
        } catch (Exception e) {
            throw new IllegalArgumentException("创建检查器失败: " + fieldPath + ":" + operator + ":" + value, e);
        }
    }
    
    // ========== 工具方法 ==========
    
    /**
     * 获取嵌套字段值
     */
    private static Object getNestedValue(Map<String, Object> data, String fieldPath) {
        if (data == null || fieldPath == null) {
            return null;
        }
        
        String[] parts = fieldPath.split("\\.");
        Object current = data;
        
        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
            } else {
                return null;
            }
        }
        
        return current;
    }
    
    /**
     * 解析数值
     */
    private static Number parseNumber(String value) {
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无法解析为数值: " + value, e);
        }
    }
    
    /**
     * 解析日期
     */
    private static LocalDate parseDate(String value) {
        try {
            return LocalDate.parse(value);
        } catch (Exception e) {
            throw new IllegalArgumentException("无法解析为日期: " + value, e);
        }
    }
    
    /**
     * 解析值（自动类型转换）
     */
    private static Object parseValue(String value) {
        if (value == null || value.isEmpty()) {
            return value;
        }
        
        // 尝试解析为数值
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException ignored) {
            // 继续尝试其他类型
        }
        
        // 尝试解析为布尔值
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }
        
        // 默认返回字符串
        return value;
    }
    
    /**
     * 验证配置语法
     */
    public static void validateConfig(String config) {
        try {
            parseConfig(config);
        } catch (Exception e) {
            throw new IllegalArgumentException("配置语法错误: " + e.getMessage(), e);
        }
    }
    
    /**
     * 获取配置支持的操作符列表
     */
    public static List<String> getSupportedOperators() {
        return Arrays.asList(
            "isNull", "isNotNull", "isBlank", "isNotBlank",
            "contains", "startsWith", "endsWith", "matches",
            "in", "notIn", ">", ">=", "<", "<=", "!=", "ne",
            "after", "before", "today"
        );
    }
}
