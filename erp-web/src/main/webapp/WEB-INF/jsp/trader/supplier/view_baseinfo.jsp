<%@page import="java.util.Date"%>
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="基本信息" scope="application" />	
<%@ include file="../../common/common.jsp"%>
<%@ include file="supplier_tag.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/supplier/view_baseinfo.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
	$(function(){
		var	url = page_url + '/trader/supplier/baseinfo.do?traderId='+$("#traderId").val()+'&traderSupplierId='+$("#traderSupplierId").val();
		if($(window.frameElement).attr('src').indexOf("baseinfo")<0){
			$(window.frameElement).attr('data-url',url);
		}
	});
</script>
<div class="content">
	<div class="parts">
		<div class="title-container">
			<div class="table-title">基本信息</div>
			<c:if test="${traderSupplier.isEnable == 1 &&  traderSupplier.verifyStatus != 0 }">
                <div class="title-click addtitle"
                     tabTitle='{"num":"supplier_edit","link":"./trader/supplier/editbaseinfo.do?traderSupplierId=${traderSupplier.traderSupplierId}","title":"编辑信息"}'>编辑</div>
            </c:if>
		</div>
		<table class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
				<tr>
					<td class="table-smallest" style="width:15%;">
						<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
						供应商名称
					</td>
					<td class="table-middle">${traderSupplier.trader.traderName }</td>
					<td class="table-smallest">供应商id</td>
					<td class="table-middle">${traderSupplier.trader.traderId }</td>
					<td class="table-smallest">
						<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
						供应商所在地区
					</td>
					<td class="table-middle">${region }</td>
				</tr>
				<input id="traderId" name="traderId" type="hidden" value="${traderSupplier.trader.traderId }"/>
				<input id="traderSupplierId" name="traderSupplierId" type="hidden" value="${traderSupplier.traderSupplierId}"/>
				<tr>
					<td>
						<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
						供应商类型</td>
					<td colspan="5" class="text-left">
						<c:if test="${traderSupplier.traderType eq 1}">生产厂家</c:if>
						<c:if test="${traderSupplier.traderType eq 2}">渠道商</c:if>
					</td>
				</tr>
				<tr>
					<td>
						<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
						库房地区
					</td>
					<td colspan="5" class="text-left">
						${warehouseRegion}
					</td>
				</tr>
				<tr>
					<td>
						<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
						库房详细地址
					</td>
					<td colspan="5" class="text-left">
						${traderSupplier.warehouseAddress}
					</td>
				</tr>
			<%--	<tr>
					<td>供应商品</td>
					<td colspan="5" class="text-left">${traderSupplier.supplyProduct }</td>
				</tr>--%>
				<tr>
					<td>热线电话</td>
					<td colspan="5" class="text-left">${traderSupplier.hotTelephone }</td>
				</tr>
				<tr>
					<td>售后总对接人</td>
					<td colspan="5" class="text-left">${traderSupplier.afterSaleManager } &nbsp;${traderSupplier.serviceTelephone }</td>
				</tr>

				<tr>
					<td>安装服务联系人</td>
					<td colspan="5" class="text-left">${traderSupplier.installServiceContactName } &nbsp;${traderSupplier.installServiceContactWay }</td>
				</tr>
				<tr>
					<td>技术支持联系人</td>
					<td colspan="5" class="text-left">${traderSupplier.technicalDirectContactName } &nbsp;${traderSupplier.technicalDirectContactWay }</td>
				</tr>

				<tr>
					<td>维修服务联系人</td>
					<td colspan="5" class="text-left">${traderSupplier.maintenanceContactName } &nbsp;${traderSupplier.maintenanceContactWay }</td>
				</tr>
				<tr>
					<td>退换货联系人</td>
					<td colspan="5" class="text-left">${traderSupplier.exchangeContactName } &nbsp;${traderSupplier.exchangeContactWay }</td>
				</tr>

				<tr>
					<td>其他对接人</td>
					<td colspan="5" class="text-left">${traderSupplier.otherContactName } &nbsp;${traderSupplier.otherContactWay }</td>
				</tr>
				<%--<tr>
					<td>承运商名称</td>
					<td colspan="5" class="text-left">${traderSupplier.logisticsName }</td>
				</tr>
				<tr>
					<td>企业宣传片</td>
					<td colspan="5" class="text-left">
						<c:forEach items="${traderSupplier.companyUriList}" var="cmu">
							<a href="${cmu.uri}" target="_blank">${cmu.uri}</a>&nbsp;&nbsp;&nbsp;
						</c:forEach>
					</td>
				</tr>
				<tr>
					<td>官方网址</td>
					<td colspan="5" class="text-left">
						<a href="${traderSupplier.website}" target="_blank">${traderSupplier.website}</a>
					</td>
				</tr>
				<tr>
					<td>供应品牌</td>
					<td colspan="5" class="text-left">
						<c:if test="${not empty traderSupplier.traderSupplierSupplyBrands }">
							<c:forEach items="${traderSupplier.traderSupplierSupplyBrands }" var="brand">
								${brand.brand.brandName }、
							</c:forEach>
						</c:if>
					</td>
				</tr>--%>
				<tr>
					<td>备注</td>
					<td colspan="5" class="text-left">
						<c:if test="${traderSupplier.comments != null}">
							${traderSupplier.comments}
						</c:if>
					</td>
				</tr>
				<%--<tr>
					<td>简介</td>
					<td colspan="5" class="text-left">
						<c:if test="${traderSupplier.brief != null}">
							${traderSupplier.brief}
						</c:if>
					</td>
				</tr>--%>
			</tbody>
		</table>
	</div>

	<div class="parts">
		<div class="title-container">
			<div class="table-title">资质信息</div>
			<c:if test="${traderSupplier.isEnable == 1 &&  traderSupplier.verifyStatus != 0 }">
				<!-- <div class="title-click  pop-new-data" layerParams='{"width":"1200px","height":"500px","title":"编辑","link":"./editAptitude.do?traderId=${traderSupplier.traderId}&traderSupplierId=${traderSupplier.traderSupplierId}"}'>编辑</div> -->
				<div class="title-click addtitle"
					 tabTitle='{"num":"supplier_edit","link":"./trader/supplier/editbaseinfo.do?traderSupplierId=${traderSupplier.traderSupplierId}","title":"编辑信息"}'>编辑</div>
			</c:if>
		</div>
		<% Date date=new Date();long now = date.getTime(); request.setAttribute("now", now); %>
		<table class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
			<tr>
				<td class="table-smallest">
					纳税人分类
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${traderSupplier.taxPayerType eq 1}">
							一般纳税人
						</c:when>
						<c:when test="${traderSupplier.taxPayerType eq 2}">
							小规模纳税人
						</c:when>
						<c:otherwise>
						</c:otherwise>
					</c:choose>
				</td>
			</tr>
			<tr>
				<td class="table-smallest">
					纳税人附件
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${taxPayerList ne null }">
							<c:forEach items="${taxPayerList}" var="taxPayer" varStatus="st">
								<c:if test="${taxPayer.uri ne null && not empty taxPayer.uri}">
									<a href="http://${taxPayer.domain}${taxPayer.uri}" target="_blank">
										纳税人附件
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							纳税人附件
							&nbsp;
						</c:otherwise>
					</c:choose>
				</td>
			</tr>
			<tr>
				<td class="table-smallest">
					<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
					营业执照
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${businessList ne null }">
							<c:forEach items="${businessList}" var="business" varStatus="st">
								<c:if test="${st.index == 0}">
									<c:set var="business_beginTime" value="${business.begintime}"></c:set>
									<c:set var="business_endTime" value="${business.endtime}"></c:set>
									<c:set var="business_isMedical" value="${business.isMedical}"></c:set>
									<c:set var="business_issueDate" value="${business.issueDate}"></c:set>
								</c:if>
								<c:if test="${business.uri ne null && not empty business.uri}">
									<a href="http://${business.domain}${business.uri}" target="_blank">
										营业执照
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							营业执照
							&nbsp;
						</c:otherwise>
					</c:choose>
					&nbsp;&nbsp;&nbsp;&nbsp;
					有效期： <date:date value ="${business_beginTime}" format="yyyy-MM-dd"/>
					<c:if test="${(business_endTime eq null || business_endTime eq 0)}">-无限期</c:if>
					<c:if test="${(business_endTime ne null && business_endTime ne 0)}">-<date:date value ="${business_endTime}" format="yyyy-MM-dd"/></c:if>&nbsp;&nbsp;&nbsp;&nbsp;
					<c:if test="${not empty business_endTime && business_endTime ne 0 && business_endTime lt now }"><span style="color: red">（已过期）</span></c:if>
					<c:if test="${business_isMedical eq 1}">含有医疗器械</c:if>
					&nbsp;&nbsp; 发证日期： <date:date value ="${business_issueDate}" format="yyyy-MM-dd"/>

				</td>
			</tr>
			<tr>
				<c:set var="medicalQualificationFlag" value="0"></c:set>
				<c:forEach items="${twoMedicalList}" var="twoMedical" >
					<c:if test="${twoMedical.medicalQualification eq 1}">
						<c:set var="medicalQualificationFlag" value="1"></c:set>
					</c:if>
				</c:forEach>
				<td class="table-smallest">
					<c:if test="${medicalQualificationFlag eq 0}">医疗器械二类备案凭证</c:if>
					<c:if test="${medicalQualificationFlag eq 1}">多证合一辅助证明</c:if>
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${twoMedicalList ne null }">
							<c:forEach items="${twoMedicalList}" var="twoMedical" varStatus="st">
								<c:if test="${st.index == 0}">
									<c:set var="beginTime" value="${twoMedical.begintime}"></c:set>
									<c:set var="endTime" value="${twoMedical.endtime}"></c:set>
									<c:set var="sn" value="${twoMedical.sn}"></c:set>
								</c:if>
								<c:if test="${twoMedical.uri ne null && not empty twoMedical.uri}">
									<a href="http://${twoMedical.domain}${twoMedical.uri}" target="_blank">
										<c:if test="${twoMedical.medicalQualification eq 0}">医疗器械二类备案凭证</c:if>
										<c:if test="${twoMedical.medicalQualification eq 1}">多证合一辅助证明</c:if>
											- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							<c:if test="${twoMedical.medicalQualification eq 0}">医疗器械二类备案凭证</c:if>
							<c:if test="${twoMedical.medicalQualification eq 1}">多证合一辅助证明</c:if>
							&nbsp;
						</c:otherwise>
					</c:choose>&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${beginTime} " format="yyyy-MM-dd"/>
					<c:if test="${twoMedicalList ne null and (endTime eq null || endTime eq 0) && not empty twoMedicalList}">-无限期</c:if>
					<c:if test="${(endTime ne null && endTime ne 0)}">
						-<date:date value ="${endTime} " format="yyyy-MM-dd"/>
					</c:if>&nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${sn}
					<c:if test="${endTime ne null && endTime ne 0 && endTime lt now }"><span style="color: red">（已过期）</span></c:if>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">医疗器械二类备案凭证详情（新国标）</td>
				<td style="text-align: left;">
					<c:forEach items="${newStandCategoryList}" var="newStandCategory">
						<c:if test="${not empty newTwo}">
							<c:forEach items="${newTwo}" var="mc">
								<c:if test="${newStandCategory.value eq mc.medicalCategoryId}">
									${newStandCategory.label} &nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:if>
					</c:forEach>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">医疗器械二类备案凭证详情（旧国标）</td>
				<td style="text-align: left;">
					<c:if test="${not empty two }">
						<c:forEach items="${two }" var="mc">
							${mc.title}&nbsp;&nbsp;
						</c:forEach>
					</c:if>
				</td>

			</tr>

			<tr>
				<td class="table-smallest">医疗器械经营许可证（三类）</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${threeMedicalList ne null }">
							<c:forEach items="${threeMedicalList}" var="threeMedical" varStatus="st">
								<c:if test="${threeMedical.uri ne null && not empty threeMedical.uri}">
									<a href="http://${threeMedical.domain}${threeMedical.uri}" target="_blank">
										医疗器械经营许可证（三类）
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							医疗器械经营许可证（三类）
							&nbsp;
						</c:otherwise>
					</c:choose>&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${threeMedical.begintime} " format="yyyy-MM-dd"/>
					<c:if test="${threeMedical ne null && (threeMedical.endtime eq null || threeMedical.endtime eq 0)}">-无限期</c:if>
					<c:if test="${(threeMedical.endtime ne null && threeMedical.endtime ne 0)}">
						-<date:date value ="${threeMedical.endtime} " format="yyyy-MM-dd"/>
					</c:if>&nbsp;&nbsp;&nbsp;&nbsp;许可证编号：${threeMedical.sn}
					<c:if test="${threeMedical.endtime ne null && threeMedical.endtime ne 0 && threeMedical.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">医疗器械经营许可证详情</br>（三类）（新国标）</td>
				<td style="text-align: left;">
					<c:forEach items="${newStandCategoryList}" var="newStandCategory">
						<c:if test="${not empty newThree}">
							<c:forEach items="${newThree}" var="mc">
								<c:if test="${newStandCategory.value eq mc.medicalCategoryId}">
									${newStandCategory.label} &nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:if>
					</c:forEach>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">医疗器械经营许可证详情</br>（三类）（旧国标）</td>
				<td style="text-align: left;">
					<c:if test="${not empty three }">
						<c:forEach items="${three }" var="mc">
							${mc.title}&nbsp;&nbsp;
						</c:forEach>
					</c:if>
				</td>
			</tr>
			<tr>
				<td class="table-smallest">医疗器械生产许可证</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${productList ne null }">
							<c:forEach items="${productList}" var="product" varStatus="st">
								<c:if test="${product.uri ne null && not empty product.uri}">
									<a href="http://${product.domain}${product.uri}" target="_blank">
										医疗器械生产许可证
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							医疗器械生产许可证
							&nbsp;
						</c:otherwise>
					</c:choose>
					&nbsp;&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${product.begintime}" format="yyyy-MM-dd"/>
					<c:if test="${product ne null && (product.endtime eq null || product.endtime eq 0)}">-无限期</c:if>
					<c:if test="${(product.endtime ne null && product.endtime ne 0)}">-<date:date value ="${product.endtime}" format="yyyy-MM-dd"/></c:if>
					<c:if test="${product.endtime ne null && product.endtime ne 0 && product.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
					&nbsp;&nbsp;&nbsp;&nbsp; 许可证编号：${product.sn}
					<c:if test="${endTime ne null && endTime ne 0 && endTime lt now }"><span style="color: red">（已过期）</span></c:if>
				</td>
				</td>
			</tr>
			<tr>
				<td class="table-smallest">第一类医疗器械生产备案凭证</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${firstCategoryCertificateList ne null }">
							<c:forEach items="${firstCategoryCertificateList}" var="firstCategoryCertificate" varStatus="st">
								<c:if test="${firstCategoryCertificate.uri ne null && not empty firstCategoryCertificate.uri}">
									<a href="http://${firstCategoryCertificate.domain}${firstCategoryCertificate.uri}" target="_blank">
										第一类医疗器械生产备案凭证
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							第一类医疗器械生产备案凭证
							&nbsp;
						</c:otherwise>
					</c:choose>&nbsp;&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${firstCategoryCertificate.begintime}" format="yyyy-MM-dd"/>
					<c:if test="${firstCategoryCertificate ne null && (firstCategoryCertificate.endtime eq null || firstCategoryCertificate.endtime eq 0)}">-无限期</c:if>
					<c:if test="${firstCategoryCertificate.endtime ne null && firstCategoryCertificate.endtime ne 0}">-<date:date value ="${firstCategoryCertificate.endtime}" format="yyyy-MM-dd"/></c:if>
					<c:if test="${firstCategoryCertificate.endtime ne null && firstCategoryCertificate.endtime ne 0 && firstCategoryCertificate.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
                    &nbsp;&nbsp;&nbsp;&nbsp; 备案号：${firstCategoryCertificate.recordNo}
				</td>
			</tr>

			<tr>
				<td class="table-smallest">生产企业生产产品登记表</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${productRegistrationList ne null }">
							<c:forEach items="${productRegistrationList}" var="productRegistration" varStatus="st">
								<c:if test="${productRegistration.uri ne null && not empty productRegistration.uri}">
									<a href="http://${productRegistration.domain}${productRegistration.uri}" target="_blank">
										生产企业生产产品登记表
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							生产企业生产产品登记表
							&nbsp;
						</c:otherwise>
					</c:choose>&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${productRegistration.begintime}" format="yyyy-MM-dd"/>
					<c:if test="${productRegistration ne null && (productRegistration.endtime eq null || productRegistration.endtime eq 0)}">-无限期</c:if>
					<c:if test="${(productRegistration.endtime ne null && productRegistration.endtime ne 0)}">-<date:date value ="${productRegistration.endtime}" format="yyyy-MM-dd"/></c:if>
					<c:if test="${productRegistration.endtime ne null && productRegistration.endtime ne 0 && productRegistration.endtime lt now }"><span style="color: red">（已过期）</span></c:if>

				</td>
			</tr>
			<%--<tr>
				<td class="table-smallest">医疗器械经营许可证</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${operate ne null && operate.uri ne null}">
							<a href="http://${operate.domain}${operate.uri}" target="_blank">医疗器械经营许可证</a>
						</c:when>
						<c:otherwise>
							医疗器械经营许可证
						</c:otherwise>
					</c:choose> &nbsp;&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${operate.begintime}" format="yyyy-MM-dd"/>
					<c:if test="${operate ne null && operate.endtime eq null}">-无限期</c:if>
					<c:if test="${operate.endtime ne null}">-<date:date value ="${operate.endtime}" format="yyyy-MM-dd"/></c:if>
					<c:if test="${operate.endtime ne null && operate.endtime ne 0 && operate.endtime lt now }"><span style="color: red">（已过期）</span></c:if>

				</td>
			</tr>--%>
			<!-- begin by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21 -->
			<tr>
				<td class="table-smallest">
					<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
					销售人员授权书
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${saleAuthList ne null }">
							<c:forEach items="${saleAuthList}" var="saleAuth" varStatus="st">
								<c:if test="${saleAuth.uri ne null && not empty saleAuth.uri}">
									<a href="http://${saleAuth.domain}${saleAuth.uri}" target="_blank">
										销售人员授权书
										- ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							销售人员授权书
							&nbsp;
						</c:otherwise>
					</c:choose>
					&nbsp;&nbsp;&nbsp;&nbsp;有效期： <date:date value ="${saleAuth.begintime}" format="yyyy-MM-dd"/>-<date:date value ="${saleAuth.endtime}" format="yyyy-MM-dd"/>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<c:if test="${saleAuth.endtime ne null && saleAuth.endtime ne 0 && saleAuth.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
					<c:if test="${saleHistoryFlag eq true}">
						<span class="title-click pop-new-data" style='float:none' layerParams='{"width":"600px","height":"300px","title":"销售人员授权书历史数据","link":"${pageContext.request.contextPath}/trader/supplier/viewHistoryAptitude.do?traderId=${traderSupplier.trader.traderId}&sysOptionDefinitionId=1100&name=销售人员授权书"}'>
														 查看历史数据
						</span>
					</c:if>
				</td>
			</tr>
			<%--<tr>
				<td class="table-smallest">授权销售人信息</td>
				<td style="text-align: left;">
					职位:&nbsp;<span>${saleAuth.authPost}</span>&nbsp;&nbsp;, 姓名:&nbsp;<span>${saleAuth.authUserName}</span>&nbsp;&nbsp;, 联系方式:&nbsp;<span>${saleAuth.authContactInfo}</span>
				</td>
			</tr>
			<!-- end by franlin for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21 -->

			<tr>
				<td class="table-smallest">品牌授权书 </td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${brandBookList ne null }">
							<c:forEach items="${brandBookList }" var="brandBook" varStatus="st">
								<c:if test="${st.index == 0}">
									<c:set var="brandBeginTime" value="${brandBook.begintime}"></c:set>
									<c:set var="brandEndTime" value="${brandBook.endtime}"></c:set>
								</c:if>
								<c:if test="${brandBook.uri ne null && not empty brandBook.uri}">
									<a href="http://${brandBook.domain}${brandBook.uri}" target="_blank">品牌授权书 - ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							品牌授权书&nbsp;
						</c:otherwise>
					</c:choose>&nbsp;&nbsp;&nbsp;
					有效期：<date:date value ="${brandBeginTime} " format="yyyy-MM-dd"/>
					<c:if test="${brandBookList ne null and brandEndTime eq null and not empty brandBookList}">-无限期</c:if>
					<c:if test="${brandEndTime ne null}">
						-<date:date value ="${brandEndTime} " format="yyyy-MM-dd"/>
					</c:if>&nbsp;
					<c:if test="${brandEndTime ne null && brandEndTime ne 0 && brandEndTime lt now }"><span style="color: red">（已过期）</span></c:if>
				</td>
			</tr>--%>

			<tr>
				<td class="table-smallest">
					<img src="${pageContext.request.contextPath}/static/images/risk_blue.png" width="18px" height="18px">
					随货同行单模板
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${goodWithTemList ne null }">
							<c:forEach items="${goodWithTemList}" var="goodWithTem" varStatus="st">
								<c:if test="${st.index == 0}">
									<c:set var="goodWithTemSysId" value="${goodWithTem.sysOptionDefinitionId}"></c:set>
								</c:if>
								<c:if test="${goodWithTem.uri ne null && not empty goodWithTem.uri}">
									<a href="http://${goodWithTem.domain}${goodWithTem.uri}" target="_blank">随货同行单 - ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							随货同行单模板
						</c:otherwise>
					</c:choose>
					<c:if test="${goodWithTemHistoryFlag eq true}">
						<span class="title-click pop-new-data" style='float:none' layerParams='{"width":"600px","height":"300px","title":"随货同行单模板历史数据","link":"${pageContext.request.contextPath}/trader/supplier/viewHistoryAptitude.do?traderId=${traderSupplier.trader.traderId}&sysOptionDefinitionId=896&name=随货同行单"}'>
														 查看历史数据
							</span>
					</c:if>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">
					质量保证协议
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${qualityAssuranceList ne null }">
							<c:forEach items="${qualityAssuranceList}" var="qualityAssurance" varStatus="st">
								<c:if test="${st.index == 0}">
									<c:set var="qualitySysId" value="${qualityAssurance.sysOptionDefinitionId}"></c:set>
								</c:if>
								<c:if test="${qualityAssurance.uri ne null && not empty qualityAssurance.uri}">
									<a href="http://${qualityAssurance.domain}${qualityAssurance.uri}" target="_blank">质量保证协议 - ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							质量保证协议
						</c:otherwise>
					</c:choose>
					有效期：<date:date value ="${qualityAssurance.begintime}" format="yyyy-MM-dd"/>
					<c:if test="${qualityAssurance ne null && (qualityAssurance.endtime eq null || qualityAssurance.endtime eq 0)}">-无限期</c:if>
					<c:if test="${(qualityAssurance.endtime ne null && qualityAssurance.endtime ne 0)}">-<date:date value ="${qualityAssurance.endtime}" format="yyyy-MM-dd"/></c:if>
					<c:if test="${qualityAssurance.endtime ne null && qualityAssurance.endtime ne 0 && qualityAssurance.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<c:if test="${qualityHistoryFlag eq true}">
						<span class="title-click pop-new-data" style='float:none' layerParams='{"width":"600px","height":"300px","title":"质量保证协议历史数据","link":"${pageContext.request.contextPath}/trader/supplier/viewHistoryAptitude.do?traderId=${traderSupplier.trader.traderId}&sysOptionDefinitionId=897&name=质量保证协议"}'>
														 查看历史数据
						</span>
					</c:if>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">
					售后服务承诺书
				</td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${afterSalesBookList ne null }">
							<c:forEach items="${afterSalesBookList}" var="afterSalesBook" varStatus="st">
								<c:if test="${st.index == 0}">
									<c:set var="afterSalesBookSysId" value="${afterSalesBook.sysOptionDefinitionId}"></c:set>
								</c:if>
								<c:if test="${afterSalesBook.uri ne null && not empty afterSalesBook.uri}">
									<a href="http://${afterSalesBook.domain}${afterSalesBook.uri}" target="_blank">售后服务承诺书 - ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							售后服务承诺书
						</c:otherwise>
					</c:choose>
					有效期：<date:date value ="${afterSalesBook.begintime}" format="yyyy-MM-dd"/>
					<c:if test="${afterSalesBook ne null && afterSalesBook.begintime ne null && (afterSalesBook.endtime eq null || afterSalesBook.endtime eq 0)}">-无限期</c:if>
					<c:if test="${(afterSalesBook.endtime ne null && afterSalesBook.endtime ne 0)}">-<date:date value ="${afterSalesBook.endtime}" format="yyyy-MM-dd"/></c:if>
					<c:if test="${afterSalesBook.endtime ne null && afterSalesBook.endtime ne 0 && afterSalesBook.endtime lt now }"><span style="color: red">（已过期）</span></c:if>
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<c:if test="${afterSalesHistoryFlag eq true}">
						<span class="title-click pop-new-data" style='float:none' layerParams='{"width":"600px","height":"300px","title":"售后服务承诺书历史数据","link":"${pageContext.request.contextPath}/trader/supplier/viewHistoryAptitude.do?traderId=${traderSupplier.trader.traderId}&sysOptionDefinitionId=898&name=售后服务承诺书"}'>
														 查看历史数据
						</span>
					</c:if>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">质量体系调查表或合格供应商档案 </td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${qualityAndTraderList ne null }">
							<c:forEach items="${qualityAndTraderList}" var="qualityAndTrader" varStatus="st">
								<c:if test="${qualityAndTrader.uri ne null && not empty qualityAndTrader.uri}">
									<a href="http://${qualityAndTrader.domain}${qualityAndTrader.uri}" target="_blank">质量体系调查表或合格供应商档案 - ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							质量体系调查表或合格供应商档案
						</c:otherwise>
					</c:choose>
				</td>
			</tr>

			<tr>
				<td class="table-smallest">其他 </td>
				<td style="text-align: left;">
					<c:choose>
						<c:when test="${otherList ne null }">
							<c:forEach items="${otherList }" var="other" varStatus="st">
								<c:if test="${other.uri ne null && not empty other.uri}">
									<a href="http://${other.domain}${other.uri}" target="_blank">其他资格证书 - ${st.index + 1}</a>&nbsp;&nbsp;
								</c:if>
							</c:forEach>
						</c:when>
						<c:otherwise>
							其他资格证书&nbsp;
						</c:otherwise>
					</c:choose>
				</td>
			</tr>

			</tbody>
		</table>
	</div>
	<iframe class="j-iframe"  src="/trader/supplier/getContactsAddress.do?traderId=${traderSupplier.trader.traderId }&traderSupplierId=${traderSupplier.traderSupplierId}" frameborder="0" style="width: 100%;border: 0; " onload="setIframeHeight(this)" scrolling="no">
	</iframe>
    <div class="tcenter mb15 mt-5">
        <input type="hidden" name="formToken" value="${formToken}"/>
        <c:choose>
            <c:when test="${traderSupplier.verifyStatus == null || traderSupplier.verifyStatus != 1}">
                <c:if test="${belongSupplyOrg and ((null==taskInfo and null==taskInfo.getProcessInstanceId())or (null!=taskInfo and taskInfo.assignee==null and empty candidateUserMap[taskInfo.id]))}">
                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidSupplier(${traderSupplier.traderSupplierId},${taskInfo.id == null ?0: taskInfo.id})">申请审核</button>
                </c:if>
                <c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
                    <c:choose>
                        <c:when test="${taskInfo.assignee == curr_user.username or candidateUserMap['belong']}">
                            <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=true&type=1&traderSupplierId=${traderSupplier.traderSupplierId}"}'>审核通过</button>
                            <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfo.id}&pass=false&type=1"}'>审核不通过</button>
                        </c:when>
                        <c:otherwise>
                            <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
                        </c:otherwise>
                    </c:choose>
                </c:if>
            </c:when>
        </c:choose>
    </div>

     <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">
                    审核记录
                </div>
            </div>
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                    <tr>
                        <td>操作人</td>
                        <td>操作时间</td>
                        <td>操作事项</td>
						<td>审核状态</td>
                        <td>备注</td>
                    </tr>
                </thead>
                <tbody>
                    <c:if test="${null!=historicActivityInstance}">
                    <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                    <c:if test="${not empty  hi.activityName}">
                    <tr>
                    	<td>
                    	<c:choose>
							<c:when test="${hi.activityType == 'startEvent'}">
							${startUser}
							</c:when>
							<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
							</c:when>
							<c:otherwise>
								<c:if test="${historicActivityInstance.size() == status.count}">
									${verifyUsers}
								</c:if>
								<c:if test="${historicActivityInstance.size() != status.count}">
									${hi.assignee}
								</c:if>
							</c:otherwise>
						</c:choose>
                    	</td>
                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                        <td>
                        <c:choose>
							<c:when test="${hi.activityType == 'startEvent' && status.count ==1 }">
							开始
							</c:when>
							<c:when test="${hi.activityType == 'startEvent' && status.count > 1 }">
								修改
							</c:when>
							<c:when test="${hi.activityType == 'intermediateThrowEvent'}">
							结束
							</c:when>
							<c:otherwise>
							${hi.activityName}
							</c:otherwise>
						</c:choose>
						</td>
						<td>
							<c:choose>
								<c:when test="${hi.activityName eq '审核完成'}">
									审核通过
								</c:when>
								<c:when test="${hi.activityName eq '驳回'}">
									审核不通过
								</c:when>
							<c:otherwise>
								审核中
							</c:otherwise>
							</c:choose>
						</td>
                        <td class="font-red">${commentMap[hi.taskId]}</td>
                    </tr>
                    </c:if>
                    </c:forEach>
                    </c:if>
                    <c:if test="${empty historicActivityInstance}">
		       			<!-- 查询无结果弹出 -->
		       			<tr>
		       				<td colspan="4">暂无审核记录。</td>
		       			</tr>
		        	</c:if>
                </tbody>
            </table>

        	<div class="clear"></div>
        </div>

	
</div>
<%@ include file="../../common/footer.jsp"%>
