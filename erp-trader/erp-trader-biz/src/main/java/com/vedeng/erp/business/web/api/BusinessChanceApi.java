package com.vedeng.erp.business.web.api;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.admin.api.dto.customer.TradeInfoDto;
import com.vedeng.erp.business.domain.dto.*;
import com.vedeng.erp.business.dto.CloseAuditDto;
import com.vedeng.erp.business.feign.CrmTraderApiService;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.InitializationService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.system.common.annotation.CustomDataOperAnnotation;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperTypeEnums;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商机数据api接口
 * @date 2022/7/12 10:39
 **/
@ExceptionController
@RestController
@RequestMapping("/businessChance")
@Slf4j
public class BusinessChanceApi {

    @Autowired
    private BusinessChanceService businessChanceService;

    @Resource
    private CrmTraderApiService crmTraderApiService;

    @Autowired
    private InitializationService initializationService;

    /**
     * 系统测算 成单机率 系统测算 商机等级 完整度
     *
     * @param businessChanceData 入参对象
     * @return 系统测算
     */
    @RequestMapping(value = "/calc", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> calc(@RequestBody BusinessChanceDto businessChanceData) {
        ValidatorUtils.validate(businessChanceData, DefaultGroup.class);
        businessChanceService.calc(businessChanceData);
        return R.success(businessChanceData);
    }

    /**
     * 保存商机接口
     * @param businessChanceDto 商机接口
     * @return R
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> add(@RequestBody BusinessChanceDto businessChanceDto) {
        BusinessChanceDto data = businessChanceService.add(businessChanceDto);
        return R.success(data);
    }

    @RequestMapping(value = "/smartQuoteAdd", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> smartQuoteAdd(@RequestBody SmartQuoteDto smartQuoteDto) {
        SmartQuoteResultDto data = businessChanceService.smartQuoteAdd(smartQuoteDto);
        if (data.getCanPdf()) {
            String quotePdf = businessChanceService.getQuotePdf(data.getQuoteOrderId(), data.getQuoteorderNo());
            data.setUrl(quotePdf);
        }

        return R.success(data);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> update(@RequestBody BusinessChanceDto businessChanceDto) {
        BusinessChanceDto update = businessChanceService.update(businessChanceDto);
        return R.success(update);
    }

    /**
     * 更改商机主管指导意见
     * 形参： businessChanceDto
     * 返回值：R
     */
    @RequestMapping(value = "/updateSupervisorGuidance", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<?> updateSupervisorGuidance(@RequestBody BusinessChanceDto businessChanceDto) {
        businessChanceService.updateSupervisorGuidance(businessChanceDto);
        return R.success();
    }

    /**
     * 编辑页查询商机信息
     * @param businessChanceDto 商机对象
     * @return 商机信息
     */
    @RequestMapping(value = "/selectOne", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> selectOne(BusinessChanceDto businessChanceDto) {
        ValidatorUtils.validate(businessChanceDto, DefaultGroup.class);
        BusinessChanceDto result = businessChanceService.selectOne(businessChanceDto);
        return R.success(result);
    }

    /**
     * 分页查询接口
     * @param businessChanceQueryDto 查询对象
     * @return R
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<?> page(@RequestBody PageParam<BusinessChanceDto> businessChanceQueryDto) {
        return R.success(businessChanceService.page(businessChanceQueryDto));
    }

    /**
     * 置顶
     * @param id 商机id
     * @return R
     */
    @RequestMapping(value = "/top", method = RequestMethod.POST)
    @CustomDataOperAnnotation(operBizType = CustomDataOperBizTypeEnums.BUSINESS_CHANCE,
            dataOperType = CustomDataOperTypeEnums.TOP)
    public R<?> top(@RequestParam Integer id) {
        return R.success();
    }

    /**
     * 取消置顶
     * @param id 商机id
     * @return R
     */
    @RequestMapping(value = "/unTop", method = RequestMethod.POST)
    @CustomDataOperAnnotation(operBizType = CustomDataOperBizTypeEnums.BUSINESS_CHANCE,
            dataOperType = CustomDataOperTypeEnums.UN_TOP)
    public R<?> unTop(@RequestParam Integer id) {
        return R.success();
    }

    /**
     * 关注
     * @param id 商机id
     * @return R
     */
    @RequestMapping(value = "/attention", method = RequestMethod.POST)
    @NoRepeatSubmit
    public R<?> attention(@RequestBody List<Integer> id) {
        log.info("操作关注商机：入参:{}", JSON.toJSON(id));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        boolean flag = businessChanceService.attention(id,currentUser);
        if (!flag) {
            return R.error("已成单、已关闭无法关注");
        }
        return R.success();
    }

    /**
     * 取消关注
     * @param id 商机id
     * @return R
     */
    @RequestMapping(value = "/cancelAttention", method = RequestMethod.POST)
    @NoRepeatSubmit
    public R<?> cancelAttention(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        boolean flag = businessChanceService.cancelAttention(id,currentUser);
        if (!flag) {
            return R.error("已关闭、已成单、已支持无法取消关注");
        }
        return R.success();
    }

    /**
     * 获取商机精准度枚举类数组
     */
    @RequestMapping(value = "/getAccuracyEnum", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<?> getAccuracyEnum() {
        return R.success(businessChanceService.getAccuracyEnum());
    }

    /**
     * 初始化接口
     * @param file 文件
     * @return R
     * @throws IOException
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public R<?> importExcel(@RequestPart("file") MultipartFile file) throws IOException {
        initializationService.importBusinessChance(file);
        return R.success();
    }

    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getDetail(@RequestParam Integer businessChanceId) {
        return R.success(businessChanceService.viewDetail(businessChanceId));
    }

    /**
     * 查询审核流中关闭审核信息
     * @param businessChanceId businessChanceId
     * @return R
     */
    @RequestMapping(value = "/getCloseVerifyInfo", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getCloseVerifyInfo(@RequestParam Integer businessChanceId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(businessChanceService.getCloseVerifyInfo(businessChanceId,currentUser));
    }

    /**
     * 获取合并商机信息
     * @param businessChanceNo 商机号
     * @return R
     */
    @RequestMapping(value = "/getMergeChance", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getOldBusinessChance(@RequestParam String businessChanceNo) {
        return R.success(businessChanceService.getChancesByNoAfterMerged(businessChanceNo));
    }

    /**
     * crm 信息
     * @param traderId 客户id
     * @return
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getBidInfo", method = RequestMethod.POST)
    public R<?> getBidInfoByTraderId(@RequestParam Integer traderId) {
        try {
            RestfulResult<List<BusinessCluesDetailResultDTO>> bidInfo = businessChanceService.getBidInfoByTraderId(traderId);
            return R.success(bidInfo);
        } catch (Exception e) {
           log.error("商机详情调用CRM查询中标信息失败：{}", traderId);
        }
        return R.success();
    }

    /**
     * 获取客户信息
     * @param traderId 客户id
     * @return R
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getTraderInfoByTraderId", method = RequestMethod.POST)
    public R<?> getTraderInfoByTraderId(@RequestParam Integer traderId) {
        try {
            RestfulResult<TradeInfoDto> traderInfo = crmTraderApiService.getTraderInfoByTraderId(traderId);
            return R.success(traderInfo);
        } catch (Exception e) {
            log.error("商机详情调用CRM查询客户交易信息失败：{}", traderId);
        }
        return R.success();
    }

    /**
     * 更新商机状态 未处理的 有沟通记录则改为处理中
     * @param id 商机
     * @return R
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/updateBusinessChanceStatusByComm", method = RequestMethod.POST)
    public R<?> updateBusinessChanceStatusByComm(@RequestParam Integer id) {
        businessChanceService.updateBusinessChanceStatusByCommunication(id);
        return R.success();
    }

    /**
     * 列表页更新单条数据
     * @param businessChanceDto 商机对象
     * @return R
     */
    @RequestMapping(value = "updateSingleData", method = RequestMethod.POST)
    public R<?> updateSingleData(@RequestBody BusinessChanceDto businessChanceDto) {
        businessChanceService.updateSingleData(businessChanceDto);
        return R.success();
    }

    /**
     * 分享商机给销售人员
     * @param rSalesJBusinessOrderDto 商机对象
     * @return R
     */
    @RequestMapping(value = "shareBusinessChance", method = RequestMethod.POST)
    @NoRepeatSubmit
    @ExcludeAuthorization
    public R<?> shareBusinessChance(@RequestBody RSalesJBusinessOrderDto rSalesJBusinessOrderDto) {
        businessChanceService.shareBusinessChance(rSalesJBusinessOrderDto);
        return R.success();
    }

    /**
     * 商机关闭审核
     * @param closeAuditDto
     * @return
     */
    @RequestMapping(value = "closeBusinessAudit", method = RequestMethod.POST)
    @NoRepeatSubmit
    @ExcludeAuthorization
    public R<?> closeBusinessAudit(@RequestBody CloseAuditDto closeAuditDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        closeAuditDto.setCurrentUser(currentUser.getUsername());
        businessChanceService.closeBusinessAudit(closeAuditDto);
        if (!closeAuditDto.getResult()){
            return R.error("审核失败");
        }
        return R.success();
    }

    /**
     * 商机分享记录
     * @return R
     */
    @RequestMapping(value = "getShareBusinessChance", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<List<RSalesJBusinessOrderDto>> getShareBusinessChance(@RequestParam Integer bussinessChanceId) {
        return R.success(businessChanceService.getShareBusinessChance(bussinessChanceId));
    }

    /**
     * 商机取消分享
     * @return R
     */
    @RequestMapping(value = "cancelShareBusinessChance", method = RequestMethod.POST)
    @NoRepeatSubmit
    @ExcludeAuthorization
    public R<?> cancelShareBusinessChance(@RequestParam Integer id) {
        businessChanceService.cancelShareBusinessChance(id);
        return R.success();
    }

    /**
     * 商机支持工作台
     */
//    @RequestMapping(value = "getBusinessSupportWorkbench", method = RequestMethod.POST)
//    @ExcludeAuthorization
//    public R<?> getBusinessSupportWorkbench(HttpServletRequest request,@RequestBody BusinessSupportWorkbenchRequestDto requestDto) {
//        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
//        return R.success(businessChanceService.getBusinessSupportWorkbench(user,requestDto));
//    }

    /**
     * 咨询待处理列表
     */
    @RequestMapping(value = "getConsultationPendingInfo", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<PageInfo<ConsultationPendingResponseDto>> getConsultationPendingInfo(@RequestBody PageParam<ConsultationPendingRequestDto> pageParam) {
        return R.success(businessChanceService.getConsultationPendingInfo(pageParam));
    }

    /**
     * 处理或关闭咨询记录
     */
    @RequestMapping(value = "/processOrCloseSeekHelp", method = RequestMethod.POST)
    @ExcludeAuthorization
    public R<?> processOrCloseSeekHelp(@RequestBody ProcessOrCloseSeekHelpDto requestDto) {
        businessChanceService.processOrCloseSeekHelp(requestDto);
        return R.success();
    }
}
