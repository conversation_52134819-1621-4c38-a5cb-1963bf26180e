.ui-global-toast {
  display: flex;
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
  top: 110px;
  padding: 12px 20px 12px 15px;
  background-color: #fff;
  border-radius: 5px;
  z-index: 99999;
  box-shadow: rgba(0, 0, 0, 0.08) 0px 7px 14px;
  align-items: center;
}
.ui-global-toast.ui-message-success {
  background: #E3F7E3;
}
.ui-global-toast.ui-message-error {
  background: #FCE9E9;
}
.ui-global-toast.ui-message-warn {
  background: #FFEDE0;
}
.ui-global-toast.ui-message-info {
  background: #E0F3FF;
}
.ui-global-toast i {
  position: relative;
  top: 1px;
  font-size: 20px;
  line-height: 1;
}
.ui-global-toast .icon-yes2 {
  color: #13BF13;
}
.ui-global-toast .icon-error2 {
  color: #E64545;
}
.ui-global-toast .icon-info2 {
  color: #09f;
}
.ui-global-toast .icon-caution2 {
  color: #f60;
}
.ui-global-toast span {
  max-width: 570px;
  margin-left: 10px;
}
.globalToast-enter-active {
  transition: 220ms ease-out;
}
.globalToast-leave-active {
  transition: 190ms ease-in;
}
.globalToast-enter,
.globalToast-leave-to {
  opacity: 0;
  transform: translate(-50%, -30px);
}
.popup-fade-enter-active {
  transition: all 0.15s ease-out;
}
.popup-fade-leave-active {
  transition: all 0.15s ease-in;
}
.popup-fade-enter,
.popup-fade-leave-to {
  opacity: 0;
}
.popup-move-enter-active {
  transition: all 0.15s ease-out;
}
.popup-move-leave-active {
  transition: all 0.15s ease-in;
}
.popup-move-enter,
.popup-move-leave-to {
  opacity: 0;
  transform: translate3d(0, -30px, 0);
}
.ui-popup-message-box-wrapper {
  position: fixed;
  z-index: 2019;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  text-align: center;
}
.ui-popup-message-box-wrapper::after {
  content: "";
  display: inline-block;
  height: 100%;
  width: 0;
  vertical-align: middle;
}
.ui-popup-message-box {
  display: inline-block;
  width: 480px;
  min-height: 128px;
  background-color: #FFFFFF;
  border-radius: 5px;
  vertical-align: middle;
  text-align: left;
}
.ui-popup-message-box .msg-title {
  display: flex;
  justify-content: flex-end;
  height: 30px;
}
.ui-popup-message-box .msg-fork {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  cursor: pointer;
}
.ui-popup-message-box .msg-fork:hover {
  color: #333333;
}
.ui-popup-message-box .msg-fork:hover .icon-delete {
  color: #333333;
}
.ui-popup-message-box .msg-fork .icon-delete {
  font-size: 24px;
  color: #CCCCCC;
}
.ui-popup-message-box .msg-fork .icon-delete:hover {
  color: #333333;
}
.ui-popup-message-box .msg-content {
  display: flex;
  padding: 0 20px;
}
.ui-popup-message-box .msg-content .vd-ui_icon {
  font-size: 32px;
  line-height: 1;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-info2 {
  color: #0099FF;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-yes2 {
  color: #13bf13;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-caution2 {
  color: #ff6600;
}
.ui-popup-message-box .msg-content .vd-ui_icon.icon-error2 {
  color: #e64545;
}
.ui-popup-message-box .msg-content .msg-tip-title {
  width: 398px;
  padding-top: 2px;
  padding-left: 10px;
  padding-right: 20px;
  font-size: 18px;
  font-weight: 700;
}
.ui-popup-message-box .msg-content .msg-tip-word {
  padding-top: 5px;
  padding-left: 10px;
  word-break: break-all;
}
.ui-popup-message-box .msg-button-choice {
  display: flex;
  justify-content: flex-end;
  padding-right: 20px;
  padding-top: 20px;
  padding-bottom: 20px;
}
.ui-popup-message-box .msg-button-choice .vd-button {
  border: 1px solid #BABFC2;
  cursor: pointer;
  border-radius: 3px;
  padding: 5px 14px;
  background-color: #F5F7FA;
  margin-left: 10px;
}
.ui-popup-message-box .msg-button-choice .vd-button:hover {
  background-color: #EDF0F2;
}
.ui-popup-message-box .msg-button-choice .vd-button.confirm {
  border-color: #09f;
  background-color: #09f;
  color: #fff;
}
.ui-popup-message-box .msg-button-choice .vd-button.confirm:hover {
  border-color: #008ae5;
  background-color: #008ae5;
}
.ui-popup-message-box .msg-button-choice .vd-button.cannel {
  border: 1px solid #CED2D9;
}
.ui-popup-message-box .msg-button-choice .vd-button.cannel:hover {
  border: solid 1px #B6BABF;
}
.ui-popup-message-box .msg-button-choice .vd-button.delete {
  border-color: #E64545;
  background-color: #E64545;
  color: #fff;
}
.ui-popup-message-box .msg-button-choice .vd-button.delete:hover {
  border-color: #cc2929;
  background-color: #cc2929;
}
