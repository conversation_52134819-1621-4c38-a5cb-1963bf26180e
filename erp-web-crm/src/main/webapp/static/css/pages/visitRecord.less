.visit-detail-wrap {
    position: relative;
    padding-top: 60px;
    padding-right: 360px;

    .visible-xs {
        display: none;
    }

    .chanceDetail-header-wrap {
        width: 100%;
        min-width: 760px;
        height: 60px;
        background: #fff;
        border-bottom: solid 1px #E1E5E8;
        position: fixed;
        top: 50px;
        z-index: 9;

        .header-main {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;

            .header-content {
                flex: 1;
                min-width: 0;
                display: flex;
                justify-content: space-between;
                align-items: center;

                .header-left {
                    display: flex;
                    align-items: center;
                    padding: 0 15px;

                    .title {
                        font-size: 20px;
                        font-weight: 700;
                        color: #333333;
                    }

                    .status {
                        height: 27px;
                        line-height: 27px;
                        padding: 0 10px;
                        border-radius: 3px;
                        color: #FF6600;
                        background-color: rgba(255, 102, 0, 0.1);
                        margin-left: 15px;
                        font-size: 14px;
                        margin-top: 1px;

                        &.status1 {
                            background: #ffede1;
                            color: #F60;
                        }
                        &.status2 {
                            background: #e1f3ff;
                            color: #09F;
                        }
                        &.status3 {
                            background: #E3F7E3;
                            color: #13BF13;
                        }
                        &.status4 {
                            background: #E3EAF0;
                            color: #1A4D80;
                        }
                    }
                }
                .header-right {
                    display: flex;
                    align-items: center;
                    padding-right: 10px;

                    .vd-ui-button {
                        margin-right: 10px;
                    }
                }
            }

            .header-aside-wrap {

                // 大屏头
                .header-md-aside {
                    position: relative;
                    width: 360px;
                    display: flex;

                    &::before {
                        content: "";
                        height: 72px;
                        width: 1px;
                        background: #E1E5E8;
                        position: absolute;
                        left: 0;
                    }
                }

                .vd-ui-title-tip-wrap {
                    flex: 1;
                }

                .header-xs-aside {
                    display: none;
                }

                .h-a-item {
                    height: 60px;
                    position: relative;
                    flex: 1;
                    background-size: 24px 24px;
                    background-position: center;
                    background-repeat: no-repeat;
                    cursor: pointer;

                    &::before {
                        position: absolute;
                        width: 100%;
                        height: 100%;
                        left: 0;
                        top: 0;
                        content: "";
                        z-index: 1;
                        background-size: 24px 24px;
                        background-position: center;
                        background-repeat: no-repeat;
                        opacity: 0;
                    }

                    &.msg {
                        background-image: url('../../image/icon/msg.svg');

                        &::before {
                            background-image: url('../../image/icon/msg-active.svg');
                        }
                    }
                    &.time {
                        background-image: url('../../image/icon/time.svg');

                        &::before {
                            background-image: url('../../image/icon/time-active.svg');
                        }
                    }
                    &.record {
                        background-image: url('../../image/icon/record.svg');

                        &::before {
                            background-image: url('../../image/icon/record-active.svg');
                        }
                    }
                    &.user {
                        background-image: url('../../image/icon/user.svg');

                        &::before {
                            background-image: url('../../image/icon/user-active.svg');
                        }
                    }

                    

                    &.active, &:hover {
                        background-image: none;

                        &::before {
                            opacity: 1;
                        }   
                    }

                    &.active {
                        &::after {
                            content: "";
                            display: block;
                            width: 30px;
                            height: 2px;
                            background: #09f;
                            position: absolute;
                            bottom: 0;
                            left: 50%;
                            transform: translateX(-50%);
                        }
                    }
                }
            }
        }
    }

    .deatil-page-top-bg {
        height: 150px;
        background: linear-gradient(to bottom, #ddeefa 0%, #f5f7fa 100%);
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        z-index: 1;

        &.finish {
            background: linear-gradient(to bottom, #e0f2e4 0%, #f5f7fa 100%);
        }
        
        &.close {
            background: linear-gradient(to bottom, #E3EAF0 0%, #f5f7fa 100%);
        }

        &.wait {
            background: linear-gradient(to bottom, #ffede1 0%, #f5f7fa 100%);
        }
    }

    .detail-page-wrap {
        padding: 0 20px 20px;
        position: relative;

        .chance-step {
            height: 90px;
            max-width: 1240px;
            min-width: 960px;
            margin: 0 auto;
            position: relative;
            z-index: 2;
        }

        .main {
            position: relative;
            z-index: 2;

            .show-font {
                margin-top: 10px;
                &:first-child {
                    margin-top: 0;
                }
            }
        }

        .right-aside {
            width: 360px;
            background: #fff;
            position: fixed;
            right: 0;
            top: 50px;
            z-index: 3;
            min-height: 500px;
            bottom: 0;
            padding-top: 60px;

            .right-aside-inner {
                height: 100%;
                border-left: solid 1px #E1E5E8;
            }
        }

        .other-contant-gap {
            margin-bottom: 5px;
        }

        .trader-detail {
            display: flex;
            align-items: center;
            color: #999;
            margin-top: 5px;
            flex-wrap: wrap;

            .detail-item {
                margin-right: 21px;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    width: 1px;
                    height: 12px;
                    top: 3px;
                    right: -10px;
                    background: #e1e5e8;
                }

                &:last-child {
                    margin-right: 0;

                    &::before {
                        display: none;
                    }
                }
            }
        }
    }

    .card-top-tip {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;
        color: #f60;

        .icon-caution1 {
            line-height: 1;
            font-size: 16px;
            margin-right: 5px;
        }
    }

    .card-top-option {
        position: absolute;
        top: 20px;
        right: 20px;
        display: flex;
        align-items: center;

        .option-item {
            color: #09f;
            cursor: pointer;
            margin-left: 10px;
            display: flex;
            align-items: center;

            .icon-add {
                font-size: 16px;
                line-height: 1;
                margin-right: 5px;
            }

            &:hover {
                color: #f60;
            }
        }
    }

    .no-padding {
        margin: 0 -20px -20px;
    }

    .map-address-wrap {
        display: flex;
        align-items: flex-start;

        .ui-view-map-wrap {
            margin-top: -3px;
            margin-left: 10px;
        }
    }

    .user-label-wrap {
        padding: 5px;
        background: #F5F7FA;
        border-radius: 3px;
        display: inline-block;
    }

    .user-label-list {
        margin-bottom: -10px;
        display: flex;
        flex-wrap: wrap;

        .user-label-wrap {
            margin-right: 10px;
            margin-bottom: 10px;
        }
    }

    .pic-list-tip {
        color: #999;
        margin-top: 5px;
    }
}

.visit-status-tag {
    line-height: 22px;
    padding: 0 5px;
    border-radius: 2px;
    display: inline-block;

    &.status-1 {
        background: #FFEDE0;
        color: #FF6600;
    }

    &.status-2 {
        background: #E0F3FF;
        color: #0099FF;
    }

    &.status-3 {
        background: #E3F7E3;
        color: #13BF13;
    }

    &.status-4 {
        background: #E3EAF0;
        color: #1A4D80;
    }
}

.vd-ui-td {
    .visit-status-tag {
        margin: -2px 0;
    }

    .visit-address-wrap {
        display: flex;
        align-items: center;

        .visit-address-txt {
            flex: 1;
            margin-right: 10px;
        }
    }
}

.trader-tip-info {
    display: flex;
    align-items: center;
    color: #999;
    margin-top: 5px;

    .trader-info-item {
        margin-right: 21px;
        position: relative;

        &::before {
            content: "";
            width: 1px;
            height: 12px;
            position: absolute;
            right: -10px;
            top: 3px;
            background: #e1e5e8;
        }

        &:last-child {
            margin-right: 0;

            &::before {
                display: none;
            }
        }
    }
}

.long-form-footer {
    padding-left: 210px;
}

.visit-form-wrap {
    .more-contact-component {
        .add-contact {
            padding-left: 210px;
        }

        .vd-ui-input {
            width: 236px !important;
        }

        .contact-list .contact-item .delete {
            left: 446px;
        }
    }

    .vd-ui-select {
        width: 100%;
    }

    .visit-form-trader {
        .trader-name {
            display: flex;
        }

        .trader-detail {
            display: flex;
            align-items: center;
            color: #999;
            margin-top: 5px;

            .detail-item {
                margin-right: 21px;
                position: relative;

                &::before {
                    content: '';
                    position: absolute;
                    width: 1px;
                    height: 12px;
                    top: 3px;
                    right: -10px;
                    background: #e1e5e8;
                }

                &:last-child {
                    margin-right: 0;

                    &::before {
                        display: none;
                    }
                }
            }
        }
    }
}

.visitor-form-container {
    .business-info-wrap {
        color: #999;
        margin-top: 5px;

        .info-item {
            margin-top: 0;
        }

        .business-info-tip {
            display: flex;
            color: #f60;
            margin-top: 5px;

            .icon-caution1 {
                font-size: 16px;
                line-height: 1;
                margin-right: 5px;
                margin-top: 1px;
            }

            .tip-txt {
                flex: 1;
            }
        }
    }

    .map-address-wrap {
        display: flex;
        align-items: flex-start;

        .ui-view-map-wrap {
            margin-left: 10px;
            
            .ui-view-map-btn {
                line-height: 30px;
            }
        }
    }
}

.form-status-tip {
    display: flex;
    align-items: center;
    margin-top: 5px;

    .vd-ui_icon {
        font-size: 16px;
        margin-right: 5px;
        line-height: 1;
    }

    &.tip-warn {
        color: #f60;
    }

    &.tip-success {
        color: #13BF13;
    }
}

@media screen and (max-width: 1366px) {
    .visit-detail-wrap {
        padding-right: 0;

        .hidden-xs {
            display: none;
        }

        .chanceDetail-header-wrap {
            .header-main {
                .header-aside-wrap {
                    .header-md-aside {
                        display: none;
                    }
                    // 小屏头
                    .header-xs-aside {
                        position: relative;
                        width: 64px;
                        margin-left: -10px;
                        display: flex;
                    }
                }
            }
        }

    }
}