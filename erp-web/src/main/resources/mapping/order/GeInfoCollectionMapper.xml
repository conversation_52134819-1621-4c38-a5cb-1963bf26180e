<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.GeInfoCollectionMapper">
    <select id="getGeNoInfo" resultType="com.vedeng.order.model.vo.BuyorderGoodsVo" parameterType="java.lang.Integer">
        SELECT
			A.GE_CONTRACT_NO,A.GE_SALE_CONTRACT_NO
		FROM T_BUYORDER_GOODS A
		where
			BUYORDER_GOODS_ID=#{buyorderGoodsId,jdbcType=INTEGER}
    </select>

<!--	<select id="getGeNoTraderInfo" resultType="com.vedeng.order.model.Buyorder" parameterType="java.lang.Integer">
		select
			A.TRADER_ID,<PERSON>.TRADER_NAME,A.TRADER_CONTACT_ID
		from
			T_BUYORDER A
		where BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
	</select>-->


	<insert id="addGeInfo" parameterType="map">


    </insert>

    <update id="upadteGeInfo" parameterType="map">
		update
		    T_BUYORDER_GOODS
		set
			GE_CONTRACT_NO=#{geContractNo,jdbcType=VARCHAR},
			GE_SALE_CONTRACT_NO=#{geSaleContractNo,jdbcType=VARCHAR}
		where
			BUYORDER_GOODS_ID=#{buyorderGoodsId,jdbcType=INTEGER}
	</update>

<!--	<update id="updateBuyorderTraderInfo" parameterType="map">
		update
		    T_BUYORDER
		set
			TRADER_ID=#{traderId,jdbcType=INTEGER}
		where
			BUYORDER_ID=#{buyorderId,jdbcType=INTEGER}
	</update>-->

	<update id="updateBuyorderTraderInfo" parameterType="com.vedeng.order.model.Buyorder" >
		update T_BUYORDER
		<set >
			<if test="buyorderNo != null" >
				BUYORDER_NO = #{buyorderNo,jdbcType=VARCHAR},
			</if>
			<if test="orderType != null" >
				ORDER_TYPE = #{orderType,jdbcType=BIT},
			</if>
			<if test="companyId != null" >
				COMPANY_ID = #{companyId,jdbcType=INTEGER},
			</if>
			<if test="orgId != null" >
				ORG_ID = #{orgId,jdbcType=INTEGER},
			</if>
			<if test="userId != null" >
				USER_ID = #{userId,jdbcType=INTEGER},
			</if>
			<if test="validStatus != null" >
				VALID_STATUS = #{validStatus,jdbcType=BIT},
			</if>
			<if test="validTime != null" >
				VALID_TIME = #{validTime,jdbcType=BIGINT},
			</if>
			<if test="status != null" >
				STATUS = #{status,jdbcType=BIT},
			</if>
			<if test="lockedStatus != null" >
				LOCKED_STATUS = #{lockedStatus,jdbcType=BIT},
			</if>
			<if test="invoiceStatus != null" >
				INVOICE_STATUS = #{invoiceStatus,jdbcType=BIT},
			</if>
			<if test="invoiceTime != null" >
				INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
			</if>
			<if test="paymentStatus != null" >
				PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},
			</if>
			<if test="paymentTime != null" >
				PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
			</if>
			<if test="deliveryStatus != null" >
				DELIVERY_STATUS = #{deliveryStatus,jdbcType=BIT},
			</if>
			<if test="deliveryTime != null" >
				DELIVERY_TIME = #{deliveryTime,jdbcType=BIGINT},
			</if>
			<if test="arrivalStatus != null" >
				ARRIVAL_STATUS = #{arrivalStatus,jdbcType=BIT},
			</if>
			<if test="arrivalTime != null" >
				ARRIVAL_TIME = #{arrivalTime,jdbcType=BIGINT},
			</if>
			<if test="serviceStatus != null" >
				SERVICE_STATUS = #{serviceStatus,jdbcType=BIT},
			</if>
			<if test="haveAccountPeriod != null" >
				HAVE_ACCOUNT_PERIOD = #{haveAccountPeriod,jdbcType=BIT},
			</if>
			<if test="deliveryDirect != null" >
				DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
			</if>
			<if test="totalAmount != null" >
				TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
			</if>
			<if test="traderId != null" >
				TRADER_ID = #{traderId,jdbcType=INTEGER},
			</if>
			<if test="traderName != null" >
				TRADER_NAME = #{traderName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactId != null" >
				TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
			</if>
			<if test="traderContactName != null" >
				TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
			</if>
			<if test="traderContactMobile != null" >
				TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="traderContactTelephone != null" >
				TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="traderAddressId != null" >
				TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
			</if>
			<if test="traderAddress != null" >
				TRADER_ADDRESS = #{traderAddress,jdbcType=VARCHAR},
			</if>
			<if test="traderComments != null" >
				TRADER_COMMENTS = #{traderComments,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderId != null" >
				TAKE_TRADER_ID = #{takeTraderId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderName != null" >
				TAKE_TRADER_NAME = #{takeTraderName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactId != null" >
				TAKE_TRADER_CONTACT_ID = #{takeTraderContactId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderContactName != null" >
				TAKE_TRADER_CONTACT_NAME = #{takeTraderContactName,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactMobile != null" >
				TAKE_TRADER_CONTACT_MOBILE = #{takeTraderContactMobile,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderContactTelephone != null" >
				TAKE_TRADER_CONTACT_TELEPHONE = #{takeTraderContactTelephone,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderAddressId != null" >
				TAKE_TRADER_ADDRESS_ID = #{takeTraderAddressId,jdbcType=INTEGER},
			</if>
			<if test="takeTraderAddress != null" >
				TAKE_TRADER_ADDRESS = #{takeTraderAddress,jdbcType=VARCHAR},
			</if>
			<if test="takeTraderArea != null" >
				TAKE_TRADER_AREA = #{takeTraderArea,jdbcType=VARCHAR},
			</if>
			<if test="paymentType != null" >
				PAYMENT_TYPE = #{paymentType,jdbcType=INTEGER},
			</if>
			<if test="prepaidAmount != null" >
				PREPAID_AMOUNT = #{prepaidAmount,jdbcType=DECIMAL},
			</if>
			<if test="accountPeriodAmount != null" >
				ACCOUNT_PERIOD_AMOUNT = #{accountPeriodAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmount != null" >
				RETAINAGE_AMOUNT = #{retainageAmount,jdbcType=DECIMAL},
			</if>
			<if test="retainageAmountMonth != null" >
				RETAINAGE_AMOUNT_MONTH = #{retainageAmountMonth,jdbcType=INTEGER},
			</if>
			<if test="logisticsId != null" >
				LOGISTICS_ID = #{logisticsId,jdbcType=INTEGER},
			</if>
			<if test="invoiceType != null" >
				INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
			</if>
			<if test="freightDescription != null" >
				FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=INTEGER},
			</if>
			<if test="paymentComments != null" >
				PAYMENT_COMMENTS = #{paymentComments,jdbcType=VARCHAR},
			</if>
			<if test="logisticsComments != null" >
				LOGISTICS_COMMENTS = #{logisticsComments,jdbcType=VARCHAR},
			</if>
			<if test="invoiceComments != null" >
				INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
			</if>
			<if test="comments != null" >
				COMMENTS = #{comments,jdbcType=VARCHAR},
			</if>
			<if test="additionalClause != null" >
				ADDITIONAL_CLAUSE = #{additionalClause,jdbcType=VARCHAR},
			</if>
			<!-- 增加预计到货时间 -->
			<if test="estimateArrivalTime != null" >
				ESTIMATE_ARRIVAL_TIME = #{estimateArrivalTime,jdbcType=VARCHAR},
			</if>
			<if test="addTime != null" >
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null" >
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null" >
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null" >
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="traderArea != null" >
				TRADER_AREA = #{traderArea,jdbcType=VARCHAR},
			</if>
			<if test="satisfyDeliveryTime != null" >
				SATISFY_DELIVERY_TIME = #{satisfyDeliveryTime,jdbcType=BIGINT},
			</if>
		</set>
		where BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	</update>
</mapper>