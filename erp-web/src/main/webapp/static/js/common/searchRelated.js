void function() {
    var defaults = {
        el: '.J-searchRelated',
        onchange: function () {},
        placeholder: '',

        type: 'text',
        width: '323px',
        maxlength: null,
        remoteInfo: {
            url: '', // 联想搜索接口
            hasPage: true, // 是否需要分页
            paramsKey: 'terminalTraderName', // 接口入参
            parseLabel: 'terminalName', // 面板label
            parseValue: 'terminalName', // 面板value
            parseLabelRight: '', // 面板right值
        },
        openTyc: function () {},
        inputName: ''
    };

    var SearchRelated = function (config) {
        this.config = $.extend({}, defaults, config);
        this.__init();
        return this;
    }

    SearchRelated.prototype = {
        constructor: 'SearchRelated',

        __init: function () {
            this.$wrapper = $(this.config.el);
            this.$input = $(this.config.input);
            this.onCompressing = false; // 中文输入
            this.__initTemp();
            this.__bindEvent();
        },

        __initTemp: function () {
            var template = `
                <div class="vd-ui-search-related J-search-related">
                    <div class="vd-ui-input">
                        <input
                            name="${this.config.inputName}"
                            class="J-search-input"
                            type="${this.config.type}"
                            ${this.config.maxlength? `maxlength="${this.config.maxlength}"` :''}
                            ${this.config.placeholder? `placeholder="${this.config.placeholder}"` :''}
                            ${this.config.disabled ? `disabled` :''}
                            autocomplete="off" 
                        />
                    </div>
                    <div class="vd-ui-related-wrap">
                        <ul class="related-ul scrollbar J-related-ul"></ul>
                    </div>
                </div>
            `;
            this.$wrapper.append(template);

            this.$value = this.$wrapper.find('.J-search-input');
            if (this.$input.length && this.$input.val().trim()) {
                this.__setValue();
            }
        },

        __bindEvent: function () {
            var _this = this;

            this.$wrapper.on('focus', '.J-search-input', function (e) {
                _this.$wrapper.find('.J-search-input').addClass('focus');
                if ($(this).val()) {
                    _this.__getSearchList();
                }
                e.stopPropagation();
            })

            this.$wrapper.on('blur', '.J-search-input', function () {
                _this.$wrapper.find('.J-search-input').removeClass('focus');
            })

            $(document).click(function (e) {
                if (!_this.$wrapper.find('.J-search-related')[0].contains(e.target)) {
                    _this.__hideList();
                }
            })

            this.$wrapper.on('click', '.J-reload', function (e) {
                _this.__getSearchList();
                e.stopPropagation();
            })
            this.$wrapper.on('click', '.J-tyc', function (e) {
                _this.config.openTyc(_this.$value.val());
                e.stopPropagation();
            })

            // ↓联想相关↓
            this.$wrapper.on('keydown', '.J-search-input', function (e) {
                if (e.key === "Tab" || e.keyCode === 9) {
                    _this.__hideList();
                    e.preventDefault();
                }
            })
            this.$wrapper.on('compositionstart', '.J-search-input', function (e) {
                _this.onCompressing = true;
            })
            this.$wrapper.on('compositionend', '.J-search-input', function (e) {
                _this.onCompressing = false;
                if (e.target.value.trim()) {
                    _this.__handlerInput();
                    _this.config.onchange(_this.$value.val(), {});
                }
            })
            this.$wrapper.on('input', '.J-search-input', function (e) {
                if (_this.onCompressing) {
                    return;
                }
                _this.__handlerInput();
                _this.config.onchange(_this.$value.val(), {});
            })
            this.$wrapper.on('click', '.J-rel-item', function (e) {
                let index = $(this).data('index');
                let info = _this.relatedList[index] || {};
                _this.config.onchange(info.terminalName, info);
                _this.__setValue(info.terminalName);
                e.stopPropagation();
            })
        },
        __handlerInput: function () {
            this.timer && clearTimeout(this.timer);
            this.timer = setTimeout(()=> {
                this.__getSearchList();
            }, 300);
        },
        __getSearchList: function () {
            let val = this.$wrapper.find('.J-search-input').val().trim();
            if (!val) {
                console.log('  ---------------------------------------- hide');
                this.__hideList();
                return;
            };

            if (this.config.remoteInfo.url) {
                
                console.log('  ---------------------------------------- show');
                this.$wrapper.find('.J-related-ul').slideDown(220).css('display', 'block').html(`
                    <li class="loading">
                        <i class="vd-icon icon-loading"></i>
                        <span>加载中...</span>
                    </li>
                `);

                let value = val.trim();
                let reqData = {};
                let requrl = this.config.remoteInfo.url;
                let reqMethod = this.config.remoteInfo.method || 'post';

                reqData[this.config.remoteInfo.paramsKey] = value;
                if (this.config.remoteInfo.hasPage) {
                    reqData['pageNum'] = 1;
                    reqData['pageSize'] = 100;
                }

                var _this = this;
                $.ajax({
                    url: requrl,
                    type: reqMethod,
                    dataType: 'json',
                    data: reqData,
                    success: function (res) {
                        if (res.code == 0) {
                            console.log('search res', res);

                            _this.relatedList = [];

                            if (_this.config.remoteInfo.parseData) {
                                _this.relatedList = _this.config.remoteInfo.parseData(res);
                            } else {
                                _this.relatedList = res.data;
                            }
                            _this.__drawRelated();

                        } else {
                            _this.__drawRelatedFail();
                        }
                    },
                    error: function(err) {
                        _this.__drawRelatedFail();
                    }
                })
            }
        },
        __drawRelated: function () {
            if (this.relatedList.length) {
                let render = [];
                let showLabel = this.config.remoteInfo.parseLabel || ''
                let rightKey = this.config.remoteInfo.parseLabelRight || '';
                this.relatedList.forEach((item, index) => {
                    render.push(`
                        <div class="sr-item J-rel-item" data-index="${index}">
                            <div class="sr-item-left">
                                <p class="text-line-1">${this.__lightHight(item[showLabel])}</p>
                            </div>
                            ${
                                rightKey? `<div class="sr-item-right">${item[rightKey]}</div>` :''
                            }
                        </div>
                    `);
                });
    
                this.$wrapper.find('.J-related-ul').html(`
                    <div class="search-list" v-if="relateList.length">
                        <div class="local-data">终端数据库匹配</div>
                        ${render.join('')}
                    </div>
                `)
            } else {
                this.$wrapper.find('.J-related-ul').html(`
                    <li class="empty-li">本地无匹配数据，<span class="J-tyc"><i class="vd-icon icon-search"></i>天眼查查询</span></li>
                `)
            }
        },
        __drawRelatedFail: function () {
            this.relatedList = [];
            this.$wrapper.find('.J-related-ul').html(`
                <li class="failed-li">
                    <i class="vd-icon icon-error2"></i>
                    <span>加载失败</span>
                    <span class="reload J-reload" @click="handleReload">重新加载</span>
                </li>
            `);
        },
        __lightHight (name) {
            var val = this.$wrapper.find('.J-search-input').val();
            val = val.trim();
            if (!val || !name) return name;
            const regExp = new RegExp(val, 'g');
            name = name.replace(regExp, `<font color='#FF6600'">${val}</font>`);
            return name;
        },

        __hideList: function () {
            this.relatedList = [];
            this.$wrapper.find('.J-related-ul').slideUp(190);
        },

        __setValue: function(str) {
            this.$value.val(str);
            this.$input.val(str);
            this.__hideList();
        }
    }

    window.SearchRelated = SearchRelated;

}.call(this);