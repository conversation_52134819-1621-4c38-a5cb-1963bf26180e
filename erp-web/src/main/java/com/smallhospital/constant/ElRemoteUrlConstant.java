package com.smallhospital.constant;

/**
 * <AUTHOR>
 * 小医院远程访问接口常量类
 */
public class ElRemoteUrlConstant {

    public static String IP = "";

    public static Integer PORT = 8080;

    public static String GENERATE_TOKEN = "";

    /**
     * 添加小医院机构
     */
    public static String ADD_EL_HOSPITAL = "";


    /**
     * 添加小医院合同
     */
    public static String ADD_EL_CONTRACT = "";

    /**
     * 添加小医院合同里的产品信息
     */
    public static String ADD_EL_PRODUCT = "";

    /**
     * 发送小医院订单的出库单
     */
    public static String SEND_EL_WAREHOUSE_ORDER = "";


    /**
     * 发送小医院订单的售后结果
     */
    public static String SEND_EL_AFTER_SALE_RESULT = "";


    /**
     * 更新小医院产品分类信息
     */
    public static String UPDATE_EL_PRODUCT_CATEGORY;


}
