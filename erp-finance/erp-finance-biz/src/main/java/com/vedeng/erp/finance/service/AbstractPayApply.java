package com.vedeng.erp.finance.service;

import com.alibaba.fastjson.JSONObject;
import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.domain.entity.AutoPayConfig;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.dto.event.CreateBillEvent;
import com.vedeng.erp.finance.mapper.AutoPayConfigMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.util.CollectionUtils;

import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description: 抽象类
 */
@Slf4j
public abstract class AbstractPayApply implements PayApplyInterface {

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Autowired
    private AutoPayConfigMapper autoPayConfigMapper;

    /**
     *  圆迈
     */
    private static final String YUAN_MAI = "上海圆迈贸易有限公司（京东商城）";

    /**
     * 是否满足自动制单
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    @Override
    public void createBillRuleCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException{
        // 数据校验规则验证
        this.createBillBaseCheckValid(payApplyCreateBillDto);
        // 业务规则校验
        this.createBillBusinessCheck(payApplyCreateBillDto);
    }
    /**
     * 自动制单
     * @param payApplyCreateBillDto
     */
    @Override
    public void autoCreateCreateBill(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException{
        // 选择银行
        Integer payVedengBankId = this.choseBank(payApplyCreateBillDto);
        // 其他业务
        this.otherProcess(payApplyCreateBillDto);
        // 制单
        this.audit(payApplyCreateBillDto.getPayApplyId(),payVedengBankId);
    }
    
    /**
     * 自动付款基础校验
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    public abstract void createBillBaseCheckValid(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException;

    /**
     * 判断是否满足自动制单
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    public abstract void createBillBusinessCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException;

    /**
     * 后续业务
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    public abstract void otherProcess(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException;

    /**
     * 自动付款规则-获取自动付款银行
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    public abstract Integer choseBank(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException;

    /**
     * 校验付款申请中
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    public void checkPayingApply(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException{
        PayApplyCreateBillDto queryDto = new PayApplyCreateBillDto();
        queryDto.setRelatedId(payApplyCreateBillDto.getRelatedId());
        queryDto.setPayType(payApplyCreateBillDto.getPayType());
        queryDto.setValidStatus(0);
        queryDto.setIsBill(0);
        queryDto.setPayStatus(0);
        List<PayApplyCreateBillDto> payApplyByDto = payApplyApiService.getPayApplyByDto(queryDto);
        if (CollectionUtils.isEmpty(payApplyByDto)){
            throw new ServiceException("未查到改付款申请单");
        }
        if (payApplyByDto.size() > 1){
            log.info("付款中的申请：{}", JSONObject.toJSONString(payApplyByDto));
            throw new ServiceException("已存在付款中的申请");
        }
    }

    /**
     * 校验付款方式和供应商
     * @param payApplyCreateBillDto
     * @throws ServiceException
     */
    public void businessCheck(PayApplyCreateBillDto payApplyCreateBillDto) throws ServiceException{
        Integer traderMode = payApplyCreateBillDto.getTraderMode();
        if (!Objects.equals(traderMode,521)){
            throw new ServiceException("付款方式非银行，不支持自动付款,当前付款方式：" + traderMode);
        }

        String traderName = payApplyCreateBillDto.getTraderName();
        if (Objects.equals(traderName, YUAN_MAI)){
            throw new ServiceException("自动付款供应商不支持圆迈");
        }
        if (StringUtils.isNotBlank(payApplyCreateBillDto.getBankRemark())){
            // 判断银行回单备注
            throw new ServiceException("银行回单备注有值，不支持自动付款");
        }
    }

    /**
     * 00:30:00,16:00:00
     * @return
     */
    public boolean payApplyRuleCheck(){
        AutoPayConfig autoPayConfig = autoPayConfigMapper.selectByPrimaryKey(1);
        log.info("付款申请规则：{}", JSONObject.toJSONString(autoPayConfig));
        // 00:30:00,16:00:00
        String payApplyTime = Optional.ofNullable(autoPayConfig).map(AutoPayConfig::getPayApplyTime).orElseThrow(() -> new ServiceException("付款申请规则时间为空"));
        String[] split = payApplyTime.split(",");
        if (split.length != 2){
            log.info("付款申请规则时间格式错误");
            return false;
        }
        String startTime = split[0];
        String endTime = split[1];
        boolean withinWorkingHours = isWithinWorkingHours(startTime, endTime);
        return withinWorkingHours;
    }

    public boolean payRuleCheck(){
        AutoPayConfig autoPayConfig = autoPayConfigMapper.selectByPrimaryKey(1);
        log.info("自动付款规则：{}", JSONObject.toJSONString(autoPayConfig));
        // 00:30:00,16:00:00
        String payBankTime = Optional.ofNullable(autoPayConfig).map(AutoPayConfig::getPayBankTime).orElseThrow(() -> new ServiceException("付款规则时间为空"));
        String[] split = payBankTime.split(",");
        if (split.length != 2){
            log.info("付款规则时间格式错误");
            return false;
        }
        String startTime = split[0];
        String endTime = split[1];
        boolean withinWorkingHours = isWithinWorkingHours(startTime, endTime);
        return withinWorkingHours;
    }


    /**
     * 获取当前时间是否在9:30:02到16:40:01之间
     * @param startTimeStr
     * @param endTimeStr
     * @return
     */
    public static boolean isWithinWorkingHours(String startTimeStr, String endTimeStr) {
        if (startTimeStr == null || endTimeStr == null) {
            return false;
        }
        // 按:截取时分秒
        String[] startTimeArr = startTimeStr.split(":");
        String[] endTimeArr = endTimeStr.split(":");
        if (startTimeArr.length != 3 || endTimeArr.length != 3) {
            return false;
        }

        // 获取当前的日期和时间
        Calendar calendar = Calendar.getInstance();

        // 获取当前的星期、小时、分钟和秒
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        int hourOfDay = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int second = calendar.get(Calendar.SECOND);

        // 检查当前时间是否在周一到周五
        boolean isWeekday = (dayOfWeek >= Calendar.MONDAY) && (dayOfWeek <= Calendar.FRIDAY);

        // 构造当前时间的秒数表示
        int currentTimeInSeconds = hourOfDay * 3600 + minute * 60 + second;

        // 构造工作时间的开始和结束的秒数表示
        int startTimeInSeconds = Integer.valueOf(startTimeArr[0])* 3600 + Integer.valueOf(startTimeArr[1]) * 60 + Integer.valueOf(startTimeArr[2]);

        int endTimeInSeconds = Integer.valueOf(endTimeArr[0]) * 3600 + Integer.valueOf(endTimeArr[1]) * 60 + Integer.valueOf(endTimeArr[2]);

        // 检查当前时间是否在9:30:02到16:40:01之间
        boolean isWorkingTime = currentTimeInSeconds >= startTimeInSeconds &&
                currentTimeInSeconds <= endTimeInSeconds;

        // 如果是工作日并且在工作时间内，则返回true
        return isWeekday && isWorkingTime;
    }

    public void audit(Integer payApplyId,Integer payVedengBankId) {
        CreateBillEvent createBillEvent = new CreateBillEvent();
        createBillEvent.setPayVedengBankId(payVedengBankId);
        createBillEvent.setPayApplyId(payApplyId);
        log.info("制单事件触发：{}", JSONObject.toJSONString(createBillEvent));
        eventBusCenter.post(createBillEvent);
    }
}
