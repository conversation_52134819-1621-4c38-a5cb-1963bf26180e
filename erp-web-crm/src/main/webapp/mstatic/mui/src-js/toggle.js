Vue.component('vd-ui-toggle', {
    template: `<div class="vd-ui-toggle-wrap" @click="toggleCheck" :class="{active: checked, disabled: disabled}">
        <div class="vd-ui-toggle-inner"><div>
    </div>`,
    props: {
        checked: {
            type: Boolean,
            default: false
        },
        disabled: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
        };
    },
    computed: {

    },
    mounted() {
        
    },
    methods: {
        toggleCheck() {
            if(!this.disabled) {
                this.checked = !this.checked;
                this.$emit('update:checked', this.checked);
                this.$emit('change', this.checked);
            }
        }
    }
})