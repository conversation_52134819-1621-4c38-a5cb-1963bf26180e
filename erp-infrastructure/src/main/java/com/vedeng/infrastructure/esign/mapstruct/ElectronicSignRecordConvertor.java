package com.vedeng.infrastructure.esign.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.infrastructure.esign.domain.dto.ElectronicSignRecordDto;
import com.vedeng.infrastructure.esign.domain.entity.ElectronicSignRecordEntity;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeEventMsgDto;
import com.vedeng.infrastructure.kingdee.domain.entity.KingDeeEventMsgEntity;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * dto 转 entity
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ElectronicSignRecordConvertor extends BaseMapStruct<ElectronicSignRecordEntity, ElectronicSignRecordDto> {



}
