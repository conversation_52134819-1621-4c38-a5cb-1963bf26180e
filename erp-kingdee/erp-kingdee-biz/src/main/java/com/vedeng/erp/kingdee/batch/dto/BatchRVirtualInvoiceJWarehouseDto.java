package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;


/**
 * @description 虚拟发票和出入库单据关系表
 * <AUTHOR>
 * @date 2023/11/27 9:37
 **/
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BatchRVirtualInvoiceJWarehouseDto extends BaseDto {
    /**
    * 主键
    */
    private Integer virtualInvoiceWarehouseId;


    /**
    * 虚拟发票ID
    */
    private Integer virtualInvoiceId;

    /**
    * 虚拟发票详情ID
    */
    private Integer virtualInvoiceItemId;

    /**
    * 出入库ID
    */
    private Integer warehouseGoodsOutInId;

    /**
    * 出入库明细ID
    */
    private Integer warehouseGoodsOutInItemId;

    /**
    * 商品ID
    */
    private Integer skuId;

    /**
    * SKU
    */
    private String sku;

    /**
    * 关联数量
    */
    private BigDecimal num;

    /**
    * 出入库类型 操作类型 1采购入库 2销售出库 3销售换货入库 4销售换货出库 5销售退货入库 6采购退货出库 7采购换货出库 8采购换货入库 9外借入库 10外借出库  12盘盈入库 13报废出库 14领用出库 16 盘亏出库,
    */
    private Integer outInType;

    private String outInNo;
}