package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.BuyorderActualSupplier;
import org.apache.ibatis.annotations.Param;

public interface BuyorderActualSupplierMapper {
    /**
     * delete by primary key
     *
     * @param buyorderActualSupplierId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long buyorderActualSupplierId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BuyorderActualSupplier record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyorderActualSupplier record);

    /**
     * select by primary key
     *
     * @param buyorderActualSupplierId primary key
     * @return object by primary key
     */
    BuyorderActualSupplier selectByPrimaryKey(Long buyorderActualSupplierId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyorderActualSupplier record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyorderActualSupplier record);

    BuyorderActualSupplier findByBuyorderId(@Param("buyorderId") Integer buyorderId);
}