<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.PublicCustomerExemptUsersMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto">
    <!--@mbg.generated-->
    <!--@Table T_PUBLIC_CUSTOMER_EXEMPT_USERS-->
    <id column="PUBLIC_CUSTOMER_EXEMPT_USERS_ID" jdbcType="INTEGER" property="publicCustomerExemptUsersId" />
    <result column="USER_ID" jdbcType="INTEGER" property="userId" />
    <result column="DELETED" jdbcType="BOOLEAN" property="deleted" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PUBLIC_CUSTOMER_EXEMPT_USERS_ID, USER_ID, DELETED, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_PUBLIC_CUSTOMER_EXEMPT_USERS
    where PUBLIC_CUSTOMER_EXEMPT_USERS_ID = #{publicCustomerExemptUsersId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_PUBLIC_CUSTOMER_EXEMPT_USERS
    where PUBLIC_CUSTOMER_EXEMPT_USERS_ID = #{publicCustomerExemptUsersId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="PUBLIC_CUSTOMER_EXEMPT_USERS_ID" keyProperty="publicCustomerExemptUsersId" parameterType="com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PUBLIC_CUSTOMER_EXEMPT_USERS (USER_ID, DELETED, ADD_TIME, 
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{userId,jdbcType=INTEGER}, #{deleted,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="PUBLIC_CUSTOMER_EXEMPT_USERS_ID" keyProperty="publicCustomerExemptUsersId" parameterType="com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_PUBLIC_CUSTOMER_EXEMPT_USERS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        USER_ID,
      </if>
      <if test="deleted != null">
        DELETED,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto">
    <!--@mbg.generated-->
    update T_PUBLIC_CUSTOMER_EXEMPT_USERS
    <set>
      <if test="userId != null">
        USER_ID = #{userId,jdbcType=INTEGER},
      </if>
      <if test="deleted != null">
        DELETED = #{deleted,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where PUBLIC_CUSTOMER_EXEMPT_USERS_ID = #{publicCustomerExemptUsersId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto">
    <!--@mbg.generated-->
    update T_PUBLIC_CUSTOMER_EXEMPT_USERS
    set USER_ID = #{userId,jdbcType=INTEGER},
      DELETED = #{deleted,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER}
    where PUBLIC_CUSTOMER_EXEMPT_USERS_ID = #{publicCustomerExemptUsersId,jdbcType=INTEGER}
  </update>

  <select id="selectByDeletedlistPage" parameterType="java.util.Map" resultType="com.vedeng.erp.trader.domain.dto.PublicCustomerExemptUsersDto">
    select
    a.PUBLIC_CUSTOMER_EXEMPT_USERS_ID, a.USER_ID, a.DELETED, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,
    b.USERNAME,c.USERNAME as creatorUsername
    from T_PUBLIC_CUSTOMER_EXEMPT_USERS a
    left join T_USER b on a.USER_ID=b.USER_ID
    left join T_USER c on a.CREATOR=c.USER_ID
    where a.DELETED=#{map.deleted,jdbcType=BOOLEAN}
    order by a.ADD_TIME desc
  </select>

<!--auto generated by MybatisCodeHelper on 2022-05-01-->
  <select id="selectByUserId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_PUBLIC_CUSTOMER_EXEMPT_USERS
    where USER_ID=#{userId,jdbcType=INTEGER}
  </select>
</mapper>