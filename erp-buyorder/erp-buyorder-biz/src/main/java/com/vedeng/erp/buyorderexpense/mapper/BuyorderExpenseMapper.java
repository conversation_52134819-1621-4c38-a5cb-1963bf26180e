package com.vedeng.erp.buyorderexpense.mapper;

import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.dto.InvoiceSaveSearchDto;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseEntity;

import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/8/22 13:36
 **/
@Repository
public interface BuyorderExpenseMapper {
    /**
     * delete by primary key
     * @param buyorderExpenseId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer buyorderExpenseId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BuyorderExpenseEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyorderExpenseEntity record);

    /**
     * select by primary key
     * @param buyorderExpenseId primary key
     * @return object by primary key
     */
    BuyorderExpenseEntity selectByPrimaryKey(Integer buyorderExpenseId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyorderExpenseEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyorderExpenseEntity record);

    int batchInsert(List<BuyorderExpenseEntity> list);

    /**
     * <AUTHOR>
     * @desc 根据采购单id查询直属费用单
     * @param buyorderId
     * @param orderType 0生效前创建，1生效后创建
     * @return
     */
    BuyorderExpenseDto queryExpenseInfoByType(@Param("buyorderId") Integer buyorderId, @Param("orderType") Integer orderType);

    /**
     * 更新审核状态
     * @param buyorderExpenseId 采购费用订单
     * @param verifyStatus 审核状态
     */
    void updateVerifyStatusByBuyorderExpenseId(@Param("buyorderExpenseId") Integer buyorderExpenseId, @Param("verifyStatus") Integer verifyStatus);

    /**
     * 更新审核状态
     * @param buyorderId 采购订单id
     * @param verifyStatus 审核状态
     */
    void updateVerifyStatusByBuyorderId(@Param("buyorderId") Integer buyorderId, @Param("verifyStatus") Integer verifyStatus);

    /**
     * 根据采购单id 更新 同步的生成的采购费用单状态
     * @param buyorderId 采购单id
     * @param status 状态
     */
    void updateStatusByBuyorderId(@Param("buyorderId") Integer buyorderId, @Param("status") Integer status);
    /**
     * 根据采购单id查询对应的采购费用订单信息集合（包括采购单生效前和生效后创建的费用单）
     * @param buyOrderId 采购单id
     * @return 购费用订单信息集合
     */
    List<BuyorderExpenseDto> getBuyOrderExpenseListByBuyOrderId(@Param("buyOrderId") Integer buyOrderId);

    /**
     * 更新合同
     * @param buyOrderExpenseId 采购费用单id
     * @param url 合同url
     */
    void updateContractUrlOfBuyorder(@Param("buyOrderExpenseId") Integer buyOrderExpenseId, @Param("url") String url);

    /**
     * 获取采购费用单信息根据 采购费用单编号
     * @param buyOrderExpenseNo
     * @return
     */
    BuyorderExpenseEntity getBuyorerExpenseByOrderNo(String buyOrderExpenseNo);


    /**
     * 根据售后id查询采购费用单信息
     * @param afterSalesId
     * @return
     */
    BuyorderExpenseDto getBuyorerExpenseByAfterSalesId(Integer afterSalesId);

    /**
     *根据 采购单id 查询相关信息
     * @param buyorderId id
     * @return BuyOrderDto
     */
    BuyOrderDto selectBuyorderData(Integer buyorderId);

    /**
     * 根据 采购单id查询直属的费用单信息
     * @param buyOrderId 采购单id
     * @return 直属费用单信息
     */
    BuyorderExpenseEntity selectDirectExpenseInfo(@Param("buyOrderId") Integer buyOrderId);

    /**
     * 检索采购费用商品信息
     * @param searchDto
     * @return
     */
    List<BuyorderExpenseItemDto> getExpenseGoodsList4InvoiceSave(InvoiceSaveSearchDto searchDto);

    /**
     * 主键获取费用单相关信息
     * @param orderIds
     * @return
     */
    List<BuyorderExpenseDto> getOrderExpenseListByOrderIds(@Param("orderIds") List<Integer> orderIds);

    /**
     * 根据费用单id查询信息
     * @param buyOrderExpenseId 费用单id
     * @return BuyorderExpenseDto
     */
    BuyorderExpenseDto getBuyOrderExpenseInfoById(@Param("buyOrderExpenseId") Integer buyOrderExpenseId);

    void updateStatusByBuyorderIdWithTicket(@Param("buyOrderExpenseId") Integer buyOrderExpenseId, @Param("status") Integer status);

    /**
     * 根据售后单id查询费用单信息
     * @param ids 费用售后单id
     * @return List<BuyorderExpenseDto> 售后单号
     */
    List<BuyorderExpenseDto> selectByExpenseAfterIds(@Param("list") List<Long> ids);


    /**
     * 根据费用单ID 列表 查询 费用单详情
     * @param buyOrderExpenseIdList
     * @return
     */
    List<BuyorderExpenseDto> getBuyOrderExpenseListByBuyOrderExpenseIdList(@Param("buyOrderExpenseIdList") List<Integer> buyOrderExpenseIdList);


    /**
     * 根据关联业务单号和订单商品id 查询商品价格
     *
     * @param orderNo
     * @param expenseItemId
     * @return
     */
    BigDecimal getBuyOrderExpenseByOrderNoAndBuyorderExpenseItemId(@Param("orderNo") String orderNo, @Param("expenseItemId") Integer expenseItemId);



}