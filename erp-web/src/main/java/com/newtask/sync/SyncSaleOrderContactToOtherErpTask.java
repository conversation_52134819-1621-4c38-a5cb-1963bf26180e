package com.newtask.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.model.dto.SaleContractDto;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.dto.PurchaseContractDto;
import com.vedeng.system.model.Attachment;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 执行同步采购订单的合同给另一个ERP的销售订单下
 */
@Slf4j
@Component
@JobHandler(value="SyncSaleOrderContactToOtherErpTask")
public class SyncSaleOrderContactToOtherErpTask extends AbstractJobHandler {

    @Autowired
    private SyncDataErpApiService syncDataErpApiService;

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;




    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        String dataType = SyncDataTypeEnum.BUYORDERCONTACT_TO_SALECONTACT.getDataType();
        List<SyncDataErpDto> syncDataErpDtoList =  syncDataErpApiService.selectByProcessStatus(ErpConst.ZERO,dataType);
        if(CollectionUtils.isEmpty(syncDataErpDtoList)){
            log.info("当前无待同步的采购订单合同需要同步到目标ERP的销售订单下");
            XxlJobLogger.log("当前无待同步的采购订单合同需要同步到目标ERP的销售订单下");
            return ReturnT.SUCCESS;
        }
        for(SyncDataErpDto syncDataErpDto : syncDataErpDtoList){
            String shortName = syncDataErpDto.getTargetErp();
            if(StringUtils.isEmpty(shortName)){
                continue;
            }
            BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByShortName(shortName);
            if(baseCompanyInfoDto != null){
                String host = baseCompanyInfoDto.getErpDomain();//请求的host http://erp.ivedeng.com
                String url = SyncDataTypeEnum.BUYORDERCONTACT_TO_SALECONTACT.getDataInputUrl(); //  /orderstream/saleorder/contractReturnSaveForOtherErp.do
                String requestContent = syncDataErpDto.getRequestContent(); //{"saleorderNo":"BD001",...}
                String content = NewHttpClientUtils.httpPostWithRawContent(host+url, requestContent);
                if(StringUtils.isNotBlank(content)){
                    JSONObject jsonObject = JSON.parseObject(content);
                    if(jsonObject != null && jsonObject.getInteger("code") == 0){
                        syncDataErpDto.setProcessStatus(ErpConst.TWO);
                        syncDataErpDto.setUpdateTime(new Date());
                        syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                        continue;
                    }
                    if(jsonObject != null && jsonObject.getInteger("code") == 3){ //当服务方返回失败时，此条不需要处理了
                        syncDataErpDto.setProcessStatus(ErpConst.THREE);
                        syncDataErpDto.setUpdateTime(new Date());
                        syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                        continue;
                    }
                }
            }
            //以下是返回2的场景，下次继续请求即可。
//            syncDataErpDto.setProcessStatus(ErpConst.THREE);
            syncDataErpDto.setUpdateTime(new Date());
            syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);

        }
        return ReturnT.SUCCESS;
    }



}
