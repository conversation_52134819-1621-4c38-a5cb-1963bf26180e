<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="发货通知详情" scope="application" />
<%@ include file="../../common/common.jsp"%>
<%--<script type="text/javascript" src='<%= basePath %>static/js/order/saleorder/view.js?rnd=${resourceVersionKey}'></script>--%>
<script type="text/javascript">
    function applyValidCustomer(deliveryNoticeId,taskId,operationType){
        checkLogin();
        var formToken = $("input[name='formToken']").val();
        //操作类型 1 申请审核 2关闭发货通知 3申请审核 4申请不通过
        //var operationType=1;
        var pass=null;
        var messageConfirm="";
        if (operationType==1){
            messageConfirm="您是否确认申请审核该发货通知？";
        }

        if (operationType==2){
            messageConfirm="您是否确认关闭该发货通知？";
        }

        if (operationType==3){
            pass=true;
            messageConfirm="您是否确认审核通过？";
        }
        if (operationType==4){
            pass=false;
            messageConfirm="您是否确认审核不通过？";
        }
        layer.confirm(messageConfirm, {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "POST",
                url: "./changeStatusDeliveryNotice.do",
                //data: {'traderCustomerId':traderCustomerId,'taskId':taskId,'formToken':formToken},
                data: {'deliveryNoticeId':deliveryNoticeId,'taskId':taskId,'formToken':formToken,'operationType':operationType,'pass':pass},
                dataType:'json',
                success: function(data){
                    if (data.code == 0) {
                        window.location.reload();
                    } else {
                        layer.alert(data.message);
                    }

                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function(){
        });
    }
</script>
<div class="content mt10 ">
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">基本信息</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">发货通知单</td>
                <td>${notice.orderNo}-${notice.deliveryNoticeNo}</td>
                <td class="table-smaller">关联订单号</td>
                <td>
                    ${notice.orderNo}
                </td>
            </tr>
            <tr>
                <td>创建者</td>
                <td>${notice.creatorName}</td>
                <td>创建时间</td>
                <td>
                    <date:date value ="${notice.addTime}"/>
                </td>
            </tr>
            <tr>
                <td>归属销售</td>
                <td>${saleorder.optUserName}</td>
                <td>通知单状态</td>
                <td>
                    <c:choose>
                        <c:when test="${notice.status eq 0}">待确认</c:when>
                        <c:when test="${notice.status eq 1}">进行中</c:when>
                        <c:when test="${notice.status eq 2}">已完结</c:when>
                        <c:when test="${notice.status eq 3}">已关闭</c:when>
                        <c:otherwise></c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>发货类型</td>
                <td>
                    <c:choose>
                        <c:when test="${saleorder.deliveryMethod eq 1620}">正常发货</c:when>
                        <c:when test="${saleorder.deliveryMethod eq 1621}">等通知发货</c:when>
                        <c:when test="${saleorder.deliveryMethod eq 1622}">多地址发货</c:when>
                        <c:otherwise></c:otherwise>
                    </c:choose>
                </td>
                <td>审核状态</td>
                <td>
                    <c:choose>
                        <c:when test="${notice.auditStatus eq 0}">待审核</c:when>
                        <c:when test="${notice.auditStatus eq 1}">审核中</c:when>
                        <c:when test="${notice.auditStatus eq 2}">审核通过</c:when>
                        <c:when test="${notice.auditStatus eq 3}">审核不通过</c:when>
                        <c:otherwise>-</c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>生效时间</td>
                <td>
                    <c:choose>
                        <c:when test="${notice.validTime eq 0 or notice.validTime eq null}">-</c:when>
                        <c:otherwise><date:date value ="${notice.validTime}"/></c:otherwise>
                    </c:choose>
                </td>
                <td>发货状态</td>
                <td>
                    <c:choose>
                        <c:when test="${notice.deliveryStatus eq 0}">未发货</c:when>
                        <c:when test="${notice.deliveryStatus eq 1}">部分发货</c:when>
                        <c:when test="${notice.deliveryStatus eq 2}">全部发货</c:when>
                        <c:otherwise>-</c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>是否打印随货出库单</td>
                <td>
                    <c:choose>
                        <c:when test="${notice.isPrintout ne 0 && notice.isPrintout ne -1}">是</c:when>
                        <c:otherwise>否</c:otherwise>
                    </c:choose>
                </td>
                <td>随货出库单是否带价格</td>
                <td>
                    <c:choose>
                        <c:when test="${notice.isPrintout eq 1}">是</c:when>
                        <c:when test="${notice.isPrintout eq 2}">否</c:when>
                        <c:otherwise>-</c:otherwise>
                    </c:choose>
                </td>
            </tr>

            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">
                收货信息
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <tbody>
            <tr>
                <td class="table-smaller">收货客户</td>
                <c:if test="${saleorder.orderType==1 }">
                    <td>${notice.traderName}</td>
                </c:if>
                <c:if test="${saleorder.orderType !=1 }">
                    <td>${notice.takeTraderName}</td>
                </c:if>
                <td class="table-smaller">收货联系人</td>
                <td>${notice.takeTraderContactName}</td>
            </tr>
            <tr>
                <td>电话</td>
                <td>
                    <%--<c:if test="${not empty notice.takeTraderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${notice.takeTraderContactTelephone}',${notice.takeTraderId},1,2,${saleorder.saleorderId},${saleorder.takeTraderContactId});"></i>
                    </c:if>--%>
                    ${notice.takeTraderContactTelephone}
                </td>
                <td>手机</td>
                <td>
                    <%--<c:if test="${not empty saleorder.takeTraderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${saleorder.takeTraderContactMobile}',${saleorder.takeTraderId},1,2,${saleorder.saleorderId},${saleorder.takeTraderContactId});"></i>
                    </c:if>--%>
                    ${notice.takeTraderContactMobile}
                </td>
            </tr>
            <tr>
                <td>收货地区</td>
                <td>${notice.takeTraderArea}</td>
                <td>发货方式</td>
                <td>
                    <c:forEach var="list" items="${deliveryTypes}">
                        <c:if test="${notice.deliveryType == list.sysOptionDefinitionId}">${list.title}</c:if>
                    </c:forEach>
                </td>
            </tr>
            <tr>
                <td>收货地址</td>
                <td colspan="3">${notice.takeTraderAddress}</td>
            </tr>
            <tr>
                <td>指定物流公司</td>
                <td>
                    <c:forEach var="list" items="${logisticsList}">
                        <c:if test="${notice.logisticsId == list.logisticsId}">${list.name}</c:if>
                    </c:forEach>
                </td>
                <td>运费说明</td>
                <td>
                    <c:forEach var="list" items="${freightDescriptions}">
                        <c:if test="${notice.freightDescription == list.sysOptionDefinitionId}">${list.title}</c:if>
                    </c:forEach>
                </td>
            </tr>
            <tr>
                <td>物流备注</td>
                <td colspan="3">${notice.logisticsComments}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">产品信息</div>
        </div>
        <table class="table  table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th style="width:50px">序号</th>
                <th style="width:70px">产品名称</th>
                <th style="width:80px">品牌</th>
                <th style="width:70px">型号</th>
                <th style="width:80px">单价</th>
                <th style="width:70px">本次发货数量</th>
                <th style="width:35px">单位</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach var="list" items="${goodsList}" varStatus="staut">
                <%--<c:if test="${list.isDelete eq 0}">--%>
                    <tr>
                        <td>${staut.count}
                            <input type="hidden" name="thisTimeDeliveryNum" >
                        </td>
                        <td class="text-left">
                            <div class="customername pos_rel">
                                <c:choose>
                                    <c:when test="${list.isDelete eq 1}">
                                        <span>${newSkuInfosMap[list.sku].SHOW_NAME}<br/></span>
                                        <span>${newSkuInfosMap[list.sku].SKU_NO} <br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>
                                    </c:when>
                                    <c:otherwise>
                                        <span class="font-blue"><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>${newSkuInfosMap[list.sku].SHOW_NAME}</a>&nbsp;<%--<i class="iconbluemouth contorlIcon"></i>--%><br/></span>
                                        <span>${newSkuInfosMap[list.sku].SKU_NO} <br>${newSkuInfosMap[list.sku].MATERIAL_CODE}</span>

                                        <c:set var="skuNo" value="${list.sku}"></c:set>
                                    </c:otherwise>
                                </c:choose>
                            </div>
                        </td>
                        <td>${newSkuInfosMap[list.sku].BRAND_NAME}</td>
                        <td>${newSkuInfosMap[list.sku].MODEL}</td>
                        <td>${list.price}</td>
                        <td class="thisTimeDeliveryNumTd"><span>${list.thisTimeDeliveryNum}</span></td>
                        <td>${newSkuInfosMap[list.sku].UNIT_NAME}</td>
                    </tr>
                <%--</c:if>--%>
            </c:forEach>
            <c:if test="${empty goodsList}">
                <tr>
                    <td colspan="7">暂无产品信息！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>
    <div class="text-center mb15">
        <c:if test="${(saleorder.optUserName == curr_user.username or curr_user.userId == notice.creator) and (notice.auditStatus eq 0 or notice.auditStatus eq 3) and (notice.status ne 3)  }">
        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidCustomer(${notice.deliveryNoticeId},${taskInfo.id == null ?0: taskInfo.id},1)">申请审核</button>
        </c:if>

        <c:if test="${(saleorder.optUserName == curr_user.username or curr_user.userId == notice.creator) and (notice.auditStatus eq 0 or notice.auditStatus eq 3) and notice.status ne 3 }">
        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 addtitle" tabTitle='{"num":"order_saleorder_edit<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"order/saleorder/deliveryNoticeEditPage.do?saleorderId=${saleorder.saleorderId}&deliveryNoticeId=${notice.deliveryNoticeId}","title":"编辑发货通知"}'>编辑发货通知</button>
        </c:if>

        <c:if test="${(saleorder.optUserName == curr_user.username or curr_user.userId == notice.creator) and (notice.auditStatus ne 1 and notice.auditStatus ne 2) and notice.status ne 3 }">
        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidCustomer(${notice.deliveryNoticeId},${taskInfo.id == null ?0: taskInfo.id},2)">关闭发货通知</button>
        </c:if>

        <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidCustomer(${notice.deliveryNoticeId},${taskInfo.id == null ?0: taskInfo.id},3)">申请通过</button>
        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidCustomer(${notice.deliveryNoticeId},${taskInfo.id == null ?0: taskInfo.id},4)">申请不通过</button>
        </c:if>

    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                审核记录
            </div>
        </div>
        <table class="table">
            <thead>
            <tr>
                <th>操作人</th>
                <th>操作时间</th>
                <th>操作事项</th>
                <th>备注</th>
            </tr>
            </thead>
            <tbody>

            <c:if test="${null!=historicActivityInstance}">
                <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                    <c:if test="${not empty  hi.activityName}">
                        <tr>
                            <td>
                                <c:choose>
                                    <c:when test="${hi.activityType == 'startEvent'}">
                                        ${startUser}
                                    </c:when>
                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                    </c:when>
                                    <c:otherwise>
                                        <c:if test="${historicActivityInstance.size() == status.count}">
                                            <c:forEach var="vs" items="${verifyUsersList}" varStatus="status">
                                                <c:if test="${fn:contains(verifyUserList, vs)}">
                                                    <span class="font-green">${vs}</span>&nbsp;
                                                </c:if>
                                                <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                    <span>${vs}</span>&nbsp;
                                                </c:if>
                                            </c:forEach>

                                            <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                ${verifyUsers}
                                            </c:if>
                                        </c:if>
                                        <c:if test="${historicActivityInstance.size() != status.count}">
                                            ${hi.assignee}
                                        </c:if>
                                    </c:otherwise>
                                </c:choose>


                            </td>
                            <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                            <td>
                                <c:choose>
                                    <c:when test="${hi.activityType == 'startEvent'}">
                                        开始
                                    </c:when>
                                    <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                        结束
                                    </c:when>
                                    <c:otherwise>
                                        ${hi.activityName}
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td class="font-red">${commentMap[hi.taskId]}</td>
                        </tr>
                    </c:if>
                </c:forEach>
            </c:if>
            <!-- 查询无结果弹出 -->

            <c:if test="${empty historicActivityInstance}">
                <!-- 查询无结果弹出 -->
                <tr>
                    <td colspan="4">暂无审核记录。</td>
                </tr>
            </c:if>
            </tbody>
        </table>



    </div>

    <input type="hidden" name="formToken" value="${formToken}"/>
    <input type="hidden" id="taskName" name="taskName" value="${taskInfo.name}"/>
    <input type="hidden" id="path" value="<%=basePath%>"/>
</div>
<%--  <script type="text/javascript" src='<%=basePath%>/static/js/order/quote/edit_quote_detail.js?rnd=${resourceVersionKey}'></script> --%>
<%--<script type="text/javascript" src='<%= basePath %>static/js/logistics/warehouseOut/viewWarehouseOut.js?rnd=${resourceVersionKey}'></script>--%>
<%@ include file="../../common/footer.jsp"%>
