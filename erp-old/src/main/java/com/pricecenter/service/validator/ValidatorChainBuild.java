package com.pricecenter.service.validator;

import com.pricecenter.service.Validator;

/**
 * 简易的校验器的链式构造器类
 */
public class ValidatorChainBuild{

    private ValidatorChain validatorChain = new ValidatorChain();

    public static ValidatorChainBuild newBuild(){
        ValidatorChainBuild build = new ValidatorChainBuild();
        return build;
    }

    public ValidatorChain create(){
        return validatorChain;
    }

    public ValidatorChainBuild setPriceValidator(PriceValidator priceValidator){
        validatorChain.add(priceValidator);
        return this;
    }

    public ValidatorChainBuild setTraderNameValidator(TraderNameValidator traderNameValidator){
        validatorChain.add(traderNameValidator);
        return this;
    }

    public ValidatorChainBuild setSkuNoValidator(SkuNoValidator skuNoValidator){
        validatorChain.add(skuNoValidator);
        return this;
    }

    public ValidatorChainBuild setCustomerNameValidator(CustomerNameValidator customerNameValidator){
        validatorChain.add(customerNameValidator);
        return this;
    }

    public ValidatorChainBuild setValidator(Validator skuNoValidator){
        validatorChain.add(skuNoValidator);
        return this;
    }
}
