package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleServiceStandardApply;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardApplyDto;
import com.vedeng.aftersales.model.dto.MaintainInstallAreaDto;
import com.vedeng.aftersales.model.dto.ReferenceSimilarProductQueryDto;

import java.util.List;
import java.util.Map;

public interface AfterSaleServiceStandardApplyMapper {

    int deleteByPrimaryKey(Long serviceStandardApplyId);

    int insertSelective(AfterSaleServiceStandardApply record);

    AfterSaleServiceStandardApply selectByPrimaryKey(Long serviceStandardApplyId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardApply record);

    List<AfterSaleServiceStandardApplyDto> querylistPage(Map<String, Object> map);

    AfterSaleServiceStandardApply selectAfterSaleServiceStandardBySkuNo(String skuNo);

    void updateBySkuNo(AfterSaleServiceStandardApply afterSaleServiceStandardApply);

    List<AfterSaleServiceStandardApplyDto> querySimilarProductListPage(Map<String, Object> map);

    Map<String, String> queryMaxAndMinInstallFee(ReferenceSimilarProductQueryDto queryDto);

    List<AfterSaleServiceStandardApplyDto> maintainInstallAreaListPage(Map<String, Object> map);

    int updateByPrimaryKey(AfterSaleServiceStandardApply record);
}