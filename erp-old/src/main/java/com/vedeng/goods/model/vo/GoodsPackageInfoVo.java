package com.vedeng.goods.model.vo;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @description: 包装信息
 * @author: Laden.chu[<EMAIL>]
 * @createDate: 2021-06-10 16:02
 * @version: 1.0
 */
@Getter
@Setter
@NoArgsConstructor
public class GoodsPackageInfoVo {

    //是否启用多级包装
    private Integer isEnableMultistagePackage;

    //中包装数量
    private Integer midPackageNum;

    //箱包装数量
    private Integer boxPackageNum;
}
