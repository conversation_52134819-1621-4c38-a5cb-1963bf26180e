
$(function () {

    let checkSelectAll = function () {
        let selectFlag = true;

        $('.J-select-sku').each(function () {
            if (!$(this)[0].checked) {
                selectFlag = false;
            }
        })

        $('.J-select-list-all')[0].checked = selectFlag;
    };

    //表格全选复选框
    $('.J-select-list-all').on("click",function () {

        let $this = $(this);

        $('.J-select-sku').each(function () {
            $(this)[0].checked = $this[0].checked;
        })

    });

    $('.J-select-sku').on("click",function () {
        checkSelectAll();
    })

    // 获取所有销量
    let $input = $('input:checkbox[name="skuId"]');
    let skuIdArray = new Array($input.length);
    for(let i=0;i<$input.length;i++){
        skuIdArray[i]=$input[i].value;
    }
    console.log(skuIdArray);
    $.ajax({
        async:true,
        url:'./getOneThreeMonthLastYearSaleNum.do',
        data: JSON.stringify(skuIdArray),
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            console.log(data);
            for(let j=0;j<data.length;j++) {
                // console.log($("#lastYearSum"+data[j].skuId));
                // console.log(data[j].skuId);
                $('#lastYearSum'+data[j].skuId).text(data[j].lastYearSum);
                $('#lastYearPart'+data[j].skuId).text(data[j].lastYearPart);
                $('#threeMonthSum'+data[j].skuId).text(data[j].threeMonthSum);
                $('#threeMonthPart'+data[j].skuId).text(data[j].threeMonthPart);
                $('#oneMonthSum'+data[j].skuId).text(data[j].oneMonthSum);
                $('#oneMonthPart'+data[j].skuId).text(data[j].oneMonthPart);
            }
        },
        error:function(data){
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
});

function joinPool(obj) {

    // obj -- [skuId]
    console.log(obj);
    if(obj.length == 0){
        console.log("结束了..");
        layer.alert("请选择需要加入定品池的商品")
        return;
    }

    let array = new Array();
    for(let i=0;i<obj.length;i++){
        array[i] = {skuId:obj[i]};
    }

    let load = layer.msg('处理中', {
        icon: 16
        ,shade: 0.1
    });

    $.ajax({
        async:false,
        url:'./joinOrderingPool.do',
        data: JSON.stringify({
            idFlag:1,
            joinOrderingPoolOperateTempList:array
        }),
        type:"POST",
        dataType : "json",
        contentType: "application/json",
        success:function(data){
            layer.close(load);
            console.log(data);
            search();
        },
        error:function(data){
            layer.close(load);
            console.log(data);
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
            if(data.status ==400){
                layer.alert("当前操作异常")
            }
        }
    })
}

function updateCategory(obj,type){
    checkLogin();
    var parentId = $(obj).val();
    var level = Number($(obj).find("option:checked").attr("id"));
    if(parentId!="-1" && parentId!=-1){
        $.ajax({
            async:false,
            url:page_url + '/goods/category/getCategoryList.do',
            data:{"parentId":parentId},
            type:"POST",
            dataType : "json",
            success:function(data){
                if(data.code==0){
                    var list = data.listData;
                    if(list!=null && list.length>0){
                        $("#categoryId").val(0);
                        var ht = "<select id='categoryOpt' name='categoryOpt' style='width: 100px;' onchange='updateCategory(this,\""+type+"\");'>";
                        ht = ht + "<option value='-1' id='"+list[0].level+"'>请选择</option>";
                        for(var i=0;i<list.length;i++){
                            ht = ht + "<option value='"+list[i].categoryId+"' id='"+list[i].level+"'>"+list[i].categoryName+"</option>";
                        }
                        ht = ht + "</select>";
                        $(obj).parent().find("select:gt("+$(obj).index()+")").remove();
                        $(obj).parent().append(ht);
                    }else{
                        if (type == 'search') {
                            $("#categoryId").val(parentId);
                        } else if (type == 'change') {
                            $("#changeCategoryId").val(parentId);
                        }
                        $(obj).parent().find("select:gt("+$(obj).index()+")").remove();
                    }
                }else{
                    layer.alert("获取对应分类信息失败，请稍后重试或联系管理员！");
                    return false;
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    } else {
        if (type == 'search') {
            $("#categoryId").val(0);
        } else if (type == 'change') {
            $("#changeCategoryId").val(0);
        }
        //$("#category_div").find("select:gt("+$(obj).index()+")").remove();
        $(obj).parent().find("select:gt("+$(obj).index()+")").remove();
    }
}

/**
 * layui函数
 */
var callbackdata = function() {

    // 获取所有销量
    let $input = $('input:checkbox:checked[name="skuId"]');
    let skuIdArray = new Array($input.length);
    for(let i=0;i<$input.length;i++){
        skuIdArray[i]=$input[i].value;
    }
    // 加入定品池
    joinPool(skuIdArray);
    // console.log(skuIdArray);
    return skuIdArray;
}