package com.vedeng.temporal.service.impl;

import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoQueryRequest;
import com.vedeng.temporal.domain.dto.FlowOrderInfoResponse;
import com.vedeng.temporal.domain.entity.FlowOrderInfoEntity;
import com.vedeng.temporal.domain.enums.FlowOrderInfoStatus;
import com.vedeng.temporal.mapper.TemporalFlowOrderInfoMapper;
import com.vedeng.temporal.service.FlowOrderInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import cn.hutool.core.bean.BeanUtil;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务流程汇总信息服务实现类
 * 
 * 实现特点：
 * - 提供批量操作减少数据库交互
 * - 包含完整的状态验证逻辑
 * - 支持事务处理保证数据一致性
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-14
 */
@Slf4j
@Service
public class FlowOrderInfoServiceImpl implements FlowOrderInfoService {

    @Autowired
    private TemporalFlowOrderInfoMapper temporalFlowOrderInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFlowOrderInfo(FlowOrderInfoUpdateRequest request) {
        try {
            // 检查记录是否存在 - 使用 FlowNodeId + FlowOrderInfoType 组合查询
            FlowOrderInfoEntity existingEntity = getByFlowNodeIdAndType(
                    request.getFlowNodeId(),
                    request.getFlowOrderInfoType()
            );

            if (existingEntity == null) {
                // 记录不存在，创建新记录
                createFlowOrderInfo(request);
            } else {
                // 记录存在，执行更新
                updateExistingRecord(request, existingEntity);
            }

            log.info("汇总信息更新成功，节点ID: {}, 业务类型: {}, 业务编号: {}",
                    request.getFlowNodeId(), request.getFlowOrderInfoType(), request.getFlowOrderInfoNo());

        } catch (Exception e) {
            log.error("汇总信息更新失败，节点ID: {}, 业务类型: {}, 错误: {}",
                    request.getFlowNodeId(), request.getFlowOrderInfoType(), e.getMessage(), e);
            throw new RuntimeException("汇总信息更新失败: " + e.getMessage(), e);
        }
    }


    @Override
    public FlowOrderInfoResponse queryFlowOrderInfo(FlowOrderInfoQueryRequest request) {
        try {
            validateQueryRequest(request);

            FlowOrderInfoEntity entity = null;

            if (request.getFlowNodeId() != null) {
                // 优先使用 FlowNodeId + FlowOrderInfoType 组合查询
                if (request.getFlowOrderInfoType() != null) {
                    entity = getByFlowNodeIdAndType(request.getFlowNodeId(), request.getFlowOrderInfoType());
                } else {
                    // 如果没有指定业务类型，使用原方法但记录警告
                    log.warn("查询时未指定业务类型，可能返回不准确结果。FlowNodeId: {}", request.getFlowNodeId());
                    entity = getByFlowNodeId(request.getFlowNodeId());
                }
            } else if (request.getFlowOrderInfoNo() != null) {
                entity = temporalFlowOrderInfoMapper.selectByBusinessNo(request.getFlowOrderInfoNo());
            }

            return entity != null ? convertToResponse(entity) : null;

        } catch (Exception e) {
            log.error("查询汇总信息失败，错误: {}", e.getMessage(), e);
            throw new RuntimeException("查询汇总信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public FlowOrderInfoEntity getByFlowNodeId(Long flowNodeId) {
        if (flowNodeId == null || flowNodeId <= 0) {
            return null;
        }
        log.warn("使用 getByFlowNodeId 方法可能返回不准确结果，建议使用 getByFlowNodeIdAndType 方法。FlowNodeId: {}", flowNodeId);
        return temporalFlowOrderInfoMapper.selectByFlowNodeId(flowNodeId);
    }

    /**
     * 根据节点ID和业务类型查询记录
     * 推荐使用此方法，因为一个FlowNodeId可能对应多条不同类型的记录
     *
     * @param flowNodeId 节点ID
     * @param flowOrderInfoType 业务类型：0-采购，1-销售
     * @return 汇总信息实体，不存在时返回null
     */
    public FlowOrderInfoEntity getByFlowNodeIdAndType(Long flowNodeId, Integer flowOrderInfoType) {
        if (flowNodeId == null || flowNodeId <= 0) {
            return null;
        }
        if (flowOrderInfoType == null) {
            log.warn("业务类型为空，使用 getByFlowNodeId 方法。FlowNodeId: {}", flowNodeId);
            return getByFlowNodeId(flowNodeId);
        }

        List<FlowOrderInfoEntity> entities = temporalFlowOrderInfoMapper.selectByConditions(
                flowNodeId, flowOrderInfoType, null, false
        );
        return entities.isEmpty() ? null : entities.get(0);
    }

    @Override
    public boolean existsByFlowNodeId(Long flowNodeId) {
        return getByFlowNodeId(flowNodeId) != null;
    }

    /**
     * 检查指定节点ID和业务类型的记录是否存在
     * 推荐使用此方法，因为一个FlowNodeId可能对应多条不同类型的记录
     *
     * @param flowNodeId 节点ID
     * @param flowOrderInfoType 业务类型：0-采购，1-销售
     * @return true-存在，false-不存在
     */
    public boolean existsByFlowNodeIdAndType(Long flowNodeId, Integer flowOrderInfoType) {
        return getByFlowNodeIdAndType(flowNodeId, flowOrderInfoType) != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFlowOrderInfo(Long flowNodeId) {
        try {
            if (flowNodeId == null || flowNodeId <= 0) {
                throw new IllegalArgumentException("节点ID不能为空且必须大于0");
            }

            int result = temporalFlowOrderInfoMapper.logicalDeleteByFlowNodeId(flowNodeId);
            if (result == 0) {
                log.warn("删除汇总信息失败，记录不存在，节点ID: {}", flowNodeId);
            } else {
                log.info("删除汇总信息成功，节点ID: {}", flowNodeId);
            }

        } catch (Exception e) {
            log.error("删除汇总信息失败，节点ID: {}, 错误: {}", flowNodeId, e.getMessage(), e);
            throw new RuntimeException("删除汇总信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createFlowOrderInfo(FlowOrderInfoUpdateRequest request) {
        try {
            // 检查是否已存在 - 使用 FlowNodeId + FlowOrderInfoType 组合检查
            if (existsByFlowNodeIdAndType(request.getFlowNodeId(), request.getFlowOrderInfoType())) {
                throw new RuntimeException("节点ID和业务类型组合已存在汇总信息: FlowNodeId=" +
                        request.getFlowNodeId() + ", Type=" + request.getFlowOrderInfoType());
            }

            // 构建实体对象
            FlowOrderInfoEntity entity = buildEntityFromRequest(request);
            entity.setAddTime(new Date());
            entity.setModTime(new Date());
            entity.setIsDelete(0);

            // 执行插入
            temporalFlowOrderInfoMapper.insert(entity);

            log.info("创建汇总信息成功，节点ID: {}, 业务类型: {}, 记录ID: {}",
                    request.getFlowNodeId(), request.getFlowOrderInfoType(), entity.getFlowOrderInfoId());

            return entity.getFlowOrderInfoId();

        } catch (Exception e) {
            log.error("创建汇总信息失败，节点ID: {}, 业务类型: {}, 错误: {}",
                    request.getFlowNodeId(), request.getFlowOrderInfoType(), e.getMessage(), e);
            throw new RuntimeException("创建汇总信息失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<FlowOrderInfoEntity> findInconsistentRecords() {
        // TODO: 实现数据一致性检查逻辑
        // 这里需要根据具体的业务规则来检查数据一致性
        return new ArrayList<>();
    }

    @Override
    public void repairInconsistentData(FlowOrderInfoEntity entity) {
        // TODO: 实现数据修复逻辑
        // 这里需要根据具体的业务规则来修复数据
        log.info("修复数据不一致记录，记录ID: {}", entity.getFlowOrderInfoId());
    }

    // ==================== 私有方法 ====================


    /**
     * 验证查询请求参数
     */
    private void validateQueryRequest(FlowOrderInfoQueryRequest request) {
        if (request == null) {
            throw new IllegalArgumentException("查询请求不能为空");
        }
        if (!request.isValid()) {
            throw new IllegalArgumentException(request.getValidationError());
        }
    }

    /**
     * 更新现有记录
     */
    private void updateExistingRecord(FlowOrderInfoUpdateRequest request, FlowOrderInfoEntity existingEntity) {
        // 验证状态转换
        validateStatusTransition(request, existingEntity);

        // 构建更新实体
        FlowOrderInfoEntity updateEntity = buildEntityFromRequest(request);
        updateEntity.setFlowOrderInfoId(existingEntity.getFlowOrderInfoId());
        updateEntity.setModTime(new Date());

        // 执行更新
        int result = temporalFlowOrderInfoMapper.updateByPrimaryKey(updateEntity);
        if (result == 0) {
            throw new RuntimeException("更新汇总信息失败，记录不存在");
        }
    }

    /**
     * 验证状态转换
     */
    private void validateStatusTransition(FlowOrderInfoUpdateRequest request, FlowOrderInfoEntity existingEntity) {
        // 验证订单状态转换
        if (request.getOrderStatus() != null) {
            FlowOrderInfoStatus.OrderStatus from = FlowOrderInfoStatus.OrderStatus.fromCode(existingEntity.getOrderStatus());
            FlowOrderInfoStatus.OrderStatus to = FlowOrderInfoStatus.OrderStatus.fromCode(request.getOrderStatus());
            if (!FlowOrderInfoStatus.OrderStatus.isValidTransition(from, to)) {
                throw new RuntimeException("订单状态转换无效: " + from + " -> " + to);
            }
        }

        // 验证款项状态转换
        if (request.getPaymentStatus() != null) {
            FlowOrderInfoStatus.PaymentStatus from = FlowOrderInfoStatus.PaymentStatus.fromCode(existingEntity.getPaymentStatus());
            FlowOrderInfoStatus.PaymentStatus to = FlowOrderInfoStatus.PaymentStatus.fromCode(request.getPaymentStatus());
            if (!FlowOrderInfoStatus.PaymentStatus.isValidTransition(from, to)) {
                throw new RuntimeException("款项状态转换无效: " + from + " -> " + to);
            }
        }

        // 验证入库状态转换
        if (request.getStorageStatus() != null) {
            FlowOrderInfoStatus.StorageStatus from = FlowOrderInfoStatus.StorageStatus.fromCode(existingEntity.getStorageStatus());
            FlowOrderInfoStatus.StorageStatus to = FlowOrderInfoStatus.StorageStatus.fromCode(request.getStorageStatus());
            if (!FlowOrderInfoStatus.StorageStatus.isValidTransition(from, to)) {
                throw new RuntimeException("入库状态转换无效: " + from + " -> " + to);
            }
        }

        // 验证票据状态转换
        if (request.getInvoiceStatus() != null) {
            FlowOrderInfoStatus.InvoiceStatus from = FlowOrderInfoStatus.InvoiceStatus.fromCode(existingEntity.getInvoiceStatus());
            FlowOrderInfoStatus.InvoiceStatus to = FlowOrderInfoStatus.InvoiceStatus.fromCode(request.getInvoiceStatus());
            if (!FlowOrderInfoStatus.InvoiceStatus.isValidTransition(from, to)) {
                throw new RuntimeException("票据状态转换无效: " + from + " -> " + to);
            }
        }
    }

    /**
     * 合并更新请求
     */
    private FlowOrderInfoUpdateRequest mergeRequests(FlowOrderInfoUpdateRequest existing, FlowOrderInfoUpdateRequest replacement) {
        // 以最新的请求为准，但保留非空字段
        FlowOrderInfoUpdateRequest merged = BeanUtil.copyProperties(replacement, FlowOrderInfoUpdateRequest.class);
        
        // 合并扩展字段
        if (existing.hasExtendedFields() && replacement.hasExtendedFields()) {
            Map<String, Object> mergedFields = new HashMap<>(existing.getExtendedFields());
            mergedFields.putAll(replacement.getExtendedFields());
            merged.setExtendedFields(mergedFields);
        }
        
        return merged;
    }

    /**
     * 从请求构建实体对象
     */
    private FlowOrderInfoEntity buildEntityFromRequest(FlowOrderInfoUpdateRequest request) {
        FlowOrderInfoEntity entity = new FlowOrderInfoEntity();
        entity.setFlowNodeId(request.getFlowNodeId());
        entity.setFlowOrderInfoType(request.getFlowOrderInfoType());
        entity.setFlowOrderInfoNo(request.getFlowOrderInfoNo());
        entity.setOrderStatus(request.getOrderStatus());
        entity.setPaymentStatus(request.getPaymentStatus());
        entity.setStorageStatus(request.getStorageStatus());
        entity.setInvoiceStatus(request.getInvoiceStatus());
        entity.setInvoiceInfo(request.getInvoiceInfo());
        entity.setContractFileUrl(request.getContractFileUrl());
        entity.setContractFileName(request.getContractFileName());
        entity.setContractUploadTime(request.getContractUploadTime());
        
        // 设置操作人信息
        if (request.getOperatorId() != null) {
            entity.setCreator(request.getOperatorId());
            entity.setUpdater(request.getOperatorId());
        }
        if (request.getOperatorName() != null) {
            entity.setCreatorName(request.getOperatorName());
            entity.setUpdaterName(request.getOperatorName());
        }
        
        return entity;
    }

    /**
     * 转换为响应对象
     */
    private FlowOrderInfoResponse convertToResponse(FlowOrderInfoEntity entity) {
        return BeanUtil.copyProperties(entity, FlowOrderInfoResponse.class);
    }
}
