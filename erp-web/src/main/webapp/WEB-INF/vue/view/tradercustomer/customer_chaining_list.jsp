<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">
    <vxe-grid ref='xGrid' v-bind="gridOptions">
        <%--    表单搜索    --%>

        <template #form_trader_name="{ data }">
            <vxe-input v-model="data.traderName" maxlength="50" placeholder="请输入" clearable></vxe-input>
        </template>





        <template #form_belonger="{ data }">
            <el-select v-model="data.userId" filterable placeholder="请选择" prop="belongerId"
                       style="width: 100%;">
                <el-option
                        v-for="item in belongerOptions"
                        :key="item.userId"
                        :label="item.username"
                        :value="item.userId">
                </el-option>
            </el-select>

        </template>

            <template #form_arrival_time="{ data }">
                <el-date-picker size="small"
                                style="width: 100%;"
                                v-model="data.arrivalTimes"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                :default-time="['00:00:00', '23:59:59']">
                </el-date-picker>
            </template>


        <%--     表格数据   --%>
        <template #trader_name="{ row }">
            <span v-if="row.traderId == 0">{{row.traderName}}</span>
            <el-link type="primary" v-if="row.traderId != 0" @click="viewTraderDetail(row)">
                {{row.traderName}}
            </el-link>
        </template>

        <template #user_name="{ row }">
                <span>{{ row.username }}</span>
        </template>

        <template #operate="{ row }">
            <vxe-button @click="audit(row)"  size="small" type="text" status="primary">去审核</vxe-button>
        </template>

    </vxe-grid>



</div>

<script src="${pageContext.request.contextPath}/static/api/system/user.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    let vm0 = null;
    const sendThis0 = (_this) => {
        vm0 = _this;
    };
    const communicateState = '${communicateState}';

    new Vue({
        el: '#app',

        data() {

            return {
                loading: false,
                belongerOptions: "",
                currentRow: {},
                gridOptions: {
                    // height: '700px',
                    align: 'center',
                    border: true,
                    showHeaderOverflow: true,
                    showOverflow: true,
                    keepSource: true,
                    id: 'customer_chaining_list',
                    rowId: 'id',
                    rowConfig: {
                        isHover: true
                    },
                    columnConfig: {
                        resizable: true
                    },
                    customConfig: {
                        storage: true,
                    },
                    pagerConfig: {
                        pageSize: 10,
                        pageSizes: [10, 20, 50, 100, 200]
                    },

                    // 列表搜索区域
                    formConfig: {
                        titleWidth: 100,
                        titleAlign: 'right',
                        items: [
                            {
                                field: 'traderName',
                                title: '客户名称',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_trader_name'}
                            },
                            {
                                field: 'userId',
                                title: '归属销售',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_belonger'}
                            },
                            {
                                field: 'arrivalTimes',
                                title: '送到时间',
                                span: 6,
                                itemRender: {},
                                slots: {default: 'form_arrival_time'}
                            },
                            {
                                span: 24,
                                align: 'center',
                                collapseNode: false,
                                itemRender: {
                                    name: '$buttons',
                                    children: [
                                        {
                                            props: {
                                                type: 'submit',
                                                content: '搜索',
                                                status: 'primary'
                                            }
                                        },
                                        {
                                            props: {
                                                type: 'reset',
                                                content: '重置'
                                            }
                                        }
                                    ]
                                }
                            }
                        ]
                    },

                    // toolbarConfig: {
                    //     custom: true
                    // },

                    proxyConfig: {
                        seq: false, //启用动态序号代理，每一页的序号会根据当前页数变化
                        form: true, //启用表单代理，当点击表单提交按钮时会自动触发 reload 行为
                        props: {
                            result: 'list', // 配置响应结果列表字段
                            total: 'total' // 配置响应结果总页数字段
                        },
                        // 只接收Promise，具体实现自由发挥
                        ajax: {
                            // 当点击工具栏查询按钮或者手动提交指令 query或reload 时会被触发
                            query: ({page, sorts, filters, form}) => {
                                const queryParams = Object.assign({}, form);
                                console.log(queryParams)
                                if (queryParams.arrivalTimes != null && queryParams.arrivalTimes.length > 0) {
                                    queryParams.beginTime = queryParams.arrivalTimes[0]
                                    queryParams.endTime = queryParams.arrivalTimes[1]
                                }

                                let pageParams = {
                                    pageNum: page.currentPage,
                                    pageSize: page.pageSize,
                                    param: queryParams
                                };
                                console.log(pageParams)
                                return axios({
                                    url: '/traderCustomerTerminalApi/page.do',
                                    method: 'post',
                                    data: pageParams
                                }).then(response => response.data.data);
                            }
                        }
                    },
                    columns: [
                        {type: 'seq',title:'序号', width: 50},
                        {
                            field: 'traderName',
                            title: '客户名称',
                            // width: 250,
                            slots: {default: 'trader_name'},
                        },
                        {
                            field: 'username',
                            title: '归属销售',
                            // width: 100,
                            slots: {default: 'user_name'},
                            visible: true
                        },

                        {
                            field: 'arrivalTime',
                            title: '送到时间',
                            // width: 160,
                            formatter: this.formatDate,
                            visible: true
                        },

                        { title: '操作', width: 200, slots: { default: 'operate' } }
                    ],
                    editConfig: {
                        trigger: 'click',
                        mode: 'cell',
                        showStatus: true
                    },
                    checkboxConfig: {
                        reserve: true,
                        highlight: true,
                        range: true
                    }
                },
            }
        },

        watch: {
        },

        created() {
            getSaleUserList().then(res => {
                this.belongerOptions = res.data.data;
            });
            this.getDict()
            sendThis0(this);
        },

        mounted() {
            loadingApp()
            const $grid = this.$refs.xGrid
            // $grid.formData.time = [getLastMonthFirst(3), getNowDateLastTime()]
        },

        methods: {
            async getDict() {

                this.findSelectList()
            },


            selectBlur(e) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = e.target.value;
            },

            clickTrader(item) {
                const $table = this.$refs.xGrid
                $table.formData.traderName = item.traderName;
            },

            findSelectList() {
                const $grid = this.$refs.xGrid
                if ($grid) {
                    // const levelColumn = $grid.getColumnByField('bussinessLevel')
                    // levelColumn.editRender.options = this.businessChanceLevelList
                    // const tagColumn = $grid.getColumnByField('tagIdList')
                    // tagColumn.editRender.options = this.tagList
                    // const orderRateColumn = $grid.getColumnByField('orderRate')
                    // orderRateColumn.editRender.options = this.orderRateList

                }
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            formatDate({cellValue}) {
                return XEUtils.toDateString(cellValue, 'yyyy-MM-dd HH:mm:ss')
            },


            viewChanceDetail(row) {
                openTab("商机详情", '/businessChance/details.do?id=' + row.bussinessChanceId);
            },

            viewTraderDetail(row) {
                openTab("客户详情", '/trader/customer/new/portrait.do?traderId=' + row.traderId);
            },

            audit(row) {
                openTab("客户建链审核详情页", '/traderChaining/detail.do?traderId=' + row.traderId);
                this.closeThis()
            },

        }
    });


</script>

<style>
    .vxe-row > .vxe-col--6 {
        float: none;
    }
</style>


</body>
</html>
