package com.vedeng.goods.manager.extension.handler;

import com.vedeng.aftersales.dao.AfterSaleServiceStandardApplyMapper;
import com.vedeng.aftersales.model.AfterSaleServiceStandardApply;
import com.vedeng.todolist.service.ITodoInstance;
import com.vedeng.todolist.service.impl.MaintainDataAfterSalePolicy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class InternalAfterSalePolicyTodoHandler extends BaseGoodsTodoHandler {

    @Resource
    private MaintainDataAfterSalePolicy maintainDataAfterSalePolicy;
    @Resource
    private AfterSaleServiceStandardApplyMapper afterSaleServiceStandardApplyMapper;

    private final static Integer CHECK_SUCCESS = 2;

    @Override
    public int getServiceId() {
        return VD_AFTER_SALES_SERVICE_ID;
    }

    @Override
    public ITodoInstance getITodoInstance() {
        return maintainDataAfterSalePolicy;
    }

    @Override
    public boolean isDone() {
        AfterSaleServiceStandardApply afterSaleServiceStandardApply = afterSaleServiceStandardApplyMapper.selectAfterSaleServiceStandardBySkuNo(getSkuNo());
        return afterSaleServiceStandardApply != null && CHECK_SUCCESS.equals(afterSaleServiceStandardApply.getAfterSaleStandardStatus());
    }

    @Override
    public void onHandler() {
        if (!isDone()) {
            addGoodsTodoRecord(getGoodId(), getSkuNo());
        }
    }

    @Override
    public void finish(Object param) {
        if (param instanceof Long) {
            AfterSaleServiceStandardApply serviceStandardApplyQuery = afterSaleServiceStandardApplyMapper.selectByPrimaryKey((Long) param);
            if (StringUtils.isNotBlank(serviceStandardApplyQuery.getSkuNo())) {
                Integer skuId;
                try {
                    skuId = Integer.valueOf(serviceStandardApplyQuery.getSkuNo().trim().substring(1));
                } catch (Exception e) {
                    logger.error("处理贝登售后政策待办时解析skuId失败", e);
                    return;
                }
                maintainDataAfterSalePolicy.finish(skuId);
            }
        }
    }
}
