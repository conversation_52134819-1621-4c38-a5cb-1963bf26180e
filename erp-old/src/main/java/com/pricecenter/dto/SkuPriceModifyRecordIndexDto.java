package com.pricecenter.dto;

import com.vedeng.price.model.SkuPriceModifyRecord;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/8/18 17:32
 */
@Data
public class SkuPriceModifyRecordIndexDto extends SkuPriceModifyRecord {

    private String skuNo;

    private String skuName;

    private String brandName;

    private String unitName;

    private String model;

    private String modifyTimeString;

    private int marketPriceFluctuationDreaction;
    private BigDecimal marketPriceFluctuation;

    private int terminalPriceFluctuationDreaction;
    private BigDecimal terminalPriceFluctuation;

    private int distributionPriceFluctuationDreaction;
    private BigDecimal distributionPriceFluctuation;

    private int purchaseCostsFluctuationDreaction;
    private BigDecimal purchaseCostsFluctuation;

    private int groupPriceFluctuationDreaction;
    private BigDecimal groupPriceFluctuation;

    private int electronicCommercePriceFluctuationDreaction;
    private BigDecimal electronicCommercePriceFluctuation;

    private int researchTerminalPriceFluctuationDreaction;
    private BigDecimal researchTerminalPriceFluctuation;

    private int isHaveUnDealAffectOrder;

}
