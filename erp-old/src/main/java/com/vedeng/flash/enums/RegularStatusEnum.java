package com.vedeng.flash.enums;

/**
 * @Description:  定品状态
 * @Author:       davis
 * @Date:         2021/5/24 上午11:03
 * @Version:      1.0
 */
public enum RegularStatusEnum {

    ENABEL(0 , "启用"),
    DELETE(1, "删除");

    RegularStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(RegularStatusEnum enums : RegularStatusEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }

}
