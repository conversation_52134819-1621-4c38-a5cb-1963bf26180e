package com.vedeng.erp.kingdee.batch.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
    * 售后安调服务记录主档
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchAfterSalesInstallServiceRecordDto {
    private Integer afterSalesServiceId;

    /**
    * 售后单ID
    */
    private Integer afterSalesId;

    /**
    * 本次验收时间
    */
    private Date checkDate;

    /**
    * 验收方式，1电话回访2纸单验收3短信通知
    */
    private Integer checkType;

    /**
    * 录音ID
    */
    private String recordId;

    /**
    * 验收结论(字典)
    */
    private Integer checkConclusion;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 创建者
    */
    private Integer creator;

    /**
    * 更新时间
    */
    private Date modTime;

    /**
    * 更新者
    */
    private Integer updater;

    /**
    * 是否删除 0否 1是
    */
    private Boolean isDelete;
}