package com.vedeng.workbench.dao;

import com.vedeng.workbench.model.dto.BussinessCommunicateRecordDto;
import com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto;
import com.vedeng.workbench.model.dto.WorkbenchDataQueryDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;


public interface WorkbenchBussinessChanceExtMapper extends com.vedeng.workbench.dao.generate.WorkbenchBussinessChancMapperGenerate {


    List<WorkbenchBussinessChanceExtDto> getThisWeekExpectOrderChanceListById( WorkbenchDataQueryDto dateQueryDto);


    List<WorkbenchBussinessChanceExtDto> getTodayToCommunicateBussinessChanceById(WorkbenchDataQueryDto dataQueryDto);

    List<BussinessCommunicateRecordDto> getCommunicateRecordByBussinessChanceId(Integer bussinessChanceId);

    List<WorkbenchBussinessChanceExtDto> getAllBussinessChanceList(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getTodayNewAddBussinessChnaceListById(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getExpectOrderBussinessChanceListByIds(WorkbenchDataQueryDto dataQueryDto);


    List<WorkbenchBussinessChanceExtDto> getThisWeekNewAddImportChanceListByIds(WorkbenchDataQueryDto dataQueryDto);


    List<WorkbenchBussinessChanceExtDto> getTwoDayUncontactChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getThisWeekCoreFailChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getThisWeekExpectFailChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getPlanCommunicateChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getYesterdayContactChanceListByIds(WorkbenchDataQueryDto dataQueryDto);

    List<WorkbenchBussinessChanceExtDto> getThisWeekNewAddChanceListByIds(WorkbenchDataQueryDto dataQueryDto);
}