package com.vedeng.common.feign.annotations;


import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Feign接口标记注解
 *
 * <AUTHOR>
 */
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface FeignApi {

    /**
     * 调用的服务地址
     */
    String serverUrl() default "";

    /**
     * 调用的服务名称
     */
    String serverName();

    boolean defaultFeign() default true;

    int maxConnTotal() default 20;

    int timeout() default 2000;

    int retry() default 5;

    boolean keepAlive() default false;
}
