package com.vedeng.erp.saleorder.domain.dto;

import lombok.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 科研购活动生产者dto
 * @date 2022/12/24 15:02
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KYGActivityPreOrderProducerDto {

    /**
     * 商机编号
     */
    private String businessNo;
    /**
     * 商机状态  1进行中（未创建订单&未关闭）、2已建单（已关联订单）、3已成单【已创建订单&（关联的订单已经部分收款|全部收款）】、4已关闭（商机关闭）
     */
    private Integer businessStatus;
    /**
     * 订单编号
     */
    private String saleOrderNo;
    /**
     * 订单状态 0已创建未付款 1已付款 2已关闭
     */
    private Integer saleOrderStatus;
    /**
     * 客户id
     */
    private Integer customerId;


    /**
     * 创建
     */
    public KYGActivityPreOrderProducerDto create(Integer customerId,String businessNo,String saleOrderNo) {
        return KYGActivityPreOrderProducerDto
                .builder()
                .businessNo(businessNo)
                .saleOrderNo(saleOrderNo)
                .customerId(customerId)
                .businessStatus(2)
                .saleOrderStatus(0)
                .build();
    }

    /**
     * 支付
     */
    public KYGActivityPreOrderProducerDto pay(Integer customerId,String businessNo,String saleOrderNo) {
        return KYGActivityPreOrderProducerDto
                .builder()
                .businessNo(businessNo)
                .saleOrderNo(saleOrderNo)
                .customerId(customerId)
                .businessStatus(3)
                .saleOrderStatus(1)
                .build();
    }

    /**
     * 关闭
     */
    public KYGActivityPreOrderProducerDto close(String businessNo) {
        return KYGActivityPreOrderProducerDto
                .builder()
                .businessNo(businessNo)
                .businessStatus(4)
                .build();
    }
}
