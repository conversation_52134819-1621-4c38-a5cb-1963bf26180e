package com.vedeng.trader.service.search;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.primitives.Ints;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.finance.dao.CapitalBillMapper;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.dao.TraderCustomerAttributeMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.TraderCustomerAttribute;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Component
public class CustomerSearchConditionsAssembler {

    private static final Integer TRADER_TYPE_CUSTOMER = 1;

    private static final Integer ON = 1;
    private static final Integer OFF = 2;

    @Value("${http_url}")
    private String httpUrl;

    @Value("${client_id}")
    private String clientId;
    @Value("${client_key}")
    protected String clientKey;

    @Resource
    private TraderContactMapper traderContactMapper;
    @Resource
    private TraderCustomerAttributeMapper traderCustomerAttributeMapper;
    @Resource
    private BussinessChanceMapper bussinessChanceMapper;
    @Resource
    private CapitalBillMapper capitalBillMap;
    @Resource
    private TraderCustomerMapper traderCustomerMapper;
    @Resource
    private CommunicateRecordMapper communicateRecordMapper;


    public void assemble(ListCustomerQuery listCustomerQuery, TraderCustomerVo traderCustomerVo) {
        Objects.requireNonNull(listCustomerQuery, "listCustomerQuery is null");
        Objects.requireNonNull(traderCustomerVo, "traderCustomerVo is null");

        if (Objects.nonNull(traderCustomerVo.getLockStatus())){
            listCustomerQuery.setLockStatus(traderCustomerVo.getLockStatus());
        }
        if (CollectionUtils.isNotEmpty(traderCustomerVo.getUserIdList())) {
            listCustomerQuery.setUserIdList(traderCustomerVo.getUserIdList());
        }

        //客户来源
        if (traderCustomerVo.getSource() != null) {
            listCustomerQuery.setSource(traderCustomerVo.getSource());
        }

        if (traderCustomerVo.getPublicTraderCustomerIdList() != null) {
            listCustomerQuery.setPublicTraderCustomerIdList(traderCustomerVo.getPublicTraderCustomerIdList());
        }

        if (StringUtils.isNotBlank(traderCustomerVo.getName())) {
            listCustomerQuery.setCustomerName(traderCustomerVo.getName().trim());
        }

        if (NumberUtil.isPositive(traderCustomerVo.getCustomerType())) {
            listCustomerQuery.setCustomerType(traderCustomerVo.getCustomerType());
        }

        if (NumberUtil.isPositive(traderCustomerVo.getCustomerNature())) {
            listCustomerQuery.setCustomerNature(traderCustomerVo.getCustomerNature());
        }

        //客户审核状态
        if (traderCustomerVo.getCustomerStatus() != null) {
            listCustomerQuery.setCustomerStatus(traderCustomerVo.getCustomerStatus());
        }

        //客户分类
        if (NumberUtil.greaterAndEqualZero(traderCustomerVo.getCustomerCategory())) {
            listCustomerQuery.setCustomerCategory(traderCustomerVo.getCustomerCategory());
        }

        if (CollectionUtils.isNotEmpty(traderCustomerVo.getCategoryList())) {
            listCustomerQuery.setCategoryList(traderCustomerVo.getCategoryList());
        }

        if (NumberUtil.isPositive(traderCustomerVo.getBelongPlatform())) {
            listCustomerQuery.setPlatformNo(traderCustomerVo.getBelongPlatform());
        }

        if (traderCustomerVo.getCustomerLevel() != null) {
            listCustomerQuery.setCustomerLevel(traderCustomerVo.getCustomerLevel());
        }

        if (NumberUtil.isPositive(traderCustomerVo.getAreaId())) {
            listCustomerQuery.setAreaId(traderCustomerVo.getAreaId());
        }

        //客户资质审核状态
        if (traderCustomerVo.getAptitudeStatus() != null) {
            listCustomerQuery.setAptitudeStatus(traderCustomerVo.getAptitudeStatus());
        }

        if (traderCustomerVo.getSmallScore() != null) {
            listCustomerQuery.setSmallScore(traderCustomerVo.getSmallScore());
        }

        if (traderCustomerVo.getBigScore() != null) {
            listCustomerQuery.setBigScore(traderCustomerVo.getBigScore());
        }

        if (traderCustomerVo.getUserEvaluate() != null) {
            listCustomerQuery.setUserEvaluate(traderCustomerVo.getUserEvaluate());
        }

        if (traderCustomerVo.getBasicMedicalDealer() != null) {
            listCustomerQuery.setBasicMedicalDealer(traderCustomerVo.getBasicMedicalDealer());
        }

        //是否改价
        if (traderCustomerVo.getIsLimitprice() != null) {
            listCustomerQuery.setIsLimitedPrice(traderCustomerVo.getIsLimitprice());
        }

        //有报价相关的客户
        if (ON.equals(traderCustomerVo.getQuote()) || OFF.equals(traderCustomerVo.getQuote())) {
            listCustomerQuery.setHasQuoted(ON.equals(traderCustomerVo.getQuote()) ? CommonConstants.ON : CommonConstants.OFF);
        }

        //是否有合作
        if (StringUtils.isNotEmpty(traderCustomerVo.getCooperate())) {
            listCustomerQuery.setIsCooperated(ON.equals(Ints.tryParse(traderCustomerVo.getCooperate())) ? CommonConstants.ON : CommonConstants.OFF);
        }

        //根据客户联系方式
        if (StringUtils.isNotBlank(traderCustomerVo.getContactWay())) {
            List<Integer> hasContactCustomerIdList = traderContactMapper.getTraderIdListByContactWay(TRADER_TYPE_CUSTOMER, traderCustomerVo.getContactWay().trim());
            listCustomerQuery.setTraderIdList(CollectionUtils.isNotEmpty(hasContactCustomerIdList) ? hasContactCustomerIdList : ListCustomerQuery.NOT_FOUND_SENTINEL);
        }

        //是否添加微信信息
        if (ON.equals(traderCustomerVo.getWxStatus()) || OFF.equals(traderCustomerVo.getWxStatus())) {
            List<Integer> boundWxCustomerIdList = traderContactMapper.getTraderIdListByWxStatus(TRADER_TYPE_CUSTOMER, traderCustomerVo.getWxStatus());
            listCustomerQuery.setWxTraderIdList(CollectionUtils.isNotEmpty(boundWxCustomerIdList) ? boundWxCustomerIdList : ListCustomerQuery.NOT_FOUND_SENTINEL);
        }

        //战略合作伙伴
        if (traderCustomerVo.getPartnerId() != null && traderCustomerVo.getPartnerId() != 0) {
            TraderCustomerAttribute traderCustomerAttribute = new TraderCustomerAttribute();
            traderCustomerAttribute.setAttributeCategoryId(SysOptionConstant.ID_CUSTOMER_ATTRIBUTE_CATEGORY_26);
            traderCustomerAttribute.setAttributeId(traderCustomerVo.getPartnerId());
            List<Integer> list = traderCustomerAttributeMapper.getTraderIdListByParentId(traderCustomerAttribute);
            listCustomerQuery.setParenterTraderIdList(CollectionUtils.isNotEmpty(list) ? list : ListCustomerQuery.NOT_FOUND_SENTINEL);
        }

        //VDERP-2465  客户中心-ERP客户列表改动 客户分群信息
        if (traderCustomerVo.getTraderGroupId() != null && traderCustomerVo.getTraderGroupId() > 0) {
            List<Integer> traderIdList = traderCustomerMapper.getTraderIdByTraderGroupId(traderCustomerVo.getTraderGroupId());
            listCustomerQuery.setSearchTraderGroupList(CollectionUtils.isNotEmpty(traderIdList) ? traderIdList : ListCustomerQuery.NOT_FOUND_SENTINEL);
        }

        if (traderCustomerVo.getTimeType() != null) {
            listCustomerQuery.setTimeType(traderCustomerVo.getTimeType());
        }

        if (traderCustomerVo.getStartTime() != null) {
            listCustomerQuery.setStartTime(traderCustomerVo.getStartTime());
        }

        if (traderCustomerVo.getEndTime() != null) {
            listCustomerQuery.setEndTime(traderCustomerVo.getEndTime());
        }

        //税号审核状态
        if (traderCustomerVo.getFinanceCheckStatus() != null) {
            listCustomerQuery.setFinanceCheckStatus(traderCustomerVo.getFinanceCheckStatus());
        }

        //交易时间
        if (traderCustomerVo.getTimeType() != null && traderCustomerVo.getTimeType() == 2
           &&(traderCustomerVo.getStartTime()!=null||traderCustomerVo.getEndTime()!=null)) {
            List<Integer> capTraderIds = capitalBillMap.getCapitalBillTreaderIdList(traderCustomerVo.getStartTime(), traderCustomerVo.getEndTime());
            listCustomerQuery.setCapitalBillTraderIdList(CollectionUtils.isNotEmpty(capTraderIds) ? capTraderIds : ListCustomerQuery.NOT_FOUND_SENTINEL);
        }

        //查询沟通记录和询价产品
        if (StringUtils.isNotBlank(traderCustomerVo.getSearchMsg())) {

            listCustomerQuery.setSearchMsg(StringUtils.trim(traderCustomerVo.getSearchMsg()));
// 2023-11-01 此处不能使用中间变量的形式查询，可能查出来级大的数据量
//            Set<Integer> traderIdSetToUse = new HashSet<>();
//
//            List<BussinessChanceVo> bizChangeList = bussinessChanceMapper.getLastMonthBussinessChance(null, null, traderCustomerVo.getSearchMsg());
//            if (CollectionUtils.isNotEmpty(bizChangeList)) {
//                List<Integer> fromBizChangeList = bizChangeList.stream().map(BussinessChanceVo::getTraderId).filter(Objects::nonNull).collect(Collectors.toList());
//                if (CollectionUtils.isNotEmpty(fromBizChangeList)) {
//                    traderIdSetToUse.addAll(fromBizChangeList);
//                }
//            }
//
//            //根据信息搜索查询沟通记录id集合获取到traderId的集合
//            List<Integer> comIds = listHasCommunicateRecordTraderIds(traderCustomerVo);
//            if (CollectionUtils.isNotEmpty(comIds)) {
//                traderIdSetToUse.addAll(comIds);
//            }
//
//            if (CollectionUtils.isNotEmpty(listCustomerQuery.getTraderIdList())) {
//                traderIdSetToUse.addAll(listCustomerQuery.getTraderIdList());
//            }
//
//            listCustomerQuery.setTraderIdList(traderIdSetToUse);
        }

        //客户提醒
        if (traderCustomerVo.getCustomerAlert() != null && traderCustomerVo.getCustomerAlert() > 0) {
            listCustomerQuery.setCustomerAlert(traderCustomerVo.getCustomerAlert());
        }

        // 公海 原归属销售
        if (traderCustomerVo.getOriginUserId() != null) {
            listCustomerQuery.setOriginUserId(traderCustomerVo.getOriginUserId());
        }

        // 公海 挖掘价值类型
        if (traderCustomerVo.getConditionType() != null) {
            listCustomerQuery.setConditionType(traderCustomerVo.getConditionType());
        }

        if (CollectionUtils.isNotEmpty(traderCustomerVo.getOriginUserList())){
            listCustomerQuery.setOriginUserList(traderCustomerVo.getOriginUserList());
        }

        // 解析营销类型 属性值 0-1-1
        if(CollectionUtils.isNotEmpty(traderCustomerVo.getBusinessTerminalCategoriesAndType())){
            traderCustomerVo.getBusinessTerminalCategoriesAndType().stream().map(s -> s.split(StrUtil.DASHED))
                    .forEach(arr -> {
                        if (arr.length >= 1) {
                            String traderCustomerMarketingType = arr[0];
                            listCustomerQuery.getTraderCustomerMarketingTypeList().add(traderCustomerMarketingType);
                        }
                        if (arr.length >= 2) {
                            String institutionType = arr[1];
                            listCustomerQuery.getInstitutionTypeList().add(institutionType);
                        }
                        if (arr.length >= 3) {
                            String institutionTypeChild = arr[2];
                            listCustomerQuery.getInstitutionTypeChildList().add(institutionTypeChild);
                        }
                    });
            listCustomerQuery.setTraderCustomerMarketingTypeList(listCustomerQuery.getTraderCustomerMarketingTypeList().stream().distinct().collect(Collectors.toList()));
            listCustomerQuery.setInstitutionTypeList(listCustomerQuery.getInstitutionTypeList().stream().distinct().collect(Collectors.toList()));
            listCustomerQuery.setInstitutionTypeChildList(listCustomerQuery.getInstitutionTypeChildList().stream().distinct().collect(Collectors.toList()));
        }


        //经销商有效性
        if (traderCustomerVo.getEffectiveness() != null) {
            listCustomerQuery.setEffectiveness(traderCustomerVo.getEffectiveness());
        }
        //终端机构性质
        if (traderCustomerVo.getInstitutionNature() != null) {
            listCustomerQuery.setInstitutionNature(String.valueOf(traderCustomerVo.getInstitutionNature()));
        }
        //终端机构评级
        if (traderCustomerVo.getInstitutionLevel() != null) {
            listCustomerQuery.setInstitutionLevel(traderCustomerVo.getInstitutionLevel());
        }
        //主营商品类型
        if (traderCustomerVo.getSkuType() != null) {
            listCustomerQuery.setSkuType(traderCustomerVo.getSkuType());
        }
        //主营商品范畴类别
        if (traderCustomerVo.getTraderCustomerMainCategoryType() != null) {
            listCustomerQuery.setTraderCustomerMainCategoryType(String.valueOf(traderCustomerVo.getTraderCustomerMainCategoryType()));
        }
        //主营商品范畴
        if (traderCustomerVo.getTraderCustomerMainCategory() != null) {
            listCustomerQuery.setTraderCustomerMainCategory(traderCustomerVo.getTraderCustomerMainCategory());
        }
        //销售类别
        if (traderCustomerVo.getTraderCustomerOwnership() != null) {
            listCustomerQuery.setTraderCustomerOwnership(String.valueOf(traderCustomerVo.getTraderCustomerOwnership()));
        }
        //核心资源
        if (traderCustomerVo.getTraderCustomerDevelopLevel() != null) {
            listCustomerQuery.setTraderCustomerDevelopLevel(traderCustomerVo.getTraderCustomerDevelopLevel());
        }
        //客户等级
        if (StringUtils.isNotBlank(traderCustomerVo.getTraderCustomerLevelGrade())) {
            listCustomerQuery.setTraderCustomerLevelGrade(traderCustomerVo.getTraderCustomerLevelGrade());
        }
        //生命周期
        if (StringUtils.isNotBlank(traderCustomerVo.getTraderCustomerLifeCycle())) {
            listCustomerQuery.setTraderCustomerLifeCycle(traderCustomerVo.getTraderCustomerLifeCycle());
        }
        //交易分类
        if (CollectionUtil.isNotEmpty(traderCustomerVo.getTraderCustomerTrade())) {
            listCustomerQuery.setTraderCustomerTrade(traderCustomerVo.getTraderCustomerTrade());
        }
        //交易分类类别
        if (traderCustomerVo.getTraderCustomerTradeType() != null) {
            listCustomerQuery.setTraderCustomerTradeType(traderCustomerVo.getTraderCustomerTradeType());
        }

        // 交易品牌
        if (CollectionUtil.isNotEmpty(traderCustomerVo.getTraderCustomerBrand())) {
            listCustomerQuery.setTraderCustomerBrand(traderCustomerVo.getTraderCustomerBrand());
        }

        // 代理品牌、代理商品、政府关系
        if (CollectionUtils.isNotEmpty(traderCustomerVo.getAgencyBrandIdList())) {
            listCustomerQuery.setAgencyBrandIdList(traderCustomerVo.getAgencyBrandIdList());
        }
        if (StringUtils.isNotBlank(traderCustomerVo.getOtherAgencyBrand())) {
            listCustomerQuery.setOtherAgencyBrand(traderCustomerVo.getOtherAgencyBrand());
        }
        if (CollectionUtils.isNotEmpty(traderCustomerVo.getAgencySkuList())) {
            listCustomerQuery.setAgencySkuList(traderCustomerVo.getAgencySkuList());
        }
        if (StringUtils.isNotBlank(traderCustomerVo.getOtherAgencySku())) {
            listCustomerQuery.setOtherAgencySku(traderCustomerVo.getOtherAgencySku());
        }
        if (StringUtils.isNotBlank(traderCustomerVo.getGovernmentRelation())) {
            listCustomerQuery.setGovernmentRelation(traderCustomerVo.getGovernmentRelation());
            // 只有政府关系选择其他时，才传入 “其他政府关系”
            if (ErpConst.PRINT_HC_TYPE_F.equals(traderCustomerVo.getGovernmentRelation()) && StringUtils.isNotBlank(traderCustomerVo.getOtherGovernmentRelation())) {
                listCustomerQuery.setOtherGovernmentRelation(traderCustomerVo.getOtherGovernmentRelation());
            }
        }

        if (Objects.nonNull(traderCustomerVo.getSharedSaleId())) {
            listCustomerQuery.setSharedSaleId(traderCustomerVo.getSharedSaleId());
        }

        // 交易品牌类型
        if (traderCustomerVo.getTraderCustomerBrandType() != null) {
            listCustomerQuery.setTraderCustomerBrandType(traderCustomerVo.getTraderCustomerBrandType());
        }
        // 沟通记录
        if (traderCustomerVo.getTraderCustomerCommunicate() != null) {
            listCustomerQuery.setTraderCustomerCommunicate(traderCustomerVo.getTraderCustomerCommunicate());
        }
        // 历史沟通次数
        if (traderCustomerVo.getTraderCustomerCommunicateTimes() != null) {
            listCustomerQuery.setTraderCustomerCommunicateTimes(traderCustomerVo.getTraderCustomerCommunicateTimes());
        }
        // 有无手机
        if (traderCustomerVo.getTraderCustomerIsMobile() != null) {
            listCustomerQuery.setTraderCustomerIsMobile(traderCustomerVo.getTraderCustomerIsMobile());
        }
        // 是否公海
        if (traderCustomerVo.getTraderCustomerIsSea() != null) {
            listCustomerQuery.setTraderCustomerIsSea(traderCustomerVo.getTraderCustomerIsSea());
        }
        // 贝登会员
        if (traderCustomerVo.getTraderCustomerIsMember() != null) {
            listCustomerQuery.setTraderCustomerIsMember(traderCustomerVo.getTraderCustomerIsMember());
        }
       // 交易分类
        if (CollectionUtil.isNotEmpty(traderCustomerVo.getTraderCustomerTrade())) {
            listCustomerQuery.setTraderCustomerTrade(traderCustomerVo.getTraderCustomerTrade());
        }
        //地区类型
        if (traderCustomerVo.getAreaType() != null) {
            listCustomerQuery.setAreaType(traderCustomerVo.getAreaType());
        }
        // 新加的查询字段 标签完善度 人工更新标签
        if (Objects.isNull(traderCustomerVo.getSelectLabelPerfectionValue())) {
            traderCustomerVo.setSelectLabelPerfectionValue(0);
        }
        listCustomerQuery.setSelectLabelPerfectionValue(traderCustomerVo.getSelectLabelPerfectionValue());
        if(Objects.nonNull(traderCustomerVo.getSelectLabelPerfectionValue()) && traderCustomerVo.getSelectLabelPerfectionValue()==0){
            if(CollectionUtils.isNotEmpty(traderCustomerVo.getSelectLabelPerfectionChildValue())){
                if(traderCustomerVo.getSelectLabelPerfectionChildValue().size()==2){
                    listCustomerQuery.setSelectLabelPerfectionMin(0);
                    listCustomerQuery.setSelectLabelPerfectionMax(100);
                }else{
                    for (int value:traderCustomerVo.getSelectLabelPerfectionChildValue()) {
                        if(value==0){
                            listCustomerQuery.setSelectLabelPerfectionMin(0);
                            listCustomerQuery.setSelectLabelPerfectionMax(80);
                        }else if(value==20){
                            listCustomerQuery.setSelectLabelPerfectionMin(81);
                            listCustomerQuery.setSelectLabelPerfectionMax(100);
                        }else{
                            listCustomerQuery.setSelectLabelPerfectionMin(0);
                            listCustomerQuery.setSelectLabelPerfectionMax(100);
                        }
                    }
                }
            }
        }
        listCustomerQuery.setSelectLabelPerfectionChildValue(traderCustomerVo.getSelectLabelPerfectionChildValue());
        listCustomerQuery.setSelectLabelPerfectionChildValueOne(traderCustomerVo.getSelectLabelPerfectionChildValueOne());
        listCustomerQuery.setManualUpdate(traderCustomerVo.getManualUpdate());

        if(CollectionUtils.isNotEmpty(traderCustomerVo.getQueryGongHaiLevelList())){
            listCustomerQuery.setQueryGongHaiLevelList(traderCustomerVo.getQueryGongHaiLevelList());
        }

        if(CollectionUtils.isNotEmpty(traderCustomerVo.getQueryInvalidReasonList())){
            listCustomerQuery.setQueryInvalidReasonList(traderCustomerVo.getQueryInvalidReasonList());
        }

    }


    private List<Integer> listHasCommunicateRecordTraderIds(TraderCustomerVo traderCustomerVo) {
        try {
            ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_COMUNICATE_IDS,
                    traderCustomerVo, clientId, clientKey, new TypeReference<ResultInfo<?>>() {
                    });
            if (result == null || result.getCode() == -1) {
                return null;
            }
            List<Integer> cmIds = (List<Integer>) result.getData();
            if (cmIds != null && cmIds.size() > 0) {
                List<Integer> traderIdList = traderCustomerVo.getTraderList();
                Map<String, Object> map = new HashMap<>();
                map.put("companyId", traderCustomerVo.getCompanyId());
                map.put("traderType", 1);
                map.put("comIdList", cmIds);
                List<Integer> traderIds = communicateRecordMapper.getTraderIdListByList(map);
                if (traderIdList != null && traderIdList.size() > 0) {
                    traderIdList.addAll(traderIds);
                } else {
                    traderIdList = new LinkedList<>(traderIds);
                }

                return traderIdList;
            }
        } catch (IOException e) {
            //ignore
        }

        return null;
    }


}
