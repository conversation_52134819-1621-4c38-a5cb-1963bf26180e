package com.vedeng.goods.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * V_CORE_SKU
 * <AUTHOR>
@Data
public class CoreSku implements Serializable {
    private Integer skuId;

    /**
     * V_GOODS_SPU.GOODS_SPU_ID
     */
    private Integer spuId;

    /**
     * 审核状态 1待完善 2返回修改 3审核通过 4删除
     */
    private Integer checkStatus;

    /**
     * 制造商型号
     */
    private String model;

    /**
     * 规格
     */
    private String spec;

    /**
     * 订货号
     */
    private String skuNo;

    /**
     * 通用名
     */
    private String skuName;

    /**
     * 商品名称
     */
    private String showName;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 供应商型号
     */
    private String supplyModel;

    /**
     * 是否备货

     */
    private String isStockup;

    /**
     * Wiki链接
     */
    private String wikiHref;

    /**
     * 技术参数
     */
    private String technicalParameter;

    /**
     * 性能参数
     */
    private String performanceParameter;

    /**
     * 规格参数
     */
    private String specParameter;

    /**
     * SKU商品单位
     */
    private Integer baseUnitId;

    /**
     * 最小起订量
     */
    private BigDecimal minOrder;

    /**
     * 商品长
     */
    private BigDecimal goodsLength;

    /**
     * 商品宽
     */
    private BigDecimal goodsWidth;

    /**
     * 商品高
     */
    private BigDecimal goodsHeight;

    /**
     * 包装体积 长
     */
    private BigDecimal packageLength;

    /**
     * 包装体积 宽
     */
    private BigDecimal packageWidth;

    /**
     * 包装体积 高
     */
    private BigDecimal packageHeight;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 换算单位 最小单位
     */
    private Integer unitId;

    /**
     * 换算数量 内含最小商品数量
     */
    private Long changeNum;

    /**
     * 包装清单
     */
    private String packingList;

    /**
     * 1 包安装 2包培训 3 物流点自提 4送货上门，含卸货上楼 5送货上门，含卸货到楼下 
     */
    private String afterSaleContent;

    /**
     * 质保年限  年
     */
    private String qaYears;

    /**
     * 存储条件（温度），1常温（已废弃），现为：常温0-30℃、2冷冻（已废弃），现为：阴凉0-20℃、3冷藏（已废弃），现为：冷藏2-10℃、4其他温度（启用温度范围值）
     */
    private Integer storageConditionOne;

    /**
     * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
     */
    private Double storageConditionOneLowerValue;

    /**
     * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
     */
    private Double storageConditionOneUpperValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较小的值
     */
    private Double storageConditionHumidityLowerValue;

    /**
     * 存储条件（湿度，单位：%）：范围值存储较大的值
     */
    private Double storageConditionHumidityUpperValue;

    /**
     * 存储条件（其他），1阴凉（已废弃）, 现为“通风”、2干燥、3避光、4防潮、5避热、6密封、7密闭、8严封 9遮光 逗号隔开
     */
    private String storageConditionTwo;

    /**
     * 产品有效期单位 1 天   2 月  3 年
     */
    private Boolean effectiveDayUnit;

    /**
     * 产品有效期
     */
    private String effectiveDays;

    /**
     * 质保期限规则
     */
    private String qaRule;

    /**
     * 质保外维修价格 元
     */
    private BigDecimal qaOutPrice;

    /**
     * 响应时间 小时
     */
    private Long qaResponseTime;

    /**
     * 有无备用机
     */
    private String hasBackupMachine;

    /**
     * 供应商延保价格
     */
    private BigDecimal supplierExtendGuaranteePrice;

    /**
     * 核心零部件附件ID
     */
    private Integer corePartsPriceFid;

    /**
     * 退货条件 1 允许退货  0不允许退货
     */
    private Boolean returnGoodsConditions;

    /**
     * 运费说明
     */
    private String freightIntroductions;

    /**
     * 换货条件
     */
    private String exchangeGoodsConditions;

    /**
     * 换货方式
     */
    private String exchangeGoodsMethod;

    /**
     * 商品备注
     */
    private String goodsComments;

    /**
     * 1启用 0禁用
     */
    private Integer status;

    /**
     * 添加事件
     */
    private Date addTime;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 修改人
     */
    private Integer updater;

    /**
     * 审核时间
     */
    private Date checkTime;

    /**
     * 审核人
     */
    private Integer checker;

    /**
     * 运营信息ID
     */
    private Integer operateInfoId;

    /**
     * 删除原因
     */
    private String deleteReason;

    /**
     * 最后审核不通过原因
     */
    private String lastCheckReason;

    /**
     * 税收分类编码
     */
    private String taxCategoryNo;

    /**
     * 贝登精选市场价
     */
    private BigDecimal jxMarketPrice;

    /**
     * 贝登精销售价
     */
    private BigDecimal jxSalePrice;

    /**
     * 是否精选商品
     */
    private Integer jxFlag;

    /**
     * 来源0ERP 1耗材商城 (用于老耗材图片展示)
     */
    private Byte source;

    /**
     * (二进制)0未推送  1贝登推送 2医械购推送 4科研购
     */
    private Integer pushStatus;

    /**
     * 填报预计发货区间
     */
    private String declareDeliveryRange;

    private Integer priceVerifyStatus;

    /**
     * 最近一年平均单价（定时任务）
     */
    private BigDecimal avgprice;

    /**
     * 最近成单销售（定时任务）
     */
    private Integer latestValidOrderUser;

    /**
     * 最近一年平均单价上次计算时间（定时任务，超过一年，AVGPRICE置为0）
     */
    private Date avgpriceUpdateTime;

    /**
     * 价格中心终端价
     */
    private BigDecimal terminalPrice;

    /**
     * 价格中心分销价
     */
    private BigDecimal distributionPrice;

    /**
     * 价格中心成本价
     */
    private BigDecimal costPrice;

    /**
     * 可用库存
     */
    private Integer availableStockNum;

    /**
     * 库存量
     */
    private Integer stockNum;

    /**
     * 上架状态(二进制)0未上架 1贝登上架 2医械购上架 4科研购
     */
    private Byte onSale;

    /**
     * 商品条形码
     */
    private String goodsBarcode;

    /**
     * 养护类型 重点养护0 一般养护1 不养护2 
     */
    private Boolean curingType;

    /**
     * 养护原因
     */
    private String curingReason;

    /**
     * 是否必须检测报告 1-是  0-否
     */
    private Boolean isNeedTestReprot;

    /**
     * 是否套件 1-是  0-否
     */
    private Boolean isKit;

    /**
     * 有哪几个套件
     */
    private String kitDesc;

    /**
     * 套件中子件的SN码是否必须一致 1-是  0-否
     */
    private Boolean isSameSnCode;

    /**
     * 是否厂家赋SN码 1-是  0-否
     */
    private Boolean isFactorySnCode;

    /**
     * 是否管理贝登追溯码 1-是  0-否
     */
    private Boolean isManageVedengCode;

    /**
     * 是否异形品 1-是  0-否
     */
    private Boolean isBadGoods;

    /**
     * 是否启用厂家批号 1-是  0-否
     */
    private Boolean isEnableFactoryBatchnum;

    /**
     * 是否启用多级包装 1-是  0-否
     */
    private Boolean isEnableMultistagePackage;

    /**
     * 中包装数量
     */
    private Integer midPackageNum;

    /**
     * 箱包装数量
     */
    private Integer boxPackageNum;

    /**
     * 是否启用效期 1-是  0-否
     */
    private Byte isEnableValidityPeriod;

    /**
     * 近效期预警天数
     */
    private Integer nearTermWarnDays;

    /**
     * 超近效期控制天数
     */
    private Integer overNearTermWarnDays;

    /**
     * 安装培训类型 0-无服务、1-电话指导、2-上门安装培训
     */
    private Boolean installTrainType;

    /**
     * 物流安装类型  0-送货上门不含卸货、1-送货上门含卸货、2-物流点自提 
     */
    private Boolean logisticsDeliverytype;

    /**
     * 是否需报备 0否 1是
     */
    private Boolean isNeedReport;

    /**
     * 是否获得授权 0否 1是
     */
    private Boolean isAuthorized;

    /**
     * 归属区域商户id集合,用逗号隔开
     */
    private String orgIdList;

    /**
     *  已推送的区域商城id集合
     */
    private String pushedOrgIdList;

    /**
     * 是否可售
     */
    private Integer isAvailableSale;

    /**
     * 禁用原因
     */
    private String disabledReason;

    /**
     * 采购到货时长
     */
    private Integer purchaseTime;

    /**
     * 是否库存管理
     */
    private Integer haveStockManage;
    /**
     * 关联费用id
     */
    private Integer costCategoryId;
    /**
     * 是否虚拟商品
     */
    private Integer isVirtureSku;
    /**
     * 虚拟商品关联费用编码时间
     */
    private Date virtureTime;
    /**
     * 虚拟商品关联费用编码操作人
     */
    private Integer virtureCreator;

    /**
     * 税收编码操作记录
     */
    private String taxCategoryNoRecord;

    private static final long serialVersionUID = 1L;
}