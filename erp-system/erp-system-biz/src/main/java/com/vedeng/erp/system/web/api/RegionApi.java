package com.vedeng.erp.system.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.system.dto.TagDto;
import com.vedeng.erp.system.service.RegionService;
import com.vedeng.erp.system.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.system.web.api
 * @Date 2022/7/21 20:47
 */
@ExceptionController
@RestController
@RequestMapping("/region")
@Slf4j
public class RegionApi {

    @Autowired
    private RegionService regionService;

    /**
     * 获取组装好的省市区三级级联数据
     * @return list
     */
    @RequestMapping(value = "/getCascaderRegionOptions")
    @NoNeedAccessAuthorization
    public R<?> getCascaderRegionOptions() {
        return R.success(regionService.getCascaderRegionOptions());
    }

    /**
     * 获取组装好的省市区三级级联数据
     * @return list
     */
    @RequestMapping(value = "/getCascaderChannelOptions")
    @NoNeedAccessAuthorization
    public R<?> getCascaderChannelOptions() {
        return R.success(regionService.getCascaderChannelOptions());
    }
}
