package com.vedeng.activiti.taskassign;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.order.service.OrderCommonService;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.LogicalAfterorderChooseService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;
import java.util.UUID;

public class EditAfterSaleExecutionListener   implements ExecutionListener {

	public static Logger logger = LoggerFactory.getLogger(EditAfterSaleExecutionListener.class);
    // 运行时注入service
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private AfterSalesService afterSalesOrderService = (AfterSalesService) context.getBean("afterSalesOrderService");
	private BuyorderInfoSyncService buyorderInfoSyncService = (BuyorderInfoSyncService) context.getBean("buyorderInfoSyncServiceImpl");
    private LogicalAfterorderChooseService logicalAfterorderChooseService = (LogicalAfterorderChooseService) context.getBean("logicalAfterorderChooseService");
	private OrderCommonService orderCommonService= (OrderCommonService) context.getBean("orderCommonServiceImpl");


    //售后审核触发器
    //根据穿参通用回写主表中状态
    @Override
	public void notify(DelegateExecution execution) throws Exception {
		ServletRequestAttributes ra= (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		HttpServletRequest request =  ra.getRequest();
		User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
		AfterSalesVo afterSales = (AfterSalesVo) execution.getVariable("afterSalesInfo");
		//afterSales.setTraderType(2);
		afterSales.setValidStatus(1);//已生效
		afterSales.setValidTime(DateUtil.sysTimeMillis());
		afterSales.setStatus(2);//审核通过
		afterSales.setAtferSalesStatus(1);//进行中
		afterSales.setCompanyId(user.getCompanyId());
		afterSales.setPayer(user.getCompanyName());
		afterSales.setModTime(DateUtil.sysTimeMillis());
		afterSales.setUpdater(user.getUserId());
		ResultInfo<?> res = afterSalesOrderService.editApplyAudit(afterSales);
		if(res != null && res.getCode().equals(0)){
			//更新售后单updateDataTime
			orderCommonService.updateAfterOrderDataUpdateTime(afterSales.getAfterSalesId(),null, OrderDataUpdateConstant.AFTER_ORDER_VAILD);

			GlobalThreadPool.submitMessage(new Runnable() {
				@Override
				public void run() {
					try {
						String uuid = UUID.randomUUID().toString().replace("-", "");
						logger.info("开始执行chooseLogicalAfterorder，uuid:{}",uuid);
						logicalAfterorderChooseService.chooseLogicalAfterorder(afterSales,user);
						logger.info("结束执行chooseLogicalAfterorder，uuid:{}",uuid);
					}catch (Exception e){
						logger.error("EditAfterSaleExecutionListener error:",e);
					}
				}
			});

		}
		try {
			logger.info("dealRefundInvoiceSendStatus afterSalesId:{}", afterSales.getAfterSalesId());
			ResultInfo resultInfo = afterSalesOrderService.dealRefundInvoiceSendStatus(afterSales.getAfterSalesId());
			if (ResultInfo.error().getCode().equals(resultInfo.getCode())){
				logger.info("dealRefundInvoiceSendStatus warn afterSalesId:{}", afterSales.getAfterSalesId());
			}
			// 计算更新售后单的 退票状态
			buyorderInfoSyncService.calculateAndUpdateInvoiceRefundStatus(afterSales.getAfterSalesId());
			logger.info("售后单审核通过计算并更新退票状态，售后单id：{}", afterSales.getAfterSalesId());
		} catch (Exception e) {
			logger.error("dealRefundInvoiceSendStatus warn", e);
		}
	}
}
