<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:choose>
    <c:when test="${costCategoryId == 0}">
        <c:set var="title" value="新增费用类别" scope="application"/>
    </c:when>
    <c:otherwise>
        <c:set var="title" value="编辑费用类别" scope="application"/>
    </c:otherwise>
</c:choose>
<%@ include file="../../common/common.jsp" %>
<%@ include file="../../component/remarkComponent.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>

<style>
    .main_content {
        margin: 20px 10px;
        padding-top: 10px;
        font-size: 10px;
    }
    .xm-icon-sousuo{
        max-height: 1px !important;
    }
</style>
<body>
<div class="layui-form main_content" lay-filter="test1">
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px"><font style="color: red">*</font>ERP费用类别名称:</label>
        <div class="f_left" style="width: 500px">
            <input id="categoryName" type="text" name="categoryName" lay-verify="required" class="layui-input">
            <input id="oldCategoryName" type="hidden" name="oldCategoryName">
            <input id="unitKingDeeNo" type="hidden" name="unitKingDeeNo">
            <input id="oldKingDeeCategoryName" type="hidden" name="oldKingDeeCategoryName">
            <input type="hidden" name="costCategoryId" value="${costCategoryId}">
        </div>
        <div style="margin-left: 20px">
            <font style="color: #00b7ee">不可与现有的费用名称重名</font>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px"><span style="color: red">*</span>金蝶费用科目:</label>
        <div class="f_left">
            <div class="">
                <div id="mxx-select" name="kingDeeCategory"></div>
                <input type="hidden" name="kingDeeId" id="kingDeeId" value="${kingDeeId}">
                <input type="hidden" name="kingDeeName" id="kingDeeName" value="${kingDeeName}">
            </div>
        </div>
    </div>
    <div class="layui-form-item">
        <label class="layui-form-label" style="width: 150px" ><span style="color: red">*</span>是否需采购:</label>
        <div class="f_left">
            <div class="layui-input-block">
                <div style="margin-left: -111px">
                    <select name="purchase" lay-filter="aihao" id="purchase">
                        <option value=""></option>
                        <option value="0" selected="">否</option>
                        <option value="1">是</option>
                    </select>
                </div>
            </div>
        </div>
        <div style="margin-left: 20px;">
            <div style="color: #00b7ee; margin-left: 333px; padding-top: 12px;">区分大类下的虚拟商品是否进待采购列表</div>
        </div>
    </div>
    <div style="text-align: center">
        <button id="close-layer" type="button" class="layui-btn layui-btn-primary layui-btn-sm">关闭</button>
        <button id="confirmSubmit" class="layui-btn layui-btn-normal layui-btn-sm" type="button">提交</button>
    </div>
</div>
</body>

<script>

    var form = layui.form;

    const costCategoryId = ${costCategoryId};

    layui.use('form', function () {

        //方法提交
        $('#confirmSubmit').on('click', function (data) {
            let categoryName = $('#categoryName').val().trim();
            let oldCategoryName = $('#oldCategoryName').val();
            let oldKingDeeCategoryName = $('#oldKingDeeCategoryName').val();
            let kingDeeNo = $('#kingDeeId').val()
            let kingDeeCategory = $('#kingDeeCategory').val()
            let purchase = $('#purchase').val()

            if (!categoryName) {
                layer.alert("费用类别名称不可为空！");
                return false;
            }
            if (categoryName.length > 30) {
                layer.alert("费用名称不能大于30个字！");
                return false;
            }
            if (!kingDeeNo){
                layer.alert("金蝶费用科目不可为空！");
                return false;
            }
            if (!purchase){
                layer.alert("是否需采购不可为空！");
                return false;
            }


            if (costCategoryId === 0) {
                $.ajax({
                    url: '/costCategory/add.do',
                    type: 'post',
                    dataType: 'json',
                    data: {"categoryName": categoryName,
                            "unitKingDeeNo":kingDeeNo,
                            "purchase":purchase},
                    success: function (data) {
                        if (data.code === 0) {
                            window.parent.location.reload();
                            layer.close(index);
                        } else {
                            layer.alert(data.message);
                        }

                    }
                })
            } else {
                $.ajax({
                    url: '/costCategory/update.do',
                    type: 'post',
                    dataType: 'json',
                    data: {
                        "costCategoryId": costCategoryId,
                        "categoryName": categoryName,
                        "oldCategoryName": oldCategoryName,
                        "unitKingDeeNo":kingDeeNo,
                        "oldUnitKingDeeName":oldKingDeeCategoryName,
                        "purchase":purchase
                    },
                    success: function (data) {
                        if (data.code === 0) {
                            window.parent.location.reload();
                            layer.close(index);
                        } else {
                            layer.alert(data.message);
                        }
                    }
                })
            }
        });


        $('#close-layer').on('click', function () {
            var layerIndex = $("#layerIndex").val();
            layer.close(layerIndex);
        })
    });
    var sel = xmSelect.render({
        el: '#mxx-select',
        tips: '输入选择',
        searchTips: '请输入',
        style: {
            marginLeft: '0',
            width: '500px'
        },
        direction: 'down',
        height: '450px',
        name: 'kingDeeCategory',
        clickClose: true,
        radio: true,
        autoRow: true,
        filterable: true,
        paging: true,
        pageSize: 10,
        remoteSearch: true,
        model: {
            icon: 'hidden',
            label: {
                type: 'text'
            }
        },
        remoteMethod: function (val, cb, show) {
            console.log(val)
            $.ajax({
                type: "post",
                url: "/costCategory/getKingCostCategory.do",
                data: {"category": val},
                dataType: "json",
                success: function (data) {
                    var arr = [];
                    console.log(data)
                    if (data.data.length > 0) {
                        data.data.forEach(function (item) {
                            var i = {
                                value: htmlEncode(item.costCategoryKingDeeNo),
                                name: htmlEncode(item.costCategoryKingDeeNo) + "-" + htmlEncode(item.costCategoryKingDeeName),
                            };
                            arr.push(i)
                        })

                    }
                    cb(arr)
                },
                error: function (data) {
                    cb([])
                }
            });
        },
        on: function (data) {
            if (data.arr.length > 0) {
                $('#kingDeeId').val(data.arr[0].value);
                $('#kingDeeName').val(data.arr[0].name);
            }

        }
    });
    var htmlEncode = function (s){
        s = s.replace(/&/g, "&amp;");//必须在第一个位置
        s = s.replace(/</g, "&lt;");
        s = s.replace(/>/g, "&gt;");
        s = s.replace(/'/g, "&#39;");
        s = s.replace(/"/g, "&quot;");

        return s;
    }

    $(function () {
        if (costCategoryId !== 0) {
            console.log(costCategoryId)
            $.ajax({
                url: "/costCategory/getCostCategoryInfo.do",
                data: {"costCategoryId": costCategoryId},
                dataType: 'json',
                async:false,
                success: function (data) {
                    if (data.code === 0) {
                        $('#categoryName').val(data.data.categoryName);
                        $('#oldCategoryName').val(data.data.categoryName);
                        $('#unitKingDeeNo').val(data.data.unitKingDeeNo);
                        $('#purchase').val(data.data.purchase);
                        form.render();
                    }
                },
                error: function (data) {
                    if (data.status === 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }
        let unitKingDeeNo = $('#unitKingDeeNo').val()
        console.log(unitKingDeeNo)
        if (unitKingDeeNo !== "" ){
            $.ajax({
                type: "post",
                url:"/costCategory/getKingDeeCostCategory.do",
                data:{"categoryNo": unitKingDeeNo},
                dataType: 'json',
                success: function (data) {
                    if (data.code === 0) {
                        sel.setValue([{name:htmlEncode(data.data.costCategoryKingDeeNo)+'-'+htmlEncode(data.data.costCategoryKingDeeName),value:htmlEncode(data.data.costCategoryKingDeeNo)}])
                        $('#oldKingDeeCategoryName').val(htmlEncode(data.data.costCategoryKingDeeNo)+'-'+htmlEncode(data.data.costCategoryKingDeeName));
                        $('#kingDeeId').val(htmlEncode(data.data.costCategoryKingDeeNo));
                        $('#kingDeeName').val(htmlEncode(data.data.costCategoryKingDeeNo)+'-'+htmlEncode(data.data.costCategoryKingDeeName));
                    }
                }
            })
        }
    })




</script>