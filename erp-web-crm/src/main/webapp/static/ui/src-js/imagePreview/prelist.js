Vue.component('ui-img-preview-list', {
    template: `<div>
        <div class="ui-img-preview-list">
            <div class="ui-img-item" v-for="(item, index) in imgList">
                <img :src="item" alt="" @error="handlerImgError">
                <div class="ui-img-preview-handler" @click="previewImg(index)">
                    <i class="vd-ui_icon icon-see"></i>
                </div>
            </div>
        </div>
        <div class="J-preview__wrap" v-if="previewList.length">
            <ui-image-preview ref="preview" :urlList="previewList" :visible.sync="isShowImgPreview" :init="imgPreviewIndex"></ui-image-preview>
        </div>
    </div>`,
    props: {
        // 按钮类型
        imgList: {
            type: Array,
            default() {
                return []
            }
        },
    },
    data() {
        return {
            isShowImgPreview: false,
            imgPreviewIndex: 1,
            previewList: [],
        }
    },
    watch: {
        isShowImgPreview() {
            if(this.isShowImgPreview) {
                document.body.style.overflow = 'hidden'
            } else {
                document.body.style.overflow = ''
            }
        }
    },
    mounted() {
        this.imgList.forEach(item => {
            this.previewList.push({
                pic: item
            })
        });

        console.log(this.previewList)
    },
    methods: {
        previewImg(index) {
            console.log('index:' + index)
            this.imgPreviewIndex = index;
            this.isShowImgPreview = true;
           
            document.body.append(this.$refs.preview.$el);
        },
        handlerImgError(e) {
            e.target.src="/static/image/img-error.png";
        }
    }
})