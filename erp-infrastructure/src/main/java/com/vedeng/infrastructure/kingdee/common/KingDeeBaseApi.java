package com.vedeng.infrastructure.kingdee.common;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.*;
import com.vedeng.infrastructure.kingdee.domain.command.*;
import com.vedeng.infrastructure.kingdee.domain.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶接口
 * @date 2022/8/27 12:40
 */
@Component
@Slf4j
public class KingDeeBaseApi extends KingDeeRecoverHandle {


    private static final String ERROR_CODE = "ErrorCode";
    private static final String DOWNLOAD_ERROR_MSG_TEMPLATE = "金蝶下载回单接口异常";
    private static final String GET_FORM_ID = "getFormId";
    private static final long DELAY_TIME = 30 * 1000L;
    private static final double MULTIPLIER = 1.5D;
    private static final int MAX_RECURSION_DEPTH = 5;


    /**
     * 新增
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public RepoStatus save(SaveExtCommand<?> command) {
        Assert.notNull(command, "金蝶扩展save参数对象不能为空");
        try {
            SaveResult saveResult = new K3CloudApi().save(command.getFormId(), command);
            if (!saveResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(saveResult));
                return saveResult.getResult().getResponseStatus();
            }
            RepoStatus responseStatus = saveResult.getResult().getResponseStatus();
            ArrayList<SuccessEntity> successEntitys = saveResult.getResult().getResponseStatus().getSuccessEntitys();
            CollUtil.getFirst(successEntitys).setNeedReturnData(saveResult.getResult().getNeedReturnData());
            return responseStatus;
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }


    /**
     * 修改
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public RepoStatus update(UpdateExtCommand<?> command) {
        Assert.notNull(command, "金蝶扩展update参数对象不能为空");
        try {
            SaveResult saveResult = new K3CloudApi().update(command.getFormId(), command);
            if (!saveResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(saveResult));
                return saveResult.getResult().getResponseStatus();
            }
            RepoStatus responseStatus = saveResult.getResult().getResponseStatus();
            ArrayList<SuccessEntity> successEntitys = saveResult.getResult().getResponseStatus().getSuccessEntitys();
            CollUtil.getFirst(successEntitys).setNeedReturnData(saveResult.getResult().getNeedReturnData());
            return responseStatus;
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 批量新增
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> batchSave(BatchSaveExtCommand<?> command) {
        Assert.notNull(command, "金蝶扩展批量save参数对象不能为空");
        try {
            SaveResult saveResult = new K3CloudApi().batchSave(command.getFormId(), command);
            if (!saveResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(saveResult));
                return new ArrayList<>();
            }
            return saveResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 提交
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> submit(OperateExtCommand command) {
        Assert.notNull(command, "金蝶扩展audit参数对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().submit(command.getFormId(), command);
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return new ArrayList<>();
            }
            return operatorResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 审核
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> audit(OperateExtCommand command) {
        Assert.notNull(command, "金蝶扩展audit参数对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().audit(command.getFormId(), command);
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return new ArrayList<>();
            }
            return operatorResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }


    /**
     * 反审核
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> unAudit(OperateExtCommand command) {
        Assert.notNull(command, "金蝶扩展unAudit数对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().unAudit(command.getFormId(), command);
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return new ArrayList<>();
            }
            return operatorResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 删除
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> delete(OperateExtCommand command) {
        Assert.notNull(command, "金蝶扩展delete参数对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().delete(command.getFormId(), command);
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return new ArrayList<>();
            }
            return operatorResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }


    /**
     * 反审核
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> unAudit(String formId, OperateParam command) {
        Assert.notNull(command, "金蝶扩展unAudit数对象不能为空");
        Assert.notNull(formId, "金蝶扩展delete参数formId对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().unAudit(formId, command);
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return new ArrayList<>();
            }
            return operatorResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 删除
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public ArrayList<SuccessEntity> delete(String formId, OperateParam command) {
        Assert.notNull(command, "金蝶扩展delete参数对象不能为空");
        Assert.notNull(formId, "金蝶扩展delete参数formId对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().delete(formId, command);
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return new ArrayList<>();
            }
            return operatorResult.getResult().getResponseStatus().getSuccessEntitys();
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public RepoStatus workflowAudit(WorkflowAuditExtCommand command) {
        Assert.notNull(command, "金蝶扩展workflowAudit参数对象不能为空");
        try {
            OperatorResult operatorResult = new K3CloudApi().workflowAudit(command.toJson());
            if (!operatorResult.getResult().getResponseStatus().isIsSuccess()) {
                log.error("调用金蝶接口异常{}", new Gson().toJson(operatorResult));
                return operatorResult.getResult().getResponseStatus();
            }
            RepoStatus responseStatus = operatorResult.getResult().getResponseStatus();
            ArrayList<SuccessEntity> successEntitys = operatorResult.getResult().getResponseStatus().getSuccessEntitys();
            CollUtil.getFirst(successEntitys).setNeedReturnData(operatorResult.getResult().getNeedReturnData());
            return responseStatus;
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 查询
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public <T> List<T> query(KingDeeQueryExtParam param, Class<T> tClass) {
        Assert.notNull(param, "金蝶扩展查询参数对象不能为空");
        Assert.notNull(tClass, "金蝶扩展查询返回类不能为空");
        Field[] fields = ReflectUtil.getFields(tClass);
        HashMap<String, Field> fieldMap = MapUtil.newHashMap(fields.length, true);
        for (Field field : fields) {
            if (field.isSynthetic()) {
                continue;
            }
            fieldMap.put(field.getName(), field);
        }
        Set<String> keys = fieldMap.keySet();
        try {
            String fieldKeys = StrUtil.join(",", keys);
            param.setFieldKeys(fieldKeys);
            List<List<Object>> list = new K3CloudApi().executeBillQuery(param.toJson());
            return parseResult(list, fieldMap, tClass);
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    /**
     * 判断当前查询结果是否存在
     *
     * @param t t
     * @return true已存在 false不存在
     */
    public <T> Boolean isExist(T t) {
        KingDeeQueryExtParam queryParam = getKingDeeQueryExtParam(t, null);
        return CollUtil.isNotEmpty(this.queryReturnMap(queryParam));
    }

    /**
     * 判断当前查询结果是否存在
     *
     * @param t t
     * @return true已存在 false不存在
     */
    public <T> List<Map<String, Object>> isExist(T t, List<String> returnFields) {
        String fields = String.join(",", returnFields);
        KingDeeQueryExtParam queryParam = getKingDeeQueryExtParam(t, fields);
        return this.queryReturnMap(queryParam);
    }

    private <T> KingDeeQueryExtParam getKingDeeQueryExtParam(T t, String returnField) {
        String formId = ReflectUtil.invoke(t, GET_FORM_ID);
        if (StrUtil.isBlank(formId)) {
            log.error(t.getClass().getName() + "未重写getFormId方法,请检查");
            throw new KingDeeException(t.getClass().getName() + "未重写getFormId方法,请检查");
        }
        String businessId = "";
        String field = "";
        Field[] fields = ReflectUtil.getFields(t.getClass());
        if (ArrayUtil.isNotEmpty(fields)) {
            for (Field fie : fields) {
                Annotation[] declaredAnnotations = fie.getDeclaredAnnotations();
                for (Annotation declaredAnnotation : declaredAnnotations) {
                    if (declaredAnnotation.annotationType().equals(BusinessID.class)) {
                        businessId = Convert.toStr(ReflectUtil.getFieldValue(t, fie));
                        BusinessID annotation = fie.getAnnotation(BusinessID.class);
                        field = StrUtil.isBlank(annotation.value()) ? fie.getName() : annotation.value();
                        break;
                    }
                }
            }
        }
        if (StrUtil.isBlank(businessId) || StrUtil.isBlank(field)) {
            log.error(t.getClass().getName() + "对应businessId无值或者无注解,请检查");
            throw new KingDeeException(t.getClass().getName() + "对应businessId无值或者@businessId无注解,请检查");
        }

        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(formId);
        queryParam.setFieldKeys(StrUtil.isNotEmpty(returnField) ? returnField : field);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder()
                .fieldName(field)
                .value(businessId)
                .build());
        queryParam.setFilterString(queryFilterDtos);
        return queryParam;
    }


    /**
     * 查询并返回list<map<>> 可以使用query 方法
     */
    @Deprecated
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public List<Map<String, Object>> queryReturnMap(KingDeeQueryExtParam param) {
        Assert.notNull(param, "金蝶扩展查询参数对象不能为空");
        try {
            List<List<Object>> list = new K3CloudApi().executeBillQuery(param.toJson());
            return parseResult(list, param);
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }


    /**
     * 金蝶方自定义接口查询
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public <T> T getSysReportData(String formId, KingDeeSysReportParam param, Class<T> tClass) {
        Assert.notNull(param, "金蝶自定义查询参数对象不能为空");
        Assert.notNull(tClass, "金蝶自定义查询返回类不能为空");
        try {
            String resultJson = new K3CloudApi().getSysReportData(formId, param.toJson());
            if (resultJson.contains(ERROR_CODE)) {
                log.error("调用查询金蝶自定义接口异常：{}," + resultJson);
                throw new KingDeeException("金蝶查询接口异常" + resultJson);
            }
            return JSON.parseObject(resultJson, tClass);
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶自定义查询接口异常，异常信息" + JSON.toJSONString(e));
        }
    }

    /**
     * 下载
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public byte[] downloadFile(KingDeeFileDataDto param) {
        try {
            String res = new K3CloudApi().attachmentDownLoad(param.toJson());
            KingDeeFileDataResultDto fileDataResultDto = JSON.parseObject(res, KingDeeFileDataResultDto.class);

            Assert.notNull(fileDataResultDto, DOWNLOAD_ERROR_MSG_TEMPLATE);
            Assert.notNull(fileDataResultDto.getResult(), DOWNLOAD_ERROR_MSG_TEMPLATE);
            Assert.notNull(fileDataResultDto.getResult().getResponseStatus(), DOWNLOAD_ERROR_MSG_TEMPLATE);
            Assert.isTrue(fileDataResultDto.getResult().getResponseStatus().getIsSuccess(), DOWNLOAD_ERROR_MSG_TEMPLATE);
            if (StrUtil.isNotEmpty(fileDataResultDto.getResult().getFilePart())) {
                return Base64.decode(fileDataResultDto.getResult().getFilePart());
            }
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
        return null;
    }

    /**
     * 附件上传
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public RepoRet attachmentUpload(Object param) {
        try {
            K3CloudApi api = new K3CloudApi();
            String result = api.attachmentUpload(JSON.toJSONString(param));
            return JSON.parseObject(result, RepoRet.class);
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }


    /**
     * 转换查询结果为bean对象
     *
     * @param result 金蝶结果
     * @param key    入参
     * @return List<Map < String, Object>>
     */

    public <T> List<T> parseResult(List<List<Object>> result, Map<String, Field> key, Class<T> tClass) {
        if (result == null) {
            throw new KingDeeException("查询金蝶接口异常，返回信息为null");
        }
        List<T> parseData = new ArrayList<>();
        if (CollectionUtil.isEmpty(result)) {
            return parseData;
        }
        String jsonString = JSON.toJSONString(result);
        if (jsonString.contains(ERROR_CODE)) {
            log.error("调用查询金蝶接口异常：{}", jsonString);
            throw new KingDeeException("金蝶查询接口异常" + jsonString);
        }
        List<String> arrayList = CollUtil.newArrayList(key.keySet());
        for (List<Object> data : result) {
            T newInstance = ReflectUtil.newInstance(tClass);
            for (int i = 0; i < arrayList.size(); i++) {
                ReflectUtil.setFieldValue(newInstance, key.get(arrayList.get(i)), data.get(i));
            }
            parseData.add(newInstance);
        }
        return parseData;
    }

    /**
     * 转换查询结果 后期优化为指定的对象，不要使用map
     *
     * @param result     金蝶结果
     * @param queryParam 入参
     * @return List<Map < String, Object>>
     */
    public List<Map<String, Object>> parseResult(List<List<Object>> result, KingDeeQueryExtParam queryParam) {
        if (result == null) {
            throw new KingDeeException("查询金蝶接口异常，返回信息为null");
        }
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }
        String jsonString = JSON.toJSONString(result);
        if (jsonString.contains(ERROR_CODE)) {
            log.error("调用查询金蝶接口异常：{}", jsonString);
            throw new KingDeeException("金蝶查询接口异常" + jsonString);
        }
        List<String> strings = StrUtil.splitTrim(queryParam.getFieldKeys().toUpperCase(), ",");
        List<Map<String, Object>> parseData = new ArrayList<>();
        for (List<Object> data : result) {
            Map<String, Object> parseItem = new HashMap<>();
            for (int i = 0; i < strings.size(); i++) {
                parseItem.put(strings.get(i), data.get(i));
            }
            parseData.add(parseItem);
        }
        return parseData;
    }

    /**
     * 查询
     */
    @Retryable(value = KingDeeException.class, backoff = @Backoff(delay = DELAY_TIME, multiplier = MULTIPLIER))
    public <T> List<T> queryToObj(KingDeeQueryExtParam param, Class<T> tClass) {
        Assert.notNull(param, "金蝶扩展查询参数对象不能为空");
        Assert.notNull(tClass, "金蝶扩展查询返回类不能为空");
        Map<String, Field> fieldMap = buildFieldMap(tClass);
        Set<String> keys = fieldMap.keySet();
        try {
            String fieldKeys = StrUtil.join(",", keys);
            param.setFieldKeys(fieldKeys);
            List<List<Object>> list = new K3CloudApi().executeBillQuery(param.toJson());
            return parseResultToObj(list, fieldMap, tClass);
        } catch (Exception e) {
            log.warn("调用金蝶接口异常，重试中", e);
            throw new KingDeeException("调用金蝶接口异常", e);
        }
    }

    private <T> Map<String, Field> buildFieldMap(Class<T> tClass) {
        Map<String, Field> fieldMap = new HashMap<>();
        buildFieldMapRecursive(tClass, fieldMap, "", new HashSet<>(), 0);
        return fieldMap;
    }

    private void buildFieldMapRecursive(Class<?> clazz, Map<String, Field> fieldMap, String prefix, Set<Class<?>> visited, int depth) {
        if (depth > MAX_RECURSION_DEPTH) {
            log.error("递归深度超过限制，可能是循环引用导致的,{}", JSON.toJSONString(fieldMap));
            throw new IllegalStateException("递归深度超过限制，可能是循环引用导致的");
        }

        if (visited.contains(clazz)) {
            return; // 避免循环引用
        }
        visited.add(clazz);

        Field[] fields = ReflectUtil.getFields(clazz);
        for (Field field : fields) {
            if (field.isSynthetic()) {
                continue;
            }
            String fieldName = prefix.isEmpty() ? field.getName() : prefix + "." + field.getName();

            if (!this.isComplexType(field.getType())) {
                // 只将简单类型的字段放入 map
                fieldMap.put(fieldName, field);
            } else {
                // 对于复杂类型，递归处理子字段，但不存储当前父字段路径
                buildFieldMapRecursive(field.getType(), fieldMap, fieldName, visited, depth + 1);
            }
        }
    }

    private boolean isComplexType(Class<?> clazz) {
        // 基本类型和一些常见的不包含嵌套字段的类型
        if (clazz.isPrimitive() || clazz.equals(String.class) || clazz.equals(Integer.class) ||
                clazz.equals(Long.class) || clazz.equals(Double.class) || clazz.equals(Float.class) ||
                clazz.equals(Boolean.class) || clazz.equals(Byte.class) || clazz.equals(Character.class) ||
                clazz.equals(Short.class)) {
            return false;
        }
        // 数组类型
        if (clazz.isArray()) {
            return isComplexType(clazz.getComponentType());
        }
        // 集合类型
        if (Collection.class.isAssignableFrom(clazz)) {
            Type type = clazz.getGenericSuperclass();
            if (type instanceof ParameterizedType) {
                ParameterizedType parameterizedType = (ParameterizedType) type;
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                if (actualTypeArguments.length > 0 && actualTypeArguments[0] instanceof Class) {
                    return isComplexType((Class<?>) actualTypeArguments[0]);
                }
            }
        }
        // 其他类型视为复杂类型
        return true;
    }

    public <T> List<T> parseResultToObj(List<List<Object>> result, Map<String, Field> key, Class<T> tClass) {
        if (result == null) {
            throw new KingDeeException("查询金蝶接口异常，返回信息为null");
        }

        List<T> parseData = new ArrayList<>();

        if (CollectionUtil.isEmpty(result)) {
            return parseData;
        }

        String jsonString = JSON.toJSONString(result);
        if (jsonString.contains(ERROR_CODE)) {
            log.error("调用查询金蝶接口异常：{}", jsonString);
            throw new KingDeeException("金蝶查询接口异常" + jsonString);
        }

        List<String> fieldNames = new ArrayList<>(key.keySet());

        for (List<Object> data : result) {
            T newInstance = ReflectUtil.newInstance(tClass);

            for (int i = 0; i < fieldNames.size(); i++) {
                String fieldName = fieldNames.get(i);
                Field field = key.get(fieldName);
                Object value = data.get(i);

                if (fieldName.contains(".")) {
                    // 处理嵌套字段 (e.g., FBaseUnitId.FNumber)
                    String[] parts = fieldName.split("\\.");
                    Object current = newInstance;

                    // 遍历到最后一个属性之前的所有父级对象
                    for (int j = 0; j < parts.length - 1; j++) {
                        Field parentField = ReflectUtil.getField(current.getClass(), parts[j]);
                        Object parentValue = ReflectUtil.getFieldValue(current, parentField);

                        if (parentValue == null) {
                            parentValue = ReflectUtil.newInstance(parentField.getType());
                            ReflectUtil.setFieldValue(current, parentField, parentValue);
                        }
                        current = parentValue;
                    }

                    // 设置最终字段的值
                    Field targetField = ReflectUtil.getField(current.getClass(), parts[parts.length - 1]);
                    ReflectUtil.setFieldValue(current, targetField, value);
                } else {
                    ReflectUtil.setFieldValue(newInstance, field, value);
                }
            }

            parseData.add(newInstance);
        }

        return parseData;
    }

}
