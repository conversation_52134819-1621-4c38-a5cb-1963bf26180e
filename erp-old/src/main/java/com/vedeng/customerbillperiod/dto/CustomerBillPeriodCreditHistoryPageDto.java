package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/9/6 19:10
 * @desc :
 */
@Data
public class CustomerBillPeriodCreditHistoryPageDto extends CustomerBillPeriodCreditHistoryDto{

    private CustomerBillPeriodCreditHistoryDto total;

    private CustomerBillPeriodCreditHistoryDto formal;

    private CustomerBillPeriodCreditHistoryDto temporarys;

    private CustomerBillPeriodCreditHistoryDto order;

    private String orgName;

    private BigDecimal applyFormalsReal;

    private BigDecimal applyFormalsHistoryReal;

    private BigDecimal applyTemporarysReal;

    private BigDecimal applyTemporarysHistoryReal;

    private BigDecimal applyOrdersReal;

    private BigDecimal applyOrdersHistoryReal;

    private BigDecimal appCountsReal;

    private BigDecimal appCountsHistoryReal;
}
