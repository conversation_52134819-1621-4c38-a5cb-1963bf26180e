package com.vedeng.finance.constant;

/**
 * <AUTHOR>
 * @desc 添加流水操作类型枚举
 */
public enum CapitalBillBussinessTypeEnum {
    ORDER_PAYMENT(525, "订单付款"),
    ORDER_RECEIPT(526, "订单收款"),
    ORDER_REFUND(531, "退款"),
    CREDIT_PAYMENT(533, "信用还款"),
    WITHDRAW_FOR(679, "对私提现");

    private Integer code;

    private String desc;


    CapitalBillBussinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CapitalBillBussinessTypeEnum getTypeEnumByCode(Integer code) {
        for (CapitalBillBussinessTypeEnum item : CapitalBillBussinessTypeEnum.values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return ORDER_PAYMENT;
    }
}
