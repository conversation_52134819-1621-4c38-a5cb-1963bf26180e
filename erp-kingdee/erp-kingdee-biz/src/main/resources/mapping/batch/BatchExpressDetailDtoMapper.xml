<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchExpressDetailDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto">
    <!--@mbg.generated-->
    <!--@Table T_EXPRESS_DETAIL-->
    <id column="EXPRESS_DETAIL_ID" jdbcType="INTEGER" property="expressDetailId" />
    <result column="EXPRESS_ID" jdbcType="INTEGER" property="expressId" />
    <result column="BUSINESS_TYPE" jdbcType="INTEGER" property="businessType" />
    <result column="RELATED_ID" jdbcType="INTEGER" property="relatedId" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="AMOUNT" jdbcType="DECIMAL" property="amount" />
    <result column="HISTORICAL_NUM" jdbcType="INTEGER" property="historicalNum" />
    <result column="NON_ALL_ARRIVAL_REASON" jdbcType="VARCHAR" property="nonAllArrivalReason" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    EXPRESS_DETAIL_ID, EXPRESS_ID, BUSINESS_TYPE, RELATED_ID, NUM, AMOUNT, HISTORICAL_NUM, 
    NON_ALL_ARRIVAL_REASON
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_EXPRESS_DETAIL
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_EXPRESS_DETAIL
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_DETAIL (EXPRESS_ID, BUSINESS_TYPE, RELATED_ID, 
      NUM, AMOUNT, HISTORICAL_NUM, 
      NON_ALL_ARRIVAL_REASON)
    values (#{expressId,jdbcType=INTEGER}, #{businessType,jdbcType=INTEGER}, #{relatedId,jdbcType=INTEGER}, 
      #{num,jdbcType=INTEGER}, #{amount,jdbcType=DECIMAL}, #{historicalNum,jdbcType=INTEGER}, 
      #{nonAllArrivalReason,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="historicalNum != null">
        HISTORICAL_NUM,
      </if>
      <if test="nonAllArrivalReason != null and nonAllArrivalReason != ''">
        NON_ALL_ARRIVAL_REASON,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="historicalNum != null">
        #{historicalNum,jdbcType=INTEGER},
      </if>
      <if test="nonAllArrivalReason != null and nonAllArrivalReason != ''">
        #{nonAllArrivalReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto">
    <!--@mbg.generated-->
    update T_EXPRESS_DETAIL
    <set>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="historicalNum != null">
        HISTORICAL_NUM = #{historicalNum,jdbcType=INTEGER},
      </if>
      <if test="nonAllArrivalReason != null and nonAllArrivalReason != ''">
        NON_ALL_ARRIVAL_REASON = #{nonAllArrivalReason,jdbcType=VARCHAR},
      </if>
    </set>
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto">
    <!--@mbg.generated-->
    update T_EXPRESS_DETAIL
    set EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      HISTORICAL_NUM = #{historicalNum,jdbcType=INTEGER},
      NON_ALL_ARRIVAL_REASON = #{nonAllArrivalReason,jdbcType=VARCHAR}
    where EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.businessType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="RELATED_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.relatedId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.amount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="HISTORICAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.historicalNum,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="NON_ALL_ARRIVAL_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.nonAllArrivalReason,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where EXPRESS_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_EXPRESS_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="EXPRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.expressId != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.expressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="BUSINESS_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.businessType != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.businessType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="RELATED_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.relatedId != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.relatedId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.num != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.amount != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.amount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="HISTORICAL_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.historicalNum != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.historicalNum,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="NON_ALL_ARRIVAL_REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nonAllArrivalReason != null">
            when EXPRESS_DETAIL_ID = #{item.expressDetailId,jdbcType=INTEGER} then #{item.nonAllArrivalReason,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where EXPRESS_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.expressDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_DETAIL
    (EXPRESS_ID, BUSINESS_TYPE, RELATED_ID, NUM, AMOUNT, HISTORICAL_NUM, NON_ALL_ARRIVAL_REASON
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.expressId,jdbcType=INTEGER}, #{item.businessType,jdbcType=INTEGER}, #{item.relatedId,jdbcType=INTEGER}, 
        #{item.num,jdbcType=INTEGER}, #{item.amount,jdbcType=DECIMAL}, #{item.historicalNum,jdbcType=INTEGER}, 
        #{item.nonAllArrivalReason,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID,
      </if>
      EXPRESS_ID,
      BUSINESS_TYPE,
      RELATED_ID,
      NUM,
      AMOUNT,
      HISTORICAL_NUM,
      NON_ALL_ARRIVAL_REASON,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressDetailId != null">
        #{expressDetailId,jdbcType=INTEGER},
      </if>
      #{expressId,jdbcType=INTEGER},
      #{businessType,jdbcType=INTEGER},
      #{relatedId,jdbcType=INTEGER},
      #{num,jdbcType=INTEGER},
      #{amount,jdbcType=DECIMAL},
      #{historicalNum,jdbcType=INTEGER},
      #{nonAllArrivalReason,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      </if>
      EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      HISTORICAL_NUM = #{historicalNum,jdbcType=INTEGER},
      NON_ALL_ARRIVAL_REASON = #{nonAllArrivalReason,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="EXPRESS_DETAIL_ID" keyProperty="expressDetailId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchExpressDetailDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_EXPRESS_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID,
      </if>
      <if test="expressId != null">
        EXPRESS_ID,
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE,
      </if>
      <if test="relatedId != null">
        RELATED_ID,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="historicalNum != null">
        HISTORICAL_NUM,
      </if>
      <if test="nonAllArrivalReason != null and nonAllArrivalReason != ''">
        NON_ALL_ARRIVAL_REASON,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="expressDetailId != null">
        #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        #{expressId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="historicalNum != null">
        #{historicalNum,jdbcType=INTEGER},
      </if>
      <if test="nonAllArrivalReason != null and nonAllArrivalReason != ''">
        #{nonAllArrivalReason,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="expressDetailId != null">
        EXPRESS_DETAIL_ID = #{expressDetailId,jdbcType=INTEGER},
      </if>
      <if test="expressId != null">
        EXPRESS_ID = #{expressId,jdbcType=INTEGER},
      </if>
      <if test="businessType != null">
        BUSINESS_TYPE = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null">
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="historicalNum != null">
        HISTORICAL_NUM = #{historicalNum,jdbcType=INTEGER},
      </if>
      <if test="nonAllArrivalReason != null and nonAllArrivalReason != ''">
        NON_ALL_ARRIVAL_REASON = #{nonAllArrivalReason,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-05-06-->
  <select id="findByExpressIdAndBusinessType" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_EXPRESS_DETAIL
        where EXPRESS_ID=#{expressId,jdbcType=INTEGER} and BUSINESS_TYPE=#{businessType,jdbcType=INTEGER}
    </select>
</mapper>