<html>
<head>
    <meta charset="UTF-8">
    <title>联系人信息列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">

</head>
<body id="traderContractList" datasource="erp-datasourcetarget" fixednumberright="1" append_column_url="" append_row_url="" empty_show="-" firstcol="numbers">
<div id="appendHead">
    <style>
        .icontel {
            height: 16px;
            width: 16px;
            display: inline-block;
            background: url('/static/images/icon.png') no-repeat;
            background-position: -174px -32px;
            margin-bottom: -2px;
            cursor: pointer;
        }
        .rowButtons{
            min-width: 160px;
        }
        li{
            position: relative;
            float: left;
            padding: 10px 0;
            display: list-item;
        }
        li>a{    color: #333;
            border-right: 1px solid #ddd;
            padding: 0 10px;}
        ul{    overflow: hidden;
            list-style: none outside none;}
        /*.layui-elip{*/
        /*    white-space: normal !important;*/
        /*}*/
        .ezcall{
            color: #01AAED;
            font-size: 16px !important;
            cursor: pointer;
        }
        .utext i{
            font-size: 16px !important;
        }
        .layui-this-def{
            color: white;
            background-color: #1E9FFF;
            border-radius: 2px;
            border: none;
        }

    </style>
</div>
<div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
        <li item_name="traderContract11"><a href="/trader/customer/new/portrait.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}&amp;customerNature=${customerNature}" item_name="traderContract11">客户360</a></li>
        <li item_name="traderContract1"><a href="/trader/customer/baseinfo.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract1">基本信息</a></li>
        <li item_name="traderContract12"><a href="/trader/customer/new/distribution/link.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract12">经销链路</a></li>
        <li item_name="traderContract2"><a href="/trader/customer/new/customeDetail.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract2">客户行为</a></li>
        <li item_name="traderContract3"><a href="/trader/customer/getFinanceAndAptitude.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract3">财务与资质信息</a></li>
        <li item_name="traderContract4"><a href="/ezadmin/list/list-traderContractList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}&amp;perPageInt=50&amp;customerNature=${customerNature}" item_name="traderContract4">联系人</a></li>
        <li item_name="addressInformation5"><a href="/ezadmin/list/list-addressInformationList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}" item_name="addressInformation5">联系地址</a></li>
        <li item_name="customerAddAddress6"><a href="/ezadmin/list/list-customerAddAddressList?TRADER_ID=${TRADER_ID}&amp;TRADER_CUSTOMERID=${TRADER_CUSTOMERID}" item_name="customerAddAddress6">客户添加的地址</a></li>
        <li item_name="customerAddAddress8"><a href="/trader/customer/businesslist.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress8">交易记录</a></li>
        <li item_name="customerAddAddress9"><a href="/trader/customer/communicaterecord.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress9">沟通记录</a></li>
        <li item_name="customerAddAddress10"><a href="/trader/relation/info.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="customerAddAddress10">关联客户</a></li>
        <li item_name="traderContract12"><a href="/trader/customer/new/share.do?traderId=${TRADER_ID}&amp;traderCustomerId=${TRADER_CUSTOMERID}" item_name="traderContract12">分享客户</a></li>
    </ul>
</div>

<form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">trader_id</label>
        <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="TRADER_ID" placeholder style alias="tc" jdbctype data datatype oper validate_rules validate_messages> </object>
        </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">合并搜索</label>
        <div class="layui-input-inline">
            <object class=" layui-input list-search-item " type="union" name="TELEPHONE,MOBILE,MOBILE2" placeholder="搜索联系电话" style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper="LIKE"></object>
        </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">座机</label>
        <div class="layui-input-inline">
            <object class=" layui-input list-search-item " type="hidden" name="TELEPHONE" placeholder style="" alias="tc" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
            </object>  </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">电话1</label>
        <div class="layui-input-inline">
            <object class=" layui-input list-search-item " type="hidden" name="MOBILE" placeholder style="" alias="tc" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
            </object>  </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">电话2</label>
        <div class="layui-input-inline">
            <object class=" layui-input list-search-item " type="hidden" name="MOBILE2" placeholder style="" alias="tc" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
            </object>  </div>
    </div>
    <div class=" layui-inline ">
        <label class="layui-form-label">姓名</label>
        <div class="layui-input-inline">
            <input class=" layui-input list-search-item " type="text" name="NAME" placeholder style="" alias="tc" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages="">
        </div>
    </div>
    <div class=" layui-inline ">
        <label class="layui-form-label">联系人类型</label>
        <div class="layui-input-inline">
            <object class=" layui-input list-search-item " type="hidden" id="POSITION_MARK_LIST" name="POSITION_MARK_LIST" placeholder style="" alias="fil" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
        </div>
    </div>
    <div class=" layui-inline ">
        <label class="layui-form-label">是否启用</label>
        <div class="layui-input-inline">
            <select class=" layui-input list-search-item " type="" name="IS_ENABLE" placeholder style="" alias="tc" jdbctype="" data="yesno" datatype="" oper="" validate_rules="" validate_messages="">
            </select></div>
    </div>

    <div class=" layui-inline "><label class="layui-form-label">traderCustomerId</label>
        <div class="layui-input-inline">
            <hidden class=" layui-input list-search-item " type="nowhere" name="TRADER_CUSTOMERID" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="" ></hidden>
        </div>
    </div>
</form>
<div id="contractTab"></div>
<hr class="layui-border-blue">
<div class="btn-group   bd-highlight" id="tableButton">
    <button item_name="新增" name="新增" url="/trader/customer/toAddContactPage.do?traderId=${TRADER_ID}&traderCustomerId=${TRADER_CUSTOMERID}&customerNature=${customerNature}" opentype="MODEL" windowname="新增联系人" area="70%,80%" style type="table">新增</button>
</div>
<table id="table" class="layui-table" style=" width:100%">
    <thead>
    <tr id="column">
        <th item_name="NAME" name="NAME" body="td-link" url="/trader/customer/getContactInfo.do?traderContactId=${TRADER_CONTACT_ID}&traderId=${TRADER_ID}&traderCustomerId=${TRADER_CUSTOMER_ID}" opentype="MODEL"   windowname="联系人基本信息" datatype="" data=""  >姓名</th>
        <th item_name="SEX" name="SEX" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width: 30px;max-width:30px">性别</th>
        <th item_name="POSITION" name="POSITION" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:50px;max-width:50px">职位</th>
        <!--<th item_name="DEPARTMENT" name="DEPARTMENT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:50px;max-width:50px">部门</th>-->
        <th item_name="POSITION_MARK_LIST" name="POSITION_MARK_LIST" body="td-utext" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:150px">联系人类型</th>
        <th item_name="TELEPHONE" name="TELEPHONE" body="td-utext" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width:95px">电话</th>
        <th item_name="MOBILE" name="MOBILE" body="td-utext" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width: 90px">手机</th>
        <th item_name="MOBILE2" name="MOBILE2" body="td-utext" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width: 90px">手机2</th>

        <th item_name="COMMENTS" name="COMMENTS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="max-width: 400px"   >备注</th>
        <th item_name="BUSSINESSCARD" name="BUSSINESSCARD" body="td-link" url="/trader/customer/viewBusinessCards.do?traderContactId=${TRADER_CONTACT_ID}" opentype="MODEL" windowname="" datatype="" data="名片查看"style="max-width:50px;min-width:50px">个人名片</th>
        <th item_name="IS_ON_JOB" name="IS_ON_JOB" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="max-width:50px;min-width:50px">在职状态</th>
        <th item_name="EMAIL" name="EMAIL" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="">邮箱</th>
        <th item_name="QQ" name="QQ" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" >QQ</th>
        <th item_name="WEIXIN" name="WEIXIN" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" >微信</th>
        <th item_name="MEMBER_FLAG" name="MEMBER_FLAG" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="min-width: 163px"  >注册信息</th>
        <th item_name="IS_ENABLESTR" name="IS_ENABLESTR" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data=""  >状态</th>
        <th item_name="IS_DEFAULTSTR" name="IS_DEFAULTSTR" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data=""  >是否默认</th>
        <th item_name="IS_TOPSTR" name="IS_TOPSTR" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data=""  >是否置顶</th>

        <!--        <th item_name="IS_VEDENG_MEMBER" name="IS_VEDENG_MEMBER" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;已注册&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;已注册&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;未注册&quot;,&quot;K&quot;:&quot;-1&quot;}]" style="max-width:40px;min-width:40px">是否注册</th>-->
        <!--        <th item_name="IS_VEDENG_JX" name="IS_VEDENG_JX" body="td-select" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;会员&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;会员&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;非会员&quot;,&quot;K&quot;:&quot;-1&quot;}]"style="max-width:40px;min-width:40px">贝登商城会员</th>-->
        <th type="rowbutton" id="rowbutton" style="min-width: 150px">
            <button type="group" opentype="MODEL"
                    url="/trader/customer/toEditContactPage.do?traderContactId=${TRADER_CONTACT_ID}&traderId=${TRADER_ID}&traderCustomerId=${TRADER_CUSTOMER_ID}&customerNature=${CUSTOMER_NATURE}"
                    windowname="编辑" name="MODEL" >编辑
            </button>
            <button  type="group" opentype="CONFIRM_AJAX"
                     url="/ezadmin/form/doSubmit-traderContractForm?ID=${TRADER_CONTACT_ID}&amp;TYPE=3&amp;TRADER_CONTACT_ID=${TRADER_CONTACT_ID}&amp;IS_TOP=${IS_TOP}"
                     windowname="" name="置顶或取消"  >置顶或取消
            </button>
            <button type="group" opentype="CONFIRM_AJAX"
                    url="/ezadmin/form/doSubmit-traderContractForm?ID=${TRADER_CONTACT_ID}&amp;TYPE=1&amp;TRADER_CONTACT_ID=${TRADER_CONTACT_ID}&amp;IS_ENABLE=${IS_ENABLE}"
                    windowname="" item_name="启用或禁用" name="启用或禁用"  >启用或禁用
            </button>
            <button  type="group" opentype="CONFIRM_AJAX"
                     url="/ezadmin/form/doSubmit-traderContractForm?ID=${TRADER_CONTACT_ID}&amp;TYPE=2&amp;TRADER_ID=${TRADER_ID}&amp;TRADER_CONTACT_ID=${TRADER_CONTACT_ID}&amp;TRADER_TYPE=${TRADER_TYPE}"
                     windowname="" item_name="设置为默认人" name="设置为默认人">设置为默认人
            </button>
            <button class="layui-btn list-row-button" type="group" opentype="MODEL"
                    url="/trader/customer/toTransferContactPage.do?traderContactId=${TRADER_CONTACT_ID}&traderType=${TRADER_TYPE}&name=${NAME}&sex=${SEXNUMBER}&department=${DEPARTMENT}&mobile=${MOBILENUMBER}&position=${POSITION}&isOnJob=${IS_ON_JOB_NUMBER}&traderId=${TRADER_ID}"
                    windowname="转移联系人" name="MODEL" area="800px,500px" style="caret-color: red">转移
            </button>
        </th>
    </tr>
    </thead>
    <tbody>
    <tr colspan=100>
        <td>
                <pre id="express" class="layui-code" orderby="ORDER BY IFNULL(df.SEQUENCE_NUMBER, 9999), tc.IS_TOP DESC" groupby="GROUP BY tc.IS_TOP,tc.TRADER_CONTACT_ID">
                 <![CDATA[     TRADER_ID=$$("TRADER_ID");
                    StringBuilder sql=new StringBuilder("
                    select
                    tc.NAME,
                    tc.POSITION,
                    tc.TRADER_TYPE,
                    tc.TRADER_CONTACT_ID,
                    tc.TRADER_ID,
                    tcr.TRADER_CUSTOMER_ID,
                    tcr.CUSTOMER_NATURE,
                    tc.DEPARTMENT,
                    fil.POSITION_MARK_LIST,
                    case
                        WHEN tc.DEPARTMENT  IS NULL OR TRIM(tc.DEPARTMENT)='' THEN
                        tc.COMMENTS
                        ELSE CONCAT('部门：',tc.DEPARTMENT,' ',IFNULL(tc.COMMENTS,'') )
                        end
                    AS COMMENTS,
                    v.STATUS,
                    t.TRADER_NAME,
                    t1.TRADER_NAME AS TRADER_NAME_TWO,
                    tc.IS_ENABLE,
                    tc.IS_TOP,
                    tc.IS_DEFAULT,
                    COALESCE(wa.IS_VEDENG_JX,wa1.IS_VEDENG_JX,- 1) AS IS_VEDENG_JX,
                    COALESCE(wa.IS_VEDENG_MEMBER,wa1.IS_VEDENG_MEMBER,- 1) AS IS_VEDENG_MEMBER,
                    df.SEQUENCE_NUMBER,

	                    CASE
                        WHEN (t.TRADER_NAME IS NULL OR t.TRADER_NAME = '') and (t1.TRADER_NAME IS NULL OR t1.TRADER_NAME = '') THEN ''
	                    WHEN v.STATUS IN (0, 1, 5) THEN CONCAT('已注册-会员', ',', COALESCE(wa.MOBILE, wa1.MOBILE))
	                    ELSE CONCAT('已注册', ',', COALESCE(wa.MOBILE, wa1.MOBILE))
	                    END AS MEMBER_FLAG,


                    (CASE WHEN  tc.IS_ON_JOB = 1 THEN '在职' WHEN tc.IS_ON_JOB = 0 THEN '离职' END) IS_ON_JOB,
                    tc.IS_ON_JOB IS_ON_JOB_NUMBER,
                               (CASE WHEN  tc.IS_ENABLE = 1 THEN '启用' WHEN tc.IS_ENABLE = 0 THEN '禁用' END) IS_ENABLESTR,
                               (CASE WHEN  tc.IS_TOP = 1 THEN '置顶' WHEN tc.IS_TOP = 0 THEN '-' END) IS_TOPSTR,
                               (CASE WHEN  tc.IS_DEFAULT = 1 THEN '默认' WHEN tc.IS_DEFAULT = 0 THEN '-' END) IS_DEFAULTSTR,

                    concat('<i class=''icontel cursor-pointer'' title=''点击拨号'' data=''',ifnull(tc.TELEPHONE,''),''' ></i>'  ,ifnull(tc.TELEPHONE,'')) TELEPHONE  ,
                    concat('<i class=''icontel cursor-pointer'' title=''点击拨号'' data=''',ifnull(tc.MOBILE,''),''' ></i>'  ,ifnull(tc.MOBILE,'')) MOBILE  ,
                    tc.MOBILE MOBILENUMBER,
                    concat('<i class=''icontel cursor-pointer'' title=''点击拨号'' data=''',ifnull(tc.MOBILE2,''),''' ></i>'  ,ifnull(tc.MOBILE2,'')) MOBILE2  ,

                    tc.EMAIL, tc.QQ, tc.WEIXIN,
                    coalesce( NULL , '名片查看') as BUSSINESSCARD,
                    (CASE WHEN  tc.SEX = 0 THEN '女' WHEN tc.SEX = 1 THEN '男' WHEN tc.SEX = 2 THEN '保密' END) SEX,
                    tc.SEX as SEXNUMBER
                    from T_TRADER_CONTACT tc
                    LEFT JOIN T_WEB_ACCOUNT wa ON tc.MOBILE = wa.MOBILE
                    LEFT JOIN T_WEB_ACCOUNT wa1 ON tc.MOBILE2 = wa1.MOBILE
                    LEFT JOIN T_TRADER_CUSTOMER tcr ON tcr.TRADER_ID = tc.TRADER_ID
                    LEFT JOIN T_VERIFIES_INFO v on v.RELATE_TABLE_KEY = tcr.TRADER_CUSTOMER_ID and v.RELATE_TABLE = 'T_CUSTOMER_APTITUDE'
                    LEFT JOIN T_TRADER t ON wa.TRADER_ID = t.TRADER_ID
                    LEFT JOIN T_TRADER t1 ON wa1.TRADER_ID = t1.TRADER_ID
                    LEFT JOIN DWH_TRADER_LIST_CONTACT_FILTER_ERP fil on fil.TRADER_CONTACT_ID = tc.TRADER_CONTACT_ID
                    LEFT JOIN DWH_TRADER_CONTACT_NUM_DF df on df.TRADER_CONTACT_ID = tc.TRADER_CONTACT_ID AND df.TRADER_ID = tc.TRADER_ID
                    where
                    tc.TRADER_TYPE = 1");
                    if(isBlank("TRADER_ID")){
              sql.append(" AND 1=2 " );
              }
                    list=search(sql);
                    return list;
                    ]]>
                </pre>
        </td>
    </tr>
    <tr>
        <td> count:</td>
        <td colspan="100"> <pre id="count" class="layui-code"></pre> </td>
    </tr>
    </tbody>
</table>
<div id="appendFoot">
    <script src="/static/js/common.js?2023"></script>
    <script src="/static/js/call/call.js?2023"></script>
    <script>


        $(function(){
            $("#list-tab").removeClass("layui-tab-title");
            $("li[item_name=traderContract4]").addClass("layui-text");
            $("#search-itemName-MOBILE").focus();
            $('i[title="点击拨号"]').each(function () {
                    var phone = $(this).attr("data");
                    var traderContractId= $(this).parent().parent().parent().find('input[item_name="TRADER_CONTACT_ID"]').val();
                    var traderId= $(this).parent().parent().parent().find('input[item_name="TRADER_ID"]').val();
                    if(phone!=''){
                        $(this).on('click', function() {
                            callout(phone,traderId,1,10,0,traderContractId);
                        });
                    }else{
                        $(this).removeClass("icontel");
                    }
                }
            );

            $(".ezcall").click(function(){
                var phone=$(this).parent().text();
                if(phone!='-'){
                    phone=phone.replace(/(^\s*)|(\s*$)/g, "");
                    ezcall(phone)
                }else{
                    alert('号码为空')
                }
            });


            $("#searchForm").append(`
                <div class="layui-tab" id="contactTypeTab">
                    <ul class="layui-tab-title">
                        <li data-type="all">全部</li>
                        <li data-type="业务联系人">业务联系人</li>
                        <li data-type="收票联系人">收票联系人</li>
                        <li data-type="收货联系人">收货联系人</li>
                        <li data-type="其他联系人">其他联系人</li>
                    </ul>
                </div>
            `)

            // 获取POSITION_MARK_LIST 值，并判断给对应contactTypeTab 的li 添加样式，默认给全部添加 layui-this-def
            var POSITION_MARK_LIST = $("#itemId-POSITION_MARK_LIST").val();
            if (POSITION_MARK_LIST === "") {
                $("#contactTypeTab .layui-tab-title li[data-type='all']").addClass("layui-this-def");
            }else{
                $("#contactTypeTab .layui-tab-title li[data-type='" + POSITION_MARK_LIST + "']").addClass("layui-this-def");
            }


            // 绑定 Tab 切换事件
            $("#contactTypeTab .layui-tab-title li").on("click", function () {
                debugger
                var currentTab = $(this);
                var currentType = currentTab.data("type"); // 获取当前点击的 Tab 类型
                var activeTab = $("#contactTypeTab .layui-tab-title li.layui-tab-def"); // 获取当前选中的 Tab

                // 如果当前点击的 Tab 和已选中的 Tab 相同，则不执行
                if (currentTab[0] === activeTab[0]) {
                    return;
                }

                // 移除所有 Tab 的选中状态
                $("#contactTypeTab .layui-tab-title li").removeClass("layui-this-def");
                // 为当前点击的 Tab 添加选中状态
                currentTab.addClass("layui-this-def");
                // filterTableData(currentType); // 过滤表格数据
                // 赋值给id为POSITION_MARK，并调用查询
                $("#itemId-POSITION_MARK_LIST").val(currentType);
                if (currentType === "all"){
                    $("#itemId-POSITION_MARK_LIST").val("");
                }
                $("#searchForm").submit();
            });

            // 过滤表格数据的函数
            function filterTableData(type) {
                var rows = $("#table tbody tr"); // 获取所有数据行
                rows.each(function () {
                    var row = $(this);
                    var contactType = row.find("td[item_name='CONTACT_TYPE']").text().trim(); // 获取联系人类型
                    if (type === "all" || contactType === type) {
                        row.show(); // 显示符合条件的行
                    } else {
                        row.hide(); // 隐藏不符合条件的行
                    }
                });
            }

            // 初始化时显示全部数据
            // filterTableData("all");
        })
    </script>
</div>
</body>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
</script>
<script>
    layui.use(function () {

    })
</script>
</html>
