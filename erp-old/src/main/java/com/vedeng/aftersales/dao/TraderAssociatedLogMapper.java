package com.vedeng.aftersales.dao;


import com.vedeng.aftersales.model.TraderAssociatedLogDo;

import java.util.List;

public interface TraderAssociatedLogMapper {


    int insertSelective(TraderAssociatedLogDo record);

    TraderAssociatedLogDo selectByPrimaryKey(Long id);

    /**
     * 根据注册用户id获取关联公司日志
     *
     * @param webAccountId
     * @return
     */
    List<TraderAssociatedLogDo> listByWebAccountId(Integer webAccountId);

}