package com.vedeng.goods.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baidu.unbiz.fluentvalidator.Result;
import com.beust.jcommander.internal.Maps;
import com.common.dto.SelectDto;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.service.BasePriceMaintainService;
import com.pricecenter.service.SkuPriceModifyRecordService;
import com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment;
import com.vedeng.aftersales.model.dto.AfterSaleServiceStandardInfoDto;
import com.vedeng.aftersales.service.AfterSaleServiceStandardService;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.SpuLevelEnum;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.*;
import com.vedeng.common.page.Page;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.common.util.NumberUtil;
import com.vedeng.common.util.SnowFlakeUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.goods.SpuValidate;
import com.vedeng.department.dao.DepartmentsHospitalMapper;
import com.vedeng.department.model.DepartmentsHospital;
import com.vedeng.department.model.DepartmentsHospitalGenerate;
import com.vedeng.department.service.DepartmentsHospitalService;
import com.vedeng.docSync.model.pojo.generate.DocOfGoodsDo;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;
import com.vedeng.erp.finance.service.TaxcodeClassificationApiService;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.system.dto.CostCategoryApiDto;
import com.vedeng.erp.system.service.CostCategroryApiService;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.model.RegistrationNumber;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.goods.command.*;
import com.vedeng.goods.dao.BrandGenerateMapper;
import com.vedeng.goods.dao.CategoryDepartmentMapper;
import com.vedeng.goods.dao.CoreSpuGenerateExtendMapper;
import com.vedeng.goods.dto.KingDeeSkuInfoDto;
import com.vedeng.goods.dto.SkuMainDeptDto;
import com.vedeng.goods.enums.GoodsStorageConditionOthersEnum;
import com.vedeng.goods.enums.SKUPushedStatusEnum;
import com.vedeng.goods.manager.extension.handler.GoodsReportTodoHandler;
import com.vedeng.goods.manager.rule.GoodsValidatedRule;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.dto.*;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.service.*;
import com.vedeng.goods.service.api.VgoodsApiService;
import com.vedeng.goods.utils.GoodsParameterUtils;
import com.vedeng.goods.utils.GoodsStorageConditionUtils;
import com.vedeng.goods.utils.GoodsUtils;
import com.vedeng.goods.vo.InspectionItemVo;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.RoleService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.TraderSupplierService;
import com.wms.service.util.GlobalThreadPool;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.Transformer;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.io.Resources;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.propertyeditors.CustomNumberEditor;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.vedeng.goods.manager.constants.GoodsValidConstants.SPU_TYPE;

@Controller
@RequestMapping(value = {"/goods/vgoods"})
public class VGoodsController extends BaseController {

    @Autowired
    private VgoodsService goodsService;
    @Autowired
    private VgoodsApiService vgoodsApiService;

    @Autowired
    private DepartmentsHospitalService departmentsHospitalService;

    @Autowired
    BaseCategoryService baseCategoryService;
    @Autowired
    FirstEngageService firstEngageService;
    @Autowired
    UnitService unitService;
    @Autowired
    BaseGoodsService baseGoodsService;
    @Autowired
    UserService userService;
    @Autowired
    private BrandGenerateMapper brandGenerateMapper;
    @Autowired
    WarehouseStockService warehouseStockService;

    @Autowired
    private GoodsPackageInfoService goodsPackageInfoService;

    @Autowired
    private RoleService roleService;

    @Autowired
    BasePriceService basePriceService;

    @Autowired
    private BasePriceMaintainService basePriceMaintainService;

    @Resource
    private GoodsRemovalService goodsRemovalService;

    @Autowired
    private RegionService regionService;

    @Autowired
    private SkuAuthorizationService skuAuthorizationService;

    @Resource
    private CategoryDepartmentMapper categoryDepartmentMapper;


    @Value("${oldLogCheck.deadline}")
    private Long deadlineOfOldLogCheck;

    @Value("${doc_url}")
    private String docUrl;

    @Autowired
    private AfterSaleServiceStandardService afterSaleServiceStandardService;

    @Autowired
    private SkuPriceModifyRecordService skuPriceModifyRecordService;

    @Resource
    private GoodsReportTodoHandler goodsReportTodoHandler;

    @Autowired
    private VGoodsCommonService vGoodsCommonService;

    @Autowired
    private InspectionItemApiService inspectionItemApiService;

    @Autowired
    private DepartmentsHospitalMapper departmentsHospitalMapper;

    @Autowired
    private CoreSkuHistoryService coreSkuHistoryService;

    @Autowired
    private CostCategroryApiService costCategroryApiService;

    @Autowired
    private KingDeeMaterialApiService kingDeeMaterialApiService;

    @Autowired
    private GoodsApiService goodsApiService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private SkuMainDeptApiService skuMainDeptApiService;

    @Autowired
    private TaxcodeClassificationApiService taxcodeClassificationApiService;

    @Override
    @InitBinder
    public void initBinder(WebDataBinder binder) {
        CustomBigDecimalEditor bigDecimalEditor = new CustomBigDecimalEditor();
        CustomDateEditor dateEditor = new CustomDateEditor();
        binder.registerCustomEditor(BigDecimal.class, bigDecimalEditor);
        //处理tomcat容器解析参数时未将null-->"null"字串，导致springmvc参数绑定Long类型是失败
        binder.registerCustomEditor(Long.class, new CustomNumberEditor(Long.class, true) {
            final String NULL_STRING = "null";

            @Override
            public void setAsText(String text) throws IllegalArgumentException {
                if (NULL_STRING.equals(text)) {
                    setValue(null);
                } else {
                    super.setAsText(text);
                }
            }
        });
        binder.registerCustomEditor(Date.class, dateEditor);
    }

    /**
     * 新商品流列表页
     *
     * @param model
     * @param spuCommand
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/list")
    public String index(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand, HttpServletRequest request) throws Exception {
        User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Boolean financeFlag = userService.getFinanceFlagByUserId(currentUser.getUserId());
        Page page = getPageTag(request, spuCommand.getPageNo(), 5);
        if (StringUtils.isNotBlank(spuCommand.getSubordinateList())) {
            spuCommand.setSubordinates(Arrays.stream(spuCommand.getSubordinateList().split(",")).map(Integer::valueOf).collect(Collectors.toList()));
        }
        if (spuCommand.getSkuDisableStatus() == null) {
            spuCommand.setSkuDisableStatus(1);
        }
        //VDERP-5583 推送平台增加集采
        Optional.ofNullable(spuCommand.getPushStatus())
                .ifPresent(pushStatus -> {
                    if (pushStatus < -1) {
                        spuCommand.setPushStatusList(new ArrayList<>());
                    } else {
                        spuCommand.setPushStatusList(GoodsUtils.getAllPushStatusValueByPlatform(pushStatus));
                    }
                });
        List<CoreSpuBaseVO> list = goodsService.selectSpuListPage(spuCommand, page);
        try {
            model.addAttribute("spuTypeList", getSysOptionDefinitionInNewGoodsFlow());
            //所有的分配人
            List<User> assUser = userService.selectAllAssignUser();
            model.addAttribute("assUser", assUser);

        } catch (Exception e) {
            logger.error("商品列表页，搜索条件初始化失败。", e);
        }

        initHasEditAuth(currentUser, list);

        List<GoodsLevelVo> goodsLevelVos = goodsCommonService.listAllGoodsLevel(false);
        List<GoodsPositionVo> goodsPositionVos = goodsCommonService.listAllGoodsPosition();

        model.addAttribute("goodsLevelList", goodsLevelVos);
        model.addAttribute("goodsPositionList", goodsPositionVos);
        model.addAttribute("currentUser", currentUser);
        model.addAttribute("page", page);
        model.addAttribute("list", list);
        model.addAttribute("command", spuCommand);
        model.addAttribute("financeFlag", financeFlag);
        return "goods/vgoods/list";
    }

    /**
     * 初始化SKU的编辑权限
     *
     * @param currentUser
     * @param list
     */
    private void initHasEditAuth(User currentUser, List<CoreSpuBaseVO> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.parallelStream().forEach(coreSpuBaseVO -> {
                if (CollectionUtils.isNotEmpty(coreSpuBaseVO.getCoreSkuBaseVOList())) {
                    coreSpuBaseVO.getCoreSkuBaseVOList().forEach(coreSkuBaseVO -> coreSkuBaseVO.setHasEditAuth(currentUser.getUserId().equals(coreSkuBaseVO.getAssignmentManagerId()) ||
                            currentUser.getUserId().equals(coreSkuBaseVO.getAssignmentAssistantId()) ? 1 : 0));
                }
            });
        }
    }

    @Autowired
    CoreSpuGenerateExtendMapper mapper;
    @Autowired
    com.vedeng.goods.dao.RCategoryJUserMapper rCategoryJUserMapper;

    @RequestMapping(value = "/skuMove")
    public String skuMove(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand, HttpServletRequest request,
                          HttpServletResponse response) {
        return "goods/vgoods/sku/sku_move";
    }

    @RequestMapping(value = "/skuMoveSubmit")
    @ResponseBody
    public String skuMoveSubmit(Model model, String spuId, String SkuIds, HttpServletRequest request,
                                HttpServletResponse response) {
        if (StringUtils.isBlank(spuId) || StringUtils.isBlank(SkuIds)) {
            return "ERROR";
        }
        String skuid[] = StringUtils.split(SkuIds, ",");
        for (int i = 0; i < skuid.length; i++) {
            CoreSkuGenerate skuGenerate = new CoreSkuGenerate();
            skuGenerate.setSkuId(NumberUtils.toInt(skuid[i].replace("V", "")));
            skuGenerate.setSpuId(NumberUtils.toInt(spuId.replace("V", "")));
            skuGenerate.setCheckStatus(GoodsCheckStatusEnum.PRE.getStatus());
            skuGenerate.setLastCheckReason("SKU 迁移SPU");
            skuGenerate.setPushStatus(SKUPushedStatusEnum.OFF.getStatus());
            baseGoodsService.mergeSku(skuGenerate);
        }
        return "OK";
    }

    // TODO
    @RequestMapping(value = "/export")
    public void export(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand, HttpServletRequest request,
                       HttpServletResponse response) throws Exception {
        List<String> list = Files.readAllLines(Paths.get(Resources.getResourceURL("process.txt").toURI()));
        List<String> aaa = Lists.newArrayList();
        for (String line : list) {
            String cell[] = StringUtils.split(line, "\t");
            String name1 = cell[0];
            String name3 = cell[2];
            String name2 = cell[1];
            Integer user1 = NumberUtils.toInt(cell[3]);
            Integer catId = 0;
            try {
                catId = mapper.selectCateId(name3, name2, name1);
            } catch (Exception e) {
                aaa.add(name1 + "\t" + name2 + "\t" + name3 + "\n");
                continue;
            }

            System.out.println(catId);
            RCategoryJUser jj = new RCategoryJUser();
            jj.setCategoryId(catId);
            jj.setUserId(user1);
            rCategoryJUserMapper.insert(jj);
            if (cell.length == 5) {
                Integer user2 = NumberUtils.toInt(cell[3]);
                RCategoryJUser jj2 = new RCategoryJUser();
                jj2.setCategoryId(catId);
                jj2.setUserId(user2);
                rCategoryJUserMapper.insert(jj2);
            }
        }
        System.out.println(StringUtils.join(aaa));
        // TODO rainy
    }

    @RequestMapping(value = "/listSku")
    @ResponseBody
    public ResultJSON listsku(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand,
                              HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (spuCommand.getSpuId() == null) {
            return ResultJSON.failed().message("spuId不能为空");
        }
        try {
            Page pp = new Page(spuCommand.getPageNo(), spuCommand.getPageSize() == 10 ? GoodsConstants.SKU_PAGE_SIZE : spuCommand.getPageSize());
            Page page = getPageTag(request, spuCommand.getPageNo(), GoodsConstants.SKU_PAGE_SIZE);

            List<CoreSkuBaseVO> list = goodsService.selectSkuListPage(spuCommand, pp);
            page.setTotalRecord(NumberUtils.toInt(pp.getTotalRecord() + ""));
            return ResultJSON.success().dataWithPage(list, page);
        } catch (Exception e) {
            logger.error("count error" + spuCommand.getSpuId(), e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/productCompany")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultJSON productCompany(Model model, String productCompanyName,
                                     HttpServletRequest request, HttpServletResponse response) {
        Map<String, Long> map = Maps.newHashMap();
        try {
            List<Map<String, Object>> companies = firstEngageService.getallcompany(productCompanyName);
            return ResultJSON.success().data(companies);
        } catch (Exception e) {
            logger.error("productCompany error" + productCompanyName, e);
            return ResultJSON.failed(e).data(map);
        }
    }

    @RequestMapping(value = "/searchSkuWithDepartment")
    @ResponseBody
    public ResultJSON searchSkuWithDepartment(Model model, String skuName, @ModelAttribute("command") SpuSearchCommand spuCommand,
                                              HttpServletRequest request, HttpServletResponse response) {
        Map<String, Long> map = Maps.newHashMap();
        PageHelper.startPage(spuCommand.getPageNo(), spuCommand.getPageSize());
        try {
            List<Map<String, Object>> companies = goodsService.searchSkuWithDepartment(skuName);
            return ResultJSON.success().data(companies);
        } catch (Exception e) {
            logger.error("productCompany error" + skuName, e);
            return ResultJSON.failed(e).data(map);
        }
    }


    @RequestMapping(value = "/departmentsHospital")
    @ResponseBody
    public ResultJSON departmentsHospital(Model model, String departmentName,
                                          HttpServletRequest request, HttpServletResponse response) {
        try {
            List<Map<String, Object>> companies = Lists.newArrayList();
            List<DepartmentsHospitalGenerate> departmentsHospitalList = departmentsHospitalService.getAllDepartmentsHospital(departmentName, 5);
            if (CollectionUtils.isNotEmpty(departmentsHospitalList)) {
                for (DepartmentsHospitalGenerate generate : departmentsHospitalList) {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("value", generate.getDepartmentId());
                    map.put("label", generate.getDepartmentName());
                    companies.add(map);
                }
            }
            return ResultJSON.success().data(companies);
        } catch (Exception e) {
            logger.error("departmentsHospital error" + departmentName, e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/count")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultJSON countAll(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand,
                               HttpServletRequest request, HttpServletResponse response) {
        Map<String, Long> map = Maps.newHashMap();
        try {
            long newCount = goodsService.countSpuByCheckStatus(GoodsCheckStatusEnum.NEW.getStatus());
            long preCount = goodsService.countSpuByCheckStatus(GoodsCheckStatusEnum.PRE.getStatus());
            long rejectCount = goodsService.countSpuByCheckStatus(GoodsCheckStatusEnum.REJECT.getStatus());
            long approveCount = goodsService.countSpuByCheckStatus(GoodsCheckStatusEnum.APPROVE.getStatus());
            long waitToPreCount = goodsService.countSpuByCheckStatus(GoodsCheckStatusEnum.WAIT_TO_PRE.getStatus());
            long allCount = newCount + preCount + rejectCount + approveCount + waitToPreCount;
            map.put("newCount", newCount);
            map.put("preCount", preCount);
            map.put("rejectCount", rejectCount);
            map.put("approveCount", approveCount);
            map.put("allCount", allCount);
            map.put("waitToPreCount", waitToPreCount);
            return ResultJSON.success().data(map);
        } catch (Exception e) {
            logger.error("count error" + spuCommand.getSpuId(), e);
            return ResultJSON.failed(e).data(map);
        }
    }


    /**
     * @param spuCommand
     * @param editable   是否为编辑页面
     */
    private void initEditSpuPage(SpuAddCommand spuCommand, boolean editable) {
        BrandGenerate brandGenerate = brandGenerateMapper.selectByPrimaryKey(spuCommand.getBrandId());
        if (brandGenerate != null) {
            if (StringUtils.isNotBlank(brandGenerate.getBrandName())) {
                spuCommand.setBrandName(brandGenerate.getBrandName());
            } else {
                spuCommand.setBrandName(brandGenerate.getBrandNameEn());
            }
        }

        List<User> productOwnerUsers = userService.selectAllAssignUser();
        spuCommand.setProductOwnerUsers(productOwnerUsers);

        if (editable) {
            String categoryPath = baseCategoryService.getAllLevelCategoryNameById(spuCommand.getCategoryId());
            spuCommand.setCategoryPath(StringUtils.isNotEmpty(categoryPath)
                    ? categoryPath.replaceAll(ErpConst.Symbol.SLASH, ErpConst.Symbol.HYPHEN) : StringUtils.EMPTY);
        } else {
            //医院科室信息已移至spu关联的三级分类下，现只有spu详情页才展示这部分信息

            // 获取分关联的科室信息
            if (spuCommand.getCategoryId() != null && spuCommand.getCategoryId() > 0) {
                List<CategoryDepartmentDo> categoryDepartmentDoList = categoryDepartmentMapper.listByCategoryId(spuCommand.getCategoryId(), CommonConstants.IS_DELETE_0);
                if (CollectionUtils.isNotEmpty(categoryDepartmentDoList)) {
                    Integer[] departmentIds = categoryDepartmentDoList.stream().map(CategoryDepartmentDo::getDepartmentId).toArray(Integer[]::new);
                    if (!ArrayUtils.isEmpty(departmentIds)) {
                        // 所有科室
                        List<DepartmentsHospital> departmentsHospitalList = departmentsHospitalMapper.findAllByIsDelete(CommonConstants.IS_DELETE_0);
                        List<DepartmentsHospitalGenerateVO> list = Lists.newArrayList();
                        if (CollectionUtils.isNotEmpty(departmentsHospitalList)) {
                            for (DepartmentsHospital g : departmentsHospitalList) {
                                DepartmentsHospitalGenerateVO vo = new DepartmentsHospitalGenerateVO();
                                BeanUtils.copyProperties(g, vo);
                                if (ArrayUtils.contains(departmentIds, g.getDepartmentId())) {
                                    vo.setSelected(true);
                                }
                                list.add(vo);
                            }
                        }
                        spuCommand.setDepartmentsHospitalList(list);
                    }
                }
                // 获取检查项目
                List<InspectionItemVo> inspectionItemVos = inspectionItemApiService.getInspectionItemList(spuCommand.getCategoryId());
                spuCommand.setInspectionItemVos(inspectionItemVos);
            }
        }

        // 所有属性
        spuCommand.setBaseAttributes(goodsService.getAttributeInfoByCategoryId(spuCommand.getCategoryId()));
        List<BaseAttributeVo> primaryAttributes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(spuCommand.getBaseAttributes())) {
            for (BaseAttributeVo vo : spuCommand.getBaseAttributes()) {
                if (ArrayUtils.contains(spuCommand.getBaseAttributeIds(), vo.getBaseAttributeId())) {
                    vo.setSelected(true);
                    if (ArrayUtils.contains(spuCommand.getPrimaryAttributeIds(), vo.getBaseAttributeId())) {
                        vo.setIsPrimary(ErpConst.ONE);
                    }
                    primaryAttributes.add(vo);
                }
            }
        }
        spuCommand.setPrimaryAttributes(primaryAttributes);

        //设置一级商品类别
        spuCommand.setSpuTypeList(getSysOptionDefinitionInNewGoodsFlow());

        //根据SPU类型决定SKU类型
        spuCommand.setSkuType(GoodsUtils.determineSkuTye(spuCommand.getSpuType()));
    }

    @RequestMapping(value = "/getAttributeInfoByCategoryId")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultJSON getAttributeInfoByCategoryId(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand,
                                                   HttpServletRequest request, HttpServletResponse response) {
        try {
            List<BaseAttributeVo> list = goodsService.getAttributeInfoByCategoryId(spuCommand.getCategoryId());
            return ResultJSON.success().data(list).message(CommonConstants.SUCCESS_MSG);
        } catch (Exception e) {
            logger.error("getAttributeInfoByCategoryId error" + spuCommand.getSpuId(), e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/searchFirstEngages")
    @ResponseBody
    public ResultJSON searchFirstEngages(@ModelAttribute("command") SpuSearchCommand spuCommand, HttpServletRequest request) {
        try {
            Page page = getPageTag(request, spuCommand.getPageNo(), spuCommand.getPageSize());
            List<Map<String, Object>> result = goodsService.searchFirstEngageListPage(spuCommand.getSearchValue(), page);
            return ResultJSON.success().data(result).message(CommonConstants.SUCCESS_MSG);
        } catch (
                Exception e) {
            logger.error("searchFirstEngage error" + spuCommand.getSearchValue(), e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/getFirstEngageById")
    @ResponseBody
    public ResultJSON getFirstEngageById(@ModelAttribute("command") SpuSearchCommand spuCommand) {
        if (spuCommand.getFirstEngageId() == null) {
            return ResultJSON.failed();
        }

        try {
            FirstEngage engage = firstEngageService.getFirstSearchBaseInfo(spuCommand.getFirstEngageId());
            if (engage == null) {
                return ResultJSON.failed();
            }
            Map<String, Object> map = Maps.newHashMap();
            map.put("firstEngageId", engage.getFirstEngageId());
            map.put("effectStartDate", engage.getEffectStartDate());
            map.put("effectEndDate", engage.getEffectEndDate());
            map.put("productCompanyChineseName", engage.getProductCompanyChineseName());
            map.put("oldStandardCategoryName", engage.getOldStandardCategoryName());
            map.put("newStandardCategoryName", engage.getNewStandardCategoryName());
            if (engage.getRegistration() != null) {
                map.put("productChineseName", engage.getRegistration().getProductChineseName());
                map.put("specsModel", engage.getRegistration().getModel());
            }
            map.put("registrationNumber", engage.getRegistrationNumber());
            //审核状态
            map.put("checkStatus", GoodsCheckStatusEnum.statusName(engage.getStatus()));
            map.put("checkStatusInt", engage.getStatus());
            // 管理分类

            SysOptionDefinition option = getSysOptionDefinition(NumberUtils.toInt(engage.getManageCategoryLevelShow()));
            if (option != null) {
                map.put("manageCategoryLevel", option.getTitle());
            }

            return ResultJSON.success().data(map).message(CommonConstants.SUCCESS_MSG);
        } catch (Exception e) {
            logger.error("searchFirstEngage error" + spuCommand.getFirstEngageId(), e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/getFeeItemByDeptIds")
    @ResponseBody
    public ResultJSON getFeeItemByDeptIds(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand
            , Integer[] departmentIds) {
        if (ArrayUtils.isEmpty(departmentIds)) {
            return ResultJSON.failed().message("科室不能为空");
        }
        try {
            Map<String, Object> param = Maps.newHashMap();
            List list = Lists.newArrayList();
            for (Integer deptId : departmentIds) {
                list.add(Maps.newHashMap("departmentId", deptId));
            }
            param.put("hospitalList", list);
            param.put("isDelete", "0");
            return ResultJSON.success().data(departmentsHospitalService.getDepartmentsHospitalByParam(param)).message(CommonConstants.SUCCESS_MSG);
        } catch (Exception e) {
            logger.error("searchFirstEngage error" + spuCommand.getFirstEngageId(), e);
            return ResultJSON.failed(e);
        }
    }

//    /**
//     * 新增临时spu页面
//     *
//     * @param spuCommand
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping(value = "/addTempSpu")
//    public ModelAndView addTempSpu(@ModelAttribute("command") SpuAddCommand spuCommand) throws Exception {
//        spuCommand.setSpuLevel(SpuLevelEnum.TEMP.spuLevel());
//        return addSpu(spuCommand);
//    }

    /**
     * 转为普通商品
     *
     * @param spuCommand
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/changeTempToCoreSpu")
    public ModelAndView changeTempToCoreSpu(@ModelAttribute("command") SpuAddCommand spuCommand) throws Exception {

        ModelAndView mv = addSpu(spuCommand);
        spuCommand.setSpuLevel(SpuLevelEnum.CORE.spuLevel());

        return mv;
    }

    /**
     * spu新增/编辑页面
     *
     * @param spuCommand
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/addSpu")
    public ModelAndView addSpu(@ModelAttribute("command") SpuAddCommand spuCommand) throws Exception {
        final String viewName;
        if (spuCommand.getSpuId() == null) {
            viewName = "goods/vgoods/spu/spu_add";
        } else {
            viewName = "goods/vgoods/spu/spu_edit";
        }
        ModelAndView modelAndView = new ModelAndView(viewName);
        try {
            goodsService.initSpu(spuCommand);
            List<GoodsLevelVo> goodsLevelVos = goodsCommonService.listAllGoodsLevel(false);
            List<GoodsPositionVo> goodsPositionVos = goodsCommonService.listAllGoodsPosition();

            modelAndView.addObject("goodsLevelList", goodsLevelVos);
            modelAndView.addObject("goodsPositionList", goodsPositionVos);
            modelAndView.addObject("isPushedSpu", goodsService.isPushedSpuBySpuId(spuCommand.getSpuId()));
            modelAndView.addObject("taxClassificationCode", spuCommand.getTaxClassificationCode());
            initEditSpuPage(spuCommand, true);
        } catch (ShowErrorMsgException e) {
            logger.error("addSpu addSpu", e);
            spuCommand.setErrors(Lists.newArrayList(e.getMessage()));
        }

        return modelAndView;
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, value = "/listGoodsLevel", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<List<GoodsLevelVo>> listGoodsLevel() {
        List<GoodsLevelVo> goodsLevelVos = goodsCommonService.listAllGoodsLevel(true);
        return ResultInfo.success(goodsLevelVos);
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, value = "/listGoodsPosition", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<List<GoodsPositionVo>> listGoodsPosition() {
        List<GoodsPositionVo> goodsPositionVos = goodsCommonService.listAllGoodsPosition();
        return ResultInfo.success(goodsPositionVos);
    }

    /**
     * 新商品流spu详情页
     *
     * @param spuCommand
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/viewSpu")
    public ModelAndView viewSpu(SpuAddCommand spuCommand, HttpServletRequest request) throws Exception {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        mv.setViewName("goods/vgoods/spu/spu_view");
        try {
            goodsService.initSpu(spuCommand);

            mv.addObject("command", spuCommand);

            mv.addObject("showType", spuCommand.getShowType());

            mv.addObject("brand", brandGenerateMapper.selectByPrimaryKey(spuCommand.getBrandId()));

            mv.addObject("cateName", baseCategoryService.getOrganizedCategoryNameById(spuCommand.getCategoryId()));

            List<LogCheckGenerate> logCheckGenerateList = goodsService.listSpuCheckLog(spuCommand.getSpuId());
            dealLogCheckGenerateCreator(logCheckGenerateList);
            List<LogCheckGenerate> oldLogCheckList = new ArrayList<>();
            List<LogCheckGenerate> newLogCheckList = new ArrayList<>();
            logCheckGenerateList.forEach(item -> {
                if (item.getAddTime().getTime() < deadlineOfOldLogCheck) {
                    oldLogCheckList.add(item);
                } else {
                    newLogCheckList.add(item);
                }
            });
            mv.addObject("oldLogCheckList", oldLogCheckList);
            mv.addObject("newLogCheckList", newLogCheckList);
            FirstEngage firstEngage = firstEngageService.getFirstSearchBaseInfo(spuCommand.getFirstEngageId());
            if (firstEngage != null) {
                mv.addObject("firstEngage", firstEngage);
                SysOptionDefinition option = getSysOptionDefinition(NumberUtils.toInt(firstEngage.getManageCategoryLevelShow()));
                if (option != null) {
                    firstEngage.setManageCategoryLevelShow(option.getTitle());
                }
            }
            initEditSpuPage(spuCommand, false);


            if (CollectionUtils.isNotEmpty(spuCommand.getProductOwnerUsers())){
                //赋值产品经理姓名
                spuCommand.getProductOwnerUsers().stream().filter(item -> item.getUserId()
                        .equals(spuCommand.getAssignmentManagerId())).findFirst().ifPresent(u->spuCommand.setAssignmentManagerName(u.getUsername()));
                //赋值产品助理姓名
                spuCommand.getProductOwnerUsers().stream().filter(item -> item.getUserId()
                        .equals(spuCommand.getAssignmentAssistantId())).findFirst().ifPresent(u->spuCommand.setAssignmentAssistantName(u.getUsername()));
            }

            try {
                //spu禁用审核记录
                mv.addObject("disableGoodsVerifyRecord", goodsService.getDisableGoodsRecord(spuCommand.getSpuId(), SPU_TYPE, user));

                //禁用spuTaskId
                Task disableGoodsTask = goodsService.getDisableGoodsTask(spuCommand.getSpuId(), SPU_TYPE);
                mv.addObject("disableGoodsTask", disableGoodsTask);

                //当前登录用户是否属于任务候选人
                if (Objects.nonNull(disableGoodsTask)) {
                    mv.addObject("isDisableGoodsCandidate", goodsService.getTaskCandidate(disableGoodsTask.getId(), user));
                }
            } catch (Exception e) {
                logger.error("获取商品审核信息异常！spuId" + spuCommand.getSpuId(), e);
            }

        } catch (ShowErrorMsgException e) {
            spuCommand.setErrors(Lists.newArrayList(e.getMessage()));
        }
        return mv;
    }


    //    @RequestMapping(value = "/saveTempSpu")
//    @ResponseBody
//    public ResultJSON saveTempSpu(Model model, @ModelAttribute("command") SpuAddCommand spuCommand, HttpServletRequest request,
//                                  HttpServletResponse response) throws Exception {
//        spuCommand.setSpuLevel(SpuLevelEnum.TEMP.spuLevel());
//        saveSpu(model, spuCommand, request, response);
//        if(CollectionUtils.isNotEmpty(spuCommand.getErrors())){
//            return ResultJSON.failed().message(StringUtils.join(spuCommand.getErrors(),","));
//        }else{
//            return ResultJSON.success();
//        }
//    }


//    /**
//     * 保存临时spu信息
//     *
//     * @param spuCommand
//     * @param goodsStorageConditionVo
//     * @return
//     * @throws Exception
//     * @see #saveSpu(SpuAddCommand, GoodsStorageConditionVo)
//     */
//    @RequestMapping(value = "/saveTempSpu")
//    public String saveTempSpu( @ModelAttribute("command") SpuAddCommand spuCommand,
//                              @ModelAttribute("goodsStorageConditionVo") GoodsStorageConditionVo goodsStorageConditionVo)
//            throws Exception {
//
//        spuCommand.setSpuLevel(SpuLevelEnum.TEMP.spuLevel());
//        return saveSpu(spuCommand, goodsStorageConditionVo);
//    }


    /**
     * 保存spu信息
     *
     * @param spuCommand
     * @param goodsStorageConditionVo
     * @return
     * @throws Exception
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/saveSpu", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE,
            produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo saveSpu(@ModelAttribute("command") SpuAddCommand spuCommand, HttpServletRequest request,
                              @ModelAttribute("goodsStorageConditionVo") GoodsStorageConditionVo goodsStorageConditionVo) {

        Result ret = SpuValidate.check(spuCommand, goodsStorageConditionVo);
        if (!ret.isSuccess()) {
            spuCommand.setErrors(ret.getErrors());
            return ResultInfo.error(ret.getErrors().get(0));
        }
        try {
            SpuUpdatedResultDto spuUpdatedResultDto = goodsService.saveSpu(request, spuCommand, goodsStorageConditionVo);
            return ResultInfo.success(spuUpdatedResultDto);
        } catch (ShowErrorMsgException e) {
            return ResultInfo.error(e.getMessage());
        }
    }

//    // 提交审核
//
//    /**
//     * 需要传输spuName
//     *
//     * @param spuCommand
//     * @param request
//     * @param response
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping(value = "/submitToCheck")
//    public String submitToCheck(Model model, @ModelAttribute("command") SpuAddCommand spuCommand, HttpServletRequest request,
//                                HttpServletResponse response) throws Exception {
//        Result ret = SpuValidate.checkSubmitCheck(spuCommand);
//        if (!ret.isSuccess()) {
//            spuCommand.setTips(StringUtils.join(ret.getErrors(), ","));
//            return viewSpu(model, spuCommand, request, response);
//        }
//        try {
//            goodsService.submitToCheck(spuCommand);
//            spuCommand.setTips("操作成功");
//            String url = "./goods/vgoods/viewSpu.do?spuId=" + spuCommand.getSpuId();
//            Map<String, String> map = new HashMap<>();
//            map.put("spuName", spuCommand.getShowName());
//            // TODO
//            // MessageUtil.sendMessage(75, varifyUserList, map, url,
//            // spuCommand.getUser().getUsername());//
//        } catch (ShowErrorMsgException e) {
//            spuCommand.setErrors(Lists.newArrayList(e.getMessage()));
//        }
//        return viewSpu(model, spuCommand, request, response);
//    }

    /**
     * 审核操作
     */
    @RequestMapping(value = "/checkSpu")
    @ResponseBody
    public ResultJSON checkSpu(Model model, @ModelAttribute("command") SpuAddCommand spuCommand, HttpServletRequest request,
                               HttpServletResponse response) throws Exception {
        Result ret = SpuValidate.checkCheckSpu(spuCommand);
        if (!ret.isSuccess()) {
            spuCommand.setTips(StringUtils.join(ret.getErrors(), ","));
            return ResultJSON.failed().message(StringUtils.join(ret.getErrors(), ","));
        }
        try {
            goodsService.checkSpu(request, spuCommand);
        } catch (ShowErrorMsgException e2) {
            return ResultJSON.failed().message(e2.getMessage());
        } catch (Exception e) {
            logger.error("checkSpu", e);
            return ResultJSON.failed().message(e.getMessage());
        }

        try {
            GlobalThreadPool.submitMessage(new Runnable() {
                @Override
                public void run() {
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    logger.info("开始执行dealPushTaskWithSpuExamine，uuid：{}",uuid);
                    dealPushTaskWithSpuExamine(spuCommand);
                    logger.info("结束执行dealPushTaskWithSpuExamine，uuid：{}",uuid);
                }
            });
        } catch (Exception e) {
            logger.error("spu审核处理sku推送信息 error", e);
        }
        return ResultJSON.success().message("操作成功");
    }


    /**
     * 提交审核操作
     */
    @RequestMapping(value = "/submitCheckSpu")
    @ResponseBody
    public ResultJSON submitCheckSpu(Model model, @ModelAttribute("command") SpuAddCommand spuCommand, HttpServletRequest request,
                                     HttpServletResponse response) throws Exception {
        Result ret = SpuValidate.checkCheckSpu(spuCommand);
        if (!ret.isSuccess()) {
            spuCommand.setTips(StringUtils.join(ret.getErrors(), ","));
            return ResultJSON.failed().message(StringUtils.join(ret.getErrors(), ","));
        }
        try {
            goodsService.checkSpu(request, spuCommand);
        } catch (ShowErrorMsgException e2) {
            return ResultJSON.failed().message(e2.getMessage());
        } catch (Exception e) {
            logger.error("submitCheckSpu", e);
            return ResultJSON.failed().message(e.getMessage());
        }
        return ResultJSON.success().message("操作成功");
    }

    /**
     * 审核操作
     */
    @RequestMapping(value = "/checkSku")
    @ResponseBody
    public ResultJSON checkSku(@ModelAttribute("command") SkuAddCommand command, HttpServletRequest request) {
        Result ret = SpuValidate.checkCheckSku(command);

        if ("null".equals(String.valueOf(command.getCheckStatus()))) {
            command.setCheckStatus(0);//待完善
        }
        if (!ret.isSuccess()) {
            command.setTips(StringUtils.join(ret.getErrors(), ","));
            return ResultJSON.failed().message(StringUtils.join(ret.getErrors(), ","));
        }
        try {
            goodsService.checkSku(request, command);
        } catch (ShowErrorMsgException sem) {
            return ResultJSON.failed().message(sem.getMessage());
        } catch (Exception e) {
            logger.error("checkSku", e);
            return ResultJSON.failed().message(e.getMessage());
        }

        try {
            if (command.getCheckStatus().equals(3)) {
                String pushedOrgIdList = goodsService.getPushedOrgIdList(command.getSkuId());
                command.setPushedOrgIdList(pushedOrgIdList);
                dealSkuPushTask(command);
            }
        } catch (Exception e) {
            logger.error("sku审核推送信息任务 error", e);
        }

        // 推送信息至金蝶
        if (command.getCheckStatus() == 3) {
            KingDeeSkuInfoDto skuInfoDto = goodsApiService.getSkuInfoBySkuId(command.getSkuId());
            if (skuInfoDto != null) {
                KingDeeMaterialDto kingDeeMaterialDto = goodsApiService.getPushSkuInfoToKingDee(skuInfoDto);
                kingDeeMaterialApiService.register(kingDeeMaterialDto);
                log.info("sku审核通过推送至金蝶，skuId：{}", command.getSkuId());
            }
        }

        return ResultJSON.success().message("操作成功");
    }

    /**
     * 审核操作
     */
    @RequestMapping(value = "/submitCheckSku")
    @ResponseBody
    public ResultJSON submitCheckSku(@ModelAttribute("command") SkuAddCommand command, HttpServletRequest request) {
        Result ret = SpuValidate.checkCheckSku(command);
        if (!ret.isSuccess()) {
            command.setTips(StringUtils.join(ret.getErrors(), ","));
            return ResultJSON.failed().message(StringUtils.join(ret.getErrors(), ","));
        }
        try {
            goodsService.checkSku(request, command);
        } catch (Exception e) {
            logger.error("submitCheckSku", e);
            return ResultJSON.failed().message(e.getMessage());
        }
        return ResultJSON.success().message("操作成功");
    }

    @Resource
    private GoodsCommonService goodsCommonService;

    /**
     * SKU编辑页面
     *
     * @param model
     * @param command
     * @param skuGenerate
     * @return
     * @throws Exception
     */
    @RequestMapping(method = RequestMethod.GET, value = "/addSku", produces = MediaType.TEXT_HTML_VALUE)
    public String addSku(Model model, @ModelAttribute("command") SkuAddCommand command, @ModelAttribute("skuGenerate") CoreSkuGenerate skuGenerate)
            throws Exception {

        try {
            //初始化页面SPU信息、
            CoreSpuBaseVO spuBaseVO = initBaseSpuPage(model, command);
            skuGenerate.setTaxCategoryNo(spuBaseVO.getTaxClassificationCode());
            if (StrUtil.isNotBlank(skuGenerate.getTaxCategoryNo())) {
                TaxcodeClassificationDto taxcodeClassificationDto = taxcodeClassificationApiService.findByCode(skuGenerate.getTaxCategoryNo());
                if (Objects.nonNull(taxcodeClassificationDto)) {
                    skuGenerate.setTaxCodeSimpleName(taxcodeClassificationDto.getClassificationAbbreviation());
                }
            }
            //初始化页面SKU信息
            //编辑
            CoreSkuGenerate generate = new CoreSkuGenerate();
            GoodsStorageConditionVo goodsStorageConditionVo = new GoodsStorageConditionVo();
            if (command.getSkuId() != null && command.getSkuId() > 0) {
                generate = goodsService.initSku(command, true);
                //convert to sku编辑页面VO
                model.addAttribute("skuGenerate", generate);
                goodsStorageConditionVo = GoodsStorageConditionUtils.createGoodsStorageConditionVo(generate);
                model.addAttribute("goodsStorageConditionVo", goodsStorageConditionVo);
                model.addAttribute("minOrder", generate.getMinOrder() != null ? generate.getMinOrder().intValue() : "");
                // VDERP-13638 判断该sku是否已创建了订单
                Integer saleOrderGoodsId = saleOrderGoodsApiService.getSaleOrderGoodsBySkuId(command.getSkuId());
                if (Objects.isNull(saleOrderGoodsId)) {
                    model.addAttribute("unitModifiable", true);
                } else {
                    model.addAttribute("unitModifiable", false);
                }
            } else {
                model.addAttribute("unitModifiable", true);
                //新增SKU时，需获取注册证信息用户提示用户信息
                if (spuBaseVO.getFirstEngageId() != null && spuBaseVO.getFirstEngageId() > 0) {
                    RegistrationNumber registrationNumber = firstEngageService.getRegistrationNumberByFirstEngageId(spuBaseVO.getFirstEngageId());
                    if (registrationNumber != null) {
                        model.addAttribute("storageCondAndEffectiveDateTips", registrationNumber.getStorageAndExpiryDate());
                    }
                }
            }


            List<SkuAuthorizationDto> skuAuthorizationDtos = getSkuAuthorizationInfo(skuGenerate.getSkuId());
            if (CollectionUtils.isNotEmpty(skuAuthorizationDtos)) {
                model.addAttribute("skuAuthorizationResponses", skuAuthorizationDtos);
            }

            //初始化页面其他信息
            goodsService.initSkuPage(model, command);
//            model.addAttribute("orgList", vgoodsApiService.getOrgDTOList());

            model.addAttribute("regions", regionService.getRegionByParentId(1));
            model.addAttribute("terminalTypes", skuAuthorizationService.getAllTerminalTypes());

            //VDERP-9776 记录sku修改历史
            CoreSkuHistory coreSkuHistory = CoreSkuHistory.builder().coreSkuGenerate(JSONObject.parseObject(JSONObject.toJSONString(generate)))
                    .skuAddCommand(JSONObject.parseObject(JSONObject.toJSONString(command)))
                    .goodsStorageConditionVo(JSONObject.parseObject(JSONObject.toJSONString(goodsStorageConditionVo))).build();
            if (CollectionUtils.isNotEmpty(skuAuthorizationDtos)) {
                // 需要将List中各个属性拼接后再转为json object
                Map<String, String> totalSkuAuthorization = new HashMap<>();
                StringBuilder authInfo = new StringBuilder();
                for(SkuAuthorizationDto item : skuAuthorizationDtos){
                    authInfo.append(item.getRegionsStr()).append(",").append(item.getTerminalTypesStr()).append(",").append(item.getSnowFlakeId().toString()).append(";");
                }
                totalSkuAuthorization.put("authInfo", authInfo.toString());
                coreSkuHistory.setSkuAuthorizationRequestVo(JSONObject.parseObject(JSONObject.toJSONString(totalSkuAuthorization)));
            }
            model.addAttribute("coreSkuHistory", JSON.toJSONString(coreSkuHistory));

        } catch (ShowErrorMsgException e) {
            command.setErrors(Lists.newArrayList(e.getErrorMsg()));
        }

        return "goods/vgoods/sku/sku_edit";
    }


    /**
     * 根据类型和商品等级获取校验规则
     *
     * @param targetValidType 1.SPU ，2.SKU
     * @param goodsLevelNo
     * @return
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, value = "/goodsValidatedRule", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo getValidatedRule(Integer targetValidType, Integer goodsLevelNo) {
        if (!NumberUtil.isPositive(targetValidType) ||
                !NumberUtil.isPositive(goodsLevelNo)) {
            return ResultInfo.error("parameters is invalid.");
        }
        GoodsValidatedRule validatedRule;
        try {
            validatedRule = goodsCommonService.getValidatedRule(targetValidType, goodsLevelNo);
        } catch (Exception e) {
            return ResultInfo.error("未匹配到商品校验规则");
        }

        return ResultInfo.success(validatedRule);
    }

    /**
     * 设置SKU报备信息
     *
     * @param skuId
     */
    private List<SkuAuthorizationDto> getSkuAuthorizationInfo(Integer skuId) {
        ArrayList<SkuAuthorizationDto> skuAuthorizationResponses = new ArrayList<>();
        SkuAuthorizationVo authorizationInfo = skuAuthorizationService.getSkuAuthorizationInfoBySkuId(skuId);
        if (authorizationInfo != null && CollectionUtils.isNotEmpty(authorizationInfo.getSkuAuthorizationItemVoList())) {
            List<SkuAuthorizationItemVo> skuAuthorizationItemVoList = authorizationInfo.getSkuAuthorizationItemVoList();
            skuAuthorizationItemVoList.forEach(skuAuthorizationItemVo -> {
                SkuAuthorizationDto skuAuthorizationResponse = new SkuAuthorizationDto();
                StringBuilder regionStr = new StringBuilder();
                skuAuthorizationItemVo.getRegionIds().forEach(regionId -> {
                    regionStr.append(regionId)
                            .append("@");
                });

                StringBuilder terminalTypesStr = new StringBuilder();
                skuAuthorizationItemVo.getTerminalTypeIds().forEach(terminalTypeId -> {
                    terminalTypesStr.append(terminalTypeId)
                            .append("@");
                });
                skuAuthorizationResponse.setRegionsStr(regionStr.toString());
                skuAuthorizationResponse.setTerminalTypesStr(terminalTypesStr.toString());
                skuAuthorizationResponse.setSnowFlakeId(skuAuthorizationItemVo.getSnowFlakeId());
                skuAuthorizationResponses.add(skuAuthorizationResponse);
            });
        }
        return skuAuthorizationResponses;
    }

    /**
     * 批量导入报备信息初始化
     *
     * @return
     */
    @ResponseBody
    @RequestMapping("/batchSaveAuthorizationInit")
    public ModelAndView batchSaveAuthorizationInit() {
        return new ModelAndView("goods/vgoods/batch_add_authorization");
    }

    @ResponseBody
    @RequestMapping("/batchSaveAuthorization")
    public ResultInfo batchSaveAuthorization(HttpServletRequest request, HttpSession session,
                                             @RequestParam("safile") MultipartFile rkfile) {
        ResultInfo<Object> resultInfo = new ResultInfo<>();

        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/logistics");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, rkfile);
            if (fileInfo.getCode() != 0) {
                resultInfo.setMessage("临时文件上传失败");
                return resultInfo;
            }
            //1.校验上传的文件类型为Excel
            String suffix = fileInfo.getFilePath().substring(fileInfo.getFilePath().lastIndexOf(".") + 1);
            if (!"xlsx".equalsIgnoreCase(suffix)) {
                logger.info("文件格式校验失败");
                resultInfo.setMessage("文件格式校验失败");
                return resultInfo;
            }
            // 创建一个用于保存Excel数据的对象
            HashMap<Integer, List<SkuAuthorizationItemVo>> skuAuthorizations = new HashMap<>();
            ArrayList<String> skuNos = new ArrayList<>();
            ArrayList<CoreSkuGenerate> skuGenerates = new ArrayList<>();
            fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
            workbook = WorkbookFactory.create(fileInputStream);
            Sheet sheet = workbook.getSheetAt(0);
            int startRowNum = sheet.getFirstRowNum() + 2;
            int endRowNum = sheet.getLastRowNum();

            //2.验证模板是否为标准模板
            Row firstRow = sheet.getRow(0);
            if (14 != firstRow.getLastCellNum()) {
                resultInfo.setMessage("模板不正确，请重新下载模板文件");
                return resultInfo;
            }

            if (0 == endRowNum) {
                resultInfo.setMessage("第1行，第1列不可为空!");
                return resultInfo;
            }
            List<Region> allRegions = regionService.getRegionByParentId(1);
            List<com.vedeng.goods.api.dto.SysOptionDefinition> allTerminalTypes = skuAuthorizationService.getAllTerminalTypes();

            for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {
                Row row = sheet.getRow(rowNum);
                int startCellNum = row.getFirstCellNum();
                int endCellNum = row.getLastCellNum() - 1;

                Integer skuId = null;
                String skuNo = null;
                Integer isNeedReport = null;
                Integer isAuthorized = null;
                ArrayList<Integer> regionsListResult = new ArrayList<>();
                ArrayList<Integer> typesListResult = new ArrayList<>();

                // 获取excel的值
                for (int cellNum = startCellNum; cellNum <= endCellNum; cellNum++) {
                    Cell cell = row.getCell(cellNum);
                    CellType type = cell.getCellType();

                    if (cellNum == 0) {
                        // 若excel中无内容，而且没有空格，cell为空--默认3，空白
                        if (setBatchAddErrorInfo(resultInfo, rowNum, cellNum, cell == null)) {
                            return resultInfo;
                        }
                        ;
                        //设置单元格类型防止类型转换错误
                        row.getCell(cellNum).setCellType(CellType.STRING);
                        skuNo = cell.getStringCellValue().toString().trim();
                        skuId = goodsService.getSkuIdBySkuNo(skuNo);
                        if (skuId == null || skuId.equals(0)) {
                            resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列SKU不存在，请修改后重新导入");
                            return resultInfo;
                        }
                    }

                    if (cellNum == 3) {
                        if (setBatchAddErrorInfo(resultInfo, rowNum, cellNum, cell == null)) {
                            return resultInfo;
                        }
                        row.getCell(cellNum).setCellType(CellType.STRING);

                        switch (cell.getStringCellValue().toString().trim()) {
                            case "是": {
                                isNeedReport = 1;
                                break;
                            }
                            case "否": {
                                isNeedReport = 0;
                                continue;
                            }
                            default: {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据格式有误，请修改后重新导入");
                                return resultInfo;
                            }
                        }
                    }

                    //无需报备情况
                    if (cellNum == 4 && isNeedReport != null && isNeedReport.equals(0)) {
                        isAuthorized = 1;
                    }

                    //需要报备情况
                    if (cellNum == 4 && isNeedReport != null && isNeedReport.equals(1)) {
                        if (setBatchAddErrorInfo(resultInfo, rowNum, cellNum, cell == null)) {
                            return resultInfo;
                        }
                        row.getCell(cellNum).setCellType(CellType.STRING);


                        switch (cell.getStringCellValue().toString()) {
                            case "是": {
                                isAuthorized = 1;
                                break;
                            }
                            case "否": {
                                isAuthorized = 0;
                                break;
                            }
                            default: {
                                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据格式有误，请修改后重新导入");
                                return resultInfo;
                            }
                        }
                    }

                    //需要报备并且有授权的情况
                    if (cellNum == 5 && isNeedReport != null && isNeedReport.equals(1) && isAuthorized != null && isAuthorized.equals(1)) {
                        row.getCell(cellNum).setCellType(CellType.STRING);
                        String authorizationRegionStr = cell.getStringCellValue().toString().trim();
                        if (setBatchAddErrorInfo(resultInfo, rowNum, cellNum, authorizationRegionStr == null)) {
                            return resultInfo;
                        }
                        if ("全国".equals(authorizationRegionStr)) {
                            for (Region region : allRegions) {
                                regionsListResult.add(region.getRegionId());
                            }
                        } else {
                            List<String> skuRegionsStr = Arrays.stream(authorizationRegionStr.split("，")).collect(Collectors.toList());
                            for (String skuRegionStr : skuRegionsStr) {
                                Integer regionId = null;
                                for (Region region : allRegions) {
                                    if (region.getRegionName().equals(skuRegionStr)) {
                                        regionId = region.getRegionId();
                                        break;
                                    }
                                }
                                if (setBatchAddErrorInfo(resultInfo, rowNum, cellNum, regionId == null)) {
                                    return resultInfo;
                                }
                                regionsListResult.add(regionId);
                            }
                        }
                    }

                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 6, "公立基层")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 7, "公立二级")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 8, "公立三级")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 9, "民营医院")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 10, "民营集团")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 11, "科研单位")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 11, "部队医院")) {
                        return resultInfo;
                    }
                    if (setTerminalType(resultInfo, allTerminalTypes, rowNum, isNeedReport,
                            isAuthorized, typesListResult, cellNum, cell, 11, "其他")) {
                        return resultInfo;
                    }
                }

                //校验是否维护过报备信息（是否需要报备”字段的值为空）
                CoreSkuGenerate coreSkuGenerate = goodsService.getSkuAuthotizationInfoBySku(skuId.longValue());
                if (coreSkuGenerate.getIsNeedReport() != null) {
                    logger.info("该sku报备信息已经维护,本次跳过，SKU:{}", skuNo);
                    continue;
                }

                skuNos.add(skuNo);

                CoreSkuGenerate skuGenerate = new CoreSkuGenerate();
                skuGenerate.setSkuId(skuId);
                skuGenerate.setIsNeedReport(isNeedReport);
                skuGenerate.setIsAuthorized(isAuthorized);
                skuGenerate.setSkuNo(skuNo);
                skuGenerates.add(skuGenerate);

                //I 封装sku的报备详情信息
                if (isNeedReport == 1 && isAuthorized == 1) {
                    if (CollectionUtils.isEmpty(regionsListResult) || CollectionUtils.isEmpty(typesListResult)) {
                        logger.info("需要报备并且已获得授权时 授权范围必填 SKU:{},行数:{}", skuNo, rowNum + 1);
                        resultInfo.setMessage("需要报备并且已获得授权时 授权范围必填 SKU:" + skuNo + ",行数:" + (rowNum + 1));
                        return resultInfo;
                    }
                    SkuAuthorizationItemVo skuAuthorizationItemVo = new SkuAuthorizationItemVo();
                    skuAuthorizationItemVo.setRegionIds(regionsListResult);
                    skuAuthorizationItemVo.setTerminalTypeIds(typesListResult);

                    if (skuAuthorizations.containsKey(skuId)) {
                        skuAuthorizations.get(skuId).add(skuAuthorizationItemVo);
                    } else {
                        ArrayList<SkuAuthorizationItemVo> skuAuthorizationItemVos = new ArrayList<>();
                        skuAuthorizationItemVos.add(skuAuthorizationItemVo);
                        skuAuthorizations.put(skuId, skuAuthorizationItemVos);
                    }
                }
            }

            //II 校验价格是否核价
            List<PriceInfoResponseDto> priceInfoResponseDtos = basePriceService.batchFindPriceInfo(skuNos, -111);
            if (CollectionUtils.isNotEmpty(priceInfoResponseDtos)) {
                for (PriceInfoResponseDto priceInfoResponseDto : priceInfoResponseDtos) {
                    if (priceInfoResponseDto.getPriceType() == null || priceInfoResponseDto.getPriceType() != 2) {
                        logger.info("SKU:" + priceInfoResponseDto.getSkuNo() + "未核价，请检查!");
                        resultInfo.setMessage("SKU:" + priceInfoResponseDto.getSkuNo() + "未核价，请检查!");
                        return resultInfo;
                    }
                }
            }
            skuGenerates.stream().forEach(sku -> {
                //III 更新sku表的报备标记信息
                ArrayList<Integer> skuIdList = new ArrayList<>();
                skuIdList.add(sku.getSkuId());
                goodsService.batchSaveSkuAuthorization(skuIdList, sku);

                //补偿机制打印价格中心同步更新sql
                logger.info("priceUpdateSql：UPDATE sku_info_image SET IS_NEED_REPORT = " + sku.getIsNeedReport() + " WHERE sku_no = " + sku.getSkuNo() + " ;");
            });

            //IV 校验重复
            for (Map.Entry<Integer, List<SkuAuthorizationItemVo>> entry : skuAuthorizations.entrySet()) {
                List<SkuAuthorizationItemVo> entryValue = entry.getValue();

                List<SkuAuthorizationItemVo> skuAuthorizationItemTmp = new ArrayList<>();
                List<SkuAuthorizationItemVo> singleSkuAuthorizationInfo = entryValue.stream().filter(
                        v -> {
                            boolean flag = true;
                            for (SkuAuthorizationItemVo skuAuthorizationItemVo : skuAuthorizationItemTmp) {
                                if (skuAuthorizationItemVo.equals(v)) {
                                    flag = false;
                                    break;
                                }
                            }
                            skuAuthorizationItemTmp.add(v);
                            return flag;
                        }
                ).collect(Collectors.toList());
                skuAuthorizations.put(entry.getKey(), singleSkuAuthorizationInfo);
            }

            //V 保存sku报备信息
            for (Map.Entry<Integer, List<SkuAuthorizationItemVo>> entry : skuAuthorizations.entrySet()) {
                SkuAuthrizationRequest skuAuthrizationRequest = new SkuAuthrizationRequest();

                ArrayList<Integer> skuIds = new ArrayList<>();
                skuIds.add(entry.getKey());

                skuAuthrizationRequest.setSkuIds(skuIds);
                entry.getValue().stream().forEach(item -> {
                    item.setSnowFlakeId(SnowFlakeUtils.uniqueLong());
                });
                skuAuthrizationRequest.setSkuAuthrizationItems(entry.getValue());
                skuAuthorizationService.saveSkuAuthrizationInfo(skuAuthrizationRequest, user);
            }
        } catch (Exception e) {
            logger.error("批量导入sku报备信息失败", e);
            resultInfo.setMessage("批量导入sku报备信息失败");
            return resultInfo;
        }finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    log.error("【batchSaveAuthorization】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.error("【batchSaveAuthorization】处理异常",e);
                }
            }
        }
        resultInfo.setCode(0);
        resultInfo.setMessage("sku报备信息批量保存成功!");
        return resultInfo;
    }

    private boolean setBatchAddErrorInfo(ResultInfo<Object> resultInfo, int rowNum, int cellNum, boolean b) {
        if (b) {
            resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "数据格式有误，请修改后重新导入");
            return true;
        }
        return false;
    }

    private boolean setTerminalType(ResultInfo<Object> resultInfo, List<com.vedeng.goods.api.dto.SysOptionDefinition> allTerminalTypes,
                                    int rowNum, Integer isNeedReport, Integer isAuthorized, ArrayList<Integer> typesListResult, int cellNum,
                                    Cell cell, int i, String tyepStr) {
        cell.setCellType(CellType.STRING);
        if (cellNum == i && isNeedReport != null && isNeedReport.equals(1) && isAuthorized != null && isAuthorized.equals(1)) {
            if (setBatchAddErrorInfo(resultInfo, rowNum, cellNum, cell == null)) {
                return true;
            }
            String authorizationTypeFlagStr = cell.getStringCellValue().toString().trim();
            if (StringUtils.isBlank(authorizationTypeFlagStr)) {
                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据格式有误，请修改后重新导入");
                return true;
            }
            int authorizationTypeFlag = 0;
            try {
                authorizationTypeFlag = Integer.parseInt(authorizationTypeFlagStr);
            } catch (NumberFormatException e) {
                logger.warn("授权范围数据格式有误 authorizationTypeFlagStr:{}", authorizationTypeFlagStr);
                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据格式有误，请修改后重新导入");
                return true;
            }

            if (authorizationTypeFlag != 0 && authorizationTypeFlag != 1) {
                resultInfo.setMessage("表格项错误，第" + (rowNum + 1) + "行" + (cellNum + 1) + "列数据格式有误，请修改后重新导入");
                return true;
            }

            if (authorizationTypeFlag == 1) {
                Integer terminalTypeId = null;
                for (com.vedeng.goods.api.dto.SysOptionDefinition terminalType : allTerminalTypes) {
                    if (tyepStr.equals(terminalType.getTitle())) {
                        terminalTypeId = terminalType.getSysOptionDefinitionId();
                        break;
                    }
                }
                if (terminalTypeId != null) {
                    typesListResult.add(terminalTypeId);
                }
            }

        }
        return false;
    }


    @RequestMapping(value = "/saveTempSku", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResultJSON saveTempSku(@ModelAttribute("command") SkuAddCommand spuCommand) {
        Result ret = SpuValidate.checkTempSku(spuCommand);
        if (!ret.isSuccess()) {
            return ResultJSON.failed();
        }
        try {
            goodsService.saveTempSku(spuCommand);
        } catch (Exception e) {
            logger.error("saveTempSku", e);
            return ResultJSON.failed(e);
        }
        return ResultJSON.success();
    }

    /**
     * 保存SKU信息
     *
     * @param model
     * @param command
     * @param skuGenerate
     * @param goodsStorageConditionVo
     * @param skuAuthorizationRequest
     * @param request
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/saveSku", produces = MediaType.TEXT_HTML_VALUE)
    public String saveSku(Model model, @ModelAttribute("command") SkuAddCommand command,
                          @ModelAttribute("skuGenerate") CoreSkuGenerate skuGenerate,
                          @ModelAttribute("goodsStorageConditionVo") GoodsStorageConditionVo goodsStorageConditionVo,
                          String coreSkuHistory, HttpServletRequest request,SkuAuthorizationRequestVo skuAuthorizationRequest) {
        User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        boolean addOperator = command.getSkuId() == null;
        Result ret = SpuValidate.checkSku(command, skuGenerate, goodsStorageConditionVo);
        if (ret.isSuccess()) {
            try {
                GoodsStorageConditionUtils.populateStorageCondition(skuGenerate, goodsStorageConditionVo);
                goodsService.saveSku(command, skuGenerate);

                try {
                    ArrayList<Integer> skuIds = new ArrayList<>(2);
                    skuIds.add(skuGenerate.getSkuId());
                    if (skuGenerate.getIsNeedReport() == 1 && skuGenerate.getIsAuthorized() == 1) {
                        saveSkuAuthorizationInfo(skuAuthorizationRequest, skuIds, sessUser);
                    }
                } catch (Exception e) {
                    logger.error("保存sku报备信息error", e);
                }

                command.setBaseAttributeValueId(null);//加载数据库里面的value
                command.setTips("操作成功");

                if (addOperator) {

                    //新增需要同步价格变更申请
                    Long priceChangeApplyId = basePriceMaintainService.addPriceChange("V" + command.getSkuId());
                    if (priceChangeApplyId != null) {
                        command.setPriceChangeApplyId(priceChangeApplyId);
                    }

                    return "redirect:/goods/vgoods/viewSku.do?pageType=0&skuId=" + command.getSkuId() + "&priceChangeApplyId=" + priceChangeApplyId;
                } else {
                    saveSkuHistory(coreSkuHistory, sessUser, command.getSkuId());
                    return "redirect:/goods/vgoods/viewSku.do?pageType=0&skuId=" + command.getSkuId();
                }

            } catch (ShowErrorMsgException e){
                logger.info("addSkuPost已知异常", e);
                command.setErrors(Lists.newArrayList(e.getMessage()));
            } catch (Exception e) {
                logger.error("addSkuPost", e);
                command.setErrors(Lists.newArrayList(e.getMessage()));
            }
            //command.setBaseAttributeValueId(null);//加载数据库里面的value
        }

        List<SkuAuthorizationDto> skuAuthorizationDtos = getSkuAuthorizationInfo(skuGenerate.getSkuId());
        if (CollectionUtils.isNotEmpty(skuAuthorizationDtos)) {
            model.addAttribute("skuAuthorizationResponses", skuAuthorizationDtos);
        }
        model.addAttribute("regions", regionService.getRegionByParentId(1));
        model.addAttribute("terminalTypes", skuAuthorizationService.getAllTerminalTypes());

        //初始化页面SPU信息
        initBaseSpuPage(model, command);
        //初始化页面其他信息
        goodsService.initSkuPage(model, command);
        model.addAttribute("orgList", vgoodsApiService.getOrgDTOList());
        model.addAttribute("unitModifiable", true);
        model.addAttribute("coreSkuHistory", coreSkuHistory);
        return "goods/vgoods/sku/sku_edit";
    }

    /**
     * 保存Sku报备信息
     *
     * @param skuAuthorizationRequestVo
     * @param skuIds
     * @param user
     */
    private void saveSkuAuthorizationInfo(SkuAuthorizationRequestVo skuAuthorizationRequestVo, ArrayList<Integer> skuIds, User user) {
        SkuAuthrizationRequest skuAuthrizationRequest = new SkuAuthrizationRequest();

        skuAuthrizationRequest.setSkuIds(skuIds);
        if (skuAuthorizationRequestVo == null || CollectionUtils.isEmpty(skuAuthorizationRequestVo.getSkuAuthorizationDtos())) {
            logger.info("添加sku时未上传报备信息 skuIds:{}", JSON.toJSONString(skuIds));
            skuAuthorizationService.saveSkuAuthrizationInfo(skuAuthrizationRequest, user);
            //解除商品报备待办
            goodsReportTodoHandler.finish(skuIds);
            return;
        }

        ArrayList<SkuAuthorizationItemVo> skuAuthorizationItemVos = new ArrayList<>(16);
        skuAuthorizationRequestVo.getSkuAuthorizationDtos().forEach(skuAuthorizationDto -> {
            SkuAuthorizationItemVo skuAuthorizationItemVo = new SkuAuthorizationItemVo();
            skuAuthorizationItemVo.setSnowFlakeId(skuAuthorizationDto.getSnowFlakeId() == null ?
                    SnowFlakeUtils.uniqueLong() : skuAuthorizationDto.getSnowFlakeId());
            List<String> regionIdStr = Arrays.stream(skuAuthorizationDto.getRegionsStr().split("@")).collect(Collectors.toList());
            ArrayList<Integer> regionIds = new ArrayList<>(16);
            CollectionUtils.collect(regionIdStr, new Transformer() {
                @Override
                public Object transform(Object o) {
                    return Integer.parseInt(o.toString());
                }
            }, regionIds);
            skuAuthorizationItemVo.setRegionIds(regionIds);

            List<String> terminalTypesStr = Arrays.stream(skuAuthorizationDto.getTerminalTypesStr().split("@"))
                    .collect(Collectors.toList());
            ArrayList<Integer> terminaltypeIds = new ArrayList<>();

            CollectionUtils.collect(terminalTypesStr, new Transformer() {
                @Override
                public Object transform(Object o) {
                    return Integer.parseInt(o.toString());
                }
            }, terminaltypeIds);
            skuAuthorizationItemVo.setTerminalTypeIds(terminaltypeIds);
            skuAuthorizationItemVos.add(skuAuthorizationItemVo);
        });
        skuAuthrizationRequest.setSkuAuthrizationItems(skuAuthorizationItemVos);
        skuAuthorizationService.saveSkuAuthrizationInfo(skuAuthrizationRequest, user);
    }

    /**
     * 保存sku修改历史记录
     */
    private Integer saveSkuHistory(String coreSkuHistory, User user, Integer skuId) {
        try {
            CoreSkuHistory coreSkuHistoryAdd = JSON.toJavaObject(JSONObject.parseObject(coreSkuHistory), CoreSkuHistory.class);
            coreSkuHistoryAdd.setModTime(System.currentTimeMillis());
            coreSkuHistoryAdd.setUpdater(user.getUserId());
            coreSkuHistoryAdd.setSkuId(skuId);
            return coreSkuHistoryService.insertOrUpdateCoreSkuHistory(coreSkuHistoryAdd);
        }catch (Exception e){
            logger.error("saveSkuHistory error", e);
            return 0;
        }
    }


    /**
     * 根据页面类型显示不同的SKU信息页
     *
     * @param model    视图
     * @param command  参数
     * @param request  请求
     * @param pageType 页面类型
     */
    @RequestMapping(value = "/viewSku")
    public String viewSku(Model model,
                          @ModelAttribute("command") SkuAddCommand command, HttpServletRequest request,
                          @RequestParam(value = "pageType", required = false, defaultValue = "1") String pageType,
                          @RequestParam(value = "type", required = false) Integer type) {
        String view = "";
        User sessUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        setIsSupplyAssistant(model, sessUser);
        model.addAttribute("showType", command.getShowType());
        //静态资源访问路径--->已迁移至oss
        model.addAttribute("api_http", api_http);
        model.addAttribute("docUrl", docUrl);

        try {
            // 初始化页面SPU信息
            CoreSpuBaseVO spuBaseVO = initBaseSpuPage(model, command);
            // 初始化页面SKU信息
            CoreSkuGenerate skuGenerate = goodsService.initSku(command, false);
            // SKU的主销售部门
            getMainDept(skuGenerate);
            // 补充sku区域商城名称
            goodsService.getSkuOrgName(model, skuGenerate);

            // 近一年有效成交数据
            goodsService.getOneYearEffectiveTransactionData(model, skuGenerate, type);

            // 初始化页面其他信息
            goodsService.initSkuPage(model, command);

            // 操作日志
            List<LogCheckGenerate> logCheckGenerateList = goodsService.listSkuCheckLog(command.getSkuId());
            this.dealLogCheckGenerateCreator(logCheckGenerateList);
            List<LogCheckGenerate> oldLogCheckList = new ArrayList<>();
            List<LogCheckGenerate> newLogCheckList = new ArrayList<>();
            logCheckGenerateList.forEach(item -> {
                if (item.getAddTime().getTime() < deadlineOfOldLogCheck) {
                    oldLogCheckList.add(item);
                } else {
                    newLogCheckList.add(item);
                }
            });
            model.addAttribute("oldLogCheckList", oldLogCheckList);
            model.addAttribute("newLogCheckList", newLogCheckList);

            // 商品分类
            BaseCategoryVo categoryVo = baseCategoryService.getBaseCategoryByParam(spuBaseVO.getCategoryId());
            if (categoryVo != null) {
                model.addAttribute("categoryType", categoryVo.getBaseCategoryType());
                model.addAttribute("categoryFullPath", baseCategoryService.getThirdCategoryFullPath(spuBaseVO.getCategoryId(), ">"));
            }

            // 获取分类对应科室
            String departmentNameStr = baseCategoryService.getDepartmentByBaseCategoryId(spuBaseVO.getCategoryId());
            model.addAttribute("departmentNameStr", departmentNameStr);

            List<InspectionItemVo> inspectionItemVos = inspectionItemApiService.getInspectionItemList(spuBaseVO.getCategoryId());
            String inspectionItemVoStr = inspectionItemVos.stream().filter(InspectionItemVo::getSelected).map(InspectionItemVo::getName).collect(Collectors.joining("、"));
            model.addAttribute("inspectionItemVoStr", inspectionItemVoStr);


            // 注册证/备案信息
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("userId", sessUser.getUserId());
            paramMap.put("firstEngageId", spuBaseVO.getFirstEngageId());
            FirstEngage firstEngage = firstEngageService.getFirstSearchDetailOnlyZczAttachment(paramMap, spuBaseVO.getFirstEngageId());
            if (firstEngage != null) {
                model.addAttribute("firstEngage", firstEngage);
                //判断注册证是否过期, true表示过期
                SimpleDateFormat ft = new SimpleDateFormat("yyyy-MM-dd");
                String nowDate = ft.format(new Date());
                if (StringUtils.isNotBlank(firstEngage.getEffectEndDate())
                        && firstEngage.getEffectEndDate().compareTo(nowDate) < 0){
                    model.addAttribute("overdue",true);
                }
                SysOptionDefinition option = getSysOptionDefinition(NumberUtils.toInt(firstEngage.getManageCategoryLevelShow()));
                if (option != null) {
                    firstEngage.setManageCategoryLevelShow(option.getTitle());
                }
            }

            // 查询价格变动信息
            model.addAttribute("lastestPriceChangeRecord", skuPriceModifyRecordService.getLastestPriceChangeRecord(command.getSkuId()));

            // 单位
            Map<String, Integer> unitParamMap = new HashMap<>(2);
            unitParamMap.put("goodsId", command.getSkuId());
            unitParamMap.put("type", 0);// 销售
            model.addAttribute("goodsChannelPriceList", goodsChannelPriceService.getGoodsChannelPriceByGoodsId(unitParamMap));
            unitParamMap.put("goodsId", command.getSkuId());
            unitParamMap.put("type", 1);// 采购
            model.addAttribute("goodsChannelPriceList2", goodsChannelPriceService.getGoodsChannelPriceByGoodsId(unitParamMap));

            // 商品库存
            Goods goods = new Goods();
            goods.setGoodsId(command.getSkuId());
            Map<String, WarehouseStock> stockInfoMap = warehouseStockService.getStockInfo(Lists.newArrayList("V" + command.getSkuId()));
            WarehouseStock stockInfo = stockInfoMap.get("V" + command.getSkuId());
            goods.setStockNum(stockInfo.getStockNum());
            goods.setOrderOccupy(stockInfo.getOccupyNum());
            model.addAttribute("stockInfo", stockInfo);
            model.addAttribute("goods", goods);

            // 获取主供应商列表信息
            List<TraderSupplierVo> mainSupplyList = traderSupplierService.getMainSupplyListByGoodsId(command.getSkuId());
            model.addAttribute("mainSupplyList", mainSupplyList);

            // 获取产品结算价
            GoodsSettlementPrice goodsSettlementPrice = new GoodsSettlementPrice();
            goodsSettlementPrice.setCompanyId(1);
            goodsSettlementPrice.setGoodsId(command.getSkuId());
            GoodsSettlementPrice goodsSettlementPriceInfo = goodsSettlementPriceService.getGoodsSettlePriceByGoods(goodsSettlementPrice);
            model.addAttribute("goodsSettlementPriceInfo", goodsSettlementPriceInfo);

            // 获取报备信息
            goodsService.getAuthorizationInfo(model, command.getSkuId());

            // 获取最近一次审核通过的贝登售后标准
            AfterSaleServiceStandardInfoDto afterSaleServiceStandardInfoDto = afterSaleServiceStandardService.getEffectAfterSalePolicy(skuGenerate.getSkuNo());
            model.addAttribute("afterSaleServiceStandardInfoDto", afterSaleServiceStandardInfoDto);
            if (afterSaleServiceStandardInfoDto != null) {
                List<AfterSaleServiceStandardInfoAttachment> attashmentList = afterSaleServiceStandardService.getAttashmentListByInfoId(afterSaleServiceStandardInfoDto.getServiceStandardInfoId());
                model.addAttribute("attashmentList", attashmentList);
            }

            // 查询包装信息：包括是否启用多级包装/中包装数量/箱包装数量
            GoodsPackageInfoVo goodsPackageInfoVo = new GoodsPackageInfoVo();
            GoodsPackageInfoDTO goodsPackageInfoDto = goodsPackageInfoService.getGoodPackageInfo(command.getSkuId(), command.getSpuId());
            goodsPackageInfoVo.setIsEnableMultistagePackage(goodsPackageInfoDto.getIsEnableMultistagePackage());
            goodsPackageInfoVo.setMidPackageNum(goodsPackageInfoDto.getMidPackageNum());
            goodsPackageInfoVo.setBoxPackageNum(goodsPackageInfoDto.getBoxPackageNum());
            model.addAttribute("goodsPackageInfo", goodsPackageInfoVo);

            // 获取商品审核信息异常
            goodsService.getGoodsAuditInfo(model, skuGenerate.getSkuId(), sessUser);

            // 获取Sku对应资料库资料
            DocOfGoodsDo docOfGoodsDo = goodsService.getDocOfGoodsBySkuId(command.getSkuId());
            model.addAttribute("docOfGoodsDo", docOfGoodsDo);

            // 根据页面类型返回数据
            view = goodsService.returnView(model, pageType, skuGenerate);
        } catch (Exception e) {
            logger.error("viewSku", e);
            command.setErrors(Lists.newArrayList(e.getMessage()));
        }
        return view;
    }

    private void getMainDept(CoreSkuGenerate skuGenerate) {
        // 获取SKU的主销售部门
        List<SkuMainDeptDto> mainDeptBySkuNo = skuMainDeptApiService.getMainDeptBySkuNo(skuGenerate.getSkuNo());
        if (CollectionUtils.isNotEmpty(mainDeptBySkuNo) && Objects.nonNull(mainDeptBySkuNo.get(0))) {
            skuGenerate.setMainDept(mainDeptBySkuNo.get(0).getMainDept());
        }
    }

    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET, value = "/listSkuTodoItems", produces = MediaType.APPLICATION_JSON_VALUE)
    public ModelAndView listSkuTodoItems(Integer skuId) {
        List<GoodsTodoItemVo> goodsTodoItemVoList = goodsCommonService.listSkuTodoItems(skuId, false);
        ModelAndView modelAndView = new ModelAndView("goods/vgoods/skuTodoList");
        modelAndView.addObject("goodsTodoItemVoList", goodsTodoItemVoList);
        return modelAndView;
    }

    /**
     * 检查是否为供应链助理
     *
     * @param model
     * @param sessUser
     */
    private void setIsSupplyAssistant(Model model, User sessUser) {
        List<Role> roles = roleService.getUserRoles(sessUser);
        Boolean isSupplyAssistant = false;
        if (CollectionUtils.isNotEmpty(roles)) {
            for (Role role : roles) {
                if ("供应管理组助理".equals(role.getRoleName())) {
                    isSupplyAssistant = true;
                    break;
                }
            }
        }
        model.addAttribute("isSupplyAssistant", isSupplyAssistant);
    }

    @RequestMapping(value = "/viewTempSkuAjax")
    @ResponseBody
    public ResultJSON viewTempSkuAjax(Model model, @ModelAttribute("command") SkuAddCommand command, HttpServletRequest request,
                                      HttpServletResponse response) throws Exception {
        try {
            //初始化页面SKU信息
            CoreSkuGenerate skuGenerate = goodsService.initSku(command, false);
            Map<String, String> map = Maps.newHashMap();
            if (StringUtils.isNotBlank(skuGenerate.getModel())) {
                map.put("skuInfo", skuGenerate.getModel());
            } else {
                map.put("skuInfo", skuGenerate.getSpec());
            }
            return ResultJSON.success().data(map);
        } catch (Exception e) {
            logger.error("viewSku", e);
            return ResultJSON.failed(e);
        }
    }

    /**
     * sku页面要那个这个方法初始化spu基本信息
     *
     * @param model
     * @param command
     * @return
     */
    private CoreSpuBaseVO initBaseSpuPage(Model model, SkuAddCommand command) {
        //所有SPU下的属性
        CoreSpuBaseDTO coreSpuDto = baseGoodsService.selectSpuBaseById(command.getSpuId());
        if (coreSpuDto == null) {
            if (command.getSkuId() != null) {
                CoreSkuBaseDTO skuBaseDTO = baseGoodsService.selectSkuBaseById(command.getSkuId());
                if (skuBaseDTO != null) {
                    command.setSpuId(skuBaseDTO.getSpuId());
                    coreSpuDto = baseGoodsService.selectSpuBaseById(skuBaseDTO.getSpuId());
                }
            }
        }
        if (coreSpuDto == null) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "找不到对应的SPU！");
        }
        CoreSpuBaseVO spuBaseVO = new CoreSpuBaseVO();
        if (StringUtils.isNotEmpty(coreSpuDto.getStorageConditionOthers())) {
            coreSpuDto.setStorageConditionOthersArray(GoodsStorageConditionOthersEnum.covert2OthersCodes(coreSpuDto.getStorageConditionOthers()));
        }
        BeanUtils.copyProperties(coreSpuDto, spuBaseVO);
        model.addAttribute("coreSpuDto", spuBaseVO);

        //设置一级商品类别
        model.addAttribute("spuTypeList", getSysOptionDefinitionInNewGoodsFlow());

        //根据商品类型决定sku的类型
        command.setSkuType(GoodsUtils.determineSkuTye(NumberUtils.toInt(coreSpuDto.getSpuType())));

        //若为新增sku时，需代入spu下的参数名
        if (command.getSkuId() == null) {
            if (ArrayUtils.isEmpty(command.getParamsName1()) && StringUtils.isNotBlank(coreSpuDto.getTechnicalParameterNames())) {
                String[] technicalParamNames = GoodsParameterUtils.splitToList(coreSpuDto.getTechnicalParameterNames()).toArray(new String[0]);
                String[] technicalParamValuePlaceholders = new String[technicalParamNames.length];
                Arrays.fill(technicalParamValuePlaceholders, StringUtils.EMPTY);
                command.setParamsName1(technicalParamNames);
                command.setParamsValue1(technicalParamValuePlaceholders);
            }

            //新增时代入SPU的商品等级和档位
            command.setGoodsLevelNo(spuBaseVO.getGoodsLevelNo());
            command.setGoodsPositionNo(spuBaseVO.getGoodsPositionNo());
        } else {
            //如果SKU等级与档位为空，代入SPU的信息
            CoreSkuGenerate skuQuery = goodsService.getSkuDoBySkuId(command.getSkuId());
            if (skuQuery == null) {
                throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1, "找不到对应的SKU信息！");
            }
            if (skuQuery.getGoodsLevelNo() == null || skuQuery.getGoodsLevelNo().equals(0)) {
                command.setGoodsLevelNo(skuQuery.getGoodsLevelNo());
            } else {
                command.setGoodsLevelNo(skuQuery.getGoodsLevelNo());
            }

            if (skuQuery.getGoodsPositionNo() == null || skuQuery.getGoodsPositionNo().equals(0)) {
                command.setGoodsPositionNo(skuQuery.getGoodsPositionNo());
            } else {
                command.setGoodsPositionNo(skuQuery.getGoodsPositionNo());
            }
        }

        List<GoodsLevelVo> goodsLevelVos = goodsCommonService.listAllGoodsLevel(false);
        List<GoodsPositionVo> goodsPositionVos = goodsCommonService.listAllGoodsPosition();

        Optional<GoodsLevelVo> goodsLevelIfMatch = goodsLevelVos.stream().filter(level -> Objects.equals(spuBaseVO.getGoodsLevelNo(), level.getId())).findFirst();
        if (goodsLevelIfMatch.isPresent()) {
            GoodsLevelVo goodsLevelVo = goodsLevelIfMatch.get();
            spuBaseVO.setGoodsLevelName(goodsLevelVo.getLevelName());
        } else {
            spuBaseVO.setGoodsLevelName(GoodsCommonService.DEFAULT_GOODS_LEVEL_NAME);
        }

        Optional<GoodsPositionVo> goodsPositionIfMatch = goodsPositionVos.stream().filter(position -> Objects.equals(spuBaseVO.getGoodsPositionNo(), position.getId())).findFirst();
        if (goodsPositionIfMatch.isPresent()) {
            GoodsPositionVo goodsPositionVo = goodsPositionIfMatch.get();
            spuBaseVO.setGoodsPositionName(goodsPositionVo.getPositionName());
        } else {
            spuBaseVO.setGoodsPositionName(GoodsCommonService.DEFAULT_GOODS_POSITION_NAME);
        }

        model.addAttribute("goodsLevelList", goodsLevelVos);
        model.addAttribute("goodsPositionList", goodsPositionVos);

        return spuBaseVO;
    }


    @RequestMapping(value = "/deleteSpu")
    @ResponseBody
    public ResultJSON deleteSpu(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand,
                                HttpServletRequest request, HttpServletResponse response) {
        try {
            Result ret = SpuValidate.checkDeleteSpu(spuCommand);
            if (!ret.isSuccess()) {
                return ResultJSON.failed().message(StringUtils.join(ret.getErrors(), ","));
            }
            goodsService.deleteSpu(spuCommand);
            return ResultJSON.success().message(CommonConstants.SUCCESS_MSG);
        } catch (Exception e) {
            logger.error("error", e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/deleteSku")
    @ResponseBody
    public ResultJSON deleteSku(Model model, @ModelAttribute("command") SpuSearchCommand spuCommand,
                                HttpServletRequest request, HttpServletResponse response) {
        try {
            Result ret = SpuValidate.checkDeleteSku(spuCommand);
            if (!ret.isSuccess()) {
                return ResultJSON.failed().message(StringUtils.join(ret.getErrors(), ","));
            }
            goodsService.deleteSku(spuCommand);
            return ResultJSON.success().message(CommonConstants.SUCCESS_MSG);
        } catch (Exception e) {
            logger.error("deleteSku", e);
            return ResultJSON.failed().message(e.getMessage());
        }
    }

    @RequestMapping(value = "/copySku")
    public String copySku(Model model, @ModelAttribute("command") SkuAddCommand command, @ModelAttribute("generate") CoreSkuGenerate generate) throws Exception {
        Result ret = SpuValidate.checkCopySku(command);
        if (!ret.isSuccess()) {
            String result = addSku(model, command, generate);
            command.setSkuId(null);
            command.setErrors(ret.getErrors());
            return result;
        }
        String result = addSku(model, command, generate);
        model.addAttribute("isCopy", 1);
        command.setSkuId(null);
        return result;
    }

    @RequestMapping(value = "/backupSku")
    @ResponseBody
    public ResultJSON backupSku(Model model, @ModelAttribute("command") SkuAddCommand command,
                                HttpServletRequest request, HttpServletResponse response) throws Exception {
        Result ret = SpuValidate.checkBackupSku(command);
        if (!ret.isSuccess()) {
            return ResultJSON.failed().message(StringUtils.join(ret.getErrors()));
        }
        try {
            goodsService.backupSku(command);
            return ResultJSON.success();
        } catch (Exception e) {
            logger.error("error", e);
            return ResultJSON.failed(e);
        }
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/static/skuTip")
    @ResponseBody
    public ResultJSON skuTip(Model model, Integer skuId,
                             HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (skuId == null) {
            return ResultJSON.failed().message("skuId is null");
        }
        try {
            return ResultJSON.success().data(goodsService.skuTip(skuId));
        } catch (Exception e) {
            logger.error("error", e);
            return ResultJSON.failed(e);
        }
    }
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/static/delRedisKey")
    @ResponseBody
    public ResultJSON key(Model model, String key,
                          HttpServletRequest request, HttpServletResponse response) throws Exception {

        try {
            JedisUtils.del(key);
            return ResultJSON.success().data("解除限制成功");
        } catch (Exception e) {
            logger.error("error", e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/static/clearLock")
    @ResponseBody
    public ResultJSON clearLock(Model model, String key,
                          HttpServletRequest request, HttpServletResponse response) throws Exception {
            return ResultJSON.success().data("success");
    }

    /**
     * @param model    command.getSkuId()  orderdetailid
     * @param request
     * @param response
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/static/getCostPrice")
    @ResponseBody
    public ResultJSON getCostPrice(Model model, @ModelAttribute("command") SkuAddCommand command,
                                   HttpServletRequest request, HttpServletResponse response) throws Exception {
        try {
            //command.getSkuId()  ==orderdetailid
            Map<String, Object> objectMap = goodsService.getCostPrice(command.getSkuId());
            return ResultJSON.success().data(objectMap);
        } catch (Exception e) {
            logger.error("error", e);
            return ResultJSON.failed(e);
        }
    }

    @RequestMapping(value = "/static/getCostPriceBySaleorderDetailIds")
    @ResponseBody
    public RestfulResult getCostPrice(Model model, @RequestBody OrderSkuCostPriceDto costPriceDto,
                                      HttpServletRequest request, HttpServletResponse response) throws Exception {
        RestfulResult<List<Map<String, Object>>> restfulResult = new RestfulResult<List<Map<String, Object>>>();
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            costPriceDto.getOrderDetailIds().forEach(item -> {
                Map<String, Object> objectMap = goodsService.getCostPrice(item);
                list.add(objectMap);
            });
            restfulResult.setData(list);
            return restfulResult;
        } catch (Exception e) {
            logger.error("error", e);
            restfulResult.setCode("error");
            restfulResult.setMessage(e.getMessage());
            return restfulResult;
        }
    }

    private String transFileInfo(String paths) {
        List<FileInfo> fileInfos = Lists.newArrayList();
        if (StringUtils.isNotBlank(paths)) {
            String[] urls = StringUtils.split(paths, ",");
            for (String url : urls) {
                FileInfo fileInfo = new FileInfo();

                //是阿里云的URL
                if (url.indexOf("resourceId") >= 0) {

                    fileInfo.setHttpUrl(api_http + domain);
                    fileInfo.setFilePath(url);
                    fileInfo.setFileName("");
                    fileInfo.setPrefix("");

                } else {

                    fileInfo.setHttpUrl(api_http + fileUrl);

                    int lastSplit = StringUtils.lastIndexOf(url, "/");
                    if (lastSplit > 1) {
                        fileInfo.setFilePath(StringUtils.substring(url, 0, lastSplit + 1));
                        fileInfo.setFileName(StringUtils.substring(url, lastSplit + 1));
                    }
                    fileInfo.setPrefix("jpg");
                }

                fileInfo.setCode(0);
                fileInfo.setMessage(null);
                fileInfos.add(fileInfo);
            }
        }
        return JsonUtils.convertConllectionToJsonStr(fileInfos);
    }

    /**
     * @param request: .
     * @description: 上传预计发货 日表格.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/14 3:47 下午.
     * @author: Tomcat.Hui.
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping(value = "/uploadSkuDeliveryRange")
    public ModelAndView uplodeSkuDeliveryRange(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("goods/goods/uplode_declare_delivery_range");
        return mv;
    }

    /**
     * @param request: .
     * @param lwfile:  .
     * @param id:      .
     * @description: 保存上传预计发货日表格数据.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/14 3:53 下午.
     * @author: Tomcat.Hui.
     * @return: com.vedeng.common.model.ResultInfo<?>.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping("/saveUplodeSkuDeliveryRange")
    @SystemControllerLog(operationType = "import", desc = "保存上传预计发货日表格数据")
    public ResultInfo<?> saveUplodeSkuDeliveryRange(HttpServletRequest request,
                                                    @RequestParam("lwfile") MultipartFile lwfile, @RequestParam(required = false) Integer id) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/goods");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);
            if (fileInfo.getCode() == 0) {

                try {
                    dealSkuExcel(user, fileInfo);
                } catch (Exception e) {
                    return new ResultInfo<>(-1, "保存上传采购到货时长表格数据出现异常:" + e.getMessage());
                }
                return new ResultInfo<>(0, "更新成功");
            }
        } catch (Exception e) {
            logger.error("保存上传采购到货时长表格数据出现异常:", e);
            return resultInfo;
        }
        return resultInfo;
    }


    /**
     * 【spu迁移准备阶段】获取待移动SPU的相关信息
     *
     * @since ERP_LV_2020_56
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/checkSPUAttributes", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo prepareSpuRemoval(Integer categoryId, Integer[] spuIds) {
        try {
            return ResultInfo.success(goodsRemovalService.prepareSpuRemoval(categoryId, spuIds));
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                logger.info("【spu迁移】准备阶段发生错误 - message:{}", e.getMessage());
            } else {
                logger.error("【spu迁移】准备阶段发生未知错误", e);
            }

            return ResultInfo.error();
        }
    }


    /**
     * 【spu迁移阶段触发】 - 获取单个spu移动时属性及属性值变动情况
     *
     * @since ERP_LV_2020_56
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/getSpuRemovalDetailTemp", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo getSpuRemovalDetailWhenConfirmPhase(Integer categoryId, Integer spuId) {
        try {
            List<SpuRemovalDetailVo> spuRemovalDetailList = goodsRemovalService.listCategoryAttributeDetailsBeforeChange(categoryId, spuId);
            return ResultInfo.success(spuRemovalDetailList);
        } catch (Exception e) {
            logger.error("【spu迁移】获取spu移动阶段目标分类的需要增补的属性及属性值明细发生错误 - targetCategoryId:{}, spuId:{}", categoryId, spuId, e);
            return ResultInfo.error();
        }
    }

    /**
     * 【spu迁移阶段】属性迁移列表页面
     *
     * @param categoryId
     * @param spuId
     * @return
     * @since ERP_LV_2020_56
     */
    @RequestMapping(method = RequestMethod.GET, value = "/getSpuRemovalDetail", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView getSpuRemovalDetail(Integer categoryId, @RequestParam(required = false) Integer spuId, @RequestParam(required = false) Integer spuRemovalLogId) {
        ModelAndView modelAndView = new ModelAndView("goods/vgoods/spuRemovalDetail");

        List<SpuRemovalDetailVo> spuRemovalDetailList;
        if (spuId != null) {
            spuRemovalDetailList = goodsRemovalService.listCategoryAttributeDetailsBeforeChange(categoryId, spuId);
        } else if (spuRemovalLogId != null) {
            spuRemovalDetailList = goodsRemovalService.listCategoryAttributeDetailsAfterChange(categoryId, spuRemovalLogId);
        } else {
            throw new ShowErrorMsgException("缺失必要参数");
        }

        modelAndView.addObject("spuRemovalDetailList", spuRemovalDetailList);
        return modelAndView;
    }

    /**
     * 【spu迁移完成后】SKU迁移列表
     *
     * @param spuId
     * @return
     * @since ERP_LV_2020_56
     */
    @RequestMapping(method = RequestMethod.GET, value = "/getSkuInfoAfterSpuRemoval", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView getSkuInfoAfterSpuRemoval(Integer spuId) {
        ModelAndView modelAndView = new ModelAndView("goods/vgoods/skuRemovalDetail");
        List<CoreSkuBaseVO> coreSkuBaseVOS = goodsRemovalService.listSkuInfoAfterSpuRemoval(spuId);
        modelAndView.addObject("skuList", coreSkuBaseVOS);
        return modelAndView;
    }

    /**
     * 【spu迁移确认】- 确认移动SPU接口
     *
     * @since ERP_LV_2020_56
     */
    @ResponseBody
    @RequestMapping(method = RequestMethod.POST, value = "/confirmSPUsRemoval", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo confirmSupRemoval(Integer categoryId, Integer[] spuIds, String reason) {
        try {
            goodsRemovalService.confirmSpuRemoval(categoryId, spuIds, reason);
        } catch (Exception e) {
            if (e instanceof IllegalArgumentException) {
                logger.info("【spu迁移】确认阶段发生错误 - message:{}", e.getMessage());
            } else {
                logger.error("【spu迁移】确认阶段发生未知错误", e);
            }
            return ResultInfo.error();
        }

        return ResultInfo.success();
    }


    private final static String EXPORT_FILE_NAME = "商品迁移记录";
    private final static String EXPORT_FILE_ETX_NAME = ".xls";

    /**
     * 商品迁移记录页面
     *
     * @return
     */
    @RequestMapping(value = "/goodsRemovalRecord", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView obtainGoodsRemovalRecordPage(GoodsRemovalRecordQueryDto goodsRemovalRecordQueryDto, HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView("goods/vgoods/goodsRemovalRecord");
        Page page = getPageTag(request, goodsRemovalRecordQueryDto.getPageNo(), goodsRemovalRecordQueryDto.getPageSize());
        List<GoodsRemovalRecordVo> goodsRemovalRecordVoList = goodsRemovalService.listGoodsRemovalRecordWithPage(goodsRemovalRecordQueryDto, page);
        modelAndView.addObject("page", page);
        modelAndView.addObject("queryCondition", goodsRemovalRecordQueryDto);
        modelAndView.addObject("goodsRemovalRecordVoList", goodsRemovalRecordVoList);
        return modelAndView;
    }


    /**
     * 导出商品迁移记录
     *
     * @param response
     */
    @RequestMapping(value = "/exportGoodsRemovalRecord")
    public void exportGoodsRemovalRecord(Integer[] spuRemovalRecordIds, HttpServletResponse response) {

        ServletOutputStream outputStream = null;
        try {
            outputStream = response.getOutputStream();

            String filename;
            try {
                filename = URLEncoder.encode(EXPORT_FILE_NAME + EXPORT_FILE_ETX_NAME, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                filename = "untitled" + EXPORT_FILE_ETX_NAME;
            }

            response.setHeader("Content-Disposition", "attachment;fileName=" + filename);
            response.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_OCTET_STREAM_VALUE);
            goodsRemovalService.exportGoodsRemovalRecords(spuRemovalRecordIds, outputStream);

            response.flushBuffer();
        } catch (IOException e) {
            logger.error("导出商品迁移记录时发生错误", e);
        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    //No-op
                }
            }
        }
    }

    @ResponseBody
    @RequestMapping(value = "/static/generateWarnDays", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo generateEffectiveDays(Integer categoryId, String day) {
        try {
            if (day == null || !NumberUtils.isNumber(day)) {
                return ResultInfo.error();
            }
            Map<String, Integer> days = Maps.newHashMap();
            EffectDayResult result = GoodsUtils.generateNearTermWarnDays(NumberUtils.toInt(day));
//            days.put("nearWarnDay",generateNearTermWarnDays(day,range,nearWarn) );
//             days.put("overWarnDay",generateNearTermWarnDays(day,range,overWarn) );
            return ResultInfo.success(result);
        } catch (Exception e) {
            logger.error("效期预警天数", e);
            return ResultInfo.error();
        }
    }


    /**
     * 校验spu名称是否重复（选择注册证触发）
     *
     * @return
     * @since ERP_LV_2020_86
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(method = RequestMethod.GET, value = "/checkSpuNameIfDuplicate", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<Integer> checkSpuNameIfDuplicate(Integer firstEngageId) {
        if (firstEngageId == null || firstEngageId < 0) {
            return ResultInfo.error();
        }
        return ResultInfo.success(goodsService.countSpuByFirstEngageId(firstEngageId));
    }


    /**
     * 商品分级分档页面
     *
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/viewGoodsLevelAndPosition", produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView obtainGoodsRemovalRecordPage() {
        ModelAndView modelAndView = new ModelAndView("goods/vgoods/goodsLevelAndPosition");
        GoodsTodoSummaryVo goodsTodoSummaryVo = goodsCommonService.statGoodsLevelAndPosition();
        modelAndView.addObject("goodsTodoSummaryVo", goodsTodoSummaryVo);
        return modelAndView;
    }


    /**
     * @param sheet: .
     * @param sheet: .
     * @description: 获取Excel行号list.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 7:46 下午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<java.lang.Integer>.
     * @throws: .
     */
    private List<Integer> getNumList(Sheet sheet) {
        return IntStream.rangeClosed(sheet.getFirstRowNum() + 1, sheet.getLastRowNum())
                .boxed().collect(Collectors.toList());
    }

    /**
     * @param sheet: .
     * @description: 分组并行.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 7:46 下午.
     * @author: Tomcat.Hui.
     * @return: java.util.List<java.util.List < java.lang.Integer>>.
     * @throws: .
     */
    private List<List<Integer>> getNumGroup(Sheet sheet) {

        List<Integer> numList = getNumList(sheet);
        return Lists.partition(numList, 100);
    }

    /**
     * @param sheet: .
     * @description: excel全局验证 (是否为空、sku是否重复).
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: 1.表格不为空 2.SKU不重复 3.所有cell均为字符类型
     * @version: 1.0.
     * @date: 2020/5/15 7:46 下午.
     * @author: Tomcat.Hui.
     * @return: java.lang.String.
     * @throws: .
     */
    private String validExcel(Sheet sheet) {

        if (null == sheet || sheet.getLastRowNum() == 0) {
            return "表格数据为空，操作失败";
        }

        Set<String> skuNameSet = Sets.newHashSet();

        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);

            if (row.getCell(0) == null) {
                return (i + 1) + "行订货号必填，请检查后重试";
            }
            if (skuNameSet.contains(row.getCell(0).getStringCellValue())) {
                return "订货号" + row.getCell(0).getStringCellValue() + "重复，提交失败";
            }
            if (row.getCell(1) != null) {
                if (row.getCell(1).getCellType() == CellType.NUMERIC) {
                    row.getCell(1).setCellType(CellType.STRING);
                    String cellValue = row.getCell(1).getStringCellValue();
                    if (cellValue.contains(".") || Integer.parseInt(cellValue) < 1) {
                        return "订货号" + row.getCell(0).getStringCellValue() + "采购到货时长,必须是≥1的整数，请检查后重试";
                    }
                } else {
                    row.getCell(1).setCellType(CellType.STRING);
                    String cellValue = row.getCell(1).getStringCellValue();
                    if (StrUtil.isNotEmpty(cellValue)) {
                        return "订货号" + row.getCell(0).getStringCellValue() + "采购到货时长,必须是≥1的整数，请检查后重试";
                    }

                }
            }

            if (row.getCell(2) != null) {
                if (row.getCell(2).getCellType() != CellType.STRING) {
                    return "发货方式字段只可填写直发或普发";
                }

                String cellValue = row.getCell(2).getStringCellValue();

                if (!(StrUtil.isEmpty(cellValue) || "普发".equals(cellValue) || "直发".equals(cellValue))) {
                    return "发货方式字段只可填写直发或普发";
                }
            }
            skuNameSet.add(row.getCell(0).getStringCellValue());
        }

        return null;
    }

    /**
     * @param list:  .
     * @param sheet: .
     * @description: 验证组内cell数据.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 7:47 下午.
     * @author: Tomcat.Hui.
     * @return: java.lang.String.
     * @throws: .
     */
    private String validCells(List<Integer> list, Sheet sheet, Set<String> range) {
        return list.stream().map(no -> {
            Row row = sheet.getRow(no);
            if (row.getCell(0) == null && (row.getCell(1) != null && row.getCell(2) != null)) {
                return no + "行订货号必填，请检查后重试";
            } else if (row.getCell(0) != null && row.getCell(2) != null && row.getCell(2).getCellType() == CellType.STRING) {
                if (!range.contains(row.getCell(2))) {
                    return no + "行3列数据必须是0-3,3-5,5-10,10-15，15+或空，请检查后重试";
                }
            }
            return null;
        }).filter(s -> StringUtil.isNotBlank(s)).findFirst().orElse(null);
    }

    /**
     * @param list:  .
     * @param sheet: .
     * @description: 验证组内sku是否存在.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 7:48 下午.
     * @author: Tomcat.Hui.
     * @return: java.lang.String.
     * @throws: .
     */
    private String validExist(List<Integer> list, Sheet sheet) {
        List<CoreSkuGenerate> skuList = goodsService.getSkuListByNo(
                list.stream().map(no -> sheet.getRow(no).getCell(0).getStringCellValue())
                        .collect(Collectors.toList())
        );

        if (list.size() == skuList.size()) {
            return null;
        } else {
            String skuNo = list.stream()
                    .map(no -> sheet.getRow(no).getCell(0).getStringCellValue())
                    .collect(Collectors.toList())
                    .stream().filter(
                            x -> !skuList.stream().map(CoreSkuGenerate::getSkuNo).collect(Collectors.toList()).contains(x)
                    ).findFirst().orElse(null);

            return "订货号" + skuNo + "不存在，提交失败";
        }
    }


    @RequestMapping(value = "/viewSkuBySkuId")
    public String viewSkuBySkuId(Model model, String skuNo, HttpServletRequest request,
                                 HttpServletResponse response, @RequestParam(value = "pageType", required = false, defaultValue = "0") String pageType) throws Exception {
        try {
//            Integer skuId=goodsService.getSkuIdBySkuNo(skuNo);
//            if (skuId==null){
//                return null;
//            }
//            Integer spuId=null;
//            if (skuId!=null){
//                spuId=goodsService.getSpuIdBySkuId(skuId);
//            }
            return "redirect:/goods/vgoods/viewSku.do?pageType=1&skuId=" + skuNo.substring(1);
        } catch (Exception e) {
            logger.error("跳转sku页面出错", e);
            return "redirect:/";
        }
    }


    /**
     * @param user:     .
     * @param fileInfo: .
     * @description: 处理excel.
     * @jira: VDERP-2217 提供预计发货时间给前台.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/15 7:49 下午.
     * @author: Tomcat.Hui.
     * @return: void.
     * @throws: .
     */
    private void dealSkuExcel(User user, FileInfo fileInfo) throws Exception {
        logger.info("用户 " + user.getUsername() + " 开始批量修改填报采购到货时长");

        FileInputStream fileInputStream = null;
        Workbook workbook = null;
        try {
            fileInputStream = new FileInputStream(new File(fileInfo.getFilePath()));
            workbook = WorkbookFactory.create(fileInputStream);
            Sheet sheet = workbook.getSheetAt(0);

            String validExcelResult = validExcel(sheet);
            if (StringUtil.isNotBlank(validExcelResult)) {
                throw new Exception(validExcelResult);
            }
            logger.info("用户 " + user.getUsername() + " 批量修改填报采购到货时长 excel校验通过");

            List<List<CoreSkuGenerate>> skuToUpdate = Lists.newArrayList();

            List<List<Integer>> numGroup = getNumGroup(sheet);
            for (int i = 0; i < numGroup.size(); i++) {
                List<Integer> list = numGroup.get(i);

                String notExitSku = validExist(list, sheet);
                if (StringUtil.isNotBlank(notExitSku)) {
                    throw new Exception(notExitSku);
                }

                List<CoreSkuGenerate> skuList = new ArrayList<>();

                for (Integer no : list) {
                    CoreSkuGenerate sku = new CoreSkuGenerate();
                    sku.setSkuNo(sheet.getRow(no).getCell(0).getStringCellValue());

                    String shipment = null;
                    if (sheet.getRow(no).getCell(2) != null) {
                        shipment = sheet.getRow(no).getCell(2).getStringCellValue();
                    }
                    if (!(StrUtil.isEmpty(shipment) || "普发".equals(shipment) || "直发".equals(shipment))) {
                        throw new Exception("发货方式字段只可填写直发或普发");
                    }
                    if (sheet.getRow(no).getCell(1) != null) {
                        String purchaseTimeValue = sheet.getRow(no).getCell(1).getStringCellValue();
                        if (StringUtils.isNotBlank(purchaseTimeValue)) {
                            sku.setPurchaseTime(purchaseTimeValue);
                        }
                    }
                    sku.setIsDirect(StrUtil.isEmpty(shipment)?null:"直发".equals(shipment)?1:0);
                    sku.setPurchaseTimeUpdateTime(new Date());
                    sku.setUpdater(user.getUserId());
                    sku.setModTime(new Date());
                    skuList.add(sku);
                }

                skuToUpdate.add(skuList);
            }
            logger.info("用户 " + user.getUsername() + " 批量修改填报采购到货时长 完成分页校验");
            Integer result = goodsService.updateSkuDeliveryRange(skuToUpdate);
            logger.info("用户 " + user.getUsername() + " 处理批量修改填报采购到货时长结束 预计处理条数" + skuToUpdate.size() + " ,实际处理条数: " + result);

        } catch (Exception e) {
            logger.error("用户 " + user.getUsername() + " 处理批量修改填报采购到货时长出现异常: ", e);
            throw e;
        }finally {
            if (fileInputStream != null) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                    log.error("【dealSkuExcel】处理异常",e);
                }
            }

            if (workbook != null) {
                try {
                    workbook.close();
                } catch (Exception e) {
                    log.error("【dealSkuExcel】处理异常",e);
                }
            }
        }
    }

    /**
     * 批量维护sku报备信息初始化
     *
     * @param skuIdsStr
     * @return
     */
    @ResponseBody
    @RequestMapping("/skuAuthorizationInit")
    public ModelAndView skuAuthorizationInit(String skuIdsStr) {
        ModelAndView modelAndView = new ModelAndView("/goods/vgoods/batch_save_authorization");
        modelAndView.addObject("skuIdsStr", skuIdsStr);
        modelAndView.addObject("regions", regionService.getRegionByParentId(1));
        modelAndView.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());
        List<String> skuIdsStrList = Arrays.stream(skuIdsStr.split("@")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuIdsStrList) || skuIdsStrList.size() > 1) {
            return modelAndView;
        }
        SkuAddCommand skuAddCommand = new SkuAddCommand();
        skuAddCommand.setSkuId(Integer.parseInt(skuIdsStrList.get(0)));
        try {
            CoreSkuGenerate skuGenerate = goodsService.initSku(skuAddCommand, false);
            modelAndView.addObject("skuGenerate", skuGenerate);
        } catch (Exception e) {
            logger.error("初始化Sku异常");
        }
        List<SkuAuthorizationDto> skuAuthorizationResponses = getSkuAuthorizationInfo(Integer.parseInt(skuIdsStrList.get(0)));
        if (CollectionUtils.isNotEmpty(skuAuthorizationResponses)) {
            modelAndView.addObject("skuAuthorizationResponses", skuAuthorizationResponses);
        }
        return modelAndView;
    }

    /**
     * 批量维护SKU报备信息
     *
     * @param skuIdsStr
     * @param session
     * @param skuAuthorizationRequest
     * @return
     */
    @ResponseBody
    @RequestMapping("/batchSaveSkuAuthorizationInfo")
    public ResultInfo batchSaveSkuAuthorizationInfo(String skuIdsStr, HttpSession session, CoreSkuGenerate skuGenerate,
                                                    SkuAuthorizationRequestVo skuAuthorizationRequest) {
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        User sessUser = (User) session.getAttribute(ErpConst.CURR_USER);
        ArrayList<Integer> skuIds = new ArrayList<>(16);
        List<String> skuIdsStrs = Arrays.stream(skuIdsStr.split("@")).collect(Collectors.toList());
        CollectionUtils.collect(skuIdsStrs, new Transformer() {
            @Override
            public Object transform(Object o) {
                return Integer.parseInt(o.toString());
            }
        }, skuIds);
        goodsService.batchSaveSkuAuthorization(skuIds, skuGenerate);
        if (skuGenerate.getIsNeedReport() == 1 && skuGenerate.getIsAuthorized() == 1) {
            saveSkuAuthorizationInfo(skuAuthorizationRequest, skuIds, sessUser);
        }
        resultInfo.setCode(0);
        return resultInfo;
    }


    @Autowired
    @Qualifier("goodsChannelPriceService")
    private GoodsChannelPriceService goodsChannelPriceService;
    @Autowired
    @Qualifier("traderSupplierService")
    private TraderSupplierService traderSupplierService;
    @Autowired
    @Qualifier("goodsSettlementPriceService")
    private GoodsSettlementPriceService goodsSettlementPriceService;

    /**
     * 商品禁用开始申请审核
     */

    @ResponseBody
    @RequestMapping(value = "/disableGoodsApplyVerify", method = RequestMethod.POST, produces = "application/json;charset=utf-8")
    @NoNeedAccessAuthorization
    public ResultInfo disableGoodsApplyVerify(HttpServletRequest request, HttpSession session, @RequestBody InvalidQueryDto invalidQueryDto) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        Integer relatedId = invalidQueryDto.getRelatedId();
        String disableReason = invalidQueryDto.getDisableReason();
        Integer goodsType = invalidQueryDto.getGoodsType();
        try {
            //前置校验
            ResultInfo resultInfo = vGoodsCommonService.checkDisableSpuOrSku(user, relatedId, goodsType, disableReason);
            if (resultInfo.getCode() == null || resultInfo.getCode() != 0) {
                return resultInfo;
            }
            //开始申请审核
            goodsService.disableGoodsApplyVerify(relatedId, disableReason, user.getUserId(), goodsType);
        } catch (Exception e) {
            String goodsTypeStr = SPU_TYPE.equals(goodsType) ? "SPU" : "SKU";
            logger.error("禁用商品申请审核错误，关联ID：" + goodsTypeStr + relatedId, e);
            return ResultInfo.error("申请审核失败！");
        }
        return ResultInfo.success();
    }


    /**
     * <b>Description:</b><br>
     * 商品禁用审核操作
     */
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, Integer relatedId, Integer goodsType, Boolean pass, @RequestParam(required = false) String lastCheckReason) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        try {
            //前置校验
            if (pass) {
                ResultInfo resultInfo = vGoodsCommonService.checkDisableSpuOrSkuWithoutReason(user, relatedId, goodsType);
                if (resultInfo.getCode() == null || resultInfo.getCode() != 0) {
                    return resultInfo;
                }
            }

            //完成审核
            goodsService.completeDisableTask(taskId, user.getUserId(), lastCheckReason, pass);
        } catch (Exception e) {
            String goodsTypeStr = SPU_TYPE.equals(goodsType) ? "SPU" : "SKU";
            logger.error("商品禁用审核操作任务完成失败，关联ID：" + goodsTypeStr + relatedId, e);
            return ResultInfo.error("商品禁用审核操作任务完成失败！");
        }
        return ResultInfo.success();
    }


    /**
     * 启用商品
     */

    @ResponseBody
    @RequestMapping(value = "/enableGoods")
    @NoNeedAccessAuthorization
    public ResultInfo enableGoods(HttpServletRequest request, HttpSession session, Integer relatedId, Integer goodsType) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        try {
            //前置校验
            ResultInfo resultInfo = vGoodsCommonService.checkAbleSpuOrSku(user, relatedId, goodsType);
            if (resultInfo.getCode() == null || resultInfo.getCode() != 0) {
                return resultInfo;
            }

            //启用商品
            goodsService.enbleGoodsApplyVerify(relatedId, user.getUserId(), goodsType);
        } catch (Exception e) {
            String goodsTypeStr = SPU_TYPE.equals(goodsType) ? "SPU" : "SKU";
            logger.error("启用商品错误，关联ID：" + goodsTypeStr + relatedId, e);
            return ResultInfo.error("启用失败！");
        }
        return ResultInfo.success();
    }

    /**
     * <AUTHOR>
     * @desc 新增虚拟商品关联费用
     * @param request
     * @return
     */
    @RequestMapping("/initChooseSku")
    public ModelAndView initChooseSku(HttpServletRequest request){
        ModelAndView mv = new ModelAndView();
        User user = getSessionUser(request);
        mv.addObject("user",user);
        //查询商品费用所有编码
        List<CostCategoryApiDto> costCategoryApiDtos = costCategroryApiService.queryAllValidInfos();
        mv.addObject("costCategoryApiDtos",costCategoryApiDtos);
        mv.setViewName("goods/vgoods/sku/virture_sku_add");
        return mv;
    }

    @RequestMapping(value = "/getSkuGoodesUnrealfoBySku")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getSkuGoodesUnrealfoBySku(Long relatedId) {
        CoreSkuGenerate skuGoodesUnrealfoBySku = goodsService.getSkuGoodesUnrealfoBySku(relatedId);
        return R.success(skuGoodesUnrealfoBySku);
    }



    @RequestMapping("/editChooseSku")
    @NoNeedAccessAuthorization
    public ModelAndView editChooseSku(HttpServletRequest request, Integer relatedId){
        ModelAndView mv = new ModelAndView();
        CoreSkuGenerate skuGoodesUnrealfoBySku = goodsService.getSkuGoodesUnrealfoBySku((long)relatedId);
        Integer costCategoryId = skuGoodesUnrealfoBySku.getCostCategoryId();
        Integer status = skuGoodesUnrealfoBySku.getStatus();
        User user = getSessionUser(request);
        mv.addObject("user",user);
        mv.addObject("relatedId",relatedId);
        mv.addObject("costCategoryId",costCategoryId);
        mv.addObject("status",status);
        //查询商品费用所有编码
        List<CostCategoryApiDto> costCategoryApiDtos = costCategroryApiService.queryAllValidInfos();
        mv.addObject("costCategoryApiDtos",costCategoryApiDtos);
        mv.setViewName("goods/vgoods/sku/virture_sku_edit");
        return mv;
    }


    @RequestMapping("/saveVirtureSku")
    @ResponseBody
    public ResultInfo saveVirtureSku(HttpServletRequest request,VirtualSkuDto virtualSkuDto){
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        User user = getSessionUser(request);
        resultInfo = goodsService.saveVirtureSku(virtualSkuDto,user);
        return resultInfo;
    }


    @RequestMapping("/updateVirtureSku")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo updateVirtureSku(HttpServletRequest request,VirtualSkuDto virtualSkuDto){
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        User user = getSessionUser(request);
        goodsService.updateVirtureSku(virtualSkuDto,user);
        return resultInfo;
    }

    @RequestMapping(value = "/searchSku")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<List<SelectDto>> skuNoSearch(Model model, String keyword, HttpServletRequest request,
                          HttpServletResponse response) {
        List<CoreSku> skuNoList = goodsService.searchSku(keyword);
        //skuNoList转换成List<Map>格式
        List<SelectDto> result = skuNoList.stream().map(sku -> {
            SelectDto map = new SelectDto(sku.getSkuId(),sku.getSkuNo(),sku.getSkuNo()+" "+sku.getShowName());
            return map;
        }).collect(Collectors.toList());
        return R.success(result);
    }



}
