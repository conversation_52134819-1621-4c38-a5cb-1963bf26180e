<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.authorization.dao.ActionMapper">
	<resultMap id="BaseResultMap" type="com.vedeng.authorization.model.Action">
		<id column="ACTION_ID" property="actionId" jdbcType="INTEGER" />
		<result column="ACTIONGROUP_ID" property="actiongroupId" jdbcType="INTEGER" />
		<result column="CONTROLLER_NAME" property="controllerName" jdbcType="VARCHAR" />
		<result column="ACTION_NAME" property="actionName" jdbcType="VARCHAR" />
		<result column="MODULE_NAME" property="moduleName" jdbcType="VARCHAR" />
		<result column="ACTION_DESC" property="actionDesc" jdbcType="VARCHAR" />
		<result column="IS_MENU" property="isMenu" jdbcType="INTEGER" />
		<result column="SORT" property="sort" jdbcType="INTEGER" />
		<result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
		<result column="CREATOR" property="creator" jdbcType="INTEGER" />
		<result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
		<result column="UPDATER" property="updater" jdbcType="INTEGER" />
		<result column="NAME" property="groupName" jdbcType="VARCHAR"/>
		<result column="PLATFORM_ID" property="platformId" jdbcType="INTEGER"/>
	</resultMap>

	<!-- 查询字段 -->
	<sql id="Base_Column_List">
		ACTION_ID, ACTIONGROUP_ID, CONTROLLER_NAME, ACTION_NAME,
		MODULE_NAME,
		ACTION_DESC,
		IS_MENU, SORT, ADD_TIME, CREATOR, MOD_TIME, UPDATER, PLATFORM_ID
	</sql>

	<!-- 查询节点信息列表 -->
	<select id="querylistpage" parameterType="Map" resultType="com.vedeng.authorization.model.Action">
		select
			a.ACTION_ID AS actionId,a.ACTIONGROUP_ID AS actiongroupId,a.CONTROLLER_NAME AS controllerName,
			a.ACTION_NAME AS actionName,a.MODULE_NAME AS moduleName,a.ACTION_DESC AS actionDesc,
			a.IS_MENU AS isMenu,a.ADD_TIME AS addTime,a.CREATOR AS creator,a.MOD_TIME AS modTime,
			a.UPDATER AS updater,b.NAME AS groupName,a.SORT AS sort,a.PLATFORM_ID AS platformId
		from T_ACTION a left join T_ACTIONGROUP b on a.ACTIONGROUP_ID = b.ACTIONGROUP_ID
		<where>
			1 = 1
			<if test="action.actiongroupId != null and action.actiongroupId > 0">
				and a.ACTIONGROUP_ID = #{action.actiongroupId,jdbcType=INTEGER}
			</if>
			<if test="action.isMenu != null and action.isMenu != -1">
				and a.IS_MENU = #{action.isMenu,jdbcType=INTEGER}
			</if>
			<if test="action.controllerName!=null and action.controllerName!=''">
				and a.CONTROLLER_NAME like
				CONCAT('%',#{action.controllerName},'%' )
			</if>
			<if test="action.actionName!=null and action.actionName!=''">
				and a.ACTION_NAME like
				CONCAT('%',#{action.actionName},'%' )
			</if>
			<if test="action.moduleName!=null and action.moduleName!=''">
				and a.MODULE_NAME like
				CONCAT('%',#{action.moduleName},'%' )
			</if>
			<if test="action.actionDesc!=null and action.actionDesc!=''">
				and a.ACTION_DESC like
				CONCAT('%',#{action.actionDesc},'%' )
			</if>
			<if test="action.platformId!=null and action.platformId!=''">
				and a.PLATFORM_ID = #{action.platformId,jdbcType=INTEGER}
			</if>
		</where>
		<choose>
			<when test="action.sortField!=null and action.order!=null">
				order by ${action.sortField} ${action.order}
			</when>
			<otherwise>
				order by a.SORT DESC
			</otherwise>
		</choose>
	</select>

	<!-- 根据主键查询节点详细信息 -->
	<select id="getActionByKey" resultMap="BaseResultMap"
		parameterType="java.lang.Integer">
		select
		<include refid="Base_Column_List" />
		from T_ACTION
		where ACTION_ID = #{actionId,jdbcType=INTEGER}
	</select>

	<!-- delete -->
	<delete id="delete" parameterType="java.lang.Integer">
		delete from T_ACTION
		where ACTION_ID = #{actionId,jdbcType=INTEGER}
	</delete>

	<!-- 添加节点保存数据 -->
	<insert id="insert" parameterType="com.vedeng.authorization.model.Action">
		insert into T_ACTION
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="actionId != null">
				ACTION_ID,
			</if>
			<if test="actiongroupId != null">
				ACTIONGROUP_ID,
			</if>
			<if test="controllerName != null">
				CONTROLLER_NAME,
			</if>
			<if test="actionName != null">
				ACTION_NAME,
			</if>
			<if test="moduleName != null">
				MODULE_NAME,
			</if>
			<if test="actionDesc != null">
				ACTION_DESC,
			</if>
			<if test="isMenu != null">
				IS_MENU,
			</if>
			<if test="sort != null">
				SORT,
			</if>
			<if test="addTime != null">
				ADD_TIME,
			</if>
			<if test="creator != null">
				CREATOR,
			</if>
			<if test="modTime != null">
				MOD_TIME,
			</if>
			<if test="updater != null">
				UPDATER,
			</if>
			<if test="platformId != null">
				PLATFORM_ID,
			</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="actionId != null">
				#{actionId,jdbcType=INTEGER},
			</if>
			<if test="actiongroupId != null">
				#{actiongroupId,jdbcType=INTEGER},
			</if>
			<if test="controllerName != null">
				#{controllerName,jdbcType=VARCHAR},
			</if>
			<if test="actionName != null">
				#{actionName,jdbcType=VARCHAR},
			</if>
			<if test="moduleName != null">
				#{moduleName,jdbcType=VARCHAR},
			</if>
			<if test="actionDesc != null">
				#{actionDesc,jdbcType=VARCHAR},
			</if>
			<if test="isMenu != null">
				#{isMenu,jdbcType=BIT},
			</if>
			<if test="sort != null">
				#{sort,jdbcType=INTEGER},
			</if>
			<if test="addTime != null">
				#{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				#{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				#{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				#{updater,jdbcType=INTEGER},
			</if>
			<if test="platformId != null">
				#{platformId,jdbcType=INTEGER},
			</if>
		</trim>
	    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="actionId">
			SELECT LAST_INSERT_ID() AS actionId
	    </selectKey>
	</insert>

	<!-- 根据主键修改节点信息 -->
	<update id="update" parameterType="com.vedeng.authorization.model.Action">
		update T_ACTION
		<set>
			<if test="actiongroupId != null">
				ACTIONGROUP_ID = #{actiongroupId,jdbcType=INTEGER},
			</if>
			<if test="controllerName != null">
				CONTROLLER_NAME = #{controllerName,jdbcType=VARCHAR},
			</if>
			<if test="actionName != null">
				ACTION_NAME = #{actionName,jdbcType=VARCHAR},
			</if>
			<if test="moduleName != null">
				MODULE_NAME = #{moduleName,jdbcType=VARCHAR},
			</if>
			<if test="actionDesc != null">
				ACTION_DESC = #{actionDesc,jdbcType=VARCHAR},
			</if>
			<if test="isMenu != null">
				IS_MENU = #{isMenu,jdbcType=BIT},
			</if>
			<if test="sort != null">
				SORT = #{sort,jdbcType=INTEGER},
			</if>
			<if test="addTime != null">
				ADD_TIME = #{addTime,jdbcType=BIGINT},
			</if>
			<if test="creator != null">
				CREATOR = #{creator,jdbcType=INTEGER},
			</if>
			<if test="modTime != null">
				MOD_TIME = #{modTime,jdbcType=BIGINT},
			</if>
			<if test="updater != null">
				UPDATER = #{updater,jdbcType=INTEGER},
			</if>
			<if test="platformId != null">
				PLATFORM_ID = #{platformId,jdbcType=INTEGER},
			</if>
		</set>
		where ACTION_ID = #{actionId,jdbcType=INTEGER}
	</update>

	<select id="selectActionList" resultMap="BaseResultMap" parameterType="com.vedeng.authorization.model.Action">
		select
		<include refid="Base_Column_List" />
		from T_ACTION a
		<where>
			<if test="actiongroupId!=null and actiongroupId!=''">
				a.ACTIONGROUP_ID = #{actiongroupId}
			</if>
		</where>
	</select>
	
		
	<!-- 查询菜单信息-->	
	<select id="getMenuListByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT DISTINCT A.ACTION_ID,A.ACTIONGROUP_ID,A.CONTROLLER_NAME,A.ACTION_NAME,A.MODULE_NAME,A.ACTION_DESC,A.IS_MENU,A.SORT,A.PLATFORM_ID FROM T_ACTION A
			LEFT JOIN T_R_ROLE_ACTION RA ON A.ACTION_ID = RA.ACTION_ID
			LEFT JOIN T_ROLE R ON R.ROLE_ID = RA.ROLE_ID
			LEFT JOIN T_R_USER_ROLE UR ON UR.ROLE_ID = R.ROLE_ID
			LEFT JOIN T_USER U ON U.USER_ID = UR.USER_ID
			LEFT JOIN T_ACTIONGROUP AG ON AG.ACTIONGROUP_ID = A.ACTIONGROUP_ID
			WHERE A.IS_MENU =1 
			<if test ="userId != null">
				and U.USER_ID = #{userId,jdbcType=INTEGER}
			</if>
			AND A.PLATFORM_ID = #{currentLoginSystem,jdbcType=INTEGER}
			AND RA.PLATFORM_ID = #{currentLoginSystem,jdbcType=INTEGER}
			AND AG.PLATFORM_ID = #{currentLoginSystem,jdbcType=INTEGER}
			ORDER BY A.SORT DESC
	</select>
	
	<!-- 获取admin所有菜单信息-->	
	<select id="getAdminMenuList" resultMap="BaseResultMap">
		SELECT 
		<include refid="Base_Column_List" />
		FROM T_ACTION A
		WHERE A.IS_MENU =1
		ORDER BY A.SORT DESC
	</select>
	
	<!-- 查询菜单权限信息-->	
	<select id="getMenuPowerListByUserId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
		SELECT DISTINCT A.ACTION_ID,A.ACTIONGROUP_ID,A.CONTROLLER_NAME,A.ACTION_NAME,A.MODULE_NAME,A.ACTION_DESC,A.IS_MENU,A.SORT,A.PLATFORM_ID FROM T_ACTION A
			LEFT JOIN T_R_ROLE_ACTION RA ON A.ACTION_ID = RA.ACTION_ID
			LEFT JOIN T_ROLE R ON R.ROLE_ID = RA.ROLE_ID
			LEFT JOIN T_R_USER_ROLE UR ON UR.ROLE_ID = R.ROLE_ID
			LEFT JOIN T_USER U ON U.USER_ID = UR.USER_ID
			LEFT JOIN T_ACTIONGROUP AG ON AG.ACTIONGROUP_ID = A.ACTIONGROUP_ID
			WHERE A.IS_MENU =0 
			<if test ="userId != null">
				and U.USER_ID = #{userId,jdbcType=INTEGER}
			</if>
			ORDER BY A.SORT DESC
	</select>
	
		<!-- 查询菜单权限信息-->	
	<select id="getMenuPowerListByUserName" parameterType="java.lang.String" resultMap="BaseResultMap">
		SELECT DISTINCT A.ACTION_ID,A.ACTIONGROUP_ID,A.CONTROLLER_NAME,A.ACTION_NAME,A.MODULE_NAME,A.ACTION_DESC,A.IS_MENU,A.SORT,A.PLATFORM_ID FROM T_ACTION A
			LEFT JOIN T_R_ROLE_ACTION RA ON A.ACTION_ID = RA.ACTION_ID
			LEFT JOIN T_ROLE R ON R.ROLE_ID = RA.ROLE_ID
			LEFT JOIN T_R_USER_ROLE UR ON UR.ROLE_ID = R.ROLE_ID
			LEFT JOIN T_USER U ON U.USER_ID = UR.USER_ID
			LEFT JOIN T_ACTIONGROUP AG ON AG.ACTIONGROUP_ID = A.ACTIONGROUP_ID
			WHERE 1=1
			<if test ="username != null and username!=''">
				and U.USERNAME = #{username,jdbcType=VARCHAR}
			</if>
			<if test="companyId!=null and companyId!=0">
				AND U.COMPANY_ID = #{companyId,jdbcType=INTEGER}
			</if>
			AND A.PLATFORM_ID = #{currentLoginSystem,jdbcType=INTEGER}
			AND RA.PLATFORM_ID = #{currentLoginSystem,jdbcType=INTEGER}
			AND AG.PLATFORM_ID = #{currentLoginSystem,jdbcType=INTEGER}
			ORDER BY A.SORT DESC
	</select>
	
	<!-- 获取admin所有菜单权限信息-->	
	<select id="getAdminMenuPowerList" resultMap="BaseResultMap">
		SELECT 
		<include refid="Base_Column_List" />
		FROM T_ACTION A
		ORDER BY A.SORT DESC
	</select>
	
	<!-- -->	
	<select id="getActionId" resultType="java.lang.Integer" parameterType="com.vedeng.authorization.model.Action">
		SELECT 
		ACTION_ID
		FROM T_ACTION 
		WHERE 1=1
		<if test="controllerName != null">
			and CONTROLLER_NAME = #{controllerName,jdbcType=VARCHAR}
		</if>
		<if test="actionName != null">
			and ACTION_NAME = #{actionName,jdbcType=VARCHAR}
		</if>
		<if test="moduleName != null">
			and MODULE_NAME = #{moduleName,jdbcType=VARCHAR}
		</if>
		limit 1
	</select>

	<select id="getActionListByPlatformId" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM T_ACTION
		<if test="platformId != null">
			WHERE PLATFORM_ID = #{platformId,jdbcType=INTEGER}
		</if>
	</select>
    <select id="getByActionDesc" parameterType="string" resultMap="BaseResultMap">
		SELECT
		<include refid="Base_Column_List" />
		FROM T_ACTION
		WHERE ACTION_DESC = #{actionDesc, jdbcType=VARCHAR}
		LIMIT 1
	</select>
	<select id="getTask" resultType="com.vedeng.authorization.model.Action">
		SELECT
			a.*
		FROM
			T_ACTION a
		WHERE
			1 = 1
			AND a.ACTION_NAME = #{actionName, jdbcType=VARCHAR}
		<if test="moduleName != null">
			AND a.MODULE_NAME = #{moduleName, jdbcType=VARCHAR}
		</if>
		<if test="controllerName != null">
			AND a.CONTROLLER_NAME = #{controllerName, jdbcType=VARCHAR}
		</if>
	</select>
</mapper>