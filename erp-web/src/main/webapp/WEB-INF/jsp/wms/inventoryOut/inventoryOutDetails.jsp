<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="盘亏出库单详情" scope="application" />
<%@ taglib uri="/WEB-INF/tld/myfn.tld" prefix="myfn" %>
<%@ include file="../../common/common.jsp"%>
<script>
    function closeSurplus() {
        checkLogin();
        index = layer.confirm("您是否确认关闭该盘亏出库单？", {
            btn: ['确定','取消'] //按钮
        }, function(){

            $.ajax({
                url:page_url+'/wms/inventoryOut/closeInventoryOutOrder.do',
                data:{"inventoryOutOrderId":"${wmsOutputOrder.id}"},
                type:"POST",
                dataType : "json",
                async: false,
                success:function(data)
                {
                    if(data.code==0){
                        window.location.reload();
                    }else{
                        layer.alert(data.message);
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });

            layer.close(index);

        }, function(){
        });
    }
</script>
<div class="content">
    <div class="formtitle"></div>
    <div  id="desc_div">
            <ul class="payplan">

                <div class="parts">

                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">
                            基本信息
                        </div>
                    </div>

                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                        <tr>
                            <td class="table-smaller">出库单号</td>
                            <td>${wmsOutputOrder.orderNo}</td>
                            <td class="table-smaller">出库状态</td>
                            <td>
                                <c:choose>
                                    <c:when test="${wmsOutputOrder.outStatus eq 0}">
                                        未出库
                                    </c:when>
                                    <c:when test="${wmsOutputOrder.outStatus eq 1}">
                                        部分出库
                                    </c:when>
                                    <c:when test="${wmsOutputOrder.outStatus eq 2}">
                                        全部出库
                                    </c:when>
                                </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>创建时间</td>
                            <td>${wmsOutputOrder.addTime}</td>
                            <td>申请人</td>
                            <td>${wmsOutputOrder.applyer}</td>
                        </tr>
                        <tr>
                            <td>申请部门</td>
                            <td>${wmsOutputOrder.applyerDepartment}</td>
                            <td>审核状态</td>
                            <td><c:choose>
                                <c:when test="${wmsOutputOrder.verifyStatus eq 0}">
                                    未审核
                                </c:when>
                                <c:when test="${wmsOutputOrder.verifyStatus eq 1}">
                                    审核中
                                </c:when>
                                <c:when test="${wmsOutputOrder.verifyStatus eq 2}">
                                    审核通过
                                </c:when>
                                <c:when test="${wmsOutputOrder.verifyStatus eq 3}">
                                    审核不通过
                                </c:when>
                                <c:when test="${wmsOutputOrder.verifyStatus eq 4}">
                                    已关闭
                                </c:when>
                            </c:choose>
                            </td>
                        </tr>
                        <tr>
                            <td>备注</td>
                            <td>${wmsOutputOrder.remark}</td>
                            <td></td>
                            <td></td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">产品信息</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered" >
                            <thead>
                            <tr>
                                <th style="width:80px">序号</th>
                                <th style="width:80px">订货号</th>
                                <th style="width:80px">产品名称</th>
                                <th style="width:80px">品牌</th>
                                <th style="width:80px">型号</th>
                                <th style="width:80px">需要出库数量</th>
                                <th style="width:80px">逻辑仓</th>
                                <th style="width:80px">已出库数量</th>
                            </tr>
                            </thead>
                            <tbody id="thisTimeUpdateTbody">
                            <c:forEach var="ordergoods" items="${wmsOutputOrderGoodsList}" varStatus="staut">
                                <tr>
                                    <td>${staut.count}</td>
                                    <td>${ordergoods.skuNo}</td>
                                    <td>${ordergoods.skuName}</td>
                                    <td>${ordergoods.brandName}</td>
                                    <td>${ordergoods.model}</td>
                                    <td>${ordergoods.outputNum}</td>
                                    <td>${ordergoods.logicalName}</td>
                                    <td>${ordergoods.alreadyOutputNum}</td>
                                </tr>
                            </c:forEach>
                            </tbody>
                        </table>
                    </div>
                </li>
                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor">出库记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
<%--                                <th style="width:80px">出库单据</th>--%>
                                <th class="wid5">序号</th>
                                <th class="wid10">订货号</th>
                                <th>产品名称</th>
                                <th>品牌</th>
                                <th>型号</th>
                                <th>实际出库数量</th>
                                <th class="wid4">单位</th>
                                <th>贝登批次码</th>
                                <th>生产日期</th>
                                <th>有效期至</th>
                                <th>出库时间</th>
                                <th>入库日期</th>
                                <th>生产批号</th>
                                <th>SN码</th>
                                <th>灭菌编号</th>
                                <th class="wid12">注册证号</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="listout" items="${warehouseOutList}"
                                       varStatus="num3">
                                <tr>
<%--                                    <td>--%>
<%--                                    <a class="addtitle"--%>
<%--                                       href="javascript:void(0);"--%>
<%--                                       tabTitle='{ "link":"./warehouse/warehousesout/detail.do?outInNo=${listout.outInNo}&outInType=${listout.operateType}","title":"出库单详情页"}'>${listout.outInNo}</a>--%>
<%--                                    </td>--%>
                                    <td>${num3.count}</td>
                                    <td>${listout.sku}</td>
                                    <td class="text-left">
                                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${listout.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${listout.goodsId}","title":"产品信息"}'>${newSkuInfosMap[listout.sku].SHOW_NAME}</a>
                                    </td>
                                    <td>${newSkuInfosMap[listout.sku].BRAND_NAME}</td>
                                    <td>${newSkuInfosMap[listout.sku].MODEL}</td>
                                        <%--<td>${newSkuInfosMap[listout.sku].MATERIAL_CODE}</td>--%>
                                    <td>${ listout.realGoodsNum}</td>
                                    <td>${newSkuInfosMap[listout.sku].UNIT_NAME}</td>
                                    <td>${ listout.vedengBatchNumer}</td>
                                    <td><date:date value ="${listout.productDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td><date:date value ="${listout.expirationDate}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td><date:date value ="${listout.addTime}" format="yyyy-MM-dd HH:mm:ss"/></td>
                                    <td><date:date value ="${listout.checkStatusTime}" format="yyyy-MM-dd HH:mm:ss"/></td>

                                    <td>${ listout.batchNumber}</td>
                                    <td>${ listout.barcodeFactory}</td>
                                        <%--<td>${ listout.barcode}</td>
                                        <td>${ listout.barcodeFactory}</td>

                                        <td>${0-listout.num}</td>
                                        <td><date:date value ="${listout.addTime}" format="yyyy-MM-dd HH:mm:ss"/></td>--%>

                                    <td>${ listout.sterilizationBatchNo}</td>


                                    <td class="text-left">
                                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewfirstgoods${listout.fitstEngageId}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${listout.fitstEngageId}","title":"首营信息"}'>${ listout.registrationNo}</a>
                                    </td>

                                </tr>
                            </c:forEach>
                            <c:if test="${empty warehouseOutList }">
                                <tr>
                                    <td colspan="16">暂无出库记录</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>

                </li>

                <li>
                    <div class="parts">
                        <div class="title-container title-container-blue">
                            <div class="table-title nobor ">审核记录</div>
                        </div>
                        <table class="table  table-bordered table-striped table-condensed table-centered">
                            <thead>
                            <tr>
                                <th style="width:50px">操作人</th>
                                <th style="width:80px">操作时间</th>
                                <th style="width:80px">操作事项</th>
                                <th style="width:80px">备注</th>
                            </tr>
                            </thead>
                            <tbody>
                            <c:forEach var="hi" items="${historicActivityInstance}" varStatus="status">
                                <c:if test="${not empty  hi.activityName}">
                                    <tr>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    ${startUser}
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                </c:when>
                                                <c:otherwise>
                                                    <c:if test="${historicActivityInstance.size() == status.count}">
                                                        <c:forEach var="vs" items="${verifyUsersList}" varStatus="status">
                                                            <c:if test="${fn:contains(verifyUserList, vs)}">
                                                                <span class="font-green">${vs}</span>&nbsp;
                                                            </c:if>
                                                            <c:if test="${!fn:contains(verifyUserList, vs)}">
                                                                <span>${vs}</span>&nbsp;
                                                            </c:if>
                                                        </c:forEach>

                                                        <c:if test="${empty verifyUsersList && empty hi.assignee}">
                                                            ${verifyUsers}
                                                        </c:if>
                                                    </c:if>
                                                    <c:if test="${historicActivityInstance.size() != status.count}">
                                                        ${hi.assignee}
                                                    </c:if>
                                                </c:otherwise>
                                            </c:choose>


                                        </td>
                                        <td><fmt:formatDate value="${hi.endTime}" pattern="yyyy-MM-dd HH:mm:ss" /></td>
                                        <td>
                                            <c:choose>
                                                <c:when test="${hi.activityType == 'startEvent'}">
                                                    开始
                                                </c:when>
                                                <c:when test="${hi.activityType == 'intermediateThrowEvent'}">
                                                    结束
                                                </c:when>
                                                <c:otherwise>
                                                    ${hi.activityName}
                                                </c:otherwise>
                                            </c:choose>
                                        </td>
                                        <td class="font-red">${commentMap[hi.taskId]}</td>
                                    </tr>
                                </c:if>
                            </c:forEach>
                            <!-- 查询无结果弹出 -->

                            <c:if test="${empty historicActivityInstance}">
                                <!-- 查询无结果弹出 -->
                                <tr>
                                    <td colspan="4">暂无审核记录。</td>
                                </tr>
                            </c:if>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter mt10">

                <c:if test="${null!=historicInfo.taskInfo and (taskInfo.assignee == curr_user.username or historicInfo.candidateUserMap['belong']) }">
                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&wmsOutputOrderId=${wmsOutputOrder.id}&pass=true"}'>审核通过</button>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./auditResult.do?taskId=${taskId}&wmsOutputOrderId=${wmsInputOrder.id}&pass=false"}'>审核不通过</button>
                </c:if>

                <c:if test="${cancelflag}">
                    <%--<button type="button" class="bt-bg-style bg-deep-red" onclick="closeSurplus();">关闭订单</button>--%>
                </c:if>

            </div>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>