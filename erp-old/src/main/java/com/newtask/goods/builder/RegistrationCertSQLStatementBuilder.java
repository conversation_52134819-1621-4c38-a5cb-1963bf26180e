package com.newtask.goods.builder;

import com.newtask.goods.DBTableType;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;
import java.util.StringJoiner;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class RegistrationCertSQLStatementBuilder extends AbstractSqlBuilder {

    private Integer medicalInclude;
    private String model;
    private String productChineseName;
    private Integer spuId;

    protected RegistrationCertSQLStatementBuilder self() {
        return this;
    }

    public Integer getMedicalInclude() {
        return medicalInclude;
    }

    public RegistrationCertSQLStatementBuilder setMedicalInclude(Integer medicalInclude) {
        this.medicalInclude = medicalInclude;
        return self();
    }

    public String getModel() {
        return model;
    }

    public RegistrationCertSQLStatementBuilder setModel(String model) {
        this.model = model;
        return self();
    }

    public String getProductChineseName() {
        return productChineseName;
    }

    public RegistrationCertSQLStatementBuilder setProductChineseName(String productChineseName) {
        this.productChineseName = productChineseName;
        return self();
    }

    public Integer getSpuId() {
        return spuId;
    }

    public RegistrationCertSQLStatementBuilder setSpuId(Integer skuId) {
        this.spuId = skuId;
        return self();
    }

    @Override
    public String build() {
        Objects.requireNonNull(getSqlCommand(), "command is required.");
        Objects.requireNonNull(getDbTable(), "target table is required.");
        Objects.requireNonNull(getSpuId(), "spuID is null.");

        String name = getSqlCommand().name();

        DBTableType table = getDbTable();
        String sqlTemplate = name + " " + table.getTableName() + "\n" + "SET %s WHERE " + table.getPrimaryKeyName() + "=" + getSpuId();


        StringJoiner stringJoiner = new StringJoiner(",");
        if (getMedicalInclude() != null) {
            stringJoiner.add(" MEDICAL_INSTRUMENT_CATALOG_INCLUDED=" + getMedicalInclude());
        }
        if (StringUtils.isNotEmpty(getModel())) {
            String model = getModel();

            stringJoiner.add(String.format(" SPECS_MODEL=\'%s\'", model.length() <= 512 ? model : model.substring(0, 512 - 1)));
        }
        if (StringUtils.isNotEmpty(getProductChineseName())) {
            stringJoiner.add(String.format(" SPU_NAME=\'%s\'", getProductChineseName()));
        }

        if (stringJoiner.length() == 0) {
            throw new IllegalArgumentException("body is empty.");
        }

        return String.format(sqlTemplate, stringJoiner.toString());
    }
}
