package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.erp.saleorder.service.SaleOrderAutoCloseService;
import com.vedeng.erp.system.service.FlowOrderApiService;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.cp.bean.message.WxCpMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 销售订单自动关闭服务实现
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Service
@Slf4j
public class SaleOrderAutoCloseServiceImpl implements SaleOrderAutoCloseService {

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private UacWxUserInfoApiService uacWxUserInfoApiService;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    FlowOrderApiService flowOrderApiService;

    @Value("${erpUrl}")
    private String erpUrl;

    // 60天的毫秒数
    private static final long SIXTY_DAYS_MILLIS = 60L * 24 * 60 * 60 * 1000;
    // 24小时的毫秒数
    private static final long TWENTY_FOUR_HOURS_MILLIS = 24L * 60 * 60 * 1000;

    // 预警去重相关常量
    private static final String WARNING_CACHE_PREFIX = "SALE_ORDER_WARNING:";
    private static final long TWENTY_FOUR_HOURS_SECONDS = 24 * 60 * 60;

    @Override
    public int processAutoCloseOrders() {
        log.info("开始处理销售订单自动关闭");

        try {
            // 查询需要自动关闭的订单
            List<Saleorder> ordersToClose = getOrdersToAutoClose();
            log.info("查询到需要自动关闭的订单数量: {}", ordersToClose.size());

            return processOrderCloseInternal(ordersToClose, "自动关闭");
        } catch (Exception e) {
            log.error("处理销售订单自动关闭失败", e);
            throw e;
        }
    }

    @Override
    public int processWarningNotifications() {
        log.info("开始处理24小时预警通知");

        try {
            // 查询需要发送预警的订单
            List<Saleorder> ordersToWarn = getOrdersToWarn();
            log.info("查询到需要发送预警的订单数量: {}", ordersToWarn.size());

            return processWarningNotificationsInternal(ordersToWarn, "定时任务24小时预警");
        } catch (Exception e) {
            log.error("处理24小时预警通知失败", e);
            return 0;
        }
    }

    @Override
    public int processWarningNotifications(List<String> orderNoList) {
        log.info("开始根据订单号列表处理消息提醒功能，订单号数量: {}", orderNoList.size());

        if (CollectionUtils.isEmpty(orderNoList)) {
            log.warn("订单号列表为空，跳过消息提醒处理");
            return 0;
        }

        try {
            // 查询指定订单号的订单，并验证是否符合预警条件
            List<Saleorder> ordersToWarn = getOrdersByOrderNoListForWarning(orderNoList);
            log.info("查询到符合预警条件的订单数量: {}/{}", ordersToWarn.size(), orderNoList.size());

            return processWarningNotificationsInternal(ordersToWarn, "指定订单号预警");
        } catch (Exception e) {
            log.error("根据订单号列表处理消息提醒功能失败", e);
            return 0;
        }
    }

    @Override
    public int processHistoricalData() {
        log.info("开始处理历史数据批量关闭");

        try {
            // 查询所有需要处理的历史订单
            List<Saleorder> historicalOrders = getHistoricalOrdersToProcess();
            log.info("查询到需要处理的历史订单数量: {}", historicalOrders.size());

            // 过滤出真正需要关闭的历史订单（超过60天）
            long currentTime = System.currentTimeMillis();
            List<Saleorder> ordersToClose = historicalOrders.stream()
                    .filter(order -> {
                        long elapsedTime = currentTime - order.getValidTime();
                        return elapsedTime > SIXTY_DAYS_MILLIS;
                    })
                    .collect(Collectors.toList());

            log.info("过滤后需要关闭的历史订单数量: {}", ordersToClose.size());

            return processOrderCloseInternal(ordersToClose, "历史数据关闭");
        } catch (Exception e) {
            log.error("处理历史数据失败", e);
            throw e;
        }
    }

    @Override
    public String processOrdersByOrderNoList(List<String> orderNoList) {
        log.info("开始根据订单号列表批量处理订单关闭，订单号数量: {}", orderNoList.size());

        try {
            // 查询指定订单号的订单，并验证是否符合基本关闭条件
            List<Saleorder> ordersFound = saleorderMapper.getOrdersByOrderNoList(orderNoList);
            log.info("查询到符合基本条件的订单数量: {}/{}", ordersFound.size(), orderNoList.size());

            // 进一步验证60天时间条件
            long currentTime = System.currentTimeMillis();
            List<Saleorder> ordersToClose = new ArrayList<>();
            List<String> notFoundOrders = new ArrayList<>();
            List<String> notOverdueOrders = new ArrayList<>();

            // 统计未找到的订单
            Set<String> foundOrderNos = ordersFound.stream()
                    .map(Saleorder::getSaleorderNo)
                    .collect(Collectors.toSet());
            for (String orderNo : orderNoList) {
                if (!foundOrderNos.contains(orderNo)) {
                    notFoundOrders.add(orderNo);
                }
            }

            // 验证每个找到的订单是否满足60天条件
            for (Saleorder order : ordersFound) {
                long elapsedTime = currentTime - order.getValidTime();

                if (elapsedTime > SIXTY_DAYS_MILLIS) {
                    // 满足60天条件，可以关闭
                    ordersToClose.add(order);
                    log.info("订单满足关闭条件，订单号: {}, 审核时间: {}, 已过天数: {}",
                            order.getSaleorderNo(),
                            new java.util.Date(order.getValidTime()),
                            elapsedTime / (24 * 60 * 60 * 1000));
                } else {
                    // 不满足60天条件
                    notOverdueOrders.add(order.getSaleorderNo());
                    long remainingDays = (SIXTY_DAYS_MILLIS - elapsedTime) / (24 * 60 * 60 * 1000);
                    log.info("订单不满足60天关闭条件，订单号: {}, 审核时间: {}, 剩余天数: {}",
                            order.getSaleorderNo(),
                            new java.util.Date(order.getValidTime()),
                            remainingDays);
                }
            }

            log.info("订单分类统计 - 可关闭: {}, 未找到或不符合基本条件: {}, 不满足60天条件: {}",
                    ordersToClose.size(), notFoundOrders.size(), notOverdueOrders.size());

            // 使用批处理机制关闭符合条件的订单
            int successCount = 0;
            int failedCount = 0;
            if (!ordersToClose.isEmpty()) {
                successCount = processOrderCloseInternal(ordersToClose, "指定订单号关闭");
                failedCount = ordersToClose.size() - successCount;
            }

            // 构建详细的处理结果
            StringBuilder result = new StringBuilder();
            result.append(String.format("订单号批量处理完成 - 总计: %d, 成功关闭: %d, 关闭失败: %d, 未找到或不符合基本条件: %d, 不满足60天条件: %d",
                    orderNoList.size(), successCount, failedCount, notFoundOrders.size(), notOverdueOrders.size()));

            if (!notFoundOrders.isEmpty()) {
                result.append(String.format(" | 未找到或不符合基本条件的订单号: %s", String.join(",", notFoundOrders)));
            }

            if (!notOverdueOrders.isEmpty()) {
                result.append(String.format(" | 不满足60天条件的订单号: %s", String.join(",", notOverdueOrders)));
            }

            String resultStr = result.toString();
            log.info("根据订单号列表批量处理完成: {}", resultStr);
            return resultStr;

        } catch (Exception e) {
            log.error("根据订单号列表批量处理订单关闭失败", e);
            throw e;
        }
    }

    /**
     * 查询需要自动关闭的订单
     * 条件：审核通过且未付款且未开票且进行中，且审核时间超过60天
     * 排除已有业务往来（收款或开票）的订单
     */
    private List<Saleorder> getOrdersToAutoClose() {
        Map<String, Object> params = new HashMap<>();
        params.put("validStatus", 1);      // 已生效
        params.put("paymentStatus", 0);    // 未付款（排除部分收款=1和全部收款=2）
        params.put("invoiceStatus", 0);    // 未开票（排除部分开票=1和全部开票=2）
        params.put("status", 1);           // 进行中

        long currentTime = System.currentTimeMillis();
        long cutoffTime = currentTime - SIXTY_DAYS_MILLIS;
        params.put("validTimeBefore", cutoffTime);

        return saleorderMapper.getOrdersForAutoClose(params);
    }

    /**
     * 查询需要发送预警的订单
     * 条件：审核通过且未付款且未开票且进行中，且剩余时间小于24小时
     * 排除已有业务往来（收款或开票）的订单
     */
    private List<Saleorder> getOrdersToWarn() {
        Map<String, Object> params = new HashMap<>();
        params.put("validStatus", 1);      // 已生效
        params.put("paymentStatus", 0);    // 未付款（排除部分收款=1和全部收款=2）
        params.put("invoiceStatus", 0);    // 未开票（排除部分开票=1和全部开票=2）
        params.put("status", 1);           // 进行中

        long currentTime = System.currentTimeMillis();
        long warnCutoffTime = currentTime - (SIXTY_DAYS_MILLIS - TWENTY_FOUR_HOURS_MILLIS);
        params.put("validTimeBefore", warnCutoffTime);
        params.put("validTimeAfter", currentTime - SIXTY_DAYS_MILLIS);

        return saleorderMapper.getOrdersForWarning(params);
    }

    /**
     * 查询需要处理的历史订单
     * 条件：审核通过且未付款且未开票且进行中
     * 排除已有业务往来（收款或开票）的订单
     */
    private List<Saleorder> getHistoricalOrdersToProcess() {
        Map<String, Object> params = new HashMap<>();
        params.put("validStatus", 1);      // 已生效
        params.put("paymentStatus", 0);    // 未付款（排除部分收款=1和全部收款=2）
        params.put("invoiceStatus", 0);    // 未开票（排除部分开票=1和全部开票=2）
        params.put("status", 1);           // 进行中

        return saleorderMapper.getHistoricalOrdersToProcess(params);
    }

    /**
     * 根据订单号列表查询符合预警条件的订单
     * 条件：审核通过且未付款且未开票且进行中
     * 排除已有业务往来（收款或开票）的订单
     */
    private List<Saleorder> getOrdersByOrderNoListForWarning(List<String> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return new ArrayList<>();
        }

        Map<String, Object> params = new HashMap<>();
        params.put("orderNoList", orderNoList);
        params.put("validStatus", 1);      // 已生效
        params.put("paymentStatus", 0);    // 未付款（排除部分收款=1和全部收款=2）
        params.put("invoiceStatus", 0);    // 未开票（排除部分开票=1和全部开票=2）
        params.put("status", 1);           // 进行中

        return saleorderMapper.getOrdersByOrderNoListForWarning(params);
    }

    /**
     * 内部统一的订单关闭处理方法
     * 支持批量处理，优化性能，保证事务一致性
     * 注意：每个订单的关闭操作都在独立的事务中执行，确保单个失败不影响其他订单
     */
    private int processOrderCloseInternal(List<Saleorder> ordersToClose, String processType) {
        if (CollectionUtils.isEmpty(ordersToClose)) {
            log.info("{}：没有需要关闭的订单", processType);
            return 0;
        }

        log.info("{}：开始处理订单关闭，订单数量: {}", processType, ordersToClose.size());

        int closedCount = 0;
        int batchSize = 20; // 订单关闭批次大小，比通知处理更小，因为涉及数据库事务
        List<String> failedOrders = new ArrayList<>();

        // 分批处理，避免长时间占用数据库连接和事务
        for (int i = 0; i < ordersToClose.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, ordersToClose.size());
            List<Saleorder> batchOrders = ordersToClose.subList(i, endIndex);

            log.info("{}：处理第{}批订单，数量: {}", processType, (i / batchSize + 1), batchOrders.size());

            for (Saleorder order : batchOrders) {
                try {
                    // 每个订单在独立的事务中关闭，确保事务一致性
                    closeOrderWithTransaction(order);
                    closedCount++;
                    log.info("{}：关闭订单成功，订单号: {}, 订单ID: {}",
                            processType, order.getSaleorderNo(), order.getSaleorderId());
                } catch (Exception e) {
                    failedOrders.add(order.getSaleorderNo());
                    log.error("{}：关闭订单失败，订单号: {}, 订单ID: {}",
                            processType, order.getSaleorderNo(), order.getSaleorderId(), e);
                }
            }

            // 批次间休息，避免对数据库造成过大压力
            if (endIndex < ordersToClose.size()) {
                try {
                    Thread.sleep(200); // 订单关闭休息时间更长，200毫秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("{}：批次处理被中断", processType);
                    break;
                }
            }
        }

        // 记录处理结果
        if (!failedOrders.isEmpty()) {
            log.warn("{}：部分订单关闭失败，失败订单号: {}", processType, String.join(",", failedOrders));
        }

        log.info("{}：订单关闭处理完成，成功关闭订单数量: {}/{}", processType, closedCount, ordersToClose.size());
        return closedCount;
    }

    /**
     * 内部统一的预警通知处理方法
     * 支持批量处理，优化性能，增强异常处理
     */
    private int processWarningNotificationsInternal(List<Saleorder> ordersToWarn, String processType) {
        if (CollectionUtils.isEmpty(ordersToWarn)) {
            log.info("{}：没有需要发送预警的订单", processType);
            return 0;
        }

        log.info("{}：开始处理预警通知，订单数量: {}", processType, ordersToWarn.size());

        int warningCount = 0;
        int batchSize = 50; // 批量处理大小，避免性能问题
        List<String> failedOrders = new ArrayList<>();

        // 分批处理，避免一次性处理过多订单造成性能问题
        for (int i = 0; i < ordersToWarn.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, ordersToWarn.size());
            List<Saleorder> batchOrders = ordersToWarn.subList(i, endIndex);

            log.info("{}：处理第{}批订单，数量: {}", processType, (i / batchSize + 1), batchOrders.size());

            for (Saleorder order : batchOrders) {
                try {
                    // 再次验证订单状态，确保数据一致性
                    if (isOrderEligibleForWarning(order)) {
                        // 检查24小时内是否已发送过预警
                        if (hasWarningBeenSentWithin24Hours(order.getSaleorderNo())) {
                            log.info("{}：订单{}在24小时内已发送过预警，跳过本次发送",
                                    processType, order.getSaleorderNo());
                            continue;
                        }

                        // 发送企业微信预警通知
                        sendWeChatWarningNotification(order, processType);

                        // 记录预警发送
                        recordWarningSent(order.getSaleorderNo(), processType);

                        warningCount++;
                        log.info("{}：发送预警通知成功，订单号: {}, 订单ID: {}",
                                processType, order.getSaleorderNo(), order.getSaleorderId());
                    } else {
                        log.info("{}：订单状态已变更，跳过预警，订单号: {}", processType, order.getSaleorderNo());
                    }
                } catch (Exception e) {
                    failedOrders.add(order.getSaleorderNo());
                    log.error("{}：发送预警通知失败，订单号: {}, 订单ID: {}",
                            processType, order.getSaleorderNo(), order.getSaleorderId(), e);
                }
            }

            // 批次间短暂休息，避免对系统造成过大压力
            if (endIndex < ordersToWarn.size()) {
                try {
                    Thread.sleep(100); // 休息100毫秒
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.warn("{}：批次处理被中断", processType);
                    break;
                }
            }
        }

        // 记录处理结果
        if (!failedOrders.isEmpty()) {
            log.warn("{}：部分订单预警通知发送失败，失败订单号: {}", processType, String.join(",", failedOrders));
        }

        log.info("{}：预警通知处理完成，成功发送通知数量: {}/{}", processType, warningCount, ordersToWarn.size());
        return warningCount;
    }


    /**
     * 验证订单是否符合预警条件
     * 确保订单状态的实时性和准确性
     */
    private boolean isOrderEligibleForWarning(Saleorder order) {
        if (order == null) {
            return false;
        }

        // 检查订单基本状态
        if (order.getValidStatus() != 1 || // 必须已生效
                order.getPaymentStatus() != 0 || // 必须未付款
                order.getInvoiceStatus() != 0 || // 必须未开票
                order.getStatus() != 1) { // 必须进行中
            log.debug("订单不符合预警条件，订单号: {}, validStatus: {}, paymentStatus: {}, invoiceStatus: {}, status: {}",
                    order.getSaleorderNo(), order.getValidStatus(), order.getPaymentStatus(),
                    order.getInvoiceStatus(), order.getStatus());
            return false;
        }

        // 检查销售人员信息
        if (order.getNumber() == null || order.getNumber().trim().isEmpty()) {
            log.warn("订单缺少销售人员信息，跳过预警，订单号: {}", order.getSaleorderNo());
            return false;
        }

        return true;
    }


    /**
     * 发送企业微信预警通知
     */
    private void sendWeChatWarningNotification(Saleorder order, String processType) throws UnsupportedEncodingException {
        try {
            // 构建消息内容
            String customerName = order.getTraderName();
            String orderNo = order.getSaleorderNo();

            // 计算剩余时间
            long currentTime = System.currentTimeMillis();
            long validTime = order.getValidTime();
            long elapsedTime = currentTime - validTime;
            long remainingTime = SIXTY_DAYS_MILLIS - elapsedTime;

            String messageContent;
            if (remainingTime <= 0) {
                messageContent = String.format("%s的%s订单已超过60天未结款，即将自动关闭。", customerName, orderNo);
            } else if (remainingTime <= TWENTY_FOUR_HOURS_MILLIS) {
                messageContent = String.format("%s的%s订单60天未结款将自动关闭，还剩最后24小时。", customerName, orderNo);
            } else {
                long remainingDays = remainingTime / (24 * 60 * 60 * 1000);
                long remainingHours = (remainingTime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
                messageContent = String.format("%s的%s订单还有%d天%d小时将自动关闭，请及时处理。",
                        customerName, orderNo, remainingDays, remainingHours);
            }

            // 构建跳转URL
            String detailUrl = erpUrl + "/index.do?target=" + URLEncoder.encode(String.format("/order/saleorder/view.do?saleorderId=%d", order.getSaleorderId()), "UTF-8");

            // 发送企业微信消息
            WxCpMessage message = new WxCpMessage();
            message.setToUser(order.getNumber()); // 销售人员工号
            message.setMsgType("textcard");
            message.setTitle("销售订单即将自动关闭提醒");
            message.setDescription(messageContent);
            message.setUrl(detailUrl);
            message.setBtnTxt("查看详情");

            uacWxUserInfoApiService.sendToUser(message);

            log.info("企业微信预警通知发送成功，订单号: {}, 销售人员: {}", orderNo, order.getNumber());
        } catch (Exception e) {
            log.error("发送企业微信预警通知失败，订单号: {}", order.getSaleorderNo(), e);
            throw e;
        }
    }

    /**
     * 在独立事务中关闭订单
     * 确保每个订单的关闭操作都有独立的事务边界
     */
    @Transactional(rollbackFor = Exception.class)
    public void closeOrderWithTransaction(Saleorder order) {
        closeOrder(order);
    }

    /**
     * 关闭订单的核心逻辑
     */
    private void closeOrder(Saleorder order) {
        Integer auditStatus = flowOrderApiService.queryAuditStatus(order.getSaleorderNo());
        if (Objects.equals(auditStatus, 1)) {
            throw new ServiceException("业务流转单已审核，销售单不可关闭");
        }
        order.setStatus(3);
        saleorderService.closeOrder(order);
    }

    @Override
    public int cleanExpiredWarningRecords() {
        log.info("开始清理过期的预警记录");
        try {
            Set<String> keys = RedisUtil.KeyOps.keys(WARNING_CACHE_PREFIX + "*");
            if (CollectionUtils.isEmpty(keys)) {
                return 0;
            }

            // Redis TTL会自动清理，这里只是统计
            log.info("清理过期预警记录完成，当前记录数量: {}", keys.size());
            return keys.size();
        } catch (Exception e) {
            log.error("清理过期预警记录失败", e);
            return 0;
        }
    }

    /**
     * 检查订单是否在24小时内已发送过预警
     */
    private boolean hasWarningBeenSentWithin24Hours(String orderNo) {
        if (orderNo == null || orderNo.trim().isEmpty()) {
            return false;
        }

        try {
            String key = WARNING_CACHE_PREFIX + orderNo;
            return RedisUtil.KeyOps.hasKey(key);
        } catch (Exception e) {
            log.error("检查订单{}预警去重状态失败", orderNo, e);
            return true; // 异常时返回true，避免重复发送
        }
    }

    /**
     * 记录预警发送
     */
    private void recordWarningSent(String orderNo, String processType) {
        if (orderNo == null || orderNo.trim().isEmpty()) {
            return;
        }

        try {
            String key = WARNING_CACHE_PREFIX + orderNo;
            String value = System.currentTimeMillis() + "|" + processType;
            RedisUtil.StringOps.setEx(key, value, TWENTY_FOUR_HOURS_SECONDS, TimeUnit.SECONDS);
            log.info("记录订单{}预警发送成功，处理类型: {}", orderNo, processType);
        } catch (Exception e) {
            log.error("记录订单{}预警发送失败，处理类型: {}", orderNo, processType, e);
        }
    }
}
