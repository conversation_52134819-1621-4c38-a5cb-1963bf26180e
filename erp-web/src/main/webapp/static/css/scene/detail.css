.detail-prod-tabs {
  padding: 15px 0;
  margin-left: -20px;
  padding-left: 20px;
}
.detail-prod-tabs.fixed {
  position: fixed;
  top: 73px;
  z-index: 100;
  background: #fff;
  width: 1200px;
}
.detail-prod-tabs .tabs-list {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -10px;
}
.detail-prod-tabs .tab-item {
  padding: 5px 16px;
  border-radius: 3px;
  border: solid 1px #BABFC2;
  background: #F5F7FA;
  margin-right: 10px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
}
.detail-prod-tabs .tab-item:hover {
  background: #EBEFF2;
}
.detail-prod-tabs .tab-item.active {
  background: #E0F3FF;
  border-color: #09f;
  color: #09f;
}
.detail-prod-tabs .tab-item.active:hover .tab-option:hover {
  background: #b3e1ff;
  color: #09f;
}
.detail-prod-tabs .tab-item .tab-option {
  width: 33px;
  height: 31px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -5px -16px -5px 0;
  position: relative;
}
.detail-prod-tabs .tab-item .tab-option .icon-app-more {
  transform: rotate(90deg);
}
.detail-prod-tabs .tab-item .tab-option:hover {
  color: #f60;
  background: #e1e5e8;
}
.detail-prod-tabs .tab-item .tab-option:hover .tab-option-drop {
  display: block;
}
.detail-prod-tabs .tab-item .tab-option .tab-option-drop {
  background: #fff !important;
  position: absolute;
  top: 31px;
  left: 0;
  padding: 5px 0;
  border: solid 1px #C9CED1;
  border-radius: 3px;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  cursor: default;
  z-index: 10;
  color: #333;
  display: none;
}
.detail-prod-tabs .tab-item .tab-option .tab-option-drop .tab-option-drop-item {
  padding: 6px 10px;
  cursor: pointer;
  white-space: nowrap;
}
.detail-prod-tabs .tab-item .tab-option .tab-option-drop .tab-option-drop-item:hover {
  background: #f5f7fa;
}
.detail-prod-tabs .tab-item.sortable {
  cursor: move;
}
.detail-prod-tabs .tab-item.sortable .tab-option {
  cursor: pointer;
}
.detail-prod-tabs .tab-item.placehodler {
  opacity: 0.1;
}
.detail-prod-list-wrap {
  margin: 0 -20px -40px -20px;
}
.detail-prod-list-wrap .detail-prod-options-placeholder {
  height: 64px;
}
.detail-prod-list-wrap .detail-prod-options {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-top: solid 1px #E1E5E8;
}
.detail-prod-list-wrap .detail-prod-options .prod-select-num {
  color: #999;
  margin-left: 10px;
}
.detail-prod-list-wrap .detail-prod-options.fixed {
  position: fixed;
  top: 72px;
  z-index: 100;
  background: #fff;
  width: 1200px;
}
.detail-prod-list-wrap .vd-ui-table-wrap .vd-ui-table-header {
  top: 1px;
}
.detail-prod-list-wrap .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-th {
  border: 1px solid #E1E5E8;
  padding: 8px 10px;
  font-size: 12px;
}
.detail-prod-list-wrap .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td {
  border: 1px solid #E1E5E8;
  padding: 10px 3px 10px 10px;
  font-size: 12px;
}
.detail-prod-list-wrap .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .option-item {
  margin-right: 0;
  margin-bottom: 5px;
}
.detail-prod-list-wrap .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .option-item:last-child {
  margin-bottom: 0;
}
.detail-prod-list-wrap .vd-ui-table-wrap .vd-ui-table .vd-ui-tr .vd-ui-td .option-item::before {
  display: none;
}
.detail-prod-list-wrap .td-cnt-wrap {
  height: 108px;
  overflow: auto;
  overscroll-behavior: contain;
}
.detail-prod-list-wrap .td-cnt-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.detail-prod-list-wrap .td-cnt-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.detail-prod-list-wrap .td-cnt-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.detail-prod-list-wrap .td-cnt-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.detail-prod-list-wrap .td-cnt-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.detail-prod-list-wrap .prod-info-wrap {
  display: flex;
}
.detail-prod-list-wrap .prod-info-wrap .prod-img {
  width: 60px;
  height: 60px;
  margin-right: 10px;
}
.detail-prod-list-wrap .prod-info-wrap .prod-img img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.detail-prod-list-wrap .prod-info-wrap .prod-info {
  flex: 1;
}
.detail-prod-list-wrap .prod-info-wrap .prod-info .prod-sku {
  color: #09f;
  cursor: pointer;
  margin-bottom: 5px;
}
.detail-prod-list-wrap .prod-info-wrap .prod-info .prod-sku:hover {
  color: #f60;
}
.detail-prod-list-wrap .prod-info-wrap .prod-info .prod-name {
  margin-bottom: 5px;
}
.detail-prod-list-wrap .prod-detail {
  margin-bottom: 5px;
  display: flex;
}
.detail-prod-list-wrap .prod-detail .detail-label {
  color: #999;
}
.detail-prod-list-wrap .prod-detail .detail-txt {
  flex: 1;
}
.detail-prod-list-wrap .prod-detail:last-child {
  margin-bottom: 0;
}
.detail-prod-list-wrap .prod-detail .user-wrap {
  display: flex;
  align-items: center;
}
.detail-prod-list-wrap .prod-detail .user-wrap .user-avatar {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 5px;
}
.detail-prod-list-wrap .prod-detail .user-wrap .user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.detail-prod-list-wrap .prod-empty-wrap {
  text-align: center;
  padding: 100px 0;
}
.detail-prod-list-wrap .prod-empty-wrap .empty-img {
  width: 110px;
  height: 95px;
  background: url(../../images/search-empty.svg);
  background-size: 100% 100%;
  display: inline-block;
  vertical-align: top;
  margin-bottom: 20px;
}
.multi-add-wrap {
  display: flex;
}
.multi-add-wrap .multi-add-l {
  width: 180px;
  margin-right: 20px;
}
.multi-add-wrap .multi-add-r {
  flex: 1;
}
.multi-add-wrap .multi-add-r .multi-add-tips {
  color: #999;
  margin-bottom: 15px;
  line-height: 24px;
}
.multi-add-wrap .multi-add-r .multi-error-wrap {
  display: flex;
  color: #e64545;
  word-break: break-all;
}
.multi-add-wrap .multi-add-r .multi-error-wrap .icon-error2 {
  font-size: 16px;
  line-height: 1;
  margin-right: 5px;
  margin-top: 3px;
}
