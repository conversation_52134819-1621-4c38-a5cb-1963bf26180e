package com.wms.task;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WarehouseGoodsStatusMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.dto.WmsStockRequest;
import com.wms.dto.WmsStockResponse;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WmsCheckStockTask.java
 * @Description TODO wms数据校准erp库存
 * @createTime 2020年08月28日 09:45:00
 */
@Component
@JobHandler(value="WmsCheckStockTask")
public class WmsCheckStockTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(WmsCheckStockTask.class);

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;
    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private WarehouseGoodsStatusMapper warehouseGoodsStatusMapper;

    @Value("${WMS_CHECK_STOCK_ALARM_MAIL}")
    protected String  WMS_CHECK_STOCK_ALARM_MAIL;

    private StringBuffer errorMessage;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        errorMessage = new StringBuffer();
        dealCheck(param);
//        wmsCheckStockSendMail(errorMessage.toString());
        return SUCCESS;
    }

    private void dealCheck(String param) {
        String type = "";
        WmsStockRequest wmsStockRequest = new WmsStockRequest();
        String[] split = param.split(",");
        if (split[0].contains("V")){
            String sku = split[0];
            type = split.length > 1 ? split[1] : "";
            wmsStockRequest.setSku(sku.trim());
            search(wmsStockRequest,type);
        }else{
            if("initall".equals(param)){
                type = "init";
            }
            List<WarehouseStock> stockNum = warehouseGoodsStatusMapper.getAllStockId();
            for (WarehouseStock warehouseStock : stockNum) {
                wmsStockRequest.setSku(warehouseStock.getSku());
                search(wmsStockRequest,type);
            }
        }

    }

    private void search(WmsStockRequest wmsStockRequest,String type) {

        try {
            WmsInterface cancelSaleorder = wmsInterfaceFactory.getWmsInterface(WMSContant.QUERT_WMS_SKUSTOCK);
            WmsResponse<List<WmsStockResponse>> response = cancelSaleorder.request(wmsStockRequest);
            List<WmsStockResponse> dataList = response.getData();
            WarehouseGoodsOperateLog warehouseGoodsOperateLogForQuery = new WarehouseGoodsOperateLog();
            List<WarehouseGoodsOperateLog> availAbleStockNumList = warehouseGoodsOperateLogMapper.getAvailAbleStockNumByGoodsId(Integer.valueOf(wmsStockRequest.getSku().substring(1)));

            //wms无库存,erp有库存
            if(CollectionUtils.isEmpty(dataList)){
                if(CollectionUtils.isNotEmpty(availAbleStockNumList)){
                    logPrint(availAbleStockNumList);
                    subStock2(availAbleStockNumList,type);
                }
                return;
            }

            //erp当前库存
            Map<String,List<WarehouseGoodsOperateLog>> inStockMap = new HashMap<>();
            for (WarehouseGoodsOperateLog inlog : availAbleStockNumList) {
                List<WarehouseGoodsOperateLog> logList = inStockMap.get(inlog.getVedengBatchNumer()+","+LogicalEnum.getLogicalWarehouseCode(inlog.getLogicalWarehouseId()));
                if(CollectionUtils.isEmpty(logList)){
                    logList = new ArrayList<>();
                }
                logList.add(inlog);
                inStockMap.put(inlog.getVedengBatchNumer()+","+LogicalEnum.getLogicalWarehouseCode(inlog.getLogicalWarehouseId()),logList);
            }
            //wms当前库存
            Map<String,WmsStockResponse> stockData = new HashMap<>();
            for (WmsStockResponse wmsStockResponse : dataList) {
                WmsStockResponse var1 = stockData.get(wmsStockResponse.getLotAtt11()+","+wmsStockResponse.getLotAtt08());
                if(var1 == null){
                    stockData.put(wmsStockResponse.getLotAtt11()+","+wmsStockResponse.getLotAtt08(),wmsStockResponse);
                }else{
                  var1.setQty(var1.getQty().add(wmsStockResponse.getQty()));
                  stockData.put(wmsStockResponse.getLotAtt11()+","+wmsStockResponse.getLotAtt08(),var1);
                }
            }

            //比较两边库存
            for (String key : stockData.keySet()) {
                WmsStockResponse datum = stockData.get(key);
                inStockMap.remove(key);
                if(datum.getQty().intValue() == 0){
                    continue;
                }
                List<WarehouseGoodsOperateLog> logList = getWarehouseGoodsOperateLogs(warehouseGoodsOperateLogForQuery, datum);
                if(CollectionUtils.isEmpty(logList)){
                    String format = String.format("数量不匹配erp未查到库存 sku: %s,逻辑仓: %s,贝登批次码: %s,WMS num: %s,来源: %s",
                            datum.getSku(), datum.getLotAtt08(), datum.getLotAtt11(), datum.getQty().toString(), datum.getLotAtt10());
                    logger.info(format);
                    XxlJobLogger.log(format);
                    errorMessage.append(format).append("\n");
                    addStock(datum,datum.getQty().intValue(),type);
                    continue;
                }
                Integer num = logList.stream().collect(Collectors.summingInt(WarehouseGoodsOperateLog::getLastStockNum));
                if(!num.equals(datum.getQty().intValue())){
                    if(num > datum.getQty().intValue()){
                        String format = String.format("数量不匹配erp大于wms sku: %s,逻辑仓: %s,贝登批次码: %s,erp数量: %s,wms数量: %s,来源: %s",
                                datum.getSku(),datum.getLotAtt08(),datum.getLotAtt11(),num,datum.getQty(),datum.getLotAtt10());
                        logger.info(format);
                        XxlJobLogger.log(format);
                        errorMessage.append(format).append("\n");
                        int subNum = num - datum.getQty().intValue();
                        subStock(datum, subNum,type);

                    }else if(num < datum.getQty().intValue()){
                        String format = String.format("数量不匹配erp小于wms sku: %s,逻辑仓: %s,贝登批次码: %s,erp数量: %s,wms数量: %s,来源: %s",
                                datum.getSku(),datum.getLotAtt08(),datum.getLotAtt11(),num,datum.getQty(),datum.getLotAtt10());
                        logger.info(format);
                        XxlJobLogger.log(format);
                        errorMessage.append(format).append("\n");
                        int addNum = datum.getQty().intValue() - num;
                        addStock(datum, addNum,type);
                    }
                }else{
                    logger.info("数量匹配erp等于wms sku:{},逻辑仓:{},贝登批次码:{},erp数量:{},wms数量:{},来源:{}"
                            ,datum.getSku(),datum.getLotAtt08(),datum.getLotAtt11(),num,datum.getQty(),datum.getLotAtt10());
                    XxlJobLogger.log("数量匹配erp等于wms sku:{},逻辑仓:{},贝登批次码:{},erp数量:{},wms数量:{},来源:{}"
                            ,datum.getSku(),datum.getLotAtt08(),datum.getLotAtt11(),num,datum.getQty(),datum.getLotAtt10());
                    updateInfo(logList,datum,type);
                }
            }
            for (String key : inStockMap.keySet()) {
                List<WarehouseGoodsOperateLog> logList = inStockMap.get(key);
                logPrint(logList);
                subStock2(logList,type);
            }
        } catch (Exception e) {
            logger.error("请求接口报错", e);
        }
    }

    private void logPrint(List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogList) {
        Integer num = warehouseGoodsOperateLogList.stream().collect(Collectors.summingInt(WarehouseGoodsOperateLog::getLastStockNum));
        WarehouseGoodsOperateLog log = warehouseGoodsOperateLogList.get(0);
        log.setSku("V"+log.getGoodsId());
        String format = String.format("数量不匹配erp有库存wms无 sku: %s,逻辑仓: %s,贝登批次码: %s,数量: %s",
                log.getSku(),LogicalEnum.getLogicalWarehouseCode(log.getLogicalWarehouseId()),log.getVedengBatchNumer(),num);
        logger.info(format);
        XxlJobLogger.log(format);
        errorMessage.append(format).append("\n");
    }

    private void updateInfo(List<WarehouseGoodsOperateLog> logList, WmsStockResponse datum, String type) {
        if(!isAsn(type,datum)){
            return;
        }
        for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : logList) {
            WarehouseGoodsOperateLog update = new WarehouseGoodsOperateLog();
            update.setWarehouseGoodsOperateLogId(warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId());
            update.setVedengBatchNumer(datum.getLotAtt11());
            update.setBatchNumber(datum.getLotAtt04());
            //生产日期
            update.setProductDate(DateUtil.convertLong(datum.getLotAtt01() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
            //效期
            update.setExpirationDate(DateUtil.convertLong(datum.getLotAtt02() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
            warehouseGoodsOperateLogMapper.updateByPrimaryKeySelective(update);
        }
    }

    private void subStock2(List<WarehouseGoodsOperateLog> availAbleStockNumList,String type) {
        if(!"init".equals(type)){
            return;
        }
        for (WarehouseGoodsOperateLog  warehouseGoodsOperateLog: availAbleStockNumList) {
            WarehouseGoodsOperateLog update = new WarehouseGoodsOperateLog();
            update.setWarehouseGoodsOperateLogId(warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId());
            update.setVedengBatchNumer("");
            warehouseGoodsOperateLogMapper.updateByPrimaryKeySelective(update);
        }
    }

    private boolean isAsn(String type, WmsStockResponse datum) {
        return datum.getLotAtt10().contains("ASN") && "init".equals(type);
    }

    private void addStock(WmsStockResponse datum, int addNum,String type) {
        if(!isAsn(type,datum)){
            return;
        }
        WarehouseGoodsOperateLog warehouseGoodsOperateLogForQuery = new WarehouseGoodsOperateLog();
        warehouseGoodsOperateLogForQuery.setGoodsId(Integer.valueOf(datum.getSku().substring(1)));
        warehouseGoodsOperateLogForQuery.setVedengBatchNumer("");
        List<WarehouseGoodsOperateLog> availableLogicalGoods = warehouseGoodsOperateLogMapper.getAvailableLogicalGoods(warehouseGoodsOperateLogForQuery);
        if(CollectionUtils.isEmpty(availableLogicalGoods)){
            return;
        }
        for (WarehouseGoodsOperateLog availableLogicalGood : availableLogicalGoods) {
            if(addNum == 0){
                break;
            }
            WarehouseGoodsOperateLog update = new WarehouseGoodsOperateLog();
            update.setWarehouseGoodsOperateLogId(availableLogicalGood.getWarehouseGoodsOperateLogId());
            update.setVedengBatchNumer(datum.getLotAtt11());
            update.setBatchNumber(datum.getLotAtt04());
            //生产日期
            update.setProductDate(DateUtil.convertLong(datum.getLotAtt01() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
            //效期
            update.setExpirationDate(DateUtil.convertLong(datum.getLotAtt02() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
            update.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(datum.getLotAtt08()));
            warehouseGoodsOperateLogMapper.updateByPrimaryKeySelective(update);
            addNum--;
        }
    }

    private void subStock(WmsStockResponse datum, int subNum,String type) {
        if(!isAsn(type,datum)){
            return;
        }
        List<WarehouseGoodsOperateLog> logList = getWarehouseGoodsOperateLogs(new WarehouseGoodsOperateLog(), datum);
        if(CollectionUtils.isEmpty(logList)){
            return;
        }
        for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : logList) {
            if(subNum == 0){
                break;
            }
            WarehouseGoodsOperateLog update = new WarehouseGoodsOperateLog();
            update.setWarehouseGoodsOperateLogId(warehouseGoodsOperateLog.getWarehouseGoodsOperateLogId());
            update.setVedengBatchNumer("");
            warehouseGoodsOperateLogMapper.updateByPrimaryKeySelective(update);
            subNum--;
        }
    }


    private List<WarehouseGoodsOperateLog> getWarehouseGoodsOperateLogs(WarehouseGoodsOperateLog warehouseGoodsOperateLogForQuery, WmsStockResponse datum) {
        warehouseGoodsOperateLogForQuery.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(datum.getLotAtt08()));
        warehouseGoodsOperateLogForQuery.setGoodsId(Integer.valueOf(datum.getSku().substring(1)));
        warehouseGoodsOperateLogForQuery.setVedengBatchNumer(datum.getLotAtt11());
        return warehouseGoodsOperateLogMapper.getAvailableLogicalGoods(warehouseGoodsOperateLogForQuery);
    }


    @Autowired
    private  JavaMailSender javaMailSender;

    @Autowired
    private SimpleMailMessage simpleMailMessage;

    private void wmsCheckStockSendMail(String message){
        try{
            //库存校验异常情况邮件接收人数组
            String[] alarmMailReceiver = {};
//                    { "<EMAIL>"//strange };
            if (StringUtil.isNotBlank(WMS_CHECK_STOCK_ALARM_MAIL)){
                alarmMailReceiver = WMS_CHECK_STOCK_ALARM_MAIL.split(",");
            }else{
                return;
            }
            simpleMailMessage.setTo(alarmMailReceiver) ;
            simpleMailMessage.setSubject("WmsCheckStockTask  库存校验结果！");
            simpleMailMessage.setSentDate(new Date());
            if(StringUtil.isBlank(message)){
                message = "核对完成,没有差异.";
            }
            simpleMailMessage.setText(message);
            javaMailSender.send(simpleMailMessage);
        }catch (Exception e){
            logger.error("wmsCheckStockSendMail，发生异常：",e);
        }
    }

}
