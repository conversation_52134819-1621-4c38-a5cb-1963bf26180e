package com.newtask.goods;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.domain.entity.CoreSku;
import com.vedeng.goods.domain.entity.SkuTerminalLevel;
import com.vedeng.goods.mapper.CoreSkuMapper;
import com.vedeng.goods.mapper.SkuTerminalLevelMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 从 T_SKU_TERMINAL_LEVEL 同步机构等级字段到 sku 表中
 * @date 2022/2/16 9:42
 */
@JobHandler(value = "SyncSkuInstitutionLevelTask")
@Component
@Slf4j
public class SyncSkuInstitutionLevelTask extends AbstractJobHandler {

    @Autowired
    private CoreSkuMapper coreSkuMapper;
    @Autowired
    private SkuTerminalLevelMapper skuTerminalLevelMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("同步机构等级start-----------");
        List<SkuTerminalLevel> skuTerminalLevels = skuTerminalLevelMapper.findByAll(new SkuTerminalLevel());
        XxlJobLogger.log("同步机构等级总任务数:[{}}", skuTerminalLevels.size());
        List<CoreSku> coreSkus = new ArrayList<>();
        if(CollUtil.isNotEmpty(skuTerminalLevels)){
            skuTerminalLevels.forEach(skuTerminalLevel -> {
                CoreSku sku = new CoreSku();
                sku.setSkuNo(skuTerminalLevel.getSkuNo());
                sku.setInstitutionLevelIds(skuTerminalLevel.getTerminalLevel());
                coreSkus.add(sku);
            });
            int taskCountSku = coreSkuMapper.updateBatchSelectiveInstitutionLevel(coreSkus);
            int taskCountSkuSearch = coreSkuMapper.updateBatchSelectiveInstitutionLevelSkuSearch(coreSkus);
            XxlJobLogger.log("Sku成功同步机构等级总任务数:[{}}", taskCountSku);
            XxlJobLogger.log("SkuSearch成功同步机构等级总任务数:[{}}", taskCountSkuSearch);
        }
        XxlJobLogger.log("同步机构等级end-----------");
        return SUCCESS;
    }

}
