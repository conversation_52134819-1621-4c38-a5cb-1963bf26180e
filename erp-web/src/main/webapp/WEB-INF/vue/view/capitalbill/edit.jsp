<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none;">

    <el-tabs v-model="activeName" @tab-click="handleClick" stretch="true">
        <el-tab-pane label="流水添加" name="flow">
            <el-form :rules="rules" label-width="100px" ref="form" :model="form">

                <el-form-item label="流水号" prop="tranFlow">
                    <el-select
                            style="width: 500px;"
                            v-model="form.tranFlow"
                            filterable
                            clearable
                            reserve-keyword
                            placeholder="请选择流水号"
                            remote
                            :remote-method="getBankBill"
                    >
                        <el-option
                                v-for="item in bankBills"
                                :key="item.traderContactId"
                                :label="item.tranFlow"
                                :value="item.tranFlow"
                                @click.native="clickBankBill(item)"
                        >
                            <span style="float: left">{{item.tranFlow}}</span>
                        </el-option>
                    </el-select>
                </el-form-item>


                <el-form-item label="交易方式" prop="traderModeName">
                    <span>{{form.traderModeName}}</span>
                </el-form-item>
                <el-form-item label="交易媒介" prop="traderMedium">
                    <span>{{form.traderMedium}}</span>
                </el-form-item>
                <el-form-item label="交易主体" prop="traderSubject">
                    <span>对公</span>
                </el-form-item>
                <el-form-item label="交易金额" prop="amount">
                    <el-input-number v-model="form.amount" style="width: 80%;" :min="0"
                                     :precision="2"></el-input-number>
                </el-form-item>
                <el-form-item label="交易名称" prop="payer">
                    <el-input v-model="form.payer" style="width: 80%;"></el-input>
                </el-form-item>

                <el-form-item label="开户银行" prop="payerBankName">
                    <el-input v-model="form.payerBankName" style="width: 80%;"></el-input>
                </el-form-item>
                <el-form-item label="银行账号" prop="payerBankAccount">
                    <el-input v-model="form.payerBankAccount" style="width: 80%;"></el-input>
                </el-form-item>
                <el-form-item label="交易备注" prop="comments">
                    <el-input v-model="form.comments" style="width: 80%;"></el-input>
                </el-form-item>
            </el-form>

            <div class="btn-container">
                <el-button type="primary" @click="onSubmit('form')" :loading="onSubmitLoading">提交</el-button>
                <el-button @click="closeThis">取消</el-button>
            </div>

        </el-tab-pane>

        <el-tab-pane label="手动添加" name="manual">
            <el-form :rules="rules" label-width="100px" ref="form" :model="form">

                <el-row>
                    <el-col :span="24">
                        <el-form-item label="交易方式" prop="traderMode">
                            <el-radio-group v-model="form.traderMode">
                                <el-radio :label="520">支付宝</el-radio>
                                <el-radio :label="521">银行</el-radio>
                                <el-radio :label="523">现金</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="交易主体" prop="traderSubject">
                            <span>对公</span>
                        </el-form-item>
                        <el-form-item label="交易金额" prop="amount">
                            <el-input-number v-model="form.amount" style="width: 80%;" :min="0"
                                             :precision="2"></el-input-number>
                        </el-form-item>
                        <el-form-item label="交易名称" prop="payer">
                            <el-input v-model="form.payer" style="width: 80%;"></el-input>
                        </el-form-item>
                        <el-form-item label="开户银行" prop="payerBankName">
                            <el-input v-model="form.payerBankName" style="width: 80%;"></el-input>
                        </el-form-item>
                        <el-form-item label="银行账号" prop="payerBankAccount">
                            <el-input v-model="form.payerBankAccount" style="width: 80%;"></el-input>
                        </el-form-item>
                        <el-form-item label="交易备注" prop="comments">
                            <el-input v-model="form.comments" style="width: 80%;"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <div class="btn-container">
                <el-button type="primary" @click="onSubmit('form')" :loading="onSubmitLoading">提交</el-button>
                <el-button @click="closeThis">取消</el-button>
            </div>

        </el-tab-pane>

    </el-tabs>

</div>

<script src="${pageContext.request.contextPath}/static/api/expenseAftersale/expenseAfterSales.js?rnd=${resourceVersionKey}"></script>


<script type="text/javascript">

    const ID = '${expenseAfterSalesId}';

    new Vue({
        el: '#app',
        data() {
            return {
                onSubmitLoading: false,
                activeName: 'flow',
                //表单校验
                rules: {
                    traderMode: [
                        {required: true, message: '请选择交易方式', trigger: 'blur'}
                    ],
                    traderSubject: [
                        {required: true, message: '请填写交易主体', trigger: 'blur'}
                    ],
                    amount: [
                        {required: true, message: '请填写交易金额', trigger: 'blur'}
                    ],
                    payer: [
                        {required: true, message: '交易名称不允许为空', trigger: 'blur'},
                        {max: 128, message: '交易名称不允许超过128个字符', trigger: 'blur'}
                    ],
                    payerBankName: [
                        {max: 100, message: '开户银行不允许超过100个字符', trigger: 'blur'}
                    ],
                    payerBankAccount: [
                        {max: 100, message: '银行账号不允许超过100个字符', trigger: 'blur'}
                    ],
                    comments: [
                        {max: 200, message: '交易备注不允许超过200个字符', trigger: 'blur'}
                    ]

                },
                //表单
                form: {
                    bankBillId: null,
                    tranFlow: null,
                    traderSubject: 1,
                    traderMode: 521,
                    amount: null,
                    payer: null,
                    payerBankName: null,
                    payerBankAccount: null,
                    comments: null,
                    capitalBillDetailDto: {
                        relatedId: ID,
                    }
                },
                isReadonly: false,
                bankBills: []
            };
        },
        mounted() {
            loadingApp()
        },
        created() {
            this.initForm();
        },
        methods: {
            closeThis() {
                parent.layer.close(index);
            },
            onSubmit(form) {
                this.onSubmitLoading = true;
                if (this.activeName == 'flow' && (this.form.tranFlow == null || this.form.tranFlow.replace(/\s*/g, "") == '')) {
                    this.$message({
                        message: '流水号不允许为空',
                        showClose: true,
                        type: 'error',
                    });
                    this.onSubmitLoading = false;
                    return
                }

                if (this.form.payer == null || this.form.payer.replace(/\s*/g, "") == '') {
                    this.$message({
                        message: '交易名称不允许为空',
                        showClose: true,
                        type: 'error',
                    });
                    this.onSubmitLoading = false;
                    return
                }

                if (this.form.payer.length > 128) {
                    this.$message({
                        message: '交易名称不允许超过128个字符',
                        showClose: true,
                        type: 'error',
                    });
                    this.onSubmitLoading = false;
                    return
                }

                if (this.form.amount == 0.00) {
                    this.$message({
                        message: '交易金额不能为0',
                        showClose: true,
                        type: 'error',
                    });
                    this.onSubmitLoading = false;
                    return
                }

                this.$refs[form].validate((valid) => {
                    if (valid) {
                        saveExpenseAfterSalesCapitalBill(this.form).then(res => {
                            if (res.data.code === 0) {
                                window.parent.location.reload();
                                parent.layer.close(index);
                            } else {
                                this.$message({
                                    message: res.data.message,
                                    showClose: true,
                                    type: 'error',
                                });
                            }
                            this.onSubmitLoading = false
                        });
                    }
                    this.onSubmitLoading = false
                })
            },

            initForm() {
                if (this.form.bankId !== '') {
                    this.isReadonly = true

                }
            },

            handleClick(tab, event) {
                this.form = {
                    tranFlow: null,
                    traderSubject: 1,
                    traderSubjectName: null,
                    traderMode: 521,
                    amount: null,
                    payer: null,
                    payerBankName: null,
                    payerBankAccount: null,
                    comments: null,
                    capitalBillDetailDto: {
                        relatedId: ID,
                    }
                }
            },

            getBankBill(flowNo) {
                getBankBill({"flowNo": flowNo}).then(res => {
                    if (res.data.code === 0) {
                        this.bankBills = res.data.data
                    }
                });
            },
            clickBankBill(e) {
                this.form.amount = e.amt;
                this.form.payer = e.accName1;
                this.form.bankBillId = e.bankBillId;
                this.form.traderSubject = 1;
                this.buildBankBillStatus(e.bankTag)
            },

            buildBankBillStatus(bankTag) {
                switch (bankTag) {
                    case 1:
                        this.form.traderMedium = '建设银行';
                        this.form.traderModeName = '银行';
                        this.form.traderMode = 521;
                        break;
                    case 2:
                        this.form.traderMedium = '南京银行';
                        this.form.traderModeName = '银行';
                        this.form.traderMode = 521;
                        break;
                    case 3:
                        this.form.traderMedium = '中国银行';
                        this.form.traderModeName = '银行';
                        this.form.traderMode = 521;
                        break;
                    case 4:
                        this.form.traderMedium = '支付宝';
                        this.form.traderModeName = '支付宝';
                        this.form.traderMode = 520;
                        break;
                    case 5:
                        this.form.traderMedium = '微信';
                        break;
                    default:
                }
            },


        }
    });


</script>

<style>
    .btn-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%; /* 根据实际情况调整高度 */
    }
</style>
