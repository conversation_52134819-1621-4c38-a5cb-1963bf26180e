package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.ExpressCostBatchJob;
import com.vedeng.erp.kingdee.batch.repository.BatchExpressCostDtoMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 快递成本
 * 推送规则：1次/月
 */
@JobHandler(value = "ExpressCostBatchTask")
@Component
public class ExpressCostBatchTask extends AbstractJobHandler {

    @Autowired
    private ExpressCostBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    BatchExpressCostDtoMapper batchExpressCostDtoMapper;

    /**
     * {"beginTime":"2023-04-01 00:00:00",
     * "endTime":"2023-04-30 23:59:59"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================ExpressCostBatchJob开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.expressCostPushJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================ExpressCostBatchJob结束====================");
        return SUCCESS;
    }


}

