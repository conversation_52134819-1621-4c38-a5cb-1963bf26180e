<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/tailwind/tailwind.min.css">

<div id="app" style="display: none;">
    <%--进度条--%>
    <br>
    <br>
    <div class="container mx-auto">
        <el-steps finish-status="success" align-center>
            <template v-for="(item, index) in items">
                <el-step :title="item.title" :status="item.type">
                    <template slot="icon">
                        <div class="el-step__icon-inner">&nbsp;</div>
                    </template>
                </el-step>
            </template>
        </el-steps>
    </div>
    <br>
    <br>

    <el-descriptions title="基本信息" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>基本信息</div>
        </div>
        <el-descriptions-item label="采购费用单号" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseNo}}
        </el-descriptions-item>
        <el-descriptions-item label="创建者" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.creatorName}}
        </el-descriptions-item>
        <el-descriptions-item label="部门" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.orgName}}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{parseTime(buyorderExpense.addTime)}}
        </el-descriptions-item>
        <el-descriptions-item label="生效时间" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{parseTime(buyorderExpense.validTime)}}
        </el-descriptions-item>
        <el-descriptions-item label="订单状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="buyorderExpense.status==0">待确认</span>
            <span v-if="buyorderExpense.status==1">进行中</span>
            <span v-if="buyorderExpense.status==2">已完结</span>
            <span v-if="buyorderExpense.status==3">已关闭</span>
        </el-descriptions-item>
        <el-descriptions-item label="审核状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="buyorderExpense.auditStatus==0">待审核</span>
            <span v-if="buyorderExpense.auditStatus==1">审核中</span>
            <span v-if="buyorderExpense.auditStatus==2">审核通过</span>
            <span v-if="buyorderExpense.auditStatus==3">审核不通过</span>
        </el-descriptions-item>
        <el-descriptions-item label="生效状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <span v-if="buyorderExpense.validStatus==0">未生效</span>
            <span v-if="buyorderExpense.validStatus==1">已生效</span>
        </el-descriptions-item>
        <el-descriptions-item label="锁定状态" label-class-name="my-label my-text-align"
                              content-class-name="my-content  my-text-align">
            <span v-if="buyorderExpense.lockedStatus==0">未锁定</span>
            <span v-if="buyorderExpense.lockedStatus==1">已锁定
                <span v-if="haveAfterSale">(售后锁定)</span>
                <span v-else="haveAfterSale">(付款锁定)</span>
            </span>
        </el-descriptions-item>
        <el-descriptions-item label="订单说明" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
<%--            <div v-html="parseOrderDesc(buyorderExpense)"></div>--%>
            <template v-for="orderRemark in buyorderExpense.buyorderExpenseDetailDto.orderDesc">
                <template v-for="goodsDto in orderRemark.originalOrderDto.goodsDtos">
                    <template v-if="orderRemark.type == 1">
                        本订单关联的销售订单
                        <el-link type="primary" :underline="false" @click="viewSaleOrderInfo(orderRemark.originalOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.originalOrderDto.no }}</el-link>
                        中
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(goodsDto.goodsId)" style="font-weight: 700;">{{ goodsDto.sku }}</el-link>
                        订货号退货 {{ goodsDto.x }} 个，分摊后本订单需要退货 {{ goodsDto.y }} 个
                        <br>
                    </template>
                    <template v-if="orderRemark.type == 2">
                        <%--该订单由销售单A中B订货号退货，联动采购费用单退货，已自动转单至C订单--%>
                        该订单由销售单
                        <el-link type="primary" :underline="false" @click="viewSaleOrderInfo(orderRemark.originalOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.originalOrderDto.no }}</el-link>
                        中
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(goodsDto.goodsId)" style="font-weight: 700;">{{ goodsDto.sku }}</el-link>
                        订货号退货，联动采购费用单退货，已自动转单至
                        <el-link type="primary" :underline="false" @click="viewExpenseDetail(orderRemark.resultOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.resultOrderDto.no }}</el-link>
                        订单
                        <br>
                    </template>
                    <template v-if="orderRemark.type == 3">
                        <%--该订单由销售单A中B订货号退货，联动采购费用单C退货，自动转单生成--%>
                        该订单由销售单
                        <el-link type="primary" :underline="false" @click="viewSaleOrderInfo(orderRemark.originalOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.originalOrderDto.no }}</el-link>
                        中
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(goodsDto.goodsId)" style="font-weight: 700;">{{ goodsDto.sku }}</el-link>
                        订货号退货，联动采购费用单
                        <el-link type="primary" :underline="false" @click="viewExpenseDetail(orderRemark.resultOrderDto.orderId)" style="font-weight: 700;">{{ orderRemark.resultOrderDto.no }}</el-link>
                        退货，自动转单生成
                        <br>
                    </template>
                </template>
            </template>
        </el-descriptions-item>
    </el-descriptions>


    <br>
    <br>
    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>审核记录</span>
        </div>
        <br>
        <div class="container mx-auto">
            <el-steps  finish-status="success" align-center>
                <template v-for="(ite,index) in lastAudit">
                    <el-step :title="ite.title"  :status="ite.type">
                        <template slot="icon">
                            <div class="el-step__icon-inner">&nbsp;</div>
                        </template>

                        <template slot="description" >
                            <template v-for="(da,ind) in ite.descriptions">

                                <template v-if="ind==2">
                                    <template v-if="da != ''&&da!=null&&da!=undefined">
                                        <p class="break-all" >
                                            备注：{{splitStrAddAllipsis(da,14)}}
                                            <template v-if="isSplitStrAddAllipsis(da,14)">
                                                <el-popover trigger="hover" placement="right" >
                                        <p class="break-all w-28">备注：{{da}}</p>
                                        <template slot="reference" >
                                            <i class="el-icon-copy-document"></i>
                                        </template>
                                        </el-popover>
                                    </template>
                                    </p>
                                </template>


                            </template>
                            <template v-if="ind==0">
                                <p class="break-all" >{{splitStrAddAllipsis(da,14)}}
                                    <template v-if="isSplitStrAddAllipsis(da,14)">
                                        <el-popover trigger="hover" placement="right" >
                                <p class="break-all w-28">{{da}}</p>
                                <template slot="reference" >
                                    <i class="el-icon-copy-document"></i>
                                </template>
                                </el-popover>
                            </template>
                            </p>
                        </template>
                        <template v-if="ind==1">
                            <p class="break-all">{{da}}</p>
                        </template>
                </template>
                </template>
                </el-step>
                </template>
            </el-steps>
        </div>
        <div class="container mx-auto" v-if="buyorderExpense.isAuto==1" align="center">
            <span align="center">该订单已自动审核，无需线下人工审核</span>
        </div>
        <br>
    </el-card>


    <br>
    <br>
    <el-descriptions title="供应商信息" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>供应商信息</div>
        </div>
        <el-descriptions-item label="供应商名称" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            <el-link type="primary" :underline="false"
                     @click="viewTraderSupplier(buyorderExpense.buyorderExpenseDetailDto.traderId)">
                {{buyorderExpense.buyorderExpenseDetailDto.traderName}}
            </el-link>
        </el-descriptions-item>
        <el-descriptions-item label="联系人" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseDetailDto.traderContactName}}
        </el-descriptions-item>
        <el-descriptions-item label="电话" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseDetailDto.traderContactTelephone}}
        </el-descriptions-item>
        <el-descriptions-item label="手机" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseDetailDto.traderContactMobile}}
        </el-descriptions-item>
        <el-descriptions-item label="地区" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseDetailDto.traderArea}}
            {{buyorderExpense.traderAddress}}
        </el-descriptions-item>
        <el-descriptions-item label="供应商备注" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">
            {{buyorderExpense.buyorderExpenseDetailDto.traderComments}}
        </el-descriptions-item>
    </el-descriptions>

    <br>
    <br>
    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>虚拟商品</span>
        </div>
        <template>
            <el-table
                    :data="buyorderExpense.buyorderExpenseItemDtos"
                    border
                    :header-cell-style="{textAlign: 'center'}"
                    style="width: 100%"
                    ref="tableData"
                    :header-cell-style="{background:'#f5f7fa'}"
                    :expand-row-keys="expends"
                    :row-key="getRowKeys"
                    :row-class-name="getRowClass">

                <el-table-column type="expand">
                    <template slot="header" slot-scope="scope">
                        <el-button type="text" size="mini" @click="toggleRowExpansion">{{ isExpand ? "收起" : "展开" }}
                        </el-button>
                    </template>
                    <template slot-scope="props">
                        <el-table :data="props.row.buyOrderSaleOrderGoodsDetailDtos"
                                  size="mini"
                                  :header-cell-style="{ 'text-align': 'center' }"
                                  :cell-style="{ 'text-align': 'center' }"
                                  style="margin-left:10px;width: 99%"
                                  border>
                            <el-table-column
                                    prop="saleorderNo"
                                    label="关联单号">
                                <template slot-scope="scope">
                                    <el-link type="primary"  @click="viewSaleOrder(scope.row)"><span style="font-size: 12px">{{scope.row.saleorderNo}}</span></el-link>
                                </template>

                            </el-table-column>
                            <el-table-column
                                    prop="applicantName"
                                    label="申请人">
                            </el-table-column>

                            <el-table-column prop="buyNum" label="采购数量/剩余数量">
                                <template slot-scope="scope">
                                    <span style="font-size: 12px">{{scope.row.buyNum}}</span> / <span style="font-size: 12px">{{_.max([scope.row.num, 0])}}</span>
                                </template>
                            </el-table-column>

                            <el-table-column
                                    prop="price"
                                    label="销售价">
                            </el-table-column>
                            <el-table-column
                                    prop="deliveryCycle"
                                    label="销售货期">
                            </el-table-column>
                            <el-table-column
                                    prop="insideComments"
                                    label="内部备注">
                            </el-table-column>
                            <el-table-column
                                    prop="goodsComments"
                                    label=产品备注>
                            </el-table-column>
                            <el-table-column
                                    prop="terminalTraderName"
                                    label="终端客户名称">
                            </el-table-column>
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="订货号"
                        width="180">
                    <template slot-scope="scope">
                        <span>{{ scope.row.buyorderExpenseItemDetailDto.sku }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="产品名称"
                        width="180">
                    <template slot-scope="scope">
                        <el-link type="primary" :underline="false" @click="viewSkuInfo(scope.row.goodsId)">
                            {{ scope.row.buyorderExpenseItemDetailDto.goodsName }}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="费用类别">
                    <template slot-scope="scope">
                        <span>{{scope.row.buyorderExpenseItemDetailDto.expenseCategoryName}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="产品归属">
                    <template slot-scope="scope">
                        <span>{{scope.row.buyorderExpenseItemDetailDto.assignmentManager}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="是否可库存管理">
                    <template slot-scope="scope">
                        <span v-if="scope.row.buyorderExpenseItemDetailDto.haveStockManage==1">是</span>
                        <span v-else>否</span>
                    </template>
                </el-table-column>
                <el-table-column label="采购数量" align="center">
                    <template slot-scope="scope">
                        <span>{{scope.row.num - (scope.row.returnNum==null?0:scope.row.returnNum)}} </span>
                        <el-popover trigger="hover" placement="right" v-if="scope.row.returnNum>0">
                            <span>原值: {{ scope.row.num }}</span>
                            <template slot="reference" >
                                <i class="el-icon-warning-outline"  style="color: red"></i>
                            </template>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column label="收票数量" align="center">
                    <template slot-scope="scope">
                        <span>{{scope.row.invoicedNum==null?0:scope.row.invoicedNum}} </span>
                        <el-popover trigger="hover" placement="right"  v-if="scope.row.invoiceByGoodsDtos != null && scope.row.invoiceByGoodsDtos.length>0">
                            <template v-for="item in scope.row.invoiceByGoodsDtos" >
                                <p>发票号:{{item.invoiceNo}}</p>
                                <p>票种:{{item.invoiceTypeStr}}</p>
                                <p>
                                    红蓝字:
                                    <span v-if="item.colorType == 1 && item.colorComplementType == 0">红字有效</span>
                                    <span v-if="item.colorType == 1 && item.colorComplementType == 1">蓝字冲销</span>
                                    <span v-if="item.colorType == 2 && item.isEnable == 1">蓝字有效</span>
                                    <span v-if="item.colorType == 2 && item.isEnable == 0">蓝字作废</span>
                                </p>
                                <p>金额:{{item.amount}}</p>
                                <p>开票数量:{{item.num}}</p>
                                <p>日期:{{parseTime(item.addTime)}}</p>
                                <br>
                            </template>
                            <template slot="reference" >
                                <i class="el-icon-copy-document"  style="color: #409EFF"></i>
                            </template>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="单价">
                    <template slot-scope="scope">
                        <span>{{formatNum(scope.row.buyorderExpenseItemDetailDto.price)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="总额">
                    <template slot-scope="scope">
                        <span>{{formatNum((scope.row.buyorderExpenseItemDetailDto.price * scope.row.num))}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        align="center"
                        label="采购备注">
                    <template slot-scope="scope">
                        <span>{{scope.row.buyorderExpenseItemDetailDto.insideComments}}</span>
                    </template>
                </el-table-column>
            </el-table>
            <div style="text-align: center;height:35px;background: #f2f2f2">
                <div style="padding: 6px;">
                    总件数
                    <span v-if="buyorderExpense.buyorderExpenseItemDtos!=null"
                          style="color:red;">{{sumGoodsNum(buyorderExpense.buyorderExpenseItemDtos)}}</span>
                    <span v-else style="color: red">0</span>，
                    总金额
                    <span v-if="buyorderExpense.buyorderExpenseItemDtos!=null"
                          style="color:red;">{{sumGoods(buyorderExpense.buyorderExpenseItemDtos)}}</span>
                </div>
            </div>
        </template>
    </el-card>


    <template v-if="buyorderExpense.buyorderId!=null&&buyorderExpense.buyorderId!=0">
        <br>
        <br>
        <el-descriptions title="采购订单" label-class-name="my-title" column="5" border direction="vertical">
            <div slot="title" style="width: 100%;padding-left: 10px">
                <div>采购订单</div>
            </div>
            <el-descriptions-item label="采购单号" label-class-name="my-text-align" content-class-name="my-text-align">
                <el-link type="primary" @click="viewBuyorderDetail(buyorderExpense.buyorderId)">
                    {{buyorderExpense.buyorderNo}}
                </el-link>
            </el-descriptions-item>
            <el-descriptions-item label="供应商" label-class-name="my-text-align" content-class-name="my-text-align">
                {{buyorderExpense.buyOrderDto.traderName}}
            </el-descriptions-item>
            <el-descriptions-item label="创建人" label-class-name="my-text-align" content-class-name="my-text-align">
                {{buyorderExpense.buyOrderDto.creatorName}}
            </el-descriptions-item>
            <el-descriptions-item label="创建人部门" label-class-name="my-text-align" content-class-name="my-text-align">
                {{buyorderExpense.buyOrderDto.orgName}}
            </el-descriptions-item>
            <el-descriptions-item label="创建时间" label-class-name="my-text-align" content-class-name="my-text-align">
                {{parseTime(buyorderExpense.buyOrderDto.addTime)}}
            </el-descriptions-item>
        </el-descriptions>

    </template>


    <br>
    <br>
    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>付款计划</span>
        </div>

        <template>
            <el-table
                    :data="buyorderExpense.paymentPlans"
                    border
                    :header-cell-style="{textAlign: 'center'}"
                    :cell-style="{textAlign: 'center'}"
                    :span-method="paymentPlanSpanMethod"
                    :row-style="{height:'30px'}"
                    :span-method="objectSpanMethod"
                    style="width: 100%">
                <el-table-column
                        label="计划"
                        width="180">
                    <template slot-scope="scope">
                        <span>{{ scope.row.plan }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="计划内容"
                        width="180">
                    <template slot-scope="scope">
                        <span>{{ scope.row.planContent }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="支付金额">
                    <template slot-scope="scope">
                        <span>{{formatNum(scope.row.amount)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="备注">
                    <template slot-scope="scope">
                        <span>{{scope.row.remark}}</span>
                    </template>
                </el-table-column>

            </el-table>
        </template>
    </el-card>

    <br>
    <br>
    <el-descriptions title="收票信息" label-class-name="my-title" column="2" border>
        <div slot="title" style="width: 100%;padding-left: 10px">
            <div>收票信息</div>
        </div>
        <el-descriptions-item label="收票类型" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseDetailDto.invoiceTypeStr}}
        </el-descriptions-item>
        <el-descriptions-item label="收票备注" label-class-name="my-label my-text-align"
                              content-class-name="my-content my-text-align">{{buyorderExpense.buyorderExpenseDetailDto.invoiceComments}}
        </el-descriptions-item>

    </el-descriptions>

    <br>
    <br>
    <div style="text-align: center">
        <template v-if="buyorderExpense.orderType==1">
            <el-button v-if="buttonData.indexOf('11') != -1" type="primary" size="small" @click="seePrints()">
                打印预览
            </el-button>
            <el-button v-if="buttonData.indexOf('12') != -1" type="primary" size="small" @click="applyValid()">申请审核
            </el-button>
            <el-button v-if="buttonData.indexOf('13') != -1" type="primary" size="small" @click="editOrder()">编辑订单</el-button>
            <el-button v-if="buttonData.indexOf('14') != -1" type="primary" size="small" @click="closeOrder()">关闭订单
            </el-button>
            <el-button v-if="buttonData.indexOf('15') != -1" type="primary" size="small" @click="auditRecord(true)">
                审核通过
            </el-button>
            <el-button v-if="buttonData.indexOf('16') != -1" type="primary" size="small" @click="auditRecord(false)">
                审核不通过
            </el-button>
            <el-button v-if="buttonData.indexOf('17') != -1" type="primary" size="small" @click="downloadFile()">
                下载合同
            </el-button>
            <el-button v-if="buttonData.indexOf('18') != -1" type="primary" size="small" @click="applyPayment()">申请付款
            </el-button>
            <el-button v-if="buttonData.indexOf('19') != -1" type="primary" size="small" @click="completeOrder()">订单完结
            </el-button>
            <el-button v-if="buttonData.indexOf('20') != -1" type="primary" size="small" @click="payApplyAudit(true)">付款审核通过
            </el-button>
            <el-button v-if="buttonData.indexOf('21') != -1" type="primary" size="small" @click="payApplyAudit(false)">付款审核不通过
            </el-button>
        </template>
        <template v-else>
            <p style="color: #909399;">直属采购费用订单仅支持在采购订单中操作！</p>
        </template>
    </div>

    <br>
    <br>
    <template v-if="buyorderExpense.validStatus!=0">
        <el-card class="box-card" shadow="never" v-if="buyorderExpense.validStatus!=0">
            <div slot="header" class="clearfix">
                <span>付款申请</span>
            </div>

            <template>
                <el-table
                        :data="payApplyDatas"
                        border
                        :header-cell-style="{textAlign: 'center'}"
                        :cell-style="{textAlign: 'center'}"
                        :row-style="{height:'30px'}"
                        style="width: 100%">
                    <el-table-column
                            label="申请金额"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ formatNum(scope.row.amount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="申请时间"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ parseTime(scope.row.addTime) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="申请人">
                        <template slot-scope="scope">
                            <span>{{scope.row.creatorName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易名称">
                        <template slot-scope="scope">
                            <span>{{scope.row.traderName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易方式">
                        <template slot-scope="scope">
                            <span>{{scope.row.traderModeStr}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="付款备注">
                        <template slot-scope="scope">
                            <span>{{scope.row.comments}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="审核状态">
                        <template slot-scope="scope">
                            <span v-if="scope.row.validStatus == 0">待审核</span>
                            <span v-if="scope.row.validStatus == 1">通过</span>
                            <span v-if="scope.row.validStatus == 2" style="color: red">审核不通过</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" fixed="right">
                        <template slot-scope="scope">
                            <el-button
                                    size="mini"
                                    type="primary" plain
                                    @click="handleSee(scope.$index, scope.row)">查看
                            </el-button>
                        </template>
                    </el-table-column>

                </el-table>
            </template>
        </el-card>
        <br>
        <br>
    </template>
    <template v-if="buyorderExpense.validStatus!=0">
        <el-card class="box-card" shadow="never" >
            <div slot="header" class="clearfix">
                <span>交易信息</span>
            </div>

            <template>
                <el-table
                        :data="capitalBillDatas"
                        border
                        :header-cell-style="{textAlign: 'center'}"
                        :cell-style="{textAlign: 'center'}"
                        :row-style="{height:'30px'}"
                        style="width: 100%">
                    <el-table-column
                            label="记账编号"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.capitalBillNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易金额"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ formatNum(scope.row.amount) }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易时间">
                        <template slot-scope="scope">
                            <span>{{parseTime(scope.row.traderTime)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="业务类型">
                        <template slot-scope="scope">
                            <span v-if="scope.row.capitalBillDetailDto.bussinessType == 525">订单付款</span>
                            <span v-if="scope.row.capitalBillDetailDto.bussinessType == 526">订单收款</span>
                            <span v-if="scope.row.capitalBillDetailDto.bussinessType == 531">退款</span>
                            <span v-if="scope.row.capitalBillDetailDto.bussinessType == 532">资金转移</span>
                            <span v-if="scope.row.capitalBillDetailDto.bussinessType == 533">信用还款</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易类型">
                        <template slot-scope="scope">
                            <span v-if="scope.row.traderType == 1">收入</span>
                            <span v-if="scope.row.traderType == 2">支出</span>
                            <span v-if="scope.row.traderType == 3">转移</span>
                            <span v-if="scope.row.traderType == 4">转入</span>
                            <span v-if="scope.row.traderType == 5">转出</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易主体">
                        <template slot-scope="scope">
                            <span v-if="scope.row.traderSubject==1">对公</span>
                            <span v-if="scope.row.traderSubject==2">对私</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易方式">
                        <template slot-scope="scope">
                            <span v-if="scope.row.traderMode == 520">支付宝</span>
                            <span v-if="scope.row.traderMode == 521">银行</span>
                            <span v-if="scope.row.traderMode == 522">微信</span>
                            <span v-if="scope.row.traderMode == 523">现金</span>
                            <span v-if="scope.row.traderMode == 527">信用支付</span>
                            <span v-if="scope.row.traderMode == 528">余额支付</span>
                            <span v-if="scope.row.traderMode == 529">退还信用</span>
                            <span v-if="scope.row.traderMode == 530">退还余额</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="付款方">
                        <template slot-scope="scope">
                            <span>{{scope.row.payer}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="收款方">
                        <template slot-scope="scope">
                            <span>{{scope.row.payee}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="交易备注">
                        <template slot-scope="scope">
                            <span>{{scope.row.comments}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="150" fixed="right">
                        <template slot-scope="scope">
                            <div v-if="(scope.row.traderType == 2 || scope.row.traderType == 5) && scope.row.bankBillId != 0 && scope.row.bankBillId != null">
                                <el-button
                                        size="mini"
                                        type="primary" plain
                                        @click="handleCapitalBill(scope.$index, scope.row)">查看
                                </el-button>
                                <el-button
                                        size="mini"
                                        type="primary" plain
                                        @click="openFile(scope.$index, scope.row)">回单
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>

                </el-table>
                <div style="text-align: center;height:35px;background: #f2f2f2">
                    <div style="padding: 6px;">
                        订单金额：{{formatNum(buyorderExpense.buyorderExpenseDetailDto.totalAmount)}}
                        订单实际金额：{{sumGoods(buyorderExpense.buyorderExpenseItemDtos)}}
                        已付款金额：{{formatNum(sumCapitalBill(capitalBillDatas))}}
                        &nbsp;&nbsp;&nbsp;&nbsp;
                        <span style="color:red">未付金额：{{formatNum(sumGoods(buyorderExpense.buyorderExpenseItemDtos) - (sumRealCapitalBill(capitalBillDatas)))}}</span>
                        &nbsp;=&nbsp;
                        订单实际金额：{{sumGoods(buyorderExpense.buyorderExpenseItemDtos)}}
                        &nbsp;-&nbsp;
                        客户实付金额：{{formatNum(sumRealCapitalBill(capitalBillDatas))}}
                    </div>
                </div>
            </template>
        </el-card>

        <br>
        <br>
    </template>

    <template v-if="buyorderExpense.validStatus!=0">
        <el-card class="box-card" shadow="never" v-if="buyorderExpense.validStatus!=0">
            <div slot="header" class="clearfix">
                <span>发票列表</span>
            </div>

            <template>
                <el-table
                        :data="invoiceDatas"
                        border
                        :header-cell-style="{textAlign: 'center'}"
                        :cell-style="{textAlign: 'center'}"
                        :row-style="{height:'30px'}"
                        style="width: 100%">
                    <el-table-column
                            label="发票号"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.invoiceNo }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="票种"
                            width="180">
                        <template slot-scope="scope">
                            <span>{{ scope.row.invoiceTypeStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="红蓝字">
                        <template slot-scope="scope">
                            <span v-if="scope.row.colorType == 1 && scope.row.colorComplementType == 0">红字有效</span>
                            <span v-if="scope.row.colorType == 1 && scope.row.colorComplementType == 1">蓝字冲销</span>
                            <span v-if="scope.row.colorType == 2 && scope.row.isEnable == 1">蓝字有效</span>
                            <span v-if="scope.row.colorType == 2 && scope.row.isEnable == 0">蓝字作废</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="发票金额">
                        <template slot-scope="scope">
                            <%--<span>{{scope.row.amount}}</span>--%>
                            <span>{{formatNum(scope.row.amount)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="操作人">
                        <template slot-scope="scope">
                            <span>{{scope.row.creatorName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="操作时间">
                        <template slot-scope="scope">
                            <span>{{parseTime(scope.row.addTime)}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="审核状态">
                        <template slot-scope="scope">
                            <span v-if="scope.row.validStatus == 0">待审核</span>
                            <span v-if="scope.row.validStatus == 1">审核通过</span>
                            <span v-if="scope.row.validStatus == 2" style="color: red">审核不通过</span>
                        </template>
                    </el-table-column>

                </el-table>
            </template>
        </el-card>
        <br>
        <br>
    </template>

    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>售后列表</span>
            <template v-if="buyorderExpense.orderType==1">
                <el-button v-if="isUser!=0&&buyorderExpense.status!=3&&buyorderExpense.status!=0&&buyorderExpense.lockedStatus==0&&doNotHaveUncofirmAndProgressAfterSaleData&&buyorderExpense.arrivalStatus==2" style="float: right; padding: 3px 0"
                           type="text" @click="addNewAfterSaleOrder()">新增售后
                </el-button>
            </template>
            <template v-else>
                <el-button v-if="isUser!=0&&buyorderExpense.status!=3&&buyorderExpense.status!=0&&buyorderExpense.lockedStatus==0&&doNotHaveUncofirmAndProgressAfterSaleData&&(buyorderExpense.buyorderPaymentStatus!=0||buyorderExpense.paymentStatus!=0)" style="float: right; padding: 3px 0"
                           type="text" @click="addNewAfterSaleOrder()">新增售后
                </el-button>
            </template>

        </div>

        <template>
            <el-table
                    :data="afterSaleData"
                    border
                    :header-cell-style="{textAlign: 'center'}"
                    :cell-style="{textAlign: 'center'}"
                    :row-style="{height:'30px'}"
                    style="width: 100%">
                <el-table-column
                        label="售后单号"
                        width="200">
                    <template slot-scope="scope">
                        <el-link type="primary" @click="viewAfterSaleDetail(scope.row.expenseAfterSalesId)">
                            {{scope.row.expenseAfterSalesNo}}
                        </el-link>
                    </template>
                </el-table-column>
                <el-table-column
                        label="售后类型"
                        width="180">
                    <template slot-scope="scope">
                        <span v-if="scope.row.expenseAfterSalesType == 4121">退货</span>
                        <span v-if="scope.row.expenseAfterSalesType == 4122">仅退票</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="创建时间">
                    <template slot-scope="scope">
                        <span >{{parseTime(scope.row.addTime)}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="创建人">
                    <template slot-scope="scope">
                        <span>{{scope.row.creatorName}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="订单状态">
                    <template slot-scope="scope">
                        <span v-if="scope.row.expenseAfterSalesStatusDto.afterSalesStatus == 0">待确认</span>
                        <span v-if="scope.row.expenseAfterSalesStatusDto.afterSalesStatus == 1">进行中</span>
                        <span v-if="scope.row.expenseAfterSalesStatusDto.afterSalesStatus == 2">已完结</span>
                        <span v-if="scope.row.expenseAfterSalesStatusDto.afterSalesStatus == 3">已关闭</span>
                    </template>
                </el-table-column>
            </el-table>

        </template>
    </el-card>
    <br>
    <br>

    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>审核记录</span>
        </div>

        <template>
            <el-table
                    :data="auditRecords"
                    border
                    :header-cell-style="{textAlign: 'center'}"
                    :cell-style="{textAlign: 'center'}"
                    :row-style="{height:'30px'}"
                    style="width: 100%"
                    >
                <el-table-column
                        label="操作人"
                        width="480">
                    <template slot-scope="scope">
                        <span>{{ scope.row.operator }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作时间"
                        width="280">
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.operationTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作事项">
                    <template slot-scope="scope">
                        <span>{{scope.row.operation}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="备注">
                    <template slot-scope="scope">
                        <span style="color: red">{{scope.row.remark}}</span>
                    </template>
                </el-table-column>
                <template slot="empty" >
                    <span v-if="buyorderExpense.isAuto==1">该订单已自动审核，无需线下人工审核</span>
                    <span v-if="buyorderExpense.isAuto!=1">暂无审核记录！</span>
                </template>
            </el-table>
        </template>
    </el-card>

    <br>
    <br>

    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span>付款审核记录</span>
        </div>

        <template>
            <el-table
                    :data="paymentAuditRecords"
                    border
                    :header-cell-style="{textAlign: 'center'}"
                    :cell-style="{textAlign: 'center'}"
                    :row-style="{height:'30px'}"
                    style="width: 100%"
                    >
                <el-table-column
                        label="操作人"
                        width="480">
                    <template slot-scope="scope">
                        <span>{{ scope.row.operator }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作时间"
                        width="280">
                    <template slot-scope="scope">
                        <span>{{ parseTime(scope.row.operationTime) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="操作事项">
                    <template slot-scope="scope">
                        <span>{{scope.row.operation}}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="备注">
                    <template slot-scope="scope">
                        <span style="color: red">{{scope.row.remark}}</span>
                    </template>
                </el-table-column>
                <template slot="empty" >
                    <span v-if="buyorderExpense.isAuto==1">该订单已自动审核，无需线下人工审核</span>
                    <span v-if="buyorderExpense.isAuto!=1">暂无审核记录！</span>
                </template>
            </el-table>
        </template>
    </el-card>

        <el-dialog title="操作确认" :visible.sync="payApplyDialogVisible" width="500px">
            <el-form :model="payApplyForm" :rules="rules" ref="form">
                <el-form-item label="备注" prop="comments">
                    <el-input v-model="payApplyForm.comments" autocomplete="off" style="width: 85%; height: 26px"></el-input>
                </el-form-item>

                <el-form-item style="text-align: center; margin-top: 10px">
                    <el-button style="background-color: #72BB72; color: #FFFFFF" size="mini" @click="submitPayApply('form')">提交</el-button>
                    <el-button style="background-color: #FFAA02; color: #FFFFFF" size="mini" @click="payApplyDialogVisible = false">取消</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
</div>

<script src="${pageContext.request.contextPath}/static/api/buyorder/buyorderExpense.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">

    const viewInfo = {
        buyorderExpenseId: '${buyorderExpenseId}',
        formToken: '${formToken}'
    };

    let vm = null
    const sendThis = (_this) => {
        vm = _this;
    };

    new Vue({
        el: '#app',
        data() {
            return {
                haveAfterSale:false,
                buyorderExpense: {
                    buyOrderDto: {},
                    buyorderExpenseDetailDto:{},
                    buyorderExpenseItemDtos:[]
                },
                afterSaleData: [],
                // 是否没有有为待确认或进行中的售后单
                doNotHaveUncofirmAndProgressAfterSaleData:false,
                contractUrl: "",
                buttonData: "",
                isUser: 0,
                auditRecords: [],
                paymentAuditRecords: [],
                payApplyDatas: [],
                capitalBillDatas: [],
                invoiceDatas: [],
                taskInfo: null,
                lastAudit: [],
                items: [
                    {
                        id: 1,
                        type: "wait",
                        title: "待确认"
                    },
                    {
                        id: 2,
                        type: "wait",
                        title: "审核中"
                    },
                    {
                        id: 3,
                        type: "wait",
                        title: "待付款"
                    },
                    {
                        id: 5,
                        type: "wait",
                        title: "待收票"
                    },
                    {
                        id: 6,
                        type: "wait",
                        title: "已完结"
                    }
                ],
                //默认展开
                isExpand: true,
                expends: [],

                rules: {
                    comments: [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                },
                payApplyForm: {
                    comments: ''
                },
                payApplyDialogVisible: false,
                payApplyTaskId: null
            };
        },

        created() {
            this.initData();
        },

        mounted() {
            loadingApp()

            sendThis(this);

        },

        methods: {
            async initData() {
                if (viewInfo.buyorderExpenseId != null && viewInfo.buyorderExpenseId != "") {
                    await getDetail(viewInfo.buyorderExpenseId).then(res => {
                        this.buyorderExpense = res.data.data;
                        this.updateItems();
                        this.setContractUrl();
                    });

                    await auditRecordData(viewInfo.buyorderExpenseId).then(res => {
                        this.auditRecords = res.data.data.auditRecordDtos;
                        this.taskInfo = res.data.data.taskInfo;
                        this.lastAudit = res.data.data.lastAudit;
                        this.payApplyTaskId = res.data.data.payApplyTaskId;
                    });

                    await afterSaleData(viewInfo.buyorderExpenseId).then(res=>{
                        this.afterSaleData = res.data.data;
                        this.checkAfterSale(this.afterSaleData)
                    })

                    await payApplyData(viewInfo.buyorderExpenseId).then(res => {
                        this.payApplyDatas = res.data.data;

                        if (this.payApplyDatas != null && this.payApplyDatas.length > 0) {

                            paymentAuditRecordData(this.payApplyDatas[0].payApplyId).then(res => {
                                this.paymentAuditRecords = res.data.data.paymentAuditRecordDtos;
                                // this.taskInfo = res.data.data.taskInfo;
                            });
                        }

                    });

                    await capitalBillData(viewInfo.buyorderExpenseId).then(res => {
                        let result = [];
                        res.data.data.forEach(item=>{
                            result.push(item);
                        })
                        this.capitalBillDatas = result;
                    });


                    await invoiceData(viewInfo.buyorderExpenseId).then(res => {
                        this.invoiceDatas = res.data.data;
                    });

                    await checkUser(viewInfo.buyorderExpenseId).then(res => {
                        this.isUser = res.data.data;
                    });

                    await getButton(viewInfo.buyorderExpenseId).then(res => {
                        this.buttonData = res.data.data;
                    });

                    this.getExpends()
                }
            },
            async addNewAfterSaleOrder() {
                await afterSaleData(viewInfo.buyorderExpenseId).then(res=>{
                    this.afterSaleData = res.data.data;
                    this.checkAfterSale(this.afterSaleData)
                    if (this.doNotHaveUncofirmAndProgressAfterSaleData) {
                        openTab("新增采购费用订单售后", '/buyorderExpense/aftersale/edit.do?buyorderExpenseId=' + viewInfo.buyorderExpenseId + '&traderId=' + this.buyorderExpense.buyorderExpenseDetailDto.traderId);
                    } else {

                    }
                })

            },
            checkAfterSale(data) {
                if (data !== null && data !== undefined && data.length > 0) {
                    let flag = true;
                    data.forEach(c => {
                        if (c.expenseAfterSalesStatusDto.afterSalesStatus == 1 || c.expenseAfterSalesStatusDto.afterSalesStatus == 0) {
                            flag = false;
                            this.haveAfterSale = true;
                        }
                    });
                    this.doNotHaveUncofirmAndProgressAfterSaleData = flag;
                } else {
                    this.doNotHaveUncofirmAndProgressAfterSaleData = true;
                }
            },
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },
            editOrder() {
                openTab("编辑采购费用订单",'/buyorderExpense/edit.do?buyorderExpenseId=' + viewInfo.buyorderExpenseId)
                this.closeThis();
            },
            seePrints() {
                openTab("打印预览", '/buyorderExpense/printContract.do?autoGenerate=false&buyorderExpenseId=' + viewInfo.buyorderExpenseId);
            },
            closeOrder() {
                index = layer.confirm("您是否确认该操作？", {
                    btn: ['确定', '取消'] //按钮
                }, function () {

                    closeBuyorderExpense(viewInfo.buyorderExpenseId).then(res => {
                        if (res.data.code == 0) {
                            self.location.reload();
                        } else {
                            layer.alert(res.data.message);
                        }

                    });

                    layer.close(index);
                }, function () {
                });

            },
            setContractUrl() {
                if (this.buyorderExpense.buyorderExpenseDetailDto.contractUrl != null && this.buyorderExpense.buyorderExpenseDetailDto.contractUrl != "") {
                    this.contractUrl = this.buyorderExpense.buyorderExpenseDetailDto.contractUrl.replace("display", "download");
                }
            },
            downloadFile() {
                window.open(this.contractUrl)
            },
            updateItems() {
                getSteps(viewInfo.buyorderExpenseId).then(res=>{
                    this.items = res.data.data;
                })
            },
            sumInvoice(data) {

                let result = 0;
                if (data == null) {
                    return result;
                }
                if (data.length === 0) {
                    return result;
                } else {
                    for (let item of data) {
                        result += item.amount;
                    }
                }
                return result;

            },
            applyPayment() {
                openTab("申请付款", "/order/buyorder/applyPayment.do?type=1&buyorderExpenseId=" + viewInfo.buyorderExpenseId)
            },
            handleSee(index, data) {
                if(this.buyorderExpense.isAuto==1){
                    openModelParam("/finance/after/autoPaymentVerify.do", "付款申请审核信息", "50%", "30%")
                }else{
                    openModelParam("/finance/after/paymentVerify.do?payApplyId=" + data.payApplyId, "付款申请审核信息", "50%", "30%")
                }

            },
            handleCapitalBill(index, data) {
                openModelParam("/finance/capitalbill/credentials.do?bankBillId=" + data.bankBillId, "电子回执单", "100%", "100%")
            },
            openFile(index, data){
                debugger
                let url = data.receiptUrl;
                if(url == null || url == ""){
                    layer.alert("暂无回单");
                }else{
                    window.open(url);
                }
            },
            auditRecord(pass) {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ["500px", "180px"],
                    title: "操作确认",
                    content: "/old/buyorderExpense/complement.do?type=3&taskId=" + this.taskInfo + "&pass=" + pass + "&buyorderExpenseId=" + this.buyorderExpense.buyorderExpenseId,
                    success: function (layero, index) {

                    }
                });
            },

            payApplyAudit(pass) {
                if (pass) {
                    this.rules.comments = [
                        {required: false, trigger: 'blur'}
                    ]
                } else {
                    this.rules.comments = [
                        {required: true, message: '请填写备注！', trigger: 'blur'},
                        {max: 256, message: '备注内容不允许超过256个字符', trigger: 'blur' }
                    ]
                }

                this.payApplyPassFlag = pass;
                this.payApplyForm.comments = '';
                this.payApplyDialogVisible = true;
            },

            submitPayApply(form) {
                this.$refs[form].validate((valid) => {
                    if (valid) {
                        let auditVariables = {
                            taskId: this.payApplyTaskId,
                            comment: this.payApplyForm.comments,
                            pass: this.payApplyPassFlag,
                            buyorderExpenseId: this.buyorderExpense.buyorderExpenseId
                        };
                        doPayApplyComplement(auditVariables).then(res => {
                            if (res.code == 0) {
                                this.$message({
                                    showClose: true,
                                    message: '付款审核成功',
                                    type: 'success'
                                });
                            } else {
                                this.$message({
                                    showClose: true,
                                    message: '付款审核失败',
                                    type: 'error'
                                });
                            }
                            this.payApplyDialogVisible = false;
                            window.location.reload();
                        });
                    } else {
                        return false;
                    }
                });
            },

            completeOrder() {
                let buyorderExpenseId = this.buyorderExpense.buyorderExpenseId;
                let that = this;
                layer.confirm("是否确认完结订单？", {
                    btn: ['确定', '取消'] //按钮
                }, function () {
                    completeOrder(buyorderExpenseId).then(res=>{
                        if (res.data.data) {
                            window.location.reload();
                        } else {
                            that.$message({
                                showClose: true,
                                message: '不符合完结条件',
                                type: 'error'
                            });
                        }
                    });
                })
            },

            applyValid() {

                console.log(this.buyorderExpense)
                let buyorderExpenseId = this.buyorderExpense.buyorderExpenseId;
                layer.confirm("是否确认提交审核？", {
                    btn: ['确定', '取消'] //按钮
                }, function () {
                    $.ajax({
                        type: "POST",
                        url: "/old/buyorderExpense/editApplyValidBuyorderExpense.do",
                        data: {"buyorderExpenseId": buyorderExpenseId, "taskId": 0},
                        dataType: 'json',
                        success: function (data) {
                            if (data.code == 0) {
                                window.location.reload();
                            } else {
                                layer.alert(data.message);
                            }
                        },
                        error: function (data) {
                            if (data.status == 1001) {
                                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                            }
                        }
                    });

                    layer.alert(data.message);
                    layer.confirm(data.message, {btn: ['确认']}, function () {
                        window.location.reload();
                    });
                    return false;

                })

            },
            viewBuyorderDetail(buyorderId) {
                openTab("采购详情", '/order/newBuyorder/newViewBuyOrderDetail.do?buyorderId=' + buyorderId);
            },

            viewAfterSaleDetail(afterSaleId) {
                openTab("采购费用售后详情", '/buyorderExpense/aftersale/detail.do?expenseAfterSalesId=' + afterSaleId);
            },
            paymentPlanSpanMethod({row, column, rowIndex, columnIndex}) {
                if (rowIndex === this.buyorderExpense.paymentPlans.length - 1) {
                    if (columnIndex === 1) {
                        return [1, 3];
                    } else if (columnIndex === 2 || columnIndex === 3) {
                        return [0, 0];
                    }

                }
            },
            formatNum(data) {
                if (data == null) {
                    data = 0;
                }
                return data.toFixed(2);
            },
            sumGoodsNum(data) {
                let result = 0;
                if (data == null) {
                    return result;
                }
                if (data.length === 0) {
                    return result;
                } else {
                    for (let item of data) {
                        result += (item.num -item.returnNum)
                    }
                }
                return result.toFixed(2);
            },
            sumGoods(data) {
                let result = 0;
                if (data == null) {
                    return result.toFixed(2);
                }
                if (data.length === 0) {
                    return result.toFixed(2);
                } else {
                    for (let item of data) {
                        result += ((item.num -item.returnNum) * item.buyorderExpenseItemDetailDto.price)
                    }
                }
                return result.toFixed(2);
            },
            sumGoodsAll(data) {
                let result = 0;
                if (data == null) {
                    return result.toFixed(2);
                }
                if (data.length === 0) {
                    return result.toFixed(2);
                } else {
                    for (let item of data) {
                        result += item.num * item.buyorderExpenseItemDetailDto.price
                    }
                }
                return result.toFixed(2);
            },
            sumCapitalBill(data) {
                let result = 0;
                if (data == null) {
                    return result;
                }
                if (data.length === 0) {
                    return result;
                } else {
                    for (let item of data) {
                        if (item.capitalBillDetailDto.bussinessType === 525) {
                            result += item.amount;
                        }
                        // 减去售后退款 包含
                        if (item.capitalBillDetailDto.bussinessType==531||item.capitalBillDetailDto.bussinessType==533) {
                            result -= Math.abs(item.amount);
                        }

                    }
                }
                return result;
            },
            sumRealCapitalBill(data) {
                let result = 0;
                if (data == null) {
                    return result;
                }
                if (data.length === 0) {
                    return result;
                } else {
                    for (let item of data) {
                        if (item.capitalBillDetailDto.bussinessType === 525) {
                            if (item.traderMode === 521 || item.traderMode === 522 || item.traderMode === 523 || item.traderMode === 528) {
                                result += item.amount;
                            }
                        }
                        if (item.capitalBillDetailDto.bussinessType === 533) {
                            if (item.traderMode === 529) {
                                // 售后的信用退款不管
                                // result -= Math.abs(item.amount);
                            } else {
                                // 正向实际付款的抵消账期的加上
                                result += item.amount;
                            }

                        }
                        if (item.capitalBillDetailDto.bussinessType === 531) {
                            result -= Math.abs(item.amount);
                        }


                    }
                }
                return result;
            },
            splitStrAddAllipsis(str,len) {
                if (str != ''&&str!=null&&str!=undefined) {
                    if (str.length>len) {
                        return str.substring(0, len) + '...';
                    }
                }

                return str;
            },
            isSplitStrAddAllipsis(str, len) {
                if (str != ''&&str!=null&&str!=undefined) {
                    if (str.length>len) {
                        return true;
                    }
                }
                return false;
            },
            parseOrderDesc(data){
                return parseBuyOrderExpenseOrderDesc(data)
            },
            // 查看供应商信息
            viewTraderSupplier(id) {
                openTab("供应商信息", '/trader/supplier/baseinfo.do?traderId=' + id);
            },
            // 查看商品信息
            viewSkuInfo(id) {
                openTab("商品信息", '/goods/goods/viewbaseinfo.do?goodsId=' + id);
            },
            // 查看销售单
            viewSaleOrder(row) {
                if(row.orderType != 2){
                    openTab("销售详情", '/orderstream/saleorder/detail.do?saleOrderId='+ row.saleorderId +'&scene=0');
                }
                if(row.orderType == 2){
                    openTab("备货详情", '/order/saleorder/viewBhSaleorder.do?saleorderId='+ row.saleorderId);
                }
            },
            viewExpenseDetail(id) {
                openTab("采购费用详情", '/buyorderExpense/details.do?buyorderExpenseId=' + id);
            },
            viewSaleOrderInfo(id){
                openTab("销售详情", '/orderstream/saleorder/detail.do?saleOrderId=' + id +'&scene=0')
            },

            toggleRowExpansion() {
                this.isExpand = !this.isExpand;
                if (this.isExpand) {
                    this.getExpends()
                } else {
                    this.expends = []
                }
            },

            getRowClass(row) {
                if (row.row.buyOrderSaleOrderGoodsDetailDtos == null || row.row.buyOrderSaleOrderGoodsDetailDtos == undefined  || row.row.buyOrderSaleOrderGoodsDetailDtos.length === 0) {
                    return 'row-expand-cover'
                }
            },

            getExpends() {
                let ids = []
                this.buyorderExpense.buyorderExpenseItemDtos.map(item=>{
                    if (item.buyOrderSaleOrderGoodsDetailDtos !== null
                        && item.buyOrderSaleOrderGoodsDetailDtos !== undefined
                        && item.buyOrderSaleOrderGoodsDetailDtos.length > 0) {
                        ids.push(item.buyorderExpenseItemId)
                    }
                })
                this.expends = ids;
            },
            getRowKeys(row) {
                return row.buyorderExpenseItemId
            }
        }

    });


</script>

<style>
    .my-content {
        width: 30%;
    }

    .my-text-align {
        text-align: center !important;
    }

    .my-label {
        width: 20%;
    }

    .el-descriptions__title {
        font-size: 16px;
        /*font-weight: 700;*/
    }

    .el-descriptions__header {
        background-color: #f2f2f2;
        margin-bottom: 0px;
        height: 35px;
    }

    .el-card .el-card__header {
        padding: 7px 10px;
        background-color: #f2f2f2;
        font-size: 16px;
        height: 35px;
        font-weight: 700;
    }

    .el-card__body {
        padding: 0px;
    }

    .el-step__head.is-success {
        color: #409EFF;
        border-color: #409EFF;
    }
    .el-step__title.is-success {
        color: #409EFF;
    }
    .el-step__description.is-success {
        color: #409EFF;
    }
    .order-desc-style{
        color: #66b1ff;
    }

    .row-expand-cover td .el-table__expand-icon{ visibility: hidden !important; }
</style>

