package com.vedeng.erp.settlement.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.dto.AlipayReceiptDto;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.BankBillQueryDto;
import com.vedeng.erp.finance.dto.HongRuiBankBillQueryDto;
import com.vedeng.erp.finance.service.HongRuiBankBillApiService;
import com.vedeng.erp.settlement.domain.entity.BankBillEntity;
import com.vedeng.erp.settlement.mapper.HongRuiBankBillMapper;
import com.vedeng.erp.settlement.mapstruct.BankBillConvertor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 服务实现类
 * @date 2022/9/6 9:39
 **/
@Service
@Slf4j
public class HongRuiBankBillApiServiceImpl implements HongRuiBankBillApiService {

    @Autowired
    private HongRuiBankBillMapper hongRuiBankBillMapper;

    @Autowired
    private BankBillConvertor bankBillConvertor;

    @Override
    public void updateBankBillReceipt(AlipayReceiptDto alipayReceiptDto) {

        if (alipayReceiptDto == null || StringUtils.isEmpty(alipayReceiptDto.getTranFlow())) {
            throw new ServiceException("流水号不可为空");
        }
        BankBillEntity bankBillEntity = hongRuiBankBillMapper.selectByTranFlow(alipayReceiptDto.getTranFlow(), 4);
        if (bankBillEntity == null) {
            throw new ServiceException("未查到支付宝流水号");
        }
        BankBillEntity update = new BankBillEntity();
        update.setBankBillId(bankBillEntity.getBankBillId());
        update.setReceiptUrl(alipayReceiptDto.getReceiptUrl());

        log.info("前台支付宝更新回单：{}", JSON.toJSON(update));
        hongRuiBankBillMapper.updateByPrimaryKeySelective(update);
    }

    @Override
    public void add(BankBillDto bankBillDto) {
        BankBillEntity bankBillEntity = bankBillConvertor.toEntity(bankBillDto);
        BankBillEntity bankBillExist = hongRuiBankBillMapper.selectByTranFlow(bankBillDto.getTranFlow(),bankBillDto.getBankTag());
        log.info("获取到金蝶保存银行流水信息{},查询到数据库已有流水{},",JSON.toJSONString(bankBillEntity),JSON.toJSONString(bankBillExist));
        if(bankBillExist != null){
            bankBillExist.setAccBankno(bankBillDto.getAccBankno());
            bankBillExist.setMatchedAmount(bankBillDto.getMatchedAmount());
            bankBillExist.setMatchedObject(bankBillDto.getMatchedObject());
//            VDERP-12561不需要金蝶的回单信息start
//            bankBillExist.setReceiptUrl(bankBillDto.getReceiptUrl());
//            VDERP-12561不需要金蝶的回单信息end
            hongRuiBankBillMapper.updateByPrimaryKeySelective(bankBillExist);
            bankBillDto.setBankBillId(bankBillExist.getBankBillId());
        }else {
            hongRuiBankBillMapper.insertSelective(bankBillEntity);
            bankBillDto.setBankBillId(bankBillEntity.getBankBillId());
        }
    }

    @Override
    public void update(BankBillDto bankBillDto) {
        BankBillEntity bankBillEntity = bankBillConvertor.toEntity(bankBillDto);
        hongRuiBankBillMapper.updateByPrimaryKeySelective(bankBillEntity);
    }

    @Override
    public List<BankBillDto> getAliPayBillInfo() {
        return hongRuiBankBillMapper.getAliPayBillNotPushedToKingDee();
    }

    @Override
    public List<BankBillDto> getByTranFlowLike(String flowNo) {
        return hongRuiBankBillMapper.getByTranFlowLike(flowNo);
    }



    @Override
    public boolean checkTranFlow(String tranFlow, Integer bankTag) {
        List<BankBillEntity> byTranFlowAndBankTag = hongRuiBankBillMapper.findByTranFlowAndBankTag(tranFlow, bankTag);
        return CollUtil.isNotEmpty(byTranFlowAndBankTag);
    }

    @Override
    public BankBillDto checkTranFlowForCharge(String tranFlow, Integer bankTag){
        List<BankBillEntity> byTranFlowAndBankTag = hongRuiBankBillMapper.findByTranFlowAndBankTag(tranFlow, bankTag);
        return CollUtil.isNotEmpty(byTranFlowAndBankTag)?bankBillConvertor.toDto(byTranFlowAndBankTag.get(0)):null;
    }

    @Override
    public void updateTranFlowForCharge(String tranFlow, Integer bankTag, String newTranFlow){
        hongRuiBankBillMapper.updateTranFlowForCharge(tranFlow,bankTag,newTranFlow);
    }


    @Override
    public List<BankBillDto> getByTranFlowAndBankTag(String tranFlow, Integer bankTag) {
        return hongRuiBankBillMapper.getByTranFlowAndBankTag(tranFlow, bankTag);
    }

    @Override
    public BankBillDto getBankBillByBankBillId(Integer bankBillId) {
        BankBillEntity bankBillEntity = hongRuiBankBillMapper.selectByPrimaryKey(bankBillId);
        return bankBillConvertor.toDto(bankBillEntity);
    }

    @Override
    public List<BankBillDto> getBankBillByDateAndCompany(Date startDate, Date endDate, String name) {
        return hongRuiBankBillMapper.findByAccName1AndTrandateBetween(name,startDate,endDate);
    }

    @Override
    public List<BankBillDto> queryRemainBankBill(String tranFlow) {
        return hongRuiBankBillMapper.queryRemainBankBill(tranFlow);
    }

    @Override
    public List<BankBillDto> querySKBankBillInfo(String tranFlow) {
        return hongRuiBankBillMapper.querySKBankBillInfo(tranFlow);
    }

//    @Override
//    public String matchVoucherNo(BankBillDto dto) {
//        log.info("流水列表匹配云星空凭证号,流水号:{}",dto.getTranFlow());
//        List<TransactionVoucherEntity> transactionVoucherList = transactionVoucherMapper.findByRelateIdAndSource(dto.getBankBillId(), ErpConstant.ONE);
//        log.info("流水列表匹配云星空凭证号,通过bankBillId匹配,匹配参数:{},匹配结果:{}",dto.getBankBillId(), JSONUtil.toJsonStr(transactionVoucherList));
//        if (CollUtil.isEmpty(transactionVoucherList)) {
//            log.info("流水列表匹配云星空凭证号,通过bankBillId匹配,匹配参数:{},未匹配到结果", dto.getBankBillId());
//            return null;
//        }
//        return transactionVoucherList.stream().map(TransactionVoucherEntity::getVoucherNo).collect(Collectors.joining(","));
//    }

    @Override
    public PageInfo<BankBillDto> queryConstructionBankBill(PageParam<BankBillQueryDto> page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> hongRuiBankBillMapper.queryConstructionBankBill(page.getParam()));
    }

    @Override
    public PageInfo<BankBillDto> queryHongRuiBankBill(PageParam<HongRuiBankBillQueryDto> page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> hongRuiBankBillMapper.queryHongRuiBankBill(page.getParam()));
    }




    @Override
    public BankBillDto checkConstructionBankBill(BankBillDto page) {
        return hongRuiBankBillMapper.checkConstructionBankBill(page);
    }

    @Override
    public PageInfo<BankBillDto> queryIgnoreBankBill(PageParam<BankBillQueryDto> page) {
        return PageHelper.startPage(page.getPageNum(), page.getPageSize()).doSelectPageInfo(() -> hongRuiBankBillMapper.queryIgnoreBankBill(page.getParam()));
    }

    @Override
    public BankBillDto checkIgnoreBankBill(Integer bankBillId) {
        return  hongRuiBankBillMapper.checkIgnoreBankBill(bankBillId);
    }

    @Override
    public int countSpecialSerialNumbers() {
        return hongRuiBankBillMapper.countSpecialSerialNumbers();
    }

    @Override
    public BankBillDto selectTranFlowForChargeLastOne(Integer bankTag){
        return hongRuiBankBillMapper.selectTranFlowForChargeLastOne(bankTag);
    }
}
