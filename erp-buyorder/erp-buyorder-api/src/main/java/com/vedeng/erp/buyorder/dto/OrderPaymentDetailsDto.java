package com.vedeng.erp.buyorder.dto;

import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 订单付款详情表
 */
@Data
public class OrderPaymentDetailsDto {
    private Long tOrderPaymentDetailsId;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者ID
    */
    private Integer creator;

    /**
    * 修改者ID
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;

    /**
    * 业务订单id
    */
    private Integer orderId;

    /**
    * 单据类型 0采购单 1销售单 2售后单
    */
    private Integer orderType;

    /**
    * 付款方式类型 520.支付宝 521.银行 522.微信 523.现金 527.信用支付 528.余额支付 10000.返利 10001.银行承兑汇票
    */
    private Integer paymentMethodType;
}