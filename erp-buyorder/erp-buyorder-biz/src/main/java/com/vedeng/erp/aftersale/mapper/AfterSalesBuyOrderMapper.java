package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AfterSalesBuyOrderMapper {
    int deleteByPrimaryKey(Integer afterSalesId);

    int insert(BuyOrderAfterSalesEntity record);

    int insertSelective(BuyOrderAfterSalesEntity record);

    BuyOrderAfterSalesEntity selectByPrimaryKey(Integer afterSalesId);

    int updateByPrimaryKeySelective(BuyOrderAfterSalesEntity record);

    int updateByPrimaryKey(BuyOrderAfterSalesEntity record);

    int updateBatchSelective(List<BuyOrderAfterSalesEntity> list);

    List<BuyOrderAfterSalesEntity> findByOrderId(Integer orderId);





}