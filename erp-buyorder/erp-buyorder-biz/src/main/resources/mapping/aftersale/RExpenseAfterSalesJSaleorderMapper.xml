<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.RExpenseAfterSalesJSaleorderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.RExpenseAfterSalesJSaleorderEntity">
    <!--@mbg.generated-->
    <!--@Table T_R_EXPENSE_AFTER_SALES_J_SALEORDER-->
    <id column="T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID" jdbcType="INTEGER" property="tRExpenseAfterSalesJSaleorderId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="SALEORDER_ID" jdbcType="INTEGER" property="saleorderId" />
    <result column="SALEORDER_GOODS_ID" jdbcType="INTEGER" property="saleorderGoodsId" />
    <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
    <result column="EXPENSE_AFTER_SALES_ITEM_ID" jdbcType="BIGINT" property="expenseAfterSalesItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="AFTER_SALES_NUM" jdbcType="INTEGER" property="afterSalesNum" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, SALEORDER_ID, SALEORDER_GOODS_ID, EXPENSE_AFTER_SALES_ID, EXPENSE_AFTER_SALES_ITEM_ID, 
    SKU_ID, SKU_NO, AFTER_SALES_NUM
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID" keyProperty="tRExpenseAfterSalesJSaleorderId" parameterType="com.vedeng.erp.aftersale.domain.entity.RExpenseAfterSalesJSaleorderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPENSE_AFTER_SALES_J_SALEORDER (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      SALEORDER_ID, SALEORDER_GOODS_ID, EXPENSE_AFTER_SALES_ID, 
      EXPENSE_AFTER_SALES_ITEM_ID, SKU_ID, SKU_NO, 
      AFTER_SALES_NUM)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{saleorderId,jdbcType=INTEGER}, #{saleorderGoodsId,jdbcType=INTEGER}, #{expenseAfterSalesId,jdbcType=BIGINT},
      #{expenseAfterSalesItemId,jdbcType=BIGINT}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR},
      #{afterSalesNum,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID" keyProperty="tRExpenseAfterSalesJSaleorderId" parameterType="com.vedeng.erp.aftersale.domain.entity.RExpenseAfterSalesJSaleorderEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID,
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID,
      </if>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID,
      </if>
      <if test="expenseAfterSalesItemId != null">
        EXPENSE_AFTER_SALES_ITEM_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="afterSalesNum != null">
        AFTER_SALES_NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="expenseAfterSalesId != null">
        #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="expenseAfterSalesItemId != null">
        #{expenseAfterSalesItemId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSalesNum != null">
        #{afterSalesNum,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.RExpenseAfterSalesJSaleorderEntity">
    <!--@mbg.generated-->
    update T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="saleorderId != null">
        SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null">
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="expenseAfterSalesId != null">
        EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      </if>
      <if test="expenseAfterSalesItemId != null">
        EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="afterSalesNum != null">
        AFTER_SALES_NUM = #{afterSalesNum,jdbcType=INTEGER},
      </if>
    </set>
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.RExpenseAfterSalesJSaleorderEntity">
    <!--@mbg.generated-->
    update T_R_EXPENSE_AFTER_SALES_J_SALEORDER
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      SALEORDER_ID = #{saleorderId,jdbcType=INTEGER},
      SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT},
      EXPENSE_AFTER_SALES_ITEM_ID = #{expenseAfterSalesItemId,jdbcType=BIGINT},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      AFTER_SALES_NUM = #{afterSalesNum,jdbcType=INTEGER}
    where T_R_EXPENSE_AFTER_SALES_J_SALEORDER_ID = #{tRExpenseAfterSalesJSaleorderId,jdbcType=INTEGER}
  </update>


  <select id="selectByBuyorderExpenseId" resultType="com.vedeng.erp.aftersale.dto.RExpenseAfterSalesJSaleorderDto">
    select TEASI.BUYORDER_EXPENSE_ITEM_ID,TREASJS.SALEORDER_ID,TREASJS.SALEORDER_GOODS_ID, SUM(TREASJS.AFTER_SALES_NUM) afterSalesNum
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS
           left join T_EXPENSE_AFTER_SALES_ITEM TEASI on TREASJS.EXPENSE_AFTER_SALES_ITEM_ID = TEASI.EXPENSE_AFTER_SALES_ITEM_ID
           left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEASI.EXPENSE_AFTER_SALES_ID = TEASS.EXPENSE_AFTER_SALES_ID
        left join T_EXPENSE_AFTER_SALES TEAS on TEASS.EXPENSE_AFTER_SALES_ID = TEAS.EXPENSE_AFTER_SALES_ID
    where TEASS.AFTER_SALES_STATUS = 2
      and TEASS.IS_DELETE = 0
      and TEASI.IS_DELETE = 0
      and TEAS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    group by TEASI.BUYORDER_EXPENSE_ITEM_ID, TREASJS.SALEORDER_ID,TREASJS.SALEORDER_GOODS_ID
    </select>
  <select id="getReturnNumByBuyorderExpenseId"
          resultType="com.vedeng.erp.aftersale.dto.RExpenseAfterSalesJSaleorderDto">
    select TEAS.EXPENSE_AFTER_SALES_ID expenseAfterSalesId,
           TREASJS.SALEORDER_ID saleorderId,
           TREASJS.SALEORDER_GOODS_ID saleorderGoodsId,
           TREASJS.SKU_ID skuId,
           TREASJS.SKU_NO skuNo,
           SUM(TREASJS.AFTER_SALES_NUM) afterSalesNum
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS
           left join T_EXPENSE_AFTER_SALES TEAS on TREASJS.EXPENSE_AFTER_SALES_ID = TEAS.EXPENSE_AFTER_SALES_ID
           left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEAS.EXPENSE_AFTER_SALES_ID = TEASS.EXPENSE_AFTER_SALES_ID
           left join T_SALEORDER TS ON TREASJS.SALEORDER_ID=TS.SALEORDER_ID
    where TEASS.AFTER_SALES_STATUS = 2
      and TEASS.IS_DELETE = 0
      and TEAS.IS_DELETE = 0
      and TEAS.EXPENSE_AFTER_SALES_TYPE = 4121
      and TEAS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
    group by TREASJS.SALEORDER_ID, TREASJS.SALEORDER_GOODS_ID,TS.SATISFY_DELIVERY_TIME
    order by TS.SATISFY_DELIVERY_TIME
  </select>
  <select id="getReturnNumByExpenseAfterSalesId"
          resultType="com.vedeng.erp.aftersale.dto.RExpenseAfterSalesJSaleorderDto" parameterType="com.vedeng.erp.aftersale.dto.RExpenseAfterSalesJSaleorderDto">
    select TREASJS.SALEORDER_ID         saleorderId,
           TREASJS.SALEORDER_GOODS_ID   saleorderGoodsId,
           TREASJS.SKU_ID               skuId,
           TREASJS.SKU_NO               skuNo,
           SUM(TREASJS.AFTER_SALES_NUM) afterSalesNum
    from T_R_EXPENSE_AFTER_SALES_J_SALEORDER TREASJS
    where TREASJS.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=INTEGER}
      and TREASJS.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
      and TREASJS.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
      limit 1
  </select>

  <!--auto generated by MybatisCodeHelper on 2023-01-31-->
  <delete id="deleteByExpenseAfterSalesId">
        delete from T_R_EXPENSE_AFTER_SALES_J_SALEORDER
        where EXPENSE_AFTER_SALES_ID=#{expenseAfterSalesId,jdbcType=BIGINT}
    </delete>
</mapper>