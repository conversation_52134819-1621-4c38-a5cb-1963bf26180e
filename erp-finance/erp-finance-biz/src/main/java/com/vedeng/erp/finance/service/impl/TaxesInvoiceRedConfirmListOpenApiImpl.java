package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceRedConfirmListResponseDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.Objects;

/**
 * 红字确认单列表
 */
@Service
public class TaxesInvoiceRedConfirmListOpenApiImpl extends AbstractTaxesOpenApiHandler {

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceRedConfirmListResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceRedConfirmListResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult = new SaleInvoiceRedConfirmListResponseDto();
        }
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(StrUtil.isNotEmpty(return_info.getReturn_message()) ? return_info.getReturn_message() : TaxesReturnCodeEnum.getMsg(return_info.getReturn_code()));
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }
        return taxesResult;

    }
}
