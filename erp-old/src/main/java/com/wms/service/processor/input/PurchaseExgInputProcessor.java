package com.wms.service.processor.input;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购售后->换货入库 处理器
 */
@Service
public class PurchaseExgInputProcessor extends AbstractInputOrderProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseExgInputProcessor.class);

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    @Qualifier("purchaseExgInputCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Override
    protected void commonValidator(InputOrderDto inputOrderDto) throws Exception {

        if(!WmsInterfaceOrderType.INPUT_PURCHASE_EXG.equals(inputOrderDto.getASNType())){
            throw new Exception("入库单:"+inputOrderDto.getASNReference1()+"的类型错误!");
        }

    }

    @Override
    protected void updateOrderData(InputOrderDto inputOrderDto) {

        String afterSaleOrderNo = inputOrderDto.getASNReference1();

        AfterSales afterSale = afterSalesMapper.getAfterSalesByNo(afterSaleOrderNo);

        List<AfterSalesGoodsVo> afterSaleGoodList =
                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSale.getAfterSalesId());

//        ThreadLocalContext.put("afterSaleGoodList",afterSaleGoodList);

        List<InputOrderGoodsDto> details = inputOrderDto.getDetails();

        details.stream().forEach(inputOrderGoodsDto -> {

            AfterSalesGoodsVo dbAfterSaleGood = getRelateAfterSaleGood(getRelateId(inputOrderDto,inputOrderGoodsDto),afterSaleGoodList);

            AfterSalesGoods updateGood = new AfterSalesGoods();
            updateGood.setAfterSalesGoodsId(dbAfterSaleGood.getAfterSalesGoodsId());
            updateGood.setArrivalNum(dbAfterSaleGood.getExchangeReturnNum() + inputOrderGoodsDto.getReceivedQty().intValue());
            updateGood.setArrivalTime(System.currentTimeMillis());

            //2-全部收货 1-部分收货
            updateGood.setArrivalStatus(updateGood.getArrivalNum().equals(dbAfterSaleGood.getNum()) ? 2 : 1);

            LOGGER.info("采购换货入库，更新换货售后单的数据:" + JSON.toJSONString(updateGood));

            afterSalesGoodsMapper.updateByPrimaryKeySelective(updateGood);

        });

    }

    private AfterSalesGoodsVo getRelateAfterSaleGood(Integer afterSaleGoodsId,List<AfterSalesGoodsVo> afterSaleGoodList){

        return afterSaleGoodList
                .stream()
                .filter(afterSalesGood -> {
                    return afterSalesGood.getAfterSalesGoodsId().equals(afterSaleGoodsId);
                })
                //.map(afterSalesGood -> afterSalesGood.getAfterSalesGoodsId())
                .findFirst()
                .orElse(null);
    }


    @Override
    protected int getOperateType(InputOrderDto inputOrderDto) {
        return StockOperateTypeConst.BUYORDER_WAREHOUSE_CHANGE_IN;
    }

    @Override
    protected int getWmsLogicalOperateType(InputOrderDto inputOrderDto) {
        return WmsLogicalOperateTypeEnum.PURCHASE_EXG.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(InputOrderDto orderDto,InputOrderGoodsDto goodsDto) {
        return Integer.valueOf(goodsDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 2;
    }
}
