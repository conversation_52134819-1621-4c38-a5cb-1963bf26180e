<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="确认收货" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<link rel="stylesheet" href="<%=basePath%>static/css/qrcode.css?rnd=${resourceVersionKey}" />
<script type="text/javascript" src='<%= basePath %>static/js/order/buyorder/confirm_arrival_init.js?rnd=${resourceVersionKey}'></script>
	<div class="content mt10 ">
		<form method="post" id="" action="" >

			<%--订单流 区分页面跳转--%>
			<input type="hidden" name="isNew" id="isNew" value="${isNew}">

        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">产品信息</div>
            </div>
            <c:set var="num1" value="0"></c:set>
	            <table class="table table-style8" id="arrival_table">
	                <thead>
	                    <tr>
							<th class="wid5"> <input type="checkbox" name="allcheck" onclick="selectall(this)"><label class="mr10 mt4">全选</label></th>
	                    	<th class="wid4">序号</th>
							<th class="wid12">快递单号</th>
	                        <th class="wid19">产品名称</th>
							<th class="wid8">品牌</th>
							<th class="wid8">型号</th>
							<th class="wid9">物料编码</th>
							<th class="wid8">采购数量</th>
							<th class="wid6">单位</th>
							<th class="wid10">单价</th>
							<th class="wid8">总额</th>
							<th class="wid20">采购备注</th>
							<th class="wid15">本次到货数量/全部未到货数量</th>
							<th class="wid20">备注</th>
	                    </tr>
	                </thead>
	                <tbody>
					<c:forEach var="bgv" items="${buyorderVo.buyorderGoodsVoList}" varStatus="num">
						<c:if test="${bgv.isDelete eq 0}">
							<c:set var="num1" value="${num1+1}"></c:set>
							<tr>
								<td><input type="checkbox" name="b_checkbox" autocomplete="off"  onclick="clickOne('b_checkbox')" value="${bgv.buyorderGoodsId}"/></td>

								<td>${num1}</td>
								<td><input id="curExpressId" type="text" name="curExpressId" hidden value="${bgv.expressId}"/>
										${bgv.logisticsNo}</td>
								<td class="text-left">
									<div class="customername pos_rel">
		                    		<span class="font-blue cursor-pointer addtitle"
										  tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
		                    				"link":"./goods/goods/viewbaseinfo.do?goodsId=${bgv.goodsId}","title":"产品信息"}'>${bgv.goodsName}
		                    				&nbsp;<i class="iconbluemouth contorlIcon"></i><br/></span>
										<div class="pos_abs customernameshow">
											物料编码： ${bgv.materialCode}<br>
											注册证号： ${bgv.registrationNumber}<br>
											管理类别： ${bgv.manageCategoryName}<br>
											产品归属：<c:if test="${not empty bgv.userList }">
											<c:forEach items="${bgv.userList }" var="user"
													   varStatus="st">
												${user.username } <c:if
													test="${st.count != bgv.userList.size() }">、</c:if>
											</c:forEach>
										</c:if> <br>
											库存：${bgv.goodsStock}<br>
											可用库存：${bgv.canUseGoodsStock > 0 ? bgv.canUseGoodsStock : 0}<br>
											订单占用：${bgv.orderOccupy}<br>
										</div>
										<div>${bgv.sku}</div>
									</div>
								</td>
								<td>${bgv.brandName}</td>
								<td>${bgv.model}</td>
								<td>${bgv.materialCode}</td>
								<td><span alt="${bgv.goodsId}">${bgv.num-bgv.afterSaleUpLimitNum}</span>
									<input type="hidden" name="buySum" alt="${bgv.buyorderGoodsId}"
										   value="${bgv.buyorderGoodsId}|${bgv.num-bgv.afterSaleUpLimitNum}"/>
								</td>
								<td>${bgv.unitName}</td>
								<td>${bgv.price}</td>
								<td>
									<span class="oneAllMoney"
										  alt="${bgv.buyorderGoodsId}">${bgv.oneBuyorderGoodsAmount}</span>
								</td>
								<td>${bgv.insideComments}</td>
								<td>
									<input style="width:40px; height:30px;text-align:center;" class="input-middle"
										   type="text"
										   name="arrivalNum"/>/${bgv.currentDeliveryNum}
									<input type="hidden" name="totalUnArrivalNum" value= "${bgv.currentDeliveryNum}" >
								</td>
								<td><input id="nonAllArrivalReason"
										   style="height:30px;text-align:center;"
										   class="input-middle"
										   type="text"
										   placeholder="本次到货数量＜全部未到货数量时必填"
										   name="nonAllArrivalReason"
								           value=""/></td>
							</tr>
						</c:if>
					</c:forEach>

	                </tbody>
	            </table>
<%--	        </c:forEach>--%>
        </div>

        <div class="add-tijiao tcenter">
			<button type="button" id="apply_all_payment_submit"  class="bg-light-blue" name="all_arrival" value="all_arrival">全部收货</button>
			&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        	<input type="hidden" name="buyorderId" id="buyorderId" value="${buyorderVo.buyorderId}">
        	<input type="hidden" name="buyorderGoodsListSize" id="buyorderGoodsListSize" value="${buyorderVo.buyorderGoodsVoList.size()}">
            <button type="button" id="apply_payment_submit"  name="tijiao" >部分收货</button>
        </div>
        </form>
    </div>
<%@ include file="../../common/footer.jsp"%>