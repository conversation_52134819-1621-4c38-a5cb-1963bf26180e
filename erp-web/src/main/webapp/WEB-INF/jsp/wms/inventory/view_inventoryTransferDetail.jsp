<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date" %>
<%@taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>

<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>查看库存转移单信息</title>
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet"
          href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">
    <style>
        .detail-block {
            position: relative;
        }
        .mytitle {
            background: #4169E1;
            color: white
        }
    </style>
</head>

<body>

<div class="detail-wrap">
    <div class="detail-block">
        <div class="block-title mytitle">
            基本信息
        </div>
        <div class="goodDetails J-toggle-show-cnt">
            <div class="detail-table">
                <div class="table-item">
                    <div class="table-th">库存转移单单号：</div>
                    <div class="table-td">
                        ${inventoryTransferVO.inventoryTransferNo}
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">创建时间：</div>
                    <div class="table-td">
                        <div class="info-pic">
                            <date:date value="${inventoryTransferVO.addTime}"/>
                        </div>
                    </div>
                </div>

                <div class="table-item">
                    <div class="table-th">单据类型：</div>
                    <div class="table-td">
                        ${inventoryTransferVO.typeStr}
                    </div>
                </div>
                <div class="table-item">
                    <div class="table-th">转移单状态：</div>
                    <div class="table-td">
                        ${inventoryTransferVO.statusStr}
                    </div>
                </div>
                <div class="table-item item-col">
                    <div class="table-th">转移单原因描述：</div>
                    <div class="table-td">
                        ${inventoryTransferVO.reasons}
                    </div>
                </div>
                <div class="table-item item-col">
                    <div class="table-th">备注：</div>
                    <div class="table-td">
                        ${inventoryTransferVO.comments}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="detail-block block-nohidden">
        <div class="block-title mytitle">转移信息</div>
        <div class="goodDetails J-toggle-show-cnt">
            <div class="detail-table">
                <table class="table table-base">
                    <tbody>
                    <tr>
                        <th>序号</th>
                        <th>订货号</th>
                        <th>商品名称</th>
                        <th>数量</th>
                        <th>来源逻辑仓</th>
                        <th>目标逻辑仓</th>
                        <th>成功转移数量</th>
                    </tr>
                    <c:if test="${not empty inventoryTransferDetailVos}">
                        <c:forEach items="${inventoryTransferDetailVos}" var="inventoryTransferDetailVo"
                                   varStatus="status">
                            <tr>
                                <td>
                                        ${status.count}
                                </td>
                                <td>
                                        ${inventoryTransferDetailVo.skuNo}
                                </td>
                                <td>
                                        ${inventoryTransferDetailVo.goodsName}
                                </td>
                                <td>
                                        ${inventoryTransferDetailVo.num}
                                </td>
                                <td>
                                        ${inventoryTransferDetailVo.fromWarehoseStr}
                                </td>
                                <td>
                                        ${inventoryTransferDetailVo.toWarehouseStr}
                                </td>
                                <td>
                                        ${inventoryTransferDetailVo.transferNum}
                                </td>
                            </tr>
                        </c:forEach>
                    </c:if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>

</body>