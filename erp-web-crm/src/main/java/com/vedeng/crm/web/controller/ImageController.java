package com.vedeng.crm.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.imageio.ImageIO;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.Color;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;


/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/24
 */
@Controller
@RequestMapping("/crm/other/profile")
public class ImageController {

    @NoNeedAccessAuthorization
    @GetMapping(value = "/pixel", produces = MediaType.IMAGE_PNG_VALUE)
    public byte[] getPixelImage() throws IOException {
        // 创建一个1x1像素的BufferedImage
        BufferedImage image = new BufferedImage(1, 1, BufferedImage.TYPE_INT_RGB);

        // 设置像素颜色为黑色
        image.setRGB(0, 0, Color.BLACK.getRGB());

        // 将图像写入ByteArrayOutputStream
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(image, "png", baos);

        // 返回字节数组
        return baos.toByteArray();
    }

    @ResponseBody
    @NoNeedAccessAuthorization
    @GetMapping(value = "/getContentForLoginLxcrm")
    public String getContent(HttpServletRequest request, HttpServletResponse response) throws IOException {
        response.setHeader("Access-Control-Allow-Origin", request.getHeader("Origin"));
        response.setHeader("Access-Control-Allow-Credentials", "true");

        response.setHeader("Access-Control-Allow-Methods", "GET, HEAD, POST, PUT, DELETE, TRACE, OPTIONS, PATCH");
        response.setHeader("Access-Control-Allow-Headers", "Authorization,Content-Type,X-Requested-With,version,gomanager,token,formtoken,source");
        response.setHeader("Access-Control-Max-Age", "3600");
        // 删除 lxCrmShiroSessionId cookie
        Cookie cookie = new Cookie("lxCrmShiroSessionId", null);
        cookie.setPath("/");  // 设置与要删除的 cookie 相同的路径
        cookie.setMaxAge(0);  // 设置最大年龄为 0，表示删除该 cookie
        response.addCookie(cookie);
        return "<script type=\"text/javascript\" >console.log('lxcrm load success')</script>";
    }
}
