package com.wms.service.chain.step;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.wms.service.chain.AbstractHandlerStep;
import com.wms.service.chain.HandlerStepContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 处理虚拟处理器
 */
@Service
public class SpecialDeliveryVirtualHandler extends AbstractHandlerStep {

    private static Logger logger = LoggerFactory.getLogger(SpecialDeliveryVirtualHandler.class);

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Override
    protected void doDealWith(HandlerStepContext handlerStepContext) throws Exception {

        Saleorder saleorder = handlerStepContext.get("saleorder");
        List<SaleorderGoodsVo> saleorderGoodsVoList = handlerStepContext.get("saleorderGoodsVoList");

        handleVirtualGoods(saleorderGoodsVoList);
    }

    /**
     *  销售订单中，只要订单全部付款或部分付款，则订单中的虚拟商品自动全部发货销售订单中，只要订单全部付款或部分付款，则订单中的虚拟商品自动全部发货
     * @param saleOrderGoodsVoList
     * @return 销售订单中包含特殊商品
     */
    private void handleVirtualGoods(List<SaleorderGoodsVo> saleOrderGoodsVoList) {
        try {
            if (saleOrderGoodsVoList.size() <= 0) {
                return ;
            }
            long logTime = System.currentTimeMillis();

            int countVirtual = 0;
            for (SaleorderGoodsVo saleorderGoodsVo : saleOrderGoodsVoList) {
                if (GoodsConstants.VIRTUAL_GOODS.contains(saleorderGoodsVo.getGoodsId())) {
                    Integer saleOrderGoodsId = saleorderGoodsVo.getSaleorderGoodsId();
                    // 虚拟商品自动全部发货
                    SaleorderGoods updateSaleOrderGood = new SaleorderGoods();
                    updateSaleOrderGood.setSaleorderGoodsId(saleOrderGoodsId);
                    // 发货状态 全部发货:2
                    updateSaleOrderGood.setDeliveryStatus(2);
                    // 发货时间
                    updateSaleOrderGood.setDeliveryTime(logTime);
                    // 已发货数量
                    updateSaleOrderGood.setDeliveryNum(saleorderGoodsVo.getNum());
                    updateSaleOrderGood.setModTime(System.currentTimeMillis());
                    saleorderGoodsMapper.updateByPrimaryKeySelective(updateSaleOrderGood);

                    countVirtual ++;
                }
            }

            if (countVirtual != 0 && countVirtual == saleOrderGoodsVoList.size()) {
                Saleorder updateSaleOrder = new Saleorder();
                updateSaleOrder.setSaleorderId(saleOrderGoodsVoList.get(0).getSaleorderId());
                updateSaleOrder.setDeliveryStatus(2);
                updateSaleOrder.setDeliveryTime(logTime);
                updateSaleOrder.setModTime(System.currentTimeMillis());
                saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);
            }

        } catch (Exception e) {
            logger.error("handleVirtualGoods error", e);
        }
    }

}
