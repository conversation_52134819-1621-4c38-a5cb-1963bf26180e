package com.vedeng.erp.settlement.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.enums.BusinessTypeEnum;
import com.vedeng.common.core.enums.SupplierAssetEnum;
import com.vedeng.common.core.enums.TraderTypeEnum;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.dto.BuyOrderApiDto;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateChargeApplyApiDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyOrderRebateChargeApplyApiService;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.finance.cmd.SettlementBillCreateCmd;
import com.vedeng.erp.finance.dto.CapitalBillDetailDto;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.settlement.domain.context.SettlementBillSettleContext;
import com.vedeng.erp.settlement.domain.dto.SettlementBillDto;
import com.vedeng.erp.settlement.domain.dto.SettlementBillItemDto;
import com.vedeng.erp.settlement.enums.CapitalBusinessTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalOrderTypeEnum;
import com.vedeng.erp.settlement.enums.CapitalTraderModeEnum;
import com.vedeng.infrastructure.file.domain.Attachment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购结算申请单
 * @date 2023/12/7 17:41
 */
@Component
@Slf4j
public class BuyOrderRebateChargeSettlementHandle extends AbstractSettlementHandle {

    @Autowired
    BuyOrderRebateChargeApplyApiService buyOrderRebateChargeApplyApiService;
    @Autowired
    BuyorderApiService buyorderApiService;
    @Autowired
    BuyorderExpenseApiService buyorderExpenseApiService;

    @Value("${oss_http}")
    private String ossHttp;


    private static final String BUY_ORDER_ORDER = "采购订单";

    private static final String BUY_ORDER_ORDER_EXPENSE = "采购费用单";

    @Override
    protected SettlementBillDto preCreate(SettlementBillCreateCmd settlementBillCreateCmd) {
        BuyOrderRebateChargeApplyApiDto dto = buyOrderRebateChargeApplyApiService
                .getBuyOrderRebateChargeApplyApiDtoById(settlementBillCreateCmd.getBusinessSourceId());

        SettlementBillDto settlementBillDto = new SettlementBillDto();
        settlementBillDto.setTraderId(dto.getTraderId());
        settlementBillDto.setTraderSubjectId(dto.getTraderSupplierId());
        settlementBillDto.setTraderSubjectType(ErpConstant.TWO);
        settlementBillDto.setSourceType(settlementBillCreateCmd.getSourceTypeEnum().getCode());
        settlementBillDto.setSettleAmount(dto.getTotalAmount());
        settlementBillDto.setAlreadySettleAmount(BigDecimal.ZERO);
        settlementBillDto.setAccountPeriod(0);
        settlementBillDto.setAccountPeriodAmount(BigDecimal.ZERO);
        settlementBillDto.setAlreadyRepaidAmount(BigDecimal.ZERO);
        settlementBillDto.setBusinessSourceTypeId(dto.getBuyOrderRebateChargeId());
        settlementBillDto.setBusinessSourceTypeNo(dto.getBuyOrderRebateChargeNo());
        List<SettlementBillItemDto> settlementBillItemDtoList = settlementBillDto.getSettlementBillItemDtoList();
        List<Attachment> detailFileList = dto.getDetailFileList();

        // 当没有附件时,生成一条空的结算明细单,主要为保证数据完整
        if(CollUtil.isEmpty(detailFileList)){
            SettlementBillItemDto settlementBillItemDto = new SettlementBillItemDto();
            settlementBillItemDto.setSettlementType(SupplierAssetEnum.rebate.getCode());
            settlementBillItemDto.setTraderDirection(TraderTypeEnum.rollIn.getType());
            settlementBillItemDto.setNumber(BigDecimal.ZERO);
            settlementBillItemDto.setPrice(BigDecimal.ZERO);
            settlementBillItemDto.setAmount(BigDecimal.ZERO);
            settlementBillItemDtoList.add(settlementBillItemDto);
            return settlementBillDto;
        }

        // 处理附件生成的明细数据
        detailFileList.forEach(d -> {
            String url = ossHttp + d.getDomain() + d.getUri();
            InputStream in;
            try {
                in = URLUtil.url(url).openStream();
            } catch (Exception e) {
                log.error("采购结算申请单导入异常", e);
                throw new ServiceException("采购结算申请单导入异常");
            }
            ExcelReader reader = ExcelUtil.getReader(in);
            List<List<Object>> rows = reader.read();
            AtomicInteger index = new AtomicInteger();
            rows.forEach(r -> {
                if (index.getAndIncrement() == ErpConstant.ZERO) {
                    return;
                }
                SettlementBillItemDto settlementBillItemDto = new SettlementBillItemDto();
                // 订单号
                settlementBillItemDto.setBusinessNo(r.get(0).toString());
                settlementBillItemDto.setSettlementType(SupplierAssetEnum.rebate.getCode());
                settlementBillItemDto.setTraderDirection(TraderTypeEnum.rollIn.getType());
                settlementBillItemDto.setNumber(BigDecimal.ZERO);
                settlementBillItemDto.setPrice(BigDecimal.ZERO);
                settlementBillItemDto.setAmount(BigDecimal.ZERO);
                // 单据类型
                String orderType = r.get(1).toString();
                // SKU
                String sku = r.get(8).toString();
                if (BUY_ORDER_ORDER.equals(orderType)) {
                    BuyOrderApiDto buyorder = buyorderApiService.getBuyorderByBuyorderNo(settlementBillItemDto.getBusinessNo());
                    settlementBillItemDto.setBusinessType(BusinessTypeEnum.buyOrder.getCode());
                    settlementBillItemDto.setBusinessId(buyorder.getBuyorderId());
                    buyorder.getBuyorderGoodsApiDtos().stream().filter(b -> b.getSku().equals(sku)).forEach(b -> {
                        settlementBillItemDto.setProductName(sku);
                        settlementBillItemDto.setBusinessItemId(b.getBuyorderGoodsId());
                    });
                }
                if (BUY_ORDER_ORDER_EXPENSE.equals(orderType)) {
                    BuyorderExpenseDto buyorderExpense = buyorderExpenseApiService.getBuyOrderExpenseByBuyorderExpenseNo(settlementBillItemDto.getBusinessNo());
                    settlementBillItemDto.setBusinessType(BusinessTypeEnum.buyOrderExpense.getCode());
                    settlementBillItemDto.setBusinessId(buyorderExpense.getBuyorderExpenseId());
                    buyorderExpense.getBuyorderExpenseItemDtos().stream().filter(b -> b.getSku().equals(sku)).forEach(b -> {
                        settlementBillItemDto.setProductName(b.getSku());
                        settlementBillItemDto.setBusinessItemId(b.getBuyorderExpenseItemId());
                    });
                }
                settlementBillItemDtoList.add(settlementBillItemDto);
            });
        });
        return settlementBillDto;
    }

    @Override
    protected void postCreate(SettlementBillDto settlementBillDto) {

    }

    @Override
    protected BigDecimal buildPayAmount(SettlementBillSettleContext context) {
        return context.getSettlementBillDto().getSettleAmount();
    }

    @Override
    protected void postSettlement(SettlementBillSettleContext context) {

    }

    @Override
    protected void buildCapitalBill(SettlementBillSettleContext context) {
        CapitalBillDto capitalBillDto = context.getCapitalBillDto();
        capitalBillDto.setTraderMode(CapitalTraderModeEnum.REBATE.getCode());
        CapitalBillDetailDto capitalBillDetailDto = capitalBillDto.getCapitalBillDetailDto();
        capitalBillDetailDto.setBussinessType(CapitalBusinessTypeEnum.ORDER_RECEIPT.getCode());
        capitalBillDetailDto.setOrderType(CapitalOrderTypeEnum.BUY_ORDER_REBATE_CHARGE_APPLY.getCode());
    }


}
