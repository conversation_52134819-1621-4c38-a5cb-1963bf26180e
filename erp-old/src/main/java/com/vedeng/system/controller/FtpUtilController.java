package com.vedeng.system.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.utils.imageupload.ChainResult;
import com.vedeng.common.core.utils.imageupload.ImgUploadVerifyActuator;
import com.vedeng.common.model.FileInfo;
import com.vedeng.system.service.FtpUtilService;
import com.vedeng.system.service.OssUtilsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

@Controller
@RequestMapping("/fileUpload")
public class FtpUtilController extends BaseController{
	
	@Autowired
	@Qualifier("ftpUtilService")
	private FtpUtilService ftpUtilService;

	@Autowired
	private OssUtilsService ossUtilsService;

	@Autowired
	private ImgUploadVerifyActuator imgUploadVerifyActuator;
	/**
	 * <b>Description:</b><br> 测试上传页面
	 * @param request
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年5月24日 上午9:40:49
	 */
	@RequestMapping(value = "/uEditorer")
	public ModelAndView uEditorer(HttpServletRequest request) {
		ModelAndView mv = new ModelAndView();
		mv.setViewName("system/file/edit_uedit");
		return mv;
	}

	/**
	 * <b>Description:</b>上传文件到Oss接口<br>
	 * @param
	 * @return
	 * @Note
	 * <b>Author:calvin</b>
	 * <br><b>Date:</b> 2020/3/5
	 */

	@RequestMapping(value = "/uploadFile2Oss")
	public void uploadFile2Oss(HttpServletRequest request,HttpServletResponse response,@RequestParam("lwfile")MultipartFile lwfile) throws IOException {
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		FileInfo fileInfo=null;
		if(user!=null){
			  fileInfo= ossUtilsService.upload2Oss(request,lwfile);
		}else{
			fileInfo= new FileInfo(-1,"登录用户不能为空");
		}
		String result=JSON.toJSONString(fileInfo);
		response.setContentType("text/html;charset=utf-8");
		response.setHeader("Content-type", "text/html;charset=utf-8");
		response.getWriter().write(result);
		response.getWriter().flush();

	}
	/**
	 * <b>Description:</b><br> ajax异步上传附件操作
	 * @param request
	 * @param response
	 * @param lwfile
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年5月24日 上午9:41:00
	 */
	@ResponseBody
	@RequestMapping(value = "ajaxFileUpload")
	@NoNeedAccessAuthorization
	public FileInfo ajaxFileUpload(HttpServletRequest request, HttpServletResponse response, @RequestParam("lwfile") MultipartFile lwfile) {
		try{
			return ossUtilsService.upload2Oss(request,lwfile);
		}catch (Exception e){
			logger.error("文件上传失败：", e);
			return new FileInfo(-1,"文件上传失败");
		}
	}

	/**
	 * <b>Description:</b><br> ueditor编辑器上传附件操作
	 * @param request
	 * @param response
	 * @param upfile
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年5月24日 上午9:41:13
	 */
	@ResponseBody
	@RequestMapping(value = "ueditFileUpload")
	public JSONObject imageUpload(HttpServletRequest request, HttpServletResponse response, @RequestParam("upfile") MultipartFile upfile) {
		JSONObject resultJsonObj = new JSONObject();
		ChainResult result = imgUploadVerifyActuator.ruleExecute(request, upfile);
		if(!result.isSuccess()){
			resultJsonObj.put("code","-1");
			resultJsonObj.put("message",result.getData().toString());
			resultJsonObj.put("state",result.getData().toString());
			return resultJsonObj;
		}

		try{

			FileInfo fileInfo = ossUtilsService.upload2Oss(request,upfile);

			resultJsonObj.put("code","0");
			resultJsonObj.put("state","SUCCESS");
			resultJsonObj.put("message","success");
			resultJsonObj.put("url","http://" + fileInfo.getHttpUrl() + fileInfo.getFilePath());
			resultJsonObj.put("title",fileInfo.getFileName());
			resultJsonObj.put("original",upfile.getOriginalFilename());

		}catch (Exception e){

			logger.error("文件上传失败：", e);
			resultJsonObj.put("code","-1");
			resultJsonObj.put("message","文件上传失败");
		}

		return resultJsonObj;

	}

	/**
	 * <b>Description:</b><br> 浏览器下载
	 * @param request
	 * @param response
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年5月25日 上午11:02:21
	 */
	@ResponseBody
	@RequestMapping(value = "downFile")
	public FileInfo downFile(HttpServletRequest request, HttpServletResponse response) {
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			String ftp_path = request.getParameter("filePath").toString();
			String domain = "************:8082";
			//ftpUtilService.downloadFtpFile(request,ftp_path,alt);//下载到本地
			return ftpUtilService.downFile(domain,ftp_path);
		}else{
			return new FileInfo(-1,"登录用户不能为空");
		}
	}

	/**
	 * <b>Description:</b><br> 删除附件
	 * @param request
	 * @param response
	 * @return
	 * @Note
	 * <b>Author:</b> duke
	 * <br><b>Date:</b> 2017年5月25日 上午11:03:04
	 */
	@ResponseBody
	@RequestMapping(value = "deleteFile")
	public FileInfo deleteFile(HttpServletRequest request, HttpServletResponse response) {
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			String path = request.getParameter("filePath").toString();
			return ftpUtilService.deleteFile(path);
		}else{
			return new FileInfo(-1,"登录用户不能为空");
		}
	}
	
	
	@ResponseBody
	@RequestMapping(value = "ajaxFileUploadServe")
	public FileInfo ajaxFileUploadServe(HttpServletRequest request, HttpServletResponse response, @RequestParam("lwfile") MultipartFile lwfile) {
		User user = (User)request.getSession().getAttribute(Consts.SESSION_USER);
		if(user!=null){
			//临时文件存放地址
			String path = request.getSession().getServletContext().getRealPath("/upload/ajax");
			ftpUtilService.fileUploadServe(path,lwfile);
			
			return new FileInfo(0,"ok");
		}else{
			return new FileInfo(-1,"登录用户不能为空");
		}
	}

	@ResponseBody
	@RequestMapping(value = "ajaxFileUploadAuthorization")
	public FileInfo ajaxFileUploadAuthorization(HttpServletRequest request, HttpServletResponse response, @RequestParam("lwfile") MultipartFile lwfile) {
		try{

			FileInfo fileInfo = ossUtilsService.upload2Oss(request,lwfile);

			fileInfo.setPrefix(fileInfo.getFileName().substring(fileInfo.getFileName().indexOf(".")+1));
			fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
			fileInfo.setFullPath(fileInfo.getHttpUrl() + fileInfo.getFilePath());

			return fileInfo;

		}catch (Exception e){
			logger.error("文件上传失败：", e);
			return new FileInfo(-1,"文件上传失败");
		}
	}
}
