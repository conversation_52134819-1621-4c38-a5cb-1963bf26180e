package com.vedeng.erp.kingdee.repository;

import com.vedeng.common.mybatis.jbatis.BaseDao;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSalesVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.domain.entity.KingDeeSupplierEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class KingDeeSalesVatPlainInvoiceRepository extends BaseDao<KingDeeSalesVatPlainInvoiceEntity, Integer> {

}