package com.vedeng.erp.trader.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.vedeng.common.mybatis.domain.BaseEntity;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
    * 客户列表（财务专用）
    */
@Data
public class TraderCustomerFinanceExcelDto {

    /**
    * 交易者ID
    */
    @ExcelProperty(value = "*客户ID", index = 0)
    private Integer traderId;

    /**
    * (新)客户名称，不推送金蝶
    */
    @ExcelProperty(value = "客户名称（新）", index = 1)
    private String customerNameNew = "";

    /**
    * 客户性质：1直接客户 2间接客户
    */
    @ExcelProperty(value = "*客户性质", index = 2)
    private String customerNature;

    /**
    * 终端客户分类：1公立 2民营个体 3民营集团
    */
    @ExcelProperty(value = "终端客户分类", index = 3)
    private String customerClass;

    /**
    * 集团名称
    */
    @ExcelProperty(value = "所属集团",index = 4)
    private String groupName = "";

    /**
    * 客户细分类：1医疗卫生机构 2非医疗卫生机构 3分销商
    */
    @ExcelProperty(value = "客户细分类", index = 5)
    private String customerSecondType;

    /**
    * 医疗机构分类：1医院 2基层医疗卫生机构 3专业医疗卫生机构 4其他医疗卫生机构
    */
    @ExcelProperty(value = "医疗机构分类", index = 6)
    private String customerThirdType;

    /**
    * 医院等级：1一级、2二级、3三级、4未分级
    */
    @ExcelProperty(value = "医院等级", index = 7)
    private String hospitalLever;

    /**
    * 所属医院共体
    */
    @ExcelProperty(value = "所属医院共体", index = 8)
    private String hospitalName = "";

    /**
     * 主键
     */
    private Integer traderCustomerFinanceId;


    /**
     * 是否推送金蝶 0否 1是
     */
    private Integer isPush = 0;

    /**
     * 推送时间
     */
    private Date pushTime;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete = 0;

    /**
     * 备注
     */
    private String remark = "";

    /**
     * 更新备注
     */
    private String updateRemark = "";


    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 创建者id
     */
    private Integer creator;

    /**
     * 修改者id
     */
    private Integer updater;

    /**
     * 创建者名称
     */
    private String creatorName;

    /**
     * 修改者名称
     */
    private String updaterName;

}