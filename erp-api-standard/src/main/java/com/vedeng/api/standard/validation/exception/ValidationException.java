package com.vedeng.api.standard.validation.exception;

import com.vedeng.api.standard.validation.ValidationResult;

/**
 * 验证异常类
 * 当验证失败时抛出此异常
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class ValidationException extends Exception {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 验证结果
     */
    private final ValidationResult validationResult;
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     */
    public ValidationException(String message) {
        super(message);
        this.validationResult = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param validationResult 验证结果
     */
    public ValidationException(String message, ValidationResult validationResult) {
        super(message);
        this.validationResult = validationResult;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     */
    public ValidationException(String message, Throwable cause) {
        super(message, cause);
        this.validationResult = null;
    }
    
    /**
     * 构造函数
     * 
     * @param message 异常消息
     * @param cause 原因异常
     * @param validationResult 验证结果
     */
    public ValidationException(String message, Throwable cause, ValidationResult validationResult) {
        super(message, cause);
        this.validationResult = validationResult;
    }
    
    /**
     * 获取验证结果
     * 
     * @return 验证结果
     */
    public ValidationResult getValidationResult() {
        return validationResult;
    }
    
    /**
     * 是否有验证结果
     * 
     * @return 是否有验证结果
     */
    public boolean hasValidationResult() {
        return validationResult != null;
    }
}
