package com.vedeng.workbench.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.dwh.constant.DwhConstants;
import com.vedeng.dwh.model.dto.DwhErpUserDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchDto;
import com.vedeng.dwh.model.dto.DwhWorkbenchOverviewDto;
import com.vedeng.dwh.service.DwhBussinessChanceService;
import com.vedeng.dwh.service.DwhThreadLocalService;
import com.vedeng.kpi.model.DTO.KpiDailyCountExtDto;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.KpiDailyCountService;
import com.vedeng.kpi.share.KpiCommonConstant;
import com.vedeng.workbench.model.HeaddataSummaryDto;
import com.vedeng.workbench.model.dto.*;
import com.vedeng.workbench.model.vo.WorkbenchRequest;
import com.vedeng.workbench.model.vo.WorkbenchResponse;
import com.vedeng.workbench.model.vo.base.WorkbenchUserInfo;
import com.vedeng.workbench.model.vo.summary.NumAmounteSummary;
import com.vedeng.workbench.service.WorkbenchBussinessChanceService;
import com.vedeng.workbench.utils.WorkbenchUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 商机工作台controller.
 *
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 7:27 下午.
 * @author: Tomcat.Hui.
 */
@SuppressWarnings("Duplicates")
@Slf4j
@Controller
@RequestMapping("/workbench/bussinesschance")
public class WorkbenchBussinessChanceController {

    // add by Randy.Xu 2020/10/29 17:14 .Desc: . begin
    @Autowired
    private WorkbenchBussinessChanceService bussinessChanceService;
    // add by Randy.Xu 2020/10/29 17:14 .Desc: . end

    @Autowired
    private KpiDailyCountService kpiDailyCountService;

    @Autowired
    private DwhBussinessChanceService dwhBussinessChanceService;

    @Autowired
    private DwhThreadLocalService dwhThreadLocalService;


    @RequestMapping(value = "/test", method = RequestMethod.GET)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<WorkbenchResponse> test(HttpSession session, @RequestParam WorkbenchRequest req, HttpServletResponse response) {

        WorkbenchResponse result = new WorkbenchResponse();
        log.info("test");
        try {
            response.getWriter().write("SUCCESS");
        } catch (IOException e) {
            log.error("error");
        }


        //获取当前登录对象
        User user = (User) session.getAttribute(Consts.SESSION_USER);


        //todo
        return new ResultInfo<WorkbenchResponse>(0, "success", result);
    }


    /**
     * .
     *
     * @param session
     * @param request
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/10/28 12:08.
     * @author: Randy.Xu.
     * @return: org.springframework.web.servlet.ModelAndView.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping(value = "/index")
    public ModelAndView getIndex(HttpSession session, HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);


        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        long thisWeekEndTIME = WorkbenchUtil.getThisWeekEnd().getTime();
        long hisStartTime = DateUtil.convertLong("2019-11-1", DateUtil.DATE_FORMAT);
        long thisMonthStartTime = DateUtil.getBeginDayOfMonth().getTime();
        long thisMonthEndTime = DateUtil.getEndDayOfMonth().getTime();

        //设置查询的用户信息
        WorkbenchDataQueryDto dataQueryDto = new WorkbenchDataQueryDto();
        dataQueryDto.setUserId(user.getUserId());
        dataQueryDto.setUsername(user.getUsername());
        dataQueryDto.setOrgId(user.getOrgId());

        //获取用户的信息
        DwhErpUserDto curUser = dwhThreadLocalService.getUserInfo(dataQueryDto.getUserId());

        //用户还未同步时返回中间页面
        if (Objects.isNull(curUser)){
            mav.addObject("message", "该用户还未同步，请待同步后重试！");
            mav.setViewName("common/fail");
            return mav;
        }

        dataQueryDto.setPositionName(curUser.getPositionName());
        dataQueryDto.setOrgId(curUser.getOrgId());

        //页面头信息
        NumAmounteSummary headdataSummary = new NumAmounteSummary();
        WorkbenchUserInfo workbenchUser = new WorkbenchUserInfo();


        if ("销售工程师".equals(curUser.getPositionName()) || "销售专员".equals(dataQueryDto.getPositionName())) {
            mav.setViewName("workbench/sales/sale_view");

            //设置当月时间
            Date thisMonthStartDay = DateUtil.getBeginDayOfMonth();
            //设置销售

            setHeadSummaryProperties(mav, dataQueryDto, curUser, headdataSummary, workbenchUser);

//            headdataSummary.getObjectInfo().setType(1);

            //数据总览thisMonthStartDay,new Date(),
            //业绩排行
            KpiUserInfoDto kpiUserInfoDto = new KpiUserInfoDto();
            kpiUserInfoDto.setUserId(dataQueryDto.getUserId());

            KpiDailyCountExtDto kpiCountVoByUser = kpiDailyCountService.getKpiCountVoByUser(kpiUserInfoDto, thisMonthStartDay, new Date(), KpiCommonConstant.KPI_CONFIG_WEIGHT_NAME_YJ);
            Integer sort = kpiCountVoByUser.getSort();
            headdataSummary.getObjectInfo().setSort(sort);


            //商机状态
            List<BussinessStatusEchartsDto> bussinessChanceStatus = bussinessChanceService.getBussinessChanceStatus(dataQueryDto);
            String bussinessChanceStatusString = JSON.toJSONString(bussinessChanceStatus);

            mav.addObject("bussinessChanceStatusList", bussinessChanceStatus);
            mav.addObject("bussinessChanceStatusString", bussinessChanceStatusString);

            //预计本周成单（实时）
            List<WorkbenchBussinessChanceExtDto> thisWeekExpectOrderChanceList = bussinessChanceService.getThisWeekExpectOrderChanceListById(dataQueryDto);
            mav.addObject("thisWeekOrderList", thisWeekExpectOrderChanceList);


            //重点商机
            List<DwhWorkbenchDto> importantBussinessChancesByUser = dwhBussinessChanceService.getImportantBussinessChancesByUser(dataQueryDto.getUserId(), hisStartTime, yesterdayEndTime);


            importantBussinessChancesByUser = importantBussinessChancesByUser.stream()
                    .sorted(Comparator.comparing(DwhWorkbenchDto::getBussinessLevel)
                            .thenComparing(DwhWorkbenchDto::getBussinessAmount, Comparator.reverseOrder())).collect(Collectors.toList());


            importantBussinessChancesByUser.stream().forEach(x -> {
                int index = -1;
                if (x != null && StringUtils.isNotEmpty(x.getGoodsName())) {
                    if ((index = x.getGoodsName().indexOf(",")) > 0) {
                        x.setGoodsName(x.getGoodsName().substring(0, index));
                    }
                    if (x.getGoodsName().length() > 60) {
                        x.setGoodsName(x.getGoodsName().substring(0, 60) + "...");
                    }
                }
            });
            mav.addObject("importantBussinessChanceList", importantBussinessChancesByUser);


            //今日待沟通商机(实时)
            List<WorkbenchBussinessChanceExtDto> todayToCommunicateBussinessChanceList = bussinessChanceService.getTodayToCommunicateBussinessChanceById(dataQueryDto);
            todayToCommunicateBussinessChanceList.stream().forEach(x -> {
                if (x.getLastCommunicateRecord() != null) {
                    long endTime = x.getLastCommunicateRecord().getEndTime();
                    Date date = new Date(endTime);
                    x.getLastCommunicateRecord().setEndDate(date);
                    if (x.getLastCommunicateRecord().getNextContactContent() != null) {
                        if (x.getLastCommunicateRecord().getNextContactContent().length() > 50) {
                            x.getLastCommunicateRecord().setNextContactContent(x.getLastCommunicateRecord().getNextContactContent().substring(0, 50) + "...");
                        }
                    }
                }
            });
            mav.addObject("todayToCommunicateChanceList", todayToCommunicateBussinessChanceList);


            //今日新增商机(实时)
            List<WorkbenchBussinessChanceExtDto> todayNewAddBussinessChanceList = bussinessChanceService.getTodayNewAddBussinessChnaceListById(dataQueryDto);

            mav.addObject("newAddBussinessChanceList", todayNewAddBussinessChanceList);

        } else if (DwhConstants.ZG.equals(curUser.getPositionName())) {
            mav.setViewName("workbench/sales/sale_view_leader_zg");
            //表头数据
            setHeadSummaryProperties(mav, dataQueryDto, curUser, headdataSummary, workbenchUser);

            //商机状态for主管
            List<BussinessStatusEchartsDto> bussinessChanceStatus = bussinessChanceService.getDeptBussinessChanceStatusDtoList(dataQueryDto);
            String bussinessChanceStatusString = JSON.toJSONString(bussinessChanceStatus);

            Integer selectId = bussinessChanceStatus.get(0).getOrgId();
            mav.addObject("selectId", selectId);

            mav.addObject("bussinessChanceStatusList", bussinessChanceStatus);
            mav.addObject("bussinessChanceStatusString", bussinessChanceStatusString);


            //预警商机 for主管
            //本周新增大项目
            List<WorkbenchBussinessChanceExtDto> thisWeekNewAddImportChanceList = bussinessChanceService.getThisWeekNewAddImportChanceListByIds(dataQueryDto);
            mav.addObject("thisWeekNewAddImportChanceList", thisWeekNewAddImportChanceList);
            //2日未联系商机
            List<WorkbenchBussinessChanceExtDto> twoDayUncontactChanceList = bussinessChanceService.getTwoDayUncontactChanceListByIds(dataQueryDto);
            mav.addObject("twoDaysUncontactChanceList", twoDayUncontactChanceList);

            //本周核心商品商机丢单
            List<WorkbenchBussinessChanceExtDto> thisWeekCoreGoodfailChanceList = bussinessChanceService.getThisWeekCoreGoodfailChanceListByIds(dataQueryDto);
            mav.addObject("thisWeekCoreGoodFailChanceList", thisWeekCoreGoodfailChanceList);

            //本周预计到款商机未到
            List<WorkbenchBussinessChanceExtDto> thisWeekExpectFailChanceList = bussinessChanceService.getThisWeekExpectFailChanceListByIds(dataQueryDto);
            mav.addObject("thisWeekExpectFailChanceList", thisWeekExpectFailChanceList);

        } else if (DwhConstants.JL.equals(curUser.getPositionName())) {
            mav.setViewName("workbench/sales/sale_view_leader_jl");
            //表头数据
            setHeadSummaryProperties(mav, dataQueryDto, curUser, headdataSummary, workbenchUser);

            //商机状态
            List<BussinessStatusEchartsDto> bussinessChanceStatus = bussinessChanceService.getDeptBussinessChanceStatusDtoList(dataQueryDto);
            String bussinessChanceStatusString = JSON.toJSONString(bussinessChanceStatus);
            Integer selectId = bussinessChanceStatus.get(0).getOrgId();
            mav.addObject("selectId", selectId);
            mav.addObject("bussinessChanceStatusList", bussinessChanceStatus);
            mav.addObject("bussinessChanceStatusString", bussinessChanceStatusString);
        } else if (DwhConstants.XSZJ.equals(curUser.getPositionName())) {
            mav.setViewName("workbench/sales/sale_view_leader_xszj");
            //表头数据
            setHeadSummaryProperties(mav, dataQueryDto, curUser, headdataSummary, workbenchUser);

            //商机状态
            List<BussinessStatusEchartsDto> bussinessChanceStatus = bussinessChanceService.getDeptBussinessChanceStatusDtoList(dataQueryDto);
            String bussinessChanceStatusString = JSON.toJSONString(bussinessChanceStatus);
            Integer selectId = bussinessChanceStatus.get(0).getOrgId();
            mav.addObject("selectId", selectId);
            mav.addObject("bussinessChanceStatusList", bussinessChanceStatus);
            mav.addObject("bussinessChanceStatusString", bussinessChanceStatusString);

        }


        return mav;

    }


    @ResponseBody
    @RequestMapping(value = "/status/echarts", method = RequestMethod.GET)
    public ResultInfo<List<BussinessStatusEchartsDto>> getEchartsDate(HttpSession session,
                                                                      @RequestParam(value = "queryOrgId", required = false) Integer queryOrgId, String positionName, Integer userId,
                                                                      @RequestParam(value = "queryUserId",required = false) Integer queryUserId) {

        //设置查询条件
        WorkbenchDataQueryDto dataQueryDto = new WorkbenchDataQueryDto();
        dataQueryDto.setPositionName(positionName);
        //查询的角色id
        dataQueryDto.setUserId(userId);
        //查看者的部门id
        dataQueryDto.setQueryUserId(queryUserId);
        if (DwhConstants.XSZJ.equals(positionName) || DwhConstants.JL.equals(positionName)) {
            //查询的部门Id
            dataQueryDto.setQueryOrgId(queryOrgId);
        }
        List<BussinessStatusEchartsDto> bussinessChanceStatus = bussinessChanceService.getBussinessChanceStatus(dataQueryDto);

        return new ResultInfo<List<BussinessStatusEchartsDto>>(0, "成功", bussinessChanceStatus);
    }


    /**
     * 表头数据拷贝
     *
     * @param mav
     * @param dataQueryDto
     * @param curUser
     * @param headdataSummary
     * @param workbenchUser
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/7 15:12.
     * @author: Randy.Xu.
     * @return: void.
     * @throws: .
     */
    private void setHeadSummaryProperties(ModelAndView mav, WorkbenchDataQueryDto dataQueryDto, DwhErpUserDto curUser, NumAmounteSummary headdataSummary, WorkbenchUserInfo workbenchUser) {
        HeaddataSummaryDto headdataSummaryDto = bussinessChanceService.getHeaddataSummaryDto(dataQueryDto);
        BeanUtils.copyProperties(headdataSummaryDto, headdataSummary);

        List<SubObjectDo> subObjectDos = bussinessChanceService.getSubObject(dataQueryDto);
        workbenchUser.setSubDirectObjectList(subObjectDos);
        workbenchUser.setUserId(dataQueryDto.getUserId());
        workbenchUser.setOrgId(dataQueryDto.getOrgId());
        workbenchUser.setUsername(dataQueryDto.getUsername());
        workbenchUser.setPositionName(curUser.getPositionName());
        //时间参数
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        Long thisWeekStartTime = WorkbenchUtil.getThisWeekStart().getTime();
        Long thisWeekEndTime = WorkbenchUtil.getThisWeekEnd().getTime();
        Long todayStartTime = System.currentTimeMillis();
        Long preWeekStartTime = WorkbenchUtil.getPreWeekStart().getTime();
        Long preWeekEndTime = WorkbenchUtil.getPreWeekEnd().getTime();


        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();
        String yesterdayEndtimeStr = DateUtil.convertString(yesterdayEndTime, DateUtil.DATE_FORMAT);
        String thisWeekStartTimeStr = DateUtil.convertString(thisWeekStartTime, DateUtil.DATE_FORMAT);
        String thisWeekEndTimeStr = DateUtil.convertString(thisWeekEndTime, DateUtil.DATE_FORMAT);
        String todayStartTimeStr = DateUtil.convertString(todayStartTime,DateUtil.DATE_FORMAT);
        String preWeekStartTimeStr = DateUtil.convertString(preWeekStartTime,DateUtil.DATE_FORMAT);
        String preWeekEndTimeStr = DateUtil.convertString(preWeekEndTime,DateUtil.DATE_FORMAT);



        workbenchUser.setYesterdayEndtimeStr(yesterdayEndtimeStr);
        workbenchUser.setThisWeekStartTimeStr(thisWeekStartTimeStr);
        workbenchUser.setThisWeekEndTimeStr(thisWeekEndTimeStr);
        workbenchUser.setTodayStartTimeStr(todayStartTimeStr);
        workbenchUser.setPreWeekStartTimeStr(preWeekStartTimeStr);
        workbenchUser.setPreWeekEndTimeStr(preWeekEndTimeStr);
        headdataSummary.setObjectInfo(workbenchUser);
        mav.addObject("workbenchUser", workbenchUser);

        mav.addObject("headSummary", headdataSummary);
    }


    /**
     * 获取管理数据总览异步方式
     * .
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/23 19:38.
     * @author: Randy.Xu.
     * @return: com.vedeng.common.model.ResultInfo<java.util.List               <                   c       o       m       .       vedeng.workbench.model.dto.DataOverviewSummaryDto>>.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping("leaderDataOverView")
    public ResultInfo<List<DataOverviewSummaryDto>> getLeaderDataOverViewSummaryList(WorkbenchDataQueryDto dataQueryDto) {
        List<DataOverviewSummaryDto> dataOverviewSummaryDtoList = new ArrayList<>();
        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            //主管级别
            dataOverviewSummaryDtoList = bussinessChanceService.getDataOverviewSummaryDtoListByIds(dataQueryDto);


        } else if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {
            //经理级别
            dataOverviewSummaryDtoList = bussinessChanceService.getDeptDataOverviewSummaryDtoListByIds(dataQueryDto);

        } else if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {
            //销售总监级别
            dataOverviewSummaryDtoList = bussinessChanceService.getDeptDataOverviewSummaryDtoListByIds(dataQueryDto);

        } else {
            return new ResultInfo<>(-1, "当前用户的职位不属于管理");
        }

        return new ResultInfo<List<DataOverviewSummaryDto>>(0, "获取数据总览成功", dataOverviewSummaryDtoList);
    }

    /**
     * .管理层待沟通商机
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/24 10:34.
     * @author: Randy.Xu.
     * @return: com.vedeng.common.model.ResultInfo<java.util.List               <                   c       o       m       .       v       e       deng.workbench.model.dto.CommunicateSummaryDto>>.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping("leaderCommunicate")
    public ResultInfo<List<CommunicateSummaryDto>> getLeaderCommunicateSummaryList(WorkbenchDataQueryDto dataQueryDto) {
        List<CommunicateSummaryDto> communicateSummaryDtoList = new ArrayList<>();
        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {

            //待沟通商机for主管
            communicateSummaryDtoList = bussinessChanceService.getCommunicateBussinessChanceByIds(dataQueryDto);

        } else if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {

            //待沟通商机for经理
            communicateSummaryDtoList = bussinessChanceService.getDeptCommunicateBussinessChanceByIds(dataQueryDto);

        } else if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {

            //待沟通商机for销售总监
            communicateSummaryDtoList = bussinessChanceService.getDeptCommunicateBussinessChanceByIds(dataQueryDto);

        } else {
            return new ResultInfo<>(-1, "当前用户职位不属于管理");
        }


        return new ResultInfo<List<CommunicateSummaryDto>>(0, "管理待沟通商机获取成功", communicateSummaryDtoList);
    }



    /**
     * 主管级别重点商机
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/24 13:16.
     * @author: Randy.Xu.
     * @return: com.vedeng.common.model.ResultInfo<java.util.List               <               c   o   m   .   v   e   d   eng.workbench.model.dto.SalersDwhWorkbenchOverviewExtDto>>.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping("leaderImportant/directLeader")
    public ResultInfo<List<SalersDwhWorkbenchOverviewExtDto>> getDirectLeaderImportantList(WorkbenchDataQueryDto dataQueryDto) {

        List<SalersDwhWorkbenchOverviewExtDto> salersDwhWorkbenchOverviewExtDtoListS = new ArrayList<>();
        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            salersDwhWorkbenchOverviewExtDtoListS = bussinessChanceService.getSalersImportantBussinessChances(dataQueryDto);

        } else {
            return new ResultInfo<>(-1, "职位不符合销售主管");
        }

        return new ResultInfo<List<SalersDwhWorkbenchOverviewExtDto>>(0, "获取主管重点商机成功", salersDwhWorkbenchOverviewExtDtoListS);
    }

    /**
     * 经理和总监级别重点商机
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/24 13:17.
     * @author: Randy.Xu.
     * @return: com.vedeng.common.model.ResultInfo<java.util.List               <                   c       o       m       .       v       e       d       eng.dwh.model.dto.DwhWorkbenchOverviewDto>>.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping("leaderImportant/leader")
    public ResultInfo<List<DwhWorkbenchOverviewDto>> getLeaderImportantList(WorkbenchDataQueryDto dataQueryDto) {

        List<DwhWorkbenchOverviewDto> deptImportantChanceDtoList = new ArrayList<>();
        if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {

            //经理级别
            deptImportantChanceDtoList = bussinessChanceService.getDeptImportantBussinessChances(dataQueryDto);

        } else if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {

            //总监级别
            deptImportantChanceDtoList = bussinessChanceService.getDeptImportantBussinessChances(dataQueryDto);

        } else {
            return new ResultInfo<>(-1, "职位不符合销售经理或总监");
        }

        return new ResultInfo<List<DwhWorkbenchOverviewDto>>(0, "获取管理非主管重点商机成功", deptImportantChanceDtoList);
    }


    /**
     * 管理级别预计本周数据
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/25 15:29.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: com.vedeng.common.model.ResultInfo<java.util.List<com.vedeng.workbench.model.dto.ExpectThisWeekOrderSummaryDto>>.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping("leaderExpect")
    public ResultInfo<List<ExpectThisWeekOrderSummaryDto>> getLeaderExpectList(WorkbenchDataQueryDto dataQueryDto) {
        List<ExpectThisWeekOrderSummaryDto> expectThisWeekOrderSummaryDtoList = new ArrayList<>();
        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {

            //预计本周成单for主管
            expectThisWeekOrderSummaryDtoList = bussinessChanceService.getExpectThisWeekOrderSummaryDtoListByIds(dataQueryDto);

        }else if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {

            //预计本周成单for经理
            expectThisWeekOrderSummaryDtoList = bussinessChanceService.getDeptExpectThisWeekOrderSummaryDtoListByIds(dataQueryDto);


        } else if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {

            //预计本周成单for总监
            expectThisWeekOrderSummaryDtoList = bussinessChanceService.getDeptExpectThisWeekOrderSummaryDtoListByIds(dataQueryDto);

        }else{

            return  new ResultInfo<>(-1,"职位不符合要求");
        }
        return new ResultInfo<List<ExpectThisWeekOrderSummaryDto>>(0,"获取管理预计本周列表信息成功",expectThisWeekOrderSummaryDtoList);
    }




    /**
     * 预警商机for销售经理和总监
     *
     * @param dataQueryDto
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/24 13:27.
     * @author: Randy.Xu.
     * @return: com.vedeng.common.model.ResultInfo<java.util.List               <               c   o   m   .   v   e   d   eng.workbench.model.dto.WarningBussinessChanceSummaryDto>>.
     * @throws: .
     */
    @ResponseBody
    @RequestMapping("leaderWarning/leader")
    public ResultInfo<List<WarningBussinessChanceSummaryDto>> getLeaderWarningList(WorkbenchDataQueryDto dataQueryDto) {
        List<WarningBussinessChanceSummaryDto> warningBussinessChanceSummaryDtoList = new ArrayList<>();

        if (DwhConstants.JL.equals(dataQueryDto.getPositionName())) {

            //预警商机for经理
            warningBussinessChanceSummaryDtoList = bussinessChanceService.getWarningBussinessChanceSummaryDtoList(dataQueryDto);

        } else if (DwhConstants.XSZJ.equals(dataQueryDto.getPositionName())) {

            //预警商机for销售总监
            warningBussinessChanceSummaryDtoList = bussinessChanceService.getWarningBussinessChanceSummaryDtoList(dataQueryDto);


        } else {
            return new ResultInfo<>(-1, "职位不符合销售经理或总监");
        }

        return new ResultInfo<List<WarningBussinessChanceSummaryDto>>(0, "获取预警商机成功", warningBussinessChanceSummaryDtoList);
    }


    /**
     * 主管预警商机新增重大
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/25 10:33.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: com.vedeng.common.model.ResultInfo<java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>>.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping("directLeaderWarning/newAddImportant")
    public ResultInfo<List<WorkbenchBussinessChanceExtDto>> getDirectLeaderWarningNewAddList(WorkbenchDataQueryDto dataQueryDto) {

        List<WorkbenchBussinessChanceExtDto> thisWeekNewAddImportChanceList = new ArrayList<>();

        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            //预警商机 for主管
            //本周新增大项目
            thisWeekNewAddImportChanceList = bussinessChanceService.getThisWeekNewAddImportChanceListByIds(dataQueryDto);

        } else {
            return new ResultInfo<>(-1, "职位不符合主管");
        }
        return new ResultInfo<List<WorkbenchBussinessChanceExtDto>>(0, "获取主管本周新增重大商机成功", thisWeekNewAddImportChanceList);
    }

    /**
     * 主管预警商机2日未联系.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/25 10:34.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: com.vedeng.common.model.ResultInfo<java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>>.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping("directLeaderWarning/twoDayUncontact")
    public ResultInfo<List<WorkbenchBussinessChanceExtDto>> getDirectLeaderWarningUncontactList(WorkbenchDataQueryDto dataQueryDto) {

        List<WorkbenchBussinessChanceExtDto> twoDayUncontactChanceList = new ArrayList<>();

        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            //预警商机 for主管
            //2日未联系商机
            twoDayUncontactChanceList = bussinessChanceService.getTwoDayUncontactChanceListByIds(dataQueryDto);


        } else {
            return new ResultInfo<>(-1, "职位不符合主管");
        }
        return new ResultInfo<List<WorkbenchBussinessChanceExtDto>>(0, "获取主管2日为沟通成功", twoDayUncontactChanceList);
    }

    /**
     * 主管预警商机核心商品丢单.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/25 10:34.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: com.vedeng.common.model.ResultInfo<java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>>.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping("directLeaderWarning/coreGoodsFail")
    public ResultInfo<List<WorkbenchBussinessChanceExtDto>> getDirectLeaderWarningCoreGoodsFailList(WorkbenchDataQueryDto dataQueryDto) {

        List<WorkbenchBussinessChanceExtDto> thisWeekCoreGoodFailChanceList = new ArrayList<>();

        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            //预警商机 for主管
            //本周核心商品商机丢单
            thisWeekCoreGoodFailChanceList = bussinessChanceService.getThisWeekCoreGoodfailChanceListByIds(dataQueryDto);
        } else {
            return new ResultInfo<>(-1, "职位不符合主管");
        }
        return new ResultInfo<List<WorkbenchBussinessChanceExtDto>>(0, "获取本周核心商品失败信息成功", thisWeekCoreGoodFailChanceList);
    }

    /**
     * 主管预警预计本周到款未到.
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/11/25 10:34.
     * @author: Randy.Xu.
     * @param dataQueryDto
     * @return: com.vedeng.common.model.ResultInfo<java.util.List<com.vedeng.workbench.model.dto.WorkbenchBussinessChanceExtDto>>.
     * @throws:  .
     */
    @ResponseBody
    @RequestMapping("directLeaderWarning/expectFail")
    public ResultInfo<List<WorkbenchBussinessChanceExtDto>> getDirectLeaderWarningExpectFailList(WorkbenchDataQueryDto dataQueryDto) {

        List<WorkbenchBussinessChanceExtDto> thisWeekExpectFailChanceList = new ArrayList<>();
        //预警商机 for主管
        //本周预计到款商机未到
        if (DwhConstants.ZG.equals(dataQueryDto.getPositionName())) {
            thisWeekExpectFailChanceList = bussinessChanceService.getThisWeekExpectFailChanceListByIds(dataQueryDto);
        } else {
            return new ResultInfo<>(-1, "职位不符合主管");
        }
        return new ResultInfo<List<WorkbenchBussinessChanceExtDto>>(0, "获取本周到款商机未到",thisWeekExpectFailChanceList);
    }

}


