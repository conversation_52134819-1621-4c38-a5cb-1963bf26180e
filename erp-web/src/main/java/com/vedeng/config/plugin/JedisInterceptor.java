package com.vedeng.config.plugin;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

import java.util.Arrays;

public class JedisInterceptor implements MethodInterceptor {

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Transaction t = Cat.newTransaction("Redis", invocation.getMethod().getName()+"|"+Arrays.stream(invocation.getArguments()).findFirst().get());
        try {
            Object res=invocation.proceed();
            t.setSuccessStatus();
            return res;
        } catch (Throwable e) {
            t.setStatus(e);
            Cat.logError(e);
            throw e;
        } finally {
            t.complete();
        }
    }
}

