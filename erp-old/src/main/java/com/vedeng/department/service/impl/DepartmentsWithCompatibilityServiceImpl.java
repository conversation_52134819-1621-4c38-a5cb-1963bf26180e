package com.vedeng.department.service.impl;

import com.ctrip.framework.apollo.ConfigService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.department.service.DepartmentsWithCompatibilityService;
import com.vedeng.goods.dao.CategoryDepartmentMapper;
import com.vedeng.goods.dao.CoreSpuSearchGenerateMapper;
import com.vedeng.goods.dao.SpuDepartmentMappingGenerateMapper;
import com.vedeng.goods.model.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class DepartmentsWithCompatibilityServiceImpl implements DepartmentsWithCompatibilityService {

    private final static Logger LOGGER = LoggerFactory.getLogger(DepartmentsWithCompatibilityServiceImpl.class);

    private final static String SYNC_DEPARTMENT_SWITCH_PROP_NAME = "goodService.syncDepartmentToSpu.switch";
    private final static Integer ON = 1;
    private final static Integer OFF = 0;

    @Resource
    private SpuDepartmentMappingGenerateMapper spuDepartmentMappingGenerateMapper;
    @Resource
    private CoreSpuSearchGenerateMapper coreSpuSearchGenerateMapper;
    @Resource
    private CategoryDepartmentMapper categoryDepartmentMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveDepartment(Integer thirdLevelCategoryId, Collection<Integer> departmentIdsToSave, User operator) {
        //if switch is on: 兼容性考虑，把科室信息同步到该三级分类下spu
        if (allowSyncDepartmentData2Spu()) {
            syncDepartmentData2Spu(thirdLevelCategoryId, departmentIdsToSave);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void syncDepartment2Spu(Integer spuId) {
        if (spuId == null || spuId < 0) {
            return;
        }

        if (!allowSyncDepartmentData2Spu()) {
            return;
        }

        CoreSpuSearchGenerate skuSearchQuery = coreSpuSearchGenerateMapper.selectByPrimaryKey(spuId);
        if (skuSearchQuery == null || skuSearchQuery.getCategoryId() == null) {
            return;
        }

        List<CategoryDepartmentDo> categoryDepartmentDoList = categoryDepartmentMapper.listByCategoryId(skuSearchQuery.getCategoryId(), CommonConstants.IS_DELETE_0);
        for (CategoryDepartmentDo categoryDepartmentDo : categoryDepartmentDoList) {
            SpuDepartmentMappingGenerateExample example = new SpuDepartmentMappingGenerateExample();
            example.createCriteria().andSpuIdEqualTo(spuId)
                    .andStatusEqualTo(1);
            spuDepartmentMappingGenerateMapper.deleteByExample(example);

            SpuDepartmentMappingGenerate spuDepartment = new SpuDepartmentMappingGenerate();
            spuDepartment.setSpuId(spuId);
            spuDepartment.setStatus(CommonConstants.STATUS_1);
            spuDepartment.setAddTime(new Date());
            spuDepartment.setModTime(new Date());
            spuDepartment.setDepartmentId(categoryDepartmentDo.getDepartmentId());
            spuDepartmentMappingGenerateMapper.insert(spuDepartment);
        }

    }


    private void syncDepartmentData2Spu(Integer thirdLevelCategoryId, final Collection<Integer> departmentIdsToSave) {
        CoreSpuSearchGenerateExample spuSearchExample = new CoreSpuSearchGenerateExample();
        spuSearchExample.createCriteria().andCategoryIdEqualTo(thirdLevelCategoryId)
                .andCheckStatusEqualTo(3)
                .andStatusEqualTo(1);
        List<CoreSpuSearchGenerate> spuList = coreSpuSearchGenerateMapper.selectByExample(spuSearchExample);

        for (CoreSpuSearchGenerate spu : spuList) {
            SpuDepartmentMappingGenerateExample example = new SpuDepartmentMappingGenerateExample();
            example.createCriteria().andSpuIdEqualTo(spu.getSpuId())
                    .andStatusEqualTo(1);
            spuDepartmentMappingGenerateMapper.deleteByExample(example);

            if (CollectionUtils.isNotEmpty(departmentIdsToSave)) {
                for (Integer departmentId : departmentIdsToSave) {
                    SpuDepartmentMappingGenerate spuDepartment = new SpuDepartmentMappingGenerate();
                    spuDepartment.setSpuId(spu.getSpuId());
                    spuDepartment.setStatus(CommonConstants.STATUS_1);
                    spuDepartment.setAddTime(new Date());
                    spuDepartment.setModTime(new Date());
                    spuDepartment.setDepartmentId(departmentId);
                    spuDepartmentMappingGenerateMapper.insert(spuDepartment);
                }

                LOGGER.info("同步科室数据至三级分类关联的spu - categoryId:{}, departmentIds:{}", thirdLevelCategoryId, departmentIdsToSave);
            }
        }
    }


    private boolean allowSyncDepartmentData2Spu() {
        Integer syncDataSwitch = ConfigService.getAppConfig().getIntProperty(SYNC_DEPARTMENT_SWITCH_PROP_NAME, OFF);
        return syncDataSwitch.equals(ON);
    }

}
