package com.newtask;

import com.alibaba.fastjson.TypeReference;
import com.newtask.service.TraderLabelService;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.crm.api.dto.customergroup.TraderLabelEXTDto;
import com.vedeng.system.service.OrganizationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

@JobHandler(value = "traderLabelHandler")
@Component
public class TraderLabelHandler extends AbstractJobHandler {

    private Logger logger= LoggerFactory.getLogger(TraderLabelHandler.class);

    @Value("${crm_url}")
    protected String crmUrl;

    @Value("${b2b_department_name}")
    protected String departmentName;
    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private TraderLabelService traderLabelService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("开始处理客户标签的计算");
        logger.info("开始处理客户标签的计算");
        List<TraderLabelEXTDto> labels=getLabels();
        if(CollectionUtils.isEmpty(labels)){
            XxlJobLogger.log("没有要标签组");
            return ReturnT.SUCCESS;
        }
        List<Integer> organizations=organizationService.getChildOrgByName(departmentName);
        if(StringUtil.isNotBlank(s)&&StringUtil.isNumeric(s)){
            Integer id=Integer.valueOf(s);
            boolean has=false;
            for(TraderLabelEXTDto g:labels){
                if(g!=null&&id.equals(g.getTraderLabelId())){
                    traderLabelService.handleOneLabel(g,organizations);
                    has=true;
                }
            }
            if(!has){
                XxlJobLogger.log("该标签不存在");
            }
            XxlJobLogger.log("结束处理客户标签的计算");
            logger.info("结束处理客户标签的计算");
            return ReturnT.SUCCESS;
        }

        traderLabelService.handleTraderLabel(labels,organizations);
        XxlJobLogger.log("结束处理客户标签的计算");
        logger.info("结束处理客户标签的计算");
        return ReturnT.SUCCESS;
    }

    private List<TraderLabelEXTDto> getLabels(){
        TypeReference<RestfulResult<List<TraderLabelEXTDto>>> typeReference=new TypeReference<RestfulResult<List<TraderLabelEXTDto>>>(){};
        String url=crmUrl+"api/customergroup/queryCustomerLabelList";
        RestfulResult<List<TraderLabelEXTDto>> result = HttpRestClientUtil.restPost(url, typeReference, null, null);
        if(result!=null&&result.isSuccess()){
            return result.getData();
        }
        return null;
    }
}
