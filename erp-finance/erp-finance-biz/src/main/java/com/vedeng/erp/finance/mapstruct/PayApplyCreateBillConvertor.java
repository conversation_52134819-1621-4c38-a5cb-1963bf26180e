package com.vedeng.erp.finance.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.settlement.domain.entity.PayApplyEntity;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface PayApplyCreateBillConvertor extends BaseMapStruct<PayApplyEntity, PayApplyCreateBillDto> {
}
