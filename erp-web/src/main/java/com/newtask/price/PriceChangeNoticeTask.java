package com.newtask.price;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.shiro.util.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.pricecenter.service.SkuPriceModifyRecordService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.price.model.SkuPriceModifyRecord;
import com.vedeng.system.service.UserService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;

/**
 * 价格变动提醒定时任务，每日凌晨启动
 * @ClassName:  PriceChangeNoticeTask   
 * @author: <PERSON><PERSON>yang
 * @date:   2025年4月22日 下午4:22:35    
 * @Copyright:
 */
@JobHandler(value = "priceChangeNoticeTask")
@Component
public class PriceChangeNoticeTask  extends AbstractJobHandler {
	
	/**日志*/
	Logger logger = LoggerFactory.getLogger(PriceChangeNoticeTask.class);
	
	@Autowired
	private SkuPriceModifyRecordService skuPriceModifyRecordService;
	
	@Autowired
	private UserService userService;

	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		XxlJobLogger.log("价格变动提醒定时任务开始");
		logger.info("价格变动提醒定时任务开始");
		long startTime = System.currentTimeMillis();
		try {
			List<SkuPriceModifyRecord> skuPriceModifyRecordList = skuPriceModifyRecordService.getYestedayPriceChangeRecordList();
			if(CollectionUtils.isEmpty(skuPriceModifyRecordList)) {
				XxlJobLogger.log("昨日不价格变动数据");
				return SUCCESS;
			}
			//存在价格变动，获取用户的角色中包含销售的所有用户ID，进行发送消息调价通知
			List<User> userList = userService.getUserByRoleName("销售");
			if(CollectionUtils.isEmpty(userList)) {
				XxlJobLogger.log("无符合角色为销售的用户列表");
				return SUCCESS;
			}
			List<Integer> userIdList = userList.stream().map(temp->temp.getUserId()).collect(Collectors.toList());
			//循环用户信息，进行发送调价通知
			String url = "./price/skuPriceModifyRecord/index.do";
			Map<String,String> map = new HashMap<>();
			map.put("skuNo", "");
			MessageUtil.sendMessage(6014, userIdList, map, url, "");
			long endTime = System.currentTimeMillis();
			XxlJobLogger.log("价格变动提醒定时任务,执行成功！耗时：{}毫秒",endTime - startTime);
			return SUCCESS;
		}catch(Exception e) {
			XxlJobLogger.log("价格变动提醒定时任务失败",e);
			return FAIL;
		}
	}

}
