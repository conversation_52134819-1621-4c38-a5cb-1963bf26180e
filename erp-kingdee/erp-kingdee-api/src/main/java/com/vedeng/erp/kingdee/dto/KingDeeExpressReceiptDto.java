package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收 dto  由erp实际业务转换
 * @date
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@WriteBackField(needBackField = {"FID"})
public class KingDeeExpressReceiptDto extends KingDeeMqBaseDto {

    private Integer id;

    /**
     * 单据内码
     */
    @WriteBackField
    @KingDeeID
    private String fid;
    /**
     * 单据编号
     */
    private String FBillNo;
    /**
     * 发货组织
     */
    private String FQzokOrgid;
    /**
     * 原始订单号
     */
    private String FQzokYsddh;
    /**
     * 归属业务单号
     */
    private String FQzokGsywdh;
    /**
     * 出入库单号
     */
    private String FQzokCrkdh;
    /**
     * 快递号
     */
    private String FQzokKdh;
    /**
     * 业务类型
     */
    private String FQzokYwlx;
    /**
     * 签收时间
     */
    private String FQzokQssj;
    /**
     * 物流公司
     */
    private String FQzokWlgs;
    /**
     * 物料编码
     */
    private String FQzokWlbm;
    /**
     * 序列号
     */
    private String FQzokXlh;
    /**
     * 批次号
     */
    private String FQzokPch;
    /**
     * 发货数量
     */
    private BigDecimal FQzokFhsl;
    /**
     * 收件人
     */
    private String FQzokSjr;
    /**
     * 电话
     */
    private String FQzokDh;
    /**
     * 地址
     */
    private String FQzokDz;
    /**
     * 贝登单据编号
     */
    @BusinessID("F_QZOK_BDDJBH")
    private String FQzokBddjbh;
    /**
     * 是否删除
     */
    private String FQzokSfsc;
    /**
     * 是否计入成本
     */
    private String FQzokSfjrcb;
    /**
     * 是否赠品(默认0，赠品 1)
     */
    private Integer FQzokSfzp;

    @Override
    public String getFormId(){
        return KingDeeFormConstant.QZOK_KDQSD;
    }

}
