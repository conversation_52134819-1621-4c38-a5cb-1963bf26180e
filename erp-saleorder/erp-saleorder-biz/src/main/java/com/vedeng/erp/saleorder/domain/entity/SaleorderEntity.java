package com.vedeng.erp.saleorder.domain.entity;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * T_SALEORDER
 *
 * <AUTHOR>
@Getter
@Setter
public class SaleorderEntity {
    private Integer saleorderId;

    /**
     * 报价订单ID
     */
    private Integer quoteorderId;

    /**
     * 父级订单ID
     */
    private Integer parentId;

    /**
     * 销售订单单号
     */
    private String saleorderNo;

    /**
     * 美年订单号
     */
    private String mSaleorderNo;

    /**
     * 订单类型0销售订单2备货订单3订货订单5耗材商城订单 6EL小医院 7集采线上订单 8集采线下订单 9线下直销订单
     */
    private Integer orderType;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 来源0ERP 1web 2wap
     */
    private Integer source;

    /**
     * 创建人部门ID
     */
    private Integer creatorOrgId;

    /**
     * 创建人部门
     */
    private String creatorOrgName;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 归属ERP用户部门
     */
    private String orgName;

    /**
     * 归属ERP用户ID
     */
    private Integer userId;

    /**
     * 生效时归属人部门ID
     */
    private Integer validOrgId;

    /**
     * 生效时归属人部门
     */
    private String validOrgName;

    /**
     * 生效时归属人
     */
    private Integer validUserId;

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 生效时间
     */
    private Long validTime;

    /**
     * 订单完结时间
     */
    private Long endTime;

    /**
     * 订单状态：0待确认（默认）、1进行中、2已完结、3已关闭 4待用户确认
     */
    private Integer status;

    /**
     * 采购状态0未采购（默认）、1部分采购、2已采购
     */
    private Integer purchaseStatus;

    /**
     * 锁定状态0未锁定 1已锁定
     */
    private Integer lockedStatus;

    /**
     * 开票状态0未开票 1部分开票 2全部开票
     */
    private Integer invoiceStatus;

    /**
     * 开票时间
     */
    private Long invoiceTime;

    /**
     * 付款状态(收款状态) 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 付款时间
     */
    private Long paymentTime;

    /**
     * 发货状态0未发货 1部分发货 2全部发货
     */
    private Integer deliveryStatus;

    /**
     * 发货时间
     */
    private Long deliveryTime;

    /**
     * 是否客户主动确认收货0否1是
     */
    private Integer isCustomerArrival;

    /**
     * 客户收货状态0未收货 1部分收货 2全部收货
     */
    private Integer arrivalStatus;

    /**
     * 客户收货时间
     */
    private Long arrivalTime;

    /**
     * 售后状态 0无 1售后中 2售后完成 3售后关闭
     */
    private Integer serviceStatus;

    /**
     * 含有账期支付 0无 1有
     */
    private Integer haveAccountPeriod;

    /**
     * 是否全部结款0未全部结款 1全部结款(含账期结款、尾款)
     */
    private Integer isPayment;

    /**
     * 总额
     */
    private BigDecimal totalAmount;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户类型 426科研医疗 427临床医疗 (字典库)
     */
    private Integer customerType;

    /**
     * 客户性质 465分销 466终端 （字典库）
     */
    private Integer customerNature;

    /**
     * 交易者名称
     */
    private String traderName;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String traderContactMobile;

    /**
     * 电话
     */
    private String traderContactTelephone;

    /**
     * 联系地址ID
     */
    private Integer traderAddressId;

    /**
     * 客户地区最小级ID
     */
    private Integer traderAreaId;

    /**
     * 客户地区
     */
    private String traderArea;

    /**
     * 联系详细地址(含省市区)
     */
    private String traderAddress;

    /**
     * 客户信息备注
     */
    private String traderComments;

    /**
     * 收货公司ID
     */
    private Integer takeTraderId;

    /**
     * 收货公司名称
     */
    private String takeTraderName;

    /**
     * 收货联系人ID
     */
    private Integer takeTraderContactId;

    /**
     * 收货联系人名称
     */
    private String takeTraderContactName;

    /**
     * 收货联系人手机
     */
    private String takeTraderContactMobile;

    /**
     * 收货联系人电话
     */
    private String takeTraderContactTelephone;

    /**
     * 收货地址ID
     */
    private Integer takeTraderAddressId;

    /**
     * 收货地区最小级ID
     */
    private Integer takeTraderAreaId;

    /**
     * 收货地区
     */
    private String takeTraderArea;

    /**
     * 收货地址
     */
    private String takeTraderAddress;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;

    /**
     * 收票公司ID
     */
    private Integer invoiceTraderId;

    /**
     * 收票公司名称
     */
    private String invoiceTraderName;

    /**
     * 收票联系人ID
     */
    private Integer invoiceTraderContactId;

    /**
     * 收票联系人名称
     */
    private String invoiceTraderContactName;

    /**
     * 收票联系人手机
     */
    private String invoiceTraderContactMobile;

    /**
     * 收票联系人电话
     */
    private String invoiceTraderContactTelephone;

    /**
     * 收票地址ID
     */
    private Integer invoiceTraderAddressId;

    /**
     * 收票地区最小级ID
     */
    private Integer invoiceTraderAreaId;

    /**
     * 收票地区
     */
    private String invoiceTraderArea;

    /**
     * 收票地址
     */
    private String invoiceTraderAddress;

    /**
     * 销售区域ID
     */
    private Integer salesAreaId;

    /**
     * 销售区域
     */
    private String salesArea;

    /**
     * 终端客户ID
     */
    private Integer terminalTraderId;

    /**
     * 终端名称
     */
    private String terminalTraderName;

    /**
     * 终端类型 字典
     */
    private Integer terminalTraderType;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 运费说明 字典表
     */
    private Integer freightDescription;

    /**
     * 发货方式 字典表
     */
    private Integer deliveryType;

    /**
     * 发货类型 字典表 1619
     */
    private Integer deliveryMethod;

    /**
     * 物流公司ID
     */
    private Integer logisticsId;

    /**
     * 付款方式 字典库
     */
    private Integer paymentType;

    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;

    /**
     * 账期支付金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 账期天数
     */
    private Integer periodDay;

    /**
     * 物流代收0否 1是
     */
    private Integer logisticsCollection;

    /**
     * 尾款
     */
    private BigDecimal retainageAmount;

    /**
     * 尾款期限(月)
     */
    private Integer retainageAmountMonth;

    /**
     * 收款备注
     */
    private String paymentComments;

    /**
     * 附加条款
     */
    private String additionalClause;

    /**
     * 物流备注
     */
    private String logisticsComments;

    /**
     * 财务备注
     */
    private String financeComments;

    /**
     * 内部备注
     */
    private String comments;

    /**
     * 开票备注
     */
    private String invoiceComments;

    /**
     * 是否直发 0否 1是
     */
    private Integer deliveryDirect;

    /**
     * 供应商条款
     */
    private String supplierClause;

    /**
     * 有提前采购 0无 1有
     */
    private Integer haveAdvancePurchase;

    /**
     * 0无提前采购需求1待审核2审核通过3审核不通过
     */
    private Integer advancePurchaseStatus;

    /**
     * 提前采购备注
     */
    private String advancePurchaseComments;

    /**
     * 提前采购申请时间
     */
    private Long advancePurchaseTime;

    /**
     * 是否加急0否1是
     */
    private Integer isUrgent;

    /**
     * 加急费用
     */
    private BigDecimal urgentAmount;

    /**
     * 有沟通记录0无 1有
     */
    private Integer haveCommunicate;

    /**
     * 备货原因
     */
    private String prepareComments;

    /**
     * 后期营销计划
     */
    private String marketingPlan;

    /**
     * 审核备注(关闭原因) 字典库
     */
    private Integer statusComments;

    /**
     * 0未同步 1已同步 2同步失败
     */
    private Integer syncStatus;

    /**
     * 是否由物流接口刷新收货状态0否1是
     */
    private Integer logisticsApiSync;

    /**
     * 是否发送签收微信消息0否1是（如果联系人尚未在贝登注册或未查询到WXOPENID也会标记为1）
     */
    private Integer logisticsWxsendSync;

    /**
     * 满足开票时间
     */
    private Long satisfyInvoiceTime;

    /**
     * 满足发货时间
     */
    private Long satisfyDeliveryTime;

    /**
     * 是否参与业绩计算0否1是
     */
    private Integer isSalesPerformance;

    /**
     * 参与业绩计算时间
     */
    private Long salesPerformanceTime;

    /**
     * 参与业绩计算更新时间
     */
    private Long salesPerformanceModTime;

    /**
     * 是否延迟开票0否1是
     */
    private Integer isDelayInvoice;

    /**
     * 开票方式1手动纸质开票、2自动纸质开票、3自动电子发票
     */
    private Integer invoiceMethod;

    /**
     * 锁定原因
     */
    private String lockedReason;

    /**
     * 添加产品成本人员ID集合
     */
    private String costUserIds;

    /**
     * 订单归属人ID
     */
    private Integer ownerUserId;

    /**
     * 收票邮箱
     */
    private String invoiceEmail;

    /**
     * 支付方式：0线上、1线下
     */
    private Integer paymentMode;

    /**
     * 支付方式：1支付宝、2微信、3银行
     */
    private Integer payType;

    /**
     * 耗材订单客户是否申请开票0否1是
     */
    private Integer isApplyInvoice;

    /**
     * 耗材订单客户申请开票时间
     */
    private Long applyInvoiceTime;

    /**
     * 艾迪康订单编号
     */
    private String adkSaleorderNo;

    /**
     * BD订单创建人手机号
     */
    private String createMobile;

    /**
     * BD订单客户备注
     */
    private String bdtraderComments;

    /**
     * 订单关闭原因
     */
    private String closeComments;

    /**
     * BD订单待用户确认状态时间
     */
    private Long bdMobileTime;

    /**
     * 前台用户确认收货时间
     */
    private Long webTakeDeliveryTime;

    /**
     * 活动Id
     */
    private Integer actionId;

    /**
     * 是否使用优惠券  0否  1是
     */
    private Integer isCoupons;

    /**
     * 优惠总金额
     */
    private BigDecimal couponmoney;

    /**
     * 订单原金额
     */
    private BigDecimal originalAmount;

    private String elSaleordreNo;

    /**
     * 打印随货出库单  0不打印   1打印带价格出库单   2打印不带价格出库单 3打印出库单(归属销售为科研购)
     */
    private Integer isPrintout;

    private Integer outIsFlag;

    /**
     * 订单关联业务更新时间
     */
    private Date updateDataTime;

    /**
     * 订单实付金额(已减去实退金额)
     */
    private BigDecimal realPayAmount;

    /**
     * 实退金额
     */
    private BigDecimal realReturnAmount;

    /**
     * 订单实际金额
     */
    private BigDecimal realTotalAmount;

    /**
     * 是否已经推送至PC
     */
    private Integer sendToPc;

    /**
     * 质保金
     */
    private BigDecimal retentionMoney;

    /**
     * 质保金期限（天）
     */
    private Integer retentionMoneyDay;

    /**
     * 货票地址是否相同 0否 1是
     */
    private Integer isSameAddress;

    /**
     * 发票寄送节点 0 全部发货时一次寄送 1 每次发货时分别寄送
     */
    private Integer invoiceSendNode;

    /**
     * 是否删除,0未删除,1已删除
     */
    private Byte isDelete;

    /**
     * 订单账期未还金额(资金流水)
     */
    private BigDecimal nopaybackAmount;

    /**
     * 订单合同模板地址
     */
    private String contractUrl;

    /**
     * HC订单是否自动审核的标志
     */
    private Integer autoAudit;

    /**
     * 是否风控 0否 1销售单风控 2报价转订单风控 3完成风控
     */
    private Integer isRisk;

    /**
     * 风控内容
     */
    private String riskComments;

    /**
     * 风控时间
     */
    private Long riskTime;

    /**
     * 集采待客下单客户编号
     */
    private Integer groupCustomerId;

    /**
     * 合同是否回传0否1是
     */
    private Byte isContractReturn;

    /**
     * 送货单是否回传0否1是
     */
    private Byte isDeliveryorderReturn;

    /**
     * 发货要求，0：立即发货，1：等通知发货
     */
    private Integer deliveryClaim;

    /**
     * 等待截止日期
     */
    private Long deliveryDelayTime;

    /**
     * 订单账期结算方式，1产品发货、2产品开票
     */
    private Byte billPeriodSettlementType;

    /**
     * 是否订单流订单 0否 1是
     */
    private Byte isNew;

    /**
     * 订单是否确认 0否 1是
     */
    private Integer confirmStatus;

    /**
     * 订单确认时间
     */
    private Date confirmTime;

    /**
     * 未盖章合同链接
     */
    private String contractNoStampUrl;

    /**
     * 在线签收状态 0未签收 1部分签收 2全部签收
     */
    private Integer onlineReceiptStatus;

    /**
     * 确认单上传状态：0.未上传；1.部分上传；2.全部上传
     */
    private Integer confirmationFormUpload;

    /**
     * 确认单审核状态：0：待提交审核；1.审核中；2.审核通过；3.审核不通过、
     */
    private Integer confirmationFormAudit;

    /**
     * 确认单提交审核时间
     */
    private Long confirmationSubmitTime;

    /**
     * 赠品单原因，字典表
     */
    private Integer prepareReaseonType;

    /**
     * 合同审核备注
     */
    private String contractReas;

    private Long modTime;

    private Integer updater;

    private Long addTime;

    private Integer creator;

    /**
     * 终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
     */
    private Integer terminalTraderNature;
}