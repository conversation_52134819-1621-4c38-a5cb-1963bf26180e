package com.vedeng.order.model.pi;

import java.math.BigDecimal;

/**
 * 推送迈瑞库存信息
 * <b>Description:</b><br> 
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> dbcenter
 * <br><b>PackageName:</b> com.vedeng.model.pi
 * <br><b>ClassName:</b> StockLog
 * <br><b>Date:</b> 2018年7月25日 下午3:54:26
 */
public class StockLog {
	
    private Integer stockLogId;

    //机型
    private String model;

    //物料编码
    private String materialCode;
    
    //序列号
    private String barcodeFactory;

    //金额
    private BigDecimal amount;

    //库存数量
    private Integer stockNum;
    
    //库存日期
    private Long addTime;
    
    /**
     * 产品类别
     */
    private Integer goodsType;
    
    private Integer type;//商品类型 1器械设备 2.试剂耗材

    private String batchNumber;//批次号
    
    private String status;

    private String message;
    
    private Integer currentCount;

    public Integer getGoodsType() {
		return goodsType;
	}

	public void setGoodsType(Integer goodsType) {
		this.goodsType = goodsType;
	}

	public String getBatchNumber() {
        return batchNumber;
    }

    public void setBatchNumber(String batchNumber) {
        this.batchNumber = batchNumber;
    }

    public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getCurrentCount() {
		return currentCount;
	}

	public void setCurrentCount(Integer currentCount) {
		this.currentCount = currentCount;
	}

	public String getMaterialCode() {
		return materialCode;
	}

	public void setMaterialCode(String materialCode) {
		this.materialCode = materialCode;
	}

	public Integer getStockNum() {
		return stockNum;
	}

	public void setStockNum(Integer stockNum) {
		this.stockNum = stockNum;
	}

	public Integer getStockLogId() {
        return stockLogId;
    }

    public void setStockLogId(Integer stockLogId) {
        this.stockLogId = stockLogId;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getBarcodeFactory() {
        return barcodeFactory;
    }

    public void setBarcodeFactory(String barcodeFactory) {
        this.barcodeFactory = barcodeFactory == null ? null : barcodeFactory.trim();
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }
}
