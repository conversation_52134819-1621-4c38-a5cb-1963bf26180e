package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/25
 */
@Data
public class CrmQuoteShareDto {


    private String quoteorderNo; //联系人-报价单号

    private String traderName; //联系人-客户名

    private String traderContactName; //联系人-姓名

    private String mobile;   //联系人-手机号

    private BigDecimal quoteMoney;//报价金额

    private Integer goodsCount;//商品种类

    private Integer totalNum;//产品总数

    private String freightDescriptionName;//运费说明

    private String shardUrl ;  //分享的地址${confirmOrderUrl}bj-${quoteordervo.quoteorderNo}.html

    private String now; //报价有效期-开始时间 yyyy-mm-dd

    private String end;//报价有效期-结束时间 yyyy-mm-dd

    private Integer day;//有效期天数

    private Date onlineShareTime;//分享的时间



}
