package com.vedeng.order.dao;

import com.vedeng.order.model.RBuyorderSaleorder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface RBuyorderSaleorderMapper {
    int deleteByPrimaryKey(Integer rBuyorderJSaleorderId);

    int insert(RBuyorderSaleorder record);

    int insertSelective(RBuyorderSaleorder record);

    RBuyorderSaleorder selectByPrimaryKey(Integer rBuyorderJSaleorderId);

    int updateByPrimaryKeySelective(RBuyorderSaleorder record);

    int updateByPrimaryKey(RBuyorderSaleorder record);

	/**
	 *
	 * <b>Description:</b>查询采购单对应的直发的销售商品
	 * @param buyorderId
	 * @return List<RBuyorderSaleorder>
	 * @Note
	 * <b>Author：</b> scott.zhu
	 * <b>Date:</b> 2018年11月22日 下午8:34:47
	 */
	List<RBuyorderSaleorder> getRBuyorderSaleorderInfoList(Integer buyorderId);

	List<RBuyorderSaleorder> getRBuyorderSaleorderInfoListByBuyorderIds(@Param("list") List<Integer> buyorderIds);
	/**
	 * <b>Description:</b><br> 查询关系列表buyorderId
	 * @param buyorderId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年11月9日 下午4:20:08
	 */
	List<RBuyorderSaleorder> getRBuyorderSaleorderListByParam(@Param("buyorderId")Integer buyorderId);

	List<Integer> getBuyOrderGoodsIdBySaleOrderGoodsId(@Param("saleOrderGoodsId") Integer saleOrderGoodsId);

	/**
	 * <b>Description:</b><br> 查询以采购商品的数量
	 * @param buyorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月31日 上午11:47:23
	 */
	Integer getBuyorderGoodsNumByParam(@Param("buyorderGoodsId")Integer buyorderGoodsId);

	/**
	 * <b>Description:</b><br> 查询以采购商品的数量
	 * @param saleorderGoodsId
	 * @return
	 * @Note
	 * <b>Author:</b> east
	 * <br><b>Date:</b> 2017年7月31日 上午11:47:23
	 */
	Integer getBuyorderGoodsNum(@Param("saleorderGoodsId")Integer saleorderGoodsId);

	/**
	 * @auhor thor
	 * @desc 根据销售单商品id查询对应实际关联采购单商品ID
	 * @param saleorderGoodsIds
	 * @return
	 */
	List<Integer> queryBuyorderGoodsIdsBySaleorderGoodsIds(@Param("saleorderGoodsIds") List<Integer> saleorderGoodsIds);

	/**
	 * 根据buyorderGoodsId查询销售单id
	 */
	List<Integer> getSaleOrderIdByBuyOrderGoodsId(@Param("buyorderGoodsId")Integer buyorderGoodsId);

	/**
	 * 根据销售单Id和sku查询对应的采购单
	 */
	List<Map<String, Integer>> getBuyorderIdBySaleorderIdAndGoodsId(@Param("goodsId")Integer goodsId, @Param("saleorderId")Integer saleorderId);

	/**
	 * <AUTHOR>
	 * @desc 根据采购单商品id查询销售单商品id（单个）
	 * @param buyorderGoodsId
	 * @return
	 */
	Integer querySaleorderGoodsIdByBuyorderGoodsId(Integer buyorderGoodsId);

	List<Integer> getBuyorderUserBySaleorderGoodsIds(@Param("saleorderGoodsIds") List<Integer> saleorderGoodsIds);

	List<Integer> getBuyorderUserBySaleorderIdAndDirect(Integer saleorderId);
}