<!DOCTYPE html>
<%@ page language="java" import="java.util.*" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>贝登ERP</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/general.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/login.css?rnd=${resourceVersionKey}">
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
    <script type='text/javascript'>
		if (top.location != self.location)
		{
			top.location=self.location;
		}
	</script>
</head>

<body>
	 <div class="login-con">
        <div class="login-main">
            <div class="login-logo"></div>
            <div class="login-form">
                <form method="post" action="${pageContext.request.contextPath}/system/user/resetPassword.do">
                    <ul>
                        <li>
                            <div class="bor password">
                                <input type="text" placeholder="旧密码" name="oldPassword"/>
                            </div>
                        </li>
                        <li>
                            <div class="bor password">
                                <input type="text" id="password" placeholder="新密码" name="password" />
                            </div>
                        </li>
                        <li>
                            <div class="bor password">
                                <input type="text" id="repassword" placeholder="确认新密码" name="repassword" />
                            </div>
                        </li>
                    </ul>
                 <div id="warning"  class="warning">${msg}</div>
                  <div class="warning">您的密码过于简单，请重置密码！</div>
                    <div class="submit">

                        <button type="submit" onclick="checkPassword(event)">确认修改</button>
                    </div>
                </form>
            </div>
        </div>
        <div class="login_copyright">版权所有 南京贝登医疗股份有限公司 &copy;2020 All Right Reserved</div>
    </div>

</body>
<script type="text/javascript">
   function checkPassword(event) {
       if ($('#password').val() != $('#repassword').val()){
           $('#warning').html('两次输入密码不一致');
           event.preventDefault();
       }
   }
</script>
</html>
