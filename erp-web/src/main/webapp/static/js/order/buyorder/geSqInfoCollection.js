//添加序列号
$(function (){
    var oTable = document.getElementById("num");
    for(var i=1;i<oTable.rows.length;i++){
        oTable.rows[i].cells[0].innerHTML = (i);
    }
});


function addNum(){
    var oTable = document.getElementById("num");
    for(var i=1;i<oTable.rows.length;i++){
        oTable.rows[i].cells[0].innerHTML = (i);
    }
}
//增加主机和序列号输入框
function addMaster(){
    // $('.tabale').append('<div><input  type="text" name="text" + j + ""/><input type="button" onclick="deltext(this)" value="删除"></div>');
    //alert("我是增加主机")
    var length=$("#num").find("tr").length;
    $('#num').append('<tr><td>'+length+'</td><td><input type="text" name="masterSno"/></td><td><div style="text-align: left"><input type="text" style="width: 200px;margin: 10px auto" name="slaveSno"><img src="/static/images/addicon.png" onclick="addSlave(this)" style="width: 18px;height: 18px;margin-left: 13px"><img src="/static/images/subicon.png" onclick="delSlave(this)" style="width: 20px;height: 20px;margin-left: 13px"></div></td><td><input type="button" onclick="delMaster(this)" value="删除主机"></td></tr>');
}

//删除主机
function delMaster(e) {
    if (confirm("删除主机和对应探头的序列号，请谨慎操作！")) {
        $(e).parent().parent().remove();
        addNum();
    }else {
        return false;
    }
}

//添加探头
function addSlave(e){
    $(e).parent().parent().append('<div style="text-align: left"><input style="width: 200px;margin: 5px auto" type="text" name="slaveSno"/><img src="/static/images/subicon.png" onclick="delSlave(this)" style="width: 20px;height: 20px;margin-left: 13px"></div></div>');
}

//删除探头
function delSlave(e) {
    //var divNum=$(e).parent().parent().find('div').length;
    var divNum=$(e).parent().prevAll().length;
    console.log(divNum);
    if (divNum==0){
        $(e).parent().children(':first').val('');
    } else {
        $(e).parent().remove();
    }
}

/*  //将input改为可编辑状态
  function editInfo(){
      alert("我是编辑信息")
  }

  //将input改为不可编辑状态
  function saveInfo(){
      alert("我是保存信息")
  }
*/

//取消操作
function giveUp(e){
    parent.layer.close(index);
}

//提交表单
function sureEdit(sku,buyorderGoodsId,goodsId){
    checkLogin();
    var masterSlaves=[];
    var result = true;
    var masterS=[];
    var allSlave=[];
    var masterSlavejson;
    //循环tr
    $("#num tr:gt(0)").each(function () {
        var masterSno;
        var slaveSno=[];
        var masterSlave={vBuyorderSncodeMaster:"",vBuyorderSncodeSlaves:""};
        //获取tr中的主机序列号和探头序列号
        masterSno=$(this).find("input[name='masterSno']").first().val().trim();
        if (masterSno==''){
            result=false;
            alert("主机序列号不可为空");
            return false ;
        }
        //存放所有主机
        masterS.push(masterSno);
        //取探头值
        $(this).find("input[name='slaveSno']").each(function () {
            if (this.value !=''){
                var slaveSnoV=this.value.trim();
                slaveSno.push(slaveSnoV);
                allSlave.push(slaveSnoV);
            }
        });
        if (!result){
            return false;
        }
        masterSlave.vBuyorderSncodeMaster=masterSno;
        masterSlave.vBuyorderSncodeSlaves=slaveSno;
        masterSlaves.push(masterSlave);
    });
    if (!result){
        return false;
    }

    //判断主机是否重复
    for (var i = 0; i < masterS.length; i++) {
        if(masterS.indexOf(masterS[i]) != i) {
            alert("输入的主机序列号[ "+masterS[i]+" ]重复！");
            result=false;
            return false;
        }
        if (!checkMasterSno(masterS[i],buyorderGoodsId)){
            layer.alert("输入的主机序列号[ "+masterS[i]+" ]已存在！")
            result=false;
            return false;
        }
    }
    if (!result){
        return false;
    }

    masterSlavejson=JSON.stringify(masterSlaves);

    //判断探头是否重复
    for (var m = 0; m < allSlave.length; m++) {
        if(allSlave.indexOf(allSlave[m]) != m) {
            alert("输入的探头序列号[ "+allSlave[m]+" ]重复！");
            result=false;
            return false;
        }
        if (!checkSlaveSno(allSlave[m],masterSlavejson)){
            layer.alert("输入的探头序列号[ "+allSlave[m]+" ]已存在！")
            result=false;
            return false;
        };
    }
    if (!result){
        return false;
    }

    //提交数据
    var index = parent.layer.getFrameIndex(window.name);
        $.ajax({
            type: "POST",
            url: page_url + "/order/georder/saveSncode.do",
            data: {"masterSlavejson": masterSlavejson,"sku": sku,"buyorderGoodsId": buyorderGoodsId,"goodsId": goodsId},
            dataType: 'json',
            success: function (data) {
                if (data.code == 0) {
                    parent.location.reload();   //刷新父级页面
                    parent.layer.close(index);  //关闭当前页面
                } else {
                    layer.alert(data.message);
                    return false;
                }
                },
            error: function (data) {
                alert("没有权限");
            }
        });
}


function checkMasterSno(masterSno,buyorderGoodsId) {
    var flag=true;
    $.ajax({
        type: "POST",
        url: page_url + "/order/georder/checkMasterSno.do",
        data: {"masterSno": masterSno,"buyorderGoodsId": buyorderGoodsId},
        dataType: 'json',
        async: false,
        success: function (data) {
            if (data.code == 0) {
                flag=false;
            }
        },
        error: function (data) {
            flag=false;
            alert("没有权限");
        }
    });
    if (!flag){
        return false;
    }
    return flag;
}

function checkSlaveSno(slaveSno,masterSlavejson) {
    var flag=true;
    $.ajax({
        type: "POST",
        url: page_url + "/order/georder/checkSlaveSno.do",
        data: {"slaveSno": slaveSno,"masterSlavejson": masterSlavejson},
        dataType: 'json',
        async: false,
        success: function (data) {
            if (data.code == 0) {
                flag=false;
            }
        },
        error: function (data) {
            flag=false;
            alert("没有权限");
        }
    });

    if (!flag){
        return false
    }
    return true;
}