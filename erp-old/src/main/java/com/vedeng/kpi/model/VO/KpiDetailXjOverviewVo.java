package com.vedeng.kpi.model.VO;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

public class KpiDetailXjOverviewVo extends KpiDetailBaseOverviewVo {

    private static final long serialVersionUID = 4841680695428100309L;

    private String userName;

    private Integer successChance;

    private Integer failChance;

    private Integer monthReceiveNum;

    private BigDecimal transProgress;

    private Integer rangeCustomerNum;

    private BigDecimal groupTransProgress;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getSuccessChance() {
        return successChance;
    }

    public void setSuccessChance(Integer successChance) {
        this.successChance = successChance;
    }

    public Integer getFailChance() {
        return failChance;
    }

    public void setFailChance(Integer failChance) {
        this.failChance = failChance;
    }

    public Integer getMonthReceiveNum() {
        return monthReceiveNum;
    }

    public void setMonthReceiveNum(Integer monthReceiveNum) {
        this.monthReceiveNum = monthReceiveNum;
    }

    public BigDecimal getTransProgress() {
        return transProgress;
    }

    public void setTransProgress(BigDecimal transProgress) {
        this.transProgress = transProgress;
    }

    public Integer getRangeCustomerNum() {
        return rangeCustomerNum;
    }

    public void setRangeCustomerNum(Integer rangeCustomerNum) {
        this.rangeCustomerNum = rangeCustomerNum;
    }

    public BigDecimal getGroupTransProgress() {
        return groupTransProgress;
    }

    public void setGroupTransProgress(BigDecimal groupTransProgress) {
        this.groupTransProgress = groupTransProgress;
    }
}
