<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSaleServiceStandardApplyMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSaleServiceStandardApply" >
    <id column="SERVICE_STANDARD_APPLY_ID" property="serviceStandardApplyId" jdbcType="BIGINT" />
    <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_INSTALL_TYPE" property="installPolicyInstallType" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_INSTALL_FEE" property="installPolicyInstallFee" jdbcType="DECIMAL" />
    <result column="INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION" property="installPolicyHaveInstallationQualification" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_FREE_REMOTE_INSTALL" property="installPolicyFreeRemoteInstall" jdbcType="INTEGER" />
    <result column="INSTALL_POLICY_RESPONSE_TIME" property="installPolicyResponseTime" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_VISIT_TIME" property="installPolicyVisitTime" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_INSTALL_TIME" property="installPolicyInstallTime" jdbcType="VARCHAR" />
    <result column="TECHNICAL_DIRECT_SUPPLY_MAINTAIN" property="technicalDirectSupplyMaintain" jdbcType="INTEGER" />
    <result column="TECHNICAL_DIRECT_RESPONSE_TIME" property="technicalDirectResponseTime" jdbcType="VARCHAR" />
    <result column="TECHNICAL_DIRECT_EFFECT_TIME" property="technicalDirectEffectTime" jdbcType="VARCHAR" />
    <result column="INSTALL_POLICY_INSTALL_AREA_COMMENT" property="installPolicyInstallAreaComment" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_IS_GUARANTEE" property="guaranteePolicyIsGuarantee" jdbcType="INTEGER" />
    <result column="GUARANTEE_POLICY_GUARANTEE_TYPE" property="guaranteePolicyGuaranteeType" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD" property="guaranteePolicyHostGuaranteePeriod" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD" property="guaranteePolicyPartsGuaranteePeriod" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_CYCLE_CALTYPE" property="guaranteePolicyCycleCaltype" jdbcType="INTEGER" />
    <result column="GUARANTEE_POLICY_AREA" property="guaranteePolicyArea" jdbcType="INTEGER" />
    <result column="GUARANTEE_POLICY_AREA_COMMENT" property="guaranteePolicyAreaComment" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_RESPONSE_TIME" property="guaranteePolicyResponseTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_VISIT_TIME" property="guaranteePolicyVisitTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_REPAIRE_TIME" property="guaranteePolicyRepaireTime" jdbcType="VARCHAR" />
    <result column="GUARANTEE_POLICY_REPAIRE_COMMENT" property="guaranteePolicyRepaireComment" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_SUPPORT_RETURN" property="returnPolicySupportReturn" jdbcType="INTEGER" />
    <result column="RETURN_POLICY_CONDITION" property="returnPolicyCondition" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_NEED_IDENTIFY" property="returnPolicyNeedIdentify" jdbcType="INTEGER" />
    <result column="RETURN_POLICY_IDENTIFY_TYPE" property="returnPolicyIdentifyType" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_RETURN_PERIOD" property="returnPolicyReturnPeriod" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_CYCLE_CALTYP" property="returnPolicyCycleCaltyp" jdbcType="INTEGER" />
    <result column="RETURN_POLICY_PACKAGING_REQUIREMENTS" property="returnPolicyPackagingRequirements" jdbcType="VARCHAR" />
    <result column="RETURN_POLICY_RETURN_COMMENTS" property="returnPolicyReturnComments" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_SUPPORT_CHANGE" property="exchangePolicySupportChange" jdbcType="INTEGER" />
    <result column="EXCHANGE_POLICY_EXCHANGE_CONTITION" property="exchangePolicyExchangeContition" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_NEED_IDENTIFY" property="exchangePolicyNeedIdentify" jdbcType="INTEGER" />
    <result column="EXCHANGE_POLICY_IDENTIFY_TYPE" property="exchangePolicyIdentifyType" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_EXCHANGE_PERIOD" property="exchangePolicyExchangePeriod" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_CYCLE_CALTYP" property="exchangePolicyCycleCaltyp" jdbcType="INTEGER" />
    <result column="EXCHANGE_POLICY_PACKAGING_REQUIREMENTS" property="exchangePolicyPackagingRequirements" jdbcType="VARCHAR" />
    <result column="EXCHANGE_POLICY_EXCHANGE_COMMENTS" property="exchangePolicyExchangeComments" jdbcType="VARCHAR" />
    <result column="PAROLE_POLICY_SUPPORT_REPAIR" property="parolePolicySupportRepair" jdbcType="INTEGER" />
    <result column="PAROLE_POLICY_SUPPORT_RENOVATION" property="parolePolicySupportRenovation" jdbcType="INTEGER" />
    <result column="PAROLE_POLICY_SUPPLY_BOX" property="parolePolicySupplyBox" jdbcType="INTEGER" />
    <result column="PAROLE_POLICY_SUPPLY_ATTACHMENT" property="parolePolicySupplyAttachment" jdbcType="INTEGER" />
    <result column="OVERDUE_POLICY_SUPPLY_BACKUP" property="overduePolicySupplyBackup" jdbcType="VARCHAR" />
    <result column="OVERDUE_POLICY_DETAIL" property="overduePolicyDetail" jdbcType="VARCHAR" />
    <result column="SUPPLY_AFTER_SALE_IS_MAINTAIN" property="supplyAfterSaleIsMaintain" jdbcType="INTEGER" />
    <result column="AFTER_SALE_STANDARD_STATUS" property="afterSaleStandardStatus" jdbcType="INTEGER" />

    <result column="LATEST_VERIFY_PASS_TIME" property="latestVerifyPassTime" jdbcType="VARCHAR" />
    <result column="AUDITOR" property="auditor" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATOR" property="updator" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="VARCHAR" />
    <result column="MOD_TIME" property="modTime" jdbcType="VARCHAR" />
    <result column="AFTER_SALE_SERVICE_LABELS" property="afterSaleServiceLabels" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    SERVICE_STANDARD_APPLY_ID, SKU_NO, INSTALL_POLICY_INSTALL_TYPE, INSTALL_POLICY_INSTALL_FEE,
    INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION, INSTALL_POLICY_FREE_REMOTE_INSTALL,
    INSTALL_POLICY_RESPONSE_TIME, INSTALL_POLICY_VISIT_TIME, INSTALL_POLICY_INSTALL_TIME,
    TECHNICAL_DIRECT_SUPPLY_MAINTAIN, TECHNICAL_DIRECT_RESPONSE_TIME, TECHNICAL_DIRECT_EFFECT_TIME, INSTALL_POLICY_INSTALL_AREA_COMMENT,
    GUARANTEE_POLICY_IS_GUARANTEE, GUARANTEE_POLICY_GUARANTEE_TYPE, GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD,
    GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD, GUARANTEE_POLICY_CYCLE_CALTYPE,
    GUARANTEE_POLICY_AREA, GUARANTEE_POLICY_AREA_COMMENT, GUARANTEE_POLICY_RESPONSE_TIME,
    GUARANTEE_POLICY_VISIT_TIME, GUARANTEE_POLICY_REPAIRE_TIME, GUARANTEE_POLICY_REPAIRE_COMMENT,
    RETURN_POLICY_SUPPORT_RETURN, RETURN_POLICY_CONDITION, RETURN_POLICY_NEED_IDENTIFY,
    RETURN_POLICY_IDENTIFY_TYPE, RETURN_POLICY_RETURN_PERIOD, RETURN_POLICY_CYCLE_CALTYP,
    RETURN_POLICY_PACKAGING_REQUIREMENTS, RETURN_POLICY_RETURN_COMMENTS, EXCHANGE_POLICY_SUPPORT_CHANGE,
    EXCHANGE_POLICY_EXCHANGE_CONTITION, EXCHANGE_POLICY_NEED_IDENTIFY, EXCHANGE_POLICY_IDENTIFY_TYPE,
    EXCHANGE_POLICY_EXCHANGE_PERIOD, EXCHANGE_POLICY_CYCLE_CALTYP, EXCHANGE_POLICY_PACKAGING_REQUIREMENTS,
    EXCHANGE_POLICY_EXCHANGE_COMMENTS, PAROLE_POLICY_SUPPORT_REPAIR, PAROLE_POLICY_SUPPORT_RENOVATION,
    PAROLE_POLICY_SUPPLY_BOX, PAROLE_POLICY_SUPPLY_ATTACHMENT, OVERDUE_POLICY_SUPPLY_BACKUP,
    OVERDUE_POLICY_DETAIL, SUPPLY_AFTER_SALE_IS_MAINTAIN, AFTER_SALE_STANDARD_STATUS,
    LATEST_VERIFY_PASS_TIME,AUDITOR, CREATOR, UPDATOR, ADD_TIME, MOD_TIME,AFTER_SALE_SERVICE_LABELS
  </sql>

  <select id="querylistPage" parameterType="Map"
                          resultType="com.vedeng.aftersales.model.dto.AfterSaleServiceStandardApplyDto">
    SELECT
      sku.SKU_NO,
      sku.SHOW_NAME,
      B.BRAND_NAME,
      sku.SPEC,
      sku.IS_INSTALLABLE,
      apply.SUPPLY_AFTER_SALE_IS_MAINTAIN,
      apply.AFTER_SALE_STANDARD_STATUS,
      apply.INSTALL_POLICY_INSTALL_TYPE,
      apply.INSTALL_POLICY_INSTALL_FEE,
      M.USERNAME as MANAGER_NAME,
      U.USERNAME as ASSIT_NAME,
      sku.HAS_AFTER_SALE_SERVICE_LABEL
    FROM V_CORE_SKU sku
    JOIN V_CORE_SPU P ON sku.SPU_ID=P.SPU_ID
    <if test="serviceStandardDto.subordinates != null and serviceStandardDto.subordinates.size > 0">
      AND (P.ASSIGNMENT_ASSISTANT_ID IN
      <foreach collection="serviceStandardDto.subordinates" item="item" index="index" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>OR P.ASSIGNMENT_MANAGER_ID IN
      <foreach collection="serviceStandardDto.subordinates" item="item" index="index" open="(" separator="," close=")">
        #{item,jdbcType=INTEGER}
      </foreach>
        )
    </if>
    LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
    LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
    LEFT JOIN T_BRAND B ON B.BRAND_ID=P.BRAND_ID
    LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_APPLY apply ON  sku.SKU_NO = apply.SKU_NO
    <if test="serviceStandardDto.buzTypeFromTodoList != null and serviceStandardDto.buzTypeFromTodoList > 0 and serviceStandardDto.goodsLevelFromTodoList != null and serviceStandardDto.goodsLevelFromTodoList > 0">
      JOIN T_TODO_LIST L ON sku.SKU_ID = L.BUZ_ID AND L.STATUS = 0 AND L.BUZ_TYPE = #{serviceStandardDto.buzTypeFromTodoList,jdbcType=INTEGER} AND
      sku.GOODS_LEVEL_NO = #{serviceStandardDto.goodsLevelFromTodoList}
    </if>
    <where>
      <if test="serviceStandardDto.keyWord!=null and serviceStandardDto.keyWord!=''">
        AND ( sku.sku_no LIKE CONCAT('%',#{serviceStandardDto.keyWord,jdbcType=VARCHAR},'%')
           or sku.sku_name LIKE CONCAT('%',#{serviceStandardDto.keyWord,jdbcType=VARCHAR},'%')
           or B.BRAND_NAME LIKE CONCAT('%',#{serviceStandardDto.keyWord,jdbcType=VARCHAR},'%')
           or sku.SPEC LIKE CONCAT('%',#{serviceStandardDto.keyWord,jdbcType=VARCHAR},'%')
        )
      </if>

      <if test="serviceStandardDto.productInstall != null and serviceStandardDto.productInstall != ''">
        AND sku.IS_INSTALLABLE = #{serviceStandardDto.productInstall,jdbcType=VARCHAR}
      </if>
      <if test="serviceStandardDto.supplyMatainment != null and serviceStandardDto.supplyMatainment != ''">
        <if test="serviceStandardDto.supplyMatainment == 0">
          AND (apply.SUPPLY_AFTER_SALE_IS_MAINTAIN = #{serviceStandardDto.supplyMatainment,jdbcType=VARCHAR} or apply.SUPPLY_AFTER_SALE_IS_MAINTAIN is NULL)
        </if>
        <if test="serviceStandardDto.supplyMatainment == 1">
          AND apply.SUPPLY_AFTER_SALE_IS_MAINTAIN = #{serviceStandardDto.supplyMatainment,jdbcType=VARCHAR}
        </if>
      </if>
      <if test="serviceStandardDto.status != null and serviceStandardDto.status != ''">
        <if test="serviceStandardDto.status == 0">
          AND (apply.AFTER_SALE_STANDARD_STATUS = #{serviceStandardDto.status,jdbcType=VARCHAR} or apply.AFTER_SALE_STANDARD_STATUS is NULL)
        </if>

        <if test="serviceStandardDto.status != 0">
          AND apply.AFTER_SALE_STANDARD_STATUS = #{serviceStandardDto.status,jdbcType=VARCHAR}
        </if>
      </if>

      <if test="serviceStandardDto.installTrainType != null and serviceStandardDto.installTrainType != ''">
        <if test="serviceStandardDto.installTrainType == 3">
          AND apply.INSTALL_POLICY_INSTALL_TYPE IS NULL
        </if>
        <if test="serviceStandardDto.installTrainType != 3">
          AND apply.INSTALL_POLICY_INSTALL_TYPE = #{serviceStandardDto.installTrainType,jdbcType=VARCHAR}
        </if>
      </if>

      <if test="serviceStandardDto.categoryId != null">
        AND P.CATEGORY_ID = #{serviceStandardDto.categoryId,jdbcType=INTEGER}
      </if>

      <if test="serviceStandardDto.auditPerson!=null and serviceStandardDto.auditPerson!=''">
        <!-- 审核人 -->
        AND FIND_IN_SET(#{serviceStandardDto.auditPerson,jdbcType=VARCHAR},apply.AUDITOR) != 0
      </if>

      <if test="serviceStandardDto.productManagerName!=null and serviceStandardDto.productManagerName!=''">
        <!-- 归属经理-->
        AND (
          M.USERNAME = #{serviceStandardDto.productManagerName,jdbcType=VARCHAR}
          or U.USERNAME = #{serviceStandardDto.productManagerName,jdbcType=VARCHAR}
        )
      </if>
        <if test="serviceStandardDto.hasAfterSaleServiceLabel != null and serviceStandardDto.hasAfterSaleServiceLabel == 1">
            AND sku.HAS_AFTER_SALE_SERVICE_LABEL = 1
        </if>
        <if test="serviceStandardDto.hasAfterSaleServiceLabel != null and serviceStandardDto.hasAfterSaleServiceLabel == 0">
            AND (sku.HAS_AFTER_SALE_SERVICE_LABEL = 0 OR sku.HAS_AFTER_SALE_SERVICE_LABEL is null)
        </if>
    </where>
    order by sku.MOD_TIME desc
  </select>


  <select id="querySimilarProductListPage" parameterType="Map"
          resultType="com.vedeng.aftersales.model.dto.AfterSaleServiceStandardApplyDto">
    SELECT
      sku.SKU_NO,
      sku.SHOW_NAME,
      B.BRAND_NAME,
      sku.SPEC,
      apply.INSTALL_POLICY_INSTALL_FEE,
      apply.LATEST_VERIFY_PASS_TIME
    FROM V_CORE_SKU sku
    LEFT JOIN V_CORE_SPU P ON sku.SPU_ID=P.SPU_ID
    LEFT JOIN T_BRAND B ON B.BRAND_ID=P.BRAND_ID
    LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_APPLY apply ON  sku.SKU_NO = apply.SKU_NO
    <where>
      P.CATEGORY_ID = #{queryDto.categoryId,jdbcType=INTEGER}
      <if test="queryDto.keyWord!=null and queryDto.keyWord!=''">
        AND ( sku.sku_no   LIKE CONCAT('%',#{queryDto.keyWord,jdbcType=VARCHAR},'%')
           or sku.sku_name LIKE CONCAT('%',#{queryDto.keyWord,jdbcType=VARCHAR},'%')
        )
      </if>
      <if test="queryDto.brandId != null">
        AND P.BRAND_ID = #{queryDto.brandId,jdbcType=BIGINT}
      </if>
      <if test="queryDto.spec != null and queryDto.spec!=''">
        AND sku.SPEC LIKE CONCAT('%',#{queryDto.spec,jdbcType=VARCHAR},'%')
      </if>
    </where>
  </select>

  <select id="maintainInstallAreaListPage" parameterType="Map"
          resultType="com.vedeng.aftersales.model.dto.AfterSaleServiceStandardApplyDto">
    SELECT
      sku.SKU_NO,
      sku.SHOW_NAME,
      sku.SPEC,
      sku.CHECK_TIME as latestVerifyPassTime,
      AREA.PROVINCE_CITY_JSONVALUE
    FROM V_CORE_SKU sku
    LEFT JOIN V_CORE_SPU P ON sku.SPU_ID=P.SPU_ID
    LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_INFO INFO on INFO.SKU_NO = sku.SKU_NO
    LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_INFO_INSTALL_AREA AREA ON AREA.SERVICE_STANDARD_INFO_ID=INFO.SERVICE_STANDARD_INFO_ID
    <where>
      sku.CHECK_STATUS = 3 and P.BRAND_ID = #{queryDto.brandId,jdbcType=INTEGER}
      <if test="queryDto.keyWord!=null and queryDto.keyWord!=''">
        AND ( sku.sku_no   LIKE CONCAT('%',#{queryDto.keyWord,jdbcType=VARCHAR},'%')
            or sku.sku_name LIKE CONCAT('%',#{queryDto.keyWord,jdbcType=VARCHAR},'%')
            or sku.SPEC LIKE CONCAT('%',#{serviceStandardDto.keyWord,jdbcType=VARCHAR},'%')
        )
      </if>
    </where>
  </select>


  <select id="queryMaxAndMinInstallFee" parameterType="com.vedeng.aftersales.model.dto.ReferenceSimilarProductQueryDto"
          resultType="Map">
    SELECT
      MAX(apply.INSTALL_POLICY_INSTALL_FEE) maxInstallFee,
      MIN(apply.INSTALL_POLICY_INSTALL_FEE) minInstallFee
    FROM V_CORE_SKU sku
    LEFT JOIN V_CORE_SPU P ON sku.SPU_ID=P.SPU_ID
    LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_APPLY apply ON  sku.SKU_NO = apply.SKU_NO
    <where>
      P.CATEGORY_ID = #{categoryId,jdbcType=INTEGER}
      <if test="keyWord!=null and keyWord!=''">
        AND ( sku.sku_no   LIKE CONCAT('%',#{keyWord,jdbcType=VARCHAR},'%')
        or sku.sku_name LIKE CONCAT('%',#{keyWord,jdbcType=VARCHAR},'%')
        )
      </if>
      <if test="brandId != null">
        AND P.BRAND_ID = #{brandId,jdbcType=BIGINT}
      </if>
      <if test="spec != null and spec!=''">
        AND sku.SPEC LIKE CONCAT('%',#{spec,jdbcType=VARCHAR},'%')
      </if>
    </where>
  </select>


  <select id="selectAfterSaleServiceStandardBySkuNo" resultType="com.vedeng.aftersales.model.dto.AfterSaleServiceStandardApplyDto" parameterType="java.lang.String" >
    SELECT apply.*,
           sku.SHOW_NAME
    FROM T_AFTER_SALE_SERVICE_STANDARD_APPLY apply
    LEFT JOIN V_CORE_SKU sku ON apply.SKU_NO = sku.SKU_NO
    where apply.SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_SERVICE_STANDARD_APPLY
    where SERVICE_STANDARD_APPLY_ID = #{serviceStandardApplyId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_AFTER_SALE_SERVICE_STANDARD_APPLY
    where SERVICE_STANDARD_APPLY_ID = #{serviceStandardApplyId,jdbcType=BIGINT}
  </delete>

  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardApply" useGeneratedKeys="true" keyProperty="serviceStandardApplyId">
    insert into T_AFTER_SALE_SERVICE_STANDARD_APPLY
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="skuNo != null" >
        SKU_NO,
      </if>
      <if test="installPolicyInstallType != null" >
        INSTALL_POLICY_INSTALL_TYPE,
      </if>
      <if test="installPolicyInstallFee != null" >
        INSTALL_POLICY_INSTALL_FEE,
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION,
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        INSTALL_POLICY_FREE_REMOTE_INSTALL,
      </if>
      <if test="installPolicyResponseTime != null" >
        INSTALL_POLICY_RESPONSE_TIME,
      </if>
      <if test="installPolicyVisitTime != null" >
        INSTALL_POLICY_VISIT_TIME,
      </if>
      <if test="installPolicyInstallTime != null" >
        INSTALL_POLICY_INSTALL_TIME,
      </if>
      <if test="installPolicyInstallAreaComment != null" >
        INSTALL_POLICY_INSTALL_AREA_COMMENT,
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN,
      </if>
      <if test="technicalDirectResponseTime != null" >
        TECHNICAL_DIRECT_RESPONSE_TIME,
      </if>
      <if test="technicalDirectEffectTime != null" >
        TECHNICAL_DIRECT_EFFECT_TIME,
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        GUARANTEE_POLICY_IS_GUARANTEE,
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        GUARANTEE_POLICY_GUARANTEE_TYPE,
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD,
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD,
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        GUARANTEE_POLICY_CYCLE_CALTYPE,
      </if>

      <if test="guaranteePolicyArea != null" >
        GUARANTEE_POLICY_AREA,
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        GUARANTEE_POLICY_AREA_COMMENT,
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        GUARANTEE_POLICY_RESPONSE_TIME,
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        GUARANTEE_POLICY_VISIT_TIME,
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        GUARANTEE_POLICY_REPAIRE_TIME,
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        GUARANTEE_POLICY_REPAIRE_COMMENT,
      </if>
      <if test="returnPolicySupportReturn != null" >
        RETURN_POLICY_SUPPORT_RETURN,
      </if>
      <if test="returnPolicyCondition != null" >
        RETURN_POLICY_CONDITION,
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        RETURN_POLICY_NEED_IDENTIFY,
      </if>
      <if test="returnPolicyIdentifyType != null" >
        RETURN_POLICY_IDENTIFY_TYPE,
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        RETURN_POLICY_RETURN_PERIOD,
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        RETURN_POLICY_CYCLE_CALTYP,
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        RETURN_POLICY_PACKAGING_REQUIREMENTS,
      </if>
      <if test="returnPolicyReturnComments != null" >
        RETURN_POLICY_RETURN_COMMENTS,
      </if>
      <if test="exchangePolicySupportChange != null" >
        EXCHANGE_POLICY_SUPPORT_CHANGE,
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        EXCHANGE_POLICY_EXCHANGE_CONTITION,
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        EXCHANGE_POLICY_NEED_IDENTIFY,
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        EXCHANGE_POLICY_IDENTIFY_TYPE,
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        EXCHANGE_POLICY_EXCHANGE_PERIOD,
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        EXCHANGE_POLICY_CYCLE_CALTYP,
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS,
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        EXCHANGE_POLICY_EXCHANGE_COMMENTS,
      </if>
      <if test="parolePolicySupportRepair != null" >
        PAROLE_POLICY_SUPPORT_REPAIR,
      </if>
      <if test="parolePolicySupportRenovation != null" >
        PAROLE_POLICY_SUPPORT_RENOVATION,
      </if>
      <if test="parolePolicySupplyBox != null" >
        PAROLE_POLICY_SUPPLY_BOX,
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        PAROLE_POLICY_SUPPLY_ATTACHMENT,
      </if>
      <if test="overduePolicySupplyBackup != null" >
        OVERDUE_POLICY_SUPPLY_BACKUP,
      </if>
      <if test="overduePolicyDetail != null" >
        OVERDUE_POLICY_DETAIL,
      </if>
      <if test="supplyAfterSaleIsMaintain != null" >
        SUPPLY_AFTER_SALE_IS_MAINTAIN,
      </if>
      <if test="afterSaleStandardStatus != null" >
        AFTER_SALE_STANDARD_STATUS,
      </if>
      <if test="auditor != null" >
        AUDITOR,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updator != null" >
        UPDATOR,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="skuNo != null" >
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallType != null" >
        #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallFee != null" >
        #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null" >
        #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null" >
        #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null" >
        #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallAreaComment != null" >
        #{installPolicyInstallAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null" >
        #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null" >
        #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        #{guaranteePolicyGuaranteeType,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>

      <if test="guaranteePolicyArea != null" >
        #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null" >
        #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null" >
        #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null" >
        #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null" >
        #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null" >
        #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        #{exchangePolicyNeedIdentify,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null" >
        #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null" >
        #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null" >
        #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null" >
        #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null" >
        #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="supplyAfterSaleIsMaintain != null" >
        #{supplyAfterSaleIsMaintain,jdbcType=INTEGER},
      </if>
      <if test="afterSaleStandardStatus != null" >
        #{afterSaleStandardStatus,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardApply" >
    update T_AFTER_SALE_SERVICE_STANDARD_APPLY
    <set >
      <if test="skuNo != null" >
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallType != null" >
        INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallFee != null" >
        INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null" >
        INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null" >
        INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null" >
        INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallAreaComment != null" >
        INSTALL_POLICY_INSTALL_AREA_COMMENT = #{installPolicyInstallAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null" >
        TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null" >
        TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>

      <if test="guaranteePolicyArea != null" >
        GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null" >
        RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null" >
        RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null" >
        RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null" >
        RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null" >
        EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null" >
        PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null" >
        PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null" >
        PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null" >
        OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null" >
        OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="supplyAfterSaleIsMaintain != null" >
        SUPPLY_AFTER_SALE_IS_MAINTAIN = #{supplyAfterSaleIsMaintain,jdbcType=INTEGER},
      </if>
      <if test="afterSaleStandardStatus != null" >
        AFTER_SALE_STANDARD_STATUS = #{afterSaleStandardStatus,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        AUDITOR = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="latestVerifyPassTime != null" >
        LATEST_VERIFY_PASS_TIME = #{latestVerifyPassTime,jdbcType=VARCHAR},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
      <if test="afterSaleServiceLabels != null" >
        AFTER_SALE_SERVICE_LABELS = #{afterSaleServiceLabels,jdbcType=VARCHAR},
      </if>
    </set>
    where SERVICE_STANDARD_APPLY_ID = #{serviceStandardApplyId,jdbcType=BIGINT}
  </update>

  <update id="updateBySkuNo" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardApply" >
    update T_AFTER_SALE_SERVICE_STANDARD_APPLY
    <set >
      <if test="installPolicyInstallType != null" >
        INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      </if>
      <if test="installPolicyInstallFee != null" >
        INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      </if>
      <if test="installPolicyHaveInstallationQualification != null" >
        INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      </if>
      <if test="installPolicyFreeRemoteInstall != null" >
        INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      </if>
      <if test="installPolicyResponseTime != null" >
        INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyVisitTime != null" >
        INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallTime != null" >
        INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      </if>
      <if test="installPolicyInstallAreaComment != null" >
        INSTALL_POLICY_INSTALL_AREA_COMMENT = #{installPolicyInstallAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectSupplyMaintain != null" >
        TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      </if>
      <if test="technicalDirectResponseTime != null" >
        TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="technicalDirectEffectTime != null" >
        TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyIsGuarantee != null" >
        GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyGuaranteeType != null" >
        GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyHostGuaranteePeriod != null" >
        GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyPartsGuaranteePeriod != null" >
        GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyCycleCaltype != null" >
        GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      </if>

      <if test="guaranteePolicyArea != null" >
        GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      </if>
      <if test="guaranteePolicyAreaComment != null" >
        GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyResponseTime != null" >
        GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyVisitTime != null" >
        GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireTime != null" >
        GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      </if>
      <if test="guaranteePolicyRepaireComment != null" >
        GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicySupportReturn != null" >
        RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyCondition != null" >
        RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyNeedIdentify != null" >
        RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyIdentifyType != null" >
        RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnPeriod != null" >
        RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyCycleCaltyp != null" >
        RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyPackagingRequirements != null" >
        RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="returnPolicyReturnComments != null" >
        RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicySupportChange != null" >
        EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyExchangeContition != null" >
        EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyNeedIdentify != null" >
        EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyIdentifyType != null" >
        EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangePeriod != null" >
        EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyCycleCaltyp != null" >
        EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      </if>
      <if test="exchangePolicyPackagingRequirements != null" >
        EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      </if>
      <if test="exchangePolicyExchangeComments != null" >
        EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      </if>
      <if test="parolePolicySupportRepair != null" >
        PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupportRenovation != null" >
        PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyBox != null" >
        PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      </if>
      <if test="parolePolicySupplyAttachment != null" >
        PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      </if>
      <if test="overduePolicySupplyBackup != null" >
        OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      </if>
      <if test="overduePolicyDetail != null" >
        OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      </if>
      <if test="supplyAfterSaleIsMaintain != null" >
        SUPPLY_AFTER_SALE_IS_MAINTAIN = #{supplyAfterSaleIsMaintain,jdbcType=INTEGER},
      </if>
      <if test="afterSaleStandardStatus != null" >
        AFTER_SALE_STANDARD_STATUS = #{afterSaleStandardStatus,jdbcType=INTEGER},
      </if>
      <if test="auditor != null" >
        AUDITOR = #{auditor,jdbcType=VARCHAR},
      </if>
      <if test="updator != null" >
        UPDATOR = #{updator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=VARCHAR},
      </if>
    </set>
    where SKU_NO = #{skuNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSaleServiceStandardApply" >
    update T_AFTER_SALE_SERVICE_STANDARD_APPLY
    set SKU_NO = #{skuNo,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_TYPE = #{installPolicyInstallType,jdbcType=INTEGER},
      INSTALL_POLICY_INSTALL_FEE = #{installPolicyInstallFee,jdbcType=DECIMAL},
      INSTALL_POLICY_HAVE_INSTALLATION_QUALIFICATION = #{installPolicyHaveInstallationQualification,jdbcType=INTEGER},
      INSTALL_POLICY_FREE_REMOTE_INSTALL = #{installPolicyFreeRemoteInstall,jdbcType=INTEGER},
      INSTALL_POLICY_RESPONSE_TIME = #{installPolicyResponseTime,jdbcType=VARCHAR},
      INSTALL_POLICY_VISIT_TIME = #{installPolicyVisitTime,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_TIME = #{installPolicyInstallTime,jdbcType=VARCHAR},
      INSTALL_POLICY_INSTALL_AREA_COMMENT = #{installPolicyInstallAreaComment,jdbcType=VARCHAR},
      TECHNICAL_DIRECT_SUPPLY_MAINTAIN = #{technicalDirectSupplyMaintain,jdbcType=INTEGER},
      TECHNICAL_DIRECT_RESPONSE_TIME = #{technicalDirectResponseTime,jdbcType=VARCHAR},
      TECHNICAL_DIRECT_EFFECT_TIME = #{technicalDirectEffectTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_IS_GUARANTEE = #{guaranteePolicyIsGuarantee,jdbcType=INTEGER},
      GUARANTEE_POLICY_GUARANTEE_TYPE = #{guaranteePolicyGuaranteeType,jdbcType=INTEGER},
      GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD = #{guaranteePolicyHostGuaranteePeriod,jdbcType=VARCHAR},
      GUARANTEE_POLICY_PARTS_GUARANTEE_PERIOD = #{guaranteePolicyPartsGuaranteePeriod,jdbcType=VARCHAR},
      GUARANTEE_POLICY_CYCLE_CALTYPE = #{guaranteePolicyCycleCaltype,jdbcType=INTEGER},
      GUARANTEE_POLICY_AREA = #{guaranteePolicyArea,jdbcType=INTEGER},
      GUARANTEE_POLICY_AREA_COMMENT = #{guaranteePolicyAreaComment,jdbcType=VARCHAR},
      GUARANTEE_POLICY_RESPONSE_TIME = #{guaranteePolicyResponseTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_VISIT_TIME = #{guaranteePolicyVisitTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_REPAIRE_TIME = #{guaranteePolicyRepaireTime,jdbcType=VARCHAR},
      GUARANTEE_POLICY_REPAIRE_COMMENT = #{guaranteePolicyRepaireComment,jdbcType=VARCHAR},
      RETURN_POLICY_SUPPORT_RETURN = #{returnPolicySupportReturn,jdbcType=INTEGER},
      RETURN_POLICY_CONDITION = #{returnPolicyCondition,jdbcType=VARCHAR},
      RETURN_POLICY_NEED_IDENTIFY = #{returnPolicyNeedIdentify,jdbcType=INTEGER},
      RETURN_POLICY_IDENTIFY_TYPE = #{returnPolicyIdentifyType,jdbcType=VARCHAR},
      RETURN_POLICY_RETURN_PERIOD = #{returnPolicyReturnPeriod,jdbcType=VARCHAR},
      RETURN_POLICY_CYCLE_CALTYP = #{returnPolicyCycleCaltyp,jdbcType=INTEGER},
      RETURN_POLICY_PACKAGING_REQUIREMENTS = #{returnPolicyPackagingRequirements,jdbcType=VARCHAR},
      RETURN_POLICY_RETURN_COMMENTS = #{returnPolicyReturnComments,jdbcType=VARCHAR},
      EXCHANGE_POLICY_SUPPORT_CHANGE = #{exchangePolicySupportChange,jdbcType=INTEGER},
      EXCHANGE_POLICY_EXCHANGE_CONTITION = #{exchangePolicyExchangeContition,jdbcType=VARCHAR},
      EXCHANGE_POLICY_NEED_IDENTIFY = #{exchangePolicyNeedIdentify,jdbcType=INTEGER},
      EXCHANGE_POLICY_IDENTIFY_TYPE = #{exchangePolicyIdentifyType,jdbcType=INTEGER},
      EXCHANGE_POLICY_EXCHANGE_PERIOD = #{exchangePolicyExchangePeriod,jdbcType=VARCHAR},
      EXCHANGE_POLICY_CYCLE_CALTYP = #{exchangePolicyCycleCaltyp,jdbcType=INTEGER},
      EXCHANGE_POLICY_PACKAGING_REQUIREMENTS = #{exchangePolicyPackagingRequirements,jdbcType=VARCHAR},
      EXCHANGE_POLICY_EXCHANGE_COMMENTS = #{exchangePolicyExchangeComments,jdbcType=VARCHAR},
      PAROLE_POLICY_SUPPORT_REPAIR = #{parolePolicySupportRepair,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPORT_RENOVATION = #{parolePolicySupportRenovation,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPLY_BOX = #{parolePolicySupplyBox,jdbcType=INTEGER},
      PAROLE_POLICY_SUPPLY_ATTACHMENT = #{parolePolicySupplyAttachment,jdbcType=INTEGER},
      OVERDUE_POLICY_SUPPLY_BACKUP = #{overduePolicySupplyBackup,jdbcType=VARCHAR},
      OVERDUE_POLICY_DETAIL = #{overduePolicyDetail,jdbcType=VARCHAR},
      SUPPLY_AFTER_SALE_IS_MAINTAIN = #{supplyAfterSaleIsMaintain,jdbcType=INTEGER},
      AFTER_SALE_STANDARD_STATUS = #{afterSaleStandardStatus,jdbcType=INTEGER},
      LATEST_VERIFY_PASS_TIME = #{latestVerifyPassTime,jdbcType=VARCHAR},
      AUDITOR = #{auditor,jdbcType=VARCHAR},
      CREATOR = #{creator,jdbcType=VARCHAR},
      UPDATOR = #{updator,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=VARCHAR}
    where SERVICE_STANDARD_APPLY_ID = #{serviceStandardApplyId,jdbcType=BIGINT}
  </update>
</mapper>