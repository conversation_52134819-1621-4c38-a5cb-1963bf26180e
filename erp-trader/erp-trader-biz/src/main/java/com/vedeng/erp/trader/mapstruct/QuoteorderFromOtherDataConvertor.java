package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.trader.domain.dto.QuoteorderDto;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import org.mapstruct.*;

/**
 * <AUTHOR>
 * @description Dto 从三方实体中获取赋值对象
 * @date 2022/7/13 12:43
 **/
@Mapper(componentModel = "spring",unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,imports = {System.class})
public interface QuoteorderFromOtherDataConvertor {


    /**
     * 客户信息
     * @param traderCustomerInfoVo 客户信息
     * @param quoteorderDto 报价单
     */
    default void quoteOrderBindTraderCustomerInfoVo(TraderCustomerInfoVo traderCustomerInfoVo, QuoteorderDto quoteorderDto) {

        quoteorderDto.setCustomerNature(traderCustomerInfoVo.getCustomerNature());
        quoteorderDto.setCustomerType(traderCustomerInfoVo.getCustomerType());
        quoteorderDto.setTraderName(traderCustomerInfoVo.getTraderName());
        quoteorderDto.setCustomerLevel(traderCustomerInfoVo.getCustomerLevelStr());
        quoteorderDto.setArea(traderCustomerInfoVo.getAddress());
        quoteorderDto.setSalesAreaId(traderCustomerInfoVo.getAreaId());
        quoteorderDto.setTerminalTraderId(traderCustomerInfoVo.getTraderId());
        // VDERP-11892 终端信息同步 和vikki沟通只修改名称，不是简单的映射调整
        // quoteorderDto.setTerminalTraderName(traderCustomerInfoVo.getTraderName());
        quoteorderDto.setTerminalTraderType(traderCustomerInfoVo.getCustomerType());
    }

    /**
     * 联系人信息  总价 商机编号等 商机中有的
     * @param businessChanceDto  商机
     * @param quoteorderDto 报价单
     */
    default void quoteOrderBindBussinessChance(BusinessChanceDto businessChanceDto, QuoteorderDto quoteorderDto) {

        quoteorderDto.setTraderContactName(businessChanceDto.getTraderContactName());
        quoteorderDto.setMobile(businessChanceDto.getMobile());
        quoteorderDto.setTelephone(businessChanceDto.getTelephone());
        quoteorderDto.setTotalAmount(businessChanceDto.getAmount());
        quoteorderDto.setBussinessChanceId(businessChanceDto.getBussinessChanceId());
    }

    /**
     * 部门id 销售id 零碎 属性赋值
     * @param currentUser 用户
     * @param quoteorderDto 报价单
     */
    @Mapping(target = "source", constant = "0")
    @Mapping(target = "addTime", expression = "java(System.currentTimeMillis())")
    @Mapping(target = "modTime", expression = "java(System.currentTimeMillis())")
    @Mapping(target = "period", constant = "14")
    @Mapping(target = "userId", source = "id")
    @Mapping(target = "creator", source = "id")
    @Mapping(target = "updater", source = "id")
    void quoteOrderBindCurrentUser(CurrentUser currentUser, @MappingTarget QuoteorderDto quoteorderDto);


}
