Vue.component('ui-pagination', {
    template: `<div class="vd-ui-page" v-if="totalCount > 1">
        <div
            class="vd-ui-page-total-txt"
            :class="{ 'vd-ui-page-total-small': size === 'small' }"
            v-if="type==='combine'"
        >
            共{{ total }}{{ totalTxt }}
        </div>
        <div
            class="vd-ui-page-list"
            :class="{ 'vd-ui-page-small': size === 'small' }"
        >
            <a
                href="javascript:void(0)"
                class="vd-ui-page-item vd-ui-page-prev"
                :class="{
                    'vd-ui-page-disabled': current === 1,
                    'vd-ui-page-item-wider': prevTxt,
                }"
                :title="prevTxt"
                @click="changeCurrentPage(current - 1)"
                rel="nofollow"
                ><i class="vd-ui_icon icon-app-left"></i>
                <template v-if="prevTxt">
                    {{ prevTxt }}
                </template>
            </a>

            <template v-if="start > 0">
                <a
                    href="javascript:void(0)"
                    class="vd-ui-page-item"
                    :class="{ 'vd-ui-page-active': current === 1 }"
                    rel="nofollow"
                    @click="changeCurrentPage(1)"
                    >1</a
                >
            </template>

            <template v-if="start > 1">
                <strong class="vd-ui-page-omit">...</strong>
            </template>

            <template v-for="i in end - start">
                <template v-if="i + start === current">
                    <a
                        href="javascript:void(0)"
                        rel="nofollow"
                        class="vd-ui-page-item vd-ui-page-active"
                        :class="{ 'vd-ui-page-item-wider': current > 9 }"
                        >{{ current }}</a
                    >
                </template>
                <template v-else>
                    <a
                        href="javascript:void(0)"
                        rel="nofollow"
                        class="vd-ui-page-item"
                        :class="{ 'vd-ui-page-item-wider': i + start > 9 }"
                        @click="changeCurrentPage(i + start)"
                        >{{ i + start }}</a
                    >
                </template>
            </template>

            <template v-if="end < totalCount - 1">
                <strong class="vd-ui-page-omit">...</strong>
            </template>

            <template
                v-if="
                    (end < totalCount && needEnd) ||
                    (!needEnd && end === totalCount - 1)
                "
            >
                <a
                    href="javascript:void(0)"
                    rel="nofollow"
                    class="vd-ui-page-item"
                    :class="[
                        { 'vd-ui-page-active': current === totalCount },
                        { 'vd-ui-page-item-wider': totalCount > 9 },
                    ]"
                    @click="changeCurrentPage(totalCount)"
                    >{{ totalCount }}</a
                >
            </template>

            <a
                href="javascript:void(0)"
                class="vd-ui-page-item vd-ui-page-next"
                :class="{
                    'vd-ui-page-disabled': current === totalCount,
                    'vd-ui-page-item-wider': nextTxt,
                }"
                :title="nextTxt"
                @click="changeCurrentPage(current + 1)"
                rel="nofollow"
            >
                <template>
                    {{ nextTxt }}
                </template>
                <i class="vd-ui_icon icon-app-right"></i
            ></a>
        </div>
        <div class="vd-ui-page-jump" v-if="jump">
            <ui-input v-model="jumpNum" type="number" placeholder="输入页码"></ui-input>
            <ui-button @click="pageJump">跳转</ui-button>  
        </div>
    </div>`,
    data() {
        return {
            start: 0,
            end: 0,
            totalCount: 0,
            current: 1,
            jumpNum: ''
        };
    },
    props: {
        currentPage: {
            type: Number,
            default: 1,
        },
        prevTxt: {
            type: String,
            default: "",
        },
        nextTxt: {
            type: String,
            default: "下一页",
        },
        total: {
            type: Number,
            default: 0,
        },
        pageSize: {
            type: Number,
            default: 10,
        },
        pageCount: {
            type: Number,
            default: 7,
        },
        size: {
            type: String,
            default: "", //可传 small
        },
        type: {
            type: String,
            default: "", //可传 combine
        },
        needEnd: {
            type: Boolean,
            default: true,
        },
        totalTxt: {
            type: String,
            default: "条结果",
        },
        jump: {
            type: Boolean,
            default: false,
        }
    },
    watch: {
        currentPage() {
            this.init();
        },
        total() {
            this.init();
        },
    },
    mounted() {
        this.init();
    },
    methods: {
        init() {
            this.totalCount = Math.ceil(this.total / this.pageSize);
            this.current = this.currentPage;

            if(this.totalCount > 1) {
                this.computedPages();
            }
        },
        computedPages: function () {
            let start = this.current - 1 - Math.floor(this.pageCount / 2);
            start = start < 1 ? 1 : start;

            let end = start + this.pageCount;
            end = end > this.totalCount - 1 ? this.totalCount - 1 : end;

            start = end - this.pageCount;
            start = start < 1 ? 1 : start;

            this.start = start;
            this.end = end;
        },
        changPage(index) {
            if(index > this.totalCount) {
                index = this.totalCount;
            }
            this.current = index;
            this.jumpNum = "";
            this.computedPages();
            this.$emit("update:currentPage", index);
            this.$emit("change", index);
        },
        changeCurrentPage(index) {
            index = parseInt(index);
            if (
                index < 1 ||
                index > this.totalCount ||
                index === this.current
            ) {
                return false;
            }

            this.changPage(index);
        },
        pageJump() {
            if (!this.jumpNum.trim()) {
                return;
            }

            this.changPage(parseInt(this.jumpNum));
        }
    },
})