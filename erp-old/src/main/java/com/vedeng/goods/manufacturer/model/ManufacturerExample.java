package com.vedeng.goods.manufacturer.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ManufacturerExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public ManufacturerExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andManufacturerIdIsNull() {
            addCriterion("MANUFACTURER_ID is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdIsNotNull() {
            addCriterion("MANUFACTURER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdEqualTo(Integer value) {
            addCriterion("MANUFACTURER_ID =", value, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdNotEqualTo(Integer value) {
            addCriterion("MANUFACTURER_ID <>", value, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdGreaterThan(Integer value) {
            addCriterion("MANUFACTURER_ID >", value, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("MANUFACTURER_ID >=", value, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdLessThan(Integer value) {
            addCriterion("MANUFACTURER_ID <", value, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdLessThanOrEqualTo(Integer value) {
            addCriterion("MANUFACTURER_ID <=", value, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdIn(List<Integer> values) {
            addCriterion("MANUFACTURER_ID in", values, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdNotIn(List<Integer> values) {
            addCriterion("MANUFACTURER_ID not in", values, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdBetween(Integer value1, Integer value2) {
            addCriterion("MANUFACTURER_ID between", value1, value2, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("MANUFACTURER_ID not between", value1, value2, "manufacturerId");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameIsNull() {
            addCriterion("MANUFACTURER_NAME is null");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameIsNotNull() {
            addCriterion("MANUFACTURER_NAME is not null");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameEqualTo(String value) {
            addCriterion("MANUFACTURER_NAME =", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameNotEqualTo(String value) {
            addCriterion("MANUFACTURER_NAME <>", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameGreaterThan(String value) {
            addCriterion("MANUFACTURER_NAME >", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameGreaterThanOrEqualTo(String value) {
            addCriterion("MANUFACTURER_NAME >=", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameLessThan(String value) {
            addCriterion("MANUFACTURER_NAME <", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameLessThanOrEqualTo(String value) {
            addCriterion("MANUFACTURER_NAME <=", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameLike(String value) {
            addCriterion("MANUFACTURER_NAME like", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameNotLike(String value) {
            addCriterion("MANUFACTURER_NAME not like", value, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameIn(List<String> values) {
            addCriterion("MANUFACTURER_NAME in", values, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameNotIn(List<String> values) {
            addCriterion("MANUFACTURER_NAME not in", values, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameBetween(String value1, String value2) {
            addCriterion("MANUFACTURER_NAME between", value1, value2, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andManufacturerNameNotBetween(String value1, String value2) {
            addCriterion("MANUFACTURER_NAME not between", value1, value2, "manufacturerName");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNull() {
            addCriterion("IS_DELETE is null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIsNotNull() {
            addCriterion("IS_DELETE is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeleteEqualTo(Integer value) {
            addCriterion("IS_DELETE =", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotEqualTo(Integer value) {
            addCriterion("IS_DELETE <>", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThan(Integer value) {
            addCriterion("IS_DELETE >", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE >=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThan(Integer value) {
            addCriterion("IS_DELETE <", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteLessThanOrEqualTo(Integer value) {
            addCriterion("IS_DELETE <=", value, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteIn(List<Integer> values) {
            addCriterion("IS_DELETE in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotIn(List<Integer> values) {
            addCriterion("IS_DELETE not in", values, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andIsDeleteNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_DELETE not between", value1, value2, "isDelete");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddNoIsNull() {
            addCriterion("ADD_NO is null");
            return (Criteria) this;
        }

        public Criteria andAddNoIsNotNull() {
            addCriterion("ADD_NO is not null");
            return (Criteria) this;
        }

        public Criteria andAddNoEqualTo(Integer value) {
            addCriterion("ADD_NO =", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotEqualTo(Integer value) {
            addCriterion("ADD_NO <>", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoGreaterThan(Integer value) {
            addCriterion("ADD_NO >", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("ADD_NO >=", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoLessThan(Integer value) {
            addCriterion("ADD_NO <", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoLessThanOrEqualTo(Integer value) {
            addCriterion("ADD_NO <=", value, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoIn(List<Integer> values) {
            addCriterion("ADD_NO in", values, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotIn(List<Integer> values) {
            addCriterion("ADD_NO not in", values, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoBetween(Integer value1, Integer value2) {
            addCriterion("ADD_NO between", value1, value2, "addNo");
            return (Criteria) this;
        }

        public Criteria andAddNoNotBetween(Integer value1, Integer value2) {
            addCriterion("ADD_NO not between", value1, value2, "addNo");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("UPDATE_TIME is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("UPDATE_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(Date value) {
            addCriterion("UPDATE_TIME =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(Date value) {
            addCriterion("UPDATE_TIME <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(Date value) {
            addCriterion("UPDATE_TIME >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("UPDATE_TIME >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(Date value) {
            addCriterion("UPDATE_TIME <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(Date value) {
            addCriterion("UPDATE_TIME <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<Date> values) {
            addCriterion("UPDATE_TIME in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<Date> values) {
            addCriterion("UPDATE_TIME not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(Date value1, Date value2) {
            addCriterion("UPDATE_TIME between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(Date value1, Date value2) {
            addCriterion("UPDATE_TIME not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateNoIsNull() {
            addCriterion("UPDATE_NO is null");
            return (Criteria) this;
        }

        public Criteria andUpdateNoIsNotNull() {
            addCriterion("UPDATE_NO is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateNoEqualTo(Integer value) {
            addCriterion("UPDATE_NO =", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotEqualTo(Integer value) {
            addCriterion("UPDATE_NO <>", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoGreaterThan(Integer value) {
            addCriterion("UPDATE_NO >", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATE_NO >=", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoLessThan(Integer value) {
            addCriterion("UPDATE_NO <", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATE_NO <=", value, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoIn(List<Integer> values) {
            addCriterion("UPDATE_NO in", values, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotIn(List<Integer> values) {
            addCriterion("UPDATE_NO not in", values, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoBetween(Integer value1, Integer value2) {
            addCriterion("UPDATE_NO between", value1, value2, "updateNo");
            return (Criteria) this;
        }

        public Criteria andUpdateNoNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATE_NO not between", value1, value2, "updateNo");
            return (Criteria) this;
        }

        public Criteria andIsUploadIsNull() {
            addCriterion("IS_UPLOAD is null");
            return (Criteria) this;
        }

        public Criteria andIsUploadIsNotNull() {
            addCriterion("IS_UPLOAD is not null");
            return (Criteria) this;
        }

        public Criteria andIsUploadEqualTo(Integer value) {
            addCriterion("IS_UPLOAD =", value, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadNotEqualTo(Integer value) {
            addCriterion("IS_UPLOAD <>", value, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadGreaterThan(Integer value) {
            addCriterion("IS_UPLOAD >", value, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadGreaterThanOrEqualTo(Integer value) {
            addCriterion("IS_UPLOAD >=", value, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadLessThan(Integer value) {
            addCriterion("IS_UPLOAD <", value, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadLessThanOrEqualTo(Integer value) {
            addCriterion("IS_UPLOAD <=", value, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadIn(List<Integer> values) {
            addCriterion("IS_UPLOAD in", values, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadNotIn(List<Integer> values) {
            addCriterion("IS_UPLOAD not in", values, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadBetween(Integer value1, Integer value2) {
            addCriterion("IS_UPLOAD between", value1, value2, "isUpload");
            return (Criteria) this;
        }

        public Criteria andIsUploadNotBetween(Integer value1, Integer value2) {
            addCriterion("IS_UPLOAD not between", value1, value2, "isUpload");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateIsNull() {
            addCriterion("BC_ISSUE_DATE is null");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateIsNotNull() {
            addCriterion("BC_ISSUE_DATE is not null");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateEqualTo(Date value) {
            addCriterion("BC_ISSUE_DATE =", value, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateNotEqualTo(Date value) {
            addCriterion("BC_ISSUE_DATE <>", value, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateGreaterThan(Date value) {
            addCriterion("BC_ISSUE_DATE >", value, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateGreaterThanOrEqualTo(Date value) {
            addCriterion("BC_ISSUE_DATE >=", value, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateLessThan(Date value) {
            addCriterion("BC_ISSUE_DATE <", value, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateLessThanOrEqualTo(Date value) {
            addCriterion("BC_ISSUE_DATE <=", value, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateIn(List<Date> values) {
            addCriterion("BC_ISSUE_DATE in", values, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateNotIn(List<Date> values) {
            addCriterion("BC_ISSUE_DATE not in", values, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateBetween(Date value1, Date value2) {
            addCriterion("BC_ISSUE_DATE between", value1, value2, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcIssueDateNotBetween(Date value1, Date value2) {
            addCriterion("BC_ISSUE_DATE not between", value1, value2, "bcIssueDate");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeIsNull() {
            addCriterion("BC_START_TIME is null");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeIsNotNull() {
            addCriterion("BC_START_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeEqualTo(Date value) {
            addCriterion("BC_START_TIME =", value, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeNotEqualTo(Date value) {
            addCriterion("BC_START_TIME <>", value, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeGreaterThan(Date value) {
            addCriterion("BC_START_TIME >", value, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("BC_START_TIME >=", value, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeLessThan(Date value) {
            addCriterion("BC_START_TIME <", value, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("BC_START_TIME <=", value, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeIn(List<Date> values) {
            addCriterion("BC_START_TIME in", values, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeNotIn(List<Date> values) {
            addCriterion("BC_START_TIME not in", values, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeBetween(Date value1, Date value2) {
            addCriterion("BC_START_TIME between", value1, value2, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("BC_START_TIME not between", value1, value2, "bcStartTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeIsNull() {
            addCriterion("BC_END_TIME is null");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeIsNotNull() {
            addCriterion("BC_END_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeEqualTo(Date value) {
            addCriterion("BC_END_TIME =", value, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeNotEqualTo(Date value) {
            addCriterion("BC_END_TIME <>", value, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeGreaterThan(Date value) {
            addCriterion("BC_END_TIME >", value, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("BC_END_TIME >=", value, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeLessThan(Date value) {
            addCriterion("BC_END_TIME <", value, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("BC_END_TIME <=", value, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeIn(List<Date> values) {
            addCriterion("BC_END_TIME in", values, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeNotIn(List<Date> values) {
            addCriterion("BC_END_TIME not in", values, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeBetween(Date value1, Date value2) {
            addCriterion("BC_END_TIME between", value1, value2, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andBcEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("BC_END_TIME not between", value1, value2, "bcEndTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeIsNull() {
            addCriterion("PE_START_TIME is null");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeIsNotNull() {
            addCriterion("PE_START_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeEqualTo(Date value) {
            addCriterion("PE_START_TIME =", value, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeNotEqualTo(Date value) {
            addCriterion("PE_START_TIME <>", value, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeGreaterThan(Date value) {
            addCriterion("PE_START_TIME >", value, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("PE_START_TIME >=", value, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeLessThan(Date value) {
            addCriterion("PE_START_TIME <", value, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("PE_START_TIME <=", value, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeIn(List<Date> values) {
            addCriterion("PE_START_TIME in", values, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeNotIn(List<Date> values) {
            addCriterion("PE_START_TIME not in", values, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeBetween(Date value1, Date value2) {
            addCriterion("PE_START_TIME between", value1, value2, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("PE_START_TIME not between", value1, value2, "peStartTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeIsNull() {
            addCriterion("PE_END_TIME is null");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeIsNotNull() {
            addCriterion("PE_END_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeEqualTo(Date value) {
            addCriterion("PE_END_TIME =", value, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeNotEqualTo(Date value) {
            addCriterion("PE_END_TIME <>", value, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeGreaterThan(Date value) {
            addCriterion("PE_END_TIME >", value, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("PE_END_TIME >=", value, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeLessThan(Date value) {
            addCriterion("PE_END_TIME <", value, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("PE_END_TIME <=", value, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeIn(List<Date> values) {
            addCriterion("PE_END_TIME in", values, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeNotIn(List<Date> values) {
            addCriterion("PE_END_TIME not in", values, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeBetween(Date value1, Date value2) {
            addCriterion("PE_END_TIME between", value1, value2, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPeEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("PE_END_TIME not between", value1, value2, "peEndTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeIsNull() {
            addCriterion("PEP_START_TIME is null");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeIsNotNull() {
            addCriterion("PEP_START_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeEqualTo(Date value) {
            addCriterion("PEP_START_TIME =", value, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeNotEqualTo(Date value) {
            addCriterion("PEP_START_TIME <>", value, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeGreaterThan(Date value) {
            addCriterion("PEP_START_TIME >", value, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("PEP_START_TIME >=", value, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeLessThan(Date value) {
            addCriterion("PEP_START_TIME <", value, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeLessThanOrEqualTo(Date value) {
            addCriterion("PEP_START_TIME <=", value, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeIn(List<Date> values) {
            addCriterion("PEP_START_TIME in", values, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeNotIn(List<Date> values) {
            addCriterion("PEP_START_TIME not in", values, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeBetween(Date value1, Date value2) {
            addCriterion("PEP_START_TIME between", value1, value2, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepStartTimeNotBetween(Date value1, Date value2) {
            addCriterion("PEP_START_TIME not between", value1, value2, "pepStartTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeIsNull() {
            addCriterion("PEP_END_TIME is null");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeIsNotNull() {
            addCriterion("PEP_END_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeEqualTo(Date value) {
            addCriterion("PEP_END_TIME =", value, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeNotEqualTo(Date value) {
            addCriterion("PEP_END_TIME <>", value, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeGreaterThan(Date value) {
            addCriterion("PEP_END_TIME >", value, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("PEP_END_TIME >=", value, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeLessThan(Date value) {
            addCriterion("PEP_END_TIME <", value, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeLessThanOrEqualTo(Date value) {
            addCriterion("PEP_END_TIME <=", value, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeIn(List<Date> values) {
            addCriterion("PEP_END_TIME in", values, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeNotIn(List<Date> values) {
            addCriterion("PEP_END_TIME not in", values, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeBetween(Date value1, Date value2) {
            addCriterion("PEP_END_TIME between", value1, value2, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andPepEndTimeNotBetween(Date value1, Date value2) {
            addCriterion("PEP_END_TIME not between", value1, value2, "pepEndTime");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceIsNull() {
            addCriterion("PRODUCT_COMPANY_LICENCE is null");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceIsNotNull() {
            addCriterion("PRODUCT_COMPANY_LICENCE is not null");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE =", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceNotEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE <>", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceGreaterThan(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE >", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceGreaterThanOrEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE >=", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceLessThan(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE <", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceLessThanOrEqualTo(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE <=", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceLike(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE like", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceNotLike(String value) {
            addCriterion("PRODUCT_COMPANY_LICENCE not like", value, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceIn(List<String> values) {
            addCriterion("PRODUCT_COMPANY_LICENCE in", values, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceNotIn(List<String> values) {
            addCriterion("PRODUCT_COMPANY_LICENCE not in", values, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceBetween(String value1, String value2) {
            addCriterion("PRODUCT_COMPANY_LICENCE between", value1, value2, "productCompanyLicence");
            return (Criteria) this;
        }

        public Criteria andProductCompanyLicenceNotBetween(String value1, String value2) {
            addCriterion("PRODUCT_COMPANY_LICENCE not between", value1, value2, "productCompanyLicence");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated do_not_delete_during_merge Thu Mar 11 15:04:50 CST 2021
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_MANUFACTURER
     *
     * @mbggenerated Thu Mar 11 15:04:50 CST 2021
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}