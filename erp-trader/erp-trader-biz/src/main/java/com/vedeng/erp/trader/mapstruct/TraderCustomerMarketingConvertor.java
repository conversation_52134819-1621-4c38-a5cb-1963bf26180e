package com.vedeng.erp.trader.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity;
import com.vedeng.erp.trader.dto.TraderCustomerMarketingTerminalDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户营销属性表
 * @date 2023/8/7 10:17
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TraderCustomerMarketingConvertor extends BaseMapStruct<TraderCustomerMarketingTerminalEntity, TraderCustomerMarketingTerminalDto> {

}
