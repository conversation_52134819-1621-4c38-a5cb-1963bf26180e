Vue.component('ui-user', {
    template: `
        <div class="vd-ui-user-wrap" @click="handlerClick">
            <img class="vd-ui-user-avatar" :src="img" alt="" @error="imgError">
            <div class="vd-ui-user-name" :style="{'color': color}">{{ name }}</div>
        </div>
    `,
    props: {
        avatar: {
            type: String,
            default: ''
        },
        name: {
            type: String,
            default: ''
        },
        color: {
            type: String,
            default: '#09F'
        }
    },
    data() {
        return {
            img: '',
            defaultImg: '/mstatic/mui/image/crm-user-avatar.svg',
            isError: false
        };
    },
    computed: {

    },
    mounted() {
        this.img = this.avatar || this.defaultImg;
    },
    methods: {
        imgError() {
            this.img = this.defaultImg;
        },
        handlerClick () {
            this.$emit('click');
        }
    }
})