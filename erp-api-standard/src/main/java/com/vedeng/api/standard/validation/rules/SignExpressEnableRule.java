package com.vedeng.api.standard.validation.rules;

import com.alibaba.fastjson.JSON;
import com.vedeng.api.standard.adapter.express.dto.ExpressSignRequest;
import com.vedeng.api.standard.validation.ValidationContextKeys;
import com.vedeng.api.standard.validation.ValidationResult;
import com.vedeng.api.standard.validation.ValidationRule;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.logistics.model.SyncExpressDetailDto;
import com.vedeng.logistics.service.ExpressService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class SignExpressEnableRule implements ValidationRule<ExpressSignRequest> {
    
    @Autowired
    private BuyorderApiService buyorderApiService;
    @Autowired
    private ExpressService expressService;

    @Override
    public ValidationResult validate(ExpressSignRequest request, Map<String, Object> context) {
        String logisticsNo = request.getLogisticsNo();
        String buyOrderNo = request.getBuyOrderNo();

        log.info("开始验证快递签收参数，快递单号: {}, 采购单号: {}", logisticsNo, buyOrderNo);

        try {
            // 参数校验
            if (logisticsNo == null || logisticsNo.trim().isEmpty()) {
                log.info("快递单号为空，采购单号: {}", buyOrderNo);
                return ValidationResult.failure("快递单号不能为空");
            }
            if (buyOrderNo == null || buyOrderNo.trim().isEmpty()) {
                log.info("采购单号为空，快递单号: {}", logisticsNo);
                return ValidationResult.failure("采购单号不能为空");
            }

            // 使用新的查询方法
            SyncExpressDetailDto detailByNo = expressService.getDetailByNo(buyOrderNo, logisticsNo);
            if (Objects.isNull(detailByNo)) {
                log.info("未查询到快递信息，采购单号: {}, 快递单号: {}", buyOrderNo, logisticsNo);
                return ValidationResult.failure("未查询到快递单号[" + logisticsNo + "]和采购单号[" + buyOrderNo + "]对应的快递信息");
            }

            Integer expressId = detailByNo.getExpressId();
            Integer relatedId = detailByNo.getRelatedId();

            if (expressId == null) {
                log.info("快递记录中expressId为空，采购单号: {}, 快递单号: {}", buyOrderNo, logisticsNo);
                return ValidationResult.failure("快递记录数据异常，expressId为空");
            }

            // 检查签收状态
            if (detailByNo.getArrivalStatus() != null && detailByNo.getArrivalStatus() == 2) {
                log.warn("快递已经签收，采购单号: {}, 快递单号: {}, expressId: {}", buyOrderNo, logisticsNo, expressId);
                return ValidationResult.success("快递已经签收,无需重复执行");
            }

            // 拼接格式：expressId&relatedId&515_
            String expressIds = expressId + "&" + relatedId + "&515_";

            log.info("快递签收参数验证通过，采购单号: {}, 快递单号: {}, expressId: {}, relatedId: {}, expressIds: {}",
                    buyOrderNo, logisticsNo, expressId, relatedId, expressIds);

            // 将参数直接存储到验证上下文中，以便后续使用
            context.put(ValidationContextKeys.EXPRESS_IDS, expressIds);
            context.put(ValidationContextKeys.BEFORE_PARAMS, expressIds);

            log.info("验证上下文参数：{}", JSON.toJSON(context));
            return ValidationResult.success();

        } catch (Exception e) {
            log.info("快递签收参数验证异常，采购单号: {}, 快递单号: {}", buyOrderNo, logisticsNo, e);
            return ValidationResult.failure("快递签收参数验证失败: " + e.getMessage());
        }
    }
    
    @Override
    public String getRuleName() {
        return "SignExpressEnableRule";
    }
}
