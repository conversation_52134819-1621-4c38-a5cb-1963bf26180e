package com.vedeng.crm.business.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.core.utils.HttpClientUtil;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.crm.api.FollowUpRecordApiService;
import com.vedeng.crm.business.business.domain.dto.BusinessToOrderResponseDto;
import com.vedeng.crm.business.business.domain.dto.LinkOrderRequestDto;
import com.vedeng.crm.business.business.domain.dto.LinkSaleOrderDto;
import com.vedeng.crm.business.business.domain.entity.CrmBusinessChanceEntity;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteOrderDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteorderGoods;
import com.vedeng.crm.business.quote.facade.QuoteFacade;
import com.vedeng.crm.business.quote.facade.QuoteNeedsFacade;
import com.vedeng.crm.business.quote.mapper.CrmQuoteorderGoodsMapper;
import com.vedeng.crm.business.quote.mapper.CrmQuoteorderMapper;
import com.vedeng.crm.business.quote.service.BusinessToQuoteService;
import com.vedeng.crm.common.enums.SystemEnum;
import com.vedeng.crm.feign.order.BusinessToOrderApiService;
import com.vedeng.crm.shard.service.ShareService;
import com.vedeng.crm.task.service.TaskService;
import com.vedeng.crm.visitrecord.api.CrmVisitApiService;
import com.vedeng.erp.business.common.constant.BusinessChanceConstant;
import com.vedeng.erp.business.common.enums.BusinessChanceStageEnum;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.RelatedOrderDto;
import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import com.vedeng.erp.business.dto.BusinessCloseDto;
import com.vedeng.erp.business.mapper.BussinessChanceMapper;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.saleorder.dto.*;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.system.domain.entity.BaseCompanyInfoEntity;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.BaseCompanyInfoService;
import com.vedeng.erp.system.service.OperationLogApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.common.enums.CommunicateRecordTypeEnum;
import com.vedeng.erp.trader.domain.entity.QuoteorderEntity;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.RSalesJTraderDto;
import com.vedeng.erp.trader.mapper.QuoteorderMapper;
import com.vedeng.erp.trader.service.RSalesJTraderApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CrmBusinessChanceServiceImpl implements CrmBusinessChanceService {

    @Autowired
    private BusinessChanceService businessChanceService;
    @Autowired
    private BusinessToQuoteService businessToQuoteService;
    @Autowired
    private BusinessToOrderApiService businessToOrderApiService;
    @Autowired
    private BussinessChanceMapper bussinessChanceMapper;
    @Autowired
    private CrmQuoteorderMapper crmQuoteorderMapper;
    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;
    @Autowired
    private CrmQuoteorderGoodsMapper crmQuoteorderGoodsMapper;
    @Autowired
    private OperationLogApiService operationLogApiService;
    @Autowired
    private QuoteFacade quoteFacade;
    @Autowired
    private UserApiService userApiService;
    @Autowired
    private OrderTerminalApiService orderTerminalApiService;
    @Autowired
    private FollowUpRecordApiService followUpRecordApiService;

    @Value("${erp_url}")
    private String erpUrl;

    @Value("${crmJumpErpUrl}")
    private String crmJumpErpUrl;

    @Value("${accessAllBusinessChance:1997}")
    private String accessAllBusinessChance;
    @Autowired
    private TaskService taskService;

    @Autowired
    private CrmVisitApiService crmVisitApiService;
    @Autowired
    private QuoteorderMapper quoteorderMapper;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public BusinessChanceDto add(BusinessChanceDto businessChanceDto) {
        log.info("CRM商机新增，businessChanceDto:{}", JSON.toJSONString(businessChanceDto));
        // 调用erp新增商机接口
        BusinessChanceDto data = businessChanceService.add(businessChanceDto);
        // 调用crm新增跟进记录接口
        CommunicateRecordDto communicateRecordDto = businessChanceDto.getCommunicateRecordDto();
        if (Objects.nonNull(communicateRecordDto)) {
            log.info("CRM商机新增跟进记录，communicateRecordDto:{}", JSON.toJSONString(communicateRecordDto));
            communicateRecordDto.setCommunicateType(CommunicateRecordTypeEnum.BUSINESS_CHANCE.getCode());
            communicateRecordDto.setRelatedId(businessChanceDto.getBussinessChanceId());
            communicateRecordDto.setTraderName(businessChanceDto.getTraderName());
            communicateRecordDto.setTraderId(businessChanceDto.getTraderId());
            communicateRecordDto.setBelongUserId(businessChanceDto.getCreator());
            communicateRecordDto.setBelongUserName(businessChanceDto.getCreatorName());
            communicateRecordDto.setFollowUpType(5901);//默认电话类型
            communicateRecordDto.setContactMob(businessChanceDto.getMobile());
            communicateRecordDto.setTelephone(businessChanceDto.getTelephone());
            communicateRecordDto.setContact(businessChanceDto.getTraderContactName());
            communicateRecordDto.setBegintime(System.currentTimeMillis());
            communicateRecordDto.setBeginTimeDate(new Date());
            communicateRecordDto.setEndtime(System.currentTimeMillis()+2*60*1000);
            communicateRecordDto.setEndTimeDate(new Date(System.currentTimeMillis()+2*60*1000));
            communicateRecordDto.setContentSuffix("首次创建"+(StringUtils.isBlank(businessChanceDto.getProductCommentsSale())?"":("#"+businessChanceDto.getProductCommentsSale()+"#")));
            followUpRecordApiService.add(communicateRecordDto);
        }
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(data.getBussinessChanceId());
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_NE);

        //checkAutoShareForBusinessChance(businessChanceDto,currentUser);

        return data;
    }

    public void checkAutoShareForBusinessChance( BusinessChanceDto businessChanceDto,CurrentUser currentUser){
        //如果是建档客户的情况下，判断当前客户，是否存在分享给当前操作人的情况
        if(businessChanceDto.getTraderId() != null && businessChanceDto.getTraderId()> 0){
            TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfo(businessChanceDto.getTraderId());
            if(traderCustomerInfoVo != null && !currentUser.getId().equals(traderCustomerInfoVo.getSaleId())){ // 如果创建商机的人，不是当前客户的归属人,进入判断是否分享的逻辑
                RSalesJTraderDto rSalesJTraderDto =  rSalesJTraderApiService.getShareTraderByUserId(businessChanceDto.getTraderId(),currentUser.getId());//分享的记录
                if(rSalesJTraderDto != null){
                    RSalesJBusinessOrderDto rSalesJBusinessOrderDto = new RSalesJBusinessOrderDto();
                    //查看是否已经分享记录，当前是创建，肯定没有
                    Integer businessId = businessChanceDto.getBussinessChanceId();//商机id
                    Integer currentUserId = currentUser.getId();//当前操作人
                    String currentUserUsername = currentUser.getUsername();//当前操作人

                    rSalesJBusinessOrderDto.setAddTime(new Date());
                    rSalesJBusinessOrderDto.setModTime(new Date());
                    rSalesJBusinessOrderDto.setCreator(currentUser.getId());
                    rSalesJBusinessOrderDto.setUpdater(currentUser.getId());
                    rSalesJBusinessOrderDto.setBusinessNo(businessChanceDto.getBussinessChanceNo());
                    rSalesJBusinessOrderDto.setBusinessId(businessId);
                    rSalesJBusinessOrderDto.setBusinessType(1);//业务类型 1.商机 2.报价 3.订单 4.售后 5.线索

                    rSalesJBusinessOrderDto.setSaleUserId( currentUserId );
                    rSalesJBusinessOrderDto.setSaleUserName( currentUserUsername);
                    shareService.shareAutoBusiness(rSalesJBusinessOrderDto,currentUser);
                }

            }
        }
    }

    @Autowired
    private RSalesJTraderApiService rSalesJTraderApiService;

    @Autowired
    private ShareService shareService;
    


    @Override
    public BusinessChanceDto detail(Integer businessChanceId) {
        // 校验
        checkPermission(businessChanceId);
        BusinessChanceDto businessChanceDto = businessChanceService.viewDetail(businessChanceId);
        BaseCompanyInfoEntity entity = getDomainBySystemCode(businessChanceDto.getFrontEndSeq());

        QuoteLinkOrderDto quoteLinkOrderDto = new QuoteLinkOrderDto();
        quoteLinkOrderDto.setBusinessChanceId(businessChanceId);
        try {
            R<SaleorderInfoDto> result = null;
            if (Objects.equals(businessChanceDto.getFrontEndSeq(),SystemEnum.VEDENG.getValue())){
                result = businessToOrderApiService.getSaleOrder(quoteLinkOrderDto);
            }else {
                String saleorderInfoDtoStr = HttpUtil.get(entity.getErpDomain() + "/api/quoteSaleOrder/getByQuoteOrderId.do?quoteOrderId="+businessChanceDto.getQuoteorderDto().getQuoteorderId(), 10 * 1000);
                result = JSON.parseObject(saleorderInfoDtoStr, new TypeReference< R<SaleorderInfoDto>>() {});
            }
           
            if (ErpConstant.ZERO.equals(result.getCode()) && Objects.nonNull(result.getData()) && Objects.nonNull(result.getData().getSaleorderId())) {
                SaleorderInfoDto data = result.getData();
                businessChanceDto.setSaleorderInfoDto(data);
                if(businessChanceDto.getWinningOrderTime() == null){
                    businessChanceDto.setWinningOrderTime(new Date(data.getAddTime()));
                }
                // 关联销售订单
                RelatedOrderDto relatedOrderDto = new RelatedOrderDto();
                relatedOrderDto.setSystemCode(businessChanceDto.getFrontEndSeq());
                relatedOrderDto.setType("订单");
                relatedOrderDto.setRelatedId(data.getSaleorderId());
                relatedOrderDto.setRelatedNo(data.getSaleorderNo());
                relatedOrderDto.setAddTimeDate(new Date(data.getAddTime()));
                if (Objects.nonNull(data.getTraderId())) {
                    Integer saleUserId = traderCustomerBaseService.getTraderCustomerUserIdByTraderId(data.getTraderId());
                    if (Objects.nonNull(saleUserId)) {
                        UserDto belongUser = userApiService.getUserBaseInfo(saleUserId);
                        relatedOrderDto.setBelongUserId(saleUserId);
                        relatedOrderDto.setBelongUserName(belongUser.getUsername());
                        relatedOrderDto.setBelongUserPic(belongUser.getAliasHeadPicture());
                    }
                }
                try {
                    String domain = entity.getErpDomain();
                    String targetTabUrl = ("/orderstream/saleorder/detail.do?saleOrderId=" + data.getSaleorderId() + "&scene=0");
                    relatedOrderDto.setJumpErpInnerUrl(targetTabUrl);
                    if (domain.indexOf("sso") > -1) {
                        String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=", "UTF-8");
                        relatedOrderDto.setJumpErpUrl(domain + encodetargetTabUrl);
                    } else {
                        String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("销售详情", "UTF-8");
                        relatedOrderDto.setJumpErpUrl(domain + encodetargetTabUrl);
                    }
                } catch (Exception e) {
                    log.error("商机详情的关联销售链接转换给前端时失败，需要检查", e);
                }
                businessChanceDto.getRelatedOrderDtoList().add(relatedOrderDto);
            }
        } catch (Exception e) {
            log.error("CRM商机详情，获取销售订单失败，businessChanceId:{}", businessChanceId, e);
        }
        return businessChanceDto;
    }

    public void checkPermission(Integer businessChanceId) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        List<Integer> accessAll = Arrays.stream(accessAllBusinessChance.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        BusinessChanceDto dto = new BusinessChanceDto();
        dto.setBussinessChanceId(businessChanceId);
        BusinessChanceDto checkDto = businessChanceService.selectOne(dto);
        Integer id = currentUser.getId();
        if(accessAll.contains(id)){//所有商机权限
            return;
        }

        Integer userId = checkDto.getUserId();
        if(id.equals(userId)){//归属人
            return ;
        }

        if(id.equals(checkDto.getCreator())){
            return ;
        }

        List<RSalesJBusinessOrderDto> shareBusinessChance = shareService.getShareListByBusinessId(businessChanceId,1);
        List<Integer> salesOrderIds = CollUtil.isNotEmpty(shareBusinessChance) ? shareBusinessChance.stream().map(RSalesJBusinessOrderDto::getSaleUserId).collect(Collectors.toList()) : Collections.emptyList();
        if(salesOrderIds.contains(id)){//如果是分享人
            return ;
        }

         //商机的归属人
        List<Integer> sonList = userApiService.queryUserIdListSubFromUac(currentUser.getId());
        boolean containSon = sonList.contains(userId);
        if (containSon) {//下属的逻辑
            return;
        }

        boolean creatorSon = sonList.contains(checkDto.getCreator());
        if (creatorSon) {//创建人下属的逻辑
            return;
        }

        List<UserDto> chatGroupUser = quoteFacade.getChatGroupUser(businessChanceId);
        List<Integer> chatGroupUserIds = CollUtil.isNotEmpty(chatGroupUser) ? chatGroupUser.stream().map(UserDto::getUserId).collect(Collectors.toList()) : Collections.emptyList();
        if(chatGroupUserIds.contains(id)) {
           return;
        }
        throw new ServiceException("您无该条单据的查看权限，可联系归属销售" + checkDto.getUsername() + "申请查看");
    }

    @Autowired
    private QuoteNeedsFacade quoteNeedsFacade;

    @Override
    @Transactional
    public BusinessChanceDto update(BusinessChanceDto businessChanceDto) {
        BusinessChanceDto pre = businessChanceService.selectOne(businessChanceDto);
        if (Objects.equals(pre.getStage(), ErpConstant.FIVE) || Objects.equals(pre.getStage(), ErpConstant.SIX)) {
            throw new ServiceException("商机已赢单或已关闭，无法操作");
        }
        // 综合项目转非综合项目
        if (!Objects.equals(businessChanceDto.getBusinessType(),BusinessChanceConstant.ID_5703)
                && Objects.equals(pre.getBusinessType(),BusinessChanceConstant.ID_5703)) {
            // 删除报价单需求
            quoteNeedsFacade.cleanNeeds(businessChanceDto.getBussinessChanceId());

        }
        BusinessChanceDto update = businessChanceService.update(businessChanceDto);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        //checkAutoShareForBusinessChance(businessChanceDto,currentUser);
        return update;
    }

    @Override
    public boolean checkSkuRepeat(Integer businessChanceId){
        CrmQuoteOrderEntity quoteOrder = crmQuoteorderMapper.findByBussinessChanceId(businessChanceId);
        List<CrmQuoteorderGoods> quoteorderGoodsList = crmQuoteorderGoodsMapper.selectByQuoteorderId(quoteOrder.getQuoteorderId());
        if (quoteorderGoodsList.size() > 0) {
            Set<String> skuSet = quoteorderGoodsList.stream()
                    .map(CrmQuoteorderGoods::getSku) // 提取 sku 字段
                    .collect(Collectors.toSet());    // 去重并收集到 Set 中
            if (skuSet.size() != quoteorderGoodsList.size()) {//如果sku的行数小于报价单的行数，就是有重复的
                return true;
            } else {
                return false;
            }
        }
        return  false;
    }

    @Override
    public BusinessToOrderResponseDto businessToOrder(Integer businessChanceId, Integer systemCode) {
        log.info("CRM商机转订单，businessChanceId:{}", businessChanceId);
        BusinessToOrderResponseDto businessToOrderResponseDto = new BusinessToOrderResponseDto();
        toOrderVerify(businessChanceId);
        BussinessChanceEntity businessChance = bussinessChanceMapper.selectByPrimaryKey(businessChanceId);
        CrmQuoteOrderEntity quoteOrder = crmQuoteorderMapper.findByBussinessChanceId(businessChanceId);
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        QuoteToOrderDto quoteToOrderDto = new QuoteToOrderDto();
        quoteToOrderDto.setQuoteOrderId(quoteOrder.getQuoteorderId());
        quoteToOrderDto.setUserId(currentUser.getId());
        quoteToOrderDto.setTerminalTraderNature(businessChance.getTerminalTraderNature());
        log.info("CRM商机转订单，quoteToOrderDto:{}", JSON.toJSONString(quoteToOrderDto));

        Integer code;
        Integer data;
        if (Objects.equals(systemCode, SystemEnum.VEDENG.getValue())){
            R<Integer> response = businessToOrderApiService.convert(quoteToOrderDto);
            if (Objects.isNull(response)){
                throw new ServiceException("商机转订单失败");
            }

            log.info("CRM商机转订单，response:{}", JSON.toJSONString(response));
            code = response.getCode();
            data = response.getData();
            if (!ErpConstant.ZERO.equals(code) || Objects.isNull(data) || data <= 0) {
                throw new ServiceException("商机转订单失败");
            }
            
        }else {
            BaseCompanyInfoEntity entity = getDomainBySystemCode(systemCode);
            String domainBySystemCode = entity.getErpDomain();
            if (domainBySystemCode == null){
                throw new ServiceException("商机转订单失败");
            }
            // 调用http请求
            JSONObject res = HttpClientUtil.httpPost(domainBySystemCode + "/api/quoteToOrder/convert.do", JSON.toJSONString(quoteToOrderDto));
            if (Objects.isNull(res)){
                throw new ServiceException("商机转订单失败");
            }
            R<QuoteToOrderRespDto> respDtoR = JSONObject.parseObject(res.toJSONString(), new TypeReference<R<QuoteToOrderRespDto>>(){});
            if (Objects.isNull(respDtoR)){
                throw new ServiceException("商机转订单失败");
            }
            log.info("CRM商机转订单，response:{}", JSON.toJSONString(respDtoR));
            code = respDtoR.getCode();
            QuoteToOrderRespDto dtoRData = respDtoR.getData();
            if (Objects.equals(dtoRData.getTraderId(),  ErpConstant.ZERO)){
                throw new ServiceException(quoteOrder.getTraderName()  + "客户尚未同步到"+ entity.getCompanyName()+ "ERP");
            }
            if (Objects.isNull(dtoRData)){
                throw new ServiceException("商机转订单失败");
            }
            data = dtoRData.getSaleOrderId();
            if (!ErpConstant.ZERO.equals(code) || Objects.isNull(data) || data <= 0) {
                throw new ServiceException("商机转订单失败");
            }
            // 关联商机
            updateQuoteAndBusiness(quoteOrder.getQuoteorderId(), businessChanceId, entity.getFrontEndSeq());
        }
       
       
        businessChanceService.updateStageAndStageTime(businessChanceId, BusinessChanceStageEnum.WINNING_ORDER);
        try {
            String targetTabUrl = ("/orderstream/saleorder/edit.do?saleorderId=" + data);
            businessToOrderResponseDto.setJumpErpInnerUrl(targetTabUrl);
            if (crmJumpErpUrl.indexOf("sso") > -1) {
                String encodetargetTabUrl = URLEncoder.encode("/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=编辑订单", "UTF-8");
                businessToOrderResponseDto.setJumpErpUrl(crmJumpErpUrl + encodetargetTabUrl);
            } else {
                String encodetargetTabUrl = "/index.do?target=" + URLEncoder.encode(targetTabUrl, "UTF-8") + "&title=" + URLEncoder.encode("编辑订单", "UTF-8");
                businessToOrderResponseDto.setJumpErpUrl(crmJumpErpUrl + encodetargetTabUrl);
            }
        } catch (Exception e) {
            log.error("商机转订单链接转换给前端时失败，需要检查", e);
        }
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(businessChanceId);
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_TO_ORDER);

        //商机赢单时，自动关闭任务
        taskService.handleTaskCompleteForBusincessChance(businessChanceId,ErpConstant.ONE,"商机赢单，自动处理任务");

        // 发送消息
        quoteFacade.sendAppMessageCard(businessChance.getBussinessChanceId(),"转订单",
                "感谢各位的支持，"+businessChance.getBussinessChanceNo()+"商机已赢单");

        // 修改群名
        quoteFacade.updateAppChatName(businessChance.getBussinessChanceId(),
                "【赢单】商机"+businessChance.getBussinessChanceNo()+"协同支持");

        return businessToOrderResponseDto;
    }
    
    @Autowired
    private BaseCompanyInfoService baseCompanyInfoService;
    
    private BaseCompanyInfoEntity getDomainBySystemCode(Integer systemCode){
        List<BaseCompanyInfoEntity> all = baseCompanyInfoService.findAll();
        // 匹配前端系统code码
        Map<Integer, BaseCompanyInfoEntity> collect = all.stream().collect(Collectors.toMap(BaseCompanyInfoEntity::getFrontEndSeq, Function.identity(),(key1,key2)->key1));
        if (MapUtil.isEmpty(collect)){
            return null;
        }
        return collect.get(systemCode);
    }
    
    @Override
    public boolean checkHasSkutoOrderVerify(Integer businessChanceId){
        CrmQuoteOrderEntity quoteOrder = crmQuoteorderMapper.findByBussinessChanceId(businessChanceId);
        if (Objects.isNull(quoteOrder)) {
            log.info("商机转订单校验，商机未添加报价，businessChanceId:{}", businessChanceId);
            return false;
        }
        return true;
    }


    @Override
    public void toOrderVerify(Integer businessChanceId) {
        log.info("CRM商机转订单校验，businessChanceId:{}", businessChanceId);
        BussinessChanceEntity businessChance = bussinessChanceMapper.selectByPrimaryKey(businessChanceId);
        if (Objects.isNull(businessChance)) {
            log.info("商机转订单校验，商机不存在，businessChanceId:{}", businessChanceId);
            throw new ServiceException("无效的商机信息");
        }
        Integer stage = businessChance.getStage();
        if (ErpConstant.FIVE.equals(stage) || ErpConstant.SIX.equals(stage)) {
            log.info("商机转订单校验，商机状态为已赢单或已关闭，stage:{}", stage);
            throw new ServiceException("商机状态为已赢单或已关闭，不能转订单");
        }
        CrmQuoteOrderEntity quoteOrder = crmQuoteorderMapper.findByBussinessChanceId(businessChanceId);
        if (Objects.isNull(quoteOrder)) {
            log.info("商机转订单校验，商机未添加报价，businessChanceId:{}", businessChanceId);
            throw new ServiceException("商机尚未添加报价，请先添加报价并生效");
        }
        Integer validStatus = quoteOrder.getValidStatus();
        if (!ErpConstant.ONE.equals(validStatus)) {
            log.info("商机转订单校验，商机报价未生效，quoteOrder:{}", JSON.toJSONString(quoteOrder));
            throw new ServiceException("您的报价单尚未生效，请确认生效后进行转单");
        }
        //是否有未建档SKU的检查
        Integer noSku= crmQuoteorderMapper.getQuoteWithNoSkuInfoById(quoteOrder.getQuoteorderId());
        if(noSku >0){
            log.info("“商机转订单”时需把手动添加产品换成ERP已建档产品，quoteOrder:{}", JSON.toJSONString(quoteOrder));
            throw new ServiceException("“商机转订单”时需把手动添加产品换成ERP已建档产品");
        }
        Integer quoteOrderId = quoteOrder.getQuoteorderId();
        QuoteLinkOrderDto quoteLinkOrderDto = new QuoteLinkOrderDto();
        quoteLinkOrderDto.setQuoteOrderId(quoteOrderId);
        R<Integer> result = businessToOrderApiService.isExistSaleOrder(quoteLinkOrderDto);
        Integer code = result.getCode();
        Integer data = result.getData();
        if (!ErpConstant.ZERO.equals(code) || Objects.isNull(data)) {
            throw new ServiceException("商机转订单校验失败");
        }
        if (data > 0) {
            log.info("商机转订单校验，商机已转订单，quoteOrderId:{}", quoteOrderId);
            throw new ServiceException("该商机已转订单");
        }
        Integer traderId = quoteOrder.getTraderId();
        TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(traderId);
        if (Objects.isNull(traderCustomerInfoVo) || ErpConstant.ZERO.equals(traderCustomerInfoVo.getIsEnable())) {
            log.info("商机转订单校验，客户被禁用后，商机无法转化成订单，traderId:{}", traderId);
            throw new ServiceException("客户被禁用后，商机无法转化成订单");
        }
       
    }

    @Override
    public void linkOrder(LinkOrderRequestDto linkOrderRequestDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        log.info("CRM商机关联订单，linkOrderRequestDto:{}", JSON.toJSONString(linkOrderRequestDto));
        Integer businessChanceId = linkOrderRequestDto.getBusinessChanceId();
        String saleOrderNo = linkOrderRequestDto.getSaleOrderNo().trim();
        toOrderVerify(businessChanceId);
        CrmQuoteOrderEntity quoteOrder = crmQuoteorderMapper.findByBussinessChanceId(businessChanceId);
        Integer quoteOrderId = quoteOrder.getQuoteorderId();
        QuoteLinkOrderDto quoteLinkOrderDto = new QuoteLinkOrderDto();
        quoteLinkOrderDto.setBusinessChanceId(businessChanceId);
        quoteLinkOrderDto.setQuoteOrderId(quoteOrderId);
        quoteLinkOrderDto.setSaleOrderNo(saleOrderNo);
        quoteLinkOrderDto.setUserId(CurrentUser.getCurrentUser().getId());
        if (Objects.equals(linkOrderRequestDto.getSystemCode(), SystemEnum.VEDENG.getValue())){
            R<?> result = businessToOrderApiService.link(quoteLinkOrderDto);
            Integer code = result.getCode();
            String message = result.getMessage();
            if (!ErpConstant.ZERO.equals(code)) {
                throw new ServiceException(message);
            }
        }else {
            toLinkOrder(quoteLinkOrderDto,linkOrderRequestDto.getSystemCode());
        }
       
        businessChanceService.updateStageAndStageTime(businessChanceId, BusinessChanceStageEnum.WINNING_ORDER);
        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(businessChanceId);
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_TO_ORDER);
    }
    
    private void toLinkOrder(QuoteLinkOrderDto quoteLinkOrderDto,Integer systemCode){
        log.info("CRM商机转订单，关联订单，接口入参:{}", JSON.toJSONString(quoteLinkOrderDto));
        Integer businessChanceId = quoteLinkOrderDto.getBusinessChanceId();
        Integer quoteOrderId = quoteLinkOrderDto.getQuoteOrderId();
        String saleOrderNo = quoteLinkOrderDto.getSaleOrderNo();
        Integer userId = quoteLinkOrderDto.getUserId();
        if (Objects.isNull(businessChanceId) || Objects.isNull(quoteOrderId) ||
                StrUtil.isBlank(saleOrderNo) || Objects.isNull(userId)) {
            throw new ServiceException("参数异常");
        }
        BaseCompanyInfoEntity entity = getDomainBySystemCode(systemCode);
        String domainBySystemCode = entity.getErpDomain();
        if (domainBySystemCode == null){
            throw new ServiceException("关联订单失败");
        }
        LinkSaleOrderDto dto = new LinkSaleOrderDto();
        dto.setQuoteOrderId(quoteOrderId);
        dto.setSaleOrderNo(saleOrderNo);
        // 更新商机关联订单
        String res = HttpUtil.post(domainBySystemCode + "/api/quoteSaleOrder/link.do", JSON.toJSONString(dto), 10 * 5000);
        log.info("关联订单返回结果:{}", res);
        JSONObject  jsonObject = JSON.parseObject(res);
        if(jsonObject.getInteger("code") != 0){
            String message = jsonObject.getString("message");
            if (StrUtil.isNotBlank(message) && message.contains("已被其他商机关联")){
                String[] split = message.split(",");
                Integer quoteId = Integer.valueOf(split[1]);
                CrmBusinessChanceEntity validateQuote = crmQuoteorderMapper.selectBusinessChanceByQuoteorderId(quoteId);
                throw new ServiceException("该单号已被" + validateQuote.getBussinessChanceNo() + "商机关联，请确认。");
            }else {
                throw new ServiceException(jsonObject.getString("message"));
            }
        }

        updateQuoteAndBusiness(quoteOrderId, businessChanceId, entity.getFrontEndSeq());
    }

    private void updateQuoteAndBusiness(Integer quoteOrderId, Integer businessChanceId, Integer frontEndSeq) {
        QuoteorderEntity updateQuoteOrder = new QuoteorderEntity();
        updateQuoteOrder.setQuoteorderId(quoteOrderId);
        updateQuoteOrder.setLinkBdStatus(2); // 已关联
        updateQuoteOrder.setFollowOrderStatus(1); // 成单
        updateQuoteOrder.setFollowOrderTime(System.currentTimeMillis());
        quoteorderMapper.updateByPrimaryKeySelective(updateQuoteOrder);

        BussinessChanceEntity businessChanceUpdate = new BussinessChanceEntity();
        businessChanceUpdate.setBussinessChanceId(businessChanceId);
        businessChanceUpdate.setStatus(3);
        businessChanceUpdate.setFrontEndSeq(frontEndSeq);
        bussinessChanceMapper.updateByPrimaryKeySelective(businessChanceUpdate);
    }


    @Override
    public void closeBusiness(BusinessCloseDto businessCloseDto) {
        BusinessChanceDto query = new BusinessChanceDto();
        query.setBussinessChanceId(businessCloseDto.getBusinessChanceId());
        BusinessChanceDto pre = businessChanceService.selectOne(query);
        if (Objects.equals(pre.getStage(), ErpConstant.FIVE) || Objects.equals(pre.getStage(), ErpConstant.SIX)) {
            throw new ServiceException("商机已赢单或已关闭，无法操作");
        }
        businessChanceService.closeBusiness(businessCloseDto);

        BusinessChanceDto dto = new BusinessChanceDto();
        dto.setBussinessChanceId(businessCloseDto.getBusinessChanceId());
        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(dto);
        // 发送消息
        quoteFacade.sendAppMessageCard(businessChanceDto.getBussinessChanceId(), "关闭提醒",
                "感谢各位的支持，"
                        + businessChanceDto.getBussinessChanceNo()
                        + "商机已关闭，原因："
                        + businessCloseDto.getComments()
                        + "-"
                        + businessCloseDto.getCloseComments());

        // 修改群名
        quoteFacade.updateAppChatName(businessChanceDto.getBussinessChanceId(),
                "【失单】商机"+businessChanceDto.getBussinessChanceNo()+"协同支持");

        taskService.handleTaskCompleteForBusincessChance(businessCloseDto.getBusinessChanceId());//商机关闭时，自动关闭任务
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        crmVisitApiService.SendMessageForCheckBusinessChance(pre.getBussinessChanceNo(),currentUser.getUsername(),businessCloseDto.getComments() + "-" + businessCloseDto.getCloseComments());
    }

    @Override
    @Transactional
    public Integer addQuote(Integer businessChanceId,CurrentUser currentUser) {
        log.info("CRM商机添加报价，入参：businessChanceId:{}", businessChanceId);
        BussinessChanceEntity bussinessChanceEntity = bussinessChanceMapper.selectByPrimaryKey(businessChanceId);
        if (Objects.isNull(bussinessChanceEntity) || Objects.isNull(bussinessChanceEntity.getBussinessChanceId())) {
            throw new ServiceException("添加报价失败，商机不存在");
        }
        if (Objects.isNull(bussinessChanceEntity.getTraderId()) || ErpConstant.ZERO.equals(bussinessChanceEntity.getTraderId())) {
            throw new ServiceException("请选择已建档客户后，再添加报价。");
        }
        CrmQuoteOrderEntity quoteOrder = crmQuoteorderMapper.findByBussinessChanceId(businessChanceId);
        log.info("报价单quoteOrder:{}", JSON.toJSONString(quoteOrder));
        if (Objects.nonNull(quoteOrder)){
            throw new ServiceException("添加报价失败，商机已添加报价");
        }

        Integer traderId = bussinessChanceEntity.getTraderId();
        TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfoVo(traderId);
        // 转报价
        CrmQuoteOrderDto crmQuoteOrderDto = new CrmQuoteOrderDto();
        crmQuoteOrderDto.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());

        crmQuoteOrderDto.setCompanyId(1);
        crmQuoteOrderDto.setSource(0);
        crmQuoteOrderDto.setOrgId(currentUser.getOrgId());
        crmQuoteOrderDto.setUserId(currentUser.getId());
        crmQuoteOrderDto.setTraderId(bussinessChanceEntity.getTraderId());
        crmQuoteOrderDto.setTraderName(bussinessChanceEntity.getTraderName());
        crmQuoteOrderDto.setArea(traderCustomerInfoVo.getAddress());
        crmQuoteOrderDto.setCustomerType(traderCustomerInfoVo.getCustomerType());
        crmQuoteOrderDto.setCustomerNature(traderCustomerInfoVo.getCustomerNature());

        //判断是不是新客户
        CrmQuoteOrderEntity effectOrders = crmQuoteorderMapper.getEffectOrders(traderId);
        log.info("判断是不是新客户effectOrders:{}", JSON.toJSONString(effectOrders));
        if(Objects.nonNull(effectOrders)) {
            crmQuoteOrderDto.setIsNewCustomer(0);
        }else {
            crmQuoteOrderDto.setIsNewCustomer(1);
        }
        crmQuoteOrderDto.setCustomerLevel(traderCustomerInfoVo.getCustomerLevelStr());
        crmQuoteOrderDto.setTraderContactId(bussinessChanceEntity.getTraderContactId());
        crmQuoteOrderDto.setTraderContactName(bussinessChanceEntity.getTraderContactName());
        crmQuoteOrderDto.setMobile(bussinessChanceEntity.getMobile());
        crmQuoteOrderDto.setTelephone(bussinessChanceEntity.getTelephone());
//        crmQuoteOrderDto.setTraderAddressId(traderCustomerInfoVo.getAreaId());
//        crmQuoteOrderDto.setAddress(traderCustomerInfoVo.getAddress());
        crmQuoteOrderDto.setValidStatus(0);
        crmQuoteOrderDto.setAddTime(System.currentTimeMillis());
        crmQuoteOrderDto.setCreator(currentUser.getId());
        crmQuoteOrderDto.setModTime(System.currentTimeMillis());
        crmQuoteOrderDto.setUpdater(currentUser.getId());

        log.info("商机转报价createQuote:{}", JSON.toJSONString(crmQuoteOrderDto));
        businessToQuoteService.createQuote(crmQuoteOrderDto,currentUser);
        // 保存终端信息
        saveQuoteOrderTerminal(currentUser, crmQuoteOrderDto);
        // 更新商机状态
        BussinessChanceEntity update = new BussinessChanceEntity();
        update.setBussinessChanceId(bussinessChanceEntity.getBussinessChanceId());
        update.setStatus(1);
        log.info("更新商机状态updateByPrimaryKeySelective:{}", JSON.toJSONString(update));
        bussinessChanceMapper.updateByPrimaryKeySelective(update);
        // 客户档案
        businessChanceService.addTrackQuotation(currentUser,crmQuoteOrderDto.getQuoteorderNo(),bussinessChanceEntity.getTraderId());

        try {
            // 更新商机阶段（初步方案） -- 点击”添加报价“后创建了报价表格时调用
            businessChanceService.updateStageAndStageTime(businessChanceId, BusinessChanceStageEnum.PRELIMINARY_SCHEME);
        }catch (Exception e){
            log.error("更新商机阶段异常",e);
        }

        OperationLogDto logDto = new OperationLogDto();
        Map<String,String> params = new HashMap<>();
        params.put("userName",currentUser.getUsername());
        logDto.setCreator(currentUser.getId());
        logDto.setCreatorName(currentUser.getUsername());
        logDto.setOperationTime(new Date());
        logDto.setParams(params);
        logDto.setBizId(businessChanceId);
        operationLogApiService.save(logDto, BizLogEnum.BUSINESS_CHANE_TO_QUOTE);

        return crmQuoteOrderDto.getQuoteorderId();
    }

    private void saveQuoteOrderTerminal(CurrentUser currentUser, CrmQuoteOrderDto crmQuoteOrderDto) {
        // VDERP-15595 商机转报价后，需要将原商机的关联终端信息复制过来
        // step1: 先查询原商机是否有关联终端
        OrderTerminalDto exist = orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(crmQuoteOrderDto.getBussinessChanceId(), ErpConstant.ONE);
        // step2: 有的话，将商机的终端信息复制一份关联到报价下面
        if (Objects.nonNull(exist)) {
            OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
            orderTerminalDto.setBusinessId(crmQuoteOrderDto.getQuoteorderId());
            orderTerminalDto.setBusinessNo(crmQuoteOrderDto.getQuoteorderNo());
            orderTerminalDto.setBusinessType(ErpConstant.TWO);
            orderTerminalDto.setTerminalName(exist.getTerminalName());
            orderTerminalDto.setDwhTerminalId(exist.getDwhTerminalId());
            orderTerminalDto.setUnifiedSocialCreditIdentifier(exist.getUnifiedSocialCreditIdentifier());
            orderTerminalDto.setOrganizationCode(exist.getOrganizationCode());
            orderTerminalDto.setProvinceId(exist.getProvinceId());
            orderTerminalDto.setProvinceName(exist.getProvinceName());
            orderTerminalDto.setCityId(exist.getCityId());
            orderTerminalDto.setCityName(exist.getCityName());
            orderTerminalDto.setAreaId(exist.getAreaId());
            orderTerminalDto.setAreaName(exist.getAreaName());
            orderTerminalDto.setTerminalTraderNature(exist.getTerminalTraderNature());//5604
            orderTerminalDto.setNatureTypeName(exist.getNatureTypeName());//非公基层
            orderTerminalDto.setAddTime(new Date());
            orderTerminalDto.setModTime(new Date());
            orderTerminalDto.setCreator(currentUser.getId());
            orderTerminalDto.setUpdater(currentUser.getId());
            orderTerminalDto.setCreatorName(currentUser.getUsername());
            orderTerminalDto.setUpdaterName(currentUser.getUsername());
            orderTerminalApiService.save(orderTerminalDto);
            if (Objects.nonNull(orderTerminalDto.getAreaId()) && orderTerminalDto.getAreaId() > 0) {
                CrmQuoteOrderEntity update = new CrmQuoteOrderEntity();
                update.setQuoteorderId(orderTerminalDto.getBusinessId());
                update.setSalesAreaId(orderTerminalDto.getAreaId());
                update.setSalesArea(orderTerminalDto.getProvinceName() + orderTerminalDto.getCityName() + orderTerminalDto.getAreaName());
                update.setTerminalTraderName(orderTerminalDto.getTerminalName());
                update.setTerminalTraderId(0);
                update.setTerminalTraderType(0);
                crmQuoteorderMapper.updateByPrimaryKeySelective(update);
            }
        }
    }
}
