package com.vedeng.erp.business.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.SupportRecordDto;
import com.vedeng.erp.business.domain.dto.SupportRecordReqDto;
import com.vedeng.erp.business.domain.entity.BusinessChanceSeekHelp;
import com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord;
import com.vedeng.erp.business.dto.BusinessRequestDto;
import com.vedeng.erp.business.dto.BusinessSupportReqDto;
import com.vedeng.erp.business.dto.NodeDto;
import com.vedeng.erp.business.dto.SupportConfigDto;
import com.vedeng.erp.business.mapper.BusinessChanceSeekHelpMapper;
import com.vedeng.erp.business.mapper.BusinessChanceSupportRecordMapper;
import com.vedeng.erp.business.mapstruct.BusinessSupportRecordConvertor;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessSupportService;
import com.vedeng.erp.system.dto.OrganizationDto;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.OrganizationApiService;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.system.service.UserWorkApiService;
import com.vedeng.erp.trader.service.BusinessSupportApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BusinessSupportServiceImpl implements BusinessSupportService, BusinessSupportApiService {

    @Autowired
    private UserApiService userApiService;

    @Autowired
    private OrganizationApiService organizationApiService;

    @Autowired
    private BusinessChanceSupportRecordMapper businessChanceSupportRecordMapper;

    @Autowired
    private BusinessChanceSeekHelpMapper businessChanceSeekHelpMapper;

    @Autowired
    BusinessSupportRecordConvertor businessSupportRecordConvertor;

    @Autowired
    UserWorkApiService userWorkApiService;

    @Autowired
    BusinessChanceService businessChanceService;

    @Value("${erp_url}")
    private String erpUrl;

    public  final String BUSINESS_SEEK_HELP_TEMPLATE = "<font color=\"warning\">** 商机待支持通知 **</font>\n{}请求您对于商机{}进行帮助,[点击查看]({})";
    public  final String BUSINESS_SUPPORT_TEMPLATE = "<font color=\"warning\">** 商机已支持通知 **</font>\n{}对您的商机{}进行了帮助，[点击查看]({})";

    @Value("${support_org}")
    private String supportOrg;

    @Override
    public List<NodeDto> getSupportList() {
        List<SupportConfigDto> supportConfigDtos = JSONArray.parseArray(supportOrg, SupportConfigDto.class);
        List<NodeDto> result = new ArrayList<>();
        List<Integer> levelChildOrgIds = supportConfigDtos.stream().map(SupportConfigDto::getLevelChild).collect(Collectors.toList());
        List<UserDto> userList = userApiService.getUserList(levelChildOrgIds);
        Map<Integer, List<UserDto>> userListMap = userList.stream().collect(Collectors.groupingBy(UserDto::getOrgId));

        for (SupportConfigDto supportConfigDto : supportConfigDtos) {
            NodeDto nodeDto = new NodeDto();
            nodeDto.setId(supportConfigDto.getLevelRoot());
            nodeDto.setLabel(StringUtils.isNotBlank(supportConfigDto.getLevelRootAlias()) ? supportConfigDto.getLevelRootAlias() : Optional.ofNullable(organizationApiService.getOrganizationById(supportConfigDto.getLevelRoot())).map(OrganizationDto::getOrgName).orElse(""));

            NodeDto childNodeDto = new NodeDto();
            childNodeDto.setId(supportConfigDto.getLevelChild());
            childNodeDto.setLabel(StringUtils.isNotBlank(supportConfigDto.getLevelChildAlias()) ? supportConfigDto.getLevelChildAlias() : Optional.ofNullable(organizationApiService.getOrganizationById(supportConfigDto.getLevelChild())).map(OrganizationDto::getOrgName).orElse(""));

            List<UserDto> userDtos = userListMap.get(supportConfigDto.getLevelChild());
            if (CollectionUtils.isEmpty(userDtos)){
                log.info("该部门下没有员工,orgId={}",supportConfigDto.getLevelChild());
                continue;
            }
            childNodeDto.setChildren(userDtos.stream().map(userDto -> {
                NodeDto node = new NodeDto();
                node.setId(userDto.getUserId());
                node.setLabel(userDto.getUsername());
                return node;
            }).distinct().collect(Collectors.toList()));
            nodeDto.setChildren(Arrays.asList(childNodeDto));
            result.add(nodeDto);
        }
        return result;
    }

    @Override
    public void saveSupportRecord(BusinessSupportReqDto businessSupportReqDto, CurrentUser currentUser) {
        BusinessChanceSupportRecord record = businessSupportRecordConvertor.toEntity(businessSupportReqDto);
        record.setAddTime(new Date());
        record.setModTime(new Date());
        record.setCreator(currentUser.getId());
        record.setUpdater(currentUser.getId());
        record.setCreatorName(currentUser.getUsername());
        record.setUpdaterName(currentUser.getUsername());
        record.setDepartment(currentUser.getOrgName());
        log.info("保存支持记录：{}", JSON.toJSON(record));
        int i = businessChanceSupportRecordMapper.insertSelective(record);

        // 关注
        Boolean attention = businessChanceService.attention(Arrays.asList(businessSupportReqDto.getBusinessChanceId()), currentUser);

        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(BusinessChanceDto.builder().bussinessChanceId(businessSupportReqDto.getBusinessChanceId()).build());
        String msg = StrUtil.format(BUSINESS_SUPPORT_TEMPLATE,
                currentUser.getUsername(),
                businessChanceDto.getBussinessChanceNo(),
                erpUrl+"businessChance/details.do?id="+businessSupportReqDto.getBusinessChanceId()
        );
        log.info("保存支持记录结果：{},自动关注结果：{}", i,attention);

        Integer userId = businessChanceDto.getUserId();
        // 发送企业微信
        if (!ErpConstant.ONE.equals(userId) && !ErpConstant.TWO.equals(userId)) {
            try {
                log.info("商机：{}支持人：{},发送企业微信内容:{}",businessChanceDto.getBussinessChanceNo(),userId,msg);
                userWorkApiService.sendInvoiceMsg(userId, msg);
            } catch (Exception e) {
                log.warn("商机支持通知,发送通知失败：{}", userId, e);
            }
        }
    }

    @Override
    public void saveSeekHelp(BusinessRequestDto businessRequestDto, CurrentUser currentUser) {
        List<Integer> userIdList = businessRequestDto.getUserIdList();
        if (CollectionUtils.isEmpty(userIdList)){
            throw new BusinessException("客户id不能为空");
        }
        List<BusinessChanceSeekHelp> list = new ArrayList<>();
        for (Integer userId : userIdList) {
            BusinessChanceSeekHelp record = new BusinessChanceSeekHelp();
            record.setUserId(userId);
            record.setStatus(0);
            record.setAddTime(new Date());
            record.setModTime(new Date());
            record.setCreator(currentUser.getId());
            record.setUpdater(currentUser.getId());
            record.setCreatorName(currentUser.getUsername());
            record.setUpdaterName(currentUser.getUsername());
            record.setContent(businessRequestDto.getRequestContent());
            record.setBusinessChanceId(businessRequestDto.getBusinessChanceId());
            record.setIsDelete(0);
            record.setUpdateRemark("");
            list.add(record);
        }
        log.info("保存咨询方案：{}", JSON.toJSON(list));
        int i = businessChanceSeekHelpMapper.batchInsert(list);
        log.info("保存咨询方案结果：{}", i);
        BusinessChanceDto businessChanceDto = businessChanceService.selectOne(BusinessChanceDto.builder().bussinessChanceId(businessRequestDto.getBusinessChanceId()).build());
        String msg = StrUtil.format(BUSINESS_SEEK_HELP_TEMPLATE,
                currentUser.getUsername(),
                businessChanceDto.getBussinessChanceNo(),
                erpUrl+"businessChance/consultationPendingSkip.do?from=vx&leadsNo="+businessChanceDto.getBussinessChanceNo()
        );

        for (Integer userId : userIdList) {
            if (!ErpConstant.ONE.equals(userId) && !ErpConstant.TWO.equals(userId)) {
                try {
                    log.info("商机：{}咨询人：{},发送企业微信内容:{}",businessChanceDto.getBussinessChanceNo(),userId,msg);
                    userWorkApiService.sendInvoiceMsg(userId, msg);
                } catch (Exception e) {
                    log.info("商机咨询方案通知,发送通知失败：{}", userId, e);
                }
            }
        }
    }

    @Override
    public List<SupportRecordDto> getSupportRecordList(SupportRecordReqDto supportRecordReqDto) {
        List<BusinessChanceSupportRecord> businessChanceSupportRecords = businessChanceSupportRecordMapper.selectByBusinessChanceId(supportRecordReqDto.getBusinessChanceId());
        List<SupportRecordDto> collect = businessChanceSupportRecords.stream().map(e -> {
            // 转成SupportRecordDto
            SupportRecordDto recordDto = new SupportRecordDto();
            recordDto.setSupportTime(DateFormatUtils.format(e.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
            recordDto.setSupportUserName(e.getCreatorName());
            recordDto.setDepartment(e.getDepartment());
            recordDto.setBusinessLevel(e.getBusinessLevel());
            recordDto.setAmount(e.getAmount());
            recordDto.setOrderDate(DateFormatUtils.format(e.getOrderDate(),"yyyy-MM-dd"));
            recordDto.setNextSupportDate(Objects.isNull(e.getNextSupportDate()) ? "无" : DateFormatUtils.format(e.getNextSupportDate(),"yyyy-MM-dd"));
            recordDto.setContent(e.getContent());
            return recordDto;
        }).collect(Collectors.toList());
        return collect;
    }
}
