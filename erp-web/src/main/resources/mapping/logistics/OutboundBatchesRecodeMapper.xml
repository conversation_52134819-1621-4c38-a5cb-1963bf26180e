<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.OutboundBatchesRecodeMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.OutboundBatchesRecode">
        <id property="id" column="ID" jdbcType="INTEGER"/>
        <result property="batchNo" column="BATCH_NO" jdbcType="VARCHAR"/>
        <result property="comments" column="COMMENTS" jdbcType="VARCHAR"/>
        <result property="auditStatus" column="AUDIT_STATUS" jdbcType="TINYINT"/>
        <result property="isEnable" column="IS_ENABLE" jdbcType="TINYINT"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="batchType" column="BATCH_TYPE" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
        <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
        <result property="deliveryTime" column="DELIVERY_TIME" jdbcType="BIGINT"/>
        <result property="uploadStatus" column="UPLOAD_STATUS" jdbcType="TINYINT"/>
        <result property="onlineConfirm" column="ONLINE_CONFIRM" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,BATCH_NO,COMMENTS,
        AUDIT_STATUS,IS_ENABLE,CREATOR,BATCH_TYPE,
        MOD_TIME,UPDATER,ADD_TIME,UPLOAD_STATUS,DELIVERY_TIME,ONLINE_CONFIRM
    </sql>

    <insert id="insertOrUpdate" parameterType="com.vedeng.logistics.model.OutboundBatchesRecode">
        insert into
            T_OUTBOUND_BATCHES_RECODE
        (
            BATCH_NO,
            BATCH_TYPE,
            DELIVERY_TIME,
            UPDATER,
            CREATOR,
            ADD_TIME,
            MOD_TIME)
        values
            (
                #{batchNo},
                #{batchType},
                #{deliveryTime},
                #{updater},
                #{creator},
                #{addTime},
                #{modTime}
            )
            on duplicate key update
            DELIVERY_TIME=values(DELIVERY_TIME),
            MOD_TIME=values(MOD_TIME)
    </insert>
    <select id="findAllByBatchNoList" resultType="com.vedeng.logistics.model.OutboundBatchesRecode">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_OUTBOUND_BATCHES_RECODE a
        WHERE 1=1
        AND a.IS_ENABLE = 0
        AND a.`BATCH_NO` IN
        <foreach item="batchNo" index="index" collection="list" open="(" separator="," close=")">
            #{batchNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <update id="approveAuditStatusById">
        update T_OUTBOUND_BATCHES_RECODE a set a.AUDIT_STATUS = 1,a.MOD_TIME = #{nowTime,jdbcType=BIGINT},a.COMMENTS =''
        where a.ID in
        <foreach collection="approveList" item="ap" index="index" open="(" close=")" separator=",">
            #{ap.id,jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="refuseAuditStatusById">
        update T_OUTBOUND_BATCHES_RECODE a
        set a.AUDIT_STATUS = 0,
            a.UPLOAD_STATUS = 0,
            a.COMMENTS     = #{r.refuseReason,jdbcType=VARCHAR},
            a.MOD_TIME     = #{nowTime,jdbcType=BIGINT}
        where a.ID = #{r.id,jdbcType=INTEGER}
    </update>

    <select id="getById" resultMap="BaseResultMap">
        select
            <include refid="Base_Column_List">
            </include>
        from
        T_OUTBOUND_BATCHES_RECODE a
        where
        a.id = #{batchId,jdbcType=INTEGER}
    </select>
    <select id="getByBatchNo" resultType="com.vedeng.logistics.model.OutboundBatchesRecode">
        select
        <include refid="Base_Column_List">
        </include>
        from
        T_OUTBOUND_BATCHES_RECODE a
        where
        a.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
    </select>

    <update id="updateAuditStatusById">
        update
            T_OUTBOUND_BATCHES_RECODE a
        set
            a.AUDIT_STATUS = #{status,jdbcType=INTEGER}
        where
        a.id = #{batchId,jdbcType=INTEGER}
    </update>


    <update id="updateUploadStatusById">
        update
            T_OUTBOUND_BATCHES_RECODE a
        set
            a.UPLOAD_STATUS = #{status,jdbcType=INTEGER}
        where
            a.id = #{batchId,jdbcType=INTEGER}
    </update>

    <update id="updateOnlineConfirmByBatchNos">
        update T_OUTBOUND_BATCHES_RECODE a set a.ONLINE_CONFIRM = #{onlineConfirm,jdbcType=INTEGER}
        ,a.MOD_TIME = UNIX_TIMESTAMP(NOW()) * 1000
        <if test="uploadStatus != null and uploadStatus != ''">
            ,a.UPLOAD_STATUS = #{uploadStatus,jdbcType=INTEGER}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            ,a.AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER}
        </if>
        where a.BATCH_NO in
        <foreach collection="batchNos" item="batchNo" index="index" open="(" close=")" separator=",">
            #{batchNo,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateUploadStatusByBatchNo">
        update
            T_OUTBOUND_BATCHES_RECODE a
        set
            a.UPLOAD_STATUS = #{status,jdbcType=INTEGER}
        where
            a.BATCH_NO = #{batchNo,jdbcType=VARCHAR}
    </update>
    <update id="updateCommentsById">
        update
            T_OUTBOUND_BATCHES_RECODE a
        set
            a.COMMENTS = ''
        where
            a.id = #{batchId,jdbcType=INTEGER}
    </update>
    <update id="updateCommentsDiyById">
        update
            T_OUTBOUND_BATCHES_RECODE a
        set
            a.COMMENTS = #{comments,jdbcType=VARCHAR}
        where
            a.id = #{batchId,jdbcType=INTEGER}
    </update>
</mapper>
