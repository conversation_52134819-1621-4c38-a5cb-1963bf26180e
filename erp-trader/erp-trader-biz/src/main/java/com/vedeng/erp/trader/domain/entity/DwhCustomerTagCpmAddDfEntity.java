package com.vedeng.erp.trader.domain.entity;

import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;


/**
 * @description 客户标签CPM补充计算表(大数据)
 * <AUTHOR>
 * @date 2024/4/1 8:50
 **/

@Getter
@Setter
@NoArgsConstructor
public class DwhCustomerTagCpmAddDfEntity {
    /**
    * 主键
    */
    private Long id;

    /**
    * 交易者ID
    */
    private Integer traderId;

    /**
    * 客户ID
    */
    private Integer traderCustomerId;

    /**
    * 办公区域
    */
    private String areaIds;

    /**
    * 商品类型 0 设备、1 高值耗材、2 中低值耗材、3 试剂、4 软件；
    */
    private String skuTypes;

    /**
    * 关系型客户 0 卫健委、1 医联体、2 其他
    */
    private String relations;

    /**
    * ETL DAY
    */
    private Date etlDay;
}