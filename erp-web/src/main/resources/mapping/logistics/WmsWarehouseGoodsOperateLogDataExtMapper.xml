<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.ext.WmsWarehouseGoodsOperateLogDataExtMapper">
  <select id="selectByMinId" resultType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    SELECT
      A.WAREHOUSE_GOODS_OPERATE_LOG_ID AS  warehouseGoodsOperateLogId,
      A.OPERATE_TYPE as operateType,
      (  CASE    A.OPERATE_TYPE
           WHEN 2 THEN    SA.SALEORDER_NO
           WHEN 4 THEN    ASS.AFTER_SALES_NO
           WHEN 6 THEN    ASS.AFTER_SALES_NO
           WHEN 7 THEN    ASS.AFTER_SALES_NO
           WHEN 10 THEN   IF    ( <PERSON><PERSON>LEND_OUT_ID IS NOT NULL,
                                  <PERSON>.LEND_OUT_NO,
                                  OO.order_no )
           WHEN 13 THEN    OO.order_no
           WHEN 14 THEN    OO.order_no
           WHEN 15 THEN    IA.INVENTORY_ADJUSTMENT_NO
           WHEN 19 THEN    WUCO.WMS_UNIT_CONVERSION_ORDER_NO
           WHEN 18 THEN    OO.order_no
           ELSE ''
        END   ) AS  businessNo,
      ( CASE A.OPERATE_TYPE
          WHEN 2 THEN SA.TRADER_ID
          WHEN 4 THEN SAAFTER.TRADER_ID
          else 0
        END ) AS traderId  ,
      ( CASE A.OPERATE_TYPE
          WHEN 2 THEN SA.SALEORDER_ID
          WHEN 4 THEN SAAFTER.SALEORDER_ID
          else 0
        END ) AS orderId  ,
      A.RELATED_ID relatedId,
      AB.SKU_NO skuNo,
      AB.SKU_NAME skuName,
      AB.MODEL skuModel,
      AB.SPEC skuSpec,
      A.NUM skuNum,
      B.BRAND_NAME brandName,
      A.BARCODE_FACTORY barcodeFactory,
      A.BATCH_NUMBER batchNumber,
      A.VEDENG_BATCH_NUMER  vedengBatchNumer,
      FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) AS outTime,
      FROM_UNIXTIME( A.PRODUCT_DATE / 1000, '%Y-%m-%d %H:%i:%S' ) AS productDate,
      FROM_UNIXTIME( A.EXPIRATION_DATE / 1000, '%Y-%m-%d %H:%i:%S' ) AS   expirationDate,
      A.STERILZATION_BATCH_NUMBER  sterilzationBatchNumber
    FROM
      T_WAREHOUSE_GOODS_OPERATE_LOG A
        LEFT JOIN
      V_CORE_SKU AB
      ON AB.SKU_ID = A.GOODS_ID
        LEFT JOIN
      V_CORE_SPU BB
      ON AB.SPU_ID = BB.SPU_ID
        LEFT JOIN
      T_BRAND B
      ON B.BRAND_ID = BB.BRAND_ID
        LEFT JOIN
      T_UNIT U
      ON U.UNIT_ID = AB.BASE_UNIT_ID
        LEFT JOIN
      T_SALEORDER_GOODS SG
      ON SG.SALEORDER_GOODS_ID = A.RELATED_ID
        AND A.OPERATE_TYPE = 2
        LEFT JOIN
      T_SALEORDER SA
      ON SG.SALEORDER_ID = SA.SALEORDER_ID
        LEFT JOIN
      T_AFTER_SALES_GOODS ASG
      ON ASG.AFTER_SALES_GOODS_ID = A.RELATED_ID
        AND A.OPERATE_TYPE IN ( 4, 6, 7 )
        LEFT JOIN
      T_AFTER_SALES ASS
      ON ASG.AFTER_SALES_ID = ASS.AFTER_SALES_ID
        left join     T_SALEORDER SAAFTER
                      ON ASS.ORDER_ID = SAAFTER.SALEORDER_ID
        LEFT JOIN
      T_AFTER_SALES_DETAIL ASD
      ON ASG.AFTER_SALES_ID = ASD.AFTER_SALES_ID
        LEFT JOIN
      T_WMS_OUTPUT_ORDER_GOODS OOG
      ON OOG.id = A.RELATED_ID
        AND A.OPERATE_TYPE IN (  10,  13, 14 ,  18)
        AND OOG.SKU_NO = AB.SKU_NO
        LEFT JOIN
      T_WMS_OUTPUT_ORDER OO
      ON OO.id = OOG.wms_output_order_id
        LEFT JOIN
      T_LEND_OUT L
      ON A.RELATED_ID = L.LEND_OUT_ID
        AND A.OPERATE_TYPE = 10
        AND A.GOODS_ID = L.GOODS_ID
        LEFT JOIN
      T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD
      ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID
        AND A.OPERATE_TYPE = 15
        LEFT JOIN
      T_WMS_INVENTORY_ADJUSTMENT IA
      ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
        LEFT JOIN
      T_WMS_UNIT_CONVERSION_ORDER_ITEM WUCOI
      ON WUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = A.RELATED_ID
        AND A.OPERATE_TYPE = 19
        LEFT JOIN
      T_WMS_UNIT_CONVERSION_ORDER WUCO
      ON WUCO.WMS_UNIT_CONVERSION_ORDER_ID = WUCOI.WMS_UNIT_CONVERSION_ORDER_ID
        LEFT JOIN
      T_BARCODE BR
      ON BR.BARCODE_ID = A.BARCODE_ID
    WHERE WAREHOUSE_GOODS_OPERATE_LOG_ID>#{minId}
      and A.LOG_TYPE = 1
      AND A.IS_ENABLE = 1
      AND A.NUM &lt; 0
    ORDER BY WAREHOUSE_GOODS_OPERATE_LOG_ID ASC
      LIMIT #{limit}
  </select>
  <select id="selectByIds" resultType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    SELECT
      A.WAREHOUSE_GOODS_OPERATE_LOG_ID AS  warehouseGoodsOperateLogId,
      A.OPERATE_TYPE as operateType,
      (  CASE    A.OPERATE_TYPE
           WHEN 2 THEN    SA.SALEORDER_NO
           WHEN 4 THEN    ASS.AFTER_SALES_NO
           WHEN 6 THEN    ASS.AFTER_SALES_NO
           WHEN 7 THEN    ASS.AFTER_SALES_NO
           WHEN 10 THEN   IF    ( L.LEND_OUT_ID IS NOT NULL,
                                  L.LEND_OUT_NO,
                                  OO.order_no )
           WHEN 13 THEN    OO.order_no
           WHEN 14 THEN    OO.order_no
           WHEN 15 THEN    IA.INVENTORY_ADJUSTMENT_NO
           WHEN 19 THEN    WUCO.WMS_UNIT_CONVERSION_ORDER_NO
           WHEN 18 THEN    OO.order_no
           ELSE ''
        END   ) AS  businessNo,
      ( CASE A.OPERATE_TYPE
          WHEN 2 THEN SA.TRADER_ID
          WHEN 4 THEN SAAFTER.TRADER_ID
          else 0
        END ) AS traderId  ,
      ( CASE A.OPERATE_TYPE
          WHEN 2 THEN SA.SALEORDER_ID else 0
        END ) AS orderId  ,
      A.RELATED_ID relatedId,
      AB.SKU_NO skuNo,
      AB.SKU_NAME skuName,
      AB.MODEL skuModel,
      AB.SPEC skuSpec,
      A.NUM skuNum,
      B.BRAND_NAME brandName,
      A.BARCODE_FACTORY barcodeFactory,
      A.BATCH_NUMBER batchNumber,
      A.VEDENG_BATCH_NUMER  vedengBatchNumer,
      FROM_UNIXTIME( A.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%S' ) AS outTime,
      FROM_UNIXTIME( A.PRODUCT_DATE / 1000, '%Y-%m-%d %H:%i:%S' ) AS productDate,
      FROM_UNIXTIME( A.EXPIRATION_DATE / 1000, '%Y-%m-%d %H:%i:%S' ) AS   expirationDate,
      A.STERILZATION_BATCH_NUMBER  sterilzationBatchNumber
    FROM
      T_WAREHOUSE_GOODS_OPERATE_LOG A
        LEFT JOIN
      V_CORE_SKU AB
      ON AB.SKU_ID = A.GOODS_ID
        LEFT JOIN
      V_CORE_SPU BB
      ON AB.SPU_ID = BB.SPU_ID
        LEFT JOIN
      T_BRAND B
      ON B.BRAND_ID = BB.BRAND_ID
        LEFT JOIN
      T_UNIT U
      ON U.UNIT_ID = AB.BASE_UNIT_ID
        LEFT JOIN
      T_SALEORDER_GOODS SG
      ON SG.SALEORDER_GOODS_ID = A.RELATED_ID
        AND A.OPERATE_TYPE = 2
        LEFT JOIN
      T_SALEORDER SA
      ON SG.SALEORDER_ID = SA.SALEORDER_ID
        LEFT JOIN
      T_AFTER_SALES_GOODS ASG
      ON ASG.AFTER_SALES_GOODS_ID = A.RELATED_ID
        AND A.OPERATE_TYPE IN ( 4, 6, 7 )
        LEFT JOIN
      T_AFTER_SALES ASS
      ON ASG.AFTER_SALES_ID = ASS.AFTER_SALES_ID
        left join     T_SALEORDER SAAFTER
                      ON ASS.ORDER_ID = SAAFTER.SALEORDER_ID
        LEFT JOIN
      T_AFTER_SALES_DETAIL ASD
      ON ASG.AFTER_SALES_ID = ASD.AFTER_SALES_ID
        LEFT JOIN
      T_WMS_OUTPUT_ORDER_GOODS OOG
      ON OOG.id = A.RELATED_ID
        AND A.OPERATE_TYPE IN (  10,  13, 14 ,  18)
        AND OOG.SKU_NO = AB.SKU_NO
        LEFT JOIN
      T_WMS_OUTPUT_ORDER OO
      ON OO.id = OOG.wms_output_order_id
        LEFT JOIN
      T_LEND_OUT L
      ON A.RELATED_ID = L.LEND_OUT_ID
        AND A.OPERATE_TYPE = 10
        AND A.GOODS_ID = L.GOODS_ID
        LEFT JOIN
      T_WMS_INVENTORY_ADJUSTMENT_DETAIL IAD
      ON IAD.INVENTORY_ADJUSTMENT_DETAIL_ID = A.RELATED_ID
        AND A.OPERATE_TYPE = 15
        LEFT JOIN
      T_WMS_INVENTORY_ADJUSTMENT IA
      ON IA.INVENTORY_ADJUSTMENT_ID = IAD.INVENTORY_ADJUSTMENT_ID
        LEFT JOIN
      T_WMS_UNIT_CONVERSION_ORDER_ITEM WUCOI
      ON WUCOI.WMS_UNIT_CONVERSION_ORDER_ITEM_ID = A.RELATED_ID
        AND A.OPERATE_TYPE = 19
        LEFT JOIN
      T_WMS_UNIT_CONVERSION_ORDER WUCO
      ON WUCO.WMS_UNIT_CONVERSION_ORDER_ID = WUCOI.WMS_UNIT_CONVERSION_ORDER_ID
        LEFT JOIN
      T_BARCODE BR
      ON BR.BARCODE_ID = A.BARCODE_ID
    WHERE WAREHOUSE_GOODS_OPERATE_LOG_ID in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
        #{id}
    </foreach>
      and A.LOG_TYPE = 1
      AND A.IS_ENABLE = 1
      AND A.NUM &lt; 0
  </select>

  <select id="selectMaxLogId" resultType="java.lang.Integer">
    select  MAX(WAREHOUSE_GOODS_OPERATE_LOG_ID)  from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
  </select>
  <select id="selectExpressNosByExpressId" resultType="java.lang.String">
    select group_concat(c.LOGISTICS_NO) from T_R_EXPRESS_OPERATE_LOG a left join T_EXPRESS_DETAIL b ON a.EXPRESS_DETAIL_ID=b.EXPRESS_DETAIL_ID
                                                                       left join T_EXPRESS c ON c.EXPRESS_ID=b.EXPRESS_ID
    where WAREHOUSE_GOODS_OPERATE_LOG_ID=#{warehouseGoodsOperateLogId}
    group by a.WAREHOUSE_GOODS_OPERATE_LOG_ID
  </select>
  <select id="selectByLogId" resultType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    select   ID as id,QUALITY_REPORT_OSS as qualityReportOss
    from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    where WAREHOUSE_GOODS_OPERATE_LOG_ID=#{logId} limit 1
  </select>
  <select id="selectLatestReportBySkuAndBatchNum"
          resultType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    select   ID as id,QUALITY_REPORT_OSS as qualityReportOss
    from T_WAREHOUSE_GOODS_OPERATE_LOG_DATA
    where  SKU_NO=#{sku} and  BATCH_NUMBER=#{batchNum}
    order by ID desc limit 1
  </select>
  <select id="selectLatestIdByExpressModTime" resultType="com.vedeng.logistics.model.WmsWarehouseGoodsOperateLogData">
    SELECT group_concat(A.LOGISTICS_NO )  expressNos ,
           CASE B.BUSINESS_TYPE WHEN 496 THEN 2 WHEN 582 THEN 4 ELSE 0 END operateType,
           B.RELATED_ID relatedId FROM T_EXPRESS A LEFT JOIN T_EXPRESS_DETAIL B ON A.EXPRESS_ID=B.EXPRESS_ID
    WHERE A.EXPRESS_ID>1234943 AND B.BUSINESS_TYPE IN(496,582) and IS_ENABLE=1 AND LENGTH(A.LOGISTICS_NO)>2
      AND  A.ADD_TIME >   #{preSystemTime}
    group by B.BUSINESS_TYPE,B.RELATED_ID
  </select>
  <select id="selectExpressNosByRidAndBtype" resultType="java.lang.String">
    SELECT group_concat(A.LOGISTICS_NO )  LOGISTICS_NOS   FROM T_EXPRESS A LEFT JOIN T_EXPRESS_DETAIL B ON A.EXPRESS_ID=B.EXPRESS_ID
    WHERE A.EXPRESS_ID>1234943 AND B.BUSINESS_TYPE IN(496,582) and IS_ENABLE=1 AND LENGTH(A.LOGISTICS_NO)>2
      AND B.RELATED_ID=#{relatedId} and B.BUSINESS_TYPE=#{businessType}
    group by B.BUSINESS_TYPE,RELATED_ID
  </select>
</mapper>