// 添加拜访记录
void function () {
    new Vue({
        el: '#page-container',
        data: {
            canAjax: true,
            pageLoading: true, // 页面加载状态

            /* Card1 */
            // 客户名称
            traderName: '',
            traderId: '',
            tycFlag: 'N', //是否是天眼查带入
            traderInfo: {},
            // 业务类型
            businessType: '',
            BusinessTypeRadio: [],
            // 产品信息
            goodsInfo: '',
            // 预计成单金额
            amount: '',
            // 预计成单日期
            orderTime: '',

            /* Card2 */
            phone: '', // 手机
            telephone: '', // 固话
            // 其他联系方式
            otherContact: [],
            otherContactInfo: '', // 接口入参
            // 联系人
            contact: '', // 联系人
            traderContactId: '', // 联系人id
            isFromPhone: false, // 是否通过手机带入的

            // 提交
            cansubmit: true,
        },
        async created() {
            this.visitRecordId = document.getElementById('visitRecordId').value || ''; // 拜访计划id

            // 商机类型-字典
            await this.$axios.post('/crm/sysOption/public/getByParentId?parentId=5700').then(({ data }) => {
                if (data.success) {
                    let arr = data.data || [];
                    this.BusinessTypeRadio = arr.map(m1 => {
                        return {
                            label: m1.title,
                            value: m1.sysOptionDefinitionId
                        }
                    })
                }
            });

            this.initData();
        },
        mounted () {
        },
        methods: {
            initData () {
                this.$axios.get(`/crm/visitrecord/m/detail?id=${this.visitRecordId}`).then(({data}) => {
                    if (data.success) {
                        let detail = data.data;
                        console.log('detail', detail);

                        this.traderId = detail.traderId || '';
                        this.traderName = detail.customerName || '';
                        let visitCustomerVo = detail.visitCustomerVo || {};
                        if (Object.keys(visitCustomerVo).length) {
                            this.tycFlag = visitCustomerVo.tycFlag || '';

                            this.traderInfo = {
                                traderId: this.traderId,
                                tycFlag: this.tycFlag, // Y:天眼查客户  N:不是天眼查客户 
                                saleName: visitCustomerVo.belongerName,
                                belong: visitCustomerVo.belong,
                                share: visitCustomerVo.share,
                                customerGrade: visitCustomerVo.customerGrade,
                            }
                        }

                        // 联系人信息
                        this.phone = detail.contactMobile || '';
                        this.telephone = detail.contactTele || '';
                        this.contact = detail.contactName || '';
                        if (detail.otherContact) {
                            this.otherContactInfo = detail.otherContact;
                            this.initOtherContact(detail.otherContact);
                        }

                        // 拜访时间
                        // const today = new Date();
                        // let year = today.getFullYear();
                        // let month = today.getMonth() + 1;
                        // let day = today.getDate();
                        // this.orderTime = `${year}-${ month > 9? month: '0' + month }-${ day > 9? day: '0' + day }`; // ???
                    }

                    this.pageLoading = false;
                })
            },

            /* 客户名称 */
            handlerTrader(data) {
                console.log('Handler traderName:', this.traderName, data);
                this.traderInfo = data || {};
                this.traderId = data.traderId || '';
                this.tycFlag = data.tycFlag || 'N';
                
            },

            // 业务类型
            businessTypeChange () {
                console.log('Handler Visit', this.visitType);
            },

            disabledDate (current) {
                // 获取今天的日期（去掉时间部分）
                const today = new Date();
                today.setHours(0, 0, 0, 0); // 将时间部分设置为 00:00:00

                // 将传入的日期（current）转换为 Date 对象，并去掉时间部分
                const currentDate = new Date(current);
                currentDate.setHours(0, 0, 0, 0);

                // 如果 currentDate 小于 today，返回 true（禁用）
                return currentDate < today;
            },
            handlerDateChange (data) {
                console.log('Handler orderTime:', data, this.orderTime);
            },

            // 初始化其他联系方式
            initOtherContact (otherStr) {
                let items = otherStr.split('##');
                let arr = [];
                items.forEach((item)=> {
                    let key = item.split(':')[0];
                    let val = item.split(':')[1];
                    if (key && val) {
                        arr.push({
                            label: key,
                            value: val,
                            id: Math.random()
                        })
                    }
                })
                this.otherContact = arr;
            },

            /* 手机 */
            handlerPhone (val) {
                console.log('Handler Phone', val);
                if (this.traderId) {
                    if (this.isFromPhone) { // 当前联系人是选择的
                        this.contact = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    } else if (val.choosed) {
                        this.isFromPhone = true;
                        this.contact = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    }
                }
            },

            /* 其他联系方式 */
            addContact () {
                if (this.otherContact.length < 5) {
                    this.$refs.otherContactDialog.show();
                }
            },
            handlerAddContact (val) {
                console.log('handlerAddContact:::::', val);
                this.otherContact.push({
                    label: val,
                    value: '',
                    id: Math.random()
                })
            },
            handlerAddOnInput () {
                let arr = [];
                this.otherContact.forEach(item => {
                    if (item.value.trim()) {
                        arr.push(`${item.label}:${item.value.trim()}`);
                    }
                });
                let str = arr.join('##');
                this.otherContactInfo = str;
            },
            handlerAddDelete (index) {
                this.otherContact.splice(index, 1);
                this.handlerAddOnInput();
            },

            /* 联系人 */
            handlerContact() {
                this.isFromPhone = false;
                this.traderContactId = '';
            },
            


            errorMsg (txt) {
                this.$message({ type: 'error', message: txt });
            },
            // 验证表单
            formBindValid () {
                if (!this.businessType) {
                    this.errorMsg('请选择业务类型');
                    return false;
                }
                else if (!this.goodsInfo) {
                    this.errorMsg('请输入产品信息');
                    return false;
                }
                else if (!this.amount) {
                    this.errorMsg('请输入成单金额');
                    return false;
                }
                else if (!/^\d{1,8}(\.\d{1,2})?$/.test(this.amount) || parseFloat(this.amount) <= 0) {
                    this.errorMsg('预计成单金额最多支持8位数+2位小数，仅限正数');
                    return false;
                }
                else if (!this.orderTime) {
                    this.errorMsg('请选择成单日期');
                    return false;
                }

                else if (!this.phone.trim() && !this.telephone.trim() && !this.otherContactInfo.trim()) {
                    this.errorMsg('手机、固话和其他联系方式至少填写一项');
                    return false;
                }
                else if (this.phone && this.phone.length !== 11) {
                    this.errorMsg('请输入11位手机号码');
                    return false;
                }
                else if (!this.contact) {
                    this.errorMsg('请输入联系人');
                    return false;
                }
                return true;
            },

            async submit () {
                console.log(' -- submit check -- ', this.cansubmit, this.formBindValid());
                if (!this.cansubmit || !this.formBindValid()) {
                    return;
                }

                let reqData = {
                    visitId: this.visitRecordId,
                    // traderId: this.traderId,
                    traderName: this.traderName,
                    businessType: this.businessType,
                    goodsInfo: this.goodsInfo,
                    amount: this.amount,
                    orderTime: this.orderTime,

                    phone: this.phone,
                    telephone: this.telephone,
                    otherContactInfo: this.otherContactInfo,
                    contact: this.contact,
                    traderContactId: this.traderContactId,      // 联系人id
                };

                console.log('====>', reqData);
                // return;

                this.cansubmit = false;
                this.$axios.post('/crm/visitrecord/m/addBusinessChance', reqData).then(({data}) => {
                    if (data.success) {
                        this.$message.success('新建成功');
                        setTimeout(() => {
                            window.localStorage.setItem('crm_visit_detail_refresh', '1');
                            window.history.go(-1);
                        }, 1000);
                    } else {
                        this.cansubmit = true;
                        this.$message({
                            message: data.message
                        })
                    }
                })
            },

        }
    })
}.call(this);