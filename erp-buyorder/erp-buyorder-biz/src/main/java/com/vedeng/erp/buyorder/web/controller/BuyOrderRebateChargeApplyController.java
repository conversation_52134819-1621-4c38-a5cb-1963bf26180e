package com.vedeng.erp.buyorder.web.controller;

import com.vedeng.common.core.annotation.ExcludeAuthorization;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * 采购返利结算收款申请
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 16:27
 */
@Controller
@RequestMapping("/buyOrder/rebateChargeApply")
public class BuyOrderRebateChargeApplyController {

    @Value("${oss_http}")
    private String ossHttp;

    /**
     * 采购返利结算收款申请列表页面
     *
     * @return ModelAndView
     */
    @RequestMapping("/index")
    @ExcludeAuthorization
    public ModelAndView index() {
        return new ModelAndView("vue/view/buyorderrebatechargeapply/list");
    }

    /**
     * 新增/编辑 采购返利结算收款申请页面
     *
     * @return ModelAndView
     */
    @RequestMapping("/edit")
    @ExcludeAuthorization
    public ModelAndView edit() {
        ModelAndView modelAndView = new ModelAndView("vue/view/buyorderrebatechargeapply/edit");
        modelAndView.addObject("ossHttp", ossHttp);
        return modelAndView;
    }

    /**
     * 采购返利结算收款申请 详情页面
     *
     * @param buyOrderRebateChargeId 采购返利结算收款申请主键id
     * @return ModelAndView
     */
    @RequestMapping("/detail")
    @ExcludeAuthorization
    public ModelAndView detail(@RequestParam Integer buyOrderRebateChargeId) {
        ModelAndView view = new ModelAndView("vue/view/buyorderrebatechargeapply/detail");
        view.addObject("buyOrderRebateChargeId", buyOrderRebateChargeId);
        return view;
    }
}