package com.vedeng.erp.finance.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.dto.CheckInvoiceApplyResponseDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.InvoiceApplyTypeEnum;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.enums.YNEnum;
import com.vedeng.erp.finance.facade.ApplyInvoiceFacade;
import com.vedeng.erp.finance.mapper.InvoiceApplyMapper;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.saleorder.dto.OrderFinanceInfoDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.enums.AfterSalesCollectionStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleOrderInvoiceStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleOrderStatusEnum;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 *
 * 申请发票Facade实现类
 *
 */
@Service
@Slf4j
public class ApplyInvoiceFacadeImpl implements ApplyInvoiceFacade {
    @Autowired
    private TraderFinanceApiService traderFinanceApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;

    @Autowired
    InvoiceApplyService invoiceApplyService;

    @Autowired
    InvoiceCheckApiService invoiceCheckApiService;

    @Autowired
    OrderPeerApiService orderPeerApiService;

    @Autowired
    TraderCustomerApiService traderCustomerApiService;

    /**
     * 申请状态
     */
    private static final List<Integer> APPLICATE_STATUS = Arrays.asList(SaleOrderStatusEnum.IN_PROGRESS.getCode(), SaleOrderStatusEnum.FINISHED.getCode());

    /**
     * 检查发票申请
     * @param invoiceApplyDto
     */
    @Override
    public CheckInvoiceApplyResponseDto checkApplyInvoice(InvoiceApplyDto invoiceApplyDto) {
        Integer saleOrderId = invoiceApplyDto.getRelatedId();
        // 1.校验业务状态
        invoiceCheckApiService.checkSaleOrderApplyInvoice(saleOrderId);
        // 2.检查客户财务资质
        CheckInvoiceApplyResponseDto checkInvoiceApplyResponseDto = getCheckInvoiceApplyResponseDto(invoiceApplyDto);
        if (!checkInvoiceApplyResponseDto.isPassCredential()){
            // 资质不通过直接返回
            return checkInvoiceApplyResponseDto;
        }
        // 3.检查是否符合票货同行
        boolean orderIsGoodsPeer = orderPeerApiService.getOrderIsGoodsPeer(saleOrderId);
        if (orderIsGoodsPeer){
            // 符合票货同行直接返回并提示
            checkInvoiceApplyResponseDto.setPassPeerCheck(Boolean.FALSE);
            return checkInvoiceApplyResponseDto;
        }
        checkInvoiceApplyResponseDto.setPassPeerCheck(Boolean.TRUE);
        // 4.申请检查是否通过
        InvoiceCheckResultDto invoiceCheckResultDto = checkCheckRule(saleOrderId);
        boolean passRuleCheck = invoiceCheckResultDto.getSuccess();
        checkInvoiceApplyResponseDto.setPassRuleCheck(passRuleCheck);
        if(!passRuleCheck){
            // 申请检查不通过直接返回
            return checkInvoiceApplyResponseDto;
        }
        return checkInvoiceApplyResponseDto;
    }

    @Override
    public CheckInvoiceApplyResponseDto checkApplyInvoiceAfter(Integer afterSalesId) {
        if (Objects.isNull(afterSalesId)){
            log.info("参数有误，售后id不能为空");
            throw new ServiceException("参数有误，售后id不能为空");
        }
        AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSalesDto)) {
            log.info("售后开票申请,未查询到售后单信息,afterSalesId:{}", afterSalesId);
            throw new ServiceException("未查询到售后单信息");
        }
        Integer subjectType = afterSalesDto.getSubjectType();
        Integer type = afterSalesDto.getType();
        if (!FinanceConstant.ID_535.equals(subjectType) && !FinanceConstant.ID_537.equals(subjectType)) {
            log.info("售后开票申请,售后单类型错误,subjectType:{}", subjectType);
            throw new ServiceException("售后单类型错误:不是销售或第三方售后");
        }
        AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetailApiDto)) {
            log.info("售后开票申请,未查询到售后单详情信息,afterSalesId:{}", afterSalesId);
            throw new ServiceException("未查询到售后单详情信息");
        }
        Integer invoiceType = afterSalesDetailApiDto.getInvoiceType();
        BigDecimal serviceAmount = afterSalesDetailApiDto.getServiceAmount();

        // 生效状态
        Integer validStatus = afterSalesDto.getValidStatus();
        if (YNEnum.N.getCode().equals(validStatus)){
            throw new ServiceException("生效状态为未生效，不可申请开票");
        }
        // 单据状态
        Integer status = afterSalesDto.getAtferSalesStatus();
        if(!APPLICATE_STATUS.contains(status)){
            throw new ServiceException("单据状态为" + SaleOrderStatusEnum.getDesc(status) + ",不可申请开票");
        }
        // 开票状态
        Integer invoiceStatus = afterSalesDetailApiDto.getInvoiceStatus();
        if (SaleOrderInvoiceStatusEnum.ALL.getCode().equals(invoiceStatus)){
            throw new ServiceException("开票状态为" + SaleOrderInvoiceStatusEnum.ALL.getDesc() + ",不可申请开票");
        }
        // 收款状态
        //Integer amountCollectionStatus = afterSalesDto.getAmountCollectionStatus();
        //if(!AfterSalesCollectionStatusEnum.ALL.getCode().equals(amountCollectionStatus)){
        //    throw new ServiceException("收款状态为" + AfterSalesCollectionStatusEnum.getDesc(amountCollectionStatus) + ",不可申请开票");
        //}
        // 进行中开票申请
        InvoiceApplyDto invoiceApply = invoiceApplyMapper.getAftersaleInvoiceApplyByRelatedIdLast(afterSalesId);
        if (Objects.nonNull(invoiceApply) &&
                (!FinanceConstant.ONE.equals(invoiceApply.getValidStatus()) && !FinanceConstant.TWO.equals(invoiceApply.getValidStatus())) &&
                !FinanceConstant.TWO.equals(invoiceApply.getAdvanceValidStatus())) {
            throw new ServiceException("已存在进行中开票申请");
        }

        CheckInvoiceApplyResponseDto checkInvoiceApplyResponseDto = getAfterCheckInvoiceApplyResponseDto(subjectType, afterSalesDto, invoiceType);
        return checkInvoiceApplyResponseDto;
    }

    /**
     * 检查发票申请
     * @param saleOrderId
     */
    @Override
    public InvoiceCheckResultDto checkCheckRule(Integer saleOrderId) {
        InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
        invoiceCheckRequestDto.setCheckChainEnum(CheckChainEnum.INVOICE_APPLY_SALES);
        invoiceCheckRequestDto.setInvoiceProperty(ErpConstant.THREE);
        invoiceCheckRequestDto.setType(InvoiceApplyTypeEnum.SALE_ORDER.getCode());
        invoiceCheckRequestDto.setRelatedId(saleOrderId);
        invoiceCheckRequestDto.setInvoiceInfoType(ErpConstant.THREE);
        invoiceCheckRequestDto.setInvoiceInfoType(YNEnum.N.getCode());
        InvoiceCheckResultDto invoiceCheckResultDto = invoiceCheckApiService.applyCheck(invoiceCheckRequestDto);
        return invoiceCheckResultDto;
    }




    /**
     * 开票申请按钮展示
     * @param saleOrderId
     */
    @Override
    public void showButtonApplyInvoice(Integer saleOrderId){
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getBySaleOrderId(saleOrderId);
        // 生效状态
        Integer validStatus = saleorderInfoDto.getValidStatus();
        if (YNEnum.N.getCode().equals(validStatus)){
            throw new ServiceException("生效状态为未生效，不可申请开票");
        }
        // 单据状态
        Integer status = saleorderInfoDto.getStatus();

        if(!APPLICATE_STATUS.contains(status)){
            throw new ServiceException("单据状态为" + SaleOrderStatusEnum.getDesc(status) + ",不可申请开票");
        }

        // 开票状态
        Integer invoiceStatus = saleorderInfoDto.getInvoiceStatus();
        if (SaleOrderInvoiceStatusEnum.ALL.getCode().equals(invoiceStatus)){
            throw new ServiceException("开票状态为" + SaleOrderInvoiceStatusEnum.ALL.getDesc() + ",不可申请开票");
        }
    }


    /**
     * 获取客户资质信息
     * @param traderId
     */
    @Override
    public TraderFinanceDto getCustomerTraderFinance(Integer traderId) {
        List<TraderFinanceDto> traderFinanceDtoList = traderFinanceApiService.findByTraderIdAndTraderType(traderId, ErpConstant.ONE);
        if (CollUtil.isEmpty(traderFinanceDtoList)){
            return new TraderFinanceDto();
        }
        return traderFinanceDtoList.get(0);
    }


    private CheckInvoiceApplyResponseDto getCheckInvoiceApplyResponseDto(InvoiceApplyDto invoiceApplyDto) {
        TraderFinanceDto traderFinanceDto = getCustomerTraderFinance(invoiceApplyDto.getTraderId());
        Integer invoiceType = invoiceApplyDto.getInvoiceType();

        // 是否专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType);
        CheckInvoiceApplyResponseDto checkInvoiceApplyResponseDto = new CheckInvoiceApplyResponseDto();
        checkInvoiceApplyResponseDto.setTraderFinanceDto(traderFinanceDto);
        checkInvoiceApplyResponseDto.setSpecialInvoice(isSpecialInvoice);
        // 普票校验
        if (!isSpecialInvoice && StrUtil.isBlank(traderFinanceDto.getTaxNum())){
            TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderCustomerAptitudeInfoByTraderId(invoiceApplyDto.getTraderId());
            if (Objects.isNull(traderCustomerDto) || !ErpConstant.ONE.equals(traderCustomerDto.getAptitudeStatus())){
                log.info("该客户信息不全，且客户资质审核状态非审核通过，不能开票:{}",JSON.toJSONString(traderCustomerDto));
                checkInvoiceApplyResponseDto.setPassCredential(Boolean.FALSE);
                return checkInvoiceApplyResponseDto;
            }
            log.info("该客户信息不全，但客户资质审核状态通过，允许开票");
        }
        // 专票校验
        if (isSpecialInvoice &&
                (StrUtil.isBlank(traderFinanceDto.getTaxNum())
                        || StrUtil.isBlank(traderFinanceDto.getRegAddress())
                        || StrUtil.isBlank(traderFinanceDto.getRegTel())
                        || StrUtil.isBlank(traderFinanceDto.getBank())
                        || StrUtil.isBlank(traderFinanceDto.getBankAccount())
                        || StrUtil.isBlank(traderFinanceDto.getAverageTaxpayerUri()))){
            // 专票需要校验：税务登记号，注册地址，注册电话，开户银行，银行账号，一般纳税人资质
            log.info("该客户信息不全，不能开专票");
            checkInvoiceApplyResponseDto.setPassCredential(Boolean.FALSE);
            return checkInvoiceApplyResponseDto;
        }
        checkInvoiceApplyResponseDto.setPassCredential(Boolean.TRUE);
        return checkInvoiceApplyResponseDto;
    }

    private CheckInvoiceApplyResponseDto getAfterCheckInvoiceApplyResponseDto(Integer subjectType, AfterSalesDto afterSalesDto, Integer invoiceType) {
        OrderFinanceInfoDto orderFinanceInfoDto = null;
        if (FinanceConstant.ID_535.equals(subjectType)) {
            orderFinanceInfoDto = saleOrderApiService.getSaleorderFinanceInfo(afterSalesDto.getOrderId());
        } else if (FinanceConstant.ID_537.equals(subjectType)) {
            orderFinanceInfoDto = afterSalesApiService.getAftersaleFinanceInfo(afterSalesDto.getAfterSalesId());
        }
        if (Objects.isNull(orderFinanceInfoDto) || Objects.isNull(orderFinanceInfoDto.getTraderId())) {
            log.info("售后开票申请,未查询到售后订单关联的财务信息,afterSalesId:{}", afterSalesDto.getAfterSalesId());
            throw new ServiceException("未查询到售后订单关联的客户财务信息");
        }
        TraderFinanceDto traderFinanceDto = getCustomerTraderFinance(orderFinanceInfoDto.getTraderId());
        if (Objects.isNull(traderFinanceDto.getTraderId())) {
            traderFinanceDto.setTraderId(orderFinanceInfoDto.getTraderId());
        }
        // 是否专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType);
        CheckInvoiceApplyResponseDto checkInvoiceApplyResponseDto = new CheckInvoiceApplyResponseDto();
        checkInvoiceApplyResponseDto.setTraderFinanceDto(traderFinanceDto);
        checkInvoiceApplyResponseDto.setSpecialInvoice(isSpecialInvoice);
        // 判断发票类型
        if (!InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            // 增值税普通发票
            if (orderFinanceInfoDto.getTraderName().contains(FinanceConstant.COMPANY) || orderFinanceInfoDto.getTraderName().contains(FinanceConstant.ENTERPRISE)) {
                if (StrUtil.isBlank(orderFinanceInfoDto.getTaxNum())) {
                    checkInvoiceApplyResponseDto.setPassCredential(Boolean.FALSE);
                    return checkInvoiceApplyResponseDto;
                }
            }
        } else if (InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType)) {
            // 增值税专用发票
            // 注册地址、注册电话、开户银行、银行账号、税务登记号、一般纳税人资质
            if (StrUtil.isBlank(orderFinanceInfoDto.getRegAddress()) || StrUtil.isBlank(orderFinanceInfoDto.getRegTel())
                    || StrUtil.isBlank(orderFinanceInfoDto.getBank()) || StrUtil.isBlank(orderFinanceInfoDto.getBankAccount())
                    || StrUtil.isBlank(orderFinanceInfoDto.getTaxNum()) || StrUtil.isBlank(orderFinanceInfoDto.getAverageTaxpayerUri())) {
                checkInvoiceApplyResponseDto.setPassCredential(Boolean.FALSE);
                return checkInvoiceApplyResponseDto;
            }
        }
        checkInvoiceApplyResponseDto.setPassCredential(Boolean.TRUE);
        return checkInvoiceApplyResponseDto;
    }
}
