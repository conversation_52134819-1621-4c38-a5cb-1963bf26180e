
.dialog-fade-enter-active {
    transition: all 0.15s ease-out;
}
.dialog-fade-leave-active {
    transition: all 0.15s ease-in;
}
.dialog-fade-enter, .dialog-fade-leave-to {
    opacity: 0;
}

.dialog-move-enter-active {
    transition: all 0.15s ease-out;
}
.dialog-move-leave-active {
    transition: all 0.15s ease-in;
}
.dialog-move-enter, .dialog-move-leave-to {
    opacity: 0;
    transform: translate3d(0, -30px, 0)
}

.mui-custom-dialog-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2019;
    background-color: rgba(0,0,0,0.6);
    text-align: center;

    &::after {
        content: "";
        display: inline-block;
        height: 100%;
        width: 0;
        vertical-align: middle;
    }

    .nui-custom-dislog-center {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
    }
}


.mui-custom-dialog-wrapper {
    border-radius: 12px;
    background-color: #fff;
    font-weight: 400;
    line-height: 1.5;
    overflow: hidden;

    .mui-custom-dialog-content {
        padding: 20px;

        &::after {
            content: "";
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            z-index: -1;
            width: 100%;
            height: 62px;
        }

        .mui-custom-dialog-title {
            font-size: 14px;
            color: #000;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .mui-custom-dialog-msg {
            font-size: 14px;
            color: #000;
        }
    }

    .mui-custom-dialog-button-choice {
        display: flex;
        border: none;
        border-top: solid 1px #E1E5E8;

        .mui-cdb {
            flex: 1;
            min-width: 0;
            height: 50px;
            color: #000;
            line-height: 50px;
            background: none;
            border-right: solid 1px #E1E5E8;
            cursor: pointer;
            font-size: 14px;

            &:last-child {
                border-right: none;
            }

            &.disabled {
                color: #CCC !important;
                pointer-events: none;
            }

            &.confirm {
                color: #09f;
            }

            &.delete {
                color: #E64545;
            }

            &.cancel {
                color: #333;
            }
        }
    }
}


.msg-fork {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    cursor: pointer;
    &:hover {
        color: #333333;
        .icon-delete {
            color: #333333;
        }
    }
    .icon-delete {
        font-size: 24px;
        color: #CCCCCC;
        &:hover {
            color: #333333;
        }
    }
}


// slide-dialog输入框样式
.slide-dialog-input-wrap {
    height: 53px;
    padding: 10px;

    /deep/ .vd-ui-input-wrap {
        .vd-ui-input {
            padding-top: 0;
            padding-bottom: 0;
        }
    }
}
