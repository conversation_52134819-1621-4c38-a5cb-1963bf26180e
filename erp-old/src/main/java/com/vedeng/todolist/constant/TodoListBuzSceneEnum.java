package com.vedeng.todolist.constant;

import java.util.Arrays;

/**
 * 待办事项业务场景
 * @Author: daniel
 * @Date: 2020/12/10 16 06
 * @Description:
 */
public enum TodoListBuzSceneEnum {

    /**
     * 客户资质质量部审核待办事项
     */
    ZLB_CHECK_TRADER_CUSTOMER_CERTIFICATE(1,""),

    /**
     * 销售订单质量部审核待办事项
     */
    ZLB_CHECK_SALE_ORDER(2,""),

    /**
     * 采购订单质量部审核待办事项
     */
    ZLB_CHECK_BUY_ORDER(3,""),

    /**
     * 风控之客户资质待办事项
     */
    RISK_CHECK_TRADER_CUSTOMER_CERTIFICATE(4,""),

    /**
     * 风控之客户数据待办事项
     */
    RISK_CHECK_TRADER_CUSTOMER_DATA(5,""),

    /**
     * 风控之供应商数据待办事项
     */
    RISK_CHECK_TRADER_SUPPLY_DATA(6,""),

    /**
     * 风控之SKU数据待办事项
     */
    RISK_CHECK_SKU_DATA(7,""),

    /**
     * 维护信息-SPU
     */
    MAINTAIN_DATA_SPU(8,""),

    /**
     * 维护信息-SKU
     */
    MAINTAIN_DATA_SKU(9,""),

    /**
     * 维护信息-预计可发货时间
     */
    MAINTAIN_DATA_DELIVERY_TIME(10,""),

    /**
     * 维护信息-商品核价
     */
    MAINTAIN_DATA_PRICE(11,""),

    /**
     * 维护信息-贝登售后政策
     */
    MAINTAIN_DATA_AFTER_SALE_POLICY(12,""),

    /**
     * 维护信息-供应商售后政策
     */
    MAINTAIN_DATA_SUPPLY_AFTER_SALE_POLICY(13,""),

    /**
     * 维护信息-报备信息
     */
    MAINTAIN_DATA_REPORT_INFO(14,""),

    /**
     * 维护信息-签约模式
     */
    MAINTAIN_DATA_SIGN_CONTRACT_MODE(15,""),

    /**
     * 维护信息-运营信息
     */
    MAINTAIN_DATA_OPERATION_INFO(16,""),
    ;

    /**
     * 待办事项业务场景标识
     */
    private Integer buzSceneId;

    /**
     * 待办事项业务跳转链接
     */
    private String buzRedirect;


    TodoListBuzSceneEnum(Integer buzSceneId, String buzRedirect) {
        this.buzSceneId = buzSceneId;
        this.buzRedirect = buzRedirect;
    }

    public Integer getBuzSceneId() {
        return buzSceneId;
    }

    public void setBuzSceneId(Integer buzSceneId) {
        this.buzSceneId = buzSceneId;
    }

    public String getBuzRedirect() {
        return buzRedirect;
    }

    public void setBuzRedirect(String buzRedirect) {
        this.buzRedirect = buzRedirect;
    }

    public static TodoListBuzSceneEnum getTodoListBuzSceneEnumById(Integer buzSceneId) {
        return Arrays.stream(values()).filter(item -> item.getBuzSceneId().equals(buzSceneId)).findFirst().orElse(null);
    }
}
