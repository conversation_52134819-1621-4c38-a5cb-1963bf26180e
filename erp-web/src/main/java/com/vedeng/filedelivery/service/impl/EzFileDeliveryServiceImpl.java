package com.vedeng.filedelivery.service.impl;

import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.util.StringUtil;
import com.vedeng.filedelivery.service.EzFileDeliveryService;
import com.vedeng.oa.dao.FileDeliveryAddressExtMapper;
import com.vedeng.oa.model.FileDeliveryAddress;
import com.vedeng.oa.service.filedelivery.api.FileDeliveryAddressApiService;
import com.vedeng.oa.service.filedelivery.vo.FileDeliveryAddressVO;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.impl.RegionServiceImpl;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import top.ezadmin.common.constants.SessionConstants;
import top.ezadmin.common.utils.NumberUtils;
import top.ezadmin.common.utils.StringUtils;
import top.ezadmin.common.utils.Utils;
import top.ezadmin.web.EzResult;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service("ezFileDeliveryService")
public class EzFileDeliveryServiceImpl implements EzFileDeliveryService {

    @Autowired
    @Qualifier("userService")
    private UserService userService;
    
    @Autowired
	private RegionServiceImpl regionServiceImpl;
    
    @Autowired
	private FileDeliveryAddressExtMapper fileDeliveryAddressExtMapper;
    
    @Autowired
    private FileDeliveryAddressApiService fileDeliveryAddressApiService;

    @Override
    public String searchCondition(Map<String, Object> request) {
        //System.out.println(JSON.toJSONString(request));
        String userId= Utils.trimNull(request.get(SessionConstants.EZ_SESSION_USER_ID_KEY));
        HttpServletRequest request1= (HttpServletRequest) request.get("HttpServletRequest");
        User session_user= (User) request1.getSession().getAttribute(ErpConst.CURR_USER);
//        List<Integer> positionType = new ArrayList<>();
//        positionType.add(SysOptionConstant.ID_310);// 销售
//        positionType.add(SysOptionConstant.ID_311);// 采购
//        positionType.add(SysOptionConstant.ID_312);// 售后
//        positionType.add(SysOptionConstant.ID_314);// 财务
//        positionType.add(SysOptionConstant.ID_313);// 物流
//        List<User> netAllUserList = new ArrayList<>();
//        Integer type=0;
//        if(session_user!=null && CollectionUtils.isNotEmpty(session_user.getPositions()) ) {
//            type = session_user.getPositions().get(0).getType();
//            if (type.equals(310) || type.equals(311)) {
//                netAllUserList = userService.getMyUserList(session_user, positionType, false);
//            } else {
//                netAllUserList = userService.getAllUser(session_user);
//            }
//        }
//        List<Integer> creatorIds = new ArrayList<Integer>();
//        // 如果申请人条件是全部的话
//        if (netAllUserList != null  ) {
//            for (User c : netAllUserList) {
//                creatorIds.add(c.getUserId());
//            }
//            creatorIds.add(session_user.getUserId());
//        }
        StringBuilder stringBuilder=new StringBuilder();
//        if(session_user!=null && CollectionUtils.isNotEmpty(session_user.getPositions()) ) {
//            type = session_user.getPositions().get(0).getType();
//            if (type.equals(310) || type.equals(311)) {
//                stringBuilder.append(" and APPLY_USER_ID in("+StringUtils.join(creatorIds,",")+") ");
//            }else{
//                //TODO
//            }
//        }

        switch (Utils.trimEmptyDefault(request.get("TAB_ID"),"all")){
            case "all": break;
            case "shenhezhong":
                stringBuilder.append(" and VERIFY_STATUS=1 ");
                break;
            case "yishenhe":
                stringBuilder.append(" and VERIFY_STATUS=2 ");
                break;
            case "yibohui":
                stringBuilder.append(" and VERIFY_STATUS=3 ");
                break;
            case "weijisong":
                stringBuilder.append(" and VERIFY_STATUS=2 and  DELIVERY_STATUS=0 and IS_CLOSED=0 ");
                break;
            case "yijisong":
                stringBuilder.append(" and  DELIVERY_STATUS=1 ");
                break;
            case "yiguanbi":
                stringBuilder.append(" and  IS_CLOSED=1 ");
            break;
        }
        return stringBuilder.toString();
    }



	@Override
	public EzResult saveAddress(Map<String, Object> request) {
        Integer userId= Utils.toIntWithNull(request.get(SessionConstants.EZ_SESSION_USER_ID_KEY)) ;
        FileDeliveryAddressVO vo=new FileDeliveryAddressVO();
        vo.setFileDeliveryId(Utils.toIntWithNull(request.get("FILE_DELIVERY_ID")));
        vo.setTraderType(Utils.toIntWithNull(request.get("TRADER_TYPE")));
        vo.setContactType(Utils.toIntWithNull(request.get("CONTACT_TYPE")));
        if(vo.getContactType()==null){
            vo.setContactType(2);
        }
        if(1==vo.getTraderType()){
            vo.setTraderId(Utils.toIntWithNull(request.get("TRADER_ID1")));
        }else  if(2==vo.getTraderType()){
            vo.setTraderId(Utils.toIntWithNull(request.get("TRADER_ID2")));
        }
        else   if(3==vo.getTraderType()){
            vo.setTraderName(Utils.trimNull(request.get("TRADER_ID3")));
        }
        //与初始化SQL保持一致
        if(1==vo.getTraderType()&&1==vo.getContactType()){
            vo.setTraderContactId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ID11")));
            vo.setTraderContactMobile(Utils.trimNull(request.get("TRADER_CONTACT_MOBILE11")));
            vo.setTraderContactAddressId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ADDRESS_ID11")));
        }else
        if(2==vo.getTraderType()&&1==vo.getContactType()){
            vo.setTraderContactId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ID21")));
            vo.setTraderContactMobile(Utils.trimNull(request.get("TRADER_CONTACT_MOBILE21")));
            vo.setTraderContactAddressId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ADDRESS_ID21")));
        }else
        if(1==vo.getTraderType()&&2==vo.getContactType()){
            vo.setTraderContactName(Utils.trimNull(request.get("TRADER_CONTACT_ID12")));
            vo.setTraderContactMobile(Utils.trimNull(request.get("TRADER_CONTACT_MOBILE12")));
            vo.setAreaId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ADDRESS_ID12")));
            vo.setTraderContactAddressInfo(Utils.trimNull(request.get("CONTACT_ADDRESS_DETAIL12")));
        }else
        if(2==vo.getTraderType()&&2==vo.getContactType()){
            vo.setTraderContactName(Utils.trimNull(request.get("TRADER_CONTACT_ID22")));
            vo.setTraderContactMobile(Utils.trimNull(request.get("TRADER_CONTACT_MOBILE22")));
            vo.setAreaId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ADDRESS_ID22")));
            vo.setTraderContactAddressInfo(Utils.trimNull(request.get("CONTACT_ADDRESS_DETAIL22")));

        }else
        if(3==vo.getTraderType()&&2==vo.getContactType()){
            vo.setTraderContactName(Utils.trimNull(request.get("TRADER_CONTACT_ID32")));
            vo.setTraderContactMobile(Utils.trimNull(request.get("TRADER_CONTACT_MOBILE32")));
            vo.setAreaId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ADDRESS_ID32")));
            vo.setTraderContactAddressInfo(Utils.trimNull(request.get("CONTACT_ADDRESS_DETAIL32")));
        }






//        vo.setTraderName(Utils.trimNull(request.get("TRADER_NAME")));
//        vo.setTraderId(Utils.toIntWithNull(request.get("TRADER_ID")));
//
//        vo.setTraderContactName(Utils.trimNull(request.get("TRADER_CONTACT_NAME")));
//
//        vo.setTraderContactAddressId(Utils.toIntWithNull(request.get("TRADER_CONTACT_ADDRESS_ID")));
//        vo.setTraderContactMobile(Utils.trimNull(request.get("CONTACT_MOBILE")));
//        vo.setTraderContactAddressInfo(Utils.trimNull(request.get("DELIVERY_ADDRESS")));
//        vo.setAreaId(Utils.toIntWithNull(request.get("REGION_ID")));

        vo.setId(Utils.toIntWithNull(request.get("ID")));
      //  fileDeliveryAddressList.add(vo);
        FileDeliveryAddressVO  result=  fileDeliveryAddressApiService.saveFileDeliveryAddress(vo,userId);
        if(vo.getTraderType()!=null&&vo.getTraderType()!=3&&vo.getContactType()!=null&&vo.getContactType()==1){
            result.setTraderContactMobile(StringUtil.hideMiddleFourDigits(vo.getTraderContactMobile()));
        }else{
           // vo.setTraderContactMobile(vo.getTraderContactMobile());
        }
		return EzResult.instance().data(result);
	}

    @Override
    public Object hasPermission(String url) {
        Boolean has= userService.judgeHasPermission(url);
        if(!has){
            return 0;
        }
        return 1;
    }
}
