Vue.component('vd-ui-address', {
    template: `
        <div class="vd-ui-address">
            <div class="slide-dialog-input-wrap">
                <ui-input 
                    class="search-input"
                    :border="true"
                    v-model="inputValue"
                    :maxlength="maxlength"
                    @input.native="handleInput"
                    @compositionend.native="commentPress"
                ></ui-input>
            </div>

            <div class="vd-ui-address-panel">

                <div class="address-search-wrap" v-if="filtering">
                    <div v-if="filterResult.length" class="filter-list">
                        <div 
                            class="filter-item" 
                            v-for="(item, index) in filterResult" :key="index"
                            @click.stop="handleSuggestionClick(item)"
                            :class="{'active': isCheck(item)}"
                        >
                            <div class="filter-selected">
                                <i class="vd-ui_icon icon-radio3" v-if="isCheck(item)"></i>
                                <i class="vd-ui_icon icon-radio1" v-else></i>
                            </div>
                            <div v-html="suggestShow(item)"></div>
                        </div>
                    </div>
                    
                    <div class="erp-load-empty" v-else>
                        <i class="vd-ui_icon icon-info1"></i>
                        <p>无匹配数据</p>
                    </div>
                </div>

                <div class="address-choose-wrap">
                    <div class="vd-ui-address-nav">
                        <div 
                            class="nav-item text-line-1 pro" 
                            :class="{'active': showTab == 1}"
                            @click="changeTab(1)"
                        >{{ selectedProData.label || '省' }}</div>
                        <div 
                            class="nav-item text-line-1 city" 
                            :class="{'active': showTab == 2}"
                            @click="changeTab(2)"
                        >{{ selectedCityData.label || '市' }}</div>
                        <div 
                            class="nav-item text-line-1 area" 
                            :class="{'active': showTab == 3}"
                            @click="changeTab(3)"
                        >{{ selectedAreaData.label || '区' }}</div>
                    </div>

                    <div class="address-choose-panel" >
                        <div class="ap-list" v-show="showTab == 1">
                            <div 
                                class="ap-item" 
                                :class="{'active': item.value == selectedProData.value}"
                                v-for="(item, index) in addressData" :key="index"
                                @click="chooseProvince(item)"
                            >
                                <span class="selected">
                                    <i v-if="item.value == selectedProData.value" class="vd-ui_icon icon-radio3"></i>
                                    <i v-else class="vd-ui_icon icon-radio1"></i>
                                </span>
                                <div class="ap-content">{{ item.label }}</div>
                            </div>
                        </div>
                        <div class="ap-list" v-show="showTab == 2">
                            <div 
                                class="ap-item" 
                                :class="{'active': item.value == selectedCityData.value}"
                                v-for="(item, index) in cityList" :key="index"
                                @click="chooseCity(item)"
                            >
                                <span class="selected">
                                    <i v-if="item.value == selectedCityData.value" class="vd-ui_icon icon-radio3"></i>
                                    <i v-else class="vd-ui_icon icon-radio1"></i>
                                </span>
                                <div class="ap-content">{{ item.label }}</div>
                            </div>
                        </div>
                        <div class="ap-list" v-show="showTab == 3">
                            <div 
                                class="ap-item" 
                                :class="{'active': item.value == selectedAreaData.value}"
                                v-for="(item, index) in areaList" :key="index"
                                @click="chooseArea(item)"
                            >
                                <span class="selected">
                                    <i v-if="item.value == selectedAreaData.value" class="vd-ui_icon icon-radio3"></i>
                                    <i v-else class="vd-ui_icon icon-radio1"></i>
                                </span>
                                <div class="ap-content">{{ item.label }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `,
    props: {
        value: {}, // 传value值
        data: {
            type: Array,
        },
        // true:需选到最后一层数据  false:可选到任意层级
        eachLevel: {
            type: Boolean,
            default: true
        },
        maxlength: {
            type: String,
            default: ''
        },
    },
    data() {
        return {
            showTab: 1, // 展示选择面板 1:省 2:市 3:区
            addressData: [],
            cityList: [],
            areaList: [],

            // 已选省市区
            selectedProData: {},
            selectedCityData: {},
            selectedAreaData: {},

            // 搜索
            inputValue: '',
            filtering: false,
            filterResult: [],
        };
    },
    watch: {
        data: {
            handler (newV) {
                this.addressData = newV || [];
                this.init();
            },
            deep: true,
            immediate: true,
        },
        value: {
            handler () {
                this.init();
            },
            deep: true,
            immediate: true,
        },
    },
    computed: {
        // 已选省
        hasPro () {
            return Object.keys(this.selectedProData).length;
        },
        // 已选市
        hasCity () {
            return Object.keys(this.selectedCityData).length;
        },
        // 已选区
        hasArea () {
            return Object.keys(this.selectedAreaData).length;
        },
        // 已选字符串 格式=>"江苏省 / 南京市 / 秦淮区"
        selectStr () {
            let str = '';
            if (this.hasPro) {
                str = this.selectedProData.label;
            }
            if (this.hasCity) {
                str += str? ' / ' + this.selectedCityData.label : this.selectedCityData.label;
            }
            if (this.hasArea) {
                str += str? ' / ' + this.selectedAreaData.label : this.selectedAreaData.label;
            }
            return str || '';
        }
    },
    mounted() {
    },
    methods: {
        clearData() {
            this.selectedProData = {};
            this.selectedCityData = {};
            this.selectedAreaData = {};
            this.cityList = [];
            this.areaList = [];
            this.showTab = 1;
        },

        /* 选择面板 Start */
        // 切换Tab
        changeTab(i) {
            // 点一个级  必须上一级有值
            if ((i == 3 && !this.hasCity) || (i == 2 && !this.hasPro)) {
                return;
            }
            this.showTab = i;
        },
        // 选择省
        setPro (item) {
            this.selectedProData = item || {};
            this.selectedCityData = {};
            this.selectedAreaData = {};

            this.showTab = 2;
            this.cityList = this.selectedProData.children || [];
            this.areaList = [];
        },
        chooseProvince (item) {
            this.setPro(item);

            if (!this.eachLevel) { // 需要选到最后一个层级
                this.handlerEmit();
            }
        },
        // 选择市
        setCity (item) {
            this.selectedCityData = item;
            this.selectedAreaData = {};

            this.showTab = 3;
            this.areaList = item.children || [];
        },
        chooseCity (item) {
            this.setCity(item);

            if (!this.eachLevel) { // 需要选到最后一个层级
                this.handlerEmit();
            }
        },
        // 选择区
        chooseArea (item) {
            this.selectedAreaData = item;
            this.handlerEmit();
        },
        /* 选择面板 End */


        /* 搜索面板 End */
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                let val = e.target.value.trim();
                this.filterSearch(val);
            }
        },
        // 中文结束
        commentPress(e) {
            let val = e.target.value.trim();
            this.filterSearch(val);
        },
        filterSearch (val) {
            if (!val) {
                this.filterResult = [];
                this.filtering = false;
                return;
            }
            if (val == this.selectStr) return;

            this.filterResult = this.getSuggestions(val);
            this.filtering = true;
        },
        getSuggestionWord (val) {
            let splitArr = val.split('/');
            let words = [];
            if (splitArr.length > 1) {
                splitArr.forEach(item=> {
                    if (item.trim()) {
                        words.push(item.trim());
                    }
                })
            } else {
                words.push(splitArr[0].trim());
            }
            return words;
        },
        getSuggestions (val) {
            let words = this.getSuggestionWord(val);
            let searchRes = []; // 匹配结果

            if (words.length > 1) {
                this.addressData.forEach(L1 => {
                    // 匹配到第一级， 注: words第一级 不一定是 list第一级别
                    let level = words.length;
                    if (L1.label.includes(words[0])) {
                        // 继续匹配下一级
                        level--;
                        if (level && L1.children && L1.children.length) {
                            L1.children.forEach(L2 => {
                                if (L2.label.includes(words[words.length - level])) {
                                    level--;

                                    if (L2.children && L2.children.length) {
                                        if (level) {
                                            L2.children.forEach(L3=> {
                                                if (L3.label.includes(words[words.length - level])) {
                                                    searchRes.push({
                                                        l1: L1,
                                                        l2: L2,
                                                        l3: L3
                                                    })
                                                }
                                            })
                                        } else {
                                            if(this.each) {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                })
                                            }

                                            L2.children.forEach(L3=> {
                                                searchRes.push({
                                                    l1: L1,
                                                    l2: L2,
                                                    l3: L3
                                                })
                                            })
                                        }
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                        })
                                    }
                                }
                            })
                        }
                    } else {
                        // 一级没匹配到, 继续从第二级比较
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(words[0])) {
                                level--;
                                if (level && L2.children && L2.children.length) {
                                    L2.children.forEach(L3 => {
                                        if (L3.label.includes(words[words.length - level])) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        }
                                    })
                                }
                            }
                        })
                    }
                })
            } else if (words.length == 1) {
                let word = words[0].trim();

                this.addressData.forEach(L1 => {
                    // 一级匹配, 则匹配结果包含所有子集
                    if (L1.label.includes(word)) {
                        if(this.each) {
                            searchRes.push({
                                l1: L1
                            })
                        }
                        if (L1.children) {
                            if (L1.children && L1.children.length) {
                                L1.children.forEach(L2 => {
                                    if (L2.children && L2.children.length) {
                                        if(this.each) {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2
                                            })
                                        }

                                        L2.children.forEach(L3 => {
                                            searchRes.push({
                                                l1: L1,
                                                l2: L2,
                                                l3: L3
                                            })
                                        })
                                    } else {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                })
                            } else {
                                searchRes.push({
                                    l1: L1,
                                })
                            }
                        }
                    }
                    // 一级不匹配, 继续轮循下面二级
                    else {
                        L1.children && L1.children.length && L1.children.forEach(L2 => {
                            if (L2.label.includes(word)) {
                                if (L2.children && L2.children.length) {
                                    if(this.each) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2
                                        })
                                    }
                                    L2.children.forEach(L3 => {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    })
                                } else {
                                    searchRes.push({
                                        l1: L1,
                                        l2: L2
                                    })
                                }
                            } 
                            // 二级不匹配, 继续轮循下面三级
                            else {
                                L2.children && L2.children.length && L2.children.forEach(L3 => {
                                    if (L3.label.includes(word)) {
                                        searchRes.push({
                                            l1: L1,
                                            l2: L2,
                                            l3: L3
                                        })
                                    }
                                })
                            }
                        })
                    }
                })
            }

            if (searchRes.length > 100) {
                searchRes = searchRes.slice(0, 100);
            }

            return searchRes;
        },
        suggestShow(item) {
            let str = '';
            if (item.l1) {
                str += item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }

            const keywords = this.getSuggestionWord(this.inputValue);
            keywords.sort((a, b) => b.length - a.length);
            for (const keyword of keywords) {
                const regExp = new RegExp(keyword, 'g');
                str = str.replace(regExp, `<font color='#FF6600'">${keyword}</font>`);
            }
            return str;
        },
        // 搜索面板选中
        handleSuggestionClick (item) {
            if (item.l1 && Object.keys(item.l1)) {
                this.setPro(item.l1);
            }
            if (item.l2 && Object.keys(item.l2)) {
                this.setCity(item.l2);
            }
            if (item.l3 && Object.keys(item.l3)) {
                this.chooseArea(item.l3);
            }
        },
        isCheck (item) {
            if (this.getStr(item) == this.selectStr) {
                return true;
            } else {
                return false;
            }
        },
        getStr (item) {
            let str = '';
            if (item.l1) {
                str = item.l1.label;
            }
            if (item.l2) {
                str += ` / ${item.l2.label}`;
            }
            if (item.l3) {
                str += ` / ${item.l3.label}`;
            }
            return str;
        },
        clearFilter() {
            this.filtering = false;
            this.filterResult = [];
            this.inputValue = '';
        },
        /* 搜索面板 End */



        handlerEmit () {
            let proValue = this.selectedProData.value || '';
            let cityValue = this.selectedCityData.value || '';
            let areaValue = this.selectedAreaData.value || '';

            let values = [proValue, cityValue, areaValue];
            this.$emit('input', values);

            this.$emit('change', [
                this.selectedProData,
                this.selectedCityData,
                this.selectedAreaData
            ])
        },
        // 初始化
        init() {
            if (!this.value.length && !this.addressData.length) {
                return;
            }

            try {
                let level = this.value.length;
                this.addressData.forEach(levelPro => {
                    if (levelPro.value == this.value[0]) {
                        this.selectedProData = levelPro;
                        this.cityList = levelPro.children || [];
                        this.areaList = [];
                        level--;

                        // 市级
                        if (level) {
                            levelPro.children.forEach(levelCity => {
                                if (levelCity.value == this.value[1]) {
                                    this.selectedCityData = levelCity;
                                    this.areaList = levelCity.children || [];
                                    this.showTab = 2;
                                    level--;
    
                                    // 区级
                                    if (level) {
                                        levelCity.children.forEach(levelArea => {
                                            if (levelArea.value == this.value[2]) {
                                                this.selectedAreaData = levelArea;
                                                this.showTab = 3;
                                            }
                                        })
                                    }
                                }
                            })
                        } 
                    }
                });
            } catch (err) {
                console.log('errrrr:', err);
            }
        },
    }
})