package com.vedeng.crm.visitrecord.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.crm.follow.service.FollowUpRecordService;
import com.vedeng.crm.visitrecord.domain.dto.*;
import com.vedeng.crm.visitrecord.domain.entity.CrmVisitRecordEntity;
import com.vedeng.crm.visitrecord.domain.vo.VisitBizCheckVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordLogVo;
import com.vedeng.crm.visitrecord.domain.vo.VisitUserDetailForPageVo;
import com.vedeng.crm.visitrecord.enums.VisitOperationTypeEnum;
import com.vedeng.crm.visitrecord.facade.CrmVisitRecordFacade;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordLogService;
import com.vedeng.crm.visitrecord.service.CrmVisitRecordService;
import com.vedeng.crm.visitrecord.service.VisitRecordCardService;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 拜访记录接口
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/28
 */
@ExceptionController
@RestController
@RequestMapping("/crm/visitrecord/profile")
@Slf4j
public class CrmVisitRecordApi {

    @Autowired
    private CrmVisitRecordService visitRecordService;

    @Autowired
    private CrmVisitRecordFacade crmVisitRecordFacade;

    @Autowired
    private CrmVisitRecordLogService crmVisitRecordLogService;

    @Autowired
    private FollowUpRecordService followUpRecordService;

    @Value("${mapConfig:fa25f2e4db0a8197a68ae58fab6c2a06,29d8231c342d7a1ad42ddd36d1b3c969}")//生产是f0331237baa46d985190a937e2cdb4aa,61854e7e3b0705cb5089d9bb47957ee9
    private String mapConfig;
    /**
     * 拜访计划-地图-key
     */
    @RequestMapping(value = "/config", method = RequestMethod.GET)
    public R<String> config() {
        R<String> result = R.success();
        result.setData(mapConfig);
        return result;
    }
    /**
     * 拜访记录列表
     */
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<PageInfo<VisitRecordVo>> page(@RequestBody PageParam<VisitRecordQueryDto> pageParam) {
        return R.success(visitRecordService.visitRecordPage(pageParam));
    }

    /**
     * 拜访计划列表-创建人/拜访人/同行人
     * @return
     */
    @RequestMapping(value = "/pageDetail", method = RequestMethod.POST)
    public R<VisitUserDetailForPageVo> pageDetail() {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(visitRecordService.pageDetail(currentUser));
    }

    /**
     * 拜访记录详情
     */
    @RequestMapping(value = "/detail", method = RequestMethod.GET)
    public R<VisitRecordVo> detail(@RequestParam Integer id) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        return R.success(visitRecordService.detail(id,currentUser));
    }

    /**
     * 保存或更新拜访计划
     */
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public R<VisitRecordVo> save(@RequestBody VisitRecordInputDto inputDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        VisitRecordVo visitRecordVo = visitRecordService.saveOrUpdate(currentUser,inputDto);
        return R.success(visitRecordVo);
    }

    /**
     * 更新拜访记录
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<Void> update(@RequestBody VisitEditInputDto editDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        visitRecordService.updateSelective(currentUser, editDto);
        return R.success();
    }

    /**
     * 关闭拜访记录
     */
    @RequestMapping(value = "/close", method = RequestMethod.POST)
    public R<Void> closeVisitRecord(@RequestBody VisitCloseDto closeDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        visitRecordService.closeVisitRecord(currentUser, closeDto);
        return R.success();
    }

    /**
     * 校验业务编号
     */
    @RequestMapping(value = "/checkBizNo", method = RequestMethod.POST)
    public R<VisitBizCheckVo> checkBizNo(@RequestBody VisitBizCheckDto checkDto) {
        return R.success(crmVisitRecordFacade.checkBizNo(checkDto));
    }

    /**
     * 检查手机号是否注册贝登商城
     */
    @RequestMapping(value = "/checkMobileExists", method = RequestMethod.POST)
    public R<Boolean> checkMobileExists(@RequestParam String mobile) {
        return R.success(visitRecordService.checkMobileExists(mobile));
    }

    /**
     * 查询操作日志
     */
    @RequestMapping(value = "/logs", method = RequestMethod.POST)
    public R<PageInfo<OperationLogDto>> logs(@RequestBody PageParam<Integer> pageParam) {
        return R.success(crmVisitRecordLogService.selectPageByVisitRecordId(pageParam));
    }

    /**
     * 添加沟通记录
     * @param visitCommunicateRecordDto
     * @return
     */
    @RequestMapping(value = "/addCommunicateRecord", method = RequestMethod.POST)
    public R<?> addCommunicateRecord( @RequestBody VisitCommunicateRecordDto visitCommunicateRecordDto) {
        CurrentUser currentUser = CurrentUser.getCurrentUser();

        Integer recordId = visitCommunicateRecordDto.getRecordId();//取拜访计划的ID
        CrmVisitRecordEntity entity = visitRecordService.selectByPrimaryKey(recordId);
        if(entity == null){
            throw new ServiceException("请确认拜访计划");
        }
        CommunicateRecordDto communicateRecordDto = new CommunicateRecordDto();
        communicateRecordDto.setCommunicateType(5503);//拜访计划-沟通记录中映射的业务类型
        communicateRecordDto.setRelatedId(visitCommunicateRecordDto.getRecordId());//拜访计划的ID
        communicateRecordDto.setContact(entity.getRecordContactName());
        communicateRecordDto.setContactMob(entity.getRecordContactMobile());
        communicateRecordDto.setTelephone(entity.getRecordContactTele());
        //填充其他字段的值
        communicateRecordDto.setContentSuffix(visitCommunicateRecordDto.getCommunicateContent());
        followUpRecordService.add(communicateRecordDto);
        crmVisitRecordLogService.addOperationLog(visitCommunicateRecordDto.getRecordId(), VisitOperationTypeEnum.SUBMIT_COMMUNICATE, "补充了与客户的沟通记录", currentUser);
        return R.success();
    }



}
