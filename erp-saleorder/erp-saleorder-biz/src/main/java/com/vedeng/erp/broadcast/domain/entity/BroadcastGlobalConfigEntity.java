package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 全局播报配置表
 */
@Getter
@Setter
public class BroadcastGlobalConfigEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 过滤销售，逗号分隔
     */
    private String excludeSaleIds;

    /**
     * AED商品ID列表，逗号分隔
     */
    private String aedSkuIds;

    /**
     * 过滤客户ID，逗号分隔
     */
    private String excludeTraderIds;

    /**
     * 个人榜单TopN
     */
    private Integer topnUser;

    /**
     * 团队榜单TopN
     */
    private Integer topnDept;

    /**
     * 日常到款播报标题
     */
    private String broadcastTitleDay;

    /**
     * 周度到款播报标题
     */
    private String broadcastTitleWeek;

    /**
     * 月度到款播报标题
     */
    private String broadcastTitleMonth;

    /**
     * AED到款播报标题
     */
    private String broadcastTitleAed;

    /**
     * 自有到款播报标题
     */
    private String broadcastTitleZy;

    /**
     * 自定义到款播报标题
     */
    private String broadcastTitleCustom;

    /**
     * 统计维度1.结款金额，2.出库数量 3.出库金额
     */
    private Integer statType;

    /**
     * 统计时间范围1.本日 2.本周 3.本月
     */
    private Integer statDateRange;

    /**
     * 播报对象1.个人 2.小组 3 部门
     */
    private String statTarget;

    /**
     * 统计商品ID列表，逗号分隔
     */
    private String statSkuIds;

    /**
     * 统计品牌ID列表，逗号分隔
     */
    private String statBrandIds;

    /**
     * 统计分类ID列表，逗号分隔
     */
    private String statCategoryIds;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
