<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="售后详情-换货" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/orderstream/aftersales/view_afterSales.js?rnd=${resourceVersionKey}'></script>
<script src="<%= basePath %>static/js/orderstream/jquery.js"></script>
<script src="<%= basePath %>static/js/orderstream/afterSales_index.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="<%= basePath %>static/css/orderstream/style.css">
<div class="main-container">
    <div class="t-line-wrap J-line-wrap"
         data-json='[
     {"label":"确认","status":${topStatusList.get(0)}},
     {"label":"入库","status":${topStatusList.get(1)}},
     {"label":"出库","status":${topStatusList.get(2)}},
     {"label":"收款","status":${topStatusList.get(3)}},
     {"label":"开票","status":${topStatusList.get(4)}},
     <c:choose>
        <c:when test="${afterSalesVo.atferSalesStatus eq 3}">
            {"label":"关闭","status":${topStatusList.get(5)}}
        </c:when>
        <c:otherwise>
            {"label":"完结","status":${topStatusList.get(5)}}
        </c:otherwise>
     </c:choose>]'>
    </div>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                基本信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">订单号</td>
                <td>${afterSalesVo.afterSalesNo}</td>
                <td class="wid20">售后类型</td>
                <td>
                    销售换货
                </td>
            </tr>
            <tr>
                <td class="wid20">订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 3}">已关闭</c:if>
                </td>
                <td>审核状态</td>
                <td>
                    <c:if test="${afterSalesVo.status eq 0}">待审核</c:if>
                    <c:if test="${afterSalesVo.status eq 1}">审核中</c:if>
                    <c:if test="${afterSalesVo.status eq 2}">审核通过</c:if>
                    <c:if test="${afterSalesVo.status eq 3}">审核不通过</c:if>
                </td>
            </tr>
            <tr>
                <td>生效状态</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 0}">未生效</c:if>
                    <c:if test="${afterSalesVo.validStatus eq 1}">已生效</c:if>
                </td>
                <td>售后处理人</td>
                <td>
                    <c:forEach var="user" items="${afterSalesVo.userList}">
                        <c:if test="${user.userId eq afterSalesVo.serviceUserId}">${user.username}</c:if>
                    </c:forEach>
                </td>
            </tr>
            <tr>
                <td>申请人</td>
                <td>
                    <c:forEach var="user" items="${afterSalesVo.userList}">
                        <c:if test="${user.userId eq afterSalesVo.creator}">${user.username}</c:if>
                    </c:forEach>
                </td>
                <td>申请时间</td>
                <td><date:date value ="${afterSalesVo.addTime}"/></td>
            </tr>
            <tr>
                <td>生效时间</td>
                <td><date:date value ="${afterSalesVo.validTime}"/></td>
                <td>完结时间</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                        <date:date value ="${afterSalesVo.modTime}"/>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>完结/关闭原因</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                        ${afterSalesVo.afterSalesStatusResonName}
                    </c:if>
                </td>
                <td>完结/关闭人</td>
                <td>
                    <c:if test="${afterSalesVo.atferSalesStatus eq 2 || afterSalesVo.atferSalesStatus eq 3}">
                        <c:forEach var="user" items="${afterSalesVo.userList}">
                            <c:if test="${user.userId eq afterSalesVo.afterSalesStatusUser}">${user.username}</c:if>
                        </c:forEach>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>完结/关闭备注</td>
                <td rowspan="3">${afterSalesVo.afterSalesStatusComments}</td>
            </tr>
            </tbody>
        </table>
    </div>

    <input type="hidden" id="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">售后原因</td>
                <td>${afterSalesVo.reasonName}</td>
                <td class="wid20">第一责任部门</td>
                <td>${afterSalesVo.firstResponsibleDepartmentStr}</td>
            </tr>
            <tr>
                <td class="wid20">售后报单人</td>
                <td>${afterSalesVo.traderContactName}</td>
                <td>售后报单人电话</td>
                <td>
                    ${afterSalesVo.traderContactTelephone}
                    <c:if test="${not empty afterSalesVo.traderContactTelephone}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${afterSalesVo.traderContactTelephone}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>售后报单人手机号</td>
                <td>
                    ${afterSalesVo.traderContactMobile}
                    <c:if test="${not empty afterSalesVo.traderContactMobile}">
                        <i class="icontel cursor-pointer" title="点击拨号" onclick="callout('${afterSalesVo.traderContactMobile}',${afterSalesVo.traderId},1,4,${afterSalesVo.afterSalesId},${afterSalesVo.traderContactId});"></i>
                    </c:if>
                </td>
                <td>是否提前退换货</td>
                <td>
                    <c:choose>
                        <c:when test="${(taskInfo.assignee == curr_user.username or candidateUserMap['belong'])
                        && (null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]
                        && (!empty taskInfo.getName()) and  (taskInfo.getName() eq '售后主管审核')}">
                            <input type="radio" name="isLightning" id="y" value="0" required="required"> 是  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <input type="radio" name="isLightning" id="n" value="1" required="required"> 否
                        </c:when>
                        <c:otherwise>
                            <c:if test="${afterSalesVo.isLightning eq 0}">是</c:if>
                            <c:if test="${afterSalesVo.isLightning eq 1}">否</c:if>
                        </c:otherwise>
                    </c:choose>
                </td>
            </tr>
            <tr>
                <td>收货地区</td>
                <td>${afterSalesVo.area}</td>
                <td>收货地址</td>
                <td class="text-left">${afterSalesVo.address}</td>
            </tr>
            <tr>
            <tr>
                <td>详情说明</td>
                <td colspan="3" class="text-left">${afterSalesVo.comments}</td>
            </tr>
            <tr>
                <td>附件</td>
                <td colspan="3" class="text-left">
                    <%@ include file="view_afterSales_files.jsp"%>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <form action="" id="editAfterSaleOrderForm">
        <input type="hidden" name="afterSalesId" value="${afterSalesVo.afterSalesId}"/>
        <input type="hidden" name="afterSalesDetailId" value="${afterSalesVo.afterSalesDetailId}">
        <input type="hidden" name="refund" value="${afterSalesVo.refund}">
        <input type="hidden" name="traderSubject" value="${afterSalesVo.traderSubject}">
        <input type="hidden" name="payee" value="${afterSalesVo.payee}">
        <input type="hidden" name="bank" value="${afterSalesVo.bank}">
        <input type="hidden" name="bankCode" value="${afterSalesVo.bankCode}">
        <input type="hidden" name="bankAccount" value="${afterSalesVo.bankAccount}">
        <input type="hidden" name="traderMode" value="${afterSalesVo.traderMode}">
        <input type="hidden" name="refundAmountStatus" value="${afterSalesVo.refundAmountStatus}">
        <input class="child" type="hidden" id="layerIndex">
        <input type="hidden" id="typeFlag" value="0">
    </form>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">换货商品信息</div>
        </div>
        <c:set var="afterSalesGoodsVoListPage" value="${afterSalesVo.afterSalesGoodsList}"></c:set>
        <%@ include file="view_afterSales_hh_goods.jsp"%>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                所属订单信息
            </div>
        </div>
        <table class="table">
            <tbody>
            <tr>
                <td class="wid20">所属销售单号</td>
                <td >
                    <div class="customername pos_rel">
                       <span class="brand-color1 addtitle" style="float:none;" tabTitle='{"num":"viewsaleorder${afterSalesVo.orderNo}","title":"订单信息",
                            "link":"./finance/invoice/viewSaleorder.do?saleorderId=${afterSalesVo.orderId}"}'>${afterSalesVo.orderNo}</span><i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            付款状态：<c:if test="${afterSalesVo.paymentStatus eq 0}">未付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 1}">部分付款</c:if>
                            <c:if test="${afterSalesVo.paymentStatus eq 2}">全部付款</c:if><br>
                            发货状态：<c:if test="${afterSalesVo.deliveryStatus eq 0}">未发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 1}">部分发货</c:if>
                            <c:if test="${afterSalesVo.deliveryStatus eq 2}">全部发货</c:if><br>
                            开票状态：<c:if test="${afterSalesVo.invoiceStatus eq 0}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 1}">部分开票</c:if>
                            <c:if test="${afterSalesVo.invoiceStatus eq 2}">全部开票</c:if><br>
                            收货状态：<c:if test="${afterSalesVo.arrivalStatus eq 0}">未收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 1}">部分收货</c:if>
                            <c:if test="${afterSalesVo.arrivalStatus eq 2}">全部收货</c:if>
                        </div>
                    </div>
                </td>
                <td class="wid20">销售单金额</td>
                <td><fmt:formatNumber type="number" value="${afterSalesVo.totalAmount}" pattern="0.00" maxFractionDigits="2" /></td>
            </tr>
            <tr>
                <td>归属销售</td>
                <td>${afterSalesVo.userName}</td>
                <td>归属部门</td>
                <td>${afterSalesVo.orgName}</td>
            </tr>
            <tr>
                <td>订单状态</td>
                <td>
                    <c:if test="${afterSalesVo.saleorderStatus eq 0}">待确认</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 1}">进行中</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 2}">已完结</c:if>
                    <c:if test="${afterSalesVo.saleorderStatus eq 3}">已关闭</c:if>
                </td>
                <td>生效时间</td>
                <td>
                    <c:if test="${afterSalesVo.validStatus eq 1}">
                        <date:date value ="${afterSalesVo.validTime}"/>
                    </c:if>
                </td>
            </tr>
            <tr>
                <td>客户名称</td>
                <td>
                    <div class="customername pos_rel">
                                  <span class="brand-color1 addtitle" style="float:none;" tabTitle='{"num":"viewcustomer${afterSalesVo.traderId}","title":"客户信息",
										"link":"./trader/customer/baseinfo.do?traderId=${afterSalesVo.traderId}"}'>${afterSalesVo.traderName}</span><i class="iconbluemouth"></i>
                        <div class="pos_abs customernameshow" style="display: none;">
                            客户性质：<c:if test="${afterSalesVo.customerNature eq 465}">分销</c:if>
                            <c:if test="${afterSalesVo.customerNature eq 466}">终端</c:if><br>
                            交易次数：${afterSalesVo.orderCount}<br>
                            交易金额：<fmt:formatNumber type="number" value="${afterSalesVo.orderTotalAmount}" pattern="0.00" maxFractionDigits="2" /><br>
                            上次交易日期：<date:date value ="${afterSalesVo.lastOrderTime}"/>
                        </div>
                    </div>
                </td>
                <td>客户等级</td>
                <td>
                    ${afterSalesVo.customerLevelStr}
                </td>
            </tr>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                跟进记录
            </div>
        </div>
        <%@ include file="add_followUp_common.jsp"%>
    </div>

    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor">
                合同回传
            </div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th>合同</th>
                <th class="table-small">操作人</th>
                <th class="table-small">时间</th>
                <th class="table-small">操作</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${afterSalesVo.afterContractAttachmentList}" var="list" varStatus="status">
                <tr>
                    <td class="font-blue"><a href="http://${list.domain}${list.uri}" target="_blank">${list.name}</a></td>
                    <td>
                        <c:forEach var="user" items="${afterSalesVo.userList}">
                            <c:if test="${user.userId eq list.creator}">${user.username}</c:if>
                        </c:forEach>
                    </td>
                    <td><date:date value ="${list.addTime}"/></td>
                    <td>
                        <div class="caozuo">
                            <span class="caozuo-red" onclick="contractReturnDel(${list.attachmentId})">删除</span>
                        </div>
                    </td>
                </tr>
            </c:forEach>
            <c:if test="${empty afterSalesVo.afterContractAttachmentList}">
                <tr>
                    <td colspan="4">暂无记录！</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">换货入库记录</div>
        </div>
        <table class="table  table-style6">
            <c:if test="${not empty afterSalesVo.normalGoodsList}">
            <c:forEach items="${afterSalesVo.normalGoodsList}" var="arg" varStatus="num_index">
            <thead>
            <tr>
                <td colspan="14 ">
                    <div style="float: left; margin-left: 100px;">该商品换货入库数量：${arg.rknum}</div>
                    <div style="float: left; margin-left: 100px;">该商品已入库数量：${arg.arrivalNum}</div>
                    <div style="float: left; margin-left: 100px;">该商品换货入库状态：
                        <c:if test="${arg.inStockStatus == 4}">无入库</c:if>
                        <c:if test="${arg.inStockStatus == 5}">未入库</c:if>
                        <c:if test="${arg.inStockStatus == 6}">部分入库</c:if>
                        <c:if test="${arg.inStockStatus == 7}">全部入库</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="wid5">序号</th>
                <th>订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>制造商型号</th>
                <th>单位</th>
                <th>入库数量</th>
                <th>生产日期</th>
                <th>贝登批次码</th>
                <th>有效期至</th>
                <th class="wid15">入库时间</th>
                <th>厂家批次号</th>
                <th>灭菌批号</th>
                <th>注册证号</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${arg.aftersalesGoodsNormalDeliveryInList}" var="normalArg" varStatus="normal_num">
                <tr>
                    <td>${normal_num.count }</td>
                    <td>${normalArg.sku}</td>
                    <td class="text-left">
                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${normalArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${normalArg.goodsId}","title":"产品信息"}'>${newSkuInfosMap[normalArg.sku].SHOW_NAME}</a>
                    </td>
                    <td>${newSkuInfosMap[normalArg.sku].BRAND_NAME}</td>
                    <td>${newSkuInfosMap[normalArg.sku].MODEL}</td>
                    <td>${newSkuInfosMap[normalArg.sku].UNIT_NAME}</td>
                    <td>${normalArg.arrivalNum}</td>
                        <%--生产日期--%>
                    <td><date:date value ="${normalArg.productDate}" format="yyyy-MM-dd"/></td>
                    <td>${normalArg.vedengBatchNumer}</td>
                    <td><date:date value ="${normalArg.expirationDate}" format="yyyy-MM-dd"/></td>
                    <td><date:date value ="${normalArg.inStockTime}" format="yyyy-MM-dd"/></td>
                    <td>${normalArg.batchNumber}</td>
                    <td>${normalArg.sterilizationBatchNo}</td>
                    <td>
                        <c:choose>
                            <c:when test="${normalArg.firstEngageId == null}">
                                ${normalArg.registrationNumber}
                            </c:when>
                            <c:otherwise>
                                <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewfirstgoods${normalArg.firstEngageId}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${normalArg.firstEngageId}","title":"首营信息"}'>${ normalArg.registrationNumber}</a>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
            </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.normalGoodsList}">
                <tr>
                    <td colspan="14">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">直发换回记录</div>
        </div>
        <table class="table  table-style6">
            <c:if test="${not empty afterSalesVo.directGoodsList}">
            <c:forEach items="${afterSalesVo.directGoodsList}" var="arg" varStatus="num_index">
            <thead>
            <tr>
                <td colspan="10">
                    <div style="float: left; margin-left: 100px;">该商品直发换回数量：${arg.rknum}</div>
                    <div style="float: left; margin-left: 100px;">该商品已直发换回数量：${arg.arrivalNum}</div>
                    <div style="float: left; margin-left: 100px;">该商品换货入库状态：
                        <c:if test="${arg.inStockStatus == 4}">无入库</c:if>
                        <c:if test="${arg.inStockStatus == 5}">未入库</c:if>
                        <c:if test="${arg.inStockStatus == 6}">部分入库</c:if>
                        <c:if test="${arg.inStockStatus == 7}">全部入库</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="wid5">序号</th>
                <th>订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>制造商型号</th>
                <th>单位</th>
                <th>换回数量</th>
                <th>生产日期</th>
                <th>有效期至</th>
                <th>厂家批次号/SN码</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${arg.aftersalesGoodsDirectDeliveryInList}" var="directArg" varStatus="direct_num">
                <tr>
                    <td>${direct_num.count }</td>
                    <td>${directArg.sku}</td>
                    <td class="text-left">
                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${directArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${directArg.goodsId}","title":"产品信息"}'>${newSkuInfosMap[directArg.sku].SHOW_NAME}</a>
                    </td>
                    <td>${newSkuInfosMap[directArg.sku].BRAND_NAME}</td>
                    <td>${newSkuInfosMap[directArg.sku].MODEL}</td>
                    <td>${newSkuInfosMap[directArg.sku].UNIT_NAME}</td>
                    <td>${directArg.arrivalNum}</td>
                        <%--生产日期--%>
                    <td><date:date value ="${directArg.productDate}" format="yyyy-MM-dd"/></td>
                    <td><date:date value ="${directArg.expirationDate}" format="yyyy-MM-dd"/></td>
                    <td>${directArg.batchNumber}</td>
                </tr>
            </c:forEach>
            </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.directGoodsList}">
                <tr>
                    <input type="hidden" id="haveAfterSalesDirectInfo" value="1"/>
                    <td colspan="10">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">换货出库记录</div>
        </div>
        <table class="table  table-style6">
            <c:if test="${not empty afterSalesVo.normalGoodsList}">
            <c:forEach items="${afterSalesVo.normalGoodsList}" var="arg" varStatus="num_index">
            <thead>
            <tr>
                <td colspan="14 ">
                    <div style="float: left; margin-left: 100px;">该商品换货出库数量：${arg.rknum}</div>
                    <div style="float: left; margin-left: 100px;">该商品已出库数量：${arg.deliveryNum < 0 ? -arg.deliveryNum : arg.deliveryNum}</div>
                    <div style="float: left; margin-left: 100px;">该商品换货出库状态：
                        <c:if test="${arg.outStockStatus == 16}">无出库</c:if>
                        <c:if test="${arg.outStockStatus == 17}">未出库</c:if>
                        <c:if test="${arg.outStockStatus == 18}">部分出库</c:if>
                        <c:if test="${arg.outStockStatus == 19}">全部出库</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="wid5">序号</th>
                <th class="wid10">订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>制造商型号</th>
                <th class="wid4">单位</th>
                <th>出库数量</th>
                <th>生产日期</th>
                <th>贝登批次码</th>
                <th>有效期至</th>
                <th>出库时间</th>
                <th>厂商批号</th>
                <th>灭菌编号</th>
                <th class="wid12">注册证号</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${arg.aftersalesGoodsNormalDeliveryOutList}" var="normalArg" varStatus="normal_num">
                <tr>
                    <td>${normal_num.count }</td>
                    <td>${normalArg.sku}</td>
                    <td class="text-left">
                            <span class="brand-color1 addtitle"
                                  tabTitle='{"num":"viewgoods${normalArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${normalArg.goodsId}", "title":"产品信息"}'>
                                    ${newSkuInfosMap[normalArg.sku].SHOW_NAME}
                            </span>
                    </td>
                    <td>${newSkuInfosMap[normalArg.sku].BRAND_NAME}</td>
                    <td>${newSkuInfosMap[normalArg.sku].MODEL}</td>
                    <td>${newSkuInfosMap[normalArg.sku].UNIT_NAME}</td>
                    <td>${normalArg.deliveryNum < 0 ? -normalArg.deliveryNum : normalArg.deliveryNum}</td>
                    <td><date:date value ="${normalArg.productDate}" format="yyyy-MM-dd"/></td>
                    <td>${ normalArg.vedengBatchNumer}</td>
                    <td><date:date value ="${normalArg.expirationDate}" format="yyyy-MM-dd"/></td>
                    <td><date:date value ="${normalArg.outStockTime}" format="yyyy-MM-dd"/></td>
                    <td>${ normalArg.batchNumber}</td>
                    <td>${ normalArg.sterilizationBatchNo}</td>
                    <td class="text-left">
                        <c:choose>
                            <c:when test="${normalArg.firstEngageId == null}">
                                ${normalArg.registrationNumber}
                            </c:when>
                            <c:otherwise>
                                <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewfirstgoods${normalArg.firstEngageId}","link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${normalArg.firstEngageId}","title":"首营信息"}'>${ normalArg.registrationNumber}</a>
                            </c:otherwise>
                        </c:choose>
                    </td>
                </tr>
            </c:forEach>
            </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.normalGoodsList}">
                <tr>
                    <td colspan="14">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">直发换出记录</div>
        </div>
        <table class="table  table-style6">
            <c:if test="${not empty afterSalesVo.directGoodsList}">
            <c:forEach items="${afterSalesVo.directGoodsList}" var="arg" varStatus="num_index">
            <thead>
            <tr>
                <td colspan="10">
                    <div style="float: left; margin-left: 100px;">该商品直发换出数量：${arg.rknum}</div>
                    <div style="float: left; margin-left: 100px;">该商品已出库数量：${arg.deliveryNum < 0 ? -arg.deliveryNum : arg.deliveryNum}</div>
                    <div style="float: left; margin-left: 100px;">该商品换货出库状态：
                        <c:if test="${arg.outStockStatus == 16}">无出库</c:if>
                        <c:if test="${arg.outStockStatus == 17}">未出库</c:if>
                        <c:if test="${arg.outStockStatus == 18}">部分出库</c:if>
                        <c:if test="${arg.outStockStatus == 19}">全部出库</c:if>
                    </div>
                </td>
            </tr>
            <tr>
                <th class="wid5">序号</th>
                <th>订货号</th>
                <th>产品名称</th>
                <th>品牌</th>
                <th>制造商型号</th>
                <th>单位</th>
                <th>换回数量</th>
                <th>生产日期</th>
                <th>有效期至</th>
                <th>厂家批次号/SN码</th>
            </tr>
            </thead>
            <tbody>
            <c:forEach items="${arg.aftersalesGoodsDirectDeliveryOutList}" var="directArg" varStatus="direct_num">
                <tr>
                    <td>${direct_num.count}</td>
                    <td>${directArg.sku}</td>
                    <td class="text-left">
                        <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${directArg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${directArg.goodsId}","title":"产品信息"}'>${newSkuInfosMap[directArg.sku].SHOW_NAME}</a>
                    </td>
                    <td>${newSkuInfosMap[directArg.sku].BRAND_NAME}</td>
                    <td>${newSkuInfosMap[directArg.sku].MODEL}</td>
                    <td>${newSkuInfosMap[directArg.sku].UNIT_NAME}</td>
                    <td>${directArg.deliveryNum}</td>
                        <%--生产日期--%>
                    <td><date:date value ="${directArg.productDate}" format="yyyy-MM-dd"/></td>
                    <td><date:date value ="${directArg.expirationDate}" format="yyyy-MM-dd"/></td>
                    <td>${directArg.batchNumber}</td>
                </tr>
            </c:forEach>
            </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.directGoodsList}">
                <tr>
                    <td colspan="10">暂无记录</td>
                </tr>
            </c:if>
            </tbody>
        </table>
    </div>

    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                售后服务费信息
            </div>
        </div>
        <%@ include file="add_afterSales_service_fee_common.jsp"%>
    </div>

    <c:if test="${afterSalesVo.status eq 2}">
        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">交易记录</div>
                <div class="title-click nobor  pop-new-data"
                     layerParams='{"width":"600px","height":"400px","title":"新增交易记录","link":"/order/afterSalesCommon/addFinanceAfterCapital.do?afterSalesId=${afterSalesVo.afterSalesId}&billType=1&payApplyId=${payApplyId}&pageType=1"}'>
                    新增交易记录</div>
            </div>
            <table class="table">
                <thead>
                <tr>
                    <td colspan="11">
                        <div style="float: left; margin-left: 100px;">实际应收金额：
                                ${afterSalesVo.serviceAmount}
                        </div>
                        <div style="float: left; margin-left: 100px;">已收金额：
                                ${afterSalesVo.receiveAmount}
                        </div>
                        <div style="float: left; margin-left: 100px;">收款状态：
                            <c:if test="${afterSalesVo.amountCollectionStatus eq 0}">无收款</c:if>
                            <c:if test="${afterSalesVo.amountCollectionStatus eq 1}">未收款</c:if>
                            <c:if test="${afterSalesVo.amountCollectionStatus eq 2}">部分收款</c:if>
                            <c:if test="${afterSalesVo.amountCollectionStatus eq 3}">全部收款</c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>记账编号</th>
                    <th>业务类型</th>
                    <th>交易时间</th>
                    <th>交易主体</th>
                    <th>交易金额</th>
                    <th>交易方式</th>
                    <th>交易名称</th>
                    <th>交易备注</th>
                    <th>操作时间</th>
                    <th>操作人</th>
                    <th>电子回执单</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.afterCapitalBillList}">
                    <c:forEach items="${afterSalesVo.afterCapitalBillList}" var="acb">
                        <tr>
                            <td>${acb.capitalBillNo}</td>
                            <td>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 525}">订单付款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 526}">订单收款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 531}">退款</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 532}">资金转移</c:if>
                                <c:if test="${acb.capitalBillDetail.bussinessType eq 533}">信用还款</c:if>
                            </td>
                            <td>
                                <c:if test="${acb.traderTime != 0}">
                                    <date:date value="${acb.traderTime}" />
                                </c:if>
                            </td>
                            <td>
                                <c:if test="${acb.traderSubject == 1}">
                                    对公
                                </c:if>
                                <c:if test="${acb.traderSubject == 2}">
                                    对私
                                </c:if>
                            </td>
                            <td><fmt:formatNumber type="number" value="${acb.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td>
                                <c:if test="${acb.traderMode eq 520}">支付宝</c:if>
                                <c:if test="${acb.traderMode eq 521}">银行</c:if>
                                <c:if test="${acb.traderMode eq 522}">微信</c:if>
                                <c:if test="${acb.traderMode eq 522}">现金</c:if>
                                <c:if test="${acb.traderMode eq 527}">信用支付</c:if>
                                <c:if test="${acb.traderMode eq 528}">余额支付</c:if>
                                <c:if test="${acb.traderMode eq 529}">退还信用</c:if>
                                <c:if test="${acb.traderMode eq 530}">退还余额</c:if>
                            </td>
                            <td>${acb.payer}</td>
                            <td class="text-left">${acb.comments}</td>
                            <td>
                                <c:if test="${acb.addTime != 0}">
                                    <date:date value="${acb.addTime}" />
                                </c:if>
                            </td>
                            <td>
                                <c:forEach var="user" items="${afterSalesVo.userList}">
                                    <c:if test="${user.userId eq acb.creator}">${user.username}</c:if>
                                </c:forEach>
                            </td>
                            <td>
                                <c:if test="${(acb.traderType == 2 || acb.traderType == 5) && acb.bankBillId != 0}">
                                    <div class="caozuo">
                                        <c:if test="${isSupply ne 1}">
                                            <span class="caozuo-blue addtitle"   tabTitle='{"num":"credentials${acb.bankBillId}", "link":"<%=basePath%>finance/capitalbill/credentials.do?bankBillId=${acb.bankBillId}","title":"电子回执单"}'>查看</span>
                                        </c:if>
                                    </div>
                                </c:if>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
        </div>

        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">发票记录</div>
                <c:if test="${afterSalesVo.invoiceMakeoutStatus == 1}">
                    <div class="title-click nobor addtitle" tabTitle='{"num":"at_add_invoice${afterSalesVo.afterSalesId}","title":"确认开票",
                                    "link":"./order/afterSalesCommon/addAfterInvoiceAt.do?afterSalesId=${afterSalesVo.afterSalesId}"}'>
                        新增开票
                    </div>
                </c:if>
            </div>
            <table class="table">
                <thead>
                <tr>
                    <td colspan="11">
                        <div style="float: left; margin-left: 100px;">开票状态：
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus == 0}">无开票</c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus == 1}">未开票</c:if>
                            <c:if test="${afterSalesVo.invoiceMakeoutStatus == 2}">全部开票</c:if>
                        </div>
                    </td>
                </tr>
                <tr>
                    <th>发票号</th>
                    <th>发票代码</th>
                    <th>票种</th>
                    <th>红蓝字</th>
                    <th>发票金额</th>
                    <th>开票时间</th>
                    <th>操作时间</th>
                    <th>操作人</th>
                    <th>快递公司</th>
                    <th>快递单号</th>
                    <th>快递状态</th>
                </tr>
                </thead>
                <tbody>
                <c:if test="${not empty afterSalesVo.afterOpenInvoiceList}">
                    <c:forEach items="${afterSalesVo.afterOpenInvoiceList}" var="aoi">
                        <tr>
                            <td>${aoi.invoiceNo}</td>
                            <td>${aoi.invoiceCode}</td>
                            <td>
                                <c:if test="${aoi.invoiceType eq 429}">17%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 430}">17%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 682}">16%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 681}">16%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 972}">13%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 971}">13%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 683}">6%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 684}">6%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 685}">3%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 686}">3%增值税专用发票</c:if>
                                <c:if test="${aoi.invoiceType eq 687}">0%增值税普通发票</c:if>
                                <c:if test="${aoi.invoiceType eq 688}">0%增值税专用发票</c:if>
                            </td>

                            <td>
                                <c:choose>
                                    <c:when test="${aoi.colorType eq 1}">
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">红字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                <span style="color: red">红字有效</span>
                                            </c:otherwise>
                                        </c:choose>
                                    </c:when>
                                    <c:otherwise>
                                        <c:choose>
                                            <c:when test="${aoi.isEnable eq 0}">
                                                <span style="color: red">蓝字作废</span>
                                            </c:when>
                                            <c:otherwise>
                                                蓝字有效
                                            </c:otherwise>
                                        </c:choose>
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td><fmt:formatNumber type="number" value="${aoi.amount}" pattern="0.00" maxFractionDigits="2" /></td>
                            <td><date:date value="${aoi.addTime}" /></td>
                            <td><date:date value="${aoi.addTime}" /></td>
                            <td>
                                <c:forEach var="user" items="${afterSalesVo.userList}">
                                    <c:if test="${user.userId eq aoi.creator}">${user.username}</c:if>
                                </c:forEach>
                            </td>
                            <td>${aoi.logisticsName}</td>
                            <td>${aoi.logisticsNo}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${aoi.arrivalStatus eq 0}">
                                        未收货
                                    </c:when>
                                    <c:when test="${aoi.arrivalStatus eq 1}">
                                        部分收货
                                    </c:when>
                                    <c:when test="${aoi.arrivalStatus eq 2}">
                                        全部收货
                                    </c:when>
                                </c:choose>
                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                <c:if test="${empty afterSalesVo.afterOpenInvoiceList}">
                    <!-- 查询无结果弹出 -->
                    <tr>
                        <td colspan='11'>暂无记录！</td>
                    </tr>
                </c:if>
                </tbody>
            </table>
        </div>

        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">支出记录</div>
            </div>
            <%@ include file="add_expenditure_common.jsp"%>
        </div>

        <div class="parts">
            <div class="title-container">
                <div class="table-title nobor">收入记录</div>
            </div>
            <%@ include file="add_revenue_common.jsp"%>
        </div>

    </c:if>


    <%--各项审核记录模块--%>
    <div class="parts">
        <div class="title-container">
            <div class="table-title nobor">
                各项审核记录
            </div>
        </div>
        <%@ include file="add_auditRecords_common.jsp"%>
    </div>

    <script type="text/javascript">
        function check(id,aftersalesid){
            if(!document.getElementById("y").checked && !document.getElementById("n").checked){
                layer.open({
                    title: '信息',
                    content: '请选择是否提前发货'
                });
                return false;
            }else {
                var isLightning=$("input[name='isLightning']:checked").val();
                layer.open({
                    type: 2,
                    area: ['500px', '180px'],
                    title: '操作确认',
                    content: './complement.do?taskId='+id+'&pass=true&type=2&isLightning='+isLightning+'&lightningAfterSalesId='+aftersalesid
                })
            }
        }
    </script>

</div>
</body>

</html>
