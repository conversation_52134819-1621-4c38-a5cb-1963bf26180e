<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeNeedReceiveAdjustMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity">
    <!--@mbg.generated-->
    <!--@Table KING_DEE_NEED_RECEIVE_ADJUST-->
    <id column="KING_DEE_NEED_RECEIVE_ID" jdbcType="INTEGER" property="kingDeeNeedReceiveId" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_VPFN_Date" jdbcType="VARCHAR" property="fVpfnDate" />
    <result column="F_VPFN_JG" jdbcType="VARCHAR" property="fVpfnJg" />
    <result column="F_VPFN_KH" jdbcType="VARCHAR" property="fVpfnKh" />
    <result column="F_ENTITY" jdbcType="VARCHAR" property="fEntity" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_NEED_RECEIVE_ID, FID, F_BILL_NO, F_VPFN_Date, F_VPFN_JG, F_VPFN_KH, F_ENTITY, 
    ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_NEED_RECEIVE_ADJUST
    where KING_DEE_NEED_RECEIVE_ID = #{kingDeeNeedReceiveId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from KING_DEE_NEED_RECEIVE_ADJUST
    where KING_DEE_NEED_RECEIVE_ID = #{kingDeeNeedReceiveId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_NEED_RECEIVE_ID" keyProperty="kingDeeNeedReceiveId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_NEED_RECEIVE_ADJUST (FID, F_BILL_NO, F_VPFN_Date, 
      F_VPFN_JG, F_VPFN_KH, F_ENTITY, 
      ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      UPDATE_REMARK)
    values (#{fid,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, #{fVpfnDate,jdbcType=VARCHAR}, 
      #{fVpfnJg,jdbcType=VARCHAR}, #{fVpfnKh,jdbcType=VARCHAR}, #{fEntity,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_NEED_RECEIVE_ID" keyProperty="kingDeeNeedReceiveId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_NEED_RECEIVE_ADJUST
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fVpfnDate != null">
        F_VPFN_Date,
      </if>
      <if test="fVpfnJg != null">
        F_VPFN_JG,
      </if>
      <if test="fVpfnKh != null">
        F_VPFN_KH,
      </if>
      <if test="fEntity != null">
        F_ENTITY,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fVpfnDate != null">
        #{fVpfnDate,jdbcType=VARCHAR},
      </if>
      <if test="fVpfnJg != null">
        #{fVpfnJg,jdbcType=VARCHAR},
      </if>
      <if test="fVpfnKh != null">
        #{fVpfnKh,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        #{fEntity,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity">
    <!--@mbg.generated-->
    update KING_DEE_NEED_RECEIVE_ADJUST
    <set>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fVpfnDate != null">
        F_VPFN_Date = #{fVpfnDate,jdbcType=VARCHAR},
      </if>
      <if test="fVpfnJg != null">
        F_VPFN_JG = #{fVpfnJg,jdbcType=VARCHAR},
      </if>
      <if test="fVpfnKh != null">
        F_VPFN_KH = #{fVpfnKh,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        F_ENTITY = #{fEntity,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_NEED_RECEIVE_ID = #{kingDeeNeedReceiveId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeNeedReceiveAdjustEntity">
    <!--@mbg.generated-->
    update KING_DEE_NEED_RECEIVE_ADJUST
    set FID = #{fid,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_VPFN_Date = #{fVpfnDate,jdbcType=VARCHAR},
      F_VPFN_JG = #{fVpfnJg,jdbcType=VARCHAR},
      F_VPFN_KH = #{fVpfnKh,jdbcType=VARCHAR},
      F_ENTITY = #{fEntity,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where KING_DEE_NEED_RECEIVE_ID = #{kingDeeNeedReceiveId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="KING_DEE_NEED_RECEIVE_ID" keyProperty="kingDeeNeedReceiveId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into KING_DEE_NEED_RECEIVE_ADJUST
    (FID, F_BILL_NO, F_VPFN_Date, F_VPFN_JG, F_VPFN_KH, F_ENTITY, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.fid,jdbcType=VARCHAR}, #{item.fBillNo,jdbcType=VARCHAR}, #{item.fVpfnDate,jdbcType=VARCHAR}, 
        #{item.fVpfnJg,jdbcType=VARCHAR}, #{item.fVpfnKh,jdbcType=VARCHAR}, #{item.fEntity,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.updater,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="countMatchNum" resultType="java.lang.Integer">
    select count(1)
    from T_AFTER_SALES TAS
           left join T_AFTER_SALES_DETAIL TASD on TAS.AFTER_SALES_ID = TASD.AFTER_SALES_ID
    where TAS.TYPE = 543    #销售订单退款
      and TASD.REFUND = 2   #退给客户
      and TAS.AFTER_SALES_NO = #{afterSaleNo,jdbcType=VARCHAR};
    </select>
</mapper>