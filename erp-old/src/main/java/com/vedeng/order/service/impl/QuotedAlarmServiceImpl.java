package com.vedeng.order.service.impl;

import com.google.common.base.Preconditions;
import com.newtask.quoteorder.exception.QuoteOrderStatusChangedException;
import com.newtask.quoteorder.model.QuotedAlarmMessageDto;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.order.dao.QuotedAlarmLogMapper;
import com.vedeng.order.dao.QuoteorderConsultMapper;
import com.vedeng.order.dao.QuoteorderConsultReplyMapper;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.enums.MessageTemplateEnum;
import com.vedeng.order.enums.QuotedAlarmLevelEnum;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import com.vedeng.order.enums.QuotedTrackStateEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.service.QuotedAlarmService;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.UserService;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.MessageFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Service
public class QuotedAlarmServiceImpl extends BaseServiceimpl implements QuotedAlarmService {

    private final static Logger LOGGER = LoggerFactory.getLogger(QuotedAlarmServiceImpl.class);

    private final static Integer ON = 1;

    private static final Integer TEMP_GOODS_ID = 0;

    private static final Integer NON_USER_ID = 0;

    private final static int PURCHASING_TIME_TYE_PARENT_ID = 410;

    private final static int NON_PARENT_USE_ID = 0;

    /**
     * 当平台自有商品未分配归属人时发给默认处理人（kelly）
     */
    private final static int DEFAULT_GOODS_MGR_ID = 290;

    @Resource
    private QuoteService quoteService;
    @Resource
    private QuoteorderMapper quoteorderMapper;
    @Resource
    private QuoteorderConsultMapper quoteorderConsultMapper;
    @Resource
    private QuotedAlarmLogMapper quotedAlarmLogMapper;
    @Resource
    private QuoteorderConsultReplyMapper quoteorderConsultReplyMapper;
    @Resource
    private UserService userService;
    @Resource
    private RoleMapper roleMapper;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void unbindQuotedAlarm(Integer quoteOrderKey, Integer currentMode, List<Integer> goodsIdList) {
        Preconditions.checkArgument(Objects.nonNull(quoteOrderKey), "解除预警消息时记录报价单编号为空");

        checkMode(currentMode);

        QuotedAlarmModeEnum matchedModeToUse = getSupportMode(currentMode);

        Quoteorder quoteOrderQuery = quoteorderMapper.getQuoteorderById(quoteOrderKey);
        if (quoteOrderQuery == null) {
            throw new IllegalStateException("通过主键" + quoteOrderKey + "未查询到报价单");
        }

        if (quoteOrderQuery.getQuotedAlarmMode() == null || quoteOrderQuery.getQuotedAlarmMode().equals(QuotedAlarmModeEnum.OFF.getMode())) {
            return;
        }

        List<Integer> userIdList = determineUserIdList(quoteOrderQuery, matchedModeToUse, goodsIdList);
        if (userIdList.isEmpty()) {
            return;
        }

        Integer levelToUse;
        if (matchedModeToUse == QuotedAlarmModeEnum.SALESMAN_MODE) {
            levelToUse = quoteOrderQuery.getSalesmanAlarmLevel();
            quoteOrderQuery.setSalesmanAlarmLevel(QuotedAlarmLevelEnum.NONE.getLevel());
        } else {
            levelToUse = quoteOrderQuery.getPurchaserAlarmLevel();
            quoteOrderQuery.setPurchaserAlarmLevel(QuotedAlarmLevelEnum.NONE.getLevel());
        }

        quoteOrderQuery.setQuotedAlarmMode(QuotedAlarmModeEnum.OFF.getMode());
        quoteOrderQuery.setModTime(System.currentTimeMillis());
        quoteorderMapper.updateQuote(quoteOrderQuery);
        //记录日志

        recordQuotedAlarmLog(quoteOrderQuery.getQuoteorderNo(), matchedModeToUse.getMode(), levelToUse, userIdList, 2);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void sendQuotedAlarmMessage(QuotedAlarmMessageDto messageToSend) {
        Preconditions.checkArgument(Objects.nonNull(messageToSend.getQuoteOrderId()), "发送预警消息时记录报价单编号为空");

        checkMode(messageToSend.getMode());

        final Integer quoteOrderKey = messageToSend.getQuoteOrderId();

        Quoteorder quoteOrderQuery = quoteorderMapper.getQuoteorderById(quoteOrderKey);
        if (quoteOrderQuery == null) {
            throw new IllegalStateException("查询报价单：" + quoteOrderKey + "失败");
        }

        //校验报价单跟踪状态
        if (validQuoteOrderFollowingStatus(quoteOrderQuery.getFollowOrderStatus())) {
            throw new IllegalStateException("报价单：" + quoteOrderKey + " 已成单或者已失单，无需发送报价信息");
        }

        QuotedAlarmModeEnum modeToUse = getSupportMode(messageToSend.getMode());

        if (modeToUse.equals(QuotedAlarmModeEnum.SALESMAN_MODE)) {
            //检查销售咨询记录记录,是否存在多条
            List<QuoteorderConsult> quotedConsultCount = quoteorderConsultMapper.listQuotedConsult(quoteOrderKey, 1);
            if (quotedConsultCount != null && quotedConsultCount.size() > 1) {
                throw new IllegalStateException("报价单：" + quoteOrderKey + " 存在多条报价咨询或回复记录无需发送站内信息");
            }
        } else {
            List<QuoteorderConsult> quoteOrderConsultList = quoteorderConsultMapper.listQuotedConsult(quoteOrderKey, 2);
            if (quoteOrderConsultList.size() > 1) {
                throw new QuoteOrderStatusChangedException("报价单:" + quoteOrderKey + ", 咨询时已存在多条记录, 不再需要进行预警监控");
            }
        }

        List<Integer> acceptorIdsToUse = determineUserIdList(quoteOrderQuery, modeToUse, messageToSend.getQuotedGoodsIdList());

        Set<Integer> acceptorIdsAfterFilter = acceptorIdsToUse.stream().filter(e -> e != null && !e.equals(NON_USER_ID)).collect(Collectors.toSet());
        if (acceptorIdsAfterFilter.isEmpty()) {
            throw new IllegalStateException("发送报价预警消息时找到消息接受人 - quotedOrderId:" + messageToSend.getQuoteOrderId()
                    + ", mode:" + modeToUse.getMode());
        }

        List<MsgTemplateMappingEnum> templateList = MsgTemplateMappingEnum.getTemplateMapping(modeToUse.getMode(), messageToSend.getLevel());
        if (CollectionUtils.isEmpty(templateList)) {
            LOGGER.error("发送报价预警消息时匹配消息模版失败 - messageInfo:{}", messageToSend);
            return;
        }

        //更新报价单预警信息
        Quoteorder quoteOrderToUpdate = new Quoteorder();
        quoteOrderToUpdate.setQuoteorderId(quoteOrderKey);
        if (modeToUse == QuotedAlarmModeEnum.SALESMAN_MODE) {
            quoteOrderToUpdate.setSalesmanAlarmLevel(messageToSend.getLevel());
        } else {
            quoteOrderToUpdate.setPurchaserAlarmLevel(messageToSend.getLevel());
        }
        quoteOrderToUpdate.setQuotedAlarmMode(modeToUse.getMode());
        quoteOrderToUpdate.setModTime(System.currentTimeMillis());
        quoteorderMapper.updateQuote(quoteOrderToUpdate);

        doSendMessage(quoteOrderQuery, templateList, acceptorIdsAfterFilter);

        //记录日志
        recordQuotedAlarmLog(quoteOrderQuery.getQuoteorderNo(), modeToUse.getMode(), messageToSend.getLevel(), acceptorIdsToUse, 1);
    }


    //~================================================================================= Private methods


    private void checkMode(Integer mode) {
        if (mode == null || mode.equals(QuotedAlarmModeEnum.OFF.getMode())) {
            throw new IllegalArgumentException("Quoted alarm not turn on");
        }
    }

    private QuotedAlarmModeEnum getSupportMode(Integer mode) {
        return mode.equals(QuotedAlarmModeEnum.SALESMAN_MODE.getMode()) ? QuotedAlarmModeEnum.SALESMAN_MODE : QuotedAlarmModeEnum.PURCHASER_MODE;
    }

    private void recordQuotedAlarmLog(String quoteOrderNo, Integer mode, Integer level, List<Integer> userIdList, Integer logTye) {
        if (userIdList == null || userIdList.size() == 0) {
            return;
        }

        if (level == null || level.equals(QuotedAlarmLevelEnum.NONE.getLevel())) {
            return;
        }

        for (Integer userId : new HashSet<>(userIdList)) {
            //只有产品归属人有多人的情况出现
            User userQuery = userService.getUserById(userId);
            if (userQuery == null || Objects.equals(userQuery.getIsDisabled(), CommonConstants.IS_DELETE_1)) {
                return;
            }

            QuotedAlarmLogDo quotedAlarmLogDo = new QuotedAlarmLogDo();
            quotedAlarmLogDo.setCreatedTime(new Date());
            quotedAlarmLogDo.setLogType(logTye);
            quotedAlarmLogDo.setUserId(userQuery.getUserId());
            quotedAlarmLogDo.setUserName(userQuery.getUsername());
            quotedAlarmLogDo.setQuoteorderNo(quoteOrderNo);
            quotedAlarmLogDo.setQuotedAlarmMode(mode);
            quotedAlarmLogDo.setQuotedAlarmLevel(level);

            try {
                quotedAlarmLogMapper.insertSelective(quotedAlarmLogDo);
            } catch (Exception e) {
                LOGGER.error("记录报价预警日志失败", e);
            }
        }
    }


    /**
     * 报价回复状态(待回复)
     */
    private static final Integer WAIT_REPLYING = -1;

    /**
     * 报价回复状态(未咨询)
     */
    private static final Integer NOT_DONE = 0;

    /**
     * 报价回复状态(已回复)
     */
    private static final Integer REPLIED = 1;


    private List<Integer> determineUserIdList(Quoteorder quoteOrder, QuotedAlarmModeEnum matchedMode, List<Integer> quotedGoodsIdList) {
        final List<Integer> acceptorIdsToUse = new ArrayList<>();
        for (Integer quotedGoodsId : quotedGoodsIdList) {
            QuoteorderGoods quotedGoodsQuery = quoteorderMapper.getQuoteorderGoodsByQuoteorderGoodsId(quotedGoodsId);
            if (quotedGoodsQuery == null|| CommonConstants.IS_DELETE_1.equals(quotedGoodsQuery.getIsDelete())) {
                continue;
            }

            //没有沟通记录
            List<QuoteorderConsultReply> quotedGoodsRecordList = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(quoteOrder.getQuoteorderId(), quotedGoodsId);
            if (CollectionUtils.isEmpty(quotedGoodsRecordList)) {
                continue;
            }

            QuoteorderConsultReply lastQuotedConsultReply = quotedGoodsRecordList.get(0);
            if (Objects.isNull(lastQuotedConsultReply.getReferencePriceReplyStatus()) || NOT_DONE.equals(lastQuotedConsultReply.getReferencePriceReplyStatus())) {
                continue;
            }

            if (matchedMode == QuotedAlarmModeEnum.SALESMAN_MODE) {
                if (WAIT_REPLYING.equals(lastQuotedConsultReply.getReferencePriceReplyStatus())) {
                    continue;
                }

                acceptorIdsToUse.add(quoteOrder.getUserId());
            } else if (matchedMode == QuotedAlarmModeEnum.PURCHASER_MODE) {
                if (REPLIED.equals(lastQuotedConsultReply.getReferencePriceReplyStatus())) {
                    continue;
                }

                if (StringUtils.isNotEmpty(quotedGoodsQuery.getReferencePrice())) {
                    //供应链已回复商品报价参考价
                    continue;
                }
                acceptorIdsToUse.addAll(determineSupplierAcceptorIdList(quotedGoodsQuery));
            }
        }



        return acceptorIdsToUse;
    }

    private List<Integer> determineSupplierAcceptorIdList(final QuoteorderGoods quotedGoods) {
        boolean temGoodsFlag = false;
        if (quotedGoods.getIsTemp().equals(ON)) {
            temGoodsFlag = true;
        } else if (quotedGoods.getGoodsId() == null || quotedGoods.getGoodsId().equals(TEMP_GOODS_ID)) {
            temGoodsFlag = true;
        } else if (StringUtils.isEmpty(quotedGoods.getSku())) {
            temGoodsFlag = true;
        }

        List<Integer> acceptorIdList = new ArrayList<>();
        if (temGoodsFlag) {
            List<QuoteorderConsultReply> quoteOrderConsultReply = quoteorderConsultReplyMapper.getByQuoteorderIdAndQuoteorderGoods(quotedGoods.getQuoteorderId(), quotedGoods.getQuoteorderGoodsId());
            if (CollectionUtils.isEmpty(quoteOrderConsultReply)) {
                Integer consultReplierId = quoteOrderConsultReply.get(0).getConsultReplier();
                if (consultReplierId != null) {
                    acceptorIdList.add(consultReplierId);
                }
            }
        } else {
            List<Integer> assignUserList = quoteService.getAssignUserOfSku(quotedGoods.getSku());
            if (CollectionUtils.isNotEmpty(assignUserList)) {
                acceptorIdList.addAll(assignUserList);
            }
        }

        //如果找到报价处理人发送给默认报价处理人（kelly）
        if (acceptorIdList.isEmpty()) {
            acceptorIdList.add(DEFAULT_GOODS_MGR_ID);
        }

        return acceptorIdList;
    }


    private void doSendMessage(Quoteorder quoteOrderQuery, List<MsgTemplateMappingEnum> templateList, Collection<Integer> acceptorIds) {
        for (Integer operatorId : acceptorIds) {
            if (operatorId != null) {
                //获取当前消息接受人消息
                User currentOperator = getMessageAcceptor(operatorId);
                if (currentOperator == null) {
                    continue;
                }

                templateList.forEach(messageTemplateMapping -> {
                    MessageTemplateEnum messageTemplateEnum = messageTemplateMapping.getTemplate();
                    Integer acceptorType = messageTemplateEnum.getAcceptorType();
                    if (acceptorType == null) {
                        return;
                    }

                    final List<Integer> acceptorIdList = new ArrayList<>();
                    if (acceptorType == MessageTemplateEnum.MessageAcceptorType.ONLY_SELF) {
                        acceptorIdList.add(currentOperator.getUserId());
                    } else if (acceptorType == MessageTemplateEnum.MessageAcceptorType.ONLY_SUPER) {
                        acceptorIdList.add(currentOperator.getParentId());
                    } else if (acceptorType == MessageTemplateEnum.MessageAcceptorType.SUPER_AND_HEADER) {
                        if (currentOperator.getParentId() != null) {
                            acceptorIdList.add(currentOperator.getParentId());
                            //Note:报价预警目前只有采购端需要发送部门主管
                            List<Integer> generalManagerIdList = roleMapper.getUserIdByRoleName("供应主管", CommonConstants.COMPANY_ID_1);
                            if (CollectionUtils.isNotEmpty(generalManagerIdList)) {
                                acceptorIdList.addAll(generalManagerIdList);
                            }
                        }
                    }

                    List<Integer> acceptorListAft = acceptorIdList.stream().filter(e -> e != null && e != NON_PARENT_USE_ID).distinct().collect(Collectors.toList());
                    if (acceptorListAft.isEmpty()) {
                        LOGGER.error("【发送报价预警时】缺少消息接受人，待处理人: {} ，消息类型：{}", currentOperator.getUserId(), messageTemplateEnum.getDescription());
                        return;
                    }

                    List<String> parameterNames = messageTemplateEnum.getParameterNames();
                    final QuotedAlarmParameter quotedAlarmParameter = new QuotedAlarmParameter();
                    quotedAlarmParameter.setQuotedOrderNo(quoteOrderQuery.getQuoteorderNo());
                    quotedAlarmParameter.setUsername(currentOperator.getUsername());

                    if (parameterNames.contains(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_PURCHASING_TIME)) {
                        String purchasingTimeStr = getPurchasingTimeByDictId(quoteOrderQuery.getPurchasingTime());
                        if (StringUtils.isEmpty(purchasingTimeStr)) {
                            LOGGER.warn("【发送报预警信息时】报价单采购类型未在配置中找到，报价单采购类型：{}", quoteOrderQuery.getPurchasingTime());
                        }
                        quotedAlarmParameter.setPurchasingTypeDesc(purchasingTimeStr);
                    }

                    Map<String, String> parameters = createParameters(parameterNames, quotedAlarmParameter);

                    String targetURL;
                    if (messageTemplateEnum.hasURL()) {
                        targetURL = messageTemplateEnum.getUrl();
                    } else {
                        targetURL = StringUtils.EMPTY;
                    }
                    if (!targetURL.equals(StringUtils.EMPTY)) {
                        targetURL = MessageFormat.format(targetURL, String.valueOf(quoteOrderQuery.getQuoteorderId().intValue()));
                    }

                    //发送消息
                    Boolean sendFlag = MessageUtil.sendMessage(messageTemplateEnum.getId(), acceptorListAft, parameters, targetURL);
                    LOGGER.info("【报价预警消息】发送消息 " + (Objects.equals(sendFlag, Boolean.TRUE) ? "成功" : "失败") + "- quoteOrderId:{}, messageTemplateId:{}, messageTemplate:{}, acceptorId:{}",
                            quoteOrderQuery.getQuoteorderId(), messageTemplateEnum.getId(), messageTemplateEnum.getDescription(), Arrays.toString(acceptorListAft.toArray(new Integer[0])));
                });
            }
        }
    }


    private User getMessageAcceptor(Integer acceptorId) {
        User currentUser;
        try {
            currentUser = userService.getUserById(acceptorId);
        } catch (Exception e) {
            LOGGER.error("【发送报价预警消息失败】查询用户信息失败 - userId:{}", acceptorId, e);
            currentUser = null;
        }

        if (currentUser == null || Objects.equals(currentUser.getIsDisabled(), CommonConstants.IS_DELETE_1)) {
            LOGGER.info("【发送报价预警消息失败】当前用户信息不存在或者已被禁用 - userId:{}", acceptorId);
        }
        return currentUser;
    }


    private boolean validQuoteOrderFollowingStatus(Integer status) {
        return (Objects.equals(status, QuotedTrackStateEnum.DONE.getState()) || Objects.equals(status, QuotedTrackStateEnum.MISSED.getState()));
    }

    /**
     * 根据数据字典配置的id获取采购时间
     *
     * @param dictId
     * @return
     */
    private String getPurchasingTimeByDictId(Integer dictId) {
        List<SysOptionDefinition> returnedList = getSysOptionDefinitionList(PURCHASING_TIME_TYE_PARENT_ID);
        if (returnedList == null || returnedList.isEmpty()) {
            returnedList = Collections.emptyList();
        }

        Map<Integer, String> options = new HashMap<>();
        returnedList.forEach(e -> {
            if (e == null) {
                return;
            } else if (e.getSysOptionDefinitionId() == null || StringUtils.isEmpty(e.getTitle()) || !Objects.equals(e.getStatus(), ON)) {
                LOGGER.error("数据字典数据异常 - id:{}, title:{},status:{}", e.getSysOptionDefinitionId(), e.getTitle(), e.getStatus());
                return;
            }
            options.put(e.getSysOptionDefinitionId(), e.getTitle());
        });

        return options.get(dictId);

    }


    private Map<String, String> createParameters(List<String> parameterNames, QuotedAlarmParameter quotedAlarmParameter) {
        Map<String, String> parameters = new HashMap<>();
        parameterNames.forEach(name -> {
            if (name.equals(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_QUOTED_ORDER_NO)) {
                parameters.put(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_QUOTED_ORDER_NO, quotedAlarmParameter.getQuotedOrderNo());
            }
            if (name.equals(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_PURCHASING_TIME)) {
                parameters.put(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_PURCHASING_TIME, quotedAlarmParameter.getPurchasingTypeDesc());
            }
            if (name.equals(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_USERNAME)) {
                parameters.put(MessageTemplateEnum.QuotedAlarmMessageParameter.PARAM_USERNAME, quotedAlarmParameter.getUsername());
            }
        });
        return parameters;
    }


//~================================================================================================ Inner classes


    @Data
    private static class QuotedAlarmParameter {

        private String quotedOrderNo;

        private String username;

        private String purchasingTypeDesc;

    }

    /**
     * 站内消息模版映射关系
     */
    private enum MsgTemplateMappingEnum {

        /**
         * 响应预警 - 采购端触发
         */
        TEMPLATE_123(MessageTemplateEnum.TEMPLATE_123, QuotedAlarmModeEnum.PURCHASER_MODE, QuotedAlarmLevelEnum.LEVEL_THREE),
        TEMPLATE_124(MessageTemplateEnum.TEMPLATE_124, QuotedAlarmModeEnum.PURCHASER_MODE, QuotedAlarmLevelEnum.LEVEL_TWO),
        TEMPLATE_125(MessageTemplateEnum.TEMPLATE_125, QuotedAlarmModeEnum.PURCHASER_MODE, QuotedAlarmLevelEnum.LEVEL_TWO),
        TEMPLATE_126(MessageTemplateEnum.TEMPLATE_126, QuotedAlarmModeEnum.PURCHASER_MODE, QuotedAlarmLevelEnum.LEVEL_ONE),
        TEMPLATE_127(MessageTemplateEnum.TEMPLATE_127, QuotedAlarmModeEnum.PURCHASER_MODE, QuotedAlarmLevelEnum.LEVEL_ONE),

        /**
         * 跟进预警 - 销售端触发
         */
        TEMPLATE_128(MessageTemplateEnum.TEMPLATE_128, QuotedAlarmModeEnum.SALESMAN_MODE, QuotedAlarmLevelEnum.LEVEL_TWO),
        TEMPLATE_129(MessageTemplateEnum.TEMPLATE_129, QuotedAlarmModeEnum.SALESMAN_MODE, QuotedAlarmLevelEnum.LEVEL_ONE),
        TEMPLATE_130(MessageTemplateEnum.TEMPLATE_130, QuotedAlarmModeEnum.SALESMAN_MODE, QuotedAlarmLevelEnum.LEVEL_ONE),
        ;


        private MessageTemplateEnum template;
        private QuotedAlarmModeEnum alarmMode;
        private QuotedAlarmLevelEnum alarmLevel;


        MsgTemplateMappingEnum(MessageTemplateEnum template, QuotedAlarmModeEnum alarmMode, QuotedAlarmLevelEnum alarmLevel) {
            this.template = template;
            this.alarmMode = alarmMode;
            this.alarmLevel = alarmLevel;
        }

        public static List<MsgTemplateMappingEnum> getTemplateMapping(Integer mode, Integer level) {
            QuotedAlarmModeEnum matchModeEnum = QuotedAlarmModeEnum.getOrDefault(mode);
            QuotedAlarmLevelEnum matchLevelEnum = QuotedAlarmLevelEnum.getOrDefault(level);

            if (matchModeEnum == QuotedAlarmModeEnum.OFF || matchLevelEnum == QuotedAlarmLevelEnum.NONE) {
                return Collections.emptyList();
            }

            List<MsgTemplateMappingEnum> returnedList = new ArrayList<>();
            for (MsgTemplateMappingEnum templateEnum : values()) {
                if (matchLevelEnum == templateEnum.getAlarmLevel() && matchModeEnum == templateEnum.getAlarmMode()) {
                    returnedList.add(templateEnum);
                }
            }

            return returnedList;
        }

        public MessageTemplateEnum getTemplate() {
            return template;
        }

        public QuotedAlarmModeEnum getAlarmMode() {
            return alarmMode;
        }

        public QuotedAlarmLevelEnum getAlarmLevel() {
            return alarmLevel;
        }}
}
