<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.phoneticWriting.dao.CommentMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.phoneticWriting.model.Comment" >
    <!--          -->
    <id column="COMMENT_ID" property="commentId" jdbcType="INTEGER" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    COMMENT_ID, CONTENT, ADD_TIME, CREATOR, MOD_TIME, UPDATER,RELATED_ID
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_COMMENT
    where COMMENT_ID = #{commentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_COMMENT
    where COMMENT_ID = #{commentId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.phoneticWriting.model.Comment" >
    <!--          -->
    insert into T_COMMENT (COMMENT_ID, CONTENT, RELATED_ID,ADD_TIME,
      CREATOR, MOD_TIME, UPDATER
      )
    values (#{commentId,jdbcType=INTEGER}, #{content,jdbcType=VARCHAR}, #{relatedId,jdbcType=INTEGER},#{addTime,jdbcType=BIGINT},
      #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.phoneticWriting.model.Comment" >
    <!--          -->
    insert into T_COMMENT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="commentId != null" >
        COMMENT_ID,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="relatedId != null" >
        RELATED_ID,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="commentId != null" >
        #{commentId,jdbcType=INTEGER},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.phoneticWriting.model.Comment" >
    <!--          -->
    update T_COMMENT
    <set >
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="relatedId != null" >
        RELATED_ID = #{relatedId,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where COMMENT_ID = #{commentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.phoneticWriting.model.Comment" >
    <!--          -->
    update T_COMMENT
    set CONTENT = #{content,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      RELATED_ID = #{relatedId,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER}
    where COMMENT_ID = #{commentId,jdbcType=INTEGER}
  </update>
  <!--查询点评记录-->
  <select id="getComList" resultMap="BaseResultMap" parameterType="com.vedeng.trader.model.CommunicateRecord" >
    <!--          -->
    select
    <include refid="Base_Column_List" />
    from T_COMMENT
    where RELATED_ID = #{communicateRecordId,jdbcType=INTEGER}
    order by COMMENT_ID
  </select>

</mapper>