package com.vedeng.order.service;

import com.vedeng.common.page.Page;
import com.vedeng.common.service.BaseService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.JcTraderContactDto;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.model.vo.TraderCustomerVo;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;
import java.util.Map;

/**
 * <b>Description:</b><br>
 * 集采订单管理接口
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 * <b>PackageName:</b> com.vedeng.order.service <br>
 * <b>ClassName:</b> JcOrderService <br>
 * <b>Date:</b> 2021年2月22日 下午1:42:24
 */
public interface JcOrderService extends BaseService {

    /**
     * 集采订单列表
     *
     * @param request
     * @param saleOrder 订单搜索条件
     * @param page
     * @return
     */
    Map<String, Object> getJcSaleOrderListPage(HttpServletRequest request, Saleorder saleOrder, Page page);

    /**
     * 获取集采用户的基本信息
     *
     * @param contactId
     * @return
     */
    JcTraderContactDto getJcAccountInfo(Integer contactId);


    /**
     * 根据用户获取集团客户列表信息（含分院）
     *
     * @param traderId
     * @return
     */
    List<TraderCustomerVo> listAllGroupCustomer(Integer traderId);

    /**
     * 根据手机号获取集采用户的角色信息，以/拼接
     * @param mobile 手机号
     * @return 角色信息
     */
    String getRoleValueOfJcAccount(String mobile);



    /**
     * 选择客户后保存线下集采订单基本信息
     *
     * @param saleorder
     * @param request
     * @param session
     * @return
     */
    Saleorder saveJcfOrderAfterSelectingCustomer(Saleorder saleorder, HttpServletRequest request, HttpSession session);

    /**
     * 保存线下集采订单详细信息
     *
     * @param saleorder
     * @param request
     * @param session
     * @return
     */
    Integer saveJcfOrderDetail(Saleorder saleorder, HttpServletRequest request, HttpSession session);


    /**
     * 保存线上集采订单信息
     *
     * @param order
     * @return
     */
    int saveJcoOrder(Saleorder order);


    List<TraderContactGenerate> listContactWithGroupCustomer(Integer traderCustomerIdId);
}
