package com.vedeng.department.dao;

import com.vedeng.department.model.DepartmentsHospitalGenerate;
import com.vedeng.department.model.DepartmentsHospitalGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DepartmentsHospitalGenerateMapper {

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	long countByExample(DepartmentsHospitalGenerateExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int deleteByExample(DepartmentsHospitalGenerateExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int deleteByPrimaryKey(Integer departmentId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int insert(DepartmentsHospitalGenerate record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int insertSelective(DepartmentsHospitalGenerate record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	List<DepartmentsHospitalGenerate> selectByExample(DepartmentsHospitalGenerateExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	DepartmentsHospitalGenerate selectByPrimaryKey(Integer departmentId);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int updateByExampleSelective(@Param("record") DepartmentsHospitalGenerate record,
			@Param("example") DepartmentsHospitalGenerateExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int updateByExample(@Param("record") DepartmentsHospitalGenerate record,
			@Param("example") DepartmentsHospitalGenerateExample example);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int updateByPrimaryKeySelective(DepartmentsHospitalGenerate record);

	/**
	 * This method was generated by MyBatis Generator. This method corresponds to the database table T_DEPARTMENTS_HOSPITAL
	 * @mbg.generated  Wed May 15 13:53:39 CST 2019
	 */
	int updateByPrimaryKey(DepartmentsHospitalGenerate record);

	/**
	 * 根据商品三级分类编号和删除标记查询关联记录
	 *
	 * @param thirdLevelCategoryId
	 * @param deleted
	 * @return
	 */
	List<DepartmentsHospitalGenerate > listDepartmentDoByThirdLevelCategoryId(@Param("thirdLevelCategoryId") Integer thirdLevelCategoryId, @Param("deleted") Integer deleted);

	/**
	 * 主键批量获取科室筛选信息
	 *
	 * @param departmentIds
	 * @return
	 */
	List<DepartmentsHospitalGenerate> getDepartmentsHospitalGenerateByIds(@Param("departmentIds")List<Integer> departmentIds);
}