package com.wms.dto;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.authorization.model.User;

import java.util.List;

/**
 * 采购退货出库处理器
 */
public class PurchaseReturnoutProcessDto {

    private User user;

    private AfterSalesVo afterSalesInfo;

    private List<PutSaleOrderGoodsDto> outputOrderGoodsList;

    public AfterSalesVo getAfterSalesInfo() {
        return afterSalesInfo;
    }

    public void setAfterSalesInfo(AfterSalesVo afterSalesInfo) {
        this.afterSalesInfo = afterSalesInfo;
    }

    public List<PutSaleOrderGoodsDto> getOutputOrderGoodsList() {
        return outputOrderGoodsList;
    }

    public void setOutputOrderGoodsList(List<PutSaleOrderGoodsDto> outputOrderGoodsList) {
        this.outputOrderGoodsList = outputOrderGoodsList;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
