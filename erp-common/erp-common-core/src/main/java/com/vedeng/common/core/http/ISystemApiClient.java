package com.vedeng.common.core.http;

import com.vedeng.common.core.enums.SystemSourceEnum;
import java.util.Map;

/**
 * 系统API客户端接口
 * 定义统一的系统级API调用规范，支持多种实现方式
 * 
 * 设计目标：
 * - 提供统一的API调用接口
 * - 支持用户认证和公司配置
 * - 支持多种实现（完整版、轻量版、代理版等）
 * - 保持接口简洁和易用性
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
public interface ISystemApiClient {
    
    /**
     * 指定用户ID进行API调用
     *
     * @param userId 用户ID
     * @return ISystemApiClient实例（支持链式调用）
     */
    ISystemApiClient withUser(Long userId);
    
    /**
     * 指定用户名进行API调用
     *
     * @param userName 用户名
     * @return ISystemApiClient实例（支持链式调用）
     */
    ISystemApiClient withUser(String userName);
    
    /**
     * 指定公司代码进行API调用
     *
     * @param companyCode 公司代码
     * @return ISystemApiClient实例（支持链式调用）
     */
    ISystemApiClient withCompany(String companyCode);
    
    /**
     * 清除当前用户信息
     *
     * @return ISystemApiClient实例（支持链式调用）
     */
    ISystemApiClient clearUser();
    
    /**
     * 清除当前公司信息
     *
     * @return ISystemApiClient实例（支持链式调用）
     */
    ISystemApiClient clearCompany();
    
    /**
     * 发送POST请求到系统API
     * 
     * 实现说明：
     * - 完整版实现：通过CompanyApiConfigService自动获取公司API地址
     * - 轻量版实现：需要通过其他方式指定API地址
     *
     * @param apiPath API路径（如：/api/v1/temporal/workflow）
     * @param requestData 请求数据
     * @return 响应结果
     */
    String postToSystemApi(String apiPath, Object requestData);
    
    /**
     * 发送POST请求到系统API（带额外请求头）
     *
     * @param apiPath API路径
     * @param requestData 请求数据
     * @param additionalHeaders 额外的请求头
     * @return 响应结果
     */
    String postToSystemApi(String apiPath, Object requestData, Map<String, String> additionalHeaders);

    /**
     * 发送POST请求到系统API（指定系统来源）
     *
     * @param apiPath API路径
     * @param requestData 请求数据
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    String postToSystemApi(String apiPath, Object requestData, String systemSource);

    /**
     * 发送POST请求到系统API（带额外请求头和系统来源）
     *
     * @param apiPath API路径
     * @param requestData 请求数据
     * @param additionalHeaders 额外的请求头
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    String postToSystemApi(String apiPath, Object requestData, Map<String, String> additionalHeaders, String systemSource);

    /**
     * 发送POST请求到系统API（使用枚举指定系统来源）
     *
     * @param apiPath API路径
     * @param requestData 请求数据
     * @param systemSource 系统来源枚举
     * @return 响应结果
     */
    String postToSystemApi(String apiPath, Object requestData, SystemSourceEnum systemSource);
    
    /**
     * 发送GET请求到系统API
     *
     * @param apiPath API路径
     * @return 响应结果
     */
    String getFromSystemApi(String apiPath);
    
    /**
     * 发送GET请求到系统API（带额外请求头）
     *
     * @param apiPath API路径
     * @param additionalHeaders 额外的请求头
     * @return 响应结果
     */
    String getFromSystemApi(String apiPath, Map<String, String> additionalHeaders);

    /**
     * 发送GET请求到系统API（指定系统来源）
     *
     * @param apiPath API路径
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    String getFromSystemApi(String apiPath, String systemSource);

    /**
     * 发送GET请求到系统API（带额外请求头和系统来源）
     *
     * @param apiPath API路径
     * @param additionalHeaders 额外的请求头
     * @param systemSource 系统来源（如：XXL-JOB、TEMPORAL、SYSTEM等）
     * @return 响应结果
     */
    String getFromSystemApi(String apiPath, Map<String, String> additionalHeaders, String systemSource);

    /**
     * 发送GET请求到系统API（使用枚举指定系统来源）
     *
     * @param apiPath API路径
     * @param systemSource 系统来源枚举
     * @return 响应结果
     */
    String getFromSystemApi(String apiPath, SystemSourceEnum systemSource);
    
    /**
     * 检查当前实现是否支持自动公司配置
     * 
     * @return true表示支持通过CompanyApiConfigService自动获取公司API地址，
     *         false表示需要手动配置API地址
     */
    boolean supportsAutoCompanyConfig();
    
    /**
     * 获取当前实现的类型描述
     * 
     * @return 实现类型描述，如："SystemApiClient-Full"、"SystemApiClient-Proxy"等
     */
    String getImplementationType();
}
