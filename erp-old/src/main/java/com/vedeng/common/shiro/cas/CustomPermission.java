package com.vedeng.common.shiro.cas;

import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.authz.Permission;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CustomPermission implements Permission {

    private final List<String> parts;

    public CustomPermission(List<String> parts) {
        this.parts = CollectionUtils.isEmpty(parts) ? Collections.emptyList() : parts;
    }

    @Override
    public boolean implies(Permission perm) {
        if (perm instanceof CustomPermission) {
            CustomPermission customPerm = (CustomPermission) perm;
            if (CollectionUtils.isNotEmpty(customPerm.getParts())) {
                return CollectionUtils.isEqualCollection(this.getParts(), customPerm.getParts());
            }
        }
        return false;
    }

    public List<String> getParts() {
        return parts;
    }

}
