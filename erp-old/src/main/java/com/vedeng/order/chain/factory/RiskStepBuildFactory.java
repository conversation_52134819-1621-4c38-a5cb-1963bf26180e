package com.vedeng.order.chain.factory;

import com.vedeng.order.chain.RiskHandlerStep;
import com.vedeng.order.chain.step.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName RiskStepBuildFactory.java
 * @Description TODO 风控步骤器
 * @createTime 2020年12月08日 15:42:00
 */

@Service
public class RiskStepBuildFactory {
    @Autowired
    private FirstRiskCheckHandler firstRiskCheckHandler;

    @Autowired
    private SkuRiskCheckHandler skuRiskCheckHandler;

    @Autowired
    private SpuRiskCheckHandler spuRiskCheckHandler;

    @Autowired
    private SupplierRiskCheckHandler supplierRiskCheckHandler;

    @Autowired
    private TraderRiskCheckHandler traderRiskCheckHandler;

    @Autowired
    private CommonRiskCheckHandler commonRiskCheckHandler;

    //sku校验
    public RiskHandlerStep skuRiskCheck(){
        commonRiskCheckHandler.setNext(skuRiskCheckHandler);
        skuRiskCheckHandler.setNext(spuRiskCheckHandler);
        spuRiskCheckHandler.setNext(firstRiskCheckHandler);
        return commonRiskCheckHandler;
    }

    //客户校验
    public RiskHandlerStep traderRiskCheck(){
        commonRiskCheckHandler.setNext(traderRiskCheckHandler);
        return commonRiskCheckHandler;
    }

    //供应商校验
    public RiskHandlerStep supplierRiskCheck(){
        commonRiskCheckHandler.setNext(supplierRiskCheckHandler);
        return commonRiskCheckHandler;
    }
}
