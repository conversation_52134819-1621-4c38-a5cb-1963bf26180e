package com.vedeng.erp.buyorder.dto;


import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BuyOrderApiDto {

    private Integer buyorderId;

    /**
     * 采购单号
     */
    private String buyorderNo;

    /**
     * 供应商名称
     */
    private String traderName;
    /**
     * 交易者id
     */
    private Integer traderId;
    /**
     * 联系人ID
     */
    private Integer traderContactId;
    /**
     * 联系人姓名
     */
    private String traderContactName;
    /**
     * 联系人电话
     */
    private String traderContactMobile;
    /**
     * 联系人地址ID
     */
    private Integer traderAddressId;
    /**
     * 供应商地区
     */
    private String traderArea;
    /**
     * 联系详细地址(含省市区)'
     */
    private String traderAddress;

    /**
     * 收货联系人ID
     */
    private Integer takeTraderContactId;

    /**
     * 收货联系人名称
     */
    private String takeTraderContactName;
    /**
     * 收货联系人电话
     */
    private String takeTraderContactMobile;
    /**
     * '收货地址ID'
     */
    private String takeTraderAddressId;
    /**
     * '收货地址'
     */
    private String takeTraderAddress;


    /**
     * 供应商id
     */
    private Integer traderSupplierId;
    /**
     * 总额
     */
    private BigDecimal totalAmount;

    /**
     * 生效时间
     */
    private Long validTime;
    /**
     * 采购单状态
     */
    private Integer status;

    /**
     * 付款方式
     */
    private Integer paymentType;
    /**
     * 预付金额
     */
    private BigDecimal prepaidAmount;
    /**
     * 尾款金额
     */
    private BigDecimal retainageAmount;
    /**
     * 尾款期限(月)
     */
    private Integer retainageAmountMonth;

    /**
     * 发票类型
     */
    private Integer invoiceType;

    /**
     * 账期支付金额
     */
    private BigDecimal accountPeriodAmount;

    /**
     * 账期天数
     */
    private Integer periodDay;

    private List<BuyorderGoodsApiDto> buyorderGoodsApiDtos = new ArrayList<>();

    private Integer creator;

    private String contractUrl;
    
    private Integer lockedStatus;
    private Integer invoiceStatus;
    private Integer arrivalStatus;
    private Integer deliveryStatus;
    // 是否直发
    private Integer deliveryDirect;

    private Integer validStatus;

    private Integer paymentStatus;

    /**
     * 发票信息列表
     */
    private List<BuyOrderInvoiceDto> invoiceList;
}
