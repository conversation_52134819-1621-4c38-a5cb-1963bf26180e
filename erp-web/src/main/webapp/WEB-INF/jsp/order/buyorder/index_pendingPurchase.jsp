
<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="待采购订单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<style>
	.gery{
		pointer-events: none;
		color:grey;
	}
	.customernameshow1 {
		top: 0px;
		border: 1px solid #999;
		z-index: 123456;
		display: none;
		padding: 8px;
		background: #fff;
		width: 222px;
		border-radius: 5px;
		text-align: left;
		box-shadow: -2px 2px 5px rgba(153, 153, 153, 0.4), 2px 2px 5px rgba(153, 153, 153, 0.4);
		-moz-box-shadow: -2px 2px 5px rgba(153, 153, 153, 0.4), 2px 2px 5px rgba(153, 153, 153, 0.4);
		-webkit-box-shadow: -2px 2px 5px rgba(153, 153, 153, 0.4), 2px 2px 5px rgba(153, 153, 153, 0.4);
	}
	 .tip-hover-title:hover+.customernameshow1{
		 display: block;
	 }

</style>
<script type="text/javascript" src='<%= basePath %>static/js/order/buyorder/index_pendingPurchase.js?rnd=${resourceVersionKey}'></script>
	<div class="main-container">
		<div class="layui-tab layui-tab-brief" lay-filter="test">
			<ul class="layui-tab-title">
				<li onclick="changeTab(0)" <c:if test="${tabFlag eq 0}">class="layui-this"</c:if>>立即采购</li>
				<li onclick="changeTab(1)" <c:if test="${tabFlag eq 1}">class="layui-this"</c:if>>暂缓采购</li>
				<li onclick="changeTab(2)" <c:if test="${tabFlag eq 2}">class="layui-this"</c:if>>无需采购</li>
			</ul>
		</div>
		<div class="list-pages-search">
			<form method="post" id="search" action="<%= basePath %>/order/buyorder/indexPendingPurchase.do?tabFlag=${tabFlag}">
				<input type="hidden" class="input-middle" id="urlSaleorderNo" name="urlSaleorderNo" value="${goodsVo.urlSaleorderNo}"/>
				<ul>
					<li>
						<label class="infor_name">产品名称</label>
						<input type="text" class="input-middle" name="goodsName" id="goodsName" value="${goodsVo.goodsName}"/>
					</li>
					<li>
						<label class="infor_name">品牌</label>
						<input type="text" class="input-middle" name="brandName" id="brandName" value="${goodsVo.brandName}"/>
					</li>
					<li>
						<label class="infor_name">型号</label>
						<input type="text" class="input-middle" name="model" id="model" value="${goodsVo.model}"/>
					</li>
					<li>
						<label class="infor_name">订货号</label>
						<input type="text" class="input-middle" name="sku" id="sku" value="${goodsVo.sku}"/>
					</li>
					<li>
						<label class="infor_name">单号</label>
						<input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${goodsVo.saleorderNo}"/>
					</li>
					<li>
						<label class="infor_name">申请人</label>
						<select class="input-middle" name="userId" id="">
							<option value="">全部</option>
							<c:forEach items="${applicantList}" var="user">
								<option value="${user.userId}" <c:if test="${goodsVo.userId eq user.userId}">selected="selected"</c:if>>${user.username}</option>
							</c:forEach>
						</select>
					</li>
					<li>
						<label class="infor_name">产品部门</label>
						<select class="input-middle" name="proOrgtId" id="" onchange="changeOrg(this);">
							<option value="">全部</option>
							<c:forEach items="${productOrgList}" var="org" varStatus="sttaus">
								<%--<c:if test="${sttaus.count gt 1}">--%>
									<option value="${org.orgId}" <c:if test="${goodsVo.proOrgtId eq org.orgId}">selected="selected"</c:if>>${org.orgName}</option>
								<%--</c:if>--%>
							</c:forEach>
						</select>
					</li>
					<li>
						<label class="infor_name">产品归属</label>
						<select class="input-middle" name="proUserId" id="proUserId">
							<option value="">全部</option>
							<c:forEach items="${productUserList}" var="user">
								<option value="${user.userId}" <c:if test="${goodsVo.proUserId eq user.userId}">selected="selected"</c:if>>${user.username}</option>
							</c:forEach>
						</select>
					</li>
					<li>
						<label class="infor_name">是否直发</label>
						<select class="input-middle" name="deliveryDirect" id="deliveryDirect">
							<option value="-1">全部</option>
							<option <c:if test="${goodsVo.deliveryDirect eq 0}">selected</c:if> value="0">普发</option>
							<option <c:if test="${goodsVo.deliveryDirect eq 1}">selected</c:if> value="1">直发</option>
						</select>
					</li>
					<li>
						<label class="infor_name">订单类型</label>
						<!-- 订单类型:0销售订单/2备货订单/3订货订单/4经销商订单 -->
						<select class="input-middle" name="orderType" id="orderType">
							<!-- 对应库里ORDER_TYPE 0,3,4 -->
							<option <c:if test="${goodsVo.orderType eq 1}">selected</c:if> value="1">非备货单</option>
							<!-- 对应库里ORDER_TYPE 2 -->
							<option <c:if test="${goodsVo.orderType eq 2}">selected</c:if> value="2">备货单</option>
							<!-- 订货单，JX订单都归属订货单 对应库里ORDER_TYPE 4 -->
							<option <c:if test="${goodsVo.orderType eq 4}">selected</c:if> value="4">经销商订单</option>
							<option <c:if test="${goodsVo.orderType eq 5}">selected</c:if> value="5">耗材商城订单</option>
							<option <c:if test="${goodsVo.orderType eq -1}">selected</c:if> value="-1">全部</option>
						</select>
					</li>
					<li>
						<label class="searchItem infor_name">可采购时间</label>
						<input class="Wdate f_left input-smaller96 m0" type="text" autocomplete="off"
							   placeholder="请选择日期"
							   onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'buyEndTimeStr\')}'})"
							   name="buyStartTimeStr" id="buyStartTimeStr"
							   value="${goodsVo.buyStartTimeStr}">
						<div class="f_left ml1 mr1 mt4">-</div> <input
							class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'buyStartTimeStr\')}'})"
							name="buyEndTimeStr" id="buyEndTimeStr" value="${goodsVo.buyEndTimeStr}">
					</li>
					<li>
						<label class="infor_name">时效状态</label>
						<select class="input-middle" name="aging" id="aging">
							<option value="">全部</option>
							<option <c:if test="${goodsVo.aging eq 0}">selected</c:if> value="0">正常</option>
							<option <c:if test="${goodsVo.aging eq 1}">selected</c:if> value="1">临期</option>
							<option <c:if test="${goodsVo.aging eq 2}">selected</c:if> value="2">逾期</option>
						</select>
					</li>
					<li>
						<label class="infor_name">预警等级</label>
						<select class="input-middle" name="warnLevel" id="warnLevel">
							<option value="">全部</option>
							<option <c:if test="${goodsVo.warnLevel eq 1}">selected</c:if> value="1">一级</option>
							<option <c:if test="${goodsVo.warnLevel eq 2}">selected</c:if> value="2">二级</option>
							<option <c:if test="${goodsVo.warnLevel eq 3}">selected</c:if> value="3">三级</option>
						</select>
					</li>
					<li>
						<label class="infor_name">订单助理</label>
						<select class="input-middle" name="orderAssId" id="orderAssId">
							<option value="-1">全部</option>
							<c:forEach items="${orderAssUserList}" var="user">
								<option value="${user.userId}" <c:if test="${goodsVo.orderAssId eq user.userId}">selected="selected"</c:if>>${user.username}</option>
							</c:forEach>
						</select>
					</li>
                    <li>
                        <label class="infor_name">专向发货</label>
                        <select class="input-middle" name="specialDelivery" id="specialDelivery">
                            <option value="">全部</option>
                            <option <c:if test="${goodsVo.specialDelivery eq 0}">selected</c:if> value="0">否</option>
                            <option <c:if test="${goodsVo.specialDelivery eq 1}">selected</c:if> value="1">是</option>
                        </select>
                    </li>
					<li>
						<label class="infor_name">赠品类型</label>
						<select class="input-middle" name="isGift" id="isGift">
							<option value="">请选择</option>
							<option <c:if test="${goodsVo.isGift eq 0}">selected</c:if> value="0">非赠品</option>
							<option <c:if test="${goodsVo.isGift eq 1}">selected</c:if> value="1">备货赠品</option>
						</select>
					</li>
					<li>
						<label class="infor_name">是否特麦帮</label>
						<select class="input-middle" name="isSpecialSales" id="isSpecialSales">
							<option value="">全部</option>
							<option <c:if test="${goodsVo.isSpecialSales eq '是'}">selected</c:if> value="是">是</option>
							<option <c:if test="${goodsVo.isSpecialSales eq '否'}">selected</c:if> value="否">否</option>
						</select>
					</li>
				</ul>
				<div class="tcenter">
					<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
					<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
					<c:if test="${tabFlag eq 0}">
						<shiro:hasPermission name="/order/buyorder/indexPendingPurchaseExport.do">
							<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="exportDetail();">下载所有待采购订单明细</span>
						</shiro:hasPermission>
					</c:if>
				</div>
			</form>
		</div>
	 <div class='parts'>
	 	<c:forEach var="gv" items="${list}" >
	 		<table class="table table-style7">
                <thead>
                    <tr>
                    	<th class="wid10">订单号</th>
                        <th class="wid8">申请人</th>
						<th class="wid15">可采购时间</th>
						<th class="wid15">终端名称</th>
						<th class="wid15">销售区域</th>
						<th class="wid15">是否特麦帮</th>
						<th class="wid15">订单内部备注</th>
						<th class="wid15">物流备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                    	<td>${gv.saleorderNo}</td>
                    	<td>${gv.applicantName}</td>
                        <c:if test="${gv.orderType eq 2}">
                        	<td>--</td>
                        	<td>--</td>
                        	<td>--</td>
                        	<td>--</td>
                        </c:if>
                       <c:if test="${gv.orderType ne 2}">
                       		<td><date:date value ="${gv.satisfyDeliveryTime}"/></td>
                        	<td>${gv.terminalTraderName}</td>
	                        <td>${gv.salesArea}</td>
	                        <td>${gv.isSpecialSales}</td>
	                        <td>${gv.comments}</td>
	                        <td>${gv.logisticsComments}</td>
                        </c:if>
                    </tr>
                    <tr>
                        <td colspan="8" class="table-container ">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th class="wid3">选择</th>
                                        <th class="wid5">预警等级</th>
                                        <th class="wid5">时效状态</th>
                                        <th class="wid5">订货号</th>
                                        <th class="wid15">产品名称</th>
                                        <th class="wid10">品牌/型号</th>
                                        <th class="wid5">物料编码</th>
                                        <th class="wid4">单位</th>
                                        <th class="wid6">产品级别</th>
										<th class="wid8">归属产品经理</th>
										<th class="wid8">归属产品助理</th>
                                        <th class="wid7">可用库存/库存量</th>
                                        <th class="wid7">在途数量</th>
                                        <th class="wid5">是否直发</th>
                                        <th class="wid5">需采数量</th>
                                        <th class="wid5">商品总数</th>
                                        <th class="wid5">专向发货</th>
                                        <th class="wid8">内部备注</th>
                                        <th class="wid8">产品备注</th>
										<c:if test="${tabFlag ne 0}"><th class="wid8">原因</th></c:if>
                                    </tr>
                                </thead>
                                <tbody>
                                	<c:forEach var="sgv" items="${gv.sgvList}" varStatus="num">
	                                    <tr>
	                                        <td>
	                                        	<c:if test="${sgv.lockedStatus eq 0}">
	                                        		<input type="checkbox" name="oneSelect" value="" autocomplete="off" onclick="oneSelect(this);" alt="${sgv.saleorderId}"/>
	                                        	</c:if>
												<c:if test="${sgv.lockedStatus eq 1}">
													<span class="warning-color1 ">锁</span>
												</c:if>
												<c:if test="${sgv.lockedStatus eq 2}">
												<div style="position: relative;">
													<span class="warning-color1 tip-hover-title" >账期</span>
													<div class='pos_abs customernameshow1 mouthControlPos' style='left:calc(100% + 10px);color:black;'>账期订单：需销售合同审批通过后，方可采购。可采购时间及时效暂不计算。</div>
												</div>
												</c:if>
												<input type="hidden" name="saleorderGoodId" value="${sgv.goodsId}|${sgv.saleorderGoodsId}"/>
												<input type="hidden" name="goodsId" value="${sgv.goodsId}"/>
												<c:if test="${sgv.deliveryDirect eq 1}">
													<input type="hidden" name="noBuyNum" value="${sgv.num-(sgv.buyNum > 0 ? sgv.buyNum : 0)}" alt3="${sgv.saleorderId}">
												</c:if>
												<c:if test="${sgv.deliveryDirect eq 0}">
													<input type="hidden" name="noBuyNum" value="${sgv.needBuyNum}" alt3="${sgv.saleorderId}">
												</c:if>
												<input type="hidden" name="saleorderType" value="${gv.orderType}">
												<input type="hidden" name="saleorderNo" value="${gv.saleorderNo}">
												<input type="hidden" name="saleorderId" value="${gv.saleorderId}">
												<input type="hidden" name="deliveryDirect" value="${sgv.deliveryDirect}">
												<input type="hidden" name="isGift" value="${sgv.isGift == null ? "0": sgv.isGift}">
												<input type="hidden" name="isVirture" value="${sgv.isVirture == null ? "0": sgv.isVirture}">
											</td>
											<!--VDERP-12903、VDERP-13550 虚拟商品在待采购订单列表字段规则，备货订单无需计算默认预警等级：无预警&时效状态：正常-->
											<td><c:choose>
													<c:when test="${sgv.warnLevel == 1}">一级</c:when>
													<c:when test="${sgv.warnLevel == 2}">二级</c:when>
													<c:when test="${sgv.warnLevel == 3}">三级</c:when>
													<c:otherwise>-</c:otherwise>
												</c:choose>
											</td>
											<!--VDERP-12903、VDERP-13550 虚拟商品在待采购订单列表字段规则，备货订单无需计算默认预警等级：无预警&时效状态：正常-->
											<td>
												<c:choose>
													<c:when test="${sgv.aging == 0}">正常</c:when>
													<c:when test="${sgv.aging == 1}">临期</c:when>
													<c:when test="${sgv.aging == 2}">逾期</c:when>
													<c:otherwise>-</c:otherwise>
												</c:choose>
											</td>
											<td>${sgv.sku}</td>
											<td>
												<c:if test="${sgv.isGift eq 1}"><span style="color: red">赠品</span>&nbsp;</c:if>
												<c:if test="${sgv.isVirture eq 2}"><span style="color: red">虚拟</span>&nbsp;</c:if><span style="display: inline" class="font-blue cursor-pointer addtitle" tabTitle='{"num":"viewsaleorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
		                    						"link":"./goods/goods/viewbaseinfo.do?goodsId=${sgv.goodsId}","title":"产品信息"}'>${newSkuInfosMap[sgv.sku].SHOW_NAME}</span>
		                    				</td>
											<td>${newSkuInfosMap[sgv.sku].BRAND_NAME}/${newSkuInfosMap[sgv.sku].MODEL}</td>
											<td>${newSkuInfosMap[sgv.sku].MATERIAL_CODE}</td>
											<td>${newSkuInfosMap[sgv.sku].UNIT_NAME}</td>
											<td>${newSkuInfosMap[sgv.sku].GOODS_LEVEL_NAME}</td>
											<td>${sgv.assignmentManagerId}</td>
											<td>${sgv.assignmentAssistantId}</td>
											<!-- -->
											<td>
												<c:if test="${sgv.isVirture != 2}">&nbsp;
													${sgv.canUseGoodsStock > 0 ? sgv.canUseGoodsStock : 0}/${sgv.goodsStock == null ? 0 : sgv.goodsStock}
												</c:if>
												<c:if test="${sgv.isVirture == 2}">
												0
												</c:if>
											</td>
											<td>
												<c:if test="${sgv.isVirture != 2 }">&nbsp;
													${sgv.onWayNum > 0 ? sgv.onWayNum : 0}</td>
												</c:if>
												<c:if test="${sgv.isVirture == 2 }">
													0
												</c:if>
											<td>
												<!--针对虚拟商品：销售订单中取对应直发字段结果值，备货订单则为普发-->
												<c:if test="${sgv.isVirture == 2}">
													<c:if test="${sgv.orderType == 2}">
														普发
													</c:if>
													<c:if test="${sgv.orderType != 2}">
														<c:if test="${sgv.deliveryDirect eq 0}">
															普发
														</c:if>
														<c:if test="${sgv.deliveryDirect eq 1}">
															<div class="customername pos_rel ">
																<div>
															<span class="warning-color1">
																直发
															</span>
																	<i class="iconbluesigh "></i>
																</div>
																<div class="pos_abs customernameshow " style="display: none; ">
																		${sgv.deliveryDirectComments}
																</div>
															</div>
														</c:if>
													</c:if>
												</c:if>
												<!--非虚拟商品按照原始逻辑展示 -->
												<c:if test="${sgv.isVirture != 2 }">
													<c:if test="${sgv.deliveryDirect eq 0}">
														普发
													</c:if>
													<c:if test="${sgv.deliveryDirect eq 1}">
														<div class="customername pos_rel ">
															<div>
															<span class="warning-color1">
																直发
															</span>
																<i class="iconbluesigh "></i>
															</div>
															<div class="pos_abs customernameshow " style="display: none; ">
																	${sgv.deliveryDirectComments}
															</div>
														</div>
													</c:if>
												</c:if>
											</td>
											<td>
												<c:if test="${sgv.isVirture == 2}">
													${sgv.needBuyNum}
												</c:if>
												<c:if test="${sgv.isVirture != 2}">
													<c:if test="${sgv.deliveryDirect eq 0}">
														${sgv.needBuyNum}
													</c:if>
													<c:if test="${sgv.deliveryDirect eq 1}">
														<!--VDERP-13277 兼容老代码，回滚时此处可以不动-->
														<c:if test="${sgv.needBuyNum == null}">
															${sgv.num-(sgv.buyNum > 0 ? sgv.buyNum : 0)}
														</c:if>
														<c:if test="${sgv.needBuyNum != null}">
															${sgv.needBuyNum}
														</c:if>
													</c:if>
												</c:if>
											</td>
											<td>${sgv.num}</td>
											<td style="color: red">
												<!--针对虚拟商品：专向发货：默认为否-->
												<c:if test="${sgv.isVirture == 2}">
													否
												</c:if>
												<c:if test="${sgv.isVirture != 2}">
													<c:if test="${sgv.specialDelivery ne 1}">
														否
													</c:if>
													<c:if test="${sgv.specialDelivery eq 1}">
														是
													</c:if>
												</c:if>
											</td>
											<td>
												<!--内部备注：针对虚拟商品，销售订单中取内部备注，备货订单则为空-->
												<c:if test="${sgv.isVirture == 2 }">
													<c:if test="${sgv.orderType != 2}">
														<span class="warning-color1">${sgv.insideComments}</span>
													</c:if>
													<!--备货订单则为空-->
												</c:if>
												<c:if test="${sgv.isVirture != 2}">
														<span class="warning-color1">${sgv.insideComments}</span>
												</c:if>

											</td>

											<td>
												<!--产品备注：针对虚拟商品，销售订单中取内部备注，备货订单则为空-->
												<c:if test="${sgv.isVirture == 2}">
													<c:if test="${sgv.orderType != 2}">
														${sgv.goodsComments}
													</c:if>
													<!--备货订单则为空-->
												</c:if>
												<c:if test="${sgv.isVirture != 2}">
													${sgv.goodsComments}
												</c:if>
											</td>
											<c:if test="${tabFlag ne 0}"><td>${sgv.buyProcessModReson}</td></c:if>
	                                    </tr>
                                    </c:forEach>
                                    <tr>
                                        <td <c:if test="${tabFlag ne 0}">colspan="19"</c:if>
											<c:if test="${tabFlag eq 0}">colspan="18"</c:if>
											class="text-left">
                                            <input type="checkbox" style="margin-top: 1px" autocomplete="off" name="oneAllSelect" onclick="oneAllSelect(this);" alt2="${gv.saleorderId}">
                                            <span class="mr20">全选</span>
                                            <span>共</span>
                                            <span class="warning-color1 ">${gv.proBuySum}</span>
                                            <span>件</span>
                                            <span>已选择</span>
                                            <span alt1="${gv.saleorderId}" class="warning-color1 oneTableSelectNum">0</span>
                                            <span>件</span>
											<c:if test="${tabFlag eq 0 || tabFlag eq 1}">
												<span <c:if test="${tabFlag eq 0}">style="display: none" </c:if> <c:if test="${gv.lockedStatus eq 1}"> class="ml4 cursor-pointer bt-middle bt-border-style gery "</c:if><c:if test="${gv.lockedStatus ne 1}">class="ml4 cursor-pointer bt-middle bt-border-style"</c:if> onclick="buyNow(${gv.saleorderId});">立即采购</span>
												<span <c:if test="${tabFlag eq 1}">style="display: none" </c:if> <c:if test="${gv.lockedStatus eq 1}"> class="ml4 cursor-pointer bt-middle bt-border-style gery "</c:if><c:if test="${gv.lockedStatus ne 1}">class="ml4 cursor-pointer bt-middle bt-border-style"</c:if> onclick="buyLater(${gv.saleorderId});">暂缓采购</span>
												<span <c:if test="${gv.lockedStatus eq 1}"> class="ml4 cursor-pointer bt-middle bt-border-style gery "</c:if><c:if test="${gv.lockedStatus ne 1}">class="ml4 cursor-pointer bt-middle bt-border-style"</c:if> onclick="dontBuy(${gv.saleorderId});">无需采购</span>
											</c:if>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                </tbody>
            </table>

        </c:forEach>
         <c:if test="${not empty list }">
            <div class="tablelastline">
	            <input type="checkbox" autocomplete="off" name="allSelect" onclick="allSelect(this);">
	            <span class="mr20">全选</span>当前全部待采购商品共<span class="warning-color1">${buySum}</span>件，已选择<span class="warning-color1" id="selected">0</span>件
            </div>
       </c:if>
       <c:if test="${empty list }">
       		 <table class="table table-style7">
                <thead>
                    <tr>
                    	<th class="wid10">订单号</th>
                        <th class="wid8">申请人</th>
						<th class="wid15">可采购时间</th>
						<th class="wid15">终端名称</th>
						<th class="wid15">销售区域</th>
						<th class="wid15">是否特麦帮</th>
						<th class="wid15">订单内部备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                    	<td colspan='6'>未搜索到满足条件的结果！请尝试使用其他搜索条件。</td>
                    </tr>
                </tbody>
             </table>
       </c:if>
            <div class="table-friend-tip">
                友情提示 <br/>1、备货单、销售订单不允许同时被选择；<br/> 2、直发与普发不允许同时被选择； <br/>3、直发仅允许选择同订单产品；
				<br/>4、专向与非专向不允许同时被选择；<br/>5、专向仅允许选择同订单产品；<br/>6、赠品产品与非赠品产品不允许同时被选择；
            </div>
            <div class="table-buttons">
				<div style="display: none">
					<button style="display: none;" type="button" class="bt-bg-style bg-light-blue bt-small mr10" id="addvbbutton"  >关联在途备货采购单</button>
				</div>
				<span style="display:none;">
					<!-- 添加到在途VB弹框 -->
					<div class="title-click nobor  pop-new-data" id="addvbview"></div>
				</span>
				<span style="display:none;">
					<!-- 改变采购要求弹框 -->
					<div class="title-click nobor  pop-new-data" id="resonView"></div>
				</span>
	            <%--<button type="button" class="bt-bg-style bg-light-green bt-small mr10" id="buyorder">生成采购订单</button>--%>
				<button type="button" class="bt-bg-style bg-light-green bt-small mr10" id="newBuyorder">生成采购订单</button>
				<span style="display:none;"><div class="title-click nobor addtitle2" id="addpf"></div></span>
				<button type="button" class="bt-bg-style bg-light-green bt-small mr10" id="addFeebuy">生成采购费用订单</button>
				<span style="display:none;"><div class="title-click nobor addtitle2" id="feebuy"></div></span>
	            <button type="button" class="bt-bg-style bg-light-green bt-small mr10" id="addbuy">加入采购订单</button>
	            <span style="display:none;"><div class="title-click nobor addtitle2" id="addbuyorder"></div></span>
	        </div>
        </div>
        <tags:page page="${page}"/>
	</div>
</body>

</html>
