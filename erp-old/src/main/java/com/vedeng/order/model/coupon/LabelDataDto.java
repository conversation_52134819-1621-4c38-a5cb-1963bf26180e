package com.vedeng.order.model.coupon;

import com.vedeng.order.model.RemarkComponentTree;
import com.vedeng.order.model.query.LabelQuery;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Description:  备注组件传输对象
 * @Author:       Davis
 * @Date:         2021/4/14 下午2:58
 * @Version:      1.0
 */
@Data
public class LabelDataDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组件集合
     */
    private List<RemarkComponentTree> data;

    /**
     * 人工备注
     */
    private String remark;

    /**
     * 组件参数
     */
    private LabelQuery labelQuery;

}
