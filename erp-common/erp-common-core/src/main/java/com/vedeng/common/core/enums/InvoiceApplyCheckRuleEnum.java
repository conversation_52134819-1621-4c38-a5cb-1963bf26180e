package com.vedeng.common.core.enums;

import java.util.ArrayList;
import java.util.List;

public enum InvoiceApplyCheckRuleEnum {
    SALES_ORDER_AUTO_INVOICE(5001, "销售订单自动开票开关"),
    AFTER_SALES_ORDER_AUTO_INVOICE(5002, "售后订单自动开票开关"),
    CONFIRMATION_EFFECTIVE_ONLY_LAST(5003, "确认单仅最后一次申请生效开关");

    private final int code;
    private final String description;

    InvoiceApplyCheckRuleEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    public static List<Integer> getCodes() {
        List<Integer> codes = new ArrayList<>();
        for (InvoiceApplyCheckRuleEnum rule : InvoiceApplyCheckRuleEnum.values()) {
            codes.add(rule.getCode());
        }
        return codes;
    }

}
