package com.wms.dto;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * WMS库存转移单详情运输类
 *
 * <AUTHOR>
 * @date 2020/7/25 14:12:25
 */
public class WmsInventoryTransferDetailDto implements Serializable {
    /**
     * 转移单行号
     */
    private String TDOCLineNo;

    /**
     * 转移时间
     */
    private String TransferTime;

    /**
     * SKU
     */
    private String SKU;

    /**
     * 转移目标数量
     */
    private BigDecimal TOQty;

    /**
     * 生产日期
     */
    private String LotAtt01;

    /**
     * 有效期至
     */
    private String LotAtt02;

    /**
     * 入库日期
     */
    private String LotAtt03;

    /**
     * 厂家批号
     */
    private String LotAtt04;

    /**
     * 灭菌批号
     */
    private String LotAtt05;

    /**
     * 注册证号
     */
    private String LotAtt06;

    /**
     * 预留字段
     */
    private String LotAtt07;

    /**
     * 质量状态
     */
    private String LotAtt08;

    /**
     * 预留字段
     */
    private String LotAtt09;

    /**
     * 入库单号
     */
    private String LotAtt10;

    /**
     * 贝登批次码
     */
    private String LotAtt11;

    /**
     * 预留字段
     */
    private String LotAtt12;

    /**
     * TO生产日期
     */
    private String ToLotAtt01;

    /**
     * TO有效期至
     */
    private String ToLotAtt02;

    /**
     * TO入库日期
     */
    private String ToLotAtt03;

    /**
     * TO厂家批号
     */
    private String ToLotAtt04;

    /**
     * TO灭菌批号
     */
    private String ToLotAtt05;

    /**
     * TO注册证号
     */
    private String ToLotAtt06;

    /**
     * TO预留字段
     */
    private String ToLotAtt07;

    /**
     * TO质量状态
     */
    private String ToLotAtt08;

    /**
     * TO销售订单号
     */
    private String ToLotAtt09;

    /**
     * TO入库单号
     */
    private String ToLotAtt10;

    /**
     * TO贝登批次码
     */
    private String ToLotAtt11;

    /**
     * TO预留字段
     */
    private String ToLotAtt12;

    /**
     * '
     * 预留字段
     */
    private String UserDefine1;

    /**
     * 预留字段
     */
    private String UserDefine2;

    /**
     * 预留字段
     */
    private String UserDefine3;

    /**
     * 预留字段
     */
    private String UserDefine4;

    /**
     * 预留字段
     */
    private String UserDefine5;

    public String getTDOCLineNo() {
        return TDOCLineNo;
    }

    public void setTDOCLineNo(String TDOCLineNo) {
        this.TDOCLineNo = TDOCLineNo;
    }

    public String getTransferTime() {
        return TransferTime;
    }

    public void setTransferTime(String transferTime) {
        TransferTime = transferTime;
    }

    public String getSKU() {
        return SKU;
    }

    public void setSKU(String SKU) {
        this.SKU = SKU;
    }

    public BigDecimal getTOQty() {
        return TOQty;
    }

    public void setTOQty(BigDecimal TOQty) {
        this.TOQty = TOQty;
    }

    public String getLotAtt01() {
        return LotAtt01;
    }

    public void setLotAtt01(String lotAtt01) {
        LotAtt01 = lotAtt01;
    }

    public String getLotAtt02() {
        return LotAtt02;
    }

    public void setLotAtt02(String lotAtt02) {
        LotAtt02 = lotAtt02;
    }

    public String getLotAtt03() {
        return LotAtt03;
    }

    public void setLotAtt03(String lotAtt03) {
        LotAtt03 = lotAtt03;
    }

    public String getLotAtt04() {
        return LotAtt04;
    }

    public void setLotAtt04(String lotAtt04) {
        LotAtt04 = lotAtt04;
    }

    public String getLotAtt05() {
        return LotAtt05;
    }

    public void setLotAtt05(String lotAtt05) {
        LotAtt05 = lotAtt05;
    }

    public String getLotAtt06() {
        return LotAtt06;
    }

    public void setLotAtt06(String lotAtt06) {
        LotAtt06 = lotAtt06;
    }

    public String getLotAtt07() {
        return LotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        LotAtt07 = lotAtt07;
    }

    public String getLotAtt08() {
        return LotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        LotAtt08 = lotAtt08;
    }

    public String getLotAtt09() {
        return LotAtt09;
    }

    public void setLotAtt09(String lotAtt09) {
        LotAtt09 = lotAtt09;
    }

    public String getLotAtt10() {
        return LotAtt10;
    }

    public void setLotAtt10(String lotAtt10) {
        LotAtt10 = lotAtt10;
    }

    public String getLotAtt11() {
        return LotAtt11;
    }

    public void setLotAtt11(String lotAtt11) {
        LotAtt11 = lotAtt11;
    }

    public String getLotAtt12() {
        return LotAtt12;
    }

    public void setLotAtt12(String lotAtt12) {
        LotAtt12 = lotAtt12;
    }

    public String getToLotAtt01() {
        return ToLotAtt01;
    }

    public void setToLotAtt01(String toLotAtt01) {
        ToLotAtt01 = toLotAtt01;
    }

    public String getToLotAtt02() {
        return ToLotAtt02;
    }

    public void setToLotAtt02(String toLotAtt02) {
        ToLotAtt02 = toLotAtt02;
    }

    public String getToLotAtt03() {
        return ToLotAtt03;
    }

    public void setToLotAtt03(String toLotAtt03) {
        ToLotAtt03 = toLotAtt03;
    }

    public String getToLotAtt04() {
        return ToLotAtt04;
    }

    public void setToLotAtt04(String toLotAtt04) {
        ToLotAtt04 = toLotAtt04;
    }

    public String getToLotAtt05() {
        return ToLotAtt05;
    }

    public void setToLotAtt05(String toLotAtt05) {
        ToLotAtt05 = toLotAtt05;
    }

    public String getToLotAtt06() {
        return ToLotAtt06;
    }

    public void setToLotAtt06(String toLotAtt06) {
        ToLotAtt06 = toLotAtt06;
    }

    public String getToLotAtt07() {
        return ToLotAtt07;
    }

    public void setToLotAtt07(String toLotAtt07) {
        ToLotAtt07 = toLotAtt07;
    }

    public String getToLotAtt08() {
        return ToLotAtt08;
    }

    public void setToLotAtt08(String toLotAtt08) {
        ToLotAtt08 = toLotAtt08;
    }

    public String getToLotAtt09() {
        return ToLotAtt09;
    }

    public void setToLotAtt09(String toLotAtt09) {
        ToLotAtt09 = toLotAtt09;
    }

    public String getToLotAtt10() {
        return ToLotAtt10;
    }

    public void setToLotAtt10(String toLotAtt10) {
        ToLotAtt10 = toLotAtt10;
    }

    public String getToLotAtt11() {
        return ToLotAtt11;
    }

    public void setToLotAtt11(String toLotAtt11) {
        ToLotAtt11 = toLotAtt11;
    }

    public String getToLotAtt12() {
        return ToLotAtt12;
    }

    public void setToLotAtt12(String toLotAtt12) {
        ToLotAtt12 = toLotAtt12;
    }

    public String getUserDefine1() {
        return UserDefine1;
    }

    public void setUserDefine1(String userDefine1) {
        UserDefine1 = userDefine1;
    }

    public String getUserDefine2() {
        return UserDefine2;
    }

    public void setUserDefine2(String userDefine2) {
        UserDefine2 = userDefine2;
    }

    public String getUserDefine3() {
        return UserDefine3;
    }

    public void setUserDefine3(String userDefine3) {
        UserDefine3 = userDefine3;
    }

    public String getUserDefine4() {
        return UserDefine4;
    }

    public void setUserDefine4(String userDefine4) {
        UserDefine4 = userDefine4;
    }

    public String getUserDefine5() {
        return UserDefine5;
    }

    public void setUserDefine5(String userDefine5) {
        UserDefine5 = userDefine5;
    }

    @Override
    public String toString() {
        return "WmsInventoryTransferDetailDto{" +
                "TDOCLineNo='" + TDOCLineNo + '\'' +
                ", TransferTime='" + TransferTime + '\'' +
                ", SKU='" + SKU + '\'' +
                ", TOQty=" + TOQty +
                ", LotAtt01='" + LotAtt01 + '\'' +
                ", LotAtt02='" + LotAtt02 + '\'' +
                ", LotAtt03='" + LotAtt03 + '\'' +
                ", LotAtt04='" + LotAtt04 + '\'' +
                ", LotAtt05='" + LotAtt05 + '\'' +
                ", LotAtt06='" + LotAtt06 + '\'' +
                ", LotAtt07='" + LotAtt07 + '\'' +
                ", LotAtt08='" + LotAtt08 + '\'' +
                ", LotAtt09='" + LotAtt09 + '\'' +
                ", LotAtt10='" + LotAtt10 + '\'' +
                ", LotAtt11='" + LotAtt11 + '\'' +
                ", LotAtt12='" + LotAtt12 + '\'' +
                ", ToLotAtt01='" + ToLotAtt01 + '\'' +
                ", ToLotAtt02='" + ToLotAtt02 + '\'' +
                ", ToLotAtt03='" + ToLotAtt03 + '\'' +
                ", ToLotAtt04='" + ToLotAtt04 + '\'' +
                ", ToLotAtt05='" + ToLotAtt05 + '\'' +
                ", ToLotAtt06='" + ToLotAtt06 + '\'' +
                ", ToLotAtt07='" + ToLotAtt07 + '\'' +
                ", ToLotAtt08='" + ToLotAtt08 + '\'' +
                ", ToLotAtt09='" + ToLotAtt09 + '\'' +
                ", ToLotAtt10='" + ToLotAtt10 + '\'' +
                ", ToLotAtt11='" + ToLotAtt11 + '\'' +
                ", ToLotAtt12='" + ToLotAtt12 + '\'' +
                ", UserDefine1='" + UserDefine1 + '\'' +
                ", UserDefine2='" + UserDefine2 + '\'' +
                ", UserDefine3='" + UserDefine3 + '\'' +
                ", UserDefine4='" + UserDefine4 + '\'' +
                ", UserDefine5='" + UserDefine5 + '\'' +
                '}';
    }
}
