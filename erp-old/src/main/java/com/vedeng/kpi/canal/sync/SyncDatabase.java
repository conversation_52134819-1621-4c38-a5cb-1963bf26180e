package com.vedeng.kpi.canal.sync;

import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.Message;
import com.vedeng.kpi.replica.dao.DynamicSqlExecuteMapper;
import com.vedeng.kpi.service.KpiCalculateService;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.lang3.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.BadSqlGrammarException;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 同步数据库，分为同步结构，同步数据
 * <AUTHOR>
 * @date created in 2020/6/10 17:17
 */
@Component
public class SyncDatabase {

    private static final Logger logger = LoggerFactory.getLogger(SyncDatabase.class);

    @Resource
    private DynamicSqlExecuteMapper dynamicSqlExecuteMapper;

    @Resource
    private KpiCalculateService kpiCalculateService;

    @Value("${canal.replica.database}")
    private String replicaDatabase;

    /**
     * mysql的数据类型，12：varchar，93：datetime
     */
    private final List<Integer> specialMysqlType = Arrays.asList(12,93);

    @Transactional(value = "canalTransactionManager", rollbackFor = Exception.class)
    public String receiveCanalEntries(Message message){
        logger.info("receiveCanalEntries start");
        for (CanalEntry.Entry entry : message.getEntries()){
            logger.info("receiveCanalEntries entry start:{}",new Object[]{entry});
            CanalEntry.RowChange rowChange;
            try {
                rowChange = CanalEntry.RowChange.parseFrom(entry.getStoreValue());
            } catch (Exception e) {
                logger.error("转换Canal数据：{}，发生错误：",entry.toString(),e);
                return ExceptionUtils.getFullStackTrace(e);
            }
            if (rowChange.getIsDdl() || rowChange.getSql().startsWith("ALTER")){
                //替换database name
                logger.info("canal同步DDL：{}",rowChange.getSql().replace(rowChange.getDdlSchemaName(),replicaDatabase));
                try {
                    dynamicSqlExecuteMapper.executeSql(rowChange.getSql().replace(rowChange.getDdlSchemaName(),replicaDatabase));
                } catch (BadSqlGrammarException e){
                    logger.error("canal同步DDL：{}，错误信息：",rowChange.getSql().replace(rowChange.getDdlSchemaName(),replicaDatabase),e);
                }
            } else {
                logger.info("receiveCanalEntries entry dataAnalysis");
                dataAnalysis(rowChange,entry.getHeader().getTableName(),entry.getHeader().getExecuteTime());
            }
        }
        return "";
    }


    public void dataAnalysis(CanalEntry.RowChange rowChange, String tableName, Long executeTime){
        logger.info("rowChange:{},tableName:{},executeTime:{}",new Object[]{rowChange,tableName,executeTime});
        for (CanalEntry.RowData rowData : rowChange.getRowDatasList()){
            Map<String, String> columnValueMap = new HashMap<>();
            CanalEntry.Column firstColumn;
            String condition;
            switch (rowChange.getEventType()){
                case DELETE:
                    firstColumn = rowData.getBeforeColumns(0);
                    condition = firstColumn.getName() + "=" + firstColumn.getValue();
                    dynamicSqlExecuteMapper.executeSql(DynamicSql.deleteSql(tableName,condition));
                    break;
                case INSERT:
                    List<String> columnNames = new ArrayList<>();
                    List<String> values = new ArrayList<>();
                    rowData.getAfterColumnsList()
                            .forEach(item -> {
                                if (item.getUpdated() && StringUtils.isNotBlank(item.getValue())){
                                    columnNames.add(item.getName());
                                    if (specialMysqlType.contains(item.getSqlType())){
                                        //对包含单引号的字符串进行转义
                                        String valueString = item.getValue().replace("\\","").replace("'","\\'");
                                        values.add("'" + valueString + "'");
                                    }else {
                                        values.add(item.getValue());
                                    }
                                    columnValueMap.put(item.getName(),item.getValue());
                                }
                            });
                    //幂等操作
                    firstColumn = rowData.getAfterColumns(0);
                    int insertSize = dynamicSqlExecuteMapper.selectCountById(DynamicSql.selectByIdSql(tableName,firstColumn.getName(),firstColumn.getValue()));
                    if (insertSize == 0) {
                        dynamicSqlExecuteMapper.executeSql(DynamicSql.insertSql(tableName,columnNames,values));
                        break;
                    }
                case UPDATE:
                    firstColumn = rowData.getAfterColumns(0);
                    condition = firstColumn.getName() + "=" + firstColumn.getValue();
                    columnValueMap.put(firstColumn.getName(),firstColumn.getValue());
                    List<String> modifiedSets = new ArrayList<>();
                    rowData.getAfterColumnsList()
                            .forEach(item -> {
                                if (item.getUpdated()){
                                    columnValueMap.put(item.getName(),item.getValue());
                                    if (StringUtils.isBlank(item.getValue())) {
                                        modifiedSets.add(item.getName() + "=" + "null");
                                    } else {
                                        if (specialMysqlType.contains(item.getSqlType())){
                                            String valueString = item.getValue().replace("\\","").replace("'","\\'");
                                            modifiedSets.add(item.getName() + "='" + valueString + "'");
                                        } else {
                                            modifiedSets.add(item.getName() + "=" + item.getValue());
                                        }
                                    }
                                }
                            });
                    if (modifiedSets.size() > 0) {
                        String[] setArray = modifiedSets.toArray(new String[modifiedSets.size()]);
                        dynamicSqlExecuteMapper.executeSql(DynamicSql.updateSql(tableName,setArray,condition));
                    }
                default:
                    break;
            }

            kpiCalculateService.monitorDataChange(rowChange,tableName,columnValueMap,executeTime);
        }
    }
}
