package com.wms.service.processor.input;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorder.service.BuyorderInfoSyncService;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.service.warningtask.ExpeditingTicketsService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.util.WmsCommonUtil;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 采购入库 处理器
 */
@Service
public class PurchaseInputOrderProcessor extends AbstractInputOrderProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(PurchaseInputOrderProcessor.class);

    @Autowired
    @Qualifier("inputOrderAuditPassCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Autowired
    private ExpeditingTicketsService expeditingTicketsService;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private BuyorderInfoSyncService buyorderInfoSyncService;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    /**
     * 通用的参数校验 校验失败,直接抛错
     * @param inputOrderDto
     */
    @Override
    public void commonValidator(InputOrderDto inputOrderDto) throws Exception {

        if(!WmsInterfaceOrderType.INPUT_PURCHASE.equals(inputOrderDto.getASNType())){
            throw new Exception("入库单:"+inputOrderDto.getASNReference1()+"的类型错误!");
        }
    }

    /**
     * 更新订单数据
     * @param inputOrderDto
     */
    @Override
    protected void updateOrderData(InputOrderDto inputOrderDto) {

        long currentTimeMillis = System.currentTimeMillis();

        String buyOrderNo = WmsCommonUtil.getOriginalOrderNo(inputOrderDto.getASNReference1());

        List<InputOrderGoodsDto> inputOrderGoodsList = inputOrderDto.getDetails();

        BuyorderVo buyOrder = buyorderMapper.getBuyorderVoByBuyorderNo(buyOrderNo);

        List<BuyorderGoodsVo> buyorderGoodList = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyOrder.getBuyorderId());

//        ThreadLocalContext.put("buyorderGoodList",buyorderGoodList);

        boolean isAllRecieve = true;

        for(BuyorderGoodsVo buyorderGood : buyorderGoodList) {

            int receievedNum = inputOrderGoodsList.stream().filter(g -> g.getSKU().equals(buyorderGood.getSku()))
                    .mapToInt(g -> g.getReceivedQty().intValue())
                    .sum();

            LOGGER.info("采购单商品"+buyorderGood.getBuyorderGoodsId()+"入库，本次收货数量:" + receievedNum);
            //原有数量减去售后数量
            int realNum = buyorderGood.getNum()- NumberUtils.toInt(buyorderGood.getAfterReturnNum()+"");
            //现有到货数量+本次到货数量
            int alreadyArrivalNum = buyorderGood.getArrivalNum() + receievedNum;

            //本次收货数量
            if(receievedNum != 0){

                BuyorderGoods buyorderGoodUpdate = new BuyorderGoodsVo();
                buyorderGoodUpdate.setBuyorderGoodsId(buyorderGood.getBuyorderGoodsId());
                buyorderGoodUpdate.setArrivalNum(alreadyArrivalNum);
                buyorderGoodUpdate.setArrivalTime(currentTimeMillis);
                buyorderGoodUpdate.setArrivalStatus(alreadyArrivalNum == realNum ? 2 : 1);
                buyorderGoodUpdate.setModTime(currentTimeMillis);

                LOGGER.info("采购单入库，更新入库单商品的数据:" + JSON.toJSONString(buyorderGoodUpdate));

                buyorderGoodsMapper.updateByPrimaryKeySelective(buyorderGoodUpdate);
                //采购单总金额大于0且不为未付款才能生成催票任务
                logger.info("采购单催票任务判断，采购单信息：{}",JSON.toJSONString(buyOrder));
                if(buyOrder.getTotalAmount().compareTo(new BigDecimal(0))>0
                        && !BuyorderVo.NOT_PAYMENT.equals(buyOrder.getPaymentStatus())){
                    generateEarlyWarningTask(buyorderGood.getBuyorderGoodsId(),receievedNum);
                }
            }

            //未全部收货
            if(alreadyArrivalNum != realNum){
                isAllRecieve = false;
            }
        }

        Buyorder buyorderUpdate = new Buyorder();
        buyorderUpdate.setBuyorderId(buyOrder.getBuyorderId());
        buyorderUpdate.setModTime(currentTimeMillis);
        buyorderUpdate.setArrivalStatus(isAllRecieve? 2 : 1);
        buyorderUpdate.setArrivalTime(currentTimeMillis);


        LOGGER.info("采购单入库，更新入库单的数据:" + JSON.toJSONString(buyorderUpdate));
        this.buyorderMapper.updateByPrimaryKeySelective(buyorderUpdate);

        //更新采购单中特殊商品的 收货状态为已收货
        buyorderGoodsMapper.updateGoodArrivalStatus(buyOrder.getBuyorderId());

        // VDERP-8759 订单流发货状态同步
        buyorderInfoSyncService.syncDeliveryStatus(buyOrder.getBuyorderId(), 1);
        LOGGER.info("订单流-发货状态同步操作");

        buyorderService.saveBuyorderInvoiceStatus(buyOrder.getBuyorderId());

        //更新赠品单状态
        buyorderService.updateGiftOrderStatus(buyOrder.getBuyorderId());


        // 更新普发采购单关联的直属费用单的收发货状态
        BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyOrder.getBuyorderId());
        if (Objects.nonNull(buyorderExpenseDto)) {
            buyorderExpenseApiService.doArrivalStatus(buyorderExpenseDto.getBuyorderExpenseId());
            buyorderExpenseApiService.doDeliveryStatus(buyorderExpenseDto.getBuyorderExpenseId());
            saleOrderGoodsApiService.dosaleDeliveryStatus(buyorderExpenseDto.getBuyorderExpenseId());
            saleOrderGoodsApiService.dosaleArrivalStatus(buyorderExpenseDto.getBuyorderExpenseId());
            LOGGER.info("普发采购入库同步更新直属费用单的收发货状态，采购费用单：{}", JSON.toJSONString(buyorderExpenseDto));
            List<Integer> saleOrderIds = rBuyorderExpenseJSaleorderService.findSaleOrderIds(buyorderExpenseDto.getBuyorderExpenseId());
            if (saleOrderIds.size()>0){
                for (Integer saleOrderId : saleOrderIds) {
                    LOGGER.info("开始校验销售单收发货状态,销售单Id：{}",saleOrderId);
                    saleOrderApiService.checkSaleorderDeliveryAndArrivalStatus(saleOrderId);
                }
            }
        }
    }
    private void generateEarlyWarningTask(Integer buyorderGoodsId,int receievedNum) {
        List<SysOptionDefinition> dictionaryByParentId = sysOptionDefinitionMapper.getDictionaryByParentId(ErpConst.SPECIALGOODS_SYS_PARENT_ID);
        if(fillteTicketsCondition(buyorderGoodsId,dictionaryByParentId)) {
            EarlyWarningTaskDto earlyWarningTaskDto = new EarlyWarningTaskDto();
            earlyWarningTaskDto.setRelateBusinessId(buyorderGoodsId);
            earlyWarningTaskDto.setUrgingTicketNum(receievedNum);
            logger.info("采购满足退票任务：{}",JSON.toJSONString(earlyWarningTaskDto));
            expeditingTicketsService.create(earlyWarningTaskDto);
        }
    }

    private boolean fillteTicketsCondition(Integer buyorderGoodsId, List<SysOptionDefinition> dictionaryByParentId) {
        BuyorderGoods buyorderGoodsInfo = buyorderGoodsMapper.getbuyorderGoodsInfoByBuyorderGoodsId(buyorderGoodsId);
        if(buyorderGoodsInfo.getPrice() == null || buyorderGoodsInfo.getPrice().compareTo(BigDecimal.ZERO) <= 0){
            return false;
        }
        if(CollectionUtils.isNotEmpty(dictionaryByParentId)){
            Optional<SysOptionDefinition> any = dictionaryByParentId.stream().filter(e -> e.getComments().equals(buyorderGoodsInfo.getGoodsId().toString())).findAny();
            if(any.isPresent()){
                return false;
            }
        }
        return true;
    }

    @Override
    protected int getOperateType(InputOrderDto inputOrderDto) {
        return StockOperateTypeConst.WAREHOUSE_IN;
    }

    @Override
    protected int getWmsLogicalOperateType(InputOrderDto inputOrderDto) {
        return WmsLogicalOperateTypeEnum.BUYORDER_TYPE.getOperateTypeCode();
    }

    public int getRelateId(InputOrderDto orderDto,InputOrderGoodsDto goodsDto){

        return Integer.valueOf(goodsDto.getUserDefine1());

    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 1;
    }

}
