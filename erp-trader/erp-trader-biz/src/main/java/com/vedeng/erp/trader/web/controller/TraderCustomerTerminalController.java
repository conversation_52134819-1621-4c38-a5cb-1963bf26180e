package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * <AUTHOR>
 * @description
 * @date 2023/8/10 15:45
 **/
@RequestMapping("/traderCustomerTerminal")
@Controller
public class TraderCustomerTerminalController  extends BaseController {

    @RequestMapping(value = "/queryView")
    @NoNeedAccessAuthorization
    public ModelAndView terminalView(String query) {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/customer_terminal");
        mv.addObject("query",query);
        return mv;
    }

    @RequestMapping(value = "/help")
    @NoNeedAccessAuthorization
    public ModelAndView help() {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/help");
        return mv;
    }
}
