package com.vedeng.trader.dao;

import com.vedeng.trader.model.dto.*;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * 客户账期信用记录
 */
@Named("traderCreditMapper")
public interface TraderCreditMapper {
    /**
     * 检索账期列表
     *
     * @param map
     * @return
     */
    List<CustomerBillPeriodItemDto> getTraderCreditViewListPage(Map<String, Object> map);

    /**
     * 获取客户账期的可用余额
     *
     * @param periodIds
     * @return
     */
    List<CustomerBillPeriodUsableAmountDto> getCreditUsableAmountByPeriodIds(@Param("periodIds") List<Long> periodIds);

    /**
     * 返回使用记录被风控的金额
     *
     * @param detailIds
     * @return
     */
    List<CustomerBillPeriodRiskAmountDto> getRiskAmountByDetailIds(@Param("detailIds") List<Long> detailIds);

    /**
     * 返回使用记录被监管到的金额
     *
     * @param detailIds
     * @return
     */
    List<CustomerBillPeriodSuperViseAmountDto> getSuperViseAmountByDetailIds(@Param("detailIds") List<Long> detailIds);

    /**
     * 检索账期申请列表
     *
     * @param map
     * @return
     */
    List<CustomerBillPeriodApplyItemDto> getCustomerBillPeriodApplyListPage(Map<String, Object> map);

    /**
     * 计算账期统计金额
     *
     * @param map
     * @return
     */
    PeriodRecordStatementDto getPeriodRecordStatement(Map<String, Object> map);

    /**
     * 计算账期订单统计信息
     *
     * @param map
     * @return
     */
    PeriodOrderStatementDto getPeriodOrderStatement(Map<String, Object> map);

    /**
     * @description: 获取该创建人的账期申请
     * @return:
     * @author: Strange
     * @date: 2021/9/8
     **/
    List<Long> getCustomerBIllPeriodApplyByCreator(@Param("checkStatus") Integer checkStatus, @Param("creatorId") Integer creatorId);
}
