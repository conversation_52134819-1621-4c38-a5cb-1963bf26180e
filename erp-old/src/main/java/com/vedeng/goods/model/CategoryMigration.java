package com.vedeng.goods.model;

import com.vedeng.trader.group.deal.DealTimesBehavior;

public class CategoryMigration {
    /**
     * 三级分类迁移表Id
     */
    private Integer categoryMigrationId;
    /**
     * 迁移的分类id
     */
    private Integer categoryId;
    /**
     * 迁移的分类名称
     */
    private String categoryName;
    /**
     * 原始分类路径
     */
    private String originPath;
    /**
     * 目标分类路径
     */
    private String targetPath;
    /**
     * 原始父级id
     */
    private String originParentId;
    /**
     * 目标父级id
     */
    private String targetParentId;
    /**
     * 迁移原因
     */
    private String migrationReason;
    /**
     * 创建时间
     */
    private Long addTime;
    /**
     * 创建者
     */
    private Integer creator;

    private Long updateTime;

    private Integer updater;

    private Integer isDeleted;

    public Integer getCategoryMigrationId() {
        return categoryMigrationId;
    }

    public void setCategoryMigrationId(Integer categoryMigrationId) {
        this.categoryMigrationId = categoryMigrationId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getOriginPath() {
        return originPath;
    }

    public void setOriginPath(String originPath) {
        this.originPath = originPath;
    }

    public String getTargetPath() {
        return targetPath;
    }

    public void setTargetPath(String targetPath) {
        this.targetPath = targetPath;
    }

    public String getOriginParentId() {
        return originParentId;
    }

    public void setOriginParentId(String originParentId) {
        this.originParentId = originParentId;
    }

    public String getTargetParentId() {
        return targetParentId;
    }

    public void setTargetParentId(String targetParentId) {
        this.targetParentId = targetParentId;
    }

    public String getMigrationReason() {
        return migrationReason;
    }

    public void setMigrationReason(String migrationReason) {
        this.migrationReason = migrationReason;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}
