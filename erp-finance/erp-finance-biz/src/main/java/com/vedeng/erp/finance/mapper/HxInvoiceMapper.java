package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.dto.HxInvoiceDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;

@Named("newHxInvoiceMapper")
public interface HxInvoiceMapper {

    /**
     * 主键获取已录信息(迁移old)
     * @param hxInvoiceId
     * @return
     */
    HxInvoiceDto getHxInvoiceRecordInfoByHxInvoiceId(Integer hxInvoiceId);

    /**
     * 保存更新航信的发票状态信息（迁移old）
     * @param hxInvoiceId
     * @param invoiceStatus
     * @return
     * @Author:hugo
     * @date:2020/5/26
     */
    Integer saveHxInvoiceStatus(@Param("hxInvoiceId") Integer hxInvoiceId, @Param("invoiceStatus") Integer invoiceStatus);

    /**
     * 根据发票号和发票代码查询可录票金额
     * <AUTHOR> @param invoiceNo
     * @param invoiceCode
     * @return
     */
    BigDecimal queryLeftAmountByInvoiceNo(@Param("invoiceNo")String invoiceNo,@Param("invoiceCode")String invoiceCode);

    /**
     * 条件获取最近的航信发票
     * @param invoiceNo
     * @param invoiceCode
     * @return
     */
    HxInvoiceDto getRecentHxInvoiceDtoByNo(@Param("invoiceNo") String invoiceNo, @Param("invoiceCode") String invoiceCode);
}
