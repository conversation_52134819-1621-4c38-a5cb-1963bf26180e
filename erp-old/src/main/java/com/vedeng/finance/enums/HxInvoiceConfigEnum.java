package com.vedeng.finance.enums;

/**
 * <AUTHOR>
 * @description:
 * @date 2021/7/12 9:14
 */
public enum HxInvoiceConfigEnum {

    /**
     * 字符剔除
     */
    CHARACTER(1,"字符剔除"),

    /**
     * 开户行
     */
    BANK(2,"开户行"),

    /**
     * 账户
     */
    ACCOUNT(3,"账户"),

    /**
     * 注册地址
     */
    ADDRESS(4,"注册地址"),

    /**
     * 注册电话
     */
    PHONE(5,"注册电话");

    private Integer type;

    private String content;

    HxInvoiceConfigEnum(Integer type, String content) {
        this.type = type;
        this.content = content;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}
