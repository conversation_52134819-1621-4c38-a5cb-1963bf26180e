package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeBusinessChanceFeedBack;
import com.vedeng.erp.buyorder.dto.GeExamineFeedBackDto;
import io.swagger.models.auth.In;
import org.apache.ibatis.annotations.Param;

public interface GeBusinessChanceFeedBackMapper {
    int deleteByPrimaryKey(Integer geBussinessChanceFeedbackId);

    int insert(GeBusinessChanceFeedBack record);

    int insertSelective(GeBusinessChanceFeedBack record);

    GeBusinessChanceFeedBack selectByPrimaryKey(Integer geBussinessChanceFeedbackId);

    int updateByPrimaryKeySelective(GeBusinessChanceFeedBack record);

    int updateByPrimaryKey(GeBusinessChanceFeedBack record);

    GeExamineFeedBackDto queryFednBackInfo(@Param("geBussinessChanceId") Integer geBussinessChanceId);

    /**
     * <AUTHOR>
     * @desc 根据GE商机id查询GE商机反馈
     * @param geBussinessChanceId
     * @return
     */
    GeBusinessChanceFeedBack queryByGeBussinessChanceId(Integer geBussinessChanceId);

    void updateByGeBussinessChanceId(GeBusinessChanceFeedBack geBusinessChanceFeedBack);

}