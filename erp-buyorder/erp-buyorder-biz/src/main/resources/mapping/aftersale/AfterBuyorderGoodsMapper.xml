<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterBuyorderGoodsMapper" >

    <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesGoodsEntity">
        <!--@mbg.generated-->
        <!--@Table T_AFTER_SALES_GOODS-->
        <id column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
        <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
        <result column="ORDER_DETAIL_ID" jdbcType="INTEGER" property="orderDetailId" />
        <result column="GOODS_TYPE" jdbcType="INTEGER" property="goodsType" />
        <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
        <result column="NUM" jdbcType="INTEGER" property="num" />
        <result column="PRICE" jdbcType="DECIMAL" property="price" />
        <result column="DELIVERY_DIRECT" jdbcType="INTEGER" property="deliveryDirect" />
        <result column="ARRIVAL_NUM" jdbcType="INTEGER" property="arrivalNum" />
        <result column="ARRIVAL_TIME" jdbcType="BIGINT" property="arrivalTime" />
        <result column="ARRIVAL_USER_ID" jdbcType="INTEGER" property="arrivalUserId" />
        <result column="ARRIVAL_STATUS" jdbcType="INTEGER" property="arrivalStatus" />
        <result column="DELIVERY_NUM" jdbcType="INTEGER" property="deliveryNum" />
        <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
        <result column="DELIVERY_TIME" jdbcType="BIGINT" property="deliveryTime" />
        <result column="SKU_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuRefundAmount" />
        <result column="SKU_OLD_REFUND_AMOUNT" jdbcType="DECIMAL" property="skuOldRefundAmount" />
        <result column="IS_ACTION_GOODS" jdbcType="INTEGER" property="isActionGoods" />
        <result column="UPDATE_DATA_TIME" jdbcType="TIMESTAMP" property="updateDataTime" />
        <result column="FACTORY_CODE" jdbcType="VARCHAR" property="factoryCode" />
        <result column="GOOD_CREATE_TIME" jdbcType="BIGINT" property="goodCreateTime" />
        <result column="GOOD_VAILD_TIME" jdbcType="BIGINT" property="goodVaildTime" />
        <result column="RKNUM" jdbcType="INTEGER" property="rknum" />
        <result column="AFTER_INVOICE_NUM" jdbcType="DECIMAL" property="afterInvoiceNum" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        AFTER_SALES_GOODS_ID, AFTER_SALES_ID, ORDER_DETAIL_ID, GOODS_TYPE, GOODS_ID, NUM,
        PRICE, DELIVERY_DIRECT, ARRIVAL_NUM, ARRIVAL_TIME, ARRIVAL_USER_ID, ARRIVAL_STATUS,
        DELIVERY_NUM, DELIVERY_STATUS, DELIVERY_TIME, SKU_REFUND_AMOUNT, SKU_OLD_REFUND_AMOUNT,
        IS_ACTION_GOODS, UPDATE_DATA_TIME, FACTORY_CODE, GOOD_CREATE_TIME, GOOD_VAILD_TIME,
        RKNUM, AFTER_INVOICE_NUM
    </sql>

    <select id="queryAfterBuyorderGoodsById" resultType="com.vedeng.erp.aftersale.dto.AfterBuyorderGoodsDto">
        SELECT
            A.AFTER_SALES_GOODS_ID,A.AFTER_SALES_ID,A.ORDER_DETAIL_ID,
            A.GOODS_ID,A.AFTER_INVOICE_NUM,B.SKU,B.GOODS_NAME AS skuName,
            B.BRAND_NAME,B.MODEL,B.UNIT_NAME,B.PRICE AS buyPrice,(B.NUM - B.AFTER_RETURN_NUM) AS buyNum,C.MATERIAL_CODE
        FROM T_AFTER_SALES_GOODS A
        LEFT JOIN T_BUYORDER_GOODS B ON A.ORDER_DETAIL_ID = B.BUYORDER_GOODS_ID
        LEFT JOIN V_CORE_SKU C ON B.GOODS_ID = C.SKU_ID
        WHERE A.AFTER_SALES_ID = #{aftersalesId,jdbcType=INTEGER}
        AND A.GOODS_TYPE = 0
    </select>

    <select id="getAfterGoodsIdByCondition" resultType="java.lang.Integer">
        SELECT ASG.AFTER_SALES_GOODS_ID
        FROM T_BUYORDER_GOODS BG
                 LEFT JOIN T_BUYORDER TB ON BG.BUYORDER_ID = TB.BUYORDER_ID
                 LEFT JOIN T_AFTER_SALES TAS ON TB.BUYORDER_ID = TAS.ORDER_ID
                 LEFT JOIN T_AFTER_SALES_GOODS ASG ON TAS.AFTER_SALES_ID = ASG.AFTER_SALES_ID
            AND ASG.ORDER_DETAIL_ID = BG.BUYORDER_GOODS_ID
        WHERE BG.BUYORDER_GOODS_ID = #{buyOrderGoodsId,jdbcType=INTEGER}
          AND TAS.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
            LIMIT 1
    </select>

    <select id="findAllByAfterSalesId" resultType = "com.vedeng.erp.aftersale.domain.entity.BuyOrderAfterSalesGoodsEntity">
        select
        a.AFTER_SALES_GOODS_ID
        , a.AFTER_SALES_ID
        , a.ORDER_DETAIL_ID
        , a.GOODS_TYPE
        , a.GOODS_ID
        , a.NUM
        , a.PRICE
        , a.DELIVERY_DIRECT
        , a.ARRIVAL_NUM
        , a.ARRIVAL_TIME
        , a.ARRIVAL_USER_ID
        , a.ARRIVAL_STATUS
        , a.DELIVERY_NUM
        , a.DELIVERY_STATUS
        , a.DELIVERY_TIME
        , a.SKU_REFUND_AMOUNT
        , a.SKU_OLD_REFUND_AMOUNT
        , a.IS_ACTION_GOODS
        , a.UPDATE_DATA_TIME
        , a.FACTORY_CODE
        , a.GOOD_CREATE_TIME
        , a.GOOD_VAILD_TIME
        , a.RKNUM
        , a.AFTER_INVOICE_NUM
        , b.SKU_NAME as goodsName
        from T_AFTER_SALES_GOODS a left join V_CORE_SKU b on a.GOODS_ID = b.SKU_ID
        where a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        AND a.GOODS_TYPE = 0
    </select>
</mapper>