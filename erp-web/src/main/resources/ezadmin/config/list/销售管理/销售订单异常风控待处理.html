<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>销售订单异常风控待处理</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="YCb1lxlGWwU" datasource="erp-reportdatasource" fixednumber="2" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
     <li item_name=""><a href="/ezadmin/list/list-YCb1lxlGWwU" item_name="">待处理</a></li>
     <li item_name="已处理"><a href="/ezadmin/list/list-rA662FVSu_Y" item_name="已处理">已处理</a></li>
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">订单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="USERNAME" placeholder="" style="" alias="" jdbctype="" data="SELECT 	U.USERNAME AS 'K', 	lower(USERNAME) AS 'V'  FROM 	T_USER U 	JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID  	AND U.IS_DISABLED = 0 AND U.COMPANY_ID = 1 	JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID  WHERE 	P.TYPE = 310 order by U.USERNAME" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">销售部门</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="ORG_NAME" placeholder="" style="" alias="" jdbctype="" data="SELECT 	A.ORG_NAME AS 'K', 	A.ORG_NAME AS 'V'  FROM 	T_ORGANIZATION A 	LEFT JOIN T_POSITION B ON A.ORG_ID = B.ORG_ID  WHERE 	B.TYPE = 310  	AND A.COMPANY_ID = 1  GROUP BY 	A.ORG_ID" datatype="KVSQLCACHE" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">被风控时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="17" name="ADD_TIME" placeholder="" style="" alias="a" jdbctype="DATE" data="" datatype="" oper="BETWEEN" validate_rules="" validate_messages=""></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">被风控备注</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="COMMENT2" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;客户资质未审核通过&quot;,&quot;K&quot;:&quot;客户资质未审核通过&quot;},{&quot;V&quot;:&quot;商品信息不完整&quot;,&quot;K&quot;:&quot;商品信息不完整&quot;},{&quot;V&quot;:&quot;客户信息不完整&quot;,&quot;K&quot;:&quot;客户信息不完整&quot;},{&quot;V&quot;:&quot;供应商信息不完整&quot;,&quot;K&quot;:&quot;供应商信息不完整&quot;}]" datatype="JSON" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">被风控时间</th>
      <th item_name="COMMENT2" name="COMMENT2" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">被风控备注</th>
      <th item_name="OPERATE" name="OPERATE" body="td-link" order="0" url="/order/saleorder/view.do?saleorderId=${SALEORDER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">操作</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="" groupby="">select

a.SALEORDER_NO,
	a.TRADER_NAME,
	a.TRADER_ID,
	a.SALEORDER_ID,
	a.USERNAME,
	a.USER_ID,
	a.ORG_ID,
	a.ORG_NAME,
	a.ADD_TIME,
	a.COMMENTS,
	a.COMMENT2,
	a.URI,
	a.OPERATE
from (
SELECT
	S.SALEORDER_NO,
	T.TRADER_NAME,
	T.TRADER_ID,
	S.SALEORDER_ID,
	U.USERNAME,
	U.USER_ID,
	O.ORG_ID,
	O.ORG_NAME,
	FROM_UNIXTIME( L.ADD_TIME / 1000 ) ADD_TIME,
	GROUP_CONCAT( DISTINCT CASE L.RISK_CHECK_BUZ_TYPE WHEN 4 THEN '客户资质未审核通过' WHEN 5 THEN '客户信息不完整' WHEN 6 THEN '供应商信息不完整' WHEN 7 THEN '商品信息不完整' END ) COMMENTS,
	GROUP_CONCAT( DISTINCT CASE L.RISK_CHECK_BUZ_TYPE WHEN 4 THEN '客户资质未审核通过' WHEN 5 THEN '客户信息不完整' WHEN 6 THEN '供应商信息不完整' WHEN 7 THEN '商品信息不完整' END ) COMMENT2,
IF
	( S.ORDER_TYPE = 5, '/order/hc/hcOrderDetailsPage.do?saleorderId=', '/order/saleorder/view.do?saleorderId=' ) URI,
	'解除风控' OPERATE
FROM
	T_RISK_CHECK_LOG L
	LEFT JOIN T_SALEORDER S ON L.RISK_CHECK_BUZ_EXTRA = S.SALEORDER_NO
	AND L.RISK_CHECK_BUZ_PROPERTY = 'saleorder'
	JOIN T_TRADER T ON S.TRADER_ID = T.TRADER_ID
	JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID
	JOIN T_USER U ON TU.USER_ID = U.USER_ID
	JOIN T_R_USER_POSIT UP ON U.USER_ID = UP.USER_ID
	JOIN T_POSITION P ON UP.POSITION_ID = P.POSITION_ID
	JOIN T_ORGANIZATION O ON P.ORG_ID = O.ORG_ID
	GROUP BY L.RISK_CHECK_BUZ_EXTRA HAVING 	SUM( L.RISK_CHECK_TRIGGER_STATUS ) &lt; COUNT( * )
	) a
	where 1=1</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>