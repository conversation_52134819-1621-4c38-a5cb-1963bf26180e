package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-09-09
 * @Description: 票据详细信息查询(B2eNbsDraftDetail)响应实体
 * @Version: 1.0
 */
@Data
public class DraftDetailResponse extends BankResponse {
    private DraftDetailResBody xDataBody;

    @Data
    public static class DraftDetailResBody{
        /** 银行渠道交易流水号 */
        private String svrId;

        /** 客户技术请求流水号，同一客户请勿重复 */
        private String trnId;

        /** 正面信息 */
        private BillInfo billInfo;

    }

    /**
     * 正面信息
     */
    @Data
    public static class BillInfo {
        /** 票据编号 */
        private String billNo;

        /** 票据类型
         AC01：银票
         AC02：商票 */
        private String billType;

        /** 票据介质
         ME01：纸票
         ME02：电票 */
        private String billClass;

        /** 是否可分包
         0：否
         1：是 */
        private String isAllowSplitBill;

        /** 出票日 */
        private String remitDt;

        /** 票面到期日 */
        private String dueDt;

        /** 承兑日期 */
        private String acptDt;

        /** 票据（包）金额 */
        private String billMoney;

        /** 出票人名称 */
        private String drwrName;

        /** 出票人账号 */
        private String drwrAcctNo;

        /** 出票人账户名称 */
        private String drwrAcctName;

        /** 出票人开户行编号 */
        private String drwrBankNo;

        /** 出票人开户行名称 */
        private String drwrBankName;

        /** 收款人名称 */
        private String pyeeName;

        /** 收款人账号 */
        private String pyeeAcctNo;

        /** 收款人账户名称 */
        private String pyeeAcctName;

        /** 收款人开户行名称 */
        private String pyeeBankName;

        /** 收款人开户行编号 */
        private String pyeeBankNo;

        /** 承兑人全称 */
        private String acptName;
        /** 承兑人账号 */
        private String acptAcctNo;

        /** 承兑人账号名称 */
        private String acptAcctName;

        /** 承兑人开户行编号 */
        private String acptBankNo;

        /** 承兑人开户行名称 */
        private String acptBankName;

        /** 承兑人开户行类型:
         201：政策型银行
         202：国有商业银行
         203：股份制商业银行
         204：外资银行
         205：城市商业银行
         206：农商行和农合行
         207：村镇银行
         208：农村信用社
         301：财务公司 */
        private String acptBankType;

        /** 子票区间起始 */
        private String billRangeStart;

        /** 子票区间截止 */
        private String billRangeEnd;

        /** 交易金额 */
        private String transAmt;

        /** 不得转让标记:
         EM00：可再转
         EM01：不得转让 */
        private String hldrBanEndrsmtMark;

        /** 交易合同号 */
        private String transCtrctNo;

        /** 发票号 */
        private String invoiceNo;

        /** 出票人信用评级 */
        private String drwrCreditRatgs;

        /** 出票人评级机构 */
        private String drwrCdtRatgAgcy;

        /** 出票人评级到期日 */
        private String drwrRatgDuedt;

        /** 承兑人评级主体 */
        private String acptCdtRatgAgcy;

        /** 承兑人信用等级 */
        private String acptCdtRatgs;

        /** 承兑人评级到期日 */
        private String acptCdtRatgDueDt;

        /** 新一代票据状态
         已出票：CS01
         已承兑：CS02
         已收票：CS03
         已到期：CS04
         已终止：CS05
         已结清：CS06 */
        private String billStatus;

        /** 票据流通状态
         TF0101：待收票<>TF0301：可流通
         TF0302：已锁定
         TF0303：不可转让
         TF0304：已质押
         TF0305：待赎回
         TF0401：托收在途
         TF0402：追索中
         TF0501：已结束 */
        private String cirStatus;
    }
}
