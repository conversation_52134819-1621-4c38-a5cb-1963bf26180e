<%--
  Created by IntelliJ IDEA.
  User: dhs
  Date: 2021/10/22
  Time: 4:57 下午
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%--<script src="${pageContext.request.contextPath}/static/js/orderstream/index.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/orderstream/style.css">--%>
<div class="parts">
    <div class="title-container title-container-blue">
        <div class="table-title nobor">产品信息</div>
    </div>
    <table class="table  table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th <%--style="width:40px"--%>>序号</th>
            <th <%--style="width:400px"--%>>产品名称</th>
            <th <%--style="width:40px"--%>>数量</th>
            <th <%--style="width:300px"--%>>发货方式</th>
            <th <%--style="width:450px"--%>>产品备注</th>
            <th <%--style="width:300px"--%>>内部备注</th>
        </tr>
        </thead>
        <tbody>
        <c:set var="num" value="0"></c:set>
        <c:set var="totleMoney" value="0.00"></c:set>
        <c:set var="isNotDelPriceZero" value="0"></c:set>
        <c:forEach var="list" items="${saleorderGoodsList}" varStatus="staut">
            <c:if test="${list.isDelete eq 0}">
                <c:set var="num" value="${num + list.num}"></c:set>
                <c:set var="totleMoney" value="${totleMoney + (list.price * list.num)}"></c:set>
                <c:if test="${list.price == '0.00'}">
                    <c:set var="isNotDelPriceZero" value="1"></c:set>
                </c:if>
            </c:if>
            <tr <c:if test="${list.isDelete eq 1}">class="caozuo-grey"</c:if>>
                <td>${staut.count}</td>
                <td class="text-left">
                    <div class="customername pos_rel">
                        <c:choose>
                            <c:when test="${list.isDelete eq 1}">
                                <span>${list.goodsName}<br/></span>
                                <span>${list.sku} <br></span>
                            </c:when>
                            <c:otherwise>
                                <c:if test="${list.isGift == 1}"><img src="<%= basePath %>static/images/gift_icon.svg" style="width: 15px" /></c:if>
                                <%--<span class="font-blue"><a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>${list.goodsName}</a>&nbsp;<i class="iconbluemouth contorlIcon"></i><br/></span>--%>										<span>${list.goodsName}<i class="iconbluemouth contorlIcon"></i><br/></span>
                                <span>${list.sku} <br></span>
                                <c:set var="skuNo" value="${list.sku}"></c:set>
                                <%@ include file="../../common/new_sku_common_tip.jsp" %>
                            </c:otherwise>
                        </c:choose>
                    </div>
                </td>
                <td>
                    <c:set var="oldDeliveryDirect" value=""></c:set>
                    <c:set var="oldDeliveryDirectComments" value=""></c:set>
                    <c:set var="oldGoodsComments" value=""></c:set>
                    <c:set var="newDeliveryDirect" value=""></c:set>
                    <c:set var="newDeliveryDirectComments" value=""></c:set>
                    <c:set var="newGoodsComments" value=""></c:set>
                    <c:forEach var="modifyApplylist" items="${saleorderModifyApplyGoodsList}" varStatus="modifyStaut">
                        <c:if test="${modifyApplylist.saleorderGoodsId == list.saleorderGoodsId}">
                            <c:set var="oldDeliveryDirect" value="${modifyApplylist.oldDeliveryDirect}"></c:set>
                            <c:set var="oldDeliveryDirectComments" value="${modifyApplylist.oldDeliveryDirectComments}"></c:set>
                            <c:set var="oldGoodsComments" value="${modifyApplylist.oldGoodsComments}"></c:set>
                            <c:set var="newDeliveryDirect" value="${modifyApplylist.deliveryDirect}"></c:set>
                            <c:set var="newDeliveryDirectComments" value="${modifyApplylist.deliveryDirectComments}"></c:set>
                            <c:set var="newGoodsComments" value="${modifyApplylist.goodsComments}"></c:set>
                        </c:if>
                    </c:forEach>
                        ${list.num}
                </td>
                <td>

                    <div class="customername pos_rel">
	                                <span>
	                                <c:choose>
										<c:when test="${newDeliveryDirect eq 0}">普发
											<c:if test="${oldDeliveryDirect ne newDeliveryDirect}">
												<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：
                            <c:choose>
                                <c:when test="${oldDeliveryDirect eq 0}">否</c:when>
                                <c:otherwise>
                                    直发
                                </c:otherwise>
                            </c:choose>
                        </div>
                        </c:if>
                        </c:when>
                        <c:otherwise>
                            直发
                            <c:if test="${oldDeliveryDirect ne newDeliveryDirect}">
                                <i class="iconbluesigh ml4 contorlIcon"></i></span>
                                <div class="pos_abs customernameshow">原值：
                                    <c:choose>
                                        <c:when test="${oldDeliveryDirect eq 0}">普发</c:when>
                                        <c:otherwise>
                                            直发
                                        </c:otherwise>
                                    </c:choose>
                                </div>
                            </c:if>
                        </c:otherwise>
                        </c:choose>
                    </div>
                    <div class="customername pos_rel">
                                <span>${newDeliveryDirectComments}
                                <c:if test="${oldDeliveryDirectComments ne newDeliveryDirectComments}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldDeliveryDirectComments}</div>
                        </c:if>
                    </div>
                </td>
                <td>
                    <div class="customername pos_rel">
                                <span>${newGoodsComments}

                                <c:if test="${oldGoodsComments ne newGoodsComments}">
									<i class="iconbluesigh ml4 contorlIcon"></i></span>
                        <div class="pos_abs customernameshow">原值：${oldGoodsComments}</div>
                        </c:if>
                </td>
                <td>
                    <div  class="customername pos_rel">
                        <span>${list.insideComments} <br></span>
                        <c:if test="${list.componentHtml ne ''}">
                            <i class="iconbluemouth contorlIcon"></i>
                            <div class="pos_abs customernameshow" label_left="-300" style="width: 500px; top: 25px;background-color: #00CD66;">
                                    ${list.componentHtml}
                            </div>
                        </c:if>
                    </div>
                </td>
            </tr>
        </c:forEach>
        </tbody>
    </table>
</div>

