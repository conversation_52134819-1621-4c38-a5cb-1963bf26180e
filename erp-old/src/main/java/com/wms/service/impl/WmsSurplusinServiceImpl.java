package com.wms.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.outIn.OutInDetail;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dao.WmsInputOrderGoodsMapper;
import com.wms.dao.WmsInputOrderMapper;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.model.dto.WmsInputOrderGoodsDto;
import com.wms.model.dto.WmsSurplusInQueryDto;
import com.wms.model.po.WmsInputOrder;
import com.wms.model.po.WmsInputOrderGoods;
import com.wms.model.po.WmsSurplusInOrder;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.WmsSurplusinService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName WmsSurplusinServiceImpl.java
 * @Description TODO 盘盈入库业务层
 * @createTime 2020年09月21日 15:58:00
 */
@Service("wmsSurplusinService")
public class WmsSurplusinServiceImpl implements WmsSurplusinService {
    private static final Logger logger = LoggerFactory.getLogger(WmsSurplusinServiceImpl.class);

    @Resource
    private WmsInputOrderMapper wmsInputOrderMapper;

    @Resource
    private WmsInputOrderGoodsMapper wmsInputOrderGoodsMapper;

    @Resource
    private OrgService orgService;

    @Resource
    private UserService userService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private OrderNoDict orderNoDict;

    @Resource
    private GoodsMapper goodsMapper;

    @Override
    public List<WmsSurplusInOrder> querySurplusInlistPage(WmsSurplusInQueryDto wmsSurplusInQueryDto, Page page) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("wmsSurplusInQueryDto", wmsSurplusInQueryDto);
        map.put("page", page);
        return wmsInputOrderMapper.querySurplusInlistPage(map);
    }

    @Transactional
    @Override
    public ResultInfo saveSurplusInOrder(WmsInputOrder wmsInputOrder) {
        ResultInfo resultInfo = new ResultInfo();

        Organization organization = orgService.getOrgByOrgId(wmsInputOrder.getApplyerDepartmentId());
        wmsInputOrder.setApplyerDepartment(organization.getOrgName());
        User user = userService.getUserById(wmsInputOrder.getApplyerUserid());
        wmsInputOrder.setApplyer(user.getUsername());
        wmsInputOrder.setOrderType(1);
        wmsInputOrder.setVerifyStatus(1);
        int i = wmsInputOrderMapper.insertSelective(wmsInputOrder);

        if(i > 0){
            String orderNum = orderNoDict.getOrderNum(wmsInputOrder.getWmsInputOrderId().intValue(), OrderNoDict.SURPLUSIN_ORDER);
            WmsInputOrder updateorder = new WmsInputOrder();
            wmsInputOrder.setOrderNo(orderNum);
            updateorder.setOrderNo(orderNum);
            updateorder.setWmsInputOrderId(wmsInputOrder.getWmsInputOrderId());
            wmsInputOrderMapper.updateByPrimaryKeySelective(updateorder);

            List<WmsInputOrderGoods> wmsInputOrderGoods = wmsInputOrder.getWmsInputOrderGoods();
            for (WmsInputOrderGoods wmsInputOrderGood : wmsInputOrderGoods) {
                wmsInputOrderGood.setWmsInputOrderId(wmsInputOrder.getWmsInputOrderId());
                wmsInputOrderGood.setGoodsId(Integer.valueOf(wmsInputOrderGood.getSkuNo().substring(1)));
                wmsInputOrderGoodsMapper.insertSelective(wmsInputOrderGood);
            }
            resultInfo.setData(wmsInputOrder);
            resultInfo.setCode(0);
            resultInfo.setMessage("成功");
        }
        return resultInfo;
    }

    @Override
    public WmsInputOrder getSurplusInOrderById(Integer wmsInputOrderId) {

        return wmsInputOrderMapper.selectByPrimaryKey(wmsInputOrderId);
    }
    @Override
    public WmsInputOrder getSurplusInorderByNo(String surplusNo) {

        return wmsInputOrderMapper.getWmsInputOrderByNo(surplusNo);
    }

    @Override
    public List<WmsInputOrderGoods> getWmsInputOrderGoods(Integer wmsInputOrderId) {
        return wmsInputOrderGoodsMapper.getWmsInfoOrderGoodsByWmsInputOrderId(wmsInputOrderId);
    }

    @Override
    public List<WmsInputOrderGoodsDto> getWmsInputOrderGoodsDto(Integer wmsInputOrderId) {

        return wmsInputOrderGoodsMapper.getWmsInfoOrderGoodsDtoByWmsInputOrderId(wmsInputOrderId);
    }

    @Override
    public List<WarehouseGoodsOperateLog> getWarehouseLogListByOrderId(Integer wmsInputOrderId, Integer logOperateType) {

        return warehouseGoodsOperateLogMapper.getWmsInputOrderLogListByOrderId(wmsInputOrderId,logOperateType);
    }

    @Override
    public Integer updatesurplusInOrderAuditStatus(Integer wmsInputOrderId, int value) {
        WmsInputOrder wmsInputOrder = new WmsInputOrder();
        wmsInputOrder.setWmsInputOrderId(wmsInputOrderId);
        wmsInputOrder.setVerifyStatus(value);
        return wmsInputOrderMapper.updateByPrimaryKeySelective(wmsInputOrder);
    }

    @Override
    public void putWmsSurplusInOrder(Integer wmsInputOrderId) {
        try {
            WmsInputOrder wmsInputOrder = wmsInputOrderMapper.selectByPrimaryKey(wmsInputOrderId);

            PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

            putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_CHECK_MORE);
            putPurchaseOrderDto.setDocNo(wmsInputOrder.getOrderNo());
            putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(wmsInputOrder.getAddTime().getTime(),"yyyy-MM-dd HH:mm:ss"));
            putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            putPurchaseOrderDto.setSupplierId(0);
            putPurchaseOrderDto.setSupplierName("VEDENG");
            putPurchaseOrderDto.setPoReferenceA(wmsInputOrder.getApplyerDepartment());
            putPurchaseOrderDto.setPoReferenceB(wmsInputOrder.getApplyer());
            putPurchaseOrderDto.setNotes(wmsInputOrder.getRemark());

            List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();
            List<WmsInputOrderGoods> wmsInputOrderGoodsList = this.getWmsInputOrderGoods(wmsInputOrderId);
            for (WmsInputOrderGoods wmsInputOrderGoods : wmsInputOrderGoodsList) {
                PutPurchaseOrderGoodsDto orderGoodsDto = new PutPurchaseOrderGoodsDto();
                orderGoodsDto.setSku(wmsInputOrderGoods.getSkuNo());
                orderGoodsDto.setOrderedQty(wmsInputOrderGoods.getInputNum());
                orderGoodsDto.setDedi04(wmsInputOrderGoods.getWmsInputOrderGoodsId().toString());
                details.add(orderGoodsDto);
            }
            putPurchaseOrderDto.setDetails(details);
            if(CollectionUtils.isEmpty(details)){
                return;
            }
            logger.info("WMS盘盈入库单下传的 单号:{},请求:{}",wmsInputOrder.getOrderNo(), JSON.toJSONString(putPurchaseOrderDto));
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);
            WmsResponse response = wmsInterface.request(putPurchaseOrderDto);
            logger.info("WMS盘盈入库单下传的 单号:{},响应:{}",wmsInputOrder.getOrderNo(),JSON.toJSONString(response));
        }catch (Exception e){
            logger.error("putWmsSurplusInOrder error",e);
        }

    }


    @Override
    public List<OutInDetail> getOUtInDetailList(String orderNo, Integer outInType, Integer operateType) {
        List<OutInDetail> outInDetailList = warehouseGoodsOperateLogMapper.getOUtInDetailList(orderNo,outInType,operateType);
        if(!CollectionUtils.isEmpty(outInDetailList)){
            List<Integer> goodsIdList = outInDetailList.stream().map(OutInDetail::getGoodsId).collect(Collectors.toList());
            List<Map<String, Object>> maps = goodsMapper.skuTipList(goodsIdList);
            maps.forEach(sku->{
                String sku_id = sku.get("SKU_ID").toString();
                outInDetailList.stream().filter(o->sku_id.equals(o.getGoodsId().toString())).forEach(detail->{
                    detail.setSkuId(Integer.valueOf(sku_id));
                    detail.setSkuNo(sku.get("SKU_NO").toString());

                    if(Objects.nonNull(sku.get("SHOW_NAME"))){
                        detail.setSkuName(sku.get("SHOW_NAME").toString());
                    }
                    if(Objects.nonNull(sku.get("BRAND_NAME"))){
                        detail.setBrandName(sku.get("BRAND_NAME").toString());
                    }
                    if(Objects.nonNull(sku.get("SPEC"))){
                        detail.setSpec(sku.get("SPEC").toString());
                    }
                    if(Objects.nonNull(sku.get("MODEL"))){
                        detail.setModel(sku.get("MODEL").toString());
                    }
                    if(Objects.nonNull(sku.get("REGISTRATION_NUMBER"))){
                        detail.setRegistrationNumber(sku.get("REGISTRATION_NUMBER").toString());
                    }
                    if(Objects.nonNull(sku.get("UNIT_NAME"))){
                        detail.setUnitName(sku.get("UNIT_NAME").toString());
                    }
                    if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))){
                        detail.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
                    }
                });
            });
        }
        return outInDetailList;
    }
}
