package com.wms.model.dto;

import com.wms.model.po.WmsOutputOrderGoods;
import com.vedeng.erp.wms.dto.WmsSampleOrderGoodsExtra;

public class WmsOutputOrderGoodsDto extends WmsOutputOrderGoods {

    private Integer skuId;
    private String showName;

    private String skuName;

    private String brandName;

    private String model;

    private String unit;

    private Integer availableStockNum;

    private Integer stockNum;

    private Integer alreadyOutputNum;

    private Integer outStatus;

    private Integer outputNum;

    @Override
    public Integer getOutputNum() {
        return outputNum;
    }

    @Override
    public void setOutputNum(Integer outputNum) {
        this.outputNum = outputNum;
    }

    @Override
    public Integer getAlreadyOutputNum() {
        return alreadyOutputNum;
    }

    @Override
    public void setAlreadyOutputNum(Integer alreadyOutputNum) {
        this.alreadyOutputNum = alreadyOutputNum;
    }

    @Override
    public Integer getOutStatus() {
        return outStatus;
    }

    @Override
    public void setOutStatus(Integer outStatus) {
        this.outStatus = outStatus;
    }

    public Integer getAvailableStockNum() {
        return availableStockNum;
    }

    public void setAvailableStockNum(Integer availableStockNum) {
        this.availableStockNum = availableStockNum;
    }

    public Integer getStockNum() {
        return stockNum;
    }

    public void setStockNum(Integer stockNum) {
        this.stockNum = stockNum;
    }

    private WmsSampleOrderGoodsExtra wmsSampleOrderGoodsExtra;

    public WmsSampleOrderGoodsExtra getWmsSampleOrderGoodsExtra() {
        return wmsSampleOrderGoodsExtra;
    }

    public void setWmsSampleOrderGoodsExtra(WmsSampleOrderGoodsExtra wmsSampleOrderGoodsExtra) {
        this.wmsSampleOrderGoodsExtra = wmsSampleOrderGoodsExtra;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
