package com.vedeng.erp.buyorderexpense.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SpecialSalesEnum;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.dto.*;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.buyorderexpense.domain.entity.RBuyorderExpenseJSaleorderEntity;
import com.vedeng.erp.buyorderexpense.mapper.RBuyorderExpenseJSaleorderMapper;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.buyorderexpense.mapstruct.SaleOrderGoodsDetailConvertor;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.trader.dto.SpecialSalesDto;
import com.vedeng.erp.trader.service.SpecialSalesApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class RBuyorderExpenseJSaleorderServiceImpl implements RBuyorderExpenseJSaleorderService {

    @Resource
    private RBuyorderExpenseJSaleorderMapper rBuyorderExpenseJSaleorderMapper;
    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;
    @Autowired
    private SaleOrderGoodsDetailConvertor saleOrderGoodsDetailConvertor;
    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;
    @Autowired
    private SpecialSalesApiService specialSalesApiService;


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void addExpenseJ(BuyorderExpenseDto buyorderExpenseDto) {
        log.info("添加采销联动关系：采购单和销售单{}", JSON.toJSONString(buyorderExpenseDto));
        if (CollUtil.isNotEmpty(buyorderExpenseDto.getBuyorderExpenseItemDtos())) {
            buyorderExpenseDto.getBuyorderExpenseItemDtos().forEach(dto -> {
                if (CollUtil.isNotEmpty(dto.getBuyOrderSaleOrderGoodsDetailDtos())) {
                    dto.getBuyOrderSaleOrderGoodsDetailDtos().forEach(s -> {
                        this.checkNeedNum(s);
                        RBuyorderExpenseJSaleorderEntity rbs = new RBuyorderExpenseJSaleorderEntity();
                        rbs.setSaleorderId(s.getSaleorderId());
                        rbs.setSaleorderGoodsId(s.getSaleorderGoodsId());
                        rbs.setBuyorderExpenseId(buyorderExpenseDto.getBuyorderExpenseId());
                        rbs.setBuyorderExpenseItemId(dto.getBuyorderExpenseItemId());
                        rbs.setSkuId(dto.getGoodsId());
                        rbs.setSkuNo(dto.getBuyorderExpenseItemDetailDto().getSku());
                        rbs.setNum(s.getBuyNum());
                        rBuyorderExpenseJSaleorderMapper.insertSelective(rbs);
                    });
                }
            });
        }

        log.info("采购费用单创建时，判断销售单中是否含有特麦帮订单, buyorderExpenseDto:{}", JSON.toJSONString(buyorderExpenseDto));

        List<Integer> saleorderIdList = buyorderExpenseDto.getBuyorderExpenseItemDtos()
                .stream()
                .flatMap(itemDto -> {
                    if (Objects.nonNull(itemDto) && CollUtil.isNotEmpty(itemDto.getBuyOrderSaleOrderGoodsDetailDtos())) {
                        return itemDto.getBuyOrderSaleOrderGoodsDetailDtos().stream();
                    } else {
                        return Stream.empty();
                    }
                })
                .filter(Objects::nonNull)
                .map(BuyOrderSaleOrderGoodsDetailDto::getSaleorderId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());


        if (CollectionUtils.isNotEmpty(saleorderIdList)) {
            log.info("采购费用单创建时，判断销售单中是否含有特麦帮订单，saleorderIdList:{}", saleorderIdList);
            List<SpecialSalesDto> specialSalesEntityList = specialSalesApiService.findByRelateIdInAndRelateTypeAndIsDelete(saleorderIdList, SpecialSalesEnum.SALEORDER.getCode(), ErpConst.ZERO);
            if (CollectionUtils.isNotEmpty(specialSalesEntityList)) {
                log.info("采购费用单创建时，销售单中含有特麦帮订单，specialSalesList:{}", JSON.toJSONString(specialSalesEntityList));
                CurrentUser user = CurrentUser.getCurrentUser();
                SpecialSalesDto specialSalesDto = SpecialSalesDto.builder()
                        .relateId(buyorderExpenseDto.getBuyorderExpenseId())
                        .relateType(SpecialSalesEnum.BUYORDER_EXPENSE.getCode())
                        .isDelete(ErpConst.ZERO)
                        .creator(user.getId())
                        .creatorName(user.getUsername())
                        .updater(user.getId())
                        .updaterName(user.getUsername())
                        .build();
                specialSalesApiService.insertSpecialSales(specialSalesDto);
                log.info("采购费用单创建时，销售单中含有特麦帮订单，新增采购费用订单特麦帮关联信息成功，specialSales:{}", JSON.toJSONString(specialSalesDto));
            }
        }

    }

    /**
     * 判断需采数量是否满足采购数量
     *
     * @param buyOrderSaleOrderGoodsDetailDto 订单详情信息
     */
    private void checkNeedNum(BuyOrderSaleOrderGoodsDetailDto buyOrderSaleOrderGoodsDetailDto) {
        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtos = saleOrderGoodsApiService.getRelatedDetail(CollUtil.newArrayList(buyOrderSaleOrderGoodsDetailDto.getSaleorderGoodsId()));
        List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = saleOrderGoodsDetailDtos.stream()
                .map(so -> saleOrderGoodsDetailConvertor.toDto(so)).collect(Collectors.toList());
        buyorderExpenseApiService.getNeedBuyNum(buyOrderSaleOrderGoodsDetailDtoList);
        if (CollUtil.isNotEmpty(buyOrderSaleOrderGoodsDetailDtoList)) {
            BuyOrderSaleOrderGoodsDetailDto detailDto = CollUtil.getFirst(buyOrderSaleOrderGoodsDetailDtoList);
            if (buyOrderSaleOrderGoodsDetailDto.getBuyorderExpenseJSaleorderId() == null) {
                if (detailDto.getNum() < buyOrderSaleOrderGoodsDetailDto.getBuyNum()) {
                    throw new ServiceException("采购数量不能大于需采数量，请刷新后重试");
                }
            } else {
                // 当是修改时，当前采购加上自身已经采购数量 ，数量不能大于需采数量
                int surplusNum = detailDto.getNum() + buyOrderSaleOrderGoodsDetailDto.getAlreadyNum();
                if (surplusNum < buyOrderSaleOrderGoodsDetailDto.getBuyNum()) {
                    throw new ServiceException("采购数量不能大于需采数量，请刷新后重试");
                }
            }
        }
    }

    @Override
    public List<RBuyorderExpenseJSaleorderDto> getRelatedDetail(List<BuyorderExpenseItemDto> buyorderExpenseItemDtoList) {
        return rBuyorderExpenseJSaleorderMapper.getRelatedDetail(buyorderExpenseItemDtoList);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateBuyNum(List<BuyorderExpenseItemDto> buyorderExpenseItemDtos) {
        List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = new ArrayList<>();
        buyorderExpenseItemDtos.forEach(o -> {
            List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtos = o.getBuyOrderSaleOrderGoodsDetailDtos();
            if (CollUtil.isNotEmpty(buyOrderSaleOrderGoodsDetailDtos)) {
                buyOrderSaleOrderGoodsDetailDtos.forEach(s -> {
                    RBuyorderExpenseJSaleorderEntity entity = rBuyorderExpenseJSaleorderMapper.selectByPrimaryKey(s.getBuyorderExpenseJSaleorderId());
                    s.setSaleorderGoodsId(entity.getSaleorderGoodsId());
                    s.setAlreadyNum(entity.getNum());
                    this.checkNeedNum(s);
                    buyOrderSaleOrderGoodsDetailDtoList.add(s);
                });
            }
        });
        if (buyOrderSaleOrderGoodsDetailDtoList.size() > 0) {
            try {
                log.info("开始更新销售单和采购费用单关系表中关联数量，更新信息：{}", JSON.toJSONString(buyOrderSaleOrderGoodsDetailDtoList));
                rBuyorderExpenseJSaleorderMapper.updateBuyNum(buyOrderSaleOrderGoodsDetailDtoList);
            } catch (Exception e) {
                log.error("更新销售单和采购费用单关系表失败，更新信息：{}", JSON.toJSONString(buyOrderSaleOrderGoodsDetailDtoList));
            }
        }
    }

    @Override
    public List<ExpenseBuyForSaleDetail> replaceExpenseBuyDetails(List<Integer> ids) {
        List<ExpenseBuyForSaleDetail> details = new ArrayList<>();
        if (ids.size() > 0) {
            details = rBuyorderExpenseJSaleorderMapper.needReplaceExpenseBuyDetails(ids);
        }
        return details;
    }

    @Override
    public List<Integer> findSaleOrderIds(Integer buyorderExpenseId) {
        return rBuyorderExpenseJSaleorderMapper.getSaleorderByBuyorderExpenseId(buyorderExpenseId);
    }
}
