package com.vedeng.flash.controller;

import com.newtask.UpdateSkuTop80PercentFlagTask;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.jasper.IreportExport;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.common.util.StringUtil;
import com.vedeng.flash.dto.po.NewAddOrderingQueryPO;
import com.vedeng.flash.dto.po.OrderingPoolQueryPO;
import com.vedeng.flash.dto.po.RecommendOrderingQueryPO;
import com.vedeng.flash.dto.temp.InlineCategoryTemp;
import com.vedeng.flash.dto.temp.JoinOrderingPoolOperateTemp;
import com.vedeng.flash.dto.temp.OneThreeMonthLastYearSaleNumTemp;
import com.vedeng.flash.dto.temp.SumaryRepeatCountTemp;
import com.vedeng.flash.dto.vo.*;
import com.vedeng.flash.model.RegularOperateLog;
import com.vedeng.flash.service.oreding.OrderingPoolService;
import com.vedeng.flash.service.prepare.impl.PrepareStockServiceImpl;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.Category;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.service.CategoryService;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.impl.BuyorderServiceImpl;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.UserService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: erp.vedeng.com
 * @description: 定品池 -- 接口
 * @author: Pusan
 * @create: 2021-05-18 13:32
 **/
@Controller
@RequestMapping("/ordering-pool/goods")
public class OrderingPoolController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private OrderingPoolService orderingPoolService;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private PrepareStockServiceImpl prepareStockServiceImpl;

    @Resource
    private UpdateSkuTop80PercentFlagTask updateSkuTop80PercentFlagTask;

    @Resource
    private BuyorderService buyorderService;
//
//    /**
//     * 异常处理
//     * @param e
//     * @return
//     */
//    @ExceptionHandler(Exception.class)
//    @ResponseBody
//    @ResponseStatus(value = HttpStatus.BAD_REQUEST,reason = "BAD_REQUEST")
//    public ResultInfo exceptionHandler(Exception e){
//
//        // 默认返回处理错误
//        ResultInfo<Object> returnResultInfo = new ResultInfo<>();
//        returnResultInfo.setMessage(e.getMessage());
//        log.info(" 模块[定品池] 接口抛出异常 {} -- Msg: {}",e.getClass(),e.getMessage());
//        StackTraceElement[] stackTrace = e.getStackTrace();
//        Arrays.stream(stackTrace).forEach(item->log.info("{} -- line:{} -- 方法名 :{}",item.getClassName(),item.getLineNumber(),item.getMethodName()));
//        return returnResultInfo;
//    }

    /**
     * 查询定品池列表
     * @return returnModelAndView
     */
    @RequestMapping("/index")
    public ModelAndView index(OrderingPoolQueryVO orderingPoolQueryVO,
                              @RequestParam(required = false) Integer pageNo,
                              @RequestParam(required = false) Integer pageSize,
                              HttpServletRequest httpServletRequest) throws Exception {

        logger.info("查询定品池列表 orderingPoolQueryVO -- {}",orderingPoolQueryVO);
        ModelAndView returnModelAndView = new ModelAndView("/goods/ordering-pool/index");

        // 设置默认值
        indexSetDefault(orderingPoolQueryVO);

        // 界面查询参数下拉框等
        currentControllerCommonMethod(httpServletRequest,returnModelAndView);

        // 查询条件
        returnModelAndView.addObject("orderingPoolQueryVO",orderingPoolQueryVO);

        // 核心数据 -- 定品池 & 分页
        Page page = getPageTag(httpServletRequest, pageNo, pageSize);
        List<OrderingPoolQueryPO> orderingPoolListPage = orderingPoolService.getOrderingPoolListPage(httpServletRequest,page,orderingPoolQueryVO);
        log.info("定品池列表 orderingPoolListPage -- {}",orderingPoolListPage);
        returnModelAndView.addObject("page",page);
        returnModelAndView.addObject("orderingPoolListPage",orderingPoolListPage);

        // 调用价格中心获取采购成本
        List<String> recommendSkuNoList = orderingPoolListPage.stream().map(item -> item.getSkuNo()).collect(Collectors.toList());
        List<SkuPriceChangeApplyDto> skuPriceChangeApplyDtos = prepareStockServiceImpl.querySkuCostFromPriceCenter(recommendSkuNoList);
        if(!CollectionUtils.isEmpty(skuPriceChangeApplyDtos)){
            for(OrderingPoolQueryPO orderingPoolQueryPO:orderingPoolListPage){
                for(SkuPriceChangeApplyDto skuPriceChangeApplyDto:skuPriceChangeApplyDtos){
                    if(orderingPoolQueryPO.getSkuNo() != null && orderingPoolQueryPO.getSkuNo().equals(skuPriceChangeApplyDto.getSkuNo())){
                        orderingPoolQueryPO.setCostPrice(StringUtil.isNotBlank(skuPriceChangeApplyDto.getMiddlePrice()) ?skuPriceChangeApplyDto.getMiddlePrice():"-");
                    }
                }
            }
        }

        return returnModelAndView;
    }

    /**
     * 定品池参数默认值
     * @param orderingPoolQueryVO
     */
    private void indexSetDefault(OrderingPoolQueryVO orderingPoolQueryVO) {
        if(orderingPoolQueryVO.getGoodsType() == null){
            orderingPoolQueryVO.setGoodsType(Integer.valueOf(-1));
        }
        if(orderingPoolQueryVO.getGoodsUserId() == null){
            orderingPoolQueryVO.setGoodsUserId(Integer.valueOf(-1));
        }
        if(orderingPoolQueryVO.getSkuStatus() == null){
            orderingPoolQueryVO.setSkuStatus(Integer.valueOf(0));
        }
    }

    /**
     * 测试刷新80%标识任务
     * @throws Exception
     */
    @RequestMapping("/testUpdateSkuTop80PercentFlagTask")
    public void testUpdateSkuTop80PercentFlagTask() throws Exception {
        updateSkuTop80PercentFlagTask.execute(new Date().toLocaleString());
    }

    /**
     * 推荐定品
     * @param recommendOrderingQueryVO
     * @param pageNo
     * @param pageSize
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/recommendOrdering")
    public ModelAndView recommendOrdering(RecommendOrderingQueryVO recommendOrderingQueryVO,
                                          @RequestParam(required = false) Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize,
                                          HttpServletRequest httpServletRequest){

        logger.info("查询推荐定品列表 recommendOrderingQueryVO -- {}",recommendOrderingQueryVO);
        ModelAndView returnModelAndView = new ModelAndView("/goods/ordering-pool/recommend");

        // 设置默认值
        recommendOrderingSetDefault(recommendOrderingQueryVO);

        // 界面查询参数下拉框等
        currentControllerCommonMethod(httpServletRequest,returnModelAndView);

        // 查询条件
        returnModelAndView.addObject("recommendOrderingQueryVO",recommendOrderingQueryVO);

        // 核心数据 -- 推荐定品 & 分页
        Page page = getPageTag(httpServletRequest, pageNo, pageSize);
        List<RecommendOrderingQueryPO> recommendOrderingListPage = orderingPoolService.getRecommendOrderingListPage(httpServletRequest,page,recommendOrderingQueryVO);
        returnModelAndView.addObject("page",page);
        returnModelAndView.addObject("recommendOrderingListPage",recommendOrderingListPage);
        log.info("推荐定品Query -- {}",recommendOrderingListPage);

        // 调用价格中心获取采购成本
        List<String> recommendSkuNoList = recommendOrderingListPage.stream().map(item -> item.getSkuNo()).collect(Collectors.toList());
        List<SkuPriceChangeApplyDto> skuPriceChangeApplyDtos = prepareStockServiceImpl.querySkuCostFromPriceCenter(recommendSkuNoList);
        if(!CollectionUtils.isEmpty(skuPriceChangeApplyDtos)){
            for(RecommendOrderingQueryPO recommendOrderingQueryPO:recommendOrderingListPage){
                for(SkuPriceChangeApplyDto skuPriceChangeApplyDto:skuPriceChangeApplyDtos){
                    if(recommendOrderingQueryPO.getSkuNo() != null && recommendOrderingQueryPO.getSkuNo().equals(skuPriceChangeApplyDto.getSkuNo())){
                        recommendOrderingQueryPO.setCostPrice(StringUtil.isNotBlank(skuPriceChangeApplyDto.getMiddlePrice()) ?skuPriceChangeApplyDto.getMiddlePrice():"-");
                    }
                }
            }
        }

        return returnModelAndView;
    }

    /**
     * 推荐定品设置默认值
     * @param recommendOrderingQueryVO
     */
    private void recommendOrderingSetDefault(RecommendOrderingQueryVO recommendOrderingQueryVO) {
        // 定品状态 全部
        if(recommendOrderingQueryVO.getOrderingFlag() == null) {
            recommendOrderingQueryVO.setOrderingFlag(-1);
        }
        // 商品类型 耗材
        if(recommendOrderingQueryVO.getGoodsType() == null) {
            recommendOrderingQueryVO.setGoodsType(317);
        }
        // 销售额比例 80%
        if(recommendOrderingQueryVO.getRate() == null) {
            recommendOrderingQueryVO.setRate(80);
        }
        // 时间范围 去年
        if(recommendOrderingQueryVO.getTimeFrame() == null) {
            recommendOrderingQueryVO.setTimeFrame(12);
        }
    }

    /**
     * 新增定品
     * @param newAddOrderingQueryVO
     * @param pageNo
     * @param pageSize
     * @param httpServletRequest
     * @return
     */
    @RequestMapping("/newAddOrdering")
    public ModelAndView newAddOrdering(NewAddOrderingQueryVO newAddOrderingQueryVO,
                                       @RequestParam(required = false) Integer pageNo,
                                       @RequestParam(required = false) Integer pageSize,
                                       HttpServletRequest httpServletRequest){

        logger.info("查询新增定品列表 newAddOrderingQueryVO -- {}",newAddOrderingQueryVO);
        ModelAndView returnModelAndView = new ModelAndView("/goods/ordering-pool/new_add_ordering");

        // 设置默认值
        newAddOrderingSetDefault(newAddOrderingQueryVO);

        // 界面查询参数下拉框等
        currentControllerCommonMethod(httpServletRequest,returnModelAndView);

        // 查询条件
        returnModelAndView.addObject("newAddOrderingQueryVO",newAddOrderingQueryVO);

        // 核心数据 -- 新增定品 & 分页
        Page page = getPageTag(httpServletRequest, pageNo, pageSize);
        List<NewAddOrderingQueryPO> newAddOrderingListPage = orderingPoolService.getNewAddOrderingListPage(httpServletRequest,page,newAddOrderingQueryVO);
        returnModelAndView.addObject("page",page);
        returnModelAndView.addObject("newAddOrderingListPage",newAddOrderingListPage);
        log.info("新增定品Query -- {}",newAddOrderingListPage);

        // 调用价格中心获取采购成本
        List<String> newAddSkuNoList = newAddOrderingListPage.stream().map(item -> item.getSkuNo()).collect(Collectors.toList());
        List<SkuPriceChangeApplyDto> skuPriceChangeApplyDtos = prepareStockServiceImpl.querySkuCostFromPriceCenter(newAddSkuNoList);
        if(!CollectionUtils.isEmpty(skuPriceChangeApplyDtos)){
            for(NewAddOrderingQueryPO newAddOrderingQueryPO:newAddOrderingListPage){
                for(SkuPriceChangeApplyDto skuPriceChangeApplyDto:skuPriceChangeApplyDtos){
                    if(newAddOrderingQueryPO.getSkuNo() != null && newAddOrderingQueryPO.getSkuNo().equals(skuPriceChangeApplyDto.getSkuNo())){
                        newAddOrderingQueryPO.setCostPrice(StringUtil.isNotBlank(skuPriceChangeApplyDto.getMiddlePrice()) ?skuPriceChangeApplyDto.getMiddlePrice():"-");
                    }
                }
            }
        }

        return returnModelAndView;
    }

    /**
     * 新增定品设置默认值
     * @param newAddOrderingQueryVO
     */
    private void newAddOrderingSetDefault(NewAddOrderingQueryVO newAddOrderingQueryVO) {
        if(newAddOrderingQueryVO.getGoodsType() == null){
            newAddOrderingQueryVO.setGoodsType(Integer.valueOf(-1));
        }
        if(newAddOrderingQueryVO.getGoodsUserId() == null){
            newAddOrderingQueryVO.setGoodsUserId(Integer.valueOf(-1));
        }
    }


    /**
     * 获取近一月 三月 去年销售量
     * @param skuIdList
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping("/getOneThreeMonthLastYearSaleNum")
    public List<OneThreeMonthLastYearSaleNumTemp> getOneThreeMonthLastYearSaleNum(@RequestBody List<Integer> skuIdList){

        log.info("getOneThreeMonthLastYearSaleNum -- {}",skuIdList);
        if(CollectionUtils.isEmpty(skuIdList)){
            return null;
        }
        return orderingPoolService.getOneThreeMonthLastYearSaleNum(skuIdList);
    }

    /**
     * 加入定品池
     * @param joinOrderingPoolOperateVO
     * @return
     */
    @ResponseBody
    @RequestMapping("/joinOrderingPool")
    public ResponseEntity joinOrderingPool(@RequestBody JoinOrderingPoolOperateVO joinOrderingPoolOperateVO,HttpServletRequest httpServletRequest){

        log.info("joinOrderingPoolOperateVO -- {}",joinOrderingPoolOperateVO);
        // 过滤空数据
        if(!ObjectUtils.allNotNull(joinOrderingPoolOperateVO)){
            return ResponseEntity.badRequest().build();
        }
        if(CollectionUtils.isEmpty(joinOrderingPoolOperateVO.getJoinOrderingPoolOperateTempList())){
            return ResponseEntity.badRequest().build();
        }

        // 操作用户
        User user = (User) httpServletRequest.getSession().getAttribute(Consts.SESSION_USER);
        User user1 = new User(); user1.setUserId(Integer.valueOf(1));
        SumaryRepeatCountTemp sumaryRepeatCountTemp = SumaryRepeatCountTemp
                .builder()
                .currentUser(user==null?user1:user)
                .repeatCount(Integer.valueOf(0))
                .build();

        // SkuId 加入
        if(Integer.valueOf(1).equals(joinOrderingPoolOperateVO.getIdFlag())){
            List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps = joinOrderingPoolOperateVO.getJoinOrderingPoolOperateTempList().stream().filter(item -> item.getSkuId() != null).collect(Collectors.toList());
            log.info("joinOrderingPoolOperateTemps -- {}",joinOrderingPoolOperateTemps);
            orderingPoolService.joinOrderingPoolBySkuId(joinOrderingPoolOperateTemps,sumaryRepeatCountTemp);
        }
        // SkuNo 加入
        if(Integer.valueOf(2).equals(joinOrderingPoolOperateVO.getIdFlag())){
            List<JoinOrderingPoolOperateTemp> joinOrderingPoolOperateTemps = joinOrderingPoolOperateVO.getJoinOrderingPoolOperateTempList().stream().filter(item -> item.getSkuNo() != null).collect(Collectors.toList());
            orderingPoolService.joinOrderingPoolBySkuNo(joinOrderingPoolOperateTemps,sumaryRepeatCountTemp);
        }

        return ResponseEntity.ok("操作成功");
    }

    /**
     * 当前接口重复性操作整合
     * @param httpServletRequest
     * @param returnModelAndView
     */
    private void currentControllerCommonMethod(HttpServletRequest httpServletRequest,ModelAndView returnModelAndView) {

        // 操作用户
        User user = (User) httpServletRequest.getSession().getAttribute(Consts.SESSION_USER);
        returnModelAndView.addObject("user",user);

        // 商品类型 -- 过滤 319 & 653
        List<SysOptionDefinition> goodsTypes = getSysOptionDefinitionList(SysOptionConstant.ID_315);
        goodsTypes = goodsTypes.stream()
                .filter(item -> !Integer.valueOf(653).equals(item.getSysOptionDefinitionId()) && !Integer.valueOf(319).equals(item.getSysOptionDefinitionId()))
                .collect(Collectors.toList());
        int index = -1;
        for(int i=0;i < goodsTypes.size();i ++){
            if(Integer.valueOf(317).equals(goodsTypes.get(i).getSysOptionDefinitionId())){
                index = i;
            }
        }
        if(index > 0){
            SysOptionDefinition remove = goodsTypes.remove(index);
            goodsTypes.add(remove);
        }
        Collections.reverse(goodsTypes);
        log.info("商品类型 goodsTypes -- {}",goodsTypes);
        returnModelAndView.addObject("goodsTypes",goodsTypes);

        // 商品分类
        Category category = new Category();
        category.setParentId(0);
        if (null != user) {
            category.setCompanyId(user.getCompanyId());
        }
        List<Category> categoryList = categoryService.getParentCateList0(category);
        returnModelAndView.addObject("categoryList",categoryList);
        // 下拉框带参数
        List<InlineCategoryTemp> categoryOptList = categoryService.getAllAndChildCategoryList(category);
        returnModelAndView.addObject("categoryOptList",categoryOptList);

        // 产品归属 -- 取值产品经理+产品助理
        List<User> goodsManagers = userService.getAllGoodsManagersAndAssistant();
        returnModelAndView.addObject("goodsManagers",goodsManagers);
    }

    /**
     * 批量导入定品信息界面
     * @return
     */
    @RequestMapping("/batchAddOrdering")
    public ModelAndView batchAddOrdering(){
        return new ModelAndView("/goods/ordering-pool/batch_add_ordering");
    }

    /**
     * 批量导入定品信息保存inde
     * @return
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping("/batchSaveOrdering")
    public ResultInfo batchSaveOrdering(HttpServletRequest request, HttpSession session,
                                          @RequestParam("safile") MultipartFile rkfile){
        ResultInfo<Object> resultInfo = new ResultInfo<>();
        try {
            User user = (User) session.getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/logistics");
            // 接收Excel
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, rkfile);
            log.info("批量导入定品信息保存 batchSaveOrdering() -- fileInfo : {}",fileInfo);
            // 解析后返回结果状态
            resultInfo = orderingPoolService.batchSaveOrdering(fileInfo, user);
        } catch (Exception e) {
            logger.error("导入定品信息失败！", e);
            resultInfo.setMessage("导入定品信息失败！" );
            return resultInfo;
        }
        return resultInfo;
    }

    /**
     * 移除定品信息界面
     * @return
     */
    @RequestMapping("/removeOrderingPool")
    public ModelAndView removeOrderingPool(@RequestParam("skuNoList") List<String> skuNoList){
        log.info("移除定品信息界面 removeOrderingPool -- skuNoList : {}",skuNoList);
        // 初始化移除页面
        ModelAndView returnModelAndView = new ModelAndView();

        if(CollectionUtils.isEmpty(skuNoList)){
            throw new RuntimeException("参数传递异常");
        }

        // 【删除一个】 页面展示不同于【删除多个】
        if(skuNoList.size() == 1){
            returnModelAndView.setViewName("/goods/ordering-pool/remove_ordering_pool");
            returnModelAndView.addObject("skuNo",skuNoList.get(0));
            CoreSku coreSku = coreSkuMapper.selectBySkuNo(skuNoList.get(0));
            returnModelAndView.addObject("showName",coreSku.getShowName());
        }else{
            returnModelAndView.setViewName("/goods/ordering-pool/remove_ordering_pool_more");
            returnModelAndView.addObject("skuNoList",skuNoList);
            returnModelAndView.addObject("skuNoSize",skuNoList.size());
        }

        return returnModelAndView;
    }

    /**
     * 移除定品
     * @param removeOrderingPoolOperateVO
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping("/deleteOrderingPool")
    public ResponseEntity deleteOrderingPool(@RequestBody RemoveOrderingPoolOperateVO removeOrderingPoolOperateVO, HttpServletRequest request){

        log.info("removeOrderingPoolOperateVO -- {}",removeOrderingPoolOperateVO);
        // 过滤空数据
        if(!ObjectUtils.allNotNull(removeOrderingPoolOperateVO)){
            return ResponseEntity.badRequest().build();
        }
        if(StringUtil.isBlank(removeOrderingPoolOperateVO.getCauseContent())){
            return ResponseEntity.badRequest().build();
        }
        if(CollectionUtils.isEmpty(removeOrderingPoolOperateVO.getSkuNoList())){
            return ResponseEntity.badRequest().build();
        }
        // 移除操作
        orderingPoolService.deleteOrderingPool(removeOrderingPoolOperateVO,request);

        return ResponseEntity.ok("操作成功");
    }

    /**
     * 删除原因查询
     * @param skuId
     * @return
     */
    @RequestMapping("/removePoolCause")
    public ModelAndView removePoolCause(@RequestParam("skuId") Integer skuId){

        log.info("删除原因查询skuId -- {}",skuId);
        if(skuId == null){
            throw new RuntimeException("参数传递异常");
        }
        // 初始化移除页面
        ModelAndView returnModelAndView = new ModelAndView("/goods/ordering-pool/remove_pool_cause");

        List<RegularOperateLog> regularOperateLogs = orderingPoolService.removePoolCause(skuId);
        CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(skuId);
        returnModelAndView.addObject("regularOperateLogs",regularOperateLogs);
        returnModelAndView.addObject("coreSku",coreSku);

        return returnModelAndView;
    }

    /**
     * 导出定品页面
     * @return
     */
    @RequestMapping(value = "/initExportOrderingListPage")
    public ModelAndView initExportOrderingListPage(ModelAndView returnModelAndView) {
        returnModelAndView.setViewName("/goods/ordering-pool/export_ordering_list");
        return returnModelAndView;
    }

    /**
     * 导出定品
     * @return
     */
    @RequestMapping(value = "/exportOrderingList")
    public void exportOrderingList(HttpServletRequest request, HttpServletResponse response,@RequestParam("exportMonth") String exportMonth) {

        log.info("导出定品 -- 月份： {}",exportMonth);
        if(StringUtil.isBlank(exportMonth)){
            throw new RuntimeException("导出月份异常");
        }
        // 导出定品月份字符串
        String exportMonthStr = exportMonth.trim();
        // 获取需要解析成Excel的数据
        Map<String, Object> exportMap = orderingPoolService.exportOrderingList(exportMonthStr);
        String[] sheetNames = new String[]{"定品", "操作日志"};
        Map<String, String> jrxmlMap = new HashMap<>();
        jrxmlMap.put("orderingPoolList", "/WEB-INF/ireport/jrxml/定品池列表.jrxml");
        jrxmlMap.put("orderingPoolOperateLogList", "/WEB-INF/ireport/jrxml/定品池操作日志.jrxml");
        // 导出Excel工具类
        IreportExport.exportMoreSheet(sheetNames, request, response, jrxmlMap,
                exportMonth+"定品池列表.xlsx", exportMap);
    }


}
