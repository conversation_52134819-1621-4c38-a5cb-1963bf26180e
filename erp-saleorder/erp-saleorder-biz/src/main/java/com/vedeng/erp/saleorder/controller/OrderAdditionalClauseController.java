package com.vedeng.erp.saleorder.controller;

import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.annotation.NoRepeatSubmit;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.saleorder.dto.SaleorderAdditionalClauseDto;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseApiService;
import com.vedeng.erp.saleorder.service.OrderAdditionalClauseService;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.system.service.RegionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 订单附加条款
 */
@Slf4j
@Controller
@RequestMapping("/order/additionalClause")
public class OrderAdditionalClauseController extends BaseController {

    @Resource
    private OrderAdditionalClauseService orderAdditionalClauseService;

    @Resource
    private OrderAdditionalClauseApiService saleOrderAdditionalClauseApiService;



    @RequestMapping(value = "/query")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> querySaleorderAdditionalClauseDto(@RequestBody SaleorderAdditionalClauseDto additionalClauseDto, HttpServletRequest request) {
        try{
            User user = getSessionUser(request);
            SaleorderAdditionalClauseDto dto= saleOrderAdditionalClauseApiService.getSaleorderAdditionalClauseByOrderId(additionalClauseDto);
            return R.success(dto);
        }catch (Exception e){
           // e.printStackTrace();
            log.error("查询失败",e);
        }
        return R.error("查询失败");
    }

    /**
     * 保存
     *
     * @param
     * @return R
     */
    @RequestMapping(value = "/save")
    @NoNeedAccessAuthorization
    @ResponseBody
    public R<?> saveSaleorderAdditionalClauseDto(@RequestBody SaleorderAdditionalClauseDto additionalClauseDto, HttpServletRequest request) {
        User user = getSessionUser(request);
        additionalClauseDto.setCreator(user.getUserId());
        additionalClauseDto.setAddTime(new Date());
        additionalClauseDto.setUpdater(user.getUserId());
        additionalClauseDto.setModTime(new Date());
        orderAdditionalClauseService.saveSaleorderAdditionalClause(additionalClauseDto );
        SaleorderAdditionalClauseDto dto= saleOrderAdditionalClauseApiService.getSaleorderAdditionalClauseByOrderId(additionalClauseDto);
        return R.success(dto);
    }
}