<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.firstengage.dao.RegistrationNumberMapper">

  <resultMap id="BaseResultMap" type="com.vedeng.firstengage.model.RegistrationNumber">
    <id column="REGISTRATION_NUMBER_ID" jdbcType="INTEGER" property="registrationNumberId" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="MANAGE_CATEGORY_LEVEL" jdbcType="INTEGER" property="manageCategoryLevel" />
    <result column="PRODUCT_COMPANY_ID" jdbcType="INTEGER" property="productCompanyId" />
    <result column="PRODUCTION_ADDRESS" jdbcType="VARCHAR" property="productionAddress" />
    <result column="PRODUCT_CHINESE_NAME" jdbcType="VARCHAR" property="productChineseName" />
    <result column="PRODUCT_ENGLISH_NAME" jdbcType="VARCHAR" property="productEnglishName" />
    <result column="PRODUCT_CATEGORY_ID" jdbcType="INTEGER" property="productCategoryId" />
    <result column="PRODUCT_CATEGORY_NAME" jdbcType="VARCHAR" property="productCategoryName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="ISSUING_DATE" jdbcType="BIGINT" property="issuingDate" />
    <result column="EFFECTIVE_DATE" jdbcType="BIGINT" property="effectiveDate" />
    <result column="DEAL_STATUS" jdbcType="INTEGER" property="dealStatus"/>
    <result column="APPROVAL_DEPARTMENT" jdbcType="VARCHAR" property="approvalDepartment" />
    <result column="TRADEMARK" jdbcType="VARCHAR" property="trademark" />
    <result column="ZIP_CODE" jdbcType="VARCHAR" property="zipCode" />
    <result column="REGISTERED_AGENT" jdbcType="VARCHAR" property="registeredAgent" />
    <result column="REGISTERED_AGENT_ADDRESS" jdbcType="VARCHAR" property="registeredAgentAddress" />
    <result column="PRO_PERF_STRU_AND_COMP" jdbcType="VARCHAR" property="proPerfStruAndComp" />
    <result column="PRODUCT_USE_RANGE" jdbcType="VARCHAR" property="productUseRange" />
    <result column="OTHER_CONTENTS" jdbcType="VARCHAR" property="otherContents" />
    <result column="PRODUCT_STANDARDS" jdbcType="VARCHAR" property="productStandards" />
    <result column="TYPE" jdbcType="BIT" property="type" />
    <result column="PRODUCTION_OR_COUNTRY" jdbcType="VARCHAR" property="productionOrCountry" />
    <result column="REMARKS" jdbcType="VARCHAR" property="remarks" />
    <result column="AFTERSALE_SERVICE_ORG" jdbcType="VARCHAR" property="aftersaleServiceOrg" />
    <result column="CHANGE_DATE" jdbcType="BIGINT" property="changeDate" />
    <result column="CHANGE_CONTENTS" jdbcType="VARCHAR" property="changeContents" />
    <result column="IS_RELEASE" jdbcType="BIT" property="isRelease" />
    <result column="EXPECTED_USAGE" jdbcType="VARCHAR" property="expectedUsage" />
    <result column="STORAGE_COND_AND_EFFECTIVE_DATE" jdbcType="VARCHAR" property="storageCondAndEffectiveDate" />
    <result column="MAIN_PRO_PERF_STRU_AND_COMP" jdbcType="VARCHAR" property="mainProPerfStruAndComp" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />

    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="PRODUCTION_ENTERPRISE_NAME" jdbcType="VARCHAR" property="productionEnterpriseName" />
    <result column="IS_SUBCONTRACT_PRODUCTION" jdbcType="INTEGER" property="isSubcontractProduction" />
    <result column="STORAGE_AND_EXPIRY_DATE" jdbcType="VARCHAR" property="storageAndExpiryDate" />
    <result column="CATEGORY" jdbcType="INTEGER" property="category" />
    <result column="ATTACHMENT" jdbcType="VARCHAR" property="attachment" />
    <result column="BC_ISSUE_DATE" jdbcType="BIGINT" property="bcIssueDate" />
    <result column="PRODUCT_COMPANY_LICENCE" jdbcType="VARCHAR" property="productCompanyLicence" />

    <result column="MANUFACTURER_ID" jdbcType="INTEGER" property="manufacturerId"/>
    <result column="MANUFACTURER_NAME" jdbcType="INTEGER" property="manufacturerName"/>

    <association property="productCompany" javaType="com.vedeng.firstengage.model.ProductCompany">
    	<!--<id column="PRODUCT_COMPANY_ID_ONE" property="productCompanyId"/>-->
    	<result column="PRODUCT_COMPANY_CHINESE_NAME" property="productCompanyChineseName"/>
    	<result column="PRODUCT_COMPANY_ENGLISH_NAME" property="productCompanyEnglishName"/>
    	<result column="PRODUCT_COMPANY_ADDRESS" property="productCompanyAddress"/>
    </association>
    
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:29:37 CST 2019.
    -->
    REGISTRATION_NUMBER_ID, REGISTRATION_NUMBER, MANAGE_CATEGORY_LEVEL, PRODUCT_COMPANY_ID, 
    PRODUCTION_ADDRESS, PRODUCT_CHINESE_NAME, PRODUCT_ENGLISH_NAME, PRODUCT_CATEGORY_ID, 
    PRODUCT_CATEGORY_NAME, MODEL, ISSUING_DATE, EFFECTIVE_DATE, APPROVAL_DEPARTMENT, 
    TRADEMARK, ZIP_CODE, REGISTERED_AGENT, REGISTERED_AGENT_ADDRESS, PRO_PERF_STRU_AND_COMP, 
    PRODUCT_USE_RANGE, OTHER_CONTENTS, PRODUCT_STANDARDS, TYPE, PRODUCTION_OR_COUNTRY, 
    REMARKS, AFTERSALE_SERVICE_ORG, CHANGE_DATE, CHANGE_CONTENTS, IS_RELEASE, EXPECTED_USAGE, 
    STORAGE_COND_AND_EFFECTIVE_DATE, MAIN_PRO_PERF_STRU_AND_COMP, ADD_TIME, CREATOR, 
    MOD_TIME, UPDATER,
    PRODUCTION_ENTERPRISE_NAME,IS_SUBCONTRACT_PRODUCTION,STORAGE_AND_EXPIRY_DATE,
    CATEGORY,ATTACHMENT,BC_ISSUE_DATE,PRODUCT_COMPANY_LICENCE,MANUFACTURER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:29:37 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_REGISTRATION_NUMBER
    where REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER}
  </select>

  <select id="selectManufacturer" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
        MANUFACTURER_ID,
        MANUFACTURER_NAME
    FROM T_MANUFACTURER WHERE 1=1
        AND IS_DELETE =0
        AND MANUFACTURER_NAME != ''
    <if test="manufacturerId != null">
        AND MANUFACTURER_ID =#{manufacturerId,jdbcType=INTEGER}
    </if>
    ORDER BY MANUFACTURER_ID DESC
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:29:37 CST 2019.
    -->
    delete from T_REGISTRATION_NUMBER
    where REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.firstengage.model.RegistrationNumber">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:29:37 CST 2019.
    -->
    insert into T_REGISTRATION_NUMBER (REGISTRATION_NUMBER_ID, REGISTRATION_NUMBER, 
      MANAGE_CATEGORY_LEVEL, PRODUCT_COMPANY_ID, 
      PRODUCTION_ADDRESS, PRODUCT_CHINESE_NAME, 
      PRODUCT_ENGLISH_NAME, PRODUCT_CATEGORY_ID, 
      PRODUCT_CATEGORY_NAME, MODEL, ISSUING_DATE, 
      EFFECTIVE_DATE, APPROVAL_DEPARTMENT, TRADEMARK, 
      ZIP_CODE, REGISTERED_AGENT, REGISTERED_AGENT_ADDRESS, 
      PRO_PERF_STRU_AND_COMP, PRODUCT_USE_RANGE, OTHER_CONTENTS, 
      PRODUCT_STANDARDS, TYPE, PRODUCTION_OR_COUNTRY, 
      REMARKS, AFTERSALE_SERVICE_ORG, CHANGE_DATE, 
      CHANGE_CONTENTS, IS_RELEASE, EXPECTED_USAGE, 
      STORAGE_COND_AND_EFFECTIVE_DATE, MAIN_PRO_PERF_STRU_AND_COMP, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER,
      PRODUCTION_ENTERPRISE_NAME,IS_SUBCONTRACT_PRODUCTION,STORAGE_AND_EXPIRY_DATE,
      CATEGORY,ATTACHMENT,BC_ISSUE_DATE)
    values (#{registrationNumberId,jdbcType=INTEGER}, #{registrationNumber,jdbcType=VARCHAR}, 
      #{manageCategoryLevel,jdbcType=INTEGER}, #{productCompanyId,jdbcType=INTEGER}, 
      #{productionAddress,jdbcType=VARCHAR}, #{productChineseName,jdbcType=VARCHAR}, 
      #{productEnglishName,jdbcType=VARCHAR}, #{productCategoryId,jdbcType=INTEGER}, 
      #{productCategoryName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, #{issuingDate,jdbcType=BIGINT}, 
      #{effectiveDate,jdbcType=BIGINT}, #{approvalDepartment,jdbcType=VARCHAR}, #{trademark,jdbcType=VARCHAR}, 
      #{zipCode,jdbcType=VARCHAR}, #{registeredAgent,jdbcType=VARCHAR}, #{registeredAgentAddress,jdbcType=VARCHAR}, 
      #{proPerfStruAndComp,jdbcType=VARCHAR}, #{productUseRange,jdbcType=VARCHAR}, #{otherContents,jdbcType=VARCHAR}, 
      #{productStandards,jdbcType=VARCHAR}, #{type,jdbcType=BIT}, #{productionOrCountry,jdbcType=VARCHAR}, 
      #{remarks,jdbcType=VARCHAR}, #{aftersaleServiceOrg,jdbcType=VARCHAR}, #{changeDate,jdbcType=BIGINT}, 
      #{changeContents,jdbcType=VARCHAR}, #{isRelease,jdbcType=BIT}, #{expectedUsage,jdbcType=VARCHAR}, 
      #{storageCondAndEffectiveDate,jdbcType=VARCHAR}, #{mainProPerfStruAndComp,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER},
      #{productionEnterpriseName},#{isSubcontractProduction},#{storageAndExpiryDate},
      #{category},#{attachment},#{bcIssueDate})
  </insert>
    <insert id="insertSelective" parameterType="com.vedeng.firstengage.model.RegistrationNumber" useGeneratedKeys="true" keyProperty="registrationNumberId">
    insert into T_REGISTRATION_NUMBER
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER,
      </if>
      <if test="manageCategoryLevel != null">
        MANAGE_CATEGORY_LEVEL,
      </if>
      <if test="productCompanyId != null">
        PRODUCT_COMPANY_ID,
      </if>
      <if test="productionAddress != null">
        PRODUCTION_ADDRESS,
      </if>
      <if test="productChineseName != null">
        PRODUCT_CHINESE_NAME,
      </if>
      <if test="productEnglishName != null">
        PRODUCT_ENGLISH_NAME,
      </if>
      <if test="productCategoryId != null">
        PRODUCT_CATEGORY_ID,
      </if>
      <if test="productCategoryName != null">
        PRODUCT_CATEGORY_NAME,
      </if>
      <if test="model != null">
        MODEL,
      </if>
      <if test="issuingDate != null">
        ISSUING_DATE,
      </if>
      <if test="effectiveDate != null">
        EFFECTIVE_DATE,
      </if>
      <if test="approvalDepartment != null">
        APPROVAL_DEPARTMENT,
      </if>
      <if test="trademark != null">
        TRADEMARK,
      </if>
      <if test="zipCode != null">
        ZIP_CODE,
      </if>
      <if test="registeredAgent != null">
        REGISTERED_AGENT,
      </if>
      <if test="registeredAgentAddress != null">
        REGISTERED_AGENT_ADDRESS,
      </if>
      <if test="proPerfStruAndComp != null">
        PRO_PERF_STRU_AND_COMP,
      </if>
      <if test="productUseRange != null">
        PRODUCT_USE_RANGE,
      </if>
      <if test="otherContents != null">
        OTHER_CONTENTS,
      </if>
      <if test="productStandards != null">
        PRODUCT_STANDARDS,
      </if>
      <if test="type != null">
        TYPE,
      </if>
      <if test="productionOrCountry != null">
        PRODUCTION_OR_COUNTRY,
      </if>
      <if test="remarks != null">
        REMARKS,
      </if>
      <if test="aftersaleServiceOrg != null">
        AFTERSALE_SERVICE_ORG,
      </if>
      <if test="changeDate != null">
        CHANGE_DATE,
      </if>
      <if test="changeContents != null">
        CHANGE_CONTENTS,
      </if>
      <if test="isRelease != null">
        IS_RELEASE,
      </if>
      <if test="expectedUsage != null">
        EXPECTED_USAGE,
      </if>
      <if test="storageCondAndEffectiveDate != null">
        STORAGE_COND_AND_EFFECTIVE_DATE,
      </if>
      <if test="mainProPerfStruAndComp != null">
        MAIN_PRO_PERF_STRU_AND_COMP,
      </if>
        <if test="attachment != null">
        ATTACHMENT,
      </if>
        <if test="category != null">
            CATEGORY,
        </if>
        <if test="storageAndExpiryDate != null">
        STORAGE_AND_EXPIRY_DATE,
      </if>
        <if test="isSubcontractProduction != null">
        IS_SUBCONTRACT_PRODUCTION,
      </if>
        <if test="productionEnterpriseName != null">
        PRODUCTION_ENTERPRISE_NAME,
      </if>
        <if test="bcIssueDate != null">
        BC_ISSUE_DATE,
      </if>
      <if test="productCompanyLicence != null">
        PRODUCT_COMPANY_LICENCE,
      </if>
      ADD_TIME,
      CREATOR,
      MOD_TIME,
      UPDATER,
      <if test="manufacturerId !=null">
        MANUFACTURER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="registrationNumberId != null">
        #{registrationNumberId,jdbcType=INTEGER},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="manageCategoryLevel != null">
        #{manageCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="productCompanyId != null">
        #{productCompanyId,jdbcType=INTEGER},
      </if>
      <if test="productionAddress != null">
        #{productionAddress,jdbcType=VARCHAR},
      </if>
      <if test="productChineseName != null">
        #{productChineseName,jdbcType=VARCHAR},
      </if>
      <if test="productEnglishName != null">
        #{productEnglishName,jdbcType=VARCHAR},
      </if>
      <if test="productCategoryId != null">
        #{productCategoryId,jdbcType=INTEGER},
      </if>
      <if test="productCategoryName != null">
        #{productCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="issuingDate != null">
        #{issuingDate,jdbcType=BIGINT},
      </if>
      <if test="effectiveDate != null">
        #{effectiveDate,jdbcType=BIGINT},
      </if>
      <if test="approvalDepartment != null">
        #{approvalDepartment,jdbcType=VARCHAR},
      </if>
      <if test="trademark != null">
        #{trademark,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="registeredAgent != null">
        #{registeredAgent,jdbcType=VARCHAR},
      </if>
      <if test="registeredAgentAddress != null">
        #{registeredAgentAddress,jdbcType=VARCHAR},
      </if>
      <if test="proPerfStruAndComp != null">
        #{proPerfStruAndComp,jdbcType=VARCHAR},
      </if>
      <if test="productUseRange != null">
        #{productUseRange,jdbcType=VARCHAR},
      </if>
      <if test="otherContents != null">
        #{otherContents,jdbcType=VARCHAR},
      </if>
      <if test="productStandards != null">
        #{productStandards,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=BIT},
      </if>
      <if test="productionOrCountry != null">
        #{productionOrCountry,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="aftersaleServiceOrg != null">
        #{aftersaleServiceOrg,jdbcType=VARCHAR},
      </if>
      <if test="changeDate != null">
        #{changeDate,jdbcType=BIGINT},
      </if>
      <if test="changeContents != null">
        #{changeContents,jdbcType=VARCHAR},
      </if>
      <if test="isRelease != null">
        #{isRelease,jdbcType=BIT},
      </if>
      <if test="expectedUsage != null">
        #{expectedUsage,jdbcType=VARCHAR},
      </if>
      <if test="storageCondAndEffectiveDate != null">
        #{storageCondAndEffectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="mainProPerfStruAndComp != null">
        #{mainProPerfStruAndComp,jdbcType=VARCHAR},
      </if>
        <if test="attachment != null">
           #{attachment},
        </if>
        <if test="category != null">
            #{category},
        </if>
        <if test="storageAndExpiryDate != null">
            #{storageAndExpiryDate},
        </if>
        <if test="isSubcontractProduction != null">
            #{isSubcontractProduction},
        </if>
        <if test="productionEnterpriseName != null">
            #{productionEnterpriseName},
        </if>
        <if test="bcIssueDate != null">
            #{bcIssueDate},
        </if>
      <if test="productCompanyLicence !=null">
        #{productCompanyLicence},
      </if>
      unix_timestamp(now())*1000,
      #{creator,jdbcType=INTEGER},
      unix_timestamp(now())*1000,
      #{creator,jdbcType=INTEGER},
      <if test="manufacturerId !=null">
        #{manufacturerId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.firstengage.model.RegistrationNumber">
    update T_REGISTRATION_NUMBER
    <set>
      <if test="registrationNumber != null">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="manageCategoryLevel != null">
        MANAGE_CATEGORY_LEVEL = #{manageCategoryLevel,jdbcType=INTEGER},
      </if>
      <if test="productCompanyId != null">
        PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER},
      </if>
      <if test="productionAddress != null">
        PRODUCTION_ADDRESS = #{productionAddress,jdbcType=VARCHAR},
      </if>
      <if test="productChineseName != null">
        PRODUCT_CHINESE_NAME = #{productChineseName,jdbcType=VARCHAR},
      </if>
      <if test="productEnglishName != null">
        PRODUCT_ENGLISH_NAME = #{productEnglishName,jdbcType=VARCHAR},
      </if>
      <if test="productCategoryId != null">
        PRODUCT_CATEGORY_ID = #{productCategoryId,jdbcType=INTEGER},
      </if>
      <if test="productCategoryName != null">
        PRODUCT_CATEGORY_NAME = #{productCategoryName,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="issuingDate != null">
        ISSUING_DATE = #{issuingDate,jdbcType=BIGINT},
      </if>
      <if test="effectiveDate != null">
        EFFECTIVE_DATE = #{effectiveDate,jdbcType=BIGINT},
      </if>
      <if test="dealStatus != null">
        DEAL_STATUS = #{dealStatus,jdbcType=INTEGER},
      </if>
      <if test="approvalDepartment != null">
        APPROVAL_DEPARTMENT = #{approvalDepartment,jdbcType=VARCHAR},
      </if>
      <if test="trademark != null">
        TRADEMARK = #{trademark,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="registeredAgent != null">
        REGISTERED_AGENT = #{registeredAgent,jdbcType=VARCHAR},
      </if>
      <if test="registeredAgentAddress != null">
        REGISTERED_AGENT_ADDRESS = #{registeredAgentAddress,jdbcType=VARCHAR},
      </if>
      <if test="proPerfStruAndComp != null">
        PRO_PERF_STRU_AND_COMP = #{proPerfStruAndComp,jdbcType=VARCHAR},
      </if>
      <if test="productUseRange != null">
        PRODUCT_USE_RANGE = #{productUseRange,jdbcType=VARCHAR},
      </if>
      <if test="otherContents != null">
        OTHER_CONTENTS = #{otherContents,jdbcType=VARCHAR},
      </if>
      <if test="productStandards != null">
        PRODUCT_STANDARDS = #{productStandards,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        TYPE = #{type,jdbcType=BIT},
      </if>
      <if test="productionOrCountry != null">
        PRODUCTION_OR_COUNTRY = #{productionOrCountry,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        REMARKS = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="aftersaleServiceOrg != null">
        AFTERSALE_SERVICE_ORG = #{aftersaleServiceOrg,jdbcType=VARCHAR},
      </if>
      <if test="changeDate != null">
        CHANGE_DATE = #{changeDate,jdbcType=BIGINT},
      </if>
      <if test="changeContents != null">
        CHANGE_CONTENTS = #{changeContents,jdbcType=VARCHAR},
      </if>
      <if test="isRelease != null">
        IS_RELEASE = #{isRelease,jdbcType=BIT},
      </if>
      <if test="expectedUsage != null">
        EXPECTED_USAGE = #{expectedUsage,jdbcType=VARCHAR},
      </if>
      <if test="storageCondAndEffectiveDate != null">
        STORAGE_COND_AND_EFFECTIVE_DATE = #{storageCondAndEffectiveDate,jdbcType=VARCHAR},
      </if>
      <if test="mainProPerfStruAndComp != null">
        MAIN_PRO_PERF_STRU_AND_COMP = #{mainProPerfStruAndComp,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      MOD_TIME = unix_timestamp(now())*1000,
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="attachment !=null">
        ATTACHMENT=#{attachment},
      </if>
      <if test="category !=null">
        CATEGORY=#{category},
      </if>
      <if test="storageAndExpiryDate !=null">
        STORAGE_AND_EXPIRY_DATE=#{storageAndExpiryDate},
      </if>
      <if test="isSubcontractProduction !=null">
        IS_SUBCONTRACT_PRODUCTION=#{isSubcontractProduction},
      </if>
      <if test="productionEnterpriseName !=null">
        PRODUCTION_ENTERPRISE_NAME=#{productionEnterpriseName},
      </if>
      <if test="bcIssueDate !=null">
        BC_ISSUE_DATE=#{bcIssueDate},
      </if>
      <if test="productCompanyLicence !=null">
        PRODUCT_COMPANY_LICENCE=#{productCompanyLicence},
      </if>
      <if test="manufacturerId !=null">
        MANUFACTURER_ID=#{manufacturerId}
      </if>
    </set>
    where REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.firstengage.model.RegistrationNumber">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Mar 20 18:29:37 CST 2019.
    -->
    update T_REGISTRATION_NUMBER
    set REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      MANAGE_CATEGORY_LEVEL = #{manageCategoryLevel,jdbcType=INTEGER},
      PRODUCT_COMPANY_ID = #{productCompanyId,jdbcType=INTEGER},
      PRODUCTION_ADDRESS = #{productionAddress,jdbcType=VARCHAR},
      PRODUCT_CHINESE_NAME = #{productChineseName,jdbcType=VARCHAR},
      PRODUCT_ENGLISH_NAME = #{productEnglishName,jdbcType=VARCHAR},
      PRODUCT_CATEGORY_ID = #{productCategoryId,jdbcType=INTEGER},
      PRODUCT_CATEGORY_NAME = #{productCategoryName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      ISSUING_DATE = #{issuingDate,jdbcType=BIGINT},
      EFFECTIVE_DATE = #{effectiveDate,jdbcType=BIGINT},
      APPROVAL_DEPARTMENT = #{approvalDepartment,jdbcType=VARCHAR},
      TRADEMARK = #{trademark,jdbcType=VARCHAR},
      ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
      REGISTERED_AGENT = #{registeredAgent,jdbcType=VARCHAR},
      REGISTERED_AGENT_ADDRESS = #{registeredAgentAddress,jdbcType=VARCHAR},
      PRO_PERF_STRU_AND_COMP = #{proPerfStruAndComp,jdbcType=VARCHAR},
      PRODUCT_USE_RANGE = #{productUseRange,jdbcType=VARCHAR},
      OTHER_CONTENTS = #{otherContents,jdbcType=VARCHAR},
      PRODUCT_STANDARDS = #{productStandards,jdbcType=VARCHAR},
      TYPE = #{type,jdbcType=BIT},
      PRODUCTION_OR_COUNTRY = #{productionOrCountry,jdbcType=VARCHAR},
      REMARKS = #{remarks,jdbcType=VARCHAR},
      AFTERSALE_SERVICE_ORG = #{aftersaleServiceOrg,jdbcType=VARCHAR},
      CHANGE_DATE = #{changeDate,jdbcType=BIGINT},
      CHANGE_CONTENTS = #{changeContents,jdbcType=VARCHAR},
      IS_RELEASE = #{isRelease,jdbcType=BIT},
      EXPECTED_USAGE = #{expectedUsage,jdbcType=VARCHAR},
      STORAGE_COND_AND_EFFECTIVE_DATE = #{storageCondAndEffectiveDate,jdbcType=VARCHAR},
      MAIN_PRO_PERF_STRU_AND_COMP = #{mainProPerfStruAndComp,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      ATTACHMENT=#{attachment},CATEGORY=#{category},
      STORAGE_AND_EXPIRY_DATE=#{storageAndExpiryDate}, IS_SUBCONTRACT_PRODUCTION=#{isSubcontractProduction},
      PRODUCTION_ENTERPRISE_NAME=#{productionEnterpriseName},BC_ISSUE_DATE=#{bcIssueDate}
    where REGISTRATION_NUMBER_ID = #{registrationNumberId,jdbcType=INTEGER}
  </update>

  <!-- 根据输入查询注册证 -->
  <select id="getRegistrationInfoByStr" resultMap="BaseResultMap">
  		SELECT
			a.REGISTRATION_NUMBER_ID,
			a.REGISTRATION_NUMBER,
			c.FIRST_ENGAGE_ID
		FROM
			T_REGISTRATION_NUMBER a
			LEFT JOIN T_FIRST_ENGAGE c ON a.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID AND c.IS_DELETED = 0
		WHERE
			1 = 1
			<if test="null != registrationStr and '' != registrationStr">
				AND a.REGISTRATION_NUMBER LIKE CONCAT('%',#{registrationStr,jdbcType=VARCHAR},'%')
			</if>

			<if test="null != registrationStr1 and '' != registrationStr1">
				AND a.REGISTRATION_NUMBER = #{registrationStr1,jdbcType=VARCHAR}
				AND c.FIRST_ENGAGE_ID IS NOT NULL
				AND c.IS_DELETED = 0
				<if test="null != firstEngageId and firstEngageId > 0">
                    AND c.FIRST_ENGAGE_ID <![CDATA[ != ]]> #{firstEngageId, jdbcType=INTEGER}
                </if>
			</if>
        ORDER BY c.FIRST_ENGAGE_ID ASC
        LIMIT 10
  </select>
  
  <select id="getRegistrationInfoById" resultMap="BaseResultMap">
  		SELECT
			a.*,
			b.PRODUCT_COMPANY_ID as PRODUCT_COMPANY_ID_ONE,
			b.PRODUCT_COMPANY_CHINESE_NAME,
			b.PRODUCT_COMPANY_ENGLISH_NAME,
			b.PRODUCT_COMPANY_ADDRESS,
            c.FIRST_ENGAGE_ID
		FROM
			T_REGISTRATION_NUMBER a
        LEFT JOIN T_FIRST_ENGAGE c ON a.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID AND c.IS_DELETED = 0
		LEFT JOIN T_PRODUCT_COMPANY b ON a.PRODUCT_COMPANY_ID = b.PRODUCT_COMPANY_ID 
		WHERE
			a.REGISTRATION_NUMBER = #{registrationNumber, jdbcType=VARCHAR}
			<if test="null != registrationNumberId and registrationNumberId > 0">
				AND a.REGISTRATION_NUMBER_ID = #{registrationNumberId, jdbcType=INTEGER}
			</if>
		LIMIT 1
  </select>

    <select id="getRegistrationInfoByNumber"  resultMap="BaseResultMap">
      select
       a.REGISTRATION_NUMBER_ID, a.REGISTRATION_NUMBER, a.MANAGE_CATEGORY_LEVEL, a.PRODUCT_COMPANY_ID,
    a.PRODUCTION_ADDRESS, a.PRODUCT_CHINESE_NAME, a.PRODUCT_ENGLISH_NAME, a.PRODUCT_CATEGORY_ID,
    a.PRODUCT_CATEGORY_NAME, a.MODEL, a.ISSUING_DATE, a.EFFECTIVE_DATE,a.DEAL_STATUS, a.APPROVAL_DEPARTMENT,
    a.TRADEMARK, a.ZIP_CODE, a.REGISTERED_AGENT, a.REGISTERED_AGENT_ADDRESS, a.PRO_PERF_STRU_AND_COMP,
    a.PRODUCT_USE_RANGE, a.OTHER_CONTENTS, a.PRODUCT_STANDARDS, a.TYPE, a.PRODUCTION_OR_COUNTRY,
    a.REMARKS, a.AFTERSALE_SERVICE_ORG, a.CHANGE_DATE, a.CHANGE_CONTENTS, a.IS_RELEASE, a.EXPECTED_USAGE,
    a.STORAGE_COND_AND_EFFECTIVE_DATE, a.MAIN_PRO_PERF_STRU_AND_COMP, a.ADD_TIME, a.CREATOR,
    a.MOD_TIME, a.UPDATER,c.FIRST_ENGAGE_ID, a.PRODUCT_COMPANY_LICENCE
      from T_REGISTRATION_NUMBER  a LEFT JOIN T_FIRST_ENGAGE c ON a.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID AND c.IS_DELETED = 0
      where a.REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR}

      limit 1
    </select>


  <update id="refreshFirstList" parameterType="java.util.Map">
    UPDATE
      T_REGISTRATION_NUMBER a
    SET
      DEAL_STATUS = 1
    WHERE
      a.REGISTRATION_NUMBER_ID IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.registrationNumberId, jdbcType=INTEGER}
    </foreach>
  </update>


  <select id="getRefreshFirstList"  resultMap="BaseResultMap">
    SELECT
      b.REGISTRATION_NUMBER_ID
    FROM
      T_REGISTRATION_NUMBER b
    WHERE
      1 = 1
    <if test="null != registrationEndTime">
      AND b.EFFECTIVE_DATE <![CDATA[ <= ]]> #{registrationEndTime, jdbcType=BIGINT}
    </if>
    AND b.DEAL_STATUS <![CDATA[ != ]]> 1
  </select>

    <update id="dealstatus" parameterType="java.lang.Integer">
        update T_REGISTRATION_NUMBER set DEAL_STATUS = 2 where REGISTRATION_NUMBER_ID = #{registrationNumberId, jdbcType=INTEGER}
    </update>





















  <select id="getPCInfo" resultMap="BaseResultMap">
    SELECT
        a.zczbh as REGISTRATION_NUMBER,
        a.zcrmc as PRODUCT_COMPANY_CHINESE_NAME,
		'' as PRODUCT_COMPANY_ENGLISH_NAME,
        a.zcrzs as PRODUCT_COMPANY_ADDRESS,
        a.scdz as PRODUCTION_ADDRESS,
        a.dlrmc as REGISTERED_AGENT,
        a.dlrzs as REGISTERED_AGENT_ADDRESS,
        a.cpmc as PRODUCT_CHINESE_NAME,
		'' as PRODUCT_ENGLISH_NAME,
		2 as TYPE,
        a.xhgg as MODEL,
        a.jgjzc as PRO_PERF_STRU_AND_COMP,
        a.syfw as PRODUCT_USE_RANGE,
        a.qtnr as OTHER_CONTENTS,
        a.bz as REMARKS,
        UNIX_TIMESTAMP(DATE_FORMAT(a.pzrq, '%Y-%m-%d'))*1000 as ISSUING_DATE,
        UNIX_TIMESTAMP(DATE_FORMAT(a.yxrq, '%Y-%m-%d'))*1000 as EFFECTIVE_DATE,
        a.cpbz as PRODUCT_STANDARDS,
        UNIX_TIMESTAMP(DATE_FORMAT(a.bgrq, '%Y-%m-%d'))*1000 as CHANGE_DATE,
        a.zyzccf as MAIN_PRO_PERF_STRU_AND_COMP,
        a.yqyt as EXPECTED_USAGE,
        a.cctj as STORAGE_COND_AND_EFFECTIVE_DATE,
        a.spbm as APPROVAL_DEPARTMENT,
        a.bgqk as CHANGE_CONTENTS
    FROM
        t_qxinfo_gcqx a
		GROUP BY a.zczbh

    UNION ALL

    SELECT
        a.zczbh as REGISTRATION_NUMBER,
        '' as PRODUCT_COMPANY_CHINESE_NAME,
		a.zcrmc as PRODUCT_COMPANY_ENGLISH_NAME,
        a.zcrzs as PRODUCT_COMPANY_ADDRESS,
        a.scdz as PRODUCTION_ADDRESS,
        a.dlrmc as REGISTERED_AGENT,
        a.dlrzs as REGISTERED_AGENT_ADDRESS,
        '' as PRODUCT_CHINESE_NAME,
		a.cpmc as PRODUCT_ENGLISH_NAME,
		1 as TYPE,
        a.xhgg as MODEL,
        a.jgjzc as PRO_PERF_STRU_AND_COMP,
        a.syfw as PRODUCT_USE_RANGE,
        a.qtnr as OTHER_CONTENTS,
        a.bz as REMARKS,
        UNIX_TIMESTAMP(DATE_FORMAT(a.pzrq, '%Y-%m-%d'))*1000 as ISSUING_DATE,
        UNIX_TIMESTAMP(DATE_FORMAT(a.yxq, '%Y-%m-%d'))*1000 as EFFECTIVE_DATE,
        a.cpbz as PRODUCT_STANDARDS,
        UNIX_TIMESTAMP(DATE_FORMAT(a.bgrq, '%Y-%m-%d'))*1000 as CHANGE_DATE,
        a.zyzccf as MAIN_PRO_PERF_STRU_AND_COMP,
        a.yqyt as EXPECTED_USAGE,
        a.cpcctj as STORAGE_COND_AND_EFFECTIVE_DATE,
        a.spbm as APPROVAL_DEPARTMENT,
        a.bgqk as CHANGE_CONTENTS
    FROM
        t_qxinfo_jkqx a
    GROUP BY a.zczbh

    limit 2
  </select>

  <insert id="insertList" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="registrationNumberId">
    INSERT INTO (REGISTRATION_NUMBER, PRODUCTION_ADDRESS, REGISTERED_AGENT, REGISTERED_AGENT_ADDRESS, PRODUCT_CHINESE_NAME,
    PRODUCT_ENGLISH_NAME, MODEL, PRO_PERF_STRU_AND_COMP, PRODUCT_USE_RANGE, OTHER_CONTENTS, REMARKS, ISSUING_DATE, EFFECTIVE_DATE, PRODUCT_COMPANY_ID,
    PRODUCT_STANDARDS, CHANGE_DATE, MAIN_PRO_PERF_STRU_AND_COMP, EXPECTED_USAGE, STORAGE_COND_AND_EFFECTIVE_DATE, APPROVAL_DEPARTMENT, CHANGE_CONTENTS)

    values

    <foreach collection="list" item="reg" separator=",">
      (#{reg.registrationNumber}, #{reg.productionAddress}, #{reg.registeredAgent}, #{reg.registeredAgentAddress}, #{reg.productChineseName},
      #{reg.productEnglishName}, #{reg.model}, #{reg.proPerfStruAndComp}, #{reg.productUseRange}, #{reg.otherContents}, #{reg.remarks}, #{reg.issuingDate}, #{reg.effectiveDate}, #{reg.productCompany.productCompanyId}
      #{reg.productStandards}, #{reg.changeDate}, #{reg.mainProPerfStruAndComp}, #{reg.expectedUsage}, #{reg.storageCondAndEffectiveDate}, #{reg.approvalDepartment}, #{reg.changeContents})
    </foreach>


  </insert>

  <select id="getHisRegFromGoods" resultMap="BaseResultMap">
      SELECT
          *
      FROM
          (
              SELECT
                  a.RECORD_NUMBER AS REGISTRATION_NUMBER,
                  a.GOODS_TYPE,
                  a.GOODS_LEVEL,
                  a.MANAGE_CATEGORY,
                  a.MANAGE_CATEGORY_LEVEL,
                  a.BEGINTIME,
                  a.ENDTIME,
                  a.MANUFACTURER
              FROM
                  T_GOODS a
              WHERE
                  a.RECORD_NUMBER IS NOT NULL
              AND a.RECORD_NUMBER != ''
              UNION ALL
                  SELECT
                      a.REGISTRATION_NUMBER AS REGISTRATION_NUMBER,
                      a.GOODS_TYPE,
                      a.GOODS_LEVEL,
                      a.MANAGE_CATEGORY,
                      a.MANAGE_CATEGORY_LEVEL,
                      a.BEGINTIME,
                      a.ENDTIME,
                      a.MANUFACTURER
                  FROM
                      T_GOODS a
                  WHERE
                      a.REGISTRATION_NUMBER IS NOT NULL
                  AND a.REGISTRATION_NUMBER != ''
          ) aa
      GROUP BY
          aa.reg
  </select>

  <select id="getWmsSkuRegData" resultType="com.wms.model.po.WmsSkuReg">
    SELECT
        sku.SKU_NO skuNo,
        rn.REGISTRATION_NUMBER registrationNumber,
        rn.ISSUING_DATE approvalNoValidFrom,
        rn.EFFECTIVE_DATE approvalNoValidTo,
        rn.REMARKS memo
    FROM
        T_REGISTRATION_NUMBER rn
        INNER JOIN T_FIRST_ENGAGE fe ON rn.REGISTRATION_NUMBER_ID = fe.REGISTRATION_NUMBER_ID
        INNER JOIN V_CORE_SPU spu ON fe.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
        INNER JOIN V_CORE_SKU sku ON sku.SPU_ID = spu.SPU_ID
        where sku.SKU_ID=#{skuId,jdbcType=INTEGER}
  </select>

  <select id="getWmsSkuRegList" resultType="com.wms.model.po.WmsSkuReg">
    SELECT
        sku.SKU_NO skuNo,
        rn.REGISTRATION_NUMBER registrationNumber,
        rn.ISSUING_DATE approvalNoValidFrom,
        IF(MANAGE_CATEGORY_LEVEL = 968 , 253401407999000 , rn.EFFECTIVE_DATE)  approvalNoValidTo,
        rn.REMARKS memo
    FROM
        T_REGISTRATION_NUMBER rn
        INNER JOIN T_FIRST_ENGAGE fe ON rn.REGISTRATION_NUMBER_ID = fe.REGISTRATION_NUMBER_ID
        INNER JOIN V_CORE_SPU spu ON fe.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
        INNER JOIN V_CORE_SKU sku ON sku.SPU_ID = spu.SPU_ID
  </select>


  <select id="getWmsSkuRegListInSkuNoStr" resultType="com.wms.model.po.WmsSkuReg">
    SELECT
        sku.SKU_NO skuNo,
        rn.REGISTRATION_NUMBER registrationNumber,
        rn.ISSUING_DATE approvalNoValidFrom,
        IF(MANAGE_CATEGORY_LEVEL = 968 , 253401407999000 , rn.EFFECTIVE_DATE)   approvalNoValidTo,
        rn.REMARKS memo
    FROM
        T_REGISTRATION_NUMBER rn
        INNER JOIN T_FIRST_ENGAGE fe ON rn.REGISTRATION_NUMBER_ID = fe.REGISTRATION_NUMBER_ID
        INNER JOIN V_CORE_SPU spu ON fe.FIRST_ENGAGE_ID = spu.FIRST_ENGAGE_ID
        INNER JOIN V_CORE_SKU sku ON sku.SPU_ID = spu.SPU_ID
    where sku.SKU_NO in (${skuNoStr})
  </select>
  <select id="getRegistrationNumberByFirstEngageId" resultMap="BaseResultMap">
    SELECT
      a.*
    FROM
      T_FIRST_ENGAGE c
        INNER JOIN T_REGISTRATION_NUMBER a ON a.REGISTRATION_NUMBER_ID = c.REGISTRATION_NUMBER_ID
    WHERE
      c.FIRST_ENGAGE_ID= #{firstEngageId, jdbcType=INTEGER} AND c.IS_DELETED = 0
    LIMIT 1
  </select>

  <select id="getNumberListPage" resultMap="BaseResultMap" parameterType="java.util.Map">
     SELECT REGISTRATION_NUMBER_ID,REGISTRATION_NUMBER,PRODUCT_CATEGORY_ID
     FROM T_REGISTRATION_NUMBER
  </select>

  <select id="getSupplierRegisterListPage" resultType="com.vedeng.firstengage.model.vo.RegisterSkuVo" parameterType="java.util.Map">
    SELECT A.*,GROUP_CONCAT(A.SKU_NO) AS SKU_NO_STR FROM (
    SELECT R.REGISTRATION_NUMBER_ID,R.REGISTRATION_NUMBER,R.EFFECTIVE_DATE,
    K.SKU_NO,R.MANAGE_CATEGORY_LEVEL
    FROM V_CORE_SKU K LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
    LEFT JOIN T_FIRST_ENGAGE E ON P.FIRST_ENGAGE_ID=E.FIRST_ENGAGE_ID
    LEFT JOIN T_REGISTRATION_NUMBER R ON E.REGISTRATION_NUMBER_ID=R.REGISTRATION_NUMBER_ID
    WHERE K.SKU_NO IN
    <foreach collection="skuNoList" item="no" open="(" close=")" separator=",">
      #{no}
    </foreach>
    AND P.FIRST_ENGAGE_ID>0
    AND R.REGISTRATION_NUMBER_ID>0
    ORDER BY R.EFFECTIVE_DATE
    ) A
    GROUP BY A.REGISTRATION_NUMBER_ID
  </select>

  <select id="getRegistrationNumber" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
    T_REGISTRATION_NUMBER
    WHERE MANAGE_CATEGORY_LEVEL = #{level, jdbcType=INTEGER}
  </select>

  <select id="queryByName" resultMap="BaseResultMap">
    SELECT
    MANUFACTURER_ID,
    MANUFACTURER_NAME
    FROM T_MANUFACTURER WHERE 1=1
    AND IS_DELETE =0
    AND MANUFACTURER_NAME like concat('%',#{name,jdbcType=VARCHAR},'%')
    ORDER BY MANUFACTURER_ID DESC
  </select>

  <select id="selectBymanufacturerId" resultType="java.lang.String">
    SELECT
    MANUFACTURER_NAME
    FROM T_MANUFACTURER
    where
    MANUFACTURER_ID = #{manufacturerId,jdbcType=INTEGER}
  </select>
</mapper>