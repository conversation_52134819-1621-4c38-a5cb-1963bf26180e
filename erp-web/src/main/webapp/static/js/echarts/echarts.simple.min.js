!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.echarts=e():t.echarts=e()}(this,function(){return function(t){function e(i){if(n[i])return n[i].exports;var r=n[i]={exports:{},id:i,loaded:!1};return t[i].call(r.exports,r,r.exports,e),r.loaded=!0,r.exports}var n={};return e.m=t,e.c=n,e.p="",e(0)}([function(t,e,n){t.exports=n(2),n(113),n(107),n(117),n(32)},function(t,e){function n(t){if(null==t||"object"!=typeof t)return t;var e=t,i=V.call(t);if("[object Array]"===i){e=[];for(var r=0,a=t.length;r<a;r++)e[r]=n(t[r])}else if(F[i]){var o=t.constructor;if(t.constructor.from)e=o.from(t);else{e=new o(t.length);for(var r=0,a=t.length;r<a;r++)e[r]=n(t[r])}}else if(!R[i]&&!E(t)&&!M(t)){e={};for(var s in t)t.hasOwnProperty(s)&&(e[s]=n(t[s]))}return e}function i(t,e,r){if(!S(e)||!S(t))return r?n(e):t;for(var a in e)if(e.hasOwnProperty(a)){var o=t[a],s=e[a];!S(s)||!S(o)||_(s)||_(o)||M(s)||M(o)||T(s)||T(o)||E(s)||E(o)?!r&&a in t||(t[a]=n(e[a],!0)):i(o,s,r)}return t}function r(t,e){for(var n=t[0],r=1,a=t.length;r<a;r++)n=i(n,t[r],e);return n}function a(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n]);return t}function o(t,e,n){for(var i in e)e.hasOwnProperty(i)&&(n?null!=e[i]:null==t[i])&&(t[i]=e[i]);return t}function s(){return document.createElement("canvas")}function l(){return B||(B=$.createCanvas().getContext("2d")),B}function h(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function u(t,e){function n(){}var i=t.prototype;n.prototype=e.prototype,t.prototype=new n;for(var r in i)t.prototype[r]=i[r];t.prototype.constructor=t,t.superClass=e}function c(t,e,n){t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,o(t,e,n)}function f(t){if(t)return"string"!=typeof t&&"number"==typeof t.length}function d(t,e,n){if(t&&e)if(t.forEach&&t.forEach===H)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var a in t)t.hasOwnProperty(a)&&e.call(n,t[a],a,t)}function p(t,e,n){if(t&&e){if(t.map&&t.map===j)return t.map(e,n);for(var i=[],r=0,a=t.length;r<a;r++)i.push(e.call(n,t[r],r,t));return i}}function g(t,e,n,i){if(t&&e){if(t.reduce&&t.reduce===X)return t.reduce(e,n,i);for(var r=0,a=t.length;r<a;r++)n=e.call(i,n,t[r],r,t);return n}}function v(t,e,n){if(t&&e){if(t.filter&&t.filter===G)return t.filter(e,n);for(var i=[],r=0,a=t.length;r<a;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}}function m(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]}function y(t,e){var n=q.call(arguments,2);return function(){return t.apply(e,n.concat(q.call(arguments)))}}function x(t){var e=q.call(arguments,1);return function(){return t.apply(this,e.concat(q.call(arguments)))}}function _(t){return"[object Array]"===V.call(t)}function b(t){return"function"==typeof t}function w(t){return"[object String]"===V.call(t)}function S(t){var e=typeof t;return"function"===e||!!t&&"object"==e}function T(t){return!!R[V.call(t)]}function M(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function A(t){return t!==t}function I(t){for(var e=0,n=arguments.length;e<n;e++)if(null!=arguments[e])return arguments[e]}function C(t,e){return null!=t?t:e}function P(t,e,n){return null!=t?t:null!=e?e:n}function L(){return Function.call.apply(q,arguments)}function k(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function D(t,e){if(!t)throw new Error(e)}function O(t){t[U]=!0}function E(t){return t[U]}function z(t){t&&d(t,function(t,e){this.set(e,t)},this)}function N(t){return new z(t)}var B,R={"[object Function]":1,"[object RegExp]":1,"[object Date]":1,"[object Error]":1,"[object CanvasGradient]":1,"[object CanvasPattern]":1,"[object Image]":1,"[object Canvas]":1},F={"[object Int8Array]":1,"[object Uint8Array]":1,"[object Uint8ClampedArray]":1,"[object Int16Array]":1,"[object Uint16Array]":1,"[object Int32Array]":1,"[object Uint32Array]":1,"[object Float32Array]":1,"[object Float64Array]":1},V=Object.prototype.toString,W=Array.prototype,H=W.forEach,G=W.filter,q=W.slice,j=W.map,X=W.reduce,U="__ec_primitive__",Z="_ec_",Y=4;z.prototype={constructor:z,get:function(t){return this[Z+t]},set:function(t,e){return this[Z+t]=e,e},each:function(t,e){void 0!==e&&(t=y(t,e));for(var n in this)this.hasOwnProperty(n)&&t(this[n],n.slice(Y))},removeKey:function(t){delete this[Z+t]}};var $={inherits:u,mixin:c,clone:n,merge:i,mergeAll:r,extend:a,defaults:o,getContext:l,createCanvas:s,indexOf:h,slice:L,find:m,isArrayLike:f,each:d,map:p,reduce:g,filter:v,bind:y,curry:x,isArray:_,isString:w,isObject:S,isFunction:b,isBuiltInObject:T,isDom:M,eqNaN:A,retrieve:I,retrieve2:C,retrieve3:P,assert:D,setAsPrimitive:O,createHashMap:N,normalizeCssArray:k,noop:function(){}};t.exports=$},function(t,e,n){function i(t){return function(e,n,i){e=e&&e.toLowerCase(),F.prototype[t].call(this,e,n,i)}}function r(){F.call(this)}function a(t,e,n){function i(t,e){return t.prio-e.prio}n=n||{},"string"==typeof e&&(e=ht[e]),this.id,this.group,this._dom=t;var a=this._zr=N.init(t,{renderer:n.renderer||"canvas",devicePixelRatio:n.devicePixelRatio,width:n.width,height:n.height});this._throttledZrFlush=z.throttle(B.bind(a.flush,a),17);var e=B.clone(e);e&&C(e,!0),this._theme=e,this._chartsViews=[],this._chartsMap={},this._componentsViews=[],this._componentsMap={},this._coordSysMgr=new A,this._api=b(this),F.call(this),this._messageCenter=new r,this._initEvents(),this.resize=B.bind(this.resize,this),this._pendingActions=[],V(lt,i),V(at,i),a.animation.on("frame",this._onframe,this),B.setAsPrimitive(this)}function o(t,e,n){var i,r=this._model,a=this._coordSysMgr.getCoordinateSystems();e=E.parseFinder(r,e);for(var o=0;o<a.length;o++){var s=a[o];if(s[t]&&null!=(i=s[t](r,e,n)))return i}}function s(t,e,n,i,r){function a(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}var o=t._model;if(!i)return void W(t._componentsViews.concat(t._chartsViews),a);var s={};s[i+"Id"]=n[i+"Id"],s[i+"Index"]=n[i+"Index"],s[i+"Name"]=n[i+"Name"];var l={mainType:i,query:s};r&&(l.subType=r),o&&o.eachComponent(l,function(e,n){a(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])},t)}function l(t,e){var n=t.type,i=t.escapeConnect,r=it[n],a=r.actionInfo,o=(a.update||"update").split(":"),l=o.pop();o=null!=o[0]&&H(o[0]),this[$]=!0;var h=[t],u=!1;t.batch&&(u=!0,h=B.map(t.batch,function(e){return e=B.defaults(B.extend({},e),t),e.batch=null,e}));var c,f=[],d="highlight"===n||"downplay"===n;W(h,function(t){c=r.action(t,this._model,this._api),c=c||B.extend({},t),c.type=a.event||c.type,f.push(c),d?s(this,l,t,"series"):o&&s(this,l,t,o.main,o.sub)},this),"none"===l||d||o||(this[K]?(et.prepareAndUpdate.call(this,t),this[K]=!1):et[l].call(this,t)),c=u?{type:a.event||n,escapeConnect:i,batch:f}:f[0],this[$]=!1,!e&&this._messageCenter.trigger(c.type,c)}function h(t){for(var e=this._pendingActions;e.length;){var n=e.shift();l.call(this,n,t)}}function u(t){!t&&this.trigger("updated")}function c(t,e,n){var i=this._api;W(this._componentsViews,function(r){var a=r.__model;r[t](a,e,i,n),_(a,r)},this),e.eachSeries(function(r,a){var o=this._chartsMap[r.__viewId];o[t](r,e,i,n),_(r,o),x(r,o)},this),y(this._zr,e),W(st,function(t){t(e,i)})}function f(t,e){for(var n="component"===t,i=n?this._componentsViews:this._chartsViews,r=n?this._componentsMap:this._chartsMap,a=this._zr,o=0;o<i.length;o++)i[o].__alive=!1;e[n?"eachComponent":"eachSeries"](function(t,o){if(n){if("series"===t)return}else o=t;var s="_ec_"+o.id+"_"+o.type,l=r[s];if(!l){var h=H(o.type),u=n?k.getClass(h.main,h.sub):D.getClass(h.sub);if(!u)return;l=new u,l.init(e,this._api),r[s]=l,i.push(l),a.add(l.group)}o.__viewId=l.__id=s,l.__alive=!0,l.__model=o,l.group.__ecComponentInfo={mainType:o.mainType,index:o.componentIndex}},this);for(var o=0;o<i.length;){var s=i[o];s.__alive?o++:(a.remove(s.group),s.dispose(e,this._api),i.splice(o,1),delete r[s.__id],s.__id=s.group.__ecComponentInfo=null)}}function d(t,e){W(at,function(n){n.func(t,e)})}function p(t){var e={};t.eachSeries(function(t){var n=t.get("stack"),i=t.getData();if(n&&"list"===i.type){var r=e[n];e.hasOwnProperty(n)&&r&&(i.stackedOn=r),e[n]=i}})}function g(t,e){var n=this._api;W(lt,function(i){i.isLayout&&i.func(t,n,e)})}function v(t,e,n){var i=this._api;t.clearColorPalette(),t.eachSeries(function(t){t.clearColorPalette()}),W(lt,function(r){(!n||!r.isLayout)&&r.func(t,i,e)})}function m(t,e){var n=this._api;W(this._componentsViews,function(i){var r=i.__model;i.render(r,t,n,e),_(r,i)},this),W(this._chartsViews,function(t){t.__alive=!1},this),t.eachSeries(function(i,r){var a=this._chartsMap[i.__viewId];a.__alive=!0,a.render(i,t,n,e),a.group.silent=!!i.get("silent"),_(i,a),x(i,a)},this),y(this._zr,t),W(this._chartsViews,function(e){e.__alive||e.remove(t,n)},this)}function y(t,e){var n=t.storage,i=0;n.traverse(function(t){t.isGroup||i++}),i>e.get("hoverLayerThreshold")&&!S.node&&n.traverse(function(t){t.isGroup||(t.useHoverLayer=!0)})}function x(t,e){var n=0;e.group.traverse(function(t){"group"===t.type||t.ignore||n++});var i=+t.get("progressive"),r=n>t.get("progressiveThreshold")&&i&&!S.node;r&&e.group.traverse(function(t){t.isGroup||(t.progressive=r?Math.floor(n++/i):-1,r&&t.stopAnimation(!0))});var a=t.get("blendMode")||null;e.group.traverse(function(t){t.isGroup||t.setStyle("blend",a)})}function _(t,e){var n=t.get("z"),i=t.get("zlevel");e.group.traverse(function(t){"group"!==t.type&&(null!=n&&(t.z=n),null!=i&&(t.zlevel=i))})}function b(t){var e=t._coordSysMgr;return B.extend(new M(t),{getCoordinateSystems:B.bind(e.getCoordinateSystems,e),getComponentByElement:function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}}})}function w(t){function e(t,e){for(var n=0;n<t.length;n++){var i=t[n];i[a]=e}}var n=0,i=1,r=2,a="__connectUpdateStatus";B.each(rt,function(o,s){t._messageCenter.on(s,function(o){if(ft[t.group]&&t[a]!==n){if(o&&o.escapeConnect)return;var s=t.makeActionFromEvent(o),l=[];B.each(ct,function(e){e!==t&&e.group===t.group&&l.push(e)}),e(l,n),W(l,function(t){t[a]!==i&&t.dispatchAction(s)}),e(l,r)}})})}/*!
	 * ECharts, a javascript interactive chart library.
	 *
	 * Copyright (c) 2015, Baidu Inc.
	 * All rights reserved.
	 *
	 * LICENSE
	 * https://github.com/ecomfe/echarts/blob/master/LICENSE.txt
	 */
var S=n(10),T=n(144),M=n(106),A=n(26),I=n(145),C=n(152),P=n(13),L=n(17),k=n(68),D=n(30),O=n(3),E=n(5),z=n(37),N=n(93),B=n(1),R=n(22),F=n(23),V=n(52),W=B.each,H=P.parseClassType,G=1e3,q=5e3,j=1e3,X=2e3,U=3e3,Z=4e3,Y=5e3,$="__flagInMainProcess",Q="__hasGradientOrPatternBg",K="__optionUpdated",J=/^[a-zA-Z0-9_]+$/;r.prototype.on=i("on"),r.prototype.off=i("off"),r.prototype.one=i("one"),B.mixin(r,F);var tt=a.prototype;tt._onframe=function(){if(this[K]){var t=this[K].silent;this[$]=!0,et.prepareAndUpdate.call(this),this[$]=!1,this[K]=!1,h.call(this,t),u.call(this,t)}},tt.getDom=function(){return this._dom},tt.getZr=function(){return this._zr},tt.setOption=function(t,e,n){var i;if(B.isObject(e)&&(n=e.lazyUpdate,i=e.silent,e=e.notMerge),this[$]=!0,!this._model||e){var r=new I(this._api),a=this._theme,o=this._model=new T(null,null,a,r);o.init(null,null,a,r)}this._model.setOption(t,ot),n?(this[K]={silent:i},this[$]=!1):(et.prepareAndUpdate.call(this),this._zr.flush(),this[K]=!1,this[$]=!1,h.call(this,i),u.call(this,i))},tt.setTheme=function(){console.log("ECharts#setTheme() is DEPRECATED in ECharts 3.0")},tt.getModel=function(){return this._model},tt.getOption=function(){return this._model&&this._model.getOption()},tt.getWidth=function(){return this._zr.getWidth()},tt.getHeight=function(){return this._zr.getHeight()},tt.getDevicePixelRatio=function(){return this._zr.painter.dpr||window.devicePixelRatio||1},tt.getRenderedCanvas=function(t){if(S.canvasSupported){t=t||{},t.pixelRatio=t.pixelRatio||1,t.backgroundColor=t.backgroundColor||this._model.get("backgroundColor");var e=this._zr,n=e.storage.getDisplayList();return B.each(n,function(t){t.stopAnimation(!0)}),e.painter.getRenderedCanvas(t)}},tt.getDataURL=function(t){t=t||{};var e=t.excludeComponents,n=this._model,i=[],r=this;W(e,function(t){n.eachComponent({mainType:t},function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)})});var a=this.getRenderedCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return W(i,function(t){t.group.ignore=!1}),a},tt.getConnectedDataURL=function(t){if(S.canvasSupported){var e=this.group,n=Math.min,i=Math.max,r=1/0;if(ft[e]){var a=r,o=r,s=-r,l=-r,h=[],u=t&&t.pixelRatio||1;B.each(ct,function(r,u){if(r.group===e){var c=r.getRenderedCanvas(B.clone(t)),f=r.getDom().getBoundingClientRect();a=n(f.left,a),o=n(f.top,o),s=i(f.right,s),l=i(f.bottom,l),h.push({dom:c,left:f.left,top:f.top})}}),a*=u,o*=u,s*=u,l*=u;var c=s-a,f=l-o,d=B.createCanvas();d.width=c,d.height=f;var p=N.init(d);return W(h,function(t){var e=new O.Image({style:{x:t.left*u-a,y:t.top*u-o,image:t.dom}});p.add(e)}),p.refreshImmediately(),d.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}},tt.convertToPixel=B.curry(o,"convertToPixel"),tt.convertFromPixel=B.curry(o,"convertFromPixel"),tt.containPixel=function(t,e){var n,i=this._model;return t=E.parseFinder(i,t),B.each(t,function(t,i){i.indexOf("Models")>=0&&B.each(t,function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n|=!!r.containPoint(e);else if("seriesModels"===i){var a=this._chartsMap[t.__viewId];a&&a.containPoint&&(n|=a.containPoint(e,t))}},this)},this),!!n},tt.getVisual=function(t,e){var n=this._model;t=E.parseFinder(n,t,{defaultMainType:"series"});var i=t.seriesModel,r=i.getData(),a=t.hasOwnProperty("dataIndexInside")?t.dataIndexInside:t.hasOwnProperty("dataIndex")?r.indexOfRawIndex(t.dataIndex):null;return null!=a?r.getItemVisual(a,e):r.getVisual(e)},tt.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},tt.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]};var et={update:function(t){var e=this._model,n=this._api,i=this._coordSysMgr,r=this._zr;if(e){e.restoreData(),i.create(this._model,this._api),d.call(this,e,n),p.call(this,e),i.update(e,n),v.call(this,e,t),m.call(this,e,t);var a=e.get("backgroundColor")||"transparent",o=r.painter;if(o.isSingleCanvas&&o.isSingleCanvas())r.configLayer(0,{clearColor:a});else{if(!S.canvasSupported){var s=R.parse(a);a=R.stringify(s,"rgb"),0===s[3]&&(a="transparent")}a.colorStops||a.image?(r.configLayer(0,{clearColor:a}),this[Q]=!0,this._dom.style.background="transparent"):(this[Q]&&r.configLayer(0,{clearColor:null}),this[Q]=!1,this._dom.style.background=a)}W(st,function(t){t(e,n)})}},updateView:function(t){var e=this._model;e&&(e.eachSeries(function(t){t.getData().clearAllVisual()}),v.call(this,e,t),c.call(this,"updateView",e,t))},updateVisual:function(t){var e=this._model;e&&(e.eachSeries(function(t){t.getData().clearAllVisual()}),v.call(this,e,t,!0),c.call(this,"updateVisual",e,t))},updateLayout:function(t){var e=this._model;e&&(g.call(this,e,t),c.call(this,"updateLayout",e,t))},prepareAndUpdate:function(t){var e=this._model;f.call(this,"component",e),f.call(this,"chart",e),et.update.call(this,t)}};tt.resize=function(t){this[$]=!0,this._zr.resize(t);var e=this._model&&this._model.resetOption("media"),n=e?"prepareAndUpdate":"update";et[n].call(this),this._loadingFX&&this._loadingFX.resize(),this[$]=!1;var i=t&&t.silent;h.call(this,i),u.call(this,i)},tt.showLoading=function(t,e){if(B.isObject(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),ut[t]){var n=ut[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},tt.hideLoading=function(){this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null},tt.makeActionFromEvent=function(t){var e=B.extend({},t);return e.type=rt[t.type],e},tt.dispatchAction=function(t,e){if(B.isObject(e)||(e={silent:!!e}),it[t.type]&&this._model){if(this[$])return void this._pendingActions.push(t);l.call(this,t,e.silent),e.flush?this._zr.flush(!0):e.flush!==!1&&S.browser.weChat&&this._throttledZrFlush(),h.call(this,e.silent),u.call(this,e.silent)}},tt.on=i("on"),tt.off=i("off"),tt.one=i("one");var nt=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];tt._initEvents=function(){W(nt,function(t){this._zr.on(t,function(e){var n,i=this.getModel(),r=e.target;if("globalout"===t)n={};else if(r&&null!=r.dataIndex){var a=r.dataModel||i.getSeriesByIndex(r.seriesIndex);n=a&&a.getDataParams(r.dataIndex,r.dataType)||{}}else r&&r.eventData&&(n=B.extend({},r.eventData));n&&(n.event=e,n.type=t,this.trigger(t,n))},this)},this),W(rt,function(t,e){this._messageCenter.on(e,function(t){this.trigger(e,t)},this)},this)},tt.isDisposed=function(){return this._disposed},tt.clear=function(){this.setOption({series:[]},!0)},tt.dispose=function(){if(!this._disposed){this._disposed=!0;var t=this._api,e=this._model;W(this._componentsViews,function(n){n.dispose(e,t)}),W(this._chartsViews,function(n){n.dispose(e,t)}),this._zr.dispose(),delete ct[this.id]}},B.mixin(a,F);var it={},rt={},at=[],ot=[],st=[],lt=[],ht={},ut={},ct={},ft={},dt=new Date-0,pt=new Date-0,gt="_echarts_instance_",vt={version:"3.7.2",dependencies:{zrender:"3.6.2"}};vt.init=function(t,e,n){var i=vt.getInstanceByDom(t);if(i)return i;var r=new a(t,e,n);return r.id="ec_"+dt++,ct[r.id]=r,t.setAttribute?t.setAttribute(gt,r.id):t[gt]=r.id,w(r),r},vt.connect=function(t){if(B.isArray(t)){var e=t;t=null,B.each(e,function(e){null!=e.group&&(t=e.group)}),t=t||"g_"+pt++,B.each(e,function(e){e.group=t})}return ft[t]=!0,t},vt.disConnect=function(t){ft[t]=!1},vt.disconnect=vt.disConnect,vt.dispose=function(t){"string"==typeof t?t=ct[t]:t instanceof a||(t=vt.getInstanceByDom(t)),t instanceof a&&!t.isDisposed()&&t.dispose()},vt.getInstanceByDom=function(t){var e;return e=t.getAttribute?t.getAttribute(gt):t[gt],ct[e]},vt.getInstanceById=function(t){return ct[t]},vt.registerTheme=function(t,e){ht[t]=e},vt.registerPreprocessor=function(t){ot.push(t)},vt.registerProcessor=function(t,e){"function"==typeof t&&(e=t,t=G),at.push({prio:t,func:e})},vt.registerPostUpdate=function(t){st.push(t)},vt.registerAction=function(t,e,n){"function"==typeof e&&(n=e,e="");var i=B.isObject(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,B.assert(J.test(i)&&J.test(e)),it[i]||(it[i]={action:n,actionInfo:t}),rt[e]=i},vt.registerCoordinateSystem=function(t,e){A.register(t,e)},vt.getCoordinateSystemDimensions=function(t){var e=A.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},vt.registerLayout=function(t,e){"function"==typeof t&&(e=t,t=j),lt.push({prio:t,func:e,isLayout:!0})},vt.registerVisual=function(t,e){"function"==typeof t&&(e=t,t=U),lt.push({prio:t,func:e})},vt.registerLoading=function(t,e){ut[t]=e},vt.extendComponentModel=function(t){return P.extend(t)},vt.extendComponentView=function(t){return k.extend(t)},vt.extendSeriesModel=function(t){return L.extend(t)},vt.extendChartView=function(t){return D.extend(t)},vt.setCanvasCreator=function(t){B.createCanvas=t},vt.registerVisual(X,n(158)),vt.registerPreprocessor(C),vt.registerLoading("default",n(143)),vt.registerAction({type:"highlight",event:"highlight",update:"highlight"},B.noop),vt.registerAction({type:"downplay",event:"downplay",update:"downplay"},B.noop),vt.zrender=N,vt.List=n(14),vt.Model=n(11),vt.Axis=n(33),vt.graphic=n(3),vt.number=n(4),vt.format=n(7),vt.throttle=z.throttle,vt.matrix=n(19),vt.vector=n(6),vt.color=n(22),vt.util={},W(["map","each","filter","indexOf","inherits","reduce","filter","bind","curry","isArray","isString","isObject","isFunction","extend","defaults","clone","merge"],function(t){vt.util[t]=B[t]}),vt.helper=n(142),vt.PRIORITY={PROCESSOR:{FILTER:G,STATISTIC:q},VISUAL:{LAYOUT:j,GLOBAL:X,CHART:U,COMPONENT:Z,BRUSH:Y}},t.exports=vt},function(t,e,n){"use strict";function i(t){return null!=t&&"none"!=t}function r(t){return"string"==typeof t?M.lift(t,-.1):t}function a(t){if(t.__hoverStlDirty){var e=t.style.stroke,n=t.style.fill,a=t.__hoverStl;a.fill=a.fill||(i(n)?r(n):null),a.stroke=a.stroke||(i(e)?r(e):null);var o={};for(var s in a)null!=a[s]&&(o[s]=t.style[s]);t.__normalStl=o,t.__hoverStlDirty=!1}}function o(t){if(!t.__isHover){if(a(t),t.useHoverLayer)t.__zr&&t.__zr.addHover(t,t.__hoverStl);else{var e=t.style,n=e.insideRollbackOpt;n&&_(e),e.extendFrom(t.__hoverStl),n&&(x(e,e.insideOriginalTextPosition,n),null==e.textFill&&(e.textFill=n.autoColor)),t.dirty(!1),t.z2+=1}t.__isHover=!0}}function s(t){if(t.__isHover){var e=t.__normalStl;t.useHoverLayer?t.__zr&&t.__zr.removeHover(t):(e&&t.setStyle(e),t.z2-=1),t.__isHover=!1}}function l(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&o(t)}):o(t)}function h(t){"group"===t.type?t.traverse(function(t){"group"!==t.type&&s(t)}):s(t)}function u(t,e){t.__hoverStl=t.hoverStyle||e||{},t.__hoverStlDirty=!0,t.__isHover&&a(t)}function c(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&l(this)}function f(t){this.__hoverSilentOnTouch&&t.zrByTouch||!this.__isEmphasis&&h(this)}function d(){this.__isEmphasis=!0,l(this)}function p(){this.__isEmphasis=!1,h(this)}function g(t,e,n,i){if(n=n||O,n.isRectText){var r=e.getShallow("position")||(i?null:"inside");"outside"===r&&(r="top"),t.textPosition=r,t.textOffset=e.getShallow("offset");var a=e.getShallow("rotate");null!=a&&(a*=Math.PI/180),t.textRotation=a,t.textDistance=w.retrieve2(e.getShallow("distance"),i?null:5)}var o,s=e.ecModel,l=s&&s.option.textStyle,h=v(e);if(h){o={};for(var u in h)if(h.hasOwnProperty(u)){var c=e.getModel(["rich",u]);m(o[u]={},c,l,n,i)}}return t.rich=o,m(t,e,l,n,i,!0),n.forceRich&&!n.textStyle&&(n.textStyle={}),t}function v(t){for(var e;t&&t!==t.ecModel;){var n=(t.option||O).rich;if(n){e=e||{};for(var i in n)n.hasOwnProperty(i)&&(e[i]=1)}t=t.parentModel}return e}function m(t,e,n,i,r,a){if(n=!r&&n||O,t.textFill=y(e.getShallow("color"),i)||n.color,t.textStroke=y(e.getShallow("textBorderColor"),i)||n.textBorderColor,t.textStrokeWidth=w.retrieve2(e.getShallow("textBorderWidth"),n.textBorderWidth),!r){if(a){var o=t.textPosition;t.insideRollback=x(t,o,i),t.insideOriginalTextPosition=o,t.insideRollbackOpt=i}null==t.textFill&&(t.textFill=i.autoColor)}t.fontStyle=e.getShallow("fontStyle")||n.fontStyle,t.fontWeight=e.getShallow("fontWeight")||n.fontWeight,t.fontSize=e.getShallow("fontSize")||n.fontSize,t.fontFamily=e.getShallow("fontFamily")||n.fontFamily,t.textAlign=e.getShallow("align"),t.textVerticalAlign=e.getShallow("verticalAlign")||e.getShallow("baseline"),t.textLineHeight=e.getShallow("lineHeight"),t.textWidth=e.getShallow("width"),t.textHeight=e.getShallow("height"),t.textTag=e.getShallow("tag"),a&&i.disableBox||(t.textBackgroundColor=y(e.getShallow("backgroundColor"),i),t.textPadding=e.getShallow("padding"),t.textBorderColor=y(e.getShallow("borderColor"),i),t.textBorderWidth=e.getShallow("borderWidth"),t.textBorderRadius=e.getShallow("borderRadius"),t.textBoxShadowColor=e.getShallow("shadowColor"),t.textBoxShadowBlur=e.getShallow("shadowBlur"),t.textBoxShadowOffsetX=e.getShallow("shadowOffsetX"),t.textBoxShadowOffsetY=e.getShallow("shadowOffsetY")),t.textShadowColor=e.getShallow("textShadowColor")||n.textShadowColor,t.textShadowBlur=e.getShallow("textShadowBlur")||n.textShadowBlur,t.textShadowOffsetX=e.getShallow("textShadowOffsetX")||n.textShadowOffsetX,t.textShadowOffsetY=e.getShallow("textShadowOffsetY")||n.textShadowOffsetY}function y(t,e){return"auto"!==t?t:e&&e.autoColor?e.autoColor:null}function x(t,e,n){var i,r=n.useInsideStyle;return null==t.textFill&&r!==!1&&(r===!0||n.isRectText&&e&&"string"==typeof e&&e.indexOf("inside")>=0)&&(i={textFill:null,textStroke:t.textStroke,textStrokeWidth:t.textStrokeWidth},t.textFill="#fff",null==t.textStroke&&(t.textStroke=n.autoColor,null==t.textStrokeWidth&&(t.textStrokeWidth=2))),i}function _(t){var e=t.insideRollback;e&&(t.textFill=e.textFill,t.textStroke=e.textStroke,t.textStrokeWidth=e.textStrokeWidth)}function b(t,e,n,i,r,a){"function"==typeof r&&(a=r,r=null);var o=i&&i.isAnimationEnabled();if(o){var s=t?"Update":"",l=i.getShallow("animationDuration"+s),h=i.getShallow("animationEasing"+s),u=i.getShallow("animationDelay"+s);"function"==typeof u&&(u=u(r,i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null)),"function"==typeof l&&(l=l(r)),l>0?e.animateTo(n,l,u||0,h,a,!!a):(e.stopAnimation(),e.attr(n),a&&a())}else e.stopAnimation(),e.attr(n),a&&a()}var w=n(1),S=n(186),T=n(8),M=n(22),A=n(19),I=n(6),C=n(61),P=n(12),L=Math.round,k=Math.max,D=Math.min,O={},E={};E.Group=n(36),E.Image=n(55),E.Text=n(91),E.Circle=n(177),E.Sector=n(183),E.Ring=n(182),E.Polygon=n(179),E.Polyline=n(180),E.Rect=n(181),E.Line=n(178),E.BezierCurve=n(176),E.Arc=n(175),E.CompoundPath=n(171),E.LinearGradient=n(105),E.RadialGradient=n(172),E.BoundingRect=P,E.extendShape=function(t){return T.extend(t)},E.extendPath=function(t,e){return S.extendFromString(t,e)},E.makePath=function(t,e,n,i){var r=S.createFromString(t,e),a=r.getBoundingRect();if(n){var o=a.width/a.height;if("center"===i){var s,l=n.height*o;l<=n.width?s=n.height:(l=n.width,s=l/o);var h=n.x+n.width/2,u=n.y+n.height/2;n.x=h-l/2,n.y=u-s/2,n.width=l,n.height=s}E.resizePath(r,n)}return r},E.mergePath=S.mergePath,E.resizePath=function(t,e){if(t.applyTransform){var n=t.getBoundingRect(),i=n.calculateTransform(e);t.applyTransform(i)}},E.subPixelOptimizeLine=function(t){var e=t.shape,n=t.style.lineWidth;return L(2*e.x1)===L(2*e.x2)&&(e.x1=e.x2=z(e.x1,n,!0)),L(2*e.y1)===L(2*e.y2)&&(e.y1=e.y2=z(e.y1,n,!0)),t},E.subPixelOptimizeRect=function(t){var e=t.shape,n=t.style.lineWidth,i=e.x,r=e.y,a=e.width,o=e.height;return e.x=z(e.x,n,!0),e.y=z(e.y,n,!0),e.width=Math.max(z(i+a,n,!1)-e.x,0===a?0:1),e.height=Math.max(z(r+o,n,!1)-e.y,0===o?0:1),t};var z=E.subPixelOptimize=function(t,e,n){var i=L(2*t);return(i+L(e))%2===0?i/2:(i+(n?1:-1))/2};E.setHoverStyle=function(t,e,n){t.__hoverSilentOnTouch=n&&n.hoverSilentOnTouch,"group"===t.type?t.traverse(function(t){"group"!==t.type&&u(t,e)}):u(t,e),t.on("mouseover",c).on("mouseout",f),t.on("emphasis",d).on("normal",p)},E.setLabelStyle=function(t,e,n,i,r,a,o){r=r||O;var s=r.labelFetcher,l=r.labelDataIndex,h=r.labelDimIndex,u=n.getShallow("show"),c=i.getShallow("show"),f=u||c?w.retrieve2(s?s.getFormattedLabel(l,"normal",null,h):null,r.defaultText):null,d=u?f:null,p=c?w.retrieve2(s?s.getFormattedLabel(l,"emphasis",null,h):null,f):null;null==d&&null==p||(N(t,n,a,r),N(e,i,o,r,!0)),t.text=d,e.text=p};var N=E.setTextStyle=function(t,e,n,i,r){return g(t,e,i,r),n&&w.extend(t,n),t.host&&t.host.dirty&&t.host.dirty(!1),t};E.setText=function(t,e,n){var i,r={isRectText:!0};n===!1?i=!0:r.autoColor=n,g(t,e,r,i),t.host&&t.host.dirty&&t.host.dirty(!1)},E.getFont=function(t,e){var n=e||e.getModel("textStyle");return[t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" ")},E.updateProps=function(t,e,n,i,r){b(!0,t,e,n,i,r)},E.initProps=function(t,e,n,i,r){b(!1,t,e,n,i,r)},E.getTransform=function(t,e){for(var n=A.identity([]);t&&t!==e;)A.mul(n,t.getLocalTransform(),n),t=t.parent;return n},E.applyTransform=function(t,e,n){return e&&!w.isArrayLike(e)&&(e=C.getLocalTransform(e)),n&&(e=A.invert([],e)),I.applyTransform([],t,e)},E.transformDirection=function(t,e,n){var i=0===e[4]||0===e[5]||0===e[0]?1:Math.abs(2*e[4]/e[0]),r=0===e[4]||0===e[5]||0===e[2]?1:Math.abs(2*e[4]/e[2]),a=["left"===t?-i:"right"===t?i:0,"top"===t?-r:"bottom"===t?r:0];return a=E.applyTransform(a,e,n),Math.abs(a[0])>Math.abs(a[1])?a[0]>0?"right":"left":a[1]>0?"bottom":"top"},E.groupTransition=function(t,e,n,i){function r(t){var e={};return t.traverse(function(t){!t.isGroup&&t.anid&&(e[t.anid]=t)}),e}function a(t){var e={position:I.clone(t.position),rotation:t.rotation};return t.shape&&(e.shape=w.extend({},t.shape)),e}if(t&&e){var o=r(t);e.traverse(function(t){if(!t.isGroup&&t.anid){var e=o[t.anid];if(e){var i=a(t);t.attr(a(e)),E.updateProps(t,i,n,t.dataIndex)}}})}},E.clipPointsByRect=function(t,e){return w.map(t,function(t){var n=t[0];n=k(n,e.x),n=D(n,e.x+e.width);var i=t[1];return i=k(i,e.y),i=D(i,e.y+e.height),[n,i]})},E.clipRectByRect=function(t,e){var n=k(t.x,e.x),i=D(t.x+t.width,e.x+e.width),r=k(t.y,e.y),a=D(t.y+t.height,e.y+e.height);if(i>=n&&a>=r)return{x:n,y:r,width:i-n,height:a-r}},E.createIcon=function(t,e,n){e=w.extend({rectHover:!0},e);var i=e.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(i.image=t.slice(8),w.defaults(i,n),new E.Image(e)):E.makePath(t.replace("path://",""),e,n,"center")},t.exports=E},function(t,e,n){function i(t){return t.replace(/^\s+/,"").replace(/\s+$/,"")}function r(t){return Math.floor(Math.log(t)/Math.LN10)}var a=n(1),o={},s=1e-4;o.linearMap=function(t,e,n,i){var r=e[1]-e[0],a=n[1]-n[0];if(0===r)return 0===a?n[0]:(n[0]+n[1])/2;if(i)if(r>0){if(t<=e[0])return n[0];if(t>=e[1])return n[1]}else{if(t>=e[0])return n[0];if(t<=e[1])return n[1]}else{if(t===e[0])return n[0];if(t===e[1])return n[1]}return(t-e[0])/r*a+n[0]},o.parsePercent=function(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return"string"==typeof t?i(t).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t},o.round=function(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t},o.asc=function(t){return t.sort(function(t,e){return t-e}),t},o.getPrecision=function(t){if(t=+t,isNaN(t))return 0;for(var e=1,n=0;Math.round(t*e)/e!==t;)e*=10,n++;return n},o.getPrecisionSafe=function(t){var e=t.toString(),n=e.indexOf("e");if(n>0){var i=+e.slice(n+1);return i<0?-i:0}var r=e.indexOf(".");return r<0?0:e.length-1-r},o.getPixelPrecision=function(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),a=Math.round(n(Math.abs(e[1]-e[0]))/i),o=Math.min(Math.max(-r+a,0),20);return isFinite(o)?o:20},o.getPercentWithPrecision=function(t,e,n){if(!t[e])return 0;var i=a.reduce(t,function(t,e){return t+(isNaN(e)?0:e)},0);if(0===i)return 0;for(var r=Math.pow(10,n),o=a.map(t,function(t){return(isNaN(t)?0:t)/i*r*100}),s=100*r,l=a.map(o,function(t){return Math.floor(t)}),h=a.reduce(l,function(t,e){return t+e},0),u=a.map(o,function(t,e){return t-l[e]});h<s;){for(var c=Number.NEGATIVE_INFINITY,f=null,d=0,p=u.length;d<p;++d)u[d]>c&&(c=u[d],f=d);++l[f],u[f]=0,++h}return l[e]/r},o.MAX_SAFE_INTEGER=9007199254740991,o.remRadian=function(t){var e=2*Math.PI;return(t%e+e)%e},o.isRadianAroundZero=function(t){return t>-s&&t<s};var l=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d\d)(?::(\d\d)(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;o.parseDate=function(t){if(t instanceof Date)return t;if("string"==typeof t){var e=l.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,+e[7]||0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,+e[7]||0)}return null==t?new Date(NaN):new Date(Math.round(t))},o.quantity=function(t){return Math.pow(10,r(t))},o.nice=function(t,e){var n,i=r(t),a=Math.pow(10,i),o=t/a;return n=e?o<1.5?1:o<2.5?2:o<4?3:o<7?5:10:o<1?1:o<2?2:o<3?3:o<5?5:10,t=n*a,i>=-20?+t.toFixed(i<0?-i:0):t},o.reformIntervals=function(t){function e(t,n,i){return t.interval[i]<n.interval[i]||t.interval[i]===n.interval[i]&&(t.close[i]-n.close[i]===(i?-1:1)||!i&&e(t,n,1))}t.sort(function(t,n){return e(t,n,0)?-1:1});for(var n=-(1/0),i=1,r=0;r<t.length;){for(var a=t[r].interval,o=t[r].close,s=0;s<2;s++)a[s]<=n&&(a[s]=n,o[s]=s?1:1-i),n=a[s],i=o[s];a[0]===a[1]&&o[0]*o[1]!==1?t.splice(r,1):r++}return t},o.isNumeric=function(t){return t-parseFloat(t)>=0},t.exports=o},function(t,e,n){function i(t,e){return t&&t.hasOwnProperty(e)}var r=n(7),a=n(4),o=n(11),s=n(1),l=s.each,h=s.isObject,u={};u.normalizeToArray=function(t){return t instanceof Array?t:null==t?[]:[t]},u.defaultEmphasis=function(t,e){if(t)for(var n=t.emphasis=t.emphasis||{},i=t.normal=t.normal||{},r=0,a=e.length;r<a;r++){var o=e[r];!n.hasOwnProperty(o)&&i.hasOwnProperty(o)&&(n[o]=i[o])}},u.TEXT_STYLE_OPTIONS=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"],u.getDataItemValue=function(t){return t&&(null==t.value?t:t.value)},u.isDataItemOption=function(t){return h(t)&&!(t instanceof Array)},u.converDataValue=function(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"===n&&"number"!=typeof t&&null!=t&&"-"!==t&&(t=+a.parseDate(t)),null==t||""===t?NaN:+t)},u.createDataFormatModel=function(t,e){var n=new o;return s.mixin(n,u.dataFormatMixin),n.seriesIndex=e.seriesIndex,n.name=e.name||"",n.mainType=e.mainType,n.subType=e.subType,n.getData=function(){return t},n},u.dataFormatMixin={getDataParams:function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),a=n.getRawIndex(t),o=n.getName(t,!0),s=n.getRawDataItem(t),l=n.getItemVisual(t,"color");return{componentType:this.mainType,componentSubType:this.subType,seriesType:"series"===this.mainType?this.subType:null,seriesIndex:this.seriesIndex,seriesId:this.id,seriesName:this.name,name:o,dataIndex:a,data:s,dataType:e,value:i,color:l,marker:r.getTooltipMarker(l),$vars:["seriesName","name","value"]}},getFormattedLabel:function(t,e,n,i,a){e=e||"normal";var o=this.getData(n),s=o.getItemModel(t),l=this.getDataParams(t,n);null!=i&&l.value instanceof Array&&(l.value=l.value[i]);var h=s.get([a||"label",e,"formatter"]);return"function"==typeof h?(l.status=e,h(l)):"string"==typeof h?r.formatTpl(h,l):void 0},getRawValue:function(t,e){var n=this.getData(e),i=n.getRawDataItem(t);if(null!=i)return!h(i)||i instanceof Array?i:i.value},formatTooltip:s.noop},u.mappingToExists=function(t,e){e=(e||[]).slice();var n=s.map(t||[],function(t,e){return{exist:t}});return l(e,function(t,i){if(h(t)){for(var r=0;r<n.length;r++)if(!n[r].option&&null!=t.id&&n[r].exist.id===t.id+"")return n[r].option=t,void(e[i]=null);for(var r=0;r<n.length;r++){var a=n[r].exist;if(!(n[r].option||null!=a.id&&null!=t.id||null==t.name||u.isIdInner(t)||u.isIdInner(a)||a.name!==t.name+""))return n[r].option=t,void(e[i]=null)}}}),l(e,function(t,e){if(h(t)){for(var i=0;i<n.length;i++){var r=n[i].exist;if(!n[i].option&&!u.isIdInner(r)&&null==t.id){n[i].option=t;break}}i>=n.length&&n.push({option:t})}}),n},u.makeIdAndName=function(t){var e=s.createHashMap();l(t,function(t,n){var i=t.exist;i&&e.set(i.id,t)}),l(t,function(t,n){var i=t.option;s.assert(!i||null==i.id||!e.get(i.id)||e.get(i.id)===t,"id duplicates: "+(i&&i.id)),i&&null!=i.id&&e.set(i.id,t),!t.keyInfo&&(t.keyInfo={})}),l(t,function(t,n){var i=t.exist,r=t.option,a=t.keyInfo;if(h(r)){if(a.name=null!=r.name?r.name+"":i?i.name:"\0-",i)a.id=i.id;else if(null!=r.id)a.id=r.id+"";else{var o=0;do a.id="\0"+a.name+"\0"+o++;while(e.get(a.id))}e.set(a.id,t)}})},u.isIdInner=function(t){return h(t)&&t.id&&0===(t.id+"").indexOf("\0_ec_\0")},u.compressBatches=function(t,e){function n(t,e,n){for(var i=0,r=t.length;i<r;i++)for(var a=t[i].seriesId,o=u.normalizeToArray(t[i].dataIndex),s=n&&n[a],l=0,h=o.length;l<h;l++){var c=o[l];s&&s[c]?s[c]=null:(e[a]||(e[a]={}))[c]=1}}function i(t,e){var n=[];for(var r in t)if(t.hasOwnProperty(r)&&null!=t[r])if(e)n.push(+r);else{var a=i(t[r],!0);a.length&&n.push({seriesId:r,dataIndex:a})}return n}var r={},a={};return n(t||[],r),n(e||[],a,r),[i(r),i(a)]},u.queryDataIndex=function(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?s.isArray(e.dataIndex)?s.map(e.dataIndex,function(e){return t.indexOfRawIndex(e)}):t.indexOfRawIndex(e.dataIndex):null!=e.name?s.isArray(e.name)?s.map(e.name,function(e){return t.indexOfName(e)}):t.indexOfName(e.name):void 0},u.makeGetter=function(){var t=0;return function(){var e="\0__ec_prop_getter_"+t++;return function(t){return t[e]||(t[e]={})}}}(),u.parseFinder=function(t,e,n){if(s.isString(e)){var r={};r[e+"Index"]=0,e=r}var a=n&&n.defaultMainType;!a||i(e,a+"Index")||i(e,a+"Id")||i(e,a+"Name")||(e[a+"Index"]=0);var o={};return l(e,function(i,r){var i=e[r];if("dataIndex"===r||"dataIndexInside"===r)return void(o[r]=i);var a=r.match(/^(\w+)(Index|Id|Name)$/)||[],l=a[1],h=(a[2]||"").toLowerCase();if(!(!l||!h||null==i||"index"===h&&"none"===i||n&&n.includeMainTypes&&s.indexOf(n.includeMainTypes,l)<0)){var u={mainType:l};"index"===h&&"all"===i||(u[h]=i);var c=t.queryComponents(u);o[l+"Models"]=c,o[l+"Model"]=c[0]}}),o},u.dataDimToCoordDim=function(t,e){var n=t.dimensions;e=t.getDimension(e);for(var i=0;i<n.length;i++){var r=t.getDimensionInfo(n[i]);if(r.name===e)return r.coordDim}},u.coordDimToDataDim=function(t,e){var n=[];return l(t.dimensions,function(i){var r=t.getDimensionInfo(i);r.coordDim===e&&(n[r.coordDimIndex]=r.name)}),n},u.otherDimToDataDim=function(t,e){var n=[];return l(t.dimensions,function(i){var r=t.getDimensionInfo(i),a=r.otherDims,o=a[e];null!=o&&o!==!1&&(n[o]=r.name)}),n},t.exports=u},function(t,e){var n="undefined"==typeof Float32Array?Array:Float32Array,i={create:function(t,e){var i=new n(2);return null==t&&(t=0),null==e&&(e=0),i[0]=t,i[1]=e,i},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:function(t){var e=new n(2);return e[0]=t[0],e[1]=t[1],e},set:function(t,e,n){return t[0]=e,t[1]=n,t},add:function(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t},scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},sub:function(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t},len:function(t){return Math.sqrt(this.lenSquare(t))},lenSquare:function(t){return t[0]*t[0]+t[1]*t[1]},mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:function(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t},normalize:function(t,e){var n=i.len(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t},distance:function(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))},distanceSquare:function(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])},negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:function(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t},applyTransform:function(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t},min:function(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t},max:function(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}};i.length=i.len,i.lengthSquare=i.lenSquare,i.dist=i.distance,i.distSquare=i.distanceSquare,t.exports=i},function(t,e,n){var i=n(1),r=n(4),a=n(16),o={};o.addCommas=function(t){return isNaN(t)?"-":(t=(t+"").split("."),t[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(t.length>1?"."+t[1]:""))},o.toCamelCase=function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,function(t,e){return e.toUpperCase()}),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},o.normalizeCssArray=i.normalizeCssArray;var s=o.encodeHTML=function(t){return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#39;")},l=["a","b","c","d","e","f","g"],h=function(t,e){return"{"+t+(null==e?"":e)+"}"};o.formatTpl=function(t,e,n){i.isArray(e)||(e=[e]);var r=e.length;if(!r)return"";for(var a=e[0].$vars||[],o=0;o<a.length;o++){var u=l[o],c=h(u,0);t=t.replace(h(u),n?s(c):c)}for(var f=0;f<r;f++)for(var d=0;d<a.length;d++){var c=e[f][a[d]];t=t.replace(h(l[d],f),n?s(c):c)}return t},o.formatTplSimple=function(t,e,n){return i.each(e,function(e,i){t=t.replace("{"+i+"}",n?s(e):e)}),t},o.getTooltipMarker=function(t,e){return t?'<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:'+o.encodeHTML(t)+";"+(e||"")+'"></span>':""};var u=function(t){return t<10?"0"+t:t};o.formatTime=function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=r.parseDate(e),a=n?"UTC":"",o=i["get"+a+"FullYear"](),s=i["get"+a+"Month"]()+1,l=i["get"+a+"Date"](),h=i["get"+a+"Hours"](),c=i["get"+a+"Minutes"](),f=i["get"+a+"Seconds"]();return t=t.replace("MM",u(s)).replace("M",s).replace("yyyy",o).replace("yy",o%100).replace("dd",u(l)).replace("d",l).replace("hh",u(h)).replace("h",h).replace("mm",u(c)).replace("m",c).replace("ss",u(f)).replace("s",f)},o.capitalFirst=function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},o.truncateText=a.truncateText,o.getTextRect=a.getBoundingRect,t.exports=o},function(t,e,n){function i(t){r.call(this,t),this.path=null}var r=n(38),a=n(1),o=n(27),s=n(168),l=n(75),h=l.prototype.getCanvasPattern,u=Math.abs,c=new o(!0);i.prototype={constructor:i,type:"path",__dirtyPath:!0,strokeContainThreshold:5,brush:function(t,e){var n=this.style,i=this.path||c,r=n.hasStroke(),a=n.hasFill(),o=n.fill,s=n.stroke,l=a&&!!o.colorStops,u=r&&!!s.colorStops,f=a&&!!o.image,d=r&&!!s.image;if(n.bind(t,this,e),this.setTransform(t),this.__dirty){var p;l&&(p=p||this.getBoundingRect(),this._fillGradient=n.getGradient(t,o,p)),u&&(p=p||this.getBoundingRect(),this._strokeGradient=n.getGradient(t,s,p))}l?t.fillStyle=this._fillGradient:f&&(t.fillStyle=h.call(o,t)),u?t.strokeStyle=this._strokeGradient:d&&(t.strokeStyle=h.call(s,t));var g=n.lineDash,v=n.lineDashOffset,m=!!t.setLineDash,y=this.getGlobalScale();i.setScale(y[0],y[1]),this.__dirtyPath||g&&!m&&r?(i.beginPath(t),g&&!m&&(i.setLineDash(g),i.setLineDashOffset(v)),this.buildPath(i,this.shape,!1),this.path&&(this.__dirtyPath=!1)):(t.beginPath(),this.path.rebuildPath(t)),a&&i.fill(t),g&&m&&(t.setLineDash(g),t.lineDashOffset=v),r&&i.stroke(t),g&&m&&t.setLineDash([]),this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this.getBoundingRect())},buildPath:function(t,e,n){},createPathProxy:function(){this.path=new o},getBoundingRect:function(){var t=this._rect,e=this.style,n=!t;if(n){var i=this.path;i||(i=this.path=new o),this.__dirtyPath&&(i.beginPath(),this.buildPath(i,this.shape,!1)),t=i.getBoundingRect()}if(this._rect=t,e.hasStroke()){var r=this._rectWithStroke||(this._rectWithStroke=t.clone());if(this.__dirty||n){r.copy(t);var a=e.lineWidth,s=e.strokeNoScale?this.getLineScale():1;
e.hasFill()||(a=Math.max(a,this.strokeContainThreshold||4)),s>1e-10&&(r.width+=a/s,r.height+=a/s,r.x-=a/s/2,r.y-=a/s/2)}return r}return t},contain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var a=this.path.data;if(r.hasStroke()){var o=r.lineWidth,l=r.strokeNoScale?this.getLineScale():1;if(l>1e-10&&(r.hasFill()||(o=Math.max(o,this.strokeContainThreshold)),s.containStroke(a,o/l,t,e)))return!0}if(r.hasFill())return s.contain(a,t,e)}return!1},dirty:function(t){null==t&&(t=!0),t&&(this.__dirtyPath=t,this._rect=null),this.__dirty=!0,this.__zr&&this.__zr.refresh(),this.__clipTarget&&this.__clipTarget.dirty()},animateShape:function(t){return this.animate("shape",t)},attrKV:function(t,e){"shape"===t?(this.setShape(e),this.__dirtyPath=!0,this._rect=null):r.prototype.attrKV.call(this,t,e)},setShape:function(t,e){var n=this.shape;if(n){if(a.isObject(t))for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);else n[t]=e;this.dirty(!0)}return this},getLineScale:function(){var t=this.transform;return t&&u(t[0]-1)>1e-10&&u(t[3]-1)>1e-10?Math.sqrt(u(t[0]*t[3]-t[2]*t[1])):1}},i.extend=function(t){var e=function(e){i.call(this,e),t.style&&this.style.extendFrom(t.style,!1);var n=t.shape;if(n){this.shape=this.shape||{};var r=this.shape;for(var a in n)!r.hasOwnProperty(a)&&n.hasOwnProperty(a)&&(r[a]=n[a])}t.init&&t.init.call(this,e)};a.inherits(e,i);for(var n in t)"style"!==n&&"shape"!==n&&(e.prototype[n]=t[n]);return e},a.inherits(i,r),t.exports=i},function(t,e,n){"use strict";function i(t,e,n,i,r){var a=0,o=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild(function(l,h){var u,c,f=l.position,d=l.getBoundingRect(),p=e.childAt(h+1),g=p&&p.getBoundingRect();if("horizontal"===t){var v=d.width+(g?-g.x+d.x:0);u=a+v,u>i||l.newline?(a=0,u=v,o+=s+n,s=d.height):s=Math.max(s,d.height)}else{var m=d.height+(g?-g.y+d.y:0);c=o+m,c>r||l.newline?(a+=s+n,o=0,c=m,s=d.width):s=Math.max(s,d.width)}l.newline||(f[0]=a,f[1]=o,"horizontal"===t?a=u+n:o=c+n)})}var r=n(1),a=n(12),o=n(4),s=n(7),l=o.parsePercent,h=r.each,u={},c=u.LOCATION_PARAMS=["left","right","top","bottom","width","height"],f=u.HV_NAMES=[["width","left","right"],["height","top","bottom"]];u.box=i,u.vbox=r.curry(i,"vertical"),u.hbox=r.curry(i,"horizontal"),u.getAvailableSize=function(t,e,n){var i=e.width,r=e.height,a=l(t.x,i),o=l(t.y,r),h=l(t.x2,i),u=l(t.y2,r);return(isNaN(a)||isNaN(parseFloat(t.x)))&&(a=0),(isNaN(h)||isNaN(parseFloat(t.x2)))&&(h=i),(isNaN(o)||isNaN(parseFloat(t.y)))&&(o=0),(isNaN(u)||isNaN(parseFloat(t.y2)))&&(u=r),n=s.normalizeCssArray(n||0),{width:Math.max(h-a-n[1]-n[3],0),height:Math.max(u-o-n[0]-n[2],0)}},u.getLayoutRect=function(t,e,n){n=s.normalizeCssArray(n||0);var i=e.width,r=e.height,o=l(t.left,i),h=l(t.top,r),u=l(t.right,i),c=l(t.bottom,r),f=l(t.width,i),d=l(t.height,r),p=n[2]+n[0],g=n[1]+n[3],v=t.aspect;switch(isNaN(f)&&(f=i-u-g-o),isNaN(d)&&(d=r-c-p-h),null!=v&&(isNaN(f)&&isNaN(d)&&(v>i/r?f=.8*i:d=.8*r),isNaN(f)&&(f=v*d),isNaN(d)&&(d=f/v)),isNaN(o)&&(o=i-u-f-g),isNaN(h)&&(h=r-c-d-p),t.left||t.right){case"center":o=i/2-f/2-n[3];break;case"right":o=i-f-g}switch(t.top||t.bottom){case"middle":case"center":h=r/2-d/2-n[0];break;case"bottom":h=r-d-p}o=o||0,h=h||0,isNaN(f)&&(f=i-g-o-(u||0)),isNaN(d)&&(d=r-p-h-(c||0));var m=new a(o+n[3],h+n[0],f,d);return m.margin=n,m},u.positionElement=function(t,e,n,i,o){var s=!o||!o.hv||o.hv[0],l=!o||!o.hv||o.hv[1],h=o&&o.boundingMode||"all";if(s||l){var c;if("raw"===h)c="group"===t.type?new a(0,0,+e.width||0,+e.height||0):t.getBoundingRect();else if(c=t.getBoundingRect(),t.needLocalTransform()){var f=t.getLocalTransform();c=c.clone(),c.applyTransform(f)}e=u.getLayoutRect(r.defaults({width:c.width,height:c.height},e),n,i);var d=t.position,p=s?e.x-c.x:0,g=l?e.y-c.y:0;t.attr("position","raw"===h?[p,g]:[d[0]+p,d[1]+g])}},u.sizeCalculable=function(t,e){return null!=t[f[e][0]]||null!=t[f[e][1]]&&null!=t[f[e][2]]},u.mergeLayoutParam=function(t,e,n){function i(n,i){var r={},s=0,u={},c=0,f=2;if(h(n,function(e){u[e]=t[e]}),h(n,function(t){a(e,t)&&(r[t]=u[t]=e[t]),o(r,t)&&s++,o(u,t)&&c++}),l[i])return o(e,n[1])?u[n[2]]=null:o(e,n[2])&&(u[n[1]]=null),u;if(c!==f&&s){if(s>=f)return r;for(var d=0;d<n.length;d++){var p=n[d];if(!a(r,p)&&a(t,p)){r[p]=t[p];break}}return r}return u}function a(t,e){return t.hasOwnProperty(e)}function o(t,e){return null!=t[e]&&"auto"!==t[e]}function s(t,e,n){h(t,function(t){e[t]=n[t]})}!r.isObject(n)&&(n={});var l=n.ignoreSize;!r.isArray(l)&&(l=[l,l]);var u=i(f[0],0),c=i(f[1],1);s(f[0],t,u),s(f[1],t,c)},u.getLayoutParams=function(t){return u.copyLayoutParams({},t)},u.copyLayoutParams=function(t,e){return e&&t&&h(c,function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t},t.exports=u},function(t,e){function n(t){var e={},n={},i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),a=t.match(/Edge\/([\d.]+)/),o=/micromessenger/i.test(t);return i&&(n.firefox=!0,n.version=i[1]),r&&(n.ie=!0,n.version=r[1]),a&&(n.edge=!0,n.version=a[1]),o&&(n.weChat=!0),{browser:n,os:e,node:!1,canvasSupported:!!document.createElement("canvas").getContext,touchEventsSupported:"ontouchstart"in window&&!n.ie&&!n.edge,pointerEventsSupported:"onpointerdown"in window&&(n.edge||n.ie&&n.version>=11)}}var i={};i="undefined"==typeof navigator?{browser:{},os:{},node:!0,canvasSupported:!0}:n(navigator.userAgent),t.exports=i},function(t,e,n){function i(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}function r(t,e,n){for(var i=0;i<e.length&&(!e[i]||(t=t&&"object"==typeof t?t[e[i]]:null,null!=t));i++);return null==t&&n&&(t=n.get(e)),t}function a(t,e){var n=s.get(t,"getParent");return n?n.call(t,e):t.parentModel}var o=n(1),s=n(15),l=n(10);i.prototype={constructor:i,init:null,mergeOption:function(t){o.merge(this.option,t,!0)},get:function(t,e){return null==t?this.option:r(this.option,this.parsePath(t),!e&&a(this,t))},getShallow:function(t,e){var n=this.option,i=null==n?n:n[t],r=!e&&a(this,t);return null==i&&r&&(i=r.getShallow(t)),i},getModel:function(t,e){var n,o=null==t?this.option:r(this.option,t=this.parsePath(t));return e=e||(n=a(this,t))&&n.getModel(t),new i(o,e,this.ecModel)},isEmpty:function(){return null==this.option},restoreData:function(){},clone:function(){var t=this.constructor;return new t(o.clone(this.option))},setReadOnly:function(t){s.setReadOnly(this,t)},parsePath:function(t){return"string"==typeof t&&(t=t.split(".")),t},customizeGetParent:function(t){s.set(this,"getParent",t)},isAnimationEnabled:function(){if(!l.node){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}}},s.enableClassExtend(i);var h=o.mixin;h(i,n(150)),h(i,n(147)),h(i,n(151)),h(i,n(149)),t.exports=i},function(t,e,n){"use strict";function i(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}var r=n(6),a=n(19),o=r.applyTransform,s=Math.min,l=Math.max;i.prototype={constructor:i,union:function(t){var e=s(t.x,this.x),n=s(t.y,this.y);this.width=l(t.x+t.width,this.x+this.width)-e,this.height=l(t.y+t.height,this.y+this.height)-n,this.x=e,this.y=n},applyTransform:function(){var t=[],e=[],n=[],i=[];return function(r){if(r){t[0]=n[0]=this.x,t[1]=i[1]=this.y,e[0]=i[0]=this.x+this.width,e[1]=n[1]=this.y+this.height,o(t,t,r),o(e,e,r),o(n,n,r),o(i,i,r),this.x=s(t[0],e[0],n[0],i[0]),this.y=s(t[1],e[1],n[1],i[1]);var a=l(t[0],e[0],n[0],i[0]),h=l(t[1],e[1],n[1],i[1]);this.width=a-this.x,this.height=h-this.y}}}(),calculateTransform:function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=a.create();return a.translate(r,r,[-e.x,-e.y]),a.scale(r,r,[n,i]),a.translate(r,r,[t.x,t.y]),r},intersect:function(t){if(!t)return!1;t instanceof i||(t=i.create(t));var e=this,n=e.x,r=e.x+e.width,a=e.y,o=e.y+e.height,s=t.x,l=t.x+t.width,h=t.y,u=t.y+t.height;return!(r<s||l<n||o<h||u<a)},contain:function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},clone:function(){return new i(this.x,this.y,this.width,this.height)},copy:function(t){this.x=t.x,this.y=t.y,this.width=t.width,this.height=t.height},plain:function(){return{x:this.x,y:this.y,width:this.width,height:this.height}}},i.create=function(t){return new i(t.x,t.y,t.width,t.height)},t.exports=i},function(t,e,n){function i(t){var e=[];return a.each(u.getClassesByMainType(t),function(t){o.apply(e,t.prototype.dependencies||[])}),a.map(e,function(t){return l.parseClassType(t).main})}var r=n(11),a=n(1),o=Array.prototype.push,s=n(50),l=n(15),h=n(9),u=r.extend({type:"component",id:"",name:"",mainType:"",subType:"",componentIndex:0,defaultOption:null,ecModel:null,dependentModels:[],uid:null,layoutMode:null,$constructor:function(t,e,n,i){r.call(this,t,e,n,i),this.uid=s.getUID("componentModel")},init:function(t,e,n,i){this.mergeDefaultAndTheme(t,n)},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,i=n?h.getLayoutParams(t):{},r=e.getTheme();a.merge(t,r.get(this.mainType)),a.merge(t,this.getDefaultOption()),n&&h.mergeLayoutParam(t,i,n)},mergeOption:function(t,e){a.merge(this.option,t,!0);var n=this.layoutMode;n&&h.mergeLayoutParam(this.option,t,n)},optionUpdated:function(t,e){},getDefaultOption:function(){if(!l.hasOwn(this,"__defaultOption")){for(var t=[],e=this.constructor;e;){var n=e.prototype.defaultOption;n&&t.push(n),e=e.superClass}for(var i={},r=t.length-1;r>=0;r--)i=a.merge(i,t[r],!0);l.set(this,"__defaultOption",i)}return l.get(this,"__defaultOption")},getReferringComponents:function(t){return this.ecModel.queryComponents({mainType:t,index:this.get(t+"Index",!0),id:this.get(t+"Id",!0)})}});l.enableClassManagement(u,{registerWhenExtend:!0}),s.enableSubTypeDefaulter(u),s.enableTopologicalTravel(u,i),a.mixin(u,n(148)),t.exports=u},function(t,e,n){(function(e){function i(t,e){p.each(m.concat(e.__wrappedMethods||[]),function(n){e.hasOwnProperty(n)&&(t[n]=e[n])}),t.__wrappedMethods=e.__wrappedMethods}function r(t){this._array=t||[]}function a(t){return p.isArray(t)||(t=[t]),t}function o(t,e){var n=t.dimensions,r=new y(p.map(n,t.getDimensionInfo,t),t.hostModel);i(r,t);for(var a=r._storage={},o=t._storage,s=0;s<n.length;s++){var l=n[s],h=o[l];p.indexOf(e,l)>=0?a[l]=new h.constructor(o[l].length):a[l]=o[l]}return r}var s="undefined",l="undefined"==typeof window?e:window,h=typeof l.Float64Array===s?Array:l.Float64Array,u=typeof l.Int32Array===s?Array:l.Int32Array,c={float:h,int:u,ordinal:Array,number:Array,time:Array},f=n(11),d=n(43),p=n(1),g=n(5),v=p.isObject,m=["stackedOn","hasItemOption","_nameList","_idList","_rawData"];r.prototype.pure=!1,r.prototype.count=function(){return this._array.length},r.prototype.getItem=function(t){return this._array[t]};var y=function(t,e){t=t||["x","y"];for(var n={},i=[],r=0;r<t.length;r++){var a,o={};"string"==typeof t[r]?(a=t[r],o={name:a,coordDim:a,coordDimIndex:0,stackable:!1,type:"number"}):(o=t[r],a=o.name,o.type=o.type||"number",o.coordDim||(o.coordDim=a,o.coordDimIndex=0)),o.otherDims=o.otherDims||{},i.push(a),n[a]=o}this.dimensions=i,this._dimensionInfos=n,this.hostModel=e,this.dataType,this.indices=[],this._storage={},this._nameList=[],this._idList=[],this._optionModels=[],this.stackedOn=null,this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._rawData,this._extent},x=y.prototype;x.type="list",x.hasItemOption=!0,x.getDimension=function(t){return isNaN(t)||(t=this.dimensions[t]||t),t},x.getDimensionInfo=function(t){return p.clone(this._dimensionInfos[this.getDimension(t)])},x.initData=function(t,e,n){t=t||[];var i=p.isArray(t);i&&(t=new r(t)),this._rawData=t;var a,o=this._storage={},s=this.indices=[],l=this.dimensions,h=this._dimensionInfos,u=t.count(),f=[],d={};e=e||[];for(var v=0;v<l.length;v++){var m=h[l[v]];0===m.otherDims.itemName&&(a=v);var y=c[m.type];o[l[v]]=new y(u)}var x=this;n||(x.hasItemOption=!1),n=n||function(t,e,n,i){var r=g.getDataItemValue(t);return g.isDataItemOption(t)&&(x.hasItemOption=!0),g.converDataValue(r instanceof Array?r[i]:r,h[e])};for(var v=0;v<u;v++){for(var _=t.getItem(v),b=0;b<l.length;b++){var w=l[b],S=o[w];S[v]=n(_,w,v,b)}s.push(v)}for(var v=0;v<u;v++){var _=t.getItem(v);!e[v]&&_&&(null!=_.name?e[v]=_.name:null!=a&&(e[v]=o[l[a]][v]));var T=e[v]||"",M=_&&_.id;!M&&T&&(d[T]=d[T]||0,M=T,d[T]>0&&(M+="__ec__"+d[T]),d[T]++),M&&(f[v]=M)}this._nameList=e,this._idList=f},x.count=function(){return this.indices.length},x.get=function(t,e,n){var i=this._storage,r=this.indices[e];if(null==r||!i[t])return NaN;var a=i[t][r];if(n){var o=this._dimensionInfos[t];if(o&&o.stackable)for(var s=this.stackedOn;s;){var l=s.get(t,e);(a>=0&&l>0||a<=0&&l<0)&&(a+=l),s=s.stackedOn}}return a},x.getValues=function(t,e,n){var i=[];p.isArray(t)||(n=e,e=t,t=this.dimensions);for(var r=0,a=t.length;r<a;r++)i.push(this.get(t[r],e,n));return i},x.hasValue=function(t){for(var e=this.dimensions,n=this._dimensionInfos,i=0,r=e.length;i<r;i++)if("ordinal"!==n[e[i]].type&&isNaN(this.get(e[i],t)))return!1;return!0},x.getDataExtent=function(t,e,n){t=this.getDimension(t);var i=this._storage[t],r=this.getDimensionInfo(t);e=r&&r.stackable&&e;var a,o=(this._extent||(this._extent={}))[t+!!e];if(o)return o;if(i){for(var s=1/0,l=-(1/0),h=0,u=this.count();h<u;h++)a=this.get(t,h,e),n&&!n(a,t,h)||(a<s&&(s=a),a>l&&(l=a));return this._extent[t+!!e]=[s,l]}return[1/0,-(1/0)]},x.getSum=function(t,e){var n=this._storage[t],i=0;if(n)for(var r=0,a=this.count();r<a;r++){var o=this.get(t,r,e);isNaN(o)||(i+=o)}return i},x.indexOf=function(t,e){var n=this._storage,i=n[t],r=this.indices;if(i)for(var a=0,o=r.length;a<o;a++){var s=r[a];if(i[s]===e)return a}return-1},x.indexOfName=function(t){for(var e=this.indices,n=this._nameList,i=0,r=e.length;i<r;i++){var a=e[i];if(n[a]===t)return i}return-1},x.indexOfRawIndex=function(t){var e=this.indices,n=e[t];if(null!=n&&n===t)return t;for(var i=0,r=e.length-1;i<=r;){var a=(i+r)/2|0;if(e[a]<t)i=a+1;else{if(!(e[a]>t))return a;r=a-1}}return-1},x.indicesOfNearest=function(t,e,n,i){var r=this._storage,a=r[t],o=[];if(!a)return o;null==i&&(i=1/0);for(var s=Number.MAX_VALUE,l=-1,h=0,u=this.count();h<u;h++){var c=e-this.get(t,h,n),f=Math.abs(c);c<=i&&f<=s&&((f<s||c>=0&&l<0)&&(s=f,l=c,o.length=0),o.push(h))}return o},x.getRawIndex=function(t){var e=this.indices[t];return null==e?-1:e},x.getRawDataItem=function(t){return this._rawData.getItem(this.getRawIndex(t))},x.getName=function(t){return this._nameList[this.indices[t]]||""},x.getId=function(t){return this._idList[this.indices[t]]||this.getRawIndex(t)+""},x.each=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),t=p.map(a(t),this.getDimension,this);var r=[],o=t.length,s=this.indices;i=i||this;for(var l=0;l<s.length;l++)switch(o){case 0:e.call(i,l);break;case 1:e.call(i,this.get(t[0],l,n),l);break;case 2:e.call(i,this.get(t[0],l,n),this.get(t[1],l,n),l);break;default:for(var h=0;h<o;h++)r[h]=this.get(t[h],l,n);r[h]=l,e.apply(i,r)}},x.filterSelf=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]),t=p.map(a(t),this.getDimension,this);var r=[],o=[],s=t.length,l=this.indices;i=i||this;for(var h=0;h<l.length;h++){var u;if(s)if(1===s)u=e.call(i,this.get(t[0],h,n),h);else{for(var c=0;c<s;c++)o[c]=this.get(t[c],h,n);o[c]=h,u=e.apply(i,o)}else u=e.call(i,h);u&&r.push(l[h])}return this.indices=r,this._extent={},this},x.mapArray=function(t,e,n,i){"function"==typeof t&&(i=n,n=e,e=t,t=[]);var r=[];return this.each(t,function(){r.push(e&&e.apply(this,arguments))},n,i),r},x.map=function(t,e,n,i){t=p.map(a(t),this.getDimension,this);var r=o(this,t),s=r.indices=this.indices,l=r._storage,h=[];return this.each(t,function(){var n=arguments[arguments.length-1],i=e&&e.apply(this,arguments);if(null!=i){"number"==typeof i&&(h[0]=i,i=h);for(var r=0;r<i.length;r++){var a=t[r],o=l[a],u=s[n];o&&(o[u]=i[r])}}},n,i),r},x.downSample=function(t,e,n,i){for(var r=o(this,[t]),a=this._storage,s=r._storage,l=this.indices,h=r.indices=[],u=[],c=[],f=Math.floor(1/e),d=s[t],p=this.count(),g=0;g<a[t].length;g++)s[t][g]=a[t][g];for(var g=0;g<p;g+=f){f>p-g&&(f=p-g,u.length=f);for(var v=0;v<f;v++){var m=l[g+v];u[v]=d[m],c[v]=m}var y=n(u),m=c[i(u,y)||0];d[m]=y,h.push(m)}return r},x.getItemModel=function(t){var e=this.hostModel;return t=this.indices[t],new f(this._rawData.getItem(t),e,e&&e.ecModel)},x.diff=function(t){var e,n=this._idList,i=t&&t._idList,r="e\0\0";return new d(t?t.indices:[],this.indices,function(t){return null!=(e=i[t])?e:r+t},function(t){return null!=(e=n[t])?e:r+t})},x.getVisual=function(t){var e=this._visual;return e&&e[t]},x.setVisual=function(t,e){if(v(t))for(var n in t)t.hasOwnProperty(n)&&this.setVisual(n,t[n]);else this._visual=this._visual||{},this._visual[t]=e},x.setLayout=function(t,e){if(v(t))for(var n in t)t.hasOwnProperty(n)&&this.setLayout(n,t[n]);else this._layout[t]=e},x.getLayout=function(t){return this._layout[t]},x.getItemLayout=function(t){return this._itemLayouts[t]},x.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?p.extend(this._itemLayouts[t]||{},e):e},x.clearItemLayouts=function(){this._itemLayouts.length=0},x.getItemVisual=function(t,e,n){var i=this._itemVisuals[t],r=i&&i[e];return null!=r||n?r:this.getVisual(e)},x.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};if(this._itemVisuals[t]=i,v(e))for(var r in e)e.hasOwnProperty(r)&&(i[r]=e[r]);else i[e]=n},x.clearAllVisual=function(){this._visual={},this._itemVisuals=[]};var _=function(t){t.seriesIndex=this.seriesIndex,t.dataIndex=this.dataIndex,t.dataType=this.dataType};x.setItemGraphicEl=function(t,e){var n=this.hostModel;e&&(e.dataIndex=t,e.dataType=this.dataType,e.seriesIndex=n&&n.seriesIndex,"group"===e.type&&e.traverse(_,e)),this._graphicEls[t]=e},x.getItemGraphicEl=function(t){return this._graphicEls[t]},x.eachItemGraphicEl=function(t,e){p.each(this._graphicEls,function(n,i){n&&t&&t.call(e,n,i)})},x.cloneShallow=function(){var t=p.map(this.dimensions,this.getDimensionInfo,this),e=new y(t,this.hostModel);return e._storage=this._storage,i(e,this),e.indices=this.indices.slice(),this._extent&&(e._extent=p.extend({},this._extent)),e},x.wrapMethod=function(t,e){var n=this[t];"function"==typeof n&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(p.slice(arguments)))})},x.TRANSFERABLE_METHODS=["cloneShallow","downSample","map"],x.CHANGABLE_METHODS=["filterSelf"],t.exports=y}).call(e,function(){return this}())},function(t,e,n){function i(t){o.assert(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(t),'componentType "'+t+'" illegal')}function r(t,e){var n=o.slice(arguments,2);return this.superClass.prototype[e].apply(t,n)}function a(t,e,n){return this.superClass.prototype[e].apply(t,n)}var o=n(1),s={},l=".",h="___EC__COMPONENT__CONTAINER___",u="\0ec_\0";s.set=function(t,e,n){return t[u+e]=n},s.get=function(t,e){return t[u+e]},s.hasOwn=function(t,e){return t.hasOwnProperty(u+e)};var c=s.parseClassType=function(t){var e={main:"",sub:""};return t&&(t=t.split(l),e.main=t[0]||"",e.sub=t[1]||""),e};s.enableClassExtend=function(t,e){t.$constructor=t,t.extend=function(t){var e=this,n=function(){t.$constructor?t.$constructor.apply(this,arguments):e.apply(this,arguments)};return o.extend(n.prototype,t),n.extend=this.extend,n.superCall=r,n.superApply=a,o.inherits(n,this),n.superClass=e,n}},s.enableClassManagement=function(t,e){function n(t){var e=r[t.main];return e&&e[h]||(e=r[t.main]={},e[h]=!0),e}e=e||{};var r={};if(t.registerClass=function(t,e){if(e)if(i(e),e=c(e),e.sub){if(e.sub!==h){var a=n(e);a[e.sub]=t}}else r[e.main]=t;return t},t.getClass=function(t,e,n){var i=r[t];if(i&&i[h]&&(i=e?i[e]:null),n&&!i)throw new Error(e?"Component "+t+"."+(e||"")+" not exists. Load it first.":t+".type should be specified.");return i},t.getClassesByMainType=function(t){t=c(t);var e=[],n=r[t.main];return n&&n[h]?o.each(n,function(t,n){n!==h&&e.push(t)}):e.push(n),e},t.hasClass=function(t){return t=c(t),!!r[t.main]},t.getAllClassMainTypes=function(){var t=[];return o.each(r,function(e,n){t.push(n)}),t},t.hasSubTypes=function(t){t=c(t);var e=r[t.main];return e&&e[h]},t.parseClassType=c,e.registerWhenExtend){var a=t.extend;a&&(t.extend=function(e){var n=a.call(this,e);return t.registerClass(n,e.type)})}return t},s.setReadOnly=function(t,e){},t.exports=s},function(t,e,n){function i(t,e){e=e||I;var n=t+":"+e;if(S[n])return S[n];for(var i=(t+"").split("\n"),r=0,a=0,o=i.length;a<o;a++)r=Math.max(L.measureText(i[a],e).width,r);return T>M&&(T=0,S={}),T++,S[n]=r,r}function r(t,e,n,i,r,s,l){return s?o(t,e,n,i,r,s,l):a(t,e,n,i,r,l)}function a(t,e,n,r,a,o){var h=v(t,e,a,o),u=i(t,e);a&&(u+=a[1]+a[3]);var c=h.outerHeight,f=s(0,u,n),d=l(0,c,r),p=new b(f,d,u,c);return p.lineHeight=h.lineHeight,p}function o(t,e,n,i,r,a,o){var h=m(t,{rich:a,truncate:o,font:e,textAlign:n,textPadding:r}),u=h.outerWidth,c=h.outerHeight,f=s(0,u,n),d=l(0,c,i);return new b(f,d,u,c)}function s(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function l(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function h(t,e,n){var i=e.x,r=e.y,a=e.height,o=e.width,s=a/2,l="left",h="top";switch(t){case"left":i-=n,r+=s,l="right",h="middle";break;case"right":i+=n+o,r+=s,h="middle";break;case"top":i+=o/2,r-=n,l="center",h="bottom";break;case"bottom":i+=o/2,r+=a+n,l="center";break;case"inside":i+=o/2,r+=s,l="center",h="middle";break;case"insideLeft":i+=n,r+=s,h="middle";break;case"insideRight":i+=o-n,r+=s,l="right",h="middle";break;case"insideTop":i+=o/2,r+=n,l="center";break;case"insideBottom":i+=o/2,r+=a-n,l="center",h="bottom";break;case"insideTopLeft":i+=n,r+=n;break;case"insideTopRight":i+=o-n,r+=n,l="right";break;case"insideBottomLeft":i+=n,r+=a-n,h="bottom";break;case"insideBottomRight":i+=o-n,r+=a-n,l="right",h="bottom"}return{x:i,y:r,textAlign:l,textVerticalAlign:h}}function u(t,e,n,i,r){if(!e)return"";var a=(t+"").split("\n");r=c(e,n,i,r);for(var o=0,s=a.length;o<s;o++)a[o]=f(a[o],r);return a.join("\n")}function c(t,e,n,r){r=_.extend({},r),r.font=e;var n=C(n,"...");r.maxIterations=C(r.maxIterations,2);var a=r.minChar=C(r.minChar,0);r.cnCharWidth=i("国",e);var o=r.ascCharWidth=i("a",e);r.placeholder=C(r.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<a&&s>=o;l++)s-=o;var h=i(n);return h>s&&(n="",h=0),s=t-h,r.ellipsis=n,r.ellipsisWidth=h,r.contentWidth=s,r.containerWidth=t,r}function f(t,e){var n=e.containerWidth,r=e.font,a=e.contentWidth;if(!n)return"";var o=i(t,r);if(o<=n)return t;for(var s=0;;s++){if(o<=a||s>=e.maxIterations){t+=e.ellipsis;break}var l=0===s?d(t,a,e.ascCharWidth,e.cnCharWidth):o>0?Math.floor(t.length*a/o):0;t=t.substr(0,l),o=i(t,r)}return""===t&&(t=e.placeholder),t}function d(t,e,n,i){for(var r=0,a=0,o=t.length;a<o&&r<e;a++){var s=t.charCodeAt(a);r+=0<=s&&s<=127?n:i}return a}function p(t){return i("国",t)}function g(t,e){var n=_.getContext();return n.font=e||I,n.measureText(t)}function v(t,e,n,i){null!=t&&(t+="");var r=p(e),a=t?t.split("\n"):[],o=a.length*r,s=o;if(n&&(s+=n[0]+n[2]),t&&i){var l=i.outerHeight,h=i.outerWidth;if(null!=l&&s>l)t="",a=[];else if(null!=h)for(var u=c(h-(n?n[1]+n[3]:0),e,i.ellipsis,{minChar:i.minChar,placeholder:i.placeholder}),d=0,g=a.length;d<g;d++)a[d]=f(a[d],u)}return{lines:a,height:o,outerHeight:s,lineHeight:r}}function m(t,e){var n={lines:[],width:0,height:0};if(null!=t&&(t+=""),!t)return n;for(var i,r=A.lastIndex=0;null!=(i=A.exec(t));){var a=i.index;a>r&&y(n,t.substring(r,a)),y(n,i[2],i[1]),r=A.lastIndex}r<t.length&&y(n,t.substring(r,t.length));var o=n.lines,s=0,l=0,h=[],c=e.textPadding,f=e.truncate,d=f&&f.outerWidth,p=f&&f.outerHeight;c&&(null!=d&&(d-=c[1]+c[3]),null!=p&&(p-=c[0]+c[2]));for(var g=0;g<o.length;g++){for(var v=o[g],m=0,x=0,_=0;_<v.tokens.length;_++){var b=v.tokens[_],S=b.styleName&&e.rich[b.styleName]||{},T=b.textPadding=S.textPadding,M=b.font=S.font||e.font,I=b.textHeight=C(S.textHeight,L.getLineHeight(M));if(T&&(I+=T[0]+T[2]),b.height=I,b.lineHeight=P(S.textLineHeight,e.textLineHeight,I),b.textAlign=S&&S.textAlign||e.textAlign,b.textVerticalAlign=S&&S.textVerticalAlign||"middle",null!=p&&s+b.lineHeight>p)return{lines:[],width:0,height:0};b.textWidth=L.getWidth(b.text,M);var k=S.textWidth,D=null==k||"auto"===k;if("string"==typeof k&&"%"===k.charAt(k.length-1))b.percentWidth=k,h.push(b),k=0;else{if(D){k=b.textWidth;var O=S.textBackgroundColor,E=O&&O.image;E&&(E=w.findExistImage(E),w.isImageReady(E)&&(k=Math.max(k,E.width*I/E.height)))}var z=T?T[1]+T[3]:0;k+=z;var N=null!=d?d-x:null;null!=N&&N<k&&(!D||N<z?(b.text="",b.textWidth=k=0):(b.text=u(b.text,N-z,M,f.ellipsis,{minChar:f.minChar}),b.textWidth=L.getWidth(b.text,M),k=b.textWidth+z))}x+=b.width=k,S&&(m=Math.max(m,b.lineHeight))}v.width=x,v.lineHeight=m,s+=m,l=Math.max(l,x)}n.outerWidth=n.width=C(e.textWidth,l),n.outerHeight=n.height=C(e.textHeight,s),c&&(n.outerWidth+=c[1]+c[3],n.outerHeight+=c[0]+c[2]);for(var g=0;g<h.length;g++){var b=h[g],B=b.percentWidth;b.width=parseInt(B,10)/100*l}return n}function y(t,e,n){for(var i=""===e,r=e.split("\n"),a=t.lines,o=0;o<r.length;o++){var s=r[o],l={styleName:n,text:s,isLineHolder:!s&&!i};if(o)a.push({tokens:[l]});else{var h=(a[a.length-1]||(a[0]={tokens:[]})).tokens,u=h.length;1===u&&h[0].isLineHolder?h[0]=l:(s||!u||i)&&h.push(l)}}}function x(t){return(t.fontSize||t.fontFamily)&&[t.fontStyle,t.fontWeight,(t.fontSize||12)+"px",t.fontFamily||"sans-serif"].join(" ")||t.textFont||t.font}var _=n(1),b=n(12),w=n(53),S={},T=0,M=5e3,A=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g,I="12px sans-serif",C=_.retrieve2,P=_.retrieve3,L={getWidth:i,getBoundingRect:r,adjustTextPositionOnRect:h,truncateText:u,measureText:g,getLineHeight:p,parsePlainText:v,parseRichText:m,adjustTextX:s,adjustTextY:l,makeFont:x,DEFAULT_FONT:I};t.exports=L},function(t,e,n){"use strict";var i=n(1),r=n(7),a=n(15),o=n(5),s=n(13),l=n(65),h=n(10),u=n(9),c=a.set,f=a.get,d=r.encodeHTML,p=r.addCommas,g=s.extend({type:"series.__base__",seriesIndex:0,coordinateSystem:null,defaultOption:null,legendDataProvider:null,visualColorAccessPath:"itemStyle.normal.color",layoutMode:null,init:function(t,e,n,i){this.seriesIndex=this.componentIndex,this.mergeDefaultAndTheme(t,n);var r=this.getInitialData(t,n);c(this,"dataBeforeProcessed",r),this.restoreData()},mergeDefaultAndTheme:function(t,e){var n=this.layoutMode,r=n?u.getLayoutParams(t):{},a=this.subType;s.hasClass(a)&&(a+="Series"),i.merge(t,e.getTheme().get(this.subType)),i.merge(t,this.getDefaultOption()),o.defaultEmphasis(t.label,["show"]),this.fillDataTextStyle(t.data),n&&u.mergeLayoutParam(t,r,n)},mergeOption:function(t,e){t=i.merge(this.option,t,!0),this.fillDataTextStyle(t.data);var n=this.layoutMode;n&&u.mergeLayoutParam(this.option,t,n);var r=this.getInitialData(t,e);r&&(c(this,"data",r),c(this,"dataBeforeProcessed",r.cloneShallow()))},fillDataTextStyle:function(t){if(t)for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&o.defaultEmphasis(t[n].label,e)},getInitialData:function(){},getData:function(t){var e=f(this,"data");return null==t?e:e.getLinkedData(t)},setData:function(t){c(this,"data",t)},getRawData:function(){return f(this,"dataBeforeProcessed")},coordDimToDataDim:function(t){return o.coordDimToDataDim(this.getData(),t)},dataDimToCoordDim:function(t){return o.dataDimToCoordDim(this.getData(),t)},getBaseAxis:function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},formatTooltip:function(t,e,n){function a(n){function a(t,n){var i=s.getDimensionInfo(n);if(i&&i.otherDims.tooltip!==!1){var a=i.type,o=(l?"- "+(i.tooltipName||i.name)+": ":"")+("ordinal"===a?t+"":"time"===a?e?"":r.formatTime("yyyy/MM/dd hh:mm:ss",t):p(t));o&&h.push(d(o))}}var l=i.reduce(n,function(t,e,n){var i=s.getDimensionInfo(n);return t|=i&&i.tooltip!==!1&&null!=i.tooltipName},0),h=[],u=o.otherDimToDataDim(s,"tooltip");return u.length?i.each(u,function(e){a(s.get(e,t),e)}):i.each(n,a),(l?"<br/>":"")+h.join(l?"<br/>":", ")}var s=f(this,"data"),l=this.getRawValue(t),h=i.isArray(l)?a(l):d(p(l)),u=s.getName(t),c=s.getItemVisual(t,"color");i.isObject(c)&&c.colorStops&&(c=(c.colorStops[0]||{}).color),c=c||"transparent";var g=r.getTooltipMarker(c),v=this.name;return"\0-"===v&&(v=""),v=v?d(v)+(e?": ":"<br/>"):"",e?g+v+h:v+g+(u?d(u)+": "+h:h)},isAnimationEnabled:function(){if(h.node)return!1;var t=this.getShallow("animation");return t&&this.getData().count()>this.getShallow("animationThreshold")&&(t=!1),t},restoreData:function(){c(this,"data",f(this,"dataBeforeProcessed").cloneShallow())},getColorFromPalette:function(t,e){var n=this.ecModel,i=l.getColorFromPalette.call(this,t,e);return i||(i=n.getColorFromPalette(t,e)),i},getAxisTooltipData:null,getTooltipPosition:null});i.mixin(g,o.dataFormatMixin),i.mixin(g,l),t.exports=g},function(t,e,n){var i=n(156),r=n(45);n(157),n(155);var a=n(34),o=n(4),s=n(1),l=n(16),h={};h.getScaleExtent=function(t,e){var n,i,r,a=t.type,l=e.getMin(),h=e.getMax(),u=null!=l,c=null!=h,f=t.getExtent();return"ordinal"===a?n=(e.get("data")||[]).length:(i=e.get("boundaryGap"),s.isArray(i)||(i=[i||0,i||0]),"boolean"==typeof i[0]&&(i=[0,0]),i[0]=o.parsePercent(i[0],1),i[1]=o.parsePercent(i[1],1),r=f[1]-f[0]||Math.abs(f[0])),null==l&&(l="ordinal"===a?n?0:NaN:f[0]-i[0]*r),null==h&&(h="ordinal"===a?n?n-1:NaN:f[1]+i[1]*r),"dataMin"===l?l=f[0]:"function"==typeof l&&(l=l({min:f[0],max:f[1]})),"dataMax"===h?h=f[1]:"function"==typeof h&&(h=h({min:f[0],max:f[1]})),(null==l||!isFinite(l))&&(l=NaN),(null==h||!isFinite(h))&&(h=NaN),t.setBlank(s.eqNaN(l)||s.eqNaN(h)),e.getNeedCrossZero()&&(l>0&&h>0&&!u&&(l=0),l<0&&h<0&&!c&&(h=0)),[l,h]},h.niceScaleExtent=function(t,e){var n=h.getScaleExtent(t,e),i=null!=e.getMin(),r=null!=e.getMax(),a=e.get("splitNumber");"log"===t.type&&(t.base=e.get("logBase"));var o=t.type;t.setExtent(n[0],n[1]),t.niceExtent({splitNumber:a,fixMin:i,fixMax:r,minInterval:"interval"===o||"time"===o?e.get("minInterval"):null,maxInterval:"interval"===o||"time"===o?e.get("maxInterval"):null});var s=e.get("interval");null!=s&&t.setInterval&&t.setInterval(s)},h.createScaleByModel=function(t,e){if(e=e||t.get("type"))switch(e){case"category":return new i(t.getCategories(),[1/0,-(1/0)]);case"value":return new r;default:return(a.getClass(e)||r).create(t)}},h.ifAxisCrossZero=function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)},h.getAxisLabelInterval=function(t,e,n,i){var r,a=0,o=0,s=1;e.length>40&&(s=Math.floor(e.length/40));for(var h=0;h<t.length;h+=s){var u=t[h],c=l.getBoundingRect(e[h],n,"center","top");c[i?"x":"y"]+=u,c[i?"width":"height"]*=1.3,r?r.intersect(c)?(o++,a=Math.max(a,o)):(r.union(c),o=0):r=c.clone()}return 0===a&&s>1?s:(a+1)*s-1},h.getFormattedLabels=function(t,e){var n=t.scale,i=n.getTicksLabels(),r=n.getTicks();return"string"==typeof e?(e=function(t){return function(e){return t.replace("{value}",null!=e?e:"")}}(e),s.map(i,e)):"function"==typeof e?s.map(r,function(n,i){return e(h.getAxisRawValue(t,n),i)},this):i},h.getAxisRawValue=function(t,e){return"category"===t.type?t.scale.getLabel(e):e},t.exports=h},function(t,e){var n="undefined"==typeof Float32Array?Array:Float32Array,i={create:function(){var t=new n(6);return i.identity(t),t},identity:function(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t},copy:function(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t},mul:function(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],a=e[0]*n[2]+e[2]*n[3],o=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=a,t[3]=o,t[4]=s,t[5]=l,t},translate:function(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t},rotate:function(t,e,n){var i=e[0],r=e[2],a=e[4],o=e[1],s=e[3],l=e[5],h=Math.sin(n),u=Math.cos(n);return t[0]=i*u+o*h,t[1]=-i*h+o*u,t[2]=r*u+s*h,t[3]=-r*h+u*s,t[4]=u*a+h*l,t[5]=u*l-h*a,t},scale:function(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t},invert:function(t,e){var n=e[0],i=e[2],r=e[4],a=e[1],o=e[3],s=e[5],l=n*o-a*i;return l?(l=1/l,t[0]=o*l,t[1]=-a*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-o*r)*l,t[5]=(a*r-n*s)*l,t):null}};t.exports=i},function(t,e,n){"use strict";function i(t){return t>-w&&t<w;
}function r(t){return t>w||t<-w}function a(t,e,n,i,r){var a=1-r;return a*a*(a*t+3*r*e)+r*r*(r*i+3*a*n)}function o(t,e,n,i,r){var a=1-r;return 3*(((e-t)*a+2*(n-e)*r)*a+(i-n)*r*r)}function s(t,e,n,r,a,o){var s=r+3*(e-n)-t,l=3*(n-2*e+t),h=3*(e-t),u=t-a,c=l*l-3*s*h,f=l*h-9*s*u,d=h*h-3*l*u,p=0;if(i(c)&&i(f))if(i(l))o[0]=0;else{var g=-h/l;g>=0&&g<=1&&(o[p++]=g)}else{var v=f*f-4*c*d;if(i(v)){var m=f/c,g=-l/s+m,y=-m/2;g>=0&&g<=1&&(o[p++]=g),y>=0&&y<=1&&(o[p++]=y)}else if(v>0){var x=b(v),w=c*l+1.5*s*(-f+x),S=c*l+1.5*s*(-f-x);w=w<0?-_(-w,M):_(w,M),S=S<0?-_(-S,M):_(S,M);var g=(-l-(w+S))/(3*s);g>=0&&g<=1&&(o[p++]=g)}else{var A=(2*c*l-3*s*f)/(2*b(c*c*c)),I=Math.acos(A)/3,C=b(c),P=Math.cos(I),g=(-l-2*C*P)/(3*s),y=(-l+C*(P+T*Math.sin(I)))/(3*s),L=(-l+C*(P-T*Math.sin(I)))/(3*s);g>=0&&g<=1&&(o[p++]=g),y>=0&&y<=1&&(o[p++]=y),L>=0&&L<=1&&(o[p++]=L)}}return p}function l(t,e,n,a,o){var s=6*n-12*e+6*t,l=9*e+3*a-3*t-9*n,h=3*e-3*t,u=0;if(i(l)){if(r(s)){var c=-h/s;c>=0&&c<=1&&(o[u++]=c)}}else{var f=s*s-4*l*h;if(i(f))o[0]=-s/(2*l);else if(f>0){var d=b(f),c=(-s+d)/(2*l),p=(-s-d)/(2*l);c>=0&&c<=1&&(o[u++]=c),p>=0&&p<=1&&(o[u++]=p)}}return u}function h(t,e,n,i,r,a){var o=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,h=(s-o)*r+o,u=(l-s)*r+s,c=(u-h)*r+h;a[0]=t,a[1]=o,a[2]=h,a[3]=c,a[4]=c,a[5]=u,a[6]=l,a[7]=i}function u(t,e,n,i,r,o,s,l,h,u,c){var f,d,p,g,v,m=.005,y=1/0;A[0]=h,A[1]=u;for(var _=0;_<1;_+=.05)I[0]=a(t,n,r,s,_),I[1]=a(e,i,o,l,_),g=x(A,I),g<y&&(f=_,y=g);y=1/0;for(var w=0;w<32&&!(m<S);w++)d=f-m,p=f+m,I[0]=a(t,n,r,s,d),I[1]=a(e,i,o,l,d),g=x(I,A),d>=0&&g<y?(f=d,y=g):(C[0]=a(t,n,r,s,p),C[1]=a(e,i,o,l,p),v=x(C,A),p<=1&&v<y?(f=p,y=v):m*=.5);return c&&(c[0]=a(t,n,r,s,f),c[1]=a(e,i,o,l,f)),b(y)}function c(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function f(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function d(t,e,n,a,o){var s=t-2*e+n,l=2*(e-t),h=t-a,u=0;if(i(s)){if(r(l)){var c=-h/l;c>=0&&c<=1&&(o[u++]=c)}}else{var f=l*l-4*s*h;if(i(f)){var c=-l/(2*s);c>=0&&c<=1&&(o[u++]=c)}else if(f>0){var d=b(f),c=(-l+d)/(2*s),p=(-l-d)/(2*s);c>=0&&c<=1&&(o[u++]=c),p>=0&&p<=1&&(o[u++]=p)}}return u}function p(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function g(t,e,n,i,r){var a=(e-t)*i+t,o=(n-e)*i+e,s=(o-a)*i+a;r[0]=t,r[1]=a,r[2]=s,r[3]=s,r[4]=o,r[5]=n}function v(t,e,n,i,r,a,o,s,l){var h,u=.005,f=1/0;A[0]=o,A[1]=s;for(var d=0;d<1;d+=.05){I[0]=c(t,n,r,d),I[1]=c(e,i,a,d);var p=x(A,I);p<f&&(h=d,f=p)}f=1/0;for(var g=0;g<32&&!(u<S);g++){var v=h-u,m=h+u;I[0]=c(t,n,r,v),I[1]=c(e,i,a,v);var p=x(I,A);if(v>=0&&p<f)h=v,f=p;else{C[0]=c(t,n,r,m),C[1]=c(e,i,a,m);var y=x(C,A);m<=1&&y<f?(h=m,f=y):u*=.5}}return l&&(l[0]=c(t,n,r,h),l[1]=c(e,i,a,h)),b(f)}var m=n(6),y=m.create,x=m.distSquare,_=Math.pow,b=Math.sqrt,w=1e-8,S=1e-4,T=b(3),M=1/3,A=y(),I=y(),C=y();t.exports={cubicAt:a,cubicDerivativeAt:o,cubicRootAt:s,cubicExtrema:l,cubicSubdivide:h,cubicProjectPoint:u,quadraticAt:c,quadraticDerivativeAt:f,quadraticRootAt:d,quadraticExtremum:p,quadraticSubdivide:g,quadraticProjectPoint:v}},function(t,e,n){"use strict";function i(t){return t.getBoundingClientRect?t.getBoundingClientRect():{left:0,top:0}}function r(t,e,n,i){return n=n||{},i||!c.canvasSupported?a(t,e,n):c.browser.firefox&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):a(t,e,n),n}function a(t,e,n){var r=i(t);n.zrX=e.clientX-r.left,n.zrY=e.clientY-r.top}function o(t,e,n){if(e=e||window.event,null!=e.zrX)return e;var i=e.type,a=i&&i.indexOf("touch")>=0;if(a){var o="touchend"!=i?e.targetTouches[0]:e.changedTouches[0];o&&r(t,o,e,n)}else r(t,e,e,n),e.zrDelta=e.wheelDelta?e.wheelDelta/120:-(e.detail||0)/3;var s=e.button;return null==e.which&&void 0!==s&&d.test(e.type)&&(e.which=1&s?1:2&s?3:4&s?2:0),e}function s(t,e,n){f?t.addEventListener(e,n):t.attachEvent("on"+e,n)}function l(t,e,n){f?t.removeEventListener(e,n):t.detachEvent("on"+e,n)}function h(t){return t.which>1}var u=n(23),c=n(10),f="undefined"!=typeof window&&!!window.addEventListener,d=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,p=f?function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}:function(t){t.returnValue=!1,t.cancelBubble=!0};t.exports={clientToLocal:r,normalizeEvent:o,addEventListener:s,removeEventListener:l,notLeftMouse:h,stop:p,Dispatcher:u}},function(t,e,n){function i(t){return t=Math.round(t),t<0?0:t>255?255:t}function r(t){return t=Math.round(t),t<0?0:t>360?360:t}function a(t){return t<0?0:t>1?1:t}function o(t){return i(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100*255:parseInt(t,10))}function s(t){return a(t.length&&"%"===t.charAt(t.length-1)?parseFloat(t)/100:parseFloat(t))}function l(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function h(t,e,n){return t+(e-t)*n}function u(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function c(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}function f(t,e){A&&c(A,e),A=M.put(t,A||e.slice())}function d(t,e){if(t){e=e||[];var n=M.get(t);if(n)return c(e,n);t+="";var i=t.replace(/ /g,"").toLowerCase();if(i in T)return c(e,T[i]),f(t,e),e;if("#"!==i.charAt(0)){var r=i.indexOf("("),a=i.indexOf(")");if(r!==-1&&a+1===i.length){var l=i.substr(0,r),h=i.substr(r+1,a-(r+1)).split(","),d=1;switch(l){case"rgba":if(4!==h.length)return void u(e,0,0,0,1);d=s(h.pop());case"rgb":return 3!==h.length?void u(e,0,0,0,1):(u(e,o(h[0]),o(h[1]),o(h[2]),d),f(t,e),e);case"hsla":return 4!==h.length?void u(e,0,0,0,1):(h[3]=s(h[3]),p(h,e),f(t,e),e);case"hsl":return 3!==h.length?void u(e,0,0,0,1):(p(h,e),f(t,e),e);default:return}}u(e,0,0,0,1)}else{if(4===i.length){var g=parseInt(i.substr(1),16);return g>=0&&g<=4095?(u(e,(3840&g)>>4|(3840&g)>>8,240&g|(240&g)>>4,15&g|(15&g)<<4,1),f(t,e),e):void u(e,0,0,0,1)}if(7===i.length){var g=parseInt(i.substr(1),16);return g>=0&&g<=16777215?(u(e,(16711680&g)>>16,(65280&g)>>8,255&g,1),f(t,e),e):void u(e,0,0,0,1)}}}}function p(t,e){var n=(parseFloat(t[0])%360+360)%360/360,r=s(t[1]),a=s(t[2]),o=a<=.5?a*(r+1):a+r-a*r,h=2*a-o;return e=e||[],u(e,i(255*l(h,o,n+1/3)),i(255*l(h,o,n)),i(255*l(h,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function g(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,a=t[2]/255,o=Math.min(i,r,a),s=Math.max(i,r,a),l=s-o,h=(s+o)/2;if(0===l)e=0,n=0;else{n=h<.5?l/(s+o):l/(2-s-o);var u=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,f=((s-a)/6+l/2)/l;i===s?e=f-c:r===s?e=1/3+u-f:a===s&&(e=2/3+c-u),e<0&&(e+=1),e>1&&(e-=1)}var d=[360*e,n,h];return null!=t[3]&&d.push(t[3]),d}}function v(t,e){var n=d(t);if(n){for(var i=0;i<3;i++)e<0?n[i]=n[i]*(1-e)|0:n[i]=(255-n[i])*e+n[i]|0;return w(n,4===n.length?"rgba":"rgb")}}function m(t,e){var n=d(t);if(n)return((1<<24)+(n[0]<<16)+(n[1]<<8)+ +n[2]).toString(16).slice(1)}function y(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var r=t*(e.length-1),o=Math.floor(r),s=Math.ceil(r),l=e[o],u=e[s],c=r-o;return n[0]=i(h(l[0],u[0],c)),n[1]=i(h(l[1],u[1],c)),n[2]=i(h(l[2],u[2],c)),n[3]=a(h(l[3],u[3],c)),n}}function x(t,e,n){if(e&&e.length&&t>=0&&t<=1){var r=t*(e.length-1),o=Math.floor(r),s=Math.ceil(r),l=d(e[o]),u=d(e[s]),c=r-o,f=w([i(h(l[0],u[0],c)),i(h(l[1],u[1],c)),i(h(l[2],u[2],c)),a(h(l[3],u[3],c))],"rgba");return n?{color:f,leftIndex:o,rightIndex:s,value:r}:f}}function _(t,e,n,i){if(t=d(t))return t=g(t),null!=e&&(t[0]=r(e)),null!=n&&(t[1]=s(n)),null!=i&&(t[2]=s(i)),w(p(t),"rgba")}function b(t,e){if(t=d(t),t&&null!=e)return t[3]=a(e),w(t,"rgba")}function w(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}var S=n(73),T={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]},M=new S(20),A=null;t.exports={parse:d,lift:v,toHex:m,fastLerp:y,fastMapToColor:y,lerp:x,mapToColor:x,modifyHSL:_,modifyAlpha:b,stringify:w}},function(t,e){var n=Array.prototype.slice,i=function(){this._$handlers={}};i.prototype={constructor:i,one:function(t,e,n){var i=this._$handlers;if(!e||!t)return this;i[t]||(i[t]=[]);for(var r=0;r<i[t].length;r++)if(i[t][r].h===e)return this;return i[t].push({h:e,one:!0,ctx:n||this}),this},on:function(t,e,n){var i=this._$handlers;if(!e||!t)return this;i[t]||(i[t]=[]);for(var r=0;r<i[t].length;r++)if(i[t][r].h===e)return this;return i[t].push({h:e,one:!1,ctx:n||this}),this},isSilent:function(t){var e=this._$handlers;return e[t]&&e[t].length},off:function(t,e){var n=this._$handlers;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,a=n[t].length;r<a;r++)n[t][r].h!=e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},trigger:function(t){if(this._$handlers[t]){var e=arguments,i=e.length;i>3&&(e=n.call(e,1));for(var r=this._$handlers[t],a=r.length,o=0;o<a;){switch(i){case 1:r[o].h.call(r[o].ctx);break;case 2:r[o].h.call(r[o].ctx,e[1]);break;case 3:r[o].h.call(r[o].ctx,e[1],e[2]);break;default:r[o].h.apply(r[o].ctx,e)}r[o].one?(r.splice(o,1),a--):o++}}return this},triggerWithContext:function(t){if(this._$handlers[t]){var e=arguments,i=e.length;i>4&&(e=n.call(e,1,e.length-1));for(var r=e[e.length-1],a=this._$handlers[t],o=a.length,s=0;s<o;){switch(i){case 1:a[s].h.call(r);break;case 2:a[s].h.call(r,e[1]);break;case 3:a[s].h.call(r,e[1],e[2]);break;default:a[s].h.apply(r,e)}a[s].one?(a.splice(s,1),o--):s++}}return this}},t.exports=i},function(t,e,n){"use strict";var i=n(3),r=n(12),a=i.extendShape({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i+a),t.lineTo(n-r,i+a),t.closePath()}}),o=i.extendShape({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,a=e.height/2;t.moveTo(n,i-a),t.lineTo(n+r,i),t.lineTo(n,i+a),t.lineTo(n-r,i),t.closePath()}}),s=i.extendShape({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,a=Math.max(r,e.height),o=r/2,s=o*o/(a-o),l=i-a+o+s,h=Math.asin(s/o),u=Math.cos(h)*o,c=Math.sin(h),f=Math.cos(h);t.arc(n,l,o,Math.PI-h,2*Math.PI+h);var d=.6*o,p=.7*o;t.bezierCurveTo(n+u-c*d,l+s+f*d,n,i-p,n,i),t.bezierCurveTo(n,i-p,n-u+c*d,l+s+f*d,n-u,l+s),t.closePath()}}),l=i.extendShape({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,a=e.y,o=i/3*2;t.moveTo(r,a),t.lineTo(r+o,a+n),t.lineTo(r,a+n/4*3),t.lineTo(r-o,a+n),t.lineTo(r,a),t.closePath()}}),h={line:i.Line,rect:i.Rect,roundRect:i.Rect,square:i.Rect,circle:i.Circle,diamond:o,pin:s,arrow:l,triangle:a},u={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var a=Math.min(n,i);r.x=t,r.y=e,r.width=a,r.height=a},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},c={};for(var f in h)h.hasOwnProperty(f)&&(c[f]=new h[f]);var d=i.extendShape({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},beforeBrush:function(){var t=this.style,e=this.shape;"pin"===e.symbolType&&"inside"===t.textPosition&&(t.textPosition=["50%","40%"],t.textAlign="center",t.textVerticalAlign="middle")},buildPath:function(t,e,n){var i=e.symbolType,r=c[i];"none"!==e.symbolType&&(r||(i="rect",r=c[i]),u[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n))}}),p=function(t){if("image"!==this.type){var e=this.style,n=this.shape;n&&"line"===n.symbolType?e.stroke=t:this.__isEmptyBrush?(e.stroke=t,e.fill="#fff"):(e.fill&&(e.fill=t),e.stroke&&(e.stroke=t)),this.dirty(!1)}},g={createSymbol:function(t,e,n,a,o,s){var l=0===t.indexOf("empty");l&&(t=t.substr(5,1).toLowerCase()+t.substr(6));var h;return h=0===t.indexOf("image://")?new i.Image({style:{image:t.slice(8),x:e,y:n,width:a,height:o}}):0===t.indexOf("path://")?i.makePath(t.slice(7),{},new r(e,n,a,o)):new d({shape:{symbolType:t,x:e,y:n,width:a,height:o}}),h.__isEmptyBrush=l,h.setColor=p,h.setColor(s),h}};t.exports=g},function(t,e,n){function i(t,e,n){function i(t,e,n){c[e]?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,v.set(e,!0))}function o(t,e,n){if(n||null!=e.get(t)){for(var i=0;null!=e.get(t+i);)i++;t+=i}return e.set(t,!0),t}e=e||[],n=n||{},t=(t||[]).slice();var d=(n.dimsDef||[]).slice(),p=a.createHashMap(n.encodeDef),g=a.createHashMap(),v=a.createHashMap(),m=[],y=n.dimCount;if(null==y){var x=r(e[0]);y=Math.max(a.isArray(x)&&x.length||1,t.length,d.length),s(t,function(t){var e=t.dimsDef;e&&(y=Math.max(y,e.length))})}for(var _=0;_<y;_++){var b=l(d[_])?{name:d[_]}:d[_]||{},w=b.name,S=m[_]={otherDims:{}};null!=w&&null==g.get(w)&&(S.name=S.tooltipName=w,g.set(w,_)),null!=b.type&&(S.type=b.type)}p.each(function(t,e){t=p.set(e,u(t).slice()),s(t,function(n,r){l(n)&&(n=g.get(n)),null!=n&&n<y&&(t[r]=n,i(m[n],e,r))})});var T=0;s(t,function(t,e){var n,t,r,o;l(t)?(n=t,t={}):(n=t.name,t=a.clone(t),r=t.dimsDef,o=t.otherDims,t.name=t.coordDim=t.coordDimIndex=t.dimsDef=t.otherDims=null);var c=u(p.get(n));if(!c.length)for(var f=0;f<(r&&r.length||1);f++){for(;T<m.length&&null!=m[T].coordDim;)T++;T<m.length&&c.push(T++)}s(c,function(e,a){var s=m[e];i(h(s,t),n,a),null==s.name&&r&&(s.name=s.tooltipName=r[a]),o&&h(s.otherDims,o)})});for(var M=n.extraPrefix||"value",A=0;A<y;A++){var S=m[A]=m[A]||{},I=S.coordDim;null==I&&(S.coordDim=o(M,v,n.extraFromZero),S.coordDimIndex=0,S.isExtraCoord=!0),null==S.name&&(S.name=o(S.coordDim,g)),null==S.type&&f(e,A)&&(S.type="ordinal")}return m}function r(t){return a.isArray(t)?t:a.isObject(t)?t.value:t}var a=n(1),o=n(5),s=a.each,l=a.isString,h=a.defaults,u=o.normalizeToArray,c={tooltip:1,label:1,itemName:1},f=i.guessOrdinal=function(t,e){for(var n=0,i=t.length;n<i;n++){var o=r(t[n]);if(!a.isArray(o))return!1;var o=o[e];if(null!=o&&isFinite(o)&&""!==o)return!1;if(l(o)&&"-"!==o)return!0}return!1};t.exports=i},function(t,e,n){"use strict";function i(){this._coordinateSystems=[]}var r=n(1),a={};i.prototype={constructor:i,create:function(t,e){var n=[];r.each(a,function(i,r){var a=i.create(t,e);n=n.concat(a||[])}),this._coordinateSystems=n},update:function(t,e){r.each(this._coordinateSystems,function(n){n.update&&n.update(t,e)})},getCoordinateSystems:function(){return this._coordinateSystems.slice()}},i.register=function(t,e){a[t]=e},i.get=function(t){return a[t]},t.exports=i},function(t,e,n){"use strict";var i=n(20),r=n(6),a=n(90),o=n(12),s=n(35).devicePixelRatio,l={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},h=[],u=[],c=[],f=[],d=Math.min,p=Math.max,g=Math.cos,v=Math.sin,m=Math.sqrt,y=Math.abs,x="undefined"!=typeof Float32Array,_=function(t){this._saveData=!t,this._saveData&&(this.data=[]),this._ctx=null};_.prototype={constructor:_,_xi:0,_yi:0,_x0:0,_y0:0,_ux:0,_uy:0,_len:0,_lineDash:null,_dashOffset:0,_dashIdx:0,_dashSum:0,setScale:function(t,e){this._ux=y(1/s/t)||0,this._uy=y(1/s/e)||0},getContext:function(){return this._ctx},beginPath:function(t){return this._ctx=t,t&&t.beginPath(),t&&(this.dpr=t.dpr),this._saveData&&(this._len=0),this._lineDash&&(this._lineDash=null,this._dashOffset=0),this},moveTo:function(t,e){return this.addData(l.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},lineTo:function(t,e){var n=y(t-this._xi)>this._ux||y(e-this._yi)>this._uy||this._len<5;return this.addData(l.L,t,e),this._ctx&&n&&(this._needsDash()?this._dashedLineTo(t,e):this._ctx.lineTo(t,e)),n&&(this._xi=t,this._yi=e),this},bezierCurveTo:function(t,e,n,i,r,a){return this.addData(l.C,t,e,n,i,r,a),this._ctx&&(this._needsDash()?this._dashedBezierTo(t,e,n,i,r,a):this._ctx.bezierCurveTo(t,e,n,i,r,a)),this._xi=r,this._yi=a,this},quadraticCurveTo:function(t,e,n,i){return this.addData(l.Q,t,e,n,i),this._ctx&&(this._needsDash()?this._dashedQuadraticTo(t,e,n,i):this._ctx.quadraticCurveTo(t,e,n,i)),this._xi=n,this._yi=i,this},arc:function(t,e,n,i,r,a){return this.addData(l.A,t,e,n,n,i,r-i,0,a?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,a),this._xi=g(r)*n+t,this._yi=v(r)*n+t,this},arcTo:function(t,e,n,i,r){return this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},rect:function(t,e,n,i){return this._ctx&&this._ctx.rect(t,e,n,i),this.addData(l.R,t,e,n,i),this},closePath:function(){this.addData(l.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&(this._needsDash()&&this._dashedLineTo(e,n),t.closePath()),this._xi=e,this._yi=n,this},fill:function(t){t&&t.fill(),this.toStatic()},stroke:function(t){t&&t.stroke(),this.toStatic()},setLineDash:function(t){if(t instanceof Array){this._lineDash=t,this._dashIdx=0;for(var e=0,n=0;n<t.length;n++)e+=t[n];this._dashSum=e}return this},setLineDashOffset:function(t){return this._dashOffset=t,this},len:function(){return this._len},setData:function(t){var e=t.length;this.data&&this.data.length==e||!x||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},appendPath:function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();x&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(var r=0;r<e;r++)for(var a=t[r].data,o=0;o<a.length;o++)this.data[i++]=a[o];this._len=i},addData:function(t){if(this._saveData){var e=this.data;this._len+arguments.length>e.length&&(this._expandData(),e=this.data);for(var n=0;n<arguments.length;n++)e[this._len++]=arguments[n];this._prevCmd=t}},_expandData:function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},_needsDash:function(){return this._lineDash},_dashedLineTo:function(t,e){var n,i,r=this._dashSum,a=this._dashOffset,o=this._lineDash,s=this._ctx,l=this._xi,h=this._yi,u=t-l,c=e-h,f=m(u*u+c*c),g=l,v=h,y=o.length;for(u/=f,c/=f,a<0&&(a=r+a),a%=r,g-=a*u,v-=a*c;u>0&&g<=t||u<0&&g>=t||0==u&&(c>0&&v<=e||c<0&&v>=e);)i=this._dashIdx,n=o[i],g+=u*n,v+=c*n,this._dashIdx=(i+1)%y,u>0&&g<l||u<0&&g>l||c>0&&v<h||c<0&&v>h||s[i%2?"moveTo":"lineTo"](u>=0?d(g,t):p(g,t),c>=0?d(v,e):p(v,e));u=g-t,c=v-e,this._dashOffset=-m(u*u+c*c)},_dashedBezierTo:function(t,e,n,r,a,o){var s,l,h,u,c,f=this._dashSum,d=this._dashOffset,p=this._lineDash,g=this._ctx,v=this._xi,y=this._yi,x=i.cubicAt,_=0,b=this._dashIdx,w=p.length,S=0;for(d<0&&(d=f+d),d%=f,s=0;s<1;s+=.1)l=x(v,t,n,a,s+.1)-x(v,t,n,a,s),h=x(y,e,r,o,s+.1)-x(y,e,r,o,s),_+=m(l*l+h*h);for(;b<w&&(S+=p[b],!(S>d));b++);for(s=(S-d)/_;s<=1;)u=x(v,t,n,a,s),c=x(y,e,r,o,s),b%2?g.moveTo(u,c):g.lineTo(u,c),s+=p[b]/_,b=(b+1)%w;b%2!==0&&g.lineTo(a,o),l=a-u,h=o-c,this._dashOffset=-m(l*l+h*h)},_dashedQuadraticTo:function(t,e,n,i){var r=n,a=i;n=(n+2*t)/3,i=(i+2*e)/3,t=(this._xi+2*t)/3,e=(this._yi+2*e)/3,this._dashedBezierTo(t,e,n,i,r,a)},toStatic:function(){var t=this.data;t instanceof Array&&(t.length=this._len,x&&(this.data=new Float32Array(t)))},getBoundingRect:function(){h[0]=h[1]=c[0]=c[1]=Number.MAX_VALUE,u[0]=u[1]=f[0]=f[1]=-Number.MAX_VALUE;for(var t=this.data,e=0,n=0,i=0,s=0,d=0;d<t.length;){var p=t[d++];switch(1==d&&(e=t[d],n=t[d+1],i=e,s=n),p){case l.M:i=t[d++],s=t[d++],e=i,n=s,c[0]=i,c[1]=s,f[0]=i,f[1]=s;break;case l.L:a.fromLine(e,n,t[d],t[d+1],c,f),e=t[d++],n=t[d++];break;case l.C:a.fromCubic(e,n,t[d++],t[d++],t[d++],t[d++],t[d],t[d+1],c,f),e=t[d++],n=t[d++];break;case l.Q:a.fromQuadratic(e,n,t[d++],t[d++],t[d],t[d+1],c,f),e=t[d++],n=t[d++];break;case l.A:var m=t[d++],y=t[d++],x=t[d++],_=t[d++],b=t[d++],w=t[d++]+b,S=(t[d++],1-t[d++]);1==d&&(i=g(b)*x+m,s=v(b)*_+y),a.fromArc(m,y,x,_,b,w,S,c,f),e=g(w)*x+m,n=v(w)*_+y;break;case l.R:i=e=t[d++],s=n=t[d++];var T=t[d++],M=t[d++];a.fromLine(i,s,i+T,s+M,c,f);break;case l.Z:e=i,n=s}r.min(h,h,c),r.max(u,u,f)}return 0===d&&(h[0]=h[1]=u[0]=u[1]=0),new o(h[0],h[1],u[0]-h[0],u[1]-h[1])},rebuildPath:function(t){for(var e,n,i,r,a,o,s=this.data,h=this._ux,u=this._uy,c=this._len,f=0;f<c;){var d=s[f++];switch(1==f&&(i=s[f],r=s[f+1],e=i,n=r),d){case l.M:e=i=s[f++],n=r=s[f++],t.moveTo(i,r);break;case l.L:a=s[f++],o=s[f++],(y(a-i)>h||y(o-r)>u||f===c-1)&&(t.lineTo(a,o),i=a,r=o);break;case l.C:t.bezierCurveTo(s[f++],s[f++],s[f++],s[f++],s[f++],s[f++]),i=s[f-2],r=s[f-1];break;case l.Q:t.quadraticCurveTo(s[f++],s[f++],s[f++],s[f++]),i=s[f-2],r=s[f-1];break;case l.A:var p=s[f++],m=s[f++],x=s[f++],_=s[f++],b=s[f++],w=s[f++],S=s[f++],T=s[f++],M=x>_?x:_,A=x>_?1:x/_,I=x>_?_/x:1,C=Math.abs(x-_)>.001,P=b+w;C?(t.translate(p,m),t.rotate(S),t.scale(A,I),t.arc(0,0,M,b,P,1-T),t.scale(1/A,1/I),t.rotate(-S),t.translate(-p,-m)):t.arc(p,m,M,b,P,1-T),1==f&&(e=g(b)*x+p,n=v(b)*_+m),i=g(P)*x+p,r=v(P)*_+m;break;case l.R:e=i=s[f],n=r=s[f+1],t.rect(s[f++],s[f++],s[f++],s[f++]);break;case l.Z:t.closePath(),i=e,r=n}}}},_.CMD=l,t.exports=_},function(t,e,n){"use strict";function i(t){for(var e=0;e<t.length&&null==t[e];)e++;return t[e]}function r(t){var e=i(t);return null!=e&&!c.isArray(p(e))}function a(t,e,n){t=t||[];var i=e.get("coordinateSystem"),a=v[i],o=d.get(i),s={encodeDef:e.get("encode"),dimsDef:e.get("dimensions")},m=a&&a(t,e,n,s),y=m&&m.dimensions;y||(y=o&&(o.getDimensionsInfo?o.getDimensionsInfo():o.dimensions.slice())||["x","y"],y=u(y,t,s));var x=m?m.categoryIndex:-1,_=new h(y,e),b=l(m,t),w={},S=x>=0&&r(t)?function(t,e,n,i){return f.isDataItemOption(t)&&(_.hasItemOption=!0),i===x?n:g(p(t),y[i])}:function(t,e,n,i){var r=p(t),a=g(r&&r[i],y[i]);f.isDataItemOption(t)&&(_.hasItemOption=!0);var o=m&&m.categoryAxesModels;return o&&o[e]&&"string"==typeof a&&(w[e]=w[e]||o[e].getCategories(),a=c.indexOf(w[e],a),a<0&&!isNaN(a)&&(a=+a)),a};return _.hasItemOption=!1,_.initData(t,b,S),_}function o(t){return"category"!==t&&"time"!==t}function s(t){return"category"===t?"ordinal":"time"===t?"time":"float"}function l(t,e){var n,i=[],r=t&&t.dimensions[t.categoryIndex];if(r&&(n=t.categoryAxesModels[r.name]),n){var a=n.getCategories();if(a){var o=e.length;if(c.isArray(e[0])&&e[0].length>1){i=[];for(var s=0;s<o;s++)i[s]=a[e[s][t.categoryIndex||0]]}else i=a.slice(0)}}return i}var h=n(14),u=n(25),c=n(1),f=n(5),d=n(26),p=f.getDataItemValue,g=f.converDataValue,v={cartesian2d:function(t,e,n,i){var r=c.map(["xAxis","yAxis"],function(t){return n.queryComponents({mainType:t,index:e.get(t+"Index"),id:e.get(t+"Id")})[0]}),a=r[0],l=r[1],h=a.get("type"),f=l.get("type"),d=[{name:"x",type:s(h),stackable:o(h)},{name:"y",type:s(f),stackable:o(f)}],p="category"===h,g="category"===f;d=u(d,t,i);var v={};return p&&(v.x=a),g&&(v.y=l),{dimensions:d,categoryIndex:p?0:g?1:-1,categoryAxesModels:v}},singleAxis:function(t,e,n,i){var r=n.queryComponents({mainType:"singleAxis",index:e.get("singleAxisIndex"),id:e.get("singleAxisId")})[0],a=r.get("type"),l="category"===a,h=[{name:"single",type:s(a),stackable:o(a)}];h=u(h,t,i);var c={};return l&&(c.single=r),{dimensions:h,categoryIndex:l?0:-1,categoryAxesModels:c}},polar:function(t,e,n,i){var r=n.queryComponents({mainType:"polar",index:e.get("polarIndex"),id:e.get("polarId")})[0],a=r.findAxisModel("angleAxis"),l=r.findAxisModel("radiusAxis"),h=l.get("type"),c=a.get("type"),f=[{name:"radius",type:s(h),stackable:o(h)},{name:"angle",type:s(c),stackable:o(c)}],d="category"===c,p="category"===h;f=u(f,t,i);var g={};return p&&(g.radius=l),d&&(g.angle=a),{dimensions:f,categoryIndex:d?1:p?0:-1,categoryAxesModels:g}},geo:function(t,e,n,i){return{dimensions:u([{name:"lng"},{name:"lat"}],t,i)}}};t.exports=a},,function(t,e,n){function i(){this.group=new o,this.uid=s.getUID("viewChart")}function r(t,e){if(t&&(t.trigger(e),"group"===t.type))for(var n=0;n<t.childCount();n++)r(t.childAt(n),e)}function a(t,e,n){var i=h.queryDataIndex(t,e);null!=i?u.each(h.normalizeToArray(i),function(e){r(t.getItemGraphicEl(e),n)}):t.eachItemGraphicEl(function(t){r(t,n)})}var o=n(36),s=n(50),l=n(15),h=n(5),u=n(1);i.prototype={type:"chart",init:function(t,e){},render:function(t,e,n,i){},highlight:function(t,e,n,i){a(t.getData(),i,"emphasis")},downplay:function(t,e,n,i){a(t.getData(),i,"normal")},remove:function(t,e){this.group.removeAll()},dispose:function(){}};var c=i.prototype;c.updateView=c.updateLayout=c.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},l.enableClassExtend(i,["dispose"]),l.enableClassManagement(i,{registerWhenExtend:!0}),t.exports=i},function(t,e,n){var i=n(1);t.exports=function(t){for(var e=0;e<t.length;e++)t[e][1]||(t[e][1]=t[e][0]);return function(e,n){for(var r={},a=0;a<t.length;a++){var o=t[a][1];if(!(e&&i.indexOf(e,o)>=0||n&&i.indexOf(n,o)<0)){var s=this.getShallow(o);null!=s&&(r[t[a][0]]=s)}}return r}}},function(t,e,n){"use strict";var i=n(3),r=n(1),a=n(2);n(60),n(122),a.extendComponentView({type:"grid",render:function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new i.Rect({shape:t.coordinateSystem.getRect(),style:r.defaults({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))}}),a.registerPreprocessor(function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})})},function(t,e,n){function i(t,e){var n=t[1]-t[0],i=e,r=n/i/2;t[0]+=r,t[1]-=r}var r=n(4),a=r.linearMap,o=n(1),s=n(18),l=[0,1],h=function(t,e,n){this.dim=t,this.scale=e,this._extent=n||[0,0],this.inverse=!1,this.onBand=!1,this._labelInterval};h.prototype={constructor:h,contain:function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},containData:function(t){return this.contain(this.dataToCoord(t))},getExtent:function(){return this._extent.slice()},getPixelPrecision:function(t){return r.getPixelPrecision(t||this.scale.getExtent(),this._extent)},setExtent:function(t,e){var n=this._extent;n[0]=t,n[1]=e},dataToCoord:function(t,e){var n=this._extent,r=this.scale;return t=r.normalize(t),this.onBand&&"ordinal"===r.type&&(n=n.slice(),i(n,r.count())),a(t,l,n,e)},coordToData:function(t,e){var n=this._extent,r=this.scale;this.onBand&&"ordinal"===r.type&&(n=n.slice(),i(n,r.count()));var o=a(t,n,l,e);return this.scale.scale(o)},pointToData:function(t,e){},getTicksCoords:function(t){if(this.onBand&&!t){for(var e=this.getBands(),n=[],i=0;i<e.length;i++)n.push(e[i][0]);return e[i-1]&&n.push(e[i-1][1]),n}return o.map(this.scale.getTicks(),this.dataToCoord,this)},getLabelsCoords:function(){return o.map(this.scale.getTicks(),this.dataToCoord,this)},getBands:function(){for(var t=this.getExtent(),e=[],n=this.scale.count(),i=t[0],r=t[1],a=r-i,o=0;o<n;o++)e.push([a*o/n+i,a*(o+1)/n+i]);return e},getBandWidth:function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},getLabelInterval:function(){var t=this._labelInterval;if(!t){var e=this.model,n=e.getModel("axisLabel"),i=n.get("interval");"category"!==this.type||"auto"!==i?t="auto"===i?0:i:this.isHorizontal&&(t=s.getAxisLabelInterval(o.map(this.scale.getTicks(),this.dataToCoord,this),e.getFormattedLabels(),n.getFont(),this.isHorizontal())),this._labelInterval=t}return t}},t.exports=h},function(t,e,n){function i(t){this._setting=t||{},this._extent=[1/0,-(1/0)],this._interval=0,this.init&&this.init.apply(this,arguments)}var r=n(15),a=i.prototype;a.parse=function(t){return t},a.getSetting=function(t){return this._setting[t]},a.contain=function(t){var e=this._extent;return t>=e[0]&&t<=e[1]},a.normalize=function(t){var e=this._extent;return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])},a.scale=function(t){var e=this._extent;return t*(e[1]-e[0])+e[0]},a.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},a.unionExtentFromData=function(t,e){this.unionExtent(t.getDataExtent(e,!0))},a.getExtent=function(){return this._extent.slice()},a.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},a.getTicksLabels=function(){for(var t=[],e=this.getTicks(),n=0;n<e.length;n++)t.push(this.getLabel(e[n]));return t},a.isBlank=function(){return this._isBlank},a.setBlank=function(t){this._isBlank=t},r.enableClassExtend(i),r.enableClassManagement(i,{registerWhenExtend:!0}),t.exports=i},function(t,e){var n=1;"undefined"!=typeof window&&(n=Math.max(window.devicePixelRatio||1,1));var i={debugMode:0,devicePixelRatio:n};t.exports=i},function(t,e,n){var i=n(1),r=n(69),a=n(12),o=function(t){t=t||{},r.call(this,t);for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);this._children=[],this.__storage=null,this.__dirty=!0};o.prototype={constructor:o,isGroup:!0,type:"group",silent:!1,children:function(){return this._children.slice()},childAt:function(t){return this._children[t]},childOfName:function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},childCount:function(){
return this._children.length},add:function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},addBefore:function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},_doAdd:function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__storage,n=this.__zr;e&&e!==t.__storage&&(e.addToStorage(t),t instanceof o&&t.addChildrenToStorage(e)),n&&n.refresh()},remove:function(t){var e=this.__zr,n=this.__storage,r=this._children,a=i.indexOf(r,t);return a<0?this:(r.splice(a,1),t.parent=null,n&&(n.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(n)),e&&e.refresh(),this)},removeAll:function(){var t,e,n=this._children,i=this.__storage;for(e=0;e<n.length;e++)t=n[e],i&&(i.delFromStorage(t),t instanceof o&&t.delChildrenFromStorage(i)),t.parent=null;return n.length=0,this},eachChild:function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},traverse:function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n];t.call(e,i),"group"===i.type&&i.traverse(t,e)}return this},addChildrenToStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.addToStorage(n),n instanceof o&&n.addChildrenToStorage(t)}},delChildrenFromStorage:function(t){for(var e=0;e<this._children.length;e++){var n=this._children[e];t.delFromStorage(n),n instanceof o&&n.delChildrenFromStorage(t)}},dirty:function(){return this.__dirty=!0,this.__zr&&this.__zr.refresh(),this},getBoundingRect:function(t){for(var e=null,n=new a(0,0,0,0),i=t||this._children,r=[],o=0;o<i.length;o++){var s=i[o];if(!s.ignore&&!s.invisible){var l=s.getBoundingRect(),h=s.getLocalTransform(r);h?(n.copy(l),n.applyTransform(h),e=e||n.clone(),e.union(n)):(e=e||l.clone(),e.union(l))}}return e||n}},i.inherits(o,r),t.exports=o},function(t,e){var n={},i="\0__throttleOriginMethod",r="\0__throttleRate",a="\0__throttleType";n.throttle=function(t,e,n){function i(){u=(new Date).getTime(),c=null,t.apply(o,s||[])}var r,a,o,s,l,h=0,u=0,c=null;e=e||0;var f=function(){r=(new Date).getTime(),o=this,s=arguments;var t=l||e,f=l||n;l=null,a=r-(f?h:u)-t,clearTimeout(c),f?c=setTimeout(i,t):a>=0?i():c=setTimeout(i,-a),h=r};return f.clear=function(){c&&(clearTimeout(c),c=null)},f.debounceNextCall=function(t){l=t},f},n.createOrUpdate=function(t,e,o,s){var l=t[e];if(l){var h=l[i]||l,u=l[a],c=l[r];if(c!==o||u!==s){if(null==o||!s)return t[e]=h;l=t[e]=n.throttle(h,o,"debounce"===s),l[i]=h,l[a]=s,l[r]=o}return l}},n.clear=function(t,e){var n=t[e];n&&n[i]&&(t[e]=n[i])},t.exports=n},function(t,e,n){function i(t){t=t||{},o.call(this,t);for(var e in t)t.hasOwnProperty(e)&&"style"!==e&&(this[e]=t[e]);this.style=new a(t.style,this),this._rect=null,this.__clipPaths=[]}var r=n(1),a=n(76),o=n(69),s=n(92);i.prototype={constructor:i,type:"displayable",__dirty:!0,invisible:!1,z:0,z2:0,zlevel:0,draggable:!1,dragging:!1,silent:!1,culling:!1,cursor:"pointer",rectHover:!1,progressive:-1,beforeBrush:function(t){},afterBrush:function(t){},brush:function(t,e){},getBoundingRect:function(){},contain:function(t,e){return this.rectContain(t,e)},traverse:function(t,e){t.call(e,this)},rectContain:function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect();return i.contain(n[0],n[1])},dirty:function(){this.__dirty=!0,this._rect=null,this.__zr&&this.__zr.refresh()},animateStyle:function(t){return this.animate("style",t)},attrKV:function(t,e){"style"!==t?o.prototype.attrKV.call(this,t,e):this.style.set(e)},setStyle:function(t,e){return this.style.set(t,e),this.dirty(!1),this},useStyle:function(t){return this.style=new a(t,this),this.dirty(!1),this}},r.inherits(i,o),r.mixin(i,s),t.exports=i},function(t,e){var n=function(t){this.colorStops=t||[]};n.prototype={constructor:n,addColorStop:function(t,e){this.colorStops.push({offset:t,color:e})}},t.exports=n},function(t,e,n){function i(t){var e={componentType:t.mainType};return e[t.mainType+"Index"]=t.componentIndex,e}function r(t,e,n,i){var r,a,o=m(n-t.rotation),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;return y(o-S/2)?(a=l?"bottom":"top",r="center"):y(o-1.5*S)?(a=l?"top":"bottom",r="center"):(a="middle",r=o<1.5*S&&o>S/2?l?"left":"right":l?"right":"left"),{rotation:o,textAlign:r,textVerticalAlign:a}}function a(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)}function o(t,e,n){var i=t.get("axisLabel.showMinLabel"),r=t.get("axisLabel.showMaxLabel");e=e||[],n=n||[];var a=e[0],o=e[1],h=e[e.length-1],u=e[e.length-2],c=n[0],f=n[1],d=n[n.length-1],p=n[n.length-2];i===!1?(s(a),s(c)):l(a,o)&&(i?(s(o),s(f)):(s(a),s(c))),r===!1?(s(h),s(d)):l(u,h)&&(r?(s(u),s(p)):(s(h),s(d)))}function s(t){t&&(t.ignore=!0)}function l(t,e,n){var i=t&&t.getBoundingRect().clone(),r=e&&e.getBoundingRect().clone();if(i&&r){var a=_.identity([]);return _.rotate(a,a,-t.rotation),i.applyTransform(_.mul([],a,t.getLocalTransform())),r.applyTransform(_.mul([],a,e.getLocalTransform())),i.intersect(r)}}function h(t){return"middle"===t||"center"===t}function u(t,e,n){var i=e.axis;if(e.get("axisTick.show")&&!i.scale.isBlank()){for(var r=e.getModel("axisTick"),a=r.getModel("lineStyle"),o=r.get("length"),s=C(r,n.labelInterval),l=i.getTicksCoords(r.get("alignWithLabel")),h=i.scale.getTicks(),u=e.get("axisLabel.showMinLabel"),c=e.get("axisLabel.showMaxLabel"),d=[],g=[],v=t._transform,m=[],y=l.length,x=0;x<y;x++)if(!I(i,x,s,y,u,c)){var _=l[x];d[0]=_,d[1]=0,g[0]=_,g[1]=n.tickDirection*o,v&&(b(d,d,v),b(g,g,v));var w=new p.Line(p.subPixelOptimizeLine({anid:"tick_"+h[x],shape:{x1:d[0],y1:d[1],x2:g[0],y2:g[1]},style:f.defaults(a.getLineStyle(),{stroke:e.get("axisLine.lineStyle.color")}),z2:2,silent:!0}));t.group.add(w),m.push(w)}return m}}function c(t,e,n){var r=e.axis,o=w(n.axisLabelShow,e.get("axisLabel.show"));if(o&&!r.scale.isBlank()){var s=e.getModel("axisLabel"),l=s.get("margin"),h=r.scale.getTicks(),u=e.getFormattedLabels(),c=(w(n.labelRotate,s.get("rotate"))||0)*S/180,d=A(n.rotation,c,n.labelDirection),v=e.get("data"),m=[],y=a(e),x=e.get("triggerEvent"),_=e.get("axisLabel.showMinLabel"),b=e.get("axisLabel.showMaxLabel");return f.each(h,function(a,o){if(!I(r,o,n.labelInterval,h.length,_,b)){var c=s;v&&v[a]&&v[a].textStyle&&(c=new g(v[a].textStyle,s,e.ecModel));var f=c.getTextColor()||e.get("axisLine.lineStyle.color"),w=r.dataToCoord(a),S=[w,n.labelOffset+n.labelDirection*l],T=r.scale.getLabel(a),M=new p.Text({anid:"label_"+a,position:S,rotation:d.rotation,silent:y,z2:10});p.setTextStyle(M.style,c,{text:u[o],textAlign:c.getShallow("align",!0)||d.textAlign,textVerticalAlign:c.getShallow("verticalAlign",!0)||c.getShallow("baseline",!0)||d.textVerticalAlign,textFill:"function"==typeof f?f("category"===r.type?T:"value"===r.type?a+"":a,o):f}),x&&(M.eventData=i(e),M.eventData.targetType="axisLabel",M.eventData.value=T),t._dumbGroup.add(M),M.updateTransform(),m.push(M),t.group.add(M),M.decomposeTransform()}}),m}}var f=n(1),d=n(7),p=n(3),g=n(11),v=n(4),m=v.remRadian,y=v.isRadianAroundZero,x=n(6),_=n(19),b=x.applyTransform,w=f.retrieve,S=Math.PI,T=function(t,e){this.opt=e,this.axisModel=t,f.defaults(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0}),this.group=new p.Group;var n=new p.Group({position:e.position.slice(),rotation:e.rotation});n.updateTransform(),this._transform=n.transform,this._dumbGroup=n};T.prototype={constructor:T,hasBuilder:function(t){return!!M[t]},add:function(t){M[t].call(this)},getGroup:function(){return this.group}};var M={axisLine:function(){var t=this.opt,e=this.axisModel;if(e.get("axisLine.show")){var n=this.axisModel.axis.getExtent(),i=this._transform,r=[n[0],0],a=[n[1],0];i&&(b(r,r,i),b(a,a,i)),this.group.add(new p.Line(p.subPixelOptimizeLine({anid:"line",shape:{x1:r[0],y1:r[1],x2:a[0],y2:a[1]},style:f.extend({lineCap:"round"},e.getModel("axisLine.lineStyle").getLineStyle()),strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1})))}},axisTickLabel:function(){var t=this.axisModel,e=this.opt,n=u(this,t,e),i=c(this,t,e);o(t,i,n)},axisName:function(){var t=this.opt,e=this.axisModel,n=w(t.axisName,e.get("name"));if(n){var o,s=e.get("nameLocation"),l=t.nameDirection,u=e.getModel("nameTextStyle"),c=e.get("nameGap")||0,g=this.axisModel.axis.getExtent(),v=g[0]>g[1]?-1:1,m=["start"===s?g[0]-v*c:"end"===s?g[1]+v*c:(g[0]+g[1])/2,h(s)?t.labelOffset+l*c:0],y=e.get("nameRotate");null!=y&&(y=y*S/180);var x;h(s)?o=A(t.rotation,null!=y?y:t.rotation,l):(o=r(t,s,y||0,g),x=t.axisNameAvailableWidth,null!=x&&(x=Math.abs(x/Math.sin(o.rotation)),!isFinite(x)&&(x=null)));var _=u.getFont(),b=e.get("nameTruncate",!0)||{},T=b.ellipsis,M=w(t.nameTruncateMaxWidth,b.maxWidth,x),I=null!=T&&null!=M?d.truncateText(n,M,_,T,{minChar:2,placeholder:b.placeholder}):n,C=e.get("tooltip",!0),P=e.mainType,L={componentType:P,name:n,$vars:["name"]};L[P+"Index"]=e.componentIndex;var k=new p.Text({anid:"name",__fullText:n,__truncatedText:I,position:m,rotation:o.rotation,silent:a(e),z2:1,tooltip:C&&C.show?f.extend({content:n,formatter:function(){return n},formatterParams:L},C):null});p.setTextStyle(k.style,u,{text:I,textFont:_,textFill:u.getTextColor()||e.get("axisLine.lineStyle.color"),textAlign:o.textAlign,textVerticalAlign:o.textVerticalAlign}),e.get("triggerEvent")&&(k.eventData=i(e),k.eventData.targetType="axisName",k.eventData.name=n),this._dumbGroup.add(k),k.updateTransform(),this.group.add(k),k.decomposeTransform()}}},A=T.innerTextLayout=function(t,e,n){var i,r,a=m(e-t);return y(a)?(r=n>0?"top":"bottom",i="center"):y(a-S)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=a>0&&a<S?n>0?"right":"left":n>0?"left":"right"),{rotation:a,textAlign:i,textVerticalAlign:r}},I=T.ifIgnoreOnTick=function(t,e,n,i,r,a){if(0===e&&r||e===i-1&&a)return!1;var o,s=t.scale;return"ordinal"===s.type&&("function"==typeof n?(o=s.getTicks()[e],!n(o,s.getLabel(o))):e%(n+1))},C=T.getInterval=function(t,e){var n=t.get("interval");return null!=n&&"auto"!=n||(n=e),n};t.exports=T},function(t,e,n){function i(t,e,n,i,s,l){var h=o.getAxisPointerClass(t.axisPointerClass);if(h){var u=a.getAxisPointerModel(e);u?(t._axisPointer||(t._axisPointer=new h)).render(e,u,i,l):r(t,i)}}function r(t,e,n){var i=t._axisPointer;i&&i.dispose(e,n),t._axisPointer=null}var a=n(47),o=n(2).extendComponentView({type:"axis",_axisPointer:null,axisPointerClass:null,render:function(t,e,n,r){this.axisPointerClass&&a.fixValue(t),o.superApply(this,"render",arguments),i(this,t,e,n,r,!0)},updateAxisPointer:function(t,e,n,r,a){i(this,t,e,n,r,!1)},remove:function(t,e){var n=this._axisPointer;n&&n.remove(e),o.superApply(this,"remove",arguments)},dispose:function(t,e){r(this,e),o.superApply(this,"dispose",arguments)}}),s=[];o.registerAxisPointerClass=function(t,e){s[t]=e},o.getAxisPointerClass=function(t){return t&&s[t]},t.exports=o},function(t,e,n){function i(t){return r.isObject(t)&&null!=t.value?t.value:t+""}var r=n(1),a=n(18);t.exports={getFormattedLabels:function(){return a.getFormattedLabels(this.axis,this.get("axisLabel.formatter"))},getCategories:function(){return"category"===this.get("type")&&r.map(this.get("data"),i)},getMin:function(t){var e=this.option,n=t||null==e.rangeStart?e.min:e.rangeStart;return this.axis&&null!=n&&"dataMin"!==n&&"function"!=typeof n&&!r.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getMax:function(t){var e=this.option,n=t||null==e.rangeEnd?e.max:e.rangeEnd;return this.axis&&null!=n&&"dataMax"!==n&&"function"!=typeof n&&!r.eqNaN(n)&&(n=this.axis.scale.parse(n)),n},getNeedCrossZero:function(){var t=this.option;return null==t.rangeStart&&null==t.rangeEnd&&!t.scale},getCoordSysModel:r.noop,setRange:function(t,e){this.option.rangeStart=t,this.option.rangeEnd=e},resetRange:function(){this.option.rangeStart=this.option.rangeEnd=null}}},function(t,e){"use strict";function n(t){return t}function i(t,e,i,r,a){this._old=t,this._new=e,this._oldKeyGetter=i||n,this._newKeyGetter=r||n,this.context=a}function r(t,e,n,i,r){for(var a=0;a<t.length;a++){var o="_ec_"+r[i](t[a],a),s=e[o];null==s?(n.push(o),e[o]=a):(s.length||(e[o]=s=[s]),s.push(a))}}i.prototype={constructor:i,add:function(t){return this._add=t,this},update:function(t){return this._update=t,this},remove:function(t){return this._remove=t,this},execute:function(){var t,e=this._old,n=this._new,i={},a={},o=[],s=[];for(r(e,i,o,"_oldKeyGetter",this),r(n,a,s,"_newKeyGetter",this),t=0;t<e.length;t++){var l=o[t],h=a[l];if(null!=h){var u=h.length;u?(1===u&&(a[l]=null),h=h.unshift()):a[l]=null,this._update&&this._update(h,t)}else this._remove&&this._remove(t)}for(var t=0;t<s.length;t++){var l=s[t];if(a.hasOwnProperty(l)){var h=a[l];if(null==h)continue;if(h.length)for(var c=0,u=h.length;c<u;c++)this._add&&this._add(h[c]);else this._add&&this._add(h)}}}},t.exports=i},,function(t,e,n){var i=n(4),r=n(7),a=n(34),o=n(67),s=i.round,l=a.extend({type:"interval",_interval:0,_intervalPrecision:2,setExtent:function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},unionExtent:function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),l.prototype.setExtent.call(this,e[0],e[1])},getInterval:function(){return this._interval},setInterval:function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=o.getIntervalPrecision(t)},getTicks:function(){return o.intervalScaleGetTicks(this._interval,this._extent,this._niceExtent,this._intervalPrecision)},getTicksLabels:function(){for(var t=[],e=this.getTicks(),n=0;n<e.length;n++)t.push(this.getLabel(e[n]));return t},getLabel:function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=i.getPrecisionSafe(t)||0:"auto"===n&&(n=this._intervalPrecision),t=s(t,n,!0),r.addCommas(t)},niceTicks:function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var a=o.intervalScaleNiceTicks(i,t,e,n);this._intervalPrecision=a.intervalPrecision,this._interval=a.interval,this._niceExtent=a.niceTickExtent}},niceExtent:function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=e[0];t.fixMax?e[0]-=n/2:(e[1]+=n/2,e[0]-=n/2)}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=s(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=s(Math.ceil(e[1]/r)*r))}});l.create=function(){return new l},t.exports=l},function(t,e,n){function i(t){this.group=new a.Group,this._symbolCtor=t||o}function r(t,e,n){var i=t.getItemLayout(e);return i&&!isNaN(i[0])&&!isNaN(i[1])&&!(n&&n(e))&&"none"!==t.getItemVisual(e,"symbol")}var a=n(3),o=n(57),s=i.prototype;s.updateData=function(t,e){var n=this.group,i=t.hostModel,o=this._data,s=this._symbolCtor,l={itemStyle:i.getModel("itemStyle.normal").getItemStyle(["color"]),hoverItemStyle:i.getModel("itemStyle.emphasis").getItemStyle(),symbolRotate:i.get("symbolRotate"),symbolOffset:i.get("symbolOffset"),hoverAnimation:i.get("hoverAnimation"),labelModel:i.getModel("label.normal"),hoverLabelModel:i.getModel("label.emphasis"),cursorStyle:i.get("cursor")};t.diff(o).add(function(i){var a=t.getItemLayout(i);if(r(t,i,e)){var o=new s(t,i,l);o.attr("position",a),t.setItemGraphicEl(i,o),n.add(o)}}).update(function(h,u){var c=o.getItemGraphicEl(u),f=t.getItemLayout(h);return r(t,h,e)?(c?(c.updateData(t,h,l),a.updateProps(c,{position:f},i)):(c=new s(t,h),c.attr("position",f)),n.add(c),void t.setItemGraphicEl(h,c)):void n.remove(c)}).remove(function(t){var e=o.getItemGraphicEl(t);e&&e.fadeOut(function(){n.remove(e)})}).execute(),this._data=t},s.updateLayout=function(){var t=this._data;t&&t.eachItemGraphicEl(function(e,n){var i=t.getItemLayout(n);e.attr("position",i)})},s.remove=function(t){var e=this.group,n=this._data;n&&(t?n.eachItemGraphicEl(function(t){t.fadeOut(function(){e.remove(t)})}):e.removeAll())},t.exports=i},function(t,e,n){function i(t,e,n){var i=e.getComponent("tooltip"),a=e.getComponent("axisPointer"),s=a.get("link",!0)||[],h=[];c(n.getCoordinateSystems(),function(n){function u(i,u,c){var f=c.model.getModel("axisPointer",a),d=f.get("show");if(d&&("auto"!==d||i||l(f))){null==u&&(u=f.get("triggerTooltip")),f=i?r(c,m,a,e,i,u):f;var v=f.get("snap"),y=p(c.model),x=u||v||"category"===c.type,_=t.axesInfo[y]={key:y,axis:c,coordSys:n,axisPointerModel:f,triggerTooltip:u,involveSeries:x,snap:v,useHandle:l(f),seriesModels:[]};g[y]=_,t.seriesInvolved|=x;var b=o(s,c);if(null!=b){var w=h[b]||(h[b]={axesInfo:{}});w.axesInfo[y]=_,w.mapper=s[b].mapper,_.linkGroup=w}}}if(n.axisPointerEnabled){var d=p(n.model),g=t.coordSysAxesInfo[d]={};t.coordSysMap[d]=n;var v=n.model,m=v.getModel("tooltip",i);if(c(n.getAxes(),f(u,!1,null)),n.getTooltipAxes&&i&&m.get("show")){var y="axis"===m.get("trigger"),x="cross"===m.get("axisPointer.type"),_=n.getTooltipAxes(m.get("axisPointer.axis"));(y||x)&&c(_.baseAxes,f(u,!x||"cross",y)),x&&c(_.otherAxes,f(u,"cross",!1))}}})}function r(t,e,n,i,r,a){var o=e.getModel("axisPointer"),s={};c(["type","snap","lineStyle","shadowStyle","label","animation","animationDurationUpdate","animationEasingUpdate","z"],function(t){s[t]=h.clone(o.get(t))}),s.snap="category"!==t.type&&!!a,"cross"===o.get("type")&&(s.type="line");var l=s.label||(s.label={});if(null==l.show&&(l.show=!1),"cross"===r&&(l.show=!0,!a)){var f=s.lineStyle=o.get("crossStyle");f&&h.defaults(l,f.textStyle)}return t.model.getModel("axisPointer",new u(s,n,i))}function a(t,e){e.eachSeries(function(e){var n=e.coordinateSystem,i=e.get("tooltip.trigger",!0),r=e.get("tooltip.show",!0);n&&"none"!==i&&i!==!1&&"item"!==i&&r!==!1&&e.get("axisPointer.show",!0)!==!1&&c(t.coordSysAxesInfo[p(n.model)],function(t){var i=t.axis;n.getAxis(i.dim)===i&&(t.seriesModels.push(e),null==t.seriesDataCount&&(t.seriesDataCount=0),t.seriesDataCount+=e.getData().count())})},this)}function o(t,e){for(var n=e.model,i=e.dim,r=0;r<t.length;r++){var a=t[r]||{};if(s(a[i+"AxisId"],n.id)||s(a[i+"AxisIndex"],n.componentIndex)||s(a[i+"AxisName"],n.name))return r}}function s(t,e){return"all"===t||h.isArray(t)&&h.indexOf(t,e)>=0||t===e}function l(t){return!!t.get("handle.show")}var h=n(1),u=n(11),c=h.each,f=h.curry,d={};d.collect=function(t,e){var n={axesInfo:{},seriesInvolved:!1,coordSysAxesInfo:{},coordSysMap:{}};return i(n,t,e),n.seriesInvolved&&a(n,t),n},d.fixValue=function(t){var e=d.getAxisInfo(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,a=n.get("status"),o=n.get("value");null!=o&&(o=i.parse(o));var s=l(n);null==a&&(r.status=s?"show":"hide");var h=i.getExtent().slice();h[0]>h[1]&&h.reverse(),(null==o||o>h[1])&&(o=h[1]),o<h[0]&&(o=h[0]),r.value=o,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}},d.getAxisInfo=function(t){var e=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return e&&e.axesInfo[p(t)]},d.getAxisPointerModel=function(t){var e=d.getAxisInfo(t);return e&&e.axisPointerModel};var p=d.makeKey=function(t){return t.type+"||"+t.id};t.exports=d},,,function(t,e,n){var i=n(1),r=n(15),a=r.parseClassType,o=0,s={},l="_";s.getUID=function(t){return[t||"",o++,Math.random()].join(l)},s.enableSubTypeDefaulter=function(t){var e={};return t.registerSubTypeDefaulter=function(t,n){t=a(t),e[t.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=a(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r},t},s.enableTopologicalTravel=function(t,e){function n(t){var n={},o=[];return i.each(t,function(s){var l=r(n,s),h=l.originalDeps=e(s),u=a(h,t);l.entryCount=u.length,0===l.entryCount&&o.push(s),i.each(u,function(t){i.indexOf(l.predecessor,t)<0&&l.predecessor.push(t);var e=r(n,t);i.indexOf(e.successor,t)<0&&e.successor.push(s)})}),{graph:n,noEntryList:o}}function r(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}function a(t,e){var n=[];return i.each(t,function(t){i.indexOf(e,t)>=0&&n.push(t)}),n}t.topologicalTravel=function(t,e,r,a){function o(t){h[t].entryCount--,0===h[t].entryCount&&u.push(t)}function s(t){c[t]=!0,o(t)}if(t.length){var l=n(e),h=l.graph,u=l.noEntryList,c={};for(i.each(t,function(t){c[t]=!0});u.length;){var f=u.pop(),d=h[f],p=!!c[f];p&&(r.call(a,f,d.originalDeps.slice()),delete c[f]),i.each(d.successor,p?s:o)}i.each(c,function(){throw new Error("Circle dependency may exists")})}}},t.exports=s},function(t,e){t.exports=function(t,e,n,i,r){i.eachRawSeriesByType(t,function(t){var r=t.getData(),a=t.get("symbol")||e,o=t.get("symbolSize");r.setVisual({legendSymbol:n||a,symbol:a,symbolSize:o}),i.isSeriesFiltered(t)||("function"==typeof o&&r.each(function(e){var n=t.getRawValue(e),i=t.getDataParams(e);r.setItemVisual(e,"symbolSize",o(n,i))}),r.each(function(t){var e=r.getItemModel(t),n=e.getShallow("symbol",!0),i=e.getShallow("symbolSize",!0);null!=n&&r.setItemVisual(t,"symbol",n),null!=i&&r.setItemVisual(t,"symbolSize",i)}))})}},function(t,e){function n(t){for(var e=0;t>=u;)e|=1&t,t>>=1;return t+e}function i(t,e,n,i){var a=e+1;if(a===n)return 1;if(i(t[a++],t[e])<0){for(;a<n&&i(t[a],t[a-1])<0;)a++;r(t,e,a)}else for(;a<n&&i(t[a],t[a-1])>=0;)a++;return a-e}function r(t,e,n){for(n--;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}function a(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var a,o=t[i],s=e,l=i;s<l;)a=s+l>>>1,r(o,t[a])<0?l=a:s=a+1;var h=i-s;switch(h){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;h>0;)t[s+h]=t[s+h-1],h--}t[s]=o}}function o(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])>0){for(s=i-r;l<s&&a(t,e[n+r+l])>0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=r,l+=r}else{for(s=r+1;l<s&&a(t,e[n+r-l])<=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}for(o++;o<l;){var u=o+(l-o>>>1);a(t,e[n+u])>0?o=u+1:l=u}return l}function s(t,e,n,i,r,a){var o=0,s=0,l=1;if(a(t,e[n+r])<0){for(s=r+1;l<s&&a(t,e[n+r-l])<0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s);var h=o;o=r-l,l=r-h}else{for(s=i-r;l<s&&a(t,e[n+r+l])>=0;)o=l,l=(l<<1)+1,l<=0&&(l=s);l>s&&(l=s),o+=r,l+=r}for(o++;o<l;){var u=o+(l-o>>>1);a(t,e[n+u])<0?l=u:o=u+1}return l}function l(t,e){function n(t,e){u[y]=t,d[y]=e,y+=1}function i(){for(;y>1;){var t=y-2;if(t>=1&&d[t-1]<=d[t]+d[t+1]||t>=2&&d[t-2]<=d[t]+d[t-1])d[t-1]<d[t+1]&&t--;else if(d[t]>d[t+1])break;a(t)}}function r(){for(;y>1;){var t=y-2;t>0&&d[t-1]<d[t+1]&&t--,a(t)}}function a(n){var i=u[n],r=d[n],a=u[n+1],c=d[n+1];d[n]=r+c,n===y-3&&(u[n+1]=u[n+2],d[n+1]=d[n+2]),y--;var f=s(t[a],t,i,r,0,e);i+=f,r-=f,0!==r&&(c=o(t[i+r-1],t,a,c,c-1,e),0!==c&&(r<=c?l(i,r,a,c):h(i,r,a,c)))}function l(n,i,r,a){var l=0;for(l=0;l<i;l++)x[l]=t[n+l];var h=0,u=r,f=n;if(t[f++]=t[u++],0!==--a){if(1===i){for(l=0;l<a;l++)t[f+l]=t[u+l];return void(t[f+a]=x[h])}for(var d,g,v,m=p;;){d=0,g=0,v=!1;do if(e(t[u],x[h])<0){if(t[f++]=t[u++],g++,d=0,0===--a){v=!0;break}}else if(t[f++]=x[h++],d++,g=0,1===--i){v=!0;break}while((d|g)<m);if(v)break;do{if(d=s(t[u],x,h,i,0,e),0!==d){for(l=0;l<d;l++)t[f+l]=x[h+l];if(f+=d,h+=d,i-=d,i<=1){v=!0;break}}if(t[f++]=t[u++],0===--a){v=!0;break}if(g=o(x[h],t,u,a,0,e),0!==g){for(l=0;l<g;l++)t[f+l]=t[u+l];if(f+=g,u+=g,a-=g,0===a){v=!0;break}}if(t[f++]=x[h++],1===--i){v=!0;break}m--}while(d>=c||g>=c);if(v)break;m<0&&(m=0),m+=2}if(p=m,p<1&&(p=1),1===i){for(l=0;l<a;l++)t[f+l]=t[u+l];t[f+a]=x[h]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[f+l]=x[h+l]}}else for(l=0;l<i;l++)t[f+l]=x[h+l]}function h(n,i,r,a){var l=0;for(l=0;l<a;l++)x[l]=t[r+l];var h=n+i-1,u=a-1,f=r+a-1,d=0,g=0;if(t[f--]=t[h--],0!==--i){if(1===a){for(f-=i,h-=i,g=f+1,d=h+1,l=i-1;l>=0;l--)t[g+l]=t[d+l];return void(t[f]=x[u])}for(var v=p;;){var m=0,y=0,_=!1;do if(e(x[u],t[h])<0){if(t[f--]=t[h--],m++,y=0,0===--i){_=!0;break}}else if(t[f--]=x[u--],y++,m=0,1===--a){_=!0;break}while((m|y)<v);if(_)break;do{if(m=i-s(x[u],t,n,i,i-1,e),0!==m){for(f-=m,h-=m,i-=m,g=f+1,d=h+1,l=m-1;l>=0;l--)t[g+l]=t[d+l];if(0===i){_=!0;break}}if(t[f--]=x[u--],1===--a){_=!0;break}if(y=a-o(t[h],x,0,a,a-1,e),0!==y){for(f-=y,u-=y,a-=y,g=f+1,d=u+1,l=0;l<y;l++)t[g+l]=x[d+l];if(a<=1){_=!0;break}}if(t[f--]=t[h--],0===--i){_=!0;break}v--}while(m>=c||y>=c);if(_)break;v<0&&(v=0),v+=2}if(p=v,p<1&&(p=1),1===a){for(f-=i,h-=i,g=f+1,d=h+1,l=i-1;l>=0;l--)t[g+l]=t[d+l];t[f]=x[u]}else{if(0===a)throw new Error;for(d=f-(a-1),l=0;l<a;l++)t[d+l]=x[l]}}else for(d=f-(a-1),l=0;l<a;l++)t[d+l]=x[l]}var u,d,p=c,g=0,v=f,m=0,y=0;g=t.length,g<2*f&&(v=g>>>1);var x=[];m=g<120?5:g<1542?10:g<119151?19:40,u=[],d=[],this.mergeRuns=i,this.forceMergeRuns=r,this.pushRun=n}function h(t,e,r,o){r||(r=0),o||(o=t.length);var s=o-r;if(!(s<2)){var h=0;if(s<u)return h=i(t,r,o,e),void a(t,r,o,r+h,e);var c=new l(t,e),f=n(s);do{if(h=i(t,r,o,e),h<f){var d=s;d>f&&(d=f),a(t,r,r+d,r+h,e),h=d}c.pushRun(r,h),c.mergeRuns(),s-=h,r+=h}while(0!==s);c.forceMergeRuns()}}var u=32,c=7,f=256;t.exports=h},function(t,e,n){function i(){var t=this.__cachedImgObj;this.onload=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}var r=n(73),a=new r(50),o={};o.findExistImage=function(t){if("string"==typeof t){var e=a.get(t);return e&&e.image}return t},o.createOrUpdateImage=function(t,e,n,r,o){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var l=a.get(t),h={hostEl:n,cb:r,cbPayload:o};return l?(e=l.image,!s(e)&&l.pending.push(h)):(!e&&(e=new Image),e.onload=i,a.put(t,e.__cachedImgObj={image:e,pending:[h]}),e.src=e.__zrImageSrc=t),e}return t}return e};var s=o.isImageReady=function(t){return t&&t.width&&t.height};t.exports=o},function(t,e,n){var i=n(35);t.exports=function(){if(0!==i.debugMode)if(1==i.debugMode)for(var t in arguments)throw new Error(arguments[t]);else if(i.debugMode>1)for(var t in arguments)console.log(arguments[t])}},function(t,e,n){function i(t){r.call(this,t)}var r=n(38),a=n(12),o=n(1),s=n(53);i.prototype={constructor:i,type:"image",brush:function(t,e){var n=this.style,i=n.image;n.bind(t,this,e);var r=this._image=s.createOrUpdateImage(i,this._image,this);if(r&&s.isImageReady(r)){var a=n.x||0,o=n.y||0,l=n.width,h=n.height,u=r.width/r.height;if(null==l&&null!=h?l=h*u:null==h&&null!=l?h=l/u:null==l&&null==h&&(l=r.width,h=r.height),this.setTransform(t),n.sWidth&&n.sHeight){var c=n.sx||0,f=n.sy||0;t.drawImage(r,c,f,n.sWidth,n.sHeight,a,o,l,h)}else if(n.sx&&n.sy){var c=n.sx,f=n.sy,d=l-c,p=h-f;t.drawImage(r,c,f,d,p,a,o,l,h)}else t.drawImage(r,a,o,l,h);this.restoreTransform(t),null!=n.text&&this.drawRectText(t,this.getBoundingRect())}},getBoundingRect:function(){var t=this.style;return this._rect||(this._rect=new a(t.x||0,t.y||0,t.width||0,t.height||0)),this._rect}},o.inherits(i,r),t.exports=i},function(t,e,n){function i(t){if(t){t.font=v.makeFont(t);var e=t.textAlign;"middle"===e&&(e="center"),t.textAlign=null==e||w[e]?e:"left";var n=t.textVerticalAlign||t.textBaseline;"center"===n&&(n="middle"),t.textVerticalAlign=null==n||S[n]?n:"top";var i=t.textPadding;i&&(t.textPadding=m.normalizeCssArray(t.textPadding))}}function r(t,e,n,i,r){var a=d(e,"font",i.font||v.DEFAULT_FONT),o=i.textPadding,l=t.__textCotentBlock;l&&!t.__dirty||(l=t.__textCotentBlock=v.parsePlainText(n,a,o,i.truncate));var c=l.outerHeight,p=l.lines,m=l.lineHeight,y=f(c,i,r),x=y.baseX,_=y.baseY,b=y.textAlign,w=y.textVerticalAlign;s(e,i,r,x,_);var S=v.adjustTextY(_,c,w),T=x,I=S,C=h(i);if(C||o){var P=v.getWidth(n,a),L=P;o&&(L+=o[1]+o[3]);var k=v.adjustTextX(x,L,b);C&&u(t,e,i,k,S,L,c),o&&(T=g(x,b,o),I+=o[0])}d(e,"textAlign",b||"left"),d(e,"textBaseline","middle"),d(e,"shadowBlur",i.textShadowBlur||0),d(e,"shadowColor",i.textShadowColor||"transparent"),d(e,"shadowOffsetX",i.textShadowOffsetX||0),d(e,"shadowOffsetY",i.textShadowOffsetY||0),I+=m/2;var D=i.textStrokeWidth,O=M(i.textStroke,D),E=A(i.textFill);O&&(d(e,"lineWidth",D),d(e,"strokeStyle",O)),E&&d(e,"fillStyle",E);for(var z=0;z<p.length;z++)O&&e.strokeText(p[z],T,I),E&&e.fillText(p[z],T,I),I+=m}function a(t,e,n,i,r){var a=t.__textCotentBlock;a&&!t.__dirty||(a=t.__textCotentBlock=v.parseRichText(n,i)),o(t,e,a,i,r)}function o(t,e,n,i,r){var a=n.width,o=n.outerWidth,c=n.outerHeight,d=i.textPadding,p=f(c,i,r),g=p.baseX,m=p.baseY,y=p.textAlign,x=p.textVerticalAlign;s(e,i,r,g,m);var _=v.adjustTextX(g,o,y),b=v.adjustTextY(m,c,x),w=_,S=b;d&&(w+=d[3],S+=d[0]);var T=w+a;h(i)&&u(t,e,i,_,b,o,c);for(var M=0;M<n.lines.length;M++){for(var A,I=n.lines[M],C=I.tokens,P=C.length,L=I.lineHeight,k=I.width,D=0,O=w,E=T,z=P-1;D<P&&(A=C[D],!A.textAlign||"left"===A.textAlign);)l(t,e,A,i,L,S,O,"left"),k-=A.width,O+=A.width,D++;for(;z>=0&&(A=C[z],"right"===A.textAlign);)l(t,e,A,i,L,S,E,"right"),k-=A.width,E-=A.width,z--;for(O+=(a-(O-w)-(T-E)-k)/2;D<=z;)A=C[D],l(t,e,A,i,L,S,O+A.width/2,"center"),O+=A.width,D++;S+=L}}function s(t,e,n,i,r){if(n&&e.textRotation){var a=e.textOrigin;"center"===a?(i=n.width/2+n.x,r=n.height/2+n.y):a&&(i=a[0]+n.x,r=a[1]+n.y),t.translate(i,r),t.rotate(-e.textRotation),t.translate(-i,-r)}}function l(t,e,n,i,r,a,o,s){var l=i.rich[n.styleName]||{},c=n.textVerticalAlign,f=a+r/2;"top"===c?f=a+n.height/2:"bottom"===c&&(f=a+r-n.height/2),!n.isLineHolder&&h(l)&&u(t,e,l,"right"===s?o-n.width:"center"===s?o-n.width/2:o,f-n.height/2,n.width,n.height);var p=n.textPadding;p&&(o=g(o,s,p),f-=n.height/2-p[2]-n.textHeight/2),d(e,"shadowBlur",_(l.textShadowBlur,i.textShadowBlur,0)),d(e,"shadowColor",l.textShadowColor||i.textShadowColor||"transparent"),d(e,"shadowOffsetX",_(l.textShadowOffsetX,i.textShadowOffsetX,0)),d(e,"shadowOffsetY",_(l.textShadowOffsetY,i.textShadowOffsetY,0)),d(e,"textAlign",s),d(e,"textBaseline","middle"),d(e,"font",n.font||v.DEFAULT_FONT);var m=M(l.textStroke||i.textStroke,x),y=A(l.textFill||i.textFill),x=b(l.textStrokeWidth,i.textStrokeWidth);m&&(d(e,"lineWidth",x),d(e,"strokeStyle",m),e.strokeText(n.text,o,f)),y&&(d(e,"fillStyle",y),e.fillText(n.text,o,f))}function h(t){return t.textBackgroundColor||t.textBorderWidth&&t.textBorderColor}function u(t,e,n,i,r,a,o){var s=n.textBackgroundColor,l=n.textBorderWidth,h=n.textBorderColor,u=m.isString(s);if(d(e,"shadowBlur",n.textBoxShadowBlur||0),d(e,"shadowColor",n.textBoxShadowColor||"transparent"),d(e,"shadowOffsetX",n.textBoxShadowOffsetX||0),d(e,"shadowOffsetY",n.textBoxShadowOffsetY||0),u||l&&h){e.beginPath();var f=n.textBorderRadius;f?y.buildPath(e,{x:i,y:r,width:a,height:o,r:f}):e.rect(i,r,a,o),e.closePath()}if(u)d(e,"fillStyle",s),e.fill();else if(m.isObject(s)){var p=s.image;p=x.createOrUpdateImage(p,null,t,c,s),p&&x.isImageReady(p)&&e.drawImage(p,i,r,a,o)}l&&h&&(d(e,"lineWidth",l),d(e,"strokeStyle",h),e.stroke())}function c(t,e){e.image=t}function f(t,e,n){var i=e.x||0,r=e.y||0,a=e.textAlign,o=e.textVerticalAlign;if(n){var s=e.textPosition;if(s instanceof Array)i=n.x+p(s[0],n.width),r=n.y+p(s[1],n.height);else{var l=v.adjustTextPositionOnRect(s,n,e.textDistance);i=l.x,r=l.y,a=a||l.textAlign,o=o||l.textVerticalAlign}var h=e.textOffset;h&&(i+=h[0],r+=h[1])}return{baseX:i,baseY:r,textAlign:a,textVerticalAlign:o}}function d(t,e,n){return t[e]=n,t[e]}function p(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function g(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}var v=n(16),m=n(1),y=n(79),x=n(53),_=m.retrieve3,b=m.retrieve2,w={left:1,right:1,center:1},S={top:1,bottom:1,middle:1},T={};T.normalizeTextStyle=function(t){return i(t),m.each(t.rich,i),t},T.renderText=function(t,e,n,i,o){i.rich?a(t,e,n,i,o):r(t,e,n,i,o)};var M=T.getStroke=function(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t},A=T.getFill=function(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t};T.needDrawText=function(t,e){return null!=t&&(t||e.textBackgroundColor||e.textBorderWidth&&e.textBorderColor||e.textPadding)},t.exports=T},function(t,e,n){function i(t,e){var n=t.getItemVisual(e,"symbolSize");return n instanceof Array?n.slice():[+n,+n]}function r(t){return[t[0]/2,t[1]/2]}function a(t,e,n){h.Group.call(this),this.updateData(t,e,n)}function o(t,e){this.parent.drift(t,e)}var s=n(1),l=n(24),h=n(3),u=n(4),c=n(97),f=a.prototype;f._createSymbol=function(t,e,n,i){this.removeAll();var a=e.hostModel,s=e.getItemVisual(n,"color"),u=l.createSymbol(t,-1,-1,2,2,s);u.attr({z2:100,culling:!0,scale:[0,0]}),u.drift=o,h.initProps(u,{scale:r(i)},a,n),this._symbolType=t,this.add(u)},f.stopSymbolAnimation=function(t){
this.childAt(0).stopAnimation(t)},f.getSymbolPath=function(){return this.childAt(0)},f.getScale=function(){return this.childAt(0).scale},f.highlight=function(){this.childAt(0).trigger("emphasis")},f.downplay=function(){this.childAt(0).trigger("normal")},f.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},f.setDraggable=function(t){var e=this.childAt(0);e.draggable=t,e.cursor=t?"move":"pointer"},f.updateData=function(t,e,n){this.silent=!1;var a=t.getItemVisual(e,"symbol")||"circle",o=t.hostModel,s=i(t,e);if(a!==this._symbolType)this._createSymbol(a,t,e,s);else{var l=this.childAt(0);l.silent=!1,h.updateProps(l,{scale:r(s)},o,e)}this._updateCommon(t,e,s,n),this._seriesModel=o};var d=["itemStyle","normal"],p=["itemStyle","emphasis"],g=["label","normal"],v=["label","emphasis"];f._updateCommon=function(t,e,n,i){var a=this.childAt(0),o=t.hostModel,l=t.getItemVisual(e,"color");"image"!==a.type&&a.useStyle({strokeNoScale:!0}),i=i||null;var f=i&&i.itemStyle,m=i&&i.hoverItemStyle,y=i&&i.symbolRotate,x=i&&i.symbolOffset,_=i&&i.labelModel,b=i&&i.hoverLabelModel,w=i&&i.hoverAnimation,S=i&&i.cursorStyle;if(!i||t.hasItemOption){var T=t.getItemModel(e);f=T.getModel(d).getItemStyle(["color"]),m=T.getModel(p).getItemStyle(),y=T.getShallow("symbolRotate"),x=T.getShallow("symbolOffset"),_=T.getModel(g),b=T.getModel(v),w=T.getShallow("hoverAnimation"),S=T.getShallow("cursor")}else m=s.extend({},m);var M=a.style;a.attr("rotation",(y||0)*Math.PI/180||0),x&&a.attr("position",[u.parsePercent(x[0],n[0]),u.parsePercent(x[1],n[1])]),S&&a.attr("cursor",S),a.setColor(l),a.setStyle(f);var A=t.getItemVisual(e,"opacity");null!=A&&(M.opacity=A);var I=c.findLabelValueDim(t);null!=I&&h.setLabelStyle(M,m,_,b,{labelFetcher:o,labelDataIndex:e,defaultText:t.get(I,e),isRectText:!0,autoColor:l}),a.off("mouseover").off("mouseout").off("emphasis").off("normal"),a.hoverStyle=m,h.setHoverStyle(a);var C=r(n);if(w&&o.isAnimationEnabled()){var P=function(){var t=C[1]/C[0];this.animateTo({scale:[Math.max(1.1*C[0],C[0]+3),Math.max(1.1*C[1],C[1]+3*t)]},400,"elasticOut")},L=function(){this.animateTo({scale:C},400,"elasticOut")};a.on("mouseover",P).on("mouseout",L).on("emphasis",P).on("normal",L)}},f.fadeOut=function(t){var e=this.childAt(0);this.silent=e.silent=!0,e.style.text=null,h.updateProps(e,{scale:[0,0]},this._seriesModel,this.dataIndex,t)},s.inherits(a,h.Group),t.exports=a},,,function(t,e,n){function i(t,e,n){return t.getCoordSysModel()===e}function r(t){var e,n=t.model,i=n.getFormattedLabels(),r=n.getModel("axisLabel"),a=1,o=i.length;o>40&&(a=Math.ceil(o/40));for(var s=0;s<o;s+=a)if(!t.isLabelIgnored(s)){var l=r.getTextRect(i[s]);e?e.union(l):e=l}return e}function a(t,e,n){this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this._initCartesian(t,e,n),this.model=t}function o(t,e,n){var i=t[e];if(n.onZero){var r=n.onZeroAxisIndex;if(null!=r){var a=i[r];return void(a&&s(a)&&(n.onZero=!1))}for(var o in i)if(i.hasOwnProperty(o)){var a=i[o];if(a&&!s(a)){r=+o;break}}null==r&&(n.onZero=!1),n.onZeroAxisIndex=r}}function s(t){return"category"===t.type||"time"===t.type||!m(t)}function l(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}function h(t,e){return d.map(_,function(e){var n=t.getReferringComponents(e)[0];return n})}function u(t){return"cartesian2d"===t.get("coordinateSystem")}var c=n(9),f=n(18),d=n(1),p=n(140),g=n(138),v=d.each,m=f.ifAxisCrossZero,y=f.niceScaleExtent;n(141);var x=a.prototype;x.type="grid",x.axisPointerEnabled=!0,x.getRect=function(){return this._rect},x.update=function(t,e){var n=this._axesMap;this._updateScale(t,this.model),v(n.x,function(t){y(t.scale,t.model)}),v(n.y,function(t){y(t.scale,t.model)}),v(n.x,function(t){o(n,"y",t)}),v(n.y,function(t){o(n,"x",t)}),this.resize(this.model,e)},x.resize=function(t,e,n){function i(){v(o,function(t){var e=t.isHorizontal(),n=e?[0,a.width]:[0,a.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),l(t,e?a.x:a.y)})}var a=c.getLayoutRect(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()});this._rect=a;var o=this._axesList;i(),!n&&t.get("containLabel")&&(v(o,function(t){if(!t.model.get("axisLabel.inside")){var e=r(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get("axisLabel.margin");a[n]-=e[n]+i,"top"===t.position?a.y+=e.height+i:"left"===t.position&&(a.x+=e.width+i)}}}),i())},x.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n){if(null==e)for(var i in n)if(n.hasOwnProperty(i))return n[i];return n[e]}},x.getAxes=function(){return this._axesList.slice()},x.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}d.isObject(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},x.getCartesians=function(){return this._coordsList.slice()},x.convertToPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},x.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(t,e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},x._findConvertTarget=function(t,e){var n,i,r=e.seriesModel,a=e.xAxisModel||r&&r.getReferringComponents("xAxis")[0],o=e.yAxisModel||r&&r.getReferringComponents("yAxis")[0],s=e.gridModel,l=this._coordsList;if(r)n=r.coordinateSystem,d.indexOf(l,n)<0&&(n=null);else if(a&&o)n=this.getCartesian(a.componentIndex,o.componentIndex);else if(a)i=this.getAxis("x",a.componentIndex);else if(o)i=this.getAxis("y",o.componentIndex);else if(s){var h=s.coordinateSystem;h===this&&(n=this._coordsList[0])}return{cartesian:n,axis:i}},x.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},x._initCartesian=function(t,e,n){function r(n){return function(r,l){if(i(r,t,e)){var h=r.get("position");"x"===n?"top"!==h&&"bottom"!==h&&(h="bottom",a[h]&&(h="top"===h?"bottom":"top")):"left"!==h&&"right"!==h&&(h="left",a[h]&&(h="left"===h?"right":"left")),a[h]=!0;var u=new g(n,f.createScaleByModel(r),[0,0],r.get("type"),h),c="category"===u.type;u.onBand=c&&r.get("boundaryGap"),u.inverse=r.get("inverse"),u.onZero=r.get("axisLine.onZero"),u.onZeroAxisIndex=r.get("axisLine.onZeroAxisIndex"),r.axis=u,u.model=r,u.grid=this,u.index=l,this._axesList.push(u),o[n][l]=u,s[n]++}}}var a={left:!1,right:!1,top:!1,bottom:!1},o={x:{},y:{}},s={x:0,y:0};return e.eachComponent("xAxis",r("x"),this),e.eachComponent("yAxis",r("y"),this),s.x&&s.y?(this._axesMap=o,void v(o.x,function(e,n){v(o.y,function(i,r){var a="x"+n+"y"+r,o=new p(a);o.grid=this,o.model=t,this._coordsMap[a]=o,this._coordsList.push(o),o.addAxis(e),o.addAxis(i)},this)},this)):(this._axesMap={},void(this._axesList=[]))},x._updateScale=function(t,e){function n(t,e,n){v(n.coordDimToDataDim(e.dim),function(n){e.scale.unionExtentFromData(t,n)})}d.each(this._axesList,function(t){t.scale.setExtent(1/0,-(1/0))}),t.eachSeries(function(r){if(u(r)){var a=h(r,t),o=a[0],s=a[1];if(!i(o,e,t)||!i(s,e,t))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),c=r.getData(),f=l.getAxis("x"),d=l.getAxis("y");"list"===c.type&&(n(c,f,r),n(c,d,r))}},this)},x.getTooltipAxes=function(t){var e=[],n=[];return v(this.getCartesians(),function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),a=i.getOtherAxis(r);d.indexOf(e,r)<0&&e.push(r),d.indexOf(n,a)<0&&n.push(a)}),{baseAxes:e,otherAxes:n}};var _=["xAxis","yAxis"];a.create=function(t,e){var n=[];return t.eachComponent("grid",function(i,r){var o=new a(i,t,e);o.name="grid_"+r,o.resize(i,e,!0),i.coordinateSystem=o,n.push(o)}),t.eachSeries(function(e){if(u(e)){var n=h(e,t),i=n[0],r=n[1],a=i.getCoordSysModel(),o=a.coordinateSystem;e.coordinateSystem=o.getCartesian(i.componentIndex,r.componentIndex)}}),n},a.dimensions=a.prototype.dimensions=p.prototype.dimensions,n(26).register("cartesian2d",a),t.exports=a},function(t,e,n){"use strict";function i(t){return t>s||t<-s}var r=n(19),a=n(6),o=r.identity,s=5e-5,l=function(t){t=t||{},t.position||(this.position=[0,0]),null==t.rotation&&(this.rotation=0),t.scale||(this.scale=[1,1]),this.origin=this.origin||null},h=l.prototype;h.transform=null,h.needLocalTransform=function(){return i(this.rotation)||i(this.position[0])||i(this.position[1])||i(this.scale[0]-1)||i(this.scale[1]-1)},h.updateTransform=function(){var t=this.parent,e=t&&t.transform,n=this.needLocalTransform(),i=this.transform;return n||e?(i=i||r.create(),n?this.getLocalTransform(i):o(i),e&&(n?r.mul(i,t.transform,i):r.copy(i,t.transform)),this.transform=i,this.invTransform=this.invTransform||r.create(),void r.invert(this.invTransform,i)):void(i&&o(i))},h.getLocalTransform=function(t){return l.getLocalTransform(this,t)},h.setTransform=function(t){var e=this.transform,n=t.dpr||1;e?t.setTransform(n*e[0],n*e[1],n*e[2],n*e[3],n*e[4],n*e[5]):t.setTransform(n,0,0,n,0,0)},h.restoreTransform=function(t){var e=t.dpr||1;t.setTransform(e,0,0,e,0,0)};var u=[];h.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(r.mul(u,t.invTransform,e),e=u);var n=e[0]*e[0]+e[1]*e[1],a=e[2]*e[2]+e[3]*e[3],o=this.position,s=this.scale;i(n-1)&&(n=Math.sqrt(n)),i(a-1)&&(a=Math.sqrt(a)),e[0]<0&&(n=-n),e[3]<0&&(a=-a),o[0]=e[4],o[1]=e[5],s[0]=n,s[1]=a,this.rotation=Math.atan2(-e[1]/a,e[0]/n)}},h.getGlobalScale=function(){var t=this.transform;if(!t)return[1,1];var e=Math.sqrt(t[0]*t[0]+t[1]*t[1]),n=Math.sqrt(t[2]*t[2]+t[3]*t[3]);return t[0]<0&&(e=-e),t[3]<0&&(n=-n),[e,n]},h.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&a.applyTransform(n,n,i),n},h.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&a.applyTransform(n,n,i),n},l.getLocalTransform=function(t,e){e=e||[],o(e);var n=t.origin,i=t.scale||[1,1],a=t.rotation||0,s=t.position||[0,0];return n&&(e[4]-=n[0],e[5]-=n[1]),r.scale(e,e,i),a&&r.rotate(e,e,a),n&&(e[4]+=n[0],e[5]+=n[1]),e[4]+=s[0],e[5]+=s[1],e},t.exports=l},function(t,e,n){var i=n(101),r=n(1),a=n(13),o=n(9),s=["value","category","time","log"];t.exports=function(t,e,n,l){r.each(s,function(a){e.extend({type:t+"Axis."+a,mergeDefaultAndTheme:function(e,i){var s=this.layoutMode,l=s?o.getLayoutParams(e):{},h=i.getTheme();r.merge(e,h.get(a+"Axis")),r.merge(e,this.getDefaultOption()),e.type=n(t,e),s&&o.mergeLayoutParam(e,l,s)},defaultOption:r.mergeAll([{},i[a+"Axis"],l],!0)})}),a.registerSubTypeDefaulter(t+"Axis",r.curry(n,t))}},function(t,e,n){"use strict";function i(t,e){return e.type||(e.data?"category":"value")}var r=n(13),a=n(1),o=n(62),s=r.extend({type:"cartesian2dAxis",axis:null,init:function(){s.superApply(this,"init",arguments),this.resetRange()},mergeOption:function(){s.superApply(this,"mergeOption",arguments),this.resetRange()},restoreData:function(){s.superApply(this,"restoreData",arguments),this.resetRange()},getCoordSysModel:function(){return this.ecModel.queryComponents({mainType:"grid",index:this.option.gridIndex,id:this.option.gridId})[0]}});a.merge(s.prototype,n(42));var l={offset:0};o("x",s,i,l),o("y",s,i,l),t.exports=s},function(t,e){t.exports=function(t,e){e.eachSeriesByType(t,function(t){var e=t.getData(),n=t.coordinateSystem;if(n){for(var i=[],r=n.dimensions,a=0;a<r.length;a++)i.push(t.coordDimToDataDim(n.dimensions[a])[0]);1===i.length?e.each(i[0],function(t,i){e.setItemLayout(i,isNaN(t)?[NaN,NaN]:n.dataToPoint(t))}):2===i.length&&e.each(i,function(t,i,r){e.setItemLayout(r,isNaN(t)||isNaN(i)?[NaN,NaN]:n.dataToPoint([t,i]))},!0)}})}},function(t,e,n){var i=n(15),r=i.set,a=i.get;t.exports={clearColorPalette:function(){r(this,"colorIdx",0),r(this,"colorNameMap",{})},getColorFromPalette:function(t,e){e=e||this;var n=a(e,"colorIdx")||0,i=a(e,"colorNameMap")||r(e,"colorNameMap",{});if(i.hasOwnProperty(t))return i[t];var o=this.get("color",!0)||[];if(o.length){var s=o[n];return t&&(i[t]=s),r(e,"colorIdx",(n+1)%o.length),s}}}},function(t,e){t.exports=function(t,e){var n=e.findComponents({mainType:"legend"});n&&n.length&&e.eachSeriesByType(t,function(t){var e=t.getData();e.filterSelf(function(t){for(var i=e.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(i))return!1;return!0},this)},this)}},function(t,e,n){function i(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}var r=n(4),a=r.round,o={};o.intervalScaleNiceTicks=function(t,e,n,i){var s={},l=t[1]-t[0],h=s.interval=r.nice(l/e,!0);null!=n&&h<n&&(h=s.interval=n),null!=i&&h>i&&(h=s.interval=i);var u=s.intervalPrecision=o.getIntervalPrecision(h),c=s.niceTickExtent=[a(Math.ceil(t[0]/h)*h,u),a(Math.floor(t[1]/h)*h,u)];return o.fixExtent(c,t),s},o.getIntervalPrecision=function(t){return r.getPrecisionSafe(t)+2},o.fixExtent=function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),i(t,0,e),i(t,1,e),t[0]>t[1]&&(t[0]=t[1])},o.intervalScaleGetTicks=function(t,e,n,i){var r=[];if(!t)return r;var o=1e4;e[0]<n[0]&&r.push(e[0]);for(var s=n[0];s<=n[1]&&(r.push(s),s=a(s+t,i),s!==r[r.length-1]);)if(r.length>o)return[];return e[1]>(r.length?r[r.length-1]:n[1])&&r.push(e[1]),r},t.exports=o},function(t,e,n){var i=n(36),r=n(50),a=n(15),o=function(){this.group=new i,this.uid=r.getUID("viewComponent")};o.prototype={constructor:o,init:function(t,e){},render:function(t,e,n,i){},dispose:function(){}};var s=o.prototype;s.updateView=s.updateLayout=s.updateVisual=function(t,e,n,i){},a.enableClassExtend(o),a.enableClassManagement(o,{registerWhenExtend:!0}),t.exports=o},function(t,e,n){"use strict";var i=n(74),r=n(23),a=n(61),o=n(184),s=n(1),l=function(t){a.call(this,t),r.call(this,t),o.call(this,t),this.id=t.id||i()};l.prototype={type:"element",name:"",__zr:null,ignore:!1,clipPath:null,drift:function(t,e){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.dirty(!1)},beforeUpdate:function(){},afterUpdate:function(){},update:function(){this.updateTransform()},traverse:function(t,e){},attrKV:function(t,e){if("position"===t||"scale"===t||"origin"===t){if(e){var n=this[t];n||(n=this[t]=[]),n[0]=e[0],n[1]=e[1]}}else this[t]=e},hide:function(){this.ignore=!0,this.__zr&&this.__zr.refresh()},show:function(){this.ignore=!1,this.__zr&&this.__zr.refresh()},attr:function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(s.isObject(t))for(var n in t)t.hasOwnProperty(n)&&this.attrKV(n,t[n]);return this.dirty(!1),this},setClipPath:function(t){var e=this.__zr;e&&t.addSelfToZr(e),this.clipPath&&this.clipPath!==t&&this.removeClipPath(),this.clipPath=t,t.__zr=e,t.__clipTarget=this,this.dirty(!1)},removeClipPath:function(){var t=this.clipPath;t&&(t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__clipTarget=null,this.clipPath=null,this.dirty(!1))},addSelfToZr:function(t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this.clipPath&&this.clipPath.addSelfToZr(t)},removeSelfFromZr:function(t){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this.clipPath&&this.clipPath.removeSelfFromZr(t)}},s.mixin(l,o),s.mixin(l,a),s.mixin(l,r),t.exports=l},function(t,e,n){function i(t,e){return t[e]}function r(t,e,n){t[e]=n}function a(t,e,n){return(e-t)*n+t}function o(t,e,n){return n>.5?e:t}function s(t,e,n,i,r){var o=t.length;if(1==r)for(var s=0;s<o;s++)i[s]=a(t[s],e[s],n);else for(var l=o&&t[0].length,s=0;s<o;s++)for(var h=0;h<l;h++)i[s][h]=a(t[s][h],e[s][h],n)}function l(t,e,n){var i=t.length,r=e.length;if(i!==r){var a=i>r;if(a)t.length=r;else for(var o=i;o<r;o++)t.push(1===n?e[o]:_.call(e[o]))}for(var s=t[0]&&t[0].length,o=0;o<t.length;o++)if(1===n)isNaN(t[o])&&(t[o]=e[o]);else for(var l=0;l<s;l++)isNaN(t[o][l])&&(t[o][l]=e[o][l])}function h(t,e,n){if(t===e)return!0;var i=t.length;if(i!==e.length)return!1;if(1===n){for(var r=0;r<i;r++)if(t[r]!==e[r])return!1}else for(var a=t[0].length,r=0;r<i;r++)for(var o=0;o<a;o++)if(t[r][o]!==e[r][o])return!1;return!0}function u(t,e,n,i,r,a,o,s,l){var h=t.length;if(1==l)for(var u=0;u<h;u++)s[u]=c(t[u],e[u],n[u],i[u],r,a,o);else for(var f=t[0].length,u=0;u<h;u++)for(var d=0;d<f;d++)s[u][d]=c(t[u][d],e[u][d],n[u][d],i[u][d],r,a,o)}function c(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}function f(t){if(x(t)){var e=t.length;if(x(t[0])){for(var n=[],i=0;i<e;i++)n.push(_.call(t[i]));return n}return _.call(t)}return t}function d(t){return t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.floor(t[2]),"rgba("+t.join(",")+")"}function p(t){var e=t[t.length-1].value;return x(e&&e[0])?2:1}function g(t,e,n,i,r,f){var g=t._getter,y=t._setter,_="spline"===e,b=i.length;if(b){var w,S=i[0].value,T=x(S),M=!1,A=!1,I=T?p(i):0;i.sort(function(t,e){return t.time-e.time}),w=i[b-1].time;for(var C=[],P=[],L=i[0].value,k=!0,D=0;D<b;D++){C.push(i[D].time/w);var O=i[D].value;if(T&&h(O,L,I)||!T&&O===L||(k=!1),L=O,"string"==typeof O){var E=m.parse(O);E?(O=E,M=!0):A=!0}P.push(O)}if(f||!k){for(var z=P[b-1],D=0;D<b-1;D++)T?l(P[D],z,I):!isNaN(P[D])||isNaN(z)||A||M||(P[D]=z);T&&l(g(t._target,r),z,I);var N,B,R,F,V,W,H=0,G=0;if(M)var q=[0,0,0,0];var j=function(t,e){var n;if(e<0)n=0;else if(e<G){for(N=Math.min(H+1,b-1),n=N;n>=0&&!(C[n]<=e);n--);n=Math.min(n,b-2)}else{for(n=H;n<b&&!(C[n]>e);n++);n=Math.min(n-1,b-2)}H=n,G=e;var i=C[n+1]-C[n];if(0!==i)if(B=(e-C[n])/i,_)if(F=P[n],R=P[0===n?n:n-1],V=P[n>b-2?b-1:n+1],W=P[n>b-3?b-1:n+2],T)u(R,F,V,W,B,B*B,B*B*B,g(t,r),I);else{var l;if(M)l=u(R,F,V,W,B,B*B,B*B*B,q,1),l=d(q);else{if(A)return o(F,V,B);l=c(R,F,V,W,B,B*B,B*B*B)}y(t,r,l)}else if(T)s(P[n],P[n+1],B,g(t,r),I);else{var l;if(M)s(P[n],P[n+1],B,q,1),l=d(q);else{if(A)return o(P[n],P[n+1],B);l=a(P[n],P[n+1],B)}y(t,r,l)}},X=new v({target:t._target,life:w,loop:t._loop,delay:t._delay,onframe:j,ondestroy:n});return e&&"spline"!==e&&(X.easing=e),X}}}var v=n(164),m=n(22),y=n(1),x=y.isArrayLike,_=Array.prototype.slice,b=function(t,e,n,a){this._tracks={},this._target=t,this._loop=e||!1,this._getter=n||i,this._setter=a||r,this._clipCount=0,this._delay=0,this._doneList=[],this._onframeList=[],this._clipList=[]};b.prototype={when:function(t,e){var n=this._tracks;for(var i in e)if(e.hasOwnProperty(i)){if(!n[i]){n[i]=[];var r=this._getter(this._target,i);if(null==r)continue;0!==t&&n[i].push({time:0,value:f(r)})}n[i].push({time:t,value:e[i]})}return this},during:function(t){return this._onframeList.push(t),this},pause:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].pause();this._paused=!0},resume:function(){for(var t=0;t<this._clipList.length;t++)this._clipList[t].resume();this._paused=!1},isPaused:function(){return!!this._paused},_doneCallback:function(){this._tracks={},this._clipList.length=0;for(var t=this._doneList,e=t.length,n=0;n<e;n++)t[n].call(this)},start:function(t,e){var n,i=this,r=0,a=function(){r--,r||i._doneCallback()};for(var o in this._tracks)if(this._tracks.hasOwnProperty(o)){var s=g(this,t,a,this._tracks[o],o,e);s&&(this._clipList.push(s),r++,this.animation&&this.animation.addClip(s),n=s)}if(n){var l=n.onframe;n.onframe=function(t,e){l(t,e);for(var n=0;n<i._onframeList.length;n++)i._onframeList[n](t,e)}}return r||this._doneCallback(),this},stop:function(t){for(var e=this._clipList,n=this.animation,i=0;i<e.length;i++){var r=e[i];t&&r.onframe(this._target,1),n&&n.removeClip(r)}e.length=0},delay:function(t){return this._delay=t,this},done:function(t){return t&&this._doneList.push(t),this},getClips:function(){return this._clipList}},t.exports=b},function(t,e){t.exports="undefined"!=typeof window&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){setTimeout(t,16)}},function(t,e){var n=2*Math.PI;t.exports={normalizeRadian:function(t){return t%=n,t<0&&(t+=n),t}}},function(t,e){var n=function(){this.head=null,this.tail=null,this._len=0},i=n.prototype;i.insert=function(t){var e=new r(t);return this.insertEntry(e),e},i.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},i.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},i.len=function(){return this._len},i.clear=function(){this.head=this.tail=null,this._len=0};var r=function(t){this.value=t,this.next,this.prev},a=function(t){this._list=new n,this._map={},this._maxSize=t||10,this._lastRemovedEntry=null},o=a.prototype;o.put=function(t,e){var n=this._list,i=this._map,a=null;if(null==i[t]){var o=n.len(),s=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var l=n.head;n.remove(l),delete i[l.key],a=l.value,this._lastRemovedEntry=l}s?s.value=e:s=new r(e),s.key=t,n.insertEntry(s),i[t]=s}return a},o.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},o.clear=function(){this._list.clear(),this._map={}},t.exports=a},function(t,e){var n=2311;t.exports=function(){return n++}},function(t,e){var n=function(t,e){this.image=t,this.repeat=e,this.type="pattern"};n.prototype.getCanvasPattern=function(t){return t.createPattern(this.image,this.repeat||"repeat")},t.exports=n},function(t,e){function n(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,a=null==e.y?0:e.y,o=null==e.y2?0:e.y2;e.global||(i=i*n.width+n.x,r=r*n.width+n.x,a=a*n.height+n.y,o=o*n.height+n.y);var s=t.createLinearGradient(i,a,r,o);return s}function i(t,e,n){var i=n.width,r=n.height,a=Math.min(i,r),o=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;e.global||(o=o*i+n.x,s=s*r+n.y,l*=a);var h=t.createRadialGradient(o,s,0,o,s,l);return h}var r=[["shadowBlur",0],["shadowOffsetX",0],["shadowOffsetY",0],["shadowColor","#000"],["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]],a=function(t,e){this.extendFrom(t,!1),this.host=e};a.prototype={constructor:a,host:null,fill:"#000",stroke:null,opacity:1,lineDash:null,lineDashOffset:0,shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,lineWidth:1,strokeNoScale:!1,text:null,font:null,textFont:null,fontStyle:null,fontWeight:null,fontSize:null,fontFamily:null,textTag:null,textFill:"#000",textStroke:null,textWidth:null,textHeight:null,textStrokeWidth:0,textLineHeight:null,textPosition:"inside",textRect:null,textOffset:null,textAlign:null,textVerticalAlign:null,textDistance:5,textShadowColor:"transparent",textShadowBlur:0,textShadowOffsetX:0,textShadowOffsetY:0,textBoxShadowColor:"transparent",textBoxShadowBlur:0,textBoxShadowOffsetX:0,textBoxShadowOffsetY:0,transformText:!1,textRotation:0,textOrigin:null,textBackgroundColor:null,textBorderColor:null,textBorderWidth:0,textBorderRadius:0,textPadding:null,rich:null,truncate:null,blend:null,bind:function(t,e,n){for(var i=this,a=n&&n.style,o=!a,s=0;s<r.length;s++){var l=r[s],h=l[0];(o||i[h]!==a[h])&&(t[h]=i[h]||l[1])}if((o||i.fill!==a.fill)&&(t.fillStyle=i.fill),(o||i.stroke!==a.stroke)&&(t.strokeStyle=i.stroke),(o||i.opacity!==a.opacity)&&(t.globalAlpha=null==i.opacity?1:i.opacity),(o||i.blend!==a.blend)&&(t.globalCompositeOperation=i.blend||"source-over"),this.hasStroke()){var u=i.lineWidth;t.lineWidth=u/(this.strokeNoScale&&e&&e.getLineScale?e.getLineScale():1)}},hasFill:function(){var t=this.fill;return null!=t&&"none"!==t},hasStroke:function(){var t=this.stroke;return null!=t&&"none"!==t&&this.lineWidth>0},extendFrom:function(t,e){if(t)for(var n in t)!t.hasOwnProperty(n)||e!==!0&&(e===!1?this.hasOwnProperty(n):null==t[n])||(this[n]=t[n])},set:function(t,e){"string"==typeof t?this[t]=e:this.extendFrom(t,!0)},clone:function(){var t=new this.constructor;return t.extendFrom(this,!0),t},getGradient:function(t,e,r){for(var a="radial"===e.type?i:n,o=a(t,e,r),s=e.colorStops,l=0;l<s.length;l++)o.addColorStop(s[l].offset,s[l].color);return o}};for(var o=a.prototype,s=0;s<r.length;s++){var l=r[s];l[0]in o||(o[l[0]]=l[1])}a.getGradient=o.getGradient,t.exports=a},function(t,e,n){var i=n(10),r=[["shadowBlur",0],["shadowColor","#000"],["shadowOffsetX",0],["shadowOffsetY",0]];t.exports=function(t){return i.browser.ie&&i.browser.version>=11?function(){var e,n=this.__clipPaths,i=this.style;if(n)for(var a=0;a<n.length;a++){var o=n[a],s=o&&o.shape,l=o&&o.type;if(s&&("sector"===l&&s.startAngle===s.endAngle||"rect"===l&&(!s.width||!s.height))){for(var h=0;h<r.length;h++)r[h][2]=i[r[h][0]],i[r[h][0]]=r[h][1];e=!0;break}}if(t.apply(this,arguments),e)for(var h=0;h<r.length;h++)i[r[h][0]]=r[h][2]}:t}},function(t,e,n){var i=n(174),r=n(173);t.exports={buildPath:function(t,e,n){var a=e.points,o=e.smooth;if(a&&a.length>=2){if(o&&"spline"!==o){var s=r(a,o,n,e.smoothConstraint);t.moveTo(a[0][0],a[0][1]);for(var l=a.length,h=0;h<(n?l:l-1);h++){var u=s[2*h],c=s[2*h+1],f=a[(h+1)%l];t.bezierCurveTo(u[0],u[1],c[0],c[1],f[0],f[1])}}else{"spline"===o&&(a=i(a,n)),t.moveTo(a[0][0],a[0][1]);for(var h=1,d=a.length;h<d;h++)t.lineTo(a[h][0],a[h][1])}n&&t.closePath()}}}},function(t,e){t.exports={buildPath:function(t,e){var n,i,r,a,o=e.x,s=e.y,l=e.width,h=e.height,u=e.r;l<0&&(o+=l,l=-l),h<0&&(s+=h,h=-h),"number"==typeof u?n=i=r=a=u:u instanceof Array?1===u.length?n=i=r=a=u[0]:2===u.length?(n=r=u[0],i=a=u[1]):3===u.length?(n=u[0],i=a=u[1],r=u[2]):(n=u[0],i=u[1],r=u[2],a=u[3]):n=i=r=a=0;var c;n+i>l&&(c=n+i,n*=l/c,i*=l/c),r+a>l&&(c=r+a,r*=l/c,a*=l/c),i+r>h&&(c=i+r,i*=h/c,r*=h/c),n+a>h&&(c=n+a,n*=h/c,a*=h/c),t.moveTo(o+n,s),t.lineTo(o+l-i,s),0!==i&&t.quadraticCurveTo(o+l,s,o+l,s+i),t.lineTo(o+l,s+h-r),0!==r&&t.quadraticCurveTo(o+l,s+h,o+l-r,s+h),t.lineTo(o+a,s+h),0!==a&&t.quadraticCurveTo(o,s+h,o,s+h-a),t.lineTo(o,s+n),0!==n&&t.quadraticCurveTo(o,s,o+n,s)}}},function(t,e,n){var i=n(1),r={};r.layout=function(t,e,n){n=n||{};var r=t.coordinateSystem,a=e.axis,o={},s=a.position,l=a.onZero?"onZero":s,h=a.dim,u=r.getRect(),c=[u.x,u.x+u.width,u.y,u.y+u.height],f={left:0,right:1,top:0,bottom:1,onZero:2},d=e.get("offset")||0,p="x"===h?[c[2]-d,c[3]+d]:[c[0]-d,c[1]+d];if(a.onZero){var g=r.getAxis("x"===h?"y":"x",a.onZeroAxisIndex),v=g.toGlobalCoord(g.dataToCoord(0));p[f.onZero]=Math.max(Math.min(v,p[1]),p[0])}o.position=["y"===h?p[f[l]]:c[0],"x"===h?p[f[l]]:c[3]],o.rotation=Math.PI/2*("x"===h?0:1);var m={top:-1,bottom:1,left:-1,right:1};o.labelDirection=o.tickDirection=o.nameDirection=m[s],o.labelOffset=a.onZero?p[f[s]]-p[f.onZero]:0,e.get("axisTick.inside")&&(o.tickDirection=-o.tickDirection),i.retrieve(n.labelInside,e.get("axisLabel.inside"))&&(o.labelDirection=-o.labelDirection);var y=e.get("axisLabel.rotate");return o.labelRotate="top"===l?-y:y,o.labelInterval=a.getLabelInterval(),o.z2=1,o},t.exports=r},,,function(t,e,n){var i=n(1);t.exports={updateSelectedMap:function(t){this._targetList=t.slice(),this._selectTargetMap=i.reduce(t||[],function(t,e){return t.set(e.name,e),t},i.createHashMap())},select:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t),i=this.get("selectedMode");"single"===i&&this._selectTargetMap.each(function(t){t.selected=!1}),n&&(n.selected=!0)},unSelect:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);n&&(n.selected=!1)},toggleSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);if(null!=n)return this[n.selected?"unSelect":"select"](t,e),n.selected},isSelected:function(t,e){var n=null!=e?this._targetList[e]:this._selectTargetMap.get(t);return n&&n.selected}}},,,,function(t,e,n){"use strict";function i(t){return t.get("stack")||f+t.seriesIndex}function r(t){return t.dim+t.index}function a(t,e){var n=[],i=t.axis,r="axis0";if("category"===i.type){for(var a=i.getBandWidth(),o=0;o<t.count;o++)n.push(h.defaults({bandWidth:a,axisKey:r,stackId:f+o},t));for(var l=s(n,e),u=[],o=0;o<t.count;o++){var c=l[r][f+o];c.offsetCenter=c.offset+c.width/2,u.push(c)}return u}}function o(t,e){var n=h.map(t,function(t){var e=t.getData(),n=t.coordinateSystem,a=n.getBaseAxis(),o=a.getExtent(),s="category"===a.type?a.getBandWidth():Math.abs(o[1]-o[0])/e.count(),l=c(t.get("barWidth"),s),h=c(t.get("barMaxWidth"),s),u=t.get("barGap"),f=t.get("barCategoryGap");return{bandWidth:s,barWidth:l,barMaxWidth:h,barGap:u,barCategoryGap:f,axisKey:r(a),stackId:i(t)}});return s(n,e)}function s(t,e){var n={};h.each(t,function(t,e){var i=t.axisKey,r=t.bandWidth,a=n[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},o=a.stacks;n[i]=a;var s=t.stackId;o[s]||a.autoWidthCount++,o[s]=o[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!o[s].width&&(o[s].width=l,l=Math.min(a.remainedWidth,l),a.remainedWidth-=l);var h=t.barMaxWidth;h&&(o[s].maxWidth=h);var u=t.barGap;null!=u&&(a.gap=u);var c=t.barCategoryGap;null!=c&&(a.categoryGap=c)});var i={};return h.each(n,function(t,e){i[e]={};var n=t.stacks,r=t.bandWidth,a=c(t.categoryGap,r),o=c(t.gap,1),s=t.remainedWidth,l=t.autoWidthCount,u=(s-a)/(l+(l-1)*o);u=Math.max(u,0),h.each(n,function(t,e){var n=t.maxWidth;n&&n<u&&(n=Math.min(n,s),t.width&&(n=Math.min(n,t.width)),s-=n,t.width=n,l--)}),u=(s-a)/(l+(l-1)*o),u=Math.max(u,0);var f,d=0;h.each(n,function(t,e){t.width||(t.width=u),f=t,d+=t.width*(1+o)}),f&&(d-=f.width*o);var p=-d/2;h.each(n,function(t,n){i[e][n]=i[e][n]||{offset:p,width:t.width},p+=t.width*(1+o)})}),i}function l(t,e,n){var a=o(h.filter(e.getSeriesByType(t),function(t){return!e.isSeriesFiltered(t)&&t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type})),s={},l={};e.eachSeriesByType(t,function(t){if("cartesian2d"===t.coordinateSystem.type){var e=t.getData(),n=t.coordinateSystem,o=n.getBaseAxis(),h=i(t),u=a[r(o)][h],c=u.offset,f=u.width,d=n.getOtherAxis(o),p=t.get("barMinHeight")||0,g=o.onZero?d.toGlobalCoord(d.dataToCoord(0)):d.getGlobalExtent()[0],v=[t.coordDimToDataDim("x")[0],t.coordDimToDataDim("y")[0]],m=e.mapArray(v,function(t,e){return n.dataToPoint([t,e])},!0);s[h]=s[h]||[],l[h]=l[h]||[],e.setLayout({offset:c,size:f}),e.each(t.coordDimToDataDim(d.dim)[0],function(t,n){if(!isNaN(t)){s[h][n]||(s[h][n]={p:g,n:g},l[h][n]={p:g,n:g});var i,r,a,o,u=t>=0?"p":"n",v=m[n],y=s[h][n][u],x=l[h][n][u];d.isHorizontal()?(i=y,r=v[1]+c,a=v[0]-x,o=f,l[h][n][u]+=a,Math.abs(a)<p&&(a=(a<0?-1:1)*p),s[h][n][u]+=a):(i=v[0]+c,r=y,a=f,o=v[1]-x,l[h][n][u]+=o,Math.abs(o)<p&&(o=(o<=0?-1:1)*p),s[h][n][u]+=o),e.setItemLayout(n,{x:i,y:r,width:a,height:o})}},!0)}},this)}var h=n(1),u=n(4),c=u.parsePercent,f="__ec_stack_";l.getLayoutOnAxis=a,t.exports=l},,function(t,e){t.exports=function(t,e){var n={};e.eachRawSeriesByType(t,function(t){var i=t.getRawData(),r={};if(!e.isSeriesFiltered(t)){var a=t.getData();a.each(function(t){var e=a.getRawIndex(t);r[e]=t}),i.each(function(e){var o=r[e],s=null!=o&&a.getItemVisual(o,"color",!0);if(s)i.setItemVisual(e,"color",s);else{var l=i.getItemModel(e),h=l.get("itemStyle.normal.color")||t.getColorFromPalette(i.getName(e),n);i.setItemVisual(e,"color",h),null!=o&&a.setItemVisual(o,"color",h)}})}})}},function(t,e,n){var i=n(6),r=n(20),a={},o=Math.min,s=Math.max,l=Math.sin,h=Math.cos,u=i.create(),c=i.create(),f=i.create(),d=2*Math.PI;a.fromPoints=function(t,e,n){if(0!==t.length){var i,r=t[0],a=r[0],l=r[0],h=r[1],u=r[1];for(i=1;i<t.length;i++)r=t[i],a=o(a,r[0]),l=s(l,r[0]),h=o(h,r[1]),u=s(u,r[1]);e[0]=a,e[1]=h,n[0]=l,n[1]=u}},a.fromLine=function(t,e,n,i,r,a){r[0]=o(t,n),r[1]=o(e,i),a[0]=s(t,n),a[1]=s(e,i)};var p=[],g=[];a.fromCubic=function(t,e,n,i,a,l,h,u,c,f){var d,v=r.cubicExtrema,m=r.cubicAt,y=v(t,n,a,h,p);for(c[0]=1/0,c[1]=1/0,f[0]=-(1/0),f[1]=-(1/0),d=0;d<y;d++){var x=m(t,n,a,h,p[d]);c[0]=o(x,c[0]),f[0]=s(x,f[0])}for(y=v(e,i,l,u,g),d=0;d<y;d++){var _=m(e,i,l,u,g[d]);c[1]=o(_,c[1]),f[1]=s(_,f[1])}c[0]=o(t,c[0]),f[0]=s(t,f[0]),c[0]=o(h,c[0]),f[0]=s(h,f[0]),c[1]=o(e,c[1]),f[1]=s(e,f[1]),c[1]=o(u,c[1]),f[1]=s(u,f[1])},a.fromQuadratic=function(t,e,n,i,a,l,h,u){var c=r.quadraticExtremum,f=r.quadraticAt,d=s(o(c(t,n,a),1),0),p=s(o(c(e,i,l),1),0),g=f(t,n,a,d),v=f(e,i,l,p);h[0]=o(t,a,g),h[1]=o(e,l,v),u[0]=s(t,a,g),u[1]=s(e,l,v)},a.fromArc=function(t,e,n,r,a,o,s,p,g){var v=i.min,m=i.max,y=Math.abs(a-o);
if(y%d<1e-4&&y>1e-4)return p[0]=t-n,p[1]=e-r,g[0]=t+n,void(g[1]=e+r);if(u[0]=h(a)*n+t,u[1]=l(a)*r+e,c[0]=h(o)*n+t,c[1]=l(o)*r+e,v(p,u,c),m(g,u,c),a%=d,a<0&&(a+=d),o%=d,o<0&&(o+=d),a>o&&!s?o+=d:a<o&&s&&(a+=d),s){var x=o;o=a,a=x}for(var _=0;_<o;_+=Math.PI/2)_>a&&(f[0]=h(_)*n+t,f[1]=l(_)*r+e,v(p,f,p),m(g,f,g))},t.exports=a},function(t,e,n){var i=n(38),r=n(1),a=n(16),o=n(56),s=function(t){i.call(this,t)};s.prototype={constructor:s,type:"text",brush:function(t,e){var n=this.style;this.__dirty&&o.normalizeTextStyle(n,!0),n.fill=n.stroke=n.shadowBlur=n.shadowColor=n.shadowOffsetX=n.shadowOffsetY=null;var i=n.text;null!=i&&(i+=""),n.bind(t,this,e),o.needDrawText(i,n)&&(this.setTransform(t),o.renderText(this,t,i,n),this.restoreTransform(t))},getBoundingRect:function(){var t=this.style;if(this.__dirty&&o.normalizeTextStyle(t,!0),!this._rect){var e=t.text;null!=e?e+="":e="";var n=a.getBoundingRect(t.text+"",t.font,t.textAlign,t.textVerticalAlign,t.textPadding,t.rich);if(n.x+=t.x||0,n.y+=t.y||0,o.getStroke(t.textStroke,t.textStrokeWidth)){var i=t.textStrokeWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect}},r.inherits(s,i),t.exports=s},function(t,e,n){var i=n(56),r=n(12),a=new r,o=function(){};o.prototype={constructor:o,drawRectText:function(t,e){var n=this.style;e=n.textRect||e,this.__dirty&&i.normalizeTextStyle(n,!0);var r=n.text;if(null!=r&&(r+=""),i.needDrawText(r,n)){t.save();var o=this.transform;n.transformText?this.setTransform(t):o&&(a.copy(e),a.applyTransform(o),e=a),i.renderText(this,t,r,n,e),t.restore()}}},t.exports=o},function(t,e,n){function i(t){delete d[t]}/*!
	 * ZRender, a high performance 2d drawing library.
	 *
	 * Copyright (c) 2013, Baidu Inc.
	 * All rights reserved.
	 *
	 * LICENSE
	 * https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
	 */
var r=n(74),a=n(10),o=n(1),s=n(159),l=n(162),h=n(163),u=n(170),c=!a.canvasSupported,f={canvas:n(161)},d={},p={};p.version="3.6.2",p.init=function(t,e){var n=new g(r(),t,e);return d[n.id]=n,n},p.dispose=function(t){if(t)t.dispose();else{for(var e in d)d.hasOwnProperty(e)&&d[e].dispose();d={}}return p},p.getInstance=function(t){return d[t]},p.registerPainter=function(t,e){f[t]=e};var g=function(t,e,n){n=n||{},this.dom=e,this.id=t;var i=this,r=new l,d=n.renderer;if(c){if(!f.vml)throw new Error("You need to require 'zrender/vml/vml' to support IE8");d="vml"}else d&&f[d]||(d="canvas");var p=new f[d](e,r,n);this.storage=r,this.painter=p;var g=a.node?null:new u(p.getViewportRoot());this.handler=new s(r,p,g,p.root),this.animation=new h({stage:{update:o.bind(this.flush,this)}}),this.animation.start(),this._needsRefresh;var v=r.delFromStorage,m=r.addToStorage;r.delFromStorage=function(t){v.call(r,t),t&&t.removeSelfFromZr(i)},r.addToStorage=function(t){m.call(r,t),t.addSelfToZr(i)}};g.prototype={constructor:g,getId:function(){return this.id},add:function(t){this.storage.addRoot(t),this._needsRefresh=!0},remove:function(t){this.storage.delRoot(t),this._needsRefresh=!0},configLayer:function(t,e){this.painter.configLayer(t,e),this._needsRefresh=!0},refreshImmediately:function(){this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1},refresh:function(){this._needsRefresh=!0},flush:function(){this._needsRefresh&&this.refreshImmediately(),this._needsRefreshHover&&this.refreshHoverImmediately()},addHover:function(t,e){this.painter.addHover&&(this.painter.addHover(t,e),this.refreshHover())},removeHover:function(t){this.painter.removeHover&&(this.painter.removeHover(t),this.refreshHover())},clearHover:function(){this.painter.clearHover&&(this.painter.clearHover(),this.refreshHover())},refreshHover:function(){this._needsRefreshHover=!0},refreshHoverImmediately:function(){this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.refreshHover()},resize:function(t){t=t||{},this.painter.resize(t.width,t.height),this.handler.resize()},clearAnimation:function(){this.animation.clear()},getWidth:function(){return this.painter.getWidth()},getHeight:function(){return this.painter.getHeight()},pathToImage:function(t,e){return this.painter.pathToImage(t,e)},setCursorStyle:function(t){this.handler.setCursorStyle(t)},findHover:function(t,e){return this.handler.findHover(t,e)},on:function(t,e,n){this.handler.on(t,e,n)},off:function(t,e){this.handler.off(t,e)},trigger:function(t,e){this.handler.trigger(t,e)},clear:function(){this.storage.delRoot(),this.painter.clear()},dispose:function(){this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,i(this.id)}},t.exports=p},function(t,e,n){var i=n(2),r=n(1);t.exports=function(t,e){r.each(e,function(e){e.update="updateView",i.registerAction(e,function(n,i){var r={};return i.eachComponent({mainType:"series",subType:t,query:n},function(t){t[e.method]&&t[e.method](n.name,n.dataIndex);var i=t.getData();i.each(function(e){var n=i.getName(e);r[n]=t.isSelected(n)||!1})}),{name:n.name,selected:r}})})}},function(t,e,n){"use strict";var i=n(17),r=n(28);t.exports=i.extend({type:"series.__base_bar__",getInitialData:function(t,e){return r(t.data,this,e)},getMarkerPosition:function(t){var e=this.coordinateSystem;if(e){var n=e.dataToPoint(t,!0),i=this.getData(),r=i.getLayout("offset"),a=i.getLayout("size"),o=e.getBaseAxis().isHorizontal()?0:1;return n[o]+=r+a/2,n}return[NaN,NaN]},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,itemStyle:{}}})},function(t,e,n){function i(t,e){"outside"===t.textPosition&&(t.textPosition=e)}var r=n(3),a={};a.setLabel=function(t,e,n,a,o,s,l){var h=n.getModel("label.normal"),u=n.getModel("label.emphasis");r.setLabelStyle(t,e,h,u,{labelFetcher:o,labelDataIndex:s,defaultText:o.getRawValue(s),isRectText:!0,autoColor:a}),i(t),i(e)},t.exports=a},function(t,e,n){var i=n(5),r={};r.findLabelValueDim=function(t){var e,n=i.otherDimToDataDim(t,"label");if(n.length)e=n[0];else for(var r,a=t.dimensions.slice();a.length&&(e=a.pop(),r=t.getDimensionInfo(e).type,"ordinal"===r||"time"===r););return e},t.exports=r},function(t,e,n){function i(t){return isNaN(t[0])||isNaN(t[1])}function r(t,e,n,r,a,o,l,v,m,y,x){for(var _=0,b=n,w=0;w<r;w++){var S=e[b];if(b>=a||b<0)break;if(i(S)){if(x){b+=o;continue}break}if(b===n)t[o>0?"moveTo":"lineTo"](S[0],S[1]),f(p,S);else if(m>0){var T=b+o,M=e[T];if(x)for(;M&&i(e[T]);)T+=o,M=e[T];var A=.5,I=e[_],M=e[T];if(!M||i(M))f(g,S);else{i(M)&&!x&&(M=S),s.sub(d,M,I);var C,P;if("x"===y||"y"===y){var L="x"===y?0:1;C=Math.abs(S[L]-I[L]),P=Math.abs(S[L]-M[L])}else C=s.dist(S,I),P=s.dist(S,M);A=P/(P+C),c(g,S,d,-m*(1-A))}h(p,p,v),u(p,p,l),h(g,g,v),u(g,g,l),t.bezierCurveTo(p[0],p[1],g[0],g[1],S[0],S[1]),c(p,S,d,m*A)}else t.lineTo(S[0],S[1]);_=b,b+=o}return w}function a(t,e){var n=[1/0,1/0],i=[-(1/0),-(1/0)];if(e)for(var r=0;r<t.length;r++){var a=t[r];a[0]<n[0]&&(n[0]=a[0]),a[1]<n[1]&&(n[1]=a[1]),a[0]>i[0]&&(i[0]=a[0]),a[1]>i[1]&&(i[1]=a[1])}return{min:e?n:i,max:e?i:n}}var o=n(8),s=n(6),l=n(77),h=s.min,u=s.max,c=s.scaleAndAdd,f=s.copy,d=[],p=[],g=[];t.exports={Polyline:o.extend({type:"ec-polyline",shape:{points:[],smooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},style:{fill:null,stroke:"#000"},brush:l(o.prototype.brush),buildPath:function(t,e){var n=e.points,o=0,s=n.length,l=a(n,e.smoothConstraint);if(e.connectNulls){for(;s>0&&i(n[s-1]);s--);for(;o<s&&i(n[o]);o++);}for(;o<s;)o+=r(t,n,o,s,s,1,l.min,l.max,e.smooth,e.smoothMonotone,e.connectNulls)+1}}),Polygon:o.extend({type:"ec-polygon",shape:{points:[],stackedOnPoints:[],smooth:0,stackedOnSmooth:0,smoothConstraint:!0,smoothMonotone:null,connectNulls:!1},brush:l(o.prototype.brush),buildPath:function(t,e){var n=e.points,o=e.stackedOnPoints,s=0,l=n.length,h=e.smoothMonotone,u=a(n,e.smoothConstraint),c=a(o,e.smoothConstraint);if(e.connectNulls){for(;l>0&&i(n[l-1]);l--);for(;s<l&&i(n[s]);s++);}for(;s<l;){var f=r(t,n,s,l,l,1,u.min,u.max,e.smooth,h,e.connectNulls);r(t,o,s+f-1,f,l,-1,c.min,c.max,e.stackedOnSmooth,h,e.connectNulls),s+=f+1,t.closePath()}}})}},,,function(t,e,n){var i=n(1),r={show:!0,zlevel:0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#333",width:1,type:"solid"}},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,lineStyle:{color:["#ccc"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.3)","rgba(200,200,200,0.3)"]}}},a=i.merge({boundaryGap:!0,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},r),o=i.merge({boundaryGap:[0,0],splitNumber:5},r),s=i.defaults({scale:!0,min:"dataMin",max:"dataMax"},o),l=i.defaults({scale:!0,logBase:10},o);t.exports={categoryAxis:a,valueAxis:o,timeAxis:s,logAxis:l}},function(t,e){t.exports={containStroke:function(t,e,n,i,r,a,o){if(0===r)return!1;var s=r,l=0,h=t;if(o>e+s&&o>i+s||o<e-s&&o<i-s||a>t+s&&a>n+s||a<t-s&&a<n-s)return!1;if(t===n)return Math.abs(a-t)<=s/2;l=(e-i)/(t-n),h=(t*i-n*e)/(t-n);var u=l*a-o+h,c=u*u/(l*l+1);return c<=s/2*s/2}}},function(t,e,n){var i=n(20);t.exports={containStroke:function(t,e,n,r,a,o,s,l,h){if(0===s)return!1;var u=s;if(h>e+u&&h>r+u&&h>o+u||h<e-u&&h<r-u&&h<o-u||l>t+u&&l>n+u&&l>a+u||l<t-u&&l<n-u&&l<a-u)return!1;var c=i.quadraticProjectPoint(t,e,n,r,a,o,l,h,null);return c<=u/2}}},function(t,e){t.exports=function(t,e,n,i,r,a){if(a>e&&a>i||a<e&&a<i)return 0;if(i===e)return 0;var o=i<e?1:-1,s=(a-e)/(i-e);1!==s&&0!==s||(o=i<e?.5:-.5);var l=s*(n-t)+t;return l>r?o:0}},function(t,e,n){"use strict";var i=n(1),r=n(39),a=function(t,e,n,i,a,o){this.x=null==t?0:t,this.y=null==e?0:e,this.x2=null==n?1:n,this.y2=null==i?0:i,this.type="linear",this.global=o||!1,r.call(this,a)};a.prototype={constructor:a},i.inherits(a,r),t.exports=a},function(t,e,n){"use strict";function i(t){r.each(a,function(e){this[e]=r.bind(t[e],t)},this)}var r=n(1),a=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isDisposed","on","off","getDataURL","getConnectedDataURL","getModel","getOption","getViewOfComponentModel","getViewOfSeriesModel"];t.exports=i},function(t,e,n){var i=n(1);n(60),n(108),n(109);var r=n(87),a=n(2);a.registerLayout(i.curry(r,"bar")),a.registerVisual(function(t){t.eachSeriesByType("bar",function(t){var e=t.getData();e.setVisual("legendSymbol","roundRect")})}),n(32)},function(t,e,n){t.exports=n(95).extend({type:"series.bar",dependencies:["grid","polar"],brushSelector:"rect"})},function(t,e,n){"use strict";function i(t,e,n){n.style.text=null,l.updateProps(n,{shape:{width:0}},e,t,function(){n.parent&&n.parent.remove(n)})}function r(t,e,n){n.style.text=null,l.updateProps(n,{shape:{r:n.shape.r0}},e,t,function(){n.parent&&n.parent.remove(n)})}function a(t,e,n,i,r,a,o,u){var c=e.getItemVisual(n,"color"),f=e.getItemVisual(n,"opacity"),d=i.getModel("itemStyle.normal"),p=i.getModel("itemStyle.emphasis").getBarItemStyle();u||t.setShape("r",d.get("barBorderRadius")||0),t.useStyle(s.defaults({fill:c,opacity:f},d.getBarItemStyle()));var g=i.getShallow("cursor");g&&t.attr("cursor",g);var v=o?r.height>0?"bottom":"top":r.width>0?"left":"right";u||h.setLabel(t.style,p,i,c,a,n,v),l.setHoverStyle(t,p)}function o(t,e){var n=t.get(u)||0;return Math.min(n,Math.abs(e.width),Math.abs(e.height))}var s=n(1),l=n(3),h=n(96),u=["itemStyle","normal","barBorderWidth"];s.extend(n(11).prototype,n(110));var c=n(2).extendChartView({type:"bar",render:function(t,e,n){var i=t.get("coordinateSystem");return"cartesian2d"!==i&&"polar"!==i||this._render(t,e,n),this.group},dispose:s.noop,_render:function(t,e,n){var o,s=this.group,h=t.getData(),u=this._data,c=t.coordinateSystem,p=c.getBaseAxis();"cartesian2d"===c.type?o=p.isHorizontal():"polar"===c.type&&(o="angle"===p.dim);var g=t.isAnimationEnabled()?t:null;h.diff(u).add(function(e){if(h.hasValue(e)){var n=h.getItemModel(e),i=d[c.type](h,e,n),r=f[c.type](h,e,n,i,o,g);h.setItemGraphicEl(e,r),s.add(r),a(r,h,e,n,i,t,o,"polar"===c.type)}}).update(function(e,n){var i=u.getItemGraphicEl(n);if(!h.hasValue(e))return void s.remove(i);var r=h.getItemModel(e),p=d[c.type](h,e,r);i?l.updateProps(i,{shape:p},g,e):i=f[c.type](h,e,r,p,o,g,!0),h.setItemGraphicEl(e,i),s.add(i),a(i,h,e,r,p,t,o,"polar"===c.type)}).remove(function(t){var e=u.getItemGraphicEl(t);"cartesian2d"===c.type?e&&i(t,g,e):e&&r(t,g,e)}).execute(),this._data=h},remove:function(t,e){var n=this.group,a=this._data;t.get("animation")?a&&a.eachItemGraphicEl(function(e){"sector"===e.type?r(e.dataIndex,t,e):i(e.dataIndex,t,e)}):n.removeAll()}}),f={cartesian2d:function(t,e,n,i,r,a,o){var h=new l.Rect({shape:s.extend({},i)});if(a){var u=h.shape,c=r?"height":"width",f={};u[c]=0,f[c]=i[c],l[o?"updateProps":"initProps"](h,{shape:f},a,e)}return h},polar:function(t,e,n,i,r,a,o){var h=new l.Sector({shape:s.extend({},i)});if(a){var u=h.shape,c=r?"r":"endAngle",f={};u[c]=r?0:i.startAngle,f[c]=i[c],l[o?"updateProps":"initProps"](h,{shape:f},a,e)}return h}},d={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=o(n,i),a=i.width>0?1:-1,s=i.height>0?1:-1;return{x:i.x+a*r/2,y:i.y+s*r/2,width:i.width-a*r,height:i.height-s*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle}}};t.exports=c},function(t,e,n){var i=n(31)([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["stroke","barBorderColor"],["lineWidth","barBorderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);t.exports={getBarItemStyle:function(t){var e=i.call(this,t);if(this.getBorderLineDash){var n=this.getBorderLineDash();n&&(e.lineDash=n)}return e}}},,,function(t,e,n){var i=n(1),r=n(2),a=r.PRIORITY;n(114),n(115),r.registerVisual(i.curry(n(51),"line","circle","line")),r.registerLayout(i.curry(n(64),"line")),r.registerProcessor(a.PROCESSOR.STATISTIC,i.curry(n(154),"line")),n(32)},function(t,e,n){"use strict";var i=n(28),r=n(17);t.exports=r.extend({type:"series.line",dependencies:["grid","polar"],getInitialData:function(t,e){return i(t.data,this,e)},defaultOption:{zlevel:0,z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,hoverAnimation:!0,clipOverflow:!0,label:{normal:{position:"top"}},lineStyle:{normal:{width:2,type:"solid"}},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:!1,connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0}})},function(t,e,n){"use strict";function i(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++){var i=t[n],r=e[n];if(i[0]!==r[0]||i[1]!==r[1])return}return!0}}function r(t){return"number"==typeof t?t:t?.3:0}function a(t){var e=t.getGlobalExtent();if(t.onBand){var n=t.getBandWidth()/2-1,i=e[1]>e[0]?1:-1;e[0]+=i*n,e[1]-=i*n}return e}function o(t){return t>=0?1:-1}function s(t,e){var n=t.getBaseAxis(),i=t.getOtherAxis(n),r=0;if(!n.onZero){var a=i.scale.getExtent();a[0]>0?r=a[0]:a[1]<0&&(r=a[1])}var s=i.dim,l="x"===s||"radius"===s?1:0;return e.mapArray([s],function(i,a){for(var h,u=e.stackedOn;u&&o(u.get(s,a))===o(i);){h=u;break}var c=[];return c[l]=e.get(n.dim,a),c[1-l]=h?h.get(s,a,!0):r,t.dataToPoint(c)},!0)}function l(t,e,n){var i=a(t.getAxis("x")),r=a(t.getAxis("y")),o=t.getBaseAxis().isHorizontal(),s=Math.min(i[0],i[1]),l=Math.min(r[0],r[1]),h=Math.max(i[0],i[1])-s,u=Math.max(r[0],r[1])-l,c=n.get("lineStyle.normal.width")||2,f=n.get("clipOverflow")?c/2:Math.max(h,u);o?(l-=f,u+=2*f):(s-=f,h+=2*f);var d=new m.Rect({shape:{x:s,y:l,width:h,height:u}});return e&&(d.shape[o?"width":"height"]=0,m.initProps(d,{shape:{width:h,height:u}},n)),d}function h(t,e,n){var i=t.getAngleAxis(),r=t.getRadiusAxis(),a=r.getExtent(),o=i.getExtent(),s=Math.PI/180,l=new m.Sector({shape:{cx:t.cx,cy:t.cy,r0:a[0],r:a[1],startAngle:-o[0]*s,endAngle:-o[1]*s,clockwise:i.inverse}});return e&&(l.shape.endAngle=-o[0]*s,m.initProps(l,{shape:{endAngle:-o[1]*s}},n)),l}function u(t,e,n){return"polar"===t.type?h(t,e,n):l(t,e,n)}function c(t,e,n){for(var i=e.getBaseAxis(),r="x"===i.dim||"radius"===i.dim?0:1,a=[],o=0;o<t.length-1;o++){var s=t[o+1],l=t[o];a.push(l);var h=[];switch(n){case"end":h[r]=s[r],h[1-r]=l[1-r],a.push(h);break;case"middle":var u=(l[r]+s[r])/2,c=[];h[r]=c[r]=u,h[1-r]=l[1-r],c[1-r]=s[1-r],a.push(h),a.push(c);break;default:h[r]=l[r],h[1-r]=s[1-r],a.push(h)}}return t[o]&&a.push(t[o]),a}function f(t,e){var n=t.getVisual("visualMeta");if(n&&n.length&&t.count()){for(var i,r=n.length-1;r>=0;r--)if(n[r].dimension<2){i=n[r];break}if(i&&"cartesian2d"===e.type){var a=i.dimension,o=t.dimensions[a],s=e.getAxis(o),l=d.map(i.stops,function(t){return{coord:s.toGlobalCoord(s.dataToCoord(t.value)),color:t.color}}),h=l.length,u=i.outerColors.slice();h&&l[0].coord>l[h-1].coord&&(l.reverse(),u.reverse());var c=10,f=l[0].coord-c,p=l[h-1].coord+c,g=p-f;if(g<.001)return"transparent";d.each(l,function(t){t.offset=(t.coord-f)/g}),l.push({offset:h?l[h-1].offset:.5,color:u[1]||"transparent"}),l.unshift({offset:h?l[0].offset:.5,color:u[0]||"transparent"});var v=new m.LinearGradient(0,0,0,0,l,!0);return v[o]=f,v[o+"2"]=p,v}}}var d=n(1),p=n(46),g=n(57),v=n(116),m=n(3),y=n(5),x=n(98),_=n(30);t.exports=_.extend({type:"line",init:function(){var t=new m.Group,e=new p;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t},render:function(t,e,n){var a=t.coordinateSystem,o=this.group,l=t.getData(),h=t.getModel("lineStyle.normal"),p=t.getModel("areaStyle.normal"),g=l.mapArray(l.getItemLayout,!0),v="polar"===a.type,m=this._coordSys,y=this._symbolDraw,x=this._polyline,_=this._polygon,b=this._lineGroup,w=t.get("animation"),S=!p.isEmpty(),T=s(a,l),M=t.get("showSymbol"),A=M&&!v&&!t.get("showAllSymbol")&&this._getSymbolIgnoreFunc(l,a),I=this._data;I&&I.eachItemGraphicEl(function(t,e){t.__temp&&(o.remove(t),I.setItemGraphicEl(e,null))}),M||y.remove(),o.add(b);var C=!v&&t.get("step");x&&m.type===a.type&&C===this._step?(S&&!_?_=this._newPolygon(g,T,a,w):_&&!S&&(b.remove(_),_=this._polygon=null),b.setClipPath(u(a,!1,t)),M&&y.updateData(l,A),l.eachItemGraphicEl(function(t){t.stopAnimation(!0)}),i(this._stackedOnPoints,T)&&i(this._points,g)||(w?this._updateAnimation(l,T,a,n,C):(C&&(g=c(g,a,C),T=c(T,a,C)),x.setShape({points:g}),_&&_.setShape({points:g,stackedOnPoints:T})))):(M&&y.updateData(l,A),C&&(g=c(g,a,C),T=c(T,a,C)),x=this._newPolyline(g,a,w),S&&(_=this._newPolygon(g,T,a,w)),b.setClipPath(u(a,!0,t)));var P=f(l,a)||l.getVisual("color");x.useStyle(d.defaults(h.getLineStyle(),{fill:"none",stroke:P,lineJoin:"bevel"}));var L=t.get("smooth");if(L=r(t.get("smooth")),x.setShape({smooth:L,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")}),_){var k=l.stackedOn,D=0;if(_.useStyle(d.defaults(p.getAreaStyle(),{fill:P,opacity:.7,lineJoin:"bevel"})),k){var O=k.hostModel;D=r(O.get("smooth"))}_.setShape({smooth:L,stackedOnSmooth:D,smoothMonotone:t.get("smoothMonotone"),connectNulls:t.get("connectNulls")})}this._data=l,this._coordSys=a,this._stackedOnPoints=T,this._points=g,this._step=C},dispose:function(){},highlight:function(t,e,n,i){var r=t.getData(),a=y.queryDataIndex(r,i);if(!(a instanceof Array)&&null!=a&&a>=0){var o=r.getItemGraphicEl(a);if(!o){var s=r.getItemLayout(a);if(!s)return;o=new g(r,a),o.position=s,o.setZ(t.get("zlevel"),t.get("z")),o.ignore=isNaN(s[0])||isNaN(s[1]),o.__temp=!0,r.setItemGraphicEl(a,o),o.stopSymbolAnimation(!0),this.group.add(o)}o.highlight()}else _.prototype.highlight.call(this,t,e,n,i)},downplay:function(t,e,n,i){var r=t.getData(),a=y.queryDataIndex(r,i);if(null!=a&&a>=0){var o=r.getItemGraphicEl(a);o&&(o.__temp?(r.setItemGraphicEl(a,null),this.group.remove(o)):o.downplay())}else _.prototype.downplay.call(this,t,e,n,i)},_newPolyline:function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new x.Polyline({shape:{points:t},silent:!0,z2:10}),this._lineGroup.add(e),this._polyline=e,e},_newPolygon:function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new x.Polygon({shape:{points:t,stackedOnPoints:e},silent:!0}),this._lineGroup.add(n),this._polygon=n,n},_getSymbolIgnoreFunc:function(t,e){var n=e.getAxesByScale("ordinal")[0];if(n&&n.isLabelIgnored)return d.bind(n.isLabelIgnored,n)},_updateAnimation:function(t,e,n,i,r){var a=this._polyline,o=this._polygon,s=t.hostModel,l=v(this._data,t,this._stackedOnPoints,e,this._coordSys,n),h=l.current,u=l.stackedOnCurrent,f=l.next,d=l.stackedOnNext;r&&(h=c(l.current,n,r),u=c(l.stackedOnCurrent,n,r),f=c(l.next,n,r),d=c(l.stackedOnNext,n,r)),a.shape.__points=l.current,a.shape.points=h,m.updateProps(a,{shape:{points:f}},s),o&&(o.setShape({points:h,stackedOnPoints:u}),m.updateProps(o,{shape:{points:f,stackedOnPoints:d}},s));for(var p=[],g=l.status,y=0;y<g.length;y++){var x=g[y].cmd;if("="===x){var _=t.getItemGraphicEl(g[y].idx1);_&&p.push({el:_,ptIdx:y})}}a.animators&&a.animators.length&&a.animators[0].during(function(){for(var t=0;t<p.length;t++){var e=p[t].el;e.attr("position",a.shape.__points[p[t].ptIdx])}})},remove:function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl(function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._data=null}})},function(t,e){function n(t){return t>=0?1:-1}function i(t,e,i){for(var r,a=t.getBaseAxis(),o=t.getOtherAxis(a),s=a.onZero?0:o.scale.getExtent()[0],l=o.dim,h="x"===l||"radius"===l?1:0,u=e.stackedOn,c=e.get(l,i);u&&n(u.get(l,i))===n(c);){r=u;break}var f=[];return f[h]=e.get(a.dim,i),f[1-h]=r?r.get(l,i,!0):s,t.dataToPoint(f)}function r(t,e){var n=[];return e.diff(t).add(function(t){n.push({cmd:"+",idx:t})}).update(function(t,e){n.push({cmd:"=",idx:e,idx1:t})}).remove(function(t){n.push({cmd:"-",idx:t})}).execute(),n}t.exports=function(t,e,n,a,o,s){for(var l=r(t,e),h=[],u=[],c=[],f=[],d=[],p=[],g=[],v=s.dimensions,m=0;m<l.length;m++){var y=l[m],x=!0;switch(y.cmd){case"=":var _=t.getItemLayout(y.idx),b=e.getItemLayout(y.idx1);(isNaN(_[0])||isNaN(_[1]))&&(_=b.slice()),h.push(_),u.push(b),c.push(n[y.idx]),f.push(a[y.idx1]),g.push(e.getRawIndex(y.idx1));break;case"+":var w=y.idx;h.push(o.dataToPoint([e.get(v[0],w,!0),e.get(v[1],w,!0)])),u.push(e.getItemLayout(w).slice()),c.push(i(o,e,w)),f.push(a[w]),g.push(e.getRawIndex(w));break;case"-":var w=y.idx,S=t.getRawIndex(w);S!==w?(h.push(t.getItemLayout(w)),u.push(s.dataToPoint([t.get(v[0],w,!0),t.get(v[1],w,!0)])),c.push(n[w]),f.push(i(s,t,w)),g.push(S)):x=!1}x&&(d.push(y),p.push(p.length))}p.sort(function(t,e){return g[t]-g[e]});for(var T=[],M=[],A=[],I=[],C=[],m=0;m<p.length;m++){var w=p[m];T[m]=h[w],M[m]=u[w],A[m]=c[w],I[m]=f[w],C[m]=d[w]}return{current:T,next:M,stackedOnCurrent:A,stackedOnNext:I,status:C}}},function(t,e,n){var i=n(1),r=n(2);n(118),n(119),n(94)("pie",[{type:"pieToggleSelect",event:"pieselectchanged",method:"toggleSelected"},{type:"pieSelect",event:"pieselected",method:"select"},{type:"pieUnSelect",event:"pieunselected",method:"unSelect"}]),r.registerVisual(i.curry(n(89),"pie")),r.registerLayout(i.curry(n(121),"pie")),r.registerProcessor(i.curry(n(66),"pie"))},function(t,e,n){"use strict";var i=n(14),r=n(1),a=n(5),o=n(4),s=n(25),l=n(83),h=n(2).extendSeriesModel({type:"series.pie",init:function(t){h.superApply(this,"init",arguments),this.legendDataProvider=function(){return this.getRawData()},this.updateSelectedMap(t.data),this._defaultLabelLine(t)},mergeOption:function(t){h.superCall(this,"mergeOption",t),this.updateSelectedMap(this.option.data)},getInitialData:function(t,e){var n=s(["value"],t.data),r=new i(n,this);return r.initData(t.data),r},getDataParams:function(t){var e=this.getData(),n=h.superCall(this,"getDataParams",t),i=[];return e.each("value",function(t){i.push(t)}),n.percent=o.getPercentWithPrecision(i,t,e.hostModel.get("percentPrecision")),n.$vars.push("percent"),n},_defaultLabelLine:function(t){a.defaultEmphasis(t.labelLine,["show"]);var e=t.labelLine.normal,n=t.labelLine.emphasis;e.show=e.show&&t.label.normal.show,n.show=n.show&&t.label.emphasis.show},defaultOption:{zlevel:0,z:2,legendHoverLink:!0,hoverAnimation:!0,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,selectedOffset:10,hoverOffset:10,avoidLabelOverlap:!0,percentPrecision:2,stillShowZeroSum:!0,label:{normal:{rotate:!1,show:!0,position:"outer"},emphasis:{}},labelLine:{normal:{show:!0,length:15,length2:15,smooth:!1,lineStyle:{width:1,type:"solid"}}},itemStyle:{normal:{borderWidth:1},emphasis:{}},animationType:"expansion",animationEasing:"cubicOut",data:[]}});r.mixin(h,l),t.exports=h},function(t,e,n){function i(t,e,n,i){var a=e.getData(),o=this.dataIndex,s=a.getName(o),l=e.get("selectedOffset");i.dispatchAction({type:"pieToggleSelect",from:t,name:s,seriesId:e.id}),a.each(function(t){r(a.getItemGraphicEl(t),a.getItemLayout(t),e.isSelected(a.getName(t)),l,n)})}function r(t,e,n,i,r){var a=(e.startAngle+e.endAngle)/2,o=Math.cos(a),s=Math.sin(a),l=n?i:0,h=[o*l,s*l];r?t.animate().when(200,{position:h}).start("bounceOut"):t.attr("position",h)}function a(t,e){function n(){a.ignore=a.hoverIgnore,s.ignore=s.hoverIgnore}function i(){a.ignore=a.normalIgnore,s.ignore=s.normalIgnore}o.Group.call(this);var r=new o.Sector({z2:2}),a=new o.Polyline,s=new o.Text;this.add(r),this.add(a),this.add(s),this.updateData(t,e,!0),this.on("emphasis",n).on("normal",i).on("mouseover",n).on("mouseout",i)}var o=n(3),s=n(1),l=a.prototype;l.updateData=function(t,e,n){function i(){l.stopAnimation(!0),l.animateTo({shape:{r:c.r+h.get("hoverOffset")}},300,"elasticOut")}function a(){l.stopAnimation(!0),l.animateTo({shape:{r:c.r}},300,"elasticOut")}var l=this.childAt(0),h=t.hostModel,u=t.getItemModel(e),c=t.getItemLayout(e),f=s.extend({},c);if(f.label=null,n){l.setShape(f);var d=h.getShallow("animationType");"scale"===d?(l.shape.r=c.r0,o.initProps(l,{shape:{r:c.r}},h,e)):(l.shape.endAngle=c.startAngle,o.updateProps(l,{shape:{endAngle:c.endAngle}},h,e))}else o.updateProps(l,{shape:f},h,e);var p=u.getModel("itemStyle"),g=t.getItemVisual(e,"color");l.useStyle(s.defaults({lineJoin:"bevel",fill:g},p.getModel("normal").getItemStyle())),l.hoverStyle=p.getModel("emphasis").getItemStyle();var v=u.getShallow("cursor");v&&l.attr("cursor",v),r(this,t.getItemLayout(e),u.get("selected"),h.get("selectedOffset"),h.get("animation")),l.off("mouseover").off("mouseout").off("emphasis").off("normal"),u.get("hoverAnimation")&&h.isAnimationEnabled()&&l.on("mouseover",i).on("mouseout",a).on("emphasis",i).on("normal",a),this._updateLabel(t,e),o.setHoverStyle(this)},l._updateLabel=function(t,e){var n=this.childAt(1),i=this.childAt(2),r=t.hostModel,a=t.getItemModel(e),s=t.getItemLayout(e),l=s.label,h=t.getItemVisual(e,"color");o.updateProps(n,{shape:{points:l.linePoints||[[l.x,l.y],[l.x,l.y],[l.x,l.y]]}},r,e),o.updateProps(i,{style:{x:l.x,y:l.y}},r,e),i.attr({rotation:l.rotation,origin:[l.x,l.y],z2:10});var u=a.getModel("label.normal"),c=a.getModel("label.emphasis"),f=a.getModel("labelLine.normal"),d=a.getModel("labelLine.emphasis"),h=t.getItemVisual(e,"color");o.setLabelStyle(i.style,i.hoverStyle={},u,c,{labelFetcher:t.hostModel,labelDataIndex:e,defaultText:t.getName(e),autoColor:h,useInsideStyle:!!l.inside},{textAlign:l.textAlign,textVerticalAlign:l.verticalAlign,opacity:t.getItemVisual(e,"opacity")}),i.ignore=i.normalIgnore=!u.get("show"),i.hoverIgnore=!c.get("show"),n.ignore=n.normalIgnore=!f.get("show"),n.hoverIgnore=!d.get("show"),n.setStyle({stroke:h,opacity:t.getItemVisual(e,"opacity")}),n.setStyle(f.getModel("lineStyle").getLineStyle()),n.hoverStyle=d.getModel("lineStyle").getLineStyle();var p=f.get("smooth");p&&p===!0&&(p=.4),n.setShape({smooth:p})},s.inherits(a,o.Group);var h=n(30).extend({type:"pie",init:function(){var t=new o.Group;this._sectorGroup=t},render:function(t,e,n,r){if(!r||r.from!==this.uid){var o=t.getData(),l=this._data,h=this.group,u=e.get("animation"),c=!l,f=t.get("animationType"),d=s.curry(i,this.uid,t,u,n),p=t.get("selectedMode");if(o.diff(l).add(function(t){var e=new a(o,t);c&&"scale"!==f&&e.eachChild(function(t){t.stopAnimation(!0)}),p&&e.on("click",d),o.setItemGraphicEl(t,e),h.add(e)}).update(function(t,e){var n=l.getItemGraphicEl(e);n.updateData(o,t),n.off("click"),p&&n.on("click",d),h.add(n),o.setItemGraphicEl(t,n)}).remove(function(t){var e=l.getItemGraphicEl(t);h.remove(e)}).execute(),u&&c&&o.count()>0&&"scale"!==f){var g=o.getItemLayout(0),v=Math.max(n.getWidth(),n.getHeight())/2,m=s.bind(h.removeClipPath,h);h.setClipPath(this._createClipPath(g.cx,g.cy,v,g.startAngle,g.clockwise,m,t))}this._data=o}},dispose:function(){},_createClipPath:function(t,e,n,i,r,a,s){var l=new o.Sector({shape:{cx:t,cy:e,r0:0,r:n,startAngle:i,endAngle:i,clockwise:r}});return o.initProps(l,{shape:{endAngle:i+(r?1:-1)*Math.PI*2}},s,a),l},containPoint:function(t,e){var n=e.getData(),i=n.getItemLayout(0);if(i){var r=t[0]-i.cx,a=t[1]-i.cy,o=Math.sqrt(r*r+a*a);return o<=i.r&&o>=i.r0}}});t.exports=h},function(t,e,n){"use strict";function i(t,e,n,i,r,a,o){function s(e,n,i,r){for(var a=e;a<n;a++)if(t[a].y+=i,a>e&&a+1<n&&t[a+1].y>t[a].y+t[a].height)return void l(a,i/2);l(n-1,i/2)}function l(e,n){for(var i=e;i>=0&&(t[i].y-=n,!(i>0&&t[i].y>t[i-1].y+t[i-1].height));i--);}function h(t,e,n,i,r,a){for(var o=a>0?e?Number.MAX_VALUE:0:e?Number.MAX_VALUE:0,s=0,l=t.length;s<l;s++)if("center"!==t[s].position){var h=Math.abs(t[s].y-i),u=t[s].len,c=t[s].len2,f=h<r+u?Math.sqrt((r+u+c)*(r+u+c)-h*h):Math.abs(t[s].x-n);e&&f>=o&&(f=o-10),!e&&f<=o&&(f=o+10),t[s].x=n+f*a,o=f}}t.sort(function(t,e){return t.y-e.y});for(var u,c=0,f=t.length,d=[],p=[],g=0;g<f;g++)u=t[g].y-c,u<0&&s(g,f,-u,r),c=t[g].y+t[g].height;o-c<0&&l(f-1,c-o);for(var g=0;g<f;g++)t[g].y>=n?p.push(t[g]):d.push(t[g]);h(d,!1,e,n,i,r),h(p,!0,e,n,i,r)}function r(t,e,n,r,a,o){for(var s=[],l=[],h=0;h<t.length;h++)t[h].x<e?s.push(t[h]):l.push(t[h]);i(l,e,n,r,1,a,o),i(s,e,n,r,-1,a,o);for(var h=0;h<t.length;h++){var u=t[h].linePoints;if(u){var c=u[1][0]-u[2][0];t[h].x<e?u[2][0]=t[h].x+3:u[2][0]=t[h].x-3,u[1][1]=u[2][1]=t[h].y,u[1][0]=u[2][0]+c}}}var a=n(16);t.exports=function(t,e,n,i){var o,s,l=t.getData(),h=[],u=!1;l.each(function(n){var i,r,c,f,d=l.getItemLayout(n),p=l.getItemModel(n),g=p.getModel("label.normal"),v=g.get("position")||p.get("label.emphasis.position"),m=p.getModel("labelLine.normal"),y=m.get("length"),x=m.get("length2"),_=(d.startAngle+d.endAngle)/2,b=Math.cos(_),w=Math.sin(_);o=d.cx,s=d.cy;var S="inside"===v||"inner"===v;if("center"===v)i=d.cx,r=d.cy,f="center";else{var T=(S?(d.r+d.r0)/2*b:d.r*b)+o,M=(S?(d.r+d.r0)/2*w:d.r*w)+s;if(i=T+3*b,r=M+3*w,!S){var A=T+b*(y+e-d.r),I=M+w*(y+e-d.r),C=A+(b<0?-1:1)*x,P=I;i=C+(b<0?-5:5),r=P,c=[[T,M],[A,I],[C,P]]}f=S?"center":b>0?"left":"right"}var L=g.getFont(),k=g.get("rotate")?b<0?-_+Math.PI:-_:0,D=t.getFormattedLabel(n,"normal")||l.getName(n),O=a.getBoundingRect(D,L,f,"top");u=!!k,d.label={x:i,y:r,position:v,height:O.height,len:y,len2:x,linePoints:c,textAlign:f,verticalAlign:"middle",rotation:k,inside:S},S||h.push(d.label)}),!u&&t.get("avoidLabelOverlap")&&r(h,o,s,e,n,i)}},function(t,e,n){var i=n(4),r=i.parsePercent,a=n(120),o=n(1),s=2*Math.PI,l=Math.PI/180;t.exports=function(t,e,n,h){e.eachSeriesByType(t,function(t){var e=t.get("center"),h=t.get("radius");o.isArray(h)||(h=[0,h]),o.isArray(e)||(e=[e,e]);var u=n.getWidth(),c=n.getHeight(),f=Math.min(u,c),d=r(e[0],u),p=r(e[1],c),g=r(h[0],f/2),v=r(h[1],f/2),m=t.getData(),y=-t.get("startAngle")*l,x=t.get("minAngle")*l,_=0;m.each("value",function(t){!isNaN(t)&&_++});var b=m.getSum("value"),w=Math.PI/(b||_)*2,S=t.get("clockwise"),T=t.get("roseType"),M=t.get("stillShowZeroSum"),A=m.getDataExtent("value");A[0]=0;var I=s,C=0,P=y,L=S?1:-1;if(m.each("value",function(t,e){var n;if(isNaN(t))return void m.setItemLayout(e,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:S,cx:d,cy:p,r0:g,r:T?NaN:v});n="area"!==T?0===b&&M?w:t*w:s/_,n<x?(n=x,I-=x):C+=t;var r=P+L*n;m.setItemLayout(e,{angle:n,startAngle:P,endAngle:r,clockwise:S,cx:d,cy:p,r0:g,r:T?i.linearMap(t,A,[g,v]):v}),P=r},!0),I<s&&_)if(I<=.001){var k=s/_;m.each("value",function(t,e){if(!isNaN(t)){var n=m.getItemLayout(e);n.angle=k,n.startAngle=y+L*e*k,n.endAngle=y+L*(e+1)*k}})}else w=I/C,P=y,m.each("value",function(t,e){if(!isNaN(t)){var n=m.getItemLayout(e),i=n.angle===x?x:t*w;n.startAngle=P,n.endAngle=P+L*i,P+=L*i}});a(t,v,u,c)})}},function(t,e,n){"use strict";n(63),n(123)},function(t,e,n){var i=n(1),r=n(3),a=n(40),o=n(41),s=n(80),l=a.ifIgnoreOnTick,h=a.getInterval,u=["axisLine","axisTickLabel","axisName"],c=["splitArea","splitLine"],f=o.extend({type:"cartesianAxis",axisPointerClass:"CartesianAxisPointer",render:function(t,e,n,o){this.group.removeAll();var l=this._axisGroup;if(this._axisGroup=new r.Group,this.group.add(this._axisGroup),t.get("show")){var h=t.getCoordSysModel(),d=s.layout(h,t),p=new a(t,d);i.each(u,p.add,p),this._axisGroup.add(p.getGroup()),i.each(c,function(e){t.get(e+".show")&&this["_"+e](t,h,d.labelInterval)},this),r.groupTransition(l,this._axisGroup,t),f.superCall(this,"render",t,e,n,o)}},_splitLine:function(t,e,n){var a=t.axis;if(!a.scale.isBlank()){var o=t.getModel("splitLine"),s=o.getModel("lineStyle"),u=s.get("color"),c=h(o,n);u=i.isArray(u)?u:[u];for(var f=e.coordinateSystem.getRect(),d=a.isHorizontal(),p=0,g=a.getTicksCoords(),v=a.scale.getTicks(),m=t.get("axisLabel.showMinLabel"),y=t.get("axisLabel.showMaxLabel"),x=[],_=[],b=s.getLineStyle(),w=0;w<g.length;w++)if(!l(a,w,c,g.length,m,y)){var S=a.toGlobalCoord(g[w]);d?(x[0]=S,x[1]=f.y,_[0]=S,_[1]=f.y+f.height):(x[0]=f.x,x[1]=S,_[0]=f.x+f.width,_[1]=S);var T=p++%u.length;this._axisGroup.add(new r.Line(r.subPixelOptimizeLine({anid:"line_"+v[w],shape:{x1:x[0],y1:x[1],x2:_[0],y2:_[1]
},style:i.defaults({stroke:u[T]},b),silent:!0})))}}},_splitArea:function(t,e,n){var a=t.axis;if(!a.scale.isBlank()){var o=t.getModel("splitArea"),s=o.getModel("areaStyle"),u=s.get("color"),c=e.coordinateSystem.getRect(),f=a.getTicksCoords(),d=a.scale.getTicks(),p=a.toGlobalCoord(f[0]),g=a.toGlobalCoord(f[0]),v=0,m=h(o,n),y=s.getAreaStyle();u=i.isArray(u)?u:[u];for(var x=t.get("axisLabel.showMinLabel"),_=t.get("axisLabel.showMaxLabel"),b=1;b<f.length;b++)if(!l(a,b,m,f.length,x,_)){var w,S,T,M,A=a.toGlobalCoord(f[b]);a.isHorizontal()?(w=p,S=c.y,T=A-w,M=c.height):(w=c.x,S=g,T=c.width,M=A-S);var I=v++%u.length;this._axisGroup.add(new r.Rect({anid:"area_"+d[b],shape:{x:w,y:S,width:T,height:M},style:i.defaults({fill:u[I]},y),silent:!0})),p=w+T,g=S+M}}}});f.extend({type:"xAxis"}),f.extend({type:"yAxis"})},,,,,,,,,,,,,,,function(t,e,n){var i=n(1),r=n(33),a=function(t,e,n,i,a){r.call(this,t,e,n),this.type=i||"value",this.position=a||"bottom"};a.prototype={constructor:a,index:0,onZero:!1,model:null,isHorizontal:function(){var t=this.position;return"top"===t||"bottom"===t},getGlobalExtent:function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},getOtherAxis:function(){this.grid.getOtherAxis()},isLabelIgnored:function(t){if("category"===this.type){var e=this.getLabelInterval();return"function"==typeof e&&!e(t,this.scale.getLabel(t))||t%(e+1)}},pointToData:function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},toLocalCoord:null,toGlobalCoord:null},i.inherits(a,r),t.exports=a},function(t,e,n){"use strict";function i(t){return this._axes[t]}var r=n(1),a=function(t){this._axes={},this._dimList=[],this.name=t||""};a.prototype={constructor:a,type:"cartesian",getAxis:function(t){return this._axes[t]},getAxes:function(){return r.map(this._dimList,i,this)},getAxesByScale:function(t){return t=t.toLowerCase(),r.filter(this.getAxes(),function(e){return e.scale.type===t})},addAxis:function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},dataToCoord:function(t){return this._dataCoordConvert(t,"dataToCoord")},coordToData:function(t){return this._dataCoordConvert(t,"coordToData")},_dataCoordConvert:function(t,e){for(var n=this._dimList,i=t instanceof Array?[]:{},r=0;r<n.length;r++){var a=n[r],o=this._axes[a];i[a]=o[e](t[a])}return i}},t.exports=a},function(t,e,n){"use strict";function i(t){a.call(this,t)}var r=n(1),a=n(139);i.prototype={constructor:i,type:"cartesian2d",dimensions:["x","y"],getBaseAxis:function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},containPoint:function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},containData:function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},dataToPoint:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return[n.toGlobalCoord(n.dataToCoord(t[0],e)),i.toGlobalCoord(i.dataToCoord(t[1],e))]},pointToData:function(t,e){var n=this.getAxis("x"),i=this.getAxis("y");return[n.coordToData(n.toLocalCoord(t[0]),e),i.coordToData(i.toLocalCoord(t[1]),e)]},getOtherAxis:function(t){return this.getAxis("x"===t.dim?"y":"x")}},r.inherits(i,a),t.exports=i},function(t,e,n){"use strict";n(63);var i=n(13);t.exports=i.extend({type:"grid",dependencies:["xAxis","yAxis"],layoutMode:"box",coordinateSystem:null,defaultOption:{show:!1,zlevel:0,z:0,left:"10%",top:60,right:"10%",bottom:60,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"}})},function(t,e,n){var i=n(28),r=n(24),a=n(18),o=n(42),s=n(11),l=n(1);t.exports={createList:function(t){var e=t.get("data");return i(e,t,t.ecModel)},completeDimensions:n(25),createSymbol:r.createSymbol,createScale:function(t,e){var n=e;e instanceof s||(n=new s(e),l.mixin(n,o));var i=a.createScaleByModel(n);return i.setExtent(t[0],t[1]),a.niceScaleExtent(i,n),i},mixinAxisModelCommonMethods:function(t){l.mixin(t,o)}}},function(t,e,n){var i=n(3),r=n(1),a=Math.PI;t.exports=function(t,e){e=e||{},r.defaults(e,{text:"loading",color:"#c23531",textColor:"#000",maskColor:"rgba(255, 255, 255, 0.8)",zlevel:0});var n=new i.Rect({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4}),o=new i.Arc({shape:{startAngle:-a/2,endAngle:-a/2+.1,r:10},style:{stroke:e.color,lineCap:"round",lineWidth:5},zlevel:e.zlevel,z:10001}),s=new i.Rect({style:{fill:"none",text:e.text,textPosition:"right",textDistance:10,textFill:e.textColor},zlevel:e.zlevel,z:10001});o.animateShape(!0).when(1e3,{endAngle:3*a/2}).start("circularInOut"),o.animateShape(!0).when(1e3,{startAngle:3*a/2}).delay(300).start("circularInOut");var l=new i.Group;return l.add(o),l.add(s),l.add(n),l.resize=function(){var e=t.getWidth()/2,i=t.getHeight()/2;o.setShape({cx:e,cy:i});var r=o.shape.r;s.setShape({x:e-r,y:i-r,width:2*r,height:2*r}),n.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},l.resize(),l}},function(t,e,n){function i(t,e){u.each(e,function(e,n){x.hasClass(n)||("object"==typeof e?t[n]=t[n]?u.merge(t[n],e,!1):u.clone(e):null==t[n]&&(t[n]=e))})}function r(t){t=t,this.option={},this.option[b]=1,this._componentsMap=u.createHashMap({series:[]}),this._seriesIndices=null,i(t,this._theme.option),u.merge(t,_,!1),this.mergeOption(t)}function a(t,e){u.isArray(e)||(e=e?[e]:[]);var n={};return d(e,function(e){n[e]=(t.get(e)||[]).slice()}),n}function o(t,e,n){var i=e.type?e.type:n?n.subType:x.determineSubType(t,e);return i}function s(t){return g(t,function(t){return t.componentIndex})||[]}function l(t,e){return e.hasOwnProperty("subType")?p(t,function(t){return t.subType===e.subType}):t}function h(t){}var u=n(1),c=n(5),f=n(11),d=u.each,p=u.filter,g=u.map,v=u.isArray,m=u.indexOf,y=u.isObject,x=n(13),_=n(146),b="\0_ec_inner",w=f.extend({constructor:w,init:function(t,e,n,i){n=n||{},this.option=null,this._theme=new f(n),this._optionManager=i},setOption:function(t,e){u.assert(!(b in t),"please use chart.getOption()"),this._optionManager.setOption(t,e),this.resetOption(null)},resetOption:function(t){var e=!1,n=this._optionManager;if(!t||"recreate"===t){var i=n.mountOption("recreate"===t);this.option&&"recreate"!==t?(this.restoreData(),this.mergeOption(i)):r.call(this,i),e=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var a=n.getTimelineOption(this);a&&(this.mergeOption(a),e=!0)}if(!t||"recreate"===t||"media"===t){var o=n.getMediaOption(this,this._api);o.length&&d(o,function(t){this.mergeOption(t,e=!0)},this)}return e},mergeOption:function(t){function e(e,r){var l=c.normalizeToArray(t[e]),h=c.mappingToExists(i.get(e),l);c.makeIdAndName(h),d(h,function(t,n){var i=t.option;y(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=o(e,i,t.exist))});var f=a(i,r);n[e]=[],i.set(e,[]),d(h,function(t,r){var a=t.exist,o=t.option;if(u.assert(y(o)||a,"Empty component definition"),o){var s=x.getClass(e,t.keyInfo.subType,!0);if(a&&a instanceof s)a.name=t.keyInfo.name,a.mergeOption(o,this),a.optionUpdated(o,!1);else{var l=u.extend({dependentModels:f,componentIndex:r},t.keyInfo);a=new s(o,this,this,l),u.extend(a,l),a.init(o,this,this,l),a.optionUpdated(null,!0)}}else a.mergeOption({},this),a.optionUpdated({},!1);i.get(e)[r]=a,n[e][r]=a.option},this),"series"===e&&(this._seriesIndices=s(i.get("series")))}var n=this.option,i=this._componentsMap,r=[];d(t,function(t,e){null!=t&&(x.hasClass(e)?r.push(e):n[e]=null==n[e]?u.clone(t):u.merge(n[e],t,!0))}),x.topologicalTravel(r,x.getAllClassMainTypes(),e,this),this._seriesIndices=this._seriesIndices||[]},getOption:function(){var t=u.clone(this.option);return d(t,function(e,n){if(x.hasClass(n)){for(var e=c.normalizeToArray(e),i=e.length-1;i>=0;i--)c.isIdInner(e[i])&&e.splice(i,1);t[n]=e}}),delete t[b],t},getTheme:function(){return this._theme},getComponent:function(t,e){var n=this._componentsMap.get(t);if(n)return n[e||0]},queryComponents:function(t){var e=t.mainType;if(!e)return[];var n=t.index,i=t.id,r=t.name,a=this._componentsMap.get(e);if(!a||!a.length)return[];var o;if(null!=n)v(n)||(n=[n]),o=p(g(n,function(t){return a[t]}),function(t){return!!t});else if(null!=i){var s=v(i);o=p(a,function(t){return s&&m(i,t.id)>=0||!s&&t.id===i})}else if(null!=r){var h=v(r);o=p(a,function(t){return h&&m(r,t.name)>=0||!h&&t.name===r})}else o=a.slice();return l(o,t)},findComponents:function(t){function e(t){var e=r+"Index",n=r+"Id",i=r+"Name";return!t||null==t[e]&&null==t[n]&&null==t[i]?null:{mainType:r,index:t[e],id:t[n],name:t[i]}}function n(e){return t.filter?p(e,t.filter):e}var i=t.query,r=t.mainType,a=e(i),o=a?this.queryComponents(a):this._componentsMap.get(r);return n(l(o,t))},eachComponent:function(t,e,n){var i=this._componentsMap;if("function"==typeof t)n=e,e=t,i.each(function(t,i){d(t,function(t,r){e.call(n,i,t,r)})});else if(u.isString(t))d(i.get(t),e,n);else if(y(t)){var r=this.findComponents(t);d(r,e,n)}},getSeriesByName:function(t){var e=this._componentsMap.get("series");return p(e,function(e){return e.name===t})},getSeriesByIndex:function(t){return this._componentsMap.get("series")[t]},getSeriesByType:function(t){var e=this._componentsMap.get("series");return p(e,function(e){return e.subType===t})},getSeries:function(){return this._componentsMap.get("series").slice()},eachSeries:function(t,e){h(this),d(this._seriesIndices,function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)},this)},eachRawSeries:function(t,e){d(this._componentsMap.get("series"),t,e)},eachSeriesByType:function(t,e,n){h(this),d(this._seriesIndices,function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)},this)},eachRawSeriesByType:function(t,e,n){return d(this.getSeriesByType(t),e,n)},isSeriesFiltered:function(t){return h(this),u.indexOf(this._seriesIndices,t.componentIndex)<0},getCurrentSeriesIndices:function(){return(this._seriesIndices||[]).slice()},filterSeries:function(t,e){h(this);var n=p(this._componentsMap.get("series"),t,e);this._seriesIndices=s(n)},restoreData:function(){var t=this._componentsMap;this._seriesIndices=s(t.get("series"));var e=[];t.each(function(t,n){e.push(n)}),x.topologicalTravel(e,x.getAllClassMainTypes(),function(e,n){d(t.get(e),function(t){t.restoreData()})})}});u.mixin(w,n(65)),t.exports=w},function(t,e,n){function i(t){this._api=t,this._timelineOptions=[],this._mediaList=[],this._mediaDefault,this._currentMediaIndices=[],this._optionBackup,this._newBaseOption}function r(t,e,n){var i,r,a=[],o=[],s=t.timeline;if(t.baseOption&&(r=t.baseOption),(s||t.options)&&(r=r||{},a=(t.options||[]).slice()),t.media){r=r||{};var l=t.media;f(l,function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))})}return r||(r=t),r.timeline||(r.timeline=s),f([r].concat(a).concat(h.map(o,function(t){return t.option})),function(t){f(e,function(e){e(t,n)})}),{baseOption:r,timelineOptions:a,mediaDefault:i,mediaList:o}}function a(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return h.each(t,function(t,e){var n=e.match(v);if(n&&n[1]&&n[2]){var a=n[1],s=n[2].toLowerCase();o(i[s],t,a)||(r=!1)}}),r}function o(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e}function s(t,e){return t.join(",")===e.join(",")}function l(t,e){e=e||{},f(e,function(e,n){if(null!=e){var i=t[n];if(c.hasClass(n)){e=u.normalizeToArray(e),i=u.normalizeToArray(i);var r=u.mappingToExists(i,e);t[n]=p(r,function(t){return t.option&&t.exist?g(t.exist,t.option,!0):t.exist||t.option})}else t[n]=g(i,e,!0)}})}var h=n(1),u=n(5),c=n(13),f=h.each,d=h.clone,p=h.map,g=h.merge,v=/^(min|max)?(.+)$/;i.prototype={constructor:i,setOption:function(t,e){t=d(t,!0);var n=this._optionBackup,i=r.call(this,t,e,!n);this._newBaseOption=i.baseOption,n?(l(n.baseOption,i.baseOption),i.timelineOptions.length&&(n.timelineOptions=i.timelineOptions),i.mediaList.length&&(n.mediaList=i.mediaList),i.mediaDefault&&(n.mediaDefault=i.mediaDefault)):this._optionBackup=i},mountOption:function(t){var e=this._optionBackup;return this._timelineOptions=p(e.timelineOptions,d),this._mediaList=p(e.mediaList,d),this._mediaDefault=d(e.mediaDefault),this._currentMediaIndices=[],d(t?e.baseOption:this._newBaseOption)},getTimelineOption:function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=d(n[i.getCurrentIndex()],!0))}return e},getMediaOption:function(t){var e=this._api.getWidth(),n=this._api.getHeight(),i=this._mediaList,r=this._mediaDefault,o=[],l=[];if(!i.length&&!r)return l;for(var h=0,u=i.length;h<u;h++)a(i[h].query,e,n)&&o.push(h);return!o.length&&r&&(o=[-1]),o.length&&!s(o,this._currentMediaIndices)&&(l=p(o,function(t){return d(t===-1?r.option:i[t].option)})),this._currentMediaIndices=o,l}},t.exports=i},function(t,e){var n="";"undefined"!=typeof navigator&&(n=navigator.platform||""),t.exports={color:["#c23531","#2f4554","#61a0a8","#d48265","#91c7ae","#749f83","#ca8622","#bda29a","#6e7074","#546570","#c4ccd3"],textStyle:{fontFamily:n.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,animation:"auto",animationDuration:1e3,animationDurationUpdate:300,animationEasing:"exponentialOut",animationEasingUpdate:"cubicOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1}},function(t,e,n){t.exports={getAreaStyle:n(31)([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]])}},function(t,e){t.exports={getBoxLayoutParams:function(){return{left:this.get("left"),top:this.get("top"),right:this.get("right"),bottom:this.get("bottom"),width:this.get("width"),height:this.get("height")}}}},function(t,e,n){var i=n(31)([["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["textPosition"],["textAlign"]]);t.exports={getItemStyle:function(t,e){var n=i.call(this,t,e),r=this.getBorderLineDash();return r&&(n.lineDash=r),n},getBorderLineDash:function(){var t=this.get("borderType");return"solid"===t||null==t?null:"dashed"===t?[5,5]:[1,1]}}},function(t,e,n){var i=n(31)([["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]);t.exports={getLineStyle:function(t){var e=i.call(this,t),n=this.getLineDash(e.lineWidth);return n&&(e.lineDash=n),e},getLineDash:function(t){null==t&&(t=1);var e=this.get("type"),n=Math.max(t,2),i=4*t;return"solid"===e||null==e?null:"dashed"===e?[i,i]:[n,n]}}},function(t,e,n){var i=n(16),r=n(3),a=["textStyle","color"];t.exports={getTextColor:function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(a):null)},getFont:function(){return r.getFont({fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},this.ecModel)},getTextRect:function(t){return i.getBoundingRect(t,this.getFont(),this.getShallow("align"),this.getShallow("verticalAlign")||this.getShallow("baseline"),this.getShallow("padding"),this.getShallow("rich"),this.getShallow("truncateText"))}}},function(t,e,n){function i(t,e){e=e.split(",");for(var n=t,i=0;i<e.length&&(n=n&&n[e[i]],null!=n);i++);return n}function r(t,e,n,i){e=e.split(",");for(var r,a=t,o=0;o<e.length-1;o++)r=e[o],null==a[r]&&(a[r]={}),a=a[r];(i||null==a[e[o]])&&(a[e[o]]=n)}function a(t){c(l,function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])})}var o=n(1),s=n(153),l=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],h=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],u=["bar","boxplot","candlestick","chord","effectScatter","funnel","gauge","lines","graph","heatmap","line","map","parallel","pie","radar","sankey","scatter","treemap"],c=o.each;t.exports=function(t,e){s(t,e);var n=t.series;c(o.isArray(n)?n:[n],function(t){if(o.isObject(t)){var e=t.type;if("pie"!==e&&"gauge"!==e||null!=t.clockWise&&(t.clockwise=t.clockWise),"gauge"===e){var n=i(t,"pointer.color");null!=n&&r(t,"itemStyle.normal.color",n)}for(var s=0;s<u.length;s++)if(u[s]===t.type){a(t);break}}}),t.dataRange&&(t.visualMap=t.dataRange),c(h,function(e){var n=t[e];n&&(o.isArray(n)||(n=[n]),c(n,function(t){a(t)}))})}},function(t,e,n){function i(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=d.length;n<i;n++){var r=d[n],a=e.normal,o=e.emphasis;a&&a[r]&&(t[r]=t[r]||{},t[r].normal?h.merge(t[r].normal,a[r]):t[r].normal=a[r],a[r]=null),o&&o[r]&&(t[r]=t[r]||{},t[r].emphasis?h.merge(t[r].emphasis,o[r]):t[r].emphasis=o[r],o[r]=null)}}function r(t,e){var n=f(t)&&t[e],i=f(n)&&n.textStyle;if(i)for(var r=0,a=u.TEXT_STYLE_OPTIONS.length;r<a;r++){var e=u.TEXT_STYLE_OPTIONS[r];i.hasOwnProperty(e)&&(n[e]=i[e])}}function a(t){f(t)&&(r(t,"normal"),r(t,"emphasis"))}function o(t){if(f(t)){i(t),a(t.label),a(t.upperLabel),a(t.edgeLabel);var e=t.markPoint;i(e),a(e&&e.label);var n=t.markLine;i(t.markLine),a(n&&n.label);var o=t.markArea;a(o&&o.label),r(t,"axisLabel"),r(t,"title"),r(t,"detail");var s=t.data;if(s)for(var l=0;l<s.length;l++)i(s[l]),a(s[l]&&s[l].label);var e=t.markPoint;if(e&&e.data)for(var u=e.data,l=0;l<u.length;l++)i(u[l]),a(u[l]&&u[l].label);var n=t.markLine;if(n&&n.data)for(var c=n.data,l=0;l<c.length;l++)h.isArray(c[l])?(i(c[l][0]),a(c[l][0]&&c[l][0].label),i(c[l][1]),a(c[l][1]&&c[l][1].label)):(i(c[l]),a(c[l]&&c[l].label))}}function s(t){return h.isArray(t)?t:t?[t]:[]}function l(t){return(h.isArray(t)?t[0]:t)||{}}var h=n(1),u=n(5),c=h.each,f=h.isObject,d=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];t.exports=function(t,e){c(s(t.series),function(t){f(t)&&o(t)});var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),c(n,function(e){c(s(t[e]),function(t){t&&(r(t,"axisLabel"),r(t.axisPointer,"label"))})}),c(s(t.parallel),function(t){var e=t&&t.parallelAxisDefault;r(e,"axisLabel"),r(e&&e.axisPointer,"label")}),c(s(t.calendar),function(t){r(t,"dayLabel"),r(t,"monthLabel"),r(t,"yearLabel")}),c(s(t.radar),function(t){r(t,"name")}),c(s(t.geo),function(t){f(t)&&(a(t.label),c(s(t.regions),function(t){a(t.label)}))}),a(l(t.timeline).label),r(l(t.axisPointer),"label"),r(l(t.tooltip).axisPointer,"label")}},function(t,e){var n={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-(1/0),n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return e},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return e},nearest:function(t){return t[0]}},i=function(t,e){return Math.round(t.length/2)};t.exports=function(t,e,r){e.eachSeriesByType(t,function(t){var e=t.getData(),r=t.get("sampling"),a=t.coordinateSystem;if("cartesian2d"===a.type&&r){var o=a.getBaseAxis(),s=a.getOtherAxis(o),l=o.getExtent(),h=l[1]-l[0],u=Math.round(e.count()/h);if(u>1){var c;"string"==typeof r?c=n[r]:"function"==typeof r&&(c=r),c&&(e=e.downSample(s.dim,1/u,c,i),t.setData(e))}}},this)}},function(t,e,n){function i(t,e){return c(t,u(e))}var r=n(1),a=n(34),o=n(4),s=n(45),l=a.prototype,h=s.prototype,u=o.getPrecisionSafe,c=o.round,f=Math.floor,d=Math.ceil,p=Math.pow,g=Math.log,v=a.extend({type:"log",base:10,$constructor:function(){a.apply(this,arguments),this._originalScale=new s},getTicks:function(){var t=this._originalScale,e=this._extent,n=t.getExtent();return r.map(h.getTicks.call(this),function(r){var a=o.round(p(this.base,r));return a=r===e[0]&&t.__fixMin?i(a,n[0]):a,a=r===e[1]&&t.__fixMax?i(a,n[1]):a},this)},getLabel:h.getLabel,scale:function(t){return t=l.scale.call(this,t),p(this.base,t)},setExtent:function(t,e){var n=this.base;t=g(t)/g(n),e=g(e)/g(n),h.setExtent.call(this,t,e)},getExtent:function(){var t=this.base,e=l.getExtent.call(this);e[0]=p(t,e[0]),e[1]=p(t,e[1]);var n=this._originalScale,r=n.getExtent();return n.__fixMin&&(e[0]=i(e[0],r[0])),n.__fixMax&&(e[1]=i(e[1],r[1])),e},unionExtent:function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=g(t[0])/g(e),t[1]=g(t[1])/g(e),l.unionExtent.call(this,t)},unionExtentFromData:function(t,e){this.unionExtent(t.getDataExtent(e,!0,function(t){return t>0}))},niceTicks:function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=o.quantity(n),r=t/n*i;for(r<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var a=[o.round(d(e[0]/i)*i),o.round(f(e[1]/i)*i)];this._interval=i,this._niceExtent=a}},niceExtent:function(t){h.niceExtent.call(this,t);var e=this._originalScale;e.__fixMin=t.fixMin,e.__fixMax=t.fixMax}});r.each(["contain","normalize"],function(t){v.prototype[t]=function(e){return e=g(e)/g(this.base),l[t].call(this,e)}}),v.create=function(){return new v},t.exports=v},function(t,e,n){var i=n(1),r=n(34),a=r.prototype,o=r.extend({type:"ordinal",init:function(t,e){this._data=t,this._extent=e||[0,t.length-1]},parse:function(t){return"string"==typeof t?i.indexOf(this._data,t):Math.round(t)},contain:function(t){return t=this.parse(t),a.contain.call(this,t)&&null!=this._data[t]},normalize:function(t){return a.normalize.call(this,this.parse(t))},scale:function(t){return Math.round(a.scale.call(this,t))},getTicks:function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push(n),n++;return t},getLabel:function(t){return this._data[t]},count:function(){return this._extent[1]-this._extent[0]+1},unionExtentFromData:function(t,e){this.unionExtent(t.getDataExtent(e,!1))},niceTicks:i.noop,niceExtent:i.noop});o.create=function(){return new o},t.exports=o},function(t,e,n){var i=n(1),r=n(4),a=n(7),o=n(67),s=n(45),l=s.prototype,h=Math.ceil,u=Math.floor,c=1e3,f=60*c,d=60*f,p=24*d,g=function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][2]<e?n=r+1:i=r}return n},v=s.extend({type:"time",getLabel:function(t){var e=this._stepLvl,n=new Date(t);return a.formatTime(e[0],n,this.getSetting("useUTC"))},niceExtent:function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=p,e[1]+=p),e[1]===-(1/0)&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-p}this.niceTicks(t.splitNumber,t.minInterval,t.maxInterval);var i=this._interval;t.fixMin||(e[0]=r.round(u(e[0]/i)*i)),t.fixMax||(e[1]=r.round(h(e[1]/i)*i))},niceTicks:function(t,e,n){t=t||10;var i=this._extent,a=i[1]-i[0],s=a/t;null!=e&&s<e&&(s=e),null!=n&&s>n&&(s=n);var l=m.length,c=g(m,s,0,l),f=m[Math.min(c,l-1)],d=f[2];if("year"===f[0]){var p=a/d,v=r.nice(p/t,!0);d*=v}var y=this.getSetting("useUTC")?0:60*new Date(+i[0]||+i[1]).getTimezoneOffset()*1e3,x=[Math.round(h((i[0]-y)/d)*d+y),Math.round(u((i[1]-y)/d)*d+y)];o.fixExtent(x,i),this._stepLvl=f,this._interval=d,this._niceExtent=x},parse:function(t){return+r.parseDate(t)}});i.each(["contain","normalize"],function(t){v.prototype[t]=function(e){return l[t].call(this,this.parse(e))}});var m=[["hh:mm:ss",1,c],["hh:mm:ss",5,5*c],["hh:mm:ss",10,10*c],["hh:mm:ss",15,15*c],["hh:mm:ss",30,30*c],["hh:mm\nMM-dd",1,f],["hh:mm\nMM-dd",5,5*f],["hh:mm\nMM-dd",10,10*f],["hh:mm\nMM-dd",15,15*f],["hh:mm\nMM-dd",30,30*f],["hh:mm\nMM-dd",1,d],["hh:mm\nMM-dd",2,2*d],["hh:mm\nMM-dd",6,6*d],["hh:mm\nMM-dd",12,12*d],["MM-dd\nyyyy",1,p],["week",7,7*p],["month",1,31*p],["quarter",3,380*p/4],["half-year",6,380*p/2],["year",1,380*p]];v.create=function(t){return new v({useUTC:t.ecModel.get("useUTC")})},t.exports=v},function(t,e,n){var i=n(39);t.exports=function(t){function e(e){var n=(e.visualColorAccessPath||"itemStyle.normal.color").split("."),r=e.getData(),a=e.get(n)||e.getColorFromPalette(e.get("name"));r.setVisual("color",a),t.isSeriesFiltered(e)||("function"!=typeof a||a instanceof i||r.each(function(t){r.setItemVisual(t,"color",a(e.getDataParams(t)))}),r.each(function(t){var e=r.getItemModel(t),i=e.get(n,!0);null!=i&&r.setItemVisual(t,"color",i)}))}t.eachRawSeries(e)}},function(t,e,n){"use strict";function i(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which}}function r(){}function a(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i,r=t;r;){if(r.clipPath&&!r.clipPath.contain(e,n))return!1;r.silent&&(i=!0),r=r.parent}return!i||u}return!1}var o=n(1),s=n(6),l=n(185),h=n(23),u="silent";r.prototype.dispose=function(){};var c=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],f=function(t,e,n,i){h.call(this),this.storage=t,this.painter=e,this.painterRoot=i,n=n||new r,this.proxy=n,n.handler=this,this._hovered={},this._lastTouchMoment,this._lastX,this._lastY,l.call(this),o.each(c,function(t){n.on&&n.on(t,this[t],this)},this)};f.prototype={constructor:f,mousemove:function(t){var e=t.zrX,n=t.zrY,i=this._hovered,r=i.target;r&&!r.__zr&&(i=this.findHover(i.x,i.y),r=i.target);var a=this._hovered=this.findHover(e,n),o=a.target,s=this.proxy;s.setCursor&&s.setCursor(o?o.cursor:"default"),r&&o!==r&&this.dispatchToElement(i,"mouseout",t),this.dispatchToElement(a,"mousemove",t),o&&o!==r&&this.dispatchToElement(a,"mouseover",t)},mouseout:function(t){this.dispatchToElement(this._hovered,"mouseout",t);var e,n=t.toElement||t.relatedTarget;do n=n&&n.parentNode;while(n&&9!=n.nodeType&&!(e=n===this.painterRoot));!e&&this.trigger("globalout",{event:t})},resize:function(t){this._hovered={}},dispatch:function(t,e){var n=this[t];n&&n.call(this,e)},dispose:function(){this.proxy.dispose(),this.storage=this.proxy=this.painter=null},setCursorStyle:function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},dispatchToElement:function(t,e,n){t=t||{};var r=t.target;if(!r||!r.silent){for(var a="on"+e,o=i(e,t,n);r&&(r[a]&&(o.cancelBubble=r[a].call(r,o)),r.trigger(e,o),r=r.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer(function(t){"function"==typeof t[a]&&t[a].call(t,o),t.trigger&&t.trigger(e,o)}))}},findHover:function(t,e,n){for(var i=this.storage.getDisplayList(),r={x:t,y:e},o=i.length-1;o>=0;o--){var s;if(i[o]!==n&&!i[o].ignore&&(s=a(i[o],t,e))&&(!r.topTarget&&(r.topTarget=i[o]),s!==u)){r.target=i[o];break}}return r}},o.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){f.prototype[t]=function(e){var n=this.findHover(e.zrX,e.zrY),i=n.target;if("mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mosueup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||s.dist(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}),o.mixin(f,h),o.mixin(f,l),t.exports=f},function(t,e,n){function i(){return!1}function r(t,e,n,i){var r=document.createElement(e),a=n.getWidth(),o=n.getHeight(),s=r.style;return s.position="absolute",s.left=0,s.top=0,s.width=a+"px",s.height=o+"px",r.width=a*i,r.height=o*i,r.setAttribute("data-zr-dom-id",t),r}var a=n(1),o=n(35),s=n(76),l=n(75),h=function(t,e,n){var s;n=n||o.devicePixelRatio,"string"==typeof t?s=r(t,"canvas",e,n):a.isObject(t)&&(s=t,t=s.id),this.id=t,this.dom=s;var l=s.style;l&&(s.onselectstart=i,l["-webkit-user-select"]="none",l["user-select"]="none",l["-webkit-touch-callout"]="none",l["-webkit-tap-highlight-color"]="rgba(0,0,0,0)",l.padding=0,l.margin=0,l["border-width"]=0),this.domBack=null,this.ctxBack=null,this.painter=e,this.config=null,this.clearColor=0,this.motionBlur=!1,this.lastFrameAlpha=.7,this.dpr=n};h.prototype={constructor:h,elCount:0,__dirty:!0,initContext:function(){this.ctx=this.dom.getContext("2d"),this.ctx.__currentValues={},this.ctx.dpr=this.dpr},createBackBuffer:function(){var t=this.dpr;this.domBack=r("back-"+this.id,"canvas",this.painter,t),this.ctxBack=this.domBack.getContext("2d"),this.ctxBack.__currentValues={},1!=t&&this.ctxBack.scale(t,t)},resize:function(t,e){var n=this.dpr,i=this.dom,r=i.style,a=this.domBack;r.width=t+"px",r.height=e+"px",i.width=t*n,i.height=e*n,a&&(a.width=t*n,a.height=e*n,1!=n&&this.ctxBack.scale(n,n))},clear:function(t){var e=this.dom,n=this.ctx,i=e.width,r=e.height,a=this.clearColor,o=this.motionBlur&&!t,h=this.lastFrameAlpha,u=this.dpr;if(o&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(e,0,0,i/u,r/u)),n.clearRect(0,0,i,r),a){var c;a.colorStops?(c=a.__canvasGradient||s.getGradient(n,a,{x:0,y:0,width:i,height:r}),a.__canvasGradient=c):a.image&&(c=l.prototype.getCanvasPattern.call(a,n)),n.save(),n.fillStyle=c||a,n.fillRect(0,0,i,r),n.restore()}if(o){var f=this.domBack;n.save(),n.globalAlpha=h,n.drawImage(f,0,0,i,r),n.restore()}}},t.exports=h},function(t,e,n){"use strict";function i(t){return parseInt(t,10)}function r(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}function a(t){t.__unusedCount++}function o(t){1==t.__unusedCount&&t.clear()}function s(t,e,n){return x.copy(t.getBoundingRect()),t.transform&&x.applyTransform(t.transform),_.width=e,_.height=n,!x.intersect(_)}function l(t,e){if(t==e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0}function h(t,e){for(var n=0;n<t.length;n++){var i=t[n];i.setTransform(e),e.beginPath(),i.buildPath(e,i.shape),e.clip(),i.restoreTransform(e)}}function u(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","overflow:hidden","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}var c=n(35),f=n(1),d=n(54),p=n(12),g=n(52),v=n(160),m=n(71),y=5,x=new p(0,0,0,0),_=new p(0,0,0,0),b=function(t,e,n){this.type="canvas";var i=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=f.extend({},n||{}),this.dpr=n.devicePixelRatio||c.devicePixelRatio,this._singleCanvas=i,this.root=t;var r=t.style;r&&(r["-webkit-tap-highlight-color"]="transparent",r["-webkit-user-select"]=r["user-select"]=r["-webkit-touch-callout"]="none",t.innerHTML=""),this.storage=e;var a=this._zlevelList=[],o=this._layers={};if(this._layerConfig={},i){null!=n.width&&(t.width=n.width),null!=n.height&&(t.height=n.height);var s=t.width,l=t.height;this._width=s,this._height=l;var h=new v(t,this,1);h.initContext(),o[0]=h,a.push(0),this._domRoot=t}else{this._width=this._getSize(0),this._height=this._getSize(1);var d=this._domRoot=u(this._width,this._height);t.appendChild(d)}this._progressiveLayers=[],this._hoverlayer,this._hoverElements=[]};b.prototype={constructor:b,getType:function(){return"canvas"},isSingleCanvas:function(){return this._singleCanvas},getViewportRoot:function(){return this._domRoot},getViewportRootOffset:function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},refresh:function(t){var e=this.storage.getDisplayList(!0),n=this._zlevelList;this._paintList(e,t);for(var i=0;i<n.length;i++){var r=n[i],a=this._layers[r];!a.__builtin__&&a.refresh&&a.refresh()}return this.refreshHover(),this._progressiveLayers.length&&this._startProgessive(),this},addHover:function(t,e){if(!t.__hoverMir){var n=new t.constructor({style:t.style,shape:t.shape});n.__from=t,t.__hoverMir=n,n.setStyle(e),this._hoverElements.push(n)}},removeHover:function(t){var e=t.__hoverMir,n=this._hoverElements,i=f.indexOf(n,e);i>=0&&n.splice(i,1),t.__hoverMir=null},clearHover:function(t){for(var e=this._hoverElements,n=0;n<e.length;n++){var i=e[n].__from;i&&(i.__hoverMir=null)}e.length=0},refreshHover:function(){var t=this._hoverElements,e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){g(t,this.storage.displayableSortFunc),n||(n=this._hoverlayer=this.getLayer(1e5));var i={};n.ctx.save();for(var r=0;r<e;){var a=t[r],o=a.__from;o&&o.__zr?(r++,o.invisible||(a.transform=o.transform,a.invTransform=o.invTransform,a.__clipPaths=o.__clipPaths,this._doPaintEl(a,n,!0,i))):(t.splice(r,1),o.__hoverMir=null,e--)}n.ctx.restore()}},_startProgessive:function(){function t(){n===e._progressiveToken&&e.storage&&(e._doPaintList(e.storage.getDisplayList()),e._furtherProgressive?(e._progress++,m(t)):e._progressiveToken=-1)}var e=this;if(e._furtherProgressive){var n=e._progressiveToken=+new Date;e._progress++,
m(t)}},_clearProgressive:function(){this._progressiveToken=-1,this._progress=0,f.each(this._progressiveLayers,function(t){t.__dirty&&t.clear()})},_paintList:function(t,e){null==e&&(e=!1),this._updateLayerStatus(t),this._clearProgressive(),this.eachBuiltinLayer(a),this._doPaintList(t,e),this.eachBuiltinLayer(o)},_doPaintList:function(t,e){function n(t){var e=a.dpr||1;a.save(),a.globalAlpha=1,a.shadowBlur=0,i.__dirty=!0,a.setTransform(1,0,0,1,0,0),a.drawImage(t.dom,0,0,u*e,c*e),a.restore()}for(var i,r,a,o,s,l,h=0,u=this._width,c=this._height,p=this._progress,g=0,v=t.length;g<v;g++){var m=t[g],x=this._singleCanvas?0:m.zlevel,_=m.__frame;if(_<0&&s&&(n(s),s=null),r!==x&&(a&&a.restore(),o={},r=x,i=this.getLayer(r),i.__builtin__||d("ZLevel "+r+" has been used by unkown layer "+i.id),a=i.ctx,a.save(),i.__unusedCount=0,(i.__dirty||e)&&i.clear()),i.__dirty||e){if(_>=0){if(!s){if(s=this._progressiveLayers[Math.min(h++,y-1)],s.ctx.save(),s.renderScope={},s&&s.__progress>s.__maxProgress){g=s.__nextIdxNotProg-1;continue}l=s.__progress,s.__dirty||(p=l),s.__progress=p+1}_===p&&this._doPaintEl(m,s,!0,s.renderScope)}else this._doPaintEl(m,i,e,o);m.__dirty=!1}}s&&n(s),a&&a.restore(),this._furtherProgressive=!1,f.each(this._progressiveLayers,function(t){t.__maxProgress>=t.__progress&&(this._furtherProgressive=!0)},this)},_doPaintEl:function(t,e,n,i){var r=e.ctx,a=t.transform;if((e.__dirty||n)&&!t.invisible&&0!==t.style.opacity&&(!a||a[0]||a[3])&&(!t.culling||!s(t,this._width,this._height))){var o=t.__clipPaths;(i.prevClipLayer!==e||l(o,i.prevElClipPaths))&&(i.prevElClipPaths&&(i.prevClipLayer.ctx.restore(),i.prevClipLayer=i.prevElClipPaths=null,i.prevEl=null),o&&(r.save(),h(o,r),i.prevClipLayer=e,i.prevElClipPaths=o)),t.beforeBrush&&t.beforeBrush(r),t.brush(r,i.prevEl||null),i.prevEl=t,t.afterBrush&&t.afterBrush(r)}},getLayer:function(t){if(this._singleCanvas)return this._layers[0];var e=this._layers[t];return e||(e=new v("zr_"+t,this,this.dpr),e.__builtin__=!0,this._layerConfig[t]&&f.merge(e,this._layerConfig[t],!0),this.insertLayer(t,e),e.initContext()),e},insertLayer:function(t,e){var n=this._layers,i=this._zlevelList,a=i.length,o=null,s=-1,l=this._domRoot;if(n[t])return void d("ZLevel "+t+" has been used already");if(!r(e))return void d("Layer of zlevel "+t+" is not valid");if(a>0&&t>i[0]){for(s=0;s<a-1&&!(i[s]<t&&i[s+1]>t);s++);o=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(o){var h=o.dom;h.nextSibling?l.insertBefore(e.dom,h.nextSibling):l.appendChild(e.dom)}else l.firstChild?l.insertBefore(e.dom,l.firstChild):l.appendChild(e.dom)},eachLayer:function(t,e){var n,i,r=this._zlevelList;for(i=0;i<r.length;i++)n=r[i],t.call(e,this._layers[n],n)},eachBuiltinLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__&&t.call(e,n,i)},eachOtherLayer:function(t,e){var n,i,r,a=this._zlevelList;for(r=0;r<a.length;r++)i=a[r],n=this._layers[i],n.__builtin__||t.call(e,n,i)},getLayers:function(){return this._layers},_updateLayerStatus:function(t){var e=this._layers,n=this._progressiveLayers,i={},r={};this.eachBuiltinLayer(function(t,e){i[e]=t.elCount,t.elCount=0,t.__dirty=!1}),f.each(n,function(t,e){r[e]=t.elCount,t.elCount=0,t.__dirty=!1});for(var a,o,s=0,l=0,h=0,u=t.length;h<u;h++){var c=t[h],d=this._singleCanvas?0:c.zlevel,p=e[d],g=c.progressive;if(p&&(p.elCount++,p.__dirty=p.__dirty||c.__dirty),g>=0){o!==g&&(o=g,l++);var m=c.__frame=l-1;if(!a){var x=Math.min(s,y-1);a=n[x],a||(a=n[x]=new v("progressive",this,this.dpr),a.initContext()),a.__maxProgress=0}a.__dirty=a.__dirty||c.__dirty,a.elCount++,a.__maxProgress=Math.max(a.__maxProgress,m),a.__maxProgress>=a.__progress&&(p.__dirty=!0)}else c.__frame=-1,a&&(a.__nextIdxNotProg=h,s++,a=null)}a&&(s++,a.__nextIdxNotProg=h),this.eachBuiltinLayer(function(t,e){i[e]!==t.elCount&&(t.__dirty=!0)}),n.length=Math.min(s,y),f.each(n,function(t,e){r[e]!==t.elCount&&(c.__dirty=!0),t.__dirty&&(t.__progress=0)})},clear:function(){return this.eachBuiltinLayer(this._clearLayer),this},_clearLayer:function(t){t.clear()},configLayer:function(t,e){if(e){var n=this._layerConfig;n[t]?f.merge(n[t],e,!0):n[t]=e;var i=this._layers[t];i&&f.merge(i,n[t],!0)}},delLayer:function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(f.indexOf(n,t),1))},resize:function(t,e){var n=this._domRoot;n.style.display="none";var i=this._opts;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=this._getSize(0),e=this._getSize(1),n.style.display="",this._width!=t||e!=this._height){n.style.width=t+"px",n.style.height=e+"px";for(var r in this._layers)this._layers.hasOwnProperty(r)&&this._layers[r].resize(t,e);f.each(this._progressiveLayers,function(n){n.resize(t,e)}),this.refresh(!0)}return this._width=t,this._height=e,this},clearLayer:function(t){var e=this._layers[t];e&&e.clear()},dispose:function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},getRenderedCanvas:function(t){function e(t,e){var i=o._zlevelList;null==t&&(t=-(1/0));for(var r,a=0;a<i.length;a++){var s=i[a],l=o._layers[s];if(!l.__builtin__&&s>t&&s<e){r=l;break}}r&&r.renderToCanvas&&(n.ctx.save(),r.renderToCanvas(n.ctx),n.ctx.restore())}if(t=t||{},this._singleCanvas)return this._layers[0].dom;var n=new v("image",this,t.pixelRatio||this.dpr);n.initContext(),n.clearColor=t.backgroundColor,n.clear();for(var i,r=this.storage.getDisplayList(!0),a={},o=this,s=0;s<r.length;s++){var l=r[s];l.zlevel!==i&&(e(i,l.zlevel),i=l.zlevel),this._doPaintEl(l,n,!0,a)}return e(i,1/0),n.dom},getWidth:function(){return this._width},getHeight:function(){return this._height},_getSize:function(t){var e=this._opts,n=["width","height"][t],r=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],o=["paddingRight","paddingBottom"][t];if(null!=e[n]&&"auto"!==e[n])return parseFloat(e[n]);var s=this.root,l=document.defaultView.getComputedStyle(s);return(s[r]||i(l[n])||i(s.style[n]))-(i(l[a])||0)-(i(l[o])||0)|0},pathToImage:function(t,e){e=e||this.dpr;var i=document.createElement("canvas"),r=i.getContext("2d"),a=t.getBoundingRect(),o=t.style,s=o.shadowBlur,l=o.shadowOffsetX,h=o.shadowOffsetY,u=o.hasStroke()?o.lineWidth:0,c=Math.max(u/2,-l+s),f=Math.max(u/2,l+s),d=Math.max(u/2,-h+s),p=Math.max(u/2,h+s),g=a.width+c+f,v=a.height+d+p;i.width=g*e,i.height=v*e,r.scale(e,e),r.clearRect(0,0,g,v),r.dpr=e;var m={position:t.position,rotation:t.rotation,scale:t.scale};t.position=[c-a.x,d-a.y],t.rotation=0,t.scale=[1,1],t.updateTransform(),t&&t.brush(r);var y=n(55),x=new y({style:{x:0,y:0,image:i}});return null!=m.position&&(x.position=t.position=m.position),null!=m.rotation&&(x.rotation=t.rotation=m.rotation),null!=m.scale&&(x.scale=t.scale=m.scale),x}},t.exports=b},function(t,e,n){"use strict";function i(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var r=n(1),a=n(10),o=n(36),s=n(52),l=function(){this._roots=[],this._displayList=[],this._displayListLen=0};l.prototype={constructor:l,traverse:function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},getDisplayList:function(t,e){return e=e||!1,t&&this.updateDisplayList(e),this._displayList},updateDisplayList:function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,r=0,o=e.length;r<o;r++)this._updateAndAddDisplayable(e[r],null,t);n.length=this._displayListLen,a.canvasSupported&&s(n,i)},_updateAndAddDisplayable:function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.__dirty&&t.update(),t.afterUpdate();var i=t.clipPath;if(i){e=e?e.slice():[];for(var r=i,a=t;r;)r.parent=a,r.updateTransform(),e.push(r),a=r,r=r.clipPath}if(t.isGroup){for(var o=t._children,s=0;s<o.length;s++){var l=o[s];t.__dirty&&(l.__dirty=!0),this._updateAndAddDisplayable(l,e,n)}t.__dirty=!1}else t.__clipPaths=e,this._displayList[this._displayListLen++]=t}},addRoot:function(t){t.__storage!==this&&(t instanceof o&&t.addChildrenToStorage(this),this.addToStorage(t),this._roots.push(t))},delRoot:function(t){if(null==t){for(var e=0;e<this._roots.length;e++){var n=this._roots[e];n instanceof o&&n.delChildrenFromStorage(this)}return this._roots=[],this._displayList=[],void(this._displayListLen=0)}if(t instanceof Array)for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);else{var a=r.indexOf(this._roots,t);a>=0&&(this.delFromStorage(t),this._roots.splice(a,1),t instanceof o&&t.delChildrenFromStorage(this))}},addToStorage:function(t){return t.__storage=this,t.dirty(!1),this},delFromStorage:function(t){return t&&(t.__storage=null),this},dispose:function(){this._renderList=this._roots=null},displayableSortFunc:i},t.exports=l},function(t,e,n){"use strict";var i=n(1),r=n(21).Dispatcher,a=n(71),o=n(70),s=function(t){t=t||{},this.stage=t.stage||{},this.onframe=t.onframe||function(){},this._clips=[],this._running=!1,this._time,this._pausedTime,this._pauseStart,this._paused=!1,r.call(this)};s.prototype={constructor:s,addClip:function(t){this._clips.push(t)},addAnimator:function(t){t.animation=this;for(var e=t.getClips(),n=0;n<e.length;n++)this.addClip(e[n])},removeClip:function(t){var e=i.indexOf(this._clips,t);e>=0&&this._clips.splice(e,1)},removeAnimator:function(t){for(var e=t.getClips(),n=0;n<e.length;n++)this.removeClip(e[n]);t.animation=null},_update:function(){for(var t=(new Date).getTime()-this._pausedTime,e=t-this._time,n=this._clips,i=n.length,r=[],a=[],o=0;o<i;o++){var s=n[o],l=s.step(t,e);l&&(r.push(l),a.push(s))}for(var o=0;o<i;)n[o]._needsRemove?(n[o]=n[i-1],n.pop(),i--):o++;i=r.length;for(var o=0;o<i;o++)a[o].fire(r[o]);this._time=t,this.onframe(e),this.trigger("frame",e),this.stage.update&&this.stage.update()},_startLoop:function(){function t(){e._running&&(a(t),!e._paused&&e._update())}var e=this;this._running=!0,a(t)},start:function(){this._time=(new Date).getTime(),this._pausedTime=0,this._startLoop()},stop:function(){this._running=!1},pause:function(){this._paused||(this._pauseStart=(new Date).getTime(),this._paused=!0)},resume:function(){this._paused&&(this._pausedTime+=(new Date).getTime()-this._pauseStart,this._paused=!1)},clear:function(){this._clips=[]},animate:function(t,e){e=e||{};var n=new o(t,e.loop,e.getter,e.setter);return this.addAnimator(n),n}},i.mixin(s,r),t.exports=s},function(t,e,n){function i(t){this._target=t.target,this._life=t.life||1e3,this._delay=t.delay||0,this._initialized=!1,this.loop=null!=t.loop&&t.loop,this.gap=t.gap||0,this.easing=t.easing||"Linear",this.onframe=t.onframe,this.ondestroy=t.ondestroy,this.onrestart=t.onrestart,this._pausedTime=0,this._paused=!1}var r=n(165);i.prototype={constructor:i,step:function(t,e){if(this._initialized||(this._startTime=t+this._delay,this._initialized=!0),this._paused)return void(this._pausedTime+=e);var n=(t-this._startTime-this._pausedTime)/this._life;if(!(n<0)){n=Math.min(n,1);var i=this.easing,a="string"==typeof i?r[i]:i,o="function"==typeof a?a(n):n;return this.fire("frame",o),1==n?this.loop?(this.restart(t),"restart"):(this._needsRemove=!0,"destroy"):null}},restart:function(t){var e=(t-this._startTime-this._pausedTime)%this._life;this._startTime=t-e+this.gap,this._pausedTime=0,this._needsRemove=!1},fire:function(t,e){t="on"+t,this[t]&&this[t](this._target,e)},pause:function(){this._paused=!0},resume:function(){this._paused=!1}},t.exports=i},function(t,e){var n={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(-Math.pow(2,-10*(t-1))+2)},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),-(n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)))},elasticOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/i)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=i/4):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?-.5*(n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)):n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?.5*(t*t*((e+1)*t-e)):.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-n.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*n.bounceIn(2*t):.5*n.bounceOut(2*t-1)+.5}};t.exports=n},function(t,e,n){var i=n(72).normalizeRadian,r=2*Math.PI;t.exports={containStroke:function(t,e,n,a,o,s,l,h,u){if(0===l)return!1;var c=l;h-=t,u-=e;var f=Math.sqrt(h*h+u*u);if(f-c>n||f+c<n)return!1;if(Math.abs(a-o)%r<1e-4)return!0;if(s){var d=a;a=i(o),o=i(d)}else a=i(a),o=i(o);a>o&&(o+=r);var p=Math.atan2(u,h);return p<0&&(p+=r),p>=a&&p<=o||p+r>=a&&p+r<=o}}},function(t,e,n){var i=n(20);t.exports={containStroke:function(t,e,n,r,a,o,s,l,h,u,c){if(0===h)return!1;var f=h;if(c>e+f&&c>r+f&&c>o+f&&c>l+f||c<e-f&&c<r-f&&c<o-f&&c<l-f||u>t+f&&u>n+f&&u>a+f&&u>s+f||u<t-f&&u<n-f&&u<a-f&&u<s-f)return!1;var d=i.cubicProjectPoint(t,e,n,r,a,o,s,l,u,c,null);return d<=f/2}}},function(t,e,n){"use strict";function i(t,e){return Math.abs(t-e)<x}function r(){var t=b[0];b[0]=b[1],b[1]=t}function a(t,e,n,i,a,o,s,l,h,u){if(u>e&&u>i&&u>o&&u>l||u<e&&u<i&&u<o&&u<l)return 0;var c=g.cubicRootAt(e,i,o,l,u,_);if(0===c)return 0;for(var f,d,p=0,v=-1,m=0;m<c;m++){var y=_[m],x=0===y||1===y?.5:1,w=g.cubicAt(t,n,a,s,y);w<h||(v<0&&(v=g.cubicExtrema(e,i,o,l,b),b[1]<b[0]&&v>1&&r(),f=g.cubicAt(e,i,o,l,b[0]),v>1&&(d=g.cubicAt(e,i,o,l,b[1]))),p+=2==v?y<b[0]?f<e?x:-x:y<b[1]?d<f?x:-x:l<d?x:-x:y<b[0]?f<e?x:-x:l<f?x:-x)}return p}function o(t,e,n,i,r,a,o,s){if(s>e&&s>i&&s>a||s<e&&s<i&&s<a)return 0;var l=g.quadraticRootAt(e,i,a,s,_);if(0===l)return 0;var h=g.quadraticExtremum(e,i,a);if(h>=0&&h<=1){for(var u=0,c=g.quadraticAt(e,i,a,h),f=0;f<l;f++){var d=0===_[f]||1===_[f]?.5:1,p=g.quadraticAt(t,n,r,_[f]);p<o||(u+=_[f]<h?c<e?d:-d:a<c?d:-d)}return u}var d=0===_[0]||1===_[0]?.5:1,p=g.quadraticAt(t,n,r,_[0]);return p<o?0:a<e?d:-d}function s(t,e,n,i,r,a,o,s){if(s-=e,s>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);_[0]=-l,_[1]=l;var h=Math.abs(i-r);if(h<1e-4)return 0;if(h%y<1e-4){i=0,r=y;var u=a?1:-1;return o>=_[0]+t&&o<=_[1]+t?u:0}if(a){var l=i;i=p(r),r=p(l)}else i=p(i),r=p(r);i>r&&(r+=y);for(var c=0,f=0;f<2;f++){var d=_[f];if(d+t>o){var g=Math.atan2(s,d),u=a?1:-1;g<0&&(g=y+g),(g>=i&&g<=r||g+y>=i&&g+y<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(u=-u),c+=u)}}return c}function l(t,e,n,r,l){for(var u=0,p=0,g=0,y=0,x=0,_=0;_<t.length;){var b=t[_++];switch(b===h.M&&_>1&&(n||(u+=v(p,g,y,x,r,l))),1==_&&(p=t[_],g=t[_+1],y=p,x=g),b){case h.M:y=t[_++],x=t[_++],p=y,g=x;break;case h.L:if(n){if(m(p,g,t[_],t[_+1],e,r,l))return!0}else u+=v(p,g,t[_],t[_+1],r,l)||0;p=t[_++],g=t[_++];break;case h.C:if(n){if(c.containStroke(p,g,t[_++],t[_++],t[_++],t[_++],t[_],t[_+1],e,r,l))return!0}else u+=a(p,g,t[_++],t[_++],t[_++],t[_++],t[_],t[_+1],r,l)||0;p=t[_++],g=t[_++];break;case h.Q:if(n){if(f.containStroke(p,g,t[_++],t[_++],t[_],t[_+1],e,r,l))return!0}else u+=o(p,g,t[_++],t[_++],t[_],t[_+1],r,l)||0;p=t[_++],g=t[_++];break;case h.A:var w=t[_++],S=t[_++],T=t[_++],M=t[_++],A=t[_++],I=t[_++],C=(t[_++],1-t[_++]),P=Math.cos(A)*T+w,L=Math.sin(A)*M+S;_>1?u+=v(p,g,P,L,r,l):(y=P,x=L);var k=(r-w)*M/T+w;if(n){if(d.containStroke(w,S,M,A,A+I,C,e,k,l))return!0}else u+=s(w,S,M,A,A+I,C,k,l);p=Math.cos(A+I)*T+w,g=Math.sin(A+I)*M+S;break;case h.R:y=p=t[_++],x=g=t[_++];var D=t[_++],O=t[_++],P=y+D,L=x+O;if(n){if(m(y,x,P,x,e,r,l)||m(P,x,P,L,e,r,l)||m(P,L,y,L,e,r,l)||m(y,L,y,x,e,r,l))return!0}else u+=v(P,x,P,L,r,l),u+=v(y,L,y,x,r,l);break;case h.Z:if(n){if(m(p,g,y,x,e,r,l))return!0}else u+=v(p,g,y,x,r,l);p=y,g=x}}return n||i(g,x)||(u+=v(p,g,y,x,r,l)||0),0!==u}var h=n(27).CMD,u=n(102),c=n(167),f=n(103),d=n(166),p=n(72).normalizeRadian,g=n(20),v=n(104),m=u.containStroke,y=2*Math.PI,x=1e-4,_=[-1,-1,-1],b=[-1,-1];t.exports={contain:function(t,e,n){return l(t,0,!1,e,n)},containStroke:function(t,e,n,i){return l(t,e,!0,n,i)}}},function(t,e,n){"use strict";function i(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}function r(t){return[(t[0][0]+t[1][0])/2,(t[0][1]+t[1][1])/2]}var a=n(21),o=function(){this._track=[]};o.prototype={constructor:o,recognize:function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},clear:function(){return this._track.length=0,this},_doTrack:function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,s=i.length;o<s;o++){var l=i[o],h=a.clientToLocal(n,l,{});r.points.push([h.zrX,h.zrY]),r.touches.push(l)}this._track.push(r)}},_recognize:function(t){for(var e in s)if(s.hasOwnProperty(e)){var n=s[e](this._track,t);if(n)return n}}};var s={pinch:function(t,e){var n=t.length;if(n){var a=(t[n-1]||{}).points,o=(t[n-2]||{}).points||a;if(o&&o.length>1&&a&&a.length>1){var s=i(a)/i(o);!isFinite(s)&&(s=1),e.pinchScale=s;var l=r(a);return e.pinchX=l[0],e.pinchY=l[1],{type:"pinch",target:t[0].target,event:e}}}}};t.exports=o},function(t,e,n){function i(t){return"mousewheel"===t&&f.browser.firefox?"DOMMouseScroll":t}function r(t,e,n){var i=t._gestureMgr;"start"===n&&i.clear();var r=i.recognize(e,t.handler.findHover(e.zrX,e.zrY,null).target,t.dom);if("end"===n&&i.clear(),r){var a=r.type;e.gestureEvent=a,t.handler.dispatchToElement({target:r.target},a,r.event)}}function a(t){t._touching=!0,clearTimeout(t._touchTimer),t._touchTimer=setTimeout(function(){t._touching=!1},700)}function o(t){var e=t.pointerType;return"pen"===e||"touch"===e}function s(t){function e(t,e){return function(){if(!e._touching)return t.apply(e,arguments)}}u.each(x,function(e){t._handlers[e]=u.bind(w[e],t)}),u.each(b,function(e){t._handlers[e]=u.bind(w[e],t)}),u.each(y,function(n){t._handlers[n]=e(w[n],t)})}function l(t){function e(e,n){u.each(e,function(e){p(t,i(e),n._handlers[e])},n)}c.call(this),this.dom=t,this._touching=!1,this._touchTimer,this._gestureMgr=new d,this._handlers={},s(this),f.pointerEventsSupported?e(b,this):(f.touchEventsSupported&&e(x,this),e(y,this))}var h=n(21),u=n(1),c=n(23),f=n(10),d=n(169),p=h.addEventListener,g=h.removeEventListener,v=h.normalizeEvent,m=300,y=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],x=["touchstart","touchend","touchmove"],_={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},b=u.map(y,function(t){var e=t.replace("mouse","pointer");return _[e]?e:t}),w={mousemove:function(t){t=v(this.dom,t),this.trigger("mousemove",t)},mouseout:function(t){t=v(this.dom,t);var e=t.toElement||t.relatedTarget;if(e!=this.dom)for(;e&&9!=e.nodeType;){if(e===this.dom)return;e=e.parentNode}this.trigger("mouseout",t)},touchstart:function(t){t=v(this.dom,t),t.zrByTouch=!0,this._lastTouchMoment=new Date,r(this,t,"start"),w.mousemove.call(this,t),w.mousedown.call(this,t),a(this)},touchmove:function(t){t=v(this.dom,t),t.zrByTouch=!0,r(this,t,"change"),w.mousemove.call(this,t),a(this)},touchend:function(t){t=v(this.dom,t),t.zrByTouch=!0,r(this,t,"end"),w.mouseup.call(this,t),+new Date-this._lastTouchMoment<m&&w.click.call(this,t),a(this)},pointerdown:function(t){w.mousedown.call(this,t)},pointermove:function(t){o(t)||w.mousemove.call(this,t)},pointerup:function(t){w.mouseup.call(this,t)},pointerout:function(t){o(t)||w.mouseout.call(this,t)}};u.each(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(t){w[t]=function(e){e=v(this.dom,e),this.trigger(t,e)}});var S=l.prototype;S.dispose=function(){for(var t=y.concat(x),e=0;e<t.length;e++){var n=t[e];g(this.dom,i(n),this._handlers[n])}},S.setCursor=function(t){this.dom.style.cursor=t||"default"},u.mixin(l,c),t.exports=l},function(t,e,n){var i=n(8);t.exports=i.extend({type:"compound",shape:{paths:null},_updatePathDirty:function(){for(var t=this.__dirtyPath,e=this.shape.paths,n=0;n<e.length;n++)t=t||e[n].__dirtyPath;this.__dirtyPath=t,this.__dirty=this.__dirty||t},beforeBrush:function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1])},buildPath:function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},afterBrush:function(){for(var t=this.shape.paths,e=0;e<t.length;e++)t[e].__dirtyPath=!1},getBoundingRect:function(){return this._updatePathDirty(),i.prototype.getBoundingRect.call(this)}})},function(t,e,n){"use strict";var i=n(1),r=n(39),a=function(t,e,n,i,a){this.x=null==t?.5:t,this.y=null==e?.5:e,this.r=null==n?.5:n,this.type="radial",this.global=a||!1,r.call(this,i)};a.prototype={constructor:a},i.inherits(a,r),t.exports=a},function(t,e,n){var i=n(6),r=i.min,a=i.max,o=i.scale,s=i.distance,l=i.add;t.exports=function(t,e,n,h){var u,c,f,d,p=[],g=[],v=[],m=[];if(h){f=[1/0,1/0],d=[-(1/0),-(1/0)];for(var y=0,x=t.length;y<x;y++)r(f,f,t[y]),a(d,d,t[y]);r(f,f,h[0]),a(d,d,h[1])}for(var y=0,x=t.length;y<x;y++){var _=t[y];if(n)u=t[y?y-1:x-1],c=t[(y+1)%x];else{if(0===y||y===x-1){p.push(i.clone(t[y]));continue}u=t[y-1],c=t[y+1]}i.sub(g,c,u),o(g,g,e);var b=s(_,u),w=s(_,c),S=b+w;0!==S&&(b/=S,w/=S),o(v,g,-b),o(m,g,w);var T=l([],_,v),M=l([],_,m);h&&(a(T,T,f),r(T,T,d),a(M,M,f),r(M,M,d)),p.push(T),p.push(M)}return n&&p.push(p.shift()),p}},function(t,e,n){function i(t,e,n,i,r,a,o){var s=.5*(n-t),l=.5*(i-e);return(2*(e-n)+s+l)*o+(-3*(e-n)-2*s-l)*a+s*r+e}var r=n(6);t.exports=function(t,e){for(var n=t.length,a=[],o=0,s=1;s<n;s++)o+=r.distance(t[s-1],t[s]);var l=o/2;l=l<n?n:l;for(var s=0;s<l;s++){var h,u,c,f=s/(l-1)*(e?n:n-1),d=Math.floor(f),p=f-d,g=t[d%n];e?(h=t[(d-1+n)%n],u=t[(d+1)%n],c=t[(d+2)%n]):(h=t[0===d?d:d-1],u=t[d>n-2?n-1:d+1],c=t[d>n-3?n-1:d+2]);var v=p*p,m=p*v;a.push([i(h[0],g[0],u[0],c[0],p,v,m),i(h[1],g[1],u[1],c[1],p,v,m)])}return a}},function(t,e,n){t.exports=n(8).extend({type:"arc",shape:{cx:0,cy:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),a=e.startAngle,o=e.endAngle,s=e.clockwise,l=Math.cos(a),h=Math.sin(a);t.moveTo(l*r+n,h*r+i),t.arc(n,i,r,a,o,!s)}})},function(t,e,n){"use strict";function i(t,e,n){var i=t.cpx2,r=t.cpy2;return null===i||null===r?[(n?c:h)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?c:h)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?u:l)(t.x1,t.cpx1,t.x2,e),(n?u:l)(t.y1,t.cpy1,t.y2,e)]}var r=n(20),a=n(6),o=r.quadraticSubdivide,s=r.cubicSubdivide,l=r.quadraticAt,h=r.cubicAt,u=r.quadraticDerivativeAt,c=r.cubicDerivativeAt,f=[];t.exports=n(8).extend({type:"bezier-curve",shape:{x1:0,y1:0,x2:0,y2:0,cpx1:0,cpy1:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,a=e.y2,l=e.cpx1,h=e.cpy1,u=e.cpx2,c=e.cpy2,d=e.percent;0!==d&&(t.moveTo(n,i),null==u||null==c?(d<1&&(o(n,l,r,d,f),l=f[1],r=f[2],o(i,h,a,d,f),h=f[1],a=f[2]),t.quadraticCurveTo(l,h,r,a)):(d<1&&(s(n,l,u,r,d,f),l=f[1],u=f[2],r=f[3],s(i,h,c,a,d,f),h=f[1],c=f[2],a=f[3]),t.bezierCurveTo(l,h,u,c,r,a)))},pointAt:function(t){return i(this.shape,t,!1)},tangentAt:function(t){var e=i(this.shape,t,!0);return a.normalize(e,e)}})},function(t,e,n){"use strict";t.exports=n(8).extend({type:"circle",shape:{cx:0,cy:0,r:0},buildPath:function(t,e,n){n&&t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI,!0)}})},function(t,e,n){t.exports=n(8).extend({type:"line",shape:{x1:0,y1:0,x2:0,y2:0,percent:1},style:{stroke:"#000",fill:null},buildPath:function(t,e){var n=e.x1,i=e.y1,r=e.x2,a=e.y2,o=e.percent;0!==o&&(t.moveTo(n,i),o<1&&(r=n*(1-o)+r*o,a=i*(1-o)+a*o),t.lineTo(r,a))},pointAt:function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]}})},function(t,e,n){var i=n(78);t.exports=n(8).extend({type:"polygon",shape:{points:null,smooth:!1,smoothConstraint:null},buildPath:function(t,e){i.buildPath(t,e,!0)}})},function(t,e,n){var i=n(78);t.exports=n(8).extend({type:"polyline",shape:{points:null,smooth:!1,smoothConstraint:null},style:{stroke:"#000",fill:null},buildPath:function(t,e){i.buildPath(t,e,!1)}})},function(t,e,n){var i=n(79);t.exports=n(8).extend({type:"rect",shape:{r:0,x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,r=e.y,a=e.width,o=e.height;e.r?i.buildPath(t,e):t.rect(n,r,a,o),t.closePath()}})},function(t,e,n){t.exports=n(8).extend({type:"ring",shape:{cx:0,cy:0,r:0,r0:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)}})},function(t,e,n){var i=n(8),r=n(77);t.exports=i.extend({type:"sector",shape:{cx:0,cy:0,r0:0,r:0,startAngle:0,endAngle:2*Math.PI,clockwise:!0},brush:r(i.prototype.brush),buildPath:function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),a=Math.max(e.r,0),o=e.startAngle,s=e.endAngle,l=e.clockwise,h=Math.cos(o),u=Math.sin(o);t.moveTo(h*r+n,u*r+i),t.lineTo(h*a+n,u*a+i),t.arc(n,i,a,o,s,!l),t.lineTo(Math.cos(s)*r+n,Math.sin(s)*r+i),0!==r&&t.arc(n,i,r,s,o,l),t.closePath()}})},function(t,e,n){"use strict";var i=n(70),r=n(1),a=r.isString,o=r.isFunction,s=r.isObject,l=n(54),h=function(){this.animators=[]};h.prototype={constructor:h,animate:function(t,e){var n,a=!1,o=this,s=this.__zr;if(t){var h=t.split("."),u=o;a="shape"===h[0];for(var c=0,f=h.length;c<f;c++)u&&(u=u[h[c]]);u&&(n=u)}else n=o;if(!n)return void l('Property "'+t+'" is not existed in element '+o.id);var d=o.animators,p=new i(n,e);return p.during(function(t){o.dirty(a)}).done(function(){d.splice(r.indexOf(d,p),1)}),d.push(p),s&&s.animation.addAnimator(p),p},stopAnimation:function(t){for(var e=this.animators,n=e.length,i=0;i<n;i++)e[i].stop(t);return e.length=0,this},animateTo:function(t,e,n,i,r,s){function l(){u--,u||r&&r()}a(n)?(r=i,i=n,n=0):o(i)?(r=i,i="linear",n=0):o(n)?(r=n,n=0):o(e)?(r=e,e=500):e||(e=500),this.stopAnimation(),this._animateToShallow("",this,t,e,n,i,r);var h=this.animators.slice(),u=h.length;u||r&&r();for(var c=0;c<h.length;c++)h[c].done(l).start(i,s)},_animateToShallow:function(t,e,n,i,a){var o={},l=0;for(var h in n)if(n.hasOwnProperty(h))if(null!=e[h])s(n[h])&&!r.isArrayLike(n[h])?this._animateToShallow(t?t+"."+h:h,e[h],n[h],i,a):(o[h]=n[h],l++);else if(null!=n[h])if(t){var u={};u[t]={},u[t][h]=n[h],this.attr(u)}else this.attr(h,n[h]);return l>0&&this.animate(t,!1).when(null==i?500:i,o).delay(a||0),this}},t.exports=h},function(t,e){function n(){this.on("mousedown",this._dragStart,this),this.on("mousemove",this._drag,this),this.on("mouseup",this._dragEnd,this),this.on("globalout",this._dragEnd,this)}function i(t,e){return{target:t,topTarget:e&&e.topTarget}}n.prototype={constructor:n,_dragStart:function(t){var e=t.target;e&&e.draggable&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.dispatchToElement(i(e,t),"dragstart",t.event))},_drag:function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,r=t.offsetY,a=n-this._x,o=r-this._y;this._x=n,this._y=r,e.drift(a,o,t),this.dispatchToElement(i(e,t),"drag",t.event);var s=this.findHover(n,r,e).target,l=this._dropTarget;this._dropTarget=s,e!==s&&(l&&s!==l&&this.dispatchToElement(i(l,t),"dragleave",t.event),s&&s!==l&&this.dispatchToElement(i(s,t),"dragenter",t.event))}},_dragEnd:function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.dispatchToElement(i(e,t),"dragend",t.event),this._dropTarget&&this.dispatchToElement(i(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null}},t.exports=n},function(t,e,n){function i(t,e,n,i,r,a,o,s,l,h,p){var m=l*(d/180),y=f(m)*(t-n)/2+c(m)*(e-i)/2,x=-1*c(m)*(t-n)/2+f(m)*(e-i)/2,_=y*y/(o*o)+x*x/(s*s);_>1&&(o*=u(_),s*=u(_));var b=(r===a?-1:1)*u((o*o*(s*s)-o*o*(x*x)-s*s*(y*y))/(o*o*(x*x)+s*s*(y*y)))||0,w=b*o*x/s,S=b*-s*y/o,T=(t+n)/2+f(m)*w-c(m)*S,M=(e+i)/2+c(m)*w+f(m)*S,A=v([1,0],[(y-w)/o,(x-S)/s]),I=[(y-w)/o,(x-S)/s],C=[(-1*y-w)/o,(-1*x-S)/s],P=v(I,C);g(I,C)<=-1&&(P=d),g(I,C)>=1&&(P=0),0===a&&P>0&&(P-=2*d),1===a&&P<0&&(P+=2*d),p.addData(h,T,M,o,s,A,P,m,a)}function r(t){if(!t)return[];var e,n=t.replace(/-/g," -").replace(/  /g," ").replace(/ /g,",").replace(/,,/g,",");for(e=0;e<h.length;e++)n=n.replace(new RegExp(h[e],"g"),"|"+h[e]);var r,a=n.split("|"),o=0,l=0,u=new s,c=s.CMD;for(e=1;e<a.length;e++){var f,d=a[e],p=d.charAt(0),g=0,v=d.slice(1).replace(/e,-/g,"e-").split(",");v.length>0&&""===v[0]&&v.shift();for(var m=0;m<v.length;m++)v[m]=parseFloat(v[m]);for(;g<v.length&&!isNaN(v[g])&&!isNaN(v[0]);){var y,x,_,b,w,S,T,M=o,A=l;switch(p){case"l":o+=v[g++],l+=v[g++],f=c.L,u.addData(f,o,l);break;case"L":o=v[g++],l=v[g++],f=c.L,u.addData(f,o,l);break;case"m":o+=v[g++],l+=v[g++],f=c.M,u.addData(f,o,l),p="l";break;case"M":o=v[g++],l=v[g++],f=c.M,u.addData(f,o,l),p="L";break;case"h":o+=v[g++],f=c.L,u.addData(f,o,l);break;case"H":o=v[g++],f=c.L,u.addData(f,o,l);break;case"v":l+=v[g++],f=c.L,u.addData(f,o,l);break;case"V":l=v[g++],f=c.L,u.addData(f,o,l);break;case"C":f=c.C,u.addData(f,v[g++],v[g++],v[g++],v[g++],v[g++],v[g++]),o=v[g-2],l=v[g-1];break;case"c":f=c.C,u.addData(f,v[g++]+o,v[g++]+l,v[g++]+o,v[g++]+l,v[g++]+o,v[g++]+l),o+=v[g-2],l+=v[g-1];break;case"S":y=o,x=l;var I=u.len(),C=u.data;r===c.C&&(y+=o-C[I-4],x+=l-C[I-3]),f=c.C,M=v[g++],A=v[g++],o=v[g++],l=v[g++],u.addData(f,y,x,M,A,o,l);break;case"s":y=o,x=l;var I=u.len(),C=u.data;r===c.C&&(y+=o-C[I-4],x+=l-C[I-3]),f=c.C,M=o+v[g++],A=l+v[g++],o+=v[g++],l+=v[g++],u.addData(f,y,x,M,A,o,l);break;case"Q":M=v[g++],A=v[g++],o=v[g++],l=v[g++],f=c.Q,u.addData(f,M,A,o,l);break;case"q":M=v[g++]+o,A=v[g++]+l,o+=v[g++],l+=v[g++],f=c.Q,u.addData(f,M,A,o,l);break;case"T":y=o,x=l;var I=u.len(),C=u.data;r===c.Q&&(y+=o-C[I-4],x+=l-C[I-3]),o=v[g++],l=v[g++],f=c.Q,u.addData(f,y,x,o,l);break;case"t":y=o,x=l;var I=u.len(),C=u.data;r===c.Q&&(y+=o-C[I-4],x+=l-C[I-3]),o+=v[g++],l+=v[g++],f=c.Q,u.addData(f,y,x,o,l);break;case"A":_=v[g++],b=v[g++],w=v[g++],S=v[g++],T=v[g++],M=o,A=l,o=v[g++],l=v[g++],f=c.A,i(M,A,o,l,S,T,_,b,w,f,u);break;case"a":_=v[g++],b=v[g++],w=v[g++],S=v[g++],T=v[g++],M=o,A=l,o+=v[g++],l+=v[g++],f=c.A,i(M,A,o,l,S,T,_,b,w,f,u)}}"z"!==p&&"Z"!==p||(f=c.Z,u.addData(f)),r=f}return u.toStatic(),u}function a(t,e){var n=r(t);return e=e||{},e.buildPath=function(t){if(t.setData){t.setData(n.data);var e=t.getContext();e&&t.rebuildPath(e)}else{var e=t;n.rebuildPath(e)}},e.applyTransform=function(t){l(n,t),this.dirty(!0)},e}var o=n(8),s=n(27),l=n(187),h=["m","M","l","L","v","V","h","H","z","Z","c","C","q","Q","t","T","s","S","a","A"],u=Math.sqrt,c=Math.sin,f=Math.cos,d=Math.PI,p=function(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])},g=function(t,e){return(t[0]*e[0]+t[1]*e[1])/(p(t)*p(e))},v=function(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(g(t,e))};t.exports={createFromString:function(t,e){return new o(a(t,e))},extendFromString:function(t,e){return o.extend(a(t,e))},mergePath:function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var a=t[r];a.path||a.createPathProxy(),a.__dirtyPath&&a.buildPath(a.path,a.shape,!0),n.push(a.path)}var s=new o(e);return s.createPathProxy(),s.buildPath=function(t){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e);
},s}}},function(t,e,n){function i(t,e){var n,i,a,u,c,f,d=t.data,p=r.M,g=r.C,v=r.L,m=r.R,y=r.A,x=r.Q;for(a=0,u=0;a<d.length;){switch(n=d[a++],u=a,i=0,n){case p:i=1;break;case v:i=1;break;case g:i=3;break;case x:i=2;break;case y:var _=e[4],b=e[5],w=l(e[0]*e[0]+e[1]*e[1]),S=l(e[2]*e[2]+e[3]*e[3]),T=h(-e[1]/S,e[0]/w);d[a]*=w,d[a++]+=_,d[a]*=S,d[a++]+=b,d[a++]*=w,d[a++]*=S,d[a++]+=T,d[a++]+=T,a+=2,u=a;break;case m:f[0]=d[a++],f[1]=d[a++],o(f,f,e),d[u++]=f[0],d[u++]=f[1],f[0]+=d[a++],f[1]+=d[a++],o(f,f,e),d[u++]=f[0],d[u++]=f[1]}for(c=0;c<i;c++){var f=s[c];f[0]=d[a++],f[1]=d[a++],o(f,f,e),d[u++]=f[0],d[u++]=f[1]}}}var r=n(27).CMD,a=n(6),o=a.applyTransform,s=[[],[],[]],l=Math.sqrt,h=Math.atan2;t.exports=i}])});