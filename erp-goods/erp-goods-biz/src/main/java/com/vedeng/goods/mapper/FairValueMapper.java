package com.vedeng.goods.mapper;


import com.vedeng.goods.domain.entity.FairValueEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FairValueMapper {
    /**
     * delete by primary key
     *
     * @param fairValueId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer fairValueId);

    /**
     * select by primary key
     *
     * @param fairValueId primary key
     * @return object by primary key
     */
    FairValueEntity selectByPrimaryKey(Integer fairValueId);

    /**
     * 根据goodsId查询对应的公允价信息
     *
     * @param goodsId 商品id
     * @return FairValueEntity
     */
    FairValueEntity selectByGoodsId(@Param("goodsId") Integer goodsId);

    /**
     * 批量插入
     *
     * @param list List<FairValueEntity>
     */
    void batchInsert(List<FairValueEntity> list);

    /**
     * 批量更新
     *
     * @param list List<FairValueEntity>
     */
    void batchUpdate(List<FairValueEntity> list);
}