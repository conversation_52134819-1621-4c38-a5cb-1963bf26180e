package com.wms.dao;


import com.wms.model.po.WmsOutInOrderConcat;

public interface WmsOutInOrderConcatMapper {
    int deleteByPrimaryKey(Integer outInOrderConcatId);

    int insert(WmsOutInOrderConcat record);

    int insertSelective(WmsOutInOrderConcat record);

    WmsOutInOrderConcat selectByPrimaryKey(Integer outInOrderConcatId);

    int updateByPrimaryKeySelective(WmsOutInOrderConcat record);

    int updateByPrimaryKey(WmsOutInOrderConcat record);

    WmsOutInOrderConcat getOutInOrderConcatInfo(WmsOutInOrderConcat wmsOutInOrderConcat);
}