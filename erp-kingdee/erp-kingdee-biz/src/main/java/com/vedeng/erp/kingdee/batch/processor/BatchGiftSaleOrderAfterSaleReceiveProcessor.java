package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInDto;
import com.vedeng.erp.kingdee.batch.dto.BatchWarehouseGoodsOutInItemDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDetailLinkDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveCommonDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeSaleInStockQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveCommonService;
import com.vedeng.erp.kingdee.service.KingDeeSaleInStockService;
import com.vedeng.erp.kingdee.service.KingDeeSaleOutStockService;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售单 赠品 标准应收单推送
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/11 9:47
 */
@Service
@Slf4j
public class BatchGiftSaleOrderAfterSaleReceiveProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeReceiveCommonDto> {

    private final String sourceType = "SAL_RETURNSTOCK";
    private final String fEntityDetailLinkFruleId = "AR_ReturnToReceivableMap";
    private final String fEntityDetailLinkFstableName = "T_SAL_RETURNSTOCKENTRY";

    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

    @Autowired
    private KingDeeSaleOutStockService kingDeeSaleOutStockService;

    @Autowired
    private KingDeeSaleInStockService kingDeeSaleInStockService;


    @Autowired
    private KingDeeReceiveCommonService kingDeeReceiveCommonService;

    @Autowired
    private BatchInvoiceDtoMapper batchInvoiceDtoMapper;


    @Override
    public KingDeeReceiveCommonDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {

        // 判断标准应收数据是否存在
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveCommonService.getKingDeeReceiveCommon(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isNotEmpty(kingDeeSaleReceivable)) {
            log.info("销售赠品退货标准应收单,金蝶数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        // 判断出库但是是否推送金蝶
        List<KingDeeSaleInStockQueryResultDto> kingDeeSaleInStocks = kingDeeSaleInStockService.getKingDeeSaleInStocks(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(kingDeeSaleInStocks)) {
            log.info("销售赠品退货标准应收单,对应入库单未推送金蝶:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }


        // 获取票的最小时间 和出库时间作比较 取最大值
        BatchWarehouseGoodsOutInDto byOutInNo = batchWarehouseGoodsOutInDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo());

        if (Objects.isNull(byOutInNo)) {
            log.info("销售赠品退货标准应收单,对应入库单未查到主单信息:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        List<BatchWarehouseGoodsOutInItemDto> batchWarehouseGoodsOutInItemDtos = batchWarehouseGoodsOutInItemDtoMapper.selectByOutInNoAndIsGiftAfterSale(batchWarehouseGoodsOutInDto.getOutInNo());
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInItemDtos)) {
            log.info("销售赠品退货标准应收单,对应入库单未查到带赠品的明细信息:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        BatchInvoiceDto bySaleOrderNoAndMinAddTime = batchInvoiceDtoMapper.findBySaleOrderNoAndMinAddTimeByAfterSaleNo(byOutInNo.getRelateNo());
        if (Objects.isNull(bySaleOrderNoAndMinAddTime)) {
            log.info("销售赠品退货标准应收单,对应入库单未查到票的信息:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        Date time = bySaleOrderNoAndMinAddTime.getAddTime() > byOutInNo.getOutInTime().getTime() ? new Date(bySaleOrderNoAndMinAddTime.getAddTime()) : byOutInNo.getOutInTime();
        // 获取入库单赠品相关数据明细数据

        log.info("开始构造销售单标准应收单信息：{}", JSON.toJSONString(batchWarehouseGoodsOutInItemDtos));

        // 第一层
        KingDeeReceiveCommonDto kingDeeReceiveCommonDto = new KingDeeReceiveCommonDto();
        kingDeeReceiveCommonDto.setFId("0");
        kingDeeReceiveCommonDto.setFDate(DateUtil.formatDate(time));
        kingDeeReceiveCommonDto.setFQzokBddjtId(batchWarehouseGoodsOutInDto.getOutInNo());

        kingDeeReceiveCommonDto.setFCustomerId(bySaleOrderNoAndMinAddTime.getTraderCustomerId().toString());
        BigDecimal taxRatio = new BigDecimal("13");
        Map<String, KingDeeSaleInStockQueryResultDto> collect = kingDeeSaleInStocks.stream().collect(Collectors.toMap(x->{

            // 兼容售后入库单 的 fid 不同规格
            String f_qzok_bddjhid = x.getF_QZOK_BDDJHID();

            if (f_qzok_bddjhid.contains(StrUtil.DASHED)) {
                List<String> split = StrUtil.split(f_qzok_bddjhid, StrUtil.DASHED);
                if (CollUtil.isEmpty(split)) {
                    throw new KingDeeException("金蝶查询到的售后入库单" + batchWarehouseGoodsOutInDto.getOutInNo() + "单据行id:" + f_qzok_bddjhid + "异常");
                }
                return split.get(0);
            } else {
                return f_qzok_bddjhid;
            }
        }, v -> v, (k1, k2) -> k1));
        List<KingDeeReceiveCommonDetailDto> detailDtoList = new ArrayList<>();
        // 二三两层
        batchWarehouseGoodsOutInItemDtos.forEach(x->{

            // 查询当前发票明细对应的erp出库单信息
            KingDeeSaleInStockQueryResultDto kingDeeSaleInStockQueryResultDto = collect.get(x.getWarehouseGoodsOutInDetailId().toString());
            if (Objects.isNull(kingDeeSaleInStockQueryResultDto)) {
                log.info("销售赠品入库单：{}，当前赠品明细行未推金蝶：{}", JSON.toJSONString(batchWarehouseGoodsOutInDto),JSON.toJSONString(x));
                return;
            }

            KingDeeReceiveCommonDetailDto tempDetailDto = bindKingDeeReceiveCommonDetailDto(taxRatio, x, kingDeeSaleInStockQueryResultDto);
            detailDtoList.add(tempDetailDto);
        });

        if (detailDtoList.size() == 0) {
            log.error("销售赠品应收单第二层信息为空，构造失败：{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }
        kingDeeReceiveCommonDto.setFEntityDetail(detailDtoList);
        log.info("销售单标赠品准应收单信息构造成功：{}", JSON.toJSONString(kingDeeReceiveCommonDto));
        return kingDeeReceiveCommonDto;
    }

    /**
     * 绑定对象
     * @param taxRatio 税率
     * @param x 出库单明细
     * @param kingDeeSaleInStockQueryResultDto 推送金蝶结果
     * @return KingDeeReceiveCommonDetailDto
     */
    private KingDeeReceiveCommonDetailDto bindKingDeeReceiveCommonDetailDto(BigDecimal taxRatio, BatchWarehouseGoodsOutInItemDto x, KingDeeSaleInStockQueryResultDto kingDeeSaleInStockQueryResultDto) {
        KingDeeReceiveCommonDetailDto tempDetailDto = new KingDeeReceiveCommonDetailDto();
        tempDetailDto.setFMaterialId(x.getSku());
        // 计价数量
        BigDecimal priceQuantity = new BigDecimal(kingDeeSaleInStockQueryResultDto.getFRealQty()).abs().negate();
        tempDetailDto.setFPriceQty(priceQuantity);
        // 含税单价=含税总价/数量
        tempDetailDto.setFTaxPrice(BigDecimal.ZERO);
        tempDetailDto.setFEntryTaxRate(taxRatio);
        // 价税合计=含税单价*计价数量
        tempDetailDto.setFAllAmountForD(BigDecimal.ZERO);
        // 税额=[价税合计/（1+税率）]*税率 (此处需要先乘再除才能保留两位小数)
        tempDetailDto.setFTaxAmountForD(BigDecimal.ZERO);
        // 不含税金额=价税合计-税额（四舍五入保留2位小数）
        tempDetailDto.setFNoTaxAmountForD(BigDecimal.ZERO);
        tempDetailDto.setFIsFree(false);
        tempDetailDto.setFQzokBddjhId("GIFT"+ x.getWarehouseGoodsOutInDetailId().toString());
        tempDetailDto.setFSourceType(sourceType);

        KingDeeReceiveCommonDetailLinkDto tempLink = new KingDeeReceiveCommonDetailLinkDto();
        tempLink.setFEntityDetailLinkFruleId(fEntityDetailLinkFruleId);
        tempLink.setFEntityDetailLinkFstableName(fEntityDetailLinkFstableName);
        // 出库主单
        // 出库明细
        tempLink.setFLinkId("0");
        tempLink.setFEntityDetailLinkFflowlineId("0");
        tempLink.setFEntityDetailLinkFstableId("0");
        tempLink.setFEntityDetailLinkFsbillId(kingDeeSaleInStockQueryResultDto.getFID());
        tempLink.setFEntityDetailLinkFsId(kingDeeSaleInStockQueryResultDto.getFEntity_FENTRYID());
        tempLink.setFEntityDetailLinkFbasicunitqtyold(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFbasicunitqty(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFsalbaseqtyold(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFsalbaseqty(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFstockbaseqtyold(priceQuantity.toString());
        tempLink.setFEntityDetailLinkFstockbaseqty(priceQuantity.toString());

        tempLink.setFEntityDetailLinkFallamountforDold(BigDecimal.ZERO.toString());
        tempLink.setFEntityDetailLinkFallamountforD(BigDecimal.ZERO.toString());

        tempDetailDto.setFEntityDetail_Link(Collections.singletonList(tempLink));
        return tempDetailDto;
    }


}