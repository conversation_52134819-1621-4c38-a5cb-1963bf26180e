package com.vedeng.price.model;

public class PriceChangeReadRecord {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_READ_RECORED_ID
     *
     * @mbggenerated <PERSON><PERSON> Aug 24 15:38:49 CST 2021
     */
    private Long priceChangeReadRecoredId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_DATE
     *
     * @mbggenerated Tu<PERSON> Aug 24 15:38:49 CST 2021
     */
    private String priceChangeDate;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_READ_RECORED.READ_TIME
     *
     * @mbggenerated <PERSON><PERSON> Aug 24 15:38:49 CST 2021
     */
    private Long readTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_PRICE_CHANGE_READ_RECORED.READER_ID
     *
     * @mbggenerated <PERSON><PERSON> Aug 24 15:38:49 CST 2021
     */
    private Long readerId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_READ_RECORED_ID
     *
     * @return the value of T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_READ_RECORED_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getPriceChangeReadRecoredId() {
        return priceChangeReadRecoredId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_READ_RECORED_ID
     *
     * @param priceChangeReadRecoredId the value for T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_READ_RECORED_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setPriceChangeReadRecoredId(Long priceChangeReadRecoredId) {
        this.priceChangeReadRecoredId = priceChangeReadRecoredId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_DATE
     *
     * @return the value of T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_DATE
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public String getPriceChangeDate() {
        return priceChangeDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_DATE
     *
     * @param priceChangeDate the value for T_PRICE_CHANGE_READ_RECORED.PRICE_CHANGE_DATE
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setPriceChangeDate(String priceChangeDate) {
        this.priceChangeDate = priceChangeDate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_READ_RECORED.READ_TIME
     *
     * @return the value of T_PRICE_CHANGE_READ_RECORED.READ_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getReadTime() {
        return readTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_READ_RECORED.READ_TIME
     *
     * @param readTime the value for T_PRICE_CHANGE_READ_RECORED.READ_TIME
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setReadTime(Long readTime) {
        this.readTime = readTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_PRICE_CHANGE_READ_RECORED.READER_ID
     *
     * @return the value of T_PRICE_CHANGE_READ_RECORED.READER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public Long getReaderId() {
        return readerId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_PRICE_CHANGE_READ_RECORED.READER_ID
     *
     * @param readerId the value for T_PRICE_CHANGE_READ_RECORED.READER_ID
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    public void setReaderId(Long readerId) {
        this.readerId = readerId;
    }
}