package com.vedeng.infrastructure.kingdee.domain.dto;

import lombok.*;

/**
 * <AUTHOR>
 * @description 金蝶查询过滤条件
 * @date 2022/9/9 13:07
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class KingDeeQueryFilterDto {
    @Builder.Default
    private String left = "(";
    private String fieldName;
    @Builder.Default
    private String compare = "=";
    private String value;
    @Builder.Default
    private String right = ")";
    @Builder.Default
    private String logic = "and";
}
