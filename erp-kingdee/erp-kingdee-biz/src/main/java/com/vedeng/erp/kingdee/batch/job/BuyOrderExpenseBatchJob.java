package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchPayExpensesDto;
import com.vedeng.erp.kingdee.batch.processor.BatchExpensePayableOrderInvoiceProcessorService;
import com.vedeng.erp.kingdee.batch.processor.BatchExpensePayableOrderProcessorService;
import com.vedeng.erp.kingdee.batch.writer.BatchExpensePayableOrderInvoiceWriterService;
import com.vedeng.erp.kingdee.batch.writer.BatchExpensePayableOrderWriterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购费用单正向入库流程job
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderExpenseBatchJob extends BaseJob {


    @Autowired
    private BatchExpensePayableOrderProcessorService batchExpensePayableOrderProcessorService;

    @Autowired
    private BatchExpensePayableOrderWriterService batchExpensePayableOrderWriterService;

    @Autowired
    private BatchExpensePayableOrderInvoiceProcessorService batchExpensePayableOrderInvoiceProcessorService;

    @Autowired
    private BatchExpensePayableOrderInvoiceWriterService batchExpensePayableOrderInvoiceWriterService;

    public Job buyOrderExpenseFlowJob() {
        return jobBuilderFactory.get("buyOrderExpenseFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(payableOrder())
                .next(expenseInvoice())
                .build();
    }


    /**
     * 采购费用应付单
     */
    private Step payableOrder() {
        return stepBuilderFactory.get("采购费用应付单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderExpenseInvoiceDtoItemReader(null, null))
                .processor(batchExpensePayableOrderProcessorService)
                .writer(batchExpensePayableOrderWriterService)
                .listener(baseProcessListener)
                .build();
    }

    /**
     * 采购费用单蓝票
     */
    private Step expenseInvoice() {
        return stepBuilderFactory.get("采购费用单蓝票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(buyOrderExpenseInvoiceDtoItemReader(null, null))
                .processor(batchExpensePayableOrderInvoiceProcessorService)
                .writer(batchExpensePayableOrderInvoiceWriterService)
                .listener(baseProcessListener)
                .build();
    }




    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> buyOrderExpenseInvoiceDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                        @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购费用开票
                .type(4126)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.beginOfDay(DateUtil.parseDateTime(beginTime)).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.endOfDay(DateUtil.parseDateTime(endTime)).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), batchInvoiceDto);
    }

}

