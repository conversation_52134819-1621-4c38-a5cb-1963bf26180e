package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.trader.domain.PublicCustomerRecord;
import com.vedeng.erp.trader.mapper.PublicCustomerRecordMapper;
import com.vedeng.erp.trader.mapper.PublicTraderMapper;
import com.vedeng.erp.trader.service.PublicTraderService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.service.TraderCustomerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022/2/15 16:26
 */
@Service
public class PublicTraderServiceImpl implements PublicTraderService {
    public static Logger logger = LoggerFactory.getLogger(PublicTraderServiceImpl.class);

    @Resource
    private PublicTraderMapper publicTraderMapper;

    @Resource
    private RegionMapper regionMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    private ErpMsgProducer erpMsgProducer;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Resource
    private PublicCustomerRecordMapper publicCustomerRecordMapper;

    @Override
    public List<Integer> getAllTraderCustomerInRegionsByUserIdList(List<Integer> userIdList) {

        if (CollectionUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<Integer> regionIdListByUserIdList = publicTraderMapper.selectRegionIdListByUserIdList(userIdList);
        if (CollectionUtil.isEmpty(regionIdListByUserIdList)) {
            return new ArrayList<>();
        }
        List<Integer> typeEqThreeRegionIdListByRegionIdList = regionMapper.selectTypeEqThreeRegionIdListByRegionIdList(regionIdListByUserIdList);
        if (CollectionUtil.isEmpty(typeEqThreeRegionIdListByRegionIdList)) {
            return new ArrayList<>();
        }
        List<Integer> salesByRegionIdList = publicTraderMapper.selectSalesByRegionIdList(typeEqThreeRegionIdListByRegionIdList);
        if (CollectionUtil.isEmpty(salesByRegionIdList)) {
            return new ArrayList<>();
        }
        List<Integer> traderIdListBySalesUserIdList = publicTraderMapper.selectTraderIdListBySalesUserIdList(salesByRegionIdList);
        if (CollectionUtil.isEmpty(traderIdListBySalesUserIdList)) {
            return new ArrayList<>();
        }
        return traderIdListBySalesUserIdList;
    }

    @Override
    public List<Integer> getAllUserListByRegionRule(List<Integer> userIdList) {
        if (CollectionUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        List<Integer> regionIdListByUserIdList = publicTraderMapper.selectRegionIdListByUserIdList(userIdList);
        if (CollectionUtil.isEmpty(regionIdListByUserIdList)) {
            return new ArrayList<>();
        }
        List<Integer> typeEqThreeRegionIdListByRegionIdList = regionMapper.selectTypeEqThreeRegionIdListByRegionIdList(regionIdListByUserIdList);
        if (CollectionUtil.isEmpty(typeEqThreeRegionIdListByRegionIdList)) {
            return new ArrayList<>();
        }
        List<Integer> salesByRegionIdList = publicTraderMapper.selectSalesByRegionIdList(typeEqThreeRegionIdListByRegionIdList);
        if (CollectionUtil.isEmpty(salesByRegionIdList)) {
            return new ArrayList<>();
        }
        return salesByRegionIdList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo lockPublicTrader(Integer publicCustomerRecordId) {

        logger.info("锁定公海列表 -- {}", publicCustomerRecordId);
        if (Objects.isNull(publicCustomerRecordId)) {
            return ResultInfo.error("系统繁忙，请刷新列表！");
        }

        // a)销售点击【锁定】，校验客户是否锁定/撤销公海，此时客户名称不可点击；
        //      该客户已被其他销售锁定：该客户已被锁定，下次速度快点哦！
        //      该客户已被撤销公海：该客户已不在公海，请刷新列表！
        // b)校验成功以后，销售锁定该客户，若未刷新公海页面，点击客户名称进入客户详情页，【锁定】按钮置灰；
        // c)刷新公海页面，该客户从公海列表移除，插入客户列表，客户列表中排序规则沿用现有规则；
        PublicCustomerRecord publicCustomerRecord = publicTraderMapper.selectByPrimaryKey(publicCustomerRecordId);
        if (Objects.isNull(publicCustomerRecord) ||
                Objects.isNull(publicCustomerRecord.getTraderCustomerId())) {
            return ResultInfo.error("系统繁忙，请刷新列表！");
        }
        if (ErpConst.ONE.equals(publicCustomerRecord.getIsPrivatized())) {
            return ResultInfo.error("该客户已被锁定，下次速度快点哦！");
        }
        if (ErpConst.TWO.equals(publicCustomerRecord.getIsPrivatized()) || ErpConst.THREE.equals(publicCustomerRecord.getIsPrivatized())) {
            return ResultInfo.error("该客户已不在公海，请刷新列表！");
        }

        TraderCustomer traderCustomer = traderCustomerMapper.selectByPrimaryKey(publicCustomerRecord.getTraderCustomerId());
        if (Objects.isNull(traderCustomer)) {
            return ResultInfo.error("系统繁忙，请刷新列表！");
        }

        // 分配公海客户给当前登录人
        HttpServletRequest httpServletRequest = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        User currentLoginUser = (User) httpServletRequest.getSession().getAttribute(Consts.SESSION_USER);
        List<Integer> traderCustomerIdList = new ArrayList<>();
        if (Objects.isNull(traderCustomer.getAssociatedCustomerGroup()) || ErpConst.ZERO.equals(traderCustomer.getAssociatedCustomerGroup().intValue())) {
            traderCustomerIdList.add(publicCustomerRecord.getTraderCustomerId());
        } else {
            List<Integer> relatedIdByAssociatedCustomerGroup =
                    traderCustomerMapper.selectRelatedIdByAssociatedCustomerGroup(traderCustomer.getAssociatedCustomerGroup());
            traderCustomerIdList.addAll(relatedIdByAssociatedCustomerGroup);
        }
        traderCustomerIdList.forEach(traderCustomerId -> {
            TraderCustomer traderCust = traderCustomerMapper.selectByPrimaryKey(traderCustomerId);
            traderCustomerService.assignSingleCustomer(traderCust.getTraderId(), currentLoginUser.getUserId(), currentLoginUser.getCompanyId(), currentLoginUser,null);
        });

        // 更新公海列表状态
        Integer updateCount = publicTraderMapper.updatePrivatizedAndTimeInIdList(traderCustomerIdList,currentLoginUser.getUsername());
        if (Objects.isNull(updateCount) || updateCount != traderCustomerIdList.size()) {
            throw new RuntimeException("该客户关联客户存在已被锁定或撤销的状态，请查询关联客户是否分别存在在两个销售！");
        }

        // 24h后尝试解锁锁定客户
        try {
            erpMsgProducer.sendMsg(RabbitConfig.PUBLIC_CUSTOMER_LOCK_EXCHANGE, RabbitConfig.PUBLIC_CUSTOMER_LOCK_ROUTINGKEY, JSONUtil.toJsonStr(traderCustomerIdList));
        } catch (Exception e) {
            logger.error("锁定公海列表,发送队列消息异常 - {}:", publicCustomerRecordId);
            throw new RuntimeException("锁定公海列表,发送队列消息异常");
        }

        return ResultInfo.success();
    }

    /**
     * 公海虚拟账户配置人员ID
     */
    @Value("${VIRTUAL_PUBLIC_CUSTOMER_USER_ID}")
    private Integer virtualPublicCustomerUserId;

    @Resource
    private UserMapper userMapper;
    
    @Override
    @Transactional
    public ResultInfo cancelLockPublicTrader(Integer publicCustomerRecordId) throws Exception {
        logger.info("撤销锁定公海列表 -- {}", publicCustomerRecordId);
        if (Objects.isNull(publicCustomerRecordId)) {
            return ResultInfo.error("系统繁忙，请刷新列表！");
        }
        PublicCustomerRecord publicCustomerRecord = publicTraderMapper.selectByPrimaryKey(publicCustomerRecordId);
        if (Objects.isNull(publicCustomerRecord) ||
                Objects.isNull(publicCustomerRecord.getTraderCustomerId())) {
            return ResultInfo.error("系统繁忙，请刷新列表！");
        }
        if (!ErpConst.ONE.equals(publicCustomerRecord.getIsPrivatized())) {
            return ResultInfo.error("数据已更新，请刷新列表！");
        }
        
        User virtualUser = userMapper.selectByPrimaryKey(virtualPublicCustomerUserId);
        Trader trader = traderCustomerMapper.getTraderByTraderCustomerId(publicCustomerRecord.getTraderCustomerId());
        // 更新Bei
        traderCustomerService.assignSingleCustomer(trader.getTraderId(), virtualUser.getUserId(), virtualUser.getCompanyId(), virtualUser,null);

        // 重新进入公海列表
        PublicCustomerRecord record = new PublicCustomerRecord();
        record.setTraderCustomerId(publicCustomerRecord.getTraderCustomerId());
        record.setOriginUserId(publicCustomerRecord.getOriginUserId());
        record.setAddTime(System.currentTimeMillis());
        record.setUpdater(CurrentUser.getCurrentUser().getId());
        publicCustomerRecordMapper.insertSelective(record);
        
        return new ResultInfo(0,"撤销锁定成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo cancelPublicTraderJudge(Integer publicCustomerRecordId,Integer dayInput,User user) {
        //查询客户进入公海记录表
        PublicCustomerRecord publicCustomerRecord = publicCustomerRecordMapper.selectByPrimaryKey(Long.valueOf(publicCustomerRecordId));
        if(ErpConst.TWO.equals(publicCustomerRecord.getIsPrivatized())){
            //当前客户进入公海记录已被撤销的情况下--拦截
            return new ResultInfo(-1,"该客户已被撤销公海，请勿重复操作！");
        }
        if(ErpConst.ONE.equals(publicCustomerRecord.getIsPrivatized())){
            //当前客户进入公海且已被锁定
            return new ResultInfo(-1,"该客户已被锁定！如需划拨，请联系质管部。");
        }
        if(ErpConst.THREE.equals(publicCustomerRecord.getIsPrivatized())){
            //当前客户进入公海记录已被撤销的情况下--拦截
            return new ResultInfo(-1,"该客户已被划拨！如需划拨，请联系质管部。");
        }
        //撤销后将客户重新分配给归属销售
        //获取所有关联客户
        TraderCustomer traderCustomer = traderCustomerMapper.selectByPrimaryKey(publicCustomerRecord.getTraderCustomerId());
        List<Integer> traderCustomerIdList = new ArrayList<>();
        if (Objects.isNull(traderCustomer.getAssociatedCustomerGroup()) || ErpConst.ZERO.equals(traderCustomer.getAssociatedCustomerGroup().intValue())) {
            traderCustomerIdList.add(publicCustomerRecord.getTraderCustomerId());
        } else {
            List<Integer> relatedIdByAssociatedCustomerGroup =
                    traderCustomerMapper.selectRelatedIdByAssociatedCustomerGroup(traderCustomer.getAssociatedCustomerGroup());
            traderCustomerIdList.addAll(relatedIdByAssociatedCustomerGroup);
        }

        //获取撤销保护截止日期
        Long nowDateLong = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Long revocationProtectionDeadline =  DateUtil.getDateAfter(new Date(),dayInput);
        for(Integer traderCustId : traderCustomerIdList){
            TraderCustomer traderCust = traderCustomerMapper.selectByPrimaryKey(traderCustId);
            PublicCustomerRecord publicCustomerRecordAsso = publicCustomerRecordMapper.queryInfoByCustId(traderCustId);
            if(!ErpConst.ZERO.equals(publicCustomerRecordAsso.getIsPrivatized())){
                logger.info("关联客户存在被私有，关联tradercustomerid为{},",traderCustId);
//                throw new RuntimeException("关联客户存在被私有,无法撤销");
            }
            traderCustomerService.assignSingleCustomer(traderCust.getTraderId(), publicCustomerRecordAsso.getOriginUserId(), ErpConst.ONE, user,null);
        }
        //所有关联客户撤销公海
        publicTraderMapper.cancelPublicTrader(traderCustomerIdList,nowDateLong,revocationProtectionDeadline);
        return new ResultInfo(0,"撤销公海成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo banchCancelPublicTraderJudge(Integer[] publicCustomerRecordIds, Integer dayInput, User user) {

        if (publicCustomerRecordIds != null && publicCustomerRecordIds.length > 0) {

            // 先判断
            for (Integer publicCustomerRecordId: publicCustomerRecordIds) {
                //查询客户进入公海记录表
                PublicCustomerRecord publicCustomerRecord = publicCustomerRecordMapper.selectByPrimaryKey(Long.valueOf(publicCustomerRecordId));
                if(ErpConst.TWO.equals(publicCustomerRecord.getIsPrivatized())){
                    //当前客户进入公海记录已被撤销的情况下--拦截
                    throw  new ServiceException(-1,"该客户已被撤销公海，请勿重复操作！");
                }
                if(ErpConst.ONE.equals(publicCustomerRecord.getIsPrivatized())){
                    //当前客户进入公海且已被锁定
                    throw  new ServiceException(-1,"该客户已被锁定！如需划拨，请联系质管部。");
                }
                if(ErpConst.THREE.equals(publicCustomerRecord.getIsPrivatized())){
                    //当前客户进入公海记录已被撤销的情况下--拦截
                    throw  new ServiceException(-1,"该客户已被划拨！如需划拨，请联系质管部。");
                }
            }
            for (Integer publicCustomerRecordId: publicCustomerRecordIds) {
                //查询客户进入公海记录表
                PublicCustomerRecord publicCustomerRecord = publicCustomerRecordMapper.selectByPrimaryKey(Long.valueOf(publicCustomerRecordId));

                //撤销后将客户重新分配给归属销售
                //获取所有关联客户
                TraderCustomer traderCustomer = traderCustomerMapper.selectByPrimaryKey(publicCustomerRecord.getTraderCustomerId());
                List<Integer> traderCustomerIdList = new ArrayList<>();
                if (Objects.isNull(traderCustomer.getAssociatedCustomerGroup()) || ErpConst.ZERO.equals(traderCustomer.getAssociatedCustomerGroup().intValue())) {
                    traderCustomerIdList.add(publicCustomerRecord.getTraderCustomerId());
                } else {
                    List<Integer> relatedIdByAssociatedCustomerGroup =
                            traderCustomerMapper.selectRelatedIdByAssociatedCustomerGroup(traderCustomer.getAssociatedCustomerGroup());
                    traderCustomerIdList.addAll(relatedIdByAssociatedCustomerGroup);
                }

                //获取撤销保护截止日期
                Long nowDateLong = LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
                Long revocationProtectionDeadline =  DateUtil.getDateAfter(new Date(),dayInput);
                for(Integer traderCustId : traderCustomerIdList){
                    TraderCustomer traderCust = traderCustomerMapper.selectByPrimaryKey(traderCustId);
                    PublicCustomerRecord publicCustomerRecordAsso = publicCustomerRecordMapper.queryInfoByCustId(traderCustId);
                    if(!ErpConst.ZERO.equals(publicCustomerRecordAsso.getIsPrivatized())){
                        logger.info("关联客户存在被私有，关联tradercustomerid为{},",traderCustId);
//                throw new RuntimeException("关联客户存在被私有,无法撤销");
                    }
                    traderCustomerService.assignSingleCustomer(traderCust.getTraderId(), publicCustomerRecordAsso.getOriginUserId(), ErpConst.ONE, user,null);
                }
                //所有关联客户撤销公海
                publicTraderMapper.cancelPublicTrader(traderCustomerIdList,nowDateLong,revocationProtectionDeadline);

            }
            return new ResultInfo(0,"撤销公海成功");
        }
        throw new ServiceException(0, "参数缺失");

    }
}
