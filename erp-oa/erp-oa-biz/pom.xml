<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.vedeng.erp</groupId>
        <artifactId>erp-oa</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>erp-oa-biz</artifactId>
    <packaging>jar</packaging>
<properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <mysql.driver.version>5.1.47</mysql.driver.version>
        <spring.version>4.1.9.RELEASE</spring.version>
        <mybatis.version>3.3.1</mybatis.version>
        <skipTests>true</skipTests>
</properties>
    <dependencies>
        <!-- Common -->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-mybatis</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- feign -->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-feign</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!--  doc-api -->
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-oa-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.mybatis-generator</groupId>
            <artifactId>mybatis-generator</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>
        
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-infrastructure</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        
        <dependency>
			<groupId>com.vedeng.erp</groupId>
			<artifactId>erp-trader-biz</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <!--mysql驱动包-->
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql.driver.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>com.vedeng.mybatis-generator</groupId>
                        <artifactId>mybatis-generator</artifactId>
                        <version>1.0-SNAPSHOT</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>