<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<!-- 引入样式 -->
<!-- 引入脚本 -->
<style scoped>
    .el-divider--horizontal {
        margin: 8px 0;
        background: 0 0;
        border-top: 3px dashed #e8eaec;
    }

    .icon_business {
        width: 1em;
        height: 1em;
        vertical-align: -0.15em;
        fill: currentColor;
        overflow: hidden;
    }
</style>
<div id="app" style="display: none;">
    <el-card class="box-card" shadow="never">
        <div slot="header">
            <span style="font-size: 16px">经营终端大类（C）</span>

        </div>

        <div>

        </div>
         <br>
        <template v-for="threeMode in threeModeList">
            <el-card class="card_child" :body-style="threeMode.show ? {}:{'padding-bottom':'0px','padding-top':'0px'}">
                <div slot="header" class="clearfix">
                    <span>{{threeMode.threeCustomerType.value}}</span>
                    <el-switch
                            style="display: block"
                            v-model="threeMode.show"
                            @change="switchChange(threeMode)"
                    >
                    </el-switch>
                </div>
                <div v-show="threeMode.show">
                    <el-row v-if="threeMode.traderCustomerMarketingType == 0 || threeMode.traderCustomerMarketingType == 1">
                        <el-col :span="1" :sm="2"  :lg="1" :md="2">
                            <template class="myText">
                               机构性质:
                            </template>
                        </el-col>
                        <el-col :span="23" :sm="22"  :lg="23" :md="22">
                            <el-checkbox-group
                                    v-model="threeMode.institutionNatureChecked"

                            >
                                <el-checkbox label="0">公立</el-checkbox>
                                <el-checkbox label="1">非公</el-checkbox>
                            </el-checkbox-group>
                        </el-col>
                    </el-row>

                    <template v-if="threeMode.institutionLevel!=null&&threeMode.institutionLevel.length>0">
                        <el-row>
                            <el-col :span="1" :sm="2"  :lg="1" :md="2">
                                <template class="myText">
                                    机构评级:
                                </template>
                            </el-col>
                            <el-col :span="23" :sm="22"  :lg="23" :md="22">
                                <el-checkbox-group v-model="threeMode.institutionLevelChecked" >
                                    <template v-for="item in threeMode.institutionLevel">
                                        <el-checkbox :label="item.label">{{item.value}}</el-checkbox>
                                    </template>
                                </el-checkbox-group>
                            </el-col>
                        </el-row>
                    </template>

                    <template v-if="threeMode.institutionType!=null&&threeMode.institutionType.length>0">
                        <el-row>
                            <el-col :span="1" :sm="2"  :lg="1" :md="2">
                                <template class="myText">
                                    机构类型:
                                </template>
                            </el-col>
                            <el-col :span="23" :sm="22"  :lg="23" :md="22">
                                <el-checkbox-group v-model="threeMode.institutionTypeChecked" >
                                    <template v-for="item in threeMode.institutionType">
                                        <el-checkbox :label="item.label">{{item.value}}</el-checkbox>
                                    </template>
                                    <template v-if="threeMode.traderCustomerMarketingType=='4'||threeMode.traderCustomerMarketingType=='5'">
                                        <el-checkbox label="100" @change="otherInstitutionTypeChange(threeMode)">其他</el-checkbox>
                                        <el-input  size="small" style="display: inline-block;width: 250px;margin-left: 25px;" v-model="threeMode.otherInstitutionType" :disabled="threeMode.otherInstitutionTypeDisabled" maxlength="20" placeholder="请输入其他机构类型"></el-input>
                                    </template>

                                </el-checkbox-group>
                            </el-col>
                        </el-row>

                    </template>

                    <template v-if="checkShowInstitutionTypeChild(threeMode)">
                        <el-row>
                            <el-col :span="1" :offset="2"  :lg="{span: 1, offset: 2}" :sm="{span: 2, offset: 2}" :md="{span: 2, offset: 2}">
                                <template class="myText">
                                    专科类型:
                                </template>
                            </el-col>
                            <el-col :span="21" :lg="21" :sm="20" :md="20">
                                <el-checkbox-group v-model="threeMode.institutionTypeChildChecked">
                                    <template v-for="item in getInstitutionTypeChild(threeMode)">
                                        <el-checkbox :label="item.label">{{item.value}}</el-checkbox>
                                    </template>

                                </el-checkbox-group>
                            </el-col>
                        </el-row>

                    </template>


                </div>
            </el-card>


        </template>



    </el-card>




    <el-card class="box-card" shadow="never">
        <div slot="header">
            <span style="font-size: 16px">经营商品（P）</span>

        </div>
        <template>
            <el-row>
                <el-col :span="1" :offset="1" :sm="2" :lg="1" :md="2"><el-link :underline="false">商品类型:</el-link></el-col>
                <el-col :span="22" :sm="21" :lg="22" :md="21">
                    <el-checkbox-group  v-model="goodsData.skuTypeChecked">
                        <el-checkbox label="0">设备</el-checkbox>
                        <el-checkbox label="1">高值耗材</el-checkbox>
                        <el-checkbox label="2">中低值耗材</el-checkbox>
                        <el-checkbox label="3">试剂</el-checkbox>
                        <el-checkbox label="4">软件</el-checkbox>
                    </el-checkbox-group>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="1" :offset="1" :sm="2" :lg="1" :md="2"><el-link :underline="false">商品范畴:</el-link></el-col>
                <el-col :span="22" :sm="21" :lg="22" :md="21">
                    <template>
                        <el-radio-group v-model="goodsData.skuScope" @input="skuScopeChange">
                            <el-radio  label="0">综合</el-radio>
                            <el-radio  label="1">专业</el-radio>
                        </el-radio-group>

                    </template>
                </el-col>
            </el-row>
            <el-row v-if="goodsData.skuScope==1">
                <el-col :span="1" :offset="2" :sm="2" :lg="1" :md="2"><el-link :underline="false">商品分类:</el-link></el-col>
                <el-col :span="21" :sm="20" :lg="21" :md="20">
                    <template>
                        <el-cascader
                                size="small"
                                v-model="goodsData.skuCategoryChecked"
                                :options="options"
                                :props="props"
                                collapse-tags
                                ref="skuCategoryCascader"
                                @change="skuCategoryChange"
                                clearable>

                        </el-cascader>
                    </template>
                </el-col>
            </el-row>
            <el-row v-if="goodsData.skuScope==1">
                <el-col :span="1" :offset="2" :sm="2" :lg="1" :md="2"><el-link :underline="false">&nbsp;</el-link></el-col>
                <el-col :span="21" :sm="20" :lg="21" :md="20">
                    <template v-for="item in skuCategoryCheckedPage">
                        <el-tag size="small">{{item.name}}</el-tag>
                    </template>

                </el-col>
            </el-row>



        </template>



    </el-card>

    <el-card class="box-card" shadow="never">
        <div slot="header">
            <span style="font-size: 16px">经营模式（M）</span>

        </div>
        <template>
            <el-row>
                <el-col :span="1" :offset="1" :sm="2" :lg="1" :md="2"><el-link :underline="false">销售类别:</el-link></el-col>
                <el-col :span="22" :sm="21" :lg="22" :md="21">
                    <template>
                        <el-radio-group v-model="goodsData.salesType">
                            <el-radio  label="0">直销为主</el-radio>
                            <el-radio  label="1">分销为主</el-radio>
                            <el-radio  label="2">直分销并重</el-radio>
                        </el-radio-group>

                    </template>
                </el-col>
            </el-row>
            <el-row>
                <el-col :span="1" :offset="1" :sm="2" :lg="1" :md="2"><el-link :underline="false">核心资源:</el-link></el-col>
            </el-row>

            <div style="margin-left: 136px;margin-top: -33px">
                <el-card class="box-card" shadow="never" >
                    <div slot="header">
                        <el-checkbox-group v-model="goodsData.isProductCustomer" @change="isProductCustomer(goodsData.isProductCustomer)">
                            <el-checkbox label="true">产品型渠道商 (以某一类产品或某一品牌为核心驱动业务的渠道商)</el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <template v-if="goodsData.isProductCustomer == true">
                        <el-row>
                            <template>
                                <el-radio-group v-model="goodsData.customerHasAgency" @change="isShowSkuAndBrand(goodsData.customerHasAgency)">
                                    <el-radio  label="1" >有代理商品或品牌</el-radio>
                                    <el-radio  label="0" >无代理商品或品牌</el-radio>
                                </el-radio-group>
                            </template>
                        </el-row>

                        <div v-show="goodsData.showCustomerAgency">
                            <el-row>
                                <el-col :span="1.5" :sm="1.5" :lg="1.5" :md="1.5"><span style="margin-right: 10px">代理品牌</span></el-col>
                                <el-col :span="5" :sm="6" :lg="4" :md="6">
                                    <template>
                                        <el-select
                                                v-model="goodsData.agencyBrandChecked"
                                                multiple
                                                filterable
                                                remote
                                                clearable
                                                size="small"
                                                reserve-keyword
                                                style="width: 250px"
                                                placeholder="添加品牌，请输入至少两个关键字"
                                                :remote-method="remoteAgencyBrand"
                                                :loading="loading">
                                            <el-option
                                                    v-for="item in brands"
                                                    :key="item.brandId"
                                                    :label="item.brandName"
                                                    :value="item.brandId">
                                            </el-option>
                                        </el-select>
                                    </template>
                                </el-col>
                                <el-col :span="1" offset="2" :sm="2" :lg="1" :md="2">
                                    <el-checkbox label="0" @change="otherAgencyBrandChange(goodsData.otherAgencyBrandShow)" v-model="goodsData.otherAgencyBrandShow">其他</el-checkbox>
                                </el-col>
                                <el-col :span="10" :sm="10" :lg="10" :md="10">
                                    <el-input size="small"  style="display: inline-block;width: 250px;margin-left: 20px;" v-model="goodsData.otherAgencyBrand" :disabled="goodsData.otherAgencyBrandDisabled" maxlength="20" placeholder="请输入其他代理品牌"></el-input>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="1.5" :sm="1.5" :lg="1.5" :md="1.5"><span style="margin-right: 10px">代理商品</span></el-col>
                                <el-col :span="5" :sm="6" :lg="4" :md="6">
                                    <template>
                                        <el-cascader
                                                style="width: 250px"
                                                size="small"
                                                v-model="goodsData.agencySkuChecked"
                                                :options="options"
                                                :props="props"
                                                collapse-tags
                                                ref="agencySkuCascader"
                                                @change="agencySkuChange"
                                                clearable>
                                        </el-cascader>
                                    </template>
                                </el-col>
                                <el-col :span="1" offset="2" :sm="2" :lg="1" :md="2">
                                    <el-checkbox label="0" @change="otherAgencySkuChange(goodsData.otherAgencySkuShow)" v-model="goodsData.otherAgencySkuShow">其他</el-checkbox>
                                </el-col>
                                <el-col :span="10" :sm="10" :lg="10" :md="10">
                                    <el-input size="small"  style="display: inline-block;width: 250px;margin-left: 20px;" v-model="goodsData.otherAgencySku" :disabled="goodsData.otherAgencySkuDisabled" maxlength="20" placeholder="请输入其他代理商品"></el-input>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="21" :offset="2" :sm="20" :lg="21" :md="20">
                                    <template v-for="item in agencySkuCheckedPage">
                                        <el-tag size="small">{{item.name}}</el-tag>
                                    </template>
                                </el-col>
                            </el-row>
                        </div>


                    </template>

                </el-card>
            </div>



            <div style="margin-left: 136px;">
                <el-card class="box-card" shadow="never" >
                    <div slot="header">
                        <el-checkbox-group v-model="goodsData.isChannelCustomer" @change="isChannelCustomer(goodsData.isChannelCustomer)">
                            <el-checkbox label="true">客户型渠道商 (以满足客户多元化需求为核心驱动业务的渠道商)</el-checkbox>
                        </el-checkbox-group>
                    </div>
                    <template v-if="goodsData.isChannelCustomer == true">
                        <el-row>
                            <el-col :span="22" :sm="21" :lg="22" :md="21">
                                <el-checkbox-group  v-model="goodsData.governmentRelationChecked">
                                    <el-checkbox label="3">基层医疗关系</el-checkbox>
                                    <el-checkbox label="4">等级医院关系</el-checkbox>
                                    <el-checkbox label="5">应急医疗机构关系</el-checkbox>
                                    <el-checkbox label="6">专业公共卫生机构关系</el-checkbox>
                                    <el-checkbox label="0">卫健委关系</el-checkbox>
                                    <el-checkbox label="1">医联体关系</el-checkbox>
                                    <el-checkbox label="2" @change="otherGovernmentRelationShowChange()">其他关系</el-checkbox>
                                    <el-input  size="small" style="display: inline-block;width: 250px;margin-left: 25px;" v-model="goodsData.otherGovernmentRelation" :disabled="otherGovernmentRelationShow" maxlength="20" placeholder="请输入其他政府关系"></el-input>
                                </el-checkbox-group>
                            </el-col>

                        </el-row>

                    </template>

                </el-card>
            </div>




        </template>



    </el-card>



</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerDealer.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/goods/category/category.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/goods/brand/brand.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTerminal.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    const viewInfo = {
        traderCustomerId: '${traderCustomerId}',
        traderId:''
    }
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                dialogFormVisible:false,
                form: {
                    terminalData:[],
                    terminalChecked:[],
                    terminalCheckedData:[],
                    errorShow:true
                },
                formLabelWidth: '120px',
                loading:false,
                threeModeList: [],
                otherGovernmentRelationShow:true,
                goodsData:{
                    skuTypeChecked:[],
                    skuScope:'',
                    skuCategoryChecked:[],
                    salesType:'',
                    agencyBrandChecked:[],
                    agencySkuChecked:[],
                    governmentRelationChecked:[],
                    otherAgencyBrandDisabled:true,
                    otherAgencySkuDisabled:true,
                    otherAgencyBrand:'',
                    otherAgencySku:'',
                    otherAgencyBrandShow:[],
                    otherAgencySkuShow:[],
                    terminalDataChecked:[],
                    otherGovernmentRelation:'',
                    customerHasAgency:'1',
                    showCustomerAgency:false,
                    isProductCustomer:false,
                    isChannelCustomer:false
                },
                brands:[],
                props: {
                    multiple: true ,
                    value: 'baseCategoryId',
                    label: 'baseCategoryName'
                },
                options: [],
                skuCategoryCheckedPage:[],
                agencySkuCheckedPage:[]
            };
        },
        created() {
            this.initData();

        },
        mounted() {
            loadingApp()
            sendThis(this);

        },


        methods: {
            async initData() {
                 getAllCategory().then(res=>{
                    this.options =res.data.data;
                })

                await queryTraderCustomerMarketingNode({'type': 6, 'isSort': true}).then(res => {
                    var threeList = [];
                    res.data.data.forEach(function (item) {
                        threeList.push({
                            ...item,
                            traderCustomerMarketingType: item.threeCustomerType.label,
                            institutionNatureChecked: [],
                            institutionLevelChecked: [],
                            institutionTypeChecked: [],
                            institutionTypeChildChecked: [],
                            otherInstitutionType:'',
                            otherInstitutionTypeDisabled:true,
                            show: false
                        })
                    });
                    this.threeModeList = threeList;
                })

                if (viewInfo.traderCustomerId != '') {
                    let that = this;
                    await queryTraderCustomerFrontData({traderCustomerId:viewInfo.traderCustomerId}).then(res=>{
                        let traderDealerFrontDto = res.data.data;
                        viewInfo.traderId = traderDealerFrontDto.traderId;
                        if (traderDealerFrontDto.principal != null&&traderDealerFrontDto.principal != undefined) {
                            let principal = traderDealerFrontDto.principal;
                            that.goodsData.skuTypeChecked = principal.skuTypeChecked;
                            that.goodsData.skuScope = principal.skuScope;
                            that.goodsData.skuCategoryChecked = principal.skuCategoryChecked;
                            that.goodsData.agencySkuChecked = principal.agencySkuChecked;
                            that.goodsData.salesType = principal.salesType;
                            that.goodsData.agencyBrandChecked = principal.agencyBrandChecked;
                            that.goodsData.governmentRelationChecked = principal.governmentRelationChecked;
                            that.goodsData.otherAgencyBrandDisabled = principal.otherAgencyBrand == '' || principal.otherAgencyBrand == undefined;
                            that.goodsData.otherAgencyBrand = principal.otherAgencyBrand;
                            that.goodsData.otherAgencyBrandShow = principal.otherAgencyBrand != '' && principal.otherAgencyBrand != undefined ? ['0'] : [];
                            that.goodsData.otherGovernmentRelation = principal.otherGovernmentRelation;
                            that.otherGovernmentRelationShow =  principal.otherGovernmentRelation == '' || principal.otherGovernmentRelation == undefined ;

                            that.goodsData.otherAgencySkuDisabled = principal.otherAgencySku == '' || principal.otherAgencySku == undefined;
                            that.goodsData.otherAgencySku = principal.otherAgencySku;
                            that.goodsData.otherAgencySkuShow = principal.otherAgencySku != '' && principal.otherAgencySku != undefined ? ['0'] : [];
                            that.goodsData.isProductCustomer=principal.isProductCustomer;
                            that.goodsData.isChannelCustomer=principal.isChannelCustomer;
                            //是否展示【代理商品或品牌】
                            debugger;
                            if(principal.isProductCustomer == true){
                                if(_.isEmpty(principal.agencyBrandChecked) && _.isEmpty(principal.agencySkuChecked) && _.isEmpty(principal.otherAgencySku) && _.isEmpty(principal.otherAgencyBrand)){
                                    that.goodsData.customerHasAgency='0';
                                    that.goodsData.showCustomerAgency=false;
                                }else{
                                    that.goodsData.customerHasAgency='1';
                                    that.goodsData.showCustomerAgency=true;
                                }
                            }else{
                                that.goodsData.customerHasAgency='2';
                                that.goodsData.showCustomerAgency=false;
                            }
                        }

                        if (traderDealerFrontDto.terminal != null && traderDealerFrontDto.terminal != undefined) {
                            let terminal = traderDealerFrontDto.terminal;
                            if (terminal != null && terminal != undefined && terminal.length > 0) {
                                that.goodsData.terminalDataChecked = terminal[0].terminalDataChecked;
                                that.threeModeList.forEach(x=>{
                                    let find = terminal.find(a => a.traderCustomerMarketingType == x.traderCustomerMarketingType);
                                    if (find != null && find != undefined) {
                                        x.institutionNatureChecked = find.institutionNatureChecked;
                                        x.institutionLevelChecked= find.institutionLevelChecked;
                                        x.institutionTypeChecked=find.institutionTypeChecked;
                                        x.institutionTypeChildChecked = find.institutionTypeChildChecked;
                                        x.otherInstitutionType = find.otherInstitutionType;
                                        x.otherInstitutionTypeDisabled = !find.institutionTypeChecked.includes("100");
                                        x.show = find.institutionNatureChecked.length>0||find.institutionLevelChecked.length>0||find.institutionTypeChecked.length>0||find.institutionTypeChildChecked.length>0||find.otherInstitutionType!='';
                                    }

                                })
                            }
                        }
                    })
                    if (that.goodsData.agencyBrandChecked.length > 0) {
                        getBrand(that.goodsData.agencyBrandChecked).then(res=>{
                            that.brands = res.data.data
                        })
                    }
                    // this.skuCategoryChange();
                    this.agencySkuChange();
                }


            },
            isShowSkuAndBrand(data){
                this.goodsData.showCustomerAgency=false;
                if(data>0){
                    this.goodsData.showCustomerAgency=true;
                }
            },

            isProductCustomer(data){
                debugger;
                this.goodsData.showCustomerAgency=false;
                this.goodsData.customerHasAgency = '0';
                if(data>0){
                    this.goodsData.showCustomerAgency=true;
                    this.goodsData.customerHasAgency = '1';
                }
            },
            terminalCheckedClear() {
                this.dialogFormVisible = false;
                this.form.terminalChecked = [];
                this.form.terminalCheckedData = [];
                this.form.errorShow = true;
            },
            doAddTerminalTag(data) {
                this.threeModeList.forEach(function (x){

                    if (x.traderCustomerMarketingType == data.traderCustomerMarketingType) {
                        if (data.institutionNature != ''&&!x.institutionNatureChecked.includes(data.institutionNature)) {
                            x.institutionNatureChecked.push(data.institutionNature);
                        }
                        if (data.institutionType != ''&&!x.institutionTypeChecked.includes(data.institutionType)) {
                            x.institutionTypeChecked.push(data.institutionType);
                        }
                        if (data.institutionLevel != ''&&!x.institutionLevelChecked.includes(data.institutionLevel)) {
                            x.institutionLevelChecked.push(data.institutionLevel);
                        }
                        if (data.institutionTypeChild != ''&&!x.institutionTypeChildChecked.includes(data.institutionTypeChild)) {
                            x.institutionTypeChildChecked.push(data.institutionTypeChild);
                        }

                    }


                })
            },
            otherGovernmentRelationShowChange() {
                if (!this.goodsData.governmentRelationChecked.includes("2")) {
                    this.goodsData.otherGovernmentRelation = '';
                    this.otherGovernmentRelationShow = true;
                } else {
                    this.otherGovernmentRelationShow = false;
                }
            },
            doTerminalChecked() {

                let that = this;
                if (that.form.terminalChecked.length > 0) {

                    that.form.terminalChecked.forEach(function (i){
                        if(!that.goodsData.terminalDataChecked.includes(i)) {
                            that.goodsData.terminalDataChecked.push(i);
                        }
                    })
                };
                if (that.form.terminalCheckedData.length > 0) {
                    that.form.terminalCheckedData.forEach(function (i){
                        that.doAddTerminalTag(i)
                    })
                }
                that.dialogFormVisible = !that.dialogFormVisible;
                that.terminalCheckedClear()
            },


            terminalCheckedChange(data) {
                var array = []
                let ts = this.form.terminalData.filter(function (i){
                    return data.includes(i.uniqueId);
                });
                if (ts != null && ts != undefined && ts.length > 0) {
                    ts.forEach(function (a){
                        array.push(a);
                    })
                }
                this.form.terminalCheckedData = array;

            },
            switchChange(data){
                if (!data.show) {
                    // data.institutionNatureChecked=[];
                    // data.institutionLevelChecked=[];
                    // data.institutionTypeChecked=[];
                    // data.institutionTypeChildChecked=[];
                    data.otherInstitutionTypeDisabled=true;
                    // data.otherInstitutionType='';
                }

            },
            checkShowInstitutionTypeChild(data) {
                var value = data.institutionTypeChecked;
                let flag = value.includes("1");
                if (!flag) {
                    data.institutionTypeChildChecked = [];
                }
                return flag;

            },
            getInstitutionTypeChild(data) {
                let filter = data.institutionType.find(function (item){
                    return item.label=='1';
                });
                if (filter.children != null && filter.children != undefined && filter.children.length > 0) {
                    return filter.children;
                }
                return [];
            },
            otherInstitutionTypeChange(data) {
                var value = data.institutionTypeChecked;

                if (!value.includes("100")) {
                    data.otherInstitutionType = '';

                }
                data.otherInstitutionTypeDisabled = !data.otherInstitutionTypeDisabled;
            },
            checkOtherAgencyBrand() {
                debugger;
                let that = this;
                let flag = false;
                flag = that.goodsData.otherAgencyBrandShow.includes("0") && that.goodsData.otherAgencyBrand == '';
                if (flag) {
                    that.$message.error({
                        message: "代理品牌勾选了其他，文本框必填"
                    });
                }
                return flag;
            },
            checkOtherAgencySku() {
                debugger;
                let that = this;
                let flag = false;
                console.log(that.goodsData.otherAgencySku)
                flag = that.goodsData.otherAgencySkuShow.includes("0") && that.goodsData.otherAgencySku == '';
                console.log(flag)
                if (flag) {
                    that.$message.error({
                        message: "代理商品勾选了其他，文本框必填"
                    });
                }
                return flag;
            },checkOtherGovernmentRelation() {
                let that = this;
                let flag = false;
                flag = that.goodsData.governmentRelationChecked.includes("2") && that.goodsData.otherGovernmentRelation == '';
                if (flag) {
                    that.$message.error({
                        message: "政府关系勾选了其他，文本框必填"
                    });
                }
                return flag;
            },
            checkOtherInstitutionType() {
                let that = this;
                let flag = false;

                for (let i = 0; i < this.threeModeList.length; i++) {
                    let threeModeListElement = that.threeModeList[i];
                    flag =threeModeListElement.institutionTypeChecked.includes("100") && threeModeListElement.otherInstitutionType == '';
                    if (flag) {
                        that.$message.error({
                            message: threeModeListElement.threeCustomerType.value+"的机构类型勾选了其他，文本框必填"
                        });
                        break;
                    }
                }
                return flag;
            },
            skuScopeChange() {
                this.goodsData.skuCategoryChecked=[];
                this.skuCategoryCheckedPage=[];
            },
            remoteAgencyBrand(query) {
                if (query.length < 2) {
                } else {
                    queryBrand({'brandName':query}).then(res=>{
                        this.brands = res.data.data;
                    })
                }


            },
            otherAgencyBrandChange(data) {
                if (!data.includes("0")) {
                    this.goodsData.otherAgencyBrand = '';
                }
                this.goodsData.otherAgencyBrandDisabled = !this.goodsData.otherAgencyBrandDisabled;
            },

            otherAgencySkuChange(data) {
                if (!data.includes("0")) {
                    this.goodsData.otherAgencySku = '';
                }
                this.goodsData.otherAgencySkuDisabled = !this.goodsData.otherAgencySkuDisabled;
            },

            // 清除所有
            changeClearAll(){
                if (this.threeModeList.length>0) {
                    this.threeModeList.forEach(x=>{
                        x.institutionNatureChecked = [];
                        x.institutionTypeChecked = [];
                        x.institutionTypeChildChecked = [];
                        x.institutionLevelChecked = [];
                        x.otherInstitutionType = '';
                        x.otherInstitutionTypeDisabled = true;
                        x.show = false;
                    })
                }
                this.goodsData.skuTypeChecked = [];
                this.goodsData.skuScope = '';
                this.goodsData.skuCategoryChecked = [];
                this.goodsData.agencySkuChecked = [];
                this.goodsData.salesType = '';
                this.goodsData.agencyBrandChecked = [];
                this.goodsData.governmentRelationChecked = [];
                this.goodsData.otherAgencyBrandDisabled = true;
                this.goodsData.otherAgencyBrand = '';
                this.goodsData.otherAgencyBrandShow = [];

                this.goodsData.otherAgencySkuDisabled = true;
                this.goodsData.otherAgencySku = '';
                this.goodsData.otherAgencySkuShow = [];

                this.goodsData.terminalDataChecked = [];
                this.goodsData.otherGovernmentRelation = '';
            },
            // 返回数据给老页面
            getTheDataOfThePage() {
                let terminalDataChecked = this.goodsData.terminalDataChecked;
                let arrays = [];
                if (this.threeModeList.length>0) {
                    this.threeModeList.forEach(x=>{
                        if (
                            (x.institutionNatureChecked==null||x.institutionNatureChecked.length==0)&&
                            (x.institutionTypeChecked==null|| x.institutionTypeChecked.length==0)&&
                            (x.institutionTypeChildChecked==null||x.institutionTypeChildChecked.length==0)&&
                            (x.institutionLevelChecked==null||x.institutionLevelChecked.length==0)&&
                            (x.otherInstitutionType==null||x.otherInstitutionType=='')
                        ) {
                            return;
                        }
                        arrays.push({
                            traderCustomerMarketingType:x.traderCustomerMarketingType,
                            institutionNatureChecked:x.institutionNatureChecked,
                            institutionTypeChecked: x.institutionTypeChecked,
                            institutionTypeChildChecked: x.institutionTypeChildChecked,
                            institutionLevelChecked:x.institutionLevelChecked,
                            otherInstitutionType:x.otherInstitutionType,
                            terminalDataChecked:terminalDataChecked
                        });
                    });
                }

                let frontGoodsData = {
                    principal: this.goodsData,
                    terminal: arrays
                };
                return JSON.stringify(frontGoodsData);

            },
            skuCategoryChange() {
                const checkedNodes = this.$refs.skuCategoryCascader.getCheckedNodes();
                this.skuCategoryCheckedPage=[];
                if (checkedNodes.length > 0) {
                    // 过滤所有的1级节点
                    let one = checkedNodes.filter(x=>{
                        return x.level === 1;
                    });
                    if (one != null && one.length > 0) {
                        one.forEach(a=>{
                            this.skuCategoryCheckedPage.push({
                                name:a.label
                            });
                        });
                    }
                    let two = checkedNodes.filter(x=>{
                        return x.level === 2&&!x.parent.checked;
                    });
                    if (two != null && two.length > 0) {
                        two.forEach(a=>{
                            this.skuCategoryCheckedPage.push({
                                name:a.parent.label+'/'+a.label
                            });
                        });
                    }

                    let three = checkedNodes.filter(x=>{
                        return x.level === 3 && !x.parent.checked;
                    });
                    if (three != null && three.length > 0) {
                        three.forEach(a=>{
                            this.skuCategoryCheckedPage.push({
                                name:a.parent.parent.label+'/'+a.parent.label+'/'+a.label
                            });
                        });
                    }
                }
            },

            agencySkuChange() {
                const checkedNodes = this.$refs.agencySkuCascader.getCheckedNodes();
                this.agencySkuCheckedPage = [];
                if (checkedNodes.length > 0) {
                    // 过滤所有的1级节点
                    let one = checkedNodes.filter(x=>{
                        return x.level === 1;
                    });
                    if (one != null && one.length > 0) {
                        one.forEach(a=>{
                            this.agencySkuCheckedPage.push({
                                name:a.label
                            });
                        });
                    }
                    let two = checkedNodes.filter(x=>{
                        return x.level === 2&&!x.parent.checked;
                    });
                    if (two != null && two.length > 0) {
                        two.forEach(a=>{
                            this.agencySkuCheckedPage.push({
                                name:a.parent.label+'/'+a.label
                            });
                        });
                    }

                    let three = checkedNodes.filter(x=>{
                        return x.level === 3 && !x.parent.checked;
                    });
                    if (three != null && three.length > 0) {
                        three.forEach(a=>{
                            this.agencySkuCheckedPage.push({
                                name:a.parent.parent.label+'/'+a.parent.label+'/'+a.label
                            });
                        });
                    }
                }
            }

        },
    });
</script>
<style>

    .el-card{
        margin-left: 10px;
        margin-right: 10px;
    }

    .el-descriptions__header {
        background-color: #f2f2f2;
        margin-bottom: 0px;
        height: 35px;
    }

    .el-card .el-card__header {
        padding: 7px 8px;
        /*background-color: #f2f2f2;*/
        font-size: 12px;
        height: 35px;
        font-weight: 700;
    }




    .row-expand-cover td .el-table__expand-icon{ visibility: hidden !important; }
</style>

<style scoped>
    .clearfix::after {
        display: none;
        clear: both;
        content: "";
    }
    .clearfix span {
        float: left;
    }
    .clearfix .el-switch {
        float: right;
    }
    .el-checkbox-group {
        margin-top: 3px;
    }
    .el-row {
        margin-bottom: 15px;
    }
    .error-border {
        border-color: red;
    }
    .el-link--inner {
        font-size: 12px;
    }

    .el-checkbox-group{
        margin-top: -1px;
    }

</style>