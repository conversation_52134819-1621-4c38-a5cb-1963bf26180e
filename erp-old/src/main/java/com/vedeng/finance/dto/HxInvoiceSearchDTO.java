package com.vedeng.finance.dto;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date created in 2020/5/19 10:48
 */
public class HxInvoiceSearchDTO {

    private String keyword;

    /**
     * 发票状态，0全部，1待录票，2审核中，3已审核，4待认领，5费用票，6异常票，7负数票，8其他
     */
    private Integer invoiceStatus;

    /**
     * 时间筛选项，1开票时间，2录票时间，3审核时间，4认证时间
     */
    private Integer timeSearchType;

    private Long startTime;

    private Long endTime;

    /**
     * 发票总额起始值
     */
    private BigDecimal invoiceAmountFrom;

    /**
     * 发票总额截止值
     */
    private BigDecimal invoiceAmountTo;

    /**
     * 发票票种，0全部，1：13%增值税普通发票，2：13%增值税专用发票，3：16%增值税普通发票，4：16%增值税专用发票，5：6%增值税普通发票，6：6%增值税专用发票，7：3%增值税普通发票，8：3%增值税专用发票，9：0%增值税普通发票
     */
    private Integer invoiceTaxRate;

    /**
     * 1蓝字有效，2红字有效，3蓝字作废
     */
    private Integer colorType;

    /**
     * 发票录票人
     */
    private Integer entryUser;

    /**
     * 审核人
     */
    private Integer validUser;

    /**
     * 审核状态,0全部，1审核通过，2审核不通过
     */
    private Integer validStatus;

    /**
     * 认证状态，0全部，1已认证，2未认证，3认证失败
     */
    private Integer authStatus;

    /**
     * 是否当月认证，0全部，1当月认证，2非当月认证
     */
    private Integer authMonth;

    /**
     * 发送结果，0全部，1是，2否
     */
    private Integer sendResult;

    /**
     * 发票对应订单的归属采购
     */
    private Integer userId;

    /**
     * 发票退票状态，0待退票，1已退票
     */
    private Integer invoiceRefundStatus;

    /**
     * 销方名称
     */
    private String salerName;

    /**
     * 发票号码
     */
    private String invoiceNum;

    /**
     * 订单号
     */
    private String saleorderNo;

    /**
     * 发票种类：1专票，0普票
     */
    private String invoiceCategory;

    /**
     * 备注
     */
    private String comment;

    /**
     * 是否正在录票
     */
    private Integer isRecording;

    /**
     * 认证方式
     */
    private Integer authMode;

    public Integer getAuthMode() {
        return authMode;
    }

    public void setAuthMode(Integer authMode) {
        this.authMode = authMode;
    }

    public Integer getIsRecording() {
        return isRecording;
    }

    public void setIsRecording(Integer isRecording) {
        this.isRecording = isRecording;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getInvoiceCategory() {
        return invoiceCategory;
    }

    public void setInvoiceCategory(String invoiceCategory) {
        this.invoiceCategory = invoiceCategory;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getInvoiceStatus() {
        return invoiceStatus;
    }

    public void setInvoiceStatus(Integer invoiceStatus) {
        this.invoiceStatus = invoiceStatus;
    }

    public Integer getTimeSearchType() {
        return timeSearchType;
    }

    public void setTimeSearchType(Integer timeSearchType) {
        this.timeSearchType = timeSearchType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public BigDecimal getInvoiceAmountFrom() {
        return invoiceAmountFrom;
    }

    public void setInvoiceAmountFrom(BigDecimal invoiceAmountFrom) {
        this.invoiceAmountFrom = invoiceAmountFrom;
    }

    public BigDecimal getInvoiceAmountTo() {
        return invoiceAmountTo;
    }

    public void setInvoiceAmountTo(BigDecimal invoiceAmountTo) {
        this.invoiceAmountTo = invoiceAmountTo;
    }

    public Integer getInvoiceTaxRate() {
        return invoiceTaxRate;
    }

    public void setInvoiceTaxRate(Integer invoiceTaxRate) {
        this.invoiceTaxRate = invoiceTaxRate;
    }

    public Integer getColorType() {
        return colorType;
    }

    public void setColorType(Integer colorType) {
        this.colorType = colorType;
    }

    public Integer getEntryUser() {
        return entryUser;
    }

    public void setEntryUser(Integer entryUser) {
        this.entryUser = entryUser;
    }

    public Integer getValidUser() {
        return validUser;
    }

    public void setValidUser(Integer validUser) {
        this.validUser = validUser;
    }

    public Integer getValidStatus() {
        return validStatus;
    }

    public void setValidStatus(Integer validStatus) {
        this.validStatus = validStatus;
    }

    public Integer getAuthStatus() {
        return authStatus;
    }

    public void setAuthStatus(Integer authStatus) {
        this.authStatus = authStatus;
    }

    public Integer getAuthMonth() {
        return authMonth;
    }

    public void setAuthMonth(Integer authMonth) {
        this.authMonth = authMonth;
    }

    public Integer getSendResult() {
        return sendResult;
    }

    public void setSendResult(Integer sendResult) {
        this.sendResult = sendResult;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getInvoiceRefundStatus() {
        return invoiceRefundStatus;
    }

    public void setInvoiceRefundStatus(Integer invoiceRefundStatus) {
        this.invoiceRefundStatus = invoiceRefundStatus;
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    public String getSalerName() {
        return salerName;
    }

    public void setSalerName(String salerName) {
        this.salerName = salerName;
    }

    public String getSaleorderNo() {
        return saleorderNo;
    }

    public void setSaleorderNo(String saleorderNo) {
        this.saleorderNo = saleorderNo;
    }

    @Override
    public String toString() {
        return "HxInvoiceSearchDTO{" +
                "keyword='" + keyword + '\'' +
                ", invoiceStatus=" + invoiceStatus +
                ", timeSearchType=" + timeSearchType +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", invoiceAmountFrom=" + invoiceAmountFrom +
                ", invoiceAmountTo=" + invoiceAmountTo +
                ", invoiceTaxRate=" + invoiceTaxRate +
                ", colorType=" + colorType +
                ", entryUser=" + entryUser +
                ", validUser=" + validUser +
                ", validStatus=" + validStatus +
                ", authStatus=" + authStatus +
                ", authMonth=" + authMonth +
                ", sendResult=" + sendResult +
                ", userId=" + userId +
                ", invoiceRefundStatus=" + invoiceRefundStatus +
                ", salerName='" + salerName + '\'' +
                ", invoiceNum='" + invoiceNum + '\'' +
                ", saleorderNo='" + saleorderNo + '\'' +
                '}';
    }
}
