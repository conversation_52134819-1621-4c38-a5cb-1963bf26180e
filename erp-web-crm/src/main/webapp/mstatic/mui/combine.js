
// merge-js.js
const fs = require('fs');
const path = require('path');

// 输出文件
const outputJs = './mui.js';
const outputCss = './mui.css';
const jsDir = './src-js';
const cssDir = './src-css';
let content = '';

let combineDirJs = function(jsDir){
  // 读取文件并合并内容
  let files = fs.readdirSync(jsDir);

  files.forEach(file => {
    let stat = fs.statSync(path.join(jsDir, file));

    if(stat.isDirectory()){
      combineDirJs(path.join(jsDir, file));
    } else {
      if (path.extname(file) === '.js') {
        let data = fs.readFileSync(path.join(jsDir, file), 'utf8');
        content += data + '\n';
        fs.writeFileSync(outputJs, content, 'utf8');
      }
    }
  });
}

console.log('监听文件变化中。。。')

fs.watch(jsDir, { recursive: true }, (eventType, filename) => {
  content = '';
  combineDirJs(jsDir);
  console.log('Merged js' + new Date().getTime());
});

fs.watch(cssDir, { recursive: true }, (eventType, filename) => {
  // 读取文件并合并内容
  fs.readdir(cssDir, (err, files) => {
    if (err) throw err;
    let content = '';
 
    files.forEach(file => {
      if (path.extname(file) === '.css') {
        fs.readFile(path.join(cssDir, file), 'utf8', (err, data) => {
          if (err) throw err;
          content += data + '\n';
          fs.writeFile(outputCss, content, 'utf8', (err) => {
            if (err) throw err;
          });
        });
      }
    });

    console.log('Merged css' + new Date().getTime());
  });
});
