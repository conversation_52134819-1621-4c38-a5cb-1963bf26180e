<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.OrderGoodsLowerPriceMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice">
    <!--@mbg.generated-->
    <!--@Table T_ORDER_GOODS_LOWER_PRICE-->
    <id column="GOODS_LOWER_PRICE_ID" jdbcType="BIGINT" property="goodsLowerPriceId" />
    <result column="ORDER_ID" jdbcType="INTEGER" property="orderId" />
    <result column="ORDER_NO" jdbcType="VARCHAR" property="orderNo" />
    <result column="ORDER_TYPE" jdbcType="INTEGER" property="orderType" />
    <result column="ORDER_GOODS_ID" jdbcType="INTEGER" property="orderGoodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="PRICE" jdbcType="DECIMAL" property="price" />
    <result column="CHECK_PRICE" jdbcType="DECIMAL" property="checkPrice" />
    <result column="ORDER_ADD_TIME" jdbcType="TIMESTAMP" property="orderAddTime" />
    <result column="APPROVAL_OPINION" jdbcType="VARCHAR" property="approvalOpinion" />
    <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    GOODS_LOWER_PRICE_ID, ORDER_ID, ORDER_NO, ORDER_TYPE, ORDER_GOODS_ID, SKU, NUM, PRICE, 
    CHECK_PRICE, ORDER_ADD_TIME, APPROVAL_OPINION, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, 
    CREATOR_NAME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_ORDER_GOODS_LOWER_PRICE
    where GOODS_LOWER_PRICE_ID = #{goodsLowerPriceId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_ORDER_GOODS_LOWER_PRICE
    where GOODS_LOWER_PRICE_ID = #{goodsLowerPriceId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="GOODS_LOWER_PRICE_ID" keyProperty="goodsLowerPriceId" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_GOODS_LOWER_PRICE (ORDER_ID, ORDER_NO, ORDER_TYPE, 
      ORDER_GOODS_ID, SKU, NUM, 
      PRICE, CHECK_PRICE, ORDER_ADD_TIME, 
      APPROVAL_OPINION, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, REMARK, 
      UPDATE_REMARK)
    values (#{orderId,jdbcType=INTEGER}, #{orderNo,jdbcType=VARCHAR}, #{orderType,jdbcType=INTEGER},
      #{orderGoodsId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, 
      #{price,jdbcType=DECIMAL}, #{checkPrice,jdbcType=DECIMAL}, #{orderAddTime,jdbcType=TIMESTAMP}, 
      #{approvalOpinion,jdbcType=VARCHAR}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="GOODS_LOWER_PRICE_ID" keyProperty="goodsLowerPriceId" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_GOODS_LOWER_PRICE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        ORDER_ID,
      </if>
      <if test="orderNo != null">
        ORDER_NO,
      </if>
      <if test="orderType != null">
        ORDER_TYPE,
      </if>
      <if test="orderGoodsId != null">
        ORDER_GOODS_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="price != null">
        PRICE,
      </if>
      <if test="checkPrice != null">
        CHECK_PRICE,
      </if>
      <if test="orderAddTime != null">
        ORDER_ADD_TIME,
      </if>
      <if test="approvalOpinion != null">
        APPROVAL_OPINION,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderGoodsId != null">
        #{orderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="checkPrice != null">
        #{checkPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderAddTime != null">
        #{orderAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalOpinion != null">
        #{approvalOpinion,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice">
    <!--@mbg.generated-->
    update T_ORDER_GOODS_LOWER_PRICE
    <set>
      <if test="orderId != null">
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null">
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="orderType != null">
        ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      </if>
      <if test="orderGoodsId != null">
        ORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="price != null">
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="checkPrice != null">
        CHECK_PRICE = #{checkPrice,jdbcType=DECIMAL},
      </if>
      <if test="orderAddTime != null">
        ORDER_ADD_TIME = #{orderAddTime,jdbcType=TIMESTAMP},
      </if>
      <if test="approvalOpinion != null">
        APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where GOODS_LOWER_PRICE_ID = #{goodsLowerPriceId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.domain.entity.OrderGoodsLowerPrice">
    <!--@mbg.generated-->
    update T_ORDER_GOODS_LOWER_PRICE
    set ORDER_ID = #{orderId,jdbcType=INTEGER},
      ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      ORDER_TYPE = #{orderType,jdbcType=INTEGER},
      ORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      PRICE = #{price,jdbcType=DECIMAL},
      CHECK_PRICE = #{checkPrice,jdbcType=DECIMAL},
      ORDER_ADD_TIME = #{orderAddTime,jdbcType=TIMESTAMP},
      APPROVAL_OPINION = #{approvalOpinion,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where GOODS_LOWER_PRICE_ID = #{goodsLowerPriceId,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" keyColumn="GOODS_LOWER_PRICE_ID" keyProperty="goodsLowerPriceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_ORDER_GOODS_LOWER_PRICE
    (ORDER_ID, ORDER_NO, ORDER_TYPE, ORDER_GOODS_ID, SKU, NUM, PRICE, CHECK_PRICE, ORDER_ADD_TIME, 
      APPROVAL_OPINION, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, 
      UPDATER_NAME, REMARK, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.orderId,jdbcType=INTEGER}, #{item.orderNo,jdbcType=VARCHAR}, #{item.orderType,jdbcType=INTEGER},
        #{item.orderGoodsId,jdbcType=INTEGER}, #{item.sku,jdbcType=VARCHAR}, #{item.num,jdbcType=INTEGER}, 
        #{item.price,jdbcType=DECIMAL}, #{item.checkPrice,jdbcType=DECIMAL}, #{item.orderAddTime,jdbcType=TIMESTAMP}, 
        #{item.approvalOpinion,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
        #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, #{item.creatorName,jdbcType=VARCHAR}, 
        #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="getLowerPriceGoodsList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_ORDER_GOODS_LOWER_PRICE
    where
        IS_DELETE = 0
    and ORDER_ID = #{orderId,jdbcType=INTEGER}
    and ORDER_TYPE = #{orderType,jdbcType=INTEGER}
    <if test="orderGoodsId != null">
       and ORDER_GOODS_ID = #{orderGoodsId,jdbcType=INTEGER}
    </if>

  </select>

  <update id="delByOrderIdAndType">
    update T_ORDER_GOODS_LOWER_PRICE set IS_DELETE = 1
    where IS_DELETE = 0 and ORDER_ID = #{orderId,jdbcType=INTEGER} and ORDER_TYPE = #{orderType,jdbcType=INTEGER}
  </update>

  <update id="addHandleOpinion">
    update T_ORDER_GOODS_LOWER_PRICE
    set HANDLE_OPINION=#{handleOpinion,jdbcType=VARCHAR}
    where HANDLE_OPINION is null
    and GOODS_LOWER_PRICE_ID = #{goodsLowerPriceId,jdbcType=INTEGER}
  </update>

  <update id="addComment">
    update T_ORDER_GOODS_LOWER_PRICE set APPROVAL_OPINION = #{comment,jdbcType=VARCHAR}
    where ORDER_ID = #{saleorderId,jdbcType=INTEGER}
    and ORDER_TYPE = 1
    and IS_DELETE = 0
  </update>

  <select id="getCheckPriceBySaleorderGoodsId" resultType="java.math.BigDecimal">
    select CHECK_PRICE
    from T_ORDER_GOODS_LOWER_PRICE
    where ORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
    and ORDER_TYPE = 1
    and IS_DELETE = 0
    limit 1
  </select>
</mapper>