package com.vedeng.erp.trader.domain.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 批量派发拜访计划
 */
@Getter
@Setter
public class VisitBatchDistributeDto {

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)
     */
    private Integer customerNature;

    /**
     * 客户来源方式 (1erp 2终端库 3天眼查)
     */
    private Integer customerFrom;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户所在地区-省CODE
     */
    private Integer provinceCode;

    /**
     * 客户所在地区-省名称
     */
    private String provinceName;

    /**
     * 客户所在地区-市CODE
     */
    private Integer cityCode;

    /**
     * 客户所在地区-市名称
     */
    private String cityName;

    /**
     * 拜访人
     */
    private Integer visitorId;

}