package com.vedeng.order.model;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Data
@Setter
@Getter
public class SaleOrderPriceDto {

    private int saleOrderPriceId;

    /**
     * 销售订单id
     */
    private Integer saleOrderId;

    /**
     * 优惠金额
     */
    private BigDecimal alterPrice;

    /**
     * 优惠类型：1随机立减
     */
    private int alterType;



}
