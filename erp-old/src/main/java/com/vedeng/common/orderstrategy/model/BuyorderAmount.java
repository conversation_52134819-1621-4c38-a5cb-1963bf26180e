package com.vedeng.common.orderstrategy.model;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @ClassName BuyorderAmount.java
 * @Description TODO 采购单金额
 * @createTime 2020年11月02日 16:38:00
 */
public class BuyorderAmount {
    private Integer buyorderId;
    //账期未还金额
    private BigDecimal noPaybackAmount;

    //信用支付金额
    private BigDecimal creditPayAmount;

    //信用还款
    private BigDecimal creditReturnAmount;

    //售后信用还款
    private BigDecimal creditAfterReturnAmount;

    public Integer getBuyorderId() {
        return buyorderId;
    }

    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    public BigDecimal getNoPaybackAmount() {
        return noPaybackAmount;
    }

    public void setNoPaybackAmount(BigDecimal noPaybackAmount) {
        this.noPaybackAmount = noPaybackAmount;
    }

    public BigDecimal getCreditPayAmount() {
        return creditPayAmount;
    }

    public void setCreditPayAmount(BigDecimal creditPayAmount) {
        this.creditPayAmount = creditPayAmount;
    }

    public BigDecimal getCreditReturnAmount() {
        return creditReturnAmount;
    }

    public void setCreditReturnAmount(BigDecimal creditReturnAmount) {
        this.creditReturnAmount = creditReturnAmount;
    }

    public BigDecimal getCreditAfterReturnAmount() {
        return creditAfterReturnAmount;
    }

    public void setCreditAfterReturnAmount(BigDecimal creditAfterReturnAmount) {
        this.creditAfterReturnAmount = creditAfterReturnAmount;
    }
}
