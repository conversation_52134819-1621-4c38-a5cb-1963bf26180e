package com.vedeng.erp.kingdee.batch.repository;


import com.vedeng.erp.kingdee.batch.dto.BatchVerifiesInfoDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BatchVerifiesInfoDtoMapper {
    /**
     * 根据关联表名和关联表主键获取审核状态
     */
    List<BatchVerifiesInfoDto> getStatusByRelateTableAndRelateTableKey(@Param("relateTable") String relateTable, @Param("relateTableKey") Integer relateTableKey);

    BatchVerifiesInfoDto getBuyOrderContractVerifyInfo(@Param("relateTable") String relateTable, @Param("relateTableKey") Integer relateTableKey, @Param("addTime") Long addTime);
}
