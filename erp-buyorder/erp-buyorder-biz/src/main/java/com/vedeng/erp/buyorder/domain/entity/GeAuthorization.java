package com.vedeng.erp.buyorder.domain.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * T_GE_AUTHORIZATION
 * <AUTHOR>
public class GeAuthorization implements Serializable {
    /**
     * 主键
     */
    private Integer authorizationId;

    /**
     * GE授权书编号
     */
    private String authorizationNo;

    /**
     * 关联的GE商机编号
     */
    private Integer geBussinessChanceId;

    /**
     * 产品类型1 CT 2 超声
     */
    private Integer type;

    /**
     * 备注
     */
    private String content;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 授权书审核状态0待审核（默认）、1审核中、2审核通过、3审核不通过、 4已关闭
     */
    private Integer status;

    /**
     * 申请人 T_USER id
     */
    private Integer creator;

    /**
     * 申请人 T_USER_DETAIL REAL_NAME 中文名
     */
    private String creatorName;

    /**
     * 申请时间
     */
    private Date addTime;

    /**
     * 修改人 T_USER id
     */
    private Integer updater;

    /**
     * 修改人 T_USER_DETAIL REAL_NAME 中文名
     */
    private String updaterName;

    /**
     * 修改时间 
     */
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Integer getAuthorizationId() {
        return authorizationId;
    }

    public void setAuthorizationId(Integer authorizationId) {
        this.authorizationId = authorizationId;
    }

    public String getAuthorizationNo() {
        return authorizationNo;
    }

    public void setAuthorizationNo(String authorizationNo) {
        this.authorizationNo = authorizationNo;
    }

    public Integer getGeBussinessChanceId() {
        return geBussinessChanceId;
    }

    public void setGeBussinessChanceId(Integer geBussinessChanceId) {
        this.geBussinessChanceId = geBussinessChanceId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getUpdaterName() {
        return updaterName;
    }

    public void setUpdaterName(String updaterName) {
        this.updaterName = updaterName;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        GeAuthorization other = (GeAuthorization) that;
        return (this.getAuthorizationId() == null ? other.getAuthorizationId() == null : this.getAuthorizationId().equals(other.getAuthorizationId()))
            && (this.getAuthorizationNo() == null ? other.getAuthorizationNo() == null : this.getAuthorizationNo().equals(other.getAuthorizationNo()))
            && (this.getGeBussinessChanceId() == null ? other.getGeBussinessChanceId() == null : this.getGeBussinessChanceId().equals(other.getGeBussinessChanceId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getContent() == null ? other.getContent() == null : this.getContent().equals(other.getContent()))
            && (this.getIsDelete() == null ? other.getIsDelete() == null : this.getIsDelete().equals(other.getIsDelete()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreator() == null ? other.getCreator() == null : this.getCreator().equals(other.getCreator()))
            && (this.getCreatorName() == null ? other.getCreatorName() == null : this.getCreatorName().equals(other.getCreatorName()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdater() == null ? other.getUpdater() == null : this.getUpdater().equals(other.getUpdater()))
            && (this.getUpdaterName() == null ? other.getUpdaterName() == null : this.getUpdaterName().equals(other.getUpdaterName()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getAuthorizationId() == null) ? 0 : getAuthorizationId().hashCode());
        result = prime * result + ((getAuthorizationNo() == null) ? 0 : getAuthorizationNo().hashCode());
        result = prime * result + ((getGeBussinessChanceId() == null) ? 0 : getGeBussinessChanceId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getContent() == null) ? 0 : getContent().hashCode());
        result = prime * result + ((getIsDelete() == null) ? 0 : getIsDelete().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreator() == null) ? 0 : getCreator().hashCode());
        result = prime * result + ((getCreatorName() == null) ? 0 : getCreatorName().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdater() == null) ? 0 : getUpdater().hashCode());
        result = prime * result + ((getUpdaterName() == null) ? 0 : getUpdaterName().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", authorizationId=").append(authorizationId);
        sb.append(", authorizationNo=").append(authorizationNo);
        sb.append(", geBussinessChanceId=").append(geBussinessChanceId);
        sb.append(", type=").append(type);
        sb.append(", content=").append(content);
        sb.append(", isDelete=").append(isDelete);
        sb.append(", status=").append(status);
        sb.append(", creator=").append(creator);
        sb.append(", creatorName=").append(creatorName);
        sb.append(", addTime=").append(addTime);
        sb.append(", updater=").append(updater);
        sb.append(", updaterName=").append(updaterName);
        sb.append(", updateTime=").append(updateTime);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}