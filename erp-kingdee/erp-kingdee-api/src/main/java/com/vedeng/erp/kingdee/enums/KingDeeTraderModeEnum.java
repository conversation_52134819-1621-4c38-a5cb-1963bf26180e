package com.vedeng.erp.kingdee.enums;

/**
 * 交易方式
 */
public enum KingDeeTraderModeEnum {
    ALI_PAY(520, "支付宝"),
    BANK_PAY(521, "银行"),
    WE_CHAT_PAY(522, "微信"),
    CASHE(523, "现金"),
    CREDIT_PAY(527, "信用支付"),
    BALANCE_PAY(528, "余额支付"),
    CREDIT_RETURN(529, "退还信用"),
    BALANCE_RETURN(530, "退还余额"),
    ;

    private Integer code;

    private String desc;

    KingDeeTraderModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String matchDescByCode(Integer code){
        for (KingDeeTraderModeEnum kingDeeTraderModeEnum : KingDeeTraderModeEnum.values()) {
            if (kingDeeTraderModeEnum.getCode().equals(code)){
               return kingDeeTraderModeEnum.getDesc();
            }
        }
        throw new IllegalArgumentException("未忽略项目无code匹配");
    }
}
