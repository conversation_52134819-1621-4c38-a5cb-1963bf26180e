<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增授权书" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<style>
    i {
        display: inline-block;
        height: 12px;
        background: none;
        margin-bottom: -2px;
    }
    .mylabe{
        width: 110px;
    }
</style>
    <div class="formpublic">
        <form  id="myform" class="layui-form">
        	<input type="hidden" id="traderSupplyId" name="traderSupplyId" value="${traderSupplier.traderSupplierId}">
        	<%--<input type="hidden" name="validStartTime" value=''>--%>
        	<%--<input type="hidden" name="validEndTime" value=''>--%>
        	<input type="hidden" id="brands" value='${brands}'>
            <div class="layui-form-item">
                <label class="layui-form-label mylabe" >设置有效期</label>
                <div class="layui-input-block">
                    <div style="padding-top: 9px;">

                        <input class="Wdate  takeDate" style="width: 100px;" type="text" name="validStartTime" id="validStartTime"
                               autocomplete="off" placeholder="请选择日期" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               format="yyyy-MM-dd"/> -

                        <input class="Wdate  takeDate" style="width: 100px;" type="text" name="validEndTime" id="validEndTime"
                               autocomplete="off" placeholder="请选择日期" onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})"
                               format="yyyy-MM-dd"/>
                    </div>
                    <%--<input type="text" name="vailDate"  placeholder="请选择日期" class="layui-input layui-input-date validDate" lay-verify="required" autocomplete="false"/>--%>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label mylabe">授权类型</label>
                <div class="layui-input-inline">
                    <select name="authType" lay-verify="required">
                        <c:if test="${traderSupplier.traderType ==1}">
                            <option value=""></option>
                            <option value="1">独家代理合作模式</option>
                            <option value="6">自有产品加工模式</option>
                            <option value="2">授权经销合作模式</option>
                            <option value="3">零星采购模式</option>
                        </c:if>
                        <c:if test="${traderSupplier.traderType == 2}">
                            <option value=""></option>
                            <option value="4">授权经销产品</option>
                            <option value="5">非授权经销产品</option>
                        </c:if>
                        <c:if test="${traderSupplier.traderType == 0}">
                            <option value=""></option>
                            <option value="1">独家代理合作模式</option>
                            <option value="6">自有产品加工模式</option>
                            <option value="2">授权经销合作模式</option>
                            <option value="3">零星采购模式</option>
                            <option value="4">授权经销产品</option>
                            <option value="5">非授权经销产品</option>
                        </c:if>


                    </select>
                </div>
            </div>

            <div class="layui-form-item">
                <label class="layui-form-label mylabe">产品品牌</label>
                <div class="layui-input-inline">
                    <div id = "mx-select" name="brandIds"></div>
                    <%--<select class="" lay-search lay-filter="multi">--%>
                    <%--    <option value=""></option>--%>
                    <%--    <option value="1">独家代理合作模式</option>--%>
                    <%--    <option value="2">授权经销合作模式</option>--%>
                    <%--    <option value="3">零星采购模式</option>--%>
                    <%--    <option value="4">授权经销产品</option>--%>
                    <%--    <option value="5">非授权进销产品</option>--%>
                    <%--</select>--%>
                </div>
            </div>
            <div class="layui-form-item" >
                <label class="layui-form-label mylabe">产品授权书</label>
                <%--<input type="text" name="title" required  lay-verify="required" placeholder="请输入标题" autocomplete="off" class="layui-input">--%>
                <input type="text"  readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">
                <button type="button" class="layui-btn layui-btn-sm layui-btn-normal" style="height: 32px;" id="test8">浏览</button>
                <input type="hidden" id="oss" name="ossFile" readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">
                <a style="display: none" target="_blank" href="">查看</a>
                <input type="hidden" id="prefix" name="prefix" readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">
            </div>
            <div class="layui-form-item" id="upload" style="text-align: left">
                <label class="layui-form-label mylabe" ></label>
                <button type="button" class="layui-btn layui-btn-primary  layui-btn-sm" id="test10"><span style="color: red">继续添加</span></button>
            </div>
            <div class="layui-form-item">
                <label class="layui-form-label mylabe" ></label>
                <div class="" style="text-align: left;color: grey">
                    1.支持格式: JPG、PNG、 JPEG、BMP、PDF<br>
                    <label class="layui-form-label mylabe" ></label>
                    2.上传文件不能超过5MB
                </div>
            </div>

            <div class="layui-btn-container">
                    <div class="layui-form-item">
                        <div class="layui-input-block">
                            <button class="layui-btn layui-btn-normal" lay-submit lay-filter="formDemo">提交</button>
                            <%--<button class="layui-btn" lay-submit lay-filter="formDemo">立即提交</button>--%>
                            <%--<button type="reset" class="layui-btn layui-btn-primary">重置</button>--%>
                        </div>
                    </div>
            </div>


            <div class="add-tijiao tcenter mt2">
            </div>




        </form>
    </div>
</body>

</html>

<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<script src="/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<script>
    //Demo
    layui.use('form', function(){
        var form = layui.form;

        //监听提交
        form.on('submit(formDemo)', function(data){


                debugger

            var validStartTime = $("input[name='validStartTime']").val();
            var validEndTime = $("input[name='validEndTime']").val();
            if (validStartTime==null||validStartTime==undefined||validStartTime==''||validEndTime==null||validEndTime==undefined||validEndTime=='') {
                layer.alert("有效期开始时间和结束时间必填！");
                return false;
            }
            if (validEndTime<=validStartTime) {
                layer.alert("有效期结束时间不可大于等于有效期开始时间！");
                return false;
            }
            var arr = [];
            var authType = $("[name='authType']").val();
            var hasFile = $("input[name='ossFile']").length >0?true:false;
            $("input[name='ossFile']").each(function(){
                if ($(this).val()=='' || $(this).val()==undefined || $(this).val()==null) {
                    hasFile = false;
                }
                if ($(this).val()!=''&&$(this).val()!=undefined&&$(this).val()!=null) {
                    arr.push($(this).val());
                }
            });
            if(authType == 1 || authType == 2|| authType == 4|| authType == 6){
                if(!hasFile){
                    layer.alert("授权类型为独家代理合作模式、自有产品加工模式、授权经销合作模式、授权经销产品时，附件必须上传");
                    return false;
                }
            }
            // <option value="1">独家代理合作模式</option>
            //     <option value="6">自有产品加工模式</option>
            //     <option value="2">授权经销合作模式</option>
            //     <option value="3">零星采购模式</option>
            //     <option value="4">授权经销产品</option>
            //     <option value="5">非授权经销产品</option>
            var pre = []
            $("input[name='prefix']").each(function(){
                if ($(this).val()!=''&&$(this).val()!=undefined&&$(this).val()!=null) {
                    pre.push($(this).val());
                }
            })

            var formdata = $('#myform').serializeArray();
            var data = {}

            $.each(formdata, function() {
                data[this.name] = this.value;
            });
            data['ossFile'] = arr;
            data['prefix'] = pre;

            console.log(JSON.stringify(data));
            $.ajax({
                async:false,
                url: '/trader/supply/auth/save.do',
                data:JSON.stringify(data),
                type:"POST",
                dataType : "json",
                contentType:'application/json',
                success:function(data){
                    console.log(data);
                    // layer.close(load);
                    if (data.code == 0) {
                        layer.msg('保存成功', {
                            icon: 1,
                            time: 2000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            //do something
                            var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
                            parent.layer.close(index);
                            parent.location.reload(true);
                        });

                    } else {
                        if (data.code==-1) {
                            layer.alert(data.message);
                        }

                    }

                    // window.parent.search();
                },
                error:function(data){
                    if (data.code==-1) {
                        layer.alert(data.message);
                    }
                    if(data.status == 1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                    if(data.status == 400){
                        layer.alert("操作失败");
                    }
                    if(data.status == 500){
                        layer.alert("操作失败");
                    }
                }
            });

            return false;
        });
    });

    layui.use('laydate',function(){
        var laydate = layui.laydate;
        //执行一个laydate实例
        laydate.render({
            elem: '.validDate' //指定元素
            ,type: 'date'
            ,trigger:'click'
            ,range:true
        });
    })
    layui.use('upload',function(){
        var upload =  layui.upload;
        console.log("上传")
        // var value = $("#expressId").val();
        uploadListIns = upload.render({
            elem: '#test8'
            ,url: '/vgoods/operate/fileUploadImg.do' //此处配置你自己的上传接口即可
            // ,data:{expressId:value}
            ,auto: true
            ,multiple: false
            // ,bindAction: '#test9'
            ,before:function (obj){
                layer.load(2,{shade:false})
            }
            ,accept:'file'
            ,exts:  'jpg|png|jpeg|pdf|bmp'
            ,size:5120
            ,acceptMime: '.jpg, .png, .jpeg,.bmp,.pdf'
            ,choose:function (obj){
                var item = this.item;
                console.log(item.get(0).parentNode.children[1])

                console.log(obj)

                //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                obj.preview(function (index, file, result) {
                    // console.log(index); //得到文件索引
                    // console.log(file); //得到文件对象
                    // console.log(result); //得到文件base64编码，比如图片
                    //获取文件名***************************
                    item.get(0).parentNode.children[1].value = file.name

                    //这里还可以做一些 append 文件列表 DOM 的操作

                    //obj.upload(index, file); //对上传失败的单个文件重新上传，一般在某个事件中使用


                });
            }
            ,done: function(res){
                console.log(res)
                if (res.code == 0) {
                    var item = this.item;
                    console.log(item.get(0).parentNode)
                    item.get(0).parentNode.children[4].value=res.filePath
                    item.get(0).parentNode.children[6].value=res.prefix
                    // item.get(0).parentNode.children[5].href=res.ossUrl
                    var $1 = $(item.get(0).parentNode.children[5]);
                    $1.attr("href", res.ossUrl);
                    $1.show();
                    layer.closeAll("loading");
                } else {
                    layer.closeAll("loading");
                    layer.alert(res.msg);
                }

            }
        });

    })
    var brandsString = $("#brands").val();
    var s =  JSON.parse(brandsString);

    var demo1 = xmSelect.render({
        el: '#mx-select',
        name:'brandIds',
        autoRow: true,
        filterable: true,
        paging: true,
        pageSize: 10,
        filterMethod: function(val, item, index, prop){
            // if(val.toLowerCase() == item.value.toLowerCase()){//把value相同的搜索出来
            //     return true;
            // }
            // debugger
            // if (val.length > 2) {
            //     layer.alert("搜索字符不可超过50个字符")
            //     return false;
            // }
            if(item.name.toLowerCase().indexOf(val.toLowerCase()) != -1){//名称中包含的搜索出来
                return true;
            }
            return false;//不知道的就不管了
        },
        data: s
    })
    var itemNum = 1000;
    $(function (){
        $(".xm-search-input").bind("input propertychange",function (){
            if ($(".xm-search-input").val().length > 50) {
                $(".xm-search-input").val("");
                layer.alert("搜索字符不可超过50个字符")
            }
            // parent.layer.close(index);
        })

        $("#test10").click(function (){
            debugger
            itemNum=itemNum+1;
            var htm =
                '<div class="layui-form-item" >'+
                '<label class="layui-form-label mylabe"></label>'+
                '<input type="text"  readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">'+
                '<button type="button" class="layui-btn layui-btn-sm layui-btn-normal " style="height: 32px;" id="myImg'+itemNum+'">浏览</button>&nbsp;&nbsp;&nbsp;'+
                '<input type="hidden" id="oss'+itemNum+'" name="ossFile"  style="height: 36px;">'+
                '<button type="button" class="layui-btn layui-btn-sm layui-btn-danger mydell "   style="height: 32px;" ">删除</button>'+
                '<a herf="" target="_blank"  style="display: none">查看</a>&nbsp;'+
                '<input type="hidden" id="prefix" name="prefix" readonly="readonly" class="layui-input-inline" autocomplete="off"  style="height: 36px;">'+
                '</div>'
            $("#upload").before(htm)

            var upload =  layui.upload;
            console.log("上传")
            uploadListIns = upload.render({
                elem: '#myImg'+itemNum
                ,url: '/vgoods/operate/fileUploadImg.do' //此处配置你自己的上传接口即可
                // ,data:{expressId:value}
                ,auto: true
                ,multiple: false
                // ,bindAction: '#test9'
                ,accept:'file'
                ,exts:  'jpg|png|jpeg|bmp|pdf'
                ,acceptMime: '.jpg, .png, .jpeg,.bmp,.pdf'
                ,size:5120
                ,before:function (obj){
                    layer.load(2,{shade:false})
                }
                ,choose:function (obj){
                    //将每次选择的文件追加到文件队列
                    // loadIndex = layer.load(1);


                    // var files = obj.pushFile();
                    console.log(obj)
                    var item = this.item;
                    console.log(item.get(0).parentNode.children[1])

                    //预读本地文件，如果是多文件，则会遍历。(不支持ie8/9)
                    obj.preview(function (index, file, result) {
                        // console.log(index); //得到文件索引
                        // console.log(file); //得到文件对象
                        // console.log(result); //得到文件base64编码，比如图片
                        //获取文件名***************************
                        item.get(0).parentNode.children[1].value = file.name

                        //这里还可以做一些 append 文件列表 DOM 的操作

                        //obj.upload(index, file); //对上传失败的单个文件重新上传，一般在某个事件中使用


                    });
                }
                ,done: function(res){
                    console.log(res)
                    if (res.code == 0) {
                        // layer.msg('上传成功');
                        var item = this.item;
                        console.log(item.get(0).parentNode)
                        item.get(0).parentNode.children[4].value=res.filePath
                        debugger
                        item.get(0).parentNode.children[6].herf=res.ossUrl
                        var $1 = $(item.get(0).parentNode.children[6]);
                        $1.attr("href", res.ossUrl);
                        $1.show();
                        item.get(0).parentNode.children[7].value=res.prefix
                        layer.closeAll("loading");
                    } else {
                        layer.alert(res.msg);
                        layer.closeAll("loading");
                    }

                }
            });
        })

        // $("#test9").click(function (){
        //     debugger
        //     var checkData = getCheckData();
        //     if (checkData.length > 0) {
        //         layer.msg('上传成功', {
        //             time: 2000
        //         }, function () {
        //             var checkData = getCheckData();
        //             parent.setFileUrls(checkData);
        //             var index = parent.layer.getFrameIndex(window.name); //获取窗口索引
        //             parent.layer.close(index);
        //         });
        //     } else {
        //         layer.alert('未选择上传产品授权书文件')
        //     }
        // })




    })

    $(function(){
        $("body").on("click",".mydell",function(){
            console.log(11)
            console.log($(this))
            $(this).get(0).parentNode.remove()
            console.log()

        })
    })
</script>
