package com.vedeng.price.model;

import lombok.Data;

import java.math.BigDecimal;
@Data
public class SkuPriceModifyRecord {

    private Long skuPriceModifyRecordId;

    private Long skuId;

    private Long priceModTime;

    private BigDecimal afterModMarketPrice;

    private BigDecimal beforeModMarketPrice;

    private BigDecimal afterModTerminalPrice;

    private BigDecimal beforeModTerminalPrice;

    private BigDecimal afterModDistributionPrice;

    private BigDecimal beforeModDistributionPrice;

    private BigDecimal afterModPurchaseCosts;

    private BigDecimal beforeModPurchaseCosts;

    private BigDecimal afterModGroupPrice;

    private BigDecimal beforeModGroupPrice;

    /**
     * 调整后电商价
     */
    private BigDecimal afterModElectronicCommercePrice;

    /**
     * 调整前电商价
     */
    private BigDecimal beforeModElectronicCommercePrice;

    /**
     * 调整后科研终端价
     */
    private BigDecimal afterModResearchTerminalPrice;

    /**
     * 调整前科研终端价
     */
    private BigDecimal beforeModResearchTerminalPrice;

    private int modPriceType;

    private Integer creator;

    private Long addTime;

    private Integer updater;

    private Long updateTime;

}