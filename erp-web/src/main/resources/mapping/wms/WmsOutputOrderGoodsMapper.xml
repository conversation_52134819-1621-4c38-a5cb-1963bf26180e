<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsOutputOrderGoodsMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsOutputOrderGoods" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="wms_output_order_id" property="wmsOutputOrderId" jdbcType="BIGINT" />
    <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
    <result column="output_num" property="outputNum" jdbcType="INTEGER" />
    <result column="expect_returned_time" property="expectReturnedTime" jdbcType="VARCHAR" />
    <result column="add_time" property="addTime" jdbcType="VARCHAR" />
    <result column="update_time" property="updateTime" jdbcType="VARCHAR" />
    <result column="is_delete" property="isDelete" jdbcType="INTEGER" />
    <result column="LOGICAL_WAREHOUSE_ID" property="logicalWarehouseId" jdbcType="INTEGER" />
  </resultMap>


  <resultMap id="sampleOutOrderResultMap" type="com.wms.model.po.WmsOutputOrderGoods" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="wms_output_order_id" property="wmsOutputOrderId" jdbcType="BIGINT" />
    <result column="sku_no" property="skuNo" jdbcType="VARCHAR" />
    <result column="output_num" property="outputNum" jdbcType="INTEGER" />
    <result column="unitName" property="unitName" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, wms_output_order_id, sku_no, output_num, expect_returned_time, add_time, update_time, 
    is_delete,LOGICAL_WAREHOUSE_ID
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER_GOODS
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="queryOutputGoodsByLendOutId" resultType="com.wms.model.dto.WmsOutputOrderGoodsDto" parameterType="java.lang.Long" >
    SELECT
       goods.id,
       goods.wms_output_order_id,
       goods.sku_no,
       goods.output_num,
       goods.already_output_num,
       goods.already_input_num,
       goods.expect_returned_time,
       goods.expect_returned_time,
       sku.SHOW_NAME,
       sku.MODEL,
       sku.SKU_ID,
      (SELECT BRAND_NAME FROM T_BRAND WHERE BRAND_ID = spu.BRAND_ID) brand_name
    FROM T_WMS_OUTPUT_ORDER_GOODS goods
    LEFT JOIN V_CORE_SKU sku ON goods.sku_no = sku.SKU_NO
    LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
    where wms_output_order_id = #{lendOutOrderId,jdbcType=BIGINT}
    and  goods.IS_DELETE = 0
    GROUP BY goods.id
  </select>

  <select id="queryOutputGoodsByWmsOutPutOrderId" resultType="com.wms.model.dto.WmsOutputOrderGoodsDto" parameterType="java.lang.Long" >
    SELECT
    goods.*,
    sys.TITLE logicalName,
    sku.SKU_NAME,
    sku.MODEL,
    BR.BRAND_NAME
    FROM T_WMS_OUTPUT_ORDER_GOODS goods
    LEFT JOIN V_CORE_SKU sku ON goods.sku_no = sku.SKU_NO
    LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
    LEFT JOIN T_BRAND BR ON BR.BRAND_ID = spu.BRAND_ID
    LEFT JOIN T_SYS_OPTION_DEFINITION sys on sys.SYS_OPTION_DEFINITION_ID = goods.LOGICAL_WAREHOUSE_ID
    where wms_output_order_id = #{scrappedOutId,jdbcType=BIGINT}
    AND goods.is_delete = 0
  </select>

  <select id="getOrderGoods" resultMap="BaseResultMap">
    SELECT
     A.id, A.wms_output_order_id, A.sku_no, A.output_num, A.expect_returned_time, A.add_time, A.update_time,
    A.is_delete
    FROM  T_WMS_OUTPUT_ORDER_GOODS A
    LEFT JOIN T_WMS_OUTPUT_ORDER B ON A.wms_output_order_id = B.id
    WHERE B.order_no = #{orderNo,jdbcType=VARCHAR} AND A.sku_no = #{skuNo,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from T_WMS_OUTPUT_ORDER_GOODS
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into T_WMS_OUTPUT_ORDER_GOODS (
            wms_output_order_id,
            sku_no,
            output_num,
            expect_returned_time,
            add_time,
            update_time
    )values
    <foreach collection="list" item="data" index="index" separator=",">
      (
        #{data.wmsOutputOrderId,jdbcType=BIGINT},
        #{data.skuNo,jdbcType=VARCHAR},
        #{data.outputNum,jdbcType=INTEGER},
        #{data.expectReturnedTime,jdbcType=VARCHAR},
        #{data.addTime,jdbcType=VARCHAR},
        #{data.updateTime,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <insert id="insertSelective" parameterType="com.wms.model.po.WmsOutputOrderGoods" keyColumn="id" keyProperty="id" useGeneratedKeys="true">
    insert into T_WMS_OUTPUT_ORDER_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="wmsOutputOrderId != null">
        wms_output_order_id,
      </if>
      <if test="skuNo != null">
        sku_no,
      </if>
      <if test="outputNum != null">
        output_num,
      </if>
      <if test="expectReturnedTime != null">
        expect_returned_time,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isDelete != null">
        is_delete,
      </if>
      <if test="logicalWarehouseId != null">
        LOGICAL_WAREHOUSE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="wmsOutputOrderId != null">
        #{wmsOutputOrderId,jdbcType=BIGINT},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="outputNum != null">
        #{outputNum,jdbcType=INTEGER},
      </if>
      <if test="expectReturnedTime != null">
        #{expectReturnedTime,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="logicalWarehouseId != null">
        #{logicalWarehouseId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsOutputOrderGoods" >
    update T_WMS_OUTPUT_ORDER_GOODS
    <set >
      <if test="wmsOutputOrderId != null" >
        wms_output_order_id = #{wmsOutputOrderId,jdbcType=BIGINT},
      </if>
      <if test="skuNo != null" >
        sku_no = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="outputNum != null" >
        output_num = #{outputNum,jdbcType=INTEGER},
      </if>
      <if test="expectReturnedTime != null" >
        expect_returned_time = #{expectReturnedTime,jdbcType=VARCHAR},
      </if>

      <if test="alreadyOutputNum != null" >
        already_output_num = #{alreadyOutputNum,jdbcType=INTEGER},
      </if>
      <if test="lastOutputTime != null" >
        last_output_time = #{lastOutputTime,jdbcType=VARCHAR},
      </if>
      <if test="outStatus != null" >
        out_status = #{outStatus,jdbcType=INTEGER},
      </if>

      <if test="alreadyInputNum != null" >
        already_input_num = #{alreadyInputNum,jdbcType=INTEGER},
      </if>
      <if test="lastInputTime != null" >
        last_input_time = #{lastInputTime,jdbcType=VARCHAR},
      </if>
      <if test="inputStatus != null" >
        input_status = #{inputStatus,jdbcType=INTEGER},
      </if>

      <if test="addTime != null" >
        add_time = #{addTime,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null" >
        is_delete = #{isDelete,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="queryOutputGoodsBySampleOutId" resultType="com.wms.model.dto.WmsOutputOrderGoodsDto">
    SELECT
    goods.id,
    goods.wms_output_order_id,
    goods.sku_no,
    unit.UNIT_NAME unitName,
    goods.output_num,
    goods.already_output_num,
    goods.out_status,
    sku.SHOW_NAME,
    sku.MODEL,
    sku.SKU_ID,
    (SELECT BRAND_NAME FROM T_BRAND WHERE BRAND_ID = spu.BRAND_ID) brand_name
    FROM T_WMS_OUTPUT_ORDER_GOODS goods
    LEFT JOIN V_CORE_SKU sku ON goods.sku_no = sku.SKU_NO
    LEFT JOIN V_CORE_SPU spu ON spu.SPU_ID = sku.SPU_ID
    LEFT JOIN T_UNIT unit on sku.BASE_UNIT_ID = unit.UNIT_ID
    where wms_output_order_id = #{sampleOutOrderId,jdbcType=BIGINT}
    and is_delete = 0
    group by goods.id
  </select>
  <select id="getWmsOutPutOrderGoods" resultType="com.wms.model.po.WmsOutputOrderGoods">
    select
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER_GOODS
    where wms_output_order_id = #{sampleOutOrderId,jdbcType=BIGINT}
  </select>
  <select id="getManageCategoryOfSku" resultType="java.lang.Integer">
      SELECT DISTINCT
        R.MANAGE_CATEGORY_LEVEL
      FROM
        T_WMS_OUTPUT_ORDER_GOODS SG
          JOIN V_CORE_SKU SKU ON SG.sku_no = SKU.SKU_NO AND SG.is_delete = 0
          JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
          JOIN T_FIRST_ENGAGE F ON SPU.FIRST_ENGAGE_ID = F.FIRST_ENGAGE_ID
          JOIN T_REGISTRATION_NUMBER R ON F.REGISTRATION_NUMBER_ID = R.REGISTRATION_NUMBER_ID
      WHERE
        SG.wms_output_order_id = #{sampleOutOrderId,jdbcType=BIGINT}
  </select>

  <update id="deleteWmsOutputOrderGoodsByOrderId">
    UPDATE T_WMS_OUTPUT_ORDER_GOODS SET is_delete = 1 WHERE wms_output_order_id = #{sampleOutOrderId,jdbcType=BIGINT}
    </update>
</mapper>