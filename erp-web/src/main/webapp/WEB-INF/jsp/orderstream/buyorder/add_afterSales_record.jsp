<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后过程" scope="application" />
<%@ include file="../../common/common.jsp"%>

<script type="text/javascript"
        src='${pageContext.request.contextPath}/static/js/orderstream/buyorder/add_afterSales_record.js?rnd=${resourceVersionKey}'></script>


<script type="text/javascript" >

</script>
<div class="form-list  form-tips4">
    <table class="table  table-style6">
        <thead>
        <tr>

            <th class="wid5">选择</th>
            <th class="wid10">订货号</th>
            <th>产品名称</th>
            <th>品牌</th>
            <th>规格/型号</th>
            <th class="wid4">单位</th>
            <th><span style="color: red">*</span>实际出货数量</th>

            <th class="wid10">贝登批次码</th>
            <th class="wid23">生产日期</th>
            <th class="wid23">有效期至</th>
            <th class="wid23">出库时间</th>
            <th class="wid10">厂商批号</th>
            <th class="wid10">灭菌编号</th>

            <th class="wid12">注册证号</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty afterSalesGoodsVoList}">
            <c:forEach items="${afterSalesGoodsVoList}" var="arg" varStatus="num_index">
                <tr>
                    <td>
                        <input type="checkbox" name="${afterSalesGoodsId}" alt="${arg.afterSalesGoodsId}">
                        <input type="hidden"  name="afterSalesGoodsId" value="${arg.afterSalesGoodsId}">
                        <input type="hidden"  name="afterSalesId" value="${arg.afterSalesId}">
                        <input type="hidden"  name="sku" value="${arg.sku}">
                    </td>
                    <td>${arg.sku}</td>
                    <td class="text-left">
										<span class="brand-color1 addtitle"
                                              tabTitle='{"num":"viewgoods${arg.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${arg.goodsId}", "title":"产品信息"}'>
                                                ${arg.goodsName}
                                        </span>
                        <input type="hidden"  name="showName" value="${arg.goodsName}">
                        <input type="hidden"  name="goodsId" value="${arg.goodsId}">
                    </td>
                    <td>${arg.brandName}
                        <input type="hidden"  name="brandName" value="${arg.brandName}">
                    </td>
                    <td>${arg.spec}/${arg.model}
                        <input type="hidden"  name="model" value="${arg.model}">
                        <input type="hidden"  name="spec" value="${arg.spec}">
                    </td>
                    <td>${arg.unitName}
                        <input type="hidden"  name="unitName" value="${arg.unitName}">
                    </td>
                    <td><input type="number" style="width:50px;" name="maxOutNum"  data-maxNum ="${arg.maxOutNum}"  value="${arg.maxOutNum}"></td>
                    <td>
                        <input type="text" style="width:50px;" name="vedengBatchNum">
                    </td>
                    <td>
                        <input class="Wdate m0 input-middle Wdate2" name ="produceTime" id="produceTime${arg.afterSalesGoodsId}" type="text" placeholder="请选择日期"
                               onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off">
                    </td>
                    <td>
                        <input class="Wdate m0 input-middle Wdate2" name ="validTime" id="validTime${arg.afterSalesGoodsId}" type="text" placeholder="请选择日期"
                               onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off">
                    </td>
                    <td>
                        <input class="Wdate m0 input-middle Wdate2" name ="outTime" id="outTime${arg.afterSalesGoodsId}" type="text" placeholder="请选择日期"
                               onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" autocomplete="off">
                    </td>

                    <td><input type="text" style="width:50px;" name="industryBatchNumber"></td>



                    <td><input type="text" style="width:50px;" name="sterilizationNumber"></td>


                    <td class="text-left">
                        <a class="addtitle"
                           href="jaxvascript:void(0);"
                           tabTitle='{"link":"./firstengage/baseinfo/getFirstSearchDetail.do?firstEngageId=${arg.firstEngageId}","title":"注册证号详情页"}'>${arg.registrationNumber}</a>
                        <input type="hidden"  name="firstEngageId" value="${arg.firstEngageId}">
                        <input type="hidden"  name="registrationNumber" value="${arg.registrationNumber}">
                    </td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty afterSalesGoodsVoList}">
        <tr>
            <td colspan="13">
            无可出库商品
            </td>
        </tr>
        </c:if>
        </tbody>
    </table>
    <div style="text-align:center">
    <span class="add-tijiao text-left mt8">
        <button type="submit" id="submit" onclick="submit()">提交</button>
    </span>
    <span style="margin-left: 10px" class="add-tijiao text-left mt8">
        <button type="button" class="dele" id="cancle">取消</button>
    </span>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>