package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.model.po.WmsOutputOrder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;
/**
 * (7)	该SKU没有 审核通过，且归还状态为未归还、部分归还的外借单；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:00.
 * @author: <PERSON><PERSON>.
 */
@Service
public class SkuNoRelatedUnbackLendOutInvalidator extends GoodsInvalidatorChain {

    @Resource
    WmsOutputOrderMapper wmsOutputOrderMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        List<WmsOutputOrder> wmsOutputOrderList = wmsOutputOrderMapper.getUnbackLendOutBySkuNo(skuNo);
        if(CollectionUtils.isNotEmpty(wmsOutputOrderList)){
            String collect = wmsOutputOrderList.stream().map(e -> e.getOrderNo()).collect(Collectors.joining(","));
            String orderNo = SkuNoRelatedUnbackLendOutInvalidatorError.getMessage().replace("orderNo", collect);
            throw new InvalidatorChainException(SkuNoRelatedUnbackLendOutInvalidatorError.getCode(),orderNo);
        }
    }
}
