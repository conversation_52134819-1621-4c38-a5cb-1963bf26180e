package com.vedeng.erp.trader.domain.entity;

import java.util.Date;

/**
 * Table: DWH_TRADER_TAG_CHANGE_ERP
 */
public class DwhTraderTagChangeErpEntity {
    /**
     * Column: TRADER_ID
     * Type: INT
     * Remark: 交易者ID
     */
    private Integer traderId;

    /**
     * Column: TRADER_CUSTOMER_ID
     * Type: INT
     * Remark: 客户ID
     */
    private Integer traderCustomerId;

    /**
     * Column: SOURCE
     * Type: INT
     * Remark: 数据来源 1：系统
     */
    private Integer source;

    /**
     * Column: OPER_TIME
     * Type: DATETIME
     * Remark: 操作时间
     */
    private Date operTime;

    /**
     * Column: OPER_TYPE
     * Type: INT
     * Remark: 模块操作类型 0：新增 1：修改 2：删除
     */
    private Integer operType;

    /**
     * Column: TAG_MODEL_NAME
     * Type: VARCHAR(20)
     * Remark: 标签模块名称
     */
    private String tagModelName;

    /**
     * Column: TAG_NAME
     * Type: VARCHAR(20)
     * Remark: 标签名称
     */
    private String tagName;

    /**
     * Column: OLD_TAG_LABEL
     * Type: VARCHAR(2000)
     * Remark: 原始标签记录值 （逗号分割）
     */
    private String oldTagLabel;

    /**
     * Column: NEW_TAG_LABEL
     * Type: VARCHAR(2000)
     * Remark: 新标签记录值 （逗号分割）
     */
    private String newTagLabel;

    /**
     * Column: TAG_CHANGE_LOG
     * Type: VARCHAR(2000)
     * Remark: 记录变化日志内容
     */
    private String tagChangeLog;

    /**
     * Column: ETL_DAY
     * Type: DATE
     */
    private Date etlDay;

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public Integer getTraderCustomerId() {
        return traderCustomerId;
    }

    public void setTraderCustomerId(Integer traderCustomerId) {
        this.traderCustomerId = traderCustomerId;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Date getOperTime() {
        return operTime;
    }

    public void setOperTime(Date operTime) {
        this.operTime = operTime;
    }

    public Integer getOperType() {
        return operType;
    }

    public void setOperType(Integer operType) {
        this.operType = operType;
    }

    public String getTagModelName() {
        return tagModelName;
    }

    public void setTagModelName(String tagModelName) {
        this.tagModelName = tagModelName == null ? null : tagModelName.trim();
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName == null ? null : tagName.trim();
    }

    public String getOldTagLabel() {
        return oldTagLabel;
    }

    public void setOldTagLabel(String oldTagLabel) {
        this.oldTagLabel = oldTagLabel == null ? null : oldTagLabel.trim();
    }

    public String getNewTagLabel() {
        return newTagLabel;
    }

    public void setNewTagLabel(String newTagLabel) {
        this.newTagLabel = newTagLabel == null ? null : newTagLabel.trim();
    }

    public String getTagChangeLog() {
        return tagChangeLog;
    }

    public void setTagChangeLog(String tagChangeLog) {
        this.tagChangeLog = tagChangeLog == null ? null : tagChangeLog.trim();
    }

    public Date getEtlDay() {
        return etlDay;
    }

    public void setEtlDay(Date etlDay) {
        this.etlDay = etlDay;
    }
}