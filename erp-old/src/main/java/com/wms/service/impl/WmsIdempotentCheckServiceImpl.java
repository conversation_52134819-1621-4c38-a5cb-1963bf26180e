package com.wms.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.util.DateUtil;
import com.wms.dao.WmsIdempotentCheckMapper;
import com.wms.model.po.WmsIdempotentCheck;
import com.wms.service.WmsIdempotentCheckService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class WmsIdempotentCheckServiceImpl implements WmsIdempotentCheckService {

    @Resource
    private WmsIdempotentCheckMapper wmsIdempotentCheckMapper;

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @Override
    public Long idempotentCheck(String businessKey,String requestBody) throws Exception{

        WmsIdempotentCheck wmsIdempotentCheck = wmsIdempotentCheckMapper.selectByBusinessKey(businessKey);

        //已经消费成功,直接抛异常
        if(wmsIdempotentCheck != null && wmsIdempotentCheck.getConsumeResult() != 0){
//            throw new Exception("消息已经成功消费，不能重复消费!");
            return null;
        }

        if(wmsIdempotentCheck == null) {
            wmsIdempotentCheck = new WmsIdempotentCheck();
            wmsIdempotentCheck.setBusinessKey(businessKey);
            wmsIdempotentCheck.setMessageBody(requestBody);
            wmsIdempotentCheck.setAddTime(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            wmsIdempotentCheck.setUpdateTime(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
            wmsIdempotentCheckMapper.insertSelective(wmsIdempotentCheck);
        }

        return wmsIdempotentCheck.getId();
    }

    @Override
    public void updateIdempotentCheckSuccess(Long idempotentId) {
        WmsIdempotentCheck wmsIdempotentCheck = new WmsIdempotentCheck();
        wmsIdempotentCheck.setId(idempotentId);
        wmsIdempotentCheck.setConsumeResult(1);
        wmsIdempotentCheck.setUpdateTime(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        wmsIdempotentCheckMapper.updateByPrimaryKeySelective(wmsIdempotentCheck);
    }
}
