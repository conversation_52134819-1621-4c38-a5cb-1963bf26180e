package com.vedeng.temporal.mapper;

import com.vedeng.temporal.domain.dto.CompanySequenceInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 基于T_FLOW_NODE的公司顺序查询Mapper
 * 替代T_COMPANY_SEQUENCE表的查询逻辑
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
public interface FlowNodeCompanyMapper {
    
    /**
     * 根据流转单ID获取公司执行顺序（联合查询）
     * 适用于采购业务（仅供应商角色）
     *
     * @param flowOrderId 流转单ID
     * @return 公司顺序信息列表
     */
    List<CompanySequenceInfo> selectCompanySequenceByFlowOrderId(@Param("flowOrderId") Long flowOrderId);
    

    
    /**
     * 根据流转单ID和业务类型获取公司执行顺序
     * 适用于采购业务（仅供应商角色）
     *
     * @param flowOrderId 流转单ID
     * @param businessType 业务类型：1-采购，2-销售
     * @return 公司顺序信息列表
     */
    List<CompanySequenceInfo> selectCompanySequenceByBusinessType(
            @Param("flowOrderId") Long flowOrderId, 
            @Param("businessType") Integer businessType);
    


    /**
     * 仅从流程节点获取公司执行顺序（不包含来自T_FLOW_ORDER的0级公司）
     * 
     * @param flowOrderId 流转单ID
     * @return 公司顺序信息列表
     */
    List<CompanySequenceInfo> selectCompanySequenceFromFlowNodeOnly(@Param("flowOrderId") Long flowOrderId);

    /**
     * 根据流转单ID和公司代码获取流程节点ID
     *
     * @param flowOrderId 流转单ID
     * @param companyCode 公司代码
     * @return 流程节点ID（FLOW_NODE_ID）
     */
    Long getFlowNodeIdByFlowOrderAndCompany(
            @Param("flowOrderId") Long flowOrderId,
            @Param("companyCode") String companyCode);

    /**
     * 根据流程节点ID获取公司代码
     *
     * @param flowNodeId 流程节点ID
     * @return 公司代码（COMPANY_SHORT_NAME）
     */
    String getCompanyCodeByFlowNodeId(@Param("flowNodeId") Long flowNodeId);
}
