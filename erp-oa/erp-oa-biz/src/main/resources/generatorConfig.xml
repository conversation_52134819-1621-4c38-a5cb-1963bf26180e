<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd"><generatorConfiguration>
    <context id="context1"  targetRuntime="MyBatis3" >
        <property name="autoDelimitKeywords" value="true"/>
        <property name="beginningDelimiter" value="`"/>
        <property name="endingDelimiter" value="`"/>
        <property name="suppressAllComments" value="true"/>
        <property name="suppressDate" value="true"/>

        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="***************************************************************************************************************************************************"
                        userId="fatwrite"
                        password="fatwrite" />
        <javaTypeResolver type="com.vedeng.mybatis.generator.types.VedengJavaTypeResolver">
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>
<!--        <javaModelGenerator targetPackage="com.vedeng.order.model"  targetProject="src/main/java" />-->
<!--        <sqlMapGenerator targetPackage="/mapping/order" targetProject="src/main/resources" />-->
<!--        <javaClientGenerator targetPackage="com.vedeng.order.dao" targetProject="src/main/java" type="XMLMAPPER" />-->
        <javaModelGenerator targetPackage="com.vedeng.oa.model"  targetProject="src/main/java" />
        <sqlMapGenerator targetPackage="/mapping/oa" targetProject="src/main/resources" />
        <javaClientGenerator targetPackage="com.vedeng.oa.dao" targetProject="src/main/java" type="XMLMAPPER" />



        <table tableName="T_FILE_DELIVERY_NEW" domainObjectName="FileDeliveryNew">
            <generatedKey column="FILE_DELIVERY_ID" sqlStatement="MySql" identity="true"/>
        </table>
        <table tableName="T_FILE_DELIVERY_CONFIG" domainObjectName="FileDeliveryConfig">
            <generatedKey column="ID" sqlStatement="MySql" identity="true"/>
        </table>
        <table tableName="T_FILE_DELIVERY_ADDRESS" domainObjectName="FileDeliveryAddress">
            <generatedKey column="ID" sqlStatement="MySql" identity="true"/>
        </table>

    </context>
</generatorConfiguration>
