package com.vedeng.erp.trader.constant;

/**
 * <AUTHOR>
 * @date 2022/6/14 7:46
 **/
public class TraderConstant {
    /**
     * 附件类型
     * 文件
     */
    public final static Integer SKU_AUTH_ATTACHMENT_TYPE_ID_461 = 461;
    /**
     * 图片
     */
    public final static Integer SKU_AUTH_ATTACHMENT_TYPE_ID_460 = 460;
    /**
     * 附件类型附件应用类型
     * 授权书
     */
    public final static Integer SKU_AUTH_ATTACHMENT_FUNCTION_ID_4101 = 4101;

    public final static String S = "S";
    public final static String A = "A";
    public final static String B = "B";
    public final static String C = "C";

    /**
     * 经营产品类型
     */
    public final static String BUSINESS_PRODUCTS_TYPE = "BUSINESS_PRODUCTS_TYPE";

    /**
     * 经营客户类型
     */
    public final static String BUSINESS_CUSTOMER_TYPE = "BUSINESS_CUSTOMER_TYPE";

    /**
     * 总公司
     */
    public final static String HEAD_OFFICE = "总公司";

    /**
     * 分公司
     */
    public final static String BRANCH_OFFICE = "分公司";

    /**
     * 临床医疗
     */
    public final static Integer CLINICAL_MEDICINE = 427;

    /**
     * 终端
     */
    public final static Integer TERMINAL = 466;

    /**
     * 资产日志操作类型
     */
    public static final int OPER_ADD = 0;
    public static final int OPER_LESSEN = 1;
    public static final int OPER_UNCHANGING = 2;


    /**
     * @Fields ID_242 :  沟通记录类型:客户ID
     */
    public static final Integer ID_242 = 242;

    /**
     * @Fields ID_243 :  沟通记录类型:供应商ID
     */
    public static final Integer ID_243 = 243;

    /**
     * @Fields ID_244 :  沟通记录类型:询价ID
     */
    public static final Integer ID_244 = 244;

    /**
     * @Fields ID_245 :  沟通记录类型:报价ID
     */
    public static final Integer ID_245 = 245;

    /**
     * @Fields ID_246 :  沟通记录类型:销售订单ID
     */
    public static final Integer ID_246 = 246;

    /**
     * @Fields ID_247 :  沟通记录类型:采购订单ID
     */
    public static final Integer ID_247 = 247;

    /**
     * @Fields ID_248 :  沟通记录类型:售后订单ID
     */
    public static final Integer ID_248 = 248;

    /**
     * 沟通类型 线索
     */
    public static final Integer ID_4083 = 4083;
}
