<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@attribute name="item" type="java.lang.Object" required="true" %>
<%@attribute name="quote" type="java.lang.Object" required="true" %>
<%@attribute name="userList" type="java.util.List" required="true" %>
<c:set var="goodsUserNm" value=""/>
<input type="hidden" value="${userList}" name="test111"/>

<input type="hidden" value="${item.lastReferenceUser}" name="itemlastReferenceUser111"/>
<c:forEach var="user" items="${userList}">
    <c:if test="${user.userId eq item.lastReferenceUser}"><c:set var="goodsUserNm" value="${user.username}"/></c:if>
</c:forEach>
产品经理
<c:if test="${ not empty goodsUserNm}">
    ：${goodsUserNm}
</c:if>
<br/>
参考报价
<c:choose>
    <c:when test="${item.quoteorderConsultReply != null && item.quoteorderConsultReply.referencePriceReplyStatus != null && item.quoteorderConsultReply.referencePriceReplyStatus == -1}">
        <c:choose>
            <c:when test="${item.quoteorderConsultReply.consultReplierRole == 0}">
                ：<span style="color: red">待${item.quoteorderConsultReply.consultReplierName}分配</span>
            </c:when>
            <c:otherwise>
                ：<span style="color: red">待${item.quoteorderConsultReply.consultReplierName}回复</span>
            </c:otherwise>
        </c:choose>
    </c:when>
    <c:when test="${item.quoteorderConsultReply != null && item.quoteorderConsultReply.referencePriceReplyStatus != null && item.quoteorderConsultReply.referencePriceReplyStatus == 1}">
        <span>：${item.quoteorderConsultReply.referencePriceReply}</span>
    </c:when>
</c:choose>
<br/>
参考货期
<c:choose>
    <c:when test="${item.quoteorderConsultReply != null && item.quoteorderConsultReply.referenceDeliveryCycleReplyStatus != null && item.quoteorderConsultReply.referenceDeliveryCycleReplyStatus == -1}">
        <c:choose>
            <c:when test="${item.quoteorderConsultReply.consultReplierRole == 0}">
                ：<span style="color: red">待${item.quoteorderConsultReply.consultReplierName}分配</span>
            </c:when>
            <c:otherwise>
                ：<span style="color: red">待${item.quoteorderConsultReply.consultReplierName}回复</span>
            </c:otherwise>
        </c:choose>
    </c:when>
    <c:when test="${item.quoteorderConsultReply != null && item.quoteorderConsultReply.referenceDeliveryCycleReplyStatus != null && item.quoteorderConsultReply.referenceDeliveryCycleReplyStatus == 1}">
        <span>：${item.quoteorderConsultReply.referenceDeliveryCycleReply}</span>
    </c:when>
</c:choose>
<c:if test="${quote != null && (quote.salesArea == '未知' or quote.terminalType eq 1)}">
    <div class="customername pos_rel" style="display: inline-block;">
        <i class="iconbluesigh ml4"></i>
        <div class="pos_abs customernameshow">
            <div class="table-item item-col">
                <div class="table-td">
                    终端信息未提供完整，信息仅供参考
                </div>
            </div>
        </div>
    </div>
</c:if>
<br/>
报备结果
<c:choose>
    <c:when test="${item.quoteorderConsultReply != null && item.quoteorderConsultReply.reportConsultReplyStatus != null && item.quoteorderConsultReply.reportConsultReplyStatus == -1}">
        <c:choose>
            <c:when test="${item.quoteorderConsultReply.consultReplierRole == 0}">
                ：<span style="color: red">待${item.quoteorderConsultReply.consultReplierName}分配</span>
            </c:when>
            <c:otherwise>
                ：<span style="color: red">待${item.quoteorderConsultReply.consultReplierName}回复</span>
            </c:otherwise>
        </c:choose>
    </c:when>
<%--    兼容报备咨询的老数据的展示--%>
    <c:when test="${(item.quoteorderConsultReply == null) or (item.quoteorderConsultReply != null && item.quoteorderConsultReply.reportConsultReplyStatus != null && item.quoteorderConsultReply.reportConsultReplyStatus == 1)}">
        <span>：
            <c:choose>
                <c:when test="${item.reportStatus == 2}">报备成功</c:when>
                <c:when test="${item.reportStatus == 0}">无需报备</c:when>
                <c:when test="${item.reportStatus == 3}">报备失败</c:when>
            </c:choose>
        </span>
    </c:when>
</c:choose>
<br/>
报备失败原因
<c:choose>
    <c:when test="${item.quoteorderConsultReply != null && item.quoteorderConsultReply.reportConsultReplyStatus != null && item.quoteorderConsultReply.reportConsultReplyStatus == 1}">
        <c:choose>
            <c:when test="${item.quoteorderConsultReply.reportConsultReply == 3}">
                <span>（${goodsUserNm}）：${item.quoteorderConsultReply.reportConsultReplyContent}</span>
            </c:when>
            <c:otherwise></c:otherwise>
        </c:choose>
    </c:when>
</c:choose>
<br/>
