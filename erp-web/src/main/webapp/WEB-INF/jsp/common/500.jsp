<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="异常反馈" scope="application" />
<%@ include file="common.jsp"%>
<body class="centered">
<div class="addElement" style="height:100%">
   <span  >woo！系统开了小差，准确填写详细信息，工程师才能更迅速的解决！</span>
    <br>
    <form method="post" action="/system/role/static/savefeedback.do">

       <ul style="margin-top:6px;">
           <li>
               <div class="infor_name">
                   <lable for='userName'>用户名：</lable>
               </div>
               <div class="f_left">
                   ${userName}
                   <input type="hidden" name='userName'    class="input-middle" value="${userName}"/>
               </div>
               <div class="clear"></div>
           </li>
           <li>
           <div class="infor_name">
             <lable for='url'>请求链接：</lable>
           </div>
           <div class="f_left">
               ${url}
             <input type="hidden" name='url'    class="input-middle" value="${url}"/>
           </div>
           <div class="clear"></div>

       </li>

           <li>
               <div class="infor_name">
                   <lable for='queryString'>请求参数：</lable>
               </div>
               <div class="f_left">
                   ${queryString}
                   <input type="hidden" name='queryString'    class="input-middle" value="${queryString}"/>
               </div>
               <div class="clear"></div>
           </li>

           <li>
               <div class="infor_name">
                   <lable for='userName'>请求时间：</lable>
               </div>
               <div class="f_left">
                   ${time}
                   <input type="hidden" name='time'     class="input-middle" value="${time}"/>
               </div>
               <div class="clear"></div>
           </li>
           <li>
               <div class="infor_name">
                   <lable for='ip'>ip地址：</lable>
               </div>
               <div class="f_left">
                   ${ip}
                   <input type="hidden" name='ip'    class="input-middle" value="${ip}"/>
               </div>
               <div class="clear"></div>
           </li>
           <li>
               <div class="infor_name">
                   <lable for='method'>方法类别：</lable>
               </div>
               <div class="f_left">
                   ${method}
                   <input type="hidden" name='method'    class="input-middle" value="${method}"/>
               </div>
               <div class="clear"></div>
           </li>
           <li>
               <div class="infor_name">
                   <lable for='method'>错误类型：</lable>
               </div>
               <div class="f_left">
                   ${type}
                   <input type="hidden" name='type'    class="input-middle" value="${type}"/>
               </div>
               <div class="clear"></div>
           </li>
           <li>
               <div class="infor_name">
                   <lable for='method'>错误代码：</lable>
               </div>
               <div class="f_left">

                   <input type="text" name='code' readonly   class="input-middle" value="${code}"/>
               </div>
               <div class="clear"></div>
           </li>
           <li>
               <div class="infor_name">
                   <lable for='method'>详细信息：</lable>
               </div>
               <div class="f_left">
                   <textarea rows="6" cols="61" name="content" id="content" placeholder="详细信息"></textarea>
               </div>
               <div class="clear"></div>
           </li>

         <div class="clear"></div>
       </ul>
       <div class="add-tijiao f_left" style="margin-top:50px;margin-left:100px">
         <button type="submit" class="J-submit">提交</button>
         <button  id="cancle" type="button" class="dele">取消</button>
           <a     href="<%=request.getScheme()+"://"+request.getServerName()+":"+request.getServerPort()+"/index.do" %>">跳转首页</a>

       </div>
     </form>

    <div style="display:none"><iframe src="/stat/500?${queryString}"></iframe></div>
</div>

</body>
</html>