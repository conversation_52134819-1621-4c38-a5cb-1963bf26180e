// javascript for selectTermial.jsp

$(function () {
    var saleOrderId = $('[name=saleorderId]').val();

    var checkTerminalDelShow = function() {
        if($('.J-terminal-table-item').length > 1) {
            $('.J-terminal-del').show();
        } else {
            $('.J-terminal-del').hide();
        }
    };

    var getTerminalList = function () {
        $.ajax({
            url: '/order/terminal/listForSaleOrder.do?saleOrderId=' + saleOrderId,
            dataType: 'json',
            success: function (res) {
                if (res.code === 0) {
                    var terminalList = res.data || [];
                    $('.J-terminal-list-body').empty();
                    if (terminalList.length) {
                        $.each(terminalList, function (i, item) {
                            console.log(item)
                            appendItem(item);
                        })
                    } else {
                        appendItem();
                    }

                    checkTerminalDelShow();
                }
            }
        })
    }

    var typeOptions = '<option value="">请选择</option>';

    $.ajax({
        url: '/order/terminal/querySaleOrderTerminal.do',
        dataType: 'json',
        success: function (res) {
            if (res.code === 0) {
                var types = res.data.naturnList || [];

                $.each(types, function(i, item) {
                    typeOptions += `<option value="${item.sysOptionDefinitionId}">${item.title}</option>`;
                })

                getTerminalList();
            }
        }
    })

    window.validTerminalTraderNature = function () {
        if(!$('.J-terminal-table-item').length) {
            return true;
        }

        var flag = true;
        $('.J-terminal-table-item').each(function() {
            var $select = $(this).find('.J-name-terminalTraderNature');
            if(!$select.val()) {
                $select.addClass('error');
                flag = false;
            } else {
                $select.removeClass('error');
            }
        })

        if(!flag) {
            $('.J-terminal-table-error').show();
        } else {
            $('.J-terminal-table-error').hide();
        }

        return flag;
    };

    var initTableItem = function($item, data) {
        $item.find('.J-name-terminalTraderNature').append(typeOptions);

        if(data) {
            $item.find('.J-name-beforeTerminalName').val(data.terminalName || '');
            $item.find('.J-name-beforeOrderTerminalId').val(data.id);
            $item.find('.J-terminal-del').data('id', data.id);
            $.each(['contractUser', 'contractMobile', 'detailAddress', 'provinceName', 'provinceCode', 'cityCode', 'cityName', 'dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode', 'terminalTraderNature'], function (i, item) {
                $item.find('.J-name-' + item).val(data[item] || '');
            })
        }

        $item.find('.J-terminal-name-placeholder').each(function () {
            new SearchRelated({
                el: $(this),
                remoteInfo: {
                    url: '/order/terminal/searchOneData.do',
                    method: 'get',
                    paramsKey: 'terminalTraderName',
                    hasPage: true,
                    parseData: (res) => {
                        return res.data.list || [];
                    },
                    parseLabel: 'terminalName',
                    parseLabelRight: '',
                },
                maxlength: '100',
                onchange: function (val, data) {
                    // console.log('change', val, data);
                    $.each(['dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode'], function (i, item) {
                        $item.find('.J-name-' + item).val(data[item] || '')
                    })

                    if(data.cityId) {
                        $item.find('.J-name-cityCode').val(data.cityId)[0].triggerChange();
                    }

                    submitData($item);
                },
                openTyc: function () {
                    openTycList($item.find('.J-search-input').val().trim(), $item);
                }
            })

            if(data) {
                $item.find('.J-terminal-name-placeholder .J-search-input').val(data.terminalName || '');
            }
        });

        var submitTimeout = null;

        $item.find('.J-terminal-address-placeholder').each(function () {
            var $this = $(this);
            var $value1 = $this.siblings('.J-name-provinceCode');
            var $value2 = $this.siblings('.J-name-cityCode');

            var firstInit = true;

            new Caecader({
                el: $(this),
                url: '/region/getCascaderRegionOptions.do',
                filterable: true,
                input: $(this).siblings('.J-name-cityCode'),
                level: 2,
                onchange: function (labels, values) {
                    $value1.val(values[0]);
                    $value2.val(values[1]);

                    $item.find('.J-name-provinceName').val(labels[0]);
                    $item.find('.J-name-cityName').val(labels[1]);

                    if(!firstInit) {
                        submitTimeout && clearTimeout(submitTimeout);

                        submitTimeout = setTimeout(function() {
                            submitData($item);
                        }, 500)
                    }
                }
            })
            
            setTimeout(function() {
                firstInit = false;
            }, 1000)
        })
    };

    var appendItem = function(data) {
        $('.J-terminal-list-body').append($('.J-terminal-table-item-tmpl').html());

        initTableItem($('.J-terminal-table-item').last(), data);
        validTerminalTraderNature();
        checkTerminalDelShow();
    };

    $('.J-terminal-list-body').on('input', '.J-name-contractMobile', function () {
        $(this).val($(this).val().replace(/[^\d]/g, ''));
    })

    //打开终端新增、编辑弹层
    $(document).on('click', '.J-terminal-edit', function () {
        appendItem()
    })

    var clearTerminalInfo = function ($item) {
        $.each(['dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode'], function(i, item) {
            $item.find('.J-name-' + item).val('')
        })
    }

    var cansubmit = true;

    var submitData = function($item) {
        var reqData = {
            saleOrderId: saleOrderId,
            natureTypeName: $item.find('.J-name-terminalTraderNature option:selected').html().trim() || '',
            terminalName: $item.find('.J-terminal-name-placeholder .J-search-input').val() || '',
        };

        $.each(['terminalTraderNature', 'dwhTerminalId', 'unifiedSocialCreditIdentifier', 'organizationCode', 'provinceCode', 'provinceName', 'cityCode', 'cityName', 'detailAddress', 'contractUser', 'contractMobile', 'beforeTerminalName', 'beforeOrderTerminalId'], function (i, item) {
            reqData[item] = $item.find('.J-name-' + item).val() || '';
        })

        if(!reqData.terminalTraderNature) {
            return;
        }

        var id = $item.find('.J-terminal-del').data('id')
        
        if(!cansubmit) {
            return false;
        }

        if(!id) {
            cansubmit = false;
        }

        $.ajax({
            url: '/order/terminal/addNew.do',
            type: 'post',
            contentType: 'application/json',
            data: JSON.stringify(reqData),
            dataType: 'json',
            success: function (res) {
                cansubmit = true;
                if (res.code === 0) {
                    $('#terminalTraderName').val(reqData.terminalName);
                    if(!id) {
                        $item.find('.J-name-beforeOrderTerminalId').val(res.data);
                        $item.find('.J-name-beforeTerminalName').val(reqData.terminalName);
                        $item.find('.J-terminal-del').data('id', res.data)
                    }
                }
            },
            error: function() {
                cansubmit = true;
            }
        })
    };

    $('.J-terminal-list-body').on('blur', '.J-name-contractUser, .J-name-detailAddress, .J-name-contractMobile', function() {
        submitData($(this).parents('.J-terminal-table-item:first'));
    })

    $('.J-terminal-list-body').on('change', '.J-name-terminalTraderNature', function() {
        submitData($(this).parents('.J-terminal-table-item:first'));
        
        validTerminalTraderNature();
    })

    //获取天眼查列表
    var getTycList = function () {
        var searchValue = $('.J-tyc-search-input').val().trim();
        if (searchValue.length < 4) {
            layer.msg('请输入不低于4个字进行查询', { icon: 7 });

            return;
        }

        var loadIndex = layer.load(2);

        $.ajax({
            url: '/order/terminal/searchTyc.do',
            data: {
                terminalTraderName: searchValue,
                pageSize: 20,
                pageNum: 1
            },
            dataType: 'json',
            success: function (res) {
                layer.close(loadIndex);

                if (res.code === 0) {
                    var list = res.data.list || [];

                    if (!list.length) {
                        $('.J-tyc-empty').show();
                        $('.J-tyc-table').hide();
                        layer.style(layerTycIndex, {
                            top: ($(window).height() - $tycDialog.height()) / 2 + 'px'
                        })
                        return;
                    } else {
                        $('.J-tyc-empty').hide();
                        $('.J-tyc-table').show();
                    }

                    if (list.length < 10) {
                        $('.J-tyc-list').removeClass('on-scroll');
                    } else {
                        $('.J-tyc-list').addClass('on-scroll');
                    }

                    $('.J-tyc-list').empty();

                    $.each(list, function (i, item) {

                        let companyType = ['普通公司', '香港公司', '社会组织', '律所', '事业单位', '基金'][item.companyType - 1] || '';

                        $('.J-tyc-list').append(`
                            <div class="list-table-tr">
                                <div class="td-item">
                                    <div class="text-line-1" title="${item.terminalName}">${item.terminalName}</div>
                                </div>
                                <div class="td-item">
                                    <div class="text-line-1" title="${companyType}">${companyType || '&nbsp;'}</div>
                                </div>
                                <div class="td-item">
                                    <div class="text-line-1" title="${item.address}">${item.address || '&nbsp;'}</div>
                                </div>
                                <div class="td-item">
                                    <div class="td-option J-tyc-list-choose" data-name="${item.terminalName}">选择</div>
                                </div>
                            </div>
                        `);
                    })

                    $('.J-tyc-list-choose').click(function () {
                        var terminalName = $(this).data('name') || '';

                        $edititem.find('.J-search-input').val(terminalName);
                        clearTerminalInfo($edititem);
                        layer.close(layerTycIndex);

                        submitData($edititem);
                    })

                    layer.style(layerTycIndex, {
                        top: ($(window).height() - $tycDialog.height()) / 2 + 'px'
                    })
                } else {
                    layer.alert(res.message || '请求失败，请重新尝试')
                }
            }

        })
    }

    var $tycDialog;
    var layerTycIndex;
    var $edititem = null;

    //打开天眼查弹层
    var openTycList = function (keyword, $item) {
        if (keyword.trim().length < 4) {
            layer.msg('请输入不低于4个字进行查询', { icon: 7 });

            return;
        }

        $edititem = $item;

        layerTycIndex = layer.open({
            type: 1,
            title: '天眼查查询',
            area: ['960px', 'auto'],
            offset: 'auto',
            content: $('.J-terminal-tyc-tmpl').html(),
            success: function (layero) {
                $tycDialog = $(layero);
            }
        })

        $tycDialog.find('.layui-layer-content').css('height', 'auto');

        if (keyword) {
            $tycDialog.find('.J-tyc-search-input').val(keyword);
        }

        getTycList();
    }

    $(document).on('keydown', '.J-tyc-search-input', function (e) {
        if (e.keyCode === 13) {
            getTycList();
        }
    })

    $(document).on('click', '.J-tyc-search-btn', function () {
        getTycList();
    })

    // refreshTerminalList();

    $(document).on('click', '.J-terminal-del', function () {
        var id = $(this).data('id');
        var $wrap = $(this).parents('.J-terminal-table-item:first');
        
        if(id) {
            layer.load(2);
            
            $.ajax({
                url: '/order/terminal/deleteForSaleOrder.do?id=' + id,
                data: {},
                dataType: 'json',
                success: function (res) {
                    layer.closeAll();
                    if (res.code === 0) {
                        $wrap.remove();
                        checkTerminalDelShow();
                        validTerminalTraderNature();
                    } 
                }
            })
        } else {
            $wrap.remove();
            checkTerminalDelShow();
            validTerminalTraderNature();
        }
    })
})