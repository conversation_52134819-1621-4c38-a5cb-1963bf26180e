package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class InvoiceReversalEntity extends BaseEntity {
    /**
     * 主键  INVOICE_REVERSAL_ID
     **/
    private Integer invoiceReversalId;

    /**
     * ERP发票主表ID  INVOICE_ID
     **/
    private Integer invoiceId;

    /**
     * 发票代码  INVOICE_CODE
     **/
    private String invoiceCode;

    /**
     * 发票号码  INVOICE_NO
     **/
    private String invoiceNo;

    /**
     * 销方名称（开票方名称）  SALE_NAME
     **/
    private String saleName;

    /**
     * 所属单据类型 1.采购售后单 2.采购售后费用单  REVERSAL_BILL_TYPE
     **/
    private Integer reversalBillType;

    /**
     * 所属单据单号  REVERSAL_BILL_NO
     **/
    private String reversalBillNo;

    /**
     * 发票类型(数据字典PARENTID=428)  INVOICE_TYPE
     **/
    private Integer invoiceType;

    /**
     * 冲销审核人ID  REVERSAL_AUDIT_USER
     **/
    private Integer reversalAuditUser;

    /**
     * 冲销审核 0待审核 1通过 不通过  REVERSAL_AUDIT_STATUS
     **/
    private Integer reversalAuditStatus;

    /**
     * 冲销审核申请时间  AUDIT_APPLY_TIME
     **/
    private Date auditApplyTime;

    /**
     * 冲销审核备注  AUDIT_COMMENTS
     **/
    private String auditComments;

    /**
     * 是否删除 0否 1是  IS_DELETE
     **/
    private Integer isDelete;

    /**
     * 更新备注  UPDATE_REMARK
     **/
    private String updateRemark;
}