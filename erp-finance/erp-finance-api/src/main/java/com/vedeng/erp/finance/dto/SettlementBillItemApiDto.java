package com.vedeng.erp.finance.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务结算单明细
 * @date 2023/11/16 10:41
 */
@Getter
@Setter
public class SettlementBillItemApiDto extends BaseDto {

    /**
     * 主键
     */
    private Integer settleItemBillId;

    /**
     * 结算单ID
     */
    private Integer settleBillId;

    /**
     * 结算单号
     */
    private String settleBillNo;

    /**
     * 结算类型
     * 1.返利
     * 2.余额
     * 3.银行
     * 4.信用额度
     */
    private Integer settlementType;

    /**
     * 交易方向1收入2支出3转移4转入5转出6反入7反出
     */
    private Integer traderDirection;

    /**
     * 业务单据类型
     * saleOrder.销售单
     * buyOrder.采购单
     * buyOrderExpense.采购费用单
     * buyOrderAfterSale.采购售后单
     * buyOrderExpenseAfterSale.采购费用售后单
     */
    private String businessType;

    /**
     * 业务单号
     */
    private String businessNo;

    /**
     * 业务单ID
     */
    private Integer businessId;

    /**
     * 业务明细单ID
     */
    private Integer businessItemId;

    /**
     * 结算产品名称
     */
    private String productName;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private BigDecimal number;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 发票状态
     * 0：无发票
     * 1：部分有票
     * 2：全部有票
     */
    private Integer invoiceStatus;

    /**
     * 结算状态
     * 0：未结算
     * 1：已结算
     */
    private Integer settlementStatus;

    /**
     * 是否删除
     */
    private Integer isDelete;

}
