<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerFinanceExtMapper">

  <select id="getTraderFinancialById" resultType="com.vedeng.erp.trader.domain.dto.TraderFinanceDetail">
    select
      a.TRADER_CUSTOMER_FINANCE_ID,
      a.TRADER_ID,
      b.TRADER_NAME,
      c.CUSTOMER_NATURE customerType,
      a.CUSTOMER_NAME_NEW,
      a.CUSTOMER_NATURE,
      a.CUSTOMER_CLASS,
      a.GROUP_NAME,
      a.CUSTOMER_SECOND_TYPE,
      a.CUSTOMER_THIRD_TYPE,
      a.HOSPITAL_LEVER,
      a.HOSPITAL_NAME,
      date_format( c.ADD_TIME,'%Y-%m-%d %H:%i:%s') addTimeStr,
      date_format( a.PUSH_TIME,'%Y-%m-%d %H:%i:%s') pushTimeStr,
      a.IS_PUSH,
      d.USER_ID,
      date_format( a.MOD_TIME,'%Y-%m-%d %H:%i:%s') modTimeStr
    from T_TRADER_CUSTOMER_FINANCE a
           left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
           left join T_TRADER_CUSTOMER c on a.TRADER_ID = c.TRADER_ID
           left join T_R_TRADER_J_USER d on d.TRADER_ID = a.TRADER_ID and d.TRADER_TYPE = 1
    where  a.TRADER_CUSTOMER_FINANCE_ID = #{traderCustomerFinanceId,jdbcType=INTEGER}
    limit 1
  </select>

  <insert id="batchInsert" keyColumn="TRADER_CUSTOMER_FINANCE_ID" keyProperty="traderCustomerFinanceId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_CUSTOMER_FINANCE
    (TRADER_ID, CUSTOMER_NAME_NEW, CUSTOMER_NATURE, CUSTOMER_CLASS, GROUP_NAME, CUSTOMER_SECOND_TYPE,
    CUSTOMER_THIRD_TYPE, HOSPITAL_LEVER, HOSPITAL_NAME, IS_PUSH, IS_DELETE,
    ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
    )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderId,jdbcType=INTEGER}, #{item.customerNameNew,jdbcType=VARCHAR}, #{item.customerNature,jdbcType=BOOLEAN},
      #{item.customerClass,jdbcType=BOOLEAN}, #{item.groupName,jdbcType=VARCHAR}, #{item.customerSecondType,jdbcType=BOOLEAN},
      #{item.customerThirdType,jdbcType=BOOLEAN}, #{item.hospitalLever,jdbcType=BOOLEAN},
      #{item.hospitalName,jdbcType=VARCHAR}, #{item.isPush,jdbcType=BOOLEAN},
      #{item.isDelete,jdbcType=BOOLEAN}, #{item.addTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
      #{item.creatorName,jdbcType=VARCHAR}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.updater,jdbcType=INTEGER},
      #{item.updaterName,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.updateRemark,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
  
  <insert id="batchInsertSelect" keyColumn="TRADER_CUSTOMER_FINANCE_ID" keyProperty="traderCustomerFinanceId" parameterType="java.util.List" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert IGNORE into T_TRADER_CUSTOMER_FINANCE
    (TRADER_ID)
    values
    <foreach collection="list" item="traderId" separator=",">
      (#{traderId,jdbcType=INTEGER})
    </foreach>
  </insert>


  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_FINANCE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_NAME_NEW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerNameNew,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_NATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerNature,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_CLASS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerClass,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="GROUP_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.groupName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_SECOND_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerSecondType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="CUSTOMER_THIRD_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerThirdType,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="HOSPITAL_LEVER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.hospitalLever,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="HOSPITAL_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.hospitalName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_PUSH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.isPush,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PUSH_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.pushTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where TRADER_CUSTOMER_FINANCE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.traderCustomerFinanceId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_TRADER_CUSTOMER_FINANCE
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}

        </foreach>
      </trim>
      <trim prefix="CUSTOMER_NAME_NEW = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerNameNew,jdbcType=VARCHAR}

        </foreach>
      </trim>
      <trim prefix="CUSTOMER_NATURE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerNature,jdbcType=BOOLEAN}

        </foreach>
      </trim>
      <trim prefix="CUSTOMER_CLASS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerClass,jdbcType=BOOLEAN}

        </foreach>
      </trim>
      <trim prefix="GROUP_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.groupName,jdbcType=VARCHAR}

        </foreach>
      </trim>
      <trim prefix="CUSTOMER_SECOND_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerSecondType,jdbcType=BOOLEAN}

        </foreach>
      </trim>
      <trim prefix="CUSTOMER_THIRD_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.customerThirdType,jdbcType=BOOLEAN}

        </foreach>
      </trim>
      <trim prefix="HOSPITAL_LEVER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.hospitalLever,jdbcType=BOOLEAN}

        </foreach>
      </trim>
      <trim prefix="HOSPITAL_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">

            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.hospitalName,jdbcType=VARCHAR}

        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_PUSH = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isPush != null">
            when TRADER_CUSTOMER_FINANCE_ID = #{item.traderCustomerFinanceId,jdbcType=INTEGER} then #{item.isPush,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
    </trim>
    where TRADER_CUSTOMER_FINANCE_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.traderCustomerFinanceId,jdbcType=INTEGER}
    </foreach>
  </update>

  <select id="getTraderFinancialByTraderId" resultType="com.vedeng.erp.trader.domain.entity.TraderCustomerFinance">
    select
    a.TRADER_CUSTOMER_FINANCE_ID,
    a.TRADER_ID
    from T_TRADER_CUSTOMER_FINANCE a
    left join T_TRADER b on a.TRADER_ID = b.TRADER_ID
    left join T_TRADER_CUSTOMER c on a.TRADER_ID = c.TRADER_ID
    left join T_R_TRADER_J_USER d on d.TRADER_ID = a.TRADER_ID
    where   d.TRADER_TYPE = 1 and a.TRADER_ID = #{traderId,jdbcType=INTEGER}
    limit 1
  </select>
  
  <select id="selectByTraderIds" resultType="java.lang.Integer" parameterType="java.util.List">
     SELECT TRADER_ID FROM T_TRADER_CUSTOMER_FINANCE WHERE IS_DELETE=0 AND TRADER_ID
     IN
     <foreach close=")" collection="list" item="traderId" open="(" separator=", ">
      #{traderId,jdbcType=INTEGER}
    </foreach>
  </select>
  
</mapper>