package com.vedeng.erp.aftersale.dto;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @description 销售退货 含虚拟商品采销联动逆向
 * @date 2023/1/5 15:34
 **/
@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class AfterSaleOrder2ExpenseDto {

    /**
     * 售后id
     */
    private Integer afterSalesId;
    /**
     * 售后单号
     */
    private String afterSalesNo;
    /**
     * 售后类型，字典表
     */
    private Integer type;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 订单id
     */
    private Integer orderId;
    /**
     * 是否生效0否1是
     */
    private Integer validStatus;
    /**
     * 售后状态0待确认1进行中2已完结3已关闭
     */
    private Integer atferSalesStatus;

    /**
     * 查询到商品
     */
    private List<AfterSaleGoods2ExpenseDto> afterSaleGoods2ExpenseDtos;

}
