<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.OrderAssistantJProductPositionMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.OrderAssistantJProductPositionDo">
    <id column="ORDER_ASSISTANT_J_PRODUCT_POSITION_ID" jdbcType="INTEGER" property="orderAssistantJProductPositionId" />
    <result column="ORDER_ASSSISTANT_USER_ID" jdbcType="INTEGER" property="orderAsssistantUserId" />
    <result column="PRODUCT_POSITOIN_USER_ID" jdbcType="INTEGER" property="productPositoinUserId" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="UPDATA_TIME" jdbcType="BIGINT" property="updataTime" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="POSITION_LEVEL" jdbcType="INTEGER" property="positionLevel" />
  </resultMap>
  <sql id="Base_Column_List">
    ORDER_ASSISTANT_J_PRODUCT_POSITION_ID, ORDER_ASSSISTANT_USER_ID, PRODUCT_POSITOIN_USER_ID, 
    ADD_TIME, UPDATA_TIME, IS_DELETE, CREATOR, UPDATER, POSITION_LEVEL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
    where ORDER_ASSISTANT_J_PRODUCT_POSITION_ID = #{orderAssistantJProductPositionId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
    where ORDER_ASSISTANT_J_PRODUCT_POSITION_ID = #{orderAssistantJProductPositionId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ORDER_ASSISTANT_J_PRODUCT_POSITION_ID" keyProperty="orderAssistantJProductPositionId" parameterType="com.vedeng.order.model.OrderAssistantJProductPositionDo" useGeneratedKeys="true">
    insert into T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION (ORDER_ASSSISTANT_USER_ID, PRODUCT_POSITOIN_USER_ID, 
      ADD_TIME, UPDATA_TIME, IS_DELETE, 
      CREATOR, UPDATER, POSITION_LEVEL
      )
    values (#{orderAsssistantUserId,jdbcType=INTEGER}, #{productPositoinUserId,jdbcType=INTEGER}, 
      #{addTime,jdbcType=BIGINT}, #{updataTime,jdbcType=BIGINT}, #{isDelete,jdbcType=TINYINT}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{positionLevel,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="ORDER_ASSISTANT_J_PRODUCT_POSITION_ID" keyProperty="orderAssistantJProductPositionId" parameterType="com.vedeng.order.model.OrderAssistantJProductPositionDo" useGeneratedKeys="true">
    insert into T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderAsssistantUserId != null">
        ORDER_ASSSISTANT_USER_ID,
      </if>
      <if test="productPositoinUserId != null">
        PRODUCT_POSITOIN_USER_ID,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updataTime != null">
        UPDATA_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="positionLevel != null">
        POSITION_LEVEL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderAsssistantUserId != null">
        #{orderAsssistantUserId,jdbcType=INTEGER},
      </if>
      <if test="productPositoinUserId != null">
        #{productPositoinUserId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updataTime != null">
        #{updataTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="positionLevel != null">
        #{positionLevel,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.OrderAssistantJProductPositionDo">
    update T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
    <set>
      <if test="orderAsssistantUserId != null">
        ORDER_ASSSISTANT_USER_ID = #{orderAsssistantUserId,jdbcType=INTEGER},
      </if>
      <if test="productPositoinUserId != null">
        PRODUCT_POSITOIN_USER_ID = #{productPositoinUserId,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updataTime != null">
        UPDATA_TIME = #{updataTime,jdbcType=BIGINT},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="positionLevel != null">
        POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER},
      </if>
    </set>
    where ORDER_ASSISTANT_J_PRODUCT_POSITION_ID = #{orderAssistantJProductPositionId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.OrderAssistantJProductPositionDo">
    update T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION
    set ORDER_ASSSISTANT_USER_ID = #{orderAsssistantUserId,jdbcType=INTEGER},
      PRODUCT_POSITOIN_USER_ID = #{productPositoinUserId,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATA_TIME = #{updataTime,jdbcType=BIGINT},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER}
    where ORDER_ASSISTANT_J_PRODUCT_POSITION_ID = #{orderAssistantJProductPositionId,jdbcType=INTEGER}
  </update>
  <select id="getProductUserByOrderAssistantId" resultType="com.vedeng.authorization.model.User">
    select U.* from T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION OJP
    inner join T_USER U on OJP.PRODUCT_POSITOIN_USER_ID = U.USER_ID
    where OJP.ORDER_ASSSISTANT_USER_ID = #{orderAssitantUserId,jdbcType = INTEGER}
    and OJP.POSITION_LEVEL = #{positionLevel,jdbcType = INTEGER}
    and OJP.IS_DELETE = 0
    and U.IS_DISABLED = 0
  </select>
  <select id="getAllProductUserByType" resultType="com.vedeng.order.model.OrderAssistantJProductPositionDo">
    select * from T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION OJP
    where ORDER_ASSSISTANT_USER_ID = #{orderAsssistantUserId}
    and POSITION_LEVEL = #{positionLevel,jdbcType=INTEGER}
    and IS_DELETE = 0
  </select>
  <insert id="batchInsert"  keyColumn="ORDER_ASSISTANT_J_PRODUCT_POSITION_ID" keyProperty="orderAssistantJProductPositionId" parameterType="com.vedeng.order.model.OrderAssistantJProductPositionDo">
    insert into T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION (ORDER_ASSSISTANT_USER_ID, PRODUCT_POSITOIN_USER_ID,
    ADD_TIME, UPDATA_TIME, IS_DELETE,
    CREATOR, UPDATER, POSITION_LEVEL
    )
    values
    <foreach collection="saveProductUserList" item="list" separator="," >
      (#{list.orderAsssistantUserId,jdbcType=INTEGER}, #{list.productPositoinUserId,jdbcType=INTEGER},
      #{list.addTime,jdbcType=BIGINT}, #{list.updataTime,jdbcType=BIGINT}, #{list.isDelete,jdbcType=TINYINT},
      #{list.creator,jdbcType=INTEGER}, #{list.updater,jdbcType=INTEGER}, #{list.positionLevel,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
  <update id="batchDeleteByOrderAsssistantUserId">
     update T_R_ORDER_ASSISTANT_J_PRODUCT_POSITION SET IS_DELETE = 1
     WHERE ORDER_ASSSISTANT_USER_ID = #{orderAsssistantUserId,jdbcType=INTEGER}
  </update>
</mapper>