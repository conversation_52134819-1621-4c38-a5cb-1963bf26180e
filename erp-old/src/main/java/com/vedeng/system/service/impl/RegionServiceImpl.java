package com.vedeng.system.service.impl;

import com.vedeng.aftersales.dao.AfterSalesLongDistanceFeeMapper;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.vo.RegionVo;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.system.service.RegionService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service("regionService")
public class RegionServiceImpl extends BaseServiceimpl implements RegionService {

	@Autowired
	@Qualifier("regionMapper")
	private RegionMapper regionMapper;
	@Autowired
	private AfterSalesLongDistanceFeeMapper afterSalesLongDistanceFeeMapper;

	@SuppressWarnings("unchecked")
	@Override
	public
	List<Region> getRegionCityAll(){
		return regionMapper.getRegionCityAll();
	}
	@SuppressWarnings("unchecked")
	@Override
	public List<Region> getRegionByParentId(Integer parentId) {
		if(parentId==null){
			return null;
		}
		if(parentId==1&&ErpConst.ROOT_REGION==100000){
			parentId=ErpConst.ROOT_REGION;
		}
		List<Region> list = null;
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_REGION_LIST + parentId)) {

			String json = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_REGION_LIST + parentId);
			//JedisUtils.del(dbType + ErpConst.KEY_PREFIX_REGION_LIST + parentId);
			JSONArray jsonArray = JSONArray.fromObject(json);
			list = (List<Region>) JSONArray.toCollection(jsonArray, Region.class);
		} else {
			list = regionMapper.getRegionByParentId(parentId);
			JedisUtils.set(dbType + ErpConst.KEY_PREFIX_REGION_LIST + parentId,
					JsonUtils.convertConllectionToJsonStr(list), cacheSecond);
		}
		return list;
	}

	@Override
	public Object getRegion(Integer regionId, Integer returnType) {
		try {
			if (regionId <= 0) {
				return null;
			}

			if (regionId < ErpConst.ROOT_REGION) {
				try {
					Region region = regionMapper.getRegionById(regionId);
					if (region != null) {
						regionId = Integer.parseInt(region.getRegionCode());
					}
				} catch (Exception e) {
//				logger.error("",e);
				}
			}
			List<Region> returnList = new ArrayList<Region>();
			List<String> regionName = new ArrayList<>();
			String returnString = "";

			// 查询当前地区信息
			Region region = null;
			if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId)) {

				String json = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId);
				//(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId);
				JSONObject jsonObject = JSONObject.fromObject(json);
				region = (Region) JSONObject.toBean(jsonObject, Region.class);
			} else {
				region = regionMapper.getRegionById(regionId);
			}


			if (region != null) {
				Integer parentId = region.getParentId();
				returnList.add(region);
				regionName.add(region.getRegionName());
				do {
					Region regionInfo = null;
					if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + parentId)) {
						String json = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + parentId);
						//(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + parentId);
						JSONObject jsonObject = JSONObject.fromObject(json);
						regionInfo = (Region) JSONObject.toBean(jsonObject, Region.class);
					} else {
						regionInfo = regionMapper.getRegionById(parentId);
					}
					parentId = regionInfo.getParentId();
					returnList.add(regionInfo);
					regionName.add(regionInfo.getRegionName());
				} while (parentId > 0);

				Collections.reverse(returnList);
				Collections.reverse(regionName);

				for (String name : regionName) {
					returnString += name + " ";
				}
			}


			if (returnType == 1) {
				return returnList;
			} else {
				return returnString;
			}
		}catch(Exception e){
			logger.error(""+regionId+ returnType,e);
			return null;
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 根据主键查询
	 * 
	 * @param
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年10月13日 上午11:14:20
	 */
	@Override
	public Region getRegionByRegionId(Integer regionId) {
		Region region = null;
		try {
			Region region2 = regionMapper.getRegionById(regionId);
			if (region2 != null && region2.getRegionCode() != null) {
				regionId = Integer.parseInt(region2.getRegionCode());
			}
		}catch (Exception e){
			// AROP-5394 注释掉错误日志打印
			// logger.error("",e);
		}
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId)) {
			String json = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId);
			if (json == null || "".equals(json)) {
				return null;
			}
			JSONObject jsonObject = JSONObject.fromObject(json);
			//(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId);
			region = (Region) JSONObject.toBean(jsonObject, Region.class);
		} else {
			region = regionMapper.getRegionById(regionId);
			if (region != null) {
				JedisUtils.set(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + regionId,
						JsonUtils.convertObjectToJsonStr(region), cacheSecond);
			}
		}
		return region;
	}

	@Override
	public Region getRegion(Region region) {
		return regionMapper.getRegion(region);
	}

	/**
	 * <b>Description:</b><br>
	 * 查询分页
	 * 
	 * @param region
	 * @param page
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2018年3月16日 上午10:57:06
	 */
	@Override
	public List<Region> getRegionListPage(Region region, Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("page", page);
		map.put("region", region);
		return regionMapper.getRegionListPage(map);
	}

	/**
	 * <b>Description:</b><br>
	 * 新增保存
	 * 
	 * @param regionVo
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2018年3月16日 下午1:39:06
	 */
	@Transactional
	@Override
	public int saveRegin(RegionVo regionVo) {
//		if (regionVo != null && ObjectUtils.isEmpty(regionVo.getZone())
//				&& ObjectUtils.notEmpty(regionVo.getCityName())) {// 添加市
//			// 验证当前添加城市是否已存在
//			Region region = new Region();
//			region.setParentId(regionVo.getProvince());
//			region.setRegionName(regionVo.getCityName());
//			region.setRegionType(2);
//			List<Region> cityList = regionMapper.getRegionByParam(region);
//			if (cityList != null && cityList.size() > 0) {
//				return -1;// 城市已存在
//			}
//			int i = regionMapper.insert(region);
//			if (i > 0) {
//				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_REGION_LIST + regionVo.getProvince());
//
//				return region.getRegionId();
//			}
//			return 0;
//
//		} else if (regionVo != null && ObjectUtils.notEmpty(regionVo.getZone())) {
//			// 验证当前添加区县是否已存在
//			Region region = new Region();
//			region.setParentId(regionVo.getCity());
//			region.setRegionName(regionVo.getZone());
//			region.setRegionType(3);
//			List<Region> zoneList = regionMapper.getRegionByParam(region);
//			if (zoneList != null && zoneList.size() > 0) {
//				return -2;// 区县已存在
//			}
//			int i = regionMapper.insert(region);
//			if (i > 0) {
//				JedisUtils.del(dbType + ErpConst.KEY_PREFIX_REGION_LIST + regionVo.getCity());
//				return region.getRegionId();
//			}
//			return 0;
//		}
		return 0;
	}

	/**
	 * <b>Description:</b><br>
	 * 编辑保存
	 * 
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2018年3月16日 下午1:39:06
	 */
	@Transactional
	@Override
	public int saveEditRegin(Region region) {
//		int i = regionMapper.update(region);
//		if (i > 0) {
//			JedisUtils.del(dbType + ErpConst.KEY_PREFIX_REGION_LIST + region.getParentId());
//			JedisUtils.del(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + region.getRegionId());
//		}
		return 0;
	}

	@Override
	public RegionVo getRegionByArea(String area) {
		String[] addressArr = area.split(" ");

		RegionVo regionVo = new RegionVo();
		if (null != addressArr && null != addressArr[0] && !addressArr[0].equals("")) {
			regionVo.setProvinceName(addressArr[0]);
			if (null != addressArr && null != addressArr[1] && !addressArr[1].equals("")) {
				regionVo.setCityName(addressArr[1]);
				if (null != addressArr && null != addressArr[2] && !addressArr[2].equals("")) {
					regionVo.setZone(addressArr[2]);
				}
			}
		} else {
			return null;
		}

		return regionMapper.getRegionByArea(regionVo);
	}

	@Override
	public String getRegionIdStringByMinRegionId(Integer minRegionId) {
		if (null == minRegionId) {
			return "";
		}
//		if(minRegionId<ErpConst.ROOT_REGION){
//			try {
//				Region region2 = regionMapper.getRegionById(minRegionId);
//				if (region2 != null) {
//					minRegionId = Integer.parseInt(region2.getRegionCode());
//				}
//			}catch (Exception e){
//				logger.error("",e);
//			}
//			return minRegionId/10000*10000+","+minRegionId/100*100+","+minRegionId;
//		}
		// 根据当前地区ID查询省市区的ID以,分割
		return regionMapper.getRegionIdStringByMinRegionId(minRegionId);
	}

	@Override
	public List<RegionVo> getCityList(RegionVo regionVo) {
		regionVo.setCityEnd(35);
		List<RegionVo> list = regionMapper.getCityList(regionVo);
		return list;
	}


	@Override
	public String getRegionNameStringByMinRegionIds(String regionIds) {
		String[] regionIdArray = StringUtils.split(regionIds, ",");
		StringBuilder sb = new StringBuilder();
		if (ArrayUtils.isNotEmpty(regionIdArray)) {
			for (String regionId : regionIdArray) {
				Region re = getRegionByRegionId(NumberUtils.toInt(regionId));
				if (null!=re && null!=re.getRegionName()){
					sb.append(re.getRegionName() + " ");
				}
			}
			return sb.toString();
		}
		return "N/A";
	}
	@Override
	public List<Region> getRegionInfoByMinRegionId(Integer areaId) {
		List<Region> result = new ArrayList<>();
		Integer parentId = 0;
		Integer regionId = 0;
		Region region = regionMapper.getRegionFullNameById(areaId);
		if(region == null){return result;}
		result.add(region);
		parentId = region.getParentId();
		regionId = region.getRegionId();
		int count = 0;
		while(!parentId.equals(1) && !regionId.equals(1) && !parentId.equals(0) && !regionId.equals(0) && !parentId.equals(100000) && !regionId.equals(100000)){
			if(count > 1){break;}
			Region regionFullNameById = regionMapper.getRegionFullNameById(parentId);
			result.add(regionFullNameById);
			parentId = regionFullNameById.getParentId();
			regionId = regionFullNameById.getRegionId();
			count++;
		}
		return result.stream().sorted(Comparator.comparing(Region::getRegionId)).collect(Collectors.toList());
	}

	@Override
	public Region getRegionByReginFullName(String zone,Integer regionType,Integer parentId) {
		return this.regionMapper.getRegionByReginFullName(zone,regionType,parentId);
	}

	@Override
	public List<Region> getRegionBringCarriage(List<Region> regionList, Integer regionId) {
		if(regionId == 1 || regionId == 100000){
			//省级长途费区间查询
			regionList.forEach(item->{
				String carriage = afterSalesLongDistanceFeeMapper.selectCarriageByProvince(item.getRegionId());
				item.setRegionName(item.getRegionName() + (carriage == null ? "":carriage));
			});
		}else{
			List<Region> provinceList = regionMapper.getRegionByParentId(ErpConst.ROOT_REGION);
			List<Integer> list = new ArrayList<>();
			for (Region region:provinceList){
				list.add(region.getRegionId());
			}
			if(list.contains(regionId)){
				//市级长途费区间查询
				regionList.forEach(item->{
					String carriage = afterSalesLongDistanceFeeMapper.selectCarriageByCity(item.getRegionId());
					item.setRegionName(item.getRegionName() + (carriage == null ? "":carriage));
				});
			}else {
				//县级长途费查询
				regionList.forEach(item->{
					String carriage = afterSalesLongDistanceFeeMapper.selectCarriageByRegionId(item.getRegionId());
					item.setRegionName(item.getRegionName() +  (carriage == null ? "":carriage));
				});
			}
		}
		return regionList;
	}

	@Override
	public Region getRegionByNameParentName(String name, String parentName) {
		return regionMapper.getRegionByNameParentName(name, parentName);
	}

    @Override
    public Region getRegionById(Integer areaId) {
        return regionMapper.getRegionById(areaId);
    }

}
