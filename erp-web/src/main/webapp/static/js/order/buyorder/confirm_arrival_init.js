$(function () {

    // 页面渲染后
    let buyorderGoodsListSize = $("#buyorderGoodsListSize").val();
    // console.log(buyorderGoodsListSize == 0)
    if (buyorderGoodsListSize == 0) {
        let buyorderId = $("#buyorderId").val();
        let pageRenderIndex = layer.confirm('无可确认收货快递单，请去新增快递', {
            // 按钮
            btn: ['新增快递']
        }, function () {
            layer.close(pageRenderIndex)
            location.href = page_url + '/order/buyorder/addExpress.do?buyorderId=' + buyorderId
        });
    }

    // 提交
    $("#apply_payment_submit").click(function () {
        checkLogin();
        var id_arrivalNum = ""
        let id_nonAllArrivalReason = ""
        var error = ""
        var buyorderId = $("#buyorderId").val();
        $.each($("input[name='b_checkbox']:checked"), function () {

            var arrivalNum = $(this).parent().nextAll().find('input[name= "arrivalNum" ]').val()
            if (arrivalNum == undefined || arrivalNum == null || arrivalNum === "" || Number(arrivalNum) < 0) {
                arrivalNum = 0
                $(this).parent().nextAll().find('input[name= "arrivalNum" ]').val(0)
            }
            var totalUnArrivalNum = $(this).parent().nextAll().find("input[name='totalUnArrivalNum']").val()
            let curExpressId = $(this).parent().nextAll().find('input[name= "curExpressId" ]').val()
            let nonAllArrivalReason = $(this).parent().nextAll().find('input[name= "nonAllArrivalReason" ]').val()
            id_arrivalNum += curExpressId + "_" + $(this).val() + "_" + arrivalNum + ","
            id_nonAllArrivalReason += curExpressId + "_" + $(this).val() + "_" + nonAllArrivalReason + ","

            if(curExpressId == undefined || curExpressId == null || curExpressId.length == 0){
                console.log("当前快递单号没解析到")
                error = "2"
            }
            if (parseInt(arrivalNum) > parseInt(totalUnArrivalNum)) {
                console.log("本次到货数量不可大于全部未到货数量")
                error = "1"
            }
            if (!(/(^[0-9]\d*$)/.test(arrivalNum))) {
                console.log("输入的不是自然数")
                error = "0"
            }
        });
        if (error === "0") {
            layer.alert("请输入正确的本次到货数量");
            return false;
        }
        if (error === "1") {
            layer.alert("本次到货数量不可大于全部未到货数量");
            return false;
        }
        if (error === "2") {
            layer.alert("当前快递单号解析错误");
            return false;
        }

        let number = nonAllArrivalReason()
        if (number == 1) {
            layer.alert("请在未全部到货SKU的备注中填写备注");
            return false;
        }
        if (number == 2) {
            layer.alert("备注不得超过50个字符");
            return false;
        }

        return confirmArrival(id_arrivalNum,id_nonAllArrivalReason, buyorderId);
    })

    // 全部收货
    $("#apply_all_payment_submit").click(function () {


        layui.use('layer', function () {

            layer.confirm('请确认是否全部收货？', {
                btn: ['确定', '取消']//按钮
            }, function (index) {

                layer.close(index);

                checkLogin();
                var id_arrivalNum = ""
                var buyorderId = $("#buyorderId").val();
                var isNew = $("#isNew").val();
                var error;
                $.each($("input[name='b_checkbox']:checked"), function () {
                    var arrivalNum = $(this).parent().nextAll().find('input[name= "arrivalNum" ]').val()
                    var totalUnArrivalNum = $(this).parent().nextAll().find("input[name='totalUnArrivalNum']").val();
                    var curExpressId = $(this).parent().nextAll().find('input[name= "curExpressId" ]').val()
                    if (curExpressId == undefined || curExpressId == null || curExpressId.length == 0) {
                        error = "2"
                        return true
                    }
                    id_arrivalNum += curExpressId + "_" + $(this).val() + "_" + totalUnArrivalNum + ","
                });
                if (error === "2") {
                    layer.alert("当前快递单号解析错误");
                    return false;
                }

                // 此处请求后台程序，下方是成功后的前台处理……
                let index_1 = layer.load(0, {shade: [0.7, '#393D49']}, {shadeClose: true}); //0代表加载的风格，支持0-2

                confirmArrival(id_arrivalNum, '', buyorderId, isNew);
                layer.close(index_1);

            });

        });

    })
})

function nonAllArrivalReason() {

    let flag = 0;
    $.each($("input[name='b_checkbox']:checked"), function () {
        let totalUnArrivalNum = $(this).parent().nextAll().find("input[name='totalUnArrivalNum']").val()
        let arrivalNum = $(this).parent().nextAll().find('input[name= "arrivalNum" ]').val()
        if (parseInt(arrivalNum) == parseInt(totalUnArrivalNum)) {
            return true
        }
        if (parseInt(arrivalNum) < parseInt(totalUnArrivalNum)) {
            let val = $(this).parent().nextAll().find('input[name="nonAllArrivalReason"]').val()
            if (val == undefined || val == null || val.length == 0) {
                flag = 1;
                return false
            }
            if (val.length > 50) {
                flag = 2
                return false
            }
        }
    })
    return flag

}

function confirmArrival(id_arrivalNum,id_nonAllArrivalReason, buyorderId, isNew) {

    console.log("id_arrivalNum: " + id_arrivalNum + "buyorderId: " + buyorderId);
    if (id_arrivalNum === "") {
        layer.alert("未选择任何信息");
        return false;
    }

    $.ajax({
        async: false,
        url: page_url + '/order/buyorder/confirmArrival.do',
        data: {
            "buyorderId": buyorderId,
            "id_arrivalNum": id_arrivalNum,
            "id_nonAllArrivalReason": id_nonAllArrivalReason,
            "isNew": isNew
        },
        type: "POST",
        dataType: 'json',

        success: function (data) {
            if (data.code == 0) {
                layer.alert("操作成功", {
                    icon : 1
                }, function(index) {
                   if (isNew == 1) {
                       if (data.data.indexOf('/') == 0){
                           location.href = page_url + data.data
                       }else{
                           location.href = page_url + '/' + data.data
                       }
                   }  else {
                       if (data.data.indexOf('/') == 0){
                           location.href = page_url + '/order/buyorder' + data.data
                       }else{
                           location.href = page_url + '/order/buyorder/' + data.data
                       }
                   }

                });
            } else {
                // layer.alert(data.message,{
                //     btn: ['点击前往'] //按钮
                // }, function(){
                //     location.href = page_url + 'order/buyorder/addExpress.do?buyorderId=' + buyorderId
                // });
                var index=layer.confirm(data.message, {
                    btn: ['点击前往'] //按钮
                }, function(){
                    layer.close(index)
                    location.href = page_url + '/order/buyorder/addExpress.do?buyorderId=' + buyorderId
                });

                // layer.alert(data.message);
            }
        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })



}

//全选
function selectall(obj) {
    checkLogin();
    if ($(obj).is(":checked")) {
        $("input[type='checkbox']").not(':disabled').prop("checked", true);
    } else {
        $("input[type='checkbox']").not(':disabled').prop('checked', false);
    }
}

//单选
function clickOne(type) {

    function clickSameExpressIdInput(currentObj,checkFlag) {

        let curExpressId = $(currentObj).parent().nextAll().find('input[name= "curExpressId"]').val()
        let finds = $('input[name= "curExpressId"]')
        $.each(finds, function (index, value) {
            if (curExpressId == value.value) {
                $(value).parent().prevAll().find('input[name= "b_checkbox"]').prop('checked', checkFlag)
            }
        })
    }

    clickSameExpressIdInput(event.srcElement,$(event.srcElement).prop('checked'))

    var num = $('input[name=' + type + ']:checked').length;
    var sum = $('input[name=' + type + ']').length;
    if (num == sum) {
        $('input[name = allcheck]').prop('checked', true);
    } else {
        $('input[name = allcheck]').prop('checked', false);
    }
}
