<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
		http://www.springframework.org/schema/aop
		http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

    <bean id="dataSource" class="com.ezadmin.plugins.sqlog.EzSqlogDataSource">
        <property name="realDataSource" ref="dataSourceTarget"></property>
        <property name="logType" value="${logType}"></property>
    </bean>

    <!-- 数据源 -->
    <bean name="dataSourceTarget"
          class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
<!--        <property name="url" value="${jdbc_url}"/>-->
<!--        <property name="username" value="${jdbc_username}"/>-->
<!--        <property name="password" value="${jdbc_password}"/>-->

        <!--<property name="url" value="*********************************************************************************************************************" />
        <property name="username" value="erpuser" />
        <property name="password" value="neweu3453563" />-->
                     	<property name="url" value="***************************************************************************************************************************************************" />
                        		<property name="username" value="fatwrite" />
                        		<property name="password" value="fatwrite" />
        <property name="maxActive" value="${jdbc_maxactive}"/>
        <property name="minIdle" value="5"/>
        <property name="maxWait" value="20000"/>
        <property name="removeAbandoned" value="true"/>
        <property name="logAbandoned" value="true"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>
        <property name="removeAbandonedTimeout" value="300" />
        <property name="validationQuery" value="select 1" />
        <!--<property name="initialSize" value="0" />
        <property name="validationQuery" value="${validationQuery}" />
        <property name="testOnBorrow" value="false" />
        <property name="testOnReturn" value="false" />
        <property name="testWhileIdle" value="true" />
        <property name="timeBetweenEvictionRunsMillis" value="60000" />
        <property name="minEvictableIdleTimeMillis" value="25200000" />
        <property name="removeAbandoned" value="true" />
        <property name="removeAbandonedTimeout" value="1800" />
        <property name="logAbandoned" value="true" />
        <property name="filters" value="mergeStat" />-->
    </bean>

    <!-- SqlSessionFactory -->
    <bean id="sqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <!-- 引用上面已经配置好的数据库连接池 -->
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="classpath:configuration.xml"/>
        <!-- mapper配置路径 -->
        <property name="mapperLocations" value="classpath*:mapping/*/*.xml"/>
        <property name="typeHandlersPackage" value="com.vedeng.common.mybatis.handler"/>
    </bean>


    <!-- Mapper扫描配置 -->
    <bean class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value=
                "com.vedeng.goods.**.dao,com.vedeng.erp.**.dao,com.vedeng.*.dao,
                com.smallhospital.dao,com.wms.dao,com.wms.*.dao,com.newtask.data.dao,
                com.newtask.celery.dao,com.vedeng.erp.finance.mapper,com.vedeng.erp.wms.mapper,
                com.vedeng.orderstream.aftersales.dao,com.vedeng.**.mapper"
        />
        <property name="sqlSessionFactoryBeanName" value="sqlSessionFactory"/>
    </bean>

    <!-- Mybatis事务管理配置 -->
    <bean id="transactionManager" class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- 事务控制 -->
    <tx:annotation-driven proxy-target-class="true" transaction-manager="transactionManager"/>

    <!-- 数据源 -->
    <bean name="ezAdminDatasource"
          class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="url" value="${ezadmin.ezdatasource.url}"/>
        <property name="username" value="${ezadmin.ezdatasource.username}"/>
        <property name="password" value="${ezadmin.ezdatasource.password}"/>
        <property name="maxActive" value="5"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="5000"/>
        <property name="initialSize" value="2"/>
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <property name="validationQuery" value="select 1 from dual"/>
        <property name="removeAbandoned" value="true"/>
        <property name="removeAbandonedTimeout" value="120"/>
        <property name="logAbandoned" value="true"/>
    </bean>

    <!-- 数据源 -->
    <!--<bean name="gomanager-dataSource"
          class="com.alibaba.druid.pool.DruidDataSource" init-method="init"
          destroy-method="close">
        <property name="url" value="${spring.gomanager-datasource.url}"/>
        <property name="username" value="${spring.gomanager-datasource.username}"/>
        <property name="password" value="${spring.gomanager-datasource.password}"/>
        <property name="maxActive" value="${spring.gomanager-datasource.druid.max-active}"/>
        <property name="minIdle" value="2"/>
        <property name="maxWait" value="5000"/>
        <property name="initialSize" value="2"/>
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>
        <property name="testWhileIdle" value="true"/>
        <property name="validationQuery" value="select 1 from dual"/>
        <property name="removeAbandoned" value="true"/>
        <property name="removeAbandonedTimeout" value="120"/>
        <property name="logAbandoned" value="true"/>
    </bean>-->

    <bean name="customAuditDataInterceptor" class="com.vedeng.common.mybatis.config.CustomAuditDataInterceptor"/>
    <bean name="mybatisSQLMonitor" class="com.vedeng.core.sqlmonitor.interceptor.MybatisSqlMonitor"/>
</beans>
