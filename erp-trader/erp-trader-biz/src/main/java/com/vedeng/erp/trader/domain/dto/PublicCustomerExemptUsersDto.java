package com.vedeng.erp.trader.domain.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 公海计算豁免用户配置表
 * <AUTHOR>
 * @date 2022/4/29 15:05
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PublicCustomerExemptUsersDto {

    private Integer publicCustomerExemptUsersId;

    /**
    * 公海计算豁免用户ID
    */
    private Integer userId;

    private String username;

    /**
    * 是否删除，0未删除，1已删除
    */
    private Boolean deleted;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 创建人
    */
    private Integer creator;

    private String creatorUsername;

    /**
    * 更新时间
    */
    private Date modTime;

    /**
    * 更新人
    */
    private Integer updater;
}