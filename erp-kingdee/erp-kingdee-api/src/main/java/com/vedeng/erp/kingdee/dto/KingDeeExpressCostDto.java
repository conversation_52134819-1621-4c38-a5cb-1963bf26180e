package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

/**
    * 金蝶快递成本表
    */
@Data
@Builder
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@WriteBackField(needBackField = {"FID"})
public class KingDeeExpressCostDto extends KingDeeMqBaseDto {

    private static final long serialVersionUID = -3496869414556918813L;

    /**
     * 是否自动提交审核
     */
    private Boolean isAutoSubmitAndAudit;
    /**
     * 是否自动调整JSON字段顺序
     */
    private Boolean isAutoAdjustField;

    /**
    * 主键ID
    */
    private Integer kingDeeExpressCostId;

    /**
    * 单据内码
    */
    @WriteBackField
    @KingDeeID
    private String fid;

    /**
    * 单据编号（固定值：“”）该字段为金蝶唯一键
    */
    private String fBillNo;

    /**
    * 发货组织（固定值）
    */
    private String fQzokOrgId;

    /**
    * 原始业务单号，销售订单号
    */
    private String fQzokYsddh;

    /**
    * 归属业务单号，销售订单号
    */
    private String fQzokGsywdh;

    /**
    * 业务类型，固定值：销售订单
    */
    private String fQzokYwlx;

    /**
    * 出入库单号
    */
    private String fQzokCrkdh;

    /**
    * 快递单号
    */
    private String fQzokKddh;

    /**
    * 物料编码
    */
    private String fQzokWlbm;

    /**
    * 物料序列号
    */
    private String fQzokXlh;

    /**
    * 批次号
    */
    private String fQzokPch;

    /**
    * 发货数量
    */
    private String fQzokFhsl;

    /**
    * 快递成本（不含税快递费）
    */
    private String fQzokCb;

    /**
    * 收件人
    */
    private String fQzokSjr;

    /**
    * 收件人电话
    */
    private String fQzokDh;

    /**
    * 收件人地址
    */
    private String fQzokDz;

    /**
    * 贝登单据编号（T_EXPRESS_COST中唯一键）
    */
    @BusinessID("F_QZOK_BDDJBH")
    private String fQzokBddjbh;

    /**
    * 是否已经删除（true,false）
    */
    private String fQzokSfsc;

    /**
    * 是否计入成本(是“Y”否“N”)，默认为Y，逻辑控制在大数据
    */
    private String fQzokSfjrcb;

    /**
     * 物流公司
     */
    private String FQzokWlgs;
    /**
     * 是否赠品(默认0，赠品 1)
     */
    private Integer FQzokSfzp;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.QZOK_KDCB;
    }

    public KingDeeExpressCostDto(){
        super();
        this.isAutoSubmitAndAudit = false;
        this.isAutoAdjustField = true;
        this.fQzokOrgId = "101";
        this.fQzokYwlx = "销售订单";
    }

}