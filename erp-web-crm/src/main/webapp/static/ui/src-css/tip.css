.vd-ui-tip-wrap {
  position: relative;
  display: flex;
  overflow: hidden;
  min-width: 18px;
}
.vd-ui-tip-wrap .vd-ui_icon {
  font-size: 16px;
  line-height: 1;
  margin-top: 1px;
}
.vd-ui-tip-wrap.warn {
  color: #f60;
}
.vd-ui-tip-wrap.info {
  color: #09f;
}
.vd-ui-tip-wrap .icon-info2 {
  color: #09f;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt {
  position: absolute;
  z-index: -1;
  opacity: -1;
  pointer-events: none;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt .vd-ui-tip-inner {
  padding: 10px 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  border-radius: 3px;
  color: #333;
  position: relative;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt .vd-ui-tip-inner.small {
  font-size: 12px;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt .vd-ui-tip-inner:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}
.vd-ui-tip-wrap .vd-ui-tip-cnt.nowrap {
  white-space: nowrap;
}
.vd-ui-tip-wrap.tl .vd-ui-tip-cnt {
  bottom: 16px;
  padding-bottom: 10px;
  right: -12px;
}
.vd-ui-tip-wrap.tl .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  bottom: -12px;
  right: 14px;
  border-top-color: #fff;
}
.vd-ui-tip-wrap.r .vd-ui-tip-cnt {
  top: 50%;
  transform: translateY(-50%);
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-wrap.r .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #fff;
}
.vd-ui-tip-wrap.rt .vd-ui-tip-cnt {
  top: -14px;
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-wrap.rt .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  top: 14px;
  border-right-color: #fff;
}
.vd-ui-tip-wrap.rb .vd-ui-tip-cnt {
  bottom: -14px;
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-wrap.rb .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  bottom: 15px;
  border-right-color: #fff;
}
.vd-ui-tip-wrap:hover .vd-ui-tip-cnt {
  pointer-events: all;
  z-index: 1;
  opacity: 1;
}
.vd-ui-title-tip-wrap {
  position: relative;
}
.vd-ui-title-tip-cnt {
  background: #333;
  color: #fff;
  border-radius: 3px;
  padding: 8px 15px;
  white-space: nowrap;
  transform: translateX(-50%);
  opacity: 0;
  z-index: -1;
  pointer-events: none;
  position: fixed;
}
.vd-ui-title-tip-cnt.show {
  z-index: 9999;
  opacity: 1;
}
.vd-ui-title-tip-cnt::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-top-color: #333;
  bottom: -9px;
  left: calc(50% - 5px);
}
.vd-ui-title-tip-cnt.bottom::before {
  border-top-color: transparent;
  border-bottom-color: #333;
  bottom: auto;
  top: -10px;
}
.vd-ui-tip-block-wrap .vd-ui-tip-cnt {
  position: absolute;
  z-index: -1;
  opacity: -1;
  pointer-events: none;
}
.vd-ui-tip-block-wrap .vd-ui-tip-cnt .vd-ui-tip-inner {
  padding: 10px 15px;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  border-radius: 3px;
  color: #333;
  position: relative;
}
.vd-ui-tip-block-wrap .vd-ui-tip-cnt .vd-ui-tip-inner.small {
  font-size: 12px;
}
.vd-ui-tip-block-wrap .vd-ui-tip-cnt .vd-ui-tip-inner:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}
.vd-ui-tip-block-wrap .vd-ui-tip-cnt.nowrap {
  white-space: nowrap;
}
.vd-ui-tip-block-wrap.tl .vd-ui-tip-cnt {
  bottom: 16px;
  padding-bottom: 10px;
  right: -12px;
}
.vd-ui-tip-block-wrap.tl .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  bottom: -12px;
  right: 14px;
  border-top-color: #fff;
}
.vd-ui-tip-block-wrap.r .vd-ui-tip-cnt {
  top: 50%;
  transform: translateY(-50%);
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-block-wrap.r .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  top: 50%;
  transform: translateY(-50%);
  border-right-color: #fff;
}
.vd-ui-tip-block-wrap.rt .vd-ui-tip-cnt {
  top: -14px;
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-block-wrap.rt .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  top: 14px;
  border-right-color: #fff;
}
.vd-ui-tip-block-wrap.rb .vd-ui-tip-cnt {
  bottom: -14px;
  padding-left: 10px;
  left: 16px;
}
.vd-ui-tip-block-wrap.rb .vd-ui-tip-cnt .vd-ui-tip-inner::before {
  left: -12px;
  bottom: 15px;
  border-right-color: #fff;
}
.vd-ui-tip-block-wrap:hover .vd-ui-tip-cnt {
  pointer-events: all;
  z-index: 1;
  opacity: 1;
}
