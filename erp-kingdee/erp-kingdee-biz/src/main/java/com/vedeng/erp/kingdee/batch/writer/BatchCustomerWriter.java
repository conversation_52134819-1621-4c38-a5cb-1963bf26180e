package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.repository.BatchCustomerFinanceDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.service.KingDeeCustomerApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/5 11:04
 */
@Service
@Slf4j
public class BatchCustomerWriter extends BaseWriter<KingDeeCustomerDto> {

    @Autowired
    private KingDeeCustomerApiService kingDeeCustomerApiService;

    @Override
    public void doWrite(KingDeeCustomerDto item, JobParameters params, ExecutionContext stepContext) throws Exception {

        log.info("审计客户信息推送：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.updateCustomer);
        kingDeeCustomerApiService.register(item,true);
    }
}