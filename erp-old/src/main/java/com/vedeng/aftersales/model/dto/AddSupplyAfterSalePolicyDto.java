package com.vedeng.aftersales.model.dto;

import java.math.BigDecimal;

/**
 * 新增供应商售后政策
 */
public class AddSupplyAfterSalePolicyDto {

    private String skuNo;
    private Long traderId;
    private String traderName;

    private Integer serviceProviderType;
    private Integer installPolicyInstallType;
    private String installPolicyInstallArea;
    private BigDecimal installPolicyInstallFee;
    private Integer installPolicyHaveInstallationQualification;
    private Integer installPolicyFreeRemoteInstall;
    private String installPolicyResponseTimeNum;
    private String installPolicyResponseTimeUnit;
    private String installPolicyVisitTimeNum;
    private String installPolicyVisitTimeUnit;
    private String installPolicyInstallTimeNum;
    private String installPolicyInstallTimeUnit;

    private Integer technicalDirectSupplyMaintain;
    private String technicalDirectResponseTimeNum;
    private String technicalDirectResponseTimeUnit;
    private String technicalDirectEffectTimeNum;
    private String technicalDirectEffectTimeUnit;

    private Integer guaranteePolicyIsGuarantee;
    private String guaranteePolicyGuaranteeType;
    private String guaranteePolicyHostGuaranteePeriodNum;
    private String guaranteePolicyHostGuaranteePeriodUnit;
    private String guaranteePolicyPartsGuaranteePeriodNum;
    private String guaranteePolicyPartsGuaranteePeriodUnit;
    private Integer guaranteePolicyCycleCaltype;
    private Integer guaranteePolicyArea;
    private String guaranteePolicyAreaComment;
    private String guaranteePolicyResponseTimeNum;
    private String guaranteePolicyResponseTimeUnit;
    private String guaranteePolicyVisitTimeNum;
    private String guaranteePolicyVisitTimeUnit;
    private String guaranteePolicyRepaireTimeNum;
    private String guaranteePolicyRepaireTimeUnit;
    private String guaranteePolicyRepaireComment;

    private Integer returnPolicySupportReturn;
    private String returnPolicyCondition;
    private Integer returnPolicyNeedIdentify;
    private String returnPolicyIdentifyType;
    private String returnPolicyReturnPeriodNum;
    private String returnPolicyReturnPeriodUnit;
    private Integer returnPolicyCycleCaltyp;
    private String returnPolicyPackagingRequirements;
    private String returnPolicyReturnComments;

    private Integer exchangePolicySupportChange;
    private String exchangePolicyExchangeContition;
    private Integer exchangePolicyNeedIdentify;
    private String exchangePolicyIdentifyType;
    private String exchangePolicyExchangePeriodUnit;
    private String exchangePolicyExchangePeriodNum;
    private Integer exchangePolicyCycleCaltyp;
    private String exchangePolicyPackagingRequirements;
    private String exchangePolicyExchangeComments;

    private Integer parolePolicySupportRepair;
    private Integer parolePolicySupportRenovation;
    private Integer parolePolicySupplyBox;
    private Integer parolePolicySupplyAttachment;

    private String overduePolicySupplyBackup;
    private String overduePolicyDetail;

    private String attashmentName;
    private String attashmentUri;

    public String getSkuNo() {
        return skuNo;
    }

    public void setSkuNo(String skuNo) {
        this.skuNo = skuNo;
    }

    public Long getTraderId() {
        return traderId;
    }

    public void setTraderId(Long traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public Integer getServiceProviderType() {
        return serviceProviderType;
    }

    public void setServiceProviderType(Integer serviceProviderType) {
        this.serviceProviderType = serviceProviderType;
    }

    public Integer getInstallPolicyInstallType() {
        return installPolicyInstallType;
    }

    public void setInstallPolicyInstallType(Integer installPolicyInstallType) {
        this.installPolicyInstallType = installPolicyInstallType;
    }

    public String getInstallPolicyInstallArea() {
        return installPolicyInstallArea;
    }

    public void setInstallPolicyInstallArea(String installPolicyInstallArea) {
        this.installPolicyInstallArea = installPolicyInstallArea;
    }

    public BigDecimal getInstallPolicyInstallFee() {
        return installPolicyInstallFee;
    }

    public void setInstallPolicyInstallFee(BigDecimal installPolicyInstallFee) {
        this.installPolicyInstallFee = installPolicyInstallFee;
    }

    public Integer getInstallPolicyHaveInstallationQualification() {
        return installPolicyHaveInstallationQualification;
    }

    public void setInstallPolicyHaveInstallationQualification(Integer installPolicyHaveInstallationQualification) {
        this.installPolicyHaveInstallationQualification = installPolicyHaveInstallationQualification;
    }

    public Integer getInstallPolicyFreeRemoteInstall() {
        return installPolicyFreeRemoteInstall;
    }

    public void setInstallPolicyFreeRemoteInstall(Integer installPolicyFreeRemoteInstall) {
        this.installPolicyFreeRemoteInstall = installPolicyFreeRemoteInstall;
    }

    public String getInstallPolicyResponseTimeNum() {
        return installPolicyResponseTimeNum;
    }

    public void setInstallPolicyResponseTimeNum(String installPolicyResponseTimeNum) {
        this.installPolicyResponseTimeNum = installPolicyResponseTimeNum;
    }

    public String getInstallPolicyResponseTimeUnit() {
        return installPolicyResponseTimeUnit;
    }

    public void setInstallPolicyResponseTimeUnit(String installPolicyResponseTimeUnit) {
        this.installPolicyResponseTimeUnit = installPolicyResponseTimeUnit;
    }

    public String getInstallPolicyVisitTimeNum() {
        return installPolicyVisitTimeNum;
    }

    public void setInstallPolicyVisitTimeNum(String installPolicyVisitTimeNum) {
        this.installPolicyVisitTimeNum = installPolicyVisitTimeNum;
    }

    public String getInstallPolicyVisitTimeUnit() {
        return installPolicyVisitTimeUnit;
    }

    public void setInstallPolicyVisitTimeUnit(String installPolicyVisitTimeUnit) {
        this.installPolicyVisitTimeUnit = installPolicyVisitTimeUnit;
    }

    public String getInstallPolicyInstallTimeNum() {
        return installPolicyInstallTimeNum;
    }

    public void setInstallPolicyInstallTimeNum(String installPolicyInstallTimeNum) {
        this.installPolicyInstallTimeNum = installPolicyInstallTimeNum;
    }

    public String getInstallPolicyInstallTimeUnit() {
        return installPolicyInstallTimeUnit;
    }

    public void setInstallPolicyInstallTimeUnit(String installPolicyInstallTimeUnit) {
        this.installPolicyInstallTimeUnit = installPolicyInstallTimeUnit;
    }

    public Integer getTechnicalDirectSupplyMaintain() {
        return technicalDirectSupplyMaintain;
    }

    public void setTechnicalDirectSupplyMaintain(Integer technicalDirectSupplyMaintain) {
        this.technicalDirectSupplyMaintain = technicalDirectSupplyMaintain;
    }

    public String getTechnicalDirectResponseTimeNum() {
        return technicalDirectResponseTimeNum;
    }

    public void setTechnicalDirectResponseTimeNum(String technicalDirectResponseTimeNum) {
        this.technicalDirectResponseTimeNum = technicalDirectResponseTimeNum;
    }

    public String getTechnicalDirectResponseTimeUnit() {
        return technicalDirectResponseTimeUnit;
    }

    public void setTechnicalDirectResponseTimeUnit(String technicalDirectResponseTimeUnit) {
        this.technicalDirectResponseTimeUnit = technicalDirectResponseTimeUnit;
    }

    public String getTechnicalDirectEffectTimeNum() {
        return technicalDirectEffectTimeNum;
    }

    public void setTechnicalDirectEffectTimeNum(String technicalDirectEffectTimeNum) {
        this.technicalDirectEffectTimeNum = technicalDirectEffectTimeNum;
    }

    public String getTechnicalDirectEffectTimeUnit() {
        return technicalDirectEffectTimeUnit;
    }

    public void setTechnicalDirectEffectTimeUnit(String technicalDirectEffectTimeUnit) {
        this.technicalDirectEffectTimeUnit = technicalDirectEffectTimeUnit;
    }

    public Integer getGuaranteePolicyIsGuarantee() {
        return guaranteePolicyIsGuarantee;
    }

    public void setGuaranteePolicyIsGuarantee(Integer guaranteePolicyIsGuarantee) {
        this.guaranteePolicyIsGuarantee = guaranteePolicyIsGuarantee;
    }

    public String getGuaranteePolicyGuaranteeType() {
        return guaranteePolicyGuaranteeType;
    }

    public void setGuaranteePolicyGuaranteeType(String guaranteePolicyGuaranteeType) {
        this.guaranteePolicyGuaranteeType = guaranteePolicyGuaranteeType;
    }

    public String getGuaranteePolicyHostGuaranteePeriodNum() {
        return guaranteePolicyHostGuaranteePeriodNum;
    }

    public void setGuaranteePolicyHostGuaranteePeriodNum(String guaranteePolicyHostGuaranteePeriodNum) {
        this.guaranteePolicyHostGuaranteePeriodNum = guaranteePolicyHostGuaranteePeriodNum;
    }

    public String getGuaranteePolicyHostGuaranteePeriodUnit() {
        return guaranteePolicyHostGuaranteePeriodUnit;
    }

    public void setGuaranteePolicyHostGuaranteePeriodUnit(String guaranteePolicyHostGuaranteePeriodUnit) {
        this.guaranteePolicyHostGuaranteePeriodUnit = guaranteePolicyHostGuaranteePeriodUnit;
    }

    public String getGuaranteePolicyPartsGuaranteePeriodNum() {
        return guaranteePolicyPartsGuaranteePeriodNum;
    }

    public void setGuaranteePolicyPartsGuaranteePeriodNum(String guaranteePolicyPartsGuaranteePeriodNum) {
        this.guaranteePolicyPartsGuaranteePeriodNum = guaranteePolicyPartsGuaranteePeriodNum;
    }

    public String getGuaranteePolicyPartsGuaranteePeriodUnit() {
        return guaranteePolicyPartsGuaranteePeriodUnit;
    }

    public void setGuaranteePolicyPartsGuaranteePeriodUnit(String guaranteePolicyPartsGuaranteePeriodUnit) {
        this.guaranteePolicyPartsGuaranteePeriodUnit = guaranteePolicyPartsGuaranteePeriodUnit;
    }

    public Integer getGuaranteePolicyCycleCaltype() {
        return guaranteePolicyCycleCaltype;
    }

    public void setGuaranteePolicyCycleCaltype(Integer guaranteePolicyCycleCaltype) {
        this.guaranteePolicyCycleCaltype = guaranteePolicyCycleCaltype;
    }

    public Integer getGuaranteePolicyArea() {
        return guaranteePolicyArea;
    }

    public void setGuaranteePolicyArea(Integer guaranteePolicyArea) {
        this.guaranteePolicyArea = guaranteePolicyArea;
    }

    public String getGuaranteePolicyAreaComment() {
        return guaranteePolicyAreaComment;
    }

    public void setGuaranteePolicyAreaComment(String guaranteePolicyAreaComment) {
        this.guaranteePolicyAreaComment = guaranteePolicyAreaComment;
    }

    public String getGuaranteePolicyResponseTimeNum() {
        return guaranteePolicyResponseTimeNum;
    }

    public void setGuaranteePolicyResponseTimeNum(String guaranteePolicyResponseTimeNum) {
        this.guaranteePolicyResponseTimeNum = guaranteePolicyResponseTimeNum;
    }

    public String getGuaranteePolicyResponseTimeUnit() {
        return guaranteePolicyResponseTimeUnit;
    }

    public void setGuaranteePolicyResponseTimeUnit(String guaranteePolicyResponseTimeUnit) {
        this.guaranteePolicyResponseTimeUnit = guaranteePolicyResponseTimeUnit;
    }

    public String getGuaranteePolicyVisitTimeNum() {
        return guaranteePolicyVisitTimeNum;
    }

    public void setGuaranteePolicyVisitTimeNum(String guaranteePolicyVisitTimeNum) {
        this.guaranteePolicyVisitTimeNum = guaranteePolicyVisitTimeNum;
    }

    public String getGuaranteePolicyVisitTimeUnit() {
        return guaranteePolicyVisitTimeUnit;
    }

    public void setGuaranteePolicyVisitTimeUnit(String guaranteePolicyVisitTimeUnit) {
        this.guaranteePolicyVisitTimeUnit = guaranteePolicyVisitTimeUnit;
    }

    public String getGuaranteePolicyRepaireTimeNum() {
        return guaranteePolicyRepaireTimeNum;
    }

    public void setGuaranteePolicyRepaireTimeNum(String guaranteePolicyRepaireTimeNum) {
        this.guaranteePolicyRepaireTimeNum = guaranteePolicyRepaireTimeNum;
    }

    public String getGuaranteePolicyRepaireTimeUnit() {
        return guaranteePolicyRepaireTimeUnit;
    }

    public void setGuaranteePolicyRepaireTimeUnit(String guaranteePolicyRepaireTimeUnit) {
        this.guaranteePolicyRepaireTimeUnit = guaranteePolicyRepaireTimeUnit;
    }

    public String getGuaranteePolicyRepaireComment() {
        return guaranteePolicyRepaireComment;
    }

    public void setGuaranteePolicyRepaireComment(String guaranteePolicyRepaireComment) {
        this.guaranteePolicyRepaireComment = guaranteePolicyRepaireComment;
    }

    public Integer getReturnPolicySupportReturn() {
        return returnPolicySupportReturn;
    }

    public void setReturnPolicySupportReturn(Integer returnPolicySupportReturn) {
        this.returnPolicySupportReturn = returnPolicySupportReturn;
    }

    public String getReturnPolicyCondition() {
        return returnPolicyCondition;
    }

    public void setReturnPolicyCondition(String returnPolicyCondition) {
        this.returnPolicyCondition = returnPolicyCondition;
    }

    public Integer getReturnPolicyNeedIdentify() {
        return returnPolicyNeedIdentify;
    }

    public void setReturnPolicyNeedIdentify(Integer returnPolicyNeedIdentify) {
        this.returnPolicyNeedIdentify = returnPolicyNeedIdentify;
    }

    public String getReturnPolicyIdentifyType() {
        return returnPolicyIdentifyType;
    }

    public void setReturnPolicyIdentifyType(String returnPolicyIdentifyType) {
        this.returnPolicyIdentifyType = returnPolicyIdentifyType;
    }

    public String getReturnPolicyReturnPeriodNum() {
        return returnPolicyReturnPeriodNum;
    }

    public void setReturnPolicyReturnPeriodNum(String returnPolicyReturnPeriodNum) {
        this.returnPolicyReturnPeriodNum = returnPolicyReturnPeriodNum;
    }

    public String getReturnPolicyReturnPeriodUnit() {
        return returnPolicyReturnPeriodUnit;
    }

    public void setReturnPolicyReturnPeriodUnit(String returnPolicyReturnPeriodUnit) {
        this.returnPolicyReturnPeriodUnit = returnPolicyReturnPeriodUnit;
    }

    public Integer getReturnPolicyCycleCaltyp() {
        return returnPolicyCycleCaltyp;
    }

    public void setReturnPolicyCycleCaltyp(Integer returnPolicyCycleCaltyp) {
        this.returnPolicyCycleCaltyp = returnPolicyCycleCaltyp;
    }

    public String getReturnPolicyPackagingRequirements() {
        return returnPolicyPackagingRequirements;
    }

    public void setReturnPolicyPackagingRequirements(String returnPolicyPackagingRequirements) {
        this.returnPolicyPackagingRequirements = returnPolicyPackagingRequirements;
    }

    public String getReturnPolicyReturnComments() {
        return returnPolicyReturnComments;
    }

    public void setReturnPolicyReturnComments(String returnPolicyReturnComments) {
        this.returnPolicyReturnComments = returnPolicyReturnComments;
    }

    public Integer getExchangePolicySupportChange() {
        return exchangePolicySupportChange;
    }

    public void setExchangePolicySupportChange(Integer exchangePolicySupportChange) {
        this.exchangePolicySupportChange = exchangePolicySupportChange;
    }

    public String getExchangePolicyExchangeContition() {
        return exchangePolicyExchangeContition;
    }

    public void setExchangePolicyExchangeContition(String exchangePolicyExchangeContition) {
        this.exchangePolicyExchangeContition = exchangePolicyExchangeContition;
    }

    public Integer getExchangePolicyNeedIdentify() {
        return exchangePolicyNeedIdentify;
    }

    public void setExchangePolicyNeedIdentify(Integer exchangePolicyNeedIdentify) {
        this.exchangePolicyNeedIdentify = exchangePolicyNeedIdentify;
    }

    public String getExchangePolicyIdentifyType() {
        return exchangePolicyIdentifyType;
    }

    public void setExchangePolicyIdentifyType(String exchangePolicyIdentifyType) {
        this.exchangePolicyIdentifyType = exchangePolicyIdentifyType;
    }

    public String getExchangePolicyExchangePeriodUnit() {
        return exchangePolicyExchangePeriodUnit;
    }

    public void setExchangePolicyExchangePeriodUnit(String exchangePolicyExchangePeriodUnit) {
        this.exchangePolicyExchangePeriodUnit = exchangePolicyExchangePeriodUnit;
    }

    public String getExchangePolicyExchangePeriodNum() {
        return exchangePolicyExchangePeriodNum;
    }

    public void setExchangePolicyExchangePeriodNum(String exchangePolicyExchangePeriodNum) {
        this.exchangePolicyExchangePeriodNum = exchangePolicyExchangePeriodNum;
    }

    public Integer getExchangePolicyCycleCaltyp() {
        return exchangePolicyCycleCaltyp;
    }

    public void setExchangePolicyCycleCaltyp(Integer exchangePolicyCycleCaltyp) {
        this.exchangePolicyCycleCaltyp = exchangePolicyCycleCaltyp;
    }

    public String getExchangePolicyPackagingRequirements() {
        return exchangePolicyPackagingRequirements;
    }

    public void setExchangePolicyPackagingRequirements(String exchangePolicyPackagingRequirements) {
        this.exchangePolicyPackagingRequirements = exchangePolicyPackagingRequirements;
    }

    public String getExchangePolicyExchangeComments() {
        return exchangePolicyExchangeComments;
    }

    public void setExchangePolicyExchangeComments(String exchangePolicyExchangeComments) {
        this.exchangePolicyExchangeComments = exchangePolicyExchangeComments;
    }

    public Integer getParolePolicySupportRepair() {
        return parolePolicySupportRepair;
    }

    public void setParolePolicySupportRepair(Integer parolePolicySupportRepair) {
        this.parolePolicySupportRepair = parolePolicySupportRepair;
    }

    public Integer getParolePolicySupportRenovation() {
        return parolePolicySupportRenovation;
    }

    public void setParolePolicySupportRenovation(Integer parolePolicySupportRenovation) {
        this.parolePolicySupportRenovation = parolePolicySupportRenovation;
    }

    public Integer getParolePolicySupplyBox() {
        return parolePolicySupplyBox;
    }

    public void setParolePolicySupplyBox(Integer parolePolicySupplyBox) {
        this.parolePolicySupplyBox = parolePolicySupplyBox;
    }

    public Integer getParolePolicySupplyAttachment() {
        return parolePolicySupplyAttachment;
    }

    public void setParolePolicySupplyAttachment(Integer parolePolicySupplyAttachment) {
        this.parolePolicySupplyAttachment = parolePolicySupplyAttachment;
    }

    public String getOverduePolicySupplyBackup() {
        return overduePolicySupplyBackup;
    }

    public void setOverduePolicySupplyBackup(String overduePolicySupplyBackup) {
        this.overduePolicySupplyBackup = overduePolicySupplyBackup;
    }

    public String getOverduePolicyDetail() {
        return overduePolicyDetail;
    }

    public void setOverduePolicyDetail(String overduePolicyDetail) {
        this.overduePolicyDetail = overduePolicyDetail;
    }

    public String getAttashmentName() {
        return attashmentName;
    }

    public void setAttashmentName(String attashmentName) {
        this.attashmentName = attashmentName;
    }

    public String getAttashmentUri() {
        return attashmentUri;
    }

    public void setAttashmentUri(String attashmentUri) {
        this.attashmentUri = attashmentUri;
    }
}
