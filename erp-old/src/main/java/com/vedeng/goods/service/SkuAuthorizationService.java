package com.vedeng.goods.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.api.dto.SysOptionDefinition;
import com.vedeng.goods.model.dto.SkuAuthrizationRequest;
import com.vedeng.goods.model.vo.SkuAuthorizationVo;

import java.util.List;

/**
 * SKU报备信息
 *
 * <AUTHOR>
 * @date 2020/9/17
 */
public interface SkuAuthorizationService {

    /**
     * 获取所有的报备信息终端类型
     *
     * @return
     */
    List<SysOptionDefinition> getAllTerminalTypes();

    /**
     * 通过SKU的ID获取sku的报备信息
     *
     * @param skuId
     * @return
     */
    SkuAuthorizationVo getSkuAuthorizationInfoBySkuId(Integer skuId);

    /**
     * 保存SKU报备信息
     *
     * @param skuAuthrizationRequest
     * @return
     */
    ResultInfo saveSkuAuthrizationInfo(SkuAuthrizationRequest skuAuthrizationRequest, User user);
}
