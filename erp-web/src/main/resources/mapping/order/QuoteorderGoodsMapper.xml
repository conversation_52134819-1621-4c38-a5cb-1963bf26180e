<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.QuoteorderGoodsMapper">
    <sql id="Base_Column_List">
        QUOTEORDER_GOODS_ID, QUOTEORDER_ID, IS_TEMP, GOODS_ID, SKU, GOODS_NAME, BRAND_NAME,
		MODEL, UNIT_NAME, PRICE, CURRENCY_UNIT_ID, NUM, DELIVERY_CYCLE,
		DELIVERY_DIRECT,
		DELIVERY_DIRECT_COMMENTS, REGISTRATION_NUMBER, SUPPLIER_NAME, REFERENCE_COST_PRICE,
		REFERENCE_PRICE, REFERENCE_DELIVERY_CYCLE, REPORT_STATUS,
		REPORT_COMMENTS, HAVE_INSTALLATION,
		GOODS_COMMENTS, INSIDE_COMMENTS, IS_DELETE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
    </sql>
    <select id="vailQuoteGoods" parameterType="com.vedeng.order.model.QuoteorderGoods" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM T_QUOTEORDER_GOODS A
		WHERE A.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} AND A.SKU = #{sku,jdbcType=VARCHAR} AND A.IS_DELETE = 0
	</select>

    <insert id="insertQuoteGoods" parameterType="com.vedeng.order.model.QuoteorderGoods"  useGeneratedKeys="true" keyProperty="quoteorderGoodsId">
        insert into T_QUOTEORDER_GOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="quoteorderGoodsId != null">
                QUOTEORDER_GOODS_ID,
            </if>
            <if test="quoteorderId != null">
                QUOTEORDER_ID,
            </if>
            <if test="isTemp != null">
                IS_TEMP,
            </if>
            <if test="goodsId != null">
                GOODS_ID,
            </if>
            <if test="sku != null">
                SKU,
            </if>
            <if test="goodsName != null">
                GOODS_NAME,
            </if>
            <if test="brandName != null">
                BRAND_NAME,
            </if>
            <if test="model != null">
                MODEL,
            </if>
            <if test="unitName != null">
                UNIT_NAME,
            </if>
            <if test="price != null">
                PRICE,
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID,
            </if>
            <if test="num != null">
                NUM,
            </if>
            <if test="deliveryCycle != null">
                DELIVERY_CYCLE,
            </if>
            <if test="deliveryDirect != null">
                DELIVERY_DIRECT,
            </if>
            <if test="deliveryDirectComments != null">
                DELIVERY_DIRECT_COMMENTS,
            </if>
            <if test="registrationNumber != null">
                REGISTRATION_NUMBER,
            </if>
            <if test="supplierName != null">
                SUPPLIER_NAME,
            </if>
            <if test="referenceCostPrice != null">
                REFERENCE_COST_PRICE,
            </if>
            <if test="referencePrice != null">
                REFERENCE_PRICE,
            </if>
            <if test="referenceDeliveryCycle != null">
                REFERENCE_DELIVERY_CYCLE,
            </if>
            <if test="reportStatus != null">
                REPORT_STATUS,
            </if>
            <if test="reportComments != null">
                REPORT_COMMENTS,
            </if>
            <if test="haveInstallation != null">
                HAVE_INSTALLATION,
            </if>
            <if test="goodsComments != null">
                GOODS_COMMENTS,
            </if>
            <if test="insideComments != null">
                INSIDE_COMMENTS,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="quoteorderGoodsId != null">
                #{quoteorderGoodsId,jdbcType=INTEGER},
            </if>
            <if test="quoteorderId != null">
                #{quoteorderId,jdbcType=INTEGER},
            </if>
            <if test="isTemp != null">
                #{isTemp,jdbcType=BIT},
            </if>
            <if test="goodsId != null">
                #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                #{sku,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null">
                #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                #{model,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                #{price,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                #{num,jdbcType=INTEGER},
            </if>
            <if test="deliveryCycle != null">
                #{deliveryCycle,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDirect != null">
                #{deliveryDirect,jdbcType=BIT},
            </if>
            <if test="deliveryDirectComments != null">
                #{deliveryDirectComments,jdbcType=VARCHAR},
            </if>
            <if test="registrationNumber != null">
                #{registrationNumber,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="referenceCostPrice != null">
                #{referenceCostPrice,jdbcType=DECIMAL},
            </if>
            <if test="referencePrice != null">
                #{referencePrice,jdbcType=DECIMAL},
            </if>
            <if test="referenceDeliveryCycle != null">
                #{referenceDeliveryCycle,jdbcType=VARCHAR},
            </if>
            <if test="reportStatus != null">
                #{reportStatus,jdbcType=BIT},
            </if>
            <if test="reportComments != null">
                #{reportComments,jdbcType=VARCHAR},
            </if>
            <if test="haveInstallation != null">
                #{haveInstallation,jdbcType=BIT},
            </if>
            <if test="goodsComments != null">
                #{goodsComments,jdbcType=VARCHAR},
            </if>
            <if test="insideComments != null">
                #{insideComments,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <select id="vailQuoteTotle" parameterType="com.vedeng.order.model.QuoteorderGoods" resultType="java.lang.Integer">
		SELECT COUNT(*)
		FROM T_QUOTEORDER A
		WHERE     IFNULL(A.TOTAL_AMOUNT, 0) <![CDATA[ <> ]]>
		          (SELECT IFNULL(SUM(B.PRICE * B.NUM), 0)
		           FROM T_QUOTEORDER_GOODS B
		           WHERE B.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} AND B.IS_DELETE = 0)
		      AND A.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
	</select>
    <select id="selectQuoteGoodsById" resultType="com.vedeng.order.model.QuoteorderGoods">
        SELECT A.*,
            B.GOODS_ID,
            B.MATERIAL_CODE,
            B.MANAGE_CATEGORY,
            RN.REGISTRATION_NUMBER AS GODDS_REGISTRATION_NUMBER,
            RN.MANAGE_CATEGORY_LEVEL,
            P.FIRST_ENGAGE_ID,
            B.PURCHASE_REMIND,
            B.PACKING_LIST,
            B.TOS,
            B.CATEGORY_ID,
            B.TECHNICAL_PARAMETER,
            B.GOODS_TYPE,
            C.TITLE AS MANAGE_CATEGORY_NAME,
            D.URI,
            D.DOMAIN,
            K.PURCHASE_TIME,
            IFNULL(E.STATUS, -1) AS VERIFY_STATUS,
            CONCAT(d1.MIN, IF(isnull(d1.MAX), '+', concat('-', d1.MAX))) as DECLARE_RANGE,
            CONCAT(d2.MIN, IF(isnull(d2.MAX), '+', concat('-', d2.MAX))) as DECLARE_DELIVERY_RANGE
            <if test="viewType != null and viewType == 5"><!-- 报价咨询 -->
                ,J.TRADER_NAME AS MAIN_SUPPLIER_NAME
            </if>
        FROM T_QUOTEORDER_GOODS A
            LEFT JOIN T_GOODS B ON A.GOODS_ID = B.GOODS_ID
            LEFT JOIN (SELECT P.GOODS_ID,P.URI,P.DOMAIN FROM T_GOODS_ATTACHMENT P WHERE P.ATTACHMENT_TYPE = 1001 AND P.STATUS = 1 GROUP BY P.GOODS_ID) D ON A.GOODS_ID = D.GOODS_ID
            LEFT JOIN T_SYS_OPTION_DEFINITION C ON B.MANAGE_CATEGORY = C.SYS_OPTION_DEFINITION_ID AND C.PARENT_ID = 20
            LEFT JOIN T_VERIFIES_INFO E ON A.GOODS_ID = E.RELATE_TABLE_KEY AND E.RELATE_TABLE = 'T_GOODS'
            LEFT JOIN V_CORE_SKU K ON A.GOODS_ID = K.SKU_ID
            LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
            LEFT JOIN T_FIRST_ENGAGE FE ON P.FIRST_ENGAGE_ID = FE.FIRST_ENGAGE_ID
            LEFT JOIN T_REGISTRATION_NUMBER RN ON FE.REGISTRATION_NUMBER_ID = RN.REGISTRATION_NUMBER_ID

            LEFT JOIN ODS_SKU_INFO_ERP_WT t ON A.GOODS_ID=t.sku_id
            left join V_CORE_SKU s on s.SKU_ID = t.sku_id
            left join T_RANGE_DICTIONARY d1 on d1.NAME = t.delivery_range
            left join T_RANGE_DICTIONARY d2 on d2.NAME = s.DECLARE_DELIVERY_RANGE
            <if test="viewType != null and viewType == 5"><!-- 报价咨询 -->
                LEFT JOIN (SELECT MIN(F.R_GOODS_J_TRADER_SUPPLIER),
                                F.GOODS_ID,
                                F.TRADER_SUPPLIER_ID
                                FROM T_R_GOODS_J_TRADER_SUPPLIER F
                            GROUP BY F.GOODS_ID
                            ORDER BY F.R_GOODS_J_TRADER_SUPPLIER) G ON A.GOODS_ID = G.GOODS_ID
                LEFT JOIN T_TRADER_SUPPLIER H ON G.TRADER_SUPPLIER_ID = H.TRADER_SUPPLIER_ID
                LEFT JOIN T_TRADER J ON H.TRADER_ID = J.TRADER_ID
            </if>
        WHERE A.QUOTEORDER_ID = #{quoteOrderId,jdbcType=INTEGER}
        ORDER BY A.QUOTEORDER_GOODS_ID ASC
    </select>

    <update id="updateQuoteTotal" parameterType="com.vedeng.order.model.QuoteorderGoods">
        UPDATE T_QUOTEORDER A
        SET A.TOTAL_AMOUNT =
        (SELECT SUM(B.PRICE * B.NUM)
        FROM T_QUOTEORDER_GOODS B
        WHERE B.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} AND B.IS_DELETE = 0),
        A.PAYMENT_TYPE = 419,
        A.PREPAID_AMOUNT =
        (SELECT SUM(B.PRICE * B.NUM)
        FROM T_QUOTEORDER_GOODS B
        WHERE B.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER} AND B.IS_DELETE = 0),
        A.ACCOUNT_PERIOD_AMOUNT = 0.00,
        A.LOGISTICS_COLLECTION = 0,
        A.RETAINAGE_AMOUNT = 0.00,
        A.RETAINAGE_AMOUNT_MONTH = 0,
        A.UPDATER = #{updater,jdbcType=INTEGER},
        A.MOD_TIME = #{modTime,jdbcType=BIGINT}
        <if test="terminalTraderId != null and terminalTraderId != ''">
            ,A.TERMINAL_TRADER_ID = #{terminalTraderId,jdbcType=INTEGER}
        </if>
        <if test="terminalTraderName != null and terminalTraderName != ''">
            ,A.TERMINAL_TRADER_NAME = #{terminalTraderName,jdbcType=VARCHAR}
        </if>
        <if test="terminalTraderType != null and terminalTraderType != ''">
            ,A.TERMINAL_TRADER_TYPE = #{terminalTraderType,jdbcType=INTEGER}
        </if>
        <if test="salesArea != null and salesArea != ''">
            ,A.SALES_AREA = #{salesArea,jdbcType=VARCHAR}
        </if>
        <if test="salesAreaId != null and salesAreaId != ''">
            ,A.SALES_AREA_ID = #{salesAreaId,jdbcType=INTEGER}
        </if>
        WHERE A.QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
    </update>

    <update id="editQuoteGoods" parameterType="com.vedeng.order.model.QuoteorderGoods">
        update T_QUOTEORDER_GOODS
        <set>
            <if test="quoteorderId != null">
                QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
            </if>
            <if test="isTemp != null">
                IS_TEMP = #{isTemp,jdbcType=BIT},
            </if>
            <if test="goodsId != null">
                GOODS_ID = #{goodsId,jdbcType=INTEGER},
            </if>
            <if test="sku != null">
                SKU = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null">
                BRAND_NAME = #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                MODEL = #{model,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                UNIT_NAME = #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                PRICE = #{price,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                NUM = #{num,jdbcType=INTEGER},
            </if>
            <if test="deliveryCycle != null">
                DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDirect != null">
                DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
            </if>
            <if test="deliveryDirectComments != null">
                DELIVERY_DIRECT_COMMENTS =
                #{deliveryDirectComments,jdbcType=VARCHAR},
            </if>
            <if test="registrationNumber != null">
                REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="referenceCostPrice != null">
                REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
            </if>
            <if test="referencePrice != null">
                REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
            </if>
            <if test="referenceDeliveryCycle != null">
                REFERENCE_DELIVERY_CYCLE =
                #{referenceDeliveryCycle,jdbcType=VARCHAR},
            </if>
            <if test="reportStatus != null">
                REPORT_STATUS = #{reportStatus,jdbcType=BIT},
            </if>
            <if test="reportComments != null">
                REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
            </if>
            <if test="haveInstallation != null">
                HAVE_INSTALLATION = #{haveInstallation,jdbcType=BIT},
            </if>
            <if test="goodsComments != null">
                GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
            </if>
            <if test="insideComments != null">
                INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER}
    </update>
    <update id="updateQuoteGoodsInfo"  parameterType="com.vedeng.order.model.QuoteorderGoods">
        UPDATE T_QUOTEORDER_GOODS
        <set>
            <if test="isTemp != null">
                IS_TEMP = #{isTemp,jdbcType=BIT},
            </if>
            <if test="sku != null">
                SKU = #{sku,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                GOODS_NAME = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="brandName != null">
                BRAND_NAME = #{brandName,jdbcType=VARCHAR},
            </if>
            <if test="model != null">
                MODEL = #{model,jdbcType=VARCHAR},
            </if>
            <if test="unitName != null">
                UNIT_NAME = #{unitName,jdbcType=VARCHAR},
            </if>
            <if test="price != null">
                PRICE = #{price,jdbcType=DECIMAL},
            </if>
            <if test="currencyUnitId != null">
                CURRENCY_UNIT_ID = #{currencyUnitId,jdbcType=INTEGER},
            </if>
            <if test="num != null">
                NUM = #{num,jdbcType=INTEGER},
            </if>
            <if test="deliveryCycle != null">
                DELIVERY_CYCLE = #{deliveryCycle,jdbcType=VARCHAR},
            </if>
            <if test="deliveryDirect != null">
                DELIVERY_DIRECT = #{deliveryDirect,jdbcType=BIT},
            </if>
            <if test="deliveryDirectComments != null">
                DELIVERY_DIRECT_COMMENTS =
                #{deliveryDirectComments,jdbcType=VARCHAR},
            </if>
            <if test="registrationNumber != null">
                REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                SUPPLIER_NAME = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="referenceCostPrice != null">
                REFERENCE_COST_PRICE = #{referenceCostPrice,jdbcType=DECIMAL},
            </if>
            <if test="referencePrice != null">
                REFERENCE_PRICE = #{referencePrice,jdbcType=VARCHAR},
            </if>
            <if test="referenceDeliveryCycle != null">
                REFERENCE_DELIVERY_CYCLE =
                #{referenceDeliveryCycle,jdbcType=VARCHAR},
            </if>
            <if test="reportStatus != null">
                REPORT_STATUS = #{reportStatus,jdbcType=BIT},
            </if>
            <if test="reportComments != null">
                REPORT_COMMENTS = #{reportComments,jdbcType=VARCHAR},
            </if>
            <if test="haveInstallation != null">
                HAVE_INSTALLATION = #{haveInstallation,jdbcType=BIT},
            </if>
            <if test="goodsComments != null">
                GOODS_COMMENTS = #{goodsComments,jdbcType=VARCHAR},
            </if>
            <if test="insideComments != null">
                INSIDE_COMMENTS = #{insideComments,jdbcType=VARCHAR},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=BIT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="goodsComments != null">
                GOODS_COMMENTS =#{goodsComments,jdbcType=VARCHAR}
            </if>
        </set>
        where QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}  AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
    </update>

    <select id="getAllQuoteOrderGoods" resultType="com.vedeng.order.model.QuoteorderGoods">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_QUOTEORDER_GOODS
        WHERE
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER}
    </select>

    <update id="updateQuoteorderGoods2Valid">
        UPDATE T_QUOTEORDER_GOODS SET IS_DELETE = 0 WHERE QUOTEORDER_ID = #{quoteorderId} AND GOODS_ID IN
        <foreach collection="goodsIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <update id="updateDefaultDeliveryDirectCommentByQuoteGoodsId">
        update T_QUOTEORDER_GOODS
        set DELIVERY_DIRECT_COMMENTS = '客户着急发货'
        where QUOTEORDER_GOODS_ID in
        <foreach collection="quoteGoodsIdList" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>

    <select id="selectQuoteGoodsId" resultType="com.vedeng.order.model.QuoteorderGoods">
        SELECT
        <include refid="Base_Column_List" />
        FROM
        T_QUOTEORDER_GOODS
        WHERE
        QUOTEORDER_GOODS_ID = #{quoteorderGoodsId}
    </select>

</mapper>
