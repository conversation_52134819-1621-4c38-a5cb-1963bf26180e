package com.vedeng.logistics.service.impl;

import com.itextpdf.text.log.Logger;
import com.itextpdf.text.log.LoggerFactory;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogVirtualMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogVirtualService;
import com.vedeng.order.model.Saleorder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 产品出入库日志服务层
 *
 * <AUTHOR>
 */
@Service("warehouseGoodsOperateLogVirtualService")
public class WarehouseGoodsOperateLogVirtualServiceImpl extends BaseServiceimpl implements WarehouseGoodsOperateLogVirtualService {
    public static Logger logger = LoggerFactory.getLogger(WarehouseGoodsOperateLogVirtualServiceImpl.class);

    @Resource
    private WarehouseGoodsOperateLogVirtualMapper warehouseGoodsOperateLogVirtualMapper;


    /**
     * 入库类型
     */
    private final List<Integer> warehouseInType = Arrays.asList(1, 3, 5, 8, 9, 11, 12);

    /**
     * 出库类型
     */
    private final List<Integer> warehouseOutType = Arrays.asList(2, 4, 6, 7, 10, 13, 14, 15);

    @Override
    public int insertSelective(WarehouseGoodsOperateLogVirtual record) {
        if (warehouseInType.contains(record.getOperateType())) {
            record.setLogType(ErpConst.ZERO);
        }

        if (warehouseOutType.contains(record.getOperateType())) {
            record.setLogType(ErpConst.ONE);
        }
        warehouseGoodsOperateLogVirtualMapper.insertSelective(record);
        return record.getWarehouseGoodsOperateLogVirtualId();
    }

    @Override
    public List<WarehouseGoodsOperateLogVirtual> getOutDetil(Saleorder saleorder) {
        List<WarehouseGoodsOperateLogVirtual> list = warehouseGoodsOperateLogVirtualMapper.getWarehouseOutList(saleorder);
        return list;
    }

    @Override
    public List<WarehouseGoodsOperateLogVirtual> getWGOlog(WarehouseGoodsOperateLog warehouseGoodsOperateLog) throws Exception {

        List<WarehouseGoodsOperateLogVirtual> list = null;
        //操作类型必须有值
        if(warehouseGoodsOperateLog.getOperateType() == null || warehouseGoodsOperateLog.getOperateType() == 0){
            return null;
        }

//		try {
        /**	移除调用db，改为调用erp
         // 定义反序列化 数据格式
         final TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>> TypeRef = new TypeReference<ResultInfo<List<WarehouseGoodsOperateLog>>>() {};
         String url=httpUrl + "warehousegoodsoperatelog/getwgolog.htm";
         ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(url,warehouseGoodsOperateLog,clientId,clientKey, TypeRef);
         list = result!=null? (List<WarehouseGoodsOperateLog>) result.getData() : null;
         **/
        list = warehouseGoodsOperateLogVirtualMapper.getWGOlog(warehouseGoodsOperateLog);
        //操作人员查询
        List<Integer> userIds = new ArrayList<>();
        if (null != list) {
            for (WarehouseGoodsOperateLogVirtual wlog : list) {
                userIds.add(wlog.getCreator());
                if(wlog.getCheckStatusUser()!=null){
                    userIds.add(wlog.getCheckStatusUser());
                }
                if(wlog.getRecheckStatusUser()!=null){
                    userIds.add(wlog.getRecheckStatusUser());
                }
            }
        }
        if (userIds.size() > 0) {
            List<User> userList = userMapper.getUserByUserIds(userIds);
            // 信息补充
            if (null != list) {
                for (WarehouseGoodsOperateLogVirtual wlog :list) {
                    for (User u : userList) {
                        if (wlog.getCreator().equals(u.getUserId())) {
                            wlog.setOperator(u.getUsername());
                        }
                    }
                }
            }
        }
        return list;
    }

    @Override
    public List<WarehouseGoodsOperateLogVirtual> getWGOlistByComments(String batChNo) {
        List<WarehouseGoodsOperateLogVirtual> list = warehouseGoodsOperateLogVirtualMapper.getWGOlistByComments(batChNo);
        return list;
    }
}
