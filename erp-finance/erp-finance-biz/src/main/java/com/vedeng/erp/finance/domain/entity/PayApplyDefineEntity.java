package com.vedeng.erp.finance.domain.entity;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PayApplyDefineEntity {
    private Integer payApplyId;
    private Integer companyId;
    private Integer payType;
    private Integer relatedId;
    private Integer traderSubject; // BIT类型
    private Integer traderMode;
    private String traderName;
    private String traderId;
    private BigDecimal amount;
    private Integer currencyUnitId;
    private String bank;
    private String bankAccount;
    private String bankCode;
    private String comments;
    private Integer validStatus; // BIT类型
    private Long validTime; // BIGINT类型
    private String validComments;
    private Integer isBill; // BIT类型
    private Long addTime; // BIGINT类型
    private Integer creator;
    private Long modTime; // BIGINT类型
    private Integer updater;
    private String creatorName;
    private Integer payStatus;
    private String mobile;
    private String card;
    private Integer payBankTypeId;
    private String payBankTypeName;
    private Integer accountType;
    private String bankRemark;
    private Date billTime; // TIMESTAMP类型
    private Integer billMethod;
    private Integer autoBill;
}
