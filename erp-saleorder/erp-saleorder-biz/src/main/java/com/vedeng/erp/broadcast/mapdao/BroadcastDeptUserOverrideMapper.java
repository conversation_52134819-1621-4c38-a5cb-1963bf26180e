package com.vedeng.erp.broadcast.mapdao;

import com.vedeng.erp.broadcast.domain.entity.BroadcastDeptUserOverrideEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 自定义归属用户Mapper
 */
public interface BroadcastDeptUserOverrideMapper {

    /**
     * 根据主键删除记录
     *
     * @param id 主键
     * @return 删除记录数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insert(BroadcastDeptUserOverrideEntity record);

    /**
     * 选择性插入记录
     *
     * @param record 记录
     * @return 插入记录数
     */
    int insertSelective(BroadcastDeptUserOverrideEntity record);

    /**
     * 根据主键查询记录
     *
     * @param id 主键
     * @return 记录
     */
    BroadcastDeptUserOverrideEntity selectByPrimaryKey(Integer id);

    /**
     * 选择性更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKeySelective(BroadcastDeptUserOverrideEntity record);

    /**
     * 根据主键更新记录
     *
     * @param record 记录
     * @return 更新记录数
     */
    int updateByPrimaryKey(BroadcastDeptUserOverrideEntity record);

    List<BroadcastDeptUserOverrideEntity> getAllBroadcastUserRelate();

    /**
     * 逻辑删除所有用户关系记录
     *
     * @param userId 操作人ID
     * @return 更新记录数
     */
    int deleteAllBroadcastUserRelate(Integer userId);

    /**
     * 批量插入用户关系记录
     *
     * @param list 记录列表
     * @return 插入记录数
     */
    int insertBatch(@Param("list")List<BroadcastDeptUserOverrideEntity> list);
}
