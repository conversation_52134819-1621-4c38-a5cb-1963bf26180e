<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="终端列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/jc/search_customer_list.js?rnd=${resourceVersionKey}"></script>
<div class="formpublic formpublic1">
	<div>
			<form method="post" id="search"
				  action="${pageContext.request.contextPath}/trader/customer/searchCustomerList.do?indexId=${indexId}">
				<div>
					<!-- 初始模式 -->
					<ul class="searchTable visible">
						<li>
							<div class="infor_name  infor_name6 ">
								<span>*</span> <label>客户名称</label>
							</div>

							<div class="f_left table-larger" style="width: 80%">
								<div class="mb10">
									<input type="text" class="input-larger mr5"
										name="searchTraderName" id="searchTraderName"
										value="${traderCustomerVo.searchTraderName}">
									<input type="hidden" name="belongPlatform" value="${traderCustomerVo.belongPlatform}">
									<span
										class="bt-bg-style bt-small bg-light-blue" onclick="search();"
										id="searchError">搜索</span>
									<c:if test="${orderType eq '9'}">
										<input type="hidden" name="orderType" value="9">
										<font color="blue">提示：直销订单只能选择终端客户</font>
									</c:if>

								</div>
							</div>
							<div>
								<table
									class="table table-bordered table-striped table-condensed table-centered mb10">
									<thead>

										<th>客户名称</th>
										<th>地区</th>
										<th>创建时间</th>
										<th>归属销售</th>
										<th class="table-smallest6">选择</th>
									</thead>
									<tbody>

										<c:if test="${not empty searchCustomerList}">
											<c:forEach items="${searchCustomerList}" var="list"
												varStatus="status">
												<tr>
													<td>${list.traderName}</td>
													<td>${list.address}</td>
													<td><date:date value="${list.addTime}" /></td>
													<td>${list.personal}</td>
													<td width="5%" style="text-align: center"><a
														href="javaScript:void(0)"
														onclick="selectCustomer('${indexId}','${list.traderId}','${list.traderName}','${list.traderCustomerId}','${list.customerType}', '${list.customerNature}',
																${list.belongPlatform})">选择</a>
													</td>
												</tr>
											</c:forEach>
										</c:if>
										<c:if test="${empty searchCustomerList}">
											<tr>
												<td colspan="5">查询无结果！请尝试使用其他关键词搜索。</td>
											</tr>
										</c:if>
									</tbody>
								</table>
							</div>
						</li>
						<li class="visible"><tags:page page="${page}" />
							<div class="clear"></div></li>
						<div class="clear"></div>
					</ul>
					<!-- 搜索最后结果lastResult -->
				</div>
			</form>
	</div>
</div>
</body>
</html>
