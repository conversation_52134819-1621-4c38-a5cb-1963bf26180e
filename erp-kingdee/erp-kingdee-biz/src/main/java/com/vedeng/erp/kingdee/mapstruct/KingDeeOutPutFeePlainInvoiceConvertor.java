package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.OutPutFeePlainInvoiceDto;
import org.mapstruct.*;

import java.util.List;

/**
 * 金蝶 销项票 dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeOutPutFeePlainInvoiceConvertor extends BaseMapStruct<KingDeeOutPutFeePlainInvoiceEntity, OutPutFeePlainInvoiceDto> {
    /**
     * KingDeeOutPutFeePlainInvoiceEntity
     *
     * @param dto OutPutFeePlainInvoiceDto
     * @return KingDeeOutPutFeePlainInvoiceEntity
     */
    @Mapping(target = "fsaleexinventry", source = "FSALEEXINVENTRY", qualifiedByName = "listToString")
    @Override
    KingDeeOutPutFeePlainInvoiceEntity toEntity(OutPutFeePlainInvoiceDto dto);

    /**
     * dto 原List 转 JSON
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("listToString")
    default String listToString(List<OutPutFeePlainInvoiceDetailDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
