<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.PriceCenterSkuInfoImageMapper">
  <resultMap id="BaseResultMap" type="com.newtask.model.SkuInfoImageDto">
    <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
      <result column="SKU_NO" property="skuNo"></result>
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="BELONG_PRODUCT_MANAGER" property="belongProductManager" />
    <result column="BELONG_PRODUCT_ASSIT" property="belongProductAssit" />
    <result column="addTimeStr" property="addTime" />
    <result column="modTimeStr" property="modTime" />
      <result column="is_deleted" property="isDeleted" />
      <result column="IS_NEED_REPORT" property="isNeedReport" />
  </resultMap>


  <select id="getSkuInfoImage" resultMap="BaseResultMap">
            SELECT
            SKU_ID,
            SKU_NO,
            SKU_NAME,
            K.IS_NEED_REPORT,
            A.USERNAME AS BELONG_PRODUCT_MANAGER,
            B.USERNAME AS BELONG_PRODUCT_ASSIT,
            DATE_FORMAT(K.ADD_TIME,'%Y-%m-%d %H:%i:%s')  addTimeStr,
			DATE_FORMAT(K.MOD_TIME,'%Y-%m-%d %H:%i:%s')  modTimeStr,
			K.STATUS is_deleted
        FROM
            V_CORE_SKU K
        LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
        LEFT JOIN T_USER A ON ASSIGNMENT_MANAGER_ID = A.USER_ID
        LEFT JOIN T_USER B ON ASSIGNMENT_ASSISTANT_ID = B.USER_ID
        WHERE
            K.MOD_TIME >= DATE_SUB(NOW(), INTERVAL 10 MINUTE)
  </select>



    <select id="getSkuInfoImageAll" resultMap="BaseResultMap">
            SELECT
            SKU_ID,
            SKU_NO,
            SKU_NAME,
            K.IS_NEED_REPORT,
            A.USERNAME AS BELONG_PRODUCT_MANAGER,
            B.USERNAME AS BELONG_PRODUCT_ASSIT,
            DATE_FORMAT(K.ADD_TIME,'%Y-%m-%d %H:%i:%s')  addTimeStr,
			DATE_FORMAT(K.MOD_TIME,'%Y-%m-%d %H:%i:%s')  modTimeStr,
			K.STATUS is_deleted
        FROM
            V_CORE_SKU K
        LEFT JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID
        LEFT JOIN T_USER A ON ASSIGNMENT_MANAGER_ID = A.USER_ID
        LEFT JOIN T_USER B ON ASSIGNMENT_ASSISTANT_ID = B.USER_ID
  </select>




</mapper>