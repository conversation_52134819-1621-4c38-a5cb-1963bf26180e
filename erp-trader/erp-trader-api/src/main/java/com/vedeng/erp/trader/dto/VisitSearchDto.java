package com.vedeng.erp.trader.dto;

import com.ctrip.framework.apollo.ConfigService;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/12/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VisitSearchDto {

    /**
     * 用户
     */
    private List<Integer> userIdList = new ArrayList<>();

    /**
     * 拜访人集合
     */
    private List<Integer> visitIdList = new ArrayList<>();

    /**
     * 实际拜访日期 空为全部 7近一周 30近一月 90近三月 180近半年
     */
    private Integer actualVisitDay;

    /**
     * 拜访结果 1未打卡 2仅打卡 3拜访事项缺失 4 拜访成功
     */
    private String actualVisitResult;

    /**
     * 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端)
     */
    private String customerNature;

    /**
     * 客户会员 全部 1普通会员 2同舟会员 3非客户
     */
    private String customrTz;

    /**
     * 会员等级1金牌 2银牌
     */
    private String customerLevel;

    /**
     * 计划拜访时间开始
     */
    private String planVisitDateStart;
    /**
     * 计划拜访时间结束
     */
    private String planVisitDateEnd;

    /**
     * 实际拜访时间开始
     */
    private String actualVisitDateStart;
    /**
     * 实际拜访时间结束
     */
    private String actualVisitDateEnd;

    /**
     * 沟通概况
     */
    private String commucateContent;

    /**
     * 拜访结果，拜访成功Y拜访事项缺失N
     */
    private String visitSuccess;

    /**
     * 是否同舟会员
     */
    private Integer tzCustomer;

    /**
     * 是否贝登会员
     */
    private Integer vdCustomer;

    /**
     * 商机编号
     */
    private String businessChanceNo;

    /**
     * 商机状态
     */
    private String businessChanceStatus;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户类型
     */
    private Integer customerType;

    /**
     * 排序的规则
     */
    private String orderBy;

    private Integer pageNo;

    private Integer pageSize;

    private Integer userId;

    private String historyTime;

    private String searchFrom;//查询来源，默认拜访计划列表，兼容历史数据，空，1：拜访记录列表；2：客户详情


    public String getHistoryTime() {
        return ConfigService.getAppConfig().getProperty("historyTime", "2024-07-07 00:00:00");
    }
}
