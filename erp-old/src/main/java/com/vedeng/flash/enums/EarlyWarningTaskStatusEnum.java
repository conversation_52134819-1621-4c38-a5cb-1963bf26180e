package com.vedeng.flash.enums;
/**
 * @Description 预警表状态枚举值
 * <AUTHOR>
 * @Date 13:42 2021/5/31
 * @Param
 * @return
 **/
public enum EarlyWarningTaskStatusEnum {

    INIT(0 , "初始化"),
    MATURE(1, "临期"),
    OVER_MATURE(2, "逾期"),
    OVER_MATURE_12HOURS(3, "逾期12小时"),
    OVER_MATURE_48HOURS(4, "逾期48小时");

    EarlyWarningTaskStatusEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(DealTypeEnum enums : DealTypeEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }
}
