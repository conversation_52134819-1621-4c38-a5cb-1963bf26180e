package com.wms.model.ddi.generate;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * T_DDI_INSTALLSTION
 * <AUTHOR>
@Data
public class DdiInstallstion implements Serializable {
    private Integer tDdiInstallstionId;

    /**
     * 经销商
     */
    private String distributor;

    /**
     * GE合同编号
     */
    private String quoteId;

    /**
     * GE销售订单单号
     */
    private String saleorderNo;

    /**
     * 装机日期
     */
    private Date installstionTime;

    /**
     * 产品类型
     */
    private String goodsType;

    /**
     * 产品名称
     */
    private String goodsName;

    /**
     * 产品型号
     */
    private String goodsModel;

    /**
     * 注册证号
     */
    private String registrationNumber;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 装机地点
     */
    private String installstionAera;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 保修期起始
     */
    private Date warrantyStartTime;

    /**
     * 保修条款及保修期
     */
    private String warrantyPeriod;

    /**
     * 保修期止于
     */
    private Date warrantyEndTime;

    /**
     * 安装工程师姓名
     */
    private String engineerName;

    /**
     * 安装工程师电话
     */
    private String engineerPhone;

    /**
     * GE销售工程师姓名
     */
    private String salerName;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除 0否 1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;
}