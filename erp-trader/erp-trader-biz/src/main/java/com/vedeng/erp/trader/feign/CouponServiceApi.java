//package com.vedeng.erp.trader.feign;
//
//import com.vedeng.bean.web.response.RestfulResult;
//import com.vedeng.common.feign.annotations.FeignApi;
//import com.vedeng.market.api.dto.request.CouponCollectionRequest;
//import feign.Headers;
//import feign.RequestLine;
//import org.springframework.web.bind.annotation.RequestBody;
//
///**
// * <AUTHOR>
// * @description
// * @date 2023/8/14 12:54
// **/
//@FeignApi(serverName = "marketService")
//public interface CouponServiceApi {
//
//
//    /**
//     * 领取新人优惠券
//     * @param couponCollectionRequest
//     * @return
//     */
//    @Headers({"Content-Type: application/json", "Accept: application/json"})
//    @RequestLine("POST /coupon/collectionNewCoupon")
//    RestfulResult collectionNewCoupon(@RequestBody CouponCollectionRequest couponCollectionRequest);
//
//}
