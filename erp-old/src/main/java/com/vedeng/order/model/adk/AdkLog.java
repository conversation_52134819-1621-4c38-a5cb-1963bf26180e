package com.vedeng.order.model.adk;

import java.util.Date;

public class AdkLog {
    private String orderNo;
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_id
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private Integer adkLogId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_timestamp
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private Long adkLogTimestamp;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_ak
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkLogAk;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_sig
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkLogSig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.add_time
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private Date addTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.update_time
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private Date updateTime;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_status
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkLogStatus;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_msg
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkLogMsg;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_type
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkLogType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.request_id
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String requestId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_order_no
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkOrderNo;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_ADK_LOG.adk_log_param
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    private String adkLogParam;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_id
     *
     * @return the value of T_ADK_LOG.adk_log_id
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public Integer getAdkLogId() {
        return adkLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_id
     *
     * @param adkLogId the value for T_ADK_LOG.adk_log_id
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogId(Integer adkLogId) {
        this.adkLogId = adkLogId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_timestamp
     *
     * @return the value of T_ADK_LOG.adk_log_timestamp
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public Long getAdkLogTimestamp() {
        return adkLogTimestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_timestamp
     *
     * @param adkLogTimestamp the value for T_ADK_LOG.adk_log_timestamp
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogTimestamp(Long adkLogTimestamp) {
        this.adkLogTimestamp = adkLogTimestamp;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_ak
     *
     * @return the value of T_ADK_LOG.adk_log_ak
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkLogAk() {
        return adkLogAk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_ak
     *
     * @param adkLogAk the value for T_ADK_LOG.adk_log_ak
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogAk(String adkLogAk) {
        this.adkLogAk = adkLogAk;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_sig
     *
     * @return the value of T_ADK_LOG.adk_log_sig
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkLogSig() {
        return adkLogSig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_sig
     *
     * @param adkLogSig the value for T_ADK_LOG.adk_log_sig
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogSig(String adkLogSig) {
        this.adkLogSig = adkLogSig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.add_time
     *
     * @return the value of T_ADK_LOG.add_time
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.add_time
     *
     * @param addTime the value for T_ADK_LOG.add_time
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.update_time
     *
     * @return the value of T_ADK_LOG.update_time
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.update_time
     *
     * @param updateTime the value for T_ADK_LOG.update_time
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_status
     *
     * @return the value of T_ADK_LOG.adk_log_status
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkLogStatus() {
        return adkLogStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_status
     *
     * @param adkLogStatus the value for T_ADK_LOG.adk_log_status
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogStatus(String adkLogStatus) {
        this.adkLogStatus = adkLogStatus;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_msg
     *
     * @return the value of T_ADK_LOG.adk_log_msg
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkLogMsg() {
        return adkLogMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_msg
     *
     * @param adkLogMsg the value for T_ADK_LOG.adk_log_msg
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogMsg(String adkLogMsg) {
        this.adkLogMsg = adkLogMsg;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_type
     *
     * @return the value of T_ADK_LOG.adk_log_type
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkLogType() {
        return adkLogType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_type
     *
     * @param adkLogType the value for T_ADK_LOG.adk_log_type
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogType(String adkLogType) {
        this.adkLogType = adkLogType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.request_id
     *
     * @return the value of T_ADK_LOG.request_id
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.request_id
     *
     * @param requestId the value for T_ADK_LOG.request_id
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_order_no
     *
     * @return the value of T_ADK_LOG.adk_order_no
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkOrderNo() {
        return adkOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_order_no
     *
     * @param adkOrderNo the value for T_ADK_LOG.adk_order_no
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkOrderNo(String adkOrderNo) {
        this.adkOrderNo = adkOrderNo;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_ADK_LOG.adk_log_param
     *
     * @return the value of T_ADK_LOG.adk_log_param
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public String getAdkLogParam() {
        return adkLogParam;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_ADK_LOG.adk_log_param
     *
     * @param adkLogParam the value for T_ADK_LOG.adk_log_param
     *
     * @mbg.generated Sun May 05 10:38:13 CST 2019
     */
    public void setAdkLogParam(String adkLogParam) {
        this.adkLogParam = adkLogParam;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}