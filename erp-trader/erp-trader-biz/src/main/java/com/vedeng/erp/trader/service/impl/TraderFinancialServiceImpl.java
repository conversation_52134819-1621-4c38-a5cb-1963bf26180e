package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.domain.dto.TraderCustomerFinanceExcelDto;
import com.vedeng.erp.trader.domain.dto.TraderFinanceDetail;
import com.vedeng.erp.trader.domain.entity.TraderCustomerFinance;
import com.vedeng.erp.trader.mapper.TraderCustomerFinanceExtMapper;
import com.vedeng.erp.trader.service.TraderFinancialService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.model.TraderCustomer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * @description:
 * @author: yana.jiang
 * @date: 2022/11/28
 */
@Service
@Slf4j
public class TraderFinancialServiceImpl implements TraderFinancialService {
    private static final int PAGE_SIZE = 500;
	public static final Integer TWO = 2;
    private static final int MAX_DATA_LENGTH = 100000;
    @Resource
    private TraderCustomerFinanceExtMapper traderCustomerFinanceExtMapper;
    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;
    
    @Override
    public TraderFinanceDetail getTraderFinancialById(Integer traderCustomerFinanceId) {
        return traderCustomerFinanceExtMapper.getTraderFinancialById(traderCustomerFinanceId);
    }

    @Override
    public void updateTraderCustomerFinance(TraderCustomerFinance traderFinance, User user) {
        traderFinance.setModTime(new Date());
        traderFinance.setUpdater(user.getUserId());
        traderFinance.setIsPush(ErpConstant.ZERO);
        traderCustomerFinanceExtMapper.updateByPrimaryKeySelective(traderFinance);
    }

    @Override
    public void importTraderCustomerFinancial(MultipartFile file, User user) throws Exception {
        List<TraderCustomerFinanceExcelDto> updateList = new ArrayList<>();
        List<TraderCustomerFinanceExcelDto> addList = new ArrayList<>();
        AtomicInteger row = new AtomicInteger(1);
        EasyExcel.read(file.getInputStream(), TraderCustomerFinanceExcelDto.class, new CustomerExcelReadListener<TraderCustomerFinanceExcelDto>(dataList ->
        {
            dataList.forEach(data -> {
            row.getAndIncrement();
                if(row.get() > MAX_DATA_LENGTH){
                    throw new ServiceException(StrUtil.format("数据量超过10W!"));
                }
            if (Objects.isNull(data.getTraderId())
                    ||Objects.isNull(data.getCustomerNature())
                    ||Objects.isNull(data.getCustomerSecondType())) {
                throw new ServiceException(StrUtil.format("第{}行数据不可为空",row.get()));
            }
            if(!"直接客户".equals(data.getCustomerNature()) && !"间接客户".equals(data.getCustomerNature())){
                throw new ServiceException(StrUtil.format("第{}行，*客户性质数据有误",row.get()));
            }else {
                if("直接客户".equals(data.getCustomerNature())){
                    data.setCustomerNature("1");
                }else {
                    data.setCustomerNature("2");
                }
            }
             if(StringUtils.isNotBlank(data.getCustomerClass()) && !"公立".equals(data.getCustomerClass()) && !"民营个体".equals(data.getCustomerClass())  && !"民营集团".equals(data.getCustomerClass())){
                    throw new ServiceException(StrUtil.format("第{}行，终端客户分类数据有误",row.get()));
             }else {
                 if("公立".equals(data.getCustomerClass())){
                     data.setCustomerClass("1");
                 }else if("民营个体".equals(data.getCustomerClass())) {
                     data.setCustomerClass("2");
                 }else if("民营集团".equals(data.getCustomerClass())) {
                     data.setCustomerClass("3");
                 }else {
                     data.setCustomerClass("0");
                 }
             }
             if(StringUtils.isNotBlank(data.getCustomerSecondType()) && !"医疗卫生机构".equals(data.getCustomerSecondType()) && !"非医疗卫生机构".equals(data.getCustomerSecondType())  && !"分销商".equals(data.getCustomerSecondType())){
                    throw new ServiceException(StrUtil.format("第{}行，客户细分类数据有误",row.get()));
             }else {
                 if("医疗卫生机构".equals(data.getCustomerSecondType())){
                     data.setCustomerSecondType("1");
                 }else if("非医疗卫生机构".equals(data.getCustomerSecondType())) {
                     data.setCustomerSecondType("2");
                 }else if("分销商".equals(data.getCustomerSecondType())){
                     data.setCustomerSecondType("3");
                 }
             }
             if(StringUtils.isNotBlank(data.getCustomerThirdType()) && !"医院".equals(data.getCustomerThirdType()) && !"基层医疗卫生机构".equals(data.getCustomerThirdType())  && !"专业医疗卫生机构".equals(data.getCustomerThirdType()) && !"其他医疗卫生机构".equals(data.getCustomerThirdType())){
                    throw new ServiceException(StrUtil.format("第{}行，医疗机构分类数据有误",row.get()));
             }else {
                 if("医院".equals(data.getCustomerThirdType())){
                     data.setCustomerThirdType("1");
                 }else if("基层医疗卫生机构".equals(data.getCustomerThirdType())) {
                     data.setCustomerThirdType("2");
                 }else if("专业医疗卫生机构".equals(data.getCustomerThirdType())) {
                     data.setCustomerThirdType("3");
                 }else if("其他医疗卫生机构".equals(data.getCustomerThirdType())) {
                     data.setCustomerThirdType("4");
                 }else {
                     data.setCustomerThirdType("0");
                 }
             }
             if(StringUtils.isNotBlank(data.getHospitalLever()) && !"一级".equals(data.getHospitalLever()) && !"二级".equals(data.getHospitalLever())  && !"三级".equals(data.getHospitalLever()) && !"未定级".equals(data.getHospitalLever())){
                    throw new ServiceException(StrUtil.format("第{}行，医院等级数据有误",row.get()));
             }else {
                 if("一级".equals(data.getHospitalLever())){
                     data.setHospitalLever("1");
                 }else if("二级".equals(data.getHospitalLever())) {
                     data.setHospitalLever("2");
                 }else if("三级".equals(data.getHospitalLever())) {
                     data.setHospitalLever("3");
                 }else if("未定级".equals(data.getHospitalLever())){
                     data.setHospitalLever("4");
                 }else {
                     data.setHospitalLever("0");
                 }
             }
            //客户ID为系统中不存在，或存在但不符合已审核通过状态，不在【客户信息(财务专用)】列表中的，提示：XXX行客户ID不存在
            TraderCustomer traderCustomer = traderCustomerMapper.selectTraderCustomer(data.getTraderId());
            if(Objects.isNull(traderCustomer)){
                throw new ServiceException(StrUtil.format("第{}行客户ID不存在",row.get()));
            }
            //判断此用户是否已导入，已导入更新。未导入不处理
            TraderCustomerFinance traderCustomerFinance = traderCustomerFinanceExtMapper.getTraderFinancialByTraderId(data.getTraderId());
            if (traderCustomerFinance != null) {
                data.setTraderCustomerFinanceId(traderCustomerFinance.getTraderCustomerFinanceId());
                data.setUpdater(user.getUserId());
                data.setUpdaterName(user.getUsername());
                data.setModTime(new Date());
                data.setIsPush(ErpConstant.ZERO);
                updateList.add(data);
            }else {
                addList.add(data);
            }
        });})).sheet().doRead();

        if (CollectionUtils.isNotEmpty(updateList)) {
            traderCustomerFinanceExtMapper.updateBatchSelective(updateList);
        }else if(CollectionUtils.isEmpty(updateList) && CollectionUtils.isEmpty(addList)){
            throw new ServiceException(StrUtil.format("上传的是空模板"));
        }
    }

	@Override
	@Transactional
	public void syncTrader(Long startDate, Long endDate) {
		//分页查询，每次500条
		Map<String,Object> queryParams = new HashMap<>(16);
		Integer page = 0;
		Integer pageSize = PAGE_SIZE;
		queryParams.put("pageSize", pageSize);
		queryParams.put("startDate", startDate);
		queryParams.put("endDate", endDate);
		queryParams.put("traderId", null);
		List<Integer> traderIdList = saleorderMapper.selectByAddTime(queryParams);
        while (!CollectionUtils.isEmpty(traderIdList)) {
        	log.info("获取订单数据第：{}页",page+1);
        	page = page+1;
        	//根据traderList 进行初始化TraderFinal表
        	List<Integer> traderIdExistsList = traderCustomerFinanceExtMapper.selectByTraderIds(traderIdList);
        	int traderId = traderIdList.stream().max(Comparator.comparing(x -> x)).orElse(null);
        	if(CollectionUtils.isNotEmpty(traderIdExistsList)) {
        		traderIdList = traderIdList.stream().filter(m->!traderIdExistsList.contains(m)).collect(Collectors.toList());
        		if(CollectionUtils.isNotEmpty(traderIdList)) {
        			log.info("插入对应的客户ID:{}",JSON.toJSONString(traderIdList));
        			traderCustomerFinanceExtMapper.batchInsertSelect(traderIdList);
        		}
        	}else {
        		log.info("插入对应的客户ID:{}",JSON.toJSONString(traderIdList));
        		traderCustomerFinanceExtMapper.batchInsertSelect(traderIdList);
        	}
        	queryParams.put("traderId", traderId);
        	traderIdList = saleorderMapper.selectByAddTime(queryParams);
        }
	}
}
