.voice-to-text {
  width: 100%;
  border: 1px solid #ddd;
  border-radius: 6px;
  background-color: #fff;
}
.voice-to-text .content {
  /*border-bottom: 1px solid #ddd;*/
  width: 100%;
  height: 400px;
  overflow-y: scroll;
  /*box-sizing: border-box;*/
}
.voice-to-text .content .before-content {
  padding-left: 16px;
  width: 100%;
  float: left;
  margin-left: -16px;
}
.voice-to-text .content .before-content .before-content-title {
  height: 30px;
  font-size: 20px;
  padding-top: 4px;
  font-weight: 700;
}
.voice-to-text .content .before-content .play {
  font-size: 14px;
  font-weight: normal;
  margin-left: 10px;
  color: #3384ef;
  padding-left: 8px;
  padding-right: 8px;
  border: 1px solid #ddd;
  border-radius: 2px;
}
.voice-to-text .content .before-content .play:hover {
  cursor: pointer;
  border: 1px solid #3988ef;
  background-color: #3384ef;
  color: #fff;
}
.voice-to-text .content .before-content .before-content-word {
  font-size: 14px;
  color: #999999;
}
.voice-to-text .content .before-content .before-content-word p:nth-child(1) {
  margin-top: 10px;
}
.voice-to-text .content .before-content .before-content-word p {
  margin: 0;
}
.voice-to-text .content .after-content {
  display: none;
  /*border-left: 1px solid #ddd;*/
  width: 50%;
  height: 100%;
  float: left;
  /*padding-left: 16px;*/
}
#updatedContent {
  padding-left: 16px;
  border-left: 1px solid #ddd;
}
.voice-to-text .content .after-content .after-content-title {
  height: 30px;
  font-size: 20px;
  padding-top: 4px;
  font-weight: 700;
  padding-left: 16px;
}
.voice-to-text .content .after-content .after-content-word {
  font-size: 14px;
  color: #333333;
}
.voice-to-text .content .after-content .after-content-word p:nth-child(1) {
  margin-top: 10px;
}
.voice-to-text .content .after-content .after-content-word p {
  margin: 0;
}
.voice-to-text .content .after-content .after-content-word p span {
  color: #333333;
}
.voice-to-text .content .after-content .after-content-word p span.active {
  color: orange;
}
.voice-to-text .content-change {
  height: 53px;
  border-bottom: 1px solid #ddd;
  border-top: 1px solid #ddd;
  overflow: hidden;
  padding-left: 16px;
}
.voice-to-text .content-change .before {
  float: left;
  padding-top: 8px;
}
.voice-to-text .content-change .before .change-before {
  padding-top: 6px;
  float: left;
  font-size: 16px;
}
.voice-to-text .content-change .before .input-before {
  margin-top: 3px;
  margin-left: 30px;
  width: 275px;
  height: 30px;
  font-size: 16px;
}
.voice-to-text .content-change .after {
  margin-left: 146px;
  float: left;
  padding-top: 8px;
}
.voice-to-text .content-change .after .change-after {
  padding-top: 6px;
  float: left;
  font-size: 16px;
}
.voice-to-text .content-change .after .input-after {
  margin-top: 3px;
  margin-left: 30px;
  width: 275px;
  height: 30px;
  font-size: 16px;
}
.voice-to-text .content-change .change-button {
  margin-top: 10px;
  margin-left: 12px;
  float: left;
  width: 86px;
  height: 30px;
  background-color: #3384ef;
  border: 1px solid #1b76ed;
  border-radius: 4px;
  color: #ffffff;
  text-align: center;
  padding-top: 3px;
  box-sizing: border-box;
}
.voice-to-text .content-change .change-button:hover {
  background-color: #0175e2;
  cursor: pointer;
}
.voice-to-text .change-record {
  box-sizing: border-box;
  width: 100%;
  border-bottom: 1px solid #ddd;
  padding-left: 16px;
  padding-right: 16px;
  padding-bottom: 16px;
}
.voice-to-text .change-record .record-title {
  height: 30px;
  font-size: 20px;
  padding-top: 4px;
  font-weight: 700;
}
.voice-to-text .change-record .record-table {
  margin-top: 10px;
  width: 100%;
  border: 1px solid #ddd;
}
.voice-to-text .change-record .record-table th {
  font-size: 14px;
  padding-top: 6px;
  padding-bottom: 6px;
  border: 1px solid #ddd;
  background-color: #e5e5e5;
  font-weight: normal;
}
.voice-to-text .change-record .record-table td {
  font-size: 12px;
  padding-top: 6px;
  padding-bottom: 6px;
  border: 1px solid #ddd;
}
.voice-to-text .change-record .record-table tr:nth-child(odd) {
  background-color: #f5f5f5;
}
.voice-to-text .change-record .record-table tr:hover {
  background-color: #fffaf2;
}
.voice-to-text .change-record .record-table .th-number {
  width: 60px;
}
.voice-to-text .change-record .record-table .th-before {
  width: 304px;
}
.voice-to-text .change-record .record-table .th-after {
  width: 304px;
}
.voice-to-text .change-record .record-table .th-type {
  width: 122px;
}
.voice-to-text .change-record .record-table .td-number {
  text-align: center;
}
.voice-to-text .change-record .record-table .td-before {
  padding-left: 16px;
}
.voice-to-text .change-record .record-table .td-after {
  padding-left: 16px;
}
.voice-to-text .change-record .record-table .td-type {
  text-align: center;
}
.voice-to-text .change-record .record-table .td-use {
  text-align: center;
}
.voice-to-text .change-record .record-table .td-use .delete {
  color: #fc5151;
  border-radius: 2px;
  border: 1px solid #ddd;
  padding-left: 8px;
  padding-right: 8px;
}
.voice-to-text .change-record .record-table .td-use .delete:hover {
  cursor: pointer;
  background-color: #fc5151;
  border: 1px solid #d74a46;
  color: #fff;
}
.voice-to-text .change-record .record-table .td-use .submit {
  margin-left: 10px;
  color: #3384ef;
  padding-left: 8px;
  padding-right: 8px;
  border: 1px solid #ddd;
  border-radius: 2px;
}
.voice-to-text .change-record .record-table .td-use .submit:hover {
  cursor: pointer;
  border: 1px solid #3988ef;
  background-color: #3384ef;
  color: #fff;
}
.voice-to-text .review {
  box-sizing: border-box;
  width: 100%;
  padding-left: 16px;
  padding-bottom: 16px;
}
.voice-to-text .review .review-title {
  height: 30px;
  font-size: 20px;
  padding-top: 4px;
  font-weight: 700;
}
.voice-to-text .review .review-content {
  margin-top: 5px;
  height: 70px;
  overflow-y: scroll;
  font-size: 12px;
}
.voice-to-text .review .review-content .review-one {
  padding-right: 16px;
  margin: 0;
  overflow: hidden;
}
.voice-to-text .review .review-content .review-one .review-person {
  float: left;
}
.voice-to-text .review .review-content .review-one .review-date {
  float: right;
  color: #666666;
}
.voice-to-text .review .review-add {
  margin-top: 20px;
  overflow: hidden;
  padding-right: 16px;
}
.voice-to-text .review .review-add .review-input {
  margin-top: 3px;
  float: left;
  width: 90%;
  height: 30px;
  font-size: 16px;
}
.voice-to-text .review .review-add .review-button {
  margin-left: 20px;
  margin-top: 2px;
  float: left;
  width: 86px;
  height: 30px;
  background-color: #3384ef;
  border: 1px solid #1b76ed;
  border-radius: 4px;
  color: #ffffff;
  text-align: center;
  padding-top: 3px;
  box-sizing: border-box;
}
.voice-to-text .review .review-add .review-button:hover {
  background-color: #0175e2;
  cursor: pointer;
}
