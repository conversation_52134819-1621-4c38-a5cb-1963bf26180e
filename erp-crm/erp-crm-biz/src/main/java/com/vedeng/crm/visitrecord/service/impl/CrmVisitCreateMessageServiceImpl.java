package com.vedeng.crm.visitrecord.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.enums.JumpErpTitleEnum;
import com.vedeng.crm.business.quote.domain.dto.SendMessageDto;
import com.vedeng.crm.visitrecord.domain.dto.VisitRecordInputDto;
import com.vedeng.crm.visitrecord.domain.vo.VisitRecordVo;
import com.vedeng.crm.visitrecord.service.CrmVisitMessageService;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.dto.TraderUserDto;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/3/10
 */
@Service("CrmVisitCreateMessageServiceImpl")
public class CrmVisitCreateMessageServiceImpl extends BaseMessageSendServiceImpl implements CrmVisitMessageService {


    @Autowired
    private UserApiService userApiService;

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Override
    public void sendMessage(VisitRecordInputDto visitRecordInputDto, Integer recordId, List<Integer>  tongxingIdsList, CurrentUser currentUser) {
        String tongxingNamesStr = "";
        if (CollectionUtils.isNotEmpty(tongxingIdsList)) {
            List<UserDto> userDtoSendList = userApiService.getUserInfoByUserIds(tongxingIdsList);
            //获取这些人的工号
            List<String> tongXingUserNames = CollectionUtil.isEmpty(userDtoSendList) ? new ArrayList<>()
                    : (userDtoSendList.stream().map(UserDto::getUsername).collect(Collectors.toList()));
            tongxingNamesStr = StringUtils.join(tongXingUserNames,"，");
        }

        //创建拼接消息，消息模板如下（没有同行人时，消息通知内无同行人字段）：
        //        拜访编号：
        //        拜访客户：
        //        拜访目标：
        //        计划拜访时间：
        //        拜访人：
        //        同行人：
        //        创建人：
        StringBuilder messageBuilder = new StringBuilder();
        messageBuilder.append("拜访编号：").append(visitRecordInputDto.getVisitRecordNo()).append("\n");
        messageBuilder.append("拜访客户：").append(visitRecordInputDto.getCustomerName()).append("\n");
        messageBuilder.append("拜访目标：").append(visitRecordInputDto.getVisitTargetStr()).append("\n");
        messageBuilder.append("计划拜访时间：").append(DateUtil.format(visitRecordInputDto.getPlanVisitDate(),"yyyy-MM-dd") ).append("\n");
        messageBuilder.append("拜访人：").append(visitRecordInputDto.getVisitorName()).append("\n");

        // 如果有同行人则添加同行人信息
        if (StringUtils.isNotEmpty(tongxingNamesStr)) {
            messageBuilder.append("同行人：").append(tongxingNamesStr).append("\n");
        }
        messageBuilder.append("创建人：").append(currentUser.getUsername());
        String message = messageBuilder.toString();

        List<Integer> userIds = new ArrayList<>();
        userIds.add(visitRecordInputDto.getVisitorId());//拜访人
        if(CollectionUtil.isNotEmpty(tongxingIdsList)) {
            userIds.addAll(tongxingIdsList);//同行人
        }
        //userIds.remove(currentUser.getId());//消息的发送人，去掉当前的操作人，即创建人
        userIds.removeAll(Collections.singleton(currentUser.getId()));//消息的发送人，去掉当前的操作人，即创建人

        if(CollectionUtil.isNotEmpty(userIds)){
            List<UserDto> userDtoSendList = userApiService.getUserInfoByUserIds(userIds);
            //获取这些人的工号
            List<String> visitJobNumList = CollectionUtil.isEmpty(userDtoSendList)?new ArrayList<>()
                    :(userDtoSendList.stream().map(UserDto::getNumber).collect(Collectors.toList())) ;
            for(String jobNumber:visitJobNumList){
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setUrl(jumpService.getMjumpUrl(crmApplicationMessageJumpUrl + "/crm/visitRecord/m/detail?id=" + recordId,
                        lxcrmUrl + "/crm/visitRecord/profile/detail?id=" + recordId,
                        JumpErpTitleEnum.VISIT_RECORD_DETAIL));
                sendMessageDto.setUserNumber(jobNumber);
                sendMessageDto.setFormat(message);
                sendCardMsg(sendMessageDto, "您收到了一条拜访计划");
            }
        }

        if(visitRecordInputDto.getTraderId() != null ){
            List<Integer > allUserIdList = new ArrayList<>();
            allUserIdList.add(currentUser.getId());
            allUserIdList.add(visitRecordInputDto.getVisitorId());
            allUserIdList.addAll(userIds);
            //查询归属销售，如果归属销售不在userIds中，则添加到userIds
            TraderUserDto traderUserDto =  traderCustomerBaseService.getTraderUserByTraderId(visitRecordInputDto.getTraderId());
            Integer belongerId = traderUserDto==null?null:traderUserDto.getUserId();
            if(belongerId!= null &&  !allUserIdList.contains(belongerId)){
                //该客户的归属销售在创建人/拜访人/同行人中，则不触发第二条；
                List<Integer> belongerIdList = new ArrayList<>( );
                belongerIdList.add(belongerId);
                List<UserDto> userDtoSendList = userApiService.getUserInfoByUserIds(belongerIdList);
                //获取这些人的工号
                List<String> visitJobNumList = CollectionUtil.isEmpty(userDtoSendList)?new ArrayList<>()
                        :(userDtoSendList.stream().map(UserDto::getNumber).collect(Collectors.toList())) ;
                SendMessageDto sendMessageDto = new SendMessageDto();
                sendMessageDto.setUrl(jumpService.getMjumpUrl(crmApplicationMessageJumpUrl + "/crm/visitRecord/m/detail?id=" + recordId,
                        lxcrmUrl + "/crm/visitRecord/profile/detail?id=" + recordId,
                        JumpErpTitleEnum.VISIT_RECORD_DETAIL));
//                sendMessageDto.setUserNumberList(visitJobNumList);
                sendMessageDto.setUserNumber(visitJobNumList.get(0));
                sendMessageDto.setFormat(message);
                sendCardMsg(sendMessageDto, "同事对您的客户创建了一条拜访计划");
            }
        }
    }
}
