package com.vedeng.erp.aftersale.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * T_EXPENSE_AFTER_SALES_ITEM
 *
 * <AUTHOR>
@EqualsAndHashCode(callSuper = true)
@Data
public class ExpenseAfterSalesItemDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long expenseAfterSalesItemId;

    /**
     * 费用售后表主键ID
     */
    private Long expenseAfterSalesId;

    /**
     * 采购费用明细主键ID
     */
    private Integer buyorderExpenseItemId;

    /**
     * 订货号
     */
    private String sku;

    /**
     * 退货数量
     */
    private Integer returnNum;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * goodsId
     */
    private Integer goodsId;

    /**
     * 费用类别名称
     */
    private String expenseCategoryName;

    /**
     * 采购数量
     */
    private Integer num;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * @组合对象@ 售后单商品关联的 销售单 关系
     */
    private List<RExpenseAfterSalesJSaleorderDto> rExpenseAfterSalesJSaleorderDtos;

}