package com.vedeng.customerbillperiod.model;

import java.io.Serializable;
import java.math.BigDecimal;

import lombok.Data;

/**
 * T_CUSTOMER_BILL_PERIOD_USE_DETAIL
 * <AUTHOR>
@Data
public class CustomerBillPeriodUseDetail implements Serializable {
    private Long billPeriodUseDetailId;

    /**
     * 客户账期ID
     */
    private Long billPeriodId;

    /**
     * 客户所属公司ID
     */
    private Integer companyId;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 账期结算周期
     */
    private Integer settlementPeriod;

    /**
     * 客户账期结算方式，1产品发货，2产品开票
     */
    private Integer settlementType;

    /**
     * 账期使用金额，支付订单时，金额大于0，售后退货、还款时，金额小于0
     */
    private BigDecimal amount;

    /**
     * 账期使用明细的未归还金额
     */
    private BigDecimal unreturnedAmount;

    /**
     * 账期使用是否占用，0未占用，1已占用
     */
    private Integer occupancy;

    /**
     * 账期使用类型，1支付订单，2关闭订单，3售后退货，4还款
     */
    private Integer useType;

    /**
     * 账期使用关联表的ID，与TYPE字段对应：订单ID，售后单ID，支付流水ID
     */
    private Long relatedId;

    /**
     * 售后退货、还款产生的明细对应的支付订单使用ID
     */
    private Long parentUseDetailId;

    /**
     * 账期使用时间
     */
    private Long addTime;

    private static final long serialVersionUID = 1L;
}