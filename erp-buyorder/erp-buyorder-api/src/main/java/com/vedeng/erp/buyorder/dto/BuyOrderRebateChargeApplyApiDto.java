package com.vedeng.erp.buyorder.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.listenerEvent.eventresult.AuditRecordEventResult;
import com.vedeng.infrastructure.file.domain.Attachment;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/22 16:51
 */
@Getter
@Setter
public class BuyOrderRebateChargeApplyApiDto extends BaseDto {

    /**
     * 主键
     */
    private Integer buyOrderRebateChargeId;

    /**
     * 采购返利收款单号
     */
    private String buyOrderRebateChargeNo;

    /**
     * 交易人id
     */
    private Integer traderId;

    /**
     * 供应商id
     */
    private Integer traderSupplierId;

    /**
     * 供应商名称
     */
    private String traderSupplierName;

    /**
     * 返利收款总金额
     */
    private BigDecimal totalAmount;

    /**
     * 返利周期开始时间
     */
    private Date cycleBeginTime;

    /**
     * 返利周期结束时间
     */
    private Date cycleEndTime;

    /**
     * 返利使用期限开始时间
     */
    private Date usePeriodBeginTime;

    /**
     * 返利使用期限结束时间
     */
    private Date usePeriodEndTime;

    /**
     * 结算附件url(多个附件逗号分割)
     */
    private String billUrlIds;

    /**
     * 结算附件 集合
     */
    private List<Attachment> billFileList;

    /**
     * 明细附件url(多个附件逗号分割)
     */
    private String detailUrlIds;

    /**
     * 明细附件 集合
     */
    private List<Attachment> detailFileList;

    /**
     * 单据状态
     * 0 已提交（审核中）
     * 1 审核通过
     * 2 审核不通过
     */
    private Integer auditStatus;

    /**
     * 审核通过时间
     */
    private Date auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 列表搜索项
     */
    private String keyWords;

    /**
     * 是否展示审核按钮
     */
    private Boolean showCheckButton = false;

    /**
     * 审核记录
     */
    private List<AuditRecordEventResult> auditRecordEventResults;

    /**
     * 审核流程id
     */
    private String taskId;
}