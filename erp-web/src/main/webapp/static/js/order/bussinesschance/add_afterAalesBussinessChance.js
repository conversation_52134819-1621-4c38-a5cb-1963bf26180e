window.sourceZj=[];
window.sourceBd=[];


function sourceClick(_this){
	$("#ul_inquiry").empty();
	var source=parseInt($(_this).val());
	window.sourceZj.forEach(function (e) {
		if(e.sourceId==source){
			e.enquiryList.forEach(function (a) {
				var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
					'<input type="radio" name="communication"  value="'+a.enqId+'"><label>'+a.enqName+'</label>\n' +
					'</li>'

				$("#ul_inquiry").append(instr)
			})}

	})
}
function changeData(str){
	var inquiry = $('input:radio[name='+str+']:checked').val();
	if (inquiry !== 0) {
		$.ajax({
			url: '/order/bussinesschance/getRadioData.do',
			data: inquiry,
			type: 'post',
			dataType: 'JSON',
			contentType: 'application/json;charset=utf-8',
			async:true,
			success: function (res) {
				if (str==='type') {
					if (res.code == 0) {
						$("#ul_inquire").empty();
						$("#ul_inquire").append();
						res.data.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="inquiry"  onclick="changeData(\'inquiry\')" value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'

							$("#ul_inquire").append(instr)
						});

						$("#ul_source").empty();
						res.data[0].sysOptionDefinitions.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="source" onclick="changeData(\'source\')"  value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'
							$("#ul_source").append(instr)
						});
						// debugger
						$("#ul_inquiry").empty();
						res.data[0].sysOptionDefinitions[0].sysOptionDefinitions.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="communication"  value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'
							$("#ul_inquiry").append(instr)
						});
					} else {
						layer.alert(res.message);
					}
				}

				if (str === 'source') {
					if (res.code == 0) {
						$("#ul_inquiry").empty();
						$("#ul_inquiry").append();
						res.data.forEach(function (a) {
							var instr='<li style="float: left;margin: 0 10px 4px 0;">\n' +
								'<input type="radio" name="communication"   value="'+a.sysOptionDefinitionId+'"><label>'+a.title+'</label>\n' +
								'</li>'

							$("#ul_inquiry").append(instr)
						})
					} else {
						layer.alert(res.message);
					}
				}

			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}
$(function(){
	$("#submit").click(function(){
		checkLogin();
		$(".warning").remove();
		$("input").removeClass("errorbor");
		$("select").removeClass("errorbor");
		$("textarea").removeClass("errorbor");
		if($("#receiveTime").val()==''){
			warnTips("receiveTime","商机时间不允许为空");
			return  false;
		}else{
			delWarnTips("receiveTime");
		}
		var inquiry=$('input:radio[name="inquiry"]:checked').val();
		if(inquiry==undefined||inquiry==""){
			$("#inquire").css("display","");
			return false;
		}else{
			$("#inquire").css("display","none");
		}
		var source=$('input:radio[name="source"]:checked').val();
		if(source==undefined||source==""){
			$("#source").css("display","");
			return false;
		}else{
			$("#source").css("display","none");
		}
		var communication=$('input:radio[name="communication"]:checked').val();
		if(communication==undefined||communication==""){
			$("#communication").css("display","");
			return false;
		}else{
			$("#communication").css("display","none");
		}
		// if($('#communication').val()=='' || $('#communication').val()== 0){
		// 	$("#sourceError").css("display","");
		// 	return false;
		// }else{
		// 	$("#sourceError").css("display","none");
		// }

        var goodsCategory=$('input:radio[name="goodsCategory"]:checked').val();
        if(goodsCategory==undefined||goodsCategory==""){
            $("#goodsCategory").css("display","");
            return false;
        }else{
            $("#goodsCategory").css("display","none");
        }
		$('#content').val(getContentStr());
		if($('#content').val()==''){
			warnTips("content","询价产品不允许为空");
			return false;
		}
		if($("#content").val().length>512){
			warnTips("content","询价产品内容不允许超过512字符");
			return false;
		}
		if ($('#productComments').val().length > 1000){
			warnTips("productComments","产品备注不允许超过1000字符");
			return false;
		}
		if($('#traderName').val()!=''&&$('#traderName').val().length>256){
			warnTips("traderName","客户名称不允许超过256字符");
			return false;
		}else{
			delWarnTips("traderName");
		}

		if($('#traderContactName').val()!=''&&$('#traderContactName').val().length>256){
			warnTips("traderContactName","联系人名称不允许超过256字符");
			return false;
		}else{
			delWarnTips("traderContactName");
		}
		var mobileReg = /^1\d{10}$|^$/;
		if($('#mobile').val()!=''&&!mobileReg.test($('#mobile').val())){
			warnTips("mobile","手机号格式错误");
			return  false;
		}else{
			delWarnTips("mobile");
		}
		if($('#mobile').val()!=''&&$('#mobile').val().length>16){
			warnTips("mobile","手机号不允许超过16字符");
			return false;
		}else{
			delWarnTips("mobile");
		}
		var telephoneReg = /^(\d{3,4}-?)?\d{7,9}(-?\d{2,6})?$|^$/;
		if($('#telephone').val()!=''&&!telephoneReg.test($('#telephone').val())){
			warnTips("telephone","电话格式错误");
			return  false;
		}else{
			delWarnTips("telephone");
		}
		if($('#telephone').val()!=''&&$('#telephone').val().length>32){
			warnTips("telephone","电话不允许超过32字符");
			return false;
		}else{
			delWarnTips("telephone");
		}
		if($('#otherContact').val()!=''&&$('#otherContact').val().length>64){
			warnTips("otherContact","其他联系方式不允许超过64字符");
			return false;
		}else{
			delWarnTips("otherContact");
		}
		if($('#userId').val()=='' || $('#userId').val()== 0){
			warnTips("userId","分配销售不允许为空");
			return false;
		}else{
			delWarnTips("userId");
		}

		if($("#comments").val()!='' && $("#comments").val().length > 128){
			warnTips("comments","备注长度不允许超过128字符");
			return  false;
		}else{
			delWarnTips("comments");
		}
		$('#city').val( $('#city').val() == undefined ||  $('#city').val() == null ? 0 :  $('#city').val());
		$('#zone').val( $('#zone').val() == undefined ||  $('#zone').val() == null ? 0 :  $('#zone').val());

	});

	window.setTraderInfo = function (traderItem) {
		$('#traderContactName').val(traderItem.traderContactName);
        $('#traderName').val(traderItem.traderName);
        $('.J-text').html(traderItem.traderName);

		$('#telephone').val(traderItem.traderContactTelephone);
		$('#otherContact').val(traderItem.traderContactMobile2);

		//填充确认客户模块信息
		$('#checkTraderName').val(traderItem.traderName);
		$('#checkTraderArea').val(traderItem.address);
		$('#checkTraderContactName').val(traderItem.traderContactName);
		$('#checkMobile').val(traderItem.traderContactMobile);
		$('#checkTraderContactTelephone').val(traderItem.traderContactTelephone);
		$('#traderId').val(traderItem.traderId);

		//地址
		if(traderItem.areaIds){
			var area = traderItem.areaIds.split(',');
			$("#province").val(area[0]);
            $("#province").select2('val', area[0]);
			getProvinceData(area[0], function () {
				$("#city").val(area[1]);
                $("#city").select2('val', area[1]);
				getCityData(area[1], function () {
                    if(area[2]){
                        $("#zone").val(area[2]);
                        $("#zone").select2('val', area[2]);
                    }
				});
			});
		}

		//归属销售
		if (traderItem.personalId){
			$('#userId').val(traderItem.personalId);
		}
		$('#mobile').val(traderItem.traderContactMobile);
		getUserByMobile()
	};

	$("#mobile").change(function () {
		getUserByMobile();
	})
	function getUserByMobile() {
		var mobile=$("#mobile").val()
		if(!mobile){
			return;
		}
		$.ajax({
			url: '/order/bussinesschance/chance/merge/old/user.do',
			data: {
				mobile: mobile
			},
			dataType: 'json',
			success: function (res) {
				if (res&&res.code==0&&res.data) {
					$('#userId').val(res.data.userId);
				}
			}
		})
	}
	new SuggestSelect({
		wrap: '.J-trader-select-wrap',
		placeholder: '请选择客户公司完整名称',
		searchPlaceholder: '请输入客户公司名称',
		asyncSearch: true,
		searchUrl: page_url + '/trader/customer/searchCustomer.do',
		asyncSearchName: 'searchTraderName',
		input: $('[name=traderId]'),
		emptyTmpl: $('.J-empty').html(),
		dataparse: function (data) {
			var resData = [];
			$.each(data.data, function (i, obj) {
				resData.push({
					label: obj.traderName,
					value: obj.traderId,
					tip: obj.personal,
					params: obj
				})
			});

			return resData;
		},
		onchange: function (data, item) {
			var traderItem = item.params;
			setTraderInfo(traderItem);
		},
		afterSearch: function () {
			var layerParams = JSON.parse($('.J-eye-layer').attr('layerparams'));

			layerParams.link = '/trader/customer/add.do?optType=1 &traderName=' + $('.J-suggest-select-input').val();
			$('.J-eye-layer').attr('layerparams', JSON.stringify(layerParams));
		}
	});

	//编辑页面选择客户名渲染
	if ($('[name="traderName"]').val()) {
		$('.J-trader-select-wrap .J-text').html($('[name="traderName"]').val());
	}

	$('.J-trader-select-wrap').on('click', '.J-eye-look', function () {
		window.localStorage.setItem('needClick', '1');
		$('.J-eye-layer').click();
	})
	$('.J-trader-select-wrap').on('click', '.J-eye-look1', function () {
		$('.J-eye-layer1').click();
	})

});
//天眼查选择客户回调
function newCustomer(traderId) {
	$.ajax({
		url: '/trader/customer/searchCustomer.do',
		data: {
			traderId: traderId
		},
		dataType: 'json',
		success: function (res) {
			if (res.code == 0) {
				setTraderInfo(res.data[0]);
			}else {
				layer.alert(res.message);
			}
		}
	});
	layer.closeAll();
}

function uploadFile(obj,num){
	checkLogin();
	var imgPath = $(obj).val();
	if(imgPath == '' || imgPath == undefined){
		return false;
	}
	var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
	//判断上传文件的后缀名
	var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
	if (strExtension != 'jpg'&& strExtension != 'gif' && strExtension != 'png' && strExtension != 'pdf' && strExtension != 'doc' && strExtension != 'docx' && strExtension != 'xls' && strExtension != 'xlsx') {
		$('#upload1').css("display","");
		return false;
	}else{
		$('#upload1').css("display","none");
	}
	  var fileSize = 0;
	  var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
	  if (isIE && !obj.files) {
	     var filePath = obj.value;
	     var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
	     var file = fileSystem.GetFile (filePath);
	     fileSize = file.Size;
	  }else {
	     fileSize = obj.files[0].size;
	  }
	  fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
	  if(fileSize>2048){
		  //$("#name_"+num ).val(oldName);
		  //$("#uri_"+num ).val("");
		  //$('#upload2').css("display","");
		  //$("#uri_"+num ).siblings("i").css("display","none");
		  layer.alert("图片文件大小应为2MB以内",{ icon: 2 });
	    return false;
	  }else{
		 // $('#upload2').css("display","none");
	  }
	  $("#img_icon_" + num).attr("class", "iconloading mt5").show();
		$("#img_view_" + num).hide();
		$("#img_del_" + num).hide();
		var domain = $("#domain").val();
	$.ajaxFileUpload({
		url : page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
		secureuri : false, //一般设置为false
		fileElementId : $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
		dataType : 'json',//返回值类型 一般设置为json
		complete : function() {//只要完成即执行，最后执行
		},
		//服务器成功响应处理函数
		success : function(data) {
			if (data.code == 0) {
				$("#name_"+num ).val(oldName);
				$("#uri_"+num ).val(data.filePath);
				$("#img_icon_" + num).attr("class", "iconsuccesss mt5").show();
				$("#img_view_" + num).attr("href", 'http://' + domain + data.filePath).show();
				$("#img_del_" + num).show();
			} else {
				layer.alert(data.message);
			}
		},
		//服务器响应失败处理函数
		error : function(data, status, e) {
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}else{
				layer.alert(data.responseText);
			}

		}
	});

}

function del(num){
	checkLogin();
	index = layer.confirm("您是否确认该操作？", {
		  btn: ['确定','取消'] //按钮
		}, function(){
		$("#img_icon_" + num).hide();
		$("#img_view_" + num).hide();
		$("#img_del_" + num).hide();
		$("#name_1").val("");
		$("#uri_1").val("")
		layer.close(index);
		}, function(){
		});

}


function addQuoteInit(traderId,bussinessChanceId,traderCustomerId){
	checkLogin();
	//验证客户信息是否被禁用
	$.ajax({
		type: "POST",
		url: page_url+"/order/quote/getTraderCustomerStatus.do",
		data: {'traderCustomerId':traderCustomerId},
		dataType:'json',
		success: function(data){
			if(data.code==0){
				//验证商机情况信息是否完整
				var bussinessLevelStrHide = $("#bussinessLevelStrHide").val();
				var bussinessStageStrHide = $("#bussinessStageStrHide").val();
				var enquiryTypeStrHide = $("#enquiryTypeStrHide").val();
				var orderRateStrHide = $("#orderRateStrHide").val();
				var amountHide = $("#amountHide").val();
				var orderTimeHide = $("#orderTimeHide").val();
				if(bussinessLevelStrHide!="" && bussinessStageStrHide!="" && enquiryTypeStrHide!="" && orderRateStrHide!="" && amountHide!="" && orderTimeHide!="" ){
					$("#addQuoteDiv").attr('tabtitle','{"num":"addQuote'+traderId+bussinessChanceId+'","link":"./order/quote/addQuote.do?traderId='+traderId+'&bussinessChanceId='+bussinessChanceId+'","title":"新增报价"}');
					$("#addQuoteDiv").click();
				}else{
					layer.alert("请先补充商机情况信息",{ icon: 2 });
				}
			}else{
				layer.alert(data.message,{ icon: 2 });
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}

/**
 * @description 添加商品产品分类信息
 * @param categoryName 产品分类信息
 * <AUTHOR>
 * @date 2020/6/15 13:55:20
 */
function addCategoryName(categoryName) {
	var productCountFlag = $('#productCountFlag').val();
	$('#content_' + productCountFlag).val(categoryName);
	var count = parseFloat(productCountFlag) + 1;
	$('#productCountFlag').val(count);
	$('#productDiv_' + productCountFlag).after(
		" <li id=\"productDiv_" + count + "\">\n" +
		"                        <div class=\"infor_name\" id=\"productTitleDiv_" + count + "\" idFlag=\"" + count + "\">\n" +
		"                            <label></label>\n" +
		"                        </div>\n" +
		"                        <div class=\"f_left\">\n" +
		"                            <input class=\"input-larger\" id=\"content_" + count + "\" />\n &nbsp;  <a onclick='deleteCategoryName(" + count + ")'>删除</a>" +
		"                        </div>\n" +
		"                    </li>"
	);
	$('#content').val(getContentStr());
	layer.closeAll();
}

/**
 * @description 获取商机产品的拼接结果
 * <AUTHOR>
 * @date 2020/6/16 14:32:12
 */
function getContentStr() {
	var contentResult = '';
	var contents = $("input[id^=content_]");
	contents.each(function () {
		contentResult = addResultStr(contentResult, $(this).val());
    })
    return contentResult;
}

/**
 * @description 循环添加每个输入框的产品
 * @param result
 * @param str
 * @returns {*}
 * <AUTHOR>
 * @date 2020/6/16 14:36:18
 */
function addResultStr(result, str) {
	var str = str.replace(/(^\s*)|(\s*$)/g, '');
	if (!(str == '' || str == undefined || str == null)) {
	    result = result == '' ? str : result + '&&' + str;
	}
	return result;
}

/**
 * @description 删除对应的产品分类信息
 * @param productCountFlag 下标
 * <AUTHOR>
 * @date 2020/6/15 15:33:12
 */
function deleteCategoryName(productCountFlag) {
	layer.confirm('确认删除此产品？',
		{btn: ['确定', '取消'], title: "提示"},
		function () {
			var countFlag = $('#productCountFlag').val();
            //是否为最后一个div
			if (productCountFlag != countFlag){
                var idFlag = $("div[id^=productTitleDiv_]")[0].getAttribute('idFlag');
				$('#productDiv_' + productCountFlag).remove();
                //是否为第一个div
				if (productCountFlag == idFlag){
                    $("div[id^=productTitleDiv_]")[0].innerHTML = "<span>*&nbsp;</span><label>询价产品</label>"
                }
				layer.closeAll();
			} else {
				layer.alert('未选择产品名称无需删除');
			}
			$('#content').val(getContentStr());
		})
}

