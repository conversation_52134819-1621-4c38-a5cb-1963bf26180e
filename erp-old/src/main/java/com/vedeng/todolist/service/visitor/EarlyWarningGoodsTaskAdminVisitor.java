package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.flash.constant.FlashConstant;
import com.vedeng.flash.dao.EarlyWarningTaskMapper;
import com.vedeng.flash.model.EarlyWarningTask;
import com.vedeng.todolist.dto.EarlyWarningGoodsTaskToDoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.CollectionUtils;
import org.aspectj.apache.bcel.classfile.Code;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class EarlyWarningGoodsTaskAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild> {

    @Autowired
    private EarlyWarningTaskMapper earlyWarningTaskMapper;

    @Autowired
    private OrganizationMapper organizationMapper;

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild todoDtoBuild) {
        List<Organization> orgaList = (List<Organization>) ThreadLocalContext.get("orgaList");
        EarlyWarningGoodsTaskToDoDto earlyWarningGoodsTaskToDoDto = new EarlyWarningGoodsTaskToDoDto();
        if(CollectionUtils.isNotEmpty(orgaList)){
            List<Integer> orgIdList = orgaList.stream().map(Organization::getOrgId).collect(Collectors.toList());
            List<User> userListBtOrgId = organizationMapper.getUserListBtOrgId(orgIdList, FlashConstant.BUY_TYPE, FlashConstant.COMPAMY_ID);
            if(CollectionUtils.isNotEmpty(userListBtOrgId)){
                List<Integer> userIdList = userListBtOrgId.stream().map(User::getUserId).collect(Collectors.toList());
                List<EarlyWarningTask> earlyWarningTaskList = earlyWarningTaskMapper.getEarlyWarningGoodsTaskListByuserIds(userIdList);

                if (CollectionUtils.isNotEmpty(earlyWarningTaskList)) {
                    long adventCount = earlyWarningTaskList.stream().filter(e -> FlashConstant.TASK_STATUS_ADVENT.equals(e.getTaskStatus())).count();
                    long overdueCount = earlyWarningTaskList.stream().filter(e -> !(FlashConstant.TASK_STATUS_ADVENT.equals(e.getTaskStatus()) || FlashConstant.TASK_STATUS_INIT.equals(e.getTaskStatus()))).count();
                    earlyWarningGoodsTaskToDoDto.setAdventExpeditingTaskNum((int) adventCount);
                    earlyWarningGoodsTaskToDoDto.setOverdueExpeditingTaskNum((int) overdueCount);
                    earlyWarningGoodsTaskToDoDto.setTotalExpeditingTaskNum((int) (adventCount + overdueCount));
                }
            }
        }
        todoDtoBuild.get().setEarlyWarningGoodsTaskToDoDto(earlyWarningGoodsTaskToDoDto);
    }
}
