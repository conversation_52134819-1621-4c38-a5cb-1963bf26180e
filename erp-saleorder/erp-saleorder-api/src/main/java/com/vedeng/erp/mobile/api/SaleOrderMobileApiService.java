package com.vedeng.erp.mobile.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.erp.mobile.dto.*;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;

import java.math.BigDecimal;
import java.util.List;

public interface SaleOrderMobileApiService {

    /**
     * 获取当前用户下销售订单归属信息
     */
    SaleOrderAttributionInfoDto getSaleOrderAttributionInfo(Integer userId);

    /**
     * 分页查询销售订单列表
     */
    PageInfo<SaleOrderListResultDto> getSaleOrderListPage(SaleOrderListQueryDto saleOrderListQueryDto);

    /**
     * 查询销售订单下的快递信息
     */
    List<SaleOrderExpressInfoDto> getExpressInfoOfSaleOrder(Integer saleOrderId, List<Integer> saleOrderGoodsIds);

    /**
     * 查询销售订单下的发票信息
     */
    List<SaleOrderInvoiceInfoDto> getInvoiceInfoOfSaleOrder(List<Integer> saleOrderIds);

    /**
     * 查询销售订单详情
     */
    SaleOrderDetailInfoDto getSaleOrderDetail(Integer saleOrderId);

    /**
     * 获取销售订单的实际金额（减去售后完结的售后单）
     */
    BigDecimal getRealTotalAmountOfSaleOrder(Integer saleOrderId);

    /**
     * 根据快递id和物流单号获取真实物流信息
     */
    SaleOrderExpressInfoDto getExpressByExpressIdAndLogisticsNo(Integer expressId, String logisticsNo);

    /**
     * 根据快递id和快递类型获取收货手机号
     */
    List<String> getExpressPhoneByBusinessType(Integer businessType, Integer expressId);

    /**
     * 获取快递100的物流信息
     */
    String getLogisticsDetail(String logisticsName, String logisticsNo, String phone);

    /**
     * 根据客户名称查询最近一次的订单-按创建时间
     * @param customerName
     * @return
     */
    SaleorderInfoDto querySaleorderByCustomerName(String customerName);

}
