package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import com.vedeng.erp.kingdee.service.KingDeePayCommonApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 采购应付单推送
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchPurchaseInvoicePayCommonWriter extends BaseWriter<KingDeePayCommonDto> {

    @Autowired
    private KingDeePayCommonApiService kingDeePayCommonApiService;


    @Override
    public void doWrite(KingDeePayCommonDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("采购发票应付单推送：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.savePurchasePayCommon);
        kingDeePayCommonApiService.register(item,true);
    }

}
