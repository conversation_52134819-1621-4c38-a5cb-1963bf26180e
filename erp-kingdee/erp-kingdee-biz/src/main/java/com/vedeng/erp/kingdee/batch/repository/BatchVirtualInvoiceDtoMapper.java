package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchVirtualInvoiceDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 * @description ${end}
 * @date 2023/11/27 9:33
 **/
public interface BatchVirtualInvoiceDtoMapper {
    int insert(BatchVirtualInvoiceDto record);

    int insertSelective(BatchVirtualInvoiceDto record);

    BatchVirtualInvoiceDto selectByInvoiceNoCodeAndSourceId(BatchVirtualInvoiceDto record);

    int updateByVirtualInvoiceId(BatchVirtualInvoiceDto updated);

    List<BatchVirtualInvoiceDto> selectBySourceInvoiceIds(@Param("list")List<Integer> sourceInvoiceId);

    Integer selectRedCountByInvoiceNo(@Param("list") List<Integer> businessOrderIds, @Param("invoiceNo") String invoiceNo);


    List<BatchVirtualInvoiceDto> findByAll(BatchVirtualInvoiceDto batchVirtualInvoiceDto);

    List<BatchVirtualInvoiceDto> findRedByAll(BatchVirtualInvoiceDto batchVirtualInvoiceDto);


    int deleteByVirtualInvoiceId(@Param("virtualInvoiceId")Integer virtualInvoiceId);

    BatchVirtualInvoiceDto selectByVirtualInvoiceId(@Param("virtualInvoiceId")Integer virtualInvoiceId);


}