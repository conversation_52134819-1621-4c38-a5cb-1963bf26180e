package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.flash.dto.EarlyWarningTaskDto;
import com.vedeng.flash.service.warningtask.ExpeditingTaskService;
import com.vedeng.order.service.BuyorderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 采购单生成催货预警任务定时任务
 *
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/5/18 15:56.
 * @author: <PERSON><PERSON>.
 */
@Component
@JobHandler(value = "BuyorderReminderGoodsProduceTask")
public class BuyorderReminderGoodsProduceTask extends AbstractJobHandler {

    static final Logger logger = LoggerFactory.getLogger(BuyorderReminderGoodsProduceTask.class);

    @Autowired
    BuyorderService buyorderService;

    @Autowired
    ExpeditingTaskService expeditingTaskService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("================开始执行采购单生成催货预警的任务=============");
        logger.info("================开始执行采购单生成催货预警的任务=============");
        //得到需要插入表的数据
        List<EarlyWarningTaskDto> earlyWarningTaskDtoList = buyorderService.getRemindGoodsTask();
        try {
            if(CollectionUtils.isNotEmpty(earlyWarningTaskDtoList)){
                for (EarlyWarningTaskDto earlyWarningTaskDto : earlyWarningTaskDtoList) {
                    expeditingTaskService.create(earlyWarningTaskDto);
                    buyorderService.updateGenerateInfo(earlyWarningTaskDto);
                }
            }
        } catch (Exception e) {
            logger.error("采购单生成催货预警的任务执行失败,错误原因：{}",e);
            XxlJobLogger.log("采购单生成催货预警的任务执行失败,错误原因：{}",e.getMessage());
            return FAIL;
        }
        XxlJobLogger.log("================采购单生成催货预警的任务执行成功=============");
        logger.info("================采购单生成催货预警的任务执行成功=============");
        return SUCCESS;
    }

}
