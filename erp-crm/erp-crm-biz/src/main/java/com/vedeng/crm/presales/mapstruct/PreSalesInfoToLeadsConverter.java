package com.vedeng.crm.presales.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.presales.dto.PreSalesInfoDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import org.mapstruct.*;

import java.util.Date;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/9
 */
@Mapper(componentModel = "spring",builder = @Builder(disableBuilder = true))
public interface PreSalesInfoToLeadsConverter  {

    /**
     * Entity转DTO
     *
     * @param entity BusinessLeadsEntity
     * @return BusinessLeadsDto
     */
    @Mapping(source="leadsNo",target = "leadsNo")
    @Mapping(source="belongPic",target = "belongerPic")
    @Mapping(source="clueType",target = "clueType")
    @Mapping(source="sendVx",target = "sendVx")
    @Mapping(source="inquiry",target = "inquiry")
    @Mapping(source="source",target = "source")
    @Mapping(source="communication",target = "communication")
    @Mapping(source="content",target = "content")

    @Mapping(source="traderName",target = "traderName")
    @Mapping(source="traderId",target = "traderId")
    @Mapping(source="tycFlag",target = "tycFlag")
    @Mapping(source="phone",target = "phone")
    @Mapping(source="telephone",target = "telephone")
    @Mapping(source="contact",target = "contact")
    @Mapping(source="traderContactId",target = "traderContactId")
    @Mapping(source="otherContactInfo",target = "otherContactInfo")
    @Mapping(source="tagIds",target = "tagIds")
    @Mapping(source="businessType",target = "businessType")//（业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
    @Mapping(target = "goodsInfo", source = "goodsInfo")//产品信息-商机里是productInfo
    @Mapping(target = "amount", source = "amount")//预计成单金额-列表字段
    @Mapping(target = "orderTime", source = "orderTime", qualifiedByName = "date2Long")//预计成单日期
//    @Mapping(target = "tagIdList", source = "tagIdList")//线索标签id list
    @Mapping(target = "customerRelationship", source = "customerRelationship")//客情关系(1熟悉终端决策人，2熟悉使用人，多选逗号分隔)【多选】
    @Mapping(target = "purchasingType", source = "purchasingType")//采购方式-字典值-（直接采购 招投标采购）
    @Mapping(target = "biddingPhase", source = "biddingPhase")//招投标阶段-字典值(提案咨询、立项论证、意向公示、公开招标、合同签署)
    @Mapping(target = "biddingParameter", source = "biddingParameter")//招标参数(1可调整，2不可调整)-搜索用
    @Mapping(target = "terminalTraderName", source = "terminalTraderName")//终端名称
    @Mapping(target = "terminalTraderNature", source = "terminalTraderNature")//终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
    @Mapping(target = "terminalTraderRegion", source = "terminalTraderRegion")//终端区域(省市区id，逗号分隔，省市区三级ID，从高向低拼接)-210000,210400,210403
    @Mapping(target = "remark", source = "remark")//备注
    @Mapping(target = "orderTerminalDto", source = "orderTerminalDto")//备注
    @Mapping(target = "communicateRecordDto", source = "communicateRecordDto")//备注
    @Mapping(target = "belongerId", source = "belongerId")//备注
    @Mapping(target = "belonger", source = "belonger")//备注
    @Mapping(target = "provinceId", source = "provinceId")//省ID
    @Mapping(target = "cityId", source = "cityId")//市ID
    @Mapping(target = "countyId", source = "countyId")//区ID
    @Mapping(target = "province", source = "province")//省-名称
    @Mapping(target = "city", source = "city")//市-名称
    @Mapping(target = "county", source = "county")//区-名称
    BusinessLeadsDto toDto(PreSalesInfoDto entity);

    /**
     * DTO转Entity
     *
     * @param dto BusinessLeadsDto
     * @return PreSalesInfoDto
     */
    @Mapping(source="leadsNo",target = "leadsNo")
    @Mapping(source="belongerPic",target = "belongPic")
    @Mapping(source = "clueType", target = "clueType")
    @Mapping(source = "sendVx", target = "sendVx")
    @Mapping(source = "inquiry", target = "inquiry")
    @Mapping(source = "source", target = "source")
    @Mapping(source = "communication", target = "communication")
    @Mapping(source = "content", target = "content")
    @Mapping(source = "traderName", target = "traderName")
    @Mapping(source = "traderId", target = "traderId")
    @Mapping(source = "tycFlag", target = "tycFlag")
    @Mapping(source = "phone", target = "phone")
    @Mapping(source = "telephone", target = "telephone")
    @Mapping(source = "contact", target = "contact")
    @Mapping(source = "traderContactId", target = "traderContactId")
    @Mapping(source = "otherContactInfo", target = "otherContactInfo")
    @Mapping(source="tagIds",target = "tagIds")
    @Mapping(source = "businessType", target = "businessType")
    @Mapping(source = "goodsInfo", target = "goodsInfo")
    @Mapping(source = "amount", target = "amount")
    @Mapping(source = "orderTime", target = "orderTime", qualifiedByName = "long2Date")
    @Mapping(source = "customerRelationship", target = "customerRelationship")
    @Mapping(source = "purchasingType", target = "purchasingType")
    @Mapping(source = "biddingPhase", target = "biddingPhase")
    @Mapping(source = "biddingParameter", target = "biddingParameter")
    @Mapping(source = "terminalTraderName", target = "terminalTraderName")
    @Mapping(source = "terminalTraderNature", target = "terminalTraderNature")
    @Mapping(source = "terminalTraderNatureStr", target = "terminalTraderNatureStr")
    @Mapping(source = "terminalTraderRegion", target = "terminalTraderRegion")
    @Mapping(source = "terminalTraderRegionStr", target = "terminalTraderRegionStr")
    @Mapping(source = "remark", target = "remark")
    @Mapping(source = "orderTerminalDto", target = "orderTerminalDto")
    @Mapping(source = "communicateRecordDto", target = "communicateRecordDto")
    @Mapping(source = "belongerId", target = "belongerId")
    @Mapping(source = "belonger", target = "belonger")
    @Mapping(source = "provinceId", target = "provinceId")
    @Mapping(source = "cityId", target = "cityId")
    @Mapping(source = "countyId", target = "countyId")
    @Mapping(source = "province", target = "province")
    @Mapping(source = "city", target = "city")
    @Mapping(source = "county", target = "county")
    @Mapping(source = "inquiryName", target = "inquiryName")
    @Mapping(source = "sourceName", target = "sourceName")
    @Mapping(source = "communicationName", target = "communicationName")
    @Mapping(source = "businessTypeStr", target = "businessTypeStr")
    @Mapping(source = "tags", target = "tags")
    @Mapping(source = "customerRelationshipStr", target = "customerRelationshipStr")
    @Mapping(source = "purchasingTypeStr", target = "purchasingTypeStr")
    @Mapping(source = "biddingPhaseStr", target = "biddingPhaseStr")
    @Mapping(source = "followStatus", target = "followStatus")
    @Mapping(source = "entrances", target = "entrances")
    @Mapping(source = "functions", target = "functions")
    @Mapping(source = "entrancesName", target = "entrancesName")
    @Mapping(source = "functionsName", target = "functionsName")
    @Mapping(source = "closeTime", target = "closeTime")
    @Mapping(source = "closeReason", target = "closeReason")
    @Mapping(source = "closeReasonType", target = "closeReasonType")
    @Mapping(source = "closeReasonTypeName",target = "closeReasonTypeName")//关闭原因类型对应的名称
    @Mapping(source = "customerNature", target = "customerNature")
    PreSalesInfoDto toResponseBody(BusinessLeadsDto dto);

    /**
     * str转strList
     *
     * @param date
     * @return long
     */
    @Named("date2Long")
    default Long date2Long(Date date) {
        if(date == null)
        {
            return null;
        }
        return date.getTime();
    }

    @Named("long2Date")
    default Date long2Date(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp);
    }
}
