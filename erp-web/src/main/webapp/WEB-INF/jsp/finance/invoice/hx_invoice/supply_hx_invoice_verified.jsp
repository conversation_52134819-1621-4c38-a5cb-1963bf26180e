<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="进项票（供应链）" scope="application"/>
<%@ include file="../../../common/common.jsp" %>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/supply_hx_invoice_verified.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/hx_invoice_common.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<div class="layui-tab layui-tab-brief" lay-filter="test">
    <ul class="layui-tab-title">
        <li onclick="changeTab(0)">待录票</li>
        <li onclick="changeTab(1)">审核中</li>
        <li onclick="changeTab(2)" class="layui-this">已审核</li>
        <li onclick="changeTab(3)">待退票</li>
        <li onclick="changeTab(4)">无效票</li>
    </ul>
</div>
<div class="layui-tab-item layui-show">
    <div class="searchfunc">
        <form method="post" id="search" action="/supplyChain/invoice/hx_invoice_wait.do?idFlag=2">
            <ul>
                <li>
                    <label class="infor_name">发票号</label>
                    <input type="text" class="input-middle" name="invoiceNum" id="invoiceNum"
                           value="${invoiceSearch.invoiceNum}"/>
                </li>

                <li>
                    <label class="infor_name">订单号</label>
                    <input type="text" class="input-middle" name="saleorderNo" id="saleorderNo"
                           value="${invoiceSearch.saleorderNo}"/>
                </li>

                <li>
                    <label class="infor_name">开票方名称</label>
                    <input type="text" class="input-middle" name="salerName" id="salerName"
                           value="${invoiceSearch.salerName}"/>
                </li>

                <li>
                    <label class="infor_name">票种</label>
                    <select class="input-middle" name="invoiceTaxRate" id="invoiceTaxRate">
                        <option value="0"
                                <c:if test="${invoiceSearch.invoiceTaxRate == 0}">selected</c:if> >全部
                        </option>
                        <option value="971"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 971}">selected="selected"</c:if>>
                            13%增值税普通发票
                        </option>
                        <option value="972"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 972}">selected="selected"</c:if>>
                            13%增值税专用发票
                        </option>
                        <option value="681"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 681}">selected="selected"</c:if>>
                            16%增值税普通发票
                        </option>
                        <option value="682"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 682}">selected="selected"</c:if>>
                            16%增值税专用发票
                        </option>
                        <option value="683"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 683}">selected="selected"</c:if>>6%增值税普通发票
                        </option>
                        <option value="684"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 684}">selected="selected"</c:if>>6%增值税专用发票
                        </option>
                        <option value="685"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 685}">selected="selected"</c:if>>3%增值税普通发票
                        </option>
                        <option value="686"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 686}">selected="selected"</c:if>>3%增值税专用发票
                        </option>
                        <option value="687"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 687}">selected="selected"</c:if>>0%增值税普通发票
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">发票总额</label>
                    <input class="f_left input-smaller96 mr5" type="text" name="invoiceAmountFrom"
                           id="invoiceAmountFrom" value='${invoiceSearch.invoiceAmountFrom}' onchange="checkValue(this)">
                    <div class="gang">-</div>
                    <input class="f_left input-smaller96" type="text" name="invoiceAmountTo" id="invoiceAmountTo"
                           value='${invoiceSearch.invoiceAmountTo}' onchange="checkValue(this)">
                </li>

                <li>
                    <label class="infor_name">审核状态</label>
                    <select class="input-middle" name="validStatus" id="validStatus">
                        <option value="0"
                                <c:if test="${invoiceSearch.validStatus == 0}">selected</c:if> >全部
                        </option>
                        <option value="1" <c:if test="${invoiceSearch.validStatus == 1}">selected</c:if>>审核通过</option>
                        <option value="2" <c:if test="${invoiceSearch.validStatus == 2}">selected</c:if>>审核不通过</option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">录票人</label>
                    <select class="input-middle" name="entryUser" id="entryUser">
                        <option value="0">全部</option>
                        <c:forEach items="${entryUsers}" var="entryUser">
                            <option value="${entryUser.userId}"
                                    <c:if test="${invoiceSearch.entryUser eq entryUser.userId}">selected="selected"</c:if>>${entryUser.username}</option>
                        </c:forEach>
                    </select>
                </li>

                <li>
                    <div class="infor_name">
                        <select name="timeSearchType" id="timeSearchType">
                            <option value="0"
                                    <c:if test="${invoiceSearch.timeSearchType == 0}">selected</c:if> >申请日期
                            </option>
                            <option value="1"
                                    <c:if test="${invoiceSearch.timeSearchType == 1}">selected</c:if> >处理日期
                            </option>
                        </select>
                    </div>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" placeholder="请选择日期"
                           onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                           name="startAddDateStr" id="startAddDateStr"
                           value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" onClick="WdatePicker()" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                           name="endAddDateStr" id="endAddDateStr"
                           value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">查询</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
            </div>
        </form>
        <br>
        <div class="content">
            <div class="">
                <div style="width:1752px;" class='superdiv'>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid4">序号</th>
                            <th class="wid10">发票号</th>
                            <th class="wid10">发票金额</th>
                            <th class="wid10">不含税金额</th>
                            <th class="wid10">税额</th>
                            <th class="wid14">票种</th>
                            <th class="wid8">红蓝字</th>
                            <th class="wid10">录票人员</th>
                            <th class="wid10">关联订单号</th>
                            <th class="wid16">开票方名称</th>
                            <th class="wid16">申请日期</th>
                            <th class="wid16">审核日期</th>
                            <th class="wid8">审核人</th>
                            <th class="wid8">审核状态</th>
                            <th class="wid14">审核备注</th>
                            <th class="wid12">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:set var="pageAmount" value="0"></c:set><!-- 当前页总额 -->
                        <c:set var="lzyxTaxFreeAmount" value="0"></c:set><!-- 不含税金额 -->
                        <c:set var="lzyxTaxAmount" value="0"></c:set><!-- 税额 -->
                        <c:set var="pageNum" value="0"></c:set><!-- 当前页记录数 -->
                        <c:forEach var="hxInvoiceVo" items="${list}" varStatus="invoiceStatus">
                            <c:set var="pageNum" value="${pageNum + 1}"></c:set>
                            <tr>
                                <td>
                                        ${invoiceStatus.count}
                                </td>

                                <td>
                                        ${hxInvoiceVo.invoiceNum}
                                </td>

                                <td>
                                    <fmt:formatNumber type="number" value="${hxInvoiceVo.amount}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                    <c:set var="pageAmount" value="${hxInvoiceVo.amount + pageAmount}"></c:set>
                                </td>

                                <td>
                                    <fmt:formatNumber type="number" value="${hxInvoiceVo.invoiceAmount}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                    <c:set var="lzyxTaxFreeAmount" value="${hxInvoiceVo.invoiceAmount + lzyxTaxFreeAmount}"></c:set>
                                </td>

                                <td>
                                    <fmt:formatNumber type="number" value="${hxInvoiceVo.taxAmount}" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                    <c:set var="lzyxTaxAmount" value="${hxInvoiceVo.taxAmount + lzyxTaxAmount}"></c:set>
                                </td>

                                <td>
                                    <c:choose>
                                        <c:when test="${hxInvoiceVo.taxRate == 971}">
                                            13%增值税普通发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 972}">
                                            13%增值税专用发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 681}">
                                            16%增值税普通发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 682}">
                                            16%增值税专用发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 683}">
                                            6%增值税普通发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 684}">
                                            6%增值税专用发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 685}">
                                            3%增值税普通发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 686}">
                                            3%增值税专用发票
                                        </c:when>
                                        <c:when test="${hxInvoiceVo.taxRate == 687}">
                                            0%增值税普通发票
                                        </c:when>
                                    </c:choose>
                                </td>

                                <td>
                                    <c:choose>
                                        <c:when test="${hxInvoiceVo.colorType == 1}">蓝字有效</c:when>
                                        <c:when test="${hxInvoiceVo.colorType == 2}">红字有效</c:when>
                                        <c:when test="${hxInvoiceVo.colorType == 3}">蓝字作废</c:when>
                                        <c:when test="${hxInvoiceVo.colorType == 4}">失控</c:when>
                                        <c:when test="${hxInvoiceVo.colorType == 5}">异常</c:when>
                                    </c:choose>
                                </td>

                                <td>
                                    <c:forEach items="${entryUsers}" var="entryUser">
                                        <c:if test="${entryUser.userId == hxInvoiceVo.entryUserId}">${entryUser.username}</c:if>
                                    </c:forEach>
                                </td>

                                <td>
                                        ${ hxInvoiceVo.saleorderNo}
                                </td>

                                <td>
                                        ${hxInvoiceVo.salerName}
                                </td>

                                <td>
                                    <date:date value="${hxInvoiceVo.entryTime}"/>
                                </td>

                                <td>
                                    <date:date value="${hxInvoiceVo.validTime}"/>
                                </td>

                                <td>
                                        ${hxInvoiceVo.validUser}
                                </td>

                                <td>
                                    <c:choose>
                                        <c:when test="${hxInvoiceVo.validStatus == 0}">审核中</c:when>
                                        <c:when test="${hxInvoiceVo.validStatus == 1}">审核通过</c:when>
                                        <c:when test="${hxInvoiceVo.validStatus == 2}">审核不通过</c:when>
                                    </c:choose>
                                </td>

                                <td>
                                        ${hxInvoiceVo.validComments}
                                </td>

                                <td>
                                    <a onclick="viewAndDownloadHxInvoiceHref('${hxInvoiceVo.invoiceCode}','${hxInvoiceVo.invoiceNum}','${hxInvoiceVo.hxInvoiceId}',0)">
                                        <font style="color: #438DEF"
                                              id="viewInvocieFont${hxInvoiceVo.hxInvoiceId}">查看发票</font>
                                    </a>
                                </td>
                            </tr>
                            <tr style="display: none" id="imgTr${hxInvoiceVo.hxInvoiceId}">
                                <td colspan="16" height="350px" id="invoiceImg${hxInvoiceVo.hxInvoiceId}">
                                </td>
                            </tr>
                            <input id="imgFlag${hxInvoiceVo.hxInvoiceId}" value="0" type="hidden">
                            <input id="imgSrc${hxInvoiceVo.hxInvoiceId}" value="${hxInvoiceVo.attachment}"
                                   type="hidden">
                            <input id="invoiceFrom${hxInvoiceVo.hxInvoiceId}" value="${hxInvoiceVo.invoiceFrom}"
                                   type="hidden">
                        </c:forEach>
                        </tbody>
                    </table>

                    <c:if test="${empty list}">
                        <!-- 查询无结果弹出 -->
                        <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
                    </c:if>
                </div>
            </div>
            <div>
                <tags:page page="${page}"/>
                <div class="clear"></div>
                <div class="fixtablelastline">
                    【全部结果 条目：${invoice.lzyxNum}
                    总金额：<fmt:formatNumber type="number" value="${invoice.lzyxAmount == null ? 0 : invoice.lzyxAmount}" pattern="0.00" maxFractionDigits="2" />
                    不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxFreeAmount == null ? 0 : invoice.lzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />
                    税额总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxAmount == null ? 0 : invoice.lzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />
                    】
                    【本页统计 条目：${pageNum}
                    总金额：<fmt:formatNumber type="number" value="${pageAmount}" pattern="0.00" maxFractionDigits="2" />
                    不含税总金额：<fmt:formatNumber type="number" value="${lzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />
                    税额总金额：<fmt:formatNumber type="number" value="${lzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />
                    】
                </div>
            </div>
        </div>
    </div>
</div>