@import 'font/style.css';
html {
  color: #333;
  font: 14px/1.5 '-apple-system', 'Helvetica', 'sans-serif';
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  margin: 0 auto;
  background-color: #F5F7FA;
}
body,
div,
span,
h1,
h2,
h3,
h4,
h5,
h6,
p,
input,
textarea,
blockquote,
pre,
address,
cite,
code,
del,
dfn,
em,
ins,
small,
strong,
sub,
sup,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
details,
figcaption,
figure,
footer,
header,
hgroup,
menu,
nav,
section,
summary,
time,
mark,
audio,
video {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
a {
  text-decoration: none;
  color: #333;
}
a:active,
a:visited {
  color: #333;
}
ul {
  list-style: none;
}
.clearfix:after {
  clear: both;
  /*清除浮动*/
  content: "";
  display: block;
  height: 0;
  line-height: 0;
  visibility: hidden;
}
.clearfix {
  zoom: 1;
  /*为IE6，7的兼容性设置*/
}
.float-left {
  float: left;
}
.float-right {
  float: right;
}
