package com.vedeng.erp.aftersale.dto;

import lombok.*;

import java.util.Objects;

/**
 * 售后商品ID查询请求运输类
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AfterSaleGoodsIdQueryReqDto {

    /**
     * 采购商品ID
     */
    private Integer buyOrderGoodsId;

    /**
     * 售后ID
     */
    private Integer afterSalesId;

    /**
     * 参数校验
     *
     * @param afterSaleGoodsIdQueryReqDto
     * @return
     */
    public static boolean verifyParam(AfterSaleGoodsIdQueryReqDto afterSaleGoodsIdQueryReqDto) {
        if (!Objects.nonNull(afterSaleGoodsIdQueryReqDto) || !Objects.nonNull(afterSaleGoodsIdQueryReqDto.getAfterSalesId()) || !Objects.nonNull(afterSaleGoodsIdQueryReqDto.getBuyOrderGoodsId())) {
            return false;
        }
        return true;
    }
}
