<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.GoodsPositionMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.GoodsPositionEntity">
        <!--@mbg.generated-->
        <!--@Table V_GOODS_POSITION-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="PRIORITY" jdbcType="INTEGER" property="priority"/>
        <result column="POSITION_NAME" jdbcType="VARCHAR" property="positionName"/>
        <result column="DESCRIPTION" jdbcType="VARCHAR" property="description"/>
        <result column="ORDINAL" jdbcType="INTEGER" property="ordinal"/>
        <result column="SIGN_CONTRACT_MODE" jdbcType="TINYINT" property="signContractMode"/>
        <result column="IS_DELETED" jdbcType="BOOLEAN" property="isDeleted"/>
        <result column="CREATOR_ID" jdbcType="INTEGER" property="creatorId"/>
        <result column="UPDATER_ID" jdbcType="INTEGER" property="updaterId"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID,
        PRIORITY,
        POSITION_NAME,
        DESCRIPTION,
        ORDINAL,
        SIGN_CONTRACT_MODE,
        IS_DELETED,
        CREATOR_ID,
        UPDATER_ID,
        ADD_TIME,
        MOD_TIME
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from V_GOODS_POSITION
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from V_GOODS_POSITION
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.goods.domain.entity.GoodsPositionEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into V_GOODS_POSITION (PRIORITY, POSITION_NAME, DESCRIPTION,
                                      ORDINAL, SIGN_CONTRACT_MODE, IS_DELETED,
                                      CREATOR_ID, UPDATER_ID, ADD_TIME,
                                      MOD_TIME)
        values (#{priority,jdbcType=INTEGER}, #{positionName,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
                #{ordinal,jdbcType=INTEGER}, #{signContractMode,jdbcType=TINYINT}, #{isDeleted,jdbcType=BOOLEAN},
                #{creatorId,jdbcType=INTEGER}, #{updaterId,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
                #{modTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.goods.domain.entity.GoodsPositionEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into V_GOODS_POSITION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="priority != null">
                PRIORITY,
            </if>
            <if test="positionName != null and positionName != ''">
                POSITION_NAME,
            </if>
            <if test="description != null and description != ''">
                DESCRIPTION,
            </if>
            <if test="ordinal != null">
                ORDINAL,
            </if>
            <if test="signContractMode != null">
                SIGN_CONTRACT_MODE,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="creatorId != null">
                CREATOR_ID,
            </if>
            <if test="updaterId != null">
                UPDATER_ID,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="priority != null">
                #{priority,jdbcType=INTEGER},
            </if>
            <if test="positionName != null and positionName != ''">
                #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="description != null and description != ''">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="ordinal != null">
                #{ordinal,jdbcType=INTEGER},
            </if>
            <if test="signContractMode != null">
                #{signContractMode,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=BOOLEAN},
            </if>
            <if test="creatorId != null">
                #{creatorId,jdbcType=INTEGER},
            </if>
            <if test="updaterId != null">
                #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.domain.entity.GoodsPositionEntity">
        <!--@mbg.generated-->
        update V_GOODS_POSITION
        <set>
            <if test="priority != null">
                PRIORITY = #{priority,jdbcType=INTEGER},
            </if>
            <if test="positionName != null and positionName != ''">
                POSITION_NAME = #{positionName,jdbcType=VARCHAR},
            </if>
            <if test="description != null and description != ''">
                DESCRIPTION = #{description,jdbcType=VARCHAR},
            </if>
            <if test="ordinal != null">
                ORDINAL = #{ordinal,jdbcType=INTEGER},
            </if>
            <if test="signContractMode != null">
                SIGN_CONTRACT_MODE = #{signContractMode,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=BOOLEAN},
            </if>
            <if test="creatorId != null">
                CREATOR_ID = #{creatorId,jdbcType=INTEGER},
            </if>
            <if test="updaterId != null">
                UPDATER_ID = #{updaterId,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.domain.entity.GoodsPositionEntity">
        <!--@mbg.generated-->
        update V_GOODS_POSITION
        set PRIORITY           = #{priority,jdbcType=INTEGER},
            POSITION_NAME      = #{positionName,jdbcType=VARCHAR},
            DESCRIPTION        = #{description,jdbcType=VARCHAR},
            ORDINAL            = #{ordinal,jdbcType=INTEGER},
            SIGN_CONTRACT_MODE = #{signContractMode,jdbcType=TINYINT},
            IS_DELETED         = #{isDeleted,jdbcType=BOOLEAN},
            CREATOR_ID         = #{creatorId,jdbcType=INTEGER},
            UPDATER_ID         = #{updaterId,jdbcType=INTEGER},
            ADD_TIME           = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME           = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update V_GOODS_POSITION
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PRIORITY = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.priority != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.priority,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="POSITION_NAME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.positionName != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.positionName,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="DESCRIPTION = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.description != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.description,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ORDINAL = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.ordinal != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.ordinal,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SIGN_CONTRACT_MODE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.signContractMode != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.signContractMode,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_DELETED = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isDeleted != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.isDeleted,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creatorId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.creatorId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updaterId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.updaterId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into V_GOODS_POSITION
        (PRIORITY, POSITION_NAME, DESCRIPTION, ORDINAL, SIGN_CONTRACT_MODE, IS_DELETED, CREATOR_ID,
         UPDATER_ID, ADD_TIME, MOD_TIME)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.priority,jdbcType=INTEGER}, #{item.positionName,jdbcType=VARCHAR},
             #{item.description,jdbcType=VARCHAR},
             #{item.ordinal,jdbcType=INTEGER}, #{item.signContractMode,jdbcType=TINYINT},
             #{item.isDeleted,jdbcType=BOOLEAN},
             #{item.creatorId,jdbcType=INTEGER}, #{item.updaterId,jdbcType=INTEGER}, #{item.addTime,jdbcType=TIMESTAMP},
             #{item.modTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2024-12-07-->
    <select id="findAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_GOODS_POSITION
        where IS_DELETED = 0
    </select>
</mapper>