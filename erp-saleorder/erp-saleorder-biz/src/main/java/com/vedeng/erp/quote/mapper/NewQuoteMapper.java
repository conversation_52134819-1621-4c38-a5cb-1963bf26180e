package com.vedeng.erp.quote.mapper;

import com.vedeng.erp.quote.domain.QuoteorderEntity;
import com.vedeng.erp.quote.domain.UpdateTerminalInfoDto;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.quote.mapper
 * @Date 2022/4/8 16:13
 */
public interface NewQuoteMapper {

    /**
     * 根据报价单ID查询订单客户信息
     * @param quoteorderId
     * @return
     */
    UpdateTerminalInfoDto getOrderTraderInfoByQuoteId(Integer quoteorderId);

    /**
     * 根据订单ID查询订单客户信息
     * @param saleorderId
     * @return
     */
    UpdateTerminalInfoDto getOrderTraderInfoBySaleorderId(Integer saleorderId);

    /**
     * 更新报价单终端快照信息
     * @param updateTerminalInfoDto
     */
    void updateQuoteTerminalInfo(UpdateTerminalInfoDto updateTerminalInfoDto);


    /**
     * 更新订单终端快照信息
     * @param updateTerminalInfoDto
     */
    void updateOrderTerminalInfo(UpdateTerminalInfoDto updateTerminalInfoDto);

}
