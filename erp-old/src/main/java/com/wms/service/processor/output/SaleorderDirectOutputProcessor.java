package com.wms.service.processor.output;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchDetailDto;
import com.vedeng.erp.buyorder.service.PurchaseDeliveryBatchDetailService;
import com.vedeng.erp.buyorder.service.PurchaseDeliveryBatchInfoService;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogVirtualMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogVirtualService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.constant.WmsOutInOrderConcatConstant;
import com.wms.dao.WmsOutInOrderConcatMapper;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.model.dto.SendSystemMsgDto;
import com.wms.model.po.WmsOutInOrderConcat;
import com.wms.service.WmsImagesService;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.util.WmsCommonUtil;
import lombok.NonNull;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SaleorderDirectOutputProcessor extends AbstractOutputOrderProcessor {
    Logger logger= LoggerFactory.getLogger(SaleorderDirectOutputProcessor.class);

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private SimpleMailMessage simpleMailMessage;

    @Value("${WMS_CHECK_STOCK_ALARM_MAIL}")
    protected String  WMS_CHECK_STOCK_ALARM_MAIL;

    @Autowired
    private WmsImagesService wmsImagesService;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private WmsOutInOrderConcatMapper wmsOutInOrderConcatMapper;

    @Resource
    private WarehouseGoodsOperateLogVirtualMapper warehouseGoodsOperateLogVirtualMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private WarehouseGoodsOperateLogVirtualService warehouseGoodsOperateLogVirtualService;

    @Autowired
    private PurchaseDeliveryBatchDetailService purchaseDeliveryBatchDetailService;
    @Autowired
    private PurchaseDeliveryBatchInfoService purchaseDeliveryBatchInfoService;

    @Resource
    private ExpressDetailMapper expressDetailMapper;

    @Override
    protected String getBusinessKey(OutputDto outputDto) {
        return "saleorderDirectOutput" + outputDto.getOrderNo();
    }

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected void insertOrUpdateLogicalOrderGoods(OutputDto outputDto) throws Exception {
        //直发出库回传不涉及逻辑仓表--此处剔除
    }

    @Override
    protected void synchronizeStockData(OutputDto outputOrderDto) throws Exception {
        //直发出库回传不涉及库存服务变动
    }

    @Override
    protected void insertOrUpdateWarehouseLog(OutputDto orderDto) throws Exception {
        //重写直发出库回传保存出库记录
        List<OutputGoodDto> details = orderDto.getDetails();

        for (OutputGoodDto outputGoodDto : details) {
            //获取直发回传的采购单批次详情对应物流明细id
            Integer expressdetailId =  purchaseDeliveryBatchDetailService.queryExpressDetailIdById(Integer.parseInt(outputGoodDto.getUserDefine4()));
            Express expressInfo = expressDetailMapper.getExpressByDetailId(expressdetailId);
            if (expressInfo == null) {
                logger.error("同行单数数据关联包裹信息异常 expressDetailId:{}", expressdetailId);
                throw new RuntimeException("同行单数数据关联包裹信息异常 expressDetailId:" + expressdetailId);
            }

            if(StringUtil.isBlank(outputGoodDto.getLotAtt11())){
                throw new RuntimeException(orderDto.getSOReference1()+"出库类型没有贝登批次码,消费失败");
            }

            int realateId = getRelateId(outputGoodDto);

            WarehouseGoodsOperateLogVirtual queryCon = new WarehouseGoodsOperateLogVirtual();
            queryCon.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputGoodDto.getLotAtt08()));
            queryCon.setVedengBatchNumer(outputGoodDto.getLotAtt11());
            queryCon.setGoodsId(Integer.valueOf(outputGoodDto.getSKU().substring(1)));

            List<WarehouseGoodsOperateLogVirtual> availableInputLogs = new ArrayList<>();
            //直发出库回传根据逻辑仓+贝登批次码+采购单号 修改对应入库记录
            queryCon.setDedicatedBuyorderNo(outputGoodDto.getLotAtt10());
            availableInputLogs = warehouseGoodsOperateLogVirtualMapper.getAvailableLogicalGoods(queryCon);
            if(CollectionUtils.isEmpty(availableInputLogs)){
                logger.error("出库错误 出库单号:{},查询入库记录条件:{}",
                        orderDto.getSOReference1(),outputGoodDto.getLotAtt11()+","+outputGoodDto.getLotAtt08()+","+outputGoodDto.getSKU());
                throw new Exception("出库错误 出库单号:"+orderDto.getSOReference1()+"未查询到贝登批次码"+outputGoodDto.getLotAtt11()+","+outputGoodDto.getLotAtt08()+","+outputGoodDto.getSKU());
            }

            int outNum = outputGoodDto.getQtyShipped().intValue();
            Integer erpStocknum = availableInputLogs.stream().collect(Collectors.summingInt(WarehouseGoodsOperateLogVirtual::getLastStockNum));
            if(outNum > erpStocknum){
                logger.error("出库错误 erp在库数量小于出库数量 出库单号:{},erpNum:{},wmsNum:{},查询入库记录条件:{}",
                        orderDto.getSOReference1(),erpStocknum,outNum,outputGoodDto.getLotAtt11()+","+outputGoodDto.getLotAtt08()+","+outputGoodDto.getSKU());
                throw new Exception("出库错误 erp在库数量小于出库数量 出库单号:"+orderDto.getSOReference1()+"查询到贝登批次码"+outputGoodDto.getLotAtt11()+","+outputGoodDto.getLotAtt08()+","+outputGoodDto.getSKU());
            }
            int snFlag = 0;
            //允许的sn码遍历的最大限制
            int nowMax = 0;
            for (WarehouseGoodsOperateLogVirtual inlog : availableInputLogs) {
                if(outNum == 0){
                    break;
                }
                Integer lastStockNum = inlog.getLastStockNum();
                //出库数量
                if(outNum >= lastStockNum){
                    inlog.setNum(-lastStockNum);
                    outNum = outNum - lastStockNum;
                }else if(outNum < lastStockNum){
                    inlog.setNum(-outNum);
                    outNum = 0;
                }

                //直发更新直发入库的记录
                warehouseGoodsOperateLogVirtualMapper.updateOutIsUseAndLastStockNumById(inlog);

                inlog.setBarcodeFactory("");
                //批次码
                inlog.setBatchNumber("NO-LOT".equals(outputGoodDto.getLotAtt04()) ? "" : outputGoodDto.getLotAtt04());
                //生产日期
                inlog.setProductDate(DateUtil.convertLong(outputGoodDto.getLotAtt01() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
                //效期
                inlog.setExpirationDate(DateUtil.convertLong(outputGoodDto.getLotAtt02() + " 00:00:00","yyyy-MM-dd HH:mm:ss"));
                inlog.setSterilzationBatchNumber(outputGoodDto.getLotAtt05());
                inlog.setOperateType(getOperateType(orderDto));
                inlog.setRelatedId(realateId);

                // 出库时间和入库时间规则统一
                LocalDateTime localDateTime = LocalDateTime.now();
                String format = localDateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
                DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                String format1 = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(expressInfo.getDeliveryTime()), ZoneId.systemDefault()));
                format1 = format1 + " " + format;
                inlog.setAddTime(DateUtil.convertLong(format1, DateUtil.TIME_FORMAT));
                inlog.setModTime(System.currentTimeMillis());
                inlog.setCompanyId(1);
                inlog.setIsEnable(1);
                inlog.setLastStockNum(0);
                inlog.setUpdater(2);
                inlog.setCreator(2);
                inlog.setLogType(1);
                inlog.setIsUse(2);
                inlog.setCreateTime(System.currentTimeMillis());
                inlog.setComments(orderDto.getSOReference1()+"/"+orderDto.getOrderNo());
                inlog.setTagSources(inlog.getWarehouseGoodsOperateLogVirtualId()+"");

                logger.info("直发专项发货SKU回传需关联VP单号：{}",outputGoodDto.getLotAtt07());
                inlog.setDedicatedBuyorderNo(outputGoodDto.getLotAtt07());

//                VDERP-6355出库单回调根据SN码新增出库记录
                if(outputGoodDto.getUserDefine3() != null && !outputGoodDto.getUserDefine3().equals("")){
                    int wmsOutNum = outputGoodDto.getQtyShipped().intValue();
                    String snArray[] = outputGoodDto.getUserDefine3().split(",");
                    //SN码数超过发货数量,抛出异常
                    if(snArray.length > wmsOutNum){
                        throw new Exception("出库错误 SN码数量大于实际出库数量，与实际情况不符，烦请重新确认后重新触发回传 出库单号:"+orderDto.getSOReference1()+"查询到贝登批次码"+outputGoodDto.getLotAtt11()+","+outputGoodDto.getLotAtt08()+","+outputGoodDto.getSKU());
                    }else {
                        //SN码数少于发货数量，则将剩余数量合为一条出库记录
                        if(snFlag == 0){
                            //确保只计算一次
                            if(snArray.length < wmsOutNum){
                                //按照SN码划分后，一个SN码一条出库记录
                                inlog.setNum(snArray.length - wmsOutNum);
                                //新增退货出库记录
                                int warehouseGoodsOperateLogVirtualId = warehouseGoodsOperateLogVirtualService.insertSelective(inlog);
                                //绑定出库记录和快递单的关系
                                warehouseGoodsOperateLogMapper.insertExpressWarehouse(warehouseGoodsOperateLogVirtualId,expressdetailId);
                            }
                        }
                        //根据sn码分割，新增出库记录，不足的部分直接合并到一起出库
                        nowMax += lastStockNum;//当前允许的sn码遍历的最大限制
                        for(;snFlag < snArray.length && snFlag < nowMax;snFlag++){
                            //防止重复循环
                            //按照SN码划分后，一个SN码一条出库记录
                            inlog.setNum(-1);
                            //SN码
                            inlog.setBarcodeFactory(snArray[snFlag]);
                            //新增退货出库记录
                            int warehouseGoodsOperateLogVirtualId = warehouseGoodsOperateLogVirtualService.insertSelective(inlog);
                            warehouseGoodsOperateLogMapper.insertExpressWarehouse(warehouseGoodsOperateLogVirtualId,expressdetailId);
                        }
                    }
                }else {
                    //直发记录
                    int warehouseGoodsOperateLogVirtualId = warehouseGoodsOperateLogVirtualService.insertSelective(inlog);
                    warehouseGoodsOperateLogMapper.insertExpressWarehouse(warehouseGoodsOperateLogVirtualId,expressdetailId);
                }
            }
        }
    }

    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.OUT_DIRECT_SALE_OUT.equals(requestBean.getOrderType())){
            throw new Exception("直发出库单:"+requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"的类型错误");
        }
        if(CollectionUtils.isEmpty(requestBean.getDetails())){
            throw new Exception("直发出库单:"+requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"的详情为空!");
        }
        if(StringUtil.isEmpty(requestBean.getSOReference1())){
            throw new Exception("WMS单号:"+requestBean.getOrderNo()+"的ERP单号为空!");
        }
        //本次出库数量
        Map<String,Integer> nowOutNumMap = new HashMap<>();
        //需要出库数量
        Map<String,Integer> needNumMap = new HashMap<>();
        for (OutputGoodDto detail : requestBean.getDetails()) {
            if(StringUtil.isEmpty(detail.getSKU())){
                throw new Exception("直发出库单:"+requestBean.getSOReference1()+"WMS单号:"+requestBean.getOrderNo()+"的详情为空!");
            }
            if(StringUtil.isEmpty(detail.getLotAtt08())){
                throw new Exception("直发出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"质量状态为空");
            }
            if(detail.getQtyShipped() == null){
                throw new Exception("直发出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"出库数量为空");
            }
            if(StringUtil.isBlank(detail.getUserDefine1())){
                throw new Exception("直发出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"详情ID为空");
            }
            if(StringUtil.isBlank(detail.getUserDefine4())){
                throw new Exception("直发出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"同行单批次号为空");
            }
            else{
                Integer saleorderGoodsId = Integer.valueOf(detail.getUserDefine1());
                SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);
                if(ErpConst.ZERO.equals(saleorderGoods.getDeliveryDirect())){
                    //直发出库回传--存在普发的sku时告警
                    String error = error(requestBean,detail.getSKU());
                    throw new Exception(error);
                }
            }
            //直发出库数量校验根据同行单批次维度进行汇总
            Integer outNum = nowOutNumMap.get(detail.getUserDefine4()) == null ? 0 : nowOutNumMap.get(detail.getUserDefine4());
            nowOutNumMap.put(detail.getUserDefine4(),outNum + detail.getQtyShipped().intValue());
            if(!needNumMap.containsKey(detail.getUserDefine4())){
                PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto = purchaseDeliveryBatchDetailService.queryInfoByDetailId(Integer.valueOf(detail.getUserDefine4()));
                needNumMap.put(detail.getUserDefine4(),purchaseDeliveryBatchDetailDto.getArrivalCount() - purchaseDeliveryBatchDetailDto.getWmsHandledDeliveryCount());
            }
        }
        logger.info("直发销售单出库回传商品信息{},ERP需出库商品信息{},",JSON.toJSONString(nowOutNumMap),JSON.toJSONString(needNumMap));
        for (String detailId : needNumMap.keySet()) {
            Integer needNum = needNumMap.get(detailId);
            Integer outNum = nowOutNumMap.get(detailId) == null ? 0 : nowOutNumMap.get(detailId);
            if(needNum < outNum){
                //回传出库数量超出需要出库数量
                String error = error(requestBean, detailId);
                throw new Exception(error);
            }
        }
    }

    private String error(OutputDto requestBean,String sku){
        String error = "直发出库单:"+ requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"明细"+sku+"出库数量超出可出库数量/直发商品WMS回调错误";
        sendMail("复核出库告警",error,WMS_CHECK_STOCK_ALARM_MAIL);
        return error;
    }

    /**
     * <AUTHOR>
     * @desc 发送告警邮件
     * @param title
     * @param message
     * @param mailTo
     */
    private void sendMail(String title,String message,String mailTo){
        try{
            String[] alarmMailReceiver  = {};
            if (StringUtil.isNotBlank(mailTo)){
                alarmMailReceiver = mailTo.split(",");
            }else{
                return;
            }
            simpleMailMessage.setTo(alarmMailReceiver) ;
            simpleMailMessage.setSubject(title);
            simpleMailMessage.setSentDate(new Date());
            simpleMailMessage.setText(message);
            javaMailSender.send(simpleMailMessage);
        }catch (Exception e){
            logger.error("MailUtil，发生异常：",e);
        }
    }
    @Override
    protected void updateOrderData(OutputDto requestBean) throws Exception {
        logger.info("直发无需根据出库回传记录更新销售单信息");
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.WAREHOUSE_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.SALEORDER_TYPE.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.valueOf(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return null;
    }

    /**
     * <AUTHOR>
     * @desc 出库回传后自定义处理
     * @param requestBean
     */
    @Override
    protected void customHandle(OutputDto requestBean) throws Exception {
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1()));
        saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
        //保存销售单和入库单关系
        dealOutorderAndInorderConcat(saleorder,requestBean);
        //保存出库单图片信息
        dealReportImage(saleorder,requestBean);
        //更新直发采购批次详情表出库已作业数量
        dealPurchaseDeliveryDirectBatchInfo(requestBean);

        //生成带价格和不带价格出库单
        generatePrintOutOrder(saleorder);
        //催办订单发送站内信
        sendMessage2web(requestBean);
    }

    private void dealOutorderAndInorderConcat(Saleorder saleorder, OutputDto requestBean) {
        try {
            Set<String> inOrderNoSet = requestBean.getDetails().stream().map(OutputGoodDto::getLotAtt10).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(inOrderNoSet)){
                return;
            }

            for (String inorderNO : inOrderNoSet) {
                if(StringUtil.isEmpty(inorderNO)){
                    continue;
                }
                String orderNo = WmsCommonUtil.getOriginalOrderNo(inorderNO);
                WmsOutInOrderConcat insertInfo = new WmsOutInOrderConcat();
                insertInfo.setOutOrderNo(saleorder.getSaleorderNo());
                insertInfo.setOutOrderId(saleorder.getSaleorderId());
                insertInfo.setOutOrderType(WmsOutInOrderConcatConstant.OUT_SALEORDER_TYPE);
                //VDERP-9480订单号变更start
                AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(orderNo);
                BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoByBuyorderNo(orderNo);
                if(afterSales != null){
                    if(afterSales == null){continue;}
                    insertInfo.setInOrderId(afterSales.getAfterSalesId());
                    insertInfo.setInOrderNo(afterSales.getAfterSalesNo());
                    insertInfo.setInOrderType(WmsOutInOrderConcatConstant.IN_AFTERORDER_TYPE);
                }else if(buyorderVo != null){
                    if(buyorderVo == null){continue;}
                    insertInfo.setInOrderId(buyorderVo.getBuyorderId());
                    insertInfo.setInOrderNo(buyorderVo.getBuyorderNo());
                    insertInfo.setInOrderType(WmsOutInOrderConcatConstant.IN_BUYORDER_TYPE);
                }
                if(insertInfo.getInOrderId() == null || insertInfo.getInOrderNo() == null || insertInfo.getInOrderType() == null){
                    continue;
                }
                WmsOutInOrderConcat oldinfo = wmsOutInOrderConcatMapper.getOutInOrderConcatInfo(insertInfo);
                if(oldinfo == null){
                    wmsOutInOrderConcatMapper.insertSelective(insertInfo);
                }
            }
        } catch (Exception e) {
            logger.error("dealOutorderAndInorderConcat error:",e);
        }
    }

    public void dealReportImage(Saleorder saleorder, OutputDto requestBean) {
        logger.info("处理出库单图片信息start orderNo:{},requestBean:{}", saleorder.getSaleorderNo(), JSON.toJSONString(requestBean));
        try {
            wmsImagesService.dealReportImage(requestBean, SysOptionConstant.QUALITY_REPORT_SALEORDER);
        }catch (Exception e){
            logger.error("WMS 推送质检报告下载error 销售单号:"+saleorder.getSaleorderNo(),e);
        }
    }

    /**
     * <AUTHOR>
     * @desc 更新直发采购单批次详情表出库作业数量
     * @param requestBean
     */
    private void dealPurchaseDeliveryDirectBatchInfo(OutputDto requestBean) throws Exception {
        for(OutputGoodDto detail : requestBean.getDetails()){
            PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto = purchaseDeliveryBatchDetailService.queryInfoByDetailId(Integer.parseInt(detail.getUserDefine4()));
            if(purchaseDeliveryBatchDetailDto.getWmsHandledDeliveryCount() + detail.getQtyShipped().intValue() > purchaseDeliveryBatchDetailDto.getArrivalCount()){
                //回传数量+已出库数量超出明细的数量--抛出异常
                throw new Exception("直发回传数量加已出库数量超出本次采购批次数量");
            }
            purchaseDeliveryBatchDetailService.updateOutNumById(Integer.parseInt(detail.getUserDefine4()),detail.getQtyShipped().intValue());
        }
    }

    /**
     * 发送站内信
     * @param requestBean
     */
    private void sendMessage2web(OutputDto requestBean) {
        try {
            //获取直发同行单明细id-查询对应整个同行单的信息
            List<Integer> deliveryDirectIds = new ArrayList<>();
            for (OutputGoodDto detail : requestBean.getDetails()) {
                Integer oneDirectDetailId = Integer.parseInt(detail.getUserDefine4());
                Integer oneDirectInfoId = purchaseDeliveryBatchDetailService.queryMainIdByDetailId(oneDirectDetailId);
                if (!deliveryDirectIds.contains(oneDirectInfoId)) {
                    deliveryDirectIds.add(oneDirectInfoId);
                }
            }
            Integer buyorderId = purchaseDeliveryBatchInfoService.queryByPrimarykey(deliveryDirectIds.get(0)).getBuyorderId();
            logger.info("本次回传涉及到的所有同行单的主表id{},采购单id{},", JSON.toJSONString(deliveryDirectIds), buyorderId);
            //遍历同行单-存在维护完成的同行单，则发送站内信
            //标识同行单是否完成
            boolean isComplete = true;
            for (Integer deliveryDirectId : deliveryDirectIds) {
                List<PurchaseDeliveryBatchDetailDto> purchaseDeliveryBatchDetailDtoList = purchaseDeliveryBatchDetailService.queryInfoByMainId(deliveryDirectId);
                //根据同行单的维度判断是否有完成的同行单
                boolean isCompleteNow = true;
                for (PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto : purchaseDeliveryBatchDetailDtoList) {
                    if (!purchaseDeliveryBatchDetailDto.getArrivalCount().equals(purchaseDeliveryBatchDetailDto.getWmsHandledDeliveryCount())) {
                        //wms出库回传数量是否等于明细收货数量--不相等则未全部完成
                        isCompleteNow = false;
                    }
                }
                isComplete = isCompleteNow;
                if (isCompleteNow) {
                    //存在一个同行单全部完成，即发送站内信
                    break;
                }
            }
            if (isComplete) {
                //发送站内信
                SendSystemMsgDto sendSystemMsgDto = buyorderMapper.getBuyorderInfoById(buyorderId);

                if (Objects.nonNull(sendSystemMsgDto) && ErpConst.ONE.equals(sendSystemMsgDto.getUrgedMaintainBatchInfo())) {
                    //站内信通知
                    List<Integer> userlist = new ArrayList<>();
                    userlist.add(sendSystemMsgDto.getOptUserId());

                    Map<String, String> map = new HashMap<>();
                    map.put("saleorderNo", sendSystemMsgDto.getSaleorderNo());

                    String url = "./orderstream/saleorder/detail.do?saleOrderId=" + sendSystemMsgDto.getSaleorderId();

                    MessageUtil.sendMessage(225, userlist, map, url, "njadmin");
                }
            }
        }catch (Exception e){
            logger.error("直发出库回传发送站内信失败",e);
        }
    }
}
