package com.vedeng.erp.kingdee.batch.processor;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.enums.InvoiceBelongTypeEnum;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceProcessDto;
import com.vedeng.erp.kingdee.batch.repository.BatchRollbackInvoiceDtoMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 回滚应付单处理器
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchRollbackPaymentProcessor implements ItemProcessor<BatchRollbackInvoiceDto, BatchRollbackInvoiceProcessDto> {

    @Resource
    private BatchRollbackInvoiceDtoMapper batchRollbackInvoiceDtoMapper;

    @Override
    public BatchRollbackInvoiceProcessDto process(BatchRollbackInvoiceDto batchRollbackInvoiceDto) throws Exception {
        log.info("batchRollbackPaymentProcessorService batchRollbackInvoiceDto:{}", JSON.toJSONString(batchRollbackInvoiceDto));

        String formId = KingDeeFormConstant.PAY_EXPENSES;
        String payCommonIdByInvoiceFid = "";

        if (InvoiceBelongTypeEnum.BUYORDER_ORDER.getCode().equals(batchRollbackInvoiceDto.getType())) {
            payCommonIdByInvoiceFid = batchRollbackInvoiceDtoMapper.getPayCommonIdByInvoiceFid(batchRollbackInvoiceDto.getInvoiceId().toString());
        } else if (InvoiceBelongTypeEnum.BUY_ORDER_EXPENSE.getCode().equals(batchRollbackInvoiceDto.getType())) {
            payCommonIdByInvoiceFid = batchRollbackInvoiceDtoMapper.getExpensePayCommonIdByInvoiceFid(batchRollbackInvoiceDto.getInvoiceId().toString());
        }

        if (StringUtils.isBlank(payCommonIdByInvoiceFid)) {
            log.warn("getPayCommonIdByInvoiceFid warn batchRollbackInvoiceDto:{}", JSON.toJSONString(batchRollbackInvoiceDto));
            throw new RuntimeException("回滚应付单处理器应付单内码不存在");
        }

        log.info("回滚应付单处理器 invoiceId:{},formId:{},payCommonIdByInvoiceFid:{}", batchRollbackInvoiceDto.getInvoiceId(), formId, payCommonIdByInvoiceFid);

        return BatchRollbackInvoiceProcessDto.builder().invoiceId(batchRollbackInvoiceDto.getInvoiceId()).ids(payCommonIdByInvoiceFid)
                .formId(formId).orgId(KingDeeConstant.ORG_ID.toString()).isPay(Boolean.TRUE)
                .build();

    }
}
