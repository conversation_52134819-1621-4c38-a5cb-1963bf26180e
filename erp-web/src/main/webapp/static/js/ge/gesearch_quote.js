//点击搜索
function gesearch() {
    var url =  '/businesschance/ge/gesearchquote.do'

    $.ajax({
        type: 'POST',
        url: url,
        data: $("#search").serialize(),
        dataType : "html",
        success: function(result) {
            var htmlCont = result;
            layer.closeAll();
            var open = layer.open({
                type: 1,
                title: '报价单号',
                shadeClose: false,
                area : ['1000px', '400px'],
                content: htmlCont,
                success: function(layer, index){

                }
            })
        }
    });
}

//取消关闭窗口
function closeLayer(){
    layer.closeAll();
}
//确定选择
function confirmSelect(){
    var isCanCreate = $("#isCanCreate").val();
    if(isCanCreate == 1){
        var choose = $('input[name="goodsnum"]:checked').val();
        var sku = $('#sku_'+choose).val();
        var goodsId = $('#goodsId_'+choose).val();
        var goodsName = $('#goodsName_'+choose).val();
        var traderName = $("#vdtraderName").val();
        var traderId = $("#vdtraderId").val();
        var salesArea = $("#vdsalesArea").val();
        var salesAreaId = $("#vdsalesAreaId").val();
        var terminalTraderName = $("#vdterminalTraderName").val();
        var terminalTraderId = $("#vdterminalTraderId").val();
        var quoteorderId = $("#vdquoteorderId").val();
        var quoteorderNo = $("#vdquoteorderNo").val();
        $("#sku").val(sku);
        $("#goodsId").val(goodsId);
        $("#goodsName").val(goodsName);
        $("#traderName").val(traderName);
        $("#traderId").val(traderId);
        $("#salesArea").val(salesArea);
        $("#salesAreaId").val(salesAreaId);
        $("#terminalTraderName").val(terminalTraderName);
        $("#terminalTraderId").val(terminalTraderId);
        $("#quoteorderId").val(quoteorderId);
        $("#quoteorderNo").val(quoteorderNo);
        //设置省市区联动选择
        if(salesAreaId != '' && salesAreaId != 0){

            var province = salesAreaId.substr(0,2) + '0000';
            $("#province").val(province);

            var city = salesAreaId.substr(0,4) + '00';
            var zone = salesAreaId;
            $("#city").data('city', city);
            $("#zone").data('zone', zone);

            $("#province").trigger("change");
        }else {
            $("#province").val(0);
            $("#province").trigger("change");
        }
        layer.closeAll();
    }
}


