package com.vedeng.trader.dao;
import org.apache.ibatis.annotations.Param;

import com.vedeng.trader.model.TraderCustomerCategory;

import java.util.List;

public interface TraderCustomerCategoryMapper {
    List<TraderCustomerCategory> getYxgCategory();

    /**
     * 获取全部客户类型分类
     *
     * @return List<TraderCustomerCategory>
     */
    List<TraderCustomerCategory> getByAll();

    /**
     * 根据id获取客户分类类型
     *
     * @param traderCustomerCategoryId 分类类型id
     * @return TraderCustomerCategory
     */
    TraderCustomerCategory selectAllByTraderCustomerCategoryId(@Param("traderCustomerCategoryId")Integer traderCustomerCategoryId);


}