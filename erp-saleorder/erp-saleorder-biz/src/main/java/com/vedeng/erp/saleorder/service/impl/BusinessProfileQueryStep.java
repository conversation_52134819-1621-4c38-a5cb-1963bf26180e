package com.vedeng.erp.saleorder.service.impl;

import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.saleorder.model.dto.BaseDataInfoDto;
import com.vedeng.erp.saleorder.model.dto.ext.BusinessProfileDataInfo;
import com.vedeng.erp.saleorder.service.WorkbenchQueryStep;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.trader.dao.CommunicateRecordMapper;
import com.vedeng.trader.model.CommunicateRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 业务概况查询模块
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/11/29 14:39.
 * @author: Randy<PERSON><PERSON>.
 */

@Slf4j
@Service
public class BusinessProfileQueryStep implements WorkbenchQueryStep {

    @Autowired
    SaleorderMapper saleorderMapper;

    @Autowired
    CommunicateRecordMapper communicateRecordMapper;


    @Override
    public BaseDataInfoDto doQuery(SaleorderUserInfoDto userInfoDto) {
        BusinessProfileDataInfo businessProfileDataInfo = new BusinessProfileDataInfo();

        Date today=new Date();
        long todayStartTime = DateUtil.getDayStartTime(today).getTime();
        long todayEndTime = DateUtil.getDayEndTime(today).getTime();
        Date yesterday = DateUtil.getPreviousDayByDateTime(new Date());
        long yesterdayStartTime = DateUtil.getDayStartTime(yesterday).getTime();
        long yesterdayEndTime = DateUtil.getDayEndTime(yesterday).getTime();

        if(CollectionUtils.isNotEmpty(userInfoDto.getSubUserIdList())){

            BigDecimal yesterdayAmount = calculatePayAmount(userInfoDto.getSubUserIdList(), yesterdayStartTime, yesterdayEndTime);
            BigDecimal todayAmount = calculatePayAmount(userInfoDto.getSubUserIdList(),todayStartTime,todayEndTime);


            if(Objects.nonNull(todayAmount)){
                businessProfileDataInfo.setTodayAmount(todayAmount);
            } else {
                businessProfileDataInfo.setTodayAmount(new BigDecimal(0));
            }
            if(Objects.nonNull(yesterdayAmount)){
                businessProfileDataInfo.setYesterdayAmount(yesterdayAmount);
            } else {
                businessProfileDataInfo.setYesterdayAmount(new BigDecimal(0));
            }
        }
        CommunicateRecord communicateQuery = new CommunicateRecord();
        communicateQuery.setUserIds(userInfoDto.getSubUserIdList());
        communicateQuery.setBegintime(todayStartTime);
        communicateQuery.setEndtime(todayEndTime);

        Integer communicationNum = communicateRecordMapper.getCommunicateNum(communicateQuery);
        if(communicationNum != null){
            businessProfileDataInfo.setCallNum(communicationNum);
        }else {
            businessProfileDataInfo.setCallNum(0);
        }
        Long time = communicateRecordMapper.getCommunicateTotalTime(communicateQuery);
        if(time != null){
            time = time/1000/60;
            businessProfileDataInfo.setCallTime(time);
        } else {
            businessProfileDataInfo.setCallTime(0);
        }

        return businessProfileDataInfo;
    }

    private BigDecimal calculatePayAmount(List<Integer> subUserIdList, long starttime, long endtime) {
        BigDecimal receiveAmout = saleorderMapper.getReceivedAmountByTime( subUserIdList,  starttime,  endtime);
        BigDecimal returnAmount = saleorderMapper.getReturnAmountByTime( subUserIdList,  starttime,  endtime);
        return receiveAmout.subtract(returnAmount);

    }
}
