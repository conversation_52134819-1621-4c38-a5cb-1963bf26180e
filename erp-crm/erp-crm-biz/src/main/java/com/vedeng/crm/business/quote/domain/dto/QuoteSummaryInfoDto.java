package com.vedeng.crm.business.quote.domain.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 报价单汇总信息
 */
@Data
public class QuoteSummaryInfoDto {

    /**
     * 是否生效 0否 1是
     */
    private Integer validStatus;

    /**
     * 是否有需求 0否 1是
     */
    private Integer requirementStatus;

    /**
     * 客户需求产品数
     */
    private Integer requiredProductNumber;

    /**
     * 客户需求满足率
     */
    private String requiredSatisfactionRate;

    /**
     * 报价产品数量
     */
    private Integer quoteProductNumber;

    /**
     * 独家产品数量
     */
    private Integer exclusiveProductNumber;

    /**
     * 需要报备数量
     */
    private Integer needReportedNumber;

    /**
     * 已核价产品数量
     */
    private Integer auditedProductNumber;

    /**
     * 全部预计货期
     */
    private String estimatedDeliveryTime;

    /**
     * 报价总金额（元）
     */
    private BigDecimal totalQuotedAmount;

    /**
     * 授权书
     */
    private QuoteApplyDto quoteApplyDto;
}
