package com.wms.dto;

import java.math.BigDecimal;

public class ExgOrderGoodDto {

    private String SKU;

    //商品数量
    private BigDecimal Qty;

    //质量状态
    private String LotAtt08;

    //出库下传(产品专项非专项区分)
    private String LotAtt07;

    //ERP订单商品id
    private String D_EDI_07;

    public String getD_EDI_07() {
        return D_EDI_07;
    }

    public void setD_EDI_07(String d_EDI_07) {
        D_EDI_07 = d_EDI_07;
    }

    public String getSKU() {
        return SKU;
    }

    public void setSKU(String SKU) {
        this.SKU = SKU;
    }

    public BigDecimal getQty() {
        return Qty;
    }

    public void setQty(BigDecimal qty) {
        Qty = qty;
    }

    public String getLotAtt08() {
        return LotAtt08;
    }

    public void setLotAtt08(String lotAtt08) {
        LotAtt08 = lotAtt08;
    }

    public String getLotAtt07() {
        return LotAtt07;
    }

    public void setLotAtt07(String lotAtt07) {
        LotAtt07 = lotAtt07;
    }
}
