package com.vedeng.erp.kingdee.task.batch;


import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.GenerateDirectPurchaseOutAndInBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 生成直发采购出库单和入库单
 * @date 2023/5/10 14:30
 */
@JobHandler(value = "GenerateDirectPurchaseOutAndInBatchTask")
@Component
public class GenerateDirectPurchaseOutAndInBatchTask extends AbstractJobHandler {

    @Autowired
    private GenerateDirectPurchaseOutAndInBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("====================生成直发采购出库单和入库单开始==================");
        Job job = batchJob.generateDirectPurchaseOutAndInFlowJob();
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("====================生成直发采购出库单和入库单结束==================");
        return SUCCESS;
    }
}
