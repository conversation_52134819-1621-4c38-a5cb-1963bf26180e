package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchRBuyorderExpenseJSaleorderDto;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/3/21 10:01
 **/
public interface BatchRBuyorderExpenseJSaleorderDtoMapper {
    int deleteByPrimaryKey(Integer tRBuyorderExpenseJSaleorderId);

    int insert(BatchRBuyorderExpenseJSaleorderDto record);

    int insertSelective(BatchRBuyorderExpenseJSaleorderDto record);

    BatchRBuyorderExpenseJSaleorderDto selectByPrimaryKey(Integer tRBuyorderExpenseJSaleorderId);

    int updateByPrimaryKeySelective(BatchRBuyorderExpenseJSaleorderDto record);

    int updateByPrimaryKey(BatchRBuyorderExpenseJSaleorderDto record);

    /**
     * 根据费用单id查询 根据销售单加费用明细id聚合
     * @param buyorderExpenseId 费用单id
     * @return
     */
    List<BatchRBuyorderExpenseJSaleorderDto> findByBuyorderExpenseId(@Param("buyorderExpenseId")Integer buyorderExpenseId);


}