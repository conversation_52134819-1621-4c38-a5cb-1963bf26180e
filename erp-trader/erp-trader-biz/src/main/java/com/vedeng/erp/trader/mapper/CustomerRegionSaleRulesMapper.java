package com.vedeng.erp.trader.mapper;


import com.vedeng.erp.trader.domain.CustomerRegionSaleRules;
import com.vedeng.erp.trader.dto.CustomerRegionSaleRulesApiDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerRegionSaleRulesMapper {
    int deleteByPrimaryKey(Integer CustomerRegionSaleRulesId);

    int insert(CustomerRegionSaleRules record);

    int insertSelective(CustomerRegionSaleRules record);

    List<Integer> selectRegionIdExist(CustomerRegionSaleRulesApiDto customerRegionSaleRulesApiDto);

    int updateByRegionIds(CustomerRegionSaleRulesApiDto customerRegionSaleRulesApiDto);

    CustomerRegionSaleRules selectByPrimaryKey(Integer CustomerRegionSaleRulesId);

    int updateByPrimaryKeySelective(CustomerRegionSaleRules record);

    int updateByPrimaryKey(CustomerRegionSaleRules record);

    int batchDeleteByPrimaryKey(@Param("list") Integer[] idList);

    CustomerRegionSaleRules selectByRegionId(Integer regionId);

    int batchAddPublicCustomerCalculateRules(@Param("list") List<CustomerRegionSaleRules> list);


}