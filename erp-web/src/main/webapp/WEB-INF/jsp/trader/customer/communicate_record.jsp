<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="沟通记录" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<script type="text/javascript">
    // 头部滚动
    $(function () {
        var thead = $('.table thead');
        var tbodyTr = $('.table tbody tr');
        var movingTop, setWid, setLastLiWid, replaceThead;
        var theadThs = thead.find('th');
        var thLeng = theadThs.length;
        var tbodyTrLeng = tbodyTr.length;
        // 创建DOM
        var replaceTheadDiv = $('<div class="replaceThead"><ul style="margin-bottom:0;"></ul></div>');
        var whiteSpace = $('<div class="white-space"></div>');

        var fixdiv = $('.fixdiv');
        var superdiv = $('.superdiv');
        var fixdivPosTop = $('.customer').outerHeight() + $('.title-container').outerHeight();
        //创建滑动表头里面的元素；公用
        // 1
        function createReplaceTheadLi() {
            for (var i = 0; i < thLeng; i++) {
                var liHtml = theadThs.eq(i).text();
                setWid = theadThs.eq(i).outerWidth();
                $('.replaceThead ul').append('<li style="width:' + setWid + 'px;">' + liHtml + '</li>');
            }
            setLastLiWid = $('.replaceThead ul li:last-child').width() - 1;
            $('.replaceThead ul li:last-child').width(setLastLiWid);
            replaceThead = $('.replaceThead');
        }

        // 2
        // movingHeadSpecial：有横向滚动条的表格使用，表头跟着滑动；
        function movingHeadSpecial() {
            var superdivWid0 = superdiv.width();
            var superdivWid1 = superdiv.width() + 21;
            var windowHeight = $(window).height();
            var fixdivPosTo, setFixHeit0, setFixHeit1, nextSiblingHeit, windowTop, fixdivScrollLeft;
            var nextSibling = $(fixdiv).next();
            var fixHeight = fixdiv.outerHeight() + 20;
            var listenNum0 = [0],
                listenNum1 = 0;
            var tableWidth = superdiv.find('table').outerWidth() + 11; //
            fixdivPosTop = $('.customer').outerHeight() + $('.title-container').outerHeight();
            // 控制superdiv的宽度；
            // 这里判断页面是否有分页，控制住表格的高度
            if (nextSibling.html() != undefined) {
                nextSiblingHeit = nextSibling.outerHeight();
                setFixHeit0 = windowHeight - nextSiblingHeit;
                if ($(fixdiv).hasClass('fixdivfix')) {
                    $(fixdiv).css({'height': setFixHeit0 + 'px', 'top': '0px'});
                }
            } else {
                setFixHeit0 = windowHeight;
            }

            $(window).scroll(function () {
                windowTop = $(window).scrollTop();
                if (windowTop > fixdivPosTop && ($(fixdiv).outerHeight() > ($(window).height() - nextSiblingHeit))) {
                    if (nextSibling.html() != undefined) {
                        nextSibling.addClass('fixPage');
                        nextSibling.hasClass('pages_container') ? (nextSiblingHeit = nextSibling.outerHeight() + 19) : (nextSiblingHeit = nextSibling.outerHeight() + 10);
                        setFixHeit0 = $(window).height() - nextSiblingHeit;
                    } else {
                        setFixHeit0 = windowHeight;
                    }
                    $(fixdiv).addClass('fixdivfix').css({'height': setFixHeit0 + 'px', 'top': '0px'});
                    superdiv.addClass('pt35').width(tableWidth).prepend(whiteSpace).prepend(replaceTheadDiv);
                    createReplaceTheadLi();
                    $(fixdiv).scrollTop(30);
                    $(fixdiv).scroll(function () {
                        var windowHeight = $(window).height();
                        fixdivScrollTop = $(this).scrollTop();
                        fixdivScrollLeft = $(this).scrollLeft();
                        if (listenNum1 < 2) {
                            listenNum0.push(fixdivScrollTop);
                            listenNum1++;
                        }
                        if (fixdivScrollTop > 0) {
                            replaceThead.show().addClass('replaceTheadMody').css('top', fixdivScrollTop + 'px');
                            $('.white-space').show();
                            superdiv.css({'padding-right': '10px', 'width': superdivWid1 + 'px'});
                        }
                        if (fixdivScrollTop < 20) {
                            $('.replaceThead ul').empty();
                            $('.superdiv').removeClass('pt35');
                            // nextSibling.removeClass('fixPage').addClass('list-bottom');
                            nextSibling.removeClass('fixPage');
                            replaceThead.hide();
                            $(fixdiv).removeClass('fixdivfix').css({'height': fixHeight + 'px'});
                            $(window).scrollTop(fixdivPosTop);
                            $('.white-space').hide();
                            superdiv.css({'padding-right': '0px', 'width': superdivWid0 + 'px'});
                        }
                    })
                }
            })
            if ($(window.parent.document).find('.tab-pane').hasClass('active')) {
                $('.replaceThead').hide();
            }
        }

        movingHeadSpecial();
        $(window).resize(function () {
            $('.replaceThead ul').empty();
            movingHeadSpecial();
        })
    })

    // 修正沟通记录弹窗
    function modifySummary(id) {
        layer.open({
            type: 2,
            content: '/communicateSummary/modify/dialog.do?communicateSummaryId=' + id,
            area: ['80%', '95%'],
            title :'沟通内容摘要'
        });
    }

</script>
<%@ include file="customer_tag.jsp" %>

<style>
    .container {
        text-align: left;
        max-height: 200px;
    }
    .multiline-ellipsis {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 8; /* 限制显示的行数 */
    }
    .tag {
        background-color: #ecf5ff;
        border-color: #d9ecff;
        display: inline-block;
        height: 32px;
        padding: 0 10px;
        line-height: 30px;
        font-size: 12px;
        color: #409EFF;
        border-width: 1px;
        border-style: solid;
        border-radius: 4px;
        box-sizing: border-box;
        white-space: nowrap;
        height: 20px;
        padding: 0 5px;
        line-height: 19px;
        white-space: normal;
        height: auto;
    }
    .table {
        width: 100%;
        table-layout: fixed;
    }
    /* 固定"操作"列 */
    td:last-child {
        position: sticky;
        right: 0;
        /*background-color: #ffffff; *//* 可选，背景颜色，使列在右侧固定时更清晰 */
        z-index: 2; /* 确保列位于上方 */
    }

    /* 固定"操作"列 */
    th:last-child {       position: sticky;
        right: 0;
        background-color: #E5E5E5; /* 可选，背景颜色，使列在右侧固定时更清晰 */
        z-index: 2; /* 确保列位于上方 */
    }
    .avatar-item-wrap{
		display: flex;
		justify-content: center;
		align-items: center;
	}
	.avatar-img {
	  width: 20px;
	  height: 20px;
	  display: flex;
	  align-items: center;
	  justify-content: center;
	  border-radius: 2px;
	  overflow: hidden;
	  margin-right: 5px;
	  position: relative;
	}
</style>

<div class="content parts">
    <div class="title-container">
        <div class="table-title nobor">沟通记录(座席/企微语音通话从通话中心/企微获取，有一定延时)</div>
<%--        <div class="title-click nobor  pop-new-data"--%>
<%--             layerParams='{"width":"800px","height":"500px","title":"通过沟通记录，你还希望提取其他什么信息？","link":"/traderCommunicateFeedback/index.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}"}'>--%>
<%--            沟通记录摘要意见反馈--%>
<%--        </div>--%>
        <div class="title-click nobor">
            <c:if test="${customerInfoByTraderCustomer.isEnable == 1  && ((customerInfoByTraderCustomer.verifyStatus != null && customerInfoByTraderCustomer.verifyStatus != 0 )|| customerInfoByTraderCustomer.verifyStatus == null)}">
                <a href="${pageContext.request.contextPath}/trader/customer/addcommunicate.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}">新增</a>
            </c:if>
        </div>
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered"
           style="max-width: 100%;overflow-x: auto;">
        <thead>
        <tr>
            <th class="table-smallest wid20">时间</th>
            <th class="wid12">单号</th>
            <th class="wid8">录音ID</th>
            <%--<th class="wid7">录音内容</th>--%>
            <th class="wid12">联系人</th>
            <th class="wid10">联系方式</th>
            <th class="wid10">沟通方式</th>
            <th class="wid10">沟通目的</th>
            <th class="linebreak table-smaller wid25">沟通内容（AI分析整理）</th>
<%--            <th class="linebreak table-smaller wid25">沟通内容摘要（AI分析整理）</th>--%>
            <th class="wid10">操作人</th>
            <th class="wid10">下次联系日期</th>
            <th class="linebreak table-smallest wid10">下次沟通内容</th>
            <th class="linebreak table-smallest wid10">备注</th>
            <th class="wid15">创建时间</th>
            <th class="linebreak">操作</th>
        </tr>
        </thead>
        <tbody>
        <c:if test="${not empty communicateRecordList}">
            <c:forEach items="${communicateRecordList }"
                       var="communicateRecord">
                <tr>
                    <td><date:date value="${communicateRecord.begintime} "/>~<date:date value="${communicateRecord.endtime}" format="HH:mm:ss"/></td>
                    <td>
                        <c:choose>
                            <c:when test="${communicateRecord.communicateType == 244 }">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewbussiness${communicateRecord.relatedId}",
										"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${communicateRecord.relatedId}&traderId=${communicateRecord.traderId }&traderCustomerId=${communicateRecord.traderCustomerId }",
										"title":"销售商机详情"}'>${communicateRecord.bussinessChanceNo }</a>
                            </c:when>
                            <c:when test="${communicateRecord.communicateType == 245 }">
                                <a class="addtitle"
                                   tabtitle="{&quot;num&quot;:&quot;viewQuote${communicateRecord.relatedId}&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${communicateRecord.relatedId}&viewType=3&quot;,&quot;title&quot;:&quot;报价详情&quot;}">${communicateRecord.quoteorderNo}</a>
                            </c:when>
                            <c:when test="${communicateRecord.communicateType == 246 }">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewsaleorder${communicateRecord.relatedId}","link":"./order/saleorder/view.do?saleorderId=${communicateRecord.relatedId}","title":"订单信息"}'>${communicateRecord.saleorderNo}</a>
                            </c:when>
                            <c:when test="${communicateRecord.communicateType == 247 }">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/buyorder/viewBuyordersh.do?buyorderId=${communicateRecord.relatedId}","title":"订单信息"}'>${communicateRecord.buyorderNo}</a>
                            </c:when>
                            <c:when test="${communicateRecord.communicateType == 248 }">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewaftersales<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./aftersales/order/viewAfterSalesDetail.do?afterSalesId=${communicateRecord.relatedId}&traderType=1","title":"售后"}'>${communicateRecord.aftersalesNo}</a>
                            </c:when>
                            <c:when test="${communicateRecord.communicateType == 4083 }">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewBusinessClues<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./business/clues/viewBusinessClues.do?businessCluesId=${communicateRecord.relatedId}","title":"线索详情"}'>${communicateRecord.businessCluesNo}</a>
                            </c:when>
                            <c:otherwise></c:otherwise>
                        </c:choose>
                    </td>
                    <td><c:if test="${not empty communicateRecord.coidUri}"> ${communicateRecord.communicateRecordId } </c:if>
                    </td>
                    <%--<td><c:if test="${not empty communicateRecord.coidUri}">
                        <c:if test="${communicateRecord.isTranslation eq 1}">
									  <span class="edit-user pop-new-data"
                                            layerParams='{"width":"90%","height":"90%","title":"查看详情","link":"${pageContext.request.contextPath}/phoneticTranscription/phonetic/viewContent.do?communicateRecordId=${communicateRecord.communicateRecordId}"}'>查看</span>
                        </c:if>
                        <span class="edit-user"
                              onclick="playrecord('${communicateRecord.coidUri}');">播放</span>
                    </c:if></td>--%>
                    <td>
                        <%--${communicateRecord.contactName}--%>
                        <div class="avatar-item-wrap" style="display: flex;align-items:flex-start;">
									<c:choose>
										<c:when test="${communicateRecord.coidType == 3 or communicateRecord.coidType==4}">
											<div class="avatar-img">
												<c:choose>
													<c:when test="${not empty communicateRecord.avatarUrl}">
														<img style="width: 100%;  height: auto;"  src="${communicateRecord.avatarUrl}">
													</c:when>
													<c:otherwise>
														<img src="https://lxcrm.vedeng.com/static/image/crm-user-avatar.svg">
													</c:otherwise>
												</c:choose>

											</div>
											<div title="${communicateRecord.contactName}" style="flex: 1">${communicateRecord.contactName}</div>
										</c:when>
										<c:otherwise>
											<div title="${communicateRecord.contactName}" style="flex: 1">${communicateRecord.contactName}</div>
										</c:otherwise>
									</c:choose>
								</div>


                    </td>
                    <td>${communicateRecord.phone}</td>
                    <td>
                        <c:choose>
                            <c:when test="${communicateRecord.coidType == 2}">
                            座席-呼出
                            </c:when>
                            <c:when test="${communicateRecord.coidType == 3}">
                            企微-呼出
                            </c:when>
                            <c:when test="${communicateRecord.coidType == 4}">
                            <span>企微-呼入</span>
                            </c:when>
                            <c:when test="${communicateRecord.coidType == 1}">
                            <span>座席-呼入</span>
                            </c:when>
                            <c:otherwise>

                            </c:otherwise>
                        </c:choose>
                    </td>
                    <td>${communicateRecord.communicateGoalName}</td>
                    <td class="linebreak ">
                        <ul class="communicatecontent ml0">
                            <c:if test="${not empty communicateRecord.tag }">
                                <c:forEach items="${communicateRecord.tag }" var="tag">
                                    <li class="bluetag" title="${tag.tagName}">${tag.tagName}</li>
                                </c:forEach>
                            </c:if>
                        </ul>
                        <div style="float: left">${communicateRecord.contentSuffix}</div>
                    </td>
<%--                    <td>--%>
<%--                        <c:if test="${communicateRecord.communicateAiSummaryApiDto.communicateSummaryId != null}">--%>
<%--                        <div class="container multiline-ellipsis" title="--%>
<%--客户意图: ${communicateRecord.communicateAiSummaryApiDto.customerIntentions}--%>
<%--意向商品: ${communicateRecord.communicateAiSummaryApiDto.intentionGoods}--%>
<%--品牌: ${communicateRecord.communicateAiSummaryApiDto.brands}--%>
<%--型号: ${communicateRecord.communicateAiSummaryApiDto.models}--%>
<%--客户类型: ${communicateRecord.communicateAiSummaryApiDto.customerTypes}--%>
<%--是否有意向: ${communicateRecord.communicateAiSummaryApiDto.isIntention}--%>
<%--是否加微信: ${communicateRecord.communicateAiSummaryApiDto.isAddWechat}--%>
<%--                                        ">--%>
<%--                            <span class="tag">客户意图:</span> ${communicateRecord.communicateAiSummaryApiDto.customerIntentions}<br/>--%>
<%--                            <span class="tag">意向商品:</span> ${communicateRecord.communicateAiSummaryApiDto.intentionGoods}<br/>--%>
<%--                            <span class="tag">品牌:</span> ${communicateRecord.communicateAiSummaryApiDto.brands}<br/>--%>
<%--                            <span class="tag">型号:</span> ${communicateRecord.communicateAiSummaryApiDto.models}<br/>--%>
<%--                            <span class="tag">客户类型:</span> ${communicateRecord.communicateAiSummaryApiDto.customerTypes}<br/>--%>
<%--                            <span class="tag">是否有意向:</span> ${communicateRecord.communicateAiSummaryApiDto.isIntention}<br/>--%>
<%--                            <span class="tag">是否加微信:</span> ${communicateRecord.communicateAiSummaryApiDto.isAddWechat}<br/>--%>
<%--                         </div>--%>
<%--                        </c:if>--%>
<%--                    </td>--%>
                    <td>${communicateRecord.user.username}</td>
                    <c:choose>
                        <c:when test="${communicateRecord.isDone == 0 }">
                            <td class="font-red">${communicateRecord.nextContactDate }</td>
                        </c:when>
                        <c:otherwise>
                            <td>${communicateRecord.nextContactDate }</td>
                        </c:otherwise>
                    </c:choose>
                    <td>${communicateRecord.nextContactContent }</td>
                    <td>${communicateRecord.comments}</td>
                    <td><date:date value="${communicateRecord.addTime} "/></td>
                    <td class="caozuo">
                        <c:if test="${not empty communicateRecord.coidUri}">
                               <span class="caozuo-blue"
                                     onclick="javascript:window.top.showAiHelpme(${communicateRecord.communicateRecordId});">
												查看
										</span>
                        </c:if>

                                 <c:if test="${customerInfoByTraderCustomer.isEnable == 1  && ((customerInfoByTraderCustomer.verifyStatus != null && customerInfoByTraderCustomer.verifyStatus != 0 )|| customerInfoByTraderCustomer.verifyStatus == null)}">
										<span class="caozuo-blue"
                                              onclick="goUrl('${pageContext.request.contextPath}/trader/customer/editcommunicate.do?communicateRecordId=${communicateRecord.communicateRecordId}&traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}')">
												编辑
                                        </span>
                                </c:if>


<%--                        <c:if test="${communicateRecord.communicateAiSummaryApiDto.communicateSummaryId != null}">--%>
<%--                           <span class="caozuo-blue"--%>
<%--                              onclick="modifySummary(${communicateRecord.communicateAiSummaryApiDto.communicateSummaryId})">--%>
<%--                              修正沟通摘要--%>
<%--                            </span>--%>
<%--                        </c:if>--%>
                    </td>
                </tr>
            </c:forEach>
        </c:if>
        <c:if test="${empty communicateRecordList }">
            <!-- 查询无结果弹出 -->
            <td class="tcenter" colspan="16">查询无结果！</td>
        </c:if>
        </tbody>

    </table>

    <tags:page page="${page}"/>
</div>
<%@ include file="../../common/footer.jsp" %>
