<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>
<c:set var="path" value="<%=basePath%>" scope="application" />
<link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
<link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />

<style>
    .radio-item {
        display: block;
        margin-bottom: 15px;
        margin-inline: 25px;
        margin-top: 15px;
    }
    .el-descriptions-item__label {
        display: none;
    }
</style>
<div id="app">

    <template v-for="item in traderCustomerTagChangeRecordList" >
        <el-descriptions title="" border :column="1" >
            <el-descriptions-item label>
                <template #label></template>
                <template>
                    {{parseTime(item[0].operTime)}}&nbsp;&nbsp;&nbsp;&nbsp;
                    <span style="color: #17a0a0"  v-if="item[0].source==0">用户</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    <span style="color: #ff6702" v-if="item[0].source==1">系统</span>&nbsp;&nbsp;&nbsp;&nbsp;
                    <template >更新标签</template>
                </template>
            </el-descriptions-item>

            <el-descriptions-item label>
                <template #label></template>
                <template v-for="i in item">
                    {{i.tagModelName+' '+i.tagChangeLog}}<br>

                </template>
            </el-descriptions-item>
        </el-descriptions>
    </template>

    <template v-if="traderCustomerTagChangeRecordList.length == 0" >
        <el-descriptions title="" border :column="1" >
            <el-descriptions-item label>
                <template #label></template>
                <template>
                    暂无标签更新记录。
                </template>
            </el-descriptions-item>
        </el-descriptions>
    </template>

</div>

<script src="${pageContext.request.contextPath}/static/api/trader/traderCustomerTagChangeRecord.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">
    const traderCustomerId = '${traderCustomerId}';
    const sendThis = (_this) => {
        vm = _this;
    }
    new Vue({
        el: '#app',
        data() {
            return {
                radio:'',
                traderCustomerTagChangeRecordList: []

            }
        },

        mounted() {
            loadingApp()
        },

        created() {
            sendThis(this);
            let that = this;
            queryByTraderCustomerId({"traderCustomerId": traderCustomerId}).then(res => {
                that.traderCustomerTagChangeRecordList = res.data.data;
            })
        },

        methods: {
            scrollTo(ref){
                let top = this.$refs[ref].$el.offsetTop;
                window.scrollTo(0, top);
            }
        }
    })
</script>
