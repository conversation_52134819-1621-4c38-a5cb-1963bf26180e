package com.vedeng.kpi.service.impl;

import com.vedeng.kpi.cache.KpiLoadingCache;
import com.vedeng.kpi.model.DO.KpiOrderLogDo;
import com.vedeng.kpi.model.DTO.KpiDataQueryDto;
import com.vedeng.kpi.service.KpiParamTransService;
import com.vedeng.kpi.share.KpiUtils;
import com.vedeng.kpi.dao.KpiOrderLogMapper;
import com.vedeng.kpi.model.DTO.KpiUserInfoDto;
import com.vedeng.kpi.service.KpiOrderLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Service("KpiOrderLogService")
public class KpiOrderLogServiceImpl implements KpiOrderLogService {

    private static final Logger log = LoggerFactory.getLogger("kpilog");

    @Resource
    private KpiOrderLogMapper kpiOrderLogMapper;

    @Autowired
    private KpiParamTransService kpiParamTransService;

    @Autowired
    @Qualifier("kpiLoadingCache")
    private KpiLoadingCache kpiLoadingCache;


    public Integer insertSelective(KpiOrderLogDo kpiOrderLog){
        return kpiOrderLogMapper.insertSelective(kpiOrderLog);
    }

    @Override
    public List<KpiDataQueryDto> getKpiLogDetail(KpiDataQueryDto query) {
        return kpiOrderLogMapper.getKpiDetail(query);
    }

    @Override
    public List<KpiOrderLogDo> getUserToUpdate(Date updateKpiDate){
        KpiDataQueryDto query = new KpiDataQueryDto();
        query.setKpiDateEnd(KpiUtils.getDateStart(updateKpiDate));
        query.setKpiDateStart(KpiUtils.getMonthStart(updateKpiDate));
        return kpiOrderLogMapper.selectUnUsedLogs(query);
    }

    /**
     * .stream按照指定key去重
     * @jira: VDERP-2376【五行剑法】规则修改.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/7/9 11:45 上午.
     * @author: Tomcat.Hui.
     * @param keyExtractor: .
     * @return: java.util.function.Predicate<T>.
     * @throw: .
     */
    private static <T> Predicate<T> distinctByKey(Function<? super T, ?> keyExtractor) {
        Map<Object, Boolean> seen = new ConcurrentHashMap<>();
        return t -> seen.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }

    @Override
    public Integer updateUnusedLog(KpiDataQueryDto query){
        return kpiOrderLogMapper.updateLogUsed(query);
    }
}
