/**
 * 重置搜索框的值
 */
function searchReset() {
    $('#keyword').val('');
    $('#userId').val(0);
    $('#invoiceRefundStatus').val(-1);
    $('#startAddDateStr').val('');
    $('#endAddDateStr').val('');
}

/**
 * iframe之间切换选项卡
 * @param index 选项卡下标
 */
function changeTab(index) {
    window.location = '/supplyChain/invoice/hx_invoice_wait.do?idFlag=' + index;
}

/**
 * @describe 标记航信发票的退票处理状态
 * @param hxInvoiceId
 * @param invoiceRefundStatus
 * <AUTHOR>
 * @date 2020/6/9 15:53:12
 */
function updateInvoiceRefundStatus(hxInvoiceId, invoiceRefundStatus) {
    var invoiceRefundStatusStr = '';
    var updateResult = '';
    switch (invoiceRefundStatus) {
        case 0:
            invoiceRefundStatusStr = '确定将该发票标记为未处理吗';
            updateResult = '已将该发票标记为未处理';
            break;
        case 1:
            invoiceRefundStatusStr = '确定将该发票标记为已处理吗';
            updateResult = '已将该发票标记为已处理';
            break;
        default:
            break;
    }
    layer.confirm(invoiceRefundStatusStr, {title: '标记发票处理状态'}, function (index) {
        $.ajax({
            url: '/supplyChain/invoice/updateInvoiceRefundStatus.do',
            data: {
                hxInvoiceId: hxInvoiceId,
                invoiceRefundStatus: invoiceRefundStatus
            },
            type: 'post',
            dataType: 'json',
            success: function (res) {
                if (res.code == 0) {
                    layer.alert(updateResult, function () {
                        window.location.reload();
                    });
                } else {
                    layer.alert(res.message);
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        })
    }, function () {
        layer.close(index);
    })
}