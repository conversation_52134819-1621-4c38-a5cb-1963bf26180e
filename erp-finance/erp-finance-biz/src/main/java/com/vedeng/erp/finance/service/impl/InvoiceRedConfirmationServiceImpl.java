package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.finance.constants.FinanceConstant;
import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.domain.dto.*;
import com.vedeng.erp.finance.domain.entity.InvoiceDetailEntity;
import com.vedeng.erp.finance.domain.entity.InvoiceEntity;
import com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity;
import com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationItemEntity;
import com.vedeng.erp.finance.dto.*;
import com.vedeng.erp.finance.enums.*;
import com.vedeng.erp.finance.mapper.InvoiceDetailMapper;
import com.vedeng.erp.finance.mapper.InvoiceMapper;
import com.vedeng.erp.finance.mapper.InvoiceRedConfirmationItemMapper;
import com.vedeng.erp.finance.mapper.InvoiceRedConfirmationMapper;
import com.vedeng.erp.finance.mapstruct.InvoiceRedConfirmationConvertor;
import com.vedeng.erp.finance.mapstruct.InvoiceRedConfirmationItemConvertor;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import com.vedeng.erp.finance.service.TaxesOpenApiService;
import com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import com.vedeng.erp.trader.service.TraderFinanceApiService;
import com.vedeng.infrastructure.taxes.config.TaxesConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import com.vedeng.common.statemachine.StateMachine;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationServiceViewButton.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务发票红字确认单
 * @date 2023/9/20 13:03
 */
@Service
@Slf4j
public class InvoiceRedConfirmationServiceImpl implements InvoiceRedConfirmationService {

    @Autowired
    private InvoiceRedConfirmationMapper invoiceRedConfirmationMapper;

    @Autowired
    private InvoiceRedConfirmationItemMapper invoiceRedConfirmationItemMapper;

    @Autowired
    private InvoiceMapper invoiceMapper;

    @Autowired
    private TraderFinanceApiService traderFinanceApiService;

    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private TaxesOpenApiService taxesOpenApiService;

    @Autowired
    private InvoiceRedConfirmationConvertor invoiceRedConfirmationConvertor;

    @Autowired
    private InvoiceRedConfirmationItemConvertor invoiceRedConfirmationItemConvertor;

    @Autowired
    @Qualifier("invoiceRedConfirmationOperaMachine")
    StateMachine<InvoiceRedConfirmationStateEnum, InvoiceRedConfirmationEvent, InvoiceRedConfirmationContext> invoiceRedConfirmationOperaMachine;

    @Autowired
    private TaxesConfig taxesConfig;

    @Autowired
    private InvoiceApiService invoiceApiService;

    @Autowired
    private InvoiceApplyService invoiceApplyService;

    @Autowired
    private InvoiceDetailMapper invoiceDetailMapper;

    /**
     * 01 无需确认
     * 02 销方录入待购方确认
     * 03 购方录入待销方确认
     * 04 购销双方已确认
     * 05 作废（销方录入购方否认）
     * 06 作废（购方录入销方否认）
     * 07 作废（超 72 小时未确认）
     * 08 作废（发起方已撤销）
     * 09 作废（确认后撤销）
     */
    private static final List<String> hzqrxxztDmList = Arrays.asList("01", "02", "03", "04");

    /**
     * 销售折让
     */
    private static final String SALES_ALLOWANCE = "04";

    /**
     * 开票有误
     */
    private static final String NO_CONFIRMATION_REQUIRED = "01";

    @Override
    public PageInfo<InvoiceRedConfirmationDto> page(PageParam<InvoiceRedConfirmationDto> query, boolean needButton) {

        log.info("page:入参:{}", JSON.toJSONString(query));

        InvoiceRedConfirmationDto param = query.getParam();
        PageInfo<InvoiceRedConfirmationDto> objectPageInfo = PageHelper.startPage(query).doSelectPageInfo(() -> invoiceRedConfirmationMapper.findByAll(param));
        List<InvoiceRedConfirmationDto> list = objectPageInfo.getList();
        if (needButton && CollUtil.isNotEmpty(list)) {
            list.forEach(x -> x.setButtonList(getButton(x.getInvoiceRedConfirmationId())));
        }

        return objectPageInfo;
    }

    @Override
    public RedConfirmTypeNumDto typeNum() {

        RedConfirmTypeNumDto redConfirmTypeNumDto = new RedConfirmTypeNumDto();
        List<RedConfirmationStatusStatistics> redConfirmationStatus = invoiceRedConfirmationMapper.findRedConfirmationStatus();
        if (CollUtil.isNotEmpty(redConfirmationStatus)) {
            Optional<RedConfirmationStatusStatistics> init = redConfirmationStatus.stream().filter(x -> ErpConstant.ZERO.equals(x.getRedConfirmationStatus())).findFirst();
            init.ifPresent(redConfirmationStatusStatistics -> redConfirmTypeNumDto.setInitNum(redConfirmationStatusStatistics.getNum()));
            Optional<RedConfirmationStatusStatistics> apply = redConfirmationStatus.stream().filter(x -> ErpConstant.ONE.equals(x.getRedConfirmationStatus())).findFirst();
            apply.ifPresent(redConfirmationStatusStatistics -> redConfirmTypeNumDto.setAppliedNum(redConfirmationStatusStatistics.getNum()));
            Optional<RedConfirmationStatusStatistics> confirm = redConfirmationStatus.stream().filter(x -> ErpConstant.TWO.equals(x.getRedConfirmationStatus())).findFirst();
            confirm.ifPresent(redConfirmationStatusStatistics -> redConfirmTypeNumDto.setConfirmedNum(redConfirmationStatusStatistics.getNum()));
        }
        return redConfirmTypeNumDto;
    }

    @Override
    public void associateAftersale(InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationDto.getInvoiceRedConfirmationId(), RELATE_AFTESALE);
        if (Objects.nonNull(failResponseDto) && StrUtil.isNotBlank(failResponseDto.getFailReason())) {
            throw new ServiceException(failResponseDto.getFailReason());
        }
        InvoiceRedConfirmationEntity updateEntity = new InvoiceRedConfirmationEntity();
        updateEntity.setInvoiceRedConfirmationId(invoiceRedConfirmationDto.getInvoiceRedConfirmationId());
        updateEntity.setRedConfirmationScope(invoiceRedConfirmationDto.getRedConfirmationScope());
        updateEntity.setBusinessOrderId(invoiceRedConfirmationDto.getBusinessOrderId());
        updateEntity.setBusinessOrderNo(invoiceRedConfirmationDto.getBusinessOrderNo());
        updateEntity.setAfterSaleBusinessOrderId(invoiceRedConfirmationDto.getAfterSaleBusinessOrderId());
        updateEntity.setAfterSaleBusinessOrderNo(invoiceRedConfirmationDto.getAfterSaleBusinessOrderNo());
        List<InvoiceEntity> invoiceEntityList = invoiceMapper.findByInvoiceNoAndRelatedIdAndTypeAndColorType(invoiceRedConfirmationDto.getBlueInvoiceNo(), invoiceRedConfirmationDto.getBusinessOrderId(), 505, 2);
        if (CollUtil.isEmpty(invoiceEntityList)) {
            throw new ServiceException("未查询到蓝字发票");
        }
        updateEntity.setBlueInvoiceId(invoiceEntityList.get(0).getInvoiceId());
        invoiceRedConfirmationMapper.updateByPrimaryKeySelective(updateEntity);

        if (FinanceConstant.ONE.equals(invoiceRedConfirmationDto.getRedConfirmationScope())) {
            // 部分红冲
            List<InvoiceRedConfirmationItemDto> itemDtoList = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList();
            if (CollUtil.isEmpty(itemDtoList)) {
                throw new ServiceException("部分红冲明细不能为空");
            }
            List<InvoiceRedConfirmationItemEntity> updateItemList = new ArrayList<>();
            for (InvoiceRedConfirmationItemDto itemDto : itemDtoList) {
                List<AssociateAfterSaleDetailDto> associateList = itemDto.getAssociateAfterSaleDetailDtoList();
                if (CollUtil.isEmpty(associateList)) {
                    throw new ServiceException("部分红冲明细关联售后明细不能为空");
                }
                AssociateAfterSaleDetailDto associateAfterSaleDetailDto = associateList.get(0);
                InvoiceRedConfirmationItemEntity updateItemEntity = new InvoiceRedConfirmationItemEntity();
                updateItemEntity.setInvoiceRedConfirmationItemId(itemDto.getInvoiceRedConfirmationItemId());
                updateItemEntity.setBusinessOrderItemId(associateAfterSaleDetailDto.getSaleOrderGoodsId());
                updateItemEntity.setAfterSaleBusinessOrderItemId(associateAfterSaleDetailDto.getAfterSaleGoodsId());
                updateItemEntity.setBlueInvoiceItemId(associateAfterSaleDetailDto.getBlueInvoiceItemId());
                updateItemList.add(updateItemEntity);
            }
            invoiceRedConfirmationItemMapper.updateBatchSelective(updateItemList);
        }

    }

    @Override
    public List<AssociateAfterSaleDetailDto> associateAfterSaleDetailInfo(Integer afterSaleId, Integer saleorderId, String blueInvoiceNo) {
        AfterSalesDto afterSalesDto = afterSalesApiService.queryInfoById(afterSaleId);
        if (Objects.isNull(afterSalesDto)) {
            throw new ServiceException("售后单不存在");
        }
        if (!FinanceConstant.TH.equals(afterSalesDto.getType()) && !FinanceConstant.TP.equals(afterSalesDto.getType())) {
            throw new ServiceException("售后单类型错误");
        }
        // 获取销售订单的税率
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getByAfterSaleId(afterSaleId);
        if (Objects.isNull(saleorderInfoDto)) {
            throw new ServiceException("销售订单不存在");
        }
        BigDecimal ratio = InvoiceTaxTypeEnum.getTax(saleorderInfoDto.getInvoiceType());

        List<InvoiceEntity> invoiceEntityList = invoiceMapper.findByInvoiceNoAndRelatedIdAndTypeAndColorType(blueInvoiceNo, saleorderId, FinanceConstant.ID_505, FinanceConstant.TWO);
        if (CollUtil.isEmpty(invoiceEntityList)) {
            throw new ServiceException("未查询到蓝字发票");
        }
        List<Integer> invoiceIdList = invoiceEntityList.stream().map(InvoiceEntity::getInvoiceId).filter(Objects::nonNull).collect(Collectors.toList());
        List<Integer> invoiceApplyIdList = invoiceEntityList.stream().map(InvoiceEntity::getInvoiceApplyId).filter(Objects::nonNull).collect(Collectors.toList());

        Map<Integer, List<InvoiceDetailDto>> InvoiceDetailMap = new HashMap<>();
        Map<Integer, List<InvoiceApplyDetailDto>> invoiceApplyDetailMap = new HashMap<>();
        List<InvoiceDetailDto> invoiceDetailDtoList = invoiceApiService.findByInvoiceId(invoiceIdList);
        if (CollUtil.isNotEmpty(invoiceDetailDtoList)) {
            InvoiceDetailMap = invoiceDetailDtoList.stream().collect(Collectors.groupingBy(InvoiceDetailDto::getDetailgoodsId));
        }
        List<InvoiceApplyDetailDto> invoiceApplyDetailDtoList = invoiceApplyService.getByInvoiceApplyId(invoiceApplyIdList);
        if (CollUtil.isNotEmpty(invoiceApplyDetailDtoList)) {
            invoiceApplyDetailMap = invoiceApplyDetailDtoList.stream().collect(Collectors.groupingBy(InvoiceApplyDetailDto::getDetailgoodsId));
        }

        List<AssociateAfterSaleDetailDto> resultList = new ArrayList<>();
        if (FinanceConstant.TH.equals(afterSalesDto.getType())) {
            afterSaleThProcessor(afterSaleId, ratio, InvoiceDetailMap, invoiceApplyDetailMap, resultList);
        }
        if (FinanceConstant.TP.equals(afterSalesDto.getType())) {
            afterSaleTpProcessor(saleorderId, InvoiceDetailMap, ratio, invoiceApplyDetailMap, resultList, invoiceDetailDtoList);
        }
        return resultList;
    }

    private void afterSaleTpProcessor(Integer saleorderId, Map<Integer, List<InvoiceDetailDto>> InvoiceDetailMap, BigDecimal ratio, Map<Integer, List<InvoiceApplyDetailDto>> invoiceApplyDetailMap, List<AssociateAfterSaleDetailDto> resultList, List<InvoiceDetailDto> invoiceDetailDtoList) {
        if (CollUtil.isEmpty(invoiceDetailDtoList)) {
            return;
        }
        List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtoList = saleOrderGoodsApiService.getBySaleorderId(saleorderId);
        if (CollUtil.isEmpty(saleOrderGoodsDetailDtoList)) {
            return;
        }
        Map<Integer, List<SaleOrderGoodsDetailDto>> goodsMap = saleOrderGoodsDetailDtoList.stream().collect(Collectors.groupingBy(SaleOrderGoodsDetailDto::getSaleorderGoodsId));
        for (InvoiceDetailDto invoiceDetailDto : invoiceDetailDtoList) {
            AssociateAfterSaleDetailDto dto = new AssociateAfterSaleDetailDto();
            dto.setNum(invoiceDetailDto.getNum());
            dto.setShjshj(invoiceDetailDto.getTotalAmount());
            // 售后税额 = （售后价税合计/（1+销售订单税率））*销售订单税率, 先乘后除保留两位小数
            BigDecimal taxAmount = (dto.getShjshj().multiply(ratio)).divide(ratio.add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
            dto.setShje(dto.getShjshj().subtract(taxAmount));
            dto.setBlueInvoiceItemId(invoiceDetailDto.getInvoiceDetailId());
            List<SaleOrderGoodsDetailDto> goodsList = goodsMap.get(invoiceDetailDto.getDetailgoodsId());
            if (CollUtil.isNotEmpty(goodsList)) {
                SaleOrderGoodsDetailDto detail = goodsList.get(0);
                dto.setXmmc(detail.getSkuName());
                dto.setGgxh(detail.getSpec());
                dto.setDw(detail.getUnitName());
                dto.setSphfwssflhbbm(StrUtil.padAfter(detail.getTaxCategoryNo(), 19, "0"));
                dto.setSaleOrderGoodsId(detail.getSaleorderGoodsId());
                dto.setAfterSaleGoodsId(FinanceConstant.ZERO);
                dto.setAfterSaletype(FinanceConstant.TP);
            }
            List<InvoiceApplyDetailDto> invoiceApplyDetailDtos = invoiceApplyDetailMap.get(invoiceDetailDto.getDetailgoodsId());
            if (CollUtil.isNotEmpty(invoiceApplyDetailDtos)) {
                dto.setXh(invoiceApplyDetailDtos.get(0).getInvoiceApplyDetailId());
            }
            resultList.add(dto);
        }
    }

    private void afterSaleThProcessor(Integer afterSaleId, BigDecimal ratio, Map<Integer, List<InvoiceDetailDto>> InvoiceDetailMap, Map<Integer, List<InvoiceApplyDetailDto>> invoiceApplyDetailMap, List<AssociateAfterSaleDetailDto> resultList) {
        // 销售订单退货
        List<AfterSalesGoodsDto> afterSalesGoodsDtoList = afterSalesApiService.getAfterSalesGoodsByAfterSalesId(afterSaleId).stream()
                .filter(item -> InvoiceDetailMap.containsKey(item.getOrderDetailId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(afterSalesGoodsDtoList)) {
            log.info("销售订单退货，未查询到已录票销售商品关联的售后商品，afterSaleId:{},InvoiceDetailMap:{}", afterSaleId, JSON.toJSONString(InvoiceDetailMap));
            return;
        }
        afterSalesGoodsDtoList.forEach(item -> this.handleAfterSalesGood(ratio, item));
        for (AfterSalesGoodsDto afterSalesGoodsDto : afterSalesGoodsDtoList) {
            AssociateAfterSaleDetailDto dto = new AssociateAfterSaleDetailDto();
            dto.setXmmc(afterSalesGoodsDto.getSkuName());
            dto.setGgxh(afterSalesGoodsDto.getSpec());
            dto.setDw(afterSalesGoodsDto.getUnitName());
            dto.setSphfwssflhbbm(afterSalesGoodsDto.getTaxCategoryNo());
            dto.setNum(BigDecimal.valueOf(afterSalesGoodsDto.getNum()));
            dto.setShje(afterSalesGoodsDto.getAfterSalesAmount());
            dto.setShjshj(afterSalesGoodsDto.getAfterSalesAmountAndTax());
            dto.setSaleOrderGoodsId(afterSalesGoodsDto.getOrderDetailId());
            dto.setAfterSaleGoodsId(afterSalesGoodsDto.getAfterSalesGoodsId());
            dto.setAfterSaletype(FinanceConstant.TH);
            List<InvoiceDetailDto> invoiceDetailDtos = InvoiceDetailMap.get(afterSalesGoodsDto.getOrderDetailId());
            if (CollUtil.isNotEmpty(invoiceDetailDtos)) {
                dto.setBlueInvoiceItemId(invoiceDetailDtos.get(0).getInvoiceDetailId());
            }
            List<InvoiceApplyDetailDto> invoiceApplyDetailDtos = invoiceApplyDetailMap.get(afterSalesGoodsDto.getOrderDetailId());
            if (CollUtil.isNotEmpty(invoiceApplyDetailDtos)) {
                dto.setXh(invoiceApplyDetailDtos.get(0).getInvoiceApplyDetailId());
            }
            resultList.add(dto);
        }
    }

    @Override
    public List<InvoiceRedConfirmationServiceViewButton> getButton(Integer invoiceRedConfirmationId) {

        // 状态和事件
        InvoiceRedConfirmationEntity redConfirmation = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
        InvoiceRedConfirmationDto invoiceRedConfirmationDto = invoiceRedConfirmationConvertor.toDto(redConfirmation);
        return new InvoiceRedConfirmationServiceViewButton(invoiceRedConfirmationDto)
                .checkApplyButton()
                .checkAgreeButton()
                .checkRejectButton()
                .checkInvoiceButton()
                .checkRevokedButton()
                .checkVoidButton()
                .checkUpdateButton()
                .checkAssociatedAfterSalesButton().initButtons();
    }


    @Override
    public InvoiceRedConfirmationStateEnum executeInvoiceRedConfirmationOperaMachine(InvoiceRedConfirmationDto invoiceRedConfirmationDto, InvoiceRedConfirmationEvent event) {
        InvoiceRedConfirmationContext context = new InvoiceRedConfirmationContext(invoiceRedConfirmationDto, event);
        // 获取最新详情
        SaleInvoiceRedConfirmDetailResponseDto detailResponseDto = this.getTaxesInvoiceRedConfirmation(invoiceRedConfirmationDto);
        // 组装事件上下文
        context.buildInvoiceRedConfirmationContext(detailResponseDto);
        return invoiceRedConfirmationOperaMachine.fireEvent(context.getInvoiceRedConfirmationStateEnum(), context.getInvoiceRedConfirmationEvent(), context);
    }

    /**
     * 实时获取确认单详情
     *
     * @param invoiceRedConfirmationDto
     * @return
     */
    @Override
    public SaleInvoiceRedConfirmDetailResponseDto getTaxesInvoiceRedConfirmation(InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        SaleInvoiceRedConfirmDetailRequestDto confirmDetailRequestDto = new SaleInvoiceRedConfirmDetailRequestDto();
        if (StringUtils.isBlank(invoiceRedConfirmationDto.getUuid())) {
            SaleInvoiceRedConfirmDetailResponseDto dto = new SaleInvoiceRedConfirmDetailResponseDto();
            dto.setIsSuccess(Boolean.FALSE);
            dto.setReturnMessage("Uuid为空");
            log.info("查询确认单详情，Uuid不能为空");
            return dto;
        }

        confirmDetailRequestDto.setUuid(invoiceRedConfirmationDto.getUuid());
        SaleInvoiceRedConfirmDetailResponseDto openapi = (SaleInvoiceRedConfirmDetailResponseDto) taxesOpenApiService.openapi(confirmDetailRequestDto, TaxesInterfaceCodeEnum.COMFIRM_DETAIL);
        if (Objects.isNull(openapi)) {
            log.info("查询税金详情返回结果为空");
            throw new ServiceException("查询税金详情返回结果为空");
        }
        if (!openapi.getIsSuccess()) {
            log.info("查询税金详情返回结果:{}", JSON.toJSONString(openapi));
            throw new ServiceException(openapi.getReturnMessage());
        }
        return openapi;
    }

    @Override
    public void init(InvoiceRedConfirmationDto dto) {
        if (Objects.isNull(dto)) {
            throw new ServiceException("初始化红字确认单传参错误");
        }
        // 初始化检查
        List<InvoiceRedConfirmationEntity> invoiceRedConfirmationEntities = invoiceRedConfirmationMapper.initCheckByAfterSaleBusinessOrderNo(dto.getAfterSaleBusinessOrderNo(), dto.getBlueInvoiceNo());
        if (CollUtil.isNotEmpty(invoiceRedConfirmationEntities)) {
            throw new ServiceException("已存在进行中红字确认单，请至红字确认单列表处理");
        }
        // 调用接口：数电：红字确认单初始化
        SaleInvoiceRedConfirmInitRequestDto requestDto = new SaleInvoiceRedConfirmInitRequestDto();
        requestDto.setGmfnsrsbh(dto.getTaxNum());
        requestDto.setLzfpqdhm(dto.getBlueInvoiceNo());
        requestDto.setLzkprq(DateUtil.formatDateTime(dto.getOpenInvoiceTime()));
        SaleInvoiceRedConfirmInitResponseDto responseDto = (SaleInvoiceRedConfirmInitResponseDto) taxesOpenApiService.openapi(requestDto, TaxesInterfaceCodeEnum.COMFIRM_INIT);
        if (!responseDto.getIsSuccess()) {
            throw new ServiceException(responseDto.getReturnMessage());
        }
        // 封装数据
        InvoiceRedConfirmationDto invoiceRedConfirmationDto = toInvoiceRedConfirmationDto(dto, responseDto);
        this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.USER_INIT);
    }

    @Override
    public void sysInit() {
        String nowDateStr = DateFormatUtils.format(new Date(), "yyyy-MM-dd");
        List<String> uuidList = new ArrayList<>();
        // 1.根据条件，循环分页调用列表接口，获取uuid集合。
        for (String hzqrxxztDm : hzqrxxztDmList) {
            int pageNum = 1;
            int pageSize = 100;
            boolean hasMoreData = true;
            while (hasMoreData) {
                SaleInvoiceRedConfirmListRequestDto dto = new SaleInvoiceRedConfirmListRequestDto();
                dto.setKprqz(nowDateStr);
                dto.setQrdzt(hzqrxxztDm);
                dto.setPageNumber(pageNum);
                dto.setPageSize(pageSize);
                SaleInvoiceRedConfirmListResponseDto openapi = (SaleInvoiceRedConfirmListResponseDto) taxesOpenApiService.openapi(dto, TaxesInterfaceCodeEnum.COMFIRM_LIST);
                if (CollUtil.isEmpty(openapi.getData())) {
                    hasMoreData = false;
                } else {
                    uuidList.addAll(openapi.getData().stream().map(SaleInvoiceRedConfirmListResponseDto.ConfirmData::getUuid).collect(Collectors.toList()));
                    pageNum++;
                }
            }
        }
        if (CollUtil.isEmpty(uuidList)) {
            return;
        }
        // 2.查询本地是否存在
        List<String> alreadyExistUuidList = invoiceRedConfirmationMapper.findUuid();
        if (CollUtil.isNotEmpty(alreadyExistUuidList)) {
            uuidList.removeAll(alreadyExistUuidList);
        }
        if (CollUtil.isEmpty(uuidList)) {
            return;
        }
        for (String uuid : uuidList) {
            // 3.不存在调用系统初始化状态机
            InvoiceRedConfirmationDto invoiceRedConfirmationDto = new InvoiceRedConfirmationDto();
            invoiceRedConfirmationDto.setRedConfirmationStatus(InvoiceRedConfirmationStateEnum.INIT.getCode());
            invoiceRedConfirmationDto.setUuid(uuid);
            invoiceRedConfirmationDto.setAfterSaleBusinessOrderType(FinanceConstant.ONE);
            this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.SYS_INIT);
        }
    }

    @Override
    public List<RedConfirmResponseDto> audit(RedConfirmReqAuditRequestDto requestDto) {
        List<RedConfirmResponseDto> result = new ArrayList<>();
        List<Integer> invoiceRedConfirmationIds = requestDto.getInvoiceRedConfirmationIds();

        for (Integer invoiceRedConfirmationId : invoiceRedConfirmationIds) {
            // 1、数据校验
            RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, YNEnum.Y.name().equals(requestDto.getStatus()) ? AGREE_NAME : REJECT_NAME);
            if (Objects.nonNull(failResponseDto)) {
                result.add(failResponseDto);
                continue;
            }
            // 2. 组装税金入参数据
            SaleInvoiceRedConfirmAuditRequestDto redConfirmAuditRequestDto = new SaleInvoiceRedConfirmAuditRequestDto();
            InvoiceRedConfirmationEntity entity = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
            redConfirmAuditRequestDto.setUuid(entity.getUuid());
            redConfirmAuditRequestDto.setQrlx(requestDto.getStatus());
            // 3. 调用税金接口
            SaleInvoiceRedConfirmAuditResponseDto responseDto = (SaleInvoiceRedConfirmAuditResponseDto) taxesOpenApiService.openapi(redConfirmAuditRequestDto, TaxesInterfaceCodeEnum.COMFIRM_AUDIT);
            Boolean isSuccess = responseDto.getIsSuccess();
            if (!isSuccess) {
                RedConfirmResponseDto taxesFailResponseDto = getRedConfirmReqApplyResponseDto(invoiceRedConfirmationId, responseDto.getReturnMessage());
                result.add(taxesFailResponseDto);
                continue;
            }
            // 4. 判断是否成功 成功执行红字确认单状态机
            InvoiceRedConfirmationDto invoiceRedConfirmationDto = this.getInvoiceRedConfirmationDto(invoiceRedConfirmationId);
            if ("Y".equals(requestDto.getStatus())) {
                this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.AGREE);
            }
            if ("N".equals(requestDto.getStatus())) {
                this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.REJECT);
            }
        }
        return result;
    }

    @Override
    public List<RedConfirmResponseDto> apply(RedConfirmReqApplyRequestDto redConfirmReqApplyRequestDto) {
        log.info("确认单申请接口入参：{}", JSON.toJSONString(redConfirmReqApplyRequestDto));
        List<RedConfirmResponseDto> result = new ArrayList<>();
        // 1 是否全部红冲 是系统挂靠关系蓝票明细数据 && 数据校验
        List<InvoiceRedConfirmationDto> applyRequestDtoList = this.getApplyRequestDetail(redConfirmReqApplyRequestDto, result);
        for (InvoiceRedConfirmationDto invoiceRedConfirmationDto : applyRequestDtoList) {
            // 2. 组装税金入参数据
            SaleInvoiceRedConfirmApplyRequestDto applyRequestDto = this.assembleApplyParam(invoiceRedConfirmationDto);
            // 3. 调用税金接口
            log.info("apply调用税金接口入参: {}", JSON.toJSONString(applyRequestDto));
            SaleInvoiceRedConfirmApplyResponseDto responseDto = (SaleInvoiceRedConfirmApplyResponseDto) taxesOpenApiService.openapi(applyRequestDto, TaxesInterfaceCodeEnum.COMFIRM_APPLY);
            log.info("apply调用税金接口返回值: {}", JSON.toJSONString(responseDto));
            Boolean isSuccess = responseDto.getIsSuccess();
            if (!isSuccess) {
                RedConfirmResponseDto taxesFailResponseDto = new RedConfirmResponseDto(invoiceRedConfirmationDto.getInvoiceRedConfirmationId(), responseDto.getReturnMessage());
                result.add(taxesFailResponseDto);
                continue;
            }
            // 先更新uuid，防止丢失，无需事务
            this.updateUuid(responseDto.getUuid(), invoiceRedConfirmationDto.getInvoiceRedConfirmationId());
            invoiceRedConfirmationDto.setUuid(responseDto.getUuid());
            // 4. 判断是否成功 成功执行红字确认单状态机
            this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.APPLY);
        }
        return result;
    }

    @Override
    public List<RedConfirmResponseDto> cancel(RedConfirmReqCommonRequestDto requestDto) {
        List<RedConfirmResponseDto> result = new ArrayList<>();
        List<Integer> invoiceRedConfirmationIds = requestDto.getInvoiceRedConfirmationIds();
        for (Integer invoiceRedConfirmationId : invoiceRedConfirmationIds) {
            // 1、数据校验
            RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, CANCEL_NAME);
            if (Objects.nonNull(failResponseDto)) {
                result.add(failResponseDto);
                continue;
            }
            // 2. 组装税金入参数据
            SaleInvoiceRedConfirmCancelRequestDto redConfirmCancelRequestDto = new SaleInvoiceRedConfirmCancelRequestDto();
            InvoiceRedConfirmationEntity entity = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
            redConfirmCancelRequestDto.setUuid(entity.getUuid());
            // 3. 调用税金接口
            SaleInvoiceRedConfirmCancelResponseDto responseDto = (SaleInvoiceRedConfirmCancelResponseDto) taxesOpenApiService.openapi(redConfirmCancelRequestDto, TaxesInterfaceCodeEnum.COMFIRM_CANCEL);
            Boolean isSuccess = responseDto.getIsSuccess();
            if (!isSuccess) {
                result.add(new RedConfirmResponseDto(invoiceRedConfirmationId, responseDto.getReturnMessage()));
                continue;
            }
            // 4. 判断是否成功 成功执行红字确认单状态机
            InvoiceRedConfirmationDto invoiceRedConfirmationDto = this.getInvoiceRedConfirmationDto(invoiceRedConfirmationId);
            this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.CANCEL);
        }
        return result;
    }

    @Override
    public List<RedConfirmResponseDto> openInvoice(RedConfirmReqCommonRequestDto requestDto) {
        List<RedConfirmResponseDto> result = new ArrayList<>();
        List<Integer> invoiceRedConfirmationIds = requestDto.getInvoiceRedConfirmationIds();
        for (Integer invoiceRedConfirmationId : invoiceRedConfirmationIds) {
            // 1、数据校验
            RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, INVOICE_NAME);
            if (Objects.nonNull(failResponseDto)) {
                result.add(failResponseDto);
                continue;
            }
            // 获取最新详情
            InvoiceRedConfirmationDto invoiceRedConfirmationDto = this.getInvoiceRedConfirmationDto(invoiceRedConfirmationId);
            SaleInvoiceRedConfirmDetailResponseDto detailResponseDto = this.getTaxesInvoiceRedConfirmation(invoiceRedConfirmationDto);
            // 已开票不调用税金接口
            if (YNEnum.N.name().equals(detailResponseDto.getYkjhzfpbz())) {

                // 2. 组装税金入参数据
                InvoiceRedConfirmationEntity entity = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
                SaleInvoiceRedConfirmOpenRequestDto redConfirmOpenRequestDto = new SaleInvoiceRedConfirmOpenRequestDto(entity.getUuid());
                // 3. 调用税金接口
                SaleInvoiceRedConfirmOpenResponseDto responseDto = (SaleInvoiceRedConfirmOpenResponseDto) taxesOpenApiService.openapi(redConfirmOpenRequestDto, TaxesInterfaceCodeEnum.COMFIRM_OPEN);
                Boolean isSuccess = responseDto.getIsSuccess();
                if (!isSuccess) {
                    RedConfirmResponseDto taxesFailResponseDto = getRedConfirmReqApplyResponseDto(invoiceRedConfirmationId, responseDto.getReturnMessage());
                    result.add(taxesFailResponseDto);
                    continue;
                }
            }
            // 4. 判断是否成功 成功执行红字确认单状态机
            this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.OPEN_INVOICE);
        }
        return result;
    }

    @Override
    public List<RedConfirmResponseDto> invalid(RedConfirmReqCommonRequestDto requestDto) {
        List<InvoiceRedConfirmationEntity> updateList = new ArrayList<>();
        List<RedConfirmResponseDto> result = new ArrayList<>();

        List<Integer> invoiceRedConfirmationIds = requestDto.getInvoiceRedConfirmationIds();
        for (Integer invoiceRedConfirmationId : invoiceRedConfirmationIds) {
            // 1、数据校验
            RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, INVALID_NAME);
            if (Objects.nonNull(failResponseDto)) {
                result.add(failResponseDto);
                continue;
            }
            InvoiceRedConfirmationEntity entity = new InvoiceRedConfirmationEntity();
            entity.setInvoiceRedConfirmationId(invoiceRedConfirmationId);
            entity.setRedConfirmationStatus(InvoiceRedConfirmationStateEnum.HAVE_BEEN_CANCEL.getCode());
            updateList.add(entity);
        }
        if (!CollectionUtils.isEmpty(updateList)) {
            invoiceRedConfirmationMapper.updateBatchSelective(updateList);
        }
        return result;
    }


    @Override
    public List<RedConfirmResponseDto> renew(RedConfirmReqCommonRequestDto requestDto) {
        List<RedConfirmResponseDto> result = new ArrayList<>();
        List<Integer> invoiceRedConfirmationIds = requestDto.getInvoiceRedConfirmationIds();
        for (Integer invoiceRedConfirmationId : invoiceRedConfirmationIds) {
            // 1、数据校验
            RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, UPDATE_NAME);
            if (Objects.nonNull(failResponseDto)) {
                result.add(failResponseDto);
                continue;
            }
            InvoiceRedConfirmationDto invoiceRedConfirmationDto = this.getInvoiceRedConfirmationDto(invoiceRedConfirmationId);
            // 判断是否成功 成功执行红字确认单状态机
            this.executeInvoiceRedConfirmationOperaMachine(invoiceRedConfirmationDto, InvoiceRedConfirmationEvent.RENEW);
        }
        return result;
    }


    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void add(InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        if (invoiceRedConfirmationDto == null) {
            return;
        }
        InvoiceRedConfirmationEntity invoiceRedConfirmationEntity = invoiceRedConfirmationConvertor.toEntity(invoiceRedConfirmationDto);
        invoiceRedConfirmationMapper.insertSelective(invoiceRedConfirmationEntity);
        invoiceRedConfirmationDto.setInvoiceRedConfirmationId(invoiceRedConfirmationEntity.getInvoiceRedConfirmationId());
        List<InvoiceRedConfirmationItemDto> invoiceRedConfirmationItemDtoList = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList();
        if (CollUtil.isEmpty(invoiceRedConfirmationItemDtoList)) {
            return;
        }
        invoiceRedConfirmationItemDtoList.forEach(dto -> {
            dto.setInvoiceRedConfirmationId(invoiceRedConfirmationEntity.getInvoiceRedConfirmationId());
            InvoiceRedConfirmationItemEntity invoiceRedConfirmationItemEntity = invoiceRedConfirmationItemConvertor.toEntity(dto);
            invoiceRedConfirmationItemMapper.insertSelective(invoiceRedConfirmationItemEntity);
            dto.setInvoiceRedConfirmationItemId(invoiceRedConfirmationItemEntity.getInvoiceRedConfirmationItemId());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        InvoiceRedConfirmationEntity entity = invoiceRedConfirmationConvertor.toEntity(invoiceRedConfirmationDto);
        invoiceRedConfirmationMapper.updateByPrimaryKeySelective(entity);
        List<InvoiceRedConfirmationItemDto> invoiceRedConfirmationItemDtoList = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList();
        if (CollUtil.isEmpty(invoiceRedConfirmationItemDtoList)) {
            throw new ServiceException("红字确认单明细不存在");
        }
        invoiceRedConfirmationItemDtoList.forEach(dto -> {
            InvoiceRedConfirmationItemEntity e = invoiceRedConfirmationItemConvertor.toEntity(dto);
            e.setInvoiceRedConfirmationId(invoiceRedConfirmationDto.getInvoiceRedConfirmationId());
            invoiceRedConfirmationItemMapper.insertOrUpdateSelective(e);
            dto.setInvoiceRedConfirmationItemId(e.getInvoiceRedConfirmationItemId());
        });
    }


    /**
     * 获取明细
     *
     * @param redConfirmReqApplyRequestDto
     * @return
     */
    private List<InvoiceRedConfirmationDto> getApplyRequestDetail(RedConfirmReqApplyRequestDto redConfirmReqApplyRequestDto, List<RedConfirmResponseDto> result) {
        List<InvoiceRedConfirmationDto> dtoList = new ArrayList<>();
        if (FinanceConstant.ZERO.equals(redConfirmReqApplyRequestDto.getRedConfirmationScope())) {
            // 0 全部
            if (!CollectionUtils.isEmpty(redConfirmReqApplyRequestDto.getInvoiceRedConfirmationIds())) {
                // 列表批量申请
                redConfirmReqApplyRequestDto.getInvoiceRedConfirmationDto().setApplyReason(NO_CONFIRMATION_REQUIRED);
            }
            Integer id = redConfirmReqApplyRequestDto.getInvoiceRedConfirmationDto().getInvoiceRedConfirmationId();
            if (Objects.nonNull(id)) {
                redConfirmReqApplyRequestDto.getInvoiceRedConfirmationIds().add(id);
            }
            List<Integer> invoiceRedConfirmationIds = redConfirmReqApplyRequestDto.getInvoiceRedConfirmationIds();
            for (Integer invoiceRedConfirmationId : invoiceRedConfirmationIds) {
                RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, APPLY_NAME);
                if (Objects.nonNull(failResponseDto)) {
                    result.add(failResponseDto);
                    continue;
                }
                InvoiceRedConfirmationDto invoiceRedConfirmationDto = this.calculateRedConfirmation(redConfirmReqApplyRequestDto, invoiceRedConfirmationId, redConfirmReqApplyRequestDto.getRedConfirmationScope());
                dtoList.add(invoiceRedConfirmationDto);
            }
            return dtoList;
        }

        if (FinanceConstant.ONE.equals(redConfirmReqApplyRequestDto.getRedConfirmationScope())) {
            // 1 部分
            Integer invoiceRedConfirmationId = redConfirmReqApplyRequestDto.getInvoiceRedConfirmationDto().getInvoiceRedConfirmationId();
            RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, APPLY_NAME);
            if (Objects.nonNull(failResponseDto)) {
                result.add(failResponseDto);
                return dtoList;
            }
            return Collections.singletonList(this.calculateRedConfirmation(redConfirmReqApplyRequestDto, invoiceRedConfirmationId, redConfirmReqApplyRequestDto.getRedConfirmationScope()));
        }
        throw new ServiceException("封装明细失败");
    }

    /**
     * 计算确认单总金额 税额等信息，包括部分红冲和全部红冲
     *
     * @param redConfirmReqApplyRequestDto 前端入参
     * @return InvoiceRedConfirmationDto
     */
    private InvoiceRedConfirmationDto calculateRedConfirmation(RedConfirmReqApplyRequestDto redConfirmReqApplyRequestDto, Integer invoiceRedConfirmationId, Integer redConfirmationScope) {
        InvoiceRedConfirmationDto temp = invoiceRedConfirmationConvertor.toDto(invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId));
        temp.setBlueInvoiceTime(invoiceApiService.findbyInvoiceId(temp.getBlueInvoiceId()).getOpenInvoiceTime());
        temp.setApplyReason(redConfirmReqApplyRequestDto.getInvoiceRedConfirmationDto().getApplyReason());
        temp.setRedConfirmationScope(redConfirmationScope);
        List<InvoiceRedConfirmationItemDto> itemDtoList = new ArrayList<>();
        // 全部红冲, 调用蓝票查询接口组装明细
        if (FinanceConstant.ZERO.equals(redConfirmationScope)) {
            itemDtoList = this.getBlueInvoiceDetail(temp.getBlueInvoiceId());
        }
        // 部分红冲，前端组装好的明细数据(仅从申请页面可以部分红冲)
        if (FinanceConstant.ONE.equals(redConfirmationScope)) {
            itemDtoList = redConfirmReqApplyRequestDto.getInvoiceRedConfirmationDto().getInvoiceRedConfirmationItemDtoList();
        }
        // 明细中的数量 金额等，需要取负数
        itemDtoList.forEach(item -> {
            item.setAmount(item.getAmount().abs().negate());
            item.setPricePlusTaxes(item.getPricePlusTaxes().abs().negate());
            item.setUaxAmount(item.getUaxAmount().abs().negate());
            // 销售折让 单价和数量需要传空,这里我们取数据库表中字段默认值
            if (!SALES_ALLOWANCE.equals(redConfirmReqApplyRequestDto.getInvoiceRedConfirmationDto().getApplyReason())) {
                item.setQuantity(item.getQuantity().abs().negate());
                item.setUnitPrice(item.getUnitPrice().abs().negate());
            } else {
                item.setQuantity(BigDecimal.ZERO);
                item.setUnitPrice(BigDecimal.ZERO);
            }
        });
        temp.setApplyAmount(itemDtoList.stream().map(InvoiceRedConfirmationItemDto::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        temp.setApplyTaxAmount(itemDtoList.stream().map(InvoiceRedConfirmationItemDto::getUaxAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        temp.setApplyPricePlusTaxes(itemDtoList.stream().map(InvoiceRedConfirmationItemDto::getPricePlusTaxes).reduce(BigDecimal.ZERO, BigDecimal::add));
        temp.setInvoiceRedConfirmationItemDtoList(itemDtoList);
        log.info("确认单组装结果：{}", JSON.toJSONString(temp));
        return temp;
    }

    @Override
    public List<AfterSalesGoodsDto> getRedDetailList(Integer invoiceRedConfirmationId) {
        InvoiceRedConfirmationEntity confirmationEntity = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
        AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(confirmationEntity.getAfterSaleBusinessOrderId());
        List<AfterSalesGoodsDto> afterSalesGoods = new ArrayList<>();
        List<InvoiceDetailEntity> invoiceDetailList = invoiceDetailMapper.queryAllByInvoiceId(confirmationEntity.getBlueInvoiceId());
        // 区分售后类型
        if (FinanceConstant.TH.equals(afterSalesDto.getType())) {
            // 过滤出 已经开过蓝票的售后商品
            Map<Integer, InvoiceDetailEntity> invoiceDetailMap = invoiceDetailList.stream().collect(Collectors.toMap(InvoiceDetailEntity::getDetailgoodsId, Function.identity()));
            afterSalesGoods = afterSalesApiService.getAfterSalesGoodsByAfterSalesId(confirmationEntity.getAfterSaleBusinessOrderId())
                    .stream()
                    .filter(item -> invoiceDetailMap.containsKey(item.getOrderDetailId()))
                    .peek(item -> item.setBlueInvoiceDetailId(invoiceDetailMap.get(item.getOrderDetailId()).getInvoiceDetailId()))
                    .collect(Collectors.toList());
        }
        if (FinanceConstant.TP.equals(afterSalesDto.getType())) {
            // 仅退票时，售后明细取关联蓝票的明细
            List<SaleOrderGoodsDetailDto> saleOrderGoodsDetailDtoList = saleOrderGoodsApiService.getBySaleorderId(confirmationEntity.getBusinessOrderId());
            Map<Integer, SaleOrderGoodsDetailDto> saleOrderGoodsDtoMap = saleOrderGoodsDetailDtoList.stream().collect(Collectors.toMap(SaleOrderGoodsDetailDto::getSaleorderGoodsId, Function.identity()));
            afterSalesGoods = invoiceDetailList.stream().map(item -> {
                AfterSalesGoodsDto temp = new AfterSalesGoodsDto();
                SaleOrderGoodsDetailDto saleOrderGoodsDto = saleOrderGoodsDtoMap.get(item.getDetailgoodsId());
                BeanUtil.copyProperties(saleOrderGoodsDto, temp);
                temp.setAfterSalesGoodsId(item.getDetailgoodsId());
                temp.setOrderDetailId(item.getDetailgoodsId());
                // price 和num取蓝票明细中的
                temp.setAfterSaleNum(item.getNum());
                temp.setPrice(item.getPrice());
                temp.setBlueInvoiceDetailId(item.getInvoiceDetailId());
                return temp;
            }).collect(Collectors.toList());
        }
        // 获取销售订单的税率
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getByAfterSaleId(confirmationEntity.getAfterSaleBusinessOrderId());
        BigDecimal ratio = InvoiceTaxTypeEnum.getTax(saleorderInfoDto.getInvoiceType());
        afterSalesGoods.forEach(item -> this.handleAfterSalesGood(ratio, item));
        return afterSalesGoods;
    }

    /**
     * 处理售后商品信息
     *
     * @param ratio 销售单税率
     * @param item  AfterSalesGoodsDto
     */
    private void handleAfterSalesGood(BigDecimal ratio, AfterSalesGoodsDto item) {
        item.handleInfo();
        // 售后价税合计 = 数量*价格
        BigDecimal afterSalesAmountAndTax = item.getPrice().multiply(item.getAfterSaleNum());
        // 售后税额 = （售后价税合计/（1+销售订单税率））*销售订单税率, 先乘后除保留两位小数
        BigDecimal taxAmount = (afterSalesAmountAndTax.multiply(ratio)).divide(ratio.add(BigDecimal.ONE), 2, RoundingMode.HALF_UP);
        // 售后金额 = 售后价税合计 - 售后税额
        item.setAfterSalesAmount(afterSalesAmountAndTax.subtract(taxAmount));
        item.setAfterSalesAmountAndTax(afterSalesAmountAndTax);
        item.setTax(taxAmount);
    }

    @Override
    public InvoiceRedConfirmationDto getInvoiceRedConfirmationById(Integer invoiceRedConfirmationId) {
        InvoiceRedConfirmationDto result = invoiceRedConfirmationConvertor.toDto(invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId));
        List<InvoiceRedConfirmationItemDto> redConfirmationItemDtoList = invoiceRedConfirmationItemConvertor.toDto(invoiceRedConfirmationItemMapper.getByInvoiceRedConfirmationId(invoiceRedConfirmationId));
        result.setBlueInvoiceTime(invoiceApiService.findbyInvoiceId(result.getBlueInvoiceId()).getOpenInvoiceTime());
        // 查询按钮显示
        result.setButtonList(this.getButton(invoiceRedConfirmationId));
        // 查询确认单明细对应的售后商品信息
        List<AfterSalesGoodsDto> afterSalesGoodsDtoList = this.getRedDetailList(invoiceRedConfirmationId);
        AfterSalesDto afterSalesDto = afterSalesApiService.getAfterSalesById(result.getAfterSaleBusinessOrderId());
        Map<Integer, AfterSalesGoodsDto> afterSalesGoodsMap = afterSalesGoodsDtoList.stream()
                .collect(Collectors.toMap(AfterSalesGoodsDto::getAfterSalesGoodsId, Function.identity()));
        redConfirmationItemDtoList.forEach(item -> {
            // 需要区分仅退票和退货退票，用不同的字段关联
            AfterSalesGoodsDto afterSalesGoodsDto = new AfterSalesGoodsDto();
            if (FinanceConstant.TH.equals(afterSalesDto.getType())) {
                afterSalesGoodsDto = afterSalesGoodsMap.get(item.getAfterSaleBusinessOrderItemId());
            }
            if (FinanceConstant.TP.equals(afterSalesDto.getType())) {
                afterSalesGoodsDto = afterSalesGoodsMap.get(item.getBusinessOrderItemId());
            }
            if (Objects.nonNull(afterSalesGoodsDto)) {
                item.getAfterSalesGoodsDtoList().add(afterSalesGoodsDto);
            }
        });
        result.setInvoiceRedConfirmationItemDtoList(redConfirmationItemDtoList);
        return result;
    }

    @Override
    public AssociateSubjectInfoDto associateSubjectInfo(Integer invoiceRedConfirmationId) {
        AssociateSubjectInfoDto associateSubjectInfoDto = new AssociateSubjectInfoDto();
        RedConfirmResponseDto failResponseDto = checkParams(invoiceRedConfirmationId, RELATE_AFTESALE);
        if (Objects.nonNull(failResponseDto) && StrUtil.isNotBlank(failResponseDto.getFailReason())) {
            throw new ServiceException(failResponseDto.getFailReason());
        }
        InvoiceRedConfirmationEntity invoiceRedConfirmationEntity = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
        if (Objects.isNull(invoiceRedConfirmationEntity)) {
            throw new ServiceException("未查询到红字确认单信息");
        }
        InvoiceRedConfirmationDto invoiceRedConfirmationDto = invoiceRedConfirmationConvertor.toDto(invoiceRedConfirmationEntity);

        List<InvoiceRedConfirmationItemEntity> itemEntityList = invoiceRedConfirmationItemMapper.getByInvoiceRedConfirmationId(invoiceRedConfirmationId);
        if (CollUtil.isEmpty(itemEntityList)) {
            throw new ServiceException("未查询到红字确认单明细信息");
        }
        List<InvoiceRedConfirmationItemDto> itemDtoList = invoiceRedConfirmationItemConvertor.toDto(itemEntityList);
        List<InvoiceRedConfirmationItemDto> invoiceRedConfirmationItemDtoList = itemDtoList.stream().peek(item -> {
            item.setAmount(item.getAmount().abs());
            item.setPricePlusTaxes(item.getPricePlusTaxes().abs());
            item.setUaxAmount(item.getUaxAmount().abs());
            item.setQuantity(item.getQuantity().abs());
            item.setUnitPrice(item.getUnitPrice().abs());
        }).collect(Collectors.toList());
        invoiceRedConfirmationDto.setInvoiceRedConfirmationItemDtoList(invoiceRedConfirmationItemDtoList);

        associateSubjectInfoDto.setInvoiceRedConfirmationDto(invoiceRedConfirmationDto);

        if (StrUtil.isBlank(invoiceRedConfirmationEntity.getBlueInvoiceNo())) {
            throw new ServiceException("未查询到蓝字发票号");
        }
        List<InvoiceEntity> invoiceEntityList = invoiceMapper.findSaleBlueInvoiceByInvoiceNo(invoiceRedConfirmationEntity.getBlueInvoiceNo());
        if (CollUtil.isEmpty(invoiceEntityList)) {
            throw new ServiceException("未查询到蓝字发票信息");
        }

        List<Integer> relatedIdList = invoiceEntityList.stream().map(InvoiceEntity::getRelatedId).filter(Objects::nonNull).collect(Collectors.toList());
        List<SaleorderInfoDto> saleorderInfoDtoList = saleOrderApiService.getSaleorderInfoBySaleorderIds(relatedIdList);
        if (CollUtil.isEmpty(saleorderInfoDtoList)) {
            log.info("未查询到蓝票关联的销售订单信息");
            return associateSubjectInfoDto;
        }
        Map<Integer, List<AfterSalesDto>> afterSalesMap = new HashMap<>();
        List<AfterSalesDto> afterSalesDtoList = afterSalesApiService.findUnderwayThOrTpByOrderIds(relatedIdList);
        if (CollUtil.isNotEmpty(afterSalesDtoList)) {
            afterSalesMap = afterSalesDtoList.stream().collect(Collectors.groupingBy(AfterSalesDto::getOrderId));
        }
        List<AssociateSubjectInfoDto.AssociateSaleOrderDto> associateSaleOrderDtoList = new ArrayList<>();
        for (SaleorderInfoDto e : saleorderInfoDtoList) {
            AssociateSubjectInfoDto.AssociateSaleOrderDto associateSaleOrderDto = new AssociateSubjectInfoDto.AssociateSaleOrderDto();
            associateSaleOrderDto.setSaleOrderId(e.getSaleorderId());
            associateSaleOrderDto.setSaleOrderNo(e.getSaleorderNo());

            List<AfterSalesDto> afterSalesDtos = afterSalesMap.get(e.getSaleorderId());
            if (CollUtil.isNotEmpty(afterSalesDtos)) {
                List<AssociateSubjectInfoDto.AssociateAfterOrderDto> associateAfterOrderDtoList = afterSalesDtos.stream().map(x -> {
                    AssociateSubjectInfoDto.AssociateAfterOrderDto associateAfterOrderDto = new AssociateSubjectInfoDto.AssociateAfterOrderDto();
                    associateAfterOrderDto.setAfterSalesId(x.getAfterSalesId());
                    associateAfterOrderDto.setAfterSalesNo(x.getAfterSalesNo());
                    return associateAfterOrderDto;
                }).collect(Collectors.toList());
                associateSaleOrderDto.setAssociateAfterOrderDtoList(associateAfterOrderDtoList);
            }
            associateSaleOrderDtoList.add(associateSaleOrderDto);
        }
        associateSubjectInfoDto.setAssociateSaleOrderDtoList(associateSaleOrderDtoList);
        return associateSubjectInfoDto;
    }

    @Override
    public void redLetterConfirmInitCheck(InvoiceRedConfirmationDto initCheckDto) {
        InvoiceDto invoiceDto = invoiceApiService.findbyInvoiceId(initCheckDto.getBlueInvoiceId());
        if (Objects.isNull(invoiceDto)) {
            throw new ServiceException("未查询到发票信息");
        }
        AfterSalesDto afterSalesDto = afterSalesApiService.queryInfoById(initCheckDto.getAfterSaleBusinessOrderId());
        if (Objects.isNull(afterSalesDto)) {
            throw new ServiceException("未查询到售后信息");
        }
        SaleorderInfoDto saleorder = saleOrderApiService.getBySaleOrderId(initCheckDto.getBusinessOrderId());
        if (Objects.isNull(saleorder)) {
            throw new ServiceException("未查询到销售订单信息");
        }

        TraderFinanceDto traderFinance = traderFinanceApiService.selectByTraderIdAndTraderType(saleorder.getTraderId(), 1);
        if (Objects.isNull(traderFinance)) {
            throw new ServiceException("未查询到客户财务信息");
        }

        InvoiceRedConfirmationDto initDto = new InvoiceRedConfirmationDto();
        initDto.setAfterSaleBusinessOrderType(1);
        initDto.setBusinessOrderId(saleorder.getSaleorderId());
        initDto.setBusinessOrderNo(saleorder.getSaleorderNo());
        initDto.setAfterSaleBusinessOrderId(afterSalesDto.getAfterSalesId());
        initDto.setAfterSaleBusinessOrderNo(afterSalesDto.getAfterSalesNo());
        initDto.setBlueInvoiceNo(invoiceDto.getInvoiceNo());
        initDto.setBlueInvoiceId(invoiceDto.getInvoiceId());
        initDto.setOpenInvoiceTime(invoiceDto.getOpenInvoiceTime());
        initDto.setTaxNum(traderFinance.getTaxNum());
        initDto.setRedConfirmationStatus(0);

        this.init(initDto);
    }

    private SaleInvoiceRedConfirmApplyRequestDto assembleApplyParam(InvoiceRedConfirmationDto invoiceRedConfirmationDto) {
        SaleInvoiceRedConfirmApplyRequestDto saleInvoiceRedConfirmApplyRequestDto = new SaleInvoiceRedConfirmApplyRequestDto();
        saleInvoiceRedConfirmApplyRequestDto.setChyyDm(invoiceRedConfirmationDto.getApplyReason());
        saleInvoiceRedConfirmApplyRequestDto.setHzcxje(invoiceRedConfirmationDto.getApplyAmount().toPlainString());
        saleInvoiceRedConfirmApplyRequestDto.setHzcxse(invoiceRedConfirmationDto.getApplyTaxAmount().toPlainString());
        saleInvoiceRedConfirmApplyRequestDto.setHzqrdmxsl(invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList().size());
        saleInvoiceRedConfirmApplyRequestDto.setGmfnsrsbh(invoiceRedConfirmationDto.getBuyerTaxNumber());
        saleInvoiceRedConfirmApplyRequestDto.setLzfpqdhm(invoiceRedConfirmationDto.getBlueInvoiceNo());
        saleInvoiceRedConfirmApplyRequestDto.setLzkprq(DateFormatUtils.format(invoiceRedConfirmationDto.getBlueInvoiceTime(), "yyyy-MM-dd HH:mm:ss"));

        List<InvoiceRedConfirmationItemDto> confirmationItemDtoList = invoiceRedConfirmationDto.getInvoiceRedConfirmationItemDtoList();
        SaleInvoiceBlueInfoRequestDto saleInvoiceBlueInfoRequestDto = new SaleInvoiceBlueInfoRequestDto();
        saleInvoiceBlueInfoRequestDto.setFphm(invoiceRedConfirmationDto.getBlueInvoiceNo());
        SaleInvoiceBlueInfoResponseDto openapi = (SaleInvoiceBlueInfoResponseDto) taxesOpenApiService.openapi(saleInvoiceBlueInfoRequestDto, TaxesInterfaceCodeEnum.BLUE_INFO);
        List<SaleInvoiceBlueInfoResponseDto.Mxzb> mxzbList = openapi.getMxzbList();
        if (CollectionUtils.isEmpty(mxzbList)) {
            log.error("未查到蓝字发票数据,蓝字发票号：{}", invoiceRedConfirmationDto.getBlueInvoiceNo());
            throw new ServiceException("未查到数据");
        }
        Map<String, SaleInvoiceBlueInfoResponseDto.Mxzb> map = mxzbList.stream().collect(Collectors.toMap(SaleInvoiceBlueInfoResponseDto.Mxzb::getXh, Function.identity(), (key1, key2) -> key1));
        List<SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx> collect = confirmationItemDtoList.stream().filter(e -> map.containsKey(e.getSerialNumber().toString())).map(e -> {
            SaleInvoiceBlueInfoResponseDto.Mxzb mxzb = map.get(e.getSerialNumber().toString());
            SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx mx = new SaleInvoiceRedConfirmApplyRequestDto.Hzqrxxmx();
            mx.setLzmxxh(e.getSerialNumber());
            mx.setOldje(mxzb.getJe());
            mx.setOldfpspsl(mxzb.getSpsl());
            mx.setOldfpspdj(mxzb.getDj());
            mx.setTaxfpspdj(mxzb.getDj());
            mx.setTaxje(mxzb.getDj());
            if (SALES_ALLOWANCE.equals(invoiceRedConfirmationDto.getApplyReason())) {
                mx.setFpspdj("");
                mx.setFpspsl("");
            } else {
                // mx.setFpspdj(e.getUnitPrice().toPlainString());
                mx.setFpspdj(mxzb.getDj());
                mx.setFpspsl(e.getQuantity().toPlainString());
            }
            mx.setJe(e.getAmount().toPlainString());
            mx.setSe(e.getUaxAmount().toPlainString());
            return mx;
        }).collect(Collectors.toList());
        saleInvoiceRedConfirmApplyRequestDto.setHzqrxxmxList(collect);
        return saleInvoiceRedConfirmApplyRequestDto;
    }

    private void updateUuid(String uuid, Integer invoiceRedConfirmationId) {
        InvoiceRedConfirmationEntity updateEntity = new InvoiceRedConfirmationEntity();
        updateEntity.setUuid(uuid);
        updateEntity.setInvoiceRedConfirmationId(invoiceRedConfirmationId);
        log.info("提前更新uuid:{},确认单ID:{}", uuid, invoiceRedConfirmationId);
        invoiceRedConfirmationMapper.updateByPrimaryKeySelective(updateEntity);
    }

    private InvoiceRedConfirmationDto toInvoiceRedConfirmationDto(InvoiceRedConfirmationDto initDto, SaleInvoiceRedConfirmInitResponseDto responseDto) {

        InvoiceRedConfirmationDto confirmationDto = new InvoiceRedConfirmationDto();
        confirmationDto.setInvoiceRedConfirmationId(ErpConstant.ZERO);
        confirmationDto.setAddTime(initDto.getAddTime());
        confirmationDto.setModTime(initDto.getModTime());
        confirmationDto.setCreator(initDto.getCreator());
        confirmationDto.setUpdater(initDto.getUpdater());
        confirmationDto.setCreatorName(initDto.getCreatorName());
        confirmationDto.setUpdaterName(initDto.getUpdaterName());
        confirmationDto.setAfterSaleBusinessOrderType(initDto.getAfterSaleBusinessOrderType());
        confirmationDto.setBusinessOrderId(initDto.getBusinessOrderId());
        confirmationDto.setBusinessOrderNo(initDto.getBusinessOrderNo());
        confirmationDto.setAfterSaleBusinessOrderId(initDto.getAfterSaleBusinessOrderId());
        confirmationDto.setAfterSaleBusinessOrderNo(initDto.getAfterSaleBusinessOrderNo());
        confirmationDto.setBlueInvoiceNo(initDto.getBlueInvoiceNo());
        confirmationDto.setBlueInvoiceId(initDto.getBlueInvoiceId());
        confirmationDto.setRedConfirmationStatus(initDto.getRedConfirmationStatus());

        confirmationDto.setSeller(responseDto.getXsfmc());
        confirmationDto.setSellerTaxNumber(responseDto.getXsfnsrsbh());
        confirmationDto.setBuyer(responseDto.getGmfmc());
        confirmationDto.setBuyerTaxNumber(responseDto.getGmfnsrsbh());
        confirmationDto.setInputPersonType(0);
        confirmationDto.setSellerBuyerType(0);
        confirmationDto.setValueAddedTaxStatus(responseDto.getZzsytDm());
        confirmationDto.setConsumptionTaxStatus("");
        confirmationDto.setEnterAccountStatus(responseDto.getFprzztDm());
        confirmationDto.setAlreadyRedInvoiceFlag(responseDto.getGjbq());

        return confirmationDto;
    }


    /**
     * 申请数据校验
     *
     * @param invoiceRedConfirmationId
     * @param buttonName
     * @return
     */
    private RedConfirmResponseDto checkParams(Integer invoiceRedConfirmationId, String buttonName) {
        RedConfirmResponseDto applyResponseDto;

        InvoiceRedConfirmationEntity dbRedConfirmation = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
        if (Objects.isNull(dbRedConfirmation)) {
            applyResponseDto = new RedConfirmResponseDto();
            applyResponseDto.setInvoiceRedConfirmationId(invoiceRedConfirmationId);
            applyResponseDto.setFailReason("未查到该确认单");
            return applyResponseDto;
        }
        List<InvoiceRedConfirmationServiceViewButton> buttons = getButton(invoiceRedConfirmationId).stream().filter(e -> buttonName.equals(e.getButtonName())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(buttons)) {
            return null;
        }
        InvoiceRedConfirmationServiceViewButton invoiceRedConfirmationServiceViewButton = buttons.get(0);
        if (!invoiceRedConfirmationServiceViewButton.isShow()) {
            applyResponseDto = new RedConfirmResponseDto();
            applyResponseDto.setInvoiceRedConfirmationId(invoiceRedConfirmationId);
            InvoiceRedConfirmationStateEnum instanceByCode = InvoiceRedConfirmationStateEnum.getInstanceByCode(dbRedConfirmation.getRedConfirmationStatus());
            if (Objects.isNull(instanceByCode)) {
                return null;
            }
            StringBuilder sb = new StringBuilder();
            sb.append("数据状态为")
                    .append(instanceByCode.getInfo())
                    .append("不可")
                    .append(invoiceRedConfirmationServiceViewButton.getButtonName());
            applyResponseDto.setFailReason(sb.toString());
            return applyResponseDto;
        }
        return null;
    }

    private static RedConfirmResponseDto getRedConfirmReqApplyResponseDto(Integer invoiceRedConfirmationId, String message) {
        RedConfirmResponseDto applyResponseDto = new RedConfirmResponseDto();
        applyResponseDto.setInvoiceRedConfirmationId(invoiceRedConfirmationId);
        applyResponseDto.setFailReason(message);
        return applyResponseDto;
    }

    @Override
    public List<InvoiceRedConfirmationItemDto> getBlueInvoiceDetail(Integer blueInvoiceId) {
        InvoiceDto invoiceDto = invoiceApiService.findbyInvoiceId(blueInvoiceId);
        SaleInvoiceRedConfirmInitRequestDto requestDto = new SaleInvoiceRedConfirmInitRequestDto();
        requestDto.setGmfnsrsbh(taxesConfig.getTaxNo());
        requestDto.setLzfpqdhm(invoiceDto.getInvoiceNo());
        requestDto.setLzkprq(DateUtil.formatDateTime(invoiceDto.getOpenInvoiceTime()));
        SaleInvoiceRedConfirmInitResponseDto initResponseDto = (SaleInvoiceRedConfirmInitResponseDto) taxesOpenApiService.openapi(requestDto, TaxesInterfaceCodeEnum.COMFIRM_INIT);
        log.info("调用SALE.DZSJHZXXBCOMFIRMFP接口返回信息：{}", JSON.toJSONString(initResponseDto));
        if (!initResponseDto.getIsSuccess()) {
            throw new ServiceException(initResponseDto.getReturnMessage());
        }
        return initResponseDto.getHzqrxxmxList().stream().map(item -> this.assembleBlueDetail(item, blueInvoiceId, initResponseDto.getZzsytDm(), initResponseDto.getFprzztDm(), invoiceDto.getRatio())).collect(Collectors.toList());
    }

    /**
     * 根据蓝票初始化接口的返回值结合erp信息组装确认单明细信息
     *
     * @param result        Hzqrxxmx
     * @param blueInvoiceId 蓝票id
     * @param blueUsage     蓝票是否确认用途
     * @param fprzztDm      发票入账状态代码 (00 未入账, 01 已入账)
     * @return InvoiceRedConfirmationItemDto
     */
    private InvoiceRedConfirmationItemDto assembleBlueDetail(SaleInvoiceRedConfirmInitResponseDto.Hzqrxxmx result, Integer blueInvoiceId, String blueUsage, String fprzztDm, BigDecimal ratio) {
        InvoiceRedConfirmationItemDto temp = new InvoiceRedConfirmationItemDto();
        temp.setProjectName(result.getXmmc());
        temp.setSpecifications(result.getGgxh());
        temp.setUnit(result.getDw());
        temp.setTaxCategoryNo(result.getSphfwssflhbbm());
        temp.setQuantity(new BigDecimal(result.getFpspsl()).abs());
        temp.setUaxAmount(new BigDecimal(result.getSe()).abs());
        temp.setAmount(new BigDecimal(result.getJe()).abs());
        temp.setSerialNumber(Integer.valueOf(result.getXh()));
        temp.setUnitPrice(new BigDecimal(result.getFpspdj()).abs());
        // 价税合计=税额+不含税金额
        temp.setPricePlusTaxes(temp.getAmount().add(temp.getUaxAmount()));
        temp.setThisTimeSurplusAmount(temp.getAmount());
        temp.setThisTimeSurplusNumber(temp.getQuantity());
        temp.setThisTimeSurplusUaxAmount(temp.getUaxAmount());
        temp.setThisTimeSurplusPricePlusTaxes(temp.getPricePlusTaxes());
        temp.setTaxRate(ratio);
        temp.setBlueUsage("00".equals(fprzztDm) && "03".equals(blueUsage) ? "03" : NO_CONFIRMATION_REQUIRED);
        temp.setXh(result.getXh());

        Optional<InvoiceApplyDetailDto> applyDetail = Optional.ofNullable(invoiceApplyService.getInvoiceApplyDetailById(Integer.valueOf(result.getXh())));
        applyDetail.ifPresent(detail -> {
            temp.setBusinessOrderItemId(detail.getDetailgoodsId());
            Optional.ofNullable(invoiceApiService.findByInvoiceIdAndDetailGoodsId(blueInvoiceId, detail.getDetailgoodsId()))
                    .ifPresent(invoiceDetailDto -> temp.setBlueInvoiceItemId(invoiceDetailDto.getInvoiceDetailId()));
        });

        log.info("assembleBlueDetail根据蓝票初始化接口组装确认单明细信息：{}", JSON.toJSONString(temp));
        return temp;
    }

    @Override
    public InvoiceRedConfirmationDto getInvoiceRedConfirmationDto(Integer invoiceRedConfirmationId) {
        InvoiceRedConfirmationEntity entity = invoiceRedConfirmationMapper.selectByPrimaryKey(invoiceRedConfirmationId);
        log.info("数据库红字确认单信息:{}", JSON.toJSONString(entity));
        InvoiceRedConfirmationDto invoiceRedConfirmationDto = invoiceRedConfirmationConvertor.toDto(entity);
        if (entity.getBlueInvoiceId() != null) {
            InvoiceEntity invoiceEntity = invoiceMapper.selectByPrimaryKey(entity.getBlueInvoiceId());
            if (invoiceEntity != null) {
                invoiceRedConfirmationDto.setBlueInvoiceType(invoiceEntity.getInvoiceType());
            }
        }

        List<InvoiceRedConfirmationItemEntity> confirmation = invoiceRedConfirmationItemMapper.getByInvoiceRedConfirmationId(invoiceRedConfirmationId);
        if (CollUtil.isNotEmpty(confirmation)) {
            invoiceRedConfirmationDto.setInvoiceRedConfirmationItemDtoList(invoiceRedConfirmationItemConvertor.toDto(confirmation));
        }
        return invoiceRedConfirmationDto;
    }
}
