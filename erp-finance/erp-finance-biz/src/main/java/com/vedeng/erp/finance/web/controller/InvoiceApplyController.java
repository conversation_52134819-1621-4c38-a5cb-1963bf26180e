package com.vedeng.erp.finance.web.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.base.BaseController;
import com.vedeng.common.core.enums.InvoiceApplyCheckRuleEnum;
import com.vedeng.erp.finance.dto.DownloadInvoiceDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.InvoiceTaxTypeEnum;
import com.vedeng.erp.finance.facade.ApplyInvoiceFacade;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.InvoiceTaxSystemRecordService;
import com.vedeng.erp.system.dto.SysOptionDefinitionDto;
import com.vedeng.erp.system.service.SysOptionDefinitionApiService;
import com.vedeng.erp.trader.dto.TraderFinanceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开票申请页面路由
 * @date 2023/11/14 15:30
 */
@RequestMapping("/invoice/invoiceApply")
@Controller
@Slf4j
public class InvoiceApplyController extends BaseController {
    @Autowired
    private InvoiceApplyService invoiceApplyService;
    @Autowired
    private InvoiceTaxSystemRecordService invoiceTaxSystemRecordService;
    @Autowired
    private SysOptionDefinitionApiService sysOptionDefinitionApiService;
    @Autowired
    private ApplyInvoiceFacade applyInvoiceFacade;

    /**
     * 售后开票申请页
     *
     * @param afterSalesId 售后单id
     * @return ModelAndView
     */
    @RequestMapping(value = "/afterSale")
    @ExcludeAuthorization
    public ModelAndView associate(Integer afterSalesId) {
        ModelAndView mv = new ModelAndView("vue/view/finance/invoice_apply_aftersale");
        mv.addObject("afterSalesId", afterSalesId);
        return mv;
    }

    /**
     * 查看开票申请
     */
    @RequestMapping("/invoiceApplyDetail")
    @ExcludeAuthorization
    public ModelAndView invoiceApplyDetail(Integer invoiceApplyId){
        ModelAndView mv = new ModelAndView("vue/view/finance/view_invoice_apply");
        InvoiceApplyDto applyDto = invoiceApplyService.getAfterSaleInvoiceApplyInfo(invoiceApplyId);
        mv.addObject("invoiceApply", JSONUtil.parse(applyDto));
        mv.addObject("invoiceType", InvoiceTaxTypeEnum.getDesc(applyDto.getInvoiceType()));
        return mv;
    }

    /**
     * 开票记录-下载(数电)发票
     */
    @RequestMapping(method = RequestMethod.POST, value = "/downloadFullyDigitalInvoice", produces = MediaType.APPLICATION_JSON_VALUE)
    public ModelAndView downloadFullyDigitalInvoice(@RequestBody DownloadInvoiceDto downloadInvoiceDto){
        ModelAndView mv = new ModelAndView("vue/view/finance/downloadFullyDigitalInvoice");
        mv.addObject("total",downloadInvoiceDto.getTotal());
        Set<String> invoiceNos = downloadInvoiceDto.getInvoiceNos().stream().filter(Objects::nonNull).collect(Collectors.toSet());
        List<String> pdfUrls = invoiceTaxSystemRecordService.getPdfUrlsByInvoiceNos(invoiceNos);
        List<String> xmlUrls = invoiceTaxSystemRecordService.getXmlUrlsByInvoiceNos(invoiceNos);
        //pdf需要将url中的display替换成download
        mv.addObject("pdfUrls",JSONUtil.parse(pdfUrls.stream().map(p -> StrUtil.replace(p,"display","download")).collect(Collectors.toList())));
        mv.addObject("xmlUrls",JSONUtil.parse(xmlUrls));
        return mv;
    }

    /**
     * 开票配置
     */
    @ResponseBody
    @RequestMapping(value = "getInvoiceApplyConfiguration")
    public ModelAndView getInvoiceApplyConfiguration() {
        List<SysOptionDefinitionDto> sysOptionDefinitionDtos = sysOptionDefinitionApiService.getByIds(InvoiceApplyCheckRuleEnum.getCodes());
        ModelAndView modelAndView = new ModelAndView();

        Map<String, Boolean> ruleStatusMap = sysOptionDefinitionDtos.stream()
                .collect(Collectors.toMap(
                        SysOptionDefinitionDto::getTitle,
                        dto -> dto.getStatus().equals(1)
                ));

        Boolean confirmation = ruleStatusMap.getOrDefault(InvoiceApplyCheckRuleEnum.CONFIRMATION_EFFECTIVE_ONLY_LAST.getDescription(), false);
        Boolean after = ruleStatusMap.getOrDefault(InvoiceApplyCheckRuleEnum.AFTER_SALES_ORDER_AUTO_INVOICE.getDescription(), false);
        Boolean sales = ruleStatusMap.getOrDefault(InvoiceApplyCheckRuleEnum.SALES_ORDER_AUTO_INVOICE.getDescription(), false);
        modelAndView.addObject("confirmation", confirmation);
        modelAndView.addObject("after", after);
        modelAndView.addObject("sales", sales);


        modelAndView.setViewName("finance/invoice/list_sale_invoice_apply_configuration");
        return modelAndView;
    }

    /**
     * 客户资质信息缺失提醒
     */
    @RequestMapping(value = "invoiceApplyCheckCustomer")
    @ExcludeAuthorization
    public ModelAndView invoiceApplyCheckCustomer(Integer traderId, Integer invoiceType) {
        ModelAndView modelAndView = new ModelAndView();
        log.info("invoiceApplyCheckCustomer:{}",traderId);
        InvoiceApplyDto invoiceApplyDto = new InvoiceApplyDto();
        invoiceApplyDto.setTraderId(traderId);
        TraderFinanceDto traderFinance = applyInvoiceFacade.getCustomerTraderFinance(traderId);
        // 是否专票
        boolean isSpecialInvoice = InvoiceTaxTypeEnum.getIsSpecialInvoice(invoiceType);
        modelAndView.addObject("traderFinanceDto", traderFinance);
        modelAndView.addObject("isSpecialInvoice", isSpecialInvoice);
        modelAndView.setViewName("finance/invoice/invoice_apply_check_customer");
        return modelAndView;
    }

    @RequestMapping(value = "checkCheckRule")
    @ExcludeAuthorization
    public ModelAndView checkCheckRule(Integer saleorderId) {
        ModelAndView modelAndView = new ModelAndView();
        log.info("checkCheckRule:{}",saleorderId);
        InvoiceCheckResultDto invoiceCheckResultDto = applyInvoiceFacade.checkCheckRule(saleorderId);
        modelAndView.addObject("invoiceCheckResultDto", invoiceCheckResultDto);
        modelAndView.setViewName("finance/invoice/invoice_apply_check_rule");
        return modelAndView;
    }
}
