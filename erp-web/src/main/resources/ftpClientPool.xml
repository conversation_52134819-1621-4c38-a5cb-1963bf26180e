<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans.xsd">

	<beans>
		<!-- ftp连接池配置参数 -->
		<bean id="ftpPoolConfig" class="com.vedeng.ftpclient.config.FtpPoolConfig">
			<property name="host" value="${ftp_url}"/>
			<property name="port" value="${ftp_port}"/>
			<property name="userName" value="${ftp_user}"/>
			<property name="password" value="${ftp_password}"/>
			<property name="connectTimeOut" value="600"/>
			<property name="controlEncoding" value="UTF-8"/>
			<property name="bufferSize" value="1024"/>
			<property name="fileType" value="2"/>
			<property name="dataTimeout" value="0"/>
			<property name="useEPSVWithIPv4" value="false"/>
			<property name="passiveMode" value="true"/>
			<!--空闲链接数目 与FTP服务器设置的单个IP最大链接数有关-->
			<property name="maxIdle" value="3"/>
			<!--最大链接数目 与FTP服务器设置的单个IP最大链接数有关-->
			<property name="maxTotal" value="8"/>
			<!--连接池满的时候超时时间 当池满的时候新的链接阻塞多少ms后超时-->
			<property name="maxWaitMillis" value="8000"/>
			<!--每多少ms运行一次空闲连接回收器 -->
			<property name="timeBetweenEvictionRunsMillis" value="180000"/>
			<!--池中的连接空闲多少ms后被回收-->
			<property name="minEvictableIdleTimeMillis" value="360000"/>
			<!--指明连接是否被空闲连接回收器(如果有)进行检验.
			如果检测失败[检测代码是FTPClientFactory.validateObject(),及发送一个命令去FTP服务器验证是否有效],
			则连接将被从池中去除 -->
			<property name="testWhileIdle" value="true"/>
		</bean>

		<!-- ftp连接池对象丢弃策略配置参数 -->
		<bean id="ftpAbandonedConfig" class="com.vedeng.ftpclient.config.FTPAbandonedConfig">
			<!--当前ftp 连接池内正在使用的链接是否需要被检测回收-->
			<property name="removeAbandonedOnMaintenance" value="true"/>
			<!--当前ftp 连接池内正在使用的链接占用多少时间后被回收-->
			<property name="removeAbandonedTimeout" value="600"/>
		</bean>

		<!-- ftp客户端工厂 -->
		<bean id="ftpClientFactory" class="com.vedeng.ftpclient.core.FTPClientFactory">
			<property name="ftpPoolConfig" ref="ftpPoolConfig"></property>
			<property name="ftpAbandonedConfig" ref="ftpAbandonedConfig"></property>
		</bean>
		<!-- ftp客户端连接池对象 -->
		<bean id="ftpClientPool" class="com.vedeng.ftpclient.core.FTPClientPool" init-method="initPool">
			<constructor-arg index="0" ref="ftpClientFactory"></constructor-arg>
		</bean>
		<!-- ftp客户端辅助bean-->
		<bean id="ftpClientHelper" class="com.vedeng.ftpclient.client.FTPClientHelper">
			<property name="ftpClientPool"  ref="ftpClientPool"/>
		</bean>
	</beans>


</beans>
