package com.vedeng.erp.buyorder.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 采购返利结算收款申请 excel上传对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class BuyOrderRebateChargeApplyDetailExcelDto {

    /**
     * 订单号
     */
    @ExcelProperty("订单号")
    private String orderNo;

    /**
     * 单据类型
     */
    @ExcelProperty("单据类型")
    private String orderType;

    /**
     * 售后单号
     */
    @ExcelProperty("售后单号")
    private String afterOrderNo;

    /**
     * 订单状态
     */
    @ExcelProperty("订单状态")
    private String orderStatus;

    /**
     * 创建时间
     */
    @ExcelProperty("创建时间")
    private String addTime;

    /**
     * 生效时间
     */
    @ExcelProperty("生效时间")
    private String validTime;

    /**
     * 供应商名称
     */
    @ExcelProperty("供应商名称")
    private String supplierName;

    /**
     * 供应商ID
     */
    @ExcelProperty("供应商ID")
    private String supplierId;

    /**
     * SKU
     */
    @ExcelProperty("SKU")
    private String sku;

    /**
     * 商品名称
     */
    @ExcelProperty("商品名称")
    private String skuName;

    /**
     * 品牌/型号
     */
    @ExcelProperty("品牌/型号")
    private String brandAndModel;

    /**
     * 规格
     */
    @ExcelProperty("规格")
    private String spec;

    /**
     * 计量单位
     */
    @ExcelProperty("计量单位")
    private String unitName;

    /**
     * 是否直发（商品）
     */
    @ExcelProperty("是否直发（商品）")
    private String deliveryDirect;

    /**
     * 到货时间
     */
    @ExcelProperty("到货时间")
    private String arrivalTime;

    /**
     * 按月到货采购数量
     */
    @ExcelProperty("按月到货采购数量")
    private String purchaseNum;

    /**
     * 采购单价（含税）
     */
    @ExcelProperty("采购单价（含税）")
    private String purchasePrice;

    /**
     * 采购总额（含税）
     */
    @ExcelProperty("采购总额（含税）")
    private String purchaseTotalAmount;

    /**
     * 采购单价（不含税）
     */
    @ExcelProperty("采购单价（不含税）")
    private String purchasePriceWithoutTax;

    /**
     * 采购总额（不含税）
     */
    @ExcelProperty("采购总额（不含税）")
    private String purchaseTotalAmountWithoutTax;

    /**
     * 一级分类
     */
    @ExcelProperty("一级分类")
    private String firstCategory;

    /**
     * 二级分类
     */
    @ExcelProperty("二级分类")
    private String secondCategory;

    /**
     * 三级分类
     */
    @ExcelProperty("三级分类")
    private String thirdCategory;
}