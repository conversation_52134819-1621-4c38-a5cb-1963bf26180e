<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>CRM-拜访计划</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>

    <jsp:include page="../common/head_import.jsp?staticResourceVersion=${requestScope.staticResourceVersion}"></jsp:include>

    <div class="page-wrap" id="page-container" style="padding-bottom: 40px;">
        <page-header></page-header>
        <div class="page-container">
            <div class="page-main">
                <div class="visitor-form-container detail-page-layout" >
                    <div class="top-featurn" :style="{'top': fixedTop+'px'}">
                        <div class="inner-header">
                            <h1 class="inner-header-title">{{ id? '编辑拜访计划': '新建拜访计划' }}</h1>
                            <ui-button @click="submit" type="primary">提交</ui-button>
                        </div>
                    </div>
                    <div class="form-wrap" style="padding: 10px 20px 20px 20px;" v-if="!pageLoading">
                        <div class="detail-top-card" v-if="id">
                            <div class="detail-top-item">
                                <div class="item-label">计划编号</div>
                                <div class="item-txt">{{ visitRecordNo }}</div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">计划拜访时间</div>
                                <div class="item-txt">{{ planVisitDate }}</div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">拜访完成时间</div>
                                <div class="item-txt">-</div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">拜访人</div>
                                <div class="item-txt">
                                    <ui-user :name="visitorName" :avatar="visitorPic"></ui-user>
                                </div>
                            </div>
                            <div class="detail-top-item">
                                <div class="item-label">创建时间</div>
                                <div class="item-txt">{{ addTime || '-' }}</div>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-title">拜访客户信息</div>
                            <div>
                                <ui-form-item label="客户名称" :must="true">
                                    <div class="ui-col-12">
                                        <ui-tyc
                                            width="490px"
                                            maxlength="100"
                                            v-model="traderName"
                                            :need-tyc="true"
                                            :tyc-flag="tycFlag"
                                            :sale-name="traderId ? (belonger || '') : ''"
                                            @change="handlerTrader"
                                            valid="AddVisitRecord_traderName"
                                            :need-self="true"
                                            placeholder="请准确输入客户公司名称"
                                            :share="isShare"
                                        ></ui-tyc> 
                                        <div class="trader-tip-info" v-if="traderInfo">
                                            <div class="trader-info-item">总交易额：{{ traderInfo.historyTransactionAmount || '-' }}</div>
                                            <div class="trader-info-item">交易次数：{{ traderInfo.historyTransactionNum || '-' }}</div>
                                            <div class="trader-info-item">最近下单：{{ traderInfo.lastOrderTime || '-' }}</div>
                                        </div>
                                    </div>
                                </ui-form-item>
                                <template v-if="traderInfo">
                                    <ui-form-item label="客户等级" :text="true">
                                        <template v-if="traderInfo.customerGrade">
                                            <ui-trader-grade :grade="traderInfo.customerGrade"></ui-trader-grade>
                                        </template>
                                        <template v-else>-</template>
                                    </ui-form-item>
                                </template>
                                <ui-form-item label="客户类型" :must="true" :text="true">
                                    <div class="ui-col-12">
                                        <ui-radio-group
                                            :list="traderTypeList"
                                            :value.sync="traderType"
                                            :disabled="!!traderInfo"
                                            valid="AddVisitRecord_traderType"
                                            @change="traderTypeChange"
                                        ></ui-radio-group>
                                    </div>
                                </ui-form-item>
                                <template v-if="!noContract">
                                    <ui-form-item label="手机">
                                        <ui-phone-related
                                            width="323px"
                                            placeholder="已建档客户，可输入手机号搜索"
                                            v-model="mobile"
                                            :trader-id="traderId"
                                            :accurate-match="false"
                                            @change="handlerPhone"
                                            valid="AddVisitRecord_mobile"
                                            :novalid="true"
                                        ></ui-phone-related>
                                        <div class="form-status-tip tip-warn" v-if="mobileStatus == 2">
                                            <i class="vd-ui_icon icon-caution1"></i>
                                            <div class="tip-txt">该手机号未注册贝登商城</div>
                                        </div>
                                        <div class="form-status-tip tip-success" v-if="mobileStatus == 1">
                                            <i class="vd-ui_icon icon-yes1"></i>
                                            <div class="tip-txt">该手机号已注册贝登商城</div>
                                        </div>
                                    </ui-form-item>
                                    <ui-form-item label="固话">
                                        <ui-input 
                                            type="number" width="323px" placeholder="需加上区号，可支持20位数字"
                                            v-model="telephone" @blur="telephone_Blur" maxlength="20"
                                        ></ui-input>
                                    </ui-form-item>
                                    <ui-more-contact
                                        label="其他联系方式"
                                        width="323px"
                                        placeholder="请输入联系方式名称"
                                        v-model="otherContactInfo"
                                        @change="handlerMoreContact"
                                    ></ui-more-contact>
                                    <ui-form-item label="联系人" :must="true">
                                        <ui-input 
                                            width="323px" 
                                            placeholder="最多支持20个字"
                                            v-model="traderContactName"
                                             maxlength="20" 
                                            @change="handlerContact"
                                            valid="AddVisitRecord_traderContactName"
                                        ></ui-input>
                                    </ui-form-item>
                                    <template v-if="traderType && positionList.length">
                                        <ui-form-item label="职位" :must="true">
                                            <ui-select 
                                                :data="positionList"  
                                                placeholder="请选择" 
                                                v-model="contactPosition"
                                                valid="AddVisitRecord_contactPosition"
                                            ></ui-select>
                                        </ui-form-item>
                                    </template>
                                </template>
                                <ui-form-item>
                                    <ui-checkbox label="暂无联系人信息" :checked.sync="noContract"></ui-checkbox>
                                </ui-form-item>
                                <ui-form-item label="拜访地区" :must="true">
                                    <ui-cascader
                                        width="323px"
                                        class="margin"
                                        :data="addressData"
                                        v-model="area"
                                        clearable
                                        filterable
                                        :need-trigger="true"
                                        @change="handleArea"
                                        valid="AddVisitRecord_areaInfo"
                                    ></ui-cascader>
                                </ui-form-item>
                                <ui-form-item label="详细地址">
                                    <div class="map-address-wrap">
                                        <ui-input
                                            type="textarea"
                                            maxlength="100"
                                            placeholder="请输入拜访打卡的详细地址，可”查看地图“确定路线与移动端导航"
                                            v-model="addressDetail"
                                            height-auto
                                            height="30px"
                                            width="780px"
                                            resize="none"
                                        ></ui-input>
                                        <ui-view-map :address="mapAddress"></ui-view-map>
                                    </div>
                                </ui-form-item>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-title">计划信息</div>
                            <div>
                                <ui-form-item label="拜访人" :must="true">
                                    <ui-select 
                                        :remote="true" 
                                        :avatar="true"  
                                        placeholder="请选择" 
                                        v-model="visitor" 
                                        :remote-info="userRemoteInfo" 
                                        valid="AddVisitRecord_visitor" 
                                        @change="handlerVisitorChange"
                                        :default-label="visitorName"
                                    ></ui-select>
                                </ui-form-item>
                                <ui-form-item label="同行人">
                                    <ui-select  
                                        multiple-type="auto" 
                                        :remote="true" 
                                        :avatar="true"  
                                        placeholder="请选择" 
                                        v-model="tongxing" 
                                        clearable 
                                        :remote-info="userRemoteInfo"
                                        @change="handlerTongxingChange"
                                        valid="AddVisitRecord_tongxing"
                                        :default-multi="defaultTongxingItems"
                                    ></ui-select>
                                </ui-form-item>
                                <ui-form-item label="计划拜访时间" :must="true">
                                    <ui-date-picker
                                        type="date"
                                        width="350px"
                                        placeholder=""
                                        v-model="planVisitDate"
                                        valid="AddVisitRecord_planVisitDate"
                                    ></ui-date-picker>
                                </ui-form-item>
                                <ui-form-item label="拜访目标" :text="true" :must="true">
                                    <ui-checkbox-group
                                        :list="visitTypeList"
                                        :values.sync="visitType"
                                        valid="AddVisitRecord_visitType"
                                    ></ui-checkbox-group>
                                </ui-form-item>
                                <template v-if="visitType.indexOf('B') !== -1">
                                    <ui-form-item label="线索/商机编号" :must="true">
                                        <ui-input 
                                            width="323px" 
                                            v-model="businessNo"
                                            @blur="checkBusinessNo"
                                            valid="AddVisitRecord_businessNo"
                                            maxlength="20"
                                            :errorable="!!businessNoMsg"
                                            :error-msg="businessNoMsg"
                                            placeholder="请输入已创建的线索/商机编号"
                                            :disabled="!!(bizId && businessNo)"
                                            :in-disabled-valid="true"
                                        ></ui-input>
                                        <div class="business-info-wrap" v-if="businessInfo.bizNo">
                                            <div class="info-item">单据类型：{{ businessInfo.relateType == 1 ? '线索' : '商机' }}</div>
                                            <div class="info-item">客户名称：{{ businessInfo.customerName || '-' }}</div>
                                            <div class="info-item">归属销售：{{ businessInfo.salesName || '-' }}</div>
                                            <div class="business-info-tip" v-if="businessNoTip">
                                                <i class="vd-ui_icon icon-caution1"></i>
                                                <div class="tip-txt">{{ businessNoTip }}</div>
                                            </div>
                                        </div>
                                    </ui-form-item>
                                </template>
                                <ui-form-item label="备注">
                                    <ui-input
                                        type="textarea"
                                        maxlength="1000"
                                        placeholder="描述客户当前需求或痛点、以及本次拜访期望解决的具体问题"
                                        show-word-limit
                                        v-model="remark"
                                        height-auto
                                        height="30px"
                                        width="780px"
                                    ></ui-input>
                                </ui-form-item>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/visitRecord/edit.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
