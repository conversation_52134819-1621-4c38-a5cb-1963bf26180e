/*
package com.rabbitmq;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.wms.inventorytransfer.model.dto.InventoryTransferRequest;
import com.wms.inventorytransfer.model.dto.InventoryTransferRespon;
import com.wms.inventorytransfer.service.InventoryTransferService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;

*/
/**
 * ERP消费者
 *
 * <AUTHOR>
 * @date 2020/7/21 11:24:15
 *//*

@Component
public class MsgReceiver {
    @Autowired
    private InventoryTransferService inventoryTransferService;

    @Value("${stock_url}")
    protected String stockUrl;

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    */
/**
     * 消息获取库存转移信息
     *
     * @param content
     *//*

    @RabbitListener(queues = RabbitConfig.INVENTORY_TRANSFER_QUEUE)
    public void getInventoryTransferInfo(String content) {
        logger.info("开始处理活动检索信息:{}" + content);
        try {
            //检索活动相关库存转移单信息
            InventoryTransferRespon inventoryTransferRespon = JSON.parseObject(content, InventoryTransferRespon.class);
            inventoryTransferRespon.setInventoryTransferDtos(inventoryTransferService.getInventoryTransferInfoByActionId(inventoryTransferRespon.getActionId()));
            HashMap<String, String> params = new HashMap<>(1);
            params.put("inventoryTransferRespon", JsonUtils.translateToJson(inventoryTransferRespon));
            NewHttpClientUtils.doPost(stockUrl + HttpURLConstant.STOCK_POST_INVENTORY_TRANSFER, params);
        } catch (Exception e) {
            logger.error("/promotion/action/InventoryTransfer", e);
        }
    }

    */
/**
     * 保存库存转移单信息
     *
     * @param content
     *//*

    @RabbitListener(queues = RabbitConfig.ACTION_CHANGE_MSG_QUEUE)
    public void saveInventoryTransfer(String content) {
        try {
            logger.info("开始保存相关库存转移单信息{}" + content);
            InventoryTransferRequest inventoryTransferRequest = JSON.parseObject(content, InventoryTransferRequest.class);
            inventoryTransferService.saveInventoryTransferRequest(inventoryTransferRequest);
        } catch (Exception e) {
            logger.error("ActionChangeMsgQueue", e);
        }
    }

}
*/
