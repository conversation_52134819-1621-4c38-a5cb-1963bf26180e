package com.vedeng.common.constant;

/**
 * <AUTHOR>
 */

public enum SpecialDeliveryEnum {

    NONE_SPECIAL_DELIVERY(0, "非专项发货"),
    NEED_SPECIAL_DELIVERY(1, "专项发货");

    private Integer code;

    private String describe;

    SpecialDeliveryEnum(Integer code, String describe) {
        this.code = code;
        this.describe = describe;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }
}
