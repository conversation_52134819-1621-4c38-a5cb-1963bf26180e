package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.service.KingDeeMaterialApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 基础物料补偿写入
 */
@Service
@Slf4j
public class BatchMaterialCompensateWriter extends BaseWriter<KingDeeMaterialDto> {

    @Autowired
    private KingDeeMaterialApiService kingDeeMaterialApiService;

    @Override
    public void doWrite(KingDeeMaterialDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("基础物料补偿写入，item:{}", JSON.toJSONString(item));
        kingDeeMaterialApiService.register(item, true);
    }
}