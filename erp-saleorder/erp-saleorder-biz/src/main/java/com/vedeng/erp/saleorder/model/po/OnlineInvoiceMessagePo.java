package com.vedeng.erp.saleorder.model.po;

import lombok.Data;

/**
 * 在线开票消息实体类
 *
 * <AUTHOR>
 */
@Data
public class OnlineInvoiceMessagePo {

    /**
     * ID
     */
    private Integer id;

    /**
     * 队列消息ID
     */
    private String messageId;

    /**
     * 业务类型的key 1.在线签收 2.订单开票
     */
    private Integer businessKey;

    /**
     * 消费的结果 0:失败 1:成功 2:消息错误
     */
    private Integer consumeResult;

    /**
     * 重试次数
     */
    private Integer consumeTimes;

    /**
     * 报文内容
     */
    private String messageBody;

    /**
     * 添加时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
