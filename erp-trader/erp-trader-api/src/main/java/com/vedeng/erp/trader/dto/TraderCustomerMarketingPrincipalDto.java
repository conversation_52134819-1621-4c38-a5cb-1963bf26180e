package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 客户营销-主营属性表
 * @date 2023/8/7 10:17
 */
@Getter
@Setter
public class TraderCustomerMarketingPrincipalDto extends BaseDto {


    /**
     * 主键
     */
    private Integer traderCustomerMarketingPrincipalId;

    /**
     * 交易者id
     */
    private Integer traderId;

    /**
     * 客户id
     */
    private Integer traderCustomerId;

    /**
     * 商品类型 0 设备、1 高值耗材、2 中低值耗材、3 试剂、4 软件；
     */
    private String skuType;

    /**
     * 商品类型 拼接展示
     */
    private String skuTypeName;

    /**
     * 商品范畴 0 综合、1 专业
     */
    private String skuScope;

    /**
     * 商品范畴 拼接展示
     */
    private String skuScopeName;

    /**
     * 商品分类
     */
    private String skuCategory;

    /**
     * 商品分类名称集合
     */
    private List<String> skuCategoryNameList = new ArrayList<>();

    /**
     * 销售类别 0 直销为主、1 分销为主、2 直分销并重；
     */
    private String salesType;

    /**
     * 销售类别 拼接展示
     */
    private String salesTypeName;

    /**
     * 代理品牌
     */
    private String agencyBrand;

    /**
     * 代理商品
     */
    private String agencySku;

    /**
     * 其他代理商品
     */
    private String otherAgencySku;

    /**
     * 代理权- 代理品牌+其他代理品牌 拼接展示
     */
    private String agencyBrandName;

    /**
     * 代理权- 代理商品+其他代理商品 拼接展示
     */
    private String agencySkuName;

    /**
     * 政府关系 0 卫健委、1 医联体、2 其他
     */
    private String governmentRelation;

    /**
     * 政府关系- 政府关系+其他政府关系 拼接展示
     */
    private String governmentRelationName;

    /**
     * 其他代理品牌
     */
    private String otherAgencyBrand;

    /**
     * 客户有效性 1 有效 0 无效
     */
    private Integer effectiveness;

    /**
     * 其他政府关系
     */
    private String otherGovernmentRelation;

    /**
     * 客户是否为产品型渠道客户 1 是 0 否
     */
    private Integer isProductCustomer;

    /**
     * 客户是否为客户型渠道客户 1 是 0 否
     */
    private Integer isChannelCustomer;
}