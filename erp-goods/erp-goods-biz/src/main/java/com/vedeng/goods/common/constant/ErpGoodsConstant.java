package com.vedeng.goods.common.constant;

/**
 * <AUTHOR>
 * @create 2022/1/11 17:46
 */
public class ErpGoodsConstant {


    /**
     *设备
     */
    public static final Integer SPU_TYPE_DEVICE = 316;

    /**
     *配件
     */
    public static final Integer SPU_TYPE_REPLACEMENT = 1008;


    /**
     * 常用不变域名地址
     */
    public static class LinkUrls {

        public static final String BASE_INFO_PREFIX_URL = "mobile/sku/queryDetail";
        public static final String NODE_BASE_INFO_PREFIX_URL = "erp/basicInfo";
        public static final String DOC_DOWNLOAD_PREFIX_URL = "mobile/doc/queryDetail";


    }

    /**
     * 没有实际意义，循环使用
     */
    public static class CommonNumbers {

        public static final int ZERO = 0;
        public static final int ONE = 1;
        public static final int TWO = 2;
        public static final int THREE = 3;
    }

    /**
     * SKU上下架状态 1-上架 0-下架
     */
    public static class OnSaleStatus {

        public static final int ON = 1;
        public static final int OFF = 0;

    }

    /**
     * SKU推送状态 1-推送 0-未推送
     */
    public static class PushStatus {

        public static final int ON = 1;
        public static final int OFF = 0;

    }

    public static final Integer NUMBER_ONE = Integer.valueOf(1);


    /**
     * 品牌(NEW)附件信息
     */
    public static final Integer ATTACHMENT_TYPE_987 = 987;

    /**
     * 授权证明
     */
    public static final Integer ATTACHMENT_FUNCTION_988 = 988;

    /**
     * 首营附件信息模块
     */
    public static final Integer ATTACHMENT_TYPE_974 = 974;

    /**
     * 注册证附件/备案凭证附件
     */
    public static final Integer ATTACHMENT_FUNCTION_975 = 975;

    /**
     * 说明书
     */
    public static final Integer ATTACHMENT_FUNCTION_976 = 976;

    /**
     * 生产企业卫生许可证
     */
    public static final Integer ATTACHMENT_FUNCTION_977 = 977;

    /**
     * 报备
     */
    public static final String IS_NEED_REPORT_TRUE = "报备";

    /**
     * 不报备
     */
    public static final String IS_NEED_REPORT_FALSE = "不报备";


}
