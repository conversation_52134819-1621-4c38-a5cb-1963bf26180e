package com.vedeng.erp.finance.manager.action;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.domain.context.InvoiceRedConfirmationContext;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationStateEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceService;
import com.vedeng.erp.finance.manager.InvoiceRedConfirmationAction;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import com.vedeng.erp.finance.service.InvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 开票
 * @date 2023/10/17 11:15
 */
@Component
@Slf4j
public class OpenInvoiceAction implements InvoiceRedConfirmationAction {

    @Autowired
    InvoiceRedConfirmationService invoiceRedConfirmationService;

    @Autowired
    FullyDigitalInvoiceService fullyDigitalInvoiceService;

    @Autowired
    InvoiceService invoiceService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void execute(InvoiceRedConfirmationStateEnum from, InvoiceRedConfirmationStateEnum to, InvoiceRedConfirmationEvent event, InvoiceRedConfirmationContext context) {
        // 1. 更新确认单信息
        log.info("OpenInvoiceAction入参{}", JSON.toJSONString(context));
        InvoiceRedConfirmationDto invoiceRedConfirmationDto = context.getInvoiceRedConfirmationDto();
        // 保证子表明细插入
        invoiceRedConfirmationService.modify(invoiceRedConfirmationDto);

        // 2. 执行售后开红票逻辑
        fullyDigitalInvoiceService.openRedInvoice(invoiceRedConfirmationDto);
        InvoiceDto redInvoice = invoiceService.getRedInvoice(invoiceRedConfirmationDto.getRedInvoiceNo());
        if (redInvoice == null) {
            throw new ServiceException("红字发票未查询到，开具失败");
        }
        invoiceRedConfirmationDto.setRedInvoiceId(redInvoice.getInvoiceId());
        // 更新确认单红票信息
        invoiceRedConfirmationService.modify(invoiceRedConfirmationDto);
    }

}
