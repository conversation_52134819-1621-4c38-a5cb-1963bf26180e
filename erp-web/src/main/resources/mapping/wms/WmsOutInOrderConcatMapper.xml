<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.wms.dao.WmsOutInOrderConcatMapper" >
  <resultMap id="BaseResultMap" type="com.wms.model.po.WmsOutInOrderConcat" >
    <!--          -->
    <id column="OUT_IN_ORDER_CONCAT_ID" property="outInOrderConcatId" jdbcType="INTEGER" />
    <result column="OUT_ORDER_NO" property="outOrderNo" jdbcType="VARCHAR" />
    <result column="OUT_ORDER_ID" property="outOrderId" jdbcType="INTEGER" />
    <result column="OUT_ORDER_TYPE" property="outOrderType" jdbcType="INTEGER" />
    <result column="IN_ORDER_NO" property="inOrderNo" jdbcType="VARCHAR" />
    <result column="IN_ORDER_ID" property="inOrderId" jdbcType="INTEGER" />
    <result column="IN_ORDER_TYPE" property="inOrderType" jdbcType="INTEGER" />
    <result column="IS_DELETE" property="isDelete" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    OUT_IN_ORDER_CONCAT_ID, OUT_ORDER_NO, OUT_ORDER_ID, OUT_ORDER_TYPE, IN_ORDER_NO, 
    IN_ORDER_ID, IN_ORDER_TYPE, IS_DELETE, ADD_TIME, UPDATE_TIME, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_OUT_IN_ORDER_CONCAT
    where OUT_IN_ORDER_CONCAT_ID = #{outInOrderConcatId,jdbcType=INTEGER}
  </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_WMS_OUT_IN_ORDER_CONCAT
    where OUT_IN_ORDER_CONCAT_ID = #{outInOrderConcatId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.wms.model.po.WmsOutInOrderConcat" >
    <!--          -->
    insert into T_WMS_OUT_IN_ORDER_CONCAT (OUT_IN_ORDER_CONCAT_ID, OUT_ORDER_NO, OUT_ORDER_ID, 
      OUT_ORDER_TYPE, IN_ORDER_NO, IN_ORDER_ID, 
      IN_ORDER_TYPE, IS_DELETE, ADD_TIME, 
      UPDATE_TIME, CREATOR, UPDATER
      )
    values (#{outInOrderConcatId,jdbcType=INTEGER}, #{outOrderNo,jdbcType=VARCHAR}, #{outOrderId,jdbcType=INTEGER}, 
      #{outOrderType,jdbcType=INTEGER}, #{inOrderNo,jdbcType=VARCHAR}, #{inOrderId,jdbcType=INTEGER}, 
      #{inOrderType,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wms.model.po.WmsOutInOrderConcat" >
    <!--          -->
    insert into T_WMS_OUT_IN_ORDER_CONCAT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="outInOrderConcatId != null" >
        OUT_IN_ORDER_CONCAT_ID,
      </if>
      <if test="outOrderNo != null" >
        OUT_ORDER_NO,
      </if>
      <if test="outOrderId != null" >
        OUT_ORDER_ID,
      </if>
      <if test="outOrderType != null" >
        OUT_ORDER_TYPE,
      </if>
      <if test="inOrderNo != null" >
        IN_ORDER_NO,
      </if>
      <if test="inOrderId != null" >
        IN_ORDER_ID,
      </if>
      <if test="inOrderType != null" >
        IN_ORDER_TYPE,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="outInOrderConcatId != null" >
        #{outInOrderConcatId,jdbcType=INTEGER},
      </if>
      <if test="outOrderNo != null" >
        #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderId != null" >
        #{outOrderId,jdbcType=INTEGER},
      </if>
      <if test="outOrderType != null" >
        #{outOrderType,jdbcType=INTEGER},
      </if>
      <if test="inOrderNo != null" >
        #{inOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="inOrderId != null" >
        #{inOrderId,jdbcType=INTEGER},
      </if>
      <if test="inOrderType != null" >
        #{inOrderType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.wms.model.po.WmsOutInOrderConcat" >
    <!--          -->
    update T_WMS_OUT_IN_ORDER_CONCAT
    <set >
      <if test="outOrderNo != null" >
        OUT_ORDER_NO = #{outOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="outOrderId != null" >
        OUT_ORDER_ID = #{outOrderId,jdbcType=INTEGER},
      </if>
      <if test="outOrderType != null" >
        OUT_ORDER_TYPE = #{outOrderType,jdbcType=INTEGER},
      </if>
      <if test="inOrderNo != null" >
        IN_ORDER_NO = #{inOrderNo,jdbcType=VARCHAR},
      </if>
      <if test="inOrderId != null" >
        IN_ORDER_ID = #{inOrderId,jdbcType=INTEGER},
      </if>
      <if test="inOrderType != null" >
        IN_ORDER_TYPE = #{inOrderType,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null" >
        UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where OUT_IN_ORDER_CONCAT_ID = #{outInOrderConcatId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wms.model.po.WmsOutInOrderConcat" >
    <!--          -->
    update T_WMS_OUT_IN_ORDER_CONCAT
    set OUT_ORDER_NO = #{outOrderNo,jdbcType=VARCHAR},
      OUT_ORDER_ID = #{outOrderId,jdbcType=INTEGER},
      OUT_ORDER_TYPE = #{outOrderType,jdbcType=INTEGER},
      IN_ORDER_NO = #{inOrderNo,jdbcType=VARCHAR},
      IN_ORDER_ID = #{inOrderId,jdbcType=INTEGER},
      IN_ORDER_TYPE = #{inOrderType,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where OUT_IN_ORDER_CONCAT_ID = #{outInOrderConcatId,jdbcType=INTEGER}
  </update>
  <select id="getOutInOrderConcatInfo" resultType="com.wms.model.po.WmsOutInOrderConcat">
    SELECT
    <include refid="Base_Column_List" />
    FROM
	T_WMS_OUT_IN_ORDER_CONCAT
    WHERE
     OUT_ORDER_NO = #{outOrderNo,jdbcType=VARCHAR} AND
      OUT_ORDER_ID = #{outOrderId,jdbcType=INTEGER} AND
      OUT_ORDER_TYPE = #{outOrderType,jdbcType=INTEGER} AND
      IN_ORDER_NO = #{inOrderNo,jdbcType=VARCHAR} AND
      IN_ORDER_ID = #{inOrderId,jdbcType=INTEGER} AND
      IN_ORDER_TYPE = #{inOrderType,jdbcType=INTEGER}
  </select>

</mapper>