<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="交易记录" scope="application" />	
<%@ include file="../../common/common.jsp"%>
<%@ include file="./customer_tag.jsp"%>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/customer/bussinesss.js?rnd=${resourceVersionKey}'></script>
	<div class="searchfunc" style="padding-top:0px;">
		<form method="post" id="search" action="<%= basePath %>/trader/customer/businesslist.do">
			<ul>
				<li>
					<label class="infor_name">产品名称</label>
					<input type="text" class="input-middle" name="goodsName" id="goodsName" value="${saleorderGoodsVo.goodsName}"/>
				</li>
				<li>
					<label class="infor_name">品牌</label>
					<input type="text" class="input-middle" name="brandName" id="brandName" value="${saleorderGoodsVo.brandName}"/>
				</li>
				<li>
					<label class="infor_name">型号</label>
					<input type="text" class="input-middle" name="model" id="model" value="${saleorderGoodsVo.model}"/>
				</li>
				<li>
					<label class="infor_name">订货号</label>
					<input type="text" class="input-middle" name="sku" id="sku" value="${saleorderGoodsVo.sku}"/>
				</li>
				<li>
					<label class="infor_name">终端客户</label>
					<input type="text" class="input-middle" name="terminalTraderName" id="terminalTraderName" value="${saleorderGoodsVo.terminalTraderName}"/>
				</li>
				<li>
					<label class="infor_name">单号</label>
					<input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${saleorderGoodsVo.saleorderNo}"/>
				</li>
				<li>
					<label class="infor_name">时间</label>
					<input class="Wdate f_left input-smaller96 m0" type="text"
					placeholder="请选择日期"
					onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})" autocomplete="off"
					name="starttime" id="starttime"
					value="${saleorderGoodsVo.starttime }">
					<div class="f_left ml1 mr1 mt4">-</div> <input
					class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期"
					onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})" autocomplete="off"
					name="endtime" id="endtime" value="${saleorderGoodsVo.endtime }">
				</li>
			</ul>
			<div class="tcenter">
				<input type="hidden" name="traderId" value="${saleorderGoodsVo.traderId }" >
				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="searchBussiness();" id="searchSpan">搜索</span>
				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="repurchase();">一键复购</span>
				<%--<c:if test="${not empty uploadEnable}">
				<span class="bt-small bg-light-blue bt-bg-style mr20" style="float:right;" onclick="uploadJdOrder();">批量制单</span>
				</c:if>--%>
				<input class="" id="saleOrderGoodsIds" value="" style="display: none"/>
				<input class="" id="saleOrderIds" value="" style="display: none"/>
				<input class="" id="traderId" value="${traderCustomer.traderId}" style="display: none"/>
				<input class="" id="traderCustomerId" value="${traderCustomer.traderCustomerId}" style="display: none"/>
				<input class="" id="customerNature" value="${traderCustomer.customerNature}" style="display: none"/>
			</div>
		</form>
	</div>
	<div class="content" style="height: 100%">
		<iframe src='./searchBusinessList.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}&customerNature=${traderCustomer.customerNature}' style="height: 100%;width: 100%" id="bussiness-info-div"></iframe>
	</div>
</body>

</html>
