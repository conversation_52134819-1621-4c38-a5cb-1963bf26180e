package com.vedeng.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.rabbitmq.ImMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.authorization.dao.*;
import com.vedeng.authorization.model.*;
import com.vedeng.authorization.model.vo.UserQueryVo;
import com.vedeng.authorization.model.vo.UserRoleQuery;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.page.Page;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.*;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.goods.dao.RCategoryJUserMapper;
import com.vedeng.infrastructure.feign.uac.UacWxUserInfoApiService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.SaleorderUserInfoDto;
import com.vedeng.system.model.OrgCallLineDto;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.UserService;
import com.vedeng.todolist.model.UserInfo;
import com.vedeng.trader.dao.RTraderJUserMapper;
import com.vedeng.trader.model.RTraderJUser;
import com.vedeng.uac.api.dto.WxUserDto;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.vedeng.order.model.dto.SaleorderUserInfoDto.*;

@Service("userService")
public class UserServiceImpl extends BaseServiceimpl implements UserService {
	@Autowired
	@Qualifier("userMapper")
	private UserMapper userMapper;

	@Autowired
	@Qualifier("userDetailMapper")
	private UserDetailMapper userDetailMapper;

	@Autowired
	@Qualifier("userAddressMapper")
	private UserAddressMapper userAddressMapper;

	@Autowired
	@Qualifier("rUserRoleMapper")
	private RUserRoleMapper rUserRoleMapper;

	@Autowired
	@Qualifier("rUserPositMapper")
	private RUserPositMapper rUserPositMapper;

	@Autowired
	@Qualifier("rCategoryJUserMapper")
	private RCategoryJUserMapper rCategoryJUserMapper;

	@Resource
	private RTraderJUserMapper rTraderJUserMapper;
	@Resource
	private UserLoginLogMapper userLoginLogMapper;

	@Autowired
	@Qualifier("organizationMapper")
	private OrganizationMapper organizationMapper;

	@Autowired
	@Qualifier("userBelongCompanyMapper")
	private UserBelongCompanyMapper userBelongCompanyMapper;

	@Autowired
	private PositionMapper positionMapper;

	@Autowired
	private RoleMapper roleMapper;

	@Autowired
	private ImMsgProducer imMsgProducer;
	@Autowired
	private OrgService orgService;

	@Value("${b2b_business_division_id}")
	private Integer b2bBusinessDivisionId;

	@Value("${B2B.service.orgids}")
	private String b2bServiceOrgids;

	@Value("${customer_service_department_id}")
	private Integer customerServiceDepartmentId;

	@Value("${yi_xie_purchase_id}")
	private Integer yiXiePurchaseId;

	@Value("${scientific_research_training_id}")
	private Integer scientificResearchTrainingId;

	@Value("${NEW_ORG_IDS}")
	protected String orgAndUserIds;

	static List<Integer> chiefLevels = Arrays.asList(442);
	static List<Integer> managerLevels = Arrays.asList(443,444);
	static List<Integer> supervisorLevels = Arrays.asList(441,609);
	static List<Integer> staffLevels = Arrays.asList(445);


	@Override
	public User getUserByNameAndPositionType(String userName, Integer positionType) {
		return userMapper.getUserByNameAndPositionType(userName,positionType);
	}

	@Override
	public User login(String username, String password, Integer companyId) {
		User user = userMapper.getByUsername(username, companyId);
		if (null != user && user.getPassword().equals(DigestUtils.md5Hex(password + user.getSalt()).toString())) {
			return user;
		}
		return user;
	}

	@Override
	public List<User> querylistPage(User user, Page page) {
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("user", user);
		map.put("page", page);
		return userMapper.querylistPage(map);

	}

	@Override
	public Boolean changDisabled(User user) {
		// 先查询员工，通过账号状态判断是启用还是禁用
		User u = userMapper.selectByPrimaryKey(user.getUserId());
		switch (u.getIsDisabled()) {
		case 0:
			user.setIsDisabled(1);
			break;
		case 1:
			user.setIsDisabled(0);
			break;
		}

		// 更新员工信息
		Integer ref = userMapper.update(user);
		if (ref > 0) {
			sendB2BorCsUserStateChangeMessage(user);
			return true;
		}
		return false;
	}

	private void sendB2BorCsUserStateChangeMessage(User user){
		try {

			List<Integer> orgIds = organizationMapper.getOrgIdListByUserId(user.getUserId());
			if (CollectionUtils.isEmpty(orgIds)) {
				return;
			}
			com.alibaba.fastjson.JSONObject userJSONObject=new com.alibaba.fastjson.JSONObject();
			userJSONObject.put("userId",user.getUserId());
			userJSONObject.put("isDisabled",user.getIsDisabled());
			Set<Integer> b2bSet = getChildrenSetOfOrg(b2bBusinessDivisionId, ErpConst.NJ_COMPANY_ID);
			if (isBelongSet(orgIds, b2bSet)) {
				logger.info("b2b部门人员启用禁用发送消息给IM,userId:{} disabled:{}",user.getUserId(),user.getIsDisabled());
				userJSONObject.put("departmentType",ErpConst.ONE);
				imMsgProducer.sendMsg(RabbitConfig.ERP_TRADER_SERVICE_EXCHANGE,RabbitConfig.ERP_USER_ROUTINGKEY, userJSONObject.toJSONString());
				return;
			}
			Set<Integer> csSet = getChildrenSetOfOrg(customerServiceDepartmentId, ErpConst.NJ_COMPANY_ID);
			if (isBelongSet(orgIds, csSet)) {
				logger.info("总机部门人员启用禁用发送消息给IM,userId:{} disabled:{}",user.getUserId(),user.getIsDisabled());
				userJSONObject.put("departmentType",ErpConst.TWO);
				imMsgProducer.sendMsg(RabbitConfig.ERP_TRADER_SERVICE_EXCHANGE,RabbitConfig.ERP_USER_ROUTINGKEY, userJSONObject.toJSONString());
			}
		}catch (Exception ex){
			logger.error("b2b部门用户变更给Im发消息失败,userId:{},disable:{},error:{}",user.getUserId(),user.getIsDisabled(),ex);
		}
	}


	private boolean isBelongSet(List<Integer> ids,Set<Integer> setIds){
	    boolean isBelong=false;
		ids.retainAll(setIds);
		if(CollectionUtils.isNotEmpty(ids))
        {
            isBelong=true;
        }
		return isBelong;
	}
	@Override
	public User getUserById(Integer userId) {
		return userMapper.selectByPrimaryKey(userId);
	}

	@Override
	public List<User> getAllUser(User user) {
		return userMapper.getAllUser(user);
	}


	private void sendMessageIfOrgChanged(User user,List<Integer> preOrgIds){
		try {
			Set<Integer> b2bOrgSet = getChildrenSetOfOrg(b2bBusinessDivisionId, ErpConst.NJ_COMPANY_ID);
			List<Integer> nowOrgIds = new ArrayList<>();
			if (StringUtil.isNotBlank(user.getOrgIds())) {
				String[] idArray = user.getOrgIds().split(",");
				if (idArray != null && idArray.length > 0) {
					for (int i = 0; i < idArray.length; i++) {
						if (StringUtil.isNotBlank(idArray[i]) && StringUtil.isNumeric(idArray[i])) {
							nowOrgIds.add(Integer.parseInt(idArray[i]));
						}
					}
				}
			}
			if (CollectionUtils.isEmpty(nowOrgIds)) {
				return;
			}
			nowOrgIds.forEach(id -> {
				if (b2bOrgSet.contains(id)) {
					JSONObject jsonObject=new JSONObject();
					jsonObject.put("userId",user.getUserId());
					jsonObject.put("orgId",id);
					imMsgProducer.sendMsg(RabbitConfig.ERP_TRADER_SERVICE_EXCHANGE,RabbitConfig.USER_ORG_CHANGE_ROUTINGKEY,jsonObject.toString());
					logger.info("向Im发送用户变更部门消息，userId:{} orgId:{}",user.getUserId(),id);
					return;
				}
			});
			boolean isPreB2b=false;
			if (CollectionUtils.isEmpty(preOrgIds)){
				return;
			}
			for(Integer id:preOrgIds){
				if(b2bOrgSet.contains(id)){
					isPreB2b=true;
				}
			}
			if(isPreB2b){
				JSONObject jsonObject=new JSONObject();
				jsonObject.put("userId",user.getUserId());
				jsonObject.put("orgId",nowOrgIds.get(0));
				imMsgProducer.sendMsg(RabbitConfig.ERP_TRADER_SERVICE_EXCHANGE,RabbitConfig.USER_ORG_CHANGE_ROUTINGKEY,jsonObject.toString());
				logger.info("向Im发送用户变更部门消息，userId:{} orgId:{}",user.getUserId(),nowOrgIds.get(0));
			}
		}catch (Exception ex){
			logger.error("b2b部门销售变更部门发送消息出错",ex);
		}
	}
	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public Integer modifyUser(HttpSession session, User user, UserDetail userDetail, UserAddress userAddress)
			throws Exception {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();

		if (null != user.getUserId() && user.getUserId() > 0) {
			// 编辑
			if (user.getPassword() != "" && user.getPassword() != null) {
				Salt salt = new Salt();
				String p_salt = salt.createSalt(false);
				user.setPassword(DigestUtils.md5Hex(user.getPassword() + p_salt).toString());
				user.setSalt(p_salt);
			}
			user.setModTime(time);
			user.setUpdater(session_user.getUserId());

			//如果staff == 0，表示非贝登员工，需要所属公司
			if(user.getStaff() == 0) {
				UserBelongCompany exist = userBelongCompanyMapper.getBelongCompany(user.getBelongCompanyName());
				if (exist != null) {
					user.setUserBelongCompanyId(exist.getUserBelongCompanyId());
				} else {
					UserBelongCompany add = new UserBelongCompany();
					add.setCompanyName(user.getBelongCompanyName());
					userBelongCompanyMapper.insertBelongCompany(add);
					user.setUserBelongCompanyId(add.getUserBelongCompanyId());
				}
			}else {
				//是贝登员工给默认值,做标识用
				user.setUserBelongCompanyId(0);
			}

			if(checkUserParentError(user.getUserId(),user.getParentId())){
				throw  new IllegalArgumentException("上级的上级不能包含自己");
			}

			Integer succ = userMapper.update(user);
			if (succ > 0) {
				// 员工详情
				UserDetail detail = userDetailMapper.getUserDetail(user.getUserId());
				if (detail != null) {
					// 编辑详情
					// UserDetail userDetail = user.getUserDetail();
					userDetail.setUserDetailId(detail.getUserDetailId());
					if(ObjectUtils.notEmpty(user.getBirthday())){
						userDetail.setBirthday(DateUtil.StringToDate(user.getBirthday(), "yyyy-MM-dd"));
					}
					userDetailMapper.update(userDetail);

				} else {
					// 新增详情
					// UserDetail userDetail = user.getUserDetail();
					userDetail.setUserId(user.getUserId());
					if(ObjectUtils.notEmpty(user.getBirthday())){
						userDetail.setBirthday(DateUtil.StringToDate(user.getBirthday(), "yyyy-MM-dd"));
					}
					userDetailMapper.insert(userDetail);
				}

				// 员工地址
				if (null != userAddress.getAddress() || user.getProvince() > 0 || user.getCity() > 0
						|| user.getZone() > 0) {

					UserAddress address = userAddressMapper.getUserAddress(user.getUserId());
					if (address != null) {
						Integer areaId = 0;
						String areaIds = "";
						if (user.getZone() > 0) {
							areaId = user.getZone();
							areaIds = 1 + "," + user.getProvince() + "," + user.getCity() + "," + user.getZone();
						} else if (user.getCity() > 0) {
							areaId = user.getCity();
							areaIds = 1 + "," + user.getProvince() + "," + user.getCity();
						} else if (user.getProvince() > 0) {
							areaId = user.getProvince();
							areaIds = 1 + "," + user.getProvince();
						}
						// UserAddress userAddress = user.getUserAddress();
						userAddress.setUserAddressId(address.getUserAddressId());
						userAddress.setAreaId(areaId);
						userAddress.setAreaIds(areaIds);
						userAddressMapper.update(userAddress);
					} else {
						Integer areaId = 0;
						String areaIds = "";
						if (user.getZone() > 0) {
							areaId = user.getZone();
							areaIds = 1 + "," + user.getProvince() + "," + user.getCity() + "," + user.getZone();
						} else if (user.getCity() > 0) {
							areaId = user.getCity();
							areaIds = 1 + "," + user.getProvince() + "," + user.getCity();
						} else if (user.getProvince() > 0) {
							areaId = user.getProvince();
							areaIds = 1 + "," + user.getProvince();
						}
						// UserAddress userAddress = user.getUserAddress();
						userAddress.setUserId(user.getUserId());
						userAddress.setAreaId(areaId);
						userAddress.setAreaIds(areaIds);
						userAddressMapper.insert(userAddress);
					}
				}

				List<Integer> preOrgIdList=organizationMapper.getOrgIdListByUserId(user.getUserId());
				// 职位
				rUserPositMapper.deleteByUserId(user.getUserId());
				if (null != user.getPositionIds() && user.getPositionIds() != "") {
					String[] positIdArray = user.getPositionIds().split(",");
					for (int i = 0; i < positIdArray.length; i++) {
						if (positIdArray[i] != null && positIdArray[i] != "") {
							int positId = Integer.parseInt(positIdArray[i]);
							RUserPosit rUserPosit = new RUserPosit();
							rUserPosit.setUserId(user.getUserId());
							rUserPosit.setPositionId(positId);

							rUserPositMapper.insert(rUserPosit);
						}
					}
				}

				// 角色
				rUserRoleMapper.deleteByUserId(user.getUserId());
				if (null != user.getRoleIds() && user.getRoleIds() != "") {
					String[] roleIdArray = user.getRoleIds().split(",");
					for (int i = 0; i < roleIdArray.length; i++) {
						if (roleIdArray[i] != null && roleIdArray[i] != "") {
							int roleId = Integer.parseInt(roleIdArray[i]);
							RUserRole rUserRole = new RUserRole();
							rUserRole.setUserId(user.getUserId());
							rUserRole.setRoleId(roleId);

							rUserRoleMapper.insert(rUserRole);
						}
					}
				}
				sendMessageIfOrgChanged(user,preOrgIdList);
				return user.getUserId();
			}
			return 0;

		} else {

			//如果staff == 0，表示非贝登员工，需要所属公司
			if(user.getStaff() == 0) {
				UserBelongCompany exist = userBelongCompanyMapper.getBelongCompany(user.getBelongCompanyName());
				if (exist != null) {
					user.setUserBelongCompanyId(exist.getUserBelongCompanyId());
				} else {
					UserBelongCompany add = new UserBelongCompany();
					add.setCompanyName(user.getBelongCompanyName());
					userBelongCompanyMapper.insertBelongCompany(add);
					user.setUserBelongCompanyId(add.getUserBelongCompanyId());
				}
			}

			// 新增
			Salt salt = new Salt();
			String p_salt = salt.createSalt(false);
			user.setCompanyId(session_user.getCompanyId());
			user.setPassword(DigestUtils.md5Hex(user.getPassword() + p_salt).toString());
			user.setSalt(p_salt);
			user.setAddTime(time);
			user.setCreator(session_user.getUserId());
			user.setModTime(time);
			user.setUpdater(session_user.getUserId());

			userMapper.insert(user);
			Integer userId = user.getUserId();

			if (userId > 0) {
				// 员工详情
				// UserDetail userDetail = user.getUserDetail();
				userDetail.setUserId(userId);
				if(ObjectUtils.notEmpty(user.getBirthday())){
					userDetail.setBirthday(DateUtil.StringToDate(user.getBirthday(), "yyyy-MM-dd"));
				}
				userDetailMapper.insert(userDetail);

				// 员工地址
				if (null != userAddress.getAddress() || user.getProvince() > 0 || user.getCity() > 0
						|| user.getZone() > 0) {
					Integer areaId = 0;
					String areaIds = "";
					if (user.getZone() > 0) {
						areaId = user.getZone();
						areaIds = 1 + "," + user.getProvince() + "," + user.getCity() + "," + user.getZone();
					} else if (user.getCity() > 0) {
						areaId = user.getCity();
						areaIds = 1 + "," + user.getProvince() + "," + user.getCity();
					} else if (user.getProvince() > 0) {
						areaId = user.getProvince();
						areaIds = 1 + "," + user.getProvince();
					}
					// UserAddress userAddress = user.getUserAddress();
					userAddress.setUserId(userId);
					userAddress.setAreaId(areaId);
					userAddress.setAreaIds(areaIds);
					userAddressMapper.insert(userAddress);
				}
				// 职位
				if (null != user.getPositionIds() && user.getPositionIds() != "") {
					String[] positIdArray = user.getPositionIds().split(",");
					for (int i = 0; i < positIdArray.length; i++) {
						if (positIdArray[i] != null && positIdArray[i] != "") {
							int positId = Integer.parseInt(positIdArray[i]);
							RUserPosit rUserPosit = new RUserPosit();
							rUserPosit.setUserId(userId);
							rUserPosit.setPositionId(positId);

							rUserPositMapper.insert(rUserPosit);
						}
					}
				}
				// 角色
				if (null != user.getRoleIds() && user.getRoleIds() != "") {
					String[] roleIdArray = user.getRoleIds().split(",");
					for (int i = 0; i < roleIdArray.length; i++) {
						if (roleIdArray[i] != null && roleIdArray[i] != "") {
							int roleId = Integer.parseInt(roleIdArray[i]);
							RUserRole rUserRole = new RUserRole();
							rUserRole.setUserId(userId);
							rUserRole.setRoleId(roleId);

							rUserRoleMapper.insert(rUserRole);
						}
					}
				}
				return userId;

			}
			return 0;
		}
	}

	/**
	 * @see getUserParentListById
	 * 解决循环依赖上下级的输入问题
	 * @param userId
	 * @return
	 */
	private  boolean checkUserParentError(Integer userId,Integer parentId) {
		if(userId==null||parentId==null ){
			return false;
		}
		if(userId==parentId){
			return true;
		}
		int currentUserId=userId;
		int currentParentId=parentId;
		for (int i=0;i<10;i++){
			User userParent = userMapper.getUserParentById(currentParentId);
			if (Objects.isNull(userParent)){
				break;
			}
			currentParentId = userParent.getUserId();
			if (userParent.getParentId() == 0){
				break;
			}
			//如果当前用户的父级的父级是自己
			if(currentUserId==currentParentId){
				return true;
			}
		}
		return false;
	}

	@Override
	public List<User> getUserByPositId(Integer positionId) {
		return userMapper.getUserByPositId(positionId);
	}

	@Override
	public List<User> getUserByRoleId(Integer roleId) {
		return userMapper.getUserByRoleId(roleId);
	}

	@Override
	public User getUser(User user) {
		return userMapper.getUser(user);
	}

	@Override
	public List<RUserRole> getRUserRoleListByUserId(Integer userId) {

		return rUserRoleMapper.getRUserRoleListByUserId(userId);
	}

	/**
	 * <b>Description:</b><br>
	 * 查询职位类型为311所有的员工
	 *
	 * @param positionType
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年5月16日 上午10:10:23
	 */
	@Override
	public List<User> getUserByPositType(Integer positionType, Integer companyId) {
		return userMapper.getUserByPositType(positionType, companyId);
	}
	@Override
	public List<User> getActiveUserByPositType(Integer positionType, Integer companyId) {
		return userMapper.getActiveUserByPositType(positionType, companyId);
	}

	@Override
	public List<Integer> getUserId(Integer proUserId) {
		return userMapper.getUserId(proUserId);//根据归属人查找用户id 2019-12-25
	}
	//带采购 刷新界面
	@Override
	public List<User> getUserListByOrgIddcg(Integer proOrgtId) {
		return userMapper.getUserListByOrgIdcg(proOrgtId);
	}


	/**
	 * <b>Description:</b><br>
	 * 根据userid获取dbcenter的trader表的主键
	 *
	 * @param userId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年5月16日 下午1:18:48
	 */
	@Override
	public List<Integer> getTraderIdListByUserId(Integer userId, Integer traderType) {
		RTraderJUser rTraderJUser =new RTraderJUser();
		rTraderJUser.setUserId(userId);
		rTraderJUser.setTraderType(traderType);
		List<Integer> traList = rTraderJUserMapper.getRTraderJUserListByUserId(rTraderJUser);
		return traList;
	}

	/**
	 * <b>Description:</b><br>
	 * 保存或更新
	 *
	 * @param user
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年5月22日 上午9:52:08
	 */
	@Transactional
	@Override
	public int saveOrUpdate(User user, String ip) {
		int res = 0;
		user.setLastLoginIp(ip);
		user.setLastLoginTime(System.currentTimeMillis());
		if (null != user && null != user.getUserId()) {
			res = userMapper.update(user);
		} else {
			res = userMapper.insert(user);
		}
		return res;
	}

	@Override
	public List<User> getUserByUserIds(List<Integer> userIds) {
		return userMapper.getUserByUserIds(userIds);
	}

	@Override
	public User getUserByUserId(Integer userId) {
		return userMapper.getUserByUserId(userId);
	}

	@Override
	public List<User> getUserByUserIds(Collection<String> userNames){
		return userMapper.getUserByUserNames(new ArrayList<>(userNames));
	}

	@Override
	public List<User> getUserListByOrgId(Integer orgId) {
		return userMapper.getUserListByOrgId(orgId);
	}

	@Override
	public List<User> getUserListByOrgIdcg(Integer orgId) {
		return userMapper.getUserListByOrgIdcg(orgId);
	}

	@Override
	public List<Integer> getUserIdListByOrgId(Integer orgId) {
		return userMapper.getUserIdListByOrgId(orgId);
	}

	@Override
	public List<String> queryUserListByOrgId(Integer orgId) {
		return userMapper.queryUserListByOrgId(orgId);
	}

	@Override
	public int modifyPassowrd(User user) {
		user.setModTime(System.currentTimeMillis());
		return userMapper.update(user);
	}

	@Override
	public List<User> getUserListByPositType(Integer typeId,Integer companyId) {
		return userMapper.getUserListByPositType(typeId,companyId);
	}



	/**
	 * <b>Description:</b><br>
	 * 根据部门id集合查询所有员工
	 *
	 * @param orgIds
	 * @param companyId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年7月13日 下午1:13:55
	 */
	@Override
	public List<User> getUserListByOrgIds(List<Integer> orgIds, Integer companyId) {
		return userMapper.getUserListByOrgIdList(orgIds, companyId);
	}

	/**
	 * <b>Description:</b><br>
	 * 根据部门id集合查询所有员工 职位类型
	 *
	 * @param orgIds
	 * @param companyId
	 * @param type 职位类型
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年7月13日 下午1:13:55
	 */
	@Override
	public List<User> getUserListByOrgIdListAndPostionType(List<Integer> orgIds, Integer companyId,Integer type) {
		return userMapper.getUserListByOrgIdListAndPostionType(orgIds, companyId,type);
	}

	/**
	 * 递归查询当前用户所有下级的用户
	 *
	 * @param userId
	 *            用户ID
	 * @param companyId
	 *            用户的公司ID
	 * @param haveMyself
	 *            返回值是否包含自己
	 * @param positionLevel
	 *            当前用户职位等级
	 * @param positionType
	 *            需要检索的职位类型 0 所有用户 1查询子集名下的用户
	 */
	/*
	 * @Override public List<User> getNextAllUserList(Integer userId, Integer
	 * companyId, boolean haveMyself, Integer positionLevel, Integer
	 * positionType) { List<User> userList = null; List<User> list = null;
	 *
	 * Boolean mySelf = false;// 是否查询自己名下的人员 if (null == positionType ||
	 * positionType.equals(0) || positionType.equals(1)) { if
	 * (positionType.equals(1)) { mySelf = true; } userList =
	 * userMapper.getAllUserList(companyId); if (!haveMyself) { for (User u :
	 * userList) { if (u.getUserId().equals(userId)) { userList.remove(u); } } }
	 * } else { if (positionType.equals(SysOptionConstant.ID_310)) {// 销售 if
	 * ((positionLevel.equals(443) || positionLevel.equals(445) ||
	 * positionLevel.equals(444)) && userId != 1) {// 销售高级主管、销售主管、销售工程师 //
	 * 查询自己下面的人员 mySelf = true; } else { userList =
	 * getUserByPositType(SysOptionConstant.ID_310, companyId); } } if
	 * (positionType.equals(SysOptionConstant.ID_311)) {// 采购 if
	 * ((positionLevel.equals(448) || positionLevel.equals(449) ) && userId !=
	 * 1) {// 采购主管、采购人员 mySelf = true; } else { userList =
	 * getUserByPositType(SysOptionConstant.ID_311, companyId); } } if
	 * (positionType.equals(SysOptionConstant.ID_312)) {// 售后 if
	 * ((positionLevel.equals(454) || positionLevel.equals(455)) && userId != 1)
	 * {// 售后主管、售前支持 mySelf = true; } else { userList =
	 * getUserByPositType(SysOptionConstant.ID_312, companyId); } } if
	 * (positionType.equals(SysOptionConstant.ID_313)) {// 物流 if
	 * ((positionLevel.equals(452) || positionLevel.equals(453)) && userId != 1)
	 * {// 物流主管、人员 mySelf = true; } else { userList =
	 * getUserByPositType(SysOptionConstant.ID_313, companyId); } } if
	 * (positionType.equals(SysOptionConstant.ID_314)) {// 财务 if
	 * ((positionLevel.equals(451)) && userId != 1) {// 财务人员 mySelf = true; }
	 * else { userList = getUserByPositType(SysOptionConstant.ID_314,
	 * companyId); } }
	 *
	 * }
	 *
	 * if (mySelf) { if (userList != null) { JSONArray jsonArray = (JSONArray)
	 * JSONArray.fromObject(userList); List<User> sellist = new
	 * ArrayList<User>();
	 *
	 * JSONArray jsonList = treeMenuList(jsonArray, userId, ""); list =
	 * resetList(jsonList, sellist, 0); } else { list = new ArrayList<User>(); }
	 * } else { list = userList; }
	 *
	 * if (haveMyself) { Boolean have = false; for(User u : list){
	 * if(u.getUserId().equals(userId)){ have = true; } } if(!have){ User user =
	 * userMapper.selectByPrimaryKey(userId); list.add(user); } }
	 *
	 * return list; }
	 */

	/**
	 *
	 * @Title: treeMenuList @Description: 递归组装树形结构 @param @param
	 *         menuList @param @param parentId @param @return @return
	 *         JSONArray @throws
	 */
	public JSONArray treeMenuList(JSONArray menuList, int parentId, String parentName) {
		JSONArray childMenu = new JSONArray();
		for (Object object : menuList) {
			JSONObject jsonMenu = (JSONObject) JSONObject.fromObject(object);
			int menuId = jsonMenu.getInt("userId");
			int pid = jsonMenu.getInt("parentId");
			if (parentName != "") {
				jsonMenu.element("nameArr", parentName + "--" + jsonMenu.getString("username"));
			} else {
				jsonMenu.element("nameArr", jsonMenu.getString("username"));
			}
			if (parentId == pid) {
				JSONArray c_node = treeMenuList(menuList, menuId, jsonMenu.getString("nameArr"));
				jsonMenu.put("childNode", c_node);
				childMenu.add(jsonMenu);
			}
		}
		return childMenu;
	}

	/**
	 *
	 * @Title: resetList @Description: 递归分析树状结构 @param @param
	 *         tasklist @param @param sellist @param @param
	 *         num @param @return @return List<SelectModel> @throws
	 */
	public List<User> resetList(JSONArray tasklist, List<User> sellist, int num) {
		String str = "";
		for (int i = 0; i < (num * 2); i++) {
			str += "-";
		}
		for (Object obj : tasklist) {
			JSONObject jsonMenu = (JSONObject) JSONObject.fromObject(obj);
			User sm = new User();
			sm.setUserId(Integer.valueOf(jsonMenu.getInt("userId")));
			sm.setUsername(str + "├" + jsonMenu.getString("username"));
			sm.setParentId(jsonMenu.getInt("parentId"));
			sm.setCcNumber(jsonMenu.getString("ccNumber"));
			sellist.add(sm);
			if (jsonMenu.get("childNode") != null) {
				if (JSONArray.fromObject(jsonMenu.get("childNode")).size() > 0) {
					num++;
					resetList(JSONArray.fromObject(jsonMenu.get("childNode")), sellist, num);
					num--;
				}
			}
		}
		return sellist;
	}

	@Override
	public List<Integer> getTraderIdListByUserList(List<User> userList, String traderType) {
		return userMapper.getTraderIdListByUserList(userList, traderType);
	}

	@Override
	public List<Integer> getTraderIdsByUserList(List<User> userList, Integer traderType) {
		return userMapper.getTraderIdsByUserList(userList, traderType);
	}

	@Override
	public User getUserByTraderId(Integer traderId, Integer traderType) {
		return userMapper.getUserByTraderId(traderId, traderType);
	}

	@Override
	public List<Integer> getUserIdListByTraderId(Integer traderId, Integer traderType) {
		return userMapper.getUserIdListByTraderId(traderId, traderType);
	}

	@Override
	public List<User> getMyUserListByUserOrgsList(User user,List<Integer> positionType,boolean haveDisabeldUser){

		User userNow = getUserById(user.getUserId());//查询当前登录用户的信息

		if(StringUtils.isBlank(userNow.getOrgIdsList())){
			return new ArrayList<>();
		}
		List<User> userList = new ArrayList<>();// 返回的用户列表
		List<Organization>  organizationList = organizationMapper.getAllOrganizationOnly();//所有的组织架构集合

		String[] orgIdListStr = StringUtils.split(userNow.getOrgIdsList(),',');
		List<Integer> orgIdList = Arrays.stream(orgIdListStr)
				.map(Integer::parseInt)
				.collect(Collectors.toList());
		List<Organization> sonList =  getOrganizationsWithChildren(orgIdList,organizationList);//以上ID的子集及自己
		if(CollectionUtil.isEmpty(sonList)){
			return new ArrayList<>();
		}
		List<Integer> sonIdList = sonList.stream()
				.map(Organization::getOrgId)
				.collect(Collectors.toList());//将所有的ID
		if(CollectionUtil.isEmpty(sonIdList)){
			return new ArrayList<>();
		}
		UserQueryVo userQueryVo = new UserQueryVo();

		if (!haveDisabeldUser) {// 非禁用用户
			userQueryVo.setIsDisabled(0);
		}
		if (positionType != null && positionType.size() > 0) {// 目标职位集合
			userQueryVo.setPositionTypes(positionType);
		}
		userQueryVo.setOrgIdList(sonIdList);
		List<User> sonUserList = userMapper.getUserByPositTypesAndOrgIdList(userQueryVo);
		return sonUserList;
	}
	public List<Organization> getOrganizationsWithChildren(List<Integer> orgIdList, List<Organization> allOrgs) {
		Map<Integer, Organization> orgMap = new HashMap<>();
		Map<Integer, List<Organization>> childMap = new HashMap<>();
		List<Organization> sonList = new ArrayList<>();

		// 将所有组织放入映射中，便于快速查找
		for (Organization org : allOrgs) {
			orgMap.put(org.getOrgId(), org);
			childMap.put(org.getOrgId(), new ArrayList<>()); // 初始化子组织列表
		}

		// 构建每个组织的子组织列表
		for (Organization org : allOrgs) {
			if (org.getParentId() != null && childMap.containsKey(org.getParentId())) {
				childMap.get(org.getParentId()).add(org);
			}
		}

		// 根据给定的组织ID列表，递归地收集组织及其所有子组织
		for (Integer id : orgIdList) {
			if (orgMap.containsKey(id)) {
				Organization org = orgMap.get(id);
				sonList.add(org);
				addChildrenToList(org, childMap, sonList);
			}
		}

		return sonList;
	}

	// 辅助方法：递归地将组织及其子组织添加到结果列表中
	private void addChildrenToList(Organization org, Map<Integer, List<Organization>> childMap, List<Organization> list) {
		List<Organization> children = childMap.get(org.getOrgId());
		for (Organization child : children) {
			list.add(child);
			addChildrenToList(child, childMap, list); // 递归添加子组织
		}
	}

	@Override
	public List<User> getMyUserList(User user, List<Integer> positionType, boolean haveDisabeldUser) {
		List<User> userList = new ArrayList<>();// 返回的用户列表
		//User user = (User) session.getAttribute(Consts.SESSION_USER); // 当前用户

		User s_u = new User();// 需要检索的目标用户
		if (!user.getIsAdmin().equals(2)) {// 非超级管理员
			s_u.setCompanyId(user.getCompanyId());
		}

		if (!haveDisabeldUser) {// 非禁用用户
			s_u.setIsDisabled(0);
		}

		if (positionType != null && positionType.size() > 0) {// 目标职位集合
			s_u.setPositionTypes(positionType);
		}

		List<User> userByPositTypes = userMapper.getUserByPositTypes(s_u);

		if (positionType != null && positionType.size() > 0) {
			if (!user.getIsAdmin().equals(2)) {// 非超级管理员
				Boolean isInPosit = false;// 自己当前职位是否在职位类型内
				for (Integer p : positionType) {
					if (p.equals(user.getPositType())) {
						isInPosit = true;
					}
				}
				if (isInPosit) {// 查询自己下面的用户
					List<User> treeUser = null;
					JSONArray jsonArray = (JSONArray) JSONArray.fromObject(userByPositTypes);
					List<User> sellist = new ArrayList<User>();

					JSONArray jsonList = treeMenuList(jsonArray, user.getUserId(), "");
					treeUser = resetList(jsonList, sellist, 0);
					treeUser.add(user);
					if (treeUser.size() > 1) {// 名称排序
						List<Integer> userIds = new ArrayList<>();
						for (User u : treeUser) {
							userIds.add(u.getUserId());
						}
						userList = userMapper.getUserByUserIds(userIds);

					} else {
						userList = treeUser;
					}
				} else {
					userList = userByPositTypes;
				}
			} else {
				userList = userByPositTypes;
			}
		} else {
			userList = userByPositTypes;
		}

		if(userList != null && userList.size() > 0){
			Boolean isE = false;
			for(User u : userList){
				if(u.getUserId().equals(user.getUserId())){
					isE = true;
					break;
				}
			}
			if(!isE){
				try
				{
					User newOne = new User();
					BeanUtils.copyProperties(newOne,user);
					userList.add(newOne);
				}catch (Exception e){
					logger.error("copy user failure",e);
				}

			}
		}else{
			try
			{
				User newOne = new User();
				BeanUtils.copyProperties(newOne,user);
				userList.add(newOne);
			}catch (Exception e){
				logger.error("copy user failure",e);
			}
		}
		return userList;
	}

	/**
	 * <b>Description:</b><br>
	 * 根据角色的集合查询关联用户的id集合
	 *
	 * @param roleIdList
	 * @return
	 * @Note <b>Author:</b> east <br>
	 * 		<b>Date:</b> 2017年8月2日 下午1:23:16
	 */
	@Override
	public List<Integer> getUserIdList(List<Integer> roleIdList) {
		return rUserRoleMapper.getUserIdList(roleIdList);
	}

	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public Integer addUser(HttpSession session, User user, UserDetail userDetail) throws Exception {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		Salt salt = new Salt();
		String p_salt = salt.createSalt(false);
		user.setCompanyId(user.getCompanyId());
		user.setPassword(DigestUtils.md5Hex(user.getPassword() + p_salt).toString());
		user.setSalt(p_salt);
		user.setAddTime(time);
		user.setCreator(session_user.getUserId());
		user.setModTime(time);
		user.setUpdater(session_user.getUserId());

		userMapper.insert(user);
		Integer userId = user.getUserId();
		if (userId > 0) {
			// 员工详情
			userDetail.setUserId(userId);

			userDetailMapper.insert(userDetail);

			// 角色
			if (null != user.getRoleIds() && user.getRoleIds() != "") {
				String[] roleIdArray = user.getRoleIds().split(",");
				for (int i = 0; i < roleIdArray.length; i++) {
					if (roleIdArray[i] != null && roleIdArray[i] != "") {
						int roleId = Integer.parseInt(roleIdArray[i]);
						RUserRole rUserRole = new RUserRole();
						rUserRole.setUserId(userId);
						rUserRole.setRoleId(roleId);

						rUserRoleMapper.insert(rUserRole);
					}
				}
			}
			return userId;
		}

		return 0;
	}

	@Transactional(rollbackFor = Exception.class, readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public Integer editUser(HttpSession session, User user, UserDetail userDetail) throws Exception {
		User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
		Long time = DateUtil.sysTimeMillis();
		// 编辑
		if (user.getPassword() != "" && user.getPassword() != null) {
			Salt salt = new Salt();
			String p_salt = salt.createSalt(false);
			user.setPassword(DigestUtils.md5Hex(user.getPassword() + p_salt).toString());
			user.setSalt(p_salt);
		}
		user.setModTime(time);
		user.setUpdater(session_user.getUserId());

		Integer succ = userMapper.update(user);
		if (succ > 0) {
			// 员工详情
			UserDetail detail = userDetailMapper.getUserDetail(user.getUserId());
			if (detail != null) {
				// 编辑详情
				// UserDetail userDetail = user.getUserDetail();
				userDetail.setUserDetailId(detail.getUserDetailId());
				//userDetail.setBirthday(DateUtil.StringToDate(user.getBirthday(), "yyyy-MM-dd"));
				userDetailMapper.update(userDetail);

			} else {
				// 新增详情
				// UserDetail userDetail = user.getUserDetail();
				userDetail.setUserId(user.getUserId());
				//userDetail.setBirthday(DateUtil.StringToDate(user.getBirthday(), "yyyy-MM-dd"));
				userDetailMapper.insert(userDetail);
			}
			// 角色
			rUserRoleMapper.deleteByUserId(user.getUserId());
			if (null != user.getRoleIds() && user.getRoleIds() != "") {
				String[] roleIdArray = user.getRoleIds().split(",");
				for (int i = 0; i < roleIdArray.length; i++) {
					if (roleIdArray[i] != null && roleIdArray[i] != "") {
						int roleId = Integer.parseInt(roleIdArray[i]);
						RUserRole rUserRole = new RUserRole();
						rUserRole.setUserId(user.getUserId());
						rUserRole.setRoleId(roleId);

						rUserRoleMapper.insert(rUserRole);
					}
				}
			}
			return user.getUserId();
		}
		return 0;
	}

	@Override
	public String getUserNameByUserId(Integer userId){
		return userMapper.getUserNameByUserId(userId);
	}

	@Override
	public String getRedisDbType() {
		return dbType;
	}

	@Override
	public User getUserParentInfo(String username, Integer companyId) {
		return userMapper.getUserParentInfo(username,companyId);
	}

	@Override
	public List<User> getUserByPositLevel(Integer orgId,Integer level) {
		//查询出所有上级部门 含当前部门
		List<Integer> orgIds = new ArrayList<>();

		Organization org = new Organization();
		org.setOrgId(orgId);
		Organization orgInfo = organizationMapper.getByOrg(org);
		Integer parentId = 0;
		if(null != orgInfo && orgInfo.getParentId() != null && orgInfo.getParentId() > 0){
			parentId = orgInfo.getParentId();
			orgIds.add(orgId);
		}
		//迭代查询上级部门
		do{
			Organization orgParent = new Organization();
			orgParent.setOrgId(parentId);
			Organization orgInfoParent = organizationMapper.getByOrg(orgParent);
			if(null != orgInfoParent && orgInfoParent.getParentId() != null && orgInfoParent.getParentId() > 0){
				parentId = orgInfoParent.getParentId();
				orgIds.add(orgInfoParent.getOrgId());
			}else{
				parentId = 0;
			}

		}while(parentId > 0);
		logger.info("UserServiceImpl.getUserByPositLevel,orgIds：{}", JSON.toJSONString(orgIds));
		if(orgIds.size() > 0){
			return userMapper.getUserByOrgIdsAndPositLevel(orgIds, level);
		}
		logger.info("UserServiceImpl.getUserByPositLevel返回null");
		return null;
	}

	@Override
	public List<Integer> getCategoryIdListByUserId(Integer goodsUserId) {
		List<Integer> catList = rCategoryJUserMapper.getCategoryIdsByUserId(goodsUserId);
		return catList;
	}

	@Override
	public List<Integer> getCategoryIdListByUserList(List<User> userList) {
		List<Integer> catList = rCategoryJUserMapper.getCategoryIdsByUserList(userList);
		return catList;
	}
	@Override
	public User getByUsername(String username,Integer companyId){
	    return userMapper.getByUsername(username,companyId);
	}

	@Override
	public User getByUsernameEnable(String username,Integer companyId){
		return userMapper.getByUsernameEnable(username,companyId);
	}

	@Override
	public User getUserInfoByTraderId(Integer traderId, Integer traderType) {
		return userMapper.getUserInfoByTraderId(traderId,traderType);
	}

	@Override
	public List<User> getTraderUserAndOrgList(List<Integer> traderIdList) {
		return userMapper.getTraderUserAndOrgList(traderIdList);
	}

	@Override
	public List<User> getUserByTraderIdList(List<Integer> traderIdList, Integer traderType) {
		return userMapper.getUserByTraderIdList(traderIdList,traderType);
	}

	@Override
	public List<Organization> getOrgNameByOrgIdList(List<Integer> orgIdList, Integer companyId) {
		return userMapper.getOrgNameByOrgIdList(orgIdList,companyId);
	}

	@Override
	public List<User> getCategoryUserList(List<Integer> categoryIdList,Integer companyId) {
		return userMapper.getCategoryUserList(categoryIdList,companyId);
	}

	@Override
	public UserDetail getUserDetailByTraderId(Integer traderId, Integer type) {
		return userDetailMapper.getUserDetailByTraderId(traderId,type);
	}

	@Override
	public List<User> getSaleUserOrgList(Integer companyId) {
		return userMapper.getSaleUserOrgList(companyId);
	}

	@Override
	public List<User> getSaleUserOrgListAll(Integer companyId) {
		return userMapper.getSaleUserOrgListAll(companyId);
	}

	@Override
	public UserDetail queryUserDetailByUserId(Integer userId)
	{
		if(null == userId)
		{
			return null;
		}
		return userDetailMapper.getUserDetail(userId);
	}

	@Override
	public UserLoginLog queryLoginOrOutInfo(UserLoginLog userLoginInfo)
	{
		if(null == userLoginInfo)
		{
			return null;
		}
		return userLoginLogMapper.queryUserLogOrOutInfo(userLoginInfo);
	}

	@Override
	public List<User> getUserListByUserIdList(List<Integer> userIdList)
	{
		return userMapper.getUserListByUserIdList(userIdList);
	}

	@Override
	public List<User> selectAllAssignUser() {
		return userMapper.selectAllAssignUser();
	}

	@Override
	public Set<User> getSubUserListForSaleApi(User user) {

		Set<User> userList = new HashSet<>();

		//获取用户的当前所有职位,正常只有一个
		List<Position> positionList = positionMapper.getPositionByUserIdAndCompanyId(user.getUserId(), user.getCompanyId());

		for (Position each : positionList) {

			if(SysOptionConstant.ID_310.equals(each.getType())){
				//读取当前部门的下级部门
				List<Organization> orgList = organizationMapper.getOrgList(each.getOrgId(), user.getCompanyId());

				if (CollectionUtils.isEmpty(orgList)) {
					//为空代表此部门是最低一级部门
					if (445 == each.getLevel()) {
						//该职位的职级为445，代表普通员工
						userList.add(getUserById(user.getUserId()));
					} else {
						//非445 说明是主管级别
						userList.addAll(getUserListByOrgId(each.getOrgId()));
					}
				} else {
					//下级部门非空的时候，需要读取下级全部员工
					userList.addAll(getUserListByOrgIds(orgList.stream().map(Organization::getOrgId).collect(Collectors.toList()),
							user.getCompanyId()));
				}
			}
		}
		return userList;
	}

	@Override
	public User getUserInfoByMobile(String traderContactMobile, Integer traderType) {
		return userMapper.getUserInfoByMobile(traderContactMobile,traderType);
	}


	@Override
	public User getBDUserInfoByMobile(String phone) {
		// TODO Auto-generated method stub
		return userMapper.getBDUserInfoByMobile(phone);
	}
	/**
	 *是否是销售  true 是 false 不是
	 * @Author:strange
	 * @Date:16:50 2020-02-21
	 */
	@Override
	public Boolean getSaleFlagUserId(Integer userId) {
        List<Role> roleList = roleMapper.getUserRoles(userId);
		boolean resultFlag = false;
		if(CollectionUtils.isNotEmpty(roleList)) {
			for (Role role : roleList) {
				String rn = role.getRoleName();
				if (rn.equals("销售总监") || rn.equals("销售主管") || rn.equals("销售工程师")
						|| rn.equals("南京贝登医疗股份有限公司管理员") || rn.equals("管理员")) {
					resultFlag = true;
					break;
				}
			}
		}
		return resultFlag;
	}
	/**
	 *是否是物流人员  true 是 false 不是
	 * @Author:strange
	 * @Date:09:07 2020-02-26
	 */
	@Override
	public List<User> getOrgUserList(Saleorder saleorder, Integer companyId) {
		List<Organization> list = orgService.getOrgList(saleorder.getOrgId(), companyId, true);
		List<Integer> orgIdList = Lists.newArrayList();
		if (null != list && list.size() > 0) {
			for (Organization organization : list) {
				if (SysOptionConstant.ID_310.equals(organization.getType())) {
					orgIdList.add(organization.getOrgId());
				}
			}
		}
		orgIdList.add(saleorder.getOrgId());
		saleorder.setOrgIdList(orgIdList);
		List<User> userList = orgService.getUserListBtOrgId(saleorder.getOrgId(), SysOptionConstant.ID_310,
				companyId);
		return userList;
	}

	@Override
    public Boolean getLogisticeFlagByUserId(Integer userId) {
        List<Role> roleList = roleMapper.getUserRoles(userId);
        boolean resultFlag = false;
        if(CollectionUtils.isNotEmpty(roleList)) {
            for (Role role : roleList) {
                String rn = role.getRoleName();
                if (rn.equals("物流总监") || rn.equals("物流专员") || rn.equals("南京贝登医疗股份有限公司管理员") || rn.equals("管理员")) {
                    resultFlag = true;
                    break;
                }
            }
        }

        return resultFlag;
    }

    @Override
    public List<Role> getRoleByUserId(Integer userId) {
        return roleMapper.getUserRoles(userId);
    }

	@Override
	public List<User> getLeadersByParentId(Integer parentId, Integer positionType) {
		List<User> leaders = new ArrayList<>();
		getLeader(parentId, positionType, leaders);
		return leaders;
	}


	/**
	 * 获取销售的归属平台
	 * VDERP-2297：去除集团业务部的判断
	 * @param userId 用户id
	 * @param companyId
	 * @return
	 */
	@Override
	public Integer getBelongPlatformOfUser(Integer userId, Integer companyId) {
		Set<Integer> b2bSet = getChildrenSetOfOrg(b2bBusinessDivisionId,companyId);
		Set<Integer> yxgSet = getChildrenSetOfOrg(yiXiePurchaseId,companyId);
		Set<Integer> kygSet = getChildrenSetOfOrg(scientificResearchTrainingId,companyId);
		List<Position> userOrgInfo = positionMapper.getOrgListOfUsers(Collections.singletonList(userId));
		if(CollectionUtils.isNotEmpty(userOrgInfo)){
			Integer orgId = Integer.valueOf(userOrgInfo.get(0).getPositionName().split(",")[0]);
			if (b2bSet.contains(orgId)){
				return 1;
			} else if (yxgSet.contains(orgId)){
				return 2;
			} else if (kygSet.contains(orgId)){
				return 3;
			} else {
				return 5;
			}
		}else{
			return 1;
		}

	}

	@Override
	public Map<Integer, Integer> getBelongPlatformOfAllSales(Integer companyId) {
		Map<Integer, Integer> map = new HashMap<>();
		Set<Integer> b2bSet = getChildrenSetOfOrg(b2bBusinessDivisionId,companyId);
		Set<Integer> yxgSet = getChildrenSetOfOrg(yiXiePurchaseId,companyId);
		Set<Integer> kygSet = getChildrenSetOfOrg(scientificResearchTrainingId,companyId);
		List<Position> userOrgInfo = positionMapper.getOrgListOfAllUsers();
		if(CollectionUtils.isNotEmpty(userOrgInfo)){
			for (Position user:userOrgInfo){
				Integer userId = user.getOrgId();
				Integer orgId = Integer.valueOf(user.getPositionName().split(",")[0]);
				if (b2bSet.contains(orgId)){
					map.put(userId,1);
				} else if (yxgSet.contains(orgId)){
					map.put(userId,2);
				} else if (kygSet.contains(orgId)){
					map.put(userId,3);
				} else {
					map.put(userId,5);
				}
			}
		}else{
			return null;
		}
		return map;
	}

	@Override
	public Set<Integer> getChildrenSetOfOrg(Integer parentOrgId, Integer companyId){
		List<Integer> childrenOrg = orgService.getChildrenByParentId(parentOrgId,companyId);
		//将父节点加入到相应子节点集合中
		childrenOrg.add(parentOrgId);
		//将部门节点转换为Map数据结构
		return new HashSet<>(childrenOrg);
	}

	@Override
	public Boolean getBelongPlatfromByOrgAndUser(User user,Integer orgId,Integer companyId){
		Set<Integer> orgSet = getChildrenSetOfOrg(orgId,companyId);
		if(orgSet.contains(user.getOrgId())){
			return true;
		}else {
			return false;
		}
	}

	@Override
	public List<Integer> getAllSubordinateByParentId(Integer parentId){
		return userMapper.getAllSubordinateByParentId(parentId);
	}


	@Override
	public Set<Integer> getChildrenSetOfOrgByCache(Integer parentOrgId, Integer companyId){
		Set<Integer> childrenOrg = new HashSet<>();
		if (JedisUtils.exists(dbType + ErpConst.ORG_PARENT_ALL_IDS + parentOrgId)) {
			String json = JedisUtils.get(dbType + ErpConst.ORG_PARENT_ALL_IDS + parentOrgId);
			JSONArray jsonArray = JSONArray.fromObject(json);
			childrenOrg = new HashSet<>((List<Integer>) JSONArray.toCollection(jsonArray, Integer.class));
		}else {
			childrenOrg =  getChildrenSetOfOrg(parentOrgId,companyId);
			JedisUtils.set(dbType + ErpConst.ORG_PARENT_ALL_IDS + parentOrgId,
					JsonUtils.convertConllectionToJsonStr(childrenOrg), 300);
		}
		return childrenOrg;
	}




	private void getLeader(Integer parentId, Integer positionType, List<User> leaders) {
		if (parentId != null && parentId > 0) {
			User user = userMapper.getUserByIdAndPositionType(parentId, positionType);
			if (user != null) {
				leaders.add(user);
				getLeader(user.getParentId(), positionType, leaders);
			}
		}
	}

	@Override
	public User getUserByName(User u) {
		return userMapper.getUserByName2(u);
	}

	@Override
	public User getUserByNumber(User u) {
		return userMapper.getUserByNumber(u);
	}

	@Override
	public List<User> selectAllcheckPreson() {
		return userMapper.selectAllcheckPerson();
	}

	@Override
	public boolean isRoledirector(String userName) {
		User user = userMapper.getUserByName(userName);
		if(user==null){
			logger.error("找不到user={}",userName);
			return false;
		}
		List<Role> roleList = getRoleByUserId(user.getUserId());
		if(CollectionUtils.isNotEmpty(roleList)){
			for (Role role : roleList) {
				//产品总监
				if(role.getRoleId().equals(7)){
					return true;
				}
			}
		}
		return false;
	}


	@Override
	public boolean isParentUserforManager(String username) {
		User parentInfo = getUserParentInfo(username,1);
		List<Role> roleList = getRoleByUserId(parentInfo.getParentId());
		if(CollectionUtils.isNotEmpty(roleList)){
			for (Role role : roleList) {
				if(role.getRoleName().equals("销售经理")){
					return true;
				}
			}
		}
		return false;
	}

	@Override
	public Boolean getFinanceFlagByUserId(Integer userId) {
		boolean resultFlag = false;
		User user = organizationMapper.getFinanceFlagByUserId(userId);
		if (user != null || userId == 2){
			resultFlag = true;
		}
		return resultFlag;
	}

	@Override
	public Boolean getSupplyChainFlagByUserId(Integer userId) {
		boolean resultFlag = false;
		Set<Integer> supplierOrgIds = getChildrenSetOfOrgByCache(6, 1);
        Organization org = organizationMapper.getOrgNameByUserId(userId);
        if (Objects.isNull(org)) {
            return false;
        }
		if (supplierOrgIds.contains(org.getOrgId())) {
			resultFlag = true;
		}
		return resultFlag;
	}

	@Value(value="${orgIdForAed:199}")
	private Integer orgIdForAed ;
	@Override
	public Boolean getAEDFlagByUserId(Integer userId) {
		boolean resultFlag = false;
		Set<Integer> supplierOrgIds = getChildrenSetOfOrgByCache(6, 1);
		Organization org = organizationMapper.getOrgNameByUserId(userId);
		if (Objects.isNull(org)) {
			return false;
		}
		if (supplierOrgIds.contains(org.getOrgId())) {
			resultFlag = true;
		}
		return resultFlag;
	}

	@Override
	public String getAssistantBySkuId(Integer skuId) {
		return userMapper.getAssistantBySkuId(skuId);
	}

	@Override
	public String getManagerBySkuId(Integer skuId) {
		return userMapper.getManagerBySkuId(skuId);
	}

	@Override
	public List<Position> getPositionByUserId(Integer userId) {

		return positionMapper.getPositionByUserId(userId);
	}

	@Override
	public List<String> getUserListByRole(String roleName) {
		return roleMapper.getUserNameByRoleName(roleName,1);
	}

	@Override
	public String getRealNameByUserName(String userName) {
		if (StringUtil.isBlank(userName)){
			return "";
		}

		//管理员
		StringBuffer realName = new StringBuffer();
		if ("admin".equals(userName) || "njadmin".equals(userName)){
			return realName.append(userName).append("\n").append(" (").append("管理员").append(")").toString();
		}
		//普通用户
		String realNameStr = userMapper.getRealNameByUserName(userName);
		if (StringUtil.isBlank(realNameStr)){
			return userName;
		}
		return realName.append(userName).append("\n").append("(").append(realNameStr).append(")").toString();
	}

	@Override
	public Boolean isUserBelongRoles(UserRoleQuery query) {
		Integer count=userMapper.countUserRoles(query);
		return count!=null&&count>0;
	}

	@Override
	public List<User> selectAllAfterSaleDirector() {
		return userMapper.selectAllAfterSaleDirector();
	}

	@Override
	public List<Integer> getAllSubordinateByUserId(Integer userId) {
		List<User> userList = userMapper.getAllValidUserByParentId(1);
		List<Integer> subordinate = new ArrayList<>();
		getAllChildrenByParentId(userList,userId,subordinate);
		subordinate.add(userId);
		return subordinate;
	}

	@Override
	public Boolean checkUserInDepartList(Integer userId, List<Integer> departIdList) {
		return userMapper.getRolesCountByUserIdAndOrgId(userId,departIdList) > 0;
	}

    @Override
    public User getUserInfoByName(String userName) {

		return userMapper.getUserByName(userName);

    }

	@Override
	public User getUserParentInfo(Integer userId) {
		return userMapper.getUserParentInfoByUserId(userId);
	}

	@Override
	public List<User> getUserParentDetailInfo(Integer userId) {
		return userMapper.getUserParentDetailInfoByUserId(userId);
	}

	@Override
	public List<User> getAllGoodsManagersAndAssistant() {
		return userMapper.getAllGoodsManagersAndAssistant();
	}

	@Override
	public UserInfo getDepartAndPositionInfo(Integer userId) {
		return userMapper.getDepartAndPositionInfo(userId);
	}

	@Override
	public List<User> getProductUserByRoleName() {
		return userMapper.getProductUserByRoleName();
	}
	
	@Override
	public List<User> getUserByRoleName(String roleName) {
		return userMapper.getUserByRoleName(roleName);
	}

	private void getAllChildrenByParentId(List<User> users, Integer parentId, List<Integer> children){
		for (User item : users){
			if (parentId != null && parentId.equals(item.getParentId())) {
				children.add(item.getUserId());
				getAllChildrenByParentId(users,item.getUserId(),children);
			}
		}
	}

	@Override
	public Boolean checkRole(User user, String roleName) {
		List<User> userList =  userMapper.getAllSaleMange(roleName);
		if(user != null && user.getUserId() !=null && CollectionUtils.isNotEmpty(userList)){
			Optional<User> first = userList.stream().filter(e -> user.getUserId().equals(e.getUserId())).findFirst();
			if(first.isPresent()){
				return true;
			}
		}
		return false;
	}

	@Override
	public ImUserDto userInfo(String UserName) {
		ImUserDto imUserDto = userMapper.userInfo(UserName);
		return imUserDto;
	}

	@Override
	public ImUserInfoDto findImUserInfo(Integer userId) {
		ImUserInfoDto imUserInfoDto = userMapper.findImUserInfo(userId);
		return imUserInfoDto;
	}

	@Override
	public String findRoleName(Integer userId) {
		return userMapper.findRoleName(userId);
	}


	@Override
	public List<User> getAllExpressCreatorUser(User user) {
		return userMapper.getAllExpressCreatorUser(user);
	}
	@Override
	public List<ImUserInfoDto> queryOrgPositionName(Integer userId) {
		List<ImUserInfoDto> orgPositionNameList = userMapper.queryOrgPositionName(userId);
		return orgPositionNameList;
	}

	@Override
	public List<User> getUserInfo(User user) {
		return userMapper.getUserInfo(user);
	}

	@Override
	public List<User> getUserByParentId(Integer parentId) {
		return userMapper.getUserByParentId(parentId);
	}

	@Override
	public SaleorderUserInfoDto getPositionAndSubUser(User user) {
		Objects.requireNonNull(user,"当前登陆人为空");
		SaleorderUserInfoDto saleorderUserInfoDto = new SaleorderUserInfoDto();
		List<User> userList =  userMapper.getPositionByUserId(user.getUserId());
		if(CollectionUtils.isNotEmpty(userList)){
			User userInfo = userList.stream().filter(e -> e.getOrgId().equals(user.getOrgId())).sorted(Comparator.comparing(User::getPositLevel)).findFirst().orElse(null);
			if(userInfo !=null) {
				saleorderUserInfoDto.setUserId(userInfo.getUserId());
				saleorderUserInfoDto.setUserName(userInfo.getUsername());
				saleorderUserInfoDto.setPositionLevel(userInfo.getPositLevel());
				saleorderUserInfoDto.setPositionType(userInfo.getPositType());
				deduceAllInfo(userInfo,saleorderUserInfoDto);
			}
		} else {
			saleorderUserInfoDto.setUserId(user.getUserId());
			saleorderUserInfoDto.setUserName(user.getUsername());
			saleorderUserInfoDto.setPositionLevel(user.getPositLevel());
			saleorderUserInfoDto.setPositionType(user.getPositType());
			deduceAllInfo(user,saleorderUserInfoDto);
		}
		saleorderUserInfoDto.setSubUserIdList(user.getSubUserIdList());

		return saleorderUserInfoDto;
	}


	@Override
	public List<User> queryOrganizationPositionUser(String orgName,Integer positionType) {

		Set<Integer> integers = getChildrenSetOfOrg(b2bBusinessDivisionId,1);
		List<Integer> collect = new ArrayList<>(integers);

		// 去除空
		List<Integer> collect1 = collect.stream().filter(Objects::nonNull).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(collect)) {
			return userMapper.getUserListByOrgIdListAndPostionType(collect1, 1,positionType);
		}

		return Collections.emptyList();
	}

	@Override
	public List<User> queryOrganizationUser(String orgName) {

		Set<Integer> integers = getChildrenSetOfOrg(b2bBusinessDivisionId,ErpConst.NJ_COMPANY_ID);
		List<Integer> collect = new ArrayList<>(integers);

		// 去除空
		List<Integer> collect1 = collect.stream().filter(Objects::nonNull).collect(Collectors.toList());
		if (CollectionUtils.isNotEmpty(collect)) {
			return userMapper.getUserListByOrgIdListAndPositionType(collect1);
		}

		return Collections.emptyList();
	}

	@Override
	public User getUserParentInfoByUserId(Integer userId) {
		return userMapper.getUserParentInfoByUserId(userId);
	}

	@Override
	public List<User> getUserByUsersOrgIds(Integer userId) {
		return userMapper.getUserByUsersOrgIds(userId);
	}

	@Override
	public List<Integer> getOrgIdsByUsersOrgIds(Integer userId) {
		User user = new User();
		user.setUserId(userId);
		User currentU = userMapper.getUser(user);//修复ez获取组织架构下所有子集问题
		if(currentU == null || StringUtils.isEmpty(currentU.getOrgIdsList())){
			return new ArrayList<>();
		}
		String[] orgIds = currentU.getOrgIdsList().split(",");
		List<Integer> orgIdIntList = new ArrayList<>();
		for (String orgId : orgIds) {
			if(StringUtils.isNotBlank(orgId)){
				orgIdIntList.add(Integer.parseInt(orgId.trim()));
			}
		}
		return userMapper.getOrgIdsByOrgIds(orgIdIntList);
	}

	@Override
	public List<Integer> getUserOrgIdsByUserId(Integer userId) {
		return userMapper.getUserOrgIdsByUserId(userId);
	}

	@Override
	public List<User> getPosTypeUserListByOrgId(Integer orgId, Integer posType) {
		return userMapper.getPosTypeUserListByOrgId(orgId, posType);
	}

	@Override
	public List<Integer> getAllChildrenByParentUser(Integer userId) {
		User queryUser = new User();
		queryUser.setCompanyId(ErpConst.NJ_COMPANY_ID);
		queryUser.setIsDisabled(0);
		List<Integer> allChildrenUser = new ArrayList<>();
		String cacheKeyOfChildrenOfParentUser = dbType + ErpConst.CHILDREN_OF_PARENT_USER + userId;
		if (JedisUtils.exists(cacheKeyOfChildrenOfParentUser)) {
			String json = JedisUtils.get(cacheKeyOfChildrenOfParentUser);
			JSONArray jsonArray = JSONArray.fromObject(json);
			allChildrenUser = (List<Integer>) JSONArray.toCollection(jsonArray, Integer.class);
		}else {
			List<User> allUsersEnabled = userMapper.getAllUser(queryUser);
			allChildrenUser = getAllChildrenOfParent(userId,allUsersEnabled);
			JedisUtils.set(cacheKeyOfChildrenOfParentUser, JsonUtils.convertConllectionToJsonStr(allChildrenUser), 300);
		}
		return allChildrenUser;
	}


	private List<Integer> getAllChildrenOfParent(Integer parentId, List<User> allUserList){
		List<Integer> children = new ArrayList<>();
		getChildrenByRecursion(allUserList,parentId,children);
		return children;
	}


	private void getChildrenByRecursion(List<User> allUserList, Integer parentId, List<Integer> children){
		for (User item : allUserList){
			if (parentId.equals(item.getParentId())) {
				if (children.contains(item.getUserId())){
					logger.error("递归查询所有下级时，存在循环问题：{}",item.getUserId());
					continue;
				}
				children.add(item.getUserId());
				getChildrenByRecursion(allUserList,item.getUserId(),children);
			}
		}
	}


	private void deduceAllInfo(User userInfo, SaleorderUserInfoDto saleorderUserInfoDto) {
		//职级
		if(chiefLevels.contains(userInfo.getPositLevel())){
			saleorderUserInfoDto.setLevelType(CHIEF_LEVEL);
			saleorderUserInfoDto.setPositionName(CHIEF_NAME);
		} else if(managerLevels.contains(userInfo.getPositLevel())){
			saleorderUserInfoDto.setLevelType(MANAGE_LEVEL);
			saleorderUserInfoDto.setPositionName(MANAGE_NAME);
		} else if(supervisorLevels.contains(userInfo.getPositLevel())){
			saleorderUserInfoDto.setLevelType(SUPERVISOR_LEVEL);
			saleorderUserInfoDto.setPositionName(SUPERVISOR_NAME);
		} else if(staffLevels.contains(userInfo.getPositLevel())){
			saleorderUserInfoDto.setLevelType(STAFF_LEVEL);
			saleorderUserInfoDto.setPositionName(STAFF_NAME);
		}
		//是否是B2B

		Integer isB2B = judgeIsB2BOrg(userInfo.getOrgId());
		saleorderUserInfoDto.setIsB2B(isB2B);
	}

	private Integer judgeIsB2BOrg(Integer orgId) {
		if(orgId == null || orgId == 0){
			return 0;
		}
		if(B2B_ORGID.equals(orgId) || AED_ORGID.equals(orgId)){
			return 1;
		}
		Integer parentId = organizationMapper.getParentIdById(orgId);
		return judgeIsB2BOrg(parentId);
	}

	@Override
	public Boolean handleOrgIds(User user) {
		Map targetAndExcludeIds = (Map)com.alibaba.fastjson.JSONObject.parse(orgAndUserIds);
		// 规则1：userId 优先级大于 orgId
		String targetUserIds = (String) targetAndExcludeIds.get("targetUserIds");
		String excludeUserIds = (String) targetAndExcludeIds.get("excludeUserIds");
		String targetOrgIds = (String) targetAndExcludeIds.get("targetOrgIds");
		String excludeOrgIds = (String) targetAndExcludeIds.get("excludeOrgIds");

		// 规则2 同时在target里和exclude里，正负抵消（正常情况下不会出现，防止配置错误，因此兼容一下）
		List<String> targetUserIdList = twoArrayComplement(targetUserIds.split(","),excludeUserIds.split(","));
		List<String> excludeUserIdList = twoArrayComplement(excludeUserIds.split(","),targetUserIds.split(","));
		List<String> targetOrgIdList = twoArrayComplement(targetOrgIds.split(","),excludeOrgIds.split(","));
		List<String> excludeOrgIdList = twoArrayComplement(excludeOrgIds.split(","),targetOrgIds.split(","));

		if (targetUserIdList.contains(user.getUserId().toString())) {
			return true;
		} else if (excludeUserIdList.contains(user.getUserId().toString())) {
			return false;
		}

		// 查询配置项的所属各级部门 去重
		Set<Integer> resultTargetOrgId = new HashSet<>();
		Set<Integer> resultExcludeOrgId = new HashSet<>();

		for (String s : targetOrgIdList) {
			Set<Integer> childrenSetOfOrg = getChildrenSetOfOrgByCache(Integer.valueOf(s), 1);
			resultTargetOrgId = Stream.concat(resultTargetOrgId.stream(), childrenSetOfOrg.stream()).collect(Collectors.toSet());
		}

		for (String s : excludeOrgIdList) {
			Set<Integer> childrenSetOfOrg = getChildrenSetOfOrgByCache(Integer.valueOf(s), 1);
			resultExcludeOrgId = Stream.concat(resultExcludeOrgId.stream(), childrenSetOfOrg.stream()).collect(Collectors.toSet());
		}

		// 判断当前用户的orgId 是否在目标部门集合中
		if (resultExcludeOrgId.contains(user.getOrgId())) {
			return false;
		} else {
			return resultTargetOrgId.contains(user.getOrgId()) || resultTargetOrgId.size() == 0;
		}


	}

	/**
	 * 两个数组取 相对补集 （且去除空字符串）
	 * @param source 被减数
	 * @param exclude 减数
	 * @return
	 */
	private List<String> twoArrayComplement(String[] source, String[] exclude) {
		List<String> resultList = new ArrayList<>();
		// 先将source去重 去空字符 存入结果集
		for (String s : source) {
			if ((!"".equals(s)) && !(resultList.contains(s))) {
				resultList.add(s);
			}
		}

		// 再将exclude与resultList相比 去重、去空字符，排除同在source和exclude中的元素
		for (String s : exclude) {
			resultList.remove(s);
		}
		return resultList;
	}

	@Override
	public void callLineInfoSave(OrgCallLineDto orgCallLineDto) {
		if (orgCallLineDto == null || CollectionUtils.isEmpty(orgCallLineDto.getUserIds())){
			return;
		}
		if (OrgCallLineDto.isUnValidData(orgCallLineDto)){
			return;
		}
		OrgCallLineDto.trimAllLine(orgCallLineDto);
		userMapper.updateCallLineInfo(orgCallLineDto);
	}

	@Override
	public Boolean getSaleAndB2BFlagByUserId(User user) {
		if (user == null) {
			return false;
		}
		int saleAndB2B = isSaleAndB2B(user);
		if (saleAndB2B == 1){
			return true;
		}
		return false;
	}

	@Override
	public Boolean getQualityFlagByUserId(User user) {
		//判断是否是质量专员
		boolean isSaleFlag = false;
		List<Position> positions = user.getPositions();
		if (CollectionUtils.isNotEmpty(positions)) {
			List<Integer> typeIds = positions.stream().map(Position::getType).collect(Collectors.toList());
			if(typeIds.contains(SysOptionConstant.ID_589)){
				isSaleFlag = true;
			}
		}else {
			if (user.getPositType()!=null&&user.getPositType().equals(SysOptionConstant.ID_589)){
				isSaleFlag = true;
			}
		}
		return isSaleFlag;
	}


	/**
	 * 判断是否是B2B部门
	 * @param orgId
	 * @return
	 */
	private Integer judgeIsB2BOrgv(Integer orgId) {
		if(orgId == null || orgId == 0){
			return 0;
		}
		if(B2B_ORGID.equals(orgId)){
			return 1;
		}
		Integer parentId = organizationMapper.getParentIdById(orgId);
		return judgeIsB2BOrgv(parentId);
	}

	/**
	 * 判断是否是B2B  附加判断如果是b2b销售支持和商务运营不放行
	 * @param sessionUser
	 * @return
	 */
	private int isSaleAndB2B(User sessionUser) {
		Integer orgId = sessionUser.getOrgId();
		if (Objects.isNull(orgId)) {
			return Constants.ZERO;
		}
		//1.判断一级部门是否是b2b事业部
		Integer isB2B = judgeIsB2BOrgv(orgId);
		if (Objects.isNull(isB2B) || Constants.ZERO.equals(isB2B)) {
			return Constants.ZERO;
		}
		//2.判断是否是b2b事业部下的销售支持和商务运营部门
		if (StringUtils.isNotBlank(b2bServiceOrgids) &&
				Arrays.stream(b2bServiceOrgids.split(",")).collect(Collectors.toList()).contains(String.valueOf(orgId))) {
			return Constants.ZERO;
		}
		//3.判断是否是销售
		List<Position> positions = sessionUser.getPositions();
		if (CollectionUtils.isNotEmpty(positions) &&
				!positions.stream().map(Position::getType).collect(Collectors.toList()).contains(SysOptionConstant.ID_310)) {
			return Constants.ZERO;
		}
		return Constants.ONE;
	}

	@Override
	public List<User> getAfterSalesDirectorList() {
		return userMapper.getAfterSalesDirectorList();
	}


	@Override
	public List<User> exincludeLiZhiUser(List<User> userList) {
		if(CollectionUtil.isEmpty(userList)){
			return userList;
		}
		List<Integer> userIdList = userList.stream()
				.map(User::getUserId)
				.collect(Collectors.toList());
		try{
			RestfulResult<List<WxUserDto>> result = uacWxUserInfoApiService.getWxUserListByUserId(userIdList);
			if(result.isSuccess()){
				List<WxUserDto> wxUserDtoList = result.getData();
				List<Integer> oldIdList = wxUserDtoList.stream().map(WxUserDto::getUserId).collect(Collectors.toList());
				return userList.stream().filter(user -> !oldIdList.contains(user.getUserId())).collect(Collectors.toList());
			}else{
				return userList;
			}
		}catch (Exception e){
			logger.error("查询uac微信用户信息判断是否已离职失败",e);
			return userList;

		}







	}


	@Autowired
	private UacWxUserInfoApiService uacWxUserInfoApiService;

	/**
	 * 根据权限uri判断该用户是否拥有这个权限
	 *
	 * @param uri 权限uri
	 * @return Boolean
	 */
	@Override
    public Boolean judgeHasPermission(String uri) {
        try {
            logger.info("judgeHasPermission结果：{}", SecurityUtils.getSubject().isPermitted(uri));
            return SecurityUtils.getSubject().isPermitted(uri);
        } catch (Exception e) {
            logger.error("判断用户是否拥有某权限异常", e);
            return false;
        }
    }

	@Override
	public List<User> searchUserList(String username,boolean filter) {
		List<User> userList = userMapper.searchUserList(username);
		if(filter) {
			// 排除当前登陆人
			CurrentUser currentUser = CurrentUser.getCurrentUser();
			return userList.stream().filter(user -> !user.getUserId().equals(currentUser.getId())).collect(Collectors.toList());
		}
		return userList;
	}

	@Override
	public List<User> searchUserListHas(String username){
		List<User> userList = userMapper.searchUserListHasDelete(username);
		return userList;
	}



	@Override
	public List<User> searchUserListForSelect(String username){
		List<User> userList = userMapper.searchUserList(username);
		return userList;
	}

	/**
	 * 根据userId精确搜索所有启用的用户集合(不包括自己)
	 *
	 * @param username 用户名
	 * @return List<User>
	 */
	@Override
	public List<User> searchUserListById(Integer userId){
		List<User> userList = new ArrayList<>();
		if(userId != null){
			User user = userMapper.selectByPrimaryKey(userId);
			if(user != null){
				userList.add(user);
			}
		}
		return userList;
	}

	@Override
	public List<User> getAllUserIdList() {
		return userMapper.getAllUserIdList();
	}

	@Override
	public User getBDUserInfoByTraderId(Integer traderId) {
		return userMapper.getBDUserInfoByTraderId(traderId);
	}
}
