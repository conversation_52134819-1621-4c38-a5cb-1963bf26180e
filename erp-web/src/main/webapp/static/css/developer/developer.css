
/*全局 开始*/
body{
	background: #a2cd86;
}
.fix_div{
	position: fixed;
	z-index: 1234;
	
	text-align: center;
	top: 50%;
	right: 30px;
}
.fix_div ul li{
	width: 100px;
	height: 30px;
	line-height: 30px;
	background: rgba(255,255,255,0.6);
	border-radius: 4px;
	margin-bottom: 2px;
}
.fix_div ul li.active{
	background: rgba(255,255,255,1);
}
.fix_div ul li a{
	padding: 4px;
	color: #48D441;
	font-size: 16px;
}

.icon {
   width: 1em; height: 1em;
   vertical-align: -0.15em;
   fill: currentColor;
   overflow: hidden;
}
.main-container{
	border-radius: 4px;
}
.developer-box{
	color: #fff;
	font-size: 16px;
	padding:  10px;
	margin: 10px;
}
.box-1{
	background: rgba(0,0,0,0.2);
	border: 1px dashed #a2cd86;
	padding: 10px 10px 30px 10px;
	position: relative;
	border-radius: 4px;
}
.box-1 h1{
	font-size: 24px;
	font-weight: bold;
	color: #fff;
	margin-top: 0;
	text-align: center;
}
.box-1 em{
	font-size: 20px;
	color: #f00;
	padding: 0 4px;
	font-weight: bold;
}
.box-1 .box-child-1{
	padding-left: 20px;
	margin-bottom: 10px;
}
.example-1 pre{
	margin: 0;
	font-size: 18px;
	font-weight: bold;
	color:  gold;
	display: inline-block;
	width: 1000px;
	white-space: normal;
	word-wrap: break-word;
	padding-left:  36px;
	line-height: 29px;
}
.example-1 .tips{
	color: yellow ;
	margin: 0;
}

/*全局 结束*/
/*第一部分html 开始*/
.main-container1{
	background:#f8cbd9;
}
/*第一部分html 结束*/
/*第二部分css 开始*/
.main-container2{
	background:#dba4ff;
}

.icon-list li,.font-color li{
	float: left;
}
.developer-title{
	font-weight: bold;
	margin-bottom: 10px;
}
/*第二部分css 结束*/
/*第三部分js 开始*/
.main-container3{
	background:url(../../images/developer/t5.jpg);
	color: #000;
}
.describe{
	font-size: 16px;
	margin: 10px 20px;
}
.methods1{
	margin: 10px 20px;
}
.main-title{
	font-size: 18px;
	font-weight: bold;
}
/*第三部分js 结束*/
.print{
	font-size: 1rem;
}
