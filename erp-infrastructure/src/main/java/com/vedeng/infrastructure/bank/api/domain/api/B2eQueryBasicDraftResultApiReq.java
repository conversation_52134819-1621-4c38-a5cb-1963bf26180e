package com.vedeng.infrastructure.bank.api.domain.api;

import com.vedeng.infrastructure.bank.api.domain.B2eQueryBasicDraftResultReq;
import com.vedeng.infrastructure.bank.api.domain.base.CMBCRequest;
import com.vedeng.infrastructure.bank.api.enums.BankApiType;

import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSeeAlso;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 融资申请结果查询
 * @date 2024/10/14 14:46
 */
@XmlRootElement(name = "CMBC")
@XmlSeeAlso({B2eQueryBasicDraftResultReq.class})
public class B2eQueryBasicDraftResultApiReq extends CMBCRequest<B2eQueryBasicDraftResultReq> {

    private static final String API_CODE = BankApiType.B2E_QUERY_BASIC_DRAFT_RESULT.getTrnCode();

    public B2eQueryBasicDraftResultApiReq() {
        initTrnCode();
    }

    public B2eQueryBasicDraftResultApiReq(B2eQueryBasicDraftResultReq xDataBody) {
        super.setXDataBody(xDataBody);
        initTrnCode();
    }

    @Override
    protected void initTrnCode() {
        setTrnCode(API_CODE);
    }

}
