<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.WmsOutputOrderGoodsExtraMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.WmsOutputOrderGoodsExtra">
    <!--@mbg.generated-->
    <!--@Table T_WMS_OUTPUT_ORDER_GOODS_EXTRA-->
    <id column="WAREHOUSE_GOODS_EXTRA_ID" jdbcType="BIGINT" property="warehouseGoodsExtraId" />
    <result column="WMS_OUTPUT_ORDER_ID" jdbcType="BIGINT" property="wmsOutputOrderId" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="PRODUCT_BELONG_NAME_INFO" jdbcType="VARCHAR" property="productBelongNameInfo" />
    <result column="PRODUCT_BELONG_ID_INFO" jdbcType="VARCHAR" property="productBelongIdInfo" />
    <result column="PRODUCT_AUDIT" jdbcType="BOOLEAN" property="productAudit" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WAREHOUSE_GOODS_EXTRA_ID, WMS_OUTPUT_ORDER_ID, SKU_NO, PRODUCT_BELONG_NAME_INFO, 
    PRODUCT_BELONG_ID_INFO, PRODUCT_AUDIT, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, 
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER_GOODS_EXTRA
    where WAREHOUSE_GOODS_EXTRA_ID = #{warehouseGoodsExtraId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_WMS_OUTPUT_ORDER_GOODS_EXTRA
    where WAREHOUSE_GOODS_EXTRA_ID = #{warehouseGoodsExtraId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="WAREHOUSE_GOODS_EXTRA_ID" keyProperty="warehouseGoodsExtraId" parameterType="com.vedeng.logistics.model.WmsOutputOrderGoodsExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_OUTPUT_ORDER_GOODS_EXTRA (WMS_OUTPUT_ORDER_ID, SKU_NO, PRODUCT_BELONG_NAME_INFO, 
      PRODUCT_BELONG_ID_INFO, PRODUCT_AUDIT, IS_DELETE, 
      ADD_TIME, CREATOR, CREATOR_NAME, 
      MOD_TIME, UPDATER, UPDATER_NAME, 
      REMARK, UPDATE_REMARK)
    values (#{wmsOutputOrderId,jdbcType=BIGINT}, #{skuNo,jdbcType=VARCHAR}, #{productBelongNameInfo,jdbcType=VARCHAR}, 
      #{productBelongIdInfo,jdbcType=VARCHAR}, #{productAudit,jdbcType=BOOLEAN}, #{isDelete,jdbcType=BOOLEAN}, 
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="WAREHOUSE_GOODS_EXTRA_ID" keyProperty="warehouseGoodsExtraId" parameterType="com.vedeng.logistics.model.WmsOutputOrderGoodsExtra" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WMS_OUTPUT_ORDER_GOODS_EXTRA
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="wmsOutputOrderId != null">
        WMS_OUTPUT_ORDER_ID,
      </if>
      <if test="skuNo != null">
        SKU_NO,
      </if>
      <if test="productBelongNameInfo != null">
        PRODUCT_BELONG_NAME_INFO,
      </if>
      <if test="productBelongIdInfo != null">
        PRODUCT_BELONG_ID_INFO,
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="wmsOutputOrderId != null">
        #{wmsOutputOrderId,jdbcType=BIGINT},
      </if>
      <if test="skuNo != null">
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null">
        #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongIdInfo != null">
        #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        #{productAudit,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WmsOutputOrderGoodsExtra">
    <!--@mbg.generated-->
    update T_WMS_OUTPUT_ORDER_GOODS_EXTRA
    <set>
      <if test="wmsOutputOrderId != null">
        WMS_OUTPUT_ORDER_ID = #{wmsOutputOrderId,jdbcType=BIGINT},
      </if>
      <if test="skuNo != null">
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongNameInfo != null">
        PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      </if>
      <if test="productBelongIdInfo != null">
        PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      </if>
      <if test="productAudit != null">
        PRODUCT_AUDIT = #{productAudit,jdbcType=BOOLEAN},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where WAREHOUSE_GOODS_EXTRA_ID = #{warehouseGoodsExtraId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.WmsOutputOrderGoodsExtra">
    <!--@mbg.generated-->
    update T_WMS_OUTPUT_ORDER_GOODS_EXTRA
    set WMS_OUTPUT_ORDER_ID = #{wmsOutputOrderId,jdbcType=BIGINT},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      PRODUCT_BELONG_NAME_INFO = #{productBelongNameInfo,jdbcType=VARCHAR},
      PRODUCT_BELONG_ID_INFO = #{productBelongIdInfo,jdbcType=VARCHAR},
      PRODUCT_AUDIT = #{productAudit,jdbcType=BOOLEAN},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where WAREHOUSE_GOODS_EXTRA_ID = #{warehouseGoodsExtraId,jdbcType=BIGINT}
  </update>

  <select id="selectWmsExtraByLendOutId" parameterType="com.vedeng.logistics.model.WmsOutputOrderGoodsExtra" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_WMS_OUTPUT_ORDER_GOODS_EXTRA
    where WMS_OUTPUT_ORDER_ID = #{wmsOutputOrderId,jdbcType=BIGINT}
    <if test="productAudit != null">
    and PRODUCT_AUDIT =#{productAudit,jdbcType=INTEGER}
    </if>
  </select>
</mapper>