package com.vedeng.kpi.model.DTO.emptyEntity;

import com.vedeng.kpi.model.DTO.KpiUserConfigDto;

import java.math.BigDecimal;

/**
 * @description: null扩展类.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:37 上午.
 * @author: Tomcat.Hui.
 */
public class NullKpiUserConfigDto extends KpiUserConfigDto {

    public NullKpiUserConfigDto(){
        this.setMonthAmountGoal(BigDecimal.ZERO);
        this.setUserName("");
        this.setGroupName("");
        this.setTeamName("");
    }
}
