<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderAddressMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderAddressEntity">
    <!--@mbg.generated-->
    <!--@Table T_TRADER_ADDRESS-->
    <id column="TRADER_ADDRESS_ID" jdbcType="INTEGER" property="traderAddressId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="IS_ENABLE" jdbcType="INTEGER" property="isEnable" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="AREA_IDS" jdbcType="VARCHAR" property="areaIds" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="IS_DEFAULT" jdbcType="INTEGER" property="isDefault" />
    <result column="ZIP_CODE" jdbcType="VARCHAR" property="zipCode" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_TOP" jdbcType="INTEGER" property="isTop" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TRADER_ADDRESS_ID, TRADER_ID, TRADER_TYPE, IS_ENABLE, AREA_ID, AREA_IDS, ADDRESS, 
    IS_DEFAULT, ZIP_CODE, COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_TOP
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_TRADER_ADDRESS
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_TRADER_ADDRESS
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="TRADER_ADDRESS_ID" keyProperty="traderAddressId" parameterType="com.vedeng.erp.trader.domain.entity.TraderAddressEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_ADDRESS (TRADER_ID, TRADER_TYPE, IS_ENABLE, 
      AREA_ID, AREA_IDS, ADDRESS, 
      IS_DEFAULT, ZIP_CODE, COMMENTS, 
      ADD_TIME, CREATOR, MOD_TIME, 
      UPDATER, IS_TOP)
    values (#{traderId,jdbcType=INTEGER}, #{traderType,jdbcType=INTEGER}, #{isEnable,jdbcType=INTEGER}, 
      #{areaId,jdbcType=INTEGER}, #{areaIds,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{isDefault,jdbcType=INTEGER}, #{zipCode,jdbcType=VARCHAR}, #{comments,jdbcType=VARCHAR}, 
      #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=BIGINT}, 
      #{updater,jdbcType=INTEGER}, #{isTop,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="TRADER_ADDRESS_ID" keyProperty="traderAddressId" parameterType="com.vedeng.erp.trader.domain.entity.TraderAddressEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="isEnable != null">
        IS_ENABLE,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="areaIds != null">
        AREA_IDS,
      </if>
      <if test="address != null">
        ADDRESS,
      </if>
      <if test="isDefault != null">
        IS_DEFAULT,
      </if>
      <if test="zipCode != null">
        ZIP_CODE,
      </if>
      <if test="comments != null">
        COMMENTS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isTop != null">
        IS_TOP,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=INTEGER},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isTop != null">
        #{isTop,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.TraderAddressEntity">
    <!--@mbg.generated-->
    update T_TRADER_ADDRESS
    <set>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null">
        IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="areaIds != null">
        AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="isDefault != null">
        IS_DEFAULT = #{isDefault,jdbcType=INTEGER},
      </if>
      <if test="zipCode != null">
        ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="comments != null">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isTop != null">
        IS_TOP = #{isTop,jdbcType=INTEGER},
      </if>
    </set>
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.TraderAddressEntity">
    <!--@mbg.generated-->
    update T_TRADER_ADDRESS
    set TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      IS_ENABLE = #{isEnable,jdbcType=INTEGER},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      AREA_IDS = #{areaIds,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      IS_DEFAULT = #{isDefault,jdbcType=INTEGER},
      ZIP_CODE = #{zipCode,jdbcType=VARCHAR},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_TOP = #{isTop,jdbcType=INTEGER}
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}
  </update>
  <insert id="batchInsert" keyColumn="TRADER_ADDRESS_ID" keyProperty="traderAddressId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_TRADER_ADDRESS
    (TRADER_ID, TRADER_TYPE, IS_ENABLE, AREA_ID, AREA_IDS, ADDRESS, IS_DEFAULT, ZIP_CODE, 
      COMMENTS, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_TOP)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.traderId,jdbcType=INTEGER}, #{item.traderType,jdbcType=INTEGER}, #{item.isEnable,jdbcType=INTEGER}, 
        #{item.areaId,jdbcType=INTEGER}, #{item.areaIds,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, 
        #{item.isDefault,jdbcType=INTEGER}, #{item.zipCode,jdbcType=VARCHAR}, #{item.comments,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=BIGINT}, #{item.creator,jdbcType=INTEGER}, #{item.modTime,jdbcType=BIGINT}, 
        #{item.updater,jdbcType=INTEGER}, #{item.isTop,jdbcType=INTEGER})
    </foreach>
  </insert>

  <select id="findTraderAddressById" resultType="com.vedeng.erp.trader.dto.TraderAddressDto">
    select
    <include refid="Base_Column_List" />
    from T_TRADER_ADDRESS
    where TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER}

  </select>

  <select id="findAll" resultType="com.vedeng.erp.trader.dto.TraderAddressDto" parameterType="com.vedeng.erp.trader.dto.TraderAddressDto">
    select
    <include refid="Base_Column_List" />
    from T_TRADER_ADDRESS
    <where>
      TRADER_ID = #{traderId,jdbcType=INTEGER}
      <if test="traderType != null">
        and TRADER_TYPE = #{traderType,jdbcType=INTEGER}
      </if>
      <if test="isEnable != null">
        and IS_ENABLE = #{isEnable,jdbcType=INTEGER}
      </if>
      <if test="areaId != null">
        and AREA_ID = #{areaId,jdbcType=INTEGER}
      </if>
      <if test="areaIds != null">
        and AREA_IDS = #{areaIds,jdbcType=VARCHAR}
      </if>
      <if test="address != null">
        and ADDRESS = #{address,jdbcType=VARCHAR}
      </if>
      <if test="isDefault != null">
        and IS_DEFAULT = #{isDefault,jdbcType=INTEGER}
      </if>
      <if test="zipCode != null">
        and ZIP_CODE = #{zipCode,jdbcType=VARCHAR}
      </if>
      <if test="comments != null">
        and COMMENTS = #{comments,jdbcType=VARCHAR}
      </if>

      <if test="isTop != null">
        and IS_TOP = #{isTop,jdbcType=INTEGER}
      </if>
    </where>
  </select>
  <select id="getLatestTransactionAddress" resultType="java.lang.Integer">
      SELECT
          *
      FROM
          (
              SELECT
                  tta.TRADER_ADDRESS_ID,
                  MAX(ts.ADD_TIME) AS ADD_TIME
              FROM
                  T_TRADER_ADDRESS tta
                      LEFT JOIN T_SALEORDER ts ON
                          tta.TRADER_ADDRESS_ID = ts.TAKE_TRADER_ADDRESS_ID
                          AND ts.TRADER_ID = #{traderId,jdbcType=INTEGER}
              WHERE
                  tta.TRADER_ID = #{traderId,jdbcType=INTEGER}
                AND ts.PAYMENT_STATUS IN (1, 2)
              group by
                  tta.TRADER_ADDRESS_ID
          ) A
      ORDER BY
          ADD_TIME DESC
      LIMIT 2
  </select>
  <select id="getByIdList" resultType="com.vedeng.erp.trader.dto.TraderAddressDto">
    SELECT
      A.TRADER_ADDRESS_ID,
      A.TRADER_ID,
      CONCAT_WS(' ', R3.REGION_NAME, R2.REGION_NAME, R1.REGION_NAME) AS area,
      A.ADDRESS,
      A.ZIP_CODE,
      A.IS_DEFAULT,
      A.COMMENTS,
      A.IS_ENABLE,
      A.AREA_ID,
      A.AREA_IDS,
      A.IS_TOP
    FROM
      T_TRADER_ADDRESS A
        LEFT JOIN
      T_REGION R1
      ON
          R1.REGION_ID = A.AREA_ID
          AND R1.REGION_ID > 100000
        LEFT JOIN
      T_REGION R2
      ON
          R1.PARENT_ID = R2.REGION_ID
          AND R2.REGION_ID> 100000
        LEFT JOIN
      T_REGION R3
      ON
          R2.PARENT_ID = R3.REGION_ID
          AND R3.REGION_ID >100000
    WHERE
    A.TRADER_TYPE = 1
    AND A.TRADER_ADDRESS_ID IN
    <foreach item="traderAddressId" index="index" collection="traderAddressIdList" open="(" separator="," close=")">
      #{traderAddressId,jdbcType=INTEGER}
    </foreach>

  </select>



<!--auto generated by MybatisCodeHelper on 2025-01-15-->
  <select id="findByTraderIdAndTraderTypeAndAddressLike" resultType="com.vedeng.erp.trader.dto.TraderAddressDto">
    select TTA.*,
           TR.REGION_NAME as area
    from T_TRADER_ADDRESS TTA
           left join T_REGION TR on TR.REGION_ID = TTA.AREA_ID
    where TTA.TRADER_ID = #{traderId,jdbcType=INTEGER}
      and TTA.TRADER_TYPE = #{traderType,jdbcType=INTEGER}
    <if test="keywords != null and keywords != ''">
      and TTA.ADDRESS like
          concat('%', #{keywords,jdbcType=VARCHAR}, '%')
    </if>
    and TTA.IS_ENABLE = 1
    group by TTA.TRADER_ADDRESS_ID
    limit  100
  </select>
</mapper>