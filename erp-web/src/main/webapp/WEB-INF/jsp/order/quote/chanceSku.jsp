<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增沟通记录" scope="application" />
<%@ include file="../../common/common.jsp"%>

		<table
				class="table table-bordered table-striped table-condensed table-centered">
			<thead>
			<tr>
				<th class="wid14">sku名称</th>
				<th class="wid14">sku品牌</th>
				<%--<th class="wid14">型号</th>--%>
				<th class="wid6">操作</th>
			</tr>
			</thead>
			<tbody>
			<c:forEach var="authorizationSkuInfo" items="${authorizationSkuInfos}" varStatus="num">
				<tr>
				<c:if test="${authorizationSkuInfo.isChance eq 0}">
					<td>${authorizationSkuInfo.skuName}</td>
					<td>${authorizationSkuInfo.brandName}</td>
					<%--<td>${authorizationSkuInfo.skuModel}</td>--%>
					<td style="text-align: center"><span style="color: #1E9FFF" onclick="chanceSku('${authorizationSkuInfo.skuName}','${authorizationSkuInfo.brandName}','${authorizationSkuInfo.skuModel}','${authorizationSkuInfo.skuId}')">选择</span></td>
				</c:if>
				</tr>
				<c:if test="${authorizationSkuInfo.isChance eq 1}">
				<tr style="color: #5e5e5e">
					<td>${authorizationSkuInfo.skuName}</td>
					<td>${authorizationSkuInfo.brandName}</td>
					<%--<td>${authorizationSkuInfo.skuModel}</td>--%>
					<td>/</td>
				</tr>
				</c:if>
			</c:forEach>
			<c:if test="${empty authorizationSkuInfos}">
				<tr>
					<td colspan="3">
						<!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
					</td>
				</tr>
			</c:if>
			</tbody>
		</table>


	<script type="application/javascript">
		function chanceSku(skuName,brandName,skuModel,skuId) {
			window.parent.reLook(skuName,brandName,skuModel,skuId);
		}
	</script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/quote/add_communicate.js?rnd=${resourceVersionKey}"></script>
	<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
