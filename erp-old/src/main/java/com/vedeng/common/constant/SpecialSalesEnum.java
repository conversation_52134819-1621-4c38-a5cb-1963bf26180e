package com.vedeng.common.constant;

/**
 * 特麦帮标记枚举类
 */
public enum SpecialSalesEnum {

    BUSINESS_CHANCE(1,"商机"),
    QUOTEORDER(2,"报价"),
    SALEORDER(3,"销售订单"),
    BUYORDER(4,"采购订单"),
    BUYORDER_EXPENSE(5, "采购费用订单")
    ;

    private Integer code;

    private String name;

    SpecialSalesEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}
