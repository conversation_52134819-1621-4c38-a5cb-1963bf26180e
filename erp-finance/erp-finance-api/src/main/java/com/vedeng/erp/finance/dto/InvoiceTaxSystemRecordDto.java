package com.vedeng.erp.finance.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 税金系统记录表
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InvoiceTaxSystemRecordDto extends BaseDto implements Serializable {
    /**
     * 主键
     */
    private Long invoiceTaxSystemRecordId;

    /**
     * 业务主键 INVOICE_APPLY_ID
     */
    private String businessId;

    /**
     * 接口类型：SALE_OPEN 销项票开具、SALE_DOWN 销项票下载
     */
    private String interfaceType;

    /**
     * 执行状态：SUCCESS 成功 FAIL 失败
     */
    private String runningStatus;

    /**
     * 重试次数 0表示未执行过
     */
    private Integer retryNum;

    /**
     * 是否删除 0否 1是
     */
    private Integer isDelete;

    /**
     * 发票PDF链接 用于交付
     */
    private String urlPdf;

    /**
     * 发票XML链接 用于审计
     */
    private String urlXml;

    /**
     * 更新备注
     */
    private String updateRemark;

    /**
     * 消息体-原始
     */
    private String bodyOrg;

    /**
     * 接口返回值
     */
    private String result;

    /**
     * 发票号码
     */
    private String invoiceNo;

    private static final long serialVersionUID = 1L;
}