<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsExtendGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <id column="GOODS_EXTEND_ID" jdbcType="INTEGER" property="goodsExtendId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="CUSTOMER_NAMES" jdbcType="VARCHAR" property="customerNames" />
    <result column="SELLING_WORDS" jdbcType="VARCHAR" property="sellingWords" />
    <result column="MARKET_STRATEGY" jdbcType="VARCHAR" property="marketStrategy" />
    <result column="PROMOTION_POLICY" jdbcType="VARCHAR" property="promotionPolicy" />
    <result column="WARRANTY_PERIOD" jdbcType="VARCHAR" property="warrantyPeriod" />
    <result column="WARRANTY_PERIOD_RULE" jdbcType="VARCHAR" property="warrantyPeriodRule" />
    <result column="WARRANTY_REPAIR_FEE" jdbcType="DECIMAL" property="warrantyRepairFee" />
    <result column="RESPONSE_TIME" jdbcType="VARCHAR" property="responseTime" />
    <result column="HAVE_STANDBY_MACHINE" jdbcType="TINYINT" property="haveStandbyMachine" />
    <result column="CONDITIONS" jdbcType="VARCHAR" property="conditions" />
    <result column="EXTENDED_WARRANTY_FEE" jdbcType="VARCHAR" property="extendedWarrantyFee" />
    <result column="IS_REFUND" jdbcType="TINYINT" property="isRefund" />
    <result column="EXCHANGE_CONDITIONS" jdbcType="VARCHAR" property="exchangeConditions" />
    <result column="EXCHANGE_MODE" jdbcType="VARCHAR" property="exchangeMode" />
    <result column="FREIGHT_DESCRIPTION" jdbcType="VARCHAR" property="freightDescription" />
    <result column="DELIVERY" jdbcType="VARCHAR" property="delivery" />
    <result column="FUTURES_DELIVERY" jdbcType="VARCHAR" property="futuresDelivery" />
    <result column="TRANSPORT_REQUIREMENTS" jdbcType="VARCHAR" property="transportRequirements" />
    <result column="TRANSPORT_WEIGHT" jdbcType="VARCHAR" property="transportWeight" />
    <result column="IS_HVAE_FREIGHT" jdbcType="TINYINT" property="isHvaeFreight" />
    <result column="TRANSPORTATION_COMPLETION_STANDARD" jdbcType="INTEGER" property="transportationCompletionStandard" />
    <result column="ACCEPTANCE_NOTICE" jdbcType="VARCHAR" property="acceptanceNotice" />
    <result column="PACKING_NUMBER" jdbcType="VARCHAR" property="packingNumber" />
    <result column="PACKING_QUANTITY" jdbcType="VARCHAR" property="packingQuantity" />
    <result column="ADVANTAGE" jdbcType="VARCHAR" property="advantage" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <result column="COMPETITIVE_ANALYSIS" jdbcType="LONGVARCHAR" property="competitiveAnalysis" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    GOODS_EXTEND_ID, GOODS_ID, CUSTOMER_NAMES, SELLING_WORDS, MARKET_STRATEGY, PROMOTION_POLICY, 
    WARRANTY_PERIOD, WARRANTY_PERIOD_RULE, WARRANTY_REPAIR_FEE, RESPONSE_TIME, HAVE_STANDBY_MACHINE, 
    CONDITIONS, EXTENDED_WARRANTY_FEE, IS_REFUND, EXCHANGE_CONDITIONS, EXCHANGE_MODE, 
    FREIGHT_DESCRIPTION, DELIVERY, FUTURES_DELIVERY, TRANSPORT_REQUIREMENTS, TRANSPORT_WEIGHT, 
    IS_HVAE_FREIGHT, TRANSPORTATION_COMPLETION_STANDARD, ACCEPTANCE_NOTICE, PACKING_NUMBER, 
    PACKING_QUANTITY, ADVANTAGE
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    COMPETITIVE_ANALYSIS
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.vedeng.goods.model.GoodsExtendGenerateExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_GOODS_EXTEND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.GoodsExtendGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_GOODS_EXTEND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from T_GOODS_EXTEND
    where GOODS_EXTEND_ID = #{goodsExtendId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_GOODS_EXTEND
    where GOODS_EXTEND_ID = #{goodsExtendId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.GoodsExtendGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    delete from T_GOODS_EXTEND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="goodsExtendId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_EXTEND (GOODS_ID, CUSTOMER_NAMES, SELLING_WORDS, 
      MARKET_STRATEGY, PROMOTION_POLICY, WARRANTY_PERIOD, 
      WARRANTY_PERIOD_RULE, WARRANTY_REPAIR_FEE, 
      RESPONSE_TIME, HAVE_STANDBY_MACHINE, CONDITIONS, 
      EXTENDED_WARRANTY_FEE, IS_REFUND, EXCHANGE_CONDITIONS, 
      EXCHANGE_MODE, FREIGHT_DESCRIPTION, DELIVERY, 
      FUTURES_DELIVERY, TRANSPORT_REQUIREMENTS, 
      TRANSPORT_WEIGHT, IS_HVAE_FREIGHT, TRANSPORTATION_COMPLETION_STANDARD, 
      ACCEPTANCE_NOTICE, PACKING_NUMBER, PACKING_QUANTITY, 
      ADVANTAGE, COMPETITIVE_ANALYSIS)
    values (#{goodsId,jdbcType=INTEGER}, #{customerNames,jdbcType=VARCHAR}, #{sellingWords,jdbcType=VARCHAR}, 
      #{marketStrategy,jdbcType=VARCHAR}, #{promotionPolicy,jdbcType=VARCHAR}, #{warrantyPeriod,jdbcType=VARCHAR}, 
      #{warrantyPeriodRule,jdbcType=VARCHAR}, #{warrantyRepairFee,jdbcType=DECIMAL}, 
      #{responseTime,jdbcType=VARCHAR}, #{haveStandbyMachine,jdbcType=TINYINT}, #{conditions,jdbcType=VARCHAR}, 
      #{extendedWarrantyFee,jdbcType=VARCHAR}, #{isRefund,jdbcType=TINYINT}, #{exchangeConditions,jdbcType=VARCHAR}, 
      #{exchangeMode,jdbcType=VARCHAR}, #{freightDescription,jdbcType=VARCHAR}, #{delivery,jdbcType=VARCHAR}, 
      #{futuresDelivery,jdbcType=VARCHAR}, #{transportRequirements,jdbcType=VARCHAR}, 
      #{transportWeight,jdbcType=VARCHAR}, #{isHvaeFreight,jdbcType=TINYINT}, #{transportationCompletionStandard,jdbcType=INTEGER}, 
      #{acceptanceNotice,jdbcType=VARCHAR}, #{packingNumber,jdbcType=VARCHAR}, #{packingQuantity,jdbcType=VARCHAR}, 
      #{advantage,jdbcType=VARCHAR}, #{competitiveAnalysis,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    <selectKey keyProperty="goodsExtendId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_EXTEND
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="customerNames != null">
        CUSTOMER_NAMES,
      </if>
      <if test="sellingWords != null">
        SELLING_WORDS,
      </if>
      <if test="marketStrategy != null">
        MARKET_STRATEGY,
      </if>
      <if test="promotionPolicy != null">
        PROMOTION_POLICY,
      </if>
      <if test="warrantyPeriod != null">
        WARRANTY_PERIOD,
      </if>
      <if test="warrantyPeriodRule != null">
        WARRANTY_PERIOD_RULE,
      </if>
      <if test="warrantyRepairFee != null">
        WARRANTY_REPAIR_FEE,
      </if>
      <if test="responseTime != null">
        RESPONSE_TIME,
      </if>
      <if test="haveStandbyMachine != null">
        HAVE_STANDBY_MACHINE,
      </if>
      <if test="conditions != null">
        CONDITIONS,
      </if>
      <if test="extendedWarrantyFee != null">
        EXTENDED_WARRANTY_FEE,
      </if>
      <if test="isRefund != null">
        IS_REFUND,
      </if>
      <if test="exchangeConditions != null">
        EXCHANGE_CONDITIONS,
      </if>
      <if test="exchangeMode != null">
        EXCHANGE_MODE,
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION,
      </if>
      <if test="delivery != null">
        DELIVERY,
      </if>
      <if test="futuresDelivery != null">
        FUTURES_DELIVERY,
      </if>
      <if test="transportRequirements != null">
        TRANSPORT_REQUIREMENTS,
      </if>
      <if test="transportWeight != null">
        TRANSPORT_WEIGHT,
      </if>
      <if test="isHvaeFreight != null">
        IS_HVAE_FREIGHT,
      </if>
      <if test="transportationCompletionStandard != null">
        TRANSPORTATION_COMPLETION_STANDARD,
      </if>
      <if test="acceptanceNotice != null">
        ACCEPTANCE_NOTICE,
      </if>
      <if test="packingNumber != null">
        PACKING_NUMBER,
      </if>
      <if test="packingQuantity != null">
        PACKING_QUANTITY,
      </if>
      <if test="advantage != null">
        ADVANTAGE,
      </if>
      <if test="competitiveAnalysis != null">
        COMPETITIVE_ANALYSIS,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="customerNames != null">
        #{customerNames,jdbcType=VARCHAR},
      </if>
      <if test="sellingWords != null">
        #{sellingWords,jdbcType=VARCHAR},
      </if>
      <if test="marketStrategy != null">
        #{marketStrategy,jdbcType=VARCHAR},
      </if>
      <if test="promotionPolicy != null">
        #{promotionPolicy,jdbcType=VARCHAR},
      </if>
      <if test="warrantyPeriod != null">
        #{warrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="warrantyPeriodRule != null">
        #{warrantyPeriodRule,jdbcType=VARCHAR},
      </if>
      <if test="warrantyRepairFee != null">
        #{warrantyRepairFee,jdbcType=DECIMAL},
      </if>
      <if test="responseTime != null">
        #{responseTime,jdbcType=VARCHAR},
      </if>
      <if test="haveStandbyMachine != null">
        #{haveStandbyMachine,jdbcType=TINYINT},
      </if>
      <if test="conditions != null">
        #{conditions,jdbcType=VARCHAR},
      </if>
      <if test="extendedWarrantyFee != null">
        #{extendedWarrantyFee,jdbcType=VARCHAR},
      </if>
      <if test="isRefund != null">
        #{isRefund,jdbcType=TINYINT},
      </if>
      <if test="exchangeConditions != null">
        #{exchangeConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeMode != null">
        #{exchangeMode,jdbcType=VARCHAR},
      </if>
      <if test="freightDescription != null">
        #{freightDescription,jdbcType=VARCHAR},
      </if>
      <if test="delivery != null">
        #{delivery,jdbcType=VARCHAR},
      </if>
      <if test="futuresDelivery != null">
        #{futuresDelivery,jdbcType=VARCHAR},
      </if>
      <if test="transportRequirements != null">
        #{transportRequirements,jdbcType=VARCHAR},
      </if>
      <if test="transportWeight != null">
        #{transportWeight,jdbcType=VARCHAR},
      </if>
      <if test="isHvaeFreight != null">
        #{isHvaeFreight,jdbcType=TINYINT},
      </if>
      <if test="transportationCompletionStandard != null">
        #{transportationCompletionStandard,jdbcType=INTEGER},
      </if>
      <if test="acceptanceNotice != null">
        #{acceptanceNotice,jdbcType=VARCHAR},
      </if>
      <if test="packingNumber != null">
        #{packingNumber,jdbcType=VARCHAR},
      </if>
      <if test="packingQuantity != null">
        #{packingQuantity,jdbcType=VARCHAR},
      </if>
      <if test="advantage != null">
        #{advantage,jdbcType=VARCHAR},
      </if>
      <if test="competitiveAnalysis != null">
        #{competitiveAnalysis,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.GoodsExtendGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    select count(*) from T_GOODS_EXTEND
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_EXTEND
    <set>
      <if test="record.goodsExtendId != null">
        GOODS_EXTEND_ID = #{record.goodsExtendId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.customerNames != null">
        CUSTOMER_NAMES = #{record.customerNames,jdbcType=VARCHAR},
      </if>
      <if test="record.sellingWords != null">
        SELLING_WORDS = #{record.sellingWords,jdbcType=VARCHAR},
      </if>
      <if test="record.marketStrategy != null">
        MARKET_STRATEGY = #{record.marketStrategy,jdbcType=VARCHAR},
      </if>
      <if test="record.promotionPolicy != null">
        PROMOTION_POLICY = #{record.promotionPolicy,jdbcType=VARCHAR},
      </if>
      <if test="record.warrantyPeriod != null">
        WARRANTY_PERIOD = #{record.warrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="record.warrantyPeriodRule != null">
        WARRANTY_PERIOD_RULE = #{record.warrantyPeriodRule,jdbcType=VARCHAR},
      </if>
      <if test="record.warrantyRepairFee != null">
        WARRANTY_REPAIR_FEE = #{record.warrantyRepairFee,jdbcType=DECIMAL},
      </if>
      <if test="record.responseTime != null">
        RESPONSE_TIME = #{record.responseTime,jdbcType=VARCHAR},
      </if>
      <if test="record.haveStandbyMachine != null">
        HAVE_STANDBY_MACHINE = #{record.haveStandbyMachine,jdbcType=TINYINT},
      </if>
      <if test="record.conditions != null">
        CONDITIONS = #{record.conditions,jdbcType=VARCHAR},
      </if>
      <if test="record.extendedWarrantyFee != null">
        EXTENDED_WARRANTY_FEE = #{record.extendedWarrantyFee,jdbcType=VARCHAR},
      </if>
      <if test="record.isRefund != null">
        IS_REFUND = #{record.isRefund,jdbcType=TINYINT},
      </if>
      <if test="record.exchangeConditions != null">
        EXCHANGE_CONDITIONS = #{record.exchangeConditions,jdbcType=VARCHAR},
      </if>
      <if test="record.exchangeMode != null">
        EXCHANGE_MODE = #{record.exchangeMode,jdbcType=VARCHAR},
      </if>
      <if test="record.freightDescription != null">
        FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=VARCHAR},
      </if>
      <if test="record.delivery != null">
        DELIVERY = #{record.delivery,jdbcType=VARCHAR},
      </if>
      <if test="record.futuresDelivery != null">
        FUTURES_DELIVERY = #{record.futuresDelivery,jdbcType=VARCHAR},
      </if>
      <if test="record.transportRequirements != null">
        TRANSPORT_REQUIREMENTS = #{record.transportRequirements,jdbcType=VARCHAR},
      </if>
      <if test="record.transportWeight != null">
        TRANSPORT_WEIGHT = #{record.transportWeight,jdbcType=VARCHAR},
      </if>
      <if test="record.isHvaeFreight != null">
        IS_HVAE_FREIGHT = #{record.isHvaeFreight,jdbcType=TINYINT},
      </if>
      <if test="record.transportationCompletionStandard != null">
        TRANSPORTATION_COMPLETION_STANDARD = #{record.transportationCompletionStandard,jdbcType=INTEGER},
      </if>
      <if test="record.acceptanceNotice != null">
        ACCEPTANCE_NOTICE = #{record.acceptanceNotice,jdbcType=VARCHAR},
      </if>
      <if test="record.packingNumber != null">
        PACKING_NUMBER = #{record.packingNumber,jdbcType=VARCHAR},
      </if>
      <if test="record.packingQuantity != null">
        PACKING_QUANTITY = #{record.packingQuantity,jdbcType=VARCHAR},
      </if>
      <if test="record.advantage != null">
        ADVANTAGE = #{record.advantage,jdbcType=VARCHAR},
      </if>
      <if test="record.competitiveAnalysis != null">
        COMPETITIVE_ANALYSIS = #{record.competitiveAnalysis,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_EXTEND
    set GOODS_EXTEND_ID = #{record.goodsExtendId,jdbcType=INTEGER},
      GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      CUSTOMER_NAMES = #{record.customerNames,jdbcType=VARCHAR},
      SELLING_WORDS = #{record.sellingWords,jdbcType=VARCHAR},
      MARKET_STRATEGY = #{record.marketStrategy,jdbcType=VARCHAR},
      PROMOTION_POLICY = #{record.promotionPolicy,jdbcType=VARCHAR},
      WARRANTY_PERIOD = #{record.warrantyPeriod,jdbcType=VARCHAR},
      WARRANTY_PERIOD_RULE = #{record.warrantyPeriodRule,jdbcType=VARCHAR},
      WARRANTY_REPAIR_FEE = #{record.warrantyRepairFee,jdbcType=DECIMAL},
      RESPONSE_TIME = #{record.responseTime,jdbcType=VARCHAR},
      HAVE_STANDBY_MACHINE = #{record.haveStandbyMachine,jdbcType=TINYINT},
      CONDITIONS = #{record.conditions,jdbcType=VARCHAR},
      EXTENDED_WARRANTY_FEE = #{record.extendedWarrantyFee,jdbcType=VARCHAR},
      IS_REFUND = #{record.isRefund,jdbcType=TINYINT},
      EXCHANGE_CONDITIONS = #{record.exchangeConditions,jdbcType=VARCHAR},
      EXCHANGE_MODE = #{record.exchangeMode,jdbcType=VARCHAR},
      FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=VARCHAR},
      DELIVERY = #{record.delivery,jdbcType=VARCHAR},
      FUTURES_DELIVERY = #{record.futuresDelivery,jdbcType=VARCHAR},
      TRANSPORT_REQUIREMENTS = #{record.transportRequirements,jdbcType=VARCHAR},
      TRANSPORT_WEIGHT = #{record.transportWeight,jdbcType=VARCHAR},
      IS_HVAE_FREIGHT = #{record.isHvaeFreight,jdbcType=TINYINT},
      TRANSPORTATION_COMPLETION_STANDARD = #{record.transportationCompletionStandard,jdbcType=INTEGER},
      ACCEPTANCE_NOTICE = #{record.acceptanceNotice,jdbcType=VARCHAR},
      PACKING_NUMBER = #{record.packingNumber,jdbcType=VARCHAR},
      PACKING_QUANTITY = #{record.packingQuantity,jdbcType=VARCHAR},
      ADVANTAGE = #{record.advantage,jdbcType=VARCHAR},
      COMPETITIVE_ANALYSIS = #{record.competitiveAnalysis,jdbcType=LONGVARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_EXTEND
    set GOODS_EXTEND_ID = #{record.goodsExtendId,jdbcType=INTEGER},
      GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      CUSTOMER_NAMES = #{record.customerNames,jdbcType=VARCHAR},
      SELLING_WORDS = #{record.sellingWords,jdbcType=VARCHAR},
      MARKET_STRATEGY = #{record.marketStrategy,jdbcType=VARCHAR},
      PROMOTION_POLICY = #{record.promotionPolicy,jdbcType=VARCHAR},
      WARRANTY_PERIOD = #{record.warrantyPeriod,jdbcType=VARCHAR},
      WARRANTY_PERIOD_RULE = #{record.warrantyPeriodRule,jdbcType=VARCHAR},
      WARRANTY_REPAIR_FEE = #{record.warrantyRepairFee,jdbcType=DECIMAL},
      RESPONSE_TIME = #{record.responseTime,jdbcType=VARCHAR},
      HAVE_STANDBY_MACHINE = #{record.haveStandbyMachine,jdbcType=TINYINT},
      CONDITIONS = #{record.conditions,jdbcType=VARCHAR},
      EXTENDED_WARRANTY_FEE = #{record.extendedWarrantyFee,jdbcType=VARCHAR},
      IS_REFUND = #{record.isRefund,jdbcType=TINYINT},
      EXCHANGE_CONDITIONS = #{record.exchangeConditions,jdbcType=VARCHAR},
      EXCHANGE_MODE = #{record.exchangeMode,jdbcType=VARCHAR},
      FREIGHT_DESCRIPTION = #{record.freightDescription,jdbcType=VARCHAR},
      DELIVERY = #{record.delivery,jdbcType=VARCHAR},
      FUTURES_DELIVERY = #{record.futuresDelivery,jdbcType=VARCHAR},
      TRANSPORT_REQUIREMENTS = #{record.transportRequirements,jdbcType=VARCHAR},
      TRANSPORT_WEIGHT = #{record.transportWeight,jdbcType=VARCHAR},
      IS_HVAE_FREIGHT = #{record.isHvaeFreight,jdbcType=TINYINT},
      TRANSPORTATION_COMPLETION_STANDARD = #{record.transportationCompletionStandard,jdbcType=INTEGER},
      ACCEPTANCE_NOTICE = #{record.acceptanceNotice,jdbcType=VARCHAR},
      PACKING_NUMBER = #{record.packingNumber,jdbcType=VARCHAR},
      PACKING_QUANTITY = #{record.packingQuantity,jdbcType=VARCHAR},
      ADVANTAGE = #{record.advantage,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_EXTEND
    <set>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="customerNames != null">
        CUSTOMER_NAMES = #{customerNames,jdbcType=VARCHAR},
      </if>
      <if test="sellingWords != null">
        SELLING_WORDS = #{sellingWords,jdbcType=VARCHAR},
      </if>
      <if test="marketStrategy != null">
        MARKET_STRATEGY = #{marketStrategy,jdbcType=VARCHAR},
      </if>
      <if test="promotionPolicy != null">
        PROMOTION_POLICY = #{promotionPolicy,jdbcType=VARCHAR},
      </if>
      <if test="warrantyPeriod != null">
        WARRANTY_PERIOD = #{warrantyPeriod,jdbcType=VARCHAR},
      </if>
      <if test="warrantyPeriodRule != null">
        WARRANTY_PERIOD_RULE = #{warrantyPeriodRule,jdbcType=VARCHAR},
      </if>
      <if test="warrantyRepairFee != null">
        WARRANTY_REPAIR_FEE = #{warrantyRepairFee,jdbcType=DECIMAL},
      </if>
      <if test="responseTime != null">
        RESPONSE_TIME = #{responseTime,jdbcType=VARCHAR},
      </if>
      <if test="haveStandbyMachine != null">
        HAVE_STANDBY_MACHINE = #{haveStandbyMachine,jdbcType=TINYINT},
      </if>
      <if test="conditions != null">
        CONDITIONS = #{conditions,jdbcType=VARCHAR},
      </if>
      <if test="extendedWarrantyFee != null">
        EXTENDED_WARRANTY_FEE = #{extendedWarrantyFee,jdbcType=VARCHAR},
      </if>
      <if test="isRefund != null">
        IS_REFUND = #{isRefund,jdbcType=TINYINT},
      </if>
      <if test="exchangeConditions != null">
        EXCHANGE_CONDITIONS = #{exchangeConditions,jdbcType=VARCHAR},
      </if>
      <if test="exchangeMode != null">
        EXCHANGE_MODE = #{exchangeMode,jdbcType=VARCHAR},
      </if>
      <if test="freightDescription != null">
        FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=VARCHAR},
      </if>
      <if test="delivery != null">
        DELIVERY = #{delivery,jdbcType=VARCHAR},
      </if>
      <if test="futuresDelivery != null">
        FUTURES_DELIVERY = #{futuresDelivery,jdbcType=VARCHAR},
      </if>
      <if test="transportRequirements != null">
        TRANSPORT_REQUIREMENTS = #{transportRequirements,jdbcType=VARCHAR},
      </if>
      <if test="transportWeight != null">
        TRANSPORT_WEIGHT = #{transportWeight,jdbcType=VARCHAR},
      </if>
      <if test="isHvaeFreight != null">
        IS_HVAE_FREIGHT = #{isHvaeFreight,jdbcType=TINYINT},
      </if>
      <if test="transportationCompletionStandard != null">
        TRANSPORTATION_COMPLETION_STANDARD = #{transportationCompletionStandard,jdbcType=INTEGER},
      </if>
      <if test="acceptanceNotice != null">
        ACCEPTANCE_NOTICE = #{acceptanceNotice,jdbcType=VARCHAR},
      </if>
      <if test="packingNumber != null">
        PACKING_NUMBER = #{packingNumber,jdbcType=VARCHAR},
      </if>
      <if test="packingQuantity != null">
        PACKING_QUANTITY = #{packingQuantity,jdbcType=VARCHAR},
      </if>
      <if test="advantage != null">
        ADVANTAGE = #{advantage,jdbcType=VARCHAR},
      </if>
      <if test="competitiveAnalysis != null">
        COMPETITIVE_ANALYSIS = #{competitiveAnalysis,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where GOODS_EXTEND_ID = #{goodsExtendId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_EXTEND
    set GOODS_ID = #{goodsId,jdbcType=INTEGER},
      CUSTOMER_NAMES = #{customerNames,jdbcType=VARCHAR},
      SELLING_WORDS = #{sellingWords,jdbcType=VARCHAR},
      MARKET_STRATEGY = #{marketStrategy,jdbcType=VARCHAR},
      PROMOTION_POLICY = #{promotionPolicy,jdbcType=VARCHAR},
      WARRANTY_PERIOD = #{warrantyPeriod,jdbcType=VARCHAR},
      WARRANTY_PERIOD_RULE = #{warrantyPeriodRule,jdbcType=VARCHAR},
      WARRANTY_REPAIR_FEE = #{warrantyRepairFee,jdbcType=DECIMAL},
      RESPONSE_TIME = #{responseTime,jdbcType=VARCHAR},
      HAVE_STANDBY_MACHINE = #{haveStandbyMachine,jdbcType=TINYINT},
      CONDITIONS = #{conditions,jdbcType=VARCHAR},
      EXTENDED_WARRANTY_FEE = #{extendedWarrantyFee,jdbcType=VARCHAR},
      IS_REFUND = #{isRefund,jdbcType=TINYINT},
      EXCHANGE_CONDITIONS = #{exchangeConditions,jdbcType=VARCHAR},
      EXCHANGE_MODE = #{exchangeMode,jdbcType=VARCHAR},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=VARCHAR},
      DELIVERY = #{delivery,jdbcType=VARCHAR},
      FUTURES_DELIVERY = #{futuresDelivery,jdbcType=VARCHAR},
      TRANSPORT_REQUIREMENTS = #{transportRequirements,jdbcType=VARCHAR},
      TRANSPORT_WEIGHT = #{transportWeight,jdbcType=VARCHAR},
      IS_HVAE_FREIGHT = #{isHvaeFreight,jdbcType=TINYINT},
      TRANSPORTATION_COMPLETION_STANDARD = #{transportationCompletionStandard,jdbcType=INTEGER},
      ACCEPTANCE_NOTICE = #{acceptanceNotice,jdbcType=VARCHAR},
      PACKING_NUMBER = #{packingNumber,jdbcType=VARCHAR},
      PACKING_QUANTITY = #{packingQuantity,jdbcType=VARCHAR},
      ADVANTAGE = #{advantage,jdbcType=VARCHAR},
      COMPETITIVE_ANALYSIS = #{competitiveAnalysis,jdbcType=LONGVARCHAR}
    where GOODS_EXTEND_ID = #{goodsExtendId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.GoodsExtendGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jun 27 19:06:50 CST 2019.
    -->
    update T_GOODS_EXTEND
    set GOODS_ID = #{goodsId,jdbcType=INTEGER},
      CUSTOMER_NAMES = #{customerNames,jdbcType=VARCHAR},
      SELLING_WORDS = #{sellingWords,jdbcType=VARCHAR},
      MARKET_STRATEGY = #{marketStrategy,jdbcType=VARCHAR},
      PROMOTION_POLICY = #{promotionPolicy,jdbcType=VARCHAR},
      WARRANTY_PERIOD = #{warrantyPeriod,jdbcType=VARCHAR},
      WARRANTY_PERIOD_RULE = #{warrantyPeriodRule,jdbcType=VARCHAR},
      WARRANTY_REPAIR_FEE = #{warrantyRepairFee,jdbcType=DECIMAL},
      RESPONSE_TIME = #{responseTime,jdbcType=VARCHAR},
      HAVE_STANDBY_MACHINE = #{haveStandbyMachine,jdbcType=TINYINT},
      CONDITIONS = #{conditions,jdbcType=VARCHAR},
      EXTENDED_WARRANTY_FEE = #{extendedWarrantyFee,jdbcType=VARCHAR},
      IS_REFUND = #{isRefund,jdbcType=TINYINT},
      EXCHANGE_CONDITIONS = #{exchangeConditions,jdbcType=VARCHAR},
      EXCHANGE_MODE = #{exchangeMode,jdbcType=VARCHAR},
      FREIGHT_DESCRIPTION = #{freightDescription,jdbcType=VARCHAR},
      DELIVERY = #{delivery,jdbcType=VARCHAR},
      FUTURES_DELIVERY = #{futuresDelivery,jdbcType=VARCHAR},
      TRANSPORT_REQUIREMENTS = #{transportRequirements,jdbcType=VARCHAR},
      TRANSPORT_WEIGHT = #{transportWeight,jdbcType=VARCHAR},
      IS_HVAE_FREIGHT = #{isHvaeFreight,jdbcType=TINYINT},
      TRANSPORTATION_COMPLETION_STANDARD = #{transportationCompletionStandard,jdbcType=INTEGER},
      ACCEPTANCE_NOTICE = #{acceptanceNotice,jdbcType=VARCHAR},
      PACKING_NUMBER = #{packingNumber,jdbcType=VARCHAR},
      PACKING_QUANTITY = #{packingQuantity,jdbcType=VARCHAR},
      ADVANTAGE = #{advantage,jdbcType=VARCHAR}
    where GOODS_EXTEND_ID = #{goodsExtendId,jdbcType=INTEGER}
  </update>
</mapper>