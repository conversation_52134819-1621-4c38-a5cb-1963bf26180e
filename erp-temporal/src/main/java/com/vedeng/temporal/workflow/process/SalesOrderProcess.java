package com.vedeng.temporal.workflow.process;

import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.enums.ExecutionMode;
import com.vedeng.temporal.workflow.activity.CompanySequenceActivity;
import com.vedeng.temporal.workflow.activity.SalesOrderActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.workflow.step.impl.SalesOrderStep;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;

/**
 * 销售订单流程服务 - 独立流程版本
 * 基于AbstractBusinessProcess架构，专门处理销售订单相关业务
 *
 * 业务职责：
 * - 专注于销售订单处理
 * - 包含完整的销售订单流程：创建 → 提交 → 审核 → 状态确认
 * - 支持多公司间的销售订单流转
 * 
 * 架构特点：
 * - 继承 AbstractBusinessProcess 统一框架
 * - 使用独立的 SalesOrderActivity 接口
 * - 串行执行模式，确保数据一致性
 * - 正序执行（A → B → C → D）
 * 
 * 执行特点：
 * - 每个公司等待前一个公司完成基础步骤
 * - 包含完整的销售订单处理逻辑
 * - 支持数据串联和状态轮询
 *
 * <AUTHOR> 4.0 sonnet
 * @version 1.0 (独立流程版本)
 * @since 2025-01-30
 */
@Slf4j
public class SalesOrderProcess extends AbstractBusinessProcess {

    private final SalesOrderActivity salesOrderActivity;

    public SalesOrderProcess(CompanySequenceActivity companySequenceActivity,
                             SalesOrderActivity salesOrderActivity) {
        
        // 调用父类构造函数
        super(companySequenceActivity);
        
        // 保存Activity依赖
        this.salesOrderActivity = salesOrderActivity;
    }

    @Override
    protected ExecutionMode getExecutionMode() {
        return ExecutionMode.SEQUENTIAL;
    }

    @Override
    protected boolean isReverseOrder() {
        return false; // 正序执行
    }

    @Override
    protected List<BusinessStep> getBusinessSteps() {
        // 销售订单流程只包含一个步骤：销售订单处理
        return Collections.singletonList(
            new SalesOrderStep(salesOrderActivity)
        );
    }

    @Override
    protected String getProcessName() {
        return "销售订单流程";
    }

    /**
     * 执行销售订单流程
     * 使用AbstractBusinessProcess统一框架
     *
     * @param request 业务请求
     * @param companySequence 公司执行序列
     * @return 执行结果
     */
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, List<String> companySequence) {
        return super.execute(request, companySequence);
    }
}