package com.vedeng.flash.service.message;

import com.vedeng.flash.dto.SendMessageDto;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.model.BuyorderGoods;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2021/5/27 14:17
 */
@Service
public class ExpeditingTicketsOver48HoursMessageSender extends AbstractMessageSender{
    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Override
    protected String getUrl(SendMessageDto sendMessageDto) {
        return "./flash/earlyWarningTicksTask/earlyWarningTicksTask.do";
    }

    @Override
    protected List<Integer> getMessageUserIdList(SendMessageDto sendMessageDto) {
        List<Integer> messageUserId = new ArrayList<>();
        Integer skuProductAssitantId = buyorderGoodsMapper.getskuProductAssitantIdByBuyorderGoodsId(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        messageUserId.add(skuProductAssitantId);
        List<Integer> supplyChainCoordinatorIdList = buyorderGoodsMapper.getSpplyChainCoordinatorIdList();
        messageUserId.addAll(supplyChainCoordinatorIdList);
        return messageUserId;
    }

    @Override
    protected Integer getMessageTemplateId() {
        //催票任务生成超过48小时消息在message_template表里的ID
        return 177;
    }

    @Override
    protected Map getTemplateVariableMap(SendMessageDto sendMessageDto) {
        Map<String,String> params = new HashMap<>();
        BuyorderGoods buyorderGoods =buyorderGoodsMapper.selectByPrimaryKey(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        String buyorderCreatorName= buyorderGoodsMapper.getbuyorderCreatorNameById(sendMessageDto.getEarlyWarningTask().getRelateBusinessId());
        params.put("buyOrderNo",sendMessageDto.getEarlyWarningTask().getBusinessExtra1());
        params.put("sku",buyorderGoods.getSku());
        params.put("thisArrivalNum",sendMessageDto.getEarlyWarningTask().getUrgingTicketNum().toString());
        params.put("buyorderCreator",buyorderCreatorName);
        return params;
    }
}
