package com.wms.controller;

import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.activiti.service.ActivitiAssigneeService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.Unit;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.UnitService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.service.WarehouseGoodsOutDetailService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.logistics.strategy.WarehouseGoodsOutDetailStrategyFactory;
import com.vedeng.system.service.UserService;
import com.wms.constant.LogicalEnum;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.model.dto.AddReceiveOutDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSReceiveOutService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import com.vedeng.common.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;


@Controller
@RequestMapping("/wms/receiveOut")
public class WMSReceiveOutController extends BaseController {
    private static final Logger LOGGER = LoggerFactory.getLogger(WMSReceiveOutController.class);
    @Autowired
    private UserService userService;


    @Autowired
    private WMSReceiveOutService wmsReceiveOutService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    @Qualifier("goodsService")
    protected GoodsService goodsService;

    @Autowired
    protected UnitService unitService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private WarehouseGoodsOutDetailStrategyFactory warehouseGoodsOutDetailStrategyFactory;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private ActivitiAssigneeService activitiAssigneeService;

    /**
     * 跳转新增领用出库单页面
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/toAddReceiveOut")
    public ModelAndView toAddLendOut(HttpServletRequest request){
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("today", DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd"));
        List<User> allUser = userService.getAllUser(user);
        modelAndView.addObject("allUser",allUser);
        modelAndView.setViewName("wms/receive/receive_out_add");
        return modelAndView;
    }

    /**
     * 获取领用人名单
     * @param request
     * @param userId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getReceiveUserInfo")
    public ResultInfo<?> getReceiveUserInfo(HttpServletRequest request, @RequestParam("userId") Integer userId){
        User user = wmsReceiveOutService.getUserInfoByUserId(userId);
        if (user != null){
            return ResultInfo.success(user);
        }else {
            return ResultInfo.error("查询不到用户信息");
        }

    }

    /**
     * 领用出库单 新增商品
     * @param request
     * @param brandName
     * @param typeSpecification
     * @param unitName
     * @param searchContent
     * @param saleorderId
     * @param session
     * @param pageNo
     * @param pageSize
     * @param callbackFunction
     * @param scene
     * @return
     */
    @RequestMapping(value = "/addReceiveOutGoods")
    public ModelAndView addSaleorderGoods(HttpServletRequest request,
                                          @RequestParam(value = "brandName", required = false) String brandName,
                                          @RequestParam(value = "typeSpecification", required = false) String typeSpecification,
                                          @RequestParam(value = "unitName", required = false) String unitName,
                                          @RequestParam(value = "searchContent", required = false) String searchContent,
                                          @RequestParam(value = "saleorderId", defaultValue = "0") Integer saleorderId, HttpSession session,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                          @RequestParam(value="callbackFunction",required=false) String callbackFunction,
                                          @RequestParam(required = false, defaultValue = "0")Integer scene) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("scene",scene);
        boolean anyNotEmpty = StringUtils.isNotBlank(brandName) || StringUtils.isNotBlank(typeSpecification)
                || StringUtils.isNotBlank(unitName) || StringUtils.isNotBlank(searchContent);
        if (anyNotEmpty) {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            Page page = getPageTag(request, pageNo, pageSize);
            Goods goods = new Goods();
            goods.setCompanyId(user.getCompanyId());
            goods.setSearchContent(searchContent);
            goods.setBrandName(brandName);
            //添加组合搜索，规格型号
            goods.setSpecModel(typeSpecification);
            goods.setUnitName(unitName);
            Map<String, Object> map = new HashMap<String, Object>();
            // 领用出库 添加商品 取 不可格库存量
            request.setAttribute("logicalId",LogicalEnum.BHG.getLogicalWarehouseId());
            map = goodsService.getBhgLogicallistGoodsStockPage(request, goods, page, user);
            mv.addObject("goodsList", map.get("goodsList"));

            mv.addObject("page", map.get("page"));
            mv.addObject("searchContent", searchContent);
            mv.addObject("zxfunitNameValue", unitName);
            mv.addObject("typeSpecification", typeSpecification);
            mv.addObject("brandName", brandName);
        }
        mv.addObject("saleorderId", saleorderId);
        mv.addObject("callbackFunction", callbackFunction);
        // 指定不合格库
        mv.addObject("logicalId", LogicalEnum.BHG.getLogicalWarehouseId());
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        Unit unit = new Unit();
        unit.setCompanyId(user.getCompanyId());
        List<Unit> unitList = unitService.getAllUnitList(unit);
        mv.addObject("unitList", unitList);
        mv.setViewName("wms/receive/add_receive_out_goods");
        return mv;

    }



    /**
     * 领用出库详情页
     * @param request
     * @param receiveOutId
     * @return
     */
    @RequestMapping("/receiveDetail")
    public ModelAndView receiveOutDetail(HttpServletRequest request, @RequestParam("receiveOutId") Long receiveOutId) {
        ModelAndView mv = new ModelAndView();

        WmsOutputOrder outputOrder = this.wmsReceiveOutService.findReceiveOutById(receiveOutId);
        mv.addObject("receiveOut", outputOrder);

        List<WmsOutputOrderGoodsDto> receiveOutGoodList = this.wmsReceiveOutService.queryOutputGoodsByReceiveOutId(receiveOutId);
        mv.addObject("receiveOutGoodList", receiveOutGoodList);


        WarehouseGoodsOutDetailService receiveWarehouseGoodsOutDetailService = warehouseGoodsOutDetailStrategyFactory.getWarehouseGoodsOutDetailService(WarehouseGoodsOutEnum.RECEIVE_WAREHOUSE_OUT.getService());

        List<WarehouseGoodsOutLogVo> receiveWarehouseGoodsOutLogList = new ArrayList<>();
        if (outputOrder != null && outputOrder.getOrderNo() != null) {
            receiveWarehouseGoodsOutLogList = receiveWarehouseGoodsOutDetailService.relatedNoWarehouseGoodsOutLogList(outputOrder.getOrderNo());
        }
        mv.addObject("wlogList", receiveWarehouseGoodsOutLogList);


        List<Integer> skuIds = new ArrayList<>();
        for (WarehouseGoodsOutLogVo receiveWarehouseGoodsOutLog : receiveWarehouseGoodsOutLogList) {
            skuIds.add(receiveWarehouseGoodsOutLog.getGoodsId());
        }

        if (!CollectionUtils.isEmpty(skuIds)) {
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_ID").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }

        //出库数量
        Long outNum = 0L;
        //已出库数量
        Long alreadyOutNum = 0L;
        for (WmsOutputOrderGoodsDto wmsOutputOrderGoodsDto : receiveOutGoodList) {
            outNum += wmsOutputOrderGoodsDto.getOutputNum();
            alreadyOutNum += wmsOutputOrderGoodsDto.getAlreadyOutputNum();
        }
        mv.addObject("outNum",outNum);
        mv.addObject("alreadyOutNum",alreadyOutNum);



        mv.setViewName("wms/receive/receive_out_detail");

        //审核流
        String key = receiveOutId + "";


        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "receiveOutAudit_" + key);
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");

            mv.addObject("taskId", taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                mv.addObject("verifyUsers", StringUtils.join(userNameList, ","));
            }
        }

        return mv;
    }


    /**
     * 页面审核
     * @param taskId
     * @param pass
     * @param receiveOutId
     * @return
     */
    @RequestMapping(value = "/auditResult")
    @NoNeedAccessAuthorization
    public ModelAndView auditResult(String taskId, Boolean pass, Integer receiveOutId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("receiveOutId", receiveOutId);
        mv.setViewName("wms/receive/auditResult");
        return mv;
    }

    /**
     * 审核通过前检查库存
     * @param request
     * @param receiveOutId
     * @return 领用商品数是否超过不合格库库存
     */
    @ResponseBody
    @RequestMapping(value="/checkStockNum")
    public ResultInfo<?> checkStockNum(HttpServletRequest request, Long receiveOutId){
        log.info("审核时检查 领用出库单库存, 领用出库单 receiveOutId: {}", receiveOutId);
        // 根据领用单ID 查询领用单商品列表
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList = wmsReceiveOutService.queryOutputGoodsByReceiveOutId(receiveOutId);
        // 组装 MAP， key是领用单商品 SKU, val 是商品领用数量
        Map<String, Integer> receiveOutOrderSkuMap = new HashMap<>();
        List<String> receiveOutOrderSkuList = new ArrayList<>();
        for (WmsOutputOrderGoodsDto wmsOutputOrderGoodsDto : wmsOutputOrderGoodsList) {
            receiveOutOrderSkuList.add(wmsOutputOrderGoodsDto.getSkuNo());
            receiveOutOrderSkuMap.put(wmsOutputOrderGoodsDto.getSkuNo(), wmsOutputOrderGoodsDto.getOutputNum());
        }
        // 调库存服务查询库存
        Map<String, WarehouseStock> warehouseStockMap = warehouseStockService.getLogicalStockMapInfo(receiveOutOrderSkuList);
        for (String sku : receiveOutOrderSkuMap.keySet()){
            WarehouseStock warehouseStock = warehouseStockMap.get(sku + LogicalEnum.BHG.getLogicalWarehouseId());
            // 如果无库存 返回 false
            if (warehouseStock == null || warehouseStock.getAvailableStockNum() == null){
                return ResultInfo.success(false);
            }
            Integer availableStockNum = warehouseStock.getAvailableStockNum();
            Integer receiveOutNum = receiveOutOrderSkuMap.get(sku);
            // 如果领用单商品数量大于库存数 返回 false
            if(receiveOutNum.compareTo(availableStockNum) > 0){
                log.info("领用出库单 receiveOutId: {} 中的 sku: {}, 领用数量: {}, 不合格库存数量: {}", receiveOutId, sku, receiveOutNum, availableStockNum);
                return ResultInfo.success(false);
            }
        }
        return ResultInfo.success(true);

    }


    /**
     * 领用出库审核操作
     * @param request
     * @param receiveOutId
     * @param taskId
     * @param comment
     * @param pass
     * @param session
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementTask(HttpServletRequest request, Integer receiveOutId, String taskId, String comment, Boolean pass,
                                        HttpSession session) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        try {
            //完成当前任务，工作流往下走
            Map<String, Object> taskVaribles = new HashMap<String, Object>();
            taskVaribles.put("pass", pass);

            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), taskVaribles);

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * 领用单审核
     * @param request
     * @param receiveOutId
     * @throws Exception
     */
    private void startProcessInstance(HttpServletRequest request,Long receiveOutId) throws Exception{

        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<String, Object>();

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        variableMap.put("processDefinitionKey", "receiveOutAudit");
        variableMap.put("businessKey", "receiveOutAudit_" + receiveOutId);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("currentAssineeId", user.getOrgId());
        variableMap.put("num",1);
        variableMap.put("receiveOutOrderId", receiveOutId);

        WmsOutputOrder receiveOut = this.wmsReceiveOutService.findReceiveOutById(receiveOutId);
        variableMap.put("receiveOutOrderNo", receiveOut.getOrderNo());

        log.info("WMSReceiveOutController.startProcessInstance 领用单 receiveOutId:{} 开始创建审核流", receiveOutId);
        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);
        log.info("WMSReceiveOutController.startProcessInstance 领用单 receiveOutId:{} 创建审核流完成", receiveOutId);
        this.wmsReceiveOutService.updateReceiveOutAuditStatus(receiveOutId, VerifyStatusEnum.Reviewing.getValue());
        log.info("WMSReceiveOutController.startProcessInstance 更新领用单 receiveOutId:{} 的审核状态为审核中", receiveOutId);

        // 获取当前活动节点
        Task taskInfo = processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                .singleResult();

        //完成申请的任务
        ResultInfo<?> resultInfo = actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);

        if (null!=resultInfo && ResultInfo.error().getCode().equals(resultInfo.getCode())){
            logger.info("申请的任务完成失败taskId :{} ", taskInfo.getId());
            throw new Exception();
        }

    }

    /**
     * 保存领用出库单
     * @param request
     * @param addReceiveOutDto
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveReceiveOutOrder")
    public ModelAndView saveReceiveOutOrder(HttpServletRequest request, AddReceiveOutDto addReceiveOutDto){
        ModelAndView mv = new ModelAndView();
        try{
            User user = (User)request.getSession().getAttribute(ErpConst.CURR_USER);
            if (!checkBhgStockNum(addReceiveOutDto)){
                mv.addObject("message","输入数量大于不合格库存量：数量不允许超过不合格库存量");
                return fail(mv);
            }
            String topDepartmentLeader = activitiAssigneeService.getTopDepartmentLeader(user.getUsername(),1);
            if (StringUtils.isBlank(topDepartmentLeader)){
                mv.addObject("message","请先设置一级部门长，再重新创建领用单");
                return fail(mv);
            }

            Long receiveOutOrderId = wmsReceiveOutService.addReceiveOutOrder(addReceiveOutDto, user);
            log.info("领用单创建完成, receiveOutOrderId: {}, 开始提交审核", receiveOutOrderId);
            startProcessInstance(request,receiveOutOrderId);
            return success(mv);
        }catch(Exception e){
            LOGGER.error("新增领用出库单失败====================:",e);
            mv.addObject("message",e.getMessage());
            return fail(mv);
        }
    }

    /**
     * 检验数量是否超过不合格库数量
     * @param addReceiveOutDto
     * @return
     */
    private boolean checkBhgStockNum(AddReceiveOutDto addReceiveOutDto){
        List<String> skuList = Arrays.asList(addReceiveOutDto.getSkuNo());
        Long[] receiveOutNumArr = addReceiveOutDto.getReceiveOutNum();
        Map<String, WarehouseStock> warehouseStockMap = warehouseStockService.getLogicalStockMapInfo(skuList);
        for (int i = 0; i < skuList.size(); i++){
            WarehouseStock warehouseStock = warehouseStockMap.get(skuList.get(i) + LogicalEnum.BHG.getLogicalWarehouseId());
            if (warehouseStock == null || warehouseStock.getAvailableStockNum() == null){
                return false;
            }
            Integer availableStockNum = warehouseStock.getAvailableStockNum();
            Integer receiveOutNum = receiveOutNumArr[i].intValue();
            if(receiveOutNum.compareTo(availableStockNum) > 0){
                log.info("sku: {}, 领用数量: {}, 不合格库存数量: {}", skuList.get(i), receiveOutNum, availableStockNum);
                return false;
            }
        }
        return true;
    }

}
