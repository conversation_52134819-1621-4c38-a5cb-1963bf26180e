package com.task.service;

/**
 * <AUTHOR>
 */
public interface VerifyInfoTaskService {
    /**处理历史供应商审核信息
     * .
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2022/1/11 9:55.
     * @author: <PERSON><PERSON>.
     * @param null
     * @return: .
     * @throws:  .
     */
    Integer handleHistorySupplierVerifyInfo();

    /** 处理历史客户资质审核信息
     * .
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2022/1/11 9:55.
     * @author: <PERSON><PERSON>.
     * @param
     * @return: java.lang.Integer.
     * @throws:  .
     */
    Integer handleHistoryCustomerVerifyInfo();
}
