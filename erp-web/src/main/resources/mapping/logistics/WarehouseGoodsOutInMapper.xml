<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.logistics.dao.WarehouseGoodsOutInMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.logistics.model.WarehouseGoodsOutIn">
    <!--@mbg.generated-->
    <!--@Table T_WAREHOUSE_GOODS_OUT_IN-->
    <id column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="BIGINT" property="warehouseGoodsOutInId" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
    <result column="WMS_NO" jdbcType="VARCHAR" property="wmsNo" />
    <result column="RELATE_NO" jdbcType="VARCHAR" property="relateNo" />
    <result column="OUT_IN_TYPE" jdbcType="BOOLEAN" property="outInType" />
    <result column="OUT_IN_COMPANY" jdbcType="VARCHAR" property="outInCompany" />
    <result column="OUT_IN_TIME" jdbcType="TIMESTAMP" property="outInTime" />
    <result column="SOURCE" jdbcType="VARCHAR" property="source" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="IS_VIRTUAL" jdbcType="INTEGER" property="isVirture" />
  </resultMap>

  <resultMap id="WarehouseGoodsOutResultMap" type="com.vedeng.logistics.model.vo.WarehouseGoodsOutVo"  extends="BaseResultMap">
        <result column="OUT_IN_TIME_STR" jdbcType="VARCHAR" property="outInTimeStr" />
        <collection property="warehouseGoodsOutLogList" javaType="java.util.List" ofType="com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo">
          <id column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" property="warehouseGoodsOutInDetailId" jdbcType="BIGINT" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID" property="companyId" jdbcType="INTEGER" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE" property="operateType" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID" property="relatedId" jdbcType="INTEGER" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID" property="goodsId" jdbcType="INTEGER" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID" property="skuId" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY" property="barcodeFactory" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM" property="num" jdbcType="INTEGER" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER" property="batchNumber" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE" property="expirationDate" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE" property="isDelete" jdbcType="INTEGER" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME" property="addTimeStr" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME" property="checkStatusTime" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME" property="recheckStatusTime" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE" property="productDate" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER" property="vedengBatchNumber" jdbcType="VARCHAR" />
          <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER" property="sterilizationBatchNumber" jdbcType="VARCHAR" />
        </collection>
  </resultMap>

  <resultMap id="WarehouseGoodsOutLogMap" type="com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo">
    <id column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" property="warehouseGoodsOutInDetailId" jdbcType="BIGINT" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="BIGINT" property="warehouseGoodsOutInId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_OUT_IN_NO" property="outInNo" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE" property="operateType" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID" property="skuId" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY" property="barcodeFactory" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM" property="num" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER" property="batchNumber" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE" property="expirationDate" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE" property="isDelete" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME" property="addTimeStr" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME" property="checkStatusTime" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME" property="recheckStatusTime" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE" property="productDate" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER" property="vedengBatchNumber" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER" property="sterilizationBatchNumber" jdbcType="VARCHAR" />
  </resultMap>

  <resultMap id="WarehouseGoodsInLogMap" type="com.vedeng.logistics.model.vo.WarehouseGoodsInLogVo">
    <id column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ID" property="warehouseGoodsOutInDetailId" jdbcType="BIGINT" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="BIGINT" property="warehouseGoodsOutInId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_OUT_IN_NO" property="outInNo" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE" property="operateType" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID" property="relatedId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID" property="skuId" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY" property="barcodeFactory" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM" property="num" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER" property="batchNumber" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE" property="expirationDate" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE" property="isDelete" jdbcType="INTEGER" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME" property="addTimeStr" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME" property="checkStatusTime" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME" property="recheckStatusTime" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE" property="productDate" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER" property="vedengBatchNumber" jdbcType="VARCHAR" />
    <result column="WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER" property="sterilizationBatchNumber" jdbcType="VARCHAR" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    WAREHOUSE_GOODS_OUT_IN_ID, OUT_IN_NO, WMS_NO, RELATE_NO, OUT_IN_TYPE, OUT_IN_COMPANY,
    OUT_IN_TIME, `SOURCE`, IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME,
    MOD_TIME, UPDATER, UPDATER_NAME, REMARK, UPDATE_REMARK,IS_VIRTUAL
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OUT_IN
    where WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_WAREHOUSE_GOODS_OUT_IN
    where WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="warehouseGoodsOutInId" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutIn" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OUT_IN (OUT_IN_NO, WMS_NO, RELATE_NO,
      OUT_IN_TYPE, OUT_IN_COMPANY, OUT_IN_TIME,
      `SOURCE`, IS_DELETE,
      ADD_TIME, CREATOR, CREATOR_NAME,
      MOD_TIME, UPDATER, UPDATER_NAME,
      REMARK, UPDATE_REMARK,IS_VIRTUAL)
    values (#{outInNo,jdbcType=VARCHAR}, #{wmsNo,jdbcType=VARCHAR}, #{relateNo,jdbcType=VARCHAR},
      #{outInType,jdbcType=BOOLEAN}, #{outInCompany,jdbcType=VARCHAR}, #{outInTime,jdbcType=TIMESTAMP},
      #{source,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN},
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR},
      #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR},#{isVirture,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="WAREHOUSE_GOODS_OUT_IN_ID" keyProperty="warehouseGoodsOutInId" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutIn" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_WAREHOUSE_GOODS_OUT_IN
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="outInNo != null">
        OUT_IN_NO,
      </if>
      <if test="wmsNo != null">
        WMS_NO,
      </if>
      <if test="relateNo != null">
        RELATE_NO,
      </if>
      <if test="outInType != null">
        OUT_IN_TYPE,
      </if>
      <if test="outInCompany != null">
        OUT_IN_COMPANY,
      </if>
      <if test="outInTime != null">
        OUT_IN_TIME,
      </if>
      <if test="source != null">
        `SOURCE`,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="isVirture != null">
        IS_VIRTUAL,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="outInNo != null">
        #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="wmsNo != null">
        #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="relateNo != null">
        #{relateNo,jdbcType=VARCHAR},
      </if>
      <if test="outInType != null">
        #{outInType,jdbcType=BOOLEAN},
      </if>
      <if test="outInCompany != null">
        #{outInCompany,jdbcType=VARCHAR},
      </if>
      <if test="outInTime != null">
        #{outInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="isVirture != null">
        #{isVirture,jdbcType=INTEGER}
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutIn">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OUT_IN
    <set>
      <if test="outInNo != null">
        OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      </if>
      <if test="wmsNo != null">
        WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      </if>
      <if test="relateNo != null">
        RELATE_NO = #{relateNo,jdbcType=VARCHAR},
      </if>
      <if test="outInType != null">
        OUT_IN_TYPE = #{outInType,jdbcType=BOOLEAN},
      </if>
      <if test="outInCompany != null">
        OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR},
      </if>
      <if test="outInTime != null">
        OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP},
      </if>
      <if test="source != null">
        `SOURCE` = #{source,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="isVirture != null">
        IS_VIRTUAL = #{isVirture,jdbcType=INTEGER},
      </if>
    </set>
    where WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.logistics.model.WarehouseGoodsOutIn">
    <!--@mbg.generated-->
    update T_WAREHOUSE_GOODS_OUT_IN
    set OUT_IN_NO = #{outInNo,jdbcType=VARCHAR},
      WMS_NO = #{wmsNo,jdbcType=VARCHAR},
      RELATE_NO = #{relateNo,jdbcType=VARCHAR},
      OUT_IN_TYPE = #{outInType,jdbcType=BOOLEAN},
      OUT_IN_COMPANY = #{outInCompany,jdbcType=VARCHAR},
      OUT_IN_TIME = #{outInTime,jdbcType=TIMESTAMP},
      `SOURCE` = #{source,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      IS_VIRTUAL = #{isVirture,jdbcType=VARCHAR}
    where WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=BIGINT}
  </update>

  <select id="selectByOutInNo" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from T_WAREHOUSE_GOODS_OUT_IN
    where OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
  </select>
  <select id="getBuyTraderNameByAftersale" parameterType="string" resultType="string">
    select TB.TRADER_NAME
    from T_AFTER_SALES TA
    JOIN T_BUYORDER TB ON TA.ORDER_NO = TB.BUYORDER_NO
    WHERE TA.AFTER_SALES_NO = #{orderNo,jdbcType=VARCHAR}
    AND IFNULL(TB.TRADER_NAME,'') !=''
    LIMIT 1
  </select>

  <select id="selectWarehouseGoodsOutDetail" resultMap="WarehouseGoodsOutResultMap">
    SELECT T1.WAREHOUSE_GOODS_OUT_IN_ID,
        T1.OUT_IN_NO,
        T1.WMS_NO,
        T1.RELATE_NO,
        T1.OUT_IN_TYPE,
        T1.OUT_IN_COMPANY,
        T1.OUT_IN_TIME,
        T1.IS_VIRTUAL,
        DATE_FORMAT(T1.OUT_IN_TIME, '%Y-%m-%d %H:%i:%s') AS OUT_IN_TIME_STR,
        T1.SOURCE,
        T1.UPDATE_REMARK,
        T2.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID           AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
        T2.COMPANY_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID,
        T2.OPERATE_TYPE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE,
        T2.RELATED_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID,
        T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID,
        T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID,
        T2.BARCODE_FACTORY                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY,
        ABS(T2.NUM)                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM,
        T2.BATCH_NUMBER                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER,
        T2.EXPIRATION_DATE                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE,
        T2.IS_DELETE                                  AS WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE,
        DATE_FORMAT(T2.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME,
        T2.CHECK_STATUS_TIME                          AS WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME,
        T2.RECHECK_STATUS_TIME                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME,
        T2.PRODUCT_DATE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE,
        T2.VEDENG_BATCH_NUMBER                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER,
        T2.STERILIZATION_BATCH_NUMBER                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER
    FROM T_WAREHOUSE_GOODS_OUT_IN T1
        LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM T2 ON T1.OUT_IN_NO = T2.OUT_IN_NO AND T2.IS_DELETE = 0 AND T2.OPERATE_TYPE = #{outInType,jdbcType=INTEGER} AND T2.COMPANY_ID = 1 AND T2.LOG_TYPE = 1
    WHERE T1.IS_DELETE = 0
        AND T1.OUT_IN_TYPE =  #{outInType,jdbcType=INTEGER}
        AND T1.OUT_IN_NO = #{outInNo,jdbcType=VARCHAR}
  </select>

  <select id="selectWarehouseGoodsOutDetailByRelatedNo" resultMap="WarehouseGoodsOutResultMap">
    SELECT T1.WAREHOUSE_GOODS_OUT_IN_ID,
      T1.OUT_IN_NO,
      T1.WMS_NO,
      T1.RELATE_NO,
      T1.OUT_IN_TYPE,
      T1.OUT_IN_COMPANY,
      T1.OUT_IN_TIME,
      T1.IS_VIRTUAL,
      DATE_FORMAT(T1.OUT_IN_TIME, '%Y-%m-%d %H:%i:%s') AS OUT_IN_TIME_STR,
      T1.SOURCE,
      T2.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID           AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
      T2.COMPANY_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID,
      T2.OPERATE_TYPE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE,
      T2.RELATED_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID,
      T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID,
      T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID,
      T2.BARCODE_FACTORY                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY,
      ABS(T2.NUM)                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM,
      T2.BATCH_NUMBER                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER,
      T2.EXPIRATION_DATE                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE,
      T2.IS_DELETE                                  AS WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE,
      DATE_FORMAT(T2.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME,
      T2.CHECK_STATUS_TIME                          AS WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME,
      T2.RECHECK_STATUS_TIME                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME,
      T2.PRODUCT_DATE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE,
      T2.VEDENG_BATCH_NUMBER                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER,
      T2.STERILIZATION_BATCH_NUMBER                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER
    FROM T_WAREHOUSE_GOODS_OUT_IN T1
        LEFT JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM T2 ON T1.OUT_IN_NO = T2.OUT_IN_NO AND T2.IS_DELETE = 0 AND T2.OPERATE_TYPE = #{outInType,jdbcType=INTEGER} AND T2.COMPANY_ID = 1 AND T2.LOG_TYPE = 1
    WHERE T1.IS_DELETE = 0
      AND T1.OUT_IN_TYPE =  #{outInType,jdbcType=INTEGER}
      AND T1.RELATE_NO = #{relatedNo,jdbcType=VARCHAR}
  </select>

  <select id="selectWarehouseGoodsOutLogListByRelatedNo" resultMap="WarehouseGoodsOutLogMap">
    SELECT
      T2.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID           AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
      T1.WAREHOUSE_GOODS_OUT_IN_ID                  AS WAREHOUSE_GOODS_OUT_IN_ID,
      T2.OUT_IN_NO                                  AS WAREHOUSE_GOODS_OUT_IN_DETAIL_OUT_IN_NO,
      T2.COMPANY_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID,
      T2.OPERATE_TYPE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE,
      T2.RELATED_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID,
      T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID,
      T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID,
      T2.BARCODE_FACTORY                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY,
      ABS(T2.NUM)                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM,
      T2.BATCH_NUMBER                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER,
      T2.EXPIRATION_DATE                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE,
      T2.IS_DELETE                                  AS WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE,
      DATE_FORMAT(T2.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME,
      T2.CHECK_STATUS_TIME                          AS WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME,
      T2.RECHECK_STATUS_TIME                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME,
      T2.PRODUCT_DATE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE,
      T2.VEDENG_BATCH_NUMBER                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER,
      T2.STERILIZATION_BATCH_NUMBER                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER
    FROM T_WAREHOUSE_GOODS_OUT_IN T1
        INNER JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM T2 ON T1.OUT_IN_NO = T2.OUT_IN_NO
    WHERE T1.IS_DELETE = 0
      AND T2.IS_DELETE = 0
      AND T2.COMPANY_ID = 1
      AND T2.LOG_TYPE = 1
      AND T1.OUT_IN_TYPE =  #{outInType,jdbcType=INTEGER}
      AND T2.OPERATE_TYPE = #{outInType,jdbcType=INTEGER}
      AND T1.RELATE_NO = #{relatedNo,jdbcType=VARCHAR}
  </select>

  <select id="selectWarehouseGoodsInLogListByRelatedNo" resultMap="WarehouseGoodsInLogMap">
    SELECT
      T2.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID           AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
      T1.WAREHOUSE_GOODS_OUT_IN_ID                  AS WAREHOUSE_GOODS_OUT_IN_ID,
      T2.OUT_IN_NO                                  AS WAREHOUSE_GOODS_OUT_IN_DETAIL_OUT_IN_NO,
      T2.COMPANY_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_COMPANY_ID,
      T2.OPERATE_TYPE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_OPERATE_TYPE,
      T2.RELATED_ID                                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RELATED_ID,
      T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_GOODS_ID,
      T2.GOODS_ID                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_SKU_ID,
      T2.BARCODE_FACTORY                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BARCODE_FACTORY,
      ABS(T2.NUM)                                   AS WAREHOUSE_GOODS_OUT_IN_DETAIL_NUM,
      T2.BATCH_NUMBER                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_BATCH_NUMBER,
      T2.EXPIRATION_DATE                            AS WAREHOUSE_GOODS_OUT_IN_DETAIL_EXPIRATION_DATE,
      T2.IS_DELETE                                  AS WAREHOUSE_GOODS_OUT_IN_DETAIL_IS_DELETE,
      DATE_FORMAT(T2.ADD_TIME, '%Y-%m-%d %H:%i:%s') AS WAREHOUSE_GOODS_OUT_IN_DETAIL_ADD_TIME,
      T2.CHECK_STATUS_TIME                          AS WAREHOUSE_GOODS_OUT_IN_DETAIL_CHECK_STATUS_TIME,
      T2.RECHECK_STATUS_TIME                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_RECHECK_STATUS_TIME,
      T2.PRODUCT_DATE                               AS WAREHOUSE_GOODS_OUT_IN_DETAIL_PRODUCT_DATE,
      T2.VEDENG_BATCH_NUMBER                        AS WAREHOUSE_GOODS_OUT_IN_DETAIL_VEDENG_BATCH_NUMBER,
      T2.STERILIZATION_BATCH_NUMBER                 AS WAREHOUSE_GOODS_OUT_IN_DETAIL_STERILIZATION_BATCH_NUMBER
    FROM T_WAREHOUSE_GOODS_OUT_IN T1
           INNER JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM T2 ON T1.OUT_IN_NO = T2.OUT_IN_NO
    WHERE T1.IS_DELETE = 0
      AND T2.IS_DELETE = 0
      AND T2.COMPANY_ID = 1
      AND T2.LOG_TYPE = 0
      AND T1.OUT_IN_TYPE =  #{outInType,jdbcType=INTEGER}
      AND T2.OPERATE_TYPE = #{outInType,jdbcType=INTEGER}
      AND T1.RELATE_NO = #{relatedNo,jdbcType=VARCHAR}
  </select>

  <select id="getBarcodFactoryListByOrderNo" resultType="string">
    select TWGOII.BARCODE_FACTORY barcodeFactory
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
           left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    WHERE TWGOI.RELATE_NO IN
      <foreach collection="orderNos" item="orderNo" index="index"
               open="(" close=")" separator=",">
        #{orderNo,jdbcType=VARCHAR}
      </foreach>
      AND TWGOI.OUT_IN_TYPE=1
      AND TWGOII.LOG_TYPE=0
      AND TWGOI.IS_DELETE=0
      AND TWGOII.IS_DELETE=0
      AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
      AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
  </select>

  <select id="getBarcodFactoryListBySaleOrderNo" resultType="string">
    select TWGOII.BARCODE_FACTORY barcodeFactory
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    LEFT JOIN
    T_AFTER_SALES_GOODS TASG ON TWGOII.RELATED_ID = TASG.ORDER_DETAIL_ID
    LEFT JOIN
    T_SALEORDER_GOODS TSG ON TASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
    WHERE TWGOI.RELATE_NO IN
    <foreach collection="orderNos" item="orderNo" index="index"
             open="(" close=")" separator=",">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
    AND TWGOI.OUT_IN_TYPE=2 /*销售出库*/
    AND TWGOII.LOG_TYPE=1 /*出库*/
    AND TWGOI.IS_DELETE=0
    AND TWGOII.IS_DELETE=0
    AND TWGOII.GOODS_ID = #{goodsId,jdbcType=INTEGER}
    AND TASG.AFTER_SALES_GOODS_ID = #{relatedId,jdbcType=INTEGER}
    AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
  </select>

  <select id="getExistBarcodFactoryList" resultType="string">
    select TWGOII.BARCODE_FACTORY
    from T_AFTER_SALES TFS
           JOIN T_WAREHOUSE_GOODS_OUT_IN TW ON TFS.AFTER_SALES_NO = TW.RELATE_NO
           JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TW.OUT_IN_NO = TWGOII.OUT_IN_NO
    WHERE TW.IS_DELETE=0
      AND TWGOII.IS_DELETE=0
      AND TFS.ORDER_NO=#{orderNo,jdbcType=VARCHAR}
      AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
      AND TFS.SUBJECT_TYPE=536
      AND TW.OUT_IN_TYPE IN (6,7)
      AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
  </select>

  <select id="getBatchNumberListByOrderNo" resultType="string">
    select TWGOII.BATCH_NUMBER barcodeFactory
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    WHERE TWGOI.RELATE_NO = #{orderNo,jdbcType=VARCHAR}
    AND TWGOI.OUT_IN_TYPE=1
    AND TWGOII.LOG_TYPE=0
    AND TWGOI.IS_DELETE=0
    AND TWGOII.IS_DELETE=0
    AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
    AND IFNULL(TWGOII.BATCH_NUMBER, '') != ''
  </select>

  <select id="getSaleTraderNameBySaleOrder" parameterType="string" resultType="string">
    select TRADER_NAME from T_SALEORDER T WHERE SALEORDER_NO= #{orderNo,jdbcType=VARCHAR}
    AND IFNULL(T.TRADER_NAME,'') !=''
    AND IS_DELETE=0
    LIMIT 1
  </select>

  <select id="getExistBarcodFactoryForInList" resultType="string">
    select TWGOII.BARCODE_FACTORY
    from T_WAREHOUSE_GOODS_OUT_IN TW
           JOIN T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TW.OUT_IN_NO = TWGOII.OUT_IN_NO
    WHERE TW.IS_DELETE=0
      AND TW.RELATE_NO =#{orderNo,jdbcType=VARCHAR}
      AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
      AND TWGOII.IS_DELETE=0
      AND TW.OUT_IN_TYPE IN (3,5)
      AND IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
  </select>

  <select id="getBuyOrderOrderNo" resultType="string">
    SELECT
      BUYORDER_NO
    FROM
      T_BUYORDER
    WHERE
        SALEORDER_NOS IN (SELECT
                            ORDER_NO
                          FROM
                            T_AFTER_SALES
                          WHERE
                            AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR})
  </select>

  <select id="getSaleOrderNoByAfterSalesNo" resultType="string">
    SELECT
      ORDER_NO
    FROM
      T_AFTER_SALES
    WHERE
      AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR}
  </select>

  <select id="getVedengBatchListByOrderNo" resultType="string">
    select TWGOII.BATCH_NUMBER batchNumber
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
           left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    WHERE TWGOI.RELATE_NO IN
      <foreach collection="orderNos" item="orderNo" index="index"
               open="(" close=")" separator=",">
        #{orderNo,jdbcType=VARCHAR}
      </foreach>
      AND TWGOI.OUT_IN_TYPE=1
      AND TWGOII.LOG_TYPE=0
      AND TWGOI.IS_DELETE=0
      AND TWGOII.IS_DELETE=0
      AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
      AND IFNULL(TWGOII.VEDENG_BATCH_NUMBER, '') != ''
  </select>

  <select id="getVedengBatchListBySaleOrderNo" resultType="string">
    select TWGOII.BATCH_NUMBER batchNumber
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    LEFT JOIN
    T_AFTER_SALES_GOODS TASG ON TWGOII.RELATED_ID = TASG.ORDER_DETAIL_ID
    LEFT JOIN
    T_SALEORDER_GOODS TSG ON TASG.ORDER_DETAIL_ID = TSG.SALEORDER_GOODS_ID
    WHERE TWGOI.RELATE_NO IN
    <foreach collection="orderNos" item="orderNo" index="index"
             open="(" close=")" separator=",">
      #{orderNo,jdbcType=VARCHAR}
    </foreach>
    AND TWGOI.OUT_IN_TYPE=2
    AND TWGOII.LOG_TYPE=1
    AND TWGOI.IS_DELETE=0
    AND TWGOII.IS_DELETE=0
    AND TWGOII.GOODS_ID=#{goodsId,jdbcType=INTEGER}
    AND TASG.AFTER_SALES_GOODS_ID = #{relatedId,jdbcType=INTEGER}
    AND IFNULL(TWGOII.BATCH_NUMBER, '') != ''
  </select>

  <select id="getSaleTraderNameByAftersale" parameterType="string" resultType="string">
    select TB.TRADER_NAME
    from T_AFTER_SALES TA
           JOIN T_SALEORDER TB ON TA.ORDER_NO = TB.SALEORDER_NO
    WHERE TA.AFTER_SALES_NO = #{orderNo,jdbcType=VARCHAR}
      AND IFNULL(TB.TRADER_NAME,'') !=''
    LIMIT 1
  </select>

  <select id="getBarcodFactoryListBySaleOrderGoodsId" resultType="java.lang.String">
    select DISTINCT TWGOII.BARCODE_FACTORY barcodeFactory
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    where TWGOI.OUT_IN_TYPE in (2,4) /*销售出库,销售换货出库*/
    and TWGOII.LOG_TYPE=1 /*出库*/
    and TWGOI.IS_DELETE=0
    and TWGOII.IS_DELETE=0
    and TWGOII.RELATED_ID = #{relatedId,jdbcType=INTEGER}
    and IFNULL(TWGOII.BARCODE_FACTORY, '') != ''
  </select>

  <select id="getWarehouseGoodsOutInBySaleOrderGoodsId" resultType="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
    select
    TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,TWGOII.BARCODE_FACTORY
    from T_WAREHOUSE_GOODS_OUT_IN TWGOI
    left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO
    where TWGOI.OUT_IN_TYPE = 2 /*销售出库*/
    and TWGOII.LOG_TYPE=1 /*出库*/
    and TWGOI.IS_DELETE=0
    and TWGOII.IS_DELETE=0
    and TWGOII.RELATED_ID = #{relatedId,jdbcType=INTEGER}
  </select>

  <select id="getWarehouseGoodsOutInChangeBySaleOrderGoodsId" resultType="com.vedeng.logistics.model.WarehouseGoodsOutInItem">
    select TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID,
    TWGOII.BARCODE_FACTORY
    from T_AFTER_SALES TAS
    inner join T_SALEORDER TS on TS.SALEORDER_ID = TAS.ORDER_ID and TAS.TYPE = 540
    inner join T_SALEORDER_GOODS TSG on TS.SALEORDER_ID = TSG.SALEORDER_ID
    inner join T_WAREHOUSE_GOODS_OUT_IN TWGOI on TWGOI.RELATE_NO = TAS.AFTER_SALES_NO and TWGOI.OUT_IN_TYPE = 4 and TWGOI.IS_DELETE = 0
    inner join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII on TWGOI.OUT_IN_NO = TWGOII.OUT_IN_NO and TWGOII.LOG_TYPE = 1 and TWGOII.IS_DELETE = 0
    where
    TSG.SALEORDER_GOODS_ID = #{relatedId,jdbcType=INTEGER}
  </select>



<!--auto generated by MybatisCodeHelper on 2023-04-03-->
  <update id="logicalDeleteByWarehouseGoodsOutInId">
    update  T_WAREHOUSE_GOODS_OUT_IN set IS_DELETE = 1
    where WAREHOUSE_GOODS_OUT_IN_ID=#{warehouseGoodsOutInId,jdbcType=BIGINT}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-04-03-->
  <select id="findOutInNoByWarehouseGoodsOutInId" resultType="java.lang.String">
    select OUT_IN_NO
    from T_WAREHOUSE_GOODS_OUT_IN
    where WAREHOUSE_GOODS_OUT_IN_ID
    in
    <foreach collection="list" close=")" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>
</mapper>