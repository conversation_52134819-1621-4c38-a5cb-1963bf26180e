<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.wms.mapper.BuyOrderAfterSaleDirectOutLogMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG-->
    <id column="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" jdbcType="INTEGER" property="afterSaleBuyorderDirectOutLogId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="AFTER_SALES_GOODS_ID" jdbcType="INTEGER" property="afterSalesGoodsId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="INTEGER" property="num" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="VEDENG_BATCH_NUM" jdbcType="VARCHAR" property="vedengBatchNum" />
    <result column="PRODUCE_TIME" jdbcType="BIGINT" property="produceTime" />
    <result column="VALID_TIME" jdbcType="BIGINT" property="validTime" />
    <result column="OUT_TIME" jdbcType="BIGINT" property="outTime" />
    <result column="INDUSTRY_BATCH_NUMBER" jdbcType="VARCHAR" property="industryBatchNumber" />
    <result column="STERILIZATION_NUMBER" jdbcType="VARCHAR" property="sterilizationNumber" />
    <result column="REGISTRATION_NUMBER" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="BRAND_NAME" jdbcType="VARCHAR" property="brandName" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="UNIT_NAME" jdbcType="VARCHAR" property="unitName" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID, AFTER_SALES_ID, AFTER_SALES_GOODS_ID, SKU, 
    NUM, ADD_TIME, CREATOR, UPDATE_TIME, UPDATER, IS_DELETE, VEDENG_BATCH_NUM, PRODUCE_TIME, 
    VALID_TIME, OUT_TIME, INDUSTRY_BATCH_NUMBER, STERILIZATION_NUMBER, REGISTRATION_NUMBER, 
    FIRST_ENGAGE_ID, SHOW_NAME, GOODS_ID, BRAND_NAME, MODEL, UNIT_NAME, SPEC
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" keyProperty="afterSaleBuyorderDirectOutLogId" parameterType="com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG (AFTER_SALES_ID, AFTER_SALES_GOODS_ID, 
      SKU, NUM, ADD_TIME, CREATOR, 
      UPDATE_TIME, UPDATER, IS_DELETE, 
      VEDENG_BATCH_NUM, PRODUCE_TIME, VALID_TIME, 
      OUT_TIME, INDUSTRY_BATCH_NUMBER, STERILIZATION_NUMBER, 
      REGISTRATION_NUMBER, FIRST_ENGAGE_ID, SHOW_NAME, 
      GOODS_ID, BRAND_NAME, MODEL, 
      UNIT_NAME, SPEC)
    values (#{afterSalesId,jdbcType=INTEGER}, #{afterSalesGoodsId,jdbcType=INTEGER}, 
      #{sku,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=BIGINT}, #{updater,jdbcType=INTEGER}, #{isDelete,jdbcType=TINYINT}, 
      #{vedengBatchNum,jdbcType=VARCHAR}, #{produceTime,jdbcType=BIGINT}, #{validTime,jdbcType=BIGINT}, 
      #{outTime,jdbcType=BIGINT}, #{industryBatchNumber,jdbcType=VARCHAR}, #{sterilizationNumber,jdbcType=VARCHAR}, 
      #{registrationNumber,jdbcType=VARCHAR}, #{firstEngageId,jdbcType=INTEGER}, #{showName,jdbcType=VARCHAR}, 
      #{goodsId,jdbcType=INTEGER}, #{brandName,jdbcType=VARCHAR}, #{model,jdbcType=VARCHAR}, 
      #{unitName,jdbcType=VARCHAR}, #{spec,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" keyProperty="afterSaleBuyorderDirectOutLogId" parameterType="com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="sku != null and sku != ''">
        SKU,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="vedengBatchNum != null and vedengBatchNum != ''">
        VEDENG_BATCH_NUM,
      </if>
      <if test="produceTime != null">
        PRODUCE_TIME,
      </if>
      <if test="validTime != null">
        VALID_TIME,
      </if>
      <if test="outTime != null">
        OUT_TIME,
      </if>
      <if test="industryBatchNumber != null and industryBatchNumber != ''">
        INDUSTRY_BATCH_NUMBER,
      </if>
      <if test="sterilizationNumber != null and sterilizationNumber != ''">
        STERILIZATION_NUMBER,
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        REGISTRATION_NUMBER,
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID,
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME,
      </if>
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME,
      </if>
      <if test="model != null and model != ''">
        MODEL,
      </if>
      <if test="unitName != null and unitName != ''">
        UNIT_NAME,
      </if>
      <if test="spec != null and spec != ''">
        SPEC,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="vedengBatchNum != null and vedengBatchNum != ''">
        #{vedengBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="produceTime != null">
        #{produceTime,jdbcType=BIGINT},
      </if>
      <if test="validTime != null">
        #{validTime,jdbcType=BIGINT},
      </if>
      <if test="outTime != null">
        #{outTime,jdbcType=BIGINT},
      </if>
      <if test="industryBatchNumber != null and industryBatchNumber != ''">
        #{industryBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="sterilizationNumber != null and sterilizationNumber != ''">
        #{sterilizationNumber,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstEngageId != null">
        #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="showName != null and showName != ''">
        #{showName,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="brandName != null and brandName != ''">
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null and unitName != ''">
        #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        #{spec,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null">
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="sku != null and sku != ''">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="vedengBatchNum != null and vedengBatchNum != ''">
        VEDENG_BATCH_NUM = #{vedengBatchNum,jdbcType=VARCHAR},
      </if>
      <if test="produceTime != null">
        PRODUCE_TIME = #{produceTime,jdbcType=BIGINT},
      </if>
      <if test="validTime != null">
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="outTime != null">
        OUT_TIME = #{outTime,jdbcType=BIGINT},
      </if>
      <if test="industryBatchNumber != null and industryBatchNumber != ''">
        INDUSTRY_BATCH_NUMBER = #{industryBatchNumber,jdbcType=VARCHAR},
      </if>
      <if test="sterilizationNumber != null and sterilizationNumber != ''">
        STERILIZATION_NUMBER = #{sterilizationNumber,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null and registrationNumber != ''">
        REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="firstEngageId != null">
        FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      </if>
      <if test="showName != null and showName != ''">
        SHOW_NAME = #{showName,jdbcType=VARCHAR},
      </if>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="brandName != null and brandName != ''">
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="model != null and model != ''">
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="unitName != null and unitName != ''">
        UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null and spec != ''">
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.wms.domain.entity.BuyOrderAfterSaleDirectOutLogEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      VEDENG_BATCH_NUM = #{vedengBatchNum,jdbcType=VARCHAR},
      PRODUCE_TIME = #{produceTime,jdbcType=BIGINT},
      VALID_TIME = #{validTime,jdbcType=BIGINT},
      OUT_TIME = #{outTime,jdbcType=BIGINT},
      INDUSTRY_BATCH_NUMBER = #{industryBatchNumber,jdbcType=VARCHAR},
      STERILIZATION_NUMBER = #{sterilizationNumber,jdbcType=VARCHAR},
      REGISTRATION_NUMBER = #{registrationNumber,jdbcType=VARCHAR},
      FIRST_ENGAGE_ID = #{firstEngageId,jdbcType=INTEGER},
      SHOW_NAME = #{showName,jdbcType=VARCHAR},
      GOODS_ID = #{goodsId,jdbcType=INTEGER},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      UNIT_NAME = #{unitName,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR}
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AFTER_SALES_GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.afterSalesGoodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SKU = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.sku,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.num,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.addTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.updateTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.isDelete,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="VEDENG_BATCH_NUM = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.vedengBatchNum,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PRODUCE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.produceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="VALID_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.validTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="OUT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.outTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="INDUSTRY_BATCH_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.industryBatchNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="STERILIZATION_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.sterilizationNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REGISTRATION_NUMBER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.registrationNumber,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="FIRST_ENGAGE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.firstEngageId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="SHOW_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.showName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="GOODS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.goodsId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="BRAND_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.brandName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="MODEL = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.model,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UNIT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.unitName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SPEC = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID = #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER} then #{item.spec,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID" keyProperty="afterSaleBuyorderDirectOutLogId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
    (AFTER_SALES_ID, AFTER_SALES_GOODS_ID, SKU, NUM, ADD_TIME, CREATOR, UPDATE_TIME, 
      UPDATER, IS_DELETE, VEDENG_BATCH_NUM, PRODUCE_TIME, VALID_TIME, OUT_TIME, INDUSTRY_BATCH_NUMBER, 
      STERILIZATION_NUMBER, REGISTRATION_NUMBER, FIRST_ENGAGE_ID, SHOW_NAME, GOODS_ID, 
      BRAND_NAME, MODEL, UNIT_NAME, SPEC)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesId,jdbcType=INTEGER}, #{item.afterSalesGoodsId,jdbcType=INTEGER}, 
        #{item.sku,jdbcType=VARCHAR}, #{item.num,jdbcType=INTEGER}, #{item.addTime,jdbcType=BIGINT}, 
        #{item.creator,jdbcType=INTEGER}, #{item.updateTime,jdbcType=BIGINT}, #{item.updater,jdbcType=INTEGER}, 
        #{item.isDelete,jdbcType=TINYINT}, #{item.vedengBatchNum,jdbcType=VARCHAR}, #{item.produceTime,jdbcType=BIGINT}, 
        #{item.validTime,jdbcType=BIGINT}, #{item.outTime,jdbcType=BIGINT}, #{item.industryBatchNumber,jdbcType=VARCHAR}, 
        #{item.sterilizationNumber,jdbcType=VARCHAR}, #{item.registrationNumber,jdbcType=VARCHAR}, 
        #{item.firstEngageId,jdbcType=INTEGER}, #{item.showName,jdbcType=VARCHAR}, #{item.goodsId,jdbcType=INTEGER}, 
        #{item.brandName,jdbcType=VARCHAR}, #{item.model,jdbcType=VARCHAR}, #{item.unitName,jdbcType=VARCHAR}, 
        #{item.spec,jdbcType=VARCHAR})
    </foreach>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-12-05-->
  <select id="findByAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_AFTER_SALE_BUYORDER_DIRECT_OUT_LOG
        <where>
            <if test="afterSaleBuyorderDirectOutLogId != null">
                and AFTER_SALE_BUYORDER_DIRECT_OUT_LOG_ID=#{afterSaleBuyorderDirectOutLogId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesId != null">
                and AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
            </if>
            <if test="afterSalesGoodsId != null">
                and AFTER_SALES_GOODS_ID=#{afterSalesGoodsId,jdbcType=INTEGER}
            </if>
            <if test="sku != null and sku != ''">
                and SKU=#{sku,jdbcType=VARCHAR}
            </if>
            <if test="num != null">
                and NUM=#{num,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME=#{addTime,jdbcType=BIGINT}
            </if>
            <if test="creator != null">
                and CREATOR=#{creator,jdbcType=INTEGER}
            </if>
            <if test="updateTime != null">
                and UPDATE_TIME=#{updateTime,jdbcType=BIGINT}
            </if>
            <if test="updater != null">
                and UPDATER=#{updater,jdbcType=INTEGER}
            </if>
            <if test="isDelete != null">
                and IS_DELETE=#{isDelete,jdbcType=TINYINT}
            </if>
            <if test="vedengBatchNum != null and vedengBatchNum != ''">
                and VEDENG_BATCH_NUM=#{vedengBatchNum,jdbcType=VARCHAR}
            </if>
            <if test="produceTime != null">
                and PRODUCE_TIME=#{produceTime,jdbcType=BIGINT}
            </if>
            <if test="validTime != null">
                and VALID_TIME=#{validTime,jdbcType=BIGINT}
            </if>
            <if test="outTime != null">
                and OUT_TIME=#{outTime,jdbcType=BIGINT}
            </if>
            <if test="industryBatchNumber != null and industryBatchNumber != ''">
                and INDUSTRY_BATCH_NUMBER=#{industryBatchNumber,jdbcType=VARCHAR}
            </if>
            <if test="sterilizationNumber != null and sterilizationNumber != ''">
                and STERILIZATION_NUMBER=#{sterilizationNumber,jdbcType=VARCHAR}
            </if>
            <if test="registrationNumber != null and registrationNumber != ''">
                and REGISTRATION_NUMBER=#{registrationNumber,jdbcType=VARCHAR}
            </if>
            <if test="firstEngageId != null">
                and FIRST_ENGAGE_ID=#{firstEngageId,jdbcType=INTEGER}
            </if>
            <if test="showName != null and showName != ''">
                and SHOW_NAME=#{showName,jdbcType=VARCHAR}
            </if>
            <if test="goodsId != null">
                and GOODS_ID=#{goodsId,jdbcType=INTEGER}
            </if>
            <if test="brandName != null and brandName != ''">
                and BRAND_NAME=#{brandName,jdbcType=VARCHAR}
            </if>
            <if test="model != null and model != ''">
                and MODEL=#{model,jdbcType=VARCHAR}
            </if>
            <if test="unitName != null and unitName != ''">
                and UNIT_NAME=#{unitName,jdbcType=VARCHAR}
            </if>
            <if test="spec != null and spec != ''">
                and SPEC=#{spec,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
</mapper>