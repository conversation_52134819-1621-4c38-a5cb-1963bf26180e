package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/7/23 17 46
 * @Description: 客户账期信用记录类
 */
@Data
public class CustomerBillPeriodCreditHistoryDto {

    /**
     * 账期类型，1正式账期，2临时账期，3订单账期
     */
    private Integer billPeriodType;

    /**
     * 账期申请额度
     */
    private BigDecimal applyAmount;

    /**
     * 剩余可用额度
     */
    private BigDecimal availableAmount;

    /**
     * 使用了账期支付，但是未完全归还的订单集合
     */
    private List<Long> orderIdList;

    /**
     * 未还账期金额
     */
    private BigDecimal unreturnedAmount;

    /**
     * 逾期未归还金额
     */
    private BigDecimal overdueAmount;

    /**
     * 逾期订单数
     */
    private Integer orderCountOfOverdue;

    /**
     * 逾期天数
     */
    private Integer daysOfOverdue;

    /**
     * 订单平均逾期天数
     */
    private BigDecimal avgOverdueDaysByOrder;

    /**
     * 历史账期逾期次数
     */
    private Integer historyCountOfOverdue;

    /**
     * 历史账期历史使用次数
     */
    private Integer historyUsedCount;

    /**
     * 历史账期次数逾期率
     */
    private BigDecimal historyPercentOverdueByUsedCount;

    /**
     * 历史使用账期支付的订单id集合
     */
    private List<Long> historyOrderIdList;

    /**
     * 历史账期订单逾期总金额
     */
    private BigDecimal historyAmountOfOverdue;

    /**
     * 历史逾期订单数
     */
    private Integer historyOrderCountOfOverdue;

    /**
     * 历史逾期天数
     */
    private Integer historyDaysOfOverdue;

    /**
     * 历史订单平均逾期天数
     */
    private BigDecimal historyAvgDaysOfOverdueByOrder;
}
