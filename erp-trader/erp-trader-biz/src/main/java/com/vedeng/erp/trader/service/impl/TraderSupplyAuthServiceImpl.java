package com.vedeng.erp.trader.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.trader.constant.TraderConstant;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.erp.trader.domain.SkuSupplyAuthBo;
import com.vedeng.erp.trader.domain.dto.SkuSupplyAuth;
import com.vedeng.erp.trader.mapper.SkuSupplyAuthMapper;
import com.vedeng.erp.trader.service.TraderSupplyAuthService;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/6/13 11:06
 **/
@Service
@Slf4j
public class TraderSupplyAuthServiceImpl implements TraderSupplyAuthService {

    @Value("${oss_url}")
    private String ossUrl;

    @Resource
    private SkuSupplyAuthMapper skuSupplyAuthMapper;

    @Autowired
    private AttachmentMapper GlobalAttachmentMapper;

    @Override
    @Transactional
    public void saveAuthData(SkuSupplyAuthBo skuSupplyAuthBo, Integer userId) {

        log.info("授权书信息保存入参：{}",JSON.toJSONString(skuSupplyAuthBo));
        // 数据校验
        checkAuthData(skuSupplyAuthBo);
        skuSupplyAuthBo.setCreator(userId);
        // 数据转换
        SkuSupplyAuth skuSupplyAuth = convertData(skuSupplyAuthBo);
        skuSupplyAuth.setAddTime(new Date());
        skuSupplyAuthMapper.insertSelective(skuSupplyAuth);
        // 附件
        if (skuSupplyAuthBo.getOssFile()!=null&&skuSupplyAuthBo.getOssFile().length>0) {
                List<Attachment> attachments = new ArrayList<>();
            for (int i = 0; i < skuSupplyAuthBo.getOssFile().length; i++) {


                Attachment attachment = new Attachment();
                if (skuSupplyAuthBo.getPrefix() != null && skuSupplyAuthBo.getPrefix().length == skuSupplyAuthBo.getOssFile().length) {
                    attachment.setSuffix(skuSupplyAuthBo.getPrefix()[i]);

                    attachment.setUri(skuSupplyAuthBo.getOssFile()[i]);
                    attachment.setDomain(ossUrl);
                    attachment.setAddTime(System.currentTimeMillis());
                    if (attachment.getSuffix().contains("pdf")) {
                        attachment.setAttachmentType(TraderConstant.SKU_AUTH_ATTACHMENT_TYPE_ID_461);
                    } else {
                        attachment.setAttachmentType(TraderConstant.SKU_AUTH_ATTACHMENT_TYPE_ID_460);
                    }

                    attachment.setAttachmentFunction(TraderConstant.SKU_AUTH_ATTACHMENT_FUNCTION_ID_4101);
                    attachment.setRelatedId(skuSupplyAuth.getSkuSupplyAuthId());
                    attachment.setCreator(userId);
                }

                attachments.add(attachment);
            }
                if (CollectionUtils.isNotEmpty(attachments)) {
                    log.info("保存的附件信息：{}",JSON.toJSONString(attachments));
                    GlobalAttachmentMapper.batchInsertAttachmentSelective(attachments);
                }
        }

    }

    /**
     * 保存授权书前台对象分装转换到数据对象
     * @param skuSupplyAuthBo
     * @return
     */
    private SkuSupplyAuth convertData(SkuSupplyAuthBo skuSupplyAuthBo) {

        SkuSupplyAuth skuSupplyAuth = new SkuSupplyAuth();
        BeanUtils.copyProperties(skuSupplyAuthBo, skuSupplyAuth);
        return skuSupplyAuth;
    }

    /**
     * 保存授权书入参校验
     * @param skuSupplyAuthBo
     */
    private void checkAuthData(SkuSupplyAuthBo skuSupplyAuthBo) {

        if (skuSupplyAuthBo == null || skuSupplyAuthBo.getTraderSupplyId() == null || skuSupplyAuthBo.getAuthType() == null || skuSupplyAuthBo.getValidEndTime() == null || skuSupplyAuthBo.getValidStartTime() == null) {
            log.error("保存所需的必要信息不全 skuSupplyAuthVo:{}", JSON.toJSONString(skuSupplyAuthBo));
            throw new ServiceException("保存所需的必要信息不全");
        }

        /*List<SkuSupplyAuthVo> skuSupplyAuthVos = skuSupplyAuthMapper.queryAuthInfoBySupplierId(skuSupplyAuthBo.getTraderSupplyId());
        if (!CollectionUtils.isEmpty(skuSupplyAuthVos)) {
            skuSupplyAuthVos.forEach(item->{
                if (skuSupplyAuthBo.getValidStartTime().before(item.getValidEndTime()) && skuSupplyAuthBo.getValidEndTime().after(item.getValidStartTime())) {
                    Date validStartTime = item.getValidStartTime();
                    Date validEndTime = item.getValidEndTime();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
                    String formatStart = simpleDateFormat.format(validStartTime);
                    String formatEnd = simpleDateFormat.format(validEndTime);
                    throw new ServiceException("生效日期交叉 已有效期" + formatStart + " " + formatEnd);
                }
            });
        }*/
    }
}
