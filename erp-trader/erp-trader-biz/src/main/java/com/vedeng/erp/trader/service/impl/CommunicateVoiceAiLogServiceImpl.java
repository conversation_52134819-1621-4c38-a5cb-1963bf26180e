package com.vedeng.erp.trader.service.impl;

import com.vedeng.erp.trader.dto.CommunicateVoiceAiLogApiDto;
import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.trader.domain.entity.CommunicateVoiceAiLogEntity;
import com.vedeng.erp.trader.mapper.CommunicateVoiceAiLogMapper;
import com.vedeng.erp.trader.service.CommunicateVoiceAiLogService;

@Service
public class CommunicateVoiceAiLogServiceImpl implements CommunicateVoiceAiLogService {

    @Autowired
    private CommunicateVoiceAiLogMapper communicateVoiceAiLogMapper;

    @Override
    public int insert(CommunicateVoiceAiLogEntity record) {
        return communicateVoiceAiLogMapper.insert(record);
    }

    @Override
    public int insertSelective(CommunicateVoiceAiLogEntity record) {
        return communicateVoiceAiLogMapper.insertSelective(record);
    }

    @Override
    public int deleteByPrimaryKey(Long id) {
        return communicateVoiceAiLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    public CommunicateVoiceAiLogEntity selectByPrimaryKey(Long id) {
        return communicateVoiceAiLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(CommunicateVoiceAiLogEntity record) {
        return communicateVoiceAiLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(CommunicateVoiceAiLogEntity record) {
        return communicateVoiceAiLogMapper.updateByPrimaryKey(record);
    }

    @Override
    public Long saveCommunicateVoiceAiLog(CommunicateVoiceAiLogApiDto communicateVoiceAiLogApiDto) {
        CommunicateVoiceAiLogEntity entity = new CommunicateVoiceAiLogEntity();
        entity.setCommunicateRecordId(communicateVoiceAiLogApiDto.getCommunicateRecordId());
        entity.setSenceCode(communicateVoiceAiLogApiDto.getSenceCode());
        entity.setGroupCode(communicateVoiceAiLogApiDto.getGroupCode());
        entity.setGptVersion(communicateVoiceAiLogApiDto.getGptVersion());
        entity.setStartTime(communicateVoiceAiLogApiDto.getStartTime());
        entity.setEndTime(communicateVoiceAiLogApiDto.getEndTime());
        entity.setRequestText(communicateVoiceAiLogApiDto.getRequestText());
        entity.setResponseText(communicateVoiceAiLogApiDto.getResponseText());
        entity.setCreator(communicateVoiceAiLogApiDto.getCreator());
        entity.setAddTime(communicateVoiceAiLogApiDto.getAddTime());
        entity.setModTime(communicateVoiceAiLogApiDto.getModTime());
        communicateVoiceAiLogMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public CommunicateVoiceAiLogApiDto queryLastComunicateVoiceLog(Integer communicateRecordId, String senceCode, String groupCode) {
        return  communicateVoiceAiLogMapper.selectByCommunicateAndSenceCodeGroupCode(communicateRecordId, senceCode, groupCode);
    }
}

