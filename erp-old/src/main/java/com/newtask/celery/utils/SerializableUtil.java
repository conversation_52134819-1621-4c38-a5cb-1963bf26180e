package com.newtask.celery.utils;

import com.newtask.celery.exception.CeleryAspectException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.SerializationUtils;

import java.io.*;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/14 17:50
 */
@Slf4j
public class SerializableUtil {

    public static byte[] buildObjectByte(Object[] args) {
        ObjectOutputStream oos = null;
        try {
            //1.创建序列化的流
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream(1024);
            oos = new ObjectOutputStream(byteArrayOutputStream);
            //2.序列化对象
            oos.writeObject(args);
            oos.flush();
            oos.close();
            return byteArrayOutputStream.toByteArray();
        } catch (Exception e) {
            log.error("序列化java失败 , msg :{}", e.getMessage(), e);

            throw new CeleryAspectException("序列化java失败 , msg :" + e.getMessage());
        } finally {

            IOUtils.closeQuietly(oos);
        }
    }

    public static Object[] toJavaObjectArray(byte[] bytes) {

        ObjectInputStream ois = null;
        try {
            //1.创建反序列化流
            ByteArrayInputStream in = new ByteArrayInputStream(bytes);
            ois = new ObjectInputStream(in);
            //2.进行反序列化操作
            return (Object[]) ois.readObject();
        } catch (Exception e) {
            log.error("反序列化java失败 , msg :{}", e.getMessage(), e);

            throw new CeleryAspectException("反序列化java失败 , msg :" + e.getMessage());
        } finally {
            IOUtils.closeQuietly(ois);
        }
    }


}
