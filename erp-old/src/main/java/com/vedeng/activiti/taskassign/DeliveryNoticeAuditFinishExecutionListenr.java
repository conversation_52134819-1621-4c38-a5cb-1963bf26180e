package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderDeliveryNoticeService;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * <AUTHOR>
 * @ClassName DeliveryNoticeAuditFinishExecutionListenr
 * @Description 发货通知单审核完成时的触发器
 * @Date 2020/7/23 14:02
 */
public class DeliveryNoticeAuditFinishExecutionListenr implements ExecutionListener {
    private  static final Logger LOGGER=LoggerFactory.getLogger(DeliveryNoticeAuditFinishExecutionListenr.class);
    // 运行时注入service
    public static WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private SaleorderDeliveryNoticeService saleorderDeliveryNoticeService;

    private WMSInterfaceFactory wmsInterfaceFactory;

    public DeliveryNoticeAuditFinishExecutionListenr(){
        saleorderDeliveryNoticeService = (SaleorderDeliveryNoticeService) context.getBean(SaleorderDeliveryNoticeService.class);
        wmsInterfaceFactory=(WMSInterfaceFactory) context.getBean(WMSInterfaceFactory.class);
    }

    @Override
    public void notify(DelegateExecution execution) throws Exception {

        String businessKey = execution.getVariable("businessKey").toString();
        int noticeId =  Integer.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString()) ;
        Saleorder saleorder=(Saleorder)execution.getVariable("saleorder");
        User currentUser=(User)execution.getVariable("user");
        Integer status =null;
        Integer auditStatus =null;
        if (pass){
            //审核通过 发货通知单状态变为进行中
            status=ErpConst.DELIVERY_NOTICE_STATUS.JXZ;
            //审核通过 发货通知单的审核状态变为审核通过
            auditStatus=ErpConst.DELIVERY_NOTICE_AUDIT_STATUS.SHTG;

            //todo 发货通知通过WMS出库单下传接口putOriginalSalesOrder下传给WMS，ordertype是SO（销售订单）

            //todo erp 三方仓占用增加

            //todo 下传WMS

            //todo 更新stock服务

            //todo V6-1 --> wms回传出库数据(sku:V1234 逻辑仓:三方仓 出库数量:8)
            //     V6-2 --> update t_saleorder_goods
            //     V6-3 --> update v_wms_logical_order_goods
            // erp订单数据 sku:V1234 数量:10
            // 逻辑仓：合格仓 数量：1 占用：1
            // 逻辑仓：近效期 数量：1 占用：1
            // 逻辑仓：三方仓 数量：8 发货数量：8




            try {
                LOGGER.info("发货通知审核通过-->WMS销售出库单下传的请求:" + JSON.toJSONString(saleorder));
                WmsInterface putSaleOrderOutputInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_ORIGINAL_SALESORDER);
                WmsResponse response = putSaleOrderOutputInterface.request(saleorder,currentUser);
                LOGGER.info("发货通知审核通过-->WMS销售出库单下传的响应:" + JSON.toJSONString(response));
            }catch (Exception e){
                LOGGER.error("发货通知审核通过-->WMS销售出库单下传失败",e);
                //todo 失败是否需要补偿机制
            }


            saleorderDeliveryNoticeService.updateDeliveryNoticeByPrimaryKeySelective(noticeId);
        }else {
            //审核不通过 发货通知单状态变为待确认
            status=ErpConst.DELIVERY_NOTICE_STATUS.DQR;
            //审核不通过 发货通知单的审核状态变为审核不通过
            auditStatus=ErpConst.DELIVERY_NOTICE_AUDIT_STATUS.SHBTG;
        }
        saleorderDeliveryNoticeService.updateSaleorderDeliveryNoticeStatus(noticeId,status);
        saleorderDeliveryNoticeService.updateSaleorderDeliveryNoticeAuditStatus(noticeId,auditStatus);
    }
}
