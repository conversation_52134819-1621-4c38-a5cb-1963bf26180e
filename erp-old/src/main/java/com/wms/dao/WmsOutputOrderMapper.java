package com.wms.dao;

import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.wms.model.po.WmsLendOutOrder;
import com.wms.model.po.WmsOutputOrder;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

public interface WmsOutputOrderMapper {

    int deleteByPrimaryKey(Long id);

    int insertSelective(WmsOutputOrder record);

    WmsOutputOrder selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WmsOutputOrder record);

    List<WmsLendOutOrder> querylistPage(Map<String, Object> map);

    WmsOutputOrder selectByOrderNo(@Param("orderNo") String lendOutNo);

    /** 
     * SKU审核中的外借单
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/9/1 9:52.
     * @author: <PERSON><PERSON><PERSON>.
     * @param skuNo
     * @return: java.util.List<com.vedeng.logistics.model.LendOut>.
     * @throws:  .
     */
    List<WmsOutputOrder> getLendoutValidingBySkuNo(@Param("skuNo") String skuNo);

    /** 
     * sku.审核通过，且归还状态为未归还、部分归还的外借单
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/9/1 10:17.
     * @author: Randy.Xu.
     * @param skuNo
     * @return: java.util.List<com.wms.model.po.WmsOutputOrder>.
     * @throws:  .
     */
    List<WmsOutputOrder> getUnbackLendOutBySkuNo(@Param("skuNo")String skuNo);

    /**
     * 根据盘亏出库单获取订单信息
     * @param orderNo
     * @return WmsOutputOrder
     */
    WmsOutputOrder getInventoryOutByOrderNo(String orderNo);


    /**
     * 根据报废出单号获取订单信息
     * @param orderNo 报废订单号
     * @return WmsOutputOrder
     */
    WmsOutputOrder getWmsOutputOrderByOrderNoAndType(@Param("orderNo") String orderNo,@Param("type") Integer type);

    List<WarehouseGoodsOperateLog> getWarehouseOutList(WarehouseGoodsOperateLog warehouseGoodsOperateLog);

    List<OrderAssistantRelationDo> getOrderAssistantBySkuIdList(@Param("skuIdList") List<Integer> skuIdList);
}