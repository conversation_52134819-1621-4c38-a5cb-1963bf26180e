package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.aftersale.service.AfterSaleAuditInfoApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesApiService;
import com.vedeng.erp.aftersale.service.ExpenseAfterSalesAutoApiService;
import com.vedeng.erp.buyorder.service.BuyorderExpenseAutoApiService;
import com.vedeng.order.service.AutoExpenseOrWarnService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

@Slf4j
public class AutoCreateExpenseAfterExecutionListener implements ExecutionListener {

	private static final long serialVersionUID = 1L;

	// 运行时注入service
	WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

	private final AfterSaleAuditInfoApiService afterSaleAuditInfoApiService = (AfterSaleAuditInfoApiService) context.getBean("afterSaleAuditInfoApiServiceImpl");

	private final ExpenseAfterSalesAutoApiService expenseAfterSalesAutoApiService = (ExpenseAfterSalesAutoApiService) context.getBean("expenseAfterSalesAutoApiServiceImpl");

	private final BuyorderExpenseAutoApiService expenseAutoApiService = (BuyorderExpenseAutoApiService) context.getBean("buyorderExpenseAutoApiServiceImpl");

	private final ExpenseAfterSalesApiService expenseAfterSalesApiService = (ExpenseAfterSalesApiService) context.getBean("expenseAfterSalesServiceImpl");

	private final AutoExpenseOrWarnService autoExpenseOrWarnService = (AutoExpenseOrWarnService) context.getBean("autoExpenseOrWarnServiceImpl");

	@Override
	public void notify(DelegateExecution execution) throws Exception {
		String processKey = execution.getVariable("processDefinitionKey").toString();
		if (StringUtil.isNotBlank(processKey)) {
			AfterSalesVo afterSales = (AfterSalesVo) execution.getVariable("afterSalesInfo");
			log.info("销售售后单审核：{}",JSON.toJSONString(afterSales.getAfterSalesId()));


		}
	}

}
