package com.newtask.historyDataHandelTask;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.AutowireCapableBeanFactory;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 处理2022.5.20-上线的前台出库单
 * @Date 2022/6/30 9:29
 */
@Component
@JobHandler(value = "HandleHistoryPrintOutInfoTask")
@Slf4j
@Deprecated
public class HandleHistoryPrintOutInfoTask extends AbstractJobHandler {

    @Autowired
    SaleorderMapper saleorderMapper;

    @Autowired
    AttachmentMapper attachmentMapper;

    @Autowired
    private AutowireCapableBeanFactory beanFactory;

    @Autowired
    private DefaultListableBeanFactory defaultListableBeanFactory;


    private AbstractOutputOrderProcessor abstractOutputOrderProcessor ;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        abstractOutputOrderProcessor = new AbstractOutputOrderProcessor() {

            @Override
            protected void commonValidator(OutputDto requestBean) throws Exception {

            }

            @Override
            protected void updateOrderData(OutputDto requestBean) throws Exception {

            }

            @Override
            protected int getOperateType(OutputDto requestBean) {
                return 0;
            }

            @Override
            protected int getWmsLogicalOperateType() {
                return 0;
            }

            @Override
            protected int getRelateId(OutputGoodDto outputGoodDto) {
                return 0;
            }

            @Override
            protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
                return null;
            }
        };
        // 将new出的对象放入Spring容器中
        defaultListableBeanFactory.registerSingleton("outputOrderProcessor",abstractOutputOrderProcessor);
        // 自动注入依赖
        beanFactory.autowireBean(abstractOutputOrderProcessor);

        if (StringUtils.isNotBlank(param)){
            log.info("定时任务前台历史出库单处理,订单号{}",param);
            Saleorder saleorderByOrderNo = saleorderMapper.getSaleorderByOrderNo(param);
            excuteMethos(saleorderByOrderNo);
            return SUCCESS;
        }


        int i =1;
        while (true){
            PageHelper.startPage(i,500);
            List<Saleorder> saleorderList = saleorderMapper.getSaleorderListByTimeLimit();
            if (CollectionUtil.isEmpty(saleorderList)){
                break;
            }
            log.info("定时任务前台历史出库单处理：第{}页订单范围{}",i ,JSON.toJSONString(saleorderList.stream().map(Saleorder::getSaleorderNo).collect(Collectors.toList())));
            for (Saleorder saleorder:saleorderList) {
                excuteMethos(saleorder);
                Thread.sleep(2000);
            }
            i++;
        }
        return SUCCESS;
    }


    private void excuteMethos(Saleorder saleorder){
        Attachment attachment = new Attachment();
        attachment.setRelatedId(saleorder.getSaleorderId());
        attachment.setAttachmentType(2004);
        attachment.setAttachmentFunction(4105);
        List<Attachment> attachments1 = attachmentMapper.selectByPropertiesSelective(attachment);
        attachment.setAttachmentFunction(4106);
        List<Attachment> attachments2 = attachmentMapper.selectByPropertiesSelective(attachment);
        if (CollectionUtil.isEmpty(attachments1)&&CollectionUtil.isEmpty(attachments2)){
            abstractOutputOrderProcessor.generatePrintOutOrder(saleorder);
        }
    }
}
