/**
 * 实时显示商品风控情况
 *
 * @param skuNo
 * @param saleorderGoodsId
 */
function checkSaleorderGoodsRisk(skuNo, saleorderGoodsId) {
    $.ajax({
        type: "POST",
        url: "/goods/goods/riskCheckSku.do",
        data: {'skuNo': skuNo},
        dataType: 'json',
        success: function (resultInfo) {
            console.log(resultInfo);
            if (resultInfo.code == -1) {
                layer.alert(resultInfo.message, {
                    closeBtn: 0,
                    btn: ['确定'] //按钮
                }, function () {
                    window.location.reload();
                });
            } else {
                if (resultInfo.data.isRisk) {
                    $('#riskFlag_' + saleorderGoodsId).hide();
                } else {
                    layer.tips("<span style='color:black'>" + resultInfo.data.skuTitle + "</span>", $('#riskFlag_' + saleorderGoodsId), {
                        tips: [1, 'white'],
                        time: 5000
                    });
                }
            }
        },
        error: function (resultInfo) {
            if (resultInfo.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}

//风控图标交互修改
$(function(){

    var ajaxTimeout = null;

    $('.J-fk-icon').hover(function(){
        var _this = this;
        var skuNo = $(this).data('sku');

        ajaxTimeout && clearTimeout(ajaxTimeout);
        ajaxTimeout = setTimeout(function(){
            $.ajax({
                type: "POST",
                url: "/goods/goods/riskCheckSku.do",
                data: {'skuNo': skuNo},
                dataType: 'json',
                success: function (resultInfo) {
                    console.log(resultInfo);
                    if (resultInfo.code == -1) {
                        layer.alert(resultInfo.message, {
                            closeBtn: 0,
                            btn: ['确定'] //按钮
                        }, function () {
                            window.location.reload();
                        });
                    } else {
                        if (resultInfo.data.isRisk) {
                            $(_this).hide()
                        } else {
                            // layer.tips("<span style='color:black'>" + resultInfo.data.skuTitle + "</span>", $('#riskFlag_' + saleorderGoodsId), {
                            //     tips: [1, 'white'],
                            //     time: 5000
                            // });
                            $(_this).addClass('width12');
                            $(_this).find('.J-tips-cnt-txt').html(resultInfo.data.skuTitle).show();
                            $(_this).find('.J-tips-cnt-loading').hide();
                        }
                    }
                },
                error: function (resultInfo) {
                    if (resultInfo.status == 1001) {
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, 300);
    }, function(){
        $(this).removeClass('width12');
        $(this).find('.J-tips-cnt-txt').hide();
        $(this).find('.J-tips-cnt-loading').show();
        ajaxTimeout && clearTimeout(ajaxTimeout);
    })
})


/**
 * 实时显示供应商风控情况
 *
 * @param skuNo
 * @param saleorderGoodsId
 */
function checkRiskTrader(traderId, traderType) {
    $.ajax({
        type: "POST",
        url: "/order/saleorder/checkRiskTrader.do",
        data: {'traderId': traderId,'traderType': traderType},
        dataType: 'json',
        success: function (resultInfo) {
            console.log(resultInfo);
            if (resultInfo.code == -1) {
                layer.alert(resultInfo.message, {
                    closeBtn: 0,
                    btn: ['确定'] //按钮
                }, function () {
                    window.location.reload();
                });
            } else {
                if (resultInfo.data.isRisk) {
                    $('#riskFlag_' + traderId).hide();
                } else {
                    layer.tips("<span style='color:black'>" + resultInfo.data.traderRiskMessage + "</span>", $('#riskFlag_' + traderId), {
                        tips: [1, 'white'],
                        time: 5000
                    });
                }
            }
        },
        error: function (resultInfo) {
            if (resultInfo.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    });
}
