.onShow {
	background-image: url(/mice/images/reg1.gif);
	background-repeat: no-repeat;
	background-position: top left;
	padding: 3px 5px 2px 25px;
	line-height: 16px;
	clear: both
}

.onFocus {
	background-color: #E9F0FF;
	background-image: url(/mice/images/reg2.gif);
	background-repeat: no-repeat;
	background-position: top left;
	padding: 3px 5px 2px 25px;
	line-height: 16px;
	clear: both
}

.onError {
	background-color: #FFC7C7;
	background-image: url(/mice/images/reg3.gif);
	background-repeat: no-repeat;
	background-position: top left;
	padding: 3px 5px 2px 25px;
	line-height: 16px;
	color: #C91B05;
	clear: both
}

.onError img {
	margin: 0 0 2px 0
}

.onSuccess {
	display: none;
}

.onLoad {
	background-color: #FFFFFF;
	background-image: url(/mice/images/loading.gif);
	background-repeat: no-repeat;
	background-position: 3px 3px;
	padding: 3px 5px 2px 25px;
	line-height: 16px;
	display: inline;
}
#dialog,#dialog4login {
    position: fixed;
    _position: absolute;
    width: 525px;
    z-index: 200;
    font-family: Verdana, Arial, Helvetica;
    font-size: 14px;

    padding:6px;
    background:#333;
    -moz-border-radius:4px;
    -webkit-border-radius:4px;
    border-raduis:4px;
    -moz-box-shadow:2px 2px 10px #666;
    -webkit-box-shadow:2px 2px 10px #666;
    box-shadow:2px 2px 10px #666;
}

#dialog-header,#header4login {
    display: block;
    position: relative;
    margin:0;
    padding:0 10px 0 20px;
    background: #f7f7f7;
    height:36px;
    line-height:36px;
    border-bottom:1px solid #ddd
}

#dialog-title,#title4login {
    float: left
}

#dialog-close,#close4login {
    float: right;
    cursor: pointer;
    margin: 12px 3px 0 0;
    height: 11px;
    width: 11px;
    background: url(/mice/images/close.gif) no-repeat
}

#dialog-content,#content4login {
    display: block;
    padding: 6px;
    min-height: 160px;
    color: #666;
    font-size: 14px;
    background:#fff;
}

#dialog-content-inner {
    min-height: 128px;
}

#dialog-mask {
    position: fixed;
    top: 0;
    left: 0;
    height: 100%;
    width: 100%;
    background: #000;
    opacity:0.4;
    filter: Alpha(Opacity=40);
    z-index: 100
}

#dialog-button-container {
    text-align: center;
    margin: 2px 6px 6px 2px;
}

.error {
	background: #fff url(/mice/images/e_bg.jpg) bottom right no-repeat;
	border: 1px solid #924949;
	border-top: none
}

.errorheader {
	background: url(/mice/images/e_hd.gif) repeat-x;
	color: #6f2c2c;
	border: 1px solid #924949;
	border-bottom: none
}

.errorbutton {
	background-color: #9F4C4C;
	border: 1px solid #924949;
	color: #fff;
}

.warning {
	background: #fff url(/mice/images/w_bg.jpg) bottom right no-repeat;
	border: 1px solid #c5a524;
	border-top: none
}

.warningheader {
	background: url(/mice/images/w_hd.gif) repeat-x;
	color: #957c17;
	border: 1px solid #c5a524;
	border-bottom: none
}

.warningbutton {
	background-color: #eFcC47;
	border: 1px solid #957C17;
	color: #957C17;
}

.success {
	background: #fff url(/mice/images/s_bg.jpg) bottom right no-repeat;
	border: 1px solid #60a174;
	border-top: none
}

.successheader {
	background: url(/mice/images/s_hd.gif) repeat-x;
	color: #3c7f51;
	border: 1px solid #60a174;
	border-bottom: none
}

.successbutton {
	background-color: #3c7f51;
	border: 1px solid #fff;
	color: #fff;
}

.prompt {
	background: #fff url(/mice/images/p_bg.jpg) bottom right no-repeat;
	border: 1px solid #4f6d81;
	border-top: none
}

.promptheader {
	background: url(/mice/images/p_hd.gif) repeat-x;
	color: #355468;
	border: 1px solid #4f6d81;
	border-bottom: none
}

.promptbutton {
	background-color: #4F6D81;
	border: 1px solid #fff;
	color: #fff;
}

.confirm {
	background: #fff url(/mice/images/p_bg.jpg) bottom right no-repeat;
	border: 1px solid #4f6d81;
	border-top: none
}

.confirmheader {
	background: url(/mice/images/p_hd.gif) repeat-x;
	color: #355468;
	border: 1px solid #4f6d81;
	border-bottom: none
}

.confirmbutton {
	background-color: #4F6D81;
	border: 1px solid #fff;
	color: #fff;
}

.focus {
	background:#f5f5f5;
	border: 1px solid #ccc;
	border-top: none;
}

.focusheader {
	background:#f5f5f5;
	border: 1px solid #ccc;
	border-bottom: none;
}

.focusbutton {
	background-color: #f5f5f5;
	border: 1px solid #ccc;
	color: #000;
	display:none;
}
/*-change dialog -2013.18 -caojiarui*/
/**
#dialog4login {
	position: fixed;
	width: 525px;
	padding: 0px;
	z-index: 200;
	background: #fff;
	font-family: Verdana, Arial, Helvetica;
	font-size: 14px;
	border:3px solid #ccc;
}

#header4login {
	display: block;
	position: relative;
	padding: 3px 6px 7px;
	height: 14px;
	font-size: 14px;
	font-weight: bold;
}

#title4login {
	float: left
}

#close4login {
	float: right;
	cursor: pointer;
	margin: 3px 3px 0 0;
	height: 11px;
	width: 11px;
	background: url(/mice/images/close.gif) no-repeat
}

#content4login {
	display: block;
	padding: 6px;
	min-height: 160px;
	color: #666666;
	font-size: 13px;
}
**/