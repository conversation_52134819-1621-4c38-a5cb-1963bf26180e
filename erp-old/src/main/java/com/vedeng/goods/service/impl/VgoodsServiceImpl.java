package com.vedeng.goods.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.netflix.discovery.converters.Auto;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.service.SkuPriceModifyRecordService;
import com.vedeng.activiti.ProcessInstanceContext;
import com.vedeng.activiti.model.HistoryVerfiyRecord;
import com.vedeng.authorization.dao.RoleMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.Constants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.goods.GoodsCheckStatusEnum;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.goods.LogTypeEnum;
import com.vedeng.common.constant.goods.SpuLevelEnum;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.http.HttpURLConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.*;
import com.vedeng.department.service.DepartmentsWithCompatibilityService;
import com.vedeng.docSync.enums.DocSyncEventEnum;
import com.vedeng.docSync.model.pojo.generate.DocOfGoodsDo;
import com.vedeng.docSync.service.SyncGoodsService;
import com.vedeng.erp.finance.dto.TaxcodeClassificationDto;
import com.vedeng.erp.finance.service.TaxcodeClassificationApiService;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.firstengage.service.FirstEngageService;
import com.vedeng.goods.command.SkuAddCommand;
import com.vedeng.goods.command.SpuAddCommand;
import com.vedeng.goods.command.SpuSearchCommand;
import com.vedeng.goods.dao.*;
import com.vedeng.goods.enums.*;
import com.vedeng.goods.manager.GoodsInfoChangeListener;
import com.vedeng.goods.manager.constants.GoodsValidConstants;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.dto.*;
import com.vedeng.goods.model.entity.GoodsLevelDo;
import com.vedeng.goods.model.entity.GoodsPositionDo;
import com.vedeng.goods.model.vo.*;
import com.vedeng.goods.service.*;
import com.vedeng.goods.service.api.VgoodsApiService;
import com.vedeng.goods.utils.*;
import com.vedeng.logistics.model.WarehouseStock;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.op.api.dto.sku.SkuOnSaleDto;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.ordergoods.dao.SaleorderGoodsGenerateMapper;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoPurchaseDto;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.RangeDictionaryMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.RangeDictionary;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.SysOptionDefinitionService;
import com.vedeng.system.service.UserService;
import com.vedeng.todolist.service.impl.MaintainDataSpu;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.constant.WMSContant;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsCoreSku;
import com.wms.model.po.WmsQLA;
import com.wms.model.po.WmsSkuReg;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import org.activiti.engine.task.Task;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.Model;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.constants.GoodsValidConstants.SKU_TYPE;

@Service
public class VgoodsServiceImpl extends BaseServiceimpl implements VgoodsService {

    Logger logger = LoggerFactory.getLogger(VgoodsServiceImpl.class);

    // add by Tomcat.Hui 2020/5/19 2:25 下午 .Desc: VDERP-2217 提供预计发货时间给前台. start
    @Autowired
    RangeDictionaryMapper rangeDictionaryMapper;

    @Autowired
    CoreSkuSearchGenerateMapper coreSkuSearchGenerateMapper;
    // add by Tomcat.Hui 2020/5/19 2:25 下午 .Desc: . end

    @Autowired
    private GoodsAttachmentGenerateMapper goodsAttachmentGenerateMapper;


    @Autowired
    private FirstEngageMapper firstEngageMapper;

    @Autowired
    private CoreSpuGenerateExtendMapper coreSpuGenerateExtendMapper;

    @Autowired
    private GoodsGenerateMapper goodsGenerateMapper;


    @Autowired
    private BaseGoodsService baseGoodsService;

    @Autowired
    private BaseCategoryService baseCategoryService;

    @Autowired
    @Lazy
    private FirstEngageService firstEngageService;

    @Autowired
    private SkuPriceModifyRecordService skuPriceModifyRecordService;

    @Autowired
    private SpuAttrMappingGenerateMapper spuAttrMappingGenerateMapper;

    @Autowired
    private SkuAttrMappingGenerateMapper skuAttrMappingGenerateMapper;

    @Value("${api_http}")
    protected String api_http;

    @Value("${file_url}")
    protected String domain;

    @Value("${oss_url}")
    protected String ossDomain;

    @Value("${redis_dbtype}")
    protected String dbType;// 开发redis，测试redis

    @Value("${invi_url}")
    private String inviUrl;

    @Autowired
    private LogCheckGenerateMapper logCheckGenerateMapper;

    @Autowired
    private VerifiesInfoGenerateMapper verifiesInfoGenerateMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Autowired
    private TransGoodsToSkuService transGoodsToSkuService;

    @Autowired
    private CoreSpuGenerateMapper coreSpuGenerateMapper;

    @Autowired
    private CoreSkuGenerateMapper coreSkuGenerateMapper;
    @Autowired
    VedengSoapService vedengSoapService;
    @Autowired
    GoodsMapper goodsMapper;
    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private RegistrationNumberMapper registrationNumberMapper;
    @Resource
    private DepartmentsWithCompatibilityService departmentsWithCompatibilityService;

    @Autowired
    private BaseAttributeValueMapper baseAttributeValueMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private RiskCheckService riskCheckService;
    @Resource
    private GoodsCommonService goodsCommonService;

    @Resource
    private CoreSpuMapper coreSpuMapper;
    @Resource
    private MaintainDataSpu maintainDataSpu;

    @Value("${is_doc_sync}")
    protected Integer isDocSync;

    @Value("${registration_certificate}")
    protected String registrationCertificateStr;

    @Resource
    private List<GoodsInfoChangeListener> goodsInfoChangeListeners;

    @Autowired
    private VgoodsProcInstanceService vgoodsProcInstanceService;

    @Autowired
    private CoreOperateInfoService coreOperateInfoService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    private BuyorderService buyorderService;

    @Autowired
    private VgoodsApiService vgoodsApiService;

    @Autowired
    private UnitService unitService;

    @Autowired
    private MlFacadeService mlFacadeService;

    @Autowired
    private SkuAuthorizationService skuAuthorizationService;

    @Autowired
    private RegionService regionService;

    @Resource
    private SyncGoodsService syncGoodsService;

    @Resource
    private CoreGoodsDisabledStatusHistoryMapper coreGoodsDisabledStatusHistoryMapper;

    @Resource
    private CoreSpuSearchGenerateMapper coreSpuSearchGenerateMapper;

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private DeliverTimeMapper deliverTimeMapper;

    @Autowired
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    
    @Resource
    private CoreSkuHistoryMapper coreSkuHistoryMapper;

    @Autowired
    private TaxcodeClassificationApiService taxcodeClassificationApiService;

    @Override
    public CoreSkuGenerate getSkuDoBySkuId(Integer skuId) {
        if (skuId == null || skuId == 0) {
            return null;
        }
        return coreSkuGenerateMapper.selectByPrimaryKey(skuId);
    }

    @Override
    public void updateSpuOperateInfoFlag(Integer optInfoId, Integer spuId) {
        if (optInfoId == null) {
            optInfoId = 0;
        }
        if (spuId == null) {
            return;
        }
        CoreSpuGenerate generate = new CoreSpuGenerate();
        generate.setSpuId(spuId);
        generate.setOperateInfoId(optInfoId);
        generate.setOperateInfoFlag(CommonConstants.STATUS_1);
        baseGoodsService.mergeSpu(generate);
        //
    }

    @Override
    public void updateSkuOperateInfoFlag(Integer optInfoId, Integer skuId) {
        if (optInfoId == null) {
            optInfoId = 0;
        }
        if (skuId == null) {
            return;
        }
        CoreSkuGenerate generate = new CoreSkuGenerate();
        generate.setSkuId(skuId);
        generate.setOperateInfoId(optInfoId);
        baseGoodsService.mergeSku(generate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SpuUpdatedResultDto saveSpu(HttpServletRequest request, SpuAddCommand spuCommand, final GoodsStorageConditionVo goodsStorageConditionVo) throws ShowErrorMsgException {
        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/attachment");
        BaseCategory thirdCategoryQuery = baseCategoryService.getBaseCategoryInfoById(spuCommand.getCategoryId());
        if (thirdCategoryQuery == null || CommonConstants.IS_DELETE_1.equals(thirdCategoryQuery.getIsDeleted())) {
            throw new ShowErrorMsgException("查询分类信息或已被删除。");
        }

        if (spuCommand.getSpuId() == null) {
            logger.info("添加spu::" + JSON.toJSONString(spuCommand));
            CoreSpuGenerate spu = addSpu(spuCommand, goodsStorageConditionVo);
            spuCommand.setSpuId(spu.getSpuId());
            if (!SpuLevelEnum.isTempSpu(spuCommand.getSpuLevel())) {
                saveSpuPics(spuCommand);
            } else {
                addTempSku(spuCommand.getUser(), spuCommand.getSkuInfo(), spu.getSpuId(), null, spuCommand.getSpuType()
                        , spu.getShowName(), spu);
            }
        } else {
            logger.info("修改spu::" + JSON.toJSONString(spuCommand));
            CoreSpuGenerate spu = checkUpdateCondition(spuCommand);

            updateSpu(spuCommand, goodsStorageConditionVo);

            if (!SpuLevelEnum.isTempSpu(spuCommand.getSpuLevel())) {
                deleteSpuPics(spuCommand);
                saveSpuPics(spuCommand);
            } else {
                //临时商品，需要修改所有skud的名称
                List<CoreSkuGenerate> skuGenerates = selectSkuListBySpuId(spu.getSpuId());
                if (CollectionUtils.isNotEmpty(skuGenerates)) {
                    for (CoreSkuGenerate generate : skuGenerates) {
                        generate.setShowName(spuCommand.getShowName());
                        generate.setSkuName(spuCommand.getShowName());
                        generate.setUpdater(spuCommand.getUser().getUserId());
                        generate.setModTime(new Date());
                        baseGoodsService.mergeSku(generate);
                    }
                }

            }
            tempSpuToCore(path, spuCommand, spu);
        }
        if (!SpuLevelEnum.isTempSpu(spuCommand.getSpuLevel())) {
            mergeAttr(spuCommand);
            //todo 临时兼容方案--编辑spu时同步三级分类下科室信息
            departmentsWithCompatibilityService.syncDepartment2Spu(spuCommand.getSpuId());
        }

        maintainDataSpu.finish(spuCommand.getSpuId());

        GoodsPositionDo goodsPositionQuery = goodsCommonService.getGoodsPosition(spuCommand.getGoodsPositionNo());
        GoodsLevelDo goodsLevelQuery = goodsCommonService.getGoodsLevel(spuCommand.getGoodsLevelNo());
        SpuUpdatedResultDto spuUpdatedResultDto = new SpuUpdatedResultDto();
        spuUpdatedResultDto.setSpuId(spuCommand.getSpuId());
        spuUpdatedResultDto.setSpuName(spuCommand.getShowName());
        spuUpdatedResultDto.setCategoryName(thirdCategoryQuery.getBaseCategoryName());
        spuUpdatedResultDto.setGoodsPositionName(goodsPositionQuery != null ? goodsPositionQuery.getPositionName() : GoodsCommonService.DEFAULT_GOODS_POSITION_NAME);
        spuUpdatedResultDto.setGoodsLevelName(goodsLevelQuery != null ? goodsLevelQuery.getLevelName() : null);
        return spuUpdatedResultDto;
    }

    /**
     * 临时商品转为非临时商品，sku置为待完善
     *
     * @param spuCommand
     * @param spu
     */
    private void tempSpuToCore(String path, SpuAddCommand spuCommand, CoreSpuGenerate spu) {
        if (!SpuLevelEnum.isTempSpu(spuCommand.getSpuLevel()) && SpuLevelEnum.isTempSpu(spu.getSpuLevel())) {//临时商品转为非临时商品，sku置为待完善
            List<CoreSkuGenerate> skuGenerates = selectSkuListBySpuId(spu.getSpuId());
            if (CollectionUtils.isNotEmpty(skuGenerates)) {
                for (CoreSkuGenerate generate : skuGenerates) {
                    checkSku(path, generate.getSkuId(), "临时商品转为非临时商品", GoodsCheckStatusEnum.NEW.getStatus(), spuCommand.getUser());
                }
            }
        }
    }


    /**
     * 合并商品关联的属性
     *
     * @param spuCommand
     * @throws ShowErrorMsgException
     */
    private void mergeAttr(SpuAddCommand spuCommand) throws ShowErrorMsgException {
        Integer[] baseAttrArray = spuCommand.getBaseAttributeIds();
        Integer[] primaryAttrArray = spuCommand.getPrimaryAttributeIds();
        if (ArrayUtils.isEmpty(baseAttrArray)) {
            //VD-3952
            SpuAttrMappingGenerate generate = new SpuAttrMappingGenerate();
            generate.setStatus(CommonConstants.STATUS_0);
            generate.setModTime(new Date());
            generate.setUpdater(spuCommand.getUser().getUserId());
            SpuAttrMappingGenerateExample example1 = new SpuAttrMappingGenerateExample();
            example1.createCriteria()
                    .andSpuIdEqualTo(spuCommand.getSpuId()).andStatusEqualTo(CommonConstants.STATUS_1);
            spuAttrMappingGenerateMapper.updateByExampleSelective(generate, example1);

            //删除所有SKU属性
            List<CoreSkuGenerate> list = selectSkuListBySpuId(spuCommand.getSpuId());
            if (CollectionUtils.isNotEmpty(list)) {
                for (CoreSkuGenerate sku : list) {
                    SkuAttrMappingGenerate kgenerate = new SkuAttrMappingGenerate();
                    kgenerate.setStatus(CommonConstants.STATUS_0);
                    kgenerate.setModTime(new Date());
                    kgenerate.setUpdater(spuCommand.getUser().getUserId());
                    SkuAttrMappingGenerateExample skuAttrMappingGenerateExample = new SkuAttrMappingGenerateExample();
                    skuAttrMappingGenerateExample.createCriteria().andSkuIdEqualTo(sku.getSkuId())
                            .andStatusEqualTo(CommonConstants.STATUS_1);
                    skuAttrMappingGenerateMapper.updateByExampleSelective(kgenerate, skuAttrMappingGenerateExample);
                }
            }
            return;
        }
        SpuAttrMappingGenerateExample example = new SpuAttrMappingGenerateExample();
        example.createCriteria().andSpuIdEqualTo(spuCommand.getSpuId()).andStatusEqualTo(CommonConstants.STATUS_1);
        List<SpuAttrMappingGenerate> list = spuAttrMappingGenerateMapper.selectByExample(example);

        List<Integer> oldAttr = Lists.newArrayList();
        List<Integer> newAttr = Lists.newArrayList(baseAttrArray);
        if (CollectionUtils.isNotEmpty(list)) {
            for (SpuAttrMappingGenerate generate : list) {
                oldAttr.add(generate.getBaseAttributeId());
                if (ArrayUtils.isNotEmpty(primaryAttrArray) && Arrays.asList(primaryAttrArray).contains(generate.getBaseAttributeId())) {
                    generate.setIsPrimary(1);
                } else {
                    generate.setIsPrimary(0);
                }
                spuAttrMappingGenerateMapper.updateByPrimaryKeySelective(generate);
            }
        }

        List<Integer> addAttr = Lists.newArrayList(newAttr);
        addAttr.removeAll(oldAttr);

        List<Integer> deleteAttr = Lists.newArrayList(oldAttr);
        deleteAttr.removeAll(newAttr);

        if (CollectionUtils.isNotEmpty(addAttr)) {
            for (Integer attr : addAttr) {
                SpuAttrMappingGenerate generate = new SpuAttrMappingGenerate();
                generate.setSpuId(spuCommand.getSpuId());
                generate.setStatus(CommonConstants.STATUS_1);
                if (ArrayUtils.isNotEmpty(primaryAttrArray) && Arrays.asList(primaryAttrArray).contains(attr)) {
                    generate.setIsPrimary(1);
                } else {
                    generate.setIsPrimary(0);
                }
                generate.setAddTime(new Date());
                generate.setModTime(new Date());
                generate.setCreator(spuCommand.getUser().getUserId());
                generate.setUpdater(spuCommand.getUser().getUserId());
                generate.setBaseAttributeId(attr);
                spuAttrMappingGenerateMapper.insert(generate);
            }
        }

        if (CollectionUtils.isNotEmpty(deleteAttr)) {
            List<Integer> deleteAttrIntegerList = Lists.newArrayList();
            for (Integer attr : deleteAttr) {
                deleteAttrIntegerList.add(attr);
            }


            SpuAttrMappingGenerate generate = new SpuAttrMappingGenerate();
            generate.setStatus(CommonConstants.STATUS_0);
            generate.setModTime(new Date());
            generate.setUpdater(spuCommand.getUser().getUserId());
            SpuAttrMappingGenerateExample example1 = new SpuAttrMappingGenerateExample();
            example1.createCriteria().andBaseAttributeIdIn(deleteAttrIntegerList)
                    .andSpuIdEqualTo(spuCommand.getSpuId()).andStatusEqualTo(CommonConstants.STATUS_1);
            spuAttrMappingGenerateMapper.updateByExampleSelective(generate, example1);


            //删除所有SKU属性
            List<CoreSkuGenerate> skuList = selectSkuListBySpuId(spuCommand.getSpuId());
            if (CollectionUtils.isNotEmpty(list)) {
                for (CoreSkuGenerate sku : skuList) {
                    SkuAttrMappingGenerate kgenerate = new SkuAttrMappingGenerate();
                    kgenerate.setStatus(CommonConstants.STATUS_0);
                    kgenerate.setModTime(new Date());
                    kgenerate.setUpdater(spuCommand.getUser().getUserId());
                    SkuAttrMappingGenerateExample skuAttrMappingGenerateExample = new SkuAttrMappingGenerateExample();
                    skuAttrMappingGenerateExample.createCriteria().andSkuIdEqualTo(sku.getSkuId())
                            .andStatusEqualTo(CommonConstants.STATUS_1).andBaseAttributeIdIn(deleteAttrIntegerList);
                    skuAttrMappingGenerateMapper.updateByExampleSelective(kgenerate, skuAttrMappingGenerateExample);
                }
            }
            return;
        }

    }

    private CoreSpuGenerate checkUpdateCondition(SpuAddCommand spuCommand) throws ShowErrorMsgException {
        if (spuCommand.getSpuId() == null) {
            throw new ShowErrorMsgException("商品ID不能为空。");
        }
        CoreSpuGenerate generate = coreSpuGenerateMapper.selectByPrimaryKey(spuCommand.getSpuId());
        if (generate == null) {
            throw new ShowErrorMsgException("找不到对应的商品。");
        }


//        if (!FirstEngageCheckStatusEnum.isApprove(firstEngage.getStatus())) {
//            throw new ShowErrorMsgException("当前首营信息审核中，请先对首营信息进行审核。");
//        }
        //TODO 临时取消 状态为待审核，不能更新
//        if (GoodsCheckStatusEnum.isPre(generate.getCheckStatus())) {
//            throw new ShowErrorMsgException("商品状态为审核中 不能修改。");
//        }

        BaseCategoryVo updateCat = baseCategoryService.getBaseCategoryByParam(spuCommand.getCategoryId());
        if (updateCat == null) {
            throw new ShowErrorMsgException("找不到分类。");
        }

        //选择注册证时，需校验注册证是否存在
        if (spuCommand.getHasRegistrationCert()) {
            if (!firstEngageService.associateWithRegistrationCert(spuCommand.getFirstEngageId())) {
                throw new ShowErrorMsgException("找不到对应的注册证/备案证信息!");
            }
        }

        //TODO 临时取消 初始化数据的时候，
//        if (dbCat != null && updateCat != null && dbCat.getBaseCategoryType() != updateCat.getBaseCategoryType()) {
//            throw new ShowErrorMsgException("分类的类型不能发生变更。");
//        }
        return generate;
    }

    @Transactional(rollbackFor = Throwable.class)
    public void updateSpu(SpuAddCommand spuCommand, final GoodsStorageConditionVo goodsStorageConditionVo) {
        CoreSpuGenerate generate = new CoreSpuGenerate();
        generate.setSpuId(spuCommand.getSpuId());
        copySpuProperties(spuCommand, generate);

        //设置spu存储条件
        GoodsStorageConditionUtils.populateStorageCondition(generate, goodsStorageConditionVo);
        generate.setUpdater(spuCommand.getUser().getUserId());
        baseGoodsService.mergeSpu(generate);
        //风控校验是否通过
        if (spuCommand.getSpuId() != null) {
            riskCheckService.checkSpuAndSkuTodo(spuCommand.getSpuId());
        }
    }

    private CoreSpuGenerate addSpu(SpuAddCommand spuCommand, final GoodsStorageConditionVo goodsStorageConditionVo) {
        //新增是校验spu名称是否已经存在
        CoreSpuGenerateExample example = new CoreSpuGenerateExample();
        example.createCriteria().andShowNameEqualTo(spuCommand.getShowName())
                .andStatusEqualTo(CommonConstants.STATUS_1);
        if (coreSpuGenerateMapper.countByExample(example) > 0) {
            logger.error("【SPU校验】新增Spu时SPU名已存在 - spuName:{}", spuCommand.getShowName());
            throw new ShowErrorMsgException(spuCommand.getShowName() + "已经存在，请勿重复提交。");
        }

        if (JedisUtils.exists(RedisKeyUtils.createKey(ErpConst.KEY_PREFIX_SPU_NAME + spuCommand.getShowName()))) {
            throw new ShowErrorMsgException(spuCommand.getShowName() + "已经存在，请勿重复提交。");
        }
        JedisUtils.set(RedisKeyUtils.createKey(ErpConst.KEY_PREFIX_SPU_NAME + spuCommand.getShowName()), String.valueOf(CommonConstants.STATUS_1), 30);
        CoreSpuGenerate generate = new CoreSpuGenerate();
        copySpuProperties(spuCommand, generate);

        //设置spu存储条件
        GoodsStorageConditionUtils.populateStorageCondition(generate, goodsStorageConditionVo);
        generate.setCreator(spuCommand.getUser().getUserId());
        generate.setAddTime(new Date());
        generate.setUpdater(spuCommand.getUser().getUserId());

        baseGoodsService.mergeSpu(generate);

        CoreSpuGenerate generateNo = new CoreSpuGenerate();
        generateNo.setSpuNo(GoodsNoDict.getSpuNo(generate.getSpuId()));
        generate.setSpuNo(generateNo.getSpuNo());
        generateNo.setSpuId(generate.getSpuId());
        generateNo.setModTime(new Date());
        baseGoodsService.mergeSpu(generate);

        return generate;
    }


    /**
     * 根据注册证信息spu是否为医疗器械
     *
     * @return
     */
    private Integer deduceMedicalInstrumentType(Boolean hasRegistrationCert, Integer firstEngageId) {
        if (Boolean.TRUE.equals(hasRegistrationCert) && firstEngageId != null && firstEngageId > 0) {
            if (firstEngageService.associateWithRegistrationCert(firstEngageId)) {
                return GoodsConstants.MEDICAL_INSTRUMENT;
            }
        }
        return GoodsConstants.NOT_MEDICAL_INSTRUMENT;
    }

    private void copySpuProperties(SpuAddCommand spuCommand, CoreSpuGenerate generate) {
        generate.setShowName(spuCommand.getShowName());
        generate.setCategoryId(spuCommand.getCategoryId());
        generate.setBrandId(spuCommand.getBrandId());
        generate.setTaxClassificationCode(Objects.isNull(spuCommand.getTaxClassificationCode()) ? "" : spuCommand.getTaxClassificationCode());
        if (StringUtils.isBlank(spuCommand.getSpuName())) {
            generate.setSpuName(NamingUtils.DEFAULT_NAME_PLACEHOLDER);
        } else {
            generate.setSpuName(spuCommand.getSpuName());
        }
        //if 存在注册证保存首营Id, else 将首Id置为0
        if (Boolean.TRUE.equals(spuCommand.getHasRegistrationCert())) {
            generate.setFirstEngageId(spuCommand.getFirstEngageId());
        } else {
            generate.setFirstEngageId(ErpConst.ZERO);
        }

        //兼容老字段的SPU等级，为空默认置为其他商品
        if (generate.getSpuLevel() == null) {
            generate.setSpuLevel(SpuLevelEnum.getOrDefault(generate.getSpuLevel()));
        }

        if (StringUtils.isNotEmpty(spuCommand.getSpecsModel())) {
            generate.setSpecsModel(spuCommand.getSpecsModel().length() <= 512 ? spuCommand.getSpecsModel()
                    : spuCommand.getSpecsModel().substring(0, 512 - 1));
        } else {
            generate.setSpecsModel(NamingUtils.DEFAULT_NAME_PLACEHOLDER);
        }

        //VDERP-4424 新增/编辑SPU界面，提交时，默认为待提交审核状态
        generate.setCheckStatus(GoodsCheckStatusEnum.WAIT_TO_PRE.getStatus());//直接转为待审核
        generate.setGoodsLevelNo(spuCommand.getGoodsLevelNo());
        generate.setGoodsPositionNo(spuCommand.getGoodsPositionNo());
        generate.setSecondLevelSpuType(spuCommand.getSecondLevelSpuType());
        //    VDERP-7159 【商品体系】新商品流SKU禁用功能
//        generate.setStatus(ErpConst.ONE);
        generate.setSpuType(spuCommand.getSpuType());
        generate.setRegistrationIcon(spuCommand.getRegistrationIcon());
        generate.setWikiHref(spuCommand.getWikiHref());
        generate.setOperateInfoFlag(ErpConst.ZERO);

        //设置器械类型
        generate.setApparatusType(this.deduceMedicalInstrumentType(spuCommand.getHasRegistrationCert(), spuCommand.getFirstEngageId()));
        generate.setAssignmentAssistantId(spuCommand.getAssignmentAssistantId());
        generate.setAssignmentManagerId(spuCommand.getAssignmentManagerId());
        if (StringUtils.isNotBlank(spuCommand.getHospitalTags())) {
            String[] tags = StringUtils.split(spuCommand.getHospitalTags(), "@_@");
            if (ArrayUtils.isNotEmpty(tags)) {
                Set<String> set = Sets.newHashSet();
                for (String tag : tags) {
                    set.add(tag);
                }
                generate.setHospitalTags(StringUtils.join(set, "@_@"));
            }
        }
        generate.setUpdater(spuCommand.getUser().getUserId());

        generate.setMedicalInstrumentCatalogIncluded(Objects.equals(spuCommand.getMedicalInstrumentCatalogIncluded(), CommonConstants.ON)
                ? CommonConstants.ON : CommonConstants.OFF);

        generate.setNoMedicalFirstType(generate.getMedicalInstrumentCatalogIncluded() == 0 ? spuCommand.getNoMedicalFirstType() : 0);
        generate.setNoMedicalSecondType(generate.getMedicalInstrumentCatalogIncluded() == 0 ? spuCommand.getNoMedicalSecondType() : 0);

        generate.setModTime(new Date());
    }


    @Resource
    private UserService userService;


    @Override
    public List<CoreSpuBaseVO> selectSpuListPage(SpuSearchCommand spuCommand, Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("page", page);
        if (StringUtil.isNotBlank(spuCommand.getSearchValue()) && StringUtil.isNumeric(spuCommand.getSearchValue())) {
            spuCommand.setSpuIdSearch(NumberUtils.toInt(spuCommand.getSearchValue()));
        }
        map.put("command", spuCommand);
        spuCommand.setSearchValue(StringUtils.trim(spuCommand.getSearchValue()));
        spuCommand.setBrandName(StringUtils.trim(spuCommand.getBrandName()));
        spuCommand.setProductCompanyName(StringUtils.trim(spuCommand.getProductCompanyName()));
        spuCommand.setDepartmentName(StringUtils.trim(spuCommand.getDepartmentName()));
        Date dateTemp = spuCommand.getModTimeEnd();
        if (spuCommand.getModTimeEnd() != null) {
            spuCommand.setModTimeEnd(DateUtils.addHours(spuCommand.getModTimeEnd(), 23));
            spuCommand.setModTimeEnd(DateUtils.addMinutes(spuCommand.getModTimeEnd(), 59));
            spuCommand.setModTimeEnd(DateUtils.addSeconds(spuCommand.getModTimeEnd(), 59));
        }


        final List<GoodsLevelVo> goodsLevelVos = goodsCommonService.listAllGoodsLevel(false);
        final List<GoodsPositionVo> goodsPositionVos = goodsCommonService.listAllGoodsPosition();

        List<SpuSkuIdForListDTO> list = coreSpuGenerateExtendMapper.selectSpuListPage(map);
        spuCommand.setModTimeEnd(dateTemp);
        if (CollectionUtils.isNotEmpty(list)) {

            List<CoreSpuBaseVO> result = Lists.newArrayList();
            for (SpuSkuIdForListDTO dto : list) {
                CoreSpuBaseDTO baseDto = baseGoodsService.selectSpuBaseById(dto.getSpuId());
                if (baseDto == null) {
                    continue;
                }
                CoreSpuBaseVO spuVo = new CoreSpuBaseVO();
                BeanUtils.copyProperties(spuVo, baseDto);

                //设置产品归属和产品助理
                if (spuVo.getAssignmentManagerId() != null) {
                    User userQuery = userService.getUserById(spuVo.getAssignmentManagerId());
                    if (userQuery != null) {
                        spuVo.setProductMgrName(userQuery.getUsername());
                    }
                }

                if (spuVo.getAssignmentAssistantId() != null) {
                    User userQuery = userService.getUserById(spuVo.getAssignmentAssistantId());
                    if (userQuery != null) {
                        spuVo.setProductAssistantName(userQuery.getUsername());
                    }
                }

                result.add(spuVo);
                String[] skuIds = StringUtils.split(dto.getSkuIds(), ",");
                if (!ArrayUtils.isEmpty(skuIds)) {
                    spuVo.setSkuTotalSize(skuIds.length);
                    spuVo.setSkuIdsInSpu(dto.getSkuIds());
                    /*
                        List<CoreSkuBaseDTO> skuList = baseGoodsService
                            .selectSkuBaseByIds(ArrayUtils.subarray(skuIds, 0, MAX_SKU_PAGE_SIZE));
                        */
                    List<CoreSkuBaseDTO> skuList = baseGoodsService
                            .selectSkuBaseByIdsWithOutStatus(ArrayUtils.subarray(skuIds, 0, MAX_SKU_PAGE_SIZE));
                    if (CollectionUtils.isNotEmpty(skuList)) {
                        for (CoreSkuBaseDTO sku : skuList) {
                            CoreSkuBaseVO skuVo = new CoreSkuBaseVO();
                            BeanUtils.copyProperties(skuVo, sku);
                            skuVo.setPushStatusStr(GoodsUtils.getPlatformStrByInteger(skuVo.getPushStatus(), "/", "未推送"));
                            skuVo.setOnSaleStr(GoodsUtils.getOnSaleStrByPushStatus(skuVo.getPushStatus(), skuVo.getOnSale(), "/", "-"));
                            skuVo.setAssignmentAssistantId(spuVo.getAssignmentAssistantId());
                            skuVo.setAssignmentManagerId(spuVo.getAssignmentManagerId());
                            spuVo.getCoreSkuBaseVOList().add(skuVo);

                            //设置商品的等级与档位
                            Optional<GoodsLevelVo> goodsLevelIfMatch = goodsLevelVos.stream().filter(level -> Objects.equals(sku.getGoodsLevelNo(), level.getId())).findFirst();
                            if (goodsLevelIfMatch.isPresent()) {
                                GoodsLevelVo goodsLevelVo = goodsLevelIfMatch.get();
                                skuVo.setGoodsLevelName(String.join(ErpConst.Symbol.HYPHEN, goodsLevelVo.getUniqueIdentifier(), goodsLevelVo.getLevelName()));
                            } else {
                                skuVo.setGoodsLevelName("无");
                            }

                            Optional<GoodsPositionVo> goodsPositionIfMatch = goodsPositionVos.stream().filter(position -> Objects.equals(sku.getGoodsPositionNo(), position.getId())).findFirst();
                            if (goodsPositionIfMatch.isPresent()) {
                                GoodsPositionVo goodsPositionVo = goodsPositionIfMatch.get();
                                skuVo.setGoodsPositionName(goodsPositionVo.getPositionName());
                            } else {
                                skuVo.setGoodsPositionName("无档位");
                            }
                        }
                    }
                }
            }
            return result;
        }
        return Collections.emptyList();
    }


    @Override
    public String getSaleStr(Integer pushStatus, Integer onSale) {
        int[] pushArr = getBitArrayFromInt(pushStatus);
        int[] onSaleArr = getBitArrayFromInt(onSale);
        String resStr = "";
        for (int i = GoodsConstants.ZERO; i < GoodsConstants.FOUR; i++) {
            String mid = "";
            if (pushArr[i] == 1) {
                if (onSaleArr[i] == GoodsConstants.ZERO) {
                    mid = "下架";
                } else if (onSaleArr[i] == GoodsConstants.ONE) {
                    mid = "上架";
                }
                if (StringUtil.isBlank(resStr)) {
                    resStr = mid;
                } else {
                    resStr = resStr + "/" + mid;
                }
            }

        }
        if (StringUtil.isBlank(resStr)) {
            resStr = "未上架";
        }
        return resStr;
    }

    /**
     * 解析二进制指定位数
     *
     * @param i  状态值 最大32 即 100000  res[3] = 1
     * @return 状态
     */
    private int[] getBitArrayFromInt(Integer i) {
        int[] res = {0, 0, 0, 0};
        if (i == null) {
            return res;
        }
        res[0] = i & GoodsConstants.ONE;
        res[1] = i >>> GoodsConstants.ONE & GoodsConstants.ONE;
        res[2] = i >>> GoodsConstants.TWO & GoodsConstants.ONE;
        // 右移5位，获得第6位数（科研特卖汇）
        res[3] = i >>> GoodsConstants.FIVE & GoodsConstants.ONE;
        return res;
    }

    /**
     * @throws Exception
     */
    @Override
    public void initSpu(SpuAddCommand spuCommand) throws Exception {
        if (spuCommand.getSpuId() == null) {
            return;
        }

        CoreSpuGenerate generate = coreSpuGenerateMapper.selectByPrimaryKey(spuCommand.getSpuId());
        if (generate == null) {
            throw new ShowErrorMsgException("找不到对应的商品！");
        }

        BeanUtils.copyProperties(spuCommand, generate);
        if (StrUtil.isNotBlank(generate.getTaxClassificationCode())) {
            TaxcodeClassificationDto taxcodeClassificationDto = taxcodeClassificationApiService.findByCode(generate.getTaxClassificationCode());
            if (Objects.nonNull(taxcodeClassificationDto)) {
                spuCommand.setTaxCodeSimpleName(taxcodeClassificationDto.getClassificationAbbreviation());
            }
        }

        boolean hasRegistrationCert = false;
        if (generate.getFirstEngageId() != null && generate.getFirstEngageId() > 0) {
            // 根据首营编号获取其关联的注册证
            hasRegistrationCert = firstEngageService.associateWithRegistrationCert(generate.getFirstEngageId());
        }

        //设置等级与档位
        spuCommand.setGoodsLevelVo(goodsCommonService.getGoodsLevelVo(generate.getGoodsLevelNo()));
        spuCommand.setGoodsPositionVo(goodsCommonService.getGoodsPositionVo(generate.getGoodsPositionNo()));

        //是否关联了注册证信息
        spuCommand.setHasRegistrationCert(hasRegistrationCert);

        //spu存储条存储条件
        spuCommand.setGoodsStorageConditionVo(GoodsStorageConditionUtils.createGoodsStorageConditionVo(generate));

        //获取商品的已经选择的属性
        List<BaseAttributeVo> selectedAttr = coreSpuGenerateExtendMapper.selectAllAttributeBySpuId(spuCommand.getSpuId());

        List<Integer> baseAttributeIds = Lists.newArrayList();
        List<Integer> primaryAttributeIds = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(selectedAttr)) {
            for (BaseAttributeVo attr : selectedAttr) {
                baseAttributeIds.add(attr.getBaseAttributeId());
                if (ErpConst.ONE.equals(attr.getIsPrimary())) {
                    primaryAttributeIds.add(attr.getBaseAttributeId());
                }
            }
            spuCommand.setPrimaryAttributeIds(primaryAttributeIds.toArray(new Integer[0]));
            spuCommand.setBaseAttributeIds(baseAttributeIds.toArray(new Integer[0]));
        }
    }

    @Override
    public List<FileInfo> listGoodsAttachmentAsFileInfo(Integer attacheType, Integer goodsId) {
        if (goodsId == null || attacheType == null) {
            return Collections.emptyList();
        }

        GoodsAttachmentGenerateExample goodsAttachmentExample658 = new GoodsAttachmentGenerateExample();
        goodsAttachmentExample658.createCriteria().andAttachmentTypeEqualTo(attacheType).andGoodsIdEqualTo(goodsId)
                .andStatusEqualTo(CommonConstants.STATUS_1);
        List<GoodsAttachmentGenerate> goodsAttachmentList = goodsAttachmentGenerateMapper.selectByExample(goodsAttachmentExample658);

        List<FileInfo> resultList = Lists.newArrayList();
        if (!goodsAttachmentList.isEmpty()) {
            for (GoodsAttachmentGenerate goodsAttachment : goodsAttachmentList) {
                FileInfo fileInfo = new FileInfo();
                fileInfo.setHttpUrl(api_http + goodsAttachment.getDomain());
                if (StringUtils.isNotEmpty(goodsAttachment.getDisplayName())) {
                    String fileExtName = FileUtils.getFileExtName(goodsAttachment.getDisplayName());
                    fileInfo.setFileName(goodsAttachment.getDisplayName());
                    fileInfo.setPrefix(fileExtName);
                }
                fileInfo.setFilePath(goodsAttachment.getUri());
                resultList.add(fileInfo);
            }
        }
        return resultList;
    }

    @Override
    public void backupSku(SkuAddCommand command) {
        logger.info("批量设置备货::" + JSON.toJSONString(command));
        if (!command.isHasEditAuth()) {
            throw new ShowErrorMsgException("权限不足");
        }
        List<Integer> list = Lists.newArrayList();
        if (StringUtils.isNotBlank(command.getSkuIds())) {
            String[] skuids = StringUtils.split(command.getSkuIds(), ",");
            if (ArrayUtils.isNotEmpty(skuids)) {
                for (String skuId : skuids) {
                    list.add(NumberUtils.toInt(skuId));
                }
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        CoreSkuGenerate generate = new CoreSkuGenerate();
        generate.setIsStockup(command.getHasBackupMachine());
        generate.setUpdater(command.getUser().getUserId());
        generate.setModTime(new Date());
        baseGoodsService.mergeSkuByIds(generate, list);
        //coreSkuGenerateMapper.updateByExampleSelective(generate, example);
    }

    @Override
    @Deprecated
    public void submitToCheck(SpuAddCommand spuCommand) throws ShowErrorMsgException {
        if (spuCommand.getSpuId() == null) {
            throw new ShowErrorMsgException("找不到对应的商品！");
        }
        CoreSpuGenerate generate = coreSpuGenerateMapper.selectByPrimaryKey(spuCommand.getSpuId());
        if (generate == null) {
            throw new ShowErrorMsgException("找不到对应的商品！");
        }
        //检查权限
        if (SpuLevelEnum.isTempSpu(generate.getSpuLevel()) && !spuCommand.isHasEditTempAuth()) {
            throw new ShowErrorMsgException("权限不足");
        }
        if (!SpuLevelEnum.isTempSpu(generate.getSpuLevel()) && !spuCommand.isHasEditAuth()) {
            throw new ShowErrorMsgException("权限不足");
        }
        if (GoodsCheckStatusEnum.isPre(generate.getCheckStatus())) {
            throw new ShowErrorMsgException("审核中的商品不能提交审核");
        }
        coreSpuGenerateExtendMapper.submitToCheck(spuCommand.getSpuId(), spuCommand.getUser().getUserId());
    }

    @Override
    @Transactional
    public void checkSpu(HttpServletRequest request, SpuAddCommand spuCommand) throws ShowErrorMsgException {
        logger.info("审核SPU::" + JSON.toJSONString(spuCommand));
        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/attachment");
        if (spuCommand.getSpuId() == null) {
            throw new ShowErrorMsgException("找不到对应的商品！");
        }
        CoreSpuBaseDTO baseSpu = baseGoodsService.selectSpuBaseById(spuCommand.getSpuId());

        if (baseSpu == null) {
            throw new ShowErrorMsgException("找不到对应的商品！");
        }
        if (GoodsCheckStatusEnum.isReject(spuCommand.getSpuCheckStatus()) && StringUtils.isBlank(spuCommand.getLastCheckReason())) {
            throw new ShowErrorMsgException("审核不通过原因不能为空！");
        }
        if (!CommonConstants.STATUS_1.equals(baseSpu.getStatus())) {
            throw new ShowErrorMsgException("SPU启用状态不为已启用，不能审核此SPU");
        }
        CoreSpuGenerate generate = new CoreSpuGenerate();
        generate.setUpdater(spuCommand.getUser().getUserId());
        generate.setModTime(new Date());
        generate.setSpuId(spuCommand.getSpuId());
        generate.setCheckStatus(spuCommand.getSpuCheckStatus());

        generate.setChecker(spuCommand.getUser().getUserId());
        generate.setCheckTime(new Date());
        generate.setLastCheckReason(spuCommand.getLastCheckReason());
        // 首营信息审核不通过，不能更新
//        BaseCategoryVo categoryVo = baseCategoryService.getBaseCategoryByParam(baseSpu.getCategoryId());
//        if (categoryVo != null && CommonConstants.CATEGORY_TYPE_YILIAO.equals(categoryVo.getBaseCategoryType())) {
        FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(baseSpu.getFirstEngageId());
//            if (firstEngage == null) {
//                throw new ShowErrorMsgException("找不到对应的首营信息。");
//            }
        if (firstEngage != null && !GoodsCheckStatusEnum.isApprove(firstEngage.getStatus())) {
            throw new ShowErrorMsgException("当前注册证/备案凭证未审核通过，请暂停对该SPU审核");
        }
        // }
        baseGoodsService.mergeSpu(generate);
        // coreSpuGenerateMapper.updateByPrimaryKeySelective(generate);
        //如果是审核不通过，则sku都审核不通过
        if (GoodsCheckStatusEnum.isReject(spuCommand.getSpuCheckStatus())) {
            List<CoreSkuGenerate> skuList = selectSkuListBySpuId(spuCommand.getSpuId());
            if (!CollectionUtils.isEmpty(skuList)) {
                for (CoreSkuGenerate skuInDb : skuList) {
                    checkSku(path, skuInDb.getSkuId(), spuCommand.getLastCheckReason(), GoodsCheckStatusEnum.REJECT.getStatus(), spuCommand.getUser());
                }
            }
        }

        //spu提交审核时，状态为待审核，那么spu下的所有sku的推送状态都更新为未推送
       /* if (GoodsCheckStatusEnum.PRE.getStatus() == spuCommand.getSpuCheckStatus()){
            baseGoodsService.updatePushStatusBySpuId(generate.getSpuId(),GoodsConstants.PUSH_STATUS_UN_PUSH);
        }*/

        generateCheckLogAndSave(spuCommand.getSpuId(), spuCommand.getSpuCheckStatus(), spuCommand.getLastCheckReason(), LogTypeEnum.SPU.getLogType());

        //VDERP-4424 SPU审核消息提醒
        sendMessageWhenCheckSpu(baseSpu, spuCommand.getSpuCheckStatus());


        if ((isDocSync == 1) && GoodsCheckStatusEnum.isApprove(generate.getCheckStatus()) && firstEngage != null) {

            syncGoodsService.syncGoods2Doc(DocSyncEventEnum.SPU_SYNC.getType(), generate.getSpuId(), firstEngage.getRegistrationNumberId());
        }

    }

    private void sendMessageWhenCheckSpu(CoreSpuBaseDTO spuBaseDTO, Integer checkStatus) {
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put("spu", spuBaseDTO.getSpuNo());
        if (checkStatus == GoodsCheckStatusEnum.PRE.getStatus()) {
            List<Integer> qualityManagerList = roleMapper.getUserIdByRoleName(GoodsConstants.GOODS_CHECK_ROLE, 1);
            if (qualityManagerList.size() > 0) {
                MessageUtil.sendMessage(151, qualityManagerList, paramMap, "./goods/vgoods/viewSpu.do?spuId=" + spuBaseDTO.getSpuId());
            }
        } else if (checkStatus == GoodsCheckStatusEnum.APPROVE.getStatus()) {
            MessageUtil.sendMessage(152, Collections.singletonList(spuBaseDTO.getUpdater()), paramMap, "./goods/vgoods/viewSpu.do?spuId=" + spuBaseDTO.getSpuId());
        } else if (checkStatus == GoodsCheckStatusEnum.REJECT.getStatus()) {
            MessageUtil.sendMessage(153, Collections.singletonList(spuBaseDTO.getUpdater()), paramMap, "./goods/vgoods/viewSpu.do?spuId=" + spuBaseDTO.getSpuId());
        }
    }


    /**
     * @param user
     * @param skuInfo
     * @param spuType
     * @return CoreSkuGenerate
     */
    @Deprecated
    public CoreSkuGenerate addTempSku(User user, String skuInfo, Integer spuId, Integer skuId, Integer spuType, String spuName, CoreSpuGenerate spu) {
        logger.info("addTempSku::" + JSON.toJSONString(user) + ",skuInfo=" + skuInfo + ",spuId=" + spuId + ",skuId=" + skuId + ",spuType=" + spuType + ",spuName=" + spuName);
        //有意思的业务

        CoreSkuGenerate skuGenerate = new CoreSkuGenerate();
        skuGenerate.setAddTime(new Date());
        skuGenerate.setCreator(user.getUserId());
        skuGenerate.setSpuId(spuId);

        //新增临时商品时带入存储条件
        if (spu != null) {
            skuGenerate.setStorageConditionOne(spu.getStorageConditionTemperature());
            skuGenerate.setStorageConditionOneLowerValue(spu.getStorageConditionTemperatureLowerValue());
            skuGenerate.setStorageConditionOneUpperValue(spu.getStorageConditionTemperatureUpperValue());
            skuGenerate.setStorageConditionHumidityLowerValue(spu.getStorageConditionHumidityLowerValue());
            skuGenerate.setStorageConditionHumidityUpperValue(spu.getStorageConditionHumidityUpperValue());
            skuGenerate.setStorageConditionTwo(spu.getStorageConditionOthers());

            //兼容下老数据等级
            skuGenerate.setGoodsLevelNo(spu.getGoodsLevelNo() != null ? spu.getGoodsLevelNo() : 0);
            skuGenerate.setGoodsPositionNo(spu.getGoodsPositionNo() != null ? spu.getGoodsPositionNo() : 0);
        } else {
            skuGenerate.setGoodsLevelNo(0);
            skuGenerate.setGoodsPositionNo(0);
        }

        skuGenerate.setCheckStatus(GoodsCheckStatusEnum.PRE.getStatus());

        SysOptionDefinition option = getSysOptionDefinitionById(spuType);
        if (option == null) {
            throw new ShowErrorMsgException("商品类型未定义");
        }

        //临时SKU属于耗材时,需维护规格
        if (GoodsUtils.isConsumables(option.getSysOptionDefinitionId())) {
            if (StringUtils.isBlank(skuInfo)) {
                throw new ShowErrorMsgException("耗材商品规格不能为空");
            } else {
                skuGenerate.setSpec(skuInfo);
            }
        } else {
            //临时SKU属于器械时,需维护制造商型号
            if (StringUtils.isBlank(skuInfo)) {
                throw new ShowErrorMsgException("制造商型号不能为空");
            } else {
                skuGenerate.setModel(skuInfo);
            }
        }
        if (skuId != null) {
            CoreSkuGenerate skuInDb = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
            if (skuInDb == null) {
                throw new ShowErrorMsgException("sku不存在" + skuId);
            }
            if (GoodsCheckStatusEnum.isPre(skuInDb.getCheckStatus())) {
                throw new ShowErrorMsgException("sku当前状态为审核中，无法修改。");
            }
            skuGenerate.setAddTime(null);
            skuGenerate.setCreator(null);
            skuGenerate.setSkuId(skuId);
            skuGenerate.setUpdater(user.getUserId());
            skuGenerate.setModTime(new Date());
            skuGenerate.setSkuName(spuName);
            skuGenerate.setShowName(spuName + skuInfo);
            skuGenerate.setCheckStatus(GoodsCheckStatusEnum.PRE.getStatus());
            baseGoodsService.mergeSku(skuGenerate);
            skuUncheckSync(skuId, spu, skuGenerate, user);
            oldSkuToUnchkUpdate(skuGenerate.getSkuId(), 0, user.getUsername());
        } else {

            if (tempSkuExsit(spuId, skuGenerate.getModel(), skuGenerate.getSpec())) {
                throw new ShowErrorMsgException("重复提交或者此规格型号已经存在");
            }
            skuGenerate.setSkuName(spuName);
            skuGenerate.setShowName(spuName + skuInfo);
            insertWithSkuNo(skuGenerate);
            skuUncheckSync(null, spu, skuGenerate, user);
//            oldSkuToUnchk(skuGenerate.getSkuId(), 0, user.getUsername());
        }
        return skuGenerate;
    }

    private boolean tempSkuExsit(Integer spuId, String model, String spec) {
        return coreSpuGenerateExtendMapper.countSkuBySpuIdAndSkuInfo(spuId, model, spec) > 0;
    }

    private void insertWithSkuNo(CoreSkuGenerate skuGenerate) {
        baseGoodsService.mergeSku(skuGenerate);
        CoreSkuGenerate temp = new CoreSkuGenerate();
        temp.setSkuNo(GoodsNoDict.getSkuNo(skuGenerate.getSkuId()));
        temp.setSkuId(skuGenerate.getSkuId());
        temp.setModTime(new Date());
        temp.setStatus(CommonConstants.STATUS_1);
        baseGoodsService.mergeSku(temp);
        skuGenerate.setSkuNo(temp.getSkuNo());
    }

    private void checkSku(String path, int skuId, String reason, Integer checkStatus, User user) {

        logger.info("checkSku::" + JSON.toJSONString(user) + ",path=" + path + ",checkStatus=" + checkStatus + ",reason=" + reason + ",skuId=" + skuId);
        CoreSkuGenerate toOld = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if (!CommonConstants.STATUS_1.equals(toOld.getStatus())) {
            throw new ShowErrorMsgException("SKU已禁用，不能审核此SKU");
        }
        CoreSpuGenerate spuGenerate = coreSpuGenerateMapper.selectByPrimaryKey(toOld.getSpuId());
        if (!CommonConstants.STATUS_1.equals(spuGenerate.getStatus())) {
            throw new ShowErrorMsgException("SPU已禁用，不能审核此SKU");
        }

        CoreSkuGenerate skuGenerate = new CoreSkuGenerate();
        skuGenerate.setCheckStatus(checkStatus);
        skuGenerate.setChecker(user.getUserId());
        skuGenerate.setCheckTime(new Date());
        skuGenerate.setLastCheckReason(reason);
        skuGenerate.setSkuId(skuId);
        skuGenerate.setUpdater(user.getUserId());
        skuGenerate.setModTime(new Date());
        baseGoodsService.mergeSku(skuGenerate);
        //如果sku审核通过，同步数据到老表中
        if (GoodsCheckStatusEnum.isApprove(checkStatus)) {
            //同步到goods表
            skuUncheckSync(skuId, spuGenerate, skuGenerate, user);
            //同步到官网
//            try{
//            vedengSoapService.goodsSync(skuId);
//            }catch(Exception e){
//                logger.error("同步到php失败",e);
//            }
            //商品档案下传WMS
            sendSkuInfoToWMS(toOld);

            //商品注册证下传WMS
            try {
                WmsSkuReg wmsSkuReg = registrationNumberMapper.getWmsSkuRegData(skuId);
                if (wmsSkuReg != null) {
                    logger.info("ERP下传商品多注册证至WMS的请求:" + JSON.toJSONString(wmsSkuReg));
                    WmsInterface putSkuInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_REG);
                    WmsResponse response = putSkuInterface.request(wmsSkuReg);
                    logger.info("ERP下传商品多注册证至WMS的响应:" + JSON.toJSONString(response));
                }
            } catch (Exception e) {
                logger.error("ERP下传商品多注册证至WMS请求接口报错", e);
            }

            //商品资质下传WMS
            try {
                List<WmsQLA> wmsQLAList = attachmentMapper.getWmsQlaListBySkuId(skuId);
                if (CollectionUtils.isNotEmpty(wmsQLAList)) {
                    logger.info("ERP下传商品资质至WMS的请求:" + JSON.toJSONString(wmsQLAList));
                    WmsInterface putQalInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_QLA);
                    WmsResponse response = putQalInterface.request(wmsQLAList.toArray());
                    logger.info("ERP下传商品资质至WMS的响应:" + JSON.toJSONString(response));
                }
            } catch (Exception e) {
                logger.error("ERP下传商品资质至WMS请求接口报错", e);
            }
        }
        VerifiesInfoGenerateExample example = new VerifiesInfoGenerateExample();
        example.createCriteria().andRelateTableEqualTo("T_GOODS")
                .andRelateTableKeyEqualTo(skuId);
        if (verifiesInfoGenerateMapper.countByExample(example) > 0) {
            oldSkuToUnchkUpdate(skuId, checkStatus, user.getUsername());
        } else {
            //默认审核通过
            oldSkuToUnchk(skuId, checkStatus, user.getUsername());
        }

        //同步更新时间
        GoodsGenerateWithBLOBs goodsGenerate = new GoodsGenerateWithBLOBs();
        goodsGenerate.setGoodsId(skuGenerate.getSkuId());
        goodsGenerate.setModTime(skuGenerate.getModTime() == null ? 0 : skuGenerate.getModTime().getTime());
        goodsGenerate.setUpdater(user.getUserId());
        goodsGenerateMapper.updateByPrimaryKeySelective(goodsGenerate);

        //添加审核记录
        generateCheckLogAndSave(skuId, checkStatus, reason, LogTypeEnum.SKU.getLogType());

        //VDERP-4293 推送站内信
        sendMessageWhenCheckSku(toOld, checkStatus);

        if ((isDocSync == 1) && GoodsCheckStatusEnum.isApprove(checkStatus) && null != spuGenerate.getFirstEngageId()) {
            //获取首营信息
            FirstEngage firstEngage = firstEngageService.getFirstSearchBaseInfo(spuGenerate.getFirstEngageId());

            if (Objects.nonNull(firstEngage) && Objects.nonNull(firstEngage.getRegistrationNumberId())) {

                syncGoodsService.syncGoods2Doc(DocSyncEventEnum.SKU_SYNC.getType(), skuGenerate.getSkuId(), firstEngage.getRegistrationNumberId());
            }
        }

    }

    // 商品档案下传WMS（由于SKU启用/禁用也需要下传，所以将此方法提出来作为公共方法）
    private void sendSkuInfoToWMS(CoreSkuGenerate toOld) {
        try {
            WmsCoreSku wmsCoreSku = coreSkuGenerateMapper.getInputSkuToWmsBySkuId(toOld.getSkuId());
            logger.info("ERP下传商品档案至WMS的请求:" + JSON.toJSONString(toOld));
            WmsInterface putSkuInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_SKU);
            WmsResponse response = putSkuInterface.request(wmsCoreSku);
            logger.info("ERP下传商品档案至WMS的响应:" + JSON.toJSONString(response));
        } catch (Exception e) {
            logger.error("ERP下传商品档案至WMS请求接口报错", e);
        }
    }

    private void sendMessageWhenCheckSku(CoreSkuGenerate skuGenerate, Integer checkStatus) {
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put("sku", skuGenerate.getSkuNo());
        if (checkStatus == GoodsCheckStatusEnum.PRE.getStatus()) {
            List<Integer> qualityManagerList = roleMapper.getUserIdByRoleName(GoodsConstants.GOODS_CHECK_ROLE, 1);
            if (qualityManagerList.size() > 0) {
                MessageUtil.sendMessage(154, qualityManagerList, paramMap, "./goods/vgoods/viewSku.do?pageType=0&skuId=" + skuGenerate.getSkuId());
            }
        } else if (checkStatus == GoodsCheckStatusEnum.APPROVE.getStatus()) {
            MessageUtil.sendMessage(155, Collections.singletonList(skuGenerate.getUpdater()), paramMap, "./goods/vgoods/viewSku.do?pageType=0&skuId=" + skuGenerate.getSkuId());
        } else if (checkStatus == GoodsCheckStatusEnum.REJECT.getStatus()) {
            MessageUtil.sendMessage(156, Collections.singletonList(skuGenerate.getUpdater()), paramMap, "./goods/vgoods/viewSku.do?pageType=0&skuId=" + skuGenerate.getSkuId());
        }
    }


    private void skuUncheckSync(Integer skuId, CoreSpuGenerate spuGenerate, CoreSkuGenerate skuGenerate, User user) {
        if (skuId != null) {
            skuGenerate = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        }
        if (spuGenerate == null) {
            spuGenerate = coreSpuGenerateMapper.selectByPrimaryKey(skuGenerate.getSpuId());
        }
        GoodsGenerateWithBLOBs goodsGenerate = transGoodsToSkuService.sku2Goods(skuGenerate, spuGenerate);
        // 首营信息审核不通过，不能更新
        FirstEngage firstEngage = firstEngageService.getFirstSearchBaseInfo(spuGenerate.getFirstEngageId());

        //非医疗器械不同步首营信息
        if (firstEngage != null && firstEngage.getRegistration() != null) {
            //新国标分类
            goodsGenerate.setStandardCategoryId(firstEngage.getNewStandardCategoryId());
            //旧国标
            goodsGenerate.setManageCategory(firstEngage.getOldStandardCategoryId());
            //管理级别 老的：339 340 341 新的： 968,969,970
            if (firstEngage.getRegistration() != null) {
                //兼容为空的情况
                if (firstEngage.getRegistration().getManageCategoryLevel() != null && firstEngage.getRegistration().getManageCategoryLevel() > 629) {
                    goodsGenerate.setManageCategoryLevel(firstEngage.getRegistration().getManageCategoryLevel() - 629);
                } else {
                    goodsGenerate.setManageCategoryLevel(0);
                }
                goodsGenerate.setBegintime(firstEngage.getRegistration().getIssuingDate());
                goodsGenerate.setEndtime(firstEngage.getRegistration().getEffectiveDate());
                goodsGenerate.setProductAddress(firstEngage.getRegistration().getProductionAddress());

                try {
                    //同步注册证图片
                    CoreSkuGenerateExample skuGenerateExample = new CoreSkuGenerateExample();
                    skuGenerateExample.createCriteria().andSpuIdEqualTo(spuGenerate.getSpuId());
                    List<CoreSkuGenerate> skuGenerateList = coreSkuGenerateMapper.selectByExample(skuGenerateExample);
                    if (CollectionUtils.isNotEmpty(skuGenerateList)) {
                        for (CoreSkuGenerate genereate : skuGenerateList) {
                            //注册证与备案
                            GoodsAttachmentGenerateExample goodsAttachmentExample344680 = new GoodsAttachmentGenerateExample();
                            goodsAttachmentExample344680.createCriteria()
                                    .andAttachmentTypeIn(Lists.newArrayList(344, 680))
                                    .andGoodsIdEqualTo(genereate.getSkuId());
                            goodsAttachmentGenerateMapper.deleteByExample(goodsAttachmentExample344680);
                        }
                    }
                    //医疗器械分类时必填
                    Integer registerNumberId = firstEngage.getRegistration().getRegistrationNumberId();
                    if (registerNumberId != null) {
                        Map<String, Object> paramMap = new HashMap<>();
                        List<Integer> attachmentFunction = new ArrayList<>();
                        attachmentFunction.add(CommonConstants.ATTACHMENT_FUNCTION_975);
                        paramMap.put("attachmentFunction", attachmentFunction);

                        // 附件类型
                        paramMap.put("attachmentType", CommonConstants.ATTACHMENT_TYPE_974);
                        paramMap.put("registrationNumberId", registerNumberId);
                        List<Attachment> attrList = attachmentMapper.getAttachmentsList(paramMap);

                        StringBuilder sb = new StringBuilder();
                        if (CollectionUtils.isNotEmpty(attrList)) {
                            for (Attachment a : attrList) {
                                sb.append(CommonConstants.PIC_SPLIT + a.getUri());
                            }
                            //注册证类型
                            if (firstEngage.getRegistration().getManageCategoryLevel() > 968) {
                                savePics(344, skuGenerate.getSkuId(), sb.toString());
                            } else {
                                savePics(680, skuGenerate.getSkuId(), sb.toString());
                            }
                        }
                    }
                } catch (Exception e) {
                    logger.error("", e);
                }
            }

            goodsGenerate.setManufacturer(firstEngage.getProductCompanyChineseName());
            if (!StringUtils.isBlank(firstEngage.getRegistrationNumber())) {
                goodsGenerate.setRegistrationNumber(firstEngage.getRegistrationNumber());
                goodsGenerate.setRecordNumber(firstEngage.getRegistrationNumber());
            }
        }

        if (goodsGenerateMapper.selectByPrimaryKey(goodsGenerate.getGoodsId()) != null) {
            goodsGenerate.setModTime(skuGenerate.getModTime() == null ? 0 : skuGenerate.getModTime().getTime());
            goodsGenerate.setUpdater(user.getUserId());
            goodsGenerateMapper.updateByPrimaryKeySelective(goodsGenerate);
        } else {
            goodsGenerate.setModTime(skuGenerate.getModTime() == null ? 0 : skuGenerate.getModTime().getTime());
            goodsGenerate.setAddTime(skuGenerate.getAddTime() == null ? 0 : skuGenerate.getAddTime().getTime());
            goodsGenerate.setUpdater(user.getUserId());
            goodsGenerate.setCreator(user.getUserId());
            coreSpuGenerateExtendMapper.insertGoods(goodsGenerate);
        }
    }

    private void oldSkuToUnchk(Integer skuId, Integer checkStatus, String userName) {
        VerifiesInfoGenerateExample example = new VerifiesInfoGenerateExample();
        example.createCriteria().andRelateTableEqualTo("T_GOODS")
                .andRelateTableKeyEqualTo(skuId);
        VerifiesInfoGenerate verifiesInfoGenerate = new VerifiesInfoGenerate();
        verifiesInfoGenerate.setRelateTable("T_GOODS");
        verifiesInfoGenerate.setRelateTableKey(skuId);
        verifiesInfoGenerate.setVerifiesType(618);
        verifiesInfoGenerate.setModTime(System.currentTimeMillis());
        verifiesInfoGenerate.setLastVerifyUsername(userName);
        verifiesInfoGenerate.setVerifyUsername(userName);


        verifiesInfoGenerate.setStatus(GoodsCheckStatusEnum.transToOld(checkStatus));//审核中或者审核不通过
        verifiesInfoGenerateMapper.insertSelective(verifiesInfoGenerate);
    }

    private void oldSkuToUnchkUpdate(Integer skuId, Integer checkStatus, String userName) {
        VerifiesInfoGenerateExample example = new VerifiesInfoGenerateExample();
        example.createCriteria().andRelateTableEqualTo("T_GOODS")
                .andRelateTableKeyEqualTo(skuId);
        VerifiesInfoGenerate verifiesInfoGenerate = new VerifiesInfoGenerate();
        verifiesInfoGenerate.setRelateTable("T_GOODS");
        verifiesInfoGenerate.setRelateTableKey(skuId);
        verifiesInfoGenerate.setVerifiesType(618);
        verifiesInfoGenerate.setModTime(System.currentTimeMillis());
        verifiesInfoGenerate.setLastVerifyUsername(userName);
        verifiesInfoGenerate.setVerifyUsername(userName);
        verifiesInfoGenerate.setStatus(GoodsCheckStatusEnum.transToOld(checkStatus));//审核中或者审核不通过
        verifiesInfoGenerateMapper.updateByExampleSelective(verifiesInfoGenerate, example);
    }

    @Override
    public Integer countSpuByCheckStatus(Integer checkStatus) {
        try {
            return countCache.get(checkStatus);
        } catch (ExecutionException e) {
            return 0;
        }
    }

    private LoadingCache<Integer, Integer> countCache = CacheBuilder
            .newBuilder().maximumSize(5).expireAfterWrite(30, TimeUnit.SECONDS)
            .build(new CacheLoader<Integer, Integer>() {
                public Integer load(Integer checkStatus) throws Exception {
                    return loadCountByStatus(checkStatus);
                }
            });

    private Integer loadCountByStatus(Integer checkStatus) {
        CoreSpuGenerateExample example = new CoreSpuGenerateExample();
        example.createCriteria().andStatusEqualTo(CommonConstants.STATUS_1).andCheckStatusEqualTo(checkStatus);
        return coreSpuGenerateMapper.countByExample(example);
    }

    private Integer getLeftRange(CoreSkuGenerate sku) {
        String numArr[] = sku.getDeliveryRange().replaceAll("[A-Z]+", ",").split(",");
        return Integer.parseInt(numArr[1]);
    }

    /**
     * @param skuNo: .
     * @description: .
     * @jira: sku存在多个区间取最小区间.
     * @notes: .
     * @version: 1.0.
     * @date: 2020/5/22 11:08 上午.
     * @author: Tomcat.Hui.
     * @return: java.lang.String.
     * @throws: .
     */
    private String getDistinctRange(String skuNo) {
        List<CoreSkuGenerate> wtSku = coreSkuGenerateMapper.getWtSkuList(Arrays.asList(skuNo));
        if (null != wtSku && wtSku.size() > 0) {

            if (wtSku.size() > 1) {
                //去重
                CoreSkuGenerate min = wtSku.stream().filter(item -> item.getDeliveryRange() != null).min(Comparator.comparing(this::getLeftRange)).orElse(null);
                return null == min ? null : min.getDeliveryRange();
            } else {
                return wtSku.get(0).getDeliveryRange();
            }
        }
        return null;
    }

    @Override
    public CoreSkuGenerate initSku(SkuAddCommand command, boolean editable) throws Exception {
        if (command.getSkuId() == null || command.getSkuId() <= 0) {
            throw new ShowErrorMsgException("SkuId不存在！");
        }
        //编辑
        CoreSkuGenerate skuInfoDb = coreSkuGenerateMapper.selectByPrimaryKey(command.getSkuId());

        if (skuInfoDb == null) {
            throw new ShowErrorMsgException("SKU不存在！");
        }
        if (StrUtil.isNotBlank(skuInfoDb.getTaxCategoryNo())) {
            TaxcodeClassificationDto taxcodeClassificationDto = taxcodeClassificationApiService.findByCode(skuInfoDb.getTaxCategoryNo());
            if (Objects.nonNull(taxcodeClassificationDto)) {
                skuInfoDb.setTaxcodeClassificationDto(taxcodeClassificationDto);
                skuInfoDb.setTaxCodeSimpleName(taxcodeClassificationDto.getClassificationAbbreviation());
            }
        }

        // 将归属的orgIds分割成List格式，用于前端展示
        List<Integer> temp;
        temp = new ArrayList<>();
        if (StringUtils.isNotEmpty(skuInfoDb.getOrgIdList())){
            for (String orgId : skuInfoDb.getOrgIdList().split(",")) {
                temp.add(NumberUtils.toInt(orgId));
            }
            skuInfoDb.setOrgIdArray(temp);
        }

        // add by Tomcat.Hui 2020/5/19 1:32 下午 .Desc: VDERP-2217 提供预计发货时间给前台. start
        try {
            List<RangeDictionary> rangeDictionaryList = rangeDictionaryMapper.getAllDict();

            if (StringUtil.isBlank(skuInfoDb.getDeclareDeliveryRange())) {
                skuInfoDb.setDeliveryRange(getDistinctRange(skuInfoDb.getSkuNo()));
            } else {
                skuInfoDb.setDeliveryRange(skuInfoDb.getDeclareDeliveryRange());
            }

            RangeDictionary dictionary = rangeDictionaryList.stream()
                    .filter(d -> d.getName().equals(skuInfoDb.getDeliveryRange()))
                    .findFirst().orElse(null);

            skuInfoDb.setDeliveryRange(null == dictionary ? "" : dictionary.toString());
        } catch (Exception e) {
            logger.error("解析预计可发货时间出现异常", e);
        }

        // add by Tomcat.Hui 2020/5/19 1:32 下午 .Desc: VDERP-2217 提供预计发货时间给前台. end

        //获取注册证信息
        final CoreSpuGenerate coreSpuQuery = coreSpuGenerateMapper.selectByPrimaryKey(skuInfoDb.getSpuId());
        //判断设备类型商品的养护类型
        if (SysOptionConstant.ID_316.equals(coreSpuQuery.getSpuType())) {
            CuringTypeEnum curingTypeEnum = determineCuringType(coreSpuQuery.getFirstEngageId(), skuInfoDb.getEffectiveDayUnit(), skuInfoDb.getEffectiveDays());
            command.setCuringType(curingTypeEnum.getType());
            command.setCuringTypeDesc(curingTypeEnum.getMessage());
        }

        //技术参数
        final Map<String, String> goodsParameterValueMap;

        // 配置列表
        final Map<String, String> goodsConfigurationValueMap;
        //sku编辑页面内容
        if (editable) {
            //如果“参数名称”有重复，则按“技术参数-规则参数-性能参数”的顺序取值
            goodsParameterValueMap = GoodsParameterUtils.tokenizeParameterValues(skuInfoDb.getTechnicalParameter(),
                    skuInfoDb.getSpecParameter(), skuInfoDb.getPerformanceParameter());

            goodsConfigurationValueMap = GoodsParameterUtils.tokenizeParameterValues(skuInfoDb.getConfigurationList());

            // 产品检测报告
            List<FileInfo> goodsDetectReportAttachmentList = listGoodsAttachmentAsFileInfo(getOptionIdByOptionType(SysOptionConstant.SKU_CHECK_FILES), command.getSkuId());
            command.setSkuCheckFilesJson(JsonUtils.translateToJson(goodsDetectReportAttachmentList));

        } else {
            goodsParameterValueMap = GoodsParameterUtils.tokenizeParameterValues(skuInfoDb.getTechnicalParameter());
            goodsConfigurationValueMap = GoodsParameterUtils.tokenizeParameterValues(skuInfoDb.getConfigurationList());
            // 详情页面设置配置列表

            //设置商品签约模式
            GoodsSignContractModeEnum goodsSignContractModeToUse;
            GoodsPositionDo goodsPosition = goodsCommonService.getGoodsPosition(skuInfoDb.getGoodsPositionNo());
            if (goodsPosition != null && !CommonConstants.IS_DELETE_1.equals(goodsPosition.getIsDeleted())) {
                goodsSignContractModeToUse = GoodsSignContractModeEnum.getByCode(goodsPosition.getSignContractMode());
            } else {
                goodsSignContractModeToUse = GoodsSignContractModeEnum.NONE;
            }
            command.setGoodsSignContractModeStr(goodsSignContractModeToUse.getName());

            //设置商品等级和商品档位，用与页面显示
            command.setGoodsLevelVo(goodsCommonService.getGoodsLevelVo(skuInfoDb.getGoodsLevelNo()));
            command.setGoodsPositionVo(goodsCommonService.getGoodsPositionVo(skuInfoDb.getGoodsPositionNo()));

            // 产品检测报告
            List<FileInfo> goodsDetectReportAttachmentList = listGoodsAttachmentAsFileInfo(getOptionIdByOptionType(SysOptionConstant.SKU_CHECK_FILES), command.getSkuId());
            command.setSkuCheck(goodsDetectReportAttachmentList);
        }

        command.setParamsName1(goodsParameterValueMap.keySet().toArray(new String[0]));
        command.setParamsValue1(goodsParameterValueMap.values().toArray(new String[0]));

        command.setConfigurationName(goodsConfigurationValueMap.keySet().toArray(new String[0]));
        command.setConfigurationQuantity(goodsConfigurationValueMap.values().toArray(new String[0]));

        // 获取机构等级
        if(StringUtils.isNotEmpty(skuInfoDb.getInstitutionLevelIds())){
            List<Integer> ids = Arrays.stream(skuInfoDb.getInstitutionLevelIds().split(",")).map(Integer::parseInt).collect(Collectors.toList());
            List<SysOptionDefinition> sysOptionDefinitions = sysOptionDefinitionMapper.findBySysOptionDefinitionIdIn(ids);
            String institutionLevel = sysOptionDefinitions.stream().map(SysOptionDefinition::getTitle).collect(Collectors.joining("、"));
            skuInfoDb.setInstitutionLevel(institutionLevel);
        }
        return skuInfoDb;
    }

    private Integer getOptionIdByOptionType(String optionType) {
        SysOptionDefinition option = getFirstSysOptionDefinitionList(optionType);
        if (option != null) {
            return option.getSysOptionDefinitionId();
        }
        return -1;
    }

    @Override
    @Transactional
    public void deleteSpu(SpuSearchCommand spuCommand) throws ShowErrorMsgException {
        logger.info("deleteSpu::" + JSON.toJSONString(spuCommand));
        CoreSpuGenerate dbSpu = coreSpuGenerateMapper.selectByPrimaryKey(spuCommand.getSpuId());
        if (dbSpu == null || CommonConstants.STATUS_0.equals(dbSpu.getStatus()) || GoodsCheckStatusEnum.DELETE.getStatus() == dbSpu.getCheckStatus()) {
            throw new ShowErrorMsgException("spu不存在或者已经被删除。");
        }
        boolean hasAuth = false;
        if (SpuLevelEnum.isTempSpu(dbSpu.getSpuLevel()) && spuCommand.isHasEditTempAuth()) {
            hasAuth = true;
        }
        if (!SpuLevelEnum.isTempSpu(dbSpu.getSpuLevel()) && spuCommand.isHasEditAuth()) {
            hasAuth = true;
        }
        if (!hasAuth) {
            throw new ShowErrorMsgException("权限不足");
        }
        if (GoodsCheckStatusEnum.isPre(dbSpu.getCheckStatus())) {
            throw new ShowErrorMsgException("所选内容中包括审核中的SPU或者SKU商品，不可删除。");
        }
        //先删除SPU
        CoreSpuGenerate generate = new CoreSpuGenerate();
        generate.setSpuId(spuCommand.getSpuId());
        generate.setStatus(CommonConstants.STATUS_0);
        generate.setCheckStatus(GoodsCheckStatusEnum.DELETE.getStatus());
        generate.setModTime(new Date());
        generate.setUpdater(spuCommand.getUser().getUserId());
        generate.setDeleteReason(spuCommand.getDeleteReason());
        baseGoodsService.mergeSpu(generate);
        //再删除sku
        CoreSkuGenerateExample skuExample = new CoreSkuGenerateExample();
        skuExample.createCriteria().andSpuIdEqualTo(spuCommand.getSpuId()).andStatusEqualTo(CommonConstants.STATUS_1);
        List<CoreSkuGenerate> list = coreSkuGenerateMapper.selectByExample(skuExample);
        for (CoreSkuGenerate sku : list) {
            deleteSkuById(sku.getSkuId(), spuCommand.getDeleteReason(), spuCommand.getUser(), true);
        }
    }

    @Override
    @Transactional
    public void deleteSku(SpuSearchCommand spuCommand) throws ShowErrorMsgException {
        if (spuCommand.getSkuId() == null || spuCommand.getSpuId() == null) {
            throw new ShowErrorMsgException(CommonConstants.FAIL_CODE,
                    "skuid 和 spuid 都不能为空,skuId:" + spuCommand.getSkuId() + ",spuId:" + spuCommand.getSpuId());
        }

        boolean flag = goodsService.checkIsDisCardBySku("V" + spuCommand.getSkuId());
        if (flag) {
            throw new ShowErrorMsgException("此sku有库存不允许删除");
        }
        CoreSpuBaseDTO baseDto = baseGoodsService.selectSpuBaseById(spuCommand.getSpuId());
        if (baseDto == null) {
            throw new ShowErrorMsgException("spu不存在或者已经被删除。");
        }
        //判断权限
        boolean hasAuth = false;
        if (SpuLevelEnum.isTempSpu(baseDto.getSpuLevel()) && spuCommand.isHasEditTempAuth()) {
            hasAuth = true;
        }
        if (!SpuLevelEnum.isTempSpu(baseDto.getSpuLevel()) && spuCommand.isHasEditAuth()) {
            hasAuth = true;
        }
        if (!hasAuth) {
            throw new ShowErrorMsgException("权限不足");
        }
        deleteSkuById(spuCommand.getSkuId(), spuCommand.getDeleteReason(), spuCommand.getUser(), false);
    }

    private void deleteSkuById(Integer skuId, String reason, User user, boolean isfromspu) throws ShowErrorMsgException {
        logger.info("deleteSkuById::" + skuId + reason + user.getUsername());
        CoreSkuGenerate newSku = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if (newSku == null || CommonConstants.STATUS_0.equals(newSku.getStatus())) {
            if (isfromspu) {
                throw new ShowErrorMsgException(CommonConstants.FAIL_CODE, "spu中有sku不存在或者已经删除。" + skuId);
            } else {
                throw new ShowErrorMsgException(CommonConstants.FAIL_CODE, "sku不存在或者已经删除。" + skuId);
            }
        }
        if (GoodsCheckStatusEnum.isPre(newSku.getCheckStatus())) {
            throw new ShowErrorMsgException("所选内容中包括审核中SKU，不可删除。");
        }


        CoreSkuGenerate sku = new CoreSkuGenerate();
        sku.setSpuId(newSku.getSpuId());
        sku.setSkuId(skuId);
        sku.setStatus(CommonConstants.STATUS_0);
        sku.setModTime(new Date());
        sku.setUpdater(user.getUserId());
        sku.setDeleteReason(reason);

        baseGoodsService.mergeSku(sku);
        // 删除老数据
        GoodsGenerateExample oldExample = new GoodsGenerateExample();
        oldExample.createCriteria().andSkuEqualTo(newSku.getSkuNo());
        GoodsGenerateWithBLOBs old = new GoodsGenerateWithBLOBs();
        old.setIsDiscard(CommonConstants.STATUS_1);
        old.setDiscardReason(reason);
        old.setDiscardTime(System.currentTimeMillis());
        goodsGenerateMapper.updateByExampleSelective(old, oldExample);
    }

    @Override
    public List<CoreSkuBaseVO> selectSkuListPage(SpuSearchCommand spuCommand, Page page) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("page", new Page(1, 10));
        map.put("command", spuCommand);

        final List<GoodsLevelVo> goodsLevelVos = goodsCommonService.listAllGoodsLevel(false);
        final List<GoodsPositionVo> goodsPositionVos = goodsCommonService.listAllGoodsPosition();

        List<SpuSkuIdForListDTO> list = coreSpuGenerateExtendMapper.selectSpuListPage(map);
        if (CollectionUtils.isNotEmpty(list)) {
            List<CoreSkuBaseVO> result = Lists.newArrayList();
            for (SpuSkuIdForListDTO dto : list) {
                String[] skuIds = StringUtils.split(dto.getSkuIds(), ",");
                if (!ArrayUtils.isEmpty(skuIds)) {
                    page.setTotalRecord(skuIds.length);
                    String[] subIds = ArrayUtils.subarray(skuIds, page.getStartRecord(), page.getPageSize() * page.getPageNo());
                    if (!ArrayUtils.isEmpty(subIds)) {
                        CoreSpuBaseDTO spuBaseDTO = baseGoodsService.selectSpuBaseById(dto.getSpuId());
                        List<CoreSkuBaseDTO> skuList = baseGoodsService
                                .selectSkuBaseByIds(subIds);
                        if (CollectionUtils.isNotEmpty(skuList)) {

                            for (CoreSkuBaseDTO sku : skuList) {
                                CoreSkuBaseVO skuVo = new CoreSkuBaseVO();
                                BeanUtils.copyProperties(skuVo, sku);
                                skuVo.setHasTodoItemsCount(goodsCommonService.countTodoItems(sku.getSkuId()));
                                skuVo.setAssignmentAssistantId(spuBaseDTO.getAssignmentAssistantId());
                                skuVo.setAssignmentManagerId(spuBaseDTO.getAssignmentManagerId());

                                //设置商品的等级与档位
                                Optional<GoodsLevelVo> goodsLevelIfMatch = goodsLevelVos.stream().filter(level -> Objects.equals(sku.getGoodsLevelNo(), level.getId())).findFirst();
                                if (goodsLevelIfMatch.isPresent()) {
                                    GoodsLevelVo goodsLevelVo = goodsLevelIfMatch.get();
                                    skuVo.setGoodsLevelName(String.join(ErpConst.Symbol.HYPHEN, goodsLevelVo.getUniqueIdentifier(), goodsLevelVo.getLevelName()));
                                } else {
                                    skuVo.setGoodsLevelName("无");
                                }

                                Optional<GoodsPositionVo> goodsPositionIfMatch = goodsPositionVos.stream().filter(position -> Objects.equals(sku.getGoodsPositionNo(), position.getId())).findFirst();
                                if (goodsPositionIfMatch.isPresent()) {
                                    GoodsPositionVo goodsPositionVo = goodsPositionIfMatch.get();
                                    skuVo.setGoodsPositionName(goodsPositionVo.getPositionName());
                                } else {
                                    skuVo.setGoodsPositionName("无档位");
                                }
                                result.add(skuVo);
                            }
                        }
                    }
                }
                break;
            }

            /**
             * 处理前台的上架状态显示问题
             */
            if (CollectionUtils.isNotEmpty(result)) {
                result.forEach(coreSkuBaseVO -> {
                    coreSkuBaseVO.setOnSaleStr(getSaleStr(coreSkuBaseVO.getPushStatus(), coreSkuBaseVO.getOnSale()));
                });
            }
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public List<CoreSpuBaseVO> exportSpuList(String spuIds) throws Exception {
        String[] spuIdArray = StringUtils.split(spuIds, ",");
        if (!ArrayUtils.isEmpty(spuIdArray)) {
            List<CoreSpuBaseVO> result = Lists.newArrayList();
            for (String spuId : spuIdArray) {
                CoreSpuBaseDTO baseDto = baseGoodsService.selectSpuBaseById(NumberUtils.toInt(spuId));
                if (baseDto == null) {
                    continue;
                }
                CoreSpuBaseVO spuVo = new CoreSpuBaseVO();
                BeanUtils.copyProperties(spuVo, baseDto);
                result.add(spuVo);
                List<CoreSkuGenerate> skuList = selectSkuListBySpuId(NumberUtils.toInt(spuId));

                if (!CollectionUtils.isEmpty(skuList)) {
                    String[] skuArray = new String[skuList.size()];
                    for (int i = 0; i < skuList.size(); i++) {
                        skuArray[i] = String.valueOf(skuList.get(i).getSkuId());
                    }
                    List<CoreSkuBaseDTO> baseSkuList = baseGoodsService.selectSkuBaseByIds(skuArray);
                    if (CollectionUtils.isNotEmpty(baseSkuList)) {
                        for (CoreSkuBaseDTO sku : baseSkuList) {
                            CoreSkuBaseVO skuVo = new CoreSkuBaseVO();
                            BeanUtils.copyProperties(skuVo, sku);
                            spuVo.getCoreSkuBaseVOList().add(skuVo);
                        }
                    }
                }
            }
            return result;
        }
        return Collections.emptyList();
    }

    @Override
    public List<BaseAttributeVo> selectAllAttributeBySkuId(Integer spuId, Integer skuId) {
        CoreSpuBaseDTO coreSpuBaseDTO = baseGoodsService.selectSpuBaseById(spuId);
        if (coreSpuBaseDTO == null) {
            throw new ShowErrorMsgException("SPU不存在");
        }
        List<Integer> oldAttr = getSelectedValuesBySkuId(skuId);
        List<BaseAttributeVo> attrVos = coreSpuGenerateExtendMapper.selectAllAttributeBySpuId(spuId);
        if (CollectionUtils.isNotEmpty(attrVos)) {
            for (BaseAttributeVo vo : attrVos) {
                List<BaseAttributeValueVo> values = coreSpuGenerateExtendMapper.selectAllAttributeValueByAttrId(vo.getBaseAttributeId(), coreSpuBaseDTO.getCategoryId());
                if (CollectionUtils.isNotEmpty(values)) {
                    for (BaseAttributeValueVo valueVo : values) {
                        if (org.springframework.util.CollectionUtils.contains(oldAttr.iterator(), valueVo.getBaseAttributeValueId())) {
                            valueVo.setSelected(true);
                        }
                    }
                }
                vo.setAttrValue(values);
            }
        }
        return attrVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveSku(SkuAddCommand command, CoreSkuGenerate skuGenerate) throws UnsupportedEncodingException {
        //发票不允许有这些字符串
        if (skuGenerate == null) {
            return;
        }
        CoreSpuBaseDTO coreSpuDto = baseGoodsService.selectSpuBaseById(skuGenerate.getSpuId());
        if (coreSpuDto == null) {
            throw new ShowErrorMsgException("保存SKU信息时未查询到其关联的SPU");
        }

        //由于财务财务开票限制，需要校验部分字段长度
        checkInvoiceCondition(command, skuGenerate);

        //根据商品类型决定sku的类型
        final Integer skuTypeToUse = GoodsUtils.determineSkuTye(NumberUtils.toInt(coreSpuDto.getSpuType()));
        command.setSkuType(skuTypeToUse);

        //决定设备类型商品的养护类型
        if (GoodsTypeEnum.DEVICE.getId().toString().equals(coreSpuDto.getSpuType())
                && NumberUtils.toInt(skuGenerate.getEffectiveDays()) > 0) {
            skuGenerate.setCuringType(this.determineCuringType(coreSpuDto.getFirstEngageId(), skuGenerate.getEffectiveDayUnit(),
                    skuGenerate.getEffectiveDays()).getType());
        }
        //当"定期维护类型"不需要填写原因时，清空"定期维护原因"
        if (!RegularMaintainTypeEnum.hasRegularMaintainReason(skuGenerate.getRegularMaintainType())) {
            skuGenerate.setRegularMaintainReason(StringUtils.EMPTY);
        } else {
            skuGenerate.setCuringReason(skuGenerate.getRegularMaintainReason());
        }

        skuGenerate.setUpdater(command.getUser().getUserId());
        skuGenerate.setModTime(new Date());

        final boolean addFlag = command.getSkuId() == null;
        if (addFlag) {
            CoreSkuGenerate sku = addSku(command, skuGenerate);
            boolean nowIsE = Integer.valueOf(5).equals(command.getGoodsLevelNo());
            if (nowIsE) {
                generateCheckLogAndSave(sku.getSkuId(), sku.getCheckStatus(), "切换至E级，商品状态自动更新为审核不通过", LogTypeEnum.SKU.getLogType());
            }
            command.setSkuId(sku.getSkuId());
            skuUncheckSync(null, null, skuGenerate, command.getUser());
            oldSkuToUnchk(sku.getSkuId(), !nowIsE?0:2, command.getUser().getUsername());
        } else {
            checkSkuUpdateCondition(command, skuGenerate);
            boolean needAddLog = updateSku(command, skuGenerate);
            if (needAddLog) {
                generateCheckLogAndSave(skuGenerate.getSkuId(), skuGenerate.getCheckStatus(), "切换至E级，商品状态自动更新为审核不通过", LogTypeEnum.SKU.getLogType());
            }
            skuUncheckSync(skuGenerate.getSkuId(), null, skuGenerate, command.getUser());
            oldSkuToUnchkUpdate(skuGenerate.getSkuId(), !needAddLog?0:2, command.getUser().getUsername());
        }

        //delete firstly if any.
        if (!addFlag) {
            deleteSkuPics(command.getSkuId());
        }

        //保存产品检测报告
        saveGoodsDetectReportAttachment(command.getGoodsDetectReportAttachment(), command.getSkuId());

        //当sku属于"器械"类型时，反哺技术参数名至spu中
        if (GoodsConstants.SKU_TYPE_INSTRUMENT == skuTypeToUse) {
            CoreSpuGenerate spuToUpdate = new CoreSpuGenerate();
            spuToUpdate.setSpuId(coreSpuDto.getSpuId());
            spuToUpdate.setModTime(new Date());
            spuToUpdate.setTechnicalParameterNames(GoodsParameterUtils
                    .mergeParameterNames(coreSpuDto.getTechnicalParameterNames(), command.getParamsName1()));
            coreSpuGenerateMapper.updateByPrimaryKeySelective(spuToUpdate);
        }

        //合并商品属性
        mergeSkuAttr(command);

        riskCheckService.checkSkuAndtodo(command.getSkuId());

        //trigger listeners after saving the goods info successfully
        fireGoodsInfoChangeListener(command.getSkuId());
    }


    private void fireGoodsInfoChangeListener(Integer goodsId) {
        for (GoodsInfoChangeListener goodsInfoChangeListener : goodsInfoChangeListeners) {
            goodsInfoChangeListener.onGoodsInfoChange(goodsId);
        }
    }

    private void checkInvoiceCondition(SkuAddCommand command, CoreSkuGenerate skuGenerate) throws UnsupportedEncodingException {
        if (isMoreStringlength(skuGenerate.getSkuName(), 100)) {
            throw new ShowErrorMsgException("由于开票原因，商品名称不得超过限定长度。");
        }
        if (GoodsConstants.SKU_TYPE_INSTRUMENT == command.getSkuType()) {
            if (isMoreStringlength(skuGenerate.getModel(), "GBK", 40)) {
                throw new ShowErrorMsgException("由于开票原因, 型号不得超过限定长度。");
            }
        } else {
            if (isMoreStringlength(skuGenerate.getSpec(), "GBK", 40)) {
                throw new ShowErrorMsgException("由于开票原因，规格不得超过限定长度。");
            }
        }
        if (StringUtils.contains(skuGenerate.getSkuName(), "*") || StringUtils.contains(skuGenerate.getSkuName(), "<")
                || StringUtils.contains(skuGenerate.getSkuName(), ">") ||
                StringUtils.contains(skuGenerate.getSkuName(), "'")) {
            throw new ShowErrorMsgException("由于开票原因，商品名称不允许4个字符  * < > '");
        }

    }

    private boolean isMoreStringlength(String string,Integer maxLength) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(string)) {
            return false;
        }
        int length = string.length();
        if (length > maxLength) {
            return true;
        }
        return false;
    }

    private boolean isMoreStringlength(String string, String format, Integer maxLength) throws UnsupportedEncodingException {
        if (StringUtils.isBlank(string)) {
            return false;
        }
        int length = string.getBytes(format).length;
        if (length > maxLength) {
            return true;
        }
        return false;
    }

    @Override
    public List<BaseAttributeVo> getAttributeInfoByCategoryId(Integer categoryId) {
        return coreSpuGenerateExtendMapper.selectAllAttributeByCategoryId(categoryId);
    }

    @Override
    public List<LogCheckGenerate> listSpuCheckLog(Integer spuId) {
        LogCheckGenerateExample example = new LogCheckGenerateExample();
        example.createCriteria().andLogBizIdEqualTo(spuId).andLogTypeEqualTo(LogTypeEnum.SPU.getLogType());
        example.setOrderByClause(" ADD_TIME desc,LOG_ID desc ");
        return logCheckGenerateMapper.selectByExample(example);
    }

    @Override
    public List<LogCheckGenerate> listSkuCheckLog(Integer skuId) {
        LogCheckGenerateExample example = new LogCheckGenerateExample();
        example.createCriteria().andLogBizIdEqualTo(skuId).andLogTypeEqualTo(LogTypeEnum.SKU.getLogType())
                ;
        example.setOrderByClause(" ADD_TIME desc,LOG_ID desc ");

        return logCheckGenerateMapper.selectByExample(example);
    }

    @Override
    public void saveTempSku(SkuAddCommand skuCommand) {
        logger.info("saveTempSku::" + JSON.toJSONString(skuCommand));
        CoreSpuGenerate coreSpuBaseDTO = coreSpuGenerateMapper.selectByPrimaryKey(skuCommand.getSpuId());
        if (coreSpuBaseDTO == null) {
            throw new ShowErrorMsgException("SPU不存在");
        }
        if (!SpuLevelEnum.isTempSpu(coreSpuBaseDTO.getSpuLevel())) {
            throw new ShowErrorMsgException("SPU不为临时商品");
        }
        addTempSku(skuCommand.getUser(), skuCommand.getSkuInfo(), coreSpuBaseDTO.getSpuId(), skuCommand.getSkuId(), coreSpuBaseDTO.getSpuType()
                , coreSpuBaseDTO.getShowName(), coreSpuBaseDTO);

    }

    @Override
    public List<Map<String, Object>> searchFirstEngageListPage(String searchValue, Page page) {
        Map<String, Object> map = new HashMap<>();
        map.put("page", page);
        map.put("searchValue", searchValue);
        return coreSpuGenerateExtendMapper.searchFirstEngageListPage(map);
    }

    @Override
    @Transactional
    public void changeSpuStatusByCategoryChange(HttpServletRequest request, Integer categoryId, User user) {
        logger.info("changeSpuStatusByCategoryChange::" + categoryId + user.getUsername());
        Map<String, Object> map = new HashMap<String, Object>();
        Page page = Page.newBuilder(1, 10000, "");
        map.put("page", page);
        SpuSearchCommand spuCommand = new SpuSearchCommand();
        spuCommand.setCategoryId(categoryId);
        map.put("command", spuCommand);
        List<SpuSkuIdForListDTO> list = coreSpuGenerateExtendMapper.selectSpuListPage(map);
        if (page.getTotalRecord() > 10000) {//TODO
            throw new ShowErrorMsgException("当前分类下超过10000条商品审核状态会变成待审核,请线下处理");
        }
        for (SpuSkuIdForListDTO dto : list) {
            SpuAddCommand command = new SpuAddCommand();
            command.setSpuId(dto.getSpuId());
            command.setCheckStatus(GoodsCheckStatusEnum.PRE.getStatus());
            checkSpu(request, command);
        }
    }


    @Override
    public void checkSku(HttpServletRequest request, SkuAddCommand command) {
        logger.info("checkSku::" + JSON.toJSONString(command));
        // 临时文件存放地址
        String path = request.getSession().getServletContext().getRealPath("/upload/attachment");
        CoreSpuBaseDTO coreSpuBaseDTO = baseGoodsService.selectSpuBaseById(command.getSpuId());
        if (coreSpuBaseDTO == null) {
            throw new ShowErrorMsgException("SPU不存在");
        }
        if (GoodsCheckStatusEnum.APPROVE.getStatus() != coreSpuBaseDTO.getCheckStatus() && command.getCheckStatus() == GoodsCheckStatusEnum.APPROVE.getStatus()) {
            throw new ShowErrorMsgException("请先审核通过SPU后，才可以审核通过SKU");
        }
        checkSku(path, command.getSkuId(), command.getLastCheckReason(), command.getCheckStatus(), command.getUser());
        sendMsgIfSkuNameChangeAndCheckPassed(command.getSkuId(), command.getSpuId(), command.getCheckStatus());
    }

    private void sendMsgIfSkuNameChangeAndCheckPassed(Integer skuId, Integer spuId, Integer status) {
        if (!ErpConst.THREE.equals(status)) {
            return;
        }

        CoreSkuGenerate sku = coreSkuGenerateMapper.selectByPrimaryKey(skuId);
        if (!ErpConst.ONE.equals(sku.getIsNameChange()) || StringUtil.isBlank(sku.getHistoryName()) || sku.getShowName().equals(sku.getHistoryName())) {
            return;
        }
        CoreSkuGenerate update = new CoreSkuGenerate();
        update.setIsNameChange(ErpConst.ZERO);
        update.setSkuId(sku.getSkuId());
        coreSkuGenerateMapper.updateByPrimaryKeySelective(update);
        CoreSpuGenerate spu = coreSpuGenerateMapper.selectByPrimaryKey(spuId);
        Integer userId = spu.getAssignmentManagerId();
        String userName = userMapper.getUserNameByUserId(userId);
        String[] msgCreator = {userName, "" + userId};
        Map<String, String> param = new HashMap<>();
        param.put("showName", sku.getSkuNo() + sku.getShowName());
        Set<Integer> idSet = new HashSet<>();
        getUserIdByRoleName("医械购商品运营", idSet);
        getUserIdByRoleName("医械购运营管理员", idSet);
        getUserIdByRoleName("科研购运营管理员", idSet);
        getUserIdByRoleName("贝登商城运营", idSet);
        String url = String.format(ErpConst.SKU_VIEW_URL, skuId, spuId);
        MessageUtil.sendMessage(143, new ArrayList<>(idSet), param, url, msgCreator);
    }

    private void getUserIdByRoleName(String roleName, Set<Integer> idSet) {
        List<Integer> list = roleMapper.getUserIdByRoleName(roleName, 1);
        idSet.addAll(list);
    }

    private List<Integer> getSelectedValuesBySkuId(Integer skuId) {
        if (skuId == null) {
            return Collections.emptyList();
        }
        SkuAttrMappingGenerateExample example = new SkuAttrMappingGenerateExample();
        example.createCriteria().andSkuIdEqualTo(skuId).andStatusEqualTo(CommonConstants.STATUS_1);
        List<SkuAttrMappingGenerate> list = skuAttrMappingGenerateMapper.selectByExample(example);
        List<Integer> oldAttr = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            for (SkuAttrMappingGenerate generate : list) {
                if (generate.getBaseAttributeValueId() != null) {
                    oldAttr.add(generate.getBaseAttributeValueId());
                }

            }
        }
        return oldAttr;
    }


    private void mergeSkuAttr(SkuAddCommand command) {
        Integer[] attrValues = command.getBaseAttributeValueId();
        if (ArrayUtils.isNotEmpty(attrValues)) {
            List<Integer> oldAttr = getSelectedValuesBySkuId(command.getSkuId());
            List<Integer> newAttr = Lists.newArrayList(attrValues);
            List<Integer> addAttr = Lists.newArrayList(newAttr);
            addAttr.removeAll(oldAttr);

            List<Integer> deleteAttr = Lists.newArrayList(oldAttr);
            deleteAttr.removeAll(newAttr);

            if (CollectionUtils.isNotEmpty(addAttr)) {
                for (Integer attr : addAttr) {
                    if (attr == null) {
                        continue;
                    }
                    BaseAttributeValue value = baseAttributeValueMapper.selectByPrimaryKey(attr);
                    SkuAttrMappingGenerate generate = new SkuAttrMappingGenerate();
                    generate.setSkuId(command.getSkuId());
                    generate.setStatus(CommonConstants.STATUS_1);
                    generate.setAddTime(new Date());
                    generate.setModTime(new Date());
                    generate.setCreator(command.getUser().getUserId());
                    generate.setUpdater(command.getUser().getUserId());
                    generate.setBaseAttributeValueId(attr);
                    generate.setBaseAttributeId(value.getBaseAttributeId());
                    skuAttrMappingGenerateMapper.insert(generate);
                }
            }
            if (CollectionUtils.isNotEmpty(deleteAttr)) {
                List<Integer> deleteAttrIntegerList = Lists.newArrayList();
                for (Integer attr : deleteAttr) {
                    deleteAttrIntegerList.add(attr);
                }
                SkuAttrMappingGenerate generate = new SkuAttrMappingGenerate();
                generate.setStatus(CommonConstants.STATUS_0);
                generate.setModTime(new Date());
                generate.setUpdater(command.getUser().getUserId());
                SkuAttrMappingGenerateExample example1 = new SkuAttrMappingGenerateExample();
                example1.createCriteria().andBaseAttributeValueIdIn(deleteAttrIntegerList)
                        .andSkuIdEqualTo(command.getSkuId()).andStatusEqualTo(CommonConstants.STATUS_1);
                skuAttrMappingGenerateMapper.updateByExampleSelective(generate, example1);
            }
        }
    }


    private boolean updateSku(SkuAddCommand command, CoreSkuGenerate skuGenerate) {
        //first log goods info
        logGoodsOperateInfo(command, skuGenerate);
//        boolean needReject = false;
        boolean nowIsE = Integer.valueOf(5).equals(command.getGoodsLevelNo());
//            CoreSkuBaseDTO coreSkuBaseDTO = baseGoodsService.selectSkuBaseById(command.getSkuId());
//            boolean orldIsE = Integer.valueOf(5).equals(coreSkuBaseDTO.getGoodsLevelNo());


        if (nowIsE) {
            skuGenerate.setCheckStatus(GoodsCheckStatusEnum.REJECT.getStatus());
        } else {
            skuGenerate.setCheckStatus(GoodsCheckStatusEnum.WAIT_TO_PRE.getStatus());
        }

        skuGenerate.setTechnicalParameter(GoodsParameterUtils.paramArrayToString(command.getParamsName1(), command.getParamsValue1()));

        // 处理“配置清单”
        skuGenerate.setConfigurationList(GoodsParameterUtils.paramArrayToString(command.getConfigurationName(), command.getConfigurationQuantity()));

        skuGenerate.setSkuName(StringUtils.trim(skuGenerate.getSkuName()));
        skuGenerate.setShowName(skuGenerate.getSkuName());
        //对一些数字字段做兼容
        baseGoodsService.initHistoryName(skuGenerate);
        baseGoodsService.initSynchronizationStatus(skuGenerate);
        baseGoodsService.mergeSku(skuGenerate);
        return nowIsE;
    }

    private void checkSkuUpdateCondition(SkuAddCommand command, CoreSkuGenerate skuGenerate) {
        if (command.getSpuId() == null) {
            throw new ShowErrorMsgException("商品ID不能为空。");
        }
        CoreSkuGenerate generate = coreSkuGenerateMapper.selectByPrimaryKey(command.getSkuId());
        if (generate == null) {
            throw new ShowErrorMsgException("找不到对应的SKU。");
        }
        if ((StrUtil.isBlank(generate.getTaxCategoryNo()) && StrUtil.isNotBlank(skuGenerate.getTaxCategoryNo())) ||
                (StrUtil.isNotBlank(generate.getTaxCategoryNo()) && !generate.getTaxCategoryNo().equals(skuGenerate.getTaxCategoryNo()))) {
            skuGenerate.setTaxCategoryNoRecord(
                    StrUtil.format(ErpConst.TAX_CATEGORY_NO_RECORD_TEMPLATE,
                            CurrentUser.getCurrentUser().getUsername(), cn.hutool.core.date.DateUtil.formatDateTime(skuGenerate.getModTime()), generate.getTaxCategoryNo()));
        }
        skuGenerate.setSkuNo(generate.getSkuNo());
//        // 首营信息审核不通过，不能更新
//        FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(command.getFirstEngageId());
//        if (firstEngage == null) {
//            throw new ShowErrorMsgException("找不到对应的首营信息。");
//        }
//        if (!FirstEngageCheckStatusEnum.isApprove(firstEngage.getStatus())) {
//            throw new ShowErrorMsgException("当前首营信息审核中，请先对首营信息进行审核。");
//        }
        CoreSpuBaseDTO coreSpuDto = baseGoodsService.selectSpuBaseById(command.getSpuId());
        if (coreSpuDto == null) {
            throw new ShowErrorMsgException("找不到对应的SPU。");
        }
        //检查是否有权限
//        if (SpuLevelEnum.isTempSpu(coreSpuDto.getSpuLevel()) && !command.isHasEditTempAuth()) {
//            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "您没有此操作的权限！");
//        }
        if (!command.isHasEditAuth()) {
            throw new ShowErrorMsgException(CommonConstants.RESULTINFO_CODE_FAIL_1 + "", "您没有此操作的权限！");
        }

        // 状态为审核中，不能更新
        if (GoodsCheckStatusEnum.isPre(generate.getCheckStatus())) {
            throw new ShowErrorMsgException("SKU状态为审核中不能修改。");
        }
    }

    private CoreSkuGenerate addSku(SkuAddCommand command, CoreSkuGenerate skuGenerate) {
        //first log goods info
        logGoodsOperateInfo(command, skuGenerate);

        //校验SKU名称是否已经存在
        if (checkSkuNameIfDuplicate(skuGenerate.getSkuName())) {
            throw new ShowErrorMsgException(skuGenerate.getSkuName() + "已经存在，请勿重复提交。");
        }

        boolean nowIsE = Integer.valueOf(5).equals(command.getGoodsLevelNo());

        if (nowIsE) {
            skuGenerate.setCheckStatus(GoodsCheckStatusEnum.REJECT.getStatus());
        } else {
            skuGenerate.setCheckStatus(GoodsCheckStatusEnum.WAIT_TO_PRE.getStatus());
        }

        skuGenerate.setAddTime(new Date());
        skuGenerate.setCreator(command.getUser().getUserId());
        skuGenerate.setTechnicalParameter(GoodsParameterUtils.paramArrayToString(command.getParamsName1(), command.getParamsValue1()));
        // 处理“configurationList”
        skuGenerate.setConfigurationList(GoodsParameterUtils.paramArrayToString(command.getConfigurationName(), command.getConfigurationQuantity()));
        skuGenerate.setSkuName(StringUtils.trim(skuGenerate.getSkuName()));
        skuGenerate.setShowName(StringUtils.trim(skuGenerate.getSkuName()));
        insertWithSkuNo(skuGenerate);
        return skuGenerate;
    }


    private void logGoodsOperateInfo(SkuAddCommand command, CoreSkuGenerate skuGenerate) {
        boolean addFlag = command.getSkuId() == null;
        logger.info(addFlag ? "addSku" : "updateSku" + "::command:{}, skuInfo:{}", JSON.toJSONString(command, SerializerFeature.SkipTransientField),
                JSON.toJSONString(skuGenerate));
    }

    private boolean checkSkuNameIfDuplicate(String skuName) {
        if (StringUtils.isEmpty(skuName)) {
            throw new IllegalArgumentException("skuName is required.");
        }
        CoreSkuGenerateExample skuExample = new CoreSkuGenerateExample();
        skuExample.createCriteria().andStatusEqualTo(CommonConstants.STATUS_1)
                .andShowNameEqualTo(skuName.trim());
        return coreSkuGenerateMapper.countByExample(skuExample) > 0;
    }

    private List<CoreSkuGenerate> selectSkuListBySpuId(Integer spuId) {
        if (spuId == null) {
            return Collections.emptyList();
        }
        CoreSkuGenerateExample skuExample = new CoreSkuGenerateExample();
        skuExample.createCriteria().andSpuIdEqualTo(spuId).andStatusEqualTo(CommonConstants.STATUS_1);
        List<CoreSkuGenerate> skuList = coreSkuGenerateMapper.selectByExample(skuExample);
        return skuList;
    }

    private void deleteSkuPics(Integer skuId) {
        if (skuId == null) {
            return;
        }
        // 先删除产品检测报告// 先删除产品专利文件
        GoodsAttachmentGenerateExample goodsAttachmentExample658 = new GoodsAttachmentGenerateExample();
        goodsAttachmentExample658.createCriteria()
                .andAttachmentTypeIn(Lists.newArrayList(getOptionIdByOptionType(SysOptionConstant.SKU_CHECK_FILES),
                        getOptionIdByOptionType(SysOptionConstant.SKU_PATENT_FILES),
                        getOptionIdByOptionType(SysOptionConstant.SKU_CORE_PART_PRICE_FILE))
                )
                .andGoodsIdEqualTo(skuId)
                .andStatusEqualTo(CommonConstants.STATUS_1);
        GoodsAttachmentGenerate generate = new GoodsAttachmentGenerate();
        generate.setStatus(CommonConstants.STATUS_0);
        goodsAttachmentGenerateMapper.updateByExampleSelective(generate, goodsAttachmentExample658);
        //老数据兼容
        GoodsAttachmentGenerateExample goodsAttachmentExample658659 = new GoodsAttachmentGenerateExample();
        goodsAttachmentExample658659.createCriteria()
                .andAttachmentTypeIn(Lists.newArrayList(658, 659)
                )
                .andGoodsIdEqualTo(skuId);
        goodsAttachmentGenerateMapper.deleteByExample(goodsAttachmentExample658659);
    }

    private void deleteSpuPics(SpuAddCommand spuCommand) {
        if (spuCommand.getSpuId() == null) {
            return;
        }
        // 先删除产品检测报告// 先删除产品专利文件
        GoodsAttachmentGenerateExample goodsAttachmentExample658 = new GoodsAttachmentGenerateExample();
        goodsAttachmentExample658.createCriteria()
                .andAttachmentTypeIn(Lists.newArrayList(getOptionIdByOptionType(SysOptionConstant.SPU_CHECK_FILES),
                        getOptionIdByOptionType(SysOptionConstant.SPU_PATENT_FILES)))
                .andGoodsIdEqualTo(spuCommand.getSpuId()).andStatusEqualTo(CommonConstants.STATUS_1);
        GoodsAttachmentGenerate generate = new GoodsAttachmentGenerate();
        generate.setStatus(CommonConstants.STATUS_0);
        goodsAttachmentGenerateMapper.updateByExampleSelective(generate, goodsAttachmentExample658);

    }


    private void saveGoodsDetectReportAttachment(List<SkuAddCommand.GoodsDetectReportAttachment> goodsDetectReportAttachmentList, Integer goodsId) {
        if (CollectionUtils.isEmpty(goodsDetectReportAttachmentList) || goodsId == null) {
            return;
        }

        // 产品检测报告 和 产品检测报告（老商品）
        List<Integer> goodsAttachmentTypeList = Arrays.asList(getOptionIdByOptionType(SysOptionConstant.SKU_CHECK_FILES), SysOptionConstant.ID_658);
        for (Integer goodsAttachmentType : goodsAttachmentTypeList) {
            if (goodsAttachmentType == null) {
                continue;
            }

            for (int i = 0; i < goodsDetectReportAttachmentList.size(); i++) {
                SkuAddCommand.GoodsDetectReportAttachment goodsDetectReportAttachment = goodsDetectReportAttachmentList.get(i);
                GoodsAttachmentGenerate goodsAttachment = new GoodsAttachmentGenerate();
                goodsAttachment.setAttachmentType(goodsAttachmentType);
                goodsAttachment.setUri(goodsDetectReportAttachment.getRelativePath());
                goodsAttachment.setDisplayName(goodsDetectReportAttachment.getDisplayName());
                goodsAttachment.setStatus(CommonConstants.STATUS_1);
                goodsAttachment.setDomain(domain);
                goodsAttachment.setSort(i);
                goodsAttachment.setGoodsId(goodsId);
                goodsAttachmentGenerateMapper.insert(goodsAttachment);
            }
        }
    }


    /**
     * @param spuCommand
     * @deprecated ERP_LV_2020_98 SPU不再维护专利文件/检测报告
     */
    @Deprecated
    private void saveSpuPics(SpuAddCommand spuCommand) {
        // 产品检测报告
        savePics(getOptionIdByOptionType(SysOptionConstant.SPU_CHECK_FILES), spuCommand.getSpuId(), spuCommand.getSpuCheckFiles());
        // 产品专利文件
        savePics(getOptionIdByOptionType(SysOptionConstant.SPU_PATENT_FILES), spuCommand.getSpuId(), spuCommand.getSpuPatentFiles());
    }

    private void savePics(Integer type, Integer id, String urls) {
        String[] urlArray = StringUtils.split(urls, CommonConstants.PIC_SPLIT);
        if (ArrayUtils.isNotEmpty(urlArray)) {
            for (int i = 0; i < urlArray.length; i++) {
                GoodsAttachmentGenerate generate = new GoodsAttachmentGenerate();
                generate.setAttachmentType(type);
                generate.setUri(urlArray[i]);
                generate.setStatus(CommonConstants.STATUS_1);
                generate.setDomain(domain);
                generate.setSort(i);
                generate.setGoodsId(id);
                goodsAttachmentGenerateMapper.insert(generate);
            }
        }
    }


    @Override
    public List<Map<String, Object>> searchSkuWithDepartment(String skuName) {
        List<Map<String, String>> dbData = coreSpuGenerateExtendMapper.searchSkuWithDepartment(skuName);
        List<Map<String, Object>> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dbData)) {
            for (Map<String, String> map : dbData) {
                Map<String, Object> row = new HashMap<>();
                String skuId = String.valueOf(map.get("SKU_ID"));
                String skuNameN = map.get("SKU_NAME");
                String deptIds = map.get("DEPARTMENT_IDS");
                String deptNames = map.get("DEPARTMENT_NAMES");
                String skuUnitName = map.get("SKU_NUIT_NAME");
                String purpose = map.get("PURPOSE");
                String status = String.valueOf(map.get("STATUS"));
                String checkStatus = String.valueOf(map.get("CHECK_STATUS"));
                //组装
                String deptArray[] = StringUtils.split(deptIds, ",");
                String deptNameArray[] = StringUtils.split(deptNames, ",");
                List<Map<String, String>> deptList = Lists.newArrayList();
                if (ArrayUtils.isNotEmpty(deptArray)) {
                    for (int i = 0; i < Math.min(deptArray.length, deptNameArray.length); i++) {
                        if (StringUtils.isNotBlank(deptArray[i]) && StringUtils.isNotBlank(deptNameArray[i])) {
                            Map<String, String> dept = Maps.newHashMap();
                            dept.put("departmentId", deptArray[i]);
                            dept.put("departmentName", deptNameArray[i]);
                            deptList.add(dept);
                        }
                    }
                }
                row.put("skuId", skuId);
                row.put("skuName", skuNameN);
                row.put("departments", deptList);
                row.put("skuUnitName", skuUnitName);
                row.put("purpose", purpose);
                row.put("status", status);
                row.put("checkStatus", checkStatus);
                result.add(row);
            }
            return result;
        } else {
            return Collections.emptyList();
        }

    }

    LoadingCache<Integer, Map<String, Object>> goodsCache = CacheBuilder
            .newBuilder().maximumSize(10000).expireAfterWrite(5, TimeUnit.MINUTES)
            .build(new CacheLoader<Integer, Map<String, Object>>() {
                public Map<String, Object> load(Integer skuId) throws Exception {
                    return skuTipDb(skuId);
                }
            });

    public Map<String, Object> skuTipDb(Integer skuId) {
        Map<String, Object> goodsInfo = goodsMapper.getGoodsInfoTips(skuId);

        if (goodsInfo == null) {
            return Maps.newHashMap();
        }

        String manager = goodsInfo.get("USERNAME") == null ? "" : goodsInfo.get("USERNAME").toString();
        String assi = goodsInfo.get("ASSIS") == null ? "" : goodsInfo.get("ASSIS").toString();
        Set<String> set = Sets.newHashSet();
        set.add(manager);
        set.add(assi);
        goodsInfo.put("PRODUCTMANAGER", StringUtils.join(set, "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"));
        goodsInfo.put("PRODUCTMANAGER_NO_SPACE", StringUtils.join(set, "<br>"));
        String sku = "V" + skuId;
        Map<String, WarehouseStock> stockInfo = warehouseStockService.getStockInfo(Lists.newArrayList(sku));
        if (stockInfo != null && stockInfo.containsKey(sku)) {
            WarehouseStock stock = stockInfo.get(sku);
            goodsInfo.put("STOCKNUM", stock.getStockNum());
            goodsInfo.put("AVAILABLESTOCKNUM", stock.getAvailableStockNum());
            goodsInfo.put("OCCUPYNUM", stock.getOccupyNum());
        } else {
            goodsInfo.put("STOCKNUM", 0);
            goodsInfo.put("AVAILABLESTOCKNUM", 0);
            goodsInfo.put("OCCUPYNUM", 0);
        }
        return goodsInfo;
    }

    @Override
    public Map<String, Object> skuTip(Integer skuId) {
        try {
            return goodsCache.get(skuId);
        } catch (Exception e) {
            logger.warn("skuTip-skuId:"+skuId, e);
            return Collections.emptyMap();
        }
    }

    @Override
    public List<Map<String, Object>> skuTipList(List<Integer> skuIds) {

        if (CollectionUtils.isEmpty(skuIds)) {
            return Lists.newArrayList();
        }

        List<Map<String, Object>> goodsInfoList = goodsMapper.skuTipList(skuIds);
        goodsInfoList.stream().forEach(goodsInfo -> {
            try {
                Set<String> set = Sets.newHashSet();
                set.add(goodsInfo.get("USERNAME") == null ? "" : goodsInfo.get("USERNAME").toString());
                set.add(goodsInfo.get("ASSIS") == null ? "" : goodsInfo.get("ASSIS").toString());
                goodsInfo.put("PRODUCTMANAGER", StringUtils.join(set, "<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;" +
                        "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"));

            } catch (Exception e) {
                logger.error("" + JSON.toJSONString(goodsInfo), e);
            }
        });

        return goodsInfoList;
    }

    @Override
    public List<CoreSpuGenerate> findSpuNamesBySpuIds(List<Integer> goodsIds) {
        return goodsMapper.findSpuNamesBySpuIds(goodsIds);

    }

    @Override
    public CoreSpuGenerate findSpuInfoBySkuNo(String skuNo) {
        return goodsMapper.findSpuInfoBySkuNo(skuNo);
    }


    @Autowired
    SaleorderGoodsGenerateMapper saleorderGoodsGenerateMapper;
    @Autowired
    SaleorderGoodsMapper saleorderGoodsMapper;

    @Override
    public Map<String, Object> getCostPrice(Integer orderDetailId) {
        Map<String, Object> result = saleorderGoodsMapper.selectOrderGoodsCostPrice(orderDetailId);
        if (result == null) {
            logger.info("无成本价：" + orderDetailId);
            return new HashMap<>();
        }
        String ids = result.get("BUYORDERIDS") == null ? "" : result.get("BUYORDERIDS") + "";
        String nos = result.get("BUYORDERNOS") == null ? "" : result.get("BUYORDERNOS") + "";
        if (StringUtils.isNotBlank(ids)) {
            String idarray[] = StringUtils.split(ids, ",");
            String noarray[] = StringUtils.split(nos, ",");
            String url = "<a class='loadMoreAddtitle' href='javascript:void(0);' tabTitle='{\"num\":\"viewfinancebuyorder##id##\", \"link\":\"./finance/buyorder/viewBuyorder.do?buyorderId=##id##\",\"title\":\"订单信息\"}'>##no##</a>";
            Set<String> links = Sets.newHashSet();
            for (int i = 0; i < idarray.length; i++) {
                String linksTemp = url.replaceAll("##id##", idarray[i]);
                linksTemp = linksTemp.replaceAll("##no##", noarray[i]);
                links.add(linksTemp);
            }
            result.put("BUYNOLINK", StringUtils.join(links, "<br>"));
        } else {
            result.put("BUYNOLINK", "");
        }
        result.put("orderDetailId", orderDetailId);
        return result;
    }

    @Override
    public List<CoreSkuGenerate> getSkuListByNo(List<String> skuNoList) {
        return coreSkuGenerateMapper.getSkuListByNo(skuNoList);
    }

    @Override
    @Transactional
    public Integer updateSkuDeliveryRange(List<List<CoreSkuGenerate>> skuList) {
        Integer result = 0;
        for (int i = 0; i < skuList.size(); i++) {
            List<CoreSkuGenerate> updateList = skuList.get(i);
            for (CoreSkuGenerate a : updateList) {
                if (StrUtil.isNotEmpty(a.getPurchaseTime())) {
                    logger.info("更新采购时间数据：{}",JSON.toJSONString(a));
                    result += coreSkuGenerateMapper.updateSkuPurchaseTime(Collections.singletonList(a));
                    coreSkuSearchGenerateMapper.updateSkuSearchPurchaseTime(Collections.singletonList(a));
                }
                if (Objects.nonNull(a.getIsDirect())) {
                    logger.info("更新发货方式数据：{}",JSON.toJSONString(a));
                    result += coreSkuGenerateMapper.updateSkuIsDirect(a);
                }
            }

        }
        return result;
    }


    @Override
    public Integer updateSkuOnSale(SkuOnSaleDto dto) {
        if (dto == null || dto.getIsOnsale() == null || dto.getPlatformId() == null || StringUtil.isBlank(dto.getSkuNo())) {
            logger.info("更新sku上下架状态，必要参数为空");
            return ErpConst.ZERO;
        }
        logger.info("正在更新sku上架状态:{},platformId:{}，onSale:{}", dto.getSkuNo(), dto.getPlatformId(), dto.getIsOnsale());
        coreSkuGenerateMapper.updateSkuOnSale(dto.getSkuNo(), getSkuOnSaleStatus(dto));
        return ErpConst.ONE;
    }

    private Integer getSkuOnSaleStatus(SkuOnSaleDto dto) {
        Integer onSale = coreSkuGenerateMapper.getSkuOnSale(dto.getSkuNo());

        if (onSale == null) {
            return ErpConst.ZERO;
        }
        if (GoodsConstants.ON_SALE_YES.equals(dto.getIsOnsale())) {
            return dto.getPlatformId() | onSale;
        } else if (GoodsConstants.ON_SALE_NO.equals(dto.getIsOnsale())) {
            int middle = GoodsConstants.ON_SALE_MAX - dto.getPlatformId();
            return middle & onSale;
        }
        return ErpConst.ZERO;
    }

    @Override
    public Integer updateSkuPushStatus(SkuOnSaleDto dto) {
        if (dto == null || dto.getPlatformId() == null || StringUtil.isBlank(dto.getSkuNo())) {
            logger.info("更新sku上下架状态，必要参数为空");
            return ErpConst.ZERO;
        }
        Integer pushStatus = coreSkuGenerateMapper.getPushStatusBySkuNo(dto.getSkuNo());
        if (pushStatus == null) {
            pushStatus = 0;
        }
        int status = dto.getPlatformId() | pushStatus;
        coreSkuGenerateMapper.updatePushStatusBySkuNo(dto.getSkuNo(), status);
        return ErpConst.ONE;
    }

    @Override
    public List<Map<String, Object>> skuTipListInfo(List<Integer> quoteorderGoodsIds) {
        if (CollectionUtils.isEmpty(quoteorderGoodsIds)) {
            return Lists.newArrayList();
        }
        return goodsMapper.skuTipList(quoteorderGoodsIds);
    }

    @Override
    public CoreSkuGenerate getSkuAuthotizationInfoBySku(Long skuId) {
        if (skuId == null) {
            return null;
        }
        return goodsMapper.getSkuAuthotizationInfoBySku(skuId);
    }

    @Override
    public Integer getSpuIdBySkuId(Integer skuId) {
        return goodsMapper.getSpuIdBySkuId(skuId);
    }

    @Override
    public Integer getSkuIdBySkuNo(String skuNo) {
        return goodsMapper.getSkuIdBySkuNo(skuNo);
    }


    @Override
    public Integer countSpuByFirstEngageId(Integer firstEngageId) {
        if (firstEngageId == null || firstEngageId < 0) {
            return -1;
        }

        CoreSpuGenerateExample example = new CoreSpuGenerateExample();
        example.createCriteria().andFirstEngageIdEqualTo(firstEngageId)
                .andStatusEqualTo(1);
        return coreSpuGenerateMapper.countByExample(example);
    }


    @Override
    public Integer batchSaveSkuAuthorization(List<Integer> skuIds, CoreSkuGenerate coreSkuGenerate) {
        if (CollectionUtils.isEmpty(skuIds) || coreSkuGenerate == null) {
            return 0;
        }
        return coreSkuGenerateMapper.batchSaveSkuAuthorization(skuIds, coreSkuGenerate);
    }

    @Transactional(readOnly = true, rollbackFor = Exception.class)
    @Override
    public List<Integer> listSkuNameForSaleContract(Collection<Integer> skuIdList) {
        if (skuIdList == null || skuIdList.isEmpty()) {
            return Collections.emptyList();
        }

        final CoreSkuGenerateExample skuExample = new CoreSkuGenerateExample();
        skuExample.createCriteria().andSkuIdIn(new ArrayList<>(skuIdList));
        final List<CoreSkuGenerate> skuList = coreSkuGenerateMapper.selectByExample(skuExample);

        List<Integer> returnList = new LinkedList<>();

        for (CoreSkuGenerate skuToUse : skuList) {
            CoreSpuGenerate spuToUse = coreSpuGenerateMapper.selectByPrimaryKey(skuToUse.getSpuId());
            if (spuToUse != null) {
                boolean hasRegistrationCert = false;
                if (spuToUse.getFirstEngageId() != null && spuToUse.getFirstEngageId() > 0) {
                    hasRegistrationCert = firstEngageService.associateWithRegistrationCert(spuToUse.getFirstEngageId());
                }

                if (!hasRegistrationCert && CommonConstants.ON.equals(spuToUse.getMedicalInstrumentCatalogIncluded())) {
                    //是否在《医疗器械分类目录》”为“是”且“是否有注册证/备案凭证”为“无
                    returnList.add(skuToUse.getSkuId());
                }
            }
        }
        return returnList;
    }

    @Override
    public void generateCheckLogAndSave(Integer logBizId, Integer checkStatus, String logMessage, Integer logType) {
        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        User user = (User) servletRequestAttributes.getRequest().getSession().getAttribute(Consts.SESSION_USER);
        LogCheckGenerate logCheckGenerate = new LogCheckGenerate();
        logCheckGenerate.setAddTime(new Date());
        logCheckGenerate.setCreator(user.getUserId());
        logCheckGenerate.setLogBizId(logBizId);
        logCheckGenerate.setCreatorName(user.getUsername());
        logCheckGenerate.setLogType(logType);

        if (checkStatus == GoodsCheckStatusEnum.PRE.getStatus()) {
            //判断是否是首次审核
            int isLogCheckFirst = logCheckGenerateMapper.selectCountByBuzIdAndType(logBizId, logType);
            if (isLogCheckFirst == 0) {
                logCheckGenerate.setLogStatusName("开始");
                logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.PRE.getStatus());
                logCheckGenerateMapper.insertSelective(logCheckGenerate);
                logCheckGenerate.setLogStatusName("申请审核");
                logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.PRE.getStatus());
                logCheckGenerateMapper.insertSelective(logCheckGenerate);
            } else {
                logCheckGenerate.setLogStatusName("修改");
                logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.PRE.getStatus());
                logCheckGenerate.setLogMessage("");
                logCheckGenerateMapper.insertSelective(logCheckGenerate);
                logCheckGenerate.setLogStatusName("申请审核");
                logCheckGenerateMapper.insertSelective(logCheckGenerate);
            }
        } else if (checkStatus == GoodsCheckStatusEnum.APPROVE.getStatus()) {
            logCheckGenerate.setLogStatusName("质量官审核");
            logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.PRE.getStatus());
            logCheckGenerateMapper.insertSelective(logCheckGenerate);
            logCheckGenerate.setLogStatusName("审核通过");
            logCheckGenerate.setLogMessage(logMessage);
            logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.APPROVE.getStatus());
            logCheckGenerateMapper.insertSelective(logCheckGenerate);
        } else if (checkStatus == GoodsCheckStatusEnum.REJECT.getStatus()) {
            logCheckGenerate.setLogStatusName("质量官审核");
            logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.PRE.getStatus());
            logCheckGenerateMapper.insertSelective(logCheckGenerate);
            logCheckGenerate.setLogStatusName("驳回");
            logCheckGenerate.setLogMessage(logMessage);
            logCheckGenerate.setLogMessage(logMessage);
            logCheckGenerate.setLogStatus(GoodsCheckStatusEnum.REJECT.getStatus());
            logCheckGenerateMapper.insertSelective(logCheckGenerate);
        }
    }

    @Override
    public CoreSkuGenerate getPushInfoBySkuId(Integer skuId) {
        if (skuId == null || skuId <= 0) {
            return null;
        }
        return coreSkuGenerateMapper.getPushInfoBySkuId(skuId);
    }

    @Override
    public List<SkuAddCommand> getValidedSkuInfoBySpuId(Integer spuId) {
        if (spuId == null || spuId <= 0) {
            return null;
        }
        return coreSkuGenerateMapper.getValidedSkuInfoBySpuId(spuId);
    }

    @Override
    public List<User> getAssignmentersBySkuId(Integer skuId) {
        if (skuId == null || skuId == 0) {
            return null;
        }
        CoreSpu spuInfo = coreSpuMapper.getSpuBySku("V" + skuId);
        if (spuInfo == null) {
            return null;
        }
        ArrayList<Integer> userIds = new ArrayList<>();
        ArrayList<User> users = new ArrayList<>();
        if (spuInfo.getAssignmentAssistantId() != null) {
            User userByUserId = userMapper.getUserByUserId(spuInfo.getAssignmentAssistantId());
            if (userByUserId != null) {
                users.add(userByUserId);
                userIds.add(userByUserId.getUserId());
            }
        }
        if (spuInfo.getAssignmentManagerId() != null && !userIds.contains(spuInfo.getAssignmentManagerId())) {
            User userByUserId = userMapper.getUserByUserId(spuInfo.getAssignmentManagerId());
            if (userByUserId != null) {
                users.add(userByUserId);
            }
        }
        if (CollectionUtils.isEmpty(users)) {
            return null;
        }
        return users;
    }

    @Override
    public boolean isPushedSpuBySpuId(Integer spuId) {
        if (spuId == null || spuId < 1) {
            return false;
        }
        Integer pushStatusCount = coreSpuMapper.getPushStatusCountBySpuId(spuId);
        return pushStatusCount != null && pushStatusCount > 0;
    }


    /**
     * 判断设备类型商品的养护类型。
     * <p>
     * 规则:
     * SKU无注册证/备案凭证	不养护	备注：代码中值的命名与原养护类型的3个值一一对应。
     * SKU有注册证/备案凭证且SKU的产品有效期≤1年	重点养护
     * SKU有注册证/备案凭证且SKU无产品有效期（“是否启用效期管理”为“否”），或SKU有注册证/备案凭证且SKU的产品有效期＞1年	一般养护
     *
     * @param firstEngageId
     * @param effectiveUnit
     * @param effectiveTime
     * @return
     */
    private CuringTypeEnum determineCuringType(Integer firstEngageId, Integer effectiveUnit, String effectiveTime) {
        if (firstEngageId != null && firstEngageId > 0) {
            if (firstEngageService.associateWithRegistrationCert(firstEngageId)) {
                int timeToCompare = NumberUtils.toInt(effectiveTime, 0);
                if (timeToCompare > 0) {
                    if (effectiveUnit == null) effectiveUnit = 1;
                    //根据时间单位产品有效期单位 1 天   2 月  3 年，转换为天数
                    final int base = effectiveUnit == 3 ? DateUtil.DAYS_OF_YEARS : (effectiveUnit == 2 ? 30 : 1);
                    if (timeToCompare * base <= DateUtil.DAYS_OF_YEARS) {
                        return CuringTypeEnum.MAJOR;
                    }
                }

                return CuringTypeEnum.GENERAL;
            }
        }

        return CuringTypeEnum.NONE;
    }


    @Override
    public List<BaseAttributeVo> selectAllAttributeBySpuId(Integer spuId) {
        return coreSpuGenerateExtendMapper.selectAllAttributeBySpuId(spuId);
    }

    @Override
    public CoreSkuGenerate selectSkuConfigBySkuId(Integer skuId) {
        return coreSkuGenerateMapper.selectByPrimaryKey(skuId);
    }

    @Override
    public DocOfGoodsDo getDocOfGoodsBySkuId(Integer skuId) {
        return coreSkuGenerateMapper.selectDocOfGoodsBySkuId(skuId);
    }

    @Override
    public void enableSkuOrSpu(Integer type, Integer id, Integer status, String reason) throws ParseException {
        // SKU
        if (GoodsValidConstants.SKU_TYPE.equals(type)) {
            // 根据skuId查询审核记录
            CoreGoodsDisabledStatusHistoryDTO coreSkuDisabledStatusHistoryDTO = coreGoodsDisabledStatusHistoryMapper.getCreatorAndTime("V" + id, 2);
            CoreSku coreSku = coreSkuMapper.selectByPrimaryKey(id);
            coreSku.setStatus(status);
            coreSku.setDisabledReason(reason);
            coreSku.setUpdater(coreSkuDisabledStatusHistoryDTO.getCreator());
            coreSku.setModTime(timeStampToDate(coreSkuDisabledStatusHistoryDTO.getAddTime()));
            // 禁用
            if (status == 0) {
                // 注意:在skuDisableOperator执行前需要将sku更新，因此这边分两次执行更新操作
                coreSkuMapper.updateByPrimaryKeySelective(coreSku);
                skuDisableOperator(coreSku);
            } else if (status == 1) { // 启用
                coreSku.setCheckStatus(0);
                coreSku.setDisabledReason(null);
                coreSkuMapper.updateByPrimaryKeySelective(coreSku);
            }
            // 更新对应的SPU信息
            CoreSpu coreSpu = coreSpuMapper.getSpuBySku(coreSku.getSkuNo());
            coreSpu.setUpdater(coreSku.getUpdater());
            coreSpu.setModTime(coreSku.getAddTime());
            coreSpuMapper.updateSpuAfterDisableSku(coreSpu);
        } else if (GoodsValidConstants.SPU_TYPE.equals(type)) { //SPU
            List<CoreSku> coreSkuList = coreSkuMapper.getSkuBySpuId(id);
            // 取该SPU的操作人、操作时间
            CoreSpu coreSpu = coreSpuMapper.getSpuinfoById(id);
            CoreGoodsDisabledStatusHistoryDTO coreSkuDisabledStatusHistoryDTO = coreGoodsDisabledStatusHistoryMapper.getCreatorAndTime("V" + id, 1);
            coreSpu.setStatus(status);
            coreSpu.setDisabledReason(reason);
            coreSpu.setUpdater(coreSkuDisabledStatusHistoryDTO.getCreator());
            coreSpu.setModTime(timeStampToDate(coreSkuDisabledStatusHistoryDTO.getAddTime()));
            if (status == 0) {
                // 更新V_CORE_SPU_SEARCH(只有禁用时需要)
                CoreSpuSearchGenerate coreSpuSearchGenerate = coreSpuSearchGenerateMapper.selectByPrimaryKey(coreSpu.getSpuId());
                if (coreSpuSearchGenerate != null) {
                    coreSpuSearchGenerate.setUpdater(coreSpu.getUpdater());
                    coreSpuSearchGenerate.setModTime(coreSpu.getModTime());
                    coreSpuSearchGenerate.setStatus(status);
                    coreSpuSearchGenerateMapper.updateByPrimaryKeySelective(coreSpuSearchGenerate);
                }
                // 更新SPU下所有SKU和V_CORE_SKU_SEARCH的信息(禁用SPU)
                for (CoreSku coreSku : coreSkuList) {
                    coreSku.setStatus(status);
                    coreSku.setDisabledReason(reason);
                    coreSku.setUpdater(coreSpu.getUpdater());
                    coreSku.setModTime(coreSpu.getModTime());
                    coreSkuMapper.updateByPrimaryKeySelective(coreSku);
                    skuDisableOperator(coreSku);
                }
            } else if (status == 1) {
                coreSpu.setCheckStatus(0);
                coreSpu.setDisabledReason(null);
            }
            coreSpuMapper.updateSpuAfterDisableSku(coreSpu);
        }
    }

    // SKU禁用后GOODS、SEARCH等相关后置的操作
    private void skuDisableOperator(CoreSku coreSku) {
        // 更改V_CORE_SKU_SEARCH表的状态字段(只有禁用时需要)
        CoreSkuSearchGenerate coreSkuSearchGenerate = coreSkuSearchGenerateMapper.selectByPrimaryKey(coreSku.getSkuId());
        if (coreSkuSearchGenerate != null) {
            coreSkuSearchGenerate.setStatus(0);
            coreSkuSearchGenerate.setUpdater(coreSku.getUpdater());
            coreSkuSearchGenerate.setModTime(coreSku.getModTime());
            coreSkuSearchGenerateMapper.updateByPrimaryKeySelective(coreSkuSearchGenerate);
        }
        // 禁用T_GOODS表
        goodsGenerateMapper.updateGoodsDiscard(coreSku.getSkuNo(), 1);

        // 重新下传WMS
        CoreSkuGenerate toOld = coreSkuGenerateMapper.selectByPrimaryKey(coreSku.getSkuId());
        sendSkuInfoToWMS(toOld);

        // 推送至区域商城处理
        handlePushSkuInfo(coreSku);
    }

    // 时间戳转日期格式
    private Date timeStampToDate(Long timeStamp) throws ParseException {
        String time = DateFormatUtils.format(new Date(timeStamp), "yyyy-MM-dd HH:mm:ss");
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(time);
    }

    // 禁用SKU/SPU后处理SKU区域商城推送
    private void handlePushSkuInfo(CoreSku coreSku) {
        if (StringUtils.isNotEmpty(coreSku.getPushedOrgIdList())) {
            coreSku.setIsAvailableSale(2);
            coreSkuMapper.updateByPrimaryKeySelective(coreSku);
            pushSkuInfo("16", coreSku.getSkuId(), coreSku.getSpuId(), coreSku.getPushedOrgIdList());
        }
    }

    @Override
    public boolean pushSkuInfo(String platfromIds, Integer skuId, Integer spuId, String pushOrgIdList) {
        logger.info("开始推送sku商品信息 platfromIds:{}, skuId:{}", platfromIds, skuId);
        try {
            com.alibaba.fastjson.JSONObject jsonObject = coreOperateInfoService.getPushGoodsInfo(platfromIds, skuId, spuId);
            if (StringUtils.isNotEmpty(pushOrgIdList)) {
                // 转为List传过去方便前台调用
                String[] split = pushOrgIdList.split(",");
                List<Integer> pushOrgIds = new ArrayList<>(split.length);
                for (String id : split) {
                    pushOrgIds.add(org.apache.commons.lang3.math.NumberUtils.toInt(id));
                }
                jsonObject.put("pushOrgIdList", pushOrgIds);
            }
            if (jsonObject != null) {
                Map<String, String> map = new HashMap<>();
                map.put("operateInfo", jsonObject.toJSONString());
                logger.info("推送商品运营信息:" + jsonObject.toJSONString());
                // 获取运营平台中：各个平台集合列表
                ResultInfo<?> resultInfo = NewHttpClientUtils.doPost(operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, map);
                if (resultInfo != null && resultInfo.getCode().equals(200) &&
                        "success".equals(JSON.parseObject(resultInfo.getData().toString()).get("code"))) {
                    logger.info("推送商品运营信息结果:success skuId:{}", skuId);
                    goodsCommonService.updateSynchronizationStatusBySkuIds(Collections.singletonList(skuId),
                            SkuSynchronizationStatusEnum.PUSHED.getStatus());

                    // 更新二进制的已推送平台字段
                    String[] split = platfromIds.split(",");
                    List<String> platfromIdList = Arrays.asList(split);
                    coreOperateInfoService.updateSkuPushStatus(platfromIdList, skuId);

                    // 将已推送的区域商城id集合传入
                    // 需要将本次传入的id和之前保存的取并集(第一次推送区域商城时不需要)
                    CoreSku record = coreSkuMapper.selectByPrimaryKey(skuId);
                    if (StringUtils.isNotEmpty(pushOrgIdList)) {
                        if (StringUtils.isNotBlank(record.getPushedOrgIdList())) {
                            List<String> tempOrgIdList = Arrays.asList(record.getPushedOrgIdList().split(","));
                            List<String> orgIdList = new ArrayList<>(tempOrgIdList);
                            List<String> newOrgIdList = Arrays.asList(pushOrgIdList.split(","));
                            for (String orgId : newOrgIdList) {
                                if (!orgIdList.contains(orgId)) {
                                    orgIdList.add(orgId);
                                }
                            }
                            record.setPushedOrgIdList(orgIdList.stream().map(String::valueOf).collect(Collectors.joining(",")));
                        } else {
                            record.setPushedOrgIdList(pushOrgIdList);
                        }
                        coreSkuMapper.updateByPrimaryKeySelective(record);
                    }
                    return true;
                }
                goodsCommonService.updateSynchronizationStatusBySkuIds(Collections.singletonList(skuId),
                        SkuSynchronizationStatusEnum.RE_PUSH.getStatus());
                logger.warn("推送商品【skuId:" + skuId + "】运营信息失败：resultInfo:{}", resultInfo.toString());
            }
        } catch (Exception e) {
            goodsCommonService.updateSynchronizationStatusBySkuIds(Collections.singletonList(skuId),
                    SkuSynchronizationStatusEnum.RE_PUSH.getStatus());
            logger.error("推送商品【skuId:" + skuId + "】时，运营后台接口异常" + operateUrl + HttpURLConstant.OP_PUSH_GOODS_INFO, e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void completeDisableTask(String taskId, Integer userId, String comment, Boolean pass) throws ParseException {

        Objects.requireNonNull(taskId, "taskId is null");
        Objects.requireNonNull(pass, "pass is null");

        Map<String, Object> variables = Maps.newHashMapWithExpectedSize(3);
        variables.put("pass", pass);
        Task currentTask = vgoodsProcInstanceService.getDisableGoodsTaskByTaskId(taskId);
        if (currentTask == null) {
            throw new IllegalStateException("Task '" + taskId + "' is not exist");
        }
        User assignee = vgoodsProcInstanceService.checkCurrentAssignee(userId);
        currentTask.getProcessVariables();
        Map<String, Object> provessVars = vgoodsProcInstanceService.getProvessVars(taskId);

        Integer relatedId = (Integer) provessVars.get(ProcessInstanceContext.ATTR_NAME_RELATED_TABLE_KEY);
        String disableReason = (String) provessVars.get(ProcessInstanceContext.ATTR_NAME_RELATED_COMMENT);
        Integer goodsType = (Integer) provessVars.get("goodsType");

        vgoodsProcInstanceService.completeTask(currentTask, assignee, variables, comment, pass);
        if (pass) {
            //审核完成后记录商品禁用状态历史
            recordGoodsDisableHistory(relatedId, goodsType, disableReason, GoodsValidConstants.DISABLE, assignee);

            //执行后置条件
            enableSkuOrSpu(goodsType, relatedId, GoodsValidConstants.DISABLE, disableReason);

        } else {
            //审核不通过
            updateGoodsStatus(relatedId, goodsType, GoodsValidConstants.ENABLE);
        }

    }

    private void recordGoodsDisableHistory(Integer relatedId, Integer goodsType, String disableReason, Integer disableStatus, User user) {
        Long nowTime = DateUtil.sysTimeMillis();
        CoreGoodsDisabledStatusHistoryDTO goodsDisabledStatusHistoryDTO = new CoreGoodsDisabledStatusHistoryDTO();
        goodsDisabledStatusHistoryDTO.setGoodsNo("V" + relatedId);
        goodsDisabledStatusHistoryDTO.setCreator(user.getUserId());
        goodsDisabledStatusHistoryDTO.setAddTime(nowTime);
        goodsDisabledStatusHistoryDTO.setDisabledStatus(disableStatus);
        goodsDisabledStatusHistoryDTO.setReason(disableReason);
        goodsDisabledStatusHistoryDTO.setGoodsType(goodsType);

        if (GoodsValidConstants.SPU_TYPE.equals(goodsType)) {
            if (GoodsValidConstants.DISABLE.equals(disableStatus)) {
                List<CoreSku> skuList = coreSkuMapper.getSkuBySpuId(relatedId);
                if (CollectionUtils.isNotEmpty(skuList)) {
                    List<CoreGoodsDisabledStatusHistoryDTO> coreGoodsDisabledStatusHistoryDTOList = new ArrayList<>();
                    skuList.forEach(item -> {
                        CoreGoodsDisabledStatusHistoryDTO skuDisStatusHistory = new CoreGoodsDisabledStatusHistoryDTO();
                        skuDisStatusHistory.setGoodsNo(item.getSkuNo());
                        skuDisStatusHistory.setGoodsType(goodsType);
                        skuDisStatusHistory.setCreator(user.getUserId());
                        skuDisStatusHistory.setAddTime(nowTime);
                        skuDisStatusHistory.setDisabledStatus(disableStatus);
                        skuDisStatusHistory.setReason(disableReason);
                        coreGoodsDisabledStatusHistoryDTOList.add(skuDisStatusHistory);
                    });
                    if (CollectionUtils.isNotEmpty(coreGoodsDisabledStatusHistoryDTOList)) {
                        coreGoodsDisabledStatusHistoryMapper.batchInsert(coreGoodsDisabledStatusHistoryDTOList);
                    }
                }
            }
        }
        coreGoodsDisabledStatusHistoryMapper.insert(goodsDisabledStatusHistoryDTO);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void disableGoodsApplyVerify(Integer relatedId, String disableReason, Integer userId, Integer goodsType) {

        //开始审核流程
        vgoodsProcInstanceService.disableGoodsApplyVerify(relatedId, disableReason, userId, goodsType);

        //开启成功后回写主表商品禁用审核状态
        updateGoodsStatus(relatedId, goodsType, GoodsValidConstants.ENABLING);
    }

    //禁用审核中和审核不通过需要更改商品启用状态
    private void updateGoodsStatus(Integer relatedId, Integer goodsType, Integer status) {
        if (GoodsValidConstants.SKU_TYPE.equals(goodsType)) {
            CoreSku coreSku = new CoreSku();
            coreSku.setSkuId(relatedId);
            coreSku.setStatus(status);
            coreSkuMapper.updateByPrimaryKeySelective(coreSku);
        }
        if (GoodsValidConstants.SPU_TYPE.equals(goodsType)) {
            CoreSpuGenerate update = new CoreSpuGenerate();
            update.setSpuId(relatedId);
            update.setStatus(status);
            coreSpuGenerateMapper.updateByPrimaryKeySelective(update);
        }
    }

    @Override
    public Task getDisableGoodsTask(Integer relatedId, Integer goodsType) {
        return vgoodsProcInstanceService.getDisableGoodsTask(relatedId, goodsType);
    }

    @Override
    public List<HistoryVerfiyRecord> getDisableGoodsRecord(Integer related, Integer goodsType, User user) {
        return vgoodsProcInstanceService.getDisableGoodsRecord(related, goodsType, user);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void enbleGoodsApplyVerify(Integer relatedId, Integer userId, Integer goodsType) throws ParseException {
        Objects.requireNonNull(relatedId, "goodsId is null");
        Objects.requireNonNull(goodsType, "goodsType is null");

        User assignee = vgoodsProcInstanceService.checkCurrentAssignee(userId);
        //记录商品禁用状态操作历史
        recordGoodsDisableHistory(relatedId, goodsType, "", GoodsValidConstants.ENABLE, assignee);

        //执行后置条件
        enableSkuOrSpu(goodsType, relatedId, GoodsValidConstants.ENABLE, "");

    }

    @Override
    public Boolean getTaskCandidate(String taskId, User user) {
        return vgoodsProcInstanceService.isTaskCandidate(taskId, user);
    }

    @Override
    public Map<String, Integer> getPurchaseTime(List<String> skuNoList) {
        Map<String, Integer> purchaseTime = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuNoList)) {
            for (String item : skuNoList) {
                CoreSku coreSku = coreSkuMapper.selectBySkuNo(item);
                if (coreSku != null) {
                    purchaseTime.put(item, coreSku.getPurchaseTime());
                }
            }
        }
        return purchaseTime;
    }

    @Override
    public Map<String, Integer> getDeliverTime(List<String> skuNoList, Integer cityId, Integer areaLevel) {
        Map<String, Integer> deliverTime = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuNoList)) {
            for (String item : skuNoList) {
                Integer time = deliverTimeMapper.getDeliverTimeByAreaIdAndAreaLevel(cityId, areaLevel);
                if (time != null) {
                    deliverTime.put(item, time);
                }
            }
        }
        return deliverTime;
    }

    @Override
    public List<String> getAllSkuNo() {
        return coreSkuGenerateMapper.getAllSkuNo();
    }

    @Override
    public String getPushedOrgIdList(Integer skuId) {
        return coreSkuMapper.selectByPrimaryKey(skuId).getPushedOrgIdList();
    }


    @Override
    public void getSkuOrgName(Model model, CoreSkuGenerate skuGenerate) {
        //1.调用前台的接口查询orgName
        List<OrgDTO> orgDTOList = vgoodsApiService.getOrgDTOList();
        if (StringUtil.isNotBlank(skuGenerate.getOrgIdList())) {
            List<Integer> orgIds = Arrays.stream(skuGenerate.getOrgIdList().split(",")).map(s -> Integer.parseInt(s.trim())).collect(Collectors.toList());
            // 2. 过滤拼接"/"
            List<Integer> orgDtoIds = orgDTOList.stream().map(OrgDTO::getOrgId).collect(Collectors.toList());
            List<Integer> ids = orgIds.stream().filter(orgDtoIds::contains).collect(Collectors.toList());
            String orgName = orgDTOList.stream().filter(orgDTO -> ids.contains(orgDTO.getOrgId())).map(OrgDTO::getOrgName).collect(Collectors.joining("/"));
            skuGenerate.setOrgNameList(orgName);
        }
        model.addAttribute("skuGenerate", skuGenerate);
        model.addAttribute("orgList", orgDTOList);
    }

    @Override
    public void getOneYearEffectiveTransactionData(Model model, CoreSkuGenerate skuGenerate, Integer type) {
        SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = vgoodsApiService.findSkuPriceInfoBySkuNo(skuGenerate.getSkuNo());
        if (skuPriceInfoDetailResponseDto != null) {
            for (SkuPriceInfoPurchaseDto skuPriceInfoPurchaseDto : skuPriceInfoDetailResponseDto.getPurchaseList()) {
                String traderName = traderCustomerService.getTraderByTraderId(skuPriceInfoPurchaseDto.getTraderId().intValue()).getTraderName();
                skuPriceInfoPurchaseDto.setTraderName(traderName);
            }
        }
        model.addAttribute("skuPriceInfoDetailResponseDto", skuPriceInfoDetailResponseDto);
        SaleorderGoods saleorderGoods = new SaleorderGoods();
        saleorderGoods.setSku(skuGenerate.getSkuNo());
        saleorderGoods.setGoodsId(skuGenerate.getSkuId());

        //查询该sku下得订单数据
        List<SaleorderGoods> saleorderGoodsList = saleorderService.statisticalOrderGoods(saleorderGoods);

        BigDecimal transactionPrice = saleorderService.transactionPrice(skuGenerate.getSkuNo());
        if (transactionPrice != null) {
            model.addAttribute("price", transactionPrice.setScale(2, RoundingMode.HALF_UP));
        } else {
            model.addAttribute("price", 0);
        }

        model.addAttribute("saleorderGoodsList", saleorderGoodsList);
        model.addAttribute("type", type);

        //分别获取最近一笔成交记录--终端用户和分销记录
        if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
            List<SaleorderGoods> saleGoodsOfTerminalTrader =
                    saleorderGoodsList.stream().filter(e -> StringUtil.isBlank(e.getTitle()) && e.getTitle().contains("终端")).collect(Collectors.toList());
            List<SaleorderGoods> saleGoodsOfAgencyTrader =
                    saleorderGoodsList.stream().filter(e -> StringUtil.isBlank(e.getTitle()) && e.getTitle().contains("分销")).collect(Collectors.toList());
            model.addAttribute("lastSaleGoodsOfTerminalTrader", saleGoodsOfTerminalTrader.stream().findFirst().orElse(null));
            model.addAttribute("lastSaleGoodsOfAgencyTrader", saleGoodsOfAgencyTrader.stream().findFirst().orElse(null));
            BigDecimal price;
            int sum;
            if (CollectionUtils.isNotEmpty(saleGoodsOfTerminalTrader)) {
                price = saleGoodsOfTerminalTrader.stream().map(e -> e.getPrice().multiply(new BigDecimal(e.getNum())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                sum = saleGoodsOfTerminalTrader.stream().mapToInt(SaleorderGoods::getNum).sum();
                model.addAttribute("terminalPrice", price.divide(new BigDecimal(sum), 2, RoundingMode.HALF_UP));
            }
            if (CollectionUtils.isNotEmpty(saleGoodsOfAgencyTrader)) {
                price = saleGoodsOfAgencyTrader.stream().map(e -> e.getPrice().multiply(new BigDecimal(e.getNum())))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                sum = saleGoodsOfAgencyTrader.stream().mapToInt(SaleorderGoods::getNum).sum();
                model.addAttribute("agencyPrice", price.divide(new BigDecimal(sum), 2, RoundingMode.HALF_UP));
            }
        }
        List<BuyorderVo> buyOrderVos = buyorderService.getBuyorderVoList(skuGenerate.getSkuId());
        model.addAttribute("buyorderVos", buyOrderVos);
    }

    @Override
    public void initSkuPage(Model model, SkuAddCommand command) {
        //所有SKU下的属性
        List<BaseAttributeVo> list = this.selectAllAttributeBySkuId(command.getSpuId(), command.getSkuId());
        if (CollectionUtils.isNotEmpty(list) && !ArrayUtils.isEmpty(command.getBaseAttributeValueId())) {
            list.stream().filter(vo -> CollectionUtils.isNotEmpty(vo.getAttrValue())).forEach(vo -> {
                List<BaseAttributeValueVo> valueVos = vo.getAttrValue();
                valueVos.forEach(valueVo -> vo.setSelected(ArrayUtils.contains(command.getBaseAttributeValueId(), valueVo.getBaseAttributeValueId())));
            });
        }

        List<BaseAttributeVo> selectedAttr = this.selectAllAttributeBySpuId(command.getSpuId());
        if (CollectionUtils.isNotEmpty(selectedAttr)) {
            selectedAttr.forEach(attr -> {
                if (ErpConst.ONE.equals(attr.getIsPrimary())) {
                    attr.setIsPrimary(1);
                }
            });
        }

        //配置清单
        if (command.getSkuId() != null) {
            CoreSkuGenerate skuGenerate = this.selectSkuConfigBySkuId(command.getSkuId());
            if (StringUtils.isNotBlank(skuGenerate.getConfigurationList())) {
                Map<String, String> configurationMap = GoodsParameterUtils.tokenizeParameterValues(skuGenerate.getConfigurationList());
                model.addAttribute("configurationMap", configurationMap);
            }
        }

        model.addAttribute("baseAttributeVoList", list);
        model.addAttribute("primaryAttributeVoList", selectedAttr);

        //所有单位
        Unit unit = new Unit();
        unit.setCompanyId(command.getUser().getCompanyId());
        List<Unit> unitList = unitService.getAllUnitList(unit);
        model.addAttribute("unitList", unitList);
    }

    @Override
    public void getAuthorizationInfo(Model model, Integer skuId) {
        try {
            SkuAuthorizationVo skuAuthorizationInfo = skuAuthorizationService.getSkuAuthorizationInfoBySkuId(skuId);
            model.addAttribute("skuAuthorizationInfo", skuAuthorizationInfo);
            model.addAttribute("regions", regionService.getRegionByParentId(1));
            model.addAttribute("terminalTypes", skuAuthorizationService.getAllTerminalTypes());
        } catch (Exception e) {
            logger.error("获取报备信息 error", e);
        }
    }

    @Override
    public void getGoodsAuditInfo(Model model, Integer skuId, User sessUser) {
        try {
            //sku禁用审核记录
            model.addAttribute("disableGoodsVerifyRecord", this.getDisableGoodsRecord(skuId, SKU_TYPE, sessUser));
            //禁用spuTaskId
            Task disableGoodsTask = this.getDisableGoodsTask(skuId, SKU_TYPE);
            model.addAttribute("disableGoodsTask", disableGoodsTask);
            //当前登录用户是否属于任务候选人
            if (Objects.nonNull(disableGoodsTask)) {
                model.addAttribute("isDisableGoodsCandidate", this.getTaskCandidate(disableGoodsTask.getId(), sessUser));
            }
        } catch (Exception e) {
            logger.error("获取商品审核信息异常！skuId" + skuId, e);
        }
    }

    @Override
    public String returnView(Model model, String pageType, CoreSkuGenerate skuGenerate) {
        String view = "";
        switch (pageType) {
            case "0":
                try {
                //获取未完成的SKU待办事项
                List<GoodsTodoItemVo> goodsTodoItemVoList = goodsCommonService.listSkuTodoItems(skuGenerate.getSkuId(), true);
                model.addAttribute("goodsTodoItemVoList", goodsTodoItemVoList);

                // VDERP-9776

                initSkuHistory(model, skuGenerate.getSkuId());
                } catch (Exception e) {
                    logger.error("处理Sku历史信息错误", e);
                }

                view = "goods/vgoods/sku/sku_view";
                break;
            case "1":
                //查询该sku对应的spu图片
                GoodsAttachment goodsAttachment = new GoodsAttachment();
                goodsAttachment.setGoodsId(skuGenerate.getSkuId());
                goodsAttachment.setStatus(CommonConstants.STATUS_1);
                goodsAttachment.setAttachmentType(CommonConstants.ATTACHMENT_TYPE_SKU_1001);
                List<GoodsAttachment> goodsAttachmentList = coreOperateInfoService.getGoodsAttachment(goodsAttachment);
                model.addAttribute("goodsImages", goodsAttachmentList);

                SkuPriceChangeApplyDto skuPriceChangeApplyDto = skuPriceModifyRecordService.findPriceChangeApply(skuGenerate.getSkuId()) ;

                if (skuPriceChangeApplyDto !=null && skuPriceChangeApplyDto.getAuditPass()!= null
                        && skuPriceChangeApplyDto.getAuditPass()== 1 &&
                        skuPriceChangeApplyDto !=null &&  skuPriceChangeApplyDto.getDisabled()!= null
                        &&  skuPriceChangeApplyDto.getDisabled()== 0
                        ){
                    model.addAttribute("alreadyPriced",1);//表示已核价
                }

                //查询该sku对应的运营信息
                CoreOperateInfoGenerateVo operateInfo = coreOperateInfoService.getCoreOperateInfoBySkuId(skuGenerate.getSkuId());
                if (operateInfo != null) {
                    MlSkuRequestVo mlSkuRequestVo = new MlSkuRequestVo();
                    mlSkuRequestVo.setScoreFlag(Boolean.TRUE);
                    mlSkuRequestVo.setSkuId(skuGenerate.getSkuId());
                    OperateInfoSourceEnum sourceEnum = OperateInfoSourceEnum.getBySource(operateInfo.getOperateInfoSource());
                    if (OperateInfoSourceEnum.ML.equals(sourceEnum)) {
                        MlSkuResponseVo mlSkuResponseVo = mlFacadeService.getSkuGraphicDetailInfo(mlSkuRequestVo);
                        if (StringUtils.isNotBlank(mlSkuResponseVo.getSkuGraphicDetail())) {
                            operateInfo.setOprateInfoHtml(mlSkuResponseVo.getSkuGraphicDetail());
                        }
                    }
                    model.addAttribute("operateInfoVo", operateInfo);
                }
                model.addAttribute("pushedPlatformNames", SKUPushedStatusEnum.getPlatformNames(skuGenerate.getPushStatus()));
                model.addAttribute("onSaleStr", this.getSaleStr(skuGenerate.getPushStatus(), skuGenerate.getOnSale()));



                view = "goods/vgoods/newAddGood/newAddGoodDetails";
                break;
            case "2":
                view = "goods/vgoods/newAddGood/newCommodityFlowInfo";
                break;
            default:
                break;
        }
        return view;
    }

    /**
     * 查询封装sku历史信息
     */
    private void initSkuHistory(Model model, Integer skuId) {
        CoreSkuHistory coreSkuHistory = coreSkuHistoryMapper.selectBySkuId(skuId);
        if (!Objects.isNull(coreSkuHistory)) {
            SkuAddCommand oldCommand = JSON.toJavaObject(coreSkuHistory.getSkuAddCommand(), SkuAddCommand.class);
            oldCommand.setGoodsLevelVo(goodsCommonService.getGoodsLevelVo(oldCommand.getGoodsLevelNo()));
            oldCommand.setGoodsPositionVo(goodsCommonService.getGoodsPositionVo(oldCommand.getGoodsPositionNo()));
            // 签约模式
            GoodsSignContractModeEnum goodsSignContractModeToUse;
            GoodsPositionDo goodsPosition = goodsCommonService.getGoodsPosition(oldCommand.getGoodsPositionNo());
            if (goodsPosition != null && !CommonConstants.IS_DELETE_1.equals(goodsPosition.getIsDeleted())) {
                goodsSignContractModeToUse = GoodsSignContractModeEnum.getByCode(goodsPosition.getSignContractMode());
            } else {
                goodsSignContractModeToUse = GoodsSignContractModeEnum.NONE;
            }
            oldCommand.setGoodsSignContractModeStr(goodsSignContractModeToUse.getName());

            // 处理 检测报告文件
            List<FileInfo> oldSkuCheck = new ArrayList<>();
            List<String> oldUrl = new ArrayList<>();
            String skuCheckFilesJson = coreSkuHistory.getSkuCheckFilesJson().replace("\\\"", "\"").substring(1);
            JSONArray jsonArray = JSONArray.parseArray(skuCheckFilesJson.substring(0, skuCheckFilesJson.length() - 1));
            for (Object o : jsonArray) {
                FileInfo tempInfo = JSON.toJavaObject((JSONObject) o, FileInfo.class);
                oldSkuCheck.add(tempInfo);
                oldUrl.add(tempInfo.getOssUrl());
            }
            oldCommand.setSkuCheck(oldSkuCheck);
            model.addAttribute("oldCommand", oldCommand);

            // 根据文件的oss地址来判断文件是否发生了改变
            List<String> newUrl = new ArrayList<>();
            SkuAddCommand command = (SkuAddCommand) model.asMap().get("command");
            command.getSkuCheck().forEach(item -> {
                newUrl.add(item.getOssUrl());
            });
            boolean skuCheckChange = false;
            for (String s : oldUrl) {
                if (!newUrl.contains(s)) {
                    skuCheckChange = true;
                    break;
                }
            }

            for (String s : newUrl) {
                if (!oldUrl.contains(s)) {
                    skuCheckChange = true;
                    break;
                }
            }
            model.addAttribute("skuCheckChange", skuCheckChange);

            CoreSkuGenerate oldSkuGenerate = JSON.toJavaObject(coreSkuHistory.getCoreSkuGenerate(), CoreSkuGenerate.class);

            // 比较是否发生变化时，用原值；展示时拼接上换行标签
            model.addAttribute("oldTechnicalParameter", oldSkuGenerate.getTechnicalParameter());
            if (StringUtils.isNotEmpty(oldSkuGenerate.getTechnicalParameter())) {
                oldSkuGenerate.setTechnicalParameter(oldSkuGenerate.getTechnicalParameter().replace(";", "<br>"));
            }

            model.addAttribute("oldConfigurationList", oldSkuGenerate.getConfigurationList());
            if (StringUtils.isNotEmpty(oldSkuGenerate.getConfigurationList())) {
                oldSkuGenerate.setConfigurationList(oldSkuGenerate.getConfigurationList().replace(";", "<br>"));
            }

            model.addAttribute("oldSkuGenerate", oldSkuGenerate);

            // 授权范围数据处理
            JSONObject skuAuthorizationRequestVo = coreSkuHistory.getSkuAuthorizationRequestVo();
            if (!Objects.isNull(skuAuthorizationRequestVo)) {
                List<SkuAuthorizationItemVo> oldSkuAuthorizationItemVoList = new ArrayList<>();
                String[] items = skuAuthorizationRequestVo.get("authInfo").toString().split(";");
                for (String s : items) {
                    SkuAuthorizationItemVo temp = new SkuAuthorizationItemVo();
                    String[] itemInfo = s.split("@,");
                    temp.setSnowFlakeId(Long.valueOf(itemInfo[2]));
                    List<Integer> regionIds = new ArrayList<>();
                    for (String s1 : itemInfo[0].split("@")) {
                        regionIds.add(Integer.valueOf(s1));
                    }
                    temp.setRegionIds(regionIds);
                    List<Integer> terminalTypeIds = new ArrayList<>();
                    for (String s1 : itemInfo[1].split("@")) {
                        terminalTypeIds.add(Integer.valueOf(s1));
                    }
                    temp.setTerminalTypeIds(terminalTypeIds);
                    oldSkuAuthorizationItemVoList.add(temp);
                }
                model.addAttribute("oldSkuAuthorizationItemVoList", oldSkuAuthorizationItemVoList);

//                // 判断授权信息是否发生了改变
                SkuAuthorizationVo skuAuthorizationInfo = (SkuAuthorizationVo)model.asMap().get("skuAuthorizationInfo");
                List<SkuAuthorizationItemVo> skuAuthorizationItemVoList = skuAuthorizationInfo.getSkuAuthorizationItemVoList();
                boolean isChange = false;
                for (SkuAuthorizationItemVo item : oldSkuAuthorizationItemVoList) {
                    if (!skuAuthorizationItemVoList.contains(item)) {
                        isChange = true;
                        break;
                    }
                }

                for (SkuAuthorizationItemVo item : skuAuthorizationItemVoList) {
                    if (!oldSkuAuthorizationItemVoList.contains(item)) {
                        isChange = true;
                        break;
                    }
                }
                model.addAttribute("isChange", isChange);
            }

        }
    }

    @Override
    public void updateByPrimaryKeySelective(CoreSku coreSku) {
        coreSkuMapper.updateByPrimaryKeySelective(coreSku);
    }

    @Override
    public ResultInfo saveVirtureSku(VirtualSkuDto virtualSkuDto,User user){
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        CoreSku curCoreSku = coreSkuMapper.selectByPrimaryKey(virtualSkuDto.getSkuId());
        if(ErpConst.ONE.equals(curCoreSku.getIsVirtureSku())){
            return new ResultInfo(-1,"该商品已经添加费用编码");
        }else {
            CoreSku coreSku = new CoreSku();
            if ((StrUtil.isBlank(curCoreSku.getTaxCategoryNo()) && StrUtil.isNotBlank(virtualSkuDto.getTaxCategoryNo())) ||
                    (StrUtil.isNotBlank(curCoreSku.getTaxCategoryNo()) && !curCoreSku.getTaxCategoryNo().equals(virtualSkuDto.getTaxCategoryNo()))) {
                coreSku.setTaxCategoryNoRecord(
                        StrUtil.format(ErpConst.TAX_CATEGORY_NO_RECORD_TEMPLATE,
                                CurrentUser.getCurrentUser().getUsername(), cn.hutool.core.date.DateUtil.formatDateTime(new Date()), curCoreSku.getTaxCategoryNo()));
            }
            coreSku.setTaxCategoryNo(virtualSkuDto.getTaxCategoryNo());
            coreSku.setSkuId(virtualSkuDto.getSkuId());
            coreSku.setCostCategoryId(virtualSkuDto.getCostCategoryId());
            coreSku.setHaveStockManage(virtualSkuDto.getHaveStockManage());
            coreSku.setVirtureCreator(user.getUserId());
            coreSku.setVirtureTime(new Date());
            coreSku.setIsVirtureSku(ErpConst.ONE);
            coreSkuMapper.updateByPrimaryKeySelective(coreSku);
        }
        return resultInfo;
    }

    @Override
    public CoreSkuGenerate getSkuGoodesUnrealfoBySku(Long relatedId) {
        return goodsMapper.getSkuGoodesUnrealfoBySku(relatedId);
    }

    @Override
    public void updateVirtureSku(VirtualSkuDto virtualSkuDto,User user) {
            CoreSku coreSku = new CoreSku();
            CoreSku curCoreSku = coreSkuMapper.selectByPrimaryKey(virtualSkuDto.getRelatedId());
            if ((StrUtil.isBlank(curCoreSku.getTaxCategoryNo()) && StrUtil.isNotBlank(virtualSkuDto.getTaxCategoryNo())) ||
                    (StrUtil.isNotBlank(curCoreSku.getTaxCategoryNo()) && !curCoreSku.getTaxCategoryNo().equals(virtualSkuDto.getTaxCategoryNo()))) {
                coreSku.setTaxCategoryNoRecord(
                        StrUtil.format(ErpConst.TAX_CATEGORY_NO_RECORD_TEMPLATE,
                                CurrentUser.getCurrentUser().getUsername(), cn.hutool.core.date.DateUtil.formatDateTime(new Date()), curCoreSku.getTaxCategoryNo()));
            }
            coreSku.setSkuId(virtualSkuDto.getRelatedId());
            coreSku.setCostCategoryId(virtualSkuDto.getCostCategoryId());
            coreSku.setVirtureCreator(user.getUserId());
            coreSku.setTaxCategoryNo(virtualSkuDto.getTaxCategoryNo());
            coreSku.setVirtureTime(new Date());
            coreSkuMapper.updateByPrimaryKeySelective(coreSku);

    }

    @Override
    public List<Integer> getVirtureSkuIdList() {
        List<CoreSku> allVirtureSkuList = coreSkuMapper.getAllVirtureSkuList();
        List<Integer> virtureSkuList = new ArrayList<>();
        for (CoreSku coreSku : allVirtureSkuList) {
            virtureSkuList.add(coreSku.getSkuId());
        }
        return virtureSkuList;
    }

    @Override
    public String getTaxCategoryNo(Integer goodsId) {
        String taxCategoryNo = coreSkuMapper.getTaxCategoryNo(goodsId);
        if (StrUtil.isEmpty(taxCategoryNo)) {
            return "";
        }
        return StrUtil.padAfter(taxCategoryNo, 19, "0");
    }

    @Override
    public List<CoreSku> searchSku(String keyword) {
        return coreSkuMapper.searchSku(keyword);
    }
}
