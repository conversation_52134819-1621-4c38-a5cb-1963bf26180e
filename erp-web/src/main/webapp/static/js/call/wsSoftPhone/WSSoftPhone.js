if (typeof String.prototype.startsWith != 'function') {
	String.prototype.startsWith = function(str) {
		var reg = new RegExp("^" + str);
		return reg.test(this);
	};
};

if (typeof String.prototype.endsWith != 'function') {
	String.prototype.endsWith = function(str) {
		var reg = new RegExp(str + "$");
		return reg.test(this);
	};
};
/**
 * WSSoftPhone软电话对象 attrs:初始化属性 eventHandler:事件回调函数集
 */
function WSSoftPhone(attrs, eventHandler) {
	//创建时生成唯一标识号
	this.spObjectId = "spo_" + new Date().valueOf() + Math.random().toString(36).substr(6);

	//获取本机guid
	this.computerId = localStorage.getItem("wsagentserver.computerId");
	if(!this.computerId){
		this.computerId = this.guid();
		localStorage.setItem("wsagentserver.computerId", this.computerId);
	}

	//var self = this;
	this.eventHandler = eventHandler;
	// WS服务器地址
	this.serverUri = "";
	// WS服务器地址2,默认为空字符串，代表不启用
	this.serverUri2 = "";

	//实际连接到的ws Server IP
	this.wsServerIp = "";

	// 与BC系统的会话标识
	this.session = "";
	// 坐席实例，通常为分机号
	this.ins = "6001";
	// 坐席工号
	this.agentId = "999";
	// 坐席姓名
	this.agentName = "DemoAgent";
	// 坐席技能
	this.agentSK = "1001";
	// 坐席状态
	this.agentState = "0";
	//工作模式,默认普通模式
	this.agentWorkMode = "0";
	//坐席状态持续时长
	this.stateTimeLength = "";
	//坐席是否处于登录状态
	this.isLogon = false;
	//ipt分机状态
	this.iptState = "0";
	//ipt分机是否静音
	this.isMute = false;
	//离席原因
	this.leaveReason= "0";
	// 是否班长席
	this.isMonitor = false;
	// 管理部门 1,2,3
	this.monitorDepts = "";
	// SoftPhoneCode
	this.softPhoneCode = "";
	// 初始化参数
	this.customInitParams = null;
	// 代理Id，在登录时获取，后续操作均需要使用改命令
	this.delegatorId = "";
	this.coInfos = {};
	//队列类型 0-详细队列  1-汇总队列  2-技能队列
	this.queueType = 0;
	//详细队列
	this.queueList = {};
	//技能队列,包含汇总长度
	this.skQueueInfo = {
		allQueueLength:0,
		myQueueLength:0,
		skills:{}
	};

	// 临时呼叫属性，在转接时可以设置
	this.tempCOInfos = {};
	// 状态按钮设置
	this.stateSettings = null;
	// 工作模式设置
	this.workModeSettings = null;
	// 离席原因设置
	this.leaveReasonSettings = null;
	// 转接下来选项
	this.transferSubItems = null;
	// 转接IVR
	this.transferIVRInfo = null;
	// 转接坐席技能
	this.transferAgentSkillInfo = null;
	// IVR快捷按钮
	this.ivrButtonSettings = null;
	//外拨主叫候选列表
	this.dialoutANIs =null;
	//是否启用循环主叫号码池
	this.rollingANI = false;
	//滚动标识，外拨后设置为tre，滚动时复原
	this.rollANIMark = false;
	//统一主叫号码池
	this.unifyANIPool = false;
	//是否允许设置自动应答，有的系统不允许坐席手动调整
	this.canSetAutoAnswer = false;
	//能否自动应答，对于模拟分机等，是不能自动应答的
	this.canAutoAnswer = false;
	//是否自动应答，打开是通过js前端自动执行应答操作
	this.isAutoAnswer = false;
	//是否连接IPT
	this.isConnectIpt = false;
	//ipt心跳时间
	this.iptHearBeatTime = new Date();

	//主动关闭Ipt连接时，设置为忽略
	this.ignoreIptClose = false;
	// 是否支持保持登录
	this.canKeepLogon = false;
	//是否进行签出限制
	this.logoffCheck = true;

	// 当前是否保持登录
	this.isKeepLogon = false;
	//ws连接成功
	this.wsConnected = false;
	// 是否正在连接Server
	this.isConnectingServer = false;
	this.Logger = null;

	//登录后获取到的ipts信息
	this.protocolType = "TCP";
	this.iptsIp = "127.0.0.1";
	this.iptsPort = "11069";

	//新增ticket支持
	this.cgiOpen = false;
	this.cgiTicket ="";

	if (attrs) {
		if (attrs.stateSettings) {
			this.stateSettings = attrs.stateSettings;
		}
		if (attrs.workModeSettings) {
			// 自定义工作模式
			this.workModeSettings = attrs.workModeSettings;
		}
		if (attrs.leaveReasonSettings) {
			// 自定义离席原因
			this.leaveReasonSettings = attrs.leaveReasonSettings;
		}

		if (attrs.customInitParams) {
			this.customInitParams = attrs.customInitParams;
		}
		if (attrs.serverUri) {
			this.serverUri = attrs.serverUri;
		}
		if(attrs.serverUri2){
			this.serverUri2 = attrs.serverUri2;
		}
		if (attrs.ins) {
			this.ins = attrs.ins;
		}
		if (attrs.agentId) {
			this.agentId = attrs.agentId;
		}
		if (attrs.agentName) {
			this.agentName = attrs.agentName;
		}

		//处理预设工作模式 2021-4-1
		if(attrs.agentWorkMode){
			this.agentWorkMode = attrs.agentWorkMode;
		}

		if(attrs.isConnectIpt){
			this.isConnectIpt = attrs.isConnectIpt;
		}
		if(attrs.isAutoAnswer){
			this.isAutoAnswer = attrs.isAutoAnswer;
		}
		if(attrs.softPhoneCode){
			this.softPhoneCode = attrs.softPhoneCode;
		}
		if (attrs.delegatorId) {
			this.delegatorId = attrs.delegatorId;
		}
		if (attrs.saveLocalLog) {
			if (attrs.saveLocalLog === true || attrs.saveLocalLog === "true") {
				this.Logger = new WSLocalLogger();
			}
		}
		//2021-12-14 新增ticket参数
		if(attrs.cgiOpen){
			this.cgiOpen = attrs.cgiOpen;
		}
		if(attrs.cgiTicket){
			this.cgiTicket = attrs.cgiTicket;
		}
	}
};

// 获取当前状态的集合
WSSoftPhone.prototype.getStateSetting = function() {
	var _wssp = this;
	//2021-12-23 临时处理，将27 转换为5的按钮建议
	var state =this.agentState;
	if(state == 27){
		state = 5;
	}

	if (_wssp.stateSettings) {
		for (var i = 0; i < _wssp.stateSettings.length; i++) {
			if (_wssp.stateSettings[i].state == state) {
				return _wssp.stateSettings[i];
			}
		}
	}
	return null;
};

/**
 * 连接WebSocket Server
 *
 * @param serverUri
 *            WebSocket地址
 */
WSSoftPhone.prototype.connectServer = function(serverUri) {
	var _wssp = this;
	_wssp.isConnectingServer = true;
	// 如果传递了URL则使用，否则使用构造参数中的地址
	if (serverUri) {
		_wssp.serverUri = serverUri;
	}

	//尝试关闭之前的连接
	_wssp.close();

	console.log(_wssp.getCurDateString() +"......connectServer:" + _wssp.serverUri);

	//重新创建WebSocket
	_wssp.ws = new WebSocket(_wssp.serverUri);
	//检查双serverUri设置，如果支持，对调地址属性
	_wssp.switchServerUri();
	_wssp.showTip("连接Server...",0);
	_wssp.ws.onopen = function() {
		// 复位连接标志
		_wssp.isConnectingServer = false;
		_wssp.wsConnected = true;
		_wssp.writeLog("WebSocket#onopen");
		_wssp.showTip("连接Server成功！",0);

		console.log(_wssp.getCurDateString() +"......connectServer:succ");
		if (_wssp.eventHandler.onWSOpen) {
			_wssp.eventHandler.onWSOpen.call(_wssp);
		}

		if(_wssp.cgiOpen) {
			//2021-12-14 增加ticket验证
			//在verifyTicket回调中，再进行checkWebSocket调用
			_wssp.verifyTicket();
		}else {
			//检查是否需要顶替原有WebSocket连接
			_wssp.checkWebSocket();
		}
	};
	_wssp.ws.onmessage = function(e) {
		if (e.data && e.data != "") {
			// 判断为消息对象
			if (e.data.startsWith("{")) {
				var msg = JSON.parse(e.data);
				if (msg.objectClass) {
					if (msg.objectClass === "SPEvent") {
						_wssp.processEvent(msg);
					} else if (msg.objectClass === "SPResponse") {
						_wssp.processResponse(msg);
					} else {
						_wssp.trace("unknow msg:" + e.data);
					}
				} else {
					_wssp.eventHandler.onTrace(_wssp, e.data);
				}
			}
		}
	};
	_wssp.ws.onclose = function(e) {
		_wssp.writeLog("WebSocket#onclose");
		_wssp.showTip("Server连接关闭！",2);
		_wssp.ws = null;
		_wssp.wsConnected = false;
		console.log(_wssp.getCurDateString() +  "......connectServer:closeed");

		if (_wssp.eventHandler.onWSClose) {
			_wssp.eventHandler.onWSClose.call(_wssp,e);
		}
		// 补充上报连接失败事件
		if (_wssp.isConnectingServer) {
			_wssp.isConnectingServer = false;
			if (_wssp.eventHandler.onWSConnectFailed) {
				_wssp.showTip("连接Server失败！",2);
				_wssp.eventHandler.onWSConnectFailed.call(_wssp);
			}
		}
	};

};



/***
 * 检查双serverUri设置，如果支持，对调地址属性
 */
WSSoftPhone.prototype.switchServerUri = function() {
	if( this.serverUri2 && this.serverUri2 != ""){
		var uri = this.serverUri;
		this.serverUri = this.serverUri2;
		this.serverUri2 = uri;
		this.trace("set serverUri=" + this.serverUri + " for next connectServer");
	}
};

/**
 * 处理事件通知
 *
 * @param evt
 *            事件对象
 */
WSSoftPhone.prototype.processEvent = function(evt) {
	var _wssp = this;
	if (evt.ins != this.ins && this.ins != INS_WAIT_FOR_DISPATCH ) {
		this.trace("Ins not match!!!!");
		return;
	}

	if (evt.event === ESPEvent.SettingsNotify) {
		_wssp.processEvent_SettingsNotify(evt);
	} else if (evt.event === ESPEvent.StateChange) {
		var st = evt.agentInfos.agentState;
		// 签出后状态保持为1，可以执行签入操作
		if (st === "0") {
			st = "1";
		}
		_wssp.agentState = st;
		_wssp.agentWorkMode = evt.agentInfos.agentWorkMode;
		_wssp.leaveReason = evt.agentInfos.leaveReason;
		// 空闲时清空呼叫信息
		if (st ==  ESPState.AGENTSTATE_IDLE) {
			_wssp.coInfos = {};
			_wssp.tempCOInfos = {};

			//空闲时，执行主叫号码滚动
			var newANI = this.rollANI();
			this.trace("rollANI=" + newANI);
		}
		_wssp.notifyStateChange(this.agentState);
	} else if (evt.event === ESPEvent.WorkModeChange) {
		var st = evt.agentInfos.agentState;
		// 签出后状态保持为1，可以执行签入操作
		if (st === "0") {
			st = "1";
		}
		_wssp.agentState = st;
		_wssp.agentWorkMode = evt.agentInfos.agentWorkMode;
		_wssp.eventHandler.onWorkModeChange.call(_wssp, _wssp.agentWorkMode);
	} else if (evt.event === ESPEvent.QueueChange) {
		// 转换为Json对象，需要增加括号
		_wssp.queueList = eval(evt.params.queueList);
		if (_wssp.eventHandler.onQueueChange) {
			_wssp.eventHandler.onQueueChange.call(_wssp, _wssp.queueList);
		}
	}else if (evt.event === ESPEvent.SkillQueueChange){
		_wssp.trace(evt.params.skQueueInfo);
		_wssp.skQueueInfo = JSON.parse(evt.params.skQueueInfo);
		if (_wssp.eventHandler.onSkillQueueChange) {
			_wssp.eventHandler.onSkillQueueChange.call(_wssp, _wssp.skQueueInfo);
		}

	} else if (evt.event === ESPEvent.CallArrive) {
		_wssp.writeLog("ESPEvent.CallArrive:" + JSON.stringify(evt.coInfos));
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("电话振铃中...",0);

		if(_wssp.canAutoAnswer === true && _wssp.isAutoAnswer === true){
			setTimeout(function() {
				_wssp.trace("autoAnswer...");
				_wssp.answerCall();
			}, 200);
		}

		if (_wssp.eventHandler.onCallArrive) {
			_wssp.eventHandler.onCallArrive.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.CallInnerBegin) {
		//2021-6-4增加CallInnerBegin的处理
		_wssp.writeLog("ESPEvent.CallInnerBegin:" + JSON.stringify(evt.coInfos));
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("内呼开始...",0);

		if (_wssp.eventHandler.onCallInnerBegin) {
			_wssp.eventHandler.onCallInnerBegin.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.CallInnerSucc) {
		//2021-6-4增加CallInnerSucc的处理
		_wssp.writeLog("ESPEvent.CallInnerSucc:" + JSON.stringify(evt.coInfos));
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("内呼成功",0);
		if (_wssp.eventHandler.onCallInnerSucc) {
			_wssp.eventHandler.onCallInnerSucc.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.LogonSucc) {
		_wssp.writeLog("ESPEvent.LogonSucc");
		_wssp.showTip("登录成功",0);
		_wssp.agentState = evt.agentInfos.agentState;
		_wssp.agentWorkMode = evt.agentInfos.agentWorkMode;
		_wssp.checkMonitor(evt.agentInfos);
		_wssp.checkKeepLogon(evt.agentInfos);
		_wssp.checkAutoAnswer(evt.agentInfos);
		_wssp.isLogon = true;
		if (_wssp.eventHandler.onLogonSucc) {
			_wssp.eventHandler.onLogonSucc.call(_wssp, evt.ins);
		}
	} else if (evt.event === ESPEvent.LogoffSucc) {
		_wssp.writeLog("ESPEvent.LogoffSucc");
		_wssp.showTip("签出成功",0);
		_wssp.isLogon = false;
		if (_wssp.eventHandler.onLogoffSucc) {
			_wssp.eventHandler.onLogoffSucc.call(_wssp, evt.ins);
		}
	} else if (evt.event === ESPEvent.LogonFailed) {
		_wssp.writeLog("ESPEvent.LogonFailed(" + evt.errorCode + "):"
			+ evt.errorDesc);
		_wssp.showTip("登录失败(" + evt.errorCode + "):" + evt.errorDesc,2);
		if (_wssp.eventHandler.onLogonFailed) {
			_wssp.eventHandler.onLogonFailed.call(_wssp, evt.ins,
				evt.errorCode, evt.errorDesc);
		}
	} else if (evt.event === ESPEvent.PrepareDialSucc) {
		if (_wssp.eventHandler.onPrepareDialSucc) {
			_wssp.eventHandler.onPrepareDialSucc.call(_wssp);
		}
	} else if (evt.event === ESPEvent.LeaveSucc) {
		_wssp.showTip("离席成功",0);
	} else if (evt.event === ESPEvent.LeaveFailed) {
		_wssp.showTip("离席失败",1);
	} else if (evt.event === ESPEvent.ResumeWorkSucc) {
		_wssp.showTip("复席成功",0);
	} else if (evt.event === ESPEvent.ResumeWorkFailed) {
		_wssp.showTip("复席失败",1);
	} else if (evt.event === ESPEvent.PrepareDialFailed) {
		_wssp.showTip("准备外拨失败(" + evt.errorCode + "):" + evt.errorDesc,1);
	} else if (evt.event === ESPEvent.DevConnected) {
		_wssp.showTip("开始通话",0);
		if (_wssp.eventHandler.onDevConnected) {
			_wssp.eventHandler.onDevConnected.call(_wssp);
		}
	} else if (evt.event === ESPEvent.DevDisconnected) {
		_wssp.showTip("通话结束",0);
		if (_wssp.eventHandler.onDevDisconnected) {
			_wssp.eventHandler.onDevDisconnected.call(_wssp);
		}
	} else if (evt.event === ESPEvent.DialBegin) {

		_wssp.writeLog("ESPEvent.DialBegin:" + JSON.stringify(evt.coInfos));
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("外拨开始",0);
		if (_wssp.eventHandler.onDialBegin) {
			_wssp.eventHandler.onDialBegin.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.DialSucc) {
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("外拨成功",0);
		if (_wssp.eventHandler.onDialSucc) {
			_wssp.eventHandler.onDialSucc.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.DialFailed) {
		_wssp.showTip("外拨失败",1);
		if (_wssp.eventHandler.onDialFailed) {
			var errorCode = evt.errorCode;
			_wssp.eventHandler.onDialFailed.call(_wssp, errorCode);
		}
	} else if (evt.event === ESPEvent.WebSocketReplaced) {
		_wssp.showTip("软电话被顶替！！！",2);
		_wssp.isLogon = false;
		//复位坐席状态
		_wssp.agentState = "0";
		_wssp.notifyStateChange("0");

		if (_wssp.eventHandler.onWebSocketReplaced) {
			_wssp.eventHandler.onWebSocketReplaced();
		}
	} else if (evt.event === ESPEvent.HoldSucc) {
		_wssp.showTip("保持成功",0);
	} else if (evt.event === ESPEvent.HoldFailed) {
		_wssp.showTip("保持失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.FetchHoldSucc) {
		_wssp.showTip("取保持成功",0);
	} else if (evt.event === ESPEvent.FetchHoldFailed) {
		_wssp.showTip("取保持失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.ConsultStart) {
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("开始咨询",0);
		if (_wssp.eventHandler.onConsultStart) {
			_wssp.eventHandler.onConsultStart.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.ConsultFailed) {
		_wssp.coInfos = evt.coInfos;
		_wssp.showTip("咨询失败:" + evt.errorCode,1);
		if (_wssp.eventHandler.onConsultFailed) {
			_wssp.eventHandler.onConsultFailed.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.ConsultSucc) {
		_wssp.showTip("咨询成功",0);
		_wssp.coInfos = evt.coInfos;
		if (_wssp.eventHandler.onConsultSucc) {
			_wssp.eventHandler.onConsultSucc.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.ConsultOver) {
		_wssp.showTip("咨询结束",0);
		_wssp.coInfos = evt.coInfos;
		if (_wssp.eventHandler.onConsultOver) {
			_wssp.eventHandler.onConsultOver.call(_wssp, evt.coInfos);
		}
	} else if (evt.event === ESPEvent.CancelConsultSucc) {
		_wssp.showTip("取消咨询成功",0);
	} else if (evt.event === ESPEvent.CancelConsultFailed) {
		_wssp.showTip("取消咨询失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.FinishConsultSucc) {
		_wssp.showTip("结束咨询成功",0);
		//20211110 dwq增加
		if (_wssp.eventHandler.finishConsultSucc) {
			_wssp.eventHandler.finishConsultSucc.call(_wssp,evt.coInfos);
		}
	} else if (evt.event === ESPEvent.FinishConsultFailed) {
		_wssp.showTip("结束咨询失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.ConsultTransferSucc) {
		_wssp.showTip("咨询转接成功",0);
		if (_wssp.eventHandler.onConsultTransferSucc) {
			_wssp.eventHandler.onConsultTransferSucc.call(_wssp,evt.coInfos);
		}
	} else if (evt.event === ESPEvent.ConsultTransferFailed) {
		_wssp.showTip("咨询转接失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.PrepareCallInnerSucc) {
		_wssp.showTip("内呼准备成功",0);
		// 准备成功后，主动刷新内呼列表
		_wssp.refreshCallInnerAgentList();
	} else if (evt.event === ESPEvent.PrepareCallInnerFailed) {
		_wssp.showTip("内呼准备失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.CancelPrepareCallInnerSucc) {
		_wssp.showTip("取消内呼准备成功",0);
	} else if (evt.event === ESPEvent.CancelPrepareCallInnerFailed) {
		_wssp.showTip("取消内呼准备失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.RefreshAgentListSucc) {
		// 转换为Json对象，需要增加括号
		var agentList = eval("(" + evt.params.agentList + ")");
		_wssp.processAgentListEvent(agentList);
	} else if (evt.event === ESPEvent.PrepareMonitorSucc) {
		_wssp.showTip("准备监听成功",0);
		// 准备成功后，主动刷新内呼列表
		_wssp.refreshMonitorAgentList();
	} else if (evt.event === ESPEvent.TransferBegin) {
		_wssp.showTip("开始转接",0);
	} else if (evt.event === ESPEvent.TransferSucc) {
		_wssp.showTip("转接成功",0);
		if (_wssp.eventHandler.onTransferSucc) {
			_wssp.eventHandler.onTransferSucc.call(_wssp,evt.coInfos);
		}

	} else if (evt.event === ESPEvent.TransferFailed) {
		_wssp.showTip("转接失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.OneStepTransferBegin) {
		_wssp.showTip("开始转接",0);
	} else if (evt.event === ESPEvent.OneStepTransferSucc) {
		_wssp.showTip("转接成功",0);
		if (_wssp.eventHandler.onOneStepTransferSucc) {
			_wssp.eventHandler.onOneStepTransferSucc.call(_wssp,evt.coInfos);
		}
	} else if (evt.event === ESPEvent.OneStepTransferFailed) {
		_wssp.showTip("转接失败:" + evt.errorCode,1);
		if (_wssp.eventHandler.onOneStepTransferFailed) {
			_wssp.eventHandler.onOneStepTransferFailed.call(_wssp,
				evt.errorCode,evt.coInfos);
		}
	} else if (evt.event === ESPEvent.IVRCheckReturn) {
		_wssp.showTip("IVR验证返回",0);

		//IVR验证返回，增加自动应答检查
		if(_wssp.canAutoAnswer === true && _wssp.isAutoAnswer === true){
			setTimeout(function() {
				_wssp.trace("autoAnswer...");
				_wssp.answerCall();
			}, 200);
		}

		if (_wssp.eventHandler.onIVRCheckReturn) {
			_wssp.eventHandler.onIVRCheckReturn.call(_wssp);
		}
	} else if (evt.event === ESPEvent.WaitCallOver) {
		_wssp.showTip("IVR验证呼叫释放",0);
		if (_wssp.eventHandler.onWaitCallOver) {
			_wssp.eventHandler.onWaitCallOver.call(_wssp);
		}
	} else if (evt.event === ESPEvent.CancelWaitSucc) {
		_wssp.showTip("取消IVR验证等待成功",0);
	} else if (evt.event === ESPEvent.CancelWaitFailed) {
		_wssp.showTip("取消IVR验证等待失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.WrapupTick) {
		_wssp.showTip("话后完成倒计时:" + evt.params.tick,0);
		//20211027 增加回调
		if (_wssp.eventHandler.onWrapupTick) {
			_wssp.eventHandler.onWrapupTick.call(_wssp,evt.params.tick);
		}
	} else if (evt.event === ESPEvent.FinishWrapupSucc) {
		_wssp.showTip("话后完成成功",0);
		if (_wssp.eventHandler.onFinishWrapupSucc) {
			_wssp.eventHandler.onFinishWrapupSucc.call(_wssp);
		}
	} else if (evt.event === ESPEvent.DropCallSucc) {
		_wssp.showTip("结束命令已发送",0);
	} else if (evt.event === ESPEvent.DropCallFailed) {
		_wssp.showTip("结束通话失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.CancelDialSucc) {
		_wssp.showTip("取消外拨成功",0);
		//2021-10-08 增加取消外拨成功回调
		if (_wssp.eventHandler.onCancelDialSucc) {
			_wssp.eventHandler.onCancelDialSucc.call(_wssp);
		}

	} else if (evt.event === ESPEvent.CancelDialFailed) {
		_wssp.showTip("取消外拨失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.AnswerCallSucc) {
		_wssp.showTip("开始软摘机",0);
	} else if (evt.event === ESPEvent.AnswerCallFailed) {
		_wssp.showTip("软摘机失败:" + evt.errorCode,1);
	} else if (evt.event === ESPEvent.MonitorControlSucc) {
		var tip = "班长控制成功";
		var destAgent = "";
		if (evt.params.destSPName) {
			destAgent = evt.params.destSPName;
		}
		if (evt.params.controlType && evt.params.extraControlParam) {
			tip = destAgent
				+ _wssp.getControlTypeName(evt.params.controlType,
					evt.params.extraControlParam) + "成功";
		}
		_wssp.showTip(tip,0);
		//2021-11-24 增加班长控制成功回调
		if (_wssp.eventHandler.MonitorControlSucc) {
			_wssp.eventHandler.MonitorControlSucc.call(_wssp,tip);
		}
	} else if (evt.event === ESPEvent.MonitorControlFailed) {
		var tip = "班长控制失败:";
		var destAgent = "";
		if (evt.params.destSPName) {
			destAgent = evt.params.destSPName;
		}
		if (evt.params.controlType) {
			tip = destAgent
				+ _wssp.getControlTypeName(evt.params.controlType, "")
				+ "失败：";
		}
		_wssp.showTip(tip + evt.errorCode,1);
		//2021-11-24 增加班长控制失败回调
		if (_wssp.eventHandler.MonitorControlFailed) {
			_wssp.eventHandler.MonitorControlFailed.call(_wssp,tip + evt.errorCode);
		}
	} else if (evt.event === ESPEvent.MonitorContorled) {
		var tip = "本坐席被班长控制";
		var destAgent = "";
		if (evt.params.destSPName) {
			destAgent = evt.params.destSPName;
		}
		if (evt.params.controlType) {
			tip = "本坐席被班长(" + destAgent + ")"
				+ _wssp.getControlTypeName(evt.params.controlType, "");
		}
		_wssp.showTip(tip,1);

		if (_wssp.eventHandler.onMonitorContorled) {
			_wssp.eventHandler.onMonitorContorled.call(_wssp,evt.params);
		}

	} else if (evt.event === ESPEvent.InterceptSucc) {
		_wssp.showTip("强拦成功",0);
		if (_wssp.eventHandler.onInterceptSucc) {
			_wssp.eventHandler.onInterceptSucc.call(_wssp);
		}
	} else if (evt.event === ESPEvent.InterveneSucc) {
		_wssp.showTip("强插成功",0);
		if (_wssp.eventHandler.onInterveneSucc) {
			_wssp.eventHandler.onInterveneSucc.call(_wssp);
		}
	} else if (evt.event === ESPEvent.ForceDropSucc) {
		_wssp.showTip("强拆成功",0);
		if (_wssp.eventHandler.onForceDropSucc) {
			_wssp.eventHandler.onForceDropSucc.call(_wssp);
		}
	}else if(evt.event ==ESPEvent.HBSucc){
		this.writeLog(this.agentId + " HBSucc");
	}else if(evt.event === ESPEvent.EmployeeSettingUpdated){
		var paramKey = evt.params.paramKey;
		var paramValue = evt.params.paramValue;
		if(paramKey === "autoAnswer"){
			_wssp.isAutoAnswer = _wssp.boolean (paramValue);
			if (_wssp.eventHandler.onEmployeeSettingUpdated) {
				_wssp.eventHandler.onEmployeeSettingUpdated.call(_wssp,paramKey,paramValue);
			}
		}
	}else if(evt.event === ESPEvent.HarassConfirm){
		if (_wssp.eventHandler.onHarassConfirm) {
			var checkHarassResult = JSON.parse(evt.params.checkHarassResult);
			_wssp.eventHandler.onHarassConfirm.call(_wssp, checkHarassResult);
		}else{
			//直接禁止骚扰外拨
			_wssp.harassConfirm(0);
		}
	}else if (evt.event === ESPEvent.MonitorBegin) {
		_wssp.showTip("开始监听...",0);
		_wssp.coInfos = evt.coInfos;

		//检查分机是否振铃，振铃则自动接听
		if(_wssp.iptState === 8){
			_wssp.trace("---------------------MonitorBegin and device ring, auto answer");
			_wssp.answerCall();
		}

		//发布事件
		if (_wssp.eventHandler.MonitorBegin) {
			_wssp.eventHandler.MonitorBegin.call(_wssp, evt.coInfos);
		}
	}else if (evt.event === ESPEvent.MonitorSucc) {
		_wssp.showTip("监听成功",0);
		_wssp.coInfos = evt.coInfos;
		//发布事件
		if (_wssp.eventHandler.onMonitorSucc) {
			_wssp.eventHandler.onMonitorSucc.call(_wssp, evt.coInfos);
		}
	}  else if (evt.event === ESPEvent.FinishMonitorSucc) {
		_wssp.showTip("结束监听成功",0);
		//发布事件
		if (_wssp.eventHandler.onFinishMonitorSucc) {
			_wssp.eventHandler.onFinishMonitorSucc.call(_wssp);
		}
	}	else if (evt.event === ESPEvent.NotifyASRResult) {
		_wssp.showTip("ASR通知",0);
		var asrResult =JSON.parse( evt.params.asrResult);
		if (_wssp.eventHandler.onNotifyASRResult) {
			_wssp.eventHandler.onNotifyASRResult.call(_wssp,asrResult);
		}
	} else if (evt.event === ESPEvent.ConfPartyLeave) {
		_wssp.showTip("与会方离开：" + evt.params.partyName,0);
		//发布事件
		if (_wssp.eventHandler.onConfPartyLeave) {
			_wssp.eventHandler.onConfPartyLeave.call(_wssp, evt.params);
		}
	}else if (evt.event === ESPEvent.ConfPartyEnter) {
		_wssp.showTip("与会方进入：" + evt.params.partyName,0);
		//发布事件
		if (_wssp.eventHandler.onConfPartyEnter) {
			_wssp.eventHandler.onConfPartyEnter.call(_wssp, evt.params);
		}
	}else if (evt.event === ESPEvent.ConfPartyChange) {
		_wssp.showTip("与会方更新",0);
		var confInfo = JSON.parse(evt.params.confInfo);
		_wssp.confInfo = confInfo;
		//发布事件
		if (_wssp.eventHandler.onConfPartyChange) {
			_wssp.eventHandler.onConfPartyChange.call(_wssp,confInfo);
		}
	}else if (evt.event === ESPEvent.Blocked) {
		//2021-10-11增加闭塞、闭塞解除、硬件转移失败事件处理
		_wssp.showTip("坐席被闭塞",0);
		//发布事件
		if (_wssp.eventHandler.onBlocked) {
			_wssp.eventHandler.onBlocked.call(_wssp);
		}
	}else if (evt.event === ESPEvent.UnBlocked) {
		_wssp.showTip("坐席闭塞解除",0);
		//发布事件
		if (_wssp.eventHandler.onUnBlocked) {
			_wssp.eventHandler.onUnBlocked.call(_wssp);
		}
	}else if (evt.event === ESPEvent.HardwareTransferFailed) {
		_wssp.showTip("硬件转移失败",1);
		//发布事件
		if (_wssp.eventHandler.onHardwareTransferFailed) {
			_wssp.eventHandler.onHardwareTransferFailed.call(_wssp);
		}
	}


	// 转发给给订阅者进行二次处理
	if (_wssp.onEvent) {
		_wssp.onEvent.call(_wssp, evt);
	}

};



WSSoftPhone.prototype.processEvent_SettingsNotify = function(evt) {
	var _wssp = this;
	_wssp.writeLog("ESPEvent.SettingsNotify");
	// 设置按钮配置，同时触发状态改变来更新UI
	_wssp.stateSettings = eval(evt.params.stateSettings);
	if (!_wssp.workModeSettings) {
		// 未设置自定义工作模式，更新为标准工作模式
		_wssp.workModeSettings = eval(evt.params.workModeSettings);

		//更新工作模式定义
		for (var i = 0; i < this.workModeSettings.length; i++) {
			var wkmd = this.workModeSettings[i];
			if(wkmd.serviceWorkMode>=0 ){
				var cmd = "wsSoftPhoneLocale.AgentWorkMode_" + wkmd.serviceWorkMode + "='"+ wkmd.serviceWorkModeName +"'";
				eval(cmd);
			}
		}

	}
	if (!_wssp.leaveReasonSettings) {
		// 未设置自定义离席原因，更新为标准离席原因
		_wssp.leaveReasonSettings = eval(evt.params.leaveReasonSettings);
	}

	// 转接下拉选项
	this.transferSubItems = eval(evt.params.transferSubItems);
	// 转接IVR
	this.transferIVRInfo = eval(evt.params.transferIVRInfo);
	// 转接坐席技能
	this.transferAgentSkillInfo = eval(evt.params.transferAgentSkillInfo);
	// IVR快捷按钮
	this.ivrButtonSettings = eval(evt.params.ivrButtonSettings);
	//增加外拨主叫池
	this.dialoutANIs = eval(evt.params.dialoutANIs);
	//滚动号码池设置,后台取值为字符串，强制转换为boolean
	this.rollingANI =( evt.params.rollingANI === "true");
	//统一号码池，转换为boolean
	this.unifyANIPool =( evt.params.unifyANIPool === "1");
	this.protocolType = evt.params.protocolType;
	this.iptsIp = evt.params.iptsIp;
	this.iptsPort = evt.params.iptsPort;
	this.iptMinVersion = evt.params.iptMinVersion;
	this.iptCurVersion = evt.params.iptCurVersion;
	this.canAutoAnswer = this.boolean( evt.params.isDigitalDevice);
	this.canSetAutoAnswer =  this.boolean( evt.params.canSetAutoAnswer);
	this.isAutoAnswer =this.boolean(evt.params.isAutoAnswer);

	_wssp.notifySettings();
	_wssp.notifyStateChange(this.agentState);
};


WSSoftPhone.prototype.getControlTypeName = function(controlType, controlParam) {
	// ControlType 0-监听 1、强制签出；2、强制离席；3:强制复席;4：强制话后完成 5:强制设置工作模式
	if (controlType === "1") {
		return "强制签出";
	} else if (controlType === "2") {
		return "强制离席";
	} else if (controlType === "3") {
		return "强制复席";
	} else if (controlType === "4") {
		return "强制完成话后";
	} else if (controlType === "5") {
		return "设置工作模式";
	} else {
		return "未知操作";
	}
};

WSSoftPhone.prototype.processAgentListEvent = function(agentList) {
	var _wssp = this;
	// 统一回调接口
	_wssp.showTip("刷新坐席列表成功",0);
	if (_wssp.eventHandler.onRefreshAgentListSucc) {
		_wssp.eventHandler.onRefreshAgentListSucc(agentList);
	}
	// //按照类型提供不同的回调接口
	// if (agentList.agentListType === EAgentListType.AGENTLISTTYPE_CONSULT) {
	// _wssp.showTip("刷新监控坐席列表成功");
	// if (_wssp.eventHandler.onRefreshConsultAgentListSucc) {
	// _wssp.eventHandler.onRefreshConsultAgentListSucc(agentList);
	// }
	// } else if (agentList.agentListType ==
	// EAgentListType.AGENTLISTTYPE_CALLINNER) {
	// _wssp.showTip("刷新内呼坐席列表成功");
	// if (_wssp.eventHandler.onRefreshCallInnerAgentListSucc) {
	// _wssp.eventHandler.onRefreshCallInnerAgentListSucc(agentList);
	// }
	// } else if (agentList.agentListType ==
	// EAgentListType.AGENTLISTTYPE_MONITOR) {
	// _wssp.showTip("刷新监控坐席列表成功");
	// if (_wssp.eventHandler.onRefreshMonitorAgentListSucc) {
	// _wssp.eventHandler.onRefreshMonitorAgentListSucc(agentList);
	// }
	// } else {
	// _wssp.showTip("刷新坐席列表成功");
	// if (_wssp.eventHandler.onRefreshAgentMsgAgentListSucc) {
	// _wssp.eventHandler.onRefreshAgentMsgAgentListSucc(agentList);
	// }
	// }
};

/**
 * 处理Server响应
 *
 * @param resp
 *            回复响应
 */
WSSoftPhone.prototype.processResponse = function(resp) {
	var _wssp = this;
	if (resp.errorCode != ESPError.Succ) {
		_wssp.writeLog(resp.command + "错误:" + resp.errorCode + "->" + resp.errorDesc);
		_wssp.showTip(resp.command + "错误:" + resp.errorCode+ "->" + resp.errorDesc,2);
		return;
	}
	// 处理个别需要响应的
	if (resp.command === ESPCommand.VerifyTicket) {
		if(resp.datas.verifySuccess){
			_wssp.writeLog("verify succ,call checkWebSocket...");
			_wssp.checkWebSocket();
		}else{
			_wssp.showTip( "软电话Ticket验证失败",2);
		}
	}
	else if (resp.command === ESPCommand.CheckWebSocket) {
		if (!resp.datas.isWebSocketExists) {
			_wssp.init();
		} else {
			if(resp.datas.isAgentIdMatch == "false"){
				//工号不匹配，进行被占用的提示
				_wssp.showTip(resp.datas.tip,2);
				if(_wssp.eventHandler.onCheckWebSocketFailed){
					_wssp.eventHandler.onCheckWebSocketFailed.call(_wssp,resp.datas);
				}
			}else {
				// 提交确认事件
				if (_wssp.eventHandler.onConfirmReplaceWebSocket) {
					_wssp.eventHandler.onConfirmReplaceWebSocket.call(_wssp);
				} else {
					// 直接替换
					_wssp.init();
				}
			}
		}
	} else if (resp.command === ESPCommand.Init) {
		_wssp.delegatorId = resp.datas.delegatorId;
		_wssp.writeLog("init get delegatorId:" + _wssp.delegatorId);
		if( _wssp.ins === INS_WAIT_FOR_DISPATCH){
			_wssp.ins = resp.agentInfos.ins;
			_wssp.writeLog("init get dispatch ins:" + _wssp.ins);
		}

		_wssp.writeLog(JSON.stringify(resp.datas));

		if(resp.agentInfos && resp.agentInfos.localIp){
			_wssp.wsServerIp = resp.agentInfos.localIp;
		}

		_wssp.agentState = resp.datas.agentState;
		if (_wssp.agentState === "0") {
			_wssp.agentState = "1";
		}
		_wssp.agentWorkMode = resp.datas.agentWorkMode;
		_wssp.trace("init state:" + _wssp.agentState);

		//2020-01-04 在init中获取leaveReason
		_wssp.leaveReason = resp.datas.leaveReason;

		//保存自动应答
		_wssp.canSetAutoAnswer = _wssp.boolean(resp.agentInfos.canSetAutoAnswer);
		_wssp.isAutoAnswer = _wssp.boolean(resp.agentInfos.isAutoAnswer);

		if( _wssp.agentState != "1" && _wssp.agentState !="0"){
			_wssp.isLogon = true;

			_wssp.checkMonitor(resp.agentInfos);
			_wssp.checkKeepLogon(resp.agentInfos);
			_wssp.checkAutoAnswer(resp.agentInfos);

			if(resp.coInfos){
				_wssp.coInfos = resp.coInfos;
				_wssp.trace("init with coInfo");
				//alert( JSON.stringify(_wssp.coInfos));
			}

			//2021-6-18 振铃状态，检测是否自动应答
			if(_wssp.agentState === "5"){
				if(_wssp.canAutoAnswer === true && _wssp.isAutoAnswer === true){
					setTimeout(function() {
						_wssp.trace("autoAnswer in init response...");
						_wssp.answerCall();
					}, 200);
				}
			}
		}else{
			_wssp.isLogon = false;
		}

		if (_wssp.eventHandler.onInited) {
			_wssp.eventHandler.onInited.call(_wssp, _wssp.delegatorId);
		}
		_wssp.notifyStateChange(this.agentState);

		//判断是否有队列信息
		if(resp.datas.queueList){
			_wssp.queueList = eval(resp.datas.queueList);
			if (_wssp.eventHandler.onQueueChange) {
				_wssp.eventHandler.onQueueChange.call(_wssp, _wssp.queueList);
			}
		}else if( resp.datas.skQueueInfo){
			_wssp.skQueueInfo = JSON.parse(resp.datas.skQueueInfo);
			if (_wssp.eventHandler.onSkillQueueChange) {
				_wssp.eventHandler.onSkillQueueChange.call(_wssp, _wssp.skQueueInfo);
			}
		}

		_wssp.checkKeepLogon(resp.agentInfos);

	} else if (resp.command === ESPCommand.Logon) {
		_wssp.delegatorId = resp.datas.delegatorId;
		_wssp.trace("logon get delegatorId:" + _wssp.delegatorId);
	} else if (resp.command === ESPCommand.RebindSocket) {
		_wssp.trace("RebindSocket 响应！！！");
	} else if (resp.command === ESPCommand.SetFunctionSwitch) {
		if (resp.errorCode === ESPError.Succ) {
			// 返回正确
			if (resp.datas && resp.datas.functionCode && resp.datas.isOn) {
				_wssp.notifyFunctionSwitch(resp.datas.functionCode, true,
					resp.datas.isOn === "true");
				_wssp.showTip("设置功能开关成功",0);
			}
		} else {
			_wssp.showTip("设置功能开关失败(" + resp.errorCode + "):" + resp.errorDesc,2);
		}
	} else if (resp.command === ESPCommand.WSHello) {
		_wssp.onWSHello(resp);
	}

};

/**
 * 关闭WebSocket连接
 */
WSSoftPhone.prototype.close = function() {
	if(this.ws){
		try {
			this.ws.close();
			this.ws = null;
		}
		catch(err){
			this.ws = null;
			this.writeLog(err);
		}
	}
};
/**
 * 判断WebSocket状态
 *
 * @returns {Boolean} 正常返回true
 */
WSSoftPhone.prototype.isReady = function() {
	if (this.ws && this.ws.readyState === this.ws.OPEN) {
		return true;
	} else {
		this.trace("websocket not ready");
		return false;
	}
};


/**
 * 检查ticket，确认授权正确，再继续后续任务处理
 */
WSSoftPhone.prototype.verifyTicket = function() {
	this.writeLog(this.agentId + " checkWebSocket");
	var cmd = new SPCommand(this, ESPCommand.VerifyTicket);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams("cgiTicket",this.cgiTicket);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
}

/**
 * 检查连接,确认是否有已存在连接
 */
WSSoftPhone.prototype.checkWebSocket = function() {
	this.writeLog(this.agentId + " checkWebSocket");
	var cmd = new SPCommand(this, ESPCommand.CheckWebSocket);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams("agentName", this.agentName);
	cmd.pushParams("delegatorId", this.delegatorId);
	cmd.pushParams("spObjectId", this.spObjectId);
	cmd.pushParams("computerId", this.computerId);
	cmd.pushParams("cgiTicket",this.cgiTicket);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 初始化，连接Server后获取初始化信息
 */
WSSoftPhone.prototype.init = function() {
	this.writeLog(this.agentId + " init");
	var cmd = new SPCommand(this, ESPCommand.Init);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.agentName, this.agentName);
	cmd.pushParams(ESPAttr.agentWorkMode, this.agentWorkMode);
	cmd.pushParams(ESPAttr.delegatorId, this.delegatorId);
	cmd.pushParams("spObjectId", this.spObjectId);
	cmd.pushParams("computerId", this.computerId);

	// 设置初始化参数
	if (this.customInitParams) {
		cmd.pushParams(ESPAttr.customInitParams, JSON
			.stringify(this.customInitParams));
	}
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 签入Server
 */
WSSoftPhone.prototype.logon = function() {
	this.writeLog(this.agentId + " logon");
	var cmd = new SPCommand(this, ESPCommand.Logon);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams("agentName", this.agentName);
	cmd.pushParams("agentWorkMode", this.agentWorkMode);
	cmd.pushParams("computerId", this.computerId);
	cmd.pushParams(ESPAttr.softPhoneCode,  this.softPhoneCode);
	// 设置初始化参数
	if (this.customInitParams) {
		cmd.pushParams(ESPAttr.customInitParams, JSON
			.stringify(this.customInitParams));
	}
	// alert(cmd.toString());
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 签出Server
 */
WSSoftPhone.prototype.logoff = function() {

	if(this.logoffCheck === true){

		switch(parseInt(this.agentState)){
			case ESPState.AGENTSTATE_IDLE :
			case ESPState.AGENTSTATE_INITIAL :
			case ESPState.AGENTSTATE_INITIALPHONE:
			case ESPState.AGENTSTATE_NODEV_ONLINE:
			case ESPState.AGENTSTATE_LEAVE:
			case ESPState.AGENTSTATE_WRAPUP:
			case ESPState.AGENTSTATE_DIALINPUTNUM:
			case ESPState.AGENTSTATE_MONITORSELECTAGENT:
			case ESPState.AGENTSTATE_INNERDIALSELECTAGENT:
			case ESPState.AGENTSTATE_INNERDIALS:
				//没有通话，允许签出
				break;
			default:
				this.showTip("当前有服务的通话，不允许签出",1);
				this.writeLog(this.agentId + "can't logoff! curState =" + this.agentState);
				return;
		}
	}

	this.writeLog(this.agentId + " logoff!");

	var cmd = new SPCommand(this, ESPCommand.Logoff);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	// alert(cmd.toString());
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}

};

/**
 * 设置工作模式
 *
 * @param wkmd
 *            目标工作模式
 */
WSSoftPhone.prototype.setWorkMode = function(wkmd) {
	this.trace(this.agentId + " setWorkMode " + wkmd);
	this.agentWorkMode = wkmd;
	//判断是否已经登录
	if( this.agentState === "0" || this.agentState === "1"){
		//未登录，进行工作模式预设
		this.eventHandler.onWorkModeChange.call(this, this.agentWorkMode);
		return;
	}

	//发送工作模式设置
	var cmd = new SPCommand(this, ESPCommand.SetWorkMode);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams("workMode", wkmd);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 调用指定功能按钮
 *
 * @param funcId
 *            功能值
 */
WSSoftPhone.prototype.callSPFunction = function(funcId) {
	var _wssp = this;
	_wssp.writeLog(_wssp.agentId + " callSPFunction(" + funcId + ")");
	switch (funcId) {
		case ESPFunc.SPFunc_LogOn:
			_wssp.logon();
			break;
		case ESPFunc.SPFunc_LogOff:
			_wssp.logoff();
			break;
		case ESPFunc.SPFunc_DialOut:
			_wssp.prepareDial();
			break;
		case ESPFunc.SPFunc_CancelPrepareDialing:
			_wssp.cancelPrepareDial();
			break;
		case ESPFunc.SPFunc_CancelDialout:
			_wssp.cancelDial();
			break;
		case ESPFunc.SPFunc_DropCall:
			_wssp.dropCall();
			break;
		case ESPFunc.SPFunc_FinishWrapup:
			_wssp.finishWrapup();
			break;
		case ESPFunc.SPFunc_Leave:
			_wssp.leave();
			break;
		case ESPFunc.SPFunc_Resume:
			_wssp.resumeWork();
			break;
		case ESPFunc.SPFunc_Hold:
			_wssp.hold();
			break;
		case ESPFunc.SPFunc_Unhold:
			_wssp.fetchHold();
			break;
		case ESPFunc.SPFunc_Consult:
			_wssp.prepareConsult();
			break;
		case ESPFunc.SPFunc_CancelConsult:
			_wssp.cancelConsult();
			break;
		case ESPFunc.SPFunc_FinishConsult:
		case ESPFunc.SPFunc_FinishCallout:
			_wssp.finishConsult();
			break;
		case ESPFunc.SPFunc_ConsultTransfer:
		case ESPFunc.SPFunc_CalloutTransfer:
			_wssp.consultTransfer();
			break;
		case ESPFunc.SPFunc_CallOut:
			_wssp.prepareCallout();
			break;
		case ESPFunc.SPFunc_CallInner:
			_wssp.prepareCallInner();
			break;
		case ESPFunc.SPFunc_CancelPrepareCallInner:
			_wssp.cancelPrepareCallInner();
			break;
		case ESPFunc.SPFunc_CancelCallInner:
			_wssp.cancelCallInner();
			break;
		case ESPFunc.SPFunc_Monitor:
			_wssp.prepareMonitor();
			break;
		case ESPFunc.SPFunc_CancelPrepareMonitor:
			_wssp.cancelPrepareMonitor();
			break;
		case ESPFunc.SPFunc_FinishMonitor:
			_wssp.finishMonitor();
			break;
		case ESPFunc.SPFunc_ConsultConf:
		case ESPFunc.SPFunc_CalloutConf:
			_wssp.consultConf();
			break;
		case ESPFunc.SPFunc_HoldConsult:
		case ESPFunc.SPFunc_HoldOutLine:
			_wssp.consultSwap();
			break;
		case ESPFunc.SPFunc_FetchConsult:
		case ESPFunc.SPFunc_FetchOutline:
			_wssp.consultSwapBack();
			break;
		case ESPFunc.SPFunc_FinishConf:
			_wssp.dropConf();
			break;
		case ESPFunc.SPFunc_CancelWait:
			_wssp.cancelWait();
			break;
		case ESPFunc.SPFunc_SoftHookOff:
			_wssp.answerCall();
			break;
		case ESPFunc.SPFunc_AgentMsg:
		case ESPFunc.SPFunc_AgentMsgOn:
			// _wssp.prepareMonitorContorl();
			_wssp.agentMsg();
			break;
		case ESPFunc.SPFunc_MonitorControl:
			_wssp.prepareMonitorContorl();
			break;
		case ESPFunc.SPFunc_Intercept:
			_wssp.intercept();
			break;
		case ESPFunc.SPFunc_Intervene:
			_wssp.intervene();
			break;
		case ESPFunc.SPFunc_ForceDrop:
			_wssp.forceDrop();
			break;
	}
};

/**
 * 离席
 */
WSSoftPhone.prototype.leave = function(reason) {
	if (!reason) {
		reason = "0";
	}
	this.leaveReason = reason;
	this.trace(this.agentId + " leave:" + reason);
	var cmd = new SPCommand(this, ESPCommand.Leave);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams("leaveReason", reason);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 复席
 */
WSSoftPhone.prototype.resumeWork = function() {
	this.trace(this.agentId + " resumeWork");
	var cmd = new SPCommand(this, ESPCommand.ResumeWork);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 保持
 */
WSSoftPhone.prototype.hold = function() {
	this.trace(this.agentId + " hold");
	var cmd = new SPCommand(this, ESPCommand.Hold);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 取保持
 */
WSSoftPhone.prototype.fetchHold = function() {
	this.trace(this.agentId + " hold");
	var cmd = new SPCommand(this, ESPCommand.FetchHold);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 外拨准备
 */
WSSoftPhone.prototype.prepareDial = function() {
	this.trace(this.agentId + " prepareDial");
	var cmd = new SPCommand(this, ESPCommand.PrepareDial);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};
/**
 * 取消外拨准备
 */
WSSoftPhone.prototype.cancelPrepareDial = function() {
	this.trace(this.agentId + " cancelPrepareDial");
	var cmd = new SPCommand(this, ESPCommand.CancelPrepareDial);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};




/**
 * 外拨
 *
 * @param phoneNum 用户号码，需要带呼叫中心的出局码
 * @param ani 动态主叫号码，仅系统支持动态主叫时可用，而且必须在系统的主叫号码范围内，否则会导致外拨失败
 * @param sourceTag (可选)呼叫来源，记入COLOG
 * @param serviceParam1 (可选)业务参数，长度50，记入COLOG
 * @param serviceParam2 (可选)业务参数，长度50，记入COLOG
 * @param useDateRollAni (可选)是否使用按天滚动的主叫，取值为1代表使用
 */
WSSoftPhone.prototype.dial = function(phoneNum,ani,sourceTag,serviceParam1,serviceParam2,useDateRollAni) {
	this.trace(this.agentId + " dial");
	if(!this.checkPhoneNum(phoneNum)){
		this.showTip("号码" + phoneNum + "无效或者包含非法字符",1);
		return;
	}

	//判断是否给坐席分配了主叫号码池，如果是，强行设置主叫号码
	ani = this.checkDefaultDialAni(ani);

	// if (ani !== null && ani !== undefined && ani !== '') {
	// 	if(!this.checkPhoneNum(ani)){
	// 		this.showTip("主叫号码" + ani + "无效或者包含非法字符",1);
	// 		return;
	// 	}
	// }


	var cmd = new SPCommand(this, ESPCommand.Dial);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.phoneNum, phoneNum);

	//判断可选参数的存在并加入cmd
	if (this.isNotBlank(sourceTag)) {
		cmd.pushParams(ESPAttr.sourceTag, sourceTag);
	}
	if (this.isNotBlank(serviceParam1)) {
		cmd.pushParams(ESPAttr.serviceParam1, serviceParam1);
	}
	if (this.isNotBlank(serviceParam2)) {
		cmd.pushParams(ESPAttr.serviceParam2, serviceParam2);
	}
	if(this.isNotBlank(useDateRollAni)){
		cmd.pushParams(ESPAttr.useDateRollAni, useDateRollAni);
	}

	//仅系统支持动态主叫时可用，而且必须在系统的主叫号码范围内，否则会导致外拨失败
	if(ani){
		cmd.pushParams("ani", ani);
		this.rollANIMark = true;
	}
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};


//判断是否给坐席分配了主叫号码池，如果是，则主叫号码必须设置，为空则从号码池中获取默认值
WSSoftPhone.prototype.checkDefaultDialAni = function(ani){
	var anis = this.dialoutANIs;
	var defaultAni = ani;
	if(anis ==null || anis.length ==0){
		//坐席没有分配主叫号码池，直接返回
		return defaultAni;
	}

	if (this.isNotBlank(ani)) {
		//已经设定主叫，直接返回
		return defaultAni;
	}

	//默认为第一个
	defaultAni = anis[0];
	for(var i = 0 ;i< anis.length;i++){
		var ani = anis[i];
		if(ani.isDefault === 1){
			//获取到缺省值
			defaultAni = anis[i].ani;
			break;
		}
	}
	//返回
	return defaultAni;
};

/**
 * 加密外拨
 * @param prefix 外拨前缀，由于业务系统加密的号码一般不带出局码，可以添加明文的prefix协助外拨
 * @param phoneNum 加密的电话号码
 * @param encryptMode 加密模式，与EEncryptMode对应
 * @param ani 主叫号码，系统可指定动态主叫时使用
 * @param sourceTag (可选)呼叫来源，记入COLOG
 * @param serviceParam1 (可选)业务参数，长度50，记入COLOG
 * @param serviceParam2 (可选)业务参数，长度50，记入COLOG
 */
WSSoftPhone.prototype.encryptDial = function(prefix,phoneNum,encryptMode, ani,sourceTag,serviceParam1,serviceParam2) {
	this.trace(this.agentId + " encryptDial");
	var cmd = new SPCommand(this, ESPCommand.EncryptDial);
	cmd.ins = this.ins;
	if(prefix){
		cmd.pushParams(ESPAttr.prefix,prefix);
	}
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.phoneNum, phoneNum);
	cmd.pushParams(ESPAttr.encryptMode, encryptMode);

	//判断可选参数的存在并加入cmd
	if (this.isNotBlank(sourceTag)) {
		cmd.pushParams(ESPAttr.sourceTag, sourceTag);
	}
	if (this.isNotBlank(serviceParam1)){
		cmd.pushParams(ESPAttr.serviceParam1, serviceParam1);
	}
	if (this.isNotBlank(serviceParam2)) {
		cmd.pushParams(ESPAttr.serviceParam2, serviceParam2);
	}

	//仅系统支持动态主叫时可用，而且必须在系统的主叫号码范围内，否则会导致外拨失败
	if(ani){
		cmd.pushParams(ESPAttr.ani, ani);
	}
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};




/**
 * 取消外拨
 */
WSSoftPhone.prototype.cancelDial = function() {
	this.trace(this.agentId + " cancelDial");
	var cmd = new SPCommand(this, ESPCommand.CancelDial);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};
/**
 * 挂机
 */
WSSoftPhone.prototype.dropCall = function() {
	this.trace(this.agentId + " dropCall");
	var cmd = new SPCommand(this, ESPCommand.DropCall);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 完成话后处理
 */
WSSoftPhone.prototype.finishWrapup = function() {
	this.trace(this.agentId + " finishWrapup");
	var cmd = new SPCommand(this, ESPCommand.FinishWrapup);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 内部咨询准备，刷新列表
 */
WSSoftPhone.prototype.prepareConsult = function() {
	this.trace(this.agentId + " prepareConsult");
	var cmd = new SPCommand(this, ESPCommand.RefreshAgentList);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams(ESPAttr.agentListType, EAgentListType.AGENTLISTTYPE_CONSULT);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 内部咨询
 */
WSSoftPhone.prototype.consult = function(destSPType, destSPIns) {
	this.trace(this.agentId + " consult(" + destSPType + ":" + destSPIns + ")");
	var cmd = new SPCommand(this, ESPCommand.Consult);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.destSPType, destSPType);
	cmd.pushParams(ESPAttr.destSPIns, destSPIns);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 取消咨询
 */
WSSoftPhone.prototype.cancelConsult = function() {
	this.trace(this.agentId + " cancelConsult()");
	var cmd = new SPCommand(this, ESPCommand.CancelConsult);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 结束咨询
 */
WSSoftPhone.prototype.finishConsult = function() {
	this.trace(this.agentId + " finishConsult()");
	var cmd = new SPCommand(this, ESPCommand.FinishConsult);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 咨询转接
 */
WSSoftPhone.prototype.consultTransfer = function() {
	this.trace(this.agentId + " consultTransfer()");
	var cmd = new SPCommand(this, ESPCommand.ConsultTransfer);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.prepareCallout = function() {
	var _wssp = this;
	_wssp.showTip("请输入外部咨询号码",0);
	if (_wssp.eventHandler.onPrepareCallOutSucc) {
		_wssp.eventHandler.onPrepareCallOutSucc();
	} else {
		var phoneNum = prompt("请输入外部咨询号码", "");
		if (phoneNum != null && phoneNum != "") {
			_wssp.callout(phoneNum);
		} else {
			_wssp.showTip("外部咨询取消",0);
		}
	}
};

WSSoftPhone.prototype.callout = function(phoneNum,ani) {
	var _wssp = this;
	this.trace(this.agentId + " callout(" + phoneNum + ":" + ani + ")");

	if(!this.checkPhoneNum(phoneNum)){
		this.showTip("号码" + phoneNum + "包含非法字符",1);
		return;
	}
	var cmd = new SPCommand(this, ESPCommand.Consult);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.destSPType, "OUT");
	cmd.pushParams(ESPAttr.destSPIns, phoneNum);
	if(ani && ani !=""){
		if(!this.checkPhoneNum(ani)){
			this.showTip("主叫" + ani + "包含非法字符",1);
			return;
		}
		//指定主叫号码
		cmd.pushParams(ESPAttr.ani,ani);
		_wssp.showTip("外部咨询" + phoneNum + ",主叫" + ani + "...",0);
	}else{
		_wssp.showTip("外部咨询" + phoneNum + "...",0);
	}
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.prepareCallInner = function() {
	this.trace(this.agentId + " prepareCallInner()");
	var cmd = new SPCommand(this, ESPCommand.PrepareCallInner);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.cancelPrepareCallInner = function() {
	this.trace(this.agentId + " cancelPrepareCallInner()");
	var cmd = new SPCommand(this, ESPCommand.CancelPrepareCallInner);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.refreshCallInnerAgentList = function() {
	this.trace(this.agentId + " refreshCallInnerAgentList");
	var cmd = new SPCommand(this, ESPCommand.RefreshAgentList);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams(ESPAttr.agentListType,
		EAgentListType.AGENTLISTTYPE_CALLINNER);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.refreshTransferAgentList = function() {
	this.trace(this.agentId + " refreshOneSteptransferAgentList");
	var cmd = new SPCommand(this, ESPCommand.RefreshAgentList);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd
		.pushParams(ESPAttr.agentListType,
			EAgentListType.AGENTLISTTYPE_TRANSFER);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.callInner = function(destSPType, destSPIns) {
	this.trace(this.agentId + " callInner(" + destSPType + ":" + destSPIns
		+ ")");
	var cmd = new SPCommand(this, ESPCommand.CallInner);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.destSPType, destSPType);
	cmd.pushParams(ESPAttr.destSPIns, destSPIns);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.cancelCallInner = function() {
	this.trace(this.agentId + " cancelCallInner()");
	var cmd = new SPCommand(this, ESPCommand.CancelCallInner);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.prepareMonitor = function() {
	this.trace(this.agentId + " prepareMonitor");
	var cmd = new SPCommand(this, ESPCommand.PrepareMonitor);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.cancelPrepareMonitor = function() {
	this.trace(this.agentId + " cancelPrepareMonitor");
	var cmd = new SPCommand(this, ESPCommand.CancelPrepareMonitor);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.refreshMonitorAgentList = function() {
	this.trace(this.agentId + " refreshMonitorAgentList");
	var cmd = new SPCommand(this, ESPCommand.RefreshAgentList);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.agentListType, EAgentListType.AGENTLISTTYPE_MONITOR);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.monitorAgent = function(destSPType, destSPIns) {
	this.trace(this.agentId + " monitorAgent(" + destSPType + ":" + destSPIns
		+ ")");
	var cmd = new SPCommand(this, ESPCommand.MonitorAgent);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.destSPType, destSPType);
	cmd.pushParams(ESPAttr.destSPIns, destSPIns);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.finishMonitor = function() {
	this.trace(this.agentId + " finishMonitor()");
	var cmd = new SPCommand(this, ESPCommand.FinishMonitor);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.consultConf = function() {
	this.trace(this.agentId + " consultConf()");
	var cmd = new SPCommand(this, ESPCommand.ConsultConf);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};
WSSoftPhone.prototype.consultSwap = function() {
	this.trace(this.agentId + " consultSwap()");
	var cmd = new SPCommand(this, ESPCommand.ConsultSwap);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.consultSwapBack = function() {
	this.trace(this.agentId + " consultSwapBack()");
	var cmd = new SPCommand(this, ESPCommand.ConsultSwapBack);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.dropConf = function() {
	this.trace(this.agentId + " dropConf()");
	var cmd = new SPCommand(this, ESPCommand.DropConf);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.cancelWait = function() {
	this.trace(this.agentId + " cancelWait()");
	var cmd = new SPCommand(this, ESPCommand.CancelWait);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.transferToIVR = function(ivrinfo) {

	// SPTransferIVRInfo
	this.trace(this.agentId + " transferToIVR()");
	var cmd = new SPCommand(this, ESPCommand.TransferToIVR);
	cmd.ins = this.ins;
	// cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams("SVN", ivrinfo.svn);
	cmd.pushParams("TRANSFERPREPARE_CHECKPASSWORD", ivrinfo.checkPassword);
	if (ivrinfo.params) {
		for ( var key in ivrinfo.params) {
			if (typeof (ivrinfo.params[key]) != " function ") {
				cmd.pushParams(key, ivrinfo.params[key]);
			}
		}
	}

	// 追加通过setCOInfo设置的信息
	if (this.tempCOInfos) {
		for ( var key in this.tempCOInfos) {
			if (typeof (this.tempCOInfos[key]) != " function ") {
				cmd.pushParams(key, this.tempCOInfos[key]);
			}
		}
	}

	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.answerCall = function() {
	this.trace(this.agentId + " answerCall()");
	var cmd = new SPCommand(this, ESPCommand.AnswerCall);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};
WSSoftPhone.prototype.agentMsg = function() {
	this.trace(this.agentId + " agentMsg()");
	var cmd = new SPCommand(this, ESPCommand.RefreshAgentList);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams(ESPAttr.agentListType,
		EAgentListType.AGENTLISTTYPE_AGENTMSG);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.prepareMonitorContorl = function() {
	this.trace(this.agentId + " prepareMonitorContorl()");
	var cmd = new SPCommand(this, ESPCommand.RefreshAgentList);
	cmd.ins = this.ins;
	cmd.pushParams("agentId", this.agentId);
	cmd.pushParams(ESPAttr.agentListType,
		EAgentListType.AGENTLISTTYPE_MONITORCONTROL);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.monitorControl = function(controlType, destIns,
												extraParam) {
	// 'controlType 0-监听 1、强制签出；2、强制离席；3:强制复席;4：强制话后完成 5:强制设置工作模式
	this.trace(this.agentId + " monitorControl(" + controlType + "," + destIns
		+ "," + extraParam + ")");
	if (controlType === "0") {
		this.monitorAgent("AGT", destIns);
	} else {
		var cmd = new SPCommand(this, ESPCommand.MonitorControl);
		cmd.ins = this.ins;
		cmd.pushParams(ESPAttr.agentId, this.agentId);
		cmd.pushParams(ESPAttr.destSPType, "AGT");
		cmd.pushParams(ESPAttr.destSPIns, destIns);
		cmd.pushParams(ESPAttr.controlType, controlType);
		cmd.pushParams(ESPAttr.extraControlParam, extraParam);
		if (this.isReady()) {
			this.ws.send(cmd.toString());
		}
	}
};

WSSoftPhone.prototype.intercept = function() {
	this.trace(this.agentId + " intercept()");
	var cmd = new SPCommand(this, ESPCommand.Intercept);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.intervene = function() {
	this.trace(this.agentId + " intervene()");
	var cmd = new SPCommand(this, ESPCommand.Intervence);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.forceDrop = function() {
	this.trace(this.agentId + " forceDrop()");
	var cmd = new SPCommand(this, ESPCommand.ForceDrop);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.oneStepTransfer = function(transferInfo) {
	// SPTransferIVRInfo
	this.trace(this.agentId + " oneStepTransfer()");
	var cmd = new SPCommand(this, ESPCommand.OneStepTransfer);
	cmd.ins = this.ins;
	cmd.pushParams("destType", transferInfo.destType);
	cmd.pushParams("destIns", transferInfo.destIns);
	if (transferInfo.params) {
		for ( var key in transferInfo.params) {
			if (typeof (transferInfo.params[key]) != " function ") {
				cmd.pushParams(key, transferInfo.params[key]);
			}
		}
	}

	// 追加通过setCOInfo设置的信息
	if (this.tempCOInfos) {
		for ( var key in this.tempCOInfos) {
			if (typeof (this.tempCOInfos[key]) != " function ") {
				cmd.pushParams(key, this.tempCOInfos[key]);
			}
		}
	}

	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.wsHello = function(helloInfo) {
	// SPTransferIVRInfo
	var myDate = new Date();// 获取系统当前时间
	this.trace(this.agentId + " wsHello()  time:" + myDate.toLocaleString());
	var cmd = new SPCommand(this, ESPCommand.WSHello);
	cmd.ins = this.ins;
	cmd.pushParams("helloInfo", helloInfo);
	cmd.pushParams("time", myDate.toLocaleString());
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

/**
 * 向创建者提交跟踪日志
 *
 * @param info
 *            日志信息
 */
WSSoftPhone.prototype.trace = function(info) {
	var _wssp = this;
	info = this.getTimeString() + "->" + info;
	if (this.eventHandler && this.eventHandler.onTrace) {
		this.eventHandler.onTrace.call(_wssp, info);
	}
};


WSSoftPhone.prototype.getTimeString = function(){
	var date = new Date();
	return date.getHours() + ":" + date.getMinutes() + ":" + date.getSeconds();
};

/**
 * 向创建者显示提示
 *
 * @param info 提示信息
 * @param type 0成功信息 1失败信息 2错误信息
 *
 */
WSSoftPhone.prototype.showTip = function(info,type) {
	var _wssp = this;
	if(type === undefined){
		type = 0;
	}
	if (this.eventHandler && this.eventHandler.onShowTip) {
		this.eventHandler.onShowTip.call(_wssp, info,type);
	}
};

/**
 * 向创建者通知事件
 *
 * @param evt
 *            事件
 */
WSSoftPhone.prototype.onEvent = function(evt) {
	var _wssp = this;
	if (this.eventHandler && this.eventHandler.onEvent) {
		this.eventHandler.onEvent.call(_wssp, evt);
	}
};

WSSoftPhone.prototype.notifyStateChange = function(agentState) {
	var _wssp = this;
	_wssp.writeLog("onStateChange(" + agentState + ")");
	if (this.eventHandler && this.eventHandler.onStateChange) {
		if(agentState === 1){
			console.log('************************WSSoftPhone onStateChange(1)');
		}
		this.eventHandler.onStateChange.call(_wssp, agentState);
	}
};

WSSoftPhone.prototype.notifySettings = function() {
	var _wssp = this;
	_wssp.writeLog("onNotifySettings()");
	if (this.eventHandler && this.eventHandler.onNotifySettings) {
		this.eventHandler.onNotifySettings.call(_wssp);
	}
};

WSSoftPhone.prototype.getCurStateName = function() {
	return this.getAgentStateName();
	//return getWSSoftPhoneText("AgentState_" + this.agentState);
};

WSSoftPhone.prototype.getCurWorkModeName = function() {
	for (var i = 0; i < this.workModeSettings.length; i++) {
		var wkmd = this.workModeSettings[i];
		if (wkmd.serviceWorkMode == this.agentWorkMode) {
			return wkmd.serviceWorkModeName;
		}
	}
	return "未知模式";
};

WSSoftPhone.prototype.getCoInfos = function() {
	return this.coInfos;
};

WSSoftPhone.prototype.getQueueList = function() {
	return this.queueList;
};

WSSoftPhone.prototype.setCOInfo = function(key, value) {
	this.tempCOInfos[key] = value;
};

WSSoftPhone.prototype.checkKeepLogon = function(agentInfos) {
	if (agentInfos.canKeepLogon && agentInfos.canKeepLogon === "true") {
		this.canKeepLogon = true;
		if (agentInfos.isKeepLogon) {
			this.isKeepLogon = (agentInfos.isKeepLogon === "true");
		}
	} else {
		this.canKeepLogon = false;
		this.isKeepLogon = false;
	}
	this.notifyFunctionSwitch("KeepLogon", this.canKeepLogon, this.isKeepLogon);
};

WSSoftPhone.prototype.checkMonitor = function(agentInfos) {
	this.isMonitor = false;
	this.monitorDepts = "";
	if (agentInfos) {
		this.isMonitor = ("true" === agentInfos.isMonitor);
		if (agentInfos.monitorDepts) {
			this.monitorDepts = agentInfos.monitorDepts;
		}
	}
};

WSSoftPhone.prototype.checkAutoAnswer = function(agentInfos) {
	if (agentInfos) {
		this.canAutoAnswer = this.boolean( agentInfos.isDigitalDevice);
	}
};

WSSoftPhone.prototype.notifyFunctionSwitch = function(functionCode, isSupport,
													  isOn) {
	var _wssp = this;
	if (functionCode != "KeepLogon") {
		return;
	}
	if (this.eventHandler && this.eventHandler.onFunctionSwitch) {
		this.eventHandler.onFunctionSwitch.call(_wssp, functionCode, isSupport,
			isOn);
	}
};

WSSoftPhone.prototype.setFunctionSwitch = function(functionCode, isOn) {
	//var _wssp = this;
	if (functionCode != "KeepLogon") {
		return;
	}

	this.writeLog(this.agentId + " setFunctionSwitch(" + functionCode + ","
		+ isOn + ")");
	var cmd = new SPCommand(this, ESPCommand.SetFunctionSwitch);
	cmd.ins = this.ins;
	cmd.pushParams("functionCode", functionCode);
	cmd.pushParams("isOn", isOn);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.onWSHello = function(resp) {
	var myDate = new Date();
	this.trace(this.agentId + " onWSHello() time:" + myDate.toLocaleString());
	console.log("onWSHello() time:" + myDate.toLocaleString())
	var _wssp = this;
	if (_wssp.eventHandler.onWSHello) {
		_wssp.eventHandler.onWSHello(resp);
	}
};

WSSoftPhone.prototype.writeLog = function(info) {
	this.trace(info);
	if (this.Logger) {
		this.Logger.writeLog(info);
	}
};

WSSoftPhone.prototype.getAgentStateName = function(st, lvrs) {
	if (!st) {
		st = this.agentState;
	}

	var stateName = getStateName(st) ;

	if (st === 23) {
		// 判断离席原因
		if( !lvrs){
			lvrs = this.leaveReason ;
		}
		stateName = stateName + "(" + this.getLeaveReasonName(lvrs)
			+ ")";
	}

	return stateName;

};

WSSoftPhone.prototype.getLeaveReasonName = function(lvrs) {
	for (var i = 0; i < this.leaveReasonSettings.length; i++) {
		var leavereason = this.leaveReasonSettings[i];
		if (leavereason.reasonId === lvrs) {
			return leavereason.reasonName;
		}
	}
	// 没有匹配，返回未知
	return "未知" + lvrs;
};

WSSoftPhone.prototype.getAgentWorkModeName = function(varwkmd) {
	if (!varwkmd) {
		varwkmd = this.agentWorkMode;
	}

	for (var i = 0; i < this.workModeSettings.length; i++) {
		var wkmd = this.workModeSettings[i];
		if (wkmd.serviceWorkMode === varwkmd) {
			return wkmd.serviceWorkModeName;
		}
	}
	// 没有匹配，返回缺省值
	return getWorkModeName(varwkmd);
};


WSSoftPhone.prototype.closeIpt = function(needLogoff) {
	var _wssp = this;
	if( _wssp.wsipt){
		try {
			if(needLogoff && needLogoff === true){
				_wssp.iptLogoff();
			}

			_wssp.ignoreIptClose = true;
			_wssp.wsipt.close();
			_wssp.wsipt = null;
			//复位状态
			_wssp.iptState = "0";
			_wssp.isMute = false;
			_wssp.notifyIptStateChange(this.iptState,this.isMute);

		}
		catch(err){
			this.wsipt = null;
			this.writeLog(err);
		}
	}
};


WSSoftPhone.prototype.connectIpt = function(ipturl) {
	var _wssp = this;
	this.iptHearBeatTime = new Date();
	//默认IPTService的WebSocket端口，可根据实际情况调整
	_wssp.wsipt = new WebSocket("ws://127.0.0.1:8687");
	_wssp.wsipt.onopen = function() {
		// 复位连接标志
		_wssp.writeLog("WebSocket#onopen-IPT WebSocket已连接");
		_wssp.showTip("语音通道连接成功！",0);
		if (_wssp.eventHandler.onIptWSOpen) {
			_wssp.eventHandler.onIptWSOpen.call(_wssp);
		}
		_wssp.iptLogon();
	};
	_wssp.wsipt.onmessage = function(e) {
		if (e.data && e.data != "") {
			// 判断为消息对象
			if (e.data.startsWith("{")) {
				var msg = JSON.parse(e.data);
				if (msg.objectClass) {
					if (msg.objectClass === "SPEvent") {
						_wssp.processIptEvent(msg);
					} else if (msg.objectClass === "SPResponse") {
						_wssp.processIptResponse(msg);
					} else {
						_wssp.trace("unknow msg:" + e.data);
					}
				} else {
					_wssp.eventHandler.onTrace(_wssp, e.data);
				}
			}
		}
	};

	_wssp.wsipt.onclose = function() {
		_wssp.writeLog("WebSocket#onclose IPT WebSocket已关闭！");
		_wssp.showTip("语音通道已关闭！",0);
		_wssp.wsipt = null;
		_wssp.iptState = "0";
		_wssp.isMute = false;

		if(_wssp.ignoreIptClose === true){
			_wssp.writeLog("ignoreIptClose, reset ignoreIptClose = false");
			_wssp.ignoreIptClose = false;
		}else{
			if (_wssp.eventHandler.onIptWSClose) {
				_wssp.eventHandler.onIptWSClose.call(_wssp);
			}
		}
		// 补充上报连接失败事件
		if (_wssp.isConnectingServer) {
			_wssp.isConnectingServer = false;
			if (_wssp.eventHandler.onIptWSConnectFailed) {
				_wssp.showTip("连接语音通道失败！",0);
				_wssp.eventHandler.onIptWSConnectFailed.call(_wssp);
			}
		}
	};
};



/**
 * 判断WebSocket状态
 *
 * @returns {Boolean} 正常返回true
 */
WSSoftPhone.prototype.iptIsReady = function() {
	if (this.wsipt && this.wsipt.readyState === this.wsipt.OPEN) {
		return true;
	} else {
		this.trace("ipt websocket no ready");
		return false;
	}
};

WSSoftPhone.prototype.iptLogon= function() {
	this.trace(this.agentId + " initIpt()");
	var cmd = new SPCommand(this, ESPCommand.IPTDeviceLogon);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.devNum, this.ins);
	cmd.pushParams(ESPAttr.hbValue, 60);
	cmd.pushParams(ESPAttr.protocolType, this.protocolType);
	cmd.pushParams(ESPAttr.iptsIp, this.iptsIp);
	cmd.pushParams(ESPAttr.iptsPort, parseInt( this.iptsPort));


	if (this.iptIsReady()) {
		this.wsipt.send(cmd.toString());
	}
};

WSSoftPhone.prototype.iptReLogon = function(){
	this.trace(this.agentId + " initIpt()");
	var cmd = new SPCommand(this, ESPCommand.IPTDeviceReLogon);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.devNum, this.ins);
	cmd.pushParams(ESPAttr.hbValue, 60);
	cmd.pushParams(ESPAttr.protocolType, this.protocolType);
	cmd.pushParams(ESPAttr.iptsIp, this.iptsIp);
	cmd.pushParams(ESPAttr.iptsPort, parseInt( this.iptsPort));

	if (this.iptIsReady()) {
		this.wsipt.send(cmd.toString());
	}
};

WSSoftPhone.prototype.iptMute = function(isMute){
	this.trace(this.agentId + " iptMute("+isMute+")");
	var intMute = 0;
	if( isMute  && isMute === true)
		intMute = 1;
	var cmd = new SPCommand(this, ESPCommand.IPTDeviceMute);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.devNum, this.ins);
	cmd.pushParams(ESPAttr.hbValue, 60);
	cmd.pushParams(ESPAttr.isMute, intMute);
	if (this.iptIsReady()) {
		this.wsipt.send(cmd.toString());
	}
};


WSSoftPhone.prototype.iptLogoff = function(){
	this.trace(this.agentId + " iptLogoff()");
	var cmd = new SPCommand(this, ESPCommand.IPTDeviceLogoff);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.devNum, this.ins);
	cmd.pushParams(ESPAttr.hbValue, 60);
	if (this.iptIsReady()) {
		this.wsipt.send(cmd.toString());
	}
};

WSSoftPhone.prototype.iptHeartBeat = function(hbInfo) {
	var _wssp= this;
	if(this.agentState === "0" || this.agentState === "1"){
		this.writeLog(this.agentId + " not logon,ignore iptHeartBeat(" + hbInfo+") ");
		return ;
	}

	if (this.iptIsReady()) {
		this.writeLog(this.agentId + " iptHeartBeat(" + hbInfo+") ");
		var cmd = new SPCommand(this, ESPCommand.IPTDeviceHeartBeat);
		cmd.ins = this.ins;
		cmd.pushParams(ESPAttr.devNum, this.ins);
		cmd.pushParams(ESPAttr.hbValue, 60);
		cmd.pushParams("hbInfo", hbInfo);
		cmd.pushParams("time",  this.getCurDateString());
		this.wsipt.send(cmd.toString());
	}else{
		this.writeLog(this.agentId + " iptHeartBeat(" + hbInfo+") not ready,igonre");
	}

	//无论是否连接，都进行心跳检测
	if(new Date().getTime() - this.iptHearBeatTime.getTime() > 15*1000){
		this.writeLog("hb failed > 15,raise onIptWSClose");
		//更新心跳时间，避免连续重新初始化
		this.iptHearBeatTime = new Date();
		if(this.wsipt !=null){
			this.wsipt =null;
		}
		//上报ipt断开事件
		if (_wssp.eventHandler.onIptWSClose) {
			_wssp.eventHandler.onIptWSClose.call(_wssp);
		}
	}


};

WSSoftPhone.prototype.getCurDateString=function(){
	var myDate = new Date();// 获取系统当前时间
	var strDate =   myDate.getFullYear() + "-" + (myDate.getMonth() + 1) + "-" +   myDate.getDate() ;
	strDate += " " +  myDate.getHours() + ":" + myDate.getMinutes() + ":" +  myDate.getSeconds();
	return strDate;
};


WSSoftPhone.prototype.iptDeviceMute = function(isMute){
	this.trace(this.agentId + " iptDeviceMute(" + isMute + ")");
	var paramIsMute = 0;
	if( isMute === "true" || isMute === "1") {
		paramIsMute = 1;
	}
	var cmd = new SPCommand(this, ESPCommand.IPTDeviceMute);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.devNum, this.ins);
	cmd.pushParams(ESPAttr.isMute, paramIsMute);
	if (this.iptIsReady()) {
		this.wsipt.send(cmd.toString());
	}
};


WSSoftPhone.prototype.iptDeviceDtmf = function(dtmf){
	this.trace(this.agentId + " iptDeviceDtmf(" + dtmf + ")");

	var cmd = new SPCommand(this, ESPCommand.IPTDeviceDtmf);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.devNum, this.ins);
	cmd.pushParams(ESPAttr.dtmfValue, dtmf);
	if (this.iptIsReady()) {
		this.wsipt.send(cmd.toString());
	}
};


WSSoftPhone.prototype.processIptResponse = function(resp) {
	var _wssp = this;

	if (resp.ins != this.ins) {
		this.trace("------------Ins not match!!!!");
		return;
	}
	this.trace("processIptResponse:" + resp.command);

	if (resp.errorCode != ESPError.Succ) {
		_wssp.writeLog(resp.command + "错误:" + resp.errorCode);
		_wssp.showTip(resp.command + "错误:" + resp.errorCode,2);
		return;
	}

	// 处理个别需要响应的
	if( resp.command === ESPCommand.IPTDeviceHeartBeat){
		//更新心跳时间
		this.iptHearBeatTime = new Date();
		//var st =  response.params.newState;
	}else if (resp.command === ESPCommand.IPTDeviceLogon) {

	} else if ( resp.command === ESPCommand.IPTDeviceMute){
		//增加Mute回复的消息处理
		var isMute = resp.params.isMute;
		_wssp.isMute = ( isMute === 1);
		_wssp.notifyIptStateChange(this.iptState,this.isMute);
	}

};

WSSoftPhone.prototype.processIptEvent = function(evt) {
	var _wssp = this;
	if (evt.ins != this.ins) {
		this.trace("------------Ins not match!!!!");
		return;
	}

	this.trace("processIptEvent:" + evt.event);
	if (evt.event === ESPEvent.IPTDeviceStateChange) {
		var oldst = evt.params.oldState;
		var st = evt.params.newState;
		var isMute = evt.params.isMute;
		this.trace("------------------IPTDeviceStateChange " + oldst + "->" + st + ",isMute=" + isMute);
		_wssp.iptState = st;
		_wssp.isMute = ( isMute === 1);
		_wssp.notifyIptStateChange(this.iptState,this.isMute);
	}else if (evt.event === ESPEvent.IPTDeviceLogonSucc) {
		this.trace("-------------IPTDeviceLogonSucc");
		if (_wssp.eventHandler.onIPTDeviceLogonSucc) {
			_wssp.eventHandler.onIPTDeviceLogonSucc.call(_wssp, evt.ins);
		}
	}else if (evt.event === ESPEvent.IPTDeviceLogonFailed) {
		this.trace("------------IPTDeviceLogonFailed");
		if (_wssp.eventHandler.onIPTDeviceLogonFailed) {
			_wssp.eventHandler.onIPTDeviceLogonFailed.call(_wssp, evt.ins);
		}
	}
};

WSSoftPhone.prototype.notifyIptStateChange = function(iptState,isMute) {
	var _wssp = this;
	_wssp.trace("---------------------onIptStateChange(" + iptState + "," + isMute+")");
	if (this.eventHandler && this.eventHandler.onIptStateChange) {
		this.eventHandler.onIptStateChange.call(_wssp, iptState,isMute);
	}

	//判断是否为监听准备的振铃状态，如果是，则调用应答
	if(iptState === 8 && _wssp.agentState === "16" ){
		setTimeout(function() {
			_wssp.trace("---------------------monitor prepair ring, auto answer");
			_wssp.answerCall();
		}, 200);
	}

	//判断是否为监听中的振铃状态，如果是，则调用应答 （通过班长控制，不进入监听准备状态）
	if(iptState === 8 && _wssp.agentState === "17" ){
		setTimeout(function() {
			_wssp.trace("---------------------monitor prepair ring, auto answer");
			_wssp.answerCall();
		}, 200);
	}
};


WSSoftPhone.prototype.getCurIptStateName = function(){
	var stateName = getDeviceCallStateName(this.iptState) ;
	return stateName;
};


/**
 * 设置自动应答
 * @param autoAnswer 自动应答
 */
WSSoftPhone.prototype.setAutoAnswer = function(autoAnswer){
	var _wssp = this;
	if(autoAnswer === undefined ){
		return;
	}

	if(!_wssp.canSetAutoAnswer){
		_wssp.showTip("系统不允许修改自动应答参数",1);
		return;
	}

	autoAnswer = this.boolean(autoAnswer);

	if( _wssp.isAutoAnswer != autoAnswer){
		_wssp.isAutoAnswer = autoAnswer;

		//通知后台进行更新
		this.writeLog(_wssp.agentId + " set autoAnswer =" + autoAnswer );
		var cmd = new SPCommand(_wssp, ESPCommand.UpdateEmployeeSetting);
		cmd.pushParams(ESPAttr.agentId, _wssp.agentId);
		cmd.pushParams(ESPAttr.paramKey, "autoAnswer");
		cmd.pushParams(ESPAttr.paramValue, _wssp.isAutoAnswer);
		if (this.isReady()) {
			this.ws.send(cmd.toString());
		}
	}
};


/**
 * 更新循环号码池标志
 * 在用户勾选或者取消"循环号码池"时调用
 * @param rollingANI 循环标志
 */
WSSoftPhone.prototype.setRollingANI = function(rollingANI){
	var _wssp = this;
	if(rollingANI === undefined ){
		return;
	}

	if( _wssp.rollingANI != rollingANI){
		_wssp.rollingANI = rollingANI;

		//通知后台进行更新
		this.writeLog(_wssp.agentId + " set rollingANI =" + rollingANI );
		var cmd = new SPCommand(_wssp, ESPCommand.UpdateEmployeeSetting);
		cmd.pushParams(ESPAttr.agentId, _wssp.agentId);
		cmd.pushParams(ESPAttr.paramKey, 	"rollingANI");
		cmd.pushParams(ESPAttr.paramValue, _wssp.rollingANI);
		if (this.isReady()) {
			this.ws.send(cmd.toString());
		}
	}
};


/**
 * 滚动主叫号码
 * 通常坐席状态变为空闲时触发滚动
 * 在勾选了循环号码池的情况下，函数会自动滚动到下一个主叫，并在后台保存为缺省主叫
 * @param forceRoll 是否强制滚动，为true时不检查rollingANI标识
 */
WSSoftPhone.prototype.rollANI = function(forceRoll){
	var _wssp = this;

	//设定了循环主叫号码池，且之前进行指定主叫外拨设置了滚动标志
	//或者强制滚动
	if(( _wssp.rollingANI === true && _wssp.rollANIMark === true)
		|| forceRoll === true)
	{
		//复位滚动标志
		_wssp.rollANIMark = false;
		//遍历主叫，将下一个主叫isDefault设置为true
		var anis = _wssp.dialoutANIs;
		var nextIndex = 0;
		for(var i = 0 ;i< anis.length;i++){
			var ani = anis[i];
			if(ani.isDefault === 1){
				ani.isDefault = 0;
				if(i < anis.length -1 ){
					//否则保持为0
					nextIndex = i +1;
				}
				break;
			}
		}
		anis[nextIndex].isDefault = 1;
		var newAni = anis[nextIndex].ani;
		//更新后台的默认主叫
		this.updateDefaultANI(newAni);
		this.writeLog(_wssp.agentId + " rollANI to " +newAni);

		if (_wssp.eventHandler.onAniRolled) {
			_wssp.eventHandler.onAniRolled.call(_wssp,newAni);
		}

		return anis[nextIndex].ani;
	}
};

/**
 * 更新默认的滚动主叫号码，同时更新坐席的默认主叫
 * @param aniValue
 */
WSSoftPhone.prototype.setDefaultRollANI = function(aniValue){
	var _wssp = this;
	//遍历主叫，将与aniValue相等的记录设置为isDefault=1,同时调用_wssp更新缺少主叫
	var anis = _wssp.dialoutANIs;

	for(var i = 0 ;i< anis.length;i++){
		var ani = anis[i];
		if(ani.ani === aniValue){
			ani.isDefault = 1;
		}else if(ani.isDefault === 1){
			ani.isDefault = 0;
		}
	}
	this.updateDefaultANI(aniValue);
};


/**
 * 更新坐席默认主叫
 * 通常在非滚动号码池的情况下，手动选择主叫号码进行外拨时进行更新
 * @param defaultANI
 */
WSSoftPhone.prototype.updateDefaultANI = function(defaultANI){
	var _wssp = this;
	//通知后台进行更新
	this.writeLog(_wssp.agentId + " defaultANI =" +defaultANI);
	var cmd = new SPCommand(_wssp, ESPCommand.UpdateEmployeeSetting);
	cmd.pushParams(ESPAttr.agentId, _wssp.agentId);
	cmd.pushParams(ESPAttr.paramKey, 	"defaultANI");
	cmd.pushParams(ESPAttr.paramValue, defaultANI);
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};

WSSoftPhone.prototype.guid = function(){
	var s = [];
	var hexDigits = "0123456789abcdef";
	for (var i = 0; i < 36; i++) {
		s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
	}
	s[14] = "4";  // bits 12-15 of the time_hi_and_version field to 0010
	s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);  // bits 6-7 of the clock_seq_hi_and_reserved to 01
	s[8] = s[13] = s[18] = s[23] = "-";

	var uuid = s.join("");
	return uuid;
};

WSSoftPhone.prototype.boolean = function(value){
	if("true" === value || "True" === value || "1" === value || true === value || 1 === value){
		return true;
	}
	return false;
};

WSSoftPhone.prototype.checkPhoneNum = function(phoneNum){
	//数字或者- 括号
	var reg=/^[0-9-()（）]{3,32}$/;
	return reg.test(phoneNum);
};

/**
 * 防骚扰确认
 * isExecuteDial：是否执行外拨,取值为0或者1
 */
WSSoftPhone.prototype.harassConfirm = function(isExecuteDial) {
	var cmd = new SPCommand(this, ESPCommand.HarassConfirm);
	cmd.ins = this.ins;
	cmd.pushParams(ESPAttr.agentId, this.agentId);
	cmd.pushParams(ESPAttr.isExecuteDial, isExecuteDial);
	if(isExecuteDial === 1){
		this.showTip("继续执行外拨...",0);
	}else{
		this.showTip("取消外拨",0);
	}
	if (this.isReady()) {
		this.ws.send(cmd.toString());
	}
};


/**
 * 判断参数是否为空
 * @param info
 * @returns {boolean}
 */
WSSoftPhone.prototype.isNotBlank = function(info){
	if (info !== null && info !== undefined && info !== '') {
		return true;
	}
	return false;
};