package com.vedeng.erp.saleorder.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.vedeng.erp.saleorder.domain.SaleorderGoodsMaoliBuyprice;
import com.vedeng.erp.saleorder.dao.SaleorderGoodsMaoliBuypriceMapper;
import com.vedeng.erp.saleorder.service.SaleorderGoodsMaoliBuypriceService;
/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/11/8
 */
@Service
public class SaleorderGoodsMaoliBuypriceServiceImpl implements SaleorderGoodsMaoliBuypriceService{

    @Autowired
    private SaleorderGoodsMaoliBuypriceMapper saleorderGoodsMaoliBuypriceMapper;

    @Override
    public int deleteByPrimaryKey(Integer saleorderGoodsMaoliBuypriceId) {
        return saleorderGoodsMaoliBuypriceMapper.deleteByPrimaryKey(saleorderGoodsMaoliBuypriceId);
    }

    @Override
    public int insert(SaleorderGoodsMaoliBuyprice record) {
        return saleorderGoodsMaoliBuypriceMapper.insert(record);
    }

    @Override
    public int insertSelective(SaleorderGoodsMaoliBuyprice record) {
        return saleorderGoodsMaoliBuypriceMapper.insertSelective(record);
    }

    @Override
    public SaleorderGoodsMaoliBuyprice selectByPrimaryKey(Integer saleorderGoodsMaoliBuypriceId) {
        return saleorderGoodsMaoliBuypriceMapper.selectByPrimaryKey(saleorderGoodsMaoliBuypriceId);
    }

    @Override
    public int updateByPrimaryKeySelective(SaleorderGoodsMaoliBuyprice record) {
        return saleorderGoodsMaoliBuypriceMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SaleorderGoodsMaoliBuyprice record) {
        return saleorderGoodsMaoliBuypriceMapper.updateByPrimaryKey(record);
    }

}
