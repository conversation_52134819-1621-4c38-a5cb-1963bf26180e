package com.vedeng.erp.trader.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.service.UserApiService;
import com.vedeng.erp.trader.domain.entity.RSalesJTraderLogEntity;
import com.vedeng.erp.trader.dto.RSalesJTraderDto;
import com.vedeng.erp.trader.domain.entity.RSalesJTraderEntity;
import com.vedeng.erp.trader.dto.TraderBatchShareDto;
import com.vedeng.erp.trader.mapper.RSalesJTraderLogMapper;
import com.vedeng.erp.trader.mapper.RSalesJTraderMapper;
import com.vedeng.erp.trader.mapstruct.RSalesJTraderConvertor;
import com.vedeng.erp.trader.mapstruct.RSalesJTraderLogConvertor;
import com.vedeng.erp.trader.service.RSalesJTraderApiService;
import com.vedeng.erp.trader.service.RSalesJTraderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/4 15:41
 */
@Service
@Slf4j
public class RSalesJTraderServiceImpl implements RSalesJTraderService, RSalesJTraderApiService {

    @Autowired
    private RSalesJTraderMapper rSalesJTraderMapper;

    @Autowired
    private RSalesJTraderConvertor rSalesJTraderConvertor;

    @Autowired
    private RSalesJTraderLogConvertor rSalesJTraderLogConvertor;

    @Autowired
    private RSalesJTraderLogMapper salesJTraderLogMapper;

    @Autowired
    private UserApiService userApiService;

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void saveTraderShare(RSalesJTraderDto rSalesJtraderDto) {
        RSalesJTraderEntity salesJoinTraderEntity = rSalesJTraderConvertor.toEntity(rSalesJtraderDto);
        rSalesJTraderMapper.insertSelective(salesJoinTraderEntity);

        RSalesJTraderLogEntity logEntity = rSalesJTraderLogConvertor.to(salesJoinTraderEntity);
        salesJTraderLogMapper.insertSelective(logEntity);
    }

    @Override
    public List<RSalesJTraderDto> getShareTraderList(Integer traderId) {
        return rSalesJTraderMapper.getByTraderId(traderId);
    }

    @Override
    public RSalesJTraderDto getShareTraderByUserId(Integer traderId,Integer userId) {
        return rSalesJTraderMapper.getByTraderIdAndUserId(traderId,userId);
    }

    @Override
    public List<Integer> getShareAndBelongTrader(List<Integer> userIds, List<Integer> traderIds) {
        List<Integer> traderIdList = rSalesJTraderMapper.getShareAndBelongTrader(userIds,traderIds);
        if (CollectionUtil.isEmpty(traderIdList)){
            return new ArrayList<>();
        }
        return traderIdList;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int saveCancelShare(Integer id) {
        RSalesJTraderEntity entity = rSalesJTraderMapper.selectByPrimaryKey(id);
        if(entity == null ){
            return 0;
        }

        RSalesJTraderEntity exist = rSalesJTraderMapper.selectByPrimaryKey(id);
        RSalesJTraderLogEntity deleteEntity =rSalesJTraderLogConvertor.to(exist);
        deleteEntity.setOperateType("delete");
        log.info("取消分享客户存入日志表: {}", JSONObject.toJSONString(deleteEntity));
        salesJTraderLogMapper.insertSelective(deleteEntity);

        log.info("取消分享客户删除原记录，id：{}", id);
        rSalesJTraderMapper.deleteByPrimaryKey(id);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public int saveBatchShare(TraderBatchShareDto dto){
//        Integer userId=  dto.getUSERID();
//        String userName = dto.getUSERNAME();
        Integer belongUserId = dto.getBelongSalesId();
        List<RSalesJTraderEntity> resultList = rSalesJTraderMapper.queryShareCustomerListByUserId(dto);

        UserDto belongUser =  userApiService.getUserById(belongUserId);
        if(CollectionUtil.isNotEmpty(resultList)){
            for(RSalesJTraderEntity entity:resultList){
                entity.setSaleUserId(belongUserId);
                entity.setSaleUserName(belongUser.getUsername());
                RSalesJTraderLogEntity logEntity = new RSalesJTraderLogEntity();
                BeanUtil.copyProperties(entity,logEntity );
                rSalesJTraderMapper.insertSelective(entity);
                salesJTraderLogMapper.insertSelective(logEntity);
            }
        }
        return CollectionUtil.size(resultList);
    }

    @Override
    public List<Map<String,Object>> queryShardUserList(Integer userId){
        List<Map<String,Object>> resultList = rSalesJTraderMapper.queryShardUserList(userId);
        return resultList;

    }

    @Override
    public List<RSalesJTraderDto> getBySaleUserAndRegion(Integer userId, Integer regionId) {
        List<RSalesJTraderDto> list = rSalesJTraderMapper.getBySaleUserAndRegion(userId, regionId);
        return list;
    }


}
