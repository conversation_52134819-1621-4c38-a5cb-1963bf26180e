package com.vedeng.esign.manager.esign;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.base.api.dto.reqParam.BusinessInfo;
import com.vedeng.common.core.exception.ServiceException;
import com.google.common.collect.Maps;
import com.vedeng.base.api.dto.reqParam.FileInfo;
import com.vedeng.base.api.dto.reqParam.SignCallbackDto;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.esign.service.ImgAndPdfService;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.model.FirstEngage;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.infrastructure.esign.domain.ElectronicSignParam;
import com.vedeng.infrastructure.esign.enums.VendorCertificateEnums;
import com.vedeng.infrastructure.esign.handle.AbstractElectronicSignHandle;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 厂商证书e签宝实现类
 * @date 2021/12/7 9:26
 */
@Component
@Slf4j
public class ManufacturerElectronicSignHandle extends AbstractElectronicSignHandle {

    public static final String ATTACHMENT_TYPE = "attachmentType";
    public static final String ATTACHMENT_FUNCTION = "attachmentFunction";
    public static final String REGISTRATION_NUMBER_ID = "registrationNumberId";
    public static final String USER_ID = "userId";
    public static final String CERTIFICATE_TYPE = "certificateType";
    public static final String MANUFACTURER_ID = "manufacturerId";
    public static final String FIRST_ENGAGE_ID = "firstEngageId";

    private static final String FILENAME_PREFIX = "厂商三证";

    @Resource
    private AttachmentMapper attachmentMapper;
    @Resource
    private FirstEngageMapper firstEngageMapper;
    @Resource
    private ManufacturerMapper manufacturerMapper;
    @Autowired
    private ImgAndPdfService imgAndPdfService;


    @Override
    protected void electronicSignFailCompensate(String error, ElectronicSignParam electronicSignParam) {
        log.error("厂商资质异步签章任务失败日志[{},{}]" , error, JSON.toJSONString(electronicSignParam));
        JSONObject jsonObject = JSON.parseObject(electronicSignParam.getBusinessInfo().getThirdData());
        Integer userId = jsonObject.getInteger(USER_ID);
        Integer certificateType = jsonObject.getInteger(CERTIFICATE_TYPE);
        Integer manufacturerId = jsonObject.getInteger(MANUFACTURER_ID);
        Integer firstEngageId = jsonObject.getInteger(FIRST_ENGAGE_ID);
        Integer registrationNumberId = jsonObject.getInteger(REGISTRATION_NUMBER_ID);
        // 处理产品注册的证
        if (certificateType == CommonConstants.ATTACHMENT_FUNCTION_1301.intValue()) {
            if (!Objects.isNull(firstEngageId) && !Objects.isNull(registrationNumberId)) {
                // 处理，回滚签章状态
                FirstEngage update = new FirstEngage();
                update.setFirstEngageId(firstEngageId);
                update.setSignature(2);
                update.setUpdater(userId);
                firstEngageMapper.updateByPrimaryKeySelective(update);

            }
        } else {
            // 处理厂家信息
            if (!Objects.isNull(manufacturerId)) {
                Manufacturer manufacturer = new Manufacturer();
                manufacturer.setManufacturerId(manufacturerId);
                manufacturer.setSignature(2);
                manufacturerMapper.updateByPrimaryKeySelective(manufacturer);
            }

        }
    }

    @Override
    protected void preMqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入厂商实际消费业务类前置处理器,编号{}" , signCallbackDto.getOrderNo());
        AtomicInteger retryCount = new AtomicInteger(0);
        String fileName = FILENAME_PREFIX + signCallbackDto.getOrderNo();
        log.info("消费-》oss保存文件名：{}" , fileName);
        this.saveFile2Oss(signCallbackDto, retryCount, fileName);
    }

    @Override
    protected void dealWithByMqException(String errorMsg, SignCallbackDto signCallbackDto) {
        log.error("厂商资质异步签章任务失败日志[{},{}]" , errorMsg, JSON.toJSONString(signCallbackDto));
        JSONObject jsonObject = JSON.parseObject(signCallbackDto.getThirdData());
        Integer userId = jsonObject.getInteger(USER_ID);
        Integer certificateType = jsonObject.getInteger(CERTIFICATE_TYPE);
        Integer manufacturerId = jsonObject.getInteger(MANUFACTURER_ID);
        Integer firstEngageId = jsonObject.getInteger(FIRST_ENGAGE_ID);
        Integer registrationNumberId = jsonObject.getInteger(REGISTRATION_NUMBER_ID);
        // 处理产品注册的证
        if (certificateType == CommonConstants.ATTACHMENT_FUNCTION_1301.intValue()) {
            if (!Objects.isNull(firstEngageId) && !Objects.isNull(registrationNumberId)) {
                // 处理，回滚签章状态
                FirstEngage update = new FirstEngage();
                update.setFirstEngageId(firstEngageId);
                update.setSignature(2);
                update.setUpdater(userId);
                firstEngageMapper.updateByPrimaryKeySelective(update);

            }
        } else {
            // 处理厂家信息
            if (!Objects.isNull(manufacturerId)) {
                Manufacturer manufacturer = new Manufacturer();
                manufacturer.setManufacturerId(manufacturerId);
                manufacturer.setSignature(2);
                manufacturerMapper.updateByPrimaryKeySelective(manufacturer);
            }

        }
    }

    @Override
    protected FileInfo toPdfGetFile(ElectronicSignParam electronicSignParam) {

        if (null == electronicSignParam.getVendorId()) {
            log.error("必要参数vendorId为空！");
            throw new ServiceException("必要参数vendorId为空！");
        }

        // 查询对应的图片
        Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
        paramMap.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
        List<Integer> attachmentFunction = Collections.singletonList(electronicSignParam.getVendorCertificateEnums().getType());
        paramMap.put(ATTACHMENT_FUNCTION, attachmentFunction);
        if (electronicSignParam.getVendorCertificateEnums().getType() != VendorCertificateEnums.REGISTRATION_CERTIFICATE.getType().intValue()) {
            paramMap.put(REGISTRATION_NUMBER_ID, electronicSignParam.getVendorId());
        } else {
            FirstEngage firstEngage = firstEngageMapper.selectByPrimaryKey(electronicSignParam.getVendorId());
            paramMap.put(REGISTRATION_NUMBER_ID, firstEngage.getRegistrationNumberId());
        }

        //查询新附件类型
        List<Attachment> undeletedAttachmentsList = attachmentMapper.getUndeletedAttachmentsList(paramMap);

        // 图片转pdf
        String fileName = UUID.randomUUID().toString().replace("-" , "");
        FileInfo fileInfo = new FileInfo();
        if (!CollectionUtils.isEmpty(undeletedAttachmentsList)) {
            String urlPath = imgAndPdfService.convertImg2Pdf(undeletedAttachmentsList, fileName);
            if (!StringUtils.isEmpty(urlPath)) {
                fileInfo.setFilePath(urlPath);
                fileInfo.setFileName(fileName + ".pdf");
                fileInfo.setTotalPage(undeletedAttachmentsList.size());
            }
        }
        return fileInfo;
    }

    @Override
    public ElectronicSignParam buildElectronicSignParam(Integer buyorderId, BusinessInfo businessInfo) {
        return null;
    }

    @Override
    protected void electronicSignSuccessCompensate(ElectronicSignParam electronicSignParam) {

    }

    @Override
    protected void mqProcessors(SignCallbackDto signCallbackDto) {
        log.info("进入厂商三证实际消费业务类,编号{}" , signCallbackDto.getOrderNo());
        log.info("e签宝返回信息：{}" , JSON.toJSONString(signCallbackDto));
        String thirdData = signCallbackDto.getThirdData();
        String fileUrl = signCallbackDto.getFileUrl();
        if (StringUtils.isEmpty(fileUrl)) {
            return;
        }
        // 获取必要信息
        JSONObject jsonObject = JSONObject.parseObject(thirdData);
        Integer userId = jsonObject.getInteger(USER_ID);
        Integer certificateType = jsonObject.getInteger(CERTIFICATE_TYPE);
        Integer manufacturerId = jsonObject.getInteger(MANUFACTURER_ID);
        Integer firstEngageId = jsonObject.getInteger(FIRST_ENGAGE_ID);

        Integer registrationNumberId = jsonObject.getInteger(REGISTRATION_NUMBER_ID);
        if (Objects.isNull(certificateType)) {
            log.error("电子签章回传的必要信息不全");
            return;
        }
        // 处理产品注册的证
        if (certificateType == CommonConstants.ATTACHMENT_FUNCTION_1301.intValue()) {
            if (!Objects.isNull(firstEngageId) && !Objects.isNull(registrationNumberId)) {
                //处理，之前的删除，加入新的
                Map<String, Object> paramMapOld = new HashMap<>(4);
                // 附件类型
                paramMapOld.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
                List<Integer> attachmentFunctionB = new ArrayList<>(1);
                // 注册证附件/备案凭证附件（贝）
                attachmentFunctionB.add(CommonConstants.ATTACHMENT_FUNCTION_1301);
                paramMapOld.put(ATTACHMENT_FUNCTION, attachmentFunctionB);
                paramMapOld.put(REGISTRATION_NUMBER_ID, registrationNumberId);
                List<Attachment> attachmentsB = attachmentMapper.getAttachmentsList(paramMapOld);
                // 文件对象
                Map<String, String> fileKey = new HashMap<>(2);
                fileKey.put(CommonConstants.ATTACHMENT_FUNCTION_1301.toString(), fileUrl);
                // 处理，之前的删除，加入新的
                log.info("更新参数：firstEngageId:{},registrationNumberId:{},userId:{},oldAttachment:{},fileKey:{},isManufacturer{}" , firstEngageId, registrationNumberId, userId, attachmentsB, fileKey, false);
                imgAndPdfService.updateBCertificates(firstEngageId, registrationNumberId, userId, attachmentsB, fileKey, false);

            }
        } else {
            // 处理厂家信息
            if (!Objects.isNull(manufacturerId)) {
                Map<String, Object> paramMap = Maps.newHashMapWithExpectedSize(3);
                paramMap.put(ATTACHMENT_TYPE, CommonConstants.ATTACHMENT_TYPE_974);
                List<Integer> attachmentFunctionB = new ArrayList<>();
                // 注册证附件/备案凭证附件（贝）
                attachmentFunctionB.add(certificateType);
                paramMap.put(ATTACHMENT_FUNCTION, attachmentFunctionB);
                paramMap.put(REGISTRATION_NUMBER_ID, manufacturerId);
                List<Attachment> old = attachmentMapper.getUndeletedAttachmentsList(paramMap);
                // 文件对象
                Map<String, String> fileKey = new HashMap<>(2);
                fileKey.put(certificateType.toString(), fileUrl);
                // 处理，之前的删除，加入新的
                log.info("更新参数：firstEngageId:{},relatedId:{},userId:{},oldAttachment:{},fileKey:{},isManufacturer{}" , null, manufacturerId, userId, old, fileKey, false);
                imgAndPdfService.updateBCertificates(null, manufacturerId, userId, old, fileKey, true);
            }

        }

    }

    @Override
    protected void postMqProcessors(SignCallbackDto signCallbackDto) {

    }

}
