<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.VBuyorderSncodeSlaveMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.ge.VBuyorderSncodeSlave">
    <id column="BUYORDER_SNCODE_SLAVE_ID" jdbcType="INTEGER" property="buyorderSncodeSlaveId" />
    <result column="BUYORDER_SNCODE_MASTER_ID" jdbcType="INTEGER" property="buyorderSncodeMasterId" />
    <result column="SLAVE_SNCODE" jdbcType="VARCHAR" property="slaveSncode" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    BUYORDER_SNCODE_SLAVE_ID, BUYORDER_SNCODE_MASTER_ID, SLAVE_SNCODE, ADD_TIME, MODE_TIME, 
    IS_DELETE, CREATOR, UPDATER
  </sql>

  <select id="getGeSlaveInfo" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT
			A.SLAVE_SNCODE
		FROM V_BUYORDER_SNCODE_SLAVE A
		WHERE
			  A.BUYORDER_SNCODE_MASTER_ID = #{MasterId,jdbcType=INTEGER}
    </select>

  <select id="checkSlaveSno"  resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
		FROM
		    V_BUYORDER_SNCODE_SLAVE A
		WHERE
			A.SLAVE_SNCODE = #{slaveSno,jdbcType=VARCHAR}
	    <if test="masterSnoCodeList != null and masterSnoCodeList.size() &gt; 0">
          and
          A.BUYORDER_SNCODE_MASTER_ID not in
          <foreach collection="masterSnoCodeList" index="index" item="item" open="(" separator="," close=")">
            #{item}
          </foreach>
        </if>
    </select>

  <delete id="delGeSlaveInfoCollection" parameterType="java.lang.Integer">
    delete from V_BUYORDER_SNCODE_SLAVE
    where BUYORDER_SNCODE_MASTER_ID = #{buyorderSncodeMasterId,jdbcType=INTEGER}
  </delete>

  <insert id="insertSelective" keyColumn="BUYORDER_SNCODE_SLAVE_ID" keyProperty="buyorderSncodeSlaveId" parameterType="com.vedeng.order.model.ge.VBuyorderSncodeSlave" useGeneratedKeys="true">
    insert into V_BUYORDER_SNCODE_SLAVE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="buyorderSncodeMasterId != null">
        BUYORDER_SNCODE_MASTER_ID,
      </if>
      <if test="slaveSncode != null">
        SLAVE_SNCODE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modeTime != null">
        MODE_TIME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="buyorderSncodeMasterId != null">
        #{buyorderSncodeMasterId,jdbcType=INTEGER},
      </if>
      <if test="slaveSncode != null">
        #{slaveSncode,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modeTime != null">
        #{modeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

</mapper>