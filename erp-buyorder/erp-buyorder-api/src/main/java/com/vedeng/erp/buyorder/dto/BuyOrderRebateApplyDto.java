package com.vedeng.erp.buyorder.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class BuyOrderRebateApplyDto {
    /**
     * 主键
     */
    private Integer buyOrderRebateChargeId;

    /**
     * 采购返利收款单号
     */
    private String buyOrderRebateChargeNo;

    /**
     * 交易人id
     */
    private Integer traderId;

    /**
     * 供应商id
     */
    private Integer traderSupplierId;

    /**
     * 返利收款总金额
     */
    private BigDecimal totalAmount;

    /**
     * 返利周期开始时间
     */
    private Date cycleBeginTime;

    /**
     * 返利周期结束时间
     */
    private Date cycleEndTime;

    /**
     * 返利使用期限开始时间
     */
    private Date usePeriodBeginTime;

    /**
     * 返利使用期限结束时间
     */
    private Date usePeriodEndTime;

    /**
     * 结算附件url(多个附件逗号分割)
     */
    private String billUrlIds;

    /**
     * 明细附件url(多个附件逗号分割)
     */
    private String detailUrlIds;

    /**
     * 单据状态
     * 0 已提交（审核中）
     * 1 审核通过
     * 2 审核不通过
     */
    private Integer auditStatus;

    /**
     * 审核通过时间
     */
    private Date auditTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否删除
     */
    private Integer isDelete;

}
