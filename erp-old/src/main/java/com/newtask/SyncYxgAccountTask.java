package com.newtask;

import com.newtask.model.ApiPage;
import com.newtask.model.IndexData;
import com.newtask.model.IndexResDto;
import com.newtask.model.YXGTraderAptitude;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.trader.dao.TraderCustomerCategoryMapper;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.TraderCustomerCategory;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.service.TraderCustomerService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <b>Description:</b>同步医械购账号数据<br>
 * <b>@Author:calvin</b>
 * <br><b>@Date:</b> 2020/11/16
 */
@JobHandler(value = "syncYxgAccountHandler")
@Component
@Deprecated
public class SyncYxgAccountTask extends AbstractJobHandler {

    @Value("${api_url}")
    private String apiUrl;
    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private TraderCustomerMapper traderCustomerMapper;

    @Autowired
    private WebAccountMapper webAccountMapper;

    @Autowired
    private TraderCustomerCategoryMapper customerCategoryMapper;
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        String url=apiUrl+"/gomanager/comparisonDate";
        YXGTraderAptitude aptitude=new YXGTraderAptitude();
        YXGTraderAptitude payAptitude=new YXGTraderAptitude();
        WebAccount webAccount=new WebAccount();
        TraderCustomer updateCustomer=new TraderCustomer();
        Map<String,Integer> categoryMap=getCategoryMap();
        int currentPage=1;
        int totalPage=2;
        ApiPage page=new ApiPage(currentPage,200);
        do{
            com.alibaba.fastjson.TypeReference<ResultInfo<IndexResDto>> typeReference = new com.alibaba.fastjson.TypeReference<ResultInfo<IndexResDto>>() {
            };
            ResultInfo<IndexResDto> result = HttpRestClientUtil.restPost(url, typeReference, null, page);
            if(result==null||result.getData()==null|| CollectionUtils.isEmpty(result.getData().getIndexDataList())){
                return ReturnT.SUCCESS;
            }
            List<IndexData> list=result.getData().getIndexDataList();
            if(result.getData().getTotalPage()!=null&&result.getData().getTotalPage()>0){
                totalPage=result.getData().getTotalPage()+1;
            }
            list.stream().forEach(item->{
                handleOneRecord(item,categoryMap,aptitude,payAptitude,webAccount,updateCustomer);
            });
            XxlJobLogger.log("is process page :"+page.getPageNum());
            currentPage++;
            page.setPageNum(currentPage);
        }while (currentPage<totalPage);
        return ReturnT.SUCCESS;
    }



    private void  handleOneRecord(IndexData record,Map<String,Integer> categoryMap,YXGTraderAptitude aptitude,
                                  YXGTraderAptitude payAptitude,WebAccount webAccount,TraderCustomer updateCustomer){
        if(record==null||record.getTraderId()==null){
            return;
        }
        aptitude.setTraderId(record.getTraderId());
        aptitude.setStatus(record.getStatus());
        aptitude.setMsg(record.getCustomerCheckMsg());
        traderCustomerService.syncYxgTraderStatus(aptitude);

        payAptitude.setTraderId(record.getTraderId());
        payAptitude.setStatus(record.getPayofStatus());
        payAptitude.setMsg(record.getPayofCheckMsg());
        traderCustomerService.syncYxgCustomerStatus(payAptitude);
//        webAccount.setSsoAccountId(record.getSsoAccountId());
//        webAccount.setName(record.getRegisterName());
//        webAccountMapper.updateBySsoAccountId(webAccount);
        TraderCustomer customer=traderCustomerMapper.getCustomerInfo(record.getTraderId());
        if(customer!=null) {
            updateCustomer.setTraderCustomerId(customer.getTraderCustomerId());
            updateCustomer.setTraderCustomerCategoryId(categoryMap.get(record.getTypeName()));
            updateCustomer.setIsShowVipPrice(record.getShowPrice() == null ? 0 : record.getShowPrice());
            traderCustomerMapper.updateCategoryIdAndVipPrice(updateCustomer);
        }
    }

    private Map<String,Integer> getCategoryMap(){
        List<TraderCustomerCategory> categoryList=customerCategoryMapper.getYxgCategory();
        Map<String,Integer> res=categoryList.stream().collect(Collectors.toMap(TraderCustomerCategory::getCustomerCategoryName,
                TraderCustomerCategory::getTraderCustomerCategoryId));
        res.put("部级院",res.get("部级医院"));
        return res;
    }
}
