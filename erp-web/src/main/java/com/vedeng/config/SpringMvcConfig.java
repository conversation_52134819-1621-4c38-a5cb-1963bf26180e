package com.vedeng.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.servlet.ViewResolver;
import org.springframework.web.servlet.view.InternalResourceViewResolver;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: springmvc 配置
 * @date 2022/7/9 13:49
 */
//@Configuration
@Slf4j
public class  SpringMvcConfig {

    /**
     * 配置基于vue的JSP视图解析器
     */
    @Bean
    @Order(1)
    public ViewResolver viewResolver() {
        log.info("自定义viewResolver成功");
        InternalResourceViewResolver resolver = new InternalResourceViewResolver();
        resolver.setViewNames("vue*");
        resolver.setPrefix("/WEB-INF/");
        resolver.setSuffix(".jsp");
        return resolver;
    }

}
