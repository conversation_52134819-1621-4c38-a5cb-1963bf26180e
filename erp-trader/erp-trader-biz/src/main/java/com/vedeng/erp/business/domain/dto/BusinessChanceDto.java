package com.vedeng.erp.business.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.system.dto.CustomDataOperDto;
import com.vedeng.erp.system.dto.CustomTagDto;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.domain.dto.QuoteorderDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description 商机基本信息
 * @date 2022/7/12 10:43
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessChanceDto {

    /**
     * 主键
     */
    @NotNull(message = "商机主键不可为空", groups = {UpdateGroup.class})
    private Integer bussinessChanceId;
    
    private Integer frontEndSeq;

    private String sendVx;
    /**
     * 线索类型字典库 391 总机线索
     * 391	总机询价
     * 392	销售新增询价
     * 394	自主询价
     * 1933	贝登微信
     * 4110	线索转商机
     * 6102 拜访
     */
    private Integer clueType;
    
    /**
     * 商机编号-搜索用-列表字段
     */
    private String bussinessChanceNo;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 部门ID
     */
    private Integer orgId;

    /**
     * 归属销售id-列表字段
     */
    private Integer userId;


    /**
     * 创建人-搜索用
     */
    private List<Integer> creatorIdList;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String checkTraderName;

    /**
     * 客户地区
     */
    private String checkTraderArea;

    /**
     * 客户联系人
     */

    private String checkTraderContactName;

    /**
     * 客户联系人手机
     */

    private String checkTraderContactMobile;

    /**
     * 客户联系人电话
     */
    private String checkTraderContactTelephone;

    /**
     * 商机来源-字典库-搜索用
     */
    private Integer type;

    /**
     * 商机时间
     */
    private Long receiveTime;

    /**
     * 商机来源字典库
     */
    private Integer source;

    /**
     * 询价行为字典库
     */
    private Integer inquiry;

    /**
     * 询价方式字典库
     */
    private Integer communication;

    /**
     * 询价内容
     */
    private String content;

    /**
     * 交易者名称-搜索用
     */
    @ExcelProperty("客户名称")
    private String traderName;

    /**
     * 客户id-traderCustomerId
     */
    private Integer traderCustomerId;

    private Integer  customerNature;

    /**
     * 返回给crm的前端erp的超链接
     */
    private String traderNameLink;

    /**
     * ERP内部链接
     */
    private String traderNameInnerLink;

    /**
     * 地区最小级ID
     */
    private Integer areaId;

    /**
     * 多级地址逗号“,”拼接（冗余字段）
     */
    private String areaIds;

    /**
     * 联系人ID
     */

    private Integer traderContactId;

    /**
     * 联系人-搜索用
     */
    @ExcelProperty("* 客户联系人")
    @NotNull(message = "联系人不可为空", groups = {AddGroup.class, UpdateGroup.class})
    private String traderContactName;

    /**
     * 手机-搜索用
     */
    @NotNull(message = "手机不可为空", groups = {AddGroup.class, UpdateGroup.class})
    @ExcelProperty("* 手机")
    private String mobile;


    /**
     * 固话-搜索用
     */
    @ExcelProperty("电话")
    private String telephone;

    /**
     * 其他联系方式
     */
    private String otherContactInfo;

    /**
     * 备注-搜索用
     */
    @ExcelProperty("备注")
    private String comments;

    /**
     * 分配时间
     */
    private Long assignTime;

    /**
     * 初次查看时间(归属人)
     */
    private Long firstViewTime;

    /**
     * 商机状态0未处理、1报价中、2已报价、3已订单、4已关闭、5未分配、6处理中、7已成单
     */
    private Integer status;

    private List<Integer> statusList;

    /**
     * 预计成单金额-列表字段
     */
    private BigDecimal amount;

    /**
     * 预计成单金额-搜索用-最小值
     */
    private BigDecimal amountMin;

    /**
     * 预计成单金额-搜索用-最大值
     */
    private BigDecimal amountMax;

    /**
     * 预计成单日期
     */
    private Long orderTime;

    /**
     * 预计成单日期-列表字段
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date orderTimeDate;

    /**
     * 关闭原因-字典值
     */
    private Integer statusComments;

    /**
     * 关闭原因-名称
     */
    private String statusCommentsStr;

    /**
     * 关闭说明
     */
    private String closedComments;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建时间-列表字段
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTimeDate;

    /**
     * 添加人
     */
    private Integer creator;

    /**
     * 添加人头像-列表字段
     */
    private String creatorPic;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;

    /**
     * 产品需求-搜索用
     */
    @NotNull(message = "产品需求不可为空", groups = {AddGroup.class})
    @ExcelProperty("*产品需求（销售）")
    private String productCommentsSale;

    /**
     * 商机等级
     */
    private Integer systemBusinessLevel;

    /**
     * 商机等级-搜索用
     */
    private List<Integer> systemBusinessLevelList;

    /**
     * 商机等级-列表字段
     */
    private String systemBusinessLevelStr;

    /**
     * 线索标签id：英文逗号分割的id
     */
    private String tagIds;

    /**
     * 标签-搜索用
     */
    private List<Integer> tagIdList;

    /**
     * 终端名称-搜索用
     */
    private String terminalTraderName;
    
    /**
     * 终端类型 字典
     */
    private Integer terminalTraderType;

    /**
     * 采购方式-字典值-（直接采购 招投标采购）-搜索用
     */
    private Integer purchasingType;

    /**
     * 采购方式-搜索用
     */
    private List<Integer> purchasingTypeList;

    /**
     * 采购方式-名称
     */
    private String purchasingTypeStr;

    /**
     * @ 组装对象 @
     * 报价单商品
     */
    private List<BusinessChanceGoodsDto> businessChanceGoodsDtos;

    /**
     * @ 组装对象 @
     * 报价单实体
     */
    private QuoteorderDto quoteorderDto = new QuoteorderDto();

    /**
     * 销售订单信息
     */
    private SaleorderInfoDto saleorderInfoDto = new SaleorderInfoDto();

    /**
     * 最新沟通记录id-列表字段
     */
    private Integer latestCommunicateRecordId;

    /**
     * 最新沟通记录内容-列表字段
     */
    private String latestCommunicateRecordContent;

    /**
     * 最新任务id-列表字段
     */
    private Integer latestTaskId;

    /**
     * 最新任务内容-列表字段
     */
    private String latestTaskContent;

    /**
     * @ 组装对象 @
     * 沟通记录
     */
    private CommunicateRecordDto communicateRecordDto;

    /**
     * 目标用户id集合
     */
    private List<Integer> userIdList;

    /**
     * 目标用户id集合
     */
    private List<Integer> businessIdList;

    /**
     * 当前登录用户id，用于查询各个人的置顶信息
     */
    private Integer currentUserId;

    /**
     * 当前登录用户的职位类型
     */
    private Integer currentPositionType;

    /**
     * 归属销售-列表字段
     */
    private String username;

    /**
     * 归属销售头像-列表字段
     */
    private String belongPic;
    
    /**
     * 下次沟通日期 开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextCommunicationDateStart;

    /**
     * 下次沟通日期 结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextCommunicationDateEnd;

    /**
     * 最近跟进日期(13位时间戳)-搜索用-开始
     */
    private Long latestFollowUpTimeStart;

    /**
     * 最近跟进日期(13位时间戳)-搜索用-结束
     */
    private Long latestFollowUpTimeEnd;

    /**
     * 最近跟进日期(13位时间戳)-搜索用-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestFollowUpTimeStartStr;

    /**
     * 最近跟进日期(13位时间戳)-搜索用-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date latestFollowUpTimeEndStr;

    /**
     * 任务截至日期-搜索用-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadlineDateStart;

    /**
     * 任务截至日期-搜索用-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadlineDateEnd;

    /**
     * 创建时间(13位时间戳)-搜索用-开始
     */
    private Long addTimeStart;
    /**
     * 创建时间(13位时间戳)-搜索用-结束
     */
    private Long addTimeEnd;

    /**
     * 创建时间-搜索用-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTimeStartStr;
    /**
     * 创建时间-搜索用-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date addTimeEndStr;
    
    /**
     * 预计成单时间(13位时间戳)-搜索用-开始
     */
    private Long orderTimeStart;

    /**
     * 预计成单时间(13位时间戳)-搜索用-结束
     */
    private Long orderTimeEnd;

    /**
     * 预计成单时间(13位时间戳)-搜索用-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeStartStr;

    /**
     * 预计成单时间(13位时间戳)-搜索用-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderTimeEndStr;

    /**
     * 客户性质 名称
     */
    private String customerNatureName;

    /**
     * 商机来源-名称
     */
    private String typeStr;

    /**
     * 创建人名称
     */
    private String creatorName;

    /**
     * 地区 省市区拼接
     */
    private String areaStr;

    /**
     * 判断是否是线索转商机
     */
    private Integer businessLeadsId;

    /**
     * 终端区域-搜索用
     */
    private Set<Integer> areaIdList;

    /**
     * 终端区域-搜索用-省ids
     */
    private Set<Integer> provinceIdList = new HashSet<>();
    /**
     * 终端区域-搜索用-市ids
     */
    private Set<Integer> cityIdList = new HashSet<>();
    /**
     * 终端区域-搜索用-区ids
     */
    private Set<Integer> countyIdList = new HashSet<>();

    /**
     * 终端区域-搜索用-省ids
     */
    private Set<String> provinceIdListStr = new HashSet<>();
    /**
     * 终端区域-搜索用-市ids
     */
    private Set<String> cityIdListStr = new HashSet<>();
    /**
     * 终端区域-搜索用-区ids
     */
    private Set<String> countyIdListStr = new HashSet<>();

    /**
     * 标签
     */
    List<CustomTagDto> tags;

    /**
     * 置顶
     */
    private CustomDataOperDto customDataOperDto;

    /**
     * 归属销售-搜索用
     */
    private List<Integer> belongerIdList;

    /**
     * 协作人-搜索用
     */
    private List<Integer> collaboratorIdList;

    /**
     * 沟通记录筛选项
     */
    private Integer communicateState;

    /**
     * 关联终端信息
     */
    private OrderTerminalDto orderTerminalDto;

    /**
     * 是否有ai助手产生
     */
    private Integer isAiAssistant;

    /**
     * 是否有ai助手产生的通话id
     */
    private Integer aiCommunicateRecordId;

    /**
     * 我的关注-搜索用-（1关注，0未关注）
     */
    private Integer attentionState;

    /**
     * 终端性质-字典值(公立等级、公立基层、非公等级、非公基层、非公集团、应急、院外)
     */
    private Integer terminalTraderNature;

    /**
     * 终端性质-搜索用
     */
    private List<Integer> terminalTraderNatureList;

    /**
     * 终端性质-名称
     */
    private String terminalTraderNatureStr;

    /**
     * 终端区域(省市区id，逗号分隔)
     */
    private String terminalTraderRegion;

    /**
     * 终端区域名称
     */
    private String terminalTraderRegionStr;

    /**
     * 商机类型-字典值(小产品、大单品、综合项目、AED、应急)
     */
    private Integer businessType;

    /**
     * 商机类型-列表字段
     */
    private String businessTypeStr;

    /**
     * 商机类型-搜索用
     */
    private List<Integer> businessTypeList;

    /**
     * 客情关系(1熟悉终端决策人，2熟悉使用人，多选逗号分隔)
     */
    private String customerRelationship;

    /**
     * 客情关系-详情展示用
     */
    private List<String> customerRelationshipStr;

    /**
     * 客情关系-搜索用
     */
    private List<Integer> customerRelationshipList;

    /**
     * 招投标阶段-字典值(提案咨询、立项论证、意向公示、公开招标、合同签署)
     */
    private Integer biddingPhase;

    /**
     * 招投标阶段-搜索用
     */
    private List<Integer> biddingPhaseList;

    /**
     * 招投标阶段-名称
     */
    private String biddingPhaseStr;

    /**
     * 招标参数(1可调整，2不可调整)-搜索用
     */
    private Integer biddingParameter;

    /**
     * 招标参数-搜索用
     */
    private List<Integer> biddingParameterList;

    /**
     * 商机阶段(1初步洽谈，2商机验证，3初步方案，4最终方案，5赢单，6关闭)
     */
    private Integer stage;

    /**
     * 商机阶段-列表字段
     */
    private String stageStr;

    /**
     * 商机阶段-搜索用
     */
    private List<Integer> stageList;

    /**
     * 初步洽谈时间
     */
    private Date preliminaryNegotiationTime;

    /**
     * 商机验证时间
     */
    private Date opportunityVerificationTime;

    /**
     * 初步方案时间
     */
    private Date preliminarySchemeTime;

    /**
     * 最终方案时间
     */
    private Date finalSchemeTime;

    /**
     * 赢单时间
     */
    private Date winningOrderTime;

    /**
     * 关闭时间
     */
    private Date loseOrderTime;

    /**
     * 任务处理-搜素用-（0未处理，1已处理，2关闭）
     */
    private Integer doneStatus;

    /**
     * 线索编号-搜索用
     */
    private String leadsNo;

    /**
     * 订货号-搜索用
     */
    private String skuNo;

    /**
     * 产品名称-搜索用
     */
    private String productName;

    /**
     * 品牌-搜索用
     */
    private String brandName;

    /**
     * 型号-搜索用
     */
    private String modelNumber;

    /**
     * 是否查询全部
     */
    private Integer queryAll;

    /**
     * 关联单据
     */
    private List<RelatedOrderDto> relatedOrderDtoList;

    /**
     * 天眼查标识
     */
    private String tycFlag;

    /**
     * 分页开始
     */
    private Long limitStart;

    /**
     * 分页结束
     */
    private Long limitEnd;

    /**
     * 咨询入口-回显
     */
    private Integer entrances;

    /**
     * 渠道类型-回显
     */
    private String entrancesName;

    /**
     * 功能-回显
     */
    private Integer functions;
    /**
     * 功能-回显
     */
    private String functionsName;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 市id
     */
    private Integer cityId;

    /**
     * 县id
     */
    private Integer countyId;

    /**
     * 省
     */
    private String province;

    /**
     * 市
     */
    private String city;

    /**
     * 县
     */
    private String county;

    /**
     * 0未付款 1已付款
     */
    private Integer paymentStatus;

    /**
     * 商品分类id
     */
    private String categoryIds;

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 商品分类ID列表
     */
    private List<Integer> categoryIdList;

    /**
     * 省份ID列表
     */
    private List<Integer> provinceIds;

    /**
     * 城市ID列表
     */
    private List<Integer> cityIds;
    
    /**部门ID列表*/
	private List<Integer> organizationIdList;
    
    /**属于部门ID列表下的所有用户ID*/
    private List<Integer> belongOrgUserIdList;
}
