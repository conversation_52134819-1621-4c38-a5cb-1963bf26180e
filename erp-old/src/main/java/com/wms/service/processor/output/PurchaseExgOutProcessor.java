package com.wms.service.processor.output;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.OutputDto;
import com.wms.dto.OutputGoodDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.processor.input.WmsLendInputProcessor;
import com.wms.service.stockcalculate.StockinfoCaculateInterface;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 采购售后换货出库处理器
 */
@Service
public class PurchaseExgOutProcessor extends AbstractOutputOrderProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(WmsLendInputProcessor.class);

    @Autowired
    @Qualifier("saleorderOutCaculateImpl")
    private StockinfoCaculateInterface stockinfoCaculateInterface;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Override
    protected void commonValidator(OutputDto outputDto) throws Exception {
        if(!WmsInterfaceOrderType.OUT_PURCHASE_EXG.equals(outputDto.getOrderType())){
            throw new Exception("出库单:"+outputDto.getOrderType()+"的类型错误!");
        }
    }

    @Override
    protected void updateOrderData(OutputDto outputDto) {

        String afterSaleOrderNo = outputDto.getSOReference1();

        AfterSales afterSale = afterSalesMapper.getAfterSalesByNo(afterSaleOrderNo);

        List<AfterSalesGoodsVo> afterSaleGoodList =
                this.afterSalesGoodsMapper.getAfterSalesGoodList(afterSale.getAfterSalesId());

        ThreadLocalContext.put("afterSaleInfo",afterSale);
        ThreadLocalContext.put("afterSaleGoodList",afterSaleGoodList);

        List<OutputGoodDto> details = outputDto.getDetails();

        details.stream().forEach(outputGoodDto -> {

            AfterSalesGoodsVo afterSalesGoodsVo = getRelateAfterSaleGood(getRelateId(outputGoodDto));

            AfterSalesGoods updateGood = new AfterSalesGoods();
            updateGood.setAfterSalesGoodsId(afterSalesGoodsVo.getAfterSalesGoodsId());
            updateGood.setDeliveryNum(afterSalesGoodsVo.getDeliveryNum() +  outputGoodDto.getQtyShipped().intValue());
            updateGood.setDeliveryTime(System.currentTimeMillis());

            //2-全部发货 1-部分发货
            updateGood.setDeliveryStatus(updateGood.getDeliveryNum().equals(afterSalesGoodsVo.getNum()) ? 2 : 1);

            LOGGER.info("采购换货出库，更新售后单商品的数据:" + JSON.toJSONString(updateGood));

            afterSalesGoodsMapper.updateByPrimaryKeySelective(updateGood);
        });

    }

    private AfterSalesGoodsVo getRelateAfterSaleGood(Integer afterSaleGoodsId) {

        List<AfterSalesGoodsVo> afterSaleGoodList = ThreadLocalContext.get("afterSaleGoodList");

        return afterSaleGoodList
                .stream()
                .filter(afterSalesGood -> {
                    return afterSalesGood.getAfterSalesGoodsId().equals(afterSaleGoodsId);
                })
                .findFirst()
                .orElse(null);
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.valueOf(outputGoodDto.getUserDefine1());

    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.BUYORDER_WAREHOUSE_CHANGE_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.PURCHASE_EXG.getOperateTypeCode();
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }

}
