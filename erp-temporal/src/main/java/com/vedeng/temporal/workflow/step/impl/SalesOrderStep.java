package com.vedeng.temporal.workflow.step.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.temporal.context.CompanyExecutionContext;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.FlowOrderInfoUpdateRequest;
import com.vedeng.temporal.polling.universal.enums.DataSourceType;
import com.vedeng.temporal.polling.universal.request.UniversalPollingRequest;
import com.vedeng.temporal.polling.universal.result.UniversalPollingResult;
import com.vedeng.temporal.polling.universal.workflow.UniversalPollingWorkflow;
import com.vedeng.temporal.enums.BusinessStepType;
import com.vedeng.temporal.workflow.activity.SalesOrderActivity;
import com.vedeng.temporal.workflow.step.BusinessStep;
import com.vedeng.temporal.exception.BusinessProcessException;
import io.temporal.workflow.Workflow;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 销售订单步骤 V2 - 新架构版本
 * <p>
 * 架构优化说明：
 * - 使用新的 SalesOrderActivity 替代 SalesOrderFlow
 * - 每个业务操作都是独立的 Activity 方法
 * - 通过 CompanyBusinessRequest.businessData 传递数据
 * - 支持数据串联：创建 → 提交 → 审核 → 状态确认
 * - 异常处理和重试由 Temporal 和 UniversalBusinessTemplate 统一管理
 * <p>
 * 业务流程：
 * 1. createSalesOrder - 创建销售订单，获取订单ID
 * 2. submitSalesOrderForApproval - 提交审核，传递订单ID
 * 3. approveSalesOrder - 审核通过，传递订单ID
 * 4. waitForCompletion - 等待订单完成（可选）
 *
 * <AUTHOR> 4.0 sonnet
 * @version 2.0 (新架构版本)
 * @since 2025-01-18
 */
@Slf4j
public class SalesOrderStep implements BusinessStep {

    private final SalesOrderActivity salesOrderActivity;

    public SalesOrderStep(SalesOrderActivity salesOrderActivity) {
        this.salesOrderActivity = salesOrderActivity;
    }

    @Override
    public CompanyBusinessResponse execute(CompanyBusinessRequest request, CompanyExecutionContext context) {
        if(context == null || StringUtils.isBlank(context.getNextCompany())){
            return CompanyBusinessResponse.success("销售订单流程执行成功", null);
        }
        request.setSourceCompanyCode(context.getCurrentCompany());
        request.setTargetCompanyCode(context.getNextCompany());
        log.info("开始执行销售订单步骤V2，业务ID: {}, 目标公司: {}",
                request.getBusinessId(), request.getTargetCompanyCode());
        String saleorderNo = null;
        // 1. 创建销售订单
        CompanyBusinessResponse createResult = salesOrderActivity.createSalesOrder(request);
        if (!createResult.getSuccess()) {
            log.error("创建销售订单失败，业务ID: {}, 错误: {}",
                    request.getBusinessId(), createResult.getMessage());
            return createResult;
        }else{
            Object obj = createResult.getResultData();
            String json = JSON.toJSONString(obj);
            Map<String, Object> map = JSON.parseObject(json, Map.class);
            saleorderNo =  (String) map.get("saleorderNo");
            if(StringUtils.isEmpty(saleorderNo)){
                Object data = map.get("data");
                if(data != null && data instanceof LinkedHashMap){
                    Map<String,Object> dataMap = (Map<String, Object>) data;
                    saleorderNo =  (String) dataMap.get("saleorderNo");
                } else if (data instanceof JSONObject) {
                    saleorderNo = ((JSONObject)data).getString("saleorderNo");         //(String) data.get("saleorderNo");
                }
            }
            request.setSourceCompanyCode(context.getCurrentCompany());
            request.setTargetCompanyCode(context.getNextCompany());
            salesOrderActivity.callBackData(request,saleorderNo);
        }

        String orderId = createResult.getGeneratedDocumentId();
        log.info("销售订单创建成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        // 2. 提交审核（传递订单ID）
        request.setSourceCompanyCode(context.getCurrentCompany());
        request.setTargetCompanyCode(context.getNextCompany());
        CompanyBusinessRequest submitRequest = buildRequestWithOrderId(request, orderId);
        request.setSourceCompanyCode(context.getCurrentCompany());
        request.setTargetCompanyCode(context.getNextCompany());
        CompanyBusinessResponse submitResult = salesOrderActivity.submitSalesOrderForApproval(submitRequest);
        if (!submitResult.getSuccess()) {
            log.error("提交销售订单审核失败，业务ID: {}, 订单ID: {}, 错误: {}",
                    request.getBusinessId(), orderId, submitResult.getMessage());
            return submitResult;
        }

        log.info("销售订单提交审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        // 3. 审核通过（传递订单ID）
        request.setSourceCompanyCode(context.getCurrentCompany());
        request.setTargetCompanyCode(context.getNextCompany());
        CompanyBusinessRequest approveRequest = buildRequestWithOrderId(request, orderId);
        request.setSourceCompanyCode(context.getCurrentCompany());
        request.setTargetCompanyCode(context.getNextCompany());
        CompanyBusinessResponse approveResult = salesOrderActivity.approveSalesOrder(approveRequest);
        if (!approveResult.getSuccess()) {
            log.error("审核销售订单失败，业务ID: {}, 订单ID: {}, 错误: {}",
                    request.getBusinessId(), orderId, approveResult.getMessage());
            return approveResult;
        }
//        request.setSourceCompanyCode(context.getCurrentCompany());
//        request.setTargetCompanyCode(context.getNextCompany());
//        salesOrderActivity.callBackUpdateData(request,saleorderNo);

        log.info("销售订单审核成功，业务ID: {}, 订单ID: {}", request.getBusinessId(), orderId);

        return CompanyBusinessResponse.success("销售订单流程执行成功", orderId);
    }

    @Override
    public String getStepName() {
        return "销售订单步骤V2";
    }

    @Override
    public BusinessStepType getStepType() {
        return BusinessStepType.SALES_ORDER;
    }


    @Override
    public String getStepDescription() {
        return "执行销售订单完整流程：创建订单 → 提交审核 → 审核通过";
    }

    // ========== 私有辅助方法 ==========

    /**
     * 构建包含订单ID的请求
     */
    private CompanyBusinessRequest buildRequestWithOrderId(CompanyBusinessRequest originalRequest, String orderId) {
        // 解析原有的业务数据
        Map<String, Object> businessData = new HashMap<>();
        if (originalRequest.getBusinessData() != null) {
            try {
                businessData = JSON.parseObject(originalRequest.getBusinessData(), Map.class);
            } catch (Exception e) {
                log.warn("解析原有业务数据失败，使用空Map: {}", originalRequest.getBusinessData(), e);
            }
        }

        // 添加订单ID
        businessData.put("orderId", orderId);

        // 构建新的请求
        return CompanyBusinessRequest.builder()
                .businessId(originalRequest.getBusinessId())
                .businessType(originalRequest.getBusinessType())
                .sourceCompanyCode(originalRequest.getSourceCompanyCode())
                .targetCompanyCode(originalRequest.getTargetCompanyCode())
                .workflowExecutionId(originalRequest.getWorkflowExecutionId())
                .flowNodeId(originalRequest.getFlowNodeId())
                .businessData(JSON.toJSONString(businessData))
                .build();
    }



}
