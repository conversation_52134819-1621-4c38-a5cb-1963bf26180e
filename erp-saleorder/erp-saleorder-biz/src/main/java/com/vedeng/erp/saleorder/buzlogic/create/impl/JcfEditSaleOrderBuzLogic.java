package com.vedeng.erp.saleorder.buzlogic.create.impl;

import com.google.common.base.Strings;
import com.vedeng.aftersales.service.WebAccountService;
import com.vedeng.common.constant.CommonConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.customerbillperiod.constant.CustomerBillPeriodSettlementTypeEnum;
import com.vedeng.erp.saleorder.buzlogic.create.EditSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.service.TraderContactService;
import com.vedeng.wechat.JcWeChatEnum;
import com.vedeng.wechat.model.JcWeChatModel;
import com.vedeng.wechat.service.JcWeChatArrService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class JcfEditSaleOrderBuzLogic extends EditSaleOrderBuzLogic {
    Logger logger= LoggerFactory.getLogger(JcfEditSaleOrderBuzLogic.class);
    @Autowired
    private BaseSaleOrderService baseSaleOrderService;
    @Resource
    private TraderCustomerMapper traderCustomerMapper;
    @Resource
    private WebAccountMapper webAccountMapper;
    @Autowired
    private WebAccountService webAccountService;
    @Autowired
    private TraderContactService traderContactService;

    @Autowired
    private JcWeChatArrService jcWeChatArrService;

    public JcfEditSaleOrderBuzLogic(){
        List<String> sequence = new ArrayList<>();
        sequence.add("setJcInfo");
        sequence.add("saveEditSaleOrderInfo");
        sequence.add("updateContactPositionIfExist");
        sequence.add("sendWxMessage");
        super.setSequence(sequence);
    }

    /**
     * @desc 设置集采收票，结算主体相关信息
     * <AUTHOR>
     * @param saleorder
     * @return
     */
    public ResultInfo setJcInfo(Saleorder saleorder){
        //订单结算主体为收票客户
        Integer invoiceTraderId = saleorder.getInvoiceTraderId();
        //校验收票客户信息是否存在且未被禁用
        if (invoiceTraderId == null || invoiceTraderId== 0) {
            return new ResultInfo(-1,"集采订单结算主体为收票客户，保存订单信息时售票客户id为空");
        }
        TraderCustomerVo invoiceCustomerQuery = traderCustomerMapper.getCustomerInfo(invoiceTraderId);
        if (invoiceCustomerQuery == null || CommonConstants.DISABLE.equals(invoiceCustomerQuery.getIsEnable())) {
            return new ResultInfo(-1,"保存集采订单时未查询到收票客户");
        }
        saleorder.setTraderName(invoiceCustomerQuery.getName());

        //检验并替换注册用户信息为联系人信息
        try {
            checkAndReplaceSubmittedAccountInfo(saleorder, false);
        }catch (Exception e){
            log.error("【setJcInfo】处理异常",e);
            return new ResultInfo(-1,e.getMessage());
        }

        //集采订单客户结算主体是"收票客户"
        saleorder.setTraderId(saleorder.getInvoiceTraderId());
        setDefaultPaymentInfo(saleorder);
        return new ResultInfo(0,"设置基础信息成功");
    }

    private void setDefaultPaymentInfo(Saleorder saleorder) {
        // 100%预付--其他付款计划：均设置默认值
        if (OrderConstant.PREPAY_100_PERCENT.equals(saleorder.getPaymentType())) {
            BigDecimal defaultAmount = new BigDecimal(0.00);
            // 账期支付金额
            saleorder.setAccountPeriodAmount(defaultAmount);
            // 账期天数
            saleorder.setPeriodDay(0);
            // 物流代收0否 1是
            saleorder.setRetainageAmount(defaultAmount);// 尾款
            saleorder.setLogisticsCollection(0);
            // 尾款期限(月)
            saleorder.setRetainageAmountMonth(0);
        }
        if(saleorder.getBillPeriodSettlementType() == null || saleorder.getBillPeriodSettlementType() == 0){
            // 默认结算方式
            saleorder.setBillPeriodSettlementType(CustomerBillPeriodSettlementTypeEnum.ORDER_INVOICE.getCode());
        }
    }

    private void checkAndReplaceSubmittedAccountInfo(Saleorder orderInfo, boolean replaceAddressIfNecessary) {
        //创建订单用户手机号
        String creatorMobileNo = orderInfo.getTraderContactMobile();

        WebAccount webAccountQuery = webAccountService.getByMobileNo(creatorMobileNo);
        if (webAccountQuery == null || CommonConstants.DISABLE.equals(webAccountQuery.getIsEnable())) {
            throw new IllegalArgumentException(String.format("保存集采订单时未查询下单人[mobileNo: %s]信息或已被禁用", creatorMobileNo));
        }

        if (webAccountQuery.getTraderId() == null || webAccountQuery.getTraderId() == 0) {
            logger.error("保存集采订单时注册用户为关联客户 - erpAccountId: {}, traderId: {}", webAccountQuery.getErpAccountId() ,webAccountQuery.getTraderId());
            throw new IllegalArgumentException("保存集采订单时注册用户关联的客户为空");
        }

        // 根据traderId查询所属客户
        TraderCustomerVo traderCustomer = traderCustomerMapper.getCustomerInfo(webAccountQuery.getTraderId());
        if (traderCustomer == null || CommonConstants.DISABLE.equals(traderCustomer.getIsEnable())) {
            logger.error("保存集采订单时属未查询穿创建订单客户或改客户已被警用 - orderNo:{}, traderId:{}",orderInfo.getSaleorderNo(), webAccountQuery.getTraderId());
            throw new IllegalArgumentException("集采订单结算主体为收票客户，保存订单信息时售票客户id为空");
        }

        //保存创建客户ID
        orderInfo.setGroupCustomerId(webAccountQuery.getTraderId());
        // 下单人客户人类型
        orderInfo.setCustomerType(traderCustomer.getCustomerType());
        // 下单人客户人性质
        orderInfo.setCustomerNature(traderCustomer.getCustomerNature());

        String accountName = StringUtils.defaultIfBlank(orderInfo.getTraderContactName(), webAccountQuery.getName());
        TraderContactGenerate traderContactQuery = getOrSaveContactInfoIfNotExist(webAccountQuery.getTraderId(), creatorMobileNo, accountName);
        orderInfo.setTraderContactId(traderContactQuery.getTraderContactId());
        orderInfo.setTraderContactName(traderContactQuery.getName());
        orderInfo.setTraderContactMobile(traderContactQuery.getMobile());
        //保存创建订单人手机号
        orderInfo.setCreateMobile(traderContactQuery.getMobile());

        //如果用收货联系地址信息替换客户信息
        if (replaceAddressIfNecessary) {
            orderInfo.setTraderAreaId(orderInfo.getTakeTraderAreaId());
            orderInfo.setTraderArea(orderInfo.getTakeTraderArea());
            orderInfo.setTraderAddressId(orderInfo.getTakeTraderAddressId());
            orderInfo.setTraderAddress(orderInfo.getTakeTraderAddress());
        }
    }
    private TraderContactGenerate getOrSaveContactInfoIfNotExist(Integer traderId, String mobile, String name) {
        if(traderId==null || StringUtils.isEmpty(mobile) || StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("参数不合法");
        }

        TraderContactGenerate contactQuery = traderContactService.getByTraderIdAndMobileNo(traderId, mobile);
        if (contactQuery != null && CommonConstants.ENABLE.equals(contactQuery.getIsEnable())) {
            return contactQuery;
        }
        TraderContactGenerate contactToSave = new TraderContactGenerate();
        contactToSave.setTraderId(traderId);
        contactToSave.setTraderType(CommonConstants.TRADER_TYPE_1);
        contactToSave.setName(name);
        contactToSave.setMobile(mobile);
        contactToSave.setIsEnable(CommonConstants.ENABLE);
        contactToSave.setAddTime(System.currentTimeMillis());
        traderContactService.insertSelective(contactToSave);
        return contactToSave;
    }
    /**
     * 更新订单联系人职位
     * @param saleorder
     * @return
     */
    private ResultInfo updateContactPositionIfExist(Saleorder saleorder) {
        if (saleorder.getTraderContactId() != null && !Strings.isNullOrEmpty(saleorder.getGroupContactPositions())) {
            WebAccount webAccountQuery = webAccountMapper.getWebAccountInfo(saleorder.getTraderContactId());
            if(webAccountQuery == null || webAccountQuery.getTraderId() == null) {
                return new ResultInfo(-1,"未查到对应注册用户信息");
            }

            TraderContactGenerate contactQuery = traderContactService.getByTraderIdAndMobileNo(webAccountQuery.getTraderId(), webAccountQuery.getMobile());
            if (contactQuery==null) {
                return new ResultInfo(-1,"交易客户对应交易人信息为空");
            }
            TraderContactGenerate traderContactGenerate = new TraderContactGenerate();
            traderContactGenerate.setTraderContactId(contactQuery.getTraderContactId());
            traderContactGenerate.setPosition(saleorder.getGroupContactPositions());
            traderContactService.updateByPrimaryKeySelective(traderContactGenerate);
            return new ResultInfo(0,"更新订单联系人职位成功");
        }
        return new ResultInfo(0,"销售单账户id或职位信息为空无需更新");
    }

    /**
     * <AUTHOR>
     * @desc jcf订单推送微信确认消息
     * @return
     */
    private ResultInfo sendWxMessage(Saleorder saleorder){
        ResultInfo resultInfo = new ResultInfo(0,"操作成功");
        //发送微信消息
        JcWeChatModel jcWeChatModel = new JcWeChatModel(saleorder.getSaleorderId());
        jcWeChatArrService.sendTemplateMsgJcorder(jcWeChatModel, JcWeChatEnum.SAVE_ORDER,false);
        return  resultInfo;
    }

    public ResultInfo run(Saleorder saleorder){
        new JcfEditSaleOrderBuzLogic();
        ResultInfo result = new ResultInfo(0,"操作成功");
        for (String methodName : getSequence()){
            switch (methodName) {
                case "setJcInfo":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = this.setJcInfo(saleorder);
                        logger.info("集采订单{},设置基础信息返回{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                case "saveEditSaleOrderInfo":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = super.saveEditSaleOrderInfo(saleorder);
                    }
                    break;
                case "updateContactPositionIfExist":
                    if(ErpConst.ZERO.equals(result.getCode())) {
                        result = this.updateContactPositionIfExist(saleorder);
                        logger.info("集采订单{},更新订单联系人职位返回信息{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                case "":
                    if(ErpConst.ZERO.equals(result.getCode())){
                        result = this.sendWxMessage(saleorder);
                        logger.info("集采订单{},发送微信验证消息返回信息{},",saleorder.getSaleorderId(),result.getMessage());
                    }
                    break;
                default:
                    break;
            }
        }
        return result;
    }
}
