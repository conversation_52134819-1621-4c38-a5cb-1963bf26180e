<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="协议价格维护" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="main-container">
	<div class="list-pages-search">
		<form action="${pageContext.request.contextPath}/price/contract/index.do" method="post" id="search">
			<%--<input type="hidden" name="search" value="click">--%>
			<ul>
				<li><label class="infor_name">商品名称</label> <input type="text"
					class="input-middle" placeholder="请输入订货号/商品名称" name="googsName"
					id="googsName"
					value="${priceContractVo.googsName}"></li>

				<li><label class="infor_name">客户名称</label> <input type="text"
					class="input-middle" placeholder="请输入客户名称" name="customName"
					id="customName"
					value="${priceContractVo.customName}"></li>

				<li><label class="infor_name">协议商品类别</label> <input type="text"
					class="input-middle" placeholder="请输入协议商品类别" name="contractContegory"
					id="contractContegory"
					value="${priceContractVo.contractContegory}"></li>

				<li><label class="infor_name">是否维护</label> <select
						class="input-middle f_left" name="isMaintain">
					<option value="">全部</option>
                    <option value="1"<c:if test="${priceContractVo.isMaintain == 1}">selected="selected"</c:if>>已维护</option>
                    <option value="2"<c:if test="${priceContractVo.isMaintain == 2}">selected="selected"</c:if>>未维护</option>
				</select></li>

				<li>
					<label class="infor_name">归属平台</label>
					<select class="input-middle f_left" name="belongPlatform">
						<option value="">全部</option>
						<option value="1" <c:if test="${priceContractVo.belongPlatform == 1}">selected="selected"</c:if>>贝登医疗</option>
						<option value="2" <c:if test="${priceContractVo.belongPlatform == 2}">selected="selected"</c:if>>医械购</option>
						<option value="3" <c:if test="${priceContractVo.belongPlatform == 3}">selected="selected"</c:if>>科研购</option>
						<option value="6" <c:if test="${priceContractVo.belongPlatform == 6}">selected="selected"</c:if>>非公集采</option>
					</select>
				</li>

				<%--<li>
					<span class="bg-light-blue bt-bg-style bt-small" style="margin-left: 10px" id="searchSpan" onclick="search();">查询</span>

				</li>
				<li><span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span></li>--%>
			</ul>

				<div class="tcenter">
					<span class="bg-light-blue bt-bg-style bt-small" style="margin-left: 10px" id="searchSpan" onclick="search();">查询</span>
					<span class="bt-small bg-light-blue bt-bg-style" onclick="reset();">重置</span>

					<shiro:hasPermission name="/price/contract/editShow.do">
						<button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
								layerparams='{"width":"500px","height":"200px","title":"批量核价","link":"./uplodeBatchPriceContract.do"}'>批量维护</button>
						<button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
								layerparams='{"width":"500px","height":"200px","title":"新增协议价","link":"./toBatchAddContractPrice.do"}'>新增协议价</button>
					</shiro:hasPermission>
				</div>
		</form>
	</div>
	<div class="normal-list-page">

			<table
				class="table table-bordered table-striped table-condensed table-centered">
				<thead>
					<tr>
						<th class="wid10">客户名称</th>
						<th class="wid10">归属平台</th>
						<th class="wid10">订货号</th>
						<th class="wid10">商品名称</th>
						<th class="wid10">单位</th>
						<th class="wid10">协议价</th>
						<th class="wid10">协议商品类别</th>

						<th class="wid10">操作</th>
					</tr>
				</thead>

				<tbody class="employeestate">
				<c:forEach items="${list}" var="priceList">

					<tr>
						<td>${priceList.customerName}</td>
						<td>
							<c:if test="${priceList.belongPlatform eq 1}">贝登医疗</c:if>
							<c:if test="${priceList.belongPlatform eq 2}">医械购</c:if>
							<c:if test="${priceList.belongPlatform eq 3}">科研购</c:if>
							<c:if test="${priceList.belongPlatform eq 4}">集团业务部</c:if>
							<c:if test="${priceList.belongPlatform eq 5}">其他</c:if>
							<c:if test="${priceList.belongPlatform eq 6}">非公集采</c:if>
						</td>
						<td>${priceList.skuNo}</td>
						<td>${priceList.skuName}</td>
						<td>${priceList.unitName}</td>
						<td>
							<c:if test="${priceList.showPic == true}">
								<img src="${pageContext.request.contextPath}/static/images/gantanhao.png" width="20px" height="20px" title="价格过低"/>
							</c:if>
							<fmt:formatNumber type="number" value="${priceList.contractPrice}" pattern="0.00"/>
						</td>
						<td>${priceList.contractCategory}</td>

						<%--<td><a layerparams='{"width":"500px","height":"200px","title":"批量核价","link":"./editPriceContract.do"}'>编辑</a> </td>--%>
						<td>
							<shiro:hasPermission name="/price/contract/editShow.do">
								<span class="edit-user pop-new-data"
									  layerParams='{"width":"500px","height":"210px","title":"编辑价格","link":"./editPriceContract.do?id=${priceList.id}&contractPrice=${priceList.contractPrice}"}'>编辑</span>
							</shiro:hasPermission>
							<c:if test="${priceList.belongPlatform eq 6}">
								<span class="bt-smaller bt-border-style border-red" onclick="deleteContractPrice(${priceList.id})">删除</span>
							</c:if>
						</td>
					</tr>
				</c:forEach>
					<c:if test="${not empty priceInfoList}">
					</c:if>
				</tbody>
			</table>
			<c:if test="${empty list}">
				<!-- 查询无结果弹出 -->
				<div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
			</c:if>

	</div>
    <%--<button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
            layerparams='{"width":"500px","height":"200px","title":"批量核价","link":"./uplodeBatchPriceContract.do"}'>批量维护</button>--%>
	<tags:page page="${page}" />
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/priceContract/index.js?rnd=${resourceVersionKey}"></script>
<%--	<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>--%>
<%@ include file="../../common/footer.jsp"%>