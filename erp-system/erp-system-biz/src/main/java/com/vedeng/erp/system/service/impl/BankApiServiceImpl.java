package com.vedeng.erp.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.erp.system.domain.entity.BankEntity;
import com.vedeng.erp.system.dto.BankInfoDto;
import com.vedeng.erp.system.mapper.BankMapper;
import com.vedeng.erp.system.mapstruct.BankConvertor;
import com.vedeng.erp.system.service.BankApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class BankApiServiceImpl implements BankApiService {

    @Autowired
    private BankMapper bankMapper;
    @Autowired
    private BankConvertor bankConvertor;


    @Override
    public List<BankInfoDto> queryByBankName(List<String> bankNames) {
        if (CollUtil.isEmpty(bankNames)){
            return new ArrayList<>();
        }
        return bankMapper.findByBankName(bankNames);
    }

    @Override
    public BankInfoDto findByBankNoAndIsDel(String bankNo, Integer isDel) {
        BankInfoDto bankInfoDto = new BankInfoDto();
        BankEntity bankEntity = bankMapper.findByBankNoAndIsDel(bankNo, isDel);
        if (Objects.nonNull(bankEntity)) {
            bankInfoDto.setBankId(bankEntity.getBankId());
            bankInfoDto.setBankNo(bankEntity.getBankNo());
            bankInfoDto.setBankName(bankEntity.getBankName());
        }
        return bankInfoDto;
    }

    @Override
    public BankInfoDto findByBankId(Integer bankId) {
        return bankMapper.findByBankId(bankId);
    }

}
