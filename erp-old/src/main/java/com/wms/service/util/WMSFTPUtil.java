package com.wms.service.util;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.*;
import java.net.SocketException;

/**
 * <AUTHOR>
 * @ClassName WMSFTPUtil.java
 * @Description TODO WMSFTP连接
 * @createTime 2021年01月08日 14:06:00
 */
@Service
public class WMSFTPUtil {

    private static final Logger logger = LoggerFactory.getLogger(WMSFTPUtil.class);

    /**
     * FTP服务器IP地址
     */
    @Value("${wms_ftp_host}")
    private String wms_ftp_host;

    /**
     * FTP服务器端口
     */
    @Value("${wms_ftp_port}")
    private int wms_ftp_port;

    /**
     * FTP登录账号
     */
    @Value("${wms_ftp_user}")
    private String wms_ftp_user;

    /**
     * FTP登录密码
     */
    @Value("${wms_ftp_password}")
    private String wms_ftp_password;

    private static String encoding = System.getProperty("file.encoding");// 系統編碼
    private static String UTF8 = "UTF-8";// 本地字符编码
    private static String SERVER_CHARSET = "ISO-8859-1";// FTP协议里面，规定文件名编码为iso-8859-1

    /**
     * @description: 获取
     * @return: FTPClient
     * @author: Strange
     * @date: 2021/1/8
     **/
    public FTPClient getFTPClient() {
        FTPClient ftpClient = null;
        try {
            ftpClient = new FTPClient();
            // 连接FPT服务器,设置IP及端口
            ftpClient.connect(wms_ftp_host, wms_ftp_port);
            // 设置用户名和密码
            ftpClient.login(wms_ftp_user, wms_ftp_password);
            // 设置连接超时时间,5000毫秒
            ftpClient.setConnectTimeout(50000);
            // 设置中文编码集，防止中文乱码
            ftpClient.setControlEncoding("UTF-8");
            ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
            if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
                logger.info("未连接到WMSFTP，用户名或密码错误");
                ftpClient.disconnect();
            }

        } catch (SocketException e) {
            logger.error("WMSFTP的IP地址可能错误，请正确配置",e);
        } catch (IOException e) {
            logger.error("WMSFTP的端口错误,请正确配置",e);
        }
        return ftpClient;
    }
    /**
     * 关闭FTP方法
     * @param
     * @return
     */
    public boolean closeFTP(FTPClient ftpClient){
        try {
            ftpClient.logout();
        } catch (Exception e) {
            logger.error("WMSFTP关闭失败");
        }finally{
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ioe) {
                    logger.error("WMSFTP关闭失败",ioe);
                }
            }
        }
        return false;
    }


    /**
     * 下载FTP下指定文件
     * @param ftp FTPClient对象
     * @param filePath FTP文件路径
     * @param fileName 文件名
     * @param downPath 下载保存的目录
     * @return
     */
    public boolean downLoadFTP(FTPClient ftp, String filePath, String fileName,
                               String downPath) {
        // 默认失败
        boolean flag = false;

        try {
            // 跳转到文件目录
            ftp.changeWorkingDirectory(filePath);
            // 获取目录下文件集合
            ftp.enterLocalPassiveMode();
            FTPFile[] files = ftp.listFiles();
            for (FTPFile file : files) {
                // 取得指定文件并下载
                if (file.getName().equals(fileName)) {
                    File downFile = new File(downPath + File.separator
                            + file.getName());

                    OutputStream out = new FileOutputStream(downFile);
                    // 绑定输出流下载文件,需要设置编码集，不然可能出现文件为空的情况
                    flag = ftp.retrieveFile(new String(file.getName().getBytes(UTF8),SERVER_CHARSET), out);

                    // 下载成功删除文件,看项目需求
                    // ftp.deleteFile(new String(fileName.getBytes("UTF-8"),"ISO-8859-1"));
                    out.flush();
                    out.close();
                }
            }

        } catch (Exception e) {
            logger.error("下载失败");
        }

        return flag;
    }

    /**
     * @description: 获取指定文件的IO流
     * @return: InputStream
     * @author: Strange
     * @date: 2021/1/8
     **/
    public InputStream downLoadFile(FTPClient ftp, String filePath, String fileName){
        try {
            // 跳转到文件目录
            ftp.changeWorkingDirectory(StringUtils.isBlank(filePath) ? "/" : filePath);
            // 获取目录下文件集合
            ftp.enterLocalPassiveMode();
            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            return ftp.retrieveFileStream(new String(fileName.getBytes(UTF8),SERVER_CHARSET));
//            FTPFile[] files = ftp.listFiles();
//            for (FTPFile file : files) {
//                // 取得指定文件并下载
//                if (file.getName().equals(fileName)) {
//
//                    // 绑定输出流下载文件,需要设置编码集，不然可能出现文件为空的情况
//                    InputStream inputStream = ftp.retrieveFileStream(new String(file.getName().getBytes(UTF8), SERVER_CHARSET));
//                    return inputStream;
//                }
//            }

        } catch (Exception e) {
            logger.error("下载失败",e);
        }
        return null;
    }

    public InputStream parsePathDownLoadFile(FTPClient ftpClient, String path) {
        int index = path.lastIndexOf("/", path.length())+1;
        String filePath = path.substring(0, index);
        String fileName = path.substring(index, path.length());
        return downLoadFile(ftpClient,filePath,fileName);
    }
}
