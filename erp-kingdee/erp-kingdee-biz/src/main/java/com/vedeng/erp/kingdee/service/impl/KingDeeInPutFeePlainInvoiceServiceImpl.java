package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeInPutFeePlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInPutFeePlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.InPutFeePlainInvoiceDto;
import com.vedeng.erp.kingdee.dto.InPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeePlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeInPutFeePlainInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeInPutFeePlainInvoiceRepository;
import com.vedeng.erp.kingdee.service.KingDeeInPutFeePlainInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeInPutFeePlainInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2023/3/22 13:09
 **/
@Service
@Slf4j
public class KingDeeInPutFeePlainInvoiceServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeInPutFeePlainInvoiceEntity,
        InPutFeePlainInvoiceDto,
        KingDeeInPutFeePlainInvoiceCommand,
        KingDeeInPutFeePlainInvoiceRepository,
        KingDeeInPutFeePlainInvoiceConvertor,
        KingDeeInPutFeePlainInvoiceCommandConvertor
        > implements KingDeeInPutFeePlainInvoiceService, KingDeeInPutFeePlainInvoiceApiService {

    @Autowired
    private BatchInvoiceVoucherMapper batchInvoiceVoucherMapper;

    @Override
    public void savePost(Object... objects) {
        InPutFeePlainInvoiceDto d = getD(objects);
        // 存完票后新增推送记录
        BatchInvoiceVoucherDto build = BatchInvoiceVoucherDto.builder().invoiceId(d.getFQzokBddjtid()).build();
        List<BatchInvoiceVoucherDto> batchInvoiceVoucherDtos = batchInvoiceVoucherMapper.selectByInvoiceId(build.getInvoiceId());
        if (CollUtil.isEmpty(batchInvoiceVoucherDtos)) {
            log.info("记录采购费用蓝票推送金蝶{}", JSON.toJSONString(build));
            batchInvoiceVoucherMapper.insertSelective(build);
        }
    }
}
