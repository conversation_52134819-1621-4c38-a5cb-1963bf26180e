package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.SettlementNoticeRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SettlementNoticeRecordMapper {
    int deleteByPrimaryKey(Long settlementNoticeRecordId);

    int insert(SettlementNoticeRecordEntity record);

    int insertSelective(SettlementNoticeRecordEntity record);

    SettlementNoticeRecordEntity selectByPrimaryKey(Long settlementNoticeRecordId);

    int updateByPrimaryKeySelective(SettlementNoticeRecordEntity record);

    int updateByPrimaryKey(SettlementNoticeRecordEntity record);
    
    List<SettlementNoticeRecordEntity> querySettlementNoticeRecord(@Param("bankBillId") Integer bankBillId, @Param("noticeType") Integer noticeType);
}
