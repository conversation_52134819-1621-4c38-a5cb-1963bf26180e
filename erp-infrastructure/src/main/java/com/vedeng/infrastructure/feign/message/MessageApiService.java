package com.vedeng.infrastructure.feign.message;
 
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.infrastructure.message.dto.MessageDto;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/18
 */
@FeignApi(serverName = "erpServer")
public interface MessageApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/message/send.do")
    RestfulResult<?> send(@RequestBody MessageDto messageDto);

}
