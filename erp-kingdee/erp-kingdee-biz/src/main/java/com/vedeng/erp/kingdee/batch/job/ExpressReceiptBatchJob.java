package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.enums.CostActEnum;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchExpressReceiptBasicDataDto;
import com.vedeng.erp.kingdee.batch.processor.BatchExpressReceiptAddDataProcessor;
import com.vedeng.erp.kingdee.batch.processor.BatchExpressReceiptUpdateDataProcessor;
import com.vedeng.erp.kingdee.batch.writer.BatchExpressReceiptAddDataWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchExpressReceiptUpdateDataWriter;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收推送金蝶job
 * @date 2023/04/14 10:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class ExpressReceiptBatchJob extends BaseJob {


    @Autowired
    private BatchExpressReceiptAddDataProcessor batchExpressReceiptAddDataProcessor;

    @Autowired
    private BatchExpressReceiptUpdateDataProcessor batchExpressReceiptUpdateDataProcessor;

    @Autowired
    private BatchExpressReceiptAddDataWriter batchExpressReceiptAddDataWriter;

    @Autowired
    private BatchExpressReceiptUpdateDataWriter batchExpressReceiptUpdateDataWriter;


    public Job expressReceiptFlowJob() {
        return jobBuilderFactory.get("expressReceiptFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(expressReceiptAddData())
                .next(expressReceiptUpdateData())
                .build();
    }

    public Job expressReceiptFlowOnlyAddJob() {
        return jobBuilderFactory.get("expressReceiptFlowJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(expressReceiptAddData())
//                .next(expressReceiptUpdateData())
                .build();
    }


    private Step expressReceiptAddData() {
        return stepBuilderFactory.get("快递签收-ADD类型")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchExpressReceiptBasicDataDto, KingDeeExpressReceiptDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchExpressReceiptAddDataReader(null,null))
                .processor(batchExpressReceiptAddDataProcessor)
                .writer(batchExpressReceiptAddDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step expressReceiptUpdateData() {
        return stepBuilderFactory.get("快递签收-UPDATE类型")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchExpressReceiptBasicDataDto, KingDeeExpressReceiptDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchExpressReceiptUpdateDataReader(null,null))
                .processor(batchExpressReceiptUpdateDataProcessor)
                .writer(batchExpressReceiptUpdateDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchExpressReceiptBasicDataDto> batchExpressReceiptAddDataReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        return getBatchExpressReceiptBasicDataDtoCommonMybatisItemReader(CollUtil.newArrayList(CostActEnum.ACT_ADD.getCode()), beginTime, endTime);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchExpressReceiptBasicDataDto> batchExpressReceiptUpdateDataReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        return getBatchExpressReceiptBasicDataDtoCommonMybatisItemReader(CollUtil.newArrayList(CostActEnum.ACT_UPDATE.getCode(), CostActEnum.ACT_DELETE.getCode()), beginTime, endTime);
    }

    private CommonMybatisItemReader<BatchExpressReceiptBasicDataDto> getBatchExpressReceiptBasicDataDtoCommonMybatisItemReader(ArrayList<Integer> actFlag, String beginTime, String endTime) {
        BatchExpressReceiptBasicDataDto expressReceiptBasicDataDto = BatchExpressReceiptBasicDataDto
                .builder()
                .arrivalActFlagList(actFlag)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchExpressReceiptBasicDataDto.class.getSimpleName(), "findByArrivalActFlagListAndAddTimeAndIsPushKingDee", expressReceiptBasicDataDto);
    }
}

