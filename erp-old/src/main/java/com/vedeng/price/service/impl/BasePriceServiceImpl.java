package com.vedeng.price.service.impl;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.price.api.price.dto.price.FilterContractSkuReqDto;
import com.vedeng.price.api.price.dto.price.FilterContractSkuResDto;
import com.vedeng.price.api.price.dto.price.PriceInfoRequestDto;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.ContractPriceInfoDetailResponseDto;
import com.vedeng.price.dto.PriceType;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.dto.SkuPriceInfoPurchaseDto;
import com.vedeng.price.service.BasePriceService;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.vedeng.common.http.NewHttpClientUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BasePriceServiceImpl implements BasePriceService {

    @Value("${price.url}")
    private String priceUrl;

    private static Logger LOGGER = LoggerFactory.getLogger(BasePriceServiceImpl.class);

    private static String SUCCESS_CODE = "success";

    private static String FIND_SKUPRICEINFO_BYSKUNO = "sku_price_info/findSkuPriceInfoBySkuNo";

    private static String FIND_SKUCONTRACT_BYSKUNO = "sku_price_info/findContractPriceByCustomerIdAndSkuNo";

    private static String BATCH_FIND_PRICEINFO = "sku_price_info/batchFindPriceInfo";

    private static String FIND_PRICEINFO_BYSKU = "sku_price_info/findPriceInfo";

    private static final String RESULT_CODE_NAME = "code";
    private static final String RESULT_DATA_NAME ="data";

    /**
     * 过滤客户的协议商品URL
     */
    private static String FILTER_CONTRACTED_GOODS = "sku_price_info/filterContractSkus";


    @Override
    public SkuPriceInfoDetailResponseDto findSkuPriceInfoBySkuNo(String skuNo) {

        SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = null;

        try {

            //封装请求参数
            Map<String,Object> requestMap=new HashMap<String, Object>();
            requestMap.put("skuNo",skuNo);
            String requestJson = JsonUtils.translateToJson(requestMap);

            //LOGGER.info("BasePriceServiceImpl->findSkuPriceInfoBySkuNo 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + FIND_SKUPRICEINFO_BYSKUNO,requestJson);

           // LOGGER.info("BasePriceServiceImpl->findSkuPriceInfoBySkuNo 响应:" + resultJsonObj.toString());

            if(null==resultJsonObj ||!SUCCESS_CODE.equals(resultJsonObj.get("code"))||null==resultJsonObj.getJSONObject("data")){
                return skuPriceInfoDetailResponseDto;
            }

            Gson gson = new Gson();
            skuPriceInfoDetailResponseDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<SkuPriceInfoDetailResponseDto>(){}.getType());


        } catch (Exception e) {
            LOGGER.error("获取skuNo核价基本信息失败"+skuNo,e);
        }

        return skuPriceInfoDetailResponseDto;
    }

    @Override
    public ContractPriceInfoDetailResponseDto findSkuContractInfoByCon(Integer traderId, String skuNo) {

        ContractPriceInfoDetailResponseDto contractPriceInfoDetailResponseDto = null;

        try {

            //封装请求参数
            Map<String,Object> requestMap=new HashMap<String, Object>();
            requestMap.put("skuNo",skuNo);
            requestMap.put("customerId",traderId);
            String requestJson = JsonUtils.translateToJson(requestMap);

           // LOGGER.info("BasePriceServiceImpl->findSkuContractInfoByCon 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + FIND_SKUCONTRACT_BYSKUNO,requestJson);

          //  LOGGER.info("BasePriceServiceImpl->findSkuContractInfoByCon 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code")) || resultJsonObj.getJSONObject("data") == null){
                return contractPriceInfoDetailResponseDto;
            }

            Gson gson = new Gson();
            contractPriceInfoDetailResponseDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<ContractPriceInfoDetailResponseDto>(){}.getType());

        } catch (Exception e) {
            LOGGER.error("获取skuNo核价基本信息失败"+traderId+skuNo,e);
        }

        return contractPriceInfoDetailResponseDto;
    }

    @Override
    public List<PriceInfoResponseDto> batchFindPriceInfo(List<String> skuNos, Integer traderId) {

        List<PriceInfoResponseDto> responseList = null;

        List<PriceInfoRequestDto> requestList = new ArrayList<>();

        skuNos.stream().forEach(skuNo -> {

            PriceInfoRequestDto priceInfoRequestDto = new PriceInfoRequestDto();
            priceInfoRequestDto.setSkuNo(skuNo);
            priceInfoRequestDto.setTraderId(traderId);

            requestList.add(priceInfoRequestDto);
        });

        try {

            //封装请求参数
            String requestJson = JsonUtils.translateToJson(requestList);

            LOGGER.info("BasePriceServiceImpl->batchFindPriceInfo 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + BATCH_FIND_PRICEINFO,requestJson);

            LOGGER.info("BasePriceServiceImpl->batchFindPriceInfo 响应: {}",resultJsonObj==null?"": resultJsonObj.toString());

            if(resultJsonObj==null||!SUCCESS_CODE.equals(resultJsonObj.get("code"))){
                return processDefaultResponse(skuNos,traderId);
            }

            Gson gson = new Gson();
            responseList = gson.fromJson(resultJsonObj.getJSONArray("data").toString(),
                    new TypeToken<List<PriceInfoResponseDto>>(){}.getType());


        } catch (Exception e) {
            LOGGER.error("批量获取skuNo核价基本信息失败",e);
        }

        return responseList;
    }

    private List<PriceInfoResponseDto> processDefaultResponse(List<String> skuNos, Integer traderId) {
        return skuNos.stream().map(skuNo -> {
            PriceInfoResponseDto priceInfoResponseDto = new PriceInfoResponseDto();
            priceInfoResponseDto.setPriceType(PriceType.UN_PRICEED);
            priceInfoResponseDto.setSkuNo(skuNo);
            priceInfoResponseDto.setTraderId(traderId);
            return priceInfoResponseDto;
        }).collect(Collectors.toList());
    }

    @Override
    public PriceInfoResponseDto findPriceInfo(String skuNo, Integer traderId) {

        PriceInfoRequestDto priceInfoRequestDto = new PriceInfoRequestDto();
        priceInfoRequestDto.setSkuNo(skuNo);
        priceInfoRequestDto.setTraderId(traderId);

        PriceInfoResponseDto priceInfoResponseDto = null;

        try {

            //封装请求参数
            String requestJson = JsonUtils.translateToJson(priceInfoRequestDto);

            LOGGER.info("BasePriceServiceImpl->findPriceInfo 请求参数:" + requestJson);

            JSONObject resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + FIND_PRICEINFO_BYSKU,requestJson);

            LOGGER.info("BasePriceServiceImpl->findPriceInfo 响应:" + resultJsonObj.toString());

            if(!SUCCESS_CODE.equals(resultJsonObj.get("code"))){
                return returnDefaultResponse(skuNo,traderId);
            }

            Gson gson = new Gson();
            priceInfoResponseDto = gson.fromJson(resultJsonObj.getJSONObject("data").toString(),
                    new TypeToken<PriceInfoResponseDto>(){}.getType());

        } catch (Exception e) {
            LOGGER.error("取skuNo核价基本信息失败",e);
            return returnDefaultResponse(skuNo,traderId);
        }

        return priceInfoResponseDto;
    }

    private PriceInfoResponseDto returnDefaultResponse(String skuNo, Integer traderId) {
        PriceInfoResponseDto priceInfoResponseDto = new PriceInfoResponseDto();
        priceInfoResponseDto.setPriceType(PriceType.UN_PRICEED);
        priceInfoResponseDto.setSkuNo(skuNo);
        priceInfoResponseDto.setTraderId(traderId);
        return priceInfoResponseDto;
    }

    @Override
    public  List<FilterContractSkuResDto> listFilteredGoodsContractPrice(Integer traderId, List<String> skuNoList) {
        if (traderId == null || CollectionUtils.isEmpty(skuNoList)) {
            return null;
        }

        LOGGER.info("BasePriceServiceImpl->listFilteredGoodsContractPrice 请求参数 - traderId:{}, skuNos:{}" ,traderId, skuNoList);

        FilterContractSkuReqDto contractSkuReqDto = new FilterContractSkuReqDto();
        contractSkuReqDto.setCustomerId(traderId);
        contractSkuReqDto.setSkuNos(skuNoList.toArray(new String[0]));

        JSONObject resultJsonObj = null;
        try {
            String requestJson = JsonUtils.translateToJson(contractSkuReqDto);
            resultJsonObj = NewHttpClientUtils.httpPost(priceUrl + FILTER_CONTRACTED_GOODS, requestJson);
        } catch (Exception e) {
            LOGGER.error("调用价格中心过滤客户的协议商品接口时发生错误", e);
        }

        if(resultJsonObj == null){
            return null;
        }

        LOGGER.info("BasePriceServiceImpl->listFilteredGoodsContractPrice 响应:" + resultJsonObj.toString());

        List<FilterContractSkuResDto> resultList = null;
        if (SUCCESS_CODE.equals(resultJsonObj.get(RESULT_CODE_NAME))) {
            try {
                resultList  = JSON.parseArray(resultJsonObj.getString(RESULT_DATA_NAME), FilterContractSkuResDto.class);
            } catch(Exception e) {
                LOGGER.error("解析过价格中心返回数据时发生错误", e);
            }
        }

        return resultList;
    }
}
