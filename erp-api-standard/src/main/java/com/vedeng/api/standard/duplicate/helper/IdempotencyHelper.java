package com.vedeng.api.standard.duplicate.helper;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.api.standard.duplicate.entity.IdempotencyRecord;
import com.vedeng.api.standard.duplicate.service.IdempotencyRecordService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 幂等性集成助手
 * 专门处理幂等性异常、响应构建和记录状态管理
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-15
 */
@Component
public class IdempotencyHelper {
    
    private static final Logger logger = LoggerFactory.getLogger(IdempotencyHelper.class);
    
    @Autowired
    private IdempotencyRecordService idempotencyRecordService;
    

    /**
     * 构建幂等性响应
     * 
     * @param businessResponse 业务响应或幂等性记录
     * @param isNewCreated 是否为新创建
     * @param businessType 业务类型名称
     * @return 增强后的响应
     */
    public Object buildIdempotentResponse(Object businessResponse, boolean isNewCreated, String businessType) {
        Map<String, Object> responseData = new HashMap<>();
        
        try {
            // 合并业务响应数据
            mergeBusinessData(responseData, businessResponse);
            
            // 添加幂等性元数据
            addIdempotencyMetadata(responseData, isNewCreated, businessType);
            
        } catch (Exception e) {
            logger.error("构建幂等性响应失败: businessType={}, isNewCreated={}", 
                businessType, isNewCreated, e);
            
            // 构建最小响应
            responseData.clear();
            buildMinimalResponse(responseData, isNewCreated, businessType, e.getMessage());
        }
        
        return responseData;
    }
    

    /**
     * 更新幂等性记录为成功状态（指定字段名）
     *
     * @param idempotencyKey 幂等性键
     * @param response 响应对象
     * @param idFieldName ID字段名（可以为null表示不提取）
     * @param noFieldName 编号字段名（可以为null表示不提取）
     */
    public void updateIdempotencySuccess(String idempotencyKey, Object response, String idFieldName, String noFieldName) {
        if (StringUtils.isEmpty(idempotencyKey)) {
            return;
        }

        try {
            String documentId = extractField(response, idFieldName);
            String documentNo = extractField(response, noFieldName);

            idempotencyRecordService.updateToSuccess(idempotencyKey, response, documentId, documentNo);

            logger.info("幂等性记录更新成功: key={}, documentId={}, documentNo={}", idempotencyKey, documentId, documentNo);

        } catch (Exception e) {
            logger.error("更新幂等性记录失败: key={}", idempotencyKey, e);
            // 不抛出异常，避免影响主业务流程
        }
    }
    
    /**
     * 更新幂等性记录为失败状态
     * 
     * @param idempotencyKey 幂等性键
     * @param exception 异常信息
     */
    public void updateIdempotencyFailure(String idempotencyKey, Exception exception) {
        if (StringUtils.isEmpty(idempotencyKey)) {
            return;
        }
        
        try {
            idempotencyRecordService.updateToFailure(idempotencyKey, exception);
            logger.info("幂等性记录更新为失败: key={}, error={}", 
                idempotencyKey, exception != null ? exception.getMessage() : "未知错误");
        } catch (Exception e) {
            logger.warn("更新幂等性失败状态失败: key={}", idempotencyKey, e);
            // 不抛出异常，避免影响主业务流程
        }
    }
    
    /**
     * 通用字段提取方法
     * 支持从Map、Bean等多种类型中提取指定字段值
     *
     * @param response 响应对象
     * @param fieldName 字段名（如果为null则返回null）
     * @return 字段值的字符串表示
     */
    private String extractField(Object response, String fieldName) {
        if (response == null || fieldName == null) {
            return null;
        }

        try {
            if (response instanceof Map) {
                Map<String, Object> map = (Map<String, Object>) response;
                Object value = map.get(fieldName);
                return value != null ? String.valueOf(value) : null;
            }
            
            // 支持Bean对象的字段提取
            try {
                Map<String, Object> beanMap = BeanUtil.beanToMap(response);
                if (beanMap != null) {
                    Object value = beanMap.get(fieldName);
                    return value != null ? String.valueOf(value) : null;
                }
            } catch (Exception beanException) {
                logger.debug("Bean转Map失败，尝试直接反射: fieldName={}", fieldName);
            }

        } catch (Exception e) {
            logger.warn("提取字段失败: fieldName={}, responseType={}", 
                fieldName, response.getClass().getSimpleName(), e);
        }

        return null;
    }
    
    /**
     * 合并业务响应数据到结果Map中
     * 
     * @param responseData 结果Map
     * @param businessResponse 业务响应对象
     */
    private void mergeBusinessData(Map<String, Object> responseData, Object businessResponse) {
        if (businessResponse instanceof IdempotencyRecord) {
            // 从幂等性记录构建响应
            IdempotencyRecord record = (IdempotencyRecord) businessResponse;
            responseData.put("documentId", record.getBusinessDocumentId());
            responseData.put("documentNo", record.getBusinessDocumentNo());
            
            // 尝试解析原始响应数据
            if (!StringUtils.isEmpty(record.getResponseData())) {
                try {
                    Map<String, Object> originalResponse = JSON.parseObject(record.getResponseData(), Map.class);
                    if (originalResponse != null) {
                        responseData.putAll(originalResponse);
                    }
                } catch (Exception e) {
                    logger.warn("解析原始响应数据失败: key={}", record.getIdempotencyKey(), e);
                }
            }
        } else if (businessResponse != null) {
            // 从业务响应构建
            if (businessResponse instanceof Map) {
                responseData.putAll((Map<String, Object>) businessResponse);
            } else {
                // 使用BeanUtil转换为Map
                Map<String, Object> beanMap = BeanUtil.beanToMap(businessResponse);
                if (beanMap != null) {
                    responseData.putAll(beanMap);
                }
            }
        }
    }
    
    /**
     * 添加幂等性元数据
     * 
     * @param responseData 结果Map
     * @param isNewCreated 是否新创建
     * @param businessType 业务类型
     */
    private void addIdempotencyMetadata(Map<String, Object> responseData, boolean isNewCreated, String businessType) {
        responseData.put("isNewCreated", isNewCreated);
        responseData.put("idempotencyStatus", isNewCreated ? "NEW_CREATED" : "EXISTING_RETURNED");
        responseData.put("businessType", businessType);
        responseData.put("message", isNewCreated ? 
            businessType + "创建成功" : businessType + "已存在，返回现有记录");
        responseData.put("timestamp", System.currentTimeMillis());
    }
    
    /**
     * 构建最小响应（异常情况下使用）
     * 
     * @param responseData 结果Map
     * @param isNewCreated 是否新创建
     * @param businessType 业务类型
     * @param errorMessage 错误信息
     */
    private void buildMinimalResponse(Map<String, Object> responseData, boolean isNewCreated, 
                                    String businessType, String errorMessage) {
        responseData.put("isNewCreated", isNewCreated);
        responseData.put("idempotencyStatus", isNewCreated ? "NEW_CREATED" : "EXISTING_RETURNED");
        responseData.put("businessType", businessType);
        responseData.put("message", "响应构建异常，但操作" + (isNewCreated ? "成功" : "已存在"));
        responseData.put("error", errorMessage);
        responseData.put("timestamp", System.currentTimeMillis());
    }
    

}
