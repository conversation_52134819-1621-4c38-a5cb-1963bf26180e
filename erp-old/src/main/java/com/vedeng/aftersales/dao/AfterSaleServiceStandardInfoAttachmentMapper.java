package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleServiceStandardInfoAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSaleServiceStandardInfoAttachmentMapper {
    int deleteByPrimaryKey(Long serviceStandardInfoAttachmentId);

    int insert(AfterSaleServiceStandardInfoAttachment record);

    int insertSelective(AfterSaleServiceStandardInfoAttachment record);

    AfterSaleServiceStandardInfoAttachment selectByPrimaryKey(Long serviceStandardInfoAttachmentId);

    int updateByPrimaryKeySelective(AfterSaleServiceStandardInfoAttachment record);

    int updateByPrimaryKey(AfterSaleServiceStandardInfoAttachment record);

    void deleteByServiceStandardInfoId(Long serviceStandardInfoId);

    void batchInsertAttashment(List<AfterSaleServiceStandardInfoAttachment> attashmentList);

    List<AfterSaleServiceStandardInfoAttachment> selectByStandardInfoId(@Param("serviceStandardInfoId") Long serviceStandardInfoId);
}