package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后的负向费用应收单组装
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchSaleOrderAfterSaleExpenseReceivableProcessor implements ItemProcessor<BatchInvoiceDto, KingDeeReceiveFeeDto> {


    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    @Autowired
    private KingDeeReceiveFeeService kingDeeReceiveFeeService;

    @Override
    public KingDeeReceiveFeeDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        log.info("销售单的负向费用应收单{}", JSON.toJSONString(batchInvoiceDto));

        if (StrUtil.isBlank(batchInvoiceDto.getInvoiceNo()) || StrUtil.isBlank(batchInvoiceDto.getInvoiceCode())) {
            log.warn("红票无发票号或者发票代码");
            return null;
        }
        // 判断费用应收数据是否存在
        List<KingDeeReceiveQueryResultDto> kingDeeSaleReceivable = kingDeeReceiveFeeService.getKingDeeReceiveFee(batchInvoiceDto.getInvoiceId().toString());
        if (CollUtil.isNotEmpty(kingDeeSaleReceivable)) {
            log.info("销售单的负向费用应收单,数据已存在:{}", JSON.toJSONString(batchInvoiceDto));
            return null;
        }

        // 设置发票明细数据,虚拟商品明细
        List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList;
        if (CollectionUtils.isEmpty(batchInvoiceDto.getBatchInvoiceDetailDtoList())) {
            // 发票明细为空，则表示是非历史数据，直接取数据库查询发票明细
            log.info("开始处理销售负向费用应收单发票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        } else {
            // 否则，则证明是从Excel中读取的发票明细
            log.info("开始处理21-22历史销售负向费用应收单发票明细：{}", JSON.toJSONString(batchInvoiceDto));
            saleOrderInvoiceDetailList = batchInvoiceDto.getBatchInvoiceDetailDtoList();
        }
        saleOrderInvoiceDetailList = saleOrderInvoiceDetailList.stream().filter(detailDto -> Convert.toBool(detailDto.getIsVirtureSku(), false)).collect(Collectors.toList());

        long count = saleOrderInvoiceDetailList.stream().filter(detailDto -> StrUtil.isBlank(detailDto.getUnitKingDeeNo())).count();
        if (count > 0) {
            log.warn("销售单的负向费用应收单,金蝶费用编号为空:{}", JSON.toJSONString(saleOrderInvoiceDetailList));
            return null;
        }

        // 构建负数应收单对象
        KingDeeReceiveFeeDto dto = new KingDeeReceiveFeeDto();
        dto.setFQzokBddjtid(batchInvoiceDto.getInvoiceId().toString());
        dto.setFdate(DateUtil.formatDate(DateUtil.date(batchInvoiceDto.getAddTime())));
        dto.setFid("0");
        dto.setFcustomerid(batchInvoiceDto.getTraderCustomerId().toString());
        BigDecimal taxRate = Objects.isNull(batchInvoiceDto.getRatio()) ? BigDecimal.ZERO : batchInvoiceDto.getRatio().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
        List<KingDeeReceiveFeeDetailDto> kingDeeReceiveFeeDetailDtoList = dto.getFEntityDetail();

        this.assembleOrdinaryGoodsInfo(saleOrderInvoiceDetailList, taxRate, kingDeeReceiveFeeDetailDtoList);
        if (CollUtil.isEmpty(kingDeeReceiveFeeDetailDtoList)) {
            return null;
        }
        return dto;
    }

    /**
     * 组装金蝶普通商品费用应收单
     *
     * @param taxRate
     * @param kingDeeReceiveFeeDetailDtoList
     */
    private void assembleOrdinaryGoodsInfo(List<BatchInvoiceDetailDto> saleOrderInvoiceDetailList,
                                           BigDecimal taxRate,
                                           List<KingDeeReceiveFeeDetailDto> kingDeeReceiveFeeDetailDtoList) {
        saleOrderInvoiceDetailList.forEach(detailDto -> {
            KingDeeReceiveFeeDetailDto kingDeeReceiveFeeDetailDto = new KingDeeReceiveFeeDetailDto()
                    .setFcostid(detailDto.getUnitKingDeeNo())
                    .setFPriceQty(detailDto.getNum().negate().toString())
                    .setFTaxPrice(detailDto.getTotalAmount().divide(detailDto.getNum(), 2, RoundingMode.HALF_UP).abs().toString())
                    .setFEntryTaxRate(taxRate.toString())
                    .setFQzokBddjhid(detailDto.getInvoiceDetailId().toString());
            kingDeeReceiveFeeDetailDtoList.add(kingDeeReceiveFeeDetailDto);
        });
    }

}
