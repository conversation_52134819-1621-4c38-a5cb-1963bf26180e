<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.repository.mappers.KingDeeSaleOutStockMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity">
    <id column="KING_DEE_SALE_OUT_STOCK_ID" jdbcType="INTEGER" property="kingDeeSaleOutStockId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="FID" jdbcType="VARCHAR" property="fid" />
    <result column="F_BILL_TYPE_ID" jdbcType="VARCHAR" property="fBillTypeId" />
    <result column="F_BILL_NO" jdbcType="VARCHAR" property="fBillNo" />
    <result column="F_QZOK_BDDJTID" jdbcType="VARCHAR" property="fQzokBddjtid" />
    <result column="F_QZOK_GSBM" jdbcType="VARCHAR" property="fQzokGsbm" />
    <result column="F_DATE" jdbcType="VARCHAR" property="fDate" />
    <result column="F_SALE_ORG_ID" jdbcType="VARCHAR" property="fSaleOrgId" />
    <result column="F_STOCK_ORG_ID" jdbcType="VARCHAR" property="fStockOrgId" />
    <result column="F_CUSTOMER_ID" jdbcType="VARCHAR" property="fCustomerId" />
    <result column="F_ENTITY" property="fEntity"
            typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
  </resultMap>
  <sql id="Base_Column_List">
    KING_DEE_SALE_OUT_STOCK_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    FID, F_BILL_TYPE_ID, F_BILL_NO, F_QZOK_BDDJTID, F_QZOK_GSBM, F_DATE, F_SALE_ORG_ID, 
    F_STOCK_ORG_ID, F_CUSTOMER_ID, F_ENTITY
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from KING_DEE_SALE_OUT_STOCK
    where KING_DEE_SALE_OUT_STOCK_ID = #{kingDeeSaleOutStockId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from KING_DEE_SALE_OUT_STOCK
    where KING_DEE_SALE_OUT_STOCK_ID = #{kingDeeSaleOutStockId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="KING_DEE_SALE_OUT_STOCK_ID" keyProperty="kingDeeSaleOutStockId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity" useGeneratedKeys="true">
    insert into KING_DEE_SALE_OUT_STOCK (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      FID, F_BILL_TYPE_ID, F_BILL_NO, 
      F_QZOK_BDDJTID, F_QZOK_GSBM, F_DATE, 
      F_SALE_ORG_ID, F_STOCK_ORG_ID, F_CUSTOMER_ID, 
      F_ENTITY)
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{fid,jdbcType=VARCHAR}, #{fBillTypeId,jdbcType=VARCHAR}, #{fBillNo,jdbcType=VARCHAR}, 
      #{fQzokBddjtid,jdbcType=VARCHAR}, #{fQzokGsbm,jdbcType=VARCHAR}, #{fDate,jdbcType=VARCHAR}, 
      #{fSaleOrgId,jdbcType=VARCHAR}, #{fStockOrgId,jdbcType=VARCHAR}, #{fCustomerId,jdbcType=VARCHAR}, 
      #{fEntity,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="KING_DEE_SALE_OUT_STOCK_ID" keyProperty="kingDeeSaleOutStockId" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity" useGeneratedKeys="true">
    insert into KING_DEE_SALE_OUT_STOCK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="fid != null">
        FID,
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID,
      </if>
      <if test="fBillNo != null">
        F_BILL_NO,
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID,
      </if>
      <if test="fQzokGsbm != null">
        F_QZOK_GSBM,
      </if>
      <if test="fDate != null">
        F_DATE,
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID,
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID,
      </if>
      <if test="fCustomerId != null">
        F_CUSTOMER_ID,
      </if>
      <if test="fEntity != null">
        F_ENTITY,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsbm != null">
        #{fQzokGsbm,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fCustomerId != null">
        #{fCustomerId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        #{fEntity,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity">
    update KING_DEE_SALE_OUT_STOCK
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="fid != null">
        FID = #{fid,jdbcType=VARCHAR},
      </if>
      <if test="fBillTypeId != null">
        F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      </if>
      <if test="fBillNo != null">
        F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      </if>
      <if test="fQzokBddjtid != null">
        F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      </if>
      <if test="fQzokGsbm != null">
        F_QZOK_GSBM = #{fQzokGsbm,jdbcType=VARCHAR},
      </if>
      <if test="fDate != null">
        F_DATE = #{fDate,jdbcType=VARCHAR},
      </if>
      <if test="fSaleOrgId != null">
        F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fStockOrgId != null">
        F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      </if>
      <if test="fCustomerId != null">
        F_CUSTOMER_ID = #{fCustomerId,jdbcType=VARCHAR},
      </if>
      <if test="fEntity != null">
        F_ENTITY = #{fEntity,jdbcType=VARCHAR},
      </if>
    </set>
    where KING_DEE_SALE_OUT_STOCK_ID = #{kingDeeSaleOutStockId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.domain.entity.KingDeeSaleOutStockEntity">
    update KING_DEE_SALE_OUT_STOCK
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      FID = #{fid,jdbcType=VARCHAR},
      F_BILL_TYPE_ID = #{fBillTypeId,jdbcType=VARCHAR},
      F_BILL_NO = #{fBillNo,jdbcType=VARCHAR},
      F_QZOK_BDDJTID = #{fQzokBddjtid,jdbcType=VARCHAR},
      F_QZOK_GSBM = #{fQzokGsbm,jdbcType=VARCHAR},
      F_DATE = #{fDate,jdbcType=VARCHAR},
      F_SALE_ORG_ID = #{fSaleOrgId,jdbcType=VARCHAR},
      F_STOCK_ORG_ID = #{fStockOrgId,jdbcType=VARCHAR},
      F_CUSTOMER_ID = #{fCustomerId,jdbcType=VARCHAR},
      F_ENTITY = #{fEntity,jdbcType=VARCHAR}
    where KING_DEE_SALE_OUT_STOCK_ID = #{kingDeeSaleOutStockId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-01-10-->
  <select id="findByFBillNo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from KING_DEE_SALE_OUT_STOCK
    where F_BILL_NO=#{fBillNo,jdbcType=VARCHAR}
  </select>
</mapper>