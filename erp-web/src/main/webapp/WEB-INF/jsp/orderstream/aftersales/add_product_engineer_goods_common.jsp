<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>

<table class="table  table-style10">
    <thead>
    <tr>
        <th class="wid5">序号</th>
        <th class="wid8">SKU</th>
        <th >产品名称</th>
        <th class="wid10">品牌</th>
        <th class="wid8">型号</th>
        <th class="wid8">物料编号</th>
        <th class="wid8">采购订单</th>
        <th class="wid5">销售价</th>
        <th class="wid8">数量</th>
        <th class="wid5">单位</th>
        <th class="wid6">售后数量</th>
        <th class="wid10">是否含安调</th>
    </tr>
    </thead>
    <tbody>
    <c:choose>
        <c:when test="${afterSalesVo.status eq 2 }">
            <c:if test="${not empty afterSalesGoodsVoListPage}">
                <c:forEach items="${afterSalesGoodsVoListPage}" var="asg" varStatus="sttaus">
                    <tr class="J-skuInfo-tr" skuId="${asg.goodsId}">
                        <td>${sttaus.count }</td>
                        <td class="JskuCode"> </td>

                        <td class="text-left">
                            <div class="customername pos_rel" style="text-align: center;">
                                <c:if test="${asg.isActionGoods==1}"><span style="color:red;">【活动】</span></c:if>
                                <span class="brand-color1 addtitle JskuName" style="float:none;"
                                      tabTitle='{"num":"viewgoods${list.goodsId}","link":"./order/afterSalesCommon/viewbaseinfo.do?goodsId=${asg.goodsId}",
	                                       					"title":"产品信息"}'>${asg.goodsName}</span><i class="iconbluemouth"></i>

                                <div class="pos_abs customernameshow JskuInfo"   style="display: none;"></div>
                            </div>
                        </td>
                        <td class="JbrandName">  </td>
                        <td  class="JskuModel">
                        </td>
                        <td class="JmaterialCode"> </td>
                        <td>

                            <c:if test="${not empty asg.buyorderNos}">
                                <c:forEach items="${asg.buyorderNos}" var="buyorder">
                                    ${buyorder.buyorderNo}<br>
                                </c:forEach>
                            </c:if>


                        </td>
                        <td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td>${asg.saleorderNum}</td>
                        <td class="JskuUnit"> </td>
                        <td>${asg.num}</td>
                        <td>
                            <c:choose>
                                <c:when test="${asg.haveInstallation eq 0}">
                                    否
                                </c:when>
                                <c:otherwise>
                                    是
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesGoodsVoListPage}">
                <tr>
                    <td colspan="11">暂无记录！</td>
                </tr>
            </c:if>
        </c:when>
        <c:when test="${(afterSalesVo.status eq 0 || afterSalesVo.status eq 3)}">
            <c:if test="${not empty afterSalesVo.afterSalesGoodsList}">
                <c:forEach items="${afterSalesVo.afterSalesGoodsList}" var="asg" varStatus="sttaus">
                    <tr class="J-skuInfo-tr" skuId="${asg.goodsId}">
                        <td>${sttaus.count }</td>
                        <td class="JskuCode"> </td>

                        <td class="text-left">
                            <div class="customername pos_rel">
                                <c:if test="${asg.isActionGoods==1}"><span style="color:red;">【活动】</span></c:if>
                                <span class="brand-color1 addtitle JskuName" style="float:none;"
                                      tabTitle='{"num":"viewgoods${list.goodsId}","link":"<%= basePath %>/goods/goods/viewbaseinfo.do?goodsId=${asg.goodsId}",
	                                       					"title":"产品信息"}'>${asg.goodsName}</span><i class="iconbluemouth"></i>

                                <div class="pos_abs customernameshow JskuInfo"   style="display: none;"></div>
                            </div>
                        </td>
                        <td class="JbrandName">  </td>
                        <td  class="JskuModel">
                        </td>
                        <td class="JmaterialCode"> </td>
                        <td>

                            <c:if test="${not empty asg.buyorderNos}">
                                <c:forEach items="${asg.buyorderNos}" var="buyorder">
                                    <a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"viewbuyorder<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
													"link":"./order/buyorder/viewBuyordersh.do?buyorderId=${buyorder.buyorderId}","title":"订单信息"}'>${buyorder.buyorderNo}</a><br>
                                </c:forEach>
                            </c:if>


                        </td>
                        <td><fmt:formatNumber type="number" value="${asg.saleorderPrice}" pattern="0.00" maxFractionDigits="2" /></td>
                        <td>${asg.saleorderNum}</td>
                        <td class="JskuUnit"> </td>
                        <td>${asg.num}</td>
                        <td>
                            <c:choose>
                                <c:when test="${asg.haveInstallation eq 0}">
                                    否
                                </c:when>
                                <c:otherwise>
                                    是
                                </c:otherwise>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            <c:if test="${empty afterSalesVo.afterSalesGoodsList}">
                <tr>
                    <td colspan="11">暂无记录！</td>
                </tr>
            </c:if>
        </c:when>
    </c:choose>
    </tbody>
</table>

<script type="application/javascript">
    //物料编码、注册证号（对应spu的“注册证号”）、管理类别、产品负责人（字段内容改为：该商品对应spu的归属产品经理&归属产品助理）、采购提醒、包装清单、服务条款、审核状态
    $(function(){
        $(".J-skuInfo-tr").each(function(){
            var tr=$(this)
            $.getJSON("/goods/vgoods/static/skuTip.do?skuId="+$(this).attr("skuId"),function(result){
                console.log(result.data)
                var data=result.data
                var goodsInfo  ='物料编号：'+data.MATERIAL_CODE;
                goodsInfo +='<br>注册证号：'+data.REGISTRATION_NUMBER;
                goodsInfo +='<br>管理类别：'+data.MANAGE_CATEGORY_LEVEL;
                goodsInfo +='<br>产品负责人：'+data.PRODUCTMANAGER;
                goodsInfo +='<br>包装清单：'+data.PACKING_LIST;
                goodsInfo +='<br>质保年限：'+(data.QA_YEARS==undefined || data.QA_YEARS==''?'':data.QA_YEARS+'年') ;

                goodsInfo +='<br>库存：'+data.STOCKNUM;
                goodsInfo +='<br>可用库存：'+data.AVAILABLESTOCKNUM;
                goodsInfo +='<br>订单占用：'+data.OCCUPYNUM;
                goodsInfo +='<br>审核状态：'+data.CHECK_STATUS;
                tr.find(".Jproductmanager").html(data.PRODUCTMANAGER_NO_SPACE);
                tr.find(".JmanageLevel").html(data.MANAGE_CATEGORY_LEVEL);
                tr.find(".JskuCode").html(data.SKU_NO);
                tr.find(".JmaterialCode").html(data.MATERIAL_CODE);
                tr.find(".JbrandName").html(data.BRAND_NAME);
                tr.find(".JskuName").html(data.SHOW_NAME);
                tr.find(".JskuModel").html(data.MODEL);
                tr.find(".JskuInfo").html(goodsInfo);
                tr.find(".JskuUnit").html(data.UNIT_NAME);
                tr.find(".JskuStock").html(data.STOCKNUM);
                tr.find(".JskuAvailableStockNum").html(data.AVAILABLESTOCKNUM);
                tr.find(".JskubaseCategoryName").html(data.BASE_CATEGORY_NAME);
            })
        })
    })
</script>
