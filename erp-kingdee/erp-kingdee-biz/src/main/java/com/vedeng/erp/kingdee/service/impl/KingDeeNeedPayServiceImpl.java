package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeNeedPayCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedPayCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeNeedPayConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeNeedPayRepository;
import com.vedeng.erp.kingdee.service.KingDeeNeedPayApiService;
import com.vedeng.erp.kingdee.service.KingDeeNeedPayService;
import org.springframework.stereotype.Service;

/**
 * 应付余额调整单
 */
@Service
public class KingDeeNeedPayServiceImpl extends KingDeeBaseServiceImpl<
        King<PERSON><PERSON><PERSON>eedPayAdjustEntity,
        King<PERSON><PERSON><PERSON>eedPayDto,
        KingDee<PERSON>eedPayCommand,
        KingDeeNeedPayRepository,
        KingDeeNeedPayConvertor,
        KingDeeNeedPayCommandConvertor> implements KingDeeNeedPayService, KingDeeNeedPayApiService {

}
