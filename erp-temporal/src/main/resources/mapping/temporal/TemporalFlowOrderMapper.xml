<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.temporal.mapper.TemporalFlowOrderMapper">
  
  <resultMap id="BaseResultMap" type="com.vedeng.temporal.domain.entity.FlowOrderEntity">
    <id column="FLOW_ORDER_ID" property="flowOrderId" />
    <result column="FLOW_ORDER_NO" property="flowOrderNo" />
    <result column="BASE_ORDER_ID" property="baseOrderId" />
    <result column="BASE_ORDER_NO" property="baseOrderNo" />
    <result column="BASE_BUSINESS_TYPE" property="baseBusinessType" />
    <result column="AUDIT_STATUS" property="auditStatus" />
    <result column="AUDIT_USER_ID" property="auditUserId" />
    <result column="AUDIT_USERNAME" property="auditUsername" />
    <result column="AUDIT_TIME" property="auditTime" />
    <result column="IS_DELETE" property="isDelete" />
    <result column="CONTRACT_STATUS" property="contractStatus" />
    <result column="PUSH_DIRECTION" property="pushDirection" />
    <result column="SOURCE_ERP" property="sourceErp" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
  </resultMap>

  <sql id="Base_Column_List">
    FLOW_ORDER_ID, FLOW_ORDER_NO, BASE_ORDER_ID, BASE_ORDER_NO, BASE_BUSINESS_TYPE,
    AUDIT_STATUS, AUDIT_USER_ID, AUDIT_USERNAME, AUDIT_TIME, IS_DELETE, CONTRACT_STATUS,
    PUSH_DIRECTION, SOURCE_ERP, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME
  </sql>

  <!-- 查询有效的流程配置数据，用于 Temporal 工作流批量启动 -->
  <select id="selectValidFlowOrders" resultMap="BaseResultMap">
    SELECT DISTINCT
    fo.FLOW_ORDER_ID, fo.FLOW_ORDER_NO, fo.BASE_ORDER_ID, fo.BASE_ORDER_NO, fo.BASE_BUSINESS_TYPE,
    fo.AUDIT_STATUS, fo.AUDIT_USER_ID, fo.AUDIT_USERNAME, fo.AUDIT_TIME, fo.IS_DELETE, fo.CONTRACT_STATUS,
    fo.PUSH_DIRECTION, fo.SOURCE_ERP, fo.CREATOR, fo.UPDATER, fo.CREATOR_NAME, fo.UPDATER_NAME, fo.ADD_TIME, fo.MOD_TIME
    FROM T_FLOW_ORDER fo
    INNER JOIN T_FLOW_NODE fn ON fo.FLOW_ORDER_ID = fn.FLOW_ORDER_ID
    LEFT JOIN T_FLOW_ORDER_INFO foi ON fn.FLOW_NODE_ID = foi.FLOW_NODE_ID AND foi.IS_DELETE = 0
    WHERE fo.AUDIT_STATUS = 1
      AND fo.IS_DELETE = 0
      AND fn.IS_DELETE = 0
      AND (
        foi.FLOW_ORDER_INFO_ID IS NULL  -- 情况1：没有T_FLOW_ORDER_INFO记录（需要首次执行）
        OR foi.ORDER_STATUS != 1        -- 情况2：有记录但未完成
      )
    ORDER BY fo.ADD_TIME DESC
  </select>

  <!-- 查询ERP方向的流转单，用于FlowOrderInfo生成任务 -->
  <select id="selectErpDirectionFlowOrders" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER
    WHERE AUDIT_STATUS = 1
      AND IS_DELETE = 0
      AND PUSH_DIRECTION= 2
    ORDER BY ADD_TIME DESC
  </select>

  <!-- 根据流转单编号查询流转单，用于单个流程启动 -->
  <select id="selectByFlowOrderNo" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER
    WHERE FLOW_ORDER_NO = #{flowOrderNo}
      AND IS_DELETE = 0
  </select>

  <!-- 根据公司简称和流转单ID查询销售单ID -->
  <select id="selectSaleOrderIdByCompanyAndFlowOrder" resultType="java.lang.String">
    SELECT foi.FLOW_ORDER_INFO_NO
    FROM T_BASE_COMPANY_INFO bci
    INNER JOIN T_FLOW_NODE fn ON bci.COMPANY_NAME = fn.TRADER_NAME 
    INNER JOIN T_FLOW_ORDER_INFO foi ON fn.FLOW_NODE_ID = foi.FLOW_NODE_ID
    WHERE bci.COMPANY_SHORT_NAME = #{companyShortName}
      AND fn.FLOW_ORDER_ID = #{flowOrderId}
      AND foi.FLOW_ORDER_INFO_TYPE = #{flowOrderInfoType}
      AND bci.IS_DELETED = 0
      AND fn.IS_DELETE = 0
      AND foi.IS_DELETE = 0
    LIMIT 1
  </select>
  
  <select id="selectBusinessNoByCompanyAndFlowOrder" resultType="java.lang.String">
    select a.BASE_ORDER_NO
    from T_FLOW_ORDER a
           left join T_BASE_COMPANY_INFO b on a.SOURCE_ERP = b.COMPANY_SHORT_NAME
    where a.SOURCE_ERP = #{companyShortName}
      and a.FLOW_ORDER_ID = #{flowOrderId}
  </select>

  <!-- ========== 扩展查询方法 ========== -->

  <!-- 查询完整的业务流转单信息 -->
  <select id="selectFlowOrderCompleteInfo" resultType="java.util.Map">
    SELECT 
        fo.FLOW_ORDER_ID,
        fo.FLOW_ORDER_NO,
        fo.BASE_ORDER_ID,
        fo.BASE_ORDER_NO,
        fo.CREATOR,
        fo.CREATOR_NAME,
        fo.BASE_BUSINESS_TYPE,
        fo.AUDIT_STATUS,
        fo.ADD_TIME
    FROM T_FLOW_ORDER fo
    WHERE fo.FLOW_ORDER_ID = #{flowOrderId}
      AND fo.IS_DELETE = 0
  </select>

  <!-- 查询流转单商品明细列表 -->
  <select id="selectFlowOrderDetails" resultType="java.util.Map">
    SELECT 
        fod.FLOW_ORDER_DETAIL_ID,
        fod.SKU_ID,
        fod.SKU_NO,
        fod.PRODUCT_NAME,
        fod.BRAND,
        fod.MODEL,
        fod.UNIT,
        fod.QUANTITY,
        fod.IS_DELETE
    FROM T_FLOW_ORDER_DETAIL fod
    WHERE fod.FLOW_ORDER_ID = #{flowOrderId}
      AND fod.IS_DELETE = 0
    ORDER BY fod.FLOW_ORDER_DETAIL_ID
  </select>

  <!-- 查询指定节点的商品价格信息 -->
  <select id="selectFlowNodePrices" resultType="java.util.Map">
    SELECT 
        fnp.PRICE,
        fnp.MARKUP_RATE,
        fnp.GROSS_PROFIT_RATE,
        fod.SKU_ID,
        fod.SKU_NO,
        fod.PRODUCT_NAME,
        fn.FLOW_NODE_ID,
        fn.NODE_LEVEL,
        bci.COMPANY_SHORT_NAME,
        bci.COMPANY_NAME
    FROM T_FLOW_NODE_ORDER_DETAIL_PRICE fnp
    INNER JOIN T_FLOW_ORDER_DETAIL fod ON fnp.FLOW_ORDER_DETAIL_ID = fod.FLOW_ORDER_DETAIL_ID
    INNER JOIN T_FLOW_NODE fn ON fnp.FLOW_NODE_ID = fn.FLOW_NODE_ID
    INNER JOIN T_BASE_COMPANY_INFO bci ON fn.TRADER_NAME = bci.COMPANY_NAME
    WHERE fod.FLOW_ORDER_ID = #{flowOrderId}
      AND (
          bci.COMPANY_SHORT_NAME = #{companyCode}
          OR fn.TRADER_NAME = #{companyCode}
      )
      AND fod.IS_DELETE = 0
      AND fn.IS_DELETE = 0
      AND bci.IS_DELETED = 0
    ORDER BY fod.FLOW_ORDER_DETAIL_ID, fn.NODE_LEVEL
  </select>

  <!-- 查询0-1采购单的商品信息 -->
  <select id="selectBuyorderGoodsByOrderId" resultType="java.util.Map">
    SELECT 
        bg.BUYORDER_GOODS_ID,
        bg.BUYORDER_ID,
        bg.GOODS_ID,
        bg.SKU,
        bg.GOODS_NAME,
        bg.BRAND_NAME,
        bg.MODEL,
        bg.UNIT_NAME,
        bg.PRICE,
        bg.NUM,
        bg.SEND_GOODS_TIME,
        bg.RECEIVE_GOODS_TIME,
        bg.INSIDE_COMMENTS,
        bg.DELIVERY_CYCLE,
        bg.INSTALLATION,
        bg.COMMENTS,
        bg.IS_HAVE_AUTH,
        bg.IS_GIFT,
        bg.REFER_PRICE,
        bg.REBATE_PRICE,
        bg.REBATE_AMOUNT,
        bg.ACTUAL_PURCHASE_PRICE
    FROM T_BUYORDER_GOODS bg
    WHERE bg.BUYORDER_ID = #{baseOrderId}
      AND bg.IS_DELETE = 0
    ORDER BY bg.BUYORDER_GOODS_ID
  </select>

  <!-- 根据流转单ID和公司代码查询流程节点的完整信息 -->
  <select id="selectFlowNodeCompleteInfo" resultType="java.util.Map">
    SELECT 
        fn.FLOW_NODE_ID,
        fn.FLOW_ORDER_ID,
        fn.NODE_LEVEL,
        fn.TRADER_ID,
        fn.TRADER_NAME,
        fn.TRADER_CONTACT_ID,
        fn.TRADER_CONTACT_NAME,
        fn.TRADER_CONTACT_PHONE,
        fn.TRADER_ADDRESS_ID,
        fn.TRADER_CONTACT_ADDRESS,  
        fn.PAYMENT_METHOD,
        fn.INVOICE_TYPE,
        fn.AMOUNT,
        fn.CREDIT_PAYMENT,
        fn.BALANCE,
        fn.BALANCE_DUE_DATE,
        bci.COMPANY_SHORT_NAME,
        bci.COMPANY_NAME
    FROM T_FLOW_NODE fn
    INNER JOIN T_TRADER t ON fn.TRADER_ID = t.TRADER_ID
    INNER JOIN T_TRADER_SUPPLIER ts ON t.TRADER_ID = ts.TRADER_ID
    LEFT JOIN T_BASE_COMPANY_INFO bci ON ts.TRADER_SUPPLIER_ID = bci.SUPPLIER_TRADER_ID
    WHERE fn.FLOW_ORDER_ID = #{flowOrderId}
      AND fn.IS_DELETE = 0
      AND t.IS_ENABLE = 1
      AND (
          bci.COMPANY_SHORT_NAME = #{companyCode}
          OR fn.TRADER_NAME = #{companyCode}
      )
      AND (bci.IS_DELETED = 0 OR bci.ID IS NULL)
    ORDER BY fn.NODE_LEVEL
    LIMIT 1
  </select>

  <!-- 根据流转单ID查询流转单实体 -->
  <select id="selectByFlowOrderId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_FLOW_ORDER
    WHERE FLOW_ORDER_ID = #{flowOrderId}
      AND IS_DELETE = 0
  </select>
</mapper>
