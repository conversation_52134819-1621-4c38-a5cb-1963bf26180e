package com.vedeng.erp.saleorder.enums;

/**
 * 订单状态 PCOrderStatusEnum
 *
 */
public enum OrderStatusEnum {
    /**
     * 待确认
     */
    TO_BE_CONFIRMED(0,"待确认"),

    /**
     * 待审核
     */
    TO_BE_AUDITED(1,"待审核"),

    /**
     * 待收款
     */
    TO_BE_PAYMENT(2, "待收款"),

    /**
     * 待发货
     */
    TO_BE_DELIVERED(3, "待发货"),

    /**
     * 待收货
     */
    TO_BE_RECEIVED(4,"待收货"),

    /**
     * 待开票
     */
    TO_BE_INVOICED(5,"待开票"),

    /**
     * 已完结
     */
    FINISHED(6,"已完结"),

    /**
     * 已关闭
     */
    CLOSE(7,"已关闭")



    ;

    private Integer status;

    private String desc;

    OrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
