package com.vedeng.erp.kingdee.task.batch;


import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.ExpressReceiptBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 快递签收
 * @date 2023/4/15 11:00
 */
@JobHandler(value = "ExpressReceiptBatchTask")
@Component
public class ExpressReceiptBatchTask extends AbstractJobHandler {

    @Autowired
    private ExpressReceiptBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("====================快递签收对接金蝶开始==================");
        Job job = batchJob.expressReceiptFlowJob();
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("====================快递签收对接金蝶结束==================");
        return SUCCESS;
    }
}
