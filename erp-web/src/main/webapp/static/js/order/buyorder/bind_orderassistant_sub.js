function cancel() {
    var index = parent.layer.getFrameIndex(window.name); //获取当前窗体索引
    parent.layer.close(index); //执行关闭;
}

function save() {
    console.log(111111);
    var orderAssistantId = $('#orderAssistantId').val();
    var managerUserId = $('#productManager').val();
    var assUserId = $('#productAssistant').val();
    var orderAssUserJProductUser = {};
    orderAssUserJProductUser.orderAssistantUserId = orderAssistantId;
    orderAssUserJProductUser.productManagerUserId = managerUserId;
    orderAssUserJProductUser.productAssitantUserId = assUserId;

    if (managerUserId == null || managerUserId === '' ) {
        layer.alert('请选择产品经理');
        return;
    }
    if (assUserId == null || assUserId === ''){
        layer.alert('请选择产品助理');
        return;
    }
    $.ajax({
        async: false,
        url: "./bindOrderAssistantToProductUser.do",
        type: 'POST',
        contentType: "application/json;charset=utf-8",
        data: JSON.stringify(orderAssUserJProductUser),
        dataType: 'json',
        success: function (result) {
            if (result.code == 0) {
                console.log(result.message);
                layer.msg(result.message,{
                    icon: 1,
                    time: 1000 //0.5秒关闭（如果不配置，默认是3秒）
                },function(){
                    cancel();
                    // parent.location.href="./bindOrderAssiIndex.do?orderAssitantUserId="+orderAssistantId;
                    parent.location.reload();

                });

            } else {
                layer.alert(result.message);
            }
        },
        error: function (result) {
            layer.alert("操作失败");
            window.location.reload();
        }
    })
}