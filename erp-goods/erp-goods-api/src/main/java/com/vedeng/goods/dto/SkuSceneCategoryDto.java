package com.vedeng.goods.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;

/**
 * 场景表
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SkuSceneCategoryDto extends BaseDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 所属方案ID
     */
    private Long sceneId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 分类描述
     */
    private String description;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * skuNos 逗号分隔
     */
    @NotEmpty(message = "商品不能为空", groups = {AddGroup.class, UpdateGroup.class})
    private String skuNos;

    /**
     * 商品信息
     */
    List<SkuDto> productInfoList;

}