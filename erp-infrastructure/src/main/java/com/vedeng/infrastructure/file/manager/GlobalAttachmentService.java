package com.vedeng.infrastructure.file.manager;

import cn.hutool.core.lang.Assert;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 附件
 * @date 2024/9/26 19:03
 */
@Component
public class GlobalAttachmentService {

    @Autowired
    private AttachmentMapper attachmentMapper;

    public void save(Attachment attachment) {
        Assert.notNull(attachment, "attachment can not be null");
        Assert.notNull(attachment.getRelatedId(), "relatedId can not be null");
        Assert.notNull(attachment.getAttachmentType(), "attachmentType can not be null");
        Assert.notNull(attachment.getAttachmentFunction(), "attachmentFunction can not be null");
        Assert.notNull(attachment.getUri());
        attachmentMapper.insertSelective(attachment);
    }

    public void saveAll(List<Attachment> attachmentList) {
        Assert.notEmpty(attachmentList, "attachmentList can not be empty");
        attachmentList.forEach(this::save);
    }

    public void delete(Integer attachmentId) {
        Assert.notNull(attachmentId, "attachmentId can not be null");
        attachmentMapper.delAttachmentByPrimaryKey(attachmentId);
    }

    public List<Attachment> get(Integer relatedId, Integer attachmentFunction) {
        return attachmentMapper.queryListByRelatedIdAndFunction(relatedId, attachmentFunction);
    }

    /**
     * 根据id获取文件
     */
    public Attachment getById(Integer attachmentId) {
        return attachmentMapper.selectByPrimaryKey(attachmentId);
    }
}
