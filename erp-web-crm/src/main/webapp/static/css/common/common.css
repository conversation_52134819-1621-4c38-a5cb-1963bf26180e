@import 'font/style.css';
html,
body {
  height: 100%;
  width: 100%;
  color: #333;
  font: 12px/1.5 'Microsoft YaHei', 'arial', 'sans-serif';
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
  background-color: #f5f7fa;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-box-sizing: border-box;
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: normal;
}
a {
  text-decoration: none;
  color: #333;
}
a:active,
a:visited {
  color: #333;
}
ul {
  list-style: none;
}
button,
input,
optgroup,
select,
textarea {
  border: 0;
  outline: 0;
  font: inherit;
  color: inherit;
}
img {
  border: 0;
  vertical-align: middle;
}
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #333;
  /*输入框提示语的字体样式*/
  font-size: 14px;
}
input[type=search] {
  -webkit-appearance: textfield;
  -webkit-box-sizing: content-box;
  font-family: inherit;
  font-size: 100%;
}
input::-webkit-search-decoration,
input::-webkit-search-cancel-button {
  display: none;
}
.form-block {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
}
.form-block:last-child {
  margin-bottom: 0;
}
.flex-box {
  display: flex;
  margin-bottom: 20px;
}
.flex-box > * {
  flex-shrink: 0;
}
.user-show {
  display: flex;
  align-items: center;
}
.user-show > .avatar {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 3px;
}
.global__loading__wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
@keyframes loading {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.global__loading__wrap .icon-loading {
  font-size: 64px;
  color: #fff;
  animation: loading 2s linear infinite;
  z-index: 9999;
}
.global__loading__wrap .global__loading__p {
  font-size: 14px;
  color: #fff;
  margin-top: 10px;
}
.form-wrap .form-item {
  display: flex;
  margin-bottom: 15px;
}
.form-wrap .form-item:last-child {
  margin-bottom: 0;
}
.form-wrap .form-item .form-label {
  padding-top: 6px;
  margin-right: 10px;
  text-align: right;
  width: 240px;
  flex-shrink: 0;
  color: #999;
}
.form-wrap .form-item .form-label .must {
  color: #e64545;
}
.form-wrap .form-item .form-fields {
  flex: 1;
  word-break: break-all;
}
.form-wrap .form-item .form-fields .vd-ui-input-error {
  color: #E64545;
  margin-top: 5px;
  font-size: 0;
  white-space: nowrap;
}
.form-wrap .form-item .form-fields .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -3px;
}
.form-wrap .form-item .form-fields .vd-ui-input-error .vd-ui-input-error--errmsg {
  font-size: 12px;
  margin: 0px;
  display: inline-block;
}
.form-wrap .form-item .form-tip {
  color: #999;
  margin-top: 5px;
}
.form-wrap .form-item.form-item-text .form-label {
  padding-top: 0;
}
.form-wrap .form-btn-wrap {
  padding-left: 270px;
}
.form-wrap.label-width-8 .form-item .form-label {
  width: 260px;
}
.form-wrap.label-width-8 .form-btn-wrap {
  padding-left: 370px;
}
.form-wrap.label-width-7 .form-item .form-label {
  width: 240px;
}
.form-wrap.label-width-7 .form-btn-wrap {
  padding-left: 350px;
}
.form-wrap.label-width-6 .form-item .form-label {
  width: 220px;
}
.form-wrap.label-width-6 .form-btn-wrap {
  padding-left: 330px;
}
.form-wrap.label-width-5 .form-item .form-label {
  width: 200px;
}
.form-wrap.label-width-5 .form-btn-wrap {
  padding-left: 310px;
}
.form-wrap.label-width-4 .form-item .form-label {
  width: 180px;
}
.form-wrap.label-width-4 .form-btn-wrap {
  padding-left: 290px;
}
.form-wrap.label-width-3 .form-item .form-label {
  width: 160px;
}
.form-wrap.label-width-3 .form-btn-wrap {
  padding-left: 270px;
}
.form-wrap.label-width-2 .form-item .form-label {
  width: 140px;
}
.form-wrap.label-width-2 .form-btn-wrap {
  padding-left: 250px;
}
.form-wrap.label-width-1 .form-item .form-label {
  width: 120px;
}
.form-wrap.label-width-1 .form-btn-wrap {
  padding-left: 230px;
}
.ui-col-1 {
  width: calc(100% * (1 / 12));
}
.ui-col-2 {
  width: calc(100% * (2 / 12));
}
.ui-col-3 {
  width: calc(100% * (3 / 12));
}
.ui-col-4 {
  width: calc(100% * (4 / 12));
}
.ui-col-5 {
  width: calc(100% * (5 / 12));
}
.ui-col-6 {
  width: calc(100% * (6 / 12));
}
.ui-col-7 {
  width: calc(100% * (7 / 12));
}
.ui-col-8 {
  width: calc(100% * (8 / 12));
}
.ui-col-9 {
  width: calc(100% * (9 / 12));
}
.ui-col-10 {
  width: calc(100% * (10 / 12));
}
.ui-col-11 {
  width: calc(100% * (11 / 12));
}
.ui-col-12 {
  width: calc(100% * (12 / 12));
}
.page-wrap {
  padding-top: 50px;
}
.page-wrap.layout-hidden {
  padding-top: 0;
}
.page-wrap.layout-hidden .page-container {
  padding-left: 0;
  min-height: 100vh;
}
.page-wrap.side-hidden .page-container {
  padding-left: 0;
}
.page-wrap .page-header-wrap {
  height: 50px;
  position: fixed;
  background: #0087E0;
  top: 0;
  left: 0;
  z-index: 50;
  width: 100%;
  display: flex;
}
.page-wrap .page-header-wrap .side-trigger {
  background-color: #004482;
  opacity: 0.75;
  background-image: url(../../image/header-category.svg);
  background-size: 20px 20px;
  background-position: center;
  background-repeat: no-repeat;
  width: 50px;
  height: 50px;
  cursor: pointer;
}
.page-wrap .page-header-wrap .side-trigger:hover {
  opacity: 1;
}
.page-wrap .page-header-wrap .header-system-info {
  display: flex;
  width: 220px;
  padding-left: 15px;
  height: 50px;
  align-items: center;
  background: #006CB3;
}
.page-wrap .page-header-wrap .header-system-info .header-system-logo {
  width: 28px;
  height: 28px;
  background-color: #fff;
  border-radius: 3px;
  background-image: url(../../image/logo.svg);
  background-size: 18px 18px;
  background-position: center;
  background-repeat: no-repeat;
  margin-right: 10px;
}
.page-wrap .page-header-wrap .header-system-info .header-system-name {
  font-size: 14px;
  color: #fff;
  font-weight: 700;
}
.page-wrap .page-header-wrap .header-system-info.no-link {
  cursor: default;
}
.page-wrap .page-header-wrap .header-user-wrap {
  position: absolute;
  right: 10px;
  top: 7px;
  height: 36px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 5px;
  padding: 0 6px;
}
.page-wrap .page-header-wrap .header-user-wrap .user-avatar {
  width: 24px;
  height: 24px;
  border-radius: 2px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 6px;
}
.page-wrap .page-header-wrap .header-user-wrap .user-avatar img {
  max-width: 100%;
  max-height: 100%;
}
.page-wrap .page-header-wrap .header-user-wrap .user-name {
  color: #fff;
  margin-right: 6px;
}
.page-wrap .page-header-wrap .header-user-wrap .icon-down {
  font-size: 16px;
  color: #fff;
  display: inline-block;
  transition: transform 0.22s ease;
}
.page-wrap .page-header-wrap .header-user-wrap .header-user-drop-wrap {
  position: absolute;
  width: 100%;
  right: 0;
  top: 36px;
  border: solid 1px #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  border-radius: 3px;
  padding: 5px 0;
  background: #fff;
  cursor: default;
  height: 0;
  opacity: 0;
  overflow: hidden;
  transition: all 0.22s ease;
}
.page-wrap .page-header-wrap .header-user-wrap .header-user-drop-wrap .login-out {
  font-size: 14px;
  line-height: 21px;
  cursor: pointer;
  height: 33px;
  line-height: 33px;
  display: block;
  padding: 0 10px;
}
.page-wrap .page-header-wrap .header-user-wrap .header-user-drop-wrap .login-out:hover {
  background: #f5f7fa;
}
.page-wrap .page-header-wrap .header-user-wrap:hover {
  background: #006CB3;
}
.page-wrap .page-header-wrap .header-user-wrap.active {
  background: #006CB3;
}
.page-wrap .page-header-wrap .header-user-wrap.active .icon-down {
  transform: rotate(180deg);
}
.page-wrap .page-header-wrap .header-user-wrap.active .header-user-drop-wrap {
  height: 45px;
  opacity: 1;
}
.page-wrap .page-container {
  padding-left: 220px;
  min-height: calc(100vh - 50px);
  min-width: 960px;
}
.page-wrap .page-container .page-main {
  position: relative;
}
.page-wrap .page-side-fixed-mask {
  position: fixed;
  z-index: -1;
  width: 100%;
  height: 100%;
  left: 0;
  top: 0;
  background: rgba(0, 0, 0, 0.6);
  opacity: 0;
  transition: all 0.22s ease;
  pointer-events: none;
}
.page-wrap .page-side-fixed-mask.show {
  opacity: 1;
  z-index: 1999;
  pointer-events: all;
}
.page-wrap .page-side-wrap {
  width: 220px;
  position: fixed;
  top: 50px;
  left: 0;
  height: calc(100vh - 50px);
  background: #FFF;
  z-index: 11;
  border-right: solid 1px #E1E5E8;
  padding: 20px 8px;
}
.page-wrap .page-side-wrap::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.page-wrap .page-side-wrap::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.page-wrap .page-side-wrap::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.page-wrap .page-side-wrap::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.page-wrap .page-side-wrap::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.page-wrap .page-side-wrap .side-menu-item {
  display: flex;
  height: 40px;
  align-items: center;
  padding: 0 16px 0 12px;
  border-radius: 5px;
}
.page-wrap .page-side-wrap .side-menu-item:hover {
  background: #F5F7FA;
}
.page-wrap .page-side-wrap .side-menu-item.active {
  background: #E6F7FF;
}
.page-wrap .page-side-wrap .side-menu-item .menu-icon {
  width: 18px;
  height: 18px;
  margin-right: 8px;
  margin-top: 1px;
}
.page-wrap .page-side-wrap .side-menu-item .menu-name {
  font-size: 14px;
}
.page-wrap .page-side-wrap .page-side-title,
.page-wrap .page-side-wrap .page-side-close {
  display: none;
}
.page-wrap .page-side-wrap.fixed {
  top: 0;
  height: 100vh;
  z-index: 2000;
  left: -230px;
  transition: left 0.22s ease;
}
.page-wrap .page-side-wrap.fixed .page-side-title {
  display: block;
  color: #999;
  margin-bottom: 10px;
}
.page-wrap .page-side-wrap.fixed .page-side-close {
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  top: 0;
  right: 0;
  color: #999;
  cursor: pointer;
}
.page-wrap .page-side-wrap.fixed .page-side-close:hover {
  color: #333;
}
.page-wrap .page-side-wrap.fixed.fixed-show {
  left: 0;
}
.page-wrap .list-title {
  background: #fff;
  padding: 15px 0 15px 20px;
  font-size: 20px;
  font-weight: 700;
}
.page-wrap .list-title-btns {
  display: flex;
  align-items: center;
  padding: 14px 20px 13px 20px;
  background: #fff;
}
.page-wrap .list-title-btns .list-title-btn-item {
  border: solid 1px #BABFC2;
  margin-right: -1px;
  height: 33px;
  line-height: 31px;
  font-size: 14px;
  padding: 0 20px;
  cursor: pointer;
}
.page-wrap .list-title-btns .list-title-btn-item:hover {
  border-color: #09f;
  color: #09f;
}
.page-wrap .list-title-btns .list-title-btn-item:first-child {
  border-radius: 4px 0 0 4px;
}
.page-wrap .list-title-btns .list-title-btn-item:last-child {
  border-radius: 0 4px 4px 0;
}
.page-wrap .list-title-btns .list-title-btn-item.active {
  background: #09f;
  border-color: #09f;
  color: #fff;
  cursor: default;
}
.page-wrap .list-top-option {
  position: absolute;
  top: 13px;
  right: 20px;
  display: flex;
  align-items: center;
}
.page-wrap .list-top-option .vd-ui-button {
  margin-right: 10px;
}
.page-wrap .list-top-option .vd-ui-button:last-child {
  margin-right: 0;
}
.card {
  position: relative;
  max-width: 1240px;
  min-width: 1160px;
  margin: 0 auto;
  background: #fff;
  padding: 0 20px 20px;
  margin-bottom: 10px;
}
.card.detail-card {
  min-width: 960px;
}
.card:last-child {
  margin-bottom: 0;
}
.card.last {
  margin-bottom: 0;
}
.card .card-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  padding: 15px 0;
  font-weight: 700;
}
.card .card-top-option {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: center;
}
.card .card-top-option .card-top-option-item {
  margin-left: 10px;
  color: #09f;
}
.card .card-top-option .card-top-option-item:hover {
  color: #f60;
}
.detail-top-card {
  display: flex;
  align-items: center;
  background: #fff;
  margin: 0 auto;
  margin-bottom: 10px;
  max-width: 1240px;
}
.detail-top-card .detail-top-item {
  flex: 1;
  padding: 15px 0;
  text-align: center;
  position: relative;
}
.detail-top-card .detail-top-item::before {
  content: "";
  position: absolute;
  width: 1px;
  height: 21px;
  background: #e1e5e8;
  right: 0;
  top: 28px;
}
.detail-top-card .detail-top-item:last-child::before {
  display: none;
}
.detail-top-card .detail-top-item .item-label {
  margin-bottom: 5px;
  color: #999;
}
.detail-top-card .detail-top-item .item-txt {
  font-weight: 700;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}
.detail-top-card .detail-top-item .item-txt .icon-popup {
  line-height: 1;
  font-size: 16px;
  font-weight: normal;
  color: #f60;
  margin-right: 5px;
}
.detail-top-card .detail-top-item .item-txt .user-avatar {
  width: 20px;
  height: 20px;
  border-radius: 3px;
  overflow: hidden;
  margin-right: 5px;
}
.detail-top-card .detail-top-item .item-txt .user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  vertical-align: top;
}
.info-item {
  display: flex;
  margin-top: 10px;
}
.info-item:first-child {
  margin-top: 0;
}
.info-item > .label {
  width: 240px;
  text-align: right;
  color: #999;
}
.info-item .content {
  flex: 1;
  min-width: 0;
  word-break: break-all;
}
.info-item.info-table {
  margin-bottom: 20px;
}
.info-item.info-table .label {
  padding-top: 9px;
}
.info-item.info-table:last-child {
  margin-bottom: 0;
}
.info-item.info-avatar .label {
  padding-top: 6px;
}
.info-wrap .info-empty {
  padding: 40px 0;
  text-align: center;
}
.info-wrap .info-empty .icon-info1 {
  font-size: 32px;
  color: #09f;
  margin-bottom: 10px;
}
.info-wrap .info-empty .info-empty-txt {
  color: #999;
}
.text-line-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.text-line-7 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 7;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.margin-b10 {
  margin-bottom: 10px;
}
.margin-b15 {
  margin-bottom: 15px;
}
.margin-b20 {
  margin-bottom: 20px;
}
.margin-t10 {
  margin-top: 20px;
}
.margin-t15 {
  margin-top: 15px;
}
.margin-t20 {
  margin-top: 20px;
}
.table-edit-wrap {
  display: flex;
  background: #F0F9FF;
  padding: 8px 10px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  min-width: 262px;
}
.table-edit-wrap .edit-content {
  flex: 1;
  margin-right: 5px;
}
.table-edit-wrap .edit-content .vd-ui-date .vd-ui-date-editor {
  width: 100% !important;
}
.table-edit-wrap .edit-content .vd-ui-cascader {
  width: 100% !important;
}
.table-edit-wrap .edit-content .vd-ui-textarea {
  vertical-align: middle;
  width: 100%;
}
.table-edit-wrap .edit-content .vd-ui-textarea .vd-ui-textarea-place {
  width: 100%;
}
.table-edit-wrap .edit-content .vd-ui-select {
  width: 100%;
}
.table-edit-wrap .edit-content .vd-ui-input-error {
  color: #E64545;
  font-size: 0;
  padding: 10px;
  border: solid 1px #BABFC2;
  background: #fff;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  border-radius: 3px;
  position: absolute;
  min-width: 100%;
  white-space: nowrap;
}
.table-edit-wrap .edit-content .vd-ui-input-error .vd-ui-input-error--icon {
  font-size: 16px;
  margin-right: 5px;
  line-height: 1;
  vertical-align: -3px;
}
.table-edit-wrap .edit-content .vd-ui-input-error .vd-ui-input-error--errmsg {
  font-size: 12px;
  margin: 0px;
  display: inline-block;
}
.table-edit-wrap .edit-option {
  display: flex;
  align-items: center;
}
.table-edit-wrap .edit-option .vd-ui-button {
  padding: 0 9px;
  margin-right: 5px;
  line-height: 31px;
}
.table-edit-wrap .edit-option .vd-ui-button:last-child {
  margin-right: 0;
}
.table-edit-wrap .edit-option .vd-ui-button .vd-ui_icon {
  margin: 0;
}
.bubble-tip-wrap {
  position: absolute;
  background: #333;
  color: #fff;
  border-radius: 3px;
  padding: 7px 10px;
  font-size: 14px;
  white-space: nowrap;
}
.bubble-tip-wrap::before {
  content: "";
  width: 0;
  height: 0;
  border: 6px solid transparent;
  position: absolute;
}
.bubble-tip-wrap.top::before {
  border-top: 6px solid #333;
  bottom: -12px;
  left: calc(50% - 6px);
}
.form-top-tip {
  display: flex;
  padding: 10px 15px;
  background: #E0F3FF;
  margin: -20px -20px 20px -20px;
}
.form-top-tip .icon-info2 {
  color: #09f;
  font-size: 16px;
  line-height: 1;
  margin-top: 3px;
  margin-right: 10px;
}
.form-top-tip .tip-cnt {
  flex: 1;
}
.top-warn-tip {
  display: flex;
  padding: 10px 15px;
  background: #FCE9E9;
  max-width: 1240px;
  min-width: 960px;
  margin: 10px auto;
}
.top-warn-tip.no-top {
  margin-top: 0;
}
.top-warn-tip .vd-ui_icon {
  color: #f60;
  margin-right: 5px;
  font-size: 16px;
  line-height: 1;
  margin-top: 1px;
}
.top-warn-tip .tip-cnt {
  flex: 1;
}
.dlg-form-footer {
  padding-left: 150px;
  width: 100%;
  display: flex;
}
.error-nowrap .vd-ui-input-error {
  white-space: nowrap;
}
#page-container {
  opacity: 0;
}
#page-container.show {
  opacity: 1;
}
.switch-line-2 .card,
.switch-line-2 .remark-wrap {
  min-width: 960px !important;
}
.switch-line-2 .card:not(.line-1) .info-wrap {
  display: flex;
  flex-wrap: wrap;
}
.switch-line-2 .card:not(.line-1) .info-wrap .info-item {
  width: calc(50% - 10px);
  margin-right: 20px;
}
.switch-line-2 .card:not(.line-1) .info-wrap .info-item:nth-child(2n) {
  margin-right: 0;
}
.switch-line-2 .card:not(.line-1) .info-wrap .info-item .label {
  width: 120px;
  margin-right: 10px;
}
.switch-line-2 .card:not(.line-1) .info-wrap .info-item .content {
  align-items: flex-start;
}
.switch-line-2 .card:not(.line-1) .info-wrap .info-item:nth-child(2) {
  margin-top: 0;
}
.switch-line-2 .card:not(.line-1) .info-wrap .info-item.all-line {
  width: 100%;
  margin-right: 0;
}
.detail-page-layout {
  padding-top: 60px;
}
.detail-page-layout .top-featurn {
  width: 100%;
  height: 60px;
  position: fixed;
  top: 50px;
  left: 0;
  z-index: 40;
}
.detail-page-layout .top-featurn .inner-header {
  padding: 0 15px;
  height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFFFFF;
  border-bottom: solid 1px #E1E5E8;
}
.detail-page-layout .top-featurn .inner-header .inner-header-title {
  font-size: 20px;
  font-weight: 700;
}
.header-aside-wrap .h-a-item-num {
  position: absolute;
  top: 8px;
  right: 33px;
  background: #e64545;
  border-radius: 10px;
  color: #fff;
  padding: 0 6px;
  z-index: 5;
  line-height: 20px;
  transform: translateX(50%);
  min-width: 20px;
  text-align: center;
}
.header-aside-wrap .header-xs-aside .h-a-item-num {
  right: 12px;
}
