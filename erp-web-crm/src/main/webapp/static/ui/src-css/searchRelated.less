@import (less) '../../css/common/mixin.css';

.vd-ui-search-category {

    .vd-ui-search-category-form {

        .vd-ui-search-category-item {
            position: relative;
            margin-bottom: 10px;
            padding-right: 40px;

            &:last-child {
                margin-bottom: 0;
            }

            .del {
                width: 30px;
                height: 33px;
                margin-left: 10px;
                display: flex;
                justify-content: center;
                align-items: center;
                cursor: pointer;

                position: absolute;
                right: 0;
                top: 0;

                &:hover {
                    color: #f60;
                }
            }

            .search-panel {
                position: absolute;
                top: 33px;
                left: 0;
                z-index: 10;
                width: 100%;
                box-shadow: 0px 5px 10px rgba(0, 0, 0, .1);

                .search-panel-inner {
                    background: #fff;
                    border: solid 1px #ddd;

                    .search-panel-list {
                        .label {
                            color: #999;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            padding: 6px 10px;
                        }

                        .search-list-content {
                            max-height: 352px;
                            overflow-y: auto;
                            .scrollbar();

                            .search-panel-item {
                                padding: 6px 10px;
                                display: flex;
                                justify-content: space-between;
                                align-items: center;
                                cursor: pointer;

                                .num {
                                    color: #999;
                                }

                                &:hover {
                                    color: #f60;
                                }
                            }
                        }

                    }

                    .search-loading {
                        padding: 10px;
                        display: flex;
                        align-items: center;

                        @keyframes loading {
                            0% {
                                transform: rotate(0);
                            }
                    
                            100% {
                                transform: rotate(360deg);
                            }
                        }

                        > .icon-loading {
                            font-size: 16px;
                            color: #09f;
                            animation: loading 2s linear infinite;
                            z-index: 99;
                            margin-right: 5px;
                        }
                    }

                    .null-data {
                        text-align: center;
                        padding: 10px;
                    }
                }
            }

            .ui-related-search-history {
                position: absolute;
                top: 33px;
                left: 0;
                z-index: 10;
                width: 100%;
                box-shadow: 0px 5px 10px rgba(0, 0, 0, .1);
                padding: 5px 0;
                background: #fff;

                .ui-related-search-history-title {
                    color: #999;
                    padding: 6px 10px;
                }

                .ui-related-search-history-item {
                    padding: 6px 10px;
                    cursor: pointer;

                    &:hover {
                        color: #f60;
                    }
                }
            }
        }
    }

    .vd-ui-search-category-btn {
        margin-top: 10px;

        .btn {
            color: #09f;
            transition: color 0.12s ease-in;
            cursor: pointer;

            &:hover {
                color: #f60;
            }

            .icon-add {
                font-size: 16px;
                vertical-align: -2px;
            }
        }
    }
}