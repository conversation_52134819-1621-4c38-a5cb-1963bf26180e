package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceVoucherDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceVoucherMapper;
import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveFeeCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveFeeEntity;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeReceiveQueryResultDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveFeeCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeReceiveFeeConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeReceiveFeeRepository;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeApiService;
import com.vedeng.erp.kingdee.service.KingDeeReceiveFeeService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/7 15:19
 */
@Service
@Slf4j
public class KingDeeReceiveFeeServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeReceiveFeeEntity,
        KingDeeReceiveFeeDto,
        KingDeeReceiveFeeCommand,
        KingDeeReceiveFeeRepository,
        KingDeeReceiveFeeConvertor,
        KingDeeReceiveFeeCommandConvertor> implements KingDeeReceiveFeeService, KingDeeReceiveFeeApiService {



    @Override
    public List<KingDeeReceiveQueryResultDto> getKingDeeReceiveFee(String invoiceId) {
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.RECEIVE_EXPENSES);
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("F_QZOK_BDDJTID")
                .value(invoiceId).build());
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fBusinessType")
                .value("FY").build());
        queryParam.setFilterString(queryFilterDtos);
        return kingDeeBaseApi.query(queryParam, KingDeeReceiveQueryResultDto.class);
    }

    @Override
    public Boolean kingDeeIsExist(KingDeeReceiveFeeDto d) {
        List<KingDeeReceiveQueryResultDto> kingDeeReceiveCommon = this.getKingDeeReceiveFee(d.getFQzokBddjtid());
        return CollectionUtil.isNotEmpty(kingDeeReceiveCommon);
    }

}