package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.service.impl.BaseServiceimpl;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.OrderReviewProcessMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.dto.OrderReviewProcessDto;
import com.vedeng.order.model.po.OrderReviewProcessPo;
import com.vedeng.order.service.OrderReviewProcessService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service("orderReviewProcessService")
public class OrderReviewProcessServiceImpl extends BaseServiceimpl implements OrderReviewProcessService {
    private final static Logger logger = LoggerFactory.getLogger(OrderReviewProcessServiceImpl.class);

    @Autowired
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Resource
    private OrderReviewProcessMapper orderReviewProcessMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private BuyorderMapper buyorderMapper;


    @Override
    public boolean saveOrderReviewProcesses(OrderReviewProcessDto orderReviewProcessDto) {
        if (orderReviewProcessDto == null || CollectionUtils.isEmpty(orderReviewProcessDto.getOrderApplyIds())){
            return false;
        }
        logger.info("订单关闭保存审核流相关记录 orderReviewProcessDto:{}", JSON.toJSONString(orderReviewProcessDto));
        orderReviewProcessDto.getOrderApplyIds().forEach(applyId -> {
            OrderReviewProcessPo orderReviewProcessPo = new OrderReviewProcessPo();
            orderReviewProcessPo.setType(orderReviewProcessDto.getType());
            orderReviewProcessPo.setOrderId(orderReviewProcessDto.getOrderId());
            orderReviewProcessPo.setBusinessKey(orderReviewProcessDto.getBusinessKeyPrefix() + applyId);
            orderReviewProcessPo.setIsDelete(0);
            orderReviewProcessPo.setCreator(orderReviewProcessDto.getUserId());
            orderReviewProcessPo.setAddTime(System.currentTimeMillis());
            orderReviewProcessMapper.insertSelective(orderReviewProcessPo);
        });

        return false;
    }

    @Override
    public List<OrderReviewProcessPo> getAllOrderReviewProcess() {
        return orderReviewProcessMapper.getAllOrderReviewProcess();
    }

    @Override
    public boolean dealSaleOrderReviewProcess(Integer orderId, Integer userId) {
        if (orderId == null){
            return false;
        }
        logger.info("处理销售订单修改审核信息 orderId:{}", orderId);

        //提前采购
        TaskService taskService = processEngine.getTaskService();
        String earlyBusinessKey = "earlyBuyorderVerify_" + orderId;
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(earlyBusinessKey).singleResult();
        if (task != null){
            orderReviewProcessMapper.insertSelective(new OrderReviewProcessPo(1, orderId, earlyBusinessKey, System.currentTimeMillis(), userId));
        }

        //订单修改相关
        List<Integer> orderApplyIds = saleorderMapper.getOrderApplyIdsBySaleOrderId(orderId);
        if (CollectionUtils.isEmpty(orderApplyIds)){
            return true;
        }

        ArrayList<String> businessKeys = new ArrayList<>();
        Arrays.asList("editSaleorderVerify_", "saleorderModifyAudit_").forEach(prefixKey -> {
            orderApplyIds.forEach(applyId -> {
                businessKeys.add(prefixKey + applyId);
            });
        });

        if (CollectionUtils.isEmpty(businessKeys)){
            return false;
        }
        businessKeys.forEach(businessKey -> {
            if (taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult() == null){
                logger.info("相关修改流程不存在或已完成 businessKey:{}", businessKey);
                return;
            }
            orderReviewProcessMapper.insertSelective(new OrderReviewProcessPo(1, orderId, businessKey, System.currentTimeMillis(), userId));
        });
        return true;
    }

    @Override
    public boolean dealBuyOrderReviewProcess(Integer orderId, Integer userId) {
        if (orderId == null){
            return false;
        }
        logger.info("处理采购订单修改审核信息 orderId:{}", orderId);
        List<Integer> orderApplyIds = buyorderMapper.getBuyOrderApplyIdsByBuyOrderId(orderId);
        if (CollectionUtils.isEmpty(orderApplyIds)){
            return false;
        }
        orderApplyIds.forEach(applyId -> {
            String businessKey = "editBuyorderVerify_" + applyId;
            TaskService taskService = processEngine.getTaskService();
            Task task = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            if (task == null){
                return;
            }
            orderReviewProcessMapper.insertSelective(new OrderReviewProcessPo(2, orderId, businessKey, System.currentTimeMillis(), userId));
        });
        return true;
    }
}
