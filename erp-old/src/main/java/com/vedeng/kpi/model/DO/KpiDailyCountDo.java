package com.vedeng.kpi.model.DO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * T_KPI_DAILY_COUNT
 * <AUTHOR>
public class KpiDailyCountDo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 销售ID
     */
    private Integer userId;

    /**
     * 公司ID
     */
    private Integer companyId;

    /**
     * 小组ID
     */
    private Integer teamId;

    /**
     * 团队(部门)ID
     */
    private Integer groupId;

    /**
     * 本月累计业绩金额
     */
    private BigDecimal kpiAmount;

    /**
     * 本月累计业绩完成度
     */
    private BigDecimal kpiAmountProgress;

    /**
     * 业绩得分
     */
    private BigDecimal kpiAmountScore;

    /**
     * 已合作客户数量
     */
    private Integer coCustomerNum;

    /**
     * 已流失客户数量
     */
    private Integer lostCustomerNum;

    /**
     * 客户数量得分
     */
    private BigDecimal customerScore;

    /**
     * 新增BD客户
     */
    private Integer bdNewCustomerNum;

    /**
     * 流失BD客户
     */
    private Integer bdLostCustomerNum;

    /**
     * BD客户数量得分
     */
    private BigDecimal bdCustomerScore;

    /**
     * 新成交询价成功数量
     */
    private Integer chanceSuccessNum;

    /**
     * 失败询价数量
     */
    private Integer chanceFailNum;

    /**
     * 商机转化率
     */
    private BigDecimal chanceTransProportion;

    /**
     * 询价区间新客数
     */
    private Integer chanceRangeCustomerNum;

    /**
     * 询价转化率得分
     */
    private BigDecimal chanceTransScore;

    /**
     * kpi结算时间
     */
    private Date kpiDate;

    /**
     * 创建时间
     */
    private Date addTime;

    private Date updateTime;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public Integer getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    public Integer getTeamId() {
        return teamId;
    }

    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    public Integer getGroupId() {
        return groupId;
    }

    public void setGroupId(Integer groupId) {
        this.groupId = groupId;
    }

    public BigDecimal getKpiAmount() {
        return kpiAmount;
    }

    public void setKpiAmount(BigDecimal kpiAmount) {
        this.kpiAmount = kpiAmount;
    }

    public BigDecimal getKpiAmountProgress() {
        return kpiAmountProgress;
    }

    public void setKpiAmountProgress(BigDecimal kpiAmountProgress) {
        this.kpiAmountProgress = kpiAmountProgress;
    }

    public BigDecimal getKpiAmountScore() {
        return kpiAmountScore;
    }

    public void setKpiAmountScore(BigDecimal kpiAmountScore) {
        this.kpiAmountScore = kpiAmountScore;
    }

    public Integer getCoCustomerNum() {
        return coCustomerNum;
    }

    public void setCoCustomerNum(Integer coCustomerNum) {
        this.coCustomerNum = coCustomerNum;
    }

    public Integer getLostCustomerNum() {
        return lostCustomerNum;
    }

    public void setLostCustomerNum(Integer lostCustomerNum) {
        this.lostCustomerNum = lostCustomerNum;
    }

    public BigDecimal getCustomerScore() {
        return customerScore;
    }

    public void setCustomerScore(BigDecimal customerScore) {
        this.customerScore = customerScore;
    }

    public Integer getBdNewCustomerNum() {
        return bdNewCustomerNum;
    }

    public void setBdNewCustomerNum(Integer bdNewCustomerNum) {
        this.bdNewCustomerNum = bdNewCustomerNum;
    }

    public Integer getBdLostCustomerNum() {
        return bdLostCustomerNum;
    }

    public void setBdLostCustomerNum(Integer bdLostCustomerNum) {
        this.bdLostCustomerNum = bdLostCustomerNum;
    }

    public BigDecimal getBdCustomerScore() {
        return bdCustomerScore;
    }

    public void setBdCustomerScore(BigDecimal bdCustomerScore) {
        this.bdCustomerScore = bdCustomerScore;
    }

    public Integer getChanceSuccessNum() {
        return chanceSuccessNum;
    }

    public void setChanceSuccessNum(Integer chanceSuccessNum) {
        this.chanceSuccessNum = chanceSuccessNum;
    }

    public Integer getChanceFailNum() {
        return chanceFailNum;
    }

    public void setChanceFailNum(Integer chanceFailNum) {
        this.chanceFailNum = chanceFailNum;
    }

    public BigDecimal getChanceTransProportion() {
        return chanceTransProportion;
    }

    public void setChanceTransProportion(BigDecimal chanceTransProportion) {
        this.chanceTransProportion = chanceTransProportion;
    }

    public Integer getChanceRangeCustomerNum() {
        return chanceRangeCustomerNum;
    }

    public void setChanceRangeCustomerNum(Integer chanceRangeCustomerNum) {
        this.chanceRangeCustomerNum = chanceRangeCustomerNum;
    }

    public BigDecimal getChanceTransScore() {
        return chanceTransScore;
    }

    public void setChanceTransScore(BigDecimal chanceTransScore) {
        this.chanceTransScore = chanceTransScore;
    }

    public Date getKpiDate() {
        return kpiDate;
    }

    public void setKpiDate(Date kpiDate) {
        this.kpiDate = kpiDate;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}