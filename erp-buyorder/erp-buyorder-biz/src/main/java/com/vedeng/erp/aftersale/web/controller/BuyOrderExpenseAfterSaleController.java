package com.vedeng.erp.aftersale.web.controller;

import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesViewDto;
import com.vedeng.erp.system.dto.RoleDto;
import com.vedeng.erp.system.service.RoleApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/13 13:29
 */
@Controller
@RequestMapping("/buyorderExpense/aftersale")
public class BuyOrderExpenseAfterSaleController {

    @Autowired
    private RoleApiService roleApiService;

    @RequestMapping("/detail")
    public ModelAndView detail(Integer expenseAfterSalesId) {
        ModelAndView mv = new ModelAndView("vue/view/afterExpense/detail");
        mv.addObject("expenseAfterSalesId", expenseAfterSalesId);
        List<RoleDto> roleList = roleApiService.getRoleListByUserId(CurrentUser.getCurrentUser().getId());
        int topOrgId = 0;
        for (RoleDto item : roleList) {
            if ("产品专员".equals(item.getRoleName()) || "产品主管".equals(item.getRoleName()) || "产品总监".equals(item.getRoleName())) {
                topOrgId = 6;
                break;
            }
            if ("财务专员".equals(item.getRoleName()) || "财务总监".equals(item.getRoleName())) {
                topOrgId = 8;
                break;
            }
        }
        mv.addObject("topOrgId", topOrgId);
        return mv;
    }

    @RequestMapping("/edit")
    public ModelAndView edit(@RequestParam(required = false) Long expenseAfterSalesId, Integer buyorderExpenseId, Integer traderId) {
        ModelAndView mv = new ModelAndView("vue/view/afterExpense/edit");
        mv.addObject("expenseAfterSalesId", expenseAfterSalesId);
        mv.addObject("buyorderExpenseId", buyorderExpenseId);
        mv.addObject("traderId", traderId);
        return mv;
    }

    @RequestMapping("/returninvoice")
    public ModelAndView returnInvoice(ExpenseAfterSalesViewDto viewInfo) {
        ModelAndView mv = new ModelAndView("vue/view/afterExpense/returninvoice");
        mv.addObject("viewInfo", viewInfo);
        return mv;
    }

    @RequestMapping("/toCapitalBillEdit")
    public ModelAndView toCapitalBillEdit(@RequestParam Long expenseAfterSalesId) {
        ModelAndView mv = new ModelAndView("vue/view/capitalbill/edit");
        mv.addObject("expenseAfterSalesId", expenseAfterSalesId);
        return mv;
    }

}