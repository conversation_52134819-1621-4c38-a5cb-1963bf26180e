<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.price.dao.PriceChangeAffectOrderMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.price.model.PriceChangeAffectOrder" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    <id column="PRICE_CHANGE_AFFECT_ORDER_ID" property="priceChangeAffectOrderId" jdbcType="BIGINT" />
    <result column="SKU_PRICE_MODIFY_RECORD_ID" property="skuPriceModifyRecordId" jdbcType="BIGINT" />
    <result column="ORDER_ID" property="orderId" jdbcType="BIGINT" />
    <result column="ORDER_TYPE" property="orderType" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="IS_DEALED" property="isDealed" jdbcType="BIT" />
    <result column="DEAL_TIME" property="dealTime" jdbcType="BIGINT" />
  </resultMap>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    delete from T_PRICE_CHANGE_AFFECT_ORDER
    where PRICE_CHANGE_AFFECT_ORDER_ID = #{priceChangeAffectOrderId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.vedeng.price.model.PriceChangeAffectOrder" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    insert into T_PRICE_CHANGE_AFFECT_ORDER (PRICE_CHANGE_AFFECT_ORDER_ID, SKU_PRICE_MODIFY_RECORD_ID, 
      ORDER_ID, ORDER_TYPE, ADD_TIME, 
      IS_DEALED, DEAL_TIME)
    values (#{priceChangeAffectOrderId,jdbcType=BIGINT}, #{skuPriceModifyRecordId,jdbcType=BIGINT}, 
      #{orderId,jdbcType=BIGINT}, #{orderType,jdbcType=BIT}, #{addTime,jdbcType=BIGINT}, 
      #{isDealed,jdbcType=BIT}, #{dealTime,jdbcType=BIGINT})
  </insert>
    <insert id="insertByList" parameterType="java.util.List">
    insert into T_PRICE_CHANGE_AFFECT_ORDER (SKU_PRICE_MODIFY_RECORD_ID,
      ORDER_ID, ORDER_TYPE, ADD_TIME)
    values
        <foreach collection="list" item="record" separator=",">
          (
            #{record.skuPriceModifyRecordId,jdbcType=BIGINT},
            #{record.orderId,jdbcType=BIGINT},
            #{record.orderType,jdbcType=BIT},
            #{record.addTime,jdbcType=BIGINT}
          )
        </foreach>
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.price.model.PriceChangeAffectOrder" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    update T_PRICE_CHANGE_AFFECT_ORDER
    set SKU_PRICE_MODIFY_RECORD_ID = #{skuPriceModifyRecordId,jdbcType=BIGINT},
      ORDER_ID = #{orderId,jdbcType=BIGINT},
      ORDER_TYPE = #{orderType,jdbcType=BIT},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      IS_DEALED = #{isDealed,jdbcType=BIT},
      DEAL_TIME = #{dealTime,jdbcType=BIGINT}
    where PRICE_CHANGE_AFFECT_ORDER_ID = #{priceChangeAffectOrderId,jdbcType=BIGINT}
  </update>

  <update id="dealAffrctOrder">
    UPDATE T_PRICE_CHANGE_AFFECT_ORDER
    SET
        IS_DEALED = 1,
        DEAL_TIME = UNIX_TIMESTAMP(NOW())*1000
    WHERE
	    PRICE_CHANGE_AFFECT_ORDER_ID = #{priceChangeAffectOrderId,jdbcType=INTEGER}
  </update>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    select PRICE_CHANGE_AFFECT_ORDER_ID, SKU_PRICE_MODIFY_RECORD_ID, ORDER_ID, ORDER_TYPE, 
    ADD_TIME, IS_DEALED, DEAL_TIME
    from T_PRICE_CHANGE_AFFECT_ORDER
    where PRICE_CHANGE_AFFECT_ORDER_ID = #{priceChangeAffectOrderId,jdbcType=BIGINT}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Aug 24 15:38:49 CST 2021.
    -->
    select PRICE_CHANGE_AFFECT_ORDER_ID, SKU_PRICE_MODIFY_RECORD_ID, ORDER_ID, ORDER_TYPE, 
    ADD_TIME, IS_DEALED, DEAL_TIME
    from T_PRICE_CHANGE_AFFECT_ORDER
  </select>
  <select id="getAffectOrderBySkuPriceModifyRecordIdListPage"
          resultType="com.pricecenter.dto.PriceChangeAffectOrderIndexDto" parameterType="Map">
    SELECT
        A.PRICE_CHANGE_AFFECT_ORDER_ID,
        A.ORDER_ID,
        A.ORDER_TYPE,
        A.ORDER_NO,
        A.NOTIFY_TIME_STRING
    FROM (
        SELECT
            A.PRICE_CHANGE_AFFECT_ORDER_ID,
            A.ORDER_ID,
            A.ORDER_TYPE,
            CASE
                WHEN A.ORDER_TYPE = 1 THEN B.SALEORDER_NO
                WHEN A.ORDER_TYPE = 2 THEN C.QUOTEORDER_NO
            END AS ORDER_NO,
            CASE
                WHEN A.ORDER_TYPE = 1 THEN B.USER_ID
                WHEN A.ORDER_TYPE = 2 THEN C.USER_ID
            END AS USER_ID,
            FROM_UNIXTIME(A.ADD_TIME/1000,'%Y-%m-%d %H:%i:%s') as NOTIFY_TIME_STRING
        FROM T_PRICE_CHANGE_AFFECT_ORDER AS A
            LEFT JOIN T_SALEORDER AS B ON A.ORDER_ID = B.SALEORDER_ID
            LEFT JOIN T_QUOTEORDER AS C ON A.ORDER_ID = C.QUOTEORDER_ID
        WHERE SKU_PRICE_MODIFY_RECORD_ID = #{skuPriceModifyRecordId,jdbcType=INTEGER}
            AND IS_DEALED = 0
    ) AS A
        WHERE A.USER_ID = #{currentUserId,jdbcType=INTEGER}
  </select>
    <select id="isNeedNotify" resultType="java.lang.Integer">
        SELECT
            COUNT(*)
        FROM
            T_SKU_PRICE_MODIFY_RECORD
        WHERE
            1=1
        <if test="null != userLatestReadRecord and userLatestReadRecord.readTime != null" >
            AND  PRICE_MOD_TIME <![CDATA[>=]]> #{userLatestReadRecord.readTime,jdbcType=BIGINT}
        </if>
            AND PRICE_MOD_TIME <![CDATA[<=]]> UNIX_TIMESTAMP(CAST(SYSDATE()AS DATE))*1000
            AND (AFTER_MOD_TERMINAL_PRICE != BEFORE_MOD_TERMINAL_PRICE
            OR BEFORE_MOD_TERMINAL_PRICE IS NULL
            OR AFTER_MOD_DISTRIBUTION_PRICE != BEFORE_MOD_DISTRIBUTION_PRICE
            OR BEFORE_MOD_DISTRIBUTION_PRICE IS NULL)
    </select>
    <select id="queryChangePriceSku" resultType="java.lang.Integer">
        SELECT
        SKU_ID
        FROM
        T_SKU_PRICE_MODIFY_RECORD
        WHERE
        1=1
        <if test="null != userLatestReadRecord and userLatestReadRecord.readTime != null" >
            AND  PRICE_MOD_TIME <![CDATA[>=]]> #{userLatestReadRecord.readTime,jdbcType=BIGINT}
        </if>
        AND PRICE_MOD_TIME <![CDATA[<=]]> UNIX_TIMESTAMP(CAST(SYSDATE()AS DATE))*1000
        AND (AFTER_MOD_TERMINAL_PRICE != BEFORE_MOD_TERMINAL_PRICE
        OR BEFORE_MOD_TERMINAL_PRICE IS NULL
        OR AFTER_MOD_DISTRIBUTION_PRICE != BEFORE_MOD_DISTRIBUTION_PRICE
        OR BEFORE_MOD_DISTRIBUTION_PRICE IS NULL)
        <if test="null == userLatestReadRecord or userLatestReadRecord.readTime == null" >
             limit 100
        </if>
    </select>

    <select id="isNeedNotifyOrder" resultType="java.lang.Integer">
        select
		count(a.SALEORDER_ID)
		from T_SALEORDER a left join T_SALEORDER_GOODS b on a.SALEORDER_ID = b.SALEORDER_ID
		 left join T_R_TRADER_J_USER c on a.TRADER_ID =c.TRADER_ID
		where
		1=1
        <if test="skuids!=null and skuids.size()>0">
           and  b.GOODS_ID in
            <foreach collection="skuids" open="(" close=")" separator="," item="skuid">
                #{skuid}
            </foreach>
        </if>
        and c.USER_ID = #{userId}
		and c.TRADER_TYPE =1
        AND b.IS_DELETE =0
        and a.STATUS =1
        and a.PAYMENT_STATUS = 0
        and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[<=]]> date(FROM_UNIXTIME(a.ADD_TIME/1000,'%Y-%m-%d %H:%i:%s') )
    </select>

    <select id="isNeedNotifyQuoteorder" resultType="java.lang.Integer">
        select
		count(a.QUOTEORDER_ID)
		from T_QUOTEORDER a left join T_QUOTEORDER_GOODS b on a.QUOTEORDER_ID = b.QUOTEORDER_ID
        left join T_R_TRADER_J_USER c on a.TRADER_ID =c.TRADER_ID
        where
        1=1
        <if test="skuids!=null and skuids.size()>0">
           and b.GOODS_ID in
            <foreach collection="skuids" open="(" close=")" separator="," item="skuid">
                #{skuid}
            </foreach>
        </if>

        and c.USER_ID = #{userId}
        and c.TRADER_TYPE =1
        AND b.IS_DELETE =0
        and a.FOLLOW_ORDER_STATUS =0
        and DATE_SUB(CURDATE(), INTERVAL 30 DAY) <![CDATA[<=]]> date(FROM_UNIXTIME(a.ADD_TIME/1000,'%Y-%m-%d %H:%i:%s') )
    </select>
</mapper>