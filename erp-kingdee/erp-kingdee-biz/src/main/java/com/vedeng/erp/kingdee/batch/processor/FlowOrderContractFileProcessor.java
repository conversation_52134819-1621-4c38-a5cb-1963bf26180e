package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.processor.BaseProcessor;
import com.vedeng.erp.kingdee.batch.dto.BatchFlowOrderContractDto;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.service.KingDeeFileDataService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 流转单合同附件处理器
 */
@Service
@Slf4j
public class FlowOrderContractFileProcessor extends BaseProcessor<BatchFlowOrderContractDto, KingDeeFileDataDto> {

    @Autowired
    private KingDeeFileDataService kingDeeFileDataService;

    @Value("${oss_http}")
    private String ossHttp;

    @Override
    public KingDeeFileDataDto doProcess(BatchFlowOrderContractDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("开始推送流转单合同附件,dto:{}", JSONUtil.toJsonStr(dto));
        if (ObjectUtil.isNull(dto.getDataId())) {
            log.info("流转单合同附件推送金蝶，流转单合同不存在:{}", JSONUtil.toJsonStr(dto));
            return null;
        }

        // 构建完整的URL
        String fileUrl = dto.getContractFileUrl();
        if (!fileUrl.startsWith("http")) {
            fileUrl = ossHttp + fileUrl;
        }

        // 通过附件回写表判断该附件是否推送过
        String formId = dto.getBaseBusinessType() == 1 ?
                KingDeeFormConstant.PUR_PurchaseOrder : KingDeeFormConstant.SAL_SaleOrder;

        KingDeeFileDataDto query = KingDeeFileDataDto.builder()
                .formId(formId)
                .erpId(dto.getFlowOrderInfoId().toString())
                .url(fileUrl)
                .build();

        List<KingDeeFileDataDto> existFile = kingDeeFileDataService.getByBusinessIdAndUri(query);
        if (!CollectionUtils.isEmpty(existFile)) {
            log.info("当前附件已经推送过金蝶，{}", JSON.toJSONString(query));
            return null;
        }

        return KingDeeFileDataDto.builder()
                .fileName("合同_" + dto.getFlowOrderNo())
                .aliasFileName("合同_" + dto.getFlowOrderNo())
                .billNo(dto.getFlowOrderInfoNo())
                .formId(formId)
                .isLast(true)
                .fId(dto.getDataId().toString())
                .url(fileUrl)
                .erpId(dto.getFlowOrderInfoId().toString())
                .businessId(formId + dto.getFlowOrderInfoId().toString())
                .build();
    }
}
