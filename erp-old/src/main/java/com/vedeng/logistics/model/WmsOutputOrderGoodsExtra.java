package com.vedeng.logistics.model;

import java.io.Serializable;
import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

/**
    * 出入库商品额外信息表
    */
@Data
public class WmsOutputOrderGoodsExtra extends BaseEntity implements Serializable {
    /**
    * 主键
    */
    private Long warehouseGoodsExtraId;

    /**
    * 出库单的id
    */
    private Long wmsOutputOrderId;

    /**
    * sku编号
    */
    private String skuNo;

    /**
    * 添加产品归属人name信息
    */
    private String productBelongNameInfo;

    /**
    * 添加产品归属人ID信息
    */
    private String productBelongIdInfo;

    /**
    * 产品经理或者助理是否审核 0-否 1-是
    */
    private Integer productAudit;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;


    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}