package com.vedeng.goods.dao;

import com.newtask.model.SkuInfoImageDto;
import com.newtask.model.TraderInfoImageDto;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import java.util.List;
@Component
public interface PriceCenterTraderInfoImageMapper {
    List<TraderInfoImageDto> getTraderInfoImage();

    List<TraderInfoImageDto> getTraderInfoImageAll();
}
