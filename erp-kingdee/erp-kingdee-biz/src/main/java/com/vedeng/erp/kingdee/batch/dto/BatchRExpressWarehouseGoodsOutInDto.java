package com.vedeng.erp.kingdee.batch.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
    * 直发物流和金蝶出入库关系
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BatchRExpressWarehouseGoodsOutInDto {
    /**
    * id
    */
    private Integer rExpressWarehouseGoodsOutInId;

    /**
    * 快递主表id
    */
    private Integer expressId;

    /**
    * 对接金蝶出入库的主表id
    */
    private Integer warehouseGoodsOutInId;

    /**
    * 是否删除 0 否 1 是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者id
    */
    private Integer creator;

    /**
    * 修改者id
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;
}