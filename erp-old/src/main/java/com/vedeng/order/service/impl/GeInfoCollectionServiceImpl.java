package com.vedeng.order.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.ObjectUtils;
import com.vedeng.order.dao.GeInfoCollectionMapper;
import com.vedeng.order.dao.VBuyorderSncodeMasterMapper;
import com.vedeng.order.dao.VBuyorderSncodeSlaveMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.ge.MasterSlaveSno;
import com.vedeng.order.model.ge.VBuyorderSncodeMaster;
import com.vedeng.order.model.ge.VBuyorderSncodeSlave;
import com.vedeng.order.service.GeInfoCollectionService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("GeInfoCollectionService")
public class GeInfoCollectionServiceImpl implements GeInfoCollectionService {

    @Resource
    private GeInfoCollectionMapper geInfoCollectionMapper;

    @Resource
    private VBuyorderSncodeMasterMapper vBuyorderSncodeMasterMapper;

    @Resource
    private VBuyorderSncodeSlaveMapper vBuyorderSncodeSlaveMapper;


    @Override
    public ResultInfo upadteGeTraderInfo(Buyorder buyorder, String traderContactStr, String traderAddressStr) {
        // 获取页面提交的供应商联系人数据
        String SupplierContacts = traderContactStr;
        if (ObjectUtils.notEmpty(SupplierContacts)) {
            String[] contacts = SupplierContacts.split("\\|");
            if (contacts.length > 0) {
                buyorder.setTraderContactId(Integer.valueOf(contacts[0]));
            }
            if (contacts.length > 1) {
                buyorder.setTraderContactName(contacts[1]);
            }
            if (contacts.length > 2) {
                buyorder.setTraderContactMobile(contacts[2]);
            }
            if (contacts.length > 3) {
                buyorder.setTraderContactTelephone(contacts[3]);
            }
        }

        // 获取页面提交的供应商地址数据
        String supplierAddress =traderAddressStr;
        if (ObjectUtils.notEmpty(supplierAddress)) {
            String[] addresss = supplierAddress.split("\\|");
            if (addresss.length > 0) {
                buyorder.setTraderAddressId(Integer.valueOf(addresss[0]));
            }
            if (addresss.length > 1) {
                buyorder.setTraderArea(addresss[1]);
            }
            if (addresss.length > 2) {
                buyorder.setTraderAddress(addresss[2]);
            }
        }
        try{
            geInfoCollectionMapper.updateBuyorderTraderInfo(buyorder);
        }catch (Exception e){
            throw new RuntimeException();
        }
        return new ResultInfo(0, "更新成功");
    }

    @Override
    public ResultInfo upadteGeConInfo(String geBuyorderGoodsIds, String geContractNo, String geSaleContractNo) {
        Map<String,Object> mapForContract= new HashMap<>();
        mapForContract.put("geContractNo",geContractNo);
        mapForContract.put("geSaleContractNo",geSaleContractNo);
        if (StringUtils.isEmpty(geBuyorderGoodsIds)) {
            return new ResultInfo(-1, "更新失败");
        }
        String[] skuArray = geBuyorderGoodsIds.split(",");
        try{
            for (int i = 0; i < skuArray.length; i++) {
                mapForContract.put("buyorderGoodsId",Integer.parseInt(skuArray[i]));
                if (geInfoCollectionMapper.upadteGeInfo(mapForContract)>0){
                    mapForContract.remove("buyorderGoodsId") ;
                }
            }
        }catch (Exception e){
            throw new RuntimeException();
        }
        return new ResultInfo(0, "更新成功");
    }

    @Override
    public ResultInfo delGeInfoCollection(Integer buyorderGoodsId) {
        try {
            //根据buyorderGoodsId将采购商品表中的GE合同编号和GE销售合同编号删除
            Map<String,Object> map= new HashMap<>();
            map.put("geContractNo","");
            map.put("geSaleContractNo","");
            map.put("buyorderGoodsId",buyorderGoodsId);
            geInfoCollectionMapper.upadteGeInfo(map);
            delMasterSlave(buyorderGoodsId);
            }
         catch (Exception e) {
             throw new RuntimeException();
        }
        return new ResultInfo(0, "删除成功");
    }

    @Override
    //删除探头和主机
    public void delMasterSlave(Integer buyorderGoodsId) {
        //根据主机序列号将每个主机序列号下的探头删掉
        List<VBuyorderSncodeMaster> vBuyorderSncodeMasterList = vBuyorderSncodeMasterMapper.getGeMasterInfo(buyorderGoodsId);
        try{
            if (CollectionUtils.isNotEmpty(vBuyorderSncodeMasterList)) {
                for (VBuyorderSncodeMaster vbm : vBuyorderSncodeMasterList) {
                    vBuyorderSncodeSlaveMapper.delGeSlaveInfoCollection(vbm.getBuyorderSncodeMasterId());
                }
                //根据buyorderGoodsId查询出该buyorderGoodsId下包含的所有主机序列号，并将所有主机删除
                vBuyorderSncodeMasterMapper.delGeMasterInfoCollection(buyorderGoodsId);
            }
        }catch (Exception e){
            throw new RuntimeException();
        }
    }

    @Override
    public ResultInfo saveSncode(String sku, String masterSlavejson, Integer buyorderGoodsId, Integer goodsId, User user) {
        try{
            //删除原有探头和主机
            delMasterSlave(buyorderGoodsId);
            //将数据插入对应的数据库
            List<MasterSlaveSno> masterSlaveSnoList= JSON.parseArray(masterSlavejson,MasterSlaveSno.class);
            if (CollectionUtils.isNotEmpty(masterSlaveSnoList)){
                VBuyorderSncodeMaster vBuyorderSncodeMaster=new VBuyorderSncodeMaster();
                vBuyorderSncodeMaster.setSku(sku);
                vBuyorderSncodeMaster.setSkuId(goodsId);
                vBuyorderSncodeMaster.setRelatedId(buyorderGoodsId);
                vBuyorderSncodeMaster.setCreator(user.getUserId());
                vBuyorderSncodeMaster.setUpdater(user.getUserId());
                VBuyorderSncodeSlave vBuyorderSncodeSlave=new VBuyorderSncodeSlave();
                vBuyorderSncodeSlave.setCreator(user.getUserId());
                vBuyorderSncodeSlave.setUpdater(user.getUserId());
                masterSlaveSnoList.forEach(itemm->{
                    String masterSno=itemm.getvBuyorderSncodeMaster();
                    vBuyorderSncodeMaster.setMasterSncode(masterSno);
                    vBuyorderSncodeMasterMapper.insertSelective(vBuyorderSncodeMaster);
                    VBuyorderSncodeMaster vBuyorderSncodeMaster1=vBuyorderSncodeMasterMapper.selectByPrimaryKey(vBuyorderSncodeMaster);
                    if (vBuyorderSncodeMaster1!=null){
                        Integer masterSnoId=vBuyorderSncodeMaster1.getBuyorderSncodeMasterId();
                        vBuyorderSncodeSlave.setBuyorderSncodeMasterId(masterSnoId);
                        itemm.getvBuyorderSncodeSlaves().forEach(items->{
                            vBuyorderSncodeSlave.setSlaveSncode(items);
                            vBuyorderSncodeSlaveMapper.insertSelective(vBuyorderSncodeSlave);
                        });
                    }
                });
            }
        }catch (Exception e){
            throw new RuntimeException();
        }
        return new ResultInfo(0,"保存成功！");
    }

    @Override
    public ResultInfo checkMasterSno(String masterSncode,Integer buyorderGoodsId) {
        VBuyorderSncodeMaster vBuyorderSncodeMaster=new VBuyorderSncodeMaster();
        vBuyorderSncodeMaster.setMasterSncode(masterSncode);
        vBuyorderSncodeMaster.setRelatedId(buyorderGoodsId);
        if (CollectionUtils.isNotEmpty(vBuyorderSncodeMasterMapper.checkMasterSno(vBuyorderSncodeMaster))){
            return new ResultInfo(0,"已存在主机序列号");
        }
        return new ResultInfo(-1,"可以使用该主机序列号");
    }

    @Override
    public ResultInfo checkSlaveSno(String slaveSno,String masterSlavejson) {
        List<MasterSlaveSno> masterSlaveSnoList= JSON.parseArray(masterSlavejson,MasterSlaveSno.class);
        List<Integer> masterSnoCodeList=new ArrayList<>();
        VBuyorderSncodeMaster vBuyorderSncodeMaster=new VBuyorderSncodeMaster();
        if (CollectionUtils.isNotEmpty(masterSlaveSnoList)){
            masterSlaveSnoList.forEach(item->{
                vBuyorderSncodeMaster.setMasterSncode(item.getvBuyorderSncodeMaster());
                VBuyorderSncodeMaster vBuyorderSncodeMaster1=vBuyorderSncodeMasterMapper.selectByPrimaryKey(vBuyorderSncodeMaster);
                if (vBuyorderSncodeMaster1!=null){
                    Integer masterSnoId=vBuyorderSncodeMaster1.getBuyorderSncodeMasterId();
                    masterSnoCodeList.add(masterSnoId);
                }
            });
        }
        if (CollectionUtils.isNotEmpty(vBuyorderSncodeSlaveMapper.checkSlaveSno(slaveSno,masterSnoCodeList))){
            return new ResultInfo(0,"已存在主机序列号");
        }
        return new ResultInfo(-1,"可以使用该主机序列号");
    }
}
