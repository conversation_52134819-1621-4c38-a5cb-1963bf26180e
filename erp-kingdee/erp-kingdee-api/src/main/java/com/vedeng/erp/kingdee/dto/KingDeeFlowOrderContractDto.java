package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 流转单合同DTO
 */
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@WriteBackField(needBackField = {"FID"})
public class KingDeeFlowOrderContractDto extends KingDeeMqBaseDto {
    /**
     * 主键
     */
    private Integer kingDeeFlowOrderContractId;

    /**
     * 云星空系统单据FID值
     */
    @WriteBackField
    @KingDeeID
    private Integer fId;

    /**
     * 组织
     */
    private String fQzokOrgid;

    /**
     * 合同号
     */
    private String fQzokHth;

    /**
     * 合同日期
     */
    private String fQzokHtrq;

    /**
     * 合同金额
     */
    private String fQzokHtje;

    /**
     * 税率
     */
    private String fQzokSll;

    /**
     * 流转单编号
     */
    @BusinessID("F_QZOK_DDH")
    private String fQzokDdh;

    /**
     * 单据编号
     */
    private String FBillNo;

    /**
     * 业务类型 1.采购 2.销售
     */
    private Integer businessType;

    @Override
    public String getFormId() {
        return businessType == 1 ? KingDeeFormConstant.PUR_PurchaseOrder : KingDeeFormConstant.SAL_SaleOrder;
    }
}
