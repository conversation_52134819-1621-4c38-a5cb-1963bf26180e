package com.vedeng.erp.finance.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.finance.domain.dto.AcceptanceBillDto;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.domain.entity.AcceptanceBillEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface AcceptanceBillService {

    int deleteByPrimaryKey(Long acceptanceBilId);

    int insert(AcceptanceBillEntity record);

    int insertSelective(AcceptanceBillEntity record);

    AcceptanceBillEntity selectByPrimaryKey(Long acceptanceBilId);

    int updateByPrimaryKeySelective(AcceptanceBillEntity record);

    int updateByPrimaryKey(AcceptanceBillEntity record);

    /**
     * 分页查询
     *
     * @param query
     * @return
     */
    PageInfo<AcceptanceBillDto> page(PageParam<AcceptanceBillDto> query);

    List<AcceptanceBillEntity> findByBillNumber(String billNumber);

    List<AcceptanceBillEntity> findByBankBillIdIn(List<Integer> bankBillIdCollection);

    /**
     * 获取非终止状态的承兑汇票 not (5,6)
     */
    List<AcceptanceBillEntity> findAcceptanceBillNonTerminatedState();

    boolean signup(String billNumber);

    boolean draftDiscount(String billNumber,String rate,Integer userId);


    /**
     * 刷新贴现的列表
     */
    void refreshDraftDiscount();

    /**
     * 自动签收
     */
    void autoSignup();
}
