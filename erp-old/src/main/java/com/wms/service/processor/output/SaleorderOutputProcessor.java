package com.wms.service.processor.output;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.constant.*;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.finance.dto.InvoiceApplyDetailDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.saleorder.dto.SaleOrderGoodsDetailDto;
import com.vedeng.erp.saleorder.service.SaleOrderGoodsApiService;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.InvoiceApplyDetail;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.wms.service.*;
import com.wms.constant.*;
import com.wms.dao.WmsOutInOrderConcatMapper;
import com.wms.dto.*;
import com.wms.model.po.WmsOutInOrderConcat;
import com.wms.service.processor.AbstractOutputOrderProcessor;
import com.wms.service.stockcalculate.SaleorderOutCaculateImpl;
import com.wms.service.util.GlobalThreadPool;
import com.wms.service.util.WmsCommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName SaleorderOutputProcessor.java
 * @Description TODO  销售单出库处理器
 * @createTime 2020年08月03日 09:55:00
 */
@Service
public class SaleorderOutputProcessor extends AbstractOutputOrderProcessor {

    Logger logger= LoggerFactory.getLogger(SaleorderOutputProcessor.class);
    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private WmsOutInOrderConcatMapper wmsOutInOrderConcatMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Resource
    private WarehouseStockService warehouseStockServiceImpl;

    @Autowired
    @Qualifier("saleorderOutCaculateImpl")
    private SaleorderOutCaculateImpl stockinfoCaculateInterface;

    @Value("${orderIsGoodsPeer_Time}")
    protected Long orderIsGoodsPeerTime;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;

    @Value("${wms_client_key}")
    private String wmsClientKey;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private ExpressService expressService;

    @Value("${erp_url}")
    protected String erpUrl;

    @Resource
    private ExpressMapper expressMapper;

    @Autowired
    private WmsImagesService wmsImagesService;

    @Value("${oss_http}")
    private String ossHttp;

    @Value("${WMS_CHECK_STOCK_ALARM_MAIL}")
    protected String  WMS_CHECK_STOCK_ALARM_MAIL;

    @Autowired
    private JavaMailSender javaMailSender;

    @Autowired
    private SimpleMailMessage simpleMailMessage;


    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private FullyDigitalInvoiceApiService fullyDigitalInvoiceApiService;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;
    @Autowired
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;
    @Autowired
    private SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    private OrderPeerApiService orderPeerApiService;

    @Autowired
    private WxRobotService wxRobotService;

    @Value("${invoice_notice_robot_num}")
    public String invoiceNoticeRobotNum;
    @Override
    protected String getBusinessKey(OutputDto outputDto) {
        return "saleorderOutput" +  outputDto.getOrderNo();
    }

    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected void commonValidator(OutputDto requestBean) throws Exception {
        if(!WmsInterfaceOrderType.OUT_SALE_OUT.equals(requestBean.getOrderType())){
            throw new Exception("出库单:"+requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"的类型错误!");
        }
        if(CollectionUtils.isEmpty(requestBean.getDetails())){
            throw new Exception("出库单:"+requestBean.getSOReference1()+"WMS单号"+requestBean.getOrderNo()+"的详情为空!");
        }
        if(StringUtil.isEmpty(requestBean.getSOReference1())){
            throw new Exception("WMS单号:"+requestBean.getOrderNo()+"的ERP单号为空!");
        }
        //本次出库数量
        Map<String,Integer> nowOutNumMap = new HashMap<>();
        for (OutputGoodDto detail : requestBean.getDetails()) {
            if(StringUtil.isEmpty(detail.getSKU())){
                throw new Exception("出库单:"+requestBean.getSOReference1()+"WMS单号:"+requestBean.getOrderNo()+"的详情为空!");
            }
            if(StringUtil.isEmpty(detail.getLotAtt08())){
                throw new Exception("出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"质量状态为空");
            }
            if(detail.getQtyShipped() == null){
                throw new Exception("出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"出库数量为空");
            }
            if(StringUtil.isBlank(detail.getUserDefine1())){
                throw new Exception("出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+",SKU:"+detail.getSKU()+"详情ID为空");
            }else{
                Integer saleorderGoodsId = Integer.valueOf(detail.getUserDefine1());
                SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);
                if(ErpConst.ONE.equals(saleorderGoods.getDeliveryDirect())){
                    String error = error(requestBean, detail.getSKU());
                    throw new Exception(error);
                }
            }
            Integer outNum = nowOutNumMap.get(detail.getSKU()) == null ? 0 : nowOutNumMap.get(detail.getSKU());
            nowOutNumMap.put(detail.getSKU(),outNum + detail.getQtyShipped().intValue());
        }
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1()));
        saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
        List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId());
        Map<String,Integer> needNumMap = new HashMap<>();
        for (SaleorderGoods saleorderGoodsVo : saleorderGoodsList) {
            if(saleorderGoodsVo.getDeliveryDirect().equals(1)){
                return;
            }
            Integer needNum = needNumMap.get(saleorderGoodsVo.getSku()) == null ? 0 : needNumMap.get(saleorderGoodsVo.getSku());
            needNumMap.put(saleorderGoodsVo.getSku(),needNum + saleorderGoodsVo.getNum() - saleorderGoodsVo.getDeliveryNum());
        }
        logger.info("commonValidator WMS"+"出库单:"+requestBean.getSOReference1()+",WMS单号:"+requestBean.getOrderNo()+" ,needNumMap:"+needNumMap +" ,nowOutNumMap:"+ nowOutNumMap);
        for (String sku : nowOutNumMap.keySet()) {
            Integer needNum = needNumMap.get(sku) == null ? 0 : needNumMap.get(sku);
            Integer outNum = nowOutNumMap.get(sku) == null ? 0 : nowOutNumMap.get(sku);
            if(needNum < outNum){
                String error = error(requestBean, sku);
                throw new Exception(error);
            }
        }
    }

    private String error(OutputDto requestBean, String sku) {
        String error = "出库单:"+ requestBean.getSOReference1()+"WMS单号"+ requestBean.getOrderNo()+"SKU"+ sku +"出库数量超出可出库数量";
        sendMail("复核出库告警",error,WMS_CHECK_STOCK_ALARM_MAIL);
        return error;
    }

    @Override
    protected void updateOrderData(OutputDto requestBean) {
        logger.info("出库单回传参数 outputdto:{}",requestBean.toString());
        List<OutputGoodDto> details = requestBean.getDetails();
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1()));
        saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
        List<SaleorderGoodsVo> saleorderGoodsVoList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
        //活动商品出库数量
        Map<String,Integer> actionGoodsMap = new HashMap<>();
        //普通商品出库数量
        Map<String,Integer> goodsMap = new HashMap<>();
        for (OutputGoodDto detail : details) {
            if(LogicalEnum.HDC.getLogicalWarehouseCode().equals(detail.getLotAtt08())){
                Integer outNum = actionGoodsMap.get(detail.getSKU()+"-"+detail.getUserDefine1());
                if(outNum == null){
                    outNum = 0;
                }
                actionGoodsMap.put(detail.getSKU()+"-"+detail.getUserDefine1(),new BigDecimal(outNum).add(detail.getQtyShipped()).intValue());
            }else{
                Integer outNum = goodsMap.get(detail.getSKU()+"-"+detail.getUserDefine1());
                if(outNum == null){
                    outNum = 0;
                }
                goodsMap.put(detail.getSKU()+"-"+detail.getUserDefine1(),new BigDecimal(outNum).add(detail.getQtyShipped()).intValue());
            }
        }
        for (SaleorderGoodsVo saleorderGoodsVo : saleorderGoodsVoList) {
            Integer realNum = saleorderGoodsVo.getNum() - getHistoryReturnNum(saleorderGoodsVo.getSaleorderGoodsId());
            Integer returnNum = afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsVo.getSaleorderGoodsId());

            SaleorderGoods updatesaleorderGoods = new SaleorderGoods();
            updatesaleorderGoods.setSaleorderGoodsId(saleorderGoodsVo.getSaleorderGoodsId());
            updatesaleorderGoods.setModTime(System.currentTimeMillis());
            updatesaleorderGoods.setDeliveryTime(System.currentTimeMillis());
            if(saleorderGoodsVo.getGoodsId().equals(GoodsConstants.FREIGHT) && !saleorderGoodsVo.getDeliveryStatus().equals(2)){
                updatesaleorderGoods.setDeliveryNum(saleorderGoodsVo.getNum());
                updatesaleorderGoods.setDeliveryStatus(2);
                saleorderGoodsMapper.updateByPrimaryKeySelective(updatesaleorderGoods);
            }
            Integer outNum;
            if(saleorderGoodsVo.getIsActionGoods() > 0){
                 outNum = actionGoodsMap.get(saleorderGoodsVo.getSku()+"-"+saleorderGoodsVo.getSaleorderGoodsId());
            }else{
                 outNum = goodsMap.get(saleorderGoodsVo.getSku()+"-"+saleorderGoodsVo.getSaleorderGoodsId());
            }
            if(outNum == null || outNum == 0 ){
                continue;
            }
            updatesaleorderGoods.setDeliveryNum(saleorderGoodsVo.getDeliveryNum() + outNum);
            updatesaleorderGoods.setDeliveryStatus(realNum - returnNum <= updatesaleorderGoods.getDeliveryNum() ? 2 : 1);
            logger.info("销售订单明细Id{}商品sku{}，发货状态{}",updatesaleorderGoods.getSaleorderGoodsId(),updatesaleorderGoods.getSku(),updatesaleorderGoods.getDeliveryStatus());
            saleorderGoodsMapper.updateByPrimaryKeySelective(updatesaleorderGoods);
        }
        Saleorder updateOrder = new Saleorder();
        updateOrder.setSaleorderId(saleorder.getSaleorderId());
        updateOrder.setDeliveryStatus(2);
        List<SaleorderGoodsVo> nowGoodsList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
        for (SaleorderGoodsVo saleorderGoodsVo : nowGoodsList) {
            Integer realNum = saleorderGoodsVo.getNum() - getHistoryReturnNum(saleorderGoodsVo.getSaleorderGoodsId());
            if(realNum.equals(0)){
                continue;
            }
            if(!saleorderGoodsVo.getGoodsId().equals(GoodsConstants.FREIGHT)
            && !saleorderGoodsVo.getDeliveryStatus().equals(2)){
                updateOrder.setDeliveryStatus(1);
                break;
            }
        }
        updateOrder.setModTime(System.currentTimeMillis());
        updateOrder.setDeliveryTime(System.currentTimeMillis());
        saleorderMapper.updateByPrimaryKeySelective(updateOrder);
    }

    @Override
    protected int getOperateType(OutputDto requestBean) {
        return StockOperateTypeConst.WAREHOUSE_OUT;
    }

    @Override
    protected int getWmsLogicalOperateType() {
        return WmsLogicalOperateTypeEnum.SALEORDER_TYPE.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(OutputGoodDto outputGoodDto) {
        return Integer.valueOf(outputGoodDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return stockinfoCaculateInterface.calculateStockInfo(stockCalculateList);
    }
    private Integer getHistoryReturnNum(Integer saleorderGoodsId) {
        return  afterSalesGoodsMapper.getSaleorderAftersaleReturnGoods(saleorderGoodsId);
    }
    @Override
    protected void synchronizeStockData(OutputDto outputOrderDto) throws Exception {
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(WmsCommonUtil.getOriginalOrderNo(outputOrderDto.getSOReference1()));
        saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
        List<StockCalculateDto> stockCalculateList  = new ArrayList<>();

        outputOrderDto.getDetails().stream().forEach(outputOrderGoodsDto -> {
            StockCalculateDto stockCalculateDto = new StockCalculateDto();
            stockCalculateDto.setSku(outputOrderGoodsDto.getSKU());
            stockCalculateDto.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(outputOrderGoodsDto.getLotAtt08()));
            stockCalculateDto.setStockNum(outputOrderGoodsDto.getQtyShipped().intValue());
            stockCalculateDto.setOccupyNum(outputOrderGoodsDto.getQtyShipped().intValue());
            stockCalculateList.add(stockCalculateDto);
        });

        List<WarehouseDto> warehouseDtos = getWarehouseStockList(stockCalculateList);
        //库存策略计算
        StockInfoDto stockInfoDto  = new StockInfoDto();
        stockInfoDto.setRelatedNo(outputOrderDto.getSOReference1());
        for (WarehouseDto warehouseDto : warehouseDtos) {
            if(LogicalEnum.HDC.getLogicalWarehouseId().equals(warehouseDto.getLogicalWarehouseId())){
                warehouseDto.setActionId(saleorder.getActionId());
            }
        }
        stockInfoDto.setWarehouseStockList(warehouseDtos);

        //更新库存服务
        warehouseStockService.updateStockInfo(stockInfoDto);
    }
    /**
     * 自定义的相关处理
     * @param requestBean
     */
    @Override
    protected void customHandle(OutputDto requestBean) {
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderNo(WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1()));
        saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
        orderCommonService.updateSaleOrderDataUpdateTime(saleorder.getSaleorderId(),null, OrderDataUpdateConstant.SALE_ORDER_OUT);
        warehouseStockServiceImpl.updateOccupyStockService(saleorder,0);

        //票货同行发票业务处理
        // 处理 数电发票 Digital Electronic Invoice
        dealSaleorderInvoice(requestBean,saleorder.getSaleorderId());

        //保存销售单和入库单关系
        dealOutorderAndInorderConcat(saleorder,requestBean);

        dealReportImage(saleorder,requestBean);

        //出库单业务处理
        dealSaleorderPrintOut(requestBean,saleorder);

        //生成带价格和不带价格出库单
        generatePrintOutOrder(saleorder);

    }

    public void dealReportImage(Saleorder saleorder, OutputDto requestBean) {
        logger.info("处理出库单图片信息start orderNo:{},requestBean:{}", saleorder.getSaleorderNo(), JSON.toJSONString(requestBean));
        try {
            wmsImagesService.dealReportImage(requestBean,SysOptionConstant.QUALITY_REPORT_SALEORDER);
        }catch (Exception e){
            logger.error("WMS 推送质检报告下载error 销售单号:"+saleorder.getSaleorderNo(),e);
        }
    }


    /**
     *  处理出库单信息
     *
     * @param requestBean
     * @param saleorder
     */
    private void dealSaleorderPrintOut(OutputDto requestBean, Saleorder saleorder) {
        Map<Integer,SaleorderGoods> outGoodsMap = new HashMap<>();
        for (OutputGoodDto detail :  requestBean.getDetails()) {
            int saleorderGoodsId = getRelateId(detail);
            SaleorderGoods saleorderGoods = outGoodsMap.get(saleorderGoodsId);
            if(saleorderGoods == null){
                saleorderGoods = new SaleorderGoods();
                saleorderGoods.setSku(detail.getSKU());
                saleorderGoods.setSaleorderGoodsId(saleorderGoodsId);
                SaleorderGoods saleorderGoodsInfo = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoodsId);
                saleorderGoods.setDeliveryNum(detail.getQtyShipped().intValue());
                saleorderGoods.setPrice(saleorderGoodsInfo.getPrice());
            }else{
                saleorderGoods.setDeliveryNum(saleorderGoods.getDeliveryNum() + detail.getQtyShipped().intValue());
            }
            outGoodsMap.put(saleorderGoodsId,saleorderGoods);
        }

        Express express = new Express();
        express.setLogisticsNo(saleorder.getSaleorderNo());
        express.setRealWeight(BigDecimal.ZERO);
        Integer goodsNum = outGoodsMap.size();
        //件数
        express.setJ_num(goodsNum);
        //计费重量
        express.setAmountWeight(BigDecimal.ZERO);
        //托寄物
        express.setMailGoods("货物");
        express.setMailGoodsNum(goodsNum);
        //运费
        express.setAmount(BigDecimal.ZERO);
        express.setLogisticsId(0);
        express.setLogisticsComments(ErpConst.VIRTUAL_EXPRESS_NO);
        express.setMailCommtents("");
        express.setWmsOrderNo(requestBean.getSOReference1()+"/"+requestBean.getOrderNo());


        StringBuffer sb = new StringBuffer();
        for (Integer id : outGoodsMap.keySet()) {
            SaleorderGoods saleorderGoods = outGoodsMap.get(id);
            sb.append(id).append("|")
                    .append(saleorderGoods.getDeliveryNum())
                    .append("|")
                    .append(saleorderGoods.getPrice())
                    .append("|")
                    .append(saleorderGoods.getSku())
                    .append("_");
        }
        delReviewExpress(express,saleorder.getSaleorderNo(),saleorder);
        //若需使用添加ERP的快递信息 在resultInfo中取
        ResultInfo resultInfo = expressService.saveExpressInfo(express, DateUtil.convertString(System.currentTimeMillis(), DateUtil.DATE_FORMAT),
                BigDecimal.ZERO, sb.toString(), saleorder, "1", saleorder.getSaleorderId(), null,"保存虚拟单" , null);

        Express expressDto = (Express) resultInfo.getData();
        if (!resultInfo.getCode().equals(0) && expressDto == null) {
            logger.error("出库单保存失败 wms单号:{},erp单号:{}" , requestBean.getOrderNo(), requestBean.getSOReference1());
//            throw new RuntimeException("出库单保存失败 wms单号" + requestBean.getOrderNo() + "erp单号" + requestBean.getSOReference1());
            return;
        }
        String printOutOrderUrl = getPrintOutOrderUrl(saleorder, expressDto);
        Express updateExpress = new Express();
        updateExpress.setExpressId(expressDto.getExpressId());
        updateExpress.setIsEnable(0);
        expressMapper.updateByPrimaryKeySelective(updateExpress);
        logger.info("复核回传出库单保存PDF路径 orderNos:{},printOutOrderUrl:{}", saleorder.getSaleorderNo(), printOutOrderUrl);
        //出库单信息上传OSS
        GlobalThreadPool.submitMessage(new Runnable() {
            @Override
            public void run() {
                try {
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    logger.info("开始执行html2PdfUrl，uuid：{}",uuid);
                    html2PdfUrl(requestBean, saleorder, printOutOrderUrl);
                    logger.info("结束执行html2PdfUrl，uuid：{}",uuid);
                } catch (Exception e) {
                    logger.error("html2PdfUrl error", e);
                }
            }
        });

        logger.info("出库单参数 单号:{},url:{}",requestBean.getSOReference1(),printOutOrderUrl);
        GlobalThreadPool.submitMessage(new Runnable() {
            @Override
            public void run() {
                try {
                    String uuid = UUID.randomUUID().toString().replace("-", "");
                    logger.info("开始执行putPrintOutOrder，uuid：{}",uuid);
                    Thread.sleep(1000L);
                    putPrintOutOrder(requestBean, printOutOrderUrl);
                    logger.info("结束执行putPrintOutOrder，uuid：{}",uuid);
                }catch (Exception e){
                    logger.error("sendPrint error",e);
                }
            }
        });

    }

    /**
     * 出库单信息上传OSS
     *
     * @param requestBean
     * @param order
     * @param printOutOrderUrl
     */
    private void html2PdfUrl(OutputDto requestBean, Saleorder order, String printOutOrderUrl) {
        logger.info("出库单信息上传OSS start saleOrderNo:{},printOutOrderUrl:{}", requestBean.getSOReference1(), printOutOrderUrl);
        if (StringUtil.isBlank(printOutOrderUrl)){
            logger.info("出库单地址为空 saleOrderNo:{}", requestBean.getSOReference1());
            return;
        }
        printOutOrderUrl = printOutOrderUrl + "&wms_client_key=" + wmsClientKey + "&wmsClientUserName=vedeng";
        String html2PdfUrl = html2PdfDomain + "/api/render";
        UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
        urlToPdfParam.setUrl(printOutOrderUrl);
        UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
        UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm","1cm","1cm","1cm");
        pdf.setMargin(margin);
        urlToPdfParam.setPdf(pdf);
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl,"pdf","出库单" + requestBean.getSOReference1(),urlToPdfParam);
        if (StringUtil.isBlank(ossUrl)){
            logger.warn("html2PdfUrl warn，saleOrderNo:{}", requestBean.getSOReference1());
            return;
        }
        logger.info("出库单信息上传成功 saleOrderNo:{},ossUrl:{}", requestBean.getSOReference1(), ossUrl);
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ErpConst.OUTBOUND_ORDER_TYPE);
        attachment.setAttachmentFunction(ErpConst.OUTBOUND_ORDER_FUNCTION);
        attachment.setRelatedId(order.getSaleorderId());
        attachment.setAlt(requestBean.getOrderNo());

        String domainAndUri = ossUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf("/");
        String domain = domainAndUri.substring(0,domainIndex);
        String uri = domainAndUri.substring(domainIndex);

        attachment.setDomain(domain);
        attachment.setUri(uri);
        attachment.setOssResourceId(uri);
        attachment.setName(requestBean.getSOReference1());

        logger.info("保存出库单路径信息 orderNo:{},attachment:{}",
                order.getSaleorderNo(), JSON.toJSONString(attachment));
        attachmentMapper.insertSelective(attachment);
    }

    private void delReviewExpress(Express express, String erpOrderNo, Saleorder saleorderInfo) {
        Express search = new Express();
        search.setWmsOrderNo(express.getWmsOrderNo());
        search.setLogisticsNo(erpOrderNo);
        search.setLogisticsComments("虚拟快递单");
        Express oldExpress2 = expressMapper.getExpressInfoByLogNoAndComments(search);
        if(oldExpress2 != null){
            oldExpress2.setIsEnable(0);
            oldExpress2.setSaleorderId(saleorderInfo.getSaleorderId());
            expressMapper.updateByPrimaryKeySelective(oldExpress2);

        }
    }
    private void dealSaleorderInvoice(OutputDto requestBean, Integer saleorderId) {
        /**
         * 复核完成满足开票条件的销售订单需要保存开票申请并开具发票
         * 票货同行”条件：
         * （1）HC或ZXF订单
         * （2）开票方式为电子发票
         * （3）订单中全部商品的的发货方式为“普发”
         *  (4) 无退票,退货售后单
         *  (5)“发票是否寄送”字段为：寄送
         *  (6)“货票地址是否相同”字段为“相同”
         */
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderId);
        logger.info("复核出库处理票货同行信息 订单ID:" + saleorderId);

        if (saleorder == null || CollectionUtils.isEmpty(requestBean.getDetails())){
            logger.info("WMS信息不全 details:{}" + requestBean.toString());
            throw new RuntimeException("WMS信息不全信息不全 details:{}" + requestBean.toString());
        }

        if (!orderPeerApiService.getOrderIsGoodsPeer(saleorder.getSaleorderId())){
            logger.info("不符合票货同行条件" + requestBean.toString());
            pushInvoiceInfo(requestBean, new InvoiceApply(), WmsInvoiceFlagEnum.NO_NEED_TO_INVOCIE.getCode(),
                    "复核完成！以下订单不满足票货同行条件，无需开票：" + requestBean.getSOReference1());
            return;
        }

        logger.info("符合出库订单复核票货同行条件 订单ID:{}", saleorderId);
        Saleorder saleorderBaseInfo = saleorderMapper.getSaleOrderById(saleorderId);
        logger.info("复核出库处理票货同行订单信息 saleorderBaseInfo:{}" + JSON.toJSONString(saleorderBaseInfo));
        saleorder.setInvoiceMethod(saleorderBaseInfo.getInvoiceMethod());
        if (saleorderBaseInfo.getInvoiceSendNode() == null){
            logger.error("票货同行订单信息未配置发票寄送节点 订单号:" + saleorderBaseInfo.getSaleorderNo());
            throw new RuntimeException("票货同行订单信息未配置发票寄送节点 订单号:" + saleorderBaseInfo.getSaleorderNo());
        }

        if (!orderPeerApiService.checkNoAfterSale(saleorderId)) {
            logger.error("存在进行中售后 订单号:" + saleorderBaseInfo.getSaleorderNo());
            return;
        }
        switch (saleorderBaseInfo.getInvoiceSendNode()){
            case InvoiceSendNode.ONCE :{
                dealInvoiceWithOnce(requestBean, saleorder, saleorderBaseInfo);
                break;
            }
            case InvoiceSendNode.EVERY_TIME:{
                dealInvoiceWithEveryTime(requestBean, saleorder, saleorderBaseInfo);
                break;
            }
            default:{
                logger.error("票货同行发票寄送节点信息异常 saleorderBaseInfo:{}" + JSON.toJSONString(saleorderBaseInfo));
                throw new RuntimeException("票货同行发票寄送节点信息异常 订单号:" + saleorderBaseInfo.getSaleorderNo());
            }
        }
    }

    /**
     * “票货同行”订单的“发票寄送节点”字段选择：全部发货时一次寄送
     *    （1）复核完成时判断票货同行订单中的商品是否已全部发货（包含本次）：
     *      ① 若订单已全部发货，则生成开票申请，并按原流程开票/返回信息
     *      ② 若订单尚未全部发货，则复核完成时不生成开票申请，同时返回“无需开票”的结果
     *      开票申请中的商品为：订单中全部的商品
     *
     * @param requestBean
     * @param saleorder
     * @param saleorderBaseInfo
     */
    private void dealInvoiceWithOnce(OutputDto requestBean, Saleorder saleorder, Saleorder saleorderBaseInfo) {
        if (!saleorderBaseInfo.getDeliveryStatus().equals(2)){
            logger.info("票货同行订单信息未全部发货 订单号:" + saleorderBaseInfo.getSaleorderNo());
            pushInvoiceInfo(requestBean, new InvoiceApply(), WmsInvoiceFlagEnum.NO_NEED_TO_INVOCIE.getCode(),
                    "复核完成！以下订单不满足票货同行条件，无需开票：" + requestBean.getSOReference1());
            return;
        }

        List<SaleorderGoods> saleorderGoodsWithoutSpecial = saleorderGoodsMapper
                .getSaleorderGoodsListBySaleorderId(saleorder.getSaleorderId()).stream()
                .filter(item -> item.getIsDelete() != null && item.getIsDelete().equals(0))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleorderGoodsWithoutSpecial)){
            logger.error("票货同行订单商品异常 订单号" + saleorderBaseInfo.getSaleorderNo());
            throw new RuntimeException("票货同行订单商品异常 订单号" + saleorderBaseInfo.getSaleorderNo());
        }

        boolean isSalorderGoodsDelivery = true;
        for (SaleorderGoods saleorderGoods : saleorderGoodsWithoutSpecial) {
            if (! saleorderGoods.getNum().equals(saleorderGoods.getDeliveryNum())){
                isSalorderGoodsDelivery = false;
                break;
            }
        }
        if (! isSalorderGoodsDelivery){
            logger.info("票货同行订单商品信息未全部发货 订单号:" + saleorderBaseInfo.getSaleorderNo());
            pushInvoiceInfo(requestBean, new InvoiceApply(), WmsInvoiceFlagEnum.NO_NEED_TO_INVOCIE.getCode(),
                    "复核完成！以下订单不满足票货同行条件，无需开票：" + requestBean.getSOReference1());
            return;
        }

        logger.info("票货同行订单信息全部发货 准备开票 订单号" + saleorderBaseInfo.getSaleorderNo());
        ArrayList<InvoiceApplyDetail> invoiceApplyDetails = new ArrayList<>();
        saleorderGoodsWithoutSpecial.forEach(saleorderGoods -> {
            InvoiceApplyDetail invoiceApplyDetail = new InvoiceApplyDetail();
            invoiceApplyDetail.setSku(saleorderGoods.getSku());
            invoiceApplyDetail.setNum(new BigDecimal(saleorderGoods.getNum()));
            invoiceApplyDetail.setDetailgoodsId(saleorderGoods.getSaleorderGoodsId());
            invoiceApplyDetail.setProductName(saleorderGoods.getGoodsName());
            invoiceApplyDetail.setSpecModel(getSpecModel(saleorderGoods.getSaleorderGoodsId(),saleorderGoods.getModel(),saleorderGoods.getSpec()));
            invoiceApplyDetail.setUnit(saleorderGoods.getUnitName());
            invoiceApplyDetail.setTaxRate(new BigDecimal(sysOptionDefinitionMapper.selectByPrimaryKey(saleorderBaseInfo.getInvoiceType()).getComments()));
            invoiceApplyDetails.add(invoiceApplyDetail);
        });
        logger.info("票货同形生成invoiceApplyDetail,{}", JSONUtil.toJsonStr(invoiceApplyDetails));
        setInvoiceApplyInfoAndOpenInvoice(requestBean, saleorder, invoiceApplyDetails);
    }

    private String getSpecModel(Integer saleorderGoodsId,String model,String spec){
        logger.info("查询销售明细规格型号,saleorderGoodsId:{},mode:{},spec:{}",saleorderGoodsId,model,spec);
        SaleOrderGoodsDetailDto goodsDto = saleOrderGoodsApiService.getBySaleOrderGoodsId(saleorderGoodsId);
        List<Integer> typeList = Arrays.asList(316,317,318,1008);
        if ((Integer.valueOf(316).equals(goodsDto.getSpuType()) || Integer.valueOf(1008).equals(goodsDto.getSpuType()))
                || (!typeList.contains(goodsDto.getSpuType()) && StrUtil.isNotBlank(goodsDto.getModel()))){
            return goodsDto.getModel();
        }else {
            return goodsDto.getSpec();
        }
    }


    /**
     * 每次发货时分别寄送
     * （1）在WMS中复核完成时，生成开票申请
     * （2）开票申请中的商品为：本次复核任务中该订单的商品
     *
     * @param requestBean
     * @param saleorder
     */
    private void dealInvoiceWithEveryTime(OutputDto requestBean, Saleorder saleorder,  Saleorder saleorderBaseInfo) {
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleorder.getSaleorderId());

        if (CollectionUtils.isEmpty(saleorderGoods)){
            logger.error("订单中的销售订单详情 error saleOrderId:{}" , saleorder.getSaleorderId());
            throw new RuntimeException("订单中的销售订单详情 error saleOrderId:{}" + saleorder.getSaleorderId());
        }

        //发票中商品详情信息
        HashMap<Integer, InvoiceApplyDetail> saleOrderGoodsInvoiceApplyDetailMap = new HashMap<>(16);
        requestBean.getDetails().forEach(outputGoodDto -> {
            saleorderGoods.forEach(saleorderGoodsInfo -> {
                if (saleorderGoodsInfo.getSaleorderGoodsId().equals(Integer.parseInt(outputGoodDto.getUserDefine1()))){
                    if (saleOrderGoodsInvoiceApplyDetailMap.containsKey(saleorderGoodsInfo.getSaleorderGoodsId())){
                        InvoiceApplyDetail invoiceApplyDetailInfo = saleOrderGoodsInvoiceApplyDetailMap.get(saleorderGoodsInfo.getSaleorderGoodsId());
                        invoiceApplyDetailInfo.setNum(invoiceApplyDetailInfo.getNum().add(outputGoodDto.getQtyShipped()));
                    } else {
                        InvoiceApplyDetail invoiceApplyDetail = new InvoiceApplyDetail();
                        invoiceApplyDetail.setSku(outputGoodDto.getSKU());
                        invoiceApplyDetail.setNum(outputGoodDto.getQtyShipped());
                        invoiceApplyDetail.setDetailgoodsId(saleorderGoodsInfo.getSaleorderGoodsId());
                        invoiceApplyDetail.setProductName(saleorderGoodsInfo.getGoodsName());
                        invoiceApplyDetail.setSpecModel(getSpecModel(saleorderGoodsInfo.getSaleorderGoodsId(),saleorderGoodsInfo.getModel(),saleorderGoodsInfo.getSpec()));
                        invoiceApplyDetail.setUnit(saleorderGoodsInfo.getUnitName());
                        invoiceApplyDetail.setTaxRate(new BigDecimal(sysOptionDefinitionMapper.selectByPrimaryKey(saleorderBaseInfo.getInvoiceType()).getComments()));
                        saleOrderGoodsInvoiceApplyDetailMap.put(saleorderGoodsInfo.getSaleorderGoodsId(),invoiceApplyDetail);
                    }

                }
            });
        });

        //封装开票申请信息
        ArrayList<InvoiceApplyDetail> invoiceApplyDetails = new ArrayList<>();
        for (Map.Entry<Integer, InvoiceApplyDetail> entry : saleOrderGoodsInvoiceApplyDetailMap.entrySet()) {
            invoiceApplyDetails.add(entry.getValue());
        }
        logger.info("票货同形生成invoiceApplyDetail,{}", JSONUtil.toJsonStr(invoiceApplyDetails));
        setInvoiceApplyInfoAndOpenInvoice(requestBean, saleorder, invoiceApplyDetails);
    }

    /**
     * 设置开票申请信息并进行开票
     *
     * @param requestBean
     * @param saleorder
     * @param invoiceApplyDetails
     */
    private void setInvoiceApplyInfoAndOpenInvoice(OutputDto requestBean, Saleorder saleorder, ArrayList<InvoiceApplyDetail> invoiceApplyDetails) {
        InvoiceApply invoiceApply = new InvoiceApply();
        invoiceApply.setRelatedId(saleorder.getSaleorderId());
        invoiceApply.setCompanyId(1);
        //处理invoiceApply isAuto、invoiceProperty字段
        //dealIsAutoAndInvoiceProperty(saleorder.getInvoiceMethod(),invoiceApply);
        invoiceApply.setIsAuto(ErpConst.FOUR);
        invoiceApply.setInvoiceProperty(ErpConst.THREE);
        invoiceApply.setCreator(1);
        invoiceApply.setUpdater(1);
        invoiceApply.setAdvanceValidStatus(0);
        invoiceApply.setAddTime(System.currentTimeMillis());
        invoiceApply.setModTime(DateUtil.gainNowDate());
        //是否提前开票
        invoiceApply.setIsAdvance(0);
        invoiceApply.setYyValidStatus(1);
        invoiceApply.setType(SysOptionConstant.ID_505);
        invoiceApply.setApplyMethod(InvoiceApplyMethodEnum.TICKET_AND_FREIGH.getApplyMethodCode());
        invoiceApply.setInvoiceApplyDetails(invoiceApplyDetails);
        invoiceApply.setErpOrderNo(WmsCommonUtil.getOriginalOrderNo(requestBean.getSOReference1()));
        invoiceApply.setWmsOrderNo(requestBean.getOrderNo());

        if (CollectionUtils.isEmpty(invoiceApplyDetails)) {
            logger.error("开票申请信息不全 invoiceApply：{}" + invoiceApply.toString());
            throw new RuntimeException("开票申请信息不全 invoiceApply：{}" + invoiceApply.toString());
        }
        logger.info("票货同行开始保存开票申请信息 invoiceApply:{}" , JSON.toJSONString(invoiceApply));
        invoiceApply = invoiceService.updateInvoiceApplyInfo(invoiceApply);
        logger.info("----开票申请信息 invoiceApply:{}" , JSON.toJSONString(invoiceApply));

        if (null == invoiceApply) {
            logger.error("订单开票申请总金额超过订单总金额 orderId:{}" , saleorder.getSaleorderId());
            return;
        }

        logger.info("开始保存开票申请信息并开票 invoiceApply:{}" , JSON.toJSONString(invoiceApply));
        final  InvoiceApply orderInvoiceApply = invoiceApply;
        GlobalThreadPool.submitMessage(new Runnable() {
            @Override
            public void run() {
                String uuid = UUID.randomUUID().toString().replace("-", "");
                logger.info("开始执行saveInvoiceApplyAndOpenInvoice,uuid:{}",uuid);
                saveInvoiceApplyAndOpenInvoice(requestBean, orderInvoiceApply);
                logger.info("结束执行saveInvoiceApplyAndOpenInvoice,uuid:{}",uuid);
            }
        });
    }

    public void dealIsAutoAndInvoiceProperty(Integer invoiceMethod, InvoiceApply invoiceApply) {
        //开票类型自动
        if (Integer.valueOf(3).equals(invoiceMethod)) {
            invoiceApply.setInvoiceProperty(ErpConst.TWO);
        }else if (Integer.valueOf(4).equals(invoiceMethod)) {
            // 数电
            invoiceApply.setInvoiceProperty(ErpConst.THREE);
        }else {
            //纸质
            invoiceApply.setInvoiceProperty(ErpConst.ONE);
        }
        invoiceApply.setIsAuto(invoiceMethod);
    }

    private void wxErrorNotice(String msg) {
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(msg);
        wxRobotService.send(invoiceNoticeRobotNum, wxMsgDto);
    }

    /**
     * 保存发票申请单并开票
     *
     * @param requestBean
     * @param invoiceApply
     */
    private void saveInvoiceApplyAndOpenInvoice(OutputDto requestBean, InvoiceApply invoiceApply) {
        ResultInfo<?> resultInfo = invoiceService.saveOpenInvoceApply(invoiceApply);
        invoiceApply.setInvoiceApplyId((Integer) resultInfo.getData());
        logger.info("开票申请结果,票货同行开始开票 resultInfo：{} applyId:{}", JSON.toJSONString(resultInfo), invoiceApply.getInvoiceApplyId());

        // 开票申请生成失败结果信息下传
        if (ResultInfo.error().getCode().equals(resultInfo.getCode())){
            logger.warn("开票申请生成失败结果信息下传 resultInfo:{}", JSON.toJSONString(resultInfo));
            pushInvoiceInfo(requestBean, invoiceApply, WmsInvoiceFlagEnum.FAILED_AUDIT.getCode(), resultInfo.getMessage());
            wxErrorNotice("wms开票申请失败" + resultInfo.getMessage());
            return;
        }

        if (Integer.valueOf(3).equals(invoiceApply.getIsAuto())) {
            ResultInfo<?> openEInvoicePush = invoiceService.openEInvoicePush(invoiceApply);
            //开票失败时的结果下传
            if (openEInvoicePush != null && openEInvoicePush.getCode().equals(WmsInvoiceFlagEnum.FAILED_AUDIT.getStatus())) {
                String jsonResult= JSON.toJSONString(openEInvoicePush);
                logger.warn("发票开具失败 invoiceApplyId:{},applyid={}", jsonResult,invoiceApply.getInvoiceApplyId());
                pushInvoiceInfo(requestBean, invoiceApply, WmsInvoiceFlagEnum.FAILED_AUDIT.getCode(), openEInvoicePush.getMessage());
            } else {
                pushInvoiceInfo(requestBean, invoiceApply,WmsInvoiceFlagEnum.OPENING_INVOICE.getCode(), WmsInvoiceFlagEnum.OPENING_INVOICE.getStatusStr());
            }
        }
        // 数电
        if (Integer.valueOf(4).equals(invoiceApply.getIsAuto())) {

            InvoiceApplyDto data = invoiceApplyApiService.getInvoiceApply(invoiceApply.getInvoiceApplyId());
            OpenInvoiceResultDto openInvoiceResultDto = fullyDigitalInvoiceApiService.openSaleInvoice(data, SalesOpenInvoiceTypeEnum.SALE_OPEN_INVOICE);
            logger.info("调用数电发票接口：{}",JSON.toJSONString(openInvoiceResultDto));
            if (openInvoiceResultDto.isSuccess()) {
                pushInvoiceInfo(requestBean, invoiceApply, WmsInvoiceFlagEnum.OPENING_INVOICE.getCode(), WmsInvoiceFlagEnum.OPENING_INVOICE.getStatusStr());
            } else {
                logger.warn("发票开具失败 invoiceApply:{},{}", JSON.toJSONString(data), JSON.toJSONString(openInvoiceResultDto));
                pushInvoiceInfo(requestBean, invoiceApply, WmsInvoiceFlagEnum.FAILED_AUDIT.getCode(), "接口异常");
            }


        }

    }

    /**
     * INVO ERP推送是否开发票給WMS
     *
     * @param requestBean
     * @param invoiceApply
     * @param invoiceFlag
     * @param userDefine2
     */
    private void pushInvoiceInfo(OutputDto requestBean, InvoiceApply invoiceApply, String invoiceFlag, String userDefine2) {
        WmsInvoiceDto wmsInvoiceDto = null;
        try {
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_INVOICE_DATA);
            wmsInvoiceDto = new WmsInvoiceDto();
            wmsInvoiceDto.setSOReference1(requestBean.getSOReference1());
            wmsInvoiceDto.setOrderNo(requestBean.getOrderNo());
            wmsInvoiceDto.setInvoice_Flag(invoiceFlag);
            wmsInvoiceDto.setUserDefine1(invoiceApply != null && invoiceApply.getInvoiceApplyId() != null ?
                    invoiceApply.getInvoiceApplyId().toString() : "");
            wmsInvoiceDto.setUserDefine2(userDefine2);

            logger.info("下传开取发票结果信息 wmsInvoiceDto:{}" + JSON.toJSONString(wmsInvoiceDto));
            WmsResponse response = wmsInterface.request(wmsInvoiceDto);
            if (response != null && response.getReturnFlag().equals(0)){
                logger.error("ERP下传是否开发票失败 订单号:{}" + wmsInvoiceDto.getSOReference1() + ",失败原因:{}" + response.getReturnDesc());
                throw new Exception("ERP下传是否开发票失败 订单号:{}" + wmsInvoiceDto.getSOReference1() + ",失败原因:{}" + response.getReturnDesc());
            }
        } catch (Exception e) {
            logger.error("ERP下传是否开发票接口error wmsInvoiceDto:{}" + JSON.toJSONString(wmsInvoiceDto), e);
        }
    }

    private void dealOutorderAndInorderConcat(Saleorder saleorder, OutputDto requestBean) {
        try {
            Set<String> inOrderNoSet = requestBean.getDetails().stream().map(OutputGoodDto::getLotAtt10).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(inOrderNoSet)){
                return;
            }

            for (String inorderNO : inOrderNoSet) {
                if(StringUtil.isEmpty(inorderNO)){
                    continue;
                }
                String orderNo = WmsCommonUtil.getOriginalOrderNo(inorderNO);
                WmsOutInOrderConcat insertInfo = new WmsOutInOrderConcat();
                insertInfo.setOutOrderNo(saleorder.getSaleorderNo());
                insertInfo.setOutOrderId(saleorder.getSaleorderId());
                insertInfo.setOutOrderType(WmsOutInOrderConcatConstant.OUT_SALEORDER_TYPE);
                //VDERP-9480订单号变更start
                AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(orderNo);
                BuyorderVo buyorderVo = buyorderMapper.getBuyorderVoByBuyorderNo(orderNo);
                if(afterSales != null){
                    if(afterSales == null){continue;}
                    insertInfo.setInOrderId(afterSales.getAfterSalesId());
                    insertInfo.setInOrderNo(afterSales.getAfterSalesNo());
                    insertInfo.setInOrderType(WmsOutInOrderConcatConstant.IN_AFTERORDER_TYPE);
                }else if(buyorderVo != null){
                    if(buyorderVo == null){continue;}
                    insertInfo.setInOrderId(buyorderVo.getBuyorderId());
                    insertInfo.setInOrderNo(buyorderVo.getBuyorderNo());
                    insertInfo.setInOrderType(WmsOutInOrderConcatConstant.IN_BUYORDER_TYPE);
                }
                if(insertInfo.getInOrderId() == null || insertInfo.getInOrderNo() == null || insertInfo.getInOrderType() == null){
                    continue;
                }
                WmsOutInOrderConcat oldinfo = wmsOutInOrderConcatMapper.getOutInOrderConcatInfo(insertInfo);
                if(oldinfo == null){
                    wmsOutInOrderConcatMapper.insertSelective(insertInfo);
                }
            }
        } catch (Exception e) {
            logger.error("dealOutorderAndInorderConcat error:",e);
        }
    }
    private String getPrintOutOrderUrl(Saleorder saleorder, Express expressDto) {
        StringBuffer path = new StringBuffer(erpUrl)
                .append("/warehouse/warehousesout/printOutOrder.do?")
                .append("expressId=" + expressDto.getExpressId())
                .append("&orderId=" + saleorder.getSaleorderId())
                .append("&bussinessType=496&bussinessNo=" + saleorder.getSaleorderNo())
                .append("&type_f=");

        String type = warehouseStockService.getPrintOutType(saleorder);

        if (StringUtil.isBlank(type)) {
            return "";
        }
        StringBuffer result = path.append(type);
        path.append("&expressType=0");
        return result.toString();
    }

    private void putPrintOutOrder(OutputDto requestBean, String printOutOrderUrl) {
        if (StringUtil.isEmpty(printOutOrderUrl)) {
            return;
        }
        PutPathDto putPathDto = new PutPathDto();
        putPathDto.setSOReference1(requestBean.getSOReference1());
        putPathDto.setOrderNo(requestBean.getOrderNo());
        List<PutPathDetailDto> detailDtoList = new ArrayList<>();
        PutPathDetailDto putPathDetailDto = new PutPathDetailDto();
        putPathDetailDto.setOrderNo(requestBean.getOrderNo());
        putPathDetailDto.setSOReference1(requestBean.getSOReference1());
        putPathDetailDto.setPicture_type(PictureTypeEnum.GOODS_INVOICE.getCode());
        putPathDetailDto.setPicture_path(printOutOrderUrl);
        detailDtoList.add(putPathDetailDto);
        putPathDto.setDetails(detailDtoList);
        try {
            logger.info("随货同行单至WMS接口的请求: putPathDto:{}", JSON.toJSONString(putPathDto));
            WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PATH_DATA);
            WmsResponse response = wmsInterface.request(putPathDto);
            logger.info("随货同行单至WMS接口的返回: putPathDto:{},response:{}", JSON.toJSONString(putPathDto), JSON.toJSONString(response));
        } catch (Exception e) {
            logger.error("随货同行单至WMS error putPrintOutOrder:{}" + putPathDto.toString(), e);
        }
    }

    private void sendMail(String title,String message,String mailTo){
        try{
            String[] alarmMailReceiver  = {};
            if (StringUtil.isNotBlank(mailTo)){
                alarmMailReceiver = mailTo.split(",");
            }else{
                return;
            }
            simpleMailMessage.setTo(alarmMailReceiver) ;
            simpleMailMessage.setSubject(title);
            simpleMailMessage.setSentDate(new Date());
            simpleMailMessage.setText(message);
            javaMailSender.send(simpleMailMessage);
        }catch (Exception e){
            logger.error("MailUtil，发生异常：",e);
        }
    }
}
