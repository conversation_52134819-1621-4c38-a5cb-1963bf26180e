package com.vedeng.erp.aftersale.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/10/21 9:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ExpenseItemAndInvoiceDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 发票id
     */
    private Integer invoiceId;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 费用单商品id
     */
    private Integer buyorderExpenseItemId;

    /**
     * 收票数量
     */
    private BigDecimal invoiceNum;

    /**
     * sku
     */
    private String sku;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * goodsId
     */
    private Integer goodsId;

    /**
     * 品牌
     */
    private String brandName;

    /**
     * 型号
     */
    private String model;

    /**
     * 采购价
     */
    private BigDecimal price;

    /**
     * 数量
     */
    private Integer num;

    /**
     * 单位
     */
    private String unitName;

    /**
     * 本次退票数量
     */
    private BigDecimal returnNum;

    /**
     * 费用售后退票id
     */
    private Long expenseAfterSalesInvoiceId;
}