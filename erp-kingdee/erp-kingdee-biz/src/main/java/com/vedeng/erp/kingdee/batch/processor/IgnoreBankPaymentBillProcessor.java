package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.dto.BankBillIgnoreRecordDto;
import com.vedeng.erp.finance.service.BankBillIgnoreRecordApiService;
import com.vedeng.erp.kingdee.batch.dto.BatchBankBillDto;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayVedengBankDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillSrcEntryLinkDto;
import com.vedeng.erp.kingdee.enums.*;
import com.vedeng.erp.kingdee.repository.KingDeePayVedengBankRepository;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 银行忽略流水（付款单）
 */
@Service
@Slf4j
public class IgnoreBankPaymentBillProcessor implements ItemProcessor<BatchBankBillDto, KingDeePayBillDto> {

    @Resource
    private KingDeePayVedengBankRepository kingDeePayVedengBankRepository;

    @Autowired
    private BankBillIgnoreRecordApiService bankBillIgnoreRecordApiService;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    private static final String PUBLIC = "对公";

    /**
     * 交易类型
     */
    private static final String FQZOKJYLX = "支出";

    private static final BigDecimal ZERO_DOT = new BigDecimal("0.0");

    @Override
    public KingDeePayBillDto process(BatchBankBillDto batchBankBillDto) throws Exception {
        log.info("【金蝶付款单】金蝶处理银行忽略付款流水信息{},", JSON.toJSONString(batchBankBillDto));
        BankBillIgnoreRecordDto ignoreRecordDto = bankBillIgnoreRecordApiService.selectByBankBillId(batchBankBillDto.getBankBillId());
        if (Objects.isNull(ignoreRecordDto)){
            log.info("【金蝶付款单】bankBillId:{} 未查询到忽略项配置",batchBankBillDto.getBankBillId());
            return null;
        }
        // 往来单位类型
        String contactUnitType = ignoreRecordDto.getContactUnitType();
        // 往来单位编号
        String contactUnitNo = ignoreRecordDto.getContactUnitNo();
        // 交易主体
        String tradeSubject = ignoreRecordDto.getTradeSubject();
        KingDeePayBillDto kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill,contactUnitType);
        kingDeePayBillDto.setKingDeeBizEnums(KingDeeBizEnums.savePayBill);
        kingDeePayBillDto.setFId("0");
        kingDeePayBillDto.setFBillNo(KingDeeConstant.IGNORE + batchBankBillDto.getBankBillId());
        kingDeePayBillDto.setFDate(DateUtil.formatDateTime(batchBankBillDto.getRealTrandatetime()));//VDERP-16088 款对接金蝶业务日期规则调整-精确到时分秒
        // 往来单位类型
        kingDeePayBillDto.setFContactUnitType(contactUnitType);
        // 往来单位
        kingDeePayBillDto.setFContactUnit(contactUnitNo);
        // / 往来单位类型
        kingDeePayBillDto.setFRectUnitType(contactUnitType);
        // 往来单位
        kingDeePayBillDto.setFRectUnit(contactUnitNo);
        kingDeePayBillDto.setFRemark("");
        // 业务类型
        if (KingDeeFormConstant.BD_CUSTOMER.contains(contactUnitType)){
            // BD_CUSTOMER 收款单或者收款退款单 IgnoreBankReceiveBillProcessor中有推送
            return null;
        }
        String fBillTypeId = KingDeeFormConstant.BD_SUPPLIER.contains(contactUnitType)?
                KingDeePayBillTypeEnums.BUYORDER_PAY.getCode() : KingDeePayBillTypeEnums.OTHER_PAY.getCode();
        kingDeePayBillDto.setFBillTypeId(fBillTypeId);

        List<KingDeePayBillEntryDto> fPayBillEntry = getFPayBillEntry(batchBankBillDto,tradeSubject,kingDeePayBillDto.getFBillTypeId());
        if (fPayBillEntry == null){
            return null;
        }
        kingDeePayBillDto.setFPayBillEntry(fPayBillEntry);

        List<KingDeePayBillSrcEntryDto> kingDeePayBillSrcEntryDtoList = new ArrayList<>();
        KingDeePayBillSrcEntryDto kingDeePayBillSrcEntryDto = new KingDeePayBillSrcEntryDto();
        kingDeePayBillSrcEntryDtoList.add(kingDeePayBillSrcEntryDto);
        kingDeePayBillDto.setFPayBillSrcEntry(kingDeePayBillSrcEntryDtoList);

        List<KingDeePayBillSrcEntryLinkDto> kingDeePayBillSrcEntryLinkDtoList = new ArrayList<>();
        KingDeePayBillSrcEntryLinkDto kingDeePayBillSrcEntryLinkDto = new KingDeePayBillSrcEntryLinkDto();
        kingDeePayBillSrcEntryLinkDtoList.add(kingDeePayBillSrcEntryLinkDto);
        kingDeePayBillSrcEntryDto.setFpaybillsrcentryLink(kingDeePayBillSrcEntryLinkDtoList);

        kingDeePayBillDto.setFQzokJylx(FQZOKJYLX);
        kingDeePayBillDto.setFQzokJyzt(tradeSubject);
        kingDeePayBillDto.setFQzokCgddh("");
        kingDeePayBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());
        // 自动审核判断字段
        kingDeePayBillDto.setErpBankBillId(batchBankBillDto.getBankBillId());
        boolean isExist = kingDeeBaseApi.isExist(kingDeePayBillDto);
        if (isExist){
            log.info("【金蝶付款单】金蝶处理银行忽略付款流水信息{},已经推送过金蝶", JSON.toJSONString(kingDeePayBillDto));
            return null;
        }
        return kingDeePayBillDto;
    }

    private List<KingDeePayBillEntryDto> getFPayBillEntry(BatchBankBillDto batchBankBillDto,String tradeSubject,String fBillTypeId){
        List<KingDeePayBillEntryDto> fPayBillEntry = new ArrayList<>();
        KingDeePayBillEntryDto dto = new KingDeePayBillEntryDto();
        Integer bankTag = batchBankBillDto.getBankTag();
        // 结算方式
        String fSettletypeid =  KingDeePayBillSettleTypeEnum.TELEGRAPHIC_SETTLETYPE.getCode();
        if (KingDeeBankTagEnum.ALI_PAY.getCode().equals(bankTag)){
            fSettletypeid = KingDeePayBillSettleTypeEnum.ALIPAY_SETTLETYPE.getCode();
        }else if (KingDeeBankTagEnum.WE_CHAT_PAY.getCode().equals(bankTag)){
            fSettletypeid = KingDeePayBillSettleTypeEnum.WECHAT_SETTLETYPE.getCode();
        }
        if (ErpConstant.THREE.equals(batchBankBillDto.getSettlementMethod())) {
            fSettletypeid = KingDeePayBillSettleTypeEnum.BANK_BILL_SETTLETYPE_NEW.getCode();
        }
        dto.setFsettletypeid(fSettletypeid);
        // 用途
        String purPoseid = KingDeePayBillUseTypeEnums.OTHER_PAY.getCode();
        if (KingDeePayBillTypeEnums.BUYORDER_PAY.getCode().equals(fBillTypeId)){
            purPoseid = KingDeePayBillUseTypeEnums.BUY_PAY.getCode();
        }
        dto.setFpurposeid(purPoseid);
        if (Objects.isNull(batchBankBillDto.getAmt())){
            log.error("【金蝶付款单】查询到银行流水总额为空，{}",JSON.toJSONString(batchBankBillDto));
            return null;
        }
        dto.setFpaytotalamountfor(batchBankBillDto.getAmt().subtract(batchBankBillDto.getMatchedAmount()));
        dto.setFovershortagefor(ZERO_DOT);
        // 手续费
        dto.setFhandlingchargefor(ZERO_DOT);
        dto.setFcostid("");
        dto.setFexpensedeptidE("");
        KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(batchBankBillDto.getBankTag());
        if(kingDeePayVedengBankDto == null){
            log.error("【金蝶付款单】资金流水推送失败!流水未查询到我方银行信息{},",batchBankBillDto.getBankTag());
            return null;
        }
        dto.setFaccountid(kingDeePayVedengBankDto.getPayBankNo());
        dto.setFsettleno("");
        dto.setFcomment("");
        dto.setFpostdate(DateFormatUtils.format(batchBankBillDto.getTranDate(), "yyyy-MM-dd"));
        dto.setFRecType(PUBLIC.equals(tradeSubject) ? "0" : "1");
        dto.setFRuZhangType(PUBLIC.equals(tradeSubject) ? "1" : "0");
        dto.setFPayType("A");
        dto.setFOppositeBankAccount(batchBankBillDto.getAccno2());
        dto.setFOppositeCcountName(batchBankBillDto.getAccName1());
        dto.setFOppositeBankName(batchBankBillDto.getCadBankNm());
        dto.setFCnaps(""); //VDERP-15926不传联行号
        dto.setFQzokYsddh("");
        dto.setFQzokGsywdh("");
        dto.setFQzokYwlx(KingDeeBaseEnums.OTHER_PAY.getName());

        fPayBillEntry.add(dto);
        return fPayBillEntry;
    }

    /**
     * 根据流水类型查询银行信息
     * @param bankType
     * @return
     */
    private KingDeePayVedengBankDto queryBankInfo(Integer bankType){
        KingDeePayVedengBankDto kingDeePayVedengBankDto;
        switch (bankType){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.THREE);
                break;
            case 7:
                //民生银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SEVEN);
                break;
            default:
                log.info("银行流水类型未查对应银行编码");
                return null;
        }
        return kingDeePayVedengBankDto;
    }
}
