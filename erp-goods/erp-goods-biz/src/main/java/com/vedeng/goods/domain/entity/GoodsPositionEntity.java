package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 商品分档信息表
 */
@Getter
@Setter
public class GoodsPositionEntity extends BaseEntity {
    /**
    * ID
    */
    private Integer id;

    /**
    * 优先级，越大优先级越高
    */
    private Integer priority;

    /**
    * 商品档位名称
    */
    private String positionName;

    /**
    * 商品档位描述
    */
    private String description;

    /**
    * 档位次序（递减）
    */
    private Integer ordinal;

    /**
    * 签约模式
    */
    private Integer signContractMode;

    /**
    * 是否删除，0:否，1:是
    */
    private Boolean isDeleted;

    /**
    * 添加纪录人ID
    */
    private Integer creatorId;

    /**
    * 上次更新人ID
    */
    private Integer updaterId;
}