package com.vedeng.kpi.model.DTO.emptyEntity;

import com.vedeng.kpi.model.DTO.KpiDailyCountExtDto;

import java.math.BigDecimal;

public class NullKpiDailyCountExtDto extends KpiDailyCountExtDto {

    public NullKpiDailyCountExtDto(){
//        this.setUserId(-1);
        this.setSort(0);
        this.setKpiAmount(BigDecimal.ZERO);
        this.setKpiAmountProgress(BigDecimal.ZERO);
        this.setKpiAmountScore(BigDecimal.ZERO);
        this.setKpiAmountProgress(BigDecimal.ZERO);
        this.setBdNewCustomerNum(0);
        this.setBdLostCustomerNum(0);
        this.setBdCustomerScore(BigDecimal.ZERO);
        this.setCoCustomerNum(0);
        this.setLostCustomerNum(0);
        this.setCustomerScore(BigDecimal.ZERO);
        this.setChanceFailNum(0);
        this.setChanceSuccessNum(0);
        this.setChanceTransProportion(BigDecimal.ZERO);
        this.setChanceTransScore(BigDecimal.ZERO);
        this.setChanceRangeCustomerNum(0);
    }

    public NullKpiDailyCountExtDto(Integer usrId){
        this();
        this.setUserId(usrId);
    }

}
