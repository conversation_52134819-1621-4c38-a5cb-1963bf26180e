<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesInstallServiceRecordDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail" >
    <!--          -->
    <id column="AFTER_SALES_SERVICE_DETAIL_ID" property="afterSalesServiceDetailId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_SERVICE_ID" property="afterSalesServiceId" jdbcType="INTEGER" />
    <result column="SKU" property="sku" jdbcType="VARCHAR" />
    <result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
    <result column="BRAND" property="brand" jdbcType="VARCHAR" />
    <result column="MODEL" property="model" jdbcType="VARCHAR" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="TIMESTAMP" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="TIMESTAMP" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
    <result column="AFTER_SALES_GOODS_ID" property="afterSalesGoodsId" jdbcType="INTEGER" />
    <result column="SUPPL_CODE" property="supplCode" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    <!--          -->
    AFTER_SALES_SERVICE_DETAIL_ID, AFTER_SALES_SERVICE_ID, SKU, SKU_NAME, BRAND, MODEL,
    NUM, SERIAL_NUMBER, ADD_TIME, CREATOR, MOD_TIME, UPDATER, IS_DELETE, AFTER_SALES_GOODS_ID,SUPPL_CODE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    <!--          -->
    select
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    <!--          -->
    delete from T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail" >
    <!--          -->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL (AFTER_SALES_SERVICE_DETAIL_ID, AFTER_SALES_SERVICE_ID,
      SKU, SKU_NAME, BRAND,
      MODEL, NUM, SERIAL_NUMBER,
      ADD_TIME, CREATOR, MOD_TIME,
      UPDATER, IS_DELETE, AFTER_SALES_GOODS_ID
      )
    values (#{afterSalesServiceDetailId,jdbcType=INTEGER}, #{afterSalesServiceId,jdbcType=INTEGER},
      #{sku,jdbcType=VARCHAR}, #{skuName,jdbcType=VARCHAR}, #{brand,jdbcType=VARCHAR},
      #{model,jdbcType=VARCHAR}, #{num,jdbcType=INTEGER}, #{serialNumber,jdbcType=VARCHAR},
      #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{modTime,jdbcType=TIMESTAMP},
      #{updater,jdbcType=INTEGER}, #{isDelete,jdbcType=BIT}, #{afterSalesGoodsId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail" useGeneratedKeys="true" keyProperty="afterSalesServiceDetailId">
    <!--          -->
    insert into T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterSalesServiceDetailId != null" >
        AFTER_SALES_SERVICE_DETAIL_ID,
      </if>
      <if test="afterSalesServiceId != null" >
        AFTER_SALES_SERVICE_ID,
      </if>
      <if test="sku != null" >
        SKU,
      </if>
      <if test="skuName != null" >
        SKU_NAME,
      </if>
      <if test="brand != null" >
        BRAND,
      </if>
      <if test="model != null" >
        MODEL,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="serialNumber != null" >
        SERIAL_NUMBER,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
      <if test="afterSalesGoodsId != null" >
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="serialNumber != null" >
        SUPPL_CODE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterSalesServiceDetailId != null" >
        #{afterSalesServiceDetailId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesServiceId != null" >
        #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        #{skuName,jdbcType=VARCHAR},
      </if>
        <if test="brand != null">
          #{brand,jdbcType=VARCHAR},
        </if>
      <if test="model != null" >
        #{model,jdbcType=VARCHAR},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="serialNumber != null" >
        #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
      <if test="afterSalesGoodsId != null" >
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="supplCode != null" >
        #{supplCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail" >
    <!--          -->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    <set >
      <if test="afterSalesServiceId != null" >
        AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      </if>
      <if test="sku != null" >
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brand != null" >
        BRAND = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null" >
        MODEL = #{model,jdbcType=VARCHAR},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="serialNumber != null" >
        SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
      <if test="afterSalesGoodsId != null" >
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="supplCode != null" >
        SUPPL_CODE = #{supplCode,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail" >
    <!--          -->
    update T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    set AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      BRAND = #{brand,jdbcType=VARCHAR},
      MODEL = #{model,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=INTEGER},
      SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BIT},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
    where AFTER_SALES_SERVICE_DETAIL_ID = #{afterSalesServiceDetailId,jdbcType=INTEGER}
  </update>
    <update id="logicDelete" parameterType="java.lang.Integer" >
      UPDATE T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
      SET IS_DELETE = 1
      WHERE
          AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
    </update>
    <select id="queryInfoByServiceId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM
         T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    WHERE
        AFTER_SALES_SERVICE_ID = #{afterSalesServiceId,jdbcType=INTEGER}
        AND IS_DELETE = 0
  </select>
  <select id="queryInfoByAftersalesGoodsId" resultMap="BaseResultMap">
    SELECT
        <include refid="Base_Column_List"/>
    FROM
    T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    WHERE
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
        AND IS_DELETE = 0
  </select>

  <!--auto generated by MybatisCodeHelper on 2023-06-28-->
  <select id="selectSupplCode" resultType="java.lang.Integer">
    select count(AFTER_SALES_SERVICE_DETAIL_ID)
    from T_AFTER_SALES_INSTALL_SERVICE_RECORD_DETAIL
    where IS_DELETE = 0 and SUPPL_CODE = #{supplCode,jdbcType=VARCHAR}
  </select>
</mapper>
