package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchCoreSkuDto;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface BatchCoreSkuDtoMapper {
    int deleteByPrimaryKey(Integer skuId);

    int insert(BatchCoreSkuDto record);

    int insertOrUpdate(BatchCoreSkuDto record);

    int insertOrUpdateSelective(BatchCoreSkuDto record);

    int insertSelective(BatchCoreSkuDto record);

    BatchCoreSkuDto selectByPrimaryKey(Integer skuId);

    int updateByPrimaryKeySelective(BatchCoreSkuDto record);

    int updateByPrimaryKey(BatchCoreSkuDto record);

    int updateBatch(List<BatchCoreSkuDto> list);

    int updateBatchSelective(List<BatchCoreSkuDto> list);

    int batchInsert(@Param("list") List<BatchCoreSkuDto> list);

    List<Map<String, Object>> skuTipList(List<Integer> skuIdList);

    List<BatchCoreSkuDto> queryMaterialCompensate(BatchCoreSkuDto batchCoreSkuDto);

    String selectStandardCategoryByCategoryId(Integer categoryId);

    String selectByOldStandardCategory(Integer id);
}