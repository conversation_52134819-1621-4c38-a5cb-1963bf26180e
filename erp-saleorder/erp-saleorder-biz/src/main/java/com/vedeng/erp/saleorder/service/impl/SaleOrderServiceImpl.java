package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.erp.aftersale.domain.dto.SaleOrderGoodsAmountDto;
import com.vedeng.erp.aftersale.mapper.AfterSalesBizMapper;
import com.vedeng.erp.common.constants.SaleOrderConstants;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.InvoiceApiService;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.dao.SaleOrderGoodsDetailMapper;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.domain.entity.SaleorderEntity;
import com.vedeng.erp.saleorder.dto.*;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.erp.saleorder.service.NewSaleOrderService;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.order.model.Saleorder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: Putin
 * @CreateTime: 2023-02-03  17:34
 */
@Service
@Slf4j
public class SaleOrderServiceImpl implements SaleOrderApiService {

    @Autowired
    private SaleOrderGoodsDetailMapper saleOrderGoodsDetailMapper;

    @Autowired
    private SaleOrderMapper saleOrderMapper;

    @Autowired
    private SaleorderSyncService saleorderSyncService;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Autowired
    private NewSaleOrderService newSaleOrderService;

    @Autowired
    private AfterSalesBizMapper afterSalesBizMapper;

    @Autowired
    private InvoiceApiService invoiceApiService;


    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;


    @Override
    public boolean checkSignCompanyInfo(Integer saleOrderId) {
        SaleorderInfoDto saleorderInfoDto = getBySaleOrderId(saleOrderId);
        if (saleorderInfoDto == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return false;
        }

        BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(saleorderInfoDto.getTraderName());
        if (baseCompanyInfoDto == null) {
            return false;
        }
        return true;

    }



    @Override
    public BaseCompanyInfoDto checkSignCompanyInfoBySaleOrderId(Integer saleOrderId) {
        SaleorderInfoDto saleorderInfoDto = getBySaleOrderId(saleOrderId);
        if (saleorderInfoDto == null) {
            log.error("发起电子签单时，多主体，该订单未找到");
            return null;
        }
       return baseCompanyInfoApiService.selectBaseCompanyByCompanyName(saleorderInfoDto.getTraderName());
    }




    @Override
    public void checkSaleorderDeliveryAndArrivalStatus(Integer saleorderId) {
        log.info("checkSaleorderDeliveryAndArrivalStatus执行,saleorderId:{}", saleorderId);
        List<CoreSku> invisibleSkuList = coreSkuMapper.getInvisibleSkuList();
        List<SaleOrderGoodsDetailDto> goodsDetail = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(saleorderId);

        List<SaleOrderGoodsDetailDto> realSaleOrderGoodsListExceptAfterSales = getRealSaleOrderGoodsListExceptAfterSales(goodsDetail, saleorderId);
        log.info("checkSaleorderDeliveryAndArrivalStatus执行,saleorderId:{},realSaleOrderGoodsListExceptAfterSales:{}", saleorderId, JSON.toJSONString(realSaleOrderGoodsListExceptAfterSales));
        List<Integer> invisibleList = invisibleSkuList.stream().map(CoreSku::getSkuId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(goodsDetail)) {

            log.info("虚拟商品发货后校验销售单整体收发货状态，销售单：{}", goodsDetail.get(0).getSaleorderNo());
            int vArrivalStatus = goodsDetail.stream()
                    .filter(detailDto -> detailDto.getArrivalStatus() != null)
                    .filter(detailDto -> ((detailDto.getIsVirtureSku() != null && detailDto.getIsVirtureSku() == 1) || invisibleList.contains(detailDto.getGoodsId())))
                    .mapToInt(SaleOrderGoodsDetailDto::getArrivalStatus).sum();

            SaleorderInfoDto saleorderInfoDto = new SaleorderInfoDto();
            long time = System.currentTimeMillis();
            saleorderInfoDto.setModTime(time);
            saleorderInfoDto.setSaleorderId(saleorderId);

            int sumDeliveryStatusOfOrder = 0;
            int sumArrivalStatusOfOrder = 0;
            int deliveryStatusOfOrder = 0;
            int arrivalStatusOfOrder = 0;

            // 未发货的全退
            long deliveryReturnNum = realSaleOrderGoodsListExceptAfterSales.stream().filter(e -> e.getNum() == 0 && e.getDeliveryStatus() == 0).collect(Collectors.toList()).stream().count();
            long arrivalReturnNum = realSaleOrderGoodsListExceptAfterSales.stream().filter(e -> e.getNum() == 0 && e.getArrivalStatus() == 0).collect(Collectors.toList()).stream().count();

            for (SaleOrderGoodsDetailDto saleorderGood : realSaleOrderGoodsListExceptAfterSales) {
                sumDeliveryStatusOfOrder += saleorderGood.getNum() == 0 ? (saleorderGood.getDeliveryStatus() == 0 ? 0 : 2) : saleorderGood.getDeliveryStatus();
                sumArrivalStatusOfOrder += saleorderGood.getNum() == 0 ? (saleorderGood.getArrivalStatus() == 0 ? 0 : 2) : saleorderGood.getArrivalStatus();
            }

            if (sumDeliveryStatusOfOrder > 0) {
                deliveryStatusOfOrder = sumDeliveryStatusOfOrder < 2 * (realSaleOrderGoodsListExceptAfterSales.size() - deliveryReturnNum) ? 1 : 2;
            }

            if (sumArrivalStatusOfOrder > 0) {
                arrivalStatusOfOrder = sumArrivalStatusOfOrder < 2 * (realSaleOrderGoodsListExceptAfterSales.size() - arrivalReturnNum) ? 1 : 2;
            }

            saleorderInfoDto.setDeliveryStatus(deliveryStatusOfOrder);
            saleorderInfoDto.setArrivalStatus(arrivalStatusOfOrder);

            saleorderInfoDto.setDeliveryTime(time);
            saleorderInfoDto.setArrivalTime(time);

            log.info("开始更新销售单：{}的收发货状态:{}", goodsDetail.get(0).getSaleorderNo(), JSON.toJSONString(saleorderInfoDto));
            saleOrderMapper.updateSaleorderStatus(saleorderInfoDto);

            if (vArrivalStatus == goodsDetail.size() * 2) {
                log.info("纯虚拟订单全部收货推送完结状态至前台，订单id：{},订单号：{}", saleorderId, goodsDetail.get(0).getSaleorderNo());
                SaleorderInfoDto saleOrderConfirmation = new SaleorderInfoDto();
                saleOrderConfirmation.setSaleorderId(saleorderId);
                saleOrderConfirmation.setConfirmationFormAudit(2);
                log.info("纯虚拟订单全部收货,更新订单的确认单审核状态为审核通过,销售单号:{},订单信息:{}", goodsDetail.get(0).getSaleorderNo(), JSON.toJSONString(saleOrderConfirmation));
                saleOrderMapper.updateConfirmationFormAudit(saleOrderConfirmation);
                saleorderSyncService.syncSaleorderStatus2Mjx(saleorderId, PCOrderStatusEnum.FINISH, SaleorderSyncEnum.PUSH_VS);
            }
        }

    }

    @Override
    public Map<String, String> getSaleOrderGoodsPrice(Integer saleorderId, String skuNo, BigDecimal price) {
        return newSaleOrderService.getSaleOrderGoodsPrice(saleorderId, skuNo, price);
    }

    private List<SaleOrderGoodsDetailDto> getRealSaleOrderGoodsListExceptAfterSales(List<SaleOrderGoodsDetailDto> goodsDetail, Integer saleorderId) {
        //售后退货完结的商品
        Map<Integer, Integer> afterSalesGoodsMap = afterSalesGoodsMapper.getAfterSalesGoodsCountOfSaleOrderRefund(saleorderId)
                .stream()
                .collect(Collectors.toMap(AfterSalesGoods::getOrderDetailId, AfterSalesGoods::getNum));

        return goodsDetail.stream()
                .filter(saleOrderGoods -> saleOrderGoods.getSaleorderGoodsId() != null)
                .map(saleOrderGoods -> {
                    SaleOrderGoodsDetailDto dto = new SaleOrderGoodsDetailDto();
                    dto.setNum(saleOrderGoods.getNum());
                    dto.setDeliveryStatus(saleOrderGoods.getDeliveryStatus());
                    dto.setArrivalStatus(saleOrderGoods.getArrivalStatus());

                    if (afterSalesGoodsMap.containsKey(saleOrderGoods.getSaleorderGoodsId())) {
                        int num = saleOrderGoods.getNum() - afterSalesGoodsMap.get(saleOrderGoods.getSaleorderGoodsId());
                        dto.setNum(num);
                    }
                    return dto;
                }).collect(Collectors.toList());
    }



    @Override
    public boolean checkByTraderNameHaveSaleOrder(String traderName) {
        if (StrUtil.isEmpty(traderName)) {
            return false;
        }
        SaleorderInfoDto saleorderInfoDto = saleOrderMapper.selectByTraderName(traderName);
        return Objects.nonNull(saleorderInfoDto);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void updateSaleOrderInvoiceStatus(Integer saleOrderId) {
        log.info("更新销售订单的发票状态,销售id{}", saleOrderId);

        // 获取订单售后信息
        List<SaleOrderGoodsAmountDto> saleOrderGoodsAmountDtoList = afterSalesBizMapper
                .getSaleOrderAmountDto(saleOrderId);
        if (CollUtil.isEmpty(saleOrderGoodsAmountDtoList)) {
            return;
        }

        Integer orderType =  CollUtil.getFirst(saleOrderGoodsAmountDtoList).getOrderType();
        // 实付金额
        BigDecimal realAmount;
        // 耗材订单
        if (Lists.newArrayList(SalesOrderTypeEnum.BD.getCode(), SalesOrderTypeEnum.HC.getCode()).contains(orderType)) {
            //总金额
            BigDecimal totalAmount = saleOrderGoodsAmountDtoList.stream().map(SaleOrderGoodsAmountDto::getMaxSkuRefundAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //总售后退款金额
            BigDecimal refundAmount = saleOrderGoodsAmountDtoList.stream().map(SaleOrderGoodsAmountDto::getRefundAmount)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            realAmount = totalAmount.subtract(refundAmount);
        } else {
            //销售总金额
            BigDecimal totalAmount = saleOrderGoodsAmountDtoList.stream().map(s -> s.getSaleOrderGoodsNum().multiply(s.getPrice()))
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //总售后退款金额
            BigDecimal refundAmount = saleOrderGoodsAmountDtoList.stream().map(s -> s.getNum().multiply(s.getPrice()))
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            realAmount = totalAmount.subtract(refundAmount);
        }
        // 查询已开票金额
        List<InvoiceDto> saleOrderInvoice = invoiceApiService.getSaleOrderInvoice(saleOrderId);
        BigDecimal saleOrderInvoiceAmount = saleOrderInvoice.stream().map(InvoiceDto::getAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        // 如果订单实付金额大于等于已开票金额，订单开票状态设置为1，否则设置为2
        SaleorderEntity entity = new SaleorderEntity();
        entity.setSaleorderId(saleOrderId);
        Integer invoiceStatus = realAmount.compareTo(saleOrderInvoiceAmount) > 0 ? SaleOrderConstants.INVOICE_STATUS_PART : SaleOrderConstants.INVOICE_STATUS_ALL;
        entity.setInvoiceStatus(invoiceStatus);
        saleOrderMapper.updateByPrimaryKeySelective(entity);
    }

    @Override
    public String getSaleOrderInvoiceTraderContactMobile(Integer saleOrderId) {
        return saleOrderMapper.findInvoiceTraderContactMobileBySaleorderId(saleOrderId);
    }

    @Override
    public String getSaleOrderInvoiceTraderContactName(Integer saleOrderId) {
        return saleOrderMapper.getSaleOrderInvoiceTraderContactName(saleOrderId);
    }

    /**
     * 根据售后单id查询关联的销售单信息
     *
     * @param afterSalesId 售后单id
     * @return SaleorderInfoDto
     */
    @Override
    public SaleorderInfoDto getByAfterSaleId(Integer afterSalesId) {
        SaleorderEntity saleOrderEntity = saleOrderMapper.getByAfterSaleId(afterSalesId);
        SaleorderInfoDto result = new SaleorderInfoDto();
        BeanUtil.copyProperties(saleOrderEntity, result);
        return result;
    }

    @Override
    public SaleorderInfoDto getBySaleOrderId(Integer saleOrderId) {
        SaleorderEntity saleOrderEntity = saleOrderMapper.findBySaleorderId(saleOrderId);
        SaleorderInfoDto result = new SaleorderInfoDto();
        BeanUtil.copyProperties(saleOrderEntity, result);
        return result;
    }

    @Override
    public List<SaleorderInfoDto> getSaleorderInfoBySaleorderIds(List<Integer> saleorderIds) {
        List<SaleorderEntity> saleOrderEntityList = saleOrderMapper.getSaleorderInfoBySaleorderIds(saleorderIds);
        if(CollUtil.isEmpty(saleOrderEntityList)){
            return Collections.emptyList();
        }
        return saleOrderEntityList.stream().map(saleOrderEntity -> {
            SaleorderInfoDto result = new SaleorderInfoDto();
            BeanUtil.copyProperties(saleOrderEntity, result);
            return result;
        }).collect(Collectors.toList());
    }

    @Override
    public OrderFinanceInfoDto getSaleorderFinanceInfo(Integer saleorderId) {
        return saleOrderMapper.getSaleorderFinanceInfo(saleorderId);
    }

    @Override
    public List<Integer> checkSettlement(CheckSettlementDto param) {

        log.info("checkSettlement:入参：{}", JSON.toJSONString(param));

        //1 订单已生效：生效状态 = 已生效
        //2 进行中：订单单据状态 = 进行中
        //3 未收款：收款状态 = 未收款
        //4 未锁定：订单没有锁定
        //5 非账期订单：T_SALEORDER.HAVE_ACCOUNT_PERIOD = 0
        //6 名称相同： T_SALEORDER.TRADER_NAME
        //7 金额相同： T_SALEORDER.REAL_TOTAL_AMOUNT
        //8 交易时间5天内：绝对值（T_BANK_BILL.REAL_TRANDATETIME- T_SALEORDER.VALID_TIME）<=5 /*以交易时间为起点，前后5天*/

        SaleOrderQueryDto saleOrderQueryDto = new SaleOrderQueryDto();
        saleOrderQueryDto.setValidStatus(1);
        saleOrderQueryDto.setStatus(1);
        saleOrderQueryDto.setPaymentStatus(0);
        saleOrderQueryDto.setLockedStatus(0);
        saleOrderQueryDto.setHaveAccountPeriod(0);

        saleOrderQueryDto.setTraderName(param.getTraderName());
        saleOrderQueryDto.setValidTimeBegin(param.getBeginTime());
        saleOrderQueryDto.setValidTimeEnd(param.getEndTime());
        saleOrderQueryDto.setRealTotalAmount(param.getAmount());

        List<Integer> integers = saleOrderMapper.querySaleOrderIdBySettlement(saleOrderQueryDto);
        log.info("checkSettlement:返回：{}", JSON.toJSONString(integers));

        return integers;
    }


    @Override
    public Integer getContractVerifyStatusBySaleOrderId(Integer saleorderId) {
        return saleOrderMapper.getContractVerifyStatusBySaleOrderId(saleorderId);
    }

    @Override
    public PageInfo<SaleorderInfoDto> getSaleorderCanInvoiceListPage(Page page, String param) {
        PageHelper.startPage(page.getPageNum(), page.getPageSize());
        List<SaleorderInfoDto> saleorderInfoDtoList = saleOrderMapper.getSaleorderCanInvoiceListPage(param);
        return new PageInfo<>(saleorderInfoDtoList);
    }

	@Override
	public SaleorderInfoDto getBySaleOrderNo(String orderNo) {
        SaleorderInfoDto saleorderInfoDto = saleOrderMapper.getByAfterSaleNo(orderNo);
        if(saleorderInfoDto == null){
            return null;
        }
        List<SaleOrderGoodsDetailDto> goodsDetailByOrderId = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(saleorderInfoDto.getSaleorderId());
        saleorderInfoDto.setSaleOrderGoodsDetailDtoList(goodsDetailByOrderId);
        return saleorderInfoDto;
	}

    @Override
    public List<Map<String, Object>> getCommunicateChanceInfo(List<Integer> saleOrderIdList) {
        return saleOrderMapper.getCommunicateSaleOrderInfo(saleOrderIdList);
    }


    @Override
    public boolean periodOrderCanDeliver(Integer saleOrderId) {
        return saleOrderMapper.getSaleorderContractStatus(saleOrderId);
    }


    @Override
    public SaleorderInfoForStandardDto getBySaleOrderNoForStandard(String orderNo){
        SaleorderInfoDto saleorderInfoDto = getBySaleOrderNo(orderNo);
        return getSaleorderInfoForStandardDto(saleorderInfoDto);
    }


    @Override
    public SaleorderInfoForStandardDto getBySaleOrderIdForStandard(Integer saleorderId){
        SaleorderInfoDto saleorderInfoDto = getBySaleOrderId(saleorderId);
        return getSaleorderInfoForStandardDto(saleorderInfoDto);
    }

    private SaleorderInfoForStandardDto getSaleorderInfoForStandardDto(SaleorderInfoDto saleorderInfoDto) {
        if(saleorderInfoDto != null){
            SaleorderInfoForStandardDto result = new SaleorderInfoForStandardDto();
            BeanUtil.copyProperties(saleorderInfoDto, result);

            List<SaleOrderInvoiceDto> saleOrderInvoiceDtoList = saleOrderMapper.findSaleOrderInvoiceListBySaleorderId(result.getSaleorderId());
            result.setSaleOrderInvoiceDtoList(saleOrderInvoiceDtoList);

            List<SaleOrderGoodsDetailDto>  saleOrderGoodsDetailDtoList = saleOrderGoodsDetailMapper.findGoodsDetailByOrderId(result.getSaleorderId());
            result.setSaleOrderGoodsDetailDtoList(saleOrderGoodsDetailDtoList);
            return result;
        }
        return null;
    }


}
