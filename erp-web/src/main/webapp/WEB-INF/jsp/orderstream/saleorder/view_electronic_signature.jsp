<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<div class="parts">
    <table class="table">
        <c:choose>
            <c:when test="${not empty electronicSignatureList }">
                <thead>
                <tr>
                    <th>电子签章链接</th>
                    <th>客户经办人</th>
                    <th>链接生成时间</th>
                    <th>操作人</th>
                    <th>操作</th>
                </tr>
                </thead>
                <tbody>
                <c:forEach items="${electronicSignatureList}" var="esList" varStatus="userStatus">
                    <tr>
                        <td>
                            <input id="es_${userStatus.index}" value="${esList.signUrl}" style="background-color: inherit;border: none;" readonly>
                        </td>
                        <td>
                                ${esList.realName}&nbsp;${esList.phoneNumber}
                        </td>
                        <td>
                                <date:date value="${esList.addTime} " />
                        </td>
                        <td>
                                ${esList.operator}
                        </td>
                        <td>
                            <c:if test="${userStatus.last}">
                                <button onclick="copyUrl(${userStatus.index})" style="color:#3384ef;background-color: inherit;">复制链接</button>
                            </c:if>
                        </td>
                    </tr>
                </c:forEach>
                    <tr>

                    </tr>
                </tbody>
            </c:when>
            <c:otherwise>
                <!-- 查询无结果弹出 -->
            <tbody>
                <tr>
                </tr>
                <tr>
                    <td colspan='5'>暂无记录！</td>
                </tr>
            </tbody>
            </c:otherwise>
        </c:choose>
    </table>
</div>