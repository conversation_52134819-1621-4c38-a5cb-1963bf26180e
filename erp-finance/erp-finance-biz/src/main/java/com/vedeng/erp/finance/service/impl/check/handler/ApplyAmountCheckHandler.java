package com.vedeng.erp.finance.service.impl.check.handler;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.aftersale.dto.AfterSalesDetailApiDto;
import com.vedeng.erp.aftersale.service.AfterSalesApiService;
import com.vedeng.erp.buyorder.dto.BuyOrderRebateDto;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * 申请金额校验
 */
@Service
@Slf4j
public class ApplyAmountCheckHandler extends AbstractCheckHandler {

    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private AfterSalesApiService afterSalesApiService;

    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("申请金额校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        List<InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto> detailList = invoiceCheckRequestDto.getDetailList();
        if (CollUtil.isEmpty(detailList)) {
            throw new ServiceException("申请金额校验-销售,明细为空");
        }
        BigDecimal applyTotalAmount = getDetailTotalAmount(invoiceCheckRequestDto);
        SaleorderInfoDto saleOrder = saleOrderApiService.getBySaleOrderId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(saleOrder)) {
            throw new ServiceException("申请金额校验-销售,订单不存在");
        }
        if (applyTotalAmount.compareTo(saleOrder.getRealTotalAmount()) > 0) {
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            buildResult(invoiceCheckResultDto, checkHandlerEnum.getPromptText());
        }
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("申请金额校验-售后,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        List<InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto> detailList = invoiceCheckRequestDto.getDetailList();
        if (CollUtil.isEmpty(detailList)) {
            throw new ServiceException("申请金额校验-售后,明细为空");
        }
        BigDecimal applyTotalAmount = getDetailTotalAmount(invoiceCheckRequestDto);
        AfterSalesDetailApiDto afterSalesDetailApiDto = afterSalesApiService.getAfterSalesDetailByAfterSalesId(invoiceCheckRequestDto.getRelatedId());
        if (Objects.isNull(afterSalesDetailApiDto)) {
            throw new ServiceException("申请金额校验-售后,售后单不存在");
        }
        if (applyTotalAmount.compareTo(afterSalesDetailApiDto.getServiceAmount()) > 0) {
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            buildResult(invoiceCheckResultDto, checkHandlerEnum.getPromptText());
        }
    }
}
