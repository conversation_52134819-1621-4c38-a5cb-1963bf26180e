<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="修改报价" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>/static/js/order/quote/edit_quote_detail.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/region/index.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript">
	$(function(){
		var	url = page_url + '/order/quote/getQuoteDetail.do?quoteorderId='+$("#quoteorderId").val()+'&quoteSource='+$("#quoteSource").val();
		if($(window.frameElement).attr('src').indexOf("quoteorderId")<0){
			$(window.frameElement).attr('data-url',url);
		}
	});

	// 弹窗关闭后的回调函数，处理相关页面信息
	function receiveTerminal(terminal) {
		// 销售区域省市区 回显
		initRegProCityArea(terminal.address);
		$('input[name="terminalName"]').val(terminal.terminalName);
		$('input[name="dwhTerminalId"]').val(terminal.dwhTerminalId);
		$('input[name="unifiedSocialCreditIdentifier"]').val(terminal.unifiedSocialCreditIdentifier);
        $('input[name="organizationCode"]').val(terminal.organizationCode);

		$("#terminalTraderNameDiv").html(terminal.terminalName);
		$("#terminalNameDetail").css("display", "");
		$("#terminalNameCheck").css("display", "none");
		$("#regionMust").css("display", "");


		$('input[name="provinceId"]').val($('select[name="provinceId"]').val());
		$('input[name="cityId"]').val($('select[name="cityId"]').val());
		$('input[name="areaId"]').val($('select[name="areaId"]').val());
		var provinceName = $('#orderTerminal-province').find('option:selected').text();
		var cityName = $('#orderTerminal-city').find('option:selected').text();
		var areaName = $('#orderTerminal-area').find('option:selected').text();
		$('input[name="provinceName"]').val(provinceName == "请选择" ? "" : provinceName);
		$('input[name="cityName"]').val(cityName == "请选择" ? "" : cityName);
		$('input[name="areaName"]').val(areaName == "请选择" ? "" : areaName);

	}

	// 根据省市区的地址字符串解析出对应的regionId
	function initRegProCityArea(regAddress) {
		var regLocation = "";//获取页面是否有经过天眼查带过来的注册地址
		if (regAddress.length > 0) {
			regLocation = regAddress;
			//第一步，先判断地址中，是否有省份
			var provinceNameDefault = "";
			var cityNameDefault = "";
			var areaNameDefault = "";
			if(regLocation.startsWith("北京")){  //先判断是否是四个直辖市
				provinceNameDefault = "北京市";
				cityNameDefault = "北京市";
				regLocation = regLocation.replaceAll("北京市","");
			} else if(regLocation.startsWith("天津")){
				provinceNameDefault = "天津市";
				cityNameDefault = "天津市";
				regLocation = regLocation.replaceAll("天津市","");
			}else if(regLocation.startsWith("上海")){
				provinceNameDefault = "上海市";
				cityNameDefault = "上海市";
				regLocation = regLocation.replaceAll("上海市","");
			}else if(regLocation.startsWith("重庆")){
				provinceNameDefault = "重庆市";
				cityNameDefault = "重庆市";
				regLocation = regLocation.replaceAll("重庆市","");
			}else{
				$("select[name=\"provinceId\"] option").each(function() {
					var optionText = $(this).text();
					var optionSuf = optionText.replaceAll("省","").replaceAll("自治区","");
					if(regLocation.startsWith(optionSuf)){
						provinceNameDefault = optionText;
						if(regLocation.startsWith(optionText)){
							regLocation = regLocation.replaceAll(optionText,"");
						}else{
							regLocation = regLocation.replaceAll(optionSuf,"");
						}
						return false;//跳出该each循环
					}
				});
			}
			if(provinceNameDefault == ''){//未匹配到省份，再匹配一次城市，有可能地址是	岳阳县新开镇胜天村长塘组- 实际是岳阳市。
				$.ajax({
					type : "POST",
					url : page_url+"/system/region/getRegionCityAll.do",
					dataType : 'json',
					async:true,
					success : function(data) {
						var provinceCode = 0;
						$.each(data.listData,function(i,n){
							 var regionName = data.listData[i]['regionName']; //岳阳市   regLocation 岳阳县新开镇胜天村长塘组 regionName  岳阳市
							var cityNameSuf = regionName.replaceAll("县","").replaceAll("市","");
							if(regLocation.startsWith(regionName) || regLocation.startsWith(cityNameSuf)){
								cityNameDefault = regionName;
								regLocation = regLocation.startsWith(regionName)?regLocation.replaceAll(regionName,""):regLocation.replaceAll(cityNameSuf,"");
								provinceCode =data.listData[i]['parentId'];
								return false;
							}
						});
						$('select[name="provinceId"]').val(provinceCode);
						$("select[name='provinceId']").trigger('change');
						$('input[name="provinceName"]').val($('#orderTerminal-province').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-province').find('option:selected').text());

						setTimeout(function () {
							$("select[name='cityId'] option:contains('"+cityNameDefault+"')").prop("selected", true);
							$("select[name='cityId']").trigger('change');
							$('input[name="cityId"]').val($('select[name="cityId"]').val());
							$('input[name="cityName"]').val($('#orderTerminal-city').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-city').find('option:selected').text());

							setTimeout(function(){  //计算区的选择逻辑
								$("select[name=\"areaId\"] option").each(function() {
									var optionText = $(this).text();
									var optionSuf = optionText.replaceAll("区","").replaceAll("自治州","").replaceAll("市","");
									regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
									regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
									regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;

									if(regLocation.startsWith(optionSuf)){//optionSuf  相城
										areaNameDefault = optionText;
										//江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
										regLocation = regLocation.replace(optionSuf,'');
										regLocation = regLocation.replace('区','');
										regLocation = regLocation.replace('自治区','');
										regLocation = regLocation.replace('县','');

										return false;//跳出该each循环
									}
								});
								if(areaNameDefault!=''){
									$("select[name='areaId'] option:contains('"+areaNameDefault+"')").prop("selected", true);
									$('input[name="areaId"]').val($('select[name="areaId"]').val());
									$('input[name="areaName"]').val($('#orderTerminal-area').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-area').find('option:selected').text());

								}
							},500);
						},500);

					},
					error:function(data){
						if(data.status ==1001){
							layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
						}
					}
				});
			}else{//如果省份已经匹配到了
				$("select[name='provinceId'] option:contains('"+provinceNameDefault+"')").prop("selected", true);
				$("select[name='provinceId']").trigger('change');
				$('input[name="provinceId"]').val($('select[name="provinceId"]').val());
				$('input[name="provinceName"]').val($('#orderTerminal-province').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-province').find('option:selected').text());
				setTimeout(function () {
					if(cityNameDefault ==''){
						$("select[name=\"cityId\"] option").each(function() {
							var optionText = $(this).text();
							var optionSuf = optionText.replaceAll("市","").replaceAll("县","").replaceAll("自治州","");
							if(regLocation.startsWith(optionSuf)){
								cityNameDefault = optionText;
								regLocation = regLocation.startsWith(optionText)?regLocation.replaceAll(optionText,""):regLocation.replaceAll(optionSuf,"");
								regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
								regLocation = regLocation.startsWith("自治州")?regLocation.replace("自治州",""):regLocation;
								regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
								return false;//跳出该each循环
							}
						});
						if(cityNameDefault  == ''){ //如果仍然没有匹配到城市
							return ;
						}
					}
					$("select[name='cityId'] option:contains('"+cityNameDefault+"')").prop("selected", true);
					$("select[name='cityId']").trigger('change');
					$('input[name="cityId"]').val($('select[name="cityId"]').val());
					$('input[name="cityName"]').val($('#orderTerminal-city').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-city').find('option:selected').text());

					setTimeout(function(){  //计算区的选择逻辑
						$("select[name=\"areaId\"] option").each(function() {
							var optionText = $(this).text();
							var optionSuf = optionText.replaceAll("区","").replaceAll("镇","");
							regLocation = regLocation.startsWith("市")?regLocation.replace("市",""):regLocation;
							regLocation = regLocation.startsWith("县")?regLocation.replace("县",""):regLocation;
							if(regLocation.startsWith(optionSuf)){
								areaNameDefault = optionText;
								//江苏苏州相城经济开发区（澄波路） 相城经济开发区（澄波路）
								regLocation = regLocation.replace(optionSuf,'');
								regLocation = regLocation.replace('区','');
								regLocation = regLocation.replace('自治区','');
								regLocation = regLocation.replace('县','');
								return false;//跳出该each循环
							}
						});
						if(areaNameDefault!=''){
							$("select[name='areaId'] option:contains('"+areaNameDefault+"')").prop("selected", true);
							$('input[name="areaId"]').val($('select[name="areaId"]').val());
							$('input[name="areaName"]').val($('#orderTerminal-area').find('option:selected').text() == "请选择" ? "" : $('#orderTerminal-area').find('option:selected').text());
						}
					},500);
				},500);
			}
		}
	}

</script>
<div class="content mt10">
	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">基本信息</div>
		</div>
		<table
			class="table">
			<tbody>
				<tr>
					<td class="table-smaller">报价单号</td>
					<td>${quote.quoteorderNo}</td>
					<td class="table-smaller">报价单状态</td>
					<td><c:choose>
							<c:when test="${quote.validStatus eq 1}">
								<span style="color: green;">已生效</span>
							</c:when>
							<c:otherwise>
								<span style="color: red;">未生效</span>
							</c:otherwise>
						</c:choose></td>
				</tr>
				<tr>
					<td>创建者</td>
					<td>${quote.creatorName}</td>
					<td>创建时间</td>
					<td><date:date value="${quote.addTime}" /></td>
				</tr>
				<tr>
					<td>销售部门</td>
					<td>${quote.salesDeptName}</td>
					<%-- <td>销售人员</td>
						<td>${quote.salesName}</td> --%>
					<td>归属人员</td>
					<td>${quote.optUserName}</td>
				</tr>
				<%-- <c:if test="${quote.followOrderStatus eq 2}"> --%>
					<tr>
						<td>跟单状态</td>
						<td>
							<c:choose>
								<c:when test="${quote.followOrderStatus eq 1}">
									成单
									<a class='addtitle' href='javascript:void(0);' tabtitle='{"num":"viewsaleorder${quote.saleorderId}","link":"./order/saleorder/view.do?saleorderId=${quote.saleorderId}","title":"订单信息"}'>(${quote.saleorderNo})</a>
								</c:when>
								<c:when test="${quote.followOrderStatus eq 2}">
									失单
									<c:if test="${!empty quote.followOrderStatusComments}">
										(<span style="color: red">${quote.followOrderStatusComments}</span>)
									</c:if>
								</c:when>
								<c:otherwise>跟单中</c:otherwise>
							</c:choose>
						</td>
						<td>成单/失单时间</td>
						<td><date:date value="${quote.followOrderTime}" /></td>
					</tr>
				<%-- </c:if> --%>
				<tr>
					<td>商机编号</td>
					<td><a class="addtitle" href="javascript:void(0);"
						tabTitle='{"num":"view${quote.bussinessChanceId}",
								"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${quote.bussinessChanceId}",
								"title":"商机详情"}'>${quote.bussinessChanceNo}
					</a></td>
					<td>商机时间</td>
					<td><date:date value="${quote.receiveTime}" /></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">客户相关信息</div>
			<c:if test="${quote.followOrderStatus ne 2}">
				<div class="title-click nobor  pop-new-data"
					layerParams='{"width":"60%","height":"70%","title":"编辑客户","link":"./editQuote.do?quoteorderId=${quote.quoteorderId}"}'>编辑</div>
			</c:if>
		</div>
		<table
			class="table table-bordered table-striped table-condensed table-centered">
			<tbody>
				<tr>
					<td class="table-smaller">客户名称</td>
					<td>
						<div class="customername pos_rel">
							<span class="font-blue"> <a class="addtitle"
								href="javascript:void(0);"
								tabTitle='{"num":"viewcustomer${customer.traderCustomerId}",
										"link":"./trader/customer/baseinfo.do?traderCustomerId=${customer.traderCustomerId}&traderId=${customer.traderId}",
										"title":"客户信息"}'>
								<c:if test="${not empty traderGroupMap && not empty traderGroupMap[quote.traderId]}">
									<span style="color: red">【${traderGroupMap[quote.traderId].traderGroupName}】</span>
								</c:if>
								${quote.traderName}
							</a>
							</span> <i class="iconbluemouth"></i>
							<div class="pos_abs customernameshow mouthControlPos">
								报价次数：${customer.quoteCount} <br />
								交易次数：${customer.buyCount} <br />
								交易金额：${customer.buyMoney} <br />
								上次交易时间：<date:date value="${customer.lastBussinessTime}" /><br />
								归属销售：${quote.optUserName}
							</div>
						</div>
					</td>
					<td class="table-smaller">地区</td>
					<td>${quote.area}</td>
				</tr>
				<tr>
					<td>客户类型</td>
					<td>${quote.customerTypeStr}</td>
					<td>客户性质</td>
					<td>${quote.customerNatureStr}</td>
				</tr>
				<tr>
					<td>客户等级</td>
					<td>${quote.customerLevel}</td>
					<td>新老客户</td>
					<td>
						<c:choose>
							<c:when test="${quote.isNewCustomer eq 0}">老客户</c:when>
							<c:otherwise>新客户</c:otherwise>
						</c:choose>
					</td>
				</tr>
				<tr>
					<td>联系人</td>
					<td>${quote.traderContactName}</td>
					<td>电话</td>
					<td><c:if test="${!empty quote.telephone}">
							<i class="icontel cursor-pointer" title="点击拨号"
								onclick="callout('${quote.telephone}',${quote.traderId},1,3,${quote.quoteorderId},0);"></i>${quote.telephone}
							</c:if></td>
				</tr>
				<tr>
					<td>手机</td>
					<td><c:if test="${!empty quote.mobile}">
							<i class="icontel cursor-pointer" title="点击拨号"
								onclick="callout('${quote.mobile}',${quote.traderId},1,3,${quote.quoteorderId},0);"></i>${quote.mobile}
							</c:if></td>
					<td>联系地址</td>
					<td>${quote.address}</td>
				</tr>
				<tr>
					<td>联系人情况</td>
					<td>
						<c:choose>
							<c:when test="${quote.isPolicymaker eq 1}">采购关键人</c:when>
							<c:otherwise>非采购关键人</c:otherwise>
						</c:choose>
					</td>
					<td>采购方式</td>
					<td>${quote.purchasingTypeStr}</td>
				</tr>
				<tr>
					<td>付款条件</td>
					<td>${quote.paymentTermStr}</td>
					<td>采购时间</td>
					<td>${quote.purchasingTimeStr}</td>
				</tr>
				<tr>
					<td>项目进展情况</td>
					<td colspan="3">${quote.projectProgress}</td>
				</tr>
			</tbody>
		</table>
	</div>

	<input type="hidden" value="${quote.quoteorderId}" name="quoteorderId" id="quoteorderId">
	<c:if test="${quote.customerNature eq 465}"><!-- 分销 -->
		<div class="parts" id="updateTerminalInfo">
			<div class="formtitle">终端信息</div>
			<ul class="payplan">
				<c:choose>
					<c:when test="${not empty orderTerminalDto.terminalName}"><!-- 客户名称存在，则默认不显示选择框 -->
						<li id="terminalNameCheck" style="display: none;">
					</c:when>
					<c:otherwise><!-- 客户名称不存在，默认显示选择框 -->
						<li id="terminalNameCheck">
					</c:otherwise>
				</c:choose>
					<div class="infor_name">
						<label>终端名称</label>
					</div>
					<div class="f_left">
						<div class="inputfloat" id="errorTxtMsg">
							<!-- 客户为终端 -->
							<input type="text" placeholder="搜索选择终端" class="input-largest" name="terminalName" id="terminalName" disabled="disabled">
							<label class="bt-bg-style bg-light-blue bt-small" onclick="searchTerminalNew();" id="errorMes">搜索</label>
							<span style="display: none;"> <!-- 弹框 -->
								<div class="title-click nobor  pop-new-data" id="terminalDiv"></div>
							</span>
						</div>
					</div>
				</li>
				<c:choose>
					<c:when test="${empty orderTerminalDto.terminalName}"><!-- 客户名称不存在，默认显示选择框 -->
						<li id="terminalNameDetail" style="display: none;">
					</c:when>
					<c:otherwise><!-- 客户名称不存在，默认显示选择框 -->
						<li id="terminalNameDetail">
					</c:otherwise>
				</c:choose>
					<div class="infor_name">
						<label>终端名称</label>
					</div>
					<div class="f_left">
						<div class=" inputfloat" id="errorTxtMsg">
							<span class="mr8 mt3" id="terminalTraderNameDiv">${orderTerminalDto.terminalName}</span>
							<label class="bt-bg-style bg-light-blue bt-small" onclick="repeatSearchTerminal();">重新搜索</label>
						</div>
					</div>
				</li>
				<li>
					<div class="infor_name ">
						<span id="regionMust">*</span>
						<label>销售区域</label>
					</div>
					<div class="f_left">
						<select class="input-small f_left mr10" name="provinceId" id="orderTerminal-province">
							<option value="0">请选择</option>
							<c:if test="${not empty provinceList }">
								<c:forEach items="${provinceList }" var="province">
									<option value="${province.regionId }"
										<c:if test="${orderTerminalDto.provinceId == province.regionId }">selected="selected"</c:if>>${province.regionName }</option>
								</c:forEach>
							</c:if>
						</select>
						<select class="input-small f_left mr10" name="cityId" id="orderTerminal-city">
							<option value="0">请选择</option>
							<c:if test="${not empty cityList }">
								<c:forEach items="${cityList }" var="city">
									<option value="${city.regionId }"
										<c:if test="${orderTerminalDto.cityId == city.regionId }">selected="selected"</c:if>>${city.regionName }</option>
								</c:forEach>
							</c:if>
						</select>
						<select class="input-small f_left" name="areaId" id="orderTerminal-area">
							<option value="0">请选择</option>
							<c:if test="${not empty zoneList }">
								<c:forEach items="${zoneList }" var="zone">
									<option value="${zone.regionId }"
										<c:if test="${orderTerminalDto.areaId == zone.regionId }">selected="selected"</c:if>>${zone.regionName }</option>
								</c:forEach>
							</c:if>
						</select>
						<div id="sales_area_msg_div" style="clear:both"></div>
					</div>
				</li>
			</ul>
		</div>
	</c:if>

	<div class="parts">
		<div class="title-container">
			<div class="table-title nobor">产品信息</div>
			<c:if test="${quote.followOrderStatus ne 2}">
				<div class="title-click nobor  pop-new-data"
					layerParams='{"width":"900px","height":"650px","title":"添加产品","link":"./addQuoteGoods.do?quoteorderId=${quote.quoteorderId}"}'>添加</div>
			</c:if>
		</div>
		<table
			class="table table-bordered table-striped table-condensed table-centered">
			<thead>
				<tr>

					<th class="wid4">序号</th>
					<th width="10%">产品名称</th>
					<th width="10%">品牌型号</th>
					<th width="6%">报价</th>
					<th width="5%">数量</th>
					<th width="5%">单位</th>
					<th width="6%">总额</th>
					<th width="5%">货期</th>
					<th width="5%">直发</th>
					<th class="wid8">可用/库存量</th>
					<th width="8%">核价参考</th>
					<th width="10%">供应链回复</th>
					<th width="5%">含安调</th>
					<th>产品备注</th>
					<th>内部备注</th>
					<th class="wid12">操作</th>
				</tr>
			</thead>
			<tbody id="goodsTbody">
				<c:set var="num" value="0"></c:set>
				<%-- <c:set var="totleMoney" value="0.00"></c:set> --%>
				<c:forEach var="list" items="${quoteGoodsList}" varStatus="staut">
					<tr <c:if test="${list.isDelete eq 1}">class="caozuo-grey"</c:if>>
						<c:if test="${list.isDelete eq 0}">
							<c:set var="num" value="${num + list.num}"></c:set>
							<%-- <c:set var="totleMoney" value="${totleMoney + (list.price * list.num)}"></c:set> --%>
						</c:if>
							<input type="hidden" class="isDelete" value="${list.isDelete}" ></input>
							<td>${staut.count}</td>
						<td class="text-left">
							<div class="customername pos_rel">
								<c:choose>
									<c:when test="${list.isDelete eq 1}">
										${list.goodsName}
											<i class="iconbluemouth"></i>
										<br>
									</c:when>
									<c:otherwise>
										<!-- 未删除 -->
										<c:if test="${list.isTemp eq 1}">${list.goodsName}</c:if>
										<c:if test="${list.isTemp eq 0}"><!-- 非临时产品 -->
											<span class="font-blue">
												<a class="addtitle" href="javascript:void(0);"
													tabtitle='{"num":"viewgoods${list.goodsId}","link":"./goods/goods/viewbaseinfo.do?goodsId=${list.goodsId}","title":"产品信息"}'>
													${list.goodsName}
												<i class="iconbluemouth"></i><br>
												</a>
											</span>
										</c:if>
									</c:otherwise>
								</c:choose>
								<c:if test="${list.isTemp eq 1}">${list.sku}</c:if>
								<c:if test="${list.isTemp eq 0}"><!-- 非临时产品 -->${newSkuInfosMap[list.sku].SKU_NO}</c:if>


								<c:set var="skuNo" value="${list.sku}"></c:set>
								<%@ include file="../../common/new_sku_common_tip.jsp" %>
							</div>
						</td>
						<td>

								${list.brandName}
							<br/>
								${list.model}


						</td>
							<td>
									${list.price}
								<c:if test= "${1 == list.isLowerGoods}" >
                                <span class="f_right inputfloat customername pos_rel">
                                <i class="iconredsigh ml4 contorlIcon"></i>
                                <div class="pos_abs customernameshow">
                                    核价经销价:${list.checkPrice}
                                </div>
                            </span>
								</c:if>
							</td>
						<td>${list.num}</td>
						<td>
								${list.unitName}
						</td>
						<td><fmt:formatNumber type="number"
								value="${list.price * list.num}" pattern="0.00"
								maxFractionDigits="2" /></td>
						<td>${list.deliveryCycle}</td>
						<td>
							<div class="customername pos_rel">
								<span> <c:choose>
										<c:when test="${list.deliveryDirect eq 0}">否</c:when>
										<c:otherwise>
												是
											<i class="iconbluesigh ml4"></i>
											<div class="pos_abs customernameshow">直发原因：${list.deliveryDirectComments}</div>
										</c:otherwise>
									</c:choose>
								</span>
							</div>
						</td>
						<td>${list.goods.availableStockNum}/${list.goods.stockNum}</td>
						<td>
							<div class="customername pos_rel" style="text-align: left">
								<tags:order_goods_consult_hejia_info item="${list}" userList="${userList}" skuNoAndPriceMap="${skuNoAndPriceMap}" />
							</div>
							<div class="customername pos_rel" style="text-align: left">
								<tags:sku_authorization list="${list}" terminalTypes="${terminalTypes}" regions="${regions}"/>
							</div>
						</td>
						<td>
							<div class="customername pos_rel" style="text-align: left">
								<tags:order_goods_consult_info item="${list}" quote="${quote}" userList="${userList}" />
							</div>
						</td>
						<td><c:choose>
								<c:when test="${list.haveInstallation eq 0}">否</c:when>
								<c:otherwise>是</c:otherwise>
							</c:choose></td>
						<td>${list.goodsComments}</td>
						<td class="c-comments" skuId="${list.goodsId}">
							<div class="customername pos_rel f_left lm-main-item">
									${list.insideComments}
										<c:if test="${list.componentHtml ne ''}">
									<%--								<input type="text" class="input-large" placeholder="仅内部可见" name="insideComments" id="insideComments" value="" onclick="insideRemark(this)" label_data="" readonly/>--%>
								<i class="iconbluemouth contorlIcon"></i>
								<div class="pos_abs customernameshow" label_left="-305" style="width: 500px; top: 25px;background-color: #00CD66;">
										${list.componentHtml}
								</div>
										</c:if>
							</div>
							<div class="no_remark_error" style="display: none;">
								<span style="color:red;">请设置订单要求，点击编辑进行内部备注修改</span>
							</div>
						</td>

						<td>
							<div class="caozuo">
								<c:if test="${quote.followOrderStatus ne 2}">
									<!-- 未失单 -->
									<c:choose>
										<c:when test="${list.isDelete eq 0}">
											<span class="caozuo-blue pop-new-data"
												layerparams='{"width":"700px","height":"650px","title":"编辑产品信息","link":"./editQuoteGoodsInit.do?quoteorderId=${list.quoteorderId}&quoteorderGoodsId=${list.quoteorderGoodsId}"}'>编辑</span>
											<span class="caozuo-red"
												onclick="delQuoteGoods(${list.quoteorderId},${list.quoteorderGoodsId},'${list.sku}',1);">删除</span>
										</c:when>
										<c:otherwise>
											已删除
										</c:otherwise>
									</c:choose>
								</c:if>
							</div>
						</td>
				</c:forEach>
				<tr style="background: #eaf2fd;">
					<td colspan="16" class="text-left">总件数<span class="font-red">${num}</span>，
						总金额 <span class="font-red"> <fmt:formatNumber type="number" value="${quote.totalAmount==null?0:quote.totalAmount}" pattern="0.00" maxFractionDigits="2" />
						<input type="hidden" id="goodsTotleMoney" value="${quote.totalAmount==null?0:quote.totalAmount}">
					</span>
					</td>
				</tr>
			</tbody>
		</table>
	</div>
	<!-- ------------------付款计划 AND 其他信息--------------------------- -->
	<form method="post" id="quotePayMoneForm" action="./editQuoteAmount.do">
	<input type="hidden" name="beforeParams" value='${beforeParams}'><!-- 日志 -->
	<input type="hidden" value="${quote.customerNature}" id="quoteCustomerNature">
	<c:if test="${quote.customerNature eq 465}"><!-- 分销 -->
		<!-- 终端ID、名称、类型  地区和最小级ID -->
		<input type="hidden" name="terminalTraderName" id="terminalTraderName" class="terminal" value="${quote.terminalTraderName}"/>
		<input type="hidden" name="terminalTraderId" id="terminalTraderId" class="terminal" value="${quote.terminalTraderId}"/>
		<input type="hidden" name="terminalTraderType" id="terminalTraderType" class="terminal" value="${quote.terminalTraderType}"/>
		<input type="hidden" name="salesArea" id="salesArea" class="terminal" value="${quote.salesArea}"/>
		<input type="hidden" name="salesAreaId" id="salesAreaId" class="terminal" value="${quote.salesAreaId}"/>

		<%--VDERP-15595 新终端信息--%>
		<input type="hidden" name="dwhTerminalId" id="dwhTerminalId" class="orderTerminal" value="${orderTerminalDto.dwhTerminalId}"/>
		<input type="hidden" name="terminalName" id="terminalNameNew" class="orderTerminal" value="${orderTerminalDto.terminalName}"/>
		<input type="hidden" name="unifiedSocialCreditIdentifier" class="orderTerminal" id="unifiedSocialCreditIdentifier" value="${orderTerminalDto.unifiedSocialCreditIdentifier}"/>
        <input type="hidden" name="organizationCode" class="orderTerminal" id="organizationCode" value="${orderTerminalDto.organizationCode}"/>
        <input type="hidden" name="provinceId" id="provinceId" class="orderTerminal" value="${orderTerminalDto.provinceId}"/>
		<input type="hidden" name="cityId" id="cityId" class="orderTerminal" value="${orderTerminalDto.cityId}"/>
		<input type="hidden" name="areaId" id="areaId" class="orderTerminal" value="${orderTerminalDto.areaId}"/>
		<input type="hidden" name="provinceName" id="provinceName" class="orderTerminal" value="${orderTerminalDto.provinceName}"/>
		<input type="hidden" name="cityName" id="cityName" class="orderTerminal" value="${orderTerminalDto.cityName}"/>
		<input type="hidden" name="areaName" id="areaName" class="orderTerminal" value="${orderTerminalDto.areaName}"/>
		<input type="hidden" value="${quote.quoteorderNo}" name="quoteorderNo" id="quoteorderNo">
	</c:if>
		<div class="parts content1">
			<input type="hidden" name="quoteorderId" id="quoteorderId" value="${quote.quoteorderId}" />
			<input type="hidden" name="quoteSource" id="quoteSource" value="${quoteSource}">
			<div class="formtitle mt10">付款计划</div>
			<ul class="payplan">
				<li>
					<div class="infor_name">
						<label>付款方式</label>
					</div>
					<div class="f_left inputfloat">
						<input type="hidden" id="paymentTypeHid" value="${quote.paymentType}">
						<c:choose>
							<c:when test="${quote.paymentType eq 0}">
								<select id="paymentType" name="paymentType" autocomplete="off" class="input-middle" onChange="totleMoney(this)">
									<c:forEach var="list" items="${paymentTermList}" varStatus="status">
										<option value="${list.sysOptionDefinitionId}"
											<c:if test="${list.sysOptionDefinitionId eq 419}">selected</c:if>>${list.title}</option>
									</c:forEach>
								</select>
							</c:when>
							<c:otherwise>
								<select id="paymentType" name="paymentType" autocomplete="off" class="input-middle" onChange="totleMoney(this)">
									<c:forEach var="list" items="${paymentTermList}" varStatus="status">
										<option value="${list.sysOptionDefinitionId}"
											<c:if test="${list.sysOptionDefinitionId eq quote.paymentType}">selected</c:if>>${list.title}</option>
									</c:forEach>
								</select>
							</c:otherwise>
						</c:choose>
					</div>
				</li>
				<li>
					<div class="infor_name">
						<label>预付金额</label>
					</div>

					<div class="f_left">
						<input type="text" class="input-middle" autocomplete="off" id="prepaidAmount" name="prepaidAmount"
							<c:if test="${quote.paymentType ne 424}">
								readonly
							</c:if>
							<c:choose>
								<c:when test="${quote.prepaidAmount == '0.00' && quote.paymentType == 419}">
									value="${quote.totalAmount}"
								</c:when>
								<c:otherwise>
									value="${quote.prepaidAmount}"
								</c:otherwise>
							</c:choose>>
					</div>
					<div id="prepaidAmountError"></div>
				</li>
				<li id="accountPeriodLi"
					<c:if test="${(quote.paymentType eq 419) or (quote.paymentType eq 0)}">style="display:none"</c:if>>
					<!-- 419先款后货100%预付:0默认不显示 -->
					<div class="infor_name ">
						<label>账期支付</label>
					</div>
					<div class="f_left inputfloat">
						<!-- 账期支付最大限额（剩余账期额度） -->
						<input type="hidden" id="accountPeriodLeft" value="<fmt:formatNumber type="number" value="${customer.accountPeriodLeft}" pattern="0.00" maxFractionDigits="2" />">
						<input type="text" class="input-middle" name="accountPeriodAmount" id="accountPeriodAmount" value="<fmt:formatNumber type="number" value="${quote.accountPeriodAmount}" pattern="0.00" maxFractionDigits="2" />">
						<input type="checkbox" style="margin-top: 7px" name="logisticsCheckBox" id="logisticsCheckBox"
							<c:if test="${quote.logisticsCollection eq 1}">checked</c:if>>
						<label class="mt4">物流代收帐期款</label>
						<input type="hidden" name="logisticsCollection" id="logisticsCollection" />
						<div id="accountPeriodAmountError"></div>
					</div>
				</li>
				<li id="retainageLi" <c:if test="${quote.paymentType ne 424}">style="display:none"</c:if>>
					<!-- 424先货后款自定义 -->
					<div class="infor_name ">
						<label>尾款</label>
					</div>
					<div class="f_left">
						<div>
							<input type="text" class="input-middle" name="retainageAmount" id="retainageAmount" value="<fmt:formatNumber type="number" value="${quote.retainageAmount}" pattern="0.00" maxFractionDigits="2" />">
							<label class="ml10 mr10">尾款期限</label>
							<input type="text" class="input-smaller" name="retainageAmountMonth" id="retainageAmountMonth" value="${quote.retainageAmountMonth==0?'':quote.retainageAmountMonth}">
							<label>个月</label>
						</div>
						<div id="retainageAmountError"></div>
					</div>
				</li>
				<li id="accountAmountId" <c:if test="${(quote.paymentType eq 419) or (quote.paymentType eq 0)}">style="display:none"</c:if>>
					<!-- 419先款后货，预付100% -->
					<div class="infor_name "></div>
					<div class="f_left">
						<div class="font-grey9 mt4" id="retainageAmountError">
							客户当前帐期剩余额度<fmt:formatNumber type="number" value="${customer.accountPeriodLeft}" pattern="0.00" maxFractionDigits="2" />元，帐期天数${customer.periodDay}天；如需更改帐期，您需要在客户详情财务信息中申请帐期；
						</div>
					</div>
				</li>
			</ul>
		</div>
		<div class="line"></div>
		<div>
			<div class="formtitle">其他信息</div>
			<ul class="payplan">
				<li>
					<div class="infor_name">
						<span>*</span> <label>报价有效期</label>
					</div>
					<div class="f_left">
						<div class=" inputfloat" id="errorMsg">
							<input type="text" class="input-small" name="period" id="period"
								value="${quote.period==0?14:quote.period}"> <span
								class="mt4 mr10">天</span> <span class="font-grey9 mt4">不允许超过30天</span>
						</div>
					</div>
				</li>
				<li>
					<div class="infor_name ">
						<label>发票类型</label>
					</div> <!-- 默认选择普通发票 -->
					<!-- 获取当前日期 -->
					<jsp:useBean id="now" class="java.util.Date" />
					<fmt:formatDate value="${now}" type="both" dateStyle="long" var="today" pattern="yyyy-MM-dd"/>
					<div class="f_left">
						<select class="input-middle" name="invoiceType" id="invoiceType">
							<!-- 4月1号后税率只有13% -->
							<c:choose>
								<c:when test="${today >= '2019-04-01'}">
									<c:forEach var="list" items="${invoiceTypeList}" varStatus="status">
										<c:if test="${list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}"><!-- 屏蔽17%税率 -->
											<option value="${list.sysOptionDefinitionId}" id="${list.comments}"
												<c:if test="${list.sysOptionDefinitionId eq (invoice.invoiceType==null?972:invoice.invoiceType)}">selected</c:if>>${list.title}
											</option>
										</c:if>
									</c:forEach>
								</c:when>
								<c:otherwise>
									<c:forEach var="list" items="${invoiceTypeList}" varStatus="status">
										<c:if test="${list.sysOptionDefinitionId eq 681 or list.sysOptionDefinitionId eq 682 or list.sysOptionDefinitionId eq 971 or list.sysOptionDefinitionId eq 972}">
											<option value="${list.sysOptionDefinitionId}"
												<c:if test="${list.sysOptionDefinitionId eq (quote.invoiceType==0?972:quote.invoiceType)}">selected</c:if>>${list.title}</option>
										</c:if>
									</c:forEach>
								</c:otherwise>
							</c:choose>

						</select>
					</div>
				</li>
				<li>
					<div class="infor_name">
						<label>运费说明</label>
					</div>
					<div class="f_left">
						<select class="input-large" name="freightDescription"
							id="freightDescription">
							<c:forEach var="list" items="${freightList}" varStatus="status">
								<option value="${list.sysOptionDefinitionId}"
									<c:if test="${list.sysOptionDefinitionId eq quote.freightDescription}">selected</c:if>>${list.title}</option>
							</c:forEach>
						</select>
					</div>
				</li>
				<li>
					<div class="infor_name">
						<label>附加条款</label>
					</div>
					<div class="f_left">
						<input type="text" class="input-large" placeholder="面向客户条款，客户可见"
							name="additionalClause" id="additionalClause"
							value="${quote.additionalClause}">
					</div>
				</li>
				<li>
					<div class="infor_name ">
						<label>内部备注</label>
					</div>
					<div class="f_left inputfloat">
						<input type="text" class="input-large" placeholder="仅内部可见"
							name="comments" id="comments" value="${quote.comments}">
					</div>
				</li>
			</ul>
		</div>
		<c:if test="${quote.followOrderStatus ne 2}">
			<!-- 未失单 -->
			<div class="add-tijiao ml110 mb15">
				<button type="button" class="bt-bg-style bg-deep-green" onclick="quotePayMoneSub()">确定</button>
			</div>
		</c:if>
		<input type="hidden" name="formToken" value="${formToken}"/>
	</form>

<%@ include file="../../common/footer.jsp"%>
