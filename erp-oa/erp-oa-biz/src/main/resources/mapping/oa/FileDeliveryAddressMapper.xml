<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.oa.dao.FileDeliveryAddressMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.oa.model.FileDeliveryAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="APPLY_ID" jdbcType="INTEGER" property="applyId" />
    <result column="FILE_DELIVERY_ID" jdbcType="INTEGER" property="fileDeliveryId" />
    <result column="TRADER_TYPE" jdbcType="INTEGER" property="traderType" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="CONTACT_TYPE" jdbcType="INTEGER" property="contactType" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_ADDRESS_ID" jdbcType="INTEGER" property="traderContactAddressId" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="TRADER_CONTACT_ADDRESS" jdbcType="VARCHAR" property="traderContactAddress" />
    <result column="IS_DELETE" jdbcType="TINYINT" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
    <result column="CITY_ID" jdbcType="INTEGER" property="cityId" />
    <result column="PROVINCE_ID" jdbcType="INTEGER" property="provinceId" />
    <result column="DELIVERY_STATUS" jdbcType="INTEGER" property="deliveryStatus" />
    <result column="LOGISTICS_NO" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="TRADER_CONTACT_ADDRESS_INFO" jdbcType="VARCHAR" property="traderContactAddressInfo" />
    <result column="EXPRESS_LABELURL" jdbcType="VARCHAR" property="expressLabelurl" />
    <result column="LOGISTICS_NO_EDIT" jdbcType="VARCHAR" property="logisticsNoEdit" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    ID, APPLY_ID, FILE_DELIVERY_ID, TRADER_TYPE, TRADER_ID, TRADER_NAME, CONTACT_TYPE, 
    TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_ADDRESS_ID, 
    AREA_ID, TRADER_CONTACT_ADDRESS, IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, UPDATER, 
    UPDATE_REMARK, CITY_ID, PROVINCE_ID, DELIVERY_STATUS, LOGISTICS_NO, TRADER_CONTACT_ADDRESS_INFO, 
    EXPRESS_LABELURL, LOGISTICS_NO_EDIT
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.oa.model.FileDeliveryAddressExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_FILE_DELIVERY_ADDRESS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_FILE_DELIVERY_ADDRESS
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    delete from T_FILE_DELIVERY_ADDRESS
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.oa.model.FileDeliveryAddressExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    delete from T_FILE_DELIVERY_ADDRESS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.oa.model.FileDeliveryAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FILE_DELIVERY_ADDRESS (APPLY_ID, FILE_DELIVERY_ID, TRADER_TYPE, 
      TRADER_ID, TRADER_NAME, CONTACT_TYPE, 
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_ADDRESS_ID, AREA_ID, TRADER_CONTACT_ADDRESS, 
      IS_DELETE, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, UPDATE_REMARK, 
      CITY_ID, PROVINCE_ID, DELIVERY_STATUS, 
      LOGISTICS_NO, TRADER_CONTACT_ADDRESS_INFO, 
      EXPRESS_LABELURL, LOGISTICS_NO_EDIT)
    values (#{applyId,jdbcType=INTEGER}, #{fileDeliveryId,jdbcType=INTEGER}, #{traderType,jdbcType=INTEGER}, 
      #{traderId,jdbcType=INTEGER}, #{traderName,jdbcType=VARCHAR}, #{contactType,jdbcType=INTEGER}, 
      #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR}, 
      #{traderContactAddressId,jdbcType=INTEGER}, #{areaId,jdbcType=INTEGER}, #{traderContactAddress,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=TINYINT}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{updateRemark,jdbcType=VARCHAR}, 
      #{cityId,jdbcType=INTEGER}, #{provinceId,jdbcType=INTEGER}, #{deliveryStatus,jdbcType=INTEGER}, 
      #{logisticsNo,jdbcType=VARCHAR}, #{traderContactAddressInfo,jdbcType=VARCHAR}, 
      #{expressLabelurl,jdbcType=VARCHAR}, #{logisticsNoEdit,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.oa.model.FileDeliveryAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_FILE_DELIVERY_ADDRESS
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="applyId != null">
        APPLY_ID,
      </if>
      <if test="fileDeliveryId != null">
        FILE_DELIVERY_ID,
      </if>
      <if test="traderType != null">
        TRADER_TYPE,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null">
        TRADER_NAME,
      </if>
      <if test="contactType != null">
        CONTACT_TYPE,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactAddressId != null">
        TRADER_CONTACT_ADDRESS_ID,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="traderContactAddress != null">
        TRADER_CONTACT_ADDRESS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
      <if test="cityId != null">
        CITY_ID,
      </if>
      <if test="provinceId != null">
        PROVINCE_ID,
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS,
      </if>
      <if test="logisticsNo != null">
        LOGISTICS_NO,
      </if>
      <if test="traderContactAddressInfo != null">
        TRADER_CONTACT_ADDRESS_INFO,
      </if>
      <if test="expressLabelurl != null">
        EXPRESS_LABELURL,
      </if>
      <if test="logisticsNoEdit != null">
        LOGISTICS_NO_EDIT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="applyId != null">
        #{applyId,jdbcType=INTEGER},
      </if>
      <if test="fileDeliveryId != null">
        #{fileDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        #{traderType,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        #{contactType,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactAddressId != null">
        #{traderContactAddressId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="traderContactAddress != null">
        #{traderContactAddress,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        #{cityId,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null">
        #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="traderContactAddressInfo != null">
        #{traderContactAddressInfo,jdbcType=VARCHAR},
      </if>
      <if test="expressLabelurl != null">
        #{expressLabelurl,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNoEdit != null">
        #{logisticsNoEdit,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.oa.model.FileDeliveryAddressExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    select count(*) from T_FILE_DELIVERY_ADDRESS
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_ADDRESS
    <set>
      <if test="record.id != null">
        ID = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.applyId != null">
        APPLY_ID = #{record.applyId,jdbcType=INTEGER},
      </if>
      <if test="record.fileDeliveryId != null">
        FILE_DELIVERY_ID = #{record.fileDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="record.traderType != null">
        TRADER_TYPE = #{record.traderType,jdbcType=INTEGER},
      </if>
      <if test="record.traderId != null">
        TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      </if>
      <if test="record.traderName != null">
        TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      </if>
      <if test="record.contactType != null">
        CONTACT_TYPE = #{record.contactType,jdbcType=INTEGER},
      </if>
      <if test="record.traderContactId != null">
        TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      </if>
      <if test="record.traderContactName != null">
        TRADER_CONTACT_NAME = #{record.traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{record.traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactAddressId != null">
        TRADER_CONTACT_ADDRESS_ID = #{record.traderContactAddressId,jdbcType=INTEGER},
      </if>
      <if test="record.areaId != null">
        AREA_ID = #{record.areaId,jdbcType=INTEGER},
      </if>
      <if test="record.traderContactAddress != null">
        TRADER_CONTACT_ADDRESS = #{record.traderContactAddress,jdbcType=VARCHAR},
      </if>
      <if test="record.isDelete != null">
        IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      </if>
      <if test="record.addTime != null">
        ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modTime != null">
        MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.creator != null">
        CREATOR = #{record.creator,jdbcType=INTEGER},
      </if>
      <if test="record.updater != null">
        UPDATER = #{record.updater,jdbcType=INTEGER},
      </if>
      <if test="record.updateRemark != null">
        UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="record.cityId != null">
        CITY_ID = #{record.cityId,jdbcType=INTEGER},
      </if>
      <if test="record.provinceId != null">
        PROVINCE_ID = #{record.provinceId,jdbcType=INTEGER},
      </if>
      <if test="record.deliveryStatus != null">
        DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="record.logisticsNo != null">
        LOGISTICS_NO = #{record.logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="record.traderContactAddressInfo != null">
        TRADER_CONTACT_ADDRESS_INFO = #{record.traderContactAddressInfo,jdbcType=VARCHAR},
      </if>
      <if test="record.expressLabelurl != null">
        EXPRESS_LABELURL = #{record.expressLabelurl,jdbcType=VARCHAR},
      </if>
      <if test="record.logisticsNoEdit != null">
        LOGISTICS_NO_EDIT = #{record.logisticsNoEdit,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_ADDRESS
    set ID = #{record.id,jdbcType=INTEGER},
      APPLY_ID = #{record.applyId,jdbcType=INTEGER},
      FILE_DELIVERY_ID = #{record.fileDeliveryId,jdbcType=INTEGER},
      TRADER_TYPE = #{record.traderType,jdbcType=INTEGER},
      TRADER_ID = #{record.traderId,jdbcType=INTEGER},
      TRADER_NAME = #{record.traderName,jdbcType=VARCHAR},
      CONTACT_TYPE = #{record.contactType,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{record.traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{record.traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{record.traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_ADDRESS_ID = #{record.traderContactAddressId,jdbcType=INTEGER},
      AREA_ID = #{record.areaId,jdbcType=INTEGER},
      TRADER_CONTACT_ADDRESS = #{record.traderContactAddress,jdbcType=VARCHAR},
      IS_DELETE = #{record.isDelete,jdbcType=TINYINT},
      ADD_TIME = #{record.addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{record.modTime,jdbcType=TIMESTAMP},
      CREATOR = #{record.creator,jdbcType=INTEGER},
      UPDATER = #{record.updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{record.updateRemark,jdbcType=VARCHAR},
      CITY_ID = #{record.cityId,jdbcType=INTEGER},
      PROVINCE_ID = #{record.provinceId,jdbcType=INTEGER},
      DELIVERY_STATUS = #{record.deliveryStatus,jdbcType=INTEGER},
      LOGISTICS_NO = #{record.logisticsNo,jdbcType=VARCHAR},
      TRADER_CONTACT_ADDRESS_INFO = #{record.traderContactAddressInfo,jdbcType=VARCHAR},
      EXPRESS_LABELURL = #{record.expressLabelurl,jdbcType=VARCHAR},
      LOGISTICS_NO_EDIT = #{record.logisticsNoEdit,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.oa.model.FileDeliveryAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_ADDRESS
    <set>
      <if test="applyId != null">
        APPLY_ID = #{applyId,jdbcType=INTEGER},
      </if>
      <if test="fileDeliveryId != null">
        FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER},
      </if>
      <if test="traderType != null">
        TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderName != null">
        TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      </if>
      <if test="contactType != null">
        CONTACT_TYPE = #{contactType,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactAddressId != null">
        TRADER_CONTACT_ADDRESS_ID = #{traderContactAddressId,jdbcType=INTEGER},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="traderContactAddress != null">
        TRADER_CONTACT_ADDRESS = #{traderContactAddress,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=TINYINT},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
      <if test="cityId != null">
        CITY_ID = #{cityId,jdbcType=INTEGER},
      </if>
      <if test="provinceId != null">
        PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
      </if>
      <if test="deliveryStatus != null">
        DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      </if>
      <if test="logisticsNo != null">
        LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      </if>
      <if test="traderContactAddressInfo != null">
        TRADER_CONTACT_ADDRESS_INFO = #{traderContactAddressInfo,jdbcType=VARCHAR},
      </if>
      <if test="expressLabelurl != null">
        EXPRESS_LABELURL = #{expressLabelurl,jdbcType=VARCHAR},
      </if>
      <if test="logisticsNoEdit != null">
        LOGISTICS_NO_EDIT = #{logisticsNoEdit,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.oa.model.FileDeliveryAddress">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 22 14:17:41 CST 2024.
    -->
    update T_FILE_DELIVERY_ADDRESS
    set APPLY_ID = #{applyId,jdbcType=INTEGER},
      FILE_DELIVERY_ID = #{fileDeliveryId,jdbcType=INTEGER},
      TRADER_TYPE = #{traderType,jdbcType=INTEGER},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_NAME = #{traderName,jdbcType=VARCHAR},
      CONTACT_TYPE = #{contactType,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_ADDRESS_ID = #{traderContactAddressId,jdbcType=INTEGER},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      TRADER_CONTACT_ADDRESS = #{traderContactAddress,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=TINYINT},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      CITY_ID = #{cityId,jdbcType=INTEGER},
      PROVINCE_ID = #{provinceId,jdbcType=INTEGER},
      DELIVERY_STATUS = #{deliveryStatus,jdbcType=INTEGER},
      LOGISTICS_NO = #{logisticsNo,jdbcType=VARCHAR},
      TRADER_CONTACT_ADDRESS_INFO = #{traderContactAddressInfo,jdbcType=VARCHAR},
      EXPRESS_LABELURL = #{expressLabelurl,jdbcType=VARCHAR},
      LOGISTICS_NO_EDIT = #{logisticsNoEdit,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
</mapper>