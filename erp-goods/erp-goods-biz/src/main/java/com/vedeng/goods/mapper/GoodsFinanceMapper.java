package com.vedeng.goods.mapper;

import com.vedeng.goods.domain.entity.GoodsFinance;
import org.apache.ibatis.annotations.Param;
import com.vedeng.goods.domain.dto.GoodsFinanceInfoDto;

import java.util.List;

public interface GoodsFinanceMapper {
    /**
     * delete by primary key
     * @param goodsFinanceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long goodsFinanceId);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(GoodsFinance record);

    /**
     * select by primary key
     * @param goodsFinanceId primary key
     * @return object by primary key
     */
    GoodsFinance selectByPrimaryKey(Long goodsFinanceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(GoodsFinance record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(GoodsFinance record);

    void batchInsert(List<GoodsFinance> list);
    /**
     * 查询前一天生效的订单sku
     * @param time
     * @return
     */
    List<GoodsFinance> getSkuNoListByValidTime(@Param("time") long time);

    /**
     * 根据 SKU 查询商品财务信息
     * @param skuNo 订货号
     * @return 商品财务信息
     */
    GoodsFinanceInfoDto selectGoodsFinanceBySkuNo(@Param("skuNo") String skuNo);

    /**
     * 根据主键查询商品财务信息
     * @param goodsFinanceId
     * @return
     */
    GoodsFinanceInfoDto selectGoodsFinanceByPrimaryKey(@Param("goodsFinanceId") Long goodsFinanceId);

    /**
     * select by skuNo
     * @param skuNo skuNo
     * @return GoodsFinance by skuNo
     */
    GoodsFinance selectBySkuNo(@Param("skuNo") String skuNo);

    /**
     * select by skuNo list
     * @param skuNoList skuNo list
     * @return GoodsFinance by skuNo list
     */
    List<GoodsFinance> selectBySkuNoList(@Param("skuNoList") List<String> skuNoList);

    /**
     * get sku list by sku list
     * @param skuNoList  sku list
     * @return sku list
     */
    List<String> getSkuNoListByQueryList(@Param("skuNoList") List<String> skuNoList);

    /**
     * update batch goodsFinance by list
     * @param list goodsFinance list
     * @return update count
     */
    int updateBatchGoodsFinanceBySkuNo(@Param("list") List<GoodsFinance> list);

    int updateBatchSelective(@Param("list") List<GoodsFinance> list);

    GoodsFinance findByGoodsFinanceId(@Param("goodsFinanceId")Long goodsFinanceId);



}