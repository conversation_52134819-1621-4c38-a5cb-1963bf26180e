<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增售后跟进记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/orderstream/aftersales/add_followUp_record.js?rnd=${resourceVersionKey}'></script>
<div class="formpublic">
    <form method="post" action="" id="addFollowUpRecord">
        <ul>
             <li>
                <div class="infor_name">
                    <label style="color: red">*</label>
                    <lable>售后内容：</lable>
                </div>
                <div class="f_left table-largest">
                    <div>
                                <textarea name="content" placeholder=""
                                          style="width: 450px; height: 100px" maxlength="200" >${afterSalesFollowUpRecord.content}</textarea>
                        <div id="contentError"></div>
                    </div>
                </div>
            </li>
            
             <li>
                <div class="infor_name">
                    <lable>当前节点部门：</lable>
                </div>
                <div class="f_left  ">
                    <select class="mr5" name="orgId" id="orgId">
                        <c:forEach var="list" items="${orgList}" varStatus="num">
                            <option value="${list.orgId}" <c:if test="${list.orgId == afterSalesFollowUpRecord.orgId}">selected="selected"</c:if>>${list.orgName}</option>
                        </c:forEach>
                    </select>
                </div>
            </li>
            
            <li>
                <div class="infor_name">
                    <lable>待操作事项：</lable>
                </div>
                <div class="f_left">
                    <input type="text" style="width: 450px;" name="operationalMatters" id="operationalMatters" placeholder="请输入待操作事项" maxlength="200" value="${afterSalesFollowUpRecord.operationalMatters}">
                    <div id="operationalMattersError"></div>
                </div>
            </li>
            
          <li>
            <div class="infor_name">
                 <label style="color: red">*</label>
                <label>下次跟进时间：</label>
            </div>
            <div class="f_left">
                <input class="Wdate input-large" type="text"
							placeholder="请选择日期"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" autocomplete="off"
							name="nextTime" id="nextTime" 
                       value="<fmt:formatDate value='${afterSalesFollowUpRecord.nextTime}' pattern='yyyy-MM-dd' />" />
                <input type="checkbox" id="isOverCheckBox" name="isOverCheckBox" style="margin-left: 20px" <c:if test="${afterSalesFollowUpRecord.isOver == 1}">checked="checked"</c:if>>
                <label for="isOverCheckBox">已完结，无下次跟进</label>
                
                <div id="nextTimeError"></div>
            </div>
        </li>

        </ul>
        <div class="add-tijiao tcenter">
            <input type="hidden" name="isOver" id="isOver" value="0">
            <input type="hidden" name="formToken" value="${formToken}"/>
            <input type="hidden" name="afterSalesId" value="${afterSalesFollowUpRecord.afterSalesId}">
            <input type="hidden" name="recordId" value="${afterSalesFollowUpRecord.recordId}">
            <button class="dele" id="close-layer" type="button"  style="background-color: #f5f7fa;border-color: #ced2d9;color: black;">取消</button>
            <button type="submit" id="submit" style="background-color: #3384ef;border-color: #fff;">确定</button>
        </div>
    </form>
</div>
<%@ include file="../../common/footer.jsp"%>
