package com.wms.controller;

import cn.hutool.core.collection.CollUtil;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Position;
import com.vedeng.authorization.model.Role;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.system.service.OrgService;
import com.vedeng.system.service.RoleService;
import com.vedeng.system.service.UserService;
import com.wms.constant.VerifyStatusEnum;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WmsInventoryOutService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 盘亏出库单
 * @date 2022/9/23 11:11
 **/
@Controller
@Slf4j
@RequestMapping("/wms/inventoryOut")
public class WMSInventoryOutController extends BaseController {

    public static final String INVENTORY_OUT_AUDIT = "inventoryOutAudit";
    @Autowired
    private OrgService orgService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private WmsInventoryOutService wmsInventoryOutService;

    @Autowired
    private UserService userService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private RoleService roleService;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private WarehouseOutService warehouseOutService;


    /**
     * 盘亏出库单
     *
     * @param session session
     * @return ModelAndView
     */
    @RequestMapping("/inventoryOutAdd")
    public ModelAndView inventoryOutAdd(HttpSession session) {

        ModelAndView mv = new ModelAndView();
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        // 获取申请部门
        List<Organization> orgList = orgService.getOrgList(0, user.getCompanyId(), true);

        mv.addObject("orgList", orgList);
        mv.addObject("today", DateUtil.convertString(System.currentTimeMillis(), "yyyy-MM-dd"));
        mv.setViewName("wms/inventoryOut/inventoryOutAdd");
        return mv;
    }

    @RequestMapping(value = "/logicalSearchGoods")
    @NoNeedAccessAuthorization
    public ModelAndView logicalSearchGoods(HttpServletRequest request,
                                           @RequestParam(value = "searchType", required = false) String searchType,
                                           @RequestParam(value = "searchContent", required = false) String searchContent,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                           @RequestParam(value = "callbackFuntion", required = false) String callbackFuntion,
                                           @RequestParam(value = "logicalId", required = false) Integer logicalId) {
        ModelAndView mv = new ModelAndView();
        if (StringUtils.isNoneBlank(searchContent)) {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            Page page = getPageTag(request, pageNo, pageSize);
            Goods goods = new Goods();
            goods.setCompanyId(user.getCompanyId());
            if (StringUtil.isNotBlank(searchType)) {
                List<String> skuList = Arrays.asList(searchContent.split(","));
                goods.setGoodsType(Integer.valueOf(searchType));
                goods.setGoodsIdArr(skuList);
            } else {
                goods.setSearchContent(searchContent);
            }

            Map<String, Object> map = goodsService.getAllLogicallistGoodsStockPage(goods, page, user);
            mv.addObject("goodsList", map.get("goodsList"));
            mv.addObject("page", map.get("page"));
            mv.addObject("searchContent", searchContent);
        }
        mv.addObject("callbackFuntion", callbackFuntion);
        mv.addObject("logicalId", logicalId);
        mv.setViewName("wms/inventoryOut/logicalSearchGoods");
        return mv;

    }

    /**
     * 保存盘库出库单
     *
     * @param request        request
     * @param wmsOutputOrder wmsOutputOrder
     * @param session        session
     * @return ModelAndView
     */
    @RequestMapping("/saveInventoryOutOrder")
    public ModelAndView saveInventoryOutOrder(HttpServletRequest request, WmsOutputOrder wmsOutputOrder, HttpSession session) {

        ModelAndView mv = new ModelAndView();
        try {
            User user = (User) session.getAttribute(ErpConst.CURR_USER);
            wmsOutputOrder.setCreator(user.getUsername());
            wmsOutputOrder.setUpdator(user.getUsername());
            WmsOutputOrder resultInfo = wmsInventoryOutService.saveInventoryOutOrder(wmsOutputOrder);
            if (Objects.nonNull(resultInfo)&&resultInfo.getId()!=null&&resultInfo.getId()!=0) {
                startProcessInstance(request, resultInfo.getId());
                mv.addObject("url", "/wms/inventoryOut/details.do?inventoryOutOrderId=" + resultInfo.getId());
                mv.addObject("refresh", "true_false_true");
                return success(mv);
            }
        } catch (Exception e) {
            log.error("saveInventoryOutOrder  error", e);
        }
        return fail(mv);
    }

    /**
     * 审核流
     *
     * @param request          请求
     * @param wmsOutPutOrderId 盘亏出库单id
     * @throws Exception 异常
     */
    private void startProcessInstance(HttpServletRequest request, Long wmsOutPutOrderId) throws Exception {

        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<>();

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        int roleflag = 0;
        List<Position> positionList = userService.getPositionByUserId(user.getUserId());
        for (Position position : positionList) {
            if (position.getPositionName().contains("经理") || position.getPositionName().contains("主管")) {
                roleflag = 1;
            }
            if (position.getPositionName().contains("总监")) {
                roleflag = 2;
                variableMap.put("pass", true);
            }
            if (position.getPositionName().contains("财务总监")) {
                roleflag = 3;
                variableMap.put("pass", true);
                break;
            }
        }
        variableMap.put("processDefinitionKey", INVENTORY_OUT_AUDIT);
        variableMap.put("businessKey", INVENTORY_OUT_AUDIT + "_" + wmsOutPutOrderId);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("currentAssineeId", user.getOrgId());
        variableMap.put("wmsOutPutOrderId", wmsOutPutOrderId);
        variableMap.put("roleflag", roleflag);

        WmsOutputOrder wmsOutputOrder = this.wmsInventoryOutService.getInventoryOutById(wmsOutPutOrderId);
        variableMap.put("orderNo", wmsOutputOrder.getOrderNo());

        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);

        // 获取当前活动节点
        Task taskInfo = processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                .singleResult();

        //完成申请的任务
        actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);
    }

    /**
     * 盘亏出库单详情也
     *
     * @param session             session
     * @param inventoryOutOrderId 盘亏出库单id
     * @return Mv
     */
    @RequestMapping("/details")
    public ModelAndView applyIndex(HttpSession session, Long inventoryOutOrderId) {
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        WmsOutputOrder wmsOutputOrder = wmsInventoryOutService.getInventoryOutById(inventoryOutOrderId);
        List<WmsOutputOrderGoodsDto> wmsOutputOrderGoodsList = wmsInventoryOutService.getWmsInputOrderGoodsDto(inventoryOutOrderId);


        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, INVENTORY_OUT_AUDIT + "_" + inventoryOutOrderId);
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");

            mv.addObject("taskId", taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());

                mv.addObject("verifyUsers", org.apache.commons.lang.StringUtils.join(userNameList, ","));
            }
        }
        Boolean cancelflag = cancelflag(wmsOutputOrder, user);

        List<Integer> skuIds = new ArrayList<>();
        for (WmsOutputOrderGoods wmsOutputOrderGood : wmsOutputOrderGoodsList) {
            String substring = wmsOutputOrderGood.getSkuNo().substring(1);
            skuIds.add(Integer.valueOf(substring));
        }
        if (!CollectionUtils.isEmpty(skuIds)) {
            List<Map<String, Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }

        List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getInventoryOutOrderDetail(inventoryOutOrderId);

        mv.addObject("warehouseOutList", warehouseOutList);

        mv.addObject("cancelflag", cancelflag);
        mv.addObject("curr_user", user);
        mv.addObject("wmsOutputOrderGoodsList", wmsOutputOrderGoodsList);
        mv.addObject("wmsOutputOrder", wmsOutputOrder);
        mv.setViewName("wms/inventoryOut/inventoryOutDetails");
        return mv;
    }

    /**
     * 取消按钮显示
     *
     * @param wmsOutputOrder data
     * @param user           user
     * @return Boolean
     */
    private Boolean cancelflag(WmsOutputOrder wmsOutputOrder, User user) {
        boolean result = true;
        if (wmsOutputOrder.getCreator() == null) {
            result = false;
        } else if (user.getUserId() == null) {
            result = false;
        } else if (!wmsOutputOrder.getCreator().equalsIgnoreCase(user.getUsername())) {
            result = false;
        }
        if (VerifyStatusEnum.COLSED.getValue() == wmsOutputOrder.getVerifyStatus()) {
            result = false;
        }
        if (VerifyStatusEnum.Approved.getValue() == wmsOutputOrder.getVerifyStatus()) {
            result = false;
        }
        if (VerifyStatusEnum.Reviewing.getValue() == wmsOutputOrder.getVerifyStatus()) {
            result = false;
        }
        return result;
    }

    /**
     * 校验库存
     * @param check 数据
     * @return ResultInfo
     */
    @RequestMapping(value = "/checkData", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<?> checkData(@RequestBody List<WmsOutputOrderGoods> check) {
        try {

            if (CollUtil.isNotEmpty(check)) {
                wmsInventoryOutService.checkData(check);
            }
            return new ResultInfo(0, "校验成功");

        } catch (Exception e) {
            logger.error("checkData:", e);
            return new ResultInfo(-1, "校验失败");
        }
    }
    /**
     * 校验盘盈入库单审核人信息
     *
     * @param request res
     * @return ResultInfo
     */
    @RequestMapping(value = "/validateApplyerInfo", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<?> validateApplyerInfo(HttpServletRequest request) {
        try {
            User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

            Integer applyerUserid = user.getUserId();
            Integer applyerDepartmentId = user.getOrgId();

            if (currentUserHasRoleName(applyerUserid, "总监")) {
                return new ResultInfo(0, "校验成功");
            }

            if (currentUserHasRoleName(applyerUserid, "经理")) {

                List<User> userList = userService.getUserByPositLevel(applyerDepartmentId, 442);
                if (CollUtil.isNotEmpty(userList)) {
                    List<User> collect = userList.stream().filter(c -> c.getIsDisabled().equals(0)).collect(Collectors.toList());
                    if (CollUtil.isEmpty(collect)) {
                        return new ResultInfo(-1, "当前用户没有对应的总监");
                    } else {
                        return new ResultInfo(0, "校验成功");
                    }
                } else {
                    return new ResultInfo(-1, "当前用户没有对应的总监");
                }

            }

            List<User> userList = userService.getUserByPositLevel(applyerDepartmentId, 609);
            if (CollUtil.isNotEmpty(userList)) {
                List<User> collect = userList.stream().filter(c -> c.getIsDisabled().equals(0)).collect(Collectors.toList());
                if (CollUtil.isEmpty(collect)) {
                    return new ResultInfo(-1, "当前用户没有对应的经理");
                }
            } else {
                return new ResultInfo(-1, "当前用户没有对应的经理");
            }


            return new ResultInfo(0, "校验成功");

        } catch (Exception e) {
            logger.error("validateApplyerInfo:", e);
            return new ResultInfo(-1, "校验失败");
        }
    }

    /**
     * 判断当前用户是否有传入的角色
     *
     * @param userID 角色id
     * @return boolean
     */
    private boolean currentUserHasRoleName(Integer userID, String roleName) {

        AtomicBoolean flag = new AtomicBoolean(false);

        List<Role> roles = roleService.getUserRoles(userID);
        if (CollectionUtils.isEmpty(roles)) {
            return false;
        }

        roles.forEach(role -> {
            if (role.getRoleName().contains(roleName)) {
                flag.set(true);
            }
        });

        return flag.get();
    }

    /**
     * 页面审核
     *
     * @param taskId
     * @param pass
     * @param wmsOutputOrderId
     * @return
     */
    @RequestMapping(value = "/auditResult")
    @NoNeedAccessAuthorization
    public ModelAndView auditResult(String taskId, Boolean pass, Integer wmsOutputOrderId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("wmsOutputOrderId", wmsOutputOrderId);
        mv.setViewName("wms/inventoryOut/auditResult");
        return mv;
    }

    /**
     * 盘亏出库审核操作
     *
     * @param request
     * @param wmsOutputOrderId
     * @param taskId
     * @param comment
     * @param pass             是否通过
     * @param session
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/complementTask")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementTask(HttpServletRequest request, Integer wmsOutputOrderId, String taskId, String comment, Boolean pass,
                                        HttpSession session) {

        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);

        try {
            //完成当前任务，工作流往下走
            Map<String, Object> taskVaribles = new HashMap<String, Object>();
            taskVaribles.put("pass", pass);

            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment,
                    user.getUsername(), taskVaribles);

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("complementTask:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    /**
     * 取消订单
     *
     * @return
     */
    @RequestMapping(value = "/closeInventoryOutOrder", method = RequestMethod.POST)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo closeSurplusInOrder(Long inventoryOutOrderId) {
        try {

            WmsOutputOrder wmsOutputOrder = wmsInventoryOutService.getInventoryOutById(inventoryOutOrderId);

            if (wmsOutputOrder.getVerifyStatus() == VerifyStatusEnum.Approved.getValue()) {
                return new ResultInfo(-1, "盘盈单已经审核通过，不允许关闭");
            }

            wmsInventoryOutService.updateInventoryOutOrderAuditStatus(inventoryOutOrderId, VerifyStatusEnum.COLSED.getValue());

            //如果是审核中,就删除现在的流程实例
            if (VerifyStatusEnum.Reviewing.getValue() == wmsOutputOrder.getVerifyStatus()) {

                // 获取当前活动节点
                Task taskInfo = processEngine.getTaskService()
                        .createTaskQuery()
                        .processInstanceBusinessKey(INVENTORY_OUT_AUDIT + "_" + inventoryOutOrderId)
                        .singleResult();

                if (taskInfo != null) {
                    //删除流程实例
                    actionProcdefService.deleteProcessInstance(taskInfo.getId());
                }
            }

            return new ResultInfo(0, "操作成功");

        } catch (Exception e) {
            logger.error("closeInventoryOutOrder:", e);
            return new ResultInfo(-1, "操作失败");
        }
    }
}
