package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.system.SystemUtil;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDetailDto;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.SaleOrderExpenseExcelDto;
import com.vedeng.erp.kingdee.batch.repository.BatchInvoiceDetailDtoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 21-22 历史销售蓝票根据Excel判断是否是虚拟商品
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/6/5 14:28
 */
@Service
@Slf4j
public class BatchHistorySaleBlueInvoiceCompositeProcessor implements ItemProcessor<BatchInvoiceDto, BatchInvoiceDto> {

    @Autowired
    private BatchInvoiceDetailDtoMapper batchInvoiceDetailDtoMapper;

    private Map<String, List<SaleOrderExpenseExcelDto>> excelDtoList = new LinkedHashMap<>();

    private final static String FILE_PATH = SystemUtil.getOsInfo().isLinux() ? "/tmp/销售费用订单21-22年.xlsx" : "C:/Users/<USER>/Desktop/销售费用订单21-22年.xlsx";

    @Override
    public BatchInvoiceDto process(BatchInvoiceDto batchInvoiceDto) throws Exception {
        // 处理 解析 excel
        boolean hasFile = FileUtil.isFile(FILE_PATH);
        // 无文件提醒
        if (!hasFile) {
            return batchInvoiceDto;
        }

        if (CollUtil.isEmpty(excelDtoList)) {
            try {
                ExcelReader reader = ExcelUtil.getReader(FILE_PATH, 0);
                List<SaleOrderExpenseExcelDto> saleOrderExpenseExcelDtoList = reader.readAll(SaleOrderExpenseExcelDto.class);
                excelDtoList = saleOrderExpenseExcelDtoList.stream().collect(Collectors.groupingBy(SaleOrderExpenseExcelDto::getSaleOrderNo));
            } catch (Exception e) {
                log.error("解析21-22销售费用Excel异常:", e);
                return null;
            }
        }

        List<BatchInvoiceDetailDto> invoiceDetailList = batchInvoiceDetailDtoMapper.getSaleOrderInvoiceDetailList(batchInvoiceDto.getInvoiceId());
        List<SaleOrderExpenseExcelDto> expenseExcelList = excelDtoList.get(batchInvoiceDto.getOrderNo());
        if (CollUtil.isNotEmpty(expenseExcelList)) {
            // 在excel中，是费用，此时遍历发票明细并给每个明细行打赠品标记
            invoiceDetailList.forEach(detail -> {
                Optional<SaleOrderExpenseExcelDto> first = expenseExcelList.stream().filter(excel -> detail.getSku().equals(excel.getSku())).findFirst();
                if (first.isPresent()) {
                    detail.setIsVirtureSku(1);
                } else {
                    // 这边必须要手动再次赋值是否虚拟商品，因为getSaleOrderInvoiceDetailList方法会查出来这个字段，要以excel中的为准
                    detail.setIsVirtureSku(0);
                }
            });
        } else {
            // 若该销售单不在Excel中，需要手动将该销售单商品设置为全实物
            invoiceDetailList.forEach(item -> item.setIsVirtureSku(0));
        }
        batchInvoiceDto.setBatchInvoiceDetailDtoList(invoiceDetailList);
        return batchInvoiceDto;
    }
}