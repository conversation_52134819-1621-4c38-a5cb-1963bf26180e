package com.vedeng.common.shiro.cas;

import com.vedeng.common.shiro.constant.SecurityConstants;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.web.filter.AccessControlFilter;
import org.apache.shiro.web.util.WebUtils;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;

/**
 * <AUTHOR> [<EMAIL>]
 */
public class CustomUserFilter extends AccessControlFilter {

    public CustomUserFilter() {
        setLoginUrl(SecurityConstants.LOGIN_URL);
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response, Object mappedValue) throws Exception {
        boolean loginRequest = isLoginRequest(request, response);
        Subject subject = getSubject(request, response);
        if (loginRequest && subject.isAuthenticated()) {
            // 如果时上次登录cookie未失效，直接进入请求成功默认路径
            WebUtils.issueRedirect(request, response, SecurityConstants.INDEX_URL);
            return false;
        }
        return true;
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response) throws Exception {
        return false;
    }

}
