package com.vedeng.erp.aftersale.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecord;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDetailDto;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;


@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface AfterSalesInstallServiceRecordDetailConvertor extends BaseMapStruct<AfterSalesInstallServiceRecordDetail, AfterSalesInstallServiceRecordDetailDto> {
}
