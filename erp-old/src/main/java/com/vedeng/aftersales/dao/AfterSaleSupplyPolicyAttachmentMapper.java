package com.vedeng.aftersales.dao;

import com.vedeng.aftersales.model.AfterSaleSupplyPolicyAttachment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSaleSupplyPolicyAttachmentMapper {

    int deleteByPrimaryKey(Long supplyPolicyAttachmentId);

    int insertSelective(AfterSaleSupplyPolicyAttachment record);

    AfterSaleSupplyPolicyAttachment selectByPrimaryKey(Long supplyPolicyAttachmentId);

    int updateByPrimaryKeySelective(AfterSaleSupplyPolicyAttachment record);

    void batchInsertAttashment(List<AfterSaleSupplyPolicyAttachment> list);

    List<AfterSaleSupplyPolicyAttachment> getSupplyAfterSalePolicyAttashMent(@Param("supplyPolicyId") Long supplyPolicyId);

    void deleteBySupplyPolicyId(Long supplyPolicyId);
}