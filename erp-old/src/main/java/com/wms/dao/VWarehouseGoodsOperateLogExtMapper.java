package com.wms.dao;

import com.wms.dao.generate.VWarehouseGoodsOperateLogMapper;
import com.wms.model.ddi.VWarehouseGoodsOperateLogExtDto;
import com.wms.model.dto.GoodsStockDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface VWarehouseGoodsOperateLogExtMapper extends VWarehouseGoodsOperateLogMapper {

    List<VWarehouseGoodsOperateLogExtDto> getAllTodayOrder(@Param("skuIdList") List<Integer> skuIdList, @Param("startTime") long startTime, @Param("endTime") long endTime);

    List<GoodsStockDto> getSingleOrder(VWarehouseGoodsOperateLogExtDto VWarehouseGoodsOperateLogExtDto);
}