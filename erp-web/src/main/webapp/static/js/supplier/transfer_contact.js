$(function() {
	
	$("#search").click(function(){
		checkLogin();
		$(".warning").remove();
		$("input").removeClass("errorbor");
		var name=$("#searchName").val();
		if(name == ''){
			//warnTips("name","名称为空不能搜索");
			warnErrorTips("searchName","supplierNameError","查询条件不允许为空");
			return  false;
		}
		$("#myform").submit();
	})
	
	$("#submit").click(function(){
		checkLogin();
		var traderId=$("input[name='traderId']").val();
		
		if(traderId==''){
			layer.msg("请选择供应商");
			return  false;
		}
		var contactId=$("#contactId").val();
		var traderType=$("#traderType").val();
		var traderSupplierId=$("#traderSupplierId").val();
		var url ='';
		if(traderType==1){
			url = page_url+'/trader/customer/transferContact.do';
		}else{
			url = page_url+'/trader/supplier/transferContact.do';
		}
		$.ajax({
			url:url,
			data:{'traderContactId':contactId,'traderId':traderId,'traderType':traderType,'name':$("input[name='name']").val(),'mobile':$("input[name='mobile']").val()},
			type:"POST",
			dataType : "json",
			async: false,
			success:function(data)
			{
				if(data.code==0){
					if(traderType==2){
						var str=page_url+"/trader/supplier/getContactsAddress.do?traderId="+traderId+"&traderSupplierId="+traderSupplierId;
						$(window.parent.document).attr('src','').attr('src',str)
						layer.closeAll();
					}else {
						window.parent.location.reload();
					}
				}else{
					layer.alert(data.message);
				}
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
		return false;
	})
	
	
	
});

function selectObj(traderId,supplierName){
	checkLogin();
	$("#cus").addClass("none");
	$("#searchName").addClass("none");
	$("#search").addClass("none");
	$("#research").removeClass("none");
	$("input[name='traderId']").val(traderId);
	$("#supplierName").removeClass("none").html(supplierName);
}

function research(){
	checkLogin();
	$("#cus").removeClass("none");
	$("#searchName").removeClass("none");
	$("#search").removeClass("none");
	$("#research").addClass("none");
	$("#supplierName").addClass("none")
}
