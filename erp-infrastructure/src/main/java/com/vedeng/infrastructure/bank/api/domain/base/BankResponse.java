package com.vedeng.infrastructure.bank.api.domain.base;

import lombok.Data;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-07-31
 * @Description: 民生银行接口通用返回体
 * @Version: 1.0
 */
@Data
public class BankResponse {

    public static final String SUCCESS = "0";
    /**
     * 响应头
     */
    private ResponseHeader responseHeader;

    /**
     * 程序错误，接口返回封装
     *
     * @param response
     * @param errorMessage
     */
    public void checkAndSetResponseHeader(BankResponse response, String errorMessage) {
        if (response.getResponseHeader() == null || response.getResponseHeader().getStatus() == null) {
            ResponseHeader.Status status = new ResponseHeader.Status();
            status.setCode("-1");
            status.setMessage(errorMessage);
            ResponseHeader responseHeader = new ResponseHeader();
            responseHeader.setStatus(status);
            response.setResponseHeader(responseHeader);
        }
    }


    /**
     * 调用接口，传入参数有误
     *
     * @param response
     */
    public void checkParamResponseHeader(BankResponse response, String msg) {
        ResponseHeader.Status status = new ResponseHeader.Status();
        status.setCode("-1");
        status.setMessage(msg);
        ResponseHeader responseHeader = new ResponseHeader();
        responseHeader.setStatus(status);
        response.setResponseHeader(responseHeader);
    }

    /**
     * 返回报文封装
     */
    public static BankResponse generateResponseHeader(String code, String msg) {
        BankResponse response = new BankResponse();
        ResponseHeader.Status status = new ResponseHeader.Status();
        status.setCode(code);
        status.setMessage(msg);
        ResponseHeader responseHeader = new ResponseHeader();
        responseHeader.setStatus(status);
        response.setResponseHeader(responseHeader);
        return response;
    }

    public void fail(BankResponse response, String errorMessage) {
        ResponseHeader.Status status = new ResponseHeader.Status();
        status.setCode("-1");
        status.setMessage(errorMessage);
        ResponseHeader responseHeader = new ResponseHeader();
        responseHeader.setStatus(status);
        response.setResponseHeader(responseHeader);
    }

    public boolean isSuccess() {
        return SUCCESS.equals(responseHeader.getStatus().getCode());
    }

    public boolean isFail() {
        return !isSuccess();
    }

    public String getMessage() {
        return responseHeader.getStatus().getMessage();
    }

}
