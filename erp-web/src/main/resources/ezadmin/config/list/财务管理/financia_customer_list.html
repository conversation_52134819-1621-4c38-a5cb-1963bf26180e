<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>客户列表（财务专用）</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="financia_customer_list" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>客户名称：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder=""  oper="like"
                       name="TRADER_NAME" empty_show="-" >
            </div>
        </div>

        <div class="layui-inline ">
            <label>客户ID：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder=""  oper="EQ"
                       name="A.TRADER_ID" empty_show="-" maxlength="50">
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">归属销售:</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="21" alias="D" name="USER_ID" placeholder="全部" style="" jdbctype=""
                        data="
select DISTINCT a.USER_ID k,
                lower( a.USERNAME) V
from T_USER a
         left join
     T_R_USER_POSIT b on a.USER_ID = b.USER_ID
         left join
     T_POSITION d ON d.POSITION_ID = b.POSITION_ID
         left join
     T_USER_DETAIL c on a.USER_ID = c.USER_ID
where 1 = 1

  and d.TYPE IN (310)
  and a.IS_DISABLED = false

ORDER BY a.USERNAME" datatype="KVSQLCACHE" oper="EQ"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">归属平台：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="BELONG_PLATFORM" alias="B" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"贝登医疗"},{"K":"2","V":"医械购"},{"K":"3","V":"科研购"},{"K":"5","V":"其他"},{"K":"6","V":"集采"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">客户性质：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="CUSTOMER_NATURE" alias="A.CUSTOMER_NATURE" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"直接客户"},{"K":"2","V":"间接客户"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">终端客户分类：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="CUSTOMER_CLASS" alias="A" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"公立"},{"K":"2","V":"民营个体"},{"K":"3","V":"民营集团"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class="layui-inline ">
            <label>所属集团：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="全部"  oper="like"
                       name="GROUP_NAME" empty_show="-" maxlength="50">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">客户细分类：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="CUSTOMER_SECOND_TYPE" alias="A" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"医疗卫生机构"},{"K":"2","V":"非医疗卫生机构"},{"K":"3","V":"分销商"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">医疗机构分类：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="CUSTOMER_THIRD_TYPE" alias="A" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"医院"},{"K":"2","V":"基层医疗卫生机构"},{"K":"3","V":"专业医疗卫生机构"},{"K":"4","V":"其他医疗卫生机构"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">医院等级：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="HOSPITAL_LEVER" alias="A" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"一级"},{"K":"2","V":"二级"},{"K":"3","V":"三级"},{"K":"4","V":"未定级"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class="layui-inline ">
            <label>所属医院共体：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder=""  oper="like"
                       name="HOSPITAL_NAME" empty_show="-" maxlength="50">
            </div>
        </div>
        <div class="layui-inline ">
            <label>创建时间：</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="DATETIME" type="search"
                        placeholder="" name="ADD_TIME" alias = "FROM_UNIXTIME(C.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s')" oper="BETWEEN"></object>
            </div>
        </div>
        <div class="layui-inline ">
            <label>推送金蝶时间：</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="DATETIME" type="search"
                        placeholder="" name="PUSH_TIME" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">客户类型：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="CUSTOMER_TYPE" alias = "C.CUSTOMER_NATURE" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"465","V":"分销"},{"K":"466","V":"终端"}]'
                        datatype="json"></select>
            </div>
        </div>
        <div class="layui-inline ">
            <label>客户名称(新)：</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder=""  oper="like"
                       name="CUSTOMER_NAME_NEW" empty_show="-" maxlength="50">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否推送金蝶：</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="IS_PUSH" alias = "A" jdbctype="INTEGER" placeholder="全部"
                        data='[{"K":"1","V":"是"},{"K":"0","V":"否"}]'
                        datatype="json"></select>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">首次交易时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" name="FIRST_TRADER_TIME" placeholder="" style="" alias="from_unixtime((G.FIRST_TRADER_TIME)/1000 ,'%Y-%m-%d %H:%i:%s')" jdbctype="" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">

    <div class="btn-group bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/traderFinancia/toUploadExcelPage.do" opentype="MODEL" area="600px,400px" windowname="批量修改客户信息">批量处理</button>
        <button item_name="导出" name="导出" url="/ezadmin/list/export-financia_customer_list" opentype="_BLANK_PARAM_COLUMN" windowname="" style type="table">导出</button>
    </div>

    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th name="TRADER_ID" >客户ID</th>
            <th name="TRADER_NAME">客户名称</th>
            <th name="CUSTOMER_TYPE">客户类型</th>
            <th name="CUSTOMER_NAME_NEW">客户名称(新)</th>
            <th name="CUSTOMER_NATURE">客户性质</th>
            <th name="CUSTOMER_CLASS">终端客户分类</th>
            <th name="GROUP_NAME">所属集团</th>
            <th name="CUSTOMER_SECOND_TYPE">客户细分类</th>
            <th name="CUSTOMER_THIRD_TYPE">医疗机构分类</th>
            <th name="HOSPITAL_NAME">所属医院共体</th>
            <th name="ADD_TIME">创建时间</th>
            <th name="PUSH_TIME">推送金蝶时间</th>
            <th name="IS_PUSH">是否推送金蝶</th>
            <th name="USERNAME" >客户归属销售</th>
            <th name="FIRST_TRADER_TIME">首次交易时间</th>

            <th type="rowbutton" id="rowbutton">
                <button class="layui-btn list-row-button" type="single" opentype="MODEL"
                        url="/traderFinancia/edit.do?traderCustomerFinanceId=${TRADER_CUSTOMER_FINANCE_ID}" windowname="修改" area="600px,600px"
                        name="update">修改
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by A.MOD_TIME desc" groupby="">

select
	A.TRADER_CUSTOMER_FINANCE_ID,
	A.TRADER_ID,
	B.TRADER_NAME,
    B.BELONG_PLATFORM,
	case
		C.CUSTOMER_NATURE when '465' then '分销'
		when '466' then '终端'
	end as CUSTOMER_TYPE,
	CASE
		WHEN (ISNULL(A.CUSTOMER_NAME_NEW)= 1) || (LENGTH(trim(A.CUSTOMER_NAME_NEW))= 0) THEN '-'
		ELSE A.CUSTOMER_NAME_NEW
	END AS CUSTOMER_NAME_NEW,
	case
		A.CUSTOMER_NATURE when '1' then '直接客户'
		when '2' then '间接客户'
		ELSE '-'
	end as CUSTOMER_NATURE,
	case
		A.CUSTOMER_CLASS when '1' then '公立'
		when '2' then '民营个体'
		when '3' then '民营集团'
		ELSE '-'
	end as CUSTOMER_CLASS,
	CASE
		WHEN (ISNULL(A.GROUP_NAME)= 1) || (LENGTH(trim(A.GROUP_NAME))= 0) THEN '-'
		ELSE A.GROUP_NAME
	END AS GROUP_NAME,
	case
		A.CUSTOMER_SECOND_TYPE when '1' then '医疗卫生机构'
		when '2' then '非医疗卫生机构'
		when '3' then '分销商'
		ELSE '-'
	end as CUSTOMER_SECOND_TYPE,
	case
		A.CUSTOMER_THIRD_TYPE when '1' then '医院'
		when '2' then '基层医疗卫生机构'
		when '3' then '专业医疗卫生机构'
		when '4' then '其他医疗卫生机构'
		ELSE '-'
	end as CUSTOMER_THIRD_TYPE,
	CASE
		WHEN (ISNULL(A.HOSPITAL_NAME)= 1) || (LENGTH(trim(A.HOSPITAL_NAME))= 0) THEN '-'
		ELSE A.HOSPITAL_NAME
	END AS HOSPITAL_NAME,
	from_unixtime(C.ADD_TIME / 1000, '%Y-%m-%d %H:%i:%s') ADD_TIME,
	case
		A.IS_PUSH when '1' then DATE_FORMAT(A.PUSH_TIME, '%Y-%m-%d %H:%i:%s')
		when '0' then '-'
	end as PUSH_TIME,
	case
		A.IS_PUSH when '1' then '是'
		when '0' then '否'
	end as IS_PUSH,
	D.USER_ID,
    F.USERNAME,
    FROM_UNIXTIME(G.FIRST_TRADER_TIME/1000,'%Y-%m-%d %H:%i:%s') AS FIRST_TRADER_TIME,
	DATE_FORMAT(A.MOD_TIME, '%Y-%m-%d %H:%i') MOD_TIME
from
	T_TRADER_CUSTOMER_FINANCE A
left join T_TRADER B on A.TRADER_ID = B.TRADER_ID
left join T_TRADER_CUSTOMER C on A.TRADER_ID = C.TRADER_ID
left join T_R_TRADER_J_USER D on  D.TRADER_ID = A.TRADER_ID and D.TRADER_TYPE = 1
LEFT JOIN  T_VERIFIES_INFO E ON E.RELATE_TABLE_KEY = C.TRADER_CUSTOMER_ID AND E.RELATE_TABLE = 'T_TRADER_CUSTOMER' and E.VERIFIES_TYPE = 617
LEFT JOIN T_USER F on F.USER_ID = D.USER_ID
LEFT JOIN T_TRADER_DATA G on G.TRADER_ID = A.TRADER_ID  and G.FIRST_TRADER_TIME > 0
where
	1 = 1
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>