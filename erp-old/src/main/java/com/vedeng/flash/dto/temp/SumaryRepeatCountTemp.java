package com.vedeng.flash.dto.temp;

import com.vedeng.authorization.model.User;
import lombok.*;

import java.util.Map;

/**
 * @program: erp.vedeng.com
 * @description: 统计重复数量
 * @author: Pusan
 * @create: 2021-05-24 13:55
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SumaryRepeatCountTemp {

    /** 重复数量 */
    private Integer repeatCount;

    /** 当前用户 */
    private User currentUser;

    /**
     * 安全库存
     */
    // private Map<String, Integer> skuNoSafeStockMap;
}
