
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<!DOCTYPE html>
<html lang="en">
<head>
  <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
  <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions"%>
  <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt"%>
  <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags"%>
  <%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>
  <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
  <%@ taglib uri="http://com.vedeng.common.util/definetags" prefix="define"%>
  <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form"%>

  <%@ page trimDirectiveWhitespaces="true" %>

  <%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
  %>
  <c:set var="path" value="<%=basePath%>" scope="application" />
  <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
  <meta name="renderer" content="webkit|ie-comp|ie-stand">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
  <meta http-equiv="Cache-Control" content="no-siteapp" />
  <title>出库单</title>
  <!--  <link rel="stylesheet" href="<%=basePath%>static/css/content.css">-->
  <link rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}" />
  <link rel="stylesheet" href="<%=basePath%>static/css/manage.css" />
  <!-- 模糊搜索下拉框css引入 -->
  <link rel="stylesheet" href="<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.css?rnd=${resourceVersionKey}" />

  <script type="text/javascript" src='<%=basePath%>static/js/jquery.min.js'></script>
  <script type="text/javascript" src="<%=basePath%>static/js/jquery/validation/jquery-form.js"></script>
  <script type="text/javascript" src='<%=basePath%>static/libs/jquery/plugins/layer/layer.js'></script>
  <script type="text/javascript" src="<%=basePath%>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
  <script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/form.js?rnd=${resourceVersionKey}'></script>
  <script type="text/javascript" charset="UTF-8" src='<%=basePath%>static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
  <script type="text/javascript" src='<%=basePath%>static/js/common.js?rnd=${resourceVersionKey}'></script>
  <script type="text/javascript" src="<%=basePath%>static/js/call/call.js?rnd=${resourceVersionKey}"></script>
  <script type="text/javascript" src='<%= basePath %>static/js/movinghead.js?rnd=${resourceVersionKey}'></script>
  <!-- 模糊搜索下拉框js引入 -->
  <script type="text/javascript" src='<%=basePath%>static/libs/searchableSelect/jquery.searchableSelect.js?rnd=${resourceVersionKey}'></script>

</head>
<body>

<link rel="stylesheet"
      href="<%=basePath%>static/css/print_out_order.css?rnd=${resourceVersionKey}" />

<style type="text/css">
  .red {
    color: red;
  }

  * {
    font-family: Arial;
  }

  /** {
      font-weight: bold;
  }
  table th, table td{
      font-weight: bold;
  }*/

  .bold {
    font-weight: bold;
  }
</style>
<div id="printdiv" style="margin-left: 0">
  <input type="hidden"  id="printAllOrder" value="${printAllOrder}">


  <c:forEach var="confirmationPrintDto" items="${confirmationPrintDtos}" varStatus="status">
    <table cellpadding="0" cellspacing="0" width="650" border="0">
      <tbody>
      <tr>
        <td>
          <!-- header -->
          <table cellpadding="0" cellspacing="0" width="100%" border="0"
                 height="90" style="padding: 0px;">
            <tbody>
            <tr>
              <td class="align_c"
                  style="line-height: 30px; font-size: 17px; font-family: Arial; font-weight: bold;">
                出库单
              </td>
            </tr>
            <tr>
              <th colspan="2" class="align_l">&nbsp;</th>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      <tr>
        <td valign="top" align="center">
          <table cellpadding="0" cellspacing="0" width="650" border="0"
                 align="center">
            <tbody>
            <tr height="17">
              <td style="line-height: 18px;width: 80px;" ><b>客户名称：</b></td>
              <td class="align_l" style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${afterSalesDetail.traderName}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.traderName}
                </c:if>
              </td>
              <td style="line-height: 18px;width: 70px;" ><b><c:choose><c:when test="${confirmationPrintDto.type == 2}">发货单号：</c:when><c:otherwise>合同单号：</c:otherwise></c:choose></b></td>

              <td class="align_l" style="line-height: 18px;width: 180px;" nowrap="">${confirmationPrintDto.bussinessNo}</td>
              <td style="line-height: 18px;width: 80px;" ><b>发货日期：</b></td>
              <td style="line-height: 18px;width:155px;" >
                <date:date value="${confirmationPrintDto.currTime}" />
              </td>
            </tr>
            <tr>
              <td style="line-height: 18px;width: 80px;" ><b>收货地址：</b></td>
              <td style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${afterSalesDetail.address}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.takeTraderAddress}
                </c:if>
              </td>
              <td style="line-height: 18px;width: 80px;" ><b>联系人：</b></td>
              <td style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${confirmationPrintDto.afterSalesDetail.traderContactName}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.takeTraderContactName}
                </c:if>
              </td>
              <td style="line-height: 18px;width: 80px;" ><b>联系电话：</b></td>
              <td style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${confirmationPrintDto.afterSalesDetail.traderContactMobile}
                  / ${confirmationPrintDto.afterSalesDetail.traderContactTelephone}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.takeTraderContactMobile}
                  / ${confirmationPrintDto.saleorder.takeTraderContactTelephone}
                </c:if>
              </td>
            </tr>

            <tr>
              <th colspan="20" class="align_l">&nbsp;</th>
            </tr>
            <tr>
              <td colspan="20" style="font-family: '微软雅黑', Arial"><strong>产品信息</strong>
                <table cellpadding="0" width="100%" cellspacing="0" border="1"
                       class="table_form"
                       style="border-collapse: collapse; border: 1px solid #000;">
                  <tbody>
                  <tr>
                    <td class="align_c" nowrap="" width="15px" style="font-family: '微软雅黑', Arial"><b>序号</b></td>
                      <%--<c:if test="${confirmationPrintDto.orderPrintOutType == 2}"><td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td></c:if>--%>
                      <%--<c:if test="${confirmationPrintDto.type != 2}"><td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td></c:if>--%>
                      <%--<td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td>--%>
                    <td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2}">产品名称</c:when><c:otherwise>产品名称</c:otherwise></c:choose></b></td>
                    <td class="align_c" nowrap="" width="40px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>品牌</b></td>
                    <td class="align_c" cz-tab="J-model" nowrap="" width="40px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>型号/规格</b></td>
                    <td class="align_c" width="40px" nowrap="" style="font-family: '微软雅黑', Arial"><b>数量单位</b></td>
                    <c:if test="${confirmationPrintDto.type == 1}">
                      <c:if test="${confirmationPrintDto.printFlag == 5}">
                        <td class="align_c" cz-tab="J-price" nowrap="" width="40px" style="font-family: '微软雅黑', Arial"><b>单价(元)</b></td>
                        <td class="align_c" cz-tab="J-amount" nowrap="" width="40px" style="font-family: '微软雅黑', Arial"><b>总金额(元)</b></td>
                      </c:if>
                      <td class="align_c" cz-tab="J-materialCode" nowrap="" style="font-family: '微软雅黑', Arial"><b>物料编码</b></td>
                      <td class="align_c" cz-tab="J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">生产批号/序列号</c:when><c:otherwise>生产批号/序列号</c:otherwise></c:choose></b></td>
                      <c:if test="${confirmationPrintDto.printFlag == 5 or printFlag == 6}">
                        <td class="align_c" cz-tab="J-productDate" nowrap="" width="50px" style="font-family: '微软雅黑', Arial"><b>生产日期</b></td>
                      </c:if>
                      <td class="align_c" cz-tab="J-expirationDate"  nowrap="" width="50px" style="font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">有效期至</c:when><c:otherwise>有效期至</c:otherwise></c:choose></b></td>
                      <td class="align_c" cz-tab="J-number" width="60px" nowrap="" style="font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">注册证号/备案凭证编号</c:when><c:otherwise>注册证号/备案凭证编号</c:otherwise></c:choose></b></td>
                      <td class="align_c" cz-tab="J-manufacturer"  nowrap="" style="font-family: '微软雅黑', Arial" ><b>生产企业</b></td>
                      <td class="align_c" cz-tab="J-productCompanyLicence" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>生产企业许可证号/备案凭证编号</b></td>
                      <td class="align_c" cz-tab="J-temperaTure" nowrap="" width="40px" style="font-family: '微软雅黑', Arial"><b>储运条件</b></td>
                    </c:if>
                    <c:if test="${confirmationPrintDto.type == 2}">
                      <c:if test="${confirmationPrintDto.priceFlag eq 1}">
                        <td class="align_c" cz-tab="J-realPrice" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>原单价</b></td>
                        <td class="align_c" cz-tab="J-price" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>单价</b></td>
                        <c:if test="${confirmationPrintDto.isExpressPrint}">
                          <td class="align_c" cz-tab="J-totalPrice" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>总价</b></td>
                        </c:if>
                      </c:if>
                      <%--<td class="align_c" cz-tab="J-materialCode" nowrap="" style="font-family: '微软雅黑', Arial"><b>物料编码</b></td>--%>
                      <td class="align_c" cz-tab="J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">生产批号/序列号</c:when><c:otherwise>生产批号/序列号</c:otherwise></c:choose></b></td>
                      <td class="align_c" cz-tab="J-productDate" nowrap="" width="50px" style="font-family: '微软雅黑', Arial"><b>生产日期</b></td>
                      <td class="align_c" cz-tab="J-expirationDate" nowrap="" style="font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">有效期至</c:when><c:otherwise>有效期至</c:otherwise></c:choose></b></td>
                      <td class="align_c" cz-tab="J-number" width="80px" nowrap="" style="font-family: '微软雅黑', Arial"><b><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">注册证号/备案凭证编号</c:when><c:otherwise>注册证号/备案凭证编号</c:otherwise></c:choose></b></td>
                      <td class="align_c" cz-tab="J-manufacturer" nowrap="" width="60px" style="font-family: '微软雅黑', Arial"><b>生产企业</b></td>
                      <td class="align_c" cz-tab="J-productCompanyLicence" nowrap="" width="80px" style="font-family: '微软雅黑', Arial"><b>生产企业许可证号/备案凭证编号</b></td>
                      <td class="align_c" cz-tab="J-temperaTure" nowrap="" style="font-family: '微软雅黑', Arial"><b>储运条件</b></td>
                    </c:if>
                  </tr>
                  <c:forEach var="list" items="${confirmationPrintDto.woList}" varStatus="num">
                    <tr>
                      <td class="align_c" style="font-family: '微软雅黑', Arial">${num.count}</td>
                        <%--<c:if test="${confirmationPrintDto.orderPrintOutType == 2}"><td class="align_c" style="font-family: '微软雅黑', Arial">
                            <c:choose>
                                <c:when test="${list.productChineseName != null && list.productChineseName != ''}">
                                    ${list.productChineseName}
                                </c:when>
                                <c:otherwise>\</c:otherwise>
                         </c:choose>
                        </td></c:if>--%>
                        <%--<c:if test="${confirmationPrintDto.type != 2}">--%>
                        <%--                                            <td class="align_c" style="font-family: '微软雅黑', Arial">--%>
                        <%--                                                <c:choose>--%>
                        <%--                                                    <c:when test="${list.productChineseNameNew != null && list.productChineseNameNew != ''}">--%>
                        <%--                                                        ${list.productChineseNameNew}--%>
                        <%--                                                    </c:when>--%>
                        <%--                                                    <c:otherwise>\</c:otherwise>--%>
                        <%--                                                </c:choose>--%>
                        <%--                                            </td>--%>
                        <%--</c:if>--%>
                      <td class="align_c" style="font-family: '微软雅黑', Arial">
                        <c:if test="${list.isActionGoods eq 1}">
                          <span style="color:red;">【活动】</span>
                        </c:if>
                          ${list.goodsName}
                      </td>
                      <td class="align_c" style="font-family: '微软雅黑', Arial">${list.brandName}</td>
                      <td class="align_c J-model" style="font-family: '微软雅黑', Arial">
                        <c:choose>
                          <c:when test="${list.spuType == 316 || list.spuType == 1008}">
                            <c:choose>
                              <c:when test="${list.model != null && list.model != ''}">
                                ${list.model}
                              </c:when>
                              <c:otherwise>\</c:otherwise>
                            </c:choose>
                          </c:when>
                          <c:when test="${list.spuType == 317 || list.spuType == 318}">
                            <c:choose>
                              <c:when test="${list.spec != null && list.spec != ''}">
                                ${list.spec}
                              </c:when>
                              <c:otherwise>\</c:otherwise>
                            </c:choose>
                          </c:when>
                          <c:otherwise>
                            <c:choose>
                              <c:when test="${list.model != null && list.model != ''}">
                                ${list.model}
                              </c:when>
                              <c:when test="${list.spec != null && list.spec != ''}">
                                ${list.spec}
                              </c:when>
                              <c:otherwise>\</c:otherwise>
                            </c:choose>
                          </c:otherwise>
                        </c:choose>
                      </td>
                      <td class="align_c" style="font-family: '微软雅黑', Arial">${0-list.num}${list.unitName}</td>
                      <c:if test="${confirmationPrintDto.type == 1}">
                        <c:if test="${confirmationPrintDto.printFlag == 5}">
                          <td class="align_c J-price" nowrap="" style="font-family: '微软雅黑', Arial">${list.price}</td>
                          <td class="align_c J-amount" nowrap="" style="font-family: '微软雅黑', Arial">${list.amount}</td>
                        </c:if>
                        <td class="align_c J-materialCode" nowrap="" style="font-family: '微软雅黑', Arial">${list.materialCode}</td>
                        <td class="align_c J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"><c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || orderPrintOutType == 4}">
                          <c:choose><c:when test="${list.barcodeFactory != null && list.barcodeFactory != ''}">${list.barcodeFactory}</c:when><c:when test="${list.batchNumber != null && list.batchNumber != ''}">${list.batchNumber}</c:when><c:otherwise>\</c:otherwise></c:choose>
                        </c:when>
                          <c:otherwise>${list.batchNumber}</c:otherwise></c:choose></td>
                        <c:if test="${confirmationPrintDto.printFlag == 5 or printFlag == 6}">
                          <c:choose>
                            <c:when test="${list.productDate == 0 or list.productDate == null }">
                              <td class="align_c J-productDate" style="font-family: '微软雅黑', Arial">${list.productDateStr}</td>
                            </c:when>
                            <c:otherwise>
                              <td class="align_c J-productDate" style="font-family: '微软雅黑', Arial"><date:dateNOSingLe value="${list.productDate}"/></td>
                            </c:otherwise>
                          </c:choose>
                        </c:if>
                        <c:choose>
                          <c:when test="${list.expirationDate == 0 or list.expirationDate == null }">
                            <td class="align_c J-expirationDate" style="font-family: '微软雅黑', Arial">${list.title}</td>
                          </c:when>
                          <c:when test="${list.expirationDate >= 4102329600000 }">
                            <td class="align_c J-expirationDate" nowrap="" style="font-family: '微软雅黑', Arial">见铭牌或说明书</td>
                          </c:when>
                          <c:otherwise>
                            <td class="align_c J-expirationDate" style="font-family: '微软雅黑', Arial"><date:dateNOSingLe value="${list.expirationDate}"/></td>
                          </c:otherwise>
                        </c:choose>
                        <td class="align_c J-number">
                          <c:if test="${confirmationPrintDto.titleType == 1}"><span style="font-family: '微软雅黑', Arial">${list.recordNumber}</span></c:if>
                          <c:if test="${confirmationPrintDto.titleType == 2}"><span style="font-family: '微软雅黑', Arial">${list.registrationNumber}</span></c:if>
                          <c:if test="${confirmationPrintDto.titleType == null}"><span style="font-family: '微软雅黑', Arial">\</span></c:if>
                        </td>
                        <td class="align_c J-manufacturer" style="font-family: '微软雅黑', Arial">${list.manufacturer}</td>
                        <td class="align_c J-productCompanyLicence" style="font-family: '微软雅黑', Arial">${list.productCompanyLicence}</td>
                        <td class="align_c J-temperaTure" nowrap="" style="font-family: '微软雅黑', Arial">
                          <c:choose>
                            <c:when test="${list.storageConditionOne ==1 }"> 常温0-30℃ </c:when>
                            <c:when test="${list.storageConditionOne ==2 }"> 阴凉0-20℃ </c:when>
                            <c:when test="${list.storageConditionOne ==3 }"> 冷藏2-10℃   </c:when>
                            <c:when test="${list.storageConditionOne ==4 }">
                              <c:if test="${not empty list.storageConditionOneLowerValue and not empty list.storageConditionOneUpperValue}">
                                其他温度<fmt:formatNumber value="${list.storageConditionOneLowerValue}" type="number"/>℃
                                -
                                <fmt:formatNumber value="${list.storageConditionOneUpperValue}" type="number"/>℃
                              </c:if>
                            </c:when>
                            <c:otherwise> \ </c:otherwise>
                          </c:choose>
                            <%--													${list.temperaTure} --%>
                        </td>
                      </c:if>
                      <c:if test="${confirmationPrintDto.type == 2}">
                        <c:if test="${confirmationPrintDto.priceFlag eq 1}">
                          <td class="align_c J-realPrice" nowrap="" style="font-family: '微软雅黑', Arial">${list.realPrice}</td>
                          <td class="align_c J-price" nowrap="" style="font-family: '微软雅黑', Arial">${list.price}</td>
                          <%--物流信息 - 医械购出库单模板 添加总价字段--%>
                          <c:if test="${confirmationPrintDto.isExpressPrint and confirmationPrintDto.isHcOrder eq 1}">
                            <td class="align_c J-totalPrice" style="font-family: '微软雅黑', Arial" nowrap="">${list.amount}</td>
                          </c:if>
                          <c:if test="${confirmationPrintDto.isExpressPrint and confirmationPrintDto.isHcOrder eq 0}">
                            <td class="align_c J-totalPrice" style="font-family: '微软雅黑', Arial" nowrap="">${list.price * (0-list.num)}</td>
                          </c:if>
                        </c:if>
                        <td class="align_c J-batchNumber" nowrap="" style="font-family: '微软雅黑', Arial"> <c:choose><c:when test="${confirmationPrintDto.orderPrintOutType == 2 || confirmationPrintDto.orderPrintOutType == 4}">
                          <c:choose><c:when test="${list.barcodeFactory != null && list.barcodeFactory != ''}">${list.barcodeFactory}</c:when><c:when test="${list.batchNumber != null && list.batchNumber != ''}">${list.batchNumber}</c:when><c:otherwise>\</c:otherwise></c:choose>
                        </c:when>
                          <c:otherwise>${list.batchNumber}</c:otherwise></c:choose></td>
                        <td class="align_c J-productDate" style="font-family: '微软雅黑', Arial">
                          <c:if test="${not empty list.productDate and list.productDate != 0}">
                            <date:dateNOSingLe value="${list.productDate}"/>
                          </c:if>
                        </td>
                        <c:choose>
                          <c:when test="${list.expirationDate == 0 or list.expirationDate == null }">
                            <td class="align_c J-expirationDate" nowrap="" style="font-family: '微软雅黑', Arial">${list.title}</td>
                          </c:when>
                          <c:when test="${list.expirationDate >= 4102329600000 }">
                            <td class="align_c J-expirationDate" nowrap="" style="font-family: '微软雅黑', Arial">见铭牌或说明书</td>
                          </c:when>
                          <c:otherwise>
                            <td class="align_c J-expirationDate" nowrap="" style="font-family: '微软雅黑', Arial"><date:dateNOSingLe value="${list.expirationDate}" /></td>
                          </c:otherwise>
                        </c:choose>
                        <td class="align_c J-number" nowrap="">
                          <c:if test="${confirmationPrintDto.titleType == 1}"><span style="font-family: '微软雅黑', Arial">${list.recordNumber}</span></c:if>
                          <c:if test="${confirmationPrintDto.titleType == 2}"><span style="font-family: '微软雅黑', Arial">${list.registrationNumber}</span></c:if>
                          <c:if test="${confirmationPrintDto.titleType == null}"><span style="font-family: '微软雅黑', Arial">\</span></c:if>
                        </td>
                        <td class="align_c J-manufacturer" style="font-family: '微软雅黑', Arial">${list.manufacturer}</td>
                        <td class="align_c J-productCompanyLicence" style="font-family: '微软雅黑', Arial">
                            ${list.productCompanyLicence}
                        </td>
                        <td class="align_c J-temperaTure" nowrap="" style="font-family: '微软雅黑', Arial">
                          <c:choose>
                            <c:when test="${list.storageConditionOne ==1 }"> 常温0-30℃ </c:when>
                            <c:when test="${list.storageConditionOne ==2 }"> 阴凉0-20℃ </c:when>
                            <c:when test="${list.storageConditionOne ==3 }"> 冷藏2-10℃   </c:when>
                            <c:when test="${list.storageConditionOne ==4 }">
                              <c:if test="${not empty list.storageConditionOneLowerValue and not empty list.storageConditionOneUpperValue}">
                                其他温度<fmt:formatNumber value="${list.storageConditionOneLowerValue}" type="number"/>℃
                                -
                                <fmt:formatNumber value="${list.storageConditionOneUpperValue}" type="number"/>℃
                              </c:if>
                            </c:when>
                            <c:otherwise> \ </c:otherwise>
                          </c:choose>

                            <%--													${list.temperaTure} --%>
                        </td>
                      </c:if>
                    </tr>
                  </c:forEach>
                  </tbody>
                </table></td>
              <c:if test="${confirmationPrintDto.printFlag == 5}">
              <c:if test="${confirmationPrintDto.priceFlag eq 1}">
            <tr>
              <table cellpadding="0" width="100%" cellspacing="0" border="1"
                     class="table_form"
                     style="border-collapse: collapse; border: 1px solid #000;">
                <tr class="align_c ">
                  <td class="align_c" style="font-family: '微软雅黑', Arial">金额总计(小写): ¥${confirmationPrintDto.totalPrice}    金额总计(大写): ¥${confirmationPrintDto.chineseTotal}</td>
                </tr>
              </table>
            </tr>
            </c:if>

            </c:if>
            <c:if test="${confirmationPrintDto.hcPrintOutflag && confirmationPrintDto.priceFlag eq 1}">
              <tr>
                <table cellpadding="0" width="100%" cellspacing="0" border="1"
                       class="table_form"
                       style="border-collapse: collapse; border: 1px solid #000;">
                  <tr class="align_c ">
                    <td class="align_c" style="font-family: '微软雅黑', Arial">商品总额: ¥${confirmationPrintDto.totalPrice}    优惠券: ¥${confirmationPrintDto.couponsPrice}    运费: ¥${confirmationPrintDto.expressPrice}   实付金额: ¥${confirmationPrintDto.realTotalPrice}</td>
                  </tr>
                </table>
              </tr>
            </c:if>
            </tr>

            <tr>
              <td colspan="6">
                <table width="100%" cellspacing="0">
                  <tbody >
                  <tr >
                    <td width="33%" style="font-family: '微软雅黑', Arial"><b>制单人：徐晓丽</b></td>
                    <td style="font-family: '微软雅黑', Arial"><b>发货人：方鹏华</b></td>
                    <div></div>
                    <td width="33%" style="font-family: '微软雅黑', Arial"><b >承运人：</b></td>
                  </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <table width="100%" cellspacing="0">
                  <tbody >
                  <tr >
                    <td width="33%"><b>客户签收：</b>_______________</td>
                    <td><b>日期：</b>________<b>年</b>________<b>月</b>________<b>日(签收后请回传)</b></td>
                  </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            <tr>
              <td colspan="6" style="position:relative;font-family: '微软雅黑', Arial">
                <c:if test="${confirmationPrintDto.type != 2}">
                  <b>友情提示：尊敬的客户，发票将在一周左右通过快递送达公司，请注意查收。</b>
                </c:if>
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <table width="100%" border="0" cellpadding="0" cellspacing="0">
                  <tbody>
                  <tr>
                  </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
    <div style="page-break-after: always;"></div>
  </c:forEach>


  <c:forEach var="confirmationPrintDto" items="${directConfirmationPrintDtos}" varStatus="status">
    <table cellpadding="0" cellspacing="0" width="1095" border="0">
      <tbody>
      <tr>
        <td>
          <!-- header -->
          <table cellpadding="0" cellspacing="0" width="100%" border="0"
                 height="90" style="padding: 0px;">
            <tbody>
            <tr>
              <td class="align_c"
                  style="line-height: 30px; font-size: 17px; font-family: Arial; font-weight: bold;  padding-right: 98px; ">
                货物确认单
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      <tr>
        <td valign="top" align="center">
          <table cellpadding="0" cellspacing="0" width="867" border="0"
                 align="center">
            <tbody>
            <tr height="17">
              <td style="line-height: 18px;width: 80px;" ><b>客户名称：</b></td>
              <td class="align_l" style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.traderName}
                </c:if>
              </td>
              <td style="line-height: 18px;width: 70px;" ><b>合同单号：</b></td>
              <td class="align_l" style="line-height: 18px;width: 180px;" nowrap="">${confirmationPrintDto.bussinessNo}</td>
              <td style="line-height: 18px;width: 80px;" ><b>联系人：</b></td>
              <td style="line-height: 18px;width: 80px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${confirmationPrintDto.afterSalesDetail.traderContactName}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.takeTraderContactName}
                </c:if>
              </td>
            </tr>
            <tr>
              <td style="line-height: 18px;width: 80px;" ><b>收货地址：</b></td>
              <td style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${afterSalesDetail.address}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.takeTraderAddress}
                </c:if>
              </td>
              <td style="line-height: 18px;width: 80px;" ><b>联系电话：</b></td>
              <td style="line-height: 18px;" >
                <c:if test="${confirmationPrintDto.bussinessType != 496}">
                  ${confirmationPrintDto.afterSalesDetail.traderContactMobile}
                  / ${confirmationPrintDto.afterSalesDetail.traderContactTelephone}
                </c:if>
                <c:if test="${confirmationPrintDto.bussinessType == 496}">
                  ${confirmationPrintDto.saleorder.takeTraderContactMobile}
                  / ${confirmationPrintDto.saleorder.takeTraderContactTelephone}
                </c:if>
              </td>
            </tr>
            <div style="">
              <td></td>
              <td></td>
              <td class="align_c"
                  style="line-height: 30px; font-size: 17px; padding-left: 7px; font-family: Arial; font-weight: bold;">
                产品信息
              </td>
            </div>
            <table cellpadding="0" width="100%" cellspacing="0" border="1"
                   class="table_form"
                   style="border-collapse: collapse; border: 1px solid #000;">
              <tbody>
              <tr>
                <td class="align_c" nowrap="" width="150px" style="font-family: '微软雅黑', Arial"><b>序号</b></td>
                  <%--<c:if test="${confirmationPrintDto.orderPrintOutType == 2}"><td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td></c:if>--%>
                  <%--<c:if test="${confirmationPrintDto.type != 2}"><td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td></c:if>--%>
                  <%--<td class="align_c" nowrap="" width="60px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>通用名称</b></td>--%>
                <td class="align_c" nowrap="" width="700px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>产品名称</b></td>
                <td class="align_c" nowrap="" width="200px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>品牌</b></td>
                <td class="align_c" cz-tab="J-model" nowrap="" width="200px" style="word-wrap:break-word;font-family: '微软雅黑', Arial"><b>型号/规格</b></td>
                <td class="align_c" width="200px" nowrap="" style="font-family: '微软雅黑', Arial"><b>数量</b></td>
                <td class="align_c" width="200px" nowrap="" style="font-family: '微软雅黑', Arial"><b>单位</b></td>
              </tr>
              <c:forEach var="list" items="${confirmationPrintDto.batchExpressVos}" varStatus="num">
                <tr>
                  <td class="align_c" style="font-family: '微软雅黑', Arial">${num.count}</td>
                  <td class="align_c" style="font-family: '微软雅黑', Arial">
                      ${list.goodsName}
                  </td>
                  <td class="align_c" style="font-family: '微软雅黑', Arial">${list.brand}</td>
                  <td class="align_c J-model" style="font-family: '微软雅黑', Arial">${list.model}</td>
                  <td class="align_c" style="font-family: '微软雅黑', Arial">${list.num}</td>
                  <td class="align_c" style="font-family: '微软雅黑', Arial">${list.unitName}</td>
                </tr>
              </c:forEach>
              </tbody>
            </table></td>
            </tr>
            <tr>
              <td colspan="6" style="position:relative;font-family: '微软雅黑', Arial">
                <b>尊敬的客户：<br>
                  您好！您已于 ${confirmationPrintDto.year}年 ${confirmationPrintDto.month}月 ${confirmationPrintDto.day}日 签收以上产品，请您认真核对上述信息，内容确认无误后请签字或盖章，并将签收单电子扫描件发送到邮箱：<EMAIL></b>
              </td>
            </tr>
            <tr>
              <td colspan="6">
                <table width="100%" cellspacing="0">
                  <tbody >
                  <tr >
                    <td width="33%"><b>客户签名：</b>_______________</td>
                    <td><b>日期：</b>________<b>年</b>________<b>月</b>________<b>日</b></td>
                  </tr>
                  </tbody>
                </table>
              </td>
            </tr>
            </tbody>
          </table>
        </td>
      </tr>
      </tbody>
    </table>
    <div style="page-break-after: always;"></div>
  </c:forEach>

  <c:if test="${WhetherToWarn == 1}">
    <div class="noBatchInfo">
    暂无待确认批次
    </div>
    <button type="button" class="confSearch bt-small bt-bg-style bg-light-blue" onclick="javascript:window.location.href=document.referrer;" >
      返回</button>
  </c:if>



</div>
<c:if test="${ WhetherToWarn eq null}">
<span class="confSearch bt-largest bt-bg-style bg-light-blue"
      onclick="preview('printdiv')" style="margin-top: 50px" id="searchSpan">打印</span>
</c:if>




<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<script type="text/javascript"
        src='<%=basePath%>static/js/logistics/warehouseIn/addBarcode.js?rnd=${resourceVersionKey}'></script>
<style>
  .noBatchInfo {
    padding-top: 119px;
    width: 400px;
    height: 400px;
    line-height: 200px;
    text-align: center;
    font-size: 20px;
    margin: auto;
  }
  .confSearch {
    width: 50px;
    height: 36px;
    line-height: 36px;
  }
</style>
</body>
</html>