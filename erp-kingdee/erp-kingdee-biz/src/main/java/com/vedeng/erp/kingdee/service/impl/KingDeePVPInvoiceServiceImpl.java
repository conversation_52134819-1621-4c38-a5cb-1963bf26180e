package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatPlainInvoiceEntity;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceConvertor;
import com.vedeng.erp.kingdee.service.KingDeePVPInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeePVPInvoiceService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import com.vedeng.infrastructure.kingdee.domain.command.UpdateExtCommand;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;

/**
 * @description: 采购增值税普通发票类
 * @return:
 * @author: Suqin
 * @date: 2022/12/2 17:22
 **/
@Service
@Slf4j
public class KingDeePVPInvoiceServiceImpl extends KingDeeMqBaseServiceImpl<PurchaseVatPlainInvoiceDto> implements KingDeePVPInvoiceService, KingDeePVPInvoiceApiService {

    @Autowired
    KingDeePurchaseVatPlainInvoiceMapper KingDeePurchaseVatPlainInvoiceMapper;

    @Autowired
    KingDeePurchaseVatPlainInvoiceCommandConvertor kingDeePurchaseVatPlainInvoiceCommandConvertor;

    @Autowired
    KingDeePurchaseVatPlainInvoiceConvertor kingDeePurchaseVatPlainInvoiceConvertor;

    private static final Logger logger = LoggerFactory.getLogger(KingDeePVPInvoiceServiceImpl.class);

    @Override
    public void update(PurchaseVatPlainInvoiceDto purchaseVatPlainInvoiceDto) {

        KingDeePurchaseVatPlainInvoiceEntity entity = KingDeePurchaseVatPlainInvoiceMapper.selectByFQzokBddjtid(purchaseVatPlainInvoiceDto.getFQzokBddjtid());
        if (entity == null) {
            logger.info("当前客户信息尚未推送过金蝶，客户信息：{}", JSONObject.toJSONString(purchaseVatPlainInvoiceDto));
            return;
        }

        purchaseVatPlainInvoiceDto.setFid(entity.getFid());
        OperateExtCommand operateExtCommand = new OperateExtCommand(purchaseVatPlainInvoiceDto.getFormId(), purchaseVatPlainInvoiceDto.getFid(),
                KingDeeConstant.ORG_ID.toString(), null);
        ArrayList<SuccessEntity> successUnAudit = kingDeeBaseApi.unAudit(operateExtCommand);

        if (CollUtil.isNotEmpty(successUnAudit)) {
            logger.info("反审核成功:{}",successUnAudit);
        }
        KingDeePurchaseVatPlainInvoiceCommand command = kingDeePurchaseVatPlainInvoiceCommandConvertor.toCommand(purchaseVatPlainInvoiceDto);
        command.setFID(entity.getFid());
        command.setFDATE(entity.getFdate());
        RepoStatus update = kingDeeBaseApi.update(new UpdateExtCommand<>(command, purchaseVatPlainInvoiceDto.getFormId()));
        ArrayList<SuccessEntity> successEntities = update.getSuccessEntitys();
        if (CollUtil.isNotEmpty(successEntities)) {
            KingDeePurchaseVatPlainInvoiceEntity invoiceEntity = kingDeePurchaseVatPlainInvoiceConvertor.toEntity(purchaseVatPlainInvoiceDto);
            invoiceEntity.setPurchaseVatPlainInvoiceId(entity.getPurchaseVatPlainInvoiceId());
            KingDeePurchaseVatPlainInvoiceMapper.updateByPrimaryKeySelective(invoiceEntity);
        }


        logger.info("修改采购增值税普通发票时间推送金蝶：{}", JSON.toJSONString(purchaseVatPlainInvoiceDto));

    }
}
