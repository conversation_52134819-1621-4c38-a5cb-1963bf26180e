package com.vedeng.crm.visitrecord.domain.dto;

import lombok.Data;
import java.util.List;

@Data
public class VisitRecordQueryDto {

    /**
     * 特殊权限的人,能看到所有的拜访计划
     */
    private Integer seeAll;

    /**
     * 该账号所有的下属
     */
    private List<Integer> allSubordinateUserIdList;

    /**
     * 当前用户的ID
     */
    private Integer currentUserId;
//------------------------------------以上两个参数与请求的参数无关--------------------------------//
    
    /** 拜访计划编号 -精确查询*/
    private String visitRecordNo;
    
    /** 计划拜访时间-开始 */
    private String planVisitDateStart;
    
    /** 计划拜访时间-结束 */
    private String planVisitDateEnd;

    /** 拜访完成时间-开始 */
    private String completeDateStart;
    /** 拜访完成时间-结束 */
    private String completeDateEnd;

    /** 添加时间-开始 */
    private String addTimeStart;
    /** 添加时间-结束 */
    private String addTimeEnd;
    
    /** 拜访人 */
    private Integer visitorId;
    
    /** 拜访目标 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开) */
    private String visitTarget;

    /** 拜访目标-集合 (A新客开发B商机跟进C老客客情维护D签约会员E产品推广，以逗号隔开) */
    private List<String> visitTargetList;
    
    /** 客户名称 */
    private String customerName;

    /** 客户类型 (ERP客户时自动带入且不能修改，其他必填。465分销466终端) */
    private Integer customerNature;


    /** 拜访内容-联系人信息-姓名 */
    private String contactName;

    /** 拜访内容-联系人信息-手机 */
    private String contactMobile;

    /** 拜访内容-联系人信息-电话 */
    private String contactTele;


    /** 拜访计划的状态：1.待拜访；2.拜访中；3.已拜访；4.已关闭 */
    private List<Integer> visitRecordStatusList;

    /** 线索/商机编号 */
    private String bussinessChanceNo;

    /** 沟通事项-沟通情况 */
    private String commucateContent;

    /**
     * 创建人集合
     */
    private List<Integer> creatorIdList;

    /**
     * 同行人集合
     */
    private List<Integer> tongxingIdList;

    /**
     * 拜访人集合
     */
    private List<Integer> visitorIdList;

    /**
     * 前端定义的排序规则
     * 默认ADD_TIME DESC
     * 前端可以给ADD_TIME DESC ，PLAN_VISIT_DATE DESC ，COMPLETE_DATETIME DESC 等等
     *
     */
    private String orderBy;





} 