<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="设置安全库存" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/flash/prepare/safe_stock.js?rnd=${resourceVersionKey}'></script>
<div class="content">
    <div class="sku-info line">
        <ul>
            <li>
                <div class="infor_name">
                    <label class="info">订货号：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.skuNo}</label>
                </div>
            </li>
            <li>
                <div class="infor_name">
                    <label class="info">商品名称：</label>
                </div>
                <div class="info-name">
                    <label>${prepareStock.skuName}</label>
                </div>
            </li>
        </ul>
    </div>
    <div class="sku-opr">
        <form id="set_safe_stock">
            <input type="hidden" name="deployType" id="deployType" value="${prepareStock.deployType}">
            <input type="hidden" name="safeStockVal" id="safeStockVal" value="${prepareStock.safeStock}">
            <input type="hidden" name="originSafeStock" id="originSafeStock" value="${prepareStock.safeStock}">
            <input type="hidden" name="regularId" id="regularId" value="${prepareStock.regularId}">
            <input type="hidden" name="skuId" id="skuId" value="${prepareStock.skuId}">
            <ul class="lastResult">
                <li>
                    <div class="infor_name label-c">
                        <span>*</span><label class="info">安全库存：</label>
                    </div>
                    <div class="f_left">
                        <div id="safe_type_input" style="display: none">
                            <input type="text" class="input-c label-c mr5"
                                   placeholder="请输入安全库存"  name="safeStock" id="safeStock" value="${prepareStock.safeStock}" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}">  ${prepareStock.unit}
                            <div>
                                <a class="mt5" onclick="changeDeployType()">切换至计算安全库存</a>
                            </div>
                        </div>
                        <div id="safe_type_sys" style="display: none">
                            <input type="text" class="input-c100 label-c mr5 input-color" readonly value="${prepareStock.threeMonthDaysSaleNum}"> X&nbsp
                            <input type="text" class="input-c100 label-c mr5" name="safeRatio" id="safeRatio" value="${prepareStock.safeRatio}" onchange="setSafeStockVal(this, ${prepareStock.threeMonthDaysSaleNum})" onkeyup="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}" onafterpaste="if(this.value.length==1){this.value=this.value.replace(/[^0-9]/g,'')}else{this.value=this.value.replace(/\D/g,'')}"> =&nbsp
                            <input type="text" class="input-c100 label-c mr5 input-color" readonly name="safeStockSys" id="safeStockSys" value="${prepareStock.threeMonthDaysSaleNum*prepareStock.safeRatio}"> ${prepareStock.unit}
                            <div>
                                <span class="mt5" style="color: gray; font-size: 13px;">近三个月日均销量</span>
                            </div>
                            <div>
                                <a class="mt5" onclick="changeDeployType()">切换至输入安全库存</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li class="safe_stock_error" style="display:none; margin: 0;" >
                    <div class="infor_name label-c">
                    </div>
                    <div class="f_left">
                        <span style="color:red;">安全库存不能为空!</span>
                    </div>
                </li>
                <li>
                    <div class="infor_name label-c">
                        <span>*</span><label class="info">调整原因：</label>
                    </div>
                    <div class="f_left">
                        <select class="input-middle label-c select-c" name="operateType" id="operateType">
                            <option value="-1">请选择</option>
                            <option value="0">价格调整暂时供应商无法供货</option>
                            <option value="1">供应商内部原因（产能调整、库存盘点）</option>
                            <option value="2">内部活动</option>
                            <option value="3">市场环境</option>
                            <option value="4">其他</option>
                        </select>
                    </div>
                </li>
                <li class="operate_type_error" style="display:none; margin: 0;">
                    <div class="infor_name label-c">
                    </div>
                    <div class="f_left">
                        <span style="color:red;">请选择调整原因!</span>
                    </div>
                </li>
                <li>
                    <div class="infor_name label-c">
                    </div>
                    <div class="f_left">
                        <textarea class="input-textarea" cols="f_left" id="operateReason" name="operateReason" placeholder="请输入调整原因,最多不超过500字" rows=4 maxLength="500"></textarea>
                    </div>
                </li>
                <li class="operate_reason_error" style="display:none; margin: 0;">
                    <div class="infor_name label-c">
                    </div>
                    <div class="f_left">
                        <span style="color:red;">当调整原因选择其他，在原因框输入原因!</span>
                    </div>
                </li>
            </ul>
            <div class="submit-c tcenter">
                <button class="bt-largest bt-bg-style bg-light-blue" type="button" onclick="setSafeStock();">确定</button>
                <button id="close-layer" type="button" class="bg-cancle-blue bt-bg-style bt-largest">取消</button>
            </div>
        </form>
    </div>
</div>
<style>
    .sku-info {
        width: 100%;
        padding: 20px;
        font-size: 15px;
    }

    .sku-info ul li {
        float: left;
        height: 30px;
        line-height: 30px;
    }

    .info {
        font-weight: 500;
    }

    .sku-info .info-name {
        float: left;
        margin: 3px 10px 0 0;
    }

    .sku-opr {
        padding: 20px;
        font-size: 15px;
    }

    .sku-opr ul li {
        margin-top: 20px;
    }

    .label-c {
        height: 40px;
        line-height: 40px;
    }

    .input-color {
        background-color: #f3f3f3;
    }

    .input-c {
        width: 200px;
        border-color: darkgray;
        text-align: center;
    }

    .input-c100 {
        width: 100px;
        border-color: #CCCCCC;
        text-align: center;
    }

    .select-c {
        width: 400px;
    }

    .input-textarea {
        width: 600px;
    }

    .submit-c {
        margin-top:30px;
    }

    .bg-cancle-blue {
        background: white;
        color: #1b76ed;
        border: 1px solid #1b76ed;
    }
</style>
<%@ include file="../../common/footer.jsp"%>
