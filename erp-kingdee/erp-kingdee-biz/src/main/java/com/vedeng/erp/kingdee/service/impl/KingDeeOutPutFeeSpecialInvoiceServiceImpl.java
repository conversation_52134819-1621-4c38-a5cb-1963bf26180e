package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.common.base.service.impl.KingDeeBaseServiceImpl;
import com.vedeng.erp.kingdee.domain.command.KingDeeOutPutFeeSpecialInvoiceCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeeOutPutFeeSpecialInvoiceEntity;
import com.vedeng.erp.kingdee.dto.OutPutFeeSpecialInvoiceDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeOutPutFeeSpecialInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeOutPutFeeSpecialInvoiceConvertor;
import com.vedeng.erp.kingdee.repository.KingDeeOutPutFeeSpecialInvoiceRepository;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeeSpecialInvoiceApiService;
import com.vedeng.erp.kingdee.service.KingDeeOutPutFeeSpecialInvoiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/3/7 16:11
 */
@Service
@Slf4j
public class KingDeeOutPutFeeSpecialInvoiceServiceImpl extends KingDeeBaseServiceImpl<
        KingDeeOutPutFeeSpecialInvoiceEntity,
        OutPutFeeSpecialInvoiceDto,
        KingDeeOutPutFeeSpecialInvoiceCommand,
        KingDeeOutPutFeeSpecialInvoiceRepository,
        KingDeeOutPutFeeSpecialInvoiceConvertor,
        KingDeeOutPutFeeSpecialInvoiceCommandConvertor
        > implements KingDeeOutPutFeeSpecialInvoiceService, KingDeeOutPutFeeSpecialInvoiceApiService {
}