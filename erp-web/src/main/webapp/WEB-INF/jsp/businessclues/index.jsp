<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="线索列表" scope="application"/>
<%@ include file="../common/common.jsp" %>
<div class="main-container">
    <div class="list-pages-search">
        <form action="${pageContext.request.contextPath}/business/clues/index.do" method="post" id="search">

            <ul>
                <li>
                    <label class="infor_name">线索编号</label>
                    <input type="text" class="input-middle" name="businessCluesNo" id="businessCluesNo"
                           value="${businessCluesVo.businessCluesNo}">
                </li>
                <li>
                    <label class="infor_name">客户名称</label>
                    <input type="text" class="input-middle" name="traderName" id="traderName"
                           value="${businessCluesVo.traderName}">
                </li>
                <li>
                    <label class="infor_name">线索标签</label>
                    <input type="text" class="input-middle" placeholder="请输入群组名称" name="groupName" id="groupName"
                           value="${businessCluesVo.groupName}">
                </li>
                <li>
                    <label class="infor_name">推送时间</label>
                    <input class="Wdate f_left input-smaller96 mr5" type="text" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endTime\')}'})"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="startTimeStr" id="startTime"
                           value="${businessCluesVo.startTimeStr}">
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startTime\')}'})"
                           onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="endTimeStr" id="endTime"
                           value="${businessCluesVo.endTimeStr}">
                </li>
                <li>
                    <label class="infor_name">商机编号</label>
                    <input type="text" class="input-middle" name="businessChanceNo" id="businessChanceNo"
                           value="${businessCluesVo.businessChanceNo}">
                </li>
                <li>
                    <label class="infor_name">归属销售</label>
                    <select class="input-middle f_left" name="belongSale">
                        <option value="">全部</option>
                        <c:if test="${not empty userList }">
                            <c:forEach items="${userList }" var="user">
                                <option value="${user.userId }"
                                        <c:if test="${businessCluesVo.belongSale eq user.userId }">selected="selected"</c:if>>${user.username }</option>
                            </c:forEach>
                        </c:if>
                    </select>
                </li>
                <li>
                    <label class="infor_name">归属平台</label>
                    <select class="input-middle f_left" name="belongPlatform">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${businessCluesVo.belongPlatform eq 1 }">selected="selected"</c:if>>贝登医疗
                        </option>
                        <option value="2"
                                <c:if test="${businessCluesVo.belongPlatform eq 2 }">selected="selected"</c:if>>医械购
                        </option>
                        <option value="3"
                                <c:if test="${businessCluesVo.belongPlatform eq 3 }">selected="selected"</c:if>>科研购
                        </option>
                        <option value="6"
                                <c:if test="${businessCluesVo.belongPlatform eq 6 }">selected="selected"</c:if>>集采
                        </option>
                        <option value="5"
                                <c:if test="${businessCluesVo.belongPlatform eq 5 }">selected="selected"</c:if>>其他
                        </option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">客户地区</label>
                    <ul class="inputfloat f_left">
                        <li>
                            <select class="wid9" name="province" id="province">
                                <option value="0">全部</option>
                                <c:if test="${not empty provinceList }">
                                    <c:forEach items="${provinceList }" var="prov">
                                        <option value="${prov.regionId }"
                                                <c:if test="${businessCluesVo.province eq prov.regionId }">selected="selected"</c:if>>${prov.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid9" name="city" id="city">
                                <option value="0">全部</option>
                                <c:if test="${not empty cityList }">
                                    <c:forEach items="${cityList }" var="cy">
                                        <option value="${cy.regionId }"
                                                <c:if test="${businessCluesVo.city eq cy.regionId }">selected="selected"</c:if>>${cy.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                        <li>
                            <select class="wid9" name="zone" id="zone">
                                <option value="0">全部</option>
                                <c:if test="${not empty zoneList }">
                                    <c:forEach items="${zoneList }" var="zo">
                                        <option value="${zo.regionId }"
                                                <c:if test="${businessCluesVo.zone eq zo.regionId }">selected="selected"</c:if>>${zo.regionName }</option>
                                    </c:forEach>
                                </c:if>
                            </select>
                        </li>
                    </ul>
                </li>

                <li>
                    <label class="infor_name">是否沟通</label>
                    <select class="input-middle f_left" name="isCommunicate">
                        <option value="">全部</option>
                        <option value="1"
                                <c:if test="${businessCluesVo.isCommunicate eq 1 }">selected="selected"</c:if>>已沟通
                        </option>
                        <option value="2"
                                <c:if test="${businessCluesVo.isCommunicate eq 2 }">selected="selected"</c:if>>未沟通
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">线索价值</label>
                    <select class="input-middle f_left" name="worth">
                        <option value="">全部</option>
                        <option value= "99"
                                <c:if test="${businessCluesVo.worth eq 99 }">selected="selected"</c:if>>未评估
                        </option>
                        <option value="1"
                                <c:if test="${businessCluesVo.worth eq 1 }">selected="selected"</c:if>>有效
                        </option>
                        <option value="0"
                                <c:if test="${businessCluesVo.worth eq 0 }">selected="selected"</c:if>>无效
                        </option>
                    </select>
                </li>
            </ul>
            <div class="tcenter">
                <span class="bg-light-blue bt-bg-style bt-middle" id="searchSpan" onclick="search();">搜索</span>
                <span class="bt-middle bg-light-blue bt-bg-style" onclick="reset();">重置</span>
            </div>
        </form>
    </div>
    <div class="fixdiv">
        <div class="superdiv" style="margin:auto;">
            <table class="table table-bordered table-striped table-condensed table-centered">
                <thead>
                <tr>
                    <th class="sorts">序号</th>
                    <th class="wid10">线索编号</th>
                    <th class="wid10">线索标签</th>
                    <th class="wid18">客户名称</th>
                    <th class="wid7">归属销售</th>
                    <th class="wid6">归属平台</th>
                    <th class="wid10">客户地区</th>
                    <th class="wid8">推送时间</th>
                    <th class="wid5">中标次数</th>
                    <th class="wid7">跟踪建议</th>
                    <th class="wid7">商机编号</th>
                    <th class="wid14">操作</th>
                </tr>
                </thead>
                <tbody class="employeestate">
                <c:if test="${not empty businessCluesVoList}">
                    <c:forEach items="${businessCluesVoList}" var="businessCluesVo" varStatus="status">
                        <tr>
                            <td>${status.count}</td>
                            <td class="text-center">
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewBusinessClues<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                            "link":"./business/clues/viewBusinessClues.do?groupId=${businessCluesVo.groupId}&traderId=${businessCluesVo.traderId}&businessCluesId=${businessCluesVo.businessCluesId}","title":"线索详情"}'>
                                        ${businessCluesVo.businessCluesNo}</a>
                            </td>
                            <td>${businessCluesVo.groupName}</td>
                            <td>
                                <a class="addtitle" href="javascript:void(0);"
                                   tabTitle='{"num":"viewcustomer<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
                                            "link":"./trader/customer/baseinfo.do?traderId=${businessCluesVo.traderId}","title":"客户信息"}'>
                                        ${businessCluesVo.traderName}</a>
                            </td>
                            <td>${businessCluesVo.belongSaleName}</td>
                            <td>
                                <c:choose>
                                    <c:when test="${businessCluesVo.belongPlatform eq 1}">
                                        贝登医疗
                                    </c:when>
                                    <c:when test="${businessCluesVo.belongPlatform eq 2}">
                                        医械购
                                    </c:when>
                                    <c:when test="${businessCluesVo.belongPlatform eq 3}">
                                        科研购
                                    </c:when>
                                    <c:when test="${businessCluesVo.belongPlatform eq 4}">
                                        集团业务部
                                    </c:when>
                                    <c:when test="${businessCluesVo.belongPlatform eq 6}">
                                        集采
                                    </c:when>
                                    <c:when test="${businessCluesVo.belongPlatform eq 5}">
                                        其他
                                    </c:when>
                                </c:choose>
                            </td>
                            <td>${businessCluesVo.traderArea}</td>
                            <td><date:date value="${businessCluesVo.addTime}" format="yyyy-MM-dd"/></td>
                            <td>${businessCluesVo.zhongbiaoCount}</td>
                            <td>${businessCluesVo.tracSuggestion}</td>
                            <td>

                                <c:if test="${isSaleFlag==0}">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${businessCluesVo.businessChanceId}&traderId=${businessCluesVo.traderId }",
<%--                                                 "link":"/businessChance/details.do?id=${businessCluesVo.businessChanceId}",--%>
												"title":"销售商机详情"}'>${businessCluesVo.businessChanceNo}</a>
                                </c:if>
                                <c:if test="${isSaleFlag==1}">
                                    <a class="addtitle" href="javascript:void(0);"
                                       tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
<%--												"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${businessCluesVo.businessChanceId}&traderId=${businessCluesVo.traderId }",--%>
                                                 "link":"/businessChance/details.do?id=${businessCluesVo.businessChanceId}",
												"title":"销售商机详情"}'>${businessCluesVo.businessChanceNo}</a>
                                </c:if>

                            </td>
                            <td>
                                <span class="bg-light-blue bt-bg-style bt-smallest"
                                      onclick="viewRemark('${businessCluesVo.remark}')">查看话术</span>

                                <c:if test="${businessCluesVo.businessChanceId == null}">
                                    <span class="bg-light-blue bt-bg-style bt-smallest mr20 addtitle"
                                          tabTitle='{"num":"businesschanceservice<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./order/bussinesschance/newAddBussinesChance.do?traderId=${businessCluesVo.traderId}&isClues=1&businessCluesId=${businessCluesVo.businessCluesId}",
                                          "title":"新增商机"}'>转商机</span>
                                </c:if>
                                <c:if test="${businessCluesVo.top eq 0}">
                                    <span class="bg-light-blue bt-bg-style bt-smallest"
                                          onclick="changeTopStatus(${businessCluesVo.businessCluesId},1)">置顶</span>
                                </c:if>
                                <c:if test="${businessCluesVo.top eq 1}">
                                    <span class="bg-light-blue bt-bg-style bt-smallest"
                                          onclick="changeTopStatus(${businessCluesVo.businessCluesId},0)">取消置顶</span>
                                </c:if>

                            </td>
                        </tr>
                    </c:forEach>
                </c:if>
                </tbody>
            </table>
            <c:if test="${empty businessCluesVoList}">
                <!-- 查询无结果弹出 -->
                <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
            </c:if>
        </div>
    </div>
    <tags:page page="${page}"/>
</div>
<%@ include file="../common/footer.jsp" %>
<script type="text/javascript" src="<%=basePath%>/static/js/businessclues/index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript" src="<%=basePath%>/static/js/region/index.js?rnd=${resourceVersionKey}"></script>


