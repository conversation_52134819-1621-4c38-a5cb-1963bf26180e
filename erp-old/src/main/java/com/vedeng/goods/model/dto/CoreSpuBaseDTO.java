package com.vedeng.goods.model.dto;

import java.util.Date;
/**
 * 加字段需慎重
 */
public class CoreSpuBaseDTO {

	private String spuNo;
	private Integer spuId;
	private String spuShowName;

	private String spuName;

	private String spuType;// 商品类型

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	private Integer brandId;

	/**
	 * @deprecated ERP_LV_2020_105 移除来商品级别
	 */
	@Deprecated
	private Integer spuLevel;

	/**
	 * 注册证号
	 *
	 */
	private String registrationNumber;
	private Integer registrationNumberId;

	private Integer checkStatus;
	private Integer updater;
	private Date modTime;
	private String wikiHref;
	private Integer categoryId;
	private Integer operateInfoId;
	
	private String registrationIcon;


	/**
	 * 归属经理名称
	 *
	 * @since ERP_LV_2020_69
	 */
	private String productMgrName;

	/**
	 * 归属助理名称
	 *
	 */
	private String productAssistantName;

	/**
	 * 存储条件（温度)
	 *
	 * @since ERP_SV_2020_61
	 * @see com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum
	 */
	private Integer storageConditionTemperature;

	/**
	 * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionTemperatureLowerValue;

	/**
	 * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionTemperatureUpperValue;

	/**
	 * 存储条件（湿度，单位：%）：范围值存储较小的值
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionHumidityLowerValue;

	/**
	 * 存储条件（湿度，单位：%）：范围值存储较大的
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionHumidityUpperValue;

	/**
	 * 存储条件（其他）
	 *
	 * @since ERP_SV_2020_61
	 * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
	 */
	private String storageConditionOthers;

	/**
	 * 存储条件（其他）
	 *
	 * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
	 * @since ERP_SV_2020_61
	 */
	private Integer[] storageConditionOthersArray;

	/**
	 * 关联sku的技术参数名称列表（用逗号分隔）
	 *
	 * @since ERP_LV_2020_86
	 */
	private String technicalParameterNames;

	/**
	 * 规格、型号
	 *
	 * @since ERP_LV_2020_98
	 */
	private String specsModel;

	/**
	 * 商品级别
	 *
	 * @since ERP_LV_2020_105
	 */
	private Integer goodsLevelNo;

	/**
	 * 商品档位
	 *
	 * @since  ERP_LV_2020_105
	 */
	private Integer goodsPositionNo;

	/**
	 * 禁用状态
	 */
	private Integer status;



	/**
	 * 禁用原因
	 */
	private String disabledReason;

	/**
	 * 税收分类编码
	 */
	private String taxClassificationCode;

	public String getTaxClassificationCode() {
		return taxClassificationCode;
	}

	public void setTaxClassificationCode(String taxClassificationCode) {
		this.taxClassificationCode = taxClassificationCode;
	}

	public String getDisabledReason() {
		return disabledReason;
	}

	public void setDisabledReason(String disabledReason) {
		this.disabledReason = disabledReason;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}



	public String getRegistrationIcon() {
		return registrationIcon;
	}

	public void setRegistrationIcon(String registrationIcon) {
		this.registrationIcon = registrationIcon;
	}

	public Integer getAssignmentManagerId() {
		return assignmentManagerId;
	}

	public void setAssignmentManagerId(Integer assignmentManagerId) {
		this.assignmentManagerId = assignmentManagerId;
	}

	private Integer assignmentManagerId;


	public Integer getAssignmentAssistantId() {
		return assignmentAssistantId;
	}

	public void setAssignmentAssistantId(Integer assignmentAssistantId) {
		this.assignmentAssistantId = assignmentAssistantId;
	}

	private Integer assignmentAssistantId;

	public Integer getFirstEngageStatus() {
		return firstEngageStatus;
	}

	public void setFirstEngageStatus(Integer firstEngageStatus) {
		this.firstEngageStatus = firstEngageStatus;
	}

	private Integer firstEngageStatus;

	public Integer getFirstEngageId() {
		return firstEngageId;
	}

	public void setFirstEngageId(Integer firstEngageId) {
		this.firstEngageId = firstEngageId;
	}

	private Integer firstEngageId;


	public String getSpuNo() {
		return spuNo;
	}

	public void setSpuNo(String spuNo) {
		this.spuNo = spuNo;
	}

	public Integer getSpuId() {
		return spuId;
	}

	public void setSpuId(Integer spuId) {
		this.spuId = spuId;
	}

	public String getSpuShowName() {
		return spuShowName;
	}

	public void setSpuShowName(String spuShowName) {
		this.spuShowName = spuShowName;
	}

	public String getSpuName() {
		return spuName;
	}

	public void setSpuName(String spuName) {
		this.spuName = spuName;
	}

	public String getSpuType() {
		return spuType;
	}

	public void setSpuType(String spuType) {
		this.spuType = spuType;
	}

	public String getRegistrationNumber() {
		return registrationNumber;
	}

	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public String getWikiHref() {
		return wikiHref;
	}

	public void setWikiHref(String wikiHref) {
		this.wikiHref = wikiHref;
	}

	public Integer getSpuLevel() {
		return spuLevel;
	}

	public void setSpuLevel(Integer spuLevel) {
		this.spuLevel = spuLevel;
	}

	public Integer getCategoryId() {
		return categoryId;
	}

	public void setCategoryId(Integer categoryId) {
		this.categoryId = categoryId;
	}

	public Date getModTime() {
		return modTime;
	}

	public void setModTime(Date modTime) {
		this.modTime = modTime;
	}

	public Integer getOperateInfoId() {
		return operateInfoId;
	}

	public void setOperateInfoId(Integer operateInfoId) {
		this.operateInfoId = operateInfoId;
	}

	public String getProductMgrName() {
		return productMgrName;
	}

	public void setProductMgrName(String productMgrName) {
		this.productMgrName = productMgrName;
	}

	public String getProductAssistantName() {
		return productAssistantName;
	}

	public void setProductAssistantName(String productAssistantName) {
		this.productAssistantName = productAssistantName;
	}

	public Integer getStorageConditionTemperature() {
		return storageConditionTemperature;
	}

	public void setStorageConditionTemperature(Integer storageConditionTemperature) {
		this.storageConditionTemperature = storageConditionTemperature;
	}

	public Float getStorageConditionTemperatureLowerValue() {
		return storageConditionTemperatureLowerValue;
	}

	public void setStorageConditionTemperatureLowerValue(Float storageConditionTemperatureLowerValue) {
		this.storageConditionTemperatureLowerValue = storageConditionTemperatureLowerValue;
	}

	public Float getStorageConditionTemperatureUpperValue() {
		return storageConditionTemperatureUpperValue;
	}

	public void setStorageConditionTemperatureUpperValue(Float storageConditionTemperatureUpperValue) {
		this.storageConditionTemperatureUpperValue = storageConditionTemperatureUpperValue;
	}

	public Float getStorageConditionHumidityLowerValue() {
		return storageConditionHumidityLowerValue;
	}

	public void setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
		this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
	}

	public Float getStorageConditionHumidityUpperValue() {
		return storageConditionHumidityUpperValue;
	}

	public void setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
		this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
	}

	public String getStorageConditionOthers() {
		return storageConditionOthers;
	}

	public void setStorageConditionOthers(String storageConditionOthers) {
		this.storageConditionOthers = storageConditionOthers;
	}

	public Integer[] getStorageConditionOthersArray() {
		return storageConditionOthersArray;
	}

	public void setStorageConditionOthersArray(Integer[] storageConditionOthersArray) {
		this.storageConditionOthersArray = storageConditionOthersArray;
	}

	public String getTechnicalParameterNames() {
		return technicalParameterNames;
	}

	public void setTechnicalParameterNames(String technicalParameterNames) {
		this.technicalParameterNames = technicalParameterNames;
	}

	public String getSpecsModel() {
		return specsModel;
	}

	public void setSpecsModel(String specsModel) {
		this.specsModel = specsModel;
	}

	public Integer getUpdater() {
		return updater;
	}

	public void setUpdater(Integer updater) {
		this.updater = updater;
	}

	public Integer getGoodsLevelNo() {
		return goodsLevelNo;
	}

	public void setGoodsLevelNo(Integer goodsLevelNo) {
		this.goodsLevelNo = goodsLevelNo;
	}

	public Integer getGoodsPositionNo() {
		return goodsPositionNo;
	}

	public void setGoodsPositionNo(Integer goodsPositionNo) {
		this.goodsPositionNo = goodsPositionNo;
	}

	public Integer getRegistrationNumberId() {
		return registrationNumberId;
	}

	public void setRegistrationNumberId(Integer registrationNumberId) {
		this.registrationNumberId = registrationNumberId;
	}
}
