package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.finance.domain.entity.BankAliasEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BankAliasMapper {
    /**
     * delete by primary key
     * @param bankReceiptAliasId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long bankReceiptAliasId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(BankAliasEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(BankAliasEntity record);

    /**
     * select by primary key
     * @param bankReceiptAliasId primary key
     * @return object by primary key
     */
    BankAliasEntity selectByPrimaryKey(Long bankReceiptAliasId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BankAliasEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BankAliasEntity record);

    int updateBatch(List<BankAliasEntity> list);

    int updateBatchSelective(List<BankAliasEntity> list);

    int batchInsert(@Param("list") List<BankAliasEntity> list);

    List<BankAliasEntity> batchSelectByBankName(@Param("list") List<String> bankNameList);
}
