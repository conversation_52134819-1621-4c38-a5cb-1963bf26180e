package com.vedeng.erp.buyorder.domain.entity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TPurchaseDeliveryDirectBatchInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Long offset;

    public TPurchaseDeliveryDirectBatchInfoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdIsNull() {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID is null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdIsNotNull() {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID is not null");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID =", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdNotEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID <>", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdGreaterThan(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID >", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID >=", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdLessThan(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID <", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdLessThanOrEqualTo(Integer value) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID <=", value, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdIn(List<Integer> values) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID in", values, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdNotIn(List<Integer> values) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID not in", values, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdBetween(Integer value1, Integer value2) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID between", value1, value2, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andPurchaseDeliveryDirectBatchInfoIdNotBetween(Integer value1, Integer value2) {
            addCriterion("PURCHASE_DELIVERY_DIRECT_BATCH_INFO_ID not between", value1, value2, "purchaseDeliveryDirectBatchInfoId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdIsNull() {
            addCriterion("BUYORDER_ID is null");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdIsNotNull() {
            addCriterion("BUYORDER_ID is not null");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdEqualTo(Integer value) {
            addCriterion("BUYORDER_ID =", value, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdNotEqualTo(Integer value) {
            addCriterion("BUYORDER_ID <>", value, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdGreaterThan(Integer value) {
            addCriterion("BUYORDER_ID >", value, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("BUYORDER_ID >=", value, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdLessThan(Integer value) {
            addCriterion("BUYORDER_ID <", value, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdLessThanOrEqualTo(Integer value) {
            addCriterion("BUYORDER_ID <=", value, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdIn(List<Integer> values) {
            addCriterion("BUYORDER_ID in", values, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdNotIn(List<Integer> values) {
            addCriterion("BUYORDER_ID not in", values, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdBetween(Integer value1, Integer value2) {
            addCriterion("BUYORDER_ID between", value1, value2, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andBuyorderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("BUYORDER_ID not between", value1, value2, "buyorderId");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Date value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Date value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Date value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Date value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Date value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Date> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Date> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Date value1, Date value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("CREATOR is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("CREATOR is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(Integer value) {
            addCriterion("CREATOR =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(Integer value) {
            addCriterion("CREATOR <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(Integer value) {
            addCriterion("CREATOR >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(Integer value) {
            addCriterion("CREATOR >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(Integer value) {
            addCriterion("CREATOR <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(Integer value) {
            addCriterion("CREATOR <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<Integer> values) {
            addCriterion("CREATOR in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<Integer> values) {
            addCriterion("CREATOR not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(Integer value1, Integer value2) {
            addCriterion("CREATOR not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Date value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Date value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Date value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Date value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Date value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Date> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Date> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Date value1, Date value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNull() {
            addCriterion("UPDATER is null");
            return (Criteria) this;
        }

        public Criteria andUpdaterIsNotNull() {
            addCriterion("UPDATER is not null");
            return (Criteria) this;
        }

        public Criteria andUpdaterEqualTo(Integer value) {
            addCriterion("UPDATER =", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotEqualTo(Integer value) {
            addCriterion("UPDATER <>", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThan(Integer value) {
            addCriterion("UPDATER >", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterGreaterThanOrEqualTo(Integer value) {
            addCriterion("UPDATER >=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThan(Integer value) {
            addCriterion("UPDATER <", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterLessThanOrEqualTo(Integer value) {
            addCriterion("UPDATER <=", value, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterIn(List<Integer> values) {
            addCriterion("UPDATER in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotIn(List<Integer> values) {
            addCriterion("UPDATER not in", values, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER between", value1, value2, "updater");
            return (Criteria) this;
        }

        public Criteria andUpdaterNotBetween(Integer value1, Integer value2) {
            addCriterion("UPDATER not between", value1, value2, "updater");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}