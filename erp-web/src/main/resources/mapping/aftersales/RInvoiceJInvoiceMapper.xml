<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.RInvoiceJInvoiceMapper" >

  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.RInvoiceJInvoice" >
    <id column="R_INVOICE_J_INVOICE_ID" property="rInvoiceJInvoiceId" jdbcType="INTEGER" />
    <result column="INVOICE_ID" property="invoiceId" jdbcType="INTEGER" />
    <result column="RELATE_INVOICE_ID" property="relateInvoiceId" jdbcType="INTEGER" />
  </resultMap>

  <sql id="Base_Column_List" >
    R_INVOICE_J_INVOICE_ID, INVOICE_ID, RELATE_INVOICE_ID
  </sql>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_R_INVOICE_J_INVOICE
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_R_INVOICE_J_INVOICE
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </delete>

  <insert id="insert" parameterType="com.vedeng.aftersales.model.RInvoiceJInvoice" >
    insert into T_R_INVOICE_J_INVOICE (R_INVOICE_J_INVOICE_ID, INVOICE_ID, RELATE_INVOICE_ID
      )
    values (#{rInvoiceJInvoiceId,jdbcType=INTEGER}, #{invoiceId,jdbcType=INTEGER}, #{relateInvoiceId,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.RInvoiceJInvoice" >
    insert into T_R_INVOICE_J_INVOICE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rInvoiceJInvoiceId != null" >
        R_INVOICE_J_INVOICE_ID,
      </if>
      <if test="invoiceId != null" >
        INVOICE_ID,
      </if>
      <if test="relateInvoiceId != null" >
        RELATE_INVOICE_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rInvoiceJInvoiceId != null" >
        #{rInvoiceJInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="invoiceId != null" >
        #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="relateInvoiceId != null" >
        #{relateInvoiceId,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.RInvoiceJInvoice" >
    update T_R_INVOICE_J_INVOICE
    <set >
      <if test="invoiceId != null" >
        INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      </if>
      <if test="relateInvoiceId != null" >
        RELATE_INVOICE_ID = #{relateInvoiceId,jdbcType=INTEGER},
      </if>
    </set>
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.RInvoiceJInvoice" >
    update T_R_INVOICE_J_INVOICE
    set INVOICE_ID = #{invoiceId,jdbcType=INTEGER},
      RELATE_INVOICE_ID = #{relateInvoiceId,jdbcType=INTEGER}
    where R_INVOICE_J_INVOICE_ID = #{rInvoiceJInvoiceId,jdbcType=INTEGER}
  </update>
  
 <select id="getAfterInvoiceTotalAmount" resultType="java.math.BigDecimal">
 	SELECT  
		COALESCE(SUM(ABS(c.AMOUNT) - ABS(b.AMOUNT)),0)
	FROM 
		T_R_INVOICE_J_INVOICE a
		LEFT JOIN T_INVOICE b ON a.INVOICE_ID = b.INVOICE_ID
		LEFT JOIN T_INVOICE c ON a.RELATE_INVOICE_ID = c.INVOICE_ID
	WHERE a.RELATE_INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
 </select>
 
 <select id="getRInvoiceJInvoiceList" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_R_INVOICE_J_INVOICE
    where RELATE_INVOICE_ID = #{invoiceId,jdbcType=INTEGER} OR INVOICE_ID = #{invoiceId,jdbcType=INTEGER}
  </select>
  
</mapper>