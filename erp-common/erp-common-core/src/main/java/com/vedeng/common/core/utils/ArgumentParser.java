package com.vedeng.common.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.ParameterizedTypeImpl;
import com.vedeng.common.core.exception.ServiceException;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 参数解析类
 * @date 2022/9/7 19:35
 */
public class ArgumentParser {

    private static final Object[] EMPTY_ARGS = new Object[]{};
    public static final String INT = "int";
    public static final String INTEGER = "Integer";
    public static final String LONG = "long";
    public static final String LONG_WRAPPER = "Long";
    public static final String FLOAT = "float";
    public static final String FLOAT_WRAPPER = "Float";
    public static final String DOUBLE = "double";
    public static final String BYTE = "byte";
    public static final String BOOLEAN = "boolean";
    public static final String SHORT = "short";
    public static final String BIG_DECIMAL = "BigDecimal";
    public static final String BIG_INTEGER = "BigInteger";
    public static final String STRING = "String";
    public static final String CLASS = "Class";
    public static final String CHARACTER = "Character";
    public static final String DOUBLE_WRAPPER = "Double";
    public static final String BOOLEAN_WRAPPER = "Boolean";
    public static final String SHORT_WRAPPER = "Short";
    public final static String SEPARATOR = "@#@";

    public static Object[] parse(String[] args) {
        if (args == null || args.length == 0) {
            return EMPTY_ARGS;
        }

        Object[] result = new Object[args.length];
        for (int i = 0; i < args.length; i++) {
            result[i] = buildArgObj(args[i]);
        }
        return result;
    }

    /**
     * 将传入的String类型参数封装为目标对象
     *
     * @param arg 以@#@分割，根据我们的定义，
     *            第一个@#@前为目标对象类型，
     *            最后一个@#@后为目标对象值（如果为JOPO，则采用json方式进行反序列化）
     *            中间的作为泛型的参数类型传入
     *            <p>
     *            几个常见的case如:
     *            <p>
     *            "Hello World"  返回 "Hello Word"
     *            "int@#@10" 返回 10
     *            "com.git.hui.fix.core.binder.DefaultServerBinder@#@{}" 返回的是对象 defaultServerBinder
     *            "java.util.List@#@java.lang.String@#@["ads","bcd"]  返回的是List集合, 相当于  Arrays.asList("asd", "bcd")
     * @return Object
     */
    private static Object buildArgObj(String arg) {
        String[] typeValue = arg.split(SEPARATOR);
        if (typeValue.length == 1) {
            // 没有 @#@，把参数当成String
            return arg;
        } else if (typeValue.length == 2) {
            // 标准的kv参数, 前面为参数类型，后面为参数值
            return parseStrToObj(typeValue[0], typeValue[1]);
        } else if (typeValue.length >= 3) {
            // 对于包含泛型的参数类型
            // java.util.List@#@java.lang.String@#@["ads","bcd"]
            String[] reflectTypes = new String[typeValue.length - 2];
            System.arraycopy(typeValue, 1, reflectTypes, 0, typeValue.length - 2);
            return parseStr2GenericObj(typeValue[typeValue.length - 1], typeValue[0], reflectTypes);
        } else {
            throw new ServiceException("Illegal invoke arg: " + arg);
        }
    }

    private static Object parseStrToObj(String type, String value) {
        try {
            if (INT.equals(type) || INTEGER.equals(type)) {
                return Integer.parseInt(value);
            } else if (LONG.equals(type) || LONG_WRAPPER.equals(type)) {
                return Long.parseLong(value);
            } else if (FLOAT.equals(type) || FLOAT_WRAPPER.equals(type)) {
                return Float.parseFloat(value);
            } else if (DOUBLE.equals(type) || DOUBLE_WRAPPER.equals(type)) {
                return Double.parseDouble(value);
            } else if (BYTE.equals(type) || CHARACTER.equals(type)) {
                return Byte.parseByte(value);
            } else if (BOOLEAN.equals(type) || BOOLEAN_WRAPPER.equals(type)) {
                return Boolean.parseBoolean(value);
            } else if (SHORT.equals(type) || SHORT_WRAPPER.equals(type)) {
                return Short.parseShort(value);
            } else if (BIG_DECIMAL.equals(type)) {
                return new BigDecimal(value);
            } else if (BIG_INTEGER.equals(type)) {
                return new BigInteger(type);
            } else if (STRING.equals(type)) {
                return value;
            } else if (CLASS.equalsIgnoreCase(type)) {
                return ArgumentParser.class.getClassLoader().loadClass(value);
            } else {
                Class clz = ArgumentParser.class.getClassLoader().loadClass(type);
                return JSON.parseObject(value, clz);
            }
        } catch (Exception e) {
            throw new ServiceException("Pare Argument to Object Error! type: " + type + " value: " + value);
        }
    }

    /**
     * 将value转换为包含泛型的参数类型
     *
     * @param value   对象json串
     * @param clzType 对象类型
     * @param tTypes  泛型参数类型
     * @return
     */
    private static Object parseStr2GenericObj(String value, String clzType, String... tTypes) {
        try {
            Type[] paramsType = new Type[tTypes.length];
            int count = 0;
            for (String t : tTypes) {
                paramsType[count++] = getType(t);
            }

            // 这里借助fastjson指定精确的Type来实现反序列化
            Type type = new ParameterizedTypeImpl(paramsType, null, getType(clzType));
            return JSONObject.parseObject(value, type);
        } catch (Exception e) {
            throw new ServiceException(
                    "Pare Argument to Object Error! type: " + clzType + " @#@ " + Arrays.asList(tTypes) + " value: " +
                            value);
        }
    }

    /**
     * 获取参数类型
     *
     * @param type
     * @return
     * @throws ClassNotFoundException
     */
    private static Type getType(String type) throws ClassNotFoundException {
        return ArgumentParser.class.getClassLoader().loadClass(type);
    }


}
