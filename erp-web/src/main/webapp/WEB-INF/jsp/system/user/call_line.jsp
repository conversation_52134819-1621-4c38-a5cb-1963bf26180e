<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="部门线路号码配置" scope="application"/>
<%@ include file="../../common/common.jsp" %>


<div class="main-container">
    <ul>
        <li>
            <div class="infor_name">
                <label>部门</label>
            </div>
            <div class="f_left">
                <select class="input-middle f_left" id="orgId" name="orgId" onchange="initPosit();">
                    <option value="0">请选择</option>
                    <c:forEach items="${orgList}" var="org">
                        <option value="${org.orgId}">${org.orgName}</option>
                    </c:forEach>
                </select>
            </div>
        </li>

        <li style="color: red">以下项目填入号码才会更新，无更新不填:</li>

        <li>
            <div class="infor_name">
                <label>电信线路号码</label>
            </div>
            <div class="f_left">
                <input type="text" name="telecomLine" id="telecomLine"
                />
            </div>
            <div class="clear"></div>
        </li>

        <li>
            <div class="infor_name">
                <label>移动线路号码</label>
            </div>
            <div class="f_left">
                <input type="text" name="mobileLine" id="mobileLine"
                />
            </div>
            <div class="clear"></div>
        </li>

        <li>
            <div class="infor_name">
                <label>联通线路号码</label>
            </div>
            <div class="f_left">
                <input type="text" name="unicomLine" id="unicomLine"
                />
            </div>
            <div class="clear"></div>
        </li>

        <li>
            <div class="infor_name">
                <label>客户专线</label>
            </div>
            <div class="f_left">
                <input type="text" name="customerLine" id="customerLine"/>
            </div>
            <div class="clear"></div>
        </li>


    </ul>


</div>

<div class='clear'></div>
<div id="" class="mb15" style="margin-top: 12px;">
    <div class="add-tijiao tcenter">
        <button type="submit" onclick="submit()">提交</button>
        <button class="dele" type="button" id="close-layer">取消</button>
    </div>
</div>
</div>
<script>
    function submit() {
        var orgId = $('#orgId').val();
        if (orgId == undefined || orgId == 0) {
            alert('请选择部门信息！');
            return
        }

        var telecomLineValue = trim($('#telecomLine').val());
        var mobileLineValue = trim($('#mobileLine').val());
        var unicomLineValue = trim($('#unicomLine').val());
        var customerLineValue = trim($('#customerLine').val());
        if (isUnValidValue(telecomLineValue) && isUnValidValue(mobileLineValue) && isUnValidValue(unicomLineValue) && isUnValidValue(customerLineValue)) {
            alert('至少要更新一种号码!');
            return;
        }

        $.ajax({
            url: page_url + '/system/user/callingLineSave.do',
            data: {
                "orgId": orgId,
                "telecomLine": telecomLineValue,
                "mobileLine": mobileLineValue,
                "unicomLine": unicomLineValue,
                "customerLine": customerLineValue
            },
            type: "POST",
            dataType: "json",
            async: false,
            success: function (data) {
                if (data.code != 0) {
                    layer.alert(data.message);
                } else {
                    refreshPageList(data);
                }

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }


    function trim(str) {
        return str.replace(/(^\s*)|(\s*$)/g, "");
    }

    function isUnValidValue(str) {
        return str == undefined || str == '';
    }


</script>
<%@ include file="../../common/footer.jsp" %>
