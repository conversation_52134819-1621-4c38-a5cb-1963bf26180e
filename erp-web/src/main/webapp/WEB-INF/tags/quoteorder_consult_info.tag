<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="consultList" type="java.util.List" required="true" %>
<%@attribute name="userList" type="java.util.List" required="true" %>
<%@attribute name="roleType" type="java.lang.Integer" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>


<div class="title-container title-container-blue">
    <div class="table-title nobor">
        报价咨询
    </div>
    <div class="title-click" onclick="showAllConsult()">
        <span id="showConsultTitle">查看全部记录</span>
        <input type="hidden" id="isShowAll" value="0">
    </div>
</div>
<table class="table">
    <thead>
    <tr>
        <th class="table-smallest10">时间</th>
        <th class="table-smallest8">人员</th>
        <th class="table-smallest6">类型</th>
        <th>内容</th>
    </tr>
    </thead>
    <tbody>

    <c:forEach var="list" items="${consultList}" varStatus="status">
        <c:choose>
            <c:when test="${status.count > 5}">
                <tr class="tr-hidden" style="display: none">
            </c:when>
            <c:otherwise>
                <tr>
            </c:otherwise>
        </c:choose>
        <td><date:date value="${list.addTime}" /></td>
        <td><c:forEach var="user" items="${userList}"
                       varStatus="stat">
            <c:if test="${user.userId eq list.creator}">${user.username}</c:if>
        </c:forEach></td>
        <td>
            <c:choose>
                <c:when test="${list.type == 1}">咨询供应链</c:when>
                <c:when test="${list.type == 2}">咨询主管</c:when>
                <c:when test="${list.type == 3}">供应链答复</c:when>
                <c:when test="${list.type == 4}">销售主管答复</c:when>
            </c:choose>
        </td>
        <td style="text-align: left">${list.content}</td>
        </tr>
    </c:forEach>
    <c:if test="${empty consultList}">
        <!-- 查询无结果弹出 -->
        <tr>
            <td colspan="4">暂无咨询记录。</td>
        </tr>
    </c:if>
    <c:if test="${roleType == 1}">
        <tr>
            <td></td>
            <td></td>
            <td></td>
            <td><textarea style='width: 100%; height: 30px' name="managerReplyContent" id="managerReplyContent" placeholder="输入回复内容"></textarea></td>
        </tr>
    </c:if>
    </tbody>
</table>


<script>
    function showAllConsult() {
        var isShowAll = $("#isShowAll").val();
        if (isShowAll == 0){
            if ($("tr.tr-hidden").length == 0){
                layer.alert("无更多沟通记录")
                return;
            }
            $("tr.tr-hidden").css("display","table-row");
            $("#isShowAll").attr("value",1);
            $("#showConsultTitle").html("收起");
        } else {
            $("tr.tr-hidden").css("display","none");
            $("#isShowAll").attr("value",0);
            $("#showConsultTitle").html("查看全部记录");
        }
    }
</script>