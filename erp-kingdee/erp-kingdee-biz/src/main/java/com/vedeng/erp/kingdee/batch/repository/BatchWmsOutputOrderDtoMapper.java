package com.vedeng.erp.kingdee.batch.repository;
import java.util.List;

import com.vedeng.erp.kingdee.batch.dto.BatchWmsOutputOrderDto;
import org.apache.ibatis.annotations.Param;

public interface BatchWmsOutputOrderDtoMapper {

    /**
     * 根据单号查询出库单
     * 未删除
     * @param orderNo 单号
     * @return     List<BatchWmsInputOrderDto>
     */
    BatchWmsOutputOrderDto findByOrderNo(@Param("orderNo")String orderNo);


    Integer getTraderCustomerIdByOrderId(Long orderId);

    BatchWmsOutputOrderDto findByOrderNoAndType(@Param("orderNo")String orderNo,@Param("type")Integer type);


}