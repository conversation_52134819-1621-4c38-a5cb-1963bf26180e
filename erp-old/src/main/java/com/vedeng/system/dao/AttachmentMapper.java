package com.vedeng.system.dao;

import java.util.List;
import java.util.Map;
import com.vedeng.system.model.Attachment;
import com.wms.model.po.WmsQLA;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;

@Named
public interface AttachmentMapper {
    int deleteByPrimaryKey(Integer attachmentId);

    int insert(Attachment record);

    int insertSelective(Attachment record);

    Attachment selectByPrimaryKey(Integer attachmentId);

    int updateByPrimaryKeySelective(Attachment record);

    int updateByPrimaryKey(Attachment record);

    /**
     * 附件信息
     * <p>Title: getAttachmentsList</p>
     * <p>Description: </p>
     * @param paramMap
     * @return
     * <AUTHOR>
     * @date 2019年4月1日
     */
	List<Attachment> getAttachmentsList(Map<String, Object> paramMap);


	List<Attachment> getSyncAttachmentsList(@Param("page") Integer page, @Param("count") Integer count);


	/**
	 * 根据参数删除
	 * <p>Title: deleteByParam</p>
	 * <p>Description: </p>
	 * @param attachmentMap
	 * @return
	 * <AUTHOR>
	 * @date 2019年4月2日
	 */
	Integer deleteByParam(Map<String, Object> attachmentMap);

	/**
	 * 添加注册证
	 * <p>Title: insertAttachmentList</p>
	 * <p>Description: </p>
	 * @param attachmentMap
	 * @return
	 * <AUTHOR>
	 * @date 2019年4月2日
	 */
	Integer insertAttachmentList(Map<String, Object> attachmentMap);


	List<Attachment> getAttachmentsByProductCompanyId(@Param("productCompanyId") Integer productCompanyId,@Param("attachmentFunction") List<Integer> attachmentFunction);
	/**
	*获取当前商品信息
	* @Author:strange
	* @Date:17:31 2020-03-20
	*/
	Attachment getSkuBarcodeByGoodsId(Integer goodsId);

	void delByKeyIdAndRelatedTableId(@Param("realtedId") Integer temporaryStorageId, @Param("realtedTableId") Integer authorizationStorageFile);

	List<Attachment> getAttachmentInfoByRelatedIdAndFunctionId(@Param("realtedId") Integer authorizationApplyId, @Param("functionId") Integer authorizationApplyFile);

	/**
	 * 根据spuid获取下传商品资质List(产品注册证975、生产企业营业执照1000、医疗器械生产许可证978)
	 * @param skuId
	 * @return
	 */
	List<WmsQLA> getWmsQlaListBySkuId(Integer skuId);

	/**
	 * 批量获取商品资质(产品注册证975、生产企业营业执照1000、医疗器械生产许可证978)
	 * @Param: []
	 * @Return: java.util.List<com.wms.model.po.WmsQLA>
	 * @Author: Rivan
	 * @Date: 2020/8/7 14:39
	 */
	List<WmsQLA> getWmsQlaList();

    List<WmsQLA> getWmsQlaListInSkuNoStr(@Param("skuNoStr") String skuNoStr);

    List<WmsQLA> getWmsQlaListByRegisterNumberId(@Param("registrationNumberId") Integer registrationNumberId);

    Attachment getAttachmentsByOriginalFilepath(Attachment search);

    List<Attachment> getWmsQlaListHistory(@Param("start") int start, @Param("limit") int limit);

	void deleteJGBDGZB(Map<String, Object> attachmentMap);

	void updataByParamNew(Map<String, Object> attachmentMap);

	List<Attachment> getUndeletedAttachmentsList(Map<String, Object> paramMap);

	/**
	 * <b>Description:</b><br> 获取订单合同回传
	 * @param saleorderId
	 * @return
	 * @Note
	 * <b>Author:</b> leo.yang
	 * <br><b>Date:</b> 2017年7月24日 下午6:18:30
	 */
	List<Attachment> getAttachmentList(Attachment record);

	List<Attachment> getAttachmentListByAlt(Attachment record);

    List<Attachment> getSaleorderAttachmentById(Integer saleorderId);

	List<Attachment> getSaleorderAttachmentByIdAndType(@Param("saleorderId") Integer saleorderId, @Param("type") Integer type);

	/**
	 * 批量更新文件
	 * @param newAttachmentList
	 */
	void batchUpdateAttachment(List<Attachment> newAttachmentList);

	List<Attachment> selectByPropertiesSelective(Attachment attachment);

	void updateByRelation(Attachment delAttachement);

	void updateUpdateAttachmentSuffix(@Param("attachment") Attachment attachment);

    List<Attachment> getAfterSaleAttachmentList(Attachment attachment);

	int delAttachment(Attachment record);

	Attachment queryRelatedIdByAttachementId(@Param("attachmentId") Integer attachmentId);

    List<Attachment> getWMSImageList();

	List<Attachment> getAttachmentsByParams(Attachment search);
	int batchDeleteByPrimaryKey(@Param("record") List<Integer> record);

	/**
	 * 软删附件信息
	 * @param attachment
	 * @return
	 */
	int updateAttachmentById(Attachment attachment);

	/**
	 * 获取附件应用类型
	 * @param attachment
	 * @return
	 */
	Attachment getAttachment(Attachment attachment);

	/**
	 * 查询销售订单ID
	 * @param attachment
	 * @return
	 */
	Attachment findSaleorderId(Attachment attachment);

	/**
	 * 更新附件表信息
	 * @param attachment
	 * @return
	 */
	int updateByAttachment(Attachment attachment);

	/**
	 * 更新附件审核信息
	 * @param attachment1
	 * @return
	 */
	int updateVerifiesInfo(Attachment attachment1);

	/**
	 * 删除附件审核信息
	 * @param attachment1
	 * @return
	 */
    int delVerifiesInfo(Attachment attachment1);
	/**
	 * 批量保存
	 * @param saveItems 数据集合
	 * @return
	 */
	int batchInsertSelective(@Param("list") List<Attachment> saveItems);

	/**
	 * 根据附件id删除附件
	 *
	 * @param attachment
	 */
	void delAttachmentId(Attachment attachment);

	/**
	 * 获取入库单附件
	 * @param attachment
	 * @return
	 */
	List<Attachment> queryOutInAttachmentList(Attachment attachment);

	/**
	 * 删除出库单附件(软删除)
	 * @param attachment
	 */
	void delWarehouseOutAttachment(Attachment attachment);
}