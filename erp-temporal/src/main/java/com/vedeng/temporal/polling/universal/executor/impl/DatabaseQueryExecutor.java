package com.vedeng.temporal.polling.universal.executor.impl;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.temporal.domain.dto.CustomPollingRequest;
import com.vedeng.temporal.enums.PollingQueryType;
import com.vedeng.temporal.polling.universal.config.DatabaseConfig;
import com.vedeng.temporal.polling.universal.executor.LocalQueryExecutor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据库查询执行器实现类
 * 
 * 实现本地数据库查询功能，专门处理数据库访问相关的逻辑。
 * 复用现有的PollingQueryType枚举来执行具体的数据库查询操作。
 * 
 * 主要功能：
 * - 执行数据库查询操作
 * - 集成现有的PollingQueryType查询逻辑
 * - 处理数据库连接和查询异常
 * - 提供查询结果缓存功能
 * - 支持跨公司数据查询
 * 
 * <AUTHOR> 4.0 sonnet
 * @version 1.0
 * @since 2025-01-24
 */
@Slf4j
@Component
public class DatabaseQueryExecutor implements LocalQueryExecutor<Map<String, Object>> {
    
    @Override
    public Map<String, Object> executeLocalQuery(Map<String, Object> context) throws Exception {
        DatabaseConfig databaseConfig = (DatabaseConfig) context.get("databaseConfig");
        if (databaseConfig == null) {
            throw new IllegalArgumentException("数据库配置不能为空");
        }
        
        log.debug("执行数据库查询，类型: {}, 业务ID: {}", 
                databaseConfig.getQueryType(), databaseConfig.getBusinessId());
        
        try {
            // 验证数据库配置
            databaseConfig.validate();
            
            // 根据查询类型执行查询
            Map<String, Object> queryResult = executeQueryByType(databaseConfig);
            
            log.debug("数据库查询成功，类型: {}, 结果: {}", 
                    databaseConfig.getQueryType(), queryResult);
            
            return queryResult;
            
        } catch (Exception e) {
            log.error("数据库查询失败，类型: {}, 业务ID: {}, 错误: {}", 
                    databaseConfig.getQueryType(), databaseConfig.getBusinessId(), e.getMessage());
            throw new RuntimeException("数据库查询失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 根据查询类型执行具体的数据库查询
     * 
     * @param databaseConfig 数据库配置
     * @return 查询结果
     */
    private Map<String, Object> executeQueryByType(DatabaseConfig databaseConfig) {
        String queryType = databaseConfig.getQueryType();
        
        try {
            // 获取对应的PollingQueryType枚举
            PollingQueryType pollingQueryType = PollingQueryType.valueOf(queryType);
            
            // 构建CustomPollingRequest（复用现有逻辑）
            CustomPollingRequest customRequest =
                    buildCustomPollingRequest(databaseConfig);
            
            // 执行查询
            Map<String, Object> result = pollingQueryType.executeQuery(customRequest);
            
            log.debug("查询类型 {} 执行成功，结果: {}", queryType, result);
            return result != null ? result : new HashMap<>();
            
        } catch (IllegalArgumentException e) {
            log.error("不支持的查询类型: {}", queryType);
            throw new RuntimeException("不支持的查询类型: " + queryType, e);
        } catch (Exception e) {
            log.error("执行查询类型 {} 时发生异常", queryType, e);
            throw new RuntimeException("查询执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建CustomPollingRequest对象
     * 将DatabaseConfig转换为CustomPollingRequest以复用现有查询逻辑
     * 
     * @param databaseConfig 数据库配置
     * @return CustomPollingRequest对象
     */
    private com.vedeng.temporal.domain.dto.CustomPollingRequest buildCustomPollingRequest(
            DatabaseConfig databaseConfig) {
        
        // 构建查询参数
        Map<String, Object> queryParameters = buildQueryParameters(databaseConfig);
        
        // 创建CustomPollingRequest
        return com.vedeng.temporal.domain.dto.CustomPollingRequest.builder()
                .businessId(databaseConfig.getBusinessId())
                .businessType("DATABASE_QUERY") // 标识这是数据库查询
                .companyCode(databaseConfig.getCompanyCode())
                .queryType(PollingQueryType.valueOf(databaseConfig.getQueryType()))
                .queryParameters(queryParameters)
                .build();
    }
    
    /**
     * 构建查询参数
     * 根据不同的查询类型构建相应的查询参数
     * 
     * @param databaseConfig 数据库配置
     * @return 查询参数Map
     */
    private Map<String, Object> buildQueryParameters(DatabaseConfig databaseConfig) {
        Map<String, Object> queryParameters = new HashMap<>();
        
        // 添加配置中的查询参数
        if (databaseConfig.getQueryParameters() != null) {
            queryParameters.putAll(databaseConfig.getQueryParameters());
        }
        
        // 根据查询类型添加特定参数
        String queryType = databaseConfig.getQueryType();
        
        switch (queryType) {
            case "TEMPORAL_FLOW_ORDER_QUERY":
                buildFlowOrderQueryParameters(queryParameters, databaseConfig);
                break;
                
            case "BASE_COMPANY_INFO_QUERY":
                buildCompanyInfoQueryParameters(queryParameters, databaseConfig);
                break;
                
            default:
                log.debug("使用默认查询参数构建，查询类型: {}", queryType);
                break;
        }
        
        log.debug("构建查询参数: {}", queryParameters);
        return queryParameters;
    }
    
    /**
     * 构建流程订单查询参数
     * 
     * @param queryParameters 查询参数Map
     * @param databaseConfig 数据库配置
     */
    private void buildFlowOrderQueryParameters(Map<String, Object> queryParameters, 
                                             DatabaseConfig databaseConfig) {
        // 设置查询类型列表
        if (!queryParameters.containsKey("queryTypes")) {
            // 默认查询销售订单和采购订单
            queryParameters.put("queryTypes", Arrays.asList("SALE_ORDER", "BUY_ORDER"));
        }
        
        // 设置公司信息
        if (StrUtil.isNotBlank(databaseConfig.getCompanyCode())) {
            queryParameters.put("currentCompany", databaseConfig.getCompanyCode());
        }
        
        if (StrUtil.isNotBlank(databaseConfig.getPreviousCompany())) {
            queryParameters.put("previousCompany", databaseConfig.getPreviousCompany());
        }
        
        log.debug("构建流程订单查询参数: {}", queryParameters);
    }
    
    /**
     * 构建公司信息查询参数
     * 
     * @param queryParameters 查询参数Map
     * @param databaseConfig 数据库配置
     */
    private void buildCompanyInfoQueryParameters(Map<String, Object> queryParameters, 
                                               DatabaseConfig databaseConfig) {
        // 设置公司代码
        if (StrUtil.isNotBlank(databaseConfig.getCompanyCode())) {
            queryParameters.put("companyCode", databaseConfig.getCompanyCode());
        }
        
        log.debug("构建公司信息查询参数: {}", queryParameters);
    }
    
    @Override
    public void clearQueryCache(Map<String, Object> context) {
        DatabaseConfig databaseConfig = (DatabaseConfig) context.get("databaseConfig");
        if (databaseConfig == null) {
            return;
        }
        
        log.debug("清理查询缓存，查询类型: {}, 业务ID: {}", 
                databaseConfig.getQueryType(), databaseConfig.getBusinessId());
        
        // 这里可以根据需要实现具体的缓存清理逻辑
        // 例如：清理Redis缓存、本地缓存等
        
        // TODO: 实现具体的缓存清理逻辑  
        log.debug("查询缓存清理完成");
    }
    
    @Override
    public String getExecutorName() {
        return "DatabaseQueryExecutor";
    }
}