package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 销售人员和客户业务数据关系表操作表
 */
@Getter
@Setter
public class RSalesJTraderLogEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 客户ID
     */
    private Integer traderCustomerId;

    /**
     * 销售人员ID
     */
    private Integer saleUserId;

    /**
     * 销售人员名称
     */
    private String saleUserName;

    /**
     * 操作类型 delete insert
     */
    private String operateType;

    /**
     * 是否删除
     */
    private Integer isDeleted;
}