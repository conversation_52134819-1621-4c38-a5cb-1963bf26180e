package com.vedeng.erp.saleorder.controller;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.newtask.data.dao.SaleorderDataMapper;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.aftersales.component.AfterSalesTypeEnum;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.aftersales.service.WebAccountService;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.MethodLockParam;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.*;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.ExcludeAuthorization;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.imageupload.ChainResult;
import com.vedeng.common.core.utils.imageupload.ImgUploadVerifyActuator;
import com.vedeng.common.exception.BusinessException;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.FileInfo;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.orderstrategy.OrderAmountStrategy;
import com.vedeng.common.page.Page;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.common.util.*;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.dto.ExpenseBuyForSaleDetail;
import com.vedeng.erp.buyorder.dto.StatusNode;
import com.vedeng.erp.buyorder.service.RBuyorderExpenseJSaleorderService;
import com.vedeng.erp.finance.service.InvoiceApplyApiService;
import com.vedeng.erp.saleorder.api.QuoteInfoService;
import com.vedeng.erp.saleorder.buzlogic.create.CancelValidSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.buzlogic.create.CreateSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.buzlogic.create.EditSaleOrderBuzLogic;
import com.vedeng.erp.saleorder.buzlogic.create.impl.*;
import com.vedeng.erp.saleorder.constant.AuditTypeEnumOfSaleOrder;
import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.dao.QuSaleHistMapper;
import com.vedeng.erp.saleorder.dto.*;
import com.vedeng.erp.saleorder.enums.OrderStatusEnum;
import com.vedeng.erp.saleorder.model.dto.QuSaleHist;
import com.vedeng.erp.saleorder.model.dto.SaleContractDto;
import com.vedeng.erp.saleorder.model.query.GoodsQuery;
import com.vedeng.erp.saleorder.service.*;
import com.vedeng.erp.saleorder.strategy.OrderTerminalContext;
import com.vedeng.erp.saleorder.strategy.SaleOrderTerminalStrategy;
import com.vedeng.erp.saleorder.vo.AuditRecordDetailsVo;
import com.vedeng.erp.saleorder.vo.AuditRecordInstanceVo;
import com.vedeng.erp.saleorder.vo.SaleOrderProcessNode;
import com.vedeng.erp.system.api.SyncOutInRelateApiService;
import com.vedeng.erp.system.dto.SyncOutInRelateDto;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.Invoice;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.finance.service.TraderAccountPeriodApplyService;
import com.vedeng.flash.dto.SpecialDeliveryInfoDto;
import com.vedeng.goods.model.*;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.service.*;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.logistics.dao.ConfirmationBatchesRelationMapper;
import com.vedeng.logistics.dao.ConfirmationFormRecodeMapper;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.model.*;
import com.vedeng.logistics.model.Logistics;
import com.vedeng.logistics.model.vo.BatchExpressVo;
import com.vedeng.logistics.model.vo.SaleOrderConfirmBatchExpressVo;
import com.vedeng.logistics.service.*;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.dao.SaleorderModifyApplyGoodsMapper;
import com.vedeng.order.dao.SaleorderModifyApplyMapper;
import com.vedeng.order.enums.SaleOrderTypeEnum;
import com.vedeng.order.model.*;
import com.vedeng.order.model.dto.GoodsBelong;
import com.vedeng.order.model.dto.JcTraderContactDto;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.order.model.dto.RemarkComponentDto;
import com.vedeng.order.model.query.LabelQuery;
import com.vedeng.order.model.vo.*;
import com.vedeng.order.service.*;
import com.vedeng.price.api.price.dto.price.PriceInfoResponseDto;
import com.vedeng.price.dto.ContractPriceInfoDetailResponseDto;
import com.vedeng.price.dto.GoodSalePrice;
import com.vedeng.price.dto.SkuPriceInfoDetailResponseDto;
import com.vedeng.price.service.BasePriceService;
import com.vedeng.price.service.PriceInfoDealWithService;
import com.vedeng.system.dao.VerifiesInfoMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.VerifiesInfo;
import com.vedeng.system.service.*;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderContactMapper;
import com.vedeng.trader.dto.TraderContactDto;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.*;
import com.vedeng.trader.service.TraderContactService;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.service.LogicalSaleorderChooseService;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.history.HistoricVariableInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.datatransfer.Clipboard;
import java.awt.datatransfer.StringSelection;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.vedeng.common.constant.OrderConstant.ORDER_TYPE_BD;
import static com.vedeng.common.constant.OrderConstant.ORDER_TYPE_HC;


/**
 * NewSaleorderController
 */
@Controller
@RequestMapping("/orderstream/saleorder")
public class NewSaleorderController extends BaseController {

    public static Logger logger = LoggerFactory.getLogger(NewSaleorderController.class);

    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private SaleOrderServiceFactory saleOrderServiceFactory;

    @Autowired
    @Qualifier("confirmationBatchesRelationMapper")
    private ConfirmationBatchesRelationMapper confirmationBatchesRelationMapper;

    @Autowired
    private AttachmentMapper attachmentMapper;

    @Resource
    private TraderCustomerService traderCustomerService;

    @Resource
    private RegionService regionService;

    @Resource
    private SaleorderService saleorderService;

    @Resource
    private UserService userService;

    @Resource
    private TraderContactService traderContactService;

    @Resource
    private OrgService orgService;

    @Resource
    private RemarkComponentService remarkComponentService;

    @Resource
    private GoodsSettlementPriceService goodsSettlementPriceService;

    @Resource
    private GoodsChannelPriceService goodsChannelPriceService;

    @Resource
    private SkuAuthorizationService skuAuthorizationService;

    @Resource
    private RiskCheckService riskCheckService;

    @Resource
    private VgoodsService vGoodsService;

    @Resource
    private PriceInfoDealWithService priceInfoDealWithService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private UnitService unitService;

    @Autowired
    private TraderAccountPeriodApplyService traderAccountPeriodApplyService;

    @Value("${retention_money_uptime}")
    private Long retentionMoneyUptime;

    @Autowired
    @Qualifier("confirmationFormRecodeService")
    protected ConfirmationFormRecodeService confirmationFormRecodeService;

    @Autowired
    private CapitalBillService capitalBillService;

    @Autowired
    private QuoteService quoteService;


    @Value("${GE_TRADER_SKU}")
    protected String geTraderSku;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private OrderPeerApiService orderPeerApiService;

    @Autowired
    private AfterSalesService afterSalesService;

    @Resource
    private BrandService brandService;
    @Resource
    private ImgUploadVerifyActuator imgUploadVerifyActuator;

    @Resource
    private OssUtilsService ossUtilsService;

    @Resource
    private BasePriceService basePriceService;

    @Resource
    private VgoodsService vgoodsService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    private TraderContactMapper traderContactMapper;
    @Autowired
    @Qualifier("traderAddressMapper")
    private TraderAddressMapper traderAddressMapper;


    @Resource
    private AuthService authService;
    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    protected VerifiesRecordService verifiesRecordService;

    @Resource
    private VsCreateSaleOrderBuzLogic vsCreateSaleOrderBuzLogic;

    @Resource
    private VsEditSaleOrderBuzLogic vsEditSaleOrderBuzLogic;

    @Resource
    private HcEditSaleOrderBuzLogic hcEditSaleOrderBuzLogic;

    @Resource
    private EditSaleOrderBuzLogic editSaleOrderBuzLogic;

    @Resource
    private ZxfEditSaleOrderBuzLogic zxfEditSaleOrderBuzLogic;

    @Resource
    private ZxfCreateSalesOrderBuzLogic zxfCreateSalesOrderBuzLogic;

    @Autowired
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Autowired
    private VerifiesInfoMapper verifiesInfoMapper;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Autowired
    private OrderCommonService orderCommonService;


    @Resource
    private LogisticsService logisticsService;

    @Autowired
    private LogicalSaleorderChooseService logicalSaleorderChooseServiceImpl;

    @Resource
    private OrderAmountStrategy orderAmountStrategy;

    @Autowired
    private SaleorderCouponService saleorderCouponService;

    @Resource
    private CreateSaleOrderBuzLogic createSaleOrderBuzLogic;

    @Resource
    private JcfCreateSaleOrderBuzLogic jcfCreateSaleOrderBuzLogic;

    @Autowired
    private WarehouseOutService warehouseOutService;


    @Autowired
    private JcOrderService jcOrderService;

    @Autowired
    private JcfEditSaleOrderBuzLogic jcfEditSaleOrderBuzLogic;

    @Autowired
    private JcfCancelValidSaleOrderBuzLogic jcfCancelValidSaleOrderBuzLogic;

    @Autowired
    private CancelValidSaleOrderBuzLogic cancelValidSaleOrderBuzLogic;

    @Autowired
    private RePurchaseCreateSaleOrderBuzLogic rePurchaseCreateSaleOrderBuzLogic;

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    @Resource
    private ElectronicSignatureService electronicSignatureService;

    @Autowired
    private PendingOrderService pendingOrderService;

    @Value("${confirm_order_url}")
    private String confirmOrderUrl;

    @Resource
    private WebAccountService webAccountService;

    @Resource
    OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;

    @Resource
    ExpressMapper expressMapper;

    @Resource
    ConfirmationFormRecodeMapper confirmationFormRecodeMapper;

    @Resource
    SaleorderDataMapper saleorderDataMapper;

    @Autowired
    ActivityPreOrderService activityPreOrderService;

    @Autowired
    SaleOrderGoodsApiService saleOrderGoodsApiService;

    @Autowired
    RBuyorderExpenseJSaleorderService rBuyorderExpenseJSaleorderService;

    @Value("${confirm_online_timestamp}")
    private Long confirmOnlineTimestamp;

    @Resource
    private QuSaleHistMapper quSaleHistMapper;

    @Resource
    private QuoteInfoService quoteInfoService;

    @Resource
    private NewSaleOrderService newSaleOrderService;

    @Autowired
    private OrderTerminalApiService orderTerminalApiService;

    @Autowired
    private SaleOrderTerminalStrategy saleOrderTerminalStrategy;


    @Autowired
    private OnlineInvoiceOpenService onlineInvoiceOpenService;

    @Autowired
    SaleorderModifyApplyMapper saleorderModifyApplyMapper;

    @Autowired
    SaleorderModifyApplyGoodsMapper saleorderModifyApplyGoodsMapper;

    @Autowired
    private InvoiceApplyApiService invoiceApplyApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Autowired
    private OrderAdditionalClauseApiService orderAdditionalClauseApiService;

    /**
     * 电子签章页面
     * @param request request
     * @param saleOrder 订单
     * @param mv mv
     * @return mv
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping("/electronicSignature")
    public ModelAndView electronicSignature(HttpServletRequest request,
                                            Saleorder saleOrder,
                                            ModelAndView mv){
        mv.setViewName("orderstream/saleorder/electronic_signature");
        try {
            electronicSignatureService.electronicSignature(request, saleOrder, mv);
        }catch (Exception e){
            return fail(mv);
        }
        return mv;
    }


    /**
     * 新增电子签章
     * @param saleOrder 订单
     * @param mv 视图
     * @return 视图
     */
    @NoNeedAccessAuthorization
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping("/addElectronicSignature")
    public ModelAndView addElectronicSignature(Saleorder saleOrder,
                                               ModelAndView mv){
        mv.addObject("saleorder", saleOrder);
        mv.setViewName("orderstream/saleorder/add_electronic_signature");
        try {
            electronicSignatureService.addElectronicSignature(saleOrder,mv);
        }catch (Exception e){
            log.error("【addElectronicSignature】处理异常",e);
            return fail(mv);
        }
        return mv;
    }

    /**
     * 保存电子签章
     * @param request
     * @param saleorder
     * @return
     */
    @NoNeedAccessAuthorization
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveElectronicSignature")
    public ResultInfo saveElectronicSignature(HttpServletRequest request, Saleorder saleorder) {
        try {
            Integer result = electronicSignatureService.saveElectronicSignature(request, saleorder);
            if (ErpConst.ONE.equals(result)) {
                return ResultInfo.error("订单状态异常，请返回查看订单状态");
            }
            if (ErpConst.TWO.equals(result)) {
                return ResultInfo.error("该订单合同在审核中或已审核通过，无法生成链接");
            }
            return ResultInfo.success();
        }catch (Exception e){
            log.error("【saveElectronicSignature】处理异常",e);
            return ResultInfo.error();
        }
    }

    /***
     * 获取销售订单下确认单信息 (审核用)
     * @param session
     * @param saleOrderId
     * @return
     */
    @ExcludeAuthorization
    @ResponseBody
    @RequestMapping(value = "/getSaleOrderConfiremInformation")
    public ResultInfo getSaleOrderConfiremInformation(HttpSession session, Integer saleOrderId) {
        if (saleOrderId == null) {
            return ResultInfo.error("没有传递销售订单Id");
        }
        try {
            SaleOrderConfirmBatchExpressVo saleOrderConfiremInformation = expressService.getSaleOrderConfiremInformation( session,saleOrderId);
            if (saleOrderConfiremInformation.getErroMessage()!=null&&StringUtil.isNotEmpty(saleOrderConfiremInformation.getErroMessage())){
                return ResultInfo.error(saleOrderConfiremInformation.getErroMessage());
            }
        return ResultInfo.success(saleOrderConfiremInformation);
        }catch (Exception e){
            log.error("【getSaleOrderConfiremInformation】处理异常",e);
            return ResultInfo.error("服务出错了，请反馈");
        }
    }




    /***
     * 获取确认单回传新增页面详情
     * @param
     * @param
     * @return
     */
    @ExcludeAuthorization
    @ResponseBody
    @RequestMapping(value = "/getSaleOrderOutboundBatchesRecode")
    public ResultInfo getSaleOrderOutboundBatchesRecode(HttpSession session, Integer saleOrderId) {
        if (saleOrderId == null) {
            return ResultInfo.error("没有传递销售订单Id");
        }
//        try {
        List<OutboundBatchesRecode> outboundBatchesRecodes = expressService.getSaleOrderOutboundBatchesRecode(session,saleOrderId);
        return ResultInfo.success(outboundBatchesRecodes);
//        }catch (Exception e){
//            return ResultInfo.error("服务出错了，请反馈");
//        }
    }



    /***
     * 上传确认单
     * @param
     * @param
     * @return
     */
    @ExcludeAuthorization
    @ResponseBody
    @RequestMapping(value = "/uploadConfirmationForm")
    public ResultInfo uploadConfirmationForm(HttpSession session,@RequestBody ConfirmationFormRecode confirmationFormRecode,HttpServletRequest request) {
        // 当前登陆用户
        CurrentUser user = CurrentUser.getCurrentUser();
        if (0 == confirmationFormRecode.getSaleOrderId() || confirmationFormRecode.getSaleOrderId() == null){
            log.info("上传确认单失败，无订单id");
            return ResultInfo.error();
        }
        log.info("开始上传确认单,订单id为:{}",confirmationFormRecode.getSaleOrderId());
        Boolean yn = confirmationFormRecodeService.uploadConfirmationForm(user,confirmationFormRecode,request);
        log.info("上传确认单完毕,订单id为:{}",confirmationFormRecode.getSaleOrderId());
        if (!yn){
            return ResultInfo.error("上传失败,当前用户不是销售或者物流人员");
        }
        return ResultInfo.success();
    }





    /**
     * 处理确认单审批结果
     * @return
     */
    @ResponseBody
    @ExcludeAuthorization
    @RequestMapping(method = RequestMethod.POST,value = "/checkConfirmationDetail")
    public ResultInfo checkConfirmationDetail(@RequestBody ConfirmationReplyMsgDto replyMsg,HttpServletRequest request){
        if(replyMsg.getSaleOrderId()==null){
            logger.error("checkConfirmationDetail id null {}" ,request.getHeader("REFERER"));
            return ResultInfo.error();
        }
        try {
            saleorderService.checkConfirmationDetail(request,replyMsg);
        } catch (BusinessException e) {
            log.error("【checkConfirmationDetail】 error",e);
            return ResultInfo.error(e.getMessageCode());
        }
        return ResultInfo.success();
    }
    /**
     *
     * @param request
     * @param traderCustomerVo
     * @param pageNo
     * @param pageSize
     * @param traderType
     * @param orderType
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addOrder")
    public ModelAndView searchCustomerList(HttpServletRequest request, TraderCustomerVo traderCustomerVo,
                                           @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                           @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                           @RequestParam(required = false) Integer traderType,
                                           @RequestParam(required = false) Integer orderType) {
        ModelAndView mv = new ModelAndView();
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        String linename = request.getParameter("searchTraderName");
        if (StringUtils.isNotBlank(linename)){
            try {
                //java : 字符解码
                traderCustomerVo.setSearchTraderName((java.net.URLDecoder.decode(java.net.URLDecoder.decode(linename, "UTF-8"), "UTF-8")).trim());
                traderCustomerVo.setCompanyId(user.getCompanyId());
                traderCustomerVo.setIsEnable(ErpConst.ONE);

                List<Integer> userIds = userService.getAllSubordinateByUserId(user.getUserId());
                traderCustomerVo.setUserIdList(userIds);
                Page page = getPageTag(request, pageNo, pageSize);
                List<TraderCustomerVo> searchCustomerList = traderCustomerService.searchTraderCustomerListPage(traderCustomerVo, page);
                mv.addObject("page", page);
                mv.addObject("searchCustomerList", searchCustomerList);
                mv.addObject("traderType", traderType);
                mv.addObject("traderCustomerVo", traderCustomerVo);
                mv.addObject("pageType", ErpConst.ONE);
//                mv.addObject("orderType", orderType);
            } catch (Exception e) {
                logger.error("searchCustomerList 客户名称:" + linename + ",错误：", e);
            }
        }
        mv.setViewName("orderstream/saleorder/customer_info");
        return mv;
    }


    /**
     * 销售订单详情页
     * @param saleOrderId 销售订单id
     * @return 销售订单详情页
     */
    @RequestMapping(value = "/detail",method = RequestMethod.GET)
    @FormToken(save = true)
    @NoNeedAccessAuthorization
    public ModelAndView orderDetail(HttpServletRequest request, HttpServletResponse response, @RequestParam Integer saleOrderId) throws IOException {
        ModelAndView mv = new ModelAndView("orderstream/saleorder/detail");
        User currentUser = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        mv.addObject("currentUser",currentUser);
        Boolean showButtonApplyInvoice = invoiceApplyApiService.showButtonApplyInvoice(saleOrderId);
        mv.addObject("showButtonApplyInvoice",showButtonApplyInvoice);
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
        Saleorder saleOrderBaseInfo = saleOrderService.getSaleOrderBaseInfo(saleOrderId);
        boolean beforeConfOnline = true;
        Boolean needShowConfirm = true;
        //VDERP-13676 所有销售订单均需确认'确认单审核状态'和'合同回传状态'
        if (Objects.nonNull(saleOrderBaseInfo)) {
            if (confirmOnlineTimestamp != null && (confirmOnlineTimestamp.compareTo(saleOrderBaseInfo.getAddTime()) < 0)) {
                beforeConfOnline = false;
                needShowConfirm = false;
                //若订单满足开票条件，不显示申请开票确认弹框
                Integer confirmationFormAudit = saleorderMapper.confirmationFormAuditBySaleOrderId(saleOrderId);
                Integer contractVerifyStatus = saleorderDataMapper.contractVerifyStatusBySaleOrderId(saleOrderId);
                if (Objects.nonNull(confirmationFormAudit) && contractVerifyStatus != null && 2 == confirmationFormAudit && 1 == contractVerifyStatus) {
                    mv.addObject("statusAllCheck", true);
                }
            }
        }
        mv.addObject("beforeConfOnline",beforeConfOnline);
        boolean checkorg = userService.handleOrgIds(currentUser);
        if (!checkorg){
            response.sendRedirect("/order/saleorder/view.do?saleorderId="+saleOrderBaseInfo.getSaleorderId());
            return null;
        }


        //基本信息
        Saleorder querySaleorder = new Saleorder();
        querySaleorder.setSaleorderId(saleOrderId);
        querySaleorder.setOptType("orderDetail");
        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(querySaleorder);
        User saleUser = userService.getUserById(saleorderInfo.getUserId());
        //当前用户是否为归属销售或上级
        if(currentUser.getUserId().equals(saleUser.getParentId()) || currentUser.getUserId().equals(saleUser.getUserId())){
            mv.addObject("isParent",1);
        }
        saleorderInfo.setCreatorName(saleOrderBaseInfo.getCreatorName());
        saleorderInfo.setOptUserName(saleOrderBaseInfo.getOptUserName());
        saleorderInfo.setSalesDeptName(saleOrderBaseInfo.getSalesDeptName());

        //查看订单的审核状态


        mv.addObject("orderCheckStatus",saleOrderService.getSaleorderCheckStatusFromVerifiesInfo("T_SALEORDER",saleOrderId,623,saleOrderBaseInfo));

        saleOrderService.getSaleOrderProcessNodeList(mv,saleOrderId);

        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleOrderId);

        int isNotDelPriceZero = 0;
        switch (SalesOrderTypeEnum.getInstance(saleorderInfo.getOrderType())){
            case HC:{
                for (SaleorderGoods item : saleorderGoods){
                    if(saleorderInfo.getTotalAmount().compareTo(new BigDecimal(199)) >= 0){
                        //耗材总金额大于199，运费为0
                        if (BigDecimal.ZERO.compareTo(item.getPrice()) == 0 && !"V127063".equals(item.getSku())){
                            isNotDelPriceZero = 1;
                            break;
                        }
                    }else {
                        if (BigDecimal.ZERO.compareTo(item.getPrice()) == 0){
                            isNotDelPriceZero = 1;
                            break;
                        }
                    }
                }
                break;
            }
            case JCF:
            case JCO:{
                TraderCustomerVo groupCustomer = traderCustomerService.getCustomerBussinessInfo(saleorderInfo.getGroupCustomerId() == null ?
                        saleorderInfo.getTraderId() : saleorderInfo.getGroupCustomerId());
                mv.addObject("groupCustomer", groupCustomer);// 客户信息
                //赋值不满足票货同行条件
                saleorderInfo.setReasonNo(saleorderService.getIsSameAddressNoRe(saleorderInfo));
                break;
            }
            default:{
                for (SaleorderGoods item : saleorderGoods){
                    if (BigDecimal.ZERO.compareTo(item.getPrice()) == 0){
                        isNotDelPriceZero = 1;
                        break;
                    }
                }
                break;
            }
        }
        mv.addObject("isNotDelPriceZero",isNotDelPriceZero);

        riskCheckService.setSaleorderIsRiskInfo(saleorderInfo, saleorderGoods);
        Integer saleorderIsRisk = saleorderInfo.getIsRisk();
        Integer riskFlag = riskCheckService.getRiskFlag(currentUser, saleorderIsRisk);
        //设置合同下载地址
        String contractUrl = "";
        if(saleorderInfo.getContractUrl() != null && !"".equals(saleorderInfo.getContractUrl())){
            //有合同章
            contractUrl = saleorderInfo.getContractUrl().replace("display","download");
        }else {
            if(saleorderInfo.getContractNoStampUrl() != null && !"".equals(saleorderInfo.getContractNoStampUrl())){
                contractUrl = saleorderInfo.getContractNoStampUrl().replace("display","download");
            }
        }
        saleorderInfo.setNeedShowConfirm(needShowConfirm);
        logger.info("订单号{},合同下载地址{},",saleorderInfo.getSaleorderNo(),contractUrl);
        mv.addObject("contractUrl",contractUrl);
        mv.addObject("saleorder",saleorderInfo);

        //注册用户id
        Integer erpAccountId = webAccountService.getWebAccountIdByMobile(saleorderInfo.getCreateMobile());
        mv.addObject("erpAccountId",erpAccountId);

        mv.addObject("saleorderGoods",saleorderGoods);
        mv.addObject("riskFlag",riskFlag);

        try {
            QuoteLinkBdLog quoteLinkBdLogQuery = new QuoteLinkBdLog();
            quoteLinkBdLogQuery.setBdOrderId(saleOrderId);
            quoteLinkBdLogQuery.setIsEnable(ErpConst.ONE);
            mv.addObject("quoteLinkBdLog", quoteService.getQuoteLinkBdLogByInfo(quoteLinkBdLogQuery) != null);
        } catch (Exception e) {
            logger.error("order getQuoteLinkBdLog warn saleOrderId:{}", saleOrderId, e);
        }

        /**
         * 客户在线签收信息
         */
        try {
            if (saleorderInfo.getDeliveryStatus() > 0 && orgService.isBelongByOrgNameAndTraderId(saleorderInfo, ErpConst.B2B_BUSINESS_UNIT)) {
                mv.addObject("showExpressReceiptInfo", 1);
                mv.addObject("expressOnlineReceiptList", saleorderService.getExpressOnlineReceiptListByOrderId(saleOrderId));
            }
        } catch (Exception e) {
            logger.error("检索客户在线签收订单消息异常 saleOrderId:{}, e:{}", saleOrderId, e);
        }

        // 获取交易信息（订单实际金额，客户已付款金额）
        Map<String, BigDecimal> saleorderDataInfo = saleorderService.getSaleorderDataInfo(saleOrderId);
//        BigDecimal awardAmount = saleorderService.getSaleorderPriceInfo(saleOrderId);
//        saleorderDataInfo.put("awardAmount",awardAmount==null?new BigDecimal(0.00):awardAmount);
        //新订单流详情页，订单实际金额计算只排除已完结的销售售后单
        saleorderDataInfo.put("realAmount",saleOrderService.getRealTotalAmountOfSaleOrder(saleOrderId));
        mv.addObject("saleorderDataInfo", saleorderDataInfo);



        //订单审核记录
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "saleorderVerify_" + saleOrderId);
        mv.addObject("taskInfo", historicInfo.get("taskInfo"));
        mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        List<String> verifyUserList = new ArrayList<>();
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            String verifyUser = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUserList"));
            if (null != verifyUser) {
                verifyUserList = Arrays.asList(verifyUser.split(","));
            }
        }
        mv.addObject("verifyUserList", verifyUserList);

        // 展示发送签收通知按钮  1
        Saleorder saleorderVo = new Saleorder();
        BeanUtils.copyProperties(saleOrderBaseInfo,saleorderVo);
        // saleorderVo.setValidTime(DateUtil.StringToLong("2021-12-20 00:00:00"));
        Boolean pendingOrderFlag = saleorderService.getPendingOrderBySaleorder(saleorderVo);
        mv.addObject("pendingOrderFlag",pendingOrderFlag);

        //判断当前用户是否是质量专员
        Boolean qualityFlagByUserId = userService.getQualityFlagByUserId(currentUser);
        if (qualityFlagByUserId){
            mv.addObject("qualityFlagByUserId", 1);
        }else {
            mv.addObject("qualityFlagByUserId", 0);
        }

        // 特殊商品集合
        List<Integer> specialSkuIdList = getSysOptionDefinitionList(SysOptionConstant.SPECIAL_SKU)
                .stream()
                .map(SysOptionDefinition::getComments)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
        mv.addObject("specialSkuIdList", specialSkuIdList);

        //客户信息
        getCustomerInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //报价咨询
        getQuoteConsultInfoOfSaleOrder(mv,saleOrderBaseInfo,currentUser);

        //收货信息、收票信息、终端信息、付款计划、其他信息
        getDeliveryInfoOfSaleOrder(mv);

        //订单商品
        getGoodsInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //沟通记录
        getCommunicationRecordInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //开票申请
        getInvoiceApplyInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //交易信息
        getDealInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //发票信息
        getInvoiceInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //物流信息
        getLogisticsInfoOfSaleOrder(mv);

        //订单申请修改
        getModifyApplyInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //合同回传和送货单回传
        getContractReturnInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //录保卡
        getGoodsWarrantiesOfSaleOrder(mv,saleOrderBaseInfo);

        //售后列表
        getAfterSalesInfoOfSaleOrder(mv,saleOrderBaseInfo);

        //优惠卷信息
        getCouponInfo(mv,saleorderInfo);

        //确认单审核记录
        getConfirmationList(mv,saleOrderBaseInfo);

        //附加条款（新）
        getAdditionalClause(mv,saleorderInfo);

        return mv;
    }

    @RequestMapping(value = "/additionalClause",method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public ModelAndView additionalClause(HttpServletRequest request, HttpServletResponse response  ) throws IOException {
        ModelAndView mv = new ModelAndView("vue/view/saleorder/additional_clause");
        return mv;
    }




    private void getAdditionalClause(ModelAndView mv, Saleorder saleorderInfo) {
        SaleorderAdditionalClauseDto queryDto=new SaleorderAdditionalClauseDto();
        queryDto.setSaleorderId(saleorderInfo.getSaleorderId());
        SaleorderAdditionalClauseDto result=orderAdditionalClauseApiService.getSaleorderAdditionalClauseByOrderId(queryDto);
        saleorderInfo.setAdditionalClause(result.getAdditionalClauseFinalShow());
    }

    private void getConfirmationList(ModelAndView mv, Saleorder saleOrderBaseInfo) {
        List<ConfirmationDto> confirmationList = new ArrayList<>();
        Express express = new Express();
        Express exQueryVo = new Express();
        exQueryVo.setSaleorderId(saleOrderBaseInfo.getSaleorderId());
        Integer saleorderId = saleOrderBaseInfo.getSaleorderId();
        express.setSaleorderId(saleorderId);
        List<Express> expressList = expressMapper.getExpressInfoConfirmation(express);
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleorderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            exQueryVo.setRelatedIds(listSale);
            buyExpressList = expressMapper.getBuyExpressList(exQueryVo);
        }
        expressList.addAll(buyExpressList);
        List<String> batchNos = expressList.stream().map(Express::getBatchNo).distinct().collect(Collectors.toList());
        if (batchNos.size()>0){
            List<OutboundBatchesRecode> allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNos);
            allByBatchNoList.forEach(o -> {
                //只展示审核状态为驳回和通过和审核中的记录
                if (o.getAuditStatus()!=null && (0 == o.getAuditStatus() || 1 == o.getAuditStatus() || 2 == o.getAuditStatus())){
                    ConfirmationDto dto = new ConfirmationDto();
                    //普发
                    if(o.getBatchType().equals(1)){
                        Saleorder saleorder = new Saleorder();
                        saleorder.setBussinessType(2);
                        saleorder.setSaleorderId(saleorderId);
                        saleorder.setBatchNoComments(o.getBatchNo());
                        List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getOutDetil(saleorder);
                        if (warehouseOutList.size()>0){
                            Long addTime = warehouseOutList.get(0).getAddTime();
                            dto.setBatchTime(DateUtil.convertString(addTime,null));
                        }
                    } else {
                        //直发
                        dto.setBatchTime(DateUtil.convertString(o.getDeliveryTime(),"yyyy-MM-dd"));
                    }

                    if (StringUtils.isBlank(o.getComments())){
                        o.setComments("");
                    }
                    dto.setAuditStatus(o.getAuditStatus())
                            .setBatchId(o.getId())
                            .setBatchNo(o.getBatchNo())
                            .setModTime(o.getModTime())
                            .setModTimeLabel(DateUtil.convertString(o.getModTime(),null));
                    if (o.getAuditStatus()!=null && o.getAuditStatus().equals(0)){
                        dto.setComments(o.getComments());
                    }else {
                        dto.setComments("");
                    }
                    //确认单名称
                    List<String> confirmationNameList = confirmationFormRecodeMapper.getConfirmationNameByBatchNo(o.getBatchNo());
                        String confirmationName = String.join(",", confirmationNameList);
                        dto.setConfirmationName(confirmationName);
                    confirmationList.add(dto);
                }
            });
        }
        //排序
        List<ConfirmationDto> collect = confirmationList.stream()
                .sorted(Comparator.comparing(ConfirmationDto::getModTime)
                        .thenComparing(ConfirmationDto::getConfirmationName))
                .collect(Collectors.toList());
        mv.addObject("confirmationList",collect);
    }


    /**
     * 销售订单详情页
     * @param saleorderNo 销售订单id
     * @return 销售订单详情页
     */
    @RequestMapping(value = "/detailByNo",method = RequestMethod.GET)
    @FormToken(save = true)
    @NoNeedAccessAuthorization
    public void orderDetail(HttpServletRequest request, HttpServletResponse response, @RequestParam String saleorderNo) throws IOException {

        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(saleorderNo);

        response.sendRedirect("/orderstream/saleorder/detail.do?saleOrderId="+saleorder.getSaleorderId());

    }

    @RequestMapping("/queryTraderGroup")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo queryTraderGroup(@RequestParam List<Integer> traderIds){
        //客户分群信息
        try {
            return ResultInfo.success(getTraderGroup(traderIds));
        } catch (Exception e) {
           log.info("查询客户分群信息失败，{}",traderIds.toString());
        }
        return ResultInfo.error("查询客户分群信息失败！");
    }

    /**
     * @Desc 查询销售的优惠卷信息
     * <AUTHOR>
     * @param mv
     * @param saleorder
     */
    private void getCouponInfo(ModelAndView mv, Saleorder saleorder){
        mv.addObject("saleorderCoupon",saleorderCouponService.selectBySaleorderId(saleorder.getSaleorderId()));
    }
    /**
     * 查询订单详情页里客户的基本信息
     * @param mv 销售订单视图
     * @param saleorder 销售订单信息
     */
    private void getCustomerInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        TraderCustomerVo customerVo = traderCustomerService.getCustomerBussinessInfo(saleorder.getTraderId());
        mv.addObject("customer",customerVo);
        TraderCustomerVo invoiceCustomerVo = traderCustomerService.getCustomerBussinessInfo(saleorder.getInvoiceTraderId());
        mv.addObject("invoiceCustomer",invoiceCustomerVo);
        TraderCustomerVo takeCustomerVo = traderCustomerService.getCustomerBussinessInfo(saleorder.getTakeTraderId());
        mv.addObject("takeCustomer",takeCustomerVo);
        if (saleorder.getOrderType().equals(0) || saleorder.getOrderType().equals(1) || saleorder.getOrderType().equals(4)){
            //客户分群信息
            Map<Integer, RTraderGroupJTrader> traderGroupMap = getTraderGroup(Collections.singletonList(saleorder.getTraderId()));
            mv.addObject("traderGroupMap", traderGroupMap);
        }

        //客户资质信息
        Map<String, Object> map = traderAccountPeriodApplyService.getTraderAptitudeInfo(saleorder.getTraderId());
        if (map != null) {
            TraderCertificateVo bus;
            // 营业执照信息
            if (map.containsKey("business")) {
                JSONObject json = JSONObject.fromObject(map.get("business"));
                bus = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mv.addObject("business", bus);
            }
            // 税务登记信息
            TraderCertificateVo tax;
            if (map.containsKey("tax")) {
                JSONObject json = JSONObject.fromObject(map.get("tax"));
                tax = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mv.addObject("tax", tax);
            }
            // 组织机构信息
            TraderCertificateVo orga;
            if (map.containsKey("orga")) {
                JSONObject json = JSONObject.fromObject(map.get("orga"));
                orga = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                mv.addObject("orga", orga);
            }
            // 客户
            mv.addObject("customerProperty", saleorder.getCustomerNature());
            if (saleorder.getCustomerNature() == 465) {// 分销
                // 二类医疗资质
                List<TraderCertificateVo> twoMedicalList;
                if (map.containsKey("twoMedical")) {
                    twoMedicalList = (List<TraderCertificateVo>) map.get("twoMedical");
                    mv.addObject("twoMedicalList", twoMedicalList);
                }
                // 二类医疗资质详情
                List<TraderMedicalCategoryVo> list;
                if (map.containsKey("medicalCertificate")) {
                    JSONArray jsonArray = JSONArray.fromObject(map.get("medicalCertificate"));
                    list = (List<TraderMedicalCategoryVo>) JSONArray.toCollection(jsonArray,
                            TraderMedicalCategoryVo.class);
                    mv.addObject("medicalCertificates", list);
                }
                // 三类医疗资质
                TraderCertificateVo threeMedical;
                if (map.containsKey("threeMedical")) {
                    JSONObject json = JSONObject.fromObject(map.get("threeMedical"));
                    threeMedical = (TraderCertificateVo) JSONObject.toBean(json, TraderCertificateVo.class);
                    mv.addObject("threeMedical", threeMedical);
                }

                if (map.containsKey("threeMedicalList")) {
                    JSONArray jsonArray = JSONArray.fromObject(map.get("threeMedicalList"));
                    List<TraderCertificateVo>  threeMedicalList = (List<TraderCertificateVo>) JSONArray.toCollection(jsonArray,
                            TraderCertificateVo.class);
                    mv.addObject("threeMedicalList", threeMedicalList);
                }
            } else {// 终端
                // 医疗机构执业许可证
                List<TraderCertificateVo> practiceList;
                if (map.containsKey("practice")) {
                    practiceList = (List<TraderCertificateVo>) map.get("practice");
                    mv.addObject("practiceList", practiceList);
                }
            }
        }


    }


    /**
     * 查询订单详情页的收货、收票信息
     * @param mv 订单详情页视图
     */
    private void getDeliveryInfoOfSaleOrder(ModelAndView mv){
        // 付款方式列表
        List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
        mv.addObject("paymentTermList", paymentTermList);

        // 发货方式
        List<SysOptionDefinition> deliveryTypes = getSysOptionDefinitionList(480);
        mv.addObject("deliveryTypes", deliveryTypes);

        // 终端性质
        List<SysOptionDefinition> terminalTraderNatureList = getSysOptionDefinitionList(5600);
        mv.addObject("terminalTraderNatureList", terminalTraderNatureList);

        // 获取物流公司列表
        List<Logistics> logisticsList = getLogisticsList(ErpConst.NJ_COMPANY_ID);
        mv.addObject("logisticsList", logisticsList);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(469);
        mv.addObject("freightDescriptions", freightDescriptions);
        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);

        //其他信息模块的订货备注
        //订单商品是否有冷冻和加急商品
        List<SaleorderGoods> saleorderGoods = (List<SaleorderGoods>) mv.getModel().get("saleorderGoods");
        int isUrgent = 0, isCold = 0;
        for (SaleorderGoods item : saleorderGoods){
            if ("V251526".equals(item.getSku())){
                isUrgent = 1;
            } else if ("V256675".equals(item.getSku())){
                isCold = 1;
            }
        }
        mv.addObject("isUrgent",isUrgent);
        mv.addObject("isCold",isCold);

        //上线之前的付款结构兼容尾款功能
        mv.addObject("retentionMoneyUptime", retentionMoneyUptime);
    }


    /**
     * 查询订单详情页里的交易信息
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getDealInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        CapitalBill capitalBill = new CapitalBill();
        if (saleorder.getPaymentStatus() > 0){
            // 获取交易信息数据
            capitalBill.setOperationType("finance_sale_detail");
            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
            capitalBillDetail.setOrderType(ErpConst.ONE);// 销售订单类型
            capitalBillDetail.setOrderNo(saleorder.getSaleorderNo());
            capitalBillDetail.setRelatedId(saleorder.getSaleorderId());
            capitalBill.setCapitalBillDetail(capitalBillDetail);
            List<CapitalBill> capitalBillList = capitalBillService.getCapitalBillList(capitalBill);
            mv.addObject("capitalBillList", capitalBillList);
            // 资金流水交易方式
            List<SysOptionDefinition> traderModes = getSysOptionDefinitionList(519);
            mv.addObject("traderModes", traderModes);

            // 资金流水业务类型
            List<SysOptionDefinition> bussinessTypes = getSysOptionDefinitionList(524);
            mv.addObject("bussinessTypes", bussinessTypes);
        }
    }


    /**
     * 查询订单详情页里的报价信息及报价咨询信息
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getQuoteConsultInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder, User crrentUser){

        //查询有效订单商品数量
        int numOfGoods = 0;
        List<SaleorderGoods> saleorderGoods = (List<SaleorderGoods>) mv.getModel().get("saleorderGoods");
        if (saleorderGoods.size() > 0) {
            numOfGoods = saleorderGoods.stream().map(SaleorderGoods::getNum).reduce(0,Integer::sum);
        }
        mv.addObject("numOfGoods",numOfGoods);


        Integer quoteOrderId = saleorder.getQuoteorderId();
        if (quoteOrderId != null && quoteOrderId > 0){
            Quoteorder quoteInfo = quoteService.getQuoteOrderInfoById(saleorder.getQuoteorderId());
            mv.addObject("quoteInfo", quoteInfo);
            //查询报价咨询记录
            List<QuoteorderConsult> consultList = quoteService.getQuoteConsultList(saleorder.getQuoteorderId());
            if(!consultList.isEmpty()){
                mv.addObject("consultList", consultList);
                List<Integer> consultUserIdList = new ArrayList<Integer>();
                for(QuoteorderConsult qc : consultList){
                    consultUserIdList.add(qc.getCreator());
                }
                List<User> consultUserList = userService.getUserByUserIds(consultUserIdList);
                mv.addObject("consultUserList", consultUserList);
            }

            //
        }
        //是否是订单归属销售的直接上级
        User saler = userService.getUserById(saleorder.getOptUserId());
        if (saler != null && saler.getParentId().equals(crrentUser.getUserId())){
            mv.addObject("roleType",1);
        } else {
            mv.addObject("roleType",0);
        }

        boolean existQuSaleHist = quoteInfoService.isExistQuSaleHist(saleorder.getSaleorderId());
        if(existQuSaleHist){
            mv.addObject("isShareOnlie",1);
        }
    }

    private void getGoodsPriceInfo(ModelAndView mv, Saleorder saleorder){
        Saleorder sale = new Saleorder();
        sale.setSaleorderId(saleorder.getSaleorderId());
        sale.setTraderId(saleorder.getTraderId());
        sale.setCompanyId(ErpConst.NJ_COMPANY_ID);
        sale.setReqType(1);
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);

        List<String> skuNos = saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());
        //批量查看sku的价格信息
        List<PriceInfoResponseDto> priceInfoResponseDtos = this.basePriceService.batchFindPriceInfo(skuNos,saleorder.getTraderId());
        //核价信息应用 add by brianna 2020/5/29 start
        List<GoodSalePrice> goodSalePriceList = saleorderGoodsList.stream()
                .map(saleorderGood -> new GoodSalePrice(saleorderGood.getSku(),saleorderGood.getChannelPrice()))
                .collect(Collectors.toList());

        Map<String,String> skuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(saleorder.getTraderId(),goodSalePriceList,priceInfoResponseDtos);
        mv.addObject("skuNoAndPriceMap", skuNoAndPriceMap);
    }

    /**
     * 查询订单详情页里的订单商品信息
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getGoodsInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        Saleorder sale = new Saleorder();
        sale.setSaleorderId(saleorder.getSaleorderId());
        sale.setTraderId(saleorder.getTraderId());
        sale.setCompanyId(ErpConst.NJ_COMPANY_ID);
        sale.setReqType(1);
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);
        try {
            setSkuAuthorizationVo(saleorderGoodsList);
        } catch (Exception e) {
            logger.error("检索sku报备信息error", e);
        }

        // 所有产品的手填成本总价1
        mv.addObject("totalReferenceCostPrice", sale.getFiveTotalAmount());

        List<Integer> skuIdList = saleorderGoodsList.stream().map(SaleorderGoods::getGoodsId).collect(Collectors.toList());
        List<String> skuNos = saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.toList());

        //产品信息提示框
        List<Map<String,Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIdList);
        Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
        mv.addObject("newSkuInfosMap", newSkuInfosMap);
        mv.addObject("regions", regionService.getRegionByParentId(1));
        mv.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());

        //更改申请开票按钮显示逻辑
        saleorder = saleorderService.updateisOpenInvoice(saleorder,saleorderGoodsList);

        //产品结算价
        saleorderGoodsList = goodsSettlementPriceService
                .getGoodsSettlePriceBySaleorderGoodsList(ErpConst.NJ_COMPANY_ID, saleorderGoodsList);
        // 计算核价信息
        TraderCustomerVo customer = (TraderCustomerVo) mv.getModel().get("customer");
        if(customer!=null) {

                saleorderGoodsList = goodsChannelPriceService.getSaleChannelPriceList(saleorder.getSalesAreaId(),
                        saleorder.getCustomerNature(), customer.getOwnership(), saleorderGoodsList);

        }

        //采购状态信息
        //根据使用场景，订单id，订货号查出采购要求和供应商要求
        Map<String,Object> remarkComponentQueryMap = new HashMap<>();
        remarkComponentQueryMap.put("skuNos",skuNos);
        remarkComponentQueryMap.put("saleorderId",saleorder.getSaleorderId());
        remarkComponentQueryMap.put("scene",RemarkComponentSceneEnum.SALE_ORDER.getCode());
        List<Map<String,Object>> componentList = remarkComponentService.findComponentList(remarkComponentQueryMap);
        if (CollectionUtils.isNotEmpty(componentList)){
            componentList.forEach(map -> {
                if (!StringUtils.isNumeric(map.get("time").toString())){
                    map.put("timeData",0);
                }
                long timeNum = Long.parseLong(map.get("time").toString());
                if (timeNum <= 0) {
                    map.put("timeData",0);
                }
                map.put("timeData",DateUtil.convertString(timeNum,"yyyy-MM-dd"));
            });
        }
        mv.addObject("componentList",componentList);
        saleorderGoodsList.forEach(item -> {
            List<String> skuNoList = new ArrayList<>();
            skuNoList.add(item.getSku());
            LabelQuery labelQuery = new LabelQuery();
            // 根据当前场景获取
            labelQuery.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
            labelQuery.setRelationId(item.getSaleorderId());
            labelQuery.setSkuNoList(skuNoList);
            String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
            item.setComponentHtml(componentHtml);
        });

        //批量查看sku的价格信息
        List<PriceInfoResponseDto> priceInfoResponseDtos = this.basePriceService.batchFindPriceInfo(skuNos,saleorder.getTraderId());
        //核价信息应用 add by brianna 2020/5/29 start
        List<GoodSalePrice> goodSalePriceList = saleorderGoodsList.stream()
                .map(saleorderGood -> new GoodSalePrice(saleorderGood.getSku(),saleorderGood.getChannelPrice()))
                .collect(Collectors.toList());

        Map<String,String> skuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(saleorder.getTraderId(),goodSalePriceList,priceInfoResponseDtos);
        mv.addObject("skuNoAndPriceMap", skuNoAndPriceMap);

        //商品发货信息
        List<ExpressDetail> expressSeList = expressService.getSEGoodsNum(saleorderGoodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList()));
        //虚拟商品发货信息
        List<SaleOrderGoodsDetailDto> allVirtualGoods = saleOrderGoodsApiService.findAllVirtualGoodsBySaleorderId(saleorder.getSaleorderId());
        for (SaleOrderGoodsDetailDto allVirtualGood : allVirtualGoods) {
            for (ExpressDetail expressDetail : expressSeList) {
                if (allVirtualGood.getSaleorderGoodsId().equals(expressDetail.getSaleOrderGoodsId())){
                    if (allVirtualGood.getArrivalStatus() ==2){
                        expressDetail.setArriveNum(allVirtualGood.getNum());
                    }
                    break;
                }
            }
        }
        mv.addObject("expressSeList", expressSeList);

        //发货数量备注
        List<SpecialDeliveryInfoDto> specialDeliveryInfoDtoList =  saleorderService.getSpecialDeliveryInfo(saleorder);
        mv.addObject("specialDeliveryList",specialDeliveryInfoDtoList);

        //已开票数量 已申请数量
        Map<Integer , Map<String, Object>> taxNumsMap = invoiceService.getInvoiceNums(saleorder);
        if (null != taxNumsMap) {
            saleorderGoodsList.forEach(g -> {
                g.setAppliedNum(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("APPLY_NUM").toString()));
                g.setInvoicedNum(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("INVOICE_NUM").toString()));
                g.setInvoicedAmount(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("INVOICE_AMOUNT").toString()));
                g.setAppliedAmount(new BigDecimal(taxNumsMap.get(g.getSaleorderGoodsId()).get("APPLY_AMOUNT").toString()));
            });
        }

        //订单商品风控
        riskCheckService.setSaleorderIsRiskInfo(saleorder,saleorderGoodsList);

        //获取订单商品的咨询情况
        if (saleorderGoodsList.size() > 0){
            saleorderGoodsList = quoteService.getConsultResultOfSaleorderGoods(saleorderGoodsList);
        }

        //订单流二期--jcf展示协议商品start
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case JCF:
            case JCO:{
                //查询sku是否为协议商品
                for(SaleorderGoods item : saleorderGoodsList){
                    ContractPriceInfoDetailResponseDto contractPriceInfoDetail = this.basePriceService.findSkuContractInfoByCon(saleorder.getTraderId(), item.getSku());
                    if (contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null) {
                        //协议商品
                        item.setContractedGoodsFlag(CommonConstants.ON);
                    }
                }
                break;
            }
            default:{
                break;
            }
        }
        //替换虚拟商品的交付信息
        replaceExpenseBuyDetails(saleorderGoodsList);

        if(saleorder.getQuoteorderId() != null && saleorder.getQuoteorderId()>0){
            List<QuoteorderGoods> quoteOrderGoodsConsultList = quoteService.getQuoteOrderGoodsConsultList(saleorder.getQuoteorderId());
            Map<String,Integer> skuToUserLastRefeerenceMap =  quoteOrderGoodsConsultList.stream()
                    .filter(quoteOrderGoods -> quoteOrderGoods.getLastReferenceUser() != null && !quoteOrderGoods.getLastReferenceUser().equals(0))
                    .collect(HashMap::new,
                            (map, quoteOrderGoods) -> map.put(quoteOrderGoods.getSku(), quoteOrderGoods.getLastReferenceUser()),
                            HashMap::putAll);
            saleorderGoodsList.forEach(saleorderGoodsTemp -> {
                String sku = saleorderGoodsTemp.getSku();
                if (skuToUserLastRefeerenceMap.containsKey(sku)) {
                    saleorderGoodsTemp.setLastReferenceUser(skuToUserLastRefeerenceMap.get(sku));
                }
            });
        }
        saleorderService.setMaoLiBuyPrice(saleorder,saleorderGoodsList);
        saleorderService.setSaleOrderGoodsImg(saleorderGoodsList);

        //订单流二期--jcf展示协议商品end
        mv.addObject("saleorderGoodsList", saleorderGoodsList);
    }


    /**
     * 查询订单详情页里的沟通记录
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getCommunicationRecordInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        // 沟通类型为商机和报价和销售订单
        CommunicateRecord cr = new CommunicateRecord();
        if (saleorder.getQuoteorderId() != null && saleorder.getQuoteorderId() != 0) {
            cr.setQuoteorderId(saleorder.getQuoteorderId());
        }
        if (saleorder.getBussinessChanceId() != null && saleorder.getBussinessChanceId() != 0) {
            cr.setBussinessChanceId(saleorder.getBussinessChanceId());
        }
        cr.setSaleorderId(saleorder.getSaleorderId());

        cr.setCompanyId(ErpConst.NJ_COMPANY_ID);
        Page page = Page.newBuilder(null, null, null);
        List<CommunicateRecord> communicateList = traderCustomerService.getCommunicateRecordListPage(cr, page);
        if (!communicateList.isEmpty()) {
            // 沟通内容
            mv.addObject("communicateList", communicateList);
            mv.addObject("page", page);
        }
    }


    private void getInvoiceApplyInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        // 开票申请
        List<InvoiceApply> saleInvoiceApplyList = invoiceService.getSaleInvoiceApplyList(saleorder.getSaleorderId(),
                SysOptionConstant.ID_505, -1);
        mv.addObject("saleInvoiceApplyList", saleInvoiceApplyList);
    }


    /**
     * 撤销订单生效状态
     * @param saleOrderId 订单id
     * @return 操作结果
     */
    @RequestMapping("/valid/cancel")
    @ResponseBody
    @SystemControllerLog(type = 1, operationType = "edit", desc = "撤销订单生效状态",isEnable = 1)
    public ResultInfo<?> cancelValidOfSaleOrder(@RequestParam Integer saleOrderId){
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            return ResultInfo.success();
        }
        SaleorderModifyApply saleorderModifyApply = saleorderService.getSaleorderModifyApplyBySaleOrderId(saleOrderId,ErpConst.ZERO);
        if (Objects.nonNull(saleorderModifyApply)){
            return ResultInfo.error("订单存在进行中的修改单,无法撤销,请刷新后重试");
        }
        if (saleorder.getStatus() == 1 && saleorder.getValidStatus() == 1
                && saleorder.getPaymentStatus() == 0 && saleorder.getDeliveryStatus() == 0 && saleorder.getArrivalStatus() == 0){
            switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
                //撤销生效执行器
                case JCF:{
                    jcfCancelValidSaleOrderBuzLogic.run(saleorder);
                    break;
                }
                default:{
                    cancelValidSaleOrderBuzLogic.run(saleorder);
                    break;
                }
            }
//            BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
//            saleOrderService.cancelValidOfSaleOrder(saleOrderId);
            return ResultInfo.success(saleOrderId);
        } else {
            return ResultInfo.error("订单无法撤销");
        }
    }


    @MethodLock(className = Integer.class,time = 10000)
    @RequestMapping(value = "/paymentByCredit",method = RequestMethod.GET)
    @ResponseBody
    public ResultInfo<Void> paymentSaleOrderByCredit(@MethodLockParam @RequestParam Integer saleOrderId, HttpServletRequest request){
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
        if (saleorder == null) {
            return ResultInfo.success();
        }
        User currentUser = getSessionUser(request);
        if (saleorder.getValidStatus() == 1 && saleorder.getPaymentStatus() == 0 && saleorder.getPaymentType() == 423){
            BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
            try {
                logger.info("用户：{}对订单：{},发起信用支付",currentUser.getUsername(),saleOrderId);
                saleOrderService.addCapitalBillByCustomerBillPeriod(saleOrderId,currentUser,request);
                logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorder,currentUser);
                //调用库存服务
               // warehouseStockService.updateOccupyStockService(saleorder, 0);
            } catch (Exception e) {
                logger.error("订单：{},100%账期支付后，下发wms异常：",saleOrderId,e);
            }
            return ResultInfo.success();
        } else {
            return ResultInfo.error("该订单无法进行信用支付");
        }
    }


    /**
     * 查询销售订单的商品的已采购数量
     * @param saleOrderId 销售订单id
     * @return 商品的已采购数量
     */
    @RequestMapping("/purchaseCount")
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<List<SaleorderGoods>> getPurchasedCountOfSaleOrderGoods(@RequestParam Integer saleOrderId){
        List<SaleorderGoods> saleorderGoodsList = new ArrayList<>();
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.BASE);
        Saleorder saleorder = saleOrderService.getSaleOrderBaseInfo(saleOrderId);
        if (saleorder == null) {
            return ResultInfo.success(saleorderGoodsList);
        }
        return ResultInfo.success(saleOrderService.getPurchasedCountOfSaleOrder(saleOrderId));
    }


    /**
     * 查询订单详情页的物流信息
     * @param mv 订单详情页视图
     */
    private void getLogisticsInfoOfSaleOrder(ModelAndView mv){
        List<SaleorderGoods> saleorderGoods = (List<SaleorderGoods>) mv.getModel().get("saleorderGoods");
        Saleorder saleorder = (Saleorder) mv.getModel().get("saleorder");
        if (saleorderGoods != null && saleorderGoods.size() > 0) {
            Express express = new Express();
            express.setBusinessType(SysOptionConstant.ID_496);
            express.setCompanyId(ErpConst.NJ_COMPANY_ID);
            List<Integer> relatedIds = new ArrayList<Integer>();
            for (SaleorderGoods sg : saleorderGoods) {
                relatedIds.add(sg.getSaleorderGoodsId());
            }
            express.setRelatedIds(relatedIds);
            express.setSaleorderId(saleorder.getSaleorderId());
            List<Express> expressList = expressService.getExpressListNewConfirm(express);
            if (CollectionUtils.isNotEmpty(expressList)){
                expressList.forEach(e->{
                    List<Integer> list = Arrays.asList(e.getCommunicateRecorderIds().split(","))
                            .stream()
                            .filter(str -> !str.isEmpty())
                            .map(Integer::parseInt)
                            .collect(Collectors.toList());
                    e.setRecordIdList(list);
                });
            }

                //新单子
            int displayPrUpFs = 1;
            mv.addObject("displayPrUpFs", displayPrUpFs);

            mv.addObject("expressList", expressList);
        }
    }


    /**
     * 查询订单的发票信息
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getInvoiceInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        // 获取发票信息
        List<Invoice> saleInvoiceList = invoiceService.getInvoiceInfoByRelatedId(saleorder.getSaleorderId(),
                SysOptionConstant.ID_505);
        mv.addObject("saleInvoiceList", saleInvoiceList);
    }


    /**
     * 查询销售订单的修改申请记录
     * @param mv 销售订单详情页视图
     * @param saleorder 订单
     */
    private void getModifyApplyInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        SaleorderModifyApply saleorderModifyApply = new SaleorderModifyApply();
        saleorderModifyApply.setSaleorderId(saleorder.getSaleorderId());
        List<SaleorderModifyApply> saleorderModifyApplyList = saleorderService
                .getSaleorderModifyApplyList(saleorderModifyApply);
        mv.addObject("saleorderModifyApplyList", saleorderModifyApplyList);
    }

    /**
     * 查询订单的合同回传信息
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getContractReturnInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        // 获取订单合同回传&订单送货单回传列表
        List<Attachment> saleorderAttachmentList = saleorderService.getSaleorderAttachmentList(saleorder.getSaleorderId());
        logger.info("销售单附件信息{},",JSON.toJSONString(saleorderAttachmentList));
        mv.addObject("saleorderAttachmentList", saleorderAttachmentList);

        // 合同回传审核信息
        Map<String, Object> historicInfoContractReturn = actionProcdefService.getHistoric(processEngine,
                "contractReturnVerify_" + saleorder.getSaleorderId());
        mv.addObject("commentMapContractReturn", historicInfoContractReturn.get("commentMap"));
        Task taskInfoContractReturn = (Task) historicInfoContractReturn.get("taskInfo");
        mv.addObject("taskInfoContractReturn", taskInfoContractReturn);
        List<HistoricActivityInstance> historicActivityInstanceContractReturn = setAssignRealNames(mv, historicInfoContractReturn);
        mv.addObject("historicActivityInstanceContractReturn", historicActivityInstanceContractReturn);
        mv.addObject("candidateUserMapContractReturn", historicInfoContractReturn.get("candidateUserMap"));
    }

    /**
     * 查询销售订单的录保卡信息
     * @param mv 订单详情页视图
     * @param saleorder 订单
     */
    private void getGoodsWarrantiesOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        // 录保卡
        List<SaleorderGoodsWarrantyVo> goodsWarranties = saleorderService.getSaleorderGoodsWarrantys(saleorder.getSaleorderId());
        mv.addObject("goodsWarranties", goodsWarranties);
    }

    /**
     * 查询销售订单的售后列表信息
     * @param mv 销售订单详情页视图
     * @param saleorder 订单
     */
    private void getAfterSalesInfoOfSaleOrder(ModelAndView mv, Saleorder saleorder){
        AfterSalesVo as = new AfterSalesVo();
        as.setOrderId(saleorder.getSaleorderId());
        as.setSubjectType(535);
        List<AfterSalesVo> afterSalesList = afterSalesService.getAfterSalesVoListByOrderId(as);
        //订单流二期--票货同行订单需要判断是否存在进行中，待确认，已完结的退货退票单
        if(afterSalesList.stream().filter(item -> (AfterSalesTypeEnum.RETURNS_SALEORDER.getCode().equals(item.getType()) && !ErpConst.THREE.equals(item.getAtferSalesStatus()))
                || (AfterSalesTypeEnum.REFUND_TICKET_SALEORDER.getCode().equals(item.getType()) && !ErpConst.THREE.equals(item.getAtferSalesStatus()))).collect(Collectors.toList()).size() > 0){
            //存在--则已不是票货同行-需展示申请开票
            mv.addObject("shInfoJudge",ErpConst.ONE);
        }else {
            mv.addObject("shInfoJudge",ErpConst.ZERO);
        }
        mv.addObject("afterSalesList", afterSalesList);
    }


    /**
     * 查询订单相关的审核记录
     * @param auditType 审核类型 @link AuditTypeEnumOfSaleOrder
     * @param saleOrderId 订单id
     * @return 审核记录
     */
    @RequestMapping(value = "/audit_record",method = RequestMethod.GET)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<AuditRecordDetailsVo> getAuditRecordListOfSaleOrder(@RequestParam Integer auditType,
                                                                          @RequestParam Integer saleOrderId){
        logger.info("查询订单相关的审核记录,auditType={},saleOrderId={}",auditType,saleOrderId);
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.BASE);
        Saleorder saleorder = saleOrderService.getSaleOrderBaseInfo(saleOrderId);
        if (saleorder == null) {
            return ResultInfo.error("订单不存在");
        }
        AuditRecordDetailsVo auditRecordDetailsVo = new AuditRecordDetailsVo();
        List<Integer> checkStatusList = new ArrayList<>();
        List<AuditRecordInstanceVo> instanceVos = new ArrayList<>();
        AuditTypeEnumOfSaleOrder auditTypeEnum = AuditTypeEnumOfSaleOrder.getByType(auditType);
        if (auditTypeEnum != null) {
            switch (auditTypeEnum){
                case VALID_CHECK:
                    List<AuditRecordInstanceVo> instanceVos1 = saleOrderService.getAuditRecordListOfSaleOrderVerify(saleOrderId,1);
                    List<AuditRecordInstanceVo> instanceVos2 = saleOrderService.getAuditRecordListOfSaleOrderVerify(saleOrderId,0);
                    instanceVos.addAll(instanceVos1);
                    instanceVos.addAll(instanceVos2);
                    break;
                case CONTRACT_RETURN_CHECK:
                    instanceVos = saleOrderService.getAuditRecordListOfContractReturnVerify(saleOrderId);
                    break;
                default:
                    break;
            }
        }
        VerifiesInfo saleOrderVerifyQuery = new VerifiesInfo();
        saleOrderVerifyQuery.setRelateTable("T_SALEORDER");
        saleOrderVerifyQuery.setRelateTableKey(saleOrderId);
        saleOrderVerifyQuery.setVerifiesType(623);
        List<VerifiesInfo> verifiesInfoOfSaleOrderVerify = verifiesInfoMapper.getVerifiesInfo(saleOrderVerifyQuery);
        int checkStatusOfSaleOrderVerify = CollectionUtils.isEmpty(verifiesInfoOfSaleOrderVerify) ? -1 : verifiesInfoOfSaleOrderVerify.get(0).getStatus();
        //订单被风控时，默认为已提交审核状态
        if (saleorder.getIsRisk() == 1 && CollectionUtils.isEmpty(verifiesInfoOfSaleOrderVerify)){
            checkStatusOfSaleOrderVerify = 0;
        }
        checkStatusList.add(checkStatusOfSaleOrderVerify);

        VerifiesInfo contractReturnVerifyQuery = new VerifiesInfo();
        contractReturnVerifyQuery.setRelateTable("T_SALEORDER");
        contractReturnVerifyQuery.setRelateTableKey(saleOrderId);
        contractReturnVerifyQuery.setVerifiesType(868);
        List<VerifiesInfo> verifiesInfoOfContractReturn = verifiesInfoMapper.getVerifiesInfo(contractReturnVerifyQuery);
        int checkStatusOfContractReturn = CollectionUtils.isEmpty(verifiesInfoOfContractReturn) ? -1 : verifiesInfoOfContractReturn.get(0).getStatus();
        checkStatusList.add(checkStatusOfContractReturn);

        auditRecordDetailsVo.setAuditRecordInstanceVoList(instanceVos);
        auditRecordDetailsVo.setCheckStatusList(checkStatusList);
        return ResultInfo.success(auditRecordDetailsVo);
    }

    @RequestMapping(value = "/confirmation_audit_record",method = RequestMethod.GET)
    @ResponseBody
    @ExcludeAuthorization
    public ResultInfo getAuditRecordListOfSaleOrder(@RequestParam Integer batchId) {
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.BASE);
        List<AuditRecordInstanceVo> instanceVos = saleOrderService.getAuditRecordListOfConfirmationOrderVerify(batchId);
        if (instanceVos.size()>0){
            return ResultInfo.success(instanceVos);
        }
        return ResultInfo.error();
    }


    /**
     * 客户联系人地址信息
     * @param traderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getCustomerContactAndAddress")
    public ResultInfo<?> getCustomerContactAndAddress(Integer traderId) {
        TraderContactVo traderContactVo = new TraderContactVo();
        traderContactVo.setTraderId(traderId);
        traderContactVo.setIsEnable(ErpConst.ONE);
        traderContactVo.setTraderType(ErpConst.ONE);
//        Map<String, Object> invoiceMap = traderCustomerService.getTraderContactVoList(traderContactVo);
//        String invoiceTastr = (String) invoiceMap.get("contact");
//        net.sf.json.JSONArray Json = net.sf.json.JSONArray.fromObject(invoiceTastr);
//        List<TraderContactVo> contactList = (List<TraderContactVo>) Json.toCollection(Json, TraderContactVo.class);
//        List<TraderAddressVo> addressList = (List<TraderAddressVo>) invoiceMap.get("address");
        List<TraderContactVo> contactList = traderCustomerService.getTraderContactVoListNew(traderContactVo);
        List<TraderAddress> oldaddressList = traderCustomerService.getTraderAddressInfoListNew(traderContactVo);
        List<TraderAddressVo> addressList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(oldaddressList)){
            for (TraderAddress ta : oldaddressList) {
                if (ta == null || ta.getAreaId() == null) {
                    continue;
                }
                TraderAddressVo tav = new TraderAddressVo();
                tav.setTraderAddress(ta);
                tav.setIsEnable(ta.getIsEnable());
                if (ta.getAreaId() > 0) {
                    tav.setArea(ta.getAreaString());
                }
                addressList.add(tav);
            }
        }


        Map<String, Object> map = new HashMap<>();
        map.put("contactList", contactList);
        map.put("addressList", addressList);
        ResultInfo<?> result = new ResultInfo<>();
        result.setCode(0);
        result.setMessage("操作成功");
        result.setParam(map);
        return result;
    }

    /**
     * 添加联系人
     * @param request
     * @return
     */
    @RequestMapping(value = "/addContact")
    public ModelAndView addContact(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("orderstream/saleorder/add_contact");
        String traderId = request.getParameter("traderId");
        String traderCustomerId = request.getParameter("traderCustomerId");
        String indexId = request.getParameter("indexId");
        String customerNature= request.getParameter("customerNature");
        mv.addObject("traderId", traderId);
        mv.addObject("traderCustomerId", traderCustomerId);
        mv.addObject("indexId", indexId);
        mv.addObject("customerNature", customerNature);
        return mv;

    }

    /**
     * 添加地址
     * @param request
     * @return
     */
    @RequestMapping(value = "/addAddress")
    public ModelAndView addAddress(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("orderstream/saleorder/add_address");
        String traderId = request.getParameter("traderId");
        String indexId = request.getParameter("indexId");
        mav.addObject("traderId", traderId);
        mav.addObject("indexId", indexId);
        // 省级地区
        getFirstProvince(mav);

        return mav;

    }

    @RequestMapping(value = "/addAddressForPage")
    @NoNeedAccessAuthorization
    public ModelAndView addAddressForPage(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView("orderstream/saleorder/add_address_new");
        String traderId = request.getParameter("traderId");
        String indexId = request.getParameter("indexId");
        mav.addObject("traderId", traderId);
        mav.addObject("indexId", indexId);
        // 省级地区
        getFirstProvince(mav);

        return mav;

    }

    @RequestMapping(value = "/addContactForPage")
    @NoNeedAccessAuthorization
    public ModelAndView addContactForPage(HttpServletRequest request) {
        ModelAndView mv = new ModelAndView("orderstream/saleorder/add_contact_new");
        String traderId = request.getParameter("traderId");
        String traderCustomerId = request.getParameter("traderCustomerId");
        String indexId = request.getParameter("indexId");
        String customerNature= request.getParameter("customerNature");
        mv.addObject("traderId", traderId);
        mv.addObject("traderCustomerId", traderCustomerId);
        mv.addObject("indexId", indexId);
        mv.addObject("customerNature", customerNature);
        return mv;

    }


    /**
     * 保存订单
     * @param request
     * @param session
     * @param saleorder
     * @param scene
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/saveSaleorderInfo", produces = MediaType.TEXT_HTML_VALUE)
    @NoNeedAccessAuthorization
    public ModelAndView saveSaleorderInfo(HttpServletRequest request, HttpSession session, Saleorder saleorder,
                                          @RequestParam(defaultValue = "0") Integer scene,@RequestParam(defaultValue = "0") Integer pageType) {
        ModelAndView modelAndView = new ModelAndView();
        modelAndView.addObject("scene",scene);
        User user = getSessionUser(request);
        String activityPreOrderId = request.getParameter("activityPreOrderId");
        ActivityPreOrderDto activityPreOrderDto = null;
        if (StrUtil.isNotBlank(activityPreOrderId)) {
            activityPreOrderDto = activityPreOrderService.getActivityPreOrderDtoById(Integer.valueOf(activityPreOrderId));

            if (activityPreOrderDto.getOrderStatus() != 1) {
                modelAndView.addObject("message", "商机无法生成订单！");
                return fail(modelAndView);
            }

            saleorder.setActionId(activityPreOrderDto.getActiveId());
            Integer existNum = activityPreOrderService.findExistNumByTraderIdAndActionId(activityPreOrderDto.getActiveId(), saleorder.getTraderId(), activityPreOrderDto.getGoodsId());
            existNum = Convert.toInt(existNum, 0);
            if (existNum + activityPreOrderDto.getNum() > activityPreOrderDto.getQuota()) {
                modelAndView.addObject("message","该客户抢购数量已超限购，无法创建订单！");
                return fail(modelAndView);
            }
            saleorder.setComments(activityPreOrderDto.getRemark());
        }
        //终端信息
        getTerminalCustomerInfo(saleorder);

        //基本信息
        initBase(saleorder,user);

        //保存订单信息
        List<String> saleSequences = new ArrayList<>();
        logger.info("保存订单信息："+ JSON.toJSONString(saleorder));
        //根据ordertype选择创建订单执行器
        Saleorder result = null;
        ResultInfo resultInfo = new ResultInfo();
        baseSaleOrderService.setSaleorderType(saleorder);
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case ZXF:{
                //if(saleorder.getInvoiceEmail() == null || saleorder.getInvoiceEmail().equals("")){
                //    saleorder.setInvoiceEmail("<EMAIL>");
                //}
                //zxf订单给默认值分批发货,立即发货,寄送发票,13%增值税普通发票,自动电子发票,先款后货，预付100%,立即采购,供应链自寻,否
                // VDERP-15759 数电发票，所有订单默认 不寄送+自动数电发票，赋值逻辑在initBase方法中
                saleorder.setDeliveryType(SysOptionConstant.PART_DELIVER);
                saleorder.setDeliveryClaim(ErpConst.ZERO);
//                saleorder.setIsSendInvoice(ErpConst.ONE);
                saleorder.setInvoiceType(SysOptionConstant.ID_971);
//                saleorder.setInvoiceMethod(ErpConst.THREE);
                saleorder.setIsPrintout(ErpConst.FOUR);
                resultInfo = zxfCreateSalesOrderBuzLogic.run(saleorder);
                break;
            }
            case VS: {
                resultInfo = vsCreateSaleOrderBuzLogic.run(saleorder);
                break;
            }
            case JCF:{
                //if(saleorder.getInvoiceEmail() == null || saleorder.getInvoiceEmail().equals("")){
                //    saleorder.setInvoiceEmail("<EMAIL>");
                //}
                saleorder.setStatus(ErpConst.FOUR);
                resultInfo = jcfCreateSaleOrderBuzLogic.run(saleorder);
                break;
            }
            default:{
                resultInfo = createSaleOrderBuzLogic.run(saleorder);
                break;
            }
        }
        result =saleorder;
        if (activityPreOrderDto != null) {
            activityPreOrderDto.setSaleorderId(result.getSaleorderId());
            activityPreOrderDto.setOrderStatus(2);
            activityPreOrderService.update(activityPreOrderDto);
            // 疫情临时秒杀活动相关
            if (saleorder.getActionId() != null) {
                ActivityPreOrderDto entity = activityPreOrderService.getActivityPreOrderBySaleorderId(saleorder.getSaleorderId());
                if (entity != null) {
                    GoodsVo goods = goodsService.queryGoodsBySku(entity.getSku());
                    if (goods == null) {
                        modelAndView.addObject("message", "无法找到当前sku或未审核通过：" + entity.getSku());
                        return fail(modelAndView);
                    }
                    SaleorderGoods saleorderGoods = new SaleorderGoods();
                    saleorderGoods.setUnitName(goods.getUnitName());
                    saleorderGoods.setBrandName(goods.getBrandName());
                    saleorderGoods.setSaleorderId(entity.getSaleorderId());
                    saleorderGoods.setGoodsId(entity.getGoodsId());
                    saleorderGoods.setGoodsName(entity.getGoodsName());
                    saleorderGoods.setPrice(entity.getPrice());
                    saleorderGoods.setSku(entity.getSku());
                    saleorderGoods.setNum(entity.getNum());
                    saleorderGoods.setIsActionGoods(1);
                    this.saveSaleorderGoods(request,saleorderGoods);
                }
            }
        }

        if (result != null) {
            Saleorder order = saleorderMapper.getSaleorderBySaleorderId(result.getSaleorderId());
            addTrackCreateSaleOrder(CurrentUser.getCurrentUser(),order.getSaleorderNo(),result.getTraderId(),"新建");
            modelAndView.addObject("url", "/orderstream/saleorder/edit.do?saleorderId=" + result.getSaleorderId()
                    + "&extraType=" + result.getExtraType() +"&scene="+scene + "&pageType=" + pageType);
            return success(modelAndView);
        } else {
            return fail(modelAndView);
        }
    }

    @Autowired
    private TrackStrategyFactory trackStrategyFactory;

    private void addTrackCreateSaleOrder(CurrentUser currentUser,String orderNo,Integer traderId,String orderSource) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_CREATE_ORDER_BACK;
        try {
            User user = authService.getUserById(currentUser.getId());
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("track_user", user);
            trackParams.put("orderSource",orderSource);
            trackParams.put("orderNo",orderNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            log.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }

    /**
     * 终端信息
     * @param orderInfo
     */
    public void getTerminalCustomerInfo(Saleorder orderInfo) {
        TraderCustomer traderCustomer = new TraderCustomer();
        traderCustomer.setTraderId(orderInfo.getTraderId());
        TraderCustomerVo customerQuery = traderCustomerService.getTraderCustomerBaseInfo(traderCustomer);
        if (customerQuery != null && SysOptionConstant.CUSTOMER_NATURE_TERMINAL.equals(orderInfo.getCustomerNature())) {
            // 终端类型
            orderInfo.setTerminalTraderType(customerQuery.getCustomerType());
            // 终端名称
            orderInfo.setTerminalTraderName(customerQuery.getTrader().getTraderName());
            // 销售区域
            orderInfo.setSalesArea((String) regionService.getRegion(customerQuery.getAreaId(), 2));
            Region region1 = regionService.getRegionByRegionId(customerQuery.getAreaId());
            if(region1 == null || region1.getRegionCode() == null){
                logger.info("订单创建查询销售区域id查询为空，客户id:{}",orderInfo.getTraderId());
            }else {
                if(region1.getRegionCode() == null){
                    logger.info("订单客户对应的销售区域code为空");
                }else {
                    orderInfo.setSalesAreaId(Integer.parseInt(region1.getRegionCode()));
                }
            }
        }
    }

    /**
     * 订单基础信息
     * @param saleorder
     * @param user
     */
    public void initBase(Saleorder saleorder,User user){
        //Set 发票类型- 13%增值税专用发票
        saleorder.setInvoiceType(OrderConstant.INVOICE_TYPE_13_PERCENT_SPECIAL);
        saleorder.setIsSendInvoice(ErpConst.ZERO);
        saleorder.setAddTime(System.currentTimeMillis());
        saleorder.setCreator(user.getUserId());
        saleorder.setCreatorOrgId(user.getOrgId());
        saleorder.setCreatorOrgName(user.getOrgName());
        saleorder.setCompanyId(user.getCompanyId());
        //订单流二期，根据客户判断订单类型
//        saleorder.setOrderType(SaleOrderTypeEnum.VS.getType());
        saleorder.setStatus(ErpConst.ZERO);
        saleorder.setFreightDescription(SysOptionConstant.ID_470);
        saleorder.setIsPrintout(ErpConst.FOUR);
        saleorder.setInvoiceMethod(ErpConst.FOUR);
        saleorder.setIsNew(ErpConst.ONE);
        // 归属销售
        if (saleorder.getTraderId() != null) {
            User belongUser = userService.getUserInfoByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorder.setUserId(belongUser.getUserId());
            }
            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorder.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorder.setOrgName(belongUser.getOrgName());
            }
        }
    }

    /**
     *
     * @param request
     * @param saleorder
     * @param scene
     * @param pageType tab标签名字标识
     * @return
     * @throws IOException
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "edit")
    public ModelAndView edit(HttpServletRequest request,
                             Saleorder saleorder,
                             @RequestParam(defaultValue = "0", required = false) Integer scene,
                             @RequestParam(defaultValue = "0", required = false) Integer pageType) throws IOException {
        ModelAndView mv = new ModelAndView();
        log.info("编辑数据：{}",JSON.toJSON(saleorder));
        try {
            User curr_user = getSessionUser(request);
            // 公司ID
            Integer companyId = curr_user.getCompanyId();

            Integer saleorderId = saleorder.getSaleorderId();
            // 获取订单基本信息
            saleorder = saleorderService.getSaleOrderInfo(saleorder);

            mv.setViewName("orderstream/saleorder/edit");
            //基础信息
            initOrderBaseInfo(saleorder, curr_user, mv);

            // 查询对应报价信息
            setQuoteOrderInfo(saleorder);

            Integer traderIdToUse = SaleOrderTypeEnum.isJcOrder(saleorder.getOrderType()) ? saleorder.getGroupCustomerId() : saleorder.getTraderId();

            TraderCustomerVo customer = traderCustomerService.getCustomerInfo(traderIdToUse);
            //账期信息
            traderCustomerService.setAccountPeriodInfo(customer, Long.valueOf(saleorderId), companyId);

            //创建人员、归属销售及部门信息
            getUserInfo(saleorder, mv, curr_user);

            //订单商品-价格
            getGoodsPriceInfo(mv,saleorder);

            //客户、收货客户、收票客户的相关信息
            getTraderContactAndAddressInfo(saleorder, customer, mv, traderIdToUse);

            //分销时显示终端信息
            dealWithCustomerNature(saleorder, mv);

            //订单产品相关信息
            getSaleOrderGoods(saleorder, customer, mv);

            //优惠卷相关信息
            getCouponInfo(mv, saleorder);


            // 付款方式列表
            List<SysOptionDefinition> paymentTermList = getSysOptionDefinitionList(SysOptionConstant.ID_418);
            // Bert ********修改付款付款方式(付款方式只提供两种选择：先款后货，预付100%和先款后货，预付0%) 任务号码:DSDEV-1975
            if (CollectionUtils.isNotEmpty(paymentTermList)) {
                paymentTermList = paymentTermList.stream()
                        .filter(option -> Objects.nonNull(option) && OrderConstant.PAYMENET_TYPE_FOR_SALEORDER.contains(option.getSysOptionDefinitionId())).collect(Collectors.toList());
            }
            switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())) {
                case HC: {
                    //hc订单只提供先款后货，预付100%和先款后货，预付0%
                    paymentTermList = paymentTermList.stream().filter(item -> (SysOptionConstant.ID_419.equals(item.getSysOptionDefinitionId())
                            || SysOptionConstant.ID_423.equals(item.getSysOptionDefinitionId()))).collect(Collectors.toList());
                    mv.addObject("paymentTermList", paymentTermList);
                    getProvinces(mv, saleorder);
                    break;
                }
                case JCF:
                case JCO: {

                    JcTraderContactDto jcTraderContactDto = jcOrderService.getJcAccountInfo(saleorder.getTraderContactId());
                    mv.addObject("jcTraderContactDto", jcTraderContactDto);

                    List<TraderCustomerVo> groupCustomers = jcOrderService.listAllGroupCustomer(traderIdToUse);
                    mv.addObject("groupCustomerList", groupCustomers);
                    mv.addObject("paymentTermList", paymentTermList);
                    break;
                }
                default:
                    mv.addObject("paymentTermList", paymentTermList);
                    break;
            }
            //状态显示
            List<StatusNode> statusList = initStatus(saleorder);
            mv.addObject("statusList", JSON.toJSONString(statusList));

            Trader trader = traderCustomerService.selectTraderNameByTraderId(traderIdToUse);
            mv.addObject("belongPlatform", trader.getBelongPlatform());

            mv.addObject("saleorder", saleorder);
            OrderTerminalDto info = orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(saleorder.getSaleorderId(), 0);
            mv.addObject("orderTerminalDto", info);

            mv.addObject("beforeParams", saveBeforeParamToRedis(JsonUtils.translateToJson(saleorder)));
            mv.addObject("scene", scene);
            mv.addObject("pageType", pageType);
            mv.addObject("traderIdToUse", traderIdToUse);
            mv.addObject("orderType", saleorder.getOrderType());
        }catch(Exception e){
            logger.error("订单进入编辑页失败",e);
            mv.addObject("message", "进入编辑失败：" + e.getMessage());
            return fail(mv);
        }
        Integer orderType = saleorder.getOrderType();
        if (orderType!=null && (orderType.equals(8)||orderType.equals(9)||orderType.equals(5)||orderType.equals(7))){
            mv.setViewName("orderstream/saleorder/edit_old");
        }
        return mv;
    }


    /**
     * 客户、收货客户、收票客户联系人的相关信息
     * @param
     * @return R
     */
    @RequestMapping(value = "/getTraderContactInfo", method = RequestMethod.POST)
    @ResponseBody
    @ExcludeAuthorization
    public R<?> getTraderContactInfo(String name,String traderIdToUse){
        TraderContactVo traderContactVo = new TraderContactVo();
        traderContactVo.setTraderId(Integer.valueOf(traderIdToUse));
        traderContactVo.setIsEnable(1);
        traderContactVo.setName(name);
        traderContactVo.setTraderType(ErpConst.ONE);
        List<TraderContactVo> tcvList = traderContactMapper.getTraderContactVoListForOrderSearch(traderContactVo);
        return R.success(tcvList);
    }


    /**
     * 客户、收货客户、收票客户地址的相关信息
     * @param
     * @return R
     */
    @RequestMapping(value = "/getTraderDressInfo", method = RequestMethod.POST)
    @ResponseBody
    @ExcludeAuthorization
    public R<?> getTraderDressInfo(String name,String traderIdToUse){
        TraderAddress ta = new TraderAddress();
        ta.setTraderId(Integer.valueOf(traderIdToUse));
        ta.setIsEnable(1);
        ta.setTraderType(ErpConst.ONE);
        ta.setAreaQuery(name);
        List<TraderAddress> taList = traderAddressMapper.getTraderAddressInfoList(ta);
        if (CollectionUtils.isNotEmpty(taList)) {
            taList.forEach(item -> {
                String address = item.getAddress();
                item.setAddress(StringUtils.isNotBlank(address) ? address.replaceAll("(\r\n|\r|\n|\n\r)", "") : "");
            });

        }
        return R.success(taList);
    }

    public void initOrderBaseInfo(Saleorder saleorder,User user, ModelAndView mv){
        //终端类型
        List<SysOptionDefinition> terminalTypeList = getSysOptionDefinitionList(425);

        mv.addObject("terminalTypeList", terminalTypeList);
        // 获取物流公司列表
        List<Logistics> logisticsList = logisticsService.getWmsLogisticsList(user.getCompanyId(),ErpConst.ONE);
//        if (CollectionUtils.isNotEmpty(logisticsList)) {
//            //物流公司中英文排序
//            logisticsList.sort(Comparator.comparing(o -> convertToPinYinString(o.getName())));
//        }
        mv.addObject("logisticsList", logisticsList);

        // 发货类型
        List<SysOptionDefinition> deliveryMethods = getSysOptionDefinitionList(1619);
        mv.addObject("deliveryMethods", deliveryMethods);
        // 发货方式
        List<SysOptionDefinition> deliveryTypes = getSysOptionDefinitionList(480);
        mv.addObject("deliveryTypes", deliveryTypes);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(469);
        mv.addObject("freightDescriptions", freightDescriptions);

        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);

        if (null != saleorder && ObjectUtils.isEmpty(saleorder.getBillPeriodSettlementType())){
            //type为0，默认产品开票
            saleorder.setBillPeriodSettlementType(ErpConst.TWO);
        }
        saleorder.setCompanyId(user.getCompanyId());
        saleorder.setTerminalTraderTypeStr(getSysOptionDefinition(saleorder.getTerminalTraderType()).getTitle());
    }

//    private String convertToPinYinString(String str){
//
//        StringBuilder sb=new StringBuilder();
//        String[] arr=null;
//
//        for(int i=0 ;i < str.length(); i++){
//            arr = PinyinHelper.toHanyuPinyinStringArray(str.charAt(i));
//            if( arr != null && arr.length > 0){
//                for (String string : arr) {
//                    sb.append(string);
//                }
//            }
//            else{
//                sb.append(str.charAt(i));
//            }
//        }
//        return sb.toString().toLowerCase(Locale.ROOT);
//    }

    public void getTraderContactAndAddressInfo(Saleorder saleorder,TraderCustomerVo customer,ModelAndView mv,Integer traderIdToUse){
        // 客户信息 联系人&联系地址
        if (saleorder.getTraderId() != null &&  saleorder.getTraderId().intValue() > 0) {
            TraderContactVo traderContactVo = new TraderContactVo();
            traderContactVo.setTraderId(traderIdToUse);
            traderContactVo.setIsEnable(1);
            traderContactVo.setTraderType(ErpConst.ONE);

            TraderContactDto traderContactDto = traderCustomerService.getTraderContactDto(traderContactVo);
            List<TraderContactVo> traderContactList ;
            List<TraderAddressVo> traderAddressList = traderContactDto.getAddressList();

            if (SaleOrderTypeEnum.isJcOrder(saleorder.getOrderType())){
                //集采订单的客户联系人取客户的注册用户集合
                traderContactList = traderContactService.getTraderContactInWebAccountByTraderId(traderIdToUse);
            } else {
                traderContactList = traderContactDto.getContactList();
            }
            mv.addObject("traderContactList", traderContactList);
            mv.addObject("traderAddressList", traderAddressList);
            String traderAddress = saleorder.getTraderAddress();
            saleorder.setTraderAddress(StringUtils.isNotBlank(traderAddress) ? traderAddress.replaceAll("(\r\n|\r|\n|\n\r)", "") : traderAddress);


            mv.addObject("customerContactAddressValue", saleorder.getTraderArea()+"/"+saleorder.getTraderAddress());
        }

        //收货信息 联系人&联系地址
        if (saleorder.getTakeTraderId() != null
                && saleorder.getTakeTraderId().intValue() > 0) {
            TraderContactVo takeTraderContactVo = new TraderContactVo();
            takeTraderContactVo.setTraderId(saleorder.getTakeTraderId());
            takeTraderContactVo.setIsEnable(1);
            takeTraderContactVo.setTraderType(ErpConst.ONE);

            TraderContactDto traderContactDto = traderCustomerService.getTraderContactDto(takeTraderContactVo);
            List<TraderContactVo> takeTraderContactList = traderContactDto.getContactList();
            List<TraderAddressVo> takeTraderAddressList = traderContactDto.getAddressList();

            mv.addObject("takeTraderAddressList", takeTraderAddressList);
            mv.addObject("takeTraderContactList", takeTraderContactList);

            for (TraderAddressVo traderAddressVo : takeTraderAddressList) {
                Integer traderAddressId = traderAddressVo.getTraderAddress().getTraderAddressId();
                if (traderAddressId!=null && traderAddressId.equals(saleorder.getTakeTraderAddressId()) && traderAddressVo.getTraderAddress()!=null) {
                    mv.addObject("addTakeTraderArea", traderAddressVo.getArea());
                    String address = traderAddressVo.getTraderAddress().getAddress();
                    address = StringUtils.isNotBlank(address) ? address.replaceAll("(\r\n|\r|\n|\n\r)", "") : address;
                    mv.addObject("addTakeTraderAddress",address);
                    mv.addObject("addTakeTraderAddressValue", traderAddressVo.getArea()+"/"+traderAddressVo.getTraderAddress().getAddress());
                }
            }
            TraderCustomerVo takeTraderCustomerInfo = traderCustomerService
                    .getCustomerInfo(saleorder.getTakeTraderId());
            mv.addObject("takeTraderCustomerInfo", takeTraderCustomerInfo);
        }

        // 收票信息 联系人&联系地址
        if ( saleorder.getInvoiceTraderId() != null
                && saleorder.getInvoiceTraderId().intValue() > 0) {
            TraderContactVo invoiceTraderContactVo = new TraderContactVo();
            invoiceTraderContactVo.setTraderId(saleorder.getInvoiceTraderId());
            invoiceTraderContactVo.setIsEnable(1);
            invoiceTraderContactVo.setTraderType(ErpConst.ONE);

            TraderContactDto traderContactDto = traderCustomerService.getTraderContactDto(invoiceTraderContactVo);
            List<TraderContactVo> invoiceTraderContactList = traderContactDto.getContactList();
            List<TraderAddressVo> invoiceTraderAddressList = traderContactDto.getAddressList();

            mv.addObject("invoiceTraderContactList", invoiceTraderContactList);
            mv.addObject("invoiceTraderAddressList", invoiceTraderAddressList);


            for (TraderAddressVo traderAddressVo : invoiceTraderAddressList) {
                Integer traderAddressId = traderAddressVo.getTraderAddress().getTraderAddressId();
                if (traderAddressId!=null && traderAddressId.equals(saleorder.getInvoiceTraderAddressId()) && traderAddressVo.getTraderAddress()!=null) {
                    mv.addObject("addInvoiceTraderArea", traderAddressVo.getArea());
                    String address = traderAddressVo.getTraderAddress().getAddress();
                    address = StringUtils.isNotBlank(address) ? address.replaceAll("(\r\n|\r|\n|\n\r)", "") : address;

                    mv.addObject("addInvoiceTraderAddress", address);
                    mv.addObject("addInvoiceTraderAddressValue", traderAddressVo.getArea()+"/"+traderAddressVo.getTraderAddress().getAddress());
                }
            }


            TraderCustomerVo invoiceTraderCustomerInfo = traderCustomerService
                    .getCustomerInfo(saleorder.getInvoiceTraderId());
            mv.addObject("invoiceTraderCustomerInfo", invoiceTraderCustomerInfo);
        }

        //客户信息
        mv.addObject("customer", customer);
        //收货客户
        TraderCustomerVo takeCustomer = getCustomerInfo(customer,saleorder.getTakeTraderId());
        mv.addObject("takeCustomer", takeCustomer);

        //收票客户
        TraderCustomerVo invoiceCustomer = getCustomerInfo(customer,saleorder.getInvoiceTraderId());
        mv.addObject("invoiceCustomer", invoiceCustomer);

        if (null != customer) {
            // 客户类型
            saleorder.setCustomerTypeStr(customer.getCustomerTypeStr());
            // 客户性质
            saleorder.setCustomerNatureStr(customer.getCustomerNatureStr());
        }
    }

    public TraderCustomerVo getCustomerInfo(TraderCustomerVo traderCustomer, Integer traderId){
        TraderCustomerVo customer = null;
        if (traderCustomer.getTraderId() !=null && traderCustomer.getTraderId().equals(traderId)){
            customer = traderCustomer;
        }else {
            customer = traderCustomerService.getCustomerInfo(traderId);
        }
        return customer;
    }

    public void setQuoteOrderInfo(Saleorder saleorder){
        if (saleorder != null && saleorder.getQuoteorderId() != null) {
            Quoteorder quoteOrderInfoById = quoteService.getQuoteOrderInfoById(saleorder.getQuoteorderId());
            if (quoteOrderInfoById != null && quoteOrderInfoById.getQuoteorderNo() != null && quoteOrderInfoById.getQuoteorderId() != null) {
                saleorder.setQuoteorderId(quoteOrderInfoById.getQuoteorderId());
                saleorder.setQuoteorderNo(quoteOrderInfoById.getQuoteorderNo());
            }
            if (quoteOrderInfoById != null && quoteOrderInfoById.getBussinessChanceNo() != null && quoteOrderInfoById.getBussinessChanceId() != null) {
                saleorder.setBussinessChanceId(quoteOrderInfoById.getBussinessChanceId());
                saleorder.setBussinessChanceNo(quoteOrderInfoById.getBussinessChanceNo());
            }
        }
    }

    public void getUserInfo(Saleorder saleorder,ModelAndView mv,User curr_user){
        if(saleorder.getOwnerUserId() != null){
            User ownerUser = this.userService.getUserById(saleorder.getOwnerUserId());
            if(ownerUser != null){
                saleorder.setOwnerUserName(ownerUser.getUsername());
            }
        }
        User user = null;
        try {

            // 销售人员名称
            user = userService.getUserById(saleorder.getUserId());
            saleorder.setSalesName(user == null ? "" : user.getUsername());
            // 创建人员
            user = userService.getUserById(saleorder.getCreator());
            saleorder.setCreatorName(user == null ? "" : user.getUsername());
            // 归属销售
            user = userService.getUserByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
            saleorder.setOptUserName(user == null ? "" : user.getUsername());
            saleorder.setOptUserId(user == null ? 0 : user.getUserId());
            // 销售部门（若一个多个部门，默认取第一个部门）
            Organization org = orgService.getOrgNameByUserId(saleorder.getUserId());
            //判断是否为科研购及其子部门
            mv.addObject("isScientificDept",orgService.getKYGOrgFlagByTraderId(saleorder));
            //判断是否是医械购及其子部门
            mv.addObject("isYxgOrgFlag",orgService.isBelongByOrgNameAndTraderId(saleorder,ErpConst.YXG_ORG_NAME));
            saleorder.setSalesDeptName(org == null ? "" : org.getOrgName());
        } catch (Exception e) {
            logger.error("请先关联归属销售", e);
        }
    }

    public void dealWithCustomerNature(Saleorder saleorder,ModelAndView mv){
        if (ErpConst.CUSTOME_RNATURE.equals(saleorder.getCustomerNature()) || ErpConst.FIVE.equals(saleorder.getOrderType())) {// 分销或耗材订单
            // 省级地区
            getFirstProvince(mv);
            if (saleorder.getSalesAreaId() != null) {
                // 地区
                List<Region> regionList = (List<Region>) regionService.getRegion(saleorder.getSalesAreaId(), 1);
                if (regionList != null && (!regionList.isEmpty())) {
                    for (Region r : regionList) {
                        switch (r.getRegionType()) {
                            case 1:
                                List<Region> cityList = regionService.getRegionByParentId(r.getRegionId());
                                mv.addObject("provinceRegion", r);
                                mv.addObject("cityList", cityList);
                                break;
                            case 2:
                                List<Region> zoneList = regionService.getRegionByParentId(r.getRegionId());
                                mv.addObject("cityRegion", r);
                                mv.addObject("zoneList", zoneList);
                                break;
                            case 3:
                                mv.addObject("zoneRegion", r);
                                break;
                            default:
                                mv.addObject("countryRegion", r);
                                break;
                        }
                    }
                }

            }
        }
    }

    /**
     * @desc 耗材订单（无法根据联系人地址id匹配）查询省市区
     * <AUTHOR>
     * @param mv
     * @param saleorder
     */
    private void getProvinces(ModelAndView mv,Saleorder saleorder) {
        // 收货地区最小级ID
        Integer traderAreaId = saleorder.getTraderAreaId();
        // 存在
        if (null != traderAreaId && !ErpConst.ZERO.equals(traderAreaId)) {
            // 根据地区最小Id查询省市区ID
            String traderAddressIdStr = regionService.getRegionIdStringByMinRegionId(traderAreaId);
            if (null != traderAddressIdStr) {
                String[] traderAddressIdArr = traderAddressIdStr.split(",");
                // 长度为3
                if (null != traderAddressIdArr && traderAddressIdArr.length == 3) {
                    // 省地区id
                    String provinceId = traderAddressIdArr[0];
                    if (provinceId != null && StringUtil.isNumeric(provinceId)) {
                        Region region = regionService.getRegionByRegionId(Integer.valueOf(provinceId));
                        mv.addObject("traderAddressIdProvince", region.getRegionId());
                    }
                    // 市地区Id
                    String cityId = traderAddressIdArr[1];
                    mv.addObject("traderAddressIdCity", cityId);
                    List<Region> traderAddressIdCityList = StringUtil.isBlank(provinceId) ? null
                            : regionService.getRegionByParentId(Integer.parseInt(provinceId));
                    mv.addObject("traderAddressIdCityList", traderAddressIdCityList);
                    // 区
                    mv.addObject("traderAddressIdZone", traderAddressIdArr[2]);
                    List<Region> traderAddressZoneList = StringUtil.isBlank(provinceId) ? null
                            : regionService.getRegionByParentId(Integer.parseInt(cityId));
                    mv.addObject("traderAddressZoneList", traderAddressZoneList);
                }
            }
        }
        // 收货地区最小级ID
        Integer takeTraderAreaId = saleorder.getTakeTraderAreaId();
        // 存在
        if (null != takeTraderAreaId && !ErpConst.ZERO.equals(takeTraderAreaId)) {
            // 根据地区最小Id查询省市区ID
            String takeTraderAddressIdStr = regionService.getRegionIdStringByMinRegionId(takeTraderAreaId);
            if (null != takeTraderAddressIdStr) {
                String[] takeTraderAddressIdArr = takeTraderAddressIdStr.split(",");
                // 长度为3
                if (null != takeTraderAddressIdArr && takeTraderAddressIdArr.length == 3) {
                    // 省地区id
                    String provinceId = takeTraderAddressIdArr[0];
                    if(provinceId!=null&&StringUtil.isNumeric(provinceId)) {
                        Region region = regionService.getRegionByRegionId(Integer.valueOf(provinceId));
                        mv.addObject("takeTraderAddressIdProvince", region.getRegionId());
                    }
                    // 市地区Id
                    String cityId = takeTraderAddressIdArr[1];
                    mv.addObject("takeTraderAddressIdCity", cityId);
                    List<Region> takeTraderAddressCityList = StringUtil.isBlank(provinceId) ? null
                            : regionService.getRegionByParentId(Integer.parseInt(provinceId));
                    mv.addObject("takeTraderAddressCityList", takeTraderAddressCityList);
                    // 区
                    mv.addObject("takeTraderAddressIdZone", takeTraderAddressIdArr[2]);
                    List<Region> takeTraderAddressZoneList = StringUtil.isBlank(cityId) ? null
                            : regionService.getRegionByParentId(Integer.parseInt(cityId));
                    mv.addObject("takeTraderAddressZoneList", takeTraderAddressZoneList);
                }
            }
        }
        // 收票地区最小级ID
        Integer invoiceTraderAreaId = saleorder.getInvoiceTraderAreaId();
        // 存在
        if (null != invoiceTraderAreaId && !ErpConst.ZERO.equals(invoiceTraderAreaId)) {
            // 根据地区最小Id查询省市区ID
            String invoiceTraderAddressIdStr = regionService.getRegionIdStringByMinRegionId(invoiceTraderAreaId);
            if (null != invoiceTraderAddressIdStr) {
                String[] invoiceTraderAddressIdArr = invoiceTraderAddressIdStr.split(",");
                // 长度为3
                if (null != invoiceTraderAddressIdArr && invoiceTraderAddressIdArr.length == 3) {
                    // 省地区ID
                    String provinceId = invoiceTraderAddressIdArr[0];
                    mv.addObject("invoiceTraderAddressIdProvince", provinceId);
                    // 市地区Id
                    String citiyId = invoiceTraderAddressIdArr[1];
                    mv.addObject("invoiceTraderAddressIdCity", citiyId);
                    List<Region> invoiceCityList = StringUtil.isBlank(provinceId) ? null
                            : regionService.getRegionByParentId(Integer.parseInt(provinceId));
                    mv.addObject("invoiceCityList", invoiceCityList);
                    // 区地区Id
                    mv.addObject("invoiceTraderAddressIdZone", invoiceTraderAddressIdArr[2]);
                    List<Region> invoiceZoneList = StringUtil.isBlank(provinceId) ? null
                            : regionService.getRegionByParentId(Integer.parseInt(citiyId));
                    mv.addObject("invoiceZoneList", invoiceZoneList);
                }
            }
        }
    }


    public void getSaleOrderGoods(Saleorder saleorder,TraderCustomerVo customer, ModelAndView mv){
        // 获取订单产品信息
        Saleorder sale = new Saleorder();
        sale.setSaleorderId(saleorder.getSaleorderId());
        sale.setTraderId(saleorder.getTraderId());
        sale.setCompanyId(saleorder.getCompanyId());
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleOrderGoodsList(sale);
        saleorderService.setSaleOrderGoodsImg(saleorderGoodsList);

        // 获取订单内部备注组件
        if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
            for (SaleorderGoods saleorderGoods:saleorderGoodsList) {
                List<String> skuNoList = new ArrayList<>();
                skuNoList.add(saleorderGoods.getSku());
                LabelQuery labelQuery = new LabelQuery();
                // 根据当前场景获取
                labelQuery.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
                labelQuery.setRelationId(saleorderGoods.getSaleorderId());
                labelQuery.setSkuNoList(skuNoList);
                String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
                saleorderGoods.setComponentHtml(componentHtml);
            }
        }

        for(int i=0;i<saleorderGoodsList.size();i++){
            saleorderGoodsList.get(i).getGoods().setUserList(saleorderService.getUserByCategory(saleorderGoodsList.get(i).getGoods().getCategoryId(), saleorderGoodsList.get(i).getGoods().getCompanyId()));
        }

        // 产品结算价
        saleorderGoodsList = goodsSettlementPriceService.getGoodsSettlePriceBySaleorderGoodsList(saleorder.getCompanyId(), saleorderGoodsList);
        // modify by franlin.wu for[避免空指针] at 2018-11-27 begin
        if (null != customer) {
            // 计算核价信息
            saleorderGoodsList = goodsChannelPriceService.getSaleChannelPriceList(saleorder.getSalesAreaId(),
                    saleorder.getCustomerNature(), customer.getOwnership(), saleorderGoodsList);
        }
        // modify by franlin.wu for[避免空指针] at 2018-11-27 end
        try{
            for (SaleorderGoods good:saleorderGoodsList) {
                CoreSkuGenerate sku= saleorderService.getSkuBySkuNo(good.getSku());
                if(sku==null){
                    continue;
                }
                CoreSpuGenerate spu=saleorderService.getSpuBySpuId(sku.getSpuId());
                if(spu==null){
                    continue;
                }
                if(spu.getSpuType().equals(1008)||spu.getSpuType().equals(316)){
                    //如果sku商品类型为器械设备或配件，产品信息的规格/型号取该sku的制造商型号
                    good.setModel(sku.getModel());
                }else{
                    //否则取该sku的规格
                    good.setModel(sku.getSpec());
                }
            }
        }catch (Exception e){
            log.error("【logicalorderChoose】处理异常",e);
        }

        try {
            setSkuAuthorizationVo(saleorderGoodsList);
        } catch (Exception e) {
            logger.error("检索sku报备信息error", e);
        }
        //订单流二期--jcf展示协议商品start
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case JCF:
            case JCO:{
                //查询sku是否为协议商品
                for(SaleorderGoods item : saleorderGoodsList){
                    ContractPriceInfoDetailResponseDto contractPriceInfoDetail = this.basePriceService.findSkuContractInfoByCon(saleorder.getTraderId(), item.getSku());
                    if (contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null) {
                        //协议商品
                        item.setContractedGoodsFlag(CommonConstants.ON);
                    }
                }
                break;
            }
            default:{
                break;
            }
        }
        //订单流二期--jcf展示协议商品end
        //将特殊商品"运费"，放在订单商品集合的最后，便于在编辑页面展示
        List<SaleorderGoods> yunfeiGoods = saleorderGoodsList.stream().filter(item -> "V127063".equals(item.getSku())).collect(Collectors.toList());
        saleorderGoodsList = saleorderGoodsList.stream().filter(item -> !"V127063".equals(item.getSku())).collect(Collectors.toList());
        saleorderGoodsList.addAll(yunfeiGoods);
        mv.addObject("saleorderGoodsList", saleorderGoodsList);
        //风控
        riskCheckService.setSaleorderIsRiskInfo(saleorder, saleorderGoodsList);

        if (null != saleorder) {
            if (saleorder.getPaymentType() == 0) {
                saleorder.setPaymentType(419);
            }
        }

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        if(!CollectionUtils.isEmpty(saleorderGoodsList)){
            List<Integer> skuIds = new ArrayList<>();
            saleorderGoodsList.stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String,Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        //核价信息应用 add by brianna 2020/5/29 start
//        List<GoodSalePrice> goodSalePriceList = saleorderGoodsList.stream().map(saleorderGood -> {
//            return new GoodSalePrice(saleorderGood.getSku(),saleorderGood.getChannelPrice());
//        }).collect(Collectors.toList());

//        Map<String,String> skuNoAndPriceMap = priceInfoDealWithService.dealWithGoodsSalePrice(sale.getTraderId() ,goodSalePriceList);
//        mv.addObject("skuNoAndPriceMap", skuNoAndPriceMap);
        //核价信息应用 add by brianna 2020/5/29 end
        Map<String,BigDecimal> moneyData=saleorderService.getSaleorderDataInfo(saleorder.getSaleorderId());
        if(moneyData!=null&&moneyData.get("realAmount")!=null){
            mv.addObject("realAmount",moneyData.get("realAmount"));
        }
        mv.addObject("regions", regionService.getRegionByParentId(ErpConst.ONE));
        mv.addObject("terminalTypes", skuAuthorizationService.getAllTerminalTypes());

    }

    @ResponseBody
    @RequestMapping(value = "getsaleordergoodsPriceInfo")
    @NoNeedAccessAuthorization
    public ResultInfo<?> getsaleordergoodsPriceInfo(HttpServletRequest request, Saleorder saleorder) {
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        if (null != curr_user) {
            saleorder.setCompanyId(curr_user.getCompanyId());
        }
        // 获取订单产品信息
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
        Map<String,String> saleorderGoods = saleOrderService.getsaleordergoodsPriceInfo(saleorder);
        return ResultInfo.success(saleorderGoods);
    }

    /**
     * 批量修改发货方式、内部备注页面
     * @param request
     * @param saleorderGoodsIdArr
     * @param saleorderId
     * @param scene
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/updateSaleGoodsInit")
    public ModelAndView updateSaleGoodsInit(HttpServletRequest request,
                                            @RequestParam(required = false, value = "saleorderGoodsIdArr") String saleorderGoodsIdArr,
                                            @RequestParam(required = false, value = "saleorderId") Integer saleorderId,Integer scene) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("saleorderGoodsIdArr", saleorderGoodsIdArr);
        mv.addObject("saleorderId", saleorderId);
        mv.addObject("scene", scene);
        if (ErpConst.ONE.equals(scene)){
            LabelQuery labelQuery = saleorderService.dealWidthInsideComments(saleorderGoodsIdArr, saleorderId, scene);
            mv.addObject("labelQuery", labelQuery);
        }
        mv.setViewName("/orderstream/saleorder/update_sale_goods");
        return mv;
    }

    /**
     * 批量添加产品
     * @param request
     * @param saleorder
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/batchAddSaleGoodsInit")
    public ModelAndView batchAddSaleGoodsInit(HttpServletRequest request, Saleorder saleorder) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("saleorder", saleorder);
        mv.setViewName("orderstream/saleorder/batch_add_sale_goods");
        return mv;
    }

    /**
     *
     * 保存销售单批量新增商品
     *
     * @param request
     * @param saleorder
     * @param lwfile
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveBatchAddSaleGoods")
    public ResultInfo<?> saveBatchAddSaleGoods(HttpServletRequest request, Saleorder saleorder,
                                               @RequestParam("lwfile") MultipartFile lwfile) {
        ResultInfo<?> resultInfo = new ResultInfo<>();
        Workbook workbook =null;
        FileInputStream inputStream=null;
        try {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            // 临时文件存放地址
            String path = request.getSession().getServletContext().getRealPath("/upload/saleorder");
            FileInfo fileInfo = ftpUtilService.fileUploadServe(path, lwfile);

            if (fileInfo.getCode() == 0) {// 上传成功

                if (user != null) {
                    saleorder.setCompanyId(user.getCompanyId());

                    saleorder.setAddTime(DateUtil.gainNowDate());
                    saleorder.setCreator(user.getUserId());
                    saleorder.setUpdater(user.getUserId());
                    saleorder.setModTime(DateUtil.gainNowDate());
                }
                // 获取excel路径
                inputStream=  new FileInputStream(new File(fileInfo.getFilePath()));
                  workbook = WorkbookFactory.create(inputStream);
                // 获取第一面sheet
                Sheet sheet = workbook.getSheetAt(0);
                // 起始行
                int startRowNum = sheet.getFirstRowNum() + 1;// 第一行标题
                int endRowNum = sheet.getLastRowNum();// 结束行

                int startCellNum = 0;// 默认从第一列开始（防止第一列为空）
                // int endCellNum = sheet.getRow(0).getLastCellNum() - 1;//列数
                List<SaleorderGoods> list = new ArrayList<>();// 保存Excel中读取的数据
                for (int rowNum = startRowNum; rowNum <= endRowNum; rowNum++) {// 循环行数
                    Row row = sheet.getRow(rowNum);
                    // 获取excel的值
                    SaleorderGoods sg = new SaleorderGoods();
                    setUserMsg(sg, user, DateUtil.gainNowDate());
                    for (int cellNum = startCellNum; cellNum <= 3; cellNum++) {// 循环列数（下表从0开始）
                        Cell cell = row.getCell(cellNum);
                        try {
                            if ((cell == null || "".equals(cell)) && cellNum <= 3) {// cell==null单元格空白（无内容，默认空）
                                resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
                                throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
                            }
                            CellType type = cell.getCellType();
                            String str = null;
                            Double pice = null;
                            Integer num = null;
                            BigDecimal bd = null;
                            switch (cellNum) {
                                case 0:// SKU（唯一识别，不为空）
                                    switch (type) {
                                        case STRING:// 字符类型
                                            str = cell.getStringCellValue();
                                            if (str == null || "".equals(str)) {
                                                resultInfo.setMessage(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不允许为空，请验证！");
                                                throw new Exception(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不允许为空，请验证！");
                                            }
                                            if (!str.matches("[A-Za-z0-9]+")) {
                                                resultInfo.setMessage(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：SKU值不符合规则，请验证！");
                                                throw new Exception(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：SKU值不符合规则，请验证！");
                                            }
                                            break;
                                        default:
                                            resultInfo.setMessage(
                                                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：SKU值不符合规则，请验证！");
                                            throw new Exception(
                                                    "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：SKU值不符合规则，请验证！");
                                    }
                                    for (int x = 0; x < list.size(); x++) {
                                        if (str.equals(list.get(x).getSku())) {
                                            resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1)
                                                    + "列：SKU在此Excel表格中存在重复，请验证！");
                                            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1)
                                                    + "列：SKU在此Excel表格中存在重复，请验证！");
                                        }
                                    }
                                    sg.setSku(str);
                                    break;
                                case 1:// 数量
                                    switch (type) {
                                        case NUMERIC:// 数字类型
                                            num = (int) cell.getNumericCellValue();
                                            if (num == null || "".equals(num)) {
                                                resultInfo.setMessage(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不允许为空，请验证！");
                                                throw new Exception(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不允许为空，请验证！");
                                            }
                                            if (!(num + "").matches("^[1-9]+[0-9]*$")) {
                                                resultInfo.setMessage(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不符合规则，请验证！");
                                                throw new Exception(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不符合规则，请验证！");
                                            }
                                            break;
                                        default:
                                            resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值错误，请验证！");
                                            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值错误，请验证！");
                                    }
                                    sg.setNum(Integer.valueOf(num));
                                    break;
                                case 2:// 单价
                                    switch (type) {
                                        case NUMERIC:// 数字类型
                                            pice = cell.getNumericCellValue();
                                            if (pice == null) {
                                                resultInfo.setMessage(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不允许为空，请验证！");
                                                throw new Exception(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不允许为空，请验证！");
                                            }
//											if () {
                                            BigDecimal priceDecimal=new BigDecimal(pice);
                                            if(!(StringUtil.doubleToString(pice)).matches("[0-9]{1,14}\\.{0,1}[0-9]{0,2}")||priceDecimal.compareTo(OrderConstant.AMOUNT_LIMIT)==1){
                                                resultInfo.setMessage(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不符合规则，请验证！");
                                                throw new Exception(
                                                        "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值不符合规则，请验证！");
                                            }
                                            break;
                                        default:
                                            resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值错误，请验证！");
                                            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值错误，请验证！");
                                    }
                                    bd = new BigDecimal(pice);
                                    sg.setPrice(bd.setScale(2, BigDecimal.ROUND_HALF_UP));
                                    if(sg.getPrice().multiply(new BigDecimal(sg.getNum())).compareTo(OrderConstant.AMOUNT_LIMIT)==1){
                                        resultInfo.setMessage("第:" + (rowNum + 1) + "行,单个商品总额超过三亿,请验证!");
                                        throw new Exception("第:" + (rowNum + 1) + "行,单个商品总额超过三亿,请验证!");
                                    }
                                    break;
                                case 3:// 获取
                                    switch (type) {
                                        case NUMERIC:// 数字类型
                                            str = cell.getNumericCellValue() + "";
                                            break;
                                        case STRING:// 字符类型
                                            str = cell.getStringCellValue();
                                            break;
                                        case  BLANK:
                                            resultInfo
                                                    .setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
                                            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：不允许为空，请验证！");
                                        default:
                                            resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值错误，请验证！");
                                            throw new Exception("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值错误，请验证！");
                                    }
                                    if (StringUtils.isNotBlank(str) && str.length() > 32) {
                                        resultInfo.setMessage(
                                                "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值超出最大范围，请验证！");
                                        throw new Exception(
                                                "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：值超出最大范围，请验证！");
                                    }
                                    sg.setDeliveryCycle(str);
                                    break;
                                default:
                                    resultInfo.setMessage(
                                            "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：获取参数值异常，请稍等重试或联系管理员！");
                                    throw new Exception(
                                            "第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列：获取参数值异常，请稍等重试或联系管理员！");
                            }
                        } catch (Exception e) {
                            //AROP-4172，错误日志处理，手动抛出的读取excel异常返回前台
//							logger.error("saveBatchAddSaleGoods 1:", e);
                            if ("操作失败".equals(resultInfo.getMessage())) {
                                resultInfo.setMessage("第:" + (rowNum + 1) + "行，第:" + (cellNum + 1) + "列读取错误，请验证！");
                            }
                            return resultInfo;
                        }
                    }
                    sg.setSaleorderId(saleorder.getSaleorderId());
                    list.add(sg);
                }
                if (list != null && !list.isEmpty()) {
                    saleorder.setGoodsList(list);
                    // 批量保存
                    resultInfo = saleorderService.saveBatchAddSaleGoods(saleorder);
                } else {
                    resultInfo.setMessage("Excel数据读取异常！");
                }
            }
        } catch (Exception e) {
            logger.error("saveBatchAddSaleGoods 2:", e);
            return new ResultInfo<>(-1, "Excel数据读取异常");
        }finally {
            try {
                inputStream.close();
            }catch (Exception e){
                log.error("【saveBatchAddSaleGoods】处理异常",e);
            }
            try {
                workbook.close();
            }catch (Exception e){
                log.error("【saveBatchAddSaleGoods】处理异常",e);
            }
        }
        return resultInfo;
    }

    /**
     *  销售订单商品编辑保存
     * @param request
     * @param session
     * @param saleorderGoods
     * @param saleorderGoodsIdArr
     * @return
     */
    @ResponseBody
    @RequestMapping("/updateSaleGoodsSave")
    public ResultInfo<?> updateSaleGoodsSave(HttpServletRequest request,HttpSession session, SaleorderGoods saleorderGoods,
                                             @RequestParam(required = false, value = "saleorderGoodsIdArr") String saleorderGoodsIdArr) {
        List<Integer> saleOrderGoodsIdList = JSON.parseArray("[" + saleorderGoodsIdArr + "]", Integer.class);
        if (saleOrderGoodsIdList == null || saleOrderGoodsIdList.size() == 0) {
            return new ResultInfo<>(-1, "参数错误");
        }
        saleorderGoods.setSaleorderGoodsIdList(saleOrderGoodsIdList);
        User user = getSessionUser(request);
        if (user != null) {
            saleorderGoods.setCompanyId(user.getCompanyId());
            saleorderGoods.setModTime(DateUtil.gainNowDate());
            saleorderGoods.setUpdater(user.getUserId());
        }
        return saleorderService.updateSaleGoodsSave(saleorderGoods);
    }


    /**
     * 选择产品页面
     * @param request
     * @param goodsQuery
     * @param scene 8,新增虚拟商品
     * @param saleorderId
     * @return
     */
    @RequestMapping(value = "/addSaleorderGoods")
    public ModelAndView addSaleorderGoods(HttpServletRequest request,
                                          GoodsQuery goodsQuery,
                                          @RequestParam(required = false, defaultValue = "0")Integer scene,
                                          @RequestParam(required = false, defaultValue = "0")Integer saleorderId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("scene",scene);
        if (!Strings.isNullOrEmpty(goodsQuery.getSearchContent()) || goodsQuery.getGoodsBrandId() != null
                || goodsQuery.getGoodsType() != null || goodsQuery.getTypeSpecification() != null
                || goodsQuery.getUnitName() != null) {
            User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
            Page page = getPageTag(request, goodsQuery.getPageNo(), goodsQuery.getPageSize());
            Goods goods = new Goods();
            goods.setCompanyId(user.getCompanyId());
            goods.setSearchContent(goodsQuery.getSearchContent());
            goods.setBrandId(goodsQuery.getGoodsBrandId());
            if (ErpConst.ZERO.equals(goodsQuery.getGoodsType())){
                goods.setGoodsType(null);
            }else {
                goods.setGoodsType(goodsQuery.getGoodsType());
            }
            //添加组合搜索，规格型号
            goods.setSpecModel(goodsQuery.getTypeSpecification());
            goods.setUnitName(goodsQuery.getUnitName());
            Map<String, Object> map = new  HashMap<String, Object>();
            List<GoodsVo> goodsList = goodsService.queryGoodsNewListPage(goods, page);
            //查询库存
            getStockInfo(goodsList);
            mv.addObject("goodsList", goodsList);
            mv.addObject("page", page);
            mv.addObject("searchContent", goodsQuery.getSearchContent());
            mv.addObject("goodsTypeValue", goodsQuery.getGoodsType());
            mv.addObject("zxfunitNameValue", goodsQuery.getUnitName());
            mv.addObject("typeSpecification", goodsQuery.getTypeSpecification());
            mv.addObject("brandName", goodsQuery.getGoodsBrandId());
            mv.addObject("goodsBrandIdValue", goodsQuery.getGoodsBrandId());
        }
        mv.addObject("saleorderId", saleorderId);
        mv.addObject("goodsTypeList", getSysOptionDefinitionInNewGoodsFlow());
//        mv.addObject("callbackFuntion", callbackFuntion);
//        mv.addObject("logicalId", logicalId);
        User user = getSessionUser(request);
        //单位
        Unit unit = new Unit();
        unit.setCompanyId(user.getCompanyId());
        List<Unit> unitList = unitService.getAllUnitList(unit);
        mv.addObject("unitList", unitList);
        //品牌
        BrandGenerate brandGenerate = new BrandGenerate();
        brandGenerate.setCompanyId(ErpConst.ONE);
        brandGenerate.setIsDelete(ErpConst.ZERO);
        List<BrandGenerate> brandList = brandService.getBrandInfoByParam();
        mv.addObject("brandList", brandList);
        mv.setViewName("orderstream/saleorder/add_saleorder_goods");
        return mv;

    }

    public void getStockInfo(List<GoodsVo> goodsList){
        if (CollectionUtils.isNotEmpty(goodsList)){
            List<String> skuList = goodsList.stream().map(Goods::getSku).collect(Collectors.toList());
            Map<String, WarehouseStock> warehouseStocks = warehouseStockService.getStockInfo(skuList);
            for (GoodsVo goodsVo : goodsList) {
                if (warehouseStocks.containsKey(goodsVo.getSku())){
                    goodsVo.setStockNum(warehouseStocks.get(goodsVo.getSku()).getStockNum());
                }
            }
        }
    }

    /**
     * 保存订单产品信息
     * @param request
     * @param saleorderGoods
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/saveSaleorderGoods")
    @SystemControllerLog(operationType = "add", desc = "保存订单产品信息")
    public ResultInfo<?> saveSaleorderGoods(HttpServletRequest request, SaleorderGoods saleorderGoods) {
        log.info("保存订单产品信息：{}",JSON.toJSON(saleorderGoods));
        User user = getSessionUser(request);
        if(saleorderGoods.getDeliveryDirect()==null) {
            saleorderGoods.setDeliveryDirect(0);
        }
        //新增商品价格为空时也认为是赠品
        if (Objects.isNull(saleorderGoods.getPrice())){
            saleorderGoods.setPrice(BigDecimal.ZERO);
        }
        //一个订单下相同商品只允许一个为非赠品一个为赠品
        Saleorder sOrder = new Saleorder();
        sOrder.setSaleorderId(saleorderGoods.getSaleorderId());
        List<SaleorderGoods> goodsList = saleorderGoodsMapper.selectSaleorderGoodsListBySaleorderId(sOrder);
        for (SaleorderGoods good : goodsList) {
            if (good.getSku().equals(saleorderGoods.getSku()) && !good.getSaleorderGoodsId().equals(saleorderGoods.getSaleorderGoodsId())){
                if (good.getPrice().compareTo(new BigDecimal("0")) == 0 && saleorderGoods.getPrice().compareTo(new BigDecimal("0")) == 0){
                    return new ResultInfo(-1, "已有相同订货号的赠品，不允许重复添加！");
                }
                if (!(good.getPrice().compareTo(new BigDecimal("0")) == 0) && !(saleorderGoods.getPrice().compareTo(new BigDecimal("0")) == 0)){
                    return new ResultInfo(-1, "已有相同订货号的产品，不允许重复添加！");
                }
            }
        }
        Saleorder saleorder = saleorderMapper.getSaleOrderById(saleorderGoods.getSaleorderId());

        boolean isBDOrder = ORDER_TYPE_BD.equals(saleorder.getOrderType());
        boolean isHCOrder = OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType());
        boolean isJCOrder = SaleOrderTypeEnum.isJcOrder(saleorder.getOrderType());

        if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
            // add by Randy.Xu 2021/3/29 19:05 .Desc: . begin
            // VDERP-5839 数据越权处理 只有申请人可在自己的备货单中添加商品
            boolean isBHOrder = OrderConstant.ORDER_TYPE_BH.equals(saleorder.getOrderType());
            if (isBHOrder) {
                List<User> userListByorderId = authService.getUserListByorderId(saleorderGoods.getSaleorderId(), authService.BH_SALEORDER_TYPE);
                Boolean checkFlag = authService.existOrNot(user, userListByorderId);
                if (checkFlag) {
                    logger.info("销售越权操作:接口[order/order/saleorder/saveSaleorderGoods],行为[修改非自己的备货单],操作人{}",user.getUsername());
                }
            } else {
                List<User> userListByorderId = authService.getUserListByorderId(saleorderGoods.getSaleorderId(), authService.SALEORDER_TYPE);
                Boolean checkFlag = authService.existOrNot(user, userListByorderId);
                if (checkFlag) {
                    logger.info("销售越权操作:接口[order/order/saleorder/saveSaleorderGoods],行为[修改非自己及下属的订单],操作人{}",user.getUsername());
                }
            }
            // add by Randy.Xu 2021/3/29 19:05 .Desc: . end
        }
        //修改
        if(saleorderGoods.getSaleorderGoodsId() != null){
            SaleorderGoods saleorderGoodsDB = saleorderGoodsMapper.selectByPrimaryKey(saleorderGoods.getSaleorderGoodsId());
            boolean useCoupon = false;
            if(isBDOrder){
                useCoupon = (saleorderGoodsDB.getIsCoupons() != null && saleorderGoodsDB.getIsCoupons() == 1);
            }
            if(isHCOrder){
                useCoupon = (saleorder.getIsCoupons() != null && saleorder.getIsCoupons() == 1);
            }
            //如果是BD订单且没使用优惠券 设置一下原始金额
            if((isBDOrder || isHCOrder || isJCOrder) && !useCoupon){
                saleorderGoods.setRealPrice(saleorderGoods.getPrice());
                BigDecimal priceToUse = saleorderGoods.getPrice() == null ? BigDecimal.ZERO : saleorderGoods.getPrice();
                saleorderGoods.setMaxSkuRefundAmount(priceToUse.multiply(new BigDecimal(saleorderGoods.getNum())));
            }
            //新增
        }else{
            if(isBDOrder || isJCOrder){
                saleorderGoods.setRealPrice(saleorderGoods.getPrice());
                BigDecimal priceToUse = saleorderGoods.getPrice() == null ? BigDecimal.ZERO : saleorderGoods.getPrice();
                saleorderGoods.setMaxSkuRefundAmount(priceToUse.multiply(new BigDecimal(saleorderGoods.getNum())));
            }
        }

        setUserMsg(saleorderGoods, user, DateUtil.sysTimeMillis());
        saleorderGoods.setUpdateDataTime(new Date());
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
        ResultInfo resultInfo = saleOrderService.saveSaleorderGoods(saleorderGoods);
        /**
         *如果是耗材订单并且耗材订单总金额不小于199，运费的金额需要减免
         */
        Saleorder oldSaleorder = new Saleorder();
        oldSaleorder.setSaleorderId(saleorderGoods.getSaleorderId());
        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(oldSaleorder);
        if (saleorderInfo.getOrderType() == 5){
            List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoods(oldSaleorder);
            BigDecimal totalPrice = new BigDecimal(0);
            BigDecimal carriage = new BigDecimal(0);
            if (CollectionUtils.isNotEmpty(saleorderGoodsList)){
                for (SaleorderGoods goods : saleorderGoodsList) {
                    if (127063 == goods.getGoodsId()){
                        carriage = goods.getPrice();
                    } else {
                        totalPrice = totalPrice.add(goods.getPrice().multiply(new BigDecimal(goods.getNum())));
                    }
                }
            }
            //判断运费的实际金额
            if (totalPrice.compareTo(new BigDecimal(199)) == -1){
                carriage = new BigDecimal(8);
            } else {
                carriage = new BigDecimal(0);
            }
            //修改运费
            if (CollectionUtils.isNotEmpty(saleorderGoodsList)){
                for (SaleorderGoods goods : saleorderGoodsList) {
                    if (127063 == goods.getGoodsId()){
                        goods.setPrice(carriage);
                        goods.setRealPrice(carriage);
                        goods.setGoodsId(null);
                        setUserMsg(goods,user,DateUtil.sysTimeMillis());
                        saleorderService.saveSaleorderGoods(goods);
                    }
                }
            }
        }
        return resultInfo;
    }

    private void setUserMsg(SaleorderGoods saleorderGoods, User user, long l) {
        if (user != null) {
            saleorderGoods.setCreator(user.getUserId());
            saleorderGoods.setAddTime(l);

            saleorderGoods.setUpdater(user.getUserId());
            saleorderGoods.setModTime(l);
        }
    }

    /**
     * 添加产品校验接口
     * @param saleorderId
     * @param goodsId
     * @param skuNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/vailSaleorderGoodsRepeat", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<?> vailOrderGoodsRepeat(Integer saleorderId, Integer goodsId,String skuNo) {
        if (saleorderId == null || goodsId == null|| Strings.isNullOrEmpty(skuNo)) {
            return ResultInfo.error("请求参数不为空");
        }
        SaleorderGoods saleorderGoods=new SaleorderGoods();
        saleorderGoods.setSaleorderId(saleorderId);
        saleorderGoods.setGoodsId(goodsId);
        //VDERP-12672取消前置相同产品校验
//        if(saleorderService.vailSaleorderGoodsRepeat(saleorderGoods)) {
//            return ResultInfo.error( "已有相同订货号的产品，不允许重复添加！");
//        }
        Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleorderId);
        return ResultInfo.success( "无相同订货号产品，允许添加！",saleorderGoods);
    }

    /**
     * 获取价格信息
     * @param saleorderId
     * @param goodsId
     * @param skuNo
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getOrderGoodsPrice", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResultInfo<?> getOrderGoodsPrice(Integer saleorderId, Integer goodsId,String skuNo) {
        Saleorder saleorder = saleorderService.getsaleorderbySaleorderId(saleorderId);
        Map<String, String> returnDateMap = new HashMap<>();
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case JCF:
            case JCO:{
                returnDateMap = getGoodsPrice(saleorderId,skuNo,null);
                break;
            }
            default:{
                returnDateMap = newSaleOrderService.getSaleOrderGoodsPrice(saleorderId, skuNo,null);
                break;
            }
        }
        return ResultInfo.success( "无相同订货号产品，允许添加！",returnDateMap);
    }

    /**
     * 添加产品信息页面
     * @param request
     * @param saleorderId
     * @param goodsId
     * @param skuNo
     * @param scene
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/editSelectedSaleorderGoodsDetail", produces = MediaType.APPLICATION_JSON_VALUE)
    public ModelAndView editSelectedSaleorderGoodsDetail(HttpServletRequest request,Integer saleorderId, Integer goodsId,String skuNo,Integer scene) {
        ModelAndView modelAndView = new ModelAndView();
        GoodsVo good = goodsService.queryGoodsBySku(skuNo);
        Map<String, String> returnDateMap = getGoodsPrice(saleorderId, skuNo, null);
        Saleorder saleorder = saleorderService.getSaleOrderById(saleorderId);
        Trader trader = null;
        if (saleorder.getTraderId() != null && saleorder.getTraderId() > 0){
            trader = traderCustomerService.selectTraderNameByTraderId(saleorder.getTraderId());
        }else {
            trader = traderCustomerService.selectTraderNameByTraderId(saleorder.getGroupCustomerId());
        }
        modelAndView.addObject("belongPlatform",trader !=null && trader.getBelongPlatform() != null ? trader.getBelongPlatform():0);
        modelAndView.addObject("good",good);
        modelAndView.addObject("saleorderId",saleorderId);
        modelAndView.addObject("scene",scene);
        modelAndView.setViewName("orderstream/saleorder/add_detail_goods");
        modelAndView.addObject("priceInfoMap",returnDateMap);
        return modelAndView;
    }



    private Map<String, String> getGoodsPrice(Integer saleOrderId, String  skuNo, BigDecimal defaultPrice) {
        Saleorder saleorder = saleorderService.getSaleOrderById(saleOrderId);

        Integer traderId = saleorder.getGroupCustomerId();

        String salePrice = "";
        String salePriceShow = "";
        String contractedGoodsFlag = CommonConstants.OFF.toString();

        //集采订单要查商品的协议价
        ContractPriceInfoDetailResponseDto contractPriceInfoDetail = this.basePriceService.findSkuContractInfoByCon(traderId, skuNo);
        //有合约价 就设置合约价
        if (contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null) {
            salePrice = contractPriceInfoDetail.getContractPrice().toPlainString();
            salePriceShow += (salePrice + "(协议价)");
            contractedGoodsFlag = CommonConstants.ON.toString();
        } else {
            //没有合约价,查基础价
            TraderCustomer traderCustomer = this.traderCustomerService.getTraderCustomerId(traderId);
            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = this.basePriceService.findSkuPriceInfoBySkuNo(skuNo);
            //已经核价就设置成本价
            if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {
                if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
                    //分销商
                    salePrice = skuPriceInfoDetailResponseDto.getDistributionPrice().toPlainString();
                    salePriceShow += (salePrice + "(经销价)");
                }else if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
                    //终端
                    salePrice = skuPriceInfoDetailResponseDto.getTerminalPrice().toPlainString();
                    salePriceShow += (salePrice + "(终端价)");
                }
            }
        }

        Map<String,String> returnMap = new HashMap<>();
        returnMap.put("limitPrice","false");
        if (defaultPrice == null) {
            returnMap.put("salePrice",salePrice);
        } else {
            returnMap.put("salePrice", defaultPrice.toString());
        }
        returnMap.put("salePriceShow",salePriceShow);
        returnMap.put("contractedGoodsFlag", contractedGoodsFlag);
        return returnMap;
    }
    /**
     * 获取产品价格信息
     * @param saleorderId
     * @param skuNo
     * @param price
     * @return
     */
    private Map<String, String> getSaleOrderGoodsPrice(Integer saleorderId,String  skuNo,BigDecimal price) {

        Saleorder saleorder = this.saleorderMapper.getSaleOrderById(saleorderId);

        Integer traderId = saleorder.getTraderId();

        ContractPriceInfoDetailResponseDto contractPriceInfoDetail = this.basePriceService.findSkuContractInfoByCon(traderId,skuNo);
        TraderCustomer traderCustomer = this.traderCustomerService.getTraderCustomerId(traderId);
        String salePrice = "";
        String salePriceShow = "";
        //有合约价 就设置合约价
        if(contractPriceInfoDetail != null && contractPriceInfoDetail.getContractPrice() != null){
            salePrice = contractPriceInfoDetail.getContractPrice().toPlainString();
            salePrice += (salePrice + "(合约价)");
            //没有合约价,查基础价
        }else{
            SkuPriceInfoDetailResponseDto skuPriceInfoDetailResponseDto = this.basePriceService.findSkuPriceInfoBySkuNo(skuNo);
            //已经核价就设置成本价
            if (skuPriceInfoDetailResponseDto != null && traderCustomer != null) {
                //分销商
                if(ErpConst.CUSTOME_RNATURE.equals(traderCustomer.getCustomerNature())){
                    salePrice = skuPriceInfoDetailResponseDto.getDistributionPrice().toPlainString();
                    salePriceShow += (salePrice + "(经销价)");
                }
                //终端
                if(ErpConst.CUSTOME_INTERMIAL.equals(traderCustomer.getCustomerNature())){
                    //客户类型 = 科研医疗 终端 取科研终端价(新增的)
                    if (ErpConst.CUSTOME_TYPE_RESEARCH_MEDICAL.equals(traderCustomer.getCustomerType())){
                        if (ObjectUtil.isNotNull(skuPriceInfoDetailResponseDto.getResearchTerminalPrice())){
                            salePrice = skuPriceInfoDetailResponseDto.getResearchTerminalPrice().toPlainString();
                            salePriceShow += (salePrice + "(科研终端价)");
                        }
                    }else {
                        salePrice = skuPriceInfoDetailResponseDto.getTerminalPrice().toPlainString();
                        salePriceShow += (salePrice + "(终端价)");
                    }
                }
            }
        }
        Map<String,String> returnMap = new HashMap<>();
        if (price == null) {
            returnMap.put("salePrice",salePrice);
        } else {
            returnMap.put("salePrice", price.toString());
        }
        returnMap.put("limitPrice","false");
        returnMap.put("salePriceShow",salePriceShow);
        return returnMap;
    }


    /**
     * 编辑产品信息
     * @param saleorderGoods
     * @param scene
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/editSaleorderGoods",produces = MediaType.TEXT_HTML_VALUE)
    public ModelAndView editSaleorderGoods(SaleorderGoods saleorderGoods,
                                           @RequestParam(defaultValue = "0", required = false) Integer scene) {
        ModelAndView modelAndView = new ModelAndView("/orderstream/saleorder/edit_order_goods");
        modelAndView.addObject("scene",scene);
        Integer saleorderGoodsId = saleorderGoods.getSaleorderGoodsId();
        // 根据订单产品ID获取对应产品信息
        saleorderGoods = saleorderService.getSaleorderGoodsInfoById(saleorderGoodsId);
        // 根据订单商品ID货区对应的订单信息
        Saleorder saleorderTmp = new Saleorder();
        saleorderTmp.setSaleorderId(saleorderGoods.getSaleorderId());
        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorderTmp);
        modelAndView.addObject("saleorder", saleorderInfo);
        modelAndView.addObject("saleorderGoods", saleorderGoods);

        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        Map<String,Object> newSkuInfo = vgoodsService.skuTip(saleorderGoods.getGoodsId());
        modelAndView.addObject("newSkuInfo", newSkuInfo);
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end
        Map<String, String> priceInfoMap = new HashMap<>();

        switch (SalesOrderTypeEnum.getInstance(saleorderInfo.getOrderType())){
            case JCF:
            case JCO:{
                priceInfoMap = getGoodsPrice(saleorderInfo.getSaleorderId(),saleorderGoods.getSku(),saleorderGoods.getPrice());
                break;
            }
            default:{
                priceInfoMap = newSaleOrderService.getSaleOrderGoodsPrice(saleorderInfo.getSaleorderId(), saleorderGoods.getSku(),saleorderGoods.getPrice());
                break;
            }
        }
        modelAndView.addObject("priceInfoMap", priceInfoMap);
        modelAndView.addObject("orderType", saleorderInfo.getOrderType());

        LabelQuery labelQuery = new LabelQuery();
        labelQuery.setScene(scene);
        labelQuery.setRelationId(saleorderGoods.getSaleorderId());
        labelQuery.setSkuNo(saleorderGoods.getSku());
        labelQuery.setSkuName(saleorderGoods.getGoodsName());
        String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
        modelAndView.addObject("componentHtml", componentHtml);
        modelAndView.addObject("bdUseCoupon", (ORDER_TYPE_BD.equals(saleorderInfo.getOrderType()) || ORDER_TYPE_HC.equals(saleorderInfo.getOrderType()))
                && saleorderInfo.getIsCoupons().equals(ErpConst.ONE) ? true : false);
        Trader trader = null;
        if (saleorderInfo.getTraderId() != null && saleorderInfo.getTraderId() > 0){
            trader = traderCustomerService.selectTraderNameByTraderId(saleorderInfo.getTraderId());
        }else {
            trader = traderCustomerService.selectTraderNameByTraderId(saleorderInfo.getGroupCustomerId());
        }
        modelAndView.addObject("belongPlatform",trader !=null && trader.getBelongPlatform() != null ? trader.getBelongPlatform():0);
        return modelAndView;
    }

    /**
     * 删除订单产品
     * @param request
     * @param saleorderGoods
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/delSaleorderGoodsById")
    @SystemControllerLog(operationType = "delete", desc = "删除订单产品")
    public ResultInfo<?> delSaleorderGoodsById(HttpServletRequest request, SaleorderGoods saleorderGoods) {
        User user = getSessionUser(request);
        if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
            Boolean checkFlag = saleorderPermissionCheck(user, saleorderGoods.getSaleorderId());
            if (checkFlag) {
                logger.info("销售越权操作:接口[order/saleorder/delSaleorderGoodsById],行为[删除非自己及下属订单的商品],操作人{}",user.getUsername());
            }
        }
        if (user != null) {
            saleorderGoods.setUpdater(user.getUserId());
            saleorderGoods.setModTime(DateUtil.sysTimeMillis());
        }
        return saleorderService.delSaleorderGoodsById(saleorderGoods);
    }

    private Boolean saleorderPermissionCheck(User user,Integer saleorderId){
        Saleorder saleorderTypeCheck = saleorderMapper.getSaleOrderById(saleorderId);
        boolean isBHOrder = OrderConstant.ORDER_TYPE_BH.equals(saleorderTypeCheck.getOrderType());
        Boolean checkFlag;
        if(isBHOrder){
            List<User> userListByorderId = authService.getUserListByorderId(saleorderId, authService.BH_SALEORDER_TYPE);
            checkFlag = authService.existOrNot(user,userListByorderId);
        }else{
            List<User> userListByorderId = authService.getUserListByorderId(saleorderId, authService.SALEORDER_TYPE);
            checkFlag = authService.existOrNot(user,userListByorderId);
        }
        return checkFlag;
    }


    /**
     * 保存订单详情信息
     *
     * @param request
     * @param session
     * @param saleorder
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/saveeditsaleorderinfo")
    @SystemControllerLog(operationType = "edit", desc = "保存编辑的订单信息")
    public ModelAndView saveEditSaleorderInfo(HttpServletRequest request,
                                              HttpSession session,
                                              Saleorder saleorder, OrderTerminalDto orderTerminalDto,
                                              @RequestParam(required = false, defaultValue = "0")Integer scene) {

        ModelAndView mv = new ModelAndView();
        User user = getSessionUser(request);
        VerifiesInfo verifiesInfoQuery = new VerifiesInfo();
        verifiesInfoQuery.setRelateTable("T_SALEORDER");
        verifiesInfoQuery.setRelateTableKey(saleorder.getSaleorderId());
        verifiesInfoQuery.setVerifiesType(623);
        List<VerifiesInfo> verifiesInfosOfSaleorder = verifiesInfoMapper.getVerifiesInfo(verifiesInfoQuery);
        int orderCheckStatus = CollectionUtils.isEmpty(verifiesInfosOfSaleorder) ? -1 : verifiesInfosOfSaleorder.get(0).getStatus();
        if (orderCheckStatus == 0){
            mv.addObject("message","订单已申请审核，请勿编辑订单");
            return fail(mv);
        }


        if (checkSaleOrderStatus(saleorder.getSaleorderId())){
            mv.addObject("message","订单状态已发生变化，请勿编辑订单");
            return fail(mv);
        }

        // 编辑保存销售单权限校验
        editSaveSalesOrderAuthValid(request, saleorder,"/orderstream/saleorder/saveeditsaleorderinfo");
        // 订单类型
        Integer orderType = saleorder.getOrderType();
        try {
            // 100%预付--其他付款计划：均设置默认值
            if (OrderConstant.PREPAY_100_PERCENT.equals(saleorder.getPaymentType())) {
                BigDecimal bd = new BigDecimal(0.00);
                saleorder.setAccountPeriodAmount(bd);// 账期支付金额
                saleorder.setPeriodDay(0);// 账期天数
                saleorder.setLogisticsCollection(0);// 物流代收0否 1是
                saleorder.setRetainageAmount(bd);// 尾款
                saleorder.setRetainageAmountMonth(0);// 尾款期限(月)
            }
            saleorder.setHaveAccountPeriod((saleorder.getAccountPeriodAmount() != null
                    && saleorder.getAccountPeriodAmount().doubleValue() != 0.00) ? 1 : 0);// 是否含有账期支付
            saleorder.setModTime(System.currentTimeMillis());
            saleorder.setUpdater(user.getUserId());
            saleorder.setUpdateDataTime(new Date());
            logger.info("保存编辑订单信息："+ JSON.toJSONString(saleorder));
            ResultInfo resultInfo = new ResultInfo();
            switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
                case ZXF:{
                    resultInfo = zxfEditSaleOrderBuzLogic.run(saleorder);
                    break;
                }
                case VS:{
                    resultInfo = vsEditSaleOrderBuzLogic.run(saleorder);
                    break;
                }
                case JCF:{
                    resultInfo = jcfEditSaleOrderBuzLogic.run(saleorder);
                    break;
                }
                case HC:{
                    resultInfo = hcEditSaleOrderBuzLogic.run(saleorder);
                    break;
                }
                case JCO:{
                    saleorder.setTakeTraderAreaId(0);
                    resultInfo = hcEditSaleOrderBuzLogic.run(saleorder);
                    break;
                }
                default:{
                    resultInfo = editSaleOrderBuzLogic.run(saleorder);
                    break;
                }
            }
            saleorder = saleorderMapper.getSaleOrderById(saleorder.getSaleorderId());
            orderAmountStrategy.execute(saleorder);
            if (orderType == 1) {
                Quoteorder quoteorder = new Quoteorder();
                quoteorder.setAccountPeriodAmount(saleorder.getAccountPeriodAmount());
                quoteorder.setPeriodDay(saleorder.getPeriodDay());
                quoteorder.setLogisticsCollection(saleorder.getLogisticsCollection());
                quoteorder.setRetainageAmount(saleorder.getRealAmount());
                quoteorder.setRetainageAmountMonth(saleorder.getRetainageAmountMonth());
                quoteorder.setInvoiceType(saleorder.getInvoiceType());
                quoteorder.setPaymentType(saleorder.getPaymentType());
                quoteorder.setQuoteorderId(saleorder.getQuoteorderId());
                quoteorder.setPrepaidAmount(saleorder.getPrepaidAmount());
                quoteService.updateQuote(quoteorder);
            }
            if (null != saleorder) {
                mv.addObject("url", "./detail.do?saleOrderId=" + saleorder.getSaleorderId());

                logger.info("进入终端更新逻辑：{}", com.alibaba.fastjson.JSONObject.toJSONString(orderTerminalDto));
                if( orderTerminalDto!= null && orderTerminalDto.getTerminalTraderNature() != null && orderTerminalDto.getTerminalTraderNature() >0){
                    // VDERP-15595 更新终端信息
                    orderTerminalDto.setBusinessId(saleorder.getSaleorderId());
                    orderTerminalDto.setBusinessNo(saleorder.getSaleorderNo());
                    OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
                    orderTerminalContext.setOrderTerminalStrategy(saleOrderTerminalStrategy);
                    orderTerminalContext.executeStrategy(orderTerminalDto);
                }

                return success(mv);
            } else {
                return fail(mv);
            }
        } catch (Exception e) {
            logger.error("saveeditsaleorderinfo:", e);
            return fail(mv);
        }
    }


    /**
     * [订单流]报价转订单 （权限校验，存在性校验等）
     * @param request
     * @param quoteOrderId
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/preQuoteToSaleOrder")
    public ResultInfo<?> preQuoteToSaleOrder(HttpServletRequest request, Integer quoteOrderId) {

        User currentLoginUser = getSessionUser(request);

        if (SysOptionConstant.ID_310.equals(currentLoginUser.getPositType())) {
            List<User> userListByorderId = authService.getUserListByorderId(quoteOrderId, authService.QUOTEORDER_TYPE);
            if (authService.existOrNot(currentLoginUser, userListByorderId)) {
                logger.info("销售越权操作:接口[orderstream/saleorder/preQuoteorderToSaleorder],行为[将非自己及下属的报价单转订单],操作人{}", currentLoginUser.getUsername());
            }
        }
        if (saleorderService.isExistQuoteorderId(quoteOrderId) > 0) {
            return new ResultInfo<>(-1, "该报价已经存在订单");
        }
        return saleorderService.preQuoteToSaleOrder(quoteOrderId);
    }


    /**
     * [订单流] 报价转订单
     * @param request
     * @param quoteOrderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/quoteToSaleOrder")
    @SystemControllerLog(operationType = "add", desc = "报价转订单")
    public ModelAndView quoteToSaleOrder(HttpServletRequest request,
                                         Integer quoteOrderId,
                                         ModelAndView mv ) {
        log.info("报价转订单quoteToSaleOrder:{}", quoteOrderId);

        try {
            Saleorder saleorder = saleorderService.quoteorderToSaleorder(quoteOrderId, getSessionUser(request));
            if (null == saleorder) {
                return fail(mv);
            }
            addTrackCreateSaleOrder(CurrentUser.getCurrentUser(),saleorder.getSaleorderNo(),saleorder.getTraderId(),"报价转");

            // 报价转订单设置默认值
            Saleorder updateDefaultSaleOrder = new Saleorder();
            updateDefaultSaleOrder.setSaleorderId(saleorder.getSaleorderId());
            // 收货信息默认
            updateDefaultSaleOrder.setIsPrintout(Integer.valueOf(4));
            updateDefaultSaleOrder.setFreightDescription(470);
            // 收票信息默认
            updateDefaultSaleOrder.setInvoiceType(972);
            // 全部调整为自动数电发票 VDERP-15896
            updateDefaultSaleOrder.setInvoiceMethod(ErpConst.FOUR);
            updateDefaultSaleOrder.setIsNew(ErpConst.ONE);

            saleorderService.updateByPrimaryKeySelective(updateDefaultSaleOrder);

            // VDERP-15595 报价转订单，同步关联终端信息
            Optional<OrderTerminalDto> quoteTerminalInfo = Optional.ofNullable(orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(saleorder.getQuoteorderId(), 2));
            if (quoteTerminalInfo.isPresent()) {
                OrderTerminalDto exist = quoteTerminalInfo.get();
                OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
                orderTerminalDto.setBusinessId(saleorder.getSaleorderId());
                orderTerminalDto.setBusinessNo(saleorder.getSaleorderNo());
                orderTerminalDto.setBusinessType(ErpConst.ZERO);
                orderTerminalDto.setDwhTerminalId(exist.getDwhTerminalId());
                orderTerminalDto.setUnifiedSocialCreditIdentifier(exist.getUnifiedSocialCreditIdentifier());
                orderTerminalDto.setCityId(exist.getCityId());
                orderTerminalDto.setAreaId(exist.getAreaId());
                orderTerminalDto.setProvinceName(exist.getProvinceName());
                orderTerminalDto.setCityName(exist.getCityName());
                orderTerminalDto.setAreaName(exist.getAreaName());
                orderTerminalDto.setTerminalName(exist.getTerminalName());
                orderTerminalDto.setOrganizationCode(exist.getOrganizationCode());
                orderTerminalDto.setProvinceId(exist.getProvinceId());
                orderTerminalDto.setTerminalTraderNature(exist.getTerminalTraderNature());//5604
                orderTerminalDto.setNatureTypeName(exist.getNatureTypeName());//非公基层
                OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
                orderTerminalContext.setOrderTerminalStrategy(saleOrderTerminalStrategy);
                orderTerminalContext.executeStrategy(orderTerminalDto);
            }
            return new ModelAndView("redirect:/orderstream/saleorder/edit.do?saleorderId=" + saleorder.getSaleorderId());
        } catch (Exception e) {
            logger.error("quoteToSaleOrder: ", e);
            return fail(mv);
        }
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/validCheckStatus")
    public ResultInfo validCheckStatus(HttpServletRequest request, Saleorder saleorder) {
        if (checkSaleOrderStatus(saleorder.getSaleorderId())){
            return ResultInfo.error("订单状态已发生变化，请勿编辑订单");
        }
        return ResultInfo.success();
    }


    @ResponseBody
    @RequestMapping(value = "/saveEditOrderBeforeOperateSku")
    public ResponseEntity saveEditOrderBeforeOperateSku(HttpServletRequest request, Saleorder saleorder) {

        logger.info("/orderstream/saleorder/saveEditOrderBeforeOperateSku -- RequestParam : {}",saleorder);

        // 编辑保存销售单权限校验
        editSaveSalesOrderAuthValid(request, saleorder,"/orderstream/saleorder/saveEditOrderBeforeOperateSku");

        // 更新销售订单信息
        saleorderService.saveEditOrderBeforeOperateSku(request,saleorder);
        return ResponseEntity.ok().build();
    }

    @ResponseBody
    @RequestMapping(value = "/saveTerminal")
    @NoNeedAccessAuthorization
    public ResponseEntity saveTerminal(HttpServletRequest request, OrderTerminalDto orderTerminalDto) {
        logger.info("终端信息更新：{}",JSON.toJSON(orderTerminalDto));
        // 更新销售订单信息
        OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
        orderTerminalContext.setOrderTerminalStrategy(saleOrderTerminalStrategy);
        orderTerminalContext.executeStrategy(orderTerminalDto);
        return ResponseEntity.ok().build();
    }

    /**
     * 编辑保存销售单权限校验
     * @param request
     * @param saleorder
     */
    protected void editSaveSalesOrderAuthValid(HttpServletRequest request, Saleorder saleorder,String currentVisitUrl) {

        User user = getSessionUser(request);
        if (SysOptionConstant.ID_310.equals(user.getPositType())) {
            List<User> userListByOrderId = authService.getUserListByorderId(saleorder.getSaleorderId(), authService.SALEORDER_TYPE);
            if (authService.existOrNot(user, userListByOrderId)) {
                logger.info("销售越权操作:接口[{}],行为[编辑非自己及下属的销售订单],操作人 {}",currentVisitUrl, user.getUsername());
            }
        }
    }

    /**
     * 获取终端客户信息
     * @param request
     * @param traderCustomerVo
     * @param modelAndView
     * @param pageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/getAsyncTerminalList")
    public ModelAndView getAsyncTerminalList(HttpServletRequest request,
                                             TraderCustomerVo traderCustomerVo,
                                             ModelAndView modelAndView,
                                             @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                             @RequestParam(required = false, defaultValue = "10") Integer pageSize) {

        User currentLoginUser = getSessionUser(request);

        // 终端信息
        traderCustomerVo.setCompanyId(currentLoginUser!=null?currentLoginUser.getCompanyId():Integer.valueOf(1));
        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> terminalList = traderCustomerService.getTerminalPageList(traderCustomerVo, page);
        modelAndView.addObject("terminalList",terminalList.get("terminalList"));
        modelAndView.addObject("page",terminalList.get("page"));

        modelAndView.setViewName("orderstream/saleorder/async_list_terminal");
        return modelAndView;
    }

    /**
     * 申请修改订单页面
     * @param request
     * @param saleorder
     * @param scene
     * @return
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "modifyApplyPageInit")
    public ModelAndView modifyApplyPageInit(HttpServletRequest request, Saleorder saleorder,String scene) {
        User curr_user = getSessionUser(request);
        Integer saleorderId = saleorder.getSaleorderId();
        // 获取订单基本信息
        saleorder.setOptType("modifyApplyInit");
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
        ModelAndView mv = new ModelAndView();
        saleorder = saleOrderService.getBaseSaleorderInfo(saleorder,mv);
        mv.addObject("scene", NumberUtils.toInt(scene,0));
        // 发货类型
        List<SysOptionDefinition> deliveryMethods = getSysOptionDefinitionList(1619);
        mv.addObject("deliveryMethods", deliveryMethods);
        // 发货方式
        List<SysOptionDefinition> deliveryTypes = getSysOptionDefinitionList(480);
        mv.addObject("deliveryTypes", deliveryTypes);

        if (null == saleorder) {
            mv.addObject("message", "无法操作，该订单申请修改查询失败");
            return fail(mv);
        }
        // add by franlin.wu for[耗材商城订单，避免空指针] at 2018-11-29 end
        if (saleorder.getLockedStatus() == 1) {
            mv.addObject("message", "无法操作，该订单已被锁定");
            return fail(mv);
        }
        // add by franlin.wu for[耗材商城订单，避免空指针以及非耗材订单的业务逻辑] at 2018-11-29 begin

        //发票是否寄送
        // 获取发票信息
        List<Invoice> saleInvoiceList = invoiceService.getInvoiceInfoByRelatedId(saleorder.getSaleorderId(),
                SysOptionConstant.ID_505);
        boolean invoiceAllSend = false;
        if(CollectionUtils.isNotEmpty(saleInvoiceList)) {
            List<Integer> invoiceIdList = saleInvoiceList.stream().map(Invoice::getInvoiceId).collect(Collectors.toList());
            //全部开票
            if (ErpConst.TWO.equals(saleorder.getInvoiceStatus())) {
                List<Invoice> invoices = invoiceService.getInvoiceExpressInfo(invoiceIdList);
                if (CollectionUtils.isNotEmpty(invoices)){
                    //寄送的发票的数量
                    int sendInvoiceNum = invoices.stream().filter(item -> item.getExpressId() != null).mapToInt(Invoice::getInvoiceNum).sum();
                    //是否全部寄送
                    if (sendInvoiceNum == saleInvoiceList.size()) {
                        invoiceAllSend = true;
                    }
                }
            }
        }
        mv.addObject("invoiceAllSend", invoiceAllSend);

        //订单主状态映射
        initSaleOrderStatus(saleorder);
        if ((ErpConst.FIVE.equals(saleorder.getOrderStreamStatus()) && ErpConst.TWO.equals(saleorder.getInvoiceStatus())) && invoiceAllSend){
            mv.addObject("message", "此状态下订单无法申请修改");
            return fail(mv);
        }

        // 订单类型
        Integer ordreType = saleorder.getOrderType();
        //------------------分批开票--------------------------------
        //若订单状态为已完成，但开票状态为“未开票”或“部分开票”，仍然显示“申请修改”的按钮，只有收票信息模块可修改，其他内容不可修改。
        Integer status = saleorder.getStatus();
        Integer invoiceStatus = saleorder.getInvoiceStatus();
        if(status.equals(2)&&invoiceStatus!=2){
            mv.addObject("invoiceModifyflag",1);
        }
        //------------------分批开票--------------------------------


        TraderCustomerVo customer = traderCustomerService.getCustomerInfo(saleorder.getTraderId());
        //收货客户
        TraderCustomerVo takeCustomer = getCustomerInfo(customer,saleorder.getTakeTraderId());
        mv.addObject("takeCustomer", takeCustomer);

        //收票客户
        TraderCustomerVo invoiceCustomer = getCustomerInfo(customer,saleorder.getInvoiceTraderId());
        mv.addObject("invoiceCustomer", invoiceCustomer);

        //设置收货、收票信息 联系人&联系地址
        getContactAndAddress(saleorder,mv);
//        }

        // add by Tomcat.Hui 2019/12/2 19:22 .Desc: VDERP-1325 分批开票 打印报错信息. start
        try{
            // 如果需要拦截开票的
            if (saleorder.getIsHaveInvoiceApply() != null && saleorder.getIsHaveInvoiceApply().equals(1)
                    && saleorderId != null) {
                List<InvoiceApply> invoiceApplyList = invoiceService.getSaleInvoiceApplyList(saleorderId, 505, -1);
                if (invoiceApplyList != null) {
                    for (InvoiceApply ia : invoiceApplyList) {
                        // 非不通过的
                        // add by Tomcat.Hui 2019/12/3 19:28 .Desc: VDERP-1325 分批开票 去掉运营审核状态校验.
                        if (!ia.getValidStatus().equals(2) && !ia.getAdvanceValidStatus().equals(2)) {
                            mv.addObject("invoiceApply", ia);
                            break;
                        }
                    }
                }
            }
        }catch (Exception e){
            logger.error(Contant.ERROR_MSG, e);
            logger.error("添加开票信息出错",e);
        }
        // add by Tomcat.Hui 2019/12/2 19:22 .Desc: VDERP-1325 分批开票 打印报错信息. end


        //获取当前订单开票审核状态 1 存在审核中 0 不存在
        Integer invoiceApplyStatus = invoiceService.getInvoiceApplyNowAuditStatus(saleorderId);
        Integer invoiceapplyFlag = 0;
        if(invoiceApplyStatus.equals(1)|| invoiceStatus!=0){
            invoiceapplyFlag = 1;
        }
        mv.addObject("invoiceapplyFlag",invoiceapplyFlag);
        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);

        // 获取订单产品信息
        Saleorder sale = new Saleorder();
        sale.setSaleorderId(saleorder.getSaleorderId());
        List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);


        // 获取订单内部备注组件
        if (CollectionUtils.isNotEmpty(saleorderGoodsList)) {
            for (SaleorderGoods saleorderGoods:saleorderGoodsList) {
                List<String> skuNoList = new ArrayList<>();
                skuNoList.add(saleorderGoods.getSku());
                LabelQuery labelQuery = new LabelQuery();
                // 根据当前场景获取
                labelQuery.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
                labelQuery.setRelationId(saleorderGoods.getSaleorderId());
                labelQuery.setSkuNoList(skuNoList);
                String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
                saleorderGoods.setComponentHtml(componentHtml);
            }
        }
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
        List<Integer> skuIds = new ArrayList<>();
        saleorderGoodsList.stream().forEach(saleGood -> {
            skuIds.add(saleGood.getGoodsId());
        });
        List<Map<String,Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
        Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
        mv.addObject("newSkuInfosMap", newSkuInfosMap);
        //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

        // 获取物流公司列表
        List<Logistics> logisticsList = logisticsService.getWmsLogisticsList(curr_user.getCompanyId(),ErpConst.ONE);
//        if (CollectionUtils.isNotEmpty(logisticsList)){
//            //物流公司中英文排序
//            logisticsList.sort(Comparator.comparing(o -> convertToPinYinString(o.getName())));
//        }
        mv.addObject("logisticsList", logisticsList);

        // 运费说明
        List<SysOptionDefinition> freightDescriptions = getSysOptionDefinitionList(469);
        mv.addObject("freightDescriptions", freightDescriptions);

        saleorder.setPhtxFlag(orderPeerApiService.getOrderIsGoodsPeer(saleorderId));



        //是否有对应VP单
       // saleorderService.getSaleorderGoodsById(sale);
        if (CollectionUtils.isNotEmpty(saleorderGoodsList)){
            List<SaleorderGoods> saleorderGoods = saleorderGoodsList.stream().filter(item -> item.getBuyNum() != null && item.getBuyNum() > 0).collect(Collectors.toList());
            mv.addObject("saleorderGoods", saleorderGoods);
        }

        //查询销售是否为科研购及其子部门
        mv.addObject("isScientificDep",orgService.getKYGOrgFlagByTraderId(saleorder));
        mv.addObject("isYxgOrgFlag",orgService.isBelongByOrgNameAndTraderId(saleorder,ErpConst.YXG_ORG_NAME));

        mv.addObject("saleorderGoodsList", saleorderGoodsList);
        mv.addObject("saleorder", saleorder);
        mv.addObject("user", curr_user);
        Long paymentTime = saleorder.getPaymentTime();
        LocalDate datePickerMinDate = LocalDate.now().plusDays(1);
        LocalDate datePickerMaxDate;
        boolean deliveryDelayTimeFlag = false;
        if (Objects.nonNull(paymentTime) && paymentTime > 0) {
            datePickerMaxDate = Instant.ofEpochMilli(paymentTime).atZone(ZoneId.systemDefault()).toLocalDate().plusDays(91);
        } else {
            datePickerMaxDate = LocalDate.now().plusDays(91);
        }
        if (!datePickerMinDate.isBefore(datePickerMaxDate)) {
            deliveryDelayTimeFlag = true;
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        mv.addObject("datePickerMinDate", datePickerMinDate.format(formatter));
        mv.addObject("datePickerMaxDate", datePickerMaxDate.format(formatter));
        mv.addObject("deliveryDelayTimeFlag", deliveryDelayTimeFlag);
        mv.setViewName("orderstream/saleorder/modify_apply_page");
        return mv;
    }

    public void getContactAndAddress(Saleorder saleorder, ModelAndView mv){
        // 收货信息 联系人&联系地址
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case HC:{
                getTakeTraderInfo(mv,saleorder);
                // 收票信息 联系人&联系地址
                getInvoiceTraderInfo(mv,saleorder);
                //获取省市区
                getFirstProvince(mv);
                getProvinces(mv,saleorder);
                break;
            }
            default:{
                if (saleorder.getTakeTraderId() != null && saleorder.getTakeTraderId().intValue() > 0) {
                    getTakeTraderInfo(mv,saleorder);
                }
                // 收票信息 联系人&联系地址
                if (saleorder.getInvoiceTraderId() != null && saleorder.getInvoiceTraderId().intValue() > 0) {
                    getInvoiceTraderInfo(mv,saleorder);
                }
                break;
            }
        }
    }

    /**
     * <AUTHOR>
     * @desc 查询收货客户信息
     * @param mv
     * @param saleorder
     */
    private void getTakeTraderInfo(ModelAndView mv,Saleorder saleorder){
        TraderContactVo takeTraderContactVo = new TraderContactVo();
        TraderCustomerVo takeTraderCustomerInfo = new TraderCustomerVo();
        if(ErpConst.FIVE.equals(saleorder.getOrderType())){
            //耗材订单且收货客户id为空
            takeTraderContactVo.setTraderId(saleorder.getTraderId());
            takeTraderCustomerInfo = traderCustomerService.getCustomerBussinessInfo(saleorder.getTraderId());
        }else {
            takeTraderContactVo.setTraderId(saleorder.getTakeTraderId());
            takeTraderCustomerInfo = traderCustomerService.getCustomerBussinessInfo(saleorder.getTakeTraderId());
        }
        takeTraderContactVo.setTraderType(ErpConst.ONE);
        Map<String, Object> takeMap = traderCustomerService.getTraderContactVoList(takeTraderContactVo);
        String takeTastr = (String) takeMap.get("contact");
        net.sf.json.JSONArray takeJson = net.sf.json.JSONArray.fromObject(takeTastr);
        List<TraderContactVo> takeTraderContactList = (List<TraderContactVo>) takeJson.toCollection(takeJson,TraderContactVo.class);
        List<TraderAddressVo> takeTraderAddressList = (List<TraderAddressVo>) takeMap.get("address");
        if(takeTraderContactList==null){
            takeTraderContactList= Lists.newArrayList();
        }
        if (takeTraderAddressList==null){
            takeTraderAddressList=Lists.newArrayList();
        }
        //如果根据收货公司ID获取收货联系人为空 则默认取订单的收货联系人
        if(takeTraderContactList.isEmpty()){
            TraderContactVo traderContactVo = new TraderContactVo();
            //收货联系人id
            traderContactVo.setTraderContactId(saleorder.getTakeTraderContactId());
            //收货联系人名称
            traderContactVo.setName(saleorder.getTakeTraderContactName());
            //收货联系人电话
            traderContactVo.setTelephone(saleorder.getTakeTraderContactTelephone());
            //收货联系人手机
            traderContactVo.setMobile(saleorder.getTakeTraderContactMobile());
            takeTraderContactList.add(traderContactVo);
        }
        //如果根据收货公司ID获取收货地址为空 则默认取订单的收货地址
        if(takeTraderAddressList.isEmpty()){
            TraderAddress traderAddress = new TraderAddress();
            traderAddress.setTraderAddressId(saleorder.getTakeTraderAddressId());
            //默认订单的收货地址
            traderAddress.setAddress(saleorder.getTakeTraderAddress());
            TraderAddressVo traderAddressVo = new TraderAddressVo();
            //默认订单的收货地区
            traderAddressVo.setArea(saleorder.getTakeTraderArea());
            traderAddressVo.setTraderAddress(traderAddress);
            takeTraderAddressList.add(traderAddressVo);
        }

        mv.addObject("takeTraderContactList", takeTraderContactList);
        mv.addObject("takeTraderAddressList", takeTraderAddressList);
        mv.addObject("takeTraderCustomerInfo", takeTraderCustomerInfo);
    }

    /**
     * <AUTHOR>
     * @desc 查询收票客户信息
     * @param mv
     * @param saleorder
     */
    public void getInvoiceTraderInfo(ModelAndView mv,Saleorder saleorder){
        TraderContactVo invoiceTraderContactVo = new TraderContactVo();
        TraderCustomerVo invoiceTraderCustomerInfo = new TraderCustomerVo();
        if(ErpConst.FIVE.equals(saleorder.getOrderType())){
            //耗材订单且收货客户id为空
            invoiceTraderContactVo.setTraderId(saleorder.getTraderId());
            invoiceTraderCustomerInfo = traderCustomerService.getCustomerBussinessInfo(saleorder.getTraderId());
        }else {
            invoiceTraderContactVo.setTraderId(saleorder.getInvoiceTraderId());
            invoiceTraderCustomerInfo = traderCustomerService.getCustomerBussinessInfo(saleorder.getInvoiceTraderId());
        }
        invoiceTraderContactVo.setTraderType(ErpConst.ONE);
        Map<String, Object> invoiceMap = traderCustomerService.getTraderContactVoList(invoiceTraderContactVo);
        String invoiceTastr = (String) invoiceMap.get("contact");
        net.sf.json.JSONArray invoiceJson = net.sf.json.JSONArray.fromObject(invoiceTastr);
        List<TraderContactVo> invoiceTraderContactList = (List<TraderContactVo>) invoiceJson.toCollection(invoiceJson, TraderContactVo.class);
        List<TraderAddressVo> invoiceTraderAddressList = (List<TraderAddressVo>) invoiceMap.get("address");
        if(invoiceTraderContactList==null){
            invoiceTraderContactList=Lists.newArrayList();
        }
        if(invoiceTraderAddressList==null){
            invoiceTraderAddressList=Lists.newArrayList();
        }
        //如果根据收票公司ID获取收票联系人为空 则默认取订单的收票联系人
        if(invoiceTraderContactList.isEmpty()){
            TraderContactVo traderContactVo = new TraderContactVo();
            //收票联系人id
            traderContactVo.setTraderContactId(saleorder.getInvoiceTraderContactId());
            //收票联系人名称
            traderContactVo.setName(saleorder.getInvoiceTraderContactName());
            //收票联系人电话
            traderContactVo.setTelephone(saleorder.getInvoiceTraderContactTelephone());
            //收票联系人手机
            traderContactVo.setMobile(saleorder.getInvoiceTraderContactMobile());
            invoiceTraderContactList.add(traderContactVo);
        }
        //如果根据收票公司ID获取收票地址为空 则默认取订单的收票地址
        if(invoiceTraderAddressList.isEmpty()){
            TraderAddress traderAddress = new TraderAddress();
            //默认订单的收货地址ID
            traderAddress.setTraderAddressId(saleorder.getInvoiceTraderAddressId());
            //默认订单的收票地址
            traderAddress.setAddress(saleorder.getInvoiceTraderAddress());
            TraderAddressVo traderAddressVo = new TraderAddressVo();
            //默认订单的收票地区
            traderAddressVo.setArea(saleorder.getInvoiceTraderArea());
            traderAddressVo.setTraderAddress(traderAddress);
            invoiceTraderAddressList.add(traderAddressVo);
        }
        mv.addObject("invoiceTraderContactList", invoiceTraderContactList);
        mv.addObject("invoiceTraderAddressList", invoiceTraderAddressList);
        mv.addObject("invoiceTraderCustomerInfo", invoiceTraderCustomerInfo);
    }
    /**
     * 销售订单申请修改提交审核
     * @param request
     * @param session
     * @param saleorderModifyApply
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/modifyApplySave")
    @SystemControllerLog(operationType = "add", desc = "销售订单申请修改提交审核")
    public ModelAndView modifyApplySave(HttpServletRequest request, HttpSession session,
                                        SaleorderModifyApply saleorderModifyApply) {
        ModelAndView mv = new ModelAndView();
        User userLogin = getSessionUser(request);
        //打印销售订单id信息日志，监控问题:修改订单后T_SALEORDER_MODIFY_APPLY表中关联的订单id记录为0 情况
        logger.info("销售订单申请修改提交审核，SaleorderId {}",JsonUtils.convertObjectToJsonStr(saleorderModifyApply.getSaleorderId()));
        try {
            saleorderModifyApply = saleorderService.convertModifyApply(saleorderModifyApply,request,session);

            //销售单商品已存在关联的未关闭采购单，是不能修改发货方式的
            List<SaleorderGoods> saleorderGoodsListApply = saleorderModifyApply.getGoodsList().stream()
                    .map(item -> {
                        SaleorderGoods goods = new SaleorderGoods();
                        goods.setSaleorderGoodsId(item.getSaleorderGoodsId());
                        goods.setDeliveryDirect(item.getDeliveryDirect());
                        return goods;
                    })
                    .collect(Collectors.toList());
            saleorderService.saleorderGoodsDeliveryDirectModifyCheck(saleorderGoodsListApply);

            isSaleorderCanModify(saleorderModifyApply,userLogin);
            saleorderModifyApply = saleorderService.modifyApplySave(saleorderModifyApply, request, session);
            String cancelStatus = (String) request.getAttribute("cancel");
            if (null != saleorderModifyApply) {
                // 生成流程
                return generateProcess(saleorderModifyApply,request,mv);
            } else {
                if(cancelStatus != null && cancelStatus.equals("fail")){
                    mv.addObject("message","WMS已复核出库完成，订单已发货，无法修改！请申请售后处理。 ");
                }
                if(request.getAttribute("forceModify") != null && request.getAttribute("forceModify").equals(ErpConst.ONE)){
                    mv.addObject("message","存在商品已发货，无法修改发货方式。");
                }
                return fail(mv);
            }
        }catch (ShowErrorMsgException e){
            logger.error("modifyApplySave 2:", e);
            mv.addObject("message", "保存操作失败：" + e.getMessage());
            return fail(mv);
        }
        catch (Exception e) {
            logger.error("modifyApplySave 2:", e);
            mv.addObject("message", "保存操作失败：" + e.getMessage());
            return fail(mv);
        }
    }

    private void isSaleorderCanModify(SaleorderModifyApply saleorderModifyApply, User user) throws Exception{
        if(null != user.getPositType() && SysOptionConstant.ID_310.equals(user.getPositType())) {
            List<User> userListByorderId = authService.getUserListByorderId(saleorderModifyApply.getSaleorderId(), authService.SALEORDER_TYPE);
            if (authService.existOrNot(user, userListByorderId)) {
                logger.info("销售越权操作:接口[order/saleorder/modifyApplySave],行为[将非自己及下属的生效订单提交开票申请],操作人{}",user.getUsername());
            }
        }
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderModifyApply.getSaleorderId());
        List<SaleorderGoods> saleorderGoods = saleorderService.getSaleorderGoodsById(saleorder);
        saleorder = saleorderService.getBaseSaleorderInfo(saleorder);
        boolean isChangeAdrress=false;
        if (saleorder.getTakeTraderAddressId() != null
                && !saleorder.getTakeTraderAddressId().equals(saleorderModifyApply.getTakeTraderAddressId())
                && saleorder.getDeliveryDirect() == 1) {
            // 如果是直发订单修改了收货地址，需要产品部审核
            isChangeAdrress=true;
            for(SaleorderGoods g:saleorderGoods){
                ProductManageAndAsistDto productManageAndAsistDto=goodsService.queryProductManageAndAsist(g.getSku());
                if(Objects.isNull(productManageAndAsistDto) || (Objects.isNull(productManageAndAsistDto.getProductAssitName()) && Objects.isNull(productManageAndAsistDto.getProductManageName()))){
                    saleorderModifyApply.setGoodsList(null);
                    throw new Exception("修改直发订单的收货地址，商品"+g.getGoodsName()+"，不存在归属产品经理");
                }
            }
        }
        if(!isChangeAdrress) {
            for (SaleorderGoods sg : saleorderGoods) {
                List<SaleorderModifyApplyGoods> goodsList2 = saleorderModifyApply.getGoodsList();
                for (SaleorderModifyApplyGoods smag : goodsList2) {
                    if (sg.getSaleorderGoodsId().equals(smag.getSaleorderGoodsId())) {
                        // 同一个订单产品比较直发状态是否相同
                        if (!sg.getDeliveryDirect().equals(smag.getDeliveryDirect())) {
                            ProductManageAndAsistDto productManageAndAsistDto = goodsService.queryProductManageAndAsist(sg.getSku());
                            if (Objects.isNull(productManageAndAsistDto) || (Objects.isNull(productManageAndAsistDto.getProductAssitName()) && Objects.isNull(productManageAndAsistDto.getProductManageName()))) {
                                saleorderModifyApply.setGoodsList(null);
                                throw new Exception("修改直发状态的商品" + sg.getGoodsName() + "，不存在归属产品经理");
                            }
                        }
                    }
                }
            }
        }
        saleorderModifyApply.setGoodsList(null);
    }

    /**
     * 生成审核流程
     * @param saleorderModifyApply
     * @param request
     * @param mv
     * @return
     */
    public ModelAndView generateProcess(SaleorderModifyApply saleorderModifyApply,HttpServletRequest request,ModelAndView mv){
        // 生成流程
        try {
            List<Integer> changeIsDirectGoodsIds=new ArrayList<>();
            boolean isChangeTakeAdress=false;
            Map<String, Object> variableMap = new HashMap<String, Object>();
            User user = userService.getUserById(saleorderModifyApply.getCreator());
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleorderModifyApply.getSaleorderId());
            List<SaleorderGoods> saleorderGoods = saleorderService.getSaleorderGoodsById(saleorder);
            saleorder = saleorderService.getBaseSaleorderInfo(saleorder);
            saleorderModifyApply.setIsChangeTakeTraderAddressId(0);
            saleorderModifyApply.setIsChangeDeliveryDirect(0);
            saleorderModifyApply.setIsChangeLogisticsComments(0);
            if ( Objects.nonNull(saleorder.getTakeTraderAddressId()) && Objects.nonNull(saleorderModifyApply.getTakeTraderAddressId())
                    && !saleorder.getTakeTraderAddressId().equals(saleorderModifyApply.getTakeTraderAddressId())
                    && saleorder.getDeliveryDirect() == 1) {
                // 如果是直发订单修改了收货地址，需要产品部审核
                isChangeTakeAdress=true;
                for(SaleorderGoods g:saleorderGoods){
                    if(g!=null&&g.getGoodsId()!=null){
                        changeIsDirectGoodsIds.add(g.getGoodsId());
                    }
                }
                saleorderModifyApply.setIsChangeDeliveryDirect(1);
            }
            // 非耗材订单进行比较（耗材订单未传TakeTraderAddressId）
            if (!saleorder.getOrderType().equals(5)) {
                if (saleorder.getTakeTraderAddressId() != null && !saleorder.getTakeTraderAddressId()
                        .equals(saleorderModifyApply.getTakeTraderAddressId())) {
                    // 是否修改收货地址 已修改
                    saleorderModifyApply.setIsChangeTakeTraderAddressId(1);
                }
            }
            if (saleorder.getLogisticsComments() != null
                    && !saleorder.getLogisticsComments().equals(saleorderModifyApply.getLogisticsComments())) {
                // 是否修改了物流备注
                saleorderModifyApply.setIsChangeLogisticsComments(1);
            }
            for (SaleorderGoods sg : saleorderGoods) {
                List<SaleorderModifyApplyGoods> goodsList2 = saleorderModifyApply.getGoodsList();
                for (SaleorderModifyApplyGoods smag : goodsList2) {
                    if (sg.getSaleorderGoodsId().equals(smag.getSaleorderGoodsId())) {
                        // 同一个订单产品比较直发状态是否相同
                        if (!sg.getDeliveryDirect().equals(smag.getDeliveryDirect())) {
                            // 是否修改直发普发状态 已修改
                            saleorderModifyApply.setIsChangeDeliveryDirect(1);
                            if(!isChangeTakeAdress) {
                                changeIsDirectGoodsIds.add(sg.getGoodsId());
                            }
                        }
                    }
                }
            }
            //原来的 processKey = editSaleorderVerify
            String processKey = "saleorderModifyAudit";
            // 开始生成流程(如果没有taskId表示新流程需要生成)
            variableMap.put("saleorderModifyApplyInfo", saleorderModifyApply);
            variableMap.put("saleorderModifyApplyId", saleorderModifyApply.getSaleorderModifyApplyId());
            variableMap.put("orderId", saleorderModifyApply.getSaleorderId());
            variableMap.put("isNew", 1);
            variableMap.put("currentAssinee", user.getUsername());
            variableMap.put("processDefinitionKey", processKey);
            variableMap.put("businessKey",
                    processKey + "_" + saleorderModifyApply.getSaleorderModifyApplyId());
            variableMap.put("relateTableKey", saleorderModifyApply.getSaleorderModifyApplyId());
            variableMap.put("relateTable", "T_SALEORDER_MODIFY_APPLY");
            // 设置审核完成监听器回写参数
            variableMap.put("tableName", "T_SALEORDER_MODIFY_APPLY");
            variableMap.put("id", "SALEORDER_MODIFY_APPLY_ID");
            variableMap.put("idValue", saleorderModifyApply.getSaleorderModifyApplyId());
            variableMap.put("idValue1", saleorderModifyApply.getSaleorderId());
            variableMap.put("key", "VALID_STATUS");
            variableMap.put("value", 1);
            // 回写数据的表在db中
            variableMap.put("db", 1);
            actionProcdefService.createProcessInstance(request, processKey,
                    processKey + "_" + saleorderModifyApply.getSaleorderModifyApplyId(), variableMap);
            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    processKey + "_" + saleorderModifyApply.getSaleorderModifyApplyId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Saleorder saleorderLocked = new Saleorder();
                saleorderLocked.setLockedReason("订单修改审核");
                saleorderLocked.setSaleorderId(saleorderModifyApply.getSaleorderId());
                saleorderLocked.setIsPrintout(saleorderModifyApply.getIsPrintout());
                saleorderService.saveEditSaleorderInfo(saleorderLocked, request, request.getSession());
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                String taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 默认审批通过
                variables.put("pass", true);
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        user.getUsername(), variables);
                if(saleorderModifyApply.getIsChangeDeliveryDirect().equals(1)
                        &&CollectionUtils.isNotEmpty(changeIsDirectGoodsIds)){
                    List<User> users=goodsService.getAssignManageUserByGoods(changeIsDirectGoodsIds);
                    List<User> assignUser = goodsService.getAssignUserByGoodsId(changeIsDirectGoodsIds);
                    // 将产品经理和产品助理合并并去重
                    users = Stream.concat(users.stream(), assignUser.stream()).
                            collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(User::getUserId))), ArrayList::new));

                    if(CollectionUtils.isNotEmpty(users)){
                        List<String> userNames=new ArrayList<>();
                        List<Integer> userIds=new ArrayList<>();
                        for(User u:users){
                            if(u==null||u.getUserId()==null||StringUtil.isBlank(u.getUsername())){
                                continue;
                            }
                            userNames.add(u.getUsername());
                            userIds.add(u.getUserId());
                        }
                        Map<String, Object> historicInfo1 = actionProcdefService.getHistoric(processEngine,
                                processKey + "_" + saleorderModifyApply.getSaleorderModifyApplyId());
                        Task taskInfo1 = (Task) historicInfo1.get("taskInfo");
                        String taskId1 = taskInfo1.getId();
                        actionProcdefService.addCandidate(taskId1,userNames);
                        Integer messageTemplateId = 64;
                        Map<String, String> map = new HashMap<>();
                        map.put("saleorderNo", saleorderModifyApply.getSaleorderModifyApplyNo());
                        String url = "./orderstream/saleorder/viewModifyApply.do?saleorderModifyApplyId="
                                + saleorderModifyApply.getSaleorderModifyApplyId() + "&saleorderId="
                                + saleorderModifyApply.getSaleorderId();
                        //MessageUtil.sendMessage(messageTemplateId,userIds,map,url);
                        if(CollectionUtils.isNotEmpty(userIds)) {
                            MessageUtil.sendMessage(messageTemplateId, userIds, map, url);
                        }
                    }
                }
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);

                }

            }
        } catch (Exception e) {
            logger.error("modifyApplySave 1:", e);
            mv.addObject("message", "任务完成操作失败：" + e.getMessage());
            return fail(mv);
        }
        mv.addObject("url", "./viewModifyApply.do?saleorderId=" + saleorderModifyApply.getSaleorderId()
                + "&saleorderModifyApplyId=" + saleorderModifyApply.getSaleorderModifyApplyId());
        return success(mv);
    }

    public void initSaleOrderStatus(Saleorder saleorder){
        //开票申请
        List<InvoiceApply> saleInvoiceApplyList = invoiceService.getSaleInvoiceApplyList(saleorder.getSaleorderId(),
                SysOptionConstant.ID_505, -1);
        if (ErpConst.ZERO.equals(saleorder.getStatus()) && ErpConst.THREE.equals(saleorder.getVerifyStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.TO_BE_CONFIRMED.getStatus());
        }
        if (ErpConst.ZERO.equals(saleorder.getStatus())
                && ErpConst.ZERO.equals(saleorder.getVerifyStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.TO_BE_AUDITED.getStatus());
        }
        if (ErpConst.ONE.equals(saleorder.getVerifyStatus())
                && ErpConst.ONE.equals(saleorder.getValidStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.TO_BE_PAYMENT.getStatus());
        }
        if (ErpConst.ONE.equals(saleorder.getValidStatus())
                && ErpConst.TWO.equals(saleorder.getPaymentStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.TO_BE_DELIVERED.getStatus());
        }
        if ((ErpConst.ONE.equals(saleorder.getDeliveryStatus())
                || ErpConst.TWO.equals(saleorder.getDeliveryStatus()))){
            saleorder.setOrderStreamStatus(OrderStatusEnum.TO_BE_RECEIVED.getStatus());
        }
        if (ErpConst.TWO.equals(saleorder.getArrivalStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.TO_BE_INVOICED.getStatus());
        }
        if (ErpConst.TWO.equals(saleorder.getStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.FINISHED.getStatus());
        }
        if (ErpConst.THREE.equals(saleorder.getStatus())){
            saleorder.setOrderStreamStatus(OrderStatusEnum.CLOSE.getStatus());
        }
    }

    private List<StatusNode> initStatus(Saleorder saleorder){
        List<StatusNode> statusList = new ArrayList<>();
        OrderStatusEnum[] orderStatusEnums = OrderStatusEnum.values();
        for (OrderStatusEnum orderStatusEnum : orderStatusEnums) {
            int status = orderStatusEnum.getStatus();
            String desc = orderStatusEnum.getDesc();
            StatusNode statusNode = new StatusNode(desc, 0);
            statusList.add(statusNode);
        }
        if (ErpConst.ZERO.equals(saleorder.getStatus())){
            statusList.get(0).setStatus(2);
            statusList.remove(7);
        }else if (ErpConst.ZERO.equals(saleorder.getStatus()) &&
                (ErpConst.ZERO.equals(saleorder.getVerifyStatus()) ||ErpConst.ONE.equals(saleorder.getVerifyStatus()))){
            statusList.get(1).setStatus(2);
            statusList.remove(7);
        }
        return statusList;
    }


    /**
     * <b>Description:</b><br>
     * 查看订单修改详情
     *
     * @param request
     * @param saleorderModifyApply
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2018年1月17日 下午5:01:30
     */
    @ResponseBody
    @RequestMapping(value = "viewModifyApply")
    public ModelAndView viewModifyApply(HttpServletRequest request, SaleorderModifyApply saleorderModifyApply) {
        User curr_user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        ModelAndView mv = new ModelAndView();
        mv.setViewName("orderstream/saleorder/modify_apply_detail");

        mv.addObject("curr_user", curr_user);
        Integer saleorderId = saleorderModifyApply.getSaleorderId();
        // 获取订单修改信息
        SaleorderModifyApply saleorderModifyApplyInfo = saleorderService
                .getSaleorderModifyApplyInfo(saleorderModifyApply);
        mv.addObject("saleorderModifyApplyInfo", saleorderModifyApplyInfo);

        String statusStr = setStatusStr(saleorderModifyApplyInfo);
        mv.addObject("statusStr",statusStr);

        // 发票类型
        List<SysOptionDefinition> invoiceTypes = getSysOptionDefinitionList(428);
        mv.addObject("invoiceTypes", invoiceTypes);
        List<SysOptionDefinition> deliveryTypes = getSysOptionDefinitionList(480);
        mv.addObject("deliveryTypes", deliveryTypes);
        List<SysOptionDefinition> freightDescriptionTypes = getSysOptionDefinitionList(469);
        mv.addObject("freightDescriptionTypes", freightDescriptionTypes);

        List<Logistics> allLogisticsList = logisticsService.getAllLogisticsList(1);
        mv.addObject("allLogisticsList", allLogisticsList);


        // 获取原订单基本信息
        Saleorder saleorder = saleorderService.getSaleOrderById(saleorderId);

        TraderCustomer takeCustomerInfo = traderCustomerService.getTraderCustomerId(saleorder.getTakeTraderId());
        mv.addObject("takeCustomerInfo", takeCustomerInfo);
        TraderCustomer invoiceCustomerInfo = traderCustomerService.getTraderCustomerId(saleorder.getInvoiceTraderId());
        mv.addObject("invoiceCustomerInfo", invoiceCustomerInfo);
        try {
            saleorder.setCompanyId(curr_user.getCompanyId());

            // 获取原订单产品信息
            Saleorder sale = new Saleorder();
            sale.setSaleorderId(saleorder.getSaleorderId());
            List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleorderGoodsById(sale);
            for (SaleorderGoods good:saleorderGoodsList) {
                List<String> skuNoList = new ArrayList<>();
                skuNoList.add(good.getSku());
                LabelQuery labelQuery = new LabelQuery();
                // 根据当前场景获取
                labelQuery.setScene(RemarkComponentSceneEnum.SALE_ORDER.getCode());
                labelQuery.setRelationId(good.getSaleorderId());
                labelQuery.setSkuNoList(skuNoList);
                String componentHtml = remarkComponentService.getComponentHtml(labelQuery);
                good.setComponentHtml(componentHtml);
            }
            mv.addObject("saleorderGoodsList", saleorderGoodsList);


            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
            List<Integer> skuIds = new ArrayList<>();
            saleorderGoodsList.stream().forEach(saleGood -> {
                skuIds.add(saleGood.getGoodsId());
            });
            List<Map<String,Object>> skuTipsMap = this.vGoodsService.skuTipList(skuIds);
            Map<String,Map<String,Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key->key.get("SKU_NO").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 end

            // 获取订单修改产品信息
            List<SaleorderModifyApplyGoods> saleorderModifyApplyGoodsList = saleorderService
                    .getSaleorderModifyApplyGoodsById(saleorderModifyApply);
            mv.addObject("saleorderModifyApplyGoodsList", saleorderModifyApplyGoodsList);

        } catch (Exception e) {
            logger.error("viewModifyApply:", e);
        }

        mv.addObject("saleorder", saleorder);
        //供应链生成
        if(saleorderModifyApplyInfo.getModifyType() > 0){
            mv.setViewName("orderstream/saleorder/modify_apply_simplie_detail");
            return mv;
        }

        //查出历史审核信息
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                "editSaleorderVerify_" + saleorderModifyApply.getSaleorderModifyApplyId());

        if(CollectionUtils.isEmpty((List)historicInfo.get("historicActivityInstance"))){
            // 订单修改审核信息
            historicInfo = actionProcdefService.getHistoric(processEngine,
                    "saleorderModifyAudit_" + saleorderModifyApply.getSaleorderModifyApplyId());
        }

        mv.addObject("taskInfo", historicInfo.get("taskInfo"));
        mv.addObject("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        // 最后审核状态
        mv.addObject("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historicActivityInstance = setAssignRealNames(mv, historicInfo);
        mv.addObject("historicActivityInstance", historicActivityInstance);
        //mv.addObject("historicActivityInstance", oldHistoricInfo.get("historicActivityInstance"));

        mv.addObject("commentMap", historicInfo.get("commentMap"));
        mv.addObject("candidateUserMap", historicInfo.get("candidateUserMap"));
        Task taskInfo = (Task) historicInfo.get("taskInfo");
        // 当前审核人
        String verifyUsers = null;
        List<String> verifyUserList = new ArrayList<>();
        if (null != taskInfo) {
            Map<String, Object> taskInfoVariables = actionProcdefService.getVariablesMap(taskInfo);
            verifyUsers = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUsers"));
            String verifyUser = getVerifyUserRealNames((String) taskInfoVariables.get("verifyUserList"));
            if (null != verifyUser) {
                verifyUserList = Arrays.asList(verifyUser.split(","));
            }
        }
        List<String> verifyUsersList = new ArrayList<>();
        if (verifyUsers != null && !verifyUserList.isEmpty()) {
            verifyUsersList = Arrays.asList(verifyUsers.split(","));
        }

        mv.addObject("verifyUsers", getVerifyUserRealNames(verifyUsers));
        mv.addObject("verifyUserList", verifyUserList);
        mv.addObject("verifyUsersList", verifyUsersList);


        return mv;
    }

    private String setStatusStr(SaleorderModifyApply saleorderModifyApplyInfo) {

        User creator = userService.getUserById(saleorderModifyApplyInfo.getCreator());
        //{"label":"待审核","jump":"#J-test","tip":"审核者：Libai(李白)","lock":"订单审核中","status":2}
        List<StatusNode> statusNodeList = new ArrayList<>();
        StatusNode statusNode = new StatusNode();
        statusNode.setLabel("待审核");
        statusNode.setJump("#J-test");
        statusNode.setTip("申请者:"+creator.getUsername());
        statusNode.setStatus(1);

        StatusNode statusNode2 = new StatusNode();
        statusNode2.setLabel("审核中");
        statusNode2.setStatus(1);
        Integer verifyStatus = saleorderModifyApplyInfo.getVerifyStatus();
        StatusNode statusNode3 = new StatusNode();
        if(verifyStatus == null){
            statusNode3.setLabel("审核通过/审核不通过");
        }else if(verifyStatus == 1){
            statusNode3.setLabel("审核通过");
        }else if(verifyStatus == 2){
            statusNode3.setLabel("审核不通过");
        }else{
            statusNode3.setLabel("审核通过/审核不通过");
        }
        statusNode3.setStatus(1);
        if(verifyStatus == null || verifyStatus == 3){
            statusNode.setStatus(2);
        }else if(verifyStatus == 0){
            statusNode2.setStatus(2);
        }else{
            statusNode3.setStatus(2);
        }

        statusNodeList.add(statusNode);
        statusNodeList.add(statusNode2);
        statusNodeList.add(statusNode3);

        return JSON.toJSONString(statusNodeList);
    }

    /**
     * <b>Description:</b><br>
     * 审核弹层页面
     *
     * @param session
     * @param taskId
     * @param pass
     * @param type
     * @return
     * @Note <b>Author:</b> leo.yang <br>
     *       <b>Date:</b> 2018年1月5日 下午3:16:51 as to 20190115 添加参数saleorderId用于同步订单状态
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    public ModelAndView complement(HttpSession session, String taskId, Boolean pass, Integer type,
                                   Integer saleorderId,
                                   @RequestParam(required = false) Integer isLightning,
                                   @RequestParam(required = false) Integer lightningAfterSalesId,@RequestParam(required = false)Integer afterSaleorderId) {
        ModelAndView mv = new ModelAndView();
        if (isLightning!=null){
            mv.addObject("isLightning",isLightning);
            mv.addObject("lightningAfterSalesId",lightningAfterSalesId);
        }
        logger.info("订单申请审核taskid{}",taskId);
        mv.addObject("taskId", taskId);
        mv.addObject("saleorderId", saleorderId);
        mv.addObject("afterSaleorderId", afterSaleorderId);
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        mv.setViewName("orderstream/saleorder/complement");
        return mv;
    }
    /**
     * <b>Description:</b><br>
     * 订单并行审核操作
     *
     * @Note <b>Author:</b> Michael <br>
     *       <b>Date:</b> 2017年11月10日 下午1:39:42
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementTaskParallel")
    public ResultInfo<?> complementTaskParallel(HttpServletRequest request, String taskId, String comment, Boolean pass) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("pass", pass);
        variables.put("updater", user.getUserId());
        variables.put("autoCheckAptitude",false);
        variables.put("rejectReason",comment);
        // 审批操作
        try {
            Integer statusInfo = 0;
            if (pass) {
                // 如果审核通过
                statusInfo = 0;
            } else {
                // 如果审核不通过
                statusInfo = 2;
                // 回写数据的表在db中
                variables.put("db", 2);
                TaskService taskService = processEngine.getTaskService();// 获取任务的Service，设置和获取流程变量
                Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
                String tableName = (String) taskService.getVariable(taskId, "tableName");
                if (tableName != null && "T_SALEORDER_MODIFY_APPLY".equals(tableName)) {
                    Integer idValue1 = (Integer) taskService.getVariable(taskId, "idValue1");
                    actionProcdefService.updateInfo("T_SALEORDER", "SALEORDER_ID", idValue1, "LOCKED_STATUS", 0, 2);
                } else {
                    if (tableName != null) {
                        actionProcdefService.updateInfo(tableName, "SALEORDER_ID", idValue, "LOCKED_STATUS", 0, 2);
                    }
                }
                verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);

            }
            ResultInfo<?> complementStatus = null;

            TaskService taskService = processEngine.getTaskService();
            HistoryService historyService = processEngine.getHistoryService(); // 任务相关service
            String processInstanceId = null;
            HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery()
                    .taskId(taskId).singleResult();
            processInstanceId = historicTaskInstance.getProcessInstanceId();
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                    .processInstanceId(processInstanceId).singleResult();
            String businessKey = historicProcessInstance.getBusinessKey();
            List<HistoricVariableInstance> historicVariableInstanceList = historyService
                    .createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
            // 把list转为Map
            Map<String, Object> variablesMap = new HashMap<String, Object>();
            // 获取当前活动节点
            Task taskInfo = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            if(taskInfo == null){
                return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
            }
            if (pass) {

                historicVariableInstanceList.forEach(hvi -> variablesMap.put(hvi.getVariableName(), hvi.getValue()));
                Integer orderId = (Integer) variablesMap.get("orderId");

                if (businessKey.contains("_")) {
                    List<String> businessKeyList = Arrays.asList(businessKey.split("_"));
                    if (businessKeyList.size() > 1 && "saleorderVerify".equals(businessKeyList.get(0))) {
                        Integer saleOrderId = Integer.valueOf(businessKeyList.get(1));
                        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderById(saleOrderId);
                        ResultInfo checkResult = traderCustomerService.checkBillPeriodForOrder(saleOrderInfo, saleorderMapper.getCustomerIdByOrderId(saleOrderId).intValue());
                        if (!checkResult.getCode().equals(0)) {
                            return checkResult;
                        }

                    }
                }

                if ("产品线归属人审核".equals(taskInfo.getName())) {

                    Integer saleorderModifyApplyId = null;
                    if (businessKey.contains("_")) {
                        List<String> businessKeyList = Arrays.asList(businessKey.split("_"));
                        if (businessKeyList.size() > 1 && "saleorderModifyAudit".equals(businessKeyList.get(0))) {
                            saleorderModifyApplyId = Integer.valueOf(businessKeyList.get(1));
                        }
                    }


                    List<Integer> changeIsDirectGoodsIds = new ArrayList<>();
                    boolean isChangeTakeAddress = false;

                    SaleorderModifyApply saleorderModifyApply = saleorderModifyApplyMapper.selectByPrimaryKey(saleorderModifyApplyId);
                    List<SaleorderModifyApplyGoods> modifyApplyGoods = saleorderModifyApplyGoodsMapper.getSaleorderModifyApplyGoodsById(saleorderModifyApply);
                    List<SaleorderGoods> saleorderGoodList = saleorderMapper.getSaleorderGoodsById(orderId);

                    Saleorder saleorder = new Saleorder();
                    saleorder.setSaleorderId(orderId);
                    saleorder = saleorderService.getBaseSaleorderInfo(saleorder);
                    if (Objects.nonNull(saleorder.getTakeTraderAddressId())
                            && Objects.nonNull(saleorderModifyApply.getTakeTraderAddressId())
                            && !saleorder.getTakeTraderAddressId().equals(saleorderModifyApply.getTakeTraderAddressId())
                            && saleorder.getDeliveryDirect() == 1) {
                        isChangeTakeAddress = true;
                        for (SaleorderGoods goods : saleorderGoodList) {
                            if (goods != null && goods.getGoodsId() != null) {
                                changeIsDirectGoodsIds.add(goods.getGoodsId());
                            }
                        }
                    }

                    for (SaleorderGoods sg : saleorderGoodList) {
                        for (SaleorderModifyApplyGoods smag : modifyApplyGoods) {
                            if (sg.getSaleorderGoodsId().equals(smag.getSaleorderGoodsId())) {
                                if (!sg.getDeliveryDirect().equals(smag.getDeliveryDirect())) {
                                    if (!isChangeTakeAddress) {
                                        changeIsDirectGoodsIds.add(sg.getGoodsId());
                                    }
                                }
                            }
                        }
                    }

                    List<IdentityLink> candidateUserList = (List<IdentityLink>) variablesMap.getOrDefault("candidateUserList", taskService.getIdentityLinksForTask(taskId));
                    List<GoodsBelong> goodsBelongList = saleorderMapper.getGoodsBelong(changeIsDirectGoodsIds);

                    List<String> verifyUserList = goodsBelongList.stream()
                            .filter(sku -> user.getUsername().equals(sku.getAssignmentManagerName()) || user.getUsername().equals(sku.getAssignmentAssistantName()))
                            .flatMap(sku -> Stream.of(sku.getAssignmentAssistantName(), sku.getAssignmentManagerName()))
                            .filter(v->!"".equals(v))
                            .distinct()
                            .collect(Collectors.toList());

                    String existingVerifyUserList = (String) variablesMap.getOrDefault("verifyUserList", "");
                    List<String> verifyUserListAlready = Arrays.asList(existingVerifyUserList.split(","));
                    List<String> mergeList = Stream.concat(verifyUserListAlready.stream(), verifyUserList.stream())
                            .filter(v->!"".equals(v))
                            .distinct()
                            .collect(Collectors.toList());
                    taskService.setVariable(taskId, "verifyUserList", String.join(",", mergeList));
                    // 获取候选用户列表
                    List<String> candidateUsers = candidateUserList.stream()
                            .map(IdentityLink::getUserId)
                            .collect(Collectors.toList());

                    if (mergeList.containsAll(candidateUsers)) {
                        complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
                        logger.info("Completed approval task taskId: {}", taskId);
                    }

                } else {
                    // 非并行节点
                    complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(),
                            variables);
                }
                //Bd订单审核通过更新精选数据
                String[] split = businessKey.split("_");
                if("saleorderVerify".equals(split[0]) &&
                        (OrderGoodsAptitudeConstants.KEY_OPERATE_CHECK.equals(taskInfo.getName()) || OrderGoodsAptitudeConstants.KEY_QUALITY_DEPART_CHECK.equals(taskInfo.getName()))
                ) {
                    Integer saleorderId = Integer.valueOf(split[1]);
                    Saleorder s = saleorderMapper.getSaleOrderById(saleorderId);
                    //VDERP-1511销售单生效时，根据订单里是否有直发商品，改变订单的直发状态
                    saleorderService.updateOrderdeliveryDirect(s);
                    if(s.getOrderType()<=1) {
                        JSONObject result2 = saleorderService.updateVedengJX(saleorderId);
                        ResultInfo senResult = expressService.sendWxMessageForArrival(saleorderId);
                    }
                    //调用库存服务
                    int i = warehouseStockService.updateOccupyStockService(s,0);
                    //VDERP-2263   订单售后采购改动通知
                    orderCommonService.updateSaleOrderDataUpdateTime(saleorderId,null, OrderDataUpdateConstant.SALE_ORDER_VAILD);
                }


                if("editSaleorderVerify".equals(split[0])&& "物流审核".equals(taskInfo.getName())) {
                    Saleorder saleorder = saleorderMapper.getSaleOrderById(orderId);
                    if(saleorder.getOrderType()==1) {
                        JSONObject result2 = saleorderService.ChangeEditSaleOrder(saleorder);
                    }
                    //调用库存服务
                    int i = warehouseStockService.updateOccupyStockService(saleorder,0);

                    //VDERP-2263   订单售后采购改动通知
                    orderCommonService.updateSaleOrderDataUpdateTime(orderId,null,OrderDataUpdateConstant.SALE_ORDER_VAILD);
                }

            } else {
                complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(),
                        variables);
            }
            //更新合同审核备注
            saleorderService.updateContractReas(comment, pass, businessKey, taskInfo);

            // 如果未结束添加审核对应主表的审核状态
            if (complementStatus != null) {
                // 审核节点操作失败
                if(complementStatus.getCode().equals(-1)){
                    return complementStatus;
                }
                if(!"endEvent".equals(complementStatus.getData())){
                    verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
                }
            }

            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            logger.error("complementTaskParallel:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }


    @ResponseBody
    @RequestMapping(value = "/updateInsideComments")
    public ResultInfo<?> updateInsideComments(@RequestBody(required = false) RemarkComponentDto labelDataDto) {
        log.info("编辑内部备注标签 updateInsideComments Start -- {}",labelDataDto);
        ResultInfo<?> resultInfo = new ResultInfo<>();
        try{
            resultInfo = saleorderService.updateInsideComments(labelDataDto);
            if (ErpConst.ZERO.equals(resultInfo.getCode())){
                //更新人工备注,不用审核VDERP-5809
                if (CollectionUtils.isNotEmpty(labelDataDto.getLabelQuery().getSkuList())){
                    int  saleorderId = labelDataDto.getLabelQuery().getRelationId();
                    String remark = labelDataDto.getRemark();
                    for (SkuVo skuVo : labelDataDto.getLabelQuery().getSkuList()) {
                        String sku = skuVo.getSkuNo();
                        saleorderService.updateInsideCommentsBySaleorderIdAndSku(saleorderId,sku,remark);
                    }
                }
            }
        }catch (Exception e){
            resultInfo = new ResultInfo<>(-1,e.getMessage());
        }
        log.info("编辑内部备注标签 updateInsideComments End -- {}",resultInfo);
        return resultInfo;
    }
    @FormToken(save = true)
    @RequestMapping(value = "/productConfirmPage")
    @ResponseBody
    public ModelAndView productConfirmPage(HttpServletRequest request,@RequestParam(required = false, value = "saleOrderIds") String saleOrderIds,
                                           @RequestParam(required = false, value = "traderId") String traderId) {
        ModelAndView mav = new ModelAndView("orderstream/saleorder/productConfirmPage");
        try {
            List<Integer> saleOrderIdList = Arrays.stream(saleOrderIds.split(","))
                    .mapToInt(Integer::parseInt)
                    .boxed()
                    .collect(Collectors.toList());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(saleOrderIdList)) {
                List<SaleorderGoods> saleorderGoodsList = saleorderService.getSaleOrderGoodsListByOrderIdList(saleOrderIdList);
                mav.addObject("saleorderGoodsList",saleorderGoodsList);
                mav.addObject("skuNoList",saleorderGoodsList.stream().map(SaleorderGoods::getSku).collect(Collectors.joining(",")));
                mav.addObject("orderIdList",saleOrderIds);
                mav.addObject("traderId",traderId);
            }
        }catch (Exception e){
            logger.error("转换失败:", e);
        }
        return mav;
    }

    @ResponseBody
    @RequestMapping(value = "getSkuPriceInfo")
    public ResultInfo<?> getSkuPriceInfo(HttpServletRequest request, String skuNos,Integer traderId) {
        if (skuNos != null){
            List<String> skuNoList = Arrays.asList(skuNos.split(","));
            BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.BASE);
            Map<String,BigDecimal> skuPriceMap = saleOrderService.getSkuPriceInfo(skuNoList,traderId);
            return ResultInfo.success(skuPriceMap);
        }
        return ResultInfo.error("查询价格失败");
    }


    @RequestMapping(value = "/getSaleOrderInfo")
    @ResponseBody
    public ResultInfo getSaleOrderInfo(HttpServletRequest request, String saleOrderNo,Integer traderId) {
        try {
            Saleorder saleorder = saleorderService.getSaleOrderByOrderNoAndTraderId(saleOrderNo,traderId);
            return ResultInfo.success(saleorder);
        } catch (IllegalArgumentException e) {
            log.error("【getSaleOrderInfo】处理异常",e);
            return ResultInfo.error(e.getMessage());
        }
    }

    /**
     * 一键复购
     * @param request
     * @param saleorder
     * @return
     */
    @FormToken(remove = true)
    @RequestMapping(value = "/saveRePurchaseOrder")
    @ResponseBody
    public ModelAndView saveRePurchaseOrder(HttpServletRequest request, Saleorder saleorder) {
        ModelAndView modelAndView = new ModelAndView();
        User user = getSessionUser(request);
        //设置基本信息
        setOrderBaseInfo(saleorder,user);

        ResultInfo resultInfo = rePurchaseCreateSaleOrderBuzLogic.run(saleorder);
        if (ErpConst.ZERO.equals(resultInfo.getCode())) {
            Saleorder order = (Saleorder) resultInfo.getData();
            modelAndView.addObject("url", "/orderstream/saleorder/edit.do?saleorderId=" + order.getSaleorderId()
                    + "&extraType=" + order.getExtraType());

            addTrackCreateSaleOrder(CurrentUser.getCurrentUser(),order.getSaleorderNo(),order.getTraderId(),"复制创建");
            return success(modelAndView);
        } else {
            return fail(modelAndView);
        }

    }

    public void setOrderBaseInfo(Saleorder saleorder, User user){
        saleorder.setAddTime(System.currentTimeMillis());
        saleorder.setCreator(user.getUserId());
        saleorder.setCreatorOrgId(user.getOrgId());
        saleorder.setCreatorOrgName(user.getOrgName());
        saleorder.setCompanyId(user.getCompanyId());
        saleorder.setStatus(ErpConst.ZERO);
        saleorder.setIsNew(ErpConst.ONE);
        //终端信息
        getTerminalCustomerInfo(saleorder);
        // 归属销售
        if (saleorder.getTraderId() != null) {
            User belongUser = userService.getUserInfoByTraderId(saleorder.getTraderId(), 1);// 1客户，2供应商
            if (belongUser != null && belongUser.getUserId() != null) {
                saleorder.setUserId(belongUser.getUserId());
            }
            if (belongUser != null && belongUser.getOrgId() != null) {
                saleorder.setOrgId(belongUser.getOrgId());
            }
            if (belongUser != null && belongUser.getOrgName() != null) {
                saleorder.setOrgName(belongUser.getOrgName());
            }
        }
    }

    /**
     * @desc 获取省区
     * <AUTHOR>
     * @param mv
     */
    private void getFirstProvince(ModelAndView mv){
        List<Region> provinceList = regionService.getRegionByParentId(ErpConst.ONE);
        mv.addObject("provinceList", provinceList);
    }


    @RequestMapping(value = "/saveCopyOrder")
    @ResponseBody
    public ModelAndView saveCopyOrder(HttpServletRequest request, Integer saleOrderId) {
        User user = getSessionUser(request);
        ModelAndView modelAndView = new ModelAndView();
        ResultInfo resultInfo = new ResultInfo();
        BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
        Saleorder saleorder = saleOrderService.getSaleOrderBaseInfo(saleOrderId);
        //设置基本信息
        setCopyOrderBaseInfo(saleorder,user);
        switch (SalesOrderTypeEnum.getInstance(saleorder.getOrderType())){
            case ZXF:{
                saleorder.setStatus(ErpConst.ZERO);
                resultInfo = zxfCreateSalesOrderBuzLogic.run(saleorder);
                break;
            }
            case JCF:{
                saleorder.setStatus(ErpConst.FOUR);
                resultInfo = jcfCreateSaleOrderBuzLogic.run(saleorder);
                break;
            }
            default:
                break;
        }
        if (ErpConst.ZERO.equals(resultInfo.getCode())) {
            modelAndView.addObject("url", "/orderstream/saleorder/edit.do?saleorderId=" + saleorder.getSaleorderId()
                    + "&extraType=" + saleorder.getExtraType());
            return success(modelAndView);
        } else {
            return fail(modelAndView);
        }

    }

    private void setCopyOrderBaseInfo(Saleorder saleorder,User user){
        saleorder.setCreator(user.getUserId());
        saleorder.setCreatorOrgId(user.getOrgId());
        saleorder.setCreatorOrgName(user.getOrgName());
        saleorder.setCompanyId(user.getCompanyId());
        saleorder.setQuoteorderId(null);
        saleorder.setValidStatus(ErpConst.ZERO);
        saleorder.setIsCopy(ErpConst.ONE);
        saleorder.setAddTime(System.currentTimeMillis());
        saleorder.setModTime(System.currentTimeMillis());
    }

    /**
     * 半程电子签章按钮
     *
     * @param request
     * @param saleorderId 订单号
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/signature")
    @NoNeedAccessAuthorization
    public ResultInfo signature(HttpServletRequest request, Integer saleorderId) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        //判断是否有权限
        boolean flag = saleorderService.isShowSignature(user,saleorderId);
        if(flag){
            electronicSignatureService.signature(user, saleorderId);
            return ResultInfo.success();
        }else{
            return ResultInfo.error("仅归属销售有操作权限");
        }

    }

    /**
     * 待签收货物列表
     * @param request
     * @param saleorder
     * @param session
     * @param pageNo
     * @param pageSize
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/pendingOrderIndex")
    public ModelAndView pendingOrderIndex(HttpServletRequest request, Saleorder saleorder, HttpSession session,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false) Integer pageSize){
        ModelAndView view = new ModelAndView();

        if(null != request.getParameter("searchBegintimeStr") &&  !"".equals(request.getParameter("searchBegintimeStr"))){
            if (saleorder.getSearchDateType() != null && saleorder.getSearchDateType() == 7) {
                saleorder.setSearchBeginDate(request.getParameter("searchBegintimeStr") + " 00:00:00");
            }
            saleorder.setSearchBegintime(DateUtil.convertLong(request.getParameter("searchBegintimeStr") + " 00:00:00", "yyyy-MM-dd HH:mm:ss"));

        }
        if(null != request.getParameter("searchEndtimeStr") && !"".equals(request.getParameter("searchEndtimeStr"))){
            if (saleorder.getSearchDateType() != null && saleorder.getSearchDateType() == 7) {
                saleorder.setSearchEndDate(request.getParameter("searchEndtimeStr") + " 23:59:59");
            }
            saleorder.setSearchEndtime(DateUtil.convertLong(request.getParameter("searchEndtimeStr") + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));

        }

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        // 获取当前销售用户下级职位用户
        List<Integer> positionType = new ArrayList<>();
        positionType.add(SysOptionConstant.ID_310);
        List<User> userList = userService.getMyUserList(user, positionType, false);
        // 操作人
        view.addObject("userList", userList);

        List<User> saleUserList = new ArrayList<>();
        if (null != saleorder.getOptUserId() && saleorder.getOptUserId() != -1) {
            User saleUser = new User();
            saleUser.setUserId(saleorder.getOptUserId());
            saleUserList.add(saleUser);
            saleorder.setSaleUserList(saleUserList);
        } else if (null != user.getPositType() && user.getPositType() == 310) {
            // 销售
            saleorder.setSaleUserList(userList);
        }

        // 获取销售部门
//        List<Organization> orgList = orgService.getSalesOrgListOrderByStruct(SysOptionConstant.ID_310, user.getCompanyId());
        List<Organization> orgList = new ArrayList<>();
        List<Organization> organizationList = orgService.getOriganizationByParentId(0);
        fillOrganization(organizationList, 6,orgList);
        view.addObject("organizationList", JSON.toJSONString(organizationList));
        view.addObject("orgList", orgList);

        // 生效时间
        // saleorder.setValidTime(DateUtil.StringToLong("2021-12-20 00:00:00"));

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> map = pendingOrderService.getPendingOrderList(saleorder, page);

        view.addObject("saleorderReq",JSON.toJSONString(saleorder));

        view.addObject("saleorderList", map.get("saleorderList"));
        view.addObject("page", map.get("page"));
        view.addObject("confirmOrderUrl",confirmOrderUrl);
        view.addObject("copyUrl",confirmOrderUrl+"e/dlm/"+map.get("traderId")+"/"+ map.get("traderContactMobile"));

        view.addObject("traderId",map.get("traderId"));
        view.addObject("traderContactMobile",map.get("traderContactMobile"));
        view.addObject("smsButton",true);
        if ("njadmin".equals(user.getUsername())) {
            view.addObject("copyButton","2");
            view.addObject("smsButton",false);
        } else {
            view.addObject("copyButton",map.get("copyButton"));
        }
        view.setViewName("order/saleorder/pending_order_index");
        return view;
    }

    private void fillOrganization(List<Organization> organizationList, Integer level,List<Organization> orgList) {
        if (level > 1 && CollectionUtils.isNotEmpty(organizationList)) {
            for (Organization organization : organizationList) {
                fill(organization, orgList);
                Integer a = level - 1;
                fillOrganization(organization.getOrganizations(), a, orgList);
            }
        }
    }

    private void fill(Organization item,List<Organization> orgList) {
        List<Organization> organizationList = orgService.getOriganizationByParentId(item.getOrgId());
        if (CollectionUtils.isNotEmpty(organizationList)) {
            item.setOrganizations(organizationList);
            orgList.addAll(organizationList);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/copyOrderLink")
    @NoNeedAccessAuthorization
    public void copyOrderLink(String traderId, String traderContactId){
        Clipboard systemClipboard = Toolkit.getDefaultToolkit().getSystemClipboard();
        String url = confirmOrderUrl+"e/dlm"+"/"+traderId+"/"+ traderContactId;
        StringSelection stringSelection = new StringSelection(url);
        systemClipboard.setContents(stringSelection,null);
    }


    @ResponseBody
    @RequestMapping(value = "/sendSms")
    public ResultInfo<String> sendSms(HttpSession session, @RequestBody List<SaleorderVo> saleorderVos){
        ResultInfo<String> result = new ResultInfo<>();
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        try {

            if (!CollectionUtils.isEmpty(saleorderVos) && saleorderVos.size() == 1 && saleorderVos.get(0).getTraderContactMobile() == null) {
                saleorderVos = pendingOrderService.getPendingOrderListBySaleorder(saleorderVos.get(0), null, null);
            }
            // TODO: 2022/10/14 VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）
            //pendingOrderService.sendSms(saleorderVos, user, ConfirmRecordSendTypeEnum.MANUAL_SEND.getCode());
            result.setCode(0);
            result.setMessage("操作成功，发送签收通知短信通知成功");
        } catch (Exception e){
            logger.error("发送签收通知短信发送失败",e);
            result.setCode(-1);
            result.setMessage("操作失败，发送签收通知短信通知失败");
        }
        return result;
    }

    @ResponseBody
    @RequestMapping(value = "/verifySpecialInvoice")
    @NoNeedAccessAuthorization
    public ResultInfo verifySpecialInvoice(Integer invoiceType,Integer saleorderId,Integer invoiceTraderId){
        ResultInfo resultInfo = new ResultInfo(0,"校验成功");
        resultInfo = saleorderService.verifySpecialTicket(saleorderId,invoiceType,invoiceTraderId);
        TraderCustomer traderCustomer = traderCustomerService.getCustomerInfo(invoiceTraderId);
        resultInfo.setData(traderCustomer);
        return resultInfo;
    }

    @ExcludeAuthorization
    @RequestMapping(value = "/confirmationCheckDetails")
    public ModelAndView confirmationCheckDetails(Integer saleOrderId){
        ModelAndView mv = new ModelAndView("vue/view/confirmation/checkDetails");
        mv.addObject("saleOrderId",saleOrderId);
        return mv;
    }

    /**
     * 打开上传确认单表单
     * @param saleOrderId
     * @return
     */
    @ExcludeAuthorization
    @RequestMapping(value = "/uploadConfirmation")
    public ModelAndView uploadConfirmation(HttpSession session,Integer saleOrderId){
        List<OutboundBatchesRecode> outboundBatchesRecodes = expressService.getSaleOrderOutboundBatchesRecode(session,saleOrderId);
        ModelAndView mv = new ModelAndView("vue/view/confirmation/uploadConfirmation");
        mv.addObject("saleOrderId",saleOrderId);
        mv.addObject("outboundBatchesRecodes",JSON.toJSON(outboundBatchesRecodes));
        return mv;
    }

    @ExcludeAuthorization
    @RequestMapping(value = "/uploadImg")
    @ResponseBody
    public FileInfo uploadImg(HttpServletRequest request, @RequestBody MultipartFile file){
        FileInfo fileInfo = null;
        ChainResult result = imgUploadVerifyActuator.ruleExecute(request, file);
        if(!result.isSuccess()){
            return new FileInfo(-1,result.getData().toString());
        }
        try{
            if (null == file){
                //Processing of multipart/form-data request failed. null
                logger.error("文件上传失败：文件为null");
                fileInfo =  new FileInfo(-1,"文件上传失败");
                return fileInfo;
            }
            // 上传文件名称
            String fileName = file.getOriginalFilename();
            // 文件后缀名称
            String prefix = fileName.substring(fileName.lastIndexOf(".") + 1);
            fileInfo = ossUtilsService.upload2Oss(request,file);
            fileInfo.setDomain(fileInfo.getHttpUrl());
            fileInfo.setHttpUrl(api_http + fileInfo.getHttpUrl());
            fileInfo.setPrefix(prefix);
            fileInfo.setFileName(fileName);
        }catch (Exception e){
            logger.error("文件上传失败：", e);
            fileInfo =  new FileInfo(-1,"文件上传失败");
        }
        return fileInfo;
    }

    @ExcludeAuthorization
    @RequestMapping(value = "/confirmationDetails")
    public ModelAndView confirmationDetails(HttpSession session,Integer confirmationId,Integer saleorderId){
        ModelAndView mv = new ModelAndView("vue/view/confirmation/confirmationDetails");
        ConfirmationBatchesRelation confirmationBatchesRelation = new ConfirmationBatchesRelation();
        confirmationBatchesRelation.setConfirmationId(confirmationId);
        //获取销售订单下的所有确认单 批次 关系
        List<ConfirmationBatchesRelation> confirmationBatchesRelations = confirmationBatchesRelationMapper.selectAllByRelation(confirmationBatchesRelation);
        //获取所有确认单
        List<Integer> confirmationIds = confirmationBatchesRelations.stream().map(ConfirmationBatchesRelation::getConfirmationId).collect(Collectors.toList());
        List<ConfirmationFormRecode> confirmationFormRecodes = confirmationFormRecodeMapper.selectAllByIds(confirmationIds);
        //获取确认单对多个批次  map关系
        Map<Integer, List<String>> cwmap = confirmationBatchesRelations.stream().collect(Collectors.toMap(ConfirmationBatchesRelation::getConfirmationId, p -> {
                    List<String> strings = new ArrayList<>();
                    strings.add(p.getBatchNo());
                    return strings;
                }, (List<String> v1, List<String> v2) -> {
                    v1.addAll(v2);
                    return v1;
                }
        ));
        //获取每个批次下的物流信息
        List<String> batchNoList = confirmationBatchesRelations.stream().map(ConfirmationBatchesRelation::getBatchNo).collect(Collectors.toList());
        HashMap<String, List<Express>> batchkvExpress = new HashMap<>();
        batchNoList.stream().forEach(x->{
            List<Express> expressListByBatchNo = expressMapper.getExpressListByBatchNo(x);
            batchkvExpress.put(x,expressListByBatchNo);
        });
        //批次具体信息
        List<OutboundBatchesRecode> allByBatchNoList = new ArrayList<>();
        if (batchNoList.size()>0){
            allByBatchNoList = outboundBatchesRecodeMapper.findAllByBatchNoList(batchNoList);
        }

        //组装确认单,批次,物流信息
        for (int i = 0; i < confirmationFormRecodes.size(); i++) {
            //url
            List<String> urls = new ArrayList<>();
            List<String> pdfs = new ArrayList<>();
            ConfirmationFormRecode cf = confirmationFormRecodes.get(i);
            List<String> fileIdstrs = Arrays.asList(cf.getFileId().split(","));
            List<Long> fileIds = fileIdstrs.stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
            List<com.vedeng.infrastructure.file.domain.Attachment> attachments = attachmentMapper.selectAllByIds(fileIds);
            attachments.forEach(x->{
                if (StringUtils.isNotBlank(x.getSuffix()) && x.getSuffix().equals("pdf")){
                    pdfs.add(x.getUri());
                }else {
                    urls.add(x.getUri());
                }
            });
            cf.setAttachmentsBack(attachments);
            cf.setUrlList(urls);
            cf.setPdfList(pdfs);
            //批次集合
            List<String> batchNos = cwmap.get(cf.getId());
            List<OutboundBatchesRecode> outboundBatchesRecodes = allByBatchNoList.stream().filter(x -> {
                if (batchNos.contains(x.getBatchNo())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            for (int i1 = 0; i1 < outboundBatchesRecodes.size(); i1++) {
                OutboundBatchesRecode outboundBatchesRecode = outboundBatchesRecodes.get(i1);
                String batchNo = outboundBatchesRecode.getBatchNo();

                //获取批次下不同商品
                User curr_user = (User) session.getAttribute(ErpConst.CURR_USER);
                Saleorder saleorder = new Saleorder();
                saleorder.setSaleorderId(saleorderId);
                // 获取订单详情
                saleorder.setCompanyId(curr_user.getCompanyId());

                saleorder.setBussinessType(2);

                List<BatchExpressVo> batchExpressByIds = new ArrayList<>();

                //判断直发普发
                if(outboundBatchesRecode.getBatchType().equals(1)){
                    List<WarehouseGoodsOperateLog> wgOlistByComments = warehouseOutService.getWGOlistByComments(batchNo,saleorderId);
                    if (wgOlistByComments.size()==0) {
                        continue;
                    }
                    //物流单号
                    List<Express> expressList = batchkvExpress.get(batchNo).stream().collect(Collectors.toList());
                    List<String> expressLogiscsNo = expressList.stream().map(Express::getLogisticsNo).collect(Collectors.toList());
                    String logiscsNos = String.join(",", expressLogiscsNo);

                    saleorder.setBatchNoComments(batchNo);
                    // 普发出库记录清单
                    List<WarehouseGoodsOperateLog> warehouseOutList = warehouseOutService.getOutDetil(saleorder);

                    List<String> checkSku = new ArrayList<>();
                    //遍历处理普发出库记录
                    if (null != warehouseOutList) {
                        Map<String, Integer> skuNum = warehouseOutList.stream().collect(Collectors.toMap(WarehouseGoodsOperateLog::getSku, WarehouseGoodsOperateLog::getRealGoodsNum, (v1, v2) -> v1 + v2));

                        for (WarehouseGoodsOperateLog wl : warehouseOutList) {
                            outboundBatchesRecode.setBatchTime(cn.hutool.core.date.DateUtil.date(wl.getAddTime()).toString("yyyy-MM-dd HH:mm:ss"));
                            BatchExpressVo batchExpressVo = new BatchExpressVo();
                            if (checkSku.contains(wl.getSku())){
                                continue;
                            }
                            batchExpressVo.setNum(skuNum.get(wl.getSku()));
                            batchExpressVo.setSku(wl.getSku());
                            batchExpressVo.setModel(wl.getModel());
                            batchExpressVo.setBrand(wl.getBrandName());
                            batchExpressVo.setGoodsName(wl.getGoodsName());
                            batchExpressVo.setLogisticsOrderNo(logiscsNos);
                            batchExpressByIds.add(batchExpressVo);
                            checkSku.add(wl.getSku());

                        }
                    }
                }else {
                    directBatchNos(saleorderId, outboundBatchesRecode, batchExpressByIds);
                }

                outboundBatchesRecode.setBatchExpressVos(batchExpressByIds);
            }
            cf.setBatchesRecodes(outboundBatchesRecodes);
            mv.addObject("confirmationFormRecode",JSON.toJSON(cf));
        }
        return mv;
    }

    private void directBatchNos(Integer saleorderId, OutboundBatchesRecode outboundBatchesRecode, List<BatchExpressVo> batchExpressByIds) {
        String batchNo = outboundBatchesRecode.getBatchNo();
        Express expressQueryVo = new Express();
        List<SaleorderGoods> saleorderGoods = saleorderGoodsMapper.getAllSaleorderGoodsBySaleorderId(saleorderId);
        List<Express> buyExpressList = new ArrayList<>();
        if (saleorderGoods.size()>0){
            List<Integer> listSale = saleorderGoods.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
            expressQueryVo.setRelatedIds(listSale);
            expressQueryVo.setBatchNo(batchNo);
            buyExpressList = expressMapper.getBuyExpressList(expressQueryVo);
        }


        Map<String, BatchExpressVo> skuNum = new HashMap<>();
        for (int j = 0; j < buyExpressList.size(); j++) {
            Express express = buyExpressList.get(j);
            String logisticsNo = express.getLogisticsNo();
            List<ExpressDetail> expressDetail = express.getExpressDetail();
            List<String> checkSku = new ArrayList<>();
            outboundBatchesRecode.setBatchTime(DateUtil.convertString(express.getDeliveryTime(), "yyyy-MM-dd"));

            for (ExpressDetail detail : expressDetail) {
                String sku = detail.getSku();
                if (skuNum.containsKey(sku)) {
                    BatchExpressVo batchExpressVo = skuNum.get(sku);
                    Integer num = batchExpressVo.getNum();
                    num = num+detail.getNum();
                    batchExpressVo.setNum(num);
                    List<String> logisticsOrderNoUse = batchExpressVo.getLogisticsOrderNoUse();
                    if (!logisticsOrderNoUse.contains(logisticsNo)) {
                        logisticsOrderNoUse.add(logisticsNo);
                        batchExpressVo.setLogisticsOrderNo(String.join(",",logisticsOrderNoUse));
                    }
                }else{
                    BatchExpressVo batchExpressVo = new BatchExpressVo();
                    batchExpressVo.setNum(detail.getNum());
                    List<String> logisticsOrderNoUse = new ArrayList<>();
                    logisticsOrderNoUse.add(logisticsNo);
                    batchExpressVo.setLogisticsOrderNoUse(logisticsOrderNoUse);
                    batchExpressVo.setLogisticsOrderNo(logisticsNo);
                    batchExpressVo.setSku(detail.getSku());
                    batchExpressVo.setModel(detail.getModel());
                    batchExpressVo.setBrand(detail.getBrandName());
                    batchExpressVo.setGoodsName(detail.getGoodName());
                    skuNum.put(detail.getSku(),batchExpressVo);
                }

            }
        }

        Set<String> keySet = skuNum.keySet();
        for (String s : keySet) {
            BatchExpressVo batchExpressVo = skuNum.get(s);
            batchExpressByIds.add(batchExpressVo);
        }
    }

    private void replaceExpenseBuyDetails(List<SaleorderGoods> saleorderGoodsList){
        List<Integer> ids = saleorderGoodsList.stream().map(SaleorderGoods::getSaleorderGoodsId).collect(Collectors.toList());
        //需要替换交付信息的的虚拟商品List
        List<ExpenseBuyForSaleDetail> details = rBuyorderExpenseJSaleorderService.replaceExpenseBuyDetails(ids);
        for (ExpenseBuyForSaleDetail detail : details) {
            for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                if (detail.getSaleorderGoodsId().equals(saleorderGoods.getSaleorderGoodsId())){
                    saleorderGoods.setBuyDockUserName(detail.getBuyDockUserName());
                    saleorderGoods.setBuyOrderDemand(detail.getBuyorderDemand());
                    saleorderGoods.setBuyProcessModTimeString(detail.getBuyProcessModTimeString());
                    saleorderGoods.setBindBuyOrderId(detail.getBuyorderNo());
                    saleorderGoods.setBuyorderStatus(detail.getBuyorderStatus());
                    //虚拟订单页面展示标记
                    saleorderGoods.setBindedBuyOrder(2);
                    break;
                }
            }
        }
    }

    @RequestMapping("/checkQuoteShare")
    @NoNeedAccessAuthorization
    @ResponseBody
    public ResultInfo checkQuoteShare(HttpServletRequest request,Integer quoteorderId){
        List<QuoteorderGoods> quoteorderGoodsList = quoteService.getQuoteGoodsByQuoteId(quoteorderId);
        QuoteorderGoods quoteorderGoods = quoteorderGoodsList.stream().filter(e -> e.getGoodsId().equals(0)).findFirst().orElse(null);
        if(!Objects.isNull(quoteorderGoods)){
            return ResultInfo.error("产品信息中存在未关联订货号的产品，请修改后再试");
        }
        return ResultInfo.success();
    }

    @RequestMapping("/shareOnline")
    @NoNeedAccessAuthorization
    public ModelAndView shareOnline(HttpServletRequest request,Integer quoteorderId){
        ModelAndView mv = new ModelAndView();
        mv.setViewName("order/quote/share_online");
        QuoteorderVo quoteordervo = quoteService.getQuoteOrderInfoById2(quoteorderId);
        mv.addObject("quoteordervo",quoteordervo);

        DateTimeFormatter format = DateTimeFormatter.ofPattern(DateUtil.DATE_FORMAT);
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime end = now.plusDays(13);
        mv.addObject("now",now.format(format));
        mv.addObject("end",end.format(format));
        mv.addObject("confirmOrderUrl",confirmOrderUrl);
        quoteService.updateOnlineSharetime(quoteorderId);
        return mv;
    }



    @RequestMapping("/showShareOnlineHist")
    @NoNeedAccessAuthorization
    public ModelAndView showShareOnlineHist(Integer saleorderId){
        ModelAndView mv = new ModelAndView();
        List<QuSaleHist> list =  quSaleHistMapper.getHistInfoBySaleorderId(saleorderId);
        for (QuSaleHist quSaleHist : list) {
            quSaleHist.setIsChangce(0);
            if (!quSaleHist.getSaleNum().equals(quSaleHist.getQuNum())){
                quSaleHist.setIsChangce(1);
                mv.addObject("orderChangce",1);
            }
        }
        mv.addObject("list",list);
        mv.setViewName("order/quote/share_online_hist");
        return mv;
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/batchContractFile")
    public ModelAndView batchContractFiel(@RequestParam(required = false) Integer saleorderId) {
        ModelAndView mv = new ModelAndView("vue/view/contract/uploadSaleContract");
        mv.addObject("creatername", CurrentUser.getCurrentUser().getUsername());
        mv.addObject("creater", CurrentUser.getCurrentUser().getId());
        mv.addObject("saleorderId", Objects.isNull(saleorderId) ? 0 : saleorderId);
        return mv;
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/initJdBatchContractFile")
    public ModelAndView initBatchContractFile(@RequestParam(required = false) Integer saleorderId) {
        ModelAndView mv = new ModelAndView("vue/view/contract/initJdUploadSaleContract");
        mv.addObject("creatername", CurrentUser.getCurrentUser().getUsername());
        mv.addObject("creater", CurrentUser.getCurrentUser().getId());
        //mv.addObject("saleorderId", Objects.isNull(saleorderId) ? 0 : saleorderId);
        return mv;
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/uploadJdContractReturnSave")
    @ResponseBody
    public ResultInfo uploadJdContractReturnSave(HttpServletRequest request, @RequestBody MultipartFile file){
        ResultInfo result =  saleorderService.checkJdBatchUpload(file.getOriginalFilename());
        if(!ErpConst.ZERO.equals(result.getCode())){
            return result;
        }
        FileInfo fileInfo = uploadImg(request,file);
        if(fileInfo.getCode() <0){
            return ResultInfo.error("文件上传失败");
        }
        Attachment attachment = new Attachment();
        attachment.setDomain(fileInfo.getDomain());
        attachment.setFilePath(fileInfo.getFilePath());
        attachment.setFileName(fileInfo.getFileName());
        attachment.setOssResourceId(fileInfo.getOssResourceId());
        attachment.setPrefix(fileInfo.getPrefix());
        ResultInfo result2 =  saleorderService.checkJdBatchUpload(fileInfo.getFileName());
        if(!ErpConst.ZERO.equals(result2.getCode())){
            return result2;
        }
        SaleorderInfoDto saleorderInfoDto = (SaleorderInfoDto)result.getData();

        //上传确认单
        List<OutboundBatchesRecode> outboundBatchesRecodes = expressService.getSaleOrderOutboundBatchesRecode(request.getSession(),saleorderInfoDto.getSaleorderId());
        List<FileInfo> fileInfoList =  new ArrayList<>();
        fileInfoList.add(fileInfo);
        ConfirmationFormRecode confirmationFormRecode = new ConfirmationFormRecode();
        confirmationFormRecode.setSaleOrderId(saleorderInfoDto.getSaleorderId());
        confirmationFormRecode.setBatchesRecodes(outboundBatchesRecodes);
        confirmationFormRecode.setFileInfos(fileInfoList);
        CurrentUser user = CurrentUser.getCurrentUser();
        log.info("开始上传确认单,订单id为:{}",confirmationFormRecode.getSaleOrderId());
        Boolean yn = confirmationFormRecodeService.uploadConfirmationForm(user,confirmationFormRecode,request);
        log.info("上传确认单完毕,订单id为:{}",confirmationFormRecode.getSaleOrderId());
        if (!yn){
            return ResultInfo.error("上传失败,当前用户不是销售或者物流人员");
        }

        //上传合同
        List<Attachment> attachmentList = new ArrayList<>();
        attachmentList.add(attachment);
        ResultInfo hetongResult =  saleorderService.contractReturnSave(CurrentUser.getCurrentUser().getId(), attachmentList,saleorderInfoDto.getSaleorderId());
        return hetongResult;
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/contractReturnSave")
    @ResponseBody
    @MethodLock(field = "saleorderId",className = SaleContractDto.class)
    public ResultInfo contractReturnSave(@RequestBody SaleContractDto saleContract){
        Integer saleorderId = saleContract.getSaleorderId();
        if(Objects.isNull(saleorderId)) {
            return ResultInfo.error("提交失败id为空");
        }
        List<Attachment> attachmentList = saleContract.getAttachmentList();
        attachmentList.removeAll(Collections.singleton(null));
        if(CollectionUtils.isEmpty(attachmentList)){
            return ResultInfo.error("提交失败附件为空");
        }

        return saleorderService.contractReturnSave(CurrentUser.getCurrentUser().getId(), attachmentList,saleorderId);
    }

    @Autowired
    private SyncOutInRelateApiService syncOutInRelateApiService;

    private ResultInfo returnContinue(String message){
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setCode(2);//需要继续请求
        resultInfo.setMessage(message);
        return resultInfo;
    }

    private ResultInfo returnNoMore(String message){
        ResultInfo resultInfo = new ResultInfo();
        resultInfo.setCode(3);
        resultInfo.setMessage(message);
        return resultInfo;
    }


    /**
     * 保存从另一个ERP(采购订单)推送过来的销售单
     * @param orderData
     * @return
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value ="/receiveSaleorderFromOtherErp", produces = "application/json;charset=UTF-8")
    @MethodLock(field = "originBuyOrderNo",className = OrderData.class)
    public ResultInfo saveYgyxBuyorderToErpSaleOrder(@RequestBody(required = false) OrderData orderData) {
        ResultInfo resultInfo = new ResultInfo<>();
        logger.info("SaleorderController->saveYgyxBuyorderToErpSaleOrder入参:" + JSONObject.fromObject(orderData).toString());
        if(StringUtils.isBlank(orderData.getPhone())) {
            resultInfo.setCode(-1);
            resultInfo.setMessage("注册手机号为空");
            return resultInfo;
        }

        Saleorder saveAddSaleorder;
        try {
            saveAddSaleorder = saleorderService.saveBDAddSaleorderForSyncHttp(orderData);
            resultInfo.setMessage("成功");
            resultInfo.setData(saveAddSaleorder);
            resultInfo.setCode(0);
        } catch (Exception e) {
            resultInfo.setCode(-1);
            resultInfo.setMessage("失败");
            e.printStackTrace();
        }
        return resultInfo;
    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/contractReturnSaveForOtherErp")
    @ResponseBody
    @MethodLock(field = "saleorderNo",className = SaleContractDto.class)
    public ResultInfo contractReturnSaveForOtherErp(@RequestBody SaleContractDto saleContract){
//        Integer saleorderId = saleContract.getSaleorderId();
//        if(Objects.isNull(saleorderId)) {
//            return ResultInfo.error("提交失败id为空");
//        }
        String saleorderNo = saleContract.getSaleorderNo();//这里接收的是外部的采购订单号，根据外部采购订单号查询到当前ERP对应的内部销售单号
        List<SyncOutInRelateDto> syncOutInRelateDtoList = syncOutInRelateApiService.selectByOutBusinessNo(saleorderNo);
        if(CollectionUtils.isEmpty(syncOutInRelateDtoList)){
            return ResultInfo.error("无此单号对应的销售单:"+saleorderNo);
        }
        List<Attachment> attachmentList = saleContract.getAttachmentList();
        if(CollectionUtils.isEmpty(attachmentList)){
//            return ResultInfo.error("提交失败附件为空");
            return returnNoMore("提交失败附件为空");
        }
        Saleorder saleorder = saleorderService.getBySaleOrderNo(syncOutInRelateDtoList.get(0).getInBusinessNo());
        //先判断不需要同步的情况
        //2. 当销售订单状态为"已关闭，已完结，存在审核中/已审核的合同"时，合同不需要上传至销售订单中
        if(Objects.isNull(saleorder)){
//            return ResultInfo.error("单据不存在："+syncOutInRelateDtoList.get(0).getInBusinessNo());
            return returnContinue("单据不存在："+syncOutInRelateDtoList.get(0).getInBusinessNo());
        }
        if(saleorder.getValidStatus().equals(ErpConst.ZERO)){
            return returnContinue("当前销售订单未生效，请稍后重试"+saleorder.getSaleorderNo());
        }
        if(saleorder.getStatus().equals(ErpConst.TWO) || saleorder.getStatus().equals(ErpConst.THREE)){
            //  `STATUS` tinyint(1) unsigned DEFAULT '0' COMMENT '订单状态：0待确认（默认）、1进行中、2已完结、3已关闭 4待用户确认',
            return returnNoMore("订单已完结或已关闭，"+saleorder.getSaleorderNo());
        }
        if(saleorder.getIsContractReturn()!=null && saleorder.getIsContractReturn().equals(1)){
            //  `IS_CONTRACT_RETURN` tinyint(4) DEFAULT '0' COMMENT '合同是否回传0否1是',
            return returnNoMore("合同已经回传过了，"+saleorder.getSaleorderNo());
        }
        return saleorderService.contractReturnSaveForOtherErp(attachmentList,saleorder.getSaleorderId());
    }

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/compenseOnlineConfirm")
    @ResponseBody
    public ResultInfo compenseOnlineConfirm(Integer id) {

        if (Objects.isNull(id)) {
            return ResultInfo.error("快递在线签收记录表id不可为空");
        }
        onlineInvoiceOpenService.compenseFile(id);
        return ResultInfo.success();
    }

    /**
     * 账期订单是否可发货
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/periodOrderCanDeliver")
    @ResponseBody
    public ResultInfo<?> periodOrderCanDeliver(Integer saleOrderId) {
        return ResultInfo.success(saleOrderApiService.periodOrderCanDeliver(saleOrderId));
    }

    /**
     * 获取订单状态信息（用于局部刷新）
     * @param saleOrderId 销售订单ID
     * @return 订单状态信息
     */
    @RequestMapping(value = "/getOrderStatusInfo", method = RequestMethod.GET)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<Map<String, Object>> getOrderStatusInfo(@RequestParam Integer saleOrderId) {
        try {
            // 获取最新的订单信息
            Saleorder saleorder = saleorderMapper.getSaleOrderById(saleOrderId);
            if (saleorder == null) {
                return ResultInfo.error("订单不存在");
            }

            Map<String, Object> result = new HashMap<>();

            // 基本状态信息
            result.put("orderStatus", saleorder.getStatus());
            result.put("validStatus", saleorder.getValidStatus());
            result.put("paymentStatus", saleorder.getPaymentStatus());
            result.put("deliveryStatus", saleorder.getDeliveryStatus());
            result.put("arrivalStatus", saleorder.getArrivalStatus());
            result.put("invoiceStatus", saleorder.getInvoiceStatus());
            BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.VS);
            // 获取流程节点信息
            List<SaleOrderProcessNode> processNodeList = saleOrderService.getSaleOrderProcessNodeList(null, saleOrderId);
            result.put("processNodeList", processNodeList);

            // 获取审核状态
            int orderCheckStatus = saleOrderService.getSaleorderCheckStatusFromVerifiesInfo("T_SALEORDER", saleOrderId, 623, saleorder);
            result.put("orderCheckStatus", orderCheckStatus);

            // 锁定状态信息
            result.put("lockedStatus", saleorder.getLockedStatus());
            result.put("lockedReason", saleorder.getLockedReason());

            return ResultInfo.success(result);
        } catch (Exception e) {
            log.error("获取订单状态信息失败", e);
            return ResultInfo.error("获取订单状态信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取销售订单自动关闭倒计时信息
     * @param saleOrderId 销售订单ID
     * @return 倒计时信息
     */
    @RequestMapping(value = "/getAutoCloseCountdown", method = RequestMethod.GET)
    @ResponseBody
    @NoNeedAccessAuthorization
    public ResultInfo<Map<String, Object>> getAutoCloseCountdown(@RequestParam Integer saleOrderId) {
        try {
            BaseSaleOrderService saleOrderService = saleOrderServiceFactory.createSaleOrderService(SalesOrderTypeEnum.BASE);
            Saleorder saleorder = saleOrderService.getSaleOrderBaseInfo(saleOrderId);

            Map<String, Object> result = new HashMap<>();

            if (saleorder == null) {
                result.put("showCountdown", false);
                return ResultInfo.success(result);
            }

            // 只有同时满足以下条件的订单才显示倒计时：
            // 1. 审核通过（validStatus = 1）
            // 2. 未付款（paymentStatus = 0，排除部分收款=1和全部收款=2）
            // 3. 未开票（invoiceStatus = 0，排除部分开票=1和全部开票=2）
            // 4. 订单状态为进行中（status = 1）
            if (saleorder.getValidStatus() != 1 ||
                saleorder.getPaymentStatus() != 0 ||
                saleorder.getInvoiceStatus() != 0 ||
                saleorder.getStatus() != 1) {
                result.put("showCountdown", false);
                return ResultInfo.success(result);
            }

            // 计算剩余时间：60天 - (当前时间 - 审核通过时间)
            long currentTime = System.currentTimeMillis();
            long validTime = saleorder.getValidTime(); // 审核通过时间
            long elapsedTime = currentTime - validTime;
            long sixtyDaysInMillis = 60L * 24 * 60 * 60 * 1000; // 60天的毫秒数
            long remainingTime = sixtyDaysInMillis - elapsedTime;

            result.put("showCountdown", true);
            result.put("remainingTime", remainingTime);
            result.put("validTime", validTime);
            result.put("currentTime", currentTime);

            return ResultInfo.success(result);
        } catch (Exception e) {
            logger.error("获取销售订单自动关闭倒计时信息失败，订单ID：{}", saleOrderId, e);
            return ResultInfo.error("获取倒计时信息失败");
        }
    }

}
