package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeNeedPayAdjustEntity;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayEntityDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;

import java.util.List;

/**
 * 金蝶 应付余额调整单 dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeNeedPayConvertor extends BaseMapStruct<KingDeeNeedPayAdjustEntity, KingDeeNeedPayDto> {

    /**
     * DTO转Entity
     *
     * @param dto KingDeeNeedPayDto
     * @return KingDeeNeedPayAdjustEntity
     */
    @Override
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "listToSting")
    KingDeeNeedPayAdjustEntity toEntity(KingDeeNeedPayDto dto);

    /**
     * entity转dto
     *
     * @param entity KingDeeNeedPayAdjustEntity
     * @return KingDeeNeedPayDto
     */
    @Override
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "stringToList")
    KingDeeNeedPayDto toDto(KingDeeNeedPayAdjustEntity entity);

    /**
     * jsonString转List<KingDeeNeedPayEntityDto>
     *
     * @param source jsonString
     * @return List<KingDeeNeedPayEntityDto>
     */
    @Named("stringToList")
    default List<KingDeeNeedPayEntityDto> stringToList(String source) {
        if (StrUtil.isEmpty(source)) {
            return null;
        }
        return JSON.parseArray(source, KingDeeNeedPayEntityDto.class);
    }

    /**
     * List<KingDeeNeedPayEntityDto>转jsonString
     *
     * @param source List<KingDeeNeedPayEntityDto>
     * @return String
     */
    @Named("listToSting")
    default String listToSting(List<KingDeeNeedPayEntityDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSON.toJSONString(source);
    }

    /**
     * 这个暂时用不到，由于dto和entity中第二层属性名字一样，因此必须重写BaseMapStruct的所有方法
     *
     * @param dto :
     * @param t   :
     * @return KingDeeNeedPayAdjustEntity
     */
    @Override
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "listToSting")
    KingDeeNeedPayAdjustEntity update(KingDeeNeedPayDto dto, @MappingTarget KingDeeNeedPayAdjustEntity t);

}

