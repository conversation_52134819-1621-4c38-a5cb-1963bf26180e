package com.vedeng.crm.visitrecord.service.impl;

import com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo;
import com.vedeng.crm.visitrecord.mapper.VisitTongxingUserMapper;
import com.vedeng.crm.visitrecord.service.VisitTongxingUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class VisitTongxingUserServiceImpl implements VisitTongxingUserService {

    @Autowired
    private VisitTongxingUserMapper visitTongxingUserMapper;

    @Override
    public List<VisitTongxingUserVo> getTongxingUserList(Integer recordId) {
        return visitTongxingUserMapper.selectTongxingUserByRecordId(recordId);
    }
} 