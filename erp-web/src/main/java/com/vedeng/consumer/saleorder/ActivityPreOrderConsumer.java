package com.vedeng.consumer.saleorder;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.MsgProducer;
import com.rabbitmq.RabbitConfig;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.saleorder.dao.ActivityPreOrderMapper;
import com.vedeng.erp.saleorder.domain.entity.ActivityPreOrderEntity;
import com.vedeng.goods.dto.CoreSkuDto;
import com.vedeng.goods.service.CoreSkuApiService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 科研购秒杀商机消费者
 * @date 2022/12/24 14:33
 */
@Component
@Slf4j
public class ActivityPreOrderConsumer extends AbstractMessageListener {

    @Autowired
    private ActivityPreOrderMapper activityPreOrderMapper;
    @Autowired
    private CoreSkuApiService coreSkuApiService;
    @Autowired
    private SaleorderService saleorderService;
    @Autowired
    private MsgProducer msgProducer;



    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        ActivityPreOrderConsumerDto activityPreOrderConsumerDto = null;
        try {
            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("科研购秒杀商机消费者：回传消息:[{}]", messageBody);
            activityPreOrderConsumerDto = JSON.parseObject(messageBody, ActivityPreOrderConsumerDto.class);
            if (activityPreOrderConsumerDto.getOperType() == 1) {
                ActivityPreOrderEntity activityPreOrderEntity = new ActivityPreOrderEntity();
                activityPreOrderEntity.setActiveId(activityPreOrderConsumerDto.getActiveId());
                activityPreOrderEntity.setCompanyName(activityPreOrderConsumerDto.getCompanyName());
                activityPreOrderEntity.setSku(activityPreOrderConsumerDto.getSkuNo());
                CoreSkuDto sku = coreSkuApiService.getCoreSkuDtoBySkuNo(activityPreOrderEntity.getSku());
                activityPreOrderEntity.setGoodsName(sku.getSkuName());
                activityPreOrderEntity.setGoodsId(sku.getSkuId());
                activityPreOrderEntity.setQuota(activityPreOrderConsumerDto.getLimitNum());
                activityPreOrderEntity.setOrderNo(activityPreOrderConsumerDto.getBusinessCode());
                activityPreOrderEntity.setRemark("秒杀活动商机：" + activityPreOrderEntity.getOrderNo());
                activityPreOrderEntity.setNum(activityPreOrderConsumerDto.getBuyNum());
                activityPreOrderEntity.setPrice(activityPreOrderConsumerDto.getLimitPrice());
                activityPreOrderEntity.setTag(1);
                activityPreOrderEntity.setContactPerson(activityPreOrderConsumerDto.getUserName());
                activityPreOrderEntity.setContactPhone(activityPreOrderConsumerDto.getMobile());
                activityPreOrderEntity.setOrderStatus(1);
                activityPreOrderMapper.insertSelective(activityPreOrderEntity);
            }
            if (activityPreOrderConsumerDto.getOperType() == 2) {
                ActivityPreOrderEntity old = activityPreOrderMapper.findByOrderNo(activityPreOrderConsumerDto.getBusinessCode());
                if (old != null ) {
                    old.setOrderStatus(4);
                    activityPreOrderMapper.updateByPrimaryKeySelective(old);
                    if (old.getSaleorderId() != null && old.getSaleorderId() != 0) {
                        // 关闭订单
                        Saleorder saleorder = new Saleorder();
                        saleorder.setStatus(3);
                        saleorder.setSaleorderId(old.getSaleorderId());
                        saleorder.setOptType("orderDetail");
                        Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);
                        if (((ErpConst.ZERO.equals(saleorderInfo.getStatus())
                                || ErpConst.ONE.equals(saleorderInfo.getStatus())
                                || ErpConst.FOUR.equals(saleorderInfo.getStatus()))
                                && ErpConst.ZERO.equals(saleorderInfo.getInvoiceStatus())
                                && ErpConst.ZERO.equals(saleorderInfo.getDeliveryStatus())
                                && ErpConst.ZERO.equals(saleorderInfo.getPaymentStatus())
                                && ErpConst.ZERO.equals(saleorderInfo.getLockedStatus())
                                && !ErpConst.ZERO.equals(saleorderInfo.getBhVerifyStatus()))
                               ) {
                            ResultInfo<?> resultInfo = saleorderService.closeOrder(saleorder);
                            log.info("关闭订单返回结果{}", JSON.toJSONString(resultInfo));
                        } else {
                            log.info("关闭订单失败，状态不正确{}", JSON.toJSONString(saleorderInfo));
                        }
                    } else {
                        StockInfoDto stockInfoDto = new StockInfoDto();
                        //消费消息时 做分支处理
                        stockInfoDto.setRelatedNo("ActivityPreOrder");
                        WarehouseDto dto = new WarehouseDto();
                        dto.setActionId(old.getActiveId());
                        dto.setSku(old.getSku());
                        dto.setOccupyNum(old.getNum());
                        stockInfoDto.setWarehouseStockList(CollUtil.newArrayList(dto));
                        String key = stockInfoDto.getRelatedNo() + "," + "update" + "#" + System.currentTimeMillis() + "#" + UUID.randomUUID().toString();
                        stockInfoDto.setMqOnlyKey(key);
                        String jsonRequest = JsonUtils.translateToJson(stockInfoDto);
                        //释放库存
                        log.info("start closeActivityPreOrder release stock {} {}:", key, jsonRequest);
                        try {
                            msgProducer.sendMsg(RabbitConfig.STOCK_SERVICE_EXCHANGE, RabbitConfig.STOCK_SERVICE_STOCKNUM_ROUTINGKEY, jsonRequest);
                        } catch (Exception e) {
                            log.error("error closeActivityPreOrder release stock {}:", key, e);
                            throw new RuntimeException("更新库存,发送消息异常");
                        }
                    }
                }
            }
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("科研购秒杀商机消费者：发生错误，回传消息：[{}]，错误信息：[{}]", JSON.toJSONString(activityPreOrderConsumerDto), e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (Exception ex) {
                log.error("科研购秒杀商机消费者：basicNack发生错误，回传消息：[{}]，错误信息：[{}]", JSON.toJSONString(activityPreOrderConsumerDto), e);
            }
        }
    }


    @NoArgsConstructor
    @Data
    private static class ActivityPreOrderConsumerDto {

        /**
         * 手机号
         */
        private String mobile;
        /**
         * skuNo
         */
        private String skuNo;
        /**
         * businessCode
         */
        private String businessCode;
        /**
         * 联系人公司名称
         */
        private String companyName;
        /**
         * 联系人名称
         */
        private String userName;
        /**
         * limitPrice
         */
        private BigDecimal limitPrice;
        /**
         * buyNum
         */
        private Integer buyNum;
        /**
         * limitNum
         */
        private Integer limitNum;
        /**
         * activeId
         */
        private Integer activeId;
        /**
         * addTime
         */
        private Date addTime;
        /**
         * operType "1新增2关闭"
         */
        private Integer operType;
    }

}
