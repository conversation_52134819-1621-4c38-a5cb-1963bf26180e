package com.vedeng.crm.business.quote.service.impl;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.bean.NoGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.trace.enums.BizLogEnum;
import com.vedeng.crm.business.quote.domain.dto.CrmQuoteOrderDto;
import com.vedeng.crm.business.quote.domain.entity.CrmQuoteOrderEntity;
import com.vedeng.crm.business.quote.mapper.CrmQuoteorderMapper;
import com.vedeng.crm.business.quote.mapstruct.CrmQuoteConvertor;
import com.vedeng.crm.business.quote.service.BusinessToQuoteService;
import com.vedeng.erp.system.dto.OperationLogDto;
import com.vedeng.erp.system.service.OperationLogApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class BusinessToQuoteServiceImpl implements BusinessToQuoteService {

    @Autowired
    private CrmQuoteorderMapper crmQuoteorderMapper;

    @Autowired
    private CrmQuoteConvertor crmQuoteConvertor;

    @Override
    public void createQuote(CrmQuoteOrderDto crmQuoteOrderDto, CurrentUser currentUser) {
        CrmQuoteOrderEntity entity = crmQuoteConvertor.toEntity(crmQuoteOrderDto);
        log.info("创建报价单：{}", JSON.toJSON(crmQuoteOrderDto));
        crmQuoteorderMapper.insertSelective(entity);
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.QUOTE_ORDER, NoGeneratorBean.builder().id(entity.getQuoteorderId()).numberOfDigits(9).build());
        String code = new BillNumGenerator().distribution(billGeneratorBean);

        CrmQuoteOrderEntity updateEntity = new CrmQuoteOrderEntity();
        updateEntity.setQuoteorderId(entity.getQuoteorderId());
        updateEntity.setQuoteorderNo(code);
        log.info("更新报价单号：{}", JSON.toJSON(updateEntity));
        crmQuoteorderMapper.updateByPrimaryKeySelective(updateEntity);

        crmQuoteOrderDto.setQuoteorderId(entity.getQuoteorderId());
        crmQuoteOrderDto.setQuoteorderNo(code);
    }
}
