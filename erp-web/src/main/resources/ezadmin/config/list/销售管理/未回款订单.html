<!doctype html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>(销售工作台)未回款订单</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
 </head>
 <body id="SSr__tjgjMc" datasource="erp-reportdatasource" fixednumber="1" fixednumberright="0" append_column_url="" append_row_url="" empty_show="" firstcol="">
  <div id="appendHead"></div>
  <div class="layui-fluid">
   <div class="layui-tab">
    <ul class="layui-tab-title" id="tab">
    </ul>
   </div>
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">订单号</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="19" name="SALEORDER_NO" placeholder="请输入" style="" alias="" jdbctype="VARCHAR" data="" datatype="" oper="LIKE" ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">逾期状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="OVERDUE_STATUS" placeholder="" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;未开始&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;未逾期&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;部分逾期&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;全部逾期&quot;,&quot;K&quot;:&quot;4&quot;}]" datatype="JSON" oper="" ></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">逾期天数</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="DAYS_OF_OVERDUE_FLAG" placeholder="" style="" alias="" jdbctype="NUMBER" data="[{&quot;V&quot;:&quot;0&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;0＜逾期≤60&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;逾期＞60&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""  ></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">归属销售</label>
     <div class="layui-input-inline">
      <xm class=" layui-input list-search-item " type="select" name="USER_ID" placeholder="" style="" alias="fina" jdbctype="NUMBER" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="JSON" oper="IN" ></xm> 
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th type="rowbutton" id="rowbutton"></th>
      <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-link" order="0" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${SALEORDER_NO}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单号</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_ID}" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">客户名称</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">归属销售</th>
      <th item_name="DELIVERY_DIRECT" name="DELIVERY_DIRECT" body="td-select" order="0" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;普发&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;直发&quot;,&quot;K&quot;:&quot;1&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">是否直发</th>
      <th item_name="TOTAL_AMOUNT" name="TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单原金额</th>
      <th item_name="REAL_TOTAL_AMOUNT" name="REAL_TOTAL_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">订单实际金额</th>
      <th item_name="USED_AMOUNT" name="USED_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">账期使用额</th>
      <th item_name="OCCUPY_AMOUNT" name="OCCUPY_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">账期占用额</th>
      <th item_name="RETURNED_AMOUNT" name="RETURNED_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">已归还账期额</th>
      <th item_name="UNRETURNED_AMOUNT" name="UNRETURNED_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">未归还账期额</th>
      <th item_name="OVERDUE_UNRETURNED_AMOUNT" name="OVERDUE_UNRETURNED_AMOUNT" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">逾期未还金额</th>
      <th item_name="OVERDUE_DAYS" name="OVERDUE_DAYS" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">逾期天数</th>
      <th item_name="OVERDUE_STATUS" name="OVERDUE_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未开始&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;未逾期&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;部分逾期&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;全部逾期&quot;,&quot;K&quot;:&quot;4&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">逾期状态</th>
      <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">发货状态</th>
      <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">开票状态</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="0" url="" opentype="" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">销售部门</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">创建时间</th>
      <th item_name="VALID_TIME" name="VALID_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" jdbctype="" head="th" edit_flag="" edit_express="" edit_plugin="" empty_show="">生效时间</th>
     </tr>
    </thead>
    <tbody>
     <tr>
      <td> express:</td>
      <td colspan="100"><pre id="express" class="layui-code" orderby="ORDER BY fina.ADD_TIME DESC" groupby="">StringBuilder sql=new StringBuilder("
SELECT
	* 
FROM
	(
	SELECT
		*,
	CASE
			
			WHEN OVERDUE_DAYS = 0 THEN
			1 
			WHEN 0 &lt; OVERDUE_DAYS 
			AND OVERDUE_DAYS &lt;= 60 THEN
				2 
				WHEN 60 &lt; OVERDUE_DAYS THEN
				3 
			END DAYS_OF_OVERDUE_FLAG 
FROM
	(
	SELECT
		TEMP.SALEORDER_ID,
		TEMP.SALEORDER_NO,
		TEMP.TRADER_NAME,TEMP.TRADER_ID,
		TEMP.USER_ID,
		TEMP.USERNAME,
		TEMP.DELIVERY_DIRECT,
		TEMP.TOTAL_AMOUNT,
		TEMP.REAL_TOTAL_AMOUNT,
		SUM( TEMP.UNRETURNED_AMOUNT ) UNRETURNED_AMOUNT,
		SUM( TEMP.USED_AMOUNT ) USED_AMOUNT,
		SUM( TEMP.OVERDUE_UNRETURNED_AMOUNT ) OVERDUE_UNRETURNED_AMOUNT,
		SUM( TEMP.OVERDUE_DAYS ) OVERDUE_DAYS,
		SUM( TEMP.OVERDUE_AMOUNT ) OVERDUE_AMOUNT,
		TEMP.DELIVERY_STATUS,
		TEMP.INVOICE_STATUS,
		TEMP.ORG_NAME,
		TEMP.ADD_TIME,
		TEMP.VALID_TIME,
		SUM( TEMP.RETURNED_AMOUNT ) RETURNED_AMOUNT,
		SUM( TEMP.OCCUPY_AMOUNT ) OCCUPY_AMOUNT,
	CASE
			
			WHEN OVERDUE_AMOUNT = 0 THEN
			2 
			WHEN OVERDUE_AMOUNT &gt; 0 
			AND USED_AMOUNT &gt; OVERDUE_AMOUNT THEN
				3 
				WHEN OVERDUE_AMOUNT = USED_AMOUNT THEN
				4 ELSE 1 
			END OVERDUE_STATUS 
FROM
	(
	SELECT
		S.SALEORDER_ID,
		S.SALEORDER_NO,
		S.TRADER_NAME,
		S.TRADER_ID,
		U.USER_ID,
		U.USERNAME,
		S.DELIVERY_DIRECT *1 AS DELIVERY_DIRECT  ,
		S.TOTAL_AMOUNT,
		S.REAL_TOTAL_AMOUNT,
		UD.UNRETURNED_AMOUNT,
		UD.AMOUNT USED_AMOUNT,
		SUM( IFNULL( IF ( OMD.OVERDUE_DAYS &gt; 0, OMD.UNRETURNED_AMOUNT, 0 ), 0 ) ) AS OVERDUE_UNRETURNED_AMOUNT,
		SUM( IFNULL( OMD.OVERDUE_DAYS, 0 ) ) OVERDUE_DAYS,
		SUM( OMD.OVERDUE_AMOUNT ) OVERDUE_AMOUNT,
		S.DELIVERY_STATUS *1 AS DELIVERY_STATUS ,
		S.INVOICE_STATUS *1 AS INVOICE_STATUS ,
		O.ORG_NAME,
		FROM_UNIXTIME( IF ( S.ADD_TIME = 0, NULL, S.ADD_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS ADD_TIME,
		FROM_UNIXTIME( IF ( S.VALID_TIME = 0, NULL, S.VALID_TIME ) / 1000, '%Y-%m-%d %H:%i:%s' ) AS VALID_TIME,
	IF
		( UD.OCCUPANCY = 0 AND UD.UNRETURNED_AMOUNT = 0, 1, 0 ) IS_CLOSED_ORDER,
		UD.AMOUNT - UD.UNRETURNED_AMOUNT RETURNED_AMOUNT,
	CASE
			UD.OCCUPANCY 
			WHEN 1 THEN
			UD.UNRETURNED_AMOUNT ELSE 0 
		END OCCUPY_AMOUNT 
FROM
	T_CUSTOMER_BILL_PERIOD_USE_DETAIL UD
	INNER JOIN T_SALEORDER S ON S.SALEORDER_ID = UD.RELATED_ID 
	AND S.COMPANY_ID = 1
	LEFT JOIN T_CUSTOMER_BILL_PERIOD BP ON UD.BILL_PERIOD_ID = BP.BILL_PERIOD_ID 
	AND BP.COMPANY_ID = 1
	LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL OMD ON UD.BILL_PERIOD_USE_DETAIL_ID = OMD.BILL_PERIOD_USE_DETAIL_ID 
	AND OMD.COMPANY_ID = 1
	LEFT JOIN T_SALEORDER_DATA SD ON SD.SALEORDER_ID = S.SALEORDER_ID
	LEFT JOIN T_ORGANIZATION O ON O.ORG_ID = SD.CURRENT_ORG_ID 
	LEFT JOIN T_USER U ON U.USER_ID = SD.CURRENT_USER_ID
WHERE
	UD.USE_TYPE = 1 
	AND UD.COMPANY_ID = 1 
	
GROUP BY
	BP.BILL_PERIOD_TYPE,
	UD.RELATED_ID 
HAVING
	IS_CLOSED_ORDER = 0 
	) TEMP 
GROUP BY
	TEMP.SALEORDER_NO 
HAVING
	UNRETURNED_AMOUNT &gt; 0 
	) res 
	) fina 
WHERE
	1 = 1 
	

");

if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " AND fina.USER_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}

list=search(sql);
return list;</pre></td>
     </tr>
     <tr>
      <td> count:</td>
      <td colspan="100"><pre id="count" class="layui-code"></pre></td>
     </tr>
    </tbody>
   </table>
  </div>
  <div id="appendFoot"></div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <script>
    layui.use(function () {

    })
</script>
 </body>
</html>