package com.test.kingdee;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressCostCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressReceiptCommand;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import com.vedeng.erp.kingdee.dto.KingDeeExpressReceiptDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressCostCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressCostConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeeExpressReceiptCommandConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeExpressCostMapper;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Objects;

/**
 * 快递签收单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeeExpressReceipt {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeeExpressReceiptCommandConvertor commandConvertor;


    @Test
    public void kingDeeExpressReceipt() {

        KingDeeExpressReceiptDto kingDeeExpressReceiptDto = KingDeeExpressReceiptDto.builder()
                .fid("0")
                .FBillNo(null)
                .FQzokOrgid(KingDeeConstant.ORG_ID.toString())
                .FQzokYsddh("BOF001")
                .FQzokGsywdh("BOF001")
                .FQzokCrkdh("CK2304141500018")
                .FQzokKdh("SF20230419")
                .FQzokYwlx("销售订单")
                .FQzokQssj("2023-04-19 09:59:59")
                .FQzokWlgs("顺丰")
                .FQzokWlbm("V514071")
                .FQzokXlh(null)
                .FQzokPch(null)
                .FQzokFhsl(BigDecimal.valueOf(1))
                .FQzokSjr("熊英翔")
                .FQzokDh("15366072261")
                .FQzokDz("江苏省南京市江宁区")
                .FQzokBddjbh("贝登单据编号")
                .FQzokSfsc(Boolean.FALSE.toString())
                .FQzokSfjrcb("Y")
                .build();

        KingDeeExpressReceiptCommand command = commandConvertor.toCommand(kingDeeExpressReceiptDto);


        System.out.println("入参："+JSON.toJSONString(command));
         kingDeeBaseApi.save(new SaveExtCommand<>(command,Boolean.FALSE, KingDeeFormConstant.QZOK_KDQSD));
    }
}
