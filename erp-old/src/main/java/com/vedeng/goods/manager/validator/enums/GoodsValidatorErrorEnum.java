package com.vedeng.goods.manager.validator.enums;

public enum GoodsValidatorErrorEnum {

    SkuNotUsingValidatorError(12001,"该SKU的启用状态不是已禁用，不能启用"),
    SkuUserValidatorError(12002,"只有供应链质量官可以启用该SKU"),
    SkuUsingSpuValidatorError(120003,"SPU的启用状态必须是已启用"),
    SpuNotUsingValidatorError(13001,"该SPU的启用状态不是已禁用，不能启用"),
    SpuUserValidatorError(13002,"只有供应链质量官可以启用该SPU"),
    ;
    private Integer code;

    private String message;

    GoodsValidatorErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
