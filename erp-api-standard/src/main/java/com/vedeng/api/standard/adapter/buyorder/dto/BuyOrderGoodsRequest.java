package com.vedeng.api.standard.adapter.buyorder.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购单商品请求DTO
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-18
 */
@Data
public class BuyOrderGoodsRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 采购商品ID
     */
    private Integer buyorderGoodsId;

    /**
     * 商品ID
     */
    private Integer goodsId;

    /**
     * 商品编码
     */
    private String goodsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 规格型号
     */
    private String specification;

    /**
     * 采购数量
     */
    private Integer quantity;

    /**
     * 采购单价
     */
    private BigDecimal unitPrice;

    /**
     * 总金额
     */
    private BigDecimal totalAmount;

    /**
     * 返利金额
     */
    private BigDecimal rebateAmount;

    /**
     * 返利价格
     */
    private BigDecimal rebatePrice;

    /**
     * 返利后价格
     */
    private BigDecimal rebateAfterPrice;

    /**
     * 返利数量
     */
    private String rebateNum;

    /**
     * 实际采购价格
     */
    private BigDecimal actualPurchasePrice;

    /**
     * 备注
     */
    private String remark;

}
