package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 分类与检查项目的中间表
 *
 * <AUTHOR>
 * @TableName V_CATEGORY_INSPECTION
 */
@Data
public class CategoryInspection extends BaseEntity {


    /**
     * 主键
     */
    private Integer id;

    /**
     * 商品分类ID，V_BASE_CATEGORY主键
     */
    private Integer categoryId;

    /**
     * 检查项目ID ，T_INSPECTION_ITEM主键
     */
    private Integer inspectionId;

}