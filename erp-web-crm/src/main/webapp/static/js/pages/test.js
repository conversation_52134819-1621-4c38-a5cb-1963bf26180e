void function () {
    new Vue({
        el: '#page-container',
        data: {
            inputValue: '',
            inputValue1: [],
            inputValue2: '',
            inputValue3: '',
            tableHeaders: [
                {
                    key: "accountName",
                    label: "名称",
                    width: "220px",
                    sortable: true,
                    filter: true
                },
                {
                    key: "companyName",
                    label: "公司名称",
                    width: "220px",
                    sortable: true,
                    filter: true
                },
                {
                    key: "positionName",
                    label: "职位",
                    width: "220px",
                },
                {
                    key: "level",
                    label: "等级",
                    width: "150px",
                    sortable: true,
                    filter: true,
                    sortType: 'SABC'
                },
                {
                    key: "roleName",
                    label: "角色",
                    width: "220px",
                },
                {
                    key: "mobile",
                    label: "手机号",
                    width: "220px",
                },
                {
                    key: "option",
                    label: "操作",
                    width: "220px",
                },
            ],
            tableList: [],
            isShowDialog: false,
            date1: "2024-07-16",
            date3: '', // ['2021-04-24', '2021-05-11']
            date4: '',
            date5: '',
            radio1: false,
            checkValue1: 1,
            radioList: [
                { label: "单选", value: 1 }, { label: "单选", value: 2 },
                { label: "单选", value: 3 }, { label: "单选", value: 4 },
                { label: "单选", value: 5 }, { label: "单选", value: 6 },
            ],
            checked1: false,
            checkboxList: [
                { label: "多选", value: 1 }, { label: "多选", value: 2 },
                { label: "多选", value: 3 }, { label: "多选", value: 4 },
                { label: "多选", value: 5 }, { label: "多选", value: 6 },
            ],
            progress: 1,
            checkValues2: [1,3,5],
            selectList1: [
                { label:'南京市', value:'1', name:'dassa' },
                { label:'无锡市', value:'2', name:'dassa' },
                { label:'徐州市', value:'4', name:'dassa' },
                { label:'常州市', value:'5', name:'dassa' },
                { label:'苏州市', value:'6', disabled: true },
                { label:'南通市', value:'7' },
                { label:'连云港市', value:'8' },
                { label:'淮安市', value:'9' },
                { label:'盐城市', value:'10' },
                { label:'扬州市', value:'11',}
            ],
            selectList2: [
                { label:'徐州市', value:'1' },
                { label:'常州市', value:'2' },
                { label:'苏州市', value:'3' },
                { label:'就是后面是打发撒市', value:'4' },
                { label:'杭州市', value:'5' },
                { label:'北京市', value:'6' },
            ],
            selectValue1: '2',
            selectValue2: [2,3,1],
            loading2: false,
            cascaderList: [
                {
                    label: '南京市',
                    value: '1',
                    children: [
                        {
                            label: '玄武区',
                            value: '11',
                            children: [
                                { label:'梅园新村街道', value:'111', children:[] },
                                { label:'新街口街道', value:'112' },
                                { label:'玄武门街道', value:'113' },
                                { label:'孝陵卫街道', value:'114' }
                            ]
                        },
                        {
                            label: '秦淮区',
                            value: '12',
                            children: [
                                { label:'秦虹街道', value: '123', },
                                { label:'夫子庙街道', value:'124' },
                                { label:'红花街道', value:'125' },
                                { label:'双塘街道', value:'126' }
                            ]
                        },
                        { label:'建邺区', value:'13' },
                        { label:'鼓楼区', value:'14' },
                        { label:'浦口区', value:'15' },
                    ]
                }, {
                    label:'无锡市', value: 2, children:[{
                        label: '无锡1区', value: '21',
                        children: [
                            { label:'无锡1街道', value:'211', children:[] },
                            { label:'无锡2街道', value:'212' },
                            { label:'无锡3街道', value:'213' },
                            { label:'无锡4街道', value:'214' }
                        ]
                    }]
                }, {
                    label: '宁夏回族自治区', value: 3, children: [{
                        label: '银川市', value: 31,
                        children: [
                            { label:'兴庆区', value: '311' },
                            { label:'吴忠市', value:'312' },
                        ]
                    },
                    {label: '石嘴山市', value: 32},
                    {label: '留山市', value: 33}]
                },
                {
                    label:'新疆维吾尔自治区', value: 4, children: [{
                        label: '巴音郭楞蒙古自治州', value: '41', children: [
                            { label: '焉耆回族自治县', value: '411' },
                            { label: '和静县', value: '412' },
                        ]
                    }, {
                        label: '博尔塔拉蒙古自治州', value: '42', children: [
                            { label: '焉耆回族自治县', value: '421' },
                            { label: '克孜勒苏柯尔克孜开发大手大脚', value: '422' },
                        ]
                    }, {
                        label: '克孜勒苏柯尔克孜自治州', value: '43', children: [
                            { label: '克孜勒苏柯尔克孜阿萨鲁德拉稍等分管生产稍等', value: '431' },
                            { label: '克孜勒苏柯尔克孜开得到大幅度发大手大脚', value: '432' },
                        ]
                    }] 
                },
                { label: '徐州市', value: 5, children:[] },
                { label: '常州市', value: 6, children:[]},
                {
                    label:'苏州市',
                    value: 7,
                    children:[ 
                        {
                            label:'姑苏', value: 71,
                            children: [
                                {label:'双塔街道',value:711,children:[]},
                                {label:'沧浪街道',value:712,children:[]},
                                {label:'平江街道',value:713,children:[]},
                                {label:'苏锦街道',value:714,children:[]},
                                {label:'虎丘街道',value:715,children:[]},
                            ]
                        },
                        {label:'虎丘',value:72,children:[]},
                        {label:'吴中',value:73,children:[]},
                        {label:'相城',value:74,children:[]},
                        {label:'吴江',value:75,children:[]},
                    ]
                },
                { label:'南通市', value:8, children:[] },
                { label:'连云港市', value: 9, children:[] },
                { label:'淮安市', value:10, children:[] },
                { label:'盐城市', value:11, children:[] },
                { label:'扬州市', value:12, children:[] },
                { label:'镇江市', value:13, children:[] },
                { label:'泰州市', value:14, children:[] },
                { label:'宿迁市', value:15, children:[] }
            ],
            cascaderValue: [1, 12, 123],
            cascaderValue2: '',
            defaultTab: [{
                label: '全部线索',
                id: 'all'
            }],
            listTotal: 0
        },
        mounted() {
            setTimeout(()=> {
                this.cascaderValue2 = {
                    level1: [3, 6],
                    level2: [12],
                    level3: [213]
                }
            }, 1000);
            console.log(this.$form)
            this.$form.rules({
                inputValue: {
                    required: '请选择XXX1'
                },
                inputValue1: {
                    required: '请选择XXX2'
                },
                inputValue2: {
                    required: '请选择XXX3'
                },
                inputValue3: {
                    required: '请选择XXX4'
                }
            }, 'busiForm', this)

            // this.$axios().then(() => {
            //     this.listTotal = 30
            // })

            this.listTotal = 30

            let tableList = [];
            for(let i=0;i<30;i++){
                tableList.push({
                    accountId: 292 + i,
                    mobile: "***********",
                    companyName: ['zhangsan', 'lisi', 'wanger', 'mazi'][i%4] + "集采分公司",
                    level: ['S', 'A', 'B', 'C'][i % 4],
                    companyOrgId: 595,
                    positionName: "总监",
                    positionId: 5,
                    deptOrgId: 714,
                    roleName: "测试2",
                    roleId: 582,
                    accountName: "测试"+i,
                    accountStatus: 1,
                    canOperateStatus: null,
                    authorityId: 5,
                    lineIds: "316,1008,317,318",
                    controlCompanyIdOrgIds: "",
                })
            }

            this.tableList = tableList
        },
        methods: {
            submit() {
                console.log(this.inputValue1)
                console.log(this.$form)
                this.$form.validForm('busiForm');
            },
            openDialog() {
                this.isShowDialog = true;
            },
            handlerChange(value) {
                console.log(value);
            },
            handleChange(news) {
                console.log(news)
            },
            selectChange2 (oldV, newV) {
                console.log(oldV, newV)
            },
            changeTyc (val) {
                console.log('天眼查:', val);
            },
            search () {
                console.log('搜索');
                this.loading2 = true;
                setTimeout(()=> {
                    this.loading2 = false;
                }, 1000)
            },
            edit(id){
                alert(id)
            },
            assignLeads(){
                let ids = this.$refs.listContainer.getSelectedData('accountId');

                if(!ids || !ids.length){
                    this.$message.warn('请选择需要操作的内容');
                }
            },
            handlerChange1 (val) {
                console.log('date:', val);
            },
            handlerDateChange (val) {
                console.log('val', val);
            },
            handlerDateChange2 (val) {
                console.log('val', val);
            },
            handlerDateChange3 (val) {
                console.log('change', val);
                console.log('date1', this.date1);
            },
        }
    })
}.call(this);