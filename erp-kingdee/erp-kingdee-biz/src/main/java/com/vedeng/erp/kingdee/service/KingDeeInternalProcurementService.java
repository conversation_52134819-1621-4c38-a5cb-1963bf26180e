package com.vedeng.erp.kingdee.service;

import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import com.vedeng.erp.kingdee.dto.result.KingDeeInternalProcurementQueryResultDto;
import com.vedeng.erp.kingdee.dto.result.KingDeePurchaseInitReceiptQueryResultDto;
import com.vedeng.infrastructure.kingdee.service.KingDeeBaseService;

import java.util.List;


public interface KingDeeInternalProcurementService extends KingDeeBaseService<KingDeeInternalProcurementDto> {

    /**
     * 获取金蝶内部采销
     * @param flowOrderId
     * @return
     */
    List<KingDeeInternalProcurementQueryResultDto> getKingDeeInternalProcurement(String flowOrderId);

}
