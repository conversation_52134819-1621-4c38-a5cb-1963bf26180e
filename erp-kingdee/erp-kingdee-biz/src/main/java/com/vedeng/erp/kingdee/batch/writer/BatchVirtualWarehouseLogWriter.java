package com.vedeng.erp.kingdee.batch.writer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchRInvoiceDetailJOperateLogDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class BatchVirtualWarehouseLogWriter extends BaseWriter<BatchVirtualWarehouseLogDto> {

    @Autowired
    private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;
    @Autowired
    private BatchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper;

    @Override
    public void doWrite(BatchVirtualWarehouseLogDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("红票生成虚拟出入库 写入:{},", JSON.toJSONString(item));
        batchWarehouseGoodsOutInDtoMapper.insertSelective(item.getBatchWarehouseGoodsInDto());
        batchWarehouseGoodsOutInDtoMapper.insertSelective(item.getBatchWarehouseGoodsOutDto());
        for (BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto : item.getBatchWarehouseGoodsInItemDtoList()) {
            batchWarehouseGoodsOutInItemDtoMapper.insertSelective(batchWarehouseGoodsOutInItemDto);
        }

        for (BatchWarehouseGoodsOutInItemDto batchWarehouseGoodsOutInItemDto : item.getBatchWarehouseGoodsOutItemDtoList()) {
            batchWarehouseGoodsOutInItemDtoMapper.insertSelective(batchWarehouseGoodsOutInItemDto);
        }

        List<BatchRWarehouseGoodsOutJWarehouseGoodsInDto> warehouseGoodsOutJWarehouseGoodsInDto = batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.findByWarehouseGetBatchRWarehouseGoodsOutJWarehouseGoodsInDto(
                item.getBatchWarehouseGoodsInDto().getRelateNo(),
                item.getBatchWarehouseGoodsOutDto().getRelateNo());
        warehouseGoodsOutJWarehouseGoodsInDto.forEach(dto -> {
            dto.setAddTime(new Date());
            dto.setCreator(ErpConstant.TWO);
            dto.setCreatorName("njadmin");
            dto.setRelationType(ErpConstant.ONE);
            batchRWarehouseGoodsOutJWarehouseGoodsInDtoMapper.insertSelective(dto);
        });


    }
}
