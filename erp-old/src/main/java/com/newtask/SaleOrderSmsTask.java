package com.newtask;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.logistics.service.WarehouseOutService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Title: ${file_name}
 * @Package ${package_name}
 * @Description: 销售单发货自动短信通知
 * @date 2021/9/411:27
 */
@JobHandler(value = "saleOrderSmsTask")
@Component
public class SaleOrderSmsTask extends AbstractJobHandler {

    Logger logger = LoggerFactory.getLogger(SaleOrderSmsTask.class);

    @Autowired
    private WarehouseOutService warehouseOutService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {

        XxlJobLogger.log("SaleOrder发货短信通知start-----------");
        logger.info("SaleOrder发货短信通知start-----------");
        warehouseOutService.sendDeliveryShortMessage();
        logger.info("SaleOrder发货短信通知start-----------");
        XxlJobLogger.log("SaleOrder发货短信通知start-----------");
        return SUCCESS;
    }

}
