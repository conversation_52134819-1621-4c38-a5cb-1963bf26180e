package com.newtask.finance;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;
import com.vedeng.erp.finance.enums.InvoiceRedConfirmationEvent;
import com.vedeng.erp.finance.service.InvoiceRedConfirmationService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 数电发票-批量更新本地红字确认单定时任务
 * @date 2023/9/20 11:50
 */
@JobHandler("UpdateInvoiceRedConfirmationTask")
@Component
@Slf4j
public class UpdateInvoiceRedConfirmationTask extends AbstractJobHandler {

    @Autowired
    InvoiceRedConfirmationService invoiceRedConfirmationService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        //1.分页获取本地进行中的红字确认单 初始化，已申请，已确认
        int pageSize = 100;
        int pageNum = 1;
        PageParam<InvoiceRedConfirmationDto> page = new PageParam<>();
        InvoiceRedConfirmationDto invoiceRedConfirmationDto = new InvoiceRedConfirmationDto();
        invoiceRedConfirmationDto.setRedConfirmationStatusList(Arrays.asList( 1, 2));
        page.setPageSize(pageSize);
        page.setPageNum(pageNum);
        page.setParam(invoiceRedConfirmationDto);
        PageInfo<InvoiceRedConfirmationDto> pages = invoiceRedConfirmationService.page(page,false);
        pageNum = pages.getPages();
        while (true) {
            log.info("分页执行进度 {},{}", pageSize, pageNum);

            PageParam<InvoiceRedConfirmationDto> pageDesc = new PageParam<>();
            pageDesc.setPageSize(pageSize);
            pageDesc.setPageNum(pageNum);
            pageDesc.setParam(invoiceRedConfirmationDto);
            PageInfo<InvoiceRedConfirmationDto> result = invoiceRedConfirmationService.page(pageDesc,false);

            List<InvoiceRedConfirmationDto> list = result.getList();
            if (list.isEmpty()) {
                break;
            }
            list.forEach(x-> {
                try {
                    InvoiceRedConfirmationDto data = invoiceRedConfirmationService.getInvoiceRedConfirmationDto(x.getInvoiceRedConfirmationId());
                    //2.调用状态机
                    invoiceRedConfirmationService.executeInvoiceRedConfirmationOperaMachine(data, InvoiceRedConfirmationEvent.RENEW);
                }catch (ServiceException e1){
                    log.warn("数据：{} UpdateInvoiceRedConfirmationTask.executeInvoiceRedConfirmationOperaMachine 执行失败", JSON.toJSONString(x),e1);
                }
                catch (Exception e) {
                    log.error("数据：{} UpdateInvoiceRedConfirmationTask.executeInvoiceRedConfirmationOperaMachine 执行失败", JSON.toJSONString(x),e);
                }
            });

            if (!result.isHasPreviousPage()) {
                break;
            }
            pageNum = result.getPrePage();
            log.info("分页查询进度 {},{}", pageSize, pageNum);
        }


        return ReturnT.SUCCESS;
    }

}
