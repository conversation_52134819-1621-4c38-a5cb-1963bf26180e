package com.vedeng.erp.system.enums;

import com.vedeng.erp.finance.dto.ValueLabelDto;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 合同状态枚举
 *
 * <AUTHOR>
 * @date 2023-07-01
 */
@Getter
@AllArgsConstructor
public enum ContractStatusEnum {

    /**
     * 无需上传（末级节点为贝登子公司）
     */
    NO_NEED_UPLOAD(0, "无需上传"),

    /**
     * 未上传（末级节点非贝登子公司且未上传合同）
     */
    NOT_UPLOADED(1, "未上传"),

    /**
     * 已上传（末级节点非贝登子公司且已上传合同）
     */
    UPLOADED(2, "已上传");

    /**
     * 状态码
     */
    private final Integer value;

    /**
     * 状态描述
     */
    private final String label;

}
