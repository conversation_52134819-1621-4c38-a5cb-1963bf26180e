package com.vedeng.erp.trader.domain.entity;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

/**
 * T_COMMUNICATE_SUMMARY
 * <AUTHOR>
@Data
public class CommunicateSummaryEntity implements Serializable {
    /**
     * 主键
     */
    private Long communicateSummaryId;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 修改时间
     */
    private Date modTime;

    /**
     * 客户意图
     */
    private String customerIntentions;

    /**
     * 意向商品
     */
    private String intentionGoods;

    /**
     * 品牌
     */
    private String brands;

    /**
     * 型号
     */
    private String models;

    /**
     * 客户类型
     */
    private String customerTypes;

    /**
     * 是否有意向
     */
    private String isIntention;

    /**
     * 是否加微信
     */
    private String isAddWechat;

    /**
     * 是否有效沟通
     */
    private String isEffectiveCommunication;

    /**
     * T_COMMUNICATE_RECORD表的ID
     */
    private Integer communicateRecordId;

    /**
     * 修改历史
     */
    private JSONObject updateLog;

    private static final long serialVersionUID = 1L;
}