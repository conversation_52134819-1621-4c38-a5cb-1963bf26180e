package com.vedeng.erp.aftersale.mapstruct;

import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.dto.BuyOrderAfterSalesDto;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/28 13:36
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface AfterSalesConvert extends BaseMapStruct<AfterSales, BuyOrderAfterSalesDto> {
}