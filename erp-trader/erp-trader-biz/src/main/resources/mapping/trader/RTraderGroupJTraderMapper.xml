<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.RTraderGroupJTraderMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.RTraderGroupJTraderEntity">
    <id column="R_TRADER_GROUP_J_TRADER" jdbcType="BIGINT" property="rTraderGroupJTrader" />
    <result column="TRADER_GROUP_ID" jdbcType="INTEGER" property="traderGroupId" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
  </resultMap>
  <sql id="Base_Column_List">
    R_TRADER_GROUP_J_TRADER, TRADER_GROUP_ID, TRADER_ID
  </sql>
  <select id="getByTraderGroupIdAndTraderId" resultType="com.vedeng.erp.trader.domain.entity.RTraderGroupJTraderEntity">
    select
    <include refid="Base_Column_List" />
    from T_R_TRADER_GROUP_J_TRADER
    where TRADER_GROUP_ID = #{traderGroupId,jdbcType=INTEGER} and TRADER_ID = #{traderId,jdbcType=INTEGER}
    limit 1
  </select>

</mapper>