package com.vedeng.infrastructure.kingdee.common.sdk.entity;

/* loaded from: k3cloud-webapi-sdk7.9.2.jar:com/kingdee/bos/webapi/entity/ProxyCfg.class */
public class ProxyCfg {
    String scheme = "http";
    String host;
    int port;

    public ProxyCfg(String host, int port) {
        this.host = "127.0.0.1";
        this.port = 8888;
        this.host = host;
        this.port = port;
    }

    public String getScheme() {
        return this.scheme;
    }

    public void setScheme(String scheme) {
        this.scheme = scheme;
    }

    public String getHost() {
        return this.host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return this.port;
    }

    public void setPort(int port) {
        this.port = port;
    }
}
