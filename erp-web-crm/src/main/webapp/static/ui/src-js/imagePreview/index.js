Vue.component('ui-image-preview', {
    template: `<div class="vd-ui-imgView-container" v-if="visible">
        <!-- 黑色背景 -->
        <div class="vd-ui-black-bg"></div>

        <!-- close btn -->
        <div class="vd-ui-btn vd-ui-close" @click="close">
            <i class="vd-ui_icon icon-delete"></i>
        </div>

        <!-- left arrow -->
        <div class="vd-ui-btn vd-ui-arrow-left" @click="preventImg">
            <i class="vd-ui_icon icon-app-left"></i>
        </div>

        <!-- right arrow -->
        <div class="vd-ui-btn vd-ui-arrow-right" @click="nextImg">
            <i class="vd-ui_icon icon-app-right"></i>
        </div>

        <!-- feature btn -->
        <div class="vd-ui-btn vd-ui-feature">
            <div class="feature-btn" @click="handleActions('zoomIn')">
                <i class="vd-ui_icon icon-see"></i>
            </div>
            <div class="feature-btn" @click="handleActions('zoomOut')">
                <i class="vd-ui_icon icon-zoomout"></i>
            </div>
            <div class="feature-btn" @click="toggleMode">
                <i v-show="fullScreen" class="vd-ui_icon icon-unfull-screen"></i>
                <i v-show="!fullScreen" class="vd-ui_icon icon-full-screen"></i>
            </div>
            <div class="feature-btn" @click="handleActions('rotateLeft')">
                <i class="vd-ui_icon icon-turnleft"></i>
            </div>
            <div class="feature-btn" @click="handleActions('rotateRight')">
                <i class="vd-ui_icon icon-turnright"></i>
            </div>
        </div>

        <!-- 图片展示 -->
        <div class="vd-ui-img-view" @click.self="close">
            <template v-for="(url, i) in urlList">
                <img
                    v-if="i === current"
                    ref="img"
                    class="img-viewer"
                    :key="currentImg + i"
                    :src="currentImg"
                    :style="imgStyle"
                    @load="handleImgLoad"
                    @error="handleImgError"
                    @mousedown="handleMouseDown"
                >
            </template>

            <div class="vd-ui-cover-img" v-show="isError">
                <span class="error">
                    <i class="vd-ui_icon icon-fail"></i>
                    <p>图片加载失败</p>
                </span>
            </div>

            <div class="vd-ui-cover-img" v-show="loading">
                <span class="loading">
                    <i class="vd-ui_icon icon-loading"></i>
                    <p>图片加载中...</p>
                </span>
            </div>
        </div>

    </div>`,
    props: {
        // 图片数组
        urlList: {
            type: Array,
            default(){
                return []
            }
        },
        // 默认展示下标
        init: {
            type: Number,
            default: 0
        },
        // 是否显示组件
        visible: Boolean,

        // 传进来的图片数组格式：true: 纯图片数组   false: 对象数组
        pureArray: {
            type: Boolean,
            default: false
        },
        // 当pureArray为true时，图片字段取值key名
        showKey: {
            type: String,
            default: 'pic'
        },
        onChange: {
            type: Function,
            default: () => { }
        },
        onClose: {
            type: Function,
            default: () => { }
        }
    },
    data() {
        return {
            isError: false,  // 是否失败
            loading: true,  // 图片是否加载完
            current: this.init,    // 由于传进来的prop不能改，所以另设current接收初始化下标
            fullScreen: false,  // 是否全屏
            transform: {
                scale: 1,
                deg: 0,
                offsetX: 0,
                offsetY: 0,
                enableTransition: false
            },
            mousewheelEventName: !!window.navigator.userAgent.match(/firefox/i) ? 'DOMMouseScroll' : 'mousewheel'
        }
    },
    computed: {
        currentImg() {
            if (this.pureArray) {
                return this.urlList[this.current];
            } else {
                let imgObj = this.urlList[this.current] || {};
                return imgObj[this.showKey];
            }
        },
        imgStyle() {
            const { scale, deg, offsetX, offsetY, enableTransition } = this.transform;
            const style = {
                transform: `scale(${scale}) rotate(${deg}deg)`,
                transition: enableTransition ? 'transform .3s' : '',
                'margin-left': `${offsetX}px`,
                'margin-top': `${offsetY}px`
            };
            if (!this.fullScreen) {
                style.maxWidth = style.maxHeight = '100%';
            } else {
                style.maxWidth = style.maxHeight = 'none';
            }
            return style;
        },
    },
    watch: {
        visible(val) {
            if (val) {
                this.initData();
            } else {  // 关闭后清空所有事件
                this.clearEvent();
            }
        },
        current: {
            handler: function (val) {
                this.reset();
                this.onChange(val);
            }
        },
        // 图片切换时，loading状态
        currentImg(val) {
            this.$nextTick(_ => {
                const $img = this.$refs.img[0];
                if (!$img.complete) {
                    this.loading = true;
                }
            });
        }
    },
    mounted() {
        if (this.visible) {
            this.initData();
        }
    },
    methods: {
        initData() {
            this.current = this.init;
            this.eventWatch();
        },
        // 监听事件 【按键/滚轮】
        eventWatch() {
            var that = this;
            // 按键事件
            this._keyDownHandler = function (e) {
                e.stopPropagation();
                var keyCode = e.keyCode;
                switch (keyCode) {
                    // ESC
                    case 27:
                        that.close();
                        break;
                    // 空格
                    case 32:
                        that.toggleMode();
                        break;
                    // up arrow
                    case 38:
                        that.handleActions('zoomIn');
                        break;
                    // down arrow
                    case 40:
                        that.handleActions('zoomOut');
                        break;
                    // left arrow
                    case 37:
                        that.preventImg();
                        break;
                    // right arrow
                    case 39:
                        that.nextImg();
                        break;
                }
            };

            // 滚轮事件
            this._mouseWheelHandler = this.rafThrottle(e => {
                console.log('e', e);
                const delta = e.wheelDelta ? e.wheelDelta : -e.detail;
                console.log(delta);
                if (delta > 0) {
                    this.handleActions('zoomIn', {
                        zoomRate: 0.05,
                        enableTransition: false
                    });
                } else {
                    this.handleActions('zoomOut', {
                        zoomRate: 0.05,
                        enableTransition: false
                    });
                }
            });
            this.on(document, 'keydown', this._keyDownHandler);
            this.on(document, this.mousewheelEventName, this._mouseWheelHandler);
        },
        // 拖动图片
        handleMouseDown(e) {
            console.log('====e=====>', e, e.button);
            if (this.loading || e.button !== 0) return;

            const { offsetX, offsetY } = this.transform;
            const startX = e.pageX;
            const startY = e.pageY;
            this._dragHandler = this.rafThrottle(ev => {
                this.transform.offsetX = offsetX + ev.pageX - startX;
                this.transform.offsetY = offsetY + ev.pageY - startY;
            });
            this.on(document, 'mousemove', this._dragHandler);
            this.on(document, 'mouseup', ev => {
                this.off(document, 'mousemove', this._dragHandler);
            });

            e.preventDefault();
        },
        // 放大/缩小/旋转图片
        handleActions(action, options = {}) {
            if (this.loading) return;
            const { zoomRate, rotateDeg, enableTransition } = {
                zoomRate: 0.2,
                rotateDeg: 90,
                enableTransition: true,
                ...options
            };
            const { transform } = this;
            switch (action) {
                case 'zoomOut':
                    if (transform.scale > 0.2) {
                        transform.scale = parseFloat((transform.scale - zoomRate).toFixed(3));
                    }
                    break;
                case 'zoomIn':
                    transform.scale = parseFloat((transform.scale + zoomRate).toFixed(3));
                    break;
                case 'rotateLeft':
                    transform.deg -= rotateDeg;
                    break;
                case 'rotateRight':
                    transform.deg += rotateDeg;
                    break;
            }
            transform.enableTransition = enableTransition;
        },
        // 上一张
        preventImg() {
            if (this.loading) return;
            if (this.current > 0) {
                this.current--;
            } else if (this.current == 0) {
                this.current = this.urlList.length - 1;
            }
        },
        // 下一张
        nextImg() {
            if (this.loading) return;
            if (this.current < this.urlList.length - 1) {
                this.current++;
            } else if (this.current == this.urlList.length - 1) {
                this.current = 0;
            }
        },
        // 切换显示模式
        toggleMode() {
            if (this.loading) return;
            this.fullScreen = !this.fullScreen;
            this.reset();
        },
        // 图片加载完毕
        handleImgLoad(e) {
            this.loading = false;
        },
        handleImgError(e) {
            this.loading = false;
            this.isError = true;
            e.target.alt = '图片加载失败';
            e.target.src = '/static/image/transparent.png';
        },
        // 重置
        reset() {
            this.isError = false;
            this.transform = {
                scale: 1,
                deg: 0,
                offsetX: 0,
                offsetY: 0,
                enableTransition: false
            };
        },
        close() {
            this.onClose();
            this.$emit('update:visible', false);
        },
        // 清空所有状态及事件
        clearEvent() {
            this.fullScreen = false;
            this.loading = false;
            this.off(document, 'keydown', this._keyDownHandler);
            this.off(document, this.mousewheelEventName, this._mouseWheelHandler);
            this._keyDownHandler = null;
            this._mouseWheelHandler = null;
        },
        on(element, event, handler) {
            if (document.addEventListener) {
                if (element && event && handler) {
                    element.addEventListener(event, handler, false);
                }
            } else {
                if (element && event && handler) {
                    element.attachEvent('on' + event, handler);
                }
            }
        },
        off(element, event, handler) {
            if (document.removeEventListener) {
                if (element && event) {
                    element.removeEventListener(event, handler, false);
                }
            } else {
                if (element && event) {
                    element.detachEvent('on' + event, handler);
                }
            }
        },
        rafThrottle(fn) {
            let locked = false;
            return function (...args) {
                if (locked) return;
                locked = true;
                //  window.requestAnimationFrame 方法接受一个函数为参数，该函数会在浏览器下一次重绘前调用，对比setTimeOut 和setInterval，它是更节流的，用这个特性结合递归来更好的操作动画。
                window.requestAnimationFrame(_ => {
                    fn.apply(this, args);
                    locked = false;
                });
            };
        }
    }
})