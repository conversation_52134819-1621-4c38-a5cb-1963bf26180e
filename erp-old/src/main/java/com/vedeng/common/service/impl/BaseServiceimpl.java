package com.vedeng.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.fasterxml.jackson.core.type.TypeReference;
import com.vedeng.authorization.dao.OrganizationMapper;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.*;
import com.vedeng.common.http.HttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.model.TemplateVar;
import com.vedeng.common.model.vo.ReqTemplateVariable;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.shiro.cas.CasClientHelper;
import com.vedeng.common.util.*;
import com.vedeng.finance.dto.HxInvoiceSearchDTO;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.passport.api.wechat.dto.res.ResWeChatDTO;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.trader.dao.RTraderGroupJTraderMapper;
import com.vedeng.trader.dao.TraderFinanceMapper;
import com.vedeng.trader.dao.TraderMapper;
import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.RTraderGroupJTrader;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.TraderFinance;
import com.vedeng.trader.model.dto.TraderBaseInfoDto;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.editor.language.json.converter.util.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

@Service("baseService")
public class BaseServiceimpl implements BaseService {
	protected String httpUrl = "http://qa.godbcenter.ivedeng.com/api/";

	@Value("${client_id}")
	protected String clientId;

	@Value("${client_key}")
	protected String clientKey;
	@Value("${orderIsGoodsPeer_Time}")
	protected Long orderIsGoodsPeerTime;

	@Value("${oss_url}")
	protected String picUrl;

	@Value("${file_url}")
	protected String fileUrl;


	@Value("${redis_dbtype}")
	protected String dbType;// 开发redis，测试redis
	@Value("${stock_url}")
	protected String stockUrl;

	// #########kingdlee#########
	@Value("${kingdlee_key}")
	protected String kingdleeKey;// 密钥
	@Value("${kingdlee_receamount_url}")
	protected String kingdleeReceamountUrl;// 收款
	@Value("${kingdlee_openinvoice_url}")
	protected String kingdleeOpeninvoiceUrl;// 开票
	@Value("${kingdlee_payamount_url}")
	protected String kingdleePayamountUrl;// 付款
	@Value("${kingdlee_receinvoice_url}")
	protected String kingdleeReceinvoiceUrl;// 收票
	// #########kingdlee#########

	@Value("${inquiry_value}")
	protected String inquiryValue;

	@Value("${call_url}")
	protected String callUrl;

	@Value("${call_namespace}")
	protected String callNamespace;

	@Value("${api_url}")
	protected String apiUrl;

	@Value("${rest_db_url}")
	protected String restDbUrl;

	@Value("${mjx_url}")
	protected String mjxUrl;

	@Value("${oss_url}")
	protected String ossUrl;

	@Value("${logincal_orga_per}")
	protected String logincalOrganizationPermission;
	/**
	 * 消息中心地址
	 */
	@Value("${message_center_url}")
	protected String messageCenterUrl;

	@Value("${price.url}")
	protected String priceUrl;

	@Value("${crm_url}")
	protected String crmUrl;

	@Value("${track_url}")
	protected String trackUrl;

	@Value("${operate_url}")
	protected String operateUrl;

	@Value("${erp_url}")
	protected String erpUrl;

	@Value("${risk_isCheck}")
	protected String isRiskcheck;

	@Value("${saleorder_warn_email}")
	protected String saleOrderWarnEmail;

	@Resource
	private RegionMapper regionMapper;
	@Resource
	protected UserMapper userMapper;
	@Resource
	private OrganizationMapper organizationMapper;
	@Resource
	private SysOptionDefinitionMapper sysOptionDefinitionMapper;

	@Resource
	private BuyorderMapper buyorderMapper;
	@Resource
	private BuyorderGoodsMapper buyorderGoodsMapper;


	@Resource
	private TraderMapper traderMapper;

	@Resource
	private RTraderGroupJTraderMapper rTraderGroupJTraderMapper;

	@Resource
	private SaleorderMapper saleorderMapper;

	@Resource
	private TraderFinanceMapper traderFinanceMapper;

	@Value("${printOut_trader}")
	protected  String printOutTrader;

	protected  static int cacheSecond=3600;

	@Value("${shipmentToRemind}")
	protected String shipmentToRemind;//发货提醒模板id

	@Value("${vx_service}")
	protected String vxService;

	@Value("${kyg_vx_service}")
	protected String kygVxService;
	/**
	 * 科研购发货物流信息推送模板id
	 */
	@Value("${kygDeliveryReminder}")
	protected String kygDeliveryReminder;
	/**
	 * @Fields logger : TODO日志
	 */
	public Logger logger = LoggerFactory.getLogger(this.getClass());


	/**
	 * <b>Description:</b><br>
	 * 获取地址
	 *
	 * @param areaId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年5月16日 下午5:30:03
	 */
	public String getAddressByAreaId(Integer areaId) {
		if (ObjectUtils.isEmpty(areaId)) {
			return null;
		}
		Region re = getRegionByAreaId(areaId);
		StringBuffer sb = new StringBuffer();
		if(re != null) {
			if (re.getRegionType() == 3) {
				String zoneName = re.getRegionName();
				re = getRegionByAreaId(re.getParentId());
				String cityName ="";
				String provName="";
				if (re!=null) {
					cityName = re.getRegionName();
					re = getRegionByAreaId(re.getParentId());
					provName = re.getRegionName();
				}
				sb.append(provName).append(" ").append(cityName).append(" ").append(zoneName);
			} else if (re.getRegionType() == 2) {
				String cityName = re.getRegionName();
				re = getRegionByAreaId(re.getParentId());
				String provName = re.getRegionName();
				sb.append(provName).append(" ").append(cityName);
			} else {
				String provName = re.getRegionName();
				sb.append(provName);
			}
		}
		return sb.toString();
	}

	/**
	 * <b>Description:</b><br>
	 * 获取当前地区
	 *
	 * @param areaId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年10月26日 下午2:05:46
	 */
	private Region getRegionByAreaId(Integer areaId) {
		Region region = null;
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + areaId)) {
			String json = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + areaId);
			if (json == null || "".equals(json)) {
				return null;
			}
			JSONObject jsonObject = JSONObject.fromObject(json);
			region = (Region) JSONObject.toBean(jsonObject, Region.class);
		} else {
			region = regionMapper.getRegionById(areaId);
			if (region != null) {
				JedisUtils.set(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + areaId,
						JsonUtils.convertObjectToJsonStr(region), ErpConst.ZERO);
			}
		}
		return region;
	}

	/**
	 * <b>Description:</b><br>
	 * 查询用户名
	 *
	 * @param userId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年8月25日 上午10:59:41
	 */
	protected String getUserNameByUserId(Integer userId) {
		if (ObjectUtils.notEmpty(userId)) {
			User us = userMapper.selectByPrimaryKey(userId);
			if (us == null) {
				return null;
			} else {
				return us.getUsername();
			}
		} else {
			return null;
		}
	}

	/**
	 * <b>Description:</b><br>
	 * 查询部门名称
	 *
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年8月25日 上午10:59:41
	 */
	protected String getOrgNameByOrgId(Integer orgId) {
		if (ObjectUtils.notEmpty(orgId)) {
			Organization org = organizationMapper.selectByPrimaryKey(orgId);
			if (org == null) {
				return null;
			} else {
				return org.getOrgName();
			}
		} else {
			return null;
		}
	}

	/**
	 * @description: 获取部门信息
	 * @return: Organization
	 * @author: Strange
	 * @date: 2020/7/6
	 **/
	protected Organization getOrgByOrgId(Integer orgId) {
		if (ObjectUtils.notEmpty(orgId)) {
			Organization org = organizationMapper.selectByPrimaryKey(orgId);
			if (org == null) {
				return null;
			} else {
				return org;
			}
		} else {
			return null;
		}
	}
	/**
	 * <b>Description:</b><br>
	 * 根据人员查询部门名称
	 *
	 * @param userId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年8月25日 上午10:59:41
	 */
	protected String getOrgaNameByUserId(Integer userId) {
		if (ObjectUtils.notEmpty(userId)) {
			Organization org = organizationMapper.getOrgNameByUserId(userId);
			if (org == null) {
				return null;
			} else {
				return org.getOrgName();
			}
		} else {
			return null;
		}
	}

	/**
	 * @description: 根据人员查询部门信息
	 * @return: Organization
	 * @author: Strange
	 * @date: 2020/7/6
	 **/
	protected Organization getOrgByUserId(Integer userId) {
		if (ObjectUtils.notEmpty(userId)) {
			Organization org = organizationMapper.getOrgNameByUserId(userId);
			if (org == null) {
				return null;
			} else {
				return org;
			}
		} else {
			return null;
		}
	}
	/**
	 * <b>Description:</b><br>
	 * 根据父id查询子列表
	 *
	 * @param parentId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年9月15日 上午9:21:48
	 */
	@Override
	public List<SysOptionDefinition> getSysOptionDefinitionListByParentId(Integer parentId) {
		if (parentId == null || parentId == 0) {
			return null;
		}

		List<SysOptionDefinition> resultList = null;
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId)) {
			String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId);
			// 避免json为空或null的字符串或[null]的字符串
			if (StringUtils.isNotBlank(jsonStr) && !"null".equalsIgnoreCase(jsonStr)
					&& !"[null]".equalsIgnoreCase(jsonStr)) {
				JSONArray json = JSONArray.fromObject(jsonStr);
				resultList = (List<SysOptionDefinition>) JSONArray.toCollection(json, SysOptionDefinition.class);
			}
		}
		// 从redis中获取为null，则从库中查询
		if (org.apache.commons.collections.CollectionUtils.isEmpty(resultList)) {
			// 调用根据parendId获取数字字典子list
			try {
				resultList = sysOptionDefinitionMapper.getDictionaryByParentId(parentId);
				JedisUtils.set(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId,
						JsonUtils.convertConllectionToJsonStr(resultList), cacheSecond);
				return resultList;
			} catch (Exception e) {
				logger.error("------BaseServiceimpl------字典数据集合加载失败!---------",e);
				throw new RuntimeException();
			}
		}
		return resultList;
	}

	/**
	 * <b>Description:</b><br>
	 * 查询字典表对象
	 *
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年9月15日 上午11:01:34
	 */
	@Override
	public SysOptionDefinition getSysOptionDefinitionById(Integer sysOptionDefinitionId) {
		if (sysOptionDefinitionId == null || sysOptionDefinitionId == 0) {
			return new SysOptionDefinition();
		}
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + sysOptionDefinitionId)) {
			String jsonStr = JedisUtils
					.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + sysOptionDefinitionId);
			JSONObject json = JSONObject.fromObject(jsonStr);
			SysOptionDefinition sysOptionDefinition = (SysOptionDefinition) JSONObject.toBean(json,
					SysOptionDefinition.class);
			return sysOptionDefinition;
		} else {
			SysOptionDefinition sod = new SysOptionDefinition();
			sod.setSysOptionDefinitionId(sysOptionDefinitionId);
			// 定义反序列化 数据格式
			final TypeReference<ResultInfo<SysOptionDefinition>> TypeRef2 = new TypeReference<ResultInfo<SysOptionDefinition>>() {
			};
			try {
				ResultInfo<?> result = (ResultInfo<?>) HttpClientUtils.post(httpUrl + ErpConst.GET_SYSTEM_OPTION_OBJECT,
						sod, clientId, clientKey, TypeRef2);
				if (result == null || result.getCode() != 0) {
					return new SysOptionDefinition();
				}
				SysOptionDefinition sysOptionDefinition = (SysOptionDefinition) result.getData();
				JedisUtils.set(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_OBJECT + sysOptionDefinitionId,
						JsonUtils.convertObjectToJsonStr(sysOptionDefinition), cacheSecond);
				return sysOptionDefinition;
			} catch (Exception e) {
				logger.error("------BaseServiceimpl------字典数据对象加载失败!---------");
				throw new RuntimeException(e);

			}
		}

	}

	/**
	 * <b>Description:</b><br>
	 * 取缓存数据（字典库）
	 *
	 * @param parentId
	 * @return
	 * @Note <b>Author:</b> duke <br>
	 *       <b>Date:</b> 2017年12月11日 下午1:11:33
	 */
	public List<SysOptionDefinition> getSysOptionDefinitionList(Integer parentId) {
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId)) {
			String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId);
			JSONArray json = JSONArray.fromObject(jsonStr);
			List<SysOptionDefinition> list = (List<SysOptionDefinition>) JSONArray.toCollection(json,
					SysOptionDefinition.class);
			return list;
		} else {
			List<SysOptionDefinition> list = this.getSysOptionDefinitionListByParentId(parentId);
			return list;
		}
	}
	@Override
	public List<SysOptionDefinition> getSysOptionDefinitionList(String optionType) {
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_TYPE + optionType)) {
			String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_TYPE + optionType);
			JSONArray json = JSONArray.fromObject(jsonStr);
			List<SysOptionDefinition> list = (List<SysOptionDefinition>) JSONArray.toCollection(json,
					SysOptionDefinition.class);
			return list;
		} else {

			List<SysOptionDefinition> list = sysOptionDefinitionMapper.getSysOptionDefinitionByOptionType(optionType);
			JedisUtils.set(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_TYPE + optionType,
					JsonUtils.convertConllectionToJsonStr(list), cacheSecond);
			return list;
		}
	}
	@Override
	public SysOptionDefinition getFirstSysOptionDefinitionList(String optionType) {
		List<SysOptionDefinition> result;
		if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_TYPE + optionType)) {
			String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_TYPE + optionType);
			JSONArray json = JSONArray.fromObject(jsonStr);
			List<SysOptionDefinition> list = (List<SysOptionDefinition>) JSONArray.toCollection(json,
					SysOptionDefinition.class);
			result = list;
		} else {
			List<SysOptionDefinition> list = sysOptionDefinitionMapper.getSysOptionDefinitionByOptionType(optionType);
			if (CollectionUtils.isEmpty(list)) {
				list = Lists.newArrayList();
			}
			JedisUtils.set(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_TYPE + optionType,
					JsonUtils.convertConllectionToJsonStr(list), cacheSecond);
			result = list;
		}
		if (CollectionUtils.isEmpty(result)) {
			return new SysOptionDefinition();
		}
		return result.get(0);
	}


	/**
	 * 获取配置apiUrl
	 *
	 * @return
	 */
	@Override
	public String getApiUri() {
		return apiUrl;
	}


	/**
	 *
	 * <b>Description: 根据id查询数字字典值</b><br>
	 * @param defaultValue  默认值
	 * @param optionType
	 * @return
	 * <b>Author: Franlin.wu</b>
	 * <br><b>Date: 2018年12月14日 下午2:37:40 </b>
	 */
	public String getConfigStringByDefault(String defaultValue, String optionType) {
		logger.debug("查询id：{}, 默认值：{}", optionType, defaultValue);

		try
		{
			// 根据id查询数字字典值
			SysOptionDefinition option = getFirstSysOptionDefinitionList(optionType);
			if(null != option && EmptyUtils.isNotBlank(option.getTitle())) {
				defaultValue = option.getTitle();
			}
		}
		catch(Exception e)
		{
			logger.error("根据id查询数字字典配置发生异常", e);
		}

		return defaultValue;
	}


	@Override
	public void sendTemplateMsgHcForShip(Saleorder saleorder, Map<String, String> sTempMap) {
		logger.info("sendTemplateMsgHcForShip | 医械购微信 | saleorder:{}, sTempMap:{}",
				JsonUtils.convertObjectToJsonStr(saleorder), sTempMap);
		ReqTemplateVariable reqTemp = new ReqTemplateVariable();
		ResultInfo<?> resultInfo=null;
		if(null != saleorder && null != sTempMap) {
			// 非耗材订单
			if(!OrderConstant.ORDER_TYPE_HC.equals(saleorder.getOrderType())) {
				return;
			}
			// add by franlin.wu for [微信推送 发货提醒] at 2019-06-19 begin
			// 订单客户联系人
			reqTemp.setPhoneNumber(saleorder.getTraderContactMobile());
			// 模板消息数字字典Id
			reqTemp.setTemplateId(WeChatSendTemplateUtil.TEMPLATE_SHIPING_REMINDER);

			// 头
			TemplateVar first = new TemplateVar();
			String firstStr = getConfigStringByDefault("尊敬的客户，您的订单已发货，请注意查收：", SysOptionConstant.ID_WECHAT_TEMPLATE_ORDER_SIGNING_NOTICE_FIRST);
			first.setValue(firstStr + "\r\n");

			// 商品名称
			TemplateVar keyword1 = new TemplateVar();
			// 订单号
			keyword1.setValue(sTempMap.get("saleorderNo"));

			// 商品名称
			TemplateVar keyword2 = new TemplateVar();
			// saleorderAllNum
			keyword2.setValue(sTempMap.get("expressFirstGoodsName") + "等" + " " + sTempMap.get("saleorderAllNum") + "个商品");

			TemplateVar keyword3 = new TemplateVar();
			// 商品数量
			keyword3.setValue(sTempMap.get("expressAllNum"));

			TemplateVar keyword4 = new TemplateVar();
			// 下单时间
			keyword4.setValue(sTempMap.get("validTime"));

			// 客户信息 联系人 以及 客户手机号
			TemplateVar keyword5 = new TemplateVar();
			String keyword5Str = sTempMap.get("customerInfo");
			String keyword6Str = sTempMap.get("logisticsName") + " " + sTempMap.get("logisticsNo");
			keyword5Str += "\r\n物流信息:" + keyword6Str + "\r\n";
			keyword5.setValue(keyword5Str);

			TemplateVar keyword6= new TemplateVar();
			//快递信息（快递公司名称+快递单号）
			keyword6.setValue(keyword6Str);

			TemplateVar remark = new TemplateVar();
			String remarkStr = getConfigStringByDefault("感谢您对医械购的支持与信任，如有疑问请联系：18651638763", SysOptionConstant.ID_WECHAT_TEMPLATE_REMARK);
			remark.setValue(remarkStr);

			reqTemp.setFirst(first);
			reqTemp.setKeyword1(keyword1);
			reqTemp.setKeyword2(keyword2);
			reqTemp.setKeyword3(keyword3);
			reqTemp.setKeyword4(keyword4);
			reqTemp.setKeyword5(keyword5);
			reqTemp.setKeyword6(keyword6);
			reqTemp.setRemark(remark);

			resultInfo=WeChatSendTemplateUtil.sendTemplateMsg(apiUrl + ApiUrlConstant.API_WECHAT_SEND_TEMPLATE_MSG, reqTemp);
			// add by franlin.wu for [微信推送 发货提醒] at 2019-06-19 end
		}
		logger.info("sendTemplateMsgHcForShip | 医械购微信 发货 模板消息 | end .......request:{},response:{}",JsonUtils.convertObjectToJsonStr(reqTemp),resultInfo);

	}

	@Override
	public void sendTemplateVxService(Saleorder saleorders, Map<String, String> sTempMap) {
		logger.info("微信服务号推送消息 发货提醒 {}",JsonUtils.convertObjectToJsonStr(saleorders));
		Integer orderType = saleorders.getOrderType();
		Integer saleorderId = saleorders.getSaleorderId();
		com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable reqTemp = new com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable();
		ResWeChatDTO result=null;
		if (null != saleorders && null != sTempMap) {
			//必须是BD JX DH VS 订单
			if (OrderConstant.ORDER_TYPE_SALE.equals(orderType) || OrderConstant.ORDER_TYPE_DH.equals(orderType)
					|| OrderConstant.ORDER_TYPE_JX.equals(orderType) || OrderConstant.ORDER_TYPE_BD.equals(orderType)) {
				// add by franlin.wu for [微信推送 发货提醒] at 2019-06-19 begin
				// 订单客户联系人
				//reqTemp.setMobile(phone);
				reqTemp.setMobile(saleorders.getTraderContactMobile());
				// 模板消息数字字典Id
				reqTemp.setTemplateId(shipmentToRemind);

				// 头
				com.vedeng.passport.api.wechat.dto.template.TemplateVar first = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				String firstStr = getConfigStringByDefault("尊敬的客户，您的订单已发货，请注意查收：", SysOptionConstant.ID_WECHAT_TEMPLATE_ORDER_SIGNING_NOTICE_FIRST);
				first.setValue(firstStr + "\r\n");
				// 商品名称
				com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword1 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				// 订单号
				keyword1.setValue(sTempMap.get("saleorderNo"));
				// 商品名称
				com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword2 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				// saleorderAllNum
				keyword2.setValue(sTempMap.get("expressFirstGoodsName") + "等" + " " + sTempMap.get("saleorderAllNum") + "个商品");

				com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword3 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				// 商品数量
				keyword3.setValue(sTempMap.get("expressAllNum"));

				com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword4 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				// 下单时间
				keyword4.setValue(sTempMap.get("validTime"));
				// 客户信息 联系人 以及 客户手机号
				com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword5 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				String keyword5Str = sTempMap.get("customerInfo");
				String keyword6Str = sTempMap.get("logisticsName") + " " + sTempMap.get("logisticsNo");
				keyword5Str += "\r\n物流信息:" + keyword6Str + "\r\n";
				keyword5.setValue(keyword5Str);
				com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword6 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				//快递信息（快递公司名称+快递单号）
				keyword6.setValue(keyword6Str);
				com.vedeng.passport.api.wechat.dto.template.TemplateVar remark = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
				String remarkStr = getConfigStringByDefault("感谢您对贝登的支持与信任，如有疑问请联系：4006-999-569", SysOptionConstant.WECHAT_TEMPLATE_BEDENG_REMARK);

				remark.setValue(remarkStr);
				reqTemp.setFirst(first);
				reqTemp.setKeyword1(keyword1);
				reqTemp.setKeyword2(keyword2);
				reqTemp.setKeyword3(keyword3);
				reqTemp.setKeyword4(keyword4);
				reqTemp.setKeyword5(keyword5);
				reqTemp.setKeyword6(keyword6);
				reqTemp.setRemark(remark);
				  result=sendTemplateMsg(vxService+"/wx/wxchat/send", reqTemp);
				//sendTemplateMsg("http://************:8280/wx/wxchat/send",reqTemp);
			}
			logger.info("sendTemplateMsgHcForShip | 微信 发货 模板消息 | end .......request:{} response:{}",JsonUtils.convertObjectToJsonStr(reqTemp),
					JsonUtils.convertObjectToJsonStr(result));
		}
	}

	public static ResWeChatDTO sendTemplateMsg(String vxService, com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable reqTemp) {

		// 接口返回
		TypeReference<ResWeChatDTO> type = new TypeReference<ResWeChatDTO>() {};
		// 接口请求头
		HashMap<String, String> heads = new HashMap<String, String>();
		// 接口版本0
		heads.put(CommonConstants.INTER_VERSION, CommonConstants.INTER_VERSION_VALUE);
		// 调用接口
		ResWeChatDTO resultInfo =  HttpRestClientUtil.post2(vxService, type, heads, reqTemp);
		return resultInfo;
	}

	/**
	 * 设置航信的税率搜索信息
	 *
	 * @param searchDTO
	 */
	public void setInvoiceTaxRateSearchInfo(HxInvoiceSearchDTO searchDTO) {
		if (searchDTO == null || searchDTO.getInvoiceTaxRate() == null){
			return;
		}
		switch(searchDTO.getInvoiceTaxRate()){
			case 971 :{
				// 13%增值税普通发票
				searchDTO.setInvoiceTaxRate(13);
				searchDTO.setInvoiceCategory("0");
				break;
			}
			case 972:{
				//13%增值税专用发票
				searchDTO.setInvoiceTaxRate(13);
				searchDTO.setInvoiceCategory("1");
				break;
			}
			case 681 :{
				// 16%增值税普通发票
				searchDTO.setInvoiceTaxRate(16);
				searchDTO.setInvoiceCategory("0");
				break;
			}
			case 682:{
				//16%增值税专用发票
				searchDTO.setInvoiceTaxRate(16);
				searchDTO.setInvoiceCategory("1");
				break;
			}
			case 683:{
				//6%增值税普通发票
				searchDTO.setInvoiceTaxRate(6);
				searchDTO.setInvoiceCategory("0");
				break;
			}
			case 684:{
				//6%增值税专用发票
				searchDTO.setInvoiceTaxRate(6);
				searchDTO.setInvoiceCategory("1");
				break;
			}
			case 685:{
				//3%增值税普通发票
				searchDTO.setInvoiceTaxRate(3);
				searchDTO.setInvoiceCategory("0");
				break;
			}
			case 686:{
				//3%增值税专用发票
				searchDTO.setInvoiceTaxRate(3);
				searchDTO.setInvoiceCategory("1");
				break;
			}
			case 687:{
				//0%增值税普通发票
				searchDTO.setInvoiceTaxRate(0);
				searchDTO.setInvoiceCategory("0");
				break;
			}
			default:{
				searchDTO.setInvoiceTaxRate(null);
				break;
			}
		}
	}

	@Override
	public boolean isCouponsType(Integer orderType) {
		if(orderType == null){
			return false;
		}
		if(orderType.equals(5) || orderType.equals(1)){
			return true;
		}
		return false;
	}

	@Override
	public void saveBuyorderInvoiceStatus(Integer buyorderId) {
        try {
            logger.info("开始保存采购单收票状态信息 buyorderId:{}", buyorderId);
            if (buyorderId == null){
               return;
            }
            BuyorderVo buyorderInfo = buyorderMapper.getBuyorderVoById(buyorderId);
            buyorderInfo.setRealAmount(buyorderMapper.getRealAmount(buyorderId));
            logger.info("采购单的真实总价为 buyorderId :{},  realAmount :{}", buyorderId, buyorderInfo.getRealAmount());

            //获取采购单的商品信息
            List<BuyorderGoodsVo> buyorderGoodsVos = new ArrayList<>();
            for (BuyorderGoodsVo item : buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderId(buyorderId)){
            	if (item.getIsDelete() == 0){
            		buyorderGoodsVos.add(item);
				}
			}
            buyorderInfo.setBuyorderGoodsVoList(buyorderGoodsVos);
            logger.info("采购单的基本信息 buyorderInfo:{}", JSON.toJSONString(buyorderInfo));
            int invoiceStatus;
            /**
             * 1.若采购单总额=0
             * （1）当采购单的到货状态变为“全部到货”时，将收票状态更新为：全部收票；
             * （2）否则为：未收票
             */
            if (BigDecimal.ZERO.compareTo(buyorderInfo.getTotalAmount()) == 0) {
                logger.info("采购订单的采购总额为0 buyorderId:{}", JSON.toJSONString(buyorderId));
                if (buyorderInfo.getArrivalStatus().equals(2)) {
                    logger.info("采购订单总额为0并且全部到货 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 2;
                } else {
                    logger.info("采购订单总额为0未全部到货 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 0;
                }
            } else {
                /**
                 * 2（1）若采购单各sku的录票数量合计=0，则更新收票状态为：未收票
                 * （2）若采购单中每个商品均满足以下条件，则采购单的收票状态为“全部收票”：
                 * ① -1≤（商品的实际金额-商品的收票总额）≤1
                 * ② 商品的收票数量=商品的实际数量
                 */
                logger.info("采购订单的采购总额不为0 buyorderId:{}", JSON.toJSONString(buyorderId));
                BigDecimal invoiceTotalNum = BigDecimal.ZERO;
                boolean isComplete = true;
				boolean isRebate = true;
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(buyorderInfo.getBuyorderGoodsVoList())) {
                	//去掉采购单价为0的采购商品
					List<BuyorderGoodsVo> buyorderGoodsVoList = new ArrayList<>();
					for (BuyorderGoodsVo item : buyorderInfo.getBuyorderGoodsVoList()){
						if (item.getPrice().compareTo(BigDecimal.ZERO) != 0){
							buyorderGoodsVoList.add(item);
						}
					}
					for (BuyorderGoodsVo buyorderGoodsVo : buyorderGoodsVoList) {
                        BigDecimal invoiceTotalAmount = buyorderMapper.getHaveInvoiceTotalAmount(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的收票总额 buyorderGoodsId:{}，invoiceTotalAmount:{}", buyorderGoodsVo.getBuyorderGoodsId(), invoiceTotalAmount);

                        //采购商品的已收票数量
                        BigDecimal haveInvoiceNums = buyorderMapper.getHaveInvoiceNums(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的已收票数量 buyorderGoodsId:{}, haveInvoiceNums:{}", buyorderGoodsVo.getBuyorderGoodsId(), haveInvoiceNums);

                        if (haveInvoiceNums != null) {
                            invoiceTotalNum = invoiceTotalNum.add(haveInvoiceNums);
                        }

                        //采购商品的实际数量
                        BigDecimal realTotalNum = buyorderMapper.getGoodsTotalNum(buyorderGoodsVo.getBuyorderGoodsId());
                        logger.info("采购订单商品的实际数量 buyorderGoodsId:{}, realTotalNum:{}", buyorderGoodsVo.getBuyorderGoodsId(), realTotalNum);
//                        VDERP-6880修复采购单sku采购又全部售后的情况导致的收票状态不正确，原本全部收票的状态误判为部分收票
						if(realTotalNum.compareTo(new BigDecimal(0)) == 0 && haveInvoiceNums == null){
							continue;
						}

                        //采购订单的实际总额
						// VDERP-16204 计算采购单实际金额时，需要减去每个sku的返利金额
						BigDecimal realPrice = buyorderGoodsVo.getPrice().subtract(buyorderGoodsVo.getRebatePrice());

						BigDecimal realTotalAmount = realPrice.multiply(realTotalNum);

						if (realPrice.compareTo(BigDecimal.ZERO) <= 0) {
							continue;
						}

						if(realPrice.compareTo(BigDecimal.ZERO) > 0){
							//商品的收票数量!=商品的实际数量
							if (haveInvoiceNums == null || haveInvoiceNums.compareTo(realTotalNum) != 0) {
								isComplete = false;
								logger.info("商品的收票数量不等于商品的实际数量， 采购商品：{}", JSON.toJSONString(buyorderGoodsVo));
							}
							isRebate = false;
						}
                        logger.info("采购订单商品的实际总额 buyorderGoodsId:{}, realTotalAmount:{}", buyorderGoodsVo.getBuyorderGoodsId(),realTotalAmount);

                        //-1≤（商品的实际金额-商品的收票总额）≤1 不成立
						if (realTotalAmount.subtract(invoiceTotalAmount == null ? BigDecimal.ZERO : invoiceTotalAmount).abs().compareTo(BigDecimal.ONE) >= 1 ) {
							isComplete = false;
							logger.info("商品的实际金额与商品的收票总额校验不通过， 采购商品：{}", JSON.toJSONString(buyorderGoodsVo));
						}
                    }
                }

                if (invoiceTotalNum.compareTo(BigDecimal.ZERO) == 0&&!isRebate) {
                    //采购订单为未收票
                    logger.info("采购订单的收票数量为0 未收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    invoiceStatus = 0;
                } else if (isComplete) {
                    logger.info("采购订单符合条件为全部收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    //采购订单全部收票
                    invoiceStatus = 2;
                } else {
                    logger.info("采购订单不符合条件为部分收票 buyorderId:{}", JSON.toJSONString(buyorderId));
                    //采购订单未全部收票
                    invoiceStatus = 1;
                }
            }

            logger.info("更新采购单录票状态信息 buyorderId:{},invoiceStatus:{}", buyorderId, invoiceStatus);
            buyorderMapper.saveInvoiceStatus(buyorderId, invoiceStatus);

            // VDERP-8755 订单流 采购单票货款全部完成时，将状态置为 已完结
			if (invoiceStatus == 2) {
				Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyorderId);
				if (buyorder.getIsNew() == 1 && buyorder.getStatus() == 1 && buyorder.getPaymentStatus() == 2 && buyorder.getDeliveryStatus() == 2 && buyorder.getArrivalStatus() == 2) {
					buyorder.setStatus(2);
					buyorderMapper.updateByPrimaryKeySelective(buyorder);
					logger.info("订单流票货款全部完成更新采购单状态为已完结--全部收票:{}", buyorderId);
				}
			}

        } catch (Exception e) {
            logger.error("采购订单保存收票状态error", e);
        }
    }


	@Override
	public TraderBaseInfoDto getTraderBaseInfoByTraderId(Integer traderId, Integer traderType) {
		if (traderId == null || traderType == null){
			return null;
		}
		TraderBaseInfoDto traderBaseInfo = new TraderBaseInfoDto();
		traderBaseInfo.setTraderId(traderId);
		User saleUser = userMapper.getUserByTraderId(traderId, traderType);
		if (saleUser != null){
			traderBaseInfo.setSalesNameStr(saleUser.getUsername());
		}
		Trader trader = traderMapper.getTraderByTraderId(traderId);
		if (trader != null){
			traderBaseInfo.setTraderName(trader.getTraderName());
		}
		return traderBaseInfo;
	}


	@Override
	public Map<Integer,RTraderGroupJTrader> getTraderGroup(TraderGroup group, List<Integer> traderIds) {
		if (group != null && org.apache.commons.collections.CollectionUtils.isNotEmpty(traderIds)){
			List<RTraderGroupJTrader> rTraderGroupJTraders = rTraderGroupJTraderMapper.getTraderGroupById(group.getTraderGroupId(),traderIds);
			Map<Integer,RTraderGroupJTrader> map = new HashMap<>();
			for (RTraderGroupJTrader rTraderGroupJTrader : rTraderGroupJTraders) {
				rTraderGroupJTrader.setTraderGroupName(group.getTraderGroupName());
				map.put(rTraderGroupJTrader.getTraderId(),rTraderGroupJTrader);
			}
			return map;
		}
		return new HashMap<>();
	}

	/**
	 * 根据id获取对应字符串
	 * @param key1 k
	 * @param key2 k
	 * @param key1_key2_value 数据格式 k_k_v,k2_k2_v2,
	 * @return
	 */
	protected String getValueByKVString(Integer key1,Integer key2, String key1_key2_value) {

		if (Objects.isNull(key1) || Objects.isNull(key2) || Objects.isNull(key1_key2_value)) {
			return null;
		}
		String[] allKkv = key1_key2_value.split(",");
		for (String oneKkv : allKkv) {
			String[] splitOneKkv = oneKkv.split("_");
			if (splitOneKkv.length == 3) {
				Integer k1 = Integer.parseInt(splitOneKkv[0]);
				Integer k2 = Integer.parseInt(splitOneKkv[1]);
				String v = String.valueOf(splitOneKkv[2]);
				if (key1.equals(k1) && key2.equals(k2)) {
					return v;
				}
			}
		}
		return null;
	}

	protected String getValueByKKVString(Integer key, String key_value) {

		if (Objects.isNull(key) || Objects.isNull(key_value)) {
			return null;
		}
		String[] allKv = key_value.split(",");
		for (String oneKv : allKv) {
			String[] splitOneKv = oneKv.split("_");
			if (splitOneKv.length == 2) {
				Integer k = Integer.parseInt(splitOneKv[0]);
				String v = String.valueOf(splitOneKv[1]);
				if (key.equals(k)) {
					return v;
				}
			}
		}
		return null;
	}

	@Override
	public User getSessionUser(HttpServletRequest request) {
		return (User) request.getSession().getAttribute(ErpConst.CURR_USER);
	}

	public boolean checkPermission(String uri,String userName) {
		if (CasClientHelper.enableSingleSignOn()) {
			return SecurityUtils.getSubject().isPermitted(uri);
		} else {
			JSONArray jsonArray = JSONArray.fromObject(JedisUtils.get(RedisKeyUtils.createPermissionKey(userName)));
			@SuppressWarnings("unchecked")
			List<String> permission = (List<String>) JSONArray.toCollection(jsonArray, String.class);
			List<String> permissionList = new LinkedList<>(permission);
			String uriToUse = org.springframework.util.StringUtils.trimLeadingCharacter(uri, '/');
			uriToUse = "/" + uriToUse;
			return permissionList.contains(uriToUse);
		}
	}
	/**
	 * <AUTHOR>
	 * @desc 科研购订单发货推送公众号消息
	 * @param saleorder
	 * @param sMap
	 */
	@Override
	public void sendKygDeliveryOrder(Saleorder saleorder, Map sMap){
		logger.info("微信消息公众号科研购订单发货通知begin,销售单号{},",saleorder.getSaleorderNo());
		if(saleorder.getSaleorderNo() != null && saleorder.getSaleorderNo().startsWith("K")) {
			// 贝登消息推送
			com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable reqTemp = new com.vedeng.passport.api.wechat.dto.req.ReqTemplateVariable();

			logger.info("sendKygDeliveryOrder | traderConMobile:{}", saleorder.getTraderContactMobile());
			// 订单客户联系人
			reqTemp.setMobile(saleorder.getTraderContactMobile());
//		reqTemp.setMobile("18297888543");
			// 模板消息数字字典Id
			reqTemp.setTemplateId(kygDeliveryReminder);

			com.vedeng.passport.api.wechat.dto.template.TemplateVar first = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			String firstStr = getConfigStringByDefault("【科研购】尊敬的客户，您的订单已发货，请注意查收：", SysOptionConstant.ID_WECHAT_TEMPLATE_KYG_DELIVERY_REMINDER_FIRST);
			logger.info("获取数据配置 | firstStr：{} ", firstStr);
			first.setValue(firstStr + "\r\n");

			com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword1 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword2 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword3 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword4 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			com.vedeng.passport.api.wechat.dto.template.TemplateVar keyword5 = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			//订单号
			keyword1.setValue(saleorder.getSaleorderNo());
			//客户名称+客户地址+收货人电话
			keyword2.setValue(saleorder.getTraderName() + "\n" + saleorder.getTakeTraderArea() + saleorder.getTakeTraderAddress() + "\n" + saleorder.getTakeTraderContactMobile());
			//发货时间
			keyword3.setValue(sMap.get("logisticTime").toString());
			//物流信息
			keyword4.setValue(sMap.get("logisticName").toString() + "  " + sMap.get("logisticNo").toString());
			//产品信息
			if (Integer.parseInt(sMap.get("goodTypeSum").toString()) == 1) {
				//单个sku
				keyword5.setValue(sMap.get("goodName").toString() + sMap.get("goodNum").toString() + "个商品");
			} else {
				//多个sku
				keyword5.setValue(sMap.get("goodName").toString() + "等" + sMap.get("goodNum").toString() + "个商品");
			}
			com.vedeng.passport.api.wechat.dto.template.TemplateVar remark = new com.vedeng.passport.api.wechat.dto.template.TemplateVar();
			String remarkStr = getConfigStringByDefault("官方售后电话：4008-729-992", SysOptionConstant.WECHAT_TEMPLATE_KYG_DELIVERY_REMINDER_REMARK);
			remark.setValue(remarkStr);

			reqTemp.setFirst(first);
			reqTemp.setKeyword1(keyword1);
			reqTemp.setKeyword2(keyword2);
			reqTemp.setKeyword3(keyword3);
			reqTemp.setKeyword4(keyword4);
			reqTemp.setKeyword5(keyword5);
			reqTemp.setRemark(remark);
			// 发货物流 提醒
			sendTemplateMsg(kygVxService + "/m/wechat/send", reqTemp);
		}else {
			logger.info("非科研购订单，无需推送");
		}
	}

	@Override
	public ResultInfo<String> verifySpecialTicket(Integer saleorderId,Integer invoiceType,Integer invoiceTraderId) {
		ResultInfo resultInfo = new ResultInfo(0,"");
		Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleorderId);
		if (saleorder.getSaleorderNo().startsWith("BFF")
				|| saleorder.getSaleorderNo().startsWith("YFZ")
				|| saleorder.getSaleorderNo().startsWith("KFF")
				|| saleorder.getSaleorderNo().startsWith("YFJ")
				|| saleorder.getSaleorderNo().startsWith("YFF")
				|| saleorder.getSaleorderNo().startsWith("KFZ")
				|| saleorder.getSaleorderNo().startsWith("BFZ")) {
			if(ErpConst.ZERO.equals(invoiceType) && ErpConst.ZERO.equals(invoiceTraderId)) {
				//申请审核校验拦截
				invoiceTraderId = ErpConst.ZERO.equals(saleorder.getInvoiceTraderId()) ? saleorder.getTraderId() : saleorder.getInvoiceTraderId();
			}
			if(!ErpConst.ZERO.equals(invoiceType)){
				saleorder.setInvoiceType(invoiceType);
			}
			if (SysOptionConstant.ID_429.equals(saleorder.getInvoiceType())
					|| SysOptionConstant.ID_972.equals(saleorder.getInvoiceType())
					|| SysOptionConstant.ID_682.equals(saleorder.getInvoiceType())
					|| SysOptionConstant.ID_684.equals(saleorder.getInvoiceType())) {
				//发票类型为专票的情况下校验专票资质
				TraderFinance traderFinance = traderFinanceMapper.getCustomerFinanceByTraderId(invoiceTraderId);
				logger.info("订单号{},财务资质信息{},", saleorder.getSaleorderNo(), JSON.toJSONString(traderFinance));
				//注册地址
				if (traderFinance != null) {
					if (traderFinance.getRegAddress() != null && !"".equals(traderFinance.getRegAddress())
							//注册电话
							&& traderFinance.getRegTel() != null && !"".equals(traderFinance.getRegTel())
							//税务登记号
							&& traderFinance.getTaxNum() != null && !"".equals(traderFinance.getTaxNum())
							//一般纳税人资质
							&& traderFinance.getAverageTaxpayerUri() != null && !"".equals(traderFinance.getAverageTaxpayerUri())
							//开户银行
							&& traderFinance.getBank() != null && !"".equals(traderFinance.getBank())
							//银行账号
							&& traderFinance.getBankAccount() != null && !"".equals(traderFinance.getBankAccount())) {
						resultInfo = new ResultInfo(0, "审核通过");
					} else {
						resultInfo = new ResultInfo(-1, "缺失专票资质，请前往客户列表维护客户财务信息");
					}
				}else {
					resultInfo = new ResultInfo(-1, "缺失专票资质，请前往客户列表维护客户财务信息");
				}
			}
		}
		return resultInfo;
	}
}
