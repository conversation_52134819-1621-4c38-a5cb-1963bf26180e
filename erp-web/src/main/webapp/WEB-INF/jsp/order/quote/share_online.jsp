<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="分享线上报价单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/order/quote/share_onlie.js?rnd=${resourceVersionKey}'></script>
<div class="main-container">
    <div>
        <table>
            <tr>
                <td class="table-smaller">报价单号: ${quoteordervo.quoteorderNo}</td>
            </tr>
            <tr>
                <td class="table-smaller">价格有效期: 14天( ${now}~${end} )</td>
            </tr>
            <tr>
                <td class="table-smaller">甲方: ${quoteordervo.traderName}</td>
            </tr>
           <tr>
               <td class="table-smaller">联系人: ${quoteordervo.traderContactName}</td>
           </tr>
            <tr>
                <td class="table-smaller">手机号: ${quoteordervo.mobile}</td>
            </tr>
            <tr>
                <td class="table-smaller">商品总价: <span style="color: red">${quoteordervo.quoteMoney}</span>
                    <span style="color: gray">(共${quoteordervo.goodsCount}种,${quoteordervo.totalNum}件商品)</span>
                </td>
            </tr>
            <tr>
                <td class="table-smaller">产品配送费: ${quoteordervo.freightDescriptionName}</td>
            </tr>
            <tr>
                <td class="table-smaller" id="text" style="background-color:#f3f3f3;">
                    您好，根据您的采购需求，我为您制作一份报价单，请您核实，若对商品信息无异议可点击页面下方“立即下单”按钮一键生成订单。详情点击：
                    <a href="${confirmOrderUrl}bj-${quoteordervo.quoteorderNo}.html" target="_blank">
                        ${confirmOrderUrl}bj-${quoteordervo.quoteorderNo}.html
                    </a>
                </td>
            </tr>
            <tr>
                <td>
                    <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 J-copy-btn"
                            data-clipboard-text="您好，根据您的采购需求，我为您制作一份报价单，请您核实，若对商品信息无异议可点击页面下方“立即下单”按钮一键生成订单。详情点击：${confirmOrderUrl}bj-${quoteordervo.quoteorderNo}.html">复制文案</button>
                </td>
            </tr>
            <tr>
                <td>
                    <span style="color: gray">复制文案发给客户，并告知用户登录状态下进行查看。</span>
                </td>
            </tr>
        </table>
    </div>
</div>
<script type="text/javascript" src="<%=basePath%>static/js/common/clipboard.min.js?rnd=${resourceVersionKey}"></script>
<script>
    var clipboard = new ClipboardJS('.J-copy-btn');

    clipboard.on('success', function(e) {
        layer.msg('复制成功')
    });

    clipboard.on('error', function(e) {
        layer.msg('复制失败')
    });

</script>

<%@ include file="../../common/footer.jsp"%>
