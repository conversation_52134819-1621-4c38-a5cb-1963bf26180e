package com.newtask.trader;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.trader.domain.entity.DwhTraderTagCpmErpEntity;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingPrincipalEntity;
import com.vedeng.erp.trader.domain.entity.TraderCustomerMarketingTerminalEntity;
import com.vedeng.erp.trader.mapper.TraderCustomerMarketingPrincipalMapper;
import com.vedeng.erp.trader.mapper.TraderCustomerMarketingTerminalMapper;
import com.vedeng.erp.trader.service.DwhTraderTagCpmErpService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 大数据客户营销标签信息同步erp
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "DwhTraderCustomerTagSyncErpTask")
@Slf4j
public class DwhTraderCustomerTagSyncErpTask extends AbstractJobHandler {
    @Autowired
    private DwhTraderTagCpmErpService dwhTraderTagCpmErpService;
    @Autowired
    private TraderCustomerMarketingPrincipalMapper traderCustomerMarketingPrincipalMapper;
    @Autowired
    private TraderCustomerMarketingTerminalMapper traderCustomerMarketingTerminalMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("DwhTraderCustomerTagSyncErpTask-JOB, START");
        log.info("大数据客户营销标签信息同步erp start param:{}", param);
        int pageSize = 1000;
        int pageNum = 1;
        while (true) {
            log.info("分页查询进度 {},{}", pageSize, pageNum);
            PageHelper.startPage(pageNum, pageSize);
            // 终端信息
            DwhTraderTagCpmErpEntity dwhTraderTagCpmErpEntity = new DwhTraderTagCpmErpEntity();
            if (StrUtil.isNotBlank(param)) {
                dwhTraderTagCpmErpEntity.setTraderId(Integer.parseInt(param));
            }
            List<DwhTraderTagCpmErpEntity> terminalList = dwhTraderTagCpmErpService.findAll(dwhTraderTagCpmErpEntity);
            // 主营信息
            List<DwhTraderTagCpmErpEntity> principalList = terminalList.stream().collect(Collectors.groupingBy(DwhTraderTagCpmErpEntity::getTraderCustomerId))
                    .values().stream().map(CollUtil::getFirst).collect(Collectors.toList());

            if (CollUtil.isNotEmpty(terminalList)) {
                List<TraderCustomerMarketingTerminalEntity> updateList = new ArrayList<>();
                List<TraderCustomerMarketingTerminalEntity> insertList = new ArrayList<>();
                terminalList.stream().filter(t->StrUtil.isNotEmpty(t.getTraderCustomerMarketingType())).forEach(e -> {
                    List<TraderCustomerMarketingTerminalEntity> entities = traderCustomerMarketingTerminalMapper.findByTraderIdAndTraderCustomerIdAndTraderCustomerMarketingType(
                            e.getTraderId(), e.getTraderCustomerId(), Integer.parseInt(e.getTraderCustomerMarketingType()));
                    TraderCustomerMarketingTerminalEntity entity;
                    if (CollUtil.isNotEmpty(entities)) {
                        entity = CollUtil.getFirst(entities);
                        BeanUtil.copyProperties(e, entity, CopyOptions.create().ignoreNullValue());
                        updateList.add(entity);
                    } else {
                        entity = new TraderCustomerMarketingTerminalEntity().init();
                        BeanUtil.copyProperties(e, entity, CopyOptions.create().ignoreNullValue());
                        insertList.add(entity);
                    }
                });
                if (CollUtil.isNotEmpty(updateList)) {
                    traderCustomerMarketingTerminalMapper.updateBatchSelective(updateList);
                }
                if (CollUtil.isNotEmpty(insertList)) {
                    traderCustomerMarketingTerminalMapper.batchInsert(insertList);
                }
            }

            if (CollUtil.isNotEmpty(principalList)) {
                List<TraderCustomerMarketingPrincipalEntity> updateList = new ArrayList<>();
                List<TraderCustomerMarketingPrincipalEntity> insertList = new ArrayList<>();
                principalList.forEach(e -> {
                    TraderCustomerMarketingPrincipalEntity entity = traderCustomerMarketingPrincipalMapper.findByTraderIdAndTraderCustomerId(e.getTraderId(), e.getTraderCustomerId());
                    if (entity != null) {
                        // 当销售没有选择综合（两个值都不勾选或只勾选专业）时,如果大数据算出来分类，则系统勾选专业和商品分类
                        if(!(StrUtil.isNotBlank(entity.getSkuScope()) && "0".equals(entity.getSkuScope()))){
                            if(StrUtil.isNotBlank(e.getMainL3CategoryIds())){
                                entity.setSkuScope("1");
                                entity.setSkuCategory(e.getMainL3CategoryIds());
                            }
                        }
                        updateList.add(entity);
                    } else {
                        TraderCustomerMarketingPrincipalEntity entityNew = new TraderCustomerMarketingPrincipalEntity().init();
                        BeanUtil.copyProperties(e, entityNew, CopyOptions.create().ignoreNullValue());
                        if(StrUtil.isNotBlank(e.getMainL3CategoryIds())){
                            entityNew.setSkuScope("1");
                        }
                        entityNew.setSkuCategory(e.getMainL3CategoryIds());
                        insertList.add(entityNew);
                    }
                });
                if (CollUtil.isNotEmpty(updateList)) {
                    traderCustomerMarketingPrincipalMapper.updateBatchSelective(updateList);
                }
                if (CollUtil.isNotEmpty(insertList)) {
                    traderCustomerMarketingPrincipalMapper.batchInsert(insertList);
                }
            }

            // 获取分页信息
            PageInfo<DwhTraderTagCpmErpEntity> pageInfo = new PageInfo<>(terminalList);
            // 判断是否还有下一页
            if (!pageInfo.isHasNextPage()) {
                break;
            }
            // 设置下一页页码
            pageNum = pageInfo.getNextPage();

            log.info("分页查询进度 {},{}", pageSize, pageNum);
        }
        log.info("大数据客户营销标签信息同步erp end");
        XxlJobLogger.log("DwhTraderCustomerTagSyncErpTask-JOB, END");
        return SUCCESS;
    }
}
