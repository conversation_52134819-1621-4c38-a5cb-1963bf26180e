package com.vedeng.system.controller;

import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.Region;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.system.service.OrgService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/10/8 9:41
 * @describe 部门管理
 */
@Controller
@RequestMapping("/system/origanization")
public class OriganizationController {

    @Autowired
    private OrgService orgService;

    /**
     * type 为 310 ，则查询所有销售部门，否则根据parentId查询树形结构的部门信息
     * @param request
     * @param parentId
     * @param level
     * @param type
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/static/getAllOriganization")
    public ResultInfo<Organization> getAllOriganization(HttpServletRequest request, Integer parentId, Integer level, Integer type) {

        ResultInfo<Organization> resultInfo = new ResultInfo<Organization>();
        if (type != null && type == 310) {
            List<Organization> salesOrgList = orgService.getSalesOrgList(SysOptionConstant.ID_310, 1);
            if (salesOrgList != null) {
                resultInfo.setCode(0);
                resultInfo.setMessage("操作成功");
                resultInfo.setListData(salesOrgList);
            }
            return  resultInfo;
        }
        List<Organization> organizationList = orgService.getOriganizationByParentId(parentId);
        fillOrganization(organizationList, level);
        if (organizationList != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setListData(organizationList);
        }
        return resultInfo;
    }

    private void fillOrganization(List<Organization> organizationList, Integer level) {
        if (level > 1 && CollectionUtils.isNotEmpty(organizationList)) {
            for (Organization organization : organizationList) {
                fill(organization);
                Integer a = level - 1;
                fillOrganization(organization.getOrganizations(), a);
            }
        }
    }

    private void fill(Organization item) {
        List<Organization> organizationList = orgService.getOriganizationByParentId(item.getOrgId());
        if (CollectionUtils.isNotEmpty(organizationList)) {
            item.setOrganizations(organizationList);
        }
    }

}
