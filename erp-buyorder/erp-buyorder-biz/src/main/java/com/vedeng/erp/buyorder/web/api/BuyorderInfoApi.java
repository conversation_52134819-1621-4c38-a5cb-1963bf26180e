package com.vedeng.erp.buyorder.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.erp.buyorder.api.BuyorderInfoQueryApi;
import com.vedeng.erp.buyorder.dto.BuyOrderDto;
import com.vedeng.erp.buyorder.mapper.BuyorderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service.impl
 * @Date 2022/1/11 17:52
 */
@RestController
public class BuyorderInfoApi implements BuyorderInfoQueryApi {

    @Autowired
    private BuyorderMapper buyorderMapper;

    @Override
    @NoNeedAccessAuthorization
    public List<BuyOrderDto> listGoodsOnwayInfo(Integer skuId) {
        return buyorderMapper.getBuyorderVoList(skuId);
    }


}
