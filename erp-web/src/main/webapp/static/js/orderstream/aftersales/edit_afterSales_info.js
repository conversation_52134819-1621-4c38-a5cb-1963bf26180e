$(function() {
    $("#submit").click(function(){
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        if ( $("textarea[name='comments']").val().length  == 0){
            warnTips("commentsError","详情说明不允许为空");
            return false;
        }

        if ($("textarea[name='comments']").val().length > 200){
            warnTips("commentsError"," 详情说明最多输入200字符，请检查后提交");
            return false;
        }

        if ($("#reason option:selected").val().length == 0){
            warnTips("reasonError"," 售后原因不允许为空");
            return false;
        }
        if ($("#firstResponsibleDepartment option:selected").val().length == 0){
            warnTips("firstResponsibleDepartmentError"," 第一责任部门不允许为空");
            return false;
        }
        if ($("#traderContactId option:selected").val().length == 0){
            warnTips("traderContactIdError"," 售后单报单人不允许为空");
            return false;
        }
        if ($("#afterConnectUserName").val().length == 0){
            warnTips("afterConnectUserNameError"," 售后联系人名不允许为空");
            return false;
        }
        if ($("#afterConnectUserName").val().length >50){
            warnTips("afterConnectUserNameError"," 售后联系人名不得超出50个字符");
            return false;
        }
        if ($("#afterConnectPhone").val().length == 0){
            warnTips("afterConnectPhoneError"," 售后联系电话不允许为空");
            return false;
        }
        if ($("#afterConnectPhone").val().length > 16){
            warnTips("afterConnectPhoneError"," 售后联系电话不得超出16位");
            return false;
        }

        /*var afterConnectPhone = $("#afterConnectPhone").val();
        var reg = /^[0-9]*$/;
        if(!reg.test(afterConnectPhone)){
            warnTips("afterConnectPhoneError"," 请输入数字字符");
            return false;
        }*/

        var province = $("#province option:selected").val();
        var city = $("#city option:selected").val();
        var zone = $("#zone option:selected").val();

        var afterSalesType =  $("#afterSalesType").val();
        if (afterSalesType.length != 0){
            //安调维修类型存在地区和服务地址
            if (afterSalesType==584 || afterSalesType==541 || afterSalesType==550 || afterSalesType==585 || afterSalesType==4090 || afterSalesType==4091){
                //最新要求，精确到区
                if (province.length == 0 || city.length == 0 || zone.length == 0){
                    warnTips("areaError"," 售后服务地区不允许为空");
                    return false;
                }
                var address = $("#address").val();
                if (address.length == 0){
                    warnTips("areaError"," 售后服务地址不允许为空");
                    return false;
                }
                if (address.length > 100){
                    warnTips("areaError"," 售后服务地址不得超出100个字符");
                    return false;
                }
            }
            //换货存在收货地址
            if (afterSalesType==540){
                var address = $("#address").val();
                if (address.length == 0){
                    warnTips("areaError"," 收货地址不允许为空");
                    return false;
                }
                if (address.length > 100){
                    warnTips("areaError"," 收货地址不得超出100个字符");
                    return false;
                }
            }
        }else{
            layer.alert("当前页面售后服务类型为空");
            return false;
        }

        $.ajax({
            url:page_url+'/order/afterSalesCommon/saveAfterSalesInfo.do',
            data:$('#editAfterSalesInfo').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data){
                if (null!=data && data.code != 0) {
                    layer.alert(data.message);
                }else {
                    $('#cancle').click();
                    parent.location.reload();
                }
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
        return false;
    });
});

function uploadFile(obj,num){
    checkLogin();
    var imgPath = $(obj).val();
    if(imgPath == '' || imgPath == undefined){
        return false;
    }
    var oldName=imgPath.substr(imgPath.lastIndexOf('\\')+1);
    var domain = $("#domain").val();
    //判断上传文件的后缀名
    var strExtension = imgPath.substr(imgPath.lastIndexOf('.') + 1);
    if (strExtension != 'jpg' && strExtension != 'gif' && strExtension != 'png' && strExtension != 'bmp'
        && strExtension != 'pdf' && strExtension != 'doc' && strExtension != 'docx') {
        $(obj).val('');
        layer.alert("文件格式不正确");
        return;
    }
    var fileSize = 0;
    var isIE = /msie/i.test(navigator.userAgent) && !window.opera;
    if (isIE && !obj.files) {
        var filePath = obj.value;
        var fileSystem = new ActiveXObject("Scripting.FileSystemObject");
        var file = fileSystem.GetFile (filePath);
        fileSize = file.Size;
    }else {
        fileSize = obj.files[0].size;
    }
    fileSize=Math.round(fileSize/1024*100)/100; //单位为KB
    if(fileSize>2048){
        layer.alert("上传附件不得超过2M");
        return false;
    }
    $.ajaxFileUpload({
        url : page_url + '/fileUpload/ajaxFileUpload.do', //用于文件上传的服务器端请求地址
        secureuri : false, //一般设置为false
        fileElementId : $(obj).attr("id"), //文件上传控件的id属性  <input type="file" id="file" name="file" /> 注意，这里一定要有name值   //$("form").serialize(),表单序列化。指把所有元素的ID，NAME 等全部发过去
        dataType : 'json',//返回值类型 一般设置为json
        complete : function() {//只要完成即执行，最后执行
        },
        //服务器成功响应处理函数
        success : function(data) {
            if (data.code == 0) {
                $("#name_"+num ).val(oldName);
                $("#uri_"+num ).val(data.filePath);
                $("#img_icon_" + num).attr("class", "iconsuccesss ml7").show();
                $("#img_view_" + num).attr("href", "http://"+ data.httpUrl + data.filePath).show();
                $("#img_del_" + num).show();
            } else {
                layer.alert(data.message);
            }
        },
        //服务器响应失败处理函数
        error : function(data, status, e) {
            if(data.status ==1001){
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }else{
                layer.alert(data.responseText);
            }

        }
    });
}


function del(num){
    var sum = Number($("#conadd").siblings(".form-blanks").length);
    var uri = $("#uri_"+num).val();
    if(uri == '' && sum > 1){
        $("#img_del_"+num).parent().parent(".form-blanks").remove();
    }else{
        index = layer.confirm("您是否确认该操作？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $("#img_icon_" + num).hide();
            $("#img_view_" + num).hide();
            if(num == 1){
                $("#img_del_" + num).hide();
            }
            $("#name_"+ num).val("");
            $("#uri_"+ num).val("");
            $("#file_"+ num).val("");
            layer.close(index);
        }, function(){
        });
    }
}

function conadd(){
    var num = Number($("#conadd").siblings(".form-blanks").length)+Number(1);
    var div = '<div class="form-blanks mt10">'+
        '<div class="pos_rel f_left">'+
        '<input type="file" class="upload_file" id="file_'+num+'" name="lwfile" style="display: none;" onchange="uploadFile(this,'+num+');">'+
        '<input type="text" class="input-largest" id="name_'+num+'" readonly="readonly"'+
        'placeholder="请上传附件" name="fileName" onclick="file_'+num+'.click();" >'+
        '<input type="hidden" id="uri_'+num+'" name="fileUri" >'+
        '</div>'+
        '<label class="bt-bg-style bt-small bg-light-blue f_left" type="file" id="busUpload" onclick="return $(\'#file_'+num+'\').click();">浏览</label>'+
        '<div class="f_left"><i class="iconsuccesss mt3 none" id="img_icon_'+num+'"></i>'+
        '<a href="" target="_blank" class="font-blue cursor-pointer mt3 none" id="img_view_'+num+'">查看</a>'+
        '<span class="font-red cursor-pointer mt3" onclick="del('+num+')" id="img_del_'+num+'">删除</span></div><div class="clear"></div>'+
        '</div>';
    $("#conadd").before(div);
}

function closeGoBack() {
    window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
}