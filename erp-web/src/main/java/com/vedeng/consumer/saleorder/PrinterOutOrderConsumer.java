package com.vedeng.consumer.saleorder;

import com.rabbitmq.client.Channel;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 出库单推送信息
 * <AUTHOR>
 */
public class PrinterOutOrderConsumer extends AbstractMessageListener {

    public static final Logger logger = LoggerFactory.getLogger(PrinterOutOrderConsumer.class);

    @Autowired
    @Qualifier("warehouseOutService")
    private WarehouseOutService warehouseOutService;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String saleorderNo = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("出库单推送信息:销售订单号{}", saleorderNo);
        if (StringUtils.isBlank(saleorderNo)) {
            logger.error("PrinterOutOrderConsumer 消息队列出库单推送信息,销售订单号为空");
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
        try {
            Saleorder saleorder = saleorderMapper.getSaleorderDataByOrderNo(saleorderNo);
            //站内信通知归属销售
            sendMessage(saleorder);
            //催办
            try{
                warehouseOutService.urgeDeliveryOrder(saleorder.getSaleorderId());
            }catch (Exception e){
                logger.info("出库单推送信息:销售订单号{},不满足催办条件", saleorderNo);
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            logger.error("出库单推送信息 error:", e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
    }

    private void sendMessage(Saleorder saleorder){
        //发送站内信
        List<Integer> userlist = new ArrayList<>();
        userlist.add(saleorder.getOptUserId());
        Map<String, String> map = new HashMap<>();
        map.put("saleorderNo",saleorder.getSaleorderNo());
        String url = "./orderstream/saleorder/detail.do?saleOrderId=" + saleorder.getSaleorderId();
        MessageUtil.sendMessage(231, userlist, map, url,"njadmin");
    }

}
