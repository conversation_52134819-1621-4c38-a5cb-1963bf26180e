<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <c:choose>
        <c:when test="${not empty param.id}"><title>编辑拜访计划</title></c:when>
        <c:otherwise><title>新建拜访计划</title></c:otherwise>
    </c:choose>
    <meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <link rel="stylesheet" href="/mstatic/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/mui/mui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/mstatic/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <jsp:include page="./head_import_im.jsp"></jsp:include>
    <input type="hidden" id="visitRecordId" name="visitRecordId" value="${param.id}"/>
    <input type="hidden" id="visitCopyId" name="visitCopyId" value="${param.copy}"/>

    <div class="page-wrap" id="page-container" style="padding-bottom: 53px;">
        <div class="page-container">
            <div class="page-main">
                <div class="business-page-wrap">

                    <div class="form-wrap" v-if="!pageLoading" style="padding-top: 10px;">
                        <div class="form-section">
                            <div class="form-section-title">拜访客户信息</div>
                            <div class="form-card">
                                <ui-form-item label="客户名称" must no-padding>
                                    <ui-trader
                                        maxlength="100"
                                        v-model="traderName"
                                        placeholder="请输入"
                                        :need-tyc="true"
                                        :need-disable="true"
                                        :trader-info="traderInfo"
                                        :trader-detail-info="traderDetailInfo"
                                        @change="handlerTrader"
                                    ></ui-trader>
                                </ui-form-item>
                                <ui-form-item label="客户等级" must>
                                    <ui-trader-level v-if="traderInfo.customerGrade" :level="traderInfo.customerGrade"></ui-trader-level>
                                    <template v-else>-</template>
                                </ui-form-item>
                                <ui-form-item label="客户类型" must>
                                    <ui-radio-group
                                        :clearable="true"
                                        :list="traderTypeList"
                                        v-model="traderType"
                                        :disabled="traderTypeDisabled"
                                        @change="traderTypeChange"
                                    ></ui-radio-group>
                                </ui-form-item>
                                <template v-if="!noContract">
                                    <ui-form-item label="手机" no-padding>
                                        <ui-phone-related
                                            v-model="mobile"
                                            placeholder="建档客户可输入手机号搜索"
                                            :trader-id="traderId"
                                            @change="handlerPhone"
                                        ></ui-phone-related>
                                    </ui-form-item>
                                    <ui-form-item label="固话" no-padding>
                                        <ui-input 
                                            v-model="telephone" 
                                            type="tel" 
                                            maxlength="20" 
                                            placeholder="需加上区号，可支持20位数字" 
                                        >
                                            <i v-if="otherContact.length < 5" class="vd-ui_icon icon-add icon" @click="addContact"></i>
                                        </ui-input>
                                    </ui-form-item>
                                    <ui-other-contact-dialog ref="otherContactDialog" @change="handlerAddContact"></ui-other-contact-dialog>
                                    <div class="other-contact-list">
                                        <div class="other-contact-item-wrap" v-for="(item, index) in otherContact"  :key="item.id + '' + index">
                                            <ui-form-item :label="item.label" no-padding>
                                                <div class="other-contact-item">
                                                    <ui-input 
                                                        class="contact-input"
                                                        maxlength="50"
                                                        v-model="item.value"
                                                        @input="handlerAddOnInput"
                                                    ></ui-input>
                                                    <div class="contact-del" @click="handlerAddDelete(index)">
                                                        <i class="vd-ui_icon icon-delete"></i>
                                                    </div>
                                                </div>
                                            </ui-form-item>
                                        </div>
                                    </div>
                                    <ui-form-item label="联系人" must no-padding>
                                        <ui-input 
                                            v-model="traderContactName" 
                                            placeholder="请输入" 
                                            maxlength="20" 
                                            @change="handlerContact"
                                        ></ui-input>
                                    </ui-form-item>
                                    <ui-form-item label="职位" must no-padding v-show="traderType && positionList.length">
                                        <ui-select 
                                            title="职位" 
                                            :list="positionList"
                                            v-model="contactPosition" 
                                            placeholder="请选择" 
                                        ></ui-select>
                                    </ui-form-item>
                                </template>
                                <ui-form-item>
                                    <ui-checkbox label="暂无联系人信息" :checked.sync="noContract"></ui-checkbox>
                                </ui-form-item>
                                <ui-form-item label="拜访地区" must no-padding>
                                    <ui-form-address
                                        ref="address"
                                        :data="addressData"
                                        v-model="area"
                                        maxlength="20"
                                        placeholder="请选择"
                                        need-trigger="true"
                                        @change="handlerArea"
                                    ></ui-form-item>
                                </ui-form-item>
                                <ui-form-item label="详细地址" vertical no-padding>
                                    <div class="address-detail">
                                        <span class="count">{{ addressDetail.length }} / 100</span>
                                        <ui-textarea
                                            v-model="addressDetail"
                                            maxlength="100"
                                            height="18px"
                                            height-auto
                                            placeholder="请输入拜访打卡的详细地址，可”查看地图“确定路线与移动端导航"
                                        ></ui-textarea>
                                        <ui-tip type="map" class="ad-tip" @click="openMap">查看地图</ui-tip>
                                    </div>
                                </ui-form-item>
                            </div>
                        </div>

                        <div class="form-section">
                            <div class="form-section-title">计划信息</div>
                            <div class="form-card">
                                <ui-form-item label="拜访人" must no-padding>
                                    <ui-wxuser-select
                                        v-model="visitors"
                                        :default-items="visitorItems"
                                        placeholder="请选择"
                                        @change="handlerVisitorChange"
                                    ></ui-wxuser-select>
                                </ui-form-item>
                                <ui-form-item label="同行人" no-padding>
                                    <ui-wxuser-select
                                        type="multi"
                                        v-model="tongxing"
                                        :default-items="defaultTongxingItems"
                                        placeholder="请选择"
                                        @change="handlerTongxingChange"
                                    ></ui-wxuser-select>
                                </ui-form-item>
                                <ui-form-item label="计划拜访时间" must no-padding>
                                    <ui-form-date-picker
                                        title="计划拜访时间"
                                        v-model="planVisitDate"
                                        :disabled-date="disabledDate"
                                        placeholder="请选择"
                                        @change="handlerDateChange"
                                    ></ui-form-date-picker>
                                </ui-form-item>
                                <ui-form-item label="拜访目标" must no-padding>
                                    <ui-form-checkbox
                                        title="拜访目标"
                                        :list="visitTypeList"
                                        v-model="visitType"
                                        placeholder="请选择"
                                        @change="handlerVisit"
                                    ></ui-form-checkbox>
                                </ui-form-item>
                                <ui-form-item label="线索/商机编号" must no-padding v-if="visitType.includes('B')">
                                    <div class="businessNo-content">
                                        <ui-input 
                                            v-model="businessNo" 
                                            maxlength="20" 
                                            placeholder="请输入"
                                            @blur="checkBusinessNo"
                                        ></ui-input>
                                        <div class="businessNo-check" :class="{'pb': businessInfo.bizNo}" v-if="businessInfo.bizNo">
                                            <div class="info-item">单据类型：{{ businessInfo.relateType == 1 ? '线索' : '商机' }}</div>
                                            <div class="info-item">客户名称：{{ businessInfo.customerName || '-' }}</div>
                                            <div class="info-item">归属销售：{{ businessInfo.salesName || '-' }}</div>
                                        </div>
                                        <ui-tip v-if="businessNoMsg" type="error" margin-bottom="10px" >{{ businessNoMsg }}</ui-tip>
                                        <ui-tip v-else-if="businessNoTip" margin-bottom="10px">{{ businessNoTip }}</ui-tip>
                                    </div>
                                </ui-form-item>
                                <ui-form-item label="备注" no-padding vertical>
                                    <ui-textarea
                                        maxlength="1000"
                                        show-word-limit
                                        placeholder="描述客户当前需求或痛点、以及本次拜访期望解决的具体问题。"
                                        v-model="remark"
                                        height="54px"
                                        height-auto
                                    ></ui-textarea>
                                </ui-form-item>
                            </div>
                        </div>
                    </div>

                    <div class="form-fixed-bottom">
                        <div class="submit-btn padding">
                            <vd-ui-button @click="submit" type="primary">提交</vd-ui-button>
                        </div>
                    </div>


                    
                    <!-- DEMO -->
                    <!-- <button @click="successDialog" style="margin-right: 20px;">success</button>
                    <button @click="errorDialog" style="margin-right: 20px;">error</button>
                    <button @click="warnDialog" style="margin-right: 20px;">warn</button>
                    <button @click="infoDialog" style="margin-right: 20px;">info</button>
                    <button @click="customDialog">customDialolg</button>
                    <button @click="message">message</button>
                    <button @click="successMessage">success</button>
                    <button @click="errorMessage">error</button>
                    <button @click="warnMessage">warn</button>
                    <button @click="infoMessage">info</button>
                    <ui-custom-dialog
                        title="自定义筛选"
                        width="500px"
                        :is-show.sync="isShowCustomDialog" 
                        :right-btn-disable="!customVal.length"
                        @handleLeftBtn="handleLeftBtn" 
                        @handleRightBtn="handleRightBtn"
                    >
                        <div class="form">
                            <div class="form-item">
                                标签名称： <input type="text" class="input" v-model="customVal" />
                            </div>
                        </div>
                    </ui-custom-dialog> -->

                </div>
            </div>
        </div>
    </div>

    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script src="/mstatic/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/mui/mui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/mstatic/js/pages/visitRecord/add.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
