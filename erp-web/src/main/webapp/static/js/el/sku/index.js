$(function(){
});

//删除职位
function delElSku(elSkuId){
    checkLogin();
    if(elSkuId > 0){
        layer.confirm("您是否确认删除？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "POST",
                url: "./deleteElSku.do",
                data: {'elSkuId':elSkuId},
                dataType:'json',
                success: function(data){
                    refreshPageList(data);//刷新父页面列表数据
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function(){
        });
    }
}