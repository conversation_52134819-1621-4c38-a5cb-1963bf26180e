<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="saleorder" type="com.vedeng.order.model.Saleorder" required="true" %>
<%@attribute name="orderCheckStatus" type="java.lang.Integer" required="true" %>
<%@attribute name="riskFlag" type="java.lang.Integer" required="true" %>
<%@attribute name="isNotDelPriceZero" type="java.lang.Integer" required="true" %>
<%@attribute name="saleInvoiceApplyList" type="java.util.List" required="true" %>
<%@attribute name="customer" type="com.vedeng.trader.model.vo.TraderCustomerVo" required="true" %>
<%@attribute name="taskInfo" type="java.lang.Object" required="true" %>
<%@attribute name="verifyUserList" type="java.util.List" required="true" %>
<%@attribute name="currentUser" type="com.vedeng.authorization.model.User" required="true" %>
<%@attribute name="candidateUserMap" type="java.util.Map" required="true" %>
<%@attribute name="shInfoJudge" type="java.lang.Integer" required="true" %>
<%@attribute name="pendingOrderFlag" type="java.lang.Boolean" required="true" %>
<%@attribute name="isShowInvoiceBtn" type="java.lang.Integer" required="true" %>
<%@attribute name="contractUrl" type="java.lang.String" required="true" %>
<%@attribute name="taskInfoContractReturn" type="java.lang.Object" required="true" %>
<%@attribute name="candidateUserMapContractReturn" type="java.util.Map" required="true" %>
<%@attribute name="verifyUserListContractReturn" type="java.util.List" required="true" %>
<%@ attribute name="statusAllCheck" type="java.lang.Boolean" required="true" %>
<%@ attribute name="beforeConfOnline" type="java.lang.Boolean" required="true" %>
<%@ attribute name="showButtonApplyInvoice" type="java.lang.Boolean" required="true" %>

<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>

<div class="parts">
    <div class="text-center mb15">
        <c:if test="${(saleorder.status == 0 && saleorder.validStatus == 0 && (orderCheckStatus == -1 || orderCheckStatus == 2))
        || (saleorder.orderType == 8 && saleorder.status == 4)}">
            <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 addtitle" tabTitle='{"num":"order_saleorder_edit<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"./orderstream/saleorder/edit.do?saleorderId=${saleorder.saleorderId}&scene=0","title":"编辑订单"}'>编辑订单</button>

            <c:choose>
                <c:when test="${saleorder.orderType == 8}">
                    <c:if test="${riskFlag eq 1 && saleorder.status != 4}">
                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidSaleOrderOfNew(${saleorder.saleorderId},
                            ${saleorder.paymentType},${taskInfo.id == null ?0: taskInfo.id},${isNotDelPriceZero})">提交审核</button>
                    </c:if>
                </c:when>
                <c:otherwise>
                    <c:if test="${riskFlag eq 1}">
                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="applyValidSaleOrderOfNew(${saleorder.saleorderId},
                            ${saleorder.paymentType},${taskInfo.id == null ?0: taskInfo.id},${isNotDelPriceZero})">提交审核</button>
                    </c:if>
                </c:otherwise>
            </c:choose>
        </c:if>

        <%--在“打印”左侧展示“电子签章”按钮--%>
        <c:if test="${ (saleorder.status eq 1 || saleorder.status eq 2) && saleorder.validStatus eq 1}">
            <span class="bg-light-blue bt-bg-style bt-small addtitle"
                  tabTitle='{"num":"electronic_signature_${saleorder.saleorderId }","link":"./orderstream/saleorder/electronicSignature.do?saleorderId=${saleorder.saleorderId}&traderId=${saleorder.traderId}","title":"电子签章"}'>电子签章</span>
        </c:if>

        <c:if test="${saleorder.validStatus == 0 && saleorder.status!=3}">
	        <span class="bg-light-green bt-bg-style bt-small addtitle" tabTitle='{"num":"warehousesout_viewOutDetail_${saleorder.saleorderId }",
				"link":"./orderstream/saleorder/contract_template/print.do?saleorderId=${saleorder.saleorderId}&autoGenerate=false&noStamp=true","title":"打印预览"}'>合同预览</span>
        </c:if>

        <c:if test="${saleorder.status == 1 || saleorder.status == 2}">
            <span class="bg-light-green bt-bg-style bt-small addtitle"
                  tabTitle='{"num":"warehousesout_viewOutDetail_${saleorder.saleorderId }","link":"./orderstream/saleorder/contract_template/print.do?saleorderId=${saleorder.saleorderId}&comment=${saleorder.invoiceComments}&autoGenerate=false&noStamp=true","title":"打印合同"}'>打印合同</span>
            <c:if test="${contractUrl != null && contractUrl ne ''}">
                <a class="bg-light-green bt-bg-style bt-small" style="display: inline-block;height: 26px;width: 80px" href="${contractUrl}">下载合同</a>
            </c:if>
        </c:if>

        <c:if test="${((saleorder.status == 0 or saleorder.status == 1) and saleorder.invoiceStatus == 0
        and saleorder.deliveryStatus == 0 and saleorder.paymentStatus == 0   and saleorder.lockedStatus eq 0)
        || (saleorder.orderType == 8 && saleorder.status == 4)}">
            <button type="button" class="bt-bg-style bg-light-orange bt-small mr10" onclick="closeSaleorder(${saleorder.saleorderId},${saleorder.bussinessChanceNo})">关闭订单</button>
        </c:if>

        <c:if test="${saleorder.status != 3 &&  saleorder.status != 2 && saleorder.validStatus == 0}">
            <c:if test="${(null!=taskInfo and null!=taskInfo.getProcessInstanceId() and null!=taskInfo.assignee) or !empty candidateUserMap[taskInfo.id]}">
                <c:set var="shenhe" value="0" />
                <c:forEach items="${verifyUserList}" var="verifyUsernameInfo">
                    <c:if test="${verifyUsernameInfo == currentUser.username}">
                        <c:set var="shenhe" value="1" />
                    </c:if>
                </c:forEach>
                <c:choose>
                    <c:when test="${(taskInfo.assignee == currentUser.username or candidateUserMap['belong']) and shenhe!=1 and taskInfo.name != '产品线归属人审核'}">
                        <button type="button" onclick="operationCheck(${saleorder.saleorderId})" class="bt-bg-style bg-light-green bt-small mr10">审核通过</button>
                        <button type="button" id="orderComplement" class=" pop-new-data"
                                layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/saleorder/complement.do?taskId=${taskInfo.id}&saleorderId=${saleorder.saleorderId}&pass=true&type=1003"}'></button>
                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/saleorder/complement.do?taskId=${taskInfo.id}&saleorderId=${saleorder.saleorderId}&pass=false&type=1003"}'>审核不通过</button>
                    </c:when>
                    <c:when test="${(taskInfo.assignee == currentUser.username or candidateUserMap['belong']) and shenhe!=1 and taskInfo.name == '产品线归属人审核'}">
                        <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data"  layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/saleorder/complement.do?taskId=${taskInfo.id}&pass=true&type=1"}'>审核通过</button>
                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"/order/saleorder/complement.do?taskId=${taskInfo.id}&pass=false&type=1"}'>审核不通过</button>
                    </c:when>
                    <c:otherwise>
                        <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请审核</button>
                    </c:otherwise>
                </c:choose>
            </c:if>
        </c:if>

        <c:if test="${showButtonApplyInvoice}">
            <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="isTraderAllowInvoice(${saleorder.traderId},'${saleorder.traderName}',${saleorder.invoiceType},1,${saleorder.saleorderId})">申请开票</button>
            <span id="invoiceApply" class="pop-new-data-noclose" layerParams='{"width":"80%","height":"620px","title":"","link":"/finance/invoice/selectInvoiceItems.do?relatedId=${saleorder.saleorderId}&comment=${saleorder.invoiceComments}&isAuto=${saleorder.invoiceMethod}&invoiceType=${saleorder.invoiceType}&editFlag=true"}'></span>
        </c:if>

            <!-- 未锁定 -->
        <c:if test="${saleorder.lockedStatus eq 0 and saleorder.validStatus eq 1}">
            <!-- 1允许开票2存在开票申请待审核-不允许;;;;:3允许提前开票，4存在提前开票待审核-不允许::::5全部开票----0全部售后 -->
            <%--票货同行不可申请提前开票,申请开票--%>
<%--            <c:if test="${(saleorder.isSameAddress ne 1 && saleorder.isOpenInvoice eq 1)--%>
<%--              || (saleorder.isSameAddress eq 1 &&--%>
<%--              (shInfoJudge eq 1 or (isShowInvoiceBtn eq 1 && (currentUser.positionName eq '销售总监' || isParent eq 1))) && saleorder.isOpenInvoice eq 1)}">--%>
<%--                <button type="button" class="bt-bg-style bg-light-green bt-small mr10" onclick="isTraderAllowInvoice(${saleorder.traderId},'${saleorder.traderName}',${saleorder.invoiceType},1)">申请开票</button>--%>
<%--            </c:if>--%>
<%--            <button isOpenInvoice="${saleorder.isOpenInvoice}"--%>
<%--                    isSameAddress="${saleorder.isSameAddress}"--%>
<%--                    shInfoJudge="${shInfoJudge}"--%>
<%--                    isShowInvoiceBtn="${isShowInvoiceBtn}"--%>
<%--                     positionName="${currentUser.positionName}"--%>
<%--                    invoiceType="${saleorder.invoiceType}"--%>
<%--                    style="display:none"  type="button" class="bt-bg-style bg-light-grey bt-small mr10" onclick="javascript:alert('开票条件：已生效未关闭、未锁定、付款状态全部付款、开票状态为未开票或者部分开票、未设置延迟开票；发货状态为部分发货或者全部发货；收货状态为部分收货或者全部收货；开票方式非手动纸质票；对公+银行 +付款方等于客户名称（非账期支付） 流水金额 大于等于实际金额；  已开票金额小于实际金额；')" >申请开票</button>--%>
<%--            <span id="invoiceApply" class="pop-new-data"--%>
<%--                  layerParams='{"width":"80%","height":"720px","title":"","link":"/finance/invoice/selectInvoiceItems.do?relatedId=${saleorder.saleorderId}&comment=${saleorder.invoiceComments}&isAuto=${saleorder.invoiceMethod}&invoiceType=${saleorder.invoiceType}&isAdvance=0&editFlag=true"}'></span>--%>
<%--            <c:if test="${(saleorder.isSameAddress ne 1 && saleorder.isOpenInvoice eq 3) || (saleorder.isSameAddress eq 1 && (shInfoJudge eq 1 or (isShowInvoiceBtn eq 1 && (currentUser.positionName eq '销售总监' || isParent eq 1))) && saleorder.isOpenInvoice eq 3)}">--%>
<%--                <button type="button" class="bt-bg-style bg-light-orange bt-small mr10"  onclick="isTraderAllowInvoice(${saleorder.traderId},'${saleorder.traderName}',${saleorder.invoiceType},2)">申请开票</button>--%>
<%--                <span id="advanceInvoiceApply" class="pop-new-data" layerParams='{"width":"80%","height":"720px","title":"","link":"/finance/invoice/selectInvoiceItems.do?relatedId=${saleorder.saleorderId}&comment=${saleorder.invoiceComments}&isAuto=${saleorder.invoiceMethod}&invoiceType=${saleorder.invoiceType}&isAdvance=1&editFlag=true"}'></span>--%>
<%--            </c:if>--%>
<%--            <c:if test="${saleorder.isOpenInvoice eq 5 or saleorder.isOpenInvoice eq 2 or saleorder.isOpenInvoice eq 4}">--%>
<%--                <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请开票</button>--%>
<%--            </c:if>--%>

            <c:if test="${saleorder.status == 1}">
                <c:choose>
                    <c:when test="${saleorder.paymentStatus == 0 && saleorder.deliveryStatus == 0 && saleorder.arrivalStatus == 0 &&
                    saleorder.invoiceStatus == 0 && empty saleInvoiceApplyList}">
                        <button type="button" class="bt-bg-style bg-light-orange bt-small mr10" onclick="cancelValidOfSaleOrder(${saleorder.saleorderId})">撤销生效</button>
                    </c:when>
                </c:choose>
            </c:if>
            <c:if test="${saleorder.status == 1 || (saleorder.status == 2 && saleorder.invoiceStatus ne 2)}">
                <button type="button" class="bt-bg-style bg-light-orange bt-small mr10" onclick="applyToModifySaleorderForTrader(${saleorder.saleorderId}, 0)">申请修改订单</button>
            </c:if>

        </c:if>

        <c:choose>
            <c:when test="${saleorder.validStatus == 1 && saleorder.paymentStatus == 0 && saleorder.paymentType == 423}">
                <button type="button" class="bt-bg-style bg-light-orange bt-small mr10" onclick="paymentByCredit(${saleorder.saleorderId})">信用支付</button>
            </c:when>
            <c:otherwise>
                <c:if test="${saleorder.status == 1 && saleorder.validStatus == 1 && saleorder.lockedStatus == 0 && customer.amount gt '0.00'}">
                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data"
                            layerParams='{"width":"600px","height":"350px","title":"余额支付","link":"/order/saleorder/initBalancePayment.do?saleorderId=${saleorder.saleorderId}"}'>余额支付</button>
                </c:if>
            </c:otherwise>
        </c:choose>

        <c:if test="${pendingOrderFlag}">
            <button type="button" class="bt-bg-style bg-light-blue bt-small mr10 addtitle" tabTitle='{"num":"order_saleorder_pending_order<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>","link":"orderstream/saleorder/pendingOrderIndex.do?saleorderNo=${saleorder.saleorderNo}","title":"发送签收通知"}'>发送签收通知</button>
        </c:if>
        <c:if test="${saleorder.status ne 0 && saleorder.status ne 4 && saleorder.lockedStatus eq 0 && (saleorder.orderType eq 8 or saleorder.orderType eq 9)}">
            <span class="bg-light-green bt-bg-style bt-small addtitle"
                  tabTitle='{"num":"warehousesout_viewOutDetail_${saleorder.saleorderId}","link":"./orderstream/saleorder/saveCopyOrder.do?saleOrderId=${saleorder.saleorderId}&formToken=${formToken}","title":"复制订单"}'>复制订单</span>
        </c:if>

        <c:if test="${(null!=taskInfoContractReturn and null!=taskInfoContractReturn.getProcessInstanceId() and null!=taskInfoContractReturn.assignee) or !empty candidateUserMapContractReturn[taskInfoContractReturn.id]}">
            <c:set var="hetongshenhe" value="0"></c:set>
            <c:forEach items="${verifyUserListContractReturn}" var="verifyUsernameInfo">
                <c:if test="${verifyUsernameInfo == currentUser.username}">
                    <c:set var="hetongshenhe" value="1"></c:set>
                </c:if>
            </c:forEach>

            <c:choose>
                <c:when test="${(taskInfoContractReturn.assignee == currentUser.username or candidateUserMapContractReturn['belong']) and hetongshenhe!=1}">
                    <button type="button" class="bt-bg-style bg-light-green bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoContractReturn.id}&pass=true&type=1"}'>合同审核通过</button>
                    <button type="button" class="bt-bg-style bg-light-orange bt-small mr10 pop-new-data" layerParams='{"width":"500px","height":"180px","title":"操作确认","link":"./complement.do?taskId=${taskInfoContractReturn.id}&pass=false&type=1"}'>合同审核不通过</button>
                </c:when>
                <c:otherwise>
                    <button type="button" class="bt-bg-style bt-small bg-light-greybe mr10">已申请合同审核</button>
                </c:otherwise>
            </c:choose>
        </c:if>


        <a id="toCustomerInfo" class="addtitle" href="javascript:void(0);" tabtitle=""></a>
        <input type="hidden" id="saleorderId" value="${saleorder.saleorderId}">
        <input type="hidden" id="orderType" value="${saleorder.orderType}">

    </div>
</div>

<script>

    /**
     * 兼容线上订单，BD订单撤销之后不允许重定向到新订单流详情页
     * @param saleOrderId
     */
    function cancelValidOfSaleOrder(saleOrderId){
        layer.confirm("您是否确认撤销生效该订单吗？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "GET",
                url: "/orderstream/saleorder/valid/cancel.do?saleOrderId=" + saleOrderId,
                dataType:'json',
                success: function(data){
                    if (data.code === 0){
                        if ($('#orderType').val() == 1){
                            window.location.replace("/order/saleorder/view.do?saleorderId="+$('#saleorderId').val()+"&scene=0&isOnlineOrder=1")
                        } else {
                            window.location.reload();
                        }
                    } else {
                        layer.alert(data.message);
                    }

                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                    }
                }
            });
        }, function(){
        });
    }

    function paymentByCredit(saleOrderId){
        layer.confirm("您是否确认对该订单进行信用支付？", {
            btn: ['确定','取消'] //按钮
        }, function(){
            $.ajax({
                type: "GET",
                url: "/orderstream/saleorder/paymentByCredit.do?saleOrderId="+saleOrderId,
                dataType:'json',
                success: function(data){
                    if (data.code === 0){
                        window.location.reload();
                    } else {
                        layer.alert(data.message);
                    }

                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        }, function(){
        });
    }
</script>