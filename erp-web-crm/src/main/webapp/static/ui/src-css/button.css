@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-button {
  font-size: 12px;
  padding: 5px 14px;
  text-align: center;
  border-radius: 3px;
  border: 1px solid #BABFC2;
  display: inline-block;
  height: max-content;
  background-color: #F5F7FA;
  color: #333333;
  transition: all 0.1s linear;
  cursor: pointer;
  position: relative;
}
.vd-ui-button .vd-ui_icon {
  position: relative;
  top: 2px;
  font-size: 16px;
  margin-right: 3px;
  display: inline-block;
  line-height: 1;
  margin-left: -5px;
  vertical-align: -1px;
}
.vd-ui-button .loading {
  animation: loading 1.8s linear infinite;
}
.vd-ui-button.vd-ui-button--large {
  font-size: 16px;
  padding: 7.5px 19px;
}
.vd-ui-button.vd-ui-button--large .vd-ui_icon {
  font-size: 18px;
  margin-right: 7px;
}
.vd-ui-button.vd-ui-button--middle {
  font-size: 14px;
  padding: 5px 14px;
}
.vd-ui-button.vd-ui-button--middle .vd-ui_icon {
  font-size: 16px;
}
.vd-ui-button.vd-ui-button--small {
  font-size: 12px;
  padding: 3px 9px;
}
.vd-ui-button.vd-ui-button--small .vd-ui_icon {
  font-size: 14px;
}
.vd-ui-button:hover {
  background-color: #EBEFF2;
}
.vd-ui-button:active {
  background-color: #E1E5E8;
}
.vd-ui-button.is-disabled {
  cursor: not-allowed;
  color: #999999;
  border-color: #D7DADE;
  background-color: #F5F7FA;
}
.vd-ui-button.vd-ui-button--primary {
  border-color: #0099FF;
  background-color: #0099FF;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--primary:hover {
  border-color: #0087e0;
  background-color: #0087e0;
}
.vd-ui-button.vd-ui-button--primary:active {
  border-color: #006cb3;
  background-color: #006cb3;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--primary.is-disabled {
  cursor: not-allowed;
  border-color: #7fccff;
  background-color: #7fccff;
}
.vd-ui-button.vd-ui-button--primary.is-loading {
  cursor: wait;
  border-color: #7fccff;
  background-color: #7fccff;
}
.vd-ui-button.vd-ui-button--success {
  border-color: #13bf13;
  background-color: #13bf13;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--success:hover {
  border-color: #11a811;
  background-color: #11a811;
}
.vd-ui-button.vd-ui-button--success:active {
  border-color: #0d860d;
  background-color: #0d860d;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--success.is-disabled {
  cursor: not-allowed;
  border-color: #88df89;
  background-color: #88df89;
}
.vd-ui-button.vd-ui-button--warning {
  border-color: #FF6600;
  background-color: #FF6600;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--warning:hover {
  border-color: #e05a00;
  background-color: #e05a00;
}
.vd-ui-button.vd-ui-button--warning:active {
  border-color: #b34800;
  background-color: #b34800;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--warning.is-disabled {
  cursor: not-allowed;
  border-color: #feb380;
  background-color: #feb380;
}
.vd-ui-button.vd-ui-button--danger {
  border-color: #E64545;
  background-color: #E64545;
  color: #ffffff;
}
.vd-ui-button.vd-ui-button--danger:hover {
  border-color: #ca3d3d;
  background-color: #ca3d3d;
}
.vd-ui-button.vd-ui-button--danger:active {
  border-color: #a23131;
  background-color: #a23131;
  transition: all 0.1s linear;
}
.vd-ui-button.vd-ui-button--danger.is-disabled {
  cursor: not-allowed;
  border-color: #f1a2a2;
  background-color: #f1a2a2;
}
.vd-ui-button.vd-ui-button--text {
  border: none;
  background-color: transparent;
}
.vd-ui-button.vd-ui-button--text:hover {
  color: #0099FF;
}
.vd-ui-button.vd-ui-button--text.is-disabled {
  cursor: not-allowed;
  color: #999999;
}
.vd-ui-button.vd-ui-button--danger-text {
  border: none;
  color: #E64545;
  background-color: transparent;
}
.vd-ui-button.vd-ui-button--danger-text:hover {
  color: #ca3d3d;
}
.vd-ui-button.vd-ui-button--danger-text.is-disabled {
  cursor: not-allowed;
  color: #f1a2a2;
}
.vd-ui-button.vd-ui-button--success-text {
  border: none;
  background-color: transparent;
  color: #13bf13;
}
.vd-ui-button.vd-ui-button--success-text:hover {
  color: #11a811;
}
.vd-ui-button.vd-ui-button--success-text.is-disabled {
  cursor: not-allowed;
  color: #88df89;
}
.vd-ui-button.vd-ui-button--link-text {
  border: none;
  color: #0099FF;
  background-color: transparent;
}
.vd-ui-button.vd-ui-button--link-text:hover {
  color: #0087e0;
}
.vd-ui-button.vd-ui-button--link-text.is-disabled {
  cursor: not-allowed;
  color: #7fccff;
}
.vd-ui-select-button {
  display: flex;
  align-items: center;
  position: relative;
  margin-right: 10px;
}
.vd-ui-select-button .ui-select-btn-txt {
  background: #0099FF;
  color: #fff;
  border-radius: 3px 0 0 3px;
  border-right: 1px solid #0087E0;
  cursor: pointer;
  font-size: 12px;
}
.vd-ui-select-button .ui-select-btn-txt:hover {
  background: #0087E0;
}
.vd-ui-select-button .ui-select-btn-txt > div {
  padding: 6px 10px;
}
.vd-ui-select-button .ui-select-btn-more {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background: #0099FF;
  border-radius: 0 3px 3px 0;
  cursor: pointer;
}
.vd-ui-select-button .ui-select-btn-more .icon-down {
  font-size: 16px;
  line-height: 1;
  transition: transform 0.22s ease;
}
.vd-ui-select-button .ui-select-btn-more:hover {
  background: #0087E0;
}
.vd-ui-select-button .ui-select-btn-drop {
  background: #fff;
  border-radius: 3px;
  border: 1px solid #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 30px;
  left: 0;
  z-index: 9;
  width: 100%;
  padding: 5px 0;
  display: none;
}
.vd-ui-select-button .ui-select-btn-drop > div {
  padding: 6px 10px;
  cursor: pointer;
}
.vd-ui-select-button .ui-select-btn-drop > div:hover {
  background: #f5f7fa;
}
.vd-ui-select-button.open .ui-select-btn-drop {
  display: block;
}
.vd-ui-select-button.open .ui-select-btn-more .icon-down {
  transform: rotate(180deg);
}
.vd-ui-select-link {
  position: relative;
}
.vd-ui-select-link .ui-select-link-trigger {
  display: flex;
  align-items: center;
  color: #09f;
  cursor: pointer;
  font-size: 12px;
}
.vd-ui-select-link .ui-select-link-trigger:hover {
  color: #f60;
}
.vd-ui-select-link .ui-select-link-trigger .icon-down {
  font-size: 16px;
  line-height: 1;
  transition: transform 0.22s ease;
  margin-left: 3px;
}
.vd-ui-select-link .ui-select-btn-drop {
  background: #fff;
  border-radius: 3px;
  border: 1px solid #BABFC2;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
  position: absolute;
  top: 18px;
  left: 0;
  z-index: 9;
  width: 100%;
  padding: 5px 0;
  display: none;
}
.vd-ui-select-link .ui-select-btn-drop > div {
  padding: 6px 10px;
  cursor: pointer;
}
.vd-ui-select-link .ui-select-btn-drop > div:hover {
  background: #f5f7fa;
}
.vd-ui-select-link.open .ui-select-btn-drop {
  display: block;
}
.vd-ui-select-link.open .icon-down {
  transform: rotate(180deg);
}
