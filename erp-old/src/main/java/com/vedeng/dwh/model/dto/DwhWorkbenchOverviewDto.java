package com.vedeng.dwh.model.dto;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * workbench.
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2020/11/3 11:09 下午.
 * @author: Tomcat.Hui.
 */

@Data
public class DwhWorkbenchOverviewDto  {

    // add by <PERSON>.Xu 2020/11/6 10:56 .Desc: . begin
    private String orgName;
    // add by <PERSON><PERSON> 2020/11/6 10:56 .Desc: . end

    private Integer status;

    private Integer orgId;

    private BigDecimal totalBussinessChanceAmount;

    private Integer totalBussinessChanceNum;

    private BigDecimal totalExpectAmount;

    // changed by <PERSON>.<PERSON>   .Desc: begin
    private BigDecimal ssTotalAmount;

    private BigDecimal aaTotalAmount;

    private Integer ssTotalNum;

    private Integer aaTotalNum;
    // changed by <PERSON><PERSON>   .Desc: end

}
