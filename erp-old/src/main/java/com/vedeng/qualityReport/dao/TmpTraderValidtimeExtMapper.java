package com.vedeng.qualityReport.dao;

import com.vedeng.qualityReport.dao.generate.TmpTraderValidtimeMapper;
import com.vedeng.qualityReport.model.TmpTraderValidtimeExtDo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TmpTraderValidtimeExtMapper extends TmpTraderValidtimeMapper {

    List<TmpTraderValidtimeExtDo> getAllValidTraderSupplier();

    List<TmpTraderValidtimeExtDo> getAllValidTraderCustomer();

    TmpTraderValidtimeExtDo getTraderCustomerValidTime(@Param("traderCustomerId") Integer traderCustomerId);

    TmpTraderValidtimeExtDo getTraderSupplyValidTime(@Param("traderSupplierId") Integer traderSupplierId);

    Integer getcount(TraderCustomerVo traderCustomerInfo);
}