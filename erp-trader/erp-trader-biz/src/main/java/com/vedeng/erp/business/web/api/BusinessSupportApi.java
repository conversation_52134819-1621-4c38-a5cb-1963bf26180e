package com.vedeng.erp.business.web.api;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.business.domain.dto.SupportRecordDto;
import com.vedeng.erp.business.domain.dto.SupportRecordReqDto;
import com.vedeng.erp.business.dto.BusinessRequestDto;
import com.vedeng.erp.business.dto.BusinessSupportReqDto;
import com.vedeng.erp.business.dto.NodeDto;
import com.vedeng.erp.business.service.BusinessSupportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@ExceptionController
@RestController
@RequestMapping("/support")
@Slf4j
public class BusinessSupportApi {

    @Autowired
    private BusinessSupportService businessSupportService;

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> add(@RequestBody BusinessSupportReqDto businessSupportReqDto) {
        log.info("添加支持记录入参：{}", JSON.toJSON(businessSupportReqDto));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        businessSupportService.saveSupportRecord(businessSupportReqDto,currentUser);
        return R.success();
    }

    @RequestMapping(value = "/addRequest", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> addRequest(@RequestBody BusinessRequestDto businessRequestDto) {
        log.info("添加咨询入参：{}", JSON.toJSON(businessRequestDto));
        CurrentUser currentUser = CurrentUser.getCurrentUser();
        businessSupportService.saveSeekHelp(businessRequestDto,currentUser);
        return R.success();
    }

    @RequestMapping(value = "/getSupportList", method = RequestMethod.GET)
    @NoNeedAccessAuthorization
    public R<List<NodeDto>> getSupportList() {
        log.info("获取咨询方案的所有人以及部门");
        List<NodeDto> supportList = businessSupportService.getSupportList();
        return R.success(supportList);
    }

    @RequestMapping(value = "/getSupportRecordList", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<List<SupportRecordDto>> getSupportRecordList(@RequestBody SupportRecordReqDto supportRecordReqDto) {
        List<SupportRecordDto> supportRecordDtoList = businessSupportService.getSupportRecordList(supportRecordReqDto);
        return R.success(supportRecordDtoList);
    }
}
