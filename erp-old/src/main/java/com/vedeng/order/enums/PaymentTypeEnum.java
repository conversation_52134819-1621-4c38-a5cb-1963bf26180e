package com.vedeng.order.enums;

import lombok.Getter;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/28 9:34
 */
@Getter
public enum PaymentTypeEnum {

    PAY_BEFORE(419, "先款后货，预付100%"), PAY_80(420, "先货后款，预付80%"),
    PAY_50(421, "先货后款，预付50%"), PAY_30(422, "先货后款，预付30%"),
    PAY_0(423, "先货后款，预付0%"), PAY_OTHER(424, "自定义");

    PaymentTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    private final Integer type;

    private final String desc;

}
