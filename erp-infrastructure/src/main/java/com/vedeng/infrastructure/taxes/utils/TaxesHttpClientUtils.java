package com.vedeng.infrastructure.taxes.utils;

import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.exception.ServiceException;
import io.netty.channel.ConnectTimeoutException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.net.SocketTimeoutException;

/**
 * 税金HttpClientUtil
 */
@Slf4j
public class TaxesHttpClientUtils  {

    /**
     * post
     * @param interfaceCode
     * @param jsonStr
     * @param url
     * @return
     */
    public static String post(String interfaceCode, String jsonStr, String url) {
        CloseableHttpResponse response;
        String result;
        try (CloseableHttpClient client = HttpClients.createDefault()){
            // 构建Post请求对象
            HttpPost post = new HttpPost(url);
            // 设置传送的内容类型是json格式
            post.setHeader("Content-Type", "application/json;charset=UTF-8");
            // 接收的内容类型也是json格式
            post.setHeader("Accept", "application/json;charset=UTF-8");
            // 设置超时时间，connetionTimeout（客户端和服务器建立连接的超时时间）、connectionRequestTimout（从连接池获取连接的超时时间）、socketTimeout（客户端从服务器读取数据的超时时间）
            RequestConfig config = RequestConfig.custom()
                    .setConnectTimeout(TaxesUtil.taxesConfig.taxesConnectTimeout)
                    .setSocketTimeout(TaxesUtil.taxesConfig.taxesSocketTimeout)
                    .build();
            post.setConfig(config);

            post.setEntity(new StringEntity(jsonStr));

            log.info("远程调用税金系统开始，{},接口代码：{}，入参：{}",url,interfaceCode,jsonStr);
            response = client.execute(post);
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity,"UTF-8");
            String unescapeJavaResult = StringEscapeUtils.unescapeJava(result);
            log.info("远程调用税金系统结束:{},接口代码：{}，返回：{}",url,interfaceCode,unescapeJavaResult);
        } catch (SocketTimeoutException | ConnectTimeoutException e) {
            log.error("调用税金接口出现连接/超时异常",e);
            throw new ServiceException(BaseResponseCode.TAXES_TIMEOUT);
        } catch (Exception e) {
            log.error("调用税金接口出现异常",e);
            throw new ServiceException(BaseResponseCode.TAXES_FAIL);
        }
        return result;
    }
}
