package com.vedeng.order.dao;


import com.vedeng.order.model.QuoteLinkBdLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteLinkBdLogMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_QUOTE_LINK_BD_LOG
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    int deleteByPrimaryKey(Integer quoteLinkBdLogId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_QUOTE_LINK_BD_LOG
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    int insert(QuoteLinkBdLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_QUOTE_LINK_BD_LOG
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    int insertSelective(QuoteLinkBdLog record);


    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_QUOTE_LINK_BD_LOG
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    QuoteLinkBdLog selectByPrimaryKey(Integer quoteLinkBdLogId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_QUOTE_LINK_BD_LOG
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    int updateByPrimaryKeySelective(QuoteLinkBdLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_QUOTE_LINK_BD_LOG
     *
     * @mbggenerated Tue Oct 13 10:45:00 CST 2020
     */
    int updateByPrimaryKey(QuoteLinkBdLog record);

    QuoteLinkBdLog getLinkLog(QuoteLinkBdLog log);

    /**
     * 报价单ID信息检索关联信息
     *
     * @param quoteId
     * @return
     */
    QuoteLinkBdLog getLinkLogByQuoteId(Integer quoteId);
}