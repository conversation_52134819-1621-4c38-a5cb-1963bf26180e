package com.vedeng.erp.finance.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 发票金蝶凭证号
 */
@Getter
@Setter
public class InvoiceVoucherEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long invoiceVoucherId;

    /**
     * 发票id
     */
    private String invoiceId;

    /**
     * 凭证号
     */
    private String voucherNo;

    /**
     * 是否删除 0 否 1是
     */
    private Integer isDelete;

    /**
     * 记账日期（yyyy-M）
     */
    private String voucherDate;

    /**
     * 凭证URL
     */
    private String voucherUrl;
}