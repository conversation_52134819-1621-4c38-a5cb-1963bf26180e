void function () {

    new Vue({
        el: '#page-container',
        data() {
            return {
                blockList: [
                    {
                        title: '质量条款',
                        list: [
                            {
                                txt: '甲方承诺本次采购的AED仅限甲方企业内部接受过心肺复苏和自动体外除额器使用培训合格的人员使用，绝不涉及对外销售、转赠、交换等经营行为，AED的安装地址为：xxx。',
                                id: 101,
                                checked: false,
                                customInfo: [{
                                    label: '安装地址',
                                    name: 'installationAddress',
                                    maxlength: 200,
                                    placeholder: '此处写清安装地址，具体到门牌号',
                                    value: '',
                                    errorMessage: ''
                                }]
                            },
                            {
                                txt: '甲方承诺购买本合同产品后销往客户xxx，客户xxx不用于医疗用途，乙方将按照甲方提供的收货信息送货，甲方为提供的收货信息真实性负责。',
                                id: 102,
                                checked: false,
                                customInfo: [{
                                    label: '客户名称',
                                    name: 'trader<PERSON>ame',
                                    maxlength: 100,
                                    value: '',
                                    errorMessage: ''
                                }]
                            },
                            {
                                txt: 'xxx产品为非医疗器械，不按照《医疗器械监督管理条例》进行管理。甲方承诺购买本合同产品后不得销往医疗机构，不得用于医疗用途。',
                                id: 103,
                                checked: false,
                                customInfo: [{
                                    label: '产品名称',
                                    name: 'prodName103',
                                    maxlength: 200,
                                    value: '',
                                    errorMessage: '',
                                }]
                            },
                            {
                                txt: '甲方承诺本合同产品只限甲方企业内部使用，绝不对外销售，不用于医疗用途。',
                                id: 104,
                                checked: false,
                            },
                            {
                                txt: 'xxx产品为非医疗器械，不按照《医疗器械监督管理条例》进行管理。甲方承诺本合同产品只限甲方企业内部使用，绝不对外销售，不用于医疗用途。',
                                id: 105,
                                checked: false,
                                customInfo: [{
                                    label: '产品名称',
                                    name: 'prodName105',
                                    maxlength: 200,
                                    value: '',
                                    errorMessage: '',
                                }]
                            },
                            {
                                txt: '甲方承诺，所购以上产品，仅限于出口，不在中华人民共和国境内使用。',
                                id: 106,
                                checked: false,
                            },
                        ]
                    },
                    {
                        title: 'AED条款',
                        list: [
                            {
                                txt: '乙方为终端用户提供最优质的售后服务，凡是在乙方购买的AED在设备正常使用过程中发生丢失并可提供公安机关出示的丢失证明材料及购买发票的用户，乙方可免费提供一台AED进行补偿。',
                                id: 201,
                                checked: false,
                            },
                            {
                                txt: '凡是在乙方购买的AED在设备正常使用过程中发生施救并可提供终端的施救证明的甲方客户，乙方可免费提供一副全新AED电极片进行补偿（每台AED可享1次此补偿机会）。',
                                id: 202,
                                checked: false,
                            },
                        ]
                    },
                    {
                        title: '产线条款',
                        list: [
                            {
                                txt: '甲方承诺遵守以下约定：本合同内采购的五分类血球试剂只销售至xxx（省/直辖市/自治区）xxx（市）xxx（医疗机构名称），且只使用在序列号为xxx迈瑞BC-5000血球仪上；若甲方违反本约定，自愿向乙方缴纳不低于5000元/次违约金',
                                id: 301,
                                checked: false,
                                customInfo: [{
                                    label: '销售地区',
                                    name: 'saleCityId',
                                    type: 'address',
                                    defalutValue: [],
                                    value: '',
                                    errorMessage: ''
                                }, {
                                    label: '医疗机构名称',
                                    name: 'terminalTraderName',
                                    maxlength: 100,
                                    value: '',
                                    errorMessage: '',
                                }, {
                                    label: '序列号',
                                    name: 'snCode',
                                    maxlength: 100,
                                    width: 300,
                                    value: '',
                                    errorMessage: ''
                                }]
                            },
                            {
                                txt: '①合同签署后，甲方应同步向乙方预付试剂款xxx元。后续甲方向乙方采购试剂时，双方另行签署试剂合同确认具体采购试剂的型号及数量。在上述甲方预付试剂款金额内，甲方无需向乙方支付试剂款。<br>②甲方承诺在本合同生效之日起12个月内，向乙方采购并消耗完毕本条第1款约定预付试剂款；甲方如在前述期限内未消耗完试剂款的，剩余未消耗试剂款作为甲方违约金，乙方不予退还。',
                                id: 302,
                                checked: false,
                                customInfo: [{
                                    label: '预付试剂款',
                                    name: 'prepaidReagentAmount',
                                    type: 'price',
                                    value: '',
                                    errorMessage: ''
                                }]
                            },
                        ]
                    }
                ],
                customChecked: false,
                additionalClauseNos: [],
                addressData: [],
                customErrorMessage: '',
                customValue: '',
                selectedNum: 0,
                saleorderId: '',
                saleorderNo: '',
                loading: true,
                id: '',
                cansubmit: true
            }

        },
        async created() {
            this.saleorderId = VD_UI_GLOBAL.getQuery('saleorderId');
            this.saleorderNo = VD_UI_GLOBAL.getQuery('saleorderNo');

            await this.getAddressData();

            await this.initData();
        },
        methods: {
            initData() {
                this.loading = true;
                return axios.post('/order/additionalClause/query.do', {
                    saleorderId: this.saleorderId
                }).then(({ data }) => {
                    if (data.code == 0) {
                        if(data.data.id) {
                            this.id = data.data.id;

                            this.blockList.forEach(bItem => {
                                bItem.list.forEach(litem => {
                                    if(data.data.additionalClauseNos.split(',').indexOf(litem.id.toString()) !== -1) {
                                        litem.checked = true;

                                        if(litem.customInfo && litem.customInfo.length) {
                                            litem.customInfo.forEach(cItem => {
                                                if(data.data[cItem.name]) {
                                                    cItem.value = data.data[cItem.name];
    
                                                    if(cItem.name == 'saleCityId') {
                                                        cItem.defalutValue = [data.data.saleProvinceId, data.data.saleCityId];
                                                    }
                                                }
                                            })
                                        }
                                    }
                                })
                            })

                            if(data.data.additionalClauseNos.split(',').indexOf('401') !== -1) {
                                this.customChecked = true;
                                this.customValue = data.data.additionalClause;
                            }
                        }
                    }

                    this.loading = false;

                    this.getSelectedNum();
                }) 
            },
            getAddressData() {
                return axios.post('/system/region/static/getAllProvinceAndCityRestful.do').then(({ data }) => {
                    if (data.code == 0) {
                        this.addressData = data.data;
                    }
                })
            },
            handleFilterAddressChange(formItem, cnt, value) {
                formItem.value = value.length ? value[1].value : '';

                this.validItem(formItem, cnt);
            },
            validItem(formItem, cnt) {
                formItem.errorMessage = '';

                let messages = {
                    installationAddress: '请输入安装地址',
                    traderName: '请输入客户名称',
                    prodName103: '请输入产品名称',
                    prodName105: '请输入产品名称',
                    saleCityId: '请选择销售地区',
                    terminalTraderName: '请输入医疗机构名称',
                    snCode: '请输入序列号',
                    prepaidReagentAmount: '请输入预付试剂款，仅限数字，保留两位小数',
                }

                console.log(formItem.value)
                if (cnt.checked) {
                    if(formItem.type == 'price' && (!formItem.value.toString().trim() || !/^\d*(.\d{1,2})$/.test(formItem.value.toString().trim()))) {
                        formItem.errorMessage = messages[formItem.name];
                        return false;
                    }

                    if(formItem.type != 'price' && !formItem.value.toString().trim()) {
                        formItem.errorMessage = messages[formItem.name];
                        return false;
                    }
                }

                return true;
            },
            validCustomItem () {
                if(this.customChecked && !this.customValue.trim()) {
                    this.customErrorMessage = '请输入自定义条款';
                    return false;
                }
                this.customErrorMessage = '';
                return true;
            },
            getSelectedNum() {
                let seletNum = 0;
                this.blockList.forEach(block => {
                    block.list.forEach(cnt => {
                        if(cnt.checked) {
                            seletNum++;
                        }
                    })
                })

                if(this.customChecked) {
                    seletNum++;
                }

                this.selectedNum = seletNum;
            },
            submitAdditional() {
                if(!this.cansubmit) {
                    return false;
                }

                // if(!this.selectedNum) {
                //     this.$message.warn('请选择需要补充的条款');
                //     return false;
                // }

                let flag = true;
                let firstName = '';
                this.blockList.forEach(block => {
                    block.list.forEach(cnt => {
                        if (cnt.customInfo && cnt.customInfo.length) {
                            cnt.customInfo.forEach(formItem => {
                                if (!this.validItem(formItem, cnt)) {
                                    flag = false;

                                    if (!firstName) {
                                        firstName = formItem.name;
                                    }
                                }
                            })
                        }
                    })
                })

                if(!this.validCustomItem()) {
                    flag = false;

                    if (!firstName) {
                        firstName = 'customValue';
                    }
                }

                if (!flag) {
                    this.$message.warn('请完善补充条款内容');

                    if(firstName == 'customValue') {
                        setTimeout(() => {
                            window.scrollTo(0, this.$refs[firstName].offsetTop - 200);
                        }, 300)
                    } else {
                        window.scrollTo(0, this.$refs[firstName][0].offsetTop - 200);
                    }

                    return;
                }

                let reqData = {
                    saleorderId: this.saleorderId,
                    saleorderNo: this.saleorderNo,
                    id: this.id
                };
                let ids = [];

                this.blockList.forEach(block => {
                    block.list.forEach(cnt => {
                        if (cnt.customInfo && cnt.customInfo.length) {
                            cnt.customInfo.forEach(formItem => {
                                if(cnt.checked) {
                                    reqData[formItem.name] = formItem.value.toString().trim();
                                } else {
                                    reqData[formItem.name] = '';
                                }
                            })
                        }

                        if(cnt.checked) {
                            ids.push(cnt.id);
                        }
                    })
                })

                if(this.customChecked) {
                    reqData.additionalClause = this.customValue.trim();
                    ids.push(401);
                }

                reqData.additionalClauseNos = ids.join(',');

                console.log(reqData);

                this.cansubmit = false;

                axios.post('/order/additionalClause/save.do', reqData).then(({data}) => {
                    if(data.code === 0) {
                        window.parent.layer.closeAll();
                        
                        window.parent.addtionalCallback(data.data);
                    } else {
                        this.cansubmit = true;
                        this.$message.error(data.message || '提交失败');
                    }
                })
            },
            hideDialog() {
                window.parent.layer.closeAll();
            }
        }
    })
}.call(this);