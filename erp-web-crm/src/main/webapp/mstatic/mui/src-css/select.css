.vd-ui-select,
.vd-ui-wxuser-select {
  width: 100%;
}
.vd-ui-select .vd-ui-select-trigger,
.vd-ui-wxuser-select .vd-ui-select-trigger {
  display: flex;
  align-items: center;
  padding: 10px;
  position: relative;
}
.vd-ui-select .vd-ui-select-trigger.multi-select,
.vd-ui-wxuser-select .vd-ui-select-trigger.multi-select {
  align-items: stretch;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-trigger-txt,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-trigger-txt {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: var(--line);
  -webkit-box-orient: vertical;
  word-break: break-all;
  flex: 1;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-trigger-txt.placeholder,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-trigger-txt.placeholder {
  color: #ccc;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags {
  flex: 1;
  max-height: calc(35px * var(--line) - 5px);
  overflow: hidden;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner {
  display: flex;
  flex-wrap: wrap;
  margin-top: -5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag {
  display: flex;
  align-items: center;
  background: #F5F7FA;
  border-radius: 3px;
  padding-left: 10px;
  margin-right: 5px;
  margin-top: 5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag.tag-avatar,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag.tag-avatar {
  padding-left: 5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar {
  margin-right: 5px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar img,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .vd-ui-select-tag-avatar img {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
  overflow: hidden;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .icon-delete,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag .icon-delete {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 16px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag-more,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-multi-tags-inner .vd-ui-select-tag-more {
  padding: 0 10px;
  height: 30px;
  display: flex;
  align-items: center;
  margin-top: 5px;
  background: #F5F7FA;
  border-radius: 3px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-icon-trigger,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-icon-trigger {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
}
.vd-ui-select .vd-ui-select-trigger .vd-ui-icon-trigger .icon-app-right,
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-icon-trigger .icon-app-right {
  margin-left: 0;
}
.vd-ui-select .vd-ui-select-trigger .icon-app-right,
.vd-ui-wxuser-select .vd-ui-select-trigger .icon-app-right {
  font-size: 16px;
  color: #666;
  margin-left: 10px;
}
.vd-ui-wxuser-select .vd-ui-select-trigger.multi-select {
  padding: 4px 10px;
}
.vd-ui-wxuser-select .vd-ui-select-trigger.multi-select .icon-app-right {
  line-height: 30px;
}
.vd-ui-wxuser-select .vd-ui-select-trigger .vd-ui-select-trigger-txt.placeholder {
  line-height: 30px;
}
.vd-ui-select-options .vd-ui-select-options-search {
  padding: 10px;
}
.vd-ui-select-options .vd-ui-select-options-list {
  max-height: calc(100vh - 147px);
  max-height: calc(100vh - 147px - constant(safe-area-inset-bottom));
  max-height: calc(100vh - 147px - env(safe-area-inset-bottom));
  overflow: auto;
}
.vd-ui-select-options .vd-ui-select-options-list.filter {
  max-height: calc(100vh - 200px);
  height: calc(100vh - 200px);
  height: calc(100vh - 200px - constant(safe-area-inset-bottom));
  height: calc(100vh - 200px - env(safe-area-inset-bottom));
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item {
  padding: 10px;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #F5F7FA;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item:last-child {
  border-bottom: 0;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .vd-ui_icon {
  font-size: 16px;
  margin-right: 5px;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-radio1,
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-checkbox1 {
  color: #BABFC2;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-radio3,
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item .icon-checkbox2 {
  color: #09f;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-item.selected {
  background: #E0F3FF;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-avatar {
  margin-right: 5px;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-avatar img {
  width: 18px;
  height: 18px;
  border-radius: 3px;
  object-fit: cover;
  overflow: hidden;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-txt {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  word-break: break-all;
}
.vd-ui-select-options .vd-ui-select-options-list .vd-ui-select-options-txt .strong {
  color: #f60;
}
.vd-ui-select-options .vd-ui-select-options-empty {
  padding: 100px 0;
  text-align: center;
  height: calc(100vh - 200px);
  height: calc(100vh - 200px - constant(safe-area-inset-bottom));
  height: calc(100vh - 200px - env(safe-area-inset-bottom));
}
.vd-ui-select-options .vd-ui-select-options-empty .empty-img {
  width: 223px;
  height: 120px;
  margin: 0 auto;
}
.vd-ui-select-options .vd-ui-select-options-empty .empty-img img {
  width: 100%;
  height: 100%;
  margin-bottom: 15px;
}
.vd-ui-select-options .vd-ui-select-options-empty .empty-txt {
  color: #666;
  font-size: 14px;
}
.vd-ui-select-options .vd-ui-select-loading {
  padding-top: 100px;
  height: calc(100vh - 200px);
  height: calc(100vh - 200px - constant(safe-area-inset-bottom));
  height: calc(100vh - 200px - env(safe-area-inset-bottom));
  text-align: center;
}
@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.vd-ui-select-options .vd-ui-select-loading .vd-ui-select-loading-icon {
  font-size: 48px;
  color: #09f;
  text-align: center;
  margin-bottom: 10px;
}
.vd-ui-select-options .vd-ui-select-loading .vd-ui-select-loading-icon .icon-loading {
  display: inline-block;
  animation: loading 2s linear infinite;
}
.vd-ui-select-options .vd-ui-select-loading .vd-ui-select-loading-txt {
  font-size: 14px;
}
.vd-ui-select-options .vd-ui-select-options-footer {
  display: flex;
  align-items: center;
  padding: 10px 0;
  background: #fff;
  border-top: solid 1px #EBEFF2;
}
.vd-ui-select-options .vd-ui-select-options-footer .btn-cancel-flex {
  flex: 2;
  padding-left: 10px;
}
.vd-ui-select-options .vd-ui-select-options-footer .btn-confirm-flex {
  flex: 3;
  padding: 0 10px;
}
