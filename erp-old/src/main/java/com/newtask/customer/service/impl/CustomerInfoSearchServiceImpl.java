package com.newtask.customer.service.impl;

import com.newtask.customer.service.CustomerInfoSearchService;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.model.TraderCustomer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/30 15:06
 * @describe
 */
@Service
public class CustomerInfoSearchServiceImpl implements CustomerInfoSearchService {
    @Resource
    private TraderCustomerMapper traderCustomerMapper;


    @Override
    public List<Integer> getTraderIdByTime(Long startTime, Long endTime){
       List<Integer> TraderIdList = traderCustomerMapper.getTraderIdByTime(startTime,endTime);
        return TraderIdList;
    }

    @Override
    public int updateHasQuoted(List<Integer> countToUpdate) {
        int num = traderCustomerMapper.updateHasQuoted(countToUpdate);
        return num;
    }

    @Override
    public List<Integer> getTraderIdByTimeAndValidStatus(Long startTime, Long endTime) {
        List<Integer> TraderIdList = traderCustomerMapper.getTraderIdByTimeAndValidStatus(startTime,endTime);
        return TraderIdList;
    }

    @Override
    public int updateIsCooperated(List<Integer> countToUpdate) {
        return traderCustomerMapper.updateIsCooperated(countToUpdate);
    }

}
