<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="订单打印" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link href='<%=basePath%>static/css/order_print/system.css'
      rel="stylesheet" type="text/css">
<link rel="stylesheet"
      href='<%=basePath%>static/css/order_print/ui.theme.css?rnd=${resourceVersionKey}'
      type="text/css" media="all">
<link type="text/css" rel="stylesheet"
      href='<%=basePath%>static/css/order_print/layer.css?rnd=${resourceVersionKey}'
      id="skinlayercss">
<script type="text/javascript"
        src='<%=basePath%>static/js/jquery.PrintArea.js'></script>
<script type="text/javascript"
        src='<%=basePath%>static/js/order/saleorder/order_print.js?rnd=${resourceVersionKey}'></script>
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/general.css?rnd=${resourceVersionKey}">
<link type="text/css" rel="stylesheet" href="<%=basePath%>static/css/manage.css"/>
<body>

<c:choose>
<c:when test="${buyorderExpenseDto.buyorderExpenseDetailDto.contractUrl != null && buyorderExpenseDto.buyorderExpenseDetailDto.contractUrl!=''}">
<div  >
    <iframe style="width: 1400px;height: 1300px"  src="${buyorderExpenseDto.buyorderExpenseDetailDto.contractUrl}" frameborder="0" style="width:1000px; height:600px;" scrolling="no"></iframe>
</div>
</c:when>

<c:otherwise>


<div id="print-contract">
        <%-- 更换字体 --%>
    <style>
        @font-face {
            font-family: 'SourceHanSansCN-Regular';
            src: url('<%=basePath%>static/hansFont/SourceHanSansCN-Regular.otf');
        }

        html, body, #print-contract {
            font-family: "SourceHanSansCN-Regular", sans-serif;
        }
        .keyWord{
            color: rgba(255,255,255,0);
            font-size: 1px;
        }
    </style>
    <div class="">
        <div class="contract-head">
            <c:if test="${ buyorderExpenseDto.buyorderExpenseDetailDto.orgId == 36}">
                <img src="<%=basePath%>static/images/logo2.jpg"/>
            </c:if>
            <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.orgId != 36 }">
                <img src="<%=basePath%>static/images/logo1.jpg?v=20201029" class=""/>
            </c:if>
            <div class="contract-number">
                <div>合同号码: ${buyorderExpenseDto.buyorderExpenseNo }</div>
                <div>制单日期:
                    <c:if test="${buyorderExpenseDto.validTime ==null }">
                        ${currTime}
                    </c:if>
                    <c:if test="${buyorderExpenseDto.validTime !=null}">
                        <fmt:formatDate value="${buyorderExpenseDto.validTime }" type="date" pattern="yyyy-MM-dd"/>
                    </c:if>
                </div>
            </div>
        </div>
        <div class="contract-head-title">服务采购合同</div>
        <div class="contract-print-table">
            <table>
                <tbody>
                <tr class="jiayi">
                    <td><span>甲方：</span><span>南京贝登医疗股份有限公司</span><span class="keyWord">$yi$</span></td>
                    <td><span>乙方：</span><span>${ buyorderExpenseDto.buyorderExpenseDetailDto.traderName}</span></td>
                </tr>
                <tr>
                    <td>
                        <div style="display: inline-block">
                            采购及收票人员： ${detail.realName }
                        </div>
                        <div>
                            <span>联系方式：</span>
                            <span>${detail.mobile }
                            <c:if test="${detail.mobile !=null && detail.mobile !=''&& detail.telephone!=null &&detail.telephone!='' }">/</c:if>
                                    ${detail.telephone }</span>
                        </div>
                        <div>
                            <span>开票信息：</span>
                            <span></span>
                        </div>
                        <div class="overflow-hidden">
                            <span style="width:71px;float:left;">注册地址/电话：</span>

                            <span style="float:left;display:inline-block;max-width:-moz-calc(100% - 91px);max-width:-webkit-calc(100% - 91px);max-width:calc(100% - 91px);">${vedeng_address_phone}</span>
                        </div>
                        <div>
                            <span>开户行：</span>
                            <span>中国建设银行股份有限公司南京中山南路支行</span>
                        </div>
                        <div>
                            <span>税号：</span>
                            <span>91320100589439066H</span>
                        </div>
                        <div>
                            <span>账号：</span>
                            <span>32001881236052503686</span>
                        </div>
                    </td>
                    <td>
                        <div>
                            <span>销售人员：</span>
                            <span>${ buyorderExpenseDto.buyorderExpenseDetailDto.traderContactName}<%--<c:if test="${buyorderExpenseDto.sex eq 0}">&nbsp;女士</c:if><c:if test="${buyorderExpenseDto.sex eq 1}">&nbsp;先生</c:if>--%>
                            </span>
                        </div>
                        <div>
                            <span>联系方式：</span>
                            <span>${buyorderExpenseDto.buyorderExpenseDetailDto.traderContactMobile}
                            <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.traderContactMobile!=null && buyorderExpenseDto.buyorderExpenseDetailDto.traderContactMobile!=''}">/</c:if>
                                    ${buyorderExpenseDto.buyorderExpenseDetailDto.traderContactTelephone }</span>
                        </div>
                        <div>
                            <span>收款信息：</span>
                            <span></span>
                        </div>
                        <div>
                            <span>开户行：</span>
                            <span>${buyorderExpenseDto.bank }</span>
                        </div>
                        <div>
                            <span>账号：</span>
                            <span>${buyorderExpenseDto.bankAccount }</span>
                        </div>
                        <div>
                            <span>税号：</span>
                            <span>${buyorderExpenseDto.taxNum }</span>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>

            <div class="contract-print-title2 pl10">一、服务信息：</div>
            <table class="print-product-table">
                <tbody>
                <tr class="font-bold tdsmall">
                    <td style="width:4%;">序号</td>
                    <td style="width:8%;">订货号</td>
                    <td>产品名称</td>
                    <td style="width:4%;">数量</td>
                    <td style="width:10%">单价(元）</td>
                    <td style="width:10%;">金额(元）</td>
                    <td style="width:6%;">备注</td>
                </tr>
                <c:set var="count" value="1"></c:set>
                <c:forEach var="list" items="${buyorderExpenseGoodsList}" varStatus="num">
                    <c:if test="${list.isDelete eq 0}">
                        <tr>
                            <td class="" style=" "> ${count}</td>
                            <c:set var="count" value="${count+1}"></c:set>
                            <td style=" ">${list.buyorderExpenseItemDetailDto.sku }</td>
                            <td style=" text-align:left;">${ list.buyorderExpenseItemDetailDto.goodsName}</td>
                            <td>${list.num }</td>
                            <td>${list.priceStr }</td>
                            <td>${list.allPrice}</td>
                            <td>${list.buyorderExpenseItemDetailDto.insideComments }</td>
                        </tr>
                    </c:if>
                </c:forEach>
                <tr>
                    <td colspan=3>合计</td>
                    <td colspan="3" style="overflow: hidden;">
                        <div class="total-count f_left">（大写）${chineseNumberTotalPrice }</div>
                        <div class="total-count-num f_right">￥${totalAmount}</div>
                    </td>
                    <td></td>
                </tr>
                </tbody>
            </table>
            <div class="contract-print-title2">二、付款方式&发票类型：</div>
            <div class="contract-details">
                1、 付款方式：
                <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentType == 419}">
                    先款后货，预付100% ，预付金额 <fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元
                </c:if>
                <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentType == 420}">
                    先货后款，预付80%，预付金额<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元
                </c:if>
                <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentType == 421}">
                    先货后款，预付50%，预付金额<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元
                </c:if>
                <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentType == 422}">
                    先货后款，预付30%，预付金额<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元
                </c:if>
                <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentType == 423}">
                    先货后款，预付0%，预付金额<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元
                </c:if>
                <c:if test="${buyorderExpenseDto.buyorderExpenseDetailDto.paymentType == 424}">
                    自定义，预付金额<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.prepaidAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元，账期支付<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.accountPeriodAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元，尾款<fmt:formatNumber value="${buyorderExpenseDto.buyorderExpenseDetailDto.retainageAmount }" type="numer" pattern="0.00" maxFractionDigits="2"/>元
                    ，尾款期限${buyorderExpenseDto.buyorderExpenseDetailDto.retainageAmountMonth }月
                </c:if>
                <br/>
                2、 发票类型：${invoiceType.title} 。乙方开具的发票必须注明双方签订合同时的订单号；否则会影响后续合作和付款。<br/>
                3、开票时间：乙方应在本合同生效之日起三个工作日内向甲方开具满足上述发票类型的合规票据并于开票之日将发票寄送给甲方。
            </div>
            <div class="contract-print-title2">三、服务提供：</div>
            <div class="contract-details">
                1、乙方应按照合同约定的时间和标准以乙方的名义向甲方提供相关服务。<br/>
                2、乙方未按照约定时间向甲方提供服务的， 乙方需承担延迟服务的责任，乙方每天赔偿合同总金额的0.3%作为违约赔偿金，直到乙方提供服务为止。
                如到合同规定的交期后14天内，乙方仍不能提供服务，甲方有权解除合同，乙方必须全额退还甲方的订货预付款，且乙方向甲方支付合同总金额30%的违约金，
                违约金不足以弥补甲方损失的，还应该承担由此对甲方造成的所有损失，包括但不限于预期利润、另行采购的差价损失等。甲方因此而产生的维权费用包括但不限于诉讼费、律师费等亦由乙方承担。<br/>
                3、乙方未能按照约定的标准向甲方提供服务的，甲方可以要求乙方重新免费提供服务。若因服务标准问题而造成甲方损失的，乙方也应承担甲方的损失。<br/>
            </div>
            <div class="contract-print-title2">四、反商业贿赂： </div>
            <div class="contract-details">
                1、 双方应按照所有适用的法律法规，包括但不限于所有适用的反贿赂和反不正当法律法规，履行其在本合同项下的各项义务。乙方确认知晓国家法律、法规和甲方相关制度关于反商业贿赂事项的各项规定。乙方已获甲方正式告知，甲方对于任何形式商业贿赂均持坚决的反对态度，亦不会授权任何员工要求、指示、暗示乙方实施、参与任何形式商业贿赂行为，以获得交易机会或其他经济利益。甲方员工如有实施商业贿赂行为，乙方将第一时间告知甲方。双方在开展业务发展活动过程中，将严格遵守国家法律、法规，不从事、参与任何形式的商业贿赂及不正当竞争行为，以自身行动维护双方良好的合作关系。一旦发现，甲方有权单方解除本协议，并将乙方纳入供应商黑名单，同时，乙方应向甲方支付等同于已发生业务货款金额二十倍的违约金，并承担由于该违约行为给甲方造成的一切损失，甲方可直接从应付乙方的货款中扣除，不足部分甲方保留向乙方追偿的权利。
            </div>

            <div class="contract-print-title2">五、知识产权：</div>
            <div class="contract-details">
                1、 未经甲⽅事先书⾯同意，乙⽅不得在任何国家或地区，并通过任何形式，使⽤甲⽅及其关联⽅的商标、商号或将与甲⽅商标、商号相同或类似的内容注册为乙⽅的商标、商号、公司域名等。<br/>
                2、 未经甲⽅事先书⾯同意，乙⽅不得对甲⽅及其关联⽅的企业名称、商标、商号、服务标志或标识进⾏任何商业使⽤和擅⾃作出任何变更，包括但不限于在⼴告、宣传资料、⼴告、 办公地点等使⽤。<br/>
                3、 乙方承诺对于乙方所销售的产品未侵害任何第三方的知识产权，若甲方因此受到任何第三方关于该产品知识产权的责任追究都由乙⽅⾃⾏承担，由此给甲⽅造成其他损失的，乙⽅还应向甲⽅承担相应的赔偿责任。
            </div>

            <div class="contract-print-title2">六、保密条款：</div>
            <div class="contract-details">
                甲方向乙方提供的与此订单有关的所有信息，包括但不限于关于产品、商业机密、图纸、文件、商标，乙方必须保密，且未经甲方事先书面同意，乙方不得公开或以其他方式向第三方透露，否则应当承担违约金100万元。<br/>
            </div>

            <div class="contract-print-title2">七、违约责任：</div>
            <div class="contract-details">
                合同生效后，乙方不得单方面撤销合同。若乙方单方面撤销合同或乙方未按照本协议各条的要求履行义务而产生违约的，乙方应承担合同总金额30%的违约金（各条有单独约定违约金的适用各条约定的违约金），违约金不足以弥补甲方损失的，还应该承担由此对甲方造成的所有损失，包括但不限于预期利润、另行采购的差价损失等。甲方因此而产生的维权费用包括但不限于诉讼费、律师费等亦由乙方承担。<br/>
            </div>

            <div class="contract-print-title2"> 八、合同争议处理：</div>
            <div class="contract-details">
                对于合同争议，双方需本着友好精神协商解决，协商不成，可将争议提请甲方所在地人民法院诉讼。
            </div>

            <div class="contract-print-title2"> 九、合同效力：</div>
            <div class="contract-details">
                本合同双方盖章或签字生效，传真件、扫描件与原件具有同等法律效力。
            </div>

            <div class="contract-print-title2"> 十、 其他约定：</div>
            <div class="contract-details">
                若货物由乙方按照甲方指示直接发送甲方客户的，乙方还应遵守如下约定：<br>
                1. 乙方以甲方名义发货，即：发货单的发件方为甲方公司名称 ，联系人为甲方销售负责人。<br>
                2. 乙方发货时，不能夹带乙方任何联系方式，如名片、便签等。<br>
                3. 乙方发货时，需要附上送货单，送货单上的送货单位显示我公司名称，不能泄露乙方与甲方约定的任何信息（尤其是价格）。<br>
                4. 乙方发货时，不能夹带乙方开具给甲方的发票。<br>
                5. 甲方的客户，甲方有权自主维护。若甲方客户有意越过甲方与乙方直接合作，乙方应拒绝为甲方客户报价，并将情况及时告知甲方。<br>
                6. 上述条款中，乙方若有任何一条违约情况发生，应承担合同总金额10倍的违约金，且违约金的金额不低于20万元。<br>
            </div>

            <div class="contract-print-title2">十一、补充条款：</div>
                <div class="contract-details">无。</div>
            <div class="contract-print-sign">
                <ul>
                    <li style="margin-left: 50px;padding:32px 45px 0px 45px;">
                        <div class="sign-name">甲方：南京贝登医疗股份有限公司<span class="keyWord">$yi$</span></div>
                        <div class="sign-place">
                            <ul>
                                <li>
                                    <div style="margin-top:22px;">${ detail.realName }</div>
                                    <div>授权人签字</div>
                                </li>
                                <li>
                                    <div style="margin-top:22px;">
                                        <c:if test="${buyorderExpenseDto.validTime ==null }">
                                            ${currTime}
                                        </c:if>
                                        <c:if test="${buyorderExpenseDto.validTime !=null}">
                                            <fmt:formatDate value="${buyorderExpenseDto.validTime }" type="date" pattern="yyyy-MM-dd"/>
                                        </c:if>
                                    </div>
                                    <div>日期</div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li style="float: right; margin-right: 50px;overflow:visible;position: relative; padding:32px 0 28px 0;">
                        <div class="sign-name">乙方：${ buyorderExpenseDto.buyorderExpenseDetailDto.traderName}</div>
                        <div class="sign-place">
                            <ul>
                                <li>
                                    <div></div>
                                    <div>授权人签字</div>
                                </li>
                                <li>
                                    <div></div>
                                    <div>日期</div>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <div class='clear'></div>
                </ul>
            </div>

        </div>
    </div>
    <c:if test="${autoGenerate != null and !autoGenerate}">
        <div class='tcenter mb15'>
            <span class=" bt-small bt-bg-style bg-light-blue" id="btnPrint">打印</span>
            <c:if test="${buyorderExpenseDto.validStatus ==1}">
                <span class=" bt-small bt-bg-style bg-light-blue"  id="signature">电子签章</span>
            </c:if>
        </div>
    </c:if>
</div>
</c:otherwise>
</c:choose>
<input type="hidden" name="buyorderExpenseId" id="buyorderExpenseId" value="${buyorderExpenseDto.buyorderExpenseId}">
<script type="text/javascript">
    $("#signature").click(function() {
        let buyorderExpenseId = $("#buyorderExpenseId").val();
        $.ajax({
            url:page_url+'/buyorderExpense/signature.do',
            data:{"buyorderExpenseId":buyorderExpenseId},
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code==0){
                    layer.alert("已发起电子签章流程。可稍后查看，无需频繁发起");
                }else{
                    layer.alert(data.message);
                }

            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });

    });
</script>
<script>
    $("#btnPrint").click(function () {
        $("#btnPrint").hide();
        if (window.ActiveXObject || "ActiveXObject" in window) {
            $("#print-contract").printArea({
                mode: 'popup'
            });
        } else {
            $("#print-contract").printArea({
                mode: 'popup',
                popHt: 800,
                popWd: 1500,
                popX: 200,
                popY: 200,
                popTitle: "${buyorderExpenseDto.buyorderExpenseNo }" + "${ buyorderExpenseDto.buyorderExpenseDetailDto.traderName}"
            });
        }
        $("#btnPrint").show();
    });
</script>
<%@ include file="../../common/footer.jsp" %>
