package com.vedeng.aftersales.model.dto;

import com.vedeng.aftersales.model.AfterSaleSupplyPolicy;
import com.vedeng.aftersales.model.AfterSaleSupplyPolicyAttachment;

import java.util.List;

public class AfterSaleSupplyPolicyDto extends AfterSaleSupplyPolicy {

    private String showName;

    private String latestOneYearNum;

    private String latestOneYearAmout;
    /**贝登售后标准**/
    private String bdAfterSaleServiceStandard;
    /**供应商售后标准**/
    private String supplyAfterSaleServiceStandard;
    /**商品名称**/
    private String skuName;
    /**品牌**/
    private String brandName;
    /**型号**/
    private String model;

    private List<AfterSaleSupplyPolicyAttachment> afterSaleSupplyPolicyAttachmentList;

    public List<AfterSaleSupplyPolicyAttachment> getAfterSaleSupplyPolicyAttachmentList() {
        return afterSaleSupplyPolicyAttachmentList;
    }

    public void setAfterSaleSupplyPolicyAttachmentList(List<AfterSaleSupplyPolicyAttachment> afterSaleSupplyPolicyAttachmentList) {
        this.afterSaleSupplyPolicyAttachmentList = afterSaleSupplyPolicyAttachmentList;
    }

    public String getShowName() {
        return showName;
    }

    public void setShowName(String showName) {
        this.showName = showName;
    }

    public String getLatestOneYearNum() {
        return latestOneYearNum;
    }

    public void setLatestOneYearNum(String latestOneYearNum) {
        this.latestOneYearNum = latestOneYearNum;
    }

    public String getLatestOneYearAmout() {
        return latestOneYearAmout;
    }

    public void setLatestOneYearAmout(String latestOneYearAmout) {
        this.latestOneYearAmout = latestOneYearAmout;
    }

    public String getBdAfterSaleServiceStandard() {
        return bdAfterSaleServiceStandard;
    }

    public void setBdAfterSaleServiceStandard(String bdAfterSaleServiceStandard) {
        this.bdAfterSaleServiceStandard = bdAfterSaleServiceStandard;
    }

    public String getSupplyAfterSaleServiceStandard() {
        return supplyAfterSaleServiceStandard;
    }

    public void setSupplyAfterSaleServiceStandard(String supplyAfterSaleServiceStandard) {
        this.supplyAfterSaleServiceStandard = supplyAfterSaleServiceStandard;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }
}
