<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.FlowNodeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.FlowNodeEntity">
    <!--@mbg.generated-->
    <!--@Table T_FLOW_NODE-->
    <id column="FLOW_NODE_ID" property="flowNodeId" />
    <result column="FLOW_ORDER_ID" property="flowOrderId" />
    <result column="OPEN_INVOICE" property="openInvoice" />
    <result column="NODE_LEVEL" property="nodeLevel" />
    <result column="PAYMENT_METHOD" property="paymentMethod" />
    <result column="INVOICE_TYPE" property="invoiceType" />
    <result column="AMOUNT" property="amount" />
    <result column="CREDIT_PAYMENT" property="creditPayment" />
    <result column="BALANCE" property="balance" />
    <result column="BALANCE_DUE_DATE" property="balanceDueDate" />
    <result column="TRADER_ID" property="traderId" />
    <result column="TRADER_NAME" property="traderName" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" property="traderContactName" />
    <result column="TRADER_CONTACT_PHONE" property="traderContactPhone" />
    <result column="TRADER_ADDRESS_ID" property="traderAddressId" />
    <result column="TRADER_CONTACT_ADDRESS" property="traderContactAddress" />
    <result column="RECEIVER_TRADER_CONTACT_ID" property="receiverTraderContactId" />
    <result column="RECEIVER_NAME" property="receiverName" />
    <result column="RECEIVER_PHONE" property="receiverPhone" />
    <result column="RECEIVER_ADDRESS_ID" property="receiverAddressId" />
    <result column="RECEIVER_ADDRESS" property="receiverAddress" />
    <result column="INVOICE_RECEIVER_TRADER_CONTACT_ID" property="invoiceReceiverTraderContactId" />
    <result column="INVOICE_RECEIVER_NAME" property="invoiceReceiverName" />
    <result column="INVOICE_RECEIVER_PHONE" property="invoiceReceiverPhone" />
    <result column="INVOICE_RECEIVER_ADDRESS_ID" property="invoiceReceiverAddressId" />
    <result column="INVOICE_RECEIVER_ADDRESS" property="invoiceReceiverAddress" />
    <result column="IS_DELETE" property="isDelete" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    FLOW_NODE_ID, FLOW_ORDER_ID, OPEN_INVOICE, NODE_LEVEL, PAYMENT_METHOD, INVOICE_TYPE,
    AMOUNT, CREDIT_PAYMENT, BALANCE, BALANCE_DUE_DATE, TRADER_ID, TRADER_NAME, TRADER_CONTACT_ID,
    TRADER_CONTACT_NAME, TRADER_CONTACT_PHONE, TRADER_ADDRESS_ID, TRADER_CONTACT_ADDRESS,
    RECEIVER_TRADER_CONTACT_ID, RECEIVER_NAME, RECEIVER_PHONE, RECEIVER_ADDRESS_ID, RECEIVER_ADDRESS,
    INVOICE_RECEIVER_TRADER_CONTACT_ID, INVOICE_RECEIVER_NAME, INVOICE_RECEIVER_PHONE,
    INVOICE_RECEIVER_ADDRESS_ID, INVOICE_RECEIVER_ADDRESS, IS_DELETE, CREATOR, UPDATER,
    CREATOR_NAME, UPDATER_NAME, ADD_TIME, MOD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from T_FLOW_NODE
    where FLOW_NODE_ID = #{flowNodeId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_FLOW_NODE
    where FLOW_NODE_ID = #{flowNodeId}
  </delete>
  <insert id="insert" keyColumn="FLOW_NODE_ID" keyProperty="flowNodeId" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_NODE (FLOW_ORDER_ID, OPEN_INVOICE, NODE_LEVEL, PAYMENT_METHOD, INVOICE_TYPE,
      AMOUNT, CREDIT_PAYMENT, BALANCE, BALANCE_DUE_DATE, TRADER_ID, TRADER_NAME,
      TRADER_CONTACT_ID, TRADER_CONTACT_NAME, TRADER_CONTACT_PHONE, TRADER_ADDRESS_ID,
      TRADER_CONTACT_ADDRESS, RECEIVER_TRADER_CONTACT_ID, RECEIVER_NAME, RECEIVER_PHONE,
      RECEIVER_ADDRESS_ID, RECEIVER_ADDRESS, INVOICE_RECEIVER_TRADER_CONTACT_ID, INVOICE_RECEIVER_NAME,
      INVOICE_RECEIVER_PHONE, INVOICE_RECEIVER_ADDRESS_ID, INVOICE_RECEIVER_ADDRESS,
      IS_DELETE, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, ADD_TIME,
      MOD_TIME)
    values (#{flowOrderId}, #{openInvoice}, #{nodeLevel}, #{paymentMethod}, #{invoiceType},
      #{amount}, #{creditPayment}, #{balance}, #{balanceDueDate}, #{traderId}, #{traderName},
      #{traderContactId}, #{traderContactName}, #{traderContactPhone}, #{traderAddressId},
      #{traderContactAddress}, #{receiverTraderContactId}, #{receiverName}, #{receiverPhone},
      #{receiverAddressId}, #{receiverAddress}, #{invoiceReceiverTraderContactId}, #{invoiceReceiverName},
      #{invoiceReceiverPhone}, #{invoiceReceiverAddressId}, #{invoiceReceiverAddress},
      #{isDelete}, #{creator}, #{updater}, #{creatorName}, #{updaterName}, #{addTime},
      #{modTime})
  </insert>
  <insert id="insertSelective" keyColumn="FLOW_NODE_ID" keyProperty="flowNodeId" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_FLOW_NODE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="flowOrderId != null">
        FLOW_ORDER_ID,
      </if>
      <if test="openInvoice != null">
        OPEN_INVOICE,
      </if>
      <if test="nodeLevel != null">
        NODE_LEVEL,
      </if>
      <if test="paymentMethod != null">
        PAYMENT_METHOD,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="amount != null">
        AMOUNT,
      </if>
      <if test="creditPayment != null">
        CREDIT_PAYMENT,
      </if>
      <if test="balance != null">
        BALANCE,
      </if>
      <if test="balanceDueDate != null">
        BALANCE_DUE_DATE,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderName != null and traderName != ''">
        TRADER_NAME,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactPhone != null and traderContactPhone != ''">
        TRADER_CONTACT_PHONE,
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID,
      </if>
      <if test="traderContactAddress != null and traderContactAddress != ''">
        TRADER_CONTACT_ADDRESS,
      </if>
      <if test="receiverTraderContactId != null">
        RECEIVER_TRADER_CONTACT_ID,
      </if>
      <if test="receiverName != null and receiverName != ''">
        RECEIVER_NAME,
      </if>
      <if test="receiverPhone != null and receiverPhone != ''">
        RECEIVER_PHONE,
      </if>
      <if test="receiverAddressId != null">
        RECEIVER_ADDRESS_ID,
      </if>
      <if test="receiverAddress != null and receiverAddress != ''">
        RECEIVER_ADDRESS,
      </if>
      <if test="invoiceReceiverTraderContactId != null">
        INVOICE_RECEIVER_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceReceiverName != null and invoiceReceiverName != ''">
        INVOICE_RECEIVER_NAME,
      </if>
      <if test="invoiceReceiverPhone != null and invoiceReceiverPhone != ''">
        INVOICE_RECEIVER_PHONE,
      </if>
      <if test="invoiceReceiverAddressId != null">
        INVOICE_RECEIVER_ADDRESS_ID,
      </if>
      <if test="invoiceReceiverAddress != null and invoiceReceiverAddress != ''">
        INVOICE_RECEIVER_ADDRESS,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="flowOrderId != null">
        #{flowOrderId},
      </if>
      <if test="openInvoice != null">
        #{openInvoice},
      </if>
      <if test="nodeLevel != null">
        #{nodeLevel},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod},
      </if>
      <if test="invoiceType != null">
        #{invoiceType},
      </if>
      <if test="amount != null">
        #{amount},
      </if>
      <if test="creditPayment != null">
        #{creditPayment},
      </if>
      <if test="balance != null">
        #{balance},
      </if>
      <if test="balanceDueDate != null">
        #{balanceDueDate},
      </if>
      <if test="traderId != null">
        #{traderId},
      </if>
      <if test="traderName != null and traderName != ''">
        #{traderName},
      </if>
      <if test="traderContactId != null">
        #{traderContactId},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        #{traderContactName},
      </if>
      <if test="traderContactPhone != null and traderContactPhone != ''">
        #{traderContactPhone},
      </if>
      <if test="traderAddressId != null">
        #{traderAddressId},
      </if>
      <if test="traderContactAddress != null and traderContactAddress != ''">
        #{traderContactAddress},
      </if>
      <if test="receiverTraderContactId != null">
        #{receiverTraderContactId},
      </if>
      <if test="receiverName != null and receiverName != ''">
        #{receiverName},
      </if>
      <if test="receiverPhone != null and receiverPhone != ''">
        #{receiverPhone},
      </if>
      <if test="receiverAddressId != null">
        #{receiverAddressId},
      </if>
      <if test="receiverAddress != null and receiverAddress != ''">
        #{receiverAddress},
      </if>
      <if test="invoiceReceiverTraderContactId != null">
        #{invoiceReceiverTraderContactId},
      </if>
      <if test="invoiceReceiverName != null and invoiceReceiverName != ''">
        #{invoiceReceiverName},
      </if>
      <if test="invoiceReceiverPhone != null and invoiceReceiverPhone != ''">
        #{invoiceReceiverPhone},
      </if>
      <if test="invoiceReceiverAddressId != null">
        #{invoiceReceiverAddressId},
      </if>
      <if test="invoiceReceiverAddress != null and invoiceReceiverAddress != ''">
        #{invoiceReceiverAddress},
      </if>
      <if test="isDelete != null">
        #{isDelete},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
      <if test="updater != null">
        #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName},
      </if>
      <if test="addTime != null">
        #{addTime},
      </if>
      <if test="modTime != null">
        #{modTime},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeEntity">
    <!--@mbg.generated-->
    update T_FLOW_NODE
    <set>
      <if test="flowOrderId != null">
        FLOW_ORDER_ID = #{flowOrderId},
      </if>
      <if test="openInvoice != null">
        OPEN_INVOICE = #{openInvoice},
      </if>
      <if test="nodeLevel != null">
        NODE_LEVEL = #{nodeLevel},
      </if>
      <if test="paymentMethod != null">
        PAYMENT_METHOD = #{paymentMethod},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType},
      </if>
      <if test="amount != null">
        AMOUNT = #{amount},
      </if>
      <if test="creditPayment != null">
        CREDIT_PAYMENT = #{creditPayment},
      </if>
      <if test="balance != null">
        BALANCE = #{balance},
      </if>
      <if test="balanceDueDate != null">
        BALANCE_DUE_DATE = #{balanceDueDate},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId},
      </if>
      <if test="traderName != null and traderName != ''">
        TRADER_NAME = #{traderName},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME = #{traderContactName},
      </if>
      <if test="traderContactPhone != null and traderContactPhone != ''">
        TRADER_CONTACT_PHONE = #{traderContactPhone},
      </if>
      <if test="traderAddressId != null">
        TRADER_ADDRESS_ID = #{traderAddressId},
      </if>
      <if test="traderContactAddress != null and traderContactAddress != ''">
        TRADER_CONTACT_ADDRESS = #{traderContactAddress},
      </if>
      <if test="receiverTraderContactId != null">
        RECEIVER_TRADER_CONTACT_ID = #{receiverTraderContactId},
      </if>
      <if test="receiverName != null and receiverName != ''">
        RECEIVER_NAME = #{receiverName},
      </if>
      <if test="receiverPhone != null and receiverPhone != ''">
        RECEIVER_PHONE = #{receiverPhone},
      </if>
      <if test="receiverAddressId != null">
        RECEIVER_ADDRESS_ID = #{receiverAddressId},
      </if>
      <if test="receiverAddress != null and receiverAddress != ''">
        RECEIVER_ADDRESS = #{receiverAddress},
      </if>
      <if test="invoiceReceiverTraderContactId != null">
        INVOICE_RECEIVER_TRADER_CONTACT_ID = #{invoiceReceiverTraderContactId},
      </if>
      <if test="invoiceReceiverName != null and invoiceReceiverName != ''">
        INVOICE_RECEIVER_NAME = #{invoiceReceiverName},
      </if>
      <if test="invoiceReceiverPhone != null and invoiceReceiverPhone != ''">
        INVOICE_RECEIVER_PHONE = #{invoiceReceiverPhone},
      </if>
      <if test="invoiceReceiverAddressId != null">
        INVOICE_RECEIVER_ADDRESS_ID = #{invoiceReceiverAddressId},
      </if>
      <if test="invoiceReceiverAddress != null and invoiceReceiverAddress != ''">
        INVOICE_RECEIVER_ADDRESS = #{invoiceReceiverAddress},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete},
      </if>
      <if test="creator != null">
        CREATOR = #{creator},
      </if>
      <if test="updater != null">
        UPDATER = #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime},
      </if>
    </set>
    where FLOW_NODE_ID = #{flowNodeId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.FlowNodeEntity">
    <!--@mbg.generated-->
    update T_FLOW_NODE
    set FLOW_ORDER_ID = #{flowOrderId},
      OPEN_INVOICE = #{openInvoice},
      NODE_LEVEL = #{nodeLevel},
      PAYMENT_METHOD = #{paymentMethod},
      INVOICE_TYPE = #{invoiceType},
      AMOUNT = #{amount},
      CREDIT_PAYMENT = #{creditPayment},
      BALANCE = #{balance},
      BALANCE_DUE_DATE = #{balanceDueDate},
      TRADER_ID = #{traderId},
      TRADER_NAME = #{traderName},
      TRADER_CONTACT_ID = #{traderContactId},
      TRADER_CONTACT_NAME = #{traderContactName},
      TRADER_CONTACT_PHONE = #{traderContactPhone},
      TRADER_ADDRESS_ID = #{traderAddressId},
      TRADER_CONTACT_ADDRESS = #{traderContactAddress},
      RECEIVER_TRADER_CONTACT_ID = #{receiverTraderContactId},
      RECEIVER_NAME = #{receiverName},
      RECEIVER_PHONE = #{receiverPhone},
      RECEIVER_ADDRESS_ID = #{receiverAddressId},
      RECEIVER_ADDRESS = #{receiverAddress},
      INVOICE_RECEIVER_TRADER_CONTACT_ID = #{invoiceReceiverTraderContactId},
      INVOICE_RECEIVER_NAME = #{invoiceReceiverName},
      INVOICE_RECEIVER_PHONE = #{invoiceReceiverPhone},
      INVOICE_RECEIVER_ADDRESS_ID = #{invoiceReceiverAddressId},
      INVOICE_RECEIVER_ADDRESS = #{invoiceReceiverAddress},
      IS_DELETE = #{isDelete},
      CREATOR = #{creator},
      UPDATER = #{updater},
      CREATOR_NAME = #{creatorName},
      UPDATER_NAME = #{updaterName},
      ADD_TIME = #{addTime},
      MOD_TIME = #{modTime}
    where FLOW_NODE_ID = #{flowNodeId}
  </update>

<!--auto generated by MybatisCodeHelper on 2025-01-08-->
  <select id="findByFlowOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_FLOW_NODE
    where FLOW_ORDER_ID = #{flowOrderId}
      and IS_DELETE = 0
  </select>

  <select id="findByFlowNodeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_FLOW_NODE
    where FLOW_NODE_ID = #{flowNodeId}
      and IS_DELETE = 0
  </select>

  <select id="getPageList" resultType="com.vedeng.erp.system.domain.dto.FlowOrderRespDto"
          parameterType="com.vedeng.erp.system.domain.dto.FlowOrderReqDto">
    SELECT a.FLOW_ORDER_ID,
           a.FLOW_ORDER_NO,
           a.PUSH_DIRECTION,
           case a.PUSH_DIRECTION when 1 then '金蝶' when 2 then 'ERP' end as PUSH_DIRECTION_NAME,
           a.SOURCE_ERP,
           c.COMPANY_SHORT_NAME as SOURCE_ERP_NAME,
           a.BASE_BUSINESS_TYPE,
           case a.BASE_BUSINESS_TYPE when 1 THEN '采购' ELSE '销售' end                           as BASE_BUSINESS_TYPE_STR,
           a.BASE_ORDER_ID,
           a.BASE_ORDER_NO,
           a.CREATOR_NAME,
           a.AUDIT_STATUS,
           case a.AUDIT_STATUS when 0 THEN '未审核' ELSE '已审核' end as AUDIT_STATUS_STR ,
           a.AUDIT_USERNAME,
           DATE_FORMAT(a.AUDIT_TIME, '%Y-%m-%d %H:%i:%s') as AUDIT_TIME,
           DATE_FORMAT(a.ADD_TIME, '%Y-%m-%d %H:%i:%s') as ADD_TIME,
           a.CONTRACT_STATUS as CONTRACT_STATUS,
           CASE a.CONTRACT_STATUS
               WHEN 0 THEN '无需上传'
               WHEN 1 THEN '未上传'
               WHEN 2 THEN '已上传'
               ELSE ''
           END as CONTRACT_STATUS_STR,
           (SELECT MAX(foi.CONTRACT_FILE_NAME) FROM T_FLOW_ORDER_INFO foi
            WHERE foi.FLOW_NODE_ID IN (SELECT fn.FLOW_NODE_ID FROM T_FLOW_NODE fn
                                      WHERE fn.FLOW_ORDER_ID = a.FLOW_ORDER_ID
                                      AND fn.NODE_LEVEL = (SELECT MAX(NODE_LEVEL) FROM T_FLOW_NODE
                                                          WHERE FLOW_ORDER_ID = a.FLOW_ORDER_ID))) as CONTRACT_FILE_NAME,
           (SELECT DATE_FORMAT(MAX(foi.CONTRACT_UPLOAD_TIME), '%Y-%m-%d %H:%i:%s') FROM T_FLOW_ORDER_INFO foi
            WHERE foi.FLOW_NODE_ID IN (SELECT fn.FLOW_NODE_ID FROM T_FLOW_NODE fn
                                      WHERE fn.FLOW_ORDER_ID = a.FLOW_ORDER_ID
                                      AND fn.NODE_LEVEL = (SELECT MAX(NODE_LEVEL) FROM T_FLOW_NODE
                                                          WHERE FLOW_ORDER_ID = a.FLOW_ORDER_ID))) as CONTRACT_UPLOAD_TIME,
           CASE
               WHEN k.F_QZOK_BDDJTID IS NOT NULL THEN '已推送'
               ELSE '未推送'
               END                                                                                AS IS_PUSHED,
           (SELECT t1.TRADER_NAME
            FROM T_TRADER t1
            WHERE t1.TRADER_ID = (SELECT n1.TRADER_ID
                                  FROM T_FLOW_NODE n1
                                  WHERE n1.IS_DELETE = 0 and n1.FLOW_ORDER_ID = a.FLOW_ORDER_ID
                                    AND n1.NODE_LEVEL = (SELECT MIN(NODE_LEVEL)
                                                         FROM T_FLOW_NODE
                                                         WHERE IS_DELETE = 0 and FLOW_ORDER_ID = a.FLOW_ORDER_ID))) AS FIRST_TRADER_NAME,
           (SELECT t2.TRADER_NAME
            FROM T_TRADER t2
            WHERE t2.TRADER_ID = (SELECT n2.TRADER_ID
                                  FROM T_FLOW_NODE n2
                                  WHERE n2.IS_DELETE = 0 and n2.FLOW_ORDER_ID = a.FLOW_ORDER_ID
                                    AND n2.NODE_LEVEL = (SELECT MAX(NODE_LEVEL)
                                                         FROM T_FLOW_NODE
                                                         WHERE IS_DELETE = 0 and FLOW_ORDER_ID = a.FLOW_ORDER_ID))) AS LAST_TRADER_NAME
    FROM T_FLOW_ORDER a
           left join T_FLOW_ORDER_DETAIL d on a.FLOW_ORDER_ID = d.FLOW_ORDER_ID
           LEFT JOIN KING_DEE_INTERNAL_PROCUREMENT k ON a.FLOW_ORDER_ID = k.F_QZOK_BDDJTID
           left join T_BASE_COMPANY_INFO c on c.COMPANY_SHORT_NAME = a.SOURCE_ERP
    WHERE a.IS_DELETE = 0 and d.IS_DELETE = 0
      <if test="flowOrderNo != null and flowOrderNo != ''">
        AND a.FLOW_ORDER_NO like concat('%',#{flowOrderNo},'%')
      </if>
      <if test="pushDirection != null">
        AND a.PUSH_DIRECTION = #{pushDirection ,jdbcType=INTEGER}
      </if>
      <if test="sourceErp != null and sourceErp != ''">
        AND a.SOURCE_ERP = #{sourceErp,jdbcType=VARCHAR}
      </if>
      <if test="baseOrderNo != null and baseOrderNo != ''">
        AND a.BASE_ORDER_NO like concat('%',#{baseOrderNo},'%')
      </if>
    <if test="auditStatus != null">
      AND a.AUDIT_STATUS = #{auditStatus,jdbcType=INTEGER}
    </if>
    <if test="contractStatus != null">
      AND a.CONTRACT_STATUS = #{contractStatus,jdbcType=INTEGER}
    </if>
      <if test="creatorList != null and creatorList.size() != 0">
        AND a.CREATOR in
        <foreach collection="creatorList" open="(" close=")" item="item" index="index" separator=",">
          #{item}
        </foreach>
      </if>
      <if test="startAddTime != null and startAddTime != ''">
        AND a.ADD_TIME >= #{startAddTime,jdbcType=VARCHAR}
      </if>
      <if test="endAddTime != null and endAddTime != ''">
        AND a.ADD_TIME &lt;= #{endAddTime,jdbcType=VARCHAR}
      </if>
      <if test="baseBusinessType != null and baseBusinessType != ''">
        AND a.BASE_BUSINESS_TYPE = #{baseBusinessType}
      </if>
      <if test="skuNo != null and skuNo != '' or brand != null and brand != '' " >
        AND EXISTS (SELECT 1
              FROM T_FLOW_ORDER_DETAIL d
              WHERE d.FLOW_ORDER_ID = a.FLOW_ORDER_ID
              and d.IS_DELETE = 0
                <if test="skuNo != null and skuNo != ''">
                  AND d.SKU_NO like CONCAT('%', #{skuNo},'%')
                </if>
              <if test="brand != null and brand != ''">
                  AND d.BRAND like CONCAT('%', #{brand},'%')
              </if>
           )
      </if>

      <if test="lastTraderName != null and lastTraderName != ''">
        AND (SELECT t2.TRADER_NAME
              FROM T_TRADER t2
              WHERE t2.TRADER_ID = (SELECT n2.TRADER_ID
                    FROM T_FLOW_NODE n2
                    WHERE n2.IS_DELETE = 0 and n2.FLOW_ORDER_ID = a.FLOW_ORDER_ID
                    AND n2.NODE_LEVEL = (SELECT MAX(NODE_LEVEL)
                          FROM T_FLOW_NODE
                          WHERE FLOW_ORDER_ID = a.FLOW_ORDER_ID))) LIKE CONCAT('%', #{lastTraderName}, '%')
      </if>
        group by d.FLOW_ORDER_ID order by a.ADD_TIME desc
    </select>

  <select id="findBuySaleOrderInfo" resultType="com.vedeng.erp.system.domain.dto.FlowOrderBuySaleOrderDto">
    select node.NODE_LEVEL,
           b.FLOW_ORDER_INFO_NO buyOrderNo,
           b.PAYMENT_STATUS     buyOrderPayStatus,
           b.STORAGE_STATUS     buyOrderInStatus,
           b.INVOICE_STATUS     buyOrderInvoiceStatus,
           b.INVOICE_INFO       buyOrderInvoiceInfo,
           COALESCE(b.CONTRACT_FILE_URL, s.CONTRACT_FILE_URL) contractUrl,
           s.FLOW_ORDER_INFO_NO saleOrderNo,
           s.PAYMENT_STATUS     saleOrderReceiveStatus,
           s.STORAGE_STATUS     saleOrderOutStatus,
           s.INVOICE_STATUS     saleOrderInvoiceStatus,
           s.INVOICE_INFO       saleOrderInvoiceInfo
    from T_FLOW_ORDER o
           left join T_FLOW_NODE node on o.FLOW_ORDER_ID = node.FLOW_ORDER_ID
           left join T_FLOW_ORDER_INFO b
                     on node.FLOW_NODE_ID = b.FLOW_NODE_ID and b.FLOW_ORDER_INFO_TYPE = 0 and b.IS_DELETE = 0
           left join T_FLOW_ORDER_INFO s
                     on node.FLOW_NODE_ID = s.FLOW_NODE_ID and s.FLOW_ORDER_INFO_TYPE = 1 and s.IS_DELETE = 0
    where o.IS_DELETE = 0 and node.IS_DELETE = 0
      and o.FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
      and o.BASE_BUSINESS_TYPE = #{baseBusinessType,jdbcType=INTEGER}
  </select>


  <select id="getBuyOrderInfo" resultType="com.vedeng.erp.system.domain.dto.FlowOrderBuySaleOrderDto">
    select 0 NODE_LEVEL,
           b.BUYORDER_ID,
           b.BUYORDER_NO    buyOrderNo,
           b.PAYMENT_STATUS buyOrderPayStatus,
           b.ARRIVAL_STATUS buyOrderInStatus,
           b.INVOICE_STATUS buyOrderInvoiceStatus,
           b.CONTRACT_URL   contractUrl
    from T_FLOW_ORDER o
           left join T_BUYORDER b on o.BASE_ORDER_ID = b.BUYORDER_ID
    where o.BASE_BUSINESS_TYPE = 1 and o.FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
  </select>

  <select id="findBuyOrderInvoice" resultType="com.vedeng.erp.system.domain.dto.FlowInvoiceDto">
    select i.INVOICE_NO, i.INVOICE_HREF invoiceUrl
    from T_FLOW_ORDER o
           left join T_INVOICE i on o.BASE_ORDER_ID = i.RELATED_ID
    where o.BASE_BUSINESS_TYPE = 1 and i.TAG = 2 and  o.FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
  </select>

  <select id="getSaleOrderInfo" resultType="com.vedeng.erp.system.domain.dto.FlowOrderBuySaleOrderDto">
    select 0 NODE_LEVEL,
           s.SALEORDER_ID,
           s.SALEORDER_NO    saleOrderNo,
           s.PAYMENT_STATUS saleOrderReceiveStatus,
           s.ARRIVAL_STATUS saleOrderOutStatus,
           s.INVOICE_STATUS saleOrderInvoiceStatus,
           s.CONTRACT_URL   contractUrl
    from T_FLOW_ORDER o
           left join T_SALEORDER s on o.BASE_ORDER_ID = s.SALEORDER_ID
    where o.BASE_BUSINESS_TYPE = 2 and o.FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
  </select>

  <select id="findSaleOrderInvoice" resultType="com.vedeng.erp.system.domain.dto.FlowInvoiceDto">
    select i.INVOICE_NO, i.INVOICE_HREF invoiceUrl
    from T_FLOW_ORDER o
           left join T_INVOICE i on o.BASE_ORDER_ID = i.RELATED_ID
    where o.BASE_BUSINESS_TYPE = 2  and i.TAG = 1 and o.FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
  </select>

  <select id="getMaxNodeTraderName" resultType="java.lang.String">
    SELECT t.TRADER_NAME
    FROM T_TRADER t
    WHERE t.TRADER_ID = (
        SELECT n.TRADER_ID
        FROM T_FLOW_NODE n
        WHERE n.IS_DELETE = 0
        AND n.FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
        AND n.NODE_LEVEL = (
            SELECT MAX(NODE_LEVEL)
            FROM T_FLOW_NODE
            WHERE IS_DELETE = 0
            AND FLOW_ORDER_ID = #{flowOrderId,jdbcType=BIGINT}
        )
    )
  </select>
</mapper>
