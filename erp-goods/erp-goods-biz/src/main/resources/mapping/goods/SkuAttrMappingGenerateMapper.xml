<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.SkuAttrMappingGenerateMapper">


  <select id="getGoodsAttribute"  resultType="com.vedeng.goods.domain.dto.SkuAttrDto" parameterType="java.lang.String">
       SELECT
        K.SKU_NO,
        BA.BASE_ATTRIBUTE_NAME,
        BV.ATTR_VALUE,
        U.UNIT_NAME
    FROM
        V_SKU_ATTR_MAPPING A
        LEFT JOIN V_CORE_SKU K ON A.SKU_ID = K.SKU_ID
        LEFT JOIN V_BASE_ATTRIBUTE BA ON BA.BASE_ATTRIBUTE_ID = A.BASE_ATTRIBUTE_ID
        AND BA.IS_DELETED = 0
        LEFT JOIN V_BASE_ATTRIBUTE_VALUE BV ON BV.BASE_ATTRIBUTE_VALUE_ID = A.BASE_ATTRIBUTE_VALUE_ID
        AND BV.IS_DELETED = 0
        LEFT JOIN T_UNIT U ON BV.UNIT_ID = U.UNIT_ID
    WHERE
        A.BASE_ATTRIBUTE_VALUE_ID IS NOT NULL
        AND sku_no = #{skuNo,jdbcType=VARCHAR}
        AND A.`STATUS` =1
    </select>


</mapper>