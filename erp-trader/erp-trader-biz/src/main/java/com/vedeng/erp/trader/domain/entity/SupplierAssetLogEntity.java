package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 供应商资产变动日志
 */
@Getter
@Setter
public class SupplierAssetLogEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer supplierAssetLogId;

    /**
     * 供应商ID
     */
    private Integer traderSupplierId;

    /**
     * 供应商资产ID
     */
    private Integer supplierAssetId;

    /**
     * 资产改变类型：0->增加；1->减少 2->不变（占用发生时）
     */
    private Integer changeType;

    /**
     * 改变资产数量
     */
    private BigDecimal changeCount;

    /**
     * 变化前总量
     */
    private BigDecimal changeBeforeTotal;

    /**
     * 变化后总量
     */
    private BigDecimal changeAfterTotal;

    /**
     * 变化总量
     */
    private BigDecimal changeCountTotal;

    /**
     * 变化前可用总量
     */
    private BigDecimal changeBeforeApply;

    /**
     * 变化后可用总量
     */
    private BigDecimal changeAfterApply;

    /**
     * 变化可用总量
     */
    private BigDecimal changeCountApply;

    /**
     * 变化前占用总量
     */
    private BigDecimal changeBeforeOccupy;

    /**
     * 变化后占用总量
     */
    private BigDecimal changeAfterOccupy;

    /**
     * 变化占用总量
     */
    private BigDecimal changeCountOccupy;

    /**
     * 业务编号
     */
    private String businessNo;

    /**
     * 来源：
     * saleOrder.销售单
     * buyOrder.采购单
     * buyOrderExpense.采购费用单
     * buyOrderAfterSale.采购售后单
     * buyOrderExpenseAfterSale.采购费用售后单
     * buyOrderRebateChargeApply.采购返利结算收款申请
     */
    private String sourceType;

    /**
     * 资产类型
     * 1:余额
     * 2:返利
     * 3:信用
     */
    private Integer assetType;

    /**
     * 操作人员
     */
    private String operateUser;

    /**
     * 操作备注
     */
    private String operateNote;
}