package com.vedeng.erp.business.domain.vo;

import com.vedeng.erp.business.domain.entity.BusinessClues;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.business.domain.vo
 * @Date 2022/3/10 17:51
 */
public class BusinessCluesVo extends BusinessClues {

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 商机编号
     */
    private String businessChanceNo;

    /**
     * 归属销售
     */
    private Integer belongSale;

    /**
     * 归属销售姓名
     */
    private String belongSaleName;

    /**
     * 归属平台
     */
    private Integer belongPlatform;

    /**
     * 客户地区(拼接后的名字用于显示)
     */
    private String traderArea;

    /**
     * 推送时间-开始（转为时间戳传入sql）
     */
    private Long startTime;

    /**
     * 推送时间-截止（转为时间戳传入sql）
     */
    private Long endTime;

    /**
     * 推送时间-开始
     */
    private String startTimeStr;

    /**
     * 推送时间-截止
     */
    private String endTimeStr;

    /**
     * 客户地区id集合（逗号分隔，T_TRADER表字段）
     */
    private String areaIds;

    /**
     * 客户最小地区id（T_TRADER表字段）
     */
    private Integer areaId;

    /**
     * 省
     */
    private Integer province;

    /**
     * 市
     */
    private Integer city;

    /**
     * 区
     */
    private Integer zone;

    /**
     * 数据权限-当前用户及其递归下属的用户
     */
    private List<Integer> userIds;

    /**
     * 是否沟通
     */
    private Integer isCommunicate;

    public Integer getIsCommunicate() {
        return isCommunicate;
    }

    public void setIsCommunicate(Integer isCommunicate) {
        this.isCommunicate = isCommunicate;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public String getBusinessChanceNo() {
        return businessChanceNo;
    }

    public void setBusinessChanceNo(String businessChanceNo) {
        this.businessChanceNo = businessChanceNo;
    }

    public Integer getBelongSale() {
        return belongSale;
    }

    public void setBelongSale(Integer belongSale) {
        this.belongSale = belongSale;
    }

    public String getBelongSaleName() {
        return belongSaleName;
    }

    public void setBelongSaleName(String belongSaleName) {
        this.belongSaleName = belongSaleName;
    }

    public Integer getBelongPlatform() {
        return belongPlatform;
    }

    public void setBelongPlatform(Integer belongPlatform) {
        this.belongPlatform = belongPlatform;
    }

    public String getTraderArea() {
        return traderArea;
    }

    public void setTraderArea(String traderArea) {
        this.traderArea = traderArea;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public String getAreaIds() {
        return areaIds;
    }

    public void setAreaIds(String areaIds) {
        this.areaIds = areaIds;
    }

    public String getStartTimeStr() {
        return startTimeStr;
    }

    public void setStartTimeStr(String startTimeStr) {
        this.startTimeStr = startTimeStr;
    }

    public String getEndTimeStr() {
        return endTimeStr;
    }

    public void setEndTimeStr(String endTimeStr) {
        this.endTimeStr = endTimeStr;
    }

    public List<Integer> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Integer> userIds) {
        this.userIds = userIds;
    }

    public Integer getAreaId() {
        return areaId;
    }

    public void setAreaId(Integer areaId) {
        this.areaId = areaId;
    }

    public Integer getProvince() {
        return province;
    }

    public void setProvince(Integer province) {
        this.province = province;
    }

    public Integer getCity() {
        return city;
    }

    public void setCity(Integer city) {
        this.city = city;
    }

    public Integer getZone() {
        return zone;
    }

    public void setZone(Integer zone) {
        this.zone = zone;
    }
}
