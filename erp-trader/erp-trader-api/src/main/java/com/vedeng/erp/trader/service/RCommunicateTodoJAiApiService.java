package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;

/**
 * <AUTHOR>
 */
public interface RCommunicateTodoJAiApiService {


    /**
     * ai分析处理结果：生成商机
     * @param record
     */
    void createBusinessChange(RCommunicateTodoJAiDto record);

    /**
     * ai分析处理结果：添加修改客户标签
     * @param record
     */
    void updateTraderSign(RCommunicateTodoJAiDto record);
}
