package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchRollbackInvoiceProcessDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.writer.BatchRollbackInvoiceWrite;
import com.vedeng.erp.kingdee.batch.writer.BatchRollbackReceiveFeeWrite;
import com.vedeng.erp.kingdee.batch.writer.BatchRollbackVirtualInvoiceWrite;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 蓝字发票回滚批处理
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class InvoiceRollbackBatchJob extends BaseJob {

    @Autowired
    private BatchRollbackInvoiceProcessor batchRollbackInvoiceProcessor;

    @Autowired
    private BatchRollbackPaymentProcessor batchRollbackPaymentProcessor;

    @Autowired
    private BatchRollbackInvoiceWrite batchRollbackInvoiceWrite;

    @Autowired
    private BatchRollbackInvoiceForSaleProcessor batchRollbackInvoiceForSaleProcessor;

    @Autowired
    private BatchRollbackReceiveProcessor batchRollbackReceiveProcessor;

    @Autowired
    private BatchRollbackReceiveFeeWrite batchRollbackReceiveFeeWrite;

    @Autowired
    private BatchRollbackVirtualPaymentProcessor batchRollbackVirtualPaymentProcessor;

    @Autowired
    private BatchRollbackVirtualInvoiceWrite batchRollbackVirtualInvoiceWrite;

    @Autowired
    private BatchRollbackVirtualInvoiceProcessor batchRollbackVirtualInvoiceProcessor;


    public Job rollbackBlueValidInvoiceJob() {
        return jobBuilderFactory.get("rollbackBlueValidInvoiceJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(rollbackBlueVirtualInvoice())
                .next(rollbackBlueValidInvoice())
                .next(rollbackVirtualPayCommon())
                .next(rollbackPayCommon())
                .next(rollbackBlueValidInvoiceForSale())
                .next(rollbackReceiveFee())
                .build();
    }

    private Step rollbackBlueValidInvoice() {
        return stepBuilderFactory.get("作废采购蓝票")
                .<BatchRollbackInvoiceDto, BatchRollbackInvoiceProcessDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchRollbackInvoiceDtoItemReader(null, null))
                .processor(batchRollbackInvoiceProcessor)
                .writer(batchRollbackInvoiceWrite)
                .listener(baseReadListener )
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step rollbackPayCommon() {
        return stepBuilderFactory.get("作废采购应付单")
                .<BatchRollbackInvoiceDto, BatchRollbackInvoiceProcessDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchRollbackInvoiceDtoItemReader(null, null))
                .processor(batchRollbackPaymentProcessor)
                .writer(batchRollbackInvoiceWrite)
                .listener(baseReadListener )
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step rollbackBlueVirtualInvoice() {
        return stepBuilderFactory.get("作废虚拟采购蓝票")
                .<BatchRollbackInvoiceDto, BatchRollbackInvoiceProcessDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchVirtualRollbackInvoiceDtoItemReader(null, null))
                .processor(batchRollbackVirtualInvoiceProcessor)
                .writer(batchRollbackVirtualInvoiceWrite)
                .listener(baseReadListener )
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step rollbackVirtualPayCommon() {
        return stepBuilderFactory.get("作废虚拟采购应付单")
                .<BatchRollbackInvoiceDto, BatchRollbackInvoiceProcessDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchVirtualRollbackInvoiceDtoItemReader(null, null))
                .processor(batchRollbackVirtualPaymentProcessor)
                .writer(batchRollbackVirtualInvoiceWrite)
                .listener(baseReadListener )
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 回滚蓝字有效票(销售)
     */
    private Step rollbackBlueValidInvoiceForSale(){
        return stepBuilderFactory.get("作废销售蓝票")
                .<BatchRollbackInvoiceDto, List<BatchRollbackInvoiceProcessDto>>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchRollbackInvoiceDtoItemForSaleReader(null, null))
                .processor(batchRollbackInvoiceForSaleProcessor)
                .writer(batchRollbackReceiveFeeWrite)
                .listener(baseReadListener )
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 回滚蓝字有效(销售)关联应收单
     */
    private Step rollbackReceiveFee() {
        return stepBuilderFactory.get("作废销售应收单")
                .<BatchRollbackInvoiceDto, List<BatchRollbackInvoiceProcessDto>>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchRollbackInvoiceDtoItemForSaleReader(null, null))
                .processor(batchRollbackReceiveProcessor)
                .writer(batchRollbackReceiveFeeWrite)
                .listener(baseReadListener )
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchRollbackInvoiceDto> batchRollbackInvoiceDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        return getRollbackInvoiceDtoItemReader(beginTime, endTime);
    }

    private CommonMybatisItemReader<BatchRollbackInvoiceDto> getRollbackInvoiceDtoItemReader(
            String beginTime, String endTime) {
        BatchRollbackInvoiceDto batchRollbackInvoiceDto = BatchRollbackInvoiceDto
                .builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchRollbackInvoiceDto.class.getSimpleName(), batchRollbackInvoiceDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchRollbackInvoiceDto> batchVirtualRollbackInvoiceDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchRollbackInvoiceDto batchRollbackInvoiceDto = BatchRollbackInvoiceDto
                .builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchRollbackInvoiceDto.class.getSimpleName(),"findVirtualInvoiceDisable", batchRollbackInvoiceDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchRollbackInvoiceDto> batchRollbackInvoiceDtoItemForSaleReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchRollbackInvoiceDto batchRollbackInvoiceDto = BatchRollbackInvoiceDto
                .builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchRollbackInvoiceDto.class.getSimpleName(),"findAfterSaleDeprecateBlueInvoice", batchRollbackInvoiceDto);
    }

}
