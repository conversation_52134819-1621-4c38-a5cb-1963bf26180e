void function () {
    new Vue({
        el: '#page-container',
        data: {
            tableHeaders: [
                {
                    key: "traderCustomerName",
                    label: "客户名称",
                    width: "220px",
                    lock: true
                },
                {
                    key: "taskContent",
                    label: "任务内容",
                    width: "300px"
                },
                {
                    key: "bizNo",
                    label: "业务编号",
                    width: "200px"
                },
                {
                    key: "mainTaskTypeLabel",
                    label: "任务类型",
                    width: "120px"
                },
                {
                    key: "doneStatus",
                    label: "处理状态",
                    width: "96px"
                },
                {
                    key: "isOverTime",
                    label: "是否超时",
                    width: "96px"
                },
                {
                    key: "applyUserName",
                    label: "发起人",
                    width: "160px",
                    avatar: 'applyUserAliasHeadPicture'
                },
                {
                    key: "todoUserNames",
                    label: "待办人",
                    width: "170px",
                    avatar: 'todoUserAliasHeadPicture'
                },
                {
                    key: "deadline",
                    label: "截止时间",
                    width: "180px",
                    sortable: true
                },
                {
                    key: "commitTime",
                    label: "提交时间",
                    width: "180px",
                    sortable: true,
                    sortKey: 'commit_time'
                },
                {
                    key: "doneRemark",
                    label: "处理结果说明",
                    width: "220px",
                },
                {
                    key: "option",
                    label: "操作",
                    width: "87px",
                },
            ],
            chanceTypeList: [],
            defaultTab: [{
                label: '我的任务',
                id: '1'
            }, {
                label: '我的发起',
                id: '2'
            }],
            defaultSearchParams: {
                listType: ''
            },
            searchParams: {
                bizNo: '',
                taskContent: '',
                creatorId: '',
                creatorIdList: [],
                todoUserId: '',
                todoUserIdList: [],
                doneStatus: '',
                overdueStatus: '',
                startDeadline: '',
                endDeadline: '',
                filterDeadline: [],
                startCommitTime: '',
                endCommitTime: '',
                filterCommitTime: [],
                taskTypeIds: {},
                mainTaskTypeList: [],
                subTaskTypListe: []
            },
            doneStatusList: [
                {
                    label: '待处理',
                    value: '0'
                },
                {
                    label: '已处理',
                    value: '1'
                },
                {
                    label: '已关闭',
                    value: '2'
                },
            ],
            overTimeList: [
                {
                    label: '超时',
                    value: '2'
                },
                {
                    label: '正常',
                    value: '1'
                }
            ],
            userRemoteInfo: {
                url: '/crm/task/profile/getCreator',
                paramsType: 'url',
                paramsKey: 'username',
                parseLabel: 'userName',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            allUserRemoteInfo: {
                url: '/crm/user/profile/getAllUserInfo',
                paramsKey: 'name',
                parseLabel: 'username',
                parseValue: 'userId',
                parseAvatar: 'aliasHeadPicture'
            },
            creatorRemoteInfo: {},
            touserRemoteInfo: {},
            taskTypeList: [],
            loading: true,
            currentTabId: '1',
            defaultCreatorItems: [],
            defaultTodoUserItems: [],
            firstOpenDetail: true, //首次加载页面，如果页面参数有taskId，默认打开弹层
        },
        created() {
            if (GLOBAL.getQuery('endDate')) {
                let endDate = GLOBAL.getQuery('endDate');
                this.searchParams.startDeadline = endDate + ' 00:00:00';
                this.searchParams.endDeadline = endDate + ' 23:59:59';
                this.searchParams.filterDeadline = [endDate, endDate];
            }

            if (GLOBAL.getQuery('businessNo')) {
                this.searchParams.bizNo = GLOBAL.getQuery('businessNo');
            }

            if (GLOBAL.getQuery('list') == 2) {
                this.defaultSearchParams.listType = 2;
                this.searchParams.creatorIdList = [USERINFO.userId];
                this.defaultCreatorItems = [{
                    label: USERINFO.userName,
                    value: USERINFO.userId
                }]
                this.currentTabId = '2';
                this.tableHeaders[7].sortable = false;
                this.tableHeaders.splice(10, 1);
                this.creatorRemoteInfo = this.userRemoteInfo;
                this.touserRemoteInfo = this.allUserRemoteInfo;
            } else {
                this.defaultSearchParams.listType = 1;
                this.searchParams.todoUserIdList = [USERINFO.userId];
                this.defaultTodoUserItems = [{
                    label: USERINFO.userName,
                    value: USERINFO.userId
                }]

                if (GLOBAL.getQuery('noStatus') != 1) {
                    this.searchParams.doneStatus = '0';
                }
                
                this.currentTabId = '1';
                this.creatorRemoteInfo = this.allUserRemoteInfo;
                this.touserRemoteInfo = this.userRemoteInfo;
            }

            let startDeadline = GLOBAL.getQuery('initStartDeadLine');
            let endDeadline = GLOBAL.getQuery('initEndDeadLine');

            if(startDeadline && endDeadline) {
                this.searchParams.filterDeadline = [startDeadline, endDeadline];
                this.searchParams.startDeadline = startDeadline + ' 00:00:00';
                this.searchParams.endDeadline = endDeadline + ' 23:59:59';
            }

            let mainTaskType = GLOBAL.getQuery('initTaskType');
            if(mainTaskType) {
                this.searchParams.mainTaskTypeList = mainTaskType.split(',');
            }

            this.getTaskTypeList();
            this.loading = false;
        },
        methods: {
            handlerListSearch(list) {
                if (this.firstOpenDetail && GLOBAL.getQuery('taskItemId')) {
                    this.firstOpenDetail = false;
                    list.forEach(item => {
                        if(item.taskItemId == GLOBAL.getQuery('taskItemId')) {
                            if(item.canHandle) {
                                this.handleRw(item);
                            } else {
                                this.lookRenwu(item);
                            }
                        }
                    })
                }
            },
            getTaskTypeList() {
                this.$axios.post('/crm/task/profile/cascadeGetTaskType').then(({ data }) => {
                    if (data.success) {
                        this.taskTypeList = data.data;
                    }
                })
            },
            remoteSelectChange(key) {
                this.searchParams[key + 'List'] = [this.searchParams[key]]
            },
            handlerFilterDateChange(key, value) {
                let startKey = 'start' + key;
                let endKey = 'end' + key;

                this.searchParams[startKey] = value[0] ? value[0] + ' 00:00:00' : '';
                this.searchParams[endKey] = value[1] ? value[1] + ' 23:59:59' : '';
            },
            hanlderTabChange(item) {
                if (item.id == 2) {
                    window.location.href = '/crm/task/profile/index?list=2';
                } else {
                    window.location.href = '/crm/task/profile/index';
                }
            },

            // 处理任务
            handleRw(item) {
                this.$refs.handleRenwu.open(item);
            },
            // 查看任务
            lookRenwu(item) {
                let query = Object.assign({}, item, {
                    listType: this.defaultSearchParams.listType
                }) 
                this.$refs.lookRenwu.open(query);
            },
            taskDone() {
                this.$refs.listContainer.refresh();
            },
            handlerReset() {
                if (GLOBAL.getQuery('list') == 2) {
                    window.location.href = '/crm/task/profile/index?list=2';
                } else {
                    window.location.href = '/crm/task/profile/index';
                }
            },
            getToUserName(data) {
                if(data.subTodoUserList && data.subTodoUserList.length) {
                    let name = [];
                    data.subTodoUserList.forEach(item => {
                        name.push(item.userName);
                    });

                    return name.join(',');
                } else {
                    return '-';
                }
            }
        }
    })
}.call(this);