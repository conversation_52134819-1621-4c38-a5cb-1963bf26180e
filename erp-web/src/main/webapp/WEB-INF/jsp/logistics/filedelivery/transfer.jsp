<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>CRM</title>
    <script type="text/javascript" src='/static/js/common/jquery.min.js'></script>
    <script src="https://wwcdn.weixin.qq.com/node/open/js/wecom-jssdk-2.0.2.js"></script>
    <script type="text/javascript">
        window.onload = function(){
            // 初始化登录组件
            const wwLogin = ww.createWWLoginPanel({
                el: '#ww_login',
                params: {
                    login_type: 'CorpApp',
                    appid: 'ww877e627a6426776c',
                    agentid: '1000062',
                    redirect_uri: 'http://qa.lxcrm.vedeng.com/test',
                    state: 'loginState',
                    redirect_type: 'callback',
                },
                onCheckWeComLogin({ isWeComLogin }) {
                    console.log(isWeComLogin)
                },
                onLoginSuccess({ code }) {
                    console.log({ code })
                    $.ajax({
                        type: "POST",
                        url: "/crm/wx/getweixinuser?code="+code,
                        dataType: 'json',
                        success: function (data) {
                            console.log(data);
                        }
                    });
                },
                onLoginFail(err) {
                    console.log(err)
                },
            })
        }
    </script>
</head>
<body>
<div style="align-content: center">test
<div id="ww_login">


</div>
</div>


</body>
</body>
</html>
