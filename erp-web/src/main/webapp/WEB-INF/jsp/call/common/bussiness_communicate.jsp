<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增沟通记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<div class="formpublic">
        <form method="post" action="" id="addCommunicate">
            <ul>
                <li>
                    <div class="infor_name">
                        <lable>客户名称</lable>
                    </div>
                    <div class="f_left">
                        <div>
                            ${traderName}
                        </div>
                        <div id="traderNameError"></div>
                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <lable>归属销售</lable>
                    </div>
                    <div class="f_left">
                        <div id="salesNameStrDiv">
                            ${saleUser.username}
                        </div>

                    </div>
                </li>

                <li>
                    <div class="infor_name">
                        <lable>联系人</lable>
                    </div>
                    <div class="f_left  ">
                        <input name="contact">
                    </div>
                    <div class="infor_name">
                        <lable>手机</lable>
                    </div>
                    <div class="f_left  ">
                        <input name="contactMob">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>沟通时间</lable>
                    </div>
                    <div class="f_left ">
                        <input class="Wdate f_left input-small mr4" type="text" placeholder="请选择时间" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'end\')}'})" name="begin" id="begin" value="${startTime}"/>
                        <div class="gang mr5">-</div>
                        <input class="Wdate f_left input-small" type="text" placeholder="请选择时间" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'begin\')}'})" name="end" id="end" value="${endTime}"/>
	                    <div id="timeErrorMsg"></div>
                    </div>
                </li>
                <c:if test="${ bussinessType eq 391 || bussinessType eq 394}">
                    <li>
                        <div class="infor_name">
                            <span>*</span>
                            <lable>商机精准度</lable>
                        </div>
                        <div class="f_left">
                            <div>
                                <select class="mr5" name="businessChanceAccuracy" id="businessChanceAccuracy">
                                    <option selected="selected" value="-1">请选择</option>
                                    <option value="0">无法判断</option>
                                    <option value="1">不精准</option>
                                    <option value="2">一般精准</option>
                                    <option value="3">高精准</option>
                                </select>
                                <c:if test="${ belongPlatfromByOrgAndUser eq 0}">
                                    <div class="tip-wrap cus-tip"  style="display:inline;">
                                        <i class="vd-icon icon-info2" style="background: none">
                                            <div class="tip arrow-left">
                                                <div class="tip-con">
                                                    高精准：客户属于本部门经营对象，购买意向为公司签约商品或者一年内有成交过的商品；
                                                    <br/>一般精准：客户属于本部门经营对象，但当前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                                                    <br/>不精准：客户不属于本部门经营对象，且目前没有明确购买意向或者需求商品不是公司签约商品和一年内有成交过的商品
                                                    <br/>无法判断：未接通（连续三天联系不上）
                                                </div>
                                            </div>
                                            <span class="arrow arrow-out">
               <span class="arrow arrow-in"></span>
              </span>
                                        </i>
                                    </div>
                                </c:if>
                                <c:if test="${ belongPlatfromByOrgAndUser eq 1}">
                                    <div class="tip-wrap cus-tip"  style="display:inline;">
                                        <i class="vd-icon icon-info2" style="background: none">
                                            <div class="tip arrow-left">
                                                <div class="tip-con">
                                                    高精准：客户有明确购买意向，且需求与我司匹配
                                                    <br/>一般精准：客户有明确购买意向，但需求与我司不匹配
                                                    <br/>不精准：客户目前没有明确购买意向
                                                    <br/>无法判断：未接通（连续三天联系不上）
                                                </div>
                                            </div>
                                            <span class="arrow arrow-out">
               <span class="arrow arrow-in"></span>
              </span>
                                        </i>
                                    </div>
                                </c:if>
                            </div>
                        </div>
                    </li>
                </c:if>
                <li style="margin-top:-5px;">
                    <div class="infor_name mt0">
                        <span>*</span>
                        <lable>沟通内容</lable>
                    </div>
                    <div class="f_left table-largest">
						<div class="inputfloat manageaddtag">
							<label class=" mr8">您可以从这些标签中选择</label>
							<c:if test="${not empty tagList }">
								<c:forEach items="${tagList }" var="tag">
									<span onclick="addTag(${tag.tagId},'${tag.tagName }',this);">${tag.tagName }</span>
								</c:forEach>
							</c:if>
							<c:if test="${page.totalPage > 1}">
							<div class="change" onclick="changeTag(${page.totalPage},10,this,32);"><span class="m0">换一批(</span><span class="m0" id="leftNum">${page.totalPage}</span><span class="m0">)</span>
							<input type="hidden" id="pageNo" value="${page.pageNo}">
							</div>
							</c:if>
						</div>
						<div class="inputfloat <c:if test="${empty tagList }">mt8</c:if>">
							<input type="text" id="defineTag" placeholder="如果标签中没有您所需要的，请自行填写"
								class="input-large">
							<div class="f_left bt-bg-style bg-light-blue bt-small  addbrand" onclick="addDefineTag(this);">添加</div>
						</div>
						<div class="addtags">
							<ul id="tag_show_ul">
							</ul>
						</div>
                        <div>
                                <textarea  name="contentSuffix" placeholder="沟通内容最多输入200字符，请检查后提交"
                                           style="width: 450px; height: 100px"></textarea>
                            <div id="contentSuffixError" ></div>
                        </div>
					</div>
                </li>
                <li>
                    <div class="infor_name">
                        <span>*</span>
                        <lable>下次沟通时间</lable>
                    </div>
                    <div class="f_left inputfloat ">
                        <input class="Wdate input-small" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'end\')}'})" name="nextDate" id="nextDate"/>
                    </div>
                    <input type="checkbox" id="noneNextDate"> 暂无下次沟通时间
                    <input type="hidden" id="noneNextDateVal" name="noneNextDate">
                </li>
                <li>
                    <div class="infor_name">
                        <lable>下次沟通内容</lable>
                    </div>
                    <div class="f_left  ">
                        <input type="text" class="input-xxx" name="nextContactContent" id="nextContactContent">
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>备注</lable>
                    </div>
                    <div class="f_left  ">
                        <input type="text" class="input-xxx" name="comments" id="comments">
                    </div>
                </li>
            </ul>
            <div class="add-tijiao tcenter">
            	<input type="hidden" id="traderId" name="traderId" value="0" >
                <input type="hidden" id="phone" name="phone" value="${callOut.phone}">
                <input type="hidden" id="coid" name="coid" value="${callOut.coid}">
                <input type="hidden" id="callType" name="callType" value="${callOut.callType}">
                <input type="hidden" id="relatedId" name="relatedId" value="${callOut.orderId}">
                <input type="hidden" name="traderName" value="${traderName}">
                <input type="hidden" name="ownerUsername" value="${saleUser.username}">
               <%-- 去电--%>
                <input type="hidden" name="communicateMode" value="250">
            	<input type="hidden" name="traderType" value="1" >
            	<input type="hidden" name="callFrom" value="1" >
                <%--商机--%>
            	<input type="hidden" name="communicateType" value="244" >
            	<input type="hidden" name="coidType" value="2" >
                <input type="hidden" id="communicateTime" name="communicateTime" value="${communicateTime}">
                <button type="submit" id="submit">提交</button>
            </div>
        </form>
    </div>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/js/call/bussiness_communicate.js?rnd=${resourceVersionKey}"></script>
	<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    $(document).ready(function() {
        setTimeout(function(){
            $.ajax({
                url: "${pageContext.request.contextPath}/system/call/pushVoiceMp3.do",
                type: "post",
                timeout: 60000,
                data: {
                    "coid":'${param.coid}'
                },
                dataType : "json",
                success: function(data) {
                    console.log("通话完成，发起AI解析。");
                }
            });
        }, 2000);
    });


</script>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
<style>
    .cus-tip .icon-info2 {
        color: #09f;
        vertical-align: -3px;
    }

    .cus-tip .tip {
        z-index: 9999; /* 一个较大的数值 */
        max-width: 500px;
        display: none;
    }

    .cus-tip:hover .tip {
        display: block;
    }
</style>
<%@ include file="../../common/footer.jsp"%>
