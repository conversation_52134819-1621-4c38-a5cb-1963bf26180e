package com.vedeng.erp.broadcast.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Getter;
import lombok.Setter;

/**
 * 自定义归属用户表
 */
@Getter
@Setter
public class BroadcastDeptUserOverrideEntity extends BaseEntity {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * ERP用户ID
     */
    private Integer erpUserId;

    /**
     * 归属通知小组ID
     */
    private Integer broadcastDeptId;

    /**
     * 是否已删除（0=否，1=是）
     */
    private Integer isDeleted;
}
