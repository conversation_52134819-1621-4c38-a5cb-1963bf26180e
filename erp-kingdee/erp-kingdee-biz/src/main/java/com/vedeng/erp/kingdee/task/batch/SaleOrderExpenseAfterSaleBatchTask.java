package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.SaleOrderExpenseAfterSaleBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @create 2023−01-06 下午5:33
 * @description
 */
@JobHandler(value = "SaleOrderExpenseAfterSaleBatchTask")
@Component
public class SaleOrderExpenseAfterSaleBatchTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private SaleOrderExpenseAfterSaleBatchJob batchJob;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.saleOrderExpenseAfterSaleJob();
        jobLauncher.run(job, jobParameters);
        return SUCCESS;
    }
}
