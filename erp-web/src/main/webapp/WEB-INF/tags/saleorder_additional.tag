<%@ tag language="java" pageEncoding="UTF-8"%>

<style>
    .additional-form-item {
        display: flex;
    }

    .additional-form-label {
        width: 96px;
        text-align: right;
        margin-right: 10px;
    }

    .additional-form-field {
        flex: 1;
    }

    .additional-add-wrap {
        display: flex;
        align-items: center;
    }

    .additional-add-wrap .additional-add-btn {
        color: #09f;
        cursor: pointer;
    }

    .additional-add-wrap .additional-add-tip {
        color: #999;
    }

    .additional-add-wrap .additional-add-btn:hover {
        color: #f60;
    }

    .additional-detail {
        margin-top: 5px;
        line-height: 18px;
    }

    .additional-detail-item {
        display: flex;
        margin-bottom: 5px;
    }

    .additional-detail-item:last-child {
        margin-bottom: 0;
    }

    .additional-detail-item .item-txt {
        flex: 1;
    }
</style>

<div class="additional-form-item">
    <div class="additional-form-label">
        附加条款：
    </div>
    <div class="additional-form-field J-additional-form-wrap" style="display: none;">
        <div class="additional-add-wrap">
            <div class="additional-add-btn J-additional-add">添加</div>
            <div class="additional-add-tip">（将会添加到合同条款“第十四项”中）</div>
        </div>
        <div class="additional-detail J-additional-detail" style="display: none;">
            <!-- <div class="additional-detail-item">
                <div class="item-num">1.</div>
                <div class="item-txt">甲方承诺本次采购的AED仅限甲方企业内部接受过心肺复苏和自动体外除额器使用培训合格的人员使用，绝不涉及对外销售、转赠、交换等经营行为。AED的安装地址为：南京市秦淮区永丰大道36号南京天安数码城01幢1007室。</div>
            </div> -->
        </div>
    </div>
</div>

<script>
    $(function() {
        var saleorderId = $("input[name='saleorderId']").val();
        var saleorderNo = $('.J-salerorder-no').html().trim();

        $('.J-additional-add').click(function(){
            let title = '添加附加条款';
            if($('.J-additional-detail').find('.additional-detail-item').length) {
                title = '编辑附加条款'
            }
    
            layer.open({
                type: 2,
                shadeClose: false, //点击遮罩关闭
                area: ['960px','calc(100vh - 100px)'],
                title: title,
                content: '/orderstream/saleorder/additionalClause.do?saleorderId=' + saleorderId + '&saleorderNo=' + saleorderNo,
                success: function(layero, index) {
                    //layer.iframeAuto(index);
                }
            });
        })
    
        window.addtionalCallback = function(data) {
            $('.J-additional-detail').empty();

            if(data.additionalClauseItemShowList && data.additionalClauseItemShowList.length) {
    
                data.additionalClauseItemShowList.forEach((item, index) => {
                    $('.J-additional-detail').append('<div class="additional-detail-item">' +
                        '<div class="item-num">' + (index + 1) + '、</div>' + 
                        '<div class="item-txt">' + item.additionalClauseItem + '</div>' + 
                    '</div>')
                });
        
                $('.J-additional-detail').show();
        
                $('.J-additional-add').html('编辑')
            } else {
                $('.J-additional-detail').hide();
                $('.J-additional-add').html('添加')
            }
        }

        $.ajax({
            url: '/order/additionalClause/query.do',
            type: 'post',
            data: JSON.stringify({
                saleorderId: saleorderId,
            }),
            contentType: 'application/json',
            dataType: 'json',
            success: function(res) {
                if(res.code === 0) {
                    addtionalCallback(res.data);
                }

                $('.J-additional-form-wrap').show();
            }
        })
    })

    
</script>