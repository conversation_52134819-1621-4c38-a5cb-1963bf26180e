package com.vedeng.common.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.order.model.Saleorder;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.trader.group.model.TraderGroup;
import com.vedeng.trader.model.RTraderGroupJTrader;
import com.vedeng.trader.model.dto.TraderBaseInfoDto;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <b>Description:</b><br>
 * 基础service 接口
 * 
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.common.service <br>
 *       <b>ClassName:</b> BaseService <br>
 *       <b>Date:</b> 2017年4月25日 上午11:13:50
 */
public interface BaseService {

	/**
	 * <b>Description:</b><br>
	 * 根据父id查询子列表
	 *
	 * @param parentId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年9月15日 上午9:21:48
	 */
	List<SysOptionDefinition> getSysOptionDefinitionListByParentId(Integer parentId);
	/**
	 * <b>Description:</b><br>
	 * 查询字典表对象
	 *
	 * @param SysOptionDefinitionId
	 * @return
	 * @Note <b>Author:</b> east <br>
	 *       <b>Date:</b> 2017年9月15日 上午11:01:34
	 */
	SysOptionDefinition getSysOptionDefinitionById(Integer SysOptionDefinitionId);

	String getApiUri();

	List<SysOptionDefinition> getSysOptionDefinitionList(String optionType);

	SysOptionDefinition getFirstSysOptionDefinitionList(String optionType);

	void sendTemplateMsgHcForShip(Saleorder saleorder, Map<String, String> sTempMap);

	void sendTemplateVxService(Saleorder saleorders, Map<String, String> sTempMap);
	boolean isCouponsType(Integer orderType);

	/**
	 * 获取客户基本信息
	 *
	 * @param traderId
	 * @param traderType
	 * @return
	 */
	TraderBaseInfoDto getTraderBaseInfoByTraderId(Integer traderId, Integer traderType);


	/***
	 * @description: 获取客户分群信息
	 * @param
	 * @return {@link Map< Integer, RTraderGroupJTrader>}
	 * @throws
	 * <AUTHOR>
	 * @date 2021/3/8 10:41
	 */
	Map<Integer,RTraderGroupJTrader> getTraderGroup(TraderGroup group , List<Integer> traderIds);



	/**
	 * 保存采购单收票状态信息
	 * @param buyorderId
	 */
	void saveBuyorderInvoiceStatus(Integer buyorderId);

	String getAddressByAreaId(Integer areaId);

	/**
	 * 获取当前登录主体
	 * @param request
	 * @return
	 */
	User getSessionUser(HttpServletRequest request);

	/**
	 * <AUTHOR>
	 * @desc 发送科研购公众号消息
	 * @param saleorder
	 * @param sMap
	 */
	void sendKygDeliveryOrder(Saleorder saleorder, Map sMap);

	/**
	 * <AUTHOR>
	 * @desc 根据销售单id校验专票信息
	 * @param saleorderId
	 * @return
	 */
	ResultInfo<String> verifySpecialTicket(Integer saleorderId,Integer invoiceType,Integer invoiceTraderId);

	/**
	 * 检查当前用户是否有相关按钮权限
	 * @param uri
	 * @return
	 */
	boolean checkPermission(String uri,String userName);
}
