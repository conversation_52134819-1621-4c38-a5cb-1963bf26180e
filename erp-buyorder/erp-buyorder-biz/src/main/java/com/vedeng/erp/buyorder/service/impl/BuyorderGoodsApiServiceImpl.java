package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.erp.buyorder.domain.entity.BuyorderGoods;
import com.vedeng.erp.buyorder.dto.BuyorderGoodsApiDto;
import com.vedeng.erp.buyorder.mapper.BuyorderGoodsMapper;
import com.vedeng.erp.buyorder.mapstruct.BuyOrderGoodsConvertor;
import com.vedeng.erp.buyorder.service.BuyorderGoodsApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BuyorderGoodsApiServiceImpl implements BuyorderGoodsApiService {

    @Autowired
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Autowired
    private BuyOrderGoodsConvertor buyOrderGoodsConvertor;

    @Override
    public List<BuyorderGoodsApiDto> queryBuyOrderGoodsList(Integer buyOrderId) {
        List<BuyorderGoods> buyorderGoods = buyorderGoodsMapper.findByBuyorderId(buyOrderId);
        List<BuyorderGoodsApiDto> buyorderGoodsApiDtos = buyOrderGoodsConvertor.to(buyorderGoods);
        return buyorderGoodsApiDtos;
    }
}
