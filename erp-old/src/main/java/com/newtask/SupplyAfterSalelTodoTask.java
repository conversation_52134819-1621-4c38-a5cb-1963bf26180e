package com.newtask;

import com.vedeng.aftersales.dao.AfterSaleSupplyPolicyMapper;
import com.vedeng.aftersales.model.dto.AfterSaleSupplyPolicyDto;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.todolist.model.TodoList;
import com.vedeng.todolist.service.impl.MaintainDataSupplyAfterSalePolicy;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;


/**
 * 供应商售后政策待办任务
 */
@Component
@JobHandler(value="supplyAfterSalelTodoTask")
public class SupplyAfterSalelTodoTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(SupplyAfterSalelTodoTask.class);

    @Autowired
    private MaintainDataSupplyAfterSalePolicy maintainDataSupplyAfterSalePolicy;

    @Resource
    private AfterSaleSupplyPolicyMapper supplyPolicyMapper;

    @Override
    public ReturnT<String> doExecute(String executeParam) throws Exception {

        XxlJobLogger.log("SupplyAfterSalelTodoTask start.......");

        List<TodoList> todoTaskList  = maintainDataSupplyAfterSalePolicy.getUnHandledListByBuzType();

        if(CollectionUtils.isEmpty(todoTaskList)){
            XxlJobLogger.log("没有对应的供应商售后政策的待办任务.....");
            return SUCCESS;
        }

        try {
            for(TodoList todoTask : todoTaskList){
                dealWithAfterSalePolicyTodoTask(todoTask);
            }
        }catch (Exception e){
            XxlJobLogger.log("供应商售后政策完成代办任务错误",e);
            return FAIL;
        }

        XxlJobLogger.log("SupplyAfterSalelTodoTask end.......");

        return SUCCESS;
    }

    private void dealWithAfterSalePolicyTodoTask(TodoList todoTask) {

        List<AfterSaleSupplyPolicyDto> afterSaleSupplyPolicyDtoList = supplyPolicyMapper.getSupplyAfterSalePolicyListBySkuNo(todoTask.getBuzExtra());

        if(CollectionUtils.isEmpty(afterSaleSupplyPolicyDtoList)){
            return;
        }

        XxlJobLogger.log("完成sku:{},供应商的代办任务:",todoTask.getBuzExtra());
        maintainDataSupplyAfterSalePolicy.finish(todoTask.getBuzId());
    }

}
