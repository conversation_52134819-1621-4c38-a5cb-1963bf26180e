package com.task.service.impl;

import cn.hutool.core.util.StrUtil;
import com.task.service.KingDeeBankBillDoOrderService;
import com.task.service.KingDeeBankBillTaskService;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.dto.PayVedengBankDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayVedengBankApiService;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeReceiveRefundBillMapper;
import com.vedeng.erp.kingdee.service.KingDeePayBillApiService;
import com.vedeng.system.service.VerifiesRecordService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2022/9/19 13:31
 **/
@Service
@Slf4j
public class KingDeeBankBillTaskServiceImpl implements KingDeeBankBillTaskService {

    public static final String END_EVENT = "endEvent";
    @Autowired
    private KingDeePayBillApiService kingDeePayBillApiService;
    @Autowired
    private KingDeeBankBillDoOrderService kingDeeBankBillDoOrderService;
    @Autowired
    private PayApplyApiService payApplyApiService;
    @Autowired
    private BankBillApiService bankBillApiService;
    @Autowired
    private PayVedengBankApiService payVedengBankApiService;
    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;
    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;
    @Autowired
    private KingDeeReceiveRefundBillMapper kingDeeReceiveRefundBillMapper;


    @Override
    public void doBankBillAndVerify(KingDeePayBillDto data, Boolean receiveRefundBill) {
        log.info("金蝶回单数据处理开始：{}", data);
        //审批操作
        this.auditTask(data);

        PayApplyDto payApplyDto = payApplyApiService.queryInfoByPayApplyId(Integer.valueOf(data.getFBillNo()));
        log.info("查到的付款单信息：{}", payApplyDto);
        if (payApplyDto.getPayStatus().equals(ErpConstant.ONE) && payApplyDto.getValidStatus().equals(ErpConstant.ONE)) {
            return;
        }
        // 更新付款审核状态
        payApplyApiService.updateValidStatus(1, payApplyDto.getPayApplyId());
        // 更新付款状态
        payApplyApiService.updatePayStatusBill(1, payApplyDto.getPayApplyId());
        // 新增银行流水
        BankBillDto bankBillDto = this.addBankBill(data, payApplyDto);
        if (receiveRefundBill){
            //更新金蝶收款退款单信息
            kingDeeReceiveRefundBillMapper.updateLshByFBillNo(data.getFBillNo(),data.getFQzokLsh());
        }else {
            // 更新金蝶付款单信息
            kingDeePayBillApiService.updateKingDeePayBillReturnUrlAndTranFlow(data);
        }
        //采购单付款申请通过
        if (payApplyDto.getPayType().equals(SysOptionConstant.ID_517)) {
            try {
                kingDeeBankBillDoOrderService.buyOrderVerify(payApplyDto, bankBillDto, data);
                actionProcdefService.updateInfo("T_BUYORDER", "BUYORDER_ID", payApplyDto.getRelatedId(), "LOCKED_STATUS", 0, 2);
            } catch (Exception e) {
                log.error("付款申请审核通过，采购单后续流程异常:", e);
                throw new ServiceException("付款申请审核通过，采购单后续流程异常");
            }
        }

        // 售后付款申请通过
        if (payApplyDto.getPayType().equals(SysOptionConstant.ID_518)) {
            try {
                kingDeeBankBillDoOrderService.afterOrderVerify(payApplyDto, bankBillDto, data);
            } catch (Exception e) {
                log.error("付款申请审核通过，售后后续流程异常:", e);
                throw new ServiceException("付款申请审核通过，售后后续流程异常");
            }

        }
        //采购费用付款申请通过
        if (payApplyDto.getPayType().equals(SysOptionConstant.ID_4125)) {
            try {
                kingDeeBankBillDoOrderService.buyOrderExpenseVerify(payApplyDto, bankBillDto, data);
                actionProcdefService.updateInfo("T_BUYORDER_EXPENSE", "BUYORDER_EXPENSE_ID", payApplyDto.getRelatedId(), "LOCKED_STATUS", 0, 2);
            } catch (Exception e) {
                log.error("付款申请审核通过，采购费用单后续流程异常:", e);
                throw new ServiceException("付款申请审核通过，采购费用单后续流程异常");
            }
        }
    }

    /**
     * 审核任务
     *
     * @param data data
     * @return 任务id
     */
    private void auditTask(KingDeePayBillDto data) {
        try {
            TaskService taskService = processEngine.getTaskService();
            Task taskInfoPay = taskService.createTaskQuery().processInstanceBusinessKey("paymentVerify_" + data.getFBillNo()).singleResult();
            if (Objects.nonNull(taskInfoPay) && StrUtil.isNotEmpty(taskInfoPay.getId())) {
                log.info("未查到审核任务号：{}", data);
                String taskId = taskInfoPay.getId();
                Map<String, Object> variables = new HashMap<>(1);
                variables.put("pass", true);
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskId, "金蝶", "njadmin", variables);
                //如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals(END_EVENT)) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }
            }
        } catch (Exception e) {
            log.error("KingDeeBankBillTask 定时任务触发审核失败:", e);
            throw new ServiceException("定时任务触发审核失败");
        }
    }

    /**
     * 新增银行流水
     *
     * @param data        data
     * @param payApplyDto payApplyDto
     * @return BankBillDto
     */
    private BankBillDto addBankBill(KingDeePayBillDto data, PayApplyDto payApplyDto) {
        BankBillDto bankBillDto = new BankBillDto();
        if (!StringUtils.isEmpty(data.getFQzokLsh())) {

            if (Objects.nonNull(payApplyDto)) {
                PayVedengBankDto payVedengBankDto = payVedengBankApiService.queryInfoByPayBankNo(data.getFQzokYhzhhm());

                Date nowDate = new Date();
                if (Objects.equals(payVedengBankDto.getPayVedengBankId(), 1)) {
                    //中国银行
                    bankBillDto.setBankTag(3);
                }
                if (Objects.equals(payVedengBankDto.getPayVedengBankId(), 2)) {
                    //南京银行
                    bankBillDto.setBankTag(2);
                }
                if (Objects.equals(payVedengBankDto.getPayVedengBankId(), 3)) {
                    //交行
                    bankBillDto.setBankTag(6);
                }
                if (Objects.equals(payVedengBankDto.getPayVedengBankId(), 7)) {
                    //民生银行
                    bankBillDto.setBankTag(7);
                }
                bankBillDto.setTranFlow(data.getFQzokLsh());
                bankBillDto.setTrandate(nowDate);
                bankBillDto.setTrantime(nowDate);
                bankBillDto.setCompanyId(1);
                bankBillDto.setRealTrandate(nowDate);
                bankBillDto.setRealTrandatetime(nowDate);
                bankBillDto.setAccName1(payApplyDto.getTraderName());
                bankBillDto.setAmt(payApplyDto.getAmount());
                bankBillDto.setAccBankno(payApplyDto.getBankCode());
                bankBillDto.setAccno2(payApplyDto.getBankAccount());
                bankBillDto.setCadbankNm(payApplyDto.getBank());
                bankBillDto.setDet(data.getFQzokCgddh());
                bankBillDto.setCadbankNm(payApplyDto.getBank());
                //付款-转出-借方
                bankBillDto.setFlag1(ErpConst.ZERO);
                bankBillApiService.add(bankBillDto);
            }

        }
        return bankBillDto;
    }

}
