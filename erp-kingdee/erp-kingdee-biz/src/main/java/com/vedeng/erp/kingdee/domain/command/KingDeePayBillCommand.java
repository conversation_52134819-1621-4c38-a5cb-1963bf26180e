package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class KingDeePayBillCommand {
    /**
     * 主键
     */
    private Integer kingDeePayBillId;

    /**
     * 云星空系统单据FID值
     */
    private String fId;

    /**
     * 单据编号
     */
    private String fBillNo;

    /**
     * 结算组织id
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();

    /**
     * 单据类型id
     */
    private KingDeeNumberCommand fSettleOrgId = new KingDeeNumberCommand();

    /**
     * 付款组织id
     */
    private KingDeeNumberCommand fPayOrgId = new KingDeeNumberCommand();

    /**
     * 单据日期
     */
    private String fDate;

    /**
     * 往来单位类型
     */
    private String fContactUnitType;

    /**
     * 往来单位id
     */
    private KingDeeNumberCommand fContactUnit = new KingDeeNumberCommand();

    /**
     * 收款单位类型
     */
    private String fRectUnitType;

    /**
     * 收款单位id
     */
    private KingDeeNumberCommand fRectUnit = new KingDeeNumberCommand();

    /**
     * 结算币别Id
     */
    private KingDeeNumberCommand fCurrencyId = new KingDeeNumberCommand();

    /**
     * 汇率
     */
    private BigDecimal fExchangeRate;

    /**
     * 结算汇率
     */
    private BigDecimal fSettleRate;

    /**
     * 业务类型
     */
    private String fBusinessType;

    /**
     * 是否相同组织0否1是
     */
    private Boolean fIsSameOrg;

    /**
     * 是否信贷业务0否1是
     */
    private Boolean fIsCredit;

    /**
     * 结算币别id
     */
    private KingDeeNumberCommand fSettleCur = new KingDeeNumberCommand();

    /**
     * 是否转销0否1是
     */
    private Boolean fIsWriteOff;

    /**
     * 实报实付0否1是
     */
    private Boolean fRealPay;

    /**
     * 备注
     */
    private String fRemark;

    /**
     * 是否下推携带汇率到结算汇率0否1是
     */
    private Boolean fIsCarryRate;

    /**
     * 结算本位币
     */
    private KingDeeNumberCommand fSettleMainBookId = new KingDeeNumberCommand();

    /**
     * 多收款人0否1是
     */
    private Boolean fMoreReceive;

    /**
     * 来源系统
     */
    private String fSourceSystem;

    /**
     * 付款单条目
     */
    private List<KingDeePayBillEntryCommand> FPayBillEntry = new ArrayList<>();

    /**
     * 付款单原单条目
     */
    private List<KingDeePayBillSrcEntryCommand> FPayBillSrcEntry = new ArrayList<>();
    /**
     * 是否erp导入，默认为Y
     */
    private String f_Qzok_dr;
    /**
     * 交易类型
     */
    private String f_Qzok_Jylx;

    /**
     * 交易主体
     */
    private String f_Qzok_Jyzt;

    /**
     * 付款erp业务单号
     */
    private String f_Qzok_Cgddh;

    /**
     * 流水号
     */
    private String f_qzok_lsh;

    /**
     * VDERP-16089  "往来单位" 所选择资金流水的订单单号 F_QZOK_PZGSYWDH
     * T_CAPITAL_BILL_DETAIL.ORDER_NO（订单单号）
     */
    private String f_qzok_pzgsywdh;

    /**
     * 提交银行标识
     * 启用推送金蝶付款单自动审核：是  1
     * 启用推送金蝶付款单自动审核：否  2
     *
     */
    private String f_qzok_zdtjyh;


    @Data
    public static class KingDeePayBillEntryCommand {
        /**
         * 结算方式
         */
        private KingDeeNumberCommand fsettletypeid = new KingDeeNumberCommand();
        /**
         * 付款用途
         */
        private KingDeeNumberCommand fpurposeid = new KingDeeNumberCommand();
        /**
         * 应付金额
         */
        private BigDecimal fpaytotalamountfor;
        /**
         * 长短款
         */
        private BigDecimal fovershortagefor;
        /**
         * 手续费
         */
        private BigDecimal fhandlingchargefor;
        /**
         * 费用项目
         */
        private KingDeeNumberCommand fcostid = new KingDeeNumberCommand();
        /**
         * 费用承担部门
         */
        private KingDeeNumberCommand fexpensedeptidE = new KingDeeNumberCommand();
        /**
         * 我方银行账号
         */
        private KingDeeNumberCommand faccountid = new KingDeeNumberCommand();
        /**
         * 结算号
         */
        private String fsettleno;
        /**
         * 备注
         */
        private String fcomment;
        /**
         * 登账日期
         */
        private String fpostdate;
        /**
         * 收款方类型
         */
        private String fRecType;
        /**
         * 入账类型
         */
        private String fRuZhangType;
        /**
         * 支付类型
         */
        private String fPayType;
        /**
         * 收款账户账号
         */
        private String fOppositeBankAccount;

        /**
         * 收款账户名称
         */
        private String fOppositeCcountName;

        /**
         * 收款单位开户行
         */
        private String fOppositeBankName;

        /**
         * 付款单位联行号
         */
        private String fCnaps;

        /**
         * 原始订单号
         */
        private String f_qzok_ysddh;

        /**
         * 归属业务单号
         */
        private String f_qzok_gsywdh;

        /**
         * 业务类型
         */
        private String f_qzok_ywlx;
    }

    @Data
    public static class KingDeePayBillSrcEntryCommand{
        /**
         * fEntryID
         */
        private String fEntryID;
        /**
         * 源单类型
         */
        private String fsourcetype;
        /**
         * 源单编号
         */
        private String fsrcbillno;
        /**
         * 计划付款金额
         */
        private String fplanpayamount;
        /**
         * 本次付款金额
         */
        private String frealpayamountS;
        /**
         * 应付金额
         */
        private String fafttaxtotalamount;
        /**
         * 结算金额
         */
        private String fsettleamount;
        /**
         * 到期日
         */
        private String fexpiry;
        /**
         * 源单内码
         */
        private String fsrcbillid;
        /**
         * 源单行内码
         */
        private String fsrcrowid;
        /**
         * 源单行号
         */
        private String fsrcseq;
        /**
         * fsrcqty
         */
        private String fsrcqty;
        /**
         * 备注
         */
        private String fsrcremark;
        /**
         * 源单币别
         */
        private KingDeeNumberCommand fsrccurrencyid = new KingDeeNumberCommand();

        private List<KingDeePayBillSrcEntryLinkCommand> fpaybillsrcentryLink;
    }

    @Data
    public static class KingDeePayBillSrcEntryLinkCommand{
        /**
         * 关联关系实体主键
         */
        private String fLinkId;
        /**
         * 转换规则
         */
        private String fpaybillsrcentryLinkFruleid;
        /**
         * 推进路线
         */
        private String fpaybillsrcentryLinkFflowlineid;
        /**
         * 源单表内码
         */
        private String fpaybillsrcentryLinkFstableid;
        /**
         * 源单表
         */
        private String fpaybillsrcentryLinkFstablename;
        /**
         * 源单内码
         */
        private String fpaybillsrcentryLinkFsbillid;
        /**
         * 源单分录内码
         */
        private String fpaybillsrcentryLinkFsid;
    }
}
