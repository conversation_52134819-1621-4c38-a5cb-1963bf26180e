package com.vedeng.erp.finance.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 红字确认单列表出参
 */
@Data
public class SaleInvoiceRedConfirmListResponseDto extends TaxesReturnInfo {

    /**
     * 总条数
     */
    private int totalRecords;

    /**
     * 当前页数据
     */
    private List<ConfirmData> data;

    @Data
    public static class ConfirmData {
        /**
         * 确认单返回代码
         */
        private String code;

        /**
         * 确认单返回消息
         */
        private String message;

        /**
         * 确认单 UUID
         */
        private String uuid;

        /**
         * 红字发票信息确认单编号
         */
        private String hzfpxxqrdbh;

        /**
         * 录入方身份
         */
        private String lrfsf;

        /**
         * 销售方名称
         */
        private String xsfnsrsbh;

        /**
         * 销售方名称
         */
        private String xsfmc;

        /**
         * 购买方纳税人识别号
         */
        private String gmfnsrsbh;

        /**
         * 购买方名称
         */
        private String gmfmc;

        /**
         * 对方纳税人识别号
         */
        private String dfnsrsbh;

        /**
         * 对方纳税人名称
         */
        private String dfnsrmc;

        /**
         * 蓝字发票代码
         */
        private String lzfpdm;

        /**
         * 蓝字发票号码
         */
        private String lzfphm;

        /**
         * 蓝字开票日期 (格式: YYYY-MM_DD HH:MM:SS)
         */
        private String lzkprq;

        /**
         * 蓝字合计金额
         */
        private BigDecimal lzhjje;

        /**
         * 蓝字合计税额
         */
        private BigDecimal lzhjse;

        /**
         * 发票种类代码
         */
        private String fppzDm;

        /**
         * 发票状态代码
         */
        private String fpztDm;

        /**
         * 蓝字发票票种代码
         */
        private String lzfppzDm;

        /**
         * 蓝字发票特定要素类型代码
         */
        private String lzfpTdyslxDm;

        /**
         * 蓝字发票类型代码2
         */
        private String lzfplxDm2;

        /**
         * 增值税用途代码
         */
        private String zzsytDm;

        /**
         * 消费税用途代码
         */
        private String xfsytDm;

        /**
         * 发票入账状态代码
         */
        private String fprzztDm;

        /**
         * 红字撤销金额
         */
        private BigDecimal hzcxje;

        /**
         * 红字撤销税额
         */
        private BigDecimal hzcxse;

        /**
         * 红字确认单明细数量
         */
        private Integer hzqrdmxsl;

        /**
         * 撤回原因代码
         */
        private String chyyDm;

        /**
         * 红字确认信息状态代码
         */
        private String hzqrxxztDm;

        /**
         * 确认日期
         */
        private String qrrq;

        /**
         * 已开具红字发票标志
         */
        private String ykjhzfpbz;

        /**
         * 红字发票号码
         */
        private String hzfphm;

        /**
         * 红字开票日期 (格式: YYYY-MM_DD HH:MM:SS)
         */
        private String hzkprq;

        /**
         * 有效标志
         */
        private String yxbz;

        /**
         * 录入人身份id
         */
        private String lrrsfid;

        /**
         * 修改人身份id
         */
        private String xgrsfid;

        /**
         * 录入日期
         */
        private String lrrq;

        /**
         * 修改日期
         */
        private String xgrq;

        /**
         * 数据产生地区
         */
        private String sjcsdq;

        /**
         * 数据归属地区
         */
        private String sjgsdq;

        /**
         * 数据同步时间
         */
        private String sjtbSj;

        /**
         * varchar
         */
        private String ywqdDm;

        /**
         * 地区纳税人识别号
         */
        private String dqnsrsbh;

        /**
         * 购销方身份
         */
        private String gxsf;

        /**
         * 明细按钮控制代码
         */
        private List<String> mxButtonCtrlDm;

        /**
         * 确认方名称
         */
        private String qrfmc;

        /**
         * 价税合计
         */
        private BigDecimal jshj;

        /**
         * 是否纸质发票标志
         */
        private String sfzzfpbz;

        /**
         * 销售方省市级税务机关代码
         */
        private String xsfssjswjgDm;

        /**
         * 销售方地市级税务机关代码
         */
        private String xsfdsjswjgDm;

        /**
         * 销售方主管税务局代码
         */
        private String xsfzgswjDm;

        /**
         * 购买方省市级税务机关代码
         */
        private String gmfssjswjgDm;

        /**
         * 购买方地市级税务机关代码
         */
        private String gmfdsjswjgDm;

        /**
         * 购买方主管税务局代码
         */
        private String gmfzgswjDm;

        /**
         * 差额征收类型代码
         */
        private String cezslxDm;

        /**
         * 红字确认信息明细列表
         */
        private List<String> hzqrxxmxList;

        /**
         * 发票来源代码
         */
        private String fplyDm;

        /**
         * varchar
         */
        private String gjbq;

        /**
         * 开票方纳税人识别号
         */
        private String kpfnsrsbh;
    }
}