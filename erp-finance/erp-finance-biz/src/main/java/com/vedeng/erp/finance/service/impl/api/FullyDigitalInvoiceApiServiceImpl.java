package com.vedeng.erp.finance.service.impl.api;

import com.google.common.eventbus.EventBus;
import com.vedeng.common.core.utils.ErpSpringBeanUtil;
import com.vedeng.erp.finance.dto.DownloadInvoice;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.dto.InvoiceRedConfirmationApiDto;
import com.vedeng.erp.finance.dto.OpenInvoiceResultDto;
import com.vedeng.erp.finance.enums.OpenInvoiceRouteEnum;
import com.vedeng.erp.finance.enums.SalesOpenInvoiceTypeEnum;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceApiService;
import com.vedeng.erp.finance.service.FullyDigitalInvoiceService;
import com.vedeng.erp.finance.service.IOpenInvoiceInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 财务数电发票
 * @date 2023/9/20 13:03
 */
@Service
@Slf4j
public class FullyDigitalInvoiceApiServiceImpl implements FullyDigitalInvoiceApiService {

    @Autowired
    FullyDigitalInvoiceService fullyDigitalInvoiceService;

    @Autowired
    @Qualifier(value = "eventBusCenter")
    private EventBus eventBusCenter;

    @Override
    public boolean isEnableGlobal() {
        return fullyDigitalInvoiceService.isEnableGlobal();
    }

    @Override
    public OpenInvoiceResultDto openBlueInvoice(InvoiceApplyDto invoiceApplyDto) {
        return fullyDigitalInvoiceService.openBlueInvoice(invoiceApplyDto);
    }


    /**
     * 开票申请
     * @param invoiceApplyDto 开票申请
     * @param salesOpenInvoiceTypeEnum 开票类型
     * @return
     */
    @Override
    public OpenInvoiceResultDto openSaleInvoice(InvoiceApplyDto invoiceApplyDto, SalesOpenInvoiceTypeEnum salesOpenInvoiceTypeEnum) {
        // 查找路由
        Class<? extends IOpenInvoiceInterface> clazz = OpenInvoiceRouteEnum.getClazzByInterfaceCodeEnum(salesOpenInvoiceTypeEnum);
        if (Objects.isNull(clazz)){
            log.error("根据映射未匹配具体实现类，请检查配置SalesOpenInvoiceTypeEnum");
            return null;
        }
        IOpenInvoiceInterface invoiceInterface = ErpSpringBeanUtil.getBean(clazz);
        // 执行开票
        OpenInvoiceResultDto openInvoiceResultDto = invoiceInterface.openInvoice(invoiceApplyDto);
        return openInvoiceResultDto;
    }

    @Override
    public void openRedInvoice(InvoiceRedConfirmationApiDto invoiceRedConfirmationApiDto) {
        eventBusCenter.post(invoiceRedConfirmationApiDto);
    }

    @Override
    public void downloadInvoice(DownloadInvoice downloadInvoice) {
        fullyDigitalInvoiceService.downloadInvoice(downloadInvoice);
    }

    @Override
    public String getBlueInvoiceOpenDate(String invoiceNo) {
        return fullyDigitalInvoiceService.getBlueInvoiceOpenDate(invoiceNo);
    }
}
