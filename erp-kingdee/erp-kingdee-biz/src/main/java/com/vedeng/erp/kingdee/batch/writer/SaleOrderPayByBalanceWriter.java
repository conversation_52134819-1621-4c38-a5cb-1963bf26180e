package com.vedeng.erp.kingdee.batch.writer;

import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeNeedReceiveDto;
import com.vedeng.erp.kingdee.service.KingDeeNeedReceiveAdjustApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 客户余额支付销售订单、售后手续费writer
 */
@Service
@Slf4j
public class SaleOrderPayByBalanceWriter extends BaseWriter<KingDeeNeedReceiveDto> {

    @Autowired
    private KingDeeNeedReceiveAdjustApiService kingDeeNeedReceiveAdjustApiService;
    @Override
    public void doWrite(KingDeeNeedReceiveDto dto, JobParameters params, ExecutionContext stepContext) throws Exception {
        dto.setKingDeeBizEnums(KingDeeBizEnums.saveNeedReceiveAdjust);
        kingDeeNeedReceiveAdjustApiService.register(dto,true);
    }
}
