<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.kpi.replica.dao.SalesPerformanceDeptReplicaMapper" >
    <resultMap id="BaseResultMap" type="com.vedeng.saleperformance.model.SalesPerformanceDept">
        <id column="SALES_PERFORMANCE_DEPT_ID" jdbcType="INTEGER" property="salesPerformanceDeptId" />
        <result column="SALES_PERFORMANCE_GROUP_ID" jdbcType="INTEGER" property="salesPerformanceGroupId" />
        <result column="DEPT_NAME" jdbcType="VARCHAR" property="deptName" />
        <result column="IS_DELETE" jdbcType="BIT" property="isDelete" />
        <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="BIGINT" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    </resultMap>


    <select id="getDeptByUserId" resultMap="BaseResultMap">
        SELECT
               D.*
        FROM T_SALES_PERFORMANCE_DEPT D
            LEFT JOIN T_SALES_PERFORMANCE_DEPT_USER DU ON D.SALES_PERFORMANCE_DEPT_ID = DU.SALES_PERFORMANCE_DEPT_ID
        WHERE DU.USER_ID = #{userId}
    </select>


</mapper>