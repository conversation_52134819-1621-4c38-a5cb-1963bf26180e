<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchBuyOrderContractDtoMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.domain.entity.KingDeeBuyOrderContractEntity">
        <!--@mbg.generated-->
        <!--@Table KING_DEE_BUYORDER_CONTRACT-->
        <id column="KING_DEE_BUYORDER_CONTRACT_ID" jdbcType="INTEGER" property="kingDeeBuyorderContractId" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
        <result column="F_ID" jdbcType="INTEGER" property="fId" />
        <result column="F_QZOK_OrgId" jdbcType="VARCHAR" property="fQzokOrgid" />
        <result column="F_QZOK_HTH" jdbcType="VARCHAR" property="fQzokHth" />
        <result column="F_QZOK_HTRQ" jdbcType="VARCHAR" property="fQzokHtrq" />
        <result column="F_QZOK_HTJE" jdbcType="VARCHAR" property="fQzokHtje" />
        <result column="F_QZOK_SLL" jdbcType="VARCHAR" property="fQzokSll" />
        <result column="F_QZOK_DDH" jdbcType="VARCHAR" property="fQzokDdh" />
        <result column="FBillNo" jdbcType="VARCHAR" property="FBillNo" />
    </resultMap>
    <sql id="Base_Column_List">
    <!--@mbg.generated-->
    KING_DEE_BUYORDER_CONTRACT_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME,
    UPDATER_NAME, F_ID, F_QZOK_OrgId, F_QZOK_HTH, F_QZOK_HTRQ, F_QZOK_HTJE, F_QZOK_SLL,
    F_QZOK_DDH,FBillNo
    </sql>

    <select id="queryBuyOrderContract" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBuyOrderContractDto">
        select tb.BUYORDER_ID,
               tb.BUYORDER_NO,
               tb.TOTAL_AMOUNT,
               tb.VALID_TIME,
               sod.COMMENTS as rate,
               kdbc.F_ID as dataId
        from T_BUYORDER tb
                 left join T_ATTACHMENT ta
                           on tb.BUYORDER_ID = ta.RELATED_ID and ta.ATTACHMENT_FUNCTION = 514 and ta.IS_DELETED = 0
                 left join KING_DEE_BUYORDER_CONTRACT kdbc on kdbc.F_QZOK_DDH = tb.BUYORDER_NO
                 left join T_SYS_OPTION_DEFINITION sod on tb.INVOICE_TYPE = sod.SYS_OPTION_DEFINITION_ID
        left join T_VERIFIES_INFO tvi on tvi.RELATE_TABLE = 'T_BUYORDER_CONTRACT' and tvi.RELATE_TABLE_KEY = tb.BUYORDER_ID and tvi.ADD_TIME = ta.ADD_TIME
        where kdbc.KING_DEE_BUYORDER_CONTRACT_ID is null
            and ta.ATTACHMENT_ID is not null
        and (tvi.STATUS is null or (tvi.STATUS is not null and tvi.STATUS = 1))
        <if test="validTimeBegin == null and validTimeEnd == null">
            and tb.VALID_TIME >= 1609430400000
        </if>
        <if test="validTimeBegin != null">
            and tb.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        </if>
        <if test="validTimeEnd != null">
            and tb.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
        </if>
        group by tb.BUYORDER_ID
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>

    <select id="queryBuyOrderContractFile" resultType="com.vedeng.erp.kingdee.batch.dto.BatchBuyOrderContractDto">
        select kdbc.F_ID as dataId,
               ta.NAME,
               ta.DOMAIN,
               ta.URI,
               ta.ATTACHMENT_ID,
               ta.SUFFIX,
               tb.BUYORDER_NO,
               tb.BUYORDER_ID,
               ta.ADD_TIME as attachmentAddTime
        from KING_DEE_BUYORDER_CONTRACT kdbc
                 left join T_BUYORDER tb on kdbc.F_QZOK_DDH = tb.BUYORDER_NO
                 left join T_ATTACHMENT ta
                           on tb.BUYORDER_ID = ta.RELATED_ID and ta.ATTACHMENT_FUNCTION = 514 and ta.IS_DELETED = 0
                 left join KING_DEE_FILE_DATA kdfd on CONCAT(#{ossPre,jdbcType=VARCHAR},ta.DOMAIN,ta.URI) = kdfd.URL and kdfd.FORM_ID = 'QZOK_BDCGHT'
        left join T_VERIFIES_INFO tvi on tvi.RELATE_TABLE = 'T_BUYORDER_CONTRACT' and tvi.RELATE_TABLE_KEY = tb.BUYORDER_ID and tvi.ADD_TIME = ta.ADD_TIME
        where kdfd.ID is null
            and ta.ATTACHMENT_ID is not null
            and (tvi.STATUS is null or (tvi.STATUS is not null and tvi.STATUS = 1))
        <if test="validTimeBegin == null and validTimeEnd == null">
            and tb.VALID_TIME >= 1609430400000
        </if>
        <if test="validTimeBegin != null">
            and tb.VALID_TIME <![CDATA[>=]]> #{validTimeBegin,jdbcType=BIGINT}
        </if>
        <if test="validTimeEnd != null">
            and tb.VALID_TIME <![CDATA[<=]]> #{validTimeEnd,jdbcType=BIGINT}
        </if>
        limit #{_pagesize} OFFSET #{_skiprows}
    </select>
</mapper>