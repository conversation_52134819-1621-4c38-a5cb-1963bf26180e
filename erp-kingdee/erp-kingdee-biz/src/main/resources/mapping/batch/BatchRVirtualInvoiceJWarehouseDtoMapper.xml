<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.kingdee.batch.repository.BatchRVirtualInvoiceJWarehouseDtoMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto">
    <!--@mbg.generated-->
    <!--@Table T_R_VIRTUAL_INVOICE_J_WAREHOUSE-->
    <id column="VIRTUAL_INVOICE_WAREHOUSE_ID" jdbcType="INTEGER" property="virtualInvoiceWarehouseId" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="VIRTUAL_INVOICE_ID" jdbcType="INTEGER" property="virtualInvoiceId" />
    <result column="VIRTUAL_INVOICE_ITEM_ID" jdbcType="INTEGER" property="virtualInvoiceItemId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="warehouseGoodsOutInId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ITEM_ID" jdbcType="INTEGER" property="warehouseGoodsOutInItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="OUT_IN_TYPE" jdbcType="INTEGER" property="outInType" />
  </resultMap>
  <resultMap id="BaseResultMapAndOutInNo" type="com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto">
    <!--@mbg.generated-->
    <!--@Table T_R_VIRTUAL_INVOICE_J_WAREHOUSE-->
    <id column="VIRTUAL_INVOICE_WAREHOUSE_ID" jdbcType="INTEGER" property="virtualInvoiceWarehouseId" />
    <result column="VIRTUAL_INVOICE_ID" jdbcType="INTEGER" property="virtualInvoiceId" />
    <result column="VIRTUAL_INVOICE_ITEM_ID" jdbcType="INTEGER" property="virtualInvoiceItemId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ID" jdbcType="INTEGER" property="warehouseGoodsOutInId" />
    <result column="WAREHOUSE_GOODS_OUT_IN_ITEM_ID" jdbcType="INTEGER" property="warehouseGoodsOutInItemId" />
    <result column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="NUM" jdbcType="DECIMAL" property="num" />
    <result column="OUT_IN_TYPE" jdbcType="INTEGER" property="outInType" />
    <result column="OUT_IN_NO" jdbcType="VARCHAR" property="outInNo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    VIRTUAL_INVOICE_WAREHOUSE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME, VIRTUAL_INVOICE_ID, VIRTUAL_INVOICE_ITEM_ID, WAREHOUSE_GOODS_OUT_IN_ID, 
    WAREHOUSE_GOODS_OUT_IN_ITEM_ID, SKU_ID, SKU, NUM, OUT_IN_TYPE
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    where VIRTUAL_INVOICE_WAREHOUSE_ID = #{virtualInvoiceWarehouseId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    where VIRTUAL_INVOICE_WAREHOUSE_ID = #{virtualInvoiceWarehouseId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="VIRTUAL_INVOICE_WAREHOUSE_ID" keyProperty="virtualInvoiceWarehouseId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_VIRTUAL_INVOICE_J_WAREHOUSE (ADD_TIME, MOD_TIME, CREATOR, 
      UPDATER, CREATOR_NAME, UPDATER_NAME, 
      VIRTUAL_INVOICE_ID, VIRTUAL_INVOICE_ITEM_ID, 
      WAREHOUSE_GOODS_OUT_IN_ID, WAREHOUSE_GOODS_OUT_IN_ITEM_ID, 
      SKU_ID, SKU, NUM, OUT_IN_TYPE
      )
    values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{updaterName,jdbcType=VARCHAR}, 
      #{virtualInvoiceId,jdbcType=INTEGER}, #{virtualInvoiceItemId,jdbcType=INTEGER}, 
      #{warehouseGoodsOutInId,jdbcType=INTEGER}, #{warehouseGoodsOutInItemId,jdbcType=INTEGER}, 
      #{skuId,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{num,jdbcType=DECIMAL}, #{outInType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="VIRTUAL_INVOICE_WAREHOUSE_ID" keyProperty="virtualInvoiceWarehouseId" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="virtualInvoiceId != null">
        VIRTUAL_INVOICE_ID,
      </if>
      <if test="virtualInvoiceItemId != null">
        VIRTUAL_INVOICE_ITEM_ID,
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID,
      </if>
      <if test="warehouseGoodsOutInItemId != null">
        WAREHOUSE_GOODS_OUT_IN_ITEM_ID,
      </if>
      <if test="skuId != null">
        SKU_ID,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="num != null">
        NUM,
      </if>
      <if test="outInType != null">
        OUT_IN_TYPE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="virtualInvoiceId != null">
        #{virtualInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="virtualInvoiceItemId != null">
        #{virtualInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInItemId != null">
        #{warehouseGoodsOutInItemId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=DECIMAL},
      </if>
      <if test="outInType != null">
        #{outInType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto">
    <!--@mbg.generated-->
    update T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="virtualInvoiceId != null">
        VIRTUAL_INVOICE_ID = #{virtualInvoiceId,jdbcType=INTEGER},
      </if>
      <if test="virtualInvoiceItemId != null">
        VIRTUAL_INVOICE_ITEM_ID = #{virtualInvoiceItemId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInId != null">
        WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      </if>
      <if test="warehouseGoodsOutInItemId != null">
        WAREHOUSE_GOODS_OUT_IN_ITEM_ID = #{warehouseGoodsOutInItemId,jdbcType=INTEGER},
      </if>
      <if test="skuId != null">
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        NUM = #{num,jdbcType=DECIMAL},
      </if>
      <if test="outInType != null">
        OUT_IN_TYPE = #{outInType,jdbcType=INTEGER},
      </if>
    </set>
    where VIRTUAL_INVOICE_WAREHOUSE_ID = #{virtualInvoiceWarehouseId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.kingdee.batch.dto.BatchRVirtualInvoiceJWarehouseDto">
    <!--@mbg.generated-->
    update T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    set ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      VIRTUAL_INVOICE_ID = #{virtualInvoiceId,jdbcType=INTEGER},
      VIRTUAL_INVOICE_ITEM_ID = #{virtualInvoiceItemId,jdbcType=INTEGER},
      WAREHOUSE_GOODS_OUT_IN_ID = #{warehouseGoodsOutInId,jdbcType=INTEGER},
      WAREHOUSE_GOODS_OUT_IN_ITEM_ID = #{warehouseGoodsOutInItemId,jdbcType=INTEGER},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      NUM = #{num,jdbcType=DECIMAL},
      OUT_IN_TYPE = #{outInType,jdbcType=INTEGER}
    where VIRTUAL_INVOICE_WAREHOUSE_ID = #{virtualInvoiceWarehouseId,jdbcType=INTEGER}
  </update>

<!--auto generated by MybatisCodeHelper on 2023-12-01-->
  <select id="selectByWarehouseGoodsOutInItemIds" resultMap="BaseResultMapAndOutInNo">
    select
    VIRTUAL_INVOICE_WAREHOUSE_ID,  VIRTUAL_INVOICE_ID, VIRTUAL_INVOICE_ITEM_ID, WAREHOUSE_GOODS_OUT_IN_ID,
    WAREHOUSE_GOODS_OUT_IN_ITEM_ID, SKU_ID, SKU, TRVIJW.NUM, OUT_IN_TYPE,TWGOII.OUT_IN_NO
    from T_R_VIRTUAL_INVOICE_J_WAREHOUSE TRVIJW  left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    on TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = TRVIJW.VIRTUAL_INVOICE_ITEM_ID
    where WAREHOUSE_GOODS_OUT_IN_ITEM_ID in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item,jdbcType=INTEGER}
    </foreach>
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-02-->
  <select id="selectByVirtualInvoiceId" resultMap="BaseResultMapAndOutInNo">
    select
    VIRTUAL_INVOICE_WAREHOUSE_ID,  VIRTUAL_INVOICE_ID, VIRTUAL_INVOICE_ITEM_ID, WAREHOUSE_GOODS_OUT_IN_ID,
    WAREHOUSE_GOODS_OUT_IN_ITEM_ID, SKU_ID, SKU, TRVIJW.NUM, OUT_IN_TYPE,TWGOII.OUT_IN_NO
    from T_R_VIRTUAL_INVOICE_J_WAREHOUSE TRVIJW  left join T_WAREHOUSE_GOODS_OUT_IN_ITEM TWGOII
    on TWGOII.WAREHOUSE_GOODS_OUT_IN_DETAIL_ID = TRVIJW.WAREHOUSE_GOODS_OUT_IN_ITEM_ID
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-02-->
  <select id="findByWarehouseGoodsOutInItemId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    where WAREHOUSE_GOODS_OUT_IN_ITEM_ID=#{warehouseGoodsOutInItemId,jdbcType=INTEGER}
    order by VIRTUAL_INVOICE_ID desc,VIRTUAL_INVOICE_ITEM_ID desc
  </select>

<!--auto generated by MybatisCodeHelper on 2023-12-07-->
  <select id="findByVirtualInvoiceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    where VIRTUAL_INVOICE_ID=#{virtualInvoiceId,jdbcType=INTEGER}
  </select>

  <delete id="deleteByVirtualInvoiceWarehouseIds">
    delete from T_R_VIRTUAL_INVOICE_J_WAREHOUSE
    where VIRTUAL_INVOICE_WAREHOUSE_ID in
    <foreach collection="list" open="(" close=")" separator="," item="item">
      #{item,jdbcType=INTEGER}
    </foreach>
  </delete>
</mapper>