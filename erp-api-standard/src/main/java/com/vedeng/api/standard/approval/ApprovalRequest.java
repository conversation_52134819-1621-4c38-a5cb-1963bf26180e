package com.vedeng.api.standard.approval;

/**
 * 审核请求接口
 * 所有需要审核的请求类都应该实现此接口
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-26
 */
public interface ApprovalRequest {
    
    /**
     * 获取任务ID
     * 
     * @return 任务ID
     */
    String getTaskId();
    
    /**
     * 设置任务ID
     * 
     * @param taskId 任务ID
     */
    void setTaskId(String taskId);
    
    /**
     * 获取审核备注
     * 
     * @return 审核备注
     */
    String getComment();
    
    /**
     * 设置审核备注
     * 
     * @param comment 审核备注
     */
    void setComment(String comment);
}
