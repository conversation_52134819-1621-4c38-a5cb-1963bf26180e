package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.util.XmlExercise;
import com.vedeng.trader.dao.LandLineRecordMapper;
import com.vedeng.trader.enums.LandLineCodeEnum;
import com.vedeng.trader.model.LandLineCallRecord;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.axis2.AxisFault;
import org.apache.axis2.addressing.EndpointReference;
import org.apache.axis2.client.Options;
import org.apache.axis2.rpc.client.RPCServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.xml.namespace.QName;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 同步座机呼出记录
 *
 * <AUTHOR>
 */
@JobHandler("syncLandLineInfoTask")
@Component
public class SyncLandLineInfoTask extends AbstractJobHandler {
    private final static Logger logger = LoggerFactory.getLogger(SyncLandLineInfoTask.class);

    @Value("${call_namespace}")
    private String callNamespace;

    @Value("${call_url}")
    private String callUrl;


    @Resource
    private LandLineRecordMapper landLineRecordMapper;

    @Resource
    private UserMapper userMapper;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        if (StringUtil.isNotBlank(s)) {
            logger.info("手动执行座机呼出记录同步 s:{}", s);
        }
        List<LandLineCallRecord> unHandleRecordList = StringUtil.isBlank(s) ?
                landLineRecordMapper.getUnHandleRecordListToday() :
                landLineRecordMapper.getLandLineCallRecordByIds(JSON.parseArray(s, Long.class))
                        .stream().filter(item -> StringUtil.isNotBlank(item.getCoid()))
                        .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(unHandleRecordList)) {
            return SUCCESS;
        }

        logger.info("unHandleRecordListCount:{}", unHandleRecordList.size());

        //获取coid  对应通话信息
        HashMap<String, LandLineCallRecord> stringLandLineCallRecordHashMap = getLandLineCallRecordHashMap(unHandleRecordList);
        if (MapUtils.isEmpty(stringLandLineCallRecordHashMap)) {
            logger.warn("暂未获取对应通话信息 unHandleRecordList:{}", JSON.toJSONString(unHandleRecordList));
            return SUCCESS;
        }

        //线路代码信息
        HashMap<String, Integer> lineCodeNumberMap = getLineCodeNumberMap();

        //同步座机通话信息
        syncLandLineRecordInfo(unHandleRecordList, stringLandLineCallRecordHashMap, lineCodeNumberMap);
        return ReturnT.SUCCESS;
    }

    /**
     * 同步座机通话信息
     *
     * @param unHandleRecordList
     * @param stringLandLineCallRecordHashMap
     * @param lineCodeNumberMap
     */
    private void syncLandLineRecordInfo(List<LandLineCallRecord> unHandleRecordList, HashMap<String,
            LandLineCallRecord> stringLandLineCallRecordHashMap, HashMap<String, Integer> lineCodeNumberMap) {
        unHandleRecordList.forEach(item -> {
            if (!stringLandLineCallRecordHashMap.containsKey(item.getCoid())) {
                return;
            }

            LandLineCallRecord dataRecord = stringLandLineCallRecordHashMap.get(item.getCoid());

            LandLineCallRecord landLineCallRecordForUpdate = new LandLineCallRecord();
            landLineCallRecordForUpdate.setLandlineCallRecordId(item.getLandlineCallRecordId());
            landLineCallRecordForUpdate.setCoidLength(dataRecord.getCoidLength());
            if (StringUtil.isNotBlank(dataRecord.getCallingNumber())) {
                landLineCallRecordForUpdate.setCallingNumber(dataRecord.getCallingNumber());
                landLineCallRecordForUpdate.setLineCode(lineCodeNumberMap.getOrDefault(dataRecord.getCallingNumber(), null));
            }
            landLineCallRecordForUpdate.setIsConnect(dataRecord.getCoidLength() > 0 ? ErpConst.TWO : ErpConst.ONE);
            landLineCallRecordForUpdate.setUpdater(ErpConst.NJ_ADMIN_ID);
            landLineCallRecordForUpdate.setModTime(System.currentTimeMillis());
            landLineRecordMapper.syncLandLineRecordInfo(landLineCallRecordForUpdate);
        });
    }

    /**
     * 获取coid  对应通话信息
     *
     * @param unHandleRecordList
     * @return
     * @throws AxisFault
     */
    private HashMap<String, LandLineCallRecord> getLandLineCallRecordHashMap(List<LandLineCallRecord> unHandleRecordList) throws AxisFault {
        // 需要传递的参数封装成xml格式
        Map paramMap = new HashMap<String, Object>(16);
        paramMap.put("coid", unHandleRecordList.stream().map(LandLineCallRecord::getCoid).collect(Collectors.toList()));


        String xmlStr = XmlExercise.mapToXml(paramMap, "data");

        // 调用接口
        RPCServiceClient ser = new RPCServiceClient();
        Options options = ser.getOptions();
        EndpointReference targetEPR = new EndpointReference(callNamespace);
        options.setTo(targetEPR);
        options.setAction("getRecordByCoid");

        Object[] opAddEntryArgs = new Object[]{xmlStr};
        Class[] classes = new Class[]{String.class};
        QName opAddEntry = new QName(callUrl, "getRecordByCoid");
        Object[] str = ser.invokeBlocking(opAddEntry, opAddEntryArgs, classes);

        // 接口返回xml字符串
        String xmlString = str[0].toString();

        Map result = XmlExercise.xmlToMapList(xmlString);

        if (MapUtils.isEmpty(result) || !ResultInfo.success().getCode().toString().equals(result.get("code"))) {
            logger.warn("获取电话录音信息异常 paramMap:{},result:{}", JSON.toJSONString(paramMap), JSON.toJSONString(result));
            return null;
        }

        List resultList = (List) result.get("data");

        if (CollectionUtils.isEmpty(resultList)) {
            logger.warn("获取电话录音信息返回结果为空 paramMap:{}", JSON.toJSONString(paramMap));
            return null;
        }

        HashMap<String, LandLineCallRecord> stringLandLineCallRecordHashMap = new HashMap<>(16);

        for (int index = 0; index < resultList.size(); index++) {
            Map dataMap = (Map) resultList.get(index);

            String resultId = dataMap.get("COID").toString();

            if (stringLandLineCallRecordHashMap.containsKey(resultId)) {
                int current = Integer.parseInt(dataMap.get("FILELEN").toString());
                LandLineCallRecord record = stringLandLineCallRecordHashMap.get(resultId);
                if (current > record.getCoidLength()) {
                    record.setCoidLength(current);
                }
            } else {
                int current = Integer.parseInt(dataMap.get("FILELEN").toString());

                LandLineCallRecord landLineCallRecordItem = new LandLineCallRecord();
                landLineCallRecordItem.setCoidLength(current);

                String callType = dataMap.get("CALLTYPE").toString();

                String callingNumber = StringUtil.isNotBlank(callType) && "0".equals(callType) ? dataMap.get("DNIS").toString() : dataMap.get("ANI").toString();
                if (StringUtil.isNotBlank(callingNumber)) {
                    landLineCallRecordItem.setCallingNumber(StringStartTrim(dealTelecomNum(callingNumber), "025"));
                }
                stringLandLineCallRecordHashMap.put(resultId, landLineCallRecordItem);
            }
        }
        return stringLandLineCallRecordHashMap;
    }

    /**
     * 电信出局码处理
     *
     * @param callingNumber
     * @return
     */
    private String dealTelecomNum(String callingNumber) {
        try {
            String pattern = "^\\s8625(\\d{8})$";
            Pattern r = Pattern.compile(pattern);
            Matcher m = r.matcher(callingNumber);
            if (m.matches()){
                callingNumber = m.group(1);
            }
        } catch (Exception e) {
            logger.error("dealTelecomNum callingNumber:{}", callingNumber, e);
            return callingNumber;
        }
        return callingNumber;
    }

    /**
     * 获取线路号码以及代码
     *
     * @return
     */
    private HashMap<String, Integer> getLineCodeNumberMap() {
        HashMap<String, Integer> lineCodeNumberMap = new HashMap<>(16);
        List<User> landLineInfo = userMapper.getLandLineInfo();
        landLineInfo.forEach(landline -> {
            //老客户线路
            if (StringUtil.isNotBlank(landline.getCustomerLine())) {
                lineCodeNumberMap.put(landline.getCustomerLine(), LandLineCodeEnum.CUSTOMER_LINE.getLineCode());
            }
            //联通线路;
            if (StringUtil.isNotBlank(landline.getUnicomLine())) {
                lineCodeNumberMap.put(landline.getUnicomLine(), LandLineCodeEnum.UNICOM_LINE.getLineCode());
            }
            //移动线路
            if (StringUtil.isNotBlank(landline.getMobileLine())) {
                lineCodeNumberMap.put(landline.getMobileLine(), LandLineCodeEnum.MOBILE_LINE.getLineCode());
            }
            //电信线路
            if (StringUtil.isNotBlank(landline.getTelecomLine())) {
                lineCodeNumberMap.put(landline.getTelecomLine(), LandLineCodeEnum.TELECOM_LINE.getLineCode());
            }
        });
        return lineCodeNumberMap;
    }

    private String StringStartTrim(String stream, String trim) {
        if (stream == null || stream.length() == 0 || trim == null || trim.length() == 0) {
            return stream;
        }
        int end;
        String regPattern = "[" + trim + "]*+";
        Pattern pattern = Pattern.compile(regPattern, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(stream);
        if (matcher.lookingAt()) {
            end = matcher.end();
            stream = stream.substring(end);
        }
        return stream;
    }
}
