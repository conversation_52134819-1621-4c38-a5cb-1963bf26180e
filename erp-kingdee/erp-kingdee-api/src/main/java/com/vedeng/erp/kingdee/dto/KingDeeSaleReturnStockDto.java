package com.vedeng.erp.kingdee.dto;

import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @description 销售退货入库单 https://www.yuque.com/manhuo/gf1570/vprm9l
 * @date 2023/1/6 11:14
 **/
@NoArgsConstructor
@Data
@WriteBackField(needBackField = {"FID","FEntity.FENTRYID", "FEntity.F_QZOK_BDDJHID"})
public class KingDeeSaleReturnStockDto extends KingDeeMqBaseDto {

    private Integer id;

    /**
     * 单据内码0：表示新增非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField
    @KingDeeID
    private String fid;
    /**
     * 单据类型填单据类型编码，默认填 "XSTHD01_SYS"
     */
    private String FBillTypeID;
    /**
     * 单据编号
     */
    @BusinessID
    private String FBillNo;
    /**
     * 贝登单据头ID
     */
    private String fQzokBddjtid;
    /**
     * 单据日期
     */
    private String FDate;
    /**
     * 退货销售组织
     */
    private String FSaleOrgId;
    /**
     * 退货库存组织
     */
    private String FStockOrgId;
    /**
     * 客户编码
     */
    private String FRetcustId;
    /**
     * fEntity
     */
    private List<KingDeeSaleReturnStockDetailDto> FEntity;

    @Override
    public String getFormId() {
        return KingDeeFormConstant.SAL_RETURN_STOCK;
    }

}
