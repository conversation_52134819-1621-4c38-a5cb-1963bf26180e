package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterBuyorderInvoiceEntity;
import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AfterBuyorderInvoiceMapper {
    int deleteByPrimaryKey(Integer afterBuyorderInvoiceId);

    int insert(AfterBuyorderInvoiceEntity record);

    int insertSelective(AfterBuyorderInvoiceEntity record);

    AfterBuyorderInvoiceEntity selectByPrimaryKey(Integer afterBuyorderInvoiceId);

    int updateByPrimaryKeySelective(AfterBuyorderInvoiceEntity record);

    int updateByPrimaryKey(AfterBuyorderInvoiceEntity record);
    /**
     * 根据售后单id查询仅退票蓝字暂存信息
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    AfterBuyorderInvoiceDto queryInfoByAfterSalesId(@Param("afterSalesId") Integer afterSalesId);
}