package com.vedeng.goods.model.dto;

import java.util.Date;

/**
 * 加字段需慎重
 */
public class CoreSkuBaseDTO {
	private Integer skuId;
	private Integer spuId;
	private String skuNo;
	private String skuName;

	private Integer onSale;
	private Integer pushStatus;

	private String pushStatusStr;

	private String brandName;

	/**
	 * 同步状态 0 未推送  1 已推送 2 需重推
	 */
	private Integer synchronizationStatus;

	// 是否可售
	private Integer isAvailableSale;

	/**
	 * 禁用状态
	 */
	private Integer status;



	/**
	 * 禁用原因
	 */
	private String disabledReason;

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}


	public String getDisabledReason() {
		return disabledReason;
	}

	public void setDisabledReason(String disabledReason) {
		this.disabledReason = disabledReason;
	}

	public Integer getIsAvailableSale() {
		return isAvailableSale;
	}

	public void setIsAvailableSale(Integer isAvailableSale) {
		this.isAvailableSale = isAvailableSale;
	}

	public Integer getSynchronizationStatus() {
		return synchronizationStatus;
	}

	public void setSynchronizationStatus(Integer synchronizationStatus) {
		this.synchronizationStatus = synchronizationStatus;
	}

	public String getBrandName() {
		return brandName;
	}

	public void setBrandName(String brandName) {
		this.brandName = brandName;
	}

	public Integer getOnSale() {
		return onSale;
	}

	public void setOnSale(Integer onSale) {
		this.onSale = onSale;
	}

	public Integer getPushStatus() {
		return pushStatus;
	}

	public String getPushStatusStr() {
		return pushStatusStr;
	}

	public void setPushStatusStr(String pushStatusStr) {
		this.pushStatusStr = pushStatusStr;
	}

	public void setPushStatus(Integer pushStatus) {
		this.pushStatus = pushStatus;
	}

	public String getShowName() {
		return showName;
	}

	public void setShowName(String showName) {
		this.showName = showName;
	}

	private String showName;
	private String skuBelong;
	private Integer hasBackupMachine;
	private String isStockup;

	private Integer checkStatus;// sku审核状态

	private Date modTime;

	private String wikiHref;

	private Integer operateInfoId;

	private String model;


	/**
	 * 存储条件（温度)
	 *
	 * @since ERP_SV_2020_61
	 * @see com.vedeng.goods.enums.GoodsStorageConditionTemperatureEnum
	 */
	private Integer storageConditionTemperature;

	/**
	 * 存储条件（温度,单位：摄氏度），其他温度范围值存储较小值
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionTemperatureLowerValue;

	/**
	 * 存储条件（温度，单位：摄氏度），其他温度范围值存储较大值
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionTemperatureUpperValue;

	/**
	 * 存储条件（湿度，单位：%）：范围值存储较小的值
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionHumidityLowerValue;

	/**
	 * 存储条件（湿度，单位：%）：范围值存储较大的
	 *
	 * @since ERP_SV_2020_61
	 */
	private Float storageConditionHumidityUpperValue;

	/**
	 * 存储条件（其他）
	 *
	 * @since ERP_SV_2020_61
	 * @see com.vedeng.goods.enums.GoodsStorageConditionOthersEnum
	 */
	private String storageConditionOthers;


	/**
	 * 商品级别
	 *
	 * @since ERP_LV_2020_105
	 */
	private Integer goodsLevelNo;

	/**
	 * 商品档位
	 *
	 * @since  ERP_LV_2020_105
	 */
	private Integer goodsPositionNo;


	public Integer getAssignmentManagerId() {
		return assignmentManagerId;
	}

	public void setAssignmentManagerId(Integer assignmentManagerId) {
		this.assignmentManagerId = assignmentManagerId;
	}

	private Integer assignmentManagerId;

	private Integer priceVerifyStatus;


	public Integer getAssignmentAssistantId() {
		return assignmentAssistantId;
	}

	public void setAssignmentAssistantId(Integer assignmentAssistantId) {
		this.assignmentAssistantId = assignmentAssistantId;
	}

	private Integer assignmentAssistantId;


	public String getModel() {
		return model;
	}

	public void setModel(String model) {
		this.model = model;
	}

	public String getSpec() {
		return spec;
	}

	public void setSpec(String spec) {
		this.spec = spec;
	}

	private String spec;

	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	public String getSkuNo() {
		return skuNo;
	}

	public void setSkuNo(String skuNo) {
		this.skuNo = skuNo;
	}

	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	public String getSkuBelong() {
		return skuBelong;
	}

	public void setSkuBelong(String skuBelong) {
		this.skuBelong = skuBelong;
	}

	public Integer getHasBackupMachine() {
		return hasBackupMachine;
	}

	public void setHasBackupMachine(Integer hasBackupMachine) {
		this.hasBackupMachine = hasBackupMachine;
	}

	public Date getModTime() {
		return modTime;
	}

	public void setModTime(Date modTime) {
		this.modTime = modTime;
	}

	public String getWikiHref() {
		return wikiHref;
	}

	public void setWikiHref(String wikiHref) {
		this.wikiHref = wikiHref;
	}

	public Integer getOperateInfoId() {
		return operateInfoId;
	}

	public void setOperateInfoId(Integer operateInfoId) {
		this.operateInfoId = operateInfoId;
	}

	public Integer getCheckStatus() {
		return checkStatus;
	}

	public void setCheckStatus(Integer checkStatus) {
		this.checkStatus = checkStatus;
	}

	public Integer getSpuId() {
		return spuId;
	}

	public void setSpuId(Integer spuId) {
		this.spuId = spuId;
	}

	public String getIsStockup() {
		return isStockup;
	}

	public void setIsStockup(String isStockup) {
		this.isStockup = isStockup;
	}

	public Integer getPriceVerifyStatus() {
		return priceVerifyStatus;
	}

	public void setPriceVerifyStatus(Integer priceVerifyStatus) {
		this.priceVerifyStatus = priceVerifyStatus;
	}

	public Integer getStorageConditionTemperature() {
		return storageConditionTemperature;
	}

	public void setStorageConditionTemperature(Integer storageConditionTemperature) {
		this.storageConditionTemperature = storageConditionTemperature;
	}

	public Float getStorageConditionTemperatureLowerValue() {
		return storageConditionTemperatureLowerValue;
	}

	public void setStorageConditionTemperatureLowerValue(Float storageConditionTemperatureLowerValue) {
		this.storageConditionTemperatureLowerValue = storageConditionTemperatureLowerValue;
	}

	public Float getStorageConditionTemperatureUpperValue() {
		return storageConditionTemperatureUpperValue;
	}

	public void setStorageConditionTemperatureUpperValue(Float storageConditionTemperatureUpperValue) {
		this.storageConditionTemperatureUpperValue = storageConditionTemperatureUpperValue;
	}

	public Float getStorageConditionHumidityLowerValue() {
		return storageConditionHumidityLowerValue;
	}

	public void setStorageConditionHumidityLowerValue(Float storageConditionHumidityLowerValue) {
		this.storageConditionHumidityLowerValue = storageConditionHumidityLowerValue;
	}

	public Float getStorageConditionHumidityUpperValue() {
		return storageConditionHumidityUpperValue;
	}

	public void setStorageConditionHumidityUpperValue(Float storageConditionHumidityUpperValue) {
		this.storageConditionHumidityUpperValue = storageConditionHumidityUpperValue;
	}

	public String getStorageConditionOthers() {
		return storageConditionOthers;
	}

	public void setStorageConditionOthers(String storageConditionOthers) {
		this.storageConditionOthers = storageConditionOthers;
	}

	public Integer getGoodsLevelNo() {
		return goodsLevelNo;
	}

	public void setGoodsLevelNo(Integer goodsLevelNo) {
		this.goodsLevelNo = goodsLevelNo;
	}

	public Integer getGoodsPositionNo() {
		return goodsPositionNo;
	}

	public void setGoodsPositionNo(Integer goodsPositionNo) {
		this.goodsPositionNo = goodsPositionNo;
	}
}
