package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BaseInfoBatchJob;
import com.vedeng.erp.kingdee.batch.job.BaseInfoCompensateBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@JobHandler(value = "BaseInfoBatchTask")
@Component
public class BaseInfoBatchTask extends AbstractJobHandler {

    @Autowired
    private BaseInfoBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private BaseInfoCompensateBatchJob baseInfoCompensateBatchJob;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("==================审计基础信息batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.baseInfoFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================审计基础信息batch结束====================");
        return SUCCESS;
    }


}

