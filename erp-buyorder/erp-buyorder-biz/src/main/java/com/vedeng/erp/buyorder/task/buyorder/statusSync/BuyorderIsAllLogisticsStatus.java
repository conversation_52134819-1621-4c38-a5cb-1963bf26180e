package com.vedeng.erp.buyorder.task.buyorder.statusSync;

import com.newtask.data.AbstractDataByTimeSync;
import com.newtask.data.dao.BuyorderDataMapper;
import com.newtask.data.dto.BuyorderDataDto;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description com.newtask.data.buyorder.statusSync
 * @Date 2021/10/20 14:30
 */
@Component
public class BuyorderIsAllLogisticsStatus extends AbstractDataByTimeSync {

    @Resource
    BuyorderDataMapper buyorderDataMapper;


    @Override
    public List<Map<String, Object>> loadBizByTimeData(Long startTime, Long endTime) {
        return buyorderDataMapper.getOrderIsAllLogisticsByTime(startTime, endTime);
    }


    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<BuyorderDataDto> buyorderDataDtos = new ArrayList<>();
            long nowTime = System.currentTimeMillis();
            dataList.forEach(item -> {
                BuyorderDataDto buyorderDataDto = new BuyorderDataDto();
                Integer buyorderId = Integer.valueOf(item.get("buyorderId").toString());
                makeExist(buyorderId, nowTime);
                buyorderDataDto.setBuyorderId(buyorderId);
                buyorderDataDto.setIsAllLogisticsStatus(Integer.valueOf(item.get("isAllLogisticsStatus").toString()));
                buyorderDataDtos.add(buyorderDataDto);
            });
            buyorderDataMapper.updateOrderIsAllLogisticsByTime(buyorderDataDtos, nowTime);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {
        int buyorderDataId = buyorderDataMapper.getBuyorderDataById(id);
        if (buyorderDataId <= 0) {
            buyorderDataMapper.insertBuyorderData(id, nowTime);
        }
    }
}
