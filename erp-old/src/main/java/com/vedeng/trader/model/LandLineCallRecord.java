package com.vedeng.trader.model;

import lombok.Data;

import java.io.Serializable;


/**
 * 座机通话记录
 *
 * <AUTHOR>
 */
@Data
public class LandLineCallRecord implements Serializable {
    /**
     * 记录主键ID
     */
    private Long landlineCallRecordId;

    /**
     * 沟通电话
     */
    private String phone;

    /**
     * 主叫号码
     */
    private String callingNumber;

    /**
     * 线路代码 1:联通 2:移动 3:电信
     */
    private Integer lineCode;

    /**
     * 呼叫中心COID
     */
    private String coid;

    /**
     * 通话类型 1呼入2呼出
     */
    private Integer coidType;

    /**
     * 录音时长
     */
    private Integer coidLength;

    /**
     * 是否接通 0:待确认 1:未接通  2:已接通
     */
    private Integer isConnect;

    /**
     * 是否有效  0:无效  1:有效
     */
    private Integer isEnable;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 最近一次编辑时间
     */
    private Long modTime;

    /**
     * 最近一次编辑人
     */
    private Integer updater;
}
