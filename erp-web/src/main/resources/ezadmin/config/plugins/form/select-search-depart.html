<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>搜索-多选下拉</title>
</head>
<body id="xmselectdepart"  alias="xm-select-depart" type="search">
<div    th:style="${custom_style}"  th:ITEM_NAME="${item_name}"  th:ITEM_ID="${item_name}"    >

    <div th:class="${#strings.isEmpty(itemParamValue)?'layui-input-inline':'layui-input-inline layui-border-blue' } "  >
        <div th:itemPlaceholder="${itemPlaceholder}" th:itemsJson="${itemsJson}" class="ez-xmselect-depart"  th:id="${'itemName-' + item_name}" th:name="${item_name}" th:value="${#strings.isEmpty(itemParamValue)?'[]':'['+itemParamValue+']'}" ></div>
    </div>
</div>
</body>
</html>
