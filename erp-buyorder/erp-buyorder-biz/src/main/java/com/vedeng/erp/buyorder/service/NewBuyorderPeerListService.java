package com.vedeng.erp.buyorder.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.buyorder.domain.entity.Buyorder;
import com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods;
import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail;
import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchInfo;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchDetailVo;
import com.vedeng.erp.buyorder.domain.vo.PurchaseDeliveryDirectBatchInfoVo;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryDirectAttachmentDto;
import com.vedeng.logistics.model.Express;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.wms.dto.PurchaseDeliveryDirectBatchDetailDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 同行单相关操作
 *
 * <AUTHOR>
 * @date 2022/4/13 9:37
 **/
public interface NewBuyorderPeerListService {

    /**
     * 获取当前快递下所有需要补充同行单的商品信息
     * @param expressId
     */
    List<PeerListBuyorderGoods> getNeedEditBuyGoods(Integer expressId);

    /**
     * 核实数据是否符合规则
     * @param data
     */
    Map<String,Object> checkDetails(List<PurchaseDeliveryDirectBatchDetailVo> data);

    /**
     * 核实数据是否符合规则
     * @param data
     */
    void checkDetailsExcel(List<PurchaseDeliveryDirectBatchDetailVo> data);

    /**
     * 保存直发采购单同行单
     * @param data 数据
     */
    void savePurchaseDelivery(PurchaseDeliveryDirectBatchInfoVo data);

    /**
     * 解析上传excel
     *
     * @param file 上传的文件对象
     * @return
     * @exception Exception
     */
    List<PeerListBuyorderGoods> analysisExcel(MultipartFile file,Integer expressId) throws Exception;



    /**
     * 需要当前的采购单id 以及 当前采购单下指定的物流id
     * @param express
     * @return Buyorder excel 导出信息
     */
    Buyorder getExcelOutData(Express express);

    /**
     * 获取同行单下发wms 数据 采购入库，销售出库
     * @param buyorderId 采购单号 不可为null
     * @param isExWarehouse false 计算入库信息，true 计算出库信息
     * @return
     */
    List<PurchaseDeliveryDirectBatchDetailDto> getExOrInPurchaseDeliveryDirectBatchDetails(Integer buyorderId, boolean  isExWarehouse);

    /**
     * <AUTHOR>
     * @desc 保存直发出库单信息
     * @param purchaseDeliveryDirectAttachmentDto
     * @return
     */
    ResultInfo savePurchaseDeliveryDirectAttachment(PurchaseDeliveryDirectAttachmentDto purchaseDeliveryDirectAttachmentDto, User user);

    /**
     * 获取采购单关联的销售单
     * @param buyorderId
     * @return
     */
    List<Integer> getSaleorderIdListByBuyorderId(Integer buyorderId);
    /**
     * 获取采购单关联的销售单
     * @param saleorderIds
     * @return
     */
    List<Integer> getBuyorderIdListBySaleorderIds(List<Integer> saleorderIds);
    
    /**
     * 根据采购单ID查询同行单信息
     * @param buyorderId 采购单ID
     * @return 同行单信息列表
     */
    PurchaseDeliveryDirectBatchInfoVo queryPeerList(Integer buyorderId);
    
    /**
     * 根据采购单ID查询出入库记录并组装返回同行单创建入参
     * @param buyorderId 采购单ID
     * @return 同行单创建入参
     */
    PurchaseDeliveryDirectBatchInfoVo queryStockRecords(Integer buyorderId);
    PurchaseDeliveryDirectBatchInfoVo queryPeerRecords(Integer buyorderId);
}
