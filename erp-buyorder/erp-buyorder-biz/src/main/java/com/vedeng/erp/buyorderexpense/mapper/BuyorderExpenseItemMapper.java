package com.vedeng.erp.buyorderexpense.mapper;

import com.vedeng.erp.aftersale.dto.ExpenseItemAndInvoiceDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseItemEntity;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @description ${end}
 * @date 2022/10/17 17:43
 **/
@Repository
public interface BuyorderExpenseItemMapper {
    /**
     * delete by primary key
     *
     * @param buyorderExpenseItemId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer buyorderExpenseItemId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(BuyorderExpenseItemEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(BuyorderExpenseItemEntity record);

    /**
     * select by primary key
     *
     * @param buyorderExpenseItemId primary key
     * @return object by primary key
     */
    BuyorderExpenseItemEntity selectByPrimaryKey(Integer buyorderExpenseItemId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(BuyorderExpenseItemEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(BuyorderExpenseItemEntity record);

    int batchInsert(List<BuyorderExpenseItemEntity> list);

    /**
     * 根据采购费用单id查询 商品+产品经理
     *
     * @param buyorderExpenseId
     * @param isDelete
     * @return
     */
    List<BuyorderExpenseItemDto> selectWithAssignmentManagerByBuyorderExpenseIdAndIsDelete(@Param("buyorderExpenseId") Integer buyorderExpenseId, @Param("isDelete") Integer isDelete);

    /**
     * 根据采购单id查询 直属采购费用单商品详情
     *
     * @param buyOrderId 采购单id
     * @return List<BuyorderExpenseItemDto>
     */
    List<BuyorderExpenseItemDto> getByBuyOrderId(@Param("buyOrderId") Integer buyOrderId);

    /**
     * 根据采购费用单id查询 商品
     *
     * @param buyorderExpenseId 费用单id
     * @param isDelete          是否删除
     * @return List<BuyorderExpenseItemDto>
     */
    List<BuyorderExpenseItemDto> selectByBuyorderExpenseIdAndIsDelete(@Param("buyorderExpenseId") Integer buyorderExpenseId, @Param("isDelete") Integer isDelete);

    /**
     * 查询费用单可退票的商品发票信息集合
     *
     * @param buyorderExpenseId 费用单id
     * @return List<ExpenseItemAndInvoiceDto>
     */
    List<ExpenseItemAndInvoiceDto> getExpenseItemAndInvoiceList(@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 全部发货
     *
     * @param buyorderExpenseId 采购费用单id
     * @return 影响行数
     */
    int deliveryAllByBuyorderExpenseId(@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 全部收货
     *
     * @param buyorderExpenseId 采购费用单id
     * @return 影响行数
     */
    int arrivalAllByBuyorderExpenseId(@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 查询费用单的所有item（纯净版，不含任何连表的信息）
     *
     * @param buyorderExpenseId 采购费用单id
     * @return List<BuyorderExpenseItemEntity>
     */
    List<BuyorderExpenseItemEntity> getByBuyorderExpenseId(@Param("buyorderExpenseId") Integer buyorderExpenseId);

    /**
     * 批量更新
     *
     * @param list List<BuyorderExpenseItemEntity>
     * @return 影响行数
     */
    int batchUpdate(List<BuyorderExpenseItemEntity> list);

    /**
     * 查询直属费用单的商品修改信息
     *
     * @param buyorderModifyApplyId 采购单申请修改id
     * @return List<BuyorderExpenseItemDto>
     */
    List<BuyorderExpenseItemDto> getExpenseItemModifyInfo(Integer buyorderModifyApplyId);

    /**
     * 根据费用单id和skuNo查询费用单商品
     *
     * @param buyorderExpenseId 费用单id
     * @param skuNo             skuNo
     * @return List<BuyorderExpenseItemDto>
     */
    List<BuyorderExpenseItemDto> getByExpenseIdAndSkuNo(@Param("buyorderExpenseId") Integer buyorderExpenseId, @Param("skuNo") String skuNo);
}