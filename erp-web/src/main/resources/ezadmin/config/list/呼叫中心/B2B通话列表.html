<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>B2B通话列表</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="b2bcalllist" datasource="erp-reportdatasource" fix_number="1" append_column_url="1" append_row_url="1" empty_show="-" fixednumber="2" firstcol="numbers">
  <div id="appendHead">
  </div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab">
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline ">
     <label class="layui-form-label">被叫号码</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="PHONE" placeholder="" style="" alias="a" jdbctype="">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">录音ID(数字)</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="COMMUNICATE_RECORD_ID" placeholder="" style="" alias="a" jdbctype=""> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">公司名称</label>
     <div class="layui-input-inline">
      <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="">
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">话务人员</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="CREATOR" placeholder="" style="" alias="a" jdbctype="" data="select user_id K,username V from T_USER ORDER BY USERNAME" datatype="KVSQLCACHE" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">通话方式</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="COID_TYPE" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;呼入&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;呼出&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">通话结果</label>
     <div class="layui-input-inline"><select class=" layui-input list-search-item " type="search" name="COID_LENGTH" placeholder="" style="" alias="IF(IFNULL(a.COID_LENGTH,0)>0,1,0)" jdbctype="" data="[{&quot;V&quot;:&quot;接通&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;未接通&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper=""></select>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">通话时间</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="from_unixtime(a.ADD_TIME/1000 ,'%Y-%m-%d %h:%i:%s')" jdbctype="" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">通话时长</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="numberrange" name="COID_LENGTH" placeholder="" style="" alias="a" jdbctype="NUMBER" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">部门</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="orgb2b" name="ORG_ID" placeholder="" style="" alias="PP" jdbctype="" data="" datatype="" oper="IN"></object>
     </div>
    </div>
   </form>
   <hr class="layui-border-blue">
   <div class="btn-group   bd-highlight" id="tableButton">
   </div>
   <table id="table" class="layui-table" style=" width:100%">
    <thead>
     <tr id="column">
      <th item_name="COMMUNICATE_RECORD_ID" name="COMMUNICATE_RECORD_ID" body="td-text" order="" url="" opentype="" windowname="" datatype="" data style head="th">录音ID</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data style head="th">添加时间</th>
      <th item_name="COID_TYPE" name="COID_TYPE" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;呼入&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;呼出&quot;,&quot;K&quot;:&quot;2&quot;}]" style head="th">通话方式</th>
      <th item_name="tt_number" name="tt_number" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">主叫号码</th>
      <th item_name="PHONE" name="PHONE" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">被叫号码</th>
      <th item_name="PC" name="PC" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">号码归属地</th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">公司名称</th>
      <th item_name="USER_ID" name="USER_ID" body="td-select" order="" url="" opentype="" windowname="" datatype="KVSQLCACHE" data="SELECT  USER_ID K,USERNAME V FROM T_USER  " style head="th">所属人员</th>
      <th item_name="COID_LENGTH" name="COID_LENGTH" body="td-select" order="" url="" opentype="" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;<SPAN STYLE='COLOR:#5db75d'>接通</SPAN>&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;<span style='color:red'>未接通</span>&quot;,&quot;K&quot;:&quot;0&quot;}]" style head="th">通话结果</th>
      <th item_name="COID_URI" name="COID_URI" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style head="th">录音</th>
      <th item_name="COID_LENGTH_SHOW" name="COID_LENGTH_SHOW" body="td-text" order="" url="" opentype="" windowname="" style head="th" jdbctype="NUMBER">通话时长（秒）</th>
      <th item_name="CREATOR" name="CREATOR" body="td-select" order="" url="" opentype="" windowname="" datatype="KVSQLCACHE" data="select USER_ID K,USERNAME V FROM T_USER" style head="th">话务人员</th>
      <th item_name="ORG_NAME" name="ORG_NAME" body="td-text" order="" url="" opentype="" windowname="" style head="th">部门</th>
      <th item_name="CONTENT_SUFFIX" name="CONTENT_SUFFIX" body="td-text" order="" url="" opentype="" windowname="" datatype="" data="" style="max-width:300px" head="th">沟通内容</th>
     </tr>
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY COMMUNICATE_RECORD_ID DESC" groupby="">StringBuilder sql=new StringBuilder("
       select
			a.COMMUNICATE_RECORD_ID,
			from_unixtime(a.ADD_TIME/1000 ,'%Y-%m-%d %H:%i:%s') ADD_TIME,
			a.COID_TYPE,
			a.PHONE,
			a.TRADER_ID,C.TRADER_NAME, a.TRADER_TYPE,a.CREATOR,
			IF(IFNULL(a.COID_LENGTH,0) &gt; 0,1,0) COID_LENGTH,a.COID_LENGTH COID_LENGTH_SHOW
            ,COID_URI,a.COID,
			 a.tt_number,R.USER_ID
            ,CONCAT(Q.PROVINCE,Q.CITY) PC,PP.ORG_ID,a.CONTENT_SUFFIX,O.ORG_NAME
		from
			T_COMMUNICATE_RECORD a
		    left join  T_TRADER  C ON a.TRADER_ID=C.TRADER_ID
		    left join  T_R_TRADER_J_USER R ON R.TRADER_ID=C.TRADER_ID AND R.TRADER_TYPE=1
			left join T_R_USER_POSIT P ON P.USER_ID= a.CREATOR
			LEFT JOIN T_POSITION PP ON PP.POSITION_ID=P.POSITION_ID
			    left join T_ORGANIZATION O ON O.ORG_ID=PP.ORG_ID
		    left join T_QCELLCORE Q ON substring(a.PHONE,1,7)=Q.PHONE
		where a.ADD_TIME &gt; 1609430400000
			AND a.COID is not null and a.COID !=''     ");

       sql.append ( " and (1=2  " );

       if ( isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
           sql.append ( " or  PP.ORG_ID  in (" );
           sql.append ( $$("EZ_SESSION_ORG_IDS_LIST_KEY") );
           sql.append (  ") " );
       }

       if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
           sql.append ( " or a.CREATOR in (" );
           sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
           sql.append (  ") " );
       }

       if ( !isNotBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; !isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
          sql.append ( " or 1=1" );
       }
       sql.append (  ") " );

       list=search(sql );
       return list;</pre> count: <pre id="count" class="layui-code">select 10000</pre></td>
     </tr> 
    </tbody> 
   </table> 
  </div>
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
  <div id="appendFoot">
   <!--<script type="text/javascript" src="http://erp.ivedeng.com:80/static/js/call/call.js?rnd=0.6096541162441355"></script>-->
   <script>
    $(function(){
     $("td[item_name=COID_URI]").each(function(){
      if($(this).parent("tr").find("input[name=row_data_hidden_COID_LENGTH]").val()==1){
       $(this).html('<button type="button" class="layui-btn layui-btn-normal layui-btn-sm playButton" url="'+$(this).text()+'"  >播放</button>')
      }else{
       $(this).html('-')
      }
     })

     $("body").on("click",".playButton",function(){
      debugger;
      var url=$(this).attr("url");
      url=iGetInnerText(url);
      layer.open({
       type: 1 //Page 层类型
       ,area: ['350px', '58px']
       ,title: ''
       ,shade: 0.6
       ,content: '<audio controls="controls" height="45" width="300" ><source src="'+url+'" type="audio/mp3" /><source src="'+url+'" type="audio/ogg" /><embed height="45" width="100" src="'+url+'" /></audio>'
      });
     })
     function iGetInnerText(testStr) {
      var resultStr = testStr.replace(/\ +/g, ""); //去掉空格
      resultStr = resultStr.replace(/[ ]/g, "");    //去掉空格
      resultStr = resultStr.replace(/[\r\n]/g, ""); //去掉回车换行
      return resultStr;

     }
    })
   </script>
  </div> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>