<%@ tag language="java" pageEncoding="UTF-8"%>
<%@attribute name="confirmationList" type="java.util.List" required="true" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%
    String path = request.getContextPath();
    String basePath = request.getScheme() + "://" + request.getServerName() + ":" + request.getServerPort()
            + path + "/";
%>

<style>
    .customer-color.J-batch-info {
        color: #0C0C0C;
    }
    .customer-color.active {
        color: #09f;
    }
</style>

<div>
    <div class="title-container" style="background-color: #00BFFF">
        <div class="table-title nobor">
            确认单审核汇总
        </div>
    </div>
    <table class="table">
        <thead>
        <tr>
            <th style="font-weight: bold">确认单</th>
            <th style="font-weight: bold">批次</th>
            <th style="font-weight: bold">审核状态</th>
            <th style="font-weight: bold">备注</th>
            <th style="font-weight: bold">操作时间</th>
        </tr>
        </thead>
        <tbody>
        <c:forEach var="list" items="${confirmationList}">
            <tr>
                <td>
                    ${list.confirmationName}
                </td>
                <td>
                    ${list.batchTime}
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.auditStatus eq 0}">
                            驳回
                        </c:when>
                        <c:when test="${list.auditStatus eq 1}">
                            审核通过
                        </c:when>
                        <c:otherwise>
                            审核中
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    <c:choose>
                        <c:when test="${list.auditStatus eq 0}">
                            ${list.comments}
                        </c:when>
                        <c:otherwise>
                            -
                        </c:otherwise>
                    </c:choose>
                </td>
                <td>
                    ${list.modTimeLabel}
                </td>
            </tr>
        </c:forEach>
        <c:if test="${empty confirmationList}">
            <tr>
                <td colspan="5">暂无确认单审核信息。</td>
            </tr>
        </c:if>
        </tbody>
    </table>
    <c:if test="${not empty confirmationList}">
    <div class="customer">
        <ul>
            <li>批次明细：</li>
            <c:forEach var="list" varStatus="idx" items="${confirmationList}">
                <li>
                    <a class="customer-color J-batch-info" data-id="${list.batchId}">
                        ${list.batchTime}
                    </a>
                </li>
            </c:forEach>
        </ul>
    </div>
    <table class="table">
        <thead>
        <tr>
            <th>操作人</th>
            <th>操作时间</th>
            <th>操作事项</th>
            <th>备注</th>
        </tr>
        </thead>
        <tbody id="audit_tbody2">

        </tbody>
    </table>
    </c:if>
</div>

<script>
    function getAuditRecordByBatchNo(batchId) {
        $.ajax({
            type: 'GET',
            url: '/orderstream/saleorder/confirmation_audit_record.do?batchId=' + batchId,
            dataType: 'JSON',
            success: function (res){
                const data = res.data;
                const tbody = document.getElementById("audit_tbody2");
                let tdHtml = "";
                if (res.code === 0 && data.length > 0){
                    for (let i = 0; i < data.length; i++) {
                        tdHtml += '<tr>';
                        tdHtml += '<td>' + parseNull(data[i].operatorName) + '</td>';
                        tdHtml += '<td>' + parseNull(data[i].operateTime) + '</td>';
                        tdHtml += '<td>' + parseNull(data[i].operateInstance) + '</td>';
                        tdHtml += '<td>' + parseNull(data[i].comment) + '</td>';
                        tdHtml += '</tr>';
                    }
                } else {
                    tdHtml += '<tr><td colspan="4">暂无记录。</td></tr>';
                }
                tbody.innerHTML = tdHtml;
            },
            error: function (){
                const tbody = document.getElementById("audit_tbody2");
                tbody.innerHTML = '<tr><td colspan="4">暂无记录。</td></tr>';
            }

        })


    }

    function parseNull(data){
        if (typeof data === 'undefined' || data == null){
            return "";
        }
        return data;
    }

    $(function () {
        $('.J-batch-info').click(function () {
            $('.J-batch-info').removeClass('active');
            $(this).addClass('active');
            getAuditRecordByBatchNo($(this).data('id'));
        })
        if ($('.J-batch-info').length){
            $('.J-batch-info').eq(0).click();
        }
    })
</script>