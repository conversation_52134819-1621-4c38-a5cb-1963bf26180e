package com.vedeng.erp.trader.service;

import com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity;
import com.vedeng.erp.trader.domain.vo.AiCommunicateRecordSummary;
import com.vedeng.erp.trader.domain.vo.AiCommunicateRecordVo;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface VoiceFieldResultService {

    int insertSelective(VoiceFieldResultEntity record);

    int deleteByPrimaryKey(Integer fieldResultId);

    int insert(VoiceFieldResultEntity record);

    VoiceFieldResultEntity selectByPrimaryKey(Integer fieldResultId);

    int updateByPrimaryKeySelective(VoiceFieldResultEntity record);

    int updateByPrimaryKey(VoiceFieldResultEntity record);

    List<VoiceFieldResultEntity> selectByCommunicateRecordIdAndSence(Integer communicateRecordId, String senceCode);

    List<VoiceFieldResultEntity> selectByCommunicateRecordIdAndSenceGroup(Integer communicateRecordId,String senceCode,String groupCode);

    AiCommunicateRecordVo selectByCommunicateRecordId(Integer communicateRecordId);

    AiCommunicateRecordSummary summary(Date date,Integer userId);
}

