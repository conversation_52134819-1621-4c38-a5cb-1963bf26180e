<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSalesLongDistanceFeeMapper">

    <insert id="insertLongDistanceFee"
            parameterType="com.vedeng.aftersales.model.entities.AfterSaleServiceStandardLongdistanceFeePo">
        INSERT INTO
        `T_AFTER_SALE_SERVICE_STANDARD_LONGDISTANCE_FEE`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="longdistanceFeeId != null">
                LONGDISTANCE_FEE_ID,
            </if>
            <if test="longdistanceFeeType != null">
                LONGDISTANCE_FEE_TYPE,
            </if>
            <if test="province != null">
                PROVINCE,
            </if>
            <if test="city != null">
                CITY,
            </if>
            <if test="region != null">
                REGION,
            </if>
            <if test="distanceFee != null">
                DISTANCE_FEE,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="longdistanceFeeId != null">
                #{longdistanceFeeId,jdbcType=INTEGER},
            </if>
            <if test="longdistanceFeeType != null">
                #{longdistanceFeeType,jdbcType=INTEGER},
            </if>
            <if test="province != null">
                #{province,jdbcType=INTEGER},
            </if>
            <if test="city != null">
                #{city,jdbcType=INTEGER},
            </if>
            <if test="region != null">
                #{region,jdbcType=INTEGER},
            </if>
            <if test="distanceFee != null">
                #{distanceFee,jdbcType=DECIMAL},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>

    <update id="disableAllLongDistanceFee">
        UPDATE `T_AFTER_SALE_SERVICE_STANDARD_LONGDISTANCE_FEE`
        SET IS_DELETE = 1,
            UPDATOR = #{userId,jdbcType=INTEGER},
            MOD_TIME = (SELECT unix_timestamp( now( ) ) * 1000)
        WHERE
            IS_DELETE != 1
    </update>

    <select id="getLongdisranceFeeVoListPage"
            resultType="com.vedeng.aftersales.model.vo.AfterSaleServiceStandardLongdistanceFeeVo">
        SELECT
            T1.*,
            T2.REGION_NAME AS PROVINCE_STR,
            T3.REGION_NAME AS CITY_STR,
            T4.REGION_NAME AS REGION_STR
        FROM
            T_AFTER_SALE_SERVICE_STANDARD_LONGDISTANCE_FEE T1
            LEFT JOIN T_REGION T2 ON T1.PROVINCE = T2.REGION_ID
            LEFT JOIN T_REGION T3 ON T1.CITY = T3.REGION_ID
            LEFT JOIN T_REGION T4 ON T1.REGION = T4.REGION_ID
        WHERE
            T1.IS_DELETE = 0
            <if test="longDistanceFreeSearchDto != null">
                <if test="longDistanceFreeSearchDto.provinceId != null and longDistanceFreeSearchDto.provinceId != 0">
                    AND T1.PROVINCE = #{longDistanceFreeSearchDto.provinceId,jdbcType=INTEGER}
                </if>
                <if test="longDistanceFreeSearchDto.cityId != null and longDistanceFreeSearchDto.cityId != 0">
                    AND T1.CITY = #{longDistanceFreeSearchDto.cityId,jdbcType=INTEGER}
                </if>
                <if test="longDistanceFreeSearchDto.regionIdsList != null">
                    AND T1.REGION IN
                    <foreach collection="longDistanceFreeSearchDto.regionIdsList" item="regionId" open="(" close=")" separator=",">
                        #{regionId, jdbcType=INTEGER}
                    </foreach>
                </if>
            </if>
    </select>

    <select id="selectCarriageByProvince" resultType="string">
        SELECT CONCAT('(长途费:',FLOOR(MIN(DISTANCE_FEE)),'~',FLOOR(MAX(DISTANCE_FEE)),')')AS carriage FROM T_AFTER_SALE_SERVICE_STANDARD_LONGDISTANCE_FEE A
        LEFT JOIN T_REGION B ON A.PROVINCE = B.REGION_ID
        LEFT JOIN T_REGION C ON A.CITY = C.REGION_ID
        LEFT JOIN T_REGION D ON A.REGION = D.REGION_ID
		WHERE B.REGION_ID = #{provinceId,jdbcType=INTEGER} AND A.IS_DELETE=0 AND A.LONGDISTANCE_FEE_TYPE=2
    </select>

    <select id="selectCarriageByCity" resultType="string">
        SELECT CONCAT('(长途费:',FLOOR(MIN(DISTANCE_FEE)),'~',FLOOR(MAX(DISTANCE_FEE)),')')AS carriage FROM T_AFTER_SALE_SERVICE_STANDARD_LONGDISTANCE_FEE A
        LEFT JOIN T_REGION C ON A.CITY = C.REGION_ID
        LEFT JOIN T_REGION D ON A.REGION = D.REGION_ID
		WHERE C.REGION_ID = #{city,jdbcType=INTEGER} AND A.IS_DELETE=0 AND A.LONGDISTANCE_FEE_TYPE=2
    </select>

    <select id="selectCarriageByRegionId" resultType="string">
        SELECT CONCAT('(长途费:',FLOOR(DISTANCE_FEE),')')AS carriage FROM T_AFTER_SALE_SERVICE_STANDARD_LONGDISTANCE_FEE A
        LEFT JOIN T_REGION D ON A.REGION = D.REGION_ID
		WHERE D.REGION_ID = #{regionId,jdbcType=INTEGER} AND A.IS_DELETE=0 AND A.LONGDISTANCE_FEE_TYPE=2
    </select>
</mapper>
