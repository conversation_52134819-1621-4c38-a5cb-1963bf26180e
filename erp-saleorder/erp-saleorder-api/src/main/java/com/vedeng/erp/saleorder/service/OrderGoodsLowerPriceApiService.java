package com.vedeng.erp.saleorder.service;


import com.vedeng.erp.saleorder.enums.LowPriceOrderTypeEnum;

import java.math.BigDecimal;

public interface OrderGoodsLowerPriceApiService {

    void tryInsertLowPrice(LowPriceOrderTypeEnum lowPriceOrderTypeEnum,Integer orderId);

    BigDecimal getCheckPrice(Integer orderId,Integer orderGoodsId,LowPriceOrderTypeEnum orderType);

    Integer addHandleOpinion(Integer goodsLowerPriceId,String handleOpinion);

    boolean isLowerPriceOrder(Integer saleorderId);

    void delOrderGoods(Integer saleorderId);

    void add(Integer saleorderGoodsId, BigDecimal priceOrderPrice);

    void addComment(Integer saleorderId, String comment);

    BigDecimal getCheckPrice(Integer saleorderId,Integer saleorderGoodsId);
}
