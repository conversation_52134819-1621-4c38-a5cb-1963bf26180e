<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
    <%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
    <%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
    <%@ taglib prefix="shiro" uri="http://shiro.apache.org/tags" %>
    <%@ taglib prefix="tags" tagdir="/WEB-INF/tags" %>
    <%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date" %>
    <%@taglib prefix="sf" uri="http://www.springframework.org/tags/form" %>
    <%@ page trimDirectiveWhitespaces="true" %>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="renderer" content="webkit|ie-comp|ie-stand">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"/>
    <meta http-equiv="Cache-Control" content="no-siteapp"/>


    <!-- 引入样式 -->
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vxe-table/css/style.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/element-ui/css/<EMAIL>">

    <!-- 引入脚本 -->
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/jquery.min.js'></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/js/jquery/validation/jquery-form.js"></script>
    <script type="text/javascript"
            src='${pageContext.request.contextPath}/static/libs/jquery/plugins/layer/layer.js'></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" charset="UTF-8" src='${pageContext.request.contextPath}/static/js/form.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" charset="UTF-8"
            src='${pageContext.request.contextPath}/static/js/closable-tab.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/common.js?rnd=${resourceVersionKey}'></script>
    <script type="text/javascript"
            src="${pageContext.request.contextPath}/static/js/call/call.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src='${pageContext.request.contextPath}/static/js/movinghead.js?rnd=${resourceVersionKey}'></script>

    <script src="${pageContext.request.contextPath}/static/vue/element-ui/js/core.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/vue/element-ui/js/vue.js"></script>
    <script src="${pageContext.request.contextPath}/static/vue/vxe-table/js/xe-utils.js"></script>
    <script src="${pageContext.request.contextPath}/static/vue/vxe-table/js/<EMAIL>"></script>
    <script src="${pageContext.request.contextPath}/static/vue/element-ui/js/<EMAIL>"></script>
    <script src="${pageContext.request.contextPath}/static/vue/element-ui/js/axios.min.js"></script>
    <script src="${pageContext.request.contextPath}/webjars/ezadmin/layui/js/core.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/vue/lodash/<EMAIL>"></script>





    <script>
        axios.interceptors.request.use(
            config => {
                config.headers = { 'X-Requested-With': 'XMLHttpRequest' }
                return config
            },
            error => {
                return Promise.reject(error)
            }
        )

        axios.interceptors.response.use(
            response => {
                return Promise.resolve(response);
            },
            error => {
                if (error && error.response) {
                    switch (error.response.status) {
                        case 302:
                            error.message = '接口重定向了！';
                            break;
                        case 400:
                            error.message = '请求错误';
                            break;
                        case 401:
                            error.message = '未授权，请登录';
                            break;
                        case 403:
                            error.message = '拒绝访问';
                            break;
                        case 404:
                            error.message = `请求地址出错`;
                            break;
                        case 408:
                            error.message = '请求超时';
                            break;
                        case 409:
                            error.message = '系统已存在相同数据！';
                            break;
                        case 500:
                            error.message = '服务器内部错误';
                            break;
                        case 501:
                            error.message = '服务未实现';
                            break;
                        case 502:
                            error.message = '网关错误';
                            break;
                        case 503:
                            error.message = '服务不可用';
                            break;
                        case 504:
                            error.message = '网关超时';
                            break;
                        case 505:
                            error.message = 'HTTP版本不受支持';
                            break;
                        case 1001:
                            var urlTips = error?.response?.config?.url || "";
                            error.message = `您没有操作权限，申请开通权限请联系研发部Aadi。<br>`+urlTips;
                            break;
                        default:
                            error.message = '异常问题，请联系管理员！';
                            break;
                    }
                }
                layer.msg(error.message, {icon: 2});
                return Promise.reject(error);
            }
        );

    </script>

</head>
<body>
