package com.wms.task;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.wms.inventorytransfer.model.dto.InventoryTransferRequest;
import com.wms.inventorytransfer.service.InventoryTransferService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 下发WMS库存转移单任务
 *
 * <AUTHOR>
 */
@Component
@JobHandler(value = "issueTransferOrderTask")
public class IssueTransferOrderTask extends AbstractJobHandler {
    @Autowired
    private InventoryTransferService inventoryTransferService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        XxlJobLogger.log("issueTransferOrderTask-JOB, START");
        if (StringUtils.isBlank(s)) {
            XxlJobLogger.log("库存转移单下发任务必须拥有相应参数");
            return ReturnT.FAIL;
        }

        InventoryTransferRequest inventoryTransferRequest = JSON.parseObject(s, InventoryTransferRequest.class);
        if (inventoryTransferRequest == null || inventoryTransferRequest.getActionId() == null) {
            XxlJobLogger.log("手动下发转移单相应参数异常");
            return ReturnT.FAIL;
        }
        inventoryTransferService.saveInventoryTransferRequest(inventoryTransferRequest);
        return SUCCESS;
    }
}
