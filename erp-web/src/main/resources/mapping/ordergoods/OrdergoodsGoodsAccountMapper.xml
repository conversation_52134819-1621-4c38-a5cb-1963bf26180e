<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.ordergoods.dao.OrdergoodsGoodsAccountMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.ordergoods.model.OrdergoodsGoodsAccount" >
    <id column="ORDERGOODS_GOODS_ACCOUNT_ID" property="ordergoodsGoodsAccountId" jdbcType="INTEGER" />
    <result column="ORDERGOODS_STORE_ID" property="ordergoodsStoreId" jdbcType="INTEGER" />
    <result column="WEB_ACCOUNT_ID" property="webAccountId" jdbcType="INTEGER" />
    <result column="GOODS_ID" property="goodsId" jdbcType="INTEGER" />
    <result column="PRICE" property="price" jdbcType="DECIMAL" />
    <result column="MIN_QUANTITY_1" property="minQuantity1" jdbcType="INTEGER" />
    <result column="PRICE_1" property="price1" jdbcType="DECIMAL" />
    <result column="MIN_QUANTITY_2" property="minQuantity2" jdbcType="INTEGER" />
    <result column="PRICE_2" property="price2" jdbcType="DECIMAL" />
    <result column="MIN_QUANTITY_3" property="minQuantity3" jdbcType="INTEGER" />
    <result column="PRICE_3" property="price3" jdbcType="DECIMAL" />
    <result column="MIN_QUANTITY_4" property="minQuantity4" jdbcType="INTEGER" />
    <result column="PRICE_4" property="price4" jdbcType="DECIMAL" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    ORDERGOODS_GOODS_ACCOUNT_ID, ORDERGOODS_STORE_ID, WEB_ACCOUNT_ID, GOODS_ID, PRICE, MIN_QUANTITY_1, PRICE_1, MIN_QUANTITY_2, 
    PRICE_2, MIN_QUANTITY_3, PRICE_3, MIN_QUANTITY_4, PRICE_4, 
    ADD_TIME, CREATOR
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_ORDERGOODS_GOODS_ACCOUNT
    where ORDERGOODS_GOODS_ACCOUNT_ID = #{ordergoodsGoodsAccountId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_ORDERGOODS_GOODS_ACCOUNT
    where ORDERGOODS_GOODS_ACCOUNT_ID = #{ordergoodsGoodsAccountId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.ordergoods.model.OrdergoodsGoodsAccount" >
    insert into T_ORDERGOODS_GOODS_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ordergoodsGoodsAccountId != null" >
        ORDERGOODS_GOODS_ACCOUNT_ID,
      </if>
      <if test="ordergoodsStoreId != null" >
        ORDERGOODS_STORE_ID,
      </if>
      <if test="webAccountId != null" >
        WEB_ACCOUNT_ID,
      </if>
      <if test="goodsId != null" >
        GOODS_ID,
      </if>
      <if test="price != null" >
        PRICE,
      </if>
      <if test="minQuantity1 != null" >
        MIN_QUANTITY_1,
      </if>
      <if test="price1 != null" >
        PRICE_1,
      </if>
      <if test="minQuantity2 != null" >
        MIN_QUANTITY_2,
      </if>
      <if test="price2 != null" >
        PRICE_2,
      </if>
      <if test="minQuantity3 != null" >
        MIN_QUANTITY_3,
      </if>
      <if test="price3 != null" >
        PRICE_3,
      </if>
      <if test="minQuantity4 != null" >
        MIN_QUANTITY_4,
      </if>
      <if test="price4 != null" >
        PRICE_4,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ordergoodsGoodsAccountId != null" >
        #{ordergoodsGoodsAccountId,jdbcType=INTEGER},
      </if>
      <if test="ordergoodsStoreId != null" >
        #{ordergoodsStoreId,jdbcType=INTEGER},
      </if>
      <if test="webAccountId != null" >
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity1 != null" >
        #{minQuantity1,jdbcType=INTEGER},
      </if>
      <if test="price1 != null" >
        #{price1,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity2 != null" >
        #{minQuantity2,jdbcType=INTEGER},
      </if>
      <if test="price2 != null" >
        #{price2,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity3 != null" >
        #{minQuantity3,jdbcType=INTEGER},
      </if>
      <if test="price3 != null" >
        #{price3,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity4 != null" >
        #{minQuantity4,jdbcType=INTEGER},
      </if>
      <if test="price4 != null" >
        #{price4,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="update" parameterType="com.vedeng.ordergoods.model.OrdergoodsGoodsAccount" >
    update T_ORDERGOODS_GOODS_ACCOUNT
    <set >
      <if test="ordergoodsStoreId != null" >
        ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER},
      </if>
      <if test="webAccountId != null" >
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="goodsId != null" >
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="price != null" >
        PRICE = #{price,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity1 != null" >
        MIN_QUANTITY_1=#{minQuantity1,jdbcType=INTEGER},
      </if>
      <if test="price1 != null" >
        PRICE_1 = #{price1,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity2 != null" >
        MIN_QUANTITY_2=#{minQuantity2,jdbcType=INTEGER},
      </if>
      <if test="price2 != null" >
        PRICE_2 = #{price2,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity3 != null" >
        MIN_QUANTITY_3=#{minQuantity3,jdbcType=INTEGER},
      </if>
      <if test="price3 != null" >
        PRICE_3 = #{price3,jdbcType=DECIMAL},
      </if>
      <if test="minQuantity4 != null" >
        MIN_QUANTITY_4=#{minQuantity4,jdbcType=INTEGER},
      </if>
      <if test="price4 != null" >
        PRICE_4 = #{price4,jdbcType=DECIMAL},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
    </set>
    where ORDERGOODS_GOODS_ACCOUNT_ID = #{ordergoodsGoodsAccountId,jdbcType=INTEGER}
  </update>
  
  <select id="getAccountGoodsListPage" parameterType="Map" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsGoodsAccountVo">
  	select
  		a.*
  	from
  		T_ORDERGOODS_GOODS_ACCOUNT a
  	where
  		1=1
  		<if test="ordergoodsGoodsAccountVo.ordergoodsStoreId != null">
  			and a.ORDERGOODS_STORE_ID = #{ordergoodsGoodsAccountVo.ordergoodsStoreId}
  		</if>
  		<if test="ordergoodsGoodsAccountVo.goodsIds != null and ordergoodsGoodsAccountVo.goodsIds.size > 0">
    		and a.GOODS_ID in
			<foreach collection="ordergoodsGoodsAccountVo.goodsIds" item="goodsId" open="(" close=")" separator=",">
				#{goodsId,jdbcType=INTEGER}
			</foreach>
    	</if>
    	<if test="ordergoodsGoodsAccountVo.webAccountId != null">
    		and a.WEB_ACCOUNT_ID = #{ordergoodsGoodsAccountVo.webAccountId}
    	</if>
  </select>
  
  <select id="getOrdergoodsAccountGoods" parameterType="com.vedeng.ordergoods.model.vo.OrdergoodsGoodsAccountVo" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsGoodsAccountVo">
  	select
  	<include refid="Base_Column_List" />
    from T_ORDERGOODS_GOODS_ACCOUNT
    where
    	1=1
    	<if test="ordergoodsStoreId != null">
    	and ORDERGOODS_STORE_ID = #{ordergoodsStoreId}
    	</if>
    	<if test="goodsId != null">
    	and GOODS_ID = #{goodsId}
    	</if>
    	<if test="webAccountId != null">
    	and WEB_ACCOUNT_ID = #{webAccountId}
    	</if>
    	limit 1
  </select>
  
  <select id="getOrdergoodsAccountGoodsByGoodsList" parameterType="com.vedeng.ordergoods.model.vo.OrdergoodsGoodsAccountVo" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsGoodsAccountVo">
  	select
  	<include refid="Base_Column_List" />
    from T_ORDERGOODS_GOODS_ACCOUNT
    where
    	1=1
    	<if test="ordergoodsStoreId != null">
    	and ORDERGOODS_STORE_ID = #{ordergoodsStoreId}
    	</if>
    	<if test="goodsId != null">
    	and GOODS_ID = #{goodsId}
    	</if>
    	<if test="webAccountId != null">
    	and WEB_ACCOUNT_ID = #{webAccountId}
    	</if>
    	<if test="goodsIds != null and goodsIds.size > 0">
    		and GOODS_ID in
			<foreach collection="goodsIds" item="goodsId" open="(" close=")" separator=",">
				#{goodsId,jdbcType=INTEGER}
			</foreach>
    	</if>
  </select>
</mapper>