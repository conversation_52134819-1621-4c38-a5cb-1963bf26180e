package com.vedeng.infrastructure.bank.api.domain;

import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

import java.util.List;

/**
 * @Description 贴现响应体
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/10/12
 */
@Data
public class B2eNbsDraftDiscountResponse extends BankResponse {

    /**
     * 响应体
     */
    private B2eNbsDraftDiscountResponse.B2eNbsDraftDiscountResBody xDataBody;

    @Data
    public static class B2eNbsDraftDiscountResBody{

        /**
         * 银行渠道交易流水号
         */
        private String svrId;

        /**
         * 客户技术请求流水号，同一客户请勿重复
         */
        private String trnId;

        /**
         * 客户业务请求流水号，同一业务请求请勿重复
         */
        private String insId;

        /**
         * 贴现张数
         */
        private int totalSize;

        /**
         * 实付贴现金额
         */
        private double totalMoney;

        /**
         * 票据贴现列表
         */
        private List<BillDiscountResponse> list;
    }

    /**
     * 票据贴现响应信息
     */
    @Data
    public static class BillDiscountResponse {

        /**
         * 票据(包)号码
         */
        private String billNo;

        /**
         * 子票区间起始
         */
        private String billRangeStart;

        /**
         * 子票区间截止
         */
        private String billRangeEnd;

        /**
         * 交易流水号
         */
        private String transId;

        /**
         * 错误码
         * 1：成功
         * 0：失败
         */
        private String retCode;

        /**
         * 返回信息
         * 1：交易成功
         * 0：具体失败错误信息
         */
        private String retMsg;
    }
}
