package com.vedeng.flash.enums;

/**
 * <AUTHOR>
 * @description: 预警任务类型枚举值
 * @date 2021/5/31 10:08
 */
public enum EarlyWarningTaskTypeEnum {
    GOOOD_TASK(1 , "催货任务"),
    TICKET_TASK(2, "催票任务"),
    PREPARE_GOOD_TASK(3, "备货任务");

    EarlyWarningTaskTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    private Integer code;

    private String msg;

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    /**
     * 根据编码获取编码信息
     * @param code
     * @return
     */
    public static String codeToMessage(Integer code){
        if(code == null){
            return "";
        }
        for(DealTypeEnum enums : DealTypeEnum.values()){
            if(code.equals(enums.getCode())){
                return enums.getMsg();
            }
        }
        return "";
    }
}
