package com.vedeng.kpi.model.DTO;

import com.vedeng.kpi.model.base.KpiBaseParams;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import java.util.Date;

/**
 * @description: kpi用户常用信息传递实体类.
 * @jira: VDERP-2376【五行剑法】规则修改.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/6/9 10:37 上午.
 * @author: Tomcat.Hui.
 */
public class KpiUserInfoDto  extends KpiBaseParams {


    private Integer saleorderId;

    private Date kpiDate;

    private Date kpiDateStart;

    private Date kpiDateEnd;

    public KpiUserInfoDto() {
    }

    public Integer getSaleorderId() {
        return saleorderId;
    }

    public void setSaleorderId(Integer saleorderId) {
        this.saleorderId = saleorderId;
    }

    public Date getKpiDate() {
        return kpiDate;
    }

    public void setKpiDate(Date kpiDate) {
        this.kpiDate = kpiDate;
    }

    public Date getKpiDateStart() {
        return kpiDateStart;
    }

    public void setKpiDateStart(Date kpiDateStart) {
        this.kpiDateStart = kpiDateStart;
    }

    public Date getKpiDateEnd() {
        return kpiDateEnd;
    }

    public void setKpiDateEnd(Date kpiDateEnd) {
        this.kpiDateEnd = kpiDateEnd;
    }
}
