<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wms.dao.VWmsOutSncodeOrderExtMapper">
    <resultMap id="BaseResultMap" type="com.wms.model.ddi.VWmsOutSncodeOrderExtDto">
        <id column="OUT_SNCODE_ORDER_ID" jdbcType="INTEGER" property="outSncodeOrderId"/>
        <result column="ORDER_ID" jdbcType="INTEGER" property="orderId"/>
        <result column="ORDER_GOODS_ID" jdbcType="INTEGER" property="orderGoodsId"/>
        <result column="OPERATE_TYPE" jdbcType="TINYINT" property="operateType"/>
        <result column="WMS_ORDER_NO" jdbcType="VARCHAR" property="wmsOrderNo"/>
        <result column="SKU" jdbcType="VARCHAR" property="sku"/>
        <result column="SKU_ID" jdbcType="INTEGER" property="skuId"/>
        <result column="SN_CODE" jdbcType="VARCHAR" property="snCode"/>
        <result column="SERIAL_NO" jdbcType="VARCHAR" property="serialNo"/>
        <result column="OUT_TIME" jdbcType="TIMESTAMP" property="outTime"/>
        <result column="PRODUCT_DATE" jdbcType="TIMESTAMP" property="productDate"/>
        <result column="EXPIRATION_DATE" jdbcType="TIMESTAMP" property="expirationDate"/>
        <result column="BATCH_NUMBER" jdbcType="VARCHAR" property="batchNumber"/>
        <result column="VEDENG_BATCH_NUMER" jdbcType="VARCHAR" property="vedengBatchNumer"/>
        <result column="COMMENTS" jdbcType="VARCHAR" property="comments"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MODE_TIME" jdbcType="TIMESTAMP" property="modeTime"/>
        <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>
    <sql id="Base_Column_List">
    OUT_SNCODE_ORDER_ID, ORDER_ID, ORDER_GOODS_ID, OPERATE_TYPE, WMS_ORDER_NO, SKU, SKU_ID, 
    SN_CODE, SERIAL_NO, OUT_TIME, PRODUCT_DATE, EXPIRATION_DATE, BATCH_NUMBER, VEDENG_BATCH_NUMER, 
    COMMENTS, ADD_TIME, MODE_TIME, IS_DELETE, CREATOR, UPDATER
  </sql>
    <select id="getAllinstallationOrderSaleOrder" resultType="com.wms.model.ddi.DdiInstallstionExtDto">
        SELECT
        "贝登" AS distributor,
        coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
        coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
        asi.SERVICE_TIME AS installstionTimeLong,
        sod.TITLE AS goodsType,
        g.GOODS_NAME AS goodsName,
        g.MODEL AS goodsModel,
        g.REGISTRATION_NUMBER AS registrationNumber,
        a.SN_CODE AS serialNumber,
        IF(s.ORDER_TYPE = 5,s.TRADER_NAME,s.TERMINAL_TRADER_NAME ) AS traderName,
        concat_ws(' ',asd.AREA,asd.ADDRESS) AS installstionAera,
        sg.NUM AS num,
        e.NAME AS engineerName,
        coalesce(e.MOBILE,e.TELEPHONE) AS engineerPhone

        FROM V_WMS_OUT_SNCODE_ORDER a


        LEFT JOIN V_CORE_SKU sku ON a.SKU_ID = sku.SKU_ID
        LEFT JOIN T_GOODS g ON a.SKU = g.SKU
        LEFT JOIN T_SYS_OPTION_DEFINITION sod ON g.GOODS_TYPE = sod.SYS_OPTION_DEFINITION_ID

        LEFT JOIN T_AFTER_SALES ass ON a.ORDER_ID = ass.ORDER_ID
        LEFT JOIN T_SALEORDER_GOODS sg ON a.ORDER_GOODS_ID = sg.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER_GOODS_WARRANTY sgw ON sg.SALEORDER_GOODS_ID = sgw.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID

        LEFT JOIN T_AFTER_SALES_INSTALLSTION asi ON ass.AFTER_SALES_ID =asi.AFTER_SALES_ID
        LEFT JOIN T_ENGINEER e ON asi.ENGINEER_ID = e.ENGINEER_ID

        LEFT JOIN T_AFTER_SALES_DETAIL asd ON ass.AFTER_SALES_ID = asd.AFTER_SALES_ID
        LEFT JOIN T_TRADER t ON asd.TRADER_ID = t.TRADER_ID

        LEFT JOIN (
          SELECT
            wb.IN_SNCODE_ORDER_ID,wb.SN_CODE,asssg.ORDER_DETAIL_ID
          FROM V_WMS_IN_SNCODE_ORDER wb
          LEFT JOIN T_AFTER_SALES_GOODS asssg ON wb.ORDER_GOODS_ID = asssg.AFTER_SALES_GOODS_ID
          WHERE wb.OPERATE_TYPE = 1
          AND wb.SKU IN
          <foreach collection="skuList" item="sku" index="index" separator="," open="(" close=")">
                #{sku,jdbcType=VARCHAR}
          </foreach>
        ) wb2 ON wb2.ORDER_DETAIL_ID = a.ORDER_GOODS_ID AND wb2.SN_CODE !=''

        LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON wiso.SN_CODE = a.SN_CODE
        AND wiso.SN_CODE != ''
        AND wiso.OPERATE_TYPE = 0

        LEFT JOIN T_BUYORDER_GOODS bg ON wiso.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
        LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
        LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = a.SN_CODE
        AND wiso2.SN_CODE != ''
        AND wiso.OPERATE_TYPE = 2 AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
        AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
        AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
        AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN ACT_HI_PROCINST ap ON CONCAT("overAfterSalesVerify_",ass.`AFTER_SALES_ID`)=ap.BUSINESS_KEY_

        WHERE
        a.OPERATE_TYPE = 0
        AND
        ass.TYPE IN (541,550,4090,4091)
        AND
        ass.ATFER_SALES_STATUS = 2
        AND
        ap.END_TIME_ <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        AND ap.END_TIME_ <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        AND a.SKU IN
        <foreach collection="skuList" item="sku" index="index" separator="," open="(" close=")">
            #{sku,jdbcType=VARCHAR}
        </foreach>
        AND
          a.SN_CODE <![CDATA[<>]]> wb2.SN_CODE
    </select>


    <select id="getAllinstallationOrderAfterSaleOrder" resultType="com.wms.model.ddi.DdiInstallstionExtDto">
        SELECT
        "贝登" AS distributor,
        coalesce(bg.GE_CONTRACT_NO,bg2.GE_CONTRACT_NO) AS quoteId,
        coalesce(bg.GE_SALE_CONTRACT_NO,bg2.GE_SALE_CONTRACT_NO) AS saleorderNo,
        asi.SERVICE_TIME AS installstionTimeLong,
        sod.TITLE AS goodsType,
        g.GOODS_NAME AS goodsName,
        g.MODEL AS goodsModel,
        g.REGISTRATION_NUMBER AS registrationNumber,
        a.SN_CODE AS serialNumber,
        IF(s.ORDER_TYPE = 5,s.TRADER_NAME,s.TERMINAL_TRADER_NAME ) AS traderName,
        concat_ws(' ',asd.AREA,asd.ADDRESS) AS installstionAera,
        asg.NUM AS num,
        e.NAME AS engineerName,
        coalesce(e.MOBILE,e.TELEPHONE) AS engineerPhone

        FROM V_WMS_OUT_SNCODE_ORDER a


        LEFT JOIN V_CORE_SKU sku ON a.SKU_ID = sku.SKU_ID
        LEFT JOIN T_GOODS g ON a.SKU = g.SKU
        LEFT JOIN T_SYS_OPTION_DEFINITION sod ON g.GOODS_TYPE = sod.SYS_OPTION_DEFINITION_ID

        LEFT JOIN T_AFTER_SALES ass ON a.ORDER_ID = ass.AFTER_SALES_ID
        LEFT JOIN T_AFTER_SALES ass1 ON ass.ORDER_ID = ass1.ORDER_ID
        LEFT JOIN T_AFTER_SALES_GOODS asg ON a.ORDER_GOODS_ID = asg.AFTER_SALES_GOODS_ID

        LEFT JOIN T_SALEORDER_GOODS sg ON asg.ORDER_DETAIL_ID= sg.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER_GOODS_WARRANTY sgw ON sg.SALEORDER_GOODS_ID = sgw.SALEORDER_GOODS_ID
        LEFT JOIN T_SALEORDER s ON sg.SALEORDER_ID = s.SALEORDER_ID

        LEFT JOIN T_AFTER_SALES_INSTALLSTION asi ON ass1.AFTER_SALES_ID =asi.AFTER_SALES_ID
        LEFT JOIN T_ENGINEER e ON asi.ENGINEER_ID = e.ENGINEER_ID

        LEFT JOIN T_AFTER_SALES_DETAIL asd ON ass1.AFTER_SALES_ID = asd.AFTER_SALES_ID
        LEFT JOIN T_TRADER t ON asd.TRADER_ID = t.TRADER_ID

        LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso ON wiso.SN_CODE = a.SN_CODE
            AND wiso.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 0
        LEFT JOIN T_BUYORDER_GOODS bg ON wiso.ORDER_GOODS_ID = bg.BUYORDER_GOODS_ID
        LEFT JOIN T_BUYORDER b ON bg.BUYORDER_ID = b.BUYORDER_ID
        LEFT JOIN V_WMS_IN_SNCODE_ORDER wiso2 ON wiso2.SN_CODE = a.SN_CODE
            AND wiso2.SN_CODE != ''
            AND wiso.OPERATE_TYPE = 2 AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN T_AFTER_SALES_GOODS asg2 ON wiso2.ORDER_GOODS_ID = asg2.AFTER_SALES_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN T_BUYORDER_GOODS bg2 ON asg2.ORDER_DETAIL_ID = bg2.BUYORDER_GOODS_ID
            AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN T_BUYORDER b2 ON bg2.BUYORDER_ID = b.BUYORDER_ID
            AND wiso.ORDER_GOODS_ID is null
        LEFT JOIN ACT_HI_PROCINST ap ON CONCAT("overAfterSalesVerify_",ass1.`AFTER_SALES_ID`)=ap.BUSINESS_KEY_
        WHERE
        a.OPERATE_TYPE = 1
        AND
        ass1.TYPE IN (541,550,4090,4091)
        AND
        ass1.ATFER_SALES_STATUS = 2
        AND
        ap.END_TIME_ <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        AND ap.END_TIME_ <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        AND a.SKU IN
        <foreach collection="skuList" item="sku" index="index" separator="," open="(" close=")">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>

</mapper>