package com.vedeng.order.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 产品归属
 * @date 2024/1/18 14:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoodsBelong {

    private Integer skuId;

    /**
     * 归属经理
     */
    private Integer assignmentManagerId;

    /**
     * 归属经理
     */
    private String assignmentManagerName;

    /**
     * 归属助理
     */
    private Integer assignmentAssistantId;

    /**
     * 归属助理
     */
    private String assignmentAssistantName;


}
