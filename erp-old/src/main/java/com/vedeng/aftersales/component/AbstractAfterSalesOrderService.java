package com.vedeng.aftersales.component;

import com.vedeng.aftersales.component.dto.AfterSaleOrderModifyDto;
import com.vedeng.aftersales.component.exception.AfterSaleCloseException;
import com.vedeng.aftersales.component.exception.AfterSaleDetailException;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.wms.service.context.ThreadLocalContext;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public abstract class AbstractAfterSalesOrderService implements AfterSalesOrderService {

    @Override
    @Transactional
    public void createAfterSalesOrder(Object afterSaleOrderAddDto) throws Exception {
        
        //新增售后单的校验
        validateAfterSaleOrderCreate(afterSaleOrderAddDto);

        //新增售后单
        AfterSales afterSaleOrder = addAfterSalerOrder(afterSaleOrderAddDto);
        ThreadLocalContext.put("afterSaleOrder",afterSaleOrder);

        //新增售后单商品
        List<AfterSalesGoods> afterSalesGoodsList = addAfterSaleGood(afterSaleOrderAddDto);
        ThreadLocalContext.put("afterSalesGoodsList", afterSalesGoodsList);

        //新增售后详情
        addAfterSaleDetail(afterSaleOrderAddDto);

        //新增售后单附件
        addAfterSalerOrderFile(afterSaleOrderAddDto,afterSaleOrder);

        //每种情况自定义的一些特殊处理
        customeAddDeal(afterSaleOrderAddDto);
    }

    protected void validateAfterSaleOrderCreate(Object afterSaleOrderAddDto) throws Exception {

        //售后单通用的一些校验
        commonAfterSaleOrderCreateValidate(afterSaleOrderAddDto);

        //每种售后单自己的一些校验
        customerAfterSaleOrderCreateValidate(afterSaleOrderAddDto);
    }

    protected abstract void customerAfterSaleOrderCreateValidate(Object afterSaleOrderAddDto) throws AfterSaleValidateException;

    protected abstract void commonAfterSaleOrderCreateValidate(Object afterSaleOrderAddDto) throws AfterSaleValidateException;

    protected abstract AfterSales addAfterSalerOrder(Object afterSaleOrderAddDto);

    protected abstract List<AfterSalesGoods> addAfterSaleGood(Object afterSaleOrderAddDto) throws Exception;

    protected abstract void addAfterSaleDetail(Object afterSaleOrderAddDto) throws AfterSaleDetailException;

    protected abstract void addAfterSalerOrderFile(Object afterSaleOrderAddDto, AfterSales afterSaleOrder);

    protected abstract void customeAddDeal(Object afterSaleOrderAddDto) throws Exception;

    @Override
    public void modifyAfterSalesOrder(Object afterSaleOrderModifyDto) throws Exception {

        //修改售后单的校验
        validateAfterSaleOrderModify(afterSaleOrderModifyDto);

        //修改售后单
        modifyAfterSalerOrder(afterSaleOrderModifyDto);

        //修改售后单商品
        modifyAfterSaleGood(afterSaleOrderModifyDto);

        //修改售后详情
        modifyAfterSaleDetail(afterSaleOrderModifyDto);

        //修改售后单附件
        modifyAfterSalerOrderFile(afterSaleOrderModifyDto);

        //每种情况自定义的一些特殊处理
        customeModifyDeal(afterSaleOrderModifyDto);
        
    }

    protected void validateAfterSaleOrderModify(Object modifyDto) throws Exception{

        //售后单通用的一些校验
        commonAfterSaleOrderModifyValidate(modifyDto);

        //每种售后单自己的一些校验
        customerAfterSaleOrderModifyValidate(modifyDto);

    }

    protected abstract void commonAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException;

    protected abstract void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException;

    protected abstract void modifyAfterSalerOrderFile(Object afterSaleOrderModifyDto);

    protected abstract void modifyAfterSaleDetail(Object afterSaleOrderModifyDto) throws AfterSaleDetailException;

    protected abstract void modifyAfterSaleGood(Object afterSaleOrderModifyDto);

    protected abstract void modifyAfterSalerOrder(Object afterSaleOrderModifyDto);

    protected abstract void customeModifyDeal(Object afterSaleOrderModifyDto) throws AfterSaleValidateException;

    @Override
    public void closeAfterSalesOrder(Object afterSaleOrderCloseDto) throws Exception {
        closeAfterSales(afterSaleOrderCloseDto);
    }
    protected abstract void closeAfterSales(Object afterSaleOrderCloseDto) throws AfterSaleCloseException;
}
