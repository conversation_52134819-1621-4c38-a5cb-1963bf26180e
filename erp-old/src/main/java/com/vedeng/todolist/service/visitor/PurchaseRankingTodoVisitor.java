package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.buyorder.dto.BuyOrderSaleOrderGoodsDetailDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.model.vo.GoodsVo;
import com.vedeng.goods.model.vo.ProductManagerAndAssistantIdVo;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.order.dao.OrderAssistantRelationMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.Spu;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.todolist.dto.PersonalTodoDealingInfo;
import com.vedeng.todolist.dto.PurchaseRankingDto;
import com.vedeng.todolist.dto.RankingDto;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理者页面->采购排名
 */
@Service
public class PurchaseRankingTodoVisitor{

    @Resource
    private UserMapper userMapper;

    private long firstDayTimestamp;

    @Resource
    private BuyorderService buyorderService;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Resource
    private OrderAssistantRelationMapper orderAssistantRelationMapper;

    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    public List<RankingDto> visitor(List<Organization> orgaList) {

        firstDayTimestamp = DateUtil.getFirstDayofMonth();

        List<Integer> orgaIdList = orgaList.stream().map(Organization::getOrgId).collect(Collectors.toList());

        List<Integer> orderAssitIdList = userMapper.getOrderAsisitIdByOrgId(orgaIdList);

        GoodsVo goodsVo =  new GoodsVo();
        goodsVo.setComponentId(2);
        goodsVo.setOrderAsistIdList(orderAssitIdList);

        List<OrderAssistantRelationDo> bingdedInfoByOrderAssIds = orderAssistantRelationMapper.getBingdedInfoByOrderAssIds(orderAssitIdList);
        List<ProductManagerAndAssistantIdVo> productManagerAndAssistantIdVoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(bingdedInfoByOrderAssIds)){
            bingdedInfoByOrderAssIds
                    .forEach(item->{
                        ProductManagerAndAssistantIdVo productManagerAndAssistantIdVo = new ProductManagerAndAssistantIdVo();
                        productManagerAndAssistantIdVo.setProductAssistantUserId(item.getProductAssitantUserId());
                        productManagerAndAssistantIdVo.setProductManagerUserId(item.getProductManagerUserId());
                        productManagerAndAssistantIdVoList.add(productManagerAndAssistantIdVo);
                    });
        }else {
            ProductManagerAndAssistantIdVo productManagerAndAssistantIdVo = new ProductManagerAndAssistantIdVo();
            productManagerAndAssistantIdVoList.add(productManagerAndAssistantIdVo);
        }
        goodsVo.setProductManagerAndAssistantIdVoList(productManagerAndAssistantIdVoList);

        Page page= Page.newBuilder(1, Integer.MAX_VALUE, null);

        Map<String, Object> map = buyorderService.getUnPurchasingOrderList(goodsVo, page);
        if(map == null){
            return null;
        }

        //查询所有的采购任务
        List<SaleorderVo> saleorderList = (List<SaleorderVo>) map.get("list");
        if (map != null) {
            List<SaleorderVo> list = (List<SaleorderVo>) map.get("list");
            List<Integer> saleorderIds = new ArrayList<>();
            //根据订单号查询归属人
            for (SaleorderVo saleorderVo : list) {
                List<SaleorderGoodsVo> sgvList = saleorderVo.getSgvList();
                if (sgvList != null) {
                    Spu spu = null;
                    for (SaleorderGoodsVo sgv : sgvList) {
                        spu = saleorderService.getSpu(sgv.getSku());
                        if (spu == null) {
                            sgv.setAssignmentManagerId(null);
                            sgv.setAssignmentAssistantId(null);
                        } else {
                            sgv.setAssignmentManagerId(spu.getAssignmentManagerId());
                            sgv.setAssignmentAssistantId(spu.getAssignmentAssistantId());
                        }
                        saleorderIds.add(sgv.getSaleorderGoodsId());
                    }
                }
            }
            //计算虚拟商品需采购数量并排除需采为0,并重新计算待采数量
            Integer buySum = (Integer) map.get("buySum");
            List<BuyOrderSaleOrderGoodsDetailDto> buyOrderSaleOrderGoodsDetailDtoList = buyorderExpenseApiService.waitBuyExpenseNeed(saleorderIds);
            for (int i = list.size() - 1; i >= 0; i--) {
                List<SaleorderGoodsVo> sgvList = list.get(i).getSgvList();
                Integer proBuySum = list.get(i).getProBuySum();
                if (sgvList.size()>0){
                    for (int j = sgvList.size() - 1; j >= 0; j--) {
                        SaleorderGoodsVo sgv = sgvList.get(j);
                        if(sgv.getIsVirture()==2){
                            for (BuyOrderSaleOrderGoodsDetailDto goodsDetailDto : buyOrderSaleOrderGoodsDetailDtoList) {
                                if (sgv.getSaleorderGoodsId().equals(goodsDetailDto.getSaleorderGoodsId())){
                                    buySum -= sgv.getNum();
                                    proBuySum -= sgv.getNum();
                                    if (goodsDetailDto.getNum() > 0){
                                        buySum += goodsDetailDto.getNum();
                                        sgv.setNeedBuyNum(goodsDetailDto.getNum());
                                        proBuySum += goodsDetailDto.getNum();
                                    }else {
                                        sgvList.remove(j);
                                    }
                                    break;
                                }
                            }
                        }
                    }
                    if (sgvList.size() == 0){
                        list.remove(i);
                    }else {
                        list.get(i).setProBuySum(proBuySum);
                    }
                }
            }

            //新商品流切换 VDERP-1970 add by brianna 2020/3/16 start
            if (!CollectionUtils.isEmpty(list)) {

                List<Integer> skuIds = new ArrayList<>();

                list.forEach(saleOrder -> {
                    if (!CollectionUtils.isEmpty(saleOrder.getSgvList())) {
                        saleOrder.getSgvList().forEach(saleOrderGoods -> {
                            skuIds.add(saleOrderGoods.getGoodsId());
                        });
                    }
                });
            }
           saleorderList = list;
        }

        // 过滤备货单
        List<SaleorderVo> saleorderListFilter = saleorderList.stream().filter(item -> !Integer.valueOf(2).equals(item.getOrderType())).collect(Collectors.toList());

        Map<Integer,SaleorderVo> saleorderMap = saleorderListFilter.stream().collect(Collectors.toMap(Saleorder::getSaleorderId, v->v,(k, v)->v));

        List<SaleorderGoodsVo> saleOrderGoodsList = new ArrayList<>();

        saleorderListFilter.forEach(saleOrder -> {
            List<SaleorderGoodsVo> collect = saleOrder.getSgvList().stream().filter(item -> Integer.valueOf(0).equals(item.getLockedStatus())).collect(Collectors.toList());
            saleOrderGoodsList.addAll(collect);
        });

        List<PurchaseRankingDto> allPurchaseRankingList =  new ArrayList<>();

        //待处理采购任务
        List<PurchaseRankingDto> unDealPurchaseRankingList = getUnDealPurchaseRankingList(saleorderMap,saleOrderGoodsList);

        allPurchaseRankingList.addAll(unDealPurchaseRankingList);

        //已处理的采购任务
        List<PurchaseRankingDto> dealedPurchaseRankingDtoList = getDealedPurchaseRankingList();

        allPurchaseRankingList.addAll(dealedPurchaseRankingDtoList);


        if(CollectionUtils.isEmpty(allPurchaseRankingList)){
            return null;
        }

        //所有的采购排名
        List<PersonalTodoDealingInfo> personalTodoDealingInfoList = dealwithAllRankingList(allPurchaseRankingList);

        return convertPersonalTodoDealingInfoToRankingList(personalTodoDealingInfoList);
    }

    private List<PurchaseRankingDto> getDealedPurchaseRankingList() {
        return saleorderGoodsMapper.getHandledSaleOrderGoods(firstDayTimestamp);
    }

    private List<RankingDto> convertPersonalTodoDealingInfoToRankingList(List<PersonalTodoDealingInfo> personalTodoDealingInfoList) {

        if(CollectionUtils.isEmpty(personalTodoDealingInfoList)){
            return null;
        }

        List<Integer> userIdList = new ArrayList<>(personalTodoDealingInfoList.stream().map(info->info.getUserId()).collect(Collectors.toSet()));

        Map<Integer, User> userMap = this.userMapper.getPurchaseUserListByUserIdList(userIdList).stream().collect(Collectors.toMap(k->k.getUserId(), v->v, (k, v) -> v));


        List<RankingDto> rankingDtoList = new ArrayList<>();

        for(int i = 0;i < personalTodoDealingInfoList.size(); i++){

            RankingDto rankingDto = new RankingDto();
            rankingDtoList.add(rankingDto);

            rankingDto.setHandler(getUserName(userMap,personalTodoDealingInfoList.get(i).getUserId()));
            rankingDto.setGroupName(getUserDepartment(userMap,personalTodoDealingInfoList.get(i).getUserId()));
            rankingDto.setAvgDealTime(new BigDecimal(personalTodoDealingInfoList.get(i).getTotalDealTime() / (personalTodoDealingInfoList.get(i).getNum() * 1000 * 60 * 60.0))
                                                    .setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString()
                                     );
        }

        rankingDtoList = rankingDtoList.stream().sorted((r1, r2) ->  Double.valueOf(r1.getAvgDealTime()).compareTo(Double.valueOf(r2.getAvgDealTime()))).collect(Collectors.toList());

        return rankingDtoList;
    }

    /**
     * 获取待处理的采购任务
     * @param saleorderMap
     * @param saleOrderGoodsList
     * @return
     */
    private List<PurchaseRankingDto> getUnDealPurchaseRankingList(Map<Integer, SaleorderVo> saleorderMap, List<SaleorderGoodsVo> saleOrderGoodsList) {

        //待处理采购任务 SKUNO集合
        List<String> skuNoList = new ArrayList<>(saleOrderGoodsList.stream().map(SaleorderGoodsVo::getSku).collect(Collectors.toSet()));

        //SKU号与 订单助理的Map
        Map<String,PurchaseRankingDto> skuNoAndOrderAssitIdMap = getSkuNoAndOrderAssitIdMap(skuNoList);

        //待处理采购任务
        return saleOrderGoodsList.stream().map(saleOrderGood -> {

                                                    // sku待采购任务开始时间,取值逻辑调整[VDERP-6642]
                                                    Long dealSatisfyDeliveryTime = dealSatisfyDeliveryTime(saleorderMap.get(saleOrderGood.getSaleorderId()).getSatisfyDeliveryTime());

                                                    PurchaseRankingDto purchaseRankingDto = new PurchaseRankingDto();
                                                    purchaseRankingDto.setType(0);
                                                    purchaseRankingDto.setSkuNo(saleOrderGood.getSku());
                                                    purchaseRankingDto.setDealTime(System.currentTimeMillis() - dealSatisfyDeliveryTime);
                                                    purchaseRankingDto.setManagerAsssistantIdList(skuNoAndOrderAssitIdMap.get(saleOrderGood.getSku()).getManagerAsssistantIdList());
                                                    purchaseRankingDto.setAssignmentAsssistantIdList(skuNoAndOrderAssitIdMap.get(saleOrderGood.getSku()).getAssignmentAsssistantIdList());
                                                    return purchaseRankingDto;
                                                }).collect(Collectors.toList());

    }

    /**
     * 将参数时间戳与当前月份比较
     *      不同则为当前月份首日时间戳
     *      相同则为参数时间戳
     * @param satisfyDeliveryTime
     * @return
     */
    private static Long dealSatisfyDeliveryTime(Long satisfyDeliveryTime) {
        Date satisfyDeliveryDate = new Date(satisfyDeliveryTime);
        String satisfyDeliveryMonth = "" + DateUtil.getYearOfDate(satisfyDeliveryDate) + DateUtil.getMonthOfDate(satisfyDeliveryDate);
        Date currentDate = new Date();
        String currentMonth = "" + DateUtil.getYearOfDate(currentDate) + DateUtil.getMonthOfDate(currentDate);
        if(currentMonth.equals(satisfyDeliveryMonth)){
            return satisfyDeliveryTime;
        }
        return DateUtil.getFirstDayofMonth();
    }

    private String getUserName(Map<Integer, User> userMap,Integer userId){
        if(userMap.get(userId) == null || StringUtils.isEmpty(userMap.get(userId).getUsername())){
            return "无此用户";
        }
        return userMap.get(userId).getUsername();
    }

    private String getUserDepartment(Map<Integer, User> userMap,Integer userId){
        if(userMap.get(userId) == null || StringUtils.isEmpty(userMap.get(userId).getOrgName())){
            return "无采购部门";
        }
        return userMap.get(userId).getOrgName();
    }

    private List<PersonalTodoDealingInfo> dealwithAllRankingList(List<PurchaseRankingDto> purchaseRankingList) {

        Map<Integer,PersonalTodoDealingInfo> personalTodoDealingInfoMap = new HashedMap();

        purchaseRankingList.stream().forEach(purchaseRanking -> {

            //未处理的采购排名
            if(purchaseRanking.getType() == 0){
                //循环所有的订单助理 因为一个采购排名可能对应多个订单助理
                purchaseRanking.getAllAsssistantIdSet().stream().forEach(asssistantId -> {
                    dealwithPersonalTodoDealingInfo(purchaseRanking,asssistantId,personalTodoDealingInfoMap);
                });
            }

            //已处理的采购排名
            if(purchaseRanking.getType() == 1){
                //已处理的采购排名只可能对应一个订单助理
                dealwithPersonalTodoDealingInfo(purchaseRanking,Integer.valueOf(purchaseRanking.getAssignmentAsssistantIdStr()),personalTodoDealingInfoMap);
            }

        });

        return new ArrayList<>(personalTodoDealingInfoMap.values()) ;
    }

    /**
     * 对于每个数据进行处理
     * @param purchaseRanking
     * @param userId
     * @param personalTodoDealingInfoMap
     */
    private void dealwithPersonalTodoDealingInfo(PurchaseRankingDto purchaseRanking,Integer userId,Map<Integer,PersonalTodoDealingInfo> personalTodoDealingInfoMap) {

        PersonalTodoDealingInfo personalTodoDealingInfo = personalTodoDealingInfoMap.get(userId);

        if (personalTodoDealingInfo == null) {
            personalTodoDealingInfo = new PersonalTodoDealingInfo();
            personalTodoDealingInfo.setUserId(userId);
            personalTodoDealingInfo.setNum(1L);
            personalTodoDealingInfo.setTotalDealTime(purchaseRanking.getDealTime());
            personalTodoDealingInfoMap.put(userId, personalTodoDealingInfo);
        } else {
            personalTodoDealingInfo.setNum(personalTodoDealingInfo.getNum() + 1);
            personalTodoDealingInfo.setTotalDealTime(personalTodoDealingInfo.getTotalDealTime() + purchaseRanking.getDealTime());
        }
    }

    private Map<String,PurchaseRankingDto> getSkuNoAndOrderAssitIdMap(List<String> skuNoList) {

        List<PurchaseRankingDto> purchaseRankingList = this.coreSkuGenerateMapper.getSkuNoAndOrderAssitIdMapModifyV2(skuNoList);

        purchaseRankingList.stream().forEach(purchaseRanking -> {

            if(StringUtil.isNotEmpty(purchaseRanking.getAssignmentAsssistantIdStr())){
                List<Integer> assignmentAsssistantIdList = Arrays.asList(purchaseRanking.getAssignmentAsssistantIdStr().split(","))
                        .stream().map(idStr -> Integer.valueOf(idStr)).collect(Collectors.toList());


                purchaseRanking.setAssignmentAsssistantIdList(assignmentAsssistantIdList);
            }


            if(StringUtil.isNotEmpty(purchaseRanking.getManagerAsssistantIdStr())){
                List<Integer> managerAsssistantIdList = Arrays.asList(purchaseRanking.getManagerAsssistantIdStr().split(","))
                        .stream().map(idStr -> Integer.valueOf(idStr)).collect(Collectors.toList());


                purchaseRanking.setManagerAsssistantIdList(managerAsssistantIdList);
            }

        });

        return purchaseRankingList.stream().collect(Collectors.toMap(k->k.getSkuNo(),v -> v,(k,v) -> v));
    }

}

