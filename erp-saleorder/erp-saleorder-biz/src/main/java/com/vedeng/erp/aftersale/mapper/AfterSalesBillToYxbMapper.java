package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesBillToYxbEntity;

import java.util.List;

public interface AfterSalesBillToYxbMapper {

    int deleteByPrimaryKey(Integer afterSalesBillToYxbId);


    int insert(AfterSalesBillToYxbEntity row);


    AfterSalesBillToYxbEntity selectByPrimaryKey(Integer afterSalesBillToYxbId);


    List<AfterSalesBillToYxbEntity> selectAll();


    int updateByPrimaryKey(AfterSalesBillToYxbEntity row);

    List<AfterSalesBillToYxbEntity> selectByCondition(AfterSalesBillToYxbEntity afterSalesBillToYxbEntity);
}