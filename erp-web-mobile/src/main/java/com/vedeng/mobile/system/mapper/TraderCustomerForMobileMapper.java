package com.vedeng.mobile.system.mapper;

import com.vedeng.mobile.system.dto.TraderCustomerSearchApiDto;
import com.vedeng.mobile.system.dto.TraderCustomerSearchResultDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository("traderCustomerForMobileMapper")
public interface TraderCustomerForMobileMapper {

    List<TraderCustomerSearchResultDto> searchTraderCustomerByName(@Param("customerName") String customerName);

    List<TraderCustomerSearchResultDto> searchTraderCustomerListPage(@Param("traderCustomerVo") TraderCustomerSearchApiDto traderCustomerVo, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    List<TraderCustomerSearchResultDto> searchTraderCustomerListPageForYingji(@Param("traderCustomerVo") TraderCustomerSearchApiDto traderCustomerVo, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);


    List<TraderCustomerSearchResultDto> searchTraderCustomerListPageForFeigong(@Param("traderCustomerVo") TraderCustomerSearchApiDto traderCustomerVo, @Param("pageNum") int pageNum, @Param("pageSize") int pageSize);

    List<Integer>  selectTraderCustomerId(Integer traderId);


    List<Integer>  selectSaleDownArea(@Param("userIds") List<Integer> userIds,@Param("areaCode")Integer areaCode);

    List<Integer>  selectSaleDownBelongA2(@Param("userIds") List<Integer> userIds,@Param("areaCode")Integer areaCode,@Param("traderId") Integer traderId);

    List<Integer>  selectBelongTraderByAll(@Param("userIds") List<Integer> userIds,@Param("traderId") Integer traderId);


    Integer getTraderWorkAreaByCustomerName(@Param("customerName") String customerName);


    String getCustomerRegionSale(@Param("regionId") Integer regionId,@Param("userId")Integer userId);


    Integer  getTraderWorkAreaByTraderId(@Param("traderId") Integer traderId);

    int updateTraderWorkAreaByTraderId(@Param("traderId") Integer traderId,@Param("areaId") Integer areaId,@Param("areaNameFull") String areaNameFull);

    int insertTraderWorkAreaByTraderId(@Param("traderId") Integer traderId,@Param("areaId") Integer areaId,@Param("areaNameFull") String areaNameFull);

}
