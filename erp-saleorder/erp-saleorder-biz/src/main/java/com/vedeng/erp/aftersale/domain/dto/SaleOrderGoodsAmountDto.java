package com.vedeng.erp.aftersale.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 订单售后商品明细金额dto（含售后）
 * @date 2023/9/21 14:01
 */
@Data
public class SaleOrderGoodsAmountDto {

    /**
     * 销售单id
     */
    private Integer saleOrderId;

    /**
     * 销售数量
     */
    private BigDecimal saleOrderGoodsNum;

    /**
     * 销售单价
     */
    private BigDecimal price;

    /**
     * 订单状态
     */
    private Integer orderType;

    /**
     * 订单商品实际金额(含退货)
     */
    private BigDecimal maxSkuRefundAmount;

    /**
     * 售后商品金额
     */
    private BigDecimal refundAmount;

    /**
     * 售后数量
     */
    private BigDecimal num;



}
