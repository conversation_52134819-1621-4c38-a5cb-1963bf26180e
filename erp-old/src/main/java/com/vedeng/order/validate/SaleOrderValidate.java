package com.vedeng.order.validate;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.wms.service.OrderPeerApiService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.order.enums.PaymentTypeEnum;
import com.vedeng.order.enums.SaleOrderStatusEnum;
import com.vedeng.order.model.GeSaleOrder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.trader.model.Trader;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderFinanceVo;
import com.vedeng.trader.service.TraderCustomerService;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.vedeng.common.util.NumberUtil.isZeroOrNull;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/28 16:47
 */
@Slf4j
@Component
public class SaleOrderValidate {


    @Resource
    private TraderCustomerService traderCustomerService;

    @Resource
    private InvoiceService invoiceService;

    @Resource
    private SaleorderService saleorderService;

    @Autowired
    private OrderPeerApiService orderPeerApiService;

    @Value("${GE_TRADER_SKU}")
    private String geTraderSku;


    private ResultInfo baseValidateWhenOrderSubmitCheck(Saleorder saleOrderInfo, TraderCustomerVo customer) {
        if (saleOrderInfo.getRetainageAmount() == null) {
            saleOrderInfo.setRetainageAmount(BigDecimal.ZERO);
        }

        if (null == saleOrderInfo.getTraderId()) {
            return new ResultInfo<>(-1, "当前订单的客户ID不存在，请检查订单的客户信息");
        }

        if (null != saleOrderInfo.getAccountPeriodAmount() && null != saleOrderInfo.getPrepaidAmount()) {
            BigDecimal prepayAmount = saleOrderInfo.getPrepaidAmount().add(saleOrderInfo.getAccountPeriodAmount())
                    .add(saleOrderInfo.getRetainageAmount());
            // VDERP-12711 totalAmount == null，不能申请审核
            if (saleOrderInfo.getTotalAmount() == null){
                return new ResultInfo<>(-1, "订单金额为空，请修改后重试");
            }
            if (saleOrderInfo.getTotalAmount().compareTo(prepayAmount) != 0) {
                return new ResultInfo<>(-1, "订单金额与【预付金额+账期支付金额+尾款】不一致");
            }
        }

        if (SaleOrderStatusEnum.CUSTOMER_CONFIRMING.getStatus().equals(saleOrderInfo.getStatus())) {
            return new ResultInfo(-1, "该订单待用户确认，无法申请审核");
        }

        List<SaleorderGoods> saleOrderGoods = saleorderService.getSaleorderGoodsInfo(saleOrderInfo);
        try {
            GeSaleOrder geSaleOrder = JsonUtils.readValue(geTraderSku, GeSaleOrder.class);
            List<String> skuList = geSaleOrder.getSkuList();
            SaleorderGoods goods = null;
            if (CollectionUtils.isNotEmpty(saleOrderGoods)) {
                goods = saleOrderGoods.stream().filter(orderGoods -> skuList.contains(orderGoods.getSku())).findFirst().orElse(null);
            }
            if (null != goods) {
                if (!org.apache.commons.lang3.StringUtils.isNotBlank(saleOrderInfo.getTerminalTraderName()) || null == saleOrderInfo.getTerminalTraderName()) {
                    return new ResultInfo(-1, "GE商品的订单，终端信息需要正确填写", saleOrderInfo);
                }
            }
        } catch (IOException e) {
            log.error("校验GE商品失败", e);
        }

        // TODO 26_V02 提交审核账期校验
        if (!PaymentTypeEnum.PAY_BEFORE.getType().equals(saleOrderInfo.getPaymentType())) {
            ResultInfo checkBillPeriodResult = traderCustomerService.checkBillPeriodForOrder(saleOrderInfo, customer.getTraderCustomerId());
            if (!checkBillPeriodResult.getCode().equals(0)) {
                return checkBillPeriodResult;
            }
        }

        return new ResultInfo(0, "");
    }

    /**
     * 校验提交审核
     * {@link com.vedeng.order.controller.SaleorderController#checkSaleOrder(HttpServletRequest, Saleorder)}
     *
     * @return
     */
    public ResultInfo validateSubmitCheckWithHC(Saleorder saleOrderInfo) {
        // 根据客户ID获取客户基本信息
        Trader trader = traderCustomerService.getBaseTraderByTraderId(saleOrderInfo.getTraderId());
        // 根据客户ID查询客户信息
        TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(saleOrderInfo.getTraderId());

        ResultInfo resultInfo = baseValidateWhenOrderSubmitCheck(saleOrderInfo, customer);
        if (!resultInfo.getCode().equals(0)) {
            return resultInfo;
        }

        if (null == trader) {
            return new ResultInfo<>(-1, "当前订单的客户不存在，请检查该订单的客户信息");
        }
        // 客户资质状态信息
        Integer traderStatus = trader.getTraderStatus();
        // 认证状态：0 未审核 1 待审核 2 审核通过 3 审核不通过 只有审核通过，才可申请审核成功
        if (!ErpConst.TWO.equals(traderStatus)) {
            // 根据客户资质状态返回描述语
            String traderStatusDesc = ErpConst.ZERO.equals(traderStatus) ? "未审核"
                    : ErpConst.ONE.equals(traderStatus) ? "待审核"
                    : ErpConst.THREE.equals(traderStatus) ? "审核不通过"
                    : "未知";
            return new ResultInfo<>(-1, "当前订单的客户资质状态" + traderStatusDesc + ", 请联系耗材运营人员对该客户信息进行审核");
        }

        try {
            boolean phtxFlag = orderPeerApiService.getOrderIsGoodsPeer(saleOrderInfo.getSaleorderId());
            if (phtxFlag) {
                //满足票货同行条件即开票方式为电子票，必然是增值税普通发票
                TraderCertificateVo tc = new TraderCertificateVo();
                tc.setTraderId(trader.getTraderId());
                tc.setTraderType(ErpConst.ONE);
                if (trader.getCustomerNature() != null && trader.getCustomerNature() == 465) {
                    tc.setCustomerType(2);
                } else {
                    tc.setCustomerType(1);
                }
                if(trader.getTraderId()==null){
                    log.error("checkInvoiceParams::traderId{}",trader.getTraderId());
                }
                Map<String, Object> map = traderCustomerService.getFinanceAndAptitudeByTraderId(tc, "all");
                JSONObject json = JSONObject.fromObject(map.get("finance"));
                if (null != json) {
                    TraderFinanceVo tf = (TraderFinanceVo) JSONObject.toBean(json, TraderFinanceVo.class);
                    //税号为空时,必须税号审核状态为审核通过才允许申请开票
                    if (com.vedeng.common.util.StringUtil.isBlank(tf.getTaxNum())) {
                        if (null != tf.getCheckStatus() && tf.getCheckStatus() == 1) {
                            return new ResultInfo(0, "操作成功");
                        } else {
                            return new ResultInfo<>(-1, "客户开票资料不全，请联系归属销售进行维护");
                        }
                    }
                } else {
                    return new ResultInfo<>(-1, "未查询到财务资质信息");
                }
            }
        } catch (Exception e) {
            log.error("校验客户税号产生异常: {}", e.getMessage(), e);
            return new ResultInfo<>(-1, "校验客户税号产生异常");
        }
        return new ResultInfo(0, "校验成功");
    }

    /**
     * 校验提交审核
     * {@link com.vedeng.order.controller.SaleorderController#checkSaleOrder(HttpServletRequest, Saleorder)}
     *
     * @return
     */
    public ResultInfo validateSubmitCheckWithoutHC(Saleorder saleOrderInfo) {
        // 根据客户ID查询客户信息
        TraderCustomerVo customer = traderCustomerService.getCustomerBussinessInfo(saleOrderInfo.getTraderId());
        ResultInfo resultInfo = baseValidateWhenOrderSubmitCheck(saleOrderInfo, customer);
        if (!resultInfo.getCode().equals(0)) {
            return resultInfo;
        }
        // 终端类型
//        if (saleOrderInfo.getCustomerNature() == 465) {
//            // 分销
//            if (isZeroOrNull(saleOrderInfo.getSalesAreaId())) {
//                return new ResultInfo(-1, "销售区域不能为空");
//            }
//        }
        if (isZeroOrNull(saleOrderInfo.getTraderId())) {
            return new ResultInfo(-1, "客户不能为空");
        }

        if (isZeroOrNull(customer.getCustomerType())) {
            return new ResultInfo(-1, "客户类型不能为空");
        }
        if (isZeroOrNull(customer.getCustomerNature())) {
            return new ResultInfo(-1, "客户终端类型不能为空");
        }

        if (StringUtils.isEmpty(saleOrderInfo.getTraderContactMobile())) {
            return new ResultInfo(-1, "客户联系人手机号不能为空");
        }

        if (StringUtils.isEmpty(saleOrderInfo.getTraderContactName()) || isZeroOrNull(saleOrderInfo.getTraderContactId())) {
            return new ResultInfo(-1, "客户联系人不能为空");
        }

        if (StringUtils.isEmpty(saleOrderInfo.getTraderAddress()) || isZeroOrNull(saleOrderInfo.getTraderAddressId())) {
            return new ResultInfo(-1, "客户联系地址不能为空");
        }

        if (StringUtils.isEmpty(saleOrderInfo.getTakeTraderName()) || isZeroOrNull(saleOrderInfo.getTakeTraderId())) {
            return new ResultInfo(-1, "收货客户不能为空");
        }
        if (StringUtils.isEmpty(saleOrderInfo.getTakeTraderContactName()) || isZeroOrNull(saleOrderInfo.getTakeTraderContactId())) {
            return new ResultInfo(-1, "收货联系人不能为空");
        }

        if (StringUtils.isEmpty(saleOrderInfo.getTakeTraderContactMobile())) {
            return new ResultInfo(-1, "收货联系人手机号不能为空");
        }
        if (isZeroOrNull(saleOrderInfo.getTakeTraderAddressId()) || StringUtils.isEmpty(saleOrderInfo.getTakeTraderAddress())) {
            return new ResultInfo(-1, "收货地址不能为空");
        }

        //用户选择不寄送时校验
        if (saleOrderInfo.getIsSendInvoice() == 1) {
            if (isZeroOrNull(saleOrderInfo.getInvoiceTraderId()) || StringUtils.isEmpty(saleOrderInfo.getInvoiceTraderName())) {
                return new ResultInfo(-1, "收票客户不能为空");
            }
            if (isZeroOrNull(saleOrderInfo.getInvoiceTraderContactId()) || StringUtils.isEmpty(saleOrderInfo.getInvoiceTraderContactName())) {
                return new ResultInfo(-1, "收票联系人不能为空");
            }
            if (StringUtils.isEmpty(saleOrderInfo.getInvoiceTraderContactMobile())) {
                return new ResultInfo(-1, "收票联系人手机号不能为空");
            }
//            if (isZeroOrNull(saleOrderInfo.getInvoiceTraderAddressId()) || StringUtils.isEmpty(saleOrderInfo.getInvoiceTraderAddress())) {
//                return new ResultInfo(-1, "收票地址不能为空");
//            }
        }

        if (isZeroOrNull(saleOrderInfo.getInvoiceType())) {
            return new ResultInfo(-1, "发票类型不能为空");
        }

        return new ResultInfo(0, "校验成功");
    }

}
