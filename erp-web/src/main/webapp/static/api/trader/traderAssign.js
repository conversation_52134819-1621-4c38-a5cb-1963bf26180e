function searchUser(param) {
    return axios({
        url: '/system/user/search.do?type=ALL',
        method: 'post',
        params: param
    })
}


function searchTraderName(param) {
    return axios({
        url: '/traderSearchApi/searchByName.do',
        method: 'post',
        params: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function queryAssignInfo(param) {
    return axios({
        url: '/trader/customer/queryAssignInfo.do',
        method: 'post',
        params: param,
    })
}

function queryOrgName(param) {
    return axios({
        url: '/user/getOrgName.do',
        method: 'post',
        params: param,
    })
}

function assignCustomer(param){
    return axios({
        url: '/publicCustomer/assign.do',
        method: 'post',
        data: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function getRegion(param) {
    return axios({
        url: '/system/region/getRegion.do',
        method: 'post',
        data: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}

function queryUserInfo(param) {
    return axios({
        url: '/trader/customer/queryUserInfo.do',
        method: 'post',
        data: param,
        headers: {
            'Content-Type': 'application/json'  //如果写成contentType会报错,如果不写这条也报错
        }
    })
}
