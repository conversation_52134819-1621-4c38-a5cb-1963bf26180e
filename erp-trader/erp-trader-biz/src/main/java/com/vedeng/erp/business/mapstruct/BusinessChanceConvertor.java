package com.vedeng.erp.business.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceGoodsDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import com.vedeng.erp.business.domain.entity.BussinessChanceEntity;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商机dto 转 entity
 * @date 2022/7/12 10:45
 **/
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessChanceConvertor extends BaseMapStruct<BussinessChanceEntity, BusinessChanceDto> {

    @Override
    @Mapping(target = "businessChanceGoodsDtos", source = "businessChanceGoods", qualifiedByName = "jsonArrayToObject")
    @Mapping(target = "otherContactInfo", source = "otherContact")
    BusinessChanceDto toDto(BussinessChanceEntity entity);

    @Override
    @Mapping(target = "businessChanceGoods", source = "businessChanceGoodsDtos", qualifiedByName = "objectToJsonArray")
    @Mapping(target = "otherContact", source = "otherContactInfo")
    BussinessChanceEntity toEntity(BusinessChanceDto dto);

    /**
     * entity 中JSONArray 转 原对象
     * @param source JSONArray
     * @return List<BusinessChanceGoodsDto> dto中的对象
     */
    @Named("jsonArrayToObject")
    default List<BusinessChanceGoodsDto> jsonArrayToObject(JSONArray source) {
        if (CollUtil.isEmpty(source)) {
            return Collections.emptyList();
        }
        return source.toJavaList(BusinessChanceGoodsDto.class);
    }
    /**
     * dto 原对象中 转 JSONArray
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("objectToJsonArray")
    default JSONArray objectToJsonArray(List<BusinessChanceGoodsDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }

}
