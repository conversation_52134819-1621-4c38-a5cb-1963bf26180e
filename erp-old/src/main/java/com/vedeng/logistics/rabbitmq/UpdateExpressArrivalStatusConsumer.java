package com.vedeng.logistics.rabbitmq;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.newtask.LogisticsInfoTask;
import com.rabbitmq.client.Channel;
import com.vedeng.base.api.dto.kuaidi.LogisticsDTO;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.erp.buyorder.service.BuyorderApiService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.LogisticsMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.service.ExpressService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2022−10-10 下午3:16
 * @description 监听mq变更express表的签收状态
 */
@Component
public class UpdateExpressArrivalStatusConsumer extends AbstractMessageListener {

    @Resource
    ExpressService expressService;

    @Resource
    ExpressMapper expressMapper;

    @Resource
    private LogisticsInfoTask logisticsInfoTask;

    @Resource
    private LogisticsMapper logisticsMapper;

    @Autowired
    private BuyorderApiService buyorderApiService;


    public static final Logger LOGGER = LoggerFactory.getLogger(UpdateExpressArrivalStatusConsumer.class);

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        try {
            String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);
            if (StringUtils.isBlank(messageBody)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            LOGGER.info("base推送更新快递收货状态,messageBody:{}",messageBody);
            net.sf.json.JSONObject messageJson = net.sf.json.JSONObject.fromObject(messageBody);
            LogisticsDTO logisticsDTO= (LogisticsDTO) net.sf.json.JSONObject.toBean(messageJson, LogisticsDTO.class);
            if (StringUtils.isBlank(logisticsDTO.getNu())){
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            Express expressByLogisticsNo = expressMapper.getExpressByLogisticsNo(logisticsDTO.getNu());
            if (Objects.isNull(expressByLogisticsNo)){
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                LOGGER.info("base推送更新快递收货状态,不满足指定条件:{}",messageBody);
                return;
            }
            List<Integer> expressIdList = logisticsMapper.getExpressIdByCodeAndNo(logisticsDTO.getCom(), logisticsDTO.getNu());

            LOGGER.info("通过物流单号查询到Express有：{}条,物流单号：{}",expressIdList.size(),logisticsDTO.getNu());
            if (CollectionUtils.isNotEmpty(expressIdList)){
                LOGGER.info("base推送更新快递收货状态,开始进行快递收货业务,expressIdList:{}", expressIdList);
                for (Integer expressId : expressIdList) {
                    List<ExpressDetail> expressDetails = expressMapper.getExpressDetailByExpressId(expressId);
                    if (CollUtil.isNotEmpty(expressDetails) && SysOptionConstant.ID_515.equals(expressDetails.get(0).getBusinessType())) {
                        LOGGER.info("base推送更新快递收货状态,该快递为515类型,expressDetails:{}", JSON.toJSONString(expressDetails));
                        if (buyorderApiService.isDeliveryDirectBuyOrderExpress(expressId)) {
                            LOGGER.info("base推送更新快递收货状态,该快递为直发采购快递单，进行确认收货处理,expressDetails:{}", JSON.toJSONString(expressDetails));
                            buyorderApiService.deliveryDirectConfirmArrival(Collections.singletonList(expressId));
                        }
                    } else {
                        LOGGER.info("base推送更新快递收货状态,该快递非515类型,正常业务处理,expressDetails:{}", JSON.toJSONString(expressDetails));
                        messageJson.put("expressId", expressId);
                        /**
                         * 更新物流签收状态并同步订单状态
                         */
                        logisticsInfoTask.updateExpressByKuai100(messageJson,logisticsDTO.getNu());
                    }
                }
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            LOGGER.error("base推送更新快递收货状态出错", e);
            try {
                channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            } catch (Exception exception) {
                LOGGER.error("base推送更新快递收货状态出错，将消息返回给rabbitmq错误：", exception);
            }
        }finally {

        }
    }
}
