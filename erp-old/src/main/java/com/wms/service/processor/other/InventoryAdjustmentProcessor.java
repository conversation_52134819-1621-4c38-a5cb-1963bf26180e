package com.wms.service.processor.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.goods.dao.GoodsMapper;
import com.vedeng.logistics.dao.BarcodeMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.model.Barcode;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogService;
import com.vedeng.logistics.service.WarehouseStockService;
import com.vedeng.logistics.service.WarehousesService;
import com.vedeng.stock.api.stock.dto.StockInfoDto;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.*;
import com.wms.dao.WmsLogicalOrdergoodsMapper;
import com.wms.dto.InventoryAdjustmentDetailDto;
import com.wms.dto.InventoryAdjustmentDto;
import com.wms.dto.WmsResponse;
import com.wms.inventoryadjustment.dao.InventoryAdjustmentMapper;
import com.wms.inventoryadjustment.model.po.InventoryAdjustmentDetailPo;
import com.wms.inventoryadjustment.model.po.InventoryAdjustmentPo;
import com.wms.model.po.WmsLogicalOrdergoods;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.wms.service.processor.AbstractWMSCalllBackProcessor;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 库存调整单处理器
 *
 * <AUTHOR>
 */
@Service
public class InventoryAdjustmentProcessor extends AbstractWMSCalllBackProcessor<InventoryAdjustmentDto> {

    private static final Logger logger = LoggerFactory.getLogger(InventoryAdjustmentProcessor.class);

    @Autowired
    private InventoryAdjustmentMapper inventoryAdjustmentMapper;

    @Autowired
    private WmsLogicalOrdergoodsMapper wmsLogicalOrdergoodsMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Resource
    private WarehouseGoodsOperateLogService warehouseGoodsOperateLogService;

    @Resource
    private GoodsMapper goodsMapper;

    @Resource
    private BarcodeMapper barcodeMapper;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;

    @Autowired
    private WarehouseStockService warehouseStockService;

    @Value("${stock_url}")
    protected String stockUrl;

    @Autowired
    private WarehousesService warehousesService;


    @Override
    protected boolean needIdempotentValidator() {
        return true;
    }

    @Override
    protected String getBusinessKey(InventoryAdjustmentDto requestBean) {
        return requestBean.getADJNo();
    }

    @Override
    protected void commonValidator(InventoryAdjustmentDto inventoryAdjustmentDto) throws Exception {
        if (!InventoryAdjustmentTypeEnum.INVENTORY_ADJUSTMENT.getCode().equals(inventoryAdjustmentDto.getAdjType())) {
            logger.error("库存调整单类型错误 AdjType:{}", inventoryAdjustmentDto.getAdjType());
            throw new Exception("库存调整单 AdjType:{}" + inventoryAdjustmentDto.getAdjType() + "的类型错误");
        }
        if (CollectionUtils.isEmpty(inventoryAdjustmentDto.getDetails())) {
            logger.error("库存调整单信息不全 details:{}", JSON.toJSONString(inventoryAdjustmentDto.getDetails()));
            throw new Exception("库存调整单信息不全 details:{}" + JSON.toJSONString(inventoryAdjustmentDto.getDetails()));
        }
        for (InventoryAdjustmentDetailDto detail : inventoryAdjustmentDto.getDetails()) {
            if (LogicalEnum.HDC.getLogicalWarehouseCode().equals(detail.getLotAtt08())) {
                logger.error("暂不支持活动库相关的库存调整单类型 detail:{}", JSON.toJSONString(detail));
                throw new Exception("暂不支持活动库相关的库存调整单类型 detail:{}" + JSON.toJSONString(detail));
            }
        }
    }

    @Override
    protected void doDealWithRequest(InventoryAdjustmentDto inventoryAdjustmentDto) throws Exception {
        logger.info("库存调整单开始  单号:{},inventoryAdjustmentDto:{}" ,inventoryAdjustmentDto.getADJNo(),JSON.toJSONString(inventoryAdjustmentDto));
        //1.更新库存调整单信息
        InventoryAdjustmentPo inventoryAdjustmentPo = pushInventoryAdjustment(inventoryAdjustmentDto);

        //2.更新库存详情单信息
        List<Integer> inventoryAdjustmentDetailIds = pushInventoryAdjustmentDetail(inventoryAdjustmentDto, inventoryAdjustmentPo);

        //3.维护库存ERP库存占用信息
        pushLogicalOrderGoods(inventoryAdjustmentDto, inventoryAdjustmentDetailIds);

        //4.更新ERP库存信息
        pushWarehouseGoodsLog(inventoryAdjustmentDto, inventoryAdjustmentDetailIds);

        //5.库存调整单推送至WMS
        pushAdjustmentInfo2Wms(inventoryAdjustmentDto);

        //6.更新库存服务调整信息
        pushStock(inventoryAdjustmentDto);
    }

    /**
     * 库存调整单推送至WMS
     *
     * @param inventoryAdjustmentDto
     * @throws Exception
     */
    private void pushAdjustmentInfo2Wms(InventoryAdjustmentDto inventoryAdjustmentDto) throws Exception {
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_INVENTORY_ADJUSRMENT);
        WmsResponse response = wmsInterface.request(inventoryAdjustmentDto);
        if (response.getReturnFlag().equals(0)) {
            logger.error("库存调整单推送至WMS失败 转移单号:{},失败原因:{}", inventoryAdjustmentDto.getADJNo() , response.getReturnDesc());
            throw new Exception("库存调整单推送至WMS失败 转移单号:{}" + inventoryAdjustmentDto.getADJNo() + ",失败原因:{}" + response.getReturnDesc());
        }
    }

    /**
     * 调整ERP库存信息
     *
     * @param inventoryAdjustmentDto
     */
    private void pushWarehouseGoodsLog(InventoryAdjustmentDto inventoryAdjustmentDto, List<Integer> inventoryAdjustmentDetailIds) throws Exception {
        List<InventoryAdjustmentDetailDto> inventoryAdjustmentDetails = inventoryAdjustmentDto.getDetails();
        if (CollectionUtils.isEmpty(inventoryAdjustmentDetails)) {
            logger.error("库存调整ERP库存 详情为空单号:{}" ,inventoryAdjustmentDto.getADJNo());
            throw new Exception("库存调整ERP库存 详情为空");
        }

        for (int index = 0; index < inventoryAdjustmentDetails.size(); index++) {
            InventoryAdjustmentDetailDto adjustmentDetailDto = inventoryAdjustmentDetails.get(index);

            if(StringUtil.isBlank(adjustmentDetailDto.getLotAtt11())){
                throw new RuntimeException(inventoryAdjustmentDto.getADJNo()+"贝登批次码为空");
            }

            if (adjustmentDetailDto.getToQty().compareTo(BigDecimal.ZERO) == 1) {
                //1.库存调整单盘盈情况
                saveAddWarehoseLoginfo(inventoryAdjustmentDto, inventoryAdjustmentDetailIds, index, adjustmentDetailDto);

            } else {
                //2.库存调整单盘亏情况
                updateSubWarehouseLogInfo(inventoryAdjustmentDto, inventoryAdjustmentDetailIds, index, adjustmentDetailDto);
            }
        }

    }

    /**
     * 库存调整盘亏情况ERP库存 log维护
     *
     * @param inventoryAdjustmentDto
     * @param adjustmentDetailDto
     * @throws Exception
     */
    private void updateSubWarehouseLogInfo(InventoryAdjustmentDto inventoryAdjustmentDto,
                                           List<Integer> inventoryAdjustmentDetailIds, int index,
                                           InventoryAdjustmentDetailDto adjustmentDetailDto) throws Exception {
        logger.info("库存调整盘亏情况log表维护 START 单号:{}",inventoryAdjustmentDto.getADJNo());
        int adjustmentNum = Math.abs(adjustmentDetailDto.getToQty().intValue());

        WarehouseGoodsOperateLog warehouseGoodsOperateLogForQuery = new WarehouseGoodsOperateLog();
        warehouseGoodsOperateLogForQuery.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(adjustmentDetailDto.getLotAtt08()));
        warehouseGoodsOperateLogForQuery.setGoodsId(goodsMapper.getGoodsIdBySku(adjustmentDetailDto.getSKU()));
        warehouseGoodsOperateLogForQuery.setVedengBatchNumer(adjustmentDetailDto.getLotAtt11());

        List<WarehouseGoodsOperateLog> availableLogicalGoods = warehouseGoodsOperateLogMapper.getAvailableLogicalGoods(warehouseGoodsOperateLogForQuery);

        if (CollectionUtils.isEmpty(availableLogicalGoods)) {
            logger.error("盘亏单ERP无符合条件库存 单号:{},贝登批次码:{},sku:{},逻辑仓:{}",
                    inventoryAdjustmentDto.getADJNo(), adjustmentDetailDto.getLotAtt11(),adjustmentDetailDto.getSKU(),adjustmentDetailDto.getLotAtt08());
            throw new Exception("盘亏单ERP无符合条件库存 单号:{},贝登批次码:{},sku:{},逻辑仓:{}" + inventoryAdjustmentDto.getADJNo() +
                    adjustmentDetailDto.getLotAtt11() + adjustmentDetailDto.getSKU() + adjustmentDetailDto.getLotAtt08());
        }
        for (WarehouseGoodsOperateLog availableLogicalGood : availableLogicalGoods) {
            if (adjustmentNum <= 0) {
                break;
            }
            int updateNum = adjustmentNum > availableLogicalGood.getLastStockNum() ?
                    availableLogicalGood.getLastStockNum() : adjustmentNum;

            WarehouseGoodsOperateLog warehouseGoodsOperateLog = new WarehouseGoodsOperateLog();
            warehouseGoodsOperateLog.setWarehouseGoodsOperateLogId(availableLogicalGood.getWarehouseGoodsOperateLogId());
            warehouseGoodsOperateLog.setLastStockNum(availableLogicalGood.getLastStockNum() - updateNum);
            warehouseGoodsOperateLog.setModTime(System.currentTimeMillis());
            warehouseGoodsOperateLog.setUpdater(1);
            warehouseGoodsOperateLog.setIsUse(warehouseGoodsOperateLog.getLastStockNum() == 0 ? 1 : 0);
            warehouseGoodsOperateLog.setComments("盘亏单单号" + inventoryAdjustmentDto.getADJNo());
            logger.info("盘亏情况更新ERP库存扣减log信息 单号:{},updateNum:{},warehouseGoodsOperateLog:{}" ,
                    inventoryAdjustmentDto.getADJNo(), updateNum, JSON.toJSONString(warehouseGoodsOperateLog));
            warehouseGoodsOperateLogMapper.updateAvailableLogicalGood(warehouseGoodsOperateLog);

            availableLogicalGood.setNum(- updateNum);
            availableLogicalGood.setLastStockNum(0);
            availableLogicalGood.setLogType(1);
            availableLogicalGood.setOperateType(StockOperateTypeConst.SURPLUS_WAREHOUSE_OUT);
            availableLogicalGood.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(adjustmentDetailDto.getLotAtt08()));
            availableLogicalGood.setTagSources(StringUtil.isBlank(adjustmentDetailDto.getLotAtt10()) ?
                    null : adjustmentDetailDto.getLotAtt10());
            availableLogicalGood.setIsEnable(1);
            availableLogicalGood.setIsUse(0);
            availableLogicalGood.setRelatedId(inventoryAdjustmentDetailIds.get(index));
            availableLogicalGood.setComments("盘亏单单号" + inventoryAdjustmentDto.getADJNo());
            availableLogicalGood.setUpdater(1);
            availableLogicalGood.setAddTime(System.currentTimeMillis());
            availableLogicalGood.setModTime(System.currentTimeMillis());

            // VDERP-6942 专项发货SKU回传需关联VP单号，默认传*，WMS通过LotAtt07字段回传
            logger.info("专项发货SKU回传需关联VP单号：{}",adjustmentDetailDto.getLotAtt07());
            availableLogicalGood.setDedicatedBuyorderNo(adjustmentDetailDto.getLotAtt07());

            logger.info("盘亏情况新增ERP库存新增log信息 单号:{},updateNum:{},availableLogicalGood:{}" ,
                    inventoryAdjustmentDto.getADJNo(), updateNum, JSON.toJSONString(availableLogicalGood));
            warehouseGoodsOperateLogService.insertSelective(availableLogicalGood);
            adjustmentNum -= updateNum;
        }
    }

    /**
     * 库存调整盘盈情况ERP log维护
     *
     * @param inventoryAdjustmentDto
     * @param inventoryAdjustmentDetailIds
     * @param index
     * @param adjustmentDetailDto
     */
    private void saveAddWarehoseLoginfo(InventoryAdjustmentDto inventoryAdjustmentDto,
                                        List<Integer> inventoryAdjustmentDetailIds, int index, InventoryAdjustmentDetailDto adjustmentDetailDto) {
        logger.info("库存调整单 盘盈  START 单号:{}",inventoryAdjustmentDto.getADJNo());
        WarehouseGoodsOperateLog warehouseGoodsOperateLog = new WarehouseGoodsOperateLog();
        Integer barcodeId = barcodeMapper.getBarcodeIdByBarcode(adjustmentDetailDto.getLotAtt11());
        if(barcodeId == null){
            //新增贝登条码
            Barcode barcode = new Barcode();
            barcode.setBarcode(adjustmentDetailDto.getLotAtt11());
            //采购
            barcode.setType(0);
            barcode.setDetailGoodsId(inventoryAdjustmentDetailIds.get(index));
            barcode.setGoodsId(Integer.valueOf(adjustmentDetailDto.getSKU().substring(1)));
            //有效
            barcode.setIsEnable(1);
            barcode.setAddTime(System.currentTimeMillis());
            barcode.setModTime(System.currentTimeMillis());
            barcodeMapper.insertSelective(barcode);
            barcodeId = barcode.getBarcodeId();
        }
        warehouseGoodsOperateLog.setBarcodeId(barcodeId);
        warehouseGoodsOperateLog.setCompanyId(1);
        warehouseGoodsOperateLog.setOperateType(StockOperateTypeConst.INVENTORY_WAREHOUSE_IN);
        warehouseGoodsOperateLog.setIsEnable(1);
        warehouseGoodsOperateLog.setRelatedId(inventoryAdjustmentDetailIds.get(index));
        warehouseGoodsOperateLog.setGoodsId(Integer.valueOf(adjustmentDetailDto.getSKU().substring(1)));
        warehouseGoodsOperateLog.setNum(adjustmentDetailDto.getToQty().intValue());
        warehouseGoodsOperateLog.setCheckStatusTime(System.currentTimeMillis());
        warehouseGoodsOperateLog.setAddTime(System.currentTimeMillis());
        warehouseGoodsOperateLog.setModTime(System.currentTimeMillis());
        warehouseGoodsOperateLog.setCreator(2);
        warehouseGoodsOperateLog.setUpdater(2);
        warehouseGoodsOperateLog.setProductDate(DateUtil.convertLong(adjustmentDetailDto.getLotAtt01(), DateUtil.DATE_FORMAT));
        warehouseGoodsOperateLog.setExpirationDate(DateUtil.convertLong(adjustmentDetailDto.getLotAtt02(), DateUtil.DATE_FORMAT));
        warehouseGoodsOperateLog.setIsUse(0);
        warehouseGoodsOperateLog.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(adjustmentDetailDto.getLotAtt08()));
        warehouseGoodsOperateLog.setVedengBatchNumer(adjustmentDetailDto.getLotAtt11());
        warehouseGoodsOperateLog.setLastStockNum(adjustmentDetailDto.getToQty().intValue());
        warehouseGoodsOperateLog.setSterilizationBatchNo(adjustmentDetailDto.getLotAtt05());
        warehouseGoodsOperateLog.setComments("盘盈入库盘盈单号" + inventoryAdjustmentDto.getADJNo());
        warehouseGoodsOperateLog.setTagSources(StringUtil.isBlank(adjustmentDetailDto.getLotAtt10()) ?
                null : adjustmentDetailDto.getLotAtt10());
        logger.info("调整单添加ERP库存信息 单号:{},warehouseGoodsOperateLog:{}" ,inventoryAdjustmentDto.getADJNo(),JSON.toJSONString(warehouseGoodsOperateLog));
        warehouseGoodsOperateLogService.insertSelective(warehouseGoodsOperateLog);
        //保存成本价
        warehousesService.savePriceInfoToWareHouseLog(Collections.singletonList(warehouseGoodsOperateLog));
    }

    /**
     * 推送调整单信息到库存服务
     *
     * @param inventoryAdjustmentDto
     */
    private void pushStock(InventoryAdjustmentDto inventoryAdjustmentDto) throws Exception {
        logger.info("推送调整单信息到库存服务 START 单号:{},inventoryAdjustmentDto:{}" ,inventoryAdjustmentDto.getADJNo(),JSON.toJSONString(inventoryAdjustmentDto));

        if (inventoryAdjustmentDto == null || CollectionUtils.isEmpty(inventoryAdjustmentDto.getDetails())) {
            logger.error("下传调整库存信息不全 单号:{}",inventoryAdjustmentDto.getADJNo());
            throw new Exception("下传调整库存信息不全 单号:"+inventoryAdjustmentDto.getADJNo());
        }
        ArrayList<WarehouseDto> warehouseDtos = new ArrayList<>();
        inventoryAdjustmentDto.getDetails().stream().forEach(inventoryAdjustmentDetailDto -> {
            WarehouseDto warehouseDto = new WarehouseDto();
            warehouseDto.setSku(inventoryAdjustmentDetailDto.getSKU());
            warehouseDto.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(inventoryAdjustmentDetailDto.getLotAtt08()));
            warehouseDto.setStockNum(inventoryAdjustmentDetailDto.getToQty().intValue());
            warehouseDtos.add(warehouseDto);
        });

        StockInfoDto stockInfoDto = new StockInfoDto();
        stockInfoDto.setWarehouseStockList(warehouseDtos);
        stockInfoDto.setRelatedNo(inventoryAdjustmentDto.getADJNo());
        logger.info("stock库存修改 START 单号:{},stockInfoDto:{}",inventoryAdjustmentDto.getADJNo(),JSON.toJSONString(stockInfoDto));
        warehouseStockService.updateStockInfo(stockInfoDto);
        logger.info("stock库存修改 END  SUCCESS 单号:{}",inventoryAdjustmentDto.getADJNo());
    }

    /**
     * 调整ERP库存占用信息
     *
     * @param inventoryAdjustmentDto
     * @param inventoryAdjustmentDetailIds
     */
    private void pushLogicalOrderGoods(InventoryAdjustmentDto inventoryAdjustmentDto, List<Integer> inventoryAdjustmentDetailIds) throws Exception {
        List<InventoryAdjustmentDetailDto> inventoryAdjustmentDetails = inventoryAdjustmentDto.getDetails();
        if (CollectionUtils.isEmpty(inventoryAdjustmentDetails)) {
            logger.error("调整ERP库存占用信息 详情为空 error 单号:{}" ,inventoryAdjustmentDto.getADJNo() );
            throw new Exception("调整ERP库存占用信息 error 详情为空 单号:" +inventoryAdjustmentDto.getADJNo());
        }

        for (int index = 0; index < inventoryAdjustmentDetails.size(); index++) {
            WmsLogicalOrdergoods wmsLogicalOrdergoods = new WmsLogicalOrdergoods();
            wmsLogicalOrdergoods.setRelatedId(inventoryAdjustmentDetailIds.get(index));
            wmsLogicalOrdergoods.setOperateType(WmsLogicalOperateTypeEnum.INVENTORY_ADJUSTMENT.getOperateTypeCode());
            wmsLogicalOrdergoods.setSku(inventoryAdjustmentDetails.get(index).getSKU());
            wmsLogicalOrdergoods.setGoodsId(goodsMapper.getGoodsIdBySku(inventoryAdjustmentDetails.get(index).getSKU()));
            wmsLogicalOrdergoods.setNum(inventoryAdjustmentDetails.get(index).getToQty().intValue());
            wmsLogicalOrdergoods.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(inventoryAdjustmentDetails.get(index).getLotAtt08()));
            wmsLogicalOrdergoods.setOccupyNum(0);
            wmsLogicalOrdergoods.setDeliveryNum(inventoryAdjustmentDetails.get(index).getToQty().compareTo(BigDecimal.ZERO) == 1 ?
                    0 : inventoryAdjustmentDetails.get(index).getToQty().intValue());
            wmsLogicalOrdergoods.setArrivalNum(inventoryAdjustmentDetails.get(index).getToQty().compareTo(BigDecimal.ZERO) == 1 ?
                    inventoryAdjustmentDetails.get(index).getToQty().intValue() : 0);
            wmsLogicalOrdergoods.setIsDelete(0);
            wmsLogicalOrdergoods.setAddTime(new Date());
            wmsLogicalOrdergoods.setCreator(1);
            wmsLogicalOrdergoodsMapper.insertSelective(wmsLogicalOrdergoods);
        }
    }

    /**
     * 维护库存调整单信息
     *
     * @param inventoryAdjustmentDto
     * @return
     */
    private InventoryAdjustmentPo pushInventoryAdjustment(InventoryAdjustmentDto inventoryAdjustmentDto) throws Exception {
        if (inventoryAdjustmentDto == null) {
            logger.error("WMS调整单信息为空 单号:{}",inventoryAdjustmentDto.getADJNo());
            throw new Exception("WMS调整单信息为空 单号:"+inventoryAdjustmentDto.getADJNo());
        }
        InventoryAdjustmentPo inventoryAdjustmentPo = new InventoryAdjustmentPo();
        inventoryAdjustmentPo.setInventoryAdjustmentNo(inventoryAdjustmentDto.getADJNo());
        inventoryAdjustmentPo.setType(InventoryAdjustmentTypeEnum.getTypeByCode(inventoryAdjustmentDto.getAdjType()));
        inventoryAdjustmentPo.setStatus(InventoryAdjustmentStatusEnum.APPROVED.getStatus());
        inventoryAdjustmentPo.setCustomer(inventoryAdjustmentDto.getCustomerID());
        inventoryAdjustmentPo.setReason(inventoryAdjustmentDto.getReason());
        inventoryAdjustmentPo.setCreatTime(DateUtil.convertLong(inventoryAdjustmentDto.getAdjCreationTime(), DateUtil.TIME_FORMAT));
        inventoryAdjustmentPo.setAddTime(System.currentTimeMillis());
        inventoryAdjustmentPo.setCreator(1);
        inventoryAdjustmentMapper.insertInventoryAdjustment(inventoryAdjustmentPo);
        return inventoryAdjustmentPo;
    }

    /**
     * 维护库存调整详情以及ERP库存占用信息
     *
     * @param inventoryAdjustmentDto
     * @param inventoryAdjustmentPo
     */
    private List<Integer> pushInventoryAdjustmentDetail(InventoryAdjustmentDto inventoryAdjustmentDto, InventoryAdjustmentPo inventoryAdjustmentPo) throws Exception {
        ArrayList<Integer> inventoryAdjustmentDetailIds = new ArrayList<>();
        List<InventoryAdjustmentDetailDto> inventoryAdjustmentDetails = inventoryAdjustmentDto.getDetails();
        if (CollectionUtils.isEmpty(inventoryAdjustmentDetails)) {
            logger.error("维护库存调整详情以及ERP库存占用信息 详情为空 单号:{}",inventoryAdjustmentDto.getADJNo() );
            throw new Exception("维护库存调整详情以及ERP库存占用信息 详情为空 单号:"+ inventoryAdjustmentDto.getADJNo());
        }

        inventoryAdjustmentDetails.forEach(inventoryAdjustmentDetailDto -> {
            InventoryAdjustmentDetailPo inventoryAdjustmentDetailPo = new InventoryAdjustmentDetailPo();
            inventoryAdjustmentDetailPo.setInventoryAdjustmentId(inventoryAdjustmentPo.getInventoryAdjustmentId());
            inventoryAdjustmentDetailPo.setAdjustmentNo(inventoryAdjustmentDetailDto.getADJLineNo());
            inventoryAdjustmentDetailPo.setSkuNo(inventoryAdjustmentDetailDto.getSKU());
            inventoryAdjustmentDetailPo.setGoodsName(goodsMapper.getGoodNameBySkuNo(inventoryAdjustmentDetailDto.getSKU()));
            //该SKU此次贝登批次码的入库均价
            WarehouseGoodsOperateLog warehouseGoodsOperateLogForQuery = new WarehouseGoodsOperateLog();
            warehouseGoodsOperateLogForQuery.setVedengBatchNumer(inventoryAdjustmentDetailDto.getLotAtt11());
            warehouseGoodsOperateLogForQuery.setGoodsId(goodsMapper.getGoodsIdBySku(inventoryAdjustmentDetailDto.getSKU()));
            List<WarehouseGoodsOperateLog> warehouseGoodsOperateLogs = warehouseGoodsOperateLogMapper.
                    getWarehouseGoodsOperateLogByVedengBatchNoAndSku(warehouseGoodsOperateLogForQuery);
            if (CollectionUtils.isNotEmpty(warehouseGoodsOperateLogs)) {
                BigDecimal totalPrice = BigDecimal.ZERO;
                Integer totalNum = 0;
                for (WarehouseGoodsOperateLog warehouseGoodsOperateLog : warehouseGoodsOperateLogs) {
                    totalPrice = totalPrice.add(warehouseGoodsOperateLog.getCostPrice().multiply(new BigDecimal(warehouseGoodsOperateLog.getNum())));
                    totalNum += warehouseGoodsOperateLog.getNum();
                }
                if (totalNum != 0){
                    inventoryAdjustmentDetailPo.setPrice(totalPrice.divide(new BigDecimal(totalNum), 2, BigDecimal.ROUND_HALF_UP));
                    inventoryAdjustmentDetailPo.setTotalPrice(inventoryAdjustmentDetailPo.getPrice().multiply(new BigDecimal(inventoryAdjustmentDetailDto.getToQty().intValue())));
                }
            }
            inventoryAdjustmentDetailPo.setNum(inventoryAdjustmentDetailDto.getToQty());
            inventoryAdjustmentDetailPo.setOrderNo(inventoryAdjustmentDetailDto.getLotAtt10());
            inventoryAdjustmentDetailPo.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(inventoryAdjustmentDetailDto.getLotAtt08()));
            inventoryAdjustmentDetailPo.setProductionTime(DateUtil.convertLong(inventoryAdjustmentDetailDto.getLotAtt01(), DateUtil.DATE_FORMAT));
            inventoryAdjustmentDetailPo.setValidUntilTime(DateUtil.convertLong(inventoryAdjustmentDetailDto.getLotAtt02(), DateUtil.DATE_FORMAT));
            inventoryAdjustmentDetailPo.setWarehousingTime(DateUtil.convertLong(inventoryAdjustmentDetailDto.getLotAtt03(), DateUtil.DATE_FORMAT));
            inventoryAdjustmentDetailPo.setManufacturerBatchNo(inventoryAdjustmentDetailDto.getLotAtt04());
            inventoryAdjustmentDetailPo.setSterilizationBatchNo(inventoryAdjustmentDetailDto.getLotAtt05());
            inventoryAdjustmentDetailPo.setRegistrationNumber(inventoryAdjustmentDetailDto.getLotAtt06());
            inventoryAdjustmentDetailPo.setVedengBatchNumer(inventoryAdjustmentDetailDto.getLotAtt11());
            inventoryAdjustmentDetailPo.setAddTime(System.currentTimeMillis());
            inventoryAdjustmentDetailPo.setUpdateTime(System.currentTimeMillis());
            inventoryAdjustmentDetailPo.setCreator(1);
            inventoryAdjustmentDetailPo.setUpdater(1);
            inventoryAdjustmentMapper.insertInventoryAdjustmentDetail(inventoryAdjustmentDetailPo);
            inventoryAdjustmentDetailIds.add(inventoryAdjustmentDetailPo.getInventoryAdjustmentDetailId());
        });
        return inventoryAdjustmentDetailIds;
    }
}
