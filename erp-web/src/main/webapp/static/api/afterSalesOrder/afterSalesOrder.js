function afterSaleRefundInfo(param) {
    return axios({
        url: '/afterSalesOrder/refundDetail.do',
        method: 'post',
        params: param
    })
}

function updateRefundInfo(param) {
    return axios({
        url: '/afterSalesOrder/updateRefundInfo.do',
        method: 'post',
        data: param
    })
}

function getCustomerBankAccount(param) {
    return axios({
        url: '/afterSalesOrder/getCustomerBankAccount.do',
        method: 'post',
        params: param
    })
}
function getCustomerBankAccountByBankAccount(param) {
    return axios({
        url: '/afterSalesOrder/getCustomerBankAccountByBankAccount.do',
        method: 'post',
        params: param
    })
}

function updateLastUseTime(param) {
    return axios({
        url: '/afterSalesOrder/updateLastUseTime.do',
        method: 'post',
        params: param
    })
}

// 销售售后：退货退款 新增付款申请
function createRefundApply(param) {
    return axios({
        url: '/afterSalesOrder/createRefundApply.do',
        method: 'post',
        data: param
    })
}

// 判断是否可以原路退回
function isRefundableToOrigin(param) {
    return axios({
        url: '/afterSalesOrder/isRefundableToOrigin.do',
        method: 'post',
        params: param
    })
}