<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>销售订单列表</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="R8XBgDSwsUc" datasource="erp-datasourcetarget" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
<div id="appendHead">
    <style>
        .COLOR_RISK{
            background-image: url(http://erp.ivedeng.com/static/images/risk.png);
            width: 28px;
            height: 24px;
            left:0;
            position: absolute;
        }
        .rowButtons{
            min-width: 80px;
        }

    </style>
</div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">
            <!--        <li class="layui-this"><a href="/ezadmin/list/list-R8XBgDSwsUc">订单列表</a></li>-->
            <!--        <li><a href="/ezadmin/list/list-Jb8cQU_twaQ">售后列表</a></li>-->

        </ul>
    </div>
    <form class="layui-form" id="search">
        <div class=" layui-inline "><label class="layui-form-label">订单号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SALEORDER_NO" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        
        <div class=" layui-inline ">
            <label class="layui-form-label">客户名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_NAME" placeholder="模糊搜索" style="" alias="a" jdbctype="" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">订单状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="SUB_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;待客户确认&quot;,&quot;K&quot;:&quot;8&quot;},{&quot;V&quot;:&quot;待确认&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;待收款&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待发货&quot;,&quot;K&quot;:&quot;3&quot;},{&quot;V&quot;:&quot;待收货&quot;,&quot;K&quot;:&quot;4&quot;},{&quot;V&quot;:&quot;待开票&quot;,&quot;K&quot;:&quot;5&quot;},{&quot;V&quot;:&quot;已完结&quot;,&quot;K&quot;:&quot;6&quot;},{&quot;V&quot;:&quot;已关闭&quot;,&quot;K&quot;:&quot;7&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="VERIFY_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;待审核&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">合并日期搜索</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="search-date" name="ADD_TIME,VALID_TIME,PAYMENT_TIME,DELIVERY_TIME,ARRIVAL_TIME" placeholder="" style="" alias="" jdbctype="" data="" datatype="" oper="between"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">下单渠道</label>
            <div class="layui-input-inline">
                <select  class=" layui-input list-search-item " name="ORDER_TYPE2" placeholder="全部" style="" alias="a.ORDER_TYPE" jdbctype="" data="[{&quot;V&quot;:&quot;线上&quot;,&quot;K&quot;:&quot;1,5,7&quot;},{&quot;V&quot;:&quot;线下&quot;,&quot;K&quot;:&quot;0,8,9&quot;}]" datatype="JSON" oper="IN"></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">生效状态</label>
            <div class="layui-input-inline">
                <select  class=" layui-input list-search-item " name="VALID_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;未生效&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已生效&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper="IN"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">归属销售</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="CURRENT_USER_ID" placeholder="" style="" alias="b" jdbctype="VARCHAR" data="${session.EZ_SESSION_MY_USER_MAP_KEY}" datatype="KVSQLCACHE" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">收款状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="PAYMENT_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;未收款&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收款&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收款&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">发货状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="DELIVERY_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未发货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分发货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部发货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">收货状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="ARRIVAL_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未收货&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分收货&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部收货&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">开票状态</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="xm-select" name="INVOICE_STATUS" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;未开票&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分开票&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部开票&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">销售部门</label>
            <div class="layui-input-inline">
                <object type="search-org" class=" layui-input list-search-item " name="CURRENT_ORG_ID" placeholder="" style="" alias="b" jdbctype="NUMBER" data="" datatype="" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">锁定状态</label>
            <div class="layui-input-inline"><select   class=" layui-input list-search-item " name="LOCKED_STATUS" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未锁定&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已锁定&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">售后状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="AFTER_SALES_STATUS" placeholder="" style="" alias="ifnull(b.AFTER_SALES_STATUS,0)" jdbctype="" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;售后中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;售后完结&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">客户性质</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="CUSTOMER_NATURE" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;分销&quot;,&quot;K&quot;:&quot;465&quot;},{&quot;V&quot;:&quot;终端&quot;,&quot;K&quot;:&quot;466&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">直发</label>
            <div class="layui-input-inline">
                <select  class=" layui-input list-search-item " name="DELIVERY_DIRECT" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">提前采购</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="HAVE_ADVANCE_PURCHASE" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;有&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">有沟通</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="HAVE_COMMUNICATE" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;无&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;有&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">计入业绩</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="IS_SALES_PERFORMANCE" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未计入&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已计入&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">合同回传</label>
            <div class="layui-input-inline">
                <select   class=" layui-input list-search-item " name="CONTRACT_STATUS" placeholder="" style="" alias="CASE
		WHEN b.CONTRACT_VERIFY_STATUS=5 or b.CONTRACT_VERIFY_STATUS is null THEN
		'0' ELSE '1'
	END" jdbctype="" data="[{&quot;V&quot;:&quot;未回传&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已回传&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">送货单</label>
            <div class="layui-input-inline"><select   class=" layui-input list-search-item " name="IS_DELIVERYORDER_RETURN" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未回传&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;已回传&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">合同审核状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="CONTRACT_VERIFY_STATUS" placeholder="" style="" alias="b" jdbctype="" datatype="JSON" data='[{"V":"待审核","K":"4"},{"V":"审核中","K":"0"},{"V":"审核通过","K":"1"},{"V":"审核不通过","K":"2"}]' datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">品牌</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MERGE_BRAND" placeholder="" style="" alias="b.SKU_BRAND_NAME_MODEL" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">产品名称</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MERGE_NAME" placeholder="" style="" alias="b.SKU_BRAND_NAME_MODEL" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">型号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MERGE_MODEL" placeholder="" style="" alias="b.SKU_BRAND_NAME_MODEL" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="MERGE_SKU" placeholder="" style="" alias="b.SKU_BRAND_NAME_MODEL" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">是否申请开票</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="INVOICE_APPLY_FLAY" placeholder="" style="" alias="IFNULL(b.INVOICE_APPLY_FLAY,'0')" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">联系人电话</label>
            <div class="layui-input-inline">
                <input type="text" class=" layui-input list-search-item " name="TRADER_CONTACT_TELEPHONE" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">联系人手机号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_CONTACT_MOBILE" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">联系人</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="TRADER_CONTACT_NAME" placeholder="" style="" alias="a" jdbctype="VARCHAR" data="" datatype="" oper="LIKE">
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">账期未还</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="NOPAYBACK_AMOUNT" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;0.00&quot;}]" datatype="JSON" oper="NE"></select>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">资质状态</label>
            <div class="layui-input-inline"><select class=" layui-input list-search-item " name="TRADER_STATUS" placeholder="全部" style="" alias="t" jdbctype="" data="[{&quot;V&quot;:&quot;未认证&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <!--VDERP-12290【审计】订单确认、签回单功能修改（隐藏和取消）-->
<!--        <div class=" layui-inline "><label class="layui-form-label">订单确认</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="CONFIRM_STATUS" placeholder="" style="" alias="if((a.CONFIRM_STATUS is null or a.CONFIRM_STATUS =0)     and (a.status=1 or a.status=2),0,1)" jdbctype="" data="[{&quot;V&quot;:&quot;无需确认&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;需要确认&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>-->

        <div class=" layui-inline "><label class="layui-form-label">收货时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="hidden" name="ARRIVAL_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.ARRIVAL_TIME= 0,         NULL,         a.ARRIVAL_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">开票时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="INVOICE_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.INVOICE_TIME= 0,         NULL,         a.INVOICE_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">发货时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="hidden" name="DELIVERY_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.DELIVERY_TIME= 0,         NULL,         a.DELIVERY_TIME) / 1000,        '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">付款时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="PAYMENT_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.PAYMENT_TIME= 0,         NULL,         a.PAYMENT_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">生效时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="hidden" name="VALID_TIME" placeholder="" style="" alias="FROM_UNIXTIME( IF ( a.VALID_TIME= 0,         NULL,         a.VALID_TIME) / 1000,         '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>
        <div class=" layui-inline ">
            <label class="layui-form-label">创建时间</label>
            <div class="layui-input-inline"><object class=" layui-input list-search-item " type="hidden" name="ADD_TIME" placeholder="请选择日期" style="" alias="FROM_UNIXTIME( IF ( a.ADD_TIME= 0,         NULL,         a.ADD_TIME) / 1000,        '%Y-%m-%d %H:%i:%s' )" jdbctype="DATETIME" data="" datatype="" oper="BETWEEN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">客户在线确认</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="ONLINE_RECEIPT_STATUS" placeholder="请选择" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;全部确认&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;部分确认&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;未确认&quot;,&quot;K&quot;:&quot;0&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>

        <div class=" layui-inline "><label class="layui-form-label">是否在线催办</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="SALEORDER_URAGE" placeholder="" style="" alias="b" jdbctype="" data="[{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;1&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">确认单状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="CONFIRMATION_FORM_UPLOAD" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;未上传&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;部分上传&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;全部上传&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">确认单审核状态</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="xm-select" name="CONFIRMATION_FORM_AUDIT" placeholder="" style="" alias="a" jdbctype="" data="[{&quot;V&quot;:&quot;待提交审核&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper="IN"></object>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">是否特麦帮</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " name="IS_SPECIAL" placeholder="" style="" alias="if(TSS.SPECIAL_SALES_ID is null, '否', '是')" jdbctype="" data="[{&quot;V&quot;:&quot;是&quot;,&quot;K&quot;:&quot;是&quot;},{&quot;V&quot;:&quot;否&quot;,&quot;K&quot;:&quot;否&quot;}]" datatype="JSON" oper=""></select>
            </div>
        </div>
        <div class=" layui-inline "><label class="layui-form-label">终端维护</label>
            <div class="layui-input-inline">
                <select  class=" layui-input list-search-item "  name="HAS_TERMINAL" placeholder="" style="" alias="" jdbctype="BODY" datatype="JSON" data='[{"V":"全部","K":""},{"V":"已维护","K":"1"},{"V":"未维护","K":"0"}]'  ></select>
            </div>
        </div>
    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="SALEORDER_NO" name="SALEORDER_NO" body="td-spanlink" url="/orderstream/saleorder/detail.do?saleOrderId=${SALEORDER_ID}&amp;scene=0" opentype="PARENT" windowname="${SALEORDER_NO}" datatype="" data="" style="min-width:200px;word-break: break-all;position: sticky;" head="18">订单号</th>
			
            <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="${TRADER_NAME}" datatype="" data="" style="min-width:200px;word-break: break-all;" head="18">客户名称</th>
            <th item_name="CURRENT_USER_ID" name="CURRENT_USER_ID" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="SELECT A.USER_ID K,A.USERNAME V  FROM T_USER A where  IS_DISABLED=0  AND COMPANY_ID=1" style="width:110px" head="th">归属销售</th>
            <th item_name="REAL_TOTAL_AMOUNT" name="REAL_TOTAL_AMOUNT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" jdbctype="NUMBER">订单实际金额</th>
            <th item_name="REAL_PAY_AMOUNT" name="REAL_PAY_AMOUNT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" jdbctype="NUMBER">客户实付金额</th>
            <th item_name="NOPAYBACK_AMOUNT" name="NOPAYBACK_AMOUNT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="text-align:right" head="18" jdbctype="NUMBER">未还账期</th>
            <th item_name="PAYMENT_STATUS" name="PAYMENT_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">收款状态</th>
            <th item_name="PURCHASE_STATUS" name="PURCHASE_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">采购状态</th>
            <th item_name="DELIVERY_STATUS" name="DELIVERY_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">发货状态</th>
            <th item_name="ARRIVAL_STATUS" name="ARRIVAL_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">收货状态</th>
            <th item_name="INVOICE_APPLY_FLAY" name="INVOICE_APPLY_FLAY" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">是否申请开票</th>
            <th item_name="INVOICE_STATUS" name="INVOICE_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">开票状态</th>
            <th item_name="AFTER_SALES_STATUS" name="AFTER_SALES_STATUS" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">售后状态</th>
            <th item_name="DELIVERY_DIRECT" name="DELIVERY_DIRECT" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">直发</th>
            <th item_name="CURRENT_ORG_ID" name="CURRENT_ORG_ID" body="td-select" url="" opentype="MODEL" windowname="" datatype="KVSQLCACHE" data="select ORG_ID K,ORG_NAME V from T_ORGANIZATION where IS_DELETED = 0" style="" head="18">销售部门</th>
            <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户性质</th>
            <th item_name="IS_DELIVERYORDER_RETURN" name="IS_DELIVERYORDER_RETURN" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">送货单回传</th>
            <th item_name="CONTRACT_STATUS" name="CONTRACT_STATUS" body="td-select" url="" opentype="MODEL" windowname=""   data='yesno' style="" head="18">合同回传</th>
            <th item_name="IS_SPECIAL" name="IS_SPECIAL" body="td-text" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">是否特麦帮</th>

            <th item_name="SALE_ORDER_TERMINAL_CUSTOMER_ID" name="SALE_ORDER_TERMINAL_CUSTOMER_ID" body="td-text" url="" style="display: none">隐藏</th>
            <th item_name="SALE_ORDER_TERMINAL_ID" name="SALE_ORDER_TERMINAL_ID" body="td-text" url="" style="display: none">隐藏</th>
            <th item_name="VALIDSTATUS" name="validStatus" body="td-text" url="" style="display: none">隐藏</th>
            <th type="rowbutton" id="rowbutton">

                <button name="CAN_HANDLE" itemname="REMARK_CUSTOMER" id="remarkCustomer" type="single" opentype="MODEL"
                         url="/order/terminal/customer/page.do?hasCustomerInfo=0&businessId=${SALEORDER_ID}"
                         windowname="备注客户" name="MODEL" area="1000px,600px">备注客户
                </button>
                <button name="CAN_HANDLE" itemname="MODIFY_CUSTOMER" id="modifyCustomer" type="single" opentype="MODEL"
                         url="/order/terminal/customer/page.do?hasCustomerInfo=1&businessId=${SALEORDER_ID}"
                         windowname="修改客户" name="MODEL" area="1000px,600px">修改客户
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by a.SALEORDER_ID DESC" groupby="">StringBuilder sql=new StringBuilder("select ");

if(isNotBlank("EZ_SUM_FLAG")){
sql.append(" ifnull(sum(a.REAL_PAY_AMOUNT),0)  REAL_PAY_AMOUNT,ifnull(sum(a.REAL_TOTAL_AMOUNT) ,0)  REAL_TOTAL_AMOUNT,count(1)  COUNT ");
}else{

	sql.append("a.SALEORDER_NO  ,a.ORIGIN_BUYORDER_NO,
CASE

		WHEN b.SUB_STATUS = 0 THEN
		'orangecircle'
		WHEN b.SUB_STATUS = 1 THEN
		'yellowcircle'
		WHEN b.SUB_STATUS = 2
		OR b.SUB_STATUS = 3
		OR b.SUB_STATUS = 4
		OR b.SUB_STATUS = 5 THEN
			'greencircle'
			WHEN a.STATUS = 2 THEN
			'bluecircle'
			WHEN a.STATUS = 3 THEN
			'greycircle'
                        WHEN a.STATUS = 4 THEN
			'purplecircle'
		END COLOR_CIRCLE,
	a.SALEORDER_ID,
CASE a.IS_NEW
WHEN 0 THEN '否'
WHEN 1 THEN '是'
END IS_NEW,
	a.VALID_STATUS * 1 validStatus,
	a.STATUS * 1 STATUS,
	a.LOCKED_STATUS * 1 LOCKED_STATUS,
	a.TRADER_CONTACT_NAME,
	a.TRADER_CONTACT_MOBILE,
	a.TRADER_CONTACT_TELEPHONE,
	a.REAL_PAY_AMOUNT,
	a.COUPONMONEY,
    ifnull(a.REAL_TOTAL_AMOUNT ,0.00) REAL_TOTAL_AMOUNT ,
    ifnull(st.ID ,0) SALE_ORDER_TERMINAL_ID ,
    ifnull(TC.ID ,0) SALE_ORDER_TERMINAL_CUSTOMER_ID ,
	b.CURRENT_ORG_ID CURRENT_ORG_ID,
	a.HAVE_COMMUNICATE * 1 HAVE_COMMUNICATE,
	a.IS_SALES_PERFORMANCE * 1 IS_SALES_PERFORMANCE,
	b.LEFT_AMOUNT_PERIOD NOPAYBACK_AMOUNT,
CASE
		b.LEFT_AMOUNT_PERIOD
		WHEN 0.00 THEN
		'0' ELSE '1'
	END NOPAYBACK_AMOUNT_STATUS,
	a.ORDER_TYPE * 1 ORDER_TYPE,
a.ORDER_TYPE * 1 ORDER_TYPE2,
	a.TRADER_NAME,
	a.TRADER_ID,
	a.TOTAL_AMOUNT,
CASE
		a.PAYMENT_STATUS
		WHEN 0 THEN
		'未收款'
		WHEN 1 THEN
		'部分收款'
		WHEN 2 THEN
		'全部收款'
	END PAYMENT_STATUS,
CASE
		a.PURCHASE_STATUS
		WHEN 0 THEN
		'未采购'
		WHEN 1 THEN
		'部分采购'
		WHEN 2 THEN
		'已采购'
	END PURCHASE_STATUS,
CASE
		a.INVOICE_STATUS
		WHEN 0 THEN
		'未开票'
		WHEN 1 THEN
		'部分开票'
		WHEN 2 THEN
		'全部开票'
	END INVOICE_STATUS,
CASE
		a.DELIVERY_STATUS
		WHEN 0 THEN
		'未发货'
		WHEN 1 THEN
		'部分发货'
		WHEN 2 THEN
		'全部发货'
	END DELIVERY_STATUS,
CASE
		a.ARRIVAL_STATUS
		WHEN 0 THEN
		'未收货'
		WHEN 1 THEN
		'部分收货'
		WHEN 2 THEN
		'全部收货'
	END ARRIVAL_STATUS,
CASE
		a.CUSTOMER_NATURE
		WHEN 465 THEN
		'分销'
		WHEN 466 THEN
		'终端'
	END CUSTOMER_NATURE,
CASE
		a.DELIVERY_DIRECT
		WHEN 0 THEN
		'否'
		WHEN 1 THEN
		'是'
	END DELIVERY_DIRECT,
CASE
		b.AFTER_SALES_STATUS
		WHEN 0 THEN
		'无'
		WHEN 1 THEN
		'售后中'
		WHEN 2 THEN
		'售后完结'
        WHEN 3 THEN
		'售后关闭' ELSE '无'
	END AFTER_SALES_STATUS,
	IFNULL( b.COMMUNICATE_NUM, 0 ) COMMUNICATE_NUM,
CASE
		a.HAVE_ADVANCE_PURCHASE
		WHEN 0 THEN
		'否'
		WHEN 1 THEN
		'有'
	END HAVE_ADVANCE_PURCHASE,
CASE
		a.IS_CONTRACT_RETURN
		WHEN 0 THEN
		'否'
		WHEN 1 THEN
		'是'
	END IS_CONTRACT_RETURN,
CASE

		WHEN b.CONTRACT_VERIFY_STATUS=5 or b.CONTRACT_VERIFY_STATUS is null THEN
		'0' ELSE '1'
	END CONTRACT_STATUS,
CASE
		a.IS_DELIVERYORDER_RETURN
		WHEN 0 THEN
		'否'
		WHEN 1 THEN
		'是'
	END IS_DELIVERYORDER_RETURN,
CASE
		b.INVOICE_APPLY_FLAY
		WHEN 0 THEN
		'否'
		WHEN 1 THEN
		'是' ELSE '否'
	END INVOICE_APPLY_FLAY,
CASE
		b.VERIFY_STATUS
		WHEN 0 THEN
		'审核中'
		WHEN 1 THEN
		'审核通过'
		WHEN 2 THEN
		'审核不通过'
		WHEN 3 THEN
		'待审核' ELSE '待审核'
	END VERIFY_STATUS,
   b.CONTRACT_VERIFY_STATUS ,
	b.CURRENT_USER_ID CURRENT_USER_ID,
        t.TRADER_STATUS,
        CONCAT(COALESCE ( v.STATUS, 3 ),',',v.VERIFY_USERNAME ) VERIFY_USERNAME,
        FROM_UNIXTIME( IF ( a.ADD_TIME= 0,         NULL,         a.ADD_TIME) / 1000,         '%Y-%m' ) ADD_TIME_STR,
    if(TSS.SPECIAL_SALES_ID is null, '否', '是') as IS_SPECIAL,
	b.SUB_STATUS ,case when a.IS_RISK=1 or   a.IS_RISK=2 then 'COLOR_RISK' else '' end COLOR_RISK             ");
}

sql.append("
  from
	T_SALEORDER a
	LEFT JOIN T_SALEORDER_DATA b ON a.SALEORDER_ID = b.SALEORDER_ID
    LEFT JOIN T_TRADER t  ON a.TRADER_ID = t.TRADER_ID
    LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY =a.SALEORDER_ID AND v.RELATE_TABLE = 'T_SALEORDER' and v.VERIFIES_TYPE = 623
    LEFT JOIN T_SPECIAL_SALES TSS ON TSS.RELATE_ID = a.SALEORDER_ID AND TSS.RELATE_TYPE = 3 AND TSS.IS_DELETE = 0
    LEFT JOIN T_SALE_ORDER_TERMINAL st ON a.SALEORDER_ID = st.SALE_ORDER_ID
    LEFT JOIN T_SALE_ORDER_TERMINAL_CUSTOMER TC ON a.SALEORDER_ID = TC.SALE_ORDER_ID
");

sql.append("
WHERE
	1 = 1
	AND a.COMPANY_ID = 1
	AND
		a.`ORDER_TYPE` IN ( 0,1,5,7,8,9)
");
if($("HAS_TERMINAL").equals("0")){
     sql.append ( " and a.CUSTOMER_NATURE = 465 and  a.SALEORDER_ID not in (
        SELECT
                tot.BUSINESS_ID
        FROM
                T_ORDER_TERMINAL tot
        WHERE
                tot.IS_DELETED = 0 AND tot.BUSINESS_TYPE = 0  AND tot.TERMINAL_TRADER_NATURE>0
        GROUP BY
                tot.BUSINESS_ID
    )" );
}

if($("HAS_TERMINAL").equals("1")){
     sql.append ( " and a.CUSTOMER_NATURE = 465 and  a.SALEORDER_ID in (
        SELECT
                tot.BUSINESS_ID
        FROM
                T_ORDER_TERMINAL tot
        WHERE
                tot.IS_DELETED = 0 AND tot.BUSINESS_TYPE = 0 AND tot.TERMINAL_TRADER_NATURE>0
        GROUP BY
                tot.BUSINESS_ID
    )" );
}

if($("ONLINE_RECEIPT_STATUS").equals("1")){
        sql.append ( " and  a.SALEORDER_ID not in (
SELECT
		tcbr.SALEORDER_ID
FROM
		T_CONFIRMATION_BATCHES_RELATION tcbr
WHERE
		tcbr.IS_ENABLE = 0
GROUP BY
		tcbr.SALEORDER_ID
                )" );
}

if($("ONLINE_RECEIPT_STATUS").equals("2")){
    sql.append ( " and  a.ARRIVAL_STATUS = 2" );
    sql.append ( " and  a.SALEORDER_ID not in (
SELECT
		tcbr.SALEORDER_ID
FROM
		T_CONFIRMATION_BATCHES_RELATION tcbr
WHERE
		tcbr.IS_ENABLE = 0
GROUP BY
		tcbr.SALEORDER_ID
                )" );
}

sql.append ( " and (1=2  " );


if ( isNotBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {
sql.append ( " or  b.CURRENT_ORG_ID  in (" );
sql.append ( $$("EZ_SESSION_ORG_IDS_LIST_KEY") );
sql.append (  ") " );
}

if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
sql.append ( " or b.CURRENT_USER_ID in (" );
sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
sql.append (  ") " );
}

if ( isNotBlank("EZ_SESSION_MY_USER_KEY","session")) {
    sql.append ( " or t.TRADER_ID in (SELECT TRADER_ID FROM T_R_SALES_J_TRADER WHERE SALE_USER_ID IN (" );
    sql.append ( $$("EZ_SESSION_MY_USER_KEY") );
    sql.append (  ")) " );
}

if ( isBlank("EZ_SESSION_MY_USER_KEY","session") &amp;&amp; isBlank("EZ_SESSION_ORG_IDS_LIST_KEY","session")) {

}
sql.append (  ") " );





list=search(sql);
return list;</pre> count:
                <pre id="count" class="layui-code">

                </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot">
    <div class=" layui-elem-quote" style="float:right;width:100%;text-align: center;">
        【全部结果 条目：<span id="totalCount"></span> 订单实际金额总计：<span id="totalRealMoney"></span> 客户实付金额总计：<span id="totalPayMoney"></span>】 【本页统计 条目：<span id="pageCount"></span> 订单实际金额总计：<span id="pageRealMoney"></span> 客户实付金额总计：<span id="pagePayMoney"></span>】
    </div>
    <script>

        function afterAllDataLoad(){
            //    $("#totalCount").text($("#totalRecord").val())   ;
            $("#pageCount").text($("input[name=row_data_id]").length)   ;

            $.get("/ezadmin/list/sum-R8XBgDSwsUc?"+getSearchParams(),function(data){
                if(data.code==0){
                    $("#totalRealMoney").text(data.data.REAL_TOTAL_AMOUNT)
                    $("#totalPayMoney").text(data.data.REAL_PAY_AMOUNT)
                    $("#totalCount").text(data.data.COUNT)
                }
            })
//todo 页面计算 实际 实付。
//实际
            var pageRealMoney = 0;
            $("#mytable tbody").eq(0).find("[item_name=REAL_TOTAL_AMOUNT]").each(function(){
                    var num = $(this).text();

                    pageRealMoney = pageRealMoney +toFloat(num);

                }
            );
            $("#pageRealMoney").text(pageRealMoney);
//实付
            var pagePayMoney = 0;
            $("#mytable tbody").eq(0).find("[item_name=REAL_PAY_AMOUNT]").each(function() {
                    var num = $(this).text();
                    pagePayMoney = pagePayMoney +toFloat(num);
                }
            );
            $("#pagePayMoney").text(pagePayMoney);


            //熙成客户
            var traderIdStr ="0";
            //遍历取值
            $("input[name='row_data_hidden_TRADER_ID']").each(function(e){
                var traderId = $(this).val();
                traderIdStr =  traderIdStr + ','+ traderId ;
            })
            $.ajax({
                url: '/orderstream/saleorder/queryTraderGroup.do',
                data:{"traderIds":traderIdStr},
                type:"POST",
                dataType : "json",
                success:function(res){
                    if(res.code==0){
                        var dataMap = res.data;
                        //遍历赋值
                        $("input[name='row_data_hidden_TRADER_ID']").each(function(e){
                            var tarderId = $(this).val();
                            var tarderVale =  dataMap[tarderId];
                            if (tarderVale != undefined && tarderVale != ''){
                                var traderGroupName = tarderVale .traderGroupName;
                                if (traderGroupName != undefined && traderGroupName != ''){
                                    var xichen= "<font color='red'>[熙成客户]</font>";
                                    $(this).parent().next().next().children("a").before(xichen);
                                }
                            }
                        })
                    }
                }
            });


        }




        $(function(){

            $("td[item_name=DELIVERY_STATUS]").each(function(e){
                if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
            });
            $("td[item_name=INVOICE_STATUS]").each(function(e){
                if($(this).text().indexOf("未")>=0){ $(this).css("color","red")}
            });

            $("td[item_name=DELIVERY_DIRECT]").each(function(e){
                if($(this).text().indexOf("是")>=0){ $(this).css("color","red")}
            });

            // $("td[item_name=VALIDSTATUS]").each(function (e) {
            //     if ($(this).find("div").text() == 0) {
            //         // 当前订单未生效，隐藏全部按钮
            //         $(this).next("td").find("button").eq(0).hide();
            //         $(this).next("td").find("button").eq(1).hide();
            //     } else {
            //         // 否则，判断是否已维护过终端信息
            //         if ($(this).prev("td").find("div").text() = 0) {
            //             // 未完善
            //             $(this).next("td").find("button").eq(1).hide();
            //         } else {
            //             $(this).next("td").find("button").eq(0).hide();
            //         }
            //     }
            // });

            $.ajax({
                url: '/system/user/hasPermission.do',
                data: {'uri':'/order/terminal/customer/page.do'},
                type: 'post',
                dataType : "json",
                success: function (res) {
                    if (res.code == 0 && res.data == true) {
                        // 有权限，根据条件隐藏
                        $("td[item_name=SALE_ORDER_TERMINAL_CUSTOMER_ID]").each(function (e) {

                            if ($(this).find("div").text() == 0) {
                                // 未维护过
                                $(this).parent().find('button[item_open_title="修改客户"]').hide();
                            } else {
                                // 已维护
                                $(this).parent().find('button[item_open_title="备注客户"]').hide();
                            }
                        })
                    } else {
                        // 无权限，全部隐藏
                        $("td[item_name=SALE_ORDER_TERMINAL_CUSTOMER_ID]").each(function (e) {
                            $(this).parent().find('button[item_open_title="修改客户"]').hide();
                            $(this).parent().find('button[item_open_title="备注客户"]').hide();
                        })
                        //$("td[item_name=SALE_ORDER_TERMINAL_CUSTOMER_ID]").parents("table").find("th[class='rowButtons fixedCol sorting_disabled']").css("min-width:80px;");
                    }
                }
            });
        });

        //审字标签
        $("input[name='row_data_hidden_VERIFY_USERNAME']").each(function(e){
            var curUser = $("#EZ_SESSION_USER_NAME_KEY").val();
            var verifyUser = $(this).val();

            var varifyUserChar = verifyUser.split(",");
            var flag = false;
            if (varifyUserChar[0] == '0'){
                for (var i = 1; i < varifyUserChar.length; i++) {
                    if (varifyUserChar[i] == curUser) {
                        flag = true;
                        break;
                    }
                }
            }
            if (flag){
                var shen = "<font color='red'>[审]</font>";
                $(this).parent().next().children("a").after(shen);
            }
        })
    </script>
</div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>