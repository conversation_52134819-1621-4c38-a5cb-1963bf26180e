package com.wms.dto;

import lombok.*;

import java.util.Date;


/**
 * @description wms操作人
 * <AUTHOR>
 * @date 2023/12/11 10:40
 **/

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class WmsInOutPersonDto {
    /**
    * id
    */
    private Integer id;

    /**
    * 操作日期
    */
    private Date date;

    /**
    * 人员
    */
    private String name;

    /**
    * 1 收货 2 验收 3 拣货 4 复核
    */
    private Integer type;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 创建者id
    */
    private Integer creator;

    /**
    * 修改者id
    */
    private Integer updater;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者名称
    */
    private String updaterName;
}