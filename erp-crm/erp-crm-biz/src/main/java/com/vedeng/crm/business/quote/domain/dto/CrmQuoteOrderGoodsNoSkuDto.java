package com.vedeng.crm.business.quote.domain.dto;

import com.vedeng.common.core.utils.validator.group.DefaultGroup;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/29
 */
@Data
public class CrmQuoteOrderGoodsNoSkuDto {

    /**
     * 需求主键ID
     */
    private Long quoteorderNeedsId;

    /**
     * 报价单商品行ID，如果为编码时，此字段有值
     */
    private Integer quoteorderGoodsId;

    @NotNull(message = "报价单id不能为空", groups = DefaultGroup.class)
    private Integer quoteorderId;

    /**
     * 产品名称
     */
    @NotNull(message = "产品名称不能为空", groups = DefaultGroup.class)
    private String goodsName;

    /**
     * 品牌名称
     */
    @NotNull(message = "品牌不能为空", groups = DefaultGroup.class)
    private String brandName;

    /**
     * 品牌ID
     */
    @NotNull(message = "品牌不能为空", groups = DefaultGroup.class)
    private Integer brandId;

    /**
     * 商品型号
     */
    @NotNull(message = "商品型号不能为空", groups = DefaultGroup.class)
    private String model;

    /**
     * 产品单位
     */
    @NotNull(message = "产品单位不能为空", groups = DefaultGroup.class)
    private String unitName;


    /**
     * 产品图片
     */
    private String imgUrl;

    /**
     * 产品参数
     */
    private String paramContent;

//    添加自定义商品
//    ALTER TABLE `T_QUOTEORDER_GOODS`
//    ADD COLUMN `BRAND_ID` int(11) unsigned DEFAULT NULL COMMENT '品牌ID',
//    ADD COLUMN `IMG_URL` varchar(100) DEFAULT NULL COMMENT '产品图片',
//    ADD COLUMN `PARAM_CONTENT` varchar(600) DEFAULT NULL COMMENT '产品参数';


}
