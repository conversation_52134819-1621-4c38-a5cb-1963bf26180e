package com.vedeng.customerbillperiod.dto;

import lombok.Data;

import java.util.List;

/**
 * @Author: daniel
 * @Date: 2021/7/30 11 23
 * @Description: 客户账期申请记录查询类
 */
@Data
public class CustomerBillPeriodApplyQueryDto {

    private Integer companyId;

    /**
     * 客户名称关键字
     */
    private String keywordOfCustomerName;

    /**
     * 客户性质
     */
    private Integer customerNature;

    /**
     * 审核状态
     */
    private Integer checkStatus;

    /**
     * 申请人集合
     */
    private List<Integer> creators;

    /**
     * 申请时间
     */
    private Long applyTimeStart;

    private Long applyTimeEnd;

    /**
     * 账期类型
     */
    private Integer billPeriodType;

    /**
     * 当前页
     */
    private Integer pageNum = 1;

    /**
     * 单页条数
     */
    private Integer pageSize = 10;
}
