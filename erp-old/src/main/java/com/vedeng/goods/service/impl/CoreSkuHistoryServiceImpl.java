package com.vedeng.goods.service.impl;

import com.vedeng.goods.dao.CoreSkuHistoryMapper;
import com.vedeng.goods.model.CoreSkuHistory;
import com.vedeng.goods.service.CoreSkuHistoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description com.vedeng.goods.service.impl
 * @Date 2022/3/24 19:02
 */
@Service
public class CoreSkuHistoryServiceImpl implements CoreSkuHistoryService {

    @Resource
    private CoreSkuHistoryMapper coreSkuHistoryMapper;

    @Override
    public Integer insertOrUpdateCoreSkuHistory(CoreSkuHistory coreSkuHistory) {
        // 判断是否已有历史数据了，再决定是新增还是更新
        CoreSkuHistory exit = coreSkuHistoryMapper.selectBySkuId(coreSkuHistory.getSkuId());
        if (Objects.isNull(exit)) {
            return coreSkuHistoryMapper.insertSelective(coreSkuHistory);
        } else {
            coreSkuHistory.setSkuHistoryId(exit.getSkuHistoryId());
            return coreSkuHistoryMapper.updateByPrimaryKeySelective(coreSkuHistory);
        }


    }

    @Override
    public CoreSkuHistory getCoreSkuHistoryBySkuId(Integer skuId) {
        return coreSkuHistoryMapper.selectBySkuId(skuId);
    }
}
