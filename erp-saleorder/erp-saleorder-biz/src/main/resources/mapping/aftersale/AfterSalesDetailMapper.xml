<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.AfterSalesDetailMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity">
    <!--@mbg.generated-->
    <!--@Table T_AFTER_SALES_DETAIL-->
    <id column="AFTER_SALES_DETAIL_ID" jdbcType="INTEGER" property="afterSalesDetailId" />
    <result column="AFTER_SALES_ID" jdbcType="INTEGER" property="afterSalesId" />
    <result column="REASON" jdbcType="INTEGER" property="reason" />
    <result column="COMMENTS" jdbcType="VARCHAR" property="comments" />
    <result column="TRADER_ID" jdbcType="INTEGER" property="traderId" />
    <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId" />
    <result column="TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="traderContactName" />
    <result column="TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="traderContactMobile" />
    <result column="TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone" />
    <result column="REFUND" jdbcType="BOOLEAN" property="refund" />
    <result column="AREA_ID" jdbcType="INTEGER" property="areaId" />
    <result column="ADDRESS_ID" jdbcType="INTEGER" property="addressId" />
    <result column="AREA" jdbcType="VARCHAR" property="area" />
    <result column="ADDRESS" jdbcType="VARCHAR" property="address" />
    <result column="SERVICE_AMOUNT" jdbcType="DECIMAL" property="serviceAmount" />
    <result column="INVOICE_TYPE" jdbcType="INTEGER" property="invoiceType" />
    <result column="IS_SEND_INVOICE" jdbcType="BOOLEAN" property="isSendInvoice" />
    <result column="REFUND_AMOUNT" jdbcType="DECIMAL" property="refundAmount" />
    <result column="REFUND_FEE" jdbcType="DECIMAL" property="refundFee" />
    <result column="REAL_REFUND_AMOUNT" jdbcType="DECIMAL" property="realRefundAmount" />
    <result column="PAYMENT_AMOUNT" jdbcType="DECIMAL" property="paymentAmount" />
    <result column="REFUND_AMOUNT_STATUS" jdbcType="BOOLEAN" property="refundAmountStatus" />
    <result column="SERVICE_AMOUNT_STATUS" jdbcType="BOOLEAN" property="serviceAmountStatus" />
    <result column="INVOICE_STATUS" jdbcType="BOOLEAN" property="invoiceStatus" />
    <result column="INVOICE_TIME" jdbcType="BIGINT" property="invoiceTime" />
    <result column="RECEIVE_INVOICE_STATUS" jdbcType="BOOLEAN" property="receiveInvoiceStatus" />
    <result column="RECEIVE_INVOICE_TIME" jdbcType="BIGINT" property="receiveInvoiceTime" />
    <result column="PAYMENT_STATUS" jdbcType="BOOLEAN" property="paymentStatus" />
    <result column="PAYMENT_TIME" jdbcType="BIGINT" property="paymentTime" />
    <result column="RECEIVE_PAYMENT_STATUS" jdbcType="BOOLEAN" property="receivePaymentStatus" />
    <result column="RECEIVE_PAYMENT_TIME" jdbcType="BIGINT" property="receivePaymentTime" />
    <result column="TRADER_MODE" jdbcType="INTEGER" property="traderMode" />
    <result column="TRADER_SUBJECT" jdbcType="BOOLEAN" property="traderSubject" />
    <result column="PAYEE" jdbcType="VARCHAR" property="payee" />
    <result column="BANK" jdbcType="VARCHAR" property="bank" />
    <result column="BANK_CODE" jdbcType="VARCHAR" property="bankCode" />
    <result column="BANK_ACCOUNT" jdbcType="VARCHAR" property="bankAccount" />
    <result column="PERIOD_AMOUNT" jdbcType="DECIMAL" property="periodAmount" />
    <result column="INVOICE_TRADER_ID" jdbcType="INTEGER" property="invoiceTraderId" />
    <result column="INVOICE_TRADER_NAME" jdbcType="VARCHAR" property="invoiceTraderName" />
    <result column="INVOICE_TRADER_CONTACT_ID" jdbcType="INTEGER" property="invoiceTraderContactId" />
    <result column="INVOICE_TRADER_CONTACT_NAME" jdbcType="VARCHAR" property="invoiceTraderContactName" />
    <result column="INVOICE_TRADER_CONTACT_MOBILE" jdbcType="VARCHAR" property="invoiceTraderContactMobile" />
    <result column="INVOICE_TRADER_CONTACT_TELEPHONE" jdbcType="VARCHAR" property="invoiceTraderContactTelephone" />
    <result column="INVOICE_TRADER_ADDRESS_ID" jdbcType="INTEGER" property="invoiceTraderAddressId" />
    <result column="INVOICE_TRADER_AREA" jdbcType="VARCHAR" property="invoiceTraderArea" />
    <result column="INVOICE_TRADER_ADDRESS" jdbcType="VARCHAR" property="invoiceTraderAddress" />
    <result column="INVOICE_COMMENTS" jdbcType="VARCHAR" property="invoiceComments" />
    <result column="FINAL_REFUNDABLE_AMOUNT" jdbcType="DECIMAL" property="finalRefundableAmount" />
    <result column="FIRST_RESPONSIBLE_DEPARTMENT" jdbcType="INTEGER" property="firstResponsibleDepartment" />
    <result column="AFTER_CONNECT_USERNAME" jdbcType="VARCHAR" property="afterConnectUsername" />
    <result column="AFTER_CONNECT_PHONE" jdbcType="VARCHAR" property="afterConnectPhone" />
    <result column="REFUND_COMMENT" jdbcType="VARCHAR" property="refundComment" />
    <result column="TRADER_NAME" jdbcType="VARCHAR" property="traderName" />
    <result column="PUBLIC_REFUND_AMOUNT" jdbcType="DECIMAL" property="publicRefundAmount" />
    <result column="PRIVATE_REFUND_AMOUNT" jdbcType="DECIMAL" property="privateRefundAmount" />
    <result column="IS_CAL_REFUND" jdbcType="INTEGER" property="isCalRefund"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    AFTER_SALES_DETAIL_ID, AFTER_SALES_ID, REASON, COMMENTS, TRADER_ID, TRADER_CONTACT_ID, 
    TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, REFUND, AREA_ID, 
    ADDRESS_ID, AREA, ADDRESS, SERVICE_AMOUNT, INVOICE_TYPE, IS_SEND_INVOICE, REFUND_AMOUNT, 
    REFUND_FEE, REAL_REFUND_AMOUNT, PAYMENT_AMOUNT, REFUND_AMOUNT_STATUS, SERVICE_AMOUNT_STATUS, 
    INVOICE_STATUS, INVOICE_TIME, RECEIVE_INVOICE_STATUS, RECEIVE_INVOICE_TIME, PAYMENT_STATUS, 
    PAYMENT_TIME, RECEIVE_PAYMENT_STATUS, RECEIVE_PAYMENT_TIME, TRADER_MODE, TRADER_SUBJECT, 
    PAYEE, BANK, BANK_CODE, BANK_ACCOUNT, PERIOD_AMOUNT, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, 
    INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, 
    INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA, 
    INVOICE_TRADER_ADDRESS, INVOICE_COMMENTS, FINAL_REFUNDABLE_AMOUNT, FIRST_RESPONSIBLE_DEPARTMENT, 
    AFTER_CONNECT_USERNAME, AFTER_CONNECT_PHONE, REFUND_COMMENT, PUBLIC_REFUND_AMOUNT, PRIVATE_REFUND_AMOUNT, IS_CAL_REFUND
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_DETAIL
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_AFTER_SALES_DETAIL
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="AFTER_SALES_DETAIL_ID" keyProperty="afterSalesDetailId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_DETAIL (AFTER_SALES_ID, REASON, COMMENTS, 
      TRADER_ID, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, 
      TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, 
      REFUND, AREA_ID, ADDRESS_ID, 
      AREA, ADDRESS, SERVICE_AMOUNT, 
      INVOICE_TYPE, IS_SEND_INVOICE, REFUND_AMOUNT, 
      REFUND_FEE, REAL_REFUND_AMOUNT, PAYMENT_AMOUNT, 
      REFUND_AMOUNT_STATUS, SERVICE_AMOUNT_STATUS, 
      INVOICE_STATUS, INVOICE_TIME, RECEIVE_INVOICE_STATUS, 
      RECEIVE_INVOICE_TIME, PAYMENT_STATUS, PAYMENT_TIME, 
      RECEIVE_PAYMENT_STATUS, RECEIVE_PAYMENT_TIME, 
      TRADER_MODE, TRADER_SUBJECT, PAYEE, 
      BANK, BANK_CODE, BANK_ACCOUNT, 
      PERIOD_AMOUNT, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, 
      INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, 
      INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE, 
      INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA, 
      INVOICE_TRADER_ADDRESS, INVOICE_COMMENTS, 
      FINAL_REFUNDABLE_AMOUNT, FIRST_RESPONSIBLE_DEPARTMENT, 
      AFTER_CONNECT_USERNAME, AFTER_CONNECT_PHONE, 
      REFUND_COMMENT)
    values (#{afterSalesId,jdbcType=INTEGER}, #{reason,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR}, 
      #{traderId,jdbcType=INTEGER}, #{traderContactId,jdbcType=INTEGER}, #{traderContactName,jdbcType=VARCHAR}, 
      #{traderContactMobile,jdbcType=VARCHAR}, #{traderContactTelephone,jdbcType=VARCHAR}, 
      #{refund,jdbcType=BOOLEAN}, #{areaId,jdbcType=INTEGER}, #{addressId,jdbcType=INTEGER}, 
      #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{serviceAmount,jdbcType=DECIMAL}, 
      #{invoiceType,jdbcType=INTEGER}, #{isSendInvoice,jdbcType=BOOLEAN}, #{refundAmount,jdbcType=DECIMAL}, 
      #{refundFee,jdbcType=DECIMAL}, #{realRefundAmount,jdbcType=DECIMAL}, #{paymentAmount,jdbcType=DECIMAL}, 
      #{refundAmountStatus,jdbcType=BOOLEAN}, #{serviceAmountStatus,jdbcType=BOOLEAN}, 
      #{invoiceStatus,jdbcType=BOOLEAN}, #{invoiceTime,jdbcType=BIGINT}, #{receiveInvoiceStatus,jdbcType=BOOLEAN}, 
      #{receiveInvoiceTime,jdbcType=BIGINT}, #{paymentStatus,jdbcType=BOOLEAN}, #{paymentTime,jdbcType=BIGINT}, 
      #{receivePaymentStatus,jdbcType=BOOLEAN}, #{receivePaymentTime,jdbcType=BIGINT}, 
      #{traderMode,jdbcType=INTEGER}, #{traderSubject,jdbcType=BOOLEAN}, #{payee,jdbcType=VARCHAR}, 
      #{bank,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{bankAccount,jdbcType=VARCHAR}, 
      #{periodAmount,jdbcType=DECIMAL}, #{invoiceTraderId,jdbcType=INTEGER}, #{invoiceTraderName,jdbcType=VARCHAR}, 
      #{invoiceTraderContactId,jdbcType=INTEGER}, #{invoiceTraderContactName,jdbcType=VARCHAR}, 
      #{invoiceTraderContactMobile,jdbcType=VARCHAR}, #{invoiceTraderContactTelephone,jdbcType=VARCHAR}, 
      #{invoiceTraderAddressId,jdbcType=INTEGER}, #{invoiceTraderArea,jdbcType=VARCHAR}, 
      #{invoiceTraderAddress,jdbcType=VARCHAR}, #{invoiceComments,jdbcType=VARCHAR}, 
      #{finalRefundableAmount,jdbcType=DECIMAL}, #{firstResponsibleDepartment,jdbcType=INTEGER}, 
      #{afterConnectUsername,jdbcType=VARCHAR}, #{afterConnectPhone,jdbcType=VARCHAR}, 
      #{refundComment,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="AFTER_SALES_DETAIL_ID" keyProperty="afterSalesDetailId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="refund != null">
        REFUND,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="addressId != null">
        ADDRESS_ID,
      </if>
      <if test="area != null and area != ''">
        AREA,
      </if>
      <if test="address != null and address != ''">
        ADDRESS,
      </if>
      <if test="serviceAmount != null">
        SERVICE_AMOUNT,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE,
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT,
      </if>
      <if test="refundFee != null">
        REFUND_FEE,
      </if>
      <if test="realRefundAmount != null">
        REAL_REFUND_AMOUNT,
      </if>
      <if test="paymentAmount != null">
        PAYMENT_AMOUNT,
      </if>
      <if test="refundAmountStatus != null">
        REFUND_AMOUNT_STATUS,
      </if>
      <if test="serviceAmountStatus != null">
        SERVICE_AMOUNT_STATUS,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME,
      </if>
      <if test="receiveInvoiceStatus != null">
        RECEIVE_INVOICE_STATUS,
      </if>
      <if test="receiveInvoiceTime != null">
        RECEIVE_INVOICE_TIME,
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS,
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME,
      </if>
      <if test="receivePaymentStatus != null">
        RECEIVE_PAYMENT_STATUS,
      </if>
      <if test="receivePaymentTime != null">
        RECEIVE_PAYMENT_TIME,
      </if>
      <if test="traderMode != null">
        TRADER_MODE,
      </if>
      <if test="traderSubject != null">
        TRADER_SUBJECT,
      </if>
      <if test="payee != null and payee != ''">
        PAYEE,
      </if>
      <if test="bank != null and bank != ''">
        BANK,
      </if>
      <if test="bankCode != null and bankCode != ''">
        BANK_CODE,
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        BANK_ACCOUNT,
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT,
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null and invoiceTraderName != ''">
        INVOICE_TRADER_NAME,
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceTraderContactName != null and invoiceTraderContactName != ''">
        INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="invoiceTraderContactMobile != null and invoiceTraderContactMobile != ''">
        INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="invoiceTraderContactTelephone != null and invoiceTraderContactTelephone != ''">
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderArea != null and invoiceTraderArea != ''">
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceTraderAddress != null and invoiceTraderAddress != ''">
        INVOICE_TRADER_ADDRESS,
      </if>
      <if test="invoiceComments != null and invoiceComments != ''">
        INVOICE_COMMENTS,
      </if>
      <if test="finalRefundableAmount != null">
        FINAL_REFUNDABLE_AMOUNT,
      </if>
      <if test="firstResponsibleDepartment != null">
        FIRST_RESPONSIBLE_DEPARTMENT,
      </if>
      <if test="afterConnectUsername != null and afterConnectUsername != ''">
        AFTER_CONNECT_USERNAME,
      </if>
      <if test="afterConnectPhone != null and afterConnectPhone != ''">
        AFTER_CONNECT_PHONE,
      </if>
      <if test="refundComment != null and refundComment != ''">
        REFUND_COMMENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null and comments != ''">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null">
        #{refund,jdbcType=BOOLEAN},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null">
        #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null and area != ''">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null and address != ''">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null">
        #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null">
        #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
        #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null">
        #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null">
        #{refundAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="serviceAmountStatus != null">
        #{serviceAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="receiveInvoiceStatus != null">
        #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receiveInvoiceTime != null">
        #{receiveInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="receivePaymentStatus != null">
        #{receivePaymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receivePaymentTime != null">
        #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="traderMode != null">
        #{traderMode,jdbcType=INTEGER},
      </if>
      <if test="traderSubject != null">
        #{traderSubject,jdbcType=BOOLEAN},
      </if>
      <if test="payee != null and payee != ''">
        #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null and bank != ''">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="periodAmount != null">
        #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null">
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null and invoiceTraderName != ''">
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null and invoiceTraderContactName != ''">
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null and invoiceTraderContactMobile != ''">
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null and invoiceTraderContactTelephone != ''">
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null and invoiceTraderArea != ''">
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null and invoiceTraderAddress != ''">
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null and invoiceComments != ''">
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="finalRefundableAmount != null">
        #{finalRefundableAmount,jdbcType=DECIMAL},
      </if>
      <if test="firstResponsibleDepartment != null">
        #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
      <if test="afterConnectUsername != null and afterConnectUsername != ''">
        #{afterConnectUsername,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null and afterConnectPhone != ''">
        #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="refundComment != null and refundComment != ''">
        #{refundComment,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES_DETAIL
    <set>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null">
        REFUND = #{refund,jdbcType=BOOLEAN},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null">
        ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null and area != ''">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null and address != ''">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null">
        SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
        REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null">
        REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null">
        REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="serviceAmountStatus != null">
        SERVICE_AMOUNT_STATUS = #{serviceAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="receiveInvoiceStatus != null">
        RECEIVE_INVOICE_STATUS = #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receiveInvoiceTime != null">
        RECEIVE_INVOICE_TIME = #{receiveInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS = #{paymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="receivePaymentStatus != null">
        RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receivePaymentTime != null">
        RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="traderMode != null">
        TRADER_MODE = #{traderMode,jdbcType=INTEGER},
      </if>
      <if test="traderSubject != null">
        TRADER_SUBJECT = #{traderSubject,jdbcType=BOOLEAN},
      </if>
      <if test="payee != null and payee != ''">
        PAYEE = #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null and bank != ''">
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null and invoiceTraderName != ''">
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null and invoiceTraderContactName != ''">
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null and invoiceTraderContactMobile != ''">
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null and invoiceTraderContactTelephone != ''">
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null and invoiceTraderArea != ''">
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null and invoiceTraderAddress != ''">
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null and invoiceComments != ''">
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="finalRefundableAmount != null">
        FINAL_REFUNDABLE_AMOUNT = #{finalRefundableAmount,jdbcType=DECIMAL},
      </if>
      <if test="firstResponsibleDepartment != null">
        FIRST_RESPONSIBLE_DEPARTMENT = #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
      <if test="afterConnectUsername != null and afterConnectUsername != ''">
        AFTER_CONNECT_USERNAME = #{afterConnectUsername,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null and afterConnectPhone != ''">
        AFTER_CONNECT_PHONE = #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="refundComment != null and refundComment != ''">
        REFUND_COMMENT = #{refundComment,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity">
    <!--@mbg.generated-->
    update T_AFTER_SALES_DETAIL
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      REASON = #{reason,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      REFUND = #{refund,jdbcType=BOOLEAN},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      AREA = #{area,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BOOLEAN},
      SERVICE_AMOUNT_STATUS = #{serviceAmountStatus,jdbcType=BOOLEAN},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      RECEIVE_INVOICE_STATUS = #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      RECEIVE_INVOICE_TIME = #{receiveInvoiceTime,jdbcType=BIGINT},
      PAYMENT_STATUS = #{paymentStatus,jdbcType=BOOLEAN},
      PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BOOLEAN},
      RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
      TRADER_MODE = #{traderMode,jdbcType=INTEGER},
      TRADER_SUBJECT = #{traderSubject,jdbcType=BOOLEAN},
      PAYEE = #{payee,jdbcType=VARCHAR},
      BANK = #{bank,jdbcType=VARCHAR},
      BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      FINAL_REFUNDABLE_AMOUNT = #{finalRefundableAmount,jdbcType=DECIMAL},
      FIRST_RESPONSIBLE_DEPARTMENT = #{firstResponsibleDepartment,jdbcType=INTEGER},
      AFTER_CONNECT_USERNAME = #{afterConnectUsername,jdbcType=VARCHAR},
      AFTER_CONNECT_PHONE = #{afterConnectPhone,jdbcType=VARCHAR},
      REFUND_COMMENT = #{refundComment,jdbcType=VARCHAR}
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.reason,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REFUND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refund,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.areaId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.addressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.area,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.address,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="SERVICE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.serviceAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceType,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="IS_SEND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.isSendInvoice,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REFUND_FEE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundFee,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REAL_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.realRefundAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.paymentAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="REFUND_AMOUNT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundAmountStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="SERVICE_AMOUNT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.serviceAmountStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="RECEIVE_INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receiveInvoiceStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="RECEIVE_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receiveInvoiceTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.paymentStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.paymentTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="RECEIVE_PAYMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receivePaymentStatus,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="RECEIVE_PAYMENT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receivePaymentTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="TRADER_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderMode,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="TRADER_SUBJECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderSubject,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="PAYEE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.payee,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BANK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.bank,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BANK_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.bankCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="BANK_ACCOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.bankAccount,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="PERIOD_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.periodAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactMobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactTelephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderAddressId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderArea,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderAddress,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="INVOICE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceComments,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="FINAL_REFUNDABLE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.finalRefundableAmount,jdbcType=DECIMAL}
        </foreach>
      </trim>
      <trim prefix="FIRST_RESPONSIBLE_DEPARTMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.firstResponsibleDepartment,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="AFTER_CONNECT_USERNAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.afterConnectUsername,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="AFTER_CONNECT_PHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.afterConnectPhone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="REFUND_COMMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundComment,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_AFTER_SALES_DETAIL
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="AFTER_SALES_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterSalesId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.afterSalesId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="REASON = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reason != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.reason,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.comments != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.comments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactName != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactMobile != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderContactTelephone != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderContactTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFUND = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.refund != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refund,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="AREA_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.areaId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.areaId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addressId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.addressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.area != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.area,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.address != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.address,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="SERVICE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceAmount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.serviceAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TYPE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceType != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceType,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_SEND_INVOICE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isSendInvoice != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.isSendInvoice,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.refundAmount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFUND_FEE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.refundFee != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundFee,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REAL_REFUND_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.realRefundAmount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.realRefundAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentAmount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.paymentAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFUND_AMOUNT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.refundAmountStatus != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundAmountStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="SERVICE_AMOUNT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serviceAmountStatus != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.serviceAmountStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceStatus != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTime != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVE_INVOICE_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveInvoiceStatus != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receiveInvoiceStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVE_INVOICE_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receiveInvoiceTime != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receiveInvoiceTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentStatus != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.paymentStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYMENT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.paymentTime != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.paymentTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVE_PAYMENT_STATUS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivePaymentStatus != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receivePaymentStatus,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="RECEIVE_PAYMENT_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.receivePaymentTime != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.receivePaymentTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_MODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderMode != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderMode,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="TRADER_SUBJECT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.traderSubject != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.traderSubject,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="PAYEE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.payee != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.payee,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bank != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.bank,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankCode != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.bankCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="BANK_ACCOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.bankAccount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.bankAccount,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="PERIOD_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.periodAmount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.periodAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderName != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactName != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_MOBILE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactMobile != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactMobile,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_CONTACT_TELEPHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderContactTelephone != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderContactTelephone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderAddressId != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderAddressId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_AREA = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderArea != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderArea,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_TRADER_ADDRESS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceTraderAddress != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceTraderAddress,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="INVOICE_COMMENTS = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceComments != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.invoiceComments,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="FINAL_REFUNDABLE_AMOUNT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.finalRefundableAmount != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.finalRefundableAmount,jdbcType=DECIMAL}
          </if>
        </foreach>
      </trim>
      <trim prefix="FIRST_RESPONSIBLE_DEPARTMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.firstResponsibleDepartment != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.firstResponsibleDepartment,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_CONNECT_USERNAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterConnectUsername != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.afterConnectUsername,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="AFTER_CONNECT_PHONE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.afterConnectPhone != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.afterConnectPhone,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="REFUND_COMMENT = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.refundComment != null">
            when AFTER_SALES_DETAIL_ID = #{item.afterSalesDetailId,jdbcType=INTEGER} then #{item.refundComment,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where AFTER_SALES_DETAIL_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.afterSalesDetailId,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="AFTER_SALES_DETAIL_ID" keyProperty="afterSalesDetailId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_DETAIL
    (AFTER_SALES_ID, REASON, COMMENTS, TRADER_ID, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, 
      TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, REFUND, AREA_ID, ADDRESS_ID, AREA, 
      ADDRESS, SERVICE_AMOUNT, INVOICE_TYPE, IS_SEND_INVOICE, REFUND_AMOUNT, REFUND_FEE, 
      REAL_REFUND_AMOUNT, PAYMENT_AMOUNT, REFUND_AMOUNT_STATUS, SERVICE_AMOUNT_STATUS, 
      INVOICE_STATUS, INVOICE_TIME, RECEIVE_INVOICE_STATUS, RECEIVE_INVOICE_TIME, PAYMENT_STATUS, 
      PAYMENT_TIME, RECEIVE_PAYMENT_STATUS, RECEIVE_PAYMENT_TIME, TRADER_MODE, TRADER_SUBJECT, 
      PAYEE, BANK, BANK_CODE, BANK_ACCOUNT, PERIOD_AMOUNT, INVOICE_TRADER_ID, INVOICE_TRADER_NAME, 
      INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, INVOICE_TRADER_CONTACT_MOBILE, 
      INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, INVOICE_TRADER_AREA, 
      INVOICE_TRADER_ADDRESS, INVOICE_COMMENTS, FINAL_REFUNDABLE_AMOUNT, FIRST_RESPONSIBLE_DEPARTMENT, 
      AFTER_CONNECT_USERNAME, AFTER_CONNECT_PHONE, REFUND_COMMENT)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.afterSalesId,jdbcType=INTEGER}, #{item.reason,jdbcType=INTEGER}, #{item.comments,jdbcType=VARCHAR}, 
        #{item.traderId,jdbcType=INTEGER}, #{item.traderContactId,jdbcType=INTEGER}, #{item.traderContactName,jdbcType=VARCHAR}, 
        #{item.traderContactMobile,jdbcType=VARCHAR}, #{item.traderContactTelephone,jdbcType=VARCHAR}, 
        #{item.refund,jdbcType=BOOLEAN}, #{item.areaId,jdbcType=INTEGER}, #{item.addressId,jdbcType=INTEGER}, 
        #{item.area,jdbcType=VARCHAR}, #{item.address,jdbcType=VARCHAR}, #{item.serviceAmount,jdbcType=DECIMAL}, 
        #{item.invoiceType,jdbcType=INTEGER}, #{item.isSendInvoice,jdbcType=BOOLEAN}, #{item.refundAmount,jdbcType=DECIMAL}, 
        #{item.refundFee,jdbcType=DECIMAL}, #{item.realRefundAmount,jdbcType=DECIMAL}, 
        #{item.paymentAmount,jdbcType=DECIMAL}, #{item.refundAmountStatus,jdbcType=BOOLEAN}, 
        #{item.serviceAmountStatus,jdbcType=BOOLEAN}, #{item.invoiceStatus,jdbcType=BOOLEAN}, 
        #{item.invoiceTime,jdbcType=BIGINT}, #{item.receiveInvoiceStatus,jdbcType=BOOLEAN}, 
        #{item.receiveInvoiceTime,jdbcType=BIGINT}, #{item.paymentStatus,jdbcType=BOOLEAN}, 
        #{item.paymentTime,jdbcType=BIGINT}, #{item.receivePaymentStatus,jdbcType=BOOLEAN}, 
        #{item.receivePaymentTime,jdbcType=BIGINT}, #{item.traderMode,jdbcType=INTEGER}, 
        #{item.traderSubject,jdbcType=BOOLEAN}, #{item.payee,jdbcType=VARCHAR}, #{item.bank,jdbcType=VARCHAR}, 
        #{item.bankCode,jdbcType=VARCHAR}, #{item.bankAccount,jdbcType=VARCHAR}, #{item.periodAmount,jdbcType=DECIMAL}, 
        #{item.invoiceTraderId,jdbcType=INTEGER}, #{item.invoiceTraderName,jdbcType=VARCHAR}, 
        #{item.invoiceTraderContactId,jdbcType=INTEGER}, #{item.invoiceTraderContactName,jdbcType=VARCHAR}, 
        #{item.invoiceTraderContactMobile,jdbcType=VARCHAR}, #{item.invoiceTraderContactTelephone,jdbcType=VARCHAR}, 
        #{item.invoiceTraderAddressId,jdbcType=INTEGER}, #{item.invoiceTraderArea,jdbcType=VARCHAR}, 
        #{item.invoiceTraderAddress,jdbcType=VARCHAR}, #{item.invoiceComments,jdbcType=VARCHAR}, 
        #{item.finalRefundableAmount,jdbcType=DECIMAL}, #{item.firstResponsibleDepartment,jdbcType=INTEGER}, 
        #{item.afterConnectUsername,jdbcType=VARCHAR}, #{item.afterConnectPhone,jdbcType=VARCHAR}, 
        #{item.refundComment,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="AFTER_SALES_DETAIL_ID" keyProperty="afterSalesDetailId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesDetailId != null">
        AFTER_SALES_DETAIL_ID,
      </if>
      AFTER_SALES_ID,
      REASON,
      COMMENTS,
      TRADER_ID,
      TRADER_CONTACT_ID,
      TRADER_CONTACT_NAME,
      TRADER_CONTACT_MOBILE,
      TRADER_CONTACT_TELEPHONE,
      REFUND,
      AREA_ID,
      ADDRESS_ID,
      AREA,
      ADDRESS,
      SERVICE_AMOUNT,
      INVOICE_TYPE,
      IS_SEND_INVOICE,
      REFUND_AMOUNT,
      REFUND_FEE,
      REAL_REFUND_AMOUNT,
      PAYMENT_AMOUNT,
      REFUND_AMOUNT_STATUS,
      SERVICE_AMOUNT_STATUS,
      INVOICE_STATUS,
      INVOICE_TIME,
      RECEIVE_INVOICE_STATUS,
      RECEIVE_INVOICE_TIME,
      PAYMENT_STATUS,
      PAYMENT_TIME,
      RECEIVE_PAYMENT_STATUS,
      RECEIVE_PAYMENT_TIME,
      TRADER_MODE,
      TRADER_SUBJECT,
      PAYEE,
      BANK,
      BANK_CODE,
      BANK_ACCOUNT,
      PERIOD_AMOUNT,
      INVOICE_TRADER_ID,
      INVOICE_TRADER_NAME,
      INVOICE_TRADER_CONTACT_ID,
      INVOICE_TRADER_CONTACT_NAME,
      INVOICE_TRADER_CONTACT_MOBILE,
      INVOICE_TRADER_CONTACT_TELEPHONE,
      INVOICE_TRADER_ADDRESS_ID,
      INVOICE_TRADER_AREA,
      INVOICE_TRADER_ADDRESS,
      INVOICE_COMMENTS,
      FINAL_REFUNDABLE_AMOUNT,
      FIRST_RESPONSIBLE_DEPARTMENT,
      AFTER_CONNECT_USERNAME,
      AFTER_CONNECT_PHONE,
      REFUND_COMMENT,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesDetailId != null">
        #{afterSalesDetailId,jdbcType=INTEGER},
      </if>
      #{afterSalesId,jdbcType=INTEGER},
      #{reason,jdbcType=INTEGER},
      #{comments,jdbcType=VARCHAR},
      #{traderId,jdbcType=INTEGER},
      #{traderContactId,jdbcType=INTEGER},
      #{traderContactName,jdbcType=VARCHAR},
      #{traderContactMobile,jdbcType=VARCHAR},
      #{traderContactTelephone,jdbcType=VARCHAR},
      #{refund,jdbcType=BOOLEAN},
      #{areaId,jdbcType=INTEGER},
      #{addressId,jdbcType=INTEGER},
      #{area,jdbcType=VARCHAR},
      #{address,jdbcType=VARCHAR},
      #{serviceAmount,jdbcType=DECIMAL},
      #{invoiceType,jdbcType=INTEGER},
      #{isSendInvoice,jdbcType=BOOLEAN},
      #{refundAmount,jdbcType=DECIMAL},
      #{refundFee,jdbcType=DECIMAL},
      #{realRefundAmount,jdbcType=DECIMAL},
      #{paymentAmount,jdbcType=DECIMAL},
      #{refundAmountStatus,jdbcType=BOOLEAN},
      #{serviceAmountStatus,jdbcType=BOOLEAN},
      #{invoiceStatus,jdbcType=BOOLEAN},
      #{invoiceTime,jdbcType=BIGINT},
      #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      #{receiveInvoiceTime,jdbcType=BIGINT},
      #{paymentStatus,jdbcType=BOOLEAN},
      #{paymentTime,jdbcType=BIGINT},
      #{receivePaymentStatus,jdbcType=BOOLEAN},
      #{receivePaymentTime,jdbcType=BIGINT},
      #{traderMode,jdbcType=INTEGER},
      #{traderSubject,jdbcType=BOOLEAN},
      #{payee,jdbcType=VARCHAR},
      #{bank,jdbcType=VARCHAR},
      #{bankCode,jdbcType=VARCHAR},
      #{bankAccount,jdbcType=VARCHAR},
      #{periodAmount,jdbcType=DECIMAL},
      #{invoiceTraderId,jdbcType=INTEGER},
      #{invoiceTraderName,jdbcType=VARCHAR},
      #{invoiceTraderContactId,jdbcType=INTEGER},
      #{invoiceTraderContactName,jdbcType=VARCHAR},
      #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      #{invoiceTraderAddressId,jdbcType=INTEGER},
      #{invoiceTraderArea,jdbcType=VARCHAR},
      #{invoiceTraderAddress,jdbcType=VARCHAR},
      #{invoiceComments,jdbcType=VARCHAR},
      #{finalRefundableAmount,jdbcType=DECIMAL},
      #{firstResponsibleDepartment,jdbcType=INTEGER},
      #{afterConnectUsername,jdbcType=VARCHAR},
      #{afterConnectPhone,jdbcType=VARCHAR},
      #{refundComment,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="afterSalesDetailId != null">
        AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER},
      </if>
      AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      REASON = #{reason,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      REFUND = #{refund,jdbcType=BOOLEAN},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      AREA = #{area,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BOOLEAN},
      SERVICE_AMOUNT_STATUS = #{serviceAmountStatus,jdbcType=BOOLEAN},
      INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      RECEIVE_INVOICE_STATUS = #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      RECEIVE_INVOICE_TIME = #{receiveInvoiceTime,jdbcType=BIGINT},
      PAYMENT_STATUS = #{paymentStatus,jdbcType=BOOLEAN},
      PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BOOLEAN},
      RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
      TRADER_MODE = #{traderMode,jdbcType=INTEGER},
      TRADER_SUBJECT = #{traderSubject,jdbcType=BOOLEAN},
      PAYEE = #{payee,jdbcType=VARCHAR},
      BANK = #{bank,jdbcType=VARCHAR},
      BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      FINAL_REFUNDABLE_AMOUNT = #{finalRefundableAmount,jdbcType=DECIMAL},
      FIRST_RESPONSIBLE_DEPARTMENT = #{firstResponsibleDepartment,jdbcType=INTEGER},
      AFTER_CONNECT_USERNAME = #{afterConnectUsername,jdbcType=VARCHAR},
      AFTER_CONNECT_PHONE = #{afterConnectPhone,jdbcType=VARCHAR},
      REFUND_COMMENT = #{refundComment,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="AFTER_SALES_DETAIL_ID" keyProperty="afterSalesDetailId" parameterType="com.vedeng.erp.aftersale.domain.entity.AfterSalesDetailEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_AFTER_SALES_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesDetailId != null">
        AFTER_SALES_DETAIL_ID,
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID,
      </if>
      <if test="reason != null">
        REASON,
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS,
      </if>
      <if test="traderId != null">
        TRADER_ID,
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="refund != null">
        REFUND,
      </if>
      <if test="areaId != null">
        AREA_ID,
      </if>
      <if test="addressId != null">
        ADDRESS_ID,
      </if>
      <if test="area != null and area != ''">
        AREA,
      </if>
      <if test="address != null and address != ''">
        ADDRESS,
      </if>
      <if test="serviceAmount != null">
        SERVICE_AMOUNT,
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE,
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE,
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT,
      </if>
      <if test="refundFee != null">
        REFUND_FEE,
      </if>
      <if test="realRefundAmount != null">
        REAL_REFUND_AMOUNT,
      </if>
      <if test="paymentAmount != null">
        PAYMENT_AMOUNT,
      </if>
      <if test="refundAmountStatus != null">
        REFUND_AMOUNT_STATUS,
      </if>
      <if test="serviceAmountStatus != null">
        SERVICE_AMOUNT_STATUS,
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS,
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME,
      </if>
      <if test="receiveInvoiceStatus != null">
        RECEIVE_INVOICE_STATUS,
      </if>
      <if test="receiveInvoiceTime != null">
        RECEIVE_INVOICE_TIME,
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS,
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME,
      </if>
      <if test="receivePaymentStatus != null">
        RECEIVE_PAYMENT_STATUS,
      </if>
      <if test="receivePaymentTime != null">
        RECEIVE_PAYMENT_TIME,
      </if>
      <if test="traderMode != null">
        TRADER_MODE,
      </if>
      <if test="traderSubject != null">
        TRADER_SUBJECT,
      </if>
      <if test="payee != null and payee != ''">
        PAYEE,
      </if>
      <if test="bank != null and bank != ''">
        BANK,
      </if>
      <if test="bankCode != null and bankCode != ''">
        BANK_CODE,
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        BANK_ACCOUNT,
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT,
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null and invoiceTraderName != ''">
        INVOICE_TRADER_NAME,
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID,
      </if>
      <if test="invoiceTraderContactName != null and invoiceTraderContactName != ''">
        INVOICE_TRADER_CONTACT_NAME,
      </if>
      <if test="invoiceTraderContactMobile != null and invoiceTraderContactMobile != ''">
        INVOICE_TRADER_CONTACT_MOBILE,
      </if>
      <if test="invoiceTraderContactTelephone != null and invoiceTraderContactTelephone != ''">
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderArea != null and invoiceTraderArea != ''">
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceTraderAddress != null and invoiceTraderAddress != ''">
        INVOICE_TRADER_ADDRESS,
      </if>
      <if test="invoiceComments != null and invoiceComments != ''">
        INVOICE_COMMENTS,
      </if>
      <if test="finalRefundableAmount != null">
        FINAL_REFUNDABLE_AMOUNT,
      </if>
      <if test="firstResponsibleDepartment != null">
        FIRST_RESPONSIBLE_DEPARTMENT,
      </if>
      <if test="afterConnectUsername != null and afterConnectUsername != ''">
        AFTER_CONNECT_USERNAME,
      </if>
      <if test="afterConnectPhone != null and afterConnectPhone != ''">
        AFTER_CONNECT_PHONE,
      </if>
      <if test="refundComment != null and refundComment != ''">
        REFUND_COMMENT,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="afterSalesDetailId != null">
        #{afterSalesDetailId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null and comments != ''">
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null">
        #{refund,jdbcType=BOOLEAN},
      </if>
      <if test="areaId != null">
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null">
        #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null and area != ''">
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null and address != ''">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null">
        #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null">
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null">
        #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="refundAmount != null">
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
        #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null">
        #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null">
        #{refundAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="serviceAmountStatus != null">
        #{serviceAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="receiveInvoiceStatus != null">
        #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receiveInvoiceTime != null">
        #{receiveInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="paymentTime != null">
        #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="receivePaymentStatus != null">
        #{receivePaymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receivePaymentTime != null">
        #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="traderMode != null">
        #{traderMode,jdbcType=INTEGER},
      </if>
      <if test="traderSubject != null">
        #{traderSubject,jdbcType=BOOLEAN},
      </if>
      <if test="payee != null and payee != ''">
        #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null and bank != ''">
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="periodAmount != null">
        #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null">
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null and invoiceTraderName != ''">
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null and invoiceTraderContactName != ''">
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null and invoiceTraderContactMobile != ''">
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null and invoiceTraderContactTelephone != ''">
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null and invoiceTraderArea != ''">
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null and invoiceTraderAddress != ''">
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null and invoiceComments != ''">
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="finalRefundableAmount != null">
        #{finalRefundableAmount,jdbcType=DECIMAL},
      </if>
      <if test="firstResponsibleDepartment != null">
        #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
      <if test="afterConnectUsername != null and afterConnectUsername != ''">
        #{afterConnectUsername,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null and afterConnectPhone != ''">
        #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="refundComment != null and refundComment != ''">
        #{refundComment,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="afterSalesDetailId != null">
        AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null">
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null">
        REASON = #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null and comments != ''">
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null">
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null">
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null and traderContactName != ''">
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null and traderContactMobile != ''">
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null and traderContactTelephone != ''">
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null">
        REFUND = #{refund,jdbcType=BOOLEAN},
      </if>
      <if test="areaId != null">
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null">
        ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null and area != ''">
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null and address != ''">
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null">
        SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null">
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null">
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BOOLEAN},
      </if>
      <if test="refundAmount != null">
        REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null">
        REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null">
        REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null">
        PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null">
        REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="serviceAmountStatus != null">
        SERVICE_AMOUNT_STATUS = #{serviceAmountStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceStatus != null">
        INVOICE_STATUS = #{invoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="invoiceTime != null">
        INVOICE_TIME = #{invoiceTime,jdbcType=BIGINT},
      </if>
      <if test="receiveInvoiceStatus != null">
        RECEIVE_INVOICE_STATUS = #{receiveInvoiceStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receiveInvoiceTime != null">
        RECEIVE_INVOICE_TIME = #{receiveInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null">
        PAYMENT_STATUS = #{paymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="paymentTime != null">
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="receivePaymentStatus != null">
        RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BOOLEAN},
      </if>
      <if test="receivePaymentTime != null">
        RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="traderMode != null">
        TRADER_MODE = #{traderMode,jdbcType=INTEGER},
      </if>
      <if test="traderSubject != null">
        TRADER_SUBJECT = #{traderSubject,jdbcType=BOOLEAN},
      </if>
      <if test="payee != null and payee != ''">
        PAYEE = #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null and bank != ''">
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null and bankCode != ''">
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null and bankAccount != ''">
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="periodAmount != null">
        PERIOD_AMOUNT = #{periodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null">
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null and invoiceTraderName != ''">
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null">
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null and invoiceTraderContactName != ''">
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null and invoiceTraderContactMobile != ''">
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null and invoiceTraderContactTelephone != ''">
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId != null">
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderArea != null and invoiceTraderArea != ''">
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddress != null and invoiceTraderAddress != ''">
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null and invoiceComments != ''">
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="finalRefundableAmount != null">
        FINAL_REFUNDABLE_AMOUNT = #{finalRefundableAmount,jdbcType=DECIMAL},
      </if>
      <if test="firstResponsibleDepartment != null">
        FIRST_RESPONSIBLE_DEPARTMENT = #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
      <if test="afterConnectUsername != null and afterConnectUsername != ''">
        AFTER_CONNECT_USERNAME = #{afterConnectUsername,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null and afterConnectPhone != ''">
        AFTER_CONNECT_PHONE = #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="refundComment != null and refundComment != ''">
        REFUND_COMMENT = #{refundComment,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2023-11-14-->
  <select id="findByAfterSalesId" resultMap="BaseResultMap">
    select
    TASD.*,TT.TRADER_NAME
    from T_AFTER_SALES_DETAIL TASD
    left join T_TRADER TT on TASD.TRADER_ID = TT.TRADER_ID
    where AFTER_SALES_ID=#{afterSalesId,jdbcType=INTEGER}
  </select>

  <select id="getGoodsNameModelAndSpecByAfterSalesGoodsId" resultType="com.vedeng.erp.aftersale.dto.AfterSaleSkuInfoDto">
    select vcs.SKU_NAME,vcp.SPU_TYPE,vcs.MODEL,vcs.SPEC from T_AFTER_SALES_GOODS asg left join V_CORE_SKU vcs on asg.GOODS_ID = vcs.SKU_ID
      left join V_CORE_SPU vcp on vcs.SPU_ID = vcp.SPU_ID
    where asg.AFTER_SALES_GOODS_ID = #{detailGoodsId,jdbcType=INTEGER}
    </select>

  <select id="getAfterSalesNum" resultType="com.vedeng.erp.saleorder.dto.AfterSalesGoodsDto">
    select b.ORDER_DETAIL_ID,SUM(b.NUM) num from T_AFTER_SALES a left join T_AFTER_SALES_GOODS b on a.AFTER_SALES_ID = b.AFTER_SALES_ID
    where a.ORDER_ID = #{saleOrderId,jdbcType=INTEGER} and  a.TYPE = 539 and a.SUBJECT_TYPE = 535 and a.ATFER_SALES_STATUS =2
    group by b.ORDER_DETAIL_ID
  </select>
    
  <select id="getPublicRefundAmount" resultType="java.math.BigDecimal">
    select IFNULL(SUM(ABS(TASD.PUBLIC_REFUND_AMOUNT)), 0)
    from T_AFTER_SALES_DETAIL TASD
           left join T_AFTER_SALES TAS on TASD.AFTER_SALES_ID = TAS.AFTER_SALES_ID
    where TAS.SUBJECT_TYPE = 535
      and TAS.TYPE IN (539, 543)
      and TAS.AFTER_SALES_ID != #{afterSalesId,jdbcType=INTEGER}
      and TAS.ORDER_ID = #{orderId,jdbcType=INTEGER}
      and TAS.ATFER_SALES_STATUS = #{afterSalesStatus,jdbcType=INTEGER}
    </select>
  
  <select id="getPrivateRefundAmount" resultType="java.math.BigDecimal">
    select IFNULL(SUM(ABS(TASD.PRIVATE_REFUND_AMOUNT)), 0)
    from T_AFTER_SALES_DETAIL TASD
    left join T_AFTER_SALES TAS on TASD.AFTER_SALES_ID = TAS.AFTER_SALES_ID
    where TAS.SUBJECT_TYPE = 535
    and TAS.TYPE IN (539, 543)
    and TAS.AFTER_SALES_ID != #{afterSalesId,jdbcType=INTEGER}
    and TAS.ORDER_ID = #{orderId,jdbcType=INTEGER}
    and TAS.ATFER_SALES_STATUS = #{afterSalesStatus,jdbcType=INTEGER}
  </select>
  
<!--auto generated by MybatisCodeHelper on 2024-08-23-->
  <update id="updateRefundByAfterSalesDetailId">
    update T_AFTER_SALES_DETAIL
    set REFUND=#{updatedRefund,jdbcType=BOOLEAN}
    where AFTER_SALES_DETAIL_ID=#{afterSalesDetailId,jdbcType=INTEGER}
  </update>
  
<!--auto generated by MybatisCodeHelper on 2024-08-23-->
  <update id="updatePublicAndPrivateRefundAmount">
    update T_AFTER_SALES_DETAIL
    set PUBLIC_REFUND_AMOUNT=#{updatedPublicRefundAmount,jdbcType=DECIMAL},
    PRIVATE_REFUND_AMOUNT=#{updatedPrivateRefundAmount,jdbcType=DECIMAL},
    REFUND=#{refund,jdbcType=INTEGER}
    where AFTER_SALES_DETAIL_ID=#{afterSalesDetailId,jdbcType=INTEGER}
  </update>
</mapper>