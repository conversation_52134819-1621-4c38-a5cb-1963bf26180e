package com.vedeng.common.core.listenerEvent;

import com.vedeng.common.core.listenerEvent.eventresult.AuditRecordEventResult;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/5 9:08
 */
@Setter
@Getter
public class AuditRecordEvent {

    /**
     * businessKey
     */
    private String businessKey;

    /**
     * 所有审核记录
     */
    private List<AuditRecordEventResult> auditRecordEventResults;

    /**
     * 当前流程id
     */
    private String taskId;

    /**
     * 当前登录用户是否是审核候选人
     */
    private Boolean isAuditUser = false;
}