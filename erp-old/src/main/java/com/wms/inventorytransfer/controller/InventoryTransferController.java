package com.wms.inventorytransfer.controller;

import com.vedeng.common.controller.BaseController;
import com.vedeng.common.page.Page;
import com.wms.inventorytransfer.model.dto.InventoryTransferSearchDto;
import com.wms.inventorytransfer.model.vo.InventoryTransferDetailVO;
import com.wms.inventorytransfer.model.vo.InventoryTransferVO;
import com.wms.inventorytransfer.service.InventoryTransferService;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @describe 库存转移
 * <AUTHOR>
 * @date 2020/7/15 13:43:20
 */
@Controller
@RequestMapping("/wms/inventory")
public class InventoryTransferController extends BaseController {
    @Autowired
    private InventoryTransferService inventoryTransferService;

    /**
     *
     * <b>Description:</b><br> 库存转移单列表
     * @return
     * @Note
     * <b>Author:</b> hugo
     * <br><b>Date:</b> 2020年7月10日 13:13:13
     */
    @ResponseBody
    @RequestMapping(value = "/inventoryTransfer")
    public ModelAndView inventoryTransfer(HttpServletRequest request,
                                          InventoryTransferSearchDto inventoryTransferDto,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize){
        ModelAndView mav = new ModelAndView("wms/inventory/index_inventoryTransfer");
        try {
            Page page = getPageTag(request, pageNo, pageSize);
            Map<String, Object> map = inventoryTransferService.getInventoryTransferVos(inventoryTransferDto,page);
            if (MapUtils.isNotEmpty(map)){
                mav.addObject("inventoryTransferVos", map.get("inventoryTransferVos"));
                mav.addObject("page",map.get("page"));
            }
        } catch (Exception e) {
            logger.error("/warehouse/warehouses/inventoryTransfer", e);
        }
        mav.addObject("inventoryTransferDto", inventoryTransferDto);
        return mav;
    }

    /**
     *
     * <b>Description:</b><br> 库存转移单详情
     * @return
     * @Note
     * <b>Author:</b> hugo
     * <br><b>Date:</b> 2020年7月10日 13:13:13
     */
    @ResponseBody
    @RequestMapping(value = "/inventoryTransferDetail")
    public ModelAndView inventoryTransferDetail(Integer inventoryTransferId){
        ModelAndView mav = new ModelAndView("wms/inventory/view_inventoryTransferDetail");
        try {
            InventoryTransferVO inventoryTransferVO = inventoryTransferService.getInventoryTransferById(inventoryTransferId);
            mav.addObject("inventoryTransferVO", inventoryTransferVO);
            List<InventoryTransferDetailVO> inventoryTransferDetailVos = inventoryTransferService.getInventoryTransferDetailById(inventoryTransferId);
            mav.addObject("inventoryTransferDetailVos", inventoryTransferDetailVos);
        } catch (Exception e) {
            logger.error("/warehouse/warehouses/inventoryTransferDetail" ,e);
        }
        return mav;
    }
}
