package com.newtask.filemove;

import com.newtask.filemove.dto.CommonConstant;
import com.newtask.filemove.dto.FileMoveDto;
import com.vedeng.common.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 抽象的文件迁移接口类
 */
@Service
public abstract class AbstractFileMoveInterface implements FileMoveInterface<String> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractFileMoveInterface.class);

    @Override
    public void fileMove(String requsetParam) throws Exception{

        String commandType = getCommandType();

        //单条处理命令
        if(CommonConstant.SINGLE.equals(commandType)){

            List<FileMoveDto> fileMoveDtoList = getPendingList(requsetParam);

            if(CollectionUtils.isEmpty(fileMoveDtoList)){
                throw new Exception("无待处理的任务");
            }

            getFileMoveTask(fileMoveDtoList).run();
        }

        //批处理命令
        if(CommonConstant.BATCH.equals(commandType)){
            
            batchDealFileMove(requsetParam);
        }
    }

    protected void batchDealFileMove(String requsetParam) throws Exception{

        long startTime = DateUtil.sysTimeMillis();

        LOGGER.info("批量文件迁移------------------start,开始时间:" + DateUtil.convertString(startTime,DateUtil.TIME_FORMAT));

        ExecutorService excutorPool = getExecutorService();

        //划分任务列表
        List<Runnable> taskLists = splitTaskByParam(requsetParam);

        if(CollectionUtils.isEmpty(taskLists)){
            throw new Exception("无待处理的任务");
        }

        for(Runnable task : taskLists){
            excutorPool.submit(task);
        }

        excutorPool.shutdown();

        try {
            while(!excutorPool.isTerminated()){
                Thread.sleep(3000);
            }
        }catch (InterruptedException e){
            LOGGER.info("中止当前线程的执行," + e.getMessage());
        }finally {
            excutorPool.shutdownNow();
            while(!excutorPool.isTerminated()){
                Thread.sleep(3000);
            }
        }

        long endTime = DateUtil.sysTimeMillis();
        LOGGER.info("批量文件迁移------------------end,结束时间:" + DateUtil.convertString(endTime,DateUtil.TIME_FORMAT)+ ",总耗时:" + (endTime-startTime)+"ms");
    }

    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList) {
        return null;
    }


    /**
     * 获取命令类型
     * 1.单个命令处理
     * 2.批处理命令
     * @return
     */
    protected abstract String getCommandType();

    /**
     * 获取待处理的列表
     * @param requsetParam
     * @return
     */
    protected List<FileMoveDto> getPendingList(String requsetParam){
        return null;
    };


    /**
     * 根据参数划分任务
     * @param requsetParam
     * @return
     */
    protected List<Runnable> splitTaskByParam(String requsetParam){
        List<Runnable> taskList = new ArrayList<>();

        String[] pageParam = requsetParam.split(",");
        int start = Integer.valueOf(pageParam[0]);
        int pageSize = CommonConstant.BATCH_SIZE;
        if(pageParam.length > 1){
            pageSize = Integer.valueOf(pageParam[1]);
        }

        while (true){

            List<FileMoveDto> fileMoveList = getFileMoveInfoByPageSize(start,pageSize);

            if(CollectionUtils.isEmpty(fileMoveList)){
                break;
            }

            taskList.add(getFileMoveTask(fileMoveList));

            start = fileMoveList.get(fileMoveList.size() - 1).getId();

        }

        return taskList;
    }

    protected List<FileMoveDto> getFileMoveInfoByPageSize(int start, int pageSize) {
        return null;
    }

    /**
     * 获取执行的线程池
     * @return
     */
    protected ExecutorService getExecutorService(){
        return null;
    }

}
