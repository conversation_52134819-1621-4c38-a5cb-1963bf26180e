package com.rest.User.controller;

import com.newtask.util.TimeUtils;
import com.rest.User.controller.model.UserInfo;
import com.rest.User.controller.model.UserMenus;
import com.rest.User.controller.model.UserRoleAndPermission;
import com.rest.traderMsg.controller.TraderMsg;
import com.vedeng.authorization.model.*;
import com.vedeng.common.model.ResultInfo;

import com.vedeng.system.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;


/**
 * 对外提供用户信息的Api接口
 * <AUTHOR>
 * @description 
 * @date 2019/6/14 14:57
 */
@Slf4j
@RestController
@RequestMapping("/userCenter")
public class UserInfoMsg {

    protected static final Logger LOGGER = LoggerFactory.getLogger(TraderMsg.class);

    @Autowired
    @Qualifier("companyService")
    protected CompanyService companyService;

    @Autowired
    @Qualifier("userService")
    protected UserService userService;

    @Autowired
    @Qualifier("roleService")
    protected RoleService roleService;

    @Autowired
    @Qualifier("actionService")
    protected ActionService actionService;

    @Autowired
    @Qualifier("actiongroupService")
    protected ActiongroupService actiongroupService;

    @Autowired
    @Qualifier("positService")
    protected PositService positService;
    @Resource(name="redisTemplate")
    private RedisTemplate<String, Object> redisTemplate;


    /**
      * 获取登陆公司
      * <AUTHOR>
      * @date  2019/6/14 14:59
      * @param 
      * @return 
      */
    @RequestMapping(value = {"/getLoginCompany"}, method = RequestMethod.GET)
    public ResultInfo getLoginCompany() {

        try {

            Company company = new Company();
            company.setIsEnable(1);
            //返回有效公司
            return new ResultInfo(0, "操作成功",companyService.getCompanyList(company));

        } catch (Exception e) {
            LOGGER.error("Error sendTraderMsg", e);
            return new ResultInfo(-1, "操作失败");
        }
    }

    /**
     * 获取登陆公司
     * <AUTHOR>
     * @date  2019/6/14 14:59
     * @param loginUser 用户
     * @return
     */
    @Deprecated
    @RequestMapping(value = {"/userLogin"}, method = RequestMethod.POST)
    public ResultInfo userLogin(@RequestBody User loginUser) {

        try {

            User user = userService.login(loginUser.getUsername(),loginUser.getPassword(),loginUser.getCompanyId());
            LOGGER.info("userLogin user==="+loginUser.getUsername());
            try{
            if(user != null){

                List<Position> positions = positService.getPositionByUserId(user.getUserId());
                if(CollectionUtils.isNotEmpty(positions)){
                    //只取第一个职位
                    user.setPositType(positions.get(0).getType());
                    user.setPositLevel(positions.get(0).getLevel());
                }
            }}catch(Exception e){
                LOGGER.error("Error userLogin", e);
            }
            return new ResultInfo(0, "操作成功",user);

        } catch (Exception e) {
            LOGGER.error("Error userLogin", e);
            return new ResultInfo(-1, "操作失败");
        }
    }

    /**
     * 获取用户的角色权限
     * <AUTHOR>
     * @date  2019/6/14 14:59
     * @param loginUser 用户
     * @return
     */
    @RequestMapping(value = {"/getUserRoleAndPermission"}, method = RequestMethod.POST)
    public ResultInfo getUserRoleAndPermission(@RequestBody User loginUser) {

        try {

            List<Role> roles = roleService.getUserRoles(loginUser);
            Set<String> roleSet = new HashSet<>();
            if (CollectionUtils.isNotEmpty(roles)) {
                for (Role r : roles) {
                    roleSet.add(r.getRoleName());
                }
            }

            Set<String> permissionSet = new HashSet<>();

            List<Action> list = actionService.getActionListByUserName(loginUser);
            if (CollectionUtils.isNotEmpty(list)) {
                for (Action ac : list) {
                    if(ac.getModuleName().startsWith("http")||ac.getModuleName().startsWith("/ezlist/")){
                        permissionSet.add(ac.getModuleName());
                    }else{
                        StringBuilder sb = new StringBuilder();
                        sb.append("/").append(ac.getModuleName()).append("/").append(ac.getControllerName()).append("/").append(ac.getActionName()).append(".do");
                        permissionSet.add(sb.toString());
                    }
                }
            }

            UserRoleAndPermission user = new UserRoleAndPermission();
            user.setRoleSet(roleSet);
            user.setPermissionSet(permissionSet);

            return new ResultInfo(0, "操作成功",user);

        } catch (Exception e) {
            LOGGER.error("Error getUserRoleAndPermission", e);
            return new ResultInfo(-1, "操作失败");
        }
    }


    /**
     * 获取用户的菜单栏
     * <AUTHOR>
     * @date  2019/6/14 14:59
     * @param loginUser 用户
     * @return
     */
    @RequestMapping(value = {"/getUserMenus"}, method = RequestMethod.POST)
    public ResultInfo getUserMenus(@RequestBody User loginUser) {

        try {
            //功能分组
            List<Actiongroup> groupsList = actiongroupService.getMenuActionGroupListForApi(loginUser);
            //功能点
            List<Action> actionsList = actionService.getActionListForApi(loginUser);

            UserMenus user = new UserMenus();
            user.setGroupsList(groupsList);
            user.setActionsList(actionsList);

            return new ResultInfo(0, "操作成功",user);

        } catch (Exception e) {
            LOGGER.error("Error getUserMenus", e);
            return new ResultInfo(-1, "操作失败");
        }
    }

    /**
     * 获取当前用户的下级用户
     * <AUTHOR>
     * @date  2019/6/14 14:59
     * @param loginUser 用户
     * @return
     */
    @RequestMapping(value = {"/getSubStaff"}, method = RequestMethod.POST)
    public ResultInfo getSubStaff(@RequestBody User loginUser) {

        try {

            if(loginUser.getUserId() == null){
                throw new Exception("userId is null");
            }

            if(loginUser.getCompanyId() == null){
                throw new Exception("companyId is null");
            }

            //读取当前用户的某个职位下的所有员工
            Set<User> subUsers = userService.getSubUserListForSaleApi(loginUser);

            List<UserInfo> userForApi = new ArrayList<>();

            subUsers.forEach(e->{
                UserInfo userInfo = new UserInfo();
                userInfo.setUserId(e.getUserId());
                userInfo.setUserName(e.getUsername());
                userForApi.add(userInfo);
            });

            return new ResultInfo(0, "操作成功",userForApi);

        } catch (Exception e) {
            LOGGER.error("Error getSubStaff", e);
            return new ResultInfo(-1, "操作失败");
        }
    }

    /**
     * 是否有权限登录
     * <AUTHOR>
     * @date  2021/6/11
     * @param loginUser 用户
     * @return
     */
    @Deprecated
    @RequestMapping(value = {"/imUserIsLogin"}, method = RequestMethod.POST)
    public ResultInfo imUserIsLogin(@RequestBody ImUserDto loginUser) {
        //获取当天剩余的时间戳
        Long millSeconds = TimeUtils.getTime();
        //查询用户信息
        ImUserDto imUserDto = userService.userInfo(loginUser.getUserName());
        if(StringUtils.isEmpty(imUserDto)){
            return new ResultInfo(-1, "账号不存在或密码错误，请重新输入");
        }else if(StringUtils.isEmpty(imUserDto.getSystems())){
            return new ResultInfo(-1, "该用户没有绑定IM平台");
        }

        Object numObj = redisTemplate.opsForValue().get(loginUser.getUserName());
        if (numObj == null){
            numObj=0;
        }
            //密码输入的错误次数
            Integer num = (Integer)numObj;
            if(num>=20){
                return new ResultInfo(-1, "该账号已锁定，请联系贝登");
            }

            if(! imUserDto.getPassWord().equals( DigestUtils.md5Hex(loginUser.getPassWord() + imUserDto.getSalt() ).toString() ) ){
                num++;
                redisTemplate.opsForValue().set(loginUser.getUserName(),num,millSeconds,TimeUnit.MILLISECONDS);
                return new ResultInfo(-1, "账号不存在或密码错误，请重新输入");
            }

        String uuid = UUID.randomUUID().toString().replaceAll("-", "");
        redisTemplate.opsForValue().set("im_user_toke:" +uuid, imUserDto.getUserId(),7*24*60*60*1000, TimeUnit.MILLISECONDS);
        imUserDto.setToken("im_user_toke:" + uuid);
        imUserDto.setPassWord("");
        return new ResultInfo(0, "可以登录",imUserDto);

    }

    /**
     * IM读取用户所在的组织
     * <AUTHOR>
     * @date  2021/6/15
     * @param imUserInfoDto 用户
     * @return
     */
    @Deprecated
    @RequestMapping(value = {"/imUserInfo"}, method = RequestMethod.POST)
    public ResultInfo imUserInfo(@RequestBody  ImUserInfoDto imUserInfoDto){
        Integer userId;
        if(StringUtils.isEmpty(imUserInfoDto.getToken())){
            return new ResultInfo(-1, "用户登录失效");
        }
        try{
             userId = Integer.valueOf(redisTemplate.opsForValue().get(imUserInfoDto.getToken()).toString()) ;
        }catch (Exception e){
            log.error("【imUserInfo】处理异常",e);
            return new ResultInfo(-1, "用户登录失效");
        }


        //查询组织职位名称
        List<ImUserInfoDto> orgPositionNameList = userService.queryOrgPositionName(userId);
        StringBuffer orgPositionName = new StringBuffer();
        if( !CollectionUtils.isEmpty(orgPositionNameList)){
            orgPositionNameList.forEach(res->{
                orgPositionName.append(res.getOrgName()+"/"+res.getPositionName()+"、");
            });
        }

        String  orgPositionNameNew = orgPositionName.deleteCharAt(orgPositionName.length()-1).toString();

        //根据用户id查询用户信息
        ImUserInfoDto ImUserInfo = userService.findImUserInfo(userId);
        if(StringUtils.isEmpty(ImUserInfo)){
            return new ResultInfo(-1,"该用户暂时没有组织");
        }
        String roleName = userService.findRoleName(ImUserInfo.getUserId());
        ImUserInfo.setRoleName(roleName);
        ImUserInfo.setOrgPositionName(orgPositionNameNew);
        ImUserInfo.setToken(imUserInfoDto.getToken());
        return new ResultInfo(0, "成功",ImUserInfo);
    }

    /**
     * IM登出接口
     * <AUTHOR>
     * @date  2021/6/17
     * @param   用户
     * @return
     */
    @Deprecated
    @RequestMapping(value = {"/imLonginOut"}, method = RequestMethod.POST)
    public ResultInfo imLonginOut(@RequestBody  ImUserInfoDto imUserInfoDto){
        if(StringUtils.isEmpty(imUserInfoDto)){
            return new ResultInfo(-1, "登出失败");
        }

        if(StringUtils.isEmpty(imUserInfoDto.getToken())){
            return new ResultInfo(-1, "登出失败");
        }
        try {
            redisTemplate.delete(imUserInfoDto.getToken());
        }catch (Exception e){
            log.error("【imLonginOut】处理异常",e);
            return new ResultInfo(-1, "登出失败");
        }
        return new ResultInfo(0, "登出成功");
    }
}
