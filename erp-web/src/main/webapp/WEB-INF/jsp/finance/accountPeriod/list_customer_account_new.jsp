<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="客户账期记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/finance/accountPeriod/list_customer_account.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" href="<%= basePath %>static/css/select2.css" />
<script type="text/javascript" src='<%= basePath %>static/js/select2.js'></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2_locale_zh-CN.js'></script>
<div class="main-container">
    <div class="list-pages-search">
        <form method="post" id="search" action="<%=basePath%>finance/accountperiod/getCustomerAccountListPage.do">
            <ul>
                <li>
                    <label class="infor_name">客户名称</label>
                    <input type="text" class="input-middle" name="traderName" id="traderName" value="${ap.traderName}" />
                </li>
                <li>
                    <label class="infor_name">归属销售</label>
                    <select class="input-middle" name="traderUserId" id="traderUserId">
                        <option value="">全部</option>
                        <c:forEach var="list" items="${ascriptionUserList}" varStatus="num">
                            <option <c:if test="${ap.traderUserId eq list.userId}">selected</c:if> value="${list.userId}">${list.username}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">逾期状态</label>
                    <select class="input-middle" name="overdueState" id="overdueState">
                        <option value="">全部</option>
                        <option value="1" <c:if test="${ap.overdueState eq 1}">selected</c:if>>未开始</option>
                        <option value="2" <c:if test="${ap.overdueState eq 2}">selected</c:if>>未逾期</option>
                        <option value="3" <c:if test="${ap.overdueState eq 3}">selected</c:if>>部分逾期</option>
                        <option value="4" <c:if test="${ap.overdueState eq 4}">selected</c:if>>全部逾期</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">订单号</label>
                    <input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${ap.saleorderNo}" />
                </li>
                <li>
                    <label class="infor_name">
                        订单生效日期
                    </label>
                    <input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="startTime"	value="${startTime}" id="startTime">
                    <div class="f_left ml1 mr1 mt4">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off" onClick="WdatePicker()" name="endTime" value="${endTime}" id="endTime">
                </li>
                <li>
                    <label class="infor_name">账期类型</label>
                    <select class="input-middle" name="billType" id="billType">
                        <option value="">全部</option>
                        <option value="1" <c:if test="${ap.billType eq 1}">selected</c:if>>正式账期</option>
                        <option value="2" <c:if test="${ap.billType eq 2}">selected</c:if>>临时账期</option>
                        <option value="3" <c:if test="${ap.billType eq 3}">selected</c:if>>订单账期</option>
                    </select>
                </li>
                <li>
                    <label class="infor_name">归属部门</label>
                    <input type="hidden" id="departmentInfoList" value="${departmentInfoList}"/>
                    <select class="" style="width: 178px;" multiple="multiple" name="departmentInfo" id="departmentInfo">
                        <option value=""></option>
                        <c:forEach items="${orgList}" var="org">
                            <option value="${org.orgId}">${org.orgName}</option>
                        </c:forEach>
                    </select>
                </li>
                <li>
                    <label class="infor_name">账期归还</label>
                    <select class="input-middle" name="billNotPaid" id="billNotPaid">
                        <option value="">全部</option>
                        <option value="1" <c:if test="${ap.billNotPaid eq 1}">selected</c:if>>是</option>
                        <option value="2" <c:if test="${ap.billNotPaid eq 2}">selected</c:if>>否</option>
                    </select>
                </li>
            </ul>
            <input type="hidden" value="${ezDomain}" id="ezDomain">
            <input type="hidden" value="${customerExport[0]}" id="ezId">
            <div class="tcenter renderData">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="exportCustomerAccount();">导出</span>
                <%--<td title="客户账期记录导出" id="traderThirdLevelLicense" redirect="${ezDomain}/ezlist/list/list.html?pageId=320">导出</td>--%>
            </div>
        </form>
    </div>
    <div class="list-page">
        <div class="fixdiv">
            <div class="superdiv">
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                    <tr>
                        <th class="wid4">序号</th>
                        <th class="wid18">客户名称</th>
                        <th class="wid10">归属销售</th>
                        <th class="wid10">归属部门</th>
                        <th class="wid10">账期类型</th>
                        <th class="wid10">账期额度</th>
                        <th class="wid10">账期可用额度</th>
                        <th class="wid10">账期有效期</th>
                        <th class="wid10">订单号</th>
                        <th class="wid10">订单生效日期</th>
                        <th class="wid10">结算标准</th>
                        <th class="wid10">订单原金额</th>
                        <th class="wid10">订单实际金额</th>
                        <th class="wid10">账期使用额</th>
                        <th class="wid10">账期占用额</th>
                        <th class="wid10">账期冻结额</th>
                        <th class="wid10">质保金</th>
                        <th class="wid10">已归还账期额</th>
                        <th class="wid10">未归还账期额</th>
                        <th class="wid10">逾期未还金额</th>
                        <th class="wid10">逾期次数</th>
                        <th class="wid10">逾期天数</th>
                       <%-- <th class="wid10">未监管账期额</th>--%>
                        <th class="wid10">逾期状态</th>
                        <th class="wid10">操作</th>
                    </tr>
                    </thead>
                    <tbody>
                    <c:set var="accountAmountPage" value="0"></c:set><!-- 使用帐期总额 -->
                    <c:set var="unReturnedAmountPage" value="0"></c:set><!-- 使用帐期总额 -->
                    <c:set var="unReturnedOverDueAmountPage" value="0"></c:set><!--逾期未还金额-->
                    <c:forEach var="list" items="${customerAccountList}" varStatus="num">
                        <tr>
                            <td>
                                    ${num.count}
                            </td>
                            <td>
                                <a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewcustomer${list.traderId}", "link":"./trader/customer/baseinfo.do?traderId=${list.traderId}", "title":"客户信息"}'><c:if test="${empty list.traderName}">-</c:if>${list.traderName}</a>
                            </td>
                            <td>
                                <c:forEach var="user" items="${userList}" varStatus="statu">
                                    <c:if test="${list.traderId eq user.traderId}">${user.username}</c:if>
                                </c:forEach>
                            </td>
                            <td>
                                <c:forEach var="orgNames" items="${orgNameList}" >
                                    <c:if test="${list.orgId eq orgNames.orgId}">
                                        ${orgNames.orgName}
                                        <c:if test="${empty orgNames.orgName}">-</c:if>
                                    </c:if>
                                </c:forEach>
                            </td>
                                <%--账期类型--%>
                            <td>
                                <c:choose>
                                    <c:when test="${empty list.billPeriodType}">-</c:when>
                                    <c:when test="${ list.billPeriodType == 1}">正式账期</c:when>
                                    <c:when test="${ list.billPeriodType == 2}">临时账期</c:when>
                                    <c:when test="${ list.billPeriodType == 3}">订单账期</c:when>
                                </c:choose>
                            </td>
                            <td><c:if test="${empty list.creditTotalAmount}">-</c:if>
                                <fmt:formatNumber type="number" value="${list.creditTotalAmount}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td><c:if test="${empty list.creditUsableAmount}">0.00</c:if>
                                <fmt:formatNumber type="number" value="${list.creditUsableAmount}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${empty list.billPeriodEnd}">-</c:when>
                                    <c:when test="${not empty list.billPeriodType && list.billPeriodType == 3}">-</c:when>
                                    <c:otherwise><date:date value="${list.billPeriodEnd}" format="yyyy-MM-dd"/></c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:choose>
                                   <c:when test="${empty roleFinance}"><a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewsaleorder${list.orderId}","link":"/finance/invoice/viewSaleorder.do?saleorderId=${list.orderId}","title":"订单信息"}'><c:if test="${empty list.saleorderNo}">-</c:if>${list.saleorderNo}</a></c:when>
                                    <c:otherwise><a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewsaleorder${list.orderId}","link":"/order/saleorder/view.do?saleorderId=${list.orderId}","title":"订单信息"}'><c:if test="${empty list.saleorderNo}">-</c:if>${list.saleorderNo}</a></c:otherwise>
                                </c:choose>
                            </td>
                            <td><c:if test="${empty list.validTime}">-</c:if> <date:date value="${list.validTime}" format="yyyy-MM-dd"/></td>
                            <td>
                                <c:choose>
                                    <c:when test="${empty list.billPeriodSettlementType}">
                                        产品开票
                                    </c:when>
                                    <c:when test="${list.billPeriodSettlementType == 1}">
                                        产品发货
                                    </c:when>
                                    <c:otherwise>
                                        产品开票
                                    </c:otherwise>
                                </c:choose>
                            </td>
                            <td>
                                <c:if test="${empty list.totalAmount}">-</c:if>
                                <c:set var="orderAmountPage" value="${list.totalAmount + orderAmountPage}"></c:set>
                                <fmt:formatNumber type="number" value="${list.totalAmount}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:if test="${empty list.realTotalAmount}">-</c:if>
                                <fmt:formatNumber type="number" value="${list.realTotalAmount}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:if test="${empty list.periodAmountUsed}">
                                    -
                                </c:if>
                                <c:set var="accountAmountPage" value="${list.periodAmountUsed + accountAmountPage}"></c:set>
                                <fmt:formatNumber type="number" value="${list.periodAmountUsed}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:if test="${empty list.periodAmountOccupy}">
                                    -
                                </c:if>
                                <fmt:formatNumber type="number" value="${list.periodAmountOccupy}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                                <%--账期冻结额，暂时跳过--%>
                            <td>
                                <c:if test="${empty list.periodAmountFreeze}">
                                    -
                                </c:if>
                                <fmt:formatNumber type="number" value="${list.periodAmountFreeze}" pattern="0.00" maxFractionDigits="2" />
                            </td>
                            <td>
                                <c:choose>
                                    <c:when test="${empty list.retentionMoney || not empty list.billPeriodTypeFirst}">-</c:when>
                                    <c:otherwise>${list.retentionMoney}</c:otherwise>
                                </c:choose>
                            </td>
                                <%--已规还--%>
                            <td>
                                <div class="title-click  pop-new-data" layerParams='{"width":"600px","height":"380px","title":"归还明细","link":"/finance/accountperiod/returnDetailsView.do?billPeriodId=${list.billPeriodId}&relatedId=${list.orderId}&parentUseDetailId=${list.billPeriodUseDetailId}"}'><c:if test="${empty list.returnedAmount}">-</c:if><fmt:formatNumber type="number" value="${list.returnedAmount}" pattern="0.00" maxFractionDigits="2" /></div>
                            </td>

                            <td>
                               <c:if test="${empty list.unReturnedAmount}">-</c:if>
                                <fmt:formatNumber type="number" value="${list.unReturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                                <c:set var="unReturnedAmountPage" value="${list.unReturnedAmount + unReturnedAmountPage}"></c:set>
                            </td>
                            <td>
                                <c:if test="${empty list.unReturnedOverDueAmount}">-</c:if>
                                <fmt:formatNumber type="number" value="${list.unReturnedOverDueAmount}" pattern="0.00" maxFractionDigits="2" />
                                <c:set var="unReturnedOverDueAmountPage" value="${list.unReturnedOverDueAmount + unReturnedOverDueAmountPage}"></c:set>
                            </td>
                            <td>
                                <c:if test="${empty list.countOfOverDue}">-</c:if>
                                ${list.countOfOverDue}
                            </td>
                            <td>
                                <c:if test="${empty list.daysOfOverdue}">-</c:if>
                                ${list.daysOfOverdue}
                            </td>

                            <%--未监管--%>
                           <%-- <td>
                                <c:if test="${empty list.unSuperViseAmount}">-</c:if>
                                <fmt:formatNumber type="number" value="${list.unSuperViseAmount}" pattern="0.00" maxFractionDigits="2" />
                            </td>--%>
                            <td>
                                <%--逾期状态--%>
                                <c:choose>
                                    <c:when test="${empty list.overdueState}">-</c:when>
                                    <c:when test="${list.overdueState==1}">未开始</c:when>
                                    <c:when test="${list.overdueState==2}">未逾期</c:when>
                                    <c:when test="${list.overdueState==3}">部分逾期</c:when>
                                    <c:when test="${list.overdueState==4}">全部逾期</c:when>
                                </c:choose>
                            </td>
                            <td>
                                <div class="title-click  pop-new-data" layerParams='{"width":"90%","height":"90%","title":"查看监管明细","link":"/finance/accountperiod/billPeriodDetailsView.do?parentUseDetailId=${list.billPeriodUseDetailId}&deliveryDirect=${list.deliveryDirect}"}'>查看监管明细</div>
                            </td>
                        </tr>
                    </c:forEach>
                    <c:if test="${empty customerAccountList}">
                        <tr>
                            <td colspan="25">
                                <!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
                            </td>
                        </tr>
                    </c:if>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="clear"></div>
        <tags:page page="${page}" />
        <div class="clear"></div>
        <div class="fixtablelastline">
            【全部结果 订单数：${orderNum != null ? orderNum : 0} 订单总额：<fmt:formatNumber type="number" value="${totalAmount == null ? 0 : totalAmount}" pattern="0.00" maxFractionDigits="2" /> 使用帐期总额：<fmt:formatNumber type="number" value="${periodUsedAmount == null ? 0 : periodUsedAmount}" pattern="0.00" maxFractionDigits="2" /> 未归还账期额：<fmt:formatNumber type="number" value="${unReturnedAmount == null ? 0 : unReturnedAmount}" pattern="0.00" maxFractionDigits="2" /> 逾期未还金额: <fmt:formatNumber type="number" value="${unReturnedOverDueAmount == null ? 0 : unReturnedOverDueAmount}" pattern="0.00" maxFractionDigits="2" />】
            【本页统计 订单数：${orderNumPage != null ? orderNumPage : 0} 订单总额：<fmt:formatNumber type="number" value="${orderTotalAmount}" pattern="0.00" maxFractionDigits="2" /> 使用帐期总额：<fmt:formatNumber type="number" value="${accountAmountPage}" pattern="0.00" maxFractionDigits="2" /> 未归还账期额：<fmt:formatNumber type="number" value="${unReturnedAmountPage}" pattern="0.00" maxFractionDigits="2" /> 逾期未还金额: <fmt:formatNumber type="number" value="${unReturnedOverDueAmountPage}" pattern="0.00" maxFractionDigits="2" /> 】
        </div>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>
