package com.vedeng.api.standard.template;

import com.vedeng.api.standard.converter.ResponseMappingConfig;
import com.vedeng.api.standard.core.ApiRequest;
import com.vedeng.api.standard.validation.ValidationRule;

import java.util.Map;

/**
 * 业务执行构建器
 * 提供链式调用的业务执行配置和执行功能
 * 
 * @param <T> 请求类型
 * @param <R> 响应类型
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class BusinessExecutionBuilder<T, R> {
    
    private final BusinessTemplate businessTemplate;
    private final ApiRequest originalRequest;
    private final BusinessStepConfig stepConfig;
    
    /**
     * 构造函数
     * 
     * @param businessTemplate 业务模板
     * @param originalRequest 原始请求
     */
    public BusinessExecutionBuilder(BusinessTemplate businessTemplate, ApiRequest originalRequest) {
        this.businessTemplate = businessTemplate;
        this.originalRequest = originalRequest;
        this.stepConfig = new BusinessStepConfig();
    }
    
    /**
     * 设置请求类型
     * 
     * @param requestType 请求类型
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> requestType(Class<T> requestType) {
        stepConfig.setRequestType(requestType);
        return this;
    }
    
    /**
     * 设置响应类型
     * 
     * @param responseType 响应类型
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> responseType(Class<R> responseType) {
        stepConfig.setResponseType(responseType);
        return this;
    }
    
    /**
     * 设置验证规则
     *
     * @param validationRules 验证规则类数组
     * @return 当前构建器
     */
    @SafeVarargs
    @SuppressWarnings({"rawtypes", "unchecked"})
    public final BusinessExecutionBuilder<T, R> validationRules(Class<? extends ValidationRule>... validationRules) {
        stepConfig.setValidationRules(validationRules);
        return this;
    }



    /**
     * 设置 Controller 信息（显式指定，避免从 URL 解析）
     *
     * @param controllerBeanName Controller Bean 名称
     * @param methodName 方法名
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> controller(String controllerBeanName, String methodName) {
        stepConfig.setControllerBeanName(controllerBeanName);
        stepConfig.setControllerMethodName(methodName);
        return this;
    }
    
    /**
     * 设置调用参数
     *
     * @param callParams 调用参数
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> callParams(Map<String, Object> callParams) {
        stepConfig.setCallParams(callParams);
        return this;
    }

    /**
     * 设置自定义方法参数配置（支持多参数Controller方法）
     *
     * @param parameterTypes 参数类型数组
     * @param parameterValues 参数值数组（可包含占位符）
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> customParameters(Class<?>[] parameterTypes, Object[] parameterValues) {
        stepConfig.setCustomParameterTypes(parameterTypes);
        stepConfig.setCustomParameterValues(parameterValues);
        return this;
    }

    /**
     * 包含HttpServletRequest参数的方法调用
     *
     * 适用于方法签名包含HttpServletRequest参数的情况：
     * - Controller方法：saveAddBuyorder(HttpServletRequest request, BuyorderVo buyorderVo)
     * - 历史Service方法：processData(HttpServletRequest request, String data, Integer id)
     * - 支持请求参数：ParameterConfig.requestParam("paramName", value) 用于request.getParameter()
     *
     * @param parameterConfig 业务参数配置（不包含HttpServletRequest，会自动添加）
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> withHttpParameters(ParameterConfig... parameterConfig) {
        // 存储原始参数配置，用于延迟解析
        stepConfig.setOriginalParameterConfigs(parameterConfig);
        stepConfig.setIncludeHttpRequest(true);

        // 分离请求参数和业务参数
        java.util.List<ParameterConfig> businessParams = new java.util.ArrayList<>();
        java.util.Map<String, Object> requestParams = new java.util.HashMap<>();

        for (ParameterConfig config : parameterConfig) {
            if (config.isRequestParameter()) {
                // 请求参数：添加到请求参数Map中
                requestParams.put(config.getRequestParameterName(), config.getValue());
            } else if (!config.isFromValidationContext()) {
                // 非验证上下文参数：添加到业务参数列表中
                businessParams.add(config);
            }
            // 验证上下文参数将在执行时动态解析
        }

        // 参数类型数组：HttpServletRequest + 业务参数
        Class<?>[] types = new Class<?>[businessParams.size() + 1]; // +1 for HttpServletRequest
        types[0] = javax.servlet.http.HttpServletRequest.class;
        for (int i = 0; i < businessParams.size(); i++) {
            types[i + 1] = businessParams.get(i).getType();
        }

        // 自定义参数数组：只包含业务参数，HttpServletRequest会自动注入
        Object[] customValues = new Object[businessParams.size()];
        for (int i = 0; i < businessParams.size(); i++) {
            customValues[i] = businessParams.get(i).getValue();
        }

        // 设置参数类型和自定义参数
        stepConfig.setCustomParameterTypes(types);
        stepConfig.setCustomParameterValues(customValues);

        // 设置请求参数（用于request.getParameter()）
        if (!requestParams.isEmpty()) {
            stepConfig.setCallParams(requestParams);
        }

        return this;
    }

    /**
     * 不包含HttpServletRequest参数的方法调用
     *
     * 适用于方法签名不包含HttpServletRequest参数的情况：
     * - 现代Service方法：getBySaleOrderId(Integer saleOrderId)
     * - 纯业务逻辑方法：calculateAmount(BigDecimal price, Integer quantity)
     *
     * @param parameterConfig 业务参数配置
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> withoutHttpParameters(ParameterConfig... parameterConfig) {
        // 存储原始参数配置，用于延迟解析
        stepConfig.setOriginalParameterConfigs(parameterConfig);
        stepConfig.setIncludeHttpRequest(false);

        // 过滤出非验证上下文参数
        java.util.List<ParameterConfig> nonContextParams = new java.util.ArrayList<>();
        for (ParameterConfig config : parameterConfig) {
            if (!config.isFromValidationContext()) {
                nonContextParams.add(config);
            }
        }

        // 参数类型数组：只包含非验证上下文的业务参数
        Class<?>[] types = new Class<?>[nonContextParams.size()];
        for (int i = 0; i < nonContextParams.size(); i++) {
            types[i] = nonContextParams.get(i).getType();
        }

        // 自定义参数数组：只包含非验证上下文的业务参数
        Object[] customValues = new Object[nonContextParams.size()];
        for (int i = 0; i < nonContextParams.size(); i++) {
            customValues[i] = nonContextParams.get(i).getValue();
        }

        // 设置参数类型和自定义参数
        stepConfig.setCustomParameterTypes(types);
        stepConfig.setCustomParameterValues(customValues);
        return this;
    }


    /**
     * 设置响应映射配置
     * 
     * @param responseConfig 响应配置
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> responseConfig(ResponseMappingConfig responseConfig) {
        stepConfig.setResponseConfig(responseConfig);
        return this;
    }
    
    /**
     * 跳过验证步骤
     * 
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> skipValidation() {
        stepConfig.setSkipValidation(true);
        return this;
    }
    

    /**
     * 设置验证上下文
     * 
     * @param validationContext 验证上下文
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> validationContext(Map<String, Object> validationContext) {
        stepConfig.setValidationContext(validationContext);
        return this;
    }
    

    /**
     * 启用幂等性异常统一处理
     *
     * @param businessType 业务类型名称
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> withIdempotencyHandling(String businessType) {
        stepConfig.setIdempotencyHandlingEnabled(true);
        stepConfig.setBusinessTypeName(businessType);
        return this;
    }

    /**
     * 启用幂等性异常统一处理（自动从请求中推断业务类型）
     *
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> withIdempotencyHandling() {
        stepConfig.setIdempotencyHandlingEnabled(true);
        return this;
    }

    /**
     * 启用幂等性异常统一处理，并指定ID和编号字段名
     *
     * @param businessType 业务类型名称
     * @param idFieldName ID字段名（用于从响应中提取文档ID，可以为null表示不提取）
     * @param noFieldName 编号字段名（用于从响应中提取文档编号，可以为null表示不提取）
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> withIdempotencyHandling(String businessType,
                                                                 String idFieldName,
                                                                 String noFieldName) {
        stepConfig.setIdempotencyHandlingEnabled(true);
        stepConfig.setBusinessTypeName(businessType);
        stepConfig.setIdempotencyIdField(idFieldName);
        stepConfig.setIdempotencyNoField(noFieldName);
        return this;
    }

    /**
     * 启用幂等性异常统一处理（仅幂等性保护，不提取业务单据字段）
     * 适用于审核、删除等不需要存储业务单据信息的接口
     *
     * @param businessType 业务类型名称
     * @return 当前构建器
     */
    public BusinessExecutionBuilder<T, R> withIdempotencyHandlingOnly(String businessType) {
        stepConfig.setIdempotencyHandlingEnabled(true);
        stepConfig.setBusinessTypeName(businessType);
        stepConfig.setIdempotencyIdField(null); // 不提取ID字段
        stepConfig.setIdempotencyNoField(null); // 不提取编号字段
        return this;
    }
    
    /**
     * 执行业务逻辑
     * 
     * @return 执行结果
     * @throws Exception 执行异常
     */
    @SuppressWarnings("unchecked")
    public R execute() throws Exception {
        return (R) businessTemplate.executeWithConfig(originalRequest, stepConfig);
    }
    
    /**
     * 获取步骤配置（用于调试和测试）
     * 
     * @return 步骤配置
     */
    public BusinessStepConfig getStepConfig() {
        return stepConfig;
    }
}
