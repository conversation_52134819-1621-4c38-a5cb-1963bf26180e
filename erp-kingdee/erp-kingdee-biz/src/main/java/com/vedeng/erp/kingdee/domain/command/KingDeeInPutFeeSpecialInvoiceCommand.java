package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 金蝶进项 专项 实际接受参数类
 * @date 2022/11/09 10:11
 */
@Data
public class KingDeeInPutFeeSpecialInvoiceCommand {

    /**
     * 单据内码
     */
    private String FID;
    /**
     * 贝登erp对应的单据头ID
     */
    private String F_QZOK_BDDJTID;
    /**
     * 发票号
     */
    private String FINVOICENO;
    /**
     * 发票代码
     */
    private String F_QZOK_FPDM;
    /**
     * 发票日期  2022-09-07 00:00:00
     */
    private String FINVOICEDATE;
    /**
     * 业务日期 2022-09-07 00:00:00
     */
    private String FDATE;
    /**
     * 往来单位类型 BD_Supplier
     */
    private String FCONTACTUNITTYPE;
    /**
     * 单据类型
     */
    private KingDeeNumberCommand FBillTypeID = new KingDeeNumberCommand();
    /**
     * 供应商
     */
    private KingDeeNumberCommand FSUPPLIERID = new KingDeeNumberCommand();

    /**
     * 结算组织
     */
    private KingDeeNumberCommand FSETTLEORGID = new KingDeeNumberCommand();

    /**
     * 采购组织
     */
    private KingDeeNumberCommand FPURCHASEORGID = new KingDeeNumberCommand();

    /**
     * 单据状态
     */
    private String FDOCUMENTSTATUS;
    /**
     * 红蓝字标识
     */
    private String fRedBlue;

    /**
     * 发票明细
     */
    private List<InPutFeeSpecialInvoiceDetailCommand> FPUREXPINVENTRY = new ArrayList<>();

    @Getter
    @Setter
    public static class InPutFeeSpecialInvoiceDetailCommand{
        /**
         * 费用编码
         */
        private KingDeeNumberCommand FEXPENSEID = new KingDeeNumberCommand();
        /**
         * 数量
         */
        private String FQty;
        /**
         * 含税单价
         */
        private BigDecimal FUNITPRICE;
        /**
         * 税率
         */
        private String FTAXRATE;
        /**
         * 贝登订单行ID
         */
        private String F_QZOK_BDDJHID;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 贝登SKU
         */
        private String F_QZOK_BDSKU;

        /**
         * 源单类型 源单是应付单：AP_Payable
         */
        private String FSOURCETYPE;

        /**
         * 关联关系表
         */
        private List<InPutFeeSpecialInvoiceDetailLinkCommand> FPUREXPINVENTRY_Link = new ArrayList<>();
    }

    @Getter
    @Setter
    public static class InPutFeeSpecialInvoiceDetailLinkCommand{
        /**
         * 实体主键
         */
        private String FLinkId;
        /**
         *
         */
        private String FPUREXPINVENTRY_Link_FFlowId;
        /**
         *
         */
        private String FPUREXPINVENTRY_Link_FFlowLineId;
        /**
         * 转换规则 源单是应付单：IV_PayableToPUREXVATIN
         */
        private String FPUREXPINVENTRY_Link_FRuleId;
        /**
         * 源单表内码
         */
        private String FPUREXPINVENTRY_Link_FSTableId;
        /**
         * 源单表 源单是应付单：T_AP_PAYABLEENTRY
         */
        private String FPUREXPINVENTRY_Link_FSTableName;
        /**
         *  源单内码 源单的表头表单的内码 费用应付单:金蝶应付单表头ID
         */
        private String FPUREXPINVENTRY_Link_FSBillId;
        /**
         * 源单分录内码 源单的表体行内码 费用应付单:金蝶应付单行ID
         */
        private String FPUREXPINVENTRY_Link_FSId;
        /**
         * 原始携带金额 费用发票价税合计，如果是红字发票，则数量为负数
         */
        private BigDecimal FPUREXPINVENTRY_Link_FAMOUNTFOR_DOLD;
        /**
         * 修改携带量（实开金额 费用发票价税合计，如果是红字发票，则数量为负数
         */
        private BigDecimal FPUREXPINVENTRY_Link_FAMOUNTFOR_D;
    }

}
