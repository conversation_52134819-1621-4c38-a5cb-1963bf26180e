.detail-wrap .info-pic {
    display: flex; }
.detail-wrap .info-pic .info-pic-item {
    width: 50px;
    margin-right: 10px;
    height: 50px;
    border: 1px solid #EDF0F2;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative; }
.detail-wrap .info-pic .info-pic-item:last-child {
    margin-right: 0; }
.detail-wrap .info-pic .info-pic-item img {
    max-width: 100%;
    max-height: 100%; }
.detail-wrap .info-pic .info-pic-item:after {
    content: '';
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, 0.5);
    cursor: pointer;
    display: none; }
.detail-wrap .info-pic .info-pic-item:before {
    content: '\e90e';
    font-family: 'HC' !important;
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 16px;
    color: #fff;
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    align-items: center;
    justify-content: center;
    z-index: 1;
    cursor: pointer;
    display: none; }
.detail-wrap .info-pic .info-pic-item:hover:before {
    display: flex; }
.detail-wrap .info-pic .info-pic-item:hover::after {
    display: block; }

.vd-large-pic-wrap .vd-large-pic-mask {
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    position: fixed;
    top: 0;
    left: 0;
}

.vd-large-pic-wrap .vd-large-pic-cnt {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
}

.vd-large-pic-wrap .vd-large-pic-cnt img {
    position: absolute;
    top: 0;
    margin: auto;
    left: 0;
    right: 0;
    bottom: 0;
    max-height: 100%;
    max-width: 100%;
}