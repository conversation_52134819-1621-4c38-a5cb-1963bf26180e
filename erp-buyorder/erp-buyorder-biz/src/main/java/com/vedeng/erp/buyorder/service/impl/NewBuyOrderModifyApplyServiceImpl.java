package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.erp.buyorder.domain.entity.BuyorderModifyApplyExpenseGoodsEntity;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseItemDto;
import com.vedeng.erp.buyorder.mapper.BuyorderModifyApplyExpenseGoodsMapper;
import com.vedeng.erp.buyorder.service.NewBuyOrderModifyApplyService;
import com.vedeng.erp.buyorder.service.NewBuyOrderService;
import com.vedeng.erp.buyorder.common.utils.NewBuyOrderUtils;
import com.vedeng.order.dao.*;
import com.vedeng.order.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.buyorder.service.impl
 * @Date 2021/11/2 9:13
 */
@Service
@Slf4j
public class NewBuyOrderModifyApplyServiceImpl implements NewBuyOrderModifyApplyService {

    @Resource
    private BuyOrderModifyApplyMapper buyorderModifyApplyMapper;

    @Resource
    private BuyorderModifyApplyGoodsMapper buyorderModifyApplyGoodsMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Autowired
    private NewBuyOrderService newBuyOrderService;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private OrderNoDict orderNoDict;

    @Autowired
    private BuyorderModifyApplyExpenseGoodsMapper buyorderModifyApplyExpenseGoodsMapper;

    @Transactional
    @Override
    public Integer saveBuyOrderEditApply(BuyorderModifyApply buyorderModifyApply, List<BuyorderExpenseItemDto> buyOrderExpenseGoodsList) {
        Objects.requireNonNull(buyorderModifyApply);

        if (buyorderModifyApplyMapper.insertSelective(buyorderModifyApply) == 0) {
            log.info("保存采购单修改申请记录错误" + buyorderModifyApply);
            throw new IllegalStateException("保存采购单修改申请记录错误");
        }

        BigDecimal totalAmount = new BigDecimal(0);

        if (buyorderModifyApply.getBuyorderModifyApplyGoodsList().size() > 0) {
            // 将前端传来的string转化为 时间戳格式存表
            for (BuyorderModifyApplyGoods buyorderModifyApplyGoods : buyorderModifyApply.getBuyorderModifyApplyGoodsList()) {
                buyorderModifyApplyGoods.setBuyorderModifyApplyId(buyorderModifyApply.getBuyorderModifyApplyId());
                buyorderModifyApplyGoods.setSendGoodsTime(NewBuyOrderUtils.getTimeStamp(buyorderModifyApplyGoods.getSendGoodsTimeShow()));
                buyorderModifyApplyGoods.setOldSendGoodsTime(NewBuyOrderUtils.getTimeStamp(buyorderModifyApplyGoods.getOldSendGoodsTimeShow()));
                buyorderModifyApplyGoods.setReceiveGoodsTime(NewBuyOrderUtils.getTimeStamp(buyorderModifyApplyGoods.getReceiveGoodsTimeShow()));
                buyorderModifyApplyGoods.setOldReceiveGoodsTime(NewBuyOrderUtils.getTimeStamp(buyorderModifyApplyGoods.getOldReceiveGoodsTimeShow()));
                buyorderModifyApplyGoods.setIsNew(1);

                // VDERP-8808计算采购单总金额
                Integer num = buyorderModifyApplyGoods.getNum();
                BigDecimal multiply = buyorderModifyApplyGoods.getPrice().multiply(new BigDecimal(Integer.parseInt(num.toString())));
                totalAmount = totalAmount.add(multiply);
            }
            buyorderModifyApplyGoodsMapper.batchInsertSelective(buyorderModifyApply.getBuyorderModifyApplyGoodsList());
        }

        // 存储虚拟商品信息
        if (buyOrderExpenseGoodsList != null && buyOrderExpenseGoodsList.size() > 0) {
            Integer buyorderModifyApplyId = buyorderModifyApply.getBuyorderModifyApplyId();
            buyOrderExpenseGoodsList.forEach(buyorderExpenseItemDto -> {
                BuyorderModifyApplyExpenseGoodsEntity buyorderModifyApplyExpenseGoodsEntity = new BuyorderModifyApplyExpenseGoodsEntity();
                buyorderModifyApplyExpenseGoodsEntity.setBuyorderModifyApplyId(buyorderModifyApplyId);
                buyorderModifyApplyExpenseGoodsEntity.setBuyorderExpenseItemId(buyorderExpenseItemDto.getBuyorderExpenseItemId());
                buyorderModifyApplyExpenseGoodsEntity.setInsideComments(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getInsideComments());
                buyorderModifyApplyExpenseGoodsEntity.setOldInsideComments(buyorderExpenseItemDto.getBuyorderExpenseItemDetailDto().getOldInsideComments());
                buyorderModifyApplyExpenseGoodsMapper.insertSelective(buyorderModifyApplyExpenseGoodsEntity);
            });
        }

        BuyorderModifyApply bma = new BuyorderModifyApply();
        bma.setBuyorderModifyApplyId(buyorderModifyApply.getBuyorderModifyApplyId());
        bma.setBuyorderModifyApplyNo(orderNoDict.getOrderNum(buyorderModifyApply.getBuyorderModifyApplyId(), 15));
        bma.setTotalAmount(totalAmount);
        if (buyorderModifyApplyMapper.updateByPrimaryKeySelective(bma) == 0) {
            log.info("更新采购单修改申请记录错误" + buyorderModifyApply);
            throw new IllegalStateException("更新采购单修改申请记录错误");
        }

        // 锁定采购单
        newBuyOrderService.updateBuyOrderLockStatus(buyorderModifyApply.getBuyorderId(), true);
        return buyorderModifyApply.getBuyorderModifyApplyId();
    }


    @Override
    public void checkChangeDeliveryType(Integer type, Integer buyorderId) {
        Buyorder bv = buyorderMapper.selectByPrimaryKey(buyorderId);
        if (bv.getDeliveryStatus() != 0) {
            throw new IllegalArgumentException("采购单已发货，不可修改发货方式");
        } else if (bv.getArrivalStatus() != 0) {
            throw new IllegalArgumentException("采购单已收货，不可修改发货方式");
        }

        // 先校验采购单是否只关联了一个销售单
        List<Integer> saleOrderIdList = buyorderMapper.getSaleorderIdListByBuyorderId(buyorderId);
        if (type == 1 && saleOrderIdList.size() != 1) { //普发改直发
            throw new IllegalArgumentException("采购单关联的销售单数量不为1，不可修改发货方式");
        }

        Saleorder saleorder = saleorderMapper.selectByPrimaryKey(saleOrderIdList.get(0));
        if (saleorder.getOrderType() == 2) {
            throw new IllegalArgumentException("采购单关联的销售单类型为备货单，不可修改发货方式");
        }

        List<Integer> lockedStatusList = saleorderMapper.getLockedStatusBySaleorderIds(saleOrderIdList);
        if (lockedStatusList.contains(ErpConst.ONE)) {
            throw new IllegalArgumentException("采购单关联的销售单已锁定，不可修改发货方式");
        }
        // 查询采购单和销售单中每个sku对应的数量(除去运费等特殊商品)
        List<BuyorderGoods> buyorderGoodsList = buyorderMapper.getBuyOrderGoodsExceptFreight(buyorderId);
        List<SaleorderGoods> saleorderGoodsList = saleorderMapper.getSaleOrderGoodsExceptFreight(saleOrderIdList.get(0));
        HashMap<String, Integer> buyorderGoods = new HashMap<>();
        HashMap<String, Integer> saleorderGoods = new HashMap<>();
        buyorderGoodsList.forEach(item -> buyorderGoods.put(item.getSku(), item.getNum()));
        saleorderGoodsList.forEach(item -> saleorderGoods.put(item.getSku(), item.getNum()));

        for (Map.Entry<String, Integer> entry : buyorderGoods.entrySet()) {
            if (!saleorderGoods.get(entry.getKey()).equals(entry.getValue())) {
                throw new IllegalArgumentException("采购单和关联的销售单内sku数量不一致，不可修改发货方式");
            }
        }
    }

    @Override
    public Boolean checkSaleorderGoodsDeliveryStatus(List<BuyorderModifyApplyGoods> buyorderModifyApplyGoodsList) {
        for (BuyorderModifyApplyGoods buyorderModifyApplyGoods : buyorderModifyApplyGoodsList) {
            Integer deliveryNum = saleorderGoodsMapper.getSaleorderGoodsDeliveryNum(buyorderModifyApplyGoods.getBuyorderGoodsId());
            if (!Objects.isNull(deliveryNum) && deliveryNum > 0) {
                return false;
            }
        }
        return true;
    }

}
