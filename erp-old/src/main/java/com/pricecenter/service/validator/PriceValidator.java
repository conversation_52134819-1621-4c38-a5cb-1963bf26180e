package com.pricecenter.service.validator;

import com.pricecenter.dto.PriceInfoUploadValidatorDto;
import com.pricecenter.dto.ValidatorResult;
import com.pricecenter.service.Validator;
import com.vedeng.common.util.StringUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 价格相关校验器
 */
@Service
public class PriceValidator implements Validator<PriceInfoUploadValidatorDto> {

    public static BigDecimal Three_Bill = BigDecimal.valueOf(300000000);

    @Override
    public ValidatorResult validator(PriceInfoUploadValidatorDto priceInfoUploadValidatorDto) {

        ValidatorResult result = ValidatorResult.newBuild();

        //获取所有的行
        List<Row> rowList = priceInfoUploadValidatorDto.getRows();

        Row thisRow = null;
        int rowNum = 0;

        int purchaseCellNum = 2;
        int marketCellNum = 3;
        int terminalCellNum = 4;
        int distributionCellNum = 5;
        int groupCellNum = 6;
        int electronicCommerceCellNum = 7;
        int researchTerminalCellNum = 8;

        for(int i = 0;i < rowList.size();i++){

            rowNum = i;

            thisRow = rowList.get(i);

            Cell purchaseCell = thisRow.getCell(purchaseCellNum);
            Cell marketCell = thisRow.getCell(marketCellNum);
            Cell terminalCell = thisRow.getCell(terminalCellNum);
            Cell distributionCell = thisRow.getCell(distributionCellNum);
            Cell groupCell = thisRow.getCell(groupCellNum);
            Cell electronicCommerceCell = thisRow.getCell(electronicCommerceCellNum);
            Cell researchTerminalCell = thisRow.getCell(researchTerminalCellNum);

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”为空，提交失败
            if ((purchaseCell == null || purchaseCell.getCellType() ==  CellType.BLANK)
                    || (marketCell == null || marketCell.getCellType() ==  CellType.BLANK)
                    || (terminalCell == null || terminalCell.getCellType() ==  CellType.BLANK)
                    || (distributionCell == null || distributionCell.getCellType() ==  CellType.BLANK)) {
                return result.setMessage("行"+(rowNum + 1)+"中采购成本,市场价,终端价,经销价不能为空，提交失败");
            }

            purchaseCell.setCellType(CellType.STRING);
            marketCell.setCellType(CellType.STRING);
            terminalCell.setCellType(CellType.STRING);
            distributionCell.setCellType(CellType.STRING);

            String purchaseValue = purchaseCell.getStringCellValue();
            String marketValue = marketCell.getStringCellValue();
            String terminalValue = terminalCell.getStringCellValue();
            String distributionValue = distributionCell.getStringCellValue();
            String groupValue = null;
            String  electronicCommerceValue = null;
            String  researchTerminalValue = null;

            if (groupCell != null) {
                groupCell.setCellType(CellType.STRING);
                groupValue = groupCell.getStringCellValue();
            }

            if (electronicCommerceCell != null) {
                electronicCommerceCell.setCellType(CellType.STRING);
                electronicCommerceValue = electronicCommerceCell.getStringCellValue();
            }

            if (researchTerminalCell != null) {
                researchTerminalCell.setCellType(CellType.STRING);
                researchTerminalValue = researchTerminalCell.getStringCellValue();
            }

            //数字保留2位小数
            if(!priceVadator(purchaseValue) || !priceVadator(marketValue) || !priceVadator(terminalValue) || !priceVadator(distributionValue)){
                return result.setMessage("行"+(rowNum + 1)+"中采购成本,市场价,终端价,经销价只能是正数，且最多2位小数，提交失败");
            }

            if(StringUtil.isNotEmpty(groupValue) && !priceVadator(groupValue)){
                return result.setMessage("行"+(rowNum + 1)+"中集团价只能是正数，且最多2位小数，提交失败");
            }

            if(StringUtil.isNotEmpty(electronicCommerceValue) && !priceVadator(electronicCommerceValue)){
                return result.setMessage("行"+(rowNum + 1)+"中电商价只能是正数，且最多2位小数，提交失败");
            }

            if(StringUtil.isNotEmpty(researchTerminalValue) && !priceVadator(researchTerminalValue)){
                return result.setMessage("行"+(rowNum + 1)+"中科研终端价只能是正数，且最多2位小数，提交失败");
            }

            BigDecimal  purchasePrice = new BigDecimal(purchaseValue);
            BigDecimal  marketPrice = new BigDecimal(marketValue);
            BigDecimal  terminalPrice = new BigDecimal(terminalValue);
            BigDecimal  distributionPrice = new BigDecimal(distributionValue);

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”为0，提交失败
            if(purchasePrice.compareTo(BigDecimal.ZERO) == 0 ||
                    marketPrice.compareTo(BigDecimal.ZERO)  == 0 ||
                    terminalPrice.compareTo(BigDecimal.ZERO)  == 0 ||
                    distributionPrice.compareTo(BigDecimal.ZERO)  == 0){
                return result.setMessage("行"+(rowNum + 1)+"中采购成本,市场价,终端价,经销价为0，提交失败");
            }

            //行XX中“采购成本”/“市场价”/“终端价”/“经销价”超过3亿，提交失败
            if(purchasePrice.compareTo(Three_Bill) > 0 ||
                    marketPrice.compareTo(Three_Bill)  > 0 ||
                    terminalPrice.compareTo(Three_Bill)  > 0 ||
                    distributionPrice.compareTo(Three_Bill)  > 0){
                return result.setMessage("行"+(rowNum + 1)+"中采购成本,市场价,终端价,经销价超过3亿，提交失败");
            }

            //集团价有值 做判断
            if (StringUtil.isNotEmpty(groupValue)) {
                BigDecimal groupPrice = new BigDecimal(groupValue);
                //集团价为0，提交失败
                if (groupPrice.compareTo(BigDecimal.ZERO) == 0) {
                    return result.setMessage("行" + (rowNum + 1) + "中集团价为0，提交失败");
                }
                //集团价超过3亿，提交失败
                if (groupPrice.compareTo(Three_Bill) > 0) {
                    return result.setMessage("行" + (rowNum + 1) + "中集团价超过3亿，提交失败");
                }
            }

            if (StringUtil.isNotEmpty(electronicCommerceValue)) {
                BigDecimal electronicCommercePrice = new BigDecimal(electronicCommerceValue);
                //集团价为0，提交失败
                if (electronicCommercePrice.compareTo(BigDecimal.ZERO) == 0) {
                    return result.setMessage("行" + (rowNum + 1) + "中电商价为0，提交失败");
                }
                //集团价超过3亿，提交失败
                if (electronicCommercePrice.compareTo(Three_Bill) > 0) {
                    return result.setMessage("行" + (rowNum + 1) + "中电商价超过3亿，提交失败");
                }
            }

        }
        return result.setResult(true);
    }

    public boolean priceVadator(String priceStr) {
        // 判断小数点后2位的数字的正则表达式
        Pattern pattern = Pattern.compile("^(([1-9]{1}\\d*)|([0]{1}))(\\.(\\d){0,2})?$");
        Matcher match = pattern.matcher(priceStr);
        return match.matches();
    }

}
