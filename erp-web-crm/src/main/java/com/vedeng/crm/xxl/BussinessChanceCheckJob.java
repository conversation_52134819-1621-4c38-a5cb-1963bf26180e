package com.vedeng.crm.xxl;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.task.service.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/8/12
 */
@Component
@JobHandler(value="BussinessChanceCheckJob")
@Slf4j
public class BussinessChanceCheckJob   extends AbstractJobHandler {

    @Autowired
    private TaskService taskService;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================商机跟进 job开始====================");
        // 超时提醒
        taskService.alertBussinessChanceProcessTask(param);
        XxlJobLogger.log("==================商机跟进 job结束====================");
        return SUCCESS;
    }
}
