package com.vedeng.firstengage.model;

import java.util.List;

import com.vedeng.system.model.Attachment;

public class RegistrationNumber {

	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Integer registrationNumberId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String registrationNumber;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.MANAGE_CATEGORY_LEVEL
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Integer manageCategoryLevel;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_COMPANY_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Integer productCompanyId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCTION_ADDRESS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productionAddress;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_CHINESE_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productChineseName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_ENGLISH_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productEnglishName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Integer productCategoryId;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productCategoryName;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.MODEL
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String model;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.ISSUING_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Long issuingDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.EFFECTIVE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Long effectiveDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.APPROVAL_DEPARTMENT
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String approvalDepartment;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.TRADEMARK
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String trademark;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.ZIP_CODE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String zipCode;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.REGISTERED_AGENT
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String registeredAgent;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.REGISTERED_AGENT_ADDRESS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String registeredAgentAddress;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRO_PERF_STRU_AND_COMP
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String proPerfStruAndComp;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_USE_RANGE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productUseRange;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.OTHER_CONTENTS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String otherContents;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCT_STANDARDS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productStandards;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.TYPE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Boolean type;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.PRODUCTION_OR_COUNTRY
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String productionOrCountry;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.REMARKS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String remarks;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.AFTERSALE_SERVICE_ORG
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String aftersaleServiceOrg;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.CHANGE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Long changeDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.CHANGE_CONTENTS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String changeContents;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.IS_RELEASE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Boolean isRelease;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.EXPECTED_USAGE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String expectedUsage;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.STORAGE_COND_AND_EFFECTIVE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String storageCondAndEffectiveDate;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.MAIN_PRO_PERF_STRU_AND_COMP
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private String mainProPerfStruAndComp;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.ADD_TIME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Long addTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.CREATOR
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Integer creator;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.MOD_TIME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Long modTime;
	/**
	 * This field was generated by MyBatis Generator. This field corresponds to the database column T_REGISTRATION_NUMBER.UPDATER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	private Integer updater;
	
	/**
	 * 批准日期 字符串类型
	 */
	private String issuingDateStr;
	
	/**
	 * 有效期至
	 */
	private String effectiveDateStr;
	
	/**
	 * 变更日期
	 */
	private String changeDateStr;
	
	/**
	 * 生产企业信息
	 */
	private ProductCompany productCompany;
	
	/**
	 * 首营id
	 */
	private Integer firstEngageId;
	
	/**
	 * 附件
	 */
	private List<Attachment> attachments;
	
	/**
	 *  注册证附件/备案凭证附件
	 */
	private List<Attachment> zczAttachments;

	/**
	 *  注册证附件/备案凭证附件源文件
	 */
	private List<Attachment> zczyAttachments;

	/**
	 *  标签样稿附件
	 */
	private List<Attachment> labelAttachments;

	/**
	 *  标签样稿附件源文件
	 */
	private List<Attachment> labelSourceAttachments;

	/**
	 * 说明书
	 */
	private List<Attachment> smsAttachments;
	
	/**
	 * 生产企业卫生许可证
	 */
	private List<Attachment> wsAttachments;
	
	/**
	 * 生产企业生产许可证
	 */
	private List<Attachment> scAttachments;
	
	/**
	 * 商标注册证
	 */
	private List<Attachment> sbAttachments;
	
	/**
	 * 注册登记表附件
	 */
	private List<Attachment> djbAttachments;

	/**
	 * 产品图片（单包装/大包装）
	 */
	private List<Attachment> cpAttachments;

	/**
	 * 营业执照
	 */
	private List<Attachment> yzAttachments;

	/**
	 * 注册证附件/备案凭证附件（贝）
	 */
	private List<Attachment> zcZBAttachments;

	/**
	 * 营业执照（贝）
	 */
	private List<Attachment> yzBAttachments;

	/**
	 * 生产企业生产许可证或备案凭证（贝）
	 */
	private List<Attachment> scBAttachments;

	/**
	 * 生产企业生产产品登记表(即登记表附近)（贝）(即注册登记表附件) 登记表（贝）
	 */
	private List<Attachment> djbBAttachments;
	
	/**
	 * 过期处理状态
	 */
	private Integer dealStatus;

	/**
	 * 附件
	 */
	private String attachment;
    /**
     *注册证类型，0国产注册证，1国产备案，2进口注册证，3进口备案
     */
	private Integer category;
	/**
	 *存储及有效期
	 */
	private String storageAndExpiryDate;
    /**
     * 是否委托生产
     */
	private Integer isSubcontractProduction;

	/**
	 * 生产企业名称
	 */
	private String productionEnterpriseName;

	/**
	 * 营业执照发证日期
	 */
	private String bcIssueDateStr;
	/**
	 *营业执照发证日期
	 */
	private Long bcIssueDate;
	/**
	 * 营业执照有效期开始时间
	 */
	private String yzStartTimeStr;

	/**
	 * 营业执照有效期结束时间
	 */
	private String yzEndTimeStr;

	/**
	 * 生产许可证有效期开始时间
	 */
	private String scStartTimeStr;

	/**
	 * 生产许可证有效期结束时间
	 */
	private String scEndTimeStr;

	/**
	 * 登记表开始时间
	 */
	private String djbStartTimeStr;

	/**
	 * 登记表结束时间
	 */
	private String djbEndTimeStr;

    /**
     * 生产企业许可证号或备案凭证编号
     */

    private String productCompanyLicence;

    //生产厂商外键id
	private Integer manufacturerId;

	private String manufacturerName;

	private List<RegistrationNumber> manufacturer;

	public List<Attachment> getLabelAttachments() {
		return labelAttachments;
	}

	public void setLabelAttachments(List<Attachment> labelAttachments) {
		this.labelAttachments = labelAttachments;
	}

	public List<Attachment> getLabelSourceAttachments() {
		return labelSourceAttachments;
	}
	public void setLabelSourceAttachments(List<Attachment> labelSourceAttachments) {
		this.labelSourceAttachments = labelSourceAttachments;
	}


	public String getProductCompanyLicence() {
        return productCompanyLicence;
    }

    public void setProductCompanyLicence(String productCompanyLicence) {
        this.productCompanyLicence = productCompanyLicence;
    }

	public String getBcIssueDateStr() {
		return bcIssueDateStr;
	}

	public void setBcIssueDateStr(String bcIssueDateStr) {
		this.bcIssueDateStr = bcIssueDateStr;
	}

	public Long getBcIssueDate() {
		return bcIssueDate;
	}

	public void setBcIssueDate(Long bcIssueDate) {
		this.bcIssueDate = bcIssueDate;
	}

	public String getAttachment() {
		return attachment;
	}

	public void setAttachment(String attachment) {
		this.attachment = attachment;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}

	public String getStorageAndExpiryDate() {
		return storageAndExpiryDate;
	}

	public void setStorageAndExpiryDate(String storageAndExpiryDate) {
		this.storageAndExpiryDate = storageAndExpiryDate;
	}

	public Integer getIsSubcontractProduction() {
		return isSubcontractProduction;
	}

	public void setIsSubcontractProduction(Integer isSubcontractProduction) {
		this.isSubcontractProduction = isSubcontractProduction;
	}

	public String getProductionEnterpriseName() {
		return productionEnterpriseName;
	}

	public void setProductionEnterpriseName(String productionEnterpriseName) {
		this.productionEnterpriseName = productionEnterpriseName;
	}

	public String getYzStartTimeStr() {
		return yzStartTimeStr;
	}

	public void setYzStartTimeStr(String yzStartTimeStr) {
		this.yzStartTimeStr = yzStartTimeStr;
	}

	public String getYzEndTimeStr() {
		return yzEndTimeStr;
	}

	public void setYzEndTimeStr(String yzEndTimeStr) {
		this.yzEndTimeStr = yzEndTimeStr;
	}

	public String getScStartTimeStr() {
		return scStartTimeStr;
	}

	public void setScStartTimeStr(String scStartTimeStr) {
		this.scStartTimeStr = scStartTimeStr;
	}

	public String getScEndTimeStr() {
		return scEndTimeStr;
	}

	public void setScEndTimeStr(String scEndTimeStr) {
		this.scEndTimeStr = scEndTimeStr;
	}

	public String getDjbStartTimeStr() {
		return djbStartTimeStr;
	}

	public void setDjbStartTimeStr(String djbStartTimeStr) {
		this.djbStartTimeStr = djbStartTimeStr;
	}

	public String getDjbEndTimeStr() {
		return djbEndTimeStr;
	}

	public void setDjbEndTimeStr(String djbEndTimeStr) {
		this.djbEndTimeStr = djbEndTimeStr;
	}

	public List<Attachment> getYzAttachments() {
		return yzAttachments;
	}

	public void setYzAttachments(List<Attachment> yzAttachments) {
		this.yzAttachments = yzAttachments;
	}

	public Integer getDealStatus() {
		return dealStatus;
	}

	public void setDealStatus(Integer dealStatus) {
		this.dealStatus = dealStatus;
	}

	public List<Attachment> getZczAttachments() {
		return zczAttachments;
	}

	public void setZczAttachments(List<Attachment> zczAttachments) {
		this.zczAttachments = zczAttachments;
	}

	public List<Attachment> getSmsAttachments() {
		return smsAttachments;
	}

	public void setSmsAttachments(List<Attachment> smsAttachments) {
		this.smsAttachments = smsAttachments;
	}

	public List<Attachment> getWsAttachments() {
		return wsAttachments;
	}

	public void setWsAttachments(List<Attachment> wsAttachments) {
		this.wsAttachments = wsAttachments;
	}

	public List<Attachment> getScAttachments() {
		return scAttachments;
	}

	public void setScAttachments(List<Attachment> scAttachments) {
		this.scAttachments = scAttachments;
	}

	public List<Attachment> getSbAttachments() {
		return sbAttachments;
	}

	public void setSbAttachments(List<Attachment> sbAttachments) {
		this.sbAttachments = sbAttachments;
	}

	public List<Attachment> getDjbAttachments() {
		return djbAttachments;
	}

	public void setDjbAttachments(List<Attachment> djbAttachments) {
		this.djbAttachments = djbAttachments;
	}

	public List<Attachment> getCpAttachments() {
		return cpAttachments;
	}

	public void setCpAttachments(List<Attachment> cpAttachments) {
		this.cpAttachments = cpAttachments;
	}

	public List<Attachment> getZcZBAttachments() {
		return zcZBAttachments;
	}

	public void setZcZBAttachments(List<Attachment> zcZBAttachments) {
		this.zcZBAttachments = zcZBAttachments;
	}

	public List<Attachment> getYzBAttachments() {
		return yzBAttachments;
	}

	public void setYzBAttachments(List<Attachment> yzBAttachments) {
		this.yzBAttachments = yzBAttachments;
	}



	public Integer getFirstEngageId() {
		return firstEngageId;
	}

	public void setFirstEngageId(Integer firstEngageId) {
		this.firstEngageId = firstEngageId;
	}

	public List<Attachment> getAttachments() {
		return attachments;
	}

	public void setAttachments(List<Attachment> attachments) {
		this.attachments = attachments;
	}
	public String getChangeDateStr() {
		return changeDateStr;
	}

	public void setChangeDateStr(String changeDateStr) {
		this.changeDateStr = changeDateStr;
	}

	public String getEffectiveDateStr() {
		return effectiveDateStr;
	}

	public void setEffectiveDateStr(String effectiveDateStr) {
		this.effectiveDateStr = effectiveDateStr;
	}

	public String getIssuingDateStr() {
		return issuingDateStr;
	}

	public void setIssuingDateStr(String issuingDateStr) {
		this.issuingDateStr = issuingDateStr;
	}

	public ProductCompany getProductCompany() {
		return productCompany;
	}

	public void setProductCompany(ProductCompany productCompany) {
		this.productCompany = productCompany;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
	 * @return  the value of T_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Integer getRegistrationNumberId() {
		return registrationNumberId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
	 * @param registrationNumberId  the value for T_REGISTRATION_NUMBER.REGISTRATION_NUMBER_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setRegistrationNumberId(Integer registrationNumberId) {
		this.registrationNumberId = registrationNumberId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.REGISTRATION_NUMBER
	 * @return  the value of T_REGISTRATION_NUMBER.REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getRegistrationNumber() {
		return registrationNumber;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.REGISTRATION_NUMBER
	 * @param registrationNumber  the value for T_REGISTRATION_NUMBER.REGISTRATION_NUMBER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setRegistrationNumber(String registrationNumber) {
		this.registrationNumber = registrationNumber == null ? null : registrationNumber.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.MANAGE_CATEGORY_LEVEL
	 * @return  the value of T_REGISTRATION_NUMBER.MANAGE_CATEGORY_LEVEL
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Integer getManageCategoryLevel() {
		return manageCategoryLevel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.MANAGE_CATEGORY_LEVEL
	 * @param manageCategoryLevel  the value for T_REGISTRATION_NUMBER.MANAGE_CATEGORY_LEVEL
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setManageCategoryLevel(Integer manageCategoryLevel) {
		this.manageCategoryLevel = manageCategoryLevel;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_COMPANY_ID
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_COMPANY_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Integer getProductCompanyId() {
		return productCompanyId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_COMPANY_ID
	 * @param productCompanyId  the value for T_REGISTRATION_NUMBER.PRODUCT_COMPANY_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductCompanyId(Integer productCompanyId) {
		this.productCompanyId = productCompanyId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCTION_ADDRESS
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCTION_ADDRESS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductionAddress() {
		return productionAddress;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCTION_ADDRESS
	 * @param productionAddress  the value for T_REGISTRATION_NUMBER.PRODUCTION_ADDRESS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductionAddress(String productionAddress) {
		this.productionAddress = productionAddress == null ? null : productionAddress.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_CHINESE_NAME
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_CHINESE_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductChineseName() {
		return productChineseName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_CHINESE_NAME
	 * @param productChineseName  the value for T_REGISTRATION_NUMBER.PRODUCT_CHINESE_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductChineseName(String productChineseName) {
		this.productChineseName = productChineseName == null ? null : productChineseName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_ENGLISH_NAME
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_ENGLISH_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductEnglishName() {
		return productEnglishName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_ENGLISH_NAME
	 * @param productEnglishName  the value for T_REGISTRATION_NUMBER.PRODUCT_ENGLISH_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductEnglishName(String productEnglishName) {
		this.productEnglishName = productEnglishName == null ? null : productEnglishName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_ID
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Integer getProductCategoryId() {
		return productCategoryId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_ID
	 * @param productCategoryId  the value for T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_ID
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductCategoryId(Integer productCategoryId) {
		this.productCategoryId = productCategoryId;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_NAME
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductCategoryName() {
		return productCategoryName;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_NAME
	 * @param productCategoryName  the value for T_REGISTRATION_NUMBER.PRODUCT_CATEGORY_NAME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductCategoryName(String productCategoryName) {
		this.productCategoryName = productCategoryName == null ? null : productCategoryName.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.MODEL
	 * @return  the value of T_REGISTRATION_NUMBER.MODEL
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getModel() {
		return model;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.MODEL
	 * @param model  the value for T_REGISTRATION_NUMBER.MODEL
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setModel(String model) {
		this.model = model == null ? null : model.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.ISSUING_DATE
	 * @return  the value of T_REGISTRATION_NUMBER.ISSUING_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Long getIssuingDate() {
		return issuingDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.ISSUING_DATE
	 * @param issuingDate  the value for T_REGISTRATION_NUMBER.ISSUING_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setIssuingDate(Long issuingDate) {
		this.issuingDate = issuingDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.EFFECTIVE_DATE
	 * @return  the value of T_REGISTRATION_NUMBER.EFFECTIVE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Long getEffectiveDate() {
		return effectiveDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.EFFECTIVE_DATE
	 * @param effectiveDate  the value for T_REGISTRATION_NUMBER.EFFECTIVE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setEffectiveDate(Long effectiveDate) {
		this.effectiveDate = effectiveDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.APPROVAL_DEPARTMENT
	 * @return  the value of T_REGISTRATION_NUMBER.APPROVAL_DEPARTMENT
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getApprovalDepartment() {
		return approvalDepartment;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.APPROVAL_DEPARTMENT
	 * @param approvalDepartment  the value for T_REGISTRATION_NUMBER.APPROVAL_DEPARTMENT
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setApprovalDepartment(String approvalDepartment) {
		this.approvalDepartment = approvalDepartment == null ? null : approvalDepartment.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.TRADEMARK
	 * @return  the value of T_REGISTRATION_NUMBER.TRADEMARK
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getTrademark() {
		return trademark;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.TRADEMARK
	 * @param trademark  the value for T_REGISTRATION_NUMBER.TRADEMARK
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setTrademark(String trademark) {
		this.trademark = trademark == null ? null : trademark.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.ZIP_CODE
	 * @return  the value of T_REGISTRATION_NUMBER.ZIP_CODE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getZipCode() {
		return zipCode;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.ZIP_CODE
	 * @param zipCode  the value for T_REGISTRATION_NUMBER.ZIP_CODE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setZipCode(String zipCode) {
		this.zipCode = zipCode == null ? null : zipCode.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.REGISTERED_AGENT
	 * @return  the value of T_REGISTRATION_NUMBER.REGISTERED_AGENT
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getRegisteredAgent() {
		return registeredAgent;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.REGISTERED_AGENT
	 * @param registeredAgent  the value for T_REGISTRATION_NUMBER.REGISTERED_AGENT
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setRegisteredAgent(String registeredAgent) {
		this.registeredAgent = registeredAgent == null ? null : registeredAgent.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.REGISTERED_AGENT_ADDRESS
	 * @return  the value of T_REGISTRATION_NUMBER.REGISTERED_AGENT_ADDRESS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getRegisteredAgentAddress() {
		return registeredAgentAddress;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.REGISTERED_AGENT_ADDRESS
	 * @param registeredAgentAddress  the value for T_REGISTRATION_NUMBER.REGISTERED_AGENT_ADDRESS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setRegisteredAgentAddress(String registeredAgentAddress) {
		this.registeredAgentAddress = registeredAgentAddress == null ? null : registeredAgentAddress.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRO_PERF_STRU_AND_COMP
	 * @return  the value of T_REGISTRATION_NUMBER.PRO_PERF_STRU_AND_COMP
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProPerfStruAndComp() {
		return proPerfStruAndComp;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRO_PERF_STRU_AND_COMP
	 * @param proPerfStruAndComp  the value for T_REGISTRATION_NUMBER.PRO_PERF_STRU_AND_COMP
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProPerfStruAndComp(String proPerfStruAndComp) {
		this.proPerfStruAndComp = proPerfStruAndComp == null ? null : proPerfStruAndComp.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_USE_RANGE
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_USE_RANGE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductUseRange() {
		return productUseRange;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_USE_RANGE
	 * @param productUseRange  the value for T_REGISTRATION_NUMBER.PRODUCT_USE_RANGE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductUseRange(String productUseRange) {
		this.productUseRange = productUseRange == null ? null : productUseRange.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.OTHER_CONTENTS
	 * @return  the value of T_REGISTRATION_NUMBER.OTHER_CONTENTS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getOtherContents() {
		return otherContents;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.OTHER_CONTENTS
	 * @param otherContents  the value for T_REGISTRATION_NUMBER.OTHER_CONTENTS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setOtherContents(String otherContents) {
		this.otherContents = otherContents == null ? null : otherContents.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCT_STANDARDS
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCT_STANDARDS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductStandards() {
		return productStandards;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCT_STANDARDS
	 * @param productStandards  the value for T_REGISTRATION_NUMBER.PRODUCT_STANDARDS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductStandards(String productStandards) {
		this.productStandards = productStandards == null ? null : productStandards.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.TYPE
	 * @return  the value of T_REGISTRATION_NUMBER.TYPE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Boolean getType() {
		return type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.TYPE
	 * @param type  the value for T_REGISTRATION_NUMBER.TYPE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setType(Boolean type) {
		this.type = type;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.PRODUCTION_OR_COUNTRY
	 * @return  the value of T_REGISTRATION_NUMBER.PRODUCTION_OR_COUNTRY
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getProductionOrCountry() {
		return productionOrCountry;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.PRODUCTION_OR_COUNTRY
	 * @param productionOrCountry  the value for T_REGISTRATION_NUMBER.PRODUCTION_OR_COUNTRY
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setProductionOrCountry(String productionOrCountry) {
		this.productionOrCountry = productionOrCountry == null ? null : productionOrCountry.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.REMARKS
	 * @return  the value of T_REGISTRATION_NUMBER.REMARKS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getRemarks() {
		return remarks;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.REMARKS
	 * @param remarks  the value for T_REGISTRATION_NUMBER.REMARKS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setRemarks(String remarks) {
		this.remarks = remarks == null ? null : remarks.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.AFTERSALE_SERVICE_ORG
	 * @return  the value of T_REGISTRATION_NUMBER.AFTERSALE_SERVICE_ORG
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getAftersaleServiceOrg() {
		return aftersaleServiceOrg;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.AFTERSALE_SERVICE_ORG
	 * @param aftersaleServiceOrg  the value for T_REGISTRATION_NUMBER.AFTERSALE_SERVICE_ORG
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setAftersaleServiceOrg(String aftersaleServiceOrg) {
		this.aftersaleServiceOrg = aftersaleServiceOrg == null ? null : aftersaleServiceOrg.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.CHANGE_DATE
	 * @return  the value of T_REGISTRATION_NUMBER.CHANGE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Long getChangeDate() {
		return changeDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.CHANGE_DATE
	 * @param changeDate  the value for T_REGISTRATION_NUMBER.CHANGE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setChangeDate(Long changeDate) {
		this.changeDate = changeDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.CHANGE_CONTENTS
	 * @return  the value of T_REGISTRATION_NUMBER.CHANGE_CONTENTS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getChangeContents() {
		return changeContents;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.CHANGE_CONTENTS
	 * @param changeContents  the value for T_REGISTRATION_NUMBER.CHANGE_CONTENTS
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setChangeContents(String changeContents) {
		this.changeContents = changeContents == null ? null : changeContents.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.IS_RELEASE
	 * @return  the value of T_REGISTRATION_NUMBER.IS_RELEASE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Boolean getIsRelease() {
		return isRelease;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.IS_RELEASE
	 * @param isRelease  the value for T_REGISTRATION_NUMBER.IS_RELEASE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setIsRelease(Boolean isRelease) {
		this.isRelease = isRelease;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.EXPECTED_USAGE
	 * @return  the value of T_REGISTRATION_NUMBER.EXPECTED_USAGE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getExpectedUsage() {
		return expectedUsage;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.EXPECTED_USAGE
	 * @param expectedUsage  the value for T_REGISTRATION_NUMBER.EXPECTED_USAGE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setExpectedUsage(String expectedUsage) {
		this.expectedUsage = expectedUsage == null ? null : expectedUsage.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.STORAGE_COND_AND_EFFECTIVE_DATE
	 * @return  the value of T_REGISTRATION_NUMBER.STORAGE_COND_AND_EFFECTIVE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getStorageCondAndEffectiveDate() {
		return storageCondAndEffectiveDate;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.STORAGE_COND_AND_EFFECTIVE_DATE
	 * @param storageCondAndEffectiveDate  the value for T_REGISTRATION_NUMBER.STORAGE_COND_AND_EFFECTIVE_DATE
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setStorageCondAndEffectiveDate(String storageCondAndEffectiveDate) {
		this.storageCondAndEffectiveDate = storageCondAndEffectiveDate == null ? null
				: storageCondAndEffectiveDate.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.MAIN_PRO_PERF_STRU_AND_COMP
	 * @return  the value of T_REGISTRATION_NUMBER.MAIN_PRO_PERF_STRU_AND_COMP
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public String getMainProPerfStruAndComp() {
		return mainProPerfStruAndComp;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.MAIN_PRO_PERF_STRU_AND_COMP
	 * @param mainProPerfStruAndComp  the value for T_REGISTRATION_NUMBER.MAIN_PRO_PERF_STRU_AND_COMP
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setMainProPerfStruAndComp(String mainProPerfStruAndComp) {
		this.mainProPerfStruAndComp = mainProPerfStruAndComp == null ? null : mainProPerfStruAndComp.trim();
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.ADD_TIME
	 * @return  the value of T_REGISTRATION_NUMBER.ADD_TIME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Long getAddTime() {
		return addTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.ADD_TIME
	 * @param addTime  the value for T_REGISTRATION_NUMBER.ADD_TIME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setAddTime(Long addTime) {
		this.addTime = addTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.CREATOR
	 * @return  the value of T_REGISTRATION_NUMBER.CREATOR
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Integer getCreator() {
		return creator;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.CREATOR
	 * @param creator  the value for T_REGISTRATION_NUMBER.CREATOR
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setCreator(Integer creator) {
		this.creator = creator;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.MOD_TIME
	 * @return  the value of T_REGISTRATION_NUMBER.MOD_TIME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Long getModTime() {
		return modTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.MOD_TIME
	 * @param modTime  the value for T_REGISTRATION_NUMBER.MOD_TIME
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setModTime(Long modTime) {
		this.modTime = modTime;
	}

	/**
	 * This method was generated by MyBatis Generator. This method returns the value of the database column T_REGISTRATION_NUMBER.UPDATER
	 * @return  the value of T_REGISTRATION_NUMBER.UPDATER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public Integer getUpdater() {
		return updater;
	}

	/**
	 * This method was generated by MyBatis Generator. This method sets the value of the database column T_REGISTRATION_NUMBER.UPDATER
	 * @param updater  the value for T_REGISTRATION_NUMBER.UPDATER
	 * @mbg.generated  Wed Mar 20 18:32:35 CST 2019
	 */
	public void setUpdater(Integer updater) {
		this.updater = updater;
	}

	public Integer getManufacturerId() {
		return manufacturerId;
	}

	public void setManufacturerId(Integer manufacturerId) {
		this.manufacturerId = manufacturerId;
	}

	public String getManufacturerName() {
		return manufacturerName;
	}

	public void setManufacturerName(String manufacturerName) {
		this.manufacturerName = manufacturerName;
	}

	public List<RegistrationNumber> getManufacturer() {
		return manufacturer;
	}

	public void setManufacturer(List<RegistrationNumber> manufacturer) {
		this.manufacturer = manufacturer;
	}

	public List<Attachment> getScBAttachments() {
		return scBAttachments;
	}

	public void setScBAttachments(List<Attachment> scBAttachments) {
		this.scBAttachments = scBAttachments;
	}

	public List<Attachment> getDjbBAttachments() {
		return djbBAttachments;
	}

	public void setDjbBAttachments(List<Attachment> djbBAttachments) {
		this.djbBAttachments = djbBAttachments;
	}

	public List<Attachment> getZczyAttachments() {
		return zczyAttachments;
	}

	public void setZczyAttachments(List<Attachment> zczyAttachments) {
		this.zczyAttachments = zczyAttachments;
	}

}