package com.wms.inventorytransfer.model.po;

import java.io.Serializable;

/**
 * @describe 库存转移单持久化对象
 * <AUTHOR>
 * @date 2020/7/10 13:23:24
 */
public class InventoryTransferPO implements Serializable {
    /**
     * 库存转移单ID
     */
    private Integer inventoryTransferId;

    /**
     * 库存转移单单号
     */
    private String inventoryTransferNo;

    /**
     * 具体活动ID
     */
    private Integer actionId;

    /**
     * 库存转移单类型
     */
    private Integer type;

    /**
     * '转移单状态  （1进行中  2已完结）',
     */
    private Integer status;

    /**
     * 目标逻辑仓
     */
    private Integer toWarehouseId;

    /**
     * 转移原因
     */
    private String reasons;

    /**
     * 备注
     */
    private String comments;

    /**
     * 关联订单ID
     */
    private Integer orderId;

    /**
     * 关联订单号
     */
    private String orderNo;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 更新时间
     */
    private Long updateTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 最后一次编辑人
     */
    private Integer updater;

    public Integer getInventoryTransferId() {
        return inventoryTransferId;
    }

    public void setInventoryTransferId(Integer inventoryTransferId) {
        this.inventoryTransferId = inventoryTransferId;
    }

    public String getInventoryTransferNo() {
        return inventoryTransferNo;
    }

    public void setInventoryTransferNo(String inventoryTransferNo) {
        this.inventoryTransferNo = inventoryTransferNo;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getToWarehouseId() {
        return toWarehouseId;
    }

    public void setToWarehouseId(Integer toWarehouseId) {
        this.toWarehouseId = toWarehouseId;
    }

    public String getReasons() {
        return reasons;
    }

    public void setReasons(String reasons) {
        this.reasons = reasons;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public Integer getActionId() {
        return actionId;
    }

    public void setActionId(Integer actionId) {
        this.actionId = actionId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
