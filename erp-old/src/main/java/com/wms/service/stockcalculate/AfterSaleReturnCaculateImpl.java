package com.wms.service.stockcalculate;

import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.dto.StockCalculateDto;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @ClassName AfterSaleReturnCaculateImpl.java
 * @Description TODO 销售退货库存服务计算  销售退货单审核通过
 * @createTime 2020年08月12日 16:03:00
 */
@Service("afterSaleReturnCaculateImpl")
public class AfterSaleReturnCaculateImpl extends AbstractStockinfoCaculateInterace{
    @Override
    protected void calculateStockNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {

    }

    @Override
    protected void calculateOcupyNum(List<StockCalculateDto> details, List<WarehouseDto> warehouseStockList) {
        /*details.stream().forEach(stockCalculateDto->{

            WarehouseDto warehouseStock = super.getWarehouseStockBySkuAndLogcic(warehouseStockList,
                    stockCalculateDto.getSku(),
                    stockCalculateDto.getLogicalWarehouseId());

            //减少占用库存数量
            warehouseStock.setOccupyNum(-Math.abs(stockCalculateDto.getOccupyNum()));
        });*/
        super.calculateStockOrOccupyNumBase(details,warehouseStockList,true,false);
    }
}
