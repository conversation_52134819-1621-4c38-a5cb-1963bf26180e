<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="临时账期申请" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/libs/jquery/plugins/DatePicker/WdatePicker.js"></script>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/steps/steps.min.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_edit.css?rnd=${resourceVersionKey}">
<div class="form-list  form-tips8 trader-customer-accountperiodapply">
    <div class="detail-block-tip vd-tip tip-orange" style="display:inline-block;width: 85%;">
        <i class="vd-tip-icon vd-icon icon-info2"></i>
        <div class="vd-tip-cnt">新增账期时,有效期不得与现有账期的效期重叠<br>
            调整现有账期,不可调整账期有效期,如有需要请新增</div>
    </div>
    <div class="normal-list-page">
        <table class="table table-bordered table-striped table-condensed table-centered" id="accountPeriodTable">
            <thead>
            <tr>
                <th class="wid1">选择</th>
                <th class="wid2">总额度</th>
                <th class="wid4">有效期</th>
                <th class="wid2">可用额度</th>
                <th class="wid2">未归还金额</th>
                <th class="wid2">逾期未还金额</th>
                <th class="wid2">使用次数</th>
                <th class="wid2">逾期次数</th>
            </tr>
            </thead>
            <tbody>
            <tr>
                <c:choose>
                    <c:when test="${customerBillPeriods == null || fn:length(customerBillPeriods) == 0}">
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td id="usefulAmount">-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                        <td>-</td>
                    </c:when>
                    <c:otherwise>
                        <c:forEach items="${customerBillPeriods}" var="customerBillPeriodSummaryInfoDto" varStatus="num">
                            <tr>
                                <td>
                                    <input type="radio" class="J-prod-type" name="chooseExist" value="${num.count}" onclick="chooseAccount(${num.count});">
                                </td>
                                <td>${customerBillPeriodSummaryInfoDto.applyAmount}</td>
                                <td><date:date value="${customerBillPeriodSummaryInfoDto.billPeriodStart}" format="yyyy/MM/dd"/>
                                    ~<date:date value="${customerBillPeriodSummaryInfoDto.billPeriodEnd}" format="yyyy/MM/dd"/></td>
                                <td>${customerBillPeriodSummaryInfoDto.availableAmount}</td>
                                <td>${customerBillPeriodSummaryInfoDto.unreturnedAmount}</td>
                                <td>${customerBillPeriodSummaryInfoDto.overDueAmount}</td>
                                <td>${customerBillPeriodSummaryInfoDto.countOfUsed}</td>
                                <td>${customerBillPeriodSummaryInfoDto.countOfOverDue}</td>
                            </tr>
                        </c:forEach>

                    </c:otherwise>
                </c:choose>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="form-block form-info" style="text-align: center">
        <span class="bt-small bg-light-blue bt-bg-style" onclick="resetEmpty();" id="addBtn" style="width: 120px">新增账期</span>
        <span class="bt-small bg-light-blue bt-bg-style" onclick="selectAccountPeriod();" id="uptBtn" style="width: 120px">已有账期调整</span>
    </div>
    <div class="form-block form-info" style="margin-top: 10px">
        <form method="post" action="" id="myform">
            <ul>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>有效期：</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input class="Wdate f_left input-smaller96 mr5" type="text" autocomplete="off" placeholder="请设置效期" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'billPeriodEnd\')}'})"
                                   name="startTime" id="billPeriodStart" value=""/>
                            <div class="gang">至</div>
                            <input class="Wdate f_left input-smaller96" type="text" autocomplete="off" placeholder="请设置效期" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'billPeriodStart\')}'})"
                                   name="endTime" id="billPeriodEnd" value=""/>
                        </div>
                    </div>

                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="timeTips">
                        --设置开始及结束时间，有效期原则上不可超过60天,开始日期必须在现有账期的截止日期之后
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable id="amountLabel">申请额度：</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks" id="amountChange">
                            <select id="changeType" >
                                <option value="上调">上调</option>
                                <option value="下调">下调</option>
                            </select>
                            <input type="text" id="changeMount" value="0.00" placeholder="请输入需调整的额度"> <span>元&nbsp;&nbsp;&nbsp;&nbsp;调后额度：</span>
                            <span id="applyAmount" >
                                0
                            </span>
                        </div>
                        <div class="form-blanks" id="amountAdd">
                            <input type="text" id="insertApplyAmount" value="0.00" placeholder="请输入需申请的额度">元
                        </div>
                        <input type="hidden" name="applyAmount" value="" id="finalApplyAmount">
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="amountTips">
                            --仅可输入正数。支持2位小数
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>结算周期:</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks" >
                            <input type="text" name="settlementPeriod" id="settlementPeriod" placeholder="请设置结算周期" value=""> 天
                        </div>
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="settleTips">
                            --基于结算标准+结算周期，确定所使用的账期理应归还的截止时间
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <lable>预期毛利率：</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                            <input type="text" name="expectedMargin" id="expectedMargin" value=""> %
                        </div>
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        <span id="expectTips">
                            --请如实填写数字,作为审批人判断依据
                        </span>
                    </div>
                </li>
                <li>
                    <div class="form-tips">
                        <span>*</span>
                        <lable>申请原因</lable>
                    </div>
                    <div class="f_left ">
                        <div class="form-blanks">
                        <textarea name="applyReason" placeholder="请填写申请原因"  style="width: 500px;height: 50px" id="applyReason"></textarea>
                        </div>
                    </div>
                </li>
                <li>
                    <div class="pop-friend-tips">
                        --请完整填写申请原因,便于上级审核,提高通过率
                    </div>
                </li>
            </ul>
                <div class="pop-friend-tips">
                    友情提醒<br/> 1、已归还的帐期可以重新使用，如果不需要修改帐期额度和时间，您不用重复申请;<br/>
                    2、逾期不还,将对客户信用造成影响;
                </div>
                <div class="add-tijiao tcenter">
                    <input type="hidden" name="customerId" value="${traderCustomer.traderCustomerId}" />
                    <input type="hidden" name="traderId" value="${traderCustomer.traderId}" />
                    <input type="hidden" name="companyId" value="1" />
                    <input type="hidden" name="billPeriodType" value="2" />
                    <input type="hidden" name="billPeriodId" value="" id="billPeriodId"/>
                    <input type="hidden" name="operateType" value="1" id="operateType"/>
                    <input type="hidden" id="formerAmount">
                    <button type="submit" id="submit">提交</button>
                    <button class="dele" id="close-layer" type="button">取消</button>
                </div>
        </form>
    </div>
</div>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/js/customer/apply_temp_accountperiod.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript">
    //修改临时账期
    function selectAccountPeriod() {
        //显示临时账期选择按钮
        if($("#accountPeriodTable tr").find('td:eq(0)').eq(0).text() == "-"){
            layer.alert("当前无可用账期，如有需要请新增！");
            $("#accountPeriodTable").find('th:eq(0)').hide();
            $("#accountPeriodTable tr").find('td:eq(0)').hide();
            $("#amountChange").hide();
        }else{
            addToUpt();
            if($("#accountPeriodTable tr").find('td:eq(0)').length > 1) {
                //存在多个临时账期
                clearInfo();
            }else {
                //只存在一个有效的临时账期
                $("#billPeriodStart").val('<date:date value ='${customerBillPeriods.get(0).billPeriodStart == null ? "" : customerBillPeriods.get(0).billPeriodStart}' format="yyyy-MM-dd"/>');
                $("#billPeriodEnd").val('<date:date value ='${customerBillPeriods.get(0).billPeriodEnd == null ? "" : customerBillPeriods.get(0).billPeriodEnd}' format="yyyy-MM-dd"/>');
                $("#applyAmount").text('${customerBillPeriods.get(0).applyAmount}');
                $("#settlementPeriod").val('${customerBillPeriods.get(0).settlementPeriod}');
                <%--$("#expectedMargin").val('${customerBillPeriods.get(0).expectedMargin}');--%>
                <%--$("#applyReason").val('${customerBillPeriods.get(0).applyReason}');--%>
                $("#finalApplyAmount").val('${customerBillPeriods.get(0).unreturnedAmount}');
                $("#formerAmount").val('${customerBillPeriods.get(0).applyAmount}');
                $("#billPeriodId").val('${customerBillPeriods.get(0).billPeriodId}');
                $("#operateType").val(2);
            }
        }
    }
    //选择临时账期
    function chooseAccount(sort) {
        <c:forEach items="${customerBillPeriods}" var="customerBillPeriodSummaryInfoDto" varStatus="num">
            var now = ${num.count};
            if(sort == now){
                $("#billPeriodStart").val('<date:date value ='${customerBillPeriodSummaryInfoDto.billPeriodStart == null ? "" : customerBillPeriodSummaryInfoDto.billPeriodStart}' format="yyyy-MM-dd"/>');
                $("#billPeriodEnd").val('<date:date value ='${customerBillPeriodSummaryInfoDto.billPeriodEnd == null ? "" : customerBillPeriodSummaryInfoDto.billPeriodEnd}' format="yyyy-MM-dd"/>');
                $("#applyAmount").text('${customerBillPeriodSummaryInfoDto.applyAmount}');
                $("#settlementPeriod").val('${customerBillPeriodSummaryInfoDto.settlementPeriod}');
                <%--$("#expectedMargin").val('${customerBillPeriodSummaryInfoDto.expectedMargin}');--%>
                <%--$("#applyReason").val('${customerBillPeriodSummaryInfoDto.applyReason}');--%>
                $("#finalApplyAmount").val('${customerBillPeriodSummaryInfoDto.unreturnedAmount}');
                $("#formerAmount").val('${customerBillPeriodSummaryInfoDto.applyAmount}');
                $("#billPeriodId").val('${customerBillPeriodSummaryInfoDto.billPeriodId}');
                $("#operateType").val(2);
            }
        </c:forEach>

    }
</script>
<%@ include file="../../common/footer.jsp"%>