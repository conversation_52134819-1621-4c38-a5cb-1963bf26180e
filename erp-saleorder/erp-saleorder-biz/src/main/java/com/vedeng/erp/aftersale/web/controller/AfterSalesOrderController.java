package com.vedeng.erp.aftersale.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 销售售后信息
 */
@RequestMapping("/afterSalesOrder")
@Controller
@Slf4j
public class AfterSalesOrderController extends BaseController {


    /**
     * 编辑售后退款信息
     */
    @RequestMapping(value = "/refundInfo/edit")
    @NoNeedAccessAuthorization
    public ModelAndView edit(Integer afterSalesId, Integer type, Integer refund, BigDecimal finalRefundableAmount) {
        ModelAndView mv = new ModelAndView("vue/view/afterSalesOrder/refund_info");
        mv.addObject("afterSalesId", afterSalesId);
        mv.addObject("type", type);
        mv.addObject("refund", refund);
        mv.addObject("finalRefundableAmount", finalRefundableAmount);
        mv.addObject("isDetail", false);
        return mv;
    }

    /**
     * 编辑售后退款信息
     */
    @RequestMapping(value = "/refundInfo/detail")
    @NoNeedAccessAuthorization
    public ModelAndView detail(Integer afterSalesId, Integer type, Integer refund, BigDecimal finalRefundableAmount) {
        ModelAndView mv = new ModelAndView("vue/view/afterSalesOrder/refund_info");
        mv.addObject("afterSalesId", afterSalesId);
        mv.addObject("type", type);
        mv.addObject("refund", refund);
        mv.addObject("finalRefundableAmount", finalRefundableAmount);
        mv.addObject("isDetail", true);
        return mv;
    }

    /**
     * 销售售后新增付款申请
     */
    @RequestMapping(value = "/payApply")
    @NoNeedAccessAuthorization
    public ModelAndView addPayment(Integer afterSalesId) {
        ModelAndView mv = new ModelAndView("vue/view/afterSalesOrder/pay_apply");
        mv.addObject("afterSalesId", afterSalesId);
        return mv;
    }
}
