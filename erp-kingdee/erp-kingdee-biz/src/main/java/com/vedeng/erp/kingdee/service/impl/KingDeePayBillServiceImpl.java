package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.vedeng.common.core.domain.FileInfoDto;
import com.vedeng.common.core.utils.FileInfoUtils;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.kingdee.batch.common.enums.ChoosePayObjectEnum;
import com.vedeng.erp.kingdee.batch.dto.BatchAfterSalesDto;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.domain.command.KingDeeFileCommand;
import com.vedeng.erp.kingdee.domain.command.KingDeePayBillCommand;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeCustomerDto;
import com.vedeng.erp.kingdee.dto.KingDeePayBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeSupplierDto;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayBillCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePayBillConvertor;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeCustomerMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePayBillMapper;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.erp.kingdee.service.KingDeePayBillApiService;
import com.vedeng.erp.kingdee.service.KingDeePayBillService;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoRet;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryExtParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeQueryFilterDto;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeSysReportParam;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeSysReportResultDto;
import com.vedeng.infrastructure.kingdee.service.impl.KingDeeMqBaseServiceImpl;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 付款单逻辑处理实现类
 */
@Service
public class KingDeePayBillServiceImpl extends KingDeeMqBaseServiceImpl<KingDeePayBillDto>
        implements KingDeePayBillService, KingDeePayBillApiService {
    private static final Logger logger = LoggerFactory.getLogger(KingDeePayBillServiceImpl.class);

    public static final String KING_DEE = "kingDee";
    public static final String PDF = "pdf";

    @Value("${payIsAutoSubmitAndAudit}")
    public Integer payIsAutoSubmitAndAudit;



    @Resource
    private KingDeeSupplierMapper kingDeeSupplierMapper;

    @Resource
    private KingDeeCustomerMapper kingDeeCustomerMapper;

    @Resource
    private KingDeePayBillMapper kingDeePayBillMapper;

    @Autowired
    private KingDeePayBillCommandConvertor commandConvertor;

    @Autowired
    private KingDeePayBillConvertor kingDeePayBillConvertor;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Autowired
    private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Override
    public void savePayBillInfo(KingDeePayBillDto kingDeePayBillDto) {
        kingDeePayBillDto.setFId(KingDeeConstant.ZERO.toString());
        logger.info("是否是忽略项：{}",kingDeePayBillDto.getIsIgnore());
        if (!kingDeePayBillDto.getIsIgnore()){
            // 非忽略项
            if (kingDeePayBillDto.getTraderSupplierId() == null && kingDeePayBillDto.getTraderCustomerId() == null) {
                logger.error("付款单收款对象为空，请检查报文后重试");
                throw new KingDeeException("付款单收款对象为空,付款单推送失败");
            }

            if (kingDeePayBillDto.getTraderSupplierId() != null) {
                //供应商查询金蝶供应商的id
                KingDeeSupplierDto kingDeeSupplierDto = kingDeeSupplierMapper.queryInfoBySupplierId(kingDeePayBillDto.getTraderSupplierId());
                if (kingDeeSupplierDto == null) {
                    logger.error("供应商ID{},尚未同步至金蝶，消费MQ失败", kingDeePayBillDto.getTraderSupplierId());
                    throw new KingDeeException("供应商信息未同步至金蝶，付款单推送失败");
                }
                kingDeePayBillDto.setFContactUnit(kingDeeSupplierDto.getFNumber().toString());
                kingDeePayBillDto.setFRectUnit(kingDeeSupplierDto.getFNumber().toString());
            }

            if (kingDeePayBillDto.getTraderCustomerId() != null) {
                //客户查询金蝶客户id
                KingDeeCustomerDto kingDeeCustomerDto = kingDeeCustomerMapper.queryInfoByCustomerId(kingDeePayBillDto.getTraderCustomerId());
                if (kingDeeCustomerDto == null) {
                    logger.error("客户ID{},尚未同步至金蝶，消费MQ失败", kingDeePayBillDto.getTraderCustomerId());
                    throw new KingDeeException("客户信息未同步至金蝶，付款单推送失败");
                }
                kingDeePayBillDto.setFContactUnit(kingDeeCustomerDto.getFNumber().toString());
                kingDeePayBillDto.setFRectUnit(kingDeeCustomerDto.getFNumber().toString());
            }
        }


        // 调用金蝶接口
        KingDeePayBillCommand command = commandConvertor.toCommand(kingDeePayBillDto);
        //保存付款申请时先查询付款单是否已推送金蝶
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.AP_PAYBILL);
        //查询金蝶字段
        queryParam.setFieldKeys("fid,fbillno");
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fbillno").value(kingDeePayBillDto.getFBillNo()).build());
        queryParam.setFilterString(queryFilterDtos);
        List<Map<String, Object>> query = kingDeeBaseApi.queryReturnMap(queryParam);
        logger.info("付款单ID{},金蝶查询反馈信息{},", kingDeePayBillDto.getFBillNo(), JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            //付款单已推送金蝶，因为丢包导致未拿到信息，直接保存金蝶id
            String fId = query.get(0).get("FID") == null ? null : query.get(0).get("FID").toString();
            kingDeePayBillDto.setFId(fId);
        } else {
            boolean isAutoSubmitAndAudit;
            if (kingDeePayBillDto.getErpBankBillId() != null && kingDeePayBillDto.getErpBankBillId() > 0){
                // 已付款的付款单推送金蝶
                isAutoSubmitAndAudit = Objects.equals(payIsAutoSubmitAndAudit, 1);
                logger.info("付款单ID{},getErpBankBillId不为空：{},isAutoSubmitAndAudit:{}",kingDeePayBillDto.getFBillNo(),kingDeePayBillDto.getErpBankBillId(),isAutoSubmitAndAudit);
            }else {
                // 使用原有参数，未付款的付款单推送金蝶
                isAutoSubmitAndAudit = kingDeePayBillDto.getIsAutoSubmitAndAudit();
                command.setF_qzok_zdtjyh(kingDeePayBillDto.getFPayBillEntry().get(0).getFQzokZdtjyh());
                logger.info("付款单ID{},getErpBankBillId为空,isAutoSubmitAndAudit:{}",kingDeePayBillDto.getFBillNo(),isAutoSubmitAndAudit);
            }
            logger.info("付款单ID{},付款单推送金蝶标识，自动付款标识：{},自动提交银行：{}",kingDeePayBillDto.getFBillNo(),isAutoSubmitAndAudit,command.getF_qzok_zdtjyh());
            //未推送金蝶，推送金蝶付款单
            RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, isAutoSubmitAndAudit, kingDeePayBillDto.getFormId()));
            if (save.isIsSuccess()) {
                ArrayList<SuccessEntity> successEntitys = save.getSuccessEntitys();
                if (CollUtil.isNotEmpty(successEntitys)) {
                    SuccessEntity successEntity = CollUtil.getFirst(successEntitys);
                    kingDeePayBillDto.setFId(successEntity.getId());
                } else {
                    throw new KingDeeException(StrUtil.format("保存金蝶信息失败,errors:{}",JSON.toJSON(save.getErrors())));
                }
            }else {
                throw new KingDeeException(StrUtil.format("保存金蝶信息失败,errors:{}",JSON.toJSON(save.getErrors())));
            }

        }
        //保存金蝶付款单信息
        KingDeePayBillEntity entity = kingDeePayBillConvertor.toEntity(kingDeePayBillDto);
        entity.setFQzokZdtjyh(StringUtils.isNotBlank(command.getF_qzok_zdtjyh()) ? command.getF_qzok_zdtjyh() : "2");
        kingDeePayBillMapper.insertSelective(entity);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePayBillAli(KingDeePayBillDto kingDeePayBillDto) {
        String fileUrl = kingDeePayBillDto.getFQzokDzhzd();
        kingDeePayBillDto.setFQzokDzhzd("");
        kingDeePayBillDto.setFId(KingDeeConstant.ZERO.toString());

        if (kingDeePayBillDto.getTraderCustomerId() == null) {
            logger.error("付款单-支付宝流水收款对象为空，请检查报文后重试");
            throw new KingDeeException("付款单-支付宝流水收款对象为空,付款单-支付宝流水推送失败");
        }

        if (kingDeePayBillDto.getTraderCustomerId() != null) {
            //客户查询金蝶客户id
            KingDeeCustomerDto kingDeeCustomerDto = kingDeeCustomerMapper.queryInfoByCustomerId(kingDeePayBillDto.getTraderCustomerId());
            if (kingDeeCustomerDto == null) {
                logger.error("客户ID{},尚未同步至金蝶，消费付款单-支付宝流水MQ失败", kingDeePayBillDto.getTraderCustomerId());
                throw new KingDeeException("客户信息未同步至金蝶，付款单-支付宝流水推送失败");
            }
            kingDeePayBillDto.setFContactUnit(kingDeeCustomerDto.getFNumber().toString());
            kingDeePayBillDto.setFRectUnit(kingDeeCustomerDto.getFNumber().toString());
        }

        // 调用金蝶接口
        KingDeePayBillCommand command = commandConvertor.toCommand(kingDeePayBillDto);
        //保存付款申请时先查询付款单是否已推送金蝶
        KingDeeQueryExtParam queryParam = new KingDeeQueryExtParam();
        queryParam.setFormId(KingDeeFormConstant.AP_PAYBILL);
        //查询金蝶字段
        queryParam.setFieldKeys("fid,fbillno");
        List<KingDeeQueryFilterDto> queryFilterDtos = new ArrayList<>();
        queryFilterDtos.add(KingDeeQueryFilterDto.builder().fieldName("fbillno").value(kingDeePayBillDto.getFBillNo()).build());
        queryParam.setFilterString(queryFilterDtos);
        List<Map<String, Object>> query = kingDeeBaseApi.queryReturnMap(queryParam);
        logger.info("付款单-支付宝流水号{},金蝶查询反馈信息{},", kingDeePayBillDto.getFBillNo(), JSON.toJSONString(query));
        if (CollUtil.isNotEmpty(query)) {
            //付款单已推送金蝶，因为丢包导致未拿到信息，直接保存金蝶id
            String fId = query.get(0).get("FID") == null ? null : query.get(0).get("FID").toString();
            kingDeePayBillDto.setFId(fId);
        } else {
            //未推送金蝶，推送金蝶付款单
            RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, true, kingDeePayBillDto.getFormId()));
            ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();
            logger.info("付款单调用金蝶接口返回信息{},",  new Gson().toJson(successEntities));
            if (CollUtil.isNotEmpty(successEntities)) {
                SuccessEntity successEntity = CollUtil.getFirst(successEntities);
                kingDeePayBillDto.setFId(successEntity.getId());
            } else {
                throw new KingDeeException(StrUtil.format("保存金蝶信息失败,errors:{}",JSON.toJSON(save.getErrors())));
            }
        }
        kingDeePayBillDto.setFQzokDzhzd(fileUrl);
        //保存金蝶付款单信息
        kingDeePayBillMapper.insertSelective(kingDeePayBillConvertor.toEntity(kingDeePayBillDto));


        //推送附件---金蝶无法提供附件的幂等校验
        KingDeeFileCommand kingDeeFileCommand = new KingDeeFileCommand();
        FileInfoDto fileInfoDto = FileInfoUtils.getBase64FromUrl(fileUrl);
        kingDeeFileCommand.setFormId(KingDeeFormConstant.AP_PAYBILL);
        kingDeeFileCommand.setInterId(kingDeePayBillDto.getFId());
        kingDeeFileCommand.setFileName(kingDeePayBillDto.getFQzokCgddh() + fileInfoDto.getSuffix());
        kingDeeFileCommand.setBillNo(kingDeePayBillDto.getFBillNo());
        kingDeeFileCommand.setAliasFileName(kingDeePayBillDto.getFQzokCgddh());
        kingDeeFileCommand.setSendByte(fileInfoDto.getFileBase64());
        logger.info("付款单-支付宝流水回单上传金蝶，报文{},", JSON.toJSONString(kingDeeFileCommand));
        RepoRet fileResult = kingDeeBaseApi.attachmentUpload(kingDeeFileCommand);
        if (fileResult == null) {
            logger.info("付款单-支付宝流水回单上传金蝶失败");
            throw new KingDeeException("付款单-支付宝流水回单上传失败");
        } else {
            if (fileResult.getResult().getResponseStatus().isIsSuccess()) {
                //收到成功保存附件信息
                logger.info("付款单-支付宝流水回单保存成功{},", JSON.toJSONString(fileResult));
            } else {
                logger.info("付款单-支付宝流水回单上传金蝶失败,金蝶反馈{},", JSON.toJSONString(fileResult));
                throw new KingDeeException("付款单-支付宝流水回单上传失败");
            }
        }
    }

    @Override
    public List<KingDeePayBillDto> queryKingDeePayBillNoReturnUrl() {
        return kingDeePayBillMapper.queryKingDeePayBillNoReturnUrl();
    }

    @Override
    public List<KingDeePayBillDto> queryKingDeeReceiveRefundBillNoReturnUrl() {
        return kingDeePayBillMapper.queryKingDeeReceiveRefundBillNoReturnUrl();
    }


    @Override
    public List<KingDeePayBillDto> queryKingDeePayBillNoReturnUrlByKingDeePayBillIds(List<Integer> kingDeePayBillIds) {
        return kingDeePayBillMapper.queryKingDeePayBillNoReturnUrlAndIds(kingDeePayBillIds);
    }


    @Override
    public boolean queryKingDeeReturnUrlAndTranFlowAndBank(KingDeePayBillDto data) {
        KingDeeSysReportParam queryParam = new KingDeeSysReportParam();
        queryParam.setFormId(KingDeeFormConstant.RECEIPT_BILL);
        queryParam.setFieldKeys("fbillno,fbusirefeno,fdata");
        KingDeeSysReportParam.QueryModel model = new KingDeeSysReportParam.QueryModel();
        model.setFPayBillNo(data.getFBillNo());
        queryParam.setModel(model);
        KingDeeSysReportResultDto kingDeeSysReportResultDto = kingDeeBaseApi.getSysReportData(KingDeeFormConstant.RECEIPT_BILL,queryParam,KingDeeSysReportResultDto.class);
        if (kingDeeSysReportResultDto != null) {
            if(CollUtil.isNotEmpty(kingDeeSysReportResultDto.getResult().get(0).getRows())
                    && kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).size() == 3) {
                String dzhzd = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(2) == null ? null : kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(2).trim();
                data.setFQzokDzhzd(dzhzd);
                String lsh = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(1) == null ? null : kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(1).trim();
                data.setFQzokLsh(lsh);
                String yhzhhm = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(0) == null ? null : kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(0).trim();
                data.setFQzokYhzhhm(yhzhhm);
                return true;
            }else {
                logger.info("查询金蝶回单流水返回信息为{},无法完成回单",JSON.toJSONString(kingDeeSysReportResultDto));
                return false;
            }
        }
        return false;
    }

    /**
     * 金蝶回单
     * @param tranFlow
     * @return
     */
    @Override
    public String queryKingDeeReturnUrl(String tranFlow) {
        String formID = KingDeeFormConstant.BANK_BILL;
        KingDeeSysReportParam queryParam = new KingDeeSysReportParam();
//        queryParam.setFormId(formID);
        queryParam.setFieldKeys("fdata");
        queryParam.setLimit(2000);
        queryParam.setStartRow(0);
        KingDeeSysReportParam.QueryModel model = new KingDeeSysReportParam.QueryModel();
        model.setFPayBillNo(tranFlow);
        queryParam.setModel(model);
        KingDeeSysReportResultDto kingDeeSysReportResultDto = kingDeeBaseApi.getSysReportData(formID,queryParam,KingDeeSysReportResultDto.class);
        if (Objects.isNull(kingDeeSysReportResultDto)){
            logger.info("查询金蝶回单流水返回信息为{},未获取到回单",JSON.toJSONString(kingDeeSysReportResultDto));
            return null;
        }
        if (CollUtil.isEmpty(kingDeeSysReportResultDto.getResult().get(0).getRows())){
            logger.info("查询金蝶回单流水返回信息为{},未获取到回单",JSON.toJSONString(kingDeeSysReportResultDto));
            return null;
        }
        if (Objects.isNull(kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(0))){
            logger.info("查询金蝶回单流水返回信息为{},未获取到回单",JSON.toJSONString(kingDeeSysReportResultDto));
            return null;
        }
        String kingDeeReturnUrlBase64 = kingDeeSysReportResultDto.getResult().get(0).getRows().get(0).get(0).trim();
        return kingDeeReturnUrlBase64;
    }

    @Override
    public void updateKingDeePayBillReturnUrlAndTranFlow(KingDeePayBillDto data) {
        KingDeePayBillEntity updte = new KingDeePayBillEntity();
        updte.setKingDeePayBillId(data.getKingDeePayBillId());
        updte.setFQzokLsh(data.getFQzokLsh());
//        VDERP-12561 不需要金蝶的回单信息start
//        updte.setFQzokDzhzd(data.getFQzokDzhzd());
//        VDERP-12561 不需要金蝶的回单信息end
        kingDeePayBillMapper.updateByPrimaryKeySelective(updte);
    }

    @Override
    public String downloadFile(String file, String fileName, String suffix) {
        if (StrUtil.isEmpty(fileName)) {
            fileName = KING_DEE + IdUtil.simpleUUID();
        }
        if (StrUtil.isEmpty(suffix)) {
            suffix = PDF;
        }
        InputStream inputStream = null;
        try {
            // 下载文件base64
            byte[] bytes = Base64.decode(file);
            inputStream = new ByteArrayInputStream(bytes);
            // 上传SSO
            return ossUtilsService.upload2OssForInputStream(suffix, fileName, inputStream);
        } catch (Exception e) {
            logger.warn("回单base64文件转换异常");
            throw new KingDeeException("回单base64文件转换异常");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                logger.error("金蝶文件流关闭失败");
            }
        }
    }

    @Override
    public void deleteKingDeeInfoByFBillNo(String fBillNo) {
        int i = kingDeePayBillMapper.deleteKingDeeInfoByFBillNo(fBillNo);
        if (i == 0){
            kingDeePayBillMapper.deleteReceiveRefundInfoByFBillNo(fBillNo);
        }
    }

    @Override
    public List<KingDeePayBillDto> getPayBillInfoBybankBillId(Integer erpBankBillId,Integer fileIsPush) {
        return kingDeePayBillMapper.getKingDeePayByBankBillId(erpBankBillId,fileIsPush);
    }

    @Override
    public String choosePayObject(Integer payType, Integer relatedId) {
        if (ChoosePayObjectEnum.AFTER_SALE.getSysOptionId().equals(payType)){
            //根据relatedId查afterSales
            BatchAfterSalesDto afterSalesDto = batchAfterSalesDtoMapper.findByAfterSalesId(relatedId);
            if (ObjectUtil.isNotNull(afterSalesDto) && ChoosePayObjectEnum.SALES.getSysOptionId().equals(afterSalesDto.getSubjectType())
                    && !CollUtil.contains(ChoosePayObjectEnum.supplierTypeList(),afterSalesDto.getType())){
                return ChoosePayObjectEnum.BD_CUSTOMER;
            }
        }
        return ChoosePayObjectEnum.BD_SUPPLIER;
    }

}
