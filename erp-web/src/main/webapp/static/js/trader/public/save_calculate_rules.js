$(function () {
    $("#submit").click(function () {
        checkLogin();
        $(".warning").remove();
        $("input").removeClass("errorbor");

        let customerCreatedDays = $("input[name='customerCreatedDays']").val();
        if (customerCreatedDays === undefined || customerCreatedDays === '') {
            warnTips("customerCreatedDaysError", "请完整填写新客保护期条件");
            return false;
        } else if (!(/^\+?[1-9]{1}[0-9]{0,2}\d{0,0}$/).test(customerCreatedDays)) {
            warnTips("customerCreatedDaysError", "请输入1-999的正整数");
            return false;
        }

        let validOrderDays = $("input[name='validOrderDays']").val();
        let validOrderCount = $("input[name='validOrderCount']").val();

        if (validOrderDays === undefined || validOrderDays === '' || validOrderCount === undefined || validOrderCount === '') {
            warnTips("validOrderDaysError", "请完整填写订单周期条件");
            return false;
        } else if (!(/^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$/).test(validOrderDays)) {
            warnTips("validOrderDaysError", "请输入1-999的整数");
            return false;
        } else if (!(/^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$/).test(validOrderCount)) {
            warnTips("validOrderDaysError", "生效订单数为0-999的整数");
            return false;
        }

        let communicationDays = $("input[name='communicationDays']").val();
        let communicationCount = $("input[name='communicationCount']").val();

        if (communicationDays === undefined || communicationDays === '') {
            warnTips("communicationDaysError", "请完整填写沟通次数条件");
            return false;
        } else if (!(/^([1-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$/).test(communicationDays)) {
            warnTips("communicationDaysError", "请输入1-999的整数");
            return false;
        } else if (!(/^([0-9]|[1-9][0-9]|[1-9][0-9][0-9]|1000)$/).test(communicationCount)) {
            warnTips("communicationDaysError", "接通电话数为0-999的整数");
            return false;
        }

        let lockProtectDays = $("input[name='lockProtectDays']").val();
        if (lockProtectDays === undefined || lockProtectDays === '') {
            warnTips("lockProtectDaysError", "请完整填写解锁保护期条件");
            return false;
        } else if (!(/^\+?[1-9]{1}[0-9]{0,2}\d{0,0}$/).test(lockProtectDays)) {
            warnTips("lockProtectDaysError", "请输入1-999的正整数");
            return false;
        }

        $.ajax({
            type: "POST",
            url: "/trader/customer/save/calculate/rules.do",
            data: $('#saveCalculateRule').serialize(),
            dataType: 'json',
            async: false,
            success: function (data) {
                if (data.code == 0) {

                    parent.layer.msg('保存成功', {
                        icon: 1,
                        time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    }, function(){
                        parent.location.reload(true);
                    });
                } else {
                    layer.alert(data.message, {icon: 2},
                        function (index) {
                            layer.close(index);
                            return false;
                        }
                    );
                }

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                }
            }
        });


    })
});
