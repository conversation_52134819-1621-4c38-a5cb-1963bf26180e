<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.broadcast.mapdao.BroadcastGlobalConfigMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="EXCLUDE_SALE_IDS" jdbcType="VARCHAR" property="excludeSaleIds"/>
        <result column="AED_SKU_IDS" jdbcType="VARCHAR" property="aedSkuIds"/>
        <result column="EXCLUDE_TRADER_IDS" jdbcType="VARCHAR" property="excludeTraderIds"/>
        <result column="TOPN_USER" jdbcType="INTEGER" property="topnUser"/>
        <result column="TOPN_DEPT" jdbcType="INTEGER" property="topnDept"/>
        <result column="BROADCAST_TITLE_DAY" jdbcType="VARCHAR" property="broadcastTitleDay"/>
        <result column="BROADCAST_TITLE_WEEK" jdbcType="VARCHAR" property="broadcastTitleWeek"/>
        <result column="BROADCAST_TITLE_MONTH" jdbcType="VARCHAR" property="broadcastTitleMonth"/>
        <result column="BROADCAST_TITLE_AED" jdbcType="VARCHAR" property="broadcastTitleAed"/>
        <result column="BROADCAST_TITLE_ZY" jdbcType="VARCHAR" property="broadcastTitleZy"/>
        <result column="BROADCAST_TITLE_CUSTOM" jdbcType="VARCHAR" property="broadcastTitleCustom"/>
        <result column="STAT_TYPE" jdbcType="TINYINT" property="statType"/>
        <result column="STAT_DATE_RANGE" jdbcType="TINYINT" property="statDateRange"/>
        <result column="STAT_TARGET" jdbcType="VARCHAR" property="statTarget"/>
        <result column="STAT_SKU_IDS" jdbcType="VARCHAR" property="statSkuIds"/>
        <result column="STAT_BRAND_IDS" jdbcType="VARCHAR" property="statBrandIds"/>
        <result column="STAT_CATEGORY_IDS" jdbcType="VARCHAR" property="statCategoryIds"/>
        <result column="IS_DELETED" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID, EXCLUDE_SALE_IDS, AED_SKU_IDS, EXCLUDE_TRADER_IDS, TOPN_USER, TOPN_DEPT,
        BROADCAST_TITLE_DAY, BROADCAST_TITLE_WEEK, BROADCAST_TITLE_MONTH, BROADCAST_TITLE_AED,
        BROADCAST_TITLE_ZY, BROADCAST_TITLE_CUSTOM, STAT_TYPE, STAT_DATE_RANGE, STAT_TARGET,
        STAT_SKU_IDS, STAT_BRAND_IDS, STAT_CATEGORY_IDS, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_GLOBAL_CONFIG
        where ID = #{id,jdbcType=INTEGER}
        and IS_DELETED = 0
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        update T_BROADCAST_GLOBAL_CONFIG
        set IS_DELETED = 1, MOD_TIME = now()
        where ID = #{id,jdbcType=INTEGER}
    </delete>

    <insert id="insert" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_GLOBAL_CONFIG (EXCLUDE_SALE_IDS, AED_SKU_IDS, EXCLUDE_TRADER_IDS,
        TOPN_USER, TOPN_DEPT, BROADCAST_TITLE_DAY,
        BROADCAST_TITLE_WEEK, BROADCAST_TITLE_MONTH, BROADCAST_TITLE_AED,
        BROADCAST_TITLE_ZY, BROADCAST_TITLE_CUSTOM, STAT_TYPE,
        STAT_DATE_RANGE, STAT_TARGET, STAT_SKU_IDS,
        STAT_BRAND_IDS, STAT_CATEGORY_IDS, IS_DELETED,
        ADD_TIME, MOD_TIME, CREATOR,
        UPDATER)
        values (#{excludeSaleIds,jdbcType=VARCHAR}, #{aedSkuIds,jdbcType=VARCHAR}, #{excludeTraderIds,jdbcType=VARCHAR},
        #{topnUser,jdbcType=INTEGER}, #{topnDept,jdbcType=INTEGER}, #{broadcastTitleDay,jdbcType=VARCHAR},
        #{broadcastTitleWeek,jdbcType=VARCHAR}, #{broadcastTitleMonth,jdbcType=VARCHAR}, #{broadcastTitleAed,jdbcType=VARCHAR},
        #{broadcastTitleZy,jdbcType=VARCHAR}, #{broadcastTitleCustom,jdbcType=VARCHAR}, #{statType,jdbcType=TINYINT},
        #{statDateRange,jdbcType=TINYINT}, #{statTarget,jdbcType=VARCHAR}, #{statSkuIds,jdbcType=VARCHAR},
        #{statBrandIds,jdbcType=VARCHAR}, #{statCategoryIds,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT},
        #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
        #{updater,jdbcType=INTEGER})
    </insert>

    <insert id="insertSelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into T_BROADCAST_GLOBAL_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="excludeSaleIds != null">
                EXCLUDE_SALE_IDS,
            </if>
            <if test="aedSkuIds != null">
                AED_SKU_IDS,
            </if>
            <if test="excludeTraderIds != null">
                EXCLUDE_TRADER_IDS,
            </if>
            <if test="topnUser != null">
                TOPN_USER,
            </if>
            <if test="topnDept != null">
                TOPN_DEPT,
            </if>
            <if test="broadcastTitleDay != null">
                BROADCAST_TITLE_DAY,
            </if>
            <if test="broadcastTitleWeek != null">
                BROADCAST_TITLE_WEEK,
            </if>
            <if test="broadcastTitleMonth != null">
                BROADCAST_TITLE_MONTH,
            </if>
            <if test="broadcastTitleAed != null">
                BROADCAST_TITLE_AED,
            </if>
            <if test="broadcastTitleZy != null">
                BROADCAST_TITLE_ZY,
            </if>
            <if test="broadcastTitleCustom != null">
                BROADCAST_TITLE_CUSTOM,
            </if>
            <if test="statType != null">
                STAT_TYPE,
            </if>
            <if test="statDateRange != null">
                STAT_DATE_RANGE,
            </if>
            <if test="statTarget != null">
                STAT_TARGET,
            </if>
            <if test="statSkuIds != null">
                STAT_SKU_IDS,
            </if>
            <if test="statBrandIds != null">
                STAT_BRAND_IDS,
            </if>
            <if test="statCategoryIds != null">
                STAT_CATEGORY_IDS,
            </if>
            <if test="isDeleted != null">
                IS_DELETED,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="excludeSaleIds != null">
                #{excludeSaleIds,jdbcType=VARCHAR},
            </if>
            <if test="aedSkuIds != null">
                #{aedSkuIds,jdbcType=VARCHAR},
            </if>
            <if test="excludeTraderIds != null">
                #{excludeTraderIds,jdbcType=VARCHAR},
            </if>
            <if test="topnUser != null">
                #{topnUser,jdbcType=INTEGER},
            </if>
            <if test="topnDept != null">
                #{topnDept,jdbcType=INTEGER},
            </if>
            <if test="broadcastTitleDay != null">
                #{broadcastTitleDay,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleWeek != null">
                #{broadcastTitleWeek,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleMonth != null">
                #{broadcastTitleMonth,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleAed != null">
                #{broadcastTitleAed,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleZy != null">
                #{broadcastTitleZy,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleCustom != null">
                #{broadcastTitleCustom,jdbcType=VARCHAR},
            </if>
            <if test="statType != null">
                #{statType,jdbcType=TINYINT},
            </if>
            <if test="statDateRange != null">
                #{statDateRange,jdbcType=TINYINT},
            </if>
            <if test="statTarget != null">
                #{statTarget,jdbcType=VARCHAR},
            </if>
            <if test="statSkuIds != null">
                #{statSkuIds,jdbcType=VARCHAR},
            </if>
            <if test="statBrandIds != null">
                #{statBrandIds,jdbcType=VARCHAR},
            </if>
            <if test="statCategoryIds != null">
                #{statCategoryIds,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity">
        update T_BROADCAST_GLOBAL_CONFIG
        <set>
            <if test="excludeSaleIds != null">
                EXCLUDE_SALE_IDS = #{excludeSaleIds,jdbcType=VARCHAR},
            </if>
            <if test="aedSkuIds != null">
                AED_SKU_IDS = #{aedSkuIds,jdbcType=VARCHAR},
            </if>
            <if test="excludeTraderIds != null">
                EXCLUDE_TRADER_IDS = #{excludeTraderIds,jdbcType=VARCHAR},
            </if>
            <if test="topnUser != null">
                TOPN_USER = #{topnUser,jdbcType=INTEGER},
            </if>
            <if test="topnDept != null">
                TOPN_DEPT = #{topnDept,jdbcType=INTEGER},
            </if>
            <if test="broadcastTitleDay != null">
                BROADCAST_TITLE_DAY = #{broadcastTitleDay,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleWeek != null">
                BROADCAST_TITLE_WEEK = #{broadcastTitleWeek,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleMonth != null">
                BROADCAST_TITLE_MONTH = #{broadcastTitleMonth,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleAed != null">
                BROADCAST_TITLE_AED = #{broadcastTitleAed,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleZy != null">
                BROADCAST_TITLE_ZY = #{broadcastTitleZy,jdbcType=VARCHAR},
            </if>
            <if test="broadcastTitleCustom != null">
                BROADCAST_TITLE_CUSTOM = #{broadcastTitleCustom,jdbcType=VARCHAR},
            </if>
            <if test="statType != null">
                STAT_TYPE = #{statType,jdbcType=TINYINT},
            </if>
            <if test="statDateRange != null">
                STAT_DATE_RANGE = #{statDateRange,jdbcType=TINYINT},
            </if>
            <if test="statTarget != null">
                STAT_TARGET = #{statTarget,jdbcType=VARCHAR},
            </if>
            <if test="statSkuIds != null">
                STAT_SKU_IDS = #{statSkuIds,jdbcType=VARCHAR},
            </if>
            <if test="statBrandIds != null">
                STAT_BRAND_IDS = #{statBrandIds,jdbcType=VARCHAR},
            </if>
            <if test="statCategoryIds != null">
                STAT_CATEGORY_IDS = #{statCategoryIds,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null">
                IS_DELETED = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity">
        update T_BROADCAST_GLOBAL_CONFIG
        set EXCLUDE_SALE_IDS = #{excludeSaleIds,jdbcType=VARCHAR},
        AED_SKU_IDS = #{aedSkuIds,jdbcType=VARCHAR},
        EXCLUDE_TRADER_IDS = #{excludeTraderIds,jdbcType=VARCHAR},
        TOPN_USER = #{topnUser,jdbcType=INTEGER},
        TOPN_DEPT = #{topnDept,jdbcType=INTEGER},
        BROADCAST_TITLE_DAY = #{broadcastTitleDay,jdbcType=VARCHAR},
        BROADCAST_TITLE_WEEK = #{broadcastTitleWeek,jdbcType=VARCHAR},
        BROADCAST_TITLE_MONTH = #{broadcastTitleMonth,jdbcType=VARCHAR},
        BROADCAST_TITLE_AED = #{broadcastTitleAed,jdbcType=VARCHAR},
        BROADCAST_TITLE_ZY = #{broadcastTitleZy,jdbcType=VARCHAR},
        BROADCAST_TITLE_CUSTOM = #{broadcastTitleCustom,jdbcType=VARCHAR},
        STAT_TYPE = #{statType,jdbcType=TINYINT},
        STAT_DATE_RANGE = #{statDateRange,jdbcType=TINYINT},
        STAT_TARGET = #{statTarget,jdbcType=VARCHAR},
        STAT_SKU_IDS = #{statSkuIds,jdbcType=VARCHAR},
        STAT_BRAND_IDS = #{statBrandIds,jdbcType=VARCHAR},
        STAT_CATEGORY_IDS = #{statCategoryIds,jdbcType=VARCHAR},
        IS_DELETED = #{isDeleted,jdbcType=TINYINT},
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
        CREATOR = #{creator,jdbcType=INTEGER},
        UPDATER = #{updater,jdbcType=INTEGER}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <!-- 自定义-->
    <select id="selectGlobalConfig"
            resultType="com.vedeng.erp.broadcast.domain.entity.BroadcastGlobalConfigEntity">
        select
        <include refid="Base_Column_List"/>
        from T_BROADCAST_GLOBAL_CONFIG
        where  IS_DELETED = 0 limit 1
    </select>

</mapper>
