package com.wms.service.impl;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOperateLogMapper;
import com.vedeng.logistics.dao.WmsOutputOrderGoodsExtraMapper;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.wms.constant.ReturnStatusConstant;
import com.wms.constant.VerifyStatusEnum;
import com.wms.constant.WmsOutputOrderTypeConstant;
import com.wms.dao.WmsOutputOrderGoodsMapper;
import com.wms.dao.WmsOutputOrderMapper;
import com.wms.dao.WmsReceiveOutMapper;
import com.wms.model.dto.AddReceiveOutDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSReceiveOutService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
public class WMSReceiveOutServiceImpl implements WMSReceiveOutService {
    @Autowired
    private WmsReceiveOutMapper wmsReceiveOutMapper;

    @Resource
    private WarehouseGoodsOperateLogMapper warehouseGoodsOperateLogMapper;

    @Autowired
    private WmsOutputOrderMapper wmsOutputOrderMapper;

    @Autowired
    private WmsOutputOrderGoodsMapper wmsOutputOrderGoodsMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Autowired
    private WmsOutputOrderGoodsExtraMapper wmsOutputOrderGoodsExtraMapper;



    @Override
    public WmsOutputOrder findReceiveOutById(Long receiveOutId) {
        return wmsReceiveOutMapper.selectById(receiveOutId);
    }

    @Override
    public List<WmsOutputOrderGoodsDto> queryOutputGoodsByReceiveOutId(Long receiveOutId) {
        return wmsReceiveOutMapper.queryOutputGoodsByReceiveOutId(receiveOutId);
    }

    @Override
    public List<WarehouseGoodsOperateLog> getWlogList(Integer wmsOutputOrderId, Integer logOperateType) {
        return warehouseGoodsOperateLogMapper.getWmsOutputOrderLogListByOrderId(wmsOutputOrderId,logOperateType);
    }

    /**
     * 更新wms状态
     * @param receiveOutId
     * @param auditStatusValue
     */
    @Override
    public void updateReceiveOutAuditStatus(Long receiveOutId, int auditStatusValue) {
        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setId(receiveOutId);
        wmsOutputOrder.setVerifyStatus(auditStatusValue);
        wmsOutputOrder.setUpdateTime(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        wmsReceiveOutMapper.updateByPrimaryKeySelective(wmsOutputOrder);
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public Long addReceiveOutOrder(AddReceiveOutDto addReceiveOutDto, User user) throws Exception {
        Integer receiveOutUserId = addReceiveOutDto.getReceiveOutUserId().intValue();
        User receiveOutUser = userMapper.getUserInfoByUserId(receiveOutUserId);
        WmsOutputOrder receiveOutOrder = convertToReceiveOutBean(addReceiveOutDto,user,receiveOutUser);
        wmsOutputOrderMapper.insertSelective(receiveOutOrder);
        List<WmsOutputOrderGoods> receiveOutOrderGoods = convertToReceiveOutGoodsBean(receiveOutOrder,addReceiveOutDto);
        wmsOutputOrderGoodsMapper.batchInsert(receiveOutOrderGoods);
        return receiveOutOrder.getId();
    }

    @Override
    public User getUserInfoByUserId(Integer userId) {
        return userMapper.getUserInfoByUserId(userId);
    }

    /**
     * 类型转换
     * @param addReceiveOutDto
     * @param user
     * @param receiveOutUser
     * @return
     */
    private WmsOutputOrder convertToReceiveOutBean(AddReceiveOutDto addReceiveOutDto, User user, User receiveOutUser) {
        WmsOutputOrder wmsOutputOrder = new WmsOutputOrder();
        wmsOutputOrder.setType(WmsOutputOrderTypeConstant.RECEIVING);
        wmsOutputOrder.setVerifyStatus(VerifyStatusEnum.ToBeImproved.getValue());
        wmsOutputOrder.setReturnStatus(ReturnStatusConstant.UN_RETURN);
        BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.RECEIVE_OUT_ORDER_LY);
        String receiveOutOrderNo = new BillNumGenerator().distribution(billGeneratorBean);
        wmsOutputOrder.setOrderNo(receiveOutOrderNo);
        wmsOutputOrder.setBorrowReason(addReceiveOutDto.getReceiveOutReason());
        wmsOutputOrder.setLogisticCommnet(addReceiveOutDto.getLogisticComment());

        wmsOutputOrder.setBelongDepartment(user.getOrgName());
        wmsOutputOrder.setCreator(user.getUsername());
        wmsOutputOrder.setUpdator(user.getUsername());

        if (receiveOutUser != null){
            wmsOutputOrder.setReceiver(receiveOutUser.getUsername());
            wmsOutputOrder.setRecipientDepartment(receiveOutUser.getOrgName());
        }

        wmsOutputOrder.setApplyer(user.getUsername());
        wmsOutputOrder.setApplyerDepartmentId(user.getOrgId());
        wmsOutputOrder.setApplyerDepartment(user.getOrgName());
        wmsOutputOrder.setApplyerId(user.getUserId());
        String addTime = DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss");
        wmsOutputOrder.setAddTime(addTime);
        wmsOutputOrder.setUpdateTime(addTime);
        return wmsOutputOrder;
    }

    //新增出库单商品的Bean
    private List<WmsOutputOrderGoods> convertToReceiveOutGoodsBean(WmsOutputOrder receiveOutOrder, AddReceiveOutDto addReceiveOutDto) {

        if(addReceiveOutDto.getSkuNo() == null || addReceiveOutDto.getSkuNo().length == 0){
            return null;
        }
        List<WmsOutputOrderGoods> outputOrderGoodsList = new ArrayList<>();

        for(int i = 0;i < addReceiveOutDto.getSkuNo().length;i++){
            WmsOutputOrderGoods wmsOutputOrderGoods = new WmsOutputOrderGoods();
            outputOrderGoodsList.add(wmsOutputOrderGoods);
            wmsOutputOrderGoods.setWmsOutputOrderId(receiveOutOrder.getId());
            wmsOutputOrderGoods.setSkuNo(addReceiveOutDto.getSkuNo()[i]);
            wmsOutputOrderGoods.setOutputNum(addReceiveOutDto.getReceiveOutNum()[i].intValue());
            wmsOutputOrderGoods.setAddTime(receiveOutOrder.getAddTime());
            wmsOutputOrderGoods.setUpdateTime(receiveOutOrder.getAddTime());
        }
        return outputOrderGoodsList;
    }



}
