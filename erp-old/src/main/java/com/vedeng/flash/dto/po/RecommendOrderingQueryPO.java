package com.vedeng.flash.dto.po;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @program: erp.vedeng.com
 * @description: 推荐定品查询结果集
 * @author: Pusan
 * @create: 2021-05-19 17:50
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class RecommendOrderingQueryPO {

    /** SkuId */
    private Integer skuId;

    /** 订货号 */
    private String skuNo;

    /** 商品名称 */
    private String goodsName;

    /** 品牌 */
    private String brandName;

    /** 商品类型 */
    private String goodsTypeName;

    /** 二级分类 */
    private String categoryNameTwo;

    /** 规格 */
    private String spec;

    /** 商品单位 */
    private String unitName;

    /** 成本价(元) */
    private String costPrice;

    /** 去年销售额 - 总 */
    private BigDecimal lastYearSum;

    /** 去年销售额 - 医械购 */
    private BigDecimal lastYearPart;

    /** 近三个月销售额 - 总 */
    private BigDecimal threeMonthSum;

    /** 近三个月销售额 - 医械购 */
    private BigDecimal threeMonthPart;

    /** 近一个月销售额 - 总 */
    private BigDecimal oneMonthSum;

    /** 近一个月销售额 - 医械购 */
    private BigDecimal oneMonthPart;

    /** 归属人 */
    private String goodsUserName;

    /** SKU审核状态 */
    private String checkStatus;

    /** 操作状态 */
    private Integer joinPoolFlag;
}
