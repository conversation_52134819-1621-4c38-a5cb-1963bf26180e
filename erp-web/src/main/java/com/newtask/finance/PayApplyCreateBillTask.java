package com.newtask.finance;

import com.alibaba.fastjson.JSONObject;
import com.common.constants.Contant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.finance.dto.AutoPayConfigDto;
import com.vedeng.erp.finance.dto.PayApplyCreateBillDto;
import com.vedeng.erp.finance.facade.PayApplyFacade;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.finance.service.PayApplyAutoPayApi;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 自动制单
 */
@Slf4j
@JobHandler(value = "PayApplyCreateBillTask")
@Component
public class PayApplyCreateBillTask extends AbstractJobHandler {

    @Autowired
    private PayApplyApiService payApplyApiService;

    @Autowired
    private PayApplyFacade payApplyFacade;

    @Autowired
    private PayApplyAutoPayApi payApplyAutoPayApi;

    @Autowired
    private RedisUtils redisUtils;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        Integer paramId = StringUtils.isNotBlank(s) ? Integer.valueOf(s) : null;
        // 筛选出满足条件的
        List<PayApplyCreateBillDto> payApply = payApplyAutoPayApi.findPayApply(paramId);
        log.info("待自动制单条数：{}",payApply.size());
        log.info("待自动制单明细：{}",JSONObject.toJSONString(payApply));
        for (PayApplyCreateBillDto payApplyDto : payApply){
            Integer payApplyId = payApplyDto.getPayApplyId();
            log.info("定时任务自动制单开始执行:{},{}",payApplyId, JSONObject.toJSONString(payApplyDto));
            // 是否关闭
            if (!isOpen()){
                log.info("已关闭自动制单,payApplyId:{}",payApplyId);
                break;
            }
            if (Objects.isNull(payApplyDto) || Objects.isNull(payApplyId)){
                log.info("数据异常，跳过该条数据：{}",JSONObject.toJSONString(payApplyDto));
                continue;
            }
            // 过滤
            try{
                payApplyFacade.filter(payApplyDto);
            }catch (ServiceException se){
                log.info("定时任务自动制单过滤，已知异常:{}",payApplyDto.getPayApplyId(),se);
                continue;
            }catch (Exception e){
                log.error("定时任务自动制单过滤，未知异常:{}",payApplyDto.getPayApplyId(),e);
                continue;
            }
            // 校验
            try{
                payApplyFacade.createBillRuleCheck(payApplyDto);
                payApplyApiService.updateAutoBill(payApplyId, 1);
            }catch (ServiceException se){
                log.info("定时任务自动制单校验，已知异常:{}",payApplyDto.getPayApplyId(),se);
                payApplyApiService.updateAutoBill(payApplyId, 0);
                continue;
            }catch (Exception e){
                log.error("定时任务自动制单校验，未知异常:{}",payApplyDto.getPayApplyId(),e);
                payApplyApiService.updateAutoBill(payApplyId, 0);
                continue;
            }

            // 加锁
            boolean lock = redisUtils.tryGetDistributedLock(Contant.REDIS_KEY_LOAD + payApplyId, UUID.randomUUID().toString(), Contant.LOCK_TIME);
            if (!lock) {
                log.info("加锁失败5分钟内不能重复操作,payApplyId:{}",payApplyId);
                continue;
            }

            // 自动制单
            try{
                payApplyFacade.autoCreateCreateBill(payApplyDto);
            }catch (ServiceException se){
                log.info("定时任务自动制单，已知异常:{}",payApplyDto.getPayApplyId(),se);
            }catch (Exception e){
                log.error("定时任务自动制单，未知异常:{}",payApplyDto.getPayApplyId(),e);
            }
            log.info("定时任务自动制单结束：{}",payApplyId);
        }

        return ReturnT.SUCCESS;
    }

    private List<PayApplyCreateBillDto> findPayApply(Integer payApplyId) {
        // 筛选付款数据
        PayApplyCreateBillDto payApplyDto = new PayApplyCreateBillDto();
        // 待审核
        payApplyDto.setValidStatus(0);
        // 未制单
        payApplyDto.setIsBill(0);
        // 未付款
        payApplyDto.setPayStatus(0);
        // 2024-01-01 00:00:00
        payApplyDto.setBeginAddTime(1704038400000L);
        // 公司id
        payApplyDto.setCompanyId(1);
        if (Objects.nonNull(payApplyId)){
            payApplyDto.setPayApplyId(payApplyId);
        }
        List<PayApplyCreateBillDto> payApplyByDto = payApplyApiService.getPayApplyByDto(payApplyDto);
        return payApplyByDto;
    }

    /**
     * 判断是否开启
     * @return
     */
    private Boolean isOpen(){
        AutoPayConfigDto autoPayConfigDto = payApplyApiService.paymentAllocationInfo();
        if (Objects.isNull(autoPayConfigDto) || !autoPayConfigDto.getEnableAutoPay()){
            log.info("自动制单未开启");
            return Boolean.FALSE;
        }
        return Boolean.TRUE;
    }


}
