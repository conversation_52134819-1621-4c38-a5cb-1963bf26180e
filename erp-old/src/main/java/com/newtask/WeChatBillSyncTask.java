package com.newtask;

import com.vedeng.billsync.task.service.BankBillDataSyncService;
import com.vedeng.billsync.task.service.WeChatBillSyncTaskService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 定时任务同步微信账单
 * @Date 2020/12/16 11:11
 */
@Component
@JobHandler(value = "WeChatBillSyncTask")
public class WeChatBillSyncTask extends AbstractJobHandler {

    @Autowired
    private WeChatBillSyncTaskService weChatBillSyncTaskService;

    @Autowired
    private BankBillDataSyncService bankBillDataSyncService;


    @Override
    public ReturnT<String> doExecute(String billDate) {
        XxlJobLogger.log("XXL-JOB, WeChatBillSyncTask. 参数: " + billDate);
        ResultInfo resultInfo;
        try {
            if (StringUtils.isNotBlank(billDate)) {
                if ( ! billDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
                    XxlJobLogger.log("XXL-JOB, WeChatBillSyncTask. 请输入日期格式: YYYY-MM-DD");
                    return FAIL;
                }
                resultInfo = weChatBillSyncTaskService.syncWeChatBillList(billDate);
                // add by Randy.Xu 2020/12/22 10:01 .Desc: . begin
                Date queryDate = new Date(DateUtil.convertLong(billDate, DateUtil.DATE_FORMAT));
                bankBillDataSyncService.syncBillDate2bankBill(queryDate,"1",5);
                // add by Randy.Xu 2020/12/22 10:01 .Desc: . end

            } else {
                billDate = DateUtil.DateToString(DateUtil.getPreviousDayByDateTime(new Date()), DateUtil.DATE_FORMAT);
                resultInfo = weChatBillSyncTaskService.syncWeChatBillList(billDate);
                // add by Randy.Xu 2020/12/22 10:01 .Desc: . begin
                Date queryDate = new Date(DateUtil.convertLong(billDate, DateUtil.DATE_FORMAT));
                bankBillDataSyncService.syncBillDate2bankBill(queryDate,"1",5);
                // add by Randy.Xu 2020/12/22 10:01 .Desc: . end

            }
            if (resultInfo != null) {
                XxlJobLogger.log("XXL-JOB, WeChatBillSyncTask. 任务状态: " + resultInfo.getMessage());
                if (resultInfo.getCode() == 0) {
                    return SUCCESS;
                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("同步微信账单失败！Exception:" + e);
            return FAIL;
        }
        return FAIL;
    }
}
