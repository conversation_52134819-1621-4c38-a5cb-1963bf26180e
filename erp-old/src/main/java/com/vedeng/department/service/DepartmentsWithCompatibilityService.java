package com.vedeng.department.service;

import com.vedeng.authorization.model.User;

import java.util.Collection;

/**
 * <AUTHOR> [<EMAIL>]
 */
public interface DepartmentsWithCompatibilityService {

    /**
     * 获取医院科室列表信息(兼容性考略）
     * <p>
     * Note:兼容前台科室信息查询（ERP_LV_2020_86科室信息从spu移至三级商品分类下）
     *
     * @param thirdLevelCategoryId 三级商品分类id
     * @return
     * @since ERP_LV_2020_86
     */
    void saveDepartment(Integer thirdLevelCategoryId, Collection<Integer> departmentIdsToSave, User operator);

    /**
     * 同步分类下的科室信息到与分类关联的spu
     *
     * @param spuId
     * @since ERP_LV_2020_86
     */
    void syncDepartment2Spu(Integer spuId);
}
