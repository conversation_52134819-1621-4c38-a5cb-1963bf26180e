package com.vedeng.erp.buyorder.domain.entity;

import java.io.Serializable;
import java.util.Date;

/**
 * T_GE_AUTHORIZATION_LOG
 * <AUTHOR>
public class GeActionLog implements Serializable {
    /**
     * 日志记录id
     */
    private Integer authorizationLogId;

    /**
     * 关联主表字段ID
     */
    private Integer relatedId;

    /**
     * 操作事项
     */
    private String operation;

    /**
     * 日志内容
     */
    private String content;

    /**
     * 处理人id
     */
    private Integer creator;

    /**
     * 用户姓名中文 T_USER_DETAIL
     */
    private String creatorName;

    /**
     * 添加时间
     */
    private Date addTime;

    /**
     * 关联表类型1GE商机2GE授权书
     */
    private Integer relatedType;

    /**
     * 关联详细信息（JSON）
     */
    private String relatedBody;

    private static final long serialVersionUID = 1L;

    public Integer getAuthorizationLogId() {
        return authorizationLogId;
    }

    public void setAuthorizationLogId(Integer authorizationLogId) {
        this.authorizationLogId = authorizationLogId;
    }

    public Integer getRelatedId() {
        return relatedId;
    }

    public void setRelatedId(Integer relatedId) {
        this.relatedId = relatedId;
    }

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Integer getRelatedType() {
        return relatedType;
    }

    public void setRelatedType(Integer relatedType) {
        this.relatedType = relatedType;
    }

    public String getRelatedBody() {
        return relatedBody;
    }

    public void setRelatedBody(String relatedBody) {
        this.relatedBody = relatedBody;
    }
}