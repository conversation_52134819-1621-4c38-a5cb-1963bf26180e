package com.vedeng.docSync.service;

import com.alibaba.fastjson.JSON;
import com.vedeng.docSync.dao.DocSyncMapper;
import com.vedeng.docSync.model.pojo.DocBuzTagMaintainLogExtDo;
import com.vedeng.docSync.model.pojo.DocRBuzTagJFileExtDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.context.ContextLoader;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/4 12:56
 */
@Slf4j
public abstract class BaseService {


    @Resource
    protected DocSyncMapper docSyncMapper;

    @Value("${erp_url}")
    protected String erpUrl;

    private String tempPath;

    protected String getTempPath() {
        if (tempPath == null) {
            tempPath = ContextLoader.getCurrentWebApplicationContext().getServletContext().getRealPath("/upload/attachment");
        }
        return tempPath;
    }

    protected void clearOldData(List<DocRBuzTagJFileExtDo> docBuzTagFileExtDoList) {
        try {
            //清除旧数据
            if (CollectionUtils.isNotEmpty(docBuzTagFileExtDoList)) {
                //删除
                List<Integer> ids = docBuzTagFileExtDoList.stream().map(DocRBuzTagJFileExtDo::getId).collect(Collectors.toList());
                docSyncMapper.batchRemoveOldBuzTagFile(ids);

                log.info("batch delete docGoodsFileR , source :{}", docBuzTagFileExtDoList);
                docBuzTagFileExtDoList.forEach(e -> {
                    e.setOperator(1);
                    e.setOperateTime(System.currentTimeMillis());
                    e.setOperateType(2);
                });
                //添加操作记录
                docSyncMapper.batchSaveActionLog(docBuzTagFileExtDoList);
            }
        } catch (Exception e) {
            log.error("同步erp到资料库，删除旧数据失败， buzModel :{}, 失败原因：{}", JSON.toJSONString(docBuzTagFileExtDoList), e);
            throw new RuntimeException("同步erp到资料库，删除旧数据失败， buzModel :"+JSON.toJSONString(docBuzTagFileExtDoList)+", 失败原因："+e);
        }

    }


    /**
     * 保存操作日志
     *
     * @param buzId   业务id
     * @param buzType 业务类型
     * @param buzTag  标签id
     * @param rId     关系id
     * @param creator 操作人
     */
    protected void saveActionLog(Integer buzId, Integer buzType, Integer buzTag, Integer rId, Integer creator) {
        DocBuzTagMaintainLogExtDo docBuzTagMaintainLogExtDo = new DocBuzTagMaintainLogExtDo();
        docBuzTagMaintainLogExtDo.setBuzType(buzType);
        docBuzTagMaintainLogExtDo.setBuzId(buzId);
        docBuzTagMaintainLogExtDo.setBuzTag(buzTag);
        docBuzTagMaintainLogExtDo.setOperateType(1);
        docBuzTagMaintainLogExtDo.setBuzTagJFileId(rId);
        docBuzTagMaintainLogExtDo.setOperator(creator);
        docBuzTagMaintainLogExtDo.setOperateTime(System.currentTimeMillis());
        docSyncMapper.saveActionLog(docBuzTagMaintainLogExtDo);
    }

}
