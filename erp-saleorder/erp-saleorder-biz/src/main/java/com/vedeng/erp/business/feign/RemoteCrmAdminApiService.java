package com.vedeng.erp.business.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailQueryDTO;
import com.vedeng.crm.admin.api.dto.tuoke.BusinessCluesDetailResponse;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
@FeignApi(serverName = ServerConstants.CRM_ADMIN_SERVER)
public interface RemoteCrmAdminApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /api/tuoke/businessClues/detail")
    RestfulResult<BusinessCluesDetailResponse> getBusinessCluesDetails(@RequestBody BusinessCluesDetailQueryDTO var1);


}
