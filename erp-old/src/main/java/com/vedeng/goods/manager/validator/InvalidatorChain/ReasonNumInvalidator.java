package com.vedeng.goods.manager.validator.InvalidatorChain;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;


/**
 * .(13)	禁用原因是必填项，且最多只允许填写32字符
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:06.
 * @author: <PERSON><PERSON>.
 */
@Service
public class ReasonNumInvalidator extends GoodsInvalidatorChain {
    @Override
    public void doInvalid() {
        String reason = getReason();
        if(StringUtils.isBlank(reason)){
            throw new InvalidatorChainException(ReasonNumNullInvalidatorError.getCode(),ReasonNumNullInvalidatorError.getMessage());
        }
        if(reason.length()>64){
            throw new InvalidatorChainException(ReasonNumLongInvalidatorError.getCode(),ReasonNumLongInvalidatorError.getMessage());
        }
    }
}
