function searchSupplier() {
    checkLogin();
    var searchTraderName = $("#searchTraderName").val() == undefined ? "" : $("#searchTraderName").val();
    if ($("#searchTraderName").val() == '') {
        $("#searchTraderName").parent("div").siblings("div").removeClass("none").html("查询条件不允许为空");
        $("#searchTraderName").addClass("errorbor");
        return false;
    } else {
        $("#searchTraderName").parent("div").siblings("div").addClass("none");
        $("#searchTraderName").removeClass("errorbor");
    }

    var searchUrl = page_url + "/order/buyorder/getSupplierByName.do?supplierName=" + encodeURI(searchTraderName);
    $("#popSupplier").attr('layerParams', '{"width":"800px","height":"500px","title":"搜索供应商","link":"' + searchUrl + '"}');
    $("#popSupplier").click();

}

function research() {
    checkLogin();
    $("#searchTraderName").val("");
    $("#searchTraderName").show();
    $("#name").addClass("none");
    $("#errorMes").removeClass("none");
    $("#research").addClass("none");
    warnTips("traderNameErr","");
}
