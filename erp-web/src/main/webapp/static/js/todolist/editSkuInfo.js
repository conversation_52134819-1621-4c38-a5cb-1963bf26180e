$(function () {
    $('.sku-unit-select').chosen({
        search_contains: true,
        no_results_text: "没有找到结果！"
    });

    if ($('#isNeedReport').val() == 0){
        $('#isAuthorizedItem').hide();
        $('#authorizationItem').hide();
    }

    if ($('#isAuthorized').val() == 0){
        $('#authorizationItem').hide();
    }
    let isEnableValidityPeriod = $('#isEnableValidityPeriod').val();
    console.log("isEnableValidityPeriod:"+isEnableValidityPeriod);
    if(isEnableValidityPeriod === "1"){
        $('#isEnableValidityPeriodDiv').show();
    }else{
        $('#isEnableValidityPeriodDiv').hide();
        if(!($("#effectiveDays")!=null && $("#effectiveDays") >0)){
            $("#effectiveDays").val(0);
        }
        if(!($("#nearTermWarnDays")!=null && $("#nearTermWarnDays") >0)){
            $("#nearTermWarnDays").val(0);
            $("#nearTermWarnDaysSpan").html('0天');
        }
        if(!($("#overNearTermWarnDays")!=null && $("#overNearTermWarnDays") >0)){
            $("#overNearTermWarnDays").val(0);
            $("#overNearTermWarnDaysSpan").html('0天');
        }
    }

    // //是否启用效期
    $('input[name=isEnableValidityPeriod]').change(function () {
        if (this.value == '1') {
            $('#isEnableValidityPeriodDiv').show();
        } else {
            $('#isEnableValidityPeriodDiv').hide();
            if(!($("#effectiveDays")!=null && $("#effectiveDays") >0)){
                $("#effectiveDays").val(0);
            }
            if(!($("#nearTermWarnDays")!=null && $("#nearTermWarnDays") >0)){
                $("#nearTermWarnDays").val(0);
                $("#nearTermWarnDaysSpan").html('0天');
            }
            if(!($("#overNearTermWarnDays")!=null && $("#overNearTermWarnDays") >0)){
                $("#overNearTermWarnDays").val(0);
                $("#overNearTermWarnDaysSpan").html('0天');
            }
        }
    });

    //日期空间初始化
    var Pikadays = [];
    $('.J-date').each(function (i) {
        var $this = $(this);
        Pikadays[i] = new Pikaday({
            format: 'yyyy-mm-dd',
            field: $(this)[0],
            firstDay: 1,
            yearRange: [1900, 2099],
            onSelect: function (date) {

            }
        });
    })




    $("#effectiveDays").keyup(function () {
        var effectiveDays = $("#effectiveDays").val();
        $.getJSON("/goods/vgoods/static/generateWarnDays.do?day=" + effectiveDays,
            function (d) {
                if (d.code == 0) {
                    $("#nearTermWarnDays").val(d.data.nearTermWarnDays);
                    $("#overNearTermWarnDays").val(d.data.overNearTermWarnDays);
                    $("#nearTermWarnDaysSpan").html(d.data.nearTermWarnDays+'天');
                    $("#overNearTermWarnDaysSpan").html(d.data.overNearTermWarnDays+'天');
                }else{
                    //  layer.alert("计算效期日期异常")
                }
            })
    })

    $("input[name=effectiveDays]").bind('input propertychange', function () {
        var days = parseInt($("input[name=effectiveDays]").val());
        if (days >= 1095) {
            $("input[name=nearTermWarnDays]").val(365);
            $("input[name=overNearTermWarnDays]").val(120);
        } else if (days < 1095 && days >= 365) {
            $("input[name=nearTermWarnDays]").val(180);
            $("input[name=overNearTermWarnDays]").val(90);
        } else if (days < 365 && days >= 180) {
            $("input[name=nearTermWarnDays]").val(120);
            $("input[name=overNearTermWarnDays]").val(60);
        } else if (days < 180 && days >= 90) {
            $("input[name=nearTermWarnDays]").val(60);
            $("input[name=overNearTermWarnDays]").val(30);
        } else if (days < 90 && days >= 30) {
            $("input[name=nearTermWarnDays]").val(20);
            $("input[name=overNearTermWarnDays]").val(10);
        } else if (days < 30 && days >= 20) {
            $("input[name=nearTermWarnDays]").val(10);
            $("input[name=overNearTermWarnDays]").val(5);
        } else {
            $("input[name=nearTermWarnDays]").val(7);
            $("input[name=overNearTermWarnDays]").val(5);
        }
    });



    $("#regularMaintainType").change(function () {
        if (this.value == '1' || this.value == '2') {
            $('#regularMaintainReasonDiv').show();
            $('#regularMaintainReason').removeAttr("disabled")
        } else {
            $('#regularMaintainReasonDiv').hide();
            $('#regularMaintainReason').attr("disabled",true)
        }
    });

    var resetShouquanNames = function () {
        $('.J-shouquan-item').each(function(i){
            $(this).find('.J-shouquan-input-1').attr('name', 'skuAuthorizationDtos[' + i + '].regionsStr');
            $(this).find('.J-shouquan-input-2').attr('name', 'skuAuthorizationDtos[' + i + '].snowFlakeId');
            $(this).find('.J-shouquan-input-3').attr('name', 'skuAuthorizationDtos[' + i + '].terminalTypesStr');
        });

        if($('.J-shouquan-item').length > 1){
            $('.J-shouquan-item .J-sort-del').show();
        }else{
            $('.J-shouquan-item .J-sort-del').hide();
        }
    };

    var isSubProduct=$('input[name="isSubcontractProduction"]:checked').val();
    if(isSubProduct=='0'){
        $(".c-title").each(function () {
            var title=$(this).data("a");
            $(this).html(title)
        })
    }else if(isSubProduct=='1'){
        $(".c-title").each(function () {
            var title=$(this).data("b");
            $(this).html(title)
        })
    }

    resetShouquanNames();

    //筛选多选
    var getMultiData = function ($item) {
        var data = [];
        $item.siblings('select').find('option').each(function () {
            data.push({
                label: $.trim($(this).html()),
                value: $(this).val()
            })
        });
        return data;
    };


    //国际分类选择显示隐藏
    $('.J-inter-type input').change(function () {
        var val = $('.J-inter-type input:checked').val();
        if (val == 2) {
            $('.J-inter-old').removeClass('ignore').show();
            $('.J-inter-new').addClass('ignore').hide();
        } else if (val == 1) {
            $('.J-inter-new').removeClass('ignore').show();
            $('.J-inter-old').addClass('ignore').hide();
        }
    })

    $('.J-inter-type input').trigger('change');

    $('.J-muiti-select').each(function () {
        var data = getMultiData($(this));

        $(this).siblings('select,.select').remove();

        //初始化品牌信息
        new SuggestSelect({
            placeholder: '请选择',
            wrap: $(this),
            data: data,
            multi: true,
            multiAll: true,
            input: $(this).siblings('.J-value'),
        })
    });

    $('.authorization_add').click(function () {
        $(this).parent().before($('.J-shouquan-tmpl').html());

        $('.J-shouquan-item').last().find('.J-muiti-select').each(function () {
            var data = getMultiData($(this));

            $(this).siblings('select,.select').remove();

            //初始化品牌信息
            new SuggestSelect({
                placeholder: '请选择',
                wrap: $(this),
                data: data,
                multi: true,
                multiAll: true,
                input: $(this).siblings('.J-value'),
            })
        });

        resetShouquanNames();
    });


    //旧国标国搜索下拉框
    new SuggestSelect({
        placeholder: '请选择',
        wrap: '.J-old-wrap',
        data: JSON.parse($('.J-old-data').html()),
        input: '.J-old-value'
    })

    new SuggestSelect({
        placeholder: '请选择',
        wrap: '.J-new-wrap',
        data: JSON.parse($('.J-new-data').html()),
        input: '.J-new-value'
    })

    //选择框实例化
    Select.use('.J-select');

    $(document).on('click', '.authorization-del', function(){
        $(this).parent().remove();
        resetShouquanNames();
    })

    //校验
    $.validator.addMethod("requiredCheck", function (value, element, params) {
        var hasVal = false;
        $(params).each(function () {
            if ($(this)[0].checked) {
                hasVal = true;
            }
        })
        return hasVal;
    });

    $.validator.addMethod('decimal2', function (value, element, params) {
        return !value || /^[0-9]+(\.[0-9]{1,2})?$/.test(value);
    }, '数字保留小数点后两位');

    $.validator.addMethod('decimal3', function (value, element, params) {
        return !value || /^[0-9]+(\.[0-9]{1,3})?$/.test(value);
    }, '数字保留小数点后三位');

    $.validator.addMethod('decimal1', function (value, element, params) {
        return !value || /^[0-9]+(\.[0-9]{1})?$/.test(value);
    }, '数字保留小数点后一位');

    $.validator.addMethod('number', function (value, element, params) {
        return !value || /^\d+(\.\d+)?$/.test(value);
    }, '必须是数字');

    $.validator.addMethod('boxMidRule', function (value, element, params) {
        //debugger;
        var boxPackageNum = $("input[name=boxPackageNum]").val();
        var midPackageNum = $("input[name=midPackageNum]").val();
        if ('' == boxPackageNum || '' == midPackageNum) {
            return true;
        }
        if (parseInt(midPackageNum) > parseInt(boxPackageNum)) {
            return false;
        }
        return true;
    }, '箱包装数量应该≥中包装数量');

    $.validator.addMethod('lwhBt', function (value, element, params) {
        //debugger;
        var l = $("input[name=packageLength]").val();
        var w = $("input[name=packageWidth]").val();
        var h = $("input[name=packageHeight]").val();
        if ('' == l || '' == w || '' == h) {
            return false;
        }
        return true;
    }, '请填写箱包装体积');


    $.validator.addMethod('needOne', function (value, element, params) {
        var flag = false;
        $('.J-tech-params .J-sort-item').each(function () {
            var $name = $(this).find('.J-sort-name');
            var $value = $(this).find('.J-sort-value');

            if ($.trim($name.val()) && $.trim($value.val())) {
                flag = true;
            }
        })

        return flag;
    }, '请填写技术参数');

    $.validator.addMethod("larger", function (value, element, params) {
        return parseFloat(value) > params;
    });

    $.validator.addMethod("registrationNumber", function (value, element, params) {
        return true;
    });

    $.validator.addMethod("rnSpace", function (value, element, params) {
        var index=value.indexOf(" ");
        return index>0?false:true;
    });


    var canSubmit = true;

    $('.J-form').validate({
        errorWrap: true,
        ignore: '.ignore input',
        rules: {
            effectiveDays: {
                required: function () {
                    return $('input:radio[name=isEnableValidityPeriod]:checked').length == 1 && $('input:radio[name=isEnableValidityPeriod]:checked').val() == '1';
                },
                maxlength: 10,
                digits: true
            },
            storageConditionTemperature: {
                required: true
            },
            storageConditionTemperatureLowerValue: {
                required: true,
                range: [-100,100],
                ltTem: true,
            },
            storageConditionTemperatureUpperValue: {
                required: true,
                range:[-100,100],
                gtTem: true,
            },
            storageConditionHumidityLowerValue: {
                required: true,
                digits: true,
                range: [0,100],
                ltHum: true,
            },
            storageConditionHumidityUpperValue: {
                required: true,
                digits: true,
                range: [0,100],
                gtHum: true,
            },
            storageConditionOthersArray:{
                required: true,
            },
            medicalInstrumentCatalogIncluded:{
                required: true,
            },
            hasRegistrationCert:{
                required: true,
            },
            firstEngageId: {
                required: true
            },
            spuName: {
                required: true,
                maxlength: 128
            },
            specsModel:{
                required: true,
                maxlength: 512,
            },
            registrationNumber: {
                required: true,
                maxlength: 64,
                registrationNumber: true,
                rnSpace:true
            },
            manageCategoryLevel: {
                required: true
            },
            productCompanyChineseName: {
                required: true,
                maxlength: 64
            },
            productChineseName: {
                required: true,
            },
            issuingDateStr: {
                required: true
            },
            model: {
                required: true
            },
            isSubcontractProduction: {
                required: true
            },
            standardCategoryType: {
                required: true
            },
            newStandardCategoryId: {
                required: true
            },
            oldStandardCategoryId: {
                required: true
            },
            upload0: {
                required: true
            },
            productionEnterpriseName:{
                required: true,
                maxlength: 128
            },
            productCompanyLicence:{
                required: true,
                maxlength: 128
            }

        },
        messages: {
            storageConditionTemperature: {
                required: '请选择存储条件(温度)'
            },
            storageConditionTemperatureLowerValue: {
                required: '请填写温度范围',
                range: '请正确填写，温度范围-100-100',
            },
            storageConditionTemperatureUpperValue: {
                required: '请填写温度范围',
                range: '请正确填写，温度范围-100-100',
            },
            storageConditionHumidityLowerValue:{
                required: '请填写湿度范围',
                digits: '请输入正确填写，湿度范围0-100（正整数）',
                range: '请输入正确填写，湿度范围0-100（正整数）',
            },
            storageConditionHumidityUpperValue:{
                required: '请填写湿度范围',
                digits: '请输入正确填写，湿度范围0-100（正整数）',
                range: '请输入正确填写，湿度范围0-100（正整数）',
            },
            storageConditionOthersArray:{
                required: '请选择存储条件其他',
            },
            effectiveDays: {
                required: '请填写有效期',
                digits: '请输入整数'
            },
            medicalInstrumentCatalogIncluded:{
                required: '请选择是否在《医疗器械分类目录》'
            },
            hasRegistrationCert:{
                required: '请选择是否有注册证/备案凭证',
            },
            firstEngageId: {
                required: '请选择注册证/备案信息'
            },
            spuName: {
                required: '请输入产品名称（注册证/备案凭证）'
            },
            specsModel:{
                required: '请输入规格、型号（注册证/备案凭证）',
                maxlength: '规格、型号（注册证/备案凭证)超过512个字符',
            },
            registrationNumber: {
                required: '请填写注册证号/备案凭证号',
                rnSpace: "注册证号/备案凭证号不能有空格"
            },
            manageCategoryLevel: {
                required: '请选择管理类别'
            },
            productCompanyChineseName: {
                required: '请填写生产企业'
            },
            productChineseName: {
                required: '请输入产品名称（注册证/备案凭证）'
            },
            issuingDateStr: {
                required: '请选择批准日期'
            },
            model: {
                required: '请输入规格、型号（注册证/备案凭证）'
            },
            isSubcontractProduction: {
                required: "请选择是否委托生产"
            },
            standardCategoryType: {
                required: '请选择国标类型'
            },
            newStandardCategoryId: {
                required: '请选择新国际分类'
            },
            oldStandardCategoryId: {
                required: '请选择旧国际分类'
            },
            upload0: {
                required: '请上传注册证附件/备案凭证附件'
            },
            productionEnterpriseName:{
                required: '请输入生产企业名称'
            },
            productCompanyLicence:{
                required: '请输入生产企业生产许可证或备案凭证编号'
            }
        },
        groups: {
            storageConditionHumidity: "storageConditionHumidityLowerValue storageConditionHumidityUpperValue",
            storageConditionTemperature: "storageConditionTemperatureLowerValue  storageConditionTemperatureUpperValue",
            technicalParameter: "paramsName1 paramsValue1",
        },
        submitHandler: function (form) {
            if (canSubmit) {
                canSubmit = false;
                window.localStorage.setItem('addSkuSuccess', 'true');
                $(".defaultZero").each(function () {
                    if ($(this).val() == '') {
                        $(this).val(0);
                    }
                })
                // if (checkMinOrder() && checkSkuAuthorization()) {
                form.submit();
                // } else {
                canSubmit = true;
                // }
            }
        },
        invalidHandler: function(form, validator) {
            //校验不通过时，展开折叠的面板
            var errorMap = validator.errorMap;

            $.map(errorMap, function (value, key) {
                var targetNode =$("[name="+ key +"]")
                while (targetNode.length > 0) {
                    if(targetNode.hasClass('form-block')){
                        break
                    }
                    targetNode = targetNode.parent();
                }

                var toggle = targetNode.find('.J-toggle-show');
                if (toggle.length > 0 && toggle.find('.J-less').css('display')==='none') {
                    toggle.trigger('click');
                }

            });

            return false;
        }
    });

    window.localStorage.setItem('addSkuSuccess', '')

    $('.J-tech-params').on('blur', '.J-sort-name,.J-sort-value', function () {
        var $label = $('label[for=paramsValid]');

        if ($label.length && $label.css('display') !== 'none') {
            $('[name=paramsValid]').valid();
        }
    })

    //选择初始化
    Select.use('select');
    //debugger;
    $('#unitHidden').find('.select').hide();
    //默认带入商品名
    $('.J-model').change(function () {
        if (!$.trim($('.J-prod-name').val())) {
            $('.J-prod-name').val($.trim($('.J-spu-name').data('html')) + $(this).val());
        }
    })

    //上传组件初始化
    timeout = null;
    var attachmentFunction = [975];
    var attachmentsName = ['zczAttachments'];
    $('.J-upload').each(function (i) {
        var _this = this;
        new Upload({
            limit: 50,
            url: GLOBAL.IMGUPLOADURL,
            wrapper: $(this),
            uploadName: 'upload' + i,
            list: JSON.parse($(this).siblings('.J-upload-data').val() || '[]'),
            onchange: function () {
                $(_this).find('.J-upload-item').each(function (ii) {
                    var data = $(this).data('item');
                    $(this).find('.J-item-name').remove();
                    $(this).append('<input type="hidden" class="J-item-name" name="registration.' + attachmentsName[i] + '[' + ii + '].attachmentFunction" value="' + attachmentFunction[i] + '">');
                    $(this).append('<input type="hidden" class="J-item-name" name="registration.' + attachmentsName[i] + '[' + ii + '].uri" value="' + data.filePath +'">');
                    $(this).append('<input type="hidden" class="J-item-name" name="registration.' + attachmentsName[i] + '[' + ii + '].domain" value="' + data.domain + '">');
                })
                if (i == 0 || i == 1) {
                    $(_this).find('[name^=upload]').valid();
                }
            },
            filters: {
                mime_types: [
                    { title: "Image files", extensions: "jpg,jpeg,png" }
                ],
                max_file_size: '5MB'
            },
            onError: function (error) {
                var errorMsg = {
                    TYPE: '上传图片格式为：JPG、PNG、JEPG格式',
                    SIZE: '图片大小不超过5M'
                }
                if (error) {
                    var $error = $(_this).siblings('.J-upload-error');
                    $error.show().find('label').html(errorMsg[error]).show();
                    timeout && clearTimeout(timeout);
                    timeout = setTimeout(function () {
                        $error.hide();
                    }, 3000)
                }
            }
        });
    })


    //排序序号
    var setNum = function ($wrap) {
        $('.J-sort-num', $wrap).each(function (i) {
            $(this).val(i + 1);
        })
    };

    $('.J-sort-wrap').each(function () {
        setNum($(this));
    })

    var sortInput = function (params) {
        var $wrap = $(params.wrap);

        var flag = true;
        var checkVal = function (el) {
            var $this = $(el);

            if (flag) {
                setTimeout(function () {
                    var val = $.trim($this.val());
                    if (val && !(/^\d*$/.test(val))) {
                        val = val.replace(/[^\d]+/g, '');
                        $this.val(val);
                    }
                }, 10)
            }
        };

        $wrap.on('compositionstart', '.J-sort-num', function () {
            flag = false;
        })

        $wrap.on('compositionend', '.J-sort-num', function () {
            flag = true;
        })

        $wrap.on('keyup', '.J-sort-num', function () {
            checkVal(this);
        });

        $wrap.on('change', '.J-sort-num', function () {
            checkVal(this);
            var _this = this;

            setTimeout(function () {
                var $items = $('.J-sort-num', $wrap);
                var len = $items.length;
                var val = parseInt($(_this).val());

                if (val === 0 || val) {
                    val = val > len ? len : (val < 1 ? 1 : val);

                    var $parent = $(_this).parents('.J-sort-item:first');
                    var index = $parent.index();

                    setTimeout(function () {
                        var $sort = $parent.clone(true);

                        if (val <= index) {
                            $('.J-sort-item', $wrap).eq(val - 1).before($sort);
                        } else {
                            $('.J-sort-item', $wrap).eq(val - 1).after($sort);
                        }

                        $parent.remove();
                        setNum($wrap);
                    })
                } else {
                    setNum($wrap);
                }

            }, 110);
        });

    };

    $('.J-sort-wrap').each(function () {
        sortInput({
            wrap: $(this)
        })
    })

    //检查添加或者删除是否显示
    var checkItemNum = function ($wrap) {
        var len = $('.J-sort-item', $wrap).length;
        var min = 1;
        var max = 30;

        if (len >= max) {
            $('.J-sort-add-option', $wrap).hide();
        } else {
            $('.J-sort-add-option', $wrap).show();
        }

        if (len <= min) {
            $('.J-sort-del', $wrap).hide();
        } else {
            $('.J-sort-del', $wrap).show();
        }
    };

    //获取name
    var paramsNames = [];

    $('.J-sort-wrap').each(function () {
        var $name = $(this).find('.J-sort-name').eq(0);
        var $value = $(this).find('.J-sort-value').eq(0);

        paramsNames.push({
            itemName: $name.attr('name'),
            itemValue: $value.attr('name')
        });

    })

    //sku类型为器械时才需要维护参数信息，避免js堆栈异常
    if($('#ex1').length){
        new Sortable(ex1, {
            animation: 150,
            ghostClass: "sortable-drag",  // drop placeholder的css类名
            chosenClass: "sortable-drag",
            onEnd: function (evt) {
                //debugger;
                var $wrap = $(evt.item).parents('.J-sort-wrap:first');

                //$(this).parents('.J-sort-item:first').remove();
                checkItemNum($wrap);
                setNum($wrap);
            },
        });
    }


    //参数添加
    var paramsTmpl = template($('.J-sort-tmpl').html());

    var addParamsItem = function ($wrap, params) {
        var defaults = {
            name: '',
            value: '',
            itemName: '',
            itemValue: ''
        };
        var index = $wrap.data('index') || 0;
        var paramsName = paramsNames[index];

        var data = $.extend({}, defaults, params, paramsName);
        var $list = $('#ex1', $wrap);

        $list.append(paramsTmpl(data));

        setNum($wrap);
        checkItemNum($wrap);
    };

    $('.J-sort-add').click(function () {
        var $wrap = $(this).parents('.J-sort-wrap:first');
        addParamsItem($wrap)
    });



    //删除参数
    $(document).on('click', '.J-sort-del', function () {
        var $wrap = $(this).parents('.J-sort-wrap:first');

        $(this).parents('.J-sort-item:first').remove();
        checkItemNum($wrap);
        setNum($wrap);
    })

    $('.J-sort-wrap').each(function () {
        checkItemNum($(this));
    })

    //单位同步
    $('.J-sku-unit').change(function () {
        if ($(this).val()) {
            $('.J-sku-unit-value').html('（' + $.trim($(this).find('option:selected').html()) + '）');
        } else {
            $('.J-sku-unit-value').html('');
        }
    })

    $('.J-sku-unit').trigger('change');

    $('.sku-unit-select').change(function () {
        if ($(this).val()){
            debugger
            $('.sku-unit-value').html('（' + $.trim($(this).find('option:selected').html()) + '）');
        } else {
            $('.sku-unit-value').html('');
        }
    });

    $('.sku-unit-select').trigger('change');

    //增加关闭提示
    GLOBAL.addtip();

});

/**
 * 有无注册证切换开关
 */
function registrationCertSwitch() {
    var switchFlag=$("input[name='hasRegistrationCert']:checked").val()
    if(switchFlag==='1'){
        $('.J-first-block').css("display","block")
        $('input[name="firstEngageId"]').removeAttr("disabled")
    }else {
        $('.J-first-block').css("display","none")
        $('input[name="firstEngageId"]').attr("disabled",true)
    }

    registrationCertNameAndSpecSwitch(switchFlag)

    //是否展示"是否为大型医疗器械"选项
    largeMedicalInstrumentSwitch($('.J-prod-type:checked').val())
}

//页面加载阶段触发
registrationCertNameAndSpecSwitch($("input[name='hasRegistrationCert']:checked").val())
/**
 * 展示/隐藏产品名称和规格型号
 * @param on
 */
function registrationCertNameAndSpecSwitch(on) {
    if(on==='1'){
        $('.J-registration-specs').css('display','block')
        $('.J-registration-name').css('display','block')
        $('input[name=spuName]').removeAttr("disabled")
        $('input[name=specsModel]').removeAttr("disabled")
    }else {
        $('.J-registration-specs').css('display','none')
        $('.J-registration-name').css('display','none')
        $('input[name=spuName]').attr("disabled",true)
        $('input[name=specsModel]').attr("disabled",true)
    }
}
function largeMedicalInstrumentSwitch(firstLevelGoodsType) {
    var hasRegistrationCert = $("input[name='hasRegistrationCert']:checked").val()=='1'

    //当"商品类型"，决定二级商品类型（目前只有"大型医疗器械"，便于后期扩展）
    if( firstLevelGoodsType==316 && hasRegistrationCert) {
        $('.J-large-facility-block').css("display","block")
    }else {
        $('.J-large-facility-block').css("display","none")
        //当"商品类型"不为"设备'时，将二级商品类型（目前只有"大型医疗器械"，便于后期扩展）置为"否'
        $.each($('input[name=secondLevelSpuType]') , function (i, item) {
            if(item.value == 0){
                item.checked=true
            }
        })
    }
}

/**
 * 检测数组元素是否重复
 * @param arr
 * @returns {boolean}
 */
function isRepeat(arr) {
    var hash = {};
    for(var i in arr) {
        if(hash[arr[i]]) {
            return true;
        }
        hash[arr[i]] = true;
    }
    return false;
}


/**
 * 校验属性值
 */
function   validateFuc(){
    $("[flag]").each(function (i,e) {
        $("input[name='baseAttributeValueId["+i+"]']").rules('add', {
            required:true,
            messages:{
                required:'请选择'+$("input[name='baseAttributeValueId["+i+"]']").attr("content1")
            }
        });
    });
};


//展开收起
$('.J-toggle-show').click(function () {
    var $optionalWrap=$(this).parent().find('.J-optional-more')
    var isShow = $optionalWrap.hasClass('show');
    var $less = $(this).find('.J-less');
    var $more = $(this).find('.J-more');

    if (isShow) {
        $optionalWrap.removeClass('show').slideUp(200);
        $less.hide();
        $more.show();
    } else {
        $optionalWrap.addClass('show').slideDown(200);
        $less.show();
        $more.hide();
    }
});

function setSlideUpOnLoading() {
    if ($('.J-toggle-show').length > 0) {
        $('.J-toggle-show').each(function () {
            $(this).trigger('click');
        });
    }
}

setSlideUpOnLoading();

//
// var legacyPropertyNameMap =new Map;
// legacyPropertyNameMap.set('technicalParameter', ['paramsName1', 'paramsValue1']);


/**
 * 获取验证SKU字段规则
 */
function loadGoodsValidationRule() {
    var goodsLevelNo = parseInt($('input[name=goodsLevelNo]').val());

    if(isNaN(goodsLevelNo) || goodsLevelNo<=0) {
        return
    }

    $.ajax({
        url: page_url + '/goods/vgoods/goodsValidatedRule.do',
        data: {
            goodsDimension: 2,
            goodsLevelNo: goodsLevelNo
        },
        dataType: 'json',
        success: function (res) {
            if(res !== null && res.code===0){
                //动态生成页面校验规则
                var ruleInJson = res.data;

                var basicItems = ruleInJson.basicItems;


                var specialNames = ['storageConditionHumidityLowerValue', 'storageConditionHumidityUpperValue'];

                next:
                    for (var i=0; i < basicItems.length; i++) {
                        var elem = basicItems[i];
                        var required = elem.required;

                        //商品属性
                        if (required && elem.propertyName === 'baseAttributeValueId'){
                            if ($('#attrRequiredIcon').length > 0) {
                                $('#attrRequiredIcon').css('display','block');
                                validateFuc();
                            }
                        }


                        if (!required) {
                            var propertyName = elem.propertyName;
                            if (propertyName == null || propertyName === '') {
                                continue
                            }

                            // var legacyPropertyName =legacyPropertyNameMap.get(propertyName);
                            // if (legacyPropertyName !== undefined && legacyPropertyName != '') {
                            //     debugger
                            //     propertyName =legacyPropertyName;
                            // }

                            var target = $("[name=" + propertyName + "]")
                            if (target.length == 0) {
                                console.log("not match property name:"+ propertyName);
                                continue
                            }

                            if(propertyName === 'storageConditionTemperature') {
                                $("[name=storageConditionTemperatureLowerValue]").rules('remove');
                                $("[name=storageConditionTemperatureUpperValue]").rules('remove');
                            }else if (propertyName === 'technicalParameter') {
                                $(["name=paramsName1"]).rules("remove", 'required');
                                $(["name=paramsValue1"]).rules("remove", 'required');
                            }

                            if(specialNames.indexOf(propertyName) >= 0) {
                                target.rules('remove');
                            }
                            var removeFlag = target.rules('remove', 'required');
                            if (removeFlag.require) {
                                continue next;
                            }

                            var parentNode = target.parent();
                            while (parentNode.length > 0) {
                                if (parentNode.hasClass('form-fields')) {
                                    break
                                }
                                parentNode=parentNode.parent()
                            }

                            if (!parentNode.length) {
                                continue
                            }

                            var targetSpan = parentNode.prev().find('.must')
                            if( targetSpan.length > 0) {
                                targetSpan.hide();
                            }

                        }
                    }

            } else {
                artDialog.alert('加载商品校验规则失败！');
            }
        },
        error:function(res){
            artDialog.alert("系统异常,请联系管理员", '系统错误');
            console.log("error message:" + res)
        }
    });
}

loadGoodsValidationRule();

//关闭当前页面
$('.close-spu-edit').click(function () {
    if(parentWindow.find('.title .active .glyphicon').length > 0){
        var closeTab = parentWindow.find('.title .active .glyphicon').get(0);
        $(closeTab).trigger('click');
    }
});

// 点击名称事件
$("#manufacturerId").change(function(){
    var manufacturerId = $("#manufacturerId").val();
    $.ajax({
        url: page_url + '/todolist/manufacturer/getManufacturerDetail.do',
        type: 'GET',
        dataType: 'json',
        data:{"manufacturerId":manufacturerId},
        success: function (result) {
            if (result.code === 0) {
                var data = result.data.productCompanyLicence;
                if (data != null){
                    $("#productCompanyLicence").attr("value",data);
                }else {
                    $("#productCompanyLicence").attr("value",'');
                }

            }
        }, error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }
        }
    })
});
