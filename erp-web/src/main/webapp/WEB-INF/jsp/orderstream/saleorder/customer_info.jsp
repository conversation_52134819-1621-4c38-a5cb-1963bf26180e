<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增订单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="<%=basePath%>static/css/select2.css?rnd=${resourceVersionKey}" />
<script type="text/javascript" src='<%= basePath %>static/js/select2.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src='<%= basePath %>static/js/select2_locale_zh-CN.js?rnd=${resourceVersionKey}'></script>
<script type="text/javascript" src="<%= basePath %>static/js/orderstream/saleorder/customer_info.js?rnd=${resourceVersionKey}"></script>
<%--<script type="text/javascript" src="<%= basePath %>static/js/orderstream/saleorder/add.js?rnd=${resourceVersionKey}"></script>--%>
<script>
    function setContentSelect(content,contact){
        $(contact).select2().val(content).trigger("change");
        $(contact).select2()
    }
</script>
<style>
    .select2-container .select2-choice .select2-arrow {
        background: #fff;
    }
</style>
<div class="content mt10 ">
    <div class="parts">
        <div class="title-container title-container-blue">
            <div class="table-title nobor">客户信息</div>
        </div>
        <div class="formpublic formpublic1" id="updateTerminalInfo" >
            <div>
                <form method="post" id="search"
                      action="${pageContext.request.contextPath}/orderstream/saleorder/addOrder.do?indexId=${indexId}">
                    <div>
                        <!-- 初始模式 -->
                        <ul class="searchTable visible">
                            <li>
                                <div class="infor_name  infor_name6 ">
                                    <span>*</span> <label>客户名称</label>
                                </div>

                                <div class="f_left table-larger" style="width: 80%">
                                    <div class="mb10">
                                        <input type="text" class="input-larger mr5 searchTraderName"
                                               name="searchTraderName" id="searchTraderName"
                                               value="${traderCustomerVo.searchTraderName}">
                                        <span
                                                class="bt-bg-style bt-small bg-light-blue" onclick="search();"
                                                id="searchError">搜索</span>

                                    </div>
                                </div>
                                <div>
                                    <table
                                            class="table table-bordered table-striped table-condensed table-centered mb10">
                                        <thead>

                                        <th>客户名称</th>
                                        <th>地区</th>
                                        <th>创建时间</th>
                                        <th>归属销售</th>
                                        <th class="table-smallest6">选择</th>
                                        </thead>
                                        <tbody>

                                        <c:if test="${not empty searchCustomerList}">
                                            <c:forEach items="${searchCustomerList}" var="list"
                                                       varStatus="status">
                                                <tr>
                                                    <td>${list.traderName}</td>
                                                    <td>${list.address}</td>
                                                    <td><date:date value="${list.addTime}" /></td>
                                                    <td>${list.personal}</td>
                                                    <td width="5%" style="text-align: center"><a
                                                            href="javaScript:void(0)"
                                                            onclick="selectCustomer('1','${list.traderId}','${list.traderName}','${list.traderCustomerId}','${list.customerType}', '${list.customerNature}',
                                                                '${list.belongPlatform}','${list.traderType}')">选择</a>
                                                    </td>
                                                </tr>
                                            </c:forEach>
                                        </c:if>
                                        <c:if test="${empty searchCustomerList}">
                                            <tr>
                                                <td colspan="5">查询无结果！请尝试使用其他关键词搜索。</td>
                                            </tr>
                                        </c:if>
                                        </tbody>
                                    </table>
                                </div>
                            </li>
                            <li class="visible"><tags:page page="${page}" />
                                <div class="clear"></div></li>
                            <div class="clear"></div>
                        </ul>
                        <!-- 搜索最后结果lastResult -->
                    </div>
                </form>
            </div>
        </div>
        <div  id="desc_div" class="formpublic formpublic1 none">
            <form method="post" id="addForm" action="${pageContext.request.contextPath}/orderstream/saleorder/saveSaleorderInfo.do">
                <ul class="payplan">
                    <li>
                        <div class="infor_name infor_name120">
                            <span>*</span> <label>客户名称：</label>
                        </div>

                        <input type="hidden" name="belongPlatform" value="" id="belongPlatform">
                        <div class="f_left table-larger" style="width: 80%">
                            <div class="mb10">
                                <input type="text" class="input-larger mr5 searchTraderName"
                                       name="searchTraderName" id="trader_name_span_1" disabled
                                       value=""/>
                                <input type="hidden" name="traderId" id="trader_id_1" value="">
                                <input type="hidden" name="indexId" id="indexId_1" value="1">
                                <input type="hidden" name="traderName" id="trader_name_1" value="">
                                <input type="hidden" name="customerType" id="customer_type_1" value="">
                                <input type="hidden" name="customerNature" id="customer_nature_1" value="">
                                <input type="hidden" name="pageType" id="pageType" value="${pageType}">
                                <%--选择客户后即可判断出创建的订单类型--%>
                                <span
                                        class="bt-bg-style bt-small bg-light-blue" onclick="reSearch();"
                                        >重新搜索</span>

                            </div>
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name120">
                            <span>*</span>
                            <label>客户类型&性质：</label>
                        </div>
                        <div class="f_left " id="customer_type_nature_div">
                        </div>
                    </li>
                    <li>
                        <div class="infor_name infor_name120">
                            <label>联系人：</label>
                        </div>
                        <div class="f_left">
                            <select class="input-xx contact" id="trader_contact_1" name="traderContactId" onclick="triggerContactInfoChanged()">
                                <option value="0">请选择联系人</option>

                            </select>
                            <input type="hidden" name="traderContactName">
                            <input type="hidden" name="traderContactTelephone">
                            <input type="hidden" name="traderContactMobile">

                            <span class="mt4 font-blue" id="addContactPart" onclick="addContact()">新增</span>
                            <div id="traderContactIdMsg" style="clear:both"></div>
                        </div>
                    </li>
                    <li id="traderAddressInfo">
                        <div class="infor_name infor_name120">
                            <label>联系地址：</label>
                        </div>
                        <div class="f_left">
                            <select class="input-xx contact" id="address_1" name="traderAddressId">
                                <option value="0">请选择联系地址</option>
                            </select>
                            <input type="hidden" name="traderArea">
                            <input type="hidden" name="traderAddress">
                            <span class="mt4 font-blue" onclick="addAddress()" >新增</span>
                            <div id="traderAddressIdMsg" style="clear:both"></div>
                        </div>
                    </li>
                    <li id="positionInfo">
                        <div class="infor_name infor_name120">
                            <label>职位</label>
                        </div>
                        <div class="f_left inputfloat" id="contact_position_1" >
                        </div>
                    </li>
                    <li id="rangeInfo">
                        <div class="infor_name infor_name120">
                            <label>可下单范围</label>
                        </div>
                        <div class="f_left inputfloat" id="allowed_goods_types_1">
                        </div>
                    </li>
                </ul>
                <div class="add-tijiao tcenter mt10">
                    <button type="button" class="bt-bg-style bg-deep-green" onclick="addSubmit();">下一步</button>
                </div>
            </form>
        </div>
    </div>
</div>
</body>
</html>
