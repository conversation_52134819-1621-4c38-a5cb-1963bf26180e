<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.order.dao.QuoteorderConsultReplyMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.QuoteorderConsultReply">
    <id column="QUOTEORDER_CONSULT_REPLY_ID" jdbcType="INTEGER" property="quoteorderConsultReplyId" />
    <result column="QUOTEORDER_ID" jdbcType="INTEGER" property="quoteorderId" />
    <result column="CONSULT_TYPE" jdbcType="INTEGER" property="consultType" />
    <result column="CONSULT_REPLIER" jdbcType="INTEGER" property="consultReplier" />
    <result column="SKU" jdbcType="VARCHAR" property="sku" />
    <result column="QUOTEORDER_GOODS_ID" jdbcType="INTEGER" property="quoteorderGoodsId" />
    <result column="CONSULT_OTHER" jdbcType="VARCHAR" property="consultOther" />
    <result column="CONSULT_OTHER_REPLY" jdbcType="VARCHAR" property="consultOtherReply" />
    <result column="CONSULT_OTHER_REPLY_STATUS" jdbcType="BOOLEAN" property="consultOtherReplyStatus" />
    <result column="REPORT_CONSULT_REPLY" jdbcType="INTEGER" property="reportConsultReply" />
    <result column="REPORT_CONSULT_REPLY_CONTENT" jdbcType="VARCHAR" property="reportConsultReplyContent" />
    <result column="REPORT_CONSULT_REPLY_STATUS" jdbcType="BOOLEAN" property="reportConsultReplyStatus" />
    <result column="REFERENCE_PRICE_REPLY" jdbcType="VARCHAR" property="referencePriceReply" />
    <result column="REFERENCE_PRICE_REPLY_STATUS" jdbcType="BOOLEAN" property="referencePriceReplyStatus" />
    <result column="REFERENCE_DELIVERY_CYCLE_REPLY" jdbcType="VARCHAR" property="referenceDeliveryCycleReply" />
    <result column="REFERENCE_DELIVERY_CYCLE_REPLY_STATUS" jdbcType="BOOLEAN" property="referenceDeliveryCycleReplyStatus" />
    <result column="CONSULT_EXECUTIVE" jdbcType="VARCHAR" property="consultExecutive" />
    <result column="CONSULT_EXECUTIVE_REPLY" jdbcType="VARCHAR" property="consultExecutiveReply" />
    <result column="CONSULT_EXECUTIVE_REPLY_STATUS" jdbcType="BOOLEAN" property="consultExecutiveReplyStatus" />
    <result column="ADD_TIME" jdbcType="BIGINT" property="addTime" />
    <result column="UPDATE_TIME" jdbcType="BIGINT" property="updateTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
  </resultMap>
  <sql id="Base_Column_List">
    QUOTEORDER_CONSULT_REPLY_ID, QUOTEORDER_ID, CONSULT_TYPE, CONSULT_REPLIER, SKU, QUOTEORDER_GOODS_ID, CONSULT_OTHER, CONSULT_OTHER_REPLY,
    CONSULT_OTHER_REPLY_STATUS, REPORT_CONSULT_REPLY,REPORT_CONSULT_REPLY_CONTENT, REPORT_CONSULT_REPLY_STATUS, REFERENCE_PRICE_REPLY,
    REFERENCE_PRICE_REPLY_STATUS, REFERENCE_DELIVERY_CYCLE_REPLY, REFERENCE_DELIVERY_CYCLE_REPLY_STATUS, 
    CONSULT_EXECUTIVE, CONSULT_EXECUTIVE_REPLY, CONSULT_EXECUTIVE_REPLY_STATUS, ADD_TIME, 
    UPDATE_TIME, CREATOR, UPDATER
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_QUOTEORDER_CONSULT_REPLY
    where QUOTEORDER_CONSULT_REPLY_ID = #{quoteorderConsultReplyId,jdbcType=INTEGER}
  </select>
  <select id="getByQuoteorderIdAndSku" resultType="com.vedeng.order.model.QuoteorderConsultReply">
    SELECT *FROM T_QUOTEORDER_CONSULT_REPLY WHERE QUOTEORDER_ID = #{quoteorderId} AND SKU = #{sku}
  </select>
  <select id="getByQuoteorderIdAndConsultType" resultType="com.vedeng.order.model.QuoteorderConsultReply">
    SELECT * FROM T_QUOTEORDER_CONSULT_REPLY WHERE QUOTEORDER_ID = #{quoteorderId} AND CONSULT_TYPE = #{consultType}
  </select>
    <select id="getUnHandledCountOfConsultSupply" resultType="java.lang.Integer">
      SELECT COUNT(*) FROM T_QUOTEORDER_CONSULT_REPLY WHERE QUOTEORDER_ID = #{quoteorderId} AND
        (CONSULT_OTHER_REPLY_STATUS = -1 || REPORT_CONSULT_REPLY_STATUS = -1 || REFERENCE_PRICE_REPLY_STATUS = -1 || REFERENCE_DELIVERY_CYCLE_REPLY_STATUS = -1)
    </select>
  <select id="getUnhandledCountOfConsultManager" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM T_QUOTEORDER_CONSULT_REPLY WHERE QUOTEORDER_ID = #{quoteorderId} AND CONSULT_EXECUTIVE_REPLY_STATUS = -1
  </select>

  <select id="getCountOfConsultSupplyReply" resultType="java.lang.Integer">
    SELECT COUNT(*) FROM T_QUOTEORDER_CONSULT_REPLY WHERE QUOTEORDER_ID = #{quoteorderId} AND CONSULT_TYPE = 1
    AND (CONSULT_OTHER_REPLY_STATUS = 1 || REPORT_CONSULT_REPLY_STATUS = 1 || REFERENCE_PRICE_REPLY_STATUS = 1 || REFERENCE_DELIVERY_CYCLE_REPLY_STATUS = 1)
  </select>
    <select id="getByQuoteorderIdAndQuoteorderGoods"
            resultType="com.vedeng.order.model.QuoteorderConsultReply">
      SELECT *FROM T_QUOTEORDER_CONSULT_REPLY WHERE QUOTEORDER_ID = #{quoteorderId} AND QUOTEORDER_GOODS_ID = #{quoteorderGoodsId}
    </select>
    <select id="getConsultReplierList" resultType="com.vedeng.authorization.model.User">
      SELECT
        *
      FROM
        T_USER
      WHERE
          USER_ID IN ( SELECT DISTINCT CONSULT_REPLIER FROM T_QUOTEORDER_CONSULT_REPLY WHERE CONSULT_REPLIER > 0 )
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_QUOTEORDER_CONSULT_REPLY
    where QUOTEORDER_CONSULT_REPLY_ID = #{quoteorderConsultReplyId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="QUOTEORDER_CONSULT_REPLY_ID" keyProperty="quoteorderConsultReplyId" parameterType="com.vedeng.order.model.QuoteorderConsultReply" useGeneratedKeys="true">
    insert into T_QUOTEORDER_CONSULT_REPLY (QUOTEORDER_ID,CONSULT_TYPE, CONSULT_REPLIER, SKU, QUOTEORDER_GOODS_ID, CONSULT_OTHER,
      CONSULT_OTHER_REPLY, CONSULT_OTHER_REPLY_STATUS, 
      REPORT_CONSULT_REPLY,REPORT_CONSULT_REPLY_CONTENT, REPORT_CONSULT_REPLY_STATUS,
      REFERENCE_PRICE_REPLY, REFERENCE_PRICE_REPLY_STATUS, 
      REFERENCE_DELIVERY_CYCLE_REPLY, REFERENCE_DELIVERY_CYCLE_REPLY_STATUS, 
      CONSULT_EXECUTIVE, CONSULT_EXECUTIVE_REPLY, 
      CONSULT_EXECUTIVE_REPLY_STATUS, ADD_TIME, UPDATE_TIME, 
      CREATOR, UPDATER)
    values (#{quoteorderId,jdbcType=INTEGER},#{consultType,jdbcType=INTEGER},#{consultReplier,jdbcType=INTEGER}, #{sku,jdbcType=VARCHAR}, #{quoteorderGoodsId,jdbcType=INTEGER}, #{consultOther,jdbcType=VARCHAR},
      #{consultOtherReply,jdbcType=VARCHAR}, #{consultOtherReplyStatus,jdbcType=BOOLEAN}, 
      #{reportConsultReply,jdbcType=INTEGER},#{reportConsultReplyContent,jdbcType=VARCHAR}, #{reportConsultReplyStatus,jdbcType=BOOLEAN},
      #{referencePriceReply,jdbcType=VARCHAR}, #{referencePriceReplyStatus,jdbcType=BOOLEAN}, 
      #{referenceDeliveryCycleReply,jdbcType=VARCHAR}, #{referenceDeliveryCycleReplyStatus,jdbcType=BOOLEAN}, 
      #{consultExecutive,jdbcType=VARCHAR}, #{consultExecutiveReply,jdbcType=VARCHAR}, 
      #{consultExecutiveReplyStatus,jdbcType=BOOLEAN}, #{addTime,jdbcType=BIGINT}, #{updateTime,jdbcType=BIGINT}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="QUOTEORDER_CONSULT_REPLY_ID" keyProperty="quoteorderConsultReplyId" parameterType="com.vedeng.order.model.QuoteorderConsultReply" useGeneratedKeys="true">
    insert into T_QUOTEORDER_CONSULT_REPLY
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        QUOTEORDER_ID,
      </if>
      <if test="consultType != null">
        CONSULT_TYPE,
      </if>
      <if test="consultReplier != null">
        CONSULT_REPLIER,
      </if>
      <if test="sku != null">
        SKU,
      </if>
      <if test="quoteorderGoodsId != null">
        QUOTEORDER_GOODS_ID,
      </if>
      <if test="consultOther != null">
        CONSULT_OTHER,
      </if>
      <if test="consultOtherReply != null">
        CONSULT_OTHER_REPLY,
      </if>
      <if test="consultOtherReplyStatus != null">
        CONSULT_OTHER_REPLY_STATUS,
      </if>
      <if test="reportConsultReply != null">
        REPORT_CONSULT_REPLY,
      </if>
      <if test="reportConsultReplyContent != null">
        REPORT_CONSULT_REPLY_CONTENT,
      </if>
      <if test="reportConsultReplyStatus != null">
        REPORT_CONSULT_REPLY_STATUS,
      </if>
      <if test="referencePriceReply != null">
        REFERENCE_PRICE_REPLY,
      </if>
      <if test="referencePriceReplyStatus != null">
        REFERENCE_PRICE_REPLY_STATUS,
      </if>
      <if test="referenceDeliveryCycleReply != null">
        REFERENCE_DELIVERY_CYCLE_REPLY,
      </if>
      <if test="referenceDeliveryCycleReplyStatus != null">
        REFERENCE_DELIVERY_CYCLE_REPLY_STATUS,
      </if>
      <if test="consultExecutive != null">
        CONSULT_EXECUTIVE,
      </if>
      <if test="consultExecutiveReply != null">
        CONSULT_EXECUTIVE_REPLY,
      </if>
      <if test="consultExecutiveReplyStatus != null">
        CONSULT_EXECUTIVE_REPLY_STATUS,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="updateTime != null">
        UPDATE_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="quoteorderId != null">
        #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="consultType != null">
        #{consultType,jdbcType=INTEGER},
      </if>
      <if test="consultReplier != null">
        #{consultReplier,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quoteorderGoodsId != null">
        #{quoteorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="consultOther != null">
        #{consultOther,jdbcType=VARCHAR},
      </if>
      <if test="consultOtherReply != null">
        #{consultOtherReply,jdbcType=VARCHAR},
      </if>
      <if test="consultOtherReplyStatus != null">
        #{consultOtherReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportConsultReply != null">
        #{reportConsultReply,jdbcType=INTEGER},
      </if>
      <if test="reportConsultReplyContent != null">
        #{reportConsultReplyContent,jdbcType=VARCHAR},
      </if>
      <if test="reportConsultReplyStatus != null">
        #{reportConsultReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="referencePriceReply != null">
        #{referencePriceReply,jdbcType=VARCHAR},
      </if>
      <if test="referencePriceReplyStatus != null">
        #{referencePriceReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="referenceDeliveryCycleReply != null">
        #{referenceDeliveryCycleReply,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycleReplyStatus != null">
        #{referenceDeliveryCycleReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="consultExecutive != null">
        #{consultExecutive,jdbcType=VARCHAR},
      </if>
      <if test="consultExecutiveReply != null">
        #{consultExecutiveReply,jdbcType=VARCHAR},
      </if>
      <if test="consultExecutiveReplyStatus != null">
        #{consultExecutiveReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.QuoteorderConsultReply">
    update T_QUOTEORDER_CONSULT_REPLY
    <set>
      <if test="quoteorderId != null">
        QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      </if>
      <if test="consultType != null">
        CONSULT_TYPE = #{consultType,jdbcType=INTEGER},
      </if>
      <if test="consultReplier != null">
        CONSULT_REPLIER = #{consultReplier,jdbcType=INTEGER},
      </if>
      <if test="sku != null">
        SKU = #{sku,jdbcType=VARCHAR},
      </if>
      <if test="quoteorderGoodsId != null">
        QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="consultOther != null">
        CONSULT_OTHER = #{consultOther,jdbcType=VARCHAR},
      </if>
      <if test="consultOtherReply != null">
        CONSULT_OTHER_REPLY = #{consultOtherReply,jdbcType=VARCHAR},
      </if>
      <if test="consultOtherReplyStatus != null">
        CONSULT_OTHER_REPLY_STATUS = #{consultOtherReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="reportConsultReply != null">
        REPORT_CONSULT_REPLY = #{reportConsultReply,jdbcType=INTEGER},
      </if>
      <if test="reportConsultReplyContent != null">
        REPORT_CONSULT_REPLY_CONTENT = #{reportConsultReplyContent,jdbcType=VARCHAR},
      </if>
      <if test="reportConsultReplyStatus != null">
        REPORT_CONSULT_REPLY_STATUS = #{reportConsultReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="referencePriceReply != null">
        REFERENCE_PRICE_REPLY = #{referencePriceReply,jdbcType=VARCHAR},
      </if>
      <if test="referencePriceReplyStatus != null">
        REFERENCE_PRICE_REPLY_STATUS = #{referencePriceReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="referenceDeliveryCycleReply != null">
        REFERENCE_DELIVERY_CYCLE_REPLY = #{referenceDeliveryCycleReply,jdbcType=VARCHAR},
      </if>
      <if test="referenceDeliveryCycleReplyStatus != null">
        REFERENCE_DELIVERY_CYCLE_REPLY_STATUS = #{referenceDeliveryCycleReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="consultExecutive != null">
        CONSULT_EXECUTIVE = #{consultExecutive,jdbcType=VARCHAR},
      </if>
      <if test="consultExecutiveReply != null">
        CONSULT_EXECUTIVE_REPLY = #{consultExecutiveReply,jdbcType=VARCHAR},
      </if>
      <if test="consultExecutiveReplyStatus != null">
        CONSULT_EXECUTIVE_REPLY_STATUS = #{consultExecutiveReplyStatus,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where QUOTEORDER_CONSULT_REPLY_ID = #{quoteorderConsultReplyId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.QuoteorderConsultReply">
    update T_QUOTEORDER_CONSULT_REPLY
    set QUOTEORDER_ID = #{quoteorderId,jdbcType=INTEGER},
      CONSULT_TYPE = #{consultType,jdbcType=INTEGER},
      CONSULT_REPLIER = #{consultReplier,jdbcType=INTEGER},
      SKU = #{sku,jdbcType=VARCHAR},
      QUOTEORDER_GOODS_ID = #{quoteorderGoodsId,jdbcType=INTEGER},
      CONSULT_OTHER = #{consultOther,jdbcType=VARCHAR},
      CONSULT_OTHER_REPLY = #{consultOtherReply,jdbcType=VARCHAR},
      CONSULT_OTHER_REPLY_STATUS = #{consultOtherReplyStatus,jdbcType=BOOLEAN},
      REPORT_CONSULT_REPLY = #{reportConsultReply,jdbcType=INTEGER},
      REPORT_CONSULT_REPLY_CONTENT = #{reportConsultReplyContent,jdbcType=VARCHAR},
      REPORT_CONSULT_REPLY_STATUS = #{reportConsultReplyStatus,jdbcType=BOOLEAN},
      REFERENCE_PRICE_REPLY = #{referencePriceReply,jdbcType=VARCHAR},
      REFERENCE_PRICE_REPLY_STATUS = #{referencePriceReplyStatus,jdbcType=BOOLEAN},
      REFERENCE_DELIVERY_CYCLE_REPLY = #{referenceDeliveryCycleReply,jdbcType=VARCHAR},
      REFERENCE_DELIVERY_CYCLE_REPLY_STATUS = #{referenceDeliveryCycleReplyStatus,jdbcType=BOOLEAN},
      CONSULT_EXECUTIVE = #{consultExecutive,jdbcType=VARCHAR},
      CONSULT_EXECUTIVE_REPLY = #{consultExecutiveReply,jdbcType=VARCHAR},
      CONSULT_EXECUTIVE_REPLY_STATUS = #{consultExecutiveReplyStatus,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      UPDATE_TIME = #{updateTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER}
    where QUOTEORDER_CONSULT_REPLY_ID = #{quoteorderConsultReplyId,jdbcType=INTEGER}
  </update>
    <update id="updateConsultExecutiveStatus">
      UPDATE T_QUOTEORDER_CONSULT_REPLY SET CONSULT_EXECUTIVE_REPLY_STATUS = 1, CONSULT_EXECUTIVE_REPLY = #{content}, UPDATER = #{updater}, UPDATE_TIME = #{timestamp} WHERE QUOTEORDER_CONSULT_REPLY_ID = #{quoteorderConsultReplyId}
    </update>

  <update id="updateAllConsultStatusByQuoteorderIdAndSkuList">
    UPDATE T_QUOTEORDER_CONSULT_REPLY
    SET REFERENCE_PRICE_REPLY_STATUS = #{status}, REFERENCE_DELIVERY_CYCLE_REPLY_STATUS = #{status}, REPORT_CONSULT_REPLY_STATUS = #{status}, CONSULT_OTHER_REPLY_STATUS = #{status}, CONSULT_EXECUTIVE_REPLY_STATUS = #{status}
    WHERE QUOTEORDER_ID = #{quoteorderId} AND SKU IN
    <foreach collection="skuList" index="index" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>
  <update id="updateAllConsultStatusByQuoteorderId">
    UPDATE T_QUOTEORDER_CONSULT_REPLY
    SET REFERENCE_PRICE_REPLY_STATUS = #{status}, REFERENCE_DELIVERY_CYCLE_REPLY_STATUS = #{status}, REPORT_CONSULT_REPLY_STATUS = #{status}, CONSULT_OTHER_REPLY_STATUS = #{status}, CONSULT_EXECUTIVE_REPLY_STATUS = #{status}
    WHERE QUOTEORDER_ID = #{quoteorderId}
  </update>


</mapper>