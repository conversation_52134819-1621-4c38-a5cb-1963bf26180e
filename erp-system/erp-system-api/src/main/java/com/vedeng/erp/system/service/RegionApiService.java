package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.RegionDto;

import java.util.List;

/**
 * <AUTHOR>
 * @description 地区服务
 * @date 2022/7/13 16:17
 **/
public interface RegionApiService {

    /**
     * 获取当前地址到父级顶级的 地区名
     * 中国 江苏 南京 秦淮
     * @param regionId 当前的地址id
     * @return String
     */
    String getThisRegionToParentRegion(Integer regionId);
    /**
     * 获取当前地址到父级顶级的 地区名
     * 江苏 南京 秦淮
     * @param regionId 当前的地址id
     * @return String
     */
    String getThisRegionToParentRegionP(Integer regionId);


    /**
     * 根据最小地区递归返回地区名称
     *
     * @param regionId 地区ID
     * @return 地区ID
     */
    String getRegion(Integer regionId);

    /**
     * 获取所有地区
     * @return
     */
    List<RegionDto> getAllRegion();


    RegionDto getRegionDto(Integer regionId);


    /**
     * 根据区域的名称获取地址信息
     * @param name
     * @return
     */
    String getRegionProvinceByCityName(String name);
}
