package com.vedeng.api.standard.internal;

/**
 * 响应解析器接口
 * 用于解析HTTP调用返回的响应数据，识别其中的错误信息
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-21
 */
public interface ResponseParser {
    
    /**
     * 解析响应数据
     * 
     * @param responseData 响应数据对象
     * @return 解析结果
     */
    ParseResult parse(Object responseData);
    
    /**
     * 判断是否可以解析指定的响应数据
     * 
     * @param responseData 响应数据对象
     * @return 是否可以解析
     */
    boolean canParse(Object responseData);
    
    /**
     * 响应解析结果
     */
    class ParseResult {
        private final boolean success;
        private final String businessCode;
        private final String errorMessage;
        private final Object data;
        
        public ParseResult(boolean success, String businessCode, String errorMessage, Object data) {
            this.success = success;
            this.businessCode = businessCode;
            this.errorMessage = errorMessage;
            this.data = data;
        }
        
        /**
         * 创建成功解析结果
         */
        public static ParseResult success(Object data) {
            return new ParseResult(true, null, null, data);
        }
        
        /**
         * 创建失败解析结果
         */
        public static ParseResult failure(String businessCode, String errorMessage, Object originalData) {
            return new ParseResult(false, businessCode, errorMessage, originalData);
        }
        
        /**
         * 创建无法解析的结果（按原数据处理）
         */
        public static ParseResult unparsable(Object data) {
            return new ParseResult(true, null, null, data);
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public String getBusinessCode() {
            return businessCode;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public Object getData() {
            return data;
        }
        
        public boolean hasBusinessCode() {
            return businessCode != null && !businessCode.trim().isEmpty();
        }
        
        public boolean hasErrorMessage() {
            return errorMessage != null && !errorMessage.trim().isEmpty();
        }
    }
}