Vue.component('ui-tyc', {
    template: `
        <div class="vd-ui-tianyancha-wrap">
            <div class="vd-ui-tianyancha">
                <div class="vd-ui-tyc-input" :style="{'width': width}">
                    <ui-input
                        :placeholder="placeholder"
                        v-bind="$attrs"
                        v-model="inputValue"
                        @blur="handlerBlur"
                        @input.native="handleInput"
                        @focus="handlerFocus"
                        @compositionend.native="commentPress"
                        width="100%"
                        :maxlength="maxlength"
                        autocomplete="off"
                    ></ui-input>
                    <div class="vd-ui-tyc-salename" v-if="tycInfo.saleName">{{ tycInfo.saleName }}</div>
                </div>

                <div class="vd-ui-tyc-right" v-if="needTyc">
                    <template v-if="tycInfo && tycInfo.tycFlag != 'Y'">
                        <div class="vd-ui-tyc-warn-icon" v-if="inputValue">
                            <i class="vd-ui_icon icon-caution1"></i>
                            <div class="vd-ui-tyc-icon-tip"><div class="tip-txt">该公司未匹配天眼查数据，可能存在不准确</div></div>
                        </div>
                        <div class="vd-ui-tyc-search-icon" @click="handlerSearch">
                            <i class="vd-ui_icon icon-search"></i>
                            <span>天眼查查询</span>
                        </div>
                    </template>
                    <i class="vd-ui_icon icon-tyc" v-else @click="showDetail"></i>
                </div>

                <transition>
                    <ul
                        class="vd-ui-tyc-search-related"
                        @click.stop
                        v-show="rotate1"
                        :style="{'width': width}"
                    >
                        <template v-if="loading">
                            <li class="loading">
                                <i class="vd-ui_icon icon-loading" ref="loading"></i>
                                <span>加载中...</span>
                            </li>
                        </template>

                        <template v-else-if="loadingFail">
                            <li class="failed-li">
                                <i class="vd-ui_icon icon-error2"></i>
                                <span>加载失败</span>
                                <span class="reload" @click="handleReload">重新加载</span>
                            </li>
                        </template>

                        <template v-else>
                            <div class="search-related-list" v-if="relateList.length">
                                <div class="local-data">建档客户匹配</div>
                                <div 
                                    class="sr-item" 
                                    :class="{'disabled': needDisable && !item.belong && !item.share && !item.subUser}"
                                    v-for="(item, index) in relateList" :key="index"
                                    @click.stop="chooseErp(item)"
                                >
                                    <div class="sr-item-left">
                                        <span class="icon" v-if="item.tycFlag == 'Y'"></span>
                                        <p v-if="needDisable && (!item.belong && !item.share && !item.subUser)">{{ item.traderName }}</p>
                                        <p v-else v-html="lightName(item)"></p>
                                    </div>
                                    <div class="sr-item-right">{{ item.saleName }}</div>
                                </div>
                            </div>
                            <li class="empty-li" v-else>
                                <p>暂无数据</p>
                            </li>
                        </template>
                    </ul>
                </transition>


                <transition>
                    <ui-dialog
                        :visible.sync="rotate2"
                        width="960px"
                        title="天眼查查询"
                    >
                        <div class="tyc-table-wrap vd-ui-table-wrap with-border" v-if="companyList.length">
                            <div class="vd-ui-table-body">
                                <table class="vd-ui-table">
                                    <colgroup>
                                        <col width="">
                                        <col width="120px">
                                        <col width="120px">
                                        <col width="120px">
                                        <col width="140px">
                                        <col width="100px">
                                    </colgroup>
                                    <thead>
                                        <tr class="vd-ui-tr">
                                            <th class="vd-ui-th">公司名称</th>
                                            <th class="vd-ui-th">机构类型</th>
                                            <th class="vd-ui-th">法人</th>
                                            <th class="vd-ui-th">省份</th>
                                            <th class="vd-ui-th">成立日期</th>
                                            <th class="vd-ui-th">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="vd-ui-tr" v-for="(item, index) in companyList" :key="index">
                                            <td class="vd-ui-td">{{ item.name }}</td>
                                            <td class="vd-ui-td">{{ item.companyType | filterCompanyType }}</td>
                                            <td class="vd-ui-td">{{ item.legalPersonName }}</td>
                                            <td class="vd-ui-td">{{ item.base }}</td>
                                            <td class="vd-ui-td">{{ item.estiblishTime | filterDateTime }}</td>
                                            <td class="vd-ui-td">
                                                <a class="option-item" class="tyc-choose" @click="chooseThis(item)">选择</a>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="tyc-empty" v-else>
                            <div class="empty-img"></div>
                            <div class="empty-txt">抱歉，未能匹配到天眼查公司数据，建议您检查关键词重新搜索</div>
                        </div>
                    </ui-dialog>
                </transition>

                <transition>
                    <ui-dialog
                        :visible.sync="rotate3"
                        width="960px"
                        title="天眼查查询"
                    >
                        <div class="tyc-info-wrap">
                            <div class="tyc-info-cnt J-tyc-info-cnt">
                                <template v-if="companyInfo && Object.keys(companyInfo).length">
                                    <div class="tyc-info-title">
                                        <div class="tyc-info-title-txt">{{ companyInfo.name }}</div>
                                        <div class="tyc-info-tags" v-if="companyInfo.tags && companyInfo.tags.length">
                                            <div class="tag-item" v-for="tag in companyInfo.tags" :key="tag">{{ tag }}</div>
                                        </div>
                                    </div>
                                    <div class="tyc-info-list">
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">地区</div>
                                            <div class="tyc-info-txt">
                                                <template v-if="companyInfo.base">{{ companyInfo.base }}</template>
                                                <template v-if="companyInfo.base && companyInfo.city"> / </template>
                                                <template v-if="companyInfo.city">{{ companyInfo.city }}</template>
                                                <template v-if="companyInfo.city && companyInfo.district"> / </template>
                                                <template v-if="companyInfo.district">{{ companyInfo.district }}</template>
                                            </div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">注册地址</div>
                                            <div class="tyc-info-txt">{{ companyInfo.regLocation || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">曾用名</div>
                                            <div class="tyc-info-txt">{{ companyInfo.historyNames || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">法人</div>
                                            <div class="tyc-info-txt">{{companyInfo.legalPersonName || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">注册资本</div>
                                            <div class="tyc-info-txt">{{companyInfo.regCapital || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">纳税人识别号</div>
                                            <div class="tyc-info-txt">{{companyInfo.taxNumber || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">企业联系方式</div>
                                            <div class="tyc-info-txt">{{companyInfo.phoneNumber || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">行业</div>
                                            <div class="tyc-info-txt">{{companyInfo.industry || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">国民经济行业分类</div>
                                            <div class="tyc-info-txt">{{ category_Big_Middle_Small || '-' }}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">成立日期</div>
                                            <div class="tyc-info-txt">{{companyInfo.estiblishTime | filterDetailDateTime}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">网址</div>
                                            <div class="tyc-info-txt">{{companyInfo.websiteList || '-'}}</div>
                                        </div>
                                        <div class="tyc-info-item">
                                            <div class="tyc-info-label">经营范围</div>
                                            <div class="tyc-info-txt">{{companyInfo.businessScope || '-'}}</div>
                                        </div>
                                    </div>
                                </template>
                                <div class="tyc-empty" v-else>
                                    <div class="empty-img"></div>
                                    <div class="empty-txt">抱歉，未能匹配到天眼查公司数据</div>
                                </div>
                            </div>
                            <div class="tyc-info-loading J-tyc-info-loading" style="display: none;">
                                <i class="bd-icon icon-loading"></i>
                            </div>
                        </div>
                    </ui-dialog>
                </transition>
            </div>
            <template v-if="inputValue.trim()">
                <template v-if="tycInfo.saleName">
                    <div class="tyc-trader-tip tip-warn" v-if="needSelf && !(tycInfo.saleName == USERINFO.userName || tycInfo.share)">
                        <i class="vd-ui_icon icon-caution1"></i>
                        <div class="tyc-tip-txt">该客户的归属销售是{{ tycInfo.saleName }}，建议您拜访前先与归属销售沟通</div>
                    </div>
                    <div class="tyc-trader-tip tip-success" v-else>
                        <i class="vd-ui_icon icon-yes1"></i>
                        <div class="tyc-tip-txt">已选择建档客户</div>
                    </div>
                </template>
                <div class="tyc-trader-tip tip-warn" v-else>
                    <i class="vd-ui_icon icon-caution1"></i>
                    <div class="tyc-tip-txt">当前录入客户为非建档客户<template v-if="!needSelf">，将无法报价</template></div>
                </div>
            </template>
        </div>
    `,

    props: {
        value: {
            type: String,
            default: ''
        },
        traderId: {
            type: [String, Number],
        },
        tycFlag: String,

        // 是否启用天眼查
        needTyc: {
            type: Boolean,
            default: true
        },
        // 选项中 启用禁用
        needDisable: {
            type: Boolean,
            default: false
        },
        // 是否要判断当前登陆账号的客户
        needSelf: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: ''
        },
        width: {
            type: String,
            default: '300px'
        },
        maxlength: {
            type: [Number, String],
            default: 1000,
        },
        saleName: String,
        share: {
            type: Boolean,
            default: false
        },
        subUser: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            inputValue: this.value,
            // erp本地匹配
            rotate1: false,
            relateList: [], // 搜索联想
            loading: false,
            loadingFail: false,
            timer: null,

            // 天眼查列表
            rotate2: false,
            companyList: [],
            canAjax: true,

            // 天眼查详情
            rotate3: false,
            companyInfo: null, // 公司信息-确定选择后
            tycInfo: {
                traderId: '',
                tycFlag: 'N', // Y:天眼查客户  N:不是天眼查客户 
                saleName: '',
                share: false,
                subUser: false,
            },
        }
    },
    computed: {
        lightName () {
            return (item) => {
                let name = item.traderName;
                if (!this.inputValue) return name;
                let regExp = new RegExp('(' + this.inputValue + ')', 'ig');
                name = name.replace(regExp, `<font color='#FF6600'">$1</font>`);

                if(item.share) {
                    name = '[分享] ' + name;
                }

                if(item.subUser) {
                    name = '[下属] ' + name;
                }

                return name;
            }
        },
        category_Big_Middle_Small () {
            let arr = [];
            this.companyInfo.category && arr.push(this.companyInfo.category);
            this.companyInfo.categoryBig && arr.push(this.companyInfo.categoryBig);
            this.companyInfo.categoryMiddle && arr.push(this.companyInfo.categoryMiddle);
            this.companyInfo.categorySmall && arr.push(this.companyInfo.categorySmall);
            return arr.join(' / ')
        }
    },
    filters: {
        filterDateTime (val) {
            if (/^\d{4}-\d{2}-\d{2}/.test(val)) {
                return val.substr(0, 10);
            } else {
                return '-'
            }
        },
        filterDetailDateTime (val) {
            if (/\d{10,}/.test(val)) {
                return moment(val).format('YYYY-MM-DD');
            } else {
                return '-'
            }
        },
        filterCompanyType (val) {
            let TYPES = {
                1: '公司',
                2: '香港企业',
                3: '社会组织',
                4: '律所',
                5: '事业单位',
                6: '基金会',
                7: '不存在法人、注册资本、统一社会信用代码、经营状态',
                8: '台湾企业',
                9: '新机构',
            }
            return TYPES[val] || ''
        }
    },
    watch: {
        value (newV) {
            this.inputValue = newV;
        },
        
        traderId: {
            handler (newV) {
                this.tycInfo.traderId = newV || '';
            },
            immediate: true
        },
        tycFlag: {
            handler (newV) {
                this.tycInfo.tycFlag = newV || '';
            },
            immediate: true
        },
        saleName: {
            handler (newV) {
                this.tycInfo.saleName = newV || '';
            },
            immediate: true
        },
        share: {
            handler (newV) {
                this.tycInfo.share = newV || false;
            },
            immediate: true
        },
        subUser: {
            handler (newV) {
                this.tycInfo.subUser = newV || false;
            },
            immediate: true
        },
        rotate1 (newV) {
            if (!newV) {
                this.loading = false;
                this.loadingFail = false;
            }
        },
    },
    mounted() {
        document.addEventListener('click', (e)=>{
            if (!this.$el.contains(e.target)) {
                this.rotate1 = false;
            }
        })
    },
    methods: {
        handlerBlur () {},

        // 清空公司信息
        clearCompany () {
            this.companyInfo = null;
            this.tycInfo = {
                traderId: '',
                tycFlag: 'N',
                saleName: '',
                share: false,
                subUser: false,
            }
            this.$emit('change', {});
        },

        // 节流搜索/300毫秒
        // handleInput: _.throttle(function (event) {
        //     this.companyInfo = null;
        //     this.$emit('input', event.target.value); // 修改外层v-model值this.$emit('change', {});
        //     this.getRelatelist(event.target.value);
        // }, 300),

        // 输入停顿300毫秒后搜素
        handleInput (e) {
            if (e.inputType != 'insertCompositionText') {
                let val = e.target.value.trim();
                this.clearCompany();
                this.$emit('input', val);

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300)
            }
        },
        handlerFocus () {
            if(this.inputValue.trim()) {
                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getRelatelist(this.inputValue);
                }, 300)
            }
        },
        // 中文结束
        commentPress(e) {
            let val = e.target.value.trim();
            if (val) {
                this.$emit('input', val);

                this.timer && clearTimeout(this.timer);
                this.timer = setTimeout(()=> {
                    this.getRelatelist(val);
                }, 300);
            }
        },

        getRelatelist (name) {
            this.rotate1 = true;
            this.loading = true;
            this.loadingFail = false;

            this.$axios.post(`/crm/trader/profile/queryTrader?name=${name}`).then(({data}) => {
                this.loading = false;
                if (data.success) {
                    this.relateList = data.data || [];
                } else {
                    this.loadingFail = true;
                }
            }).catch(err=> {
                this.loading = false;
                this.loadingFail = true;
            })
        },

        // 重新加载
        handleReload () {
            this.getRelatelist(this.inputValue);
        },

        // 选择已建档客户
        async chooseErp (item) {
            this.tycInfo = {
                tycFlag: item.tycFlag || 'N',
                traderId: item.traderId || '',
                saleName: item.saleName || '',
                share: item.share || false,
                subUser: item.subUser || false,
                // name: item.traderName,
            }
            this.inputValue = item.traderName || '';
            this.rotate1 = false;

            this.$emit("input", item.traderName || ''); // 修改外层v-model值

            let emitData = Object.assign({}, item, this.tycInfo);
            console.log('已建档信息', emitData);
            this.$emit('change', emitData);

            if (item.tycFlag == 'Y') {
                let data = await this.getDetail(item.traderName);
                if(data.success) {
                    this.companyInfo = data.data;
                }
            }
        },

        // 天眼查列表
        handlerSearch () {
            if (!this.inputValue) {
                this.$message.warn('请输入公司名称后进行查询')
                return false;
            }
            this.canAjax = false;
            this.rotate1 = false;
            GLOBAL.showGlobalLoading();

            this.$axios.post(`/crm/trader/profile/queryTycList?traderName=${this.inputValue}`).then(({data}) => {
                if (data.success) {
                    this.canAjax = true;
                    this.companyList = data.data || [];
                    this.rotate2 = true;
                } else {
                    this.$message.warn(data.message || '网络异常');
                    this.canAjax = true;
                }
                GLOBAL.hideGlobalLoading();
            }).catch(err=> {
                GLOBAL.hideGlobalLoading();
            })
        },
        // 选择天眼查公司
        async chooseThis(item) {
            let fetxhDetail = await this.getDetail(item.name);
            if (fetxhDetail.success) {
                this.companyInfo = fetxhDetail.data || {};
                this.inputValue = this.companyInfo.name || '';

                this.tycInfo = {
                    tycFlag: 'Y',
                    traderId: '',
                    saleName: '',
                    share: false,
                    subUser: false
                }
                let emitData = Object.assign({}, this.companyInfo, this.tycInfo)
                this.$emit("input", this.companyInfo.name || ''); // 修改外层v-model值
                console.log('天眼查信息', emitData);
                this.$emit('change', emitData);
            }

            this.rotate1 = false;
            this.rotate2 = false;
        },

        // 获取详情信息
        getDetail (name) {
            return new Promise((resolve, reject) => {
                GLOBAL.showGlobalLoading();
                this.$axios.post(`/crm/trader/profile/queryTycDetail?traderName=${name}`).then(({data}) => {
                    GLOBAL.hideGlobalLoading();
                    resolve(data || {});
                }).catch(errr=> {
                    GLOBAL.hideGlobalLoading();
                    reject(errr || {});
                    console.log('fetch error', errr);
                })
            })
        },

        async showDetail () {
            if (!(this.companyInfo && Object.keys(this.companyInfo).length)) {
                let fetxhDetail = await this.getDetail(this.inputValue);
                
            console.log(fetxhDetail)
                if (fetxhDetail.success) {
                    this.companyInfo = fetxhDetail.data || {};
                }
            }
            this.rotate3 = true;
        }
    }
})