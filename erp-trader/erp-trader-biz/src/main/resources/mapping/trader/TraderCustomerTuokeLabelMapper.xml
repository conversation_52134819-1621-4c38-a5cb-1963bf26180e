<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerTuokeLabelMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel">
            <id property="traderCustomerTuokeLabelId" column="TRADER_CUSTOMER_TUOKE_LABEL_ID" jdbcType="INTEGER"/>
            <result property="traderId" column="TRADER_ID" jdbcType="INTEGER"/>
            <result property="businessTarget" column="BUSINESS_TARGET" jdbcType="VARCHAR"/>
            <result property="hospitalLevel" column="HOSPITAL_LEVEL" jdbcType="VARCHAR"/>
            <result property="businessDepartment" column="BUSINESS_DEPARTMENT" jdbcType="VARCHAR"/>
            <result property="businessGoods" column="BUSINESS_GOODS" jdbcType="VARCHAR"/>
            <result property="businessModel" column="BUSINESS_MODEL" jdbcType="VARCHAR"/>
            <result property="salesModel" column="SALES_MODEL" jdbcType="VARCHAR"/>
            <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
            <result property="addTime" column="ADD_TIME" jdbcType="BIGINT"/>
            <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
            <result property="modTime" column="MOD_TIME" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        TRADER_CUSTOMER_TUOKE_LABEL_ID,TRADER_ID,BUSINESS_TARGET,
        HOSPITAL_LEVEL,BUSINESS_DEPARTMENT,BUSINESS_GOODS,
        BUSINESS_MODEL,SALES_MODEL,CREATOR,
        ADD_TIME,UPDATER,MOD_TIME
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CUSTOMER_TUOKE_LABEL
        where  TRADER_CUSTOMER_TUOKE_LABEL_ID = #{traderCustomerTuokeLabelId,jdbcType=INTEGER} 
    </select>

    <select id="selectByTraderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from T_TRADER_CUSTOMER_TUOKE_LABEL
        where  TRADER_ID = #{TRADER_ID,jdbcType=INTEGER}
    </select>
    <select id="getTuokeLabelInfo" resultType="com.vedeng.erp.trader.domain.vo.TraderCustomerTuokeLabelVo">
        SELECT
            TL.BUSINESS_TARGET AS businessTargetStr,
            TL.HOSPITAL_LEVEL AS hospitalLevelStr,
            TL.BUSINESS_DEPARTMENT AS businessDepartmentStr,
            TL.BUSINESS_GOODS AS businessGoodsStr,
            TL.BUSINESS_MODEL AS businessModelStr,
            TL.SALES_MODEL AS salesModelStr,
            T.TRADER_NAME,
            T.TRADER_ID,
            BC.BUSINESS_CHANCE_ID,
            BC.BUSINESS_CLUES_ID,
            BC.WORTH,
            BC.COMMENT
        FROM
            T_TRADER T
            LEFT JOIN T_TRADER_CUSTOMER_TUOKE_LABEL TL ON TL.TRADER_ID = T.TRADER_ID
            LEFT JOIN T_BUSINESS_CLUES BC ON T.TRADER_ID = BC.TRADER_ID
        WHERE
            T.TRADER_ID = #{traderId,jdbcType=INTEGER}
            AND BC.BUSINESS_CLUES_ID = #{businessCluesId,jdbcType=INTEGER}
        LIMIT 1
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete from T_TRADER_CUSTOMER_TUOKE_LABEL
        where  TRADER_CUSTOMER_TUOKE_LABEL_ID = #{traderCustomerTuokeLabelId,jdbcType=INTEGER} 
    </delete>
    <insert id="insert" keyColumn="TRADER_CUSTOMER_TUOKE_LABEL_ID" keyProperty="traderCustomerTuokeLabelId" parameterType="com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel" useGeneratedKeys="true">
        insert into T_TRADER_CUSTOMER_TUOKE_LABEL
        ( TRADER_CUSTOMER_TUOKE_LABEL_ID,TRADER_ID,BUSINESS_TARGET
        ,HOSPITAL_LEVEL,BUSINESS_DEPARTMENT,BUSINESS_GOODS
        ,BUSINESS_MODEL,SALES_MODEL,CREATOR
        ,ADD_TIME,UPDATER,MOD_TIME
        )
        values (#{traderCustomerTuokeLabelId,jdbcType=INTEGER},#{traderId,jdbcType=INTEGER},#{businessTarget,jdbcType=VARCHAR}
        ,#{hospitalLevel,jdbcType=VARCHAR},#{businessDepartment,jdbcType=VARCHAR},#{businessGoods,jdbcType=VARCHAR}
        ,#{businessModel,jdbcType=VARCHAR},#{salesModel,jdbcType=VARCHAR},#{creator,jdbcType=INTEGER}
        ,#{addTime,jdbcType=BIGINT},#{updater,jdbcType=INTEGER},#{modTime,jdbcType=BIGINT}
        )
    </insert>
    <insert id="insertSelective" keyColumn="TRADER_CUSTOMER_TUOKE_LABEL_ID" keyProperty="traderCustomerTuokeLabelId" parameterType="com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel" useGeneratedKeys="true">
        insert into T_TRADER_CUSTOMER_TUOKE_LABEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="traderCustomerTuokeLabelId != null">TRADER_CUSTOMER_TUOKE_LABEL_ID,</if>
                <if test="traderId != null">TRADER_ID,</if>
                <if test="businessTarget != null">BUSINESS_TARGET,</if>
                <if test="hospitalLevel != null">HOSPITAL_LEVEL,</if>
                <if test="businessDepartment != null">BUSINESS_DEPARTMENT,</if>
                <if test="businessGoods != null">BUSINESS_GOODS,</if>
                <if test="businessModel != null">BUSINESS_MODEL,</if>
                <if test="salesModel != null">SALES_MODEL,</if>
                <if test="creator != null">CREATOR,</if>
                <if test="addTime != null">ADD_TIME,</if>
                <if test="updater != null">UPDATER,</if>
                <if test="modTime != null">MOD_TIME,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
                <if test="traderCustomerTuokeLabelId != null">#{traderCustomerTuokeLabelId,jdbcType=INTEGER},</if>
                <if test="traderId != null">#{traderId,jdbcType=INTEGER},</if>
                <if test="businessTarget != null">#{businessTarget,jdbcType=VARCHAR},</if>
                <if test="hospitalLevel != null">#{hospitalLevel,jdbcType=VARCHAR},</if>
                <if test="businessDepartment != null">#{businessDepartment,jdbcType=VARCHAR},</if>
                <if test="businessGoods != null">#{businessGoods,jdbcType=VARCHAR},</if>
                <if test="businessModel != null">#{businessModel,jdbcType=VARCHAR},</if>
                <if test="salesModel != null">#{salesModel,jdbcType=VARCHAR},</if>
                <if test="creator != null">#{creator,jdbcType=INTEGER},</if>
                <if test="addTime != null">#{addTime,jdbcType=BIGINT},</if>
                <if test="updater != null">#{updater,jdbcType=INTEGER},</if>
                <if test="modTime != null">#{modTime,jdbcType=BIGINT},</if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel">
        update T_TRADER_CUSTOMER_TUOKE_LABEL
        <set>
                <if test="traderId != null">
                    TRADER_ID = #{traderId,jdbcType=INTEGER},
                </if>
                <if test="businessTarget != null">
                    BUSINESS_TARGET = #{businessTarget,jdbcType=VARCHAR},
                </if>
                <if test="hospitalLevel != null">
                    HOSPITAL_LEVEL = #{hospitalLevel,jdbcType=VARCHAR},
                </if>
                <if test="businessDepartment != null">
                    BUSINESS_DEPARTMENT = #{businessDepartment,jdbcType=VARCHAR},
                </if>
                <if test="businessGoods != null">
                    BUSINESS_GOODS = #{businessGoods,jdbcType=VARCHAR},
                </if>
                <if test="businessModel != null">
                    BUSINESS_MODEL = #{businessModel,jdbcType=VARCHAR},
                </if>
                <if test="salesModel != null">
                    SALES_MODEL = #{salesModel,jdbcType=VARCHAR},
                </if>
                <if test="creator != null">
                    CREATOR = #{creator,jdbcType=INTEGER},
                </if>
                <if test="addTime != null">
                    ADD_TIME = #{addTime,jdbcType=BIGINT},
                </if>
                <if test="updater != null">
                    UPDATER = #{updater,jdbcType=INTEGER},
                </if>
                <if test="modTime != null">
                    MOD_TIME = #{modTime,jdbcType=BIGINT},
                </if>
        </set>
        where   TRADER_CUSTOMER_TUOKE_LABEL_ID = #{traderCustomerTuokeLabelId,jdbcType=INTEGER} 
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.dto.TraderCustomerTuokeLabel">
        update T_TRADER_CUSTOMER_TUOKE_LABEL
        set 
            TRADER_ID =  #{traderId,jdbcType=INTEGER},
            BUSINESS_TARGET =  #{businessTarget,jdbcType=VARCHAR},
            HOSPITAL_LEVEL =  #{hospitalLevel,jdbcType=VARCHAR},
            BUSINESS_DEPARTMENT =  #{businessDepartment,jdbcType=VARCHAR},
            BUSINESS_GOODS =  #{businessGoods,jdbcType=VARCHAR},
            BUSINESS_MODEL =  #{businessModel,jdbcType=VARCHAR},
            SALES_MODEL =  #{salesModel,jdbcType=VARCHAR},
            CREATOR =  #{creator,jdbcType=INTEGER},
            ADD_TIME =  #{addTime,jdbcType=BIGINT},
            UPDATER =  #{updater,jdbcType=INTEGER},
            MOD_TIME =  #{modTime,jdbcType=BIGINT}
        where   TRADER_CUSTOMER_TUOKE_LABEL_ID = #{traderCustomerTuokeLabelId,jdbcType=INTEGER} 
    </update>
</mapper>
