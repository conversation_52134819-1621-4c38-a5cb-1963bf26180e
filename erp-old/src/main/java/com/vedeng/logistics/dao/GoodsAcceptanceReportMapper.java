package com.vedeng.logistics.dao;

import com.vedeng.logistics.model.GoodsAcceptanceReport;

public interface GoodsAcceptanceReportMapper {
    /**
     * delete by primary key
     * @param goodsAcceptanceReportId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long goodsAcceptanceReportId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(GoodsAcceptanceReport record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(GoodsAcceptanceReport record);

    /**
     * select by primary key
     * @param goodsAcceptanceReportId primary key
     * @return object by primary key
     */
    GoodsAcceptanceReport selectByPrimaryKey(Long goodsAcceptanceReportId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(GoodsAcceptanceReport record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(GoodsAcceptanceReport record);
}