<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增沟通记录" scope="application" />	
<%@ include file="../../common/common.jsp"%>
<c:if test="${!(pop == 'Y')}">
	<%@ include file="customer_tag.jsp"%>
</c:if>
<div class="baseinforcontainer" style="padding-bottom: 15px;">
	<div class="border">
		<div class="baseinfor f_left">沟通记录</div>
		<div class="clear"></div>
	</div>
	<div class="baseinforeditform">
		<form method="post"
			action="${pageContext.request.contextPath}/trader/customer/saveaddcommunicate.do"
			id="form">
			<ul>
				<li>
					<div class="infor_name">
						<lable>客户名称</lable>
					</div>
					<div class="f_left">
						<div style="padding-top: 3px;">
							${traderBaseInfo.traderName}
						</div>
						<div id="traderNameError"></div>
					</div>
				</li>

				<li>
					<div class="infor_name">
						<lable>归属销售</lable>
					</div>
					<div class="f_left">
						<div  style="padding-top: 3px;">
							${traderBaseInfo.salesNameStr}
						</div>
						<div id="salesNameError"></div>
					</div>
				</li>

				<li>
					<div class="infor_name">
						<span>*</span>
						<lable>联系人</lable>
					</div>
					<div class="f_left  ">
						<select class="mr5" name="traderContactId" id="traderContactId">
							<c:choose>
								<c:when test="${contactList.size() == 0}">
									<option selected="selected" value="0">请添加联系人</option>
								</c:when>
								<c:otherwise>
									<option value="0">请选择</option>
									<c:if test="${not empty contactList }">
										<c:forEach items="${contactList }" var="contact">
											<option value="${contact.traderContactId }" <c:if test="${contactList.size() == 1}">selected="selected"</c:if>>
													${contact.name }
												<c:if
														test="${contact.telephone !='' and contact.telephone != null }">|${contact.telephone }</c:if>
												<c:if
														test="${contact.mobile !='' and contact.mobile != null }">|${contact.mobile }</c:if>
												<c:if
														test="${contact.mobile2 !=''and contact.mobile2 != null}">|${contact.mobile2 }</c:if>
											</option>
										</c:forEach>
									</c:if>
								</c:otherwise>
							</c:choose>
						</select>
					</div>
					<div class="title-click   pop-new-data" style='float:left;margin:-4px 0 0 10px;' layerParams='{"width":"700px","height":"500px","title":"新增联系人","link":"${pageContext.request.contextPath}/order/bussinesschance/addTraderContact.do?traderId=${traderCustomer.traderId}"}'>
						添加联系人
					</div>
				</li>
				<li>
					<div class="infor_name">
						<span>*</span>
						<lable>沟通时间</lable>
					</div>
					<div class="f_left inputfloat ">
						<input class="Wdate input-small mr0" type="text"
							placeholder="请选择时间"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'end\')}'})" autocomplete="off"
							name="begin" id="begin" value="<date:date value ="${communicateRecord.begintime} "/>"/>
							<div class="gang">-</div>
							<input class="Wdate input-small ml5"
							type="text" placeholder="请选择时间"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'begin\')}'})" name="end" autocomplete="off"
							id="end" value="<date:date value ="${communicateRecord.endtime} "/>"/>
					</div>
				</li>

				<%-- 2020.12http://jira.ivedeng.com/browse/VDERP-5008 需求中要求删除的字段--%>
				<%--<li class="bt">
					<div class="infor_name mt0">
						<span>*</span>
						<lable>沟通目的</lable>
					</div>
					<div class="f_left inputfloat table-largest">
						<ul id="communicateGoalDiv">
							<c:if test="${not empty communicateGoal }">
								<c:forEach items="${communicateGoal }" var="goal">
									<c:if test="${goal.sysOptionDefinitionId != 271 }">
										<li><input type="radio" name="communicateGoal"
											value="${goal.sysOptionDefinitionId }" /> <label class="mr8">${goal.title }</label>
										</li>
									</c:if>
								</c:forEach>
							</c:if>
						</ul>
					</div>
				</li>
				<li class="bt">
					<div class="infor_name mt0">
						<span>*</span>
						<lable>沟通方式</lable>
					</div>
					<div class="f_left inputfloat table-largest">
						<ul id="communicateModeDiv">
							<c:if test="${not empty communicateMode }">
								<c:forEach items="${communicateMode }" var="mode">
									<li><input type="radio" name="communicateMode"
										value="${mode.sysOptionDefinitionId }" /> <label class="mr8">${mode.title }</label>
									</li>
								</c:forEach>
							</c:if>
						</ul>
					</div>
				</li>--%>
				<li>
					<div class="infor_name">
						<span>*</span>
						<lable>沟通内容</lable>
					</div>
					<div class="f_left table-largest">
						<div class="inputfloat manageaddtag">
							<label class="mt4 mr8">您可以从这些标签中选择</label>
							<c:if test="${not empty tagList }">
								<c:forEach items="${tagList }" var="tag">
									<span onclick="addTag(${tag.tagId},'${tag.tagName }',this);">${tag.tagName }</span>
								</c:forEach>
							</c:if>
							<c:if test="${page.totalPage > 1}">
								<div class="change"
									onclick="changeTag(${page.totalPage},10,this,32);">
									<span class="m0">换一批(</span><span class="m0" id="leftNum">${page.totalPage}</span><span
										class="m0">)</span> <input type="hidden" id="pageNo"
										value="${page.pageNo}">
								</div>
							</c:if>
						</div>
						<div class="inputfloat <c:if test="${empty tagList }">mt8</c:if>">
							<input type="text" id="defineTag"
								placeholder="如果标签中没有您所需要的，请自行填写" class="input-large " style='height:26px;'>
							<div class="f_left bt-bg-style bg-light-blue bt-small  addbrand"
								onclick="addDefineTag(this);">添加</div>
						</div>
						<div class="addtags mt6 none">
							<ul id="tag_show_ul">
							</ul>
						</div>
						<div>
                                <textarea  name="contentSuffix" placeholder="沟通内容最多输入200字符，请检查后提交"
										   style="width: 450px; height: 100px"></textarea>
							<div id="contentSuffixError" ></div>
						</div>
					</div>
				</li>
				<li>
					<div class="infor_name">
						<span>*</span>
						<lable>下次沟通时间</lable>
					</div>
					<div class="f_left ">
						<input class="Wdate input-small" type="text" placeholder="请选择日期" autocomplete="off"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd'})" name="nextDate" id="nextDate"/>
					</div>
					<input type="checkbox" id="noneNextDate"> 暂无下次沟通时间
					<input type="hidden" id="noneNextDateVal" name="noneNextDate">
				</li>
				<li>
					<div class="infor_name">
						<lable>下次沟通内容</lable>
					</div>
					<div class="f_left  ">
						<input type="text" class="input-xxx" name="nextContactContent"
							id="nextContactContent">
					</div>
				</li>
				<li>
					<div class="infor_name">
						<lable>备注</lable>
					</div>
					<div class="f_left  ">
						<input type="text" class="input-xxx" name="comments" id="comments">
					</div>
				</li>
			</ul>
			<div class="add-tijiao tcenter">
				<input type="hidden" name="formToken" value="${formToken}"/>
				<input type="hidden" name="pop" value="${pop}"/>

				<input type="hidden" name="traderId"
					value="${traderCustomer.traderId }">
					 <input type="hidden" name="traderType" value="1">
					 <input type="hidden"
					name="traderCustomerId" value="${traderCustomer.traderCustomerId }">
				<button type="submit">提交</button>
<%--				<button class="dele" id="close-layer" type="button"
					onclick="goUrl('${pageContext.request.contextPath}/trader/customer/communicaterecord.do?traderId=${traderCustomer.traderId}&traderCustomerId=${traderCustomer.traderCustomerId}')">取消</button>--%>
			</div>
		</form>
	</div>
</div>
<script type="text/javascript">
	$(document).ready(function() {
		setTimeout(function(){
			$.ajax({
				url: "${pageContext.request.contextPath}/system/call/pushVoiceMp3.do",
				type: "post",
				timeout: 60000,
				data: {
					"coid":'${param.coid}'
				},
				dataType : "json",
				success: function(data) {
					console.log("通话完成，发起AI解析。");
				}
			});
		}, 2000);
	});


</script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/customer/add_communicate.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/tag.js?rnd=${resourceVersionKey}"></script>
<%@ include file="../../common/footer.jsp"%>
