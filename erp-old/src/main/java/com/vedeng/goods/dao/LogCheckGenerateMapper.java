package com.vedeng.goods.dao;

import com.vedeng.flash.dto.ReviewQueryDto;
import com.vedeng.goods.model.LogCheckGenerate;
import com.vedeng.goods.model.LogCheckGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface LogCheckGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int countByExample(LogCheckGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int deleteByExample(LogCheckGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int deleteByPrimaryKey(Integer logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int insert(LogCheckGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int insertSelective(LogCheckGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    List<LogCheckGenerate> selectByExample(LogCheckGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    LogCheckGenerate selectByPrimaryKey(Integer logId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int updateByExampleSelective(@Param("record") LogCheckGenerate record, @Param("example") LogCheckGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int updateByExample(@Param("record") LogCheckGenerate record, @Param("example") LogCheckGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int updateByPrimaryKeySelective(LogCheckGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_LOG_CHECK
     *
     * @mbggenerated Fri May 31 09:38:39 CST 2019
     */
    int updateByPrimaryKey(LogCheckGenerate record);

    Integer selectCountByBuzIdAndType(@Param("buzId") Integer buzId, @Param("logType") Integer logType);

    List<LogCheckGenerate> getAllUnfinishProcess(@Param("selfReviewTypeList") List<Integer> selfReviewTypeList, @Param("reviewQueryDto") ReviewQueryDto reviewQueryDto);

    /**
     * 根据 审核类型 审核状态 获取 最后一次审核记录
     * @param logBizId
     * @param logType
     * @param logStatus
     * @return
     */
     LogCheckGenerate getByLogBizIdAndLogTypeAndLogStatusOrderByAddTime(@Param("logBizId")Integer logBizId,@Param("logType")Integer logType,@Param("logStatus")Integer logStatus);


}