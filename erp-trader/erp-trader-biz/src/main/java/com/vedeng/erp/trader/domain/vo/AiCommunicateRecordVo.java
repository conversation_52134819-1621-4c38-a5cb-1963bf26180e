package com.vedeng.erp.trader.domain.vo;

import com.vedeng.erp.trader.domain.entity.VoiceFieldResultEntity;
import com.vedeng.erp.trader.dto.RCommunicateTodoJAiDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: ai分析结果vo
 * @date 2024/5/11 13:51
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AiCommunicateRecordVo {

    /**
     * 录音ID
     */
    private Integer communicateRecordId;
    /**
     * 联系人
     */
    private Integer traderContactId;
    /**
     * 联系人
     */
    private String traderContactName;
    /**
     * 电话
     */
    private String traderContactPhone;
    /**
     * 沟通时间
     */
    private String contactDate;
    /**
     * 通话类型
     */
    private String contactType;
    /**
     * 分析结果-摘要
     */
    private List<Digest> digestList;
    /**
     * 交易io
     */
    private Integer traderId;

    private Integer traderCustomerId;

    private String traderCustomerName;

    /**
     * 是否存在AI解析结果
     */
    private boolean exist;

    /**
     * 无结果时提示
     */
    private String tip;

    /**
     * 1呼入2呼出 &  表示企业微信沟通3 呼出 4呼入
     */
    private Integer coidType;

    /**
     * 分析结果-待办
     */
    private List<VoiceFieldResultDto> todoList;

    private RCommunicateTodoJAiDto rCommunicateTodoJAiDto = new RCommunicateTodoJAiDto();


    @Data
    public static class Digest {
        private Integer id;
        private String value;
        private boolean checked;
        private boolean disabled;
    }
}
