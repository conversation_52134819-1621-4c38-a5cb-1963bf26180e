package com.vedeng.order.enums;

import lombok.Getter;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum SaleOrderStatusEnum {

    /**
     * 待用户确认（集采）
     */
    CUSTOMER_CONFIRMING(4, "待用户确认"),


    /**
     * 待审核（默认）
     */
    CONFIRMING(0, "待审核"),

    /**
     * 已关闭
     */
    CANCEL(3, "已关闭"),

    ;

    private Integer status;
    private String message;

    SaleOrderStatusEnum(Integer status, String message) {
        this.status = status;
        this.message = message;
    }
}
