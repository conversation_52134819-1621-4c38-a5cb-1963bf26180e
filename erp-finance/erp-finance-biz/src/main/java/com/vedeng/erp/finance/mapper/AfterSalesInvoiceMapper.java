package com.vedeng.erp.finance.mapper;

import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.erp.finance.domain.entity.AfterSalesInvoiceEntity;
import org.springframework.stereotype.Repository;

import java.util.List;
@Repository("newAfterSalesInvoiceMapper")
public interface AfterSalesInvoiceMapper {
    /**
     * delete by primary key
     * @param afterSalesInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer afterSalesInvoiceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(AfterSalesInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(AfterSalesInvoiceEntity record);

    /**
     * select by primary key
     * @param afterSalesInvoiceId primary key
     * @return object by primary key
     */
    AfterSalesInvoiceEntity selectByPrimaryKey(Integer afterSalesInvoiceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(AfterSalesInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(AfterSalesInvoiceEntity record);

    int updateBatchSelective(List<AfterSalesInvoiceEntity> list);

}