package com.newtask.filemove.batch;

import com.google.common.collect.Lists;
import com.newtask.filemove.AbstractFileMoveInterface;
import com.newtask.filemove.dto.CommonConstant;

import com.newtask.filemove.dto.FileMoveDto;
import com.newtask.filemove.theadpool.AttachmentTask;
import com.newtask.filemove.theadpool.FileMoveThreadPool;
import com.vedeng.filemove.dao.FileMoveDao;
import com.vedeng.system.model.Attachment;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/**
 * 商品附件的批处理命令
 */
@Service
public class AttachmentBatchCommand extends AbstractFileMoveInterface {

    private static final Logger LOGGER = LoggerFactory.getLogger(AttachmentBatchCommand.class);

    @Resource
    private FileMoveDao fileMoveDao;

    protected String getCommandType() {
        return CommonConstant.BATCH;
    }

    protected List<FileMoveDto> getFileMoveInfoByPageSize(int start, int pageSize) {

        List<Attachment> attachmentList = fileMoveDao.getAttachmentByPage(start,pageSize);

        if(CollectionUtils.isEmpty(attachmentList)){
            return null;
        }

        return Lists.transform(attachmentList , (entity) -> {
                                                    FileMoveDto fileMoveDto = new FileMoveDto();
                                                    fileMoveDto.setId(entity.getAttachmentId());
                                                    fileMoveDto.setDomain(entity.getDomain());
                                                    fileMoveDto.setUri(entity.getUri());
                                                    return fileMoveDto;
                                                });
    }

    @Override
    protected Runnable getFileMoveTask(List<FileMoveDto> fileMoveDtoList) {
        return new AttachmentTask(fileMoveDtoList);
    }

    @Override
    protected ExecutorService getExecutorService() {
        return FileMoveThreadPool.getTraderCertificateThreadPool();
    }
}
