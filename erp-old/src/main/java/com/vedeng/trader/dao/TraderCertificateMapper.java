package com.vedeng.trader.dao;

import com.vedeng.trader.model.TraderCertificate;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import javax.inject.Named;
import java.util.List;

@Named("traderCertificateMapper")
public interface TraderCertificateMapper {

    int insertSelective(TraderCertificate record);

    int updateByPrimaryKeySelective(TraderCertificate record);

    List<TraderCertificate> getTraderCertificatesByTraderId(TraderCustomer customer);

    /**
     * <b>Description:</b>根据客户标识获取资质证书<br>
     * @param
     * @return
     * @Note
     * <b>Author:calvin</b>
     * <br><b>Date:</b> 2020/3/17
     */
    List<TraderCertificate> getCertificateListByTraderId(@Param("traderId")Integer traderId,@Param("traderType")Integer traderType);

    List<TraderCertificate> getTraderCertificates(@Param("start") Integer start, @Param("limit") Integer limit);

    void updateTraderCertificate(TraderCertificate certificate);


    TraderCertificate getTraderCertificateById(Integer id);

    void delTraderCertificateAndByTypeId(@Param("traderId") Integer traderId, @Param("typeId") Integer typeId);

    /**
     * @description: 获取资质信息
     * @return: TraderCertificateVo
     * @author: Strange
     * @date: 2020/12/15
     **/
    List<TraderCertificateVo> getListTraderCertificateVo(TraderCertificateVo traderCertificateVo);

    /**
     * 获取资质信息传给Doc
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2021/1/15 16:30.
     * @author: Randy.Xu.
     * @param traderCertificateVo
     * @return: java.util.List<com.vedeng.trader.model.vo.TraderCertificateVo>.
     * @throws:  .
     */
    List<TraderCertificateVo> getTraderCertificateVoListByIdAndType(TraderCertificateVo traderCertificateVo);


    /**
     * <b>Description:</b><br> 获取资质信息
     * @param traderId
     */
    List<TraderCertificateVo> getTraderCertificateVo(@Param("traderId") Integer traderId,@Param("traderType")Integer traderType);

    /**
     * <b>Description:</b><br> 获取资质信息
     * @param record
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年5月31日 下午5:56:57
     */
    TraderCertificateVo getTraderCertificatePageVo(TraderCertificate record);

    /**
     * <b>Description:</b><br> 根据traderid删除所有资质
     * @param traderId
     * @return
     * @Note
     * <b>Author:</b> east
     * <br><b>Date:</b> 2017年8月17日 上午10:04:31
     */
    int delTraderCertificate(@Param("traderId") Integer traderId);

    /**
     * 获取资质信息列表
     * @return
     * @param page
     * @param count
     */
    List<TraderCertificate> getTraderCertificateList(@Param("page") Integer page, @Param("count") Integer count);

    /**
     * 批量更新资质信息
     * @param traderCertificateList
     */
    void batchUpdateTraderCertificate(List<TraderCertificate> traderCertificateList);

    void updateTraderCertificateSuffix(@Param("traderCertificate") TraderCertificate traderCertificate);

    @Select("SELECT SUFFIX FROM T_FILE WHERE RESOURCE_ID = #{resourceId} AND IS_DELETED = 0")
    String selectSuffixFromFile(@Param("resourceId") String resourceId);

    /**
     * 根据客户id和客户类型删除客户资质
     * @param traderId
     * @param traderType
     */
    void delTraderCertificateByTraderIdAndTraderType(@Param("traderId") Integer traderId, @Param("traderType") Integer traderType);


    /**
     * 获取资质信息
     * @param traderCertificate
     * @return
     */
    List<TraderCertificate> getTraderCertificatesById(TraderCertificate traderCertificate);


    /**
     * 更新资质状态
     * @param traderCertificate
     */
    void updatTraderBusinessCard(TraderCertificate traderCertificate);

}
