package com.vedeng.erp.saleorder.constant;

/**
 * <AUTHOR>
 * @date 2021/12/27 13:05
 **/
public enum CustomerNatureTypeEnum {

    /**
     * CUSTOMER_NATURE 客户性质
     */
    DISTRIBUTION(465, "分销"),

    TERMINAL(466, "终端");

    /**
     * 性质
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    CustomerNatureTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }
}
