@import url(../font/font.css);
.dlg-lvselect--wrap {
  position: fixed;
  z-index: 9999;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1.5;
  font-size: 14px;
}
.dlg-lvselect--wrap * {
  box-sizing: border-box;
}
.dlg-lvselect--wrap .dlg-inner {
  width: 900px;
  border-radius: 3px;
  background: #fff;
}
.dlg-lvselect--wrap .dlg-inner .dlg-title {
  height: 45px;
  line-height: 45px;
  font-weight: bold;
  font-size: 16px;
  padding: 0 20px;
  border-bottom: 1px solid #ced3d9;
}
.dlg-lvselect--wrap .dlg-inner .dlg-cnt {
  padding: 20px;
}
.dlg-lvselect--wrap .dlg-inner .dlg-footer {
  text-align: right;
  padding: 10px 20px;
}
.dlg-lvselect--wrap .dlg-inner .dlg-footer .sp-btn {
  padding: 5px 10px;
  border-radius: 3px;
  background: #F5F7FA;
  border: 1px solid #CED3D9;
  display: inline-block;
  text-decoration: none;
  margin-left: 10px;
  cursor: pointer;
}
.dlg-lvselect--wrap .dlg-inner .dlg-footer .sp-btn:hover {
  background: #EDF0F2;
}
.dlg-lvselect--wrap .dlg-inner .dlg-footer .sp-btn.btn-blue {
  color: #fff;
  background: #09f;
  border-color: #09f;
}
.dlg-lvselect--wrap .dlg-inner .dlg-footer .sp-btn.btn-blue:hover {
  background: #0091f2;
  border-color: #0091f2;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap {
  display: flex;
  border: 1px solid #ced3d9;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-loading {
  padding: 200px 0;
  width: 100%;
  text-align: center;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list {
  width: 33%;
  max-height: 400px;
  overflow-y: auto;
  border-right: 1px solid #ced3d9;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list:nth-child(3) .sp-item .item-txt {
  cursor: default;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list:last-child {
  border-right: 0;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list:first-child {
  padding-left: 0;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-input-price {
  margin-bottom: 10px;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-input-price input[type="text"] {
  border: 1px solid #ced3d9;
  padding: 7px 10px;
  width: 80px;
  border-radius: 3px;
  outline: none;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-input-price input[type="text"]:hover {
  border-color: #b6babf;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-input-price input[type="text"]:focus {
  border-color: #09f;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item {
  padding: 0 15px;
  display: flex;
  align-items: center;
  margin: 10px 0;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item:hover {
  background: #f5f7fa;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item.active {
  background: #e5f5ff;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt {
  flex: 1;
  height: 31px;
  line-height: 31px;
  position: relative;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt label {
  display: flex;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt .item-txt-span {
  flex: 1;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt .input-cb-place {
  width: 16px;
  height: 16px;
  border: 1px solid #ced3d9;
  border-radius: 2px;
  display: inline-block;
  margin-right: 10px;
  margin-top: 1px;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt input[type="checkbox"] {
  width: 0;
  height: 0;
  position: absolute;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt input[type="checkbox"]:checked + .input-cb-place {
  border-color: #09f;
  position: relative;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt input[type="checkbox"]:checked + .input-cb-place:before {
  content: '';
  display: block;
  width: 8px;
  height: 4px;
  border-left: 2px solid #09f;
  border-bottom: 2px solid #09f;
  transform: rotate(-45deg);
  position: absolute;
  top: 4px;
  left: 3px;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt input[type="checkbox"]:disabled + .input-cb-place {
  border-color: #edf0f2;
  background: #f5f7fa;
  cursor: not-allowed;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt input[type="checkbox"]:disabled + .input-cb-place:before {
  border-left-color: #ced3d9;
  border-bottom-color: #ced3d9;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .item-txt:hover .input-cb-place {
  border-color: #b6babf;
}
.dlg-lvselect--wrap .dlg-inner .sp-wrap .sp-list .sp-item .sp-input-price {
  width: 80px;
  margin-left: 10px;
  margin-bottom: 0;
}
.sp--info {
  position: relative;
}
.sp--info .icon-info2 {
  background: none;
  color: #09f;
  cursor: pointer;
}
.sp--info .sp-prev-wrap {
  position: absolute;
  white-space: nowrap;
  right: 0;
  top: 18px;
  width: 250px;
  background: #fff;
  z-index: 1;
  padding: 10px;
  box-shadow: 3px 3px 10px rgba(0, 0, 0, 0.1);
  display: none;
  max-height: 300px;
  overflow-y: auto;
}
.sp--info:hover .sp-prev-wrap {
  display: block;
}
