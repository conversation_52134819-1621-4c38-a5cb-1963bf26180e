package com.vedeng.order.model;

import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <b>Description:</b><br> 采购商品
 * <AUTHOR>
 * @Note
 * <b>ProjectName:</b> erp
 * <br><b>PackageName:</b> com.vedeng.order.model
 * <br><b>ClassName:</b> BuyorderGoods
 * <br><b>Date:</b> 2017年7月11日 上午9:05:48
 */
@ToString
public class BuyorderGoods implements Serializable{
    /**
	 * @Fields serialVersionUID : TODO
	 */
	private static final long serialVersionUID = 1L;

	private Integer buyorderGoodsId;

    private Integer buyorderId;

    private Integer goodsId;

    private String sku;

    private String goodsName;

    private String brandName;

    private String model;

    private String unitName;

    /**
     * 规格
     */
    private String spec;

    /**
     * 注册证号/备案凭证号
     */
    private String registrationNumber;

    /**
     * 生产企业名称
     */

    private String manufacturerName;

    private BigDecimal price;

    private BigDecimal originalPurchasePrice;

    private String couponReason;

    private Integer currencyUnitId;

    private Integer num;

    private Integer arrivalNum;

    private Long estimateDeliveryTime;

    private Long estimateArrivalTime;

    private Integer arrivalUserId;

    private Integer arrivalStatus;

    private Long arrivalTime;

    private Integer isDelete;

    private String insideComments;

    private Long addTime;

    private Integer creator;

    private Long modTime;

    private Integer updater;

    private BigDecimal totalAmount;//订单总额

    private String manufacturer;//生产厂家

    private String productionLicense;//生产许可证号

    private String deliveryCycle;//DELIVERY_CYCLE 货期

    private String installation;//INSTALLATION 安调信息

    private String goodsComments;//COMMENTS 内部备注

    private Long searchBegintime;//搜索开始时间

    private Long searchEndtime;//搜索结束时间
    //售后金额
    private BigDecimal afterReturnAmount;
    //售后退货数量
    private Integer afterReturnNum;
    //实际录票金额
    private BigDecimal realInvoiceAmount;
    //实际录票数量
    private BigDecimal realInvoiceNum;

    private Integer expressId;

    /**
     * 是否风控 0否  1是
     */
    private Integer isRisk;
    /**
     * 风控内容
     */
    private String riskComments;

    private Integer productAudit;

    /**
     * 记录采购预计发货日&采购预计到货日 Start
     */
    // 采购预计发货日
    private Long sendGoodsTime;
    // 采购预计到货日
    private Long receiveGoodsTime;

    private String productBelongIdInfo;

    private String productBelongNameInfo;

    //是否直发
    private Integer deliveryDirect;

    private String buyorderNo;
    /**生效时间**/
    private Long validTime;

    /**
     * 是否有授权，0否1是
     */
    private Integer isHaveAuth;

    private Integer isGift;

    private BigDecimal referPrice;

    /**
     * 返利单价
     */
    private BigDecimal rebatePrice;

    /**
     * 返利后单价
     */
    private BigDecimal rebateAfterPrice;

    /**
     * 返利数量
     */
    private BigDecimal rebateNum;

    /**
     * 返利总额
     */
    private BigDecimal rebateAmount;

    /**
     * 实际采购价
     */
    private BigDecimal actualPurchasePrice;

    public BigDecimal getActualPurchasePrice() {
        return actualPurchasePrice;
    }

    public void setActualPurchasePrice(BigDecimal actualPurchasePrice) {
        this.actualPurchasePrice = actualPurchasePrice;
    }

    public BigDecimal getRebateAfterPrice() {
        return rebateAfterPrice;
    }

    public void setRebateAfterPrice(BigDecimal rebateAfterPrice) {
        this.rebateAfterPrice = rebateAfterPrice;
    }

    public BigDecimal getRebatePrice() {
        return rebatePrice;
    }

    public void setRebatePrice(BigDecimal rebatePrice) {
        this.rebatePrice = rebatePrice;
    }

    public BigDecimal getRebateNum() {
        return rebateNum;
    }

    public void setRebateNum(BigDecimal rebateNum) {
        this.rebateNum = rebateNum;
    }

    public BigDecimal getRebateAmount() {
        return rebateAmount;
    }

    public void setRebateAmount(BigDecimal rebateAmount) {
        this.rebateAmount = rebateAmount;
    }

    public BigDecimal getReferPrice() {
        return referPrice;
    }

    public void setReferPrice(BigDecimal referPrice) {
        this.referPrice = referPrice;
    }

    public Integer getIsGift() {
        return isGift;
    }

    public void setIsGift(Integer isGift) {
        this.isGift = isGift;
    }

    public Integer getIsHaveAuth() {
        return isHaveAuth;
    }

    public void setIsHaveAuth(Integer isHaveAuth) {
        this.isHaveAuth = isHaveAuth;
    }

    public Integer getExpressId() {
        return expressId;
    }

    public void setExpressId(Integer expressId) {
        this.expressId = expressId;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public String getManufacturerName() {
        return manufacturerName;
    }

    public void setManufacturerName(String manufacturerName) {
        this.manufacturerName = manufacturerName;
    }

    public String getBuyorderNo() {
        return buyorderNo;
    }

    public void setBuyorderNo(String buyorderNo) {
        this.buyorderNo = buyorderNo;
    }

    public Integer getDeliveryDirect() {
        return deliveryDirect;
    }

    public void setDeliveryDirect(Integer deliveryDirect) {
        this.deliveryDirect = deliveryDirect;
    }

    public Long getSendGoodsTime() {
        return sendGoodsTime;
    }

    public void setSendGoodsTime(Long sendGoodsTime) {
        this.sendGoodsTime = sendGoodsTime;
    }

    public Long getReceiveGoodsTime() {
        return receiveGoodsTime;
    }

    public void setReceiveGoodsTime(Long receiveGoodsTime) {
        this.receiveGoodsTime = receiveGoodsTime;
    }

    /**
     * End
     */

    public Integer getIsRisk() {
        return isRisk;
    }

    public void setIsRisk(Integer isRisk) {
        this.isRisk = isRisk;
    }

    public String getRiskComments() {
        return riskComments;
    }

    public void setRiskComments(String riskComments) {
        this.riskComments = riskComments;
    }

    public BigDecimal getAfterReturnAmount() {
        return afterReturnAmount;
    }

    public void setAfterReturnAmount(BigDecimal afterReturnAmount) {
        this.afterReturnAmount = afterReturnAmount;
    }

    public Integer getAfterReturnNum() {
        return afterReturnNum;
    }

    public void setAfterReturnNum(Integer afterReturnNum) {
        this.afterReturnNum = afterReturnNum;
    }

    public BigDecimal getRealInvoiceAmount() {
        return realInvoiceAmount;
    }

    public void setRealInvoiceAmount(BigDecimal realInvoiceAmount) {
        this.realInvoiceAmount = realInvoiceAmount;
    }

    public BigDecimal getRealInvoiceNum() {
        return realInvoiceNum;
    }

    public void setRealInvoiceNum(BigDecimal realInvoiceNum) {
        this.realInvoiceNum = realInvoiceNum;
    }

    public Long getSearchBegintime() {
        return searchBegintime;
    }

    public void setSearchBegintime(Long searchBegintime) {
        this.searchBegintime = searchBegintime;
    }

    public Long getSearchEndtime() {
        return searchEndtime;
    }

    public void setSearchEndtime(Long searchEndtime) {
        this.searchEndtime = searchEndtime;
    }

    public String getDeliveryCycle() {
		return deliveryCycle;
	}

	public void setDeliveryCycle(String deliveryCycle) {
		this.deliveryCycle = deliveryCycle;
	}

    public String getInstallation() {
		return installation;
	}

	public void setInstallation(String installation) {
		this.installation = installation;
	}

	public String getGoodsComments() {
		return goodsComments;
	}

	public void setGoodsComments(String goodsComments) {
		this.goodsComments = goodsComments;
	}

	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	public Integer getBuyorderGoodsId() {
        return buyorderGoodsId;
    }

    public void setBuyorderGoodsId(Integer buyorderGoodsId) {
        this.buyorderGoodsId = buyorderGoodsId;
    }

    public Integer getBuyorderId() {
        return buyorderId;
    }

    public void setBuyorderId(Integer buyorderId) {
        this.buyorderId = buyorderId;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku == null ? null : sku.trim();
    }

    public String getGoodsName() {
        return goodsName;
    }

    public void setGoodsName(String goodsName) {
        this.goodsName = goodsName == null ? null : goodsName.trim();
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName == null ? null : brandName.trim();
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model == null ? null : model.trim();
    }

    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName == null ? null : unitName.trim();
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getCurrencyUnitId() {
        return currencyUnitId;
    }

    public void setCurrencyUnitId(Integer currencyUnitId) {
        this.currencyUnitId = currencyUnitId;
    }

    public Integer getNum() {
        return num;
    }

    public void setNum(Integer num) {
        this.num = num;
    }

    public Integer getArrivalNum() {
        return arrivalNum;
    }

    public void setArrivalNum(Integer arrivalNum) {
        this.arrivalNum = arrivalNum;
    }

    public Long getEstimateDeliveryTime() {
        return estimateDeliveryTime;
    }

    public void setEstimateDeliveryTime(Long estimateDeliveryTime) {
        this.estimateDeliveryTime = estimateDeliveryTime;
    }

    public Long getEstimateArrivalTime() {
        return estimateArrivalTime;
    }

    public void setEstimateArrivalTime(Long estimateArrivalTime) {
        this.estimateArrivalTime = estimateArrivalTime;
    }

    public Integer getArrivalUserId() {
        return arrivalUserId;
    }

    public void setArrivalUserId(Integer arrivalUserId) {
        this.arrivalUserId = arrivalUserId;
    }

    public Integer getArrivalStatus() {
        return arrivalStatus;
    }

    public void setArrivalStatus(Integer arrivalStatus) {
        this.arrivalStatus = arrivalStatus;
    }

    public Long getArrivalTime() {
        return arrivalTime;
    }

    public void setArrivalTime(Long arrivalTime) {
        this.arrivalTime = arrivalTime;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getInsideComments() {
        return insideComments;
    }

    public void setInsideComments(String insideComments) {
        this.insideComments = insideComments == null ? null : insideComments.trim();
    }

    public Long getAddTime() {
        return addTime;
    }

    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    public Integer getCreator() {
        return creator;
    }

    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    public Long getModTime() {
        return modTime;
    }

    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    public Integer getUpdater() {
        return updater;
    }

    public void setUpdater(Integer updater) {
        this.updater = updater;
    }

    public String getManufacturer() {
	return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
	this.manufacturer = manufacturer == null ? null : manufacturer.replaceAll("\\(","（").replaceAll("\\)","）").trim();
    }

    public String getProductionLicense() {
	return productionLicense;
    }

    public void setProductionLicense(String productionLicense) {
	this.productionLicense = productionLicense;
    }

    public BigDecimal getOriginalPurchasePrice() {
        return originalPurchasePrice;
    }

    public void setOriginalPurchasePrice(BigDecimal originalPurchasePrice) {
        this.originalPurchasePrice = originalPurchasePrice;
    }

    public String getCouponReason() {
        return couponReason;
    }

    public void setCouponReason(String couponReason) {
        this.couponReason = couponReason;
    }

    public Integer getProductAudit() {
        return productAudit;
    }

    public void setProductAudit(Integer productAudit) {
        this.productAudit = productAudit;
    }

    public String getProductBelongIdInfo() {
        return productBelongIdInfo;
    }

    public void setProductBelongIdInfo(String productBelongIdInfo) {
        this.productBelongIdInfo = productBelongIdInfo;
    }

    public String getProductBelongNameInfo() {
        return productBelongNameInfo;
    }

    public void setProductBelongNameInfo(String productBelongNameInfo) {
        this.productBelongNameInfo = productBelongNameInfo;
    }

    public Long getValidTime() {
        return validTime;
    }

    public void setValidTime(Long validTime) {
        this.validTime = validTime;
    }
}
