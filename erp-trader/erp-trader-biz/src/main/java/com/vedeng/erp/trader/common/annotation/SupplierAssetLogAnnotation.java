package com.vedeng.erp.trader.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商资产变化日志
 * @date 2023/11/23 13:49
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface SupplierAssetLogAnnotation {

    /**
     * 资产变化类型
     */
    int type();
}
