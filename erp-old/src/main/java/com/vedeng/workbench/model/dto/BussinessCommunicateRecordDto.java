package com.vedeng.workbench.model.dto;


import lombok.Data;

import java.util.Date;

//import java.sql.Date;

/**
* .
* @jira:
* @notes: 沟通记录Vo
* @version: 1.0.
* @date: 2020/10/30 17:13.
* @author: <PERSON><PERSON>.
*/

@Data
public class BussinessCommunicateRecordDto {

    /**
     * 沟通记录id
     */
    private Integer communicateRecordId;

    /*
     * 下一次沟通内容
     */
    private String nextContactContent;

    /**
     * 下次沟通时间
     */
    private Date nextContactDate;

    /**
     * 沟通结束时间
     */
    private long endTime;

    /**
     * 沟通结束日期
     */
    private Date endDate;



//    /**
//     * 下次沟通时间
//     */
//    private Timestamp nextContactDate;

}
