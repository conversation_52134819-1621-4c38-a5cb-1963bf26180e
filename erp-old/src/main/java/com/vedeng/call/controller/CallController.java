package com.vedeng.call.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.*;
import com.vedeng.call.model.CallFailed;
import com.vedeng.call.model.CallOut;
import com.vedeng.call.model.PhonePrefixInfo;
import com.vedeng.call.service.CallService;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.redis.RedisUtils;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.BrowserUtil;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.market.api.MarketPlanApiService;
import com.vedeng.erp.trader.api.CommunicateVoiceTaskApi;
import com.vedeng.erp.trader.constant.AiConstant;
import com.vedeng.erp.trader.dto.CommunicateVoiceAiLogApiDto;
import com.vedeng.erp.trader.dto.CommunicateVoiceTaskDto;
import com.vedeng.erp.trader.dto.TraderCustomerDto;
import com.vedeng.erp.trader.dto.VoiceFieldResultDto;
import com.vedeng.erp.trader.service.BusinessLeadsApiService;
import com.vedeng.erp.trader.service.TraderCustomerApiService;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.vo.BussinessChanceVo;
import com.vedeng.order.service.BussinessChanceService;
import com.vedeng.system.model.QCellCore;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.model.Tag;
import com.vedeng.system.service.*;
import com.vedeng.trader.enums.LandLineCodeEnum;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.dto.CallingLineDto;
import com.vedeng.trader.model.dto.TraderBaseInfoDto;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import com.vedeng.trader.service.CommunicateService;
import com.vedeng.trader.service.LandLineRecordService;
import com.vedeng.trader.service.TraderCustomerService;
import net.sf.json.JSONObject;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <b>Description:</b><br>
 * 呼叫中心控制器
 *
 * <AUTHOR>
 * @Note <b>ProjectName:</b> erp <br>
 *       <b>PackageName:</b> com.vedeng.callcenter.controller <br>
 *       <b>ClassName:</b> CallCenterController <br>
 *       <b>Date:</b> 2017年4月25日 上午9:43:39
 */
@Controller
@RequestMapping("/system/call")
public class CallController extends BaseController {

    @Autowired
    @Qualifier("callService")
    private CallService callService;
    @Autowired
    @Qualifier("traderCustomerService")
    private TraderCustomerService traderCustomerService;
    @Autowired
    @Qualifier("tagService")
    private TagService tagService;
    @Autowired
    @Qualifier("communicateService")
    private CommunicateService communicateService;

    @Autowired
    @Qualifier("regionService")
    private RegionService regionService;// 自动注入regionService

    @Autowired
    @Qualifier("userService")
    private UserService userService;

    @Autowired
    @Qualifier("bussinessChanceService")
    private BussinessChanceService bussinessChanceService;

    @Autowired
    private BusinessLeadsApiService businessLeadsApiService;


    @Autowired
    @Qualifier("positService")
    private PositService positService;

    @Autowired
    @Qualifier("orgService")
    private OrgService orgService;// 自动注入orgService

    @Autowired
    private LandLineRecordService landLineRecordService;

    @Autowired
    private MarketPlanApiService marketPlanApiService;

    @Autowired
    private RoleService roleService;

    @Value("${close_communicate_roleIds}")
    private String closeCommunicateRoleIds;

    @Value("${phone_prefix_info}")
    private String phonePrefixInfo;

    @Value("${phone_prefix_array}")
    private String phonePrefixArray;

    int maxCallNum=80;

    @Autowired
    private RedisUtils redisUtils;

    @Value("${ccWsServer}")
    private String ccWsServer;

    @Value("${ccUpgradeSwitch}")
    private String ccUpgradeSwitch;

    @Value("${voice.minCoidLength:30}")
    private Integer minCoidLength;

    @Value("${voice.maxCoidLength:600}")
    private Integer maxCoidLength;


    /**
     * CC呼出标记信息
     */
    private static final String COMMUNICATE_CALL_FLAG = "CALLOUT_COMMUNICATE";
    /**
     * 沟通记录信息缓存时间
     */
    private static final int COMMUNICATE_CATCH_SECONDS = 86400;

    //科研购事业部
    private static final Integer SCIENCE_ORGID = 36;
    //科研购所属COMPANY_ID
    private static final Integer SCIENCE_COMPANY_ID = 1;


    /**
     * <b>Description:</b><br>
     * 呼叫中心初始化
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年4月25日 上午9:43:41
     */
    @RequestMapping(value = "/index")
    public ModelAndView index(HttpSession session, HttpServletRequest request) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        if(user==null){
            return new ModelAndView("redirect:/login.do");
        }
        ModelAndView mv = new ModelAndView();
        User userInfo = userService.getUserByUserId(user.getUserId());
        if (StringUtil.isNotBlank(userInfo.getTelecomLine())) {
            mv.addObject("telecomLine", userInfo.getTelecomLine());
        }else{
            mv.addObject("telecomLine", "");
        }

        HashMap callParams = callService.callInit();

        //访问浏览器是52版本Firefox，并且CallCenter升级开关已关闭，那么跳转到老的CallCenter界面；否则跳转到新的CallCenter界面
        if (callService.getCallCenterVersionByBrowserInfo(request) == 0){
            mv.addObject("ccUpgradeSwitch",ccUpgradeSwitch);
            mv.setViewName("call/index");
        } else {
            mv.setViewName("call/index1");
        }

        try {
            mv.addObject("user", user);
            mv.addObject("callParams", callParams);
            mv.addObject("ccWsServer",ccWsServer);

            //主管以上拥有监听功能agentType = 1;
            Integer agentType = 0;
            if (user.getPositLevel()!=null&&(user.getPositLevel().equals(442)
                    || user.getPositLevel().equals(443)
                    || user.getPositLevel().equals(447)
                    || user.getPositLevel().equals(448)
                    || user.getPositLevel().equals(454))
            ) {
                agentType = 1;
            }

            int txtSK = 1002;
            if (user.getPositType()!=null&&user.getPositType().equals(645)) {//售前
                txtSK = 1001;
            }

            mv.addObject("agentType", agentType);
            mv.addObject("txtSK", txtSK);
            String typevalue = getTtNumberType(user.getUserId());
            mv.addObject("mycall", NumberUtils.toInt(typevalue,0));

            mv.addObject("ttNumber", user.getTtNumber());

            if (StringUtils.isNotBlank(user.getTtNumber())) {
                Integer countTtNumber = communicateService.countTtNumberCountToday(user.getTtNumber());
                mv.addObject("ttNumberCount", maxCallNum - countTtNumber);
            }

            //是否可关闭沟通记录弹窗
            mv.addObject("canCloseCommunicate",
                    Collections.disjoint(JSON.parseArray(closeCommunicateRoleIds, Integer.class),
                            roleService.getUserRoles(user).stream()
                                    .map(Role::getRoleId)
                                    .collect(Collectors.toList())));

            mv.addObject("newCallCenterFlag", isNewCallCenterFlag(user));

        }catch (Exception e){
            logger.error("",e);
        }
        return mv;
    }

    @ResponseBody
    @RequestMapping(value = "/static/changeMyCall")
    public String changeMyCall(HttpServletRequest request,HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();
        String type=request.getParameter("type");//0 默认 1 号码池
        if(user!=null){
            //user.setUseTtNumber(NumberUtils.toInt(type));
            JedisUtils.set("CALL_CENTER_USER_CALL_TYPE_"+user.getUserId(),type, COMMUNICATE_CATCH_SECONDS);
        }
        return "OK";
    }
    @ResponseBody
    @RequestMapping(value = "/static/getTtNumber")
    public ResultInfo getTtNumber(HttpServletRequest request,HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();
        String typekey="CALL_CENTER_USER_CALL_TYPE_"+user.getUserId();
//        String typevalue=StringUtils.isBlank(JedisUtils.get(typekey))?"0":JedisUtils.get(typekey);
        String typevalue=getTtNumberType(user.getUserId());
        JedisUtils.set(typekey,typevalue, COMMUNICATE_CATCH_SECONDS);
        Map<String,Object> map=new HashMap<>();
        map.put("typevalue",typevalue);
        if(StringUtils.isNotBlank(user.getTtNumber())){
            Integer countTtNumber= communicateService.countTtNumberCountToday(user.getTtNumber());
            if(countTtNumber<maxCallNum){
                map.put("leftCount",maxCallNum-countTtNumber);
                map.put("ttNumber",user.getTtNumber());
                return ResultInfo.success(map);
            }
        }
        logger.info("getTtNumber");
        return ResultInfo.error();
    }


    @RequestMapping("saveAgentList")
    @NoNeedAccessAuthorization
    @ResponseBody
    public void saveAgentList(String agentOn, String agentLeave, HttpSession session){
        Map<String,String> map = new HashMap<>(2);
        map.put("agentOn",agentOn);
        map.put("agentLeave",agentLeave);
        String agentListKey = "agentList:"+session.getId();
        redisUtils.hset(agentListKey,map);
        redisUtils.expire(agentListKey,3600);
    }


    /**
     * <b>Description:</b><br>
     * 刷新坐席列表
     *
     * @param type
     *            弹出层类型1转接 2内部咨询
     * @param agentState
     *            模式
     * @param agentOn
     *            坐席（在线）
     * @param agentLeave
     *            分机（离线）
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月10日 下午1:12:52
     */
    @RequestMapping(value = "/getagentlist")
    public ModelAndView getAgentList(Integer type, Integer agentState, String agentOn, String agentLeave,HttpSession session, HttpServletRequest request) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        List<User> nextAllUserList = null;
        if(agentState == 16){
            //获取自己名下的用户
            //nextAllUserList = userService.getNextAllUserList(user.getUserId(), user.getCompanyId(), true, user.getPositLevel(), 1);
            nextAllUserList = userService.getMyUserList(user, null, false);
        }
        ModelAndView mv = new ModelAndView();

        if (callService.getCallCenterVersionByBrowserInfo(request) == 0){
            mv.setViewName("call/agent_list");
        } else {
            mv.setViewName("call/agent_list1");
        }

        // 在线空闲坐席列表
        List agentOnList = new ArrayList<>();

        if (StringUtils.isBlank(agentOn) && StringUtil.isBlank(agentLeave)){
            //如果agentOn和agentLeave都是空字符串，则从redis中获取
            String agentListKey = "agentList:"+session.getId();
            agentOn = redisUtils.hget(agentListKey,"agentOn");
            agentLeave = redisUtils.hget(agentListKey,"agentLeave");
        }

        JSONObject jsonObject = JSONObject.fromObject(agentOn);
        Iterator iterator = jsonObject.keys();
        while (iterator.hasNext()) {
            String key = (String) iterator.next();
            String value = jsonObject.getString(key);
            JSONObject jsonArray = JSONObject.fromObject(value);

            //判断是否自己名下的用户
            HashMap agent = new HashMap<>();
            if(agentState == 16){
                for(User u : nextAllUserList){
                    if(jsonArray.get("INS").toString().equals(u.getCcNumber())){
                        agent.put("INS", jsonArray.get("INS"));
                        agent.put("AGENTNAME", jsonArray.get("AGENTNAME"));
                        agent.put("ST", jsonArray.get("ST"));
                        agent.put("WKMD", jsonArray.get("WKMD"));
                        agentOnList.add(agent);
                    }
                }
            }else{
                agent.put("INS", jsonArray.get("INS"));
                agent.put("AGENTNAME", jsonArray.get("AGENTNAME"));
                agent.put("ST", jsonArray.get("ST"));
                agent.put("WKMD", jsonArray.get("WKMD"));
                agentOnList.add(agent);
            }

        }

        // 离席分机列表
        List agentLeaveList = new ArrayList<>();
        JSONObject jsonObject2 = JSONObject.fromObject(agentLeave);
        Iterator iterator2 = jsonObject2.keys();
        while (iterator2.hasNext()) {
            String key = (String) iterator2.next();
            String value = jsonObject2.getString(key);
            JSONObject jsonArray = JSONObject.fromObject(value);
            HashMap agent = new HashMap<>();
            agent.put("INS", jsonArray.get("INS"));
            agentLeaveList.add(agent);
        }

        // 支持部门
        List deptPhone = new ArrayList<>();
        HashMap deptMap = new HashMap<>();
        deptMap.put("call", "068538253");
        deptMap.put("phone", "68538253");
        deptMap.put("name", "公司固话");
        deptPhone.add(deptMap);

        mv.addObject("type", type);
        mv.addObject("agentState", agentState);
        mv.addObject("agentOn", agentOnList);
        mv.addObject("agentLeave", agentLeaveList);
        mv.addObject("deptPhone", deptPhone);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 电话呼出号码处理，座机：1.默认本地一个0    2.电信线路本地号码加1
     *                 手机：1.本地电信加1，其他加0   2.外地电信加10，其他加00
     *
     * @param phone
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月11日 上午8:56:31
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/getphoneinfo")
    public ResultInfo getPhoneInfo(String phone,HttpServletRequest request,Integer lineCode) {
        User sessionUser=getSessionUser(request);
        ResultInfo resultInfo = new ResultInfo<>();
        if(sessionUser==null){
            resultInfo.setCode(403);
            resultInfo.setMessage("登录已经过期，无权限");
            resultInfo.setData(null);
            return resultInfo;
        }
        logger.info("getphoneinfo -{}-",phone);
        //统一正则处理
        //phone=phone.replaceAll(  "-| ","");
        phone=phone.replaceAll(  "^025","");

        phone=StringUtil.trimToDigit(phone);//phoneResult.toString();

        if(StringUtils.isBlank(phone) ){
            resultInfo.setCode(403);
            resultInfo.setMessage("呼出号码不能为空");
            resultInfo.setData(null);
            return resultInfo;
        }
        String typekey="CALL_CENTER_USER_CALL_TYPE_"+sessionUser.getUserId();
        String typevalue=getTtNumberType(sessionUser.getUserId());
        JedisUtils.set(typekey,typevalue, COMMUNICATE_CATCH_SECONDS);

        logger.info("新呼出中心呼出电话类型 phone:{}, typevalue:{}", phone, typevalue);
        List<Integer> childrenOrg = orgService.getChildrenByParentId(10,1);
        //医修帮
        if(childrenOrg.contains(sessionUser.getOrgId())){
            resultInfo.setCode(0);
            String outPhone = "";
            if (StringUtil.matchPhone(phone) ) {
                // 电话
                String phoneStr = phone.substring(0, 7);
                QCellCore phoneInfo = callService.getQCellCoreByPhone(phoneStr);
                if (phoneInfo == null) {
                    phoneInfo = callService.saveQCellCoreInfoByQueryAliApi(phone);
                }
                if (null != phoneInfo) {
                    if (StringUtils.equals(phoneInfo.getCode(),"025") ) {
                        outPhone = "7" + phone;// 本地一个0
                    } else {
                        outPhone = "70" + phone;// 手机外地的加两个0
                    }
                } else {
                    outPhone = phone;
                }

            } else {
                outPhone = "7" + phone;// 座机统一前面加一个0
            }
            resultInfo.setData(  outPhone);
            resultInfo.setMessage("操作成功");
            return resultInfo;
        }
        switch (typevalue){
            //号码池
            case "1":
                resultInfo.setCode(0);
                resultInfo.setData("9" + phone);
                resultInfo.setMessage("操作成功");
                return resultInfo;
            //主叫号码
            case "2":
                String ttNumber=sessionUser.getTtNumber();
                try {
                    Optional<PhonePrefixInfo> optional = com.alibaba.fastjson.JSONObject.parseArray(phonePrefixInfo,PhonePrefixInfo.class)
                            .stream().filter(item -> item.getPhoneNo().equals(ttNumber)).findFirst();
                    resultInfo.setData(optional.isPresent() ? optional.get().getPrefix() + phone : phone);
                } catch (Exception e) {
                    resultInfo.setData(phone);
                    logger.error("主叫号码中号码前缀异常 phone:{}", phone, e);
                }
                logger.info("电话呼出号码处理为主叫中号码 ttNumber:{}", resultInfo.getData());
                resultInfo.setCode(0);
                resultInfo.setMessage("操作成功");
                return resultInfo;
            default: //普通拨号
                String outPhone = "";
                if (StringUtil.matchPhone(phone) ) {
                    // 电话
                    String phoneStr = phone.substring(0, 7);
                    QCellCore phoneInfo = callService.getQCellCoreByPhone(phoneStr);
                    if (phoneInfo == null) {
                        phoneInfo = callService.saveQCellCoreInfoByQueryAliApi(phone);
                    }

                    if (null != phoneInfo) {
                        if (StringUtils.equals(phoneInfo.getCode(), "025")) {
                            //电信线路本地号码加1
                            if (LandLineCodeEnum.TELECOM_LINE.getLineCode().equals(lineCode)) {
                                outPhone = "1" + phone;
                            } else {
                                // 默认本地一个0
                                outPhone = "0" + phone;
                            }
                        } else {
                            if (LandLineCodeEnum.TELECOM_LINE.getLineCode().equals(lineCode)) {
                                outPhone = "10" + phone;
                            } else {
                                // 手机外地的加两个0
                                //电信线路，不能走到此逻辑，0为联通线路
                                outPhone = "00" + phone;
                            }
                        }
                    } else {
                        outPhone = phone;
                    }

                } else {
                    //电信线路本地号码加1
                    if (LandLineCodeEnum.TELECOM_LINE.getLineCode().equals(lineCode)) {
                        outPhone = "1" + phone;
                    } else {
                        // 默认本地一个0
                        outPhone = "0" + phone;
                    }
                }
                resultInfo.setCode(0);
                resultInfo.setMessage("操作成功");
                resultInfo.setData(outPhone);
                return resultInfo;
        }
    }


    /**
     * 获取呼出主叫号码
     *
     * @param phone
     * @param request
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getCallingLine")
    public ResultInfo getCallingLine(String phone, HttpServletRequest request) {
        User user = getSessionUser(request);
        if (user == null){
            return ResultInfo.error("登录失效，请重新登录");
        }
        phone = StringUtil.trimToDigit(phone);

        if (StringUtils.isBlank(phone)){
            return ResultInfo.error("被叫号码不能为空");
        }

        CallingLineDto callingLineDto = null;
        try {
            callingLineDto = communicateService.getCalloutNumber(phone, user.getUserId());
        } catch (Exception e) {
            logger.error("getCallingLine warn phone:{},userId:{}", phone, user.getUserId());
            return ResultInfo.error(e.getMessage());
        }

        try {
            callingLineDto = communicateService.callingLineSecondCheck(callingLineDto, phone, user.getUserId());
        } catch (Exception e) {
            logger.error("座机呼出线路二次校验异常 callingLineDto:{},phone:{}", JSON.toJSONString(callingLineDto), phone, e);
            return ResultInfo.success(callingLineDto);
        }

        logger.info("getCallingLine phone:{}, callingLineDto:{}", phone, JSON.toJSONString(callingLineDto));
        return ResultInfo.success(callingLineDto);
    }

    /**
     * 标记号码池号码
     *
     * @param request
     * @param session
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/static/signPpNumber")
    public String signPpNumber(HttpServletRequest request,HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        String ppNumber=request.getParameter("ppNumber");
        if(user!=null){
            logger.info("标记号码池号码 ppNumber:{},userId:{}", ppNumber,user.getUserId());
            JedisUtils.set("CALL_CENTER_USER_PP_NUMBER_"+user.getUserId(),ppNumber, COMMUNICATE_CATCH_SECONDS);
        }
        return "OK";
    }

    /**
     * 存当前登录人且包括所有的下属
     * @param user
     * @return
     */
    public List<Integer> getUserIds(User user){
        List<Integer> subUserList = new ArrayList<>();
        List<Position> positionList = positService.getPositionByUserId(user.getUserId());
        if (positionList.size() == 1) {
            Integer positType = positionList.get(0).getType();
            List<User> myUserList = userService.getMyUserList(user, Collections.singletonList(positType), false);
            if(CollectionUtils.isNotEmpty(myUserList)){
                subUserList =myUserList.stream().map(User::getUserId).collect(Collectors.toList());
            }
        }
        return subUserList;
    }
    /**
     * <b>Description:</b><br>
     * 呼出弹屏 根据用户身份弹屏
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月11日 上午9:42:09
     */
    @ResponseBody
    @RequestMapping(value = "/getcallout")
    public ModelAndView getCallOut(CallOut callOut, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        // 去除号码中的-和空格
        String phone = callOut.getPhone();
        phone = phone.replaceAll("-", "");
        phone = phone.replaceAll(" ", "");

        callOut.setPhone(phone);
        String sphone=phone;
        if(sphone.startsWith("09")){
            sphone=sphone.substring(2);
        }
        //userIds存当前登录人且包括所有的下属
        List<Integer> subUserList = getUserIds(user);
        //页面上是否有权限点击超链接 客户对应的归属销售和归属销售上级是当前登录人时 有权限
        Boolean isClick = false;
        try {
            List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
            String phoneNumber = sphone;
            Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();

            if (optional.isPresent()){
                sphone=sphone.substring(2);
            }
        } catch (Exception e) {
            logger.error("呼出弹屏解析号码池前缀异常 sphone:{}", sphone);
        }
        try {
            String typekey = "CALL_CENTER_USER_CALL_TYPE_" + user.getUserId();
            String typevalue=getTtNumberType(user.getUserId());
            logger.info("getcallout:" + typevalue +"\t"+callOut.getPhone()+"\t"+sphone+"\t"+user.getUsername()+"\t"+user.getTtNumber());
        }catch (Exception e){
            logger.error(" ", e);
        }
        callOut.setPhone(sphone);

        QCellCore phoneInfo = new QCellCore();
//        String regEx = "1[34578]{1}\\d{9}$";
//        // 编译正则表达式
//        Pattern pattern = Pattern.compile(regEx);
//        Matcher matcher = pattern.matcher(sphone);
        if (StringUtil.matchPhone(sphone) ) {
            // 电话
            String phoneStr = sphone.substring(0, 7);
            phoneInfo = callService.getQCellCoreByPhone(phoneStr);
        } else {
            if (phone.length() == 8) {
                phoneInfo.setProvince("江苏");
                phoneInfo.setCity("南京");
            }
        }

        String traderName = "";

        Integer positType = user.getPositType();
        Integer traderType = ErpConst.ONE;// 默认客户
        //直发业务，物流部电话确认客户是否收货
        // http://jira.ivedeng.com/browse/VDERP-16932
        if (positType.equals(SysOptionConstant.ID_310) || positType.equals(SysOptionConstant.ID_313)) {// 销售或物流
            callOut.setTraderType(ErpConst.ONE);
            mv.setViewName("call/call_out");
            TraderCustomerVo traderCustomerVo = callService.getCustomerInfoByPhone(callOut);
            if(traderCustomerVo != null){
                traderName = traderCustomerVo.getName();


               Integer userId = callService.getuserIdByTraderId(traderCustomerVo.getTraderId());
               if(subUserList.contains(userId)){
                    isClick=true;
               }

                if (callOut.getTraderId().equals(0) && null != traderCustomerVo.getTraderId()) {
                    callOut.setTraderId(traderCustomerVo.getTraderId());
                }

                mv.addObject("traderCustomer", traderCustomerVo);

                // VDERP-15252
                if (traderCustomerVo.getCustomerType() == 427) {
                    mv.addObject("improveLabels", true);
                }
            }
        }
        if (positType.equals(SysOptionConstant.ID_311)) {// 采购
            traderType = ErpConst.TWO;
            callOut.setTraderType(ErpConst.TWO);
            mv.setViewName("call/call_out_supplier");
            TraderSupplierVo traderSupplierVo = callService.getSupplierInfoByPhone(callOut);
            if(traderSupplierVo!= null){

                traderName = traderSupplierVo.getTraderSupplierName();

                if (callOut.getTraderId().equals(0) && null != traderSupplierVo.getTraderId()) {
                    callOut.setTraderId(traderSupplierVo.getTraderId());
                }

                mv.addObject("traderSupplier", traderSupplierVo);
            }
        }
        if (positType.equals(SysOptionConstant.ID_312) || positType.equals(SysOptionConstant.ID_645)) {// 售后
            if (callOut.getTraderType().equals(1)) {// 客户
                callOut.setTraderType(ErpConst.ONE);
                mv.setViewName("call/call_out");
                TraderCustomerVo traderCustomerVo = callService.getCustomerInfoByPhone(callOut);

                if(null != traderCustomerVo){

                    traderName = traderCustomerVo.getName();
                    if (callOut.getTraderId().equals(0) && null != traderCustomerVo.getTraderId()) {
                        callOut.setTraderId(traderCustomerVo.getTraderId());
                    }

                    mv.addObject("traderCustomer", traderCustomerVo);
                }
            } else if (callOut.getTraderType().equals(2)) {// 供应商
                traderType = ErpConst.TWO;
                callOut.setTraderType(ErpConst.TWO);
                mv.setViewName("call/call_out_supplier");
                TraderSupplierVo traderSupplierVo = callService.getSupplierInfoByPhone(callOut);
                if(null != traderSupplierVo){

                    traderName = traderSupplierVo.getTraderSupplierName();

                    if (callOut.getTraderId().equals(0) && null != traderSupplierVo.getTraderId()) {
                        callOut.setTraderId(traderSupplierVo.getTraderId());
                    }

                    mv.addObject("traderSupplier", traderSupplierVo);
                }
            } else {
                mv.setViewName("call/call_out_service");

                callOut.setTraderType(ErpConst.ONE);
                TraderCustomerVo traderCustomerVo = callService.getCustomerInfoByPhone(callOut);
                mv.addObject("traderCustomer", traderCustomerVo);

                CallOut supplierCallout = new CallOut();
                supplierCallout = callOut;
                supplierCallout.setTraderType(ErpConst.TWO);
                TraderSupplierVo traderSupplierVo = callService.getSupplierInfoByPhone(callOut);
                mv.addObject("traderSupplier", traderSupplierVo);

                if (null != traderCustomerVo) {
                    traderName = traderCustomerVo.getName();
                    callOut.setTraderId(traderCustomerVo.getTraderId());
                } else if (null != traderSupplierVo) {
                    traderName = traderSupplierVo.getTraderSupplierName();
                    callOut.setTraderId(traderSupplierVo.getTraderId());
                }
            }
        }

        Integer callType = 0;// 呼出类型
        Integer orderId = 0;// 呼出订单ID
        Integer traderContactId = 0;// 联系人ID

        if (null != callOut.getCallType() && callOut.getCallType() > 0) {
            callType = callOut.getCallType();
            orderId = callOut.getOrderId();
        }
        if (null != callOut.getTraderContactId() && callOut.getTraderContactId() > 0) {
            traderContactId = callOut.getTraderContactId();
        }

        // 增加沟通记录
        CommunicateRecord communicateRecord = new CommunicateRecord();
        Integer communicateType = SysOptionConstant.ID_242;// 沟通类型，默认客户
        Integer relatedId = callOut.getTraderId();
        // 以是否有订单为主：呼出类型 1商机2销售订单3报价4售后5采购订单
        if (null != callOut.getCallType() && callOut.getCallType() > 0) {
            if (callOut.getCallType() == 1) {
                communicateType = SysOptionConstant.ID_244;// 商机
            }
            if (callOut.getCallType() == 2) {
                communicateType = SysOptionConstant.ID_246;// 销售订单
            }
            if (callOut.getCallType() == 3) {
                communicateType = SysOptionConstant.ID_245;// 报价
            }
            if (callOut.getCallType() == 4) {
                communicateType = SysOptionConstant.ID_248;// 售后
            }
            if (callOut.getCallType() == 5) {
                communicateType = SysOptionConstant.ID_247;// 采购订单
            }

            if (callOut.getCallType() == 6) {
                communicateType = SysOptionConstant.ID_4083;// 线索
            }

            if (callOut.getCallType() == 7) {
                communicateType = SysOptionConstant.ID_4109;// 商机线索
            }

            if (callOut.getCallType() == 8) {
                communicateType =SysOptionConstant.ID_4133;
            }

            if (callOut.getCallType() == 9) {
                communicateType =SysOptionConstant.ID_COMMNCATE_TYPE_5501;//产品推广-营销任务
            }
            if (callOut.getCallType() == 10) {
                communicateType =SysOptionConstant.ID_COMMNCATE_TYPE_5502;//客户开发-联系人入口
            }
            if (callOut.getCallType() == 11) {
                communicateType =SysOptionConstant.ID_COMMNCATE_TYPE_5503;//拜访计划-11-5503
            }

            relatedId = callOut.getOrderId();
        } else if (null != callOut.getTraderType() && callOut.getTraderType() == 2) {
            communicateType = SysOptionConstant.ID_243;// 供应商
        } else {
            communicateType = SysOptionConstant.ID_242;// 客户
        }

        Long time = DateUtil.sysTimeMillis();

        communicateRecord.setCommunicateType(communicateType);
        communicateRecord.setRelatedId(relatedId);
        communicateRecord.setPhone(callOut.getPhone());
        communicateRecord.setCoid(callOut.getCoid());
        communicateRecord.setCoidType(ErpConst.TWO);
        communicateRecord.setTraderType(traderType);
        communicateRecord.setTraderId(callOut.getTraderId());
        communicateRecord.setBegintime(time);
        communicateRecord.setEndtime(time);
        communicateRecord.setCompanyId(user.getCompanyId());
        communicateRecord.setLineCode(callOut.getLineCode());
        communicateRecord.setCallingNumber(callOut.getCallingNumber());
        String typekey="CALL_CENTER_USER_CALL_TYPE_"+user.getUserId();
        String typevalue=getTtNumberType(user.getUserId());
        if(StringUtils.isNotBlank(user.getTtNumber())&& StringUtils.equals(typevalue,"2")){
            // Integer countTtNumber= communicateService.countTtNumberCountToday(user.getTtNumber());

            communicateRecord.setTtNumber(user.getTtNumber());

        }

        if(StringUtils.equals(typevalue,"1")){
            String ppNumberKey="CALL_CENTER_USER_PP_NUMBER_" + user.getUserId();
            if (StringUtil.isNotBlank(JedisUtils.get(ppNumberKey))){
                communicateRecord.setTtNumber(JedisUtils.get(ppNumberKey));
            }
        }

        /**
         * 保存呼出电话相关信息
         */
        saveCalloutCommunicateInfo(callOut, session, user, traderType, communicateRecord, typevalue);

        mv.addObject("isClick", isClick);
        mv.addObject("phone", sphone);
        mv.addObject("phoneInfo", phoneInfo);
        // 沟通方式去电
        mv.addObject("communicateMode", SysOptionConstant.ID_250);
        mv.addObject("callType", callType);
        mv.addObject("orderId", orderId);
        mv.addObject("traderContactId", traderContactId);
        mv.addObject("positType", positType);
        mv.addObject("traderName", traderName);
        return mv;
    }

    /**
     * 保存呼出电话相关信息
     * 防止呼出与电话机接通触发了相同的事件（evt.event === ESPEvent.DialBegin）
     *
     * @param callOut
     * @param session
     * @param user
     * @param traderType
     * @param communicateRecord
     * @param typeValue
     */
    private void saveCalloutCommunicateInfo(CallOut callOut, HttpSession session, User user, Integer traderType,
                                            CommunicateRecord communicateRecord, String typeValue) {
        if (communicateService.isCommunicateSavedByCondition(communicateRecord.getCoid(), user.getUserId())){
            logger.info("通话记录已经保存，本次跳过。 communicateRecord:{}", JSON.toJSONString(communicateRecord));
            return;
        }
        logger.info("开始保存处理通话记录信息 communicateRecord:{}", JSON.toJSONString(communicateRecord));

        if(SysOptionConstant.ID_4109.equals(communicateRecord.getCommunicateType())){// 商机线索
            businessLeadsApiService.updateLeadsFollowStatus(communicateRecord.getRelatedId());
            businessLeadsApiService.updateLeadsFirstFollowTime(communicateRecord.getRelatedId());
        }
        //给总机开一个包含未接通电话的列表
        communicateService.addCallCenterCommunicate(communicateRecord, user.getUserId());

        /**
         * 沟通记录保存前置
         */
        CommunicateRecord communicateRecordInfo = new CommunicateRecord();
        BeanUtils.copyProperties(communicateRecord, communicateRecordInfo);
        setCommunicateBaseInfo(typeValue, communicateRecordInfo, user, traderType);

        /**
         * 保存座机呼出信息
         */
        if (isNewCallCenterFlag(user) && StringUtils.equals(typeValue, "0")){
            communicateRecordInfo.setTtNumber(communicateRecord.getCallingNumber());
            landLineRecordService.saveLandLineRecordByCommunicate(communicateRecord);
        }

        communicateRecordInfo.setCommunicateRecordId(null);
        communicateRecordInfo.setTraderId(null);
        communicateRecordInfo.setCommunicateMode(SysOptionConstant.ID_250);
        communicateService.addCommunicate(communicateRecordInfo, session);

        try {
            String key = COMMUNICATE_CALL_FLAG + callOut.getCoid() + user.getUserId();
            String value = communicateRecordInfo.getCommunicateRecordId().toString();

            logger.info("保存呼出通话相关ID信息 key:{},value:{}", key, value);
            JedisUtils.set(key, value, COMMUNICATE_CATCH_SECONDS);
        } catch (Exception e) {
            logger.error("保存呼出通话相关ID信息错误告警 communicateRecordInfo:{}", JSON.toJSONString(communicateRecordInfo), e);
        }
        dealBusinessChanceStatusBySaveRecord(communicateRecordInfo);
    }

    /**
     * 通话记录基础信息
     *
     * @param typevalue
     * @param communicateRecord
     * @param user
     * @param traderType
     */
    private void setCommunicateBaseInfo(String typevalue, CommunicateRecord communicateRecord, User user, Integer traderType) {
        if (StringUtils.equals(typevalue, "2")) {
            communicateRecord.setTtNumber(user.getTtNumber());
        }else if(StringUtils.equals(typevalue, "1")){
            if (StringUtil.isBlank(communicateRecord.getTtNumber())){
                communicateRecord.setTtNumber("号码池");
            }
        }else{
            communicateRecord.setTtNumber("固话");
        }
        //去除前缀
        if (StringUtils.equals(typevalue, "2")||StringUtils.equals(typevalue, "1")) {
            try {
                List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
                String phoneNumber = communicateRecord.getPhone();
                Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();
                //0915 座机会去除09，此处临时加个判断
                if (optional.isPresent()&&phoneNumber.length()>6){
                    communicateRecord.setPhone(phoneNumber.substring(2));
                }
            } catch (Exception e) {
                logger.error("呼出弹屏解析号码池前缀异常 phone:{}", communicateRecord.getPhone());
            }
        }

        communicateRecord.setPhone(StringUtil.cleanPhone(communicateRecord.getPhone()) );
        // 未选择类型
        if (communicateRecord.getCommunicateType() == 0) {
            communicateRecord.setRelatedId(communicateRecord.getTraderId());
            // 客户
            if (traderType == 1) {
                communicateRecord.setCommunicateType(SysOptionConstant.ID_242);
            }
            // 供应商
            if (traderType == 2) {
                communicateRecord.setCommunicateType(SysOptionConstant.ID_243);
            }
        }
    }

    /**
     * cc呼出保存通话记录处理商机状态信息
     *
     * @param communicateRecord
     */
    private void dealBusinessChanceStatusBySaveRecord(CommunicateRecord communicateRecord) {
        if(!SysOptionConstant.ID_244.equals(communicateRecord.getCommunicateType())){
            return;
        }
        logger.info("cc呼出保存通话记录处理商机状态信息 communicateRecord:{}", JSON.toJSONString(communicateRecord));
        try {
            BussinessChance query = new BussinessChance();
            query.setBussinessChanceId(communicateRecord.getRelatedId());
            query = bussinessChanceService.getBussinessChanceInfo(query);
            bussinessChanceService.updateBcStatusByTerm(query);
        }catch (Exception ex){
            logger.error("CC更新商机状态出错,bncId:{},exception:{}", communicateRecord.getRelatedId(),ex);
        }
    }

    @ResponseBody
    @RequestMapping(value = "/getcallin1")
    public String getCallIn1(CallOut callOut, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        session.setAttribute("A","A123");
        return JSON.toJSONString(user)+"#######################"+session.getAttribute("hashkey");

    }

    @ResponseBody
    @RequestMapping(value = "/getcallin2")
    public String getCallIn2(CallOut callOut, HttpSession session) {
        CurrentUser user = (CurrentUser) session.getAttribute(ErpConstant.CURRENT_USER);
        return JSON.toJSONString(user)+"#######################"+session.getAttribute("A");

    }


    /**
     * <b>Description:</b><br>
     * 来电弹屏
     *
     * @param callOut
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月12日 上午10:55:12
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/getcallin")
    public ModelAndView getCallIn(CallOut callOut, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        logger.info("getCallInByUser:{}",new Object[]{JSON.toJSONString(user)});
        ModelAndView mv = new ModelAndView();

        //userIds存当前登录人且包括所有的下属
        List<Integer> subUserList = getUserIds(user);
        //页面上是否有权限点击超链接 客户对应的归属销售和归属销售上级是当前登录人时 有权限
        Boolean isClick = false;
        // 去除号码中的-和空格
        String phone = callOut.getPhone();
        phone = phone.replaceAll("-", "");
        phone = phone.replaceAll(" ", "");

        QCellCore phoneInfo = new QCellCore();
        String regEx = "1[34578]{1}\\d{9}$";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(phone);
        if (matcher.matches()) {
            // 电话
            String phoneStr = phone.substring(0, 7);
            phoneInfo = callService.getQCellCoreByPhone(phoneStr);
        } else {
            if (phone.length() == 8) {
                phoneInfo.setProvince("江苏");
                phoneInfo.setCity("南京");
            }
        }

        String traderName = "";
        // 用户职位类型
        Integer positType = user.getPositType();
        Integer traderType = ErpConst.ONE;// 默认客户
        // 增加沟通记录
        CommunicateRecord communicateRecord = new CommunicateRecord();
        Integer communicateType = SysOptionConstant.ID_242;// 沟通类型，默认客户
        Integer relatedId = 0;
        if (positType.equals(SysOptionConstant.ID_310)) {// 销售
            callOut.setTraderType(ErpConst.ONE);
            mv.setViewName("call/call_in_sale");
            TraderCustomerVo traderCustomerVo = callService.getCustomerInfoByPhone(callOut);

            if (null != traderCustomerVo) {
                relatedId = traderCustomerVo.getTraderId();
                traderName = traderCustomerVo.getName();

                Integer userId = callService.getuserIdByTraderId(traderCustomerVo.getTraderId());
                if(subUserList.contains(userId)){
                    isClick=true;
                }
                // VDERP-15252
                if (traderCustomerVo.getCustomerType() == 427) {
                    mv.addObject("improveLabels", true);
                }
            }
            mv.addObject("traderCustomer", traderCustomerVo);
        }
        if (positType.equals(SysOptionConstant.ID_311)) {// 采购
            communicateType = SysOptionConstant.ID_243;// 供应商
            traderType = ErpConst.TWO;
            callOut.setTraderType(ErpConst.TWO);
            mv.setViewName("call/call_in_supplier");
            TraderSupplierVo traderSupplierVo = callService.getSupplierInfoByPhone(callOut);

            if (null != traderSupplierVo) {
                relatedId = traderSupplierVo.getTraderId();
                traderName = traderSupplierVo.getTraderSupplierName();
            }
            mv.addObject("traderSupplier", traderSupplierVo);

        }
        if (positType.equals(SysOptionConstant.ID_312) || positType.equals(SysOptionConstant.ID_645)) {// 售后
            mv.setViewName("call/call_in_service");

            callOut.setTraderType(ErpConst.ONE);
            TraderCustomerVo traderCustomerVo = callService.getCustomerInfoByPhone(callOut);
            mv.addObject("traderCustomer", traderCustomerVo);

            CallOut supplierCallout = new CallOut();
            supplierCallout = callOut;
            supplierCallout.setTraderType(ErpConst.TWO);
            TraderSupplierVo traderSupplierVo = callService.getSupplierInfoByPhone(callOut);
            mv.addObject("traderSupplier", traderSupplierVo);

            // 优先挂客户沟通记录，只有供应商信息挂供应商
            if (null != traderCustomerVo) {
                relatedId = traderCustomerVo.getTraderId();
                traderName = traderCustomerVo.getName();
            } else if (null != traderSupplierVo) {
                traderType = ErpConst.TWO;
                communicateType = SysOptionConstant.ID_243;// 供应商
                relatedId = traderSupplierVo.getTraderId();
                traderName = traderSupplierVo.getTraderSupplierName();
            }
        }

        // 增加沟通记录
        Long time = DateUtil.sysTimeMillis();
        communicateRecord.setCommunicateType(communicateType);
        communicateRecord.setRelatedId(relatedId);
        communicateRecord.setPhone(callOut.getPhone());
        communicateRecord.setCoid(callOut.getCoid());
        communicateRecord.setCoidType(ErpConst.ONE);
        communicateRecord.setTraderType(traderType);
        communicateRecord.setTraderId(relatedId);
        communicateRecord.setBegintime(time);
        communicateRecord.setEndtime(time);
        communicateRecord.setCompanyId(user.getCompanyId());

        if (isNewCallCenterFlag(user)){
            logger.info("来电弹屏保存通话记录 communicateRecord:{}", JSON.toJSONString(communicateRecord));
            landLineRecordService.saveLandLineRecordByCommunicate(communicateRecord);
        }

        communicateService.addCommunicate(communicateRecord, session);

        //工作台-精准营销活动，需要更新活动-销售任务中的是否已沟通更新为已沟通。-Kerwin VDERP-15559
        marketPlanApiService.updateMarketPlanTraderSendMsg(communicateRecord.getTraderId());

        try {
            String key = COMMUNICATE_CALL_FLAG + callOut.getCoid() + user.getUserId();
            String value = communicateRecord.getCommunicateRecordId().toString();

            logger.info("电话呼入处理保存相关业务信息 key:{},value:{}", key, value);
            JedisUtils.set(key, value, COMMUNICATE_CATCH_SECONDS);
        } catch (Exception e) {
            logger.error("电话呼入处理保存相关业务信息错误告警 communicateRecordInfo:{}", JSON.toJSONString(communicateRecord), e);
        }

        mv.addObject("isClick",isClick);
        mv.addObject("communicateMode", SysOptionConstant.ID_249);// 沟通方式来电
        mv.addObject("phone", phone);
        mv.addObject("phoneInfo", phoneInfo);
        mv.addObject("positType", positType);
        mv.addObject("traderName", traderName);
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 新增联系
     *
     * @param callOut
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月12日 上午10:57:00
     */
    @ResponseBody
    @RequestMapping(value = "/getaddcomm")
    public ModelAndView getAddComm(CallOut callOut, HttpServletRequest request, HttpSession session) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mv = new ModelAndView();

        // 客户基本信息(获取客户名称)

        Trader trader = callService.getTraderByTraderId(callOut.getTraderId());

        List<TraderContact> contactList = null;// 联系人
        Integer traderType = callOut.getTraderType();
        if (traderType.equals(1)) {// 客户

            TraderContact traderContact = new TraderContact();
            // 联系人
            traderContact.setTraderId(callOut.getTraderId());
            traderContact.setIsEnable(ErpConst.ONE);
            traderContact.setTraderType(ErpConst.ONE);
            contactList = traderCustomerService.getTraderContact(traderContact);
        }
        if (traderType.equals(2)) {// 供应商
            TraderContact traderContact = new TraderContact();
            // 联系人
            traderContact.setTraderId(callOut.getTraderId());
            traderContact.setIsEnable(ErpConst.ONE);
            traderContact.setTraderType(ErpConst.TWO);
            contactList = traderCustomerService.getTraderContact(traderContact);
        }

        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);

        // 沟通目的
        List<SysOptionDefinition> communicateGoalList = getSysOptionDefinitionList(SysOptionConstant.ID_24);
        mv.addObject("communicateGoalList", communicateGoalList);
//		if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_24)) {
//			String strJson = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_24);
//			JSONArray jsonArray = JSONArray.fromObject(strJson);
//			List<SysOptionDefinition> communicateGoalList = (List<SysOptionDefinition>) JSONArray
//					.toCollection(jsonArray, SysOptionDefinition.class);
//			mv.addObject("communicateGoalList", communicateGoalList);
//		}

        // 沟通类型
//		if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_22)) {
//			String strJson = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_22);
//			JSONArray jsonArray = JSONArray.fromObject(strJson);
//			List<SysOptionDefinition> communicateTypeList = (List<SysOptionDefinition>) JSONArray
//					.toCollection(jsonArray, SysOptionDefinition.class);
//			mv.addObject("communicateTypeList", communicateTypeList);
//		}
        mv.addObject("communicateTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_22));
        mv.setViewName("call/add_comm");
        mv.addObject("callOut", callOut);
        mv.addObject("trader", trader);
        mv.addObject("contactList", contactList);
        mv.addObject("tagList", (List<Tag>) tagMap.get("list"));
        mv.addObject("page", (Page) tagMap.get("page"));
        return mv;
    }

    /**
     * <b>Description:</b><br>
     * 根据沟通类型查询订单
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月13日 上午10:10:47
     */
    @ResponseBody
    @RequestMapping(value = "/getorderlist")
    public ResultInfo getOrderList(CommunicateRecord communicateRecord) {
        ResultInfo resultInfo = new ResultInfo<>();
        List<?> orderList = callService.getOrderList(communicateRecord);
        if (orderList != null) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setListData(orderList);
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 保存沟通联系  没有找到客户
     *
     * @param communicateRecord
     * @param request
     * @param session
     * @return
     * @throws Exception
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月13日 上午11:22:23
     */

    @ResponseBody
    @RequestMapping(value = "/saveaddcommunicate")
    @SystemControllerLog(operationType = "add",desc = "保存新增电话沟通")
    public ResultInfo saveAddCommunicate(CommunicateRecord communicateRecord, HttpServletRequest request,
                                         HttpSession session) throws Exception {
        Integer traderType = Integer.parseInt(request.getParameter("traderType"));
        Integer traderId = Integer.parseInt(request.getParameter("traderId"));
        communicateRecord.setTraderId(traderId);
        communicateRecord.setTraderType(traderType);
        User sessionUser = (User) session.getAttribute(Consts.SESSION_USER);
        String typekey="CALL_CENTER_USER_CALL_TYPE_"+sessionUser.getUserId();
        String typevalue=getTtNumberType(sessionUser.getUserId());

        setCommunicateBaseInfo(typevalue, communicateRecord, sessionUser, traderType);
        Integer callFrom = Integer.parseInt(request.getParameter("callFrom"));
        if (callFrom == 0) {// 呼入
            communicateRecord.setCoidType(ErpConst.ONE);
        }
        if (callFrom == 1) {// 呼出
            communicateRecord.setCoidType(ErpConst.TWO);
        }

        logger.info("未找到客户时保存通话记录 communicateRecord:{}", JSON.toJSONString(communicateRecord));

        if (!communicateService.isCommunicateSavedByCondition(communicateRecord.getCoid(), sessionUser.getUserId()) && isNewCallCenterFlag(sessionUser)){
            CommunicateRecord communicateForLandLine = new CommunicateRecord();
            BeanUtils.copyProperties(communicateRecord, communicateForLandLine);
            communicateForLandLine.setCreator(sessionUser.getUserId());
            communicateForLandLine.setAddTime(System.currentTimeMillis());

            logger.info("保存沟通联系  没有找到客户 communicateForLandLine:{}", JSON.toJSONString(communicateForLandLine));
            landLineRecordService.saveLandLineRecordByCommunicate(communicateForLandLine);
        }

        Boolean res = callService.saveCommunicate(communicateRecord, request, session);
        if(res){
            dealBusinessChanceStatusBySaveRecord(communicateRecord);
        }

        if (res) {
            return new ResultInfo(0, "操作成功！");
        } else {
            return new ResultInfo(1, "操作失败！");
        }
    }

    /**
     * <b>Description:</b><br>
     * 新增商机
     *
     * @param callOut
     * @param request
     * @param session
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月14日 下午3:34:50
     */
    @ResponseBody
    @RequestMapping(value = "/getaddbussinesschance")
    public ModelAndView getAddBussinessChance(CallOut callOut, HttpServletRequest request, HttpSession session) {
        ModelAndView mav = new ModelAndView();
        // 商机商品分类
//		if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_387)) {
//			String strJson = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_387);
//			JSONArray jsonArray = JSONArray.fromObject(strJson);
//			List<SysOptionDefinition> goodsTypeList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray,
//					SysOptionDefinition.class);
//
//			mav.addObject("goodsTypeList", goodsTypeList);
//		}
        mav.addObject("goodsTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_387));
        // 商机来源
//		if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_365)) {
//			String strJson = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_365);
//			JSONArray jsonArray = JSONArray.fromObject(strJson);
//			List<SysOptionDefinition> scoureList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray,
//					SysOptionDefinition.class);
//
//			mav.addObject("scoureList", scoureList);
//		}
        mav.addObject("scoureList", getSysOptionDefinitionList(SysOptionConstant.ID_365));
        // 询价方式
//		if (JedisUtils.exists(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_376)) {
//			String strJson = JedisUtils.get(dbType+ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + SysOptionConstant.ID_376);
//			JSONArray jsonArray = JSONArray.fromObject(strJson);
//			List<SysOptionDefinition> scoureList = (List<SysOptionDefinition>) JSONArray.toCollection(jsonArray,
//					SysOptionDefinition.class);
//			mav.addObject("inquiryList", scoureList);
//		}
        mav.addObject("inquiryList", getSysOptionDefinitionList(SysOptionConstant.ID_376));
        // 省级地区
        List<Region> provinceList = regionService.getRegionByParentId(1);
        mav.addObject("provinceList", provinceList);
        // 查询所有销售
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        List<User> userList = userService.getUserByPositType(SysOptionConstant.ID_310, user.getCompanyId());

        TraderCustomerVo traderCustomerVo = null;
        Integer province = 0;
        Integer city = 0;
        Integer zone = 0;
        if (null != callOut.getTraderId() && callOut.getTraderId() > 0) {
            TraderCustomer traderCustomer = new TraderCustomer();
            traderCustomer.setTraderId(callOut.getTraderId());
            traderCustomerVo = traderCustomerService.getTraderCustomerVo(traderCustomer);
            if (null != traderCustomerVo && null != traderCustomerVo.getAreaIds()) {
                String[] ids = traderCustomerVo.getAreaIds().split(",");
                if (null != ids[0]) {
                    province = Integer.parseInt(ids[0]);

                }
                if (null != ids[1]) {
                    city = Integer.parseInt(ids[1]);
                    // 市级地区
                    List<Region> cityList = regionService.getRegionByParentId(province);
                    mav.addObject("cityList", cityList);
                }
                if (null != ids[2]) {
                    zone = Integer.parseInt(ids[2]);
                    // 县区级地区
                    List<Region> zoneList = regionService.getRegionByParentId(city);
                    mav.addObject("zoneList", zoneList);
                }
            }
        }

        // 去除号码中的-和空格
        String phone = callOut.getPhone();
        phone = phone.replaceAll("-", "");
        phone = phone.replaceAll(" ", "");

        String regEx = "1[34578]{1}\\d{9}$";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(phone);
        String mobile = "";
        String tel = "";
        if (matcher.matches()) {
            mobile = phone;
        } else {
            tel = phone;
        }
        mav.addObject("traderCustomer", traderCustomerVo);
        mav.addObject("userList", userList);
        BussinessChanceVo bcv = new BussinessChanceVo();
        bcv.setReceiveTime(DateUtil.sysTimeMillis());
        mav.addObject("bussinessChanceVo", bcv);

        mav.addObject("province", province);
        mav.addObject("city", city);
        mav.addObject("zone", zone);
        mav.addObject("mobile", mobile);
        mav.addObject("tel", tel);

        mav.setViewName("call/add_bussinesschance");
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 保存商机
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月14日 下午5:19:31
     */
    @ResponseBody
    @RequestMapping(value = "/savebussinesschance")
    @SystemControllerLog(operationType = "add",desc = "保存来电新增商机")
    public ResultInfo saveBussinessChance(HttpSession session, Integer province, Integer city, Integer zone,
                                          String time, BussinessChance bussinessChance) {
        ResultInfo rs = new ResultInfo<>();
        User user = (User) session.getAttribute(ErpConst.CURR_USER);
        try {
            if (province != 0 && city != 0 && zone != 0) {
                bussinessChance.setAreaId(zone);
                bussinessChance.setAreaIds(province + "," + city + "," + zone);
            } else if (province != 0 && city != 0 && zone == 0) {
                bussinessChance.setAreaId(city);
                bussinessChance.setAreaIds(province + "," + city);
            } else if (province != 0 && city == 0 && zone == 0) {
                bussinessChance.setAreaId(province);
                bussinessChance.setAreaIds(province.toString());
            }

            if (time != null && !"".equals(time)) {
                bussinessChance.setReceiveTime(DateUtil.convertLong(time, DateUtil.TIME_FORMAT));
            }

            bussinessChance.setReceiveTime(DateUtil.convertLong(time, DateUtil.TIME_FORMAT));
            bussinessChance.setType(SysOptionConstant.ID_391);// 商机类型:总机询价
            bussinessChance.setCompanyId(user.getCompanyId());
            rs = bussinessChanceService.saveBussinessChance(bussinessChance, user, null);
            if (null != rs && rs.getCode() == 0) {
                JSONObject json = JSONObject.fromObject(rs.getData());
                BussinessChance bc = (BussinessChance) JSONObject.toBean(json, BussinessChance.class);
                bussinessChanceService.sendMessageIfMerge(bussinessChance.getBussinessChanceId(), bc);
            }
            return rs;
        } catch (Exception e) {
            logger.error("savebussinesschance:", e);
            return rs;
        }
    }

    /**
     * <b>Description:</b><br>
     * 获取最近一条商机信息
     *
     * @param callOut
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月17日 上午10:29:54
     */
    @ResponseBody
    @RequestMapping(value = "/getbussincesschance")
    public ModelAndView getBussincessChance(CallOut callOut) {
        ModelAndView mav = new ModelAndView();

        // 去除号码中的-和空格
        String phone = callOut.getPhone();
        phone = phone.replaceAll("-", "");
        phone = phone.replaceAll(" ", "");

        callOut.setPhone(phone);

        QCellCore phoneInfo = null;
        String regEx = "1[34578]{1}\\d{9}$";
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regEx);
        Matcher matcher = pattern.matcher(phone);
        if (matcher.matches()) {
            // 电话
            String phoneStr = phone.substring(0, 7);
            phoneInfo = callService.getQCellCoreByPhone(phoneStr);
        } else {
            if (phone.length() == 8) {
                phoneInfo = new QCellCore();
                phoneInfo.setProvince("江苏");
                phoneInfo.setCity("南京");
            }
        }

        BussinessChance bussinessChance = callService.getBussincessChance(callOut);

        mav.addObject("phone", phone);
        mav.addObject("phoneInfo", phoneInfo);
        mav.addObject("bussinessChance", bussinessChance);
        mav.setViewName("call/bussinesschance_info");
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 根据来电获取交易者信息
     *
     * @param callOut
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月17日 下午1:29:32
     */
    @ResponseBody
    @RequestMapping(value = "/gettraderinfo")
    public ResultInfo getTraderInfo(CallOut callOut, HttpSession session) {
        ResultInfo resultInfo = new ResultInfo<>();
        User user = (User) session.getAttribute(Consts.SESSION_USER);

        // 用户职位类型
        Integer positType = user.getPositType();
        if (positType.equals(SysOptionConstant.ID_310)) {// 销售
            callOut.setTraderType(ErpConst.ONE);
        }
        if (positType.equals(SysOptionConstant.ID_311)) {// 采购
            callOut.setTraderType(ErpConst.TWO);
        }
        if(StringUtils.isNotBlank(callOut.getPhone())&&callOut.getPhone().startsWith("09")){
            callOut.setPhone(callOut.getPhone().substring(2));
        }

        try {
            if(StringUtils.isNotBlank(callOut.getPhone())){
                List<String> phonePrefixArrayList = JSON.parseArray(phonePrefixArray, String.class);
                String phoneNumber = callOut.getPhone();
                Optional<String> optional = phonePrefixArrayList.stream().filter(item -> phoneNumber.startsWith(item)).findFirst();
                if (optional.isPresent()){
                    callOut.setPhone(callOut.getPhone().substring(2));
                }
            }
        } catch (Exception e) {
            logger.error("根据来电获取交易者信息解析号码池前缀异常 callOut:{}", JSON.toJSONString(callOut));
        }

        Trader trader = callService.getTraderByPhone(callOut);
        if (null != trader) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
            resultInfo.setData(trader);
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 话机状态
     *
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月17日 下午2:32:11
     */
    @ResponseBody
    @RequestMapping(value = "/getisinit")
    public ResultInfo getIsInit(HttpSession session) {
        ResultInfo resultInfo = new ResultInfo<>();
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        if (null != user.getUserDetail().getCcNumber()) {
            String type = callService.getIsInit(user.getNumber());
            if (null != type) {
                if (type.equals("logon")) {
                    resultInfo.setCode(0);
                    resultInfo.setMessage("操作成功");
                }
            }
        }
        return resultInfo;
    }

    /**
     * <b>Description:</b><br>
     * 话务综合统计
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月19日 下午1:53:17
     */
    @ResponseBody
    @RequestMapping(value = "/getstatistics")
    public ModelAndView getStatistics(HttpServletRequest request) {
        ModelAndView mav = new ModelAndView();
        String starttime = "";
        String endtime = "";
        if (null != request.getParameter("starttime") || null != request.getParameter("endtime")) {
            if (null != request.getParameter("starttime") && !request.getParameter("starttime").equals("")) {
                starttime = request.getParameter("starttime");
            }
            if (null != request.getParameter("endtime") && !request.getParameter("endtime").equals("")) {
                endtime = request.getParameter("endtime");
            }

            Map<String, Object> info = callService.getStatistics(starttime, endtime);

            mav.addObject("info", info);
        }
        mav.addObject("starttime", starttime);
        mav.addObject("endtime", endtime);

        mav.setViewName("call/statistics");
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 座席明细统计
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月19日 下午3:20:16
     */
    @ResponseBody
    @RequestMapping(value = "/getuserdetail")
    public ModelAndView getUserDetail(HttpServletRequest request, HttpSession session) {
        User session_user = (User) session.getAttribute(ErpConst.CURR_USER);
        ModelAndView mav = new ModelAndView();
        // 获取部门
        List<Organization> orgList = orgService.getOrgListNotDelete(0, session_user.getCompanyId(), true);
//		List<User> userList = userService.getNextAllUserList(session_user.getUserId(), session_user.getCompanyId(),
//				true, session_user.getPositLevel(), 0);
        List<User> userList = userService.getMyUserList(session_user, null, false);

        String starttime = DateUtil.convertString(DateUtil.sysTimeMillis(), "yyyy-MM-dd");
        String endtime = starttime;
        Integer orgId = 0;
        String userNumber = "all";
        if (null != request.getParameter("starttime") || null != request.getParameter("endtime")
                || null != request.getParameter("orgId") || null != request.getParameter("userId")) {
            List<String> numberList = new ArrayList<>();
            if (null != request.getParameter("starttime") && !request.getParameter("starttime").equals("")) {
                starttime = request.getParameter("starttime");
            }
            if (null != request.getParameter("endtime") && !request.getParameter("endtime").equals("")) {
                endtime = request.getParameter("endtime");
            }
            if ( StringUtils.isNotBlank(request.getParameter("orgId")) && !request.getParameter("orgId").equals("0")) {
                orgId = Integer.parseInt(request.getParameter("orgId"));
            }
            if (null != request.getParameter("userNumber") && request.getParameter("userNumber") != "all") {
                userNumber = request.getParameter("userNumber");
            }

            if (userNumber.equals("all")) {
                List<Integer> orgIds = new ArrayList<>();
                List<Organization> orgs = orgService.getOrgListNotDelete(orgId, session_user.getCompanyId(), true);
                orgIds.add(orgId);
                if (orgs.size() > 0) {
                    for (Organization o : orgs) {
                        orgIds.add(o.getOrgId());
                    }
                }

                List<User> userListByOrgIds = userService.getUserListByOrgIds(orgIds, session_user.getCompanyId());
                if (userListByOrgIds.size() > 0) {
                    for (User u : userListByOrgIds) {
                        if (null != u.getNumber() && !u.getNumber().equals("")) {
                            numberList.add(u.getNumber());
                        }
                    }

                }
            } else {
                numberList.add(userNumber);
                // numberList.add("0237");
                // numberList.add("0225");
            }

            if (numberList.size() > 0) {
                List info = callService.getUserDetail(starttime, endtime, numberList);
                mav.addObject("info", info);
            }
        }

        mav.addObject("starttime", starttime);
        mav.addObject("endtime", endtime);
        mav.addObject("orgId", orgId);
        mav.addObject("userNumber", userNumber);
        mav.addObject("orgList", orgList);
        mav.addObject("userList", userList);
        mav.setViewName("call/statistics_user");
        return mav;
    }

//    public static void main(String[] args) {
//        List<User> userList = new ArrayList<>();
//        User u1 = new User();
//        u1.setUsername("b.Wang");
//        u1.setUserId(2);
//        userList.add(u1);
//
//        User u2 = new User();
//        u2.setUsername("a.Wang");
//        u2.setUserId(1);
//        userList.add(u2);
//
//        User u3 = new User();
//        u3.setUsername("c.Wang");
//        u3.setUserId(3);
//        userList.add(u3);
//
//
//
//
//        Collections.sort(userList, new Comparator<User>() {
//            @Override
//            public int compare(User u1, User u2) {
//                return u1.getUsername().compareToIgnoreCase(u2.getUsername());
//            }
//        });
//
//        for(User u:userList){
//            System.out.println(com.alibaba.fastjson.JSONObject.toJSONString(u));
//        }
//    }

    /**
     * <b>Description:</b><br>
     * 通话记录
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月19日 下午3:23:22
     */
    @ResponseBody
    @RequestMapping(value = "/getrecord")
    public ModelAndView getRecord(HttpServletRequest request, CommunicateRecord communicateRecord,
                                  @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                  @RequestParam(required = false) Integer pageSize, HttpSession session) {
        ModelAndView mav = new ModelAndView();
        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);
        // 查询所有职位类型为310的员工
        List<User> userList = null;
        if(null != user.getPositType() && user.getPositType().equals(310)){
            List<Integer> positionType = new ArrayList<>();
            positionType.add(SysOptionConstant.ID_310);//销售
            userList = userService.getMyUserList(user, positionType, false);
            List<User> userListByOrgId = userService.getMyUserListByUserOrgsList(user,positionType,false);
            if(CollectionUtils.isNotEmpty(userListByOrgId)){

                Set<Integer> userIds = new HashSet<>();
                for (User u : userList) {
                    userIds.add(u.getUserId());
                }
                // 遍历 userListByOrgId 并添加那些 userId 不在 userIds Set 中的 User 对象
                for (User newUser : userListByOrgId) {
                    if (!userIds.contains(newUser.getUserId())) {
                        userList.add(newUser);
                        userIds.add(newUser.getUserId()); // 更新 userIds Set
                    }
                }
                // 此时 userList 包含了原始用户以及新添加的，没有重复 userId 的用户

                // 使用自定义比较器进行排序，不区分大小写
                Collections.sort(userList, new Comparator<User>() {
                    @Override
                    public int compare(User u1, User u2) {
                        return u1.getUsername().compareToIgnoreCase(u2.getUsername());
                    }
                });
            }



            mav.addObject("userList", userList);
            if(userList == null || userList.size() == 0){
                userList.add(new User());
            }
        }else{
            userList = callService.getRecordUserNew();
            mav.addObject("userList", userList);
        }

        List<Integer> userIds = new ArrayList<>();

        if(null != userList && userList.size() > 0){
            for(User u : userList){
                userIds.add(u.getUserId());
            }
        }else{
            userIds.add(-1);
        }
        communicateRecord.setUserIds(userIds);
        Page page = getPageTag(request, pageNo, pageSize);

        String begindate = request.getParameter("begindate");
        LocalDate now = LocalDate.now();
        if (null != begindate && !"".equals(begindate)) {
            communicateRecord.setBegintime(DateUtil.convertLong(begindate, "yyyy-MM-dd"));
        } else {
            //今日往前推30天
            LocalDate lastDay = now.minusDays(30);
            String lastDayFormat = lastDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            communicateRecord.setBegintime(DateUtil.convertLong(lastDayFormat, "yyyy-MM-dd"));
            communicateRecord.setBegindate(lastDayFormat);
        }
        String enddate = request.getParameter("enddate");
        if (null != enddate && !"".equals(enddate)) {
            communicateRecord.setEndtime(DateUtil.convertLong(enddate + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
        } else {
            //今日
            String firstdayFormat = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            communicateRecord.setEndtime(DateUtil.convertLong(firstdayFormat + " 23:59:59", "yyyy-MM-dd HH:mm:ss"));
            communicateRecord.setEnddate(firstdayFormat);
        }



        List<CommunicateRecord> recordList = callService.queryRecordlistPage(communicateRecord, page, session);

        mav.addObject("page", page);
        mav.addObject("communicateRecord", communicateRecord);

        mav.addObject("recordList", recordList);
        mav.setViewName("call/record_list");
        return mav;
    }


    /**
     * <b>Description:</b><br>
     * 漏接来电
     *
     * @param request
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月19日 下午3:23:22
     */
    @ResponseBody
    @RequestMapping(value = "/getcallfailed")
    public ModelAndView getCallFailed(HttpServletRequest request, CallFailed callFailed,
                                      @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                      @RequestParam(required = false) Integer pageSize, HttpSession session) {
        ModelAndView mav = new ModelAndView();

        User user = (User) session.getAttribute(Consts.SESSION_USER);
        Page page = getPageTag(request, pageNo, pageSize);

        callFailed.setCompanyId(user.getCompanyId());
        List<CallFailed> callFailedList = callService.queryCallFailedlistPage(callFailed, page);
        List<CallFailed> dialBackUserList = callService.getCallFailedDialBackUser();

        mav.addObject("dialBackUserList", dialBackUserList);
        mav.addObject("callFailedList", callFailedList);
        mav.addObject("page", page);
        mav.addObject("callFailed", callFailed);
        mav.setViewName("call/failed_list");
        return mav;
    }

    /**
     * <b>Description:</b><br>
     * 录音播放
     *
     * @param url
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月20日 下午2:14:40
     */
    @ResponseBody
    @RequestMapping(value = "/getrecordpaly")
    public ModelAndView getRecordPaly(HttpServletRequest request, String url) {
        ModelAndView mav = new ModelAndView();

        // 浏览器信息
        String agent = request.getHeader("User-Agent").toLowerCase();
        BrowserUtil browserUtil = new BrowserUtil();
        String browserName = browserUtil.getBrowserName(agent);

        Integer type = 1;
        if (browserName.indexOf("ie") != -1) {
            type = 2;
        }

        mav.setViewName("call/record_play");
        mav.addObject("url", url);
        mav.addObject("type", type);
        return mav;
    }

    static String convertToText(Integer coidLength){
        double result = (double) coidLength / 60;

        DecimalFormat df = new DecimalFormat("0.00");
        String formatted = df.format(result);

        DecimalFormat df2 = new DecimalFormat("0.#");
        String finalResult = df2.format(Double.parseDouble(formatted));
        return finalResult;
    }

    @Autowired
    private CommunicateVoiceTaskApi communicateVoiceTaskApi;

    @Value("${voice.voiceGptServerUrl:http://172.16.10.211:9709/streamChatWithWeb}")
    private String voiceGptServerUrl ;


    @Value("${voice.cacheGptStream:N}")
    private String cacheGptStream ;



    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/getrecordplayForAi")
    public ModelAndView getrecordplayForAi(HttpSession session, Integer communicateRecordId) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView mav = new ModelAndView();
        Integer type = 1;
        mav.setViewName("call/record_play_ai");
        mav.addObject("minCoidLength",minCoidLength);
        mav.addObject("maxCoidLength",maxCoidLength);
        CommunicateRecord communicateRecordQuery = new CommunicateRecord();
        communicateRecordQuery.setCommunicateRecordId(communicateRecordId);
        CommunicateRecord communicateRecord = callService.getCommunicateRecordById(communicateRecordQuery);

        String errorTipsTemplate = (user!=null?(user.getUsername()+"，"):"") + "贝壳助理目前只解析2024年5月30日之后${minCoidLength}分钟-${maxCoidLength}分钟之间的通话录音哦，贝壳将继续努力，增加分析范围。";
        String errorTips = errorTipsTemplate.replace("${minCoidLength}",convertToText(minCoidLength))
                .replace("${maxCoidLength}",convertToText(maxCoidLength));
        mav.addObject("errorTips",errorTips);
        mav.addObject("url", communicateRecord.getCoidUri());
        mav.addObject("type", type);
        mav.addObject("communicateRecordId", communicateRecordId);
        mav.addObject("title","贝壳助理语音识别-"+communicateRecordId);
        mav.addObject("voiceGptServerUrl",voiceGptServerUrl);
        mav.addObject("communicateType",AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getCode());
        return mav;
    }




    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/getrecordplayDetail")
    public ResultInfo<CommunicateVoiceTaskDto> getrecordplayDetail(HttpServletRequest request, Integer communicateRecordId) {
        CommunicateRecord communicateRecordQuery = new CommunicateRecord();
        communicateRecordQuery.setCommunicateRecordId(communicateRecordId);
        CommunicateRecord communicateRecord = callService.getCommunicateRecordById(communicateRecordQuery);
        if(communicateRecord == null){
            logger.error("通话记录不存在:"+communicateRecordId);
            return ResultInfo.error("通话记录不存在");
        }
        CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicateRecordId,
                AiConstant.getEnumByCommunicateType(communicateRecord.getCommunicateType()).getCode());
        if(taskDto == null){
            taskDto = communicateVoiceTaskApi.selectByCommunicateRecordId(communicateRecordId);
        }
        return ResultInfo.success(taskDto);

    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/getrecordplayDetailAi")
    public ResultInfo<Object> getrecordplayDetail( HttpSession session, Integer communicateRecordId,String senceCode) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        Integer userId = user!=null?user.getUserId():0;
        if("Y".equals(cacheGptStream)){
            CommunicateVoiceAiLogApiDto aiLogApiDto = communicateVoiceTaskApi.queryLastComunicateVoiceLog(communicateRecordId,senceCode,AiConstant.CODE_GROUP_SUMMARY);
            if(aiLogApiDto!=null && StringUtils.isNotBlank(aiLogApiDto.getResponseText())){//如果开启缓存的情况下，并且查到了上一次记录的结果，则将结果返回给前端，同时，复制该记录再存一次，以便记录日志；埋点使用。
                aiLogApiDto.setCreator(userId);
                aiLogApiDto.setStartTime(new Date());
                aiLogApiDto.setEndTime(new Date());
                aiLogApiDto.setAddTime(new Date());
                aiLogApiDto.setModTime(new Date());
                communicateVoiceTaskApi.saveCommunicateVoiceAiLog(aiLogApiDto);
                return ResultInfo.success(aiLogApiDto.getResponseText());
            }
        }




        CommunicateRecord communicateRecordQuery = new CommunicateRecord();
        communicateRecordQuery.setCommunicateRecordId(communicateRecordId);
        CommunicateRecord communicateRecord = callService.getCommunicateRecordById(communicateRecordQuery);
        CommunicateVoiceTaskDto taskDto = communicateVoiceTaskApi.selectByCommunicateRecordIdAndSence(communicateRecordId, senceCode);
        if(taskDto == null){
            CommunicateVoiceAiLogApiDto communicateVoiceAiLogApiDto = getCommunicateVoiceAiLogApiDto(communicateRecordId, senceCode, AiConstant.CODE_GROUP_SUMMARY, userId);
            Long logId= communicateVoiceTaskApi.saveCommunicateVoiceAiLog(communicateVoiceAiLogApiDto);
            //查询是否已存在GPT任务，不存在则发起；
            ResultInfo result = ResultInfo.error("任务不存在，需实时发起GPT会话");
            result.setData(logId);
            return result;
//            callService.sendAiRequest(communicateRecord, senceCode);//实时发起解析请求
        }
        List<VoiceFieldResultDto> resultList = communicateVoiceTaskApi.selectVoiceResultByCommunicateRecordIdAndSenceGroup(
                communicateRecordId, senceCode, AiConstant.CODE_GROUP_SUMMARY);
        if(CollectionUtils.isEmpty(resultList)){
            CommunicateVoiceAiLogApiDto communicateVoiceAiLogApiDto = getCommunicateVoiceAiLogApiDto(communicateRecordId, senceCode, AiConstant.CODE_GROUP_SUMMARY, userId);
            Long logId= communicateVoiceTaskApi.saveCommunicateVoiceAiLog(communicateVoiceAiLogApiDto);
            ResultInfo result = ResultInfo.error("任务存在但无结果，需实时发起GPT会话");
            result.setData(logId);
            return result;
        }
        StringBuffer sbr = new StringBuffer();
        int lineCount = 1;
        for(VoiceFieldResultDto resultDto:resultList){
            sbr.append(lineCount+"、")
                    .append(resultDto.getFieldName())
                    .append(":")
                    .append(StringUtils.isNotBlank(resultDto.getFieldResult())?resultDto.getFieldResult():"")
                    .append(    (StringUtils.isNotBlank(resultDto.getFieldResult()) && resultDto.getFieldResult().endsWith("。"))?"":"。")//如果是句号结束，则不添加这一段的句号了
                    .append("\n");
            lineCount++;
        }
        //转换为字段返回；
        return ResultInfo.success(sbr.toString());

    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/saveVoiceDetailAi")
    public ResultInfo<List<VoiceFieldResultDto>> getrecordplayDetail( Integer logId,String content) {
        try{
            communicateVoiceTaskApi.updateVoiceAiLog(logId,content);
            return ResultInfo.success();
        }catch (Exception e){
            log.error("保存AI解析结果失败",e);
            return ResultInfo.error("保存AI解析结果失败");
        }
    }

    /**
     * 组装参数
     * @param communicateRecordId
     * @param senceCode
     * @param groupCode
     * @param userId
     * @return
     */
    private static CommunicateVoiceAiLogApiDto getCommunicateVoiceAiLogApiDto(Integer communicateRecordId, String senceCode,String groupCode, Integer userId) {
        CommunicateVoiceAiLogApiDto communicateVoiceAiLogApiDto = new CommunicateVoiceAiLogApiDto();
        communicateVoiceAiLogApiDto.setCommunicateRecordId(communicateRecordId);
        communicateVoiceAiLogApiDto.setSenceCode(senceCode);
        communicateVoiceAiLogApiDto.setGroupCode(groupCode);
        communicateVoiceAiLogApiDto.setGptVersion("deepseek");
        communicateVoiceAiLogApiDto.setStartTime(new Date());
        communicateVoiceAiLogApiDto.setEndTime(null);
        communicateVoiceAiLogApiDto.setRequestText("");
        communicateVoiceAiLogApiDto.setResponseText("");
        communicateVoiceAiLogApiDto.setCreator(userId);
        communicateVoiceAiLogApiDto.setAddTime(new Date());
        communicateVoiceAiLogApiDto.setModTime(new Date());
        return communicateVoiceAiLogApiDto;
    }

    /**
     * <b>Description:</b><br>
     * 漏接来电更新
     *
     * @param session
     * @param callFailed
     * @return
     * @Note <b>Author:</b> Jerry <br>
     *       <b>Date:</b> 2017年7月20日 下午3:04:43
     */
    @ResponseBody
    @RequestMapping(value = "/editcallfailedcoid")
    @SystemControllerLog(operationType = "edit",desc = "保存漏接来电更新")
    public ResultInfo editCallFailedCoid(HttpSession session, CallFailed callFailed) {
        ResultInfo resultInfo = new ResultInfo<>();
        Boolean res = callService.editCallFailedCoid(callFailed, session);
        if (res) {
            resultInfo.setCode(0);
            resultInfo.setMessage("操作成功");
        }

        return resultInfo;
    }



    /**
     * 根据电话获取客户ID
     * @param callOut
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getTraderIdByPhone")
    public ResultInfo getTraderIdByPhone(CallOut callOut) {
        String phone = callOut.getPhone();
        return new ResultInfo<>(0, "查询成功", callService.getTraderIdsByPhone(phone));
    }

    /**
     * 获取客户联系人信息
     *
     * @param traderId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getTraderContact")
    public ResultInfo<List<TraderContact>> getTraderContact(Integer traderId) {
        if (traderId == null || traderId < 1){
            return new ResultInfo(-1, "请输入客户ID！");
        }
        TraderContact traderContact = new TraderContact();
        traderContact.setTraderId(traderId);
        traderContact.setIsEnable(ErpConst.ONE);
        traderContact.setTraderType(ErpConst.ONE);
        List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);
        return new ResultInfo(0, "查询成功！", contactList);
    }

    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/pushVoiceMp3")
    public ResultInfo pullVoiceMp3(CallOut callOut, HttpSession session, HttpServletRequest request) {
        if(callOut != null && StringUtils.isNotBlank(callOut.getCoid())){
            callService.pullVoiceMp3(callOut.getCoid());
            return  ResultInfo.success("操作成功");
        }
        return ResultInfo.error("无coid，推送失败");
    }

    /**
     * {
     * "jobNumber": "1338",
     * "externalUserId": "wmG8lLEQAAgwIUQlqRkSaO3d2Y93d3YA",
     * "talkId":"123",
     * "coidUri":"http://crfile.ivedeng.com/voice/5609317808002023702_1733377679187_external.mp3",
     * "coidLength":109,
     * "begintime": 1744883231000,
     * "endtime": 1744883373000
     * }
     *
     * @param communicateRecord
     * @return
     */
    @NoNeedAccessAuthorization
    @ResponseBody
    @RequestMapping(value = "/pushVoiceWxMp3")
    public ResultInfo pushVoiceWxMp3(@RequestBody  WxCommunicateRecord communicateRecord) {
        boolean checkParams = communicateRecord != null && !StringUtils.isBlank(communicateRecord.getTalkId())
                && !StringUtils.isBlank(communicateRecord.getJobNumber()) && !StringUtils.isBlank(communicateRecord.getExternalUserId())
                && communicateRecord.getCoidLength() != null
                && communicateRecord.getBegintime() != null && communicateRecord.getEndtime() != null;
        //检查以上这六个字段否为空
        if(checkParams){
            try {
                boolean result=  callService.pullVoiceWxMp3(communicateRecord);
                if(result){
                    return ResultInfo.success("发起微信语音解析成功");
                }
                return  ResultInfo.success("操作失败");
            }catch (Exception e){
                log.error("语音解析失败",e);
                return ResultInfo.error("语音解析失败："+e.getMessage());
            }
        }
        return ResultInfo.error("参数缺失，请检查");
    }


    @Autowired
    private TraderCustomerApiService traderCustomerApiService;

    /**
     * 呼出添加沟通记录 找到了客户
     *
     * @param callOut
     * @param session
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addCommunicate")
    public ModelAndView addCommunicate(CallOut callOut, HttpSession session, HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView("call/common/add_communicate");
        User user = (User) session.getAttribute(Consts.SESSION_USER);

        List<Integer> traderIds = callService.getTraderIdsByPhone(callOut.getPhone());
        if (CollectionUtils.isNotEmpty(traderIds)){
            List<TraderCustomerDto> traderCustomerDtos = traderCustomerApiService.getTraderCustomerInfoByTraderIds(traderIds);
            Map<Integer, TraderCustomerDto> traderCustomerDtoMap = traderCustomerDtos.stream().collect(Collectors.toMap(TraderCustomerDto::getTraderId, Function.identity()));
            ArrayList<TraderBaseInfoDto> traderBaseInfoDtos = new ArrayList<>();
            traderIds.forEach(traderId -> {
                TraderBaseInfoDto traderBaseInfo = getTraderBaseInfoByTraderId(traderId, ErpConst.ONE);
                TraderCustomerDto traderCustomerDto = traderCustomerDtoMap.get(traderId);
                if (Objects.nonNull(traderCustomerDto)){
                    traderBaseInfo.setCustomerNature(traderCustomerDto.getCustomerNature());
                }
                traderBaseInfoDtos.add(traderBaseInfo);
            });
            modelAndView.addObject("traders", traderBaseInfoDtos);
        }


        Integer traderId = null;
        if (callOut.getTraderId() != null && callOut.getTraderId() > 0){
            traderId = callOut.getTraderId();
        } else if (CollectionUtils.isNotEmpty(traderIds) && traderIds.size() == 1){
            traderId = traderIds.get(0);
        }

        if (traderId != null){
            callOut.setTraderId(traderId);
            TraderContact traderContact = new TraderContact();
            traderContact.setTraderId(traderId);
            traderContact.setIsEnable(ErpConst.ONE);
            traderContact.setTraderType(ErpConst.ONE);
            List<TraderContact> contactList = traderCustomerService.getTraderContact(traderContact);
            modelAndView.addObject("contactList", contactList);
            
            TraderCustomerDto traderCustomerDto = traderCustomerApiService.getTraderCustomerInfoByTraderId(traderId);
            modelAndView.addObject("traderCustomerDto", traderCustomerDto);
        }

        // 客户标签
        setCommunicateInfo(callOut, request, user, modelAndView);

        Integer communicateType = null;

        // 以是否有订单为主：呼出类型 1商机2销售订单3报价4售后5采购订单
        if ( callOut.getCallType() != null && callOut.getCallType() > 0) {
            switch (callOut.getCallType()){
                // 商机
                case 1 :{
                    communicateType = SysOptionConstant.ID_244;
                    break;
                }
                // 销售订单
                case 2 : {
                    communicateType = SysOptionConstant.ID_246;
                    break;
                }
                // 报价
                case 3 :{
                    communicateType = SysOptionConstant.ID_245;
                    break;
                }
                // 售后
                case 4 :{
                    communicateType = SysOptionConstant.ID_248;
                    break;
                }
                // 采购订单
                case 5 :{
                    communicateType = SysOptionConstant.ID_247;
                    break;
                }
                // 线索
                case 6 :{
                    communicateType = SysOptionConstant.ID_4083;
                    break;
                }
                // 商机线索
                case 7 :{
                    communicateType = SysOptionConstant.ID_4109;
                    break;
                }
                case 8: {
                    communicateType =SysOptionConstant.ID_4133;
                    break;
                }
                default:{
                    break;
                }
            }
        } else {
            communicateType = 0;
        }
        //商机新增沟通记录时更新商机精准度VDERP-15625
        if( callOut.getCallType() != null && callOut.getCallType() > 0 && callOut.getCallType() == 1){
            BussinessChance bussinessChance = new BussinessChance();
            bussinessChance.setBussinessChanceId(callOut.getOrderId());
            BussinessChance bussinessChanceInfo = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
            if(bussinessChanceInfo != null){
                modelAndView.addObject("bussinessType", bussinessChanceInfo.getType());
                modelAndView.addObject("businessChanceAccuracy", bussinessChanceInfo.getBusinessChanceAccuracy());
                //判断是否是科研购事业部的
                Boolean belongPlatfromByOrgAndUser = userService.getBelongPlatfromByOrgAndUser(user, SCIENCE_ORGID, SCIENCE_COMPANY_ID);
                modelAndView.addObject("belongPlatfromByOrgAndUser", belongPlatfromByOrgAndUser ? 1 : 0);
            }else {
                modelAndView.addObject("bussinessType", 0);
            }
        }
        modelAndView.addObject("relatedId", callOut.getOrderId());
        modelAndView.addObject("communicateType", communicateType);
        modelAndView.addObject("communicateTime", callOut.getCommunicateTime());
        modelAndView.addObject("coid",callOut.getCoid());
        return modelAndView;
    }

    /**
     * 商机未找到联系人时的沟通记录弹窗初始化
     *
     * @param callOut
     * @param session
     * @param request
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/bussinessCommunicate")
    public ModelAndView bussinessCommunicate(CallOut callOut, HttpSession session, HttpServletRequest request) {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
        ModelAndView modelAndView = new ModelAndView("call/common/bussiness_communicate");
        setCommunicateInfo(callOut, request, user, modelAndView);

        BussinessChance bussinessChance = new BussinessChance();
        bussinessChance.setBussinessChanceId(callOut.getOrderId());

        BussinessChance bussinessChanceInfo = bussinessChanceService.getBussinessChanceInfo(bussinessChance);
        if (bussinessChanceInfo != null){
            modelAndView.addObject("bussinessType", bussinessChanceInfo.getType());
            //判断是否是科研购事业部的
            Boolean belongPlatfromByOrgAndUser = userService.getBelongPlatfromByOrgAndUser(user, SCIENCE_ORGID, SCIENCE_COMPANY_ID);
            modelAndView.addObject("belongPlatfromByOrgAndUser", belongPlatfromByOrgAndUser ? 1 : 0);
            modelAndView.addObject("traderName", StringUtils.isNotBlank(bussinessChanceInfo.getCheckTraderName()) ?
                    bussinessChanceInfo.getCheckTraderName() : bussinessChanceInfo.getTraderName());
            if (bussinessChanceInfo.getUserId() != null && bussinessChanceInfo.getUserId() != 0){
                modelAndView.addObject("saleUser",  userService.getUserById(bussinessChanceInfo.getUserId()));
            }
        }

        modelAndView.addObject("bussinessChanceInfo", bussinessChanceInfo);
        modelAndView.addObject("communicateTime", callOut.getCommunicateTime());
        return modelAndView;
    }

    private void setCommunicateInfo(CallOut callOut, HttpServletRequest request, User user, ModelAndView modelAndView) {
        // 客户标签
        Tag tag = new Tag();
        tag.setTagType(SysOptionConstant.ID_32);
        tag.setIsRecommend(ErpConst.ONE);
        tag.setCompanyId(user.getCompanyId());

        //默认沟通时间
        Date now = new Date();
        Date endDate = new Date(now.getTime() + 120000);
        modelAndView.addObject("startTime", DateUtil.DateToString(now, "yyyy-MM-dd HH:mm:ss"));
        modelAndView.addObject("endTime", DateUtil.DateToString(endDate, "yyyy-MM-dd HH:mm:ss"));

        Integer pageNo = 1;
        Integer pageSize = 10;

        Page page = getPageTag(request, pageNo, pageSize);
        Map<String, Object> tagMap = tagService.getTagListPage(tag, page);
        modelAndView.addObject("tagList", (List<Tag>) tagMap.get("list"));
        modelAndView.addObject("page", (Page) tagMap.get("page"));
        modelAndView.addObject("callOut", callOut);
    }

    private String getTtNumberType(Integer userId){
        String key="CALL_CENTER_USER_CALL_TYPE_"+userId;
        String value=JedisUtils.get(key);
        String typevalue=StringUtils.isBlank(value)?"0":value;
        return typevalue;
    }

    /**
     *  當用戶刷新頁面時，記錄日志，備查
     * @param callOut
     * @param session
     * @param request
     */
    @ResponseBody
    @RequestMapping(value = "/static/beforeunload")
    public void beforeunload(CallOut callOut, HttpSession session, HttpServletRequest request) throws IOException {
        User user = (User) session.getAttribute(Consts.SESSION_USER);
//        logger.info(JsonUtils.translateToJson(user));
    }



    @NoNeedAccessAuthorization
    @RequestMapping(method = RequestMethod.GET,value = "/stateChangePoint")
    @ResponseBody
    public void alertWhenStateChangeToOne(@RequestParam String agentId, @RequestParam String ins, @RequestParam String workMode,
                                          @RequestParam String state, @RequestParam String isLogon){
        logger.info("呼叫中心连接状态变为1，工号：{}",agentId);
    }

    /**
     * 完善沟通记录信息
     *
     * @param record
     * @param request
     * @return
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/completeCommunicateInfo")
    public ResultInfo completeCommunicateInfo(CommunicateRecord record,  HttpServletRequest request, HttpSession session){
        logger.info("completeCommunicateInfo record:{}", JSON.toJSONString(record));
        User sessionUser = getSessionUser(request);
        if (sessionUser == null){
            return ResultInfo.error("无用户信息，请重新登录！");
        }
        String recordIdStr = JedisUtils.get(COMMUNICATE_CALL_FLAG + record.getCoid() + sessionUser.getUserId());
        logger.info("相关录音对应的ID信息为COID:{},recordIdStr:{}", record.getCoid(), recordIdStr);
        if (StringUtil.isBlank(recordIdStr)){
            logger.error("要处理，未找到沟通ID信息 record:{}", JSON.toJSONString(record));
            return ResultInfo.success();
        }
        record.setCommunicateRecordId(Integer.parseInt(recordIdStr));

        try {
            traderCustomerService.saveEditCommunicate(record, request, session);
        } catch (Exception e) {
            logger.error("completeCommunicateInfo error", e);
            return ResultInfo.error("沟通信息保存失败！");
        }
        return ResultInfo.success();
    }

}
