package com.vedeng.system.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class Tag implements Serializable {
    private Integer tagId;

    private Integer companyId;

    private Integer tagType;

    private Integer isRecommend;

    private String tagName;

    private String comments;

    public void setTagName(String tagName) {
        this.tagName = tagName == null ? null : tagName.trim();
    }

    public void setComments(String comments) {
        this.comments = comments == null ? null : comments.trim();
    }

    public Tag() {
    }

    public Tag(Integer companyId, Integer tagType, Integer tagId) {
        this.companyId = companyId;
        this.tagType = tagType;
        this.tagId = tagId;
    }
}