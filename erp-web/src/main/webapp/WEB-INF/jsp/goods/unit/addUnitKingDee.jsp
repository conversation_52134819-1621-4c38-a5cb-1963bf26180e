<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="新增单位" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%= basePath %>static/js/unit/addKingDee.js?rnd=${resourceVersionKey}'></script>
<div class="addElement">
	<div class="top-left">
		<input type='button'  class="bt-small bt-bg-style bg-light-blue" value="添加" onclick="add()"/>
	</div>
	<form action="" method="post" id="addUnitKingDee">
		<table class="table table-bordered table-striped table-condensed table-centered" id="table">
			<thead>
			<tr>
				<th><span style="color: red">*</span>金蝶单位编号</th>
				<th><span  style="color: red">*</span>金蝶单位名称</th>
				<th>操作</th>
			</tr>
			</thead>

			<tbody  id="kingDeeTbody">
			<c:if test="${not empty unitKingDeeList}">
				<c:forEach items="${unitKingDeeList}" var="list" varStatus="status">
					<tr>
						<td><xmp>${list.unitKingDeeNo}</xmp></td>
						<td><xmp>${list.unitKingDeeName}</xmp></td>
						<td>&nbsp;</td>
					</tr>
				</c:forEach>
			</c:if>
			</tbody>
		</table>
		<div class="add-tijiao tcenter">
			<input type='submit'  class="bt-small bt-bg-style bg-light-blue" value="提交" />
			<button type="button" class="dele" id="cancle">取消</button>
		</div>
	</form>
</div>
<%@ include file="../../common/footer.jsp"%>