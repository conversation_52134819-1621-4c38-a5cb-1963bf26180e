package com.vedeng.erp.trader.dto;

import java.util.List;

import lombok.Data;

/**
 * 查询通话记录
 * @ClassName:  CommunicateTelRecordParams   
 * @author: <PERSON>.yang
 * @date:   2025年7月4日 下午1:16:43    
 * @Copyright:
 */
@Data
public class CommunicateTelRecordParams {

	/**查询关键字（号码/联系人）*/
	private String keywords;
	
	/**沟通方向【 1座机-呼入；2座机-呼出；3：企微-呼出；4：企微-呼入】*/
	private List<Integer> coidTypeList;
	
	/**录音ID*/
	private Integer communicateRecordId;
	
	/**通话开始时间-年月日*/
	private String createTimeStart;
	
	/**通话结束时间-年月日*/
	private String createTimeEnd;
	
	/**当前登录用户自身以及下属用户ID列表（按企微结构）,不传默认根据当前用户获取自身及下属用户ID*/
	private List<Integer> userIdList;
	
}
