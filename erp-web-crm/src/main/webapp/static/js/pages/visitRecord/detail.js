void function () {
    new Vue({
        el: '#page-container',
        data: {
            id: '',
            pageLoading: true,
            fixedTop: layout_hidden_value ? 0 : 50, //表头悬浮高度，根据有没有头部判断,
            cardLineNum: '',
            asideList: [{ name: '操作记录', icon: 'time', id: 1 },],
            asideIndex: 1,
            detail: {},
            relatedOrderHeaders: [
                { key: "typeStr", label: "单据类型", width: "84px" },
                { key: "bizNo", label: "单据编号", width: "184px" },
                { key: "traderName", label: "客户名称", width: "245px" },
                { key: "belonger", label: "归属销售", width: "153px", avatar: 'belongerPic' },
                { key: "status", label: "状态", width: "122px" },
                { key: "createTime", label: "创建时间", width: "170px" },
            ],
            relatedOrderDtoList: [],
            clockInHeader: [
                { key: "userName", label: "拜访人", width: "296px", avatar: 'aliasHeadPicture' },
                { key: "cardTime", label: "打卡时间", width: "316px", tip: '仅展示最后一次打卡信息' },
                { key: "cardLocation", label: "查看打卡位置", width: "210px" },
            ],
            cardPicList: [],
            feedbackHeader: [
                { key: "userName", label: "记录人", width: "141px", avatar: 'aliasHeadPicture', vertical: 'center' },
                { key: "addTime", label: "记录时间", width: "180px", vertical: 'center'},
                { key: "contentSuffix", label: "记录内容", width: "380px" },
            ],
            //拜访记录弹层
            isShowVisitDialog: false,
            visitFormMobile: '',
            visitFormMobileMsg: '',
            visitFormTelephone: '',
            visitFormTelephoneMsg: '',
            visitFormOtherContact: '',
            visitFormContact: '',
            visitFormContactId: '',
            visitFormTraderId: '',
            visitFormDepart: '',
            visitFormPPT: '',
            visitFormConnect: '',
            isNoVisitContactInfo: false,
            positionList: [],
            isFromPhone: false,
            //end
            //沟通记录弹层
            isShowConnectDialog: false,
            connectValue: '',
            //end
            //关闭
            isShowCloseDialog: false,
            CloseReasonTypes: [{
                label: '客户临时出差/不在场',
                value: 1
            }, {
                label: '客户主动取消预约',
                value: 2
            }, {
                label: '客户需求已解决',
                value: 3
            }, {
                label: '线索/商机已关闭',
                value: 4
            }, {
                label: '个人时间冲突',
                value: 5
            }, {
                label: '其他',
                value: 6
            }],
            closeReasonType: '',
            closeReason: '',
            //end
            // 进度条数据
            stepList: [],
            stepActive: '',
            stepStatus: '',
            cansubmit: true,
            contactMobileStatus: 0, //客户信息手机号注册状态 0:未判断 1：已注册 2：未注册
            recordContactMobileStatus: 0, //拜访记录手机号注册状态
        },
        async created() {
            this.getDetailInfo();
        },
        mounted() {

        },
        methods: {
            // cardTest() {
            //     this.$axios.post('/crm/visitrecord/m/card', {
            //         recordId: '479',
            //         visitUserId: 401,
            //         cardPhotoUrls: '',
            //         cardLocation: '116.150509,39.034709'
            //     })
            // },
            getDetailInfo() {
                GLOBAL.showGlobalLoading();
                this.id = GLOBAL.getQuery('id');

                this.$axios.get('/crm/visitrecord/profile/detail?id=' + this.id).then(({ data }) => {
                    GLOBAL.hideGlobalLoading();
                    this.pageLoading = false;

                    if (data.success) {
                        this.detail = data.data;
                        let leadsInfo = data.data.businessLeadsForVisitDto;
                        let businessInfo = data.data.businessChanceForVisitDto;

                        if (leadsInfo) {
                            this.relatedOrderDtoList.push({
                                typeStr: '线索',
                                type: 1,
                                bizNo: leadsInfo.leadsNo,
                                belonger: leadsInfo.belonger,
                                belongerPic: leadsInfo.belongerPic,
                                status: leadsInfo.followStatusStr,
                                createTime: leadsInfo.createTime,
                                traderName: leadsInfo.traderName,
                                tycFlag: leadsInfo.tycFlag,
                                traderNameLink: leadsInfo.traderNameLink,
                                traderNameInnerLink: leadsInfo.traderNameInnerLink,
                                bizId: leadsInfo.id,
                                traderId: leadsInfo.traderId
                            });
                        }

                        if (businessInfo) {
                            this.relatedOrderDtoList.push({
                                typeStr: '商机',
                                type: 2,
                                bizNo: businessInfo.bussinessChanceNo,
                                belonger: businessInfo.belonger,
                                belongerPic: businessInfo.belongPic,
                                status: businessInfo.stageStr,
                                createTime: businessInfo.createTime,
                                traderName: businessInfo.traderName,
                                tycFlag: businessInfo.tycFlag,
                                traderNameLink: businessInfo.traderNameLink,
                                traderNameInnerLink: businessInfo.traderNameInnerLink,
                                bizId: businessInfo.bussinessChanceId,
                                traderId: businessInfo.traderId
                            });
                        }

                        if (this.detail.cardList && this.detail.cardList.length) {
                            this.detail.cardList.forEach(item => {
                                if (item.cardPhotoUrls) {
                                    this.cardPicList = this.cardPicList.concat(item.cardPhotoUrls.split(','))
                                }
                            })
                        }

                        this.getStepList();

                        if(this.detail.contactMobile) {
                            this.checkTelStatus(1, this.detail.contactMobile)
                        }

                        if(this.detail.recordContactMobile) {
                            this.checkTelStatus(2, this.detail.recordContactMobile)
                        }

                    } else {
                        this.$message.warn(data.message);
                    }
                })
            },
            getStepList() {
                let list = [{
                    label: '创建计划',
                    id: 1
                }]

                let status = this.detail.visitRecordStatus;

                if (status != 4) {
                    list = list.concat([{
                        label: '上门打卡',
                        id: 2
                    }, {
                        label: '编写拜访记录',
                        id: 3
                    }, {
                        label: '拜访完成',
                        id: 3
                    },])

                    if (status == 3) {
                        this.stepStatus = 'finish';
                    } 

                    if (status == 1) {
                        this.stepStatus = 'wait';
                    } 
                } else {
                    this.stepStatus = 'close';

                    if (this.detail.cardList && this.detail.cardList.length) {
                        list.push({
                            label: '上门打卡',
                            id: 2
                        })
                    }

                    list.push({
                        label: '已关闭',
                        id: 4
                    })
                }

                this.stepList = list;
                this.stepActive = status;
            },
            checkTelStatus(type, mobile) {
                this.$axios.post('/crm/visitrecord/profile/checkMobileExists?mobile=' + mobile).then(({ data }) => {
                    if (data.success) {
                        if(type == 1) {
                            this.contactMobileStatus = data.data ? 1 : 2;
                        } else {
                            this.recordContactMobileStatus = data.data ? 1 : 2;
                        }
                    }
                })
            },
            clearVisitFormValue() {
                this.visitFormMobile = '';
                this.visitFormMobileMsg = '';
                this.visitFormTelephone = '';
                this.visitFormTelephoneMsg = '';
                this.visitFormOtherContact = '';
                this.visitFormContact = '';
                this.visitFormContactId = '';
                this.visitFormDepart = '';
                this.visitFormPPT = '';
                this.visitFormConnect = '';
                this.isNoVisitContactInfo = false;
            },
            getTraderOption() {
                this.positionList = [];

                setTimeout(() => {
                    let list = [];

                    let labels = {
                        465: ['老板', '销售负责人', '销售经理', '采购负责人', '采购经理', '商务人员', '财务人员', '物流人员', '售后人员', '临床工程师', '其他'],
                        466: ['院长', '设备科人员', '临床医生', '采购负责人', '采购经理', '运营人员', '财务人员', '医院库管', '其他']
                    };

                    labels[this.detail.customerNature].forEach(item => {
                        list.push({
                            label: item,
                            value: item
                        })
                    })

                    this.positionList = list;
                }, 100);
            },
            showVisitDialog() {
                if (this.detail.visitRecordStatus == 1) {
                    this.$message.warn('请完成打卡之后再进行记录填写');
                    return;
                }

                this.getTraderOption()

                let _this = this;
                this.$form.rules({
                    visitFormMobile: {
                        custom: [{
                            valid() {
                                if (!_this.isNoVisitContactInfo && !_this.visitFormMobile.trim() && !_this.visitFormTelephone.trim() && !_this.visitFormOtherContact.trim()) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '手机、固话、其他联系方式，需有一个及以上的填写。'
                        }, {
                            valid() {
                                if (!_this.isNoVisitContactInfo && _this.visitFormMobile && _this.visitFormMobile.length !== 11) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '请输入11位手机号码'
                        }],
                    },
                    visitFormContact: {
                        custom: {
                            valid() {
                                if (!_this.isNoVisitContactInfo && !_this.visitFormContact.trim()) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '请输入联系人'
                        },
                    },
                    visitFormDepart: {
                        custom: {
                            valid() {
                                if (!_this.isNoVisitContactInfo && !_this.visitFormDepart) {
                                    return false;
                                } else {
                                    return true;
                                }
                            },
                            message: '请选择职位'
                        },
                    },
                    visitFormConnect: {
                        required: '请输入沟通记录',
                    }
                }, 'visitForm', this);

                this.clearVisitFormValue();
                this.visitFormMobile = this.detail.contactMobile || '';
                this.visitFormTelephone = this.detail.contactTele || '';
                this.visitFormOtherContact = this.detail.otherContact || '';
                this.visitFormContact = this.detail.contactName || '';
                this.visitFormDepart = this.detail.contactPosition || '';

                this.isShowVisitDialog = true;
            },
            // 手机
            handlerPhone(val) {
                console.log('handlerPhone', val);
                if (this.detail.traderId) {
                    if (this.isFromPhone) { // 当前是选择的
                        this.visitFormContact = val.traderContactName || '';
                        this.visitFormContactId = val.traderContactId || '';
                    } else if (val.choosed) {
                        this.isFromPhone = true;
                        this.visitFormContact = val.traderContactName || '';
                        this.visitFormContactId = val.traderContactId || '';
                    }
                }

                this.checkVisitFormPhone();

                if (this.isFromPhone && this.visitFormContact) {
                    this.$form.validEl('visitForm_visitFormContact');
                }
            },
            checkVisitFormPhone(){
                if(this.visitFormMobile && this.$form.validEl('visitForm_visitFormMobile')) {
                    this.checkTelStatus(2, this.visitFormMobile);
                } else {
                    this.recordContactMobileStatus = 0;
                }
            },
            validPhone() {
                this.$form.validEl('visitForm_visitFormMobile');
            },
            toggleVisitContactInfo() {
                this.isNoVisitContactInfo = !this.isNoVisitContactInfo;
            },
            addVisitRecord() {
                if (this.$form.validForm('visitForm') && this.cansubmit) {
                    let _this = this;

                    _this.$popup.warn({
                        message: '提交后将完成拜访，拜访计划和记录将不可修改，确认继续提交?',
                        buttons: [{
                            txt: '提交',
                            btnClass: 'confirm',
                            callback() {
                                _this.submitVisitRecord();
                            }
                        }, {
                            txt: '取消',
                        }]
                    })
                }
            },
            submitVisitRecord() {
                this.cansubmit = false;

                let reqData = {
                    id: this.id,
                    showPpt: this.visitFormPPT,
                    customerRequires: this.visitFormConnect,
                    noContract: 'Y'
                };

                if (!this.isNoVisitContactInfo) {
                    reqData.recordContactMobile = this.visitFormMobile;
                    reqData.recordContactName = this.visitFormContact;
                    reqData.recordContactTele = this.visitFormTelephone;
                    reqData.recordContactPosition = this.visitFormDepart;
                    reqData.recordOtherContact = this.visitFormOtherContact;
                    reqData.noContract = 'N';
                }

                this.$axios.post('/crm/visitrecord/profile/update', reqData).then(({ data }) => {
                    if (data.success) {
                        this.$message.success('添加成功');

                        setTimeout(() => {
                            window.location.reload();
                        }, 1000)
                    } else {
                        this.cansubmit = true;
                        this.$message.warn(data.message)
                    }
                })
            },
            handlerContact() {
                this.isFromPhone = false;
                this.visitFormContactId = '';
            },
            showConnectDialog() {
                this.$form.rules({
                    connectValue: {
                        required: '请填写沟通记录'
                    },
                }, 'connectForm', this);

                this.connectValue = "";
                this.isShowConnectDialog = true;
            },
            addConnectRecord() {
                if (this.$form.validForm('connectForm') && this.cansubmit) {
                    this.cansubmit = false;
                    this.$axios.post('/crm/visitrecord/profile/addCommunicateRecord', {
                        recordId: this.id,
                        communicateContent: this.connectValue
                    }).then(({ data }) => {
                        if (data.success) {
                            this.$message.success('添加成功');

                            setTimeout(() => {
                                window.location.reload();
                            }, 1000)
                        } else {
                            this.cansubmit = true;
                            this.$message.warn(data.message);
                        }
                    })
                }
            },
            showCloseDialog() {
                if(this.detail.visitRecordStatus == 2) {
                    this.$message.warn('当前状态为拜访中，如特殊原因无法完成拜访，可在拜访记录中进行详细描述')
                    
                    return;
                }

                this.$form.rules({
                    closeReasonType: {
                        required: '请选择关闭原因'
                    },
                }, 'closeForm', this);

                this.closeReasonType = "";
                this.closeReason = "";
                this.isShowCloseDialog = true;
            },
            submitClose() {
                if (this.$form.validForm('closeForm')) {
                    this.$axios.post('/crm/visitrecord/profile/close', {
                        id: this.id,
                        closeReasonType: this.closeReasonType,
                        closeReasonContent: this.closeReason
                    }).then(({data}) => {
                        if(data.success) {
                            this.$message.success('关闭成功')

                            setTimeout(() => {
                                window.location.reload()
                            }, 1000)
                        }
                    })
                }
            },
            openDialog(id) {
                switch(id) {
                    case 1:
                        this.$refs.operationLogDialog.open({
                            relatedId: this.id, // 业务id
                            url: '/crm/visitrecord/profile/logs'
                        });
                        break;
                }
            },
            editPlan() {
                window.location.href = "/crm/visitRecord/profile/add?id=" + this.id;
            },
            addNextPlan() {
                if (GLOBAL.auth('C0401')) {
                    GLOBAL.link({ name: '新建拜访计划', url: '/crm/visitRecord/profile/add?copy=' + this.id, multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            },
            addBusinessChance() {
                if (GLOBAL.auth('C0201')) {
                    GLOBAL.link({ name: '新建商机/线索', url: '/crm/businessChance/profile/add?visitid=' + this.id, multi: true })
                } else {
                    GLOBAL.showNoAuth();
                }
            }
        }
    })
}.call(this);