<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.RegionJdMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    <!--@Table T_REGION_JD-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="JD_PROVINCE_CODE" jdbcType="VARCHAR" property="jdProvinceCode" />
    <result column="JD_CITY_CODE" jdbcType="VARCHAR" property="jdCityCode" />
    <result column="JD_AREA_CODE" jdbcType="VARCHAR" property="jdAreaCode" />
    <result column="JD_P_C_AREA_NAME" jdbcType="VARCHAR" property="jdPCAreaName" />
    <result column="VD_REGION_ID" jdbcType="INTEGER" property="vdRegionId" />
    <result column="VD_REGION_NAME" jdbcType="VARCHAR" property="vdRegionName" />
    <result column="VD_CITY_ID" jdbcType="INTEGER" property="vdCityId" />
    <result column="VD_CITY_NAME" jdbcType="VARCHAR" property="vdCityName" />
    <result column="VD_PROVINCE_ID" jdbcType="INTEGER" property="vdProvinceId" />
    <result column="VD_PROVINCE_NAME" jdbcType="VARCHAR" property="vdProvinceName" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, JD_PROVINCE_CODE, JD_CITY_CODE, JD_AREA_CODE, JD_P_C_AREA_NAME, VD_REGION_ID, 
    VD_REGION_NAME, VD_CITY_ID, VD_CITY_NAME, VD_PROVINCE_ID, VD_PROVINCE_NAME, ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_REGION_JD
    where ID = #{id,jdbcType=VARCHAR}
  </select>
    <select id="selectJdToVdRegion" parameterType="list" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List" />
        from T_REGION_JD
        where JD_P_C_AREA_NAME in
        <foreach close=")" collection="list" item="item" open="(" separator=",">
            #{item}
        </foreach>
    </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--@mbg.generated-->
    delete from T_REGION_JD
    where ID = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    insert into T_REGION_JD (ID, JD_PROVINCE_CODE, JD_CITY_CODE, 
      JD_AREA_CODE, JD_P_C_AREA_NAME, VD_REGION_ID, 
      VD_REGION_NAME, VD_CITY_ID, VD_CITY_NAME, 
      VD_PROVINCE_ID, VD_PROVINCE_NAME, ADD_TIME
      )
    values (#{id,jdbcType=VARCHAR}, #{jdProvinceCode,jdbcType=VARCHAR}, #{jdCityCode,jdbcType=VARCHAR}, 
      #{jdAreaCode,jdbcType=VARCHAR}, #{jdPCAreaName,jdbcType=VARCHAR}, #{vdRegionId,jdbcType=INTEGER}, 
      #{vdRegionName,jdbcType=VARCHAR}, #{vdCityId,jdbcType=INTEGER}, #{vdCityName,jdbcType=VARCHAR}, 
      #{vdProvinceId,jdbcType=INTEGER}, #{vdProvinceName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    insert into T_REGION_JD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="jdProvinceCode != null">
        JD_PROVINCE_CODE,
      </if>
      <if test="jdCityCode != null">
        JD_CITY_CODE,
      </if>
      <if test="jdAreaCode != null">
        JD_AREA_CODE,
      </if>
      <if test="jdPCAreaName != null">
        JD_P_C_AREA_NAME,
      </if>
      <if test="vdRegionId != null">
        VD_REGION_ID,
      </if>
      <if test="vdRegionName != null">
        VD_REGION_NAME,
      </if>
      <if test="vdCityId != null">
        VD_CITY_ID,
      </if>
      <if test="vdCityName != null">
        VD_CITY_NAME,
      </if>
      <if test="vdProvinceId != null">
        VD_PROVINCE_ID,
      </if>
      <if test="vdProvinceName != null">
        VD_PROVINCE_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="jdProvinceCode != null">
        #{jdProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="jdCityCode != null">
        #{jdCityCode,jdbcType=VARCHAR},
      </if>
      <if test="jdAreaCode != null">
        #{jdAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="jdPCAreaName != null">
        #{jdPCAreaName,jdbcType=VARCHAR},
      </if>
      <if test="vdRegionId != null">
        #{vdRegionId,jdbcType=INTEGER},
      </if>
      <if test="vdRegionName != null">
        #{vdRegionName,jdbcType=VARCHAR},
      </if>
      <if test="vdCityId != null">
        #{vdCityId,jdbcType=INTEGER},
      </if>
      <if test="vdCityName != null">
        #{vdCityName,jdbcType=VARCHAR},
      </if>
      <if test="vdProvinceId != null">
        #{vdProvinceId,jdbcType=INTEGER},
      </if>
      <if test="vdProvinceName != null">
        #{vdProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    update T_REGION_JD
    <set>
      <if test="jdProvinceCode != null">
        JD_PROVINCE_CODE = #{jdProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="jdCityCode != null">
        JD_CITY_CODE = #{jdCityCode,jdbcType=VARCHAR},
      </if>
      <if test="jdAreaCode != null">
        JD_AREA_CODE = #{jdAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="jdPCAreaName != null">
        JD_P_C_AREA_NAME = #{jdPCAreaName,jdbcType=VARCHAR},
      </if>
      <if test="vdRegionId != null">
        VD_REGION_ID = #{vdRegionId,jdbcType=INTEGER},
      </if>
      <if test="vdRegionName != null">
        VD_REGION_NAME = #{vdRegionName,jdbcType=VARCHAR},
      </if>
      <if test="vdCityId != null">
        VD_CITY_ID = #{vdCityId,jdbcType=INTEGER},
      </if>
      <if test="vdCityName != null">
        VD_CITY_NAME = #{vdCityName,jdbcType=VARCHAR},
      </if>
      <if test="vdProvinceId != null">
        VD_PROVINCE_ID = #{vdProvinceId,jdbcType=INTEGER},
      </if>
      <if test="vdProvinceName != null">
        VD_PROVINCE_NAME = #{vdProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    update T_REGION_JD
    set JD_PROVINCE_CODE = #{jdProvinceCode,jdbcType=VARCHAR},
      JD_CITY_CODE = #{jdCityCode,jdbcType=VARCHAR},
      JD_AREA_CODE = #{jdAreaCode,jdbcType=VARCHAR},
      JD_P_C_AREA_NAME = #{jdPCAreaName,jdbcType=VARCHAR},
      VD_REGION_ID = #{vdRegionId,jdbcType=INTEGER},
      VD_REGION_NAME = #{vdRegionName,jdbcType=VARCHAR},
      VD_CITY_ID = #{vdCityId,jdbcType=INTEGER},
      VD_CITY_NAME = #{vdCityName,jdbcType=VARCHAR},
      VD_PROVINCE_ID = #{vdProvinceId,jdbcType=INTEGER},
      VD_PROVINCE_NAME = #{vdProvinceName,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
    where ID = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_REGION_JD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="JD_PROVINCE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdProvinceCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="JD_CITY_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdCityCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="JD_AREA_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdAreaCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="JD_P_C_AREA_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdPCAreaName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VD_REGION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdRegionId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="VD_REGION_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdRegionName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VD_CITY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdCityId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="VD_CITY_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdCityName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="VD_PROVINCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdProvinceId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="VD_PROVINCE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdProvinceName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when ID = #{item.id,jdbcType=VARCHAR} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_REGION_JD
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="JD_PROVINCE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdProvinceCode != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdProvinceCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="JD_CITY_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdCityCode != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdCityCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="JD_AREA_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdAreaCode != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdAreaCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="JD_P_C_AREA_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.jdPCAreaName != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.jdPCAreaName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_REGION_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdRegionId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdRegionId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_REGION_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdRegionName != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdRegionName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_CITY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdCityId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdCityId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_CITY_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdCityName != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdCityName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_PROVINCE_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdProvinceId != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdProvinceId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="VD_PROVINCE_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.vdProvinceName != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.vdProvinceName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when ID = #{item.id,jdbcType=VARCHAR} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
    </trim>
    where ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    <!--@mbg.generated-->
    insert into T_REGION_JD
    (ID, JD_PROVINCE_CODE, JD_CITY_CODE, JD_AREA_CODE, JD_P_C_AREA_NAME, VD_REGION_ID, 
      VD_REGION_NAME, VD_CITY_ID, VD_CITY_NAME, VD_PROVINCE_ID, VD_PROVINCE_NAME, ADD_TIME
      )
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.jdProvinceCode,jdbcType=VARCHAR}, #{item.jdCityCode,jdbcType=VARCHAR}, 
        #{item.jdAreaCode,jdbcType=VARCHAR}, #{item.jdPCAreaName,jdbcType=VARCHAR}, #{item.vdRegionId,jdbcType=BIGINT}, 
        #{item.vdRegionName,jdbcType=VARCHAR}, #{item.vdCityId,jdbcType=BIGINT}, #{item.vdCityName,jdbcType=VARCHAR}, 
        #{item.vdProvinceId,jdbcType=BIGINT}, #{item.vdProvinceName,jdbcType=VARCHAR}, 
        #{item.addTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" parameterType="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    insert into T_REGION_JD
    (ID, JD_PROVINCE_CODE, JD_CITY_CODE, JD_AREA_CODE, JD_P_C_AREA_NAME, VD_REGION_ID, 
      VD_REGION_NAME, VD_CITY_ID, VD_CITY_NAME, VD_PROVINCE_ID, VD_PROVINCE_NAME, ADD_TIME
      )
    values
    (#{id,jdbcType=VARCHAR}, #{jdProvinceCode,jdbcType=VARCHAR}, #{jdCityCode,jdbcType=VARCHAR}, 
      #{jdAreaCode,jdbcType=VARCHAR}, #{jdPCAreaName,jdbcType=VARCHAR}, #{vdRegionId,jdbcType=BIGINT}, 
      #{vdRegionName,jdbcType=VARCHAR}, #{vdCityId,jdbcType=BIGINT}, #{vdCityName,jdbcType=VARCHAR}, 
      #{vdProvinceId,jdbcType=BIGINT}, #{vdProvinceName,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}
      )
    on duplicate key update 
    ID = #{id,jdbcType=VARCHAR}, 
    JD_PROVINCE_CODE = #{jdProvinceCode,jdbcType=VARCHAR}, 
    JD_CITY_CODE = #{jdCityCode,jdbcType=VARCHAR}, 
    JD_AREA_CODE = #{jdAreaCode,jdbcType=VARCHAR}, 
    JD_P_C_AREA_NAME = #{jdPCAreaName,jdbcType=VARCHAR}, 
    VD_REGION_ID = #{vdRegionId,jdbcType=BIGINT}, 
    VD_REGION_NAME = #{vdRegionName,jdbcType=VARCHAR}, 
    VD_CITY_ID = #{vdCityId,jdbcType=BIGINT}, 
    VD_CITY_NAME = #{vdCityName,jdbcType=VARCHAR}, 
    VD_PROVINCE_ID = #{vdProvinceId,jdbcType=BIGINT}, 
    VD_PROVINCE_NAME = #{vdProvinceName,jdbcType=VARCHAR}, 
    ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
  </insert>
  <insert id="insertOrUpdateSelective" parameterType="com.vedeng.erp.saleorder.model.po.RegionJd">
    <!--@mbg.generated-->
    insert into T_REGION_JD
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID,
      </if>
      <if test="jdProvinceCode != null">
        JD_PROVINCE_CODE,
      </if>
      <if test="jdCityCode != null">
        JD_CITY_CODE,
      </if>
      <if test="jdAreaCode != null">
        JD_AREA_CODE,
      </if>
      <if test="jdPCAreaName != null">
        JD_P_C_AREA_NAME,
      </if>
      <if test="vdRegionId != null">
        VD_REGION_ID,
      </if>
      <if test="vdRegionName != null">
        VD_REGION_NAME,
      </if>
      <if test="vdCityId != null">
        VD_CITY_ID,
      </if>
      <if test="vdCityName != null">
        VD_CITY_NAME,
      </if>
      <if test="vdProvinceId != null">
        VD_PROVINCE_ID,
      </if>
      <if test="vdProvinceName != null">
        VD_PROVINCE_NAME,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="jdProvinceCode != null">
        #{jdProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="jdCityCode != null">
        #{jdCityCode,jdbcType=VARCHAR},
      </if>
      <if test="jdAreaCode != null">
        #{jdAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="jdPCAreaName != null">
        #{jdPCAreaName,jdbcType=VARCHAR},
      </if>
      <if test="vdRegionId != null">
        #{vdRegionId,jdbcType=BIGINT},
      </if>
      <if test="vdRegionName != null">
        #{vdRegionName,jdbcType=VARCHAR},
      </if>
      <if test="vdCityId != null">
        #{vdCityId,jdbcType=BIGINT},
      </if>
      <if test="vdCityName != null">
        #{vdCityName,jdbcType=VARCHAR},
      </if>
      <if test="vdProvinceId != null">
        #{vdProvinceId,jdbcType=BIGINT},
      </if>
      <if test="vdProvinceName != null">
        #{vdProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        ID = #{id,jdbcType=VARCHAR},
      </if>
      <if test="jdProvinceCode != null">
        JD_PROVINCE_CODE = #{jdProvinceCode,jdbcType=VARCHAR},
      </if>
      <if test="jdCityCode != null">
        JD_CITY_CODE = #{jdCityCode,jdbcType=VARCHAR},
      </if>
      <if test="jdAreaCode != null">
        JD_AREA_CODE = #{jdAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="jdPCAreaName != null">
        JD_P_C_AREA_NAME = #{jdPCAreaName,jdbcType=VARCHAR},
      </if>
      <if test="vdRegionId != null">
        VD_REGION_ID = #{vdRegionId,jdbcType=BIGINT},
      </if>
      <if test="vdRegionName != null">
        VD_REGION_NAME = #{vdRegionName,jdbcType=VARCHAR},
      </if>
      <if test="vdCityId != null">
        VD_CITY_ID = #{vdCityId,jdbcType=BIGINT},
      </if>
      <if test="vdCityName != null">
        VD_CITY_NAME = #{vdCityName,jdbcType=VARCHAR},
      </if>
      <if test="vdProvinceId != null">
        VD_PROVINCE_ID = #{vdProvinceId,jdbcType=BIGINT},
      </if>
      <if test="vdProvinceName != null">
        VD_PROVINCE_NAME = #{vdProvinceName,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2022-12-07-->
  <select id="findFirstByVdRegionId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_REGION_JD
    where VD_REGION_ID=#{vdRegionId,jdbcType=INTEGER} limit 1
  </select>
</mapper>