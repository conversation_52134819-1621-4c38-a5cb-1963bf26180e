function getRedInvoiceDetailList(param) {
    return axios({
        url: '/redConfirm/api/getRedInvoiceDetailList.do',
        method: 'post',
        params: param
    })
}

function getInvoiceRedConfirmationById(param) {
    return axios({
        url: '/redConfirm/api/detail.do',
        method: 'post',
        params: param
    })
}

function getBlueInvoiceDetailList(param) {
    return axios({
        url: '/redConfirm/api/blueInvoiceInfo.do',
        method: 'post',
        params: param
    })
}

function apply(data) {
    return axios({
        url: '/redConfirm/api/apply.do',
        method: 'post',
        data: data
    })
}

function cancel(data) {
    return axios({
        url: '/redConfirm/api/cancel.do',
        method: 'post',
        data: data
    })
}