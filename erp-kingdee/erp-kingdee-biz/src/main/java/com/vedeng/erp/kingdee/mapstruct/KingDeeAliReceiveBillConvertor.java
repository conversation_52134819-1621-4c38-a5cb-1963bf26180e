package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeReceiveBillEntity;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillDto;
import com.vedeng.erp.kingdee.dto.KingDeeAliReceiveBillEntryDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,builder = @Builder(disableBuilder = true))
public interface KingDeeAliReceiveBillConvertor extends BaseMapStruct<KingDeeReceiveBillEntity, KingDeeAliReceiveBillDto> {
    @Override
    @Mapping(target = "FReceiveBillEntry", source = "FReceiveBillEntry", qualifiedByName = "srcEntryListToJsonArray")
    KingDeeReceiveBillEntity toEntity(KingDeeAliReceiveBillDto dto);

    @Override
    @Mapping(target = "FReceiveBillEntry", source = "FReceiveBillEntry", qualifiedByName = "srcEntryJsonArrayToList")
    KingDeeAliReceiveBillDto toDto(KingDeeReceiveBillEntity entity);

    /**
     * entity 中JSONArray 转 原对象
     *
     * @param receiveBillEntryDtos JSONArray
     * @return List<KingDeePayBillDto> dto中的对象
     */
    @Named("srcEntryJsonArrayToList")
    default List<KingDeeAliReceiveBillEntryDto> srcEntryJsonArrayToList(JSONArray receiveBillEntryDtos) {
        if (CollUtil.isEmpty(receiveBillEntryDtos)) {
            return Collections.emptyList();
        }
        return receiveBillEntryDtos.toJavaList(KingDeeAliReceiveBillEntryDto.class);
    }


    /**
     * dto 原对象中 转 JSONArray
     *
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("srcEntryListToJsonArray")
    default JSONArray srcEntryListToJsonArray(List<KingDeeAliReceiveBillEntryDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}
