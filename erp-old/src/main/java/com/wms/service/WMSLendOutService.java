package com.wms.service;

import com.vedeng.authorization.model.User;
import com.vedeng.common.page.Page;
import com.vedeng.logistics.model.GoodsAcceptanceReport;
import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;
import com.vedeng.logistics.model.vo.GoodsAcceptanceReportDto;
import com.vedeng.system.model.Attachment;
import com.wms.model.dto.AddLendOutDto;
import com.wms.model.dto.WMSLendOutQueryDto;
import com.wms.model.po.WmsLendOutOrder;
import com.wms.model.po.WmsOutputOrder;

import java.util.Collection;
import java.util.List;

/**
 * WMS外借
 */
public interface WMSLendOutService {

    List<WmsLendOutOrder> querylistPage(WMSLendOutQueryDto lendOutQueryDto, Page page);

    Long addLendOutOrder(AddLendOutDto addLendOutDto, User user) throws Exception;

    void lendOutOrderAuditPass(Long lendOutOrderId) throws Exception;

    void updateLendOutAuditStatus(Long lendOutOrderId, int value);

    WmsOutputOrder findLendOutById(Long lendOutId);
    
    void updateWmsOutputOrderGoodsExtra(WmsOutputOrderGoodsExtra goodsExtra);

    List<WmsOutputOrderGoodsExtra> findGoodsAuditList(WmsOutputOrderGoodsExtra extra);

    /**
     * 获取外借的验收报告单
     * @param goodsAcceptanceReportId
     * @return
     */
    GoodsAcceptanceReport getGooodsAcceptanceReportById(Long goodsAcceptanceReportId);

    /**
     * 新增商品外借的验收报告单，返回主键
     * @param report
     */
    void addReportByOrderNo(GoodsAcceptanceReport report);

    /**
     * 新增验收报告单的附件
     * @param nameList
     * @param uriList
     * @param goodsAcceptanceReportId
     * @param user
     */
    void addAttachmentByGoodsAcceptanceReportId(List<String> nameList, List<String> uriList, Long goodsAcceptanceReportId, User user);

    /**
     * 新增验收报告
     * @param report
     * @param nameList
     * @param uriList
     * @param user
     */
    void addReport(GoodsAcceptanceReport report, List<String> nameList, List<String> uriList, User user);

    /**
     * 更新验收报告
     * @param report
     * @param nameList
     * @param uriList
     * @param user
     */
    void updateReport(GoodsAcceptanceReport report, List<String> nameList, List<String> uriList, User user);

    /**
     * 获取详情页产品验收报告列表
     * @param orderNo
     * @return
     */
    List<GoodsAcceptanceReportDto> getAcceptanceReportList(String orderNo);

    /**
     * 获取验收报告的附件文件
     * @param goodsAcceptanceReportId
     * @return
     */
    List<Attachment> getReportAttachmentList(Long goodsAcceptanceReportId);

    /**
     * 发送抄送消息-微信
     * @param lendOutId
     * @param goodsExtraList
     * @param userNameList
     */
    void sendWeixinNotice(Long lendOutId, Collection<String> userNameList);


    /**
     * 审核流待办提醒
     * @param lendOutId
     * @param taskId
     * @param userName
     */
    void sendWeixinNoticeForActiviti(Long lendOutId, String businessKey, String taskId,String userName);

}

