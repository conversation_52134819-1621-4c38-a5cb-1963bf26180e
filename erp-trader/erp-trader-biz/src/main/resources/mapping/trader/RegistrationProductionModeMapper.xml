<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.trader.mapper.RegistrationProductionModeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.RegistrationProductionModeEntity">
    <!--@mbg.generated-->
    <!--@Table T_REGISTRATION_PRODUCTION_MODE-->
    <id column="PRODUCTION_MODE_ID" property="productionModeId" />
    <result column="ADD_TIME" property="addTime" />
    <result column="MOD_TIME" property="modTime" />
    <result column="CREATOR" property="creator" />
    <result column="UPDATER" property="updater" />
    <result column="CREATOR_NAME" property="creatorName" />
    <result column="UPDATER_NAME" property="updaterName" />
    <result column="REGISTRATION_NUMBER_ID" property="registrationNumberId" />
    <result column="PRODUCTION_MODE" property="productionMode" />
    <result column="ENTERPRISE_NAME" property="enterpriseName" />
    <result column="MANUFACTURER_ID" property="manufacturerId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PRODUCTION_MODE_ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
    REGISTRATION_NUMBER_ID, PRODUCTION_MODE, ENTERPRISE_NAME, MANUFACTURER_ID
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_REGISTRATION_PRODUCTION_MODE
    where PRODUCTION_MODE_ID = #{productionModeId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_REGISTRATION_PRODUCTION_MODE
    where PRODUCTION_MODE_ID = #{productionModeId}
  </delete>
  <insert id="insert" keyColumn="PRODUCTION_MODE_ID" keyProperty="productionModeId" parameterType="com.vedeng.erp.trader.domain.entity.RegistrationProductionModeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_REGISTRATION_PRODUCTION_MODE (ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, UPDATER_NAME, 
      REGISTRATION_NUMBER_ID, PRODUCTION_MODE, ENTERPRISE_NAME, MANUFACTURER_ID
      )
    values (#{addTime}, #{modTime}, #{creator}, #{updater}, #{creatorName}, #{updaterName}, 
      #{registrationNumberId}, #{productionMode}, #{enterpriseName}, #{manufacturerId}
      )
  </insert>
  <insert id="insertSelective" keyColumn="PRODUCTION_MODE_ID" keyProperty="productionModeId" parameterType="com.vedeng.erp.trader.domain.entity.RegistrationProductionModeEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_REGISTRATION_PRODUCTION_MODE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID,
      </if>
      <if test="productionMode != null">
        PRODUCTION_MODE,
      </if>
      <if test="enterpriseName != null and enterpriseName != ''">
        ENTERPRISE_NAME,
      </if>
      <if test="manufacturerId != null">
        MANUFACTURER_ID,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="addTime != null">
        #{addTime},
      </if>
      <if test="modTime != null">
        #{modTime},
      </if>
      <if test="creator != null">
        #{creator},
      </if>
      <if test="updater != null">
        #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName},
      </if>
      <if test="registrationNumberId != null">
        #{registrationNumberId},
      </if>
      <if test="productionMode != null">
        #{productionMode},
      </if>
      <if test="enterpriseName != null and enterpriseName != ''">
        #{enterpriseName},
      </if>
      <if test="manufacturerId != null">
        #{manufacturerId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.trader.domain.entity.RegistrationProductionModeEntity">
    <!--@mbg.generated-->
    update T_REGISTRATION_PRODUCTION_MODE
    <set>
      <if test="addTime != null">
        ADD_TIME = #{addTime},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime},
      </if>
      <if test="creator != null">
        CREATOR = #{creator},
      </if>
      <if test="updater != null">
        UPDATER = #{updater},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName},
      </if>
      <if test="registrationNumberId != null">
        REGISTRATION_NUMBER_ID = #{registrationNumberId},
      </if>
      <if test="productionMode != null">
        PRODUCTION_MODE = #{productionMode},
      </if>
      <if test="enterpriseName != null and enterpriseName != ''">
        ENTERPRISE_NAME = #{enterpriseName},
      </if>
      <if test="manufacturerId != null">
        MANUFACTURER_ID = #{manufacturerId},
      </if>
    </set>
    where PRODUCTION_MODE_ID = #{productionModeId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.trader.domain.entity.RegistrationProductionModeEntity">
    <!--@mbg.generated-->
    update T_REGISTRATION_PRODUCTION_MODE
    set ADD_TIME = #{addTime},
      MOD_TIME = #{modTime},
      CREATOR = #{creator},
      UPDATER = #{updater},
      CREATOR_NAME = #{creatorName},
      UPDATER_NAME = #{updaterName},
      REGISTRATION_NUMBER_ID = #{registrationNumberId},
      PRODUCTION_MODE = #{productionMode},
      ENTERPRISE_NAME = #{enterpriseName},
      MANUFACTURER_ID = #{manufacturerId}
    where PRODUCTION_MODE_ID = #{productionModeId}
  </update>

  <select id="queryRegistrationProductionMode" resultType="com.vedeng.erp.trader.dto.RegistrationProductionModeDto">
    SELECT <include refid="Base_Column_List" />     FROM T_REGISTRATION_PRODUCTION_MODE
    WHERE REGISTRATION_NUMBER_ID = #{registrationNumberId}
  </select>


  <delete id="deleteByRegistrationNumberId"   parameterType="java.lang.Integer">
    DELETE FROM T_REGISTRATION_PRODUCTION_MODE
    where REGISTRATION_NUMBER_ID = #{registrationNumberId}
  </delete>

</mapper>