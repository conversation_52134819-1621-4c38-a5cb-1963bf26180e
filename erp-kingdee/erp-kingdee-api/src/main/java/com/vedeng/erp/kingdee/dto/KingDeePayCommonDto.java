package com.vedeng.erp.kingdee.dto;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.infrastructure.kingdee.annotation.BusinessID;
import com.vedeng.infrastructure.kingdee.annotation.KingDeeID;
import com.vedeng.infrastructure.kingdee.annotation.WriteBackField;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.dto.KingDeeMqBaseDto;
import lombok.*;

import java.util.List;

/**
 * 应付单：标准应付单   https://www.yuque.com/manhuo/gf1570/sgy478
 * 标准应付单关联的源单是采购入库单和采购退料单，同一个接口，两种场景入参，请注意入参参数。未防止错误。
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@Getter
@Setter
@ToString
@AllArgsConstructor
@WriteBackField(needBackField = {"FID","FEntityDetail.FEntryID","FEntityDetail.F_QZOK_BDDJHID"})
public class KingDeePayCommonDto extends KingDeeMqBaseDto {

    /**
     * fId 0：表示新增
     * 非0：云星空系统单据FID值，表示修改
     */
    @WriteBackField
    @KingDeeID
    private String fId;

    /**
     * 单据日期 格式yyyy-MM-dd
     * fDate
     */
    private String fDate;
    /**
     * 贝登单据头ID
     */
    @BusinessID("F_QZOK_BDDJTID")
    private String fQzokBddjtId;

    /**
     * 业务类型 默认CG
     */
    private String fBusinessType;
    /**
     * 对应入库单 默认CG
     */
    private String fInStockBusType;

    /**
     * 立账类型 默认填写1
     */
    private String fSetAccountType;
    /**
     * fEntityDetail
     */
    private List<KingDeePayCommonDetailDto> fEntityDetail;

    /**
     * 单据类型 填单据类型编码，默认YFD01_SYS
     */

    private String fBillTypeID ;

    /**
     * 供应商 填写供应商编码
     */
    private String fSupplierId;

    /**
     * 结算组织 默认101,配置化
     */
    private String fSettleOrgId;

    /**
     * 付款组织 默认101,配置化
     */
    private String fPayOrgId;

    /**
     * 单据号
     */
    private String fBillNo;

    /**
     * 贝登erp对应的单据头ID
     */
    private String F_QZOK_BDDJTID;

    /**
     * 填写客户编码
     */
    private String FCUSTOMERID;


    public KingDeePayCommonDto() {
        // 默认0
        this.fId = "0";
        //业务类型
        this.fBusinessType = "CG";
        this.fInStockBusType = "CG";
        //结算组织
        this.fSettleOrgId = KingDeeConstant.ORG_ID.toString();
        //付款组织
        this.fPayOrgId = KingDeeConstant.ORG_ID.toString();
        //立账类型
        this.fSetAccountType = ErpConstant.ONE.toString();
        this.fBillTypeID = "YFD01_SYS";
    }

    @Override
    public String getFormId() {
        return KingDeeFormConstant.PAY_EXPENSES;
    }
}
