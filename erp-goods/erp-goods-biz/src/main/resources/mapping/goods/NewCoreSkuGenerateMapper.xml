<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.NewCoreSkuGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.dto.CoreSkuGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <id column="SKU_ID" jdbcType="INTEGER" property="skuId" />
    <result column="SPU_ID" jdbcType="INTEGER" property="spuId" />
    <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
    <result column="MODEL" jdbcType="VARCHAR" property="model" />
    <result column="SPEC" jdbcType="VARCHAR" property="spec" />
    <result column="SKU_NO" jdbcType="VARCHAR" property="skuNo" />
    <result column="SKU_NAME" jdbcType="VARCHAR" property="skuName" />
    <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
    <result column="MATERIAL_CODE" jdbcType="VARCHAR" property="materialCode" />
    <result column="SUPPLY_MODEL" jdbcType="VARCHAR" property="supplyModel" />
    <result column="IS_STOCKUP" jdbcType="VARCHAR" property="isStockup" />
    <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
    <result column="TECHNICAL_PARAMETER" jdbcType="VARCHAR" property="technicalParameter" />
    <result column="PERFORMANCE_PARAMETER" jdbcType="VARCHAR" property="performanceParameter" />
    <result column="SPEC_PARAMETER" jdbcType="VARCHAR" property="specParameter" />
    <result column="BASE_UNIT_ID" jdbcType="INTEGER" property="baseUnitId" />
    <result column="MIN_ORDER" jdbcType="DECIMAL" property="minOrder" />
    <result column="GOODS_LENGTH" jdbcType="DECIMAL" property="goodsLength" />
    <result column="GOODS_WIDTH" jdbcType="DECIMAL" property="goodsWidth" />
    <result column="GOODS_HEIGHT" jdbcType="DECIMAL" property="goodsHeight" />
    <result column="PACKAGE_LENGTH" jdbcType="DECIMAL" property="packageLength" />
    <result column="PACKAGE_WIDTH" jdbcType="DECIMAL" property="packageWidth" />
    <result column="PACKAGE_HEIGHT" jdbcType="DECIMAL" property="packageHeight" />
    <result column="NET_WEIGHT" jdbcType="DECIMAL" property="netWeight" />
    <result column="GROSS_WEIGHT" jdbcType="DECIMAL" property="grossWeight" />
    <result column="UNIT_ID" jdbcType="INTEGER" property="unitId" />
    <result column="CHANGE_NUM" jdbcType="DECIMAL" property="changeNum" />
    <result column="PACKING_LIST" jdbcType="VARCHAR" property="packingList" />
    <result column="AFTER_SALE_CONTENT" jdbcType="VARCHAR" property="afterSaleContent" />
    <result column="QA_YEARS" jdbcType="VARCHAR" property="qaYears" />
    <!--存储条件标准化 since ERP_LV_2020_67-->
    <result column="STORAGE_CONDITION_ONE" jdbcType="TINYINT" property="storageConditionOne" />
    <result column="STORAGE_CONDITION_TEMPERATURE_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionOneLowerValue" />
    <result column="STORAGE_CONDITION_TEMPERATURE_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionOneUpperValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityLowerValue" />
    <result column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE" jdbcType="FLOAT" property="storageConditionHumidityUpperValue" />
    <result column="STORAGE_CONDITION_TWO" jdbcType="VARCHAR" property="storageConditionTwo" />

    <result column="EFFECTIVE_DAY_UNIT" jdbcType="TINYINT" property="effectiveDayUnit" />
    <result column="EFFECTIVE_DAYS" jdbcType="VARCHAR" property="effectiveDays" />
    <result column="QA_RULE" jdbcType="VARCHAR" property="qaRule" />
    <result column="QA_OUT_PRICE" jdbcType="DECIMAL" property="qaOutPrice" />
    <result column="QA_RESPONSE_TIME" jdbcType="DECIMAL" property="qaResponseTime" />
    <result column="HAS_BACKUP_MACHINE" jdbcType="VARCHAR" property="hasBackupMachine" />
    <result column="SUPPLIER_EXTEND_GUARANTEE_PRICE" jdbcType="DECIMAL" property="supplierExtendGuaranteePrice" />
    <result column="CORE_PARTS_PRICE_FID" jdbcType="INTEGER" property="corePartsPriceFid" />
    <result column="RETURN_GOODS_CONDITIONS" jdbcType="TINYINT" property="returnGoodsConditions" />
    <result column="FREIGHT_INTRODUCTIONS" jdbcType="VARCHAR" property="freightIntroductions" />
    <result column="EXCHANGE_GOODS_CONDITIONS" jdbcType="VARCHAR" property="exchangeGoodsConditions" />
    <result column="EXCHANGE_GOODS_METHOD" jdbcType="VARCHAR" property="exchangeGoodsMethod" />
    <result column="GOODS_COMMENTS" jdbcType="VARCHAR" property="goodsComments" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="CHECKER" jdbcType="INTEGER" property="checker" />
    <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
    <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
    <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
    <result column="TAX_CATEGORY_NO" jdbcType="VARCHAR" property="taxCategoryNo" />
    <result column="JX_MARKET_PRICE" jdbcType="DECIMAL" property="jxMarketPrice" />
    <result column="JX_SALE_PRICE" jdbcType="DECIMAL" property="jxSalePrice" />
    <result column="JX_FLAG" jdbcType="INTEGER" property="jxFlag" />
    <result column="SOURCE" jdbcType="TINYINT" property="source" />
    <!-- add by Tomcat.Hui 2020/5/14 4:50 下午 .Desc: VDERP-2217 提供预计发货时间给前台. start -->
    <result column="DECLARE_DELIVERY_RANGE" jdbcType="VARCHAR" property="declareDeliveryRange" />
    <result column="DELIVERY_RANGE" jdbcType="VARCHAR" property="deliveryRange" />
    <!-- add by Tomcat.Hui 2020/5/14 4:50 下午 .Desc: VDERP-2217 提供预计发货时间给前台. end -->
    <result column="ON_SALE" jdbcType="TINYINT" property="onSale" />
    <result column="PUSH_STATUS" jdbcType="INTEGER" property="pushStatus" />
    <!--新增字段 -->
    <result column="GOODS_BARCODE" jdbcType="VARCHAR" property="goodsBarcode" />
    <result column="CURING_TYPE" jdbcType="TINYINT" property="curingType" />
    <result column="CURING_REASON" jdbcType="VARCHAR" property="curingReason" />
    <result column="REGULAR_MAINTAIN_TYPE" jdbcType="TINYINT" property="regularMaintainType" />
    <result column="REGULAR_MAINTAIN_REASON" jdbcType="VARCHAR" property="regularMaintainReason" />
    <result column="IS_NEED_TEST_REPROT" jdbcType="TINYINT" property="isNeedTestReprot" />
    <result column="IS_KIT" jdbcType="TINYINT" property="isKit" />
    <result column="KIT_DESC" jdbcType="VARCHAR" property="kitDesc" />
    <result column="IS_SAME_SN_CODE" jdbcType="TINYINT" property="isSameSnCode" />
    <result column="IS_FACTORY_SN_CODE" jdbcType="TINYINT" property="isFactorySnCode" />
    <result column="IS_MANAGE_VEDENG_CODE" jdbcType="TINYINT" property="isManageVedengCode" />
    <result column="IS_BAD_GOODS" jdbcType="TINYINT" property="isBadGoods" />
    <result column="IS_ENABLE_FACTORY_BATCHNUM" jdbcType="TINYINT" property="isEnableFactoryBatchnum" />
    <result column="IS_ENABLE_MULTISTAGE_PACKAGE" jdbcType="TINYINT" property="isEnableMultistagePackage" />
    <result column="INSTALL_TRAIN_TYPE" jdbcType="TINYINT" property="installTrainType" />
    <result column="LOGISTICS_DELIVERYTYPE" jdbcType="TINYINT" property="logisticsDeliveryType" />
    <result column="MID_PACKAGE_NUM" jdbcType="INTEGER" property="midPackageNum" />
    <result column="BOX_PACKAGE_NUM" jdbcType="INTEGER" property="boxPackageNum" />
    <result column="IS_ENABLE_VALIDITY_PERIOD" jdbcType="TINYINT" property="isEnableValidityPeriod" />
    <result column="NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="nearTermWarnDays" />
    <result column="OVER_NEAR_TERM_WARN_DAYS" jdbcType="INTEGER" property="overNearTermWarnDays" />
    <result column="IS_NEED_REPORT" jdbcType="TINYINT" property="isNeedReport" />
    <result column="IS_AUTHORIZED" jdbcType="TINYINT" property="isAuthorized" />
    <result column="HISTORY_NAME" jdbcType="VARCHAR" property="historyName" />
    <result column="IS_NAME_CHANGE" jdbcType="TINYINT" property="isNameChange" />
    <result column="GOODS_LEVEL_NO" jdbcType="INTEGER" property="goodsLevelNo" />
    <result column="GOODS_POSITION_NO" jdbcType="INTEGER" property="goodsPositionNo" />
    <result column="IS_INSTALLABLE" jdbcType="INTEGER" property="isInstallable" />
    <result column="SYNCHRONIZATION_STATUS" jdbcType="TINYINT" property="synchronizationStatus" />
    <result column="DISABLED_REASON" jdbcType="VARCHAR" property="disabledReason" />
    <result column="PURCHASE_TIME" jdbcType="INTEGER" property="perchaseTime" />
  </resultMap>


  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Sat Aug 10 17:13:52 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">

    SKU_ID, SPU_ID, CHECK_STATUS, MODEL, SPEC, SKU_NO, SKU_NAME, SHOW_NAME, MATERIAL_CODE,
    SUPPLY_MODEL, IS_STOCKUP, WIKI_HREF, TECHNICAL_PARAMETER, PERFORMANCE_PARAMETER,
    SPEC_PARAMETER, BASE_UNIT_ID, MIN_ORDER, GOODS_LENGTH, GOODS_WIDTH, GOODS_HEIGHT,
    PACKAGE_LENGTH, PACKAGE_WIDTH, PACKAGE_HEIGHT, NET_WEIGHT, GROSS_WEIGHT, UNIT_ID,
    CHANGE_NUM, PACKING_LIST, AFTER_SALE_CONTENT, QA_YEARS,REGULAR_MAINTAIN_TYPE,REGULAR_MAINTAIN_REASON,
    IS_AVAILABLE_SALE, ORG_ID_LIST, PUSHED_ORG_ID_LIST,CONFIGURATION_LIST,
    <include refid="GoodsStorageConditionColumn"/>,
    EFFECTIVE_DAY_UNIT, EFFECTIVE_DAYS, QA_RULE, QA_OUT_PRICE, QA_RESPONSE_TIME, HAS_BACKUP_MACHINE,
    SUPPLIER_EXTEND_GUARANTEE_PRICE, CORE_PARTS_PRICE_FID, RETURN_GOODS_CONDITIONS, FREIGHT_INTRODUCTIONS,
    EXCHANGE_GOODS_CONDITIONS, EXCHANGE_GOODS_METHOD, GOODS_COMMENTS, `STATUS`, ADD_TIME,
    CREATOR, MOD_TIME, UPDATER, CHECK_TIME, CHECKER, OPERATE_INFO_ID, DELETE_REASON,
    LAST_CHECK_REASON, TAX_CATEGORY_NO, JX_MARKET_PRICE, JX_SALE_PRICE, JX_FLAG, `SOURCE`,DECLARE_DELIVERY_RANGE,
ON_SALE, PUSH_STATUS,HISTORY_NAME,IS_NAME_CHANGE,
    GOODS_BARCODE,CURING_TYPE,CURING_REASON,IS_NEED_TEST_REPROT,IS_KIT,
    KIT_DESC,IS_SAME_SN_CODE,IS_FACTORY_SN_CODE,IS_MANAGE_VEDENG_CODE,IS_BAD_GOODS,
    IS_ENABLE_FACTORY_BATCHNUM,IS_ENABLE_MULTISTAGE_PACKAGE,
    MID_PACKAGE_NUM,BOX_PACKAGE_NUM,NEAR_TERM_WARN_DAYS,OVER_NEAR_TERM_WARN_DAYS,INSTALL_TRAIN_TYPE,LOGISTICS_DELIVERYTYPE,IS_ENABLE_VALIDITY_PERIOD,
    IS_NEED_REPORT,IS_AUTHORIZED,GOODS_LEVEL_NO,GOODS_POSITION_NO,IS_INSTALLABLE,SYNCHRONIZATION_STATUS,DISABLED_REASON,PURCHASE_TIME
  </sql>
  <sql id="GoodsStorageConditionColumn">
    STORAGE_CONDITION_ONE, STORAGE_CONDITION_ONE_LOWER_VALUE, STORAGE_CONDITION_ONE_UPPER_VALUE,
    STORAGE_CONDITION_HUMIDITY_LOWER_VALUE, STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,
    STORAGE_CONDITION_TWO
  </sql>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from V_CORE_SKU
    where SKU_ID = #{skuId,jdbcType=INTEGER}
  </select>
</mapper>