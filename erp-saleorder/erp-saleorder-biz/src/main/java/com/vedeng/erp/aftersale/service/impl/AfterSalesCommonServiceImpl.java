package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.common.constants.Contant;
import com.google.common.collect.Lists;
import com.vedeng.aftersales.component.vo.MapExtendsVo;
import com.vedeng.aftersales.dao.*;
import com.vedeng.aftersales.model.*;
import com.vedeng.aftersales.model.dto.AfterSalesPushRecordDTO;
import com.vedeng.aftersales.model.dto.AfterSalesUpdateRecordDto;
import com.vedeng.aftersales.model.dto.TradePushRecodeDto;
import com.vedeng.aftersales.model.vo.*;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.aftersales.util.SaleAfterServiceUtil;
import com.vedeng.authorization.dao.ActionMapper;
import com.vedeng.authorization.dao.RegionMapper;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Action;
import com.vedeng.authorization.model.Region;
import com.vedeng.authorization.model.User;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.billsync.dao.generate.BankBillMapper;
import com.vedeng.billsync.task.model.entity.generate.BankBillDo;
import com.vedeng.common.constant.*;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.exception.RunTimeServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.service.BaseService;
import com.vedeng.common.shiro.JedisUtils;
import com.vedeng.common.util.*;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.erp.aftersale.domain.dto.*;
import com.vedeng.erp.aftersale.domain.entity.*;
import com.vedeng.erp.aftersale.enums.NewAfterRefundStatusEnum;
import com.vedeng.erp.aftersale.enums.NewAfterSalesStatusEnum;
import com.vedeng.erp.aftersale.feign.AfterSaleOrderYxbApi;
import com.vedeng.erp.aftersale.mapper.AfterSalesInstallServiceRecordDetailMapper;
import com.vedeng.erp.aftersale.mapper.AfterSalesInstallServiceRecordMapper;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesFollowUpRecordConvertor;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesInstallServiceRecordConvertor;
import com.vedeng.erp.aftersale.mapstruct.AfterSalesInstallServiceRecordDetailConvertor;
import com.vedeng.erp.aftersale.service.AfterSalesBillToYxbApiService;
import com.vedeng.erp.aftersale.service.AfterSalesCommonService;
import com.vedeng.erp.aftersale.service.AfterSalesFollowUpRecordService;
import com.vedeng.erp.aftersale.service.AfterSalesToYxbApiService;
import com.vedeng.erp.finance.dto.CapitalBillDto;
import com.vedeng.erp.finance.dto.PayApplyDto;
import com.vedeng.erp.finance.service.CapitalBillApiService;
import com.vedeng.erp.finance.service.PayApplyApiService;
import com.vedeng.erp.kingdee.enums.KingDeeTraderModeEnum;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDetailDto;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDto;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.finance.constant.PayApplyTypeEnum;
import com.vedeng.finance.dao.*;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.vo.PayApplyDetailVo;
import com.vedeng.finance.model.vo.PayApplyVo;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.wxrobot.dto.WxMsgDto;
import com.vedeng.infrastructure.wxrobot.service.WxRobotService;
import com.vedeng.logistics.dao.WarehouseGoodsOutInItemMapper;
import com.vedeng.logistics.dao.WarehouseGoodsOutInMapper;
import com.vedeng.logistics.eums.WarehouseGoodsInEnum;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.service.WarehouseGoodsInService;
import com.vedeng.logistics.service.WarehouseGoodsOutService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.OrderData;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.model.vo.SaleorderVo;
import com.vedeng.order.service.*;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.orderstream.aftersales.dao.AfterSalesDirectInfoMapper;
import com.vedeng.orderstream.aftersales.dao.RInstallstionJGoodsMapper;
import com.vedeng.orderstream.aftersales.dao.ReturnVisitRecordMapper;
import com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo;
import com.vedeng.orderstream.aftersales.model.AfterSalesFollowUpRecord;
import com.vedeng.orderstream.aftersales.model.RInstallstionJGoods;
import com.vedeng.orderstream.aftersales.model.ReturnVisitRecord;
import com.vedeng.soap.service.VedengSoapService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.SysOptionDefinition;
import com.vedeng.system.service.RegionService;
import com.vedeng.trader.dao.*;
import com.vedeng.trader.model.*;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderSupplierVo;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> simgo.wang
 * @date : 2021/10/15 16:22
 * @desc :
 */
@Service
public class AfterSalesCommonServiceImpl implements AfterSalesCommonService {

    public static Logger logger = LoggerFactory.getLogger(AfterSalesCommonServiceImpl.class);


    private ProcessEngine getProcessEngine() {
        return ProcessEngines.getDefaultProcessEngine();// 流程引擎
    }

    @Resource
    private AfterSalesInstallstionMapper afterSalesInstallstionMapper;

    @Resource
    private RInstallstionJGoodsMapper rInstallstionJGoodsMapper;

    @Autowired
    private WxRobotService wxRobotService;

    @Resource
    private EngineerMapper engineerMapper;

    @Value("${redis_dbtype}")
    protected String dbType;

    @Value("${api_http}")
    protected String httpUrl;

    @Resource
    private RegionMapper regionMapper;

    @Autowired
    private AfterSalesDirectInfoMapper afterSalesDirectInfoMapper;

    @Resource
    private UserMapper userMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    @Resource
    private CapitalBillMapper capitalBillMapper;

    @Resource
    private AfterSalesDetailMapper afterSalesDetailMapper;

    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private InvoiceApplyMapper invoiceApplyMapper;
    @Resource
    private RegionService regionService;

    @Resource
    private WarehouseGoodsOutInItemMapper warehouseGoodsOutInItemMapper;

    @Resource
    private ActionMapper actionMapper;

    @Resource
    private AttachmentMapper attachmentMapper;

    @Value("${file_url}")
    private String fileUrl;

    @Value("${oss_url}")
    private String ossUrl;

    @Resource
    private BuyorderDataService buyorderDataService;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private TraderContactMapper traderContactMapper;

    @Resource
    private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;

    @Resource
    private BaseService baseService;

    @Resource
    private AfterSalesBillToYxbApiService afterSalesBillToYxbApiService;
    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Resource
    private TraderMapper traderMapper;

    @Resource
    private PayApplyMapper payApplyMapper;

    @Resource
    private AfterSalesDataService afterSalesDataService;

    @Resource
    private InvoiceAfterMapper invoiceAfterMapper;

    @Resource
    private CapitalBillDetailMapper capitalBillDetailMapper;

    @Resource
    private BankBillMapper bankBillMapper;

    @Resource
    private TraderCustomerMapper traderCustomerMapper;

    @Resource
    private PayApplyDetailMapper payApplyDetailMapper;

    @Resource
    private TraderFinanceMapper traderFinanceMapper;
    @Autowired
    private AfterSalesFollowUpRecordService afterSalesFollowUpRecordService;
    @Autowired
    private OrderAccountPeriodService orderAccountPeriodService;

    @Resource
    private OrderCommonService orderCommonService;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private VedengSoapService vedengSoapService;

    @Resource
    private WebAccountMapper webAccountMapper;

    @Autowired
    @Qualifier("hcSaleorderService")
    private HcSaleorderService hcSaleorderService;

    @Autowired
    private BussinessChanceService bussinessChanceService;

    @Resource
    private TraderSupplierMapper traderSupplierMapper;

    @Resource
    private InvoiceMapper invoiceMapper;

    @Resource
    private InvoiceDetailMapper invoiceDetailMapper;

    @Resource
    private AfterSalesService afterSalesService;
    @Resource
    private CapitalBillService capitalBillService;
    @Resource
    private OrderNoDict orderNoDict;
    @Resource
    private AfterSalesInstallServiceRecordMapper afterSalesInstallServiceRecordMapper;

    @Autowired
    private AfterSalesInstallServiceRecordDetailMapper afterSalesInstallServiceRecordDetailMapper;

    @Resource
    private AfterSaleBuyorderDirectOutLogMapper afterSaleBuyorderDirectOutLogMapper;
    @Resource
    private SaleorderSyncService saleorderSyncService;

    @Autowired
    private AfterSalesFollowUpRecordConvertor afterSalesFollowUpRecordConvertor;

    @Autowired
    private WarehouseGoodsOutService warehouseGoodsOutService;
    @Resource
    private WarehouseGoodsInService warehouseGoodsInService;
    @Autowired
    private WarehouseGoodsOutInMapper warehouseGoodsOutInMapper;
    @Autowired
    private AfterSalesInstallServiceRecordConvertor afterSalesInstallServiceRecordConvertor;
    @Autowired
    private AfterSalesInstallServiceRecordDetailConvertor afterSalesInstallServiceRecordDetailConvertor;
    @Autowired
    private AfterSalesToYxbApiService afterSalesToYxbApiService;
    @Autowired
    private ReturnVisitRecordMapper returnVisitRecordMapper;
    @Autowired
    private CapitalBillApiService capitalBillApiService;
    @Autowired
    @Qualifier("newAfterSalesDetailMapper")
    private com.vedeng.erp.aftersale.mapper.AfterSalesDetailMapper newAfterSalesDetailMapper;

    @Autowired
    OssUtilsService ossUtilsService;
    //改变售后单状态
    @Autowired
    private PayApplyApiService payApplyApiService;
    //取消,完结，关闭医修帮工单
    private static final String CANCEL_DISPATCH_URL = "api/updateAftersalesStatus";
    //下派医修帮工单
    private static final String CONFIRMATION_DISPATCH_URL = "api/syncAftersalesOrder";
    //推送医修帮交易付款记录
    private static final String PUSH_YXB_URL = "api/addTradeRecode";
    @Resource
    private AfterSaleOrderYxbApi afterSaleOrderYxbApi;
    @Value("${yxbToken}")
    private String yxbToken;

    @Value("${yxb_PayRecord_RobotNum}")
    private String yxbPayRecordRobotNum;

    @Autowired
    private AfterSalesService afterSalesOrderService;
    
    @Override
    public int saveAfterSalesEngineer(AfterSalesInstallstionVo afterSalesInstallstionVo) {

        AfterSalesInstallstion afterSalesInstallstion = new AfterSalesInstallstion();
        afterSalesInstallstion.setUpdater(afterSalesInstallstionVo.getUpdater());
        afterSalesInstallstion.setModTime(afterSalesInstallstionVo.getModTime());
        afterSalesInstallstion.setAfterSalesId(afterSalesInstallstionVo.getAfterSalesId());
        afterSalesInstallstion.setEngineerAmount(afterSalesInstallstionVo.getEngineerAmount());
        afterSalesInstallstion.setEngineerId(afterSalesInstallstionVo.getEngineerId());
        afterSalesInstallstion.setServiceTime(afterSalesInstallstionVo.getServiceTime());

        int res = 0;
        //存在ID即修改
        if (afterSalesInstallstionVo.getAfterSalesInstallstionId() != null && !ErpConst.ZERO.equals(afterSalesInstallstionVo.getAfterSalesInstallstionId())) {
            afterSalesInstallstion.setAfterSalesInstallstionId(afterSalesInstallstionVo.getAfterSalesInstallstionId());
            res = afterSalesInstallstionMapper.update(afterSalesInstallstion);
        } else {
            AfterSalesInstallstionVo aiv = afterSalesInstallstionMapper.getEditAfterSalesInstallstionVo(afterSalesInstallstionVo);
            if (aiv == null) {
                afterSalesInstallstion.setAddTime(afterSalesInstallstionVo.getAddTime());
                afterSalesInstallstion.setCreator(afterSalesInstallstionVo.getCreator());
                res = afterSalesInstallstionMapper.insert(afterSalesInstallstion);
            } else {
                res = -1;
            }
        }

        if (res > 0) {
            //编辑状态先删除与安调关联的产品
            if (afterSalesInstallstionVo.getAfterSalesInstallstionId() != null && afterSalesInstallstionVo.getAfterSalesInstallstionId() != 0) {
                RInstallstionJGoods rin = new RInstallstionJGoods();
                rin.setAfterSalesInstallstionId(afterSalesInstallstionVo.getAfterSalesInstallstionId());
                rInstallstionJGoodsMapper.delRInstallstionJGoods(rin);

            }

            String[] nums = afterSalesInstallstionVo.getAfterSalesNums();
            if (nums != null) {
                // VDERP-11050 记录需要保留的售后安调商品
                List<Integer> keepGoodsId = new ArrayList<>();
                for (String i : nums) {
                    if (!"".equals(i)) {
                        Integer afterSalesGoodsId = Integer.valueOf(i.split("\\|")[0]);
                        Integer num = Integer.valueOf(i.split("\\|")[1]);
                        RInstallstionJGoods rInstallstionJGoods = new RInstallstionJGoods();
                        rInstallstionJGoods.setAfterSalesGoodsId(afterSalesGoodsId);
                        rInstallstionJGoods.setAfterSalesInstallstionId(afterSalesInstallstion.getAfterSalesInstallstionId());
                        rInstallstionJGoods.setNum(num);
                        res = rInstallstionJGoodsMapper.insertSelective(rInstallstionJGoods);

                        if (afterSalesInstallstionVo.getIsAtwx() != null && afterSalesInstallstionVo.getIsAtwx() == 1) {
                            // VDERP-11050
                            AfterSalesGoods afg = new AfterSalesGoods();
                            afg.setAfterSalesGoodsId(afterSalesGoodsId);
                            afg.setNum(num);
                            afterSalesGoodsMapper.updateByPrimaryKeySelective(afg);
                            keepGoodsId.add(afterSalesGoodsId);
                        }

                        if (res == 0) {
                            break;
                        }
                    }
                }

                if (keepGoodsId.size() > 0) {
                    afterSalesGoodsMapper.deleteUnInstallGoods(afterSalesInstallstionVo.getAfterSalesId(), keepGoodsId);
                }
            }
        }
        return res;
    }

    @Override
    public Map<String, Object> getEngineerPage(AfterSalesVo afterSales, Page page) {

        try {

            Map<String, Object> map = new HashMap<>();
            EngineerVo engineerVo = new EngineerVo();
            //engineerVo.setAfterSalesId(afterSales.getAfterSalesId());
            engineerVo.setAreaId(afterSales.getAreaId());
            engineerVo.setName(afterSales.getName());
            engineerVo.setCompanyId(afterSales.getCompanyId());
            map.put("engineerVo", engineerVo);
            map.put("page", page);
            List<EngineerVo> evlist = engineerMapper.getEngineerVoListPage(map);
            if (CollectionUtils.isNotEmpty(evlist)) {
                evlist.stream().forEach(s -> {
                    s.setAreaStr(getAddressByAreaId(s.getAreaId()));
                });
            }
            map.put("list", evlist);
            return map;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }

    }

    /**
     * 获取地址
     *
     * @param areaId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年5月16日 下午5:30:03
     */
    @Override
    public String getAddressByAreaId(Integer areaId) {
        if (ObjectUtils.isEmpty(areaId)) {
            return null;
        }
        Region re = getRegionByAreaId(areaId);
        StringBuffer sb = new StringBuffer();
        if (re != null) {
            if (ErpConst.THREE.equals(re.getRegionType())) {
                String zoneName = re.getRegionName();
                re = getRegionByAreaId(re.getParentId());
                String cityName = "";
                String provName = "";
                if (re != null) {
                    cityName = re.getRegionName();
                    re = getRegionByAreaId(re.getParentId());
                    provName = re.getRegionName();
                }

                sb.append(provName).append(" ").append(cityName).append(" ").append(zoneName);
            } else if (ErpConst.TWO.equals(re.getRegionType())) {
                String cityName = re.getRegionName();
                re = getRegionByAreaId(re.getParentId());
                String provName = re.getRegionName();
                sb.append(provName).append(" ").append(cityName);
            } else {
                String provName = re.getRegionName();
                sb.append(provName);
            }
        }
        return sb.toString();
    }


    @Override
    public ResultInfo updateLockSaleorderWarning(Integer saleorderId) {
        try {
            List<SaleorderGoods> saleorderGoodsLists = saleorderMapper.getSaleorderGoodsBySaleOrderId(saleorderId);
            if (CollectionUtils.isEmpty(saleorderGoodsLists)) {
                return ResultInfo.success("锁定更新销售单预警成功");
            }
            saleorderGoodsLists.forEach(item -> {
                SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                saleorderGoodsVo.setSaleorderGoodsId(item.getSaleorderGoodsId());
                saleorderGoodsVo.setWarnLevel(null);
                saleorderGoodsMapper.updateWarnStatusSelective(saleorderGoodsVo);
            });
        } catch (Exception e) {
            logger.info("锁定销售单时，清除预警失败，销售单ID：{},错误原因：{}", saleorderId, e);
            return ResultInfo.error("锁定更新销售单预警失败");
        }
        return ResultInfo.success("锁定更新销售单预警成功");
    }

    @Override
    public ResultInfo saveInstallstionScore(AfterSalesInstallstion afterSalesInstallstion) {
        try {
            Boolean succ = true;
            int update = afterSalesInstallstionMapper.update(afterSalesInstallstion);
            if (update > 0) {
                AfterSalesInstallstion installstion = afterSalesInstallstionMapper.selectByPrimaryKey(afterSalesInstallstion.getAfterSalesInstallstionId());
                //更新工程师总体评分平均值
                AfterSalesInstallstionVo scoreInfoByEngineer = afterSalesInstallstionMapper.getScoreInfoByEngineer(installstion.getEngineerId());

                if (scoreInfoByEngineer.getScoreTimes() > 0) {
                    DecimalFormat df2 = new DecimalFormat("0.0");
                    BigDecimal serviceScore = new BigDecimal(df2.format((float) scoreInfoByEngineer.getServiceScoreTotal() / scoreInfoByEngineer.getScoreTimes()));
                    BigDecimal skillScore = new BigDecimal(df2.format((float) scoreInfoByEngineer.getSkillScoreTotal() / scoreInfoByEngineer.getScoreTimes()));
                    Engineer engineer = new Engineer();
                    engineer.setEngineerId(installstion.getEngineerId());
                    engineer.setServiceScore(serviceScore);
                    engineer.setSkillScore(skillScore);
                    engineerMapper.updateByPrimaryKey(engineer);
                }
            } else {
                succ = false;
            }
            if (succ) {
                return ResultInfo.success("操作成功");
            }
            return new ResultInfo();
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return new ResultInfo();
        }
    }

    @Override
    public void saveCollectionAmountStatus(Integer afterSalesId, Integer status) {
        afterSalesMapper.saveCollectionAmountStatus(afterSalesId, status);
    }

    @Override
    public void savePayAmountStatuts(Integer afterSalesId, Integer status) {
        afterSalesMapper.savePayAmountStatuts(afterSalesId, status);
    }

    @Override
    public void saveMakeOutInvoiceStatus(Integer afterSalesId, Integer status) {
        afterSalesMapper.saveMakeOutInvoiceStatus(afterSalesId, status);
    }

    @Override
    public AfterSalesVo getAfterSalesVoListById(AfterSales afterSales) {

        try {
            AfterSalesVo asv = afterSalesMapper.getAfterSalesVoListById(afterSales);

            // begin modify by franlin at 2018-05-04 for bug [3270 销售换货出库，出库完成页面未展示客户名称信息 ]
            // 当businessType=540 即 销售业务出库--出库完成页面[客户名称]不展示 修复 通过T_AFTER_SALES中ORDER_ID来查询 表 [T_SALEORDER/T_BUYORDER]
            if (null != afterSales.getBusinessType() && null != asv && null != asv.getOrderId() && null == asv.getTraderId() && null == asv.getTraderName() && afterSales.getScenesPageType() != 0) {
                if (SysOptionConstant.ID_540.equals(afterSales.getBusinessType()) || ErpConst.ONE.equals(afterSales.getBusinessType())) {
                    asv.setBusinessType(ErpConst.ONE);
                } else {
                    asv.setBusinessType(ErpConst.TWO);
                }
                AfterSalesVo asvo = afterSalesMapper.getTranderInfoById(asv);
                if (null != asvo) {
                    // 设置交易ID
                    asv.setTraderId(asvo.getTraderId());
                    // 设置交易名称即客户名称
                    asv.setTraderName(asvo.getTraderName());
                }
            }
            // end modify by franlin at 2018-05-04 for bug [3270 销售换货出库，出库完成页面未展示客户名称信息 ]

            return asv;
        } catch (Exception e) {
            logger.error("getAfterSalesVoListById :{}", e);
            return null;
        }
    }

    @Override
    public void putOrderPayStatustoHC(Saleorder saleorderInfo) {
        try {
            if (saleorderInfo != null && saleorderInfo.getOrderType() != null && saleorderInfo.getPaymentStatus() != null && saleorderInfo.getOrderType() == 5 && saleorderInfo.getPaymentStatus() > 0) {
                Map<String, Object> maptemp = new HashMap<String, Object>();
                maptemp.put("orderNo", saleorderInfo.getSaleorderNo());
                maptemp.put("orderStatus", 1);// 已付款
                maptemp.put("isPay", 1);// 已支付
                maptemp.put("payTime", System.currentTimeMillis());
                // 查询支付方式
                Integer tradeMode = capitalBillMapper.getTradeModeByOrderNo(saleorderInfo.getSaleorderNo());
                if (tradeMode == 520) {// 支付宝
                    maptemp.put("payType", 1);
                } else if (tradeMode == 521) {// 银行
                    maptemp.put("payType", 3);
                } else if (tradeMode == 522) {// 微信
                    maptemp.put("payType", 2);
                }
                // 调用接口推送到耗材商城
                hcSaleorderService.putOrderPayStatustoHC(maptemp);
            }
        } catch (Exception e) {
            logger.error("调用接口推送到耗材商城", e);
        }
    }

    @Override
    public ResultInfo saveAddCapitalBillSimple(CapitalBill cb, Integer traderType, Integer traderMode,
                                               Integer bussinessType, BigDecimal amount, Integer detailTraderType, Integer traderId) {
        if (amount.compareTo(BigDecimal.ZERO) != 0) {
            CapitalBill capitalBill = new CapitalBill();
            List<CapitalBillDetail> capitalBillDetailso = new ArrayList<>();
            CapitalBillDetail capitalBillDetail2 = new CapitalBillDetail();
            capitalBill = new CapitalBill();
            capitalBill.setBankBillId(cb.getBankBillId());
            capitalBill.setTranFlow(cb.getTranFlow());
            capitalBill.setCreator(cb.getCreator());
            capitalBill.setAddTime(DateUtil.sysTimeMillis());
            capitalBill.setCurrencyUnitId(1);
            capitalBill.setTraderTime(DateUtil.sysTimeMillis());
            capitalBill.setTraderType(traderType);// 收入
            capitalBill.setTraderSubject(cb.getTraderSubject());
            capitalBill.setTraderMode(traderMode);// 银行
            capitalBill.setAmount(amount);
            capitalBill.setComments(cb.getComments());
            capitalBill.setPayee(cb.getPayee());
            capitalBill.setPayer(cb.getPayer());
            capitalBill.setCompanyId(cb.getCompanyId());
            capitalBillDetailso = new ArrayList<>();
            capitalBillDetail2 = new CapitalBillDetail();
            capitalBillDetail2.setAmount(capitalBill.getAmount());
            capitalBillDetail2.setBussinessType(bussinessType);// 订单收款
            capitalBillDetail2.setOrderType(cb.getCapitalBillDetail().getOrderType());
            capitalBillDetail2.setRelatedId(cb.getCapitalBillDetail().getRelatedId());
            capitalBillDetail2.setOrderNo(cb.getCapitalBillDetail().getOrderNo());
            capitalBillDetail2.setTraderId(traderId);
            capitalBillDetail2.setTraderType(detailTraderType);
            capitalBillDetail2.setUserId(cb.getCapitalBillDetail().getUserId());
            capitalBillDetail2.setOrgId(cb.getCapitalBillDetail().getOrgId());
            capitalBillDetail2.setOrgName(cb.getCapitalBillDetail().getOrgName());
            capitalBillDetailso.add(capitalBillDetail2);
            capitalBill.setCapitalBillDetails(capitalBillDetailso);
            capitalBill.setBdPayStatusFlag(cb.getBdPayStatusFlag());
            // add by Randy.Xu 2021/2/26 17:40 .Desc: . begin
            //支付宝结款，添加交易流水
            capitalBill.setCapitalSearchFlow(cb.getCapitalSearchFlow());
            // add by Randy.Xu 2021/2/26 17:40 .Desc: . end

            // 账期支付处理
            if (traderMode == 527) {
                capitalBill.setTraderSubject(1);
                capitalBill.setComments("");

                // 根据traderId获取交易者名称
                Trader traderInfo = traderMapper.selectByPrimaryKey(traderId);
                if (detailTraderType == 1) {
                    capitalBill.setPayer(traderInfo.getTraderName());
                } else if (detailTraderType == 2) {
                    capitalBill.setPayee(traderInfo.getTraderName());
                }
            }
            logger.info("saveAddCapitalBillSimple insert:{}", capitalBill.toString());
            int i = capitalBillMapper.insertSelective(capitalBill);
            if (i == 1) {
                Integer capitalBillId = capitalBill.getCapitalBillId();
                // 生成资金流水号
                CapitalBill capitalBillExtra = new CapitalBill();
                capitalBillExtra.setCapitalBillId(capitalBillId);
                capitalBillExtra.setCapitalBillNo(orderNoDict.getOrderNum(capitalBillId, 10));
                logger.info("saveAddCapitalBillSimple 资金流水号:{}", capitalBillExtra.getCapitalBillNo());
                capitalBillMapper.updateByPrimaryKeySelective(capitalBillExtra);

                if (null != capitalBill.getCapitalBillDetails()) {
                    List<CapitalBillDetail> capitalBillDetails = capitalBill.getCapitalBillDetails();
                    logger.info("saveAddCapitalBillSimple insertdetail:{}", capitalBillDetails.toString());
                    for (CapitalBillDetail capitalBillDetail : capitalBillDetails) {
                        capitalBillDetail.setCapitalBillId(capitalBillId);
                        capitalBillDetailMapper.insertSelective(capitalBillDetail);
                    }
                }
                Saleorder saleorderInfo = null;
                if (cb.getCapitalBillDetail().getOrderType().equals(1)) {
                    // 查询订单基本信息
                    saleorderInfo = saleorderMapper.getBaseSaleorderInfo(cb.getCapitalBillDetail().getRelatedId());
                }
                //BD订单状态
                // changed by Tomcat.Hui 2020/3/6 9:10 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分 部分付款和全部付款都需要通知 .
                if (saleorderInfo != null && saleorderInfo.getOrderType() != null
                        && saleorderInfo.getOrderType() <= 1
                        && saleorderInfo.getPaymentStatus() != null
                        && (saleorderInfo.getPaymentStatus() == 2 || saleorderInfo.getPaymentStatus() == 1)
                        && cb.getBdPayStatusFlag()) {
                    //更新BD订单前台状态
                    OrderData orderData = new OrderData();
                    orderData.setOrderNo(saleorderInfo.getSaleorderNo());
                    // changed by Tomcat.Hui 2020/3/6 9:10 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分 部分付款和全部付款都需要通知 .
                    orderData.setOrderStatus(saleorderInfo.getPaymentStatus() == 2 ? 3 : 7);//发货

                    //重构
                    saleorderSyncService.syncSaleorderStatus2Mjx(saleorderInfo.getSaleorderId()
                            , PCOrderStatusEnum.get(saleorderInfo.getPaymentStatus() == 2 ? 3 : 7), SaleorderSyncEnum.CAPITAL_BILL);
                }

                putOrderPayStatustoHC(saleorderInfo);

                return new ResultInfo(0, "操作成功", capitalBillId);

            } else {
                return new ResultInfo();
            }
        } else {
            return new ResultInfo(-1, "资金流水金额为零");
        }

    }

    /**
     * 获取流水ID
     *
     * @param resultInfo
     * @return
     */
    private Integer getCapitalBillId(ResultInfo resultInfo) {
        logger.info("getCapitalBillId resultInfo:{}", JSON.toJSONString(resultInfo));
        if (resultInfo == null || resultInfo.getCode() == null || !resultInfo.getCode().equals(0)) {
            return 0;
        }
        int capitalBillId = 0;
        try {
            capitalBillId = Integer.parseInt(resultInfo.getData().toString());
        } catch (NumberFormatException e) {
            logger.error("getCapitalBillId warn :{}", e);
            return 0;
        }
        return capitalBillId;
    }

    @Override
    public ResultInfo updatePaymentStatusBySaleorderId(Integer saleorderId, CapitalBill cb) {
        Saleorder saleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorderId);
        // 订单账期金额(已收款) --- 已收款账期金额减去退还账期金额
        BigDecimal periodAmountExtra = saleorderMapper.getPeriodAmount(saleorderId);
        periodAmountExtra = periodAmountExtra == null ? new BigDecimal(0) : periodAmountExtra;

        // 订单实际预付款(A)
        BigDecimal realPreAmount = null;
        if (null != saleorderInfo && null != saleorderInfo.getOrderType() && 5 == saleorderInfo.getOrderType()) {
            // 耗材退款金额不能用订单单价*数量
            realPreAmount = saleorderMapper.getRealPreAmountForHcOrder(saleorderId);
        } else {
            realPreAmount = saleorderMapper.getRealPreAmount(saleorderId);
        }
        realPreAmount = realPreAmount == null ? new BigDecimal(0) : realPreAmount;

        // 订单账期(B)
        BigDecimal accountPeriodAmount = saleorderInfo.getAccountPeriodAmount();
        accountPeriodAmount = accountPeriodAmount == null ? new BigDecimal(0) : accountPeriodAmount;

        // 未归还账期(D)
        BigDecimal lackAccountPeriodAmount = saleorderMapper.getSaleorderLackAccountPeriodAmount(saleorderId);
        lackAccountPeriodAmount = lackAccountPeriodAmount == null ? new BigDecimal(0) : lackAccountPeriodAmount;

        // 订单已收款金额(实际收款)(C)
        BigDecimal paymentAmount = saleorderMapper.getSaleorderPaymentAmount(saleorderId).add(periodAmountExtra).subtract(lackAccountPeriodAmount);
        paymentAmount = paymentAmount == null ? new BigDecimal(0) : paymentAmount;

        // 订单实际金额（减去售后数量后）(E)
        BigDecimal realAmount = saleorderMapper.getRealAmount(saleorderId);
        realAmount = realAmount == null ? new BigDecimal(0) : realAmount;

        // 本次收款金额(F)
        BigDecimal thisPaymentAmount = (cb.getAmount() == null ? new BigDecimal(0) : cb.getAmount());
        // 订单尾款金额(G)
        BigDecimal retainageAmount = (saleorderInfo.getRetainageAmount() == null ? new BigDecimal(0) : saleorderInfo.getRetainageAmount());

        // 获取当前销售订单的销售归属人员
        RTraderJUser rTraderJUser = new RTraderJUser();
        rTraderJUser.setTraderId(saleorderInfo.getTraderId());
        rTraderJUser.setTraderType(ErpConst.ONE);// 经销商（包含终端）
        RTraderJUser rTraderJUserInfo = traderMapper.getRTraderUser(rTraderJUser);
        if (null != rTraderJUserInfo) {
            cb.getCapitalBillDetail().setUserId(rTraderJUserInfo.getUserId());
        } else {
            cb.getCapitalBillDetail().setUserId(0);
        }

        // 先判断是否是账期订单
        Integer traderMode = cb.getTraderMode();
        Integer traderType = cb.getTraderMode() == 528 ? 4 : 1;// 如果是余额支付那么交易类型为

        BigDecimal creditRepayment = BigDecimal.ZERO;
        Integer capitalBillId = 0;
        // 转入
        if (saleorderInfo.getHaveAccountPeriod() == 1) {// 是账期订单
            if (paymentAmount.add(thisPaymentAmount).compareTo(realPreAmount) == -1) {// C+F<A
                // 订单收款 F
                this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, thisPaymentAmount, 1, saleorderInfo.getTraderId());
            } else if (paymentAmount.add(thisPaymentAmount).compareTo(realPreAmount) >= 0 // C+F>=A
                    && paymentAmount.add(thisPaymentAmount).compareTo(realAmount) < 1) {// C+F<=E
                if (paymentAmount.compareTo(realPreAmount) >= 0) {// C>=A
                    if (thisPaymentAmount.compareTo(lackAccountPeriodAmount) < 1) {// F<=D
                        // 账期归还 F
                        creditRepayment = thisPaymentAmount;
                        ResultInfo resultInfo = this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                                thisPaymentAmount, 1, saleorderInfo.getTraderId());
                        capitalBillId = getCapitalBillId(resultInfo);
                    } else {
                        // 账期归还D
                        creditRepayment = lackAccountPeriodAmount;
                        ResultInfo resultInfo = this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                                lackAccountPeriodAmount, 1, saleorderInfo.getTraderId());
                        capitalBillId = getCapitalBillId(resultInfo);
                        if (saleorderInfo.getPaymentType() != 419 && retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                            // 尾款收款 F-D
                            this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, thisPaymentAmount.subtract(lackAccountPeriodAmount), 1, saleorderInfo.getTraderId());
                        }
                    }
                } else {
                    // 订单收款 A-C
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, realPreAmount.subtract(paymentAmount), 1, saleorderInfo.getTraderId());

                    Integer isAccountPeriod = capitalBillMapper.isAccountPeriod(1, saleorderInfo.getSaleorderId());
                    if (isAccountPeriod == 0) {
                        // 账期支付 B
                        this.saveAddCapitalBillSimple(cb, 3, 527, 526, accountPeriodAmount, 1, saleorderInfo.getTraderId());
                    }
                    lackAccountPeriodAmount = saleorderMapper.getSaleorderLackAccountPeriodAmount(saleorderId);
                    if (paymentAmount.add(thisPaymentAmount).subtract(realPreAmount).compareTo(lackAccountPeriodAmount) < 1) {// C+F-A<=D
                        // 账期还款 C+F-A
                        creditRepayment = paymentAmount.add(thisPaymentAmount).subtract(realPreAmount);
                        ResultInfo resultInfo = this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                                paymentAmount.add(thisPaymentAmount).subtract(realPreAmount), 1, saleorderInfo.getTraderId());
                        capitalBillId = getCapitalBillId(resultInfo);
                    } else {
                        // 账期归还D
                        creditRepayment = lackAccountPeriodAmount;
                        ResultInfo resultInfo = this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                                lackAccountPeriodAmount, 1, saleorderInfo.getTraderId());
                        capitalBillId = getCapitalBillId(resultInfo);
                        if (saleorderInfo.getPaymentType() != 419 && retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                            // 尾款收款C+F-A-D
                            this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526,
                                    paymentAmount.add(thisPaymentAmount).subtract(realPreAmount).subtract(lackAccountPeriodAmount), 1, saleorderInfo.getTraderId());
                        }
                    }
                }
            } else if (paymentAmount.add(thisPaymentAmount).compareTo(realAmount) == 1) {// C+F>E
                if (paymentAmount.compareTo(realAmount) >= 0) {// C>=E
                    // 订单收款 F
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, thisPaymentAmount, 1,
                            saleorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realAmount) == -1 && paymentAmount.compareTo(realPreAmount) >= 0) {// C<E && C>=A
                    // 账期归还 E-C-G
                    creditRepayment = realAmount.subtract(paymentAmount).subtract(retainageAmount);
                    ResultInfo resultInfo = this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                            realAmount.subtract(paymentAmount).subtract(retainageAmount), 1, saleorderInfo.getTraderId());
                    capitalBillId = getCapitalBillId(resultInfo);
                    if (saleorderInfo.getPaymentType() != 419 && retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款 G
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, retainageAmount, 1, saleorderInfo.getTraderId());
                    }
                    // 订单收款C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, paymentAmount.add(thisPaymentAmount).subtract(realAmount), 1, saleorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realPreAmount) == -1) {// C<A
                    // 订单收款 A-C
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, realPreAmount.subtract(paymentAmount), 1, saleorderInfo.getTraderId());

                    Integer isAccountPeriod = capitalBillMapper.isAccountPeriod(1, saleorderInfo.getSaleorderId());
                    if (isAccountPeriod == 0) {
                        // 账期支付 B
                        this.saveAddCapitalBillSimple(cb, 3, 527, 526, accountPeriodAmount, 1,
                                saleorderInfo.getTraderId());
                    }

                    // 账期还款 B
                    creditRepayment = accountPeriodAmount;
                    ResultInfo resultInfo = this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533, accountPeriodAmount, 1,
                            saleorderInfo.getTraderId());
                    capitalBillId = getCapitalBillId(resultInfo);
                    if (saleorderInfo.getPaymentType() != 419 && retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款G
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, retainageAmount, 1, saleorderInfo.getTraderId());
                    }

                    // 订单收款 C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, paymentAmount.add(thisPaymentAmount).subtract(realAmount), 1, saleorderInfo.getTraderId());
                }
            }
        } else {// 非账期订单
            if (paymentAmount.add(thisPaymentAmount).compareTo(realAmount) == -1) {// C+F<A
                // 订单收款 F
                this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, thisPaymentAmount, 1, saleorderInfo.getTraderId());
            } else if (paymentAmount.add(thisPaymentAmount).compareTo(realPreAmount) >= 0 // C+F>=A
                    && paymentAmount.add(thisPaymentAmount).compareTo(realAmount) == -1) {// && C+F<E
                // 订单收款尾款 F
                this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, thisPaymentAmount, 1, saleorderInfo.getTraderId());
            } else {// C+F>=E
                if (paymentAmount.compareTo(realAmount) >= 0) {// C>=E
                    // 订单收款 F
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, thisPaymentAmount, 1, saleorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realAmount) == -1 && paymentAmount.compareTo(realPreAmount) >= 0) {// C<E && C>=A
                    if (saleorderInfo.getPaymentType() != 419 && retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款G
                        BigDecimal realRetainage = new BigDecimal(0);
                        // 已支付金额 加 本次付款金额 小于等于 订单实际金额
                        if (paymentAmount.add(thisPaymentAmount).compareTo(realAmount) <= 0) {
                            realRetainage = thisPaymentAmount;
                        } else {
                            realRetainage = realAmount.subtract(paymentAmount);
                        }
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, realRetainage, 1, saleorderInfo.getTraderId());
                    }

                    // 订单收款C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, paymentAmount.add(thisPaymentAmount).subtract(realAmount), 1, saleorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realPreAmount) == -1) {// C<A
                    // 订单收款 A-C
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, realPreAmount.subtract(paymentAmount), 1, saleorderInfo.getTraderId());
                    if (saleorderInfo.getPaymentType() != 419 && retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款G
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, retainageAmount, 1, saleorderInfo.getTraderId());
                    }
                    // 订单收款 C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 526, paymentAmount.add(thisPaymentAmount).subtract(realAmount), 1, saleorderInfo.getTraderId());
                }
            }
        }

        // 判断订单付款状态
        paymentAmount = saleorderMapper.getSaleorderPaymentAmount(saleorderId);// 订单已收款金额(实际收款)
        BigDecimal periodAmount = saleorderMapper.getPeriodAmount(saleorderId);// 订单账期金额
        lackAccountPeriodAmount = saleorderMapper.getSaleorderLackAccountPeriodAmount(saleorderId);// 未归还账期
        BigDecimal refundBalanceAmount = afterSalesDataService.getRefundBalanceAmountBySaleorderId(saleorderId);// 查询销售订单退还余额的金额
        realAmount = saleorderMapper.getRealAmount(saleorderId);// 订单实际金额（减去售后数量后）
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(saleorderId);
        saleorder.setPaymentTime(DateUtil.sysTimeMillis());
        if (paymentAmount.add(periodAmount).compareTo(realAmount) > -1) {
            // 全部付款
            saleorder.setPaymentStatus(2);
            bussinessChanceService.updateBcStatusIfSaleorderPaid(saleorderId, BCConstants.BC_ORDERED);
            // 如果该订单只包含特殊产品，需要将特殊商品的发货状态、到货状态自动刷为已发货、已收货
            Integer isAllSpecialGoods = saleorderMapper.isAllSpecialGoods(saleorderId);
            if (isAllSpecialGoods == 0) {// 全特殊商品
                saleorder.setDeliveryStatus(2);
                saleorder.setDeliveryTime(DateUtil.sysTimeMillis());
                saleorder.setArrivalStatus(2);
                saleorder.setArrivalTime(DateUtil.sysTimeMillis());
                Saleorder specialSaleorder = new Saleorder();
                specialSaleorder.setSaleorderId(saleorderId);
                List<SaleorderGoods> saleorderGoodsList = saleorderGoodsMapper.getSaleorderGoodsByIdDB(specialSaleorder);
                for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                    SaleorderGoods sg = new SaleorderGoods();
                    sg.setSaleorderGoodsId(saleorderGoods.getSaleorderGoodsId());
                    sg.setDeliveryStatus(2);
                    sg.setDeliveryTime(DateUtil.sysTimeMillis());
                    sg.setDeliveryNum(saleorderGoods.getNum());
                    sg.setArrivalStatus(2);
                    sg.setArrivalTime(DateUtil.sysTimeMillis());
                    saleorderGoodsMapper.updateByPrimaryKeySelectiveDB(sg);
                }
            }
        } else if (paymentAmount.add(periodAmount).compareTo(realAmount) == -1 && paymentAmount.add(periodAmount).compareTo(BigDecimal.ZERO) == 1) {
            // 部分付款
            saleorder.setPaymentStatus(1);

        } else if (paymentAmount.add(periodAmount).compareTo(BigDecimal.ZERO) == 0) {
            // 未付款
            saleorder.setPaymentStatus(0);
        }

        realPreAmount = saleorderMapper.getRealPreAmount(saleorderId);// 订单实际预付款
        // 可发货时间
        if (saleorderInfo.getSatisfyDeliveryTime() == 0) {
            if (paymentAmount.compareTo(realPreAmount) > -1) {
                saleorder.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());
            }
        }

        // 银企直联（订单已收款金额>=订单实际金额）
        if (paymentAmount.add(periodAmount).subtract(lackAccountPeriodAmount).subtract(refundBalanceAmount).compareTo(realAmount) > -1) {
            saleorder.setIsPayment(1);
        } else {
            saleorder.setIsPayment(0);
        }

        int i = saleorderMapper.updateByPrimaryKeySelective(saleorder);

        Map<String, Object> map = new HashMap<>(16);
        map.put("orderId", saleorderInfo.getSaleorderId());
        map.put("creditRepayment", creditRepayment == null ? BigDecimal.ZERO : creditRepayment);
        map.put("capitalBillId", capitalBillId);

        if (i > 0) {
            map.put("orderNo", saleorderInfo.getSaleorderNo());
            map.put("traderId", saleorderInfo.getTraderId());
            map.put("paymentStatus", saleorder.getPaymentStatus());
            map.put("orderType", saleorder.getOrderType());
            return new ResultInfo(0, "操作成功", map);
        } else {
            return new ResultInfo(0, "操作成功", map);
        }
    }

    private TraderSupplier getTraderSupplier(CapitalBill capitalBill, CapitalBillDetail capitalBillDetail) {
        TraderSupplierVo traderSupplierInfo = traderSupplierMapper.getSupplierByTraderId(capitalBillDetail.getTraderId());
        TraderSupplier tc = new TraderSupplier();
        tc.setTraderSupplierId(traderSupplierInfo.getTraderSupplierId());
        tc.setAmount(traderSupplierInfo.getAmount().subtract(capitalBill.getAmount()));
        tc.setModTime(capitalBill.getAddTime());
        tc.setUpdater(capitalBill.getCreator());
        return tc;
    }

    private TraderCustomer getTraderCustomer(CapitalBill capitalBill, CapitalBillDetail capitalBillDetail2) {
        TraderCustomer traderCustomer = new TraderCustomer();
        traderCustomer.setTraderId(capitalBillDetail2.getTraderId());
        TraderCustomer traderCustomerInfo = traderCustomerMapper.getTraderCustomer(traderCustomer);
        TraderCustomer tc = new TraderCustomer();
        tc.setTraderCustomerId(traderCustomerInfo.getTraderCustomerId());
        tc.setAmount(traderCustomerInfo.getAmount().subtract(capitalBill.getAmount()));
        tc.setModTime(capitalBill.getAddTime());
        tc.setUpdater(capitalBill.getCreator());
        return tc;
    }

    @Override
    public ResultInfo<?> saveAddCaptitalBillDB(CapitalBill capitalBill) throws Exception {

        Integer saleorderId = null, buyorderId = null, afterSalesId = null;
        if (null != capitalBill.getCapitalBillDetails()) {
            List<CapitalBillDetail> capitalBillDetails = capitalBill.getCapitalBillDetails();
            for (CapitalBillDetail capitalBillDetail : capitalBillDetails) {
                if (ErpConst.ONE.equals(capitalBillDetail.getOrderType())) {
                    // 销售
                    saleorderId = capitalBillDetail.getRelatedId();
                } else if (ErpConst.TWO.equals(capitalBillDetail.getOrderType())) {
                    // 采购
                    buyorderId = capitalBillDetail.getRelatedId();
                } else if (ErpConst.THREE.equals(capitalBillDetail.getOrderType())) {
                    // 售后
                    afterSalesId = capitalBillDetail.getRelatedId();
                }
            }
        }
        //销售单
        if (saleorderId != null) {
            //由于会有多次发送，故只取第一次
            boolean firstAddCapitalBillFlag = false;
            if (capitalBill.getBdPayStatusFlag()) {
                firstAddCapitalBillFlag = true;
            }
            capitalBill.setBdPayStatusFlag(false);
            // 如果是销售订单 余额支付 需要扣除对应客户的可用余额
            if (null != capitalBill.getCapitalBillDetails()) {
                List<CapitalBillDetail> capitalBillDetails2 = capitalBill.getCapitalBillDetails();
                for (CapitalBillDetail capitalBillDetail2 : capitalBillDetails2) {
                    if (capitalBill.getTraderType() == 4 && capitalBill.getTraderMode() == 528 && capitalBillDetail2.getBussinessType() == 526) {
                        TraderCustomer tc = getTraderCustomer(capitalBill, capitalBillDetail2);
                        logger.info("saveAddCapitalBill saleorder updatetraderCustomer:{}", tc.toString());
                        traderCustomerMapper.update(tc);
                    }
                    break;
                }
            }
            if (null != capitalBill.getBankBillId()) {
                BankBill bankBillEdit = getBankBillEdit(capitalBill);
                logger.info("saveAddCapitalBill saleorder editBankBill:{}", bankBillEdit.toString());
                //bankBillService.editBankBill(bankBillEdit);
                editBankBill(bankBillEdit);
            }
            // 根据订单ID刷新订单付款状态
            ResultInfo reInfo = updatePaymentStatusBySaleorderId(saleorderId, capitalBill);
            if (reInfo.getCode().equals(0)) {
                Map<String, Object> map = (Map<String, Object>) reInfo.getData();
                // map.put("capitalBillId", capitalBillId);
                // 查询订单基本信息
                Saleorder saleorderInfo = saleorderMapper.getBaseSaleorderInfo(saleorderId);
                map.put("saleorderInfo", saleorderInfo);
                logger.info("saveAddCapitalBill saleorderId:" + JSON.toJSONString(saleorderInfo) + "\tfirstAddCapitalBillFlag:" + firstAddCapitalBillFlag);
                //“待用户确认”状态下，状态变为“进行中”
                if (saleorderInfo.getStatus() == 4) {
                    saleorderMapper.updateSaleorderStatus(saleorderInfo.getSaleorderId(), 1, System.currentTimeMillis());
                }

                //BD订单状态
                // changed by Tomcat.Hui 2020/3/6 9:10 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分 部分付款和全部付款都需要通知 .
                if (saleorderInfo.getOrderType() <= 1 && (saleorderInfo.getPaymentStatus() == 2 || saleorderInfo.getPaymentStatus() == 1) && firstAddCapitalBillFlag) {

//                    //更新BD订单前台状态
//                    OrderData orderData = new OrderData();
//                    orderData.setOrderNo(saleorderInfo.getSaleorderNo());
//                    // changed by Tomcat.Hui 2020/3/6 9:10 上午 .Desc: VDERP-2057 BD订单流程优化-ERP部分 部分付款和全部付款都需要通知 .
//                    //发货
//                    orderData.setOrderStatus( (saleorderInfo.getPaymentStatus() == 2) ? 3 : 7);
                    saleorderSyncService.syncSaleorderStatus2Mjx(saleorderInfo.getSaleorderId()
                            , PCOrderStatusEnum.get(saleorderInfo.getPaymentStatus() == 2 ? 3 : 7), SaleorderSyncEnum.CAPITAL_BILL_BD);

                }
                reInfo.setData(map);
            }
            return reInfo;
        }
        //采购单
        if (buyorderId != null) {
            logger.info("saveAddCapitalBill buyorderId:{}", buyorderId);
            // 如果是采购订单 余额支付 需要扣除对应供应商的可用余额
            if (null != capitalBill.getCapitalBillDetails()) {
                List<CapitalBillDetail> capitalBillDetails2 = capitalBill.getCapitalBillDetails();
                for (CapitalBillDetail capitalBillDetail2 : capitalBillDetails2) {
                    if (capitalBill.getTraderType() == 5 && capitalBill.getTraderMode() == 528
                            && capitalBillDetail2.getBussinessType() == 525) {
                        TraderSupplier tc = getTraderSupplier(capitalBill, capitalBillDetail2);
                        logger.info("saveAddCapitalBill buyorder updatetraderSupplier:{}", tc.toString());
                        traderSupplierMapper.updateByPrimaryKeySelectiveDB(tc);
                    }
                    break;
                }
            }
            if (null != capitalBill.getBankBillId()) {
                BankBill bankBillEdit = getBankBillEdit(capitalBill);
                logger.info("saveAddCapitalBill buyorder editBankBill:{}", bankBillEdit.toString());
                editBankBill(bankBillEdit);
            }
            // 根据采购订单ID刷新订单收款状态
            ResultInfo reInfo = updatePaymentStatusByBuyorderId(buyorderId, capitalBill);
            if (reInfo.getCode().equals(0)) {
                Map<String, Object> map = (Map<String, Object>) reInfo.getData();

                reInfo.setData(map);
                // 识别码生成
//		Buyorder buyorder = new Buyorder();
//		buyorder.setBuyorderId(buyorderId);
//		buyorder.setCreator(capitalBill.getCreator());
//		buyorder.setAddTime(capitalBill.getAddTime());
//		buyorderService.batchCreateGoodsCode(buyorder);
                return reInfo;
            }
        }
        //售后单
        if (afterSalesId != null) {
            logger.info("saveAddCapitalBill afterSalesId:{}", afterSalesId);
            // 售后订单收款
            if (capitalBill.getAfterSalesPaymentType() != null && capitalBill.getAfterSalesPaymentType() == 1) {
                if (null != capitalBill.getBankBillId()) {
                    BankBill bankBillEdit = getBankBillEdit(capitalBill);
                    logger.info("saveAddCapitalBill 售后订单收款 editBankBill:{}", bankBillEdit.toString());
                    editBankBill(bankBillEdit);
                }
                ResultInfo reInfo = this.updatePaymentStatusAfterSalesId(afterSalesId, capitalBill);
                if (reInfo.getCode().equals(0)) {
                    Map<String, Object> map = (Map<String, Object>) reInfo.getData();
                    reInfo.setData(map);
                }
                return reInfo;
            } else {// 售后订单付款
                logger.info("saveAddCapitalBill 售后订单付款  insertCapitalBill:{}", capitalBill.toString());
                int i = capitalBillMapper.insertSelective(capitalBill);
                if (i > 0) {
                    Integer capitalBillId = capitalBill.getCapitalBillId();
                    // 生成资金流水号
                    CapitalBill capitalBillExtra = new CapitalBill();
                    capitalBillExtra.setCapitalBillId(capitalBillId);
                    capitalBillExtra.setCapitalBillNo(orderNoDict.getOrderNum(capitalBillId, 10));
                    logger.info("saveAddCapitalBill 售后订单付款 updateCapitalBill:{}", capitalBillExtra.toString());
                    capitalBillMapper.updateByPrimaryKeySelective(capitalBillExtra);

                    for (CapitalBillDetail capitalBillDetail : capitalBill.getCapitalBillDetails()) {
                        capitalBillDetail.setCapitalBillId(capitalBillId);
                        logger.info("saveAddCapitalBill 售后订单付款 insertCapBillDetail:{}", capitalBillDetail.toString());
                        capitalBillDetailMapper.insertSelective(capitalBillDetail);
                        // 售后订单详情
                        AfterSalesDetailVo afterSalesDetail = invoiceAfterMapper.getAfterSalesDetail(afterSalesId);
                        if (afterSalesDetail.getSubjectType() == 535) {
                            // 销售售后
                            // 余额支付，需要扣除对应的账户余额
                            if (capitalBill.getTraderMode() == 528) {
                                TraderCustomer tc = getTraderCustomer(capitalBill, capitalBillDetail);
                                logger.info("saveAddCapitalBill 售后订单付款 销售售后 updateTraderCustomer:{}", tc.toString());
                                traderCustomerMapper.update(tc);
                            }
                        } else if (afterSalesDetail.getSubjectType() == 536) {
                            // 采购售后
                            // 如果余额支付，需要扣除对应的账户余额
                            if (capitalBill.getTraderMode() == 528) {
                                TraderSupplier tc = getTraderSupplier(capitalBill, capitalBillDetail);
                                logger.info("saveAddCapitalBill 售后订单付款 采购售后 updateTraderCustomer:{}", tc.toString());
                                traderSupplierMapper.updateByPrimaryKeySelective(tc);
                            }
                        } else if (afterSalesDetail.getSubjectType() == 537) {
                            // 第三方售后
                            // 获取第三方售后详情

                            // 更新对应的第三方付款状态
                        }
                        // 更新付款状态
                        if (afterSalesDetail.getSubjectType() == 535 || afterSalesDetail.getSubjectType() == 537) {
                            if (afterSalesDetail.getAfterType() == 584 || afterSalesDetail.getAfterType() == 550
                                    || afterSalesDetail.getAfterType() == 541
                                    || afterSalesDetail.getAfterType() == 585) {
                                AfterSalesDetail afterSalesDetailExtra = new AfterSalesDetail();
                                afterSalesDetailExtra.setAfterSalesDetailId(afterSalesDetail.getAfterSalesDetailId());

                                // 获取售后订单已付款金额
                                BigDecimal afterSalesPayAmount = afterSalesDataService
                                        .getSaleorderAfterSalesPayAmount(afterSalesId);
                                // 获取工程师酬金总金额
                                BigDecimal afterSalesInstallstionAmount = afterSalesDataService
                                        .getAfterSalesInstallstionAmount(afterSalesId);

                                if (afterSalesPayAmount.compareTo(afterSalesInstallstionAmount) > -1) {
                                    // 全部付款
                                    afterSalesDetailExtra.setPaymentStatus(2);
                                } else if (afterSalesPayAmount.compareTo(afterSalesInstallstionAmount) == -1
                                        && afterSalesPayAmount.compareTo(BigDecimal.ZERO) == 1) {
                                    // 部分付款
                                    afterSalesDetailExtra.setPaymentStatus(1);
                                } else if (afterSalesPayAmount.compareTo(BigDecimal.ZERO) == 0) {
                                    // 未付款
                                    afterSalesDetailExtra.setPaymentStatus(0);
                                }
                                afterSalesDetailExtra.setPaymentTime(DateUtil.sysTimeMillis());
                                logger.info("saveAddCapitalBill 售后订单付款 更新付款状态 updateafterSaleDetail:{}", afterSalesDetailExtra.toString());
                                afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetailExtra);
                            }
                        }
                        capitalBill.setCapitalBillDetail(capitalBillDetail);
                        break;
                    }
                    if (null != capitalBill.getBankBillId()) {
                        BankBill bankBillEdit = getBankBillEdit(capitalBill);
                        logger.info("saveAddCapitalBill 售后订单付款 editBankBill:{}", bankBillEdit.toString());
                        editBankBill(bankBillEdit);
                    }
                }
                // return new ResultInfo(0, "操作成功");
            }
        }
        return new ResultInfo(0, "操作成功");
    }

    @Override
    public ResultInfo<?> saveAddCapitalBill(CapitalBill capitalBill) {
        try {
            orderAccountPeriodService.dealOrderCustomerBillPeriodWithAddCapitalBill(capitalBill);
        } catch (CustomerBillPeriodException e) {
            logger.error("添加流水处理订单账期信息 warn :{}", e.getMessage(), e);
            return ResultInfo.error(e.getMessage());
        }

        try {

            ResultInfo resultInfo = saveAddCaptitalBillDB(capitalBill);
            if (resultInfo != null && resultInfo.getCode().equals(0) && resultInfo.getData() != null) {
                Map<String, Object> maps = (Map<String, Object>) resultInfo.getData();
                Saleorder saleorderInfo = (Saleorder) maps.get("saleorderInfo");
                if (saleorderInfo != null) {
                    // 将耗材订单付款状态推送到耗材订单中,全部付款并且是耗材商城的订单才推送
                    logger.info("saveAddCapitalBill 推送HC订单状态 订单号:{},付款状态:{}", saleorderInfo.getSaleorderNo(), saleorderInfo.getPaymentStatus());
                    putOrderPayStatustoHC(saleorderInfo);
                }

                Map<String, Object> result_map = (Map<String, Object>) resultInfo.getData();
                if (!result_map.isEmpty()) {
                    if (capitalBill.getCapitalBillDetails().get(0).getOrderType().intValue() == 1) {//销售
                        //销售订单收款后--发送消息
                        if (result_map.get("traderId") != null && result_map.get("orderNo") != null && result_map.get("orderId") != null) {
                            //根据客户Id查询客户负责人
                            List<Integer> userIdList = userMapper.getUserIdListByTraderId(Integer.valueOf(result_map.get("traderId").toString()), ErpConst.ONE);
                            Map<String, String> map = new HashMap<>();
                            map.put("saleorderNo", result_map.get("orderNo").toString());
                            MessageUtil.sendMessage(9, userIdList, map, "./order/saleorder/view.do?saleorderId=" + result_map.get("orderId"));//

                            //同步到网站
                            vedengSoapService.messageSyncWeb(2, Integer.parseInt(result_map.get("orderId").toString()), 2);
                        }

                            try {
                                logger.info("添加流水处理订单账期信息 begin result_map:{}", JSON.toJSONString(result_map));
                                Integer orderId = Integer.parseInt(result_map.get("orderId").toString());
                                BigDecimal creditRepayment = new BigDecimal(result_map.get("creditRepayment").toString());
                                Integer capitalBillId = Integer.parseInt(result_map.get("capitalBillId").toString());
                                orderAccountPeriodService.unfreezingBillPeriodByCreditRepayment(orderId, creditRepayment, capitalBillId);
                            } catch (CustomerBillPeriodException e) {
                                logger.error("添加流水处理订单账期信息 warn :{}", e.getMessage(), e);
                            }

                    } else if (capitalBill.getCapitalBillDetails().get(0).getOrderType().intValue() == 2) {//采购
                        //采购订单付款后--发送消息
                        if (result_map.get("traderId") != null && result_map.get("orderNo") != null && result_map.get("orderId") != null) {
                            //根据客户Id查询供应商负责人
                            List<Integer> userIdList = userMapper.getUserIdListByTraderId(Integer.valueOf(result_map.get("traderId").toString()), ErpConst.TWO);
                            Map<String, String> map = new HashMap<>();
                            map.put("buyorderNo", result_map.get("orderNo").toString());
                            MessageUtil.sendMessage(17, userIdList, map, "./order/buyorder/viewBuyordersh.do?buyorderId=" + result_map.get("orderId"));//
                        }
                        // 判断是否是全部付款 全部付款发送站内信 paymentStatus： 2
                        Integer paymentStatus = (Integer) result_map.get("paymentStatus");
                        if (paymentStatus != null && Integer.valueOf(2).equals(paymentStatus)) {
                            Integer orderId = (Integer) result_map.get("orderId");
                            String orderNo = (String) result_map.get("orderNo");
                            if (orderId != null && orderNo != null) {
                                List<Saleorder> saleorderList = saleorderMapper.selectSaleorderNoByBuyOrderId(orderId);
                                for (Saleorder saleOrderItem : saleorderList) {
                                    // 根据销售单编码查找对应销售
                                    List<Integer> saleOrderTraderIds = buyorderGoodsMapper.selectSaleOrderTraderIdsBySaleOrderId(saleOrderItem.getSaleorderId());
                                    saleOrderTraderIds = saleOrderTraderIds.stream().filter(ObjectUtils::notEmpty).collect(Collectors.toList());
                                    if (!ObjectUtils.allNotNull(saleOrderItem)) {
                                        continue;
                                    }
                                    Map<String, String> mapParams = new HashMap<>();
                                    mapParams.put("saleOrderNo", saleOrderItem.getSaleorderNo());
                                    mapParams.put("buyOrderNo", orderNo);
                                    MessageUtil.sendMessage2(172, saleOrderTraderIds, mapParams, "./order/saleorder/view.do?saleorderId=" + saleOrderItem.getSaleorderId().intValue(), "njadmin");
                                }

                                // 更新采购单商品进程详情
                                buyorderGoodsMapper.updateProcessDescByBuyOrderId(orderId, "采购单已付款");
                            }
                        }
                    }
                }
            }

            //VDERP-2263   订单售后采购改动通知
            if (resultInfo != null && resultInfo.getCode() == 0) {
                updateOrderDateTimeByCapitalBill(capitalBill);
            }
            return resultInfo;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return ResultInfo.error(e.getMessage());
        }

    }

    private void updateOrderDateTimeByCapitalBill(CapitalBill capitalBill) {
        if (capitalBill.getCapitalBillDetail().getOrderType() == 1) {
            //销售订单
            orderCommonService.updateSaleOrderDataUpdateTime(capitalBill.getCapitalBillDetail().getRelatedId(), null, OrderDataUpdateConstant.SALE_ORDER_PAY);
        } else if (capitalBill.getCapitalBillDetail().getOrderType() == 2) {
            //采购订单
            orderCommonService.updateBuyOrderDataUpdateTime(capitalBill.getCapitalBillDetail().getRelatedId(), null, OrderDataUpdateConstant.BUY_ORDER_PAY);
        } else if (capitalBill.getCapitalBillDetail().getOrderType() == 3) {
            //售后订单
            orderCommonService.updateAfterOrderDataUpdateTime(capitalBill.getCapitalBillDetail().getRelatedId(), null, OrderDataUpdateConstant.AFTER_ORDER_PAY);
        }
    }

    public ResultInfo updatePaymentStatusAfterSalesId(Integer afterSalesId, CapitalBill capitalBill) throws Exception {
        logger.info("updatePaymentStatusAfterSalesId insertcap:{}", capitalBill.toString());
        int i = capitalBillMapper.insertSelective(capitalBill);
        if (i > 0) {
            Integer capitalBillId = capitalBill.getCapitalBillId();
            // 生成资金流水号
            CapitalBill capitalBillExtra = new CapitalBill();
            capitalBillExtra.setCapitalBillId(capitalBillId);
            capitalBillExtra.setCapitalBillNo(orderNoDict.getOrderNum(capitalBillId, 10));
            logger.info("updatePaymentStatusAfterSalesId capitalBillId:{},CapitalBillNo:{}", capitalBillId, capitalBillExtra.getCapitalBillNo());
            capitalBillMapper.updateByPrimaryKeySelective(capitalBillExtra);

            // 售后订单详情
            AfterSalesDetailVo afterSalesDetail = invoiceAfterMapper.getAfterSalesDetail(afterSalesId);
            for (CapitalBillDetail capitalBillDetail : capitalBill.getCapitalBillDetails()) {
                // 客户支付费用
                BigDecimal payAmount = afterSalesDataService.getPayAmount(afterSalesId, 1);
                if (payAmount == null) {
                    payAmount = new BigDecimal(0);
                }

                /*
                 * if(capitalBill.getCapitalBillDetail().getBussinessType().
                 * intValue() == 526) {//收款--销售收款不验证（销售要考虑退的款）
                 * //已收款金额加本次收款金额大于服务费总额 }else
                 */
                if (capitalBill.getCapitalBillDetail().getBussinessType().intValue() != 526) {// 付款（非收款）
                    // 已支付费用加本次付款金额大于应付金额
                    if (payAmount.add(capitalBill.getAmount()).doubleValue() > Math
                            .abs(afterSalesDetail.getRealRefundAmount().doubleValue())) {
                        throw new Exception("已付金额加本次付款金额大于应付金额");
                    }
                }
                capitalBillDetail.setCapitalBillId(capitalBillId);
                logger.info("updatePaymentStatusAfterSalesId capitalBillDetail:{}", capitalBillDetail.toString());
                capitalBillDetailMapper.insertSelective(capitalBillDetail);
                capitalBill.setCapitalBillDetail(capitalBillDetail);
            }

            boolean afterIsReturn = false;
            // 获取售后订单-对应的订单产品总额及售后产品总额
            // Map<String, String> map =
            // invoiceAfterMapper.getAfterOrderMultipleAmount(afterSalesId);
            AfterSalesDetailVo afterDetail = invoiceAfterMapper.getAfterSalesDetail(afterSalesId);
            // capitalBill-capitalBillDetail目前一对一关系2017.10.25
            // 交易类型：收入---业务类型:订单收款--------或采购退款（退给我们）
            if (capitalBill.getTraderType().intValue() == 1 && (capitalBill.getCapitalBillDetails().get(0)
                    .getBussinessType().intValue() == SysOptionConstant.ID_526
                    || capitalBill.getCapitalBillDetails().get(0).getBussinessType()
                    .intValue() == SysOptionConstant.ID_531)) {
                afterIsReturn = true;// 售后-收款-手续费
            }
            if (afterIsReturn) {// 售后-收款
                // 修改售后订单手续费状态
                AfterSalesDetailVo asd = new AfterSalesDetailVo();
                asd.setAfterSalesId(afterSalesId);
                if (afterDetail.getSubjectType().equals(SysOptionConstant.ID_535)
                        || afterDetail.getSubjectType().equals(SysOptionConstant.ID_537)) { // 销售--第三方
                    // 查询资金流水-已收到的手续费总额
                    BigDecimal receivedReturnFee = afterSalesDataService.getPayAmount(afterSalesId, 1);// 售后订单
                    if (capitalBill.getAmount().doubleValue() == 0 && receivedReturnFee.doubleValue() == 0) {
                        asd.setServiceAmountStatus(0);
                        asd.setReceivePaymentStatus(0);
                    } else if ((receivedReturnFee
                            .doubleValue() /*
                     * + capitalBill.getAmount().
                     * doubleValue()
                     */) >= afterDetail.getServiceAmount().doubleValue()) {
                        asd.setServiceAmountStatus(2);
                        asd.setReceivePaymentStatus(2);
                    } else if ((receivedReturnFee
                            .doubleValue() /*
                     * + capitalBill.getAmount().
                     * doubleValue()
                     */) < afterDetail.getServiceAmount().doubleValue()) {
                        asd.setServiceAmountStatus(1);
                        asd.setReceivePaymentStatus(1);
                    }
                    asd.setReceivePaymentTime(DateUtil.gainNowDate());
                    logger.info("updatePaymentStatusAfterSalesId 销售--第三方 updateAfterDetailServiceStatus:{}", asd.toString());
                    invoiceAfterMapper.updateAfterDetailServiceStatus(asd);
                } else if (afterDetail.getSubjectType().equals(SysOptionConstant.ID_536)) {// 采购
                    // 查询资金流水-已收到的退款总额
                    BigDecimal refundAmount = afterSalesDataService.getRefundAmount(afterSalesId, 2);// 2采购订单售后

                    if (capitalBill.getAmount().doubleValue() == 0 && refundAmount.doubleValue() == 0) {
                        asd.setRefundAmountStatus(1);
                    } else if ((refundAmount.doubleValue() + capitalBill.getAmount().doubleValue()) >= afterDetail
                            .getRealRefundAmount().doubleValue()) {
                        asd.setRefundAmountStatus(3);
                    } else if ((refundAmount.doubleValue() + capitalBill.getAmount().doubleValue()) < afterDetail
                            .getRealRefundAmount().doubleValue()) {
                        asd.setRefundAmountStatus(2);
                    }
                    logger.info("updatePaymentStatusAfterSalesId 采购 updateAfterDetailServiceStatus:{}", asd.toString());
                    invoiceAfterMapper.updateAfterDetailServiceStatus(asd);
                }
                // 判断是否为采购售后退款给我们（是：需要减去在供应商处的余额）
                // 采购售后：采购退货或采购退款：退到贝登（非供应商处余额）
                /*
                 * if(afterSalesDetail.getSubjectType().equals(536) &&
                 * (afterSalesDetail.getAfterType().equals(546) ||
                 * afterSalesDetail.getAfterType().equals(549) &&
                 * afterSalesDetail.getRefund().equals(1))){ //减去贝登在供应商处的余额 int
                 * t = invoiceAfterMapper.updateSupplyBalance(afterSalesDetail.
                 * getTraderId(),capitalBill.getAmount()); if(t == 0){ throw new
                 * Exception("修改供应商处余额失败"); } }
                 */
            }
            Map<String, Object> map = new HashMap<>();
            map.put("orderId", afterDetail.getAfterSalesId());
            map.put("orderNo", afterDetail.getAfterSalesNo());
            map.put("traderId", afterDetail.getTraderId());
            return new ResultInfo(0, "操作成功", map);
        }
        return new ResultInfo();
    }

    @Override
    public ResultInfo updatePaymentStatusByBuyorderId(Integer buyorderId, CapitalBill cb) {
        Buyorder buyorderInfo = buyorderMapper.selectByPrimaryKey(buyorderId);
        // 订单账期金额(已收款) --- 已收款账期金额减去退还账期金额
        BigDecimal periodAmountExtra = buyorderDataService.getPeriodAmount(buyorderId);

        // 订单实际预付款(A)
        BigDecimal realPreAmount = buyorderDataService.getRealPreAmount(buyorderId);
        // 订单账期(B)
        BigDecimal accountPeriodAmount = buyorderInfo.getAccountPeriodAmount();
        // 未归还账期(D)
        BigDecimal lackAccountPeriodAmount = buyorderDataService.getLackAccountPeriodAmount(buyorderId);
        // 订单已收款金额(实际收款)(C)
        BigDecimal paymentAmount = buyorderDataService.getPaymentAmountDB(buyorderId).add(periodAmountExtra)
                .subtract(lackAccountPeriodAmount);
        // 订单实际金额（减去售后数量后）(E)
        BigDecimal realAmount = buyorderDataService.getRealAmount(buyorderId);
        // 本次收款金额(F)
        BigDecimal thisPaymentAmount = cb.getAmount();
        // 订单尾款金额(G)
        BigDecimal retainageAmount = buyorderInfo.getRetainageAmount();

        // 先判断是否是账期订单
        Integer traderMode = cb.getTraderMode();
        Integer traderType = cb.getTraderMode() == 528 ? 5 : 2;// 如果是余额支付那么交易类型为
        // 转出
        if (buyorderInfo.getHaveAccountPeriod() == 1) {// 是账期订单
            if (paymentAmount.add(thisPaymentAmount).compareTo(realPreAmount) == -1) {// C+F<A
                // 订单收款 F
                this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, thisPaymentAmount, 2,
                        buyorderInfo.getTraderId());
            } /*
             * else if (paymentAmount.add(thisPaymentAmount).compareTo(
             * prepaidAmount) == 0) {//C+F=A //账期支付 B 同时更新订单付款状态为已付款 }
             */ else if (paymentAmount.add(thisPaymentAmount).compareTo(realPreAmount) >= 0
                    && paymentAmount.add(thisPaymentAmount).compareTo(realAmount) < 1) {// C+F>=A
                // &&
                // C+F<=E
                if (paymentAmount.compareTo(realPreAmount) >= 0) {// C>=A
                    if (thisPaymentAmount.compareTo(lackAccountPeriodAmount) < 1) {// F<=D
                        // 账期归还 F
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533, thisPaymentAmount, 2,
                                buyorderInfo.getTraderId());
                    } else {
                        // 账期归还D
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533, lackAccountPeriodAmount, 2,
                                buyorderInfo.getTraderId());
                        if (retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                            // 尾款收款 F-D
                            this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                                    thisPaymentAmount.subtract(lackAccountPeriodAmount), 2, buyorderInfo.getTraderId());
                        }
                    }
                } else {
                    // 订单收款 A-C
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            realPreAmount.subtract(paymentAmount), 2, buyorderInfo.getTraderId());
                    // 账期支付 B
                    this.saveAddCapitalBillSimple(cb, 3, 527, 525, accountPeriodAmount, 2, buyorderInfo.getTraderId());

                    lackAccountPeriodAmount = buyorderDataService.getLackAccountPeriodAmount(buyorderId);
                    if (paymentAmount.add(thisPaymentAmount).subtract(realPreAmount)
                            .compareTo(lackAccountPeriodAmount) < 1) {// C+F-A<=D
                        // 账期还款 C+F-A
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                                paymentAmount.add(thisPaymentAmount).subtract(realPreAmount), 2,
                                buyorderInfo.getTraderId());
                    } else {
                        // 账期归还D
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533, lackAccountPeriodAmount, 2,
                                buyorderInfo.getTraderId());
                        if (retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                            // 尾款收款C+F-A-D
                            this.saveAddCapitalBillSimple(cb,
                                    traderType, traderMode, 525, paymentAmount.add(thisPaymentAmount)
                                            .subtract(realPreAmount).subtract(lackAccountPeriodAmount),
                                    2, buyorderInfo.getTraderId());
                        }
                    }
                }
            } else if (paymentAmount.add(thisPaymentAmount).compareTo(realAmount) == 1) {// C+F>E
                if (paymentAmount.compareTo(realAmount) >= 0) {// C>=E
                    // 订单收款 F
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, thisPaymentAmount, 2,
                            buyorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realAmount) == -1 && paymentAmount.compareTo(realPreAmount) >= 0) {// C<E
                    // &&
                    // C>=A
                    // 账期归还 E-C-G
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533,
                            realAmount.subtract(paymentAmount).subtract(retainageAmount), 2,
                            buyorderInfo.getTraderId());
                    if (retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款 G
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, retainageAmount, 2,
                                buyorderInfo.getTraderId());
                    }

                    // 订单收款C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            paymentAmount.add(thisPaymentAmount).subtract(realAmount), 2, buyorderInfo.getTraderId());

                } else if (paymentAmount.compareTo(realPreAmount) == -1) {// C<A
                    // 订单收款 A-C
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            realPreAmount.subtract(paymentAmount), 2, buyorderInfo.getTraderId());
                    // 账期支付 B
                    this.saveAddCapitalBillSimple(cb, 3, 527, 525, accountPeriodAmount, 2, buyorderInfo.getTraderId());
                    // 账期还款 B
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 533, accountPeriodAmount, 2,
                            buyorderInfo.getTraderId());
                    if (retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款G
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, retainageAmount, 2,
                                buyorderInfo.getTraderId());
                    }
                    // 订单收款 C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            paymentAmount.add(thisPaymentAmount).subtract(realAmount), 2, buyorderInfo.getTraderId());
                }
            }
        } else {// 非账期订单
            if (paymentAmount.add(thisPaymentAmount).compareTo(realAmount) == -1) {// C+F<A
                // 订单收款 F
                this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, thisPaymentAmount, 2,
                        buyorderInfo.getTraderId());
            } else if (paymentAmount.add(thisPaymentAmount).compareTo(realPreAmount) >= 0
                    && paymentAmount.add(thisPaymentAmount).compareTo(realAmount) == -1) {// C+F>=A
                // &&
                // C+F<E
                // 订单收款尾款 F
                this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, thisPaymentAmount, 2,
                        buyorderInfo.getTraderId());
            } else {// C+F>=E
                if (paymentAmount.compareTo(realAmount) >= 0) {// C>=E
                    // 订单收款 F
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, thisPaymentAmount, 2,
                            buyorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realAmount) == -1 && paymentAmount.compareTo(realPreAmount) >= 0) {// C<E
                    // &&
                    // C>=A
                    if (retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款G
                        if (paymentAmount.add(thisPaymentAmount).compareTo(realAmount) < 1) {
                            this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, thisPaymentAmount, 2,
                                    buyorderInfo.getTraderId());
                        } else {
                            this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                                    realAmount.subtract(paymentAmount), 2, buyorderInfo.getTraderId());
                        }
                    }

                    // 订单收款C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            paymentAmount.add(thisPaymentAmount).subtract(realAmount), 2, buyorderInfo.getTraderId());
                } else if (paymentAmount.compareTo(realPreAmount) == -1) {// C<A
                    // 订单收款 A-C
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            realPreAmount.subtract(paymentAmount), 2, buyorderInfo.getTraderId());
                    if (retainageAmount.compareTo(BigDecimal.ZERO) == 1) {// G>0
                        // 尾款收款G
                        this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525, retainageAmount, 2,
                                buyorderInfo.getTraderId());
                    }
                    // 订单收款 C+F-E
                    this.saveAddCapitalBillSimple(cb, traderType, traderMode, 525,
                            paymentAmount.add(thisPaymentAmount).subtract(realAmount), 2, buyorderInfo.getTraderId());
                }
            }
        }

        // 判断订单付款状态
        paymentAmount = buyorderDataService.getPaymentAmountDB(buyorderId);// 订单已收款金额(实际收款)
        BigDecimal periodAmount = buyorderDataService.getPeriodAmount(buyorderId);// 订单账期金额
        realAmount = buyorderDataService.getRealAmount(buyorderId);// 订单实际金额（减去售后数量后）
        Buyorder buyorder = new Buyorder();
        buyorder.setBuyorderId(buyorderId);
        buyorder.setPaymentTime(DateUtil.sysTimeMillis());
        if (paymentAmount.add(periodAmount).compareTo(realAmount) > -1) {
            // 全部付款
            buyorder.setPaymentStatus(2);
        } else if (paymentAmount.add(periodAmount).compareTo(realAmount) == -1
                && paymentAmount.add(periodAmount).compareTo(BigDecimal.ZERO) == 1) {
            // 部分付款
            buyorder.setPaymentStatus(1);
        } else if (paymentAmount.add(periodAmount).compareTo(BigDecimal.ZERO) == 0) {
            // 未付款
            buyorder.setPaymentStatus(0);
        }

        realPreAmount = buyorderDataService.getRealPreAmount(buyorderId);// 订单实际预付款
        // 可发货时间
        if (buyorderInfo.getSatisfyDeliveryTime() == 0) {
            if (paymentAmount.compareTo(realPreAmount) > -1) {
                buyorder.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());
            }
        }
        logger.info("updatePaymentStatusByBuyorderId buyorderInfo:{}", buyorder.toString());
        int i = buyorderMapper.updateByPrimaryKeySelective(buyorder);
        if (i > 0) {
            Map<String, Object> map = new HashMap<>();
            map.put("orderId", buyorderInfo.getBuyorderId());
            map.put("orderNo", buyorderInfo.getBuyorderNo());
            map.put("traderId", buyorderInfo.getTraderId());
            map.put("paymentStatus", buyorder.getPaymentStatus());
            return new ResultInfo(0, "操作成功", map);
        } else {
            return new ResultInfo();
        }
    }

    @Override
    public int saveOpenInvoceApply(InvoiceApply invoiceApply) {

		/*l根据国家相关规定增值税发票必须提供客户税务登记号，对客户名称中含有“企业”或“公司”的必须有税号，否则不允许申请（2017.11.17修正）；
		l在申请增值税专用发票时，判断客户必须具有注册地址、注册电话、开户银行、银行账号、税务登记号、一般纳税人资质等，缺一不可，否则不允许申请；*/
        if (invoiceApply != null) {

            //AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(invoiceApply.getRelatedId());
            AfterSales as = new AfterSales();

            as.setAfterSalesId(invoiceApply.getRelatedId());

            AfterSalesVo afterSales = afterSalesMapper.viewAfterSalesDetail(as);

            SaleorderVo saleorder = null;

            if (afterSales != null && afterSales.getSubjectType() == 535) {//销售

                //验证是否允许申请
                saleorder = saleorderMapper.getSaleorderFinanceInfo(afterSales.getOrderId());

            } else if (afterSales != null && afterSales.getSubjectType() == 537) {

                //第三方
                saleorder = afterSalesMapper.getAftersaleFinanceInfo(afterSales.getAfterSalesId());

            }

            //判断发票类型
            if (afterSales != null && (afterSales.getInvoiceType().intValue() == 430 || afterSales.getInvoiceType().intValue() == 681
                    || afterSales.getInvoiceType().intValue() == 683 || afterSales.getInvoiceType().intValue() == 685
                    || afterSales.getInvoiceType().intValue() == 971 || afterSales.getInvoiceType().intValue() == 687)) {

                //17%增值税普通发票
                if (saleorder != null && saleorder.getTraderName().indexOf("企业") != -1 || saleorder.getTraderName().indexOf("公司") != -1) {

                    if (StringUtils.isBlank(saleorder.getTaxNum())) {//税务登记号

                        //return new ResultInfo(-1,"客户开票资料不全，无法开具增值税普通发票，请完善信息！");
                        return -2;

                    }

                }
            } else if (afterSales != null && (afterSales.getInvoiceType().intValue() == 429 || afterSales.getInvoiceType().intValue() == 682
                    || afterSales.getInvoiceType().intValue() == 684 || afterSales.getInvoiceType().intValue() == 686
                    || afterSales.getInvoiceType().intValue() == 972 || afterSales.getInvoiceType().intValue() == 688)) {//17%增值税专用发票

                //注册地址、注册电话、开户银行、银行账号、税务登记号、一般纳税人资质
                if (saleorder != null && StringUtils.isBlank(saleorder.getRegAddress()) || StringUtils.isBlank(saleorder.getRegTel())
                        || StringUtils.isBlank(saleorder.getBank()) || StringUtils.isBlank(saleorder.getBankAccount())
                        || StringUtils.isBlank(saleorder.getTaxNum()) || StringUtils.isBlank(saleorder.getAverageTaxpayerUri())) {

                    //return new ResultInfo(-1,"客户开票资料不全，无法开具增值税专用发票，请完善信息！");
                    return -3;

                }

            }

            if (saleorder != null) {
                invoiceApply.setType(SysOptionConstant.ID_504);
                int i = invoiceApplyMapper.insert(invoiceApply);
                return i;
            }

        }

        return 0;

    }

    @Override
    public AfterSalesGoodsVo getAfterOpenInvoiceInfoAt(AfterSalesGoodsVo afterSalesGoodsVo) {

        //获取安调产品信息（特殊产品，安调费用）
        afterSalesGoodsVo = invoiceAfterMapper.getAfterSalesGoodsAtInfo(afterSalesGoodsVo);

        if (afterSalesGoodsVo != null) {

            //查询已开票信息
            Map<String, BigDecimal> map = invoiceAfterMapper.getAfterOpenInvoiceInfoAt(afterSalesGoodsVo.getAfterSalesId());

            if (map != null) {
                afterSalesGoodsVo.setInvoiceNum(map.get("NUM"));
                afterSalesGoodsVo.setInvoiceAmount(map.get("AMOUNT"));
            }

        }

        return afterSalesGoodsVo;

    }

    @Override
    public ResultInfo<?> saveAfterOpenInvoiceAt(Invoice invoice) {

        InvoiceDetail invoiceDetail = new InvoiceDetail();

        for (int i = 0; i < invoice.getDetailGoodsIdList().size(); i++) {
            invoiceDetail.setDetailgoodsId(invoice.getDetailGoodsIdList().get(i));
            invoiceDetail.setNum(invoice.getInvoiceNumList().get(i));
            invoiceDetail.setPrice(invoice.getInvoicePriceList().get(i));
            invoiceDetail.setTotalAmount(invoice.getInvoiceTotleAmountList().get(i));
        }

        //插入发票主表--蓝字作废发票记录，根据已有发票ID
        int j = invoiceMapper.insert(invoice);

        if (j == 1) {

            invoiceDetail.setInvoiceId(invoice.getInvoiceId());

            int insert = invoiceDetailMapper.insert(invoiceDetail);

            if (insert == 1) {

                //售后安调维修开票--修改申请表审核状态
                invoiceAfterMapper.updateAfterOpenInvoiceApply(invoice);

            }

            //售后-开票
            if (invoice.getType().equals(504) && invoice.getTag().equals(1)) {

                //查询售后需开票的金额（服务费金额）
                AfterSalesDetailVo afterSalesDetail = invoiceAfterMapper.getAfterSalesDetail(invoice.getRelatedId());

                if (afterSalesDetail != null) {
                    invoiceAfterMapper.updateAfterDetailInvoiceStatus(afterSalesDetail);
                }

            }

            return new ResultInfo(0, "操作成功");

        }

        return new ResultInfo();

    }

    @Override
    public AfterSalesInstallstionVo getAfterSalesInstallstionVo(AfterSalesInstallstionVo afterSalesInstallstionVo) {

        afterSalesInstallstionVo = afterSalesInstallstionMapper.getEditAfterSalesInstallstionVo(afterSalesInstallstionVo);

        if (afterSalesInstallstionVo != null) {

            AfterSalesVo af = new AfterSalesVo();

            af.setAfterSalesId(afterSalesInstallstionVo.getAfterSalesId());
            afterSalesInstallstionVo.setAfterSalesGoodsVoList(afterSalesService.getAfterSalesGoodsVoListByParamNew(af));

            com.vedeng.aftersales.model.RInstallstionJGoods rin = new com.vedeng.aftersales.model.RInstallstionJGoods();

            rin.setAfterSalesInstallstionId(afterSalesInstallstionVo.getAfterSalesInstallstionId());
            afterSalesInstallstionVo.setRiInstallstionJGoodList(afterSalesService.getRInstallstionJGoodsList(rin));

        }

        return afterSalesInstallstionVo;

    }

    @Override
    public AfterSalesInstallstion getAfterSalesInstallstion(AfterSalesInstallstion afterSalesInstallstion) {
        return afterSalesInstallstionMapper.selectByPrimaryKey(afterSalesInstallstion.getAfterSalesInstallstionId());
    }

    public List<CapitalBill> getCaptionBillList(CapitalBill capitalBill) {
        //获取交易记录
        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
        capitalBillDetail.setOrderType(Constants.THREE);//售后订单类型
        /*capitalBillDetail.setOrderNo(afterSalesVo.getOrderNo());*/
        capitalBillDetail.setRelatedId(capitalBill.getRelatedId());
        capitalBill.setCapitalBillDetail(capitalBillDetail);
        List<CapitalBill> list = capitalBillMapper.getCapitalBillList(capitalBill);
        return list;
    }

    @Override
    public void saveCollectionAmountAtStatus(Integer afterSalesId) {
        AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (null != afterSalesDetailVo) {
            switch (AfterSalesProcessEnum.getInstance(afterSalesDetailVo.getAfterType())) {
                case AFTERASALES_THIRD_AT:
                case AFTERASALES_THIRD_WX:
                case AFTERSALES_HH:
                case AFTERSALES_WX:
                case AFTERSALES_AT:
                case AFTERSALES_ATY:
                case AFTERSALES_ATN: {
                    if (afterSalesDetailVo.getServiceAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        //售后服务费小于等于0--更新收款状态为无收款
                        saveCollectionAmountStatus(afterSalesId, ErpConst.ZERO);
                    } else {
                        CapitalBill capitalBill = new CapitalBill();
                        capitalBill.setRelatedId(afterSalesId);
                        List<CapitalBill> list = getCaptionBillList(capitalBill);
                        if (CollectionUtils.isNotEmpty(list)) {
                            //已收金额
                            BigDecimal amountReceived = list.stream().filter(t -> null != t.getCapitalBillDetail() && SysOptionConstant.ID_526.equals(t.getCapitalBillDetail().getBussinessType())).map(CapitalBill::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            //实际应收
                            BigDecimal seriveAmount = afterSalesDetailVo == null ? BigDecimal.ZERO : afterSalesDetailVo.getServiceAmount();
                            if (amountReceived.compareTo(BigDecimal.ZERO) == 0) {
                                saveCollectionAmountStatus(afterSalesId, ErpConst.ONE);
                            } else {
                                if (amountReceived.compareTo(seriveAmount) < 0) {
                                    saveCollectionAmountStatus(afterSalesId, ErpConst.TWO);
                                } else {
                                    saveCollectionAmountStatus(afterSalesId, ErpConst.THREE);
                                }
                            }
                        } else {
                            //售后服务费不等于0且收款流水为空--更新收款状态为未收款
                            saveCollectionAmountStatus(afterSalesId, ErpConst.ONE);
                        }
                    }
                    break;
                }
            }
        }

    }

    @Override
    public ResultInfo<?> complementTask(HttpServletRequest request, String taskId, String comment, String assignee,
                                        Map<String, Object> variables) {
        logger.info("================开始完成审核节点===taskId:" + taskId + "===comment:" + comment + "===assignee:" + assignee + "================");
        User user = null;
        if (request != null) {
            user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        } else {
            user = new User();
            user.setUserId(ErpConst.TWO);
            user.setUsername("njadmin");
            user.setCompanyId(ErpConst.ONE);
        }
        HistoryService historyService = this.getProcessEngine().getHistoryService(); // 任务相关service
        TaskService taskService = this.getProcessEngine().getTaskService();
        // 使用任务id,获取任务对象，获取流程实例id
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
        }
        // 利用任务对象，获取流程实例id
        String processInstancesId = task.getProcessInstanceId();
        if (comment != null) {
            taskService.addComment(taskId, processInstancesId, comment);
        }
        if (assignee != null) {
            taskService.setAssignee(taskId, assignee);
        }
        try {
            taskService.complete(taskId, variables);
            logger.info("================完成审核节点==={}:================", taskId);
            List<HistoricActivityInstance> hia = historyService.createHistoricActivityInstanceQuery()
                    .processInstanceId(processInstancesId).orderByHistoricActivityInstanceStartTime().asc().list();
            String endStatus = hia.get(hia.size() - 1).getActivityType();
//			//打印历史节点日志
//			for(HistoricActivityInstance hiai:hia){
//				logger.info("================历史审核节点对应信息===taskId:"+taskId+"=================ActivityType:"+(hiai!=null ? hiai.getActivityType() : ""));
//			}
//            logger.info("================结束完成审核节点===taskId:"+taskId+"===comment:"+comment+"===assignee:"+assignee+"=====endStatus:"+endStatus+"===========");
            return new ResultInfo(0, "操作成功", endStatus);
        } catch (Exception e) {
            logger.error("complementTask任务完成操作失败",e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }

    @Override
    public void saveMakeOutInvoiceCompleteData(Integer relatedId) {

        //审核通过时相关节点状态更新
        //AfterSales afterSales = afterSalesMapper.getAfterSalesById(relatedId);
        AfterSalesDetailVo afterSales = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(relatedId);

        logger.info("审核 通过时相关节点状态更新 afterSales:{}", JSON.toJSONString(afterSales));

        switch (AfterSalesProcessEnum.getInstance(afterSales.getAfterType())) {
            case AFTERSALES_HH:
            case AFTERSALES_TH: {
                List<InvoiceApply> invoiceApplyList = invoiceApplyMapper.getAftersaleInvoiceApplyByRelatedId(relatedId);
                if (CollectionUtils.isEmpty(invoiceApplyList)) {
                    //无开票申请，开票状态为无开票
                    afterSalesMapper.saveMakeOutInvoiceStatus(relatedId, ErpConst.ZERO);
                } else {
                    List<AfterSalesInvoiceVo> voList = afterSalesInvoiceMapper.getAfterInvoiceList(relatedId, SysOptionConstant.ID_504, ErpConst.ONE);
                    if (CollectionUtils.isEmpty(voList)) {
                        //存在开票申请，不存在开票记录，开票状态为未开票
                        afterSalesMapper.saveMakeOutInvoiceStatus(relatedId, ErpConst.ONE);
                    } else {
                        //存在开票申请，存在开票记录，开票状态为全部开票
                        afterSalesMapper.saveMakeOutInvoiceStatus(relatedId, ErpConst.TWO);
                    }
                }
                break;
            }
            case AFTERASALES_THIRD_AT:
            case AFTERASALES_THIRD_WX:
            case AFTERSALES_ATY:
            case AFTERSALES_ATN:
            case AFTERSALES_WX:
            case AFTERSALES_AT: {
                List<AfterSalesInvoiceVo> voList = afterSalesInvoiceMapper.getAfterInvoiceList(relatedId, SysOptionConstant.ID_504, ErpConst.ONE);

                //应开
                BigDecimal invoiceAmount = afterSales.getServiceAmount();

                if (invoiceAmount.compareTo(BigDecimal.ZERO) == 0) {
                    saveMakeOutInvoiceStatus(relatedId, ErpConst.ZERO);
                } else if (invoiceAmount.compareTo(BigDecimal.ZERO) > 0) {

                    if (CollectionUtils.isNotEmpty(voList)) {

                        BigDecimal makeOutInvoice = voList.stream().map(AfterSalesInvoiceVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (makeOutInvoice.compareTo(BigDecimal.ZERO) == 0) {
                            saveMakeOutInvoiceStatus(relatedId, ErpConst.ONE);
                        } else {
                            saveMakeOutInvoiceStatus(relatedId, ErpConst.TWO);
                            /*if (invoiceAmount.compareTo(makeOutInvoice) > 0) {
                                //无部分开票状态
                                //saveMakeOutInvoiceStatus(relatedId, ErpConst.THREE);
                            } else if (invoiceAmount.compareTo(makeOutInvoice) == 0) {
                                saveMakeOutInvoiceStatus(relatedId, ErpConst.TWO);
                            }*/
                        }
                    } else {
                        saveMakeOutInvoiceStatus(relatedId, ErpConst.ONE);
                    }

                }

                break;
            }
        }
    }

    /*@Override
    public void getInvoiceRefreshStatus(Integer afterSalesId){

        //金额
        AfterSalesDetailVo detailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if(null != detailVo) {
            switch (AfterSalesProcessEnum.getInstance(detailVo.getAfterType())) {
                case AFTERSALES_WX:
                case AFTERSALES_AT: {

                    List<AfterSalesInvoiceVo> voList = afterSalesInvoiceMapper.getAfterInvoiceList(afterSalesId,SysOptionConstant.ID_504,ErpConst.ONE);
                    //应开
                    BigDecimal invoiceAmount = detailVo.getServiceAmount();

                    if (invoiceAmount.compareTo(BigDecimal.ZERO) == 0){
                        saveMakeOutInvoiceStatus(afterSalesId, ErpConst.ZERO);
                    }else if (invoiceAmount.compareTo(BigDecimal.ZERO)>0) {
                        if (CollectionUtils.isNotEmpty(voList)) {
                            BigDecimal makeOutInvoice = voList.stream().map(AfterSalesInvoiceVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                            if (makeOutInvoice.compareTo(BigDecimal.ZERO) == 0){
                                saveMakeOutInvoiceStatus(afterSalesId, ErpConst.ONE);
                            }else {
                                if (invoiceAmount.compareTo(makeOutInvoice) > 0) {
                                    //无部分开票状态
                                    //saveMakeOutInvoiceStatus(relatedId, ErpConst.THREE);
                                } else if (invoiceAmount.compareTo(makeOutInvoice) == 0) {
                                    saveMakeOutInvoiceStatus(afterSalesId, ErpConst.TWO);
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }

    }*/

    @Override
    public void savePayAmountAtStatuts(Integer relatedId) {

        AfterSalesDetailVo detailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(relatedId);
        if (null != detailVo) {
            switch (AfterSalesProcessEnum.getInstance(detailVo.getAfterType())) {
                case AFTERASALES_THIRD_AT:
                case AFTERASALES_THIRD_WX:
                case AFTERSALES_ATY:
                case AFTERSALES_ATN:
                case AFTERSALES_WX:
                case AFTERSALES_AT: {
                    CapitalBill capitalBill = new CapitalBill();
                    capitalBill.setRelatedId(relatedId);
                    List<CapitalBill> list = getCaptionBillList(capitalBill);
                    //实际应付
                    //AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(capitalBill.getRelatedId());
                    //BigDecimal seriveAmount = afterSalesDetailVo == null ? BigDecimal.ZERO: afterSalesDetailVo.getServiceAmount();
                    //List<PayApply> payApplyList = afterSalesInvoiceMapper.getAfterAtPaymentApply(capitalBill.getRelatedId());
                    /*if (CollectionUtils.isEmpty(payApplyList)){
                        savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ZERO);
                        break;
                    }*/
                    // 获取工程师酬金总金额
                    BigDecimal seriveAmount = afterSalesDataService.getAfterSalesInstallstionAmount(capitalBill.getRelatedId());
                    if (seriveAmount.compareTo(BigDecimal.ZERO) == 0) {
                        savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ZERO);
                    } else if (seriveAmount.compareTo(BigDecimal.ZERO) > 0) {
                        savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ONE);
                    }
                    if (CollectionUtils.isNotEmpty(list)) {
                        //已付金额
                        BigDecimal amountReceived = list.stream().filter(t -> null != t.getCapitalBillDetail() && SysOptionConstant.ID_525.equals(t.getCapitalBillDetail().getBussinessType())).map(CapitalBill::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

                        //BigDecimal seriveAmount = payApplyList.stream().filter(Objects::nonNull).filter(s-> ErpConst.ONE.equals(s.getValidStatus())).map(PayApply::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);
                        //BigDecimal seriveAmount = payApplyList.stream().filter(Objects::nonNull).filter(s-> ErpConst.ONE.equals(s.getValidStatus())).map(PayApply::getAmount).reduce(BigDecimal.ZERO,BigDecimal::add);

                        if (seriveAmount.compareTo(BigDecimal.ZERO) > 0) {
                            if (amountReceived.compareTo(BigDecimal.ZERO) == 0) {
                                savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.ONE);
                            } else {
                                if (amountReceived.compareTo(seriveAmount) < 0) {
                                    savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.TWO);
                                } else {
                                    savePayAmountStatuts(capitalBill.getRelatedId(), ErpConst.THREE);
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }

    }

    @Override
    public List<Attachment> getAttachmentListByParam(AfterSalesVo afterSales) {

        //排除技术咨询和其他
        Attachment attachment = new Attachment();
        attachment.setRelatedId(afterSales.getAfterSalesId());
        attachment.setAttachmentFunction(afterSales.getType());
        List<Attachment> attachments = attachmentMapper.getAfterSaleAttachmentList(attachment);
        return attachments;
    }

    @Override
    public Boolean saveAttachment(AfterSalesVo afterSales, String[] fileNames, String[] fileUri) {
        //添加附件
        try {
            Attachment att = new Attachment();
            att.setAttachmentFunction(afterSales.getAfterSalesType());
            att.setRelatedId(afterSales.getAfterSalesId());
            attachmentMapper.delAttachment(att);
            for (int i = 0; i < fileNames.length; i++) {
                String name = fileNames[i];
                String uri = fileUri[i];
                if (ObjectUtils.notEmpty(name) && ObjectUtils.notEmpty(uri)) {
                    Attachment attachment = new Attachment();
                    attachment.setName(name);
                    attachment.setUri(uri);
                    if (checkPic(uri)) {
                        attachment.setAttachmentType(SysOptionConstant.ID_460);
                    } else {
                        attachment.setAttachmentType(SysOptionConstant.ID_461);
                    }
                    attachment.setAttachmentFunction(afterSales.getAfterSalesType());
                    attachment.setRelatedId(afterSales.getAfterSalesId());
                    attachment.setCreator(afterSales.getCreator());
                    attachment.setAddTime(afterSales.getAddTime());

                    if (uri.indexOf("resourceId") >= 0) {
                        attachment.setDomain(this.ossUrl);
                    } else {
                        attachment.setDomain(this.fileUrl);
                    }

                    int res3 = attachmentMapper.insert(attachment);
                    if (res3 == 0) {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            logger.info("保存附件失败", e);
            return false;
        }
    }

    @Override
    public Boolean checkPic(String picUrl) {
        if (ObjectUtils.notEmpty(picUrl)) {
            /*BufferedImage image = ImageIO.read(file);*/
            if (picUrl.contains(SysOptionConstant.SUFFIX_IMG) || picUrl.contains(SysOptionConstant.SUFFIX_PNG) || picUrl.contains(SysOptionConstant.SUFFIX_GIF) || picUrl.contains(SysOptionConstant.SUFFIX_BMP)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Integer saveEditAfterSales(AfterSalesVo afterSalesVo) throws Exception {

        if (afterSalesVo != null) {
            // add by frnlin.wu for [耗材商城售后：提出代码] at 2018-12-18 begin
            // 订单ID
            Integer orderId = afterSalesVo.getOrderId();
            // 订单号
            String orderNo = afterSalesVo.getOrderNo();
            // 售后类型
            Integer type = afterSalesVo.getType();
            // 售后主体
            Integer subjectType = afterSalesVo.getSubjectType();
            // 公司ID
            Integer companyId = afterSalesVo.getCompanyId();
            // 售后来源
            Integer source = afterSalesVo.getSource();
            // 售后订单ID
            Integer afterSalesId = afterSalesVo.getAfterSalesId();
            logger.info("编辑售后订单，订单ID:{}, 订单号:{}, 售后类型:{}, 售后主体:{}, 公司ID:{}, source:{}", orderId, orderNo, type, subjectType, companyId, source);
            //VDERP-2469  配合前台和指南针优惠券功能，ERP相应的改动
            //增加售后关联销售单类型
            afterSalesVo.setOrderType(SysOptionConstant.MINUS_ONE);
            if (SysOptionConstant.ID_539.equals(type)) {
                Saleorder saleorder = saleorderMapper.getSaleorderInfoById(orderId);
                afterSalesVo.setOrderType(saleorder.getOrderType());
            }

            // add by Tomcat.Hui 2020/8/17 10:16 下午 .Desc: VDERP-3275 【修复】【历史遗留】HC订单售后锁定状态错误. start
            //同步过来的订单编辑时增加锁定状态
            //销售订单退货 销售订单换 货销售订单退款 三种类型需要加锁
            if (Arrays.asList(539, 540, 543).contains(type.intValue())) {
                Saleorder saleorderUpdate = new Saleorder();
                saleorderUpdate.setSaleorderId(orderId);
                saleorderUpdate.setLockedStatus(ErpConst.ONE);
                saleorderUpdate.setLockedReason("售后锁定");
                saleorderMapper.updateByPrimaryKeySelective(saleorderUpdate);
                updateLockSaleorderWarning(orderId);
            }
            // add by Tomcat.Hui 2020/8/17 10:16 下午 .Desc: VDERP-3275 【修复】【历史遗留】HC订单售后锁定状态错误. end

            // 统计销售订单sku商品数量 和 销售订单的sku的小计
            Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap = new HashMap<Integer, MapExtendsVo<Integer, BigDecimal>>();
            // 销售退货 && 是 耗材售后
            if (SysOptionConstant.ID_539.equals(type) && (CommonConstants.ONE.equals(source) || ErpConst.ONE.equals(afterSalesVo.getOrderType()))) {
                Saleorder saleorder = new Saleorder();
                saleorder.setSaleorderId(afterSalesVo.getOrderId());
                List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
                // 查询特殊商品id的list
                List<Integer> specialGoodIdList = getSpecialGoodsId();
                // modify by franlin.wu [统计销售订单sku商品数量] at 2018-12-18 begin
                if (CollectionUtils.isNotEmpty(sgvList)) {
                    // 当前sku的商品信息
                    sgvList.stream().filter(Objects::nonNull).filter(s -> null == s.getGoodsId())
                            .filter(s -> CommonConstants.ONE.equals(source) || ErpConst.ONE.equals(afterSalesVo.getOrderType()))
                            .forEach(s -> {
                                // 统计每个sku的销售订单数量和sku的小计
                                MapExtendsVo<Integer, BigDecimal> skuNumAndAmount = new MapExtendsVo<Integer, BigDecimal>();
                                // 数量
                                skuNumAndAmount.setExData(s.getNum());
                                // sku的小计
                                skuNumAndAmount.setValue(null == s.getMaxSkuRefundAmount() ? BigDecimal.ZERO : s.getMaxSkuRefundAmount());
                                // 循环特殊商品ID
                                specialGoodIdList.stream().forEach(t -> {
                                    // 商品ID
                                    if (s.getGoodsId().equals(t)) {
                                        // sku的小计 ，特殊商品最大退款金额为当前单价
                                        skuNumAndAmount.setValue(null == s.getPrice() ? BigDecimal.ZERO : s.getPrice());
                                    }
                                });
                                // 统计
                                skuNumMap.put(s.getSaleorderGoodsId(), skuNumAndAmount);
                            });
                }
            }

            // add by frnlin.wu for [耗材商城售后：提出代码] at 2018-12-18 end

            AfterSales afterSales = new AfterSales();
            afterSales.setOrderId(orderId);
            afterSales.setOrderNo(orderNo);
            afterSales.setAfterSalesId(afterSalesId);
            afterSales.setModTime(afterSalesVo.getModTime());
            afterSales.setStatus(afterSalesVo.getStatus());
            afterSales.setUpdater(afterSalesVo.getUpdater());
            afterSales.setType(type);
            afterSales.setSubjectType(subjectType);
            afterSales.setCompanyId(companyId);
            Integer res = afterSalesMapper.updateByPrimaryKeySelective(afterSales);

            if (res > 0) {
                //先判断售后单是否包含商品，如果有再去更新订单商品锁定状态和删除原有售后单商品
                if (afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesId(afterSalesId).size() > 0) {
                    //解除商品锁定状态
                    List<Integer> saleorderGoodsIds = afterSalesGoodsMapper.getSaleorderGoodsIdByAftersaleId(afterSalesVo.getAfterSalesId());
                    saleorderGoodsMapper.updateAfterGoodsLockStuts(saleorderGoodsIds);
                    // 删除售后商品
                    afterSalesGoodsMapper.delAfterSalesGoodsByParam(afterSalesVo.getAfterSalesId());
                }

                // 统计当前
                Map<Integer, Integer> nowReundSkuNumMap = new HashMap<Integer, Integer>();
                // 添加售后订单商品
                if (!addAfterSaleForGoods(afterSalesVo, afterSalesId, nowReundSkuNumMap, ErpConst.TWO)) {
                    return 0;
                }
                // 涉及耗材售后订单 退款金额
                BigDecimal hcReundAmount = BigDecimal.ZERO;
                try {
                    hcReundAmount = subReundAmountBySourceAndType(afterSalesVo, afterSalesId, nowReundSkuNumMap, skuNumMap);
                } catch (Exception e) {
                    logger.error("业务异常", e);
                    throw e;
                }

                // 添加售后详情
                if (!addAfterSaleDetails(afterSalesVo, afterSalesId, source, hcReundAmount, ErpConst.TWO)) {
                    return 0;
                }

                //添加附件
                String[] fileNames = afterSalesVo.getAttachName();
                String[] fileUri = afterSalesVo.getAttachUri();
                if (fileNames != null && fileUri != null) {
                    AfterSalesVo afterSalesAttach = new AfterSalesVo();
                    afterSalesAttach.setAfterSalesType(afterSalesVo.getType());
                    afterSalesAttach.setAfterSalesId(afterSalesVo.getAfterSalesId());

                    Boolean boolRes = saveAttachment(afterSalesAttach, fileNames, fileUri);

                    if (!boolRes) {
                        logger.info("保存售后信息附件,失败！afterSalesId:" + afterSalesAttach.getAfterSalesId());
                        return 0;
                    }
                }

                if (null != afterSalesVo.getSku() && !("".equals(afterSalesVo.getSku()))) {
                    String[] skus = afterSalesVo.getSku().split(",");
                    Object[] skuClear = oneClear(skus);
                    String[] sku = Arrays.copyOf(skuClear, skuClear.length, String[].class);
                    List<AfterSalesGoodsVo> list = new ArrayList<>();
                    for (int i = 0; i < sku.length; i++) {
                        AfterSalesGoodsVo afterSalesGoodsVo = new AfterSalesGoodsVo();
                        if (null != sku[i].trim() && !("".equals(sku[i].trim()))) {
                            afterSalesGoodsVo.setSku(sku[i]);
                            afterSalesGoodsVo.setSaleorderId(afterSalesVo.getSaleorderId());
                            list.add(afterSalesGoodsVo);
                        }
                    }
                    if (list.size() > 0) {
                        int reloc = saleorderGoodsMapper.updateGoodsLockStatusBySku(list);
                        if (reloc == 0) {
                            return 0;
                        }
                    }
                }
                String[] invoiceIds = afterSalesVo.getInvoiceIds();
                if (invoiceIds != null) {
                    //先删除售后订单之前关联的发票信息
                    afterSalesInvoiceMapper.delAfterSalesInvoiceByAfterSalesId(afterSales.getAfterSalesId());
                    for (int i = 0; i < invoiceIds.length; i++) {
                        if (!"".equals(invoiceIds[i])) {
                            AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
                            afterSalesInvoice.setInvoiceId(Integer.valueOf(invoiceIds[i]));
                            afterSalesInvoice.setAfterSalesId(afterSales.getAfterSalesId());
                            afterSalesInvoice.setIsRefundInvoice(ErpConst.ONE);
                            int res4 = afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
                            if (res4 == 0) {
                                return 0;
                            }
                        }
                    }
                }
            } else {
                return 0;
            }
            return afterSales.getAfterSalesId();
        }
        return 0;

    }

    public static Object[] oneClear(Object[] arr) {
        Set set = new HashSet();

        Arrays.asList(arr).stream().forEach(x -> set.add(x));

        return set.toArray();
    }

    /**
     *
     * <b>Description: 添加售后详情</b><br>
     * @param afterSalesVo
     * @param afterSalesId
     * @param source   订单来源
     * @param hcReundAmount   耗材退货涉及退款金额
     * @return
     * <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年12月18日 上午9:32:51 </b>
     */
    /**
     * <b>Description:</b><br>
     *
     * @param afterSalesVo
     * @param afterSalesId
     * @param source        [订单来源] 1-- 耗材订单售后 / 0 erp的订单售后
     * @param hcReundAmount
     * @param operateType   [操作类型] 1-- 新增/ 2-- 编辑
     * @return <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年12月18日 下午3:52:15 </b>
     */
    private boolean addAfterSaleDetails(AfterSalesVo afterSalesVo, Integer afterSalesId, Integer source, BigDecimal hcReundAmount, int operateType) {
        logger.info("添加售后详情信息入参" + JSON.toJSONString(afterSalesVo) + "afterSalesId=" + afterSalesId + "source=" + source + "hcReundAmount=" + hcReundAmount + "operateType=" + operateType);
        // 订单ID
        Integer orderId = afterSalesVo.getOrderId();
        // 售后类型
        Integer type = afterSalesVo.getType();
        // 售后主体
        Integer subjectType = afterSalesVo.getSubjectType();

        //添加详情表信息----开始
        AfterSalesDetail afterSalesDetail = new AfterSalesDetail();
        afterSalesDetail.setAfterConnectUserName(afterSalesVo.getAfterConnectUserName());
        afterSalesDetail.setAfterConnectPhone(afterSalesVo.getAfterConnectPhone());

        /****************计算实退金额开始************************/
        //BigDecimal perid = new BigDecimal(0);
        //BigDecimal realRefundAmount = new BigDecimal(0);
        // add by for franlin.wu [耗材售后] at 2018-12-18 begin

        if (CommonConstants.ONE.equals(source) || (ErpConst.ONE.equals(afterSalesVo.getOrderType()) && (!SysOptionConstant.ID_543.equals(type)))) {
            //退款金额
            afterSalesDetail.setRefundAmount(hcReundAmount);
        } else {
            afterSalesDetail.setRefundAmount(afterSalesVo.getRefundAmount());//退款金额
        }
        // add by for franlin.wu [耗材售后] at 2018-12-18 end
        afterSalesDetail.setTraderMode(afterSalesVo.getTraderMode());//交易方式（字典表查）

        // 销售订单退款 or 第三方退款 or 耗材 && 线上
        if (SysOptionConstant.ID_543.equals(type) || AfterSalesProcessEnum.THIRD_AMOUNT_RETURN.getCode().equals(type)) {
            // 款项退还0无 1退到客户余额 2退给客户
            afterSalesDetail.setRefund(ErpConst.TWO);
        } else {
            afterSalesDetail.setRefund(afterSalesVo.getRefund());
        }
        // 退款状态0无退款1未退款 2部分退款 3已退款
        afterSalesDetail.setRefundAmountStatus(afterSalesVo.getRefundAmountStatus());
//		//之前售后订单的已退款总额
//		BigDecimal hasRefunded = afterSalesMapper.getSumTKAfterSalesBySaleorderid(afterSales.getOrderId());
        //销售---售后服务费>0
        if (SysOptionConstant.ID_535.equals(subjectType)) {
            if (SysOptionConstant.ID_539.equals(type)) {
                // 订单已付款金额，不含账期
                BigDecimal pa = saleorderMapper.getPaymentAndPeriodAmount(orderId);
                // 已付款金额（不含账期）
                afterSalesDetail.setPaymentAmount(pa);
                // 耗材售后
                if (CommonConstants.ONE.equals(source)) {
                    // 根据订单ID查询销售订单信息
                    Saleorder saleorder = saleorderMapper.selectByPrimaryKey(orderId);
                    // 空
                    if (null == saleorder) {
                        return false;
                    }
                    // 支付方式：0线上、1线下  默认1
                    Integer paymentMode = saleorder.getPaymentMode();
                    // 支付方式：1支付宝、2微信、3银行
                    Integer payType = saleorder.getPayType();
                    // 默认 退给客户
                    if (CommonConstants.ZERO.equals(paymentMode)) {
                        afterSalesDetail.setRefund(ErpConst.TWO);
                    }
                    // 默认0
                    Integer traderMode = Constants.ONE.equals(payType) ? 520 : Constants.TWO.equals(payType) ? 522 : Constants.THREE.equals(payType) ? 521 : 0;
                    // 交易方式
                    afterSalesDetail.setTraderMode(traderMode);
                    //实退金额
                    //查询当前订单的所有已完结退货总额
                    BigDecimal tk = afterSalesMapper.getSumTKAfterSalesBySaleorderid(orderId);
                    //BigDecimal perid = saleorderDataService.getPeriodAmount(afterSales.getOrderId());//账期金额
                    BigDecimal lackperid = saleorderMapper.getSaleorderLackAccountPeriodAmount(orderId);//账期欠款金额
                    BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk));
                    BigDecimal payPeriod = new BigDecimal(0);//偿还帐期金额
                    if (PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(BigDecimal.ZERO) > 0) {
                        payPeriod = PreAnnealing;
                    } else if (lackperid.compareTo(BigDecimal.ZERO) > 0) {
                        payPeriod = lackperid;
                    }

                    afterSalesDetail.setPayPeriodAmount(payPeriod);
                    logger.info("订单ID" + orderId + "售后单ID" + afterSalesId + "实退金额=" + PreAnnealing + "-" + payPeriod);
                    if (PreAnnealing.compareTo(BigDecimal.ZERO) > 0) {//预付款大于0
                        afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod));//实退金额
                        if (afterSalesDetail.getRealRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                            afterSalesDetail.setRefundAmountStatus(ErpConst.ONE);
                        }
                    } else {
                        afterSalesDetail.setRealRefundAmount(BigDecimal.ZERO);
                    }
                    // 实付金额大于0则 修改退款状态：未退款
				/*	if(hcReundAmount.compareTo(BigDecimal.ZERO) > 0)
					{
						afterSalesDetail.setRefundAmountStatus(ErpConst.ONE);
					}*/
                } else {
                    //查询当前订单的所有已完结退货总额
                    BigDecimal tk = afterSalesMapper.getSumTKAfterSalesBySaleorderid(orderId);

                    //BigDecimal perid = saleorderDataService.getPeriodAmount(afterSales.getOrderId());//账期金额
                    BigDecimal lackperid = saleorderMapper.getSaleorderLackAccountPeriodAmount(orderId);//账期欠款金额
                    Saleorder saleorder = saleorderMapper.selectByPrimaryKey(orderId);

                    //支付宝提现
                    BigDecimal zhifubao = capitalBillMapper.getzhifubaoAmount(orderId);

                    // 预退款金额=订单已付款金额+退货产品金额-订单金额，（此处订单已付款包含帐期支付）。如果预退款金额<=0时，按0处理，即不退款，偿还帐期金额=实际退款金额=0；
//					BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk).subtract(zhifubao));

                    BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk));
                    //BigDecimal PreAnnealing = pa.add(saleorder.getAccountPeriodAmount()).add(afterSalesDetail.getRefundAmount()).subtract(saleorderDataService.getRealAmount(afterSales.getOrderId())).subtract(tk);
                    BigDecimal payPeriod = new BigDecimal(0);//偿还帐期金额
                    if (PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(BigDecimal.ZERO) > 0) {
                        payPeriod = PreAnnealing;
                    } else if (lackperid.compareTo(BigDecimal.ZERO) > 0) {
                        payPeriod = lackperid;
                    } else {
                        payPeriod = new BigDecimal(0);
                    }
                    afterSalesDetail.setPayPeriodAmount(payPeriod);
                    logger.info("2订单ID" + orderId + "售后单ID" + afterSalesId + "实退金额=" + PreAnnealing + "-" + payPeriod);
                    if (PreAnnealing.compareTo(BigDecimal.ZERO) > 0) {//预付款大于0
                        afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod));//实退金额
                        if (afterSalesDetail.getRealRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                            afterSalesDetail.setRefundAmountStatus(ErpConst.ONE);
                        }
                    } else {
                        afterSalesDetail.setRealRefundAmount(BigDecimal.ZERO);
                    }
                }
            } else if (type == 543) {
                afterSalesDetail.setRealRefundAmount(afterSalesDetail.getRefundAmount());//实退金额
            }
        } else if (subjectType == 536 && type == 546) {//采购---售后服务费<0
            BigDecimal pa = buyorderDataService.getPaymentAndPeriodAmount(orderId).abs();//订单已付款金额，不含账期
            afterSalesDetail.setPaymentAmount(pa);
            //BigDecimal perid = buyorderDataService.getPeriodAmount(afterSales.getOrderId());//账期金额
            BigDecimal lackperid = buyorderDataService.getLackAccountPeriodAmount(orderId);//账期欠款金额
            Buyorder buyorder = buyorderMapper.selectByPrimaryKey(orderId);
            //查询当前采购订单的所有已完结退货售后
            BigDecimal tk = afterSalesMapper.getSumTKAfterSalesByBuyorderid(orderId);

//			afterSalesDetail.setRefundAmount(tk.add(afterSalesDetail.getRefundAmount()));

            // 预退款金额=订单已付款金额+退货产品金额-订单金额，（此处订单已付款包含帐期支付）。如果预退款金额<=0时，按0处理，即不退款，偿还帐期金额=实际退款金额=0；
            BigDecimal PreAnnealing = pa.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(buyorder.getTotalAmount().subtract(tk));
            //BigDecimal PreAnnealing = pa.add(buyorder.getAccountPeriodAmount()).add(afterSalesDetail.getRefundAmount()).subtract(buyorderDataService.getRealAmount(afterSales.getOrderId())).subtract(tk);
            BigDecimal payPeriod = new BigDecimal(0);//偿还帐期金额
            if (PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(BigDecimal.ZERO) > 0) {
                payPeriod = PreAnnealing;
            } else if (lackperid.compareTo(BigDecimal.ZERO) > 0) {
                payPeriod = lackperid;
            } else {
                payPeriod = new BigDecimal(0);
            }
            afterSalesDetail.setPayPeriodAmount(payPeriod);
            if (PreAnnealing.compareTo(BigDecimal.ZERO) > 0) {//预付款大于0
                afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod));//实退金额
                if (afterSalesDetail.getRealRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
                    afterSalesDetail.setRefundAmountStatus(ErpConst.ONE);
                }
            } else {
                afterSalesDetail.setRealRefundAmount(BigDecimal.ZERO);
            }
        } else if (SysOptionConstant.ID_537.equals(subjectType) && AfterSalesProcessEnum.THIRD_AMOUNT_RETURN.equals(type)) {
            afterSalesDetail.setRealRefundAmount(afterSalesVo.getRefundAmount());//实退金额
        }
        // add by franlin.wu for [兼容编辑] at 2018-12-18 begin
        else if (2 == operateType && SysOptionConstant.ID_537.equals(subjectType) && (AfterSalesProcessEnum.AFTERASALES_THIRD_AT.equals(type) || AfterSalesProcessEnum.AFTERASALES_THIRD_WX.equals(type))) {
            afterSalesDetail.setServiceAmount(afterSalesVo.getServiceAmount());
            afterSalesDetail.setInvoiceType(afterSalesVo.getInvoiceType());
            afterSalesDetail.setIsSendInvoice(afterSalesVo.getIsSendInvoice());
        }
        // add by franlin.wu for [兼容编辑] at 2018-12-18 end
//		if(realRefundAmount.compareTo(BigDecimal.ZERO) > 0){
//			afterSalesDetail.setRefundAmountStatus(ErpConst.ONE);
//		}
        /****************计算实退金额结束************************/
        // add by franlin.wu for [兼容编辑] at 2018-12-18 begin
        if (2 == operateType && (SysOptionConstant.ID_546.equals(subjectType) || SysOptionConstant.ID_547.equals(subjectType))) {
            afterSalesDetail.setServiceAmount(afterSalesVo.getServiceAmount());
            afterSalesDetail.setInvoiceType(afterSalesVo.getInvoiceType());
            afterSalesDetail.setIsSendInvoice(afterSalesVo.getIsSendInvoice());
        }
        // add by franlin.wu for [兼容编辑] at 2018-12-18 end

        afterSalesDetail.setAfterSalesId(afterSalesId);
        afterSalesDetail.setComments(afterSalesVo.getComments());
        afterSalesDetail.setReason(afterSalesVo.getReason());

        afterSalesDetail.setTraderId(afterSalesVo.getTraderId());
        TraderContact traderContact = null;
        if (ObjectUtils.notEmpty(afterSalesVo.getTraderContactId())) {
            traderContact = traderContactMapper.selectByPrimaryKey(afterSalesVo.getTraderContactId());
        } else if (ObjectUtils.notEmpty(afterSalesVo.getTraderId())) {
            traderContact = new TraderContact();
            traderContact.setTraderId(afterSalesVo.getTraderId());
            traderContact.setTraderType(ErpConst.ONE);
            traderContact.setIsDefault(ErpConst.ONE);
            traderContact = traderContactMapper.getTraderContactVo(traderContact);//2018-2-7取第一条数据，兼容老数据
        }
        if (traderContact != null) {
            afterSalesDetail.setTraderContactId(afterSalesVo.getTraderContactId());
            afterSalesDetail.setTraderContactMobile(traderContact.getMobile());
            afterSalesDetail.setTraderContactName(traderContact.getName());
            afterSalesDetail.setTraderContactTelephone(traderContact.getTelephone());
            afterSalesDetail.setTraderId(traderContact.getTraderId());
        } else {
            afterSalesDetail.setTraderContactName(afterSalesVo.getTraderContactName());
            afterSalesDetail.setTraderContactMobile(afterSalesVo.getTraderContactMobile());
        }
        if (ObjectUtils.notEmpty(afterSalesVo.getTakeMsg()) && afterSalesVo.getTakeMsg().split("\\|").length == 4) {
            afterSalesDetail.setAddressId(Integer.valueOf(afterSalesVo.getTakeMsg().split("\\|")[0]));
            afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getTakeMsg().split("\\|")[1]));
            afterSalesDetail.setArea(afterSalesVo.getTakeMsg().split("\\|")[2]);
            afterSalesDetail.setAddress(afterSalesVo.getTakeMsg().split("\\|")[3]);
        }
        if (ObjectUtils.notEmpty(afterSalesVo.getProvince())) {
            //必须到区
            if (ObjectUtils.notEmpty(afterSalesVo.getZone())) {
                afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getZone()));
            } else if (ObjectUtils.notEmpty(afterSalesVo.getCity()) && !ObjectUtils.notEmpty(afterSalesVo.getZone())) {
                afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getCity()));
            } else {
                afterSalesDetail.setAreaId(Integer.valueOf(afterSalesVo.getProvince()));
            }
            afterSalesDetail.setArea(afterSalesVo.getArea());
            afterSalesDetail.setAddress(afterSalesVo.getAddress());
        }
        if (ObjectUtils.notEmpty(afterSalesVo.getTraderSubject())) {
            afterSalesDetail.setRefundAmount(afterSalesVo.getRefundAmount());
            afterSalesDetail.setTraderSubject(afterSalesVo.getTraderSubject());
            afterSalesDetail.setBank(afterSalesVo.getBank());
            afterSalesDetail.setBankAccount(afterSalesVo.getBankAccount());
            afterSalesDetail.setBankCode(afterSalesVo.getBankCode());

        }
        afterSalesDetail.setPayee(afterSalesVo.getPayee());

        // add by franlin.wu for [@1:兼容编辑| @2:避免执行退款运算时，当前的traderId为空，应该是之前查询traderId] at 2018-12-20/2019-04-22[modify@2] begin
        Integer res1 = 0;
        // 2 编辑
        if (CommonConstants.TWO.equals(operateType)) {
            // 编辑时页面没有传 afterSalesVo.getTraderId()
            afterSalesDetail.setAfterSalesDetailId(afterSalesVo.getAfterSalesDetailId());
            res1 = afterSalesDetailMapper.updateByPrimaryKeySelective(afterSalesDetail);
        } else {
            res1 = afterSalesDetailMapper.insertSelective(afterSalesDetail);
        }
        // add by franlin.wu for [@1:兼容编辑| @2:避免执行退款运算时，当前的traderId为空，应该是之前查询traderId] at 2018-12-20/2019-04-22[modify@2] end
        if (ErpConst.ZERO.equals(res1)) {
            return false;
        }
        return true;
    }

    /**
     * <b>Description: 计算耗材的售后退款金额</b><br>
     *
     * @param nowReundSkuNumMap
     * @param skuNumMap
     * @return <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年12月18日 下午1:22:19 </b>
     * @throws RunTimeServiceException
     */
    private BigDecimal subReundAmountBySourceAndType(AfterSalesVo afterSalesVo, Integer afterSalesId, Map<Integer, Integer> nowReundSkuNumMap, Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap) throws RunTimeServiceException {
        // 订单来源
        Integer source = afterSalesVo.getSource();
        // 售后类型
        Integer type = afterSalesVo.getType();
        // 订单ID
        Integer orderId = afterSalesVo.getOrderId();

        // 涉及耗材售后订单 退款金额
        BigDecimal hcReundAmount = BigDecimal.ZERO;
        // 耗材退货
        if ((CommonConstants.ONE.equals(source) || ErpConst.ONE.equals(afterSalesVo.getOrderType())) && SysOptionConstant.ID_539.equals(type)) {
            //  计算当前销售订单下每个SalesGoodsId已退货金额
            Map<Integer, MapExtendsVo<Integer, BigDecimal>> everySkuReundAmountMap = subSkuReundAmountByOrderId(orderId);

            Integer saleorderType = saleorderMapper.getSaleorderBySaleorderId(orderId).getOrderType();

            // 遍历 当前退货商品
            Iterator<Map.Entry<Integer, Integer>> iterator = nowReundSkuNumMap.entrySet().iterator();
            // 遍历
            while (iterator.hasNext()) {
                Map.Entry<Integer, Integer> entry = iterator.next();
                // 空，则继续
                if (null == entry || null == entry.getKey() || null == entry.getValue()) {
                    continue;
                }
                // 获取sa
                Integer goodsId = entry.getKey();
                // 当前SalesGoodsId的退货数量
                Integer reundSkuNum = entry.getValue();
                // 获取销售订单的商品信息
                MapExtendsVo<Integer, BigDecimal> orderSku = skuNumMap.get(goodsId);
                // 校验不通过
                if (null == orderSku || null == orderSku.getExData() || orderSku.getExData() < reundSkuNum) {
                    throw new RunTimeServiceException(CommonConstants.ONE, "当前退货数量大于销售订单销售数量");
                }
                // 当前销售订单的sku的数量
                Integer skuNum = orderSku.getExData();
                // 当前SalesGoodsId的最大退款金额
                BigDecimal skuTotalAmount = orderSku.getValue();
                // 当前sku已退数量
                BigDecimal alReturnNum = BigDecimal.ZERO;
                // 退货金额
                BigDecimal alReturnAmount = BigDecimal.ZERO;
                // 已经退货数量和退货金额
                MapExtendsVo<Integer, BigDecimal> alReundNumAndAmount = (null == everySkuReundAmountMap ? null : everySkuReundAmountMap.get(goodsId));
                // 非空
                if (null != alReundNumAndAmount) {
                    // 退货数量
                    alReturnNum = null == alReundNumAndAmount.getExData() ? alReturnNum : new BigDecimal(alReundNumAndAmount.getExData());
                    // 退货金额
                    alReturnAmount = null == alReundNumAndAmount.getValue() ? alReturnAmount : alReundNumAndAmount.getValue();
                }

                SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsByIdSample(goodsId);

                // 当前SalesGoodsId的退款小计
                BigDecimal nowSkuReundAmount = SaleAfterServiceUtil.subRefundAmount(new BigDecimal(reundSkuNum), new BigDecimal(skuNum),
                        alReturnNum, skuTotalAmount, alReturnAmount, saleorderGoods);

                AfterSalesGoods record = new AfterSalesGoods();
                record.setOrderDetailId(goodsId);
                record.setAfterSalesId(afterSalesId);
                record.setSkuRefundAmount(nowSkuReundAmount);
                record.setSkuOldRefundAmount(nowSkuReundAmount);
                // 更新当前SalesGoodsId退货金额
                afterSalesGoodsMapper.updateByGoodsIdAndAfterSalesId(record);

                // 累计
                hcReundAmount = hcReundAmount.add(nowSkuReundAmount);
            }

        }

        return hcReundAmount;
    }

    /**
     * <b>Description: 添加售后订单商品</b><br>
     *
     * @param afterSalesVo
     * @param afterSalesId [售后订单ID]
     * @param skuNumMap
     * @param operaterType [操作类型] 1 -- 新增/ 2 编辑
     * @return <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年12月18日 下午3:26:06 </b>
     * @throws RunTimeServiceException
     */
    private boolean addAfterSaleForGoods(AfterSalesVo afterSalesVo, Integer afterSalesId, Map<Integer, Integer> skuNumMap, int operaterType) throws RunTimeServiceException {
        // 页面参数
        String[] afterSalesNums = afterSalesVo.getAfterSalesNum();
        // 特殊商品ID
        Integer thGoodsId = afterSalesVo.getThGoodsId();
        // 售后类型
        Integer type = afterSalesVo.getType();
        // 公司ID
        Integer companyId = afterSalesVo.getCompanyId();
        // 订单ID
        Integer orderId = afterSalesVo.getOrderId();
        //添加售后产品表信息----开始
        if (afterSalesNums != null) {
            List<Integer> orderGoodsIdList = new ArrayList<>();//退货产品的id
            List<Integer> thNumList = new ArrayList<>();//退货产品的数量
            List<AfterSalesGoods> afterSalesGoodsList = new ArrayList<>();//退货产品集合
            AfterSalesGoods afterSalesGoods = null;

            // 查询售后订单商品单价与小计
            List<AfterSalesGoods> orderGoodsPriceList = null;
            // 非第三方售后（第三方售后无对应订单）
            if (afterSalesVo.getSubjectType() != null && !SysOptionConstant.ID_537.equals(afterSalesVo.getSubjectType())) {
                orderGoodsPriceList = afterSalesMapper.getOrderGoodsPriceByAfterId(afterSalesVo.getSubjectType(), afterSalesId);
            }
            // 计算退款金额
//			BigDecimal refundAmount = new BigDecimal(0);
            for (int i = 0; i < afterSalesNums.length; i++) {
                String afterSalesNum = afterSalesNums[i];
                logger.info("新增售后订单，售后产品信息:{}", afterSalesNum);
                if (ObjectUtils.notEmpty(afterSalesNum)) {
                    Integer saleorderGoodsId = Integer.valueOf(afterSalesNum.split("\\|")[0]);//此处的saleorderGoodsId有可能是buyorderGoodsId
                    afterSalesGoods = new AfterSalesGoods();

                    Integer num = 0;
                    if (!afterSalesNum.split("\\|")[1].equals("undefined")) {
                        // 退货数量
                        num = Integer.valueOf(afterSalesNum.split("\\|")[1]);
                        afterSalesGoods.setNum(num);
                        thNumList.add(num);
                    }
                    afterSalesGoods.setAfterSalesId(afterSalesId);
                    afterSalesGoods.setOrderDetailId(saleorderGoodsId);
                    orderGoodsIdList.add(saleorderGoodsId);

                    if (!afterSalesNum.split("\\|")[2].equals("undefined")) {
                        Integer deliveryDirect = Integer.valueOf(afterSalesNum.split("\\|")[2]);
                        afterSalesGoods.setDeliveryDirect(deliveryDirect);
                    }

                    // 商品ID
                    Integer goodsId = null;

                    if (afterSalesNum.split("\\|").length >= 4) {

                        goodsId = Integer.valueOf(afterSalesNum.split("\\|")[3]);

                        if (afterSalesVo.getSubjectType() != null && afterSalesVo.getSubjectType().equals(535)) {
                            //是否是活动商品
                            Integer isActionGoods = Integer.valueOf(afterSalesNum.split("\\|")[4]);
                            //是否是活动商品
                            afterSalesGoods.setIsActionGoods(isActionGoods);
                        }

                    } else {
                        goodsId = Integer.valueOf(afterSalesNum.split("\\|")[0]);
                    }

                    // add by franlin.wu for [统计每个saleorderGoodsId商品退货数量] at 2018-12-18 begin
                    if (null == skuNumMap.get(saleorderGoodsId)) {
                        skuNumMap.put(saleorderGoodsId, num);
                    } else {
                        // 累加
                        skuNumMap.put(saleorderGoodsId, num + skuNumMap.get(saleorderGoodsId));
                    }
                    // add by franlin.wu for [统计每个sku商品退货数量] at 2018-12-18 end

                    // 计算退货金额
                    if (EmptyUtils.isNotEmpty(orderGoodsPriceList)) {
                        for (int x = 0; x < orderGoodsPriceList.size(); x++) {
                            if (saleorderGoodsId != null && saleorderGoodsId.equals(orderGoodsPriceList.get(x).getGoodsId())) {
                                afterSalesGoods.setPrice(orderGoodsPriceList.get(x).getPrice());
                                afterSalesGoods.setSkuRefundAmount(orderGoodsPriceList.get(x).getPrice().multiply(new BigDecimal(num)));
                                afterSalesGoods.setSkuOldRefundAmount(orderGoodsPriceList.get(x).getPrice().multiply(new BigDecimal(num)));
                                break;// 结束for循环
                            }
                        }
                    }
//					refundAmount.add(afterSalesGoods.getSkuRefundAmount());
                    afterSalesGoods.setGoodsId(goodsId);
                    afterSalesGoodsList.add(afterSalesGoods);
                    Integer res2 = afterSalesGoodsMapper.insertSelective(afterSalesGoods);
                    if (ErpConst.ZERO.equals(res2)) {
                        throw new RunTimeServiceException(CommonConstants.ONE, "没有插入，抛出事务回滚");
                    }
                }
            }
            // 退款金额统一使用SKU_REFUND_AMOUNT累计 2019-02-27
//			afterSalesVo.setRefundAmount(refundAmount);
            //退货产生的退票
            if (SysOptionConstant.ID_539.equals(type) || SysOptionConstant.ID_546.equals(type)) {
                // 编辑时 删除
                if (2 == operaterType) {
                    //先删除售后关联的InvoiceId
                    afterSalesInvoiceMapper.delAfterSalesInvoiceByAfterSalesId(afterSalesId);
                }

                List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = null;
                AfterSalesInvoiceVo afterSalesInvoiceVo = new AfterSalesInvoiceVo();
                afterSalesInvoiceVo.setCompanyId(companyId);
                afterSalesInvoiceVo.setRelatedId(orderId);

                if (SysOptionConstant.ID_539.equals(type)) {
                    //查询是否有电子票，如果有就全退，且不存在纸质票
                    afterSalesInvoiceVo.setInvoiceProperty(ErpConst.TWO);
                    afterSalesInvoiceVo.setType(SysOptionConstant.ID_505);
                    afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
                    if (afterSalesInvoiceVoList == null || afterSalesInvoiceVoList.size() == 0) {
                        List<Integer> typeList = new ArrayList<>();
                        typeList.add(SysOptionConstant.ID_504);
                        typeList.add(SysOptionConstant.ID_505);
                        afterSalesInvoiceVo.setTypeList(typeList);
                        afterSalesInvoiceVo.setOrderGoodsIdList(orderGoodsIdList);
                        afterSalesInvoiceVo.setInvoiceProperty(null);
                        afterSalesInvoiceVo.setType(null);
                        afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
                    }
                } else {
                    List<Integer> typeList = new ArrayList<>();
                    typeList.add(SysOptionConstant.ID_503);
                    typeList.add(SysOptionConstant.ID_504);
                    afterSalesInvoiceVo.setTypeList(typeList);
                    afterSalesInvoiceVo.setOrderGoodsIdList(orderGoodsIdList);
                    afterSalesInvoiceVo.setType(null);
                    afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
                }
                if (CollectionUtils.isNotEmpty(afterSalesInvoiceVoList) && afterSalesInvoiceVoList.size() > 0) {
                    //过滤红字有效的是否已关联蓝子有效,或者蓝字有效关联了红字有效
                    for (AfterSalesInvoiceVo asi : afterSalesInvoiceVoList) {
                        List<RInvoiceJInvoice> rInvoiceJInvoiceList = rInvoiceJInvoiceMapper.getRInvoiceJInvoiceList(asi.getInvoiceId());
                        if (CollectionUtils.isNotEmpty(rInvoiceJInvoiceList) && rInvoiceJInvoiceList.size() > 0) {
                            BigDecimal amount = rInvoiceJInvoiceMapper.getAfterInvoiceTotalAmount(asi.getInvoiceId());
                            if (amount.compareTo(BigDecimal.ZERO) > 0) {
                                AfterSalesInvoice as = new AfterSalesInvoice();
                                as.setAfterSalesId(afterSalesId);
                                as.setInvoiceId(asi.getInvoiceId());
                                as.setIsRefundInvoice(ErpConst.ONE);
                                int re = afterSalesInvoiceMapper.insertSelective(as);
                                if (re == 0) {
//									return 0;
                                    return false;
                                }
                            }
                        } else {
                            AfterSalesInvoice as = new AfterSalesInvoice();
                            as.setAfterSalesId(afterSalesId);
                            as.setInvoiceId(asi.getInvoiceId());
                            as.setIsRefundInvoice(ErpConst.ONE);
                            int re = afterSalesInvoiceMapper.insertSelective(as);
                            if (re == 0) {
//								return 0;
                                return false;
                            }
                        }


                    }
                }

                //此处需要比对销售/采购产品的总金额与退货产品总金额、开票产品总金额的差值  ---2018-05-16
//				Set<Integer> invoiceIdSet = getInvoiceIds(afterSalesVo, orderGoodsIdList, afterSalesGoodsList);
//				if(invoiceIdSet != null && invoiceIdSet.size() > 0){
//					for (Integer invoiceId : invoiceIdSet) {
//						AfterSalesInvoice as = new AfterSalesInvoice();
//						as.setAfterSalesId(afterSales.getAfterSalesId());
//						as.setInvoiceId(invoiceId);
//						as.setIsRefundInvoice(ErpConst.ONE);
//						int re = afterSalesInvoiceMapper.insertSelective(as);
//						if(re == 0){
//							return 0;
//						}
//					}
//				}
            }

        }

        //退换货,安调，维修手续费
        if (SysOptionConstant.ID_539.equals(type) || SysOptionConstant.ID_540.equals(type) || SysOptionConstant.ID_546.equals(type) || SysOptionConstant.ID_547.equals(type)
                || AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(type) || AfterSalesProcessEnum.AFTERSALES_WX.getCode().equals(type) || AfterSalesProcessEnum.AFTERASALES_THIRD_AT.getCode().equals(type) 
                || AfterSalesProcessEnum.AFTERASALES_THIRD_WX.getCode().equals(type) || AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(type) || AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(type)) {
            AfterSalesGoods asg = new AfterSalesGoods();
            asg.setAfterSalesId(afterSalesId);
            asg.setGoodsType(ErpConst.ONE);
            asg.setNum(ErpConst.ONE);
            asg.setGoodsId(thGoodsId);//特殊商品的id
            // add by franlin.wu for [编辑,逻辑保存不变] at 2018-12-18 begin
            if (2 == operaterType) {
                asg.setPrice(afterSalesVo.getServiceAmount());
            }
            // add by franlin.wu for [编辑,逻辑保存不变] at 2018-12-18 end
            int res2 = afterSalesGoodsMapper.insertSelective(asg);
            if (res2 == 0) {
                return false;
            }
        }
        //添加售后产品表信息----结束

        return true;
    }

    /**
     * <b>Description: 查询特殊商品id的list</b><br>
     *
     * @return <b>Author: Franlin.wu</b>
     * <br><b>Date: 2018年12月20日 上午9:53:25 </b>
     */
    private List<Integer> getSpecialGoodsId() {
        List<Integer> list = new LinkedList<>();
        try {
            // 根据id查询特殊商品
            List<SysOptionDefinition> sysOptList = getSysOptionDefinitionList(SysOptionConstant.SPECIAL_SKU);

            if (CollectionUtils.isNotEmpty(sysOptList)) {
                for (SysOptionDefinition sysOpt : sysOptList) {
                    if (null == sysOpt || StringUtil.isEmpty(sysOpt.getComments())) {
                        continue;
                    }

                    list.add(Integer.parseInt(sysOpt.getComments().trim()));
                }
            }
        } catch (Exception e) {
            logger.error("根据id查询特殊商品发生异常", e);
        }

        return list;
    }

    /**
     * <b>Description:</b><br>
     * 获取字典表中的集合
     *
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年7月27日 下午4:32:06
     */
    @Override
    @SuppressWarnings("unchecked")
    public List<SysOptionDefinition> getSysOptionDefinitionList(Integer parentId) {
        // modify by franlin.wu
        // for[针对只判断redis缓存的key是否存在来获取数字字典值,可能存在从redis缓存获取为null的场景,做以下修改] at
        // 2018-11-23 begin
        // 数字字典list
        List<SysOptionDefinition> resultList = null;
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId)) {
            String jsonStr = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_DATA_DICTIONARY_LIST + parentId);
            // 避免json为空或null的字符串或[null]的字符串
            if (StringUtils.isNotBlank(jsonStr) && !"null".equalsIgnoreCase(jsonStr)
                    && !"[null]".equalsIgnoreCase(jsonStr)) {
                JSONArray json = JSONArray.fromObject(jsonStr);
                resultList = (List<SysOptionDefinition>) JSONArray.toCollection(json, SysOptionDefinition.class);
            }
        }
        // 从redis中获取为null，则从库中查询
        if (CollectionUtils.isEmpty(resultList)) {
            // 调用根据parendId获取数字字典子list
            resultList = baseService.getSysOptionDefinitionListByParentId(parentId);
        }

        //去重
        if (CollectionUtils.isNotEmpty(resultList)) {
            resultList.stream().filter(Objects::nonNull).map(SysOptionDefinition::getSysOptionDefinitionId).distinct().collect(Collectors.toList());
        }
        return resultList;
        // modify by franlin.wu
        // for[针对只判断redis缓存的key是否存在来获取数字字典值,可能存在从redis缓存获取为null的场景,做以下修改] at
        // 2018-11-23 end

    }

    /**
     * <b>Description:</b><br> 查询售后服务费信息
     *
     * @param afterSalesDetail
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2018年4月16日 上午11:04:41
     */
    @Override
    public AfterSalesDetailVo getAfterSalesDetailVoByParam(AfterSalesDetail afterSalesDetail) {
        try {

            AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(afterSalesDetail.getAfterSalesId());
            AfterSalesDetailVo afterSalesDetailVo = afterSalesDetailMapper.getAfterSalesDetailVoById(afterSalesDetail.getAfterSalesDetailId());
            //非采购类型
            if (afterSales != null && !SysOptionConstant.ID_536.equals(afterSales.getSubjectType())) {
                TraderContact tc = new TraderContact();
                TraderAddress ta = new TraderAddress();
                Integer traderId = 0;
                if (ObjectUtils.isEmpty(afterSalesDetailVo.getInvoiceTraderId())) {
                    tc.setTraderId(afterSalesDetailVo.getTraderId());
                    ta.setTraderId(afterSalesDetailVo.getTraderId());
                    traderId = afterSalesDetailVo.getTraderId();

                } else if (ObjectUtils.notEmpty(afterSalesDetailVo.getInvoiceTraderId())) {
                    tc.setTraderId(afterSalesDetailVo.getInvoiceTraderId());
                    ta.setTraderId(afterSalesDetailVo.getInvoiceTraderId());
                    traderId = afterSalesDetailVo.getInvoiceTraderId();
                }
                tc.setTraderType(ErpConst.ONE);
                tc.setIsEnable(ErpConst.ONE);
                //获取交易者联系人
                List<TraderContact> tcList = traderContactMapper.getTraderContact(tc);
                afterSalesDetailVo.setTcList(tcList);

                ta.setTraderType(ErpConst.ONE);
                ta.setIsEnable(ErpConst.ONE);
                //获取客户的联系地址
                List<TraderAddress> taList = traderAddressMapper.getTraderAddressListByModel(ta);
                afterSalesDetailVo.setTaList(taList);
                if (traderId != 0) {
                    Trader trader = traderMapper.selectByPrimaryKey(traderId);
                    afterSalesDetailVo.setTraderName(trader.getTraderName());
                }
            }

            //刷新收票地址列表
            if (afterSalesDetailVo != null) {
                List<TraderAddress> taList = afterSalesDetailVo.getTaList();
                if (CollectionUtils.isNotEmpty(taList)) {
                    List<TraderAddressVo> list = new ArrayList<>();
                    TraderAddressVo tav = null;
                    for (TraderAddress ta : taList) {
                        tav = new TraderAddressVo();
                        tav.setTraderAddress(ta);
                        tav.setArea(getAddressByAreaId(ta.getAreaId()));
                        list.add(tav);
                    }
                    afterSalesDetailVo.setTavList(list);
                }
            }

            return afterSalesDetailVo;
        } catch (Exception e) {
            //logger.error("getAfterSalesDetailVoByParam error:{}",e);
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    @Override
    public ResultInfo<?> payApplyPass(PayApply payApply) {
        ResultInfo<?> result = new ResultInfo<>();
        PayApply payApplyInfo = payApplyMapper.selectByPrimaryKey(payApply.getPayApplyId());
        Buyorder buyorderInfo = buyorderMapper.selectByPrimaryKey(payApplyInfo.getRelatedId());
        if (buyorderInfo != null && ErpConst.THREE.equals(buyorderInfo.getStatus())) {
            return ResultInfo.error("操作失败，订单已被关闭");
        } else {
            try {
                int i = payApplyMapper.updateByPrimaryKeySelective(payApply);
                if (i == 1) {
                    return ResultInfo.success();
                }
            } catch (Exception e) {
                logger.error(Contant.ERROR_MSG, e);
            }
        }
        return result;
    }

    @Override
    public ResultInfo<?> saveRefundCapitalBillNew(CapitalBill capitalBill) {
        Integer afterSalesId = capitalBill.getCapitalBillDetail().getRelatedId();
        // 查询售后订单已退金额
        BigDecimal refundAmount = afterSalesDataService.getRefundTraderAmount(afterSalesId, ErpConst.ONE);// 1售后订单退款（退款给客户）
        if (refundAmount == null) {
            refundAmount = new BigDecimal(0);
        }
        // 根据售后单ID查询售后信息
        AfterSalesDetailVo afterDetail = invoiceAfterMapper.getAfterSalesDetail(afterSalesId);
        if (ErpConst.TWO.equals(afterDetail.getRefund().intValue())) {// 退给客户
            // 已退金额加上此次退款金额大于实际需要退款金额
            if (afterDetail.getRealRefundAmount().doubleValue() < refundAmount.add(capitalBill.getAmount())
                    .doubleValue()) {
                return ResultInfo.error("已付款金额加此次付款金额大于实际需支付金额");
            } else {
                // 保存资金流水主表信息记录
                int i = capitalBillMapper.insertSelective(capitalBill);
                if (i == 1) {
                    Integer capitalBillId = capitalBill.getCapitalBillId();
                    // 生成资金流水号
                    CapitalBill capitalBillExtra = new CapitalBill();
                    capitalBillExtra.setCapitalBillId(capitalBillId);
                    capitalBillExtra.setCapitalBillNo(orderNoDict.getOrderNum(capitalBillId, 10));
                    capitalBillMapper.updateByPrimaryKeySelective(capitalBillExtra);

                    // 添加CAPITAL_BILL_DETAIL
                    // CapitalBillDetail capitalBillDetail = new
                    // CapitalBillDetail();
                    // capitalBillDetail.setRelatedId(capitalBill.getRelatedId);
                    CapitalBillDetail cbd = capitalBill.getCapitalBillDetail();
                    if (null != cbd) {
                        cbd.setCapitalBillId(capitalBillId);
                        cbd.setAmount(capitalBill.getAmount());
                        capitalBillDetailMapper.insertSelective(cbd);
                    }

                    if (null != capitalBill.getBankBillId()) {
                        BankBill bankBillEdit = getBankBillEdit(capitalBill);
                        editBankBill(bankBillEdit);
                    }
                }
                // 判断是否全部退款
                Integer refundAmountStatus = 2;// 部分退款
                // 实际需要退款金额 等于 已退金额加上此次退款金额
                if (afterDetail.getRealRefundAmount().doubleValue() == refundAmount.add(capitalBill.getAmount())
                        .doubleValue()) {
                    refundAmountStatus = 3;// 已退款
                }
                AfterSalesDetail asd = new AfterSalesDetail();
                asd.setAfterSalesId(afterSalesId);
                asd.setRefundAmountStatus(refundAmountStatus);
                invoiceAfterMapper.updateAfterSalesDetail(asd);

                //查询售后类型
                AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
                if (afterSales != null && !SysOptionConstant.ID_539.equals(afterSales.getType()) && !SysOptionConstant.ID_543.equals(afterSales.getType())) {

                    //VDERP-2193 在财务制单时就扣减客户余额
                    if (capitalBill.getCapitalBillDetail().getBussinessType().intValue() == 531 // 退款
                            && capitalBill.getTraderType().intValue() == 2 // 支出
                            // && capitalBill.getTraderSubject().intValue() == 1
                            // //对公
                            && capitalBill.getCapitalBillDetail().getOrderType().intValue() == 3) {// 售后订单
                        // 修改客户余额（退款均从客户余额中出去）
                        TraderCustomer tc = new TraderCustomer();
                        tc.setTraderId(capitalBill.getCapitalBillDetail().getTraderId());
                        tc.setAmount(capitalBill.getAmount().multiply(new BigDecimal(-1)));
                        tc.setUpdater(capitalBill.getCreator());
                        tc.setModTime(capitalBill.getAddTime());
                        traderCustomerMapper.updateTraderCustomerAmount(tc);
                    }
                }

            }
        } else {

            return new ResultInfo(-1, "无需退还到客户", afterDetail);
        }
        return ResultInfo.success("修改成功");


    }

    @Override
    public AfterSalesVo getAfterSalesApplyPay(AfterSalesVo afterSalesVo) {
        if (SysOptionConstant.ID_536.equals(afterSalesVo.getTraderSubject())) {
            afterSalesVo = afterSalesMapper.viewAfterSalesDetailBuyorder(afterSalesVo);//采购的售后详情
        }
        if (SysOptionConstant.ID_535.equals(afterSalesVo.getTraderSubject()) || SysOptionConstant.ID_537.equals(afterSalesVo.getTraderSubject())) {
            //查询关联的工程师
            AfterSalesInstallstion afterSalesInstallstion = new AfterSalesInstallstion();
            afterSalesInstallstion.setAfterSalesId(afterSalesVo.getAfterSalesId());
            List<AfterSalesInstallstionVo> asivList = afterSalesInstallstionMapper.getAfterSalesInstallstionVoByParam(afterSalesInstallstion);
            //查询每个工程师的付款数量和金额
            PayApplyDetailVo payApplyDetailVo = null;
            for (AfterSalesInstallstionVo asiv : asivList) {
                payApplyDetailVo = new PayApplyDetailVo();
                payApplyDetailVo.setDetailgoodsId(asiv.getAfterSalesInstallstionId());
                payApplyDetailVo.setPayType(518);
                payApplyDetailVo.setRelatedId(asiv.getAfterSalesId());
                payApplyDetailVo = payApplyDetailMapper.getPayApplyDetailVo(payApplyDetailVo);
                asiv.setPayApplySum(payApplyDetailVo.getApplicationSum());
                asiv.setPayApplyTotalAmount(payApplyDetailVo.getTotalAmountSum());
            }
            afterSalesVo.setAfterSalesInstallstionVoList(asivList);
        }
        //查询售后的特殊商品
        AfterSalesGoods afterSalesGoods = new AfterSalesGoods();
        afterSalesGoods.setAfterSalesId(afterSalesVo.getAfterSalesId());
        AfterSalesGoodsVo afterSalesGoodsVo = afterSalesGoodsMapper.getSpecialGoods(afterSalesGoods);
        if (afterSalesGoodsVo != null) {
//			PayApplyDetailVo payApplyDetailVo = new PayApplyDetailVo();
//			payApplyDetailVo.setDetailgoodsId(afterSalesGoodsVo.getAfterSalesGoodsId());
//			payApplyDetailVo.setPayType(518);
//			payApplyDetailVo = payApplyDetailMapper.getPayApplyDetailVo(payApplyDetailVo);
//			if(payApplyDetailVo != null){
//				afterSalesGoodsVo.setPayApplySum(payApplyDetailVo.getApplicationSum());
//				afterSalesGoodsVo.setPayApplyTotalAmount(payApplyDetailVo.getTotalAmountSum());
//			}else{
//				afterSalesGoodsVo.setPayApplySum(new BigDecimal(0));
//				afterSalesGoodsVo.setPayApplyTotalAmount(new BigDecimal(0));
//			}
            List<AfterSalesGoodsVo> list = new ArrayList<>();
            list.add(afterSalesGoodsVo);
            afterSalesVo.setAfterSalesGoodsList(list);
        }
        return afterSalesVo;
    }

    @Override
    public ResultInfo<?> saveApplyPay(PayApplyVo payApplyVo, User user) {
        payApplyVo.setAddTime(DateUtil.sysTimeMillis());
        payApplyVo.setCreator(user.getUserId());
        payApplyVo.setModTime(DateUtil.sysTimeMillis());
        payApplyVo.setUpdater(user.getUserId());
        payApplyVo.setCompanyId(user.getCompanyId());
        payApplyVo.setPayType(SysOptionConstant.ID_518);//售后

        if (payApplyVo != null && payApplyVo.getIsUseBalance() == 1) {
            TraderFinance traderFinance = traderFinanceMapper.selectByPrimaryKey(payApplyVo.getTraderFinanceId());
            payApplyVo.setBank(traderFinance.getBank());
            payApplyVo.setBankAccount(traderFinance.getBankAccount());
            payApplyVo.setBankCode(traderFinance.getBankCode());
        }
        PayApply payApply = new PayApply(payApplyVo.getCompanyId(), payApplyVo.getPayType(), payApplyVo.getRelatedId(), payApplyVo.getTraderSubject(), payApplyVo.getTraderMode(), payApplyVo.getTraderName(),
                payApplyVo.getAmount(), payApplyVo.getBank(), payApplyVo.getBankAccount(), payApplyVo.getBankCode(), payApplyVo.getAddTime(),
                payApplyVo.getCreator(), payApplyVo.getModTime(), payApplyVo.getUpdater());
        payApply.setComments(payApplyVo.getComments());
        payApply.setBankRemark(payApplyVo.getBankRemark());
        int res = payApplyMapper.insertSelective(payApply);
        if (res == 0) {
            return ResultInfo.error();
        }
        // 更新往来单位类型
        payApplyApiService.updateAccountType(payApply.getPayApplyId());
        PayApplyDetail payApplyDetail = new PayApplyDetail(payApply.getPayApplyId(), payApplyVo.getDetailgoodsId(), payApplyVo.getPrice(),
                payApplyVo.getNum(), payApplyVo.getTotalAmount());
        res = payApplyDetailMapper.insertSelective(payApplyDetail);
        if (res == 0) {
            return ResultInfo.error();
        }
//		if(payApplyVo != null && payApplyVo.getIsUseBalance() == 1){
//			AfterSales as = new AfterSales();
//			as.setAfterSalesId(payApplyVo.getRelatedId());
//			AfterSalesVo afterSalesVo = afterSalesMapper.viewAfterSalesDetailBuyorder(as);
//			TraderSupplier tc = new TraderSupplier();
//			CapitalBill cb = new CapitalBill();
//			CapitalBillDetail cbd = new CapitalBillDetail();
//			tc.setTraderSupplierId(payApplyVo.getTraderSupplierId());
//			TraderSupplier traderSupplier = traderSupplierMapper.selectByPrimaryKey(payApplyVo.getTraderSupplierId());
//
//			//此处有可能产生供应商处余额小于退换货手续的状况--2018-1-30，暂不考虑
//				if(traderSupplier.getAmount().compareTo(payApplyVo.getAmount()) >=0){//判断当前供应商的余额足够支付退换货手续费
//
//				}
//				if(afterSalesVo.getServiceAmount().compareTo(traderSupplier.getAmount()) > 0 ){
//					cb.setAmount(new BigDecimal("-"+traderSupplier.getAmount().toString()));
//					tc.setAmount(new BigDecimal(0));
//				}else if(afterSalesVo.getServiceAmount().compareTo(traderSupplier.getAmount()) <= 0){
//					cb.setAmount(new BigDecimal("-"+afterSalesVo.getServiceAmount().toString()));
//					tc.setAmount(traderSupplier.getAmount().subtract(afterSalesVo.getServiceAmount()));
//				}
//
//			cb.setAmount(new BigDecimal("-"+afterSalesVo.getServiceAmount().toString()));
//			tc.setAmount(traderSupplier.getAmount().subtract(afterSalesVo.getServiceAmount()));
//
//			cb.setTraderMode(528);//余额支付
//			cb.setTraderType(5);//转出
//			cbd.setBussinessType(525);//订单付款
//			res = saveCaptailBill(afterSalesVo,cb,cbd,2);//支付退换货手续费---交易记录
//			if(res == 0){
//				return 0;
//			}
//			res = traderSupplierMapper.updateByPrimaryKeySelective(tc);//修改供应商的客户余额
//			if(res == 0){
//				return 0;
//			}
//		}

        return ResultInfo.success(payApply.getPayApplyId());
    }

    @Override
    public AfterSalesDetailVo getAfterCapitalBillInfo(AfterSalesDetailVo afterDetailVo) {
        AfterSalesDetailVo asdv = invoiceAfterMapper.getAfterCapitalBillInfo(afterDetailVo);
        //535销售   536采购
        if (SysOptionConstant.ID_535.equals(asdv.getSubjectType()) || SysOptionConstant.ID_537.equals(asdv.getSubjectType())) {
            //已归还给客户金额
            BigDecimal refundAmount = afterSalesDataService.getRefundTraderAmount(asdv.getAfterSalesId(), asdv.getSubjectType().intValue() == 535 ? 1 : 2);
            asdv.setPayAmount(refundAmount == null ? (new BigDecimal(0)) : refundAmount);

            //查询余额付款（给我们：即我们收款）总额
            CapitalBill cb = new CapitalBill();
            cb.setCompanyId(asdv.getCompanyId());
//			cb.setTraderType(ErpConst.THREE);//转移
            cb.setTraderSubject(ErpConst.ONE);//对公
//			cb.setTraderMode(528);//余额支付
            CapitalBillDetail cbd = new CapitalBillDetail();
            cbd.setBussinessType(SysOptionConstant.ID_526);//订单收款
            cbd.setOrderType(ErpConst.THREE);//订单类型 1销售订单 2采购订单 3售后订单
//			cbd.setOrderNo(asdv.getOrderNo());//订单单号
            cbd.setRelatedId(asdv.getAfterSalesId());//关联表ID
            cbd.setTraderId(asdv.getTraderId());//交易者ID
            if (SysOptionConstant.ID_535.equals(asdv.getSubjectType())) {//销售
                cbd.setTraderType(ErpConst.ONE);//所属类型 1::经销商（包含终端）2:供应商
            }/*else if(SysOptionConstant.ID_536.equals(asdv.getSubjectType())){//采购
                cbd.setTraderType(ErpConst.TWO);//所属类型 1::经销商（包含终端）2:供应商
            }*/
            cb.setCapitalBillDetail(cbd);
            BigDecimal capitalTotalAmount = invoiceAfterMapper.getCapitalBillTotalAmount(cb);
            asdv.setCapitalTotalAmount(capitalTotalAmount == null ? (new BigDecimal(0)) : capitalTotalAmount);
        } else if (SysOptionConstant.ID_536.equals(asdv.getSubjectType())) {
            //查询资金流水-已收到的退款总额
            BigDecimal refundAmount = afterSalesDataService.getRefundAmount(asdv.getAfterSalesId(), ErpConst.TWO);//2采购订单售后
            asdv.setCapitalTotalAmount(refundAmount == null ? (new BigDecimal(0)) : refundAmount);
        }
        return asdv;
    }

    @Override
    public List<PayApplyDetail> getPayApplyDetailList(Integer payApplyId) {
        PayApplyDetail payApplyDetail = new PayApplyDetail();
        payApplyDetail.setPayApplyId(payApplyId);
        return payApplyDetailMapper.getPayApplyDetailList(payApplyDetail);
    }

    private ResultInfo<?> editBankBill(BankBill bankBill) {
        if (null != bankBill) {
            try {
                int sNum = bankBillMapper.updateByPrimaryKeySelectiveNew(bankBill);
                return ResultInfo.success();
            } catch (Exception e) {
                logger.info("修改银行流水记录error :{}", e);
                return ResultInfo.error();
            }
        }
        return new ResultInfo<>();
    }

    private BankBill getBankBillEdit(CapitalBill capitalBill) {

        BankBill bankBillEdit = new BankBill();
        bankBillEdit.setBankBillId(capitalBill.getBankBillId());

        if (null == capitalBill.getBankBillId()) {
            bankBillEdit.setMatchedAmount(BigDecimal.ZERO.add(capitalBill.getAmount()));
            return bankBillEdit;
        }
        // 获取银行流水信息
        BankBillDo bankBill = bankBillMapper.selectByPrimaryKey(capitalBill.getBankBillId());

        bankBillEdit.setMatchedAmount(bankBill.getMatchedAmount().add(capitalBill.getAmount()));
        return bankBillEdit;
    }

    @Override
    public void addTask(List<Action> list) {
        list = new ArrayList<>();
        afterSalesMapper.addTask(list);
    }

    @Override
    public int addTask(String string2, String moduleName, String controllerName) {
        Action action = new Action();
        action.setModuleName(moduleName);
        action.setControllerName(controllerName);
        action.setActionName(string2);
        return actionMapper.insert(action);
    }

    @Override
    public Boolean getTask(String actionName, String moduleName, String controllerName) {
        List<Action> result = actionMapper.getTask(actionName, moduleName, controllerName);
        if (CollectionUtils.isNotEmpty(result) && result.size() > 0) {
            return true;
        }
        return false;
    }


    /**
     * 计算当前销售订单下每个商品id已退货金额
     *
     * @param orderId
     * @return
     */
    private Map<Integer, MapExtendsVo<Integer, BigDecimal>> subSkuReundAmountByOrderId(Integer orderId) {
        if (null != orderId) {
            AfterSalesVo afterSalesVo = new AfterSalesVo();
            afterSalesVo.setOrderId(orderId);
            // 审核通过
            afterSalesVo.setAtferSalesStatus(ErpConst.TWO);
            // 已完结
            afterSalesVo.setStatus(ErpConst.TWO);
            afterSalesVo.setType(539);
            // getAfterSalesVoListByOrderId
            List<AfterSalesVo> afterList = getAfterSalesVoListByOrderId(afterSalesVo);
            // 非空
            if (CollectionUtils.isNotEmpty(afterList)) {
                // 统计各sku的
                Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuMap = new HashMap<Integer, MapExtendsVo<Integer, BigDecimal>>();
                // 根据afterSalesId获取当前售后订单商品（不包含特殊商品）
                List<AfterSalesGoods> afterSalesGoodsList = afterSalesGoodsMapper.selectByAfterSalesId(afterList, 693);

                for (AfterSalesGoods asg : afterSalesGoodsList) {
                    if (null == asg) {
                        continue;
                    }
                    // 商品ID
                    Integer goodsId = asg.getOrderDetailId();
                    // 定义 当前sku的退款金额
                    BigDecimal alSkuReundAmount = BigDecimal.ZERO;
                    // 当前sku的退款金额 不是null则赋予
                    if (null != asg.getSkuRefundAmount()) {
                        alSkuReundAmount = asg.getSkuRefundAmount();
                    }
                    // 定义当前退货数量
                    Integer num = 0;
                    if (null != asg.getNum()) {
                        num = asg.getNum();
                    }
                    // Integer 退货数量 BigDecimal 退货金额
                    MapExtendsVo<Integer, BigDecimal> mapExVo = new MapExtendsVo<Integer, BigDecimal>();
                    // 根据goodsId区分
                    if (null == skuMap.get(goodsId)) {
                        // 退货数量
                        mapExVo.setExData(num);
                        // 退款金额
                        mapExVo.setValue(alSkuReundAmount);
                    } else {
                        // 退货数量
                        mapExVo.setExData(num + skuMap.get(goodsId).getExData());
                        // 退款金额
                        mapExVo.setValue(alSkuReundAmount.add(skuMap.get(goodsId).getValue()));
                    }

                    skuMap.put(goodsId, mapExVo);
                }
                return skuMap;
            }
        }
        return null;
    }


    /**
     * <b>Description:</b><br> 根据订单id查询对应的售后订单列表
     *
     * @param afterSalesVo
     * @return
     * @Note <b>Author:</b> east
     * <br><b>Date:</b> 2017年10月10日 上午9:12:45
     */
    @Override
    public List<AfterSalesVo> getAfterSalesVoListByOrderId(AfterSalesVo afterSalesVo) {

        try {
            List<AfterSalesVo> asvList = afterSalesMapper.getAfterSalesVoListByOrderId(afterSalesVo);

            if (CollectionUtils.isNotEmpty(asvList)) {
                asvList.stream().forEach(s -> {
                    if (s.getCreator() != 0) {
                        s.setCreatorName(getUserNameByUserId(s.getCreator()));
                    }
                });
            }
            return asvList;
        } catch (Exception e) {
            logger.error(Contant.ERROR_MSG, e);
            return null;
        }
    }

    /**
     * <b>Description:</b><br>
     * 查询用户名
     *
     * @param userId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年8月25日 上午10:59:41
     */
    public String getUserNameByUserId(Integer userId) {
        if (ObjectUtils.notEmpty(userId)) {
            User us = userMapper.selectByPrimaryKey(userId);
            if (us == null) {
                return null;
            } else {
                return us.getUsername();
            }
        } else {
            return null;
        }
    }


    /**
     * <b>Description:</b><br>
     * 获取当前地区
     *
     * @param areaId
     * @return
     * @Note <b>Author:</b> east <br>
     * <b>Date:</b> 2017年10月26日 下午2:05:46
     */
    private Region getRegionByAreaId(Integer areaId) {
        Region region = null;
        if (JedisUtils.exists(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + areaId)) {
            String json = JedisUtils.get(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + areaId);
            if (json == null || "".equals(json)) {
                return null;
            }
            JSONObject jsonObject = JSONObject.fromObject(json);
            region = (Region) JSONObject.toBean(jsonObject, Region.class);
        } else {
            region = regionMapper.getRegionById(areaId);
            if (region != null) {
                JedisUtils.set(dbType + ErpConst.KEY_PREFIX_REGION_OBJECT + areaId,
                        JsonUtils.convertObjectToJsonStr(region), ErpConst.ZERO);
            }
        }
        return region;
    }

    @Override
    @Transactional
    public int saveDirectStockInfo(AfterSalesDirectInfo afterSalesDirectInfo, User user) {
        //设置保存直发记录
        afterSalesDirectInfo.setGoodVaildTime(DateUtil.convertLong(afterSalesDirectInfo.getGoodVaildTimeStr(), "yyyy-MM-dd"));
        afterSalesDirectInfo.setGoodCreateTime(DateUtil.convertLong(afterSalesDirectInfo.getGoodCreateTimeStr(), "yyyy-MM-dd"));
        afterSalesDirectInfo.setCreator(user.getUserId());
        afterSalesDirectInfo.setAddTime(DateUtil.sysTimeMillis());
        afterSalesDirectInfo.setUpdater(user.getUserId());
        afterSalesDirectInfo.setModeTime(DateUtil.sysTimeMillis());
        afterSalesDirectInfo.setIsDelete(0);
        //更新售后商品表收发货状态及数量
        AfterSalesGoods afterSalesGoods = afterSalesGoodsMapper.selectByPrimaryKey(afterSalesDirectInfo.getAfterSalesGoodsId());
        if (ErpConst.ONE.equals(afterSalesDirectInfo.getType())) {
            //直发出库
            Integer alreadyNum = afterSalesGoods.getDeliveryNum();
            if ((alreadyNum + afterSalesDirectInfo.getNum()) == afterSalesGoods.getRknum()) {
                //已发货数量+本次录入直发出库数量=售后数量 -- 全部发货
                afterSalesGoods.setDeliveryStatus(ErpConst.TWO);
            } else if ((alreadyNum + afterSalesDirectInfo.getNum()) > afterSalesGoods.getRknum()) {
                logger.info("保存新增售后直发出库记录数量超出总数");
                return -1;
            } else {
                afterSalesGoods.setDeliveryStatus(ErpConst.ONE);
            }
            afterSalesGoods.setDeliveryTime(DateUtil.sysTimeMillis());
            afterSalesGoods.setDeliveryNum(alreadyNum + afterSalesDirectInfo.getNum());
            //新增金蝶快照销售售后换货出库记录
            warehouseGoodsOutService.saveAfterSalesDirectOutInLog(afterSalesGoods, afterSalesDirectInfo, user);
        } else if (ErpConst.TWO.equals(afterSalesDirectInfo.getType())) {
            //直发入库
            Integer alreadyNum = afterSalesGoods.getArrivalNum();
            if ((alreadyNum + afterSalesDirectInfo.getNum()) == afterSalesGoods.getRknum()) {
                //已收货数量+本次录入直发入库数量=售后数量 -- 全部收货
                afterSalesGoods.setArrivalStatus(ErpConst.TWO);
            } else if ((alreadyNum + afterSalesDirectInfo.getNum()) > afterSalesGoods.getRknum()) {
                logger.info("保存新增售后直发入库记录数量超出总数");
                return -1;
            } else {
                afterSalesGoods.setArrivalStatus(ErpConst.ONE);
            }
            afterSalesGoods.setArrivalTime(DateUtil.sysTimeMillis());
            afterSalesGoods.setArrivalNum(alreadyNum + afterSalesDirectInfo.getNum());
        }
        afterSalesGoodsMapper.updateByPrimaryKeySelective(afterSalesGoods);

        int i = afterSalesDirectInfoMapper.insertSelective(afterSalesDirectInfo);

        //【直发项目】直发退货商品销售售后与采购售后出库数据同步
        try {
            if (i != 0 && ErpConst.TWO.equals(afterSalesDirectInfo.getType())) {
                //校验是否满足更新条件
                AfterSalesGoodsVo afterSalesGoodsCheck = checkUpdateCondition(afterSalesDirectInfo.getAfterSalesId(), afterSalesGoods.getGoodsId());
                if (Objects.nonNull(afterSalesGoodsCheck)) {
                    //同步更新采购售后记录
                    AfterSaleBuyorderDirectOutLog afterSaleBuyorderDirectOutLog = new AfterSaleBuyorderDirectOutLog();
                    afterSaleBuyorderDirectOutLog.setAfterSalesId(afterSalesGoodsCheck.getAfterSalesId());
                    afterSaleBuyorderDirectOutLog.setAfterSalesGoodsId(afterSalesGoodsCheck.getAfterSalesGoodsId());
                    afterSaleBuyorderDirectOutLog.setShowName(afterSalesGoodsCheck.getGoodsName());
                    afterSaleBuyorderDirectOutLog.setGoodsId(afterSalesGoodsCheck.getGoodsId());
                    afterSaleBuyorderDirectOutLog.setModel(afterSalesGoodsCheck.getModel());
                    afterSaleBuyorderDirectOutLog.setSpec(afterSalesGoodsCheck.getSpec());
                    afterSaleBuyorderDirectOutLog.setSku(afterSalesGoodsCheck.getSku());
                    afterSaleBuyorderDirectOutLog.setRegistrationNumber(afterSalesGoodsCheck.getRegistrationNumber());
                    afterSaleBuyorderDirectOutLog.setFirstEngageId(afterSalesGoodsCheck.getFirstEngageId());
                    afterSaleBuyorderDirectOutLog.setBrandName(afterSalesGoodsCheck.getBrandName());
                    afterSaleBuyorderDirectOutLog.setUnitName(afterSalesGoodsCheck.getUnitName());
                    afterSaleBuyorderDirectOutLog.setNum(afterSalesDirectInfo.getNum());
                    afterSaleBuyorderDirectOutLog.setVedengBatchNum(afterSalesDirectInfo.getFactoryCode());
                    afterSaleBuyorderDirectOutLog.setProduceTime(afterSalesDirectInfo.getGoodCreateTime());
                    afterSaleBuyorderDirectOutLog.setValidTime(afterSalesDirectInfo.getGoodVaildTime());
                    afterSaleBuyorderDirectOutLog.setCreator(afterSalesDirectInfo.getCreator());
                    afterSaleBuyorderDirectOutLog.setAddTime(afterSalesDirectInfo.getAddTime());
                    afterSaleBuyorderDirectOutLog.setUpdateTime(afterSalesDirectInfo.getModeTime());
                    afterSaleBuyorderDirectOutLogMapper.insertSelective(afterSaleBuyorderDirectOutLog);
                    //添加出库单
                    List<AfterSaleBuyorderDirectOutLog> afterSaleBuyorderDirectOutLogList = new ArrayList<>();
                    afterSaleBuyorderDirectOutLogList.add(afterSaleBuyorderDirectOutLog);
                    warehouseGoodsOutService.saveDirectOutInLog(user, afterSaleBuyorderDirectOutLogList);
                }
            }
        } catch (Exception e) {
            logger.info("直发退货商品销售售后与采购售后出库数据同步失败，售后单ID{}", afterSalesDirectInfo.getAfterSalesId(), e);
        }
        try {
            //0默认1出库2入库  销售售后退货、换货入库
            if (ErpConst.TWO.equals(afterSalesDirectInfo.getType())) {
                //获取销售售后单类型：539退货  540换货
                List<Integer> arrays = Arrays.asList(afterSalesDirectInfo.getAfterSalesId());
                List<AfterSales> afterSalesList = afterSalesMapper.getAfterSalesByAfterSalesIdList(arrays);
                if (CollectionUtils.isNotEmpty(afterSalesList)) {
                    AfterSales afterSales = afterSalesList.get(0);
                    //539退货  540换货
                    Integer orderType = afterSales.getType();
                    if (orderType == 539) {
                        logger.info("销售售后退货入库,操作用户：{}，入库数据：{}，销售售后退货单主键：{}", user, afterSalesDirectInfo, afterSalesDirectInfo.getAfterSalesId());
                        warehouseGoodsInService.inserSalesAfterThOrHh(user, afterSalesDirectInfo.getAfterSalesId(), afterSalesDirectInfo, WarehouseGoodsInEnum.ORDER_WAREHOUSE_BACK_IN);
                    } else if (orderType == 540) {
                        logger.info("销售售后换货入库,操作用户：{}，入库数据：{}，销售售后换货单主键：{}", user, afterSalesDirectInfo, afterSalesDirectInfo.getAfterSalesId());
                        warehouseGoodsInService.inserSalesAfterThOrHh(user, afterSalesDirectInfo.getAfterSalesId(), afterSalesDirectInfo, WarehouseGoodsInEnum.ORDER_WAREHOUSE_CHANGE_IN);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("未自测，避免报错影响其他功能，暂时catch，待自测后放开", e);
        }
        return i;
    }


    private AfterSalesGoodsVo checkUpdateCondition(Integer afterSalesId, Integer goodsId) {
        List<AfterSalesGoodsVo> afterSalesGoodsList = afterSalesGoodsMapper.getAfterSalesByDeliveryDirectAfterId(afterSalesId, goodsId);
        if (CollectionUtils.isNotEmpty(afterSalesGoodsList) && ErpConst.ONE.equals(afterSalesGoodsList.size())) {
            AfterSalesGoodsVo afterSalesGoodsVo = afterSalesGoodsList.get(0);
            List<AfterSaleBuyorderDirectOutLog> buyOrderAfterSaleLog = afterSaleBuyorderDirectOutLogMapper.getBuyOrderAfterSaleLog(afterSalesGoodsVo);
            if (CollectionUtils.isEmpty(buyOrderAfterSaleLog)) {
                return afterSalesGoodsVo;
            }
        }
        return null;
    }


    @Override
    public  List<AfterSalesInstallServiceRecordDetailDto> getAfterSalesInstallGoodsInfoAdd(Integer aftersalesId) {
        List<SaleorderGoods> saleorderGoodsList = afterSalesGoodsMapper.getRelatedSaleOrderGoods(aftersalesId);
        List<AfterSalesGoods> afterSalesGoodsList = afterSalesGoodsMapper.getAfterSalesGoodsByAfterSalesId(aftersalesId);
        List<AfterSalesInstallServiceRecordDetailDto> detailDtoList = new ArrayList<>();
        //获取安调售后单中的商品信息--并拆分为单个
        if (CollectionUtils.isNotEmpty(afterSalesGoodsList)) {
            afterSalesGoodsList = afterSalesGoodsList.stream().filter(item -> ErpConst.ZERO.equals(item.getGoodsType())).collect(Collectors.toList());
            logger.info("需要新增维护的安调售后的商品信息{},", JSON.toJSONString(afterSalesGoodsList));
            for (AfterSalesGoods afterSalesGoods : afterSalesGoodsList) {

                // 查询每一个产品的出库 以及 签收 时间
                Long deliveryTime = getDeliveryTime(afterSalesGoods);
                Long arriveTime = getArriveTime(afterSalesGoods);

                //剔除已维护的信息
                List<AfterSalesInstallServiceRecordDetail> afterSalesInstallServiceRecordDetails = afterSalesInstallServiceRecordDetailMapper.queryInfoByAftersalesGoodsId(afterSalesGoods.getAfterSalesGoodsId());
                int alreadyNum = 0;
                List<String> serialNumberList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(afterSalesInstallServiceRecordDetails)) {
                    alreadyNum = afterSalesInstallServiceRecordDetails.size();
                    serialNumberList = afterSalesInstallServiceRecordDetails.stream().map(AfterSalesInstallServiceRecordDetail::getSerialNumber)
                            .filter(StringUtils::isNotBlank).collect(Collectors.toList());
                }
                for (int i = 0; i < afterSalesGoods.getNum() - alreadyNum; i++) {
                    AfterSalesInstallServiceRecordDetailDto dto = new AfterSalesInstallServiceRecordDetailDto();
                    for (SaleorderGoods saleorderGoods : saleorderGoodsList) {
                        //获取销售单的商品快照信息
                        if (saleorderGoods.getSaleorderGoodsId().equals(afterSalesGoods.getOrderDetailId())) {
                            if (saleorderGoods.getArrivalStatus().equals(ErpConstant.ZERO)) {
                                continue;
                            }
                            dto.setSku(saleorderGoods.getSku());
                            dto.setSkuName(saleorderGoods.getGoodsName());
                            dto.setBrand(saleorderGoods.getBrandName());
                            dto.setModel(saleorderGoods.getModel());
                            dto.setNum(ErpConst.ONE);
                            dto.setAfterSalesGoodsId(afterSalesGoods.getAfterSalesGoodsId());
                            dto.setDeliveryTime(deliveryTime);
                            dto.setArrivalTime(arriveTime);

                            // 所有出库单
                            List<WarehouseGoodsOutInItem> itemList = new ArrayList<>();
                            // 根据销售单获取换货售后单出库单
                            List<WarehouseGoodsOutInItem> changeItemList = warehouseGoodsOutInMapper.getWarehouseGoodsOutInChangeBySaleOrderGoodsId(saleorderGoods.getSaleorderGoodsId());
                            // 根据销售单获取销售出库单
                            List<WarehouseGoodsOutInItem> outItemList = warehouseGoodsOutInMapper.getWarehouseGoodsOutInBySaleOrderGoodsId(saleorderGoods.getSaleorderGoodsId());
                            itemList.addAll(changeItemList);
                            itemList.addAll(outItemList);
                            logger.info("获取销售单出库单和换货出库单信息:{}",JSON.toJSONString(itemList));

                            // 获取不为空的sn码
                            List<String> barcodeFactoryList = itemList.stream().map(WarehouseGoodsOutInItem::getBarcodeFactory).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
                            logger.info("销售单id:{},销售单商品id:{},普发发货查询sn码:{},", saleorderGoods.getSaleorderId(),
                                    saleorderGoods.getSaleorderGoodsId(), JSON.toJSONString(barcodeFactoryList));

                            // 将barcodeFactoryList中的数据排除掉在serialNumberList中的数据
                            if (CollUtil.isNotEmpty(barcodeFactoryList) && CollUtil.isNotEmpty(serialNumberList)) {
                                barcodeFactoryList.removeAll(serialNumberList);
                            }

                            //普发 有出库单 且 出库单没有任何SN空值
                            if (ErpConstant.ZERO.equals(saleorderGoods.getDeliveryDirect())
                                    && CollUtil.isNotEmpty(itemList)
                                    && itemList.size() == barcodeFactoryList.size()) {
                                dto.setSnSelectType(SnSelectType.SELECT);
                            }

                            //普发 有出库单 且 出库单至少有一个SN空值
                            if (ErpConstant.ZERO.equals(saleorderGoods.getDeliveryDirect())
                                    && CollUtil.isNotEmpty(itemList)
                                    && itemList.size() > barcodeFactoryList.size()) {
                                dto.setSnSelectType(SnSelectType.CUSTOM);
                            }

                            // 直发
                            if (ErpConstant.ONE.equals(saleorderGoods.getDeliveryDirect())) {
                                dto.setSnSelectType(SnSelectType.DORPSHIP);
                            }

                            // 普发 且无出库单
                            if (ErpConstant.ZERO.equals(saleorderGoods.getDeliveryDirect()) && CollUtil.isEmpty(itemList)) {
                                dto.setSnSelectType(SnSelectType.HISTORY);
                            }
                            dto.setSnList(barcodeFactoryList);
                            detailDtoList.add(dto);
                            break;
                        }
                    }
                }
            }
        }
        return detailDtoList;
    }

    public static final class SnSelectType {
        //下拉选择
        public static final int SELECT = 0;
        //自定义补充码
        public static final int CUSTOM = -1;
        //直发手填补充码
        public static final int DORPSHIP = -2;
        //历史出库手填补充码
        public static final int HISTORY = -3;
    }


    /**
     * 获取安调产品原来的发货时间
     *
     * @param afterSalesGoods 当前安调对象
     * @return long 发货时间
     */
    private Long getDeliveryTime(AfterSalesGoods afterSalesGoods) {

        // 1.在提交安调服务记录时校验当前选择SKU中对应的最小出库时间A和最小签收时间B
        //最小出库时间A:
        //1).普发SKU取最小出库时间
        //2).直发SKU取订单商品表取发货时间
        SaleorderGoods relatedSaleOrderGoodsByGoodsId = afterSalesGoodsMapper.getRelatedSaleOrderGoodsByGoodsId(afterSalesGoods);
        if (relatedSaleOrderGoodsByGoodsId == null) {
            return null;
        }
        if (relatedSaleOrderGoodsByGoodsId.getDeliveryDirect() != null && relatedSaleOrderGoodsByGoodsId.getDeliveryDirect() == 1) {
            return relatedSaleOrderGoodsByGoodsId.getDeliveryTime();
        }
        return afterSalesGoodsMapper.getOutTimeByWarehouseGoodsOperateLog(afterSalesGoods.getOrderDetailId());
    }

    /**
     * 获取安调产品原来的发货时间
     *
     * @param afterSalesGoods 当前安调对象
     * @return long 发货时间
     */
    private Long getArriveTime(AfterSalesGoods afterSalesGoods) {

        //最小签收时间B:
        //1).普发SKU关联销售快递单取最小签收时间
        //2).直发SKU关联对应采购快递单取最小签收时间
        SaleorderGoods relatedSaleOrderGoodsByGoodsId = afterSalesGoodsMapper.getRelatedSaleOrderGoodsByGoodsId(afterSalesGoods);
        if (relatedSaleOrderGoodsByGoodsId == null) {
            return null;
        }
        if (relatedSaleOrderGoodsByGoodsId.getDeliveryDirect() != null && relatedSaleOrderGoodsByGoodsId.getDeliveryDirect() == 1) {
            return afterSalesGoodsMapper.getArriveTimeByBuyOrderExpressTime(relatedSaleOrderGoodsByGoodsId.getSaleorderGoodsId());
        }
        return afterSalesGoodsMapper.getArriveTimeBySaleOrderExpressTime(relatedSaleOrderGoodsByGoodsId.getSaleorderGoodsId());
    }


    @Override
    @Transactional
    public void saveServiceRecord(AfterSalesInstallServiceRecordDto dto) {
        // 需要根据sn码和售后单详情id确定出库单详情id
        AfterSalesInstallServiceRecord record = afterSalesInstallServiceRecordConvertor.toEntity(dto);
        record.setIsDelete(ErpConst.ZERO);
        afterSalesInstallServiceRecordMapper.insertSelective(record);

        Integer afterSalesServiceId = record.getAfterSalesServiceId();
        dto.getDetail().forEach(item -> {
            AfterSalesInstallServiceRecordDetail detail = afterSalesInstallServiceRecordDetailConvertor.toEntity(item);
            detail.setAfterSalesServiceId(afterSalesServiceId);
            afterSalesInstallServiceRecordDetailMapper.insertSelective(detail);
        });

        //保存上传附件
        if (!"".equals(dto.getFileNames()) && !"".equals(dto.getFileUris())) {
            ResultInfo resultInfo = saveServiceRecordAttachment(dto.getFileNames(), dto.getFileUris(), afterSalesServiceId);
            if (!resultInfo.getCode().equals(0)) {
                throw new ServiceException("附件上传异常");
            }
        }
    }

    /**
     * @param fileNames
     * @param fileUris
     * @param afterSalesServiceId
     * @return
     * <AUTHOR>
     * @desc 保存附件
     */
    private ResultInfo saveServiceRecordAttachment(String fileNames, String fileUris, Integer afterSalesServiceId) {
        CurrentUser curUser = CurrentUser.getCurrentUser();
        ResultInfo resultInfo = new ResultInfo(0, "操作成功");
        String[] fileNameArray = fileNames.split(",");
        String[] fileUriArray = fileUris.split(",");
        if (fileNameArray.length != fileUriArray.length) {
            resultInfo = new ResultInfo(-1, "上传附件有误，请重试上传");
        } else {
            List<Attachment> attachmentList = new ArrayList<>();
            for (int i = 0; i < fileNameArray.length; i++) {
                if (!"".equals(fileNameArray[i])) {
                    Attachment attachment = new Attachment();
                    attachment.setAttachmentFunction(SysOptionConstant.ID_4092);
                    attachment.setAttachmentType(SysOptionConstant.ID_460);
                    attachment.setRelatedId(afterSalesServiceId);
                    attachment.setDomain(ossUrl);
                    attachment.setAddTime(DateUtil.sysTimeMillis());
                    attachment.setCreator(curUser.getId());
                    attachment.setName(fileNameArray[i]);
                    attachment.setUri(fileUriArray[i]);
                    attachmentList.add(attachment);
                }
            }
            attachmentMapper.batchInsertSelective(attachmentList);
        }
        return resultInfo;
    }

    @Override
    public void getAfterSalesInstallInfoEdit(Integer afterSalesServiceId, ModelAndView mv) {
        AfterSalesInstallServiceRecord afterSalesInstallServiceRecord = afterSalesInstallServiceRecordMapper.selectByPrimaryKey(afterSalesServiceId);
        mv.addObject("afterSalesInstallServiceRecord", afterSalesInstallServiceRecord);
        List<AfterSalesInstallServiceRecordDetail> afterSalesInstallServiceRecordDetailList = afterSalesInstallServiceRecordDetailMapper.queryInfoByServiceId(afterSalesServiceId);
        List<AfterSalesInstallServiceRecordDetailDto> detailDtoList = afterSalesInstallServiceRecordDetailConvertor.toDto(afterSalesInstallServiceRecordDetailList);
        mv.addObject("detailDtoList", detailDtoList);
        //查询出库记录中的SN码
        for (AfterSalesInstallServiceRecordDetailDto dto : detailDtoList) {
            AfterSalesGoods afterSalesGoods = afterSalesGoodsMapper.selectByPrimaryKey(dto.getAfterSalesGoodsId());
            SaleorderGoods saleorderGoods = saleorderGoodsMapper.selectByPrimaryKey(afterSalesGoods.getOrderDetailId());
            // 查询每一个产品的出库 以及 签收 时间
            Long deliveryTime = getDeliveryTime(afterSalesGoods);
            Long arriveTime = getArriveTime(afterSalesGoods);
            dto.setArrivalTime(arriveTime);
            dto.setDeliveryTime(deliveryTime);
            List<String> barcodeFactoryList = warehouseGoodsOutInMapper.getBarcodFactoryListBySaleOrderGoodsId(saleorderGoods.getSaleorderGoodsId());
            logger.info("销售单id:{},销售单商品id:{},普发发货查询sn码:{},", saleorderGoods.getSaleorderId(), saleorderGoods.getSaleorderGoodsId(), JSON.toJSONString(barcodeFactoryList));
            dto.setSnList(barcodeFactoryList);
        }
    }

    @Override
    @Transactional
    public void updateServiceRecord(AfterSalesInstallServiceRecordDto dto) {
        AfterSalesInstallServiceRecord record = afterSalesInstallServiceRecordConvertor.toEntity(dto);
        afterSalesInstallServiceRecordMapper.updateByPrimaryKeySelective(record);
        //dto.getDetail().forEach(item -> {
        //    AfterSalesInstallServiceRecordDetail detail = afterSalesInstallServiceRecordDetailConvertor.toEntity(item);
        //    afterSalesInstallServiceRecordDetailMapper.updateByPrimaryKeySelective(detail);
        //});

        //保存上传附件
        if (!"".equals(dto.getFileNames()) && !"".equals(dto.getFileUris())) {
            //不为空，表示重新上传了附件
            //先作废原有附件信息
            Attachment old = new Attachment();
            old.setRelatedId(dto.getAfterSalesServiceId());
            old.setAttachmentFunction(SysOptionConstant.ID_4092);
            attachmentMapper.updateByRelation(old);
            //再保存
            ResultInfo resultInfo = saveServiceRecordAttachment(dto.getFileNames(), dto.getFileUris(), dto.getAfterSalesServiceId());
            if (!resultInfo.getCode().equals(0)) {
                throw new ServiceException("附件上传异常");
            }
        }
    }

    /**
     * 更新出库单sn码数据
     * @param detail
     */
    @Transactional
    public void updateWarehouseOutSn(AfterSalesInstallServiceRecordDetail detail) {
        AfterSalesGoods afterSalesGoods = afterSalesGoodsMapper.selectByPrimaryKey(detail.getAfterSalesGoodsId());

        // 所有出库单
        List<WarehouseGoodsOutInItem> itemList = new ArrayList<>();
        // 根据销售单获取换货售后单出库单
        List<WarehouseGoodsOutInItem> changeItemList = warehouseGoodsOutInMapper
                .getWarehouseGoodsOutInChangeBySaleOrderGoodsId(afterSalesGoods.getOrderDetailId());
        // 根据销售单获取销售出库单
        List<WarehouseGoodsOutInItem> outItemList = warehouseGoodsOutInMapper
                .getWarehouseGoodsOutInBySaleOrderGoodsId(afterSalesGoods.getOrderDetailId());
        itemList.addAll(changeItemList);
        itemList.addAll(outItemList);

        //过滤出库单Sn为空的数据
        List<WarehouseGoodsOutInItem> warehouseGoodsOutInItemList = itemList.stream()
                .filter(item -> StringUtils.isEmpty(item.getBarcodeFactory())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(warehouseGoodsOutInItemList)) {
            WarehouseGoodsOutInItem warehouseGoodsOutInItem = CollUtil.getFirst(warehouseGoodsOutInItemList);
            warehouseGoodsOutInItem.setBarcodeFactory(detail.getSupplCode());
            warehouseGoodsOutInItemMapper.updateByPrimaryKeySelective(warehouseGoodsOutInItem);
        }
    }

    @Override
    public List<AfterSalesInstallServiceRecordDetail> selectDetail(Integer afterSalesServiceId) {
        return afterSalesInstallServiceRecordDetailMapper.queryInfoByAftersalesGoodsId(afterSalesServiceId);
    }

    @Override
    @Transactional
    public Boolean cancelDispatch(ProductSelectionDispatchDto productSelectionDispatchDto) {
        Integer afterSalesId = productSelectionDispatchDto.getAfterSalesId();
        // 根据afterSalesId获取售后信息
        AfterSales sales = afterSalesMapper.getAfterSalesById(afterSalesId);
        // 如果售后状态为空或者不是进行中，则返回false
        if (Objects.isNull(sales.getAtferSalesStatus()) || !ErpConst.ONE.equals(sales.getAtferSalesStatus())) {
            logger.info("判断医修帮是否可以取消下派,订单状态为空或者不是进行中 AfterSalesId:{}");
            return false;
        }

        // 如果售后类型不是AFTERSALES_JZ，则查询其付款申请信息
        if(!AfterSalesProcessEnum.getInstance(sales.getType()).equals(AfterSalesProcessEnum.AFTERSALES_JZ)){
            List<PayApply> afterAtPaymentApply = afterSalesInvoiceMapper.getAfterAtPaymentApply(afterSalesId);
            // 判断是否为南京医修帮的维修公司
            Boolean medicalHelpCompany = afterSalesService.isMedicalHelpCompany(afterSalesId);
            // 如果维修公司不是南京医修帮或者新增了付款记录，则返回false
            if (!(medicalHelpCompany && CollectionUtils.isEmpty(afterAtPaymentApply))) {
                logger.info("判断医修帮是否可以取消下派,订单状态新增了付款记录或维修公司不是南极医修帮 AfterSalesId:{}");
                return false;
            }
        }
        AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(afterSalesId);
        String afterSalesNo = afterSalesById.getAfterSalesNo();
        String cancelReason = productSelectionDispatchDto.getCancelReason();
        Date date = new Date();

        //调用下派接口
        HashMap<String, String> headers = getHeaders();
        headers.put("token",yxbToken);
        headers.put("Content-Type", "application/json");


        AfterSalesUpdateRecordDto afterSalesUpdateRecordDto = new AfterSalesUpdateRecordDto();
        afterSalesUpdateRecordDto.setAfterSalesNo(afterSalesNo);
        afterSalesUpdateRecordDto.setCancelReason(cancelReason);
        afterSalesUpdateRecordDto.setErpOrderStatus("3");
        afterSalesUpdateRecordDto.setDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date));

        Boolean yxbApi = InvokeYXBApi(afterSalesId, afterSalesNo, afterSalesUpdateRecordDto, CANCEL_DISPATCH_URL, ErpConst.ONE);
        if (yxbApi) {
            //取消下派后把所有的下派成功数据软删除
            int i = afterSalesToYxbApiService.logicDeleteAfterCancel(afterSalesId);
            return true;
        }
        return false;
    }

    @Override
    @Transactional
    public Boolean confirmationDispatch(AfterSaleDto afterSaleDto) {
        Integer afterSalesId = afterSaleDto.getAfterSalesId();
        String afterSalesNo = afterSaleDto.getAfterSalesNo();
        Date date = new Date();
        // 根据afterSalesId获取售后信息
        AfterSales sales = afterSalesMapper.getAfterSalesById(afterSalesId);
        // 如果售后状态为空或者不是进行中，则返回false
        if (Objects.isNull(sales.getAtferSalesStatus()) || !ErpConst.ONE.equals(sales.getAtferSalesStatus())) {
            logger.info("判断医修帮是否可以下派,订单状态为空或者不是进行中 AfterSalesId:{}");
            return false;
        }

        // 如果售后类型不是AFTERSALES_JZ，则查询其付款申请信息
        if(!AfterSalesProcessEnum.getInstance(sales.getType()).equals(AfterSalesProcessEnum.AFTERSALES_JZ)){
            List<PayApply> afterAtPaymentApply = afterSalesInvoiceMapper.getAfterAtPaymentApply(afterSalesId);
            // 判断是否为南京医修帮的维修公司
            Boolean medicalHelpCompany = afterSalesService.isMedicalHelpCompany(afterSalesId);
            // 如果维修公司不是南京医修帮或者新增了付款记录，则返回false
            if (!(medicalHelpCompany && CollectionUtils.isEmpty(afterAtPaymentApply))) {
                logger.info("判断医修帮是否可以下派,订单状态新增了付款记录或维修公司不是南极医修帮 AfterSalesId:{}");
                return false;
            }
        }

        //下派确认防重校验
        //判断是否已经下派了
        AfterSalesToYxbEntity afterSalesToYxb = AfterSalesToYxbEntity.builder().afterSalesOrderId(afterSalesId).interfaceType(ErpConst.ZERO).success(ErpConst.ONE).isDelete(ErpConst.ZERO).build();
        List<AfterSalesToYxbDto> afterSalesToYxbDtos = afterSalesToYxbApiService.selectByConditionDispatch(afterSalesToYxb);
        Boolean isSuccessful = afterSalesToYxbDtos.stream().sorted(Comparator.comparing(AfterSalesToYxbDto::getAddTime).reversed()).findFirst().map(dto -> ErpConst.ONE == dto.getSuccess()).orElse(false);
        if (isSuccessful) {
            logger.info("该售后单已经下派成功，请勿重复操作");
            return false;
        }
        //调用下派接口
        AfterSalesPushRecordDTO afterSalesPushRecordDTO = new AfterSalesPushRecordDTO();
        //销售单号
        afterSalesPushRecordDTO.setAfterSalesNo(afterSalesNo);
        //售后类型
        afterSalesPushRecordDTO.setType(afterSaleDto.getTypeName());
        //售后处理人
        afterSalesPushRecordDTO.setAfterSalesPerson(afterSaleDto.getServiceUserName());
        //酬金
        List<AfterSalesInstallstionVo> afterSalesInstallstionVoList = afterSaleDto.getAfterSalesInstallstionVoList();
        if (CollUtil.isNotEmpty(afterSalesInstallstionVoList)) {
            afterSalesPushRecordDTO.setPayment(afterSalesInstallstionVoList.get(0).getEngineerAmount());
        }
        //下派时间
        afterSalesPushRecordDTO.setPushTime(date);
        //详情说明
        afterSalesPushRecordDTO.setBusinessDescription(afterSaleDto.getComments());
        Trader trader = traderMapper.getTraderInfoByTraderId(afterSaleDto.getTraderId());
        //客户id
        afterSalesPushRecordDTO.setDealerCustomerId(afterSaleDto.getTraderId());
        //客户名称
        afterSalesPushRecordDTO.setDealerCustomerName(afterSaleDto.getTraderName());
        //客户地区,省市区
        Integer areaId = trader.getAreaId();
        String addressIdStr = regionService.getRegionIdStringByMinRegionId(areaId);
        if (StringUtil.isNotEmpty(addressIdStr)) {
            String[] traderAreaArr = addressIdStr.split(",");
            Optional.ofNullable(traderAreaArr).filter(arr -> arr.length > 0).ifPresent(arr -> afterSalesPushRecordDTO.setDealerProvince(arr[0]));
            Optional.ofNullable(traderAreaArr).filter(arr -> arr.length > 1).ifPresent(arr -> afterSalesPushRecordDTO.setDealerCity(arr[1]));
            Optional.ofNullable(traderAreaArr).filter(arr -> arr.length > 2).ifPresent(arr -> afterSalesPushRecordDTO.setDealerArea(arr[2]));
            afterSalesPushRecordDTO.setCustomerAddress(afterSaleDto.getAddress());
        }

        //客户类型
        afterSalesPushRecordDTO.setDealerType(ErpConst.CUSTOME_TYPE_RESEARCH_MEDICAL.equals(trader.getCustomerType())?"科研医疗":"临床医疗");
        //客户等级
        List<SysOptionDefinition> customerLevers = getSysOptionDefinitionList(SysOptionConstant.ID_11);
        if (CollUtil.isNotEmpty(customerLevers)) {
            customerLevers.forEach(item -> {
                if (afterSaleDto.getCustomerLevel() != null && item.getSysOptionDefinitionId() != null
                        && afterSaleDto.getCustomerLevel().equals(item.getSysOptionDefinitionId())) {
                    afterSalesPushRecordDTO.setDealerLevel(item.getTitle());
                }
            });
        }
        //售后报单人id
        afterSalesPushRecordDTO.setDealerContactsId(afterSaleDto.getTraderContactId());
        //售后报单人姓名
        afterSalesPushRecordDTO.setDealerContacts(afterSaleDto.getTraderContactName());
        //售后报单人手机
        afterSalesPushRecordDTO.setDealerContactsMobile(afterSaleDto.getTraderContactMobile());
        afterSalesPushRecordDTO.setDealerContactsPhone(afterSaleDto.getTraderContactTelephone());
        TraderContact traderContact = traderContactMapper.selectByPrimaryKey(afterSaleDto.getTraderContactId());
        //报单人职位
        afterSalesPushRecordDTO.setDealerContactsPosition(traderContact.getPosition());
        //报单人备注
        afterSalesPushRecordDTO.setDealerContactsRemark(traderContact.getComments());
        //售后联系人
        afterSalesPushRecordDTO.setCustomerCharge(afterSaleDto.getAfterConnectUserName());
        //售后联系人电话
        afterSalesPushRecordDTO.setCustomerPhone(afterSaleDto.getAfterConnectPhone());
        //售后地区及详细地址  省市区编码
        afterSalesPushRecordDTO.setCustomerRegion(afterSaleDto.getArea());
        String invoiceTraderAddressIdStr = regionService.getRegionIdStringByMinRegionId(afterSaleDto.getAreaId());
        if(StringUtil.isNotEmpty(invoiceTraderAddressIdStr)){
            String[] invoiceTraderAddressIdArr = invoiceTraderAddressIdStr.split(",");
            Optional.ofNullable(invoiceTraderAddressIdArr).filter(arr -> arr.length > 0).ifPresent(arr -> afterSalesPushRecordDTO.setProvinceName(arr[0]));
            Optional.ofNullable(invoiceTraderAddressIdArr).filter(arr -> arr.length > 1).ifPresent(arr -> afterSalesPushRecordDTO.setCityName(arr[1]));
            Optional.ofNullable(invoiceTraderAddressIdArr).filter(arr -> arr.length > 2).ifPresent(arr -> afterSalesPushRecordDTO.setAreaName(arr[2]));
        }
        //附件
        List<String> urls = new ArrayList<>();
        List<Attachment> attachmentList = afterSaleDto.getAttachmentList();
        ArrayList<AfterSalesPushRecordDTO.File> files = new ArrayList<>();
        if (attachmentList != null && !attachmentList.isEmpty()) {
            for (Attachment att : attachmentList) {
                AfterSalesPushRecordDTO.File file = new AfterSalesPushRecordDTO.File();
                String url;
                if (att.getDomain().startsWith("http")) {
                    url = att.getDomain() + att.getUri();
                } else {
                    url = httpUrl + att.getDomain() + att.getUri();
                }
                file.setFileName(att.getName());
                file.setFileUrl(url);
                files.add(file);
                urls.add(url);
            }
        }
        afterSalesPushRecordDTO.setAttachment(JSON.toJSONString(files));
        //归属销售
        afterSalesPushRecordDTO.setSalesPerson(afterSaleDto.getUserName());
        //归属部门
        afterSalesPushRecordDTO.setDepartmentName(afterSaleDto.getOrgName());
        //商品明细数据
        ArrayList<AfterSalesPushRecordDTO.DeviceInfo> deviceInfos = new ArrayList<>();
        afterSaleDto.getProductSelectionDispatchDtos().forEach(item -> {
            AfterSalesPushRecordDTO.DeviceInfo product = new AfterSalesPushRecordDTO.DeviceInfo();
            product.setAfterSaleNum(item.getAfterSalesQuantity() == 0 ? 1 : item.getAfterSalesQuantity());
            product.setMechineName(item.getProductName());
            product.setMechineBrand(item.getProductBrand());
            product.setMechineModel(item.getProductModel());
            product.setMechineSku("V"+item.getProductId().toString());
            deviceInfos.add(product);
        });
        afterSalesPushRecordDTO.setDeviceInfoList(deviceInfos);
        afterSalesPushRecordDTO.setErpOrderStatus(0);
        afterSalesPushRecordDTO.setToken(yxbToken);
        afterSalesPushRecordDTO.setSaleOrderNo(sales.getOrderNo());
        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(sales.getOrderNo());
        if (saleorder != null && saleorder.getValidTime() != null){
            afterSalesPushRecordDTO.setSaleOrderValidTime(new Date(saleorder.getValidTime()));
        }
       
        try {
            logger.info("医修帮对接接口"+CONFIRMATION_DISPATCH_URL+"入参 :{}" ,JSON.toJSONString(afterSalesPushRecordDTO));


            RestfulResult<String> stringRestfulResult = afterSaleOrderYxbApi.syncWorkOrder(afterSalesPushRecordDTO, yxbToken);

            logger.info("医修帮对接接口"+CONFIRMATION_DISPATCH_URL+"出参 :{}" ,JSON.toJSONString(stringRestfulResult));
            if (Objects.nonNull(stringRestfulResult) && "success".equals(stringRestfulResult.getCode())) {
                logger.info("医修帮"+CONFIRMATION_DISPATCH_URL+"接口调用成功");
                AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder()
                        .afterSalesOrderId(afterSalesId)
                        .interfaceType(0)
                        .afterSalesOrderNo(afterSalesNo)
                        .param(JSON.toJSONString(afterSalesPushRecordDTO))
                        .result(stringRestfulResult.getData())
                        .isDelete(ErpConst.ZERO)
                        .success(ErpConst.ONE).build();
                afterSalesToYxbApiService.insertCancelAfterSalesToYxb(afterSalesToYxbEntity);
                return true;
            }else {

                logger.info("医修帮"+CONFIRMATION_DISPATCH_URL+"接口调用失败");

                AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder()
                        .afterSalesOrderId(afterSalesId)
                        .interfaceType(0)
                        .afterSalesOrderNo(afterSalesNo)
                        .param(JSON.toJSONString(afterSalesPushRecordDTO))
                        .result(stringRestfulResult.getData())
                        .isDelete(ErpConst.ZERO)
                        .success(ErpConst.ZERO).build();

                afterSalesToYxbApiService.insertCancelAfterSalesToYxb(afterSalesToYxbEntity);
                return false;
            }
        } catch (Exception e) {
            logger.error("医修帮"+CONFIRMATION_DISPATCH_URL+"接口调用接口异常", e);

            AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder()
                    .afterSalesOrderId(afterSalesId)
                    .interfaceType(0)
                    .afterSalesOrderNo(afterSalesNo)
                    .param(JSON.toJSONString(afterSalesPushRecordDTO))
                    .isDelete(ErpConst.ZERO)
                    .success(ErpConst.ZERO).build();

            afterSalesToYxbApiService.insertCancelAfterSalesToYxb(afterSalesToYxbEntity);
            return false;
        }
    }

    private void wxErrorNotice(String msg) {
        WxMsgDto wxMsgDto = new WxMsgDto().initWxMsgDto(msg);
        wxRobotService.send(yxbPayRecordRobotNum, wxMsgDto);
    }

    private  HashMap<String, String> getHeaders() {
        HashMap<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json;charset=UTF-8");
        headers.put("Accept", "application/json;charset=UTF-8");
        return headers;
    }

    @Override
    public Boolean closeOrCompletedYxb(AfterSalesToYxbDto afterSalesToYxbDto) {
        Integer afterSalesOrderId = afterSalesToYxbDto.getAfterSalesOrderId();
        AfterSales afterSalesById = afterSalesMapper.getAfterSalesById(afterSalesOrderId);
        Integer atferSalesStatus = afterSalesById.getAtferSalesStatus();
        String afterSalesNo = afterSalesById.getAfterSalesNo();
        if (ErpConst.TWO == atferSalesStatus) {
            //已完结
            AfterSalesUpdateRecordDto afterSalesUpdateRecordDto = new AfterSalesUpdateRecordDto();
            afterSalesUpdateRecordDto.setAfterSalesNo(afterSalesNo);
            afterSalesUpdateRecordDto.setErpOrderStatus("1");
            Boolean x = InvokeYXBApi(afterSalesOrderId, afterSalesNo, afterSalesUpdateRecordDto, CANCEL_DISPATCH_URL, ErpConst.THREE);
            if (x != null) return x;
        }
        if (ErpConst.THREE == atferSalesStatus) {
            //已关闭
            AfterSalesUpdateRecordDto afterSalesUpdateRecordDto = new AfterSalesUpdateRecordDto();
            afterSalesUpdateRecordDto.setAfterSalesNo(afterSalesNo);
            afterSalesUpdateRecordDto.setErpOrderStatus("2");
            Boolean x = InvokeYXBApi(afterSalesOrderId, afterSalesNo, afterSalesUpdateRecordDto, CANCEL_DISPATCH_URL, ErpConst.TWO);
            if (x != null) return x;
        }
        return false;
    }

    @Override
    public void saveYxbPayRecord(AfterSalesToYxbDto afterSalesToYxbDto) {
        //找到售后单对应的付款交易记录
        CapitalBill capitalBill = new CapitalBill();
        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
        capitalBillDetail.setOrderType(Constants.THREE);//售后订单类型
        capitalBillDetail.setOrderNo(afterSalesToYxbDto.getAfterSalesOrderNo());
        capitalBillDetail.setRelatedId(afterSalesToYxbDto.getAfterSalesOrderId());
        capitalBill.setCapitalBillDetail(capitalBillDetail);
        List<CapitalBill> capitalBillList = capitalBillService.getCapitalBillList(capitalBill);
        if (CollUtil.isNotEmpty(capitalBillList)) {
            for (int i = 0; i < capitalBillList.size(); i++) {
                try {
                    TradePushRecodeDto tradePushRecodeDto = new TradePushRecodeDto();
                    CapitalBill bill = capitalBillList.get(i);
                    List<CapitalBillDetail> capitalBillDetails = capitalBillDetailMapper.selectByCapitalBillId(bill.getCapitalBillId());
                    Integer bussinessType = 0;
                    if (CollectionUtil.isNotEmpty(capitalBillDetails)) {
                        CapitalBillDetail cbd = capitalBillDetails.get(0);
                        bussinessType = cbd.getBussinessType();
                    }
                    if (bussinessType != 525) {
                        logger.info("业务类型不为订单付款，不推送 CapitalBillId:{}", bill.getCapitalBillId());
                        continue;
                    }
                    Integer integer = afterSalesBillToYxbApiService.countAfterSalesBillByAfterSalesIdAndCapitalBillId(afterSalesToYxbDto.getAfterSalesOrderId(), bill.getCapitalBillId());
                    if (integer == ErpConst.ZERO) {
                        logger.info("推送售后单对应的付款交易记录");
                        HashMap<String, Object> params = new HashMap<>();
                        //售后单号
                        tradePushRecodeDto.setAfterSalesNo(afterSalesToYxbDto.getAfterSalesOrderNo());
                        //流水号
                        tradePushRecodeDto.setBillNo(bill.getCapitalBillNo());
                        //业务类型
                        String bussinessTypeText = "订单付款";
                        tradePushRecodeDto.setBusinessType(bussinessTypeText);
                        //交易时间
                        tradePushRecodeDto.setTraderTime(bill.getTraderTime());
                        //交易主体
                        tradePushRecodeDto.setTraderSubject(ErpConst.ONE.equals(bill.getTraderSubject()) ? "对公" : "对私");
                        //交易金额
                        tradePushRecodeDto.setTraderPrice(bill.getAmount());
                        //交易方式
                        tradePushRecodeDto.setTraderWay(KingDeeTraderModeEnum.matchDescByCode(bill.getTraderMode()));
                        //交易名称
                        tradePushRecodeDto.setTraderName(bill.getPayer());
                        //交易备注
                        tradePushRecodeDto.setTraderRemake(bill.getComments());
                        logger.info("医修帮对接接口" + PUSH_YXB_URL + "入参 :{}", JSON.toJSONString(params));

                        RestfulResult<String> stringRestfulResult = afterSaleOrderYxbApi.syncWorkOrderPay(tradePushRecodeDto, yxbToken);
                        logger.info("医修帮对接接口" + PUSH_YXB_URL + "出参 :{}", JSON.toJSONString(stringRestfulResult));
                        if (Objects.nonNull(stringRestfulResult) && "success".equals(stringRestfulResult.getCode())) {
                            logger.info("医修帮" + PUSH_YXB_URL + "接口调用成功");
                            AfterSalesBillToYxbEntity afterSalesBillToYxbEntity = AfterSalesBillToYxbEntity.builder()
                                    .afterSalesOrderNo(afterSalesToYxbDto.getAfterSalesOrderNo())
                                    .capitalBillNo(bill.getCapitalBillNo())
                                    .amount(bill.getAmount())
                                    .capitalBillId(bill.getCapitalBillId())
                                    .afterSalesOrderId(afterSalesToYxbDto.getAfterSalesOrderId())
                                    .comments(bill.getComments())
                                    .payer(bill.getPayer())
                                    .traderSubject(bill.getTraderSubject())
                                    .transactionMode(KingDeeTraderModeEnum.matchDescByCode(bill.getTraderMode()))
                                    .result(JSON.toJSONString(stringRestfulResult))
                                    .isDelete(0)
                                    .build();
                            afterSalesBillToYxbApiService.insertAfterSalesBillToYxb(afterSalesBillToYxbEntity);
                        } else {
                            logger.info("医修帮" + PUSH_YXB_URL + "接口调用失败");
                            this.wxErrorNotice("医修帮" + PUSH_YXB_URL + "接口调用失败  "+ "销售单号 :"+afterSalesToYxbDto.getAfterSalesOrderNo());
                        }
                    }
                } catch (Exception e) {
                    logger.error("医修帮" + PUSH_YXB_URL + "接口调用接口异常", e);
                    this.wxErrorNotice("医修帮" + PUSH_YXB_URL + "接口调用接口异常  "+ "销售单号 :"+afterSalesToYxbDto.getAfterSalesOrderNo());
                }
            }
        }
    }


    @Override
    public Boolean saveYxbFollowRecord(AfterSalesFollowUpRecordDto afterSalesFollowUpRecordDto) {
        String afterSalesNo = afterSalesFollowUpRecordDto.getAfterSalesNo();
        AfterSales afterSalesByNo = afterSalesMapper.getAfterSalesByNo(afterSalesNo);
        if (Objects.isNull(afterSalesByNo)) {
            return false;
        }
        Integer afterSalesId = afterSalesByNo.getAfterSalesId();
        afterSalesFollowUpRecordDto.setAfterSalesId(afterSalesId);
        AfterSalesFollowUpRecord afterSalesFollowUpRecord = afterSalesFollowUpRecordConvertor.toEntity(afterSalesFollowUpRecordDto);
        afterSalesFollowUpRecord.setCreator(1);;//系统admin
        afterSalesFollowUpRecord.setUpdater(1);;//系统admin
        afterSalesFollowUpRecord.setOrgId(10);//南京售后部
        afterSalesFollowUpRecordService.insert(afterSalesFollowUpRecord);
        return true;
    }


    private Boolean InvokeYXBApi(Integer afterSalesOrderId, String afterSalesNo, AfterSalesUpdateRecordDto params, String url, Integer interfaceType) {
        try {
            logger.info("医修帮对接接口"+url+"入参 :{}" ,JSON.toJSONString(params));

            RestfulResult<String> stringRestfulResult = afterSaleOrderYxbApi.syncWorkOrderStatus(params, yxbToken);

            logger.info("医修帮对接接口"+url+"出参 :{}" ,JSON.toJSONString(stringRestfulResult));

            if (Objects.nonNull(stringRestfulResult) && "success".equals(stringRestfulResult.getCode())) {

                logger.info("医修帮"+url+"接口调用成功");

                AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder()
                        .afterSalesOrderId(afterSalesOrderId)
                        .interfaceType(interfaceType)
                        .afterSalesOrderNo(afterSalesNo)
                        .param(JSON.toJSONString(params))
                        .result(JSON.toJSONString(stringRestfulResult))
                        .isDelete(ErpConst.ZERO)
                        .success(ErpConst.ONE).build();

                afterSalesToYxbApiService.insertCancelAfterSalesToYxb(afterSalesToYxbEntity);
                return true;
            }else {

                logger.info("医修帮"+url+"接口调用失败");

                AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder()
                        .afterSalesOrderId(afterSalesOrderId)
                        .interfaceType(interfaceType)
                        .afterSalesOrderNo(afterSalesNo)
                        .param(JSON.toJSONString(params))
                        .result(JSON.toJSONString(stringRestfulResult))
                        .isDelete(ErpConst.ZERO)
                        .success(ErpConst.ZERO).build();

                afterSalesToYxbApiService.insertCancelAfterSalesToYxb(afterSalesToYxbEntity);
                return false;
            }
    } catch (Exception e) {
            logger.error("医修帮"+url+"接口调用接口异常", e);

            AfterSalesToYxbEntity afterSalesToYxbEntity = AfterSalesToYxbEntity.builder()
                    .afterSalesOrderId(afterSalesOrderId)
                    .interfaceType(interfaceType)
                    .afterSalesOrderNo(afterSalesNo)
                    .param(JSON.toJSONString(params))
                    .isDelete(ErpConst.ZERO)
                    .success(ErpConst.ZERO).build();

            afterSalesToYxbApiService.insertCancelAfterSalesToYxb(afterSalesToYxbEntity);
        }
        return false;
    }

    @Override
    public AfterSales queryAfterSalesByNo(String afterSalesNo) {
        return afterSalesMapper.getAfterSalesByNo(afterSalesNo);
    }

    @Override
    public List<String> yxbFileSava(List<String> urls) {

        if (CollectionUtils.isEmpty(urls)) {
            return Collections.emptyList();
        }

        List<String> collect = urls.stream().map(x -> {
            byte[] bytes = HttpUtil.downloadBytes(x);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);

            // 标记输入流的开始位置，这样我们可以多次读取它
            inputStream.mark(0);

            String type = FileTypeUtil.getType(inputStream);

            // 重置输入流到开始位置，这样我们可以再次读取它
            inputStream.reset();

            String s = ossUtilsService.uploadFileStream2Oss(inputStream, type);
            if (StrUtil.isNotEmpty(s)) {
                String[] split = s.split(ossUrl);
                if (split.length > 1) {
                    return split[1];
                }
            }
            return null;
        }).filter(StrUtil::isNotEmpty).collect(Collectors.toList());

        return collect;
    }

    @Override
    public void modifyAfterSalesContactInfo(ModifyAfterSalesContactInfoDto modifyAfterSalesContactInfoDto) {
        logger.info("前台修改售后单联系人信息推送,参数:{}", JSON.toJSONString(modifyAfterSalesContactInfoDto));
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(modifyAfterSalesContactInfoDto.getAfterSalesOrderNo());
        if (Objects.isNull(afterSales)) {
            throw new ServiceException("前台修改售后单联系人信息推送,售后单不存在");
        }
        Integer afterSalesId = afterSales.getAfterSalesId();
        AfterSalesDetailVo afterSalesDetail = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            throw new ServiceException("前台修改售后单联系人信息推送,售后单详情不存在");
        }
        Integer afterSalesDetailId = afterSalesDetail.getAfterSalesDetailId();
        AfterSalesDetailVo updateDetail = new AfterSalesDetailVo();
        updateDetail.setAfterSalesDetailId(afterSalesDetailId);
        // 售后联系人
        updateDetail.setAfterConnectUserName(modifyAfterSalesContactInfoDto.getContactUserName());
        // 售后联系电话
        updateDetail.setAfterConnectPhone(modifyAfterSalesContactInfoDto.getContactMobile());
        // 售后服务地区id
        updateDetail.setAreaId(modifyAfterSalesContactInfoDto.getContactArea());
        // 售后服务地区
        updateDetail.setArea(this.getAddressByAreaId(modifyAfterSalesContactInfoDto.getContactArea()));
        // 售后服务详细地址
        updateDetail.setAddress(modifyAfterSalesContactInfoDto.getContactAddress());
        logger.info("前台修改售后单联系人信息推送,更新数据:{}", JSON.toJSONString(updateDetail));
        afterSalesDetailMapper.updateByPrimaryKeyDetailInfo(updateDetail);
    }

    @Override
    public void afterSalesEvaluationReturn(AfterSalesEvaluationReturnDto afterSalesEvaluationReturnDto) {
        logger.info("售后评价回传,参数:{}", JSON.toJSONString(afterSalesEvaluationReturnDto));
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSalesEvaluationReturnDto.getAfterSalesOrderNo());
        if (Objects.isNull(afterSales)) {
            throw new ServiceException("售后评价回传,售后单不存在");
        }
        Integer afterSalesId = afterSales.getAfterSalesId();
        AfterSalesDetailVo afterSalesDetail = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            throw new ServiceException("前台修改售后单联系人信息推送,售后单详情不存在");
        }
        Date now = new Date();
        ReturnVisitRecord returnVisitRecord = new ReturnVisitRecord();
        returnVisitRecord.setAfterSalesId(afterSalesId);
        returnVisitRecord.setCustomerName(afterSalesDetail.getAfterConnectUserName());
        returnVisitRecord.setCustomerNature(ErpConstant.TWO);
        returnVisitRecord.setCustomerMobile(afterSalesDetail.getAfterConnectPhone());
        returnVisitRecord.setStatus(ErpConstant.ONE);
        returnVisitRecord.setReturnVisitDepartment(ErpConstant.THREE);
        returnVisitRecord.setServiceResponseScore(afterSalesEvaluationReturnDto.getServiceResponse() * 20);
        returnVisitRecord.setServiceAttitudeScore(afterSalesEvaluationReturnDto.getServiceAttitude() * 20);
        returnVisitRecord.setServiceCapabilityScore(afterSalesEvaluationReturnDto.getServiceProfession() * 20);
        returnVisitRecord.setIsComplaint(ErpConstant.ZERO);
        returnVisitRecord.setIsRecommend(ErpConstant.ZERO);
        returnVisitRecord.setTotalScore(returnVisitRecord.getServiceResponseScore() * 0.2 + returnVisitRecord.getServiceAttitudeScore() * 0.4 + returnVisitRecord.getServiceCapabilityScore() * 0.4);
        returnVisitRecord.setComments(afterSalesEvaluationReturnDto.getOpinion());
        returnVisitRecord.setAddTime(now.getTime());
        returnVisitRecord.setModTime(now.getTime());
        logger.info("售后评价回传,插入数据:{}", JSON.toJSONString(returnVisitRecord));
        returnVisitRecordMapper.insertSelective(returnVisitRecord);
    }

    @Override
    public AfterSalesRefundInfoDto refundDetail(Integer afterSalesId) {
        logger.info("售后退款信息详情,售后单id:{}", afterSalesId);
        AfterSalesRefundInfoDto afterSalesRefundInfoDto = new AfterSalesRefundInfoDto();
        List<AfterSalesRefundInfoDto.RefundDetailDto> refundDetailDtoList = new ArrayList<>();
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSales)) {
            throw new ServiceException("售后单不存在");
        }
        AfterSalesDetailVo afterSalesDetail = afterSalesDetailMapper.getAfterSalesDetailByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            throw new ServiceException("售后单详情不存在");
        }
        logger.info("售后退款信息详情,afterSales:{},afterSalesDetail:{}", JSON.toJSONString(afterSales), JSON.toJSONString(afterSalesDetail));


        if(afterSalesDetail.getRefund().equals(0)){
            afterSalesRefundInfoDto.setRefund(ErpConstant.TWO);//默认退到客户
        }else{
            afterSalesRefundInfoDto.setRefund(afterSalesDetail.getRefund());
        }



        afterSalesRefundInfoDto.setFinalRefundableAmount(afterSalesDetail.getFinalRefundableAmount());
        AfterSalesRefundInfoDto.RefundDetailDto publicRefund = new AfterSalesRefundInfoDto.RefundDetailDto();
        publicRefund.setTraderSubject(ErpConstant.ONE);
        publicRefund.setTraderSubjectName("对公");
        publicRefund.setRefundAmount(afterSalesDetail.getPublicRefundAmount());
        AfterSalesRefundInfoDto.RefundDetailDto privateRefund = new AfterSalesRefundInfoDto.RefundDetailDto();
        privateRefund.setTraderSubject(ErpConstant.TWO);
        privateRefund.setTraderSubjectName("对私");
        privateRefund.setRefundAmount(afterSalesDetail.getPrivateRefundAmount());


        List<PayApplyDto> byPayTypeAndRelatedId = payApplyApiService.findByPayTypeAndRelatedId(PayApplyTypeEnum.AFTER_SALES.getCode(), afterSalesId);
        if (CollectionUtils.isNotEmpty(byPayTypeAndRelatedId)) {
            BigDecimal publicPaidAmount = byPayTypeAndRelatedId.stream()
                    .filter(payApplyDto -> Objects.equals(payApplyDto.getTraderSubject(), ErpConstant.ONE))
                    .map(PayApplyDto::getAmount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            publicRefund.setPaidAmount(publicPaidAmount);

            BigDecimal privatePaidAmount = byPayTypeAndRelatedId.stream()
                    .filter(payApplyDto -> Objects.equals(payApplyDto.getTraderSubject(), ErpConstant.TWO))
                    .map(PayApplyDto::getAmount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            privateRefund.setPaidAmount(privatePaidAmount);
        }

        
        Integer orderId = afterSales.getOrderId();
        List<CapitalBillDto> receivedcapitalBillList = capitalBillApiService.getReceivedAmountBySaleOrderId(orderId);
        logger.info("售后退款信息详情,查询销售单关联的收款单:{}", JSON.toJSONString(receivedcapitalBillList));
        if (CollUtil.isNotEmpty(receivedcapitalBillList)) {
            Map<Integer, BigDecimal> receivedAmountMap = receivedcapitalBillList.stream()
                    .collect(Collectors.groupingBy(
                            CapitalBillDto::getTraderSubject,
                            Collectors.reducing(BigDecimal.ZERO, CapitalBillDto::getAmount, BigDecimal::add)
                    ));
            for (Map.Entry<Integer, BigDecimal> entry : receivedAmountMap.entrySet()) {
                if (ErpConstant.ONE.equals(entry.getKey())) {
                    publicRefund.setReceivedAmount(entry.getValue());
                    publicRefund.setRefundedAmount(newAfterSalesDetailMapper.getPublicRefundAmount(afterSalesId, orderId, ErpConstant.TWO));
                    publicRefund.setOccupiedAmount(newAfterSalesDetailMapper.getPublicRefundAmount(afterSalesId, orderId, ErpConstant.ONE));
                    publicRefund.setRefundableAmount(publicRefund.getReceivedAmount().subtract(publicRefund.getRefundedAmount().add(publicRefund.getOccupiedAmount())));
                }
                if (ErpConstant.TWO.equals(entry.getKey())) {
                    privateRefund.setReceivedAmount(entry.getValue());
                    privateRefund.setRefundedAmount(newAfterSalesDetailMapper.getPrivateRefundAmount(afterSalesId, orderId, ErpConstant.TWO));
                    privateRefund.setOccupiedAmount(newAfterSalesDetailMapper.getPrivateRefundAmount(afterSalesId, orderId, ErpConstant.ONE));
                    privateRefund.setRefundableAmount(privateRefund.getReceivedAmount().subtract(privateRefund.getRefundedAmount().add(privateRefund.getOccupiedAmount())));
                }
            }

        }
        logger.info("售后退款信息详情,对公明细行:{}", JSON.toJSONString(publicRefund));
        refundDetailDtoList.add(publicRefund);
        logger.info("售后退款信息详情,对私明细行:{}", JSON.toJSONString(privateRefund));
        refundDetailDtoList.add(privateRefund);
        afterSalesRefundInfoDto.setRefundDetailDtoList(refundDetailDtoList);
        return afterSalesRefundInfoDto;
    }

    @Override
    public void updateRefundInfo(AfterSalesRefundInfoDto afterSalesRefundInfoDto, HttpServletRequest request) {
        logger.info("售后退款信息更新，入参：{}", JSON.toJSONString(afterSalesRefundInfoDto));
        Integer afterSalesId = afterSalesRefundInfoDto.getAfterSalesId();
        Integer refund = afterSalesRefundInfoDto.getRefund();
        BigDecimal finalRefundableAmount = afterSalesRefundInfoDto.getFinalRefundableAmount();
        if (Objects.isNull(afterSalesId) || Objects.isNull(refund) || Objects.isNull(finalRefundableAmount)) {
            throw new ServiceException("售后单id或售后退款类型不能为空");
        }
        // 校验
        updateRefundCheck(afterSalesId);
        AfterSales as = new AfterSales();
        as.setAfterSalesId(afterSalesId);
        AfterSalesVo afterSales = afterSalesMapper.viewAfterSalesDetail(as);
        afterSalesOrderService.calRefund(afterSales);
        AfterSalesDetailEntity afterSalesDetail = newAfterSalesDetailMapper.findByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            throw new ServiceException("售后单详情不存在");
        }
        if (finalRefundableAmount.compareTo(afterSalesDetail.getFinalRefundableAmount()) != 0) {
            throw new ServiceException("退款金额与计算后的退款金额不一致");
        }
        // 提前更新退还路径
        updateRefund(refund, afterSalesDetail.getAfterSalesDetailId());
        BigDecimal publicRefundAmount = BigDecimal.ZERO;
        BigDecimal privateRefundAmount = BigDecimal.ZERO;
        if (ErpConstant.ZERO.equals(refund)) {
            // 对公和私人的退款金额都为零
        } else {
            // 获取对公退款金额
            Optional<AfterSalesRefundInfoDto.RefundDetailDto> publicRefund = afterSalesRefundInfoDto.getRefundDetailDtoList().stream()
                    .filter(refundDetailDto -> ErpConstant.ONE.equals(refundDetailDto.getTraderSubject()))
                    .findFirst();

            publicRefundAmount = publicRefund
                    .map(AfterSalesRefundInfoDto.RefundDetailDto::getRefundAmount)
                    .orElseThrow(() -> new ServiceException("对公退款金额不能为空"));

            if (ErpConstant.ONE.equals(refund)) {
                // 私人退款金额保持为零
            } else if (ErpConstant.TWO.equals(refund)) {
                // 获取私人退款金额
                Optional<AfterSalesRefundInfoDto.RefundDetailDto> privateRefund = afterSalesRefundInfoDto.getRefundDetailDtoList().stream()
                        .filter(refundDetailDto -> ErpConstant.TWO.equals(refundDetailDto.getTraderSubject()))
                        .findFirst();

                privateRefundAmount = privateRefund
                        .map(AfterSalesRefundInfoDto.RefundDetailDto::getRefundAmount)
                        .orElseThrow(() -> new ServiceException("私人退款金额不能为空"));
            }
        }
        logger.info("executeRefundOperation afterSales begin:{}", JSON.toJSONString(afterSales));
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        AfterSalesDetailVo afterSalesDetailVo = afterSalesService.getAfterSalesDetail(afterSales);
        afterSales.setRefund(afterSalesDetailVo.getRefund());
        if (null != afterSalesDetailVo && Lists.newArrayList(2,3,4).contains(afterSalesDetailVo.getRefundAmountStatus())) {
            logger.info("售后订单 {} 当前退款状态未完成", afterSales.getAfterSalesId());
            throw new ServiceException("退款状态校验未通过");
        }
        afterSales.setTraderId(afterSalesDetailVo.getTraderId());
        if(AfterSalesProcessEnum.AFTERSALES_TH.getCode().equals(afterSales.getType()) ||
                AfterSalesProcessEnum.AFTERSALES_TK.getCode().equals(afterSales.getType())){
            afterSales.setPayer(user.getCompanyName());
        }
        ResultInfo<?> resultInfo = afterSalesOrderService.executeRefundOperation(afterSales, user);
        //执行退款运算之后计算售后单的退款状态
        if (resultInfo == null || ResultInfo.error().getCode().equals(resultInfo.getCode()) || ErpConst.HUNDRED.equals(resultInfo.getCode())){
            //100也是执行退款运算失败的返回值
            throw new ServiceException("退款失败");
        }
        afterSalesOrderService.refreshAmountRefundStatus(afterSales.getAfterSalesId());
        logger.info("executeRefundOperation afterSales end:{}", JSON.toJSONString(resultInfo));

        logger.info("售后退款信息更新,refund:{},publicRefundAmount:{},privateRefundAmount:{},afterSalesDetailId:{}", refund, publicRefundAmount, privateRefundAmount, afterSalesDetail.getAfterSalesDetailId());
        newAfterSalesDetailMapper.updatePublicAndPrivateRefundAmount(refund, publicRefundAmount, privateRefundAmount, afterSalesDetail.getAfterSalesDetailId());
    }

    private void updateRefund(Integer refund, Integer afterSalesDetailId) {
        newAfterSalesDetailMapper.updateRefundByAfterSalesDetailId(refund, afterSalesDetailId);
    }

    public void updateRefundCheck(Integer afterSalesId) {
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSales)) {
            throw new ServiceException("售后单不存在");
        }
        Integer atferSalesStatus = afterSales.getAtferSalesStatus();
        if (Objects.isNull(atferSalesStatus) || !ErpConstant.ONE.equals(atferSalesStatus)) {
            throw new ServiceException("单据状态为" + NewAfterSalesStatusEnum.getDescByCode(atferSalesStatus) + "，不可执行该操作");
        }
        Integer amountRefundStatus = afterSales.getAmountRefundStatus();
//        if (Objects.isNull(amountRefundStatus)) {
//            throw new ServiceException("请先执行退款运算");
//        }
        if (Objects.nonNull(amountRefundStatus) && !ErpConstant.ONE.equals(amountRefundStatus)) {
            throw new ServiceException("退款状态为 " + NewAfterRefundStatusEnum.getDescByCode(amountRefundStatus) + "，不可执行该操作");
        }
        AfterSalesDetailEntity afterSalesDetail = newAfterSalesDetailMapper.findByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            throw new ServiceException("售后单详情不存在");
        }
        Integer isCalRefund = afterSalesDetail.getIsCalRefund();
        if (!ErpConstant.ONE.equals(isCalRefund)) {
            throw new ServiceException("请先执行退款运算");
        }
        BigDecimal refundAmount = afterSalesDetail.getPublicRefundAmount().add(afterSalesDetail.getPrivateRefundAmount());
        if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
            throw new ServiceException("已执行过退还款项分配");
        }
        List<PayApplyDto> payApplyDtoList = payApplyApiService.getListByPayTypeAndRelatedId(PayApplyTypeEnum.AFTER_SALES.getCode(), afterSalesId);
        if (CollUtil.isNotEmpty(payApplyDtoList) &&
                payApplyDtoList.stream().anyMatch(payApplyDto -> payApplyDto.getValidStatus() == 0 || payApplyDto.getValidStatus() == 1)) {
            throw new ServiceException("存在待审核或已审核付款申请，不可执行该操作");
        }
    }

    public void detailRefundCheck(Integer afterSalesId) {
        AfterSales afterSales = afterSalesMapper.getAfterSalesById(afterSalesId);
        if (Objects.isNull(afterSales)) {
            throw new ServiceException("售后单不存在");
        }
        Integer atferSalesStatus = afterSales.getAtferSalesStatus();
        if (Objects.isNull(atferSalesStatus) || ErpConstant.ZERO.equals(atferSalesStatus)) {
            throw new ServiceException("单据状态为" + NewAfterSalesStatusEnum.getDescByCode(atferSalesStatus) + "，不可执行该操作");
        }
        Integer amountRefundStatus = afterSales.getAmountRefundStatus();
//        if (Objects.isNull(amountRefundStatus)) {
//            throw new ServiceException("请先执行退款运算");
//        }
        AfterSalesDetailEntity afterSalesDetail = newAfterSalesDetailMapper.findByAfterSalesId(afterSalesId);
        if (Objects.isNull(afterSalesDetail)) {
            throw new ServiceException("售后单详情不存在");
        }
        Integer isCalRefund = afterSalesDetail.getIsCalRefund();
        if (!ErpConstant.ONE.equals(isCalRefund)) {
            throw new ServiceException("请先执行退款运算");
        }
        BigDecimal refundAmount = afterSalesDetail.getPublicRefundAmount().add(afterSalesDetail.getPrivateRefundAmount());
        if (refundAmount.compareTo(BigDecimal.ZERO) == 0) {
            throw new ServiceException("请先执行退还款项分配");
        }
//        List<PayApplyDto> payApplyDtoList = payApplyApiService.getListByPayTypeAndRelatedId(PayApplyTypeEnum.AFTER_SALES.getCode(), afterSalesId);
//        if (CollUtil.isEmpty(payApplyDtoList) ||
//                payApplyDtoList.stream().noneMatch(payApplyDto -> payApplyDto.getValidStatus() == 0 || payApplyDto.getValidStatus() == 1)) {
//            throw new ServiceException("没有待审核或已审核付款申请，不可执行该操作");
//        }
    }
    
}
