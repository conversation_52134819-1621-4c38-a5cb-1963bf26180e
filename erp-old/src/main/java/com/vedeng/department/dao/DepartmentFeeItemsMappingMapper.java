package com.vedeng.department.dao;

import com.vedeng.department.model.DepartmentFeeItemsMapping;
import com.vedeng.department.model.DepartmentsHospital;

import java.util.List;
import java.util.Map;

public interface DepartmentFeeItemsMappingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_DEPARTMENT_FEE_ITEMS_MAPPING
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    int deleteByPrimaryKey(Integer departmentFeeItemsMappingId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_DEPARTMENT_FEE_ITEMS_MAPPING
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    int insert(DepartmentFeeItemsMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_DEPARTMENT_FEE_ITEMS_MAPPING
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    int insertSelective(DepartmentFeeItemsMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_DEPARTMENT_FEE_ITEMS_MAPPING
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    DepartmentFeeItemsMapping selectByPrimaryKey(Integer departmentFeeItemsMappingId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_DEPARTMENT_FEE_ITEMS_MAPPING
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    int updateByPrimaryKeySelective(DepartmentFeeItemsMapping record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_DEPARTMENT_FEE_ITEMS_MAPPING
     *
     * @mbg.generated Tue Apr 09 19:02:47 CST 2019
     */
    int updateByPrimaryKey(DepartmentFeeItemsMapping record);

    /**
     * 收费项目
     * <p>Title: getDepartmentsHospitalByParam</p>  
     * <p>Description: </p>  
     * @param paramMap
     * @return  
     * <AUTHOR>
     * @date 2019年4月12日
     */
	List<DepartmentsHospital> getDepartmentsHospitalByParam(Map<String, Object> paramMap);

	/**
	 * @description 添加收费项目
	 * <AUTHOR>
	 * @param
	 * @date 2019/4/16
	 */
    Integer insertDepartmentFeeItemsInfos(Map<String, Object> paramMap);

    /**
     * @description 删除科室信息中间表
     * <AUTHOR>
     * @param
     * @date 2019/4/28
     */
    Integer deleteFeeItemsMappingsById(DepartmentsHospital departmentsHospital);

}