package com.vedeng.order.model.vo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2025/2/18
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TaskDetailVo {

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 任务明细ID
     */
    private Long taskItemId;

    /**
     * 任务的内容
     */
    private String taskContent;

    /**
     * 业务单号
     */
    private String bizNo;

    /**
     * 处理状态：0待处理 1已处理 2关闭'
     */
    private Integer doneStatus;

}
