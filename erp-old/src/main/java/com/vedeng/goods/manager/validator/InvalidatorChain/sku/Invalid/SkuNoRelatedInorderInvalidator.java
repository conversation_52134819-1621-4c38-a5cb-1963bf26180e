package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.wms.dao.WmsInputOrderMapper;
import com.wms.model.po.WmsInputOrder;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.*;


/**
 * .(8)	该SKU没有（审核状态为待审核，审核中，审核通过，且入库状态为未入库，部分入库）的盘盈入库单；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:01.
 * @author: <PERSON><PERSON>
 */
@Service
public class SkuNoRelatedInorderInvalidator extends GoodsInvalidatorChain {

    @Resource
    WmsInputOrderMapper wmsInputOrderMapper;

    @Override
    public void doInvalid() {
        String skuNo = getSkuNo();
        List<WmsInputOrder> wmsInputOrderList = wmsInputOrderMapper.getValidAndUnInInpuorderBySkuNos(skuNo);
        if(CollectionUtils.isNotEmpty(wmsInputOrderList)){
            String collect = wmsInputOrderList.stream().map(e -> e.getOrderNo()).collect(Collectors.joining(","));
            String orderNo = SkuNoRelatedInorderInvalidatorError.getMessage().replace("orderNo", collect);
            throw new InvalidatorChainException(SkuNoRelatedInorderInvalidatorError.getCode(),orderNo);
        }
    }
}
