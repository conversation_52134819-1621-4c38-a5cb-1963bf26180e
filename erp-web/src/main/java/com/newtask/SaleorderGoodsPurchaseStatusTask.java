package com.newtask;

import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.WarehouseOperateDataDto;
import com.report.dao.SaleorderDataSelectMapper;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.service.BaseSaleOrderService;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.saleorder.task
 * @Date 2021/12/13 17:03
 */
@JobHandler(value = "SaleorderGoodsPurchaseStatusTask")
@Component
public class SaleorderGoodsPurchaseStatusTask extends AbstractJobHandler {


    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    @Resource
    SaleorderDataMapper saleorderDataMapper;
    @Resource
    SaleorderDataSelectMapper saleorderDataSelectMapper;

    @SuppressWarnings("all")
    @Override
    public ReturnT<String> doExecute(String time) throws Exception {

        //根据时间加载数据
        Long startTime = System.currentTimeMillis() - 15 * 60 * 1000;
        Long endTime = System.currentTimeMillis();

        XxlJobLogger.log(new Date(startTime) + "--" + new Date(endTime));

        List<Integer> buyStatusChangedGoods = new ArrayList<>();

        List<Integer> buyStatusChangedGoodsOne = saleorderDataMapper.getBuyStatusChangedGoodsFirstQuery(startTime, endTime);

        if (CollectionUtils.isNotEmpty(buyStatusChangedGoodsOne)) {
            buyStatusChangedGoods.addAll(buyStatusChangedGoodsOne);
        }

        List<Integer> buyStatusChangedGoodsTwo = getBuyStatusChangedGoodsSecondQuery(startTime);

        if (CollectionUtils.isNotEmpty(buyStatusChangedGoodsTwo)) {
            buyStatusChangedGoods.addAll(buyStatusChangedGoodsTwo);
        }

        if (CollectionUtils.isNotEmpty(buyStatusChangedGoods)) {

            long nowTime = System.currentTimeMillis();
            List<SaleorderGoodsVo> saleorderGoodsVoList = new ArrayList<>();
            buyStatusChangedGoods.forEach(item -> {
                SaleorderGoodsVo saleorderGoodsVo = new SaleorderGoodsVo();
                SaleorderGoodsVo saleorderGoodsInfo = saleorderDataMapper.getSaleorderGoodsInfoById(item);
                if (!Objects.isNull(saleorderGoodsInfo)) {
                    saleorderGoodsVo.setSaleorderGoodsId(item);
                    saleorderGoodsVo.setNum(saleorderGoodsInfo.getNum());
                    saleorderGoodsVo.setIsNew(saleorderGoodsInfo.getIsNew());
                    saleorderGoodsVoList.add(saleorderGoodsVo);
                }
            });
            if (CollectionUtils.isNotEmpty(saleorderGoodsVoList)) {
                List<SaleorderGoods> saleorderGoodsList = baseSaleOrderService.calculateGoodsBuyStatus(saleorderGoodsVoList);
                XxlJobLogger.log("当前业务数据量：" + saleorderGoodsList.size());
                if (CollectionUtils.isNotEmpty(saleorderGoodsList)){
                    saleorderDataMapper.updateSaleorderGoodsBuyStatus(saleorderGoodsList, nowTime);
                }
            }
        }
        return SUCCESS;
    }


    private List<Integer> getBuyStatusChangedGoodsSecondQuery(long startTime) {

        //初始500条数据
        int num = 500;
        List<Integer> warehouseGoodsOperateChangedData = getWarehouseGoodsOperateChangedData(num, startTime);
        if(CollectionUtils.isEmpty(warehouseGoodsOperateChangedData)){
            return Collections.emptyList();
        }
        return saleorderDataSelectMapper.getBuyStatusChangedGoodsSecondQuery(warehouseGoodsOperateChangedData);
    }


    private List<Integer> getWarehouseGoodsOperateChangedData(int num, long startTime) {

        //按照id降序，获取num条数据
        List<WarehouseOperateDataDto> warehouseGoodsOperateChangedData = saleorderDataSelectMapper.getWarehouseGoodsOperateChangedData(num);

        int originalDataSize = warehouseGoodsOperateChangedData.size();

        //按照开始时间过滤出满足条件的数据

        List<WarehouseOperateDataDto> resultData = warehouseGoodsOperateChangedData.stream().filter(item -> item.getAddTime() > startTime).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(resultData)) {
            return null;
        }

        int resultDataSize = resultData.size();

        if (originalDataSize != resultDataSize) {
            return resultData.stream().map(WarehouseOperateDataDto::getWarehouseGoodsOperateLogId).collect(Collectors.toList());
        }

        return getWarehouseGoodsOperateChangedData(num * 2, startTime);
    }

}
