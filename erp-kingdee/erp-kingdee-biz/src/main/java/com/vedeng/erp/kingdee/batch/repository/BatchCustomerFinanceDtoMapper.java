package com.vedeng.erp.kingdee.batch.repository;

import com.vedeng.erp.kingdee.batch.dto.BatchCustomerFinanceDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.kingdee.batch.mapper
 * @Date 2022/12/5 13:20
 */
public interface BatchCustomerFinanceDtoMapper {

    /**
     * 查询审计客户信息
     *
     * @param queryDto BatchCustomerFinanceDto
     * @return List<BatchCustomerFinanceDto>
     */
    List<BatchCustomerFinanceDto> findByAll(BatchCustomerFinanceDto queryDto);

    /**
     * 更新审计客户推送金蝶状态
     *
     * @param traderCustomerFinanceId traderCustomerFinanceId
     */
    void updateKingDeePushStatus(@Param("traderCustomerFinanceId") Integer traderCustomerFinanceId);

    /**
     * 根据TraderId查询CustomerId
     * @param traderId
     * @return
     */
    BatchCustomerFinanceDto getCustomerIdByTraderId(@Param("traderId") Integer traderId);

    /**
     * 查询客户补偿数据
     */
    List<BatchCustomerFinanceDto> queryCustomerCompensate(BatchCustomerFinanceDto queryDto);
}
