<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/vue/vd-ui/ui.css">


<div class="page-wrap" id="page-container">
    <div class="page-container">
        <div class="page-main">
            <div class="list-title">场景方案</div>
            <div class="list-top-option">
                <ui-button type="primary" icon="icon-add" @click="gotoAdd">新建方案</ui-button>
            </div>
            <ui-search-container 
                ref="listContainer" 
                :headers="tableHeaders" 
                url="/sku/scene/page.do" 
                :search-params="searchParams" 
                :can-choose="false" 
                :is-list-sort="true" 
                :list-option="false" 
                :need-custom="false" 
                :need-setting="false"
            >
                <template v-slot:filter-list>
                    <ui-search-item label="编号">
                        <ui-input v-model="searchParams.sceneNo"></ui-input>
                    </ui-search-item>
                    <ui-search-item label="场景名称">
                        <ui-input v-model="searchParams.name"></ui-input>
                    </ui-search-item>
                    <ui-search-item label="创建时间">
                        <ui-date-picker
                            v-model="searchParams.filterAddTime"
                            type="daterange"
                            start-placeholder="起始时间"
                            end-placeholder="截止时间"
                            @input="handlerFilterDateChange('create', $event)"
                        ></ui-date-picker>
                    </ui-search-item>
                    <ui-search-item label="创建人">
                        <ui-select :remote="true" :avatar="true" multiple-type="fixed" placeholder="全部" v-model="searchParams.createUserList" clearable :remote-info="createrListRemoteInfo"></ui-select>
                    </ui-search-item>
                    <ui-search-item label="编辑时间">
                        <ui-date-picker
                            v-model="searchParams.filterModTime"
                            type="daterange"
                            start-placeholder="起始时间"
                            end-placeholder="截止时间"
                            @input="handlerFilterDateChange('modify', $event)"
                        ></ui-date-picker>
                    </ui-search-item>
                    <ui-search-item label="编辑人">
                        <ui-select :remote="true" :avatar="true" multiple-type="fixed" placeholder="全部" v-model="searchParams.modifyUserList" clearable :remote-info="updaterListRemoteInfo"></ui-select>
                    </ui-search-item>
                    <ui-search-item label="状态">
                        <ui-select placeholder="全部" :data="statusList" v-model="searchParams.status" clearable></ui-select>
                    </ui-search-item>
                </template>
                <template v-slot:sceneno="{ row }">
                    <span class="text-line-1 td-link" @click="gotoDetail(row.id)">
                        {{ row.sceneNo }}
                    </span>
                </template>
                <template v-slot:count="{ row }">
                    <span class="text-line-1">
                        {{ row.errorCount }} / {{ row.productCount }}
                    </span>
                </template>
                <template v-slot:creator="{ row }">
                    <span class="text-line-1" :title="row.addTime + ' / ' + row.creatorName">
                        {{ row.addTime }} / {{ row.creatorName }}
                    </span>
                </template>
                <template v-slot:updater="{ row }">
                    <span class="text-line-1" :title="row.modTime + ' / ' + row.updaterName">
                        {{ row.modTime }} / {{ row.updaterName }}
                    </span>
                </template>
                <template v-slot:status="{ row }">
                    <span :class="row.status == 0 ? 'green' : 'red'">
                        {{ ['已上架', '已下架'][row.status] }}
                    </span>
                </template>
                <template v-slot:sort="{ row }">
                    <div class="list-sort-wrap">
                        <ui-input v-model="row.sort" @input="forceNum(row)" @blur="hanlderSortChange(row, $event)"></ui-input>
                    </div>
                </template>
                <template v-slot:option="{ row }">
                    <div class="option-wrap">
                        <a class="table-edit" @click="gotoDetail(row.id)">查看</a>
                        <a class="table-edit" @click="gotoEdit(row.id)">编辑</a>
                        <div class="table-edit-more">
                            <div class="table-edit-more-label">
                                <div class="edit-more-txt">更多</div>
                                <i class="vd-ui_icon icon-down"></i>
                            </div>
                            <div class="table-edit-more-drop">
                                <div class="table-edit-more-btn" @click="updateScene(row.id, 1)" v-if="row.status === 0">下架</div>
                                <div class="table-edit-more-btn" @click="updateScene(row.id, 0)" v-else>上架</div>
                                <div class="table-edit-more-btn" @click="copyScene(row.id)">复制</div>
                                <div class="table-edit-more-btn" @click="exportScene(row.id)">导出</div>
                            </div>
                        </div>
                    </div>
                </template>
            </ui-search-container>
        </div>
    </div>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js"></script>
<script src="${pageContext.request.contextPath}/static/js/scene/list.js"></script>
