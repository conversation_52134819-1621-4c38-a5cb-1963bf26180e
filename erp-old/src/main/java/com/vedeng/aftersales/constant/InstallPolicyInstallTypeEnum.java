package com.vedeng.aftersales.constant;

import com.pricecenter.constant.VerifyStatusEnum;

public enum InstallPolicyInstallTypeEnum {

    CHARGE(0, "收费安装"),

    FREE(1, "免费安装"),

    NONE(2, "不可安装");

    private Integer type;

    private String message;

    InstallPolicyInstallTypeEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }

    public static String getNameByValue(int findValue){

        String name = "";

        for(InstallPolicyInstallTypeEnum enumItem : InstallPolicyInstallTypeEnum.values()){
            if(enumItem.getType() == findValue){
                name = enumItem.getMessage();
                break;
            }
        }

        return name;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
