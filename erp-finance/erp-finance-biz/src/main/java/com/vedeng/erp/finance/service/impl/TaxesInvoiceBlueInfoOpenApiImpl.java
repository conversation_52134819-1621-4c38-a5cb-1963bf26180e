package com.vedeng.erp.finance.service.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.erp.finance.domain.dto.InvoiceApplyDetailXhDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceBlueInfoRequestDto;
import com.vedeng.erp.finance.domain.dto.SaleInvoiceBlueInfoResponseDto;
import com.vedeng.erp.finance.dto.InvoiceDto;
import com.vedeng.erp.finance.service.AbstractTaxesOpenApiHandler;
import com.vedeng.erp.finance.service.InvoiceApplyDetailXhService;
import com.vedeng.erp.finance.service.InvoiceService;
import com.vedeng.infrastructure.taxes.base.ITaxesParam;
import com.vedeng.infrastructure.taxes.base.ITaxesResult;
import com.vedeng.infrastructure.taxes.domain.TaxesOpenApiResult;
import com.vedeng.infrastructure.taxes.enums.TaxesReturnCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 销项票-蓝字发票信息
 */
@Slf4j
@Service
public class TaxesInvoiceBlueInfoOpenApiImpl extends AbstractTaxesOpenApiHandler {
    
    @Autowired
    private InvoiceApplyDetailXhService invoiceApplyDetailXhService;
    @Autowired
    private InvoiceService invoiceService;

    /**
     * 后置处理
     * @param taxesParam
     * @param executeResult
     * @return
     */
    @Override
    protected ITaxesResult afterExecute(ITaxesParam taxesParam, ITaxesResult executeResult) {
        TaxesOpenApiResult openapi = (TaxesOpenApiResult) executeResult;
        String decodeStr = new String(Base64Utils.decodeFromString(openapi.getData()));
        SaleInvoiceBlueInfoResponseDto taxesResult = JSONObject.parseObject(decodeStr, SaleInvoiceBlueInfoResponseDto.class);
        if (Objects.isNull(taxesResult)){
            taxesResult =  new SaleInvoiceBlueInfoResponseDto();
        }
        TaxesOpenApiResult.ReturnInfo return_info = openapi.getReturn_info();
        taxesResult.setReturnCode(return_info.getReturn_code());
        taxesResult.setReturnMessage(StrUtil.isNotEmpty(return_info.getReturn_message()) ? return_info.getReturn_message() : TaxesReturnCodeEnum.getMsg(return_info.getReturn_code()));
        if (TaxesReturnCodeEnum.SUCCESS.getCode().equals(return_info.getReturn_code())){
            taxesResult.setIsSuccess(Boolean.TRUE);
        }
        
        SaleInvoiceBlueInfoRequestDto saleInvoiceBlueInfoRequestDto = (SaleInvoiceBlueInfoRequestDto) taxesParam;
        InvoiceDto invoiceDto = invoiceService.findByInvoiceNo(saleInvoiceBlueInfoRequestDto.getFphm());
        if (Objects.isNull(invoiceDto)){
            log.info("开票后置处理结果：{}",JSONObject.toJSON(taxesResult));
            return taxesResult;
        }
        List<InvoiceApplyDetailXhDto> xhDto = invoiceApplyDetailXhService.getByApplyId(invoiceDto.getInvoiceApplyId());
        if (Objects.isNull(xhDto)){
            log.info("开票后置处理结果：{}",JSONObject.toJSON(taxesResult));
            return taxesResult;
        }
        Map<Integer, InvoiceApplyDetailXhDto> map = xhDto.stream().collect(Collectors.toMap(InvoiceApplyDetailXhDto::getXh, Function.identity(),(key1, key2) -> key1));
        if (MapUtil.isEmpty(map)){
            log.info("开票后置处理结果：{}",JSONObject.toJSON(taxesResult));
            return taxesResult;
        }
        List<SaleInvoiceBlueInfoResponseDto.Mxzb> mxzbList = taxesResult.getMxzbList();
        
        for (SaleInvoiceBlueInfoResponseDto.Mxzb mxzb : mxzbList) {
            InvoiceApplyDetailXhDto dto = map.get(Integer.valueOf(mxzb.getXh()));
            if (Objects.nonNull(dto)){
                mxzb.setXh(String.valueOf(dto.getInvoiceApplyDetailId()));
            }
        }
        log.info("开票后置处理结果：{}",JSONObject.toJSON(taxesResult));
        return taxesResult;
    }
}
