package com.vedeng.erp.system.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 业务流转单明细表
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class FlowOrderDetailDto extends BaseDto {
    /**
     * 主键
     */
    private Long flowOrderDetailId;

    /**
     * 关联业务流转单ID
     */
    private Long flowOrderId;

    /**
     * 商品ID
     */
    private Integer skuId;

    /**
     * 商品编号
     */
    private String skuNo;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 单位
     */
    private String unit;

    /**
     * 规格/型号
     */
    private String model;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 发货/收货数量
     */
    private Integer deliveryQuantity;

    /**
     * 节点金额信息
     */
    private List<FlowNodeOrderDetailPriceDto> flowNodeOrderDetailPriceDtoList;



}
