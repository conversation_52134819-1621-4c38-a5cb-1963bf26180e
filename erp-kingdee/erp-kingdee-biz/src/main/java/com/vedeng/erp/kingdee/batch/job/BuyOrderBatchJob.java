package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.tasklet.BuyOrderInvoiceWarehouseInTasklet;
import com.vedeng.erp.kingdee.batch.tasklet.BuyOrderInvoiceWarehouseOutTasklet;
import com.vedeng.erp.kingdee.batch.writer.*;
import com.vedeng.erp.kingdee.dto.KingDeeFileDataDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonAndInvoiceDto;
import com.vedeng.erp.kingdee.dto.KingDeePayCommonDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 * @version 1.0
 * @description: 采购单正向流程job
 * @date 2022/10/25 16:00
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class BuyOrderBatchJob extends BaseJob {


    @Autowired
    private PurchaseInProcessor purchaseInProcessor;
    @Autowired
    private PurchaseInWriter purchaseInWriter;
    @Autowired
    private BatchPurchaseInvoicePayCommonProcessor batchPurchaseInvoicePayCommonProcessor;
    @Autowired
    private BatchPurchaseInvoicePayCommonWriter batchPurchaseInvoicePayCommonWriter;
    @Autowired
    private BatchPurchaseInvoiceWriter batchPurchaseInvoiceWriter;
    @Autowired
    private BatchPurchaseInvoiceVatProcessor batchPurchaseInvoiceVatProcessor;

    @Autowired
    private BuyOrderInvoiceWarehouseInTasklet buyOrderInvoiceWarehouseInTasklet;
    @Autowired
    private BuyOrderInvoiceWarehouseOutTasklet buyOrderInvoiceWarehouseOutTasklet;

    @Autowired
    private BatchPurchaseInAcceptanceFormProcessor batchPurchaseInAcceptanceFormProcessor;

    @Autowired
    private BatchPurchaseInvoiceCompatibleExpenseProcessor batchPurchaseInvoiceCompatibleExpenseProcessor;

    @Autowired
    private BatchPurchaseInvoiceExpensePayCommonProcessor batchPurchaseInvoiceExpensePayCommonProcessor;

    @Autowired
    private BatchExpensePayableOrderWriterService batchExpensePayableOrderWriterService;

    @Autowired
    private BatchPurchaseInvoiceExpenseProcessor batchPurchaseInvoiceExpenseProcessor;

    @Autowired
    private BatchExpensePayableOrderInvoiceWriterService batchExpensePayableOrderInvoiceWriterService;

    @Autowired
    private CommonFileDataWriter commonFileDataWriter;

    @Autowired
    private VirtualInvoiceBlueProcessor virtualInvoiceBlueProcessor;

    @Autowired
    private VirtualInvoiceRedProcessor virtualInvoiceRedProcessor;

    @Autowired
    private VirtualInvoiceWriter virtualInvoiceWriter;

    @Autowired
    private VirtualInvoiceBindProcessor virtualInvoiceBindProcessor;

    @Autowired
    private VirtualInvoiceBindWriter virtualInvoiceBindWriter;

    @Autowired
    private BatchBuyOrderVirtualInvoicePayCommonProcessor batchBuyOrderVirtualInvoicePayCommonProcessor;

    @Autowired
    private BatchBuyOrderVirtualInvoiceVatProcessor batchBuyOrderVirtualInvoiceVatProcessor;



    public Job buyOrderFlowJob() {
        return jobBuilderFactory.get("采购单正向流程job")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(virtualInvoiceIn())
                .next(virtualInvoiceOut())
                .next(virtualInvoiceBlueBind())
                .next(virtualInvoiceRedBind())
                .next(buyOrderInvoiceWarehouseInTasklet())
                .next(buyOrderInvoiceWarehouseOutTasklet())
                .next(purchaseIn())
                .next(acceptanceForm())
                .next(invoicePayCommon())
                .next(invoice())
                .next(virtualInvoicePayCommon())
                .next(virtualInvoice())
                .next(invoiceExpensePayCommon())
                .next(invoiceExpense())
                .build();
    }



    /**
     * 采购蓝票生成虚拟蓝票
     */
    private Step virtualInvoiceIn() {
        return stepBuilderFactory.get("采购蓝票生成虚拟蓝票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchSettlementBillDto, List<BatchVirtualInvoiceDto>>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceBlueReader(null, null))
                .processor(virtualInvoiceBlueProcessor)
                .writer(virtualInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 采购蓝票生成虚拟红票
     */
    private Step virtualInvoiceOut() {
        return stepBuilderFactory.get("采购蓝票生成虚拟红票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchSettlementBillDto, List<BatchVirtualInvoiceDto>>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceRedReader(null, null))
                .processor(virtualInvoiceRedProcessor)
                .writer(virtualInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step virtualInvoiceBlueBind() {
        return stepBuilderFactory.get("采购虚拟蓝票绑定货票关系")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchVirtualInvoiceDto, List<BatchRVirtualInvoiceJWarehouseDto>>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceBlueBindReader(null, null))
                .processor(virtualInvoiceBindProcessor)
                .writer(virtualInvoiceBindWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step virtualInvoiceRedBind() {
        return stepBuilderFactory.get("采购虚拟红票绑定货票关系")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchVirtualInvoiceDto, List<BatchRVirtualInvoiceJWarehouseDto>>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceRedBindReader(null, null))
                .processor(virtualInvoiceBindProcessor)
                .writer(virtualInvoiceBindWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    public Job buyOrderOnlyInJob() {
        return jobBuilderFactory.get("采购仅推送入库")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(purchaseIn())
                .build();
    }


    /**
     * 采购蓝票和入库关系绑定
     */
    private Step buyOrderInvoiceWarehouseInTasklet() {
        return stepBuilderFactory.get("采购蓝票和入库关系绑定")
                .tasklet(buyOrderInvoiceWarehouseInTasklet)
                .build();
    }

    /**
     * 采购红票和出库关系绑定
     */
    private Step buyOrderInvoiceWarehouseOutTasklet() {
        return stepBuilderFactory.get("采购红票和出库关系绑定")
                .tasklet(buyOrderInvoiceWarehouseOutTasklet)
                .build();
    }


    private Step purchaseIn() {
        return stepBuilderFactory.get("采购入库单推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchWarehouseGoodsOutInDto, KingDeePurchaseReceiptDto>chunk(1)
                // 捕捉到异常就重试,重试3次还是异常,JOB就停止并标志失败
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(purchaseInReader(null, null))
                .processor(purchaseInProcessor)
                .writer(purchaseInWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step acceptanceForm() {
        return stepBuilderFactory.get("采购入库单附件推送")
                .<BatchWarehouseGoodsOutInDto, KingDeeFileDataDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(purchaseInReader(null, null))
                .processor(batchPurchaseInAcceptanceFormProcessor)
                .writer(commonFileDataWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step invoicePayCommon() {
        return stepBuilderFactory.get("采购应付单推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, KingDeePayCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(purchaseBlueEnableInvoiceDtoItemReader(null, null))
                .processor(compositeItemInvoicePayProcessor())
                .writer(batchPurchaseInvoicePayCommonWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step invoice() {
        return stepBuilderFactory.get("采购蓝票推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(purchaseBlueEnableInvoiceDtoItemReader(null, null))
                .processor(compositeItemInvoiceProcessor())
                .writer(batchPurchaseInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step invoiceExpensePayCommon() {
        return stepBuilderFactory.get("采购Excel费用应付单推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(purchaseBlueEnableInvoiceDtoItemReader(null, null))
                .processor(compositeItemExcelPayableProcessor())
                .writer(batchExpensePayableOrderWriterService)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }


    private Step virtualInvoicePayCommon() {
        return stepBuilderFactory.get("采购虚拟应付单推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchVirtualInvoiceDto, KingDeePayCommonDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceBlueBindReader(null, null))
                .processor(batchBuyOrderVirtualInvoicePayCommonProcessor)
                .writer(batchPurchaseInvoicePayCommonWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    private Step virtualInvoice() {
        return stepBuilderFactory.get("采购虚拟蓝票推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchVirtualInvoiceDto, KingDeePayCommonAndInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(virtualInvoiceBlueBindReader(null, null))
                .processor(batchBuyOrderVirtualInvoiceVatProcessor)
                .writer(batchPurchaseInvoiceWriter)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }



    private Step invoiceExpense() {
        return stepBuilderFactory.get("采购excel费用蓝票推送")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchPayExpensesDto>chunk(1)
                .faultTolerant()
                .retryLimit(3)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(purchaseBlueEnableInvoiceDtoItemReader(null, null))
                .processor(compositeItemExcelInvoiceProcessor())
                .writer(batchExpensePayableOrderInvoiceWriterService)
                .listener(baseProcessListener)
                .listener(baseReadListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 标识excel内匹配的数据
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeePayCommonDto> compositeItemInvoicePayProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeePayCommonDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(CollUtil.newArrayList(batchPurchaseInvoiceCompatibleExpenseProcessor, batchPurchaseInvoicePayCommonProcessor));
        return compositeItemProcessor;
    }


    /**
     * 票
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto> compositeItemInvoiceProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeePayCommonAndInvoiceDto> compositeItemProcessor = new CompositeItemProcessor<>();

        compositeItemProcessor.setDelegates(Arrays.asList(batchPurchaseInvoiceCompatibleExpenseProcessor, batchPurchaseInvoiceVatProcessor));
        return compositeItemProcessor;
    }


    /**
     * 标识excel内匹配的数据 应付
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, BatchPayExpensesDto> compositeItemExcelPayableProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, BatchPayExpensesDto> compositeItemProcessor = new CompositeItemProcessor<>();

        compositeItemProcessor.setDelegates(Arrays.asList(batchPurchaseInvoiceCompatibleExpenseProcessor, batchPurchaseInvoiceExpensePayCommonProcessor));
        return compositeItemProcessor;
    }

    /**
     * 标识excel内匹配的数据 票
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, BatchPayExpensesDto> compositeItemExcelInvoiceProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, BatchPayExpensesDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchPurchaseInvoiceCompatibleExpenseProcessor, batchPurchaseInvoiceExpenseProcessor));
        return compositeItemProcessor;
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSettlementBillDto> virtualInvoiceBlueReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchSettlementBillDto query = BatchSettlementBillDto.builder()
                .sourceType("buyOrder")
//                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
//                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .isDelete(0).build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSettlementBillDto.class.getSimpleName(), "queryBuyOrderRebate", query);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchVirtualInvoiceDto> virtualInvoiceBlueBindReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchVirtualInvoiceDto query = BatchVirtualInvoiceDto.builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .colorType(2)
                .businessType(1)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchVirtualInvoiceDto.class.getSimpleName(), query);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchVirtualInvoiceDto> virtualInvoiceRedBindReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchVirtualInvoiceDto query = BatchVirtualInvoiceDto.builder()
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .colorType(1)
                .businessType(2)
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchVirtualInvoiceDto.class.getSimpleName(), query);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchSettlementBillDto> virtualInvoiceRedReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchSettlementBillDto query = BatchSettlementBillDto.builder()
                .sourceType("buyOrderAfterSale")
//                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
//                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .isDelete(0).build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchSettlementBillDto.class.getSimpleName(), "queryBuyOrderAfterRebate", query);
    }


    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchWarehouseGoodsOutInDto> purchaseInReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime
    ) {
        BatchWarehouseGoodsOutInDto warehouseGoodsOutInDto = BatchWarehouseGoodsOutInDto
                .builder()
                .outInTypeList(CollUtil.newArrayList(1))
                .isDelete(0)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(beginTime))
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()) : DateUtil.parseDateTime(endTime))
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchWarehouseGoodsOutInDto.class.getSimpleName(), "purchaseInFindByAll", warehouseGoodsOutInDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> purchaseBlueEnableInvoiceDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                           @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 采购开票
                .type(503)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "buyorderBlueEnableInvoicefindByAll", batchInvoiceDto);
    }

}

