package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.rabbitmq.ErpMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.common.constant.*;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.saleorder.dao.OnlineInvoiceOpenMapper;
import com.vedeng.erp.saleorder.dao.OnlineSignatureMapper;
import com.vedeng.erp.saleorder.exception.OnlineInvoiceOpenException;
import com.vedeng.erp.saleorder.model.dto.*;
import com.vedeng.erp.saleorder.model.po.ExpressOnlineReceiptRecordPo;
import com.vedeng.erp.saleorder.service.OnlineInvoiceOpenService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.InvoiceApply;
import com.vedeng.finance.model.InvoiceApplyDetail;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.InvoiceService;
import com.vedeng.goods.dao.VerifiesInfoGenerateMapper;
import com.vedeng.goods.model.VerifiesInfoGenerate;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.dao.OutboundBatchesRecodeMapper;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.OutboundBatchesRecode;
import com.vedeng.logistics.service.ConfirmationFormRecodeService;
import com.vedeng.order.api.constant.InvoiceErrorMessageEnum;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.dao.SaleorderModifyApplyGoodsMapper;
import com.vedeng.order.dao.SaleorderModifyApplyMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.SaleorderModifyApply;
import com.vedeng.order.model.SaleorderModifyApplyGoods;
import com.vedeng.order.model.vo.ExpressOnlineReceiptVo;
import com.vedeng.order.model.vo.ExpressSkuDataVo;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.dao.AttachmentMapper;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.model.vo.UrlToPdfParam;
import com.vedeng.system.service.OssUtilsService;
import com.vedeng.system.service.RegionService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.dao.TraderAddressMapper;
import com.vedeng.trader.dao.TraderFinanceMapper;
import com.vedeng.trader.dao.WebAccountMapper;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderFinance;
import com.vedeng.trader.model.WebAccount;
import com.vedeng.trader.service.TraderCustomerService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 在线开票服务层
 *
 * <AUTHOR>
 */
@Service
public class OnlineInvoiceOpenServiceImpl implements OnlineInvoiceOpenService {

    public static final Logger logger = LoggerFactory.getLogger(OnlineInvoiceOpenServiceImpl.class);

    @Autowired
    private SaleorderService saleorderService;

    @Autowired
    ErpMsgProducer erpMsgProducer;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private UserService userService;

    @Autowired
    private RegionService regionService;

    @Resource
    private SaleorderModifyApplyMapper saleorderModifyApplyMapper;

    @Resource
    private SaleorderModifyApplyGoodsMapper saleorderModifyApplyGoodsMapper;

    @Resource
    private OnlineInvoiceOpenMapper onlineInvoiceOpenMapper;

    @Resource
    private SaleorderMapper saleorderMapper;

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private OrderNoDict orderNoDict;

    @Resource
    private TraderFinanceMapper traderFinanceMapper;

    @Resource
    private OnlineSignatureMapper onlineSignatureMapper;

    @Resource
    private WebAccountMapper webAccountMapper;

    @Resource
    private ExpressMapper expressMapper;
    @Autowired
    private CapitalBillService capitalBillService;
    @Resource
    private TraderAddressMapper traderAddressMapper;

    @Resource
    private VerifiesInfoGenerateMapper verifiesInfoGenerateMapper;

    @Autowired
    private OutboundBatchesRecodeMapper outboundBatchesRecodeMapper;

    @Autowired // 自动装载
    protected ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;
    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private ConfirmationFormRecodeService confirmationFormRecodeService;

    @Autowired
    private OssUtilsService ossUtilsService;

    @Value("${oss_http}")
    private String ossHttp;

    @Value("${html2Pdf.domain}")
    private String html2PdfDomain;
    private static final double SCALE = 0.6;
    private static final String RENDER_URL = "/api/render";

    @Autowired
    private AttachmentMapper attachmentMapper;


    /**
     * 销售订单(账期类型)付款计划
     */
    public static final List PAYMENT_TYPE_FOR_PAYMENT_DAYS = Collections.unmodifiableList(Lists.newArrayList(
            OrderConstant.PREPAY_80_PERCENT,
            OrderConstant.PREPAY_50_PERCENT ,
            OrderConstant.PREPAY_30_PERCENT,
            OrderConstant.PREPAY_0_PERCENT));

    /**
     * 开票前的数据处理
     *
     * @param invoiceOpenInfoDto
     * @throws Exception
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealDataBeforeInvoiceApply(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception {
        //前置校验部分
        validRecentBusinessCondition(invoiceOpenInfoDto);

        //1.订单开票信息处理
        dealGenerateOrderModify(invoiceOpenInfoDto);

        //2.客户资质信息处理
        dealCustomerInvoiceInfo(invoiceOpenInfoDto);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void dealInvoiceOpenBusiness(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception {
        logger.info("dealInvoiceOpenBusiness invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderId(invoiceOpenInfoDto.getOrderNo());
        logger.info("在线开票业务开始生成开票申请 saleOrderInfo:{}", JSON.toJSONString(saleOrderInfo));

        InvoiceApply invoiceApply = new InvoiceApply();
        invoiceApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
        invoiceApply.setType(SysOptionConstant.ID_505);
        invoiceApply.setRelatedId(saleOrderInfo.getSaleorderId());
        invoiceApply.setIsAdvance(ErpConst.ZERO);
        //决定是否提前开票
//        decideIsAdvance(saleOrderInfo, invoiceApply);
        invoiceApply.setYyValidStatus(ErpConst.ONE);
        invoiceApply.setAddTime(System.currentTimeMillis());
        invoiceApply.setCreator(ErpConst.NJ_ADMIN_ID);
        invoiceApply.setModTime(System.currentTimeMillis());
        invoiceApply.setUpdater(ErpConst.NJ_ADMIN_ID);
        //invoiceApply.setIsAuto(invoiceOpenInfoDto.getOrderInvoiceInfo().getInvoiceMethod());
        //if (Integer.valueOf(4).equals(invoiceApply.getIsAuto())) {
        //    invoiceApply.setInvoiceProperty(ErpConst.THREE);
        //}else if (ErpConst.THREE.equals(invoiceApply.getIsAuto())){
        //    invoiceApply.setInvoiceProperty(ErpConst.TWO);
        //}else {
        //    invoiceApply.setInvoiceProperty(ErpConst.ONE);
        //}
        invoiceApply.setIsAuto(ErpConst.FOUR);
        invoiceApply.setInvoiceProperty(ErpConst.THREE);
        invoiceApply.setInvoiceType(invoiceOpenInfoDto.getOrderInvoiceInfo().getInvoiceType());
        invoiceApply.setApplyMethod(InvoiceApplyMethodEnum.ONLINE_INVOICE_OPEN.getApplyMethodCode());
        invoiceApply.setComments(saleOrderInfo.getSaleorderNo());
        WebAccount wenAccountInfoByMobile = webAccountMapper.getWenAccountInfoByMobile(invoiceOpenInfoDto.getMobile());

        if (wenAccountInfoByMobile != null) {
            invoiceApply.setSignerId(wenAccountInfoByMobile.getErpAccountId());
            invoiceApply.setSignerName("用户ID:" + wenAccountInfoByMobile.getErpAccountId());
        }


        Saleorder sale = new Saleorder();
        sale.setSaleorderId(saleOrderInfo.getSaleorderId());
        List<SaleorderGoods> goodsList = saleorderService.getSaleorderGoodsById(sale);

        if (CollectionUtils.isEmpty(goodsList)) {
            logger.error("开票申请时商品列表数据异常 onLineInvoiceOpenWarn orderId:{}", saleOrderInfo.getSaleorderId());
            throw new Exception("开票申请时商品列表数据异常");
        }

        if (saleorderService.isCouponsType(saleOrderInfo.getOrderType())) {
            goodsList.forEach(orderGoods -> orderGoods.setRealTotalAmount(orderGoods.getMaxSkuRefundAmount().subtract(orderGoods.getAfterReturnAmount())));
        } else {
            goodsList.forEach(orderGoods -> {
                Integer afterReturnNum = orderGoods.getAfterReturnNum();
                Integer num = orderGoods.getNum();
                orderGoods.setRealTotalAmount(orderGoods.getPrice().multiply(new BigDecimal(num - afterReturnNum)));
            });
        }

        ArrayList<InvoiceApplyDetail> invoiceApplyDetails = new ArrayList<>();
        goodsList.forEach(goods -> {
            InvoiceApplyDetail invoiceApplyDetail = new InvoiceApplyDetail();
            invoiceApplyDetail.setDetailgoodsId(goods.getSaleorderGoodsId());
            invoiceApplyDetail.setPrice(goods.getPrice());

            if (BigDecimal.ZERO.compareTo(goods.getPrice()) == 0) {
                logger.info("过滤单价为0的商品 goods:{}", JSON.toJSONString(goods));
                return;
            }

            BigDecimal applyNum = new BigDecimal(goods.getNum() - (goods.getAfterReturnNum() == null ? 0 : goods.getAfterReturnNum())).subtract(goods.getAppliedNum() == null ? BigDecimal.ZERO : goods.getAppliedNum()).subtract(goods.getInvoicedNum() == null ? BigDecimal.ZERO : goods.getInvoicedNum());
            if (applyNum.compareTo(BigDecimal.ZERO) < 1) {
                logger.error("开票申请中商品实际数量异常 onLineInvoiceOpenWarn goods:{}", JSON.toJSONString(goods));
                return;
            }
            invoiceApplyDetail.setNum(applyNum);
            invoiceApplyDetail.setTotalAmount(goods.getRealTotalAmount().subtract(goods.getAppliedAmount() == null ? BigDecimal.ZERO : goods.getAppliedAmount()).subtract(goods.getInvoicedAmount() == null ? BigDecimal.ZERO : goods.getInvoicedAmount()));
            if (saleOrderInfo.getInvoiceMethod().equals(ErpConst.ONE)) {
                invoiceApplyDetail.setChangedGoodsName(goods.getGoodsName());
            }
            String specModel = getSpecModel(goods);
            invoiceApplyDetail.setProductName(goods.getGoodsName());
            invoiceApplyDetail.setSpecModel(specModel);
            invoiceApplyDetail.setUnit(goods.getUnitName());
            invoiceApplyDetails.add(invoiceApplyDetail);
        });

        invoiceApply.setInvoiceApplyDetails(invoiceApplyDetails);

        logger.info("在线开票业务最终开始申请开票 invoiceApply:{}", JSON.toJSONString(invoiceApply));
        ResultInfo<?> resultInfo = invoiceService.saveOpenInvoceApply(invoiceApply);
        if (resultInfo == null) {
            logger.error("在线开票开票申请保存信息返回为空 onLineInvoiceOpenWarn invoiceApply:{}", JSON.toJSONString(invoiceApply));
            throw new Exception("在线开票开票申请保存信息返回为空");
        }

        logger.info("在线开票业务申请开票结果 resultInfo:{}", JSON.toJSONString(resultInfo));

        if (ErpConst.ERROR_CODE.equals(resultInfo.getCode())) {
            logger.error("在线开票业务申请开票错误 业务异常 onLineInvoiceOpenWarn resultInfo:{}", JSON.toJSONString(resultInfo));

            InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
            invoiceOpenResultDto.setOrderNo(invoiceOpenInfoDto.getOrderNo());
            invoiceOpenResultDto.setCode(ErpConst.ERROR_CODE);
            invoiceOpenResultDto.setMessage(resultInfo.getMessage());
            erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));

            throw new OnlineInvoiceOpenException("在线开票业务申请开票错误");
        }
        // 获取开票申请id
        invoiceOpenInfoDto.setInvoiceApplyId((Integer) resultInfo.getData());
    }

    private String getSpecModel(SaleorderGoods goods) {
        String specModel = "";
        try {
            int spuType = Integer.parseInt(goods.getSpuType());
            String model = goods.getModel();
            String spec = goods.getSpec();
            if (spuType == 316 || spuType == 1008) {
                specModel = model;
            } else if (spuType == 317 || spuType == 318) {
                specModel = spec;
            } else {
                if (StrUtil.isNotBlank(model)) {
                    specModel = model;
                } else {
                    specModel = spec;
                }
            }
        } catch (Exception e) {
            logger.error("获取规格型号异常", e);
        }
        return specModel;
    }


    /**
     *  对私打款：申请开票时，订单关联的流水业务类型为：订单收款、退款，且交易主体：对公，且流水对应客户ID=订单客户ID 的所有流水的收款金额-退款金额＜订单实际金额
     *
     *  如订单实际金额100元，归属客户A 。 交易流水  A客户150元收款，A客户50元退款，B客户100元收款。则合计对公收款100元  则为符合对公收款，进入申请开票列表。
     *
     *  如订单实际金额100元，归属客户A 。 交易流水 A客户149元收款，A客户50元退款，B客户100元收款。则合计对公收款99元， 则订单为对私收款，进入提前开票列表。
     * @param saleOrderInfo
     * @param invoiceApply
     */
    private void decideIsAdvance(Saleorder saleOrderInfo, InvoiceApply invoiceApply) {
        if (saleOrderInfo.getPaymentStatus() > 0){
            //实际金额
            BigDecimal realTotalAmountExceptAfterSalesFinished = saleorderMapper.getRealTotalAmountExceptAfterSalesFinished(saleOrderInfo.getSaleorderId());
            //获取交易信息数据
            List<CapitalBill> capitalBillList = getCapitalBills(saleOrderInfo);
            if (CollectionUtils.isNotEmpty(capitalBillList)){
                // 交易流水金额
                BigDecimal capitalBillAmount = BigDecimal.ZERO;
                for (CapitalBill bill : capitalBillList) {
                    if (bill.getCapitalBillDetail() != null) {
                        //计算对公收款
                        capitalBillAmount = getBillAmount(saleOrderInfo, capitalBillAmount, bill);
                    }
                }
                // 订单金额
                if (capitalBillAmount.compareTo(realTotalAmountExceptAfterSalesFinished) < 0){
                    invoiceApply.setIsAdvance(ErpConst.ONE);
                }
            }
        }
    }

    private List<CapitalBill> getCapitalBills(Saleorder saleOrderInfo) {
        CapitalBill capitalBill = new CapitalBill();
        // 获取交易信息数据
        capitalBill.setOperationType("finance_sale_detail");
        CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
        capitalBillDetail.setOrderType(ErpConst.ONE);// 销售订单类型
        capitalBillDetail.setOrderNo(saleOrderInfo.getSaleorderNo());
        capitalBillDetail.setRelatedId(saleOrderInfo.getSaleorderId());
        capitalBill.setCapitalBillDetail(capitalBillDetail);
        List<CapitalBill> capitalBillList = capitalBillService.getCapitalBillList(capitalBill);
        return capitalBillList;
    }

    private static BigDecimal getBillAmount(Saleorder saleOrderInfo, BigDecimal capitalBillAmount, CapitalBill bill) {
        if (bill.getCapitalBillDetail().getBussinessType().equals(526)
                || bill.getCapitalBillDetail().getBussinessType().equals(531)) {
            // 交易主体为对公
            // 且流水对应客户ID=订单客户ID
            if (bill.getTraderSubject().equals(1) && bill.getPayer().equals(saleOrderInfo.getTraderName())) {
                BigDecimal amount = bill.getCapitalBillDetail().getBussinessType().equals(526) ? bill.getAmount().abs() : bill.getAmount().abs().negate();
                capitalBillAmount = capitalBillAmount.add(amount);
            }
        }
        return capitalBillAmount;

    }


    /**
     * 当前业务校验部分
     *
     * @param invoiceOpenInfoDto
     */
    private void validRecentBusinessCondition(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception {
        if (invoiceOpenInfoDto == null || StringUtils.isBlank(invoiceOpenInfoDto.getOrderNo())) {
            logger.error("在线开票订单号不能为空 onLineInvoiceOpenWarn invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
            throw new RuntimeException("在线开票订单号不能为空");
        }
        // VDERP-12402 【在线申请开票优化】【ERP】去掉订单正在售后、订单正在修改的限制
        //if (onlineInvoiceOpenMapper.getAfterSalesNumByOrderNum(invoiceOpenInfoDto.getOrderNo()) > 0) {
        //    InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
        //    invoiceOpenResultDto.setOrderNo(invoiceOpenInfoDto.getOrderNo());
        //    invoiceOpenResultDto.setCode(ErpConst.ERROR_CODE);
        //    invoiceOpenResultDto.setMessage(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc());
        //    erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));
        //
        //    logger.warn(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc() + JSON.toJSONString(invoiceOpenInfoDto));
        //    throw new OnlineInvoiceOpenException(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc());
        //}

        if (onlineInvoiceOpenMapper.getOrderInvoiceApplyNumByOrderNo(invoiceOpenInfoDto.getOrderNo()) > 0) {
            InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
            invoiceOpenResultDto.setOrderNo(invoiceOpenInfoDto.getOrderNo());
            invoiceOpenResultDto.setCode(ErpConst.ERROR_CODE);
            invoiceOpenResultDto.setMessage(InvoiceErrorMessageEnum.INVOICE_APPLY_REASON.getDesc());
            erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));

            logger.error(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc() + JSON.toJSONString(invoiceOpenInfoDto));
            throw new OnlineInvoiceOpenException(InvoiceErrorMessageEnum.INVOICE_APPLY_REASON.getDesc());
        }

        if (onlineInvoiceOpenMapper.getOrderInvoiceStatusByOrderNo(invoiceOpenInfoDto.getOrderNo()) > 0) {
            InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
            invoiceOpenResultDto.setOrderNo(invoiceOpenInfoDto.getOrderNo());
            invoiceOpenResultDto.setCode(ErpConst.ERROR_CODE);
            invoiceOpenResultDto.setMessage(InvoiceErrorMessageEnum.INVOICE_OPEN_REASON.getDesc());
            erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));

            logger.error(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc() + JSON.toJSONString(invoiceOpenInfoDto));
            throw new OnlineInvoiceOpenException(InvoiceErrorMessageEnum.INVOICE_OPEN_REASON.getDesc());
        }

        if (onlineInvoiceOpenMapper.getOrderIsLockByOrderNo(invoiceOpenInfoDto.getOrderNo()) > 0) {
            InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
            invoiceOpenResultDto.setOrderNo(invoiceOpenInfoDto.getOrderNo());
            invoiceOpenResultDto.setCode(ErpConst.ERROR_CODE);
            invoiceOpenResultDto.setMessage(InvoiceErrorMessageEnum.ORDER_LOCK_REASON.getDesc());
            erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));

            logger.error(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc() + JSON.toJSONString(invoiceOpenInfoDto));
            throw new OnlineInvoiceOpenException(InvoiceErrorMessageEnum.ORDER_LOCK_REASON.getDesc());
        }
        // VDERP-12402 【在线申请开票优化】【ERP】去掉订单正在售后、订单正在修改的限制
        //if (onlineInvoiceOpenMapper.getOrderModifyNumByOrderNo(invoiceOpenInfoDto.getOrderNo()) > 0) {
        //    InvoiceOpenResultDto invoiceOpenResultDto = new InvoiceOpenResultDto();
        //    invoiceOpenResultDto.setOrderNo(invoiceOpenInfoDto.getOrderNo());
        //    invoiceOpenResultDto.setCode(ErpConst.ERROR_CODE);
        //    invoiceOpenResultDto.setMessage(InvoiceErrorMessageEnum.ORDER_MODIFY_REASON.getDesc());
        //    erpMsgProducer.sendMsg(RabbitConfig.ONLINE_INVOICING_EXCHANGE, RabbitConfig.ONLINE_INVOICING_KEY, JSON.toJSONString(invoiceOpenResultDto));
        //
        //    logger.warn(InvoiceErrorMessageEnum.AFTER_SALES_REASON.getDesc() + JSON.toJSONString(invoiceOpenInfoDto));
        //    throw new OnlineInvoiceOpenException(InvoiceErrorMessageEnum.ORDER_MODIFY_REASON.getDesc());
        //}

        if (saleorderMapper.getSaleOrderId(invoiceOpenInfoDto.getOrderNo()) == null) {
            logger.error("在线开票订单号信息异常 invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
            throw new Exception("在线开票订单号信息异常");
        }
    }


    /**
     * 客户资质信息处理
     *
     * @param invoiceOpenInfoDto
     * @throws Exception
     */
    private void dealCustomerInvoiceInfo(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception {
        logger.info("客户资质信息处理 invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
        if (invoiceOpenInfoDto.getTraderFinanceInfo() == null) {
            logger.error("在线申请开票时客户资质为空 onLineInvoiceOpenWarn invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
            throw new RuntimeException("在线申请开票时客户资质为空");
        }

        try {
            TraderFinanceInfoDto targetFinanceInfo = invoiceOpenInfoDto.getTraderFinanceInfo();

            TraderFinance sourceFinanceInfo = traderFinanceMapper.getCustomerFinanceByTraderId(targetFinanceInfo.getInvoiceTraderId());

            if (sourceFinanceInfo == null) {
                TraderFinance traderFinanceForSave = new TraderFinance();
                traderFinanceForSave.setTraderId(targetFinanceInfo.getInvoiceTraderId());
                traderFinanceForSave.setTraderType(ErpConst.ONE);
                traderFinanceForSave.setRegAddress(targetFinanceInfo.getRegAddress());
                traderFinanceForSave.setRegTel(targetFinanceInfo.getRegTel());
                traderFinanceForSave.setTaxNum(targetFinanceInfo.getTaxNum());
                traderFinanceForSave.setBank(targetFinanceInfo.getBank());
                traderFinanceForSave.setBankAccount(targetFinanceInfo.getBankAccount());
                traderFinanceForSave.setBankCode(targetFinanceInfo.getBankCode());
                if (StringUtils.isNotBlank(targetFinanceInfo.getAverageTaxpayerUri())) {
                    URL url = new URL(targetFinanceInfo.getAverageTaxpayerUri());
                    traderFinanceForSave.setAverageTaxpayerDomain(url.getHost());
                    traderFinanceForSave.setAverageTaxpayerUri(url.getFile());
                }
                logger.info("客户资质信息保存 traderFinanceForSave:{}", JSON.toJSONString(traderFinanceForSave));
                ResultInfo<?> resultInfo = traderCustomerService.saveCustomerFinance(traderFinanceForSave, userService.getUserById(ErpConst.NJ_ADMIN_ID));
                if (resultInfo == null || ErpConst.ERROR_CODE.equals(resultInfo.getCode())) {
                    logger.error("初始保存资质信息时出现错误 traderId:{} resultInfo:{}", targetFinanceInfo.getInvoiceTraderId(), JSON.toJSONString(resultInfo));
                    throw new Exception("保存资质信息时出现错误");
                }
                return;
            }


            boolean isNeedModify = isFieldCondition(sourceFinanceInfo.getRegAddress(), targetFinanceInfo.getRegAddress()) || isFieldCondition(sourceFinanceInfo.getRegTel(), targetFinanceInfo.getRegTel()) || isFieldCondition(sourceFinanceInfo.getTaxNum(), targetFinanceInfo.getTaxNum()) || isFieldCondition(sourceFinanceInfo.getBank(), targetFinanceInfo.getBank()) || isFieldCondition(sourceFinanceInfo.getBankCode(), targetFinanceInfo.getBankCode()) || isFieldCondition(sourceFinanceInfo.getBankAccount(), targetFinanceInfo.getBankAccount()) || isFieldCondition(sourceFinanceInfo.getAverageTaxpayerUri(), targetFinanceInfo.getAverageTaxpayerUri());
            if (!isNeedModify) {
                logger.info("客户资质信息推送不符合修改条件 orderNo:{}", invoiceOpenInfoDto.getOrderNo());
                return;
            }

            TraderFinance traderFinanceForUpdate = new TraderFinance();
            traderFinanceForUpdate.setTraderId(targetFinanceInfo.getInvoiceTraderId());
            traderFinanceForUpdate.setTraderFinanceId(sourceFinanceInfo.getTraderFinanceId());
            traderFinanceForUpdate.setTraderType(ErpConst.ONE);
            if (isFieldCondition(sourceFinanceInfo.getRegAddress(), targetFinanceInfo.getRegAddress())) {
                traderFinanceForUpdate.setRegAddress(targetFinanceInfo.getRegAddress());
            }
            if (isFieldCondition(sourceFinanceInfo.getRegTel(), targetFinanceInfo.getRegTel())) {
                traderFinanceForUpdate.setRegTel(targetFinanceInfo.getRegTel());
            }
            if (isFieldCondition(sourceFinanceInfo.getTaxNum(), targetFinanceInfo.getTaxNum())) {
                traderFinanceForUpdate.setTaxNum(targetFinanceInfo.getTaxNum());
            }
            if (isFieldCondition(sourceFinanceInfo.getBank(), targetFinanceInfo.getBank())) {
                traderFinanceForUpdate.setBank(targetFinanceInfo.getBank());
            }
            if (isFieldCondition(sourceFinanceInfo.getBankCode(), targetFinanceInfo.getBankCode())) {
                traderFinanceForUpdate.setBankCode(targetFinanceInfo.getBankCode());
            }
            if (isFieldCondition(sourceFinanceInfo.getBankAccount(), targetFinanceInfo.getBankAccount())) {
                traderFinanceForUpdate.setBankAccount(targetFinanceInfo.getBankAccount());
            }
            if (isFieldCondition(sourceFinanceInfo.getAverageTaxpayerUri(), targetFinanceInfo.getAverageTaxpayerUri())) {
                URL url = new URL(targetFinanceInfo.getAverageTaxpayerUri());
                traderFinanceForUpdate.setAverageTaxpayerDomain(url.getHost());
                traderFinanceForUpdate.setAverageTaxpayerUri(url.getFile());
            }

            logger.info("客户资质信息更新 traderFinanceForUpdate:{}", JSON.toJSONString(traderFinanceForUpdate));
            ResultInfo<?> resultInfo = traderCustomerService.saveCustomerFinance(traderFinanceForUpdate, userService.getUserById(ErpConst.NJ_ADMIN_ID));
            if (resultInfo == null || ErpConst.ERROR_CODE.equals(resultInfo.getCode())) {
                logger.error("保存资质信息时出现错误 traderId:{} resultInfo:{}", targetFinanceInfo.getInvoiceTraderId(), JSON.toJSONString(resultInfo));
                throw new Exception("保存资质信息时出现错误");
            }
        } catch (Exception e) {
            logger.error("处理客户资质信息异常 onLineInvoiceOpenWarn invoiceOpenInfoDto:{},e:{}", JSON.toJSONString(invoiceOpenInfoDto), e);
            throw new Exception("处理客户资质信息异常");
        }
    }


    /**
     * 字段是否满足条件(有更新返回true)
     *
     * @param source
     * @param target
     * @return
     */
    private boolean isFieldCondition(String source, String target) {
        if (StringUtils.isBlank(target)) {
            return false;
        }
        if (StringUtils.isNotBlank(source)) {
            return false;
        }
        return true;
    }

    /**
     * 同步订单修改单信息
     *
     * @param orderModifyApply
     */
    private void syncOrderModifyInfo(SaleorderModifyApply orderModifyApply) throws Exception {
        try {
            Saleorder orderForUpdate = new Saleorder();
            orderForUpdate.setSaleorderId(orderModifyApply.getSaleorderId());
            orderForUpdate.setInvoiceType(orderModifyApply.getInvoiceType());
            orderForUpdate.setInvoiceMethod(orderModifyApply.getInvoiceMethod());
            orderForUpdate.setInvoiceTraderContactName(orderModifyApply.getInvoiceTraderContactName());
            orderForUpdate.setInvoiceTraderContactMobile(orderModifyApply.getInvoiceTraderContactMobile());
            orderForUpdate.setInvoiceTraderAddressId(orderModifyApply.getInvoiceTraderAddressId());
            orderForUpdate.setInvoiceTraderAddress(orderModifyApply.getInvoiceTraderAddress());
            orderForUpdate.setInvoiceTraderArea(orderModifyApply.getInvoiceTraderArea());


            orderForUpdate.setIsSendInvoice(orderModifyApply.getIsSendInvoice());
            orderForUpdate.setIsDelayInvoice(orderModifyApply.getIsDelayInvoice());
            logger.info("在线开票订单修改单更新订单相关信息 orderForUpdate:{}", JSON.toJSONString(orderForUpdate));
            saleorderMapper.updateByPrimaryKeySelective(orderForUpdate);
        } catch (Exception e) {
            logger.error("订单修改单更新订单信息错误 onLineInvoiceOpenWarn orderModifyApply:{},e:{}", JSON.toJSONString(orderModifyApply), e);
            throw new Exception("订单修改单更新订单信息错误");
        }
    }

    /**
     * 处理生成开票信息相关
     *
     * @param invoiceOpenInfoDto
     * @throws Exception
     */
    private void dealGenerateOrderModify(InvoiceOpenInfoDto invoiceOpenInfoDto) throws Exception {
        logger.info("处理生成开票信息相关 invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderId(invoiceOpenInfoDto.getOrderNo());

        OrderInvoiceInfo orderInvoiceInfo = invoiceOpenInfoDto.getOrderInvoiceInfo();
        if (orderInvoiceInfo == null) {
            logger.error("在线开票开票信息不能为空 onLineInvoiceOpenWarn invoiceOpenInfoDto:{}", JSON.toJSONString(invoiceOpenInfoDto));
            throw new RuntimeException("在线开票开票信息不能为空");
        }

        OrderInvoiceInfo invoiceOpenInfoByOrderNo = onlineInvoiceOpenMapper.getInvoiceOpenInfoByOrderNo(invoiceOpenInfoDto.getOrderNo());
        logger.info("订单原收票信息相关参数 invoiceOpenInfoByOrderNo:{}", JSON.toJSONString(invoiceOpenInfoByOrderNo));

        if (orderInvoiceInfo.equals(invoiceOpenInfoByOrderNo)) {
            logger.info("订单发票信息与前台推送相同，无需修改 orderNo:{}", invoiceOpenInfoDto.getOrderNo());
            return;
        }

        //订单修改单相关
        SaleorderModifyApply saleorderModifyApply = null;
        try {
            saleorderModifyApply = new SaleorderModifyApply();
            saleorderModifyApply.setSaleorderId(saleOrderInfo.getSaleorderId());
            saleorderModifyApply.setInvoiceType(orderInvoiceInfo.getInvoiceType());
            saleorderModifyApply.setInvoiceMethod(orderInvoiceInfo.getInvoiceMethod());

            /**
             * VDERP-11444
             * 电子发票默认寄送，默认地区填充收货地区，默认地址填充收货地址。
             */
            if (ErpConst.THREE.equals(orderInvoiceInfo.getInvoiceMethod())) {
                saleorderModifyApply.setInvoiceTraderAddressId(saleOrderInfo.getTakeTraderAddressId());
                saleorderModifyApply.setInvoiceTraderArea(saleOrderInfo.getTakeTraderArea());
                saleorderModifyApply.setInvoiceTraderAddress(saleOrderInfo.getTakeTraderAddress());
                saleorderModifyApply.setInvoiceTraderContactName(saleOrderInfo.getInvoiceTraderContactName());
                saleorderModifyApply.setInvoiceTraderContactMobile(saleOrderInfo.getInvoiceTraderContactMobile());
            } else {
                Integer targetInvoiceTraderAddressId = orderInvoiceInfo.getInvoiceTraderAddressId();
                saleorderModifyApply.setInvoiceTraderAddressId(saleOrderInfo.getInvoiceTraderAddressId());
                saleorderModifyApply.setInvoiceTraderAddress(orderInvoiceInfo.getInvoiceTraderAddress());
                if (targetInvoiceTraderAddressId != null && targetInvoiceTraderAddressId > 0) {
                    String invoiceTraderAddressIdStr = regionService.getRegionIdStringByMinRegionId(targetInvoiceTraderAddressId);
                    if (StringUtils.isNotBlank(invoiceTraderAddressIdStr)) {
                        TraderAddress traderAddressForUpdate = new TraderAddress();
                        traderAddressForUpdate.setTraderAddressId(saleOrderInfo.getInvoiceTraderAddressId());
                        traderAddressForUpdate.setAreaId(orderInvoiceInfo.getInvoiceTraderAddressId());
                        traderAddressForUpdate.setAreaIds(invoiceTraderAddressIdStr);
                        traderAddressForUpdate.setAddress(orderInvoiceInfo.getInvoiceTraderAddress());
                        traderAddressForUpdate.setModTime(System.currentTimeMillis());
                        traderAddressForUpdate.setUpdater(ErpConst.NJ_ADMIN_ID);
                        traderAddressMapper.updateByPrimaryKeySelective(traderAddressForUpdate);
                        saleorderModifyApply.setInvoiceTraderArea(regionService.getRegionNameStringByMinRegionIds(invoiceTraderAddressIdStr));
                    }
                }
                saleorderModifyApply.setInvoiceTraderContactName(orderInvoiceInfo.getInvoiceTraderContactName());
                saleorderModifyApply.setInvoiceTraderContactMobile(orderInvoiceInfo.getInvoiceTraderContactMobile());
            }

            saleorderModifyApply.setValidStatus(ErpConst.ONE);
            saleorderModifyApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
            saleorderModifyApply.setCreator(ErpConst.NJ_ADMIN_ID);
            saleorderModifyApply.setAddTime(System.currentTimeMillis());

            saleorderModifyApply.setTakeTraderId(saleOrderInfo.getTakeTraderId());
            saleorderModifyApply.setTakeTraderName(saleOrderInfo.getTakeTraderName());
            saleorderModifyApply.setTakeTraderContactId(saleOrderInfo.getTakeTraderContactId());
            saleorderModifyApply.setTakeTraderContactName(saleOrderInfo.getTakeTraderContactName());
            saleorderModifyApply.setTakeTraderContactMobile(saleOrderInfo.getTakeTraderContactMobile());
            saleorderModifyApply.setTakeTraderContactTelephone(saleOrderInfo.getTakeTraderContactTelephone());
            saleorderModifyApply.setTakeTraderArea(saleOrderInfo.getTakeTraderArea());
            saleorderModifyApply.setTakeTraderAddress(saleOrderInfo.getTakeTraderAddress());

            saleorderModifyApply.setInvoiceTraderId(saleOrderInfo.getInvoiceTraderId());
            saleorderModifyApply.setInvoiceTraderName(saleOrderInfo.getInvoiceTraderName());
            saleorderModifyApply.setInvoiceTraderContactId(saleOrderInfo.getInvoiceTraderContactId());
            saleorderModifyApply.setInvoiceTraderContactTelephone(saleOrderInfo.getInvoiceTraderContactTelephone());
            saleorderModifyApply.setInvoiceComments(saleOrderInfo.getInvoiceComments());

            saleorderModifyApply.setDeliveryType(saleOrderInfo.getDeliveryType());
            saleorderModifyApply.setDeliveryClaim(saleOrderInfo.getDeliveryClaim());
            saleorderModifyApply.setLogisticsComments(saleOrderInfo.getLogisticsComments());

            WebAccount wenAccountInfoByMobile = webAccountMapper.getWenAccountInfoByMobile(invoiceOpenInfoDto.getMobile());
            if (wenAccountInfoByMobile != null) {
                saleorderModifyApply.setOnlinePersonId(wenAccountInfoByMobile.getErpAccountId());
            }

            saleorderModifyApply.setIsSendInvoice(ErpConst.ONE);
            saleorderModifyApply.setIsDelayInvoice(ErpConst.ZERO);

            // 新增主信息
            setOrderModifyBaseInfo(saleOrderInfo, saleorderModifyApply);
            logger.info("开票信息相关保存订单修改申请 saleorderModifyApply:{}", JSON.toJSONString(saleorderModifyApply));
            saleorderModifyApplyMapper.insertSelective(saleorderModifyApply);


            SaleorderModifyApply orderApplyExtra = new SaleorderModifyApply();
            final Integer applyId = saleorderModifyApply.getSaleorderModifyApplyId();
            orderApplyExtra.setSaleorderModifyApplyId(applyId);
            orderApplyExtra.setSaleorderModifyApplyNo(orderNoDict.getOrderNum(applyId, 12));
            saleorderModifyApply.setSaleorderModifyApplyNo(orderApplyExtra.getSaleorderModifyApplyNo());
            logger.info("开票信息订单修改申请更新修改单信息 orderApplyExtra:{}", JSON.toJSONString(orderApplyExtra));
            saleorderModifyApplyMapper.updateByPrimaryKeySelective(orderApplyExtra);

            VerifiesInfoGenerate verifiesInfoGenerate = new VerifiesInfoGenerate();
            verifiesInfoGenerate.setRelateTableKey(applyId);
            verifiesInfoGenerate.setRelateTable("T_SALEORDER_MODIFY_APPLY");
            verifiesInfoGenerate.setVerifiesType(ErpConst.ORDER_MODIFY_TYPE);
            verifiesInfoGenerate.setStatus(ErpConst.ONE);
            verifiesInfoGenerate.setAddTime(System.currentTimeMillis());
            verifiesInfoGenerateMapper.insertSelective(verifiesInfoGenerate);


            List<SaleorderGoods> orderGoods = saleorderGoodsMapper.getSaleorderGoodsBySaleorderId(saleOrderInfo.getSaleorderId());
            if (CollectionUtils.isNotEmpty(orderGoods)) {
                orderGoods.forEach(orderGood -> {
                    SaleorderModifyApplyGoods newGoods = new SaleorderModifyApplyGoods();
                    newGoods.setSaleorderModifyApplyId(applyId);
                    newGoods.setSaleorderGoodsId(orderGood.getSaleorderGoodsId());
                    newGoods.setDeliveryDirect(orderGood.getDeliveryDirect());
                    newGoods.setDeliveryDirectComments(orderGood.getDeliveryDirectComments());
                    newGoods.setGoodsComments(orderGood.getGoodsComments());
                    newGoods.setOldDeliveryDirect(orderGood.getDeliveryDirect());
                    newGoods.setOldDeliveryDirectComments(orderGood.getDeliveryDirectComments());
                    newGoods.setOldGoodsComments(orderGood.getGoodsComments());
                    saleorderModifyApplyGoodsMapper.insertSelective(newGoods);
                });
            }
        } catch (Exception e) {
            logger.error("生成订单修改单失败 onLineInvoiceOpenWarn invoiceOpenInfoDto:{},e:{}", JSON.toJSONString(invoiceOpenInfoDto), e);
            throw new Exception("生成订单修改单失败");
        }

        //订单开票信息更新
        syncOrderModifyInfo(saleorderModifyApply);
    }

    /**
     * 订单修改单基础信息
     *
     * @param saleOrderInfo
     * @param saleorderModifyApply
     */
    private void setOrderModifyBaseInfo(Saleorder saleOrderInfo, SaleorderModifyApply saleorderModifyApply) {
        saleorderModifyApply.setOldTakeTraderContactId(saleOrderInfo.getTakeTraderContactId());
        saleorderModifyApply.setOldTakeTraderContactName(saleOrderInfo.getTakeTraderContactName());
        saleorderModifyApply.setOldTakeTraderContactMobile(saleOrderInfo.getTakeTraderContactMobile());
        saleorderModifyApply.setOldTakeTraderContactTelephone(saleOrderInfo.getTakeTraderContactTelephone());
        saleorderModifyApply.setOldTakeTraderAddressId(saleOrderInfo.getTakeTraderAddressId());
        saleorderModifyApply.setOldTakeTraderArea(saleOrderInfo.getTakeTraderArea());
        saleorderModifyApply.setOldTakeTraderAreaId(saleOrderInfo.getTakeTraderAreaId());
        saleorderModifyApply.setOldTakeTraderAddress(saleOrderInfo.getTakeTraderAddress());
        saleorderModifyApply.setOldLogisticsComments(saleOrderInfo.getLogisticsComments());
        saleorderModifyApply.setOldInvoiceTraderContactId(saleOrderInfo.getInvoiceTraderContactId());
        saleorderModifyApply.setOldInvoiceTraderContactName(saleOrderInfo.getInvoiceTraderContactName());
        saleorderModifyApply.setOldInvoiceTraderContactMobile(saleOrderInfo.getInvoiceTraderContactMobile());
        saleorderModifyApply.setOldInvoiceTraderContactTelephone(saleOrderInfo.getInvoiceTraderContactTelephone());
        saleorderModifyApply.setOldInvoiceTraderAddressId(saleOrderInfo.getInvoiceTraderAddressId());
        saleorderModifyApply.setOldInvoiceTraderArea(saleOrderInfo.getInvoiceTraderArea());
        saleorderModifyApply.setOldInvoiceTraderAddress(saleOrderInfo.getInvoiceTraderAddress());
        saleorderModifyApply.setOldInvoiceComments(saleOrderInfo.getInvoiceComments());
        saleorderModifyApply.setOldInvoiceType(saleOrderInfo.getInvoiceType());
        saleorderModifyApply.setOldIsSendInvoice(saleOrderInfo.getIsSendInvoice());
        saleorderModifyApply.setOldIsDelayInvoice(saleOrderInfo.getIsDelayInvoice());
        saleorderModifyApply.setOldInvoiceMethod(saleOrderInfo.getInvoiceMethod());
        saleorderModifyApply.setOldIsSameAddress(saleOrderInfo.getIsSameAddress());
        saleorderModifyApply.setOldInvoiceSendNode(saleOrderInfo.getInvoiceSendNode());
        saleorderModifyApply.setOldDeliveryMethod(saleOrderInfo.getDeliveryMethod());
        saleorderModifyApply.setOldDeliveryType(saleOrderInfo.getDeliveryType());

        saleorderModifyApply.setOldDeliveryClaim(saleOrderInfo.getDeliveryClaim());
        saleorderModifyApply.setOldDeliveryDelayTime(saleOrderInfo.getDeliveryDelayTime());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealExpressReceiptBusiness(List<ExpressReceiptDto> expressReceiptList) throws Exception {
        if (CollectionUtils.isEmpty(expressReceiptList)) {
            throw new RuntimeException("快递在线签收数据异常");
        }

        String orderNo = expressReceiptList.get(0).getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            logger.error("报文订单号为空 onLineInvoiceOpenWarn expressReceiptList:{}", JSON.toJSONString(expressReceiptList));
            throw new RuntimeException("报文订单号为空");
        }

        Saleorder saleOrderInfo = saleorderMapper.getSaleOrderId(orderNo);
        if (saleOrderInfo == null) {
            logger.error("在线签收订单号检索订单信息异常 onLineInvoiceOpenWarn orderNo:{}", orderNo);
            throw new RuntimeException("在线签收订单号检索订单信息异常");
        }

        WebAccount wenAccountInfoByMobile = webAccountMapper.getWenAccountInfoByMobile(expressReceiptList.get(0).getMobile());
        if (wenAccountInfoByMobile == null) {
            logger.error("在线签收注册用户信息异常 onLineInvoiceOpenWarn orderNo:{}", orderNo);
            throw new RuntimeException("在线签收注册用户信息异常");
        }

        ExpressOnlineReceiptRecordPo expressOnlineReceiptRecordPo = new ExpressOnlineReceiptRecordPo();
        expressOnlineReceiptRecordPo.setOrderId(saleOrderInfo.getSaleorderId());
        expressOnlineReceiptRecordPo.setTraderName(saleOrderInfo.getTraderName());
        expressOnlineReceiptRecordPo.setTraderId(wenAccountInfoByMobile.getTraderId());
        expressOnlineReceiptRecordPo.setMobile(expressReceiptList.get(0).getMobile());
        expressOnlineReceiptRecordPo.setUserId(wenAccountInfoByMobile.getErpAccountId());
        expressOnlineReceiptRecordPo.setComments("客户在线确认");
        expressOnlineReceiptRecordPo.setIsEnable(ErpConst.ONE);
        expressOnlineReceiptRecordPo.setSignTime(expressReceiptList.get(0).getSignTime());
        expressOnlineReceiptRecordPo.setAddTime(System.currentTimeMillis());
        expressOnlineReceiptRecordPo.setCreator(ErpConst.NJ_ADMIN_ID);
        onlineSignatureMapper.insert(expressOnlineReceiptRecordPo);


        List<String> logisticsNos = expressReceiptList.stream().map(ExpressReceiptDto::getLogisticsNo).filter(StringUtil::isNotBlank).collect(Collectors.toList());

        List<Integer> expressIdList = expressReceiptList.stream().map(ExpressReceiptDto::getExpressId).filter(Objects::nonNull).collect(Collectors.toList());

        logger.info("logisticsNos:{},expressIdList:{}", JSON.toJSONString(logisticsNos), JSON.toJSONString(expressIdList));

        if (CollectionUtils.isEmpty(logisticsNos) && CollectionUtils.isEmpty(expressIdList)) {
            logger.error("报文信息中快递单号信息异常 onLineInvoiceOpenWarn expressReceiptList:{}", JSON.toJSONString(expressReceiptList));
            throw new RuntimeException("报文信息中快递单号信息异常");
        }

        List<Express> expressListByOrderNo = expressMapper.getExpressListByOrderNo(orderNo);
        if (CollectionUtils.isEmpty(expressListByOrderNo)) {
            logger.error("在线签收订单号检索包裹信息异常 onLineInvoiceOpenWarn orderNo:{}", orderNo);
            throw new Exception("在线签收订单号检索包裹信息异常");
        }

        List<Integer> expressIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(expressIdList)) {
            expressIds = expressListByOrderNo.stream().map(Express::getExpressId).filter(expressIdList::contains).collect(Collectors.toList());
        } else if (CollectionUtils.isNotEmpty(logisticsNos)) {
            expressIds = expressListByOrderNo.stream().filter(item -> logisticsNos.contains(item.getLogisticsNo())).map(Express::getExpressId).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(expressIds)) {
            logger.error("签收报文的快递在订单中未能匹配 onLineInvoiceOpenWarn orderNo:{}, expressReceiptList:{}", orderNo, JSON.toJSONString(expressReceiptList));
            throw new Exception("签收报文的快递在订单中未能匹配");
        }


        try {
            /**
             * 包裹签收状态更新
             */
            logger.info("updateOnLineReceiptIdByExpressIds,id:{},expressIds:{}",expressOnlineReceiptRecordPo.getExpressOnlineReceiptRecordId(),expressIds);
            expressMapper.updateOnLineReceiptIdByExpressIds(expressOnlineReceiptRecordPo.getExpressOnlineReceiptRecordId(), expressIds);

            /**
             * 订单签收状态更新
             */

            List<Express> expressList = expressMapper.getExpressListByOrderNo(orderNo);

            Integer onlineReceiptStatus;

            if (ErpConst.TWO.equals(saleOrderInfo.getDeliveryStatus()) && CollectionUtils.isEmpty(expressList.stream().filter(item -> item.getOnlineReceiptId().equals(0)).collect(Collectors.toList()))) {
                onlineReceiptStatus = ErpConst.TWO;
            } else if (CollectionUtils.isEmpty(expressList.stream().filter(item -> item.getOnlineReceiptId() > 0).collect(Collectors.toList()))) {
                onlineReceiptStatus = ErpConst.ZERO;
            } else {
                onlineReceiptStatus = ErpConst.ONE;
            }
            saleorderMapper.updateOnlineReceiptStatusById(saleOrderInfo.getSaleorderId(), onlineReceiptStatus);

            // 更新批次表的在线确认状态
            logger.info("客户在线确认，开始更新批次表的在线确认状态，saleOrderInfo={},expressIds={}", saleOrderInfo, expressIds);
            List<String> batchNos = expressMapper.selectBatchNosByExpressIds(expressIds);
            List<String> updateBatchNos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(batchNos)) {
                updateBatchNos = batchNos.stream().filter(batchNo -> Constants.ONE.equals(expressMapper.verifyAllOnlineConfirmation(batchNo))).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(updateBatchNos)) {
                for (String updateBatchNo : updateBatchNos) {
                    OutboundBatchesRecode outboundBatchesRecode = outboundBatchesRecodeMapper.getByBatchNo(updateBatchNo);
                    if (Objects.isNull(outboundBatchesRecode)) {
                        continue;
                    }
                    // 更新批次的在线确认状态、上传状态、审核状态
                    Integer status = PAYMENT_TYPE_FOR_PAYMENT_DAYS.contains(saleOrderInfo.getPaymentType()) ? null : Constants.ONE;
                    logger.info("客户在线确认,需要更新在线确认状态的批次编号为={},订单是否为账期订单={}", updateBatchNos, status);
                    Integer auditStatus = outboundBatchesRecode.getAuditStatus();
                    if (!Objects.isNull(auditStatus) && Constants.TWO.equals(auditStatus)) {
                        status = null;
                    }
                    //①若确认单中有某个或某些批次在审核中，若客户此时在线确认后，正好满足该批次下的快递全部签收，则该批次原审核流自动审核通过，
                    // 此时物流信息模块：确认单审核状态变为审核通过，操作时间改为在线确认的操作时间。批次审核明细模块：操作人变为njadmin（管理员），
                    // 操作时备注改为审核通过（系统自动审核），如下图。如客户已经在线确认，同时质管人员已经打开质量审核的页面，
                    // 则此时原审核中的单据在质量审核时不可提交。报错文案：客户已经在线签收，不可操作。同时在工作台刷新后去除该待审单据；
                    //②若上传的确认单被审核不通过，此时客户在线确认，正好满足该批次下的快递全部签收，
                    // 则物流信息处确认单审核状态覆盖原批次的审核不通过状态变为“审核通过”状态，通过审核时间改为客户在线签收的时间，驳回原因去除改为“-”；下方批次的批次审核明细依然为审核不通过状态；
                    if (!PAYMENT_TYPE_FOR_PAYMENT_DAYS.contains(saleOrderInfo.getPaymentType()) && (Constants.TWO.equals(auditStatus) || Constants.ZERO.equals(auditStatus))) {
                        status = Constants.ONE;
                        String comments = Constants.TWO.equals(auditStatus) ? "" : "-";
                        logger.info("开始处理确认单审核通过的审核流，批次id: {}", outboundBatchesRecode.getId());
                        outboundBatchesRecodeMapper.updateCommentsDiyById(outboundBatchesRecode.getId(), comments);
                        if (Constants.TWO.equals(auditStatus)) {
                            this.confirmationCheck(Boolean.TRUE, outboundBatchesRecode.getId(), "审核通过（系统自动审核）");
                        }
                    }

                    logger.info("客户在线确认,需要更新在线确认状态的批次编号为={},当前批次的审核状态为={},更新状态为={}", updateBatchNos, auditStatus, status);
                    outboundBatchesRecodeMapper.updateOnlineConfirmByBatchNos(Lists.newArrayList(updateBatchNo), Constants.ONE, status, status);
                    if (Constants.ONE.equals(status)) {
                        logger.info("客户在线确认，触发确认单审核，订单信息={}", saleOrderInfo);
                        confirmationFormRecodeService.confirmationOrderAudit(null, System.currentTimeMillis(), saleOrderInfo.getSaleorderId());
                    }
                }
            }
            List<ExpressOnlineReceiptVo> data = expressMapper.getExpressOnlineReceiptListByOnlineReceiptId(expressOnlineReceiptRecordPo.getExpressOnlineReceiptRecordId());
            processFile(data);
        } catch (Exception e) {
            logger.error("在线签收本地处理异常，将进行本地补偿 onLineInvoiceOpenWarn expressReceiptList:{}", JSON.toJSONString(expressReceiptList));
            throw new Exception("在线签收本地处理异常");
        }

    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void compenseFile(Integer id) {

        Attachment attachment = new Attachment();
        attachment.setAttachmentFunction(ErpConst.ONLINE_EXPRESS_ATTACHMET_FUNCTION);
        attachment.setAttachmentType(ErpConst.ONLINE_EXPRESS_ATTACHMET_TYPE);
        attachment.setRelatedId(id);
        List<Attachment> attachments = attachmentMapper.selectByPropertiesSelective(attachment);
        if (CollUtil.isNotEmpty(attachments)) {
            attachments.forEach(x->{
                attachmentMapper.delAttachmentId(x);
            });
        }
        List<ExpressOnlineReceiptVo> data = expressMapper.getExpressOnlineReceiptListByOnlineReceiptId(id);
        processFile(data);
    }

    public void processFile(List<ExpressOnlineReceiptVo> data) {

        if (CollUtil.isEmpty(data)) {
            return;
        }

        StringBuilder sb = new StringBuilder();

        String preFix = "<!DOCTYPE html><html lang=\"en\"><head><meta charset=\"UTF-8\"><title>客户在线确认记录</title></head><style>.ax_default{font-family:'Arial Normal''Arial'sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000;text-align-last:center}table{border-spacing:0;border-collapse:collapse}.H2{font-family:'Arial Normal''Arial'sans-serif;font-weight:bold;font-style:normal;font-size:18px}.line-text{display:flex;align-items:center;text-align:center;color:#333}.line-text::before,.line-text::after{content:'';flex:1;border-bottom:1px solid grey}.line-text::before{margin-right:2.25em;margin-left:2.25em}.line-text::after{margin-left:2.25em;margin-right:2.25em}</style><body><div class=\"ax_default\"><div><div class=\"H2\">客户在线确认记录（orderNo）</div></div><table><thead><tr><th style=\"width: 8%\">快递单号</th><th style=\"width: 8%\">快递公司</th><th style=\"width: 18%\">客户名称</th><th style=\"width: 2%\">客户ID</th><th style=\"width: 7%\">在线确认者注册手机</th><th style=\"width: 10%\">在线确认者及用户ID</th><th style=\"width: 8%\">在线确认时间</th><th style=\"width: 5%\">备注</th></tr></thead><tbody>";

        sb.append(preFix.replace("orderNo", data.get(0).getSaleorderNo()));

        for (int i = 0; i < data.size(); i++) {
            ExpressOnlineReceiptVo expressOnlineReceiptVo = data.get(i);
            String middleFix = "<tr><td>logisticsNo</td><td>logisticsName</td><td>traderName</td><td>traderId</td><td>mobile</td><td>traderContactName/traderContactId</td><td>time</td><td>客户在线确认</td></tr>";
            Date date = new Date(expressOnlineReceiptVo.getSignTime());
            sb.append(middleFix.replace("logisticsNo", StrUtil.isEmpty(expressOnlineReceiptVo.getLogisticsNo()) ? "" : expressOnlineReceiptVo.getLogisticsNo())
                    .replace("logisticsName", StrUtil.isEmpty(expressOnlineReceiptVo.getLogisticsName()) ? "" : expressOnlineReceiptVo.getLogisticsName())
                    .replace("traderName", StrUtil.isEmpty(expressOnlineReceiptVo.getTraderName()) ? "" : expressOnlineReceiptVo.getTraderName())
                    .replace("traderId", expressOnlineReceiptVo.getTraderId().toString())
                    .replace("mobile", DesensitizedUtil.mobilePhone(expressOnlineReceiptVo.getMobile()))
                    .replace("traderContactName", StrUtil.isEmpty(expressOnlineReceiptVo.getTraderContactName()) ? "" : expressOnlineReceiptVo.getTraderContactName())
                    .replace("traderContactId", expressOnlineReceiptVo.getUserId().toString())
                    .replace("time", DateUtil.formatDateTime(date)));
        }

        String surFix = "</tbody></table style=\"width: 100%\"><br><div class=\"line-text\"><span class=\"H2\">商品明细</span></div><br><table style=\"width: 100%\"><thead><tr><th style=\"width: 8%\">快递单号</th><th style=\"width: 8%\">sku</th><th style=\"width: 18%\">商品名称</th><th style=\"width: 8%\">品牌</th><th style=\"width: 7%\">型号规格</th><th style=\"width: 7%\">单位</th><th style=\"width: 5%\">数量</th></tr></thead><tbody>";
        sb.append(surFix);

        data.forEach(x->{

            List<ExpressSkuDataVo> expressSkuDataByExpressId = expressMapper.getExpressSkuDataByExpressId(x.getExpressId());

            int size = expressSkuDataByExpressId.size();
            for (int i = 0; i < expressSkuDataByExpressId.size(); i++) {
                if (i == 0) {
                    String skuFix = "<tr><td rowspan=\"size\">logisticsNo</td><td>skuNo</td><td>skuName</td><td>brand</td><td>model</td><td>unit</td><td>num</td></tr>";
                    sb.append(skuFix.replace("size", Integer.toString(size))
                            .replace("logisticsNo", StrUtil.isEmpty(x.getLogisticsNo())?"":x.getLogisticsNo())
                            .replace("skuNo", expressSkuDataByExpressId.get(i).getSku())
                            .replace("skuName", expressSkuDataByExpressId.get(i).getSkuName())
                            .replace("brand", expressSkuDataByExpressId.get(i).getBrand())
                            .replace("model", expressSkuDataByExpressId.get(i).getModel())
                            .replace("unit", expressSkuDataByExpressId.get(i).getUnit())
                            .replace("num", expressSkuDataByExpressId.get(i).getNum().toString())
                    );
                } else {
                    String skuFix = "<tr><td>skuNo</td><td>skuName</td><td>brand</td><td>model</td><td>unit</td><td>num</td></tr>";
                    sb.append(skuFix
                            .replace("logisticsNo", StrUtil.isEmpty(x.getLogisticsNo())?"":x.getLogisticsNo())
                            .replace("skuNo", expressSkuDataByExpressId.get(i).getSku())
                            .replace("skuName", expressSkuDataByExpressId.get(i).getSkuName())
                            .replace("brand", expressSkuDataByExpressId.get(i).getBrand())
                            .replace("model", expressSkuDataByExpressId.get(i).getModel())
                            .replace("unit", expressSkuDataByExpressId.get(i).getUnit())
                            .replace("num", expressSkuDataByExpressId.get(i).getNum().toString())
                    );
                }
            }


        });
        sb.append("</tbody></table></div></body></html>");
        String html = sb.toString();
        if (StrUtil.isEmpty(html)) {
            return;
        }
        ExpressOnlineReceiptVo expressOnlineReceiptVo = data.get(0);
        String fileName = "QRD" + StrUtil.UNDERLINE + expressOnlineReceiptVo.getSaleorderNo() + StrUtil.UNDERLINE + expressOnlineReceiptVo.getExpressOnlineRecceiptReordId();
        logger.info("在线确认id:{}附件，name:{},html:{}",expressOnlineReceiptVo.getExpressOnlineRecceiptReordId(),fileName,html);
        htmlStrToPdfGetFile(html, fileName,expressOnlineReceiptVo.getExpressOnlineRecceiptReordId());
    }


    @Retryable(value = NullPointerException.class, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
    public void htmlStrToPdfGetFile(String html, String fileName, Integer id) {

        String html2PdfUrl = html2PdfDomain + RENDER_URL;
        UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
        urlToPdfParam.setHtml(html);
        UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
        UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm", "1cm", "1cm", "0cm");
        pdf.setMargin(margin);
        pdf.setScale(SCALE);
        urlToPdfParam.setPdf(pdf);
        // 上传入库单验收报告返回oss链接
        String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", fileName, urlToPdfParam);
        logger.info("生成在线确认附件：{},返回地址ossUrl:{}", fileName, ossUrl);
        if (org.apache.commons.lang3.StringUtils.isBlank(ossUrl)) {
            logger.info("生成在线确认附件失败");
            throw new NullPointerException("生成在线确认附件失败");
        }

        //新增验收报告附件
        Attachment attachment = new Attachment();
        attachment.setAttachmentType(ErpConst.ONLINE_EXPRESS_ATTACHMET_TYPE);
        attachment.setAttachmentFunction(ErpConst.ONLINE_EXPRESS_ATTACHMET_FUNCTION);
        attachment.setRelatedId(id);

        String domainAndUri = ossUrl.split(ossHttp)[1];
        int domainIndex = domainAndUri.indexOf('/');
        String domain = domainAndUri.substring(0, domainIndex);
        attachment.setDomain(domain);
        attachment.setUri(ossUrl);
        attachment.setName(fileName);
        attachment.setSuffix("pdf");
        attachment.setAddTime(System.currentTimeMillis());
        attachmentMapper.insertSelective(attachment);
    }

    private ResultInfo confirmationCheck(boolean passBoolean, Integer batchId, String refuseReason){
        // 2.审核通过或驳回
        Boolean pass = passBoolean;
        String comment = pass ? "审核通过" : "审核不通过";
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        variables.put("updater", 2);
        variables.put("autoCheckAptitude", false);
        // 审批操作
        try {
            String businessKey = "confirmationOrderVerify_" + batchId;
            Task taskInfo = processEngine.getTaskService().createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();
            if (Objects.isNull(taskInfo)) {
                return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
            }
            String taskId = taskInfo.getId();
            int statusInfo = 0;
            if (Boolean.FALSE.equals(pass)) {
                // 如果审核不通过
                statusInfo = 2;
                // 回写数据的表在db中
                variables.put("db", 2);
                verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(refuseReason)){
                comment = refuseReason;
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(null, taskId, comment, "njadmin", variables);
            // 如果未结束添加审核对应主表的审核状态
            if (complementStatus != null) {
                // 审核节点操作失败
                if (complementStatus.getCode().equals(-1)) {
                    return complementStatus;
                }
                if (!"endEvent".equals(complementStatus.getData())) {
                    verifiesRecordService.saveVerifiesInfo(taskId, statusInfo);
                }
            }
        } catch (Exception e) {
            logger.error("确认单批次通过或驳回异常，complementTaskParallel:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
        return ResultInfo.error();
    }
}
