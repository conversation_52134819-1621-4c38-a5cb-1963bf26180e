package com.vedeng.erp.trader.web.api;


import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.domain.dto.TraderSearchReqDto;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@ExceptionController
@RestController
@RequestMapping("/traderSearchApi")
@Slf4j
public class TraderSearchApi {
    
    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @NoNeedAccessAuthorization
    @RequestMapping(value = "/searchByName",method = RequestMethod.POST)
    public R<List<TraderCustomerInfoVo>> traderSearch(@RequestParam String traderName) {
        List<TraderCustomerInfoVo> list = traderCustomerBaseService.getTraderCustomerByTraderName(traderName, 0, 20);
        return R.success(list);
    }
}
