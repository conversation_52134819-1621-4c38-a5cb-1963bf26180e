package com.vedeng.erp.kingdee.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeInternalProcurementEntity;
import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import org.mapstruct.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeInternalProcurementConvertor extends BaseMapStruct<KingDeeInternalProcurementEntity, KingDeeInternalProcurementDto> {

    /**
     * DTO转Entity
     */
    @Mapping(target = "fid", source = "FID")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "FQzokGsywdh", source = "f_QZOK_GSYWDH")
    @Mapping(target = "FQzokBddjtid", source = "f_QZOK_BDDJTID")
    @Mapping(target = "FQzokDate", source = "f_QZOK_Date")
    @Mapping(target = "FQzokYwlx", source = "f_QZOK_YWLX")
    @Mapping(target = "FQzokOrgId", source = "f_QZOK_OrgId")
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "listToJsonArray")
    @Mapping(target = "FQzokEntity", source = "f_QZOK_Entity", qualifiedByName = "fQzokEntityListToJsonArray")
    @Override
    KingDeeInternalProcurementEntity toEntity(KingDeeInternalProcurementDto dto);

    /**
     * Entity转DTO
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_BDDJTID", source = "FQzokBddjtid")
    @Mapping(target = "f_QZOK_Date", source = "FQzokDate")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_OrgId", source = "FQzokOrgId")
    @Mapping(target = "FEntity", source = "FEntity", qualifiedByName = "jsonArrayToList")
    @Mapping(target = "f_QZOK_Entity", source = "FQzokEntity", qualifiedByName = "jsonArrayToListFQzokEntity")
    @Override
    KingDeeInternalProcurementDto toDto(KingDeeInternalProcurementEntity entity);



    @Named("listToJsonArray")
    default JSONArray listToJsonArray(List<KingDeeInternalProcurementDto.FEntity> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }

    @Named("fQzokEntityListToJsonArray")
    default JSONArray fQzokEntityListToJsonArray(List<KingDeeInternalProcurementDto.F_QZOK_Entity> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }



    @Named("jsonArrayToList")
    default List<KingDeeInternalProcurementDto.FEntity> entryJsonArrayToList(JSONArray array) {
        if (CollUtil.isEmpty(array)) {
            return Collections.emptyList();
        }
        return array.toJavaList(KingDeeInternalProcurementDto.FEntity.class);
    }

    @Named("jsonArrayToListFQzokEntity")
    default List<KingDeeInternalProcurementDto.F_QZOK_Entity> jsonArrayToListFQzokEntity(JSONArray array) {
        if (CollUtil.isEmpty(array)) {
            return Collections.emptyList();
        }
        return array.toJavaList(KingDeeInternalProcurementDto.F_QZOK_Entity.class);
    }


}
