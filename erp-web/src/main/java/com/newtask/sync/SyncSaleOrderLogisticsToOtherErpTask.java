package com.newtask.sync;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.logistics.dao.ExpressMapper;
import com.vedeng.logistics.model.SyncExpressDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 执行同步销售订单的物流信息同步给另一个ERP系统的采购订单。
 */
@Slf4j
@Component
@JobHandler(value="SyncSaleOrderLogisticsToOtherErpTask")
public class SyncSaleOrderLogisticsToOtherErpTask extends AbstractJobHandler {

    @Autowired
    private SyncDataErpApiService syncDataErpApiService;

    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;

    @Override
    public ReturnT<String> doExecute(String s) throws Exception {
        String dataType = SyncDataTypeEnum.SALEORDER_LOGISTICS_TO_BUYORDER.getDataType();//待同步的物流信息表
        List<SyncDataErpDto> syncDataErpDtoList =  syncDataErpApiService.selectByProcessStatus(ErpConst.ZERO,dataType);
        if(CollectionUtils.isEmpty(syncDataErpDtoList)){
            log.info("当前无待同步销售订单的物流信息同步给另一个ERP系统的采购订单");
            XxlJobLogger.log("当前无待同步销售订单的物流信息同步给另一个ERP系统的采购订单");
            return ReturnT.SUCCESS;
        }
        for(SyncDataErpDto syncDataErpDto : syncDataErpDtoList){
            XxlJobLogger.log("当前处理到了"+syncDataErpDto.getBusinessNo());
            SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getBySaleOrderNo(syncDataErpDto.getBusinessNo());
            if(saleorderInfoDto == null || saleorderInfoDto.getStatus().equals(3)){//  `STATUS` tinyint(1) unsigned DEFAULT '0' COMMENT '订单状态：0待确认（默认）、1进行中、2已完结、3已关闭 4待用户确认',
                syncDataErpDto.setRemark("源订单不存在或已关闭");
                syncDataErpDto.setProcessStatus(ErpConst.THREE);
                syncDataErpDto.setUpdateTime(new Date());
                syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                continue;
            }

            String shortName = syncDataErpDto.getTargetErp();
            BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByShortName(shortName);
            if(baseCompanyInfoDto != null){
                String host = baseCompanyInfoDto.getErpDomain();//请求的host http://erp.ivedeng.com
                String url = SyncDataTypeEnum.SALEORDER_LOGISTICS_TO_BUYORDER.getDataInputUrl(); //  /orderstream/saleorder/contractReturnSaveForOtherErp.do
                String requestContent = processRequestContent(syncDataErpDto.getBusinessNo(),syncDataErpDto.getRequestContent());
                if(StringUtils.isNotBlank(requestContent)){
                    String content = NewHttpClientUtils.httpPostWithRawContent(host+url, requestContent);
                    if(StringUtils.isNotBlank(content)){
                        JSONObject jsonObject = JSON.parseObject(content);
                        if(jsonObject != null && jsonObject.getInteger("code") == 0
                        ){ //需要增加一条，当前销售单全部发货了
                            if(saleorderInfoDto.getDeliveryStatus() != null && saleorderInfoDto.getDeliveryStatus().equals(ErpConst.TWO)){
                                syncDataErpDto.setRemark("目标系统返回成功");
                                syncDataErpDto.setProcessStatus(ErpConst.TWO);
                                syncDataErpDto.setUpdateTime(new Date());
                                syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                                continue;
                            }else{
                                //
                                syncDataErpDto.setRemark("部分发货同步完成，待继续同步");
                                syncDataErpDto.setUpdateTime(new Date());
                                syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                                continue;
                            }

                        }
                        if(jsonObject != null && jsonObject.getInteger("code") == 3){ //当服务方返回失败时，此条不需要处理了
                            syncDataErpDto.setRemark("目标系统返回3，表示不需要再处理");
                            syncDataErpDto.setProcessStatus(ErpConst.THREE);
                            syncDataErpDto.setUpdateTime(new Date());
                            syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
                            continue;
                        }
                    }
                }
            }
            //以下是返回2的场景，下次继续请求即可。
//            syncDataErpDto.setProcessStatus(ErpConst.THREE);
            syncDataErpDto.setRemark("未满足同步条件");
            syncDataErpDto.setUpdateTime(new Date());
            syncDataErpApiService.updateByPrimaryKeySelective(syncDataErpDto);
        }
        return ReturnT.SUCCESS;
    }

    /**
     *
     * @param saleOrderNo 当前ERP的销售订单
     * @param requestContent 目标ERP系统的采购订单号 {"buyorderNo":"EGCG2025051900001"}
     * @return
     */
    private String processRequestContent(String saleOrderNo,String requestContent){
        List<SyncExpressDto> syncExpressDtoList = expressMapper.getSyncExpressListForSaleOrder(saleOrderNo);
        if(CollectionUtils.isEmpty(syncExpressDtoList)){//如果没有发货
            return "";
        }
        JSONObject jsonObject = JSONObject.parseObject(requestContent);
        String buyorderNo = jsonObject.getString("buyorderNo");
        syncExpressDtoList.forEach(syncExpressDto -> {
            syncExpressDto.setBuyorderNo(buyorderNo);
        });
        return JSONObject.toJSONString(syncExpressDtoList);
    }

    @Autowired
    @Qualifier("expressMapper")
    private ExpressMapper expressMapper;

}
