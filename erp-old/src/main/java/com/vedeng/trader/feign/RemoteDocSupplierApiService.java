package com.vedeng.trader.feign;

import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.common.feign.constants.ServerConstants;
import com.vedeng.doc.api.docsupplier.command.SupplierDocCommand;
import com.vedeng.doc.api.docsupplier.seivice.DocSupplierApiService;
import com.vedeng.doc.api.dto.DataResult;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 远程调用doc
 * fixme: 后期重构供应商逻辑时，不在old中引入
 *
 * <AUTHOR>
 */
@FeignApi(serverName = ServerConstants.DOC_SERVER)
public interface RemoteDocSupplierApiService extends DocSupplierApiService {

    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /doc/supplier/add")
    @Override
    DataResult addDocSupplier(@RequestBody SupplierDocCommand supplierDocCommand);

}
