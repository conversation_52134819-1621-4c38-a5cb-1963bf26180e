<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.erp.trader.mapper.TraderCustomerCategoryMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity" >
    <id column="TRADER_CUSTOMER_CATEGORY_ID" property="traderCustomerCategoryId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="CUSTOMER_CATEGORY_NAME" property="customerCategoryName" jdbcType="VARCHAR" />
    <result column="SORT" property="sort" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    TRADER_CUSTOMER_CATEGORY_ID, PARENT_ID, CUSTOMER_CATEGORY_NAME, SORT
  </sql>
  <select id="getTraderCustomerCategoryByParentId" resultMap="BaseResultMap" parameterType="java.lang.Integer">
	select
	  <include refid="Base_Column_List" />
	  from T_TRADER_CUSTOMER_CATEGORY
	  where PARENT_ID=#{parentId,jdbcType=INTEGER} AND IS_HIDDEN = 0
	  order by SORT desc
  </select>
  
  <select id="getTraderCustomerCategoryById" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	select
	  <include refid="Base_Column_List" />
	  from T_TRADER_CUSTOMER_CATEGORY
	  where TRADER_CUSTOMER_CATEGORY_ID=#{traderCustomerCategoryId,jdbcType=INTEGER}
  </select>
  
  <resultMap id="childRsultMap" type="com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity" >
    <id column="TRADER_CUSTOMER_CATEGORY_ID" property="traderCustomerCategoryId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="CUSTOMER_CATEGORY_NAME" property="customerCategoryName" jdbcType="VARCHAR" />
    <result column="SORT" property="sort" jdbcType="INTEGER" />
    <collection column="TRADER_CUSTOMER_CATEGORY_ID" property="childrenList" ofType="com.vedeng.erp.trader.domain.entity.TraderCustomerCategoryEntity" javaType="java.util.ArrayList" select="selectChildrenById"/>
  </resultMap>
  
  <!-- 查询子节点 -->
  <select id="selectChildrenById" resultMap="childRsultMap" parameterType="java.lang.Integer">
  	select 
  		<include refid="Base_Column_List" />
  		from T_TRADER_CUSTOMER_CATEGORY
  		where PARENT_ID=#{traderCustomerCategoryId}
	  order by SORT desc
  </select>
  

  <select id="getTraderCustomerCategoryChildList" resultMap="childRsultMap" parameterType="java.lang.Integer">
  	select
	  <include refid="Base_Column_List" />
	  from T_TRADER_CUSTOMER_CATEGORY
	  where TRADER_CUSTOMER_CATEGORY_ID=#{traderCustomerCategoryId,jdbcType=INTEGER}
  </select>
</mapper>
