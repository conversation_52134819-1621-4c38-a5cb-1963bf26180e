package com.vedeng.erp.kingdee.batch.dto;

import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 库存转换换单主表
 * <AUTHOR>
 * @date 2023/2/22 9:43
 **/
@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BatchWmsUnitConversionOrderDto {
    /**
    * 单位转换订单id
    */
    private Integer wmsUnitConversionOrderId;

    /**
    * 单位转换订单号
    */
    private String wmsUnitConversionOrderNo;

    /**
    * 申请类型 1单位转换、2主机配件、3组合产品
    */
    private Integer orderType;

    /**
    * 申请原因
    */
    private String reason;

    /**
    * 审核状态 0 待审核 1 审核中 2 审核通过 3 审核不通过
    */
    private Integer verifyStatus;

    /**
    * 部门id
    */
    private Integer orgId;

    /**
    * 部门
    */
    private String orgName;

    /**
    * 备注
    */
    private String comments;

    /**
    * 是否删除 0 否 1 是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 创建者id
    */
    private Integer creator;

    /**
    * 创建者名称
    */
    private String creatorName;

    /**
    * 修改者id
    */
    private Integer updater;

    /**
    * 修改者名称
    */
    private String updaterName;

    /**
    * 修改时间
    */
    private Date modTime;
}