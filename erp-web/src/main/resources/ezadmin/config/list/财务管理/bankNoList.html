<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>联行号管理</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="bankNoList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="numbers">
<div id="appendHead">
</div>

<div class="layui-fluid">
    <form class="layui-form" id="search">
        <div class="layui-inline ">
            <label>联行号</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入"  oper="like"
                       name="BANK_NO" empty_show="-">
            </div>
        </div>

        <div class="layui-inline ">
            <label>银行名称</label>
            <div class="layui-input-inline">
                <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like"
                       name="BANK_NAME" empty_show="-">
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">操作人</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="UPDATER" jdbctype="INTEGER"
                        data="SELECT DISTINCT u.USER_ID K, lower(u.USERNAME) V
                                FROM T_USER u
                                         LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
                                         LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
                                         LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
                                WHERE o.TYPE = 314 order by u.USERNAME"
                        datatype="KVSQLCACHE"></select>
            </div>
        </div>

        <div class="layui-inline ">
            <label>操作时间</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="daterange" jdbctype="DATE" type="search"
                        placeholder="请输入" name="MOD_TIME" oper="BETWEEN"></object>
            </div>
        </div>


    </form>
    <hr class="layui-border-blue">

    <div class="btn-group bd-highlight" id="tableButton">
        <button type="table" class="layui-btn" url="/bank/edit.do" opentype="MODEL" area="600px,400px" windowname="新增" >+ 新增</button>
        <button type="table" class="layui-btn" url="/bankMatchConfig/index.do" opentype="MODEL" area="800px,800px" windowname="开户行配置" >开户行配置</button>
    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>

        <tr id="column">
            <th name="BANK_NAME">支行名称</th>
            <th name="BANK_NO">联行号</th>
            <th name="UPDATER_NAME">操作人</th>
            <th name="MOD_TIME">操作时间</th>

            <th type="rowbutton" id="rowbutton">
                <button class="layui-btn list-row-button" type="single" opentype="MODEL"
                        url="/bank/edit.do?bankId=${BANK_ID}" windowname="编辑" area="600px,400px"
                        name="update">编辑
                </button>
                <button class="layui-btn list-row-button" type="single" opentype="CONFIRM_AJAX"
                        url="/bank/delete.do?bankId=${BANK_ID}" windowname="删除" name="update">删除
                </button>
            </th>
        </tr>
        </thead>
        <tbody>
        <tr>
            <td colspan="100">
                express:
                <pre id="express" class="layui-code" orderby="order by BANK_ID desc" groupby="">
                    select BANK_ID,BANK_NO,BANK_NAME,UPDATER_NAME , DATE_FORMAT(MOD_TIME, '%Y-%m-%d %H:%i') MOD_TIME from T_BANK
                             where IS_DEL = 0 AND DISABLE_FLAG = 0
                 </pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>

<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>
</body>
</html>