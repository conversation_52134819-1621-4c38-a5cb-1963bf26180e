package com.vedeng.mobile.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;


/**
 * 销售订单详情返回类
 */
@Data
public class SaleorderDetailDto {

    /**
     * 销售订单id
     */
    private Integer saleOrderId;

    /**
     * 销售订单号
     */
    private String saleOrderNo;

    /**
     * 销售订单状态
     */
    private Integer orderStatus;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 归属销售Id
     */
    private Integer currentUserId;

    /**
     * 归属销售名称
     */
    private String currentUserName;

    /**
     * 内部备注
     */
    private String comments;

    /**
     * 订单总价
     */
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    private String addTime;

    /**
     * 发票详情地址
     */
    private List<ResourceFileDto> invoiceUrl;

    /**
     * 合同详情地址
     */
    private ResourceFileDto contractUrl;

    /**
     * 是否有售后信息
     */
    private Boolean haveAfterSalesInfo;

    /**
     * 是否有物流信息
     */
    private Boolean haveExpressInfo;

    /**
     * 收货信息
     */
    private TakeTraderDto takeTrader;

    /**
     * 收票信息
     */
    private InvoiceInfoDto invoiceInfo;

    /**
     * 商品明细信息
     */
    private List<SaleorderListOfGoodsDto> saleOrderGoodsList;

}
