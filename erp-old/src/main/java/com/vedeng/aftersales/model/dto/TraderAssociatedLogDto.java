package com.vedeng.aftersales.model.dto;

import lombok.Data;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Data
public class TraderAssociatedLogDto {

    /**
     * ERP注册用户唯一编号
     */
    private Integer erpAccountId;

    /**
     * 注册用户归属平台
     *
     * @see com.vedeng.common.constant.BelongPlatformEnum
     */
    private Integer webAccountToBelongPlatformNo;

    private Integer traderId;

    private String reason;

    private String remark;

    /**
     * 操作类型，1:绑定日志，2:解除绑定日志
     */
    private Integer operationType;

    private Integer operatorId;

    /**
     * 操作人所在部门
     */
    private String operatorOrganization;

    private String operatorName;

}
