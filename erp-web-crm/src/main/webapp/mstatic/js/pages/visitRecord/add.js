void function () {
    new Vue({
        el: '#page-container',
        data: {
            isEdit: false, // true:编辑状态  false:新建
            canAjax: true,
            pageLoading: true, // 页面加载状态
            id: '', // 编辑id
            copyid: '', // 下次拜访·获取对应信息

            /* Card1 */
            // 客户名称
            traderName: '',
            traderId: '',
            tycFlag: 'N', //是否是天眼查带入
            isFromTyc: false, // 其他信息·是否客户名称带入
            traderInfo: {},
            belonger: '',
            traderDetailInfo: {},
            // 客户类型
            traderType: '',
            traderTypeDisabled: false,
            traderTypeList: [
                { label: '渠道商', value: 465 },
                { label: '终端', value: 466 }
            ],
            // 手机
            mobile: '',
            // 固话
            telephone: '',
            // 其他联系方式
            otherContact: [],
            otherContactInfo: '', // 接口入参
            // 联系人
            traderContactName: '',
            isFromPhone: false, // 是否通过手机号选择的
            // 职位
            contactPosition: '',
            positionList: [],
            // 暂无联系人信息
            noContract: false,
            // 拜访地区
            addressData: [],
            area: [],
            areaInfo: [],
            // 详细地址
            addressDetail: '',

            /* Card2 */
            // 拜访人
            visitor: '',
            visitorItems: {}, // 拜访人默认值
            visitors: [],
            // 同行人
            tongxing: [],
            defaultTongxingItems: [],
            // 计划拜访时间
            planVisitDate: '',
            // 拜访目标
            visitType: [],
            visitTypeList: [
                { label: '客情维护', value: 'C' },
                { label: '新客开发', value: 'A' },
                { label: '签约会员', value: 'D' },
                { label: '产品推广', value: 'E' },
                { label: '线索/商机跟进', value: 'B' }
            ],
            // 线索/商机编号
            businessNo: '',
            businessInfo: {},
            businessNoMsg: '',
            businessNoTip: '',
            // 备注
            remark: '',

            // 提交
            cansubmit: true,

            // DEMO
            // isShowCustomDialog: false,
            // customVal: '',
        },
        async created() {
            this.id = document.getElementById('visitRecordId').value || '';
            this.copyid = document.getElementById('visitCopyId').value || '';

            // 编辑
            if (this.id || this.copyid) {
                if (this.copyid) {
                    this.initVisitor();
                }
                this.getDetailInfo();
            }
            // 新建
            else {
                this.initVisitor();
                // this.initForm();
                this.pageLoading = false;
            }

            // 地址数据
            await this.$axios.get('/crm/common/m/getRegionAll').then(({ data }) => {
                if (data.success) {
                    this.addressData = data.data || [];
                }
            });

            GLOBAL.wxregister(['selectEnterpriseContact']);
        },
        mounted () {
            
        },
        methods: {
            // 拜访人·默认带入当前登录人
            initVisitor() {
                // let current_user_id = document.getElementById('golbal_userId').value || '';
                let current_user_QW_id = document.getElementById('golbal_userJobNumber').value || '';
                let current_user_name = document.getElementById('golbal_userName').value || '';
                let current_user_avatar = document.getElementById('golbal_headPic').value || '';
                this.visitor = current_user_QW_id;
                this.visitors = [ current_user_QW_id ];
                this.visitorItems = {
                    id: current_user_QW_id,
                    name: current_user_name,
                    avatar: current_user_avatar
                }
            },
            // 表单默认值带入
            initForm () {
                // 拜访时间
                const today = new Date();
                let year = today.getFullYear();
                let month = today.getMonth() + 1;
                let day = today.getDate();
                this.planVisitDate = `${year}-${month > 9? month: '0'+month}-${day > 9? day: '0'+day}`;
            },

            getDetailInfo () {
                this.$axios.get(`/crm/visitrecord/m/detail?id=${this.id || this.copyid}`).then(({data}) => {
                    if (data.success) {
                        let detail = data.data;
                        console.log('detail', detail);

                        // 客户
                        this.traderId = detail.traderId || '';
                        this.traderName = detail.customerName || '';
                        this.traderType = detail.customerNature || '';
                        if (this.traderType) {
                            this.traderTypeChange();
                        }
                        if (detail.visitCustomerVo && Object.keys(detail.visitCustomerVo).length) {
                            this.traderDetailInfo = detail.visitCustomerVo || {};
                            this.tycFlag = detail.visitCustomerVo.tycFlag || 'N';
                            this.traderInfo = {
                                traderId: this.traderId,
                                tycFlag: this.tycFlag, // Y:天眼查客户  N:不是天眼查客户 
                                saleName: detail.visitCustomerVo.belongerName,
                                belong: detail.visitCustomerVo.belong,
                                share: detail.visitCustomerVo.share,
                                customerGrade: detail.visitCustomerVo.customerGrade,
                            }
                            this.traderTypeDisabled = true;
                        }

                        this.noContract = detail.noContract === 'Y' ? true : false;

                        if (!this.noContract) {
                            this.traderContactName = detail.contactName || '';
                            this.mobile = detail.contactMobile || '';
                            this.telephone = detail.contactTele || '';
                            this.contactPosition = detail.contactPosition || '';
                            this.otherContactInfo = detail.otherContact || '';
                            if (this.otherContactInfo) {
                                this.initOtherContact();
                            }
                        }

                        if (detail.areaName) {
                            this.area = [detail.provinceCode, detail.cityCode, detail.areaCode];
                            this.areaInfo = [{
                                label: detail.provinceName,
                                value: detail.provinceCode
                            }, {
                                label: detail.cityName,
                                value: detail.cityCode
                            }, {
                                label: detail.areaName,
                                value: detail.areaCode
                            }];
                        }

                        this.addressDetail = detail.visitAddress || '';

                        // 编辑
                        if (this.id) {
                            this.visitRecordNo = detail.visitRecordNo || '';
                            // 拜访人
                            let visitorAvatar = detail.visitorPic || '';
                            let visitorName = detail.visitorName || '';
                            let visitorId = detail.visitorJobNumber || '';

                            this.visitor = visitorId;
                            this.visitors = [ visitorId ];
                            this.visitorItems = { id: visitorId, name: visitorName, avatar: visitorAvatar };

                            // 拜访信息
                            this.planVisitDate = detail.planVisitDate || '';
                            this.visitType = detail.visitTarget && detail.visitTarget.split(',') || [];
                            this.remark = detail.remark || '';
                            this.addTime = detail.addTime || '';

                            // 同行人
                            if (detail.tongxingUserList && detail.tongxingUserList.length) {
                                detail.tongxingUserList.forEach(item => {
                                    this.defaultTongxingItems.push({
                                        id: item.tongxingJobNumber,
                                        name: item.userName,
                                        avatar: item.aliasHeadPicture,
                                    })
                                    this.tongxing.push(item.tongxingJobNumber)
                                })
                            }

                            // 线索/商机编号
                            if (detail.bussinessChanceNo) {
                                this.businessInfo = {
                                    relateType: detail.relateType,
                                    bizId: detail.bussinessChanceId
                                };
                                this.businessNo = detail.bussinessChanceNo
    
                                this.checkBusinessNo();
                            }
                        }
                    }

                    this.pageLoading = false;
                })
            },

            /* 客户名称 */
            handlerTrader(data) {
                console.log('Handler traderName:', this.traderName, data);
                this.traderInfo = data || {};
                this.traderId = data.traderId || '';
                this.tycFlag = data.tycFlag || 'N';

                if (this.traderId) {
                    // 客户类型·erp带入不可修改
                    if (data.customerNature) {
                        this.traderTypeDisabled = true;
                        this.traderType = data.customerNature;
                        this.traderTypeChange();
                    }
                    if (data.areaIds) { // 终端
                        let area = data.areaIds.split(',');
                        if (area.length && area.length == 3) {
                            this.area = area;
                        }
                    }
                } else { // 清空
                    if (this.traderTypeDisabled) {
                        this.traderType = '';
                        this.traderTypeDisabled = false;
                    }
                }
            },
            traderTypeChange () {
                this.positionList = [];
                this.contactPosition = "";

                setTimeout(() => {
                    let list = [];

                    let labels = {
                        465: ['老板', '销售负责人', '销售经理', '采购负责人', '采购经理', '商务人员', '财务人员', '物流人员', '售后人员', '临床工程师', '其他'],
                        466: ['院长', '设备科人员', '临床医生', '采购负责人', '采购经理', '运营人员', '财务人员', '医院库管', '其他']
                    };

                    labels[this.traderType].forEach(item => {
                        list.push({
                            label: item,
                            value: item
                        })
                    })

                    this.positionList = list;
                }, 100);
            },

            /* 手机 */
            handlerPhone (val) {
                console.log('Handler Phone', val);
                if (this.traderId) {
                    if (this.isFromPhone) { // 当前联系人是选择的
                        this.traderContactName = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    } else if (val.choosed) {
                        this.isFromPhone = true;
                        this.traderContactName = val.traderContactName || '';
                        this.traderContactId = val.traderContactId || '';
                    }
                }
            },

            /* 其他联系方式 */
            initOtherContact () {
                let items = this.otherContactInfo.split('##');
                let arr = [];
                items.forEach((item)=> {
                    let key = item.split(':')[0];
                    let val = item.split(':')[1];
                    if (key && val) {
                        arr.push({
                            label: key,
                            value: val,
                            id: Math.random()
                        })
                    }
                })
                this.otherContact = arr;
            },
            addContact () {
                if (this.otherContact.length < 5) {
                    this.$refs.otherContactDialog.show();
                }
            },
            handlerAddContact (val) {
                console.log('handlerAddContact:::::', val);
                this.otherContact.push({
                    label: val,
                    value: '',
                    id: Math.random()
                })
            },
            handlerAddOnInput () {
                let arr = [];
                this.otherContact.forEach(item => {
                    if (item.value.trim()) {
                        arr.push(`${item.label}:${item.value.trim()}`);
                    }
                });
                let str = arr.join('##');
                this.otherContactInfo = str;
            },
            handlerAddDelete (index) {
                this.otherContact.splice(index, 1);
                this.handlerAddOnInput();
            },

            /* 联系人 */
            handlerContact() {
                this.isFromPhone = false;
                this.traderContactId = '';
            },

            /* 拜访地区 */
            handlerArea (data) {
                console.log('Handler Area', data, data.length);
                if (data.length < 3) {
                    this.area = [];
                    this.areaInfo = [];
                } else {
                    this.areaInfo = data;
                    let area = [];
                    data.forEach(item => {
                        if (item.value) {
                            area.push(item.value);
                        }
                    })
                    this.area = area;
                }
            },

            /* 查看地图 */
            openMap () {
                if (this.addressDetail) {
                    let areaStr = '';
                    if (this.area.length) {
                        areaStr = this.areaInfo[0].label + this.areaInfo[1].label + this.areaInfo[2].label || '';
                    }
                    window.location.href = `https://www.amap.com/search?query=${areaStr}${this.addressDetail}`;
                } else if (this.traderName) {
                    window.location.href = `https://www.amap.com/search?query=${this.traderName}`;
                } else {
                    window.location.href = `https://www.amap.com/`;
                }
            },

            /* 拜访人 */
            handlerVisitorChange (data) {
                console.log('Handler Visitor', data);
                this.visitor = this.visitors[0] || '';
                this.visitorItems = data.list[0] || {};
            },
            /* 同行人 */
            handlerTongxingChange (data) {
                console.log('Handler Tongxing', data, this.tongxing)
            },
            // 
            handlerDateChange (data) {
                console.log('Handler planVisitDate:', data, this.planVisitDate);
            },
            disabledDate (current) {
                // 获取今天的日期（去掉时间部分）
                const today = new Date();
                today.setHours(0, 0, 0, 0); // 将时间部分设置为 00:00:00

                // 将传入的日期（current）转换为 Date 对象，并去掉时间部分
                const currentDate = new Date(current);
                currentDate.setHours(0, 0, 0, 0);

                // 如果 currentDate 小于 today，返回 true（禁用）
                return currentDate < today;
            },
            // 拜访目标
            handlerVisit () {
                console.log('Handler Visit', this.visitType);
            },
            /* 线索/商机编号 */
            async checkBusinessNo () {
                if (!this.visitType.includes('B')) {
                    return true;
                }

                let flag = true;
                await this.$axios.post('/crm/visitrecord/m/checkBizNo', {
                    bizNo: this.businessNo,
                    customerName: this.traderName
                }).then(({ data }) => {
                    if (data.success) {
                        flag = data.data.checkResult;
                        this.businessNoMsg = data.data.errorMsg;
                        this.businessNoTip = data.data.warnMsg;

                        if (flag) {
                            this.businessInfo = {
                                relateType: data.data.relateType,
                                bizId: data.data.bizId,
                                bizNo: this.businessNo,
                                customerName: data.data.customerName,
                                salesName: data.data.salesName
                            }
                        } else {
                            this.businessInfo = {
                                relateType: '',
                                bizId: '',
                                bizNo: '',
                                customerName: '',
                                salesName: ''
                            };
                        }
                    }
                });

                if (!flag) {
                    return false;
                }

                this.businessNoMsg = '';
                return true;
            },

            // 验证表单
            formBindValid () {
                if (!this.traderName) {
                    this.errorMsg('请输入客户名称');
                    return false;
                }
                else if (!this.traderType) {
                    this.errorMsg('请选择客户类型');
                    return false;
                }
                else if (!this.validContact()) {
                    return false;
                }
                else if (!this.noContract && !this.traderContactName) {
                    this.errorMsg('请输入联系人');
                    return false;
                }
                else if (!this.noContract && this.traderType && !this.contactPosition) {
                    this.errorMsg('请选择联系人职位');
                    return false;
                }
                else if (!this.area.length) {
                    this.errorMsg('请选择拜访地区');
                    return false;
                }
                else if (this.area.length < 3) {
                    this.errorMsg('省市区不完整');
                    return false;
                }
                else if (!this.visitor) {
                    this.errorMsg('请选择拜访人');
                    return false;
                }
                else if (this.visitor && this.tongxing.length && this.tongxing.indexOf(this.visitor) !== -1) {
                    this.errorMsg('拜访人和同行拜访人请勿重叠');
                    return false;
                }
                else if (!this.planVisitDate) {
                    this.errorMsg('请选择拜访时间');
                    return false;
                }
                else if (!this.visitType.length) {
                    this.errorMsg('请选择拜访目标');
                    return false;
                }
                else if (this.visitType.includes('B') && !this.businessNo) {
                    this.errorMsg('请输入跟进的线索或商机编号');
                    return false;
                }
                return true;
            },
            // 验证·联系方式必填其一
            validContact () {
                if (!this.noContract && !this.mobile.trim() && !this.telephone.trim() && !this.otherContactInfo.trim()) {
                    this.errorMsg('手机、固话和其他联系方式至少填写一项');
                    return false;
                }
                else if (!this.noContract && this.mobile && this.mobile.length !== 11) {
                    this.errorMsg('请输入11位手机号码');
                    return false;
                }
                return true;
            },
            errorMsg (txt) {
                this.$message({ type: 'error', message: txt });
            },
            async submit () {
                let bizNoCheck = await this.checkBusinessNo();
                console.log(' -- submit check -- ', this.cansubmit, this.formBindValid(), bizNoCheck);
                if (!this.cansubmit || !this.formBindValid() || !bizNoCheck) {
                    return;
                }

                let reqData = {
                    id: this.id,
                    visitRecordNo: this.visitRecordNo,
                    traderId: this.traderId,
                    customerName: this.traderName,
                    customerNature: this.traderType,
                    provinceCode: this.areaInfo[0].value,
                    provinceName: this.areaInfo[0].label,
                    cityCode: this.areaInfo[1].value,
                    cityName: this.areaInfo[1].label,
                    areaCode: this.areaInfo[2].value,
                    areaName: this.areaInfo[2].label,
                    visitAddress: this.addressDetail,
                    visitorJobNumber: this.visitor,
                    visitorName: this.visitorItems.name,
                    planVisitDate: this.planVisitDate,
                    visitTarget: this.visitType.join(','),
                    tongxingJobNumbers: this.tongxing,
                    // tongxingIds: this.tongxing,
                    remark: this.remark,
                    noContract: this.noContract ? 'Y' : 'N'
                }

                if (!this.noContract) {
                    reqData.contactName = this.traderContactName;
                    reqData.contactMobile = this.mobile;
                    reqData.contactTele = this.telephone;
                    reqData.contactPosition = this.contactPosition;
                    reqData.otherContact = this.otherContactInfo;
                }

                if (this.visitType.includes('B') && this.businessNo) {
                    reqData.relateBizData = {
                        relateType: this.businessInfo.relateType,
                        bizId: this.businessInfo.bizId,
                        bizNo: this.businessNo
                    };
                }

                if (this.copyid) {
                    reqData.preId = this.copyid;
                }

                this.cansubmit = false;
                this.$axios.post('/crm/visitrecord/m/save', reqData).then(({data}) => {
                    if (data.success) {
                        window.location.replace("/crm/visitRecord/m/detail?id=" + data.data.id);
                    } else {
                        this.cansubmit = true;
                        this.$message({
                            message: data.message,
                            type: 'error'
                        })
                    }
                })

            },


            // DEMO
            // successDialog() {
            //     this.$dialog({
            //         type: 'success',
            //         message: '添加成功，谢谢合作，祝您每天愉快!',
            //         buttons: [{
            //             txt: '谢谢平台'
            //         }]
            //     })
            // },
            // errorDialog() {
            //     this.$dialog({
            //         type: 'error',
            //         message: '文件删除后不可恢复，确定删除选中的文件？',
            //         buttons: [{
            //             txt: '取消',
            //             btnClass: 'cancel',
            //         },  {
            //             txt: '删除',
            //             btnClass: 'delete',
            //         }]
            //     })
            // },
            // warnDialog() {
            //     this.$dialog({
            //         type: 'warn',
            //         message: '提交后将完成拜访计划，拜访计划和记录将不可修改，确认继续提交？',
            //         buttons: [ {
            //             txt: '取消'
            //         }, {
            //             txt: '提交',
            //             btnClass: 'confirm',
            //         }]
            //     })
            // },
            // infoDialog() {
            //     this.$dialog({
            //         type: 'info',
            //         message: '拜访计划由拜访人 Aadi.chen 添加和编辑',
            //         buttons: [ {
            //             txt: '我知道了',
            //             btnClass: 'confirm'
            //         }]
            //     })
            // },
            // customDialog () {
            //     console.log('open custom dialog');
            //     this.isShowCustomDialog = true;
            // },
            // message() {
            //     this.$message({
            //         message: '消息提示',
            //     })
            // },
            // successMessage() {
            //     this.$message({
            //         type: 'success',
            //         message: '消息提示',
            //     })
            // },
            // errorMessage() {
            //     this.$message({
            //         type: 'error',
            //         message: '消息提示',
            //     })
            // },
            // warnMessage() {
            //     this.$message({
            //         type: 'warn',
            //         message: '消息提示',
            //     })
            // },
            // infoMessage () {
            //     this.$message({
            //         type: 'info',
            //         message: '消息提示',
            //     })
            // },
            // handleLeftBtn() {
            //     console.log('left btn');
            // },
            // handleRightBtn() {
            //     console.log('left btn');
            // },

        }
    })
}.call(this);