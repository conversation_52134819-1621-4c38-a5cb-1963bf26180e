package com.vedeng.erp.trader.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.erp.trader.service.TraderSupplierBizService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description 客户信息
 * @date 2022/7/15 10:51
 **/
@ExceptionController
@RestController
@RequestMapping("/traderTraderSupplier")
@Slf4j
public class TraderSupplierApi {

    @Autowired
    private TraderSupplierBizService traderSupplierBizService;

    /**
     * 获取供应商基本信息
     * @param traderId 客户id
     * @return 信息
     */
    @RequestMapping(value = "/getTraderSupplierInfo",method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> getTraderSupplierInfo(@RequestParam Integer traderId) {
        return R.success(traderSupplierBizService.getTraderSupplierInfoById(traderId));
    }
}
