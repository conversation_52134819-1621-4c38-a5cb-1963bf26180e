package com.vedeng.infrastructure.kingdee.common;


import com.vedeng.infrastructure.kingdee.common.sdk.entity.ConstDefine;

import java.util.HashMap;

public class ApiQueryRequester extends ApiRequester {
    /* JADX INFO: Access modifiers changed from: package-private */
    public ApiQueryRequester(String uri) {
        super(uri);
    }

    @Override // com.kingdee.bos.webapi.sdk.ApiRequester
    public HashMap<String, String> buildHeader(String path) {
        HashMap<String, String> headers = super.buildHeader(path);
        headers.put(ConstDefine.BEGINMETHOD_Header, ConstDefine.BEGINMETHOD_Method);
        headers.put(ConstDefine.QUERYMETHOD_Header, ConstDefine.QUERYMETHOD_Method);
        return headers;
    }
}
