package com.vedeng.order.service;

import com.vedeng.common.service.BaseService;
import com.vedeng.order.model.dto.OrderReviewProcessDto;
import com.vedeng.order.model.po.OrderReviewProcessPo;

import java.util.List;

/**
 * 订单审核流管理接口
 */
public interface OrderReviewProcessService extends BaseService {

    /**
     * 保存审核流相关记录
     *
     * @param orderReviewProcessDto
     * @return
     */
    boolean saveOrderReviewProcesses(OrderReviewProcessDto orderReviewProcessDto);

    /**
     * 获取所有审核记录日志
     *
     * @return
     */
    List<OrderReviewProcessPo> getAllOrderReviewProcess();

    /**
     * 处理销售订单修改审核信息
     *
     * @param orderId
     * @param userId
     * @return
     */
    boolean dealSaleOrderReviewProcess(Integer orderId, Integer userId) ;

    /**
     * 处理采购订单修改审核信息
     *
     * @param orderId
     * @param userId
     * @return
     */
    boolean dealBuyOrderReviewProcess(Integer orderId, Integer userId) ;
}
