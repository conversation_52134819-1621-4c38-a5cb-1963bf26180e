$(function() {
	var htmlEncode = function (s){
		s = s.replace(/&/g, "&amp;");//必须在第一个位置
		s = s.replace(/</g, "&lt;");
		s = s.replace(/>/g, "&gt;");
		s = s.replace(/'/g, "&#39;");
		s = s.replace(/"/g, "&quot;");

		return s;
	}
	var sel = xmSelect.render({
		el: '#mx-select',
		tips: '输入选择',
		searchTips: '请输入',
		style: {
			marginLeft: '0',
			width: '200px'
		},
		direction: 'down',
		clickClose: true,
		radio: true,
		height: '300px',
		name: 'unitKingDee',
		autoRow: true,
		filterable: true,
		paging: true,
		pageSize: 10,
		remoteSearch: true,
		model: {
			icon: 'hidden',
			label: {
				type: 'text'
			}
		},
		remoteMethod: function (val, cb, show) {
			console.log(val)
			$.ajax({
				type: "post",
				url: "./allUnitKingDeeList.do",
				data: {"name": val},
				dataType: "json",
				success: function (data) {
					var arr = [];
					console.log(data)
					if (data.data.length > 0) {
						data.data.forEach(function (item) {
							var i = {
								value: htmlEncode(item.unitKingDeeNo),
								name: htmlEncode(item.unitKingDeeNo) + "-" + htmlEncode(item.unitKingDeeName),
							};
							arr.push(i)
						})

					}
					cb(arr)
				},
				error: function (data) {
					cb([])
				}
			});
		},
		on: function (data) {
			if (data.arr.length > 0) {
				$('#unitKingDeeNo').val(data.arr[0].value);
				$('#unitKingDeeName').val(data.arr[0].name);
			}
		}
	});
	if ($(".J-No xmp").html() !== "" ){
		sel.setValue([{name:htmlEncode($(".J-No xmp").html()+'-'+$(".J-Name xmp").html()),value:htmlEncode($(".J-No xmp").html())}])
	}

	var $form = $("#editUnitform");
	$form.submit(function() {
		checkLogin();
		$(".warning").remove();
		$.ajax({
			async:false,
			url:'./editUnit.do',
			data:$form.serialize(),
			type:"POST",
			dataType : "json",
			beforeSend:function(){
				var unitKingDeeNo = $("#unitKingDeeNo").val().trim();
				var unitKingDeeName = $("#unitKingDeeName").val().trim();
				var unitName = $("#unitName").val().trim();
				var unitNameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,]{1,16}$/;
				var unitNameEnReg = /^[A-Za-z]+$/;
				if (unitName.length==0) {
					warnTips("unitName","单位名称不能为空");//文本框ID和提示用语
					return false;
				}				
				if(unitName.length < 1 || unitName.length > 16){
					warnTips("unitName","单位名称不允许超过16个字符");
					return false;
				}

				if (unitKingDeeNo.length==0) {
					warnTips("unitKingDeeNo","金蝶单位不能为空");//文本框ID和提示用语
					return false;
				}

				if(!unitNameReg.test(unitName)){
					warnTips("unitName","单位名称不允许使用特殊字符");
					return false;
				}				
				var unitNameEn = $("#unitNameEn").val().trim();
				if(unitNameEn.length != 0 && (unitNameEn.length < 1 || unitNameEn.length > 16)){
					warnTips("unitNameEn","英文单位名称不允许超过16个字符");
					return false;
				}				
				if(unitNameEn.length != 0 && !unitNameEnReg.test(unitNameEn)){
					warnTips("unitNameEn","英文单位名称只允许使用英文字符");
					return false;
				}				
				var sort = $("#sort").val().trim();
				if (sort.length==0) {
					warnTips("sort","排序编号不能为空");//文本框ID和提示用语
					return false;
				}else{
					var re = /^[0-9]{1,4}$/;
					if(!re.test(sort)){
						warnTips("sort","排序编号必须是0-9999之间的正整数");//文本框ID和提示用语
						return false;
					}
				}
			},
			success:function(data){
				refreshPageList(data);
			},
			error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		})
		return false;
	});


});