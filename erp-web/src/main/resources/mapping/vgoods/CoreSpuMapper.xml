<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CoreSpuMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CoreSpu">
        <!--
          WARNING - @mbggenerated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Jun 27 19:06:50 CST 2019.
        -->
        <id column="SPU_ID" jdbcType="INTEGER" property="spuId" />
        <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId" />
        <result column="BRAND_ID" jdbcType="INTEGER" property="brandId" />
        <result column="SPU_NO" jdbcType="VARCHAR" property="spuNo" />
        <result column="SPU_NAME" jdbcType="VARCHAR" property="spuName" />
        <result column="SHOW_NAME" jdbcType="VARCHAR" property="showName" />
        <result column="SPU_LEVEL" jdbcType="TINYINT" property="spuLevel" />
        <result column="STATUS" jdbcType="TINYINT" property="status" />
        <result column="SPU_TYPE" jdbcType="INTEGER" property="spuType" />
        <result column="FIRST_ENGAGE_ID" jdbcType="INTEGER" property="firstEngageId" />
        <result column="REGISTRATION_ICON" jdbcType="VARCHAR" property="registrationIcon" />
        <result column="WIKI_HREF" jdbcType="VARCHAR" property="wikiHref" />
        <result column="OPERATE_INFO_FLAG" jdbcType="TINYINT" property="operateInfoFlag" />
        <result column="CHECK_STATUS" jdbcType="TINYINT" property="checkStatus" />
        <result column="OPERATE_INFO_ID" jdbcType="INTEGER" property="operateInfoId" />
        <result column="HOSPITAL_TAGS" jdbcType="VARCHAR" property="hospitalTags" />
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
        <result column="CREATOR" jdbcType="INTEGER" property="creator" />
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
        <result column="UPDATER" jdbcType="INTEGER" property="updater" />
        <result column="CHECK_TIME" jdbcType="TIMESTAMP" property="checkTime" />
        <result column="CHECKER" jdbcType="INTEGER" property="checker" />
        <result column="DELETE_REASON" jdbcType="VARCHAR" property="deleteReason" />
        <result column="LAST_CHECK_REASON" jdbcType="VARCHAR" property="lastCheckReason" />
        <result column="ASSIGNMENT_MANAGER_ID" jdbcType="INTEGER" property="assignmentManagerId" />
        <result column="ASSIGNMENT_ASSISTANT_ID" jdbcType="INTEGER" property="assignmentAssistantId" />
    </resultMap>


    <select id="getSpuBySku" resultType="com.vedeng.goods.model.CoreSpu">
        SELECT P.* FROM V_CORE_SPU P LEFT JOIN V_CORE_SKU K ON P.SPU_ID = K.SPU_ID WHERE K.SKU_NO = #{sku}
    </select>


    <select id="getPushStatusCountBySpuId" resultType="java.lang.Integer">
        SELECT
            IFNULL( SUM( IFNULL( T2.PUSH_STATUS, 0 ) ), 0 )
        FROM
            V_CORE_SPU T1
            LEFT JOIN V_CORE_SKU T2 ON T1.SPU_ID = T2.SPU_ID
        WHERE
            T1.`STATUS` = 1
            AND T2.`STATUS` = 1
            AND T1.SPU_ID = #{spuId,jdbcType=INTEGER}
    </select>
    <select id="getSpuinfoById" resultType="com.vedeng.goods.model.CoreSpu">
        SELECT *
        FROM V_CORE_SPU
        WHERE SPU_ID = #{logBizId,jdbcType = INTEGER}
    </select>

    <select id="selectOrderAssistantByNo" resultType="java.lang.Integer">
        SELECT B.ORDER_ASSISTANT_USER_ID FROM V_CORE_SPU A
        LEFT JOIN T_ORDER_ASSISTANT_RELATION B ON A.ASSIGNMENT_MANAGER_ID = B.PRODUCT_MANAGER_USER_ID AND A.ASSIGNMENT_ASSISTANT_ID = B.PRODUCT_ASSITANT_USER_ID
        WHERE SPU_NO = #{spu,jdbcType=VARCHAR}
    </select>
    <select id="getSpuInfoBySkuNos" resultType="com.vedeng.goods.model.CoreSpu">
        select SPU.*
        from V_CORE_SPU SPU
        left join V_CORE_SKU SKU ON SPU.SPU_ID= SKU.SPU_ID
        WHERE SKU.SKU_NO IN
        <foreach collection="skuNos" open="(" close=")" separator="," item="skuNo">
            #{skuNo,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getValidingSpuInfoBySpuNo" resultType="com.vedeng.goods.model.CoreSpu">
        select *
        from V_CORE_SPU
        where CHECK_STATUS in (1)
        and SPU_NO = #{spuNo,jdbcType=VARCHAR}
    </select>
    <select id="getSpuInfoBySpuNo" resultType="com.vedeng.goods.model.CoreSpu">
         select *
        from V_CORE_SPU
        where
        SPU_NO = #{spuNo,jdbcType=VARCHAR}
    </select>

    <update id="updateSpuAfterDisableSku">
        UPDATE V_CORE_SPU
        SET UPDATER = #{creator,jdbcType = INTEGER}, MOD_TIME = #{addTime,jdbcType = BIGINT}, DISABLED_REASON = #{disabledReason,jdbcType = VARCHAR},
        STATUS = #{status,jdbcType = INTEGER}, CHECK_STATUS = #{checkStatus,jdbcType = INTEGER}, UPDATER = #{updater,jdbcType = INTEGER}, MOD_TIME = #{modTime,jdbcType = TIMESTAMP}
        WHERE SPU_ID = #{spuId,jdbcType = INTEGER}
    </update>
    <select id="listSpu" resultType="com.vedeng.goods.model.CoreSpu">
        select * from V_CORE_SPU
    </select>
    <select id="listSpuByModTime" resultType="com.vedeng.goods.model.CoreSpu">
        SELECT
            SPU_ID,
            SPU_TYPE
        FROM
            V_CORE_SPU
        WHERE
            SPU_ID IN ( SELECT SPU_ID FROM V_CORE_SKU WHERE date( MOD_TIME ) = date_sub( #{modTime}, INTERVAL 1 DAY ) )
    </select>
</mapper>