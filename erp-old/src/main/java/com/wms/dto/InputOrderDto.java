package com.wms.dto;

import java.util.List;

/**
 * 入库单dto
 */
public class InputOrderDto extends AbstractCommonDto{

    //WMS单据编号
    private String ASNNO;

    //ERP单据编号
    private String ASNReference1;

    //订单类型
    private String ASNType;

    private List<InputOrderGoodsDto> details;

    public String getASNNO() {
        return ASNNO;
    }

    public void setASNNO(String ASNNO) {
        this.ASNNO = ASNNO;
    }

    public String getASNReference1() {
        return ASNReference1;
    }

    public void setASNReference1(String ASNReference1) {
        this.ASNReference1 = ASNReference1;
    }

    public String getASNType() {
        return ASNType;
    }

    public void setASNType(String ASNType) {
        this.ASNType = ASNType;
    }

    public List<InputOrderGoodsDto> getDetails() {
        return details;
    }

    public void setDetails(List<InputOrderGoodsDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "InputOrderDto{" +
                "ASNNO='" + ASNNO + '\'' +
                ", ASNReference1='" + ASNReference1 + '\'' +
                ", ASNType='" + ASNType + '\'' +
                ", details=" + details +
                '}';
    }
}
