package com.vedeng.erp.trader.feign;

import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.feign.annotations.FeignApi;
import com.vedeng.onedataapi.api.link.req.DistributorLinkReqV2Dto;
import com.vedeng.onedataapi.api.link.req.TerminalLibLinkReqDto;
import com.vedeng.onedataapi.api.link.req.TerminalLinkReqV2Dto;
import com.vedeng.onedataapi.api.link.res.DistributorLinkResV2;
import com.vedeng.onedataapi.api.link.res.TerminalLinkResV2;
import feign.Headers;
import feign.RequestLine;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Description 大数据 经销/终端链路 接口
 * @Date 2023/9/14 14:05
 */
@FeignApi(serverName = "onedataapi")
public interface OneDataDistributorTerminalLinkApi {

    /**
     * 终端链路
     *
     * @param var1 TerminalLinkReqDto
     * @return TerminalLinkResV2
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /v2/link/terminal")
    RestfulResult<TerminalLinkResV2> queryTerminalLink(@RequestBody TerminalLinkReqV2Dto var1);

    /**
     * 经销链路
     *
     * @param var1 DistributorLinkReqDto
     * @return DistributorLinkResV2
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /v2/link/distributor")
    RestfulResult<DistributorLinkResV2> queryDistributorLink(@RequestBody DistributorLinkReqV2Dto var1);

    /**
     * 360终端经销链路
     *
     * @param var1 TerminalLibLinkReqDto
     * @return TerminalLinkResV2
     */
    @Headers({"Content-Type: application/json", "Accept: application/json"})
    @RequestLine("POST /link/distributor/terminalLib/")
    RestfulResult<TerminalLinkResV2> queryTerminalLibLink(@RequestBody TerminalLibLinkReqDto var1);
}
