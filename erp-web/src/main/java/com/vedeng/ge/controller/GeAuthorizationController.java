package com.vedeng.ge.controller;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.RUserRole;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.OrderNoDict;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.common.constant.GeAuthorizationConstant;
import com.vedeng.erp.buyorder.domain.entity.GeAuthorization;
import com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate;
import com.vedeng.erp.buyorder.domain.entity.GeActionLog;
import com.vedeng.erp.buyorder.domain.entity.GeAuthorizationVerify;
import com.vedeng.erp.buyorder.service.GeAuthorizationService;
import com.vedeng.system.service.UserService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ge授权书
 * <AUTHOR>
 * @date 2022/2/14 13:12
 **/
@Controller
@RequestMapping("/ge/authorization")
@Slf4j
public class GeAuthorizationController extends BaseController {

    @Autowired
    private UserService userService;

    @Autowired
    private GeAuthorizationService geAuthorizationService;


    @Value("${oss_url}")
    private String ossUrl;

    @Autowired
    private OrderNoDict orderNoDict;

    /**
     * 授权书编辑、新增页面
     * @param authorizationId 授权书id
     * @param geBussinessChanceId 商机id
     * @return
     */
    @RequestMapping("/editview")
    @FormToken(save = true)
    public ModelAndView editView(@RequestParam(value = "authorizationId",required = false) Integer authorizationId, @RequestParam("geBussinessChanceId") Integer geBussinessChanceId) {

        ModelAndView mv = new ModelAndView("ge/edit_authorization");
        mv.addObject("domain", ossUrl);
        if (authorizationId != null) {
            mvBindAuthorizationBaseData(authorizationId, mv);
            return mv;
        }
        boolean flag = geAuthorizationService.checkBusinessChance(geBussinessChanceId);
        if (!flag) {
            ModelAndView mv2 = new ModelAndView();
            mv2.addObject("message","商机不可跟进不可创建授权书");
            return fail(mv2);
        }
        GeAuthorization geAuthorization = new GeAuthorization();
        geAuthorization.setGeBussinessChanceId(geBussinessChanceId);
        List<GeAuthorization> geAuthorizations = geAuthorizationService.countGeBussinessChanceHaveNoCloseGeAuthorization(geAuthorization);
        if (!CollectionUtils.isEmpty(geAuthorizations)) {
//            ResultInfo<Object> objectResultInfo = new ResultInfo<>();
//            objectResultInfo.setCode(2);
//            objectResultInfo.setData(geAuthorizations.get(0).getAuthorizationId());
//            return objectResultInfo;
            mv.addObject("url", "/ge/authorization/itemView.do?authorizationId="+geAuthorizations.get(0).getAuthorizationId());
            mv.addObject("message","此商机已存在未关闭的授权书"+geAuthorizations.get(0).getAuthorizationNo()+"，请不要重新创建授权书");
            return fail(mv);
        }
        mv.addObject("geBussinessChanceId", geBussinessChanceId);
        return mv;
    }


    /**
     * 授权书详情页
     * @param authorizationId 授权书id
     * @return
     */
    @RequestMapping("/itemView")
    @FormToken(save = true)
    public ModelAndView itemView(@RequestParam(value = "authorizationId") Integer authorizationId) {

        if (authorizationId == null) {
            throw new ServiceException("授权书id不可为空");
        }
        ModelAndView mv = new ModelAndView("ge/view_authorization");
        User userById = (User) ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes())
                .getRequest().getSession().getAttribute(ErpConst.CURR_USER);
        List<RUserRole> rUserRoleListByUserId = userService.getRUserRoleListByUserId(userById.getUserId());
        List<Integer> collect = rUserRoleListByUserId.stream().map(RUserRole::getRoleId).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            // GE角色id250
            if (collect.contains(GeAuthorizationConstant.GE_ROLE)) {
                mv.addObject("geRole", true);
            }
        }
        // 基础信息
        mvBindAuthorizationBaseData(authorizationId, mv);
        // 日志信息
        List<GeActionLog> geActionLogData = geAuthorizationService.getGeAuthorizationLogData(authorizationId);
        // 审核记录
        List<GeAuthorizationVerify> geAuthorizationVerifyData = geAuthorizationService.getGeAuthorizationVerifyData(authorizationId);
        mv.addObject("geAuthorizationVerifyData", geAuthorizationVerifyData);
        mv.addObject("geAuthorizationLogs", geActionLogData);
        mv.addObject("authorizationId", authorizationId);
        mv.addObject("domain", ossUrl);

        // 权限信息 供应链 销售 GE
        return mv;
    }

    /**
     * 申请审核接口
     * @param authorizationId 授权书id
     * @param request
     * @return
     */
    @RequestMapping("/doForAuditAndClose")
    @ResponseBody
    public ResultInfo doForAuditAndClose(@RequestParam(value = "authorizationId") Integer authorizationId,Integer status,HttpServletRequest request) {

        if (authorizationId == null) {
            throw new ServiceException("授权书id不可为空");
        }
        if (status == null) {
            throw new ServiceException("授权书状态不可为空");
        }
        User user = getSessionUser(request);
        if (null == user || user.getUserId() == null) {
            throw new ServiceException("请重新登录");
        }
        User userById = userService.getUserById(user.getUserId());

        // 状态改变
        boolean b = geAuthorizationService.doForAudit(authorizationId,status);
        // 日志
        if (b) {
            GeAuthorizationVerify geAuthorizationVerify = new GeAuthorizationVerify();
            geAuthorizationVerify.setAuthorizationId(authorizationId);
            geAuthorizationVerify.setAddTime(new Date());
            geAuthorizationVerify.setCreator(userById.getUserId());
            geAuthorizationVerify.setCreatorName(userById.getRealName());
            if (status == 1) {
                geAuthorizationVerify.setStats(2);
            }
            if (status == 4) {
                geAuthorizationVerify.setStats(3);
            }
            geAuthorizationService.saveGeAuthorizationAuitLog(geAuthorizationVerify);

            return ResultInfo.success(1);
        }
        return ResultInfo.error(0);
    }



    /**
     * 审核接口
     * @param geAuthorization 授权书
     * @param request
     * @return
     */
    @RequestMapping("/makeAudit")
    @ResponseBody
    @MethodLock(className = GeAuthorization.class,field = "authorizationId")
    public ResultInfo makeAudit(GeAuthorization geAuthorization,String message,HttpServletRequest request) {

        if (null!=geAuthorization && geAuthorization.getAuthorizationId() == null) {
            throw new ServiceException("授权书id不可为空");
        }

        User user = getSessionUser(request);
        if (null == user || user.getUserId() == null) {
            throw new ServiceException("请重新登录");
        }
        User userById = userService.getUserById(user.getUserId());
        // 状态改变
        boolean b = geAuthorizationService.makeAudit(geAuthorization);
        // 日志
        if (b) {
            GeAuthorizationVerify geAuthorizationVerify = new GeAuthorizationVerify();
            geAuthorizationVerify.setAuthorizationId(geAuthorization.getAuthorizationId());
            geAuthorizationVerify.setAddTime(new Date());
            geAuthorizationVerify.setCreator(userById.getUserId());
            geAuthorizationVerify.setCreatorName(userById.getRealName());
            if (geAuthorization.getStatus()==2) {
                geAuthorizationVerify.setStats(1);
            }
            if(geAuthorization.getStatus()==3){
                geAuthorizationVerify.setStats(0);
            }
            geAuthorizationVerify.setContent(message);
            geAuthorizationService.saveGeAuthorizationAuitLog(geAuthorizationVerify);

            return ResultInfo.success(1);
        }
        return ResultInfo.error(0);
    }



    /**
     * 授权书保存接口，跳转详情页
     * @param request
     * @param geAuthorization
     * @param response
     * @throws IOException
     */
    @RequestMapping(value = "/saveAuthorization")
    @SystemControllerLog(operationType = "edit", desc = "保存授权书")
//    @ResponseBody
    public ModelAndView saveAuthorization(HttpServletRequest request, GeAuthorization geAuthorization, HttpServletResponse response) throws IOException {
        ModelAndView mv = new ModelAndView();
        User user = getSessionUser(request);
        if (null == user || user.getUserId() == null) {
            throw new ServiceException("请重新登录");
        }
        User userById = userService.getUserById(user.getUserId());

        // 新授权书保存
        if (geAuthorization != null && geAuthorization.getAuthorizationId() == null) {
            // 保存主体信息
            geAuthorization.setStatus(0);
            geAuthorization.setCreator(userById.getUserId());
            geAuthorization.setCreatorName(userById.getRealName());
            geAuthorization.setUpdater(userById.getUserId());
            geAuthorization.setUpdaterName(user.getRealName());
            geAuthorization.setAddTime(new Date());
            geAuthorization.setUpdateTime(new Date());

            // get no from redis
            // String geAuthorizationNoByRedis = orderNoDict.getGeAuthorizationNoByRedis("myGeAuthorizationNoByRedis", "GESQ", 3);
            String geAuthorizationNoByRedis = orderNoDict.getOrderNum(null, 27);
            geAuthorization.setAuthorizationNo(geAuthorizationNoByRedis);
            log.info("geAuthorization:{}",geAuthorization);
            geAuthorizationService.saveGeAuthorization(geAuthorization);
            if (geAuthorization.getAuthorizationId() != null) {
                // save new file info
                saveNewFile(request, geAuthorization, userById);
                // save log
                GeActionLog geActionLog = new GeActionLog();
                geActionLog.setAddTime(new Date());
                geActionLog.setContent("新增授权书信息");
                geActionLog.setCreator(userById.getUserId());
                geActionLog.setCreatorName(userById.getRealName());
                geActionLog.setRelatedId(geAuthorization.getAuthorizationId());
                geActionLog.setRelatedType(2);
                geAuthorizationService.saveAuthorizationLog(geActionLog);
            }

        } else if (geAuthorization != null && geAuthorization.getAuthorizationId() != null) {
            // 更新 状态改为待审核
            geAuthorization.setStatus(0);
            geAuthorization.setUpdater(userById.getUserId());
            geAuthorization.setUpdaterName(user.getRealName());
            geAuthorization.setAddTime(new Date());
            geAuthorization.setUpdateTime(new Date());

            // 更新主体信息
            geAuthorizationService.updateAuthorizationById(geAuthorization);
            // 去除老图附件，保存新图
            geAuthorizationService.deleteOldTypeFile(geAuthorization);
            saveNewFile(request, geAuthorization, userById);
            // save log
            String[] changes = request.getParameterValues("change");
            if (changes != null) {
                String logStr = geAuthorizationService.getGeAuthorizationLogContent(changes);
                if (!StringUtils.isEmpty(logStr)) {
                    GeActionLog geActionLog = new GeActionLog();
                    geActionLog.setAddTime(new Date());
                    geActionLog.setContent(logStr);
                    geActionLog.setCreator(userById.getUserId());
                    geActionLog.setCreatorName(userById.getRealName());
                    geActionLog.setRelatedId(geAuthorization.getAuthorizationId());
                    geActionLog.setRelatedType(2);
                    geAuthorizationService.saveAuthorizationLog(geActionLog);
                }
            }
        }
        if (geAuthorization.getAuthorizationId() != null) {
                mv.addObject("url", "/ge/authorization/itemView.do?authorizationId="+geAuthorization.getAuthorizationId());
                return success(mv);

//            return ResultInfo.success(geAuthorization.getAuthorizationId());
//            response.sendRedirect("/ge/authorization/itemView.do?authorizationId="+geAuthorization.getAuthorizationId());
        }
        List<GeAuthorization> geAuthorizations = geAuthorizationService.countGeBussinessChanceHaveNoCloseGeAuthorization(geAuthorization);
        if (!CollectionUtils.isEmpty(geAuthorizations)) {
//            ResultInfo<Object> objectResultInfo = new ResultInfo<>();
//            objectResultInfo.setCode(2);
//            objectResultInfo.setData(geAuthorizations.get(0).getAuthorizationId());
//            return objectResultInfo;
            mv.addObject("url", "/ge/authorization/itemView.do?authorizationId="+geAuthorizations.get(0).getAuthorizationId());
            mv.addObject("message","此商机已存在未关闭的授权书"+geAuthorizations.get(0).getAuthorizationNo()+"，请不要重新创建授权书");
            return fail(mv);
        }
        mv.addObject("message","此商机已存在未关闭的授权书，请不要重新创建授权书");
        return fail(mv);
    }

    /**
     * 8种文件信息存储
     * @param request
     * @param geAuthorization
     * @param userById
     */
    private void saveNewFile(HttpServletRequest request, GeAuthorization geAuthorization, User userById) {
        // 营业执照
        List<GeAuthorizationCertificate> geAuthorizationCertificates = geAuthorizationService.saveYyFile(request, geAuthorization, userById.getUserId());
        log.info("geAuthorizationCertificates:{}", JSON.toJSONString(geAuthorizationCertificates));

        // 二类
        List<GeAuthorizationCertificate> elAuthorizationCertificates = geAuthorizationService.saveElFile(request, geAuthorization, userById.getUserId());
        log.info("elAuthorizationCertificates:{}", JSON.toJSONString(elAuthorizationCertificates));

        // 三类
        List<GeAuthorizationCertificate> slAuthorizationCertificates = geAuthorizationService.saveSlFile(request, geAuthorization, userById.getUserId());
        log.info("slAuthorizationCertificates:{}", JSON.toJSONString(slAuthorizationCertificates));

        // 诚信
        List<GeAuthorizationCertificate> cxAuthorizationCertificates = geAuthorizationService.saveCxFile(request, geAuthorization, userById.getUserId());
        log.info("cxAuthorizationCertificates:{}", JSON.toJSONString(cxAuthorizationCertificates));

        // 使用二级分销商声明
        List<GeAuthorizationCertificate> syAuthorizationCertificates = geAuthorizationService.saveSyFile(request, geAuthorization, userById.getUserId());
        log.info("syAuthorizationCertificates:{}", JSON.toJSONString(syAuthorizationCertificates));

        // 信用查询结果
        List<GeAuthorizationCertificate> xyAuthorizationCertificates = geAuthorizationService.saveXyFile(request, geAuthorization, userById.getUserId());
        log.info("xyAuthorizationCertificates:{}", JSON.toJSONString(xyAuthorizationCertificates));

        // 招标文件
        List<GeAuthorizationCertificate> zbAuthorizationCertificates = geAuthorizationService.saveZbFile(request, geAuthorization, userById.getUserId());
        log.info("zbAuthorizationCertificates:{}", JSON.toJSONString(zbAuthorizationCertificates));

        // 其他附件
        List<GeAuthorizationCertificate> qtAuthorizationCertificates = geAuthorizationService.saveQtFile(request, geAuthorization, userById.getUserId());
        log.info("qtAuthorizationCertificates:{}", JSON.toJSONString(qtAuthorizationCertificates));
    }


    /**
     * 将授权书基础信息绑入MV
     * @param authorizationId 授权书id
     * @param mv 视图对象
     */
    private void mvBindAuthorizationBaseData(@NonNull Integer authorizationId, @NonNull ModelAndView mv) {
        Map<String, Object> geAuthorizationData = geAuthorizationService.getGeAuthorizationData(authorizationId);
        mv.addObject("yy", geAuthorizationData.get("yy"));
        mv.addObject("el", geAuthorizationData.get("el"));
        mv.addObject("sl", geAuthorizationData.get("sl"));
        mv.addObject("cx", geAuthorizationData.get("cx"));
        mv.addObject("sy", geAuthorizationData.get("sy"));
        mv.addObject("xy", geAuthorizationData.get("xy"));
        mv.addObject("zb", geAuthorizationData.get("zb"));
        mv.addObject("qt", geAuthorizationData.get("qt"));
        mv.addObject("geAuthorization", geAuthorizationData.get("geAuthorization"));
        mv.addObject("geBusinessChance", geAuthorizationData.get("geBusinessChance"));
        mv.addObject("authorizationId", authorizationId);
    }
}
