package com.vedeng.erp.broadcast.service.impl;

import java.io.IOException;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.erp.broadcast.service.QrMessage;
import com.vedeng.erp.common.broadcast.param.QwMessageParam;


/**
 * 发送企微消息
 * @ClassName:  QrMessageImpl   
 * @author: <PERSON>.yang
 * @date:   2025年6月6日 下午4:03:36    
 * @Copyright:
 */
@Service
public class QrMessageImpl implements QrMessage {
	
	//日志
	public static Logger LOGGER = LoggerFactory.getLogger(QrMessageImpl.class);

	@Override
	public void sendMessage(List<QwMessageParam> qwMessageParamList, String webHookUrl) {
		try {
			for (QwMessageParam qwMessageParam : qwMessageParamList) {
				String json = JsonUtils.translateToJson(qwMessageParam);
				NewHttpClientUtils.httpPost(webHookUrl, json);
			}
		} catch (IOException e) {
			LOGGER.error("发送企微通知失败",e);;
		}
	}

}
