package com.wms.dto;

import java.util.List;

/**
 * 库存调整单DTO
 */
public class InventoryAdjustmentDto extends AbstractCommonDto {
    /**
     * 库存调整单号
     */
    private String ADJNo;

    /**
     * 库存调整单类型
     */
    private String adjType;

    /**
     * 调整单创建时间
     */
    private String adjCreationTime;

    /**
     * 原因代码
     */
    private String reasonCode;

    /**
     * 调整原因
     */
    private String reason;

    /**
     * 调整单详情
     */
    private List<InventoryAdjustmentDetailDto> details;

    public String getADJNo() {
        return ADJNo;
    }

    public void setADJNo(String ADJNo) {
        this.ADJNo = ADJNo;
    }

    public String getAdjType() {
        return adjType;
    }

    public void setAdjType(String adjType) {
        this.adjType = adjType;
    }

    public String getAdjCreationTime() {
        return adjCreationTime;
    }

    public void setAdjCreationTime(String adjCreationTime) {
        this.adjCreationTime = adjCreationTime;
    }

    public String getReasonCode() {
        return reasonCode;
    }

    public void setReasonCode(String reasonCode) {
        this.reasonCode = reasonCode;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public List<InventoryAdjustmentDetailDto> getDetails() {
        return details;
    }

    public void setDetails(List<InventoryAdjustmentDetailDto> details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "InventoryAdjustmentDto{" +
                "ADJNo='" + ADJNo + '\'' +
                ", adjType='" + adjType + '\'' +
                ", adjCreationTime='" + adjCreationTime + '\'' +
                ", reasonCode='" + reasonCode + '\'' +
                ", reason='" + reason + '\'' +
                ", details=" + details +
                '}';
    }
}
