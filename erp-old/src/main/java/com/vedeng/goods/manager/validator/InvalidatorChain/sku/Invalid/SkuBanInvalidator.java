package com.vedeng.goods.manager.validator.InvalidatorChain.sku.Invalid;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.exception.InvalidatorChainException;
import com.vedeng.goods.manager.validator.GoodsInvalidatorChain;
import com.vedeng.goods.model.CoreSku;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.vedeng.goods.manager.validator.enums.GoodsInvalidatorErrorEnum.SkuBanInvalidatorError;
import static com.vedeng.goods.manager.constants.GoodsValidConstants.*;

/**
 * .(3)	该SKU在所有的平台（除了区域商城平台）上为全部下架状态，或者未推送状态；
 * @jira: .
 * @notes: .
 * @version: 1.0.
 * @date: 2021/9/1 16:00.
 * @author: <PERSON><PERSON>.
 */
@Service
public class SkuBanInvalidator extends GoodsInvalidatorChain {

    @Resource
    private CoreSkuMapper coreSkuMapper;


    @Override
    public void doInvalid(){
        String skuNo = getSkuNo();
        CoreSku sku = coreSkuMapper.selectBySkuNo(skuNo);
        if (sku != null ) {
           if(!(NOT_ON_SALE.equals(Integer.valueOf(sku.getOnSale())) || NOT_PUSH_STATUS.equals(Integer.valueOf(sku.getPushStatus())))) {
               throw new InvalidatorChainException(SkuBanInvalidatorError.getCode(), SkuBanInvalidatorError.getMessage());
           }
        }

    }
}
