<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib prefix="tags" tagdir="/WEB-INF/tags"%>

<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <title>分配商品归属</title>
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
    <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goodsdribute/goodsdribute.css?rnd=${resourceVersionKey}">
</head>

<body>
    <div class="erp-wrap">
        <div class="erp-title">
            <div class="erp-title-txt">分配商品SPU归属</div>
        </div>
        <div class="erp-block base-form search-wrap J-search-wrap">
            <div class="search-list">
                <div class="search-item">
                    <div class="item-label">分配产品经理：</div>
                    <div class="item-fields">
                        <select  class="J-select" name="hasM">
                      <option value="-1">全部</option>
                      <option value="0" <c:if test="${goodsDistribute.hasM eq 0}"> selected="selected"</c:if> >已分配</option>
                      <option value="1" <c:if test="${goodsDistribute.hasM eq 1}"> selected="selected"</c:if> >未分配</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">分配产品助理：</div>
                    <div class="item-fields">
                        <select  class="J-select" name="hasA">
                        <option value="-1">全部</option>
                        <option value="0" <c:if test="${goodsDistribute.hasA eq 0}"> selected="selected"</c:if> >已分配</option>
                        <option value="1" <c:if test="${goodsDistribute.hasA eq 1}"> selected="selected"</c:if> >未分配</option>

                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">商品等级：</div>
                    <div class="item-fields">
                        <select  class="J-select" name="spuLevel">
                            <option value="-1">全部</option>
                            <option <c:if test="${goodsDistribute.spuLevel eq 0}">selected</c:if> value="0">其他商品</option>
                            <option <c:if test="${goodsDistribute.spuLevel eq 1}">selected</c:if> value="1">核心商品</option>
                            <option <c:if test="${goodsDistribute.spuLevel eq 2}">selected</c:if> value="2">临时商品</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">产品分类：</div>
                    <div class="item-fields cate-select">
                        <div class="select-item J-select-cate1" data-value="${goodsDistribute.categoryLv1Name}"></div>
                        <div class="select-item J-select-cate2" data-value="${goodsDistribute.categoryLv2Name}"></div>
                        <div class="select-item J-select-cate3" data-value="${goodsDistribute.categoryLv3Name}"></div>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">品牌类型：</div>
                    <div class="item-fields">
                        <select class="J-select" name="brandNatureSearch">
                            <option value="">全部</option>
                            <option value="2" <c:if test="${goodsDistribute.brandNatureSearch eq 2}"> selected="selected"</c:if>>进口品牌</option>
                            <option value="1" <c:if test="${goodsDistribute.brandNatureSearch eq 1}"> selected="selected"</c:if>>国产品牌</option>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">品牌：</div>
                    <div class="item-fields fields-suggest">
                        <input type="text" value="${goodsDistribute.brandName}" class="input-text J-suggest-input" data-url="/firstengage/brand/brandName.do" name="brandName">
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">归属产品经理：</div>
                    <div class="item-fields">
                        <div class="J-muiti-select fields-select-suggest"></div>
                        <input type="hidden" class="J-value" name="managerUsername" value="${goodsDistribute.managerUsername}">
                        <select name="assignmentManagerId">
		                    <c:forEach var="list" items="${managerUser}">
		                    <option value="${list.userId}"
		                    	<c:if test="${goodsDistribute.assignmentManagerId == list.userId}">selected="selected"</c:if> >${list.username}</option>
		                    </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">归属产品助理：</div>
                    <div class="item-fields">
                        <div class="J-muiti-select fields-select-suggest"></div>
                        <input type="hidden" class="J-value" name="assUsername" value="${goodsDistribute.assUsername}">
                        <select name="" name="assignmentAssistantId">
		                    <c:forEach var="asslist" items="${assUser}">
		                    <option value="${asslist.userId}"
		                          <c:if test="${goodsDistribute.assignmentAssistantId == asslist.userId}">selected="selected"</c:if>>${asslist.username}</option>
		                    </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">商品名称：</div>
                    <div class="item-fields">
                        <input type="text" placeholder="请输入您想要搜索的商品名称" class="input-text" name="spuName" value="${goodsDistribute.spuName }">
                    </div>
                </div>
                <div class="search-item">
                    <div class="item-label">SPU ID：</div>
                    <div class="item-fields">
                        <input type="text" placeholder="请输入SPU ID 或SKU订货号进行查询" class="input-text" name="spuNo" value="${goodsDistribute.spuNo}">
                    </div>
                </div>
            </div>
            <div class="search-btns">
                <div class="btn btn-small btn-blue-bd J-search">搜索</div>
                <div class="btn btn-small J-reset">重置</div>
            </div>
               </div>
        <div class="erp-block erp-block-list">
            <div class="option-wrap J-fix-wrap">
                <div class="option-fix-wrap cf J-fix-cnt">
                    <a class="btn J-distribute">分配归属人</a>
                    <a class="btn J-change-distribute">归属人更换</a>
                    <div class="option-r">
                        <div class="option-pager">
                            <div class="option-pager-wrap"></div>
                        </div>
                    </div>
                </div>
            </div>
            <table class="table table-base table-hover base-form J-table-wrap">
                <colgroup>
                    <col width="60px">
                    <col width="60px">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                    <col width="">
                </colgroup>
                <tbody>
                    <tr>
                        <th>
                            <div class="input-checkbox">
                                <label class="input-wrap">
                                    <input type="checkbox" class="J-select-all">
                                    <span class="input-ctnr"></span>
                                </label>
                            </div>
                        </th>
                        <th>序号</th>
                        <th>SPU ID</th>
                        <th>商品名称</th>
                        <th>归属产品经理</th>
                        <th>归属产品助理</th>
                        <th>商品等级</th>
                        <th>品牌类型</th>
                        <th>一级产品分类</th>
                    </tr>
                   <c:if test="${empty distributelist}">
                    <tr>

                        <td class="no-data" colspan="10">
                            <div><i class="vd-icon icon-caution1"></i></div>
                                                                                                    没有匹配的数据
                        </td>
                    </tr>
                      </c:if>
                       <c:if test="${not empty distributelist}">
                   <c:forEach items="${distributelist}" var="list" varStatus="status">
                   <!--隐藏域-->
                     <tr>
                        <td>
                            <div class="input-checkbox">
                                <label class="input-wrap">
                                    <input type="checkbox" class="J-select-item" value="${list.spuId}">
                                    <span class="input-ctnr"></span>
                                </label>
                            </div>
                        </td>
                        <td>${status.index + 1}</td>
                        <td>${list.spuId}</td>
                        <td>${list.spuName}</td>
                        <td>${list.managerUsername}</td>
                        <td>${list.assUsername}</td>
                        <td><c:if test="${list.spuLevel eq 0}">其他产品</c:if>
                            <c:if test="${list.spuLevel eq 1}">核心产品</c:if>
                            <c:if test="${list.spuLevel eq 2}">临时产品</c:if>
                        </td>
                        <td>${list.brandNature}</td>
                        <td>${list.categoryName}</td>
                   </c:forEach>
                   </c:if>


                </tbody>
            </table>
            <c:if test="${page.totalPage > 1}">
                <tags:pageNew page="${page}" />
            </c:if>
        </div>
    </div>
    <script class="J-add-tmpl" type="text/tmpl">
        <form class="J-add-form">
            <div class="dlg-form-wrap base-form form-span-5">
                <div class="form-item">
                    <div class="form-label">归属产品经理：</div>
                    <div class="form-fields">
                        <select name="manager">
                            <option value="">请选择</option>
                            <c:forEach var="asslist" items="${assUser}">
                            <option value="${asslist.userId}"
                                    <c:if test="${goodsDistribute.assignmentAssistantId == asslist.userId}">selected="selected"</c:if>>${asslist.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
                <div class="form-item">
                    <div class="form-label">归属产品助理：</div>
                    <div class="form-fields">
                        <select name="assistant">
                            <option value="">请选择</option>
                            <c:forEach var="asslist" items="${assUser}">
                            <option value="${asslist.userId}"
                                    <c:if test="${goodsDistribute.assignmentAssistantId == asslist.userId}">selected="selected"</c:if>>${asslist.username}</option>
                            </c:forEach>
                        </select>
                    </div>
                </div>
            </div>
        </form>
    </script>
    <script class="J-change-tmpl" type="text/tmpl">
        <div class="dlg-form-wrap base-form form-span-5 J-change-form">
            <div class="form-title" style="font-weight: bolder;">商品归属产品经理&产品助理（更换前）</div>
            <div class="form-item item-show" style="margin-bottom:0px;">
                <div class="form-label">产品经理：</div>
                <div class="form-fields">
                    <select name="managerBefore">
                        <option value="">请选择</option>
                        <c:forEach var="list" items="${managerUser}">
                        <option value="${list.userId}"
                            <c:if test="${goodsDistribute.assignmentManagerId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                        </c:forEach>
                    </select>
                </div>
                <div class="form-label">& &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
                <div class="form-fields">
                </div>
            </div>
            <div class="form-item item-show">
                <div class="form-label">产品助理：</div>
                <div class="form-fields">
                     <select name="assistantBefore">
                        <option value="">请选择</option>
                        <c:forEach var="asslist" items="${assUser}">
                        <option value="${asslist.userId}"
                                <c:if test="${goodsDistribute.assignmentAssistantId == asslist.userId}">selected="selected"</c:if>>${asslist.username}</option>
                        </c:forEach>
                    </select>
                </div>
            </div>
            <div class="form-title" style="color:#918d8d !important;">商品归属产品经理&产品助理 (更换前) 更换成产品经理&产品助理（更换后）</div>
            <div class="form-title" style="font-weight: bolder;">商品归属产品经理&产品助理（更换后）</div>
            <div class="form-item" style="margin-bottom:0px;">
                <div class="form-label">产品经理：</div>
                <div class="form-fields">
                    <select name="managerAfter">
                        <option value="">请选择</option>
                        <c:forEach var="list" items="${managerUser}">
                        <option value="${list.userId}"
                            <c:if test="${goodsDistribute.assignmentManagerId == list.userId}">selected="selected"</c:if> >${list.username}</option>
                        </c:forEach>
                    </select>
                </div>
                <div class="form-label">& &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</div>
                <div class="form-fields">
                </div>
            </div>
            <div class="form-item">
                <div class="form-label">产品助理：</div>
                <div class="form-fields">
                    <select name="assistantAfter">
                        <option value="">请选择</option>
                        <c:forEach var="asslist" items="${assUser}">
                        <option value="${asslist.userId}"
                                <c:if test="${goodsDistribute.assignmentAssistantId == asslist.userId}">selected="selected"</c:if>>${asslist.username}</option>
                        </c:forEach>
                    </select>
                </div>
            </div>
        </div>
    </script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/util.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/pikaday.2.1.0.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>
    <script src="${pageContext.request.contextPath}/static/new/js/common/suggestSelect.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/inputSuggest.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/common/lv-select.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/modules/list.js?rnd=${resourceVersionKey}"></script>
    <script type="text/javascript" src="${pageContext.request.contextPath}/static/new/js/pages/goodsdribute/goodsdribute.js?rnd=${resourceVersionKey}"></script>
</body>

</html>