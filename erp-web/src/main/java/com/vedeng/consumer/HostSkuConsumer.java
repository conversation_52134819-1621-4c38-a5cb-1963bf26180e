package com.vedeng.consumer;

import com.alibaba.fastjson.JSONObject;
import com.rabbitmq.client.Channel;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.trace.mq.AbstractMessageListener;
import com.vedeng.goods.dto.VHostWordDTO;
import com.vedeng.goods.service.HostSkuService;
import com.vedeng.op.api.dto.mqdto.HotWordToErpDTO;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName HostSkuConsumer.java
 * @Description TODO  op推送热词信息
 * @createTime 2021年12月31日 14:00:00
 */
@Component
public class HostSkuConsumer extends AbstractMessageListener {

    public static final Logger logger = LoggerFactory.getLogger(HostSkuConsumer.class);

    @Autowired
    private HostSkuService hostSkuService;

    @Override
    public void doBusiness(Message message, Channel channel) throws Exception {
        String messageBody = new String(message.getBody(), StandardCharsets.UTF_8);

        logger.info("op推送热词信息:{}",messageBody);
        List<HotWordToErpDTO> list = new ArrayList<>();
        try {
            list = JSONObject.parseArray(messageBody,HotWordToErpDTO.class);
            if (CollectionUtils.isEmpty(list)) {
                throw new RuntimeException("op推送热词信息为空");
            }
        }catch (Exception e){
            logger.error("HostSkuConsumer 消息队列json解析失败",e);
            channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,false);
        }

         try {
             for (HotWordToErpDTO hotWordToErpDTO : list) {
                 Optional<HotWordToErpDTO> dtoOptional = Optional.ofNullable(hotWordToErpDTO);
                 int hotWordId = dtoOptional.map(HotWordToErpDTO::getHotWordId).orElseThrow(() -> new IllegalArgumentException("op推送热词信息HotWordId 为空")).intValue();
                 String hotWordStr = dtoOptional.map(HotWordToErpDTO::getHotWordStr).orElseThrow(() -> new IllegalArgumentException("op推送热词信息HotWordStr 为空"));
                 Integer status = dtoOptional.map(HotWordToErpDTO::getOperateStatus).orElseThrow(() -> new IllegalArgumentException("op推送热词信息OperateStatus 为空"));

                 VHostWordDTO hostWordDTO = new VHostWordDTO();
                 hostWordDTO.setOpHostWordId(hotWordId);
                 hostWordDTO.setOperateStatus(status);
                 hostWordDTO.setWordName(hotWordStr);

                 List<String> skuNoList = new ArrayList<>();
                 if(ErpConst.ZERO.equals(status)){
                     skuNoList = Optional.ofNullable(hotWordToErpDTO.getSkuNoList()).orElseThrow(() -> new IllegalArgumentException("op推送热词信息SkuNoList 为空"));
                     skuNoList = skuNoList.stream().distinct().collect(Collectors.toList());
                 }
                 int isDetele = 0;
                 if(hotWordToErpDTO.getHotDelete() != null && hotWordToErpDTO.getHotDelete() == 1){
                     isDetele = 1;
                 }
                 hostWordDTO.setIsDelete(isDetele);
                 hostSkuService.saveHostByopPushMsg(hostWordDTO,skuNoList);
             }

             channel.basicAck(message.getMessageProperties().getDeliveryTag(),false);
            } catch (Exception e) {
             logger.error("op推送热词信息 error:",e);
             channel.basicNack(message.getMessageProperties().getDeliveryTag(),false,true);
         }
    }
}
