package com.vedeng.customerbillperiod.service;

import com.vedeng.customerbillperiod.dto.RollbackCustomerBillPeriodManagementDetailDto;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;

/**
 * @Author: daniel
 * @Date: 2021/7/28 17 32
 * @Description: 客户账期编码管理服务基础接口
 */
public interface CustomerBillPeriodManagementCodeCommonService {

    /**
     * 生成负数客户账期管理编码
     * 针对未归还完全的逾期编码，依次生成负数编码
     * @param managementDetailDto 负数编码生成类
     * @throws CustomerBillPeriodException 操作异常
     */
    void generateNegativeBillPeriodManagementCode(RollbackCustomerBillPeriodManagementDetailDto managementDetailDto) throws CustomerBillPeriodException;

    /**
     * 账期管理编码生成器
     * @param companyId 公司id
     * @param customerId 客户id
     * @param orderNo 订单号
     * @param orderId 订单id
     * @param settlementType 结算方式，0N编码，1：H编码，2P编码
     * @return 编码
     */
    String customerBillPeriodManagementCodeGenerator(Integer companyId, Long customerId, String orderNo, Long orderId, Integer settlementType);



    /**
     * 账期管理编码尾部数字自动加一
     * @param code 原账期管理编码
     * @return 加一后的账期管理编码
     */
    String billPeriodManagementCodeIncrease(String code);
}
