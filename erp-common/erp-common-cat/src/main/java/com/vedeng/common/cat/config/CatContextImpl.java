package com.vedeng.common.cat.config;

import com.dianping.cat.Cat;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-08-23
 * @Description: TODO
 * @Version: 1.0
 */
public class CatContextImpl implements Cat.Context{

    private Map<String, String> properties = new HashMap<>(16);

    @Override
    public void addProperty(String key, String value) {
        properties.put(key, value);
    }

    @Override
    public String getProperty(String key) {
        return properties.get(key);
    }
}
