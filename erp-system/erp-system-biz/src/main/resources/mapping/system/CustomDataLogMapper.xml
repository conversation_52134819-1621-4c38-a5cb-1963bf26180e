<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.CustomDataLogMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.CustomDataLogEntity">
        <!--@mbg.generated-->
        <!--@Table T_CUSTOM_DATA_LOG-->
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="RELATED_IDS" jdbcType="LONGVARCHAR" property="relatedIds"/>
        <result column="TYPE" jdbcType="INTEGER" property="type"/>
        <result column="SAVE_TYPE" jdbcType="INTEGER" property="saveType"/>
        <result column="RESULT_INFO" jdbcType="VARCHAR" property="resultInfo"/>
        <result column="SUCCESS_FLAG" jdbcType="TINYINT" property="successFlag"/>
        <result column="BELONGER_ID" jdbcType="INTEGER" property="belongerId"/>
        <result column="BELONGER" jdbcType="VARCHAR" property="belonger"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        ID, ADD_TIME, MOD_TIME, CREATOR, UPDATER, RELATED_IDS, `TYPE`, SAVE_TYPE, RESULT_INFO,
        SUCCESS_FLAG, BELONGER_ID, BELONGER
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_DATA_LOG
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        <!--@mbg.generated-->
        delete
        from T_CUSTOM_DATA_LOG
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.CustomDataLogEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_DATA_LOG (ADD_TIME, MOD_TIME, CREATOR,
                                       UPDATER, RELATED_IDS, `TYPE`,
                                       SAVE_TYPE, RESULT_INFO, SUCCESS_FLAG,
                                       BELONGER_ID, BELONGER)
        values (#{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER},
                #{updater,jdbcType=INTEGER}, #{relatedIds,jdbcType=LONGVARCHAR}, #{type,jdbcType=INTEGER},
                #{saveType,jdbcType=INTEGER}, #{resultInfo,jdbcType=VARCHAR}, #{successFlag,jdbcType=TINYINT},
                #{belongerId,jdbcType=INTEGER}, #{belonger,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="ID" keyProperty="id"
            parameterType="com.vedeng.erp.system.domain.entity.CustomDataLogEntity" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_DATA_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="relatedIds != null">
                RELATED_IDS,
            </if>
            <if test="type != null">
                `TYPE`,
            </if>
            <if test="saveType != null">
                SAVE_TYPE,
            </if>
            <if test="resultInfo != null">
                RESULT_INFO,
            </if>
            <if test="successFlag != null">
                SUCCESS_FLAG,
            </if>
            <if test="belongerId != null">
                BELONGER_ID,
            </if>
            <if test="belonger != null">
                BELONGER,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="relatedIds != null">
                #{relatedIds,jdbcType=LONGVARCHAR},
            </if>
            <if test="type != null">
                #{type,jdbcType=INTEGER},
            </if>
            <if test="saveType != null">
                #{saveType,jdbcType=INTEGER},
            </if>
            <if test="resultInfo != null">
                #{resultInfo,jdbcType=VARCHAR},
            </if>
            <if test="successFlag != null">
                #{successFlag,jdbcType=TINYINT},
            </if>
            <if test="belongerId != null">
                #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                #{belonger,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.CustomDataLogEntity">
        <!--@mbg.generated-->
        update T_CUSTOM_DATA_LOG
        <set>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="relatedIds != null">
                RELATED_IDS = #{relatedIds,jdbcType=LONGVARCHAR},
            </if>
            <if test="type != null">
                `TYPE` = #{type,jdbcType=INTEGER},
            </if>
            <if test="saveType != null">
                SAVE_TYPE = #{saveType,jdbcType=INTEGER},
            </if>
            <if test="resultInfo != null">
                RESULT_INFO = #{resultInfo,jdbcType=VARCHAR},
            </if>
            <if test="successFlag != null">
                SUCCESS_FLAG = #{successFlag,jdbcType=TINYINT},
            </if>
            <if test="belongerId != null">
                BELONGER_ID = #{belongerId,jdbcType=INTEGER},
            </if>
            <if test="belonger != null">
                BELONGER = #{belonger,jdbcType=VARCHAR},
            </if>
        </set>
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.CustomDataLogEntity">
        <!--@mbg.generated-->
        update T_CUSTOM_DATA_LOG
        set ADD_TIME     = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME     = #{modTime,jdbcType=TIMESTAMP},
            CREATOR      = #{creator,jdbcType=INTEGER},
            UPDATER      = #{updater,jdbcType=INTEGER},
            RELATED_IDS  = #{relatedIds,jdbcType=LONGVARCHAR},
            `TYPE`       = #{type,jdbcType=INTEGER},
            SAVE_TYPE    = #{saveType,jdbcType=INTEGER},
            RESULT_INFO  = #{resultInfo,jdbcType=VARCHAR},
            SUCCESS_FLAG = #{successFlag,jdbcType=TINYINT},
            BELONGER_ID  = #{belongerId,jdbcType=INTEGER},
            BELONGER     = #{belonger,jdbcType=VARCHAR}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <!--@mbg.generated-->
        update T_CUSTOM_DATA_LOG
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="ADD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.addTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.addTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="MOD_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.modTime != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.modTime,jdbcType=TIMESTAMP}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CREATOR = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.creator != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.creator,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="UPDATER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updater != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.updater,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RELATED_IDS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.relatedIds != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.relatedIds,jdbcType=LONGVARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="`TYPE` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.type != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.type,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SAVE_TYPE = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.saveType != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.saveType,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="RESULT_INFO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.resultInfo != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.resultInfo,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
            <trim prefix="SUCCESS_FLAG = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.successFlag != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.successFlag,jdbcType=TINYINT}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONGER_ID = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belongerId != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.belongerId,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="BELONGER = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.belonger != null">
                        when ID = #{item.id,jdbcType=INTEGER} then #{item.belonger,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where ID in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="ID" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into T_CUSTOM_DATA_LOG
        (ADD_TIME, MOD_TIME, CREATOR, UPDATER, RELATED_IDS, `TYPE`, SAVE_TYPE, RESULT_INFO,
         SUCCESS_FLAG, BELONGER_ID, BELONGER)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER},
             #{item.updater,jdbcType=INTEGER}, #{item.relatedIds,jdbcType=LONGVARCHAR}, #{item.type,jdbcType=INTEGER},
             #{item.saveType,jdbcType=INTEGER}, #{item.resultInfo,jdbcType=VARCHAR},
             #{item.successFlag,jdbcType=TINYINT},
             #{item.belongerId,jdbcType=INTEGER}, #{item.belonger,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <!--auto generated by MybatisCodeHelper on 2022-07-19-->
    <select id="getByAllOrderByAddTime" resultType="com.vedeng.erp.system.dto.CustomDataLogDto">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_DATA_LOG
        <where>
            <if test="relatedIds != null">
                and RELATED_IDS = #{relatedIds,jdbcType=LONGVARCHAR}
            </if>
            <if test="type != null">
                and `TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="saveType != null">
                and SAVE_TYPE = #{saveType,jdbcType=INTEGER}
            </if>
            <if test="resultInfo != null">
                and RESULT_INFO = #{resultInfo,jdbcType=VARCHAR}
            </if>
            <if test="successFlag != null">
                and SUCCESS_FLAG = #{successFlag,jdbcType=TINYINT}
            </if>
            <if test="belongerId != null">
                and BELONGER_ID = #{belongerId,jdbcType=INTEGER}
            </if>
            <if test="belonger != null">
                and BELONGER = #{belonger,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and ID = #{id,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater,jdbcType=INTEGER}
            </if>
        </where>
        order by ADD_TIME desc
        limit 1
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-07-22-->
    <select id="findByAll" resultType="com.vedeng.erp.system.dto.CustomDataLogDto">
        select
        <include refid="Base_Column_List"/>
        from T_CUSTOM_DATA_LOG
        <where>
            <if test="relatedIds != null">
                and RELATED_IDS = #{relatedIds,jdbcType=LONGVARCHAR}
            </if>
            <if test="type != null">
                and `TYPE` = #{type,jdbcType=INTEGER}
            </if>
            <if test="saveType != null">
                and SAVE_TYPE = #{saveType,jdbcType=INTEGER}
            </if>
            <if test="resultInfo != null">
                and RESULT_INFO = #{resultInfo,jdbcType=VARCHAR}
            </if>
            <if test="successFlag != null">
                and SUCCESS_FLAG = #{successFlag,jdbcType=TINYINT}
            </if>
            <if test="belongerId != null">
                and BELONGER_ID = #{belongerId,jdbcType=INTEGER}
            </if>
            <if test="belonger != null">
                and BELONGER = #{belonger,jdbcType=VARCHAR}
            </if>
            <if test="id != null">
                and ID = #{id,jdbcType=INTEGER}
            </if>
            <if test="addTime != null">
                and ADD_TIME = #{addTime,jdbcType=TIMESTAMP}
            </if>
            <if test="modTime != null">
                and MOD_TIME = #{modTime,jdbcType=TIMESTAMP}
            </if>
            <if test="creator != null">
                and CREATOR = #{creator,jdbcType=INTEGER}
            </if>
            <if test="updater != null">
                and UPDATER = #{updater,jdbcType=INTEGER}
            </if>
            <if test="belongers != null and belongers.size() != 0">
                and BELONGER_ID in
                <foreach item="item" collection="belongers" separator="," close=")" open="(">
                      #{item,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
</mapper>