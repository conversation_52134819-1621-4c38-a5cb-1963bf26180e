package com.vedeng.aftersales.component.impl;

import com.vedeng.aftersales.component.AbstractAfterSalesOrderServiceOfSaleOrder;
import com.vedeng.aftersales.component.AfterSalesTypeEnum;
import com.vedeng.aftersales.component.dto.*;
import com.vedeng.aftersales.component.exception.AfterSaleCloseException;
import com.vedeng.aftersales.component.exception.AfterSaleValidateException;
import com.vedeng.aftersales.component.exception.LockSaleOrderSkuOnCreateException;
import com.vedeng.aftersales.dao.AfterSalesInvoiceMapper;
import com.vedeng.aftersales.dao.RInvoiceJInvoiceMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesInvoice;
import com.vedeng.aftersales.model.RInvoiceJInvoice;
import com.vedeng.aftersales.component.vo.MapExtendsVo;
import com.vedeng.aftersales.dao.AfterSalesGoodsMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.aftersales.model.AfterSalesGoods;
import com.vedeng.aftersales.model.vo.AfterSalesGoodsVo;
import com.vedeng.aftersales.model.vo.AfterSalesInvoiceVo;
import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.aftersales.util.SaleAfterServiceUtil;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.SaleorderGoods;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.system.dao.SysOptionDefinitionMapper;
import com.vedeng.system.model.SysOptionDefinition;
import com.wms.constant.CancelReasonConstant;
import com.wms.service.CancelTypeService;
import com.wms.service.context.ThreadLocalContext;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.*;

/**
 * @Author: daniel
 * @Date: 2021/6/21 10 56
 * @Description:
 */
@Slf4j
@Service
public class ReturnsAfterSalesOfSaleOrderService extends AbstractAfterSalesOrderServiceOfSaleOrder {

    @Resource
    private SaleorderGoodsMapper saleorderGoodsMapper;

    @Resource
    private SaleorderMapper saleorderMapper;
    
    @Resource
    private AfterSalesGoodsMapper afterSalesGoodsMapper;

    @Resource
    private AfterSalesInvoiceMapper afterSalesInvoiceMapper;

    @Resource
    private RInvoiceJInvoiceMapper rInvoiceJInvoiceMapper;

    @Resource
    private AfterSalesMapper afterSalesMapper;

    @Resource
    private SysOptionDefinitionMapper sysOptionDefinitionMapper;

    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private CancelTypeService cancelTypeService;

    @Override
    protected void customerAfterSaleOrderCreateValidate(Object addDto) throws AfterSaleValidateException {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto)addDto;

        //售后原因如果为空
        if(afterSaleOrderAddDto.getAfterSalesReason() == null){
            throw new AfterSaleValidateException("售后原因不能为空");
        }

        if(CollectionUtils.isEmpty(afterSaleOrderAddDto.getGoodsList())){
            throw new AfterSaleValidateException("所选择的售后商品为空");
        }


        if(!cancelTypeService.cancelOutSaleOutMethod(afterSaleOrderAddDto.getSaleOrderNo(), CancelReasonConstant.AFTER_ORDER)){
            throw new AfterSaleValidateException("物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
        }

    }

    @Override
    protected void customerPropertySet(AfterSaleOrderAddDto afterSaleOrderAddDto, AfterSales afterSales) {
        afterSales.setType(AfterSalesTypeEnum.RETURNS_SALEORDER.getCode());
    }

    @Override
    protected void customerAfterSaleOrderClose(AfterSaleOrderCloseDto afterSaleOrderCloseDto) throws AfterSaleCloseException {
        AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(afterSaleOrderCloseDto.getAfterSalesNo());
        Saleorder saleorder = saleorderMapper.getSaleorderBySaleorderId(afterSales.getOrderId());
        saleorder.setServiceStatus(3);//售后关闭
        saleorderMapper.updateByPrimaryKeySelective(saleorder);
        if(saleorder.getLockedStatus() != null && saleorder.getLockedStatus()==0){
            updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
        }
        if(0 == saleorderMapper.updateByPrimaryKeySelective(saleorder)){
            throw new AfterSaleCloseException("关闭售后单遇到错误！");
        }
        List<AfterSalesGoodsVo> afterSalesGoodsList = afterSalesGoodsMapper.getAfterSalesGoodListForSaleorder(afterSales.getAfterSalesId());
        if(CollectionUtils.isEmpty(afterSalesGoodsList)){
            throw new AfterSaleCloseException("未查询到售后单商品信息！");
        }
        saleorderGoodsMapper.updateGoodsNoLockStatusBySaleorderGoodsId(afterSalesGoodsList);
        if(!isHaveUnlockSaleorderGoods(saleorder) && !isHaveReturnMoneyAfterSaleorder(saleorder)){
            saleorder.setLockedStatus(0);//0未锁定，1已锁定
            saleorderMapper.updateByPrimaryKeySelective(saleorder);
            updateUnlockSaleOrderWarning(saleorder.getSaleorderId());
        }
    }

    /**
     * 新增商品的时候有一些自定义的处理
     * @param afterSaleOrderAddDto
     */
    @Override
    protected void customerAddAfterSaleGood(AfterSaleOrderAddDto afterSaleOrderAddDto) {

        Saleorder saleorder = saleorderMapper.getSaleorderByOrderNo(afterSaleOrderAddDto.getSaleOrderNo());
        List<Integer> saleOrderGoodIdList =  ThreadLocalContext.get("saleOrderGoodIdList");
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");

        AfterSalesInvoiceVo afterSalesInvoiceVo = new AfterSalesInvoiceVo();
        afterSalesInvoiceVo.setCompanyId(1);
        afterSalesInvoiceVo.setRelatedId(saleorder.getSaleorderId());
        //查询是否有电子票，如果有就全退，且不存在纸质票
        afterSalesInvoiceVo.setInvoiceProperty(2);
        afterSalesInvoiceVo.setType(505);

        List<AfterSalesInvoiceVo> afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);

        if(CollectionUtils.isEmpty(afterSalesInvoiceVoList)){
            afterSalesInvoiceVo.setTypeList(Arrays.asList(504,505));
            afterSalesInvoiceVo.setOrderGoodsIdList(saleOrderGoodIdList);
            afterSalesInvoiceVo.setInvoiceProperty(null);
            afterSalesInvoiceVo.setType(null);
            afterSalesInvoiceVoList = afterSalesInvoiceMapper.getAfterSalesInvoiceVos(afterSalesInvoiceVo);
        }

        if(CollectionUtils.isEmpty(afterSalesInvoiceVoList)){
            return;
        }

        afterSalesInvoiceVoList.stream().forEach(asi -> {

            //过滤红字有效的是否已关联蓝子有效,或者蓝字有效关联了红字有效
            List<RInvoiceJInvoice> rInvoiceJInvoiceList = rInvoiceJInvoiceMapper.getRInvoiceJInvoiceList(asi.getInvoiceId());
            if(CollectionUtils.isEmpty(rInvoiceJInvoiceList)){
                insertAfterSalesInvoce(afterSaleOrder.getAfterSalesId(),asi.getInvoiceId());
                return;
            }

            BigDecimal amount = rInvoiceJInvoiceMapper.getAfterInvoiceTotalAmount(asi.getInvoiceId());
            if(amount.compareTo(new BigDecimal(0))>0){
                insertAfterSalesInvoce(afterSaleOrder.getAfterSalesId(),asi.getInvoiceId());
            }
        });

    }

    @Override
    protected void customerAfterSaleOrderModifyValidate(Object modifyDto) throws AfterSaleValidateException {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        if(!cancelTypeService.cancelOutSaleOutMethod(afterSaleOrderModifyDto.getSaleOrderNo(),CancelReasonConstant.AFTER_ORDER)){
            throw new AfterSaleValidateException("物流正在进行货物操作，请等待作业完成后操作，如紧急请直接联系物流部。");
        }
    }

    public void insertAfterSalesInvoce(Integer afterSalesId,Integer invoiceId){
        AfterSalesInvoice afterSalesInvoice = new AfterSalesInvoice();
        afterSalesInvoice.setAfterSalesId(afterSalesId);
        afterSalesInvoice.setInvoiceId(invoiceId);
        afterSalesInvoice.setIsRefundInvoice(1);
        afterSalesInvoiceMapper.insertSelective(afterSalesInvoice);
    }

    @Override
    protected <T extends AbstractAfterSaleOrderDto> void customerAddAfterSaleDetail(AfterSalesDetail afterSalesDetail, T afterSaleOrderAddDto) {
        List<AfterSalesGoods> afterSalesGoodsList = ThreadLocalContext.get("afterSalesGoodsList");
        if (CollectionUtils.isEmpty(afterSalesGoodsList)) {
            log.info("处理退货售后单关联退货产品为空！");
            return;
        }
        AfterSales afterSaleOrder = ThreadLocalContext.get("afterSaleOrder");
        if (null == afterSaleOrder  || null == afterSaleOrder.getOrderId()) {
            log.info("处理退货售后单为空或者售后关联销售单ID为空！");
            return;
        }
        Map<Integer, Integer> nowReundSkuNumMap = ThreadLocalContext.get("skuNumMap");
        // 统计销售订单sku商品数量 和 销售订单的sku的小计
        Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap = new HashMap();
        getSkuNumMap(afterSaleOrder.getOrderId(), skuNumMap);
        BigDecimal refundAmount = subRefundAmount(afterSaleOrder, nowReundSkuNumMap, skuNumMap);
        afterSalesDetail.setRefundAmount(refundAmount);

        // 售后服务费
        //dealAfterSaleServiceAmount()
        // 订单已付款金额，不含账期
        BigDecimal paymentAmount = saleorderMapper.getPaymentAndPeriodAmount(afterSaleOrder.getOrderId());
        // 已付款金额（不含账期）
        afterSalesDetail.setPaymentAmount(paymentAmount);
        //查询当前订单的所有已完结退货总额
        BigDecimal tk = afterSalesMapper.getSumTKAfterSalesBySaleorderid(afterSaleOrder.getOrderId());

        BigDecimal lackperid = saleorderMapper.getSaleorderLackAccountPeriodAmount(afterSaleOrder.getOrderId());//账期欠款金额
        Saleorder saleorder = saleorderMapper.getSaleOrderById(afterSaleOrder.getOrderId());

        // 预退款金额=订单已付款金额+退货产品金额-订单金额，（此处订单已付款包含帐期支付）。如果预退款金额<=0时，按0处理，即不退款，偿还帐期金额=实际退款金额=0；
        BigDecimal PreAnnealing = paymentAmount.add(lackperid).add(afterSalesDetail.getRefundAmount()).subtract(saleorder.getTotalAmount().subtract(tk));
        BigDecimal payPeriod;//偿还帐期金额
        if(PreAnnealing.compareTo(lackperid) <= 0 && lackperid.compareTo(new BigDecimal(0)) > 0){
            payPeriod = PreAnnealing;
        }else if(lackperid.compareTo(new BigDecimal(0)) > 0){
            payPeriod = lackperid;
        }else {
            payPeriod = new BigDecimal(0);
        }
        afterSalesDetail.setPayPeriodAmount(payPeriod);
        log.info("退货售后单处理,订单ID:" + afterSaleOrder.getOrderId() +",售后单ID:"+ afterSaleOrder.getAfterSalesId() +"，实退金额="+PreAnnealing+"-"+payPeriod);
        if(PreAnnealing.compareTo(new BigDecimal(0)) > 0){ //预付款大于0
            afterSalesDetail.setRealRefundAmount(PreAnnealing.subtract(payPeriod)); //实退金额
            if(afterSalesDetail.getRealRefundAmount().compareTo(new BigDecimal(0)) > 0){
                afterSalesDetail.setRefundAmountStatus(ErpConst.ONE);
            }
        }else{
            afterSalesDetail.setRealRefundAmount(new BigDecimal(0));
        }
    }

    @Override
    protected void customeAddDealOfSaleOrder(Object addDto) throws LockSaleOrderSkuOnCreateException {

        AfterSaleOrderAddDto afterSaleOrderAddDto = (AfterSaleOrderAddDto) addDto;
        lockSaleOrderSkuOnCreate(afterSaleOrderAddDto);

        User adminUser = this.userMapper.getUserByName("njadmin");
        logicalSaleorderChooseServiceImpl.putSaleAfterReturnStartGoods(afterSaleOrderAddDto.getSaleOrderNo(),adminUser);
    }

    @Override
    protected void customeModifyDealOfSaleOrder(Object modifyDto) throws AfterSaleValidateException {

        AfterSaleOrderModifyDto afterSaleOrderModifyDto = (AfterSaleOrderModifyDto)modifyDto;

        User adminUser = this.userMapper.getUserByName("njadmin");
        logicalSaleorderChooseServiceImpl.putSaleAfterReturnStartGoods(afterSaleOrderModifyDto.getSaleOrderNo(),adminUser);
        //锁定修改后的子sku
        lockSaleOrderSkuOnModify(modifyDto);

    }

    /**
     * 统计销售订单sku商品数量 和 销售订单的sku的小计
     * @param orderId
     * @param skuNumMap
     */
    public void getSkuNumMap(Integer orderId, Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap) {
        Saleorder saleorder = new Saleorder();
        saleorder.setSaleorderId(orderId);
        List<SaleorderGoodsVo> sgvList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);
        // 查询特殊商品id的list
        List<Integer> specialGoodIdList = getSpecialGoodsId();
        if(CollectionUtils.isEmpty(sgvList)) {
            return;
        }
        for (int i = 0; i < sgvList.size(); i++){
            // 当前sku的商品信息
            SaleorderGoodsVo sgv = sgvList.get(i);
            // null，则继续
            if(null == sgv || null == sgv.getGoodsId()) {
                continue;
            }
            // 商品ID
            Integer goodsId = sgv.getGoodsId();
            // 统计每个SaleorderGoodId的销售订单数量和sku的小计
            MapExtendsVo<Integer, BigDecimal> skuNumAndAmount = new MapExtendsVo<Integer, BigDecimal>();
            // 数量
            skuNumAndAmount.setExData(sgv.getNum());
            // sku的小计
            skuNumAndAmount.setValue(null == sgv.getMaxSkuRefundAmount() ? BigDecimal.ZERO : sgv.getMaxSkuRefundAmount());
            // 循环特殊商品ID
            for(Integer specialGoodsId : specialGoodIdList) {
                if(goodsId.equals(specialGoodsId)) {
                    // sku的小计 ，特殊商品最大退款金额为当前单价
                    skuNumAndAmount.setValue(null == sgv.getPrice() ? BigDecimal.ZERO : sgv.getPrice());
                    break;
                }
            }
            // 统计
            skuNumMap.put(sgv.getSaleorderGoodsId(), skuNumAndAmount);
        }
    }
    
    /**
     * 计算退款金额
     * @param nowReundSkuNumMap
     * @param skuNumMap
     * @return
     */
    protected BigDecimal subRefundAmount(AfterSales afterSales, Map<Integer, Integer> nowReundSkuNumMap, Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuNumMap) throws RuntimeException
    {
        // 订单ID
        Integer orderId = afterSales.getOrderId();

        // 涉及耗材售后订单 退款金额
        BigDecimal hcReundAmount = BigDecimal.ZERO;
        // 耗材退货
        //  计算当前销售订单下每个SalesGoodsId已退货金额
        Map<Integer, MapExtendsVo<Integer, BigDecimal>> everySkuReundAmountMap = subSkuReundAmountByOrderId(orderId);

        // 遍历 当前退货商品
        Iterator<Map.Entry<Integer, Integer>> iterator = nowReundSkuNumMap.entrySet().iterator();
        // 遍历
        while (iterator.hasNext()) {
            Map.Entry<Integer, Integer> entry = iterator.next();
            // 空，则继续
            if(null == entry || null == entry.getKey() || null == entry.getValue()) {
                continue;
            }
            // 获取sa
            Integer goodsId = entry.getKey();
            // 当前SalesGoodsId的退货数量
            Integer reundSkuNum = entry.getValue();
            // 获取销售订单的商品信息
            MapExtendsVo<Integer, BigDecimal> orderSku = skuNumMap.get(goodsId);
            // 校验不通过
            if(null == orderSku || null == orderSku.getExData() || orderSku.getExData() < reundSkuNum)  {
                throw new RuntimeException("当前退货数量大于销售订单销售数量");
            }
            // 当前销售订单的sku的数量
            Integer skuNum = orderSku.getExData();
            // 当前SalesGoodsId的最大退款金额
            BigDecimal skuTotalAmount = orderSku.getValue();
            // 当前sku已退数量
            BigDecimal alReturnNum = BigDecimal.ZERO;
            // 退货金额
            BigDecimal alReturnAmount = BigDecimal.ZERO;
            // 已经退货数量和退货金额
            MapExtendsVo<Integer, BigDecimal> alReundNumAndAmount = (null == everySkuReundAmountMap ? null : everySkuReundAmountMap.get(goodsId));
            // 非空
            if(null != alReundNumAndAmount)
            {
                // 退货数量
                alReturnNum = null == alReundNumAndAmount.getExData() ? alReturnNum : new BigDecimal(alReundNumAndAmount.getExData());
                // 退货金额
                alReturnAmount = null == alReundNumAndAmount.getValue() ? alReturnAmount : alReundNumAndAmount.getValue();
            }

            SaleorderGoods saleorderGoods = saleorderGoodsMapper.getSaleorderGoodsByIdSample(goodsId);

            // 当前SalesGoodsId的退款小计
            BigDecimal nowSkuReundAmount = SaleAfterServiceUtil.subRefundAmount(new BigDecimal(reundSkuNum), new BigDecimal(skuNum), alReturnNum,
                    skuTotalAmount, alReturnAmount,saleorderGoods);

            AfterSalesGoods record = new AfterSalesGoods();
            record.setOrderDetailId(goodsId);
            record.setAfterSalesId(afterSales.getAfterSalesId());
            record.setSkuRefundAmount(nowSkuReundAmount);
            record.setSkuOldRefundAmount(nowSkuReundAmount);
            // 更新当前SalesGoodsId退货金额
            afterSalesGoodsMapper.updateByGoodsIdAndAfterSalesId(record);
            // 累计
            hcReundAmount = hcReundAmount.add(nowSkuReundAmount);
        }

        return hcReundAmount;
    }

    /**
     *
     * <b>Description: 计算当前销售订单下每个商品id已退货金额</b><br>
     * @param orderId
     * @return
     * <b>Author: Franlin.wu</b>  
     * <br><b>Date: 2018年12月17日 下午9:18:38 </b>
     */
    private Map<Integer, MapExtendsVo<Integer, BigDecimal>> subSkuReundAmountByOrderId(Integer orderId)
    {
        if(null != orderId) {
            AfterSalesVo afterSalesVo = new AfterSalesVo();
            afterSalesVo.setOrderId(orderId);
            // 审核通过
            afterSalesVo.setAtferSalesStatus(2);
            // 已完结
            afterSalesVo.setStatus(2);
            afterSalesVo.setType(539);
            List<AfterSalesVo> afterList = afterSalesMapper.getAfterSalesVoListByOrderId(afterSalesVo);
            // 非空
            if(CollectionUtils.isNotEmpty(afterList)) {
                // 统计各sku的
                Map<Integer, MapExtendsVo<Integer, BigDecimal>> skuMap = new HashMap<Integer, MapExtendsVo<Integer, BigDecimal>>();
                // 根据afterSalesId获取当前售后订单商品（不包含特殊商品）
                List<AfterSalesGoods> afterSalesGoodsList = afterSalesGoodsMapper.selectByAfterSalesId(afterList, 693);

                for(AfterSalesGoods asg : afterSalesGoodsList) {
                    if(null == asg) {
                        continue;
                    }
                    // 商品ID
                    Integer goodsId = asg.getOrderDetailId();
                    // 定义 当前sku的退款金额
                    BigDecimal alSkuReundAmount = BigDecimal.ZERO;
                    // 当前sku的退款金额 不是null则赋予
                    if(null != asg.getSkuRefundAmount()) {
                        alSkuReundAmount = asg.getSkuRefundAmount();
                    }
                    // 定义当前退货数量
                    Integer num = 0;
                    if(null != asg.getNum()) {
                        num = asg.getNum();
                    }
                    // Integer 退货数量 BigDecimal 退货金额
                    MapExtendsVo<Integer, BigDecimal> mapExVo = new MapExtendsVo<Integer, BigDecimal>();
                    // 根据goodsId区分
                    if(null == skuMap.get(goodsId)) {
                        // 退货数量
                        mapExVo.setExData(num);
                        // 退款金额
                        mapExVo.setValue(alSkuReundAmount);
                    }
                    else {
                        // 退货数量
                        mapExVo.setExData(num + skuMap.get(goodsId).getExData());
                        // 退款金额
                        mapExVo.setValue(alSkuReundAmount.add(skuMap.get(goodsId).getValue()));
                    }
                    skuMap.put(goodsId, mapExVo);
                }
                return skuMap;
            }
        }
        return null;
    }

    /**
     *
     * <b>Description: 查询特殊商品id的list</b><br> 
     * @return
     * <b>Author: Franlin.wu</b>  
     * <br><b>Date: 2018年12月20日 上午9:53:25 </b>
     */
    private List<Integer> getSpecialGoodsId()
    {
        List<Integer> list = new LinkedList<>();
        try{
            // 根据id查询特殊商品
            List<SysOptionDefinition> sysOptList = sysOptionDefinitionMapper.getDictionaryByParentId(693);
            if(CollectionUtils.isNotEmpty(sysOptList)){
                for(SysOptionDefinition sysOpt : sysOptList){
                    if(null == sysOpt || StringUtil.isEmpty(sysOpt.getComments())){
                        continue;
                    }
                    list.add(Integer.parseInt(sysOpt.getComments().trim()));
                }
            }
        }
        catch (Exception e){
            log.error("根据id查询特殊商品发生异常", e);
        }
        return list;
    }


}
