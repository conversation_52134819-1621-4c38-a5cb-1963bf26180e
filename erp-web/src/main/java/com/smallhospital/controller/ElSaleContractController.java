package com.smallhospital.controller;

import com.alibaba.fastjson.JSON;
import com.smallhospital.constant.ElEnum;
import com.smallhospital.dto.*;
import com.smallhospital.model.ContractSku;
import com.smallhospital.model.ElContractSku;
import com.smallhospital.model.vo.ELContractVO;
import com.smallhospital.model.vo.ElTraderVo;
import com.smallhospital.service.*;
import com.vedeng.authorization.model.User;
import com.vedeng.common.controller.Consts;
import com.vedeng.common.util.DateUtil;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.vedeng.goods.dao.CoreSpuGenerateMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Daniel
 * @Description: 小医院合同、产品控制类
 * @Date created in 2019/11/20 6:48 下午
 */
@RestController
@RequestMapping("/el")
@Slf4j
public class ElSaleContractController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ElSaleOrderController.class);

    public static final Integer SYSTEM_CREATOR_USER = 0;

    public static final Integer SYN_USER_SUCCESS = 1;
    public static final Integer SYN_USER_ERROR = 2;


    public static final Integer CONFIRM_SUCCESS = 1;

    @Autowired
    private ElSaleContractService elSaleContractService;

    @Autowired
    private ELContractService contractService;

    @Autowired
    private ELContractSkuService contractSkuService;

    @Autowired
    private ELTraderService traderService;

    @Autowired
    private ElSkuService skuService;

    @Autowired
    @Qualifier("coreSkuGenerateMapper")
    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    @Autowired
    @Qualifier("coreSpuGenerateMapper")
    private CoreSpuGenerateMapper coreSpuGenerateMapper;

    /**
     * 小医院系统推送用户意向合同申请，将该意向合同保存到T_EL_CONTRACT表中，状态为0
     * @return 结果
     */
    @RequestMapping(method = RequestMethod.POST, value = "/contract/intention")
    public ElResultDTO addElContractIntention(HttpServletRequest request,@RequestBody IntentionContractVo contractVo){
        LOGGER.info("小医院意向合同申请入参:" + JSON.toJSONString(contractVo));
        if(contractVo == null || contractVo.getHospitalId() == null || contractVo.getProductList() == null){
            return ElResultDTO.error("参数不能为空");
        }

        User user = (User) request.getSession().getAttribute(Consts.SESSION_USER);

        //创建意向合同
        ELContractVO contract = new ELContractVO();
        contract.setTraderId(contractVo.getHospitalId());
        contract.setOwner(SYSTEM_CREATOR_USER);
        contract.setCreator(SYSTEM_CREATOR_USER);
        contract.setUpdator(SYSTEM_CREATOR_USER);
        contract.setStatus(0);
        int contractId = contractService.saveContractInfo(contract);

        List<ElContractSku> skuLists = new ArrayList<ElContractSku>();
        List<ContractSku> productList = contractVo.getProductList();
        productList.forEach(product ->{
            ElContractSku sku = new ElContractSku();
            sku.setContractId(contractId);
            sku.setAddTime(DateUtil.gainNowDate());
            sku.setUpdateTime(DateUtil.gainNowDate());
            sku.setCreator(SYSTEM_CREATOR_USER);
            sku.setUpdator(SYSTEM_CREATOR_USER);
            if (product.getProductId()!=null){
                sku.setSkuId(product.getProductId());
            }
            if (product.getPrice()!= null){
                sku.setPrice(product.getPrice());
            }
            skuLists.add(sku);
        });
        contractSkuService.batchAddContractSkus(skuLists);

        return ElResultDTO.ok();
    }

    /**
     * 合同确认
     * @return 结果
     */
    @RequestMapping(method = RequestMethod.POST, value = "/contract/confirm")
    public ElResultDTO confirm(@RequestBody ContractConfirmDto confirmDto){
        try{

            if(confirmDto == null || confirmDto.getContractId() == null){
                return ElResultDTO.error("参数不能为空");
            }

            ELContractVO contractVO = new ELContractVO();
            contractVO.setElContractId(confirmDto.getContractId());
            contractVO.setConfirmStatus(confirmDto.getConfirm());
            contractVO.setEffctiveStatus(1);
            contractService.updateContract(contractVO);

        }catch (Exception e){
            log.error("【confirm】处理异常",e);
            return ElResultDTO.error("合同确认失败");
        }
        return ElResultDTO.ok();
    }

    /**
     * 合同产品推送
     * @param confirmDto
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/contractSku/push")
    public ElResultDTO pushSku(@RequestBody ContractPushDto confirmDto){
        try{

            if(confirmDto == null || confirmDto.getContractId() == null){
                return ElResultDTO.error("参数不能为空");
            }


            LOGGER.info("合同产品推送回调结果入参:" + JSON.toJSONString(confirmDto));

            ELContractVO contractVO = new ELContractVO();
            contractVO.setElContractId(confirmDto.getContractId());
            contractVO.setProductSynStatus(confirmDto.getStatus( )== 1? SYN_USER_SUCCESS : SYN_USER_ERROR);

            contractService.updateContract(contractVO);

        }catch (Exception e){
            log.error("【pushSku】处理异常",e);
            return ElResultDTO.error("合同推送失败");
        }
        return ElResultDTO.ok();
    }

    /**
     * 合同推送
     * @param confirmDto
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/contract/push")
    public ElResultDTO push(@RequestBody ContractPushDto confirmDto){
        try{

            LOGGER.info("合同推送回调结果入参:" + JSON.toJSONString(confirmDto));

            if(confirmDto == null || confirmDto.getContractId() == null){
                return ElResultDTO.error("参数不能为空");
            }

            ELContractVO contractVO = new ELContractVO();
            contractVO.setElContractId(confirmDto.getContractId());
            contractVO.setContractSynStatus(confirmDto.getStatus( )== 1? SYN_USER_SUCCESS : SYN_USER_ERROR);
            contractVO.setConfirmStatus(1);
            //合同生效
            contractVO.setEffctiveStatus(1);
            contractService.updateContract(contractVO);

        }catch (Exception e){
            log.error("【push】处理异常",e);
            return ElResultDTO.error("合同推送失败");


        }
        return ElResultDTO.ok();
    }

    /**
     * 小医院系统获取生产商家资格证书
     * @param credentialsDto 商家
     * @return 结果
     */
    @RequestMapping(method = RequestMethod.POST, value = "/manufacturer/credentials")
    public ElResultDTO getCredentialsOfManufacturer(@RequestBody CredentialsDto credentialsDto){
        if (credentialsDto.getManufacturerId() == null){
            return ElResultDTO.error("参数错误");
        }

        LOGGER.info("生产厂家证书接口入参：" + JSON.toJSONString(credentialsDto));

        ElResultDTO dto = ElResultDTO.ok();
        dto.setData(elSaleContractService.getCredentialsOfManufacturer(credentialsDto.getManufacturerId()));

        LOGGER.info("生产厂家证书接口响应：" + JSON.toJSONString(dto));
        return dto;
    }

    /***
     * @description: 小医院系统获取贝登资格证书
     * @param traderDto  小医院id
     * @return {@link ElResultDTO}
     * @throws
     * <AUTHOR>
     * @date 2020/12/9 9:59
     */
    @RequestMapping(method = RequestMethod.POST, value = "/vedeng/credentials")
    public ElResultDTO getCredentials(@RequestBody ELTraderDto traderDto){
        LOGGER.info("小医院获取贝登资格证书接口入参：" + JSON.toJSONString(traderDto));
        if(traderDto.getHospitalId() == null){
            return ElResultDTO.errorInternal("请求参数hospitalId不能为空");
        }

        ElTraderVo trader = this.traderService.getElTraderByTraderId(traderDto.getHospitalId());
        if(trader == null){
            return ElResultDTO.errorInternal("请求参数hospitalId="+traderDto.getHospitalId()+"非小医院的客户，请重试");
        }
        List<Integer> list = new ArrayList<>();
        list.add(ElEnum.CERTIFICATE.getType());
        list.add(ElEnum.LICENSE.getType());
        List<ElCredentialsDto> credentials = elSaleContractService.getQualifications(list);
        if (CollectionUtils.isNotEmpty(credentials)){
            ElResultDTO dto = ElResultDTO.ok();
            dto.setData(credentials);
            dto.setTotal(credentials.size());
            LOGGER.info("小医院获取贝登资格证书接口响应：" + JSON.toJSONString(dto));
            return dto;
        }
        return ElResultDTO.error("小医院获取贝登资格证书失败");
    }

}