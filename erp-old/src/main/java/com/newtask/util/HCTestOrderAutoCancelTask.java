package com.newtask.util;

import com.alibaba.fastjson.JSON;
import com.rabbitmq.MarketMsgProducer;
import com.rabbitmq.RabbitConfig;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 针对使用优惠券的HC订单 自动取消的任务
 */
@Component
@JobHandler(value="HCTestOrderAutoCancelTask")
public class HCTestOrderAutoCancelTask extends AbstractJobHandler {

    private Logger logger = LoggerFactory.getLogger(HCTestOrderAutoCancelTask.class);


    @Autowired
    private MarketMsgProducer msgProducer;

    @Autowired
    @Qualifier("hcSaleorderService")
    protected HcSaleorderService hcSaleorderService;


    @Override
    public ReturnT<String> doExecute(String executeParam) throws Exception {

        XxlJobLogger.log("HCOrderAutoCancelTask start.......");
        Map<String, String> couponReturnReqMap = new HashMap<>();
        couponReturnReqMap.put("couponCode", "cccccc");
        couponReturnReqMap.put("traderId", "123" + Strings.EMPTY);

        logger.info("归还优惠券消息HC start=======" + JSON.toJSONString(couponReturnReqMap));
        msgProducer.sendMsg(RabbitConfig.MARKET_RETURNCOUPON_EXCHANGE, RabbitConfig.MARKET_RETURNCOUPON_ROUTINGKEY, JSON.toJSONString(couponReturnReqMap));
        logger.info("归还优惠券消息HC end=======");
        return null;
    }



}
