package com.vedeng.flash.dao;

import com.vedeng.flash.dto.PrepareStockCommand;
import com.vedeng.flash.model.RegularSnapshot;

import java.util.List;

public interface RegularSnapshotMapper {

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_SNAPSHOT
     *
     * @mbggenerated Tue May 18 19:27:21 CST 2021
     */
    int deleteByPrimaryKey(Integer snapshotId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_SNAPSHOT
     *
     * @mbggenerated Tue May 18 19:27:21 CST 2021
     */
    int insert(RegularSnapshot record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_SNAPSHOT
     *
     * @mbggenerated Tue May 18 19:27:21 CST 2021
     */
    int insertSelective(RegularSnapshot record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_SNAPSHOT
     *
     * @mbggenerated Tue May 18 19:27:21 CST 2021
     */
    RegularSnapshot selectByPrimaryKey(Integer snapshotId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_SNAPSHOT
     *
     * @mbggenerated Tue May 18 19:27:21 CST 2021
     */
    int updateByPrimaryKeySelective(RegularSnapshot record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_REGULAR_SNAPSHOT
     *
     * @mbggenerated Tue May 18 19:27:21 CST 2021
     */
    int updateByPrimaryKey(RegularSnapshot record);

    /**
     * 批量新增
     * @param list
     */
    void insertBatch(List<RegularSnapshot> list);

    /**
     * 根据月份获取快照数据
     * @param command
     * @return
     */
    List<RegularSnapshot> selectSnapshotListByMonth(PrepareStockCommand command);
}
