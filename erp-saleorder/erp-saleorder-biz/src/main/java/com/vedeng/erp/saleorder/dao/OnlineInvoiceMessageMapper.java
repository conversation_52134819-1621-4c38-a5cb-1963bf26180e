package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.model.po.OnlineInvoiceMessagePo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 在线开票消息mapper
 *
 * <AUTHOR>
 */
public interface OnlineInvoiceMessageMapper {

    /**
     * 在线开票数据保存数据保存
     *
     * @param onlineInvoiceMessagePo
     * @return
     */
    int insert(OnlineInvoiceMessagePo onlineInvoiceMessagePo);

    /**
     * 更新在线开票消息记录
     *
     * @param id
     * @param consumeResult
     * @param currentTime
     * @return
     */
    int updateOnlineInvoiceMessage(@Param("id") Integer id, @Param("consumeResult") Integer consumeResult, @Param("currentTime") Long currentTime);

    /**
     * 主键批量获取报文信息
     *
     * @param ids
     * @return
     */
    List<OnlineInvoiceMessagePo> getOnlineInvoiceMessageByIds(@Param("ids") List<Integer> ids);

    /**
     * 获取未处理的在线开票消息
     *
     * @return
     */
    List<OnlineInvoiceMessagePo> getUnHandleRecords();

    /**
     * message 查找消息数量
     *
     * @param messageId
     * @return
     */
    OnlineInvoiceMessagePo getMessageInfoByMessageId(String messageId);
}
