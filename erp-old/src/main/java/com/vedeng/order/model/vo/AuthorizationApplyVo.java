package com.vedeng.order.model.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AuthorizationApplyVo {

    private Integer authorizationApplyId;

    private String authorizationApplyNum;

    private String applyPerson;

    private String beginTime;

    private String endTime;

    private Integer applyStatus;

    private String company;

    private String applyName;

    private  String applyNum;

    private String authorizationCompany;

    /**
     * 当前登录人
     */
    private Integer userId;

    private String userName;

    private List<Integer> userIds;

    /**
     * 是否本人处理
     * 1：是，0：否
     */
    private Integer isSelf;


    private Integer authType;

    /**
     * 公章类型 1.南京贝登医疗股份有限公司 2.南京医购优选供应链管理有限公司
     */
    private Integer sealType;
}
