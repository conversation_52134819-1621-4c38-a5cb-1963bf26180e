package com.newtask.quoteorder;

import com.newtask.quoteorder.exception.QuoteOrderStatusChangedException;
import com.newtask.quoteorder.exception.QuotedAlarmUpgradedException;
import com.newtask.quoteorder.model.QuotedAlarmMessageDto;
import com.newtask.quoteorder.model.QuotedAlarmRecord;
import com.newtask.quoteorder.strategy.SupplierStrategy;
import com.newtask.quoteorder.strategy.QuotedAlarmStrategy;
import com.newtask.quoteorder.strategy.SalesmanStrategy;
import com.vedeng.common.redis.RedisKeyUtils;
import com.vedeng.common.util.JsonUtils;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.enums.QuotedAlarmLevelEnum;
import com.vedeng.order.enums.QuotedAlarmModeEnum;
import com.vedeng.order.enums.QuotedConsultStateEnum;
import com.vedeng.order.enums.QuotedTrackStateEnum;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.service.QuotedAlarmService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import io.netty.util.concurrent.DefaultThreadFactory;
import org.apache.commons.collections.CollectionUtils;
import org.joda.time.Interval;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 报价单咨询记录监控器，每五分钟轮训报价预警记录。当满足记录的触发时间点在设定时间窗口内（5分钟），
 * 将预警记录放入队列中准备发送。
 *
 * <AUTHOR> [<EMAIL>]
 * @since ERP_LV_2020_38
 */
@JobHandler(value = QuotedAlarmMonitor.JOB_NAME)
@Component
public class QuotedAlarmMonitor extends LifecycleSupport {

    private final static Logger LOGGER = LoggerFactory.getLogger(QuotedAlarmMonitor.class);

    final static String JOB_NAME = "QuotedAlarmProcessor";

    private final static String QUOTED_ALARM_RECORD_SUFFIX = "quotedAlarmRecordMap";
    public static final String QUOTED_RECORDS_REDIS_KEY = RedisKeyUtils.createKey(QUOTED_ALARM_RECORD_SUFFIX);

    private final static int MESSAGE_MISSED_RETRY_ATTEMPTS = 1;

    private final static long DEFAULT_DELAY = TimeUnit.MINUTES.toMillis(2);


    protected final static Integer ON = 1;

    private final JsonJacksonCodec jacksonCodec = new TypedJsonJacksonCodec((Class<?>) null);

    private final QuotedAlarmStrategy supplierStrategy = new SupplierStrategy();
    private final QuotedAlarmStrategy salesmanStrategy = new SalesmanStrategy();

    private final List<Class<? extends Exception>> knownExceptions = new ArrayList<>(4);

    {
        knownExceptions.add(IllegalStateException.class);
        knownExceptions.add(IllegalArgumentException.class);
        knownExceptions.add(RuntimeException.class);
        knownExceptions.add(IOException.class);
    }

    private final static String TASK_PREFIX_NAME = "Async-QuotedAlarm-MessageSender";

    private ThreadPoolExecutor messageSenderExecutor;

    @Resource
    private QuoteorderMapper quoteorderMapper;
    @Resource
    private QuotedAlarmService quotedAlarmService;
    @Resource
    private RedissonClient redissonClient;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        try {
            retrieveQuotedAlarmRecords().forEach((key, value) -> {
                LOGGER.info("报价预警监控中心开始监控 - key: {}, record: {}", key, value);

                QuotedAlarmRecord quotedAlarmRecordToUse = null;

                boolean removeFlag = false;
                try {
                    quotedAlarmRecordToUse = JsonUtils.convertValue(value, QuotedAlarmRecord.class);
                } catch (Exception e) {
                    LOGGER.error("解析json时发生错误", e);
                    removeFlag = true;
                }

                if (removeFlag || !validateQuotedAlarmRecord(quotedAlarmRecordToUse)) {
                    removeQuotedAlarmRecord(Integer.valueOf(key.toString()));
                    return;
                }

                //next step
                processQuotedAlarmRecord(quotedAlarmRecordToUse);
            });
        } catch (Throwable e) {
            LOGGER.error("执行定时任务失败, message:{}", e.getMessage(), e);

            boolean done = handleThrowable(e);
            if (!done) {
                return new ReturnT<>(ReturnT.FAIL_CODE, e.getMessage());
            }
        }

        return SUCCESS;
    }


    @Override
    public void onStart() {
        messageSenderExecutor = new ThreadPoolExecutor(1, Runtime.getRuntime().availableProcessors(),
                10, TimeUnit.MINUTES, new LinkedBlockingQueue<>(1000),
                new DefaultThreadFactory(TASK_PREFIX_NAME));
        messageSenderExecutor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        messageSenderExecutor.allowCoreThreadTimeOut(true);
    }

    @Override
    public void onStop() {
        if (messageSenderExecutor != null && !messageSenderExecutor.isShutdown()) {
            messageSenderExecutor.shutdown();
        }
    }


    //~================================================================================= Private methods

    private void processQuotedAlarmRecord(final QuotedAlarmRecord quotedAlarmRecord) {
        boolean evictFlag = false;
        boolean continueUpgradeLevelForNext = true;

        if (quotedAlarmRecord.hasTriggerTime()) {
            //判断记录的发触发时间点是否在发送窗口内
            boolean skip = false;
            continueUpgradeLevelForNext = false;

            Long triggerTimeToCheck = quotedAlarmRecord.getTriggerTime();
            for (int count = 0; count <= MESSAGE_MISSED_RETRY_ATTEMPTS; count++) {
                //达到触发时间范围内时，放入到消息待发送队列中
                MessageTriggerState triggerStatus = MessageTriggerState.getMessageTriggerState(triggerTimeToCheck);
                if (triggerStatus == MessageTriggerState.TRIGGERING) {
                    //在发送窗口内，发送预警消息
                    asyncSendMessage(quotedAlarmRecord);
                    continueUpgradeLevelForNext = true;
                    break;
                } else if (!skip && triggerStatus == MessageTriggerState.WAITTING) {
                    //触发时间还未到达,暂不处理
                    return;
                } else if (triggerStatus == MessageTriggerState.MISSED) {
                    //记录触发时间点过期,尝试拯救一次
                    triggerTimeToCheck = triggerTimeToCheck + DEFAULT_DELAY;
                    evictFlag = true;
                    skip = true;
                }
            }
        }

        if (continueUpgradeLevelForNext) {
            evictFlag = !upgradeForNextTrigger(quotedAlarmRecord);
        }

        if (evictFlag) {
            //清理预警记录信息
            removeQuotedAlarmRecord(quotedAlarmRecord.getQuoteOrderKey());
        }
    }


    private boolean upgradeForNextTrigger(final QuotedAlarmRecord quotedAlarmRecord) {
        //检查报价单不存在、已成单或者已失单时已，删除记录
        final Quoteorder quoteOrderQuery = quoteorderMapper.getQuoteorderById(quotedAlarmRecord.getQuoteOrderKey());
        if (quoteOrderQuery == null) {
            LOGGER.info("【数据异常】查询报价单信息失败- quoteOrderId:{}", quotedAlarmRecord.getQuoteOrderKey());
            return false;
        }

        //校验报价当订单状态
        try {
            checkQuoteOrderStatus(quoteOrderQuery, quotedAlarmRecord);
        } catch (QuoteOrderStatusChangedException e) {
            LOGGER.info("订单状态出错,需要解除订单报价预警 - message:{}", e.getMessage());
            //校验报价当订单状态，解除订单状态
            unbindQuotedAlarm(quotedAlarmRecord);
            return false;
        }

        //刷新下一次预警时间并更新记录
        try {
            if (quotedAlarmRecord.getMode().equals(QuotedAlarmModeEnum.SALESMAN_MODE.getMode())) {
                salesmanStrategy.upgradeForNextLevel(quotedAlarmRecord, quoteOrderQuery.getPurchasingTime());
            } else {
                supplierStrategy.upgradeForNextLevel(quotedAlarmRecord);
            }
        } catch (QuotedAlarmUpgradedException e) {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.error("订单状态出错,需要解除订单报价预警 - message:", e);
            }
            return false;
        }

        //更新监控记录信息
        updateAlarmRecordAfterUpgrade(quotedAlarmRecord);
        return true;
    }


    /**
     * Second checks - 校验报价单咨询状态和预警状态经状态是否发生变化
     *
     * @param quotedOrder
     * @param quotedAlarmRecord
     * @throws QuoteOrderStatusChangedException 报价单状态发生改变需要解除预警状态
     */
    private void checkQuoteOrderStatus(Quoteorder quotedOrder, QuotedAlarmRecord quotedAlarmRecord) {
        //校验报价单追踪状态
        if (!checkFollowingStatus(quotedOrder)) {
            throw new QuoteOrderStatusChangedException("报价单:" + quotedOrder.getQuoteorderNo() + ", 跟单状态:" + quotedOrder.getFollowOrderStatus());
        }

        //采购端模式校验
        if (quotedAlarmRecord.getMode().equals(QuotedAlarmModeEnum.PURCHASER_MODE.getMode())) {
            if (Objects.equals(quotedOrder.getQuotedAlarmMode(), QuotedAlarmModeEnum.SALESMAN_MODE.getMode())) {
                throw new QuoteOrderStatusChangedException("报价单:" + quotedOrder.getQuoteorderNo() + ", 预警模式已切换为:" + QuotedAlarmModeEnum.SALESMAN_MODE.getMessage());
            }
            //报价单咨询状态已处理
            if (Objects.equals(quotedOrder.getConsultStatus(), QuotedConsultStateEnum.SUB_DONE_AND_SUP_NOT_DONE.getState())
                    || Objects.equals(quotedOrder.getConsultStatus(), QuotedConsultStateEnum.BOTH_DONE.getState())) {
                throw new QuoteOrderStatusChangedException("报价单:" + quotedOrder.getQuoteorderNo() + ", 咨询状态为" + QuotedConsultStateEnum.SUB_DONE_AND_SUP_NOT_DONE.getMessage()
                        + "或者" + QuotedConsultStateEnum.BOTH_DONE.getMessage());
            }
            //判断时候未首次触发
            if (!Objects.equals(quotedOrder.getIsSend(), ON)) {
                throw new QuoteOrderStatusChangedException("报价单:" + quotedOrder.getQuoteorderNo() + ", 咨询时配置发送状态:" + quotedOrder.getIsSend());
            }
        } else if (quotedAlarmRecord.getMode().equals(QuotedAlarmModeEnum.SALESMAN_MODE.getMode())) {
            if (Objects.equals(quotedOrder.getQuotedAlarmMode(), QuotedAlarmModeEnum.PURCHASER_MODE.getMode())) {
                throw new QuoteOrderStatusChangedException("报价单:" + quotedOrder.getQuoteorderNo() + ", 预警模式已切换为:" + QuotedAlarmModeEnum.PURCHASER_MODE.getMessage());
            }
            //报价单咨询状态-->已被全部处理
            if (Objects.equals(quotedOrder.getConsultStatus(), QuotedConsultStateEnum.NONE.getState())) {
                throw new QuoteOrderStatusChangedException("报价单:" + quotedOrder.getQuoteorderNo() + "已被全部处理，当前咨询状态为：" + quotedOrder.getConsultStatus());
            }
        }
    }


    private void asyncSendMessage(QuotedAlarmRecord quotedAlarmRecord) {
        messageSenderExecutor.execute(() -> {
            if (isClose() || !validateQuotedAlarmRecord(quotedAlarmRecord)) {
                return;
            }

            QuotedAlarmMessageDto messageToSend = QuotedAlarmMessageDto.createWithQuotedAlarmRecord(quotedAlarmRecord);

            boolean success = false;
            try {
                //发送预警信息
                quotedAlarmService.sendQuotedAlarmMessage(messageToSend);
                success = true;
            } catch (IllegalArgumentException | IllegalStateException e) {
                LOGGER.info("发送报价预警信息失败, info: {}, reason: {}", messageToSend, e.getMessage());
            } catch (Throwable t) {
                LOGGER.info("报价预警子任务发生错误", t);
            }

            if (!success) {
                removeQuotedAlarmRecord(quotedAlarmRecord.getQuoteOrderKey());
                unbindQuotedAlarm(quotedAlarmRecord);
            }
        });
    }

    private Map<Object, Object> retrieveQuotedAlarmRecords() {
        Map<Object, Object> result = redissonClient.getMap(QUOTED_RECORDS_REDIS_KEY, jacksonCodec).readAllMap();
        return result == null ? Collections.emptyMap() : result;
    }


    private void updateAlarmRecordAfterUpgrade(QuotedAlarmRecord quotedAlarmRecord) {
        try {
            redissonClient.getMap(QUOTED_RECORDS_REDIS_KEY, jacksonCodec).replace(quotedAlarmRecord.getQuoteOrderKey(), quotedAlarmRecord);
        } catch (Exception e) {
            LOGGER.error("刷新报价预警记录失败 - record:{}", quotedAlarmRecord, e);
        }
    }


    private void unbindQuotedAlarm(QuotedAlarmRecord quotedAlarmRecord) {
        //filter
        if (quotedAlarmRecord.getCurrentLevel().equals(QuotedAlarmLevelEnum.NONE.getLevel())) {
            return;
        }
        try {
            quotedAlarmService.unbindQuotedAlarm(quotedAlarmRecord.getQuoteOrderKey(), quotedAlarmRecord.getMode(),
                    quotedAlarmRecord.getQuotedGoodsIdList());
        } catch (Exception e) {
            LOGGER.error("解除报价单预警报时发生错误 - record: " + quotedAlarmRecord, e);
        }
    }


    private void removeQuotedAlarmRecord(Integer entryKey) {
        long count = 0;
        try {
            count = redissonClient.getMap(QUOTED_RECORDS_REDIS_KEY).fastRemove(entryKey);
        } catch (Exception e) {
            LOGGER.error("删除报价预警记录时发生错误", e);
        }

        if (count > 0) {
            LOGGER.info("删除报价预警记录时成功 - quotedOrderId: " + entryKey);
        }
    }


    /**
     * First checks  - 检查报价单销售追踪状态
     *
     * @param quotedOrder
     * @return return if valid
     * @throws IllegalArgumentException if status of quoted order invalid.
     */
    private boolean checkFollowingStatus(Quoteorder quotedOrder) {
        boolean valid = true;
        if (Objects.equals(quotedOrder.getFollowOrderStatus(), QuotedTrackStateEnum.DONE.getState()) ||
                Objects.equals(quotedOrder.getFollowOrderStatus(), QuotedTrackStateEnum.MISSED.getState())) {
            valid = false;
        }
        return valid;
    }


    private boolean validateQuotedAlarmRecord(QuotedAlarmRecord quotedAlarmRecord) {
        boolean valid = true;

        if (quotedAlarmRecord.getQuoteOrderKey() == null || quotedAlarmRecord.getFirstMarkTime() == null) {
            valid = false;
        } else if (CollectionUtils.isEmpty(quotedAlarmRecord.getQuotedGoodsIdList())) {
            valid = false;
        } else if (!QuotedAlarmModeEnum.getOrDefault(quotedAlarmRecord.getMode()).allowLevel(quotedAlarmRecord.getCurrentLevel())) {
            valid = false;
        }

        return valid;
    }


    /**
     * Handles exception.
     *
     * @param throwable
     * @return
     */
    private boolean handleThrowable(Throwable throwable) {
        boolean matched = false;
        for (Class<? extends Exception> knownException : knownExceptions) {
            if (knownException.isInstance(throwable)) {
                matched = true;
                break;
            }
        }
        return matched;
    }


    @SuppressWarnings("all")
    private enum MessageTriggerState {

        WAITTING, TRIGGERING, MISSED;

        private final static long START_TIEME_MILLIS = TimeUnit.MINUTES.toMillis(3);
        private final static long END_TIEME_MILLIS = TimeUnit.MINUTES.toMillis(2);

        public static MessageTriggerState getMessageTriggerState(Long nextTriggerTime) {
            Interval internalToUse = getTimeWindow();
            if (internalToUse.contains(nextTriggerTime)) {
                return MessageTriggerState.TRIGGERING;
            }

            if (internalToUse.isAfter(nextTriggerTime)) {
                return MessageTriggerState.MISSED;
            } else {
                return MessageTriggerState.WAITTING;
            }
        }

        /**
         * 发送消息时默认延在5分钟内就发送
         */
        private static Interval getTimeWindow() {
            long now = now();
            return new Interval(now - START_TIEME_MILLIS, now + END_TIEME_MILLIS);
        }

        private static long now() {
            return System.currentTimeMillis();
        }
    }
}
