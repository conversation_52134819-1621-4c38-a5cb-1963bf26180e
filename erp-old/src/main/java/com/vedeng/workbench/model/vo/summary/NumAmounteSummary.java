package com.vedeng.workbench.model.vo.summary;

import com.vedeng.workbench.model.vo.base.WorkbenchSummaryObject;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 数量金额汇总信息.
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 8:33 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class NumAmounteSummary {



    /**
     * 统计对象信息
     */
    private WorkbenchSummaryObject objectInfo;

    /**
     * 商机总额
     */
    private BigDecimal totalBussinessAmount;

    /**
     * 商机数量
     */
    private Integer totalBussinessNum;

    /**
     * 平均金额
     */
    private BigDecimal averageAmount;

    /**
     * 昨日新增商机数量
     */
    private Integer yesterdayTotalBussinessNum;

    /**
     * 本周新增商机数量
     */
    private Integer thisWeekTotalBussinessNum;

    /**
     * 预计当月到款商机数
     */
    private Integer thisMouthBussinessNum;
    /**
     * 昨日新增商机金额
     */
    private BigDecimal yesterdayTotalBussinessAmount;

    /**
     * 本周新增商机金额
     */
    private BigDecimal thisWeekTotalAmount;

    /**
     * 预计当月到款
     */
    private BigDecimal thisMouthTotalAmount;

}
