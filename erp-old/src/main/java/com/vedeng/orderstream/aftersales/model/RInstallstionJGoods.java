package com.vedeng.orderstream.aftersales.model;

public class RInstallstionJGoods {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_R_INSTALLSTION_J_GOODS.R_INSTALLSTION_J_GOODS_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    private Integer rInstallstionJGoodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_R_INSTALLSTION_J_GOODS.AFTER_SALES_INSTALLSTION_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    private Integer afterSalesInstallstionId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_R_INSTALLSTION_J_GOODS.AFTER_SALES_GOODS_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    private Integer afterSalesGoodsId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_R_INSTALLSTION_J_GOODS.NUM
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    private Integer num;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_R_INSTALLSTION_J_GOODS.R_INSTALLSTION_J_GOODS_ID
     *
     * @return the value of T_R_INSTALLSTION_J_GOODS.R_INSTALLSTION_J_GOODS_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public Integer getrInstallstionJGoodsId() {
        return rInstallstionJGoodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_R_INSTALLSTION_J_GOODS.R_INSTALLSTION_J_GOODS_ID
     *
     * @param rInstallstionJGoodsId the value for T_R_INSTALLSTION_J_GOODS.R_INSTALLSTION_J_GOODS_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void setrInstallstionJGoodsId(Integer rInstallstionJGoodsId) {
        this.rInstallstionJGoodsId = rInstallstionJGoodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_R_INSTALLSTION_J_GOODS.AFTER_SALES_INSTALLSTION_ID
     *
     * @return the value of T_R_INSTALLSTION_J_GOODS.AFTER_SALES_INSTALLSTION_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public Integer getAfterSalesInstallstionId() {
        return afterSalesInstallstionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_R_INSTALLSTION_J_GOODS.AFTER_SALES_INSTALLSTION_ID
     *
     * @param afterSalesInstallstionId the value for T_R_INSTALLSTION_J_GOODS.AFTER_SALES_INSTALLSTION_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void setAfterSalesInstallstionId(Integer afterSalesInstallstionId) {
        this.afterSalesInstallstionId = afterSalesInstallstionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_R_INSTALLSTION_J_GOODS.AFTER_SALES_GOODS_ID
     *
     * @return the value of T_R_INSTALLSTION_J_GOODS.AFTER_SALES_GOODS_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public Integer getAfterSalesGoodsId() {
        return afterSalesGoodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_R_INSTALLSTION_J_GOODS.AFTER_SALES_GOODS_ID
     *
     * @param afterSalesGoodsId the value for T_R_INSTALLSTION_J_GOODS.AFTER_SALES_GOODS_ID
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void setAfterSalesGoodsId(Integer afterSalesGoodsId) {
        this.afterSalesGoodsId = afterSalesGoodsId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_R_INSTALLSTION_J_GOODS.NUM
     *
     * @return the value of T_R_INSTALLSTION_J_GOODS.NUM
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public Integer getNum() {
        return num;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_R_INSTALLSTION_J_GOODS.NUM
     *
     * @param num the value for T_R_INSTALLSTION_J_GOODS.NUM
     *
     * @mbggenerated Fri Oct 15 16:55:14 CST 2021
     */
    public void setNum(Integer num) {
        this.num = num;
    }
}