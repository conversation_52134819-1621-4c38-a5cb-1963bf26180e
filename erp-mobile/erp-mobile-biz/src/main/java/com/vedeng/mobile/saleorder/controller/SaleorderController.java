package com.vedeng.mobile.saleorder.controller;

import com.vedeng.common.core.base.BaseResponseCode;
import com.vedeng.common.core.base.R;
import com.vedeng.mobile.dto.*;
import com.vedeng.mobile.query.*;
import com.vedeng.mobile.service.SaleOrderMobileAggregateApiService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * 销售订单
 */
@Slf4j
@RestController
@RequestMapping("/mobile/saleorder")
@RequiredArgsConstructor
public class SaleorderController {

    @Autowired
    private SaleOrderMobileAggregateApiService saleOrderMobileAggregateApiService;

    @PostMapping("/list")
    public R<SaleorderListPageDto> list(@RequestBody SaleorderListQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getUserId())) {
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(), "参数错误");
        }
        return R.success(saleOrderMobileAggregateApiService.querySaleorderList(query));
    }

    @PostMapping("/aftersaleList")
    public R<List<SaleorderOfAftersalesListDto>> aftersaleList(@RequestBody SaleorderOfAftersalesListQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(), "参数错误");
        }
        return R.success(saleOrderMobileAggregateApiService.querySaleorderOfAftersaleList(query));
    }

    @PostMapping("/detail")
    public R<SaleorderDetailDto> detail(@RequestBody SaleorderDetailQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(), "参数错误");
        }
        return R.success(saleOrderMobileAggregateApiService.querySaleorderDetail(query));
    }

    @PostMapping("/logisticsInfo")
    public R<List<SaleorderOfLogisticsInfoDto>> logisticsInfo(@RequestBody SaleorderOfLogisticsInfoQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(), "参数错误");
        }
        return R.success(saleOrderMobileAggregateApiService.querylogisticsInfo(query));
    }

    @PostMapping("/viewContract")
    public R<ResourceFileDto> viewContract(@RequestBody ViewContractQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(), "参数错误");
        }
        return R.success(saleOrderMobileAggregateApiService.viewContract(query));
    }

    @PostMapping("/viewInvoice")
    public R<List<ResourceFileDto>> viewInvoice(@RequestBody ViewInvoiceQuery query) {
        if (Objects.isNull(query) || Objects.isNull(query.getSaleOrderId())) {
            return R.error(BaseResponseCode.BAD_REQUEST.getCode(), "参数错误");
        }
        return R.success(saleOrderMobileAggregateApiService.viewInvoice(query));
    }

}
