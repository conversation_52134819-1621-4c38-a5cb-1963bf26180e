<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="选择关联备货单" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style type="text/css">
    .unable-choose{
        color: #888;
    }
</style>
<script type="text/javascript" src="${pageContext.request.contextPath}/static/js/order/buyorder/addVB.js?rnd=${resourceVersionKey}"></script>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>
<div class="formpublic">

    <form action="${pageContext.request.contextPath}/order/buyorder/searchVB.do" id="myform1" method="post">
        <blockquote class="layui-elem-quote layui-text" style="border-left: 5px solid #3384ef">
            温馨提示：可关联产品数>=所选订单产品总数的备货单方可关联<br>
            关联成功后，此销售单将从待采购列表消失，并自动通知销售已采购！
        </blockquote>
        <div>
            <ul>
                <li>
                    <div class="title-container title-container-blue">
                        <div class="table-title nobor">绑定产品信息</div>
                    </div>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <tbody>
                            <tr>
                                <td class="table-smaller">产品名称</td>
                                <td>${goodName}</td>
                                <td class="table-smaller">产品总数</td>
                                <td>
                                    ${goodNum}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </li>

                <li>
                    <div class="infor_name">
                        <label>关联单号</label>
                    </div>
                    <div class='f_left'>
                        <div class='inputfloat'>
                            <input type="text" name="buyOrderId" id="buyOrderId" value="${buyOrderId}" class="input-largest">
                            <input type="hidden" name="saleorderGoodsId" id="saleorderGoodsId" value="${saleorderGoodsId}" readonly="readonly"/>
                            <input type="hidden" name="goodId" id="goodId" value="${goodId}" readonly="readonly"/>
                            <input type="hidden" name="goodNum" id="goodNum" value="${goodNum}" readonly="readonly"/>
                            <input type="hidden" name="goodName" id="goodName" value="${goodName}" readonly="readonly"/>
                            <span class="bg-light-grey bt-bg-style bt-small" id="reset">重置</span>
                            <span class="bg-light-blue bt-bg-style bt-small"  id="search">搜索</span>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </form>
    <hr>
    可关联备货单
    <div>
        <table class="table table-bordered table-striped table-condensed table-centered" id="cus">
            <thead>
            <tr>
                <th class="text-left wid15">备货单号</th>
                <th class="wid13">下单人</th>
                <th class="wid12">采购单生效时间</th>
                <th class="wid8">产品采购数</th>
                <th class="wid8">可关联产品数</th>
                <th class="wid6">操作</th>
            </tr>
            </thead>
            <tbody class="employeestate">
            <c:if test="${not empty list}">
                <c:forEach items="${list}" var="VB" varStatus="status">
                    <tr>
                        <td>${VB.buyorderNo }</td>
                        <td>${VB.createrName }</td>
                        <td>${VB.addTime }</td>
                        <td>${VB.goodSum }</td>
                        <td>${VB.goodNumAble }</td>
                        <td>
                            <c:choose>
                                <c:when test="${VB.canSelect eq 1}">
                                    <a class='setWidth' style="color: #1e9fff" href="javascript:void(0)"
                                       onclick="addVB('${saleorderGoodsIds}','${VB.buyorderGoodsId}');">选择</a>
                                </c:when>
                            </c:choose>
                        </td>
                    </tr>
                </c:forEach>
            </c:if>
            </tbody>
        </table>
        <input type="hidden" name="goodId" value="${goodId}"/>
        <c:if test="${empty list }">
            <!-- 查询无结果弹出 -->
            <div class="noresult"> 查询无结果！请尝试使用其他搜索条件。</div>
        </c:if>
        <tags:page page="${page}"/>
    </div>
</div>
</body>

</html>
