package com.vedeng.order.chain.step;

import com.vedeng.common.util.StringUtil;
import com.vedeng.firstengage.dao.FirstEngageMapper;
import com.vedeng.firstengage.dao.RegistrationNumberMapper;
import com.vedeng.firstengage.model.SimpleMedicalCategory;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.order.chain.AbstractRiskHandlerStep;
import com.vedeng.order.chain.RiskEnum;
import com.vedeng.order.chain.RiskHandlerKeyConstant;
import com.vedeng.order.chain.model.RiskModel;
import com.wms.service.chain.HandlerStepContext;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Set;

/**
 * <AUTHOR>
 * @ClassName SkuRiskCheckHandler.java
 * @Description TODO sku风控信息校验
 * @createTime 2020年12月08日 15:45:00
 */
@Service
public class SkuRiskCheckHandler extends AbstractRiskHandlerStep {

    @Resource
    private CoreSkuMapper coreSkuMapper;

    @Resource
    private FirstEngageMapper firstEngageMapper;

    @Override
    public RiskModel doDealWith(HandlerStepContext context) {
        RiskModel riskModel = context.get(RiskHandlerKeyConstant.RISK_MODEL);
        String sku = context.get(RiskHandlerKeyConstant.SKU);

        CoreSku coreSku =  coreSkuMapper.getSkuInfoByNo(sku);

        SimpleMedicalCategory simpleMedicalCategory = firstEngageMapper.getSimpleMedicalCategory(sku);
        if(simpleMedicalCategory == null || simpleMedicalCategory.getRegistrationNumberId() == null){
            //非医疗器械
            return riskModel;
        }

        boolean flag = true;
        if(coreSku == null){
            flag = false;
        }if(coreSku.getStorageConditionOne() == null || coreSku.getStorageConditionOne().equals(0)){
            //存储条件（温度）
            flag = false;
        }else if(coreSku.getStorageConditionHumidityLowerValue() == null || coreSku.getStorageConditionHumidityUpperValue() == null){
            //存储条件(湿度）
            flag = false;
        }else if(StringUtil.isBlank(coreSku.getStorageConditionTwo())){
            //存储条件（其他）
            flag = false;
        }else if(coreSku.getIsEnableValidityPeriod() == null ||
                (coreSku.getIsEnableValidityPeriod().equals(1) && StringUtil.isBlank(coreSku.getEffectiveDays()))){
            //是否启用有效期管理-有效期
            flag = false;
        }

        if(!flag){
            Set<RiskEnum> riskEnumList = riskModel.getRiskEnumList();
            riskEnumList.add(RiskEnum.SKU_RISK);
            riskEnumList.add(RiskEnum.SKU_SPU_FIRST_RISK);
            riskModel.setRiskEnumList(riskEnumList);
            riskModel.setSkuRiskMessage("商品质量风控信息不完整");
            riskModel.setIsRisk(false);
            riskModel.setIsOneChek(false);
        }
        return riskModel;
    }
}
