package com.vedeng.erp.saleorder.constant;

/**
 * @Author: daniel
 * @Date: 2021/10/13 17 44
 * @Description: 销售订单相关的审核类型枚举类
 */
public enum AuditTypeEnumOfSaleOrder {

    /**
     * 订单有效性审核
     */
    VALID_CHECK(1,"订单审核"),

    CONTRACT_RETURN_CHECK(2,"合同回传审核"),

    ;

    /**
     * 审核类型
     */
    private Integer type;

    /**
     * 描述
     */
    private String desc;

    AuditTypeEnumOfSaleOrder(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static AuditTypeEnumOfSaleOrder getByType(Integer type){
        for (AuditTypeEnumOfSaleOrder item : AuditTypeEnumOfSaleOrder.values()){
            if (item.getType().equals(type)){
                return item;
            }
        }
        return null;
    }
}
