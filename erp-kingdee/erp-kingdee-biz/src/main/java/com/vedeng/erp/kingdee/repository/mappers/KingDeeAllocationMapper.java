package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeeAllocationEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * @description: 调拨单
 * @author: yana.jiang
 * @date: 2022/11/11
 */
public interface KingDeeAllocationMapper {
    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeeAllocationEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeeAllocationEntity record);

    /**
     * 批量插入
     * @param list list
     * @return int
     */
    int batchInsert(List<KingDeeAllocationEntity> list);

    /**
     * 根据fBillNo查询
     * @param fBillNo fBillNo
     * @return List<KingDeeAllocationEntity>
     */
    List<KingDeeAllocationEntity> findByFBillNo(@Param("fBillNo")String fBillNo);


}