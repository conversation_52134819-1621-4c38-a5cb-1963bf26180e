<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.mapper.CoreSkuMapper">

    <resultMap id="BaseResultMap" type="com.vedeng.goods.domain.entity.CoreSku">
        <id property="skuId" column="SKU_ID" jdbcType="INTEGER"/>
        <result property="spuId" column="SPU_ID" jdbcType="INTEGER"/>
        <result property="checkStatus" column="CHECK_STATUS" jdbcType="INTEGER"/>
        <result property="model" column="MODEL" jdbcType="VARCHAR"/>
        <result property="spec" column="SPEC" jdbcType="VARCHAR"/>
        <result property="skuNo" column="SKU_NO" jdbcType="VARCHAR"/>
        <result property="skuName" column="SKU_NAME" jdbcType="VARCHAR"/>
        <result property="showName" column="SHOW_NAME" jdbcType="VARCHAR"/>
        <result property="materialCode" column="MATERIAL_CODE" jdbcType="VARCHAR"/>
        <result property="supplyModel" column="SUPPLY_MODEL" jdbcType="VARCHAR"/>
        <result property="isStockup" column="IS_STOCKUP" jdbcType="VARCHAR"/>
        <result property="wikiHref" column="WIKI_HREF" jdbcType="VARCHAR"/>
        <result property="technicalParameter" column="TECHNICAL_PARAMETER" jdbcType="VARCHAR"/>
        <result property="performanceParameter" column="PERFORMANCE_PARAMETER" jdbcType="VARCHAR"/>
        <result property="specParameter" column="SPEC_PARAMETER" jdbcType="VARCHAR"/>
        <result property="baseUnitId" column="BASE_UNIT_ID" jdbcType="INTEGER"/>
        <result property="minOrder" column="MIN_ORDER" jdbcType="DECIMAL"/>
        <result property="goodsLength" column="GOODS_LENGTH" jdbcType="DECIMAL"/>
        <result property="goodsWidth" column="GOODS_WIDTH" jdbcType="DECIMAL"/>
        <result property="goodsHeight" column="GOODS_HEIGHT" jdbcType="DECIMAL"/>
        <result property="packageLength" column="PACKAGE_LENGTH" jdbcType="DECIMAL"/>
        <result property="packageWidth" column="PACKAGE_WIDTH" jdbcType="DECIMAL"/>
        <result property="packageHeight" column="PACKAGE_HEIGHT" jdbcType="DECIMAL"/>
        <result property="netWeight" column="NET_WEIGHT" jdbcType="DECIMAL"/>
        <result property="grossWeight" column="GROSS_WEIGHT" jdbcType="DECIMAL"/>
        <result property="unitId" column="UNIT_ID" jdbcType="INTEGER"/>
        <result property="changeNum" column="CHANGE_NUM" jdbcType="DECIMAL"/>
        <result property="packingList" column="PACKING_LIST" jdbcType="VARCHAR"/>
        <result property="afterSaleContent" column="AFTER_SALE_CONTENT" jdbcType="VARCHAR"/>
        <result property="qaYears" column="QA_YEARS" jdbcType="VARCHAR"/>
        <result property="storageConditionOne" column="STORAGE_CONDITION_ONE" jdbcType="BOOLEAN"/>
        <result property="storageConditionOneLowerValue" column="STORAGE_CONDITION_ONE_LOWER_VALUE" jdbcType="FLOAT"/>
        <result property="storageConditionOneUpperValue" column="STORAGE_CONDITION_ONE_UPPER_VALUE" jdbcType="FLOAT"/>
        <result property="storageConditionHumidityLowerValue" column="STORAGE_CONDITION_HUMIDITY_LOWER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionHumidityUpperValue" column="STORAGE_CONDITION_HUMIDITY_UPPER_VALUE"
                jdbcType="FLOAT"/>
        <result property="storageConditionTwo" column="STORAGE_CONDITION_TWO" jdbcType="VARCHAR"/>
        <result property="effectiveDayUnit" column="EFFECTIVE_DAY_UNIT" jdbcType="BOOLEAN"/>
        <result property="effectiveDays" column="EFFECTIVE_DAYS" jdbcType="VARCHAR"/>
        <result property="qaRule" column="QA_RULE" jdbcType="VARCHAR"/>
        <result property="qaOutPrice" column="QA_OUT_PRICE" jdbcType="DECIMAL"/>
        <result property="qaResponseTime" column="QA_RESPONSE_TIME" jdbcType="DECIMAL"/>
        <result property="hasBackupMachine" column="HAS_BACKUP_MACHINE" jdbcType="VARCHAR"/>
        <result property="supplierExtendGuaranteePrice" column="SUPPLIER_EXTEND_GUARANTEE_PRICE" jdbcType="DECIMAL"/>
        <result property="corePartsPriceFid" column="CORE_PARTS_PRICE_FID" jdbcType="INTEGER"/>
        <result property="returnGoodsConditions" column="RETURN_GOODS_CONDITIONS" jdbcType="BOOLEAN"/>
        <result property="freightIntroductions" column="FREIGHT_INTRODUCTIONS" jdbcType="VARCHAR"/>
        <result property="exchangeGoodsConditions" column="EXCHANGE_GOODS_CONDITIONS" jdbcType="VARCHAR"/>
        <result property="exchangeGoodsMethod" column="EXCHANGE_GOODS_METHOD" jdbcType="VARCHAR"/>
        <result property="goodsComments" column="GOODS_COMMENTS" jdbcType="VARCHAR"/>
        <result property="status" column="STATUS" jdbcType="BOOLEAN"/>
        <result property="addTime" column="ADD_TIME" jdbcType="TIMESTAMP"/>
        <result property="creator" column="CREATOR" jdbcType="INTEGER"/>
        <result property="modTime" column="MOD_TIME" jdbcType="TIMESTAMP"/>
        <result property="updater" column="UPDATER" jdbcType="INTEGER"/>
        <result property="checkTime" column="CHECK_TIME" jdbcType="TIMESTAMP"/>
        <result property="checker" column="CHECKER" jdbcType="INTEGER"/>
        <result property="operateInfoId" column="OPERATE_INFO_ID" jdbcType="INTEGER"/>
        <result property="deleteReason" column="DELETE_REASON" jdbcType="VARCHAR"/>
        <result property="lastCheckReason" column="LAST_CHECK_REASON" jdbcType="VARCHAR"/>
        <result property="taxCategoryNo" column="TAX_CATEGORY_NO" jdbcType="VARCHAR"/>
        <result property="jxMarketPrice" column="JX_MARKET_PRICE" jdbcType="DECIMAL"/>
        <result property="jxSalePrice" column="JX_SALE_PRICE" jdbcType="DECIMAL"/>
        <result property="jxFlag" column="JX_FLAG" jdbcType="INTEGER"/>
        <result property="source" column="SOURCE" jdbcType="TINYINT"/>
        <result property="pushStatus" column="PUSH_STATUS" jdbcType="INTEGER"/>
        <result property="declareDeliveryRange" column="DECLARE_DELIVERY_RANGE" jdbcType="VARCHAR"/>
        <result property="priceVerifyStatus" column="PRICE_VERIFY_STATUS" jdbcType="INTEGER"/>
        <result property="goodsBarcode" column="GOODS_BARCODE" jdbcType="VARCHAR"/>
        <result property="curingType" column="CURING_TYPE" jdbcType="BOOLEAN"/>
        <result property="curingReason" column="CURING_REASON" jdbcType="VARCHAR"/>
        <result property="isNeedTestReprot" column="IS_NEED_TEST_REPROT" jdbcType="BOOLEAN"/>
        <result property="isKit" column="IS_KIT" jdbcType="BOOLEAN"/>
        <result property="kitDesc" column="KIT_DESC" jdbcType="VARCHAR"/>
        <result property="isSameSnCode" column="IS_SAME_SN_CODE" jdbcType="BOOLEAN"/>
        <result property="isFactorySnCode" column="IS_FACTORY_SN_CODE" jdbcType="BOOLEAN"/>
        <result property="isManageVedengCode" column="IS_MANAGE_VEDENG_CODE" jdbcType="BOOLEAN"/>
        <result property="isBadGoods" column="IS_BAD_GOODS" jdbcType="BOOLEAN"/>
        <result property="isEnableFactoryBatchnum" column="IS_ENABLE_FACTORY_BATCHNUM" jdbcType="BOOLEAN"/>
        <result property="isEnableMultistagePackage" column="IS_ENABLE_MULTISTAGE_PACKAGE" jdbcType="BOOLEAN"/>
        <result property="midPackageNum" column="MID_PACKAGE_NUM" jdbcType="INTEGER"/>
        <result property="boxPackageNum" column="BOX_PACKAGE_NUM" jdbcType="INTEGER"/>
        <result property="isEnableValidityPeriod" column="IS_ENABLE_VALIDITY_PERIOD" jdbcType="TINYINT"/>
        <result property="nearTermWarnDays" column="NEAR_TERM_WARN_DAYS" jdbcType="INTEGER"/>
        <result property="overNearTermWarnDays" column="OVER_NEAR_TERM_WARN_DAYS" jdbcType="INTEGER"/>
        <result property="installTrainType" column="INSTALL_TRAIN_TYPE" jdbcType="BOOLEAN"/>
        <result property="logisticsDeliverytype" column="LOGISTICS_DELIVERYTYPE" jdbcType="BOOLEAN"/>
        <result property="avgprice" column="AVGPRICE" jdbcType="DECIMAL"/>
        <result property="latestValidOrderUser" column="LATEST_VALID_ORDER_USER" jdbcType="INTEGER"/>
        <result property="avgpriceUpdateTime" column="AVGPRICE_UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="terminalPrice" column="TERMINAL_PRICE" jdbcType="DECIMAL"/>
        <result property="distributionPrice" column="DISTRIBUTION_PRICE" jdbcType="DECIMAL"/>
        <result property="availableStockNum" column="AVAILABLE_STOCK_NUM" jdbcType="INTEGER"/>
        <result property="stockNum" column="STOCK_NUM" jdbcType="INTEGER"/>
        <result property="onSale" column="ON_SALE" jdbcType="INTEGER"/>
        <result property="isNeedReport" column="IS_NEED_REPORT" jdbcType="BOOLEAN"/>
        <result property="isAuthorized" column="IS_AUTHORIZED" jdbcType="BOOLEAN"/>
        <result property="historyName" column="HISTORY_NAME" jdbcType="VARCHAR"/>
        <result property="isNameChange" column="IS_NAME_CHANGE" jdbcType="TINYINT"/>
        <result property="oneYearSaleNum" column="ONE_YEAR_SALE_NUM" jdbcType="INTEGER"/>
        <result property="regularMaintainType" column="REGULAR_MAINTAIN_TYPE" jdbcType="BOOLEAN"/>
        <result property="regularMaintainReason" column="REGULAR_MAINTAIN_REASON" jdbcType="VARCHAR"/>
        <result property="synchronizationStatus" column="SYNCHRONIZATION_STATUS" jdbcType="BOOLEAN"/>
        <result property="isInstallable" column="IS_INSTALLABLE" jdbcType="BOOLEAN"/>
        <result property="goodsLevelNo" column="GOODS_LEVEL_NO" jdbcType="INTEGER"/>
        <result property="goodsPositionNo" column="GOODS_POSITION_NO" jdbcType="INTEGER"/>
        <result property="costPrice" column="COST_PRICE" jdbcType="DECIMAL"/>
        <result property="lastYearRatioEightySort" column="LAST_YEAR_RATIO_EIGHTY_SORT" jdbcType="INTEGER"/>
        <result property="threeMonthRatioEightySort" column="THREE_MONTH_RATIO_EIGHTY_SORT" jdbcType="INTEGER"/>
        <result property="oneMonthRatioEightySort" column="ONE_MONTH_RATIO_EIGHTY_SORT" jdbcType="INTEGER"/>
        <result property="orgIdList" column="ORG_ID_LIST" jdbcType="VARCHAR"/>
        <result property="isAvailableSale" column="IS_AVAILABLE_SALE" jdbcType="INTEGER"/>
        <result property="pushedOrgIdList" column="PUSHED_ORG_ID_LIST" jdbcType="VARCHAR"/>
        <result property="configurationList" column="CONFIGURATION_LIST" jdbcType="VARCHAR"/>
        <result property="disabledReason" column="DISABLED_REASON" jdbcType="VARCHAR"/>
        <result property="hasAfterSaleServiceLabel" column="HAS_AFTER_SALE_SERVICE_LABEL" jdbcType="BOOLEAN"/>
        <result property="actionLockNum" column="ACTION_LOCK_NUM" jdbcType="INTEGER"/>
        <result property="orderOccupyNum" column="ORDER_OCCUPY_NUM" jdbcType="INTEGER"/>
        <result property="spuType" column="SPU_TYPE" jdbcType="INTEGER"/>
        <result property="purchaseTime" column="PURCHASE_TIME" jdbcType="INTEGER"/>
        <result property="purchaseTimeUpdateTime" column="PURCHASE_TIME_UPDATE_TIME" jdbcType="TIMESTAMP"/>
        <result property="institutionLevelIds" column="INSTITUTION_LEVEL_IDS" jdbcType="VARCHAR"/>
        <result property="taxClassificationCode" column="TAX_CLASSIFICATION_CODE" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@sql select -->
        SKU_ID,SPU_ID,CHECK_STATUS,
        MODEL,SPEC,SKU_NO,
        SKU_NAME,SHOW_NAME,MATERIAL_CODE,
        SUPPLY_MODEL,IS_STOCKUP,WIKI_HREF,
        TECHNICAL_PARAMETER,PERFORMANCE_PARAMETER,SPEC_PARAMETER,
        BASE_UNIT_ID,MIN_ORDER,GOODS_LENGTH,
        GOODS_WIDTH,GOODS_HEIGHT,PACKAGE_LENGTH,
        PACKAGE_WIDTH,PACKAGE_HEIGHT,NET_WEIGHT,
        GROSS_WEIGHT,UNIT_ID,CHANGE_NUM,
        PACKING_LIST,AFTER_SALE_CONTENT,QA_YEARS,
        STORAGE_CONDITION_ONE,STORAGE_CONDITION_ONE_LOWER_VALUE,STORAGE_CONDITION_ONE_UPPER_VALUE,
        STORAGE_CONDITION_HUMIDITY_LOWER_VALUE,STORAGE_CONDITION_HUMIDITY_UPPER_VALUE,STORAGE_CONDITION_TWO,
        EFFECTIVE_DAY_UNIT,EFFECTIVE_DAYS,QA_RULE,
        QA_OUT_PRICE,QA_RESPONSE_TIME,HAS_BACKUP_MACHINE,
        SUPPLIER_EXTEND_GUARANTEE_PRICE,CORE_PARTS_PRICE_FID,RETURN_GOODS_CONDITIONS,
        FREIGHT_INTRODUCTIONS,EXCHANGE_GOODS_CONDITIONS,EXCHANGE_GOODS_METHOD,
        GOODS_COMMENTS,STATUS,ADD_TIME,
        CREATOR,MOD_TIME,UPDATER,
        CHECK_TIME,CHECKER,OPERATE_INFO_ID,
        DELETE_REASON,LAST_CHECK_REASON,TAX_CATEGORY_NO,
        JX_MARKET_PRICE,JX_SALE_PRICE,JX_FLAG,
        SOURCE,PUSH_STATUS,PUSH_STATUSCopy,
        DECLARE_DELIVERY_RANGE,PRICE_VERIFY_STATUS,GOODS_BARCODE,
        CURING_TYPE,CURING_REASON,IS_NEED_TEST_REPROT,
        IS_KIT,KIT_DESC,IS_SAME_SN_CODE,
        IS_FACTORY_SN_CODE,IS_MANAGE_VEDENG_CODE,IS_BAD_GOODS,
        IS_ENABLE_FACTORY_BATCHNUM,IS_ENABLE_MULTISTAGE_PACKAGE,MID_PACKAGE_NUM,
        BOX_PACKAGE_NUM,IS_ENABLE_VALIDITY_PERIOD,NEAR_TERM_WARN_DAYS,
        OVER_NEAR_TERM_WARN_DAYS,INSTALL_TRAIN_TYPE,LOGISTICS_DELIVERYTYPE,
        AVGPRICE,LATEST_VALID_ORDER_USER,AVGPRICE_UPDATE_TIME,
        TERMINAL_PRICE,DISTRIBUTION_PRICE,AVAILABLE_STOCK_NUM,
        STOCK_NUM,ON_SALE,IS_NEED_REPORT,
        IS_AUTHORIZED,HISTORY_NAME,IS_NAME_CHANGE,
        ONE_YEAR_SALE_NUM,REGULAR_MAINTAIN_TYPE,REGULAR_MAINTAIN_REASON,
        SYNCHRONIZATION_STATUS,IS_INSTALLABLE,GOODS_LEVEL_NO,
        GOODS_POSITION_NO,COST_PRICE,LAST_YEAR_RATIO_EIGHTY_SORT,
        THREE_MONTH_RATIO_EIGHTY_SORT,ONE_MONTH_RATIO_EIGHTY_SORT,ORG_ID_LIST,
        IS_AVAILABLE_SALE,PUSHED_ORG_ID_LIST,CONFIGURATION_LIST,
        DISABLED_REASON,HAS_AFTER_SALE_SERVICE_LABEL,ACTION_LOCK_NUM,
        ORDER_OCCUPY_NUM,SPU_TYPE,PURCHASE_TIME,
        PURCHASE_TIME_UPDATE_TIME,INSTITUTION_LEVEL_IDS,TAX_CLASSIFICATION_CODE
        <!--@sql from V_CORE_SKU-->
    </sql>
    <sql id="Base_Column_List2">
        <!--@sql select -->
        SKU_ID,IS_INSTALLABLE,IS_DIRECT,STATUS,CHECK_STATUS,SPU_ID,SPU_TYPE
        <!--@sql from V_CORE_SKU-->
    </sql>


    <select id="listGoodsInfoForSerachGoodsQuery" parameterType="com.vedeng.goods.query.SearchGoodsQuery"
            resultType="com.vedeng.goods.domain.dto.GoodsDto">
        SELECT
        A.SKU_ID skuId,
        A.SKU_NAME skuName,
        A.SKU_NO sku,
        CASE
        WHEN B.SPU_TYPE = 316 OR B.SPU_TYPE = 1008 THEN CONCAT('型号：',A.MODEL)
        WHEN B.SPU_TYPE = 317 OR B.SPU_TYPE = 318 THEN CONCAT('规格：',A.SPEC)
        ELSE ''
        END AS modelOrSpec,
        UU.UNIT_NAME unitName,
        IFNULL(A.TERMINAL_PRICE,0) terminalPrice,
        IFNULL(A.DISTRIBUTION_PRICE,0) distributionPrice,
        B.BRAND_ID brandId,
        E.BRAND_NAME brandName,
        B.CATEGORY_ID categoryId,
        CB.BASE_CATEGORY_NAME baseCategoryName,
        IFNULL(A.ONE_YEAR_SALE_NUM,0) ONE_YEAR_SALE_NUM,
        A.AVAILABLE_STOCK_NUM availableStockNum
        FROM
        V_CORE_SKU A
        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
        LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
        LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
        LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
        WHERE
        A.STATUS = 1 AND B.STATUS = 1
        <if test="searchKeyword != null">
            and concat_ws(' ',A.SKU_NO,A.SKU_NAME,A.MODEL,A.SPEC) like
            concat('%',#{searchKeyword,jdbcType=VARCHAR},'%')
        </if>
        <if test="thirdCategoryId != null">
            and CB.BASE_CATEGORY_ID = #{thirdCategoryId,jdbcType=INTEGER}
        </if>
        <if test="brandIdList != null">
            and E.BRAND_ID IN
            <foreach collection="brandIdList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="availableStockFlag != null and availableStockFlag">
            and IFNULL(A.AVAILABLE_STOCK_NUM,0) <![CDATA[>]]>  0
        </if>
        <if test="availableStockFlag != null and !availableStockFlag">
            and IFNULL(A.AVAILABLE_STOCK_NUM,0) <![CDATA[=]]>  0
        </if>

        <if test="sortType != null and sortType == 0">
            order by A.SKU_ID desc
        </if>
        <if test="sortType != null and sortType == 1">
            order by ONE_YEAR_SALE_NUM desc
        </if>
        <if test="sortType != null and sortType == 2">
            order by TERMINAL_PRICE
        </if>
        <if test="sortType != null and sortType == 3">
            order by DISTRIBUTION_PRICE
        </if>
        <if test="lift != null and lift == 0">
            desc
        </if>
    </select>


    <select id="getCoreSkuInfoBySkuId" resultType="com.vedeng.goods.dto.CoreSkuDto"
            parameterType="java.lang.Integer">
        SELECT
            A.SKU_NAME,
            A.SKU_NO sku,
            E.BRAND_NAME,
            A.MATERIAL_CODE,
            A.SPEC,
            A.MODEL,
            UU.UNIT_NAME,
            A.MIN_ORDER,
            A.AVAILABLE_STOCK_NUM,
            A.STOCK_NUM,
            A.ORDER_OCCUPY_NUM,
            A.ACTION_LOCK_NUM,
            A.PURCHASE_TIME,
            A.IS_NEED_REPORT,
            A.IS_INSTALLABLE,
            B.SPU_TYPE,
            A.SKU_ID,
            B.SPU_ID,
        TSCC.COST_CATEGORY_ID,
        TSCC.CATEGORY_NAME,
        A.HAVE_STOCK_MANAGE
        FROM
            V_CORE_SKU A
            LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
            LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
            LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
            LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
            LEFT JOIN T_SYS_COST_CATEGORY TSCC on TSCC.COST_CATEGORY_ID = A.COST_CATEGORY_ID
        WHERE
            A.STATUS = 1
            AND B.STATUS = 1 and A.SKU_ID=#{skuId,jdbcType=INTEGER}
    </select>

    <select id="getInfoBySkuNo" resultType="com.vedeng.goods.dto.CoreSkuDto"
            parameterType="java.lang.String">
        SELECT A.SKU_ID,
               A.SKU_NAME,
               A.SKU_NO                                                                           sku,
               E.BRAND_NAME,
               A.MATERIAL_CODE,
               A.SPEC,
               A.MODEL,
               CASE
                   WHEN B.SPU_TYPE = 316 OR B.SPU_TYPE = 1008 THEN A.MODEL
                   WHEN B.SPU_TYPE = 317 OR B.SPU_TYPE = 318 THEN A.SPEC
                   ELSE ''
                   END            AS modelOrSpec,
               UU.UNIT_NAME,
               A.MIN_ORDER,
               A.AVAILABLE_STOCK_NUM,
               A.STOCK_NUM,
               A.ORDER_OCCUPY_NUM,
               A.ACTION_LOCK_NUM,
               A.PURCHASE_TIME,
               A.IS_NEED_REPORT,
               A.IS_INSTALLABLE,
               A.GOODS_POSITION_NO,
               B.SPU_TYPE,
               A.SKU_ID,
               B.SPU_ID,
               TSCC.COST_CATEGORY_ID,
               TSCC.CATEGORY_NAME,
               A.HAVE_STOCK_MANAGE,
               case
                  when A.IS_DIRECT = 1 THEN TSDD.DIRECT_DELIVERY_TIME_END
                  when A.IS_DIRECT = 0 THEN TSDD.COMMON_DELIVERY_TIME_END
                  ELSE ''
               END AS expectDeliveryTime,
               A.TECHNICAL_PARAMETER,
               A.EFFECTIVE_DAYS,
               A.SERVICE_LIFE,
               apply.GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD as hostPeriod,
               PC.PRODUCT_COMPANY_CHINESE_NAME as companyName,
               RN.REGISTRATION_NUMBER
        FROM V_CORE_SKU A
                 LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
        left join T_FIRST_ENGAGE FE on B.FIRST_ENGAGE_ID = FE.FIRST_ENGAGE_ID
        left join T_REGISTRATION_NUMBER RN on FE.REGISTRATION_NUMBER_ID = RN.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY PC ON RN.PRODUCT_COMPANY_ID = PC.PRODUCT_COMPANY_ID
                 LEFT JOIN T_SKU_DELIVERY_DATA TSDD on A.SKU_ID = TSDD.SKU_ID  AND TSDD.IS_DELETE = 0
                 LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_APPLY apply on A.SKU_NO = apply.SKU_NO
                 LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
                 LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
                 LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
                 LEFT JOIN T_SYS_COST_CATEGORY TSCC on TSCC.COST_CATEGORY_ID = A.COST_CATEGORY_ID
        WHERE A.STATUS = 1
          AND B.STATUS = 1
          and A.SKU_NO = #{skuNo,jdbcType=VARCHAR}
    </select>

    <select id="getInfoBySkuNos" resultType="com.vedeng.goods.dto.CoreSkuDto" >
        SELECT A.SKU_ID,
        B.CATEGORY_ID,
        A.SKU_NAME,
        A.SKU_NO                                                                           sku,
        E.BRAND_NAME,
        A.MATERIAL_CODE,
        A.SPEC,
        A.MODEL,
        A.CHECK_STATUS,
        CASE
        WHEN B.SPU_TYPE = 316 OR B.SPU_TYPE = 1008 THEN A.MODEL
        WHEN B.SPU_TYPE = 317 OR B.SPU_TYPE = 318 THEN A.SPEC
        ELSE ''
        END            AS modelOrSpec,
        UU.UNIT_NAME,
        A.MIN_ORDER,
        A.AVAILABLE_STOCK_NUM,
        A.STOCK_NUM,
        A.ORDER_OCCUPY_NUM,
        A.ACTION_LOCK_NUM,
        A.PURCHASE_TIME,
        A.IS_NEED_REPORT,
        A.IS_INSTALLABLE,
        A.GOODS_LEVEL_NO,
        A.GOODS_POSITION_NO,
        B.SPU_TYPE,
        A.SKU_ID,
        B.SPU_ID,
        TSCC.COST_CATEGORY_ID,
        TSCC.CATEGORY_NAME,
        A.HAVE_STOCK_MANAGE,
        case
        when A.IS_DIRECT = 1 THEN TSDD.DIRECT_DELIVERY_TIME_END
        when A.IS_DIRECT = 0 THEN TSDD.COMMON_DELIVERY_TIME_END
        ELSE ''
        END AS expectDeliveryTime,
        A.TECHNICAL_PARAMETER,
        A.EFFECTIVE_DAYS,
        A.SERVICE_LIFE,
        apply.GUARANTEE_POLICY_HOST_GUARANTEE_PERIOD as hostPeriod,
        PC.PRODUCT_COMPANY_CHINESE_NAME as companyName,
        RN.REGISTRATION_NUMBER
        FROM V_CORE_SKU A
        LEFT JOIN V_CORE_SPU B ON A.SPU_ID = B.SPU_ID
        left join T_FIRST_ENGAGE FE on B.FIRST_ENGAGE_ID = FE.FIRST_ENGAGE_ID
        left join T_REGISTRATION_NUMBER RN on FE.REGISTRATION_NUMBER_ID = RN.REGISTRATION_NUMBER_ID
        LEFT JOIN T_PRODUCT_COMPANY PC ON RN.PRODUCT_COMPANY_ID = PC.PRODUCT_COMPANY_ID
        LEFT JOIN T_SKU_DELIVERY_DATA TSDD on A.SKU_ID = TSDD.SKU_ID  AND TSDD.IS_DELETE = 0
        LEFT JOIN T_AFTER_SALE_SERVICE_STANDARD_APPLY apply on A.SKU_NO = apply.SKU_NO
        LEFT JOIN T_BRAND E ON B.BRAND_ID = E.BRAND_ID
        LEFT JOIN V_BASE_CATEGORY CB ON B.CATEGORY_ID = CB.BASE_CATEGORY_ID
        LEFT JOIN T_UNIT UU ON UU.UNIT_ID = A.BASE_UNIT_ID
        LEFT JOIN T_SYS_COST_CATEGORY TSCC on TSCC.COST_CATEGORY_ID = A.COST_CATEGORY_ID
        WHERE A.STATUS = 1
        AND B.STATUS = 1
        and A.SKU_NO IN
        <foreach collection="skuNos" item="skuNo" open="(" close=")" separator=",">
            #{skuNo,jdbcType=VARCHAR}
        </foreach>
        GROUP BY A.SKU_NO
    </select>

    <select id="getGoodsAttachment" parameterType="java.lang.Integer" resultType="com.vedeng.goods.domain.entity.GoodsAttachmentEntity">
        SELECT
        GOODS_ID, ATTACHMENT_TYPE, `DOMAIN`,
        URI, ALT, SORT, IS_DEFAULT, `STATUS`,SYN_SUCCESS,ORIGINAL_FILEPATH,concat('https://',DOMAIN,URI) URL
        FROM
        T_GOODS_ATTACHMENT
        WHERE `STATUS` = 1
        AND GOODS_ID = #{goodsId,jdbcType=INTEGER}
        AND ATTACHMENT_TYPE = 1001
        ORDER BY SORT limit 1
    </select>

    <select id="getCoreSkuInfoBySkuNo" parameterType="java.lang.String" resultType="com.vedeng.goods.dto.CoreSkuDto">
        select
        <include refid="Base_Column_List2" />
        from V_CORE_SKU
        where STATUS = 1 and SKU_NO = #{skuNo,jdbcType=VARCHAR} limit 1
    </select>

    <select id="findBySkuId" resultMap="BaseResultMap">
        select
        *
        from V_CORE_SKU
        where SKU_ID=#{skuId,jdbcType=INTEGER}
    </select>

    <select id="selectAllSkuByKeywords" resultType="com.vedeng.goods.vo.CoreSkuVo">
        SELECT
        SKU.SKU_ID,
        SKU.SKU_NO,
        SKU.SKU_NAME,
        SKU.SPEC,
        SKU.MODEL,
        B.BRAND_NAME,
        SPU.SPU_TYPE
        FROM
        V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_BRAND B ON B.BRAND_ID = SPU.BRAND_ID
        WHERE
        1=1
        <if test="skuName != null and skuName != ''">
            AND SKU.SKU_NAME LIKE CONCAT( '%', #{skuName,jdbcType=VARCHAR}, '%' )
        </if>
        <if test="skuNo != null and skuNo != ''">
            AND SKU.SKU_NO LIKE CONCAT( '%', #{skuNo,jdbcType=VARCHAR}, '%' )
        </if>
        <if test="brandName != null and brandName != ''">
            AND B.BRAND_NAME LIKE CONCAT( '%', #{brandName,jdbcType=VARCHAR}, '%' )
        </if>
        <if test="specModel != null and specModel != ''">
            AND SKU.MODEL LIKE CONCAT( '%', #{specModel,jdbcType=VARCHAR}, '%' )
            OR SKU.SPEC LIKE CONCAT( '%', #{specModel,jdbcType=VARCHAR}, '%' )
        </if>

    </select>


    <update id="updateBatchSelectiveInstitutionLevel" parameterType="java.util.List">
        update V_CORE_SKU
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="INSTITUTION_LEVEL_IDS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.institutionLevelIds != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.institutionLevelIds,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where SKU_NO in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
    </update>

    <update id="updateBatchSelectiveInstitutionLevelSkuSearch" parameterType="java.util.List">
        update V_CORE_SKU_SEARCH
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="INSTITUTION_LEVEL_IDS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.institutionLevelIds != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.institutionLevelIds,jdbcType=VARCHAR}
                    </if>
                </foreach>
            </trim>
        </trim>
        where SKU_NO in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="selectSkusHaveExclusive" resultType="java.lang.Integer">
        select count(1) from V_CORE_SKU a where a.SKU_NO in
        <foreach collection="skuNos" item="sku" open="(" close=")" separator=",">
            #{sku,jdbcType=VARCHAR}
        </foreach>
        and a.GOODS_POSITION_NO = 5
    </select>

    <update id="updateBatchSelectiveInitSkuInfo2Front" parameterType="java.util.List">
        update V_CORE_SKU
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="PURCHASE_TIME = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.purchaseTime != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.purchaseTime,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="IS_NEED_REPORT = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.isNeedReport != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.isNeedReport,jdbcType=BOOLEAN}
                    </if>
                </foreach>
            </trim>
            <trim prefix="GOODS_LEVEL_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.goodsLevelNo != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.goodsLevelNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="CHECK_STATUS = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.checkStatus != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.checkStatus,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            <trim prefix="GOODS_POSITION_NO = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.goodsPositionNo != null">
                        when SKU_NO = #{item.skuNo,jdbcType=VARCHAR} then #{item.goodsPositionNo,jdbcType=INTEGER}
                    </if>
                </foreach>
            </trim>
            `STATUS` = true
        </trim>
        where SKU_NO in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.skuNo,jdbcType=VARCHAR}
        </foreach>
    </update>

    <select id="listBySkuNoList" parameterType="java.util.List" resultMap="BaseResultMap">
        SELECT
        A.SKU_ID skuId,
        A.SPU_ID spuId,
        A.SKU_NO skuNo
        FROM
        V_CORE_SKU A
        where A.SKU_NO in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getGoodsInfoBySkuId" resultType="com.vedeng.goods.vo.CoreSkuVo">
         SELECT SKU.SKU_NO, SKU.SHOW_NAME,SKU.MODEL, B.BRAND_NAME,
         U.UNIT_NAME, SKU.IS_VIRTURE_SKU, SKU.COST_CATEGORY_ID,
         C.CATEGORY_NAME, SKU.HAVE_STOCK_MANAGE, SKU.SKU_ID
      FROM V_CORE_SKU SKU
      LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
      LEFT JOIN T_BRAND B ON SPU.BRAND_ID = B.BRAND_ID
      LEFT JOIN T_UNIT U ON SKU.UNIT_ID = U.UNIT_ID
      LEFT JOIN T_SYS_COST_CATEGORY C ON SKU.COST_CATEGORY_ID = C.COST_CATEGORY_ID
      WHERE SKU.SKU_ID = #{skuId,jdbcType=INTEGER}
    </select>

    <select id="getGoodsInfoBySkuNos" resultType="com.vedeng.goods.vo.CoreSkuVo">
        SELECT SKU.SKU_NO, SKU.SHOW_NAME,SKU.MODEL, B.BRAND_NAME,
        U.UNIT_NAME, SKU.IS_VIRTURE_SKU, SKU.COST_CATEGORY_ID,
        C.CATEGORY_NAME, SKU.HAVE_STOCK_MANAGE, SKU.SKU_ID,SKU.IS_DIRECT
        FROM V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_BRAND B ON SPU.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT U ON SKU.UNIT_ID = U.UNIT_ID
        LEFT JOIN T_SYS_COST_CATEGORY C ON SKU.COST_CATEGORY_ID = C.COST_CATEGORY_ID
        WHERE SKU.SKU_NO in
        <foreach collection="skuNos" separator="," close=")" open="(" item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getAllVirtualGoodsInfo" resultType="com.vedeng.goods.vo.CoreSkuVo">
        SELECT
            SKU_ID
        FROM
            V_CORE_SKU
        WHERE
            IS_VIRTURE_SKU = 1
        <if test="status == 1">
            AND STATUS = 1
        </if>
    </select>

    <select id="batchQueryProductManageAndAsist" resultType="com.vedeng.goods.dto.ProductManageAndAsistDto">
        select
        K.SKU_NO,
        U.USER_ID as productAssitUserId,
        U.USERNAME as productAssitName,
        M.USER_ID AS productManageUserId,
        M.USERNAME AS productManageName
        from V_CORE_SKU K
        LEFT JOIN V_CORE_SPU P ON K.SPU_ID=P.SPU_ID
        LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
        where K.SKU_NO  in
        <foreach collection="list" item="sku" open="(" close=")" separator=",">
            #{sku,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="getKingDeeSkuInfoBySkuId" resultType="com.vedeng.goods.dto.KingDeeSkuInfoDto">
      SELECT
        vcs.SKU_ID,
        vcs.SPU_ID,
        vcs.SKU_NO,
        vcs.SKU_NAME,
        vcs.SPEC ,
        vcs.MODEL,
        vcs.TAX_CATEGORY_NO,
        spu.SPU_TYPE,
        vcs.MATERIAL_CODE ,
        tb.BRAND_NAME,
        trn.REGISTRATION_NUMBER ,
        trn.PRODUCT_USE_RANGE,
        tc.BASE_CATEGORY_NAME AS thirdCategoryName,
        tc2.BASE_CATEGORY_NAME AS secondCategoryName,
        tc3.BASE_CATEGORY_NAME AS firstCategoryName,
        CASE
            spu.NO_MEDICAL_FIRST_TYPE
        WHEN 1 THEN '科研产品'
            WHEN 2 THEN '医疗器械附件产品'
            WHEN 3 THEN '其他非医疗器械'
            ELSE ''
        END noMedicalFirstTypeName,
        CASE
            spu.NO_MEDICAL_SECOND_TYPE
        WHEN 1 THEN '科研配件'
            WHEN 2 THEN '实验耗材'
            WHEN 3 THEN '仪器设备'
            ELSE ''
        END noMedicalSecondTypeName,
        tu.UNIT_KING_DEE_NO
        FROM
            V_CORE_SKU vcs
        LEFT JOIN V_CORE_SPU spu ON
            vcs.SPU_ID = spu.SPU_ID
        LEFT JOIN T_BRAND tb ON
            spu.BRAND_ID = tb.BRAND_ID
        LEFT JOIN T_FIRST_ENGAGE tfe ON
            spu.FIRST_ENGAGE_ID = tfe.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER trn ON
            trn.REGISTRATION_NUMBER_ID = tfe.REGISTRATION_NUMBER_ID
        LEFT JOIN V_BASE_CATEGORY tc ON
            spu.CATEGORY_ID = tc.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY tc2 ON
            tc.PARENT_ID = tc2.BASE_CATEGORY_ID
        LEFT JOIN V_BASE_CATEGORY tc3 ON
            tc2.PARENT_ID = tc3.BASE_CATEGORY_ID
        LEFT JOIN T_UNIT tu ON
            vcs.BASE_UNIT_ID = tu.UNIT_ID
        WHERE
            1 = 1
            <if test="skuId != null">
                and  vcs.SKU_ID = #{skuId,jdbcType=INTEGER}
            </if>
            <if test="limit != null">
                AND vcs.CHECK_STATUS = 3
                limit #{limit,jdbcType=INTEGER}, 1000
            </if>
    </select>
    <select id="getCheckPassSkuNum" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        from
            V_CORE_SKU vcs
        WHERE
            vcs.CHECK_STATUS = 3
    </select>
    <select id="getAllSkuId" resultType="java.lang.Integer">
        SELECT SKU_ID
        FROM V_CORE_SKU
    </select>
    <!--auto generated by MybatisCodeHelper on 2023-01-17-->
    <select id="findBySkuIdIn" resultType="com.vedeng.goods.vo.CoreSkuVo">
        select SKU.SKU_NO,
        SKU.SHOW_NAME,
        SKU.MODEL,
        B.BRAND_NAME,
        U.UNIT_NAME,
        SKU.IS_VIRTURE_SKU,
        SKU.COST_CATEGORY_ID,
        C.CATEGORY_NAME,
        SKU.HAVE_STOCK_MANAGE,
        SKU.SKU_ID
        from V_CORE_SKU SKU
        LEFT JOIN V_CORE_SPU SPU ON SKU.SPU_ID = SPU.SPU_ID
        LEFT JOIN T_BRAND B ON SPU.BRAND_ID = B.BRAND_ID
        LEFT JOIN T_UNIT U ON SKU.UNIT_ID = U.UNIT_ID
        LEFT JOIN T_SYS_COST_CATEGORY C ON SKU.COST_CATEGORY_ID = C.COST_CATEGORY_ID
        where SKU.SKU_ID in
        <foreach item="item" index="index" collection="skuIdCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </select>

<!--auto generated by MybatisCodeHelper on 2023-01-17-->
    <select id="findAllSpecialSkus" resultType="java.lang.Integer">
        select SKU_ID
        from V_CORE_SKU a
                 left join T_SYS_COST_CATEGORY b on a.COST_CATEGORY_ID = b.COST_CATEGORY_ID
        where a.IS_VIRTURE_SKU = 1
          and a.STATUS = 1
          and b.IS_NEED_PURCHASE = 0
        union
        SELECT COMMENTS SKU_ID
        FROM T_SYS_OPTION_DEFINITION
        WHERE PARENT_ID = 693
    </select>

    <select id="getTaxCategoryNo" resultType="java.lang.String">
        select TAX_CATEGORY_NO from V_CORE_SKU where SKU_ID = #{goodsId,jdbcType=INTEGER}
    </select>

    <select id="getSkuImage" resultType="com.vedeng.goods.domain.dto.SkuImageDto">
        SELECT SKU.SKU_NO,
        CONCAT(
        CASE
        WHEN ATTACHMENT.DOMAIN LIKE '%ivedeng%' THEN 'http://'
        ELSE 'https://'
        END,
        ATTACHMENT.DOMAIN,
        ATTACHMENT.URI
        ) as IMG_URL
        FROM V_CORE_SKU SKU
        LEFT JOIN V_CORE_OPERATE_INFO INFO ON SKU.SKU_ID = INFO.SKU_ID
        LEFT JOIN T_GOODS_ATTACHMENT ATTACHMENT ON ATTACHMENT.STATUS = 1  AND ATTACHMENT.ATTACHMENT_TYPE = 1001  AND ATTACHMENT.GOODS_ID = INFO.SKU_ID
        WHERE
        SKU.SKU_NO IN
        <foreach collection="skuNoList" index="index" item="skuNo" open="(" separator="," close=")">
            #{skuNo, jdbcType=VARCHAR}
        </foreach>
        AND INFO.OPERATE_INFO_TYPE = 2
        GROUP BY SKU.SKU_NO
        UNION ALL
        SELECT SKU.SKU_NO,
        CONCAT(
        CASE
        WHEN ATTACHMENT.DOMAIN LIKE '%ivedeng%' THEN 'http://'
        ELSE 'https://'
        END,
        ATTACHMENT.DOMAIN,
        ATTACHMENT.URI
        ) as IMG_URL
        FROM V_CORE_SKU SKU
        LEFT JOIN V_CORE_OPERATE_INFO INFO ON SKU.SKU_ID = INFO.SKU_ID
        LEFT JOIN T_GOODS_ATTACHMENT ATTACHMENT ON ATTACHMENT.STATUS = 1  AND ATTACHMENT.ATTACHMENT_TYPE = 1002 AND ATTACHMENT.GOODS_ID = INFO.SPU_ID
        WHERE
        SKU.SKU_NO in
        <foreach collection="skuNoList" index="index" item="skuNo" open="(" separator="," close=")">
            #{skuNo, jdbcType=VARCHAR}
        </foreach>
        AND INFO.OPERATE_INFO_TYPE = 1
        GROUP BY SKU.SKU_NO
    </select>


<!--auto generated by MybatisCodeHelper on 2024-12-05-->
    <select id="findBySkuNoIn" resultType="com.vedeng.goods.dto.CoreSkuDto">
        select
        SKU_ID,
        SKU_NO sku,
        GOODS_LEVEL_NO,
        CHECK_STATUS
        from V_CORE_SKU
        where SKU_NO in
        <foreach item="item" index="index" collection="skuNoCollection"
                 open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>


    <select id="skuTipMap" resultType="map">
        select
        F.FIRST_ENGAGE_ID FIRST_ENGAGE_ID,F.STANDARD_CATEGORY_TYPE,
        F.NEW_STANDARD_CATEGORY_ID,F.OLD_STANDARD_CATEGORY_ID,
        P.SPU_ID,P.SPU_TYPE,
        K.MATERIAL_CODE AS "materialCode",
        ifnull(NUM.REGISTRATION_NUMBER,'') "registrationNumber",
        CASE NUM.MANAGE_CATEGORY_LEVEL
        WHEN 968 THEN '一类医疗器械'
        WHEN  969 THEN '二类医疗器械'
        WHEN  970 THEN '三类医疗器械'
        ELSE '' END AS "manageCategoryLevel",
        M.USERNAME 'managerName',
        U.USERNAME AS 'assistName',
        ifnull(K.PACKING_LIST,'') "packingList" ,
        CONCAT(IFNULL(M.USERNAME, ''), ',', IFNULL(U.USERNAME, '')) AS "productManager",
        CASE K.CHECK_STATUS
        WHEN 0 THEN '待完善'
        WHEN 1 THEN '审核中'
        WHEN 2 THEN '审核不通过'
        WHEN 3 THEN '审核通过'
        else '' end as "checkStatus",
        K.SKU_ID,
        K.SKU_NO,
        K.SHOW_NAME,
        IFNULL(K.SPEC,'') SPEC,
        B.BRAND_NAME,
        ifnull(K.MODEL,'')  MODEL,
        ifnull(UN.UNIT_NAME,'') UNIT_NAME,
        CASE QA_YEARS
        WHEN NULL THEN ''
        WHEN '' THEN ''
        else CONCAT(QA_YEARS,"年") end as "qaYears",
        CASE P.SPU_LEVEL
        WHEN 0 THEN '其他产品'
        WHEN 1 THEN '核心产品'
        WHEN 2 THEN '临时产品'
        ELSE '' END AS GOODS_LEVEL_NAME,
        OPT.TITLE AS GOODS_TYPE_NAME,
        IFNULL(K.AVAILABLE_STOCK_NUM,0) "availableStockNum",
        IFNULL(K.STOCK_NUM,0) "stockNum",
        IFNULL(K.ORDER_OCCUPY_NUM,0) "occupynum",
        P.FIRST_ENGAGE_ID, P.SPU_TYPE
        from V_CORE_SKU K LEFT JOIN V_CORE_SPU P
        ON K.SPU_ID=P.SPU_ID
        LEFT JOIN T_FIRST_ENGAGE F ON P.FIRST_ENGAGE_ID=F.FIRST_ENGAGE_ID
        LEFT JOIN T_REGISTRATION_NUMBER NUM ON NUM.REGISTRATION_NUMBER_ID=F.REGISTRATION_NUMBER_ID
        LEFT JOIN T_USER U ON U.USER_ID=P.ASSIGNMENT_ASSISTANT_ID
        LEFT JOIN T_USER M ON M.USER_ID=P.ASSIGNMENT_MANAGER_ID
        LEFT JOIN T_BRAND B ON B.BRAND_ID=P.BRAND_ID
        left join T_UNIT UN ON UN.UNIT_ID=K.BASE_UNIT_ID
        LEFT JOIN T_SYS_OPTION_DEFINITION OPT ON P.SPU_TYPE = OPT.SYS_OPTION_DEFINITION_ID
        where K.SKU_NO = #{skuNo,jdbcType=VARCHAR}
        LIMIT 1
    </select>
</mapper>
