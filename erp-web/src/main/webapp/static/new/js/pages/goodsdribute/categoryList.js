function choiceSecondCategory(id) {
    checkLogin();
    $(".firstCatrgoryId").val(id);
    $(".first").removeAttr("style");
    $(".second").removeAttr("style");
    $("#button_id").attr('disabled',true);
    var a='#'+id;
    $(a).attr("style","background-color: #00b7ee");
    $('.confirmButton').attr("style","background-color: gainsboro");
    $.ajax({
        type: "POST",
        url: page_url + "/category/base/getSecondCategoryList.do",
        data: {baseCategoryId: id},
        dataType: 'json',
        async: false,
        success: function (data) {
            $('.secondCategory').html("");
            $.each(data.data, function (i, n) {
                var vid=n.baseCategoryId;
                var name=n.baseCategoryName;
                var final="<li class='second' id="+vid+" onclick='choiceFinalCategory("+vid+")' value="+vid+">"+name+"</li>";
                $('.secondCategory').append(final);
            });

        },
        error: function (data) {
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
            }else {
                layer.alert("操作失败")
            }
        }
    });
}

function choiceFinalCategory(vid) {
    checkLogin();
    $(".second").removeAttr("style");
    $(".confirmButton").removeAttr("style");
    $('#'+vid).attr("style","background-color: #00b7ee");
    $(".secondCatrgoryId").val(vid);
    $('#button_id').removeAttr("disabled")
}

function qd() {
    checkLogin();
    var a=$(".firstCatrgoryId").val();
    var b=$(".secondCatrgoryId").val();
    var c=$(".thirdCatrgoryId").val();

    var searchUrl = page_url+"/category/base/commitCategory.do?firstCategoryId="+a+"&secondCatrgoryId="+b+"&thirdCatrgoryId="+c;

    window.parent.choice(searchUrl)
    /*$("#popEngineer").attr('layerParams','{"width":"500px","height":"500px","title":"分类迁移确认","link":"'+searchUrl+'"}');
    $("#popEngineer").click();*/
}


function searchCategory(thirdCategory) {
    checkLogin();
    var keyWords=$('#sousuo').val();
    var searchUrl = page_url+"/category/base/choiceCategory.do?keyWords="+keyWords+"&thirdCategoryId="+thirdCategory;
    window.parent.searchCategory2(searchUrl)
}