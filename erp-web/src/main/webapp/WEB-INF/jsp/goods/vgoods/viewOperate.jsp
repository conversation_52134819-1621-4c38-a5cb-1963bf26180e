<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta http-equiv="X-UA-Compatible" content="ie=edge">
  <title>运营信息维护</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/tag.css?rnd=${resourceVersionKey}">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/lib/form.css?rnd=${resourceVersionKey}">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/viewOperate.css?rnd=${resourceVersionKey}">
    <link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">

</head>
<style>
    .layui-form-checked[lay-skin=primary] i{
        border-color:#FFF !important;
        background-color:#3384F0 !important;
        color:#FFF !important;
        font-weight: bold;
    }
</style>
<body>
<div class="detail-wrap">
    <div class="detail-title">
        查看SKU：${coreOperateInfoGenerateVo.productName}
    </div>
    <input type="hidden" id="canedit" value="${canedit}">
    <div class="tab-nav">
        <c:if test="${coreOperateInfoGenerateVo.operateInfoType == 1}">
            <a class="tab-item"
               href="${pageContext.request.contextPath}/goods/vgoods/viewSpu.do?spuId=${coreOperateInfoGenerateVo.spuId}&&pageType=0">基本信息</a>
            <a class="tab-item current" href="./viewOperate.do?spuId=${coreOperateInfoGenerateVo.spuId}">运营信息</a>
        </c:if>
        <c:if test="${coreOperateInfoGenerateVo.operateInfoType == 2}">
            <a class="tab-item"
               href="${pageContext.request.contextPath}/goods/vgoods/viewSku.do?skuId=${coreOperateInfoGenerateVo.skuId}&spuId=${coreOperateInfoGenerateVo.upSpuId}&&pageType=0">基本信息</a>
            <a class="tab-item current" href="/vgoods/operate/viewOperate.do?skuId=${coreOperateInfoGenerateVo.skuId}">运营信息</a>
            <a class="tab-item"
               href="/goods/vgoods/viewSku.do?skuId=${coreOperateInfoGenerateVo.skuId}&spuId=${coreOperateInfoGenerateVo.spuId}&&pageType=1&type=1">商品信息整合</a>
        </c:if>

    </div>
    <div class="detail-option-wrap">
      <div class="option-btns">
        <c:if test="${synchronizationStatus eq 2}">
            <img src="${pageContext.request.contextPath}/static/images/wanring.jpg" width="22px" height="22px" title="需重推"/>
            &nbsp;&nbsp;
        </c:if>

          <c:if test="${not empty coreOperateInfoGenerateVo.skuId}">
              <a class="btn btn-small J-set2">推送区域商城</a>
          </c:if>

        <c:if test="${not empty coreOperateInfoGenerateVo.skuId}">
          <a class="btn btn-small J-set">推送各平台</a>
        </c:if>

<%--        <c:choose>--%>
<%--          <c:when test="${not empty coreOperateInfoGenerateVo.operateInfoId}">--%>
<%--            <a href="./openOperate.do?operateInfoId=${coreOperateInfoGenerateVo.operateInfoId}" class="btn btn-blue btn-small">编辑</a>--%>
<%--          </c:when>--%>
<%--          <c:otherwise>--%>
<%--            <c:if test="${coreOperateInfoGenerateVo.operateInfoType == 1}">--%>
<%--              <a href="./openOperate.do?spuId=${coreOperateInfoGenerateVo.spuId}" class="btn btn-blue btn-small">编辑</a>--%>
<%--            </c:if>--%>
<%--            <c:if test="${coreOperateInfoGenerateVo.operateInfoType == 2}">--%>
<%--              <a href="./openOperate.do?skuId=${coreOperateInfoGenerateVo.skuId}" class="btn btn-blue btn-small">编辑</a>--%>
<%--            </c:if>--%>
<%--          </c:otherwise>--%>
<%--        </c:choose>--%>
        <%-- <a class="btn btn-small J-del" data-id="">删除</a>--%>
      </div>
    </div>
    <div class="form-container base-form form-span-7">
        <div class="form-block">
            <div class="form-block-title">推送平台信息</div>
            <div class="form-item">
                <div class="form-label">已推送平台：</div>
                <div class="form-fields">
                    <div class="form-fields-txt">
                        ${pushStatusStr}
                    </div>
                </div>
            </div>
            <div class="form-item">
                <div class="form-label">上下架状态：</div>
                <div class="form-fields">
                    <div class="form-fields-txt  ">${onSaleStr}</div>
                </div>
            </div>
        </div>

        <div class="form-block">
            <div class="form-block-title">
                商品主图<span class="form-block-title-tip">商品主图建议添加3张以上，有利于商品的推广用户的对商品的了解。</span>
            </div>
            <div class="form-item">
                <div class="form-label">商品图片：</div>
                <div class="form-fields">
                    <div class="prod-pic-list">
                        <input type="hidden" class="J-upload-data" value='${goodsImgJson}'>
                        <c:forEach items="${coreOperateInfoGenerateVo.goodsAttachmentList}" var="goodsImage">
                            <div class="prod-pic-item J-prod-pic-item"
                                 data-large="http://${goodsImage.domain}${goodsImage.uri}">
                                <img src="http://${goodsImage.domain}${goodsImage.uri}" alt="">
                                <div class="prod-pic-large-mask">
                                    <i class="vd-icon icon-search"></i>
                                </div>
                            </div>
                        </c:forEach>


                        <div class="prod-pic-edit-btn">
                            <a href="javascript:void(0);" class="btn btn-blue J-edit-pic">编辑主图</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="form-block">
            <div class="form-block-title">运营图文信息<span class="form-block-title-tip">编辑完成之后才可以选中。</span></div>
            <div class="form-item">
                <div class="form-label">图文详情：</div>
                <div class="form-fields">
                    <div class="select-wrap">
                        <div class="input-radio">
                            <div class="select-item">
                                <label class="input-wrap">
                                    <input <c:if test="${coreOperateInfoGenerateVo.oprateInfoHtml == null}"> disabled
                                                                                                             </c:if>type="radio"
                                                                                                             <c:if test="${coreOperateInfoGenerateVo.operateInfoSource==1&&coreOperateInfoGenerateVo.oprateInfoHtml != null}">checked="checked" </c:if>
                                                                                                             name="detailType"
                                                                                                             value="1">
                                    <span class="input-ctnr"></span>富文本（<c:if
                                        test="${coreOperateInfoGenerateVo.oprateInfoHtml !=null }">已</c:if>
                                    <c:if test="${coreOperateInfoGenerateVo.oprateInfoHtml == null}">未</c:if>编辑）
                                </label>
                                <a href="javascript:void(0);" class="item-edit J-edit-rich">编辑</a>
                            </div>
                            <div class="select-item">
                                <label class="input-wrap">
                                    <input type="radio"
                                           <c:if test="${coreOperateInfoGenerateVo.operateInfoSource==2 && coreOperateInfoGenerateVo.mlDegreeOfCompletion!='0%'}">checked="checked" </c:if>

                                    <c:if test="${coreOperateInfoGenerateVo.mlDegreeOfCompletion=='0%'}"> disabled

                                    </c:if>
                                           name="detailType" value="2">
                                    <span class="input-ctnr"></span>马良（${coreOperateInfoGenerateVo.mlDegreeOfCompletion}）
                                </label>
                                <a href="${mlUrl}" target="_blank" class="item-edit J-edit-maliang">编辑</a>
                            </div>
                        </div>
                    </div>
                    <!--运营详情-->
                    <div class="prev-wrap J-rich-html-wrap" style="    overflow: auto;display: none;">
                        ${coreOperateInfoGenerateVo.oprateInfoHtml}
                    </div>
                    <!--马良详情-->
                    <div class="prev-wrap ml J-ml-html-wrap" style=" overflow: auto;display: none;">
                        ${coreOperateInfoGenerateVo.mlSkuGraphicDetail}
                    </div>
                </div>
            </div>
        </div>

        <form id="seofrom" class="J-form" method="POST">
            <input type="hidden" name="skuId" value="${coreOperateInfoGenerateVo.skuId}">

            <div class="form-block ">

                <div class="form-block-title">SEO信息
                    <span class="form-block-title-button"><button class="btn btn-blue" type="submit" onclick="validform();">保存</button></span>
                </div>
                <div class="form-item">
                        <div class="form-label"><span class="must">*</span>SEO关键词：</div>
                        <div class="form-fields">
                            <div class="attr-wrap J-seo-wrap" style="display: grid">
                                <c:if test="${empty coreOperateInfoGenerateVo.seoKeyWordsArray}">
                                    <div class="form-item-seo-item J-item-wrap" style="margin-bottom: 10px">
                                        <div class="attr-item col-4">
                                            <input type="text" name="seoKeyWordsArray" autocomplete="off" valid-max="20" class="input-text">
                                            <div class="feedback-block"></div>
                                        </div>
                                        <div class="col-1">
                                            <i class="vd-icon icon-recycle J-seo-del"></i>
                                        </div>
                                    </div>
                                </c:if>

                                <c:if test="${not empty coreOperateInfoGenerateVo.seoKeyWordsArray}">
                                    <c:forEach var="seoKeyWords" items="${coreOperateInfoGenerateVo.seoKeyWordsArray}">
                                        <div class="form-item-seo-item J-item-wrap" style="margin-bottom: 10px">
                                            <div class="attr-item col-4">
                                                <input type="text" name="seoKeyWordsArray" value="${seoKeyWords}" autocomplete="off" valid-max="20" class="input-text">
                                                <div class="feedback-block"></div>
                                            </div>
                                            <div class="col-1">
                                                <i class="vd-icon icon-recycle J-seo-del"></i>
                                            </div>
                                        </div>
                                    </c:forEach>
                                </c:if>
                            </div>
                    </div>
            </div>

                <div class="form-item" style="margin-left: 15%">
                    <a href="javascript:void(0);" class="attr-add-btn J-seo-add">
                        <i class="vd-icon icon-add"></i>新增关键词
                    </a>
                </div>
                <div class="form-item">

                    <div class="layui-form">
                    <!-- 权限配置部分-->
                    <c:if test="${not empty vHostWordDTOS}">
                    <div class="layui-form-item layui-row layui-col-md-offset2 layui-col-md-offset2">
                        <div class="layui-col-xs12">
                            <div style="line-height: 18px; float: left;padding-top: 10px;margin-bottom: 6px;padding-right: 10px;margin-left: -108px;">热词关键词：</div>
                            <div>
                                <input type="checkbox"  name="check_all" title="全选/反选" lay-skin="primary" <c:if test="${vHostWordAllCheck}">checked</c:if> lay-filter="check_all"/>
                            </div>
                            <div>
                                <div class="layui-row">
                                    <c:forEach var="vHostWordDTO" items="${vHostWordDTOS}">
                                        <div class="layui-inline ">
                                            <input type="checkbox" class="authority" name="hotWords" value="${vHostWordDTO.opHostWordId}:${vHostWordDTO.wordName}" title="${vHostWordDTO.wordName}" lay-skin="primary" lay-filter="authority" <c:if test="${vHostWordDTO.checked}">checked</c:if> />
                                        </div>
                                    </c:forEach>

                                </div>
                            </div>
                        </div>
                    </div>
                    </c:if>
                </div>
                </div>
            </div>

        </form>

        <form id="otherInfoForm" class="J-form" method="POST">
            <input type="hidden" name="skuId" value="${coreOperateInfoGenerateVo.skuId}">

            <div class="form-block ">

                <div class="form-block-title">其他信息
                    <span class="form-block-title-button"><button class="btn btn-blue" type="submit"
                                                                  onclick="saveBaseInfo();return false;">保存</button></span>
                </div>
                <div class="form-item">
                    <div class="form-label"><span class="must">*</span>是否可售：</div>
                    <div class="form-fields">
                        <div class="input-radio">
                            <label class="input-wrap">
                                <input type="radio" name="isAvailableSale" value="1"
                                <c:if test="${coreOperateInfoGenerateVo.isAvailableSale ne 2}"> checked </c:if> >
                                <span class="input-ctnr"></span>是
                            </label>
                            <label class="input-wrap">
                                <input type="radio" name="isAvailableSale" value="2"  <c:if
                                        test="${coreOperateInfoGenerateVo.isAvailableSale eq 2}"> checked </c:if>>
                                <span class="input-ctnr"></span>否
                            </label>
                        </div>
                        <div class="feedback-block" wrapfor="isAvailableSale"></div>
                    </div>
                </div>


                <div class="form-item">
                    <div class="form-label">归属：</div>
                    <div class="form-fields">
                        <div class="input-checkbox">
                            <c:forEach var="org" items="${allOrg}">
                                <label class="input-wrap">
                                    <input type="checkbox"
                                    <c:forEach var="orgId" items="${orgIdArray}">
                                    <c:if test="${orgId eq org.orgId}"> checked </c:if>
                                    </c:forEach>
                                           name="orgIdArray" value="${org.orgId}">
                                    <span class="input-ctnr"></span>${org.orgName}
                                </label>
                            </c:forEach>
                        </div>
                    </div>
                </div>


            </div>
        </form>



    </div>
</div>
<input type="hidden" name="operateInfoType" id="operateInfoType" value="2">
<input type="hidden" name="operateInfoId" id="operateInfoId" value="${coreOperateInfoGenerateVo.operateInfoId}">
<input type="hidden" name="skuId" id="skuId" value="${coreOperateInfoGenerateVo.skuId}">
<div class="prod-pic-large-wrap J-prod-pic-large-wrap" style="display: none;">
    <img src="" alt="">
</div>
<script type="text/template" class="J-rich-tmpl">
    ${coreOperateInfoGenerateVo.oprateInfoHtml}
</script>
<div class="dlg-rich-wrap J-dlg-rich-wrap">
    <div class="dlg-dialog base-form">
        <div class="dlg-titlebar">
            <div class="dlg-title">编辑图文详情</div>
            <span class="dlg-close J-hide-rich-dlg"></span>
        </div>
        <div class="rich-dlg-wrap">
            <form>
                <script id="content" name="oprateInfoHtml" type="text/plain"
                        style="width: 900px; height: 500px;"></script>
            </form>
        </div>
        <div class="dlg-footer">
            <div class="feedback-block J-rich-error">
                <label for="" class="error" style="display: none;"></label>
            </div>
            <div class="dlg-buttons" style="">
                <button class="btn btn-blue J-rich-submit" type="button">提交</button>
                <button class="btn J-hide-rich-dlg" type="button">取消</button>
            </div>
        </div>
    </div>
</div>

<script type="text/tmpl" class="J-dlg-tmpl">
    <div class="dlg-form-wrap">
      <form class="base-form form-span-5 J-dlg-form">
        <input type="hidden" name="spuId" value="${coreOperateInfoGenerateVo.spuId}"/>
        <input type="hidden" name="skuId" value="${coreOperateInfoGenerateVo.skuId}"/>
        <div class="form-item">
          <div class="form-label">选择推送的平台：</div>
          <div class="form-fields">
            <div class="input-checkbox">
              <c:forEach items="${platfromList}" var="platfrom">
                <label class="input-wrap">
                <input type="checkbox" <c:if test="${platfrom.platfromId != 32}">checked </c:if> name="platfromIds" value="${platfrom.platfromId}" >
                <span class="input-ctnr"></span>${platfrom.platfromName}
                </label>
             </c:forEach>
              <div class="feedback-block" wrapfor="platfromIds"></div>
            </div>
          </div>
        </div>
      </form>
    </div>
</script>


<script type="text/tmpl" class="J-seo-tmpl">
         <div class="attr-item-wrap J-item-wrap cf" style="margin-bottom: 10px">
            <div class="attr-item col-4">
                <input type="text" class="input-text J-attr-value" autocomplete="off" name="seoKeyWordsArray" valid-max="20">
                <div class="feedback-block"></div>
            </div>
            <div class="col-1">
                <i class="vd-icon icon-recycle J-seo-del"></i>
            </div>
        </div>
</script>


<script type="text/tmpl" class="J-dlg-tmpl2">
        <div class="dlg-form-wrap">
          <form class="base-form form-span-5 J-dlg-form">
            <input type="hidden" name="spuId" value="${coreOperateInfoGenerateVo.spuId}"/>
            <input type="hidden" name="skuId" value="${coreOperateInfoGenerateVo.skuId}"/>
            <input type="hidden" name="platfromIds" value="16"/>
            <div class="form-item">
              <div class="form-label">选择推送的平台：</div>
              <div class="form-fields">
                <div class="input-checkbox">
                  <c:forEach items="${orgDTOList}" var="orgDTO">
                        <label class="input-wrap">
                        <input type="checkbox" checked name="pushOrgIdList" value="${orgDTO.orgId}" >
                        <span class="input-ctnr"></span>${orgDTO.orgName}
                        </label>
                    </c:forEach>
                  <div class="feedback-block" wrapfor="pushOrgIdList"></div>
                </div>
              </div>
            </div>
          </form>
        </div>
</script>

<script type="text/tmpl" class="J-dlg-pic-tmpl">
    <div class="pic-form base-form">
      <div class="form-item">
        <div class="form-label">商品图片：</div>
        <div class="form-fields">
            <div class="J-upload"></div>
            <div class="form-fields-tip">
              - 请上传1-5张商品图片；<br>
              - 图片格式为JPG、JPEG；<br>
              - 图片大小不超过300k；<br>
              - 尺寸800*800像素；<br>
              - 可拖拽图片进行排序
            </div>
            <div class="feedback-block J-upload-error">
              <label for="" class="error" style="display: none;"></label>
            </div>
        </div>
      </div>
    </div>
</script>

<script src="${pageContext.request.contextPath}/static/new/js/common/jquery.js"></script>
<script src="${pageContext.request.contextPath}/static/js/jquery.min.js"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/global.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/artDialog/2.0.0/artDialog.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/template.js?rnd=${resourceVersionKey}"></script>

  <script type="text/javascript" src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<!-- 以下ueditor编辑器需要引用的文件 -->
<script src="${pageContext.request.contextPath}/static/libs/textmodify/ueditor.simple.config.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/libs/textmodify/ueditor.all.min.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/libs/textmodify/lang/zh-cn/zh-cn.js?rnd=${resourceVersionKey}"></script>
<!-- 以上ueditor编辑器需要引用的文件 -->
<!-- 以下为异步上传附件要引用的文件 -->
<script src="${pageContext.request.contextPath}/static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<!-- 以上为异步上传附件要引用的文件 -->
<script src="${pageContext.request.contextPath}/static/js/file/edit_uedit.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/upload.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/common/sortable.min.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
        src="${pageContext.request.contextPath}/static/new/js/common/jquery.validate.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/viewOperate.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/new/js/pages/goods/vgoods/useCheckBox.js?rnd=${resourceVersionKey}"></script>

<script>
    $(function () {


        if ($("#canedit").val() == 2) {

            var dia = artDialog.alert("当前商品未审核通过，无法编辑运营信息。", "信息", function () {
                    $(".dlg-confirm-cnt").append("<br>正在跳转...");
                    $(".btn-blue").attr("disabled", "disabled")
                    location.href = "/goods/vgoods/viewSku.do?&pageType=0&skuId=" + $("#skuId").val();
                    return false;
                },
                {type: "warn"});
            $(".dlg-close").hide();
        }
    })



    function validform() {
         $("#seofrom").validate({
            //表单验证规则
            rules: {
                //使用输入框的"name"属性进行绑定
                seoKeyWordsArray: {
                    //键入验证规则，并使规则生效
                    maxlength: 20
                }},
            //验证不符合条件提示错误信息
            messages: {
                seoKeyWordsArray: {
                    //错误提示信息
                    maxlength: "最多输入20个字符!"
                }
            },
             submitHandler: function () {
                 //验证通过后进行注册
                 saveSEO();
             }
        });

    }

    // 保存seo
    function saveSEO() {
        $.ajax({
            type: "POST",
            data : $("#seofrom").serialize(),
            url: "/vgoods/operate/updateSeoKeyWords.do",
            dataType:'json',
            success: function(data){
                layer.alert('SEO信息保存成功!', { icon: 1, closeBtn: 0 },
                    function (index) {
                        window.location.reload();
                        layer.close(index);
                }
                );
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }

    // 保存其他信息模块
    function saveBaseInfo() {
        $.ajax({
            type: "POST",
            data : $("#otherInfoForm").serialize(),
            url: "/vgoods/operate/updateOtherInfo.do",
            dataType:'json',
            success: function(data){
                layer.alert('保存成功!', { icon: 1, closeBtn: 0 },
                    function (index) {
                        window.location.reload();
                        layer.close(index);
                    }
                );
            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }



</script>
<%@ include file="../../common/footer.jsp"%>