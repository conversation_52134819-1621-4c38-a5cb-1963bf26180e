package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.buyorder.domain.entity.PeerListBuyorderGoods;
import com.vedeng.erp.buyorder.common.utils.AbstractDoService;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 这个类设计的不太好，后面有机会在改
 * 当然上面的话很可能不会兑现
 * <AUTHOR>
 * @date 2022/4/16 16:48
 **/
@Data
public class PeerListBuyorderGoodsDoService extends AbstractDoService<PeerListBuyorderGoods> {

    private Integer expressId;
    /**
     * 此物流下还需要维护的信息
     */
    private List<PeerListBuyorderGoods> peerListBuyorderGoods;
    /**
     * sku为key
     */
    private Map<String, List<PeerListBuyorderGoods>> list2Map;

    /**
     * 物流明细id
     */
    private List<Integer> expressDetailIds;

    /**
     * excel里所有解析以及封装后的数据
     */
    private List<PeerListBuyorderGoods> result = new ArrayList<>();

    @Override
    public void doService(List<PeerListBuyorderGoods> data) {

        List<String> collect = data.stream().map(PeerListBuyorderGoods::getSku).distinct().collect(Collectors.toList());
        List<Integer> expressDetails = data.stream().map(PeerListBuyorderGoods::getExpressDetailId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            data.forEach(a->{
                if (a.getExpressDetailId() == null) {
                    throw new ServiceException("上传的excel中物流明细id必填");
                }
            });
            Set<String> keySet = list2Map.keySet();
            if (!CollectionUtils.isEmpty(keySet)) {
                if (!keySet.containsAll(collect)) {
                    throw new ServiceException("上传的excel中有不可维护的sku信息");
                }
                if (!CollectionUtils.isEmpty(expressDetailIds)) {
                    expressDetails.forEach(c->{
                        if ( !expressDetailIds.contains(c)) {
                            throw new ServiceException("上传的excel中有不可维护的物流详情信息");
                        }
                    });
                }

                // 数据的二次装填 oldNum dnum
                data.forEach(c->{
                    List<PeerListBuyorderGoods> peerListBuyorderGoods = list2Map.get(c.getSku());
                    if (CollectionUtils.isEmpty(peerListBuyorderGoods)) {
                        throw new ServiceException("上传的excel中有不可维护的sku信息：" + c.getSku());
                    }
                    Optional<PeerListBuyorderGoods> first = peerListBuyorderGoods.stream().filter(a -> a.getExpressDetailId().equals(c.getExpressDetailId())).findFirst();
                    if (first.isPresent()) {
                        c.setSpuType(first.get().getSpuType());
                        c.setDnum(first.get().getDnum());
                        c.setOldNum(first.get().getOldNum());
                        c.setBuyorderId(first.get().getBuyorderId());
                        c.setGoodsId(first.get().getGoodsId());
                        c.setMedicalInstrumentCatalogIncluded(first.get().getMedicalInstrumentCatalogIncluded());
                        c.setIsManageVedengCode(first.get().getIsManageVedengCode());
                        c.setIsEnableValidityPeriod(first.get().getIsEnableValidityPeriod());
                        result.add(c);
                    } else {
                        throw new ServiceException("sku:" + c.getSku() + " 未查到此物流明细");
                    }
                });

            }
        }
    }
}
