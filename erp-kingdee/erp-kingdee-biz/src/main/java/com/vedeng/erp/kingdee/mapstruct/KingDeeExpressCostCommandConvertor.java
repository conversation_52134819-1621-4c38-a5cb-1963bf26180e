package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeExpressCostCommand;
import com.vedeng.erp.kingdee.dto.KingDeeExpressCostDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface KingDeeExpressCostCommandConvertor extends BaseCommandMapStruct<KingDeeExpressCostCommand, KingDeeExpressCostDto> {
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_OrgId.FNumber", source = "FQzokOrgId")
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_CRKDH", source = "FQzokCrkdh")
    @Mapping(target = "f_QZOK_KDDH", source = "FQzokKddh")
    @Mapping(target = "f_QZOK_WLBM.FNumber", source = "FQzokWlbm")
    @Mapping(target = "f_QZOK_XLH", source = "FQzokXlh")
    @Mapping(target = "f_QZOK_PCH", source = "FQzokPch")
    @Mapping(target = "f_QZOK_FHSL", source = "FQzokFhsl")
    @Mapping(target = "f_QZOK_CB", source = "FQzokCb")
    @Mapping(target = "f_QZOK_SJR", source = "FQzokSjr")
    @Mapping(target = "f_QZOK_DH", source = "FQzokDh")
    @Mapping(target = "f_QZOK_DZ", source = "FQzokDz")
    @Mapping(target = "f_QZOK_BDDJBH", source = "FQzokBddjbh")
    @Mapping(target = "f_QZOK_SFSC", source = "FQzokSfsc")
    @Mapping(target = "f_QZOK_SFJRCB", source = "FQzokSfjrcb")
    @Mapping(target = "f_QZOK_WLGS", source = "FQzokWlgs")
    @Mapping(target = "f_QZOK_SFZP", source = "FQzokSfzp")
    KingDeeExpressCostCommand toCommand(KingDeeExpressCostDto dto);
}
