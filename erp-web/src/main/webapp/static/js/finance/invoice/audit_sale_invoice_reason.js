function btnSub(invoiceApplyId,status,isAdvance){
	checkLogin();
	var comments = $("#comments").val();
	if (comments.length < 1) {
		warn2Tips("comments", "审核不通过原因不允许为空");// 文本框ID和提示用语
		return false;
	}
	if (comments.length > 128) {
		warn2Tips("comments", "审核不通过原因最大允许128位字符");// 文本框ID和提示用语
		return false;
	}
	//校验开票申请状态
	var validStatus;
	$.ajax({
		async : false,
		url:'/invoiceApply/api/checkInvoiceApplyStStatus.do?invoiceApplyId='+invoiceApplyId,
		type:'GET',
		dataType:"json",
		success: function (res){
			console.log(res.data)
			if (1 == res.data){
				validStatus = 1;
			}
		}
	})
	if (1 == validStatus){
		layer.alert("数据状态为已开票，不可驳回")
		return false;
	}
	/*var nameReg = /^[a-zA-Z0-9\u4e00-\u9fa5\.\(\)\,]{1,128}$/;
	if (!nameReg.test(advanceValidComments)) {
		warn2Tips("advanceValidComments", "审核原因不允许使用特殊字符");
		return false;
	}*/
	if(isAdvance == 1){//提前开票列表页面审核
		$.ajax({
			async : false,
			url : './saveOpenInvoiceAudit.do',
			data : {"invoiceApplyId":invoiceApplyId,"advanceValidStatus":status,"advanceValidComments":comments,"isAdvance":isAdvance},
			type : "POST",
			dataType : "json",
			success : function(data) {

				if (data.code == 0) {
					refreshNowPageList(data);
				} else {
					layer.alert(data.data[0].msg);
					// parent.layui.use(['layer', 'table'], function () {
					// 	var layer = parent.layui.layer;
					// 	var table = parent.layui.table;
					// 	layer.open({
					// 		title: '异常信息',
					// 		area: ['800px', '400px'],
					// 		content: '<table id="errorTable"></table>', // 弹出层中的内容是一个表格
					// 		success: function(layero, index){
					// 			// 在弹出层创建成功后，使用 layui 的 table 模块渲染表格
					// 			table.render({
					// 				elem: '#errorTable',
					// 				data: data.data,
					// 				cols: [[
					// 					{field: 'invoiceApplyId',width:80, title: 'ID'},
					// 					{field: 'orderNo', title: '编码'},
					// 					{field: 'msg', title: '失败原因'}
					// 				]]
					// 			});
					// 		},
					// 		btn:[],
					// 		cancel: function () {
					// 			var layer = parent.layui.layer;
					// 			layer.closeAll();
					// 		},
					//
					// 	});
					// });

				}


			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}else if(isAdvance == 0){//开票审核页面审核
		$.ajax({
			async : false,
			url : './saveOpenInvoiceAudit.do',
			data : {"invoiceApplyId":invoiceApplyId,"validStatus":status,"validComments":comments,"isAdvance":isAdvance},
			type : "POST",
			dataType : "json",
			success : function(data) {

				if (data.code == 0) {
					refreshNowPageList(data);
				} else {

					layer.alert(data.data[0].msg);

					// parent.layui.use(['layer', 'table'], function () {
					// 	var layer = parent.layui.layer;
					// 	var table = parent.layui.table;
					// 	layer.open({
					// 		title: '异常信息',
					// 		area: ['800px', '400px'],
					// 		content: '<table id="errorTable"></table>', // 弹出层中的内容是一个表格
					// 		success: function(layero, index){
					// 			// 在弹出层创建成功后，使用 layui 的 table 模块渲染表格
					// 			table.render({
					// 				elem: '#errorTable',
					// 				data: data.data,
					// 				cols: [[
					// 					{field: 'invoiceApplyId',width:80, title: 'ID'},
					// 					{field: 'orderNo', title: '编码'},
					// 					{field: 'msg', title: '失败原因'}
					// 				]]
					// 			});
					// 		},
					// 		btn:[],
					// 		cancel: function () {
					// 			var layer = parent.layui.layer;
					// 			layer.closeAll();
					// 		},
					//
					// 	});
					// });

				}
			},error:function(data){
				if(data.status ==1001){
					layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
				}
			}
		});
	}
}


//刷新父级页面(添加、删除)
function refreshParentPage(data){
	if(data.code == 0){
		if((parent.$("#searchSpan") != undefined) && (parent.$("#searchSpan").html() != undefined)){
			if(parent.$("#winPageNo").val()!=undefined && parent.$("#choosePageSize").val()!=undefined){
				var $obj = parent.$("body").children("div.content").children("div.searchfunc").children("form");
				$obj.append("<input type='hidden' name='pageNo' value='"+parent.$("#winPageNo").val()+"'/>"
					+"<input type='hidden' name='pageSize' value='"+parent.$("#choosePageSize").val()+"'/>");
			}

		}else if(($("#searchSpan") != undefined) && ($("#searchSpan").html() != undefined)){
			if($("#winPageNo").val()!=undefined && $("#choosePageSize").val()!=undefined){
				var $obj = $("body").children("div.content").children("div.searchfunc").children("form");
				$obj.append("<input type='hidden' name='pageNo' value='"+$("#winPageNo").val()+"'/>"+"<input type='hidden' name='pageSize' value='"+$("#choosePageSize").val()+"'/>");
			}

		}else{
			var parentUrl = (window.parent.location).toString();
			if(parentUrl.endsWith('main.do')){
				if(parent.$("#searchSpan")==undefined || parent.$("#searchSpan").html()==undefined){
					window.location.reload();
					return;
				}else{
					if(parent.$("#winPageNo").val()!=undefined && parent.$("#choosePageSize").val()!=undefined){
						var $obj = parent.$("body").children("div.content").children("div.searchfunc").children("form");
						$obj.append("<input type='hidden' name='pageNo' value='"+parent.$("#winPageNo").val()+"'/>"+"<input type='hidden' name='pageSize' value='"+parent.$("#choosePageSize").val()+"'/>");
					}
				}
			}else{
				window.parent.location.reload();
			}
		}
		if(parent.layer!=undefined){
			parent.layer.close(index);
		}
		window.parent.location.reload();
	}
	if(data.code == -1){
		if(data.listData != null){
			$(".service-return-error").remove();
			var error = "<ul>";
			var errors = data.listData;
			for(var i=0;i<errors.length;i++){
				error += "<li>"+(i+1)+"、"+errors[i]['defaultMessage']+"</li>";
			}
			error += "</ul>";
			$("form").before("<div class='service-return-error'>"
				+ error
				+"</div>");
		}else{
			var msg = data.message != '' ? data.message : '操作失败';
			layer.alert(msg,{
				btn:['确认','取消']
			},function () {
				parent.layer.close(index);
			});
		}
	}

}