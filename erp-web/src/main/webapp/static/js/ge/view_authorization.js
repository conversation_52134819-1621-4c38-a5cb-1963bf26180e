function startCheckAptitude(traderCustomerId,taskId) {
    layer.confirm("您是否确认申请审核该用户？", {
        btn: ['确定','取消'] //按钮
    }, function(){
        $.ajax({
            type: "POST",
            url: "./startCheckAptitude.do",
            data: {'traderCustomerId':traderCustomerId,'taskId':taskId},
            dataType:'json',
            success: function(data){
                if (data.code == 0) {
                    window.location.reload();
                } else {
                    layer.alert(data.message);
                }

            },
            error:function(data){
                if(data.status ==1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }
            }
        });
    }, function(){
    });
}
function showMore(){
    $("#showId").css("display","none");
    $("tr[name='log-hidde']").each(function (){
        $(this).css("display","")
    });
}
function doForAudit(authorizationId){
    $.ajax({
        type: "POST",
        url: "/ge/authorization/doForAuditAndClose.do",
        data: {'authorizationId':authorizationId,'status':1},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                status=data.data;
                if(status==1){
                    // layer.alert("发起审核成功",function (){
                    //     window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                    // });
                    // layer.msg("发起审核成功",{
                    //     icon: 1,
                    //     time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    // },function (){
                    //
                    // });
                    window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;


                }else if(status==0){
                    layer.alert("发起审核失败,审核中或已关闭不可申请审核",function (){
                        window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                    });
                }
            } else {
                layer.alert("发起审核失败,审核中或已关闭不可申请审核",function (){
                    window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                });
            }

        },
        error:function(data){
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

}

function doForClose(authorizationId){
    $.ajax({
        type: "POST",
        url: "/ge/authorization/doForAuditAndClose.do",
        data: {'authorizationId':authorizationId,'status':4},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                status=data.data;
                if(status==1){
                    // layer.alert("关闭成功",function (){
                    //     window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                    // });
                    layer.msg("关闭成功",{
                        icon: 1,
                        time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    },function (){
                        window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                    });


                }else if(status==0){
                    layer.alert("关闭失败,审核中或已关闭不可关闭",function (){
                        window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                    });
                }
            } else {
                layer.alert("关闭失败,审核中或已关闭不可关闭",function (){
                    window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                });
            }

        },
        error:function(data){
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

}

function makeAudit(authorizationId){
    $.ajax({
        type: "POST",
        url: "/ge/authorization/makeAudit.do",
        data: {'authorizationId':authorizationId,'status':2},
        dataType:'json',
        success: function(data){
            if (data.code == 0) {
                status=data.data;
                if(status==1){
                    layer.msg("操作成功",{
                        icon: 1,
                        time: 1000 //2秒关闭（如果不配置，默认是3秒）
                    },function (){

                    });
                    window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;

                }else if(status==0){
                    layer.alert("操作失败");
                }
            } else {
                layer.alert(data.message,function (){
                    window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                });
            }

        },
        error:function(data){
            if (data.status == 1001) {
                layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
            }
        }
    });

}

function makeAuditNo(authorizationId){

    layer.open({
        type:1,
        content: '<div class="del-wrap">\n' +
            '\t\t<div class="del-tip">\n' +
            '\t\t\t<span class="font-red">*</span>不通过原因\n' +
            '\t\t</div>\n' +
            '\t\t<form class="J-audit-form">\n' +
            '\t\t\t<div class="del-cnt base-form">\n' +
            '\t\t\t\t<textarea name="message" id="mess" cols="30" rows="10" class="input-textarea" placeholder="必填：最多200个字"></textarea>\n' +
            '\t\t\t</div>\n' +
            '\t\t</form>\n' +
            '\t</div>',
        btn: ['确定'],
        title: '信息',
        yes:function (index,layero){
            // let val = $("#mess").val();
            if ($("#mess").val()==undefined||$("#mess").val()==null||$("#mess").val().length==0) {
                layer.msg('审核不通过原因必填，烦请填写', {
                    icon: 2,
                    time: 2000 //2秒关闭（如果不配置，默认是3秒）
                }, function(){
                    //do something
                });
            }
            if($("#mess").val().length>200){
                layer.msg('审核不通过原因不可超过200字符，请重新编辑并提交', {
                    icon: 2,
                    time: 2000 //2秒关闭（如果不配置，默认是3秒）
                }, function(){
                    //do something
                });
            }
            else if($("#mess").val().length>0 && $("#mess").val().length<=200){
                $.ajax({
                    type: "POST",
                    url: "/ge/authorization/makeAudit.do",
                    data: {'authorizationId':authorizationId,'status':3,'message':$("#mess").val()},
                    dataType:'json',
                    success: function(data){
                        if (data.code == 0) {
                            status=data.data;
                            // alert(11)
                            if(status==1){
                                layer.msg("操作成功",{
                                    icon: 1,
                                    time: 1000 //2秒关闭（如果不配置，默认是3秒）
                                },function (){

                                });
                                window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;

                            }else if(status==0){
                                layer.alert("操作成功失败");
                                window.location.href='/ge/authorization/itemView.do?authorizationId='+authorizationId;
                            }
                        } else {
                            layer.alert(data.message);
                        }
                        layer.close(index);
                    },
                    error:function(data){
                        if (data.status == 1001) {
                            layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                        }
                        layer.close(index);
                    }
                });
            }

        }
    });


}
