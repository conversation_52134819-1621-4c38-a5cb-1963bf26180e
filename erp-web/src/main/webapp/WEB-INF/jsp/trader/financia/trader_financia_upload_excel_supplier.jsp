<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../../../vue/view/common/common.jsp"%>

<div id="app">
    <el-container>
        <el-main>

            <el-row>
                <el-col>
                    <p>请选择要导入的文件：</p>
                    <el-upload
                            class="upload-demo"
                            ref="upload"
                            multiple="false"
                            action="/traderFinancia/importSupplier.do"
                            show-file-list="false"
                            accept=".xlsx,.xls"
                            limit="1"
                            :before-upload="beforeUpload"
                            :on-change="onChangeToolFile"
                            :on-success="onSuccess"
                            :file-list="fileList"
                            :auto-upload="false">
                        <el-button slot="trigger" size="small" type="primary">添加文件</el-button>
                    </el-upload>
                </el-col>
            </el-row>

            <el-row style="margin-top: 20px;">
                <el-col>
                    如果您没有标准模版，请
                    <el-link href="/static/template/批量处理供应商信息（财务专用）.xlsx" :underline="false" type="primary">下载数据模板</el-link>
                </el-col>
            </el-row>

        </el-main>
        <el-footer>
            <el-row>
                <el-col>
                    <el-button size="small" @click="closeThis">取消</el-button>
                    <el-button size="small" type="primary" @click="submitUpload">导入</el-button>
                </el-col>
            </el-row>
        </el-footer>
    </el-container>
</div>

<script type="text/javascript">

    new Vue({
        el: '#app',

        data() {
            return {
                url: '',
                fileList: [],
                list: [],
            };

        },
        mounted() {

        },

        methods: {

            closeThis() {
                parent.layer.close(index);
            },

            beforeUpload(){
                console.log("导入前");
            },

            onChangeToolFile(file, fileList) {
                if (fileList.length > 0) {
                    this.fileList = [fileList[fileList.length - 1]]  // 这一步，是 展示最后一次选择的文件
                }
            },

            submitUpload() {
                if (this.fileList.length === 0) {
                    this.$message({
                        message: '请选择需要上传的文件！',
                        duration: 1000,
                        showClose: true,
                        type: 'error'
                    });
                    return;
                }
                this.$refs.upload.submit();
            },
            handleRemove(file, fileList) {
                console.log(file, fileList);
            },
            handlePreview(file) {
                console.log(file);
            },
            onSuccess(data) {
                console.log('====>', data);
                if (data.code === 0) {
                    this.$message({
                        message: '导入成功',
                        type: 'success',
                        duration: 1000,
                        showClose: true,
                        onClose: () => {
                            parent.layer.close(index);
                            window.parent.location.reload();
                        }
                    })
                } else {
                    this.$message({
                        message: data.message+",请重新添加文件",
                        type: 'error'
                    });
                    this.fileList = [];
                    this.$refs.upload.clearFiles();
                }
            },

        }

    });

</script>

<style>

</style>