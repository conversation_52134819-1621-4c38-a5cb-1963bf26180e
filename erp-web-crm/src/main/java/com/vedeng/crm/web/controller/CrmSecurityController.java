package com.vedeng.crm.web.controller;

import com.vedeng.security.web.SecurityController;
import com.vedeng.security.web.view.ErrorPageView;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/9/25
 */
//@Controller
public class CrmSecurityController  extends SecurityController {

    //@RequestMapping({"/notBoundPlatform"})
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public View notBoundPlatformErrorPage() {
        return new ErrorPageView();
    }

//    @GetMapping({"/checkpreload.html"})
    public void healthCheck(HttpServletResponse response) throws IOException {
        response.getWriter().print("success");
    }

    public String getContentType() {
        return "text/html";
    }

    public void render(Map<String, ?> map, HttpServletRequest request, HttpServletResponse response) throws Exception {
        if (!response.isCommitted()) {
            response.setCharacterEncoding(StandardCharsets.UTF_8.name());
            response.addHeader("Content-Type", this.getContentType());
            response.getWriter().println("<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <title>无法访问</title>\n    <style>\n        #banner {\n            overflow: hidden;\n            text-align:center;\n        }\n\n        h1 {\n            font-size: 40px;\n        }\n    </style>\n</head>\n<body>\n<div class=\"layui-container\" id=\"banner\">\n    <img src=\"/static/image/serverError/no-auth.svg\" style=\"height: 500px;width: 600px;\">\n</div>\n</div>\n<div class=\"layui-container\" style=\"text-align: center;margin-top: 20px;\">\n    <h1>401</h1>\n    <h3>当前账号未绑定该平台</h3>\n</div>\n</body>\n</html>");
        }
    }
}
