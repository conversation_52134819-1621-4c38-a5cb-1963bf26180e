package com.vedeng.common.core.enums;

/**
 * 系统来源枚举
 * 定义不同系统调用API时的来源标识
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20
 */
public enum SystemSourceEnum {
    
    /**
     * XXL-Job 定时任务系统
     */
    XXL_JOB("XXL-JOB", "XXL-Job定时任务系统"),
    
    /**
     * Temporal 工作流引擎
     */
    TEMPORAL("TEMPORAL", "Temporal工作流引擎"),
    
    /**
     * 系统内部调用
     */
    SYSTEM("SYSTEM", "系统内部调用"),
    
    /**
     * 手动触发
     */
    MANUAL("MANUAL", "手动触发"),
    
    /**
     * 批处理任务
     */
    BATCH("BATCH", "批处理任务"),
    
    /**
     * 消息队列触发
     */
    MQ("MQ", "消息队列触发"),
    
    /**
     * 外部系统调用
     */
    EXTERNAL("EXTERNAL", "外部系统调用");
    
    private final String code;
    private final String description;
    
    SystemSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     *
     * @param code 系统来源代码
     * @return SystemSourceEnum 枚举值，如果找不到则返回null
     */
    public static SystemSourceEnum getByCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (SystemSourceEnum sourceEnum : values()) {
            if (sourceEnum.getCode().equalsIgnoreCase(code.trim())) {
                return sourceEnum;
            }
        }
        return null;
    }
    
    /**
     * 检查代码是否有效
     *
     * @param code 系统来源代码
     * @return true表示有效，false表示无效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
    
    @Override
    public String toString() {
        return code;
    }
}
