package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.erp.quote.domain.UpdateTerminalInfoDto;
import com.vedeng.erp.quote.mapper.NewQuoteMapper;
import com.vedeng.erp.saleorder.constant.TerminalCustomerEnum;
import com.vedeng.erp.saleorder.dao.OrderTerminalMapper;
import com.vedeng.erp.saleorder.dao.SaleOrderMapper;
import com.vedeng.erp.saleorder.dao.SaleOrderTerminalCustomerMapper;
import com.vedeng.erp.saleorder.dao.SaleOrderTerminalMapper;
import com.vedeng.erp.saleorder.domain.entity.OrderTerminalEntity;
import com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalCustomerEntity;
import com.vedeng.erp.saleorder.domain.entity.SaleorderEntity;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalCustomerDto;
import com.vedeng.erp.saleorder.dto.SaleOrderTerminalDto;
import com.vedeng.erp.saleorder.enums.OrderTerminalConstant;
import com.vedeng.erp.saleorder.feign.OneDataTerminalFeignApi;
import com.vedeng.erp.saleorder.mapstruct.OrderTerminalConvertor;
import com.vedeng.erp.saleorder.mapstruct.SaleOrderTerminalConvertor;
import com.vedeng.erp.saleorder.mapstruct.SaleOrderTerminalCustomerConvertor;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.OrderTerminalService;
import com.vedeng.erp.system.service.RegionApiService;
import com.vedeng.infrastructure.tyc.domain.dto.TycResultDto;
import com.vedeng.infrastructure.tyc.service.TycSearchService;
import com.vedeng.onedataapi.api.terminal.req.TerminalReqDto;
import com.vedeng.onedataapi.api.terminal.res.TerminalDataRes;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4 15:03
 */
@Slf4j
@Service
public class OrderTerminalServiceImpl implements OrderTerminalApiService, OrderTerminalService {

    @Autowired
    private OrderTerminalMapper orderTerminalMapper;

    @Autowired
    private OrderTerminalConvertor orderTerminalConvertor;

    @Autowired
    private OneDataTerminalFeignApi oneDataTerminalFeignApi;

    @Autowired
    private SaleOrderMapper saleOrderMapper;


    @Autowired
    private TycSearchService tycSearchService;

    @Autowired
    private SaleOrderTerminalMapper saleOrderTerminalMapper;

    @Autowired
    private SaleOrderTerminalConvertor saleOrderTerminalConvertor;

    @Autowired
    private SaleOrderTerminalCustomerMapper saleOrderTerminalCustomerMapper;

    @Autowired
    private SaleOrderTerminalCustomerConvertor saleOrderTerminalCustomerConvertor;

    @Resource
    private NewQuoteMapper newQuoteMapper;

    @Override
    public OrderTerminalDto getTerminalInfoByBusinessIdAndBusinessType(Integer businessId, Integer businessType) {
        boolean isNonNull = Objects.nonNull(businessId) && Objects.nonNull(businessType);
        return isNonNull ? orderTerminalConvertor.toDto(orderTerminalMapper.getByBusinessIdAndBusinessType(businessId, businessType)) : null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(OrderTerminalDto terminalDto) {
        OrderTerminalEntity terminalEntity = orderTerminalConvertor.toEntity(terminalDto);
        // 先判断该订单是否已存在信息
        Optional<OrderTerminalDto> existTerminal = Optional.ofNullable(this.getTerminalInfoByBusinessIdAndBusinessType(terminalDto.getBusinessId(), terminalDto.getBusinessType()));
        if (!existTerminal.isPresent()) {
            log.info("保存终端信息:{}", JSONObject.toJSONString(terminalEntity));
            // 新增
            orderTerminalMapper.insertSelective(terminalEntity);
        } else {
            // 修改
            terminalEntity.setOrderTerminalId(existTerminal.get().getOrderTerminalId());
            log.info("更新终端信息:{}", JSONObject.toJSONString(terminalEntity));
            orderTerminalMapper.updateByPrimaryKeySelective(terminalEntity);
        }
    }

    @Override
    public void remove(OrderTerminalDto terminalDto) {
        orderTerminalMapper.logicalDelete(terminalDto.getBusinessId(), terminalDto.getBusinessType());
    }



    @Override
    public PageInfo getOneDataTerminalInfo(String terminalName, Integer pageSize, Integer pageNum) {
        PageInfo<OrderTerminalDto> pageResult = new PageInfo<>();
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNum);
        TerminalReqDto query = new TerminalReqDto();
        query.setPageSize(pageSize);
        query.setPageNo(pageNum);
        query.setSearchName(terminalName);
        try {
            RestfulResult<TerminalDataRes> searchDetail = oneDataTerminalFeignApi.getTerminalSearchDetail(query);
            TerminalDataRes terminalDataRes = Optional.ofNullable(searchDetail.getData()).orElse(new TerminalDataRes());
            if (CollectionUtils.isNotEmpty(terminalDataRes.getList())) {
                List<OrderTerminalDto> result = terminalDataRes.getList().stream().map(item -> OrderTerminalDto.builder()
                        .terminalName(item.getHosName())
                        .dwhTerminalId(item.getUniqueId())
                        .provinceId(item.getProvinceId())
                        .provinceName(item.getProvince())
                        .cityId(item.getCityId())
                        .cityName(item.getCity())
                        .areaId(item.getAreaId())
                        .areaName(item.getArea())
                        .address(item.getProvince() + item.getCity() + item.getArea())
                        .unifiedSocialCreditIdentifier(item.getUnifiedSocialCreditCode())
                        .hosModelName(OrderTerminalConstant.ZERO.equals(item.getHosModel()) ? OrderTerminalConstant.PUBLIC : OrderTerminalConstant.NON_PUBLIC)
                        .hosLevelName(TerminalCustomerEnum.getLabel(item.getHosLevel()))
                        .build()).collect(Collectors.toList());
                pageResult.setTotal(terminalDataRes.getTotalRecord());
                pageResult.setList(result);
                return pageResult;
            }
        } catch (Exception e) {
            log.error("查询终端库接口异常，terminalName：{},", terminalName, e);
        }
        return pageResult;
    }

    @Override
    public PageInfo getTycTerminalInfo(String terminalName, Integer pageSize, Integer pageNum) {
        PageInfo<OrderTerminalDto> pageResult = new PageInfo<>();
        PageInfo<TycResultDto> pageInfo = tycSearchService.searchByTerminalName(terminalName);
        List<TycResultDto> tycResultDtoList = Optional.ofNullable(pageInfo.getList()).orElse(new ArrayList<>());
        List<OrderTerminalDto> orderTerminalDtoList = tycResultDtoList.stream().map(item -> OrderTerminalDto.builder()
                .terminalName(item.getName())
                .companyType(item.getCompanyType())
                .address(item.getBase())
                .unifiedSocialCreditIdentifier(item.getCreditCode())
                .organizationCode(item.getOrgNumber())
                .build()).collect(Collectors.toList());
        pageResult.setTotal(pageInfo.getTotal());
        pageResult.setList(orderTerminalDtoList);
        pageResult.setPageSize(pageSize);
        pageResult.setPageNum(pageNum);
        return pageResult;
    }


    private String getValueForCheckNull(String str){
        if(str==null){
            return StringUtils.EMPTY;
        }
        return str;
    }

    private Integer getValueForCheckNull(Integer oldValue){
        if(oldValue==null){
            return 0;
        }
        return oldValue;
    }


    @Transactional
    @Override
    public int saveSaleOrderTerminalForNew(SaleOrderTerminalDto saleOrderTerminalDto) {
//        if(saleOrderTerminalDto.getBeforeOrderTerminalId() ==null){
//            //非修改场景，需要先检查此订单是否已经存在终端信息
//            OrderTerminalEntity entity = orderTerminalMapper.getByBusinessIdNameAndBusinessType(saleOrderTerminalDto.getSaleOrderId(),saleOrderTerminalDto.getTerminalName(), 0);
//            if(entity!=null){
//                throw new ServiceException("此订单已存在终端信息，无法再次添加");
//            }
//
//        }

        UpdateTerminalInfoDto updateTerminalInfoDto = new UpdateTerminalInfoDto();
//        updateTerminalInfoDto.setAreaStr(saleOrderTerminalDto.getProvinceName() +" "+saleOrderTerminalDto.getCityName() +" "+saleOrderTerminalDto.getAreaName() );
//        updateTerminalInfoDto.setAreaId(saleOrderTerminalDto.getAreaCode());
        updateTerminalInfoDto.setSaleorderId(saleOrderTerminalDto.getSaleOrderId());
        updateTerminalInfoDto.setTraderName(saleOrderTerminalDto.getTerminalName());
        updateTerminalInfoDto.setTerminalTraderNature(saleOrderTerminalDto.getTerminalTraderNature());
        //根据结果更新，订单快照信息
        log.info("updateOrderTerminalInfo {}",new Object[]{JSONObject.toJSONString(updateTerminalInfoDto)});
        newQuoteMapper.updateOrderTerminalInfo(updateTerminalInfoDto);

        if(saleOrderTerminalDto.getBeforeOrderTerminalId()  !=null) { //编辑场景
            //只有新增场景将原数据标记为删除，保留现有的数据
            OrderTerminalEntity oldOrderTerminalEntity= new OrderTerminalEntity();
            oldOrderTerminalEntity.setTerminalName(saleOrderTerminalDto.getTerminalName());
            oldOrderTerminalEntity.setOrderTerminalId(saleOrderTerminalDto.getBeforeOrderTerminalId());//将主键设置为修改的订单终端数据IDid
            oldOrderTerminalEntity.setDwhTerminalId(getValueForCheckNull(saleOrderTerminalDto.getDwhTerminalId()));
            oldOrderTerminalEntity.setUpdater(getValueForCheckNull(saleOrderTerminalDto.getUpdater()));
            oldOrderTerminalEntity.setUpdaterName(getValueForCheckNull(saleOrderTerminalDto.getUpdaterName()));
            oldOrderTerminalEntity.setModTime(new Date());
            oldOrderTerminalEntity.setOrganizationCode(getValueForCheckNull(saleOrderTerminalDto.getOrganizationCode()));
            oldOrderTerminalEntity.setUnifiedSocialCreditIdentifier(getValueForCheckNull(saleOrderTerminalDto.getUnifiedSocialCreditIdentifier()));
            oldOrderTerminalEntity.setProvinceId(getValueForCheckNull(saleOrderTerminalDto.getProvinceCode()));
            oldOrderTerminalEntity.setProvinceName(getValueForCheckNull(saleOrderTerminalDto.getProvinceName()));
            oldOrderTerminalEntity.setCityId(getValueForCheckNull(saleOrderTerminalDto.getCityCode()));
            oldOrderTerminalEntity.setCityName(getValueForCheckNull(saleOrderTerminalDto.getCityName()));
            oldOrderTerminalEntity.setAreaId(getValueForCheckNull(saleOrderTerminalDto.getAreaCode()));
            oldOrderTerminalEntity.setAreaName(getValueForCheckNull(saleOrderTerminalDto.getAreaName()));
            oldOrderTerminalEntity.setTerminalTraderNature(getValueForCheckNull(saleOrderTerminalDto.getTerminalTraderNature()));
            oldOrderTerminalEntity.setDetailAddress(getValueForCheckNull(saleOrderTerminalDto.getDetailAddress()));
            oldOrderTerminalEntity.setNatureTypeName(getValueForCheckNull(saleOrderTerminalDto.getNatureTypeName()));
            oldOrderTerminalEntity.setContractUser(getValueForCheckNull(saleOrderTerminalDto.getContractUser()));
            oldOrderTerminalEntity.setContractMobile(getValueForCheckNull(saleOrderTerminalDto.getContractMobile()));
            oldOrderTerminalEntity.setBeforeTerminalName(getValueForCheckNull(saleOrderTerminalDto.getBeforeTerminalName()));
            orderTerminalMapper.updateByPrimaryKeySelective(oldOrderTerminalEntity);
            return oldOrderTerminalEntity.getOrderTerminalId();
        }

        SaleorderEntity saleOrderEntity = saleOrderMapper.findBySaleorderId(saleOrderTerminalDto.getSaleOrderId());
        //不论数据是不是新增场景，都新增一条，原数据需要删除
        OrderTerminalEntity customerEntity= new OrderTerminalEntity();
        customerEntity.setTerminalName(saleOrderTerminalDto.getTerminalName());
        customerEntity.setDwhTerminalId(saleOrderTerminalDto.getDwhTerminalId());
        customerEntity.setIsDeleted(0);
        customerEntity.setBusinessId(saleOrderTerminalDto.getSaleOrderId());
        customerEntity.setBusinessNo(saleOrderEntity.getSaleorderNo());
        customerEntity.setBusinessType(0);
        customerEntity.setOrganizationCode(saleOrderTerminalDto.getOrganizationCode());
        customerEntity.setUnifiedSocialCreditIdentifier(saleOrderTerminalDto.getUnifiedSocialCreditIdentifier());
        customerEntity.setProvinceId(saleOrderTerminalDto.getProvinceCode());
        customerEntity.setProvinceName(saleOrderTerminalDto.getProvinceName());
        customerEntity.setCityId(saleOrderTerminalDto.getCityCode());
        customerEntity.setCityName(saleOrderTerminalDto.getCityName());
        customerEntity.setAreaId(saleOrderTerminalDto.getAreaCode());
        customerEntity.setAreaName(saleOrderTerminalDto.getAreaName());
        customerEntity.setAddTime(new Date());
        customerEntity.setCreator(saleOrderTerminalDto.getUserId());
        customerEntity.setCreatorName(saleOrderTerminalDto.getUserName());
        customerEntity.setModTime(new Date());
        customerEntity.setUpdater(saleOrderTerminalDto.getUserId());
        customerEntity.setUpdaterName(saleOrderTerminalDto.getUserName());

        //20241119同步新增terminalTraderNature、detailAddress、natureTypeName、contractUser、contractMobile、beforeTerminalName
        customerEntity.setTerminalTraderNature(saleOrderTerminalDto.getTerminalTraderNature());
        customerEntity.setDetailAddress(saleOrderTerminalDto.getDetailAddress());
        customerEntity.setNatureTypeName(saleOrderTerminalDto.getNatureTypeName());
        customerEntity.setContractUser(saleOrderTerminalDto.getContractUser());
        customerEntity.setContractMobile(saleOrderTerminalDto.getContractMobile());
        customerEntity.setBeforeTerminalName(saleOrderTerminalDto.getBeforeTerminalName());

        // 新增
        orderTerminalMapper.insertSelective(customerEntity);
        return customerEntity.getOrderTerminalId();
    }


    @Override
    public SaleOrderTerminalDto findSaleOrderTerminalById(Integer id){
        OrderTerminalEntity entity =  orderTerminalMapper.selectByPrimaryKey(id);
        if(entity != null){
            SaleOrderTerminalDto dto = new SaleOrderTerminalDto();
            dto.setId(entity.getOrderTerminalId());
            dto.setSaleOrderId(entity.getBusinessId());
            dto.setTerminalName(entity.getTerminalName());
            dto.setDwhTerminalId(entity.getDwhTerminalId());
            dto.setUnifiedSocialCreditIdentifier(entity.getUnifiedSocialCreditIdentifier());
            dto.setOrganizationCode(entity.getOrganizationCode());
            dto.setProvinceName(entity.getProvinceName());
            dto.setCityName(entity.getCityName());
            dto.setAreaName(entity.getAreaName());
            dto.setProvinceCode(entity.getProvinceId());
            dto.setCityCode(entity.getCityId());
            dto.setAreaCode(entity.getAreaId());
            dto.setTerminalTraderNature(entity.getTerminalTraderNature());
            dto.setNatureTypeName(entity.getNatureTypeName());
            dto.setDetailAddress(entity.getDetailAddress());
            dto.setContractUser(entity.getContractUser());
            dto.setContractMobile(entity.getContractMobile());
            dto.setBeforeTerminalName(entity.getBeforeTerminalName());
            return dto;
        }else{
            return null;
        }
    }

    @Transactional
    @Override
    public boolean deleteTerminalForSaleOrderId(Integer id, CurrentUser user){

        SaleOrderTerminalDto saleOrderTerminalDtoForDelete =  findSaleOrderTerminalById(id);
        if(saleOrderTerminalDtoForDelete == null){
            log.warn("deleteTerminalForSaleOrderId failure, data not exit:{}",id);
            return false;
        }

        OrderTerminalEntity oldOrderTerminalEntity= new OrderTerminalEntity();
        oldOrderTerminalEntity.setOrderTerminalId(id);//将主键设置为修改的订单终端数据IDid
        oldOrderTerminalEntity.setIsDeleted(1);
        oldOrderTerminalEntity.setUpdater(user.getId());
        oldOrderTerminalEntity.setUpdaterName(user.getUsername());
        oldOrderTerminalEntity.setModTime(new Date());
        orderTerminalMapper.updateByPrimaryKeySelective(oldOrderTerminalEntity);

        List<SaleOrderTerminalDto>  saleOrderTerminalDtoList = findSaleOrderTerminalBySaleOrderId(saleOrderTerminalDtoForDelete.getSaleOrderId());
        if(saleOrderTerminalDtoList.size() == 0){
            //如果没有终端信息，更新订单快照信息为空
            UpdateTerminalInfoDto updateTerminalInfoDto = new UpdateTerminalInfoDto();
            updateTerminalInfoDto.setAreaStr("");
            updateTerminalInfoDto.setAreaId(null);
            updateTerminalInfoDto.setSaleorderId(saleOrderTerminalDtoForDelete.getSaleOrderId());
            updateTerminalInfoDto.setTraderName("");
            updateTerminalInfoDto.setTerminalTraderNature(0);
            //根据结果更新，订单快照信息
            newQuoteMapper.updateOrderTerminalInfo(updateTerminalInfoDto);
        }else{
            SaleOrderTerminalDto lastTerminal = saleOrderTerminalDtoList.get(saleOrderTerminalDtoList.size()-1);
            UpdateTerminalInfoDto updateTerminalInfoDto = new UpdateTerminalInfoDto();
            updateTerminalInfoDto.setAreaStr(lastTerminal.getProvinceName() +" "+lastTerminal.getCityName() +" "+lastTerminal.getAreaName() );
            updateTerminalInfoDto.setAreaId(lastTerminal.getAreaCode());
            updateTerminalInfoDto.setSaleorderId(lastTerminal.getSaleOrderId());
            updateTerminalInfoDto.setTraderName(lastTerminal.getTerminalName());
            updateTerminalInfoDto.setTerminalTraderNature(lastTerminal.getTerminalTraderNature());
            //根据结果更新，订单快照信息
            newQuoteMapper.updateOrderTerminalInfo(updateTerminalInfoDto);
        }
        return true;


    }

    @Override
    public List<SaleOrderTerminalDto> findSaleOrderTerminalBySaleOrderId(Integer saleOrderId){
        List<OrderTerminalEntity> entityList =  orderTerminalMapper.findBySaleOrderId(saleOrderId,0);
        if(CollectionUtils.isEmpty(entityList)){
            return new ArrayList<>();
        }

        List<SaleOrderTerminalDto> dtoList = new ArrayList<>();
        for (OrderTerminalEntity entity : entityList) {
            if( (entity.getTerminalTraderNature() != null && entity.getTerminalTraderNature() > 0  )
            || StringUtils.isNotBlank(entity.getTerminalName())
                    ){
                //过滤掉为空的数据
                SaleOrderTerminalDto dto = new SaleOrderTerminalDto();
                dto.setId(entity.getOrderTerminalId());
                dto.setSaleOrderId(entity.getBusinessId());
                dto.setTerminalName(entity.getTerminalName());
                dto.setDwhTerminalId(entity.getDwhTerminalId());
                dto.setUnifiedSocialCreditIdentifier(entity.getUnifiedSocialCreditIdentifier());
                dto.setOrganizationCode(entity.getOrganizationCode());
                dto.setProvinceName(entity.getProvinceName());
                dto.setCityName(entity.getCityName());
                dto.setAreaName(entity.getAreaName());
                dto.setProvinceCode(entity.getProvinceId());
                dto.setCityCode(entity.getCityId());
                dto.setAreaCode(entity.getAreaId());
                dto.setTerminalTraderNature(entity.getTerminalTraderNature());
                dto.setNatureTypeName(entity.getNatureTypeName());
                dto.setDetailAddress(entity.getDetailAddress());
                dto.setContractUser(entity.getContractUser());
                dto.setContractMobile(entity.getContractMobile());
                dto.setBeforeTerminalName(entity.getBeforeTerminalName());
                dtoList.add(dto);
            }

        }
        return dtoList;

    }

    @Autowired
    private RegionApiService regionApiService;

    private String getRegionName(Integer areaId){
        if(Objects.isNull(areaId)){
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
        if(areaId == 0){
            return null;
        }
        String regionName = regionApiService.getThisRegionToParentRegion(areaId);
        if(org.apache.commons.lang.StringUtils.isBlank(regionName)){
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
        return regionName;

    }

    @Override
    public void saveSaleOrderTerminal(SaleOrderTerminalDto saleOrderTerminalDto) {
        UpdateTerminalInfoDto updateTerminalInfoDto = new UpdateTerminalInfoDto();
        Integer saleAreaCode  = saleOrderTerminalDto.getAreaCode();
        updateTerminalInfoDto.setAreaId(saleAreaCode);
        updateTerminalInfoDto.setAreaStr(getRegionName(saleAreaCode));

//        updateTerminalInfoDto.setCustomerType();
        updateTerminalInfoDto.setSaleorderId(saleOrderTerminalDto.getSaleOrderId());
        updateTerminalInfoDto.setTraderName(saleOrderTerminalDto.getTerminalName());
        updateTerminalInfoDto.setTerminalTraderNature(saleOrderTerminalDto.getTerminalTraderNature());
        //根据结果更新，订单快照信息
        newQuoteMapper.updateOrderTerminalInfo(updateTerminalInfoDto);

        log.info("updateOrderTerminalInfo {}",new Object[]{JSONObject.toJSONString(updateTerminalInfoDto)});
        SaleorderEntity saleOrderEntity = saleOrderMapper.findBySaleorderId(saleOrderTerminalDto.getSaleOrderId());


        // 先判断该订单是否已存在信息
        OrderTerminalEntity oldOrderTerminalEntity  =orderTerminalMapper.getByBusinessIdAndBusinessType(saleOrderTerminalDto.getSaleOrderId(), 0);
        if (oldOrderTerminalEntity== null) {
            OrderTerminalEntity customerEntity= new OrderTerminalEntity();
            customerEntity.setTerminalName(saleOrderTerminalDto.getTerminalName());
           // customerEntity.setOrderTerminalId(saleOrderTerminalDto.getDwhTerminalId()!=null?Integer.parseInt(saleOrderTerminalDto.getDwhTerminalId()):null);
            customerEntity.setDwhTerminalId(saleOrderTerminalDto.getDwhTerminalId());
            customerEntity.setIsDeleted(0);
            customerEntity.setBusinessId(saleOrderTerminalDto.getSaleOrderId());
            customerEntity.setBusinessNo(saleOrderEntity.getSaleorderNo());
            customerEntity.setBusinessType(0);
            customerEntity.setOrganizationCode(saleOrderTerminalDto.getOrganizationCode());
            customerEntity.setUnifiedSocialCreditIdentifier(saleOrderTerminalDto.getUnifiedSocialCreditIdentifier());
            customerEntity.setProvinceId(saleOrderTerminalDto.getProvinceCode());
            customerEntity.setProvinceName(saleOrderTerminalDto.getProvinceName());
            customerEntity.setCityId(saleOrderTerminalDto.getCityCode());
            customerEntity.setCityName(saleOrderTerminalDto.getCityName());
            customerEntity.setAreaId(saleOrderTerminalDto.getAreaCode());
            customerEntity.setAreaName(saleOrderTerminalDto.getAreaName());
            customerEntity.setAddTime(new Date());
            customerEntity.setCreator(saleOrderTerminalDto.getUserId());
            customerEntity.setCreatorName(saleOrderTerminalDto.getUserName());
            customerEntity.setModTime(new Date());
            customerEntity.setUpdater(saleOrderTerminalDto.getUserId());
            customerEntity.setUpdaterName(saleOrderTerminalDto.getUserName());
            // 新增
            orderTerminalMapper.insertSelective(customerEntity);
            return ;
        } else {
            // 修改
            oldOrderTerminalEntity.setTerminalName(saleOrderTerminalDto.getTerminalName());
            //oldOrderTerminalEntity.setOrderTerminalId(saleOrderTerminalDto.getDwhTerminalId()!=null?Integer.parseInt(saleOrderTerminalDto.getDwhTerminalId()):null);
            oldOrderTerminalEntity.setDwhTerminalId(saleOrderTerminalDto.getDwhTerminalId());
            oldOrderTerminalEntity.setIsDeleted(0);
            oldOrderTerminalEntity.setBusinessId(saleOrderTerminalDto.getSaleOrderId());
            oldOrderTerminalEntity.setBusinessNo(saleOrderEntity.getSaleorderNo());
            oldOrderTerminalEntity.setBusinessType(0);
            oldOrderTerminalEntity.setOrganizationCode(StringUtils.isEmpty(saleOrderTerminalDto.getOrganizationCode())?"":saleOrderTerminalDto.getOrganizationCode());
            oldOrderTerminalEntity.setUnifiedSocialCreditIdentifier(saleOrderTerminalDto.getUnifiedSocialCreditIdentifier());
            oldOrderTerminalEntity.setProvinceId(Objects.nonNull(saleOrderTerminalDto.getProvinceCode()) ? saleOrderTerminalDto.getProvinceCode() : 0);
            oldOrderTerminalEntity.setProvinceName(StrUtil.isNotBlank(saleOrderTerminalDto.getProvinceName()) ? saleOrderTerminalDto.getProvinceName() : "");
            oldOrderTerminalEntity.setCityId(Objects.nonNull(saleOrderTerminalDto.getCityCode()) ? saleOrderTerminalDto.getCityCode() : 0);
            oldOrderTerminalEntity.setCityName(StrUtil.isNotBlank(saleOrderTerminalDto.getCityName()) ? saleOrderTerminalDto.getCityName() : "");
            oldOrderTerminalEntity.setAreaId(Objects.nonNull(saleOrderTerminalDto.getAreaCode()) ? saleOrderTerminalDto.getAreaCode() : 0);
            oldOrderTerminalEntity.setAreaName(StrUtil.isNotBlank(saleOrderTerminalDto.getAreaName()) ? saleOrderTerminalDto.getAreaName() : "");
//            oldOrderTerminalEntity.setAddTime(new Date());
//            oldOrderTerminalEntity.setCreator(saleOrderTerminalDto.getUserId());
//            oldOrderTerminalEntity.setCreatorName(saleOrderTerminalDto.getUserName());
            oldOrderTerminalEntity.setModTime(new Date());
            oldOrderTerminalEntity.setUpdater(saleOrderTerminalDto.getUserId());
            oldOrderTerminalEntity.setUpdaterName(saleOrderTerminalDto.getUserName());

            orderTerminalMapper.updateByPrimaryKey(oldOrderTerminalEntity);
            return ;
        }

//        SaleOrderTerminalEntity orderTerminalEntity = saleOrderTerminalMapper.selectBySaleOrderId(saleOrderTerminalDto.getSaleOrderId());
//        if (Objects.isNull(orderTerminalEntity)) {
//            // 新增
//            SaleOrderTerminalEntity insert = saleOrderTerminalConvertor.toEntity(saleOrderTerminalDto);
//            saleOrderTerminalMapper.insertSelective(insert);
//        } else {
//            // 编辑
//            orderTerminalEntity.setTerminalName(saleOrderTerminalDto.getTerminalName());
//            orderTerminalEntity.setDwhTerminalId(saleOrderTerminalDto.getDwhTerminalId());
//            orderTerminalEntity.setUnifiedSocialCreditIdentifier(saleOrderTerminalDto.getUnifiedSocialCreditIdentifier());
//            orderTerminalEntity.setOrganizationCode(saleOrderTerminalDto.getOrganizationCode());
//            saleOrderTerminalMapper.updateByPrimaryKeySelective(orderTerminalEntity);
//        }
    }

    @Override
    public SaleOrderTerminalDto getBySaleOrderId(Integer saleOrderId) {
        return saleOrderTerminalConvertor.toDto(saleOrderTerminalMapper.selectBySaleOrderId(saleOrderId));
    }

    @Override
    public SaleOrderTerminalCustomerDto getSaleOrderTerminalCustomerByOrderId(Integer saleOrderId) {
        SaleOrderTerminalCustomerEntity terminalCustomerEntity = saleOrderTerminalCustomerMapper.selectBySaleOrderId(saleOrderId);
        return saleOrderTerminalCustomerConvertor.toDto(terminalCustomerEntity);
    }

    @Override
    public void saveSaleOrderTerminalCustomer(SaleOrderTerminalCustomerDto terminalCustomerDto) {
        SaleOrderTerminalCustomerEntity customerEntity = saleOrderTerminalCustomerConvertor.toEntity(terminalCustomerDto);
        SaleOrderTerminalCustomerDto existCustomerInfo = this.getSaleOrderTerminalCustomerByOrderId(terminalCustomerDto.getSaleOrderId());
        if (Objects.nonNull(existCustomerInfo)) {
            // 更新
            customerEntity.setId(existCustomerInfo.getId());
            saleOrderTerminalCustomerMapper.updateByPrimaryKeySelective(customerEntity);
        } else {
            // 插入
            saleOrderTerminalCustomerMapper.insertSelective(customerEntity);
        }
    }

    @Override
    public Integer getNatureBySaleOrderId(Integer saleOrderId) {
        SaleorderEntity saleOrder = saleOrderMapper.findBySaleorderId(saleOrderId);
        return Objects.nonNull(saleOrder) ? saleOrder.getTerminalTraderNature() : 0;
    }

    @Override
    public List<SaleOrderTerminalDto> findSaleOrderTerminalBySaleOrderIdAPI(Integer saleorderId) {
        //TODO
        return findSaleOrderTerminalBySaleOrderId(saleorderId);
    }

}
