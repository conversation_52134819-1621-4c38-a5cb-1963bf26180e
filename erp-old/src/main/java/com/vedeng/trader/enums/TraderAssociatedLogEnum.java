package com.vedeng.trader.enums;

import lombok.Getter;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum TraderAssociatedLogEnum {

    BIND(1, "关联公司"),
    UNBIND(2, "解除关联客户");

    TraderAssociatedLogEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }

    private Integer type;
    private String description;

    public static TraderAssociatedLogEnum getByType(Integer input) {
        if (input == null) {
            return null;
        }

        for (TraderAssociatedLogEnum operationType : values()) {
            if (operationType.type.equals(input)) {
                return operationType;
            }
        }

        return null;
    }
}
