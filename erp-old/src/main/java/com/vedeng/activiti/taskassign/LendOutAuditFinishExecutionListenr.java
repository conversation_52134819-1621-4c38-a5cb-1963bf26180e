package com.vedeng.activiti.taskassign;

import com.pricecenter.constant.VerifyStatusEnum;
import com.pricecenter.dto.SkuPriceChangeApplyDto;
import com.pricecenter.service.BasePriceMaintainService;
import com.vedeng.goods.dao.CoreSkuGenerateMapper;
import com.wms.service.WMSLendOutService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;

/**
 * 出库单审核完成时候的监听器
 */
public class LendOutAuditFinishExecutionListenr implements ExecutionListener {
    private static Logger logger = LoggerFactory.getLogger(LendOutAuditFinishExecutionListenr.class);
    // 运行时注入service
    public static WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();

    private WMSLendOutService lendOutService;

    private CoreSkuGenerateMapper coreSkuGenerateMapper;

    public LendOutAuditFinishExecutionListenr(){
        lendOutService = (WMSLendOutService) context.getBean(WMSLendOutService.class);
    }

    @Override
    public void notify(DelegateExecution execution) throws Exception {

        String businessKey = execution.getVariable("businessKey").toString();
        long lendOrderId =  Long.valueOf(businessKey.split("_")[1]);
        boolean pass = Boolean.valueOf(execution.getVariable("pass").toString());

        //更新审核状态
        lendOutService.updateLendOutAuditStatus(lendOrderId,pass ? VerifyStatusEnum.Approved.getValue() : VerifyStatusEnum.Reject.getValue());

        //外借单审核通过，将单据同步到WMS
        if(pass){
            lendOutService.lendOutOrderAuditPass(lendOrderId);
        }
    }
}
