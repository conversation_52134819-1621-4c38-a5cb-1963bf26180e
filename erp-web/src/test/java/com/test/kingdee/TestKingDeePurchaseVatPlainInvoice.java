package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseVatPlainInvoiceCommand;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDetailDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDetailLinkDto;
import com.vedeng.erp.kingdee.dto.PurchaseVatPlainInvoiceDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseVatPlainInvoiceMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseVatPlainInvoiceConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.RepoStatus;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 金蝶  采购费用普通发票
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeePurchaseVatPlainInvoice {

    @Autowired
    private KingDeePurchaseVatPlainInvoiceCommandConvertor vatPlainInvoiceCommandConvertor;

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceMapper vatPlainInvoiceMapper;

    @Autowired
    private KingDeePurchaseVatPlainInvoiceConvertor vatPlainInvoiceConvertor;

    @Test
    public void KingDeePurchaseVatPlainInvoice() {

        PurchaseVatPlainInvoiceDto dto = new PurchaseVatPlainInvoiceDto();

        dto.setFid("0");
        dto.setFdate("2022-11-10 00:00:00");
        dto.setFQzokBddjtid("BD43444");
        dto.setFinvoiceno("FP123");
        dto.setFinvoicedate("2022-11-10 00:00:00");
        dto.setFsupplierid("ven0006");
        dto.setFdocumentstatus("Z");
        dto.setFBillTypeID("CGPTFP01_SYS");
        dto.setFsettleorgid("101");
        dto.setFpurchaseorgid("101");
        dto.setFCancelStatus("A");
        dto.setFRedBlue("0");
        dto.setFQzokFpdm("FPDM21");
        dto.setFQzokFplx("1");

        List<PurchaseVatPlainInvoiceDetailDto> FPURCHASEICENTRY = new ArrayList<>();
        PurchaseVatPlainInvoiceDetailDto sonDto = new PurchaseVatPlainInvoiceDetailDto();
        sonDto.setFmaterialid("sku0001");
        sonDto.setFpriceqty("50.0");
        sonDto.setFauxtaxprice("15.0");
        sonDto.setFQzokBddjhid("333");
        sonDto.setFQzokYsddh("BD1");
        sonDto.setFQzokGsywdh("BD9");
        sonDto.setFQzokYwlx("ADD_Q");
        sonDto.setFsourcetype("AP_Payable");

        List<PurchaseVatPlainInvoiceDetailLinkDto> fpurchaseicentryLink = new ArrayList<>();
        PurchaseVatPlainInvoiceDetailLinkDto inSon = new PurchaseVatPlainInvoiceDetailLinkDto();
        inSon.setFLinkId("0");
        inSon.setFpurchaseicentryLinkFflowid("");
        inSon.setFpurchaseicentryLinkFflowlineid("0");
        inSon.setFpurchaseicentryLinkFruleid("IV_PayableToPurchaseIC");
        inSon.setFpurchaseicentryLinkFstableid("0");
        inSon.setFpurchaseicentryLinkFstablename("T_AP_PAYABLEENTRY");
        inSon.setFpurchaseicentryLinkFsbillid("100083");
        inSon.setFpurchaseicentryLinkFsid("100088");
        inSon.setFpurchaseicentryLinkFbasicunitqtyold("40");
        inSon.setFpurchaseicentryLinkFbasicunitqty("50");
        inSon.setFpurchaseicentryLinkFallamountforold(new BigDecimal(500));
        inSon.setFpurchaseicentryLinkFallamountfor(new BigDecimal(400));
        fpurchaseicentryLink.add(inSon);
        sonDto.setFpurchaseicentryLink(fpurchaseicentryLink);
        FPURCHASEICENTRY.add(sonDto);
        dto.setFPURCHASEICENTRY(FPURCHASEICENTRY);

        System.out.println("dto-->"+ JSON.toJSON(dto));
        //转换为金蝶入参
        KingDeePurchaseVatPlainInvoiceCommand command = vatPlainInvoiceCommandConvertor.toCommand(dto);
        System.out.println("入参commad-->"+JSON.toJSON(command));
        //调用金蝶方法
        RepoStatus save = kingDeeBaseApi.save(new SaveExtCommand<>(command, dto.getFormId()));
        ArrayList<SuccessEntity> successEntities = save.getSuccessEntitys();

        System.out.println("金蝶返回-->"+JSON.toJSON(successEntities));

        if (CollUtil.isNotEmpty(successEntities)) {
            SuccessEntity successEntity = CollUtil.getFirst(successEntities);
            dto.setFid(successEntity.getId());
            //金蝶业务数据入表
            vatPlainInvoiceMapper.insertSelective(vatPlainInvoiceConvertor.toEntity(dto));
            System.out.println("finish-->");
        }
    }

    //调用记录 - 1 11-10 15:32 成功
    //{"fID":"0","fSUPPLIERID":{"fNumber":"ven0006"},"f_QZOK_FPDM":"FPDM21","f_QZOK_BDDJTID":"BD43444","f_QZOK_FPLX":"1","fINVOICENO":"FP123","fSETTLEORGID":{"fNumber":"101"},"fDATE":"2022-11-10 00:00:00","fPURCHASEORGID":{"fNumber":"101"},"fDOCUMENTSTATUS":"Z","fINVOICEDATE":"2022-11-10 00:00:00","fRedBlue":"0","fBillTypeID":{"fNumber":"CGPTFP01_SYS"},"fCancelStatus":"A","fPURCHASEICENTRY":[{"fPRICEQTY":"50.0","fSOURCETYPE":"AP_Payable","f_QZOK_GSYWDH":"BD9","f_QZOK_YSDDH":"BD1","f_QZOK_YWLX":"ADD_Q","fPURCHASEICENTRY_Link":[],"fAUXTAXPRICE":"15.0","fMATERIALID":{"fNumber":"sku0001"},"f_QZOK_BDDJHID":"333"}]}

    //调用金蝶接口，返回值[{"Result":{"ResponseStatus":{"IsSuccess":true,"Errors":[],"SuccessEntitys":[{"Id":101230,"Number":"POINV00000027","DIndex":0}],"SuccessMessages":[],"MsgCode":0},"Id":101230,"Number":"POINV00000027","NeedReturnData":[{}]}}]
    //金蝶返回-->[{"number":"POINV00000027","dIndex":0,"id":"101230"}]
}
