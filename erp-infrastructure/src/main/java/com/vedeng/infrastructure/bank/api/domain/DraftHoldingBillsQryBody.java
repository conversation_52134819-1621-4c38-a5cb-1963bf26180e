package com.vedeng.infrastructure.bank.api.domain;

import cn.hutool.core.util.StrUtil;
import com.vedeng.infrastructure.bank.api.domain.base.BankResponse;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: Patric.Cheng
 * @CreateTime: 2024-09-09
 * @Description: 持有票据查询(B2eNbsDraftHoldingBillsQry)请求体
 * @Version: 1.0
 */
@Data
public class DraftHoldingBillsQryBody {
    /**
     * 客户基数请求流水号
     */
    private String trnId;

    /**
     * 签约账号
     */
    private String custAccount;

    /**
     * 当前页码(从1开始)，不传默认为1
     */
    private Integer pageNo;

    /**
     * 每页数据条数（默认10条，最大每页100条）
     */
    private Integer pageSize;

    /**
     * 票据类型
     * AC01：银承
     * AC02：商承
     */
    private String billType;

    /**
     * 票号
     */
    private String billNo;

    /**
     * 票据金额范围起(15,2)
     */
    private BigDecimal minBillMoney;

    /**
     * 票据金额范围止(15,2)
     */
    private BigDecimal maxBillMoney;

    /**
     * 出票日起 yyyy-MM-dd
     */
    private String beginAcptDt;

    /**
     * 出票日止 yyyy-MM-dd
     */
    private String endAcptDt;

    /**
     * 票面到期日起yyyy-MM-dd
     */
    private String beginEndDate;

    /**
     * 票面到期日止yyyy-MM-dd
     */
    private String endDate;

    /**
     * 承兑人支持模糊查询
     */
    private String acptName;

    /**
     * 交易前手支持模糊查询
     */
    private String oppName;


    /**
     * 校验必填参数
     *
     * @return true 如果必填参数有效，false 如果缺失
     * @param response
     */
    public boolean validate(BankResponse response) {
        if (StrUtil.isBlank(trnId)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"trnId不能为空");
            return false;
        }
        if (StrUtil.isBlank(custAccount)) {
            //参数校验失败，通用返回
            response.checkParamResponseHeader(response,"custAccount不能为空");
            return false;
        }
        return true;
    }
}
