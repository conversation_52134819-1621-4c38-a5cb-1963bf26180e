package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @desc 采购售后仅退票sku-invoiceNo-invoiceCode
 */
@Data
public class AfterBuyorderInvoiceGoodsDto {
    /**
     * 订货号
     */
    private String sku;
    /**
     * 发票号
     */
    private String invoiceNo;
    /**
     * 发票代码
     */
    private String invoiceCode;
    /**
     * 品牌
     */
    private String brandName;
    /**
     * 规格
     */
    private String spec;
    /**
     * 型号
     */
    private String model;
    /**
     * 物料编码
     */
    private String materialCode;
    /**
     * 采购价
     */
    private BigDecimal buyPrice;
    /**
     * 数量
     */
    private BigDecimal num;
    /**
     * 单位
     */
    private String unitName;
    /**
     * 收票数
     */
    private BigDecimal invoiceNum;
    /**
     * 红字发票的原蓝字发票号
     */
    private String originInvoiceNo;
    /**
     * 红字发票的原蓝字发票代码
     */
    private String originInvoiceCode;
    /**
     * 发票关联采购单商品明细id
     */
    private Integer detailGoodsId;
    /**
     * skuId
     */
    private Integer goodsId;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 编辑仅退票展示商品是否呗选中
     */
    private boolean chooseFlag;
}
