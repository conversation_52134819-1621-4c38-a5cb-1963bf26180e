<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<html>
<head>
    <title>商机支持</title>
</head>
<style>
    body {
        display: block;
        margin: 0;
    }
    
    .el-header {
        background-color: #FFFFFF;
        color: #333;
        text-align: left;
        line-height: 60px;
        font-weight: bold;
    }
    
    .el-footer {
        background-color: #FFFFFF;
        color: #333;
        text-align: left;
        font-size: 14px;
    }

    .el-aside {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: #FFFFFF;
        color: #333;
        text-align: center;
    }

    .el-main {
        background-color: #FFFFFF;
        color: #333;
        text-align: center;
    }

    .flex-half {
        flex: 1;
    }

    .main-content {
        display: flex;
        flex-direction: column; /* 子元素垂直排列 */
        align-items: flex-start; /* 子元素在水平方向上靠左对齐 */
        padding: 20px; /* 根据需要添加一些内边距 */
    }

    .block {
        width: auto; /* 使得日期选择器根据内容调整宽度 */
        margin-bottom: 10px;
        margin-top: 5px;
    }

    .main-content .el-table {
        width: 100%; /* 表格宽度自适应 */
    }

    .todo-block-list {
        display: flex;
        justify-content: center;
        text-align: center;
    }

    .todo-item {
        padding: 0 15%;
    }

    .el-link {
        font-size: 20px;
        display: inline-block;
        margin-bottom: 30%;
    }

    .item-label {
        white-space: nowrap;
    }

    .el-date-editor.el-input, .el-date-editor.el-input__inner {
        width: 115px;
    }
    
</style>

<body>
<div id="app" v-cloak>
    <el-container>
        <el-header style="border-bottom: 1px solid #ebebeb;">商机支持</el-header>
        
        <el-container>
            <el-aside class="flex-half" style="border-right: 1px solid #ebebeb;">
                <div class="todo-block-list">
                    <div class="todo-item">
                        <el-link type="primary"
                                 :underline="false"
                                 @click="consultationPendingSkip()">{{ this.businessSupportWorkbenchResponseDto.consultationPendingNum }}</el-link>
                        <div class="item-label" style="font-weight: bold">咨询待处理</div>
                    </div>
                    <div class="todo-item">
                        <el-link type="primary"
                                 :underline="false"
                                 @click="followUpTodaySkip()">{{ this.businessSupportWorkbenchResponseDto.followUpTodayNum }}</el-link>
                        <div class="item-label" style="font-weight: bold">今日需跟进</div>
                    </div>
                    <div class="todo-item">
                        <el-link type="primary"
                                 :underline="false"
                                 @click="businessConversionRateSkip()">{{ this.businessSupportWorkbenchResponseDto.businessConversionRate }}</el-link>
                        <div class="item-label" style="font-weight: bold">商机转化率</div>
                    </div>
                </div>
            </el-aside>
            
            <el-main class="flex-half main-content">
                
                <div class="block">
                    <el-date-picker
                            v-model="timeFrame"
                            type="month"
                            placeholder="选择月份"
                            :editable="false"
                            :clearable="false"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            @change="submit()">
                    </el-date-picker>
                </div>

                <el-table
                        :data="businessSupportWorkbenchResponseDto.statisticalDataList"
                        border>
                    <el-table-column
                            prop="title"
                            align="center"
                            label="商机等级">
                    </el-table-column>
                    <el-table-column
                            prop="sdata"
                            align="center"
                            label="S">
                    </el-table-column>
                    <el-table-column
                            prop="adata"
                            align="center"
                            label="A">
                    </el-table-column>
                    <el-table-column
                            prop="bdata"
                            align="center"
                            label="B">
                    </el-table-column>
                    <el-table-column
                            prop="cdata"
                            align="center"
                            label="C">
                    </el-table-column>
                </el-table>
            </el-main>
        </el-container>
        
        <el-footer style="border-top: 1px solid #ebebeb;">
            说明：1、今日需跟进：商机支持记录中本人填写的下次跟进时间小于等于当日的商机； 2、看板数据不包含已关闭商机；3、看版数据以关注时间统计；4、"预计成单金额"本人商机支持中填写的最新一条数据的预计成单金额；5、本人已关注的商机只有成单后方计入转化。
        </el-footer>
    </el-container>
</div>
</body>

<script src="${pageContext.request.contextPath}/static/api/trader/businessChance.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    let vueApp = new Vue({
        el: '#app',
        data() {
            return {
                timeFrame: this.getCurrentMonthFirstDay(),

                businessSupportWorkbenchResponseDto: {
                    consultationPendingNum: '0',
                    followUpTodayNum: '0',
                    businessConversionRate: '0',
                    statisticalDataList: [{
                        title: '已支持商机数',
                        sdata: '',
                        adata: '',
                        bdata: '',
                        cdata: '',
                    },{
                        title: '预计成单金额',
                        sdata: '',
                        adata: '',
                        bdata: '',
                        cdata: '',
                    }]
                }
            }
        },

        mounted() {
            loadingApp();
            this.initData()
        },


        methods: {
            initData() {
                let requestDto = {
                    "timeFrame": this.timeFrame
                }
                getBusinessSupportWorkbench(requestDto).then(res => {
                        if (res.data.code !== 0) {
                            this.$message.error("数据异常：" + res.data.message);
                            return;
                        }
                        this.businessSupportWorkbenchResponseDto = res.data.data;
                        console.log("businessSupportWorkbenchResponseDto", this.businessSupportWorkbenchResponseDto);
                    }).catch(err => {
                        this.$message.error("系统异常：" + err.message);
                    });
            },


            submit(){
                console.log("timeFrame", this.timeFrame);
                this.initData();
            },

            getCurrentMonthFirstDay() {
                const now = new Date();
                const year = now.getFullYear();
                const month = (now.getMonth() + 1).toString().padStart(2, '0'); // 月份是从0开始的，加1，并确保两位数
                // 设置为当前月份的第一天，时间为00:00:00
                return year+'-'+month+'-'+'01 00:00:00';
            },

            // 咨询待处理跳转页面
            consultationPendingSkip() {
                console.log('/businessChance/consultationPendingSkip.do?status=0');
                let title = '咨询待处理';
                let link = '/businessChance/consultationPendingSkip.do?status=0';
                var uniqueName = link.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                window.parent.parent.postMessage({
                    from:'ez',
                    name: title,
                    url:link,
                    id:"tab-"+uniqueName
                }, '*');
            },

            // 今日需跟进跳转页面
            followUpTodaySkip() {
                const now = new Date();
                const year = now.getFullYear();
                let month = now.getMonth() + 1;
                let day = now.getDate();
                month = month < 10 ? '0' + month : month;
                day = day < 10 ? '0' + day : day;
                let today = year+"-"+month+"-"+day;
                console.log('/ezadmin/list/list-bussiness?STATUS=0%2C6%2C1%2C2%2C3&NEXT_SUPPORT_DATE_END='+today);
                let title = '今日需跟进';
                let link = '/ezadmin/list/list-bussiness?STATUS=0%2C6%2C1%2C2%2C3&NEXT_SUPPORT_DATE_END='+today;
                var uniqueName = link.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                window.parent.parent.postMessage({
                    from:'ez',
                    name: title,
                    url:link,
                    id:"tab-"+uniqueName
                }, '*');
            },

            // 商机转化率跳转页面
            businessConversionRateSkip() {
                console.log('/ezadmin/list/list-bussiness?STATUS=7');
                let title = '商机转化率';
                let link = '/ezadmin/list/list-bussiness?STATUS=7';
                var uniqueName = link.replace('./', '').replace(/["&'./:=%?[\]]/gi, '-').replace(/(--)/gi, '');
                window.parent.parent.postMessage({
                    from:'ez',
                    name: title,
                    url:link,
                    id:"tab-"+uniqueName
                }, '*');
            },
            
        }
    });
</script>
</html>
