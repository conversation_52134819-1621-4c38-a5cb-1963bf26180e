function searchTerminal() {
    checkLogin();
//	$("#updateTerminalInfo").find("#errorMes").removeClass("errorbor");
    delWarnTips("errorTxtMsg");

    var searchTraderName = $("#updateTerminalInfo").find("#searchTraderName").val() == undefined ? "" : $("#updateTerminalInfo").find("#searchTraderName").val();
    /*	if(searchTraderName==""){
            warnTips("errorMes","请输入终端信息");//文本框ID和提示用语
            return false;
        }*/

    var formPlatformNo = $('#customerFromPlatformNo').val();

    $("#updateTerminalInfo").find("#terminalDiv").attr('layerParams', '{"width":"800px","height":"300px","title":"客户信息","link":"' + page_url + '/trader/customer/searchCustomerList.do?belongPlatform=' + formPlatformNo + '&searchTraderName=' + encodeURI(searchTraderName) + '&indexId=1"}');
    $("#updateTerminalInfo").find("#terminalDiv").click();
}


function addSubmit() {
    checkLogin();
    var $form = $("#addForm");
    $("form :input").parents('li').find('.warning').remove();

    if ($("select[name='traderContactId']").val() == 0) {
        warnTips("traderContactIdMsg", "联系人不允许为空");
        return false;
    }

    var traderContactIdText = $("select[name='traderContactId']").find("option:selected").text();
    var traderContactIdTextArr = traderContactIdText.split('/');
    $("input[name='traderContactName']").val(traderContactIdTextArr[0]);
    $("input[name='traderContactMobile']").val(traderContactIdTextArr[1]);

    $form.submit();
}


function triggerContactInfoChanged() {
    var contactId = $('#contact_1').find('option:selected').val();
    if (contactId == 0 || contactId=='') {
        $("#contact_position_1").html('');
        $("#allowed_goods_types_1").html('');
        return
    }

    //根据客户ID获取联系人列表&地址列表
    $.ajax({
        url: page_url + '/order/jc/getContactInfo.do',
        data: {"contactId": contactId},
        type: "get",
        dataType: "json",
        success: function (data) {
            if (data.code !== 0 && data.data !== null) {
                console.log("获取客户下联系人列表信息失败，message: " + data.message);
                return
            }
            //处理集采客户 -- 需显示集团标示信息
            if (data.data.groupLevel==1) {
                $('.group-label.parent').show();
            } else {
                $('.group-label.child').show();
            }
            //处理集采客户
            $("#contact_position_1").html(data.data.positions);
            $("#allowed_goods_types_1").html(data.data.allowedGoodsTypes);
        }
    });
}