<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.finance.mapper.InvoiceApplyCheckMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity">
    <!--@mbg.generated-->
    <!--@Table T_INVOICE_APPLY_CHECK-->
    <id column="INVOICE_APPLY_CHECK_ID" jdbcType="BIGINT" property="invoiceApplyCheckId" />
    <result column="INVOICE_APPLY_ID" jdbcType="INTEGER" property="invoiceApplyId" />
    <result column="APPLY_NO_PASS_RULE_CODE" jdbcType="VARCHAR" property="applyNoPassRuleCode" />
    <result column="OPEN_NO_PASS_RULE_CODE" jdbcType="VARCHAR" property="openNoPassRuleCode" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    INVOICE_APPLY_CHECK_ID, INVOICE_APPLY_ID, APPLY_NO_PASS_RULE_CODE, OPEN_NO_PASS_RULE_CODE, 
    IS_DELETE, ADD_TIME, MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_INVOICE_APPLY_CHECK
    where INVOICE_APPLY_CHECK_ID = #{invoiceApplyCheckId,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from T_INVOICE_APPLY_CHECK
    where INVOICE_APPLY_CHECK_ID = #{invoiceApplyCheckId,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="INVOICE_APPLY_CHECK_ID" keyProperty="invoiceApplyCheckId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_CHECK (INVOICE_APPLY_ID, APPLY_NO_PASS_RULE_CODE, 
      OPEN_NO_PASS_RULE_CODE, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, 
      UPDATER, UPDATER_NAME, UPDATE_REMARK
      )
    values (#{invoiceApplyId,jdbcType=INTEGER}, #{applyNoPassRuleCode,jdbcType=VARCHAR}, 
      #{openNoPassRuleCode,jdbcType=VARCHAR}, #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, 
      #{modTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="INVOICE_APPLY_CHECK_ID" keyProperty="invoiceApplyCheckId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="applyNoPassRuleCode != null">
        APPLY_NO_PASS_RULE_CODE,
      </if>
      <if test="openNoPassRuleCode != null">
        OPEN_NO_PASS_RULE_CODE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="applyNoPassRuleCode != null and applyNoPassRuleCode != ''">
        #{applyNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="openNoPassRuleCode != null and openNoPassRuleCode != ''">
        #{openNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_CHECK
    <set>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="applyNoPassRuleCode != null">
        APPLY_NO_PASS_RULE_CODE = #{applyNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="openNoPassRuleCode != null">
        OPEN_NO_PASS_RULE_CODE = #{openNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where INVOICE_APPLY_CHECK_ID = #{invoiceApplyCheckId,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_CHECK
    set INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      APPLY_NO_PASS_RULE_CODE = #{applyNoPassRuleCode,jdbcType=VARCHAR},
      OPEN_NO_PASS_RULE_CODE = #{openNoPassRuleCode,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where INVOICE_APPLY_CHECK_ID = #{invoiceApplyCheckId,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_CHECK
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="INVOICE_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.invoiceApplyId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="APPLY_NO_PASS_RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.applyNoPassRuleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="OPEN_NO_PASS_RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.openNoPassRuleCode,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where INVOICE_APPLY_CHECK_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceApplyCheckId,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update T_INVOICE_APPLY_CHECK
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="INVOICE_APPLY_ID = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.invoiceApplyId != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.invoiceApplyId,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="APPLY_NO_PASS_RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applyNoPassRuleCode != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.applyNoPassRuleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="OPEN_NO_PASS_RULE_CODE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.openNoPassRuleCode != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.openNoPassRuleCode,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="IS_DELETE = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDelete != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.isDelete,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="ADD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.addTime != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.addTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="MOD_TIME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modTime != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.modTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creator != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.creator,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="CREATOR_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.creatorName != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.creatorName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updater != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.updater,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATER_NAME = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updaterName != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.updaterName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="UPDATE_REMARK = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateRemark != null">
            when INVOICE_APPLY_CHECK_ID = #{item.invoiceApplyCheckId,jdbcType=BIGINT} then #{item.updateRemark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where INVOICE_APPLY_CHECK_ID in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.invoiceApplyCheckId,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="INVOICE_APPLY_CHECK_ID" keyProperty="invoiceApplyCheckId" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_CHECK
    (INVOICE_APPLY_ID, APPLY_NO_PASS_RULE_CODE, OPEN_NO_PASS_RULE_CODE, IS_DELETE, ADD_TIME, 
      MOD_TIME, CREATOR, CREATOR_NAME, UPDATER, UPDATER_NAME, UPDATE_REMARK)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.invoiceApplyId,jdbcType=INTEGER}, #{item.applyNoPassRuleCode,jdbcType=VARCHAR}, 
        #{item.openNoPassRuleCode,jdbcType=VARCHAR}, #{item.isDelete,jdbcType=BOOLEAN}, 
        #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP}, #{item.creator,jdbcType=INTEGER}, 
        #{item.creatorName,jdbcType=VARCHAR}, #{item.updater,jdbcType=INTEGER}, #{item.updaterName,jdbcType=VARCHAR}, 
        #{item.updateRemark,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertOrUpdate" keyColumn="INVOICE_APPLY_CHECK_ID" keyProperty="invoiceApplyCheckId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyCheckId != null">
        INVOICE_APPLY_CHECK_ID,
      </if>
      INVOICE_APPLY_ID,
      APPLY_NO_PASS_RULE_CODE,
      OPEN_NO_PASS_RULE_CODE,
      IS_DELETE,
      ADD_TIME,
      MOD_TIME,
      CREATOR,
      CREATOR_NAME,
      UPDATER,
      UPDATER_NAME,
      UPDATE_REMARK,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyCheckId != null">
        #{invoiceApplyCheckId,jdbcType=BIGINT},
      </if>
      #{invoiceApplyId,jdbcType=INTEGER},
      #{applyNoPassRuleCode,jdbcType=VARCHAR},
      #{openNoPassRuleCode,jdbcType=VARCHAR},
      #{isDelete,jdbcType=BOOLEAN},
      #{addTime,jdbcType=TIMESTAMP},
      #{modTime,jdbcType=TIMESTAMP},
      #{creator,jdbcType=INTEGER},
      #{creatorName,jdbcType=VARCHAR},
      #{updater,jdbcType=INTEGER},
      #{updaterName,jdbcType=VARCHAR},
      #{updateRemark,jdbcType=VARCHAR},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceApplyCheckId != null">
        INVOICE_APPLY_CHECK_ID = #{invoiceApplyCheckId,jdbcType=BIGINT},
      </if>
      INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      APPLY_NO_PASS_RULE_CODE = #{applyNoPassRuleCode,jdbcType=VARCHAR},
      OPEN_NO_PASS_RULE_CODE = #{openNoPassRuleCode,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="INVOICE_APPLY_CHECK_ID" keyProperty="invoiceApplyCheckId" parameterType="com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_INVOICE_APPLY_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyCheckId != null">
        INVOICE_APPLY_CHECK_ID,
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID,
      </if>
      <if test="applyNoPassRuleCode != null and applyNoPassRuleCode != ''">
        APPLY_NO_PASS_RULE_CODE,
      </if>
      <if test="openNoPassRuleCode != null and openNoPassRuleCode != ''">
        OPEN_NO_PASS_RULE_CODE,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME,
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="invoiceApplyCheckId != null">
        #{invoiceApplyCheckId,jdbcType=BIGINT},
      </if>
      <if test="invoiceApplyId != null">
        #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="applyNoPassRuleCode != null and applyNoPassRuleCode != ''">
        #{applyNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="openNoPassRuleCode != null and openNoPassRuleCode != ''">
        #{openNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="invoiceApplyCheckId != null">
        INVOICE_APPLY_CHECK_ID = #{invoiceApplyCheckId,jdbcType=BIGINT},
      </if>
      <if test="invoiceApplyId != null">
        INVOICE_APPLY_ID = #{invoiceApplyId,jdbcType=INTEGER},
      </if>
      <if test="applyNoPassRuleCode != null and applyNoPassRuleCode != ''">
        APPLY_NO_PASS_RULE_CODE = #{applyNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="openNoPassRuleCode != null and openNoPassRuleCode != ''">
        OPEN_NO_PASS_RULE_CODE = #{openNoPassRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null and creatorName != ''">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null and updaterName != ''">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null and updateRemark != ''">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

<!--auto generated by MybatisCodeHelper on 2024-01-15-->
  <select id="findByInvoiceApplyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_INVOICE_APPLY_CHECK
    where INVOICE_APPLY_ID=#{invoiceApplyId,jdbcType=INTEGER}
  </select>

  <select id="addApplyCheck">
    insert into T_INVOICE_APPLY_CHECK(INVOICE_APPLY_ID,APPLY_NO_PASS_RULE_CODE)
    values (#{applyId,jdbcType=INTEGER},#{collect,jdbcType=VARCHAR})
    </select>
</mapper>