package com.vedeng.erp.saleorder.service;


import com.vedeng.erp.saleorder.constant.SalesOrderTypeEnum;
import com.vedeng.erp.saleorder.service.impl.HcSaleOrderServiceImpl;
import com.vedeng.erp.saleorder.service.impl.VsSaleOrderServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Author: daniel
 * @Date: 2021/10/8 13 35
 * @Description: 销售订单服务工厂类
 */
@Service
public class SaleOrderServiceFactory {

    @Autowired
    private BaseSaleOrderService baseSaleOrderService;

    @Autowired
    private VsSaleOrderServiceImpl vsSaleOrderService;

    @Autowired
    private HcSaleOrderServiceImpl hcSaleOrderService;

    public BaseSaleOrderService createSaleOrderService(SalesOrderTypeEnum saleOrderTypeEnum){
        switch (saleOrderTypeEnum){
            case VS:
                return vsSaleOrderService;
            case HC:
                return hcSaleOrderService;
            default:
                return baseSaleOrderService;
        }
    }
}
