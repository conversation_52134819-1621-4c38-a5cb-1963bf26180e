package com.newtask.celery.cache;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: Cherny.chen
 * @Create: 2021/7/12 19:56
 */
public class HandleLockCache {

    private static final Map<String, Integer> HANDLE_RECORD_MAP = new ConcurrentHashMap<>();

    public static void lock(String uuid) {
        HANDLE_RECORD_MAP.merge(uuid, 1, Integer::sum);
    }

    public static boolean isAsyncRun(String uuid) {
        return HANDLE_RECORD_MAP.containsKey(uuid);
    }

    public synchronized static void unlock(String uuid) {
        HANDLE_RECORD_MAP.remove(uuid);
    }

}
