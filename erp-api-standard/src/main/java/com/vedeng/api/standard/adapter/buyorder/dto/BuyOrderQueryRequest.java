package com.vedeng.api.standard.adapter.buyorder.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 采购单查询请求
 * 专门用于查询操作，包含查询条件和过滤字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BuyOrderQueryRequest extends BaseBuyOrderRequest {

    private static final long serialVersionUID = 1L;

    // ========== 查询条件字段 ==========
    /**
     * 搜索供应商名称
     */
    private String searchTraderName;

    /**
     * 供应商ID（关联表）
     */
    private Integer traderSupplierId;

    /**
     * 实际供应商搜索名称
     */
    private String searchTraderNameAct = "";

    /**
     * 实际供应商ID（字符串格式）
     */
    private String traderSupplierIdAct = "";

    // ========== 过滤条件 ==========
    /**
     * 是否备货订单
     */
    private Boolean isBhOrder = false;

    /**
     * 有效返利费用
     */
    private BigDecimal validRebateCharge = BigDecimal.ZERO;

    /**
     * 期间余额
     */
    private BigDecimal periodBalance = BigDecimal.ZERO;

    // ========== 分页和排序 ==========
    /**
     * 页码（从1开始）
     */
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    private Integer pageSize = 20;

    /**
     * 排序字段
     */
    private String orderBy;

    /**
     * 排序方向（ASC/DESC）
     */
    private String orderDirection = "DESC";

    @Override
    public String getOperationType() {
        return "query";
    }

    @Override
    public void validate() {
        super.validate();
        
        if (pageNum != null && pageNum < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        
        if (pageSize != null && (pageSize < 1 || pageSize > 1000)) {
            throw new IllegalArgumentException("每页大小必须在1-1000之间");
        }
    }

    /**
     * 创建用于详情查询的请求对象
     */
    public static BuyOrderQueryRequest forDetail(Integer buyOrderId) {
        BuyOrderQueryRequest request = new BuyOrderQueryRequest();
        request.setBuyOrderId(buyOrderId);
        return request;
    }

    /**
     * 创建用于列表查询的请求对象
     */
    public static BuyOrderQueryRequest forList() {
        BuyOrderQueryRequest request = new BuyOrderQueryRequest();
        request.setPageNum(1);
        request.setPageSize(20);
        request.setOrderDirection("DESC");
        return request;
    }

    /**
     * 创建用于供应商查询的请求对象
     */
    public static BuyOrderQueryRequest forTrader(String traderName) {
        BuyOrderQueryRequest request = forList();
        request.setSearchTraderName(traderName);
        return request;
    }
}
