package com.vedeng.erp.saleorder.dao;

import com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalCustomerEntity;

/**
 * <AUTHOR>
 */
public interface SaleOrderTerminalCustomerMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SaleOrderTerminalCustomerEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SaleOrderTerminalCustomerEntity record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SaleOrderTerminalCustomerEntity selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SaleOrderTerminalCustomerEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SaleOrderTerminalCustomerEntity record);

    /***
     * 根据销售单id查询终端客户信息
     * @param saleOrderId 销售单id
     * @return SaleOrderTerminalCustomerEntity
     */
    SaleOrderTerminalCustomerEntity selectBySaleOrderId(Integer saleOrderId);
}