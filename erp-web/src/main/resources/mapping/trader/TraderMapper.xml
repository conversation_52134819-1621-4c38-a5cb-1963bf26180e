<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.trader.dao.TraderMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.trader.model.Trader" >
    <id column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="COMPANY_ID" property="companyId" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
    <result column="AREA_IDS" property="areaIds" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="THREE_IN_ONE" property="threeInOne" jdbcType="BIT" />
    <result column="MEDICAL_QUALIFICATION" property="medicalQualification" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="TRADER_CUSTOMER_ID" property="traderCustomerId" jdbcType="INTEGER" />
    <result column="TRADER_SUPPLIER_ID" property="traderSupplierId" jdbcType="INTEGER" />
    <result column="SOURCE" property="source" jdbcType="INTEGER" />
    <result column="ACCOUNT_COMPANY_ID" property="accountCompanyId" jdbcType="INTEGER" />
    <result column="TRADER_STATUS" property="traderStatus" jdbcType="BIT" />
    <result column="CUSTOMER_TYPE" property="customerType" jdbcType="INTEGER" />
    <result column="CUSTOMER_NATURE" property="customerNature" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR" />
    <result column="NAME" property="traderContactName" jdbcType="VARCHAR" />
    <result column="MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
    <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
    <result column="TRADERADDRESS" property="traderAddress" jdbcType="VARCHAR" />
    <result column="customerLevelStr" property="customerLevelStr" jdbcType="VARCHAR" />
    <result column="BELONG_PLATFORM" property="belongPlatform" jdbcType="INTEGER"  />
    <result column="LAST_VALID_TIME" property="lastValidTime" jdbcType="TIMESTAMP" />

  </resultMap>
  <sql id="Base_Column_List" >
    TRADER_ID, PARENT_ID, COMPANY_ID, IS_ENABLE, TRADER_NAME, AREA_ID, AREA_IDS, ADDRESS, THREE_IN_ONE, MEDICAL_QUALIFICATION,ADD_TIME,
    CREATOR, MOD_TIME, UPDATER, SOURCE, ACCOUNT_COMPANY_ID, TRADER_STATUS,BELONG_PLATFORM,WAREHOUSE_DETAIL_ADDRESS,TRADER_TYPE,TRADER_TYPE
  </sql>

    <insert id="insert" parameterType="com.vedeng.trader.model.Trader" useGeneratedKeys="true" keyProperty="traderId" >
        insert into T_TRADER
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="traderId != null" >
                TRADER_ID,
            </if>
            <if test="companyId != null" >
                COMPANY_ID,
            </if>
            <if test="isEnable != null" >
                IS_ENABLE,
            </if>
            <if test="traderName != null" >
                TRADER_NAME,
            </if>
            <if test="areaId != null" >
                AREA_ID,
            </if>
            <if test="areaIds != null" >
                AREA_IDS,
            </if>
            <if test="address != null" >
                ADDRESS,
            </if>
            <if test="threeInOne != null" >
                THREE_IN_ONE,
            </if>
            <if test="medicalQualification != null" >
                MEDICAL_QUALIFICATION,
            </if>
            <if test="addTime != null" >
                ADD_TIME,
            </if>
            <if test="creator != null" >
                CREATOR,
            </if>
            <if test="modTime != null" >
                MOD_TIME,
            </if>
            <if test="updater != null" >
                UPDATER,
            </if>
            <if test="source != null" >
                SOURCE,
            </if>
            <if test="accountCompanyId != null" >
                ACCOUNT_COMPANY_ID,
            </if>
            <if test="traderStatus != null" >
                TRADER_STATUS,
            </if>
            <if test="belongPlatform != null" >
                BELONG_PLATFORM,
            </if>
            <if test="warehouseAreaId != null" >
                WAREHOUSE_AREA_ID,
            </if>
            <if test="warehouseAreaIds != null" >
                WAREHOUSE_AREA_IDS,
            </if>
            <if test="warehouseDetailAddress != null" >
                WAREHOUSE_DETAIL_ADDRESS,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="traderId != null" >
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="companyId != null" >
                #{companyId,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null" >
                #{isEnable,jdbcType=BIT},
            </if>
            <if test="traderName != null" >
                #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null" >
                #{areaId,jdbcType=INTEGER},
            </if>
            <if test="areaIds != null" >
                #{areaIds,jdbcType=VARCHAR},
            </if>
            <if test="address != null" >
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="threeInOne != null" >
                #{threeInOne,jdbcType=BIT},
            </if>
            <if test="medicalQualification != null" >
                #{medicalQualification,jdbcType=BIT},
            </if>
            <if test="addTime != null" >
                #{addTime,jdbcType=BIGINT},
            </if>
            <if test="creator != null" >
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="modTime != null" >
                #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="source != null" >
                #{source,jdbcType=INTEGER},
            </if>
            <if test="accountCompanyId != null" >
                #{accountCompanyId,jdbcType=INTEGER},
            </if>
            <if test="traderStatus != null" >
                #{traderStatus, jdbcType=BIT},
            </if>
            <if test="belongPlatform != null" >
                #{belongPlatform, jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaId != null" >
                #{warehouseAreaId, jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaIds != null" >
                #{warehouseAreaIds, jdbcType=VARCHAR},
            </if>
            <if test="warehouseDetailAddress != null" >
                #{warehouseDetailAddress, jdbcType=VARCHAR},
            </if>
        </trim>

        <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="traderId">
            SELECT LAST_INSERT_ID() AS traderId
        </selectKey>
    </insert>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        select
        <include refid="Base_Column_List" />
        from T_TRADER
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <update id="updatePartBySelective" parameterType="com.vedeng.trader.model.Trader" >
        update T_TRADER
        <set >
            <if test="companyId != null" >
                COMPANY_ID = #{companyId,jdbcType=INTEGER},
            </if>
            <if test="isEnable != null" >
                IS_ENABLE = #{isEnable,jdbcType=BIT},
            </if>
            <if test="traderName != null" >
                TRADER_NAME = #{traderName,jdbcType=VARCHAR},
            </if>
            <if test="areaId != null" >
                AREA_ID = #{areaId,jdbcType=INTEGER},
            </if>
            <if test="areaIds != null" >
                AREA_IDS = #{areaIds,jdbcType=VARCHAR},
            </if>
            <if test="address != null" >
                ADDRESS = #{address,jdbcType=VARCHAR},
            </if>
            <if test="threeInOne != null" >
                THREE_IN_ONE = #{threeInOne,jdbcType=BIT},
            </if>
            <if test="medicalQualification != null" >
                MEDICAL_QUALIFICATION = #{medicalQualification,jdbcType=BIT},
            </if>
            <if test="modTime != null" >
                MOD_TIME = #{modTime,jdbcType=BIGINT},
            </if>
            <if test="updater != null" >
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="source != null" >
                SOURCE = #{source,jdbcType=INTEGER},
            </if>
            <if test="accountCompanyId != null" >
                ACCOUNT_COMPANY_ID = #{accountCompanyId,jdbcType=INTEGER},
            </if>
            <if test="traderStatus != null" >
                TRADER_STATUS = #{traderStatus, jdbcType=BIT},
            </if>
            <if test="payofStatus != null" >
                PAYOF_STATUS = #{payofStatus, jdbcType=BIT},
            </if>
            <if test="belongPlatform != null">
                BELONG_PLATFORM=#{belongPlatform,jdbcType=INTEGER},
            </if>
            <if test="traderCheckMsg != null">
                TRADER_CHECK_MSG=#{traderCheckMsg,jdbcType=VARCHAR},
            </if>
            <if test="belongPlatform != null">
                PAYOF_CHECK_MSG=#{payofCheckMsg,jdbcType=INTEGER},
            </if>
        </set>
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>

    <select id="getTraderAptitudeCheckedByTraderId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
        SELECT
         T.TRADER_ID,T.TRADER_NAME
        FROM
        T_TRADER T
        LEFT JOIN T_TRADER_CUSTOMER C ON T.TRADER_ID=C.TRADER_ID
        LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=C.TRADER_CUSTOMER_ID AND v.relate_table='T_CUSTOMER_APTITUDE'
        WHERE
        v.STATUS=1 AND
        T.TRADER_ID = #{traderId,jdbcType=INTEGER}
        LIMIT 1
    </select>

  <select id="getTraderByTraderId" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    SELECT 
    	<include refid="Base_Column_List" />
    FROM 
    	T_TRADER
    WHERE 
    	TRADER_ID = #{traderId,jdbcType=INTEGER}
    LIMIT 1
  </select>
  <select id="getTraderInfoByTraderId" resultMap="BaseResultMap" >
	select
	a.*,
	b.TRADER_CUSTOMER_ID,
	b.CUSTOMER_TYPE,
	b.CUSTOMER_NATURE,
	c.TRADER_SUPPLIER_ID,
	d.`TRADER_CONTACT_ID`,
	d.`NAME`,
	d.`TELEPHONE`,
	d.`MOBILE`,
	e.`TRADER_ADDRESS_ID`,
	e.`ADDRESS` as TRADERADDRESS,
	f.`TITLE` as customerLevelStr
	from
	T_TRADER a
	LEFT JOIN
	T_TRADER_CUSTOMER b ON a.TRADER_ID = b.TRADER_ID
	LEFT JOIN
	T_TRADER_SUPPLIER c ON
	a.TRADER_ID = c.TRADER_ID
	LEFT JOIN `T_TRADER_CONTACT` d
    ON a.`TRADER_ID` = d.`TRADER_ID`
	 LEFT JOIN `T_TRADER_ADDRESS` e
    ON a.`TRADER_ID` = e.`TRADER_ID`
      LEFT JOIN `T_SYS_OPTION_DEFINITION` f
  	ON f.`SYS_OPTION_DEFINITION_ID` = b.`CUSTOMER_LEVEL`
	where
	a.TRADER_ID = #{traderId,jdbcType=INTEGER}
	LIMIT 1
  </select>
  <select id="getTraderCompanyByTraderId"  resultMap="BaseResultMap">
  SELECT
        a.*,
        b.TRADER_CUSTOMER_ID,
        b.CUSTOMER_TYPE,
        b.CUSTOMER_NATURE,
        c.TRADER_SUPPLIER_ID,
        e.`TRADER_ADDRESS_ID`,
        e.`ADDRESS` AS TRADERADDRESS,
        f.`TITLE` AS customerLevelStr  
    FROM
        T_TRADER a  
    LEFT JOIN
        T_TRADER_CUSTOMER b 
            ON a.TRADER_ID = b.TRADER_ID  
    LEFT JOIN
        T_TRADER_SUPPLIER c 
            ON  a.TRADER_ID = c.TRADER_ID   
    LEFT JOIN
        `T_TRADER_ADDRESS` e     
            ON a.`TRADER_ID` = e.`TRADER_ID`       
    LEFT JOIN
        `T_SYS_OPTION_DEFINITION` f    
            ON f.`SYS_OPTION_DEFINITION_ID` = b.`CUSTOMER_LEVEL`  
    WHERE
        a.TRADER_ID = #{traderId}  AND e.`TRADER_ADDRESS_ID`=#{traderAddressId}
       
  </select>
  <select id="getTraderNameByTraderContactId" resultMap="BaseResultMap">
  SELECT * FROM `T_TRADER_CONTACT` A WHERE A.`TRADER_CONTACT_ID`=#{traderContactId}
  
  </select>
  <select id="getTraderBussinessAreaByTraderId" resultType="java.lang.String"  parameterType="java.lang.Integer">
	SELECT
	B.`AREA_IDS`
	FROM
	`T_TRADER_CUSTOMER` A
	LEFT JOIN `T_TRADER_CUSTOMER_BUSSINESS_AREA` B
	ON A.`TRADER_CUSTOMER_ID` = B.`TRADER_CUSTOMER_ID`
	WHERE A.`TRADER_ID` = #{traderId} limit 1
  </select>

  <select id="getAccountSaler" parameterType="java.util.List" resultType="com.vedeng.system.model.vo.AccountSalerToGo">
        SELECT
            a.TRADER_ID AS traderId,
            b.USER_ID AS userId,
            b.USERNAME  AS userName
        FROM T_R_TRADER_J_USER a
        LEFT JOIN T_USER b ON a.USER_ID = b.USER_ID
        WHERE a.TRADER_TYPE = 1
        <if test="list != null and list.size() > 0">
          AND a.TRADER_ID IN
          <foreach collection="list" index="index" open="(" close=")" separator="," item="traderId">
            #{traderId,jdbcType=INTEGER}
          </foreach>
        </if>
  </select>
  <select id="getTraderTypeById" resultType="java.lang.Integer" > 
  SELECT
        TRADER_TYPE 
    FROM
        T_TRADER_TAG  
    WHERE
        TRADER_ID = ${traderId}
        AND TRADER_TYPE!=3
  </select>


    <select id="getTraderListPage" parameterType="java.util.Map" resultMap="BaseResultMap">
        select t.TRADER_ID,t.TRADER_NAME FROM T_TRADER_CUSTOMER c
        LEFT JOIN T_TRADER t on c.TRADER_ID=t.TRADER_ID
        LEFT JOIN T_VERIFIES_INFO v ON v.RELATE_TABLE_KEY=c.TRADER_CUSTOMER_ID AND v.relate_table='T_CUSTOMER_APTITUDE'
        where v.status=1
    </select>
    <select id="getTraderWrapByTraderId" resultType="com.vedeng.trader.model.Trader">
        SELECT
            T.TRADER_ID,
            TU.USER_ID AS CREATOR,
            T.BELONG_PLATFORM
        FROM
            T_TRADER T
            JOIN T_R_TRADER_J_USER TU ON T.TRADER_ID = TU.TRADER_ID
        WHERE
            T.TRADER_ID = #{traderId} LIMIT 1
    </select>
    <select id="getAllUserId" resultType="java.lang.Integer">
        SELECT DISTINCT U.USER_ID FROM T_R_TRADER_J_USER TU JOIN
            T_USER U ON TU.USER_ID = U.USER_ID WHERE U.IS_DISABLED = 0;
    </select>


    <update id="updateBelongPlatformOfTrader">
        UPDATE T_TRADER
        <set>
        <if test="belongPlatform!=null">
            BELONG_PLATFORM=#{belongPlatform}
        </if>
        </set>
        WHERE
        <if test="traderId!=null">
            TRADER_ID=#{traderId}
        </if>
       <if test="traderId==null">
           TRADER_ID in (SELECT TRADER_ID FROM T_R_TRADER_J_USER WHERE USER_ID=#{userId})
       </if>
    </update>
    <update id="updateTraderPlatformByUserList">
        UPDATE T_TRADER
        SET BELONG_PLATFORM = #{belongPlatform}
        WHERE
            TRADER_ID IN
            (
                SELECT TRADER_ID FROM T_R_TRADER_J_USER WHERE USER_ID IN
                <foreach collection="userList" item="item" index="index" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            );
    </update>
    <update id="updateLastCommunicateTimeOfTrader">
        UPDATE T_TRADER SET LAST_COMMUNICATE_TIME = #{communicateTime,jdbcType=BIGINT}
        WHERE TRADER_ID = #{traderId,jdbcType=INTEGER} AND (LAST_COMMUNICATE_TIME IS NULL OR LAST_COMMUNICATE_TIME <![CDATA[<]]> #{communicateTime,jdbcType=BIGINT} )
    </update>

    <select id="getFlowerTraderId" resultType="java.lang.Integer">
    SELECT
        A.TRADER_ID
    FROM
        T_TRADER A
    WHERE
        A.TRADER_NAME IN
        <foreach collection="traderNameList" item="item" open="(" close=")"  separator=",">
            #{item}
        </foreach>
    </select>


    <select id="findTraderByName" resultMap="BaseResultMap">
        SELECT
          a.TRADER_ID,
          a.TRADER_STATUS
        FROM T_TRADER a
        INNER JOIN T_TRADER_SUPPLIER s ON a.TRADER_ID = s.TRADER_ID
        WHERE a.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
    </select>

    <select id="findTraderAuditStatus" resultType="java.lang.Integer">
        SELECT a.status
        FROM  T_TRADER_SUPPLIER ts
        LEFT JOIN  T_TRADER t ON t.TRADER_ID = ts.TRADER_ID
        LEFT JOIN  T_VERIFIES_INFO a ON a.RELATE_TABLE_KEY = ts.TRADER_SUPPLIER_ID AND a.RELATE_TABLE = 'T_TRADER_SUPPLIER' AND a.VERIFIES_TYPE = 619
        WHERE t.TRADER_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="getLimitPriceTraderName" resultType="com.vedeng.trader.model.vo.TraderCustomerVo">
        SELECT A.TRADER_CUSTOMER_ID, A.TRADER_ID, B.TRADER_NAME
        FROM T_TRADER_CUSTOMER A INNER JOIN T_TRADER B ON A.TRADER_ID = B.TRADER_ID
        WHERE A.IS_ENABLE = 1 AND A.IS_LIMITPRICE = 1
        <if test="traderName != null and traderName != ''">
            AND B.TRADER_NAME LIKE CONCAT('%',#{traderName,jdbcType=VARCHAR},'%' )
        </if>
        ORDER BY A.MOD_TIME DESC, CONVERT(B.TRADER_NAME USING GBK) <!-- 按汉字字母排序 -->
    </select>
    <select id="getTraderList" resultMap="BaseResultMap">
    select  TRADER_ID,IS_ENABLE,TRADER_NAME from T_TRADER where TRADER_STATUS=2 and TRADER_NAME is not null
    </select>
    <select id="getCustomerList" resultType="com.vedeng.trader.model.Trader">
SELECT
	t.TRADER_ID traderId,
	t.IS_ENABLE isEnable,
	TRADER_NAME traderName,
	1 customerType -- 客户
FROM
	T_TRADER t
	inner JOIN T_TRADER_CUSTOMER c ON c.TRADER_ID = t.TRADER_ID
WHERE
	TRADER_STATUS = 2
	AND TRADER_NAME IS NOT NULL
    </select>
    <select id="getSupplierList" resultType="com.vedeng.trader.model.Trader">
	SELECT
	t.TRADER_ID traderId,
	t.IS_ENABLE isEnable,
	TRADER_NAME traderName,
	2 customerType -- 供应商
FROM
	T_TRADER t
	INNER JOIN  T_TRADER_SUPPLIER c ON c.TRADER_ID = t.TRADER_ID
WHERE
	TRADER_STATUS = 2
	AND TRADER_NAME IS NOT NULL
    </select>
    <select id="getCustomerAndsupplierList" resultType="com.vedeng.trader.model.Trader">
        SELECT
            t.TRADER_ID,
            t.IS_ENABLE,
            TRADER_NAME,
            1 CUSTOMER_TYPE
        FROM
            T_TRADER t
            INNER   JOIN T_TRADER_CUSTOMER c ON c.TRADER_ID = t.TRADER_ID
        WHERE
            TRADER_STATUS = 2
            AND TRADER_NAME IS NOT NULL
        UNION ALL
        SELECT
            t.TRADER_ID,
            t.IS_ENABLE,
            TRADER_NAME,
            2 CUSTOMER_TYPE
        FROM
            T_TRADER t
            INNER JOIN  T_TRADER_SUPPLIER c ON c.TRADER_ID = t.TRADER_ID
        WHERE
            TRADER_STATUS = 2
            AND TRADER_NAME IS NOT NULL
    </select>
    <select id="getTraderByWebAccountMobile" resultType="com.vedeng.trader.model.Trader">
        SELECT T.* FROM T_WEB_ACCOUNT A LEFT JOIN T_TRADER T ON A.TRADER_ID = T.TRADER_ID WHERE A.MOBILE = #{mobile}
    </select>

    <select id="getCustomerAndsupplierIn" resultType="com.vedeng.trader.model.Trader">
        SELECT
            t.TRADER_ID,
            t.IS_ENABLE,
            t.TRADER_NAME,
            IF(EXISTS(SELECT * FROM T_TRADER_CUSTOMER s WHERE s.TRADER_ID = t.TRADER_ID), 1, 2) CUSTOMER_TYPE
        FROM T_TRADER t
        WHERE TRADER_ID in (${traderIdStr})
    </select>

    <select id="getTraderListOfLastCommunicateTimeIsNull" resultType="java.lang.Integer">
        SELECT TRADER_ID
        FROM T_TRADER
        WHERE TRADER_ID &gt; #{startTraderId,jdbcType=INTEGER}
        <if test="startTraderId == 0">
            AND LAST_COMMUNICATE_TIME IS NULL
        </if>
        <if test="traderId != null">
            AND TRADER_ID = #{traderId,jdbcType=INTEGER}
        </if>
        AND IS_ENABLE = 1
        limit #{limit,jdbcType=INTEGER}
    </select>


    <select id="getTraderSupplyByTraderName" resultMap="BaseResultMap">
        SELECT T.* FROM T_TRADER_SUPPLIER TS LEFT JOIN T_TRADER T
            ON TS.TRADER_ID = T.TRADER_ID WHERE T.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
    </select>

    <select id="getTraderCustomerValidTime" resultType="com.vedeng.trader.model.Trader">
        SELECT TTCC.TRADER_ID AS traderId,MAX(AHP.END_TIME_) AS lastValidTime
        FROM  T_TRADER_CUSTOMER TTCC
        INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_CUSTOMER' AND VI.RELATE_TABLE_KEY = TTCC.TRADER_CUSTOMER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 617
        LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderCustomerVerify_',TTCC.TRADER_CUSTOMER_ID)
        WHERE
        TTCC.TRADER_CUSTOMER_ID= #{traderCustomerId}
        GROUP BY TTCC.TRADER_CUSTOMER_ID,AHP.BUSINESS_KEY_
    </select>
    <select id="getTraderSupplyValidTime" resultType="com.vedeng.trader.model.Trader">
        SELECT TS.TRADER_ID AS traderId,,MAX(AHP.END_TIME_) AS lastValidTime
        FROM  T_TRADER_SUPPLIER TS
        INNER JOIN T_VERIFIES_INFO VI ON VI.RELATE_TABLE = 'T_TRADER_SUPPLIER' AND VI.RELATE_TABLE_KEY = TS.TRADER_SUPPLIER_ID AND VI.STATUS =1  AND VI.VERIFIES_TYPE = 619
        LEFT JOIN ACT_HI_PROCINST AHP ON AHP.BUSINESS_KEY_ = CONCAT('traderSupplierVerify_',TS.TRADER_SUPPLIER_ID)
        WHERE
        TS.TRADER_SUPPLIER_ID=#{traderSupplierId}
        GROUP BY TS.TRADER_SUPPLIER_ID,AHP.BUSINESS_KEY_
    </select>

    <update id="UpdateLastValidTimeByIdAndTime">
        UPDATE T_TRADER
        SET LAST_VALID_TIME = #{validTime,jdbcType=TIMESTAMP}
        WHERE TRADER_ID = #{traderId}
    </update>

    <select id="getTraderByTraderName" resultMap="BaseResultMap">
        SELECT T.* FROM T_TRADER T WHERE T.TRADER_NAME = #{traderName,jdbcType=VARCHAR}
                                     AND IS_ENABLE = 1 AND COMPANY_ID = 1
    </select>


    <update id="updateTraderWarehouseAdressById" parameterType="com.newtask.dto.TraderDto" >
        update T_TRADER
        <set>
            <if test="warehouseAreaId != null" >
                WAREHOUSE_AREA_ID = #{warehouseAreaId,jdbcType=INTEGER},
            </if>
            <if test="warehouseAreaIds != null and warehouseAreaIds != ''" >
                WAREHOUSE_AREA_IDS = #{warehouseAreaIds,jdbcType=VARCHAR},
            </if>
            <if test="warehouseDetailAddress != null and warehouseDetailAddress != ''" >
                WAREHOUSE_DETAIL_ADDRESS = #{warehouseDetailAddress,jdbcType=VARCHAR},
            </if>
        </set>
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>
    <update id="setPublicCustomerPrivatizedByAssign">
        update T_PUBLIC_CUSTOMER_RECORD
        set IS_PRIVATIZED = 3,
            PRIVATIZED_TIME = unix_timestamp(now()) * 1000
        where IS_PRIVATIZED = 0
          and TRADER_CUSTOMER_ID in (
            select tc.TRADER_CUSTOMER_ID
            from T_TRADER t
                     join T_TRADER_CUSTOMER tc on t.TRADER_ID = tc.TRADER_ID
            where t.TRADER_ID in
            <foreach collection="traderIdList" index="index" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        )
    </update>


    <update id="setPublicCustomerPrivatizedByAssignWithTraderId">
        update T_PUBLIC_CUSTOMER_RECORD
        set IS_PRIVATIZED = 3,
        PRIVATIZED_TIME = unix_timestamp(now()) * 1000
        where IS_PRIVATIZED = 0
        and TRADER_CUSTOMER_ID = #{traderCustomerId,jdbcType=INTEGER}
    </update>



    <select id="getJDTraderInfoByTraderIdAndMobile" resultType="com.vedeng.trader.model.Trader">
        select
            a.*,
            b.TRADER_CUSTOMER_ID,
            b.CUSTOMER_TYPE,
            b.CUSTOMER_NATURE,
            d.`TRADER_CONTACT_ID`,
            d.`NAME`  traderContactName,
            d.`TELEPHONE` traderContactTelephone,
            d.`MOBILE`  traderContactMobile,
            e.`TRADER_ADDRESS_ID`,
            e.`ADDRESS` as TRADERADDRESS,
            f.`TITLE` as customerLevelStr
        from
            T_TRADER a
                LEFT JOIN  T_TRADER_CUSTOMER b ON a.TRADER_ID = b.TRADER_ID
                LEFT JOIN `T_TRADER_CONTACT` d  ON a.`TRADER_ID` = d.`TRADER_ID` AND d.IS_ENABLE=1 and d.MOBILE = #{mobile,jdbcType=VARCHAR}
                LEFT JOIN `T_TRADER_ADDRESS` e ON a.`TRADER_ID` = e.`TRADER_ID` AND e.IS_ENABLE=1 and e.IS_DEFAULT = 1
                LEFT JOIN `T_SYS_OPTION_DEFINITION` f ON f.`SYS_OPTION_DEFINITION_ID` = b.`CUSTOMER_LEVEL`
        where
            a.TRADER_ID = #{traderId,jdbcType=INTEGER}
        limit 1
    </select>


    <select id="getTraderInfoByTraderIdAndMobile" resultType="com.vedeng.trader.model.Trader">
        select
            a.*,
            b.TRADER_CUSTOMER_ID,
            b.CUSTOMER_TYPE,
            b.CUSTOMER_NATURE,
            d.`TRADER_CONTACT_ID`,
            d.`NAME`  traderContactName,
            d.`TELEPHONE` traderContactTelephone,
            d.`MOBILE`  traderContactMobile,
            e.`TRADER_ADDRESS_ID`,
            e.`ADDRESS` as TRADERADDRESS,
            f.`TITLE` as customerLevelStr
        from
            T_TRADER a
                LEFT JOIN  T_TRADER_CUSTOMER b ON a.TRADER_ID = b.TRADER_ID
                LEFT JOIN `T_TRADER_CONTACT` d  ON a.`TRADER_ID` = d.`TRADER_ID` AND d.IS_ENABLE=1 and d.MOBILE = #{mobile,jdbcType=VARCHAR}
                LEFT JOIN `T_TRADER_ADDRESS` e ON a.`TRADER_ID` = e.`TRADER_ID` AND e.IS_ENABLE=1
                LEFT JOIN `T_SYS_OPTION_DEFINITION` f ON f.`SYS_OPTION_DEFINITION_ID` = b.`CUSTOMER_LEVEL`
        where
            a.TRADER_ID = #{traderId,jdbcType=INTEGER}
        limit 1
    </select>
    <select id="getChildrenTrader" resultType="com.vedeng.trader.model.Trader">
        SELECT
        <include refid="Base_Column_List" />
        FROM T_TRADER WHERE PARENT_ID = #{traderId,jdbcType=INTEGER}
    </select>

    <select id="findTradePlatFormByIds" resultType="com.pricecenter.dto.ContractTraderDto">
        SELECT
          TRADER_ID,
          BELONG_PLATFORM
        FROM T_TRADER WHERE TRADER_ID in
        <foreach collection="tradreIdList" item="traderId" open="(" close=")" separator=",">
            #{traderId,jdbcType=INTEGER}
        </foreach>

    </select>
    <select id="getTradeIdList" resultType="java.lang.Integer" >
        select
            a.TRADER_ID
        from
            T_TRADER a
        where
            IS_ENABLE = 1
            and
            a.COMPANY_ID = #{companyId,jdbcType=INTEGER}
    </select>
    <select id="getRTraderUser" resultType="com.vedeng.trader.model.RTraderJUser" parameterType="com.vedeng.trader.model.RTraderJUser" >
        select
        R_TRADER_J_USER_ID, TRADER_TYPE, USER_ID, TRADER_ID
        from
        T_R_TRADER_J_USER
        where 1

            and TRADER_ID = #{traderId,jdbcType=INTEGER}

        <if test="traderType != null and traderType > 0">
            and TRADER_TYPE = #{traderType,jdbcType=INTEGER}
        </if>
    </select>
    <select id="queryNoPublicGroupList" resultType="com.vedeng.trader.model.Trader">
        SELECT
	    TRADER_ID,
	    TRADER_NAME
        FROM
	    T_TRADER
        WHERE TRADER_TYPE= 0
        AND BELONG_PLATFORM in (2,6)
    </select>


    <!--auto generated by MybatisCodeHelper on 2022-05-31-->
    <update id="updateByTraderId">
        update T_TRADER
        <set>
            TRADER_NAME = #{traderName,jdbcType=VARCHAR},
        </set>
        where TRADER_ID = #{traderId,jdbcType=INTEGER}
    </update>

    <!--判断首单逻辑-->
    <select id="getSaleorderRealAmountByTraderId" resultType="java.lang.Integer">
        SELECT
            A.SALEORDER_ID
        FROM
            T_SALEORDER A
        WHERE
            A.`STATUS` IN ( 1, 2 )
          AND A.REAL_PAY_AMOUNT > 0
          AND A.TRADER_ID =#{traderId,jdbcType=INTEGER}
    </select>


    <select id="findByTraderIdGetSaleInfo" resultType="java.util.Map">
        SELECT TT.TRADER_ID traderId,
               TT.TRADER_NAME  traderName,
               A.USER_ID userId,
               TU.USERNAME userName
        FROM T_TRADER TT
                 LEFT JOIN T_R_TRADER_J_USER A ON TT.TRADER_ID = A.TRADER_ID
                 LEFT JOIN T_USER TU ON TU.USER_ID = A.USER_ID
        WHERE A.TRADER_ID = #{traderId,jdbcType=INTEGER}
        LIMIT 1
    </select>
    
    <select id="getTraderInfoByDeal" resultType="com.vedeng.trader.model.Trader">
        select
            a.*,
            b.TRADER_CUSTOMER_ID,
            b.CUSTOMER_TYPE,
            b.CUSTOMER_NATURE,
            d.`TRADER_CONTACT_ID`,
            d.`NAME`  traderContactName,
            d.`TELEPHONE` traderContactTelephone,
            d.`MOBILE`  traderContactMobile,
            e.`TRADER_ADDRESS_ID`,
            e.`ADDRESS` as TRADERADDRESS,
            f.`TITLE` as customerLevelStr
        from
            T_TRADER a
                LEFT JOIN  T_TRADER_CUSTOMER b ON a.TRADER_ID = b.TRADER_ID
                LEFT JOIN `T_TRADER_CONTACT` d  ON a.`TRADER_ID` = d.`TRADER_ID` AND d.IS_ENABLE=1 and d.MOBILE = #{mobile,jdbcType=VARCHAR}  and d.name=#{contactName,jdbcType=VARCHAR}
                LEFT JOIN `T_TRADER_ADDRESS` e ON a.`TRADER_ID` = e.`TRADER_ID` AND e.IS_ENABLE=1 and e.ADDRESS=#{address,jdbcType=VARCHAR}
                LEFT JOIN `T_SYS_OPTION_DEFINITION` f ON f.`SYS_OPTION_DEFINITION_ID` = b.`CUSTOMER_LEVEL`
        where
            a.TRADER_ID = #{traderId,jdbcType=INTEGER}
        limit 1
    </select>


</mapper>