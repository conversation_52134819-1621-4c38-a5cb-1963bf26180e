package com.report.dao;

import com.vedeng.order.model.dto.CustomerCountDto;
import com.vedeng.trader.model.TraderCustomer;
import com.vedeng.trader.model.dto.TraderCustomerAssociateInfoDto;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.util.List;

/**
 * @Author: daniel
 * @Date: 2022/5/17 14 12
 * @Description:
 */
@Named("publicCustomerCalculateMapper")
public interface PublicCustomerCalculateMapper {

    List<TraderCustomerAssociateInfoDto> getB2bCustomer(@Param("traderCustomerIdList") List<Integer> traderCustomerIdList);

    /**
     * 查询所有的B2B客户（排除公海豁免的客户）
     * @param traderCustomerIdList 指定客户ID
     * @return B2B客户
     */
    List<TraderCustomerAssociateInfoDto> getB2bCustomerExceptExempt(@Param("traderCustomerIdList") List<Integer> traderCustomerIdList);


    /**
     * 获取最近锁定的客户
     * @param recentDays 最近的天数
     * @return 公海客户
     */
    List<Integer> getPublicCustomerLockedRecently(@Param("recentDays") Integer recentDays);

    /**
     * 配置条件获取非新客信息
     *
     * @param customerCreatedDays 新客创建时间
     * @return 新客集合
     */
    List<TraderCustomerAssociateInfoDto> getOldTraderCustomerByCondition(@Param("customerCreatedDays") Integer customerCreatedDays);

    /**
     * 获取未被私有的公海客户记录
     * @return
     */
    List<Integer> getUnPrivateCustomerRecords();

    /**
     * 获取商机保护近多少天的客户
     * @return 客户ID集合
     */
    List<Integer> getCustomerListUnlockedByBusinessChance(@Param("unlockedDays") Integer unlockedDays);

    /**
     * 解锁保护期
     * @param lockProtectDays
     * @return
     */
    List<Integer> getLockProtectDays(@Param("lockProtectDays") Integer lockProtectDays);


    /**
     * 获取大于保护期天数的客户集合
     * @return 客户集合
     */
    List<Integer> getCustomerIdsOfGrateThenProtectedDays(@Param("protectedDays") Integer protectedDays);

    List<CustomerCountDto> getValidOrderCountOfB2bCustomer(@Param("validOrderDays") Integer validOrderDays);

    List<TraderCustomer> getCustomerListByAssociatedGroup();

    List<CustomerCountDto> getValidCommunicationCountOfB2bCustomer(@Param("communicationDays") Integer communicationDays);

}
