<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="客户信用记录" scope="application" />
<%@ include file="../../common/common.jsp"%>
<style>
    .bottom-c{
        bottom: 10px;
        right: 0px;
        left: 0px;
    }
    .bottom-bottom{
        position: fixed;
        bottom: 0px;
        right: 0px;
    }
</style>
<div>
    <div class="layui-layer-title">客户信用记录</div>
<div class="form-list  form-tips8 trader-customer-accountperiodapply">
    <div class="title-container">
        <div class="table-title nobor">账期信息</div>
    </div>
    <div class="title-container">
        <div class="table-title nobor" >客户信用：</div>
        ${finance.creditRating}
    </div>
    <table class="table table-bordered table-striped table-condensed table-centered">
        <thead>
        <tr>
            <th class="table-smallest">
                <div class="customernamec pos_rel">
                    <span >账期类型</span>
                    <i class="iconbluemouth"></i>
                    <div class="pos_abs customernameshow" style="display: none;">
                        <label>1）正式账期：开放给正式合作的客户的可支持长期使用的信用额，一个客户只有一个正式账期，只能对此账期进行额度调整、有效期调整。可适用于多个订单。<br>
                            2）临时账期：针对某个客户在短期时间内可临时使用的一笔信用额，可适用于多个订单。一个客户可以有多个临时账期，账期之间有效期不可交叉出现。<br>
                            3）订单账期：对某个初次或极少合作的客户针对某个特定订单开放的账期额度，该账期只能适用于该订单。</label>
                    </div>
                </div>
            </th>
            <th class="table-smallest">
                <div class="customernamec pos_rel">
                    <span>账期总额度</span>
                    <i class="iconbluemouth"></i>
                    <div class="pos_abs customernameshow" style="display: none;">
                        <label>此客户下所有已申请审核通过的对应类型的账期总额度</label>
                    </div>
                </div>
            </th>
            <th class="table-smallest">
                <div class="customernamec pos_rel">
                    <span>账期有效期</span>
                    <i class="iconbluemouth"></i>
                    <div class="pos_abs customernameshow" style="display: none;">
                        <label>当前可用账期的有效期</label>
                    </div>
                </div>
            </th>
            <th class="table-smallest">最近一次修改日期</th>
            <th class="table-smallest">
                <div class="customernamec pos_rel">
                    <span>有效账期数</span>
                    <i class="iconbluemouth"></i>
                    <div class="pos_abs customernameshow" style="display: none;">
                        <label>同时满足（申请审核通过）&（目前处于有效期内）的账期个数</label>
                    </div>
                </div>
            </th>
            <th class="table-smallest">
                <div class="customernamec pos_rel">
                    <span>当前可用总额度</span>
                    <i class="iconbluemouth"></i>
                    <div class="pos_abs customernameshow" style="display: none;">
                        <label>有效期内的账期可用于支付新订单的额度<br>【账期额度-已冻结额度（订单生效未实际支付的账期）-已占用额度（已用于支付的账期）+已归还的账期（售后退货或结款偿还的账期）】总和</label>
                    </div>
                </div>
            </th>
            <th class="table-smallest">未归还账期总额</th>
            <th class="table-smallest">逾期未还金额</th>
            <th class="table-smallest">使用次数</th>
            <th class="table-smallest">逾期次数</th>
            <th class="table-smallest">操作</th>
        </tr>
        </thead>

        <tbody>
        <tr>
            <td>订单账期</td>
            <c:choose>
                <c:when test="${empty billInfoListOrder}">
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                </c:when>
                <c:otherwise>
                    <%--<td><c:if test="${empty billInfoListOrder.availableAmount && empty billInfoListOrder.unreturnedAmount}"></c:if>${billInfoListOrder.availableAmount+billInfoListOrder.unreturnedAmount}</td>--%>
                    <td><c:if test="${empty billInfoListOrder.totalAmount}">0</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListOrder.totalAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>-</td>
                    <td><c:if test="${empty billInfoListOrder.lastModTime || billInfoListOrder.lastModTime == 0}">-</c:if>
                        <date:date value="${billInfoListOrder.lastModTime}" /></td>
                    </td>
                    <td><c:if test="${empty billInfoListOrder.countOfValid}">-</c:if>${billInfoListOrder.countOfValid}</td>
                    <td><c:if test="${empty billInfoListOrder.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListOrder.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListOrder.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListOrder.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListOrder.overDueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListOrder.overDueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListOrder.countOfUsed}">-</c:if>${billInfoListOrder.countOfUsed}</td>
                    <td><c:if test="${empty billInfoListOrder.countOfOverDue}">-</c:if>${billInfoListOrder.countOfOverDue}</td>
                </c:otherwise>
            </c:choose>
            <td>
                <div class="title-click addtitle" style="float:none" tabTitle='{"num":"accountListOrder${traderId}","link":"./finance/accountperiod/getCustomerAccountListPage.do?traderId=${traderId}&billType=3","title":"使用明细"}'>使用明细</div>

            </td>

        </tr>
        <tr>
            <td>临时账期</td>
            <c:choose>
                <c:when test="${empty  billInfoListTemporary}">
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                </c:when>
                <c:otherwise>
                    <%--<td><c:if test="${empty billInfoListTemporary.availableAmount && empty billInfoListTemporary.unreturnedAmount}"></c:if>${billInfoListTemporary.availableAmount+billInfoListTemporary.unreturnedAmount}</td>--%>
                    <td><c:if test="${empty billInfoListTemporary.totalAmount}">0</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListTemporary.totalAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td>-</td>
                    <td><c:if test="${empty billInfoListTemporary.lastModTime || billInfoListTemporary.lastModTime == 0}">-</c:if>
                        <date:date value="${billInfoListTemporary.lastModTime}" /></td>
                    </td>
                    <td><c:if test="${empty billInfoListTemporary.countOfValid}">-</c:if>${billInfoListTemporary.countOfValid}</td>
                    <td><c:if test="${empty billInfoListTemporary.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListTemporary.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListTemporary.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListTemporary.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListTemporary.overDueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListTemporary.overDueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListTemporary.countOfUsed}">-</c:if>${billInfoListTemporary.countOfUsed}</td>
                    <td><c:if test="${empty billInfoListTemporary.countOfOverDue}">-</c:if>${billInfoListTemporary.countOfOverDue}</td>
                </c:otherwise>
            </c:choose>
            <td>
                <div class="title-click addtitle" style="float:none" tabTitle='{"num":"accountListTemporary${traderId}","link":"./finance/accountperiod/getCustomerAccountListPage.do?traderId=${traderId}&billType=2","title":"使用明细"}'>使用明细</div>
            </td>
        </tr>

        <tr>
            <td>正式账期</td>
            <c:choose>
                <c:when test="${empty billInfoListFormal}">
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                    <td>-</td>
                </c:when>
                <c:otherwise>
                    <%--<td><c:if test="${empty billInfoListFormal.availableAmount && empty billInfoListFormal.unreturnedAmount}"></c:if>${billInfoListFormal.availableAmount+billInfoListFormal.unreturnedAmount}</td>--%>
                    <td><c:if test="${empty billInfoListFormal.totalAmount}">0</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListFormal.totalAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListFormal.billPeriodEnd}">-</c:if>
                        <date:date value="${billInfoListFormal.billPeriodEnd}" format="yyyy-MM-dd"/></td>
                    </td>
                    <td><c:if test="${empty billInfoListFormal.lastModTime || billInfoListFormal.lastModTime == 0}">-</c:if>
                        <date:date value="${billInfoListFormal.lastModTime}" /></td>
                    </td>
                    <td><c:if test="${empty billInfoListFormal.countOfValid}">-</c:if>${billInfoListFormal.countOfValid}</td>
                    <td><c:if test="${empty billInfoListFormal.availableAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListFormal.availableAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListFormal.unreturnedAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListFormal.unreturnedAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListFormal.overDueAmount}">-</c:if>
                        <fmt:formatNumber type="number" value="${billInfoListFormal.overDueAmount}" pattern="0.00" maxFractionDigits="2" />
                    </td>
                    <td><c:if test="${empty billInfoListFormal.countOfUsed}">-</c:if>${billInfoListFormal.countOfUsed}</td>
                    <td><c:if test="${empty billInfoListFormal.countOfOverDue}">-</c:if>${billInfoListFormal.countOfOverDue}</td>
                </c:otherwise>
            </c:choose>
            <td>
                <div class="title-click addtitle" style="float:none" tabTitle='{"num":"accountListFormal${traderId}","link":"./finance/accountperiod/getCustomerAccountListPage.do?traderId=${traderId}&billType=1","title":"使用明细"}'>使用明细</div>
            </td>
        </tr>
        </tbody>
    </table>
</div>

    <div class="add-tijiao pt25 layui-layer-title bottom-bottom" style="width: 100%">
        <button class="bt-small bt-bg-style bt-middle bg-light-blue bottom-c"  id="close-layer" type="button" onclick="closeGoBack();">确定</button>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        var bluemouthleft;
        $('.customernamec i').hover(function() {
            bluemouthleft = $(this).position().left + 25;
            bluemouthtop = $(this).position().top;
            $(this).parents('.customernamec').find('.customernameshow').show().css({ 'left': bluemouthleft + 'px', 'top': bluemouthtop + 'px' });

        }, function() {
            $(this).parents('.customernamec').find('.customernameshow').hide();
        })
    })
    function closeGoBack() {
        window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'),window.parent.closableTab.resizeMove);
    }
</script>
<%@ include file="../../common/footer.jsp"%>