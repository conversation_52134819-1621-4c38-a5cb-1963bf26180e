function tg(authorizationApplyId,formToken,endSt,skuid) {
	var sug=$('#suggestion').val();
	checkLogin();
	$.ajax({
		type : "POST",
		url : page_url+"/order/quote/canApply.do",
		data :{'authorizationApplyId':authorizationApplyId,'formToken':formToken,'endSt':endSt,'skuid':skuid},
		dataType : 'json',
		success : function(data) {
			if (data.code==0) {
				$.ajax({
					type : "POST",
					url : page_url+"/order/quote/next.do",
					data :{'authorizationApplyId':authorizationApplyId,'suggestion':sug ,'pass':true},
					dataType : 'json',
					success : function(data) {
						window.location.reload();
					}
				});
			}else {
				layer.confirm(data.message, {
					btn : [ '确定' ]
					//按钮
				}, function() {
					layer.closeAll();
					window.location.reload();
				}, function() {
				});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}


function bh(authorizationApplyId,formToken,endSt,skuid) {
	checkLogin();
	var sug=$('#suggestion').val();
	var sugLenth=$('#suggestion').val().length;
	if (sugLenth==0){
		layer.confirm("驳回理由不能为空", {
			btn : [ '确定']
			//按钮
		}, function() {
			layer.closeAll();
		});
		return;
	}
	$.ajax({
		type : "POST",
		url : page_url+"/order/quote/canApply.do",
		data :{'authorizationApplyId':authorizationApplyId,'formToken':formToken,'endSt':endSt,'skuid':skuid},
		dataType : 'json',
		success : function(data) {
			if (data.code==0) {
				$.ajax({
					type : "POST",
					url : page_url+"/order/quote/next.do",
					data :{'authorizationApplyId':authorizationApplyId,'suggestion':sug ,'pass': false},
					dataType : 'json',
					success : function(data) {
						window.location.reload();
					}
				});
			}else {
				layer.confirm(data.message, {
					btn : [ '确定' ]
					//按钮
				}, function() {
					layer.closeAll();
					window.location.reload();
				}, function() {
				});
			}
		},
		error:function(data){
			if(data.status ==1001){
				layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
			}
		}
	});

}