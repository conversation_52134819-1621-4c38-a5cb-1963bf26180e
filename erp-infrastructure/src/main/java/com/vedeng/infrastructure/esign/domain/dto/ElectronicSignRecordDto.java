package com.vedeng.infrastructure.esign.domain.dto;

import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.*;

/**
 * 电子签章记录表
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ElectronicSignRecordDto extends BaseDto {
    /**
     * 主键
     */
    private Integer electronicSignRecordId;

    /**
     * 状态(1.发送签章成功 2.发送签章失败 3.签章成功 4.签章失败)
     */
    private Integer signStatus;

    /**
     * 业务主键
     */
    private String businessId;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 是否删除
     */
    private Integer isDelete;

    /**
     * 重试次数
     */
    private Integer retryNum;

    /**
     * 是否发过通知
     */
    private Integer sendMsg;

    /**
     * 异常信息
     */
    private String errorMsg;
}