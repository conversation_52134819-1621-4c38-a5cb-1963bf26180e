package com.vedeng.infrastructure.bank.api.domain.dto;

import com.vedeng.infrastructure.bank.api.domain.B2eBasicDraftApplyReq;
import com.vedeng.infrastructure.bank.api.domain.B2eBasicDraftApplyRes;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 承兑汇票创建dto
 * @date 2024/10/11 11:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AcceptanceBillCreateDto {


    /**
     * 文件链接oss地址
     */
    private String fileUrl;

    /**
     * 付款申请id
     */
    private Integer payAppId;

    /**
     * 融资预申请请求体
     */
    private B2eBasicDraftApplyReq b2eBasicDraftApplyReq;

}
