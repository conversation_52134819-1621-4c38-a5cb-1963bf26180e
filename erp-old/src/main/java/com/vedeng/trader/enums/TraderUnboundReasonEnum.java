package com.vedeng.trader.enums;

import lombok.Getter;

/**
 * <AUTHOR> [<EMAIL>]
 */
@Getter
public enum TraderUnboundReasonEnum {
    STAFF_ON_FIRE(1, "人员离职"),
    STAFF_CHANGE_MOBILE(2, "更换新手机号"),
    OTHERS(-1, "其他");

    private Integer type;
    private String message;

    TraderUnboundReasonEnum(Integer type, String message) {
        this.type = type;
        this.message = message;
    }

    public static TraderUnboundReasonEnum getByType(Integer input) {
        if (input == null) {
            return null;
        }

        for (TraderUnboundReasonEnum value : values()) {
            if (input.equals(value.getType())) {
                return value;
            }
        }

        return null;
    }
}
