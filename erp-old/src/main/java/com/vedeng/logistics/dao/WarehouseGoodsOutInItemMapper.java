package com.vedeng.logistics.dao;
import java.util.Collection;
import java.util.List;

import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import com.vedeng.logistics.model.WarehouseGoodsOutInItemSn;
import com.vedeng.logistics.model.dto.GoodsOperateNumDto;
import org.apache.ibatis.annotations.Param;

public interface WarehouseGoodsOutInItemMapper {
    /**
     * delete by primary key
     * @param warehouseGoodsOutInDetailId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long warehouseGoodsOutInDetailId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(WarehouseGoodsOutInItem record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(WarehouseGoodsOutInItem record);

    /**
     * select by primary key
     * @param warehouseGoodsOutInDetailId primary key
     * @return object by primary key
     */
    WarehouseGoodsOutInItem selectByPrimaryKey(Long warehouseGoodsOutInDetailId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(WarehouseGoodsOutInItem record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(WarehouseGoodsOutInItem record);

    /**
     * select by  warehouseGoodsOutInItem
     * @param warehouseGoodsOutInItem
     * @return
     */
    List<WarehouseGoodsOutInItem> findByAll(WarehouseGoodsOutInItem warehouseGoodsOutInItem);

    /**
     * 获取逻辑仓可用商品信息
     * @param queryCon
     * @return
     */
	List<WarehouseGoodsOutInItem> getAvailableLogicalGoods(WarehouseGoodsOutInItem queryCon);

	/**
	 * 根据售后单号查询入库记录
	 * @param afterSalesNo
	 * @return
	 */
	List<WarehouseGoodsOutInItem> getRetrunOutInItem(String afterSalesNo);

    /**
     * 根据关联单号查出所有出入库单
     * @param relateNo
     * @return
     */
    List<WarehouseGoodsOutInItemSn> getOutInItemByRelateNo(@Param("relateNo") String relateNo,@Param("outInType") Integer outInType);


    /**
     * 售后商品ID获取出库数量
     * @param goodsIds
     * @return
     */
    List<GoodsOperateNumDto> getGoodsOperateNumByGoodsIds(@Param("goodsIds") List<Integer> goodsIds);

    /**
     * 逻辑删除
     * @param outInNo
     * @return
     */
    int logicalDeleteByOutInNo(@Param("outInNo")String outInNo);

    List<WarehouseGoodsOutInItem> findByRelatedId(@Param("relatedId")Integer relatedId);




    /**
     * 根据出库单类型和sn码判断是否存在
     *
     * @param barcodeFactory sn码
     * @param operateTypeCollection 出入库类型
     * @return 是否存在
     */
    boolean findByBarcodeFactoryAndOperateTypeIn(@Param("barcodeFactory")String barcodeFactory,
                                                                       @Param("operateTypeCollection")Collection<Integer> operateTypeCollection);



}