<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>添加虚拟商品</title> 
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="expenseSkuList" datasource="erp-datasourcetarget" empty_show="" fixednumber="0" firstcol="radio" fixednumberright="" append_column_url="" append_row_url=""> 
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <form class="layui-form" id="search"> 
    <div class="layui-inline "> <label>订货号</label> 
     <div class="layui-input-inline"> 
      <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like" name="SKU_NO" empty_show="-" style="" alias=""> 
     </div> 
    </div> 
    <div class="layui-inline "> <label>产品名称</label> 
     <div class="layui-input-inline"> 
      <input class="layui-input list-search-item" jdbctype="VARCHAR" type="text" placeholder="请输入" oper="like" name="SHOW_NAME" empty_show="-" style="" alias=""> 
     </div> 
    </div> 
    <div class=" layui-inline "> <label class="layui-form-label">费用类别</label> 
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="COST_CATEGORY_ID" placeholder="请选择" style="" alias="cs" jdbctype="" data="
     select COST_CATEGORY_ID K,
           		CATEGORY_NAME V
           		FROM T_SYS_COST_CATEGORY
      " datatype="KVSQLCACHE" oper="LIKE"></object> 
     </div> 
    </div> 
    <div class=" layui-inline "><label class="layui-form-label">SOURCE</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="hidden" name="SOURCE" placeholder style="" alias="" jdbctype="BODY" data="" datatype="" oper="" validate_rules="" validate_messages=""></object>
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton"> <button class="layui-btn list-row-button" url="ez_callback_chooseSku" name="SKU_ID,SKU_NO,SHOW_NAME,COST_CATEGORY_ID,CATEGORY_NAME,HAVE_STOCK_MANAGE" windowname="" style type="tableselectradio">选择</button> 
   </div> 
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column"> 
      <th name="SKU_NO">订货号</th> 
      <th name="SHOW_NAME">产品名称</th> 
      <th name="CATEGORY_NAME">费用类别</th> 
      <th type="rowbutton" id="rowbutton"> </th> 
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="order by cs.VIRTURE_TIME desc" groupby="">StringBuilder sql=new StringBuilder("select cs.SKU_ID,cs.SKU_NO,cs.SHOW_NAME,cs.COST_CATEGORY_ID,scc.CATEGORY_NAME,cs.HAVE_STOCK_MANAGE
                    FROM V_CORE_SKU cs
                     left join T_SYS_COST_CATEGORY scc on cs.COST_CATEGORY_ID = scc.COST_CATEGORY_ID
                     where ");
                    if($("SOURCE").equals("1")){
                        sql.append( " IS_VIRTURE_SKU = 1 AND cs.STATUS = 1 AND scc.IS_NEED_PURCHASE = 0" );
                    }else{
                        sql.append( " IS_VIRTURE_SKU = 1 AND cs.STATUS = 1");
                    }
                    list=search(sql);
                    return list;</pre> <pre id="count" class="layui-code"></pre> </td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot">
   <script>

        console.log('hello world!')
    </script>
  </div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script>  
 </body>
</html>