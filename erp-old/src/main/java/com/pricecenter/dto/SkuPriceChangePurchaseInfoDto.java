package com.pricecenter.dto;

import java.io.Serializable;
import java.math.BigDecimal;

public class SkuPriceChangePurchaseInfoDto implements Serializable {

    private Long id;

    private Long skuPriceChangeApplyId;

    private Integer traderId;

    private String traderName;

    private String changeReason;

    private BigDecimal purchasePrice;

    private BigDecimal lastPurchasePrice;

    private boolean purchasePriceChange;

    private String terminalRate;

    private String distributionRate;

    private String groupRate;

    private String electronicCommerceRate;

    private String researchTerminalRate;

    /**
     * 供应商售后政策是否维护
     */
    private Integer supplyPolicyMaintained;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getSkuPriceChangeApplyId() {
        return skuPriceChangeApplyId;
    }

    public void setSkuPriceChangeApplyId(Long skuPriceChangeApplyId) {
        this.skuPriceChangeApplyId = skuPriceChangeApplyId;
    }

    public Integer getTraderId() {
        return traderId;
    }

    public void setTraderId(Integer traderId) {
        this.traderId = traderId;
    }

    public String getTraderName() {
        return traderName;
    }

    public void setTraderName(String traderName) {
        this.traderName = traderName;
    }

    public BigDecimal getPurchasePrice() {
        return purchasePrice;
    }

    public void setPurchasePrice(BigDecimal purchasePrice) {
        this.purchasePrice = purchasePrice;
    }

    public String getChangeReason() {
        return changeReason;
    }

    public void setChangeReason(String changeReason) {
        this.changeReason = changeReason;
    }

    public String getTerminalRate() {
        return terminalRate;
    }

    public void setTerminalRate(String terminalRate) {
        this.terminalRate = terminalRate;
    }

    public String getDistributionRate() {
        return distributionRate;
    }

    public void setDistributionRate(String distributionRate) {
        this.distributionRate = distributionRate;
    }

    public BigDecimal getLastPurchasePrice() {
        return lastPurchasePrice;
    }

    public void setLastPurchasePrice(BigDecimal lastPurchasePrice) {
        this.lastPurchasePrice = lastPurchasePrice;
    }

    public boolean isPurchasePriceChange() {
        return purchasePriceChange;
    }

    public void setPurchasePriceChange(boolean purchasePriceChange) {
        this.purchasePriceChange = purchasePriceChange;
    }

    public Integer getSupplyPolicyMaintained() {
        return supplyPolicyMaintained;
    }

    public void setSupplyPolicyMaintained(Integer supplyPolicyMaintained) {
        this.supplyPolicyMaintained = supplyPolicyMaintained;
    }

    public String getGroupRate() {
        return groupRate;
    }

    public void setGroupRate(String groupRate) {
        this.groupRate = groupRate;
    }

    public String getElectronicCommerceRate() {
        return electronicCommerceRate;
    }

    public void setElectronicCommerceRate(String electronicCommerceRate) {
        this.electronicCommerceRate = electronicCommerceRate;
    }

    public String getResearchTerminalRate() {
        return researchTerminalRate;
    }

    public void setResearchTerminalRate(String researchTerminalRate) {
        this.researchTerminalRate = researchTerminalRate;
    }
}
