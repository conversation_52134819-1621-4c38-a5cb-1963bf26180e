package com.vedeng.erp.finance.service.impl.alias;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.finance.domain.dto.BankReceiptRecordDto;
import com.vedeng.erp.finance.domain.entity.BankReceiptRecordEntity;
import com.vedeng.erp.finance.mapper.BankReceiptRecordMapper;
import com.vedeng.erp.finance.mapstruct.BankReceiptRecordConvertor;
import com.vedeng.erp.finance.service.BankReceiptRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class BankReceiptRecordServiceImpl implements BankReceiptRecordService {

    @Autowired
    private BankReceiptRecordMapper bankReceiptRecordMapper;

    @Autowired
    private BankReceiptRecordConvertor bankReceiptRecordConvertor;


    @Override
    public BankReceiptRecordDto getById(Long bankReceiptAliasId) {

        BankReceiptRecordEntity bankReceiptRecordEntity = bankReceiptRecordMapper.selectByPrimaryKey(bankReceiptAliasId);
        BankReceiptRecordDto dto = bankReceiptRecordConvertor.toDto(bankReceiptRecordEntity);
        return dto;
    }

    @Override
    public List<BankReceiptRecordDto> findByAccountNoAndAccountType(String accountNo, Integer accountType) {
        List<BankReceiptRecordEntity> list = bankReceiptRecordMapper.findByAccountNoAndAccountType(accountNo, accountType);
        return bankReceiptRecordConvertor.toDto(list);
    }

    @Override
    public void tryCreateBankReceiptRecord(BankReceiptRecordDto bankReceiptRecordDto) {
        String nameAlias = bankReceiptRecordDto.getBankNameAlias();
        List<BankReceiptRecordEntity> list = bankReceiptRecordMapper.findByAccountNoAndAccountType(bankReceiptRecordDto.getAccountNo(), bankReceiptRecordDto.getAccountType());
        if (CollUtil.isNotEmpty(list)){
            BankReceiptRecordEntity bankReceiptRecordEntity = list.get(0);
            String bankNameAlias = bankReceiptRecordEntity.getBankNameAlias();

            List<String> bankNameAliasList = Arrays.asList(bankNameAlias.split(","));
            if (bankNameAliasList.contains(nameAlias)){
                log.info("已经存在回单解析记录，直接返回，{}",nameAlias);
                return;
            }
            log.info("更新回单解析记录");

            BankReceiptRecordEntity updateEntity = new BankReceiptRecordEntity();
            updateEntity.setBankNameAlias(bankNameAlias + "," + nameAlias);
            updateEntity.setBankReceiptAliasId(bankReceiptRecordEntity.getBankReceiptAliasId());
            bankReceiptRecordMapper.updateByPrimaryKeySelective(updateEntity);


            return;
        }
        BankReceiptRecordEntity entity = bankReceiptRecordConvertor.toEntity(bankReceiptRecordDto);
        log.info("插入回单解析记录：{}", JSON.toJSON(entity));
        bankReceiptRecordDto.setAddTime(new Date());
        bankReceiptRecordDto.setModTime(new Date());
        bankReceiptRecordMapper.insertSelective(entity);
    }
}
