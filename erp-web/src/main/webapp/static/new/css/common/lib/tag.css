.tag {
    border: 1px solid;
    border-radius: 3px;
    font-size: 12px;
    display: inline-block;
    text-decoration: none;
    padding: 0 5px;
    border-color: #ced3d9;
    color: #999;
}

.tag.tag-with-delete {
    cursor: default;
    position: relative;
}

.tag.tag-with-delete .micon-delete,
.tag.tag-with-delete .icon-delete {
    display: none;
    position: absolute;
    right: 5px;
    top: 50%;
    margin-top: -8px;
    cursor: pointer;
    color: #999;
}

.tag.tag-with-delete .micon-delete:hover,
.tag.tag-with-delete .icon-delete:hover {
    color: #333;
}

.tag.tag-with-delete:hover {
    padding-right: 25px;
    border-color: #7a8a99;
    color: #666;
    margin-right: -20px;
    background: #fff;
    z-index: 1;
}

.tag.tag-with-delete:hover .micon-delete,
.tag.tag-with-delete:hover .icon-delete {
    display: inline;
}

.tag.tag-with-delete.tag-large .micon-delete,
.tag.tag-with-delete.tag-large .icon-delete {
    right: 10px;
}

.tag.tag-with-delete.tag-large:hover {
    padding-right: 30px !important;
    margin-right: -20px;
}

.tag.tag-radio {
    padding: 0 5px;
    border-color: #ced3d9;
    color: #999;
}

.tag.tag-radio:hover {
    border-color: #7a8a99;
    color: #666;
    text-decoration: none;
}

.tag.tag-radio.selected {
    padding: 0 8px;
    border-color: #2E8AE6;
    color: #2E8AE6;
    position: relative;
}

.tag.tag-radio.selected:after {
    display: block;
    position: absolute;
    bottom: 0;
    right: 0;
    content: "";
    border-style: solid;
    font-size: 0;
    border-width: 6px 6px;
    border-color: transparent #2E8AE6 #2E8AE6 transparent;
    z-index: 1;
}

.tag.tag-radio.selected:before {
    content: "";
    display: inline-block;
    width: 12px;
    height: 12px;
    position: absolute;
    right: 0;
    bottom: 0;
    background: url("$${path.common}/img/icon/tag-selected.svg") no-repeat right bottom;
    background-size: 12px 12px;
    z-index: 2;
}

.tag.tag-color {
    padding: 0 5px;
}

.tag.tag-link {
    padding: 0 5px;
    border-color: #ced3d9;
    color: #999;
}

.tag.tag-link:hover {
    border-color: #7a8a99;
    color: #666;
    text-decoration: none;
}

.tag.tag-small {
    height: 18px;
    line-height: 18px;
}

.tag.tag-large {
    padding: 0 10px !important;
    height: 28px;
    line-height: 28px;
}

.tag.tag-blue {
    border-color: #3d85cc;
    color: #3d85cc;
}

.tag.tag-azure {
    border-color: #13a8bf;
    color: #13a8bf;
}

.tag.tag-grey {
    border-color: #ced3d9;
    color: #999;
}

.tag.tag-green {
    border-color: #32a632;
    color: #32a632;
}

.tag.tag-purple {
    border-color: #be60bf;
    color: #be60bf;
}

.tag.tag-red {
    border-color: #cc7070;
    color: #cc7070;
}

.tag.tag-yellow {
    border-color: #d99230;
    color: #d99230;
}

.tip {
    position: absolute;
    padding: 10px 10px;
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.5;
    color: #666;
    background: #fff;
    border: 1px solid #ced3d9;
    box-shadow: 2px 2px 3px rgba(0, 33, 66, 0.1);
}

.tip .tip-con .tip-para {
    margin: 0 0 10px 0;
}

.tip .tip-con .tip-para:last-child,
.tip .tip-con .tip-para.last-child {
    margin-bottom: 0;
}

.tip.icon-tip {
    box-shadow: none;
    padding: 3px 10px;
    border: none;
}

.tip.icon-tip .arrow {
    border: 6px dashed transparent;
}

.tip.icon-tip.tip-black {
    background: #333;
    color: #fff;
}

.tip.icon-tip.arrow-top .arrow {
    border-bottom: 6px solid #333;
    left: 50%;
    top: -11px;
    margin-left: -6px;
}

.tip.icon-tip.arrow-bottom .arrow {
    border-top: 6px solid #333;
    left: 50%;
    bottom: -11px;
    margin-left: -6px;
}

.tip.icon-tip.arrow-left .arrow {
    border-right: 6px solid #333;
    top: 50%;
    left: -11px;
    margin-top: -6px;
}

.tip.icon-tip.arrow-right .arrow {
    border-left: 6px solid #333;
    top: 50%;
    right: -11px;
    margin-top: -6px;
}

.tip .arrow {
    position: absolute;
    zoom: 1;
    width: 0;
    height: 0;
    line-height: 0;
    font-size: 0;
    border: 8px dashed transparent;
}

.tip.arrow-top .arrow-out {
    border-bottom: 8px solid #ced3d9;
    left: 10px;
    top: -16px;
}

.tip.arrow-top .arrow-in {
    border-bottom: 8px solid #fff;
    margin: -7px 0 0 -8px;
}

.tip.arrow-bottom .arrow-out {
    border-top: 8px solid #ced3d9;
    left: 10px;
    bottom: -16px;
}

.tip.arrow-bottom .arrow-in {
    border-top: 8px solid #fff;
    margin: -9px 0 0 -8px;
}

.tip.arrow-left .arrow-out {
    border-right: 8px solid #ced3d9;
    top: 10px;
    left: -16px;
}

.tip.arrow-left .arrow-in {
    border-right: 8px solid #fff;
    margin: -8px 0 0 -7px;
}

.tip.arrow-right .arrow-out {
    border-left: 8px solid #ced3d9;
    top: 10px;
    right: -16px;
}

.tip.arrow-right .arrow-in {
    border-left: 8px solid #fff;
    margin: -8px 0 0 -9px;
}

.dialog-alert {
    position: relative;
    margin-bottom: 20px;
}

.dialog-alert .dialog-alert-con {
    font-size: 14px;
    color: #666;
    display: inline-block;
    margin-left: 60px;
}

.dialog-alert .micon,
.dialog-alert .ob-icon {
    font-size: 32px;
    margin-right: 0;
}

.dialog-alert .dialog-micon-state {
    margin-right: 8px;
    position: absolute;
    left: 20px;
    top: -3px;
}

.dialog-alert.dialog-warn .dialog-micon-state {
    color: #ff9500;
}

.dialog-alert.dialog-succ .dialog-micon-state {
    color: #00bf00;
}

.dialog-alert.dialog-info .dialog-micon-state {
    color: #2e8ae5;
}

.dialog-alert.dialog-err .dialog-micon-state {
    color: #ff5959;
}