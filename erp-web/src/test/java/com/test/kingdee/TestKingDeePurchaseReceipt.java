package com.test.kingdee;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.vedeng.erp.kingdee.domain.command.KingDeePurchaseReceiptCommand;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeePurchaseReceiptDto;
import com.vedeng.erp.kingdee.repository.mappers.KingDeePurchaseReceiptMapper;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseReceiptCommandConvertor;
import com.vedeng.erp.kingdee.mapstruct.KingDeePurchaseReceiptConvertor;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.sdk.entity.SuccessEntity;
import com.vedeng.infrastructure.kingdee.domain.command.SaveExtCommand;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;
import java.util.*;

/**
 * 采购入库单
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
public class TestKingDeePurchaseReceipt {

    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private KingDeePurchaseReceiptCommandConvertor commandConvertor;

    @Resource
    private KingDeePurchaseReceiptConvertor purchaseReceiptConvertor;

    @Autowired
    private KingDeePurchaseReceiptMapper kingDeePayExpensesMapper;


    @Test
    public void kingDeePurchaseReceipt() {
    	KingDeePurchaseReceiptDto dto = new KingDeePurchaseReceiptDto();
        dto.setFId("0");
        dto.setFBillTypeId("RKD01_SYS");
        dto.setFBillNo("rkd-1001335s57554535945");
        dto.setFQzokBddjtId("贝登erp对应的单据头ID");
        dto.setFDate("2022-09-15");
        dto.setFStockOrgId("101");
        dto.setFSupplierId("ven0006");

        List<KingDeePurchaseReceiptDetailDto>  FEntityDetail = new ArrayList<>();
        KingDeePurchaseReceiptDetailDto detailDto = new KingDeePurchaseReceiptDetailDto();
        detailDto.setFMaterialId("SKU0001");
        detailDto.setFRealQty("100.0");
        detailDto.setFTaxPrice("113");
        detailDto.setFEntryTaxRate("13.00");
        detailDto.setFStockId("ck9999");
        detailDto.setFQzokYsddh("PD016848122111030369");
        detailDto.setFQzokGsywdh("采购订单");
        detailDto.setFQzokYwlx("业务类型");
        detailDto.setFQzokPch("pici_ly_test");
        detailDto.setFQzokXlh("SN");
        detailDto.setFQzokSfzf("是");
        detailDto.setFQzokBddjhId("4");
        FEntityDetail.add(detailDto);

        KingDeePurchaseReceiptDetailDto detailDto1 = new KingDeePurchaseReceiptDetailDto();
        detailDto1.setFMaterialId("SKU0001");
        detailDto1.setFRealQty("100.0");
        detailDto1.setFTaxPrice("113");
        detailDto1.setFEntryTaxRate("13.00");
        detailDto1.setFStockId("ck9999");
        detailDto1.setFQzokYsddh("PD016848122111030369");
        detailDto1.setFQzokGsywdh("采购订单");
        detailDto1.setFQzokYwlx("业务类型");
        detailDto1.setFQzokPch("pici_ly_test");
        detailDto1.setFQzokXlh("SN");
        detailDto1.setFQzokSfzf("是");
        detailDto1.setFQzokBddjhId("4");
        detailDto1.setFQzokSqlx("授权类型");
        FEntityDetail.add(detailDto1);

        dto.setFInStockEntry(FEntityDetail);

        KingDeePurchaseReceiptCommand command = commandConvertor.toCommand(dto);

        ArrayList<String> needReturnFields = new ArrayList<>();
        needReturnFields.add("FInStockEntry.FEntryID");
        needReturnFields.add("FInStockEntry.F_QZOK_BDDJHID");

        System.out.println("入参："+JSON.toJSONString(command));
        kingDeeBaseApi.save(new SaveExtCommand<>(command,dto.getFormId(),needReturnFields));
    }
}
