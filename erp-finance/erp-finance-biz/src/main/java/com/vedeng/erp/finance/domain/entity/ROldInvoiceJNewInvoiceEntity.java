package com.vedeng.erp.finance.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ROldInvoiceJNewInvoiceEntity extends BaseEntity {
    /**   R_OLD_INVOICE_J_NEW_INVOICE_ID **/
    private Integer rOldInvoiceJNewInvoiceId;

    /** 原蓝字发票ID  OLD_INVOICE_ID **/
    private Integer oldInvoiceId;

    /** 新蓝字发票ID  NEW_INVOICE_ID **/
    private Integer newInvoiceId;

    /** 仅退票的金额  INVOICE_AMOUNT **/
    private BigDecimal invoiceAmount;

    /** 是否删除0否1是  IS_DELETE **/
    private Integer isDelete;
}