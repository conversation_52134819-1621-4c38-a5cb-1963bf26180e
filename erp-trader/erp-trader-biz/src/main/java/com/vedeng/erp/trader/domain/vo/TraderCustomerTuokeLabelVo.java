package com.vedeng.erp.trader.domain.vo;

import com.vedeng.goods.dto.BaseCategoryDto;
import com.vedeng.goods.dto.DepartmentsHospitalDto;
import lombok.*;

import java.io.Serializable;
import java.util.List;

/**
 * 客户群标签表
 * @TableName T_TRADER_CUSTOMER_TUOKE_LABEL
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TraderCustomerTuokeLabelVo implements Serializable {
    /**
     * 客户群标签表
     */
    private Integer traderCustomerTuokeLabelId;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * 线索id
     */
    private Integer businessCluesId;

    /**
     * 线索对应的商机id
     */
    private Integer businessChanceId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 经营对象，逗号分隔，1：公立，2：民营(详情页使用)
     */
    private String businessTargetStr;

    /**
     * 医院等级，逗号分隔，1市级以上，2区县级医疗机构，3基层医疗，4公共卫生(详情页使用)
     */
    private String hospitalLevelStr;

    /**
     * 经营科室，逗号分隔，T_DEPARTMENTS_HOSPITAL主键(详情页使用)
     */
    private String businessDepartmentStr;

    /**
     * 经营产品，逗号分隔，V_BASE_CATEGORY一级分类的ID(详情页使用)
     */
    private String businessGoodsStr;

    /**
     * 业务模式，逗号分隔，1：直销，2：分销(详情页使用)
     */
    private String businessModelStr;

    /**
     * 销售模式，逗号分隔，1：代理权，2：关系(详情页使用)
     */
    private String salesModelStr;

    /**
     * 经营对象，逗号分隔，1：公立，2：民营
     */
    private String[] businessTarget;

    /**
     * 医院等级，逗号分隔，1市级以上，2区县级医疗机构，3基层医疗，4公共卫生
     */
    private String[] hospitalLevel;

    /**
     * 经营科室，逗号分隔，T_DEPARTMENTS_HOSPITAL主键
     */
    private String[] businessDepartment;

    /**
     * 经营产品，逗号分隔，V_BASE_CATEGORY一级分类的ID
     */
    private String[] businessGoods;

    /**
     * 业务模式，逗号分隔，1：直销，2：分销
     */
    private String[] businessModel;

    /**
     * 销售模式，逗号分隔，1：代理权，2：关系
     */
    private String[] salesModel;

    /**
     *
     */
    private Integer creator;

    /**
     *
     */
    private Long addTime;

    /**
     *
     */
    private Integer updater;

    /**
     *
     */
    private Long modTime;

    /**
     * 线索价值
     */
    private Integer worth;

    /**
     * 备注
     */
    private String comment;

    List<DepartmentsHospitalDto> allLabel;

    List<BaseCategoryDto> levelCategory;

    List<String> businessTargets;

    List<String> hospitalLevels;

    List<String> businessModels;

    List<String> salesModels;

    private static final long serialVersionUID = 1L;

}