<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.AfterSalesDetailMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.aftersales.model.AfterSalesDetail" >
    <id column="AFTER_SALES_DETAIL_ID" property="afterSalesDetailId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    <result column="REASON" property="reason" jdbcType="INTEGER" />
    <result column="COMMENTS" property="comments" jdbcType="VARCHAR" />
    <result column="TRADER_ID" property="traderId" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_NAME" property="traderContactName" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_MOBILE" property="traderContactMobile" jdbcType="VARCHAR" />
    <result column="TRADER_CONTACT_TELEPHONE" property="traderContactTelephone" jdbcType="VARCHAR" />
    <result column="REFUND" property="refund" jdbcType="BIT" />
    <result column="AREA_ID" property="areaId" jdbcType="INTEGER" />
    <result column="ADDRESS_ID" property="addressId" jdbcType="INTEGER" />
    <result column="AREA" property="area" jdbcType="VARCHAR" />
    <result column="ADDRESS" property="address" jdbcType="VARCHAR" />
    <result column="REFUND_AMOUNT" property="refundAmount" jdbcType="DECIMAL" />
    <result column="REFUND_FEE" property="refundFee" jdbcType="DECIMAL" />
    <result column="REAL_REFUND_AMOUNT" property="realRefundAmount" jdbcType="DECIMAL" />
    <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="DECIMAL" />
    <result column="REFUND_AMOUNT_STATUS" property="refundAmountStatus" jdbcType="BIT" />
    <result column="TRADER_SUBJECT" property="traderSubject" jdbcType="BIT" />
    <result column="PAYEE" property="payee" jdbcType="VARCHAR" />
    <result column="BANK" property="bank" jdbcType="VARCHAR" />
    <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR" />
    <result column="BANK_ACCOUNT" property="bankAccount" jdbcType="VARCHAR" />
    <result column="SERVICE_AMOUNT" property="serviceAmount" jdbcType="DECIMAL" />
    <result column="INVOICE_TYPE" property="invoiceType" jdbcType="INTEGER" />
    <result column="IS_SEND_INVOICE" property="isSendInvoice" jdbcType="BIT" />
    <result column="TRADER_MODE" property="traderMode" jdbcType="BIT" />
    <result column="PERIOD_AMOUNT" property="payPeriodAmount" jdbcType="DECIMAL" />
    <result column="INVOICE_TRADER_ID" property="invoiceTraderId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_NAME" property="invoiceTraderName" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_ID" property="invoiceTraderContactId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_CONTACT_NAME" property="invoiceTraderContactName" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_MOBILE" property="invoiceTraderContactMobile" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_CONTACT_TELEPHONE" property="invoiceTraderContactTelephone" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_ADDRESS_ID" property="invoiceTraderAddressId" jdbcType="INTEGER" />
    <result column="INVOICE_TRADER_ADDRESS" property="invoiceTraderAddress" jdbcType="VARCHAR" />
    <result column="INVOICE_TRADER_AREA" property="invoiceTraderArea" jdbcType="VARCHAR" />
    
    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR" />
    
    <result column="RECEIVE_PAYMENT_STATUS" property="receivePaymentStatus" jdbcType="BIT" />
    <result column="RECEIVE_PAYMENT_TIME" property="receivePaymentTime" jdbcType="BIGINT" />

    <result column="RECEIVE_INVOICE_STATUS" property="receiveInvoiceStatus" jdbcType="INTEGER" />

    <result column="PAYMENT_STATUS" property="paymentStatus" jdbcType="BIT" />
    <result column="PAYMENT_TIME" property="paymentTime" jdbcType="BIGINT" />
    <result column="FINAL_REFUNDABLE_AMOUNT" property="finalRefundableAmount" jdbcType="DECIMAL" />
  </resultMap>
  
  <resultMap type="com.vedeng.aftersales.model.vo.AfterSalesDetailVo" id="VoResultMap" extends="BaseResultMap">
  	<result column="ORDER_ID" property="orderId" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    AFTER_SALES_DETAIL_ID, AFTER_SALES_ID, REASON, COMMENTS, TRADER_ID, TRADER_CONTACT_ID, TRADER_CONTACT_NAME, 
    TRADER_CONTACT_MOBILE, TRADER_CONTACT_TELEPHONE, REFUND, AREA_ID, ADDRESS_ID, AREA, 
    ADDRESS, REFUND_AMOUNT, REFUND_FEE, REAL_REFUND_AMOUNT, PAYMENT_AMOUNT, REFUND_AMOUNT_STATUS, TRADER_SUBJECT, TRADER_MODE,
    PAYEE, BANK, BANK_CODE, BANK_ACCOUNT, SERVICE_AMOUNT, INVOICE_TYPE, IS_SEND_INVOICE, PERIOD_AMOUNT,
    INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, 
    INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, 
    INVOICE_TRADER_ADDRESS, INVOICE_TRADER_AREA, INVOICE_COMMENTS,RECEIVE_PAYMENT_STATUS,RECEIVE_PAYMENT_TIME,FINAL_REFUNDABLE_AMOUNT
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_DETAIL
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_AFTER_SALES_DETAIL
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.aftersales.model.AfterSalesDetail" >
    insert into T_AFTER_SALES_DETAIL (AFTER_SALES_DETAIL_ID, AFTER_SALES_ID, 
      REASON, COMMENTS, TRADER_ID, TRADER_CONTACT_ID, 
      TRADER_CONTACT_NAME, TRADER_CONTACT_MOBILE, 
      TRADER_CONTACT_TELEPHONE, REFUND, AREA_ID, 
      ADDRESS_ID, AREA, ADDRESS, 
      REFUND_AMOUNT, REFUND_FEE, REAL_REFUND_AMOUNT, PAYMENT_AMOUNT,
      REFUND_AMOUNT_STATUS, TRADER_SUBJECT, TRADER_MODE, PAYEE, 
      BANK, BANK_CODE, BANK_ACCOUNT, PERIOD_AMOUNT,
      INVOICE_TRADER_ID, INVOICE_TRADER_NAME, INVOICE_TRADER_CONTACT_ID, INVOICE_TRADER_CONTACT_NAME, 
      INVOICE_TRADER_CONTACT_MOBILE, INVOICE_TRADER_CONTACT_TELEPHONE, INVOICE_TRADER_ADDRESS_ID, 
      INVOICE_TRADER_ADDRESS, INVOICE_TRADER_AREA, INVOICE_COMMENTS, RECEIVE_PAYMENT_STATUS,RECEIVE_PAYMENT_TIME
      )
    values (#{afterSalesDetailId,jdbcType=INTEGER}, #{afterSalesId,jdbcType=INTEGER}, 
      #{reason,jdbcType=INTEGER}, #{comments,jdbcType=VARCHAR}, #{traderId,jdbcType=INTEGER}, #{traderContactId,jdbcType=INTEGER}, 
      #{traderContactName,jdbcType=VARCHAR}, #{traderContactMobile,jdbcType=VARCHAR}, 
      #{traderContactTelephone,jdbcType=VARCHAR}, #{refund,jdbcType=BIT}, #{areaId,jdbcType=INTEGER}, 
      #{addressId,jdbcType=INTEGER}, #{area,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{refundAmount,jdbcType=DECIMAL}, #{refundFee,jdbcType=DECIMAL}, #{realRefundAmount,jdbcType=DECIMAL}, #{paymentAmount,jdbcType=DECIMAL},
      #{refundAmountStatus,jdbcType=BIT}, #{traderSubject,jdbcType=BIT}, #{traderMode,jdbcType=BIT}, #{payee,jdbcType=VARCHAR}, 
      #{bank,jdbcType=VARCHAR}, #{bankCode,jdbcType=VARCHAR}, #{bankAccount,jdbcType=VARCHAR}, #{payPeriodAmount,jdbcType=DECIMAL},
      #{invoiceTraderId,jdbcType=INTEGER}, #{invoiceTraderName,jdbcType=VARCHAR}, #{invoiceTraderContactId,jdbcType=INTEGER},
      #{invoiceTraderContactName,jdbcType=VARCHAR}, #{invoiceTraderContactMobile,jdbcType=VARCHAR}, #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      #{invoiceTraderAddressId,jdbcType=INTEGER}, #{invoiceTraderAddress,jdbcType=VARCHAR}, #{invoiceTraderArea,jdbcType=VARCHAR}, #{invoiceComments,jdbcType=VARCHAR},
      #{receivePaymentStatus,jdbcType=BIT}, #{receivePaymentTime,jdbcType=BIGINT},#{paymentStatus,jdbcType=BIT}, #{paymentTime,jdbcType=BIGINT} 
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.aftersales.model.AfterSalesDetail" useGeneratedKeys="true" keyColumn="AFTER_SALES_DETAIL_ID" keyProperty="afterSalesDetailId">
    insert into T_AFTER_SALES_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterSalesDetailId != null" >
        AFTER_SALES_DETAIL_ID,
      </if>
      <if test="afterSalesId != null" >
        AFTER_SALES_ID,
      </if>
      <if test="reason != null" >
        REASON,
      </if>
      <if test="comments != null" >
        COMMENTS,
      </if>
      <if test="traderId != null" >
        TRADER_ID,
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID,
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME,
      </if>
      <if test="traderContactMobile != null" >
        TRADER_CONTACT_MOBILE,
      </if>
      <if test="traderContactTelephone != null" >
        TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="refund != null" >
        REFUND,
      </if>
      <if test="areaId != null" >
        AREA_ID,
      </if>
      <if test="addressId != null" >
        ADDRESS_ID,
      </if>
      <if test="area != null" >
        AREA,
      </if>
      <if test="address != null" >
        ADDRESS,
      </if>
      <if test="refundAmount != null" >
        REFUND_AMOUNT,
      </if>
      <if test="refundFee != null" >
        REFUND_FEE,
      </if>
      <if test="realRefundAmount != null" >
        REAL_REFUND_AMOUNT,
      </if>
      <if test="paymentAmount != null" >
        PAYMENT_AMOUNT,
      </if>
      <if test="refundAmountStatus != null" >
        REFUND_AMOUNT_STATUS,
      </if>
      <if test="traderSubject != null" >
        TRADER_SUBJECT,
      </if>
      <if test="traderMode != null" >
        TRADER_MODE,
      </if>
      <if test="payee != null" >
        PAYEE,
      </if>
      <if test="bank != null" >
        BANK,
      </if>
      <if test="bankCode != null" >
        BANK_CODE,
      </if>
      <if test="bankAccount != null" >
        BANK_ACCOUNT,
      </if>
      <if test="serviceAmount != null" >
        SERVICE_AMOUNT,
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE,
      </if>
      <if test="isSendInvoice != null" >
        IS_SEND_INVOICE,
      </if>
      <if test="payPeriodAmount != null" >
        PERIOD_AMOUNT,
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID,
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME,
      </if>      
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID ,
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME ,
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE ,
      </if>      
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE,
      </if>
      <if test="invoiceTraderAddressId  != null" >
        INVOICE_TRADER_ADDRESS_ID,
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS,
      </if>      
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA,
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS,
      </if>
      <if test="receivePaymentStatus != null" >
        RECEIVE_PAYMENT_STATUS,
      </if>
      <if test="receivePaymentTime != null" >
        RECEIVE_PAYMENT_TIME,
      </if>
      <if test="paymentStatus != null" >
        PAYMENT_STATUS,
      </if>
      <if test="paymentTime != null" >
        PAYMENT_TIME,
      </if>
      <if test="afterConnectUserName != null">
        AFTER_CONNECT_USERNAME,
      </if>
      <if test="afterConnectPhone != null">
        AFTER_CONNECT_PHONE,
      </if>
      <if test="firstResponsibleDepartment != null">
        FIRST_RESPONSIBLE_DEPARTMENT,
      </if>
      <if test="refundComment != null">
        REFUND_COMMENT,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterSalesDetailId != null" >
        #{afterSalesDetailId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesId != null" >
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null" >
        #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null" >
        #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null" >
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null" >
        #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null" >
        #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null" >
        #{refund,jdbcType=BIT},
      </if>
      <if test="areaId != null" >
        #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null" >
        #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null" >
        #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null" >
        #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null" >
        #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null" >
        #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null" >
        #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null and refundAmountStatus != -1 " >
        #{refundAmountStatus,jdbcType=BIT},
      </if>
      <if test="refundAmountStatus != null and refundAmountStatus == -1 " >
        null,
      </if>
      <if test="traderSubject != null" >
        #{traderSubject,jdbcType=BIT},
      </if>
      <if test="traderMode != null" >
        #{traderMode,jdbcType=BIT},
      </if>
      <if test="payee != null" >
        #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null" >
        #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null" >
        #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null" >
        #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null" >
        #{isSendInvoice,jdbcType=BIT},
      </if>
      <if test="payPeriodAmount != null" >
        #{payPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null" >
        #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        #{invoiceTraderName,jdbcType=VARCHAR},
      </if>      
      <if test="invoiceTraderContactId != null" >
        #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>      
      <if test="invoiceTraderContactTelephone != null" >
        #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId  != null" >
        #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAddress != null" >
        #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>      
      <if test="invoiceTraderArea != null" >
        #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="receivePaymentStatus != null" >
        #{receivePaymentStatus,jdbcType=BIT},  
      </if>
      <if test="receivePaymentTime != null" >
        #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        #{paymentStatus,jdbcType=BIT},  
      </if>
      <if test="paymentTime != null" >
        #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="afterConnectUserName != null">
        #{afterConnectUserName,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null">
        #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="firstResponsibleDepartment != null">
        #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
      <if test="refundComment != null">
        #{refundComment,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.aftersales.model.AfterSalesDetail" >
    update T_AFTER_SALES_DETAIL
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        REASON = #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null" >
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null" >
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null" >
        REFUND = #{refund,jdbcType=BIT},
      </if>
      <if test="areaId != null" >
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null" >
        ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null" >
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null" >
        REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null" >
        REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null" >
        REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null" >
        PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null and refundAmountStatus != -1" >
        REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BIT},
      </if>
      <if test="refundAmountStatus != null and refundAmountStatus == -1" >
        REFUND_AMOUNT_STATUS = null,
      </if>
      <if test="traderSubject != null" >
        TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
      </if>
      <if test="traderMode != null" >
        TRADER_MODE = #{traderMode,jdbcType=BIT},
      </if>
      <if test="payee != null" >
        PAYEE = #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null" >
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null" >
        SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null" >
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
      </if>
      <if test="payPeriodAmount != null" >
        PERIOD_AMOUNT = #{payPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>      
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>      
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId  != null" >
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>      
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="receivePaymentStatus != null" >
        RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BIT},  
      </if>
      <if test="receivePaymentTime != null" >
        RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},  
      </if>
      <if test="paymentTime != null" >
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="receiveInvoiceStatus != null" >
        RECEIVE_INVOICE_STATUS = #{receiveInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveInvoiceTime != null" >
        RECEIVE_INVOICE_TIME = #{receiveInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="afterConnectUserName != null">
        AFTER_CONNECT_USERNAME = #{afterConnectUserName,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null">
        AFTER_CONNECT_PHONE = #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="firstResponsibleDepartment != null">
        FIRST_RESPONSIBLE_DEPARTMENT = #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
      <if test="refundComment != null">
        REFUND_COMMENT = #{refundComment,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.AfterSalesDetail" >
    update T_AFTER_SALES_DETAIL
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      REASON = #{reason,jdbcType=INTEGER},
      COMMENTS = #{comments,jdbcType=VARCHAR},
      TRADER_ID = #{traderId,jdbcType=INTEGER},
      TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      REFUND = #{refund,jdbcType=BIT},
      AREA_ID = #{areaId,jdbcType=INTEGER},
      ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      AREA = #{area,jdbcType=VARCHAR},
      ADDRESS = #{address,jdbcType=VARCHAR},
      REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BIT},
      TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
      TRADER_MODE = #{traderMode,jdbcType=BIT},
      PAYEE = #{payee,jdbcType=VARCHAR},
      BANK = #{bank,jdbcType=VARCHAR},
      BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
      PERIOD_AMOUNT = #{payPeriodAmount,jdbcType=DECIMAL},
      INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BIT},  
	  RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
	  PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},  
	  PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT}
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </update>
  
  <select id="getAfterSalesDetailVo" parameterType="com.vedeng.aftersales.model.AfterSalesDetail" resultMap="VoResultMap">
  	select
  		a.AFTER_SALES_DETAIL_ID, a.AFTER_SALES_ID, a.REASON, a.COMMENTS, a.TRADER_ID, a.TRADER_CONTACT_ID, a.TRADER_CONTACT_NAME, 
	    a.TRADER_CONTACT_MOBILE, a.TRADER_CONTACT_TELEPHONE, a.REFUND, a.AREA_ID, a.ADDRESS_ID, a.AREA, 
	    a.ADDRESS, a.REFUND_AMOUNT, a.REFUND_FEE, a.REAL_REFUND_AMOUNT, a.PAYMENT_AMOUNT, a.REFUND_AMOUNT_STATUS, a.TRADER_SUBJECT, a.TRADER_MODE,
	    a.PAYEE, a.BANK, a.BANK_CODE, a.BANK_ACCOUNT,b.ORDER_ID,a.SERVICE_AMOUNT, a.INVOICE_TYPE, a.IS_SEND_INVOICE, a.PERIOD_AMOUNT,
	    a.INVOICE_TRADER_ID, a.INVOICE_TRADER_NAME, a.INVOICE_TRADER_CONTACT_ID, a.INVOICE_TRADER_CONTACT_NAME, 
      	a.INVOICE_TRADER_CONTACT_MOBILE, a.INVOICE_TRADER_CONTACT_TELEPHONE, a.INVOICE_TRADER_ADDRESS_ID, 
      	a.INVOICE_TRADER_ADDRESS, a.INVOICE_TRADER_AREA, a.INVOICE_COMMENTS
  	from T_AFTER_SALES_DETAIL a
  	left join T_AFTER_SALES b on a.AFTER_SALES_ID = b.AFTER_SALES_ID
  	where 1=1
  		<if test="afterSalesId != null" >
        	and a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
        </if>
  </select>
  
  <delete id="delAfterSalesDetailByafterSalesId" parameterType="java.lang.Integer" >
    delete from T_AFTER_SALES_DETAIL
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </delete>
  
  <select id="getAfterSalesDetailVoById" resultMap="VoResultMap" parameterType="java.lang.Integer" >
    select 
    	<include refid="Base_Column_List" />
    from T_AFTER_SALES_DETAIL a
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </select>
  <!-- 查询售后详情 -->
   <select id="selectadtbyid" resultMap="VoResultMap" parameterType="com.vedeng.aftersales.model.AfterSales" >
    SELECT
		a.*,b.TRADER_NAME
	FROM
		T_AFTER_SALES_DETAIL a
		LEFT JOIN T_TRADER b
	ON a.TRADER_ID = b.TRADER_ID
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>

  <update id="addIsLightning">
    UPDATE T_AFTER_SALES
    SET
    IS_LIGHTNING = #{isLightning,jdbcType=INTEGER}
    WHERE AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyInfo">
    update T_AFTER_SALES
    <set >
      <if test="afterSalesNo != null" >
        AFTER_SALES_NO = #{afterSalesNo,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null" >
        COMPANY_ID = #{companyId,jdbcType=INTEGER},
      </if>
      <if test="subjectType != null" >
        SUBJECT_TYPE = #{subjectType,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        `TYPE` = #{type,jdbcType=INTEGER},
      </if>
      <if test="orderId != null" >
        ORDER_ID = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderNo != null" >
        ORDER_NO = #{orderNo,jdbcType=VARCHAR},
      </if>
      <if test="serviceUserId != null" >
        SERVICE_USER_ID = #{serviceUserId,jdbcType=INTEGER},
      </if>
      <if test="validStatus != null" >
        VALID_STATUS = #{validStatus,jdbcType=BIT},
      </if>
      <if test="validTime != null" >
        VALID_TIME = #{validTime,jdbcType=BIGINT},
      </if>
      <if test="status != null" >
        STATUS = #{status,jdbcType=BIT},
      </if>
      <if test="atferSalesStatus != null" >
        ATFER_SALES_STATUS = #{atferSalesStatus,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <!--       	新增售后关闭/完结原因、操作人、备注 -->
      <if test="afterSalesStatusReson != null">
        ATFER_SALES_STATUS_RESON = #{afterSalesStatusReson,jdbcType=INTEGER},
      </if>
      <if test="afterSalesStatusUser != null">
        ATFER_SALES_STATUS_USER = #{afterSalesStatusUser,jdbcType=INTEGER},
      </if>
      <if test="afterSalesStatusComments != null">
        ATFER_SALES_STATUS_COMMENTS = #{afterSalesStatusComments},
      </if>
      <!--       end -->
      <if test="firstValidStatus != null" >
        FIRST_VALID_STATUS = #{firstValidStatus,jdbcType=BIT},
      </if>
      <if test="firstValidTime != null" >
        FIRST_VALID_TIME = #{firstValidTime,jdbcType=BIGINT},
      </if>
      <if test="firstValidUser != null" >
        FIRST_VALID_USER = #{firstValidUser,jdbcType=INTEGER},
      </if>
      <if test="firstValidComments != null" >
        FIRST_VALID_COMMENTS = #{firstValidComments,jdbcType=VARCHAR},
      </if>
      <if test="source != null" >
        SOURCE = #{source,jdbcType=BIT},
      </if>
      <if test="isOutAfter!=null">
        IS_OUT_AFTER=#{isOutAfter},
      </if>
      <if test="createFrontEndUser != null" >
        CREATE_FRONT_END_USER = #{createFrontEndUser,jdbcType=VARCHAR},
      </if>
      <if test="closeFrontEndMobile != null" >
        CLOSE_FRONT_END_MOBILE = #{closeFrontEndMobile,jdbcType=VARCHAR},
      </if>
    </set>
    where AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </update>

  <update id="updateByPrimaryKeyDetailInfo">
    update T_AFTER_SALES_DETAIL
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="reason != null" >
        REASON = #{reason,jdbcType=INTEGER},
      </if>
      <if test="comments != null" >
        COMMENTS = #{comments,jdbcType=VARCHAR},
      </if>
      <if test="traderId != null" >
        TRADER_ID = #{traderId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderContactName != null" >
        TRADER_CONTACT_NAME = #{traderContactName,jdbcType=VARCHAR},
      </if>
      <if test="traderContactMobile != null" >
        TRADER_CONTACT_MOBILE = #{traderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="traderContactTelephone != null" >
        TRADER_CONTACT_TELEPHONE = #{traderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="refund != null" >
        REFUND = #{refund,jdbcType=BIT},
      </if>
      <if test="areaId != null" >
        AREA_ID = #{areaId,jdbcType=INTEGER},
      </if>
      <if test="addressId != null" >
        ADDRESS_ID = #{addressId,jdbcType=INTEGER},
      </if>
      <if test="area != null" >
        AREA = #{area,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        ADDRESS = #{address,jdbcType=VARCHAR},
      </if>
      <if test="refundAmount != null" >
        REFUND_AMOUNT = #{refundAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundFee != null" >
        REFUND_FEE = #{refundFee,jdbcType=DECIMAL},
      </if>
      <if test="realRefundAmount != null" >
        REAL_REFUND_AMOUNT = #{realRefundAmount,jdbcType=DECIMAL},
      </if>
      <if test="paymentAmount != null" >
        PAYMENT_AMOUNT = #{paymentAmount,jdbcType=DECIMAL},
      </if>
      <if test="refundAmountStatus != null" >
        REFUND_AMOUNT_STATUS = #{refundAmountStatus,jdbcType=BIT},
      </if>
      <if test="traderSubject != null" >
        TRADER_SUBJECT = #{traderSubject,jdbcType=BIT},
      </if>
      <if test="traderMode != null" >
        TRADER_MODE = #{traderMode,jdbcType=BIT},
      </if>
      <if test="payee != null" >
        PAYEE = #{payee,jdbcType=VARCHAR},
      </if>
      <if test="bank != null" >
        BANK = #{bank,jdbcType=VARCHAR},
      </if>
      <if test="bankCode != null" >
        BANK_CODE = #{bankCode,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null" >
        BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="serviceAmount != null" >
        SERVICE_AMOUNT = #{serviceAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceType != null" >
        INVOICE_TYPE = #{invoiceType,jdbcType=INTEGER},
      </if>
      <if test="isSendInvoice != null" >
        IS_SEND_INVOICE = #{isSendInvoice,jdbcType=BIT},
      </if>
      <if test="payPeriodAmount != null" >
        PERIOD_AMOUNT = #{payPeriodAmount,jdbcType=DECIMAL},
      </if>
      <if test="invoiceTraderId != null" >
        INVOICE_TRADER_ID = #{invoiceTraderId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderName != null" >
        INVOICE_TRADER_NAME = #{invoiceTraderName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactId != null" >
        INVOICE_TRADER_CONTACT_ID = #{invoiceTraderContactId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderContactName != null" >
        INVOICE_TRADER_CONTACT_NAME = #{invoiceTraderContactName,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactMobile != null" >
        INVOICE_TRADER_CONTACT_MOBILE = #{invoiceTraderContactMobile,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderContactTelephone != null" >
        INVOICE_TRADER_CONTACT_TELEPHONE = #{invoiceTraderContactTelephone,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderAddressId  != null" >
        INVOICE_TRADER_ADDRESS_ID = #{invoiceTraderAddressId,jdbcType=INTEGER},
      </if>
      <if test="invoiceTraderAddress != null" >
        INVOICE_TRADER_ADDRESS = #{invoiceTraderAddress,jdbcType=VARCHAR},
      </if>
      <if test="invoiceTraderArea != null" >
        INVOICE_TRADER_AREA = #{invoiceTraderArea,jdbcType=VARCHAR},
      </if>
      <if test="invoiceComments != null" >
        INVOICE_COMMENTS = #{invoiceComments,jdbcType=VARCHAR},
      </if>
      <if test="receivePaymentStatus != null" >
        RECEIVE_PAYMENT_STATUS = #{receivePaymentStatus,jdbcType=BIT},
      </if>
      <if test="receivePaymentTime != null" >
        RECEIVE_PAYMENT_TIME = #{receivePaymentTime,jdbcType=BIGINT},
      </if>
      <if test="paymentStatus != null" >
        PAYMENT_STATUS = #{paymentStatus,jdbcType=BIT},
      </if>
      <if test="paymentTime != null" >
        PAYMENT_TIME = #{paymentTime,jdbcType=BIGINT},
      </if>
      <if test="receiveInvoiceStatus != null" >
        RECEIVE_INVOICE_STATUS = #{receiveInvoiceStatus,jdbcType=INTEGER},
      </if>
      <if test="receiveInvoiceTime != null" >
        RECEIVE_INVOICE_TIME = #{receiveInvoiceTime,jdbcType=BIGINT},
      </if>
      <if test="afterConnectUserName != null" >
        AFTER_CONNECT_USERNAME = #{afterConnectUserName,jdbcType=VARCHAR},
      </if>
      <if test="afterConnectPhone != null" >
        AFTER_CONNECT_PHONE = #{afterConnectPhone,jdbcType=VARCHAR},
      </if>
      <if test="firstResponsibleDepartment != null" >
        FIRST_RESPONSIBLE_DEPARTMENT = #{firstResponsibleDepartment,jdbcType=INTEGER},
      </if>
    </set>
    where AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
  </update>

    <update id="updateFinalRefundableAmountByDetalId">
      UPDATE T_AFTER_SALES_DETAIL
      SET FINAL_REFUNDABLE_AMOUNT = #{finalRefundableAmount,jdbcType=DECIMAL}
      WHERE
          AFTER_SALES_DETAIL_ID = #{afterSalesDetailId,jdbcType=INTEGER}
    </update>

    <select id="selectBuyConBuyOrderNo" resultType="string">
    SELECT (CASE WHEN d.SPECIAL_DELIVERY = 1 THEN e.BUYORDER_NO ELSE '*' END)AS buyorderNo
    FROM T_AFTER_SALES_GOODS a
    LEFT JOIN T_BUYORDER_GOODS b ON a.ORDER_DETAIL_ID = b.BUYORDER_GOODS_ID
	LEFT JOIN T_BUYORDER e ON b.BUYORDER_ID = e.BUYORDER_ID
    LEFT JOIN T_R_BUYORDER_J_SALEORDER c ON b.BUYORDER_GOODS_ID = c.SALEORDER_GOODS_ID
    LEFT JOIN T_SALEORDER_GOODS d ON c.SALEORDER_GOODS_ID = d.SALEORDER_GOODS_ID
    WHERE a.AFTER_SALES_GOODS_ID=#{afterSalesGoodsId,jdbcType=INTEGER} LIMIT 1
  </select>
  <select id="getAfterSalesDetailByAfterSalesId"
          resultType="com.vedeng.aftersales.model.vo.AfterSalesDetailVo">
    select
      a.*,
      b.SUBJECT_TYPE,
      b.IS_NEW,
      b.`TYPE` as afterType,
    c.TRADER_NAME
    from T_AFTER_SALES_DETAIL a
    left join T_AFTER_SALES b on a.AFTER_SALES_ID = b.AFTER_SALES_ID
    left join T_TRADER c on a.TRADER_ID = c.TRADER_ID
    where 1=1
      and a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
  <select id="getAfterSalesInfoByAfterSalesId"
          resultType="com.vedeng.aftersales.model.vo.AfterSalesDetailVo">
   select
    a.SERVICE_AMOUNT,
    a.REFUND_AMOUNT_STATUS,
    b.AFTER_SALES_ID,
    b.AFTER_SALES_NO,
    b.ORDER_ID,
    b.ORDER_NO,
    b.SUBJECT_TYPE,
    b.TYPE as afterType,a.INVOICE_TYPE
    from T_AFTER_SALES_DETAIL a
    left join T_AFTER_SALES b on a.AFTER_SALES_ID = b.AFTER_SALES_ID
    where 1=1
    and a.AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER}
  </select>
</mapper>