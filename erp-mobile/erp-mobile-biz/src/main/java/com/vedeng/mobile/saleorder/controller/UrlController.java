package com.vedeng.mobile.saleorder.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.ModelAndView;

/**
 * 合同预览
 */
@Slf4j
@Controller
public class UrlController {

    @RequestMapping("/mobile/url/display")
    public ModelAndView display(@RequestParam("fileUrl") String fileUrl) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("url", fileUrl);
        mv.setViewName("url_display");
        return mv;
    }

}
