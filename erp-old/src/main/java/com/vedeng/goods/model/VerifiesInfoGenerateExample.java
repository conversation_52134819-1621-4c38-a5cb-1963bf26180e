package com.vedeng.goods.model;

import java.util.ArrayList;
import java.util.List;

public class VerifiesInfoGenerateExample {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected String orderByClause;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected boolean distinct;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected List<Criteria> oredCriteria;

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public VerifiesInfoGenerateExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public String getOrderByClause() {
        return orderByClause;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public boolean isDistinct() {
        return distinct;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andVerifiesInfoIdIsNull() {
            addCriterion("VERIFIES_INFO_ID is null");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdIsNotNull() {
            addCriterion("VERIFIES_INFO_ID is not null");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdEqualTo(Integer value) {
            addCriterion("VERIFIES_INFO_ID =", value, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdNotEqualTo(Integer value) {
            addCriterion("VERIFIES_INFO_ID <>", value, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdGreaterThan(Integer value) {
            addCriterion("VERIFIES_INFO_ID >", value, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("VERIFIES_INFO_ID >=", value, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdLessThan(Integer value) {
            addCriterion("VERIFIES_INFO_ID <", value, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdLessThanOrEqualTo(Integer value) {
            addCriterion("VERIFIES_INFO_ID <=", value, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdIn(List<Integer> values) {
            addCriterion("VERIFIES_INFO_ID in", values, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdNotIn(List<Integer> values) {
            addCriterion("VERIFIES_INFO_ID not in", values, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdBetween(Integer value1, Integer value2) {
            addCriterion("VERIFIES_INFO_ID between", value1, value2, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andVerifiesInfoIdNotBetween(Integer value1, Integer value2) {
            addCriterion("VERIFIES_INFO_ID not between", value1, value2, "verifiesInfoId");
            return (Criteria) this;
        }

        public Criteria andRelateTableIsNull() {
            addCriterion("RELATE_TABLE is null");
            return (Criteria) this;
        }

        public Criteria andRelateTableIsNotNull() {
            addCriterion("RELATE_TABLE is not null");
            return (Criteria) this;
        }

        public Criteria andRelateTableEqualTo(String value) {
            addCriterion("RELATE_TABLE =", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableNotEqualTo(String value) {
            addCriterion("RELATE_TABLE <>", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableGreaterThan(String value) {
            addCriterion("RELATE_TABLE >", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableGreaterThanOrEqualTo(String value) {
            addCriterion("RELATE_TABLE >=", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableLessThan(String value) {
            addCriterion("RELATE_TABLE <", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableLessThanOrEqualTo(String value) {
            addCriterion("RELATE_TABLE <=", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableLike(String value) {
            addCriterion("RELATE_TABLE like", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableNotLike(String value) {
            addCriterion("RELATE_TABLE not like", value, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableIn(List<String> values) {
            addCriterion("RELATE_TABLE in", values, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableNotIn(List<String> values) {
            addCriterion("RELATE_TABLE not in", values, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableBetween(String value1, String value2) {
            addCriterion("RELATE_TABLE between", value1, value2, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableNotBetween(String value1, String value2) {
            addCriterion("RELATE_TABLE not between", value1, value2, "relateTable");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyIsNull() {
            addCriterion("RELATE_TABLE_KEY is null");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyIsNotNull() {
            addCriterion("RELATE_TABLE_KEY is not null");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyEqualTo(Integer value) {
            addCriterion("RELATE_TABLE_KEY =", value, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyNotEqualTo(Integer value) {
            addCriterion("RELATE_TABLE_KEY <>", value, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyGreaterThan(Integer value) {
            addCriterion("RELATE_TABLE_KEY >", value, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyGreaterThanOrEqualTo(Integer value) {
            addCriterion("RELATE_TABLE_KEY >=", value, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyLessThan(Integer value) {
            addCriterion("RELATE_TABLE_KEY <", value, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyLessThanOrEqualTo(Integer value) {
            addCriterion("RELATE_TABLE_KEY <=", value, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyIn(List<Integer> values) {
            addCriterion("RELATE_TABLE_KEY in", values, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyNotIn(List<Integer> values) {
            addCriterion("RELATE_TABLE_KEY not in", values, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyBetween(Integer value1, Integer value2) {
            addCriterion("RELATE_TABLE_KEY between", value1, value2, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andRelateTableKeyNotBetween(Integer value1, Integer value2) {
            addCriterion("RELATE_TABLE_KEY not between", value1, value2, "relateTableKey");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeIsNull() {
            addCriterion("VERIFIES_TYPE is null");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeIsNotNull() {
            addCriterion("VERIFIES_TYPE is not null");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeEqualTo(Integer value) {
            addCriterion("VERIFIES_TYPE =", value, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeNotEqualTo(Integer value) {
            addCriterion("VERIFIES_TYPE <>", value, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeGreaterThan(Integer value) {
            addCriterion("VERIFIES_TYPE >", value, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("VERIFIES_TYPE >=", value, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeLessThan(Integer value) {
            addCriterion("VERIFIES_TYPE <", value, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeLessThanOrEqualTo(Integer value) {
            addCriterion("VERIFIES_TYPE <=", value, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeIn(List<Integer> values) {
            addCriterion("VERIFIES_TYPE in", values, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeNotIn(List<Integer> values) {
            addCriterion("VERIFIES_TYPE not in", values, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeBetween(Integer value1, Integer value2) {
            addCriterion("VERIFIES_TYPE between", value1, value2, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andVerifiesTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("VERIFIES_TYPE not between", value1, value2, "verifiesType");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameIsNull() {
            addCriterion("LAST_VERIFY_USERNAME is null");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameIsNotNull() {
            addCriterion("LAST_VERIFY_USERNAME is not null");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameEqualTo(String value) {
            addCriterion("LAST_VERIFY_USERNAME =", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameNotEqualTo(String value) {
            addCriterion("LAST_VERIFY_USERNAME <>", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameGreaterThan(String value) {
            addCriterion("LAST_VERIFY_USERNAME >", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("LAST_VERIFY_USERNAME >=", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameLessThan(String value) {
            addCriterion("LAST_VERIFY_USERNAME <", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameLessThanOrEqualTo(String value) {
            addCriterion("LAST_VERIFY_USERNAME <=", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameLike(String value) {
            addCriterion("LAST_VERIFY_USERNAME like", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameNotLike(String value) {
            addCriterion("LAST_VERIFY_USERNAME not like", value, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameIn(List<String> values) {
            addCriterion("LAST_VERIFY_USERNAME in", values, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameNotIn(List<String> values) {
            addCriterion("LAST_VERIFY_USERNAME not in", values, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameBetween(String value1, String value2) {
            addCriterion("LAST_VERIFY_USERNAME between", value1, value2, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andLastVerifyUsernameNotBetween(String value1, String value2) {
            addCriterion("LAST_VERIFY_USERNAME not between", value1, value2, "lastVerifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameIsNull() {
            addCriterion("VERIFY_USERNAME is null");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameIsNotNull() {
            addCriterion("VERIFY_USERNAME is not null");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameEqualTo(String value) {
            addCriterion("VERIFY_USERNAME =", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameNotEqualTo(String value) {
            addCriterion("VERIFY_USERNAME <>", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameGreaterThan(String value) {
            addCriterion("VERIFY_USERNAME >", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameGreaterThanOrEqualTo(String value) {
            addCriterion("VERIFY_USERNAME >=", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameLessThan(String value) {
            addCriterion("VERIFY_USERNAME <", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameLessThanOrEqualTo(String value) {
            addCriterion("VERIFY_USERNAME <=", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameLike(String value) {
            addCriterion("VERIFY_USERNAME like", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameNotLike(String value) {
            addCriterion("VERIFY_USERNAME not like", value, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameIn(List<String> values) {
            addCriterion("VERIFY_USERNAME in", values, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameNotIn(List<String> values) {
            addCriterion("VERIFY_USERNAME not in", values, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameBetween(String value1, String value2) {
            addCriterion("VERIFY_USERNAME between", value1, value2, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andVerifyUsernameNotBetween(String value1, String value2) {
            addCriterion("VERIFY_USERNAME not between", value1, value2, "verifyUsername");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("`STATUS` is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("`STATUS` is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("`STATUS` =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("`STATUS` <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("`STATUS` >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("`STATUS` <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("`STATUS` <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("`STATUS` in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("`STATUS` not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("`STATUS` not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNull() {
            addCriterion("ADD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andAddTimeIsNotNull() {
            addCriterion("ADD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andAddTimeEqualTo(Long value) {
            addCriterion("ADD_TIME =", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotEqualTo(Long value) {
            addCriterion("ADD_TIME <>", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThan(Long value) {
            addCriterion("ADD_TIME >", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME >=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThan(Long value) {
            addCriterion("ADD_TIME <", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeLessThanOrEqualTo(Long value) {
            addCriterion("ADD_TIME <=", value, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeIn(List<Long> values) {
            addCriterion("ADD_TIME in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotIn(List<Long> values) {
            addCriterion("ADD_TIME not in", values, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andAddTimeNotBetween(Long value1, Long value2) {
            addCriterion("ADD_TIME not between", value1, value2, "addTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNull() {
            addCriterion("MOD_TIME is null");
            return (Criteria) this;
        }

        public Criteria andModTimeIsNotNull() {
            addCriterion("MOD_TIME is not null");
            return (Criteria) this;
        }

        public Criteria andModTimeEqualTo(Long value) {
            addCriterion("MOD_TIME =", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotEqualTo(Long value) {
            addCriterion("MOD_TIME <>", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThan(Long value) {
            addCriterion("MOD_TIME >", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME >=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThan(Long value) {
            addCriterion("MOD_TIME <", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeLessThanOrEqualTo(Long value) {
            addCriterion("MOD_TIME <=", value, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeIn(List<Long> values) {
            addCriterion("MOD_TIME in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotIn(List<Long> values) {
            addCriterion("MOD_TIME not in", values, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME between", value1, value2, "modTime");
            return (Criteria) this;
        }

        public Criteria andModTimeNotBetween(Long value1, Long value2) {
            addCriterion("MOD_TIME not between", value1, value2, "modTime");
            return (Criteria) this;
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated do_not_delete_during_merge Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    /**
     * This class was generated by MyBatis Generator.
     * This class corresponds to the database table T_VERIFIES_INFO
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}