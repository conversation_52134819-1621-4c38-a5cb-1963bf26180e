package com.vedeng.order.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.common.dto.StepsNodeDto;
import com.common.enums.StepsTypeEnum;
import com.pricecenter.constant.VerifyStatusEnum;
import com.vedeng.activiti.model.AssigneeVo;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.authorization.model.User;
import com.vedeng.common.annotation.MethodLock;
import com.vedeng.common.annotation.SystemControllerLog;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.putHCutil.service.HcSaleorderService;
import com.vedeng.common.util.StringUtil;
import com.vedeng.common.validator.FormToken;
import com.vedeng.erp.buyorder.dto.AuditRecordDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.finance.model.PayApply;
import com.vedeng.finance.service.PayApplyService;
import com.vedeng.goods.service.GoodsApiService;
import com.vedeng.order.service.RiskCheckService;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.wms.service.context.ThreadLocalContext;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.IdentityLinkEntity;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 采购费用单审核流接口 为兼容 审核流还再old模块问题
 * @date 2022/8/24 14:23
 **/
@Controller
@RequestMapping("/old/buyorderExpense")
@Slf4j
public class BuyOrderExpenseVerifyController {

    public static final String processKey = "buyorderExpenseVerify";
    public static final String T_BUYORDER_EXPENSE = "T_BUYORDER_EXPENSE";
    public static final String BUYORDER_EXPENSE_ID = "BUYORDER_EXPENSE_ID";
    public static final String VALID_STATUS = "VALID_STATUS";
    public static final String STRING = "开始";
    public static final String PRODUCT_AUDIT = "产品经理或助理审核";
    public static final String END = "结束";


    @Autowired
    private BuyorderExpenseApiService buyorderExpenseApiService;

    @Autowired // 自动装载
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private PayApplyService payApplyService;

    @Resource
    private UserService userService;


    @Autowired
    private RiskCheckService riskCheckService;


    @Autowired
    @Qualifier("actionProcdefService")
    private ActionProcdefService actionProcdefService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;


    @Autowired
    @Qualifier("hcSaleorderService")
    protected HcSaleorderService hcSaleorderService;

    @Autowired
    private GoodsApiService goodsApiService;

    /**
     * 采购费用单 按钮
     * @param buyorderExpenseId 采购费用单id
     * @return
     */
    @RequestMapping("/getButton")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<String> getButton(Integer buyorderExpenseId) {
        // add by hollis 2022/8/30 9:23 11912 start
        // 11打印预览 12申请审核 13编辑订单 14关闭订单 15审核通过 16审核不通过 17下载合同 18申请付款 19订单完结 20付款审核通过 21付款审核不通过
        List<Integer> result = new ArrayList<>();
        BuyorderExpenseDto orderMainData = buyorderExpenseApiService.getOrderMainData(buyorderExpenseId);
        if (Objects.isNull(orderMainData) || Objects.isNull(orderMainData.getOrderType()) || orderMainData.getOrderType().equals(0)) {
            return R.success("", "");
        }
        Integer checkUser = buyorderExpenseApiService.checkUser(buyorderExpenseId);
        Map<String, Object> stringObjectMap = bindProductManage(buyorderExpenseId);
        Task taskInfo = (Task) stringObjectMap.get("taskInfo");
        Map<String, Object> candidateUserMap = (Map) stringObjectMap.get("candidateUserMap");
        // 节点对应规则: 1待确认、2审核中、3待付款、5待收票、6已完结、7已关闭
        if (orderMainData.getStatus() == 0) {
            if (orderMainData.getAuditStatus() != null && orderMainData.getAuditStatus() == 1) {
                // 审核中
                if (checkUser == 1 || checkUser == 2) {
                    result.add(11);
                }

                if ((taskInfo != null && taskInfo.getProcessInstanceId() != null && taskInfo.getAssignee() != null) || (taskInfo != null && taskInfo.getId() != null && null != candidateUserMap.get(taskInfo.getId()))) {
                    Boolean belong = (Boolean) candidateUserMap.get("belong");
                    if (PRODUCT_AUDIT.equals(taskInfo.getName())) {
                        List<AuditRecordDto> auditRecordDtos = this.bindAuditRecordData(stringObjectMap);
                        if (CollUtil.isNotEmpty(auditRecordDtos)) {
                            AuditRecordDto auditRecordDto = auditRecordDtos.get(auditRecordDtos.size() - 1);
                            if (auditRecordDto.getOperator().toLowerCase(Locale.ROOT).contains(CurrentUser.getCurrentUser().getUsername().toLowerCase(Locale.ROOT))) {
                                belong = true;
                            }
                        }
                    }
                    if (CurrentUser.getCurrentUser().getUsername().equals(taskInfo.getAssignee()) || (belong != null) && belong) {
                        result.add(11);
                        result.add(15);
                        result.add(16);
                    }
                }
            } else {
                // 待确认
                // 1打印预览 2申请审核 3编辑订单 4关闭订单 5审核通过 6审核不通过 7下载合同 8申请付款
                if (checkUser == 1 || checkUser == 2) {
                    result.add(11);
                    result.add(12);
                    result.add(13);
                    result.add(14);
                }
            }
        } else if (orderMainData.getStatus() == 1) {
            // 付款状态 1:全部付款 0: 部分/未付款
            int paymentStatus = orderMainData.getPaymentStatus() == 2 ? 1 : 0;
            // 收票状态 8:全部收票 0:部分/未收票
            int invoiceStatus = orderMainData.getInvoiceStatus() == 2 ? 8 : 0;
            // 1打印预览 2申请审核 3编辑订单 4关闭订单 5审核通过 6审核不通过 7下载合同 8申请付款
            if (paymentStatus + invoiceStatus != 9) {
                if (checkUser == 1 || checkUser == 2) {
                    if (StringUtils.isEmpty(orderMainData.getBuyorderExpenseDetailDto().getContractUrl())) {
                        result.add(11);
                    } else {
                        result.add(17);
                    }
                    if (paymentStatus == 0 && orderMainData.getLockedStatus() == 0) {
                        result.add(18);
                    }
                }
            }
            if (paymentStatus + invoiceStatus == 9) {
                if (StringUtils.isEmpty(orderMainData.getBuyorderExpenseDetailDto().getContractUrl())) {
                    result.add(11);
                } else {
                    result.add(17);
                }
                result.add(19);
            }
            // 关闭按钮未付款的时候可以显示
            if (orderMainData.getPaymentStatus() == 0 && orderMainData.getLockedStatus() == 0) {
                result.add(14);
            }

            // 付款审核按钮
            if (orderMainData.getPaymentStatus() == 0) {
                PayApply payApply = new PayApply();
                payApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
                // 采购费用单付款申请
                payApply.setPayType(4125);
                payApply.setRelatedId(buyorderExpenseId);
                List<PayApply> payApplyList = payApplyService.getPayApplyList(payApply);
                if (CollectionUtils.isNotEmpty(payApplyList)) {
                    Map<String, Object> expenseHistoricInfoPay = actionProcdefService.getHistoric(processEngine, "paymentVerify_" + payApplyList.get(0).getPayApplyId());
                    Task expenseTaskInfoPay = (Task) expenseHistoricInfoPay.get("taskInfo");
                    Map<String,Object> expenseCandidateUserMapPay = (Map)expenseHistoricInfoPay.get("candidateUserMap");
                    if (((expenseTaskInfoPay != null && expenseTaskInfoPay.getProcessInstanceId() != null && expenseTaskInfoPay.getAssignee() != null) || expenseCandidateUserMapPay.get(expenseTaskInfoPay != null ? expenseTaskInfoPay.getId() : null) != null) && "供应链产品总监".equals(expenseHistoricInfoPay.get("endStatus"))) {
                        Boolean belong = (Boolean) expenseCandidateUserMapPay.get("belong");
                        List<IdentityLinkEntity> userList = (List<IdentityLinkEntity>) expenseCandidateUserMapPay.get(expenseTaskInfoPay != null ? expenseTaskInfoPay.getId() : null);
                            if (CollUtil.isNotEmpty(userList)) {
                                for (IdentityLinkEntity identityLinkEntity : userList) {
                                    if (CurrentUser.getCurrentUser().getUsername().equals(identityLinkEntity.getUserId())){
                                        belong = true;
                                        break;
                                    }
                                }
                            }
                        if (CurrentUser.getCurrentUser().getUsername().equals(expenseTaskInfoPay != null ? expenseTaskInfoPay.getAssignee() : null) || (belong != null) && belong) {
                            result.add(20);
                            result.add(21);
                        }
                    }
                }
            }
        } else if (orderMainData.getStatus() == 2) {
            if (StringUtils.isEmpty(orderMainData.getBuyorderExpenseDetailDto().getContractUrl())) {
                result.add(11);
            } else {
                result.add(17);
            }
        } else {
            return R.success("", "");
        }
        return R.success("", result.stream().map(String::valueOf).collect(Collectors.joining(",")));
        // add by hollis 2022/8/30 9:23 11912 end
    }

    /**
     * 加上当前产品的产品归属
     * @param buyorderExpenseId
     * @return
     */
    private Map<String, Object> bindProductManage(Integer buyorderExpenseId) {
        Map<String, Object> stringObjectMap = this.buyorderCheckStatus(buyorderExpenseId);
        List<String> skus = buyorderExpenseApiService.getProductManageAndAsistNameList(buyorderExpenseId);
        List<com.vedeng.goods.dto.ProductManageAndAsistDto> productManageAndAsistDtos = goodsApiService.batchQueryProductManageAndAsist(skus);
        List<String> productManageAndAsistNameList = getProductManageAndAsistNameList(productManageAndAsistDtos);
        stringObjectMap.put("manageAndAsistName", productManageAndAsistNameList);
        return stringObjectMap;
    }


    /**
     * 获取采购费用单审核记录
     * @param buyorderExpenseId
     * @return
     */
    @RequestMapping("/getBuyorderExpenseCheckStatus")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getBuyorderExpenseCheckStatus(Integer buyorderExpenseId) {

        Map<String, Object> stringObjectMap = bindProductManage(buyorderExpenseId);
        // 所有的审核记录
        List<AuditRecordDto> auditRecordDtos = this.bindAuditRecordData(stringObjectMap);
        // 审核进度条
        List<StepsNodeDto> lastAudit = getLastAudit(auditRecordDtos);

        Task taskInfo = (Task) stringObjectMap.get("taskInfo");

        Map<String, Object> result = new HashMap<>();
        result.put("auditRecordDtos", auditRecordDtos);
        result.put("taskInfo", taskInfo == null ? 0 : taskInfo.getId());
        result.put("lastAudit", lastAudit);

        PayApply payApply = new PayApply();
        payApply.setCompanyId(ErpConst.NJ_COMPANY_ID);
        // 采购费用单付款申请
        payApply.setPayType(4125);
        payApply.setRelatedId(buyorderExpenseId);
        List<PayApply> payApplyList = payApplyService.getPayApplyList(payApply);
        if (CollectionUtils.isNotEmpty(payApplyList)) {
            Map<String, Object> expenseHistoricInfoPay = actionProcdefService.getHistoric(processEngine, "paymentVerify_" + payApplyList.get(0).getPayApplyId());
            Task expenseTaskInfoPay = (Task) expenseHistoricInfoPay.get("taskInfo");
            result.put("payApplyTaskId", expenseTaskInfoPay == null ? 0 : expenseTaskInfoPay.getId());
        }
        return R.success(result);
    }

    /**
     * 费用单审核进度条
     * @param auditRecordDtos 审核记录
     * @return 进度条
     */
    private List<StepsNodeDto> getLastAudit(List<AuditRecordDto> auditRecordDtos) {


        List<StepsNodeDto> result = new ArrayList<>();
        List<AuditRecordDto> lastRecord = new ArrayList<>();

        List<AuditRecordDto> reverse = CollUtil.reverseNew(auditRecordDtos);
        // 倒序拿到最后一次发起的审核
        for (AuditRecordDto auditRecordDto:reverse){
            lastRecord.add(auditRecordDto);
            if (STRING.equals(auditRecordDto.getOperation())) {
                break;
            }
        }

        // 数据封装
        if (CollUtil.isNotEmpty(lastRecord)) {
            Optional<AuditRecordDto> activeFirst = lastRecord.stream().filter(c -> Objects.isNull(c.getOperationTime())).findFirst();
            List<AuditRecordDto> reverseRecord = CollUtil.reverse(lastRecord);
            for (AuditRecordDto audit : reverseRecord) {
                if ("驳回".equals(audit.getOperation())) {
                    if (result.size() > 0) {
                        StepsNodeDto stepsNodeDto = result.get(result.size() - 1);
                        stepsNodeDto.setType(StepsTypeEnum.error.getType());
                    }
                    continue;
                }
                StepsNodeDto stepsNodeDto = bindStepsNode(StepsTypeEnum.success, audit);
                result.add(stepsNodeDto);
            }
            if (activeFirst.isPresent()) {
                StepsNodeDto stepsNodeDto = result.get(result.size() - 1);
                stepsNodeDto.setType(StepsTypeEnum.process.getType());
                if (stepsNodeDto.getTitle().equals("产品助理或经理审核")) {
                    result.add(StepsNodeDto.builder().title("供应主管审核").type(StepsTypeEnum.wait.getType()).build());
                    result.add(StepsNodeDto.builder().title("审核完成").type(StepsTypeEnum.success.getType()).build());
                }
                if (stepsNodeDto.getTitle().equals("供应主管审核")) {
                    result.add(StepsNodeDto.builder().title("审核完成").type(StepsTypeEnum.wait.getType()).build());
                }
            }

        } else {
            StepsNodeDto start = StepsNodeDto.builder().title("开始审核").type(StepsTypeEnum.wait.getType()).build();
            StepsNodeDto apply = StepsNodeDto.builder().title("申请人").type(StepsTypeEnum.wait.getType()).build();
            StepsNodeDto product = StepsNodeDto.builder().title("产品助理或经理审核").type(StepsTypeEnum.wait.getType()).build();
            StepsNodeDto director = StepsNodeDto.builder().title("供应主管审核").type(StepsTypeEnum.wait.getType()).build();
            StepsNodeDto end = StepsNodeDto.builder().title("审核完成").type(StepsTypeEnum.wait.getType()).build();
            result.add(start);
            result.add(apply);
            result.add(product);
            result.add(director);
            result.add(end);
        }

        return result;

    }

    private StepsNodeDto bindStepsNode( StepsTypeEnum stepsTypeEnum, AuditRecordDto recordDto) {
        StepsNodeDto nodeDto = StepsNodeDto.builder().title(recordDto.getOperation()).type(stepsTypeEnum.getType()).build();
        List<String> data = new ArrayList<>(8);
        data.add(recordDto.operator);
        data.add(DateUtil.format(recordDto.operationTime, DatePattern.NORM_DATETIME_PATTERN));
        data.add(recordDto.getRemark());
        nodeDto.setDescriptions(data);
        return nodeDto;
    }

    /**
     * 订单审核记录封装
     * @param data
     * @return
     */
    private List<AuditRecordDto> bindAuditRecordData(Map<String, Object> data) {
        List<AuditRecordDto> result = new ArrayList<>();

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) data.get("historicActivityInstance");

        String startUser = (String) data.get("startUser");
        String verifyUsers = (String) data.get("verifyUsers");
        ArrayList<AssigneeVo> assigneeVos = (ArrayList<AssigneeVo>) data.get("assigneeVos");
        Map commentMap = (Map) data.get("commentMap");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)) {
            for (int i = 0; i < historicActivityInstance.size(); i++) {
                HistoricActivityInstance c = historicActivityInstance.get(i);

                // 操作人
                if (StringUtils.isNotEmpty(c.getActivityName())) {
                    AuditRecordDto auditRecordDto = new AuditRecordDto();
                    if ("startEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator(startUser==null?"":startUser);
                    } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator("");
                    } else {

                        if (Objects.isNull(c.getEndTime())) {
                            if (!PRODUCT_AUDIT.equals(c.getActivityName())) {
                                auditRecordDto.setOperator(verifyUsers);
                            } else {
                                auditRecordDto.setOperator(verifyUsers);
                            }
                        } else {
                            auditRecordSetOperator(assigneeVos, c, auditRecordDto);

                        }
                    }

                    // 时间
                    auditRecordDtoBindOpertationAndRemarkAndTime(result, commentMap, c, auditRecordDto);
                }
            }
        }

        return doTinyAuditRecord(result);
    }

    static void auditRecordSetOperator(ArrayList<AssigneeVo> assigneeVos, HistoricActivityInstance c, AuditRecordDto auditRecordDto) {
        for (AssigneeVo assigneeVo : assigneeVos) {
            if (StringUtils.isNotEmpty(assigneeVo.getAssignee())) {
                if (assigneeVo.getAssignee().equals(c.getAssignee())) {
                    auditRecordDto.setOperator(assigneeVo.getRealName());
                }
            }
        }
        if (StringUtils.isEmpty(auditRecordDto.getOperator())) {
            auditRecordDto.setOperator("");
        }
    }

    /**
     * 过滤 parallel 的多个待处理task 以及 驳回后导致的多个 驳回记录
     * @param data
     * @return
     */
    private List<AuditRecordDto> doTinyAuditRecord(List<AuditRecordDto> data) {

        List<List<AuditRecordDto>> groupResult = new ArrayList<>();
        List<AuditRecordDto> result = new ArrayList<>();
        List<AuditRecordDto> itemRecords = new ArrayList<>();

        for (AuditRecordDto recordDto : data) {
            if (STRING.equals(recordDto.operation)) {
                if (CollUtil.isNotEmpty(itemRecords)) {
                    groupResult.add(itemRecords);
                    itemRecords = new ArrayList<>();
                }
            }
            if (PRODUCT_AUDIT.equals(recordDto.getOperation())) {

                Optional<AuditRecordDto> first = itemRecords.stream().filter(c -> PRODUCT_AUDIT.equals(c.getOperation()) && c.operator.equalsIgnoreCase(recordDto.getOperator())).findFirst();
                if (first.isPresent()) {
                    continue;
                }
            }
            itemRecords.add(recordDto);
        }
        if (CollUtil.isNotEmpty(itemRecords)) {
            List<AuditRecordDto> collect = itemRecords.stream().sorted(Comparator.comparing(AuditRecordDto::getOperationTime, Comparator.nullsLast(Date::compareTo))).collect(Collectors.toList());
            groupResult.add(collect);
            itemRecords = null;
        }


        for (int i = 0; i < groupResult.size(); i++) {
            List<AuditRecordDto>  itemRecord = groupResult.get(i);
            result.addAll(itemRecord);
        }

        return result;
    }

    /**
     * 获取付款申请审核记录
     * @param payApplyId
     * @return
     */
    @RequestMapping("/getBuyorderExpensePaymentCheckStatus")
    @ResponseBody
    @NoNeedAccessAuthorization
    public R<?> getBuyorderExpensePaymentCheckStatus(Integer payApplyId) {

        Map<String, Object> stringObjectMap = this.buyorderPaymentCheckStatus(payApplyId);

        List<AuditRecordDto> auditRecordDtos = this.bindPaymentAuditRecordData(stringObjectMap);
        Task taskInfo = (Task) stringObjectMap.get("taskInfo");

        Map<String, Object> result = new HashMap<>();
        result.put("paymentAuditRecordDtos", auditRecordDtos);
        result.put("paymentTaskInfo", taskInfo==null?0:taskInfo.getId());
        return R.success(result);

    }

    /**
     * 付款审核记录封装
     * @param data
     * @return
     */
    private List<AuditRecordDto> bindPaymentAuditRecordData(Map<String, Object> data) {
        List<AuditRecordDto> result = new ArrayList<>();

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>) data.get("historicActivityInstancePay");

        String startUser = (String) data.get("startUserPay");
        String verifyUsers = (String) data.get("verifyUsersPay");
        Map commentMap = (Map) data.get("commentMapPay");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)) {
            for (int i = 0; i < historicActivityInstance.size(); i++) {
                HistoricActivityInstance c = historicActivityInstance.get(i);

                // 操作人
                if (StringUtils.isNotEmpty(c.getActivityName())) {
                    AuditRecordDto auditRecordDto = new AuditRecordDto();
                    if ("startEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator(startUser==null?"":startUser);
                    } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
                        auditRecordDto.setOperator("");
                    } else {
                        if (i == historicActivityInstance.size() - 1) {
                            auditRecordDto.setOperator(verifyUsers);
                        } else {
                            auditRecordDto.setOperator(c.getAssignee());
                            if (StringUtils.isEmpty(auditRecordDto.getOperator())) {
                                auditRecordDto.setOperator("");
                            }
                        }
                    }
                    auditRecordDtoBindOpertationAndRemarkAndTime(result, commentMap, c, auditRecordDto);

                }
            }
        }
        return result;
    }

    private void auditRecordDtoBindOpertationAndRemarkAndTime(List<AuditRecordDto> result, Map commentMap, HistoricActivityInstance c, AuditRecordDto auditRecordDto) {
        // 时间
        auditRecordDto.setOperationTime(c.getEndTime());

        // 操作
        if ("startEvent".equals(c.getActivityType())) {
            auditRecordDto.setOperation(STRING);
        } else if ("intermediateThrowEvent".equals(c.getActivityType())) {
            auditRecordDto.setOperation(END);
        } else {
            auditRecordDto.setOperation(c.getActivityName() == null ? "" : c.getActivityName());
        }
        String remark = "";

        // 备注
        if (Objects.nonNull(c.getTaskId())&&CollUtil.isNotEmpty(commentMap)) {
            remark= (String) commentMap.get(c.getTaskId());
        }

        auditRecordDto.setRemark(remark == null ? "" : remark);
        result.add(auditRecordDto);
    }

    private Map<String, Object> buyorderPaymentCheckStatus(Integer payApplyId) {

        Map<String, Object> result = new HashMap<>();
        if (payApplyId == null) {
            return result;
        }

        Map<String, Object> historicInfoPay = actionProcdefService.getHistoric(processEngine,
                "paymentVerify_" + payApplyId);
        Task taskInfoPay = (Task) historicInfoPay.get("taskInfo");
        result.put("taskInfoPay", taskInfoPay);
        result.put("startUserPay", historicInfoPay.get("startUser"));
        // 最后审核状态
        result.put("endStatusPay", historicInfoPay.get("endStatus"));
        result.put("historicActivityInstancePay", historicInfoPay.get("historicActivityInstance"));
        result.put("commentMapPay", historicInfoPay.get("commentMap"));
        result.put("candidateUserMapPay", historicInfoPay.get("candidateUserMap"));
        // 当前审核人
        String verifyUsersPay = null;
        if (null != taskInfoPay) {
            Map<String, Object> taskInfoVariablesPay = actionProcdefService.getVariablesMap(taskInfoPay);
            verifyUsersPay = (String) taskInfoVariablesPay.get("verifyUsers");
        }
        // 当前付款审核人
        result.put("verifyUsersPay", verifyUsersPay);
        return result;
    }


    private Map<String, Object> buyorderCheckStatus( Integer buyorderExpenseId) {
        Map<String, Object> result = new HashMap<>();
        if (buyorderExpenseId == null) {
            return result;
        }

        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                processKey + "_" + buyorderExpenseId);
        result.put("taskInfo", historicInfo.get("taskInfo"));
        result.put("startUser", historicInfo.get("startUser") != null ?
                getRealNameByUserName(historicInfo.get("startUser").toString()) : null);
        result.put("candidateUserMap", historicInfo.get("candidateUserMap"));
        // 最后审核状态
        result.put("endStatus", historicInfo.get("endStatus"));

        List<HistoricActivityInstance> historicActivityInstance = setAssignRealNames(result, historicInfo);

        boolean permoissionsFlag = false;
        if (historicInfo != null && historicInfo.get("startUser") != null) {
            User startUser = userService.getByUsername(historicInfo.get("startUser").toString(), 1);
            permoissionsFlag = startUser != null ? riskCheckService.isOrgFlag(startUser, ErpConst.QUALITY_ORG) : false;
        }
        result.put("permoissionsFlag", permoissionsFlag);
        result.put("historicActivityInstance", historicActivityInstance);
        Object commentMap = historicInfo.get("commentMap");
        result.put("commentMap", commentMap);
        Task taskInfoPay = (Task) historicInfo.get("taskInfo");
        String verifyUsersPay = null;
        if (null != taskInfoPay) {

            Map candidateUserMap = (Map) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfoPay.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());

                verifyUsersPay = StringUtils.join(userNameList, ",");

            }
            // 会签Parallel 节点的时候 处理待审核人
            if (taskInfoPay.getName().equals(PRODUCT_AUDIT)) {
                List<Task> tasks = processEngine.getTaskService().createTaskQuery().taskName(PRODUCT_AUDIT).processInstanceId(taskInfoPay.getProcessInstanceId()).list();
                Set<String> producetName = new HashSet<>();
                tasks.stream().forEach(c -> {
                    List<IdentityLink> data = processEngine.getTaskService().getIdentityLinksForTask(c.getId());
                    if (data != null && data.size() > 0) {
                        for (IdentityLink identityLink : data) {
                            producetName.add(identityLink.getUserId());
                        }
                    }
                });
                verifyUsersPay = StringUtils.join(producetName, ",");
            }
        }
        String verifyUsers = getVerifyUserRealNames(verifyUsersPay);
        result.put("verifyUsers",verifyUsers );

        return result;

    }

    /**
     * 获取多个审核人员真实姓名
     *
     * @param verifyUsers
     * @return
     */
    protected String getVerifyUserRealNames(String verifyUsers){
        if (verifyUsers == null){
            return null;
        }
        List<String> userNames = Arrays.stream(verifyUsers.split(","))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userNames)){
            return verifyUsers;
        }
        StringBuffer realNames = new StringBuffer();
        userNames.forEach(userName -> {
            realNames.append(getRealNameByUserName(userName)).append(",");
        });
        realNames.deleteCharAt(realNames.length() - 1);
        return  realNames.toString();
    }

    /**
     * 根据用户名获取真实姓名 xxx（xx）
     * @param userName
     * @return
     */
    public String getRealNameByUserName(String userName){
        return userService.getRealNameByUserName(userName);
    }

    /**
     * 设置工作流审核人全称
     *
     * @param result
     * @param historicInfo
     * @return
     */
    protected List<HistoricActivityInstance> setAssignRealNames(Map<String,Object> result, Map<String, Object> historicInfo) {
        ArrayList<AssigneeVo> assigneeVos = new ArrayList<>() ;

        List<HistoricActivityInstance> historicActivityInstance = (List<HistoricActivityInstance>)historicInfo.get("historicActivityInstance");
        if (CollectionUtils.isNotEmpty(historicActivityInstance)){
            for (HistoricActivityInstance historicActivityInstanceInfo : historicActivityInstance) {
                if (StringUtil.isBlank(historicActivityInstanceInfo.getAssignee())){
                    continue;
                }
                AssigneeVo assigneeVo = new AssigneeVo();
                assigneeVo.setAssignee(historicActivityInstanceInfo.getAssignee());
                assigneeVo.setRealName(getRealNameByUserName(historicActivityInstanceInfo.getAssignee()));
                assigneeVos.add(assigneeVo);
            }
        }
        ArrayList<AssigneeVo> assigneeVosResult = assigneeVos.stream()
                .filter(assigneeVo -> StringUtil.isNotBlank(assigneeVo.getAssignee()))
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(AssigneeVo::getAssignee))), ArrayList::new));
        result.put("assigneeVos", assigneeVosResult);
        return historicActivityInstance;
    }


    /**
     * 采购单审核页
     */
    @FormToken(save = true)
    @ResponseBody
    @RequestMapping(value = "/complement")
    @NoNeedAccessAuthorization
    public ModelAndView complement(String taskId, Boolean pass, Integer type, Integer buyorderExpenseId) {
        ModelAndView mv = new ModelAndView();

        //防止多个用户点击审核操作，导致空指针的异常，如果查询不到对应的task，则弹出提示框，刷新父页面
        Task task = processEngine.getTaskService().createTaskQuery().taskId(taskId).singleResult();
        if (task == null) {
            mv.addObject("error_tips", "该采购费用单审核状态已发生变化，将刷新采购单详情页");
            mv.setViewName("order/buyorder/parent_reload");
            return mv;
        }

        mv.addObject("taskId", taskId);
        String taskName = task.getName();
        mv.addObject("pass", pass);
        mv.addObject("type", type);
        if (null != buyorderExpenseId) {
            mv.addObject("buyorderExpenseId", buyorderExpenseId);
        } else {
            mv.addObject("buyorderExpenseId", 0);
        }
        mv.setViewName("order/buyorderexpense/new_complement");
        return mv;
    }


    /**
     * 审核流 发起
     *
     * @param request
     * @param buyorderExpenseDto
     * @param taskId
     * @return
     */
    @MethodLock(field = "buyorderExpenseId", className = BuyorderExpenseDto.class)
    @ResponseBody
    @RequestMapping(value = "/editApplyValidBuyorderExpense")
    @SystemControllerLog(operationType = "edit", desc = "采购订单申请审核")
    @NoNeedAccessAuthorization
    public ResultInfo<?> editApplyValidBuyorder(HttpServletRequest request, BuyorderExpenseDto buyorderExpenseDto, String taskId) {
        User user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
        buyorderExpenseDto.setUpdater(user.getUserId());
        buyorderExpenseDto.setModTime(new Date());
        buyorderExpenseDto.setStatus(null);
        // 不锁锁单
        buyorderExpenseDto.setLockedStatus(0);

        buyorderExpenseApiService.updateBuyorderExpenseStatusByVerity(buyorderExpenseDto);

        try {

            Map<String, Object> variableMap = new HashMap<String, Object>();
            // 查询当前订单的一些状态
            BuyorderExpenseDto buyorderInfo = buyorderExpenseApiService.getOrderMainData(buyorderExpenseDto.getBuyorderExpenseId());
            List<String> skus = buyorderExpenseApiService.getProductManageAndAsistNameList(buyorderExpenseDto.getBuyorderExpenseId());
            List<com.vedeng.goods.dto.ProductManageAndAsistDto> productManageAndAsistDtos = goodsApiService.batchQueryProductManageAndAsist(skus);
            if (CollUtil.isEmpty(getProductManageAndAsistNameList(productManageAndAsistDtos))) {
                throw new ServiceException("当前采购商品没有产品经理或产品助理");
            }
            String processDefinitionKey = getProcessKey(buyorderInfo);

            Map<String, Object> historicInfo3 = actionProcdefService.getHistoric(processEngine,
                    processDefinitionKey + "_" + buyorderExpenseDto.getBuyorderExpenseId());
            if (historicInfo3.get("endStatus") != null) {
                if (!historicInfo3.get("endStatus").equals("驳回")) {
                    return new ResultInfo(-1, ErpConst.APPLY_VALID_ERROR_MESSAGE);
                }
            }

            String orderBelongUser = userService.getUserNameByUserId(buyorderInfo.getCreator());

            // 开始生成流程(如果没有taskId表示新流程需要生成)
            BuyorderExpenseDto data = new BuyorderExpenseDto();
            data.setBuyorderExpenseId(buyorderInfo.getBuyorderExpenseId());
            data.setBuyorderExpenseNo(buyorderInfo.getBuyorderExpenseNo());
            data.setBuyorderId(buyorderInfo.getBuyorderId());
            data.setCreator(buyorderInfo.getCreator());
            if (taskId.equals("0")) {
                variableMap.put("buyorderExpenseInfo", buyorderInfo);
                variableMap.put("currentAssinee", orderBelongUser);
                variableMap.put("relateTableKey", buyorderInfo.getBuyorderExpenseId());
                variableMap.put("relateTable", T_BUYORDER_EXPENSE);
                variableMap.put("orgId", user.getOrgId());
                variableMap.put("processDefinitionKey", processDefinitionKey);
                variableMap.put("businessKey", processDefinitionKey + "_" + buyorderInfo.getBuyorderExpenseId());
                actionProcdefService.createProcessInstance(request, processDefinitionKey,
                        processDefinitionKey + "_" + buyorderInfo.getBuyorderExpenseId(), variableMap);
            }

            // 默认申请人通过
            // 根据BusinessKey获取生成的审核实例
            Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine,
                    processDefinitionKey + "_" + buyorderInfo.getBuyorderExpenseId());
            if (historicInfo.get("endStatus") != "审核完成") {
                Task taskInfo = (Task) historicInfo.get("taskInfo");
                taskId = taskInfo.getId();
                Authentication.setAuthenticatedUserId(user.getUsername());
                Map<String, Object> variables = new HashMap<String, Object>();
                // 设置审核完成监听器回写参数
                variables.put("tableName", T_BUYORDER_EXPENSE);
                variables.put("id", BUYORDER_EXPENSE_ID);
                variables.put("idValue", buyorderInfo.getBuyorderExpenseId());
                variables.put("key", VALID_STATUS);
                variables.put("value", 1);
                variables.put("key1", "LOCKED_STATUS");
                variables.put("value1", 0);
                // 回写数据的表在db中
                variables.put("db", 2);
                // 默认审批通过
                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, "",
                        orderBelongUser, variables);
                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                    //维护verifyStatus主表字段

                }


                // 如果未结束添加审核对应主表的审核状态
                if (!complementStatus.getData().equals("endEvent")) {
                    verifiesRecordService.saveVerifiesInfo(taskId, 0);
                }

            }
            return new ResultInfo(0, "操作成功");
        } catch (Exception e) {
            log.error("editApplyValidBuyorderExpense:", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }

    }

    List<String> getProductManageAndAsistNameList(List<com.vedeng.goods.dto.ProductManageAndAsistDto> productManageAndAsistDtos) {

        Set<String> manageAndAsistNameSet = new HashSet<>();

        productManageAndAsistDtos.forEach(manageAndAsist -> {

            if (StrUtil.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductAssitName());
            }

            if (StrUtil.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameSet.add(manageAndAsist.getProductManageName());
            }
        });

        return new ArrayList<>(manageAndAsistNameSet);
    }
    List<Integer> getProductManageAndAsistIdList(List<com.vedeng.goods.dto.ProductManageAndAsistDto> productManageAndAsistDtos) {

        Set<Integer> manageAndAsistNameIdSet = new HashSet<>();

        productManageAndAsistDtos.forEach(manageAndAsist -> {

            if (StringUtils.isNotEmpty(manageAndAsist.getProductAssitName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductAssitUserId());
            }

            if (StringUtils.isNotEmpty(manageAndAsist.getProductManageName())) {
                manageAndAsistNameIdSet.add(manageAndAsist.getProductManageUserId());
            }

        });

        return new ArrayList<>(manageAndAsistNameIdSet);
    }


    /**
     * 审核流审核操作
     * @param request
     * @param taskId 审核id
     * @param comment 备注
     * @param pass 是否通过
     * @param buyorderExpenseId 采购费用单id
     * @param session
     * @return
     */
    @FormToken(remove = true)
    @ResponseBody
    @RequestMapping(value = "/complementTaskForBuyOrder")
    @SystemControllerLog(operationType = "edit", desc = "采购费用单审核操作")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementTaskForBuyOrder(HttpServletRequest request, String taskId,
                                                   String comment, Boolean pass,
                                                   Integer buyorderExpenseId,
                                                   HttpSession session) {
        //获取session中user信息
        User user = (User) session.getAttribute(ErpConst.CURR_USER);

        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);

        // 审批操作
        try {

            TaskService taskService = processEngine.getTaskService();

            // 使用任务id,获取任务对象，获取流程实例id
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

            if (task == null) {
                return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
            }

            if (PRODUCT_AUDIT.equals(task.getName())) {

                // 获取当前活动节点

                List<Task> tasks = processEngine.getTaskService().createTaskQuery().taskName(PRODUCT_AUDIT).processInstanceId(task.getProcessInstanceId()).list();
                List<Task> collect = tasks.stream().filter(c -> {
                    List<IdentityLink> candidateUserList = taskService.getIdentityLinksForTask(c.getId());
                    if (candidateUserList != null && candidateUserList.size() > 0) {
                        for (IdentityLink identityLink : candidateUserList) {
                            if (identityLink.getUserId().equalsIgnoreCase(user.getUsername())) {
                                return true;
                            }

                        }
                    }
                    return false;
                }).collect(Collectors.toList());

                if (pass) {
                    if (tasks.size() == 1 || (CollUtil.isNotEmpty(collect) && tasks.size() == collect.size())) {
                        taskService.setVariable(taskId, "pass", true);
                        variables.put("passAll", true);
                    }
                    doComplementTask(request, comment, user, variables, collect);
                    return new ResultInfo(0, "操作成功", buyorderExpenseId);
                } else {
                    variables.put("passAll", false);
                    // 采购单申请不通过解锁
                    actionProcdefService.updateInfo(T_BUYORDER_EXPENSE, BUYORDER_EXPENSE_ID, buyorderExpenseId, "STATUS", 0, 2);

                    //如果未结束添加审核对应主表的审核状态
                    if (CollUtil.isNotEmpty(tasks)) {
                        for (int i = 0; i < tasks.size(); i++) {
                            Task task1 = collect.get(0);
                            if (task1.getId().equals(tasks.get(i).getId())) {
                                //当前审核人是谁
                                verifiesRecordService.saveVerifiesInfo(tasks.get(i).getId(), 2);
                                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, tasks.get(i).getId(), comment, user.getUsername(), variables);
                            } else {
                                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, tasks.get(i).getId(), comment==null?"_AUTO":comment+"_AUTO", user.getUsername(), variables);
                            }

                        }

                    }
                    //更新主表审核状态
                    buyorderExpenseApiService.unAudit(buyorderExpenseId);

                    return new ResultInfo(0, "操作成功", buyorderExpenseId);
                }

            }

            if (!pass) {

                // 采购单申请不通过解锁
                actionProcdefService.updateInfo(T_BUYORDER_EXPENSE, BUYORDER_EXPENSE_ID, buyorderExpenseId, "STATUS", 0, 2);

                //如果未结束添加审核对应主表的审核状态
                verifiesRecordService.saveVerifiesInfo(taskId, 2);

                ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);

                //更新主表审核状态
                buyorderExpenseApiService.unAudit(buyorderExpenseId);

                return new ResultInfo(0, "操作成功", buyorderExpenseId);
            }
            //当前审核人是谁
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);

            // 如果未结束添加审核对应主表的审核状态
            if (!complementStatus.getData().equals("endEvent")) {
                verifiesRecordService.saveVerifiesInfo(taskId, 0);
            }

            return new ResultInfo(0, "操作成功", buyorderExpenseId);
        } catch (IllegalArgumentException ar) {
            log.info("complementTaskForBuyOrder :", ar);
            return new ResultInfo(-1, "任务完成操作失败：" + ar.getMessage());
        } catch (Exception e) {
            log.error("complementTaskForBuyOrder :", e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        } finally {
            ThreadLocalContext.removeAll();
        }
    }

    private void doComplementTask(HttpServletRequest request, String comment, User user, Map<String, Object> variables, List<Task> collect) {
        if (CollUtil.isNotEmpty(collect)) {
            for (int i = 0; i < collect.size(); i++) {
                //当前审核人是谁
                if (i == 0) {
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, collect.get(i).getId(), comment, user.getUsername(), variables);

                    // 如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(collect.get(i).getId(), 0);
                    }
                } else {
                    ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, collect.get(i).getId(), comment==null?"_AUTO":comment+"_AUTO", user.getUsername(), variables);

                    // 如果未结束添加审核对应主表的审核状态
                    if (!complementStatus.getData().equals("endEvent")) {
                        verifiesRecordService.saveVerifiesInfo(collect.get(i).getId(), 0);
                    }
                }

            }

        }
    }



    /**
     * 获取activity主键信息
     *
     * @param buyorderInfo 订单信息
     * @return 标识
     */
    private String getProcessKey(BuyorderExpenseDto buyorderInfo) {
        User creatorInfo = userService.getUserById(buyorderInfo.getCreator());
        boolean isSupplyHCGroup = false;
        //是否是供应链耗材组
        if (!StringUtils.isEmpty(creatorInfo.getOrgName())
                && creatorInfo.getOrgName().contains("供应链管理部耗材组")) {
            isSupplyHCGroup = true;
        }

        if (!isSupplyHCGroup) {
            return processKey;
        }

        return processKey;

    }

    /**
     * 非直属采购费用单付款审核操作（仅大于十万时供应链产品经理会调用到）
     *
     * @param request           HttpServletRequest
     * @param taskId            付款审核流程id
     * @param comment           审核备注
     * @param pass              是否通过
     * @param buyorderExpenseId 费用单id
     * @return ResultInfo
     */
    @ResponseBody
    @RequestMapping(value = "/doPayApplyComplement")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementAfterSaleTask(HttpServletRequest request, String taskId, String comment, Boolean pass, Integer buyorderExpenseId) {
        CurrentUser user = CurrentUser.getCurrentUser();
        Map<String, Object> variables = new HashMap<>();
        variables.put("pass", pass);
        ResultInfo backResultInfo = new ResultInfo(0, "操作成功", buyorderExpenseId);
        //审批操作
        try {
            TaskService taskService = processEngine.getTaskService();
            String id = (String) taskService.getVariable(taskId, "id");
            Integer idValue = (Integer) taskService.getVariable(taskId, "idValue");
            String key = (String) taskService.getVariable(taskId, "key");
            if (!pass) {
                variables.put("db", 2);
                //采购费用单付款申请不通过
                actionProcdefService.updateInfo("T_BUYORDER_EXPENSE", "BUYORDER_EXPENSE_ID", buyorderExpenseId, "LOCKED_STATUS", 0, 2);
                if (id != null && idValue != null && key != null) {
                    actionProcdefService.updateInfo("T_PAY_APPLY", id, idValue, key, 2, 2);
                }
                verifiesRecordService.saveVerifiesInfo(taskId, 2);
            }
            ResultInfo<?> complementStatus = actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), variables);
            //如果审核没结束添加审核对应主表的审核状态
            if (!"endEvent".equals(complementStatus.getData())) {
                int status;
                if (pass) {
                    //如果审核通过
                    status = 0;
                } else {
                    //如果审核不通过
                    status = 2;
                }
                verifiesRecordService.saveVerifiesInfo(taskId, status);
            }
            return backResultInfo;
        } catch (Exception e) {
            log.error("库存转换单申请审核失败:" + buyorderExpenseId, e);
            return new ResultInfo(-1, "任务完成操作失败：" + e.getMessage());
        }
    }
}
