package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeNeedPayCommand;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayEntityDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * 金蝶 应付余额调整单 转 command
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeNeedPayCommandConvertor extends BaseCommandMapStruct<KingDeeNeedPayCommand, KingDeeNeedPayDto> {

    /**
     * KingDeeNeedPayCommand
     *
     * @param dto KingDeeNeedPayEntityDto
     * @return KingDeeNeedPayCommand
     */
    @Mapping(target = "FID", source = "fid")
    @Mapping(target = "FBillNo", source = "FBillNo")
    @Mapping(target = "f_QZOK_Date", source = "FQzokDate")
    @Mapping(target = "f_QZOK_JG.FNumber", source = "FQzokJg")
    @Mapping(target = "f_QZOK_Base.FNumber", source = "FQzokBase")
    @Override
    KingDeeNeedPayCommand toCommand(KingDeeNeedPayDto dto);

    /**
     * KingDeeNeedPayEntityCommand
     *
     * @param dto KingDeeNeedPayEntityDto
     * @return KingDeeNeedPayEntityCommand
     */
    @Mapping(target = "f_QZOK_YSDDH", source = "FQzokYsddh")
    @Mapping(target = "f_QZOK_GSYWDH", source = "FQzokGsywdh")
    @Mapping(target = "f_QZOK_YWLX", source = "FQzokYwlx")
    @Mapping(target = "f_QZOK_TZJE", source = "FQzokTzje")
    KingDeeNeedPayCommand.KingDeeNeedPayEntityCommand toCommand(KingDeeNeedPayEntityDto dto);

}

