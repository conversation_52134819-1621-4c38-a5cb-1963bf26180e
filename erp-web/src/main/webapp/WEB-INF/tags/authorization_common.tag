<%@ tag language="java" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/fmt" prefix="fmt"%>
<%@ taglib uri="http://com.vedeng.common.util/tags" prefix="date"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<%@attribute name="authorizationApply" type="java.lang.Object" required="true" %>
<c:forEach items="${companyInfoList}" var="company">
    <c:if test="${company.frontEndSeq eq authorizationApply.sealType}">
        <c:set var="companyName" value="${company.companyName}" />
    </c:if>
</c:forEach>

<div class="wrapper wraper-print"  id="print-contract">
    <div class="header">
        <div style="margin-top: 17px;width: 300px;float: left;">
            <c:choose>
                <c:when test="${not empty companyName and companyName eq '南京贝登医疗股份有限公司'}">
                    <img src="${pageContext.request.contextPath}/static/images/logonew.jpg" alt="贝登logo" id="company-logo">
                </c:when>
                <c:otherwise>

                </c:otherwise>
            </c:choose>
        </div>
        <div class="header-txt" style="padding-right:60px; text-align: right;">
            授权书编号：${authorizationApply.authorizationApplyNum}<br>
        </div>
    </div>
    <div class="title">授 权 书</div>
    <div class="content ">
        <p class="p-t">致：<span class="strong">${authorizationApply.purchaseOrBidding}</span></p>
        <p><c:choose>
                <c:when test="${empty companyName}">南京贝登医疗股份有限公司</c:when>
                <c:otherwise>${companyName}</c:otherwise>
            </c:choose>作为<span class="strong"> ${authorizationApply.productCompany}
                        <c:if test ="${authorizationApply.natureOfOperation eq 1}">生产</c:if>
                        <c:if test ="${authorizationApply.natureOfOperation eq 2}">代理销售</c:if>
                </span><span class="strong">${authorizationApply.brandName}</span>品牌的<span class="strong"> ${authorizationApply.skuName}<%--${authorizationApply.skuModel}--%> </span>的<span class="strong">
                       <c:if test ="${authorizationApply.distributionsType eq 1}">独家经销</c:if>
                        <c:if test ="${authorizationApply.distributionsType eq 2}">经销</c:if>
                        <c:if test ="${authorizationApply.distributionsType eq 3}">代理</c:if>
                    </span>商，在此授权<span class="strong">${authorizationApply.authorizedCompany}</span>（被授权人）使用上述货物<%--
            --%><c:choose><%--
                --%><c:when test="${(not empty authorizationApply.purchaseProjectName) and (not empty authorizationApply.purchaseProjectNum)}"><%--
                    --%>就项目：<%--
                    --%><span class="strong"><%--
                            --%>${authorizationApply.purchaseProjectName}<%--
                    --%></span><%--
                    --%>项目编号：<%--
                    --%><span class="strong"><%--
                            --%>${authorizationApply.purchaseProjectNum}<%--
                    --%></span><%--
                --%></c:when><%--
                --%><c:when test="${(not empty authorizationApply.purchaseProjectName) and (empty authorizationApply.purchaseProjectNum)}"><%--
                    --%>就项目：<%--
                    --%><span class="strong"><%--
                            --%>${authorizationApply.purchaseProjectName}<%--
                    --%></span><%--
                --%></c:when><%--
                --%><c:when test="${(empty authorizationApply.purchaseProjectName) and (not empty authorizationApply.purchaseProjectNum)}"><%--
                    --%>就<%--
                    --%>项目编号：<%--
                    --%><span class="strong"><%--
                            --%>${authorizationApply.purchaseProjectNum}<%--
                    --%></span><%--
                --%></c:when><%--
            --%></c:choose><%--
            --%>递交<span class="strong">
                        <c:if test ="${authorizationApply.fileType eq 1}">投标</c:if>
                        <c:if test ="${authorizationApply.fileType eq 2}">响应</c:if>
                    </span>文件，且<span class="strong">${authorizationApply.authorizedCompany}</span>以其自己的名义处理后续的商业谈判和签署合同并独立承担责任。
        </p>
        <p>
            以上所述授权产品的相关售后服务支持工作将由<span class="strong">${authorizationApply.aftersalesCompany}</span>提供。
        </p>
        <p>
            该授权未经
            <c:choose>
                <c:when test="${empty companyName}">南京贝登医疗股份有限公司</c:when>
                <c:otherwise>${companyName}</c:otherwise></c:choose>书面同意，被授权人不可转授任何第三方。被授权人禁止将该授权书作为任何网络线上渠道的资格审核材料，且禁止被授权人在任何网络线上渠道展示和销售该授权书涉及的商品。
        </p>
        <p>
            本授权的有效期：<span class="beginT" style="display: inline">${authorizationApply.beginTime}</span> 至 <span class="endT" style="display: inline">${authorizationApply.endTime}</span> 。
        </p>
        <p class="top right">

            <c:choose>
                    <c:when test="${empty companyName}">南京贝登医疗股份有限公司</c:when>
                    <c:otherwise>${companyName}</c:otherwise>
            </c:choose>
        </p>
        <!-- 隐藏关键字，用于电子签章定位 -->
        <p class="right">
            <span style="color: rgba(255,255,255,0);font-size: 1px;">VEDENG_SIGN_POSITION</span>${authorizationApply.applyYear} 年 ${authorizationApply.applyMonth} 月 ${authorizationApply.applyDay} 日
        </p>
        <div class="footer footer-print">
            <div class="footer-l">www.vedeng.com</div>
            <div class="footer-r">医疗器械互联网供应链服务平台</div>
        </div>
    </div>
</div>
<script type="text/javascript">
    $(function () {
        var y=$(".beginT").text().substr(0,4)
        var m=$(".beginT").text().substr(5,2)
        var d=$(".beginT").text().substr(8,2)
        $(".beginT").text(y+"年"+m+"月"+d+"日")

        var y1=$(".endT").text().substr(0,4)
        var m1=$(".endT").text().substr(5,2)
        var d1=$(".endT").text().substr(8,2)
        $(".endT").text(y1+"年"+m1+"月"+d1+"日")
    })
</script>

