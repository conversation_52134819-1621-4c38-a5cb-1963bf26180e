Vue.component('ui-tip', {
    template: `<div class="vd-ui-tip-wrap" :class="[position, iconParse[icon] || 'warn']" ref="tipWrap" :style="positionStyle" @mouseover="showCnt" @mouseleave="hideCnt">
        <span class="vd-ui-tip-icon-sum" v-if="icon === 'sum'"></span>
        <template v-else-if="icon === 'custom'"><slot name="icon"></slot></template>
        <i class="vd-ui_icon" :class="'icon-' + icon" v-else></i>
        <div class="vd-ui-tip-cnt" :class="[{nowrap: isCalcing}]" :style="widthStyle" ref="tipCnt">
            <div class="vd-ui-tip-inner" :class="{small: font=='small'}">
                <slot></slot>
            </div>
        </div>
    </div>`,
    props: {
        icon: {
            type: String,
            default: 'caution2'
        },
        font: {
            type: String,
            default: ''
        },
        width: {
            type: Number,
            default: 300
        },
        position: {
            type: String,
            default: 'lt'
        },
        nofixed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            iconParse: {
                caution2: 'warn',
                info2: 'info'
            },
            isShow: true,
            isCalcing: true,
            widthStyle: '',
            positionStyle: ''
        }
    },
    mounted() {
        window.addEventListener('scroll', this.hideCnt)
    },
    methods: {
        showCnt() {
            this.isShow = true;
            
            if(!this.nofixed) {
                let wrapPosition = this.$refs.tipWrap.getBoundingClientRect();
                this.positionStyle = `overflow:visible;position:fixed;top:${wrapPosition.top}px;left:${wrapPosition.left}px;z-index:9999;`
            } else {
                this.positionStyle = `overflow:visible`;
            }

            if(this.$refs.tipCnt.offsetWidth > this.width) {
                this.isCalcing = false;
                this.widthStyle = `width:${this.width}px;`;
            }

            this.$emit('hover')
        },
        hideCnt() {
            this.widthStyle = "";
            this.positionStyle = "";
            this.isCalcing = true;
        }
    }
})

Vue.component('ui-title-tip', {
    template: `<div class="vd-ui-title-tip-wrap" ref="tipWrap" @mouseover="showCnt" @mouseleave="hideCnt">
        <slot></slot>
        <div class="vd-ui-title-tip-cnt" :class="[{show: isShow}, position]" ref="tipCnt" :style="positionStyle">{{ title }}</div>
    </div>`,
    props: {
        title: {
            type: String,
            default: ''
        },
        position: {
            type: String,
            default: 'top'
        },
        y: {
            type: Number,
            default: 0
        },
        nofixed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            positionStyle: '',
            isShow: false
        }
    },
    mounted() {
        window.addEventListener('scroll', this.hideCnt)
    },
    methods: {
        showCnt() {
            if(!this.nofixed) {
                document.body.append(this.$refs.tipCnt);
                let wrapPosition = this.$refs.tipWrap.getBoundingClientRect();
                console.log(wrapPosition)

                let top = wrapPosition.top + this.y;

                if(this.position == 'bottom') {
                    top = wrapPosition.top + wrapPosition.height;
                }

                this.positionStyle = `top:${top}px;left:${wrapPosition.left + wrapPosition.width/2}px;`
            }

            this.isShow = true;
        },
        hideCnt() {
            this.isShow = false;
        }
    }
})

Vue.component('ui-block-tip', {
    template: `<div class="vd-ui-tip-block-wrap" :class="[position, iconParse[icon] || 'warn']" ref="tipWrap" @mouseover="showCnt" @mouseleave="hideCnt">
        <div class="vd-ui-tip-block-placeholder">
            <slot></slot>
        </div>
        <div class="vd-ui-tip-cnt" :class="[{nowrap: isCalcing}]" :style="positionStyle + widthStyle" ref="tipCnt">
            <div class="vd-ui-tip-inner" :class="{small: font=='small'}">
                <slot name="tip"></slot>
            </div>
        </div>
    </div>`,
    props: {
        icon: {
            type: String,
            default: 'caution2'
        },
        font: {
            type: String,
            default: ''
        },
        width: {
            type: Number,
            default: 300
        },
        position: {
            type: String,
            default: 'lt'
        },
        nofixed: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            iconParse: {
                caution2: 'warn',
                info2: 'info'
            },
            isShow: true,
            isCalcing: true,
            widthStyle: '',
            positionStyle: ''
        }
    },
    mounted() {
        window.addEventListener('scroll', this.hideCnt)
    },
    methods: {
        showCnt() {
            this.isShow = true;
            
            if(!this.nofixed) {
                let wrapPosition = this.$refs.tipWrap.getBoundingClientRect();
                console.log(wrapPosition.height)
                this.positionStyle = `overflow:visible;position:fixed;top:${wrapPosition.top + (wrapPosition.height/2 - 19)}px;left:${wrapPosition.left + wrapPosition.width}px;z-index:9999;`
            } else {
                this.positionStyle = `overflow:visible;`;
            }

            if(this.$refs.tipCnt.offsetWidth > this.width) {
                this.isCalcing = false;
                this.widthStyle = `width:${this.width}px;`;
            }

            this.$emit('hover')
        },
        hideCnt() {
            this.widthStyle = "";
            this.positionStyle = "";
            this.isCalcing = true;
        }
    }
})