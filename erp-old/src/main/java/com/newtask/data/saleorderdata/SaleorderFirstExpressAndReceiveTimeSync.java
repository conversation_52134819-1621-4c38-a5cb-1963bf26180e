package com.newtask.data.saleorderdata;

import com.newtask.data.AbstractDataSync;
import com.newtask.data.dao.SaleorderDataMapper;
import com.newtask.data.dto.SaleoderDataDto;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 初始化首次发货时间
 */
@Component
public class SaleorderFirstExpressAndReceiveTimeSync extends AbstractDataSync {

    @Resource
    SaleorderDataMapper saleorderDataMapper;

    @Override
    public List<Integer> loadBizId(Long startTime, Long endTime) {
        List<Integer> saleorderFirstExpressTimeIdsList = saleorderDataMapper.getSaleorderFirstExpressTimeIdsList(startTime, endTime);

        return saleorderFirstExpressTimeIdsList;
    }

    @Override
    public List<Map<String, Object>> loadBizData(List<Integer> bizIds) {
        List<Map<String, Object>> firstExpressTime = null;

        if (CollectionUtils.isNotEmpty(bizIds)) {
            firstExpressTime = saleorderDataMapper.findFirstExpressTimeBySaleorderId(bizIds);
        }

        return firstExpressTime;
    }

    @Override
    public void updateData(List<Map<String, Object>> dataList) {
        if (CollectionUtils.isNotEmpty(dataList)) {
            long timeMillis = System.currentTimeMillis();
            List<SaleoderDataDto> saleoderDataDtos = new ArrayList<>();
            dataList.forEach(item -> {
                Integer saleorderId = NumberUtils.toInt(item.get("saleorderId").toString());

                SaleoderDataDto saleoderDataDto = new SaleoderDataDto();
                saleoderDataDto.setSaleorderId(saleorderId);
                saleoderDataDto.setFirstExpressTime(NumberUtils.toLong(item.get("firstExpressTime")+""));
                saleoderDataDto.setFirstReceiveTime(NumberUtils.toLong(item.get("firstReceiveTime")+""));
                saleoderDataDtos.add(saleoderDataDto);
                // 判断saleorderId是否存在
                makeExist(saleorderId, timeMillis);
            });
            // 根据id修改firstPayTime
            saleorderDataMapper.updateSaleorderFirstExpressTime(saleoderDataDtos, timeMillis);
        }
    }

    @Override
    public void makeExist(Integer id, Long nowTime) {

        Long count = saleorderDataMapper.getSaleorderById(id);
        if (count <= 0) {
            saleorderDataMapper.insertSaleOrder(id, nowTime);
        }
    }
}
