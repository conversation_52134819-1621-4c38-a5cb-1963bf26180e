package com.wms.dao;

import com.wms.model.po.WmsSendOrder;

import java.util.List;

public interface WmsSendOrderMapper {
    int deleteByPrimaryKey(Integer wmsSendOrderId);

    int insert(WmsSendOrder record);

    int insertSelective(WmsSendOrder record);

    WmsSendOrder selectByPrimaryKey(Integer wmsSendOrderId);

    int updateByPrimaryKeySelective(WmsSendOrder record);

    int updateByPrimaryKey(WmsSendOrder record);

    /**
     * @description: 根据orderType获取下传失败的订单
     * @return: List<WmsSendOrder>
     * @author: Strange
     * @date: 2021/2/24
     **/
    List<WmsSendOrder> getSendFailSaleorder(Integer orderType);

    WmsSendOrder getWmsSendOrderInfo(WmsSendOrder wmsSendOrder);
}