<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.aftersale.mapper.ExpenseAfterSalesMapper">
    <resultMap id="BaseResultMap" type="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity">
        <id column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId"/>
        <result column="EXPENSE_AFTER_SALES_NO" jdbcType="VARCHAR" property="expenseAfterSalesNo"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="EXPENSE_AFTER_SALES_TYPE" jdbcType="INTEGER" property="expenseAfterSalesType"/>
        <result column="EXPENSE_AFTER_SALES_REASON" jdbcType="INTEGER" property="expenseAfterSalesReason"/>
        <result column="EXPENSE_AFTER_SALES_COMMENTS" jdbcType="VARCHAR" property="expenseAfterSalesComments"/>
        <result column="ORDER_DESC" jdbcType="VARCHAR"
                javaType="com.vedeng.erp.buyorder.dto.OrderRemarkDto"
                property="orderDesc"
                typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        <result column="IS_AUTO" jdbcType="INTEGER" property="isAuto" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
        <result column="REFUND_METHOD" jdbcType="INTEGER" property="refundMethod"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
    </resultMap>

    <resultMap id="expenseAfterSalesDetail" type="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto">
        <id column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId"/>
        <result column="EXPENSE_AFTER_SALES_NO" jdbcType="VARCHAR" property="expenseAfterSalesNo"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="EXPENSE_AFTER_SALES_TYPE" jdbcType="INTEGER" property="expenseAfterSalesType"/>
        <result column="EXPENSE_AFTER_SALES_REASON" jdbcType="INTEGER" property="expenseAfterSalesReason"/>
        <result column="EXPENSE_AFTER_SALES_COMMENTS" jdbcType="VARCHAR" property="expenseAfterSalesComments"/>
        <result column="ORDER_DESC" jdbcType="VARCHAR"
                javaType="com.vedeng.erp.buyorder.dto.OrderRemarkDto"
                property="orderDesc"
                typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        <result column="IS_AUTO" jdbcType="INTEGER" property="isAuto" />
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="REFUND_METHOD" jdbcType="INTEGER" property="refundMethod"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="NAME" jdbcType="VARCHAR" property="traderContactName"/>
        <result column="TELEPHONE" jdbcType="VARCHAR" property="traderContactTelephone"/>
        <result column="MOBILE" jdbcType="VARCHAR" property="traderContactMobile"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <association property="expenseAfterSalesStatusDto"
                     javaType="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesStatusDto">
            <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId" />
            <result column="AFTER_SALES_STATUS" jdbcType="INTEGER" property="afterSalesStatus" />
            <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus" />
            <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus" />
            <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime" />
            <result column="REFUND_STATUS" jdbcType="INTEGER" property="refundStatus" />
            <result column="RETURN_INVOICE_STATUS" jdbcType="INTEGER" property="returnInvoiceStatus" />
            <result column="REPAYMENT_PERIOD" javaType="decimal" property="repaymentPeriod"/>
            <result column="NEED_RETURN_AMOUNT" javaType="decimal" property="needReturnAmount"/>
        </association>
    </resultMap>

    <resultMap id="expense_after_salse" type="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto">
        <id column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId"/>
        <result column="EXPENSE_AFTER_SALES_NO" jdbcType="VARCHAR" property="expenseAfterSalesNo"/>
        <result column="BUYORDER_EXPENSE_ID" jdbcType="INTEGER" property="buyorderExpenseId"/>
        <result column="EXPENSE_AFTER_SALES_TYPE" jdbcType="INTEGER" property="expenseAfterSalesType"/>
        <result column="EXPENSE_AFTER_SALES_REASON" jdbcType="INTEGER" property="expenseAfterSalesReason"/>
        <result column="EXPENSE_AFTER_SALES_COMMENTS" jdbcType="VARCHAR" property="expenseAfterSalesComments"/>
        <result column="ORDER_DESC" jdbcType="VARCHAR"
                javaType="com.vedeng.erp.buyorder.dto.OrderRemarkDto"
                property="orderDesc"
                typeHandler="com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler"/>
        <result column="IS_AUTO" jdbcType="INTEGER" property="isAuto" />
        <result column="TRADER_ID" jdbcType="INTEGER" property="traderId"/>
        <result column="TRADER_CONTACT_ID" jdbcType="INTEGER" property="traderContactId"/>
        <result column="REFUND_METHOD" jdbcType="INTEGER" property="refundMethod"/>
        <result column="TOTAL_AMOUNT" jdbcType="DECIMAL" property="totalAmount"/>
        <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
        <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
        <association property="expenseAfterSalesStatusDto"
                     javaType="com.vedeng.erp.aftersale.dto.ExpenseAfterSalesStatusDto">
            <id column="EXPENSE_AFTER_SALES_STATUS_ID" jdbcType="BIGINT" property="expenseAfterSalesStatusId"/>
            <result column="EXPENSE_AFTER_SALES_ID" jdbcType="BIGINT" property="expenseAfterSalesId"/>
            <result column="AFTER_SALES_STATUS" jdbcType="INTEGER" property="afterSalesStatus"/>
            <result column="AUDIT_STATUS" jdbcType="INTEGER" property="auditStatus"/>
            <result column="VALID_STATUS" jdbcType="INTEGER" property="validStatus"/>
            <result column="VALID_TIME" jdbcType="TIMESTAMP" property="validTime"/>
            <result column="REFUND_STATUS" jdbcType="INTEGER" property="refundStatus"/>
            <result column="RETURN_INVOICE_STATUS" jdbcType="INTEGER" property="returnInvoiceStatus"/>
            <result column="IS_DELETE" jdbcType="INTEGER" property="isDelete"/>
            <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
            <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
            <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName"/>
            <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
            <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
            <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName"/>
            <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark"/>
        </association>
    </resultMap>


    <sql id="Base_Column_List">
        EXPENSE_AFTER_SALES_ID,
        EXPENSE_AFTER_SALES_NO,
        BUYORDER_EXPENSE_ID,
        EXPENSE_AFTER_SALES_TYPE,
        EXPENSE_AFTER_SALES_REASON,
        EXPENSE_AFTER_SALES_COMMENTS,
        ORDER_DESC, IS_AUTO,
        TRADER_ID,
        TRADER_CONTACT_ID,
        REFUND_METHOD,
        IS_DELETE,
        ADD_TIME,
        CREATOR,
        CREATOR_NAME,
        MOD_TIME,
        UPDATER,
        UPDATER_NAME,
        UPDATE_REMARK,
        TOTAL_AMOUNT
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from T_EXPENSE_AFTER_SALES
        where EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from T_EXPENSE_AFTER_SALES
        where EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="EXPENSE_AFTER_SALES_ID" keyProperty="expenseAfterSalesId"
            parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity" useGeneratedKeys="true">
        insert into T_EXPENSE_AFTER_SALES (EXPENSE_AFTER_SALES_NO, BUYORDER_EXPENSE_ID,
                                           EXPENSE_AFTER_SALES_TYPE, EXPENSE_AFTER_SALES_REASON,
                                           EXPENSE_AFTER_SALES_COMMENTS,ORDER_DESC, IS_AUTO, TRADER_ID, TRADER_CONTACT_ID,
                                           REFUND_METHOD, IS_DELETE, ADD_TIME,
                                           CREATOR, CREATOR_NAME, MOD_TIME,
                                           UPDATER, UPDATER_NAME, UPDATE_REMARK, TOTAL_AMOUNT)
        values (#{expenseAfterSalesNo,jdbcType=VARCHAR}, #{buyorderExpenseId,jdbcType=INTEGER},
                #{expenseAfterSalesType,jdbcType=INTEGER}, #{expenseAfterSalesReason,jdbcType=INTEGER},
                #{expenseAfterSalesComments,jdbcType=VARCHAR}, #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler}, #{isAuto,jdbcType=INTEGER},#{traderId,jdbcType=INTEGER},
                #{traderContactId,jdbcType=INTEGER},
                #{refundMethod,jdbcType=INTEGER}, #{isDelete,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP},
                #{creator,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP},
                #{updater,jdbcType=INTEGER}, #{updaterName,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR},
                #{totalAmount,jdbcType=DECIMAL})
    </insert>
    <insert id="insertSelective" keyColumn="EXPENSE_AFTER_SALES_ID" keyProperty="expenseAfterSalesId"
            parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity" useGeneratedKeys="true">
        insert into T_EXPENSE_AFTER_SALES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expenseAfterSalesNo != null">
                EXPENSE_AFTER_SALES_NO,
            </if>
            <if test="buyorderExpenseId != null">
                BUYORDER_EXPENSE_ID,
            </if>
            <if test="expenseAfterSalesType != null">
                EXPENSE_AFTER_SALES_TYPE,
            </if>
            <if test="expenseAfterSalesReason != null">
                EXPENSE_AFTER_SALES_REASON,
            </if>
            <if test="expenseAfterSalesComments != null">
                EXPENSE_AFTER_SALES_COMMENTS,
            </if>
            <if test="orderDesc != null">
                ORDER_DESC,
            </if>
            <if test="isAuto != null">
                IS_AUTO,
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT,
            </if>
            <if test="traderId != null">
                TRADER_ID,
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID,
            </if>
            <if test="refundMethod != null">
                REFUND_METHOD,
            </if>
            <if test="isDelete != null">
                IS_DELETE,
            </if>
            <if test="addTime != null">
                ADD_TIME,
            </if>
            <if test="creator != null">
                CREATOR,
            </if>
            <if test="creatorName != null">
                CREATOR_NAME,
            </if>
            <if test="modTime != null">
                MOD_TIME,
            </if>
            <if test="updater != null">
                UPDATER,
            </if>
            <if test="updaterName != null">
                UPDATER_NAME,
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expenseAfterSalesNo != null">
                #{expenseAfterSalesNo,jdbcType=VARCHAR},
            </if>
            <if test="buyorderExpenseId != null">
                #{buyorderExpenseId,jdbcType=INTEGER},
            </if>
            <if test="expenseAfterSalesType != null">
                #{expenseAfterSalesType,jdbcType=INTEGER},
            </if>
            <if test="expenseAfterSalesReason != null">
                #{expenseAfterSalesReason,jdbcType=INTEGER},
            </if>
            <if test="expenseAfterSalesComments != null">
                #{expenseAfterSalesComments,jdbcType=VARCHAR},
            </if>
            <if test="orderDesc != null">
                #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
            </if>
            <if test="isAuto != null">
                #{isAuto,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="traderId != null">
                #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderContactId != null">
                #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="refundMethod != null">
                #{refundMethod,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                #{updateRemark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity">
        update T_EXPENSE_AFTER_SALES
        <set>
            <if test="expenseAfterSalesNo != null">
                EXPENSE_AFTER_SALES_NO = #{expenseAfterSalesNo,jdbcType=VARCHAR},
            </if>
            <if test="buyorderExpenseId != null">
                BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER},
            </if>
            <if test="expenseAfterSalesType != null">
                EXPENSE_AFTER_SALES_TYPE = #{expenseAfterSalesType,jdbcType=INTEGER},
            </if>
            <if test="expenseAfterSalesReason != null">
                EXPENSE_AFTER_SALES_REASON = #{expenseAfterSalesReason,jdbcType=INTEGER},
            </if>
            <if test="expenseAfterSalesComments != null">
                EXPENSE_AFTER_SALES_COMMENTS = #{expenseAfterSalesComments,jdbcType=VARCHAR},
            </if>
            <if test="orderDesc != null">
                ORDER_DESC = #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
            </if>
            <if test="isAuto != null">
                IS_AUTO = #{isAuto,jdbcType=INTEGER},
            </if>
            <if test="totalAmount != null">
                TOTAL_AMOUNT = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="traderId != null">
                TRADER_ID = #{traderId,jdbcType=INTEGER},
            </if>
            <if test="traderContactId != null">
                TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
            </if>
            <if test="refundMethod != null">
                REFUND_METHOD = #{refundMethod,jdbcType=INTEGER},
            </if>
            <if test="isDelete != null">
                IS_DELETE = #{isDelete,jdbcType=INTEGER},
            </if>
            <if test="addTime != null">
                ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
            </if>
            <if test="creator != null">
                CREATOR = #{creator,jdbcType=INTEGER},
            </if>
            <if test="creatorName != null">
                CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
            </if>
            <if test="modTime != null">
                MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updater != null">
                UPDATER = #{updater,jdbcType=INTEGER},
            </if>
            <if test="updaterName != null">
                UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
            </if>
            <if test="updateRemark != null">
                UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
            </if>
        </set>
        where EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity">
        update T_EXPENSE_AFTER_SALES
        set EXPENSE_AFTER_SALES_NO       = #{expenseAfterSalesNo,jdbcType=VARCHAR},
            BUYORDER_EXPENSE_ID          = #{buyorderExpenseId,jdbcType=INTEGER},
            EXPENSE_AFTER_SALES_TYPE     = #{expenseAfterSalesType,jdbcType=INTEGER},
            EXPENSE_AFTER_SALES_REASON   = #{expenseAfterSalesReason,jdbcType=INTEGER},
            EXPENSE_AFTER_SALES_COMMENTS = #{expenseAfterSalesComments,jdbcType=VARCHAR},
            ORDER_DESC = #{orderDesc,jdbcType=OTHER,typeHandler=com.vedeng.common.mybatis.handler.CustomSqlJsonArrayHandler},
            IS_AUTO = #{isAuto,jdbcType=INTEGER},
            TOTAL_AMOUNT                 = #{totalAmount,jdbcType=DECIMAL},
            TRADER_ID                    = #{traderId,jdbcType=INTEGER},
            TRADER_CONTACT_ID            = #{traderContactId,jdbcType=INTEGER},
            REFUND_METHOD                = #{refundMethod,jdbcType=INTEGER},
            IS_DELETE                    = #{isDelete,jdbcType=INTEGER},
            ADD_TIME                     = #{addTime,jdbcType=TIMESTAMP},
            CREATOR                      = #{creator,jdbcType=INTEGER},
            CREATOR_NAME                 = #{creatorName,jdbcType=VARCHAR},
            MOD_TIME                     = #{modTime,jdbcType=TIMESTAMP},
            UPDATER                      = #{updater,jdbcType=INTEGER},
            UPDATER_NAME                 = #{updaterName,jdbcType=VARCHAR},
            UPDATE_REMARK                = #{updateRemark,jdbcType=VARCHAR}
        where EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    </update>

    <select id="selectByExpenseId" resultMap="expense_after_salse">
        select TEAS.EXPENSE_AFTER_SALES_ID,
               TEAS.EXPENSE_AFTER_SALES_NO,
               TEAS.BUYORDER_EXPENSE_ID,
               TEAS.EXPENSE_AFTER_SALES_TYPE,
               TEAS.EXPENSE_AFTER_SALES_REASON,
               TEAS.EXPENSE_AFTER_SALES_COMMENTS,
               TEAS.TRADER_ID,
               TEAS.TRADER_CONTACT_ID,
               TEAS.REFUND_METHOD,
               TEAS.IS_DELETE,
               TEAS.ADD_TIME,
               TEAS.CREATOR,
               TEAS.CREATOR_NAME,
               TEAS.MOD_TIME,
               TEAS.UPDATER,
               TEAS.UPDATER_NAME,
               TEAS.TOTAL_AMOUNT,
               TEASS.EXPENSE_AFTER_SALES_STATUS_ID,
               TEASS.EXPENSE_AFTER_SALES_ID,
               TEASS.AFTER_SALES_STATUS,
               TEASS.AUDIT_STATUS,
               TEASS.VALID_STATUS,
               TEASS.VALID_TIME,
               TEASS.REFUND_STATUS,
               TEASS.RETURN_INVOICE_STATUS,
               TEASS.IS_DELETE,
               TEASS.ADD_TIME,
               TEASS.CREATOR,
               TEASS.CREATOR_NAME,
               TEASS.MOD_TIME,
               TEASS.UPDATER,
               TEASS.UPDATER_NAME
        from T_EXPENSE_AFTER_SALES TEAS
                 left join T_EXPENSE_AFTER_SALES_STATUS TEASS on TEAS.EXPENSE_AFTER_SALES_ID =
                                                                 TEASS.EXPENSE_AFTER_SALES_ID
        where TEAS.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
          and TEAS.IS_DELETE = 0
          and TEASS.IS_DELETE = 0
        order by TEAS.EXPENSE_AFTER_SALES_ID asc
    </select>

    <select id="getExpenseAfterSalesDetail" resultMap="expenseAfterSalesDetail">
        SELECT teas.EXPENSE_AFTER_SALES_ID,
               teas.EXPENSE_AFTER_SALES_NO,
               teas.EXPENSE_AFTER_SALES_TYPE,
               teas.CREATOR,
               teas.CREATOR_NAME,
               teas.ADD_TIME,
               teass.AFTER_SALES_STATUS,
               teass.AUDIT_STATUS,
               teass.VALID_STATUS,
               teass.VALID_TIME,
               teass.EXPENSE_AFTER_SALES_STATUS_ID,
               teass.EXPENSE_AFTER_SALES_ID,
               teass.AFTER_SALES_STATUS,
               teass.AUDIT_STATUS,
               teass.VALID_STATUS,
               teass.VALID_TIME,
               teass.REFUND_STATUS,
               teass.RETURN_INVOICE_STATUS,
               teass.NEED_RETURN_AMOUNT,
               teass.REPAYMENT_PERIOD,
               teas.EXPENSE_AFTER_SALES_REASON,
               teas.TRADER_CONTACT_ID,
               teas.REFUND_METHOD,
               teas.EXPENSE_AFTER_SALES_COMMENTS,
               teas.BUYORDER_EXPENSE_ID,
               teas.TRADER_ID,
               teas.TOTAL_AMOUNT,
               teas.ORDER_DESC,
               teas.IS_AUTO,
               tc.NAME,
               tc.TELEPHONE,
               tc.MOBILE
        FROM T_EXPENSE_AFTER_SALES teas
                 LEFT JOIN T_EXPENSE_AFTER_SALES_STATUS teass ON
                    teas.EXPENSE_AFTER_SALES_ID = teass.EXPENSE_AFTER_SALES_ID
                AND teass.IS_DELETE = 0
                 LEFT JOIN T_TRADER_CONTACT tc ON teas.TRADER_CONTACT_ID = tc.TRADER_CONTACT_ID
        WHERE teas.IS_DELETE = 0
          AND teas.EXPENSE_AFTER_SALES_ID = #{expenseAfterSalesId,jdbcType=BIGINT}
    </select>

    <select id="queryInfoByNo" resultMap="expense_after_salse">
        SELECT *
        FROM T_EXPENSE_AFTER_SALES
        WHERE EXPENSE_AFTER_SALES_NO = #{expenseAfterSalesNo,jdbcType=VARCHAR}
    </select>
    <select id="getAllCompletedAfterSales" resultType="java.lang.Long">
        SELECT
            teas.EXPENSE_AFTER_SALES_ID
        FROM
            T_EXPENSE_AFTER_SALES teas
        LEFT JOIN T_EXPENSE_AFTER_SALES_STATUS teass ON
            teas.EXPENSE_AFTER_SALES_ID = teass.EXPENSE_AFTER_SALES_ID
        WHERE
            teas.BUYORDER_EXPENSE_ID = #{buyorderExpenseId,jdbcType=INTEGER}
            AND teas.IS_DELETE = 0
            AND teass.IS_DELETE = 0
            AND teass.AFTER_SALES_STATUS = 2

    </select>
</mapper>