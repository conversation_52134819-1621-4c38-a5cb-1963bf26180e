package com.vedeng.erp.saleorder.domain.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
    * 改低价列表
    */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class OrderGoodsLowerPrice implements Serializable {
    /**
    * 主键
    */
    private Long goodsLowerPriceId;

    /**
    * 单据ID
    */
    private Integer orderId;

    /**
    * 单号
    */
    private String orderNo;

    /**
    * 1销售单 2报价单
    */
    private Integer orderType;

    /**
    * 单据明细ID
    */
    private Integer orderGoodsId;

    /**
    * 订货号
    */
    private String sku;

    /**
    * 数量
    */
    private Integer num;

    /**
    * 销售单价
    */
    private BigDecimal price;

    /**
    * 核价-销售价
    */
    private BigDecimal checkPrice;

    /**
    * 单据创建时间
    */
    private Date orderAddTime;

    /**
    * 审批意见
    */
    private String approvalOpinion;

    /**
    * 是否删除 0否 1是
    */
    private Integer isDelete;

    /**
    * 创建时间
    */
    private Date addTime;

    /**
    * 修改时间
    */
    private Date modTime;

    /**
    * 添加人ID
    */
    private Integer creator;

    /**
    * 添加人名称
    */
    private String creatorName;

    /**
    * 更新人ID
    */
    private Integer updater;

    /**
    * 更新人名称
    */
    private String updaterName;

    /**
    * 备注
    */
    private String remark;

    /**
    * 更新备注
    */
    private String updateRemark;

    private static final long serialVersionUID = 1L;
}