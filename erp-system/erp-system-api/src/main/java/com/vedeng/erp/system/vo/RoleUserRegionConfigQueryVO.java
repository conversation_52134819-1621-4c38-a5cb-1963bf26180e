package com.vedeng.erp.system.vo;

import lombok.Data;

import java.util.List;

/**
 * 人员与产线区域配置查询VO
 */
@Data
public class RoleUserRegionConfigQueryVO {
    /**
     * 省份ID列表
     */
    private List<Integer> provinceIds;
    
    /**
     * 城市ID列表
     */
    private List<Integer> cityIds;
    
    /**
     * 业务人员ID列表
     */
    private List<Integer> businessUserIds;
    
    /**
     * 在职状态（0-在职，1-离职）
     */
    private Integer employmentStatus;

    /**
     * 部门用户ID列表
     */
    private List<Integer> departmentUserIds;
    
    /**
     * 部门列表
     */
    private List<Integer> departments;
    
    /**
     * 用户ID列表
     */
    private List<Integer> userIds;

}

