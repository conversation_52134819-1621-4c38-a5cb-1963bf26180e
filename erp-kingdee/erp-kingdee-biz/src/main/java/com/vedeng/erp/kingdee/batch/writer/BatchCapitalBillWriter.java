package com.vedeng.erp.kingdee.batch.writer;

import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.common.writer.BaseWriter;
import com.vedeng.erp.kingdee.dto.KingDeeNeedPayDto;
import com.vedeng.erp.kingdee.service.KingDeeNeedPayApiService;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.item.ExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/2 13:47
 */
@Service
@Slf4j
public class BatchCapitalBillWriter extends BaseWriter<KingDeeNeedPayDto> {

    @Autowired
    private KingDeeNeedPayApiService kingDeeNeedPayApiService;

    @Override
    public void doWrite(KingDeeNeedPayDto item, JobParameters params, ExecutionContext stepContext) throws Exception {
        log.info("余额支付流水推送：{}", JSON.toJSONString(item));
        item.setKingDeeBizEnums(KingDeeBizEnums.saveKingDeeNeedPayAdjust);
        kingDeeNeedPayApiService.register(item, true);
    }
}