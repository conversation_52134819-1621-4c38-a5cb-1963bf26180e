package com.vedeng.api.standard.adapter.buyorder.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 采购单请求基类
 * 包含所有操作类型的公共字段
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-01
 */
@Data
public abstract class BaseBuyOrderRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // ========== 基础标识字段 ==========
    /**
     * 采购单ID
     */
    private Integer buyOrderId;

    /**
     * 采购单编号
     */
    private String buyOrderNo;

    // ========== 表单控制字段 ==========
    /**
     * 表单令牌
     */
    private String formToken;

    /**
     * 页面URI
     */
    private String uri;

    /**
     * 之前的参数
     */
    private String beforeParams;

    // ========== 公共业务字段 ==========
    /**
     * 备注
     */
    private String remark;

    /**
     * 内部备注
     */
    private String comments;

    /**
     * 订单类型（0-销售订单采购，1-备货订单采购）
     */
    private Integer orderType;

    /**
     * 是否赠品订单（0-否，1-是）
     */
    private Integer isGift;

    /**
     * 获取操作类型
     * 子类需要实现此方法来标识自己的操作类型
     */
    public abstract String getOperationType();

    /**
     * 验证请求数据的有效性
     * 子类可以重写此方法来实现特定的验证逻辑
     */
    public void validate() {
        // 基础验证逻辑
        // 子类可以重写添加特定验证
    }
}
