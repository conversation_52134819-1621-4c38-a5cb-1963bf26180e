<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.system.dao.MessageMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.system.model.Message" >
    <id column="MESSAGE_ID" property="messageId" jdbcType="INTEGER" />
    <result column="CATEGORY" property="category" jdbcType="INTEGER" />
    <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
    <result column="TITLE" property="title" jdbcType="VARCHAR" />
    <result column="CONTENT" property="content" jdbcType="VARCHAR" />
    <result column="URL" property="url" jdbcType="VARCHAR" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="SOURCE_NAME" property="sourceName" jdbcType="VARCHAR" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    MESSAGE_ID, CATEGORY, TITLE, CONTENT, ADD_TIME, CREATOR
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_MESSAGE
    where MESSAGE_ID = #{messageId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_MESSAGE
    where MESSAGE_ID = #{messageId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.system.model.Message" >
    insert into T_MESSAGE (MESSAGE_ID, CATEGORY, TITLE, 
      CONTENT, ADD_TIME, CREATOR
      )
    values (#{messageId,jdbcType=INTEGER}, #{category,jdbcType=INTEGER}, #{title,jdbcType=VARCHAR}, 
      #{content,jdbcType=VARCHAR}, #{addTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.system.model.Message" >
    insert into T_MESSAGE
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="messageId != null" >
        MESSAGE_ID,
      </if>
      <if test="category != null" >
        CATEGORY,
      </if>
      <if test="title != null" >
        TITLE,
      </if>
      <if test="url != null" >
        URL,
      </if>
      <if test="content != null" >
        CONTENT,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="sourceName != null" >
        SOURCE_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="messageId != null" >
        #{messageId,jdbcType=INTEGER},
      </if>
      <if test="category != null" >
        #{category,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        #{title,jdbcType=VARCHAR},
      </if>
       <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="sourceName != null" >
        #{sourceName,jdbcType=VARCHAR},
      </if>
    </trim>
     <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="messageId">
		SELECT LAST_INSERT_ID() AS messageId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.system.model.Message" >
    update T_MESSAGE
    <set >
      <if test="category != null" >
        CATEGORY = #{category,jdbcType=INTEGER},
      </if>
      <if test="title != null" >
        TITLE = #{title,jdbcType=VARCHAR},
      </if>
      <if test="content != null" >
        CONTENT = #{content,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
    </set>
    where MESSAGE_ID = #{messageId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.system.model.Message" >
    update T_MESSAGE
    set CATEGORY = #{category,jdbcType=INTEGER},
      TITLE = #{title,jdbcType=VARCHAR},
      CONTENT = #{content,jdbcType=VARCHAR},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER}
    where MESSAGE_ID = #{messageId,jdbcType=INTEGER}
  </update>
  
  <resultMap id="MessageResultMap" type="com.vedeng.system.model.Message" extends="BaseResultMap">
  	<collection property="messageUser" ofType="com.vedeng.system.model.MessageUser">
	    <id column="MESSAGE_USER_ID" property="messageUserId" jdbcType="INTEGER" />
	    <result column="USER_ID" property="userId" jdbcType="INTEGER" />
	    <result column="IS_VIEW" property="isView" jdbcType="BIT" />
	    <result column="VIEW_TIME" property="viewTime" jdbcType="BIGINT" />
  	</collection>
  </resultMap>
  <!-- 查询节点信息列表 -->
	<select id="getMessageByUserlistpage" parameterType="Map" resultMap="MessageResultMap">
	  select a.*,b.USER_ID,b.IS_VIEW,b.VIEW_TIME,b.MESSAGE_USER_ID
	    from T_MESSAGE a
	    left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
	    where 
	    b.USER_ID = #{userId,jdbcType=INTEGER}
		<if test="message.category!=null and message.category!='-1'">
			and a.CATEGORY=#{message.category,jdbcType=INTEGER}
		</if>
		<if test="message.isView!=null and message.isView!='-1'">
			and b.IS_VIEW=#{message.isView,jdbcType=INTEGER}
		</if>

               

        AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
	    order by  
	  		a.MESSAGE_ID desc
	</select>
	
	<select id="getMessageCount" parameterType="Map" resultType="java.lang.Integer">
	  select count(a.MESSAGE_ID)
	    from T_MESSAGE a
	    left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
	    where
	    b.USER_ID = #{userId,jdbcType=INTEGER}
		<if test="message.category!=null and message.category!='-1'">
			and a.CATEGORY=#{message.category,jdbcType=INTEGER}
		</if>
		<if test="message.isView!=null and message.isView!='-1'">
			and b.IS_VIEW=#{message.isView,jdbcType=INTEGER}
		</if>
           
        AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
	</select>
	
    <!--查询用户所有未读消息-->
    <select id="getMessageList" parameterType="java.lang.Integer" resultMap="MessageResultMap">
        select a.*,b.USER_ID,b.IS_VIEW,b.VIEW_TIME,b.MESSAGE_USER_ID
        from T_MESSAGE a
        left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
        where
        b.USER_ID = #{userId,jdbcType=INTEGER}
        and b.IS_VIEW=0  
          AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
             
        order by
        a.MESSAGE_ID desc
    </select>
    <!--查询用户所有未读商机消息-->
    <select id="getMessageSjList" parameterType="java.lang.Integer" resultMap="MessageResultMap">
        select a.*,b.USER_ID,b.IS_VIEW,b.VIEW_TIME,b.MESSAGE_USER_ID
        from T_MESSAGE a
        left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
        where
        b.USER_ID = #{userId,jdbcType=INTEGER}
        and b.IS_VIEW=0 AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
             
        and a.CATEGORY in( 627 ,3302,4116)
        order by
        a.MESSAGE_ID desc
        LIMIT 100
    </select>
    <!--查询客户未读商机数量-->
    <select id="queryNoReadMsgNum" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select count(a.MESSAGE_ID)
        from T_MESSAGE a
        left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID    AND b.MESSAGE_USER_ID>17324083
        and b.USER_ID = #{userId,jdbcType=INTEGER}
            and b.IS_VIEW=0
        where
        b.USER_ID = #{userId,jdbcType=INTEGER}
        and b.IS_VIEW=0
        and a.CATEGORY = 627    
          AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
        LIMIT 1000
    </select>
    <!--查询客户未读集采JCO数量-->
    <select id="queryJCONoReadMsgNum" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select count(a.MESSAGE_ID)
        from T_MESSAGE a
        left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
        where
        b.USER_ID = #{userId,jdbcType=INTEGER}
        and b.IS_VIEW=0
        and a.CATEGORY = 3302    
          AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
            LIMIT 1000
    </select>

    <select id="getUnMessageCount" parameterType="java.lang.Integer" resultType="java.lang.Integer">
        select count(a.MESSAGE_ID)
        from T_MESSAGE a
        left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
        where
          b.USER_ID = #{userId,jdbcType=INTEGER} and b.IS_VIEW=0    
          AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
    </select>

    <!--查询用户所有未读消息-->
    <select id="getUnReadMessageListTop10" parameterType="java.lang.Integer" resultMap="MessageResultMap">
        select a.*,o.TITLE as CATEGORY_NAME,b.USER_ID,b.IS_VIEW,b.VIEW_TIME,b.MESSAGE_USER_ID
        from T_MESSAGE a
        left join T_MESSAGE_USER b on a.MESSAGE_ID=b.MESSAGE_ID  
        LEFT JOIN T_SYS_OPTION_DEFINITION o ON a.CATEGORY = o.SYS_OPTION_DEFINITION_ID
        where
        b.USER_ID = #{userId,jdbcType=INTEGER}  
        and b.IS_VIEW=0  
          AND   a.ADD_TIME> (unix_timestamp()-60*60*24*180)*1000
        order by a.MESSAGE_ID desc
        limit 10
    </select>
</mapper>