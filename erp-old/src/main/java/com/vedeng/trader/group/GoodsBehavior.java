package com.vedeng.trader.group;

import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class GoodsBehavior extends BaseBehavior{
    /**
     * 品牌Id
     */
    private Integer brandId;
    /**
     * 分类Id
     */
    private Integer categoryId;
    /**
     * erp分类Id集合
     */
    private List<Integer> categoryIds;
    /**
     * 商品Id集合
     */
    private List<String> goodsIds;
    /**
     * 选择的类型
     */
    private Integer chooseType;


    private List<String> skuList;

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public List<Integer> getCategoryIds() {
        if(CollectionUtils.isEmpty(categoryIds)){
            categoryIds=new ArrayList<>();
            categoryIds.add(-1);
        }
        return categoryIds;
    }

    public void setCategoryIds(List<Integer> categoryIds) {
        this.categoryIds = categoryIds;
    }

    public List<String> getGoodsIds() {
        return goodsIds;
    }

    public void setGoodsIds(List<String> goodsIds) {
        this.goodsIds = goodsIds;
    }

    public Integer getChooseType() {
        return chooseType;
    }

    public void setChooseType(Integer chooseType) {
        this.chooseType = chooseType;
    }

    public List<String> getSkuList() {
        return skuList;
    }

    public void setSkuList(List<String> skuList) {
        this.skuList = skuList;
    }
}
