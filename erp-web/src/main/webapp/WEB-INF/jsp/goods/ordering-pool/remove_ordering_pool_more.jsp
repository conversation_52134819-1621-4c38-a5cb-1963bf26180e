<%--
  Created by IntelliJ IDEA.
  User: admin
  Date: 2021/5/27
  Time: 11:08
  To change this template use File | Settings | File Templates.
--%>
<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="批量删除" scope="application" />
<%@ include file="../../common/common.jsp"%>
<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css?rnd=1+${resourceVersionKey}" />
<html>
<head>
    <style>
        .delIndex{
            /*padding-left: 3%;*/
            padding-top: 4%;
        }
        .skuno{
            padding-top: 1%;
            /*padding-left: 3%;*/
        }

        .delcause{
            padding-top: 5%;
        }
        .delcausetext{
            width: auto;

        }
    </style>
</head>
<body>
<div class="layui-container">
    <div class="layui-row ">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 delIndex" >
            删除 <b><span style="font-size: 200%">${skuNoSize}</span> </b>个商品
        </div>
    </div>

    <div class="layui-row">
        <div class="layui-col-xs12 layui-col-sm12 layui-col-md12 skuno" >
            <b>订货号：</b>
            <span id="skuNo">
                <c:forEach items="${skuNoList}" var="item" varStatus="status">
                    <c:if test="${status.last}">${item}</c:if>
                    <c:if test="${not status.last}">${item}、</c:if>
                </c:forEach>
            </span>
        </div>
    </div>
    <div hidden id="skuNoList">
        <c:forEach items="${skuNoList}" var="item">
            <div name="skuNo">${item}</div>
        </c:forEach>
    </div>

</div>
<div class="layui-form-item layui-form-text delcause">
    <label class="layui-form-label delcausetext"><font style="color: red">*</font>删除原因：</label>
    <div class="layui-input-block">
        <textarea name="desc" placeholder="请输入内容" style="width: 90%" class="layui-textarea" id="content"></textarea>
    </div>
</div>
</body>
</html>
<script>
    
    function callbackdata3() {
        let array = new Array();
        let $div = $("div[name='skuNo']");
        for(let i=0;i<$div.length;i++){
            array[i]=$($div[i]).text();
        }

        let content = $("#content").val();

        if(content.length == 0){
            layer.alert("删除原因不能为空");
            return;
        }

        let flag = false;
        let load = layer.msg('处理中', {
            icon: 16
            ,shade: 0.1
        });
        $.ajax({
            async:false,
            url:page_url + '/ordering-pool/goods/deleteOrderingPool.do',
            data:JSON.stringify({
                causeContent:content,
                skuNoList:array
            }),
            type:"POST",
            dataType : "json",
            contentType:'application/json',
            success:function(data){
                console.log(data);
                layer.close(load);
                flag = true;
                // window.parent.search();
            },
            error:function(data){
                layer.close(load);
                if(data.status == 1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));;
                }
                if(data.status == 400){
                    layer.alert("操作失败");
                }
                if(data.status == 500){
                    layer.alert("操作失败");
                }
            }
        });
        return flag;

    }
</script>