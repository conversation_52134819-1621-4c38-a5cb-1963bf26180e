@charset "utf-8";
/*
 * artDialog skin
 * http://code.google.com/p/artdialog/
 * (c) 2009-2011 TangBin, http://www.planeArt.cn
 *
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */
 
/* common start */
body { _margin:0; _height:100%; /*IE6 BUG*/ }
.aui_outer { text-align:left; }
table.aui_border, table.aui_dialog { border:0; margin:0; border-collapse:collapse; width:auto; }
.aui_nw, .aui_n, .aui_ne, .aui_w, .aui_c, .aui_e, .aui_sw, .aui_s, .aui_se, .aui_header, .aui_tdIcon, .aui_main, .aui_footer { padding:0; }
.aui_header, .aui_buttons button { font: 12px/1.11 'Microsoft Yahei', Tahoma, Arial, Helvetica, STHeiti; _font-family:Tahoma,Arial,Helvetica,STHeiti; -o-font-family: Tahoma, Arial; }
.aui_title { overflow:hidden; text-overflow: ellipsis; }
.aui_state_noTitle .aui_title { display:none; }
.aui_close { display:block; position:absolute; text-decoration:none; outline:none; _cursor:pointer; }
.aui_close:hover { text-decoration:none; }
.aui_main { text-align:center; min-width:9em; min-width:0 \9/*IE8 BUG*/; }
.aui_content { display:inline-block; *zoom:1; *display:inline; text-align:left; border:none 0; }
.aui_content.aui_state_full { display:block; width:100%; margin:0; padding:0!important; height:100%; }
.aui_loading { width:96px; height:32px; text-align:left; text-indent:-999em; overflow:hidden; background:url(icons/loading.gif) no-repeat center center; }
.aui_icon { vertical-align: middle; }
.aui_icon div { width:48px; height:48px; margin:10px 0 10px 10px; background-position: center center; background-repeat:no-repeat; }
.aui_buttons { padding:8px; text-align:right; white-space:nowrap; }
.aui_buttons button { margin-left:15px; padding: 6px 8px; cursor: pointer; display: inline-block; text-align: center; line-height: 1; *padding:4px 10px; *height:2em; letter-spacing:2px; font-family: Tahoma, Arial/9!important; width:auto; overflow:visible; *width:1; color: #333; border: solid 1px #999; border-radius: 5px; background: #DDD; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFF', endColorstr='#DDDDDD'); background: linear-gradient(top, #FFF, #DDD); background: -moz-linear-gradient(top, #FFF, #DDD); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#FFF), to(#DDD)); text-shadow: 0px 1px 1px rgba(255, 255, 255, 1); box-shadow: 0 1px 0 rgba(255, 255, 255, .7),  0 -1px 0 rgba(0, 0, 0, .09); -moz-transition:-moz-box-shadow linear .2s; -webkit-transition: -webkit-box-shadow linear .2s; transition: box-shadow linear .2s; }
.aui_buttons button::-moz-focus-inner{ border:0; padding:0; margin:0; }
.aui_buttons button:focus { outline:none 0; border-color:#426DC9; box-shadow:0 0 8px rgba(0, 50, 0, .7); }
.aui_buttons button:hover { color:#000; border-color:#666; }
.aui_buttons button:active { border-color:#666; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#DDDDDD', endColorstr='#FFFFFF'); background: linear-gradient(top, #DDD, #FFF); background: -moz-linear-gradient(top, #DDD, #FFF); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#DDD), to(#FFF)); box-shadow:inset 0 1px 5px rgba(0, 50, 0, .7), inset 0 1px 1em rgba(0, 0, 0, .3); }
.aui_buttons button[disabled] { cursor:default; color:#666; background:#DDD; border: solid 1px #999; filter:alpha(opacity=50); opacity:.5; box-shadow:none; }
button.aui_state_highlight { color: #FFF; border: solid 1px #679a10; background: #7cb61b; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#98d237', endColorstr='#7cb61b'); background: linear-gradient(top, #98d237, #7cb61b); background: -moz-linear-gradient(top, #98d237, #7cb61b); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#98d237), to(#7cb61b)); text-shadow: -1px -1px 1px #679a10; }
button.aui_state_highlight:focus { border-color:#679a10; }
button.aui_state_highlight:hover { color:#FFF; border-color:#3c5412; }
button.aui_state_highlight:active { border-color:#3c5412; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#98d237', endColorstr='#7cb61b'); background: linear-gradient(top, #98d237, #7cb61b); background: -moz-linear-gradient(top, #98d237, #7cb61b); background: -webkit-gradient(linear, 0% 0%, 0% 100%, from(#98d237), to(#7cb61b)); }
/* common end */

.aui_inner { background:#f7f7f7; }
.aui_titleBar { width:100%; height:0; position:relative; bottom:30px; _bottom:0; _margin-top:-30px; }
.aui_title { height:29px; line-height:29px; padding:0 25px 0 0; _padding:0; text-indent:5px; color:#FFF; font-weight:700; text-shadow:-1px -1px 0 rgba(0, 50, 0, .7); }
.aui_nw, .aui_ne, .aui_sw, .aui_se, .aui_n, .aui_s, .aui_close { background-image:url(green/bg.png); background-repeat:no-repeat; }
.aui_nw { width:15px; height:38px; background-position: 0 0; _png:green/ie6/nw.png; }
.aui_ne { width:15px; height:38px; background-position: -15px 0; _png:green/ie6/ne.png; }
.aui_sw { width:15px; height:18px; background-position: 0 -38px; _png:green/ie6/sw.png; }
.aui_se { width:15px; height:18px; background-position: -15px -38px; _png:green/ie6/se.png; }
.aui_close { top:4px; right:4px; _z-index:1; width:20px; height:20px; _font-size:0; _line-height:0; text-indent:-9999em; background-position:0 -112px; _png:green/ie6/close.png; }
.aui_close:hover { background-position:0 -132px; }
.aui_n, .aui_s { background-repeat:repeat-x; }
.aui_n { background-position: 0 -56px; _png:green/ie6/n.png; }
.aui_s { background-position: 0 -94px; _png:green/ie6/s.png; }
.aui_w, .aui_e { background-image:url(green/bg2.png); background-repeat:repeat-y; }
.aui_w { background-position:left top; _png:green/ie6/w.png; }
.aui_e { background-position: right bottom; _png:green/ie6/e.png; }
aui_icon, .aui_main { padding-top:3px; }
@media screen and (min-width:0) {
	.aui_outer { border-radius:8px; box-shadow:0 5px 15px rgba(0, 50, 0, .4); -moz-transition:-moz-box-shadow linear .2s; -webkit-transition: -webkit-box-shadow linear .2s; transition: -webkit-box-shadow linear .2s; }
	.aui_state_lock .aui_outer { box-shadow:0 3px 26px rgba(0, 0, 0, .9); }
	.aui_outer:active { box-shadow:0 0 5px rgba(0, 50, 0, .1)!important; }
	.aui_state_drag .aui_outer { box-shadow:none!important; }
	.aui_nw, .aui_ne, .aui_sw, .aui_se, .aui_n, .aui_s, .aui_close { background-image:url(green/bg_css3.png); }
	.aui_nw { width:5px; height:31px; }
	.aui_ne { width:5px; height:31px; background-position: -5px 0; _png:green/ie6/ne.png; }
	.aui_sw { width:5px; height:5px;background-position: 0 -31px; }
	.aui_se { width:5px; height:5px; background-position: -5px -31px; }
	.aui_close { background-position:0 -72px; }
	.aui_close:hover { background-position:0 -92px; }
	.aui_n { background-position: 0 -36px; }
	.aui_s { background-position: 0 -67px; }
	.aui_w, .aui_e { background-image:url(green/bg_css3_2.png); }
}
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_se { width:3px; height:3px; }
.aui_state_noTitle .aui_inner { border:1px solid #666; background:#FFF; }
.aui_state_noTitle .aui_outer { box-shadow:none; }
.aui_state_noTitle .aui_nw, .aui_state_noTitle .aui_n, .aui_state_noTitle .aui_ne, .aui_state_noTitle .aui_w, .aui_state_noTitle .aui_e, .aui_state_noTitle .aui_sw, .aui_state_noTitle .aui_s, .aui_state_noTitle .aui_se { background:rgba(0, 0, 0, .05); background:#000\9!important; filter:alpha(opacity=5)!important; }
.aui_state_noTitle .aui_titleBar { bottom:0; _bottom:0; _margin-top:0; }
.aui_state_noTitle .aui_close { top:0; right:0; width:18px; height:18px; line-height:18px; text-align:center; text-indent:0; font-family: Helvetica, STHeiti; _font-family: '\u9ed1\u4f53', 'Book Antiqua', Palatino; font-size:18px; text-decoration:none; color:#214FA3; background:none; filter:!important; }
.aui_state_noTitle .aui_close:hover, .aui_state_noTitle .aui_close:active { text-decoration:none; color:#900; }