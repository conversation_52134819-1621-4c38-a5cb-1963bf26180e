package com.vedeng.workbench.model.vo.base;

import com.vedeng.workbench.model.dto.SubObjectDo;
import lombok.Data;

import java.util.List;

/**
 * .
 * @jira: VDERP-3073【商机管理中心】ERP销售工作台.
 * @notes: .
 * @version: 1.0.
 * @date: 2020/10/25 7:46 下午.
 * @author: Tomcat.Hui.
 */
@Data
public class WorkbenchUserInfo extends WorkbenchSummaryObject {

    private Integer userId;

    private String username;

    private Integer orgId;

    private List<SubObjectDo> subDirectObjectList;

    private String yesterdayEndtimeStr;

    private String thisWeekStartTimeStr;

    private String thisWeekEndTimeStr;

    private String historyStartTimeStr;

    private String todayStartTimeStr;

    private String preWeekStartTimeStr;

    private String preWeekEndTimeStr;
}
