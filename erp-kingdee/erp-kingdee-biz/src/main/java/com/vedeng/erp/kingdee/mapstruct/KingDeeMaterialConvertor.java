package com.vedeng.erp.kingdee.mapstruct;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.kingdee.domain.entity.KingDeeMaterialEntity;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialDto;
import com.vedeng.erp.kingdee.dto.KingDeeMaterialSubHeadEntityDto;
import org.mapstruct.*;

/**
 * 金蝶物料dto 转 entity
 *
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface KingDeeMaterialConvertor extends BaseMapStruct<KingDeeMaterialEntity, KingDeeMaterialDto> {

    /**
     * DTO转Entity
     *
     * @param dto
     * @return
     */
    @Mapping(target = "subheadentity", source = "subHeadEntity", qualifiedByName = "entryModelToJsonObject")
    @Override
    KingDeeMaterialEntity toEntity(KingDeeMaterialDto dto);

    /**
     * dto 原对象中 转 JSONObject
     *
     * @param source 对象
     * @return JSONObject jsonObject
     */
    @Named("entryModelToJsonObject")
    default String entryModelToJsonObject(KingDeeMaterialSubHeadEntityDto source) {
        if (source == null) {
            return null;
        }
        return JSON.toJSONString(source);
    }
}
