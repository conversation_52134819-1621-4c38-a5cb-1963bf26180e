/* Common */
.erp-loading {
  color: #666;
  height: 39px;
  line-height: 37px;
  padding: 0px 10px;
  overflow: hidden;
}
.erp-loading i {
  animation: loading 1.8s linear infinite;
  display: inline-block;
  position: relative;
  top: 1px;
  font-size: 16px;
  margin-right: 5px;
  color: #09F;
}
.erp-load-fail {
  height: 39px;
  line-height: 39px;
  padding: 0px 10px;
}
.erp-load-fail i {
  position: relative;
  top: 2px;
  font-size: 16px;
  color: #E64545;
  margin-right: 5px;
}
.erp-load-empty {
  text-align: center;
  padding: 100px 0;
}
.erp-load-empty > i {
  display: block;
  font-size: 32px;
  color: #09f;
  margin-bottom: 10px;
}
.erp-load-empty > img {
  width: 223px;
  height: 120px;
  margin-bottom: 14px;
}
.erp-load-empty > p {
  color: #999;
}
/* 客户名称 */
.trader-show-wrap {
  padding-left: 10px;
}
.trader-show-wrap .trader-show {
  overflow: hidden;
}
.trader-show-wrap .trader-show .input-value {
  font-size: 12px;
  color: #000;
  height: 38px;
  line-height: 38px;
  padding-right: 38px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.trader-show-wrap .trader-show .other-info {
  padding-right: 10px;
  margin-top: -5px;
  margin-bottom: 10px;
}
.trader-show-wrap .trader-show .other-info .other-item {
  color: #999;
}
.trader-show-wrap .trader-icon {
  width: 38px;
  height: 38px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 9;
}
.trader-show-wrap .trader-icon > .icon-tianyancha {
  font-size: 16px;
  color: #09f;
}
.trader-show-wrap .trader-icon > .icon-search {
  font-size: 16px;
  color: #666;
}
/* 客户名称erp查询弹层 */
.trader-erp-panel .erp-search-list {
  height: calc(100vh - 201px);
  height: calc(100vh - 201px - constant(safe-area-inset-bottom));
  height: calc(100vh - 201px - env(safe-area-inset-bottom));
}
.trader-erp-panel .erp-search-list .search-related {
  height: 100%;
}
.trader-erp-panel .erp-search-list .search-related .related-list {
  height: 100%;
  overflow-y: auto;
  border-bottom: solid 1px #EBEFF2;
}
.trader-erp-panel .erp-search-list .search-related .related-list .local-data {
  font-size: 12px;
  color: #999;
  line-height: 30px;
  padding: 0 10px;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item {
  padding: 10px;
  display: flex;
  align-items: center;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left {
  flex: 1;
  min-width: 0;
  display: flex;
  align-items: center;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left .trader-select {
  height: 16px;
  flex-shrink: 0;
  margin-right: 5px;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left .trader-select > i {
  font-size: 16px;
  color: #ccc;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-left .icon {
  font-size: 16px;
  color: #0084FF;
  margin-left: 5px;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item .related-item-right {
  width: 90px;
  margin-left: 10px;
  flex-shrink: 0;
  color: #999;
  text-align: right;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item.active {
  background: #E0F3FF;
}
.trader-erp-panel .erp-search-list .search-related .related-list .related-item.active .related-item-left .trader-select > i {
  color: #09f;
}
/* 客户等级 */
.ui-trader-level-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  border-radius: 3px;
}
.ui-trader-level-icon.level-S {
  background: url('/mstatic/image/traderLevel/S.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-S1 {
  background: url('/mstatic/image/traderLevel/S1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-S2 {
  background: url('/mstatic/image/traderLevel/S2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-A1 {
  background: url('/mstatic/image/traderLevel/A1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-A2 {
  background: url('/mstatic/image/traderLevel/A2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-A3 {
  background: url('/mstatic/image/traderLevel/A3.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-B1 {
  background: url('/mstatic/image/traderLevel/B1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-B2 {
  background: url('/mstatic/image/traderLevel/B2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-B3 {
  background: url('/mstatic/image/traderLevel/B3.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-C1 {
  background: url('/mstatic/image/traderLevel/C1.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-C2 {
  background: url('/mstatic/image/traderLevel/C2.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-C3 {
  background: url('/mstatic/image/traderLevel/C3.svg') no-repeat;
  background-size: cover;
}
.ui-trader-level-icon.level-D {
  background: url('/mstatic/image/traderLevel/D.svg') no-repeat;
  background-size: cover;
}
/* 天眼查列表 */
.tyc-list-panel {
  height: calc(100vh - 45px - 50px);
}
.tyc-list-panel .tyc-list-wrap {
  background: #F5F7FA;
  padding: 10px;
  height: calc(100vh - 45px - 53px - 53px);
  overflow-y: auto;
}
.tyc-list-panel .tyc-list-wrap .tyc-list {
  position: relative;
  overflow-y: auto;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item {
  background: #fff;
  border-radius: 5px;
  padding: 10px 10px 5px 0;
  display: flex;
  margin-bottom: 5px;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item:last-child {
  margin-bottom: 0;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .tyc-select {
  width: 36px;
  flex-shrink: 0;
  font-size: 16px;
  color: #ccc;
  text-align: center;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item.active {
  background: #E0F3FF;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item.active .tyc-select {
  color: #09f;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right {
  flex: 1;
  min-width: 0;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .name {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 5px;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .attrs {
  display: flex;
  flex-wrap: wrap;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .attrs .attr-item {
  flex: 0 0 50%;
  min-width: 0;
  font-size: 12px;
  font-weight: 400;
  color: #000;
  margin-bottom: 5px;
}
.tyc-list-panel .tyc-list-wrap .tyc-list .tyc-item .item-right .attrs .attr-item .label {
  color: #aaa;
}
/* 天眼查详情 */
.tyc-detail-panel {
  max-height: calc(100vh - 45px - 53px);
  overflow-y: auto;
}
.tyc-detail-panel .tyc-detail-top {
  background: #fff;
  padding: 10px 15px;
}
.tyc-detail-panel .tyc-detail-top .tyc-name {
  font-size: 14px;
  font-weight: 700;
}
.tyc-detail-panel .tyc-detail-top .tyc-tags {
  font-size: 0;
  margin-top: 10px;
  margin-bottom: -5px;
  display: flex;
  flex-wrap: wrap;
}
.tyc-detail-panel .tyc-detail-top .tyc-tags .tag {
  padding: 2px 5px;
  background: #E0F3FF;
  border-radius: 2px;
  font-size: 12px;
  color: #09f;
  margin-right: 5px;
  margin-bottom: 5px;
}
.tyc-detail-panel .tyc-detail-bottom {
  padding: 10px;
  background: #F5F7FA;
}
.tyc-detail-panel .tyc-detail-bottom .tyc-attr {
  background: #fff;
  border-radius: 5px;
  padding-right: 10px;
}
/* 详情·客户名称回显 */
.vd-ui-trader-name-wrap .trader-name-wrap {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui-trader-txt {
  flex: 1;
  min-width: 0;
  margin-right: 10px;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui_icon {
  width: 32px;
  flex-shrink: 0;
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui_icon.icon-tyc {
  color: #0084FF;
  padding-left: 4px;
}
.vd-ui-trader-name-wrap .trader-name-wrap .vd-ui_icon.icon-baidu {
  color: #2932E1;
  padding-right: 4px;
}
