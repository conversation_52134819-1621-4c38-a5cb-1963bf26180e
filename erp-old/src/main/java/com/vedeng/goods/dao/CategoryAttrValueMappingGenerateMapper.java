package com.vedeng.goods.dao;

import com.vedeng.goods.model.CategoryAttrValueMappingGenerate;
import com.vedeng.goods.model.CategoryAttrValueMappingGenerateExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CategoryAttrValueMappingGenerateMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int countByExample(CategoryAttrValueMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByExample(CategoryAttrValueMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int deleteByPrimaryKey(Integer categoryAttrValueMappingId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insert(CategoryAttrValueMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int insertSelective(CategoryAttrValueMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    List<CategoryAttrValueMappingGenerate> selectByExample(CategoryAttrValueMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    CategoryAttrValueMappingGenerate selectByPrimaryKey(Integer categoryAttrValueMappingId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExampleSelective(@Param("record") CategoryAttrValueMappingGenerate record, @Param("example") CategoryAttrValueMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByExample(@Param("record") CategoryAttrValueMappingGenerate record, @Param("example") CategoryAttrValueMappingGenerateExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKeySelective(CategoryAttrValueMappingGenerate record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table V_CATEGORY_ATTR_VALUE_MAPPING
     *
     * @mbggenerated Thu Jun 27 19:06:50 CST 2019
     */
    int updateByPrimaryKey(CategoryAttrValueMappingGenerate record);
}