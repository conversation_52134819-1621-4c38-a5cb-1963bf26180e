package com.vedeng.crm.sku.web.api;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.crm.api.ProductKeywordApiService;
import com.vedeng.crm.business.quote.domain.dto.QuoteSyncInfoDto;
import com.vedeng.crm.dto.CategoryMatchDto;
import com.vedeng.goods.service.GoodsApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/30
 */
@ExceptionController
@RestController
@RequestMapping("/crm/sku/public")
@Slf4j
public class SkuSkipController {

    @Autowired
    private GoodsApiService goodsApiService;
    @Autowired
    private ProductKeywordApiService productKeywordApiService;

    /**
     * 1、根据SKU查询tip提示信息
     * @param skuNo
     * @return
     */
    @ResponseBody
    @RequestMapping("/querySkuSkip")
    @NoNeedAccessAuthorization
    public R<Map<String,Object>> queryQuoteDetail(@RequestParam String skuNo) {
        if(StringUtils.isBlank(skuNo)){
            return R.error("请输入SKU_NO");
        }
        Map<String,Object> resultMap = goodsApiService.skuTipMap(skuNo);
        return R.success(resultMap);
    }

    /**
     * 提取关键词并匹配商品分类
     * @param input 用户输入内容
     * @return 包含关键词和匹配的商品分类列表
     */
    @ResponseBody
    @RequestMapping("/querySearchKeywordsByInput")
    @NoNeedAccessAuthorization
    public R<CategoryMatchDto> matchCategories(@RequestBody String input) {
        // 提取关键词
        List<String> keywords = productKeywordApiService.extractKeywords(input);
        CategoryMatchDto result = new CategoryMatchDto();
        result.setKeywords(keywords);
        return R.success(result);
    }


}
