<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ include file="../common/common.jsp" %>

<link rel="stylesheet" href="/static/vue/vd-ui/common.css">
<link rel="stylesheet" href="/static/vue/vd-ui/ui.css">
<link rel="stylesheet" href="/static/vue/receiptnotice/edit.css">

<div class="setting-form-wrap form-wrap" id="page-container">
    <template v-if="!pageloading">
        <div class="setting-form-block">
            <div class="setting-form-block-title">基础设置</div>
            <ui-form-item label="过滤销售">
                <div class="ui-col-10">
                    <ui-select :remote="true" multiple-type="auto" placeholder="请选择" v-model="excludeSaleIds" :default-multi="excludeSaleList" clearable :remote-info="userRemoteInfo"></ui-select>
                </div>
            </ui-form-item>
            <ui-form-item label="AED商品">
                <div class="ui-col-10">
                    <ui-select :remote="true" multiple-type="auto" placeholder="请选择" v-model="aedSkuIds" :default-multi="aedSkuList" clearable :remote-info="skuRemoteInfo"></ui-select>
                </div>
            </ui-form-item>
            <ui-form-item label="过滤公司">
                <div class="ui-col-10">
                    <ui-select :remote="true" multiple-type="auto" placeholder="请选择" v-model="excludeTraderIds" :default-multi="excludeTraderList" clearable :remote-info="traderRemoteInfo"></ui-select>
                </div>
            </ui-form-item>
            <ui-form-item label="TOP播报" :must="true">
                <div class="top-broad-item">
                    个人榜单播报前<ui-input type="number" v-model="topnUser" valid="BroadcastForm_topnUser"></ui-input>名
                </div>
                <div class="top-broad-item">
                    团队（小组，部门）榜单播报前<ui-input type="number" v-model="topnDept" valid="BroadcastForm_topnDept"></ui-input>名
                </div>
                <div class="form-tip">-由于播报内容有字数限制，此处不建议超过5</div>
            </ui-form-item>
            <ui-form-item label="项目配置" :must="true">
                <div class="project-table">
                    <div class="table-item">
                        <div class="table-th">播报项目</div>
                        <div class="table-td">日常到款播报</div>
                        <div class="table-td">周度到款Top</div>
                        <div class="table-td">月度到款Top</div>
                        <div class="table-td">月度AED TOP</div>
                        <div class="table-td">月度自有品牌TOP</div>
                        <div class="table-td">自定义播报</div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">播报标题</div>
                        <div class="table-td"><ui-input v-model="broadcastTitleDay" @blur="validBroadcastTitle"></ui-input></div>
                        <div class="table-td"><ui-input v-model="broadcastTitleWeek" @blur="validBroadcastTitle"></ui-input></div>
                        <div class="table-td"><ui-input v-model="broadcastTitleMonth" @blur="validBroadcastTitle"></ui-input></div>
                        <div class="table-td"><ui-input v-model="broadcastTitleAed" @blur="validBroadcastTitle"></ui-input></div>
                        <div class="table-td"><ui-input v-model="broadcastTitleZy" @blur="validBroadcastTitle"></ui-input></div>
                        <div class="table-td"><ui-input v-model="broadcastTitleCustom" @blur="validBroadcastTitle"></ui-input></div>
                    </div>
                    <div class="table-item">
                        <div class="table-th">播报时间</div>
                        <div class="table-td">{{ jobTimeStrMap.dayJobTime }}</div>
                        <div class="table-td">{{ jobTimeStrMap.weekJobTime }}</div>
                        <div class="table-td">{{ jobTimeStrMap.monthJobTime }}</div>
                        <div class="table-td">{{ jobTimeStrMap.aedJobTime }}</div>
                        <div class="table-td">{{ jobTimeStrMap.zyJobTime }}</div>
                        <div class="table-td">{{ jobTimeStrMap.customJobTime }}</div>
                    </div>
                </div>
                <div class="vd-ui-input-error" v-if="broadcastTitleErrorMsg">
                    <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i> 
                    <p class="vd-ui-input-error--errmsg">{{ broadcastTitleErrorMsg }}</p>
                </div>
            </ui-form-item>
        </div>
        <div class="setting-form-block">
            <div class="setting-form-block-title">播报目标设置</div>
            <div class="target-table-wrap">
                <div class="form-table target-table">
                    <div class="table-tr">
                        <div class="table-th">播报目标</div>
                        <div class="table-th">播报项目</div>
                        <div class="table-th"><span class="must">*</span>日常到款金额梯度(单位：万)</div>
                        <div class="table-th">webhook</div>
                    </div>
                    <div class="table-tr" v-for="(item, index) in deptConfigList" :key="index">
                        <div class="table-td">{{ item.broadcastDeptName }}</div>
                        <div class="table-td">
                            <ui-checkbox-group 
                                :list="targetProjectList"
                                :values.sync="item.checkValues"
                            >
                            </ui-checkbox-group>
                        </div>
                        <div class="table-td"><ui-input v-model="item.amountStep" @blur="validAmountStep" type="number" maxlength="8"></ui-input></div>
                        <div class="table-td"><ui-input v-model="item.webhook"></ui-input></div>
                    </div>
                </div>
            </div>
            <div class="form-item" v-if="amountStepErrorMsg">
                <div class="form-fields">
                    <div class="vd-ui-input-error">
                        <i class="vd-ui_icon icon-error2 vd-ui-input-error--icon"></i> 
                        <p class="vd-ui-input-error--errmsg">{{ amountStepErrorMsg }}</p>
                    </div>
                </div>
            </div> 
        </div>
        <div class="setting-form-block">
            <div class="setting-form-block-title">自定义播报设置</div>
            <ui-form-item label="统计维度" :must="true" :text="true">
                <ui-radio-group 
                    :list="tongjiList"
                    :value.sync="statType"
                    valid="BroadcastForm_statType"
                >
                </ui-radio-group>
            </ui-form-item>
            <ui-form-item label="时间范围" :must="true" :text="true">
                <ui-radio-group 
                    :list="timeRangeList"
                    :value.sync="statDateRange"
                    valid="BroadcastForm_statDateRange"
                >
                </ui-radio-group>
            </ui-form-item>
            <ui-form-item label="播报对象" :must="true" :text="true">
                <ui-checkbox-group 
                    :list="broadTargetList"
                    :values.sync="statTargetArr"
                    valid="BroadcastForm_statTargetArr"
                >
                </ui-checkbox-group>
                <div class="form-tip">-“小组，部门”只对到款大群生效</div>
            </ui-form-item>
            <ui-form-item label="统计商品">
                <div class="ui-col-10">
                    <ui-select :remote="true" multiple-type="auto" placeholder="请选择" v-model="statSkuIds" :default-multi="statSkuList" clearable :remote-info="skuRemoteInfo"></ui-select>
                </div>
            </ui-form-item>
            <ui-form-item label="统计品牌">
                <div class="ui-col-10">
                    <ui-select :remote="true" multiple-type="auto" placeholder="请选择" v-model="statBrandIds" :default-multi="statBrandList" clearable :remote-info="brandRemoteInfo"></ui-select>
                </div>
            </ui-form-item>
            <ui-form-item label="统计分类">
                <el-cascader
                    placeholder="请选择"
                    :options="allCategoryList"
                    :props="{ multiple: true }"
                    size="small"
                    filterable
                    style="width: 500px;"
                    v-model="statCategoryIds"
                >
                </el-cascader>
            </ui-form-item>
        </div>
        <div class="form-footer-wrap">
            <ui-button type="primary" @click="save">保存</ui-button>
        </div>
    </template>
</div>

<script src="${pageContext.request.contextPath}/static/vue/vd-ui/ui.js"></script>
<script src="${pageContext.request.contextPath}/static/js/receiptnotice/broadcastEdit.js"></script>