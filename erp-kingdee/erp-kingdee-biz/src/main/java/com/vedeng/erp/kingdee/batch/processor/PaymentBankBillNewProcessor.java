package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.kingdee.batch.common.enums.AfterSalesProcessEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchAfterSalesDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchBuyorderExpenseDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchCapitalBillDtoMapper;
import com.vedeng.erp.kingdee.domain.entity.KingDeePayVedengBankDto;
import com.vedeng.erp.kingdee.dto.*;
import com.vedeng.erp.kingdee.enums.KingDeeBaseEnums;
import com.vedeng.erp.kingdee.enums.KingDeePayBillSettleTypeEnum;
import com.vedeng.erp.kingdee.enums.KingDeePayBillTypeEnums;
import com.vedeng.erp.kingdee.enums.KingDeePayBillUseTypeEnums;
import com.vedeng.erp.kingdee.repository.KingDeePayVedengBankRepository;
import com.vedeng.erp.kingdee.repository.mappers.KingDeeSupplierMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.enums.KingDeeBizEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * 已结算付款银行流水推送金蝶processor
 */
@Service
@Slf4j
public class PaymentBankBillNewProcessor implements ItemProcessor<BatchBankBillDto, KingDeePayBillDto> {

    @Autowired
    private BatchBuyorderDtoMapper buyorderMapper;
    @Autowired
    private BatchAfterSalesDtoMapper afterSalesMapper;
    @Autowired
    private BatchBuyorderExpenseDtoMapper buyorderExpenseMapper;
    @Autowired
    private BatchCapitalBillDtoMapper batchCapitalBillDtoMapper;
    @Resource
    private KingDeePayVedengBankRepository kingDeePayVedengBankRepository;
    @Resource
    private KingDeeSupplierMapper kingDeeSupplierMapper;

    private static final String FRECTYPE = "1"; //交易主体 1对公 2对私
    public KingDeePayBillDto process(BatchBankBillDto batchBankBillDto) {
        log.info("已结算付款银行流水推送金蝶processor开始执行，参数：{}", JSON.toJSONString(batchBankBillDto));

        Integer orderType = batchBankBillDto.getOrderType();

        String orderNo = batchBankBillDto.getOrderNo();

        if (Objects.isNull(orderType) || StringUtils.isBlank(orderNo)) {
            log.error("已结算付款银行流水推送金蝶processor执行失败，订单类型或订单号为空：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        KingDeePayBillDto kingDeePayBillDto;

        KingDeePayBillEntryDto kingDeePayBillEntryDto = new KingDeePayBillEntryDto();

        if (KingDeeConstant.TWO.equals(orderType) || KingDeeConstant.FOUR.equals(orderType)) {

            kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, KingDeeConstant.ONE);
            //采购单申请付款
            Integer traderSupplierId = getTraderSupplierId(orderType, orderNo);
            if (Objects.isNull(traderSupplierId)) {
                log.error("已结算付款银行流水推送金蝶processor执行失败，未查到对应的供应商Id：{}", JSON.toJSONString(batchBankBillDto));
                return null;
            }

            kingDeePayBillDto.setTraderSupplierId(traderSupplierId);

        } else if (KingDeeConstant.THREE.equals(orderType)) {
            //销售售后
            BatchAfterSalesDto afterSales = afterSalesMapper.findByAfterSalesNo(orderNo);

            if (Objects.isNull(afterSales)) {
                log.error("已结算付款银行流水推送金蝶processor执行失败，未查到对应的售后单：{}", JSON.toJSONString(batchBankBillDto));
                return null;
            }

            Integer subjectType = afterSales.getSubjectType();

            Integer type = afterSales.getType();

            if (Objects.isNull(subjectType) || Objects.isNull(type)) {
                log.error("已结算付款银行流水推送金蝶processor执行失败，售后单主体类型或售后单类型为空：{}", JSON.toJSONString(afterSales));
                return null;
            }

            if (shouldProcessAfterSales(subjectType, type)) {
                kingDeePayBillDto = new KingDeePayBillDto(KingDeeBizEnums.savePayBill, KingDeeConstant.ONE);
                List<BatchAfterSalesDto> afterSalesDtos = afterSalesMapper.getAfterTraderSuplier(orderNo);

                if (CollectionUtils.isEmpty(afterSalesDtos)) {
                    log.error("已结算付银行款流水推送金蝶processor执行失败，安调维修售后单{}，查询供应商信息为空", orderNo);
                    return null;
                }

                Integer traderSupplierId = getTraderSupplierId(afterSalesDtos);
                if (Objects.isNull(traderSupplierId)) {
                    log.error("已结算付款银行流水推送金蝶processor执行失败，安调维修售后单{}，查询供应商信息为空", orderNo);
                    return null;
                }

                kingDeePayBillDto.setTraderSupplierId(traderSupplierId);

            } else {
                log.info("已结算付款银行流水推送金蝶processor执行失败，无需处理的销售售后单类型：orderNo={}，subjectType={}，type={}", orderNo, subjectType, type);
                return null;
            }

        } else {
            log.error("已结算付款银行流水推送金蝶processor执行失败，未知的订单类型：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }

        kingDeePayBillDto.setFBillTypeId(KingDeePayBillTypeEnums.BUYORDER_PAY.getCode());
        kingDeePayBillDto.setFQzokCgddh(orderNo);
        kingDeePayBillDto.setFQzokPzgsywdh(orderNo);//T_CAPITAL_BILL_DETAIL.ORDER_NO（订单单号）
        kingDeePayBillDto.setFQzokLsh(batchBankBillDto.getTranFlow());

        kingDeePayBillEntryDto.setFQzokYsddh("");
        kingDeePayBillEntryDto.setFQzokGsywdh(orderNo);
        kingDeePayBillEntryDto.setFQzokYwlx(getFQzokYwlx(orderType));
        kingDeePayBillEntryDto.setFcomment(orderNo);
        kingDeePayBillEntryDto.setFpurposeid(KingDeePayBillUseTypeEnums.BUY_PAY.getCode());

        kingDeePayBillDto.setErpBankBillId(batchBankBillDto.getBankBillId());
        kingDeePayBillDto.setKingDeeBizEnums(KingDeeBizEnums.savePayBill);
        kingDeePayBillDto.setFDate(DateUtil.formatDateTime(batchBankBillDto.getRealTrandatetime()));
        kingDeePayBillDto.setFQzokJylx("支出");

        kingDeePayBillDto.setFBillNo(KingDeeConstant.SETTLE_ACCOUNT + batchBankBillDto.getBankBillId());

        if (KingDeeConstant.ONE.equals(batchBankBillDto.getTraderSubject())) {

            kingDeePayBillDto.setFQzokJyzt("对公");
            kingDeePayBillEntryDto.setFRecType(KingDeeConstant.ZERO.toString());
            kingDeePayBillEntryDto.setFRuZhangType(KingDeeConstant.ONE.toString());

        } else if (KingDeeConstant.TWO.equals(batchBankBillDto.getTraderSubject())) {

            kingDeePayBillDto.setFQzokJyzt("对私");
            kingDeePayBillEntryDto.setFRecType(KingDeeConstant.ONE.toString());
            kingDeePayBillEntryDto.setFRuZhangType(KingDeeConstant.ZERO.toString());

        } else {
            log.error("已结算付款流水推送金蝶processor失败,未知的交易主体类型{}", batchBankBillDto.getTraderSubject());
            return null;
        }

        //付款方式为银行的付款申请单，推送金蝶
        kingDeePayBillEntryDto.setFsettletypeid(getFsettletypeid(batchBankBillDto.getBankTag(), batchBankBillDto.getSettlementMethod()));
        kingDeePayBillEntryDto.setFcostid("");
        kingDeePayBillEntryDto.setFexpensedeptidE("");

        // 我方银行账号信息
        KingDeePayVedengBankDto kingDeePayVedengBankDto = queryBankInfo(batchBankBillDto.getBankTag());
        if (Objects.isNull(kingDeePayVedengBankDto)) {
            log.error("已结算付款流水推送金蝶processor失败,未查询到我方银行信息{}", batchBankBillDto.getBankTag());
            return null;
        }

        kingDeePayBillEntryDto.setFaccountid(kingDeePayVedengBankDto.getPayBankNo());
        kingDeePayBillEntryDto.setFsettleno("");
        kingDeePayBillEntryDto.setFpostdate(kingDeePayBillDto.getFDate());

        List<KingDeePayBillEntryDto> kingDeePayBillEntryDtoList = new ArrayList<>();
        // 查询银行流水关联的所有资金流水
        List<BatchCapitalBillDto> capitalBillDtoList = batchCapitalBillDtoMapper.queryByTraderTypeAndBankBillId(KingDeeConstant.TWO, batchBankBillDto.getBankBillId());
        if (CollUtil.isEmpty(capitalBillDtoList)) {
            log.error("已结算付款银行流水推送金蝶processor执行失败，银行流水关联的资金流水不存在：{}", JSON.toJSONString(batchBankBillDto));
            return null;
        }
        for (BatchCapitalBillDto batchCapitalBillDto : capitalBillDtoList) {
            KingDeePayBillEntryDto kingDeePayBillEntryDtoBettter = new KingDeePayBillEntryDto();
            BeanUtils.copyProperties(kingDeePayBillEntryDto,kingDeePayBillEntryDtoBettter);
            //收款单位联行号 VDERP-15926不传联行号
            kingDeePayBillEntryDtoBettter.setFCnaps("");
            kingDeePayBillEntryDtoBettter.setFOppositeBankAccount(batchBankBillDto.getAccno2());
            //收款单位开户行
            kingDeePayBillEntryDtoBettter.setFOppositeBankName(batchBankBillDto.getCadBankNm());
            kingDeePayBillEntryDtoBettter.setFOppositeCcountName(batchBankBillDto.getAccName1());
            kingDeePayBillEntryDtoBettter.setFpaytotalamountfor(batchCapitalBillDto.getAmount());

            //归属业务
            kingDeePayBillEntryDtoBettter.setFQzokGsywdh(batchCapitalBillDto.getOrderNo());
            //业务类型
            kingDeePayBillEntryDtoBettter.setFQzokYwlx(getFQzokYwlx(batchCapitalBillDto.getOrderType()));
            //收款方类型
            kingDeePayBillEntryDtoBettter.setFRecType(FRECTYPE.equals(batchCapitalBillDto.getTraderSubject().toString()) ? "0" : "1");
            //入账类型
            kingDeePayBillEntryDtoBettter.setFRuZhangType(FRECTYPE.equals(batchCapitalBillDto.getTraderSubject().toString()) ? "1" : "0");
            //表体行备注
            kingDeePayBillEntryDtoBettter.setFcomment(batchCapitalBillDto.getOrderNo());
            //归属业务单号

            kingDeePayBillEntryDtoList.add(kingDeePayBillEntryDtoBettter);
        }

        kingDeePayBillDto.setFPayBillEntry(kingDeePayBillEntryDtoList);

        KingDeePayBillSrcEntryDto kingDeePayBillSrcEntryDto = new KingDeePayBillSrcEntryDto();
        KingDeePayBillSrcEntryLinkDto kingDeePayBillSrcEntryLinkDto = new KingDeePayBillSrcEntryLinkDto();
        List<KingDeePayBillSrcEntryLinkDto> kingDeePayBillSrcEntryLinkDtoList = new ArrayList<>();
        kingDeePayBillSrcEntryLinkDtoList.add(kingDeePayBillSrcEntryLinkDto);
        kingDeePayBillSrcEntryDto.setFpaybillsrcentryLink(kingDeePayBillSrcEntryLinkDtoList);

        List<KingDeePayBillSrcEntryDto> kingDeePayBillSrcEntryDtoList = new ArrayList<>();
        kingDeePayBillSrcEntryDtoList.add(kingDeePayBillSrcEntryDto);
        kingDeePayBillDto.setFPayBillSrcEntry(kingDeePayBillSrcEntryDtoList);

        KingDeeSupplierDto kingDeeSupplierDto = kingDeeSupplierMapper.queryInfoBySupplierId(kingDeePayBillDto.getTraderSupplierId());
        if (Objects.isNull(kingDeeSupplierDto)) {
            log.error("已结算付款银行流水推送金蝶processor执行失败，未查询到金蝶供应商信息：{}", kingDeePayBillDto.getTraderSupplierId());
            return null;
        }
        kingDeePayBillDto.setFContactUnit(kingDeeSupplierDto.getFNumber().toString());
        kingDeePayBillDto.setFRectUnit(kingDeeSupplierDto.getFNumber().toString());

        return kingDeePayBillDto;
    }

    /**
     * 2采购订单 4采购费用单 5采购费用售后单 查询对应的供应商Id
     */
    private Integer getTraderSupplierId(Integer orderType, String orderNo) {

        Integer traderSupplierId = null;

        try {
            switch (orderType) {
                case 2:
                    traderSupplierId = buyorderMapper.selectByBuyorderNo(orderNo).getTraderSupplierId();
                    break;
                case 4:
                    traderSupplierId = buyorderExpenseMapper.findTraderSupplierIdByBuyorderExpenseNo(orderNo);
                    break;
                case 5:
                    traderSupplierId = buyorderExpenseMapper.findTraderSupplierIdByExpenseAfterSalesNo(orderNo);
                    break;
                default:
                    log.error("查询供应商id失败,订单类型错误，订单类型为{}，订单号为{}", orderType, orderNo);
                    break;
            }
        } catch (Exception e) {
            log.error("查询供应商id失败", e);
            return null;
        }

        return traderSupplierId;
    }

    /**
     * 3售后订单(安调维修类型) 查询对应的供应商Id
     */
    private Integer getTraderSupplierId(List<BatchAfterSalesDto> afterSalesDtos) {
        if (CollectionUtils.isEmpty(afterSalesDtos)) {
            return null;
        }

        return afterSalesDtos.stream()
                .map(BatchAfterSalesDto::getTraderSupplierId)
                .filter(Objects::nonNull)
                .findFirst()
                .orElse(afterSalesDtos.get(0).getTraderId());
    }


    /**
     * 根据流水类型查询银行信息
     */
    private KingDeePayVedengBankDto queryBankInfo(Integer bankTag){
        KingDeePayVedengBankDto kingDeePayVedengBankDto;
        switch (bankTag){
            case 1:
                //建设银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SIX);
                break;
            case 2:
                //南京银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.TWO);
                break;
            case 3:
                //中国银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.ONE);
                break;
            case 4:
                //支付宝
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FOUR);
                break;
            case 5:
                //微信
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.FIVE);
                break;
            case 6:
                //交通银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.THREE);
                break;
            case 7:
                //民生银行
                kingDeePayVedengBankDto = kingDeePayVedengBankRepository.findById(ErpConstant.SEVEN);
                break;
            default:
                log.info("未知的银行类型");
                return null;
        }
        return kingDeePayVedengBankDto;
    }

    private String getFQzokYwlx(Integer orderType) {
        switch (orderType) {
            case 2:
                return KingDeeBaseEnums.BUY_PAY.getName();
            case 3:
                return KingDeeBaseEnums.INSTALLATION_MAINTENANCE.getName();
            case 4:
                return KingDeeBaseEnums.BUY_EXPENSE_PAY.getName();
            default:
                log.error("已结算银行付款流水推送金蝶processor执行失败，未知的订单类型：{}", orderType);
                return "";
        }
    }

    private boolean shouldProcessAfterSales(Integer subjectType, Integer type) {
        return AfterSalesProcessEnum.THIRD_ORDER.getCode().equals(subjectType) ||
                (AfterSalesProcessEnum.SALES_ORDER.getCode().equals(subjectType) &&
                        (AfterSalesProcessEnum.AFTERSALES_ATN.getCode().equals(type) ||
                                AfterSalesProcessEnum.AFTERSALES_ATY.getCode().equals(type) ||
                                AfterSalesProcessEnum.AFTERSALES_AT.getCode().equals(type) ||
                                AfterSalesProcessEnum.AFTERSALES_WX.getCode().equals(type)));
    }

    private String getFsettletypeid(Integer bankTag, Integer settlementMethod){
        if (ErpConstant.THREE.equals(settlementMethod)) {
            return KingDeePayBillSettleTypeEnum.BANK_BILL_SETTLETYPE_NEW.getCode();
        }
        switch (bankTag) {
            case 4:
                return KingDeePayBillSettleTypeEnum.ALIPAY_SETTLETYPE.getCode();
            case 5:
                return KingDeePayBillSettleTypeEnum.WECHAT_SETTLETYPE.getCode();
            default:
                return KingDeePayBillSettleTypeEnum.TELEGRAPHIC_SETTLETYPE.getCode();
        }
    }

}
