package com.wms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.activiti.service.ActionProcdefService;
import com.vedeng.activiti.service.impl.ActivitiAssigneeServiceImpl;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.LendOutProcessConstants;
import com.vedeng.common.constant.OrderGoodsAptitudeConstants;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.controller.BaseController;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.exception.ShowErrorMsgException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.page.Page;
import com.vedeng.common.util.MessageUtil;
import com.vedeng.goods.model.Goods;
import com.vedeng.goods.model.Unit;
import com.vedeng.goods.service.GoodsService;
import com.vedeng.goods.service.UnitService;
import com.vedeng.goods.service.VgoodsService;
import com.vedeng.logistics.eums.WarehouseGoodsOutEnum;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.WarehouseGoodsOperateLog;
import com.vedeng.logistics.model.WmsOutputOrderGoodsExtra;
import com.vedeng.logistics.model.vo.WarehouseGoodsOutLogVo;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.logistics.service.WarehouseGoodsOutDetailService;
import com.vedeng.logistics.service.WarehouseOutService;
import com.vedeng.logistics.strategy.WarehouseGoodsOutDetailStrategyFactory;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.dto.ProductManageAndAsistDto;
import com.vedeng.system.model.Attachment;
import com.vedeng.system.service.UserService;
import com.vedeng.system.service.VerifiesRecordService;
import com.vedeng.trader.model.TraderAddress;
import com.vedeng.trader.model.TraderContactGenerate;
import com.vedeng.trader.model.vo.TraderAddressVo;
import com.vedeng.trader.model.vo.TraderContactVo;
import com.vedeng.trader.service.TraderCustomerService;
import com.wms.constant.VerifyStatusEnum;
import com.wms.model.dto.AddSampleOutDto;
import com.wms.model.dto.WmsOutputOrderGoodsDto;
import com.wms.model.po.WmsOutputOrder;
import com.wms.model.po.WmsOutputOrderGoods;
import com.wms.service.WMSLendOutService;
import com.wms.service.WMSScrappedOutService;
import com.wms.service.WmsSampleOutService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.TaskService;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.*;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/wms/sampleOut")
public class WmsSampleOutController extends BaseController {

    @Autowired
    private WmsSampleOutService wmsSampleOutService;

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private UnitService unitService;

    @Autowired
    private VgoodsService vGoodsService;

    @Autowired
    private ExpressService expressService;

    @Autowired
    private UserService userService;

    @Autowired
    private WMSScrappedOutService scrappedOutService;

    @Autowired
    @Qualifier("actionProcdefService")
    protected ActionProcdefService actionProcdefService;

    @Autowired
    private TraderCustomerService traderCustomerService;

    @Autowired
    private WarehouseOutService warehouseOutService;

    @Autowired
    private ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();

    @Autowired
    private WarehouseGoodsOutDetailStrategyFactory warehouseGoodsOutDetailStrategyFactory;


    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/toAddSampleOut")
    public ModelAndView toAddLendOut(HttpServletRequest request) {
        User sessionUser = getSessionUser(request);
        ModelAndView modelAndView = new ModelAndView("wms/sampleOut/sample_out_add");
        modelAndView.addObject("domain", domain);
        modelAndView.addObject("applyTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_4220));
        modelAndView.addObject("isSales", SysOptionConstant.ID_310.equals(sessionUser.getPositType()) ? 1 : 0);
        return modelAndView;
    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/addSampleOutGoods")
    public ModelAndView addSaleOrderGoods(HttpServletRequest request, HttpSession session,
                                          @RequestParam(value = "brandName", required = false) String brandName,
                                          @RequestParam(value = "typeSpecification", required = false) String typeSpecification,
                                          @RequestParam(value = "unitName", required = false) String unitName,
                                          @RequestParam(value = "searchContent", required = false) String searchContent,
                                          @RequestParam(value = "saleorderId", defaultValue = "0") Integer saleorderId,
                                          @RequestParam(value = "traderId", defaultValue = "0") Integer traderId,
                                          @RequestParam(required = false, defaultValue = "1") Integer pageNo,
                                          @RequestParam(required = false, defaultValue = "10") Integer pageSize,
                                          @RequestParam(value = "callbackFunction", required = false) String callbackFunction,
                                          @RequestParam(required = false, defaultValue = "0") Integer scene) {
        User user = getSessionUser(request);
        ModelAndView mv = new ModelAndView("wms/sampleOut/add_sample_out_goods");

        mv.addObject("scene", scene);
        boolean anyNotEmpty = StringUtils.isNotBlank(brandName) || StringUtils.isNotBlank(typeSpecification)
                || StringUtils.isNotBlank(unitName) || StringUtils.isNotBlank(searchContent);
        if (anyNotEmpty) {

            Page page = getPageTag(request, pageNo, pageSize);
            Goods goods = new Goods();
            goods.setCompanyId(ErpConst.NJ_COMPANY_ID);
            goods.setSearchContent(searchContent);
            goods.setBrandName(brandName);
            //添加组合搜索，规格型号
            goods.setSpecModel(typeSpecification);
            goods.setUnitName(unitName);

            Map<String, Object> map = goodsService.getSampleOrderGoodsPage(goods, page, session, user, traderId);
            mv.addObject("goodsList", map.get("list"));

            mv.addObject("page", map.get("page"));
            mv.addObject("searchContent", searchContent);
            mv.addObject("zxfunitNameValue", unitName);
            mv.addObject("typeSpecification", typeSpecification);
            mv.addObject("brandName", brandName);
        }
        Unit unit = new Unit();
        unit.setCompanyId(ErpConst.NJ_COMPANY_ID);
        List<Unit> unitList = unitService.getAllUnitList(unit);
        mv.addObject("traderId", traderId);
        mv.addObject("unitList", unitList);
        mv.addObject("callbackFunction", callbackFunction);
        return mv;

    }


    @ResponseBody
    @RequestMapping(value = "/saveSampleOutOrder")
    public ModelAndView saveReceiveOutOrder(HttpServletRequest request, AddSampleOutDto addSampleOutDto) {
        ModelAndView mv = new ModelAndView();

        try {
            User user = getSessionUser(request);
            Long sampleOutOrderId = wmsSampleOutService.saveSampleOutOrder(addSampleOutDto, user);
            log.info("保存样品出库单创建完成, sampleOutOrderId: {}, 开始提交审核", sampleOutOrderId);
            startProcessInstance(request, sampleOutOrderId);
            mv.setViewName("redirect:/wms/sampleOut/detail.do?sampleOrderId=" + sampleOutOrderId);
            return mv;
        } catch (Exception e) {
            log.error("保存样品出库单失败====================:", e);
            mv.addObject("message", e.getMessage());
            return fail(mv);
        }
    }

    @Autowired
    @Qualifier("activitiAssigneeService")
    private ActivitiAssigneeServiceImpl activitiAssigneeService;

    private void startProcessInstance(HttpServletRequest request, Long sampleOutOrderId) throws Exception {
        logger.info("样品出库单审核开始 sampleOutOrderId:{}", sampleOutOrderId);

        User user = getSessionUser(request);

        //初始化工作流变量
        Map<String, Object> variableMap = new HashMap<String, Object>();

        variableMap.put("processDefinitionKey", "sampleOutAudit");
        variableMap.put("businessKey", "sampleOutAudit_" + sampleOutOrderId);
        variableMap.put("currentAssinee", user.getUsername());
        variableMap.put("currentAssineeId", user.getOrgId());
        variableMap.put("num", 1);
        variableMap.put("sampleOutOrderId", sampleOutOrderId);

        WmsOutputOrder sampleOut = wmsSampleOutService.getSampleOutOrderDetail(sampleOutOrderId);
        variableMap.put("sampleOutOrderNo", sampleOut.getOrderNo());
        variableMap.put("auditLine", wmsSampleOutService.getAuditLine(sampleOut));
        variableMap.put("isKaOrg", wmsSampleOutService.isKaOrg(user));
        variableMap.put("isSales", SysOptionConstant.ID_310.equals(user.getPositType()) ? 1 : 0);

        User assigneeObj = userService.getUserParentInfo(user.getUsername(), ErpConst.NJ_COMPANY_ID);
        String assigneeObjName = Objects.nonNull(assigneeObj) && Objects.nonNull(assigneeObj.getpUsername()) ? assigneeObj.getpUsername() : user.getUsername();
        variableMap.put("parentAssignee", assigneeObjName);
        User assigneeObjParent = userService.getUserParentInfo(assigneeObjName, ErpConst.NJ_COMPANY_ID);
        String assigneeObjParentName = Objects.nonNull(assigneeObjParent) && Objects.nonNull(assigneeObjParent.getpUsername()) ? assigneeObjParent.getpUsername() : assigneeObjName;
        variableMap.put("isBoss", ErpConst.BOSS.equals(StringUtils.lowerCase(assigneeObjParentName)) ? 1 : 0);
        logger.info("样品出库单审核流 variableMap:{}", JSON.toJSONString(variableMap));


        log.info("样品出库单创建审核流开始 sampleOutOrderId:{}", sampleOutOrderId);
        actionProcdefService.createProcessInstance(request, variableMap.get("processDefinitionKey").toString(), variableMap.get("businessKey").toString(), variableMap);
        log.info("样品出库单创建审核流完成 sampleOutOrderId:{}", sampleOutOrderId);
        wmsSampleOutService.updateSampleOutAuditStatus(sampleOutOrderId, VerifyStatusEnum.Reviewing.getValue());
        log.info("更新样品出库单审核状态为审核中 sampleOutOrderId:{}", sampleOutOrderId);

        // 获取当前活动节点
        Task taskInfo = processEngine.getTaskService()
                .createTaskQuery()
                .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                .singleResult();

        //完成申请的任务
        ResultInfo<?> resultInfo = actionProcdefService.complementTask(request, taskInfo.getId(), "", user.getUsername(), variableMap);

        if (null != resultInfo && ResultInfo.success().getCode().equals(resultInfo.getCode())) {
            List<String> noticeUserList = activitiAssigneeService.getUserListByPosition("进存销主管");
            //发起自动审核通过
            log.info("样品审核单，发起自动审核通过");
            Task taskInfoNotice = processEngine.getTaskService()
                    .createTaskQuery()
                    .processInstanceBusinessKey(variableMap.get("businessKey").toString())
                    .singleResult();
            actionProcdefService.complementTask(request, taskInfoNotice.getId(), "进存销主管:"+String.join(",", noticeUserList) , "njadmin", variableMap);

            wmsSampleOutService.sendWeixinNotice(sampleOutOrderId,noticeUserList);

        }


        if (null != resultInfo && ResultInfo.error().getCode().equals(resultInfo.getCode())) {
            logger.info("申请的任务完成失败taskId :{} ", taskInfo.getId());
            throw new Exception();
        }


    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/detail")
    public ModelAndView detail(HttpServletRequest request, @Param("sampleOrderId") Long sampleOrderId) {
        User sessionUser = getSessionUser(request);

        WmsOutputOrder outputOrder = wmsSampleOutService.getSampleOutOrderDetail(sampleOrderId);

        ModelAndView mv = new ModelAndView();
        mv.addObject("currentUser", sessionUser);
        mv.addObject("sampleOrderId", sampleOrderId);
        mv.setViewName("/wms/sampleOut/sampleOutDetail");
        mv.addObject("outputOrder", outputOrder);
        mv.addObject("applyTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_4220));
        mv.addObject("isSales", SysOptionConstant.ID_310.equals(sessionUser.getPositType()) ? 1 : 0);

        WarehouseGoodsOutDetailService sampleWarehouseGoodsOutDetailService = warehouseGoodsOutDetailStrategyFactory.getWarehouseGoodsOutDetailService(WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getService());


        List<WarehouseGoodsOutLogVo> lendWarehouseGoodsOutLogList = new ArrayList<>();
        if (outputOrder != null && outputOrder.getOrderNo() != null) {
            lendWarehouseGoodsOutLogList = sampleWarehouseGoodsOutDetailService.relatedNoWarehouseGoodsOutLogList(outputOrder.getOrderNo());
        }

        List<WmsOutputOrderGoodsDto> lendOutGoodList = wmsSampleOutService.queryOutputGoodsBySampleOrderId(sampleOrderId);
        mv.addObject("lendOutGoodList", lendOutGoodList);

        mv.addObject("warehouseOutList", lendWarehouseGoodsOutLogList);

        List<Integer> skuIds = new ArrayList<>();
        for (WarehouseGoodsOutLogVo lendWarehouseGoodsOutLog : lendWarehouseGoodsOutLogList) {
            skuIds.add(lendWarehouseGoodsOutLog.getGoodsId());
        }

        if (!CollectionUtils.isEmpty(skuIds)) {
            List<Map<String, Object>> skuTipsMap = vGoodsService.skuTipList(skuIds);
            Map<String, Map<String, Object>> newSkuInfosMap = skuTipsMap.stream().collect(Collectors.toMap(key -> key.get("SKU_ID").toString(), v -> v, (k, v) -> v));
            mv.addObject("newSkuInfosMap", newSkuInfosMap);
        }

        String key = sampleOrderId + "";
        Map<String, Object> historicInfo = actionProcdefService.getHistoric(processEngine, "sampleOutAudit_" + key);
        mv.addObject("historicInfo", historicInfo);
        mv.addObject("historicActivityInstance", historicInfo.get("historicActivityInstance"));
        mv.addObject("commentMap", historicInfo.get("commentMap"));

        if (historicInfo.get("taskInfo") != null) {

            Task taskInfo = (Task) historicInfo.get("taskInfo");

            mv.addObject("taskId", taskInfo.getId());
            mv.addObject("taskInfo", taskInfo);

            //获取审核人候选组
            Map<String, Object> candidateUserMap = (Map<String, Object>) historicInfo.get("candidateUserMap");

            //获取审核人候选组
            List<IdentityLink> candidateUserList = (List<IdentityLink>) candidateUserMap.get(taskInfo.getId());

            if (CollectionUtils.isNotEmpty(candidateUserList)) {

                List<String> userNameList = candidateUserList.stream().map(identityLink -> identityLink.getUserId()).collect(Collectors.toList());

                mv.addObject("verifyUsers", StringUtils.join(userNameList, ","));
            }
        }
        Attachment attachment = wmsSampleOutService.getAttachmentBySampleOrderId(sampleOrderId);
        mv.addObject("attachment", attachment);

        Express express = new Express();
        express.setBusinessType(SysOptionConstant.ID_660);
        express.setCompanyId(ErpConst.NJ_COMPANY_ID);
        List<Long> goodsIdList = lendOutGoodList.stream().map(WmsOutputOrderGoods::getId).distinct().collect(Collectors.toList());
        express.setRelatedIds(JSONArray.parseArray(goodsIdList.toString(), Integer.class));

        List<Express> expressList = null;
        try {
            expressList = expressService.getExpressList(express);

            if (!CollectionUtils.isEmpty(expressList)) {
                expressList.forEach(expressItem -> {
                    expressItem.getExpressDetail().forEach(expressDetailItem -> {
                        expressDetailItem.setSku(expressDetailItem.getSkuCode());
                        expressDetailItem.setGoodsId(Integer.parseInt(expressDetailItem.getSkuCode().substring(1)));
                        Map<String, Object> goodsInfoMap = this.vGoodsService.skuTip(expressDetailItem.getGoodsId());
                        if (MapUtils.isEmpty(goodsInfoMap)) {
                            return;
                        }
                        expressDetailItem.setGoodName(goodsInfoMap.get("SHOW_NAME").toString());
                        expressDetailItem.setUnitName(goodsInfoMap.get("UNIT_NAME").toString());
                    });
                });
            }
            mv.addObject("expressList", expressList);
        } catch (Exception e) {
            logger.error("getExpressList:", e);
        }


        return mv;
    }


    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/editSampleOutOrder")
    public ModelAndView editSampleOutOrder(HttpServletRequest request, @Param("sampleOrderId") Long sampleOrderId) {
        User sessionUser = getSessionUser(request);
        ModelAndView modelAndView = new ModelAndView("wms/sampleOut/sample_out_edit");
        modelAndView.addObject("domain", domain);
        WmsOutputOrder outputOrder = wmsSampleOutService.getSampleOutOrderDetail(sampleOrderId);
        modelAndView.addObject("outputOrder", outputOrder);

        List<WmsOutputOrderGoodsDto> lendOutGoodList = wmsSampleOutService.queryOutputGoodsBySampleOrderId(sampleOrderId);
        modelAndView.addObject("lendOutGoodList", lendOutGoodList);


        TraderContactVo traderContactVo = new TraderContactVo();
        traderContactVo.setTraderId(outputOrder.getBorrowTraderId().intValue());
        traderContactVo.setTraderType(ErpConst.ONE);
        Map<String, Object> invoiceMap = traderCustomerService.getTraderContactVoList(traderContactVo);
        String invoiceTastr = (String) invoiceMap.get("contact");
        net.sf.json.JSONArray Json = net.sf.json.JSONArray.fromObject(invoiceTastr);
        List<TraderContactVo> contactList = (List<TraderContactVo>) Json.toCollection(Json, TraderContactVo.class);
        List<TraderAddressVo> addressList = (List<TraderAddressVo>) invoiceMap.get("address");

        modelAndView.addObject("sampleOrderId", sampleOrderId);
        modelAndView.addObject("contactList", contactList);
        modelAndView.addObject("addressList", addressList);
        modelAndView.addObject("applyTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_4220));
        modelAndView.addObject("isSales", SysOptionConstant.ID_310.equals(sessionUser.getPositType()) ? 1 : 0);

        Attachment attachment = wmsSampleOutService.getAttachmentBySampleOrderId(sampleOrderId);
        modelAndView.addObject("attachment", attachment);
        return modelAndView;
    }

    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/saveEditSampleOutOrder")
    public ModelAndView saveEditSampleOutOrder(HttpServletRequest request, AddSampleOutDto addSampleOutDto) {
        ModelAndView mv = new ModelAndView();

        try {
            User user = getSessionUser(request);
            Long sampleOutOrderId = wmsSampleOutService.saveEditSampleOutOrder(addSampleOutDto, user);
            log.info("保存更新样品出库单成功, sampleOutOrderId: {}, 开始提交审核", sampleOutOrderId);
            startProcessInstance(request, sampleOutOrderId);
            mv.setViewName("redirect:/wms/sampleOut/detail.do?sampleOrderId=" + sampleOutOrderId);
            return mv;
        } catch (Exception e) {
            log.error("保存更新样品出库单失败====================:", e);
            mv.addObject("message", e.getMessage());
            return fail(mv);
        }
    }

    private void sendSampleOutInfo(User user, WmsOutputOrder sampleOut) {

        User userParentInfo = userService.getUserParentInfo(user.getUserId());

        Map<String, String> variableMap = new HashedMap(8);
        variableMap.put("sampleOrderNo", sampleOut.getOrderNo());

        String url = "./wms/sampleOut/detail.do?sampleOrderId=" + sampleOut.getId();

        MessageUtil.sendMessage(260, Collections.singletonList(userParentInfo.getUserId()), variableMap, url, user.getUsername());

    }


    @RequestMapping(value = "/auditResult")
    @NoNeedAccessAuthorization
    public ModelAndView auditResult(String taskId, Boolean pass, Integer sampleOutId) {
        ModelAndView mv = new ModelAndView();
        mv.addObject("taskId", taskId);
        mv.addObject("pass", pass);
        mv.addObject("sampleOutId", sampleOutId);
        mv.setViewName("wms/sampleOut/auditResult");
        return mv;
    }

    @Autowired
    private WMSLendOutService lendOutService;

    @Autowired
    @Qualifier("verifiesRecordService")
    private VerifiesRecordService verifiesRecordService;


    /**
     * 获取产品经理及助理列表
     * @param auditList
     * @return
     */
    private Set<String> getProductBelongNameList(List<WmsOutputOrderGoodsExtra> auditList) {
        Set<String> uAuditorSet = new HashSet<>();
        for (WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra : auditList) {
            String productBelongNameInfo = wmsOutputOrderGoodsExtra.getProductBelongNameInfo();
            if (StringUtils.isNotBlank(productBelongNameInfo)){
                uAuditorSet.addAll(Arrays.asList(productBelongNameInfo.split(",")));
            }
        }
        return uAuditorSet;
    }

    private ProductManageAndAsistDto convertToProductManageAndAsistDto(WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra) {
        ProductManageAndAsistDto productManageAndAsistDto = new ProductManageAndAsistDto();
        productManageAndAsistDto.setProductAssitName(wmsOutputOrderGoodsExtra.getProductBelongNameInfo().split(",")[0]);
        productManageAndAsistDto.setProductManageName(wmsOutputOrderGoodsExtra.getProductBelongNameInfo().split(",")[1]);
        String[] productBelongId = wmsOutputOrderGoodsExtra.getProductBelongIdInfo().split(",");
        if (org.apache.commons.lang3.StringUtils.isNumeric(productBelongId[0])) {
            productManageAndAsistDto.setProductAssitUserId(Integer.valueOf(productBelongId[0]));
        }
        if (org.apache.commons.lang3.StringUtils.isNumeric(productBelongId[1])) {
            productManageAndAsistDto.setProductManageUserId(Integer.valueOf(productBelongId[1]));
        }
        return productManageAndAsistDto;
    }

    /**
     * 当前用户是否是产品助理后者产品经理
     *
     * @param user
     * @param productManageAndAsistDto
     * @return
     */
    private boolean currentUserIsProduct(User user, ProductManageAndAsistDto productManageAndAsistDto) {

        if (user.getUserId().equals(productManageAndAsistDto.getProductManageUserId()) ||
                user.getUserId().equals(productManageAndAsistDto.getProductAssitUserId())) {
            return true;
        }

        return false;
    }


    /**
     * 设置任务候选人
     *
     * @param taskService
     * @param businessKey
     * @param manageAndAsistNameSet
     */
    private void setTaskCandidateUser(TaskService taskService, String businessKey, Set<String> manageAndAsistNameSet) {

        Task nextTask = taskService.createTaskQuery().processInstanceBusinessKey(businessKey).singleResult();

        for (String manageAndAsistName : manageAndAsistNameSet) {
            processEngine.getTaskService().addCandidateUser(nextTask.getId() + "", manageAndAsistName);
        }
        ;

    }

    @ResponseBody
    @RequestMapping(value = "/complementTask")
    @NoNeedAccessAuthorization
    public ResultInfo<?> complementTask(HttpServletRequest request, Integer sampleOutId, String taskId, String comment, Boolean pass) {
        User user = getSessionUser(request);
        try {
            //完成当前任务，工作流往下走
            Map<String, Object> taskVaribles = new HashMap<>();
            taskVaribles.put("pass", pass);
            // 在审核操作前先查询一次当前审核节点，判断当前节点审核完成后，是否需要进入“资质自动审核”逻辑
            TaskService taskService = processEngine.getTaskService();
            String businessKey = "sampleOutAudit_" + sampleOutId;
            Task currentTaskInfo = taskService.createTaskQuery().processInstanceBusinessKey("sampleOutAudit_" + sampleOutId).singleResult();
            //产品经理或助理审核

            boolean allPass = false;
            if(pass && OrderGoodsAptitudeConstants.GOODS_MANAGEMENT_USER_APTITUDE.equals(currentTaskInfo.getName())){
                WmsOutputOrderGoodsExtra extra=new WmsOutputOrderGoodsExtra();
                extra.setWmsOutputOrderId(Long.valueOf(sampleOutId));
                List<WmsOutputOrderGoodsExtra> auditList=lendOutService.findGoodsAuditList(extra);
                List<WmsOutputOrderGoodsExtra> collect = auditList.stream()
                        .filter(buyOrderGood -> {
                            return buyOrderGood.getProductAudit() == 0;
                        }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    Set<String> set = getProductBelongNameList(auditList);
                    if (!set.contains(user.getUsername())) {
                        //判断当前人 对应的产品是否已经审核了 如果已经审核了 就直接返回
                        return new ResultInfo(-1, "当前产品已经有对应的产品经理和产品助理审核，请刷新页面重试");
                    }
                }
                log.info("出入库商品额外信息表:{}", JSON.toJSONString(auditList));
                //修改商品审核状态
                for (WmsOutputOrderGoodsExtra wmsOutputOrderGoodsExtra : auditList) {

                    ProductManageAndAsistDto productManageAndAsistDto = convertToProductManageAndAsistDto(wmsOutputOrderGoodsExtra);
                    if (currentUserIsProduct(user, productManageAndAsistDto)) {
                        wmsOutputOrderGoodsExtra.setProductAudit(1);
                        log.info("修改产品经理负责商品审核状态:{}",sampleOutId);
                        lendOutService.updateWmsOutputOrderGoodsExtra(wmsOutputOrderGoodsExtra);
                    }

                }
                extra.setProductAudit(0);
                List<WmsOutputOrderGoodsExtra> unAuditSkuList=lendOutService.findGoodsAuditList(extra);
                //还有未审核的产品
                if (CollectionUtils.isNotEmpty(unAuditSkuList)) {
                    log.info("还有未审核的产品:{}", JSON.toJSONString(unAuditSkuList));
                    taskService.setVariable(taskId, "allPass", false);

                    //完成当前任务
                    actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), taskVaribles);

//                    Set<String> uAuditorSet = getProductBelongNameList(unAuditSkuList);
//
//                    //设置下个任务的候选人
//                    setTaskCandidateUser(taskService, businessKey, uAuditorSet);

                    verifiesRecordService.saveVerifiesInfo(taskId, 0);

                    return new ResultInfo(0, "操作成功");
                }
                allPass = true;
                taskService.setVariable(taskId, "allPass", allPass);

            }
            actionProcdefService.complementTask(request, taskId, comment, user.getUsername(), taskVaribles);
            // 只有在“资质自动审核”的上游三个节点审核时，才需要进入资质自动审核的逻辑
            if (pass && (OrderGoodsAptitudeConstants.DIRECT_SUPERIOR_APPROVAL.equals(currentTaskInfo.getName()) ||
                    OrderGoodsAptitudeConstants.SALES_MANAGER_POST.equals(currentTaskInfo.getName()) || OrderGoodsAptitudeConstants.SALES_DIRECTOR_POST.equals(currentTaskInfo.getName()))) {
                Task autoTaskInfo = taskService.createTaskQuery().processInstanceBusinessKey("sampleOutAudit_" + sampleOutId).singleResult();
                if (autoTaskInfo == null) {
                    return new ResultInfo<>(-1, "流程此节点已操作完成，请确认");
                }
                // VDERP-15907  【审计支持】样品申请单优化—— 在质量总监审核前增加资质自动审核，审核不通过才需要质量总监审核
                if (OrderGoodsAptitudeConstants.KEY_AUTOCHECK_APTITUDE.equals(autoTaskInfo.getName())) {
                    logger.info("样品出库单触发资质自动审核，sampleOutId：{}", sampleOutId);
                    ResultInfo resultInfo = wmsSampleOutService.checkWmsSampleOutAutoValid(sampleOutId);
                    logger.info("样品出库单资质自动审核校验结果：{}", JSONObject.toJSONString(resultInfo));
                    boolean autoPass = false;
                    if (resultInfo.getCode() == 0) {
                        comment = wmsSampleOutService.getCommentsOfAutoCheck(sampleOutId);
                        autoPass = true;
                    } else if (resultInfo.getCode() == -1) {
                        comment = resultInfo.getMessage();
                    } else if (resultInfo.getCode() == 1) {
                        comment = resultInfo.getMessage();
                        autoPass = true;
                    }
                    taskVaribles.put("pass", autoPass);
                    actionProcdefService.complementTask(request, autoTaskInfo.getId(), comment, "njadmin", taskVaribles);
                }
            }
            if(pass){

            }


            return ResultInfo.success();
        } catch (Exception e) {
            logger.error("complementTask:", e);
            return ResultInfo.error("任务完成操作失败：" + e.getMessage());
        }
    }


    @NoNeedAccessAuthorization
    @RequestMapping(value = "/scrapDetail")
    public ModelAndView scrapDetail(HttpServletRequest request, @Param("scrappedOutId") Long scrappedOutId) {

        ModelAndView mv = new ModelAndView("wms/sampleOut/scrappedOutDetail");

        WmsOutputOrder outputOrder = wmsSampleOutService.getSampleOutOrderDetail(scrappedOutId);
        mv.addObject("scrappedOut", outputOrder);

        List<WmsOutputOrderGoodsDto> scrappedOutGoodList = wmsSampleOutService.queryOutputGoodsBySampleOrderId(scrappedOutId);
        mv.addObject("scrappedOutGoodList", scrappedOutGoodList);
        List<WarehouseGoodsOperateLog> wlogList = wlogList = scrappedOutService
                .getWlogList(Integer.valueOf(scrappedOutId.intValue()), StockOperateTypeConst.SAMPLE_ORDER_OUT);
        if (CollectionUtils.isNotEmpty(wlogList)) {
            mv.addObject("realOutputTime", Collections.max(wlogList.stream().map(WarehouseGoodsOperateLog::getAddTime).collect(Collectors.toList())));
        }
        mv.addObject("wlogList", wlogList);
        mv.addObject("applyTypeList", getSysOptionDefinitionList(SysOptionConstant.ID_4220));
        return mv;
    }

    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/printOutOrder")
    public ModelAndView printOutOrder(HttpServletRequest request, String wdlIds, Saleorder saleorder,
                                      @RequestParam(required = false, defaultValue = "1") Integer priceFlag,
                                      @RequestParam(required = false) String directWlogIds,
                                      @RequestParam(required = false) Integer expressType,
                                      @RequestParam(required = false, defaultValue = "yes") String printerButtonFlag) throws ShowErrorMsgException {
        User user = getSessionUser(request);
        String type_f = ErpConst.PRINT_EXPIRATIONDATE_TYPE_F;
        Integer printFlag = ErpConst.NOPRICE_PRINT_ORDERTYPE;

        ModelAndView mv = new ModelAndView("logistics/warehouseOut/print_out_order");
        WmsOutputOrder sampleOutOrder = wmsSampleOutService.getSampleOutOrderDetail(saleorder.getOrderId().longValue());

        Saleorder s = new Saleorder();
        saleorder.setBussinessType(SysOptionConstant.ID_496);
        s.setTraderName(sampleOutOrder.getBorrowTraderName());
        TraderAddress traderAddress = traderCustomerService.getTraderAddressById(sampleOutOrder.getWmsOutputOrderExtra().getReceiverAddressId());
        if (Objects.nonNull(traderAddress)) {
            s.setTakeTraderAddress(traderAddress.getAddress());
        }
        TraderContactGenerate traderContact = traderCustomerService.getTraderContactByTraderContactId(sampleOutOrder.getWmsOutputOrderExtra().getReceiverId());
        if (Objects.nonNull(traderContact)) {
            s.setTakeTraderContactName(traderContact.getName());
            s.setTakeTraderContactMobile(traderContact.getMobile());
            s.setTakeTraderContactTelephone(traderContact.getTelephone());
        }

        mv.addObject("bussinessNo", sampleOutOrder.getOrderNo());
        mv.addObject("saleorder", s);


        List<WarehouseGoodsOperateLog> woList = new ArrayList<WarehouseGoodsOperateLog>();
        // 根据出库id获取出库信息
        WarehouseGoodsOperateLog w = new WarehouseGoodsOperateLog();
        if (!SysOptionConstant.ID_496.equals(saleorder.getBussinessType())) {
            w.setBussinessType(ErpConst.TYPE_1);
        }
        w.setOperatorfalg(ErpConst.PRINT_ORDER);
        w.setSaleorderId(saleorder.getSaleorderId());

        saleorder.setOptType(type_f);

        //普发发记录内的最后一次出库时间
        Long currTime = 0L;

        //组装出库单内普发出库记录id
        saleorder.setSearch(wdlIds);
        List<Integer> idList = wmsSampleOutService.getPrintOutIdListByType(saleorder, ErpConst.ZERO, expressType);

        if (CollectionUtils.isNotEmpty(idList)) {
            int parseInt = Integer.parseInt(type_f);
            w.setYwType(parseInt);
            if (CollectionUtils.isNotEmpty(idList)) {
                w.setIdList(idList);
                currTime = warehouseOutService.getLastOutTime(w, ErpConst.ZERO);
                woList = wmsSampleOutService.getWLById(w, ErpConst.ZERO);
            }
        }
        /**
         * 判定 产品注册证号/者备案凭证编号 ,生产企业,生产企业许可证号/备案凭证编号.SET	,储运条件  字段
         */
        Integer titleType = null;
        titleType = warehouseOutService.updatefirstRegistraionInfo(woList, new ArrayList<>(), titleType, type_f, printFlag);

        Long time = 0L;
        // 记录本页数量
        WarehouseGoodsOperateLog wLog = new WarehouseGoodsOperateLog();
        if (!CollectionUtils.isEmpty(woList)) {
            for (WarehouseGoodsOperateLog wl : woList) {
                if (Objects.isNull(wl)) {
                    continue;
                }
                if (wl.getAddTime() > time) {
                    time = wl.getAddTime();
                    wLog.setCreator(wl.getCreator());
                }
            }
        }


        User u = userService.getUserById(wLog.getCreator());

        mv.addObject("currTime", currTime);
        mv.addObject("timeStr", CollectionUtils.isNotEmpty(woList) ? woList.get(0).getTimeStr() : "");

        if (Objects.nonNull(saleorder.getExpressId()) && saleorder.getExpressId() != 0) {
            //标识是否是物流信息的打印入口
            mv.addObject("isExpressPrint", true);
        }
        mv.addObject("outName", u == null ? "" : u.getUsername());
        mv.addObject("userName", user.getUsername());

        for (WarehouseGoodsOperateLog log : woList) {
            Integer manageCategoryLevel = log.getManageCategoryLevel();
            String productCompanyLicence = log.getProductCompanyLicence();
            if (Objects.equals(SysOptionConstant.ID_968, manageCategoryLevel)) {
                // 一类 取第一类医疗器械生产备案编号
                productCompanyLicence = StringUtils.isNotBlank(log.getRecordCertificateLicence()) ? log.getRecordCertificateLicence() : "/";
            } else if (Objects.equals(SysOptionConstant.ID_969, manageCategoryLevel)
                    || Objects.equals(SysOptionConstant.ID_970, manageCategoryLevel)) {
                // 二类、三类 取生产企业生产许可证号
                productCompanyLicence = StringUtils.isNotBlank(log.getProductionLicenseNumber()) ? log.getProductionLicenseNumber() : "/";
            }
            log.setProductCompanyLicence(productCompanyLicence);
        }
        mv.addObject("woList", woList);
        mv.addObject("type", type_f);
        mv.addObject("bussinessNo", saleorder.getBussinessNo());
        mv.addObject("bussinessType", saleorder.getBussinessType());
        mv.addObject("titleType", titleType);
        mv.addObject("printFlag", printFlag);
        mv.addObject("printerButtonFlag", printerButtonFlag);
        mv.addObject("orderPrintOutType", 2);
        mv.addObject("isHcOrder", 0);
        return mv;

    }

    /**
     * 获取当前流程中，流转到当前用户的流程中，对应的erp业务单据id集合
     *
     * @param processDefinitionKey 审核流流程key
     * @return erp业务单据id集合
     */
    @ResponseBody
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getOrderIdUnderReview")
    public ResultInfo<List<Integer>> getOrderIdUnderReview(@RequestParam String processDefinitionKey) {
        return ResultInfo.success(warehouseOutService.getOrderIdUnderReview(processDefinitionKey));
    }

}
