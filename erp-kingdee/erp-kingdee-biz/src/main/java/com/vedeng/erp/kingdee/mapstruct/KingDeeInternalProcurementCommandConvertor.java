package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingdeeInternalProcurementCommand;
import com.vedeng.erp.kingdee.dto.KingDeeInternalProcurementDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring")
public interface KingDeeInternalProcurementCommandConvertor extends BaseCommandMapStruct<KingdeeInternalProcurementCommand, KingDeeInternalProcurementDto> {


    @Mapping(target = "f_QZOK_OrgId.FNumber", source = "f_QZOK_OrgId")
    @Override
    KingdeeInternalProcurementCommand toCommand(KingDeeInternalProcurementDto dto);

    @Mapping(target = "f_QZOK_ZZ.FNumber", source = "f_QZOK_ZZ")
    @Mapping(target = "f_QZOK_CGGYS.FNumber", source = "f_QZOK_CGGYS")
    @Mapping(target = "f_QZOK_XSKH.FNumber", source = "f_QZOK_XSKH")
    KingdeeInternalProcurementCommand.FEntity toCommand(KingDeeInternalProcurementDto.FEntity dto);

    @Mapping(target = "f_QZOK_SPDM.FNumber", source = "f_QZOK_SPDM")
    KingdeeInternalProcurementCommand.F_QZOK_Entity toCommand(KingDeeInternalProcurementDto.F_QZOK_Entity dto);

    KingdeeInternalProcurementCommand.F_QZOK_SubEntity toCommand(KingDeeInternalProcurementDto.F_QZOK_SubEntity dto);

}
