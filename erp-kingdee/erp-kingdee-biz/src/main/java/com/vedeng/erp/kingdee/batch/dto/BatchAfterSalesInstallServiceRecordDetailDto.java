package com.vedeng.erp.kingdee.batch.dto;

import com.vedeng.erp.kingdee.batch.common.bean.BatchBaseDto;
import lombok.*;

import java.util.Date;

/**
    * 售后安调服务记录明细
    */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BatchAfterSalesInstallServiceRecordDetailDto extends BatchBaseDto {
    private Integer afterSalesServiceDetailId;

    /**
    * 售后安调服务记录主档ID
    */
    private Integer afterSalesServiceId;

    /**
    * 售后单商品ID
    */
    private Integer afterSalesGoodsId;

    /**
    * sku
    */
    private String sku;

    /**
    * 产品名称
    */
    private String skuName;

    /**
    * 品牌
    */
    private String brand;

    /**
    * 型号
    */
    private String model;

    /**
    * 本次服务数量
    */
    private Integer num;

    /**
    * SN码
    */
    private String serialNumber;

    /**
    * 是否删除 0否 1是
    */
    private Boolean isDelete;


    /**
     * 补充码
     */
    private String supplCode;





    /**
     * 售后单ID
     */
    private Integer afterSalesId;

    /**
     * 本次验收时间
     */
    private Date checkDate;

    /**
     * 验收方式，1电话回访2纸单验收3短信通知
     */
    private Integer checkType;

    /**
     * 验收方式名称
     */
    private String checkTypeName;

    /**
     * 录音ID
     */
    private String recordId;

    /**
     * 验收结论(字典)
     */
    private Integer checkConclusion;

    /**
     * 验收结论名称
     */
    private String checkConclusionName;





    /**
     * 最大时间
     */
    private Date beginTime;

    /**
     * 最小时间
     */
    private Date endTime;
}
