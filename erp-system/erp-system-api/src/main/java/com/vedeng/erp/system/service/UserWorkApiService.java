package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.BussinessChanceMessageVo;
import com.vedeng.erp.system.dto.UserDto;
import com.vedeng.erp.system.dto.UserWorkDetail;

import java.util.List;

public interface UserWorkApiService {

    boolean handUserWorkDetail(List<UserWorkDetail> userWorkDetailList);

    Integer getUserBusiness2OtherSaleUser(Integer saleUserId,String date);

    boolean isWorkTime();

    boolean sendMsgForXs(Integer userId, BussinessChanceMessageVo messageVo);
    boolean sendMsg(Integer userId, BussinessChanceMessageVo messageVo);

    UserDto getUserByUserId(Integer userId);

    boolean sendInvoiceMsg(Integer userId,String msg);
    boolean sendMsg(Integer userId,String msg);


}
