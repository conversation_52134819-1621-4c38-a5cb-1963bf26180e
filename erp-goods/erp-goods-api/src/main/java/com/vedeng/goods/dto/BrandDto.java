package com.vedeng.goods.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 品牌
 * @TableName T_BRAND
 */
@Data
public class BrandDto implements Serializable {

    /**
     * 品牌ID
     */
    private Integer brandId;

    /**
     * ERP公司ID(T_COMPANY)
     */
    private Integer companyId;

    /**
     * 品牌性质 1国产 2进口
     */
    private Boolean brandNature;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌英文名
     */
    private String brandNameEn;

    /**
     * 生产企业
     */
    private String manufacturer;

    /**
     * 品牌链接
     */
    private String brandWebsite;

    /**
     * 品牌商
     */
    private String owner;

    /**
     * 商标图片域名
     */
    private String logoDomain;

    /**
     * 商标图片地址
     */
    private String logoUri;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 中文首字母
     */
    private String initialCn;

    /**
     * 英文首字母
     */
    private String initialEn;

    /**
     * 描述
     */
    private String description;

    /**
     * 0 ERP 1耗材商城
     */
    private Boolean source;

    /**
     * 创建时间
     */
    private Long addTime;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新时间
     */
    private Long modTime;

    /**
     * 更新人
     */
    private Integer updater;

    /**
     * 是否删除标志 0-未删除 | 1-已删除
     */
    private Boolean isDelete;

    /**
     * 删除原因
     */
    private String comments;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * OSS的唯一id
     */
    private String ossResourceId;

    /**
     * 原始的文件路径
     */
    private String originalFilepath;

    /**
     * 同步标志:0-未同步 1-同步成功 2-同步失败
     */
    private Integer synSuccess;

    /**
     * 耗时
     */
    private Long costTime;

    /**
     * 品牌中文名
     */
    private String brandNameCn;

    private static final long serialVersionUID = 1L;
}
