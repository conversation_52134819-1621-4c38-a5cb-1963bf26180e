<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/functions" prefix="fn" %>
<c:set var="title" value="调价列表" scope="application" />
<%@ include file="../../common/common.jsp"%>

<link rel="stylesheet" href="/webjars/ezadmin/plugins/layui/css/layui.css" media="all">

<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/pages/firstengage/first/index.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/select2.css" />
<script type="text/javascript" src='<%= basePath %>static/js/price/priceModifyRecord/priceModifyRecord_list.js?rnd=${resourceVersionKey}'></script>
<div class="main-container">
    <div class="searchfunc">
        <form method="post" id="search" action="<%=basePath%>/price/skuPriceModifyRecord/index.do?fromMenuFlag=${fromMenuFlag}" >
            <ul>
                <li>
                    <label class="infor_name">订货号</label>
                    <input type="text" class="input-middle" name="skuNoSearchString" id="skuNoSearchString" value="${skuPriceModifyRecordSearchDto.skuNoSearchString}"/>
                </li>
                <li>
                    <label class="infor_name">商品名称</label>
                    <input type="text" class="input-middle" name="skuNameSearchString" id="skuNameSearchString" value="${skuPriceModifyRecordSearchDto.skuNameSearchString}"/>
                </li>
                <li>
                    <label class="infor_name">品牌</label>
                    <input type="text" class="input-middle" name="brandNameSearchString" id="brandNameSearchString" value="${skuPriceModifyRecordSearchDto.brandNameSearchString}"/>
                </li>
                <li>
                    <label class="infor_name">产品归属</label>
                    <input type="text" class="input-middle" name="holderNameSearchString" id="holderNameSearchString" value="${skuPriceModifyRecordSearchDto.holderNameSearchString}"/>
                </li>
                <li>
                    <label class="infor_name" style="width: 105px;">调价方式</label>
                    <select class="input-middle" name="skuPriceModifyType" id="skuPriceModifyType" value="${skuPriceModifyRecordSearchDto.skuPriceModifyType}">
                        <option value="0" <c:if test="${skuPriceModifyRecordSearchDto.skuPriceModifyType eq 0}">selected="selected"</c:if>>全部</option>
                        <option value="1" <c:if test="${skuPriceModifyRecordSearchDto.skuPriceModifyType eq 1}">selected="selected"</c:if>>价格新增</option>
                        <option value="2" <c:if test="${skuPriceModifyRecordSearchDto.skuPriceModifyType eq 2}">selected="selected"</c:if>>价格调整</option>
                    </select>
                </li>
                <li>
                    <div class="infor_name">
                        <lable>调整时间</lable>
                    </div>
                    <div class="f_left inputfloat">
                        <div>
                            <input class="Wdate input-small mr0" type="text" placeholder="请选择时间" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',maxDate:'#F{$dp.$D(\'endTimeStr\')}'})"
                                   name="startTimeStr" id="startTimeStr" value="<date:date value ="${skuPriceModifyRecordSearchDto.startTimeStr}"/>"/><div class="gang">-</div>
                            <input class="Wdate input-small ml0" type="text" placeholder="请选择时间" autocomplete="off" onFocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',minDate:'#F{$dp.$D(\'startTimeStr\')}'})"
                                   name="endTimeStr" id="endTimeStr" onchange="changeDate(this);" value="<date:date value ="${skuPriceModifyRecordSearchDto.endTimeStr}"/>"/>
                        </div>
                        <div id="timeError"></div>
                    </div>
                </li>
                <li>
                    <div class="infor_name">
                        <label>调价类型</label>
                    </div>
                    <div class="f_left" style="min-width:140px;">
                        <select  name="isPriceChange" class="" multiple="multiple" id="organId">
                            <option value=""></option>
                            <c:if test="${fromMenuFlag eq 1}">
                                <shiro:hasPermission name="/price/skuPriceModifyRecord/isShowCostPrice.do">
                                    <option value="1">采购成本</option>
                                </shiro:hasPermission>
                            </c:if>
                            <option value="2">市场价</option>
                            <option value="3">经销价</option>
                            <option value="4">终端价</option>
                            <option value="5">集团价</option>
                        </select>
                    </div>
                </li>
                <c:if test="${fromMenuFlag ne 1}">
                    <li>
                        <label class="infor_name" style="width: 105px;">关联订单</label>
                        <select class="input-middle" name="isHaveAffectOrder" id="isHaveAffectOrder" value="${skuPriceModifyRecordSearchDto.isHaveAffectOrder}">
                            <option value="2" <c:if test="${skuPriceModifyRecordSearchDto.isHaveAffectOrder eq 2}">selected="selected"</c:if>>全部</option>
                            <option value="1" <c:if test="${skuPriceModifyRecordSearchDto.isHaveAffectOrder eq 1}">selected="selected"</c:if>>未处理</option>
                            <option value="0" <c:if test="${skuPriceModifyRecordSearchDto.isHaveAffectOrder eq 0}">selected="selected"</c:if>>已处理</option>
                        </select>
                    </li>
                </c:if>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
            </div>
        </form>
    </div>


    <div class="content">
        <div class="fixdiv">
            <div class="superdiv" style='width:2200px;'>
                <table class="table table-bordered table-striped table-condensed table-centered">
                    <thead>
                        <tr>
                            <th class="wid5">
                                <input type="checkbox"  name="b_checknox" autocomplete="off" onclick="selectAll(this);" >
                            </th>
                            <c:if test="${fromMenuFlag ne 1}">
                                <th class="wid5">可处理订单</th>
                            </c:if>
                            <th class="wid5">订货号</th>
                            <th class="wid15">商品名称</th>
                            <th class="wid5">品牌</th>
                            <th class="wid5">单位</th>
                            <th class="wid10">型号/规格</th>
                            <th class="wid5">调价方式</th>
                            <th class="wid10">调整时间</th>
                            <c:if test="${fromMenuFlag eq 1}">
                                <shiro:hasPermission name="/price/skuPriceModifyRecord/isShowCostPrice.do">
                                    <th class="wid5">采购成本</th>
                                </shiro:hasPermission>
                            </c:if>
                            <th class="wid5">市场价</th>
                            <th class="wid5">经销价</th>
                            <th class="wid5">终端价</th>
                            <th class="wid5">集团价</th>
                            <th class="wid5">电商价</th>
                            <th class="wid5">科研终端价</th>
                            <c:if test="${fromMenuFlag eq 1}">
                                <shiro:hasPermission name="/price/skuPriceModifyRecord/isShowCostPrice.do">
                                    <th class="wid5">原采购成本</th>
                                    <th class="wid5">成本价浮动</th>
                                </shiro:hasPermission>
                            </c:if>
                            <th class="wid5">原市场价</th>
                            <th class="wid5">市场价浮动</th>
                            <th class="wid5">原经销价</th>
                            <th class="wid5">经销价浮动</th>
                            <th class="wid5">原终端价</th>
                            <th class="wid5">终端价浮动</th>
                            <th class="wid5">原集团价</th>
                            <th class="wid5">集团价浮动</th>
                            <th class="wid5">原电商价</th>
                            <th class="wid5">电商价浮动</th>
                            <th class="wid5">原科研终端价</th>
                            <th class="wid5">科研终端价浮动</th>
                        </tr>
                    </thead>
                    <tbody>
                    <c:choose>
                        <c:when test="${not empty skuPriceModifyRecordsList}">
                            <c:forEach var="skuPriceModifyRecord" items="${skuPriceModifyRecordsList}" varStatus="num">
                                <tr>
                                    <td>
                                        <input type="checkbox" name="checkOne" value="${skuPriceModifyRecord.skuPriceModifyRecordId}" autocomplete="off">
                                        <input type="hidden" name="skuPriceModifyRecordId" value="${skuPriceModifyRecord.skuPriceModifyRecordId}" >
                                    </td>
                                    <c:if test="${fromMenuFlag eq 0}">
                                        <td>
                                            <c:choose>
                                                <c:when test="${skuPriceModifyRecord.isHaveUnDealAffectOrder eq 0}">无</c:when>
                                                <c:when test="${skuPriceModifyRecord.isHaveUnDealAffectOrder eq 1}">
                                                    <a href="javascript:void(0);" onclick="openAffectOrderListView(${skuPriceModifyRecord.skuPriceModifyRecordId})" >有</a>
                                                </c:when>
                                            </c:choose>
                                        </td>
                                   </c:if>
                                    <td>${skuPriceModifyRecord.skuNo}</td>
                                    <td>
                                        <a class="addtitle" href="javascript:void(0);" tabtitle='{"num":"viewSkuDetail${skuPriceModifyRecord.skuId}", "link":"./goods/vgoods/viewSku.do?skuId=${skuPriceModifyRecord.skuId}&pageType=1", "title":"商品整合查询页"}'>${skuPriceModifyRecord.skuName}</a>
                                    </td>
                                    <td>${skuPriceModifyRecord.brandName}</td>
                                    <td>${skuPriceModifyRecord.unitName}</td>
                                    <td>${skuPriceModifyRecord.model}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${skuPriceModifyRecord.modPriceType == 1}">首次新增</c:when>
                                            <c:when test="${skuPriceModifyRecord.modPriceType == 2}">价格调整</c:when>
                                        </c:choose>
                                    </td>
                                    <td>${skuPriceModifyRecord.modifyTimeString}</td>
                                    <c:if test="${fromMenuFlag eq 1}">
                                        <shiro:hasPermission name="/price/skuPriceModifyRecord/isShowCostPrice.do">
                                            <td>${skuPriceModifyRecord.afterModPurchaseCosts}</td>
                                        </shiro:hasPermission>

                                    </c:if>
                                    <td>${skuPriceModifyRecord.afterModMarketPrice}</td>
                                    <td>${skuPriceModifyRecord.afterModDistributionPrice}</td>
                                    <td>${skuPriceModifyRecord.afterModTerminalPrice}</td>
                                    <td>${skuPriceModifyRecord.afterModGroupPrice}</td>
                                    <td>${skuPriceModifyRecord.afterModElectronicCommercePrice}</td>
                                    <td>${skuPriceModifyRecord.afterModResearchTerminalPrice}</td>
                                    <c:if test="${fromMenuFlag eq 1}">
                                        <shiro:hasPermission name="/price/skuPriceModifyRecord/isShowCostPrice.do">
                                            <td>${skuPriceModifyRecord.beforeModPurchaseCosts}</td>
                                            <td>
                                                <c:choose>
                                                <c:when test="${skuPriceModifyRecord.modPriceType == 2
                                                and skuPriceModifyRecord.purchaseCostsFluctuationDreaction ne 0}">
                                                <c:if test="${skuPriceModifyRecord.purchaseCostsFluctuationDreaction eq 1}">
                                                <span style="color: red"> ↑
                                                    </c:if>
                                                    <c:if test="${skuPriceModifyRecord.purchaseCostsFluctuationDreaction eq -1}">
                                                        <span style="color: green"> ↓
                                                    </c:if>
                                                            ${skuPriceModifyRecord.purchaseCostsFluctuation}%
                                                    </span>
                                                </c:when>
                                                <c:otherwise>
                                                    <span> -- </span>
                                                </c:otherwise>
                                            </c:choose>
                                            </td>
                                        </shiro:hasPermission>
                                    </c:if>

                                    <td>${skuPriceModifyRecord.beforeModMarketPrice}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${skuPriceModifyRecord.modPriceType == 2 and skuPriceModifyRecord.marketPriceFluctuationDreaction ne 0}">
                                                <c:if test="${skuPriceModifyRecord.marketPriceFluctuationDreaction eq 1}">
                                                    <span style="color: red"> ↑
                                                </c:if>
                                                <c:if test="${skuPriceModifyRecord.marketPriceFluctuationDreaction eq -1}">
                                                    <span style="color: green"> ↓
                                                </c:if>
                                                        ${skuPriceModifyRecord.marketPriceFluctuation}%
                                                    </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span> -- </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>

                                    <td>${skuPriceModifyRecord.beforeModDistributionPrice}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${skuPriceModifyRecord.modPriceType == 2 and skuPriceModifyRecord.distributionPriceFluctuationDreaction ne 0}">
                                                <c:if test="${skuPriceModifyRecord.distributionPriceFluctuationDreaction eq 1}">
                                                    <span style="color: red;"> ↑
                                                </c:if>
                                                <c:if test="${skuPriceModifyRecord.distributionPriceFluctuationDreaction eq -1}">
                                                    <span style="color: green;"> ↓
                                                </c:if>
                                                        ${skuPriceModifyRecord.distributionPriceFluctuation}%
                                                    </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span> -- </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>

                                    <td>${skuPriceModifyRecord.beforeModTerminalPrice}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${skuPriceModifyRecord.modPriceType == 2 and skuPriceModifyRecord.terminalPriceFluctuationDreaction ne 0}">
                                                <c:if test="${skuPriceModifyRecord.terminalPriceFluctuationDreaction eq 1}">
                                                    <span style="color: red"> ↑
                                                </c:if>
                                                <c:if test="${skuPriceModifyRecord.terminalPriceFluctuationDreaction eq -1}">
                                                    <span style="color: green"> ↓
                                                </c:if>
                                                        ${skuPriceModifyRecord.terminalPriceFluctuation}%
                                                    </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span> -- </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>

                                    <td>${skuPriceModifyRecord.beforeModGroupPrice}</td>
                                    <td>
                                        <c:choose>
                                            <c:when test="${skuPriceModifyRecord.modPriceType == 2 and skuPriceModifyRecord.groupPriceFluctuationDreaction ne 0}">
                                                <c:if test="${skuPriceModifyRecord.groupPriceFluctuationDreaction eq 1}">
                                                    <span style="color: red"> ↑
                                                </c:if>
                                                <c:if test="${skuPriceModifyRecord.groupPriceFluctuationDreaction eq -1}">
                                                    <span style="color: green"> ↓
                                                </c:if>
                                                        ${skuPriceModifyRecord.groupPriceFluctuation}%
                                                    </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span> -- </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>

                                    <td>${skuPriceModifyRecord.beforeModElectronicCommercePrice}</td>
                                    <td>
                                        <c:choose>
                                        <c:when test="${skuPriceModifyRecord.modPriceType == 2 and skuPriceModifyRecord.electronicCommercePriceFluctuationDreaction ne 0}">
                                        <c:if test="${skuPriceModifyRecord.electronicCommercePriceFluctuationDreaction eq 1}">
                                        <span style="color: red"> ↑
                                                </c:if>
                                                <c:if test="${skuPriceModifyRecord.electronicCommercePriceFluctuationDreaction eq -1}">
                                                    <span style="color: green"> ↓
                                                </c:if>
                                                        ${skuPriceModifyRecord.electronicCommercePriceFluctuation}%
                                                    </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span> -- </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>

                                    <td>${skuPriceModifyRecord.beforeModResearchTerminalPrice}</td>
                                    <td>
                                        <c:choose>
                                        <c:when test="${skuPriceModifyRecord.modPriceType == 2 and skuPriceModifyRecord.researchTerminalPriceFluctuationDreaction ne 0}">
                                        <c:if test="${skuPriceModifyRecord.researchTerminalPriceFluctuationDreaction eq 1}">
                                        <span style="color: red"> ↑
                                                </c:if>
                                                <c:if test="${skuPriceModifyRecord.researchTerminalPriceFluctuationDreaction eq -1}">
                                                    <span style="color: green"> ↓
                                                </c:if>
                                                        ${skuPriceModifyRecord.researchTerminalPriceFluctuation}%
                                                    </span>
                                            </c:when>
                                            <c:otherwise>
                                                <span> -- </span>
                                            </c:otherwise>
                                        </c:choose>
                                    </td>
                                </tr>
                            </c:forEach>
                        </c:when>
                        <c:otherwise>
                            <tr><td colspan="20">暂无数据</td></tr>
                        </c:otherwise>
                    </c:choose>
                    <span style="display:none;">
					    <div class="title-click nobor  pop-new-data" id="affectorderview"></div>
				    </span>
                    </tbody>
                </table>
            </div>
        </div>
        <tags:page page="${page}" />
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>
<script type="text/javascript" src='${pageContext.request.contextPath}/static/js/select2.js'></script>
<script type="text/javascript">
    $(document).ready(function() {
        if(${skuPriceModifyRecordSearchDto.isPriceChange ne null}){
            $("#organId").val(${skuPriceModifyRecordSearchDto.isPriceChange}).trigger('change');
        }
    });
</script>
