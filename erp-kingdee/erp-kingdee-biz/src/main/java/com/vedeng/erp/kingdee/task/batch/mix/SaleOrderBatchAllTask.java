package com.vedeng.erp.kingdee.task.batch.mix;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.*;
import com.vedeng.erp.kingdee.task.batch.TaskBatchHandle;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/1/30 14:11
 *
 * 销售正逆项流程
 */
@JobHandler(value = "SaleOrderBatchAllTask")
@Component
public class SaleOrderBatchAllTask extends AbstractJobHandler {

    @Autowired
    private JobLauncher jobLauncher;

    @Autowired
    private SaleOrderBatchJob saleOrderBatchJob;

    @Autowired
    private SaleOrderInstallServiceRecordBatchJob installServiceRecordFlowJob;

    @Autowired
    private SaleSettlementAdjustmentBatchJob saleSettlementAdjustmentBatchJob;

    @Autowired
    private SaleOrderAfterSaleExchangeBatchJob saleOrderAfterSaleExchangeBatchJob;

    @Autowired
    private SaleOrderAfterSaleBatchJob batchJob;

    @Autowired
    private InvoiceRollbackBatchJob invoiceRollbackBatchJob;
    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {

        XxlJobLogger.log("=====BlueValidInvoiceRollbackTask start param:{}====" , param);
        JobParameters jobParametersInvoice = new TaskBatchHandle().buildJobParameters(param);
        Job invoiceJob = invoiceRollbackBatchJob.rollbackBlueValidInvoiceJob();
        jobLauncher.run(invoiceJob, jobParametersInvoice);
        XxlJobLogger.log("=====BlueValidInvoiceRollbackTask end====");

        XxlJobLogger.log("==================销售订单实物商品流程batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = saleOrderBatchJob.saleOrderPhysicalGoodsFlowJob();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================销售订单实物商品流程batch结束====================");

        XxlJobLogger.log("====================销售换货售后流程开始==================");
        Job saleOrderAfterSaleExchangeJob = saleOrderAfterSaleExchangeBatchJob.saleOrderAfterSaleExchangeFlowJob();
        jobLauncher.run(saleOrderAfterSaleExchangeJob, jobParameters);
        XxlJobLogger.log("====================销售换货售后流程结束==================");

        XxlJobLogger.log("====================销售单售后退货入库单对接金蝶==================");
        Job saleOrderAfterSaleBatchJob = batchJob.saleOrderAfterSaleFlowJob();
        JobParameters saleOrderAfterSaleBatchJobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(saleOrderAfterSaleBatchJob, saleOrderAfterSaleBatchJobParameters);
        XxlJobLogger.log("====================销售单售后退货入库单对接金蝶==================");

        XxlJobLogger.log("====================安调回单对接金蝶==================");
        Job installServiceRecordJob = installServiceRecordFlowJob.installServiceRecordFlowJob();
        JobParameters installJobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(installServiceRecordJob, installJobParameters);
        XxlJobLogger.log("====================安调回单对接金蝶==================");

        XxlJobLogger.log("====================调整单对接金蝶==================");
        Job saleAdjustmentFlowJob = saleSettlementAdjustmentBatchJob.saleAdjustmentFlowJob();
        JobParameters saleAdjustmentFlowJobParameters = new TaskBatchHandle().buildJobParameters(param);
        jobLauncher.run(saleAdjustmentFlowJob, saleAdjustmentFlowJobParameters);
        XxlJobLogger.log("====================调整单对接金蝶==================");

        return SUCCESS;
    }
}
