package com.vedeng.api.standard.converter;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 响应转换工具类
 * 提供通用的字段提取和类型转换方法
 * 
 * <AUTHOR>
 * @version 1.0
 * @since 2024-06-27
 */
public class ResponseUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ResponseUtils.class);
    
    /**
     * 从Map中提取Integer值，支持多个候选字段名
     *
     * @param dataMap 数据Map
     * @param fieldNames 候选字段名数组
     * @return Integer值，如果都不存在则返回null
     */
    public static Integer extractInteger(Map<String, Object> dataMap, String... fieldNames) {
        if (dataMap == null || fieldNames == null) {
            return null;
        }

        for (String fieldName : fieldNames) {
            Object value = dataMap.get(fieldName);
            if (value != null) {
                return Convert.toInt(value, null);
            }
        }
        return null;
    }
    
    /**
     * 从Map中提取String值，支持多个候选字段名
     * 
     * @param dataMap 数据Map
     * @param fieldNames 候选字段名数组
     * @return String值，如果都不存在则返回null
     */
    public static String extractString(Map<String, Object> dataMap, String... fieldNames) {
        if (dataMap == null || fieldNames == null) {
            return null;
        }
        
        for (String fieldName : fieldNames) {
            Object value = dataMap.get(fieldName);
            if (value != null) {
                return value.toString();
            }
        }
        return null;
    }
    
    /**
     * 从Map中提取BigDecimal值，支持多个候选字段名
     *
     * @param dataMap 数据Map
     * @param fieldNames 候选字段名数组
     * @return BigDecimal值，如果都不存在则返回null
     */
    public static BigDecimal extractBigDecimal(Map<String, Object> dataMap, String... fieldNames) {
        if (dataMap == null || fieldNames == null) {
            return null;
        }

        for (String fieldName : fieldNames) {
            Object value = dataMap.get(fieldName);
            if (value != null) {
                return Convert.toBigDecimal(value, null);
            }
        }
        return null;
    }
    

    
    /**
     * 检查viewName是否匹配成功条件
     * 
     * @param viewName 视图名称
     * @param successViewNames 成功视图名称数组
     * @return 是否匹配成功条件
     */
    public static boolean isSuccessViewName(String viewName, String... successViewNames) {
        if (viewName == null || successViewNames == null) {
            return false;
        }
        
        for (String successViewName : successViewNames) {
            if (successViewName != null && 
                (viewName.equals(successViewName) || viewName.contains(successViewName))) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查viewName是否匹配失败条件
     * 
     * @param viewName 视图名称
     * @param failureViewNames 失败视图名称数组
     * @return 是否匹配失败条件
     */
    public static boolean isFailureViewName(String viewName, String... failureViewNames) {
        if (viewName == null || failureViewNames == null) {
            return false;
        }
        
        for (String failureViewName : failureViewNames) {
            if (failureViewName != null && 
                (viewName.equals(failureViewName) || viewName.contains(failureViewName))) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 从Map中安全获取错误消息
     *
     * @param dataMap 数据Map
     * @param defaultMessage 默认错误消息
     * @return 错误消息
     */
    public static String extractErrorMessage(Map<String, Object> dataMap, String defaultMessage) {
        if (dataMap == null) {
            return defaultMessage;
        }

        // 尝试多种可能的错误消息字段名
        String errorMsg = extractString(dataMap, "errorMsg", "error", "message", "msg");
        return StrUtil.isNotBlank(errorMsg) ? errorMsg : defaultMessage;
    }
    
    /**
     * 检查数据Map是否为空或无效
     * 
     * @param dataMap 数据Map
     * @return 是否为空或无效
     */
    public static boolean isEmptyDataMap(Map<String, Object> dataMap) {
        return dataMap == null || dataMap.isEmpty();
    }
}
