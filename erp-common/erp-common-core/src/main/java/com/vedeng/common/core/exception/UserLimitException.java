package com.vedeng.common.core.exception;

import com.vedeng.common.core.base.BaseException;
import com.vedeng.common.core.base.BaseResponseCode;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/7/20
 */
public class UserLimitException extends BaseException {

    public UserLimitException(BaseResponseCode code) {
        super(code);
    }

    public UserLimitException(String message) {
        super(message);
    }

    public UserLimitException(BaseResponseCode status, Object data) {
        super(status, data);
    }

    public UserLimitException(Integer code, String message) {
        super(code, message);
    }

    public UserLimitException(Integer code, String message, Object data) {
        super(code, message, data);
    }

    public UserLimitException(String message, Throwable cause) {
        super(message, cause);
    }
}
