package com.vedeng.temporal.config;

import com.vedeng.temporal.workflow.activity.impl.CompanySequenceActivityImpl;
import com.vedeng.temporal.workflow.activity.impl.InventoryReceiptActivityImpl;
import com.vedeng.temporal.workflow.activity.impl.InvoiceEntryActivityImpl;
import com.vedeng.temporal.workflow.activity.impl.PaymentActivityImpl;
import com.vedeng.temporal.workflow.activity.impl.PurchaseOrderActivityImpl;
import com.vedeng.temporal.workflow.impl.MultiCompanyBusinessWorkflowImpl;
import com.vedeng.temporal.workflow.activity.impl.SalesOrderActivityImpl;
import com.vedeng.temporal.polling.universal.activity.impl.UniversalPollingActivityImpl;
import com.google.protobuf.util.Durations;
import io.temporal.api.workflowservice.v1.DescribeNamespaceRequest;
import io.temporal.api.workflowservice.v1.DescribeNamespaceResponse;
import io.temporal.api.workflowservice.v1.RegisterNamespaceRequest;
import io.temporal.api.workflowservice.v1.UpdateNamespaceRequest;
import io.temporal.api.namespace.v1.NamespaceConfig;
import io.temporal.api.namespace.v1.UpdateNamespaceInfo;
import io.temporal.client.WorkflowClient;
import io.temporal.client.WorkflowClientOptions;
import io.temporal.serviceclient.WorkflowServiceStubs;
import io.temporal.serviceclient.WorkflowServiceStubsOptions;
import io.temporal.worker.Worker;
import io.temporal.worker.WorkerFactory;
import io.temporal.worker.WorkerOptions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;

/**
 * Temporal工作流引擎配置类
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-20
 */
@Configuration
@ComponentScan(basePackages = "com.vedeng.temporal")
@Slf4j
public class TemporalConfig {

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private TemporalProperties temporalProperties;



    /**
     * 创建Temporal服务连接
     */
    @Bean
    public WorkflowServiceStubs workflowServiceStubs() {
        String serviceUrl = temporalProperties.getServer().getHost() + ":" + temporalProperties.getServer().getPort();
        log.info("创建Temporal服务连接，URL: {}", serviceUrl);

        try {
            return WorkflowServiceStubs.newServiceStubs(
                WorkflowServiceStubsOptions.newBuilder()
                    .setTarget(serviceUrl)
                    .build()
            );
        } catch (Exception e) {
            log.error("连接Temporal服务失败，URL: {}, 错误: {}", serviceUrl, e.getMessage());
            throw new RuntimeException("无法连接到Temporal服务器: " + serviceUrl, e);
        }
    }

    /**
     * 创建Temporal工作流客户端
     */
    @Bean
    public WorkflowClient workflowClient(WorkflowServiceStubs serviceStubs) {
        log.info("创建Temporal工作流客户端，命名空间: {}", temporalProperties.getNamespace().getName());
        return WorkflowClient.newInstance(serviceStubs,
            WorkflowClientOptions.newBuilder()
                .setNamespace(temporalProperties.getNamespace().getName())
                .build());
    }

    /**
     * 创建Worker工厂
     */
    @Bean
    public WorkerFactory workerFactory(WorkflowClient workflowClient) {
        log.info("创建Temporal Worker工厂");
        return WorkerFactory.newInstance(workflowClient);
    }

    /**
     * 配置多公司业务Worker实例
     * 处理多公司间的业务流程自动化，包括采购、销售、库存、发票等所有业务流程
     */
    @Bean
    public Worker multiCompanyBusinessWorker(WorkerFactory workerFactory) {
        log.info("创建多公司业务Worker实例，任务队列: {}", temporalProperties.getTaskQueue().getMultiCompany());

        // 配置Worker选项 - 使用统一配置
        WorkerOptions options = WorkerOptions.newBuilder()
            .setMaxConcurrentActivityExecutionSize(temporalProperties.getWorker().getMaxConcurrentActivityExecutions())
            .setMaxConcurrentWorkflowTaskExecutionSize(temporalProperties.getWorker().getMaxConcurrentWorkflowTaskExecutions())
            .build();

        return workerFactory.newWorker(temporalProperties.getTaskQueue().getMultiCompany(), options);
    }

    /**
     * 配置 RestTemplate Bean，用于 Temporal 模块的 HTTP 调用
     */
    @Bean
    public RestTemplate restTemplate() {
        log.info("创建RestTemplate Bean用于Temporal HTTP调用");
        return new RestTemplate();
    }



    /**
     * 初始化 Temporal Namespace
     * 在 WorkflowServiceStubs 创建后立即执行
     */
    @Bean
    public String temporalNamespaceInitializer(WorkflowServiceStubs serviceStubs) throws Exception {
        String namespaceName = temporalProperties.getNamespace().getName();
        log.info("初始化Temporal namespace: {}", namespaceName);

        if (!temporalProperties.getNamespace().isAutoCreate()) {
            log.info("自动创建namespace已禁用，跳过namespace初始化");
            return namespaceName;
        }

        try {
            // 检查namespace是否存在
            if (!namespaceExists(serviceStubs, namespaceName)) {
                createNamespace(serviceStubs, namespaceName);
            } else {
                log.info("Temporal namespace已存在: {}", namespaceName);
                // 检查并更新namespace配置（如果启用了自动更新）
                if (temporalProperties.getNamespace().isAutoUpdate()) {
                    updateNamespaceIfNeeded(serviceStubs, namespaceName);
                } else {
                    log.debug("自动更新namespace已禁用，跳过配置检查");
                }
            }

            return namespaceName;

        } catch (Exception e) {
            log.error("Temporal namespace初始化失败: {}", e.getMessage(), e);
            log.warn("系统将继续启动，但Temporal功能可能不可用");
            return namespaceName;
        }
    }

    /**
     * 初始化Worker并启动
     * 在所有组件创建完成后执行
     */
    @Bean
    public String workerInitializer(WorkerFactory workerFactory,
                                   Worker multiCompanyBusinessWorker,
                                   String temporalNamespaceInitializer) {
        log.info("初始化Temporal Worker");

        try {
            // 注册工作流和活动到Worker
            registerWorkflowsAndActivities(multiCompanyBusinessWorker);

            // 启动Worker
            workerFactory.start();
            log.info("Temporal Worker启动成功");

            return "Worker初始化完成";

        } catch (Exception e) {
            log.error("Temporal Worker初始化失败", e);
            throw new RuntimeException("Worker初始化失败", e);
        }
    }

    /**
     * 注册工作流和活动到Worker
     */
    private void registerWorkflowsAndActivities(Worker multiCompanyBusinessWorker) {
        try {
            log.info("开始注册工作流和活动到多公司业务Worker");

            // 注册工作流实现类（只有主工作流）
            multiCompanyBusinessWorker.registerWorkflowImplementationTypes(
                    MultiCompanyBusinessWorkflowImpl.class
            );

            // 注册活动实现类（使用Spring容器中的Bean实例）
            // 只注册原子 Activity，符合 Temporal 最佳实践
            multiCompanyBusinessWorker.registerActivitiesImplementations(
                    getBean(CompanySequenceActivityImpl.class),
                    getBean(SalesOrderActivityImpl.class),
                    getBean(PurchaseOrderActivityImpl.class),
                    getBean(PaymentActivityImpl.class),
                    getBean(InventoryReceiptActivityImpl.class),
                    getBean(InvoiceEntryActivityImpl.class),
                    getBean(UniversalPollingActivityImpl.class)
            );

            log.info("工作流和活动注册完成");

        } catch (Exception e) {
            log.error("注册工作流和活动异常", e);
            throw new RuntimeException("工作流和活动注册失败", e);
        }
    }

    /**
     * 从Spring容器获取Bean实例
     */
    private <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    /**
     * 检查 namespace 是否存在
     */
    private boolean namespaceExists(WorkflowServiceStubs serviceStubs, String namespace) {
        try {
            DescribeNamespaceRequest request = DescribeNamespaceRequest.newBuilder()
                    .setNamespace(namespace)
                    .build();

            serviceStubs.blockingStub().describeNamespace(request);
            return true;

        } catch (Exception e) {
            log.debug("Namespace不存在或检查失败: {}", namespace);
            return false;
        }
    }

    /**
     * 检查并更新 namespace 配置（如果需要）
     */
    private void updateNamespaceIfNeeded(WorkflowServiceStubs serviceStubs, String namespace) {
        try {
            // 获取当前namespace信息
            DescribeNamespaceRequest request = DescribeNamespaceRequest.newBuilder()
                    .setNamespace(namespace)
                    .build();

            DescribeNamespaceResponse response = serviceStubs.blockingStub().describeNamespace(request);

            // 检查保留期是否需要更新
            long currentRetentionDays = response.getConfig().getWorkflowExecutionRetentionTtl().getSeconds() / (24 * 60 * 60);
            int configuredRetentionDays = temporalProperties.getNamespace().getRetentionDays();

            if (currentRetentionDays != configuredRetentionDays) {
                log.info("检测到namespace保留期配置变更: {} -> {}天，开始更新", currentRetentionDays, configuredRetentionDays);
                updateNamespace(serviceStubs, namespace);
            } else {
                log.debug("Namespace配置无需更新，当前保留期: {}天", currentRetentionDays);
            }

        } catch (Exception e) {
            log.warn("检查namespace配置时发生异常: {}", e.getMessage());
        }
    }

    /**
     * 更新 namespace 配置
     */
    private void updateNamespace(WorkflowServiceStubs serviceStubs, String namespace) {
        try {
            log.info("开始更新Temporal namespace配置: {}", namespace);

            UpdateNamespaceRequest request = UpdateNamespaceRequest.newBuilder()
                    .setNamespace(namespace)
                    .setUpdateInfo(UpdateNamespaceInfo.newBuilder()
                            .setDescription(temporalProperties.getNamespace().getDescription())
                            .build())
                    .setConfig(NamespaceConfig.newBuilder()
                            .setWorkflowExecutionRetentionTtl(Durations.fromDays(temporalProperties.getNamespace().getRetentionDays()))
                            .build())
                    .build();

            serviceStubs.blockingStub().updateNamespace(request);
            log.info("成功更新Temporal namespace: {}, 新保留期: {}天", namespace, temporalProperties.getNamespace().getRetentionDays());

        } catch (Exception e) {
            log.error("更新namespace配置失败: {}", namespace, e);
            // 不抛出异常，允许系统继续启动
        }
    }

    /**
     * 创建 namespace
     */
    private void createNamespace(WorkflowServiceStubs serviceStubs, String namespace) {
        try {
            log.info("开始创建Temporal namespace: {}", namespace);

            RegisterNamespaceRequest request = RegisterNamespaceRequest.newBuilder()
                    .setNamespace(namespace)
                    .setWorkflowExecutionRetentionPeriod(Durations.fromDays(temporalProperties.getNamespace().getRetentionDays()))
                    .setDescription(temporalProperties.getNamespace().getDescription())
                    .build();

            serviceStubs.blockingStub().registerNamespace(request);
            log.info("成功创建Temporal namespace: {}, 保留期: {}天, 描述: {}",
                    namespace, temporalProperties.getNamespace().getRetentionDays(), temporalProperties.getNamespace().getDescription());

            // 等待namespace创建完成
            Thread.sleep(3000);

            // 验证创建结果
            if (namespaceExists(serviceStubs, namespace)) {
                log.info("Namespace创建验证成功: {}", namespace);
            } else {
                log.warn("Namespace创建验证失败，可能需要更长时间生效: {}", namespace);
            }

        } catch (Exception e) {
            log.error("创建namespace失败: {}", namespace, e);
            throw new RuntimeException("创建namespace失败: " + e.getMessage(), e);
        }
    }

    /**
     * 应用启动后的初始化
     */
    @PostConstruct
    public void postConstruct() {
        log.info("TemporalConfig 初始化完成");
    }

    /**
     * 应用关闭时的清理工作
     */
    @PreDestroy
    public void preDestroy() {
        log.info("TemporalConfig 开始清理资源");
        try {
            // 获取 WorkerFactory 和 WorkflowServiceStubs 进行清理
            WorkerFactory workerFactory = applicationContext.getBean(WorkerFactory.class);
            WorkflowServiceStubs serviceStubs = applicationContext.getBean(WorkflowServiceStubs.class);

            // 关闭 WorkerFactory
            if (workerFactory != null) {
                log.info("正在关闭Temporal WorkerFactory...");
                workerFactory.shutdown();
                log.info("Temporal WorkerFactory关闭成功");
            }

            // 关闭服务连接
            if (serviceStubs != null) {
                log.info("正在关闭Temporal服务连接...");
                serviceStubs.shutdown();
                log.info("Temporal服务连接关闭成功");
            }

            log.info("TemporalConfig 资源清理完成");
        } catch (Exception e) {
            log.error("TemporalConfig 资源清理失败", e);
        }
    }
}
