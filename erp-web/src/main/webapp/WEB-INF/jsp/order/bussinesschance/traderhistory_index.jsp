<%@ page language="java" contentType="text/html; charset=UTF-8"
	pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="历史商机选择页" scope="application" />
<%@ include file="../../common/common.jsp"%>
<c:if test="${not empty method && method == 'bussinesschance' }">
	<%@ include file="../../trader/customer/customer_tag.jsp"%>
</c:if>
<style>
.customer{
	margin-left: 10px;
}

</style>
<div class="searchfunc" <c:if test="${not empty method}">style="margin-top: -10px;"</c:if>>
	<form method="post"
		action="${pageContext.request.contextPath}/order/bussinesschance/traderHistoryBussinessIndex.do"
		id="search">
		<ul>
			<li><label class="searchItem infor_name">商机编号</label> <input type="text"
				class="input-middle" name="bussinessChanceNo" id="bussinessChanceNo"
				value="${bussinessChanceVo.bussinessChanceNo }" /></li>

			<li><label class="searchItem infor_name">商机类型</label> <select
				class="input-middle" name="type">
					<option value="0">全部</option>
					<c:forEach items="${typeList }" var="type">
						<option value="${type.sysOptionDefinitionId }"
							<c:if test="${type.sysOptionDefinitionId == bussinessChanceVo.type}">selected="selected"</c:if>>${type.title }</option>
					</c:forEach>
			</select></li>

			<li><label class="searchItem infor_name">商机来源</label> <select
				class="input-middle" name="source">
					<option value="0">全部</option>
					<c:forEach items="${sourceList }" var="type">
						<option value="${type.sysOptionDefinitionId }"
							<c:if test="${type.sysOptionDefinitionId == bussinessChanceVo.source}">selected="selected"</c:if>>${type.title }</option>
					</c:forEach>
			</select></li>

			<li><label class="searchItem infor_name">联系方式</label> <input type="text"
				class="input-middle" name="traderContactName" id="traderContactName"
				value="${bussinessChanceVo.traderContactName }"
				placeholder="联系人/手机号/电话/其他联系方式" /></li>

			<li><label class="searchItem infor_name">询价产品</label> <input type="text"
				class="input-middle" name="content" id="content"
				value="${bussinessChanceVo.content }" /></li>

			<li>
			    <label class="searchItem infor_name">预计成单时间</label>
			    <input class="Wdate f_left input-smaller96 m0" type="text"
				placeholder="请选择日期"
				onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'cdendtime\')}'})" autocomplete="off"
				name="cdstarttime" id="cdstarttime"
				value="${bussinessChanceVo.cdstarttime }">
				<div class="f_left ml1 mr1 mt4">-</div> <input
				class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
				onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'cdstarttime\')}'})"
				name="cdendtime" id="cdendtime" value="${bussinessChanceVo.cdendtime }">
			</li>
			<li>
				<label class="searchItem infor_name">创建时间</label>
				<input class="Wdate f_left input-smaller96 m0" type="text"
				placeholder="请选择日期"
				onClick="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endtime\')}'})"
				name="starttime" id="starttime"
				value="${bussinessChanceVo.starttime }">
				<div class="f_left ml1 mr1 mt4">-</div> <input
				class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
				onClick="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'starttime\')}'})"
				name="endtime" id="endtime" value="${bussinessChanceVo.endtime }">
			</li>

		</ul>
		<input type="hidden" id="status" name="status" value="${bussinessChanceVo.status }"/>
		<input type="hidden" name="traderId" value="${bussinessChanceVo.traderId}">
		<div class="tcenter">
			<span class="bt-small bg-light-blue bt-bg-style mr20 "
				onclick="search();" id="searchSpan">搜索</span>
			<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="reset();">重置</span>
		</div>
	</form>
</div>
<div class="content">
	<div class="fixdiv">
		<div class="superdiv">
			<table
				class="table table-bordered table-striped table-condensed table-centered" style="table-layout: fixed;">
				<thead>
					<tr>
						<th class="th-config wid5">选择</th>
						<th class="th-config wid7">商机编号</th>
						<th class="th-config wid7">商机类型</th>
						<th class="th-config wid7">商机来源</th>
						<th class="th-config wid8">咨询入口</th>
						<th class="th-config wid8">功能</th>
						<th class="th-config wid9">商机等级</th>
						<th class="th-config wid13">预计金额</th>
						<th class="th-config wid9">成单几率</th>
						<th class="th-config wid10">分配销售部门</th>
						<th class="th-config wid15">询价产品</th>
						<th class="th-config wid7">商机状态</th>
						<th class="th-config wid8">报价单号</th>
						<th class="th-config wid10">分配时间</th>
						<th class="th-config wid12">商机初次查看时间</th>
					</tr>
				</thead>
				<tbody>
					<c:if test="${not empty bussinessChanceList}">
						<c:forEach items="${bussinessChanceList }" var="bussinessChance">
							<tr>
								<td>
									<input type="radio" name="checked" value="${bussinessChance.bussinessChanceId}" class="J-quote-item"/>
								</td>
								<td>
									<div class="font-blue">
										<a class="addtitle" href="javascript:void(0);" tabTitle='{"num":"view<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>",
												"link":"./order/bussinesschance/toSalesDetailPage.do?bussinessChanceId=${bussinessChance.bussinessChanceId}&traderId=${bussinessChance.traderId }",
												"title":"销售商机详情"}'>${bussinessChance.bussinessChanceNo }</a>
									</div>
								</td>
								<td>${bussinessChance.typeName }</td>
								<td>${bussinessChance.sourceName }</td>
								<td>${bussinessChance.entranceName}</td>
								<td>${bussinessChance.functionName}</td>
								<td>${bussinessChance.bussinessLevelStr} </td>
								<td>${bussinessChance.amount }</td>
								<td>${bussinessChance.orderRateStr } </td>
								<td>${bussinessChance.orgName }</td>
								<td style="word-wrap:break-word;">${bussinessChance.content }</td>
								<td>
									<c:choose>
											<c:when test="${bussinessChance.status eq '0'}">
					                    		<span class="warning-color1">未处理</span>
					                    	</c:when>
											<c:when test="${bussinessChance.status eq '1'}">
				                    	<span class="success-color1">报价中</span>
				                    	</c:when>
											<c:when test="${bussinessChance.status eq '2'}">
				                    	<span class="success-color1">已报价</span>
				                    	</c:when>
											<c:when test="${bussinessChance.status eq '3'}">
				                    	<span class="success-color1">已订单</span>
				                    	</c:when>
											<c:when test="${bussinessChance.status eq '4'}">
				                    	已关闭
				                    	</c:when>
									</c:choose>
								</td>
								<td>
											<a class="addtitle"
												tabtitle="{&quot;num&quot;:&quot;viewQuote3<%=System.currentTimeMillis() + (int)(Math.random()*10000) %>&quot;,&quot;link&quot;:&quot;./order/quote/getQuoteDetail.do?quoteorderId=${bussinessChance.quoteorderId}&viewType=2&quot;,&quot;title&quot;:&quot;报价信息&quot;}">${bussinessChance.quoteorderNo}</a>
								</td>
								<td><date:date value="${bussinessChance.assignTimeStr} " /></td>
								<td><date:date value="${bussinessChance.firstViewTimeStr} " /></td>
							</tr>
						</c:forEach>
					</c:if>
					<c:if test="${empty bussinessChanceList }">
						<!-- 查询无结果弹出 -->
						<tr>
							<td colspan="14">查询无结果！</td>
						</tr>
					</c:if>
				</tbody>
			</table>
		</div>
	</div>
	<div>
		<div class="sucsess-ok J-select-quote" style="display: inline-block">选择</div>
		<tags:page page="${page}" />
	</div>
</div>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/order/bussinesschance/service_index.js?rnd=${resourceVersionKey}"></script>
<script type="text/javascript"
	src="${pageContext.request.contextPath}/static/js/region/index.js?rnd=${resourceVersionKey}"></script>
<script>
	$('.J-select-quote').click(function(){
		var $checkedItem = $('.J-quote-item:checked');

		if(!$checkedItem.length){
			layer.alert("请选择商机")
		}else{
			window.parent && window.parent.selectQuote($checkedItem.val());
		}
	})
</script>
<input type="hidden" id="rest" value="${bussinessChanceVo.isRest }"/>
<%@ include file="../../common/footer.jsp"%>
