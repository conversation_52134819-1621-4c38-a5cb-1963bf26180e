package com.vedeng.erp.system.service;

import com.vedeng.erp.system.domain.entity.CommonSearchEntity;
import com.vedeng.erp.system.dto.CommonSearchDto;

import java.util.List;

public interface CommonSearchService{

    int deleteByPrimaryKey(Long id);

    int insert(CommonSearchEntity record);

    int insertSelective(CommonSearchEntity record);

    CommonSearchEntity selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(CommonSearchEntity record);

    int updateByPrimaryKey(CommonSearchEntity record);


    List<CommonSearchEntity> selectListByUserIdAndSearchFrom(CommonSearchDto commonSearchDto);

    int saveOrUpdate(CommonSearchDto commonSearchDto);


}
