package com.vedeng.erp.finance.service.impl.api;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.erp.finance.domain.entity.InvoiceApplyCheckEntity;
import com.vedeng.erp.finance.dto.InvoiceApplyDetailDto;
import com.vedeng.erp.finance.dto.InvoiceApplyDto;
import com.vedeng.erp.finance.enums.CheckChainEnum;
import com.vedeng.erp.finance.enums.YNEnum;
import com.vedeng.erp.finance.mapper.InvoiceApplyCheckMapper;
import com.vedeng.erp.finance.mapper.InvoiceApplyDetailMapper;
import com.vedeng.erp.finance.mapper.InvoiceApplyMapper;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.service.InvoiceApplyService;
import com.vedeng.erp.finance.service.impl.check.builder.CheckChainBuilder;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.service.InvoiceCheckApiService;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.enums.SaleOrderInvoiceStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleOrderStatusEnum;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 开票校验统一api接口实现类
 */
@Service
@Slf4j
public class InvoiceCheckApiServiceImpl implements InvoiceCheckApiService {

    @Autowired
    private CheckChainBuilder checkChainBuilder;
    @Autowired
    private InvoiceApplyCheckMapper invoiceApplyCheckMapper;
    @Autowired
    private InvoiceApplyService invoiceApplyService;
    @Autowired
    private SaleOrderApiService saleOrderApiService;
    @Autowired
    private InvoiceApplyMapper invoiceApplyMapper;
    @Autowired
    private InvoiceApplyDetailMapper invoiceApplyDetailMapper;

    /**
     * 申请状态
     */
    private static final List<Integer> APPLICATE_STATUS = Arrays.asList(SaleOrderStatusEnum.IN_PROGRESS.getCode(), SaleOrderStatusEnum.FINISHED.getCode());


    @Override
    public InvoiceCheckResultDto applyCheck(InvoiceCheckRequestDto invoiceCheckRequestDto) {
        log.info("发票申请校验开始,invoiceCheckRequestDto:{}", JSON.toJSONString(invoiceCheckRequestDto));
        return getResult(invoiceCheckRequestDto);
    }

    @Override
    public InvoiceCheckResultDto openCheck(InvoiceCheckRequestDto invoiceCheckRequestDto) {
        log.info("发票开具校验开始,invoiceCheckRequestDto:{}", JSON.toJSONString(invoiceCheckRequestDto));
        return getResult(invoiceCheckRequestDto);
    }

    private InvoiceCheckResultDto getResult(InvoiceCheckRequestDto invoiceCheckRequestDto) {
        AbstractCheckHandler abstractCheckHandler = checkChainBuilder.buildChain(invoiceCheckRequestDto.getCheckChainEnum());
        if (Objects.isNull(abstractCheckHandler)) {
            log.info("校验链条为空,invoiceCheckRequestDto:{}", JSON.toJSONString(invoiceCheckRequestDto));
            throw new ServiceException("校验链条为空");
        }
        InvoiceCheckResultDto initResult = new InvoiceCheckResultDto();
        List<InvoiceCheckResultDto.InvoiceCheckResultDetailDto> resultDetailList = new ArrayList<>();
        initResult.setInvoiceCheckResultDetailDtoList(resultDetailList);
        InvoiceCheckResultDto invoiceCheckResultDto = abstractCheckHandler.processHandler(invoiceCheckRequestDto, initResult);
        saveResultToInvoiceApplyCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
        return invoiceCheckResultDto;
    }

    @Override
    public void saveResultToInvoiceApplyCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("保存校验结果到发票规则检验不通过原因表,invoiceCheckRequestDto:{},invoiceCheckResultDto:{}", JSON.toJSONString(invoiceCheckRequestDto), JSON.toJSONString(invoiceCheckResultDto));
        if (Objects.isNull(invoiceCheckRequestDto) || Objects.isNull(invoiceCheckResultDto) || Objects.isNull(invoiceCheckRequestDto.getInvoiceApplyId())) {
            log.info("保存校验结果到发票规则检验不通过原因表,参数为空");
            return;
        }
        InvoiceApplyCheckEntity invoiceApplyCheck = invoiceApplyCheckMapper.findByInvoiceApplyId(invoiceCheckRequestDto.getInvoiceApplyId());
        List<InvoiceCheckResultDto.InvoiceCheckResultDetailDto> resultDetailList = invoiceCheckResultDto.getInvoiceCheckResultDetailDtoList();
        String noPassRuleCode = CollUtil.isEmpty(resultDetailList) ? "" : resultDetailList
                .stream()
                .map(InvoiceCheckResultDto.InvoiceCheckResultDetailDto::getRuleCode)
                .sorted()
                .reduce((a, b) -> a + "," + b)
                .orElse("");
        if (Objects.isNull(invoiceApplyCheck)) {
            if (invoiceCheckResultDto.getSuccess()) {
                log.info("保存校验结果到发票规则检验不通过原因表,invoiceApplyCheck为空,校验通过,不保存数据");
                return;
            }
            InvoiceApplyCheckEntity insertEntity = new InvoiceApplyCheckEntity();
            insertEntity.setInvoiceApplyId(invoiceCheckRequestDto.getInvoiceApplyId());
            insertEntity.setIsDelete(ErpConstant.F);
            if (CheckChainEnum.isApply(invoiceCheckRequestDto.getCheckChainEnum())) {
                insertEntity.setApplyNoPassRuleCode(noPassRuleCode);
            } else if (CheckChainEnum.isOpen(invoiceCheckRequestDto.getCheckChainEnum())) {
                insertEntity.setOpenNoPassRuleCode(noPassRuleCode);
            }
            log.info("保存校验结果到发票规则检验不通过原因表,invoiceApplyCheck为空,新增数据:{}", JSON.toJSONString(insertEntity));
            invoiceApplyCheckMapper.insertSelective(insertEntity);
        }else {
            invoiceApplyCheck.setIsDelete(ErpConstant.F);
            if (CheckChainEnum.isApply(invoiceCheckRequestDto.getCheckChainEnum())) {
                invoiceApplyCheck.setApplyNoPassRuleCode(noPassRuleCode);
            } else if (CheckChainEnum.isOpen(invoiceCheckRequestDto.getCheckChainEnum())) {
                invoiceApplyCheck.setOpenNoPassRuleCode(noPassRuleCode);
            }
            log.info("保存校验结果到发票规则检验不通过原因表,invoiceApplyCheck不为空,更新数据:{}", JSON.toJSONString(invoiceApplyCheck));
            invoiceApplyCheckMapper.updateByPrimaryKeySelective(invoiceApplyCheck);
        }
    }


    /**
     * 检查发票申请
     * @param saleOrderId
     */
    @Override
    public void checkSaleOrderApplyInvoice(Integer saleOrderId){
        if (Objects.isNull(saleOrderId)){
            log.info("参数有误，销售id不能为空");
            throw new ServiceException("参数有误，销售id不能为空");
        }
        SaleorderInfoDto saleorderInfoDto = saleOrderApiService.getBySaleOrderId(saleOrderId);
        // 生效状态
        Integer validStatus = saleorderInfoDto.getValidStatus();
        if (YNEnum.N.getCode().equals(validStatus)){
            throw new ServiceException("生效状态为未生效，不可申请开票");
        }
        // 单据状态
        Integer status = saleorderInfoDto.getStatus();

        if(!APPLICATE_STATUS.contains(status)){
            throw new ServiceException("单据状态为" + SaleOrderStatusEnum.getDesc(status) + ",不可申请开票");
        }

        // 开票状态
        Integer invoiceStatus = saleorderInfoDto.getInvoiceStatus();
        if (SaleOrderInvoiceStatusEnum.ALL.getCode().equals(invoiceStatus)){
            throw new ServiceException("开票状态为" + SaleOrderInvoiceStatusEnum.ALL.getDesc() + ",不可申请开票");
        }
        // 锁定状态
        Integer lockedStatus = saleorderInfoDto.getLockedStatus();
        if (YNEnum.Y.getCode().equals(lockedStatus)){
            throw new ServiceException("订单已锁定，请订单解除锁定后重试");
        }
        // 订单实际金额
        BigDecimal totalAmount = saleorderInfoDto.getTotalAmount();
        if (BigDecimal.ZERO.compareTo(totalAmount) == 0){
            throw new ServiceException("订单实际金额为0，不可申请开票");
        }

        // 进行中开票申请
        List<InvoiceApplyDto> invoiceApplying = invoiceApplyService.getInvoiceApplyBySaleOrderId(saleOrderId);
        if (CollUtil.isNotEmpty(invoiceApplying)){
            throw new ServiceException("已存在进行中开票申请");
        }
    }

    @Override
    public void refreshInvoiceApplyCheckData() {
        log.info("刷新发票校验数据开始");
        checkAndRefresh(invoiceApplyMapper.getSaleOrderInvoiceApplyAdvanceData(), CheckChainEnum.INVOICE_APPLY_SALES);
        checkAndRefresh(invoiceApplyMapper.getSaleOrderInvoiceApplyData(), CheckChainEnum.INVOICE_OPEN_SALES);
        checkAndRefresh(invoiceApplyMapper.getAfterSalesInvoiceApplyAdvanceData(), CheckChainEnum.INVOICE_APPLY_AFTER);
        checkAndRefresh(invoiceApplyMapper.getAfterSalesInvoiceApplyData(), CheckChainEnum.INVOICE_OPEN_AFTER);
    }

    @Override
    public void refreshInvoiceApplyCheck(CheckChainEnum checkChainEnum, Integer id) {
        switch (checkChainEnum) {
            case INVOICE_APPLY_SALES:
                checkAndRefresh(invoiceApplyMapper.getSaleOrderInvoiceApplyAdvanceData(), CheckChainEnum.INVOICE_APPLY_SALES);
                break;
            case INVOICE_OPEN_SALES:
                List<InvoiceApplyDto> saleOrderInvoiceApplyDataById = invoiceApplyMapper.getSaleOrderInvoiceApplyDataById(id);
                checkAndRefresh(saleOrderInvoiceApplyDataById, CheckChainEnum.INVOICE_OPEN_SALES);
                break;
            case INVOICE_APPLY_AFTER:
                checkAndRefresh(invoiceApplyMapper.getAfterSalesInvoiceApplyAdvanceData(), CheckChainEnum.INVOICE_APPLY_AFTER);
                break;
            case INVOICE_OPEN_AFTER:
                checkAndRefresh(invoiceApplyMapper.getAfterSalesInvoiceApplyData(), CheckChainEnum.INVOICE_OPEN_AFTER);
                break;
            default:
                log.info("不支持的校验链:{}", checkChainEnum);
                break;
        }
    }


    public void checkAndRefresh(List<InvoiceApplyDto> invoiceApplyDtoList, CheckChainEnum checkChainEnum) {
        if (CollUtil.isEmpty(invoiceApplyDtoList)) {
            return;
        }
        log.info("刷新发票校验数据开始,invoiceApplyDtoList:{},checkChainEnum:{}", JSON.toJSONString(invoiceApplyDtoList), checkChainEnum);
        for (InvoiceApplyDto invoiceApplyDto : invoiceApplyDtoList) {
            InvoiceCheckRequestDto invoiceCheckRequestDto = new InvoiceCheckRequestDto();
            invoiceCheckRequestDto.setCheckChainEnum(checkChainEnum);
            invoiceCheckRequestDto.setInvoiceApplyId(invoiceApplyDto.getInvoiceApplyId());
            invoiceCheckRequestDto.setInvoiceProperty(invoiceApplyDto.getInvoiceProperty());
            invoiceCheckRequestDto.setType(invoiceApplyDto.getType());
            invoiceCheckRequestDto.setRelatedId(invoiceApplyDto.getRelatedId());
            invoiceCheckRequestDto.setInvoiceInfoType(invoiceApplyDto.getInvoiceInfoType());
            invoiceCheckRequestDto.setInvoiceMessage(invoiceApplyDto.getInvoiceMessage());
            List<InvoiceApplyDetailDto> detailList = invoiceApplyDetailMapper.getDetailByInvoiceApplyId(invoiceApplyDto.getInvoiceApplyId());
            if (CollUtil.isEmpty(detailList)) {
                continue;
            }
            invoiceCheckRequestDto.setDetailList(detailList.stream().map(detail -> {
                InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto requestDetail = new InvoiceCheckRequestDto.InvoiceCheckRequestDetailDto();
                requestDetail.setInvoiceApplyDetailId(detail.getInvoiceApplyDetailId());
                requestDetail.setDetailGoodsId(detail.getDetailgoodsId());
                requestDetail.setPrice(detail.getPrice());
                requestDetail.setNum(detail.getNum());
                requestDetail.setTotalAmount(detail.getTotalAmount());
                return requestDetail;
            }).collect(Collectors.toList()));

            try {
                if (CheckChainEnum.isApply(checkChainEnum)) {
                    applyCheck(invoiceCheckRequestDto);
                } else if (CheckChainEnum.isOpen(checkChainEnum)) {
                    openCheck(invoiceCheckRequestDto);
                }
            } catch (Exception e) {
                log.error("刷新发票校验数据异常,invoiceCheckRequestDto:{},checkChainEnum:{}", JSON.toJSONString(invoiceCheckRequestDto), checkChainEnum, e);
            }
        }
    }

    @Override
    public void addApplyCheck(Integer applyId, String collect) {
        invoiceApplyCheckMapper.addApplyCheck(applyId,collect);
    }
}
