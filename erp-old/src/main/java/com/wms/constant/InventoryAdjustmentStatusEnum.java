package com.wms.constant;

/**
 * <AUTHOR>
 * @Description 库存条调整单审核状态
 * @createTime 2020年07月30日 14:36:00
 */
public enum InventoryAdjustmentStatusEnum {
    APPROVED(1, "审核通过","99"),
    VETO(2, "审核不通过","90");

    InventoryAdjustmentStatusEnum(Integer status, String statusStr, String code) {
        this.status = status;
        this.statusStr = statusStr;
        this.code = code;
    }

    private Integer status;

    private String statusStr;

    private String code;

    public static String getStatusStr(Integer status) {
        String statusStr = "";

        for (InventoryAdjustmentStatusEnum enumItem : InventoryAdjustmentStatusEnum.values()) {
            if (enumItem.getStatus().equals(status)) {
                statusStr = enumItem.getStatusStr();
                break;
            }
        }

        return statusStr;
    }

    public static String getCode(Integer status) {
        String code = "";

        for (InventoryAdjustmentStatusEnum enumItem : InventoryAdjustmentStatusEnum.values()) {
            if (enumItem.getStatus().equals(status)) {
                code = enumItem.getCode();
                break;
            }
        }

        return code;
    }
    public Integer getStatus() {
        return status;
    }

    public String getStatusStr() {
        return statusStr;
    }

    public String getCode() {
        return code;
    }}
