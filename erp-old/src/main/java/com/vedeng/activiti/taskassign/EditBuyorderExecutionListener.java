package com.vedeng.activiti.taskassign;

import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDto;
import com.vedeng.erp.buyorder.service.BuyorderExpenseApiService;
import com.vedeng.erp.system.SyncDataTypeEnum;
import com.vedeng.erp.system.api.SyncDataErpApiService;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.dto.SyncDataErpDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.order.model.vo.SaleorderGoodsVo;
import com.vedeng.order.service.BuyorderService;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.system.service.UserService;
import com.vedeng.trader.service.TraderSupplierService;
import com.wms.dao.WmsSendOrderMapper;
import com.wms.dto.WmsResponse;
import com.wms.model.po.WmsSendOrder;
import com.wms.service.*;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.context.ContextLoader;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.xml.ws.WebServiceContext;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class EditBuyorderExecutionListener implements ExecutionListener {

	Logger logger= LoggerFactory.getLogger(EditBuyorderExecutionListener.class);
    WebApplicationContext context = ContextLoader.getCurrentWebApplicationContext();
    private BuyorderService buyorderService = (BuyorderService) context.getBean("buyorderService");
    private CapitalBillService capitalBillService = (CapitalBillService) context.getBean("capitalBillService");
    private UserService userService = (UserService) context.getBean("userService");

	private BuyorderMapper buyorderMapper = (BuyorderMapper) context.getBean("buyorderMapper");

	private SaleorderMapper saleorderMapper = (SaleorderMapper) context.getBean("saleorderMapper");

	private SaleorderGoodsMapper saleorderGoodsMapper = (SaleorderGoodsMapper) context.getBean("saleorderGoodsMapper");

	private LogicalSaleorderChooseService logicalSaleorderChooseService = (LogicalSaleorderChooseService) context.getBean("logicalSaleorderChooseService");

	private WmsSendOrderMapper wmsSendOrderMapper = (WmsSendOrderMapper) context.getBean("wmsSendOrderMapper");
	private OrderCommonService orderCommonService= (OrderCommonService) context.getBean("orderCommonServiceImpl");

	private BuyorderExpenseApiService buyorderExpenseApiService = (BuyorderExpenseApiService) context.getBean("buyorderExpenseServiceImpl");

	private BaseCompanyInfoApiService baseCompanyInfoApiService =  (BaseCompanyInfoApiService) context.getBean("baseCompanyInfoApiServiceImpl");

	private SyncDataErpApiService syncDataErpApiService =  (SyncDataErpApiService) context.getBean("syncDataErpApiServiceImpl");

	private DoPutService doPutService= (DoPutService)context.getBean("doPutService");
    //采购订单审核触发器
    //根据穿参通用回写主表中状态
    @Override
	public void notify(DelegateExecution execution) throws Exception {
    	logger.info("准备设置采购订单的状态");

		BuyorderVo buyorderInfo = (BuyorderVo) execution.getVariable("buyorderInfo");
		log.info("采购单审核,审核流变量：buyorderInfo:{}", JSON.toJSONString(buyorderInfo));

		BigDecimal prepaidAmount = buyorderMapper.getPrepaidAmountByBuyorderId(buyorderInfo.getBuyorderId());
		log.info("添加触发账期流水：账期金额,数据库:{}", prepaidAmount);
		log.info("添加触发账期流水：账期金额,审核流变量:{}", buyorderInfo.getPrepaidAmount());

		//如果预付款金额等于0，添加触发账期流水
		if(prepaidAmount.compareTo(BigDecimal.ZERO) == 0){
			User user = getUser();
		    //添加流水
		    // 归属销售
			User belongUser = new User();
			if (buyorderInfo.getTraderId() != null) {
				belongUser = userService.getUserByTraderId(buyorderInfo.getTraderId(), 2);// 1客户，2供应商
				if (belongUser != null && belongUser.getUserId() != null) {
					belongUser = userService.getUserById(belongUser.getUserId());
				}
			}
		    CapitalBill capitalBill = new CapitalBill();
		    capitalBill.setCompanyId(user.getCompanyId());
		    //信用支付
		    capitalBill.setTraderMode(527);
		    capitalBill.setCurrencyUnitId(1);
		    capitalBill.setTraderTime(DateUtil.sysTimeMillis());
		    //交易类型 转移
		    capitalBill.setTraderType(3);
		    capitalBill.setPayee(buyorderInfo.getTraderName());
		    capitalBill.setPayer(user.getCompanyName());

		    List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
		    CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
		    //订单类型   采购订单
		    capitalBillDetail.setOrderType(2);
		    capitalBillDetail.setOrderNo(buyorderInfo.getBuyorderNo());
		    capitalBillDetail.setRelatedId(buyorderInfo.getBuyorderId());
		    //所属类型  经销商（包含终端）
		    capitalBillDetail.setTraderType(2);
		    capitalBillDetail.setTraderId(buyorderInfo.getTraderId());
		    capitalBillDetail.setUserId(buyorderInfo.getUserId());
		    //业务类型  订单收款
		    capitalBillDetail.setBussinessType(525);
		    capitalBillDetail.setAmount(buyorderInfo.getAccountPeriodAmount());
		    if(belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null){
			capitalBillDetail.setOrgName(belongUser.getOrgName());
			capitalBillDetail.setOrgId(belongUser.getOrgId());
		    }
		    capitalBillDetails.add(capitalBillDetail);

		    capitalBill.setCapitalBillDetails(capitalBillDetails);
		    CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
		    capitalBillDetailInfo.setOrderType(2);
		    capitalBillDetailInfo.setOrderNo(buyorderInfo.getBuyorderNo());
		    capitalBillDetailInfo.setRelatedId(buyorderInfo.getBuyorderId());
		    capitalBillDetailInfo.setTraderType(2);
		    capitalBillDetailInfo.setTraderId(buyorderInfo.getTraderId());
		    capitalBillDetailInfo.setUserId(buyorderInfo.getUserId());
		    //业务类型  订单收款
		    capitalBillDetailInfo.setBussinessType(525);
		    capitalBillDetailInfo.setAmount(buyorderInfo.getAccountPeriodAmount());
		    if(belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null){
			capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
			capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
		    }
		    capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
		    //添加当前登陆人
		    capitalBill.setCreator(user.getUserId());
		    capitalBillService.saveCapitalBill(capitalBill);

			logger.info("准备设置采购订单的状态"+buyorderInfo.getBuyorderId());
			BuyorderVo buyorder = new BuyorderVo();
			buyorder.setBuyorderId(buyorderInfo.getBuyorderId());
			Buyorder buyorder1 = buyorderMapper.selectByPrimaryKey(buyorderInfo.getBuyorderId());

			if(buyorderInfo.getRetainageAmount().compareTo(BigDecimal.ZERO) == 0){
				//如果尾款等于0.付款状态为全部付款
				buyorder.setPaymentStatus(2);
			}else{
				//如果尾款不等于0.付款状态为部分付款
				buyorder.setPaymentStatus(1);
			}
			buyorder.setPaymentTime(DateUtil.sysTimeMillis());
			//赠品单审核通过不更新
			if(buyorder1.getIsGift().equals(1)){
				buyorder.setPaymentStatus(null);
				buyorder.setPaymentTime(null);
			}
			buyorder.setSatisfyDeliveryTime(DateUtil.sysTimeMillis());
			buyorder.setStatus(1);
			buyorderService.saveBuyorder(buyorder);

			buyorderExpenseApiService.updateStatusByBuyorderId(buyorder.getBuyorderId(),1);
			// add by hollis 2022/8/31 16:22 采购费用单更新盛和状态 end
			this.buyorderExpenseDataAccountPrepaidAmount(buyorder.getBuyorderId());

		}else{
		    BuyorderVo buyorder = new BuyorderVo();
		    buyorder.setBuyorderId(buyorderInfo.getBuyorderId());
		    buyorder.setStatus(1);
			logger.info("准备设置采购订单的状态"+buyorderInfo.getBuyorderId());
			buyorderService.saveBuyorder(buyorder);

			// add by hollis 2022/8/31 16:22 采购费用单更新盛和状态 start
			buyorderExpenseApiService.updateStatusByBuyorderId(buyorder.getBuyorderId(),1);
			// add by hollis 2022/8/31 16:22 采购费用单更新盛和状态 end
		}

		//采购单审核通过 下发wms接口
		Buyorder buyorder = buyorderService.getBuyOrderByOrderId(buyorderInfo.getBuyorderId());
		this.checkNeedSendToOtherErp(buyorder);
		// 处理虚拟商品发货状态
		buyorderService.handleBuyOrderVirtualGoodsArrival(buyorder.getBuyorderId());
		orderCommonService.updateBuyOrderDataUpdateTime(buyorderInfo.getBuyorderId(), null, OrderDataUpdateConstant.BUY_ORDER_VAILD);
		//普法 才下发采购单
		if(buyorder.getDeliveryDirect() == 0){

			logger.info("采购单:"+buyorder.getBuyorderNo()+",审核通过下发WMS start=============================");


			try {
				//下发，存表V_WMS_SEND_ORDER
				WmsSendOrder wmsSendOrder = logicalSaleorderChooseService.getWmsSendOrder(buyorder.getBuyorderNo(),buyorder.getBuyorderId(),ErpConst.ONE,getUser());

				WmsResponse wmsResponse = doPutService.doPutPurchaseOrderMethod(buyorder.getBuyorderId(), null);
				logger.info("采购入库单下发wms响应:" +buyorder.getBuyorderNo()+","+ JSON.toJSONString(wmsResponse));
				//下发成功，修改状态
				wmsSendOrder.setSendStatus(ErpConst.ONE);
				wmsSendOrderMapper.updateByPrimaryKeySelective(wmsSendOrder);
			}catch (Exception e){
				logger.error("采购入库单下发wms失败 单号:"+buyorder.getBuyorderNo(),e);
			}
		}
		//查看VP采购单 关联的销售单是否是专项发货单  如果是触发对应的销售单下传
		if(ErpConst.ZERO.equals(buyorder.getOrderType())) {

			//查询采购单关联的销售单
			List<Integer> saleOrderIdList = saleorderMapper.getRelateSaleOrderId(buyorder.getBuyorderId());

			Buyorder specialBuyorder = buyorderMapper.selectByPrimaryKey(buyorder.getBuyorderId());
			logger.info("专项发货采购单审核通过，下发wms，{},",specialBuyorder);
			//如果销售单是包含专项发货的 就再次下发销售单
			for (Integer saleOrderId : saleOrderIdList) {
				try {
					if (hasSpecialDeliveryGoodByOrderId(saleOrderId)) {
						Saleorder order = saleorderMapper.getSaleOrderById(saleOrderId);
						logicalSaleorderChooseService.chooseLogicalSaleorder(order, getUser());
					}
				} catch (Exception e) {
					logger.error("专项发货单:"+saleOrderId+",下发WMS失败:",e);
				}
			}
		}

    }

    private boolean hasSpecialDeliveryGoodByOrderId(Integer saleOrderId){

		Saleorder saleorder = new Saleorder();
		saleorder.setSaleorderId(saleOrderId);

		List<SaleorderGoodsVo> saleorderGoodsVoList = saleorderGoodsMapper.getSaleorderGoodsVoListBySaleorderId(saleorder);

		//存在专项发货的SKU
		List<String> specialDeliverySkuList = saleorderGoodsVoList.stream()
																	.filter(saleorderGoodsVo -> saleorderGoodsVo.getSpecialDelivery() == 1)
																	.map(saleorderGoodsVo -> saleorderGoodsVo.getSku())
																	.collect(Collectors.toList());

		return CollectionUtils.isNotEmpty(specialDeliverySkuList);
	}


	public void checkNeedSendToOtherErp(Buyorder buyorder){
		String traderName = buyorder.getTraderName();
		BaseCompanyInfoDto baseCompanyInfoDto = baseCompanyInfoApiService.selectBaseCompanyByCompanyName(traderName);
		if(baseCompanyInfoDto == null){
			log.info("采购订单{}不符合同步到子公司的条件,E1",traderName);
			return;
		}

		boolean checkIsFromStandard =  syncDataErpApiService.checkBuyOrderFromStandard(buyorder.getBuyorderNo());
		if(checkIsFromStandard){
			log.info("采购订单{}不符合同步到子公司的条件,E2,归属业务流转",buyorder.getBuyorderNo());
			return;
		}
		log.info("采购订单{}符合同步到子公司的条件",traderName);
		String dataType = SyncDataTypeEnum.BUYORDER_TO_SALEORDER.getDataType();
		//需要同步给子公司
		SyncDataErpDto syncDataErpDto = new SyncDataErpDto();
		syncDataErpDto.setBusinessNo(buyorder.getBuyorderNo());
		syncDataErpDto.setTargetErp(baseCompanyInfoDto.getCompanyShortName());
		syncDataErpDto.setBusinessType(dataType);
		syncDataErpDto.setRequestContent("{}");
		syncDataErpDto.setProcessStatus(ErpConst.ZERO);
		syncDataErpApiService.insert(syncDataErpDto);
	}


    private User getUser(){
		User user = null;
		ServletRequestAttributes ra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
		if (ra != null) {
			HttpServletRequest request = ra.getRequest();
			if (request != null && request.getSession() != null) {
				user = (User) request.getSession().getAttribute(ErpConst.CURR_USER);
			}
		}
		if(user==null){
			user= new User();
			user.setCompanyId(1);
			user.setUserId(2);
			user.setCompanyName("南京贝登医疗有限公司");
			user.setUsername("njadmin");
		}
		return user;
	}

	private void buyorderExpenseDataAccountPrepaidAmount(Integer buyorderId) {
		BuyorderExpenseDto buyorderExpenseDto = buyorderExpenseApiService.queryAttributiveExpenseInfoByBuyorderId(buyorderId);
		if (buyorderExpenseDto != null) {
			//如果预付款金额等于0，添加触发账期流水
			if (buyorderExpenseDto.getBuyorderExpenseDetailDto().getPrepaidAmount().compareTo(BigDecimal.ZERO) == 0) {
				User user = getUser();
				BuyorderExpenseDto update = new BuyorderExpenseDto();
				update.setBuyorderExpenseId(buyorderExpenseDto.getBuyorderExpenseId());
				if (buyorderExpenseDto.getBuyorderExpenseDetailDto().getRetainageAmount().compareTo(BigDecimal.ZERO) == 0) {
					//如果尾款等于0.付款状态为全部付款
					update.setPaymentStatus(2);
				} else {
					//如果尾款不等于0.付款状态为部分付款
					update.setPaymentStatus(1);
				}
				update.setPaymentTime(new Date());
				//添加啊流水
				// 归属销售
				User belongUser = new User();
				if (buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId() != null) {
					belongUser = userService.getUserByTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId(), 2);// 1客户，2供应商
					if (belongUser != null && belongUser.getUserId() != null) {
						belongUser = userService.getUserById(belongUser.getUserId());
					}
				}
				CapitalBill capitalBill = new CapitalBill();
				capitalBill.setCompanyId(user.getCompanyId());
				//信用支付
				capitalBill.setTraderMode(527);
				capitalBill.setCurrencyUnitId(1);
				capitalBill.setTraderTime(DateUtil.sysTimeMillis());
				//交易类型 转移
				capitalBill.setTraderType(3);
				capitalBill.setPayee(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderName());
				capitalBill.setPayer(user.getCompanyName());

				List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
				CapitalBillDetail capitalBillDetail = new CapitalBillDetail();
				//订单类型   采购费用订单
				capitalBillDetail.setOrderType(4);
				capitalBillDetail.setOrderNo(buyorderExpenseDto.getBuyorderExpenseNo());
				capitalBillDetail.setRelatedId(buyorderExpenseDto.getBuyorderExpenseId());
				//所属类型  经销商（包含终端）
				capitalBillDetail.setTraderType(2);
				capitalBillDetail.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
				capitalBillDetail.setUserId(buyorderExpenseDto.getCreator());
				//业务类型  订单收款
				capitalBillDetail.setBussinessType(525);
				capitalBillDetail.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
				if (belongUser != null && buyorderExpenseDto.getOrgName() != null && belongUser.getOrgId() != null) {
					capitalBillDetail.setOrgName(belongUser.getOrgName());
					capitalBillDetail.setOrgId(belongUser.getOrgId());
				}
				capitalBillDetails.add(capitalBillDetail);

				capitalBill.setCapitalBillDetails(capitalBillDetails);
				CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
				capitalBillDetailInfo.setOrderType(4);
				capitalBillDetailInfo.setOrderNo(buyorderExpenseDto.getBuyorderExpenseNo());
				capitalBillDetailInfo.setRelatedId(buyorderExpenseDto.getBuyorderExpenseId());
				capitalBillDetailInfo.setTraderType(2);
				capitalBillDetailInfo.setTraderId(buyorderExpenseDto.getBuyorderExpenseDetailDto().getTraderId());
				capitalBillDetailInfo.setUserId(buyorderExpenseDto.getCreator());
				//业务类型  订单收款
				capitalBillDetailInfo.setBussinessType(525);
				capitalBillDetailInfo.setAmount(buyorderExpenseDto.getBuyorderExpenseDetailDto().getAccountPeriodAmount());
				if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
					capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
					capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
				}
				capitalBill.setCapitalBillDetail(capitalBillDetailInfo);
				//添加当前登陆人
				capitalBill.setCreator(user.getUserId());
				logger.info("采购单直属费用单：{}添加账期流水：{}",JSON.toJSONString(buyorderExpenseDto.getBuyorderExpenseId()),JSON.toJSONString(capitalBill));
				capitalBillService.saveCapitalBill(capitalBill);
				buyorderExpenseApiService.updateBuyorderExpenseStatusByVerity(update);
				buyorderExpenseApiService.doDeliveryStatus(update.getBuyorderExpenseId());
				buyorderExpenseApiService.doArrivalStatus(update.getBuyorderExpenseId());
			}

		}
	}

}

