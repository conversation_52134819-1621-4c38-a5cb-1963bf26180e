package com.vedeng.erp.trader.domain.dto;

import com.vedeng.authorization.model.User;
import lombok.Data;

@Data
public class CustomerAssignDto {

    /**
     * 1按客户，2按销售，3按导入
     */
    private Integer type;

    private Integer traderId;

    private String traderName;

    private Integer traderCustomerId;

    /**
     * 划拨给销售
     */
    private Integer userId;
    
    public String userName;

    /**
     * 被划拨销售 type = 2
     */
    private Integer fromUserId;
    /**
     * 省市区 type = 2
     */
    private Integer province;
    private Integer city;
    private Integer regionId;

    /**
     * 名下客户数
     */
    private Integer belongNum;

    private User sessionUser;
    
}
