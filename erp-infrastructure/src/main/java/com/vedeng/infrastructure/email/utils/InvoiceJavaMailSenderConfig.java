package com.vedeng.infrastructure.email.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;

/**
 * <AUTHOR>
 */
@Configuration
public class InvoiceJavaMailSenderConfig {


    @Value("${invoice_email_protocol}")
    protected String emailProtocol;

    @Value("${invoice_email_host}")
    protected String emailHost;

    @Value("${invoice_email_username}")
    protected String emailUsername;

    @Value("${invoice_email_password}")
    protected String emailPassword;

    @Bean
    public JavaMailSender invoiceJavaMailSender() {

        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setHost(emailHost);
        mailSender.setUsername(emailUsername);
        mailSender.setPassword(emailPassword);

        Properties props = mailSender.getJavaMailProperties();
        props.put("mail.transport.protocol", emailProtocol);
        props.put("mail.host", emailHost);
        props.put("mail.smtp.auth", true);
        return mailSender;
    }


}