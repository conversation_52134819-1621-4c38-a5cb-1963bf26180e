<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.ordergoods.dao.OrdergoodsStoreAccountMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.ordergoods.model.OrdergoodsStoreAccount" >
    <id column="ORDERGOODS_STORE_ACCOUNT_ID" property="ordergoodsStoreAccountId" jdbcType="INTEGER" />
    <result column="WEB_ACCOUNT_ID" property="webAccountId" jdbcType="INTEGER" />
    <result column="ORDERGOODS_STORE_ID" property="ordergoodsStoreId" jdbcType="INTEGER" />
    <result column="TRADER_CONTACT_ID" property="traderContactId" jdbcType="INTEGER" />
    <result column="TRADER_ADDRESS_ID" property="traderAddressId" jdbcType="INTEGER" />
    <result column="IS_ENABLE" property="isEnable" jdbcType="BIT" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="MOD_TIME" property="modTime" jdbcType="BIGINT" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="UUID" property="uuid" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    ORDERGOODS_STORE_ACCOUNT_ID, WEB_ACCOUNT_ID,UUID, ORDERGOODS_STORE_ID, TRADER_CONTACT_ID, 
    TRADER_ADDRESS_ID, IS_ENABLE, ADD_TIME, CREATOR, MOD_TIME, UPDATER
  </sql>
  <select id="selectByPrimaryKey" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsStoreAccountVo" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_ORDERGOODS_STORE_ACCOUNT
    where ORDERGOODS_STORE_ACCOUNT_ID = #{ordergoodsStoreAccountId,jdbcType=INTEGER}
  </select>
  <insert id="insert" parameterType="com.vedeng.ordergoods.model.OrdergoodsStoreAccount" >
    insert into T_ORDERGOODS_STORE_ACCOUNT
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="ordergoodsStoreAccountId != null" >
        ORDERGOODS_STORE_ACCOUNT_ID,
      </if>
      <if test="webAccountId != null" >
        WEB_ACCOUNT_ID,
      </if>
      <if test="uuid != null" >
        UUID,
      </if>
      <if test="ordergoodsStoreId != null" >
        ORDERGOODS_STORE_ID,
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID,
      </if>
      <if test="traderAddressId != null" >
        TRADER_ADDRESS_ID,
      </if>
      <if test="isEnable != null" >
        IS_ENABLE,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="modTime != null" >
        MOD_TIME,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="ordergoodsStoreAccountId != null" >
        #{ordergoodsStoreAccountId,jdbcType=INTEGER},
      </if>
      <if test="webAccountId != null" >
        #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null" >
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="ordergoodsStoreId != null" >
        #{ordergoodsStoreId,jdbcType=INTEGER},
      </if>
      <if test="traderContactId != null" >
        #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderAddressId != null" >
        #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        #{isEnable,jdbcType=BIT},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="modTime != null" >
        #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
    </trim>
    
    <selectKey resultType="java.lang.Integer" order="AFTER" keyProperty="ordergoodsStoreAccountId">
		SELECT LAST_INSERT_ID() AS ordergoodsStoreAccountId
    </selectKey>
  </insert>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.ordergoods.model.OrdergoodsStoreAccount" >
    update T_ORDERGOODS_STORE_ACCOUNT
    <set >
      <if test="webAccountId != null" >
        WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER},
      </if>
      <if test="uuid != null" >
        UUID = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="traderContactId != null" >
        TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER},
      </if>
      <if test="traderAddressId != null" >
        TRADER_ADDRESS_ID = #{traderAddressId,jdbcType=INTEGER},
      </if>
      <if test="isEnable != null" >
        IS_ENABLE = #{isEnable,jdbcType=BIT},
      </if>
      <if test="modTime != null" >
        MOD_TIME = #{modTime,jdbcType=BIGINT},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
    </set>
    where ORDERGOODS_STORE_ACCOUNT_ID = #{ordergoodsStoreAccountId,jdbcType=INTEGER}
  </update>
  
  <select id="getOrdergoodsAccountListPage" parameterType="Map" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsStoreAccountVo">
  	select
  		a.ORDERGOODS_STORE_ACCOUNT_ID, a.WEB_ACCOUNT_ID, a.ORDERGOODS_STORE_ID, a.TRADER_CONTACT_ID, 
    	a.TRADER_ADDRESS_ID, a.IS_ENABLE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER
  	from
  		T_ORDERGOODS_STORE_ACCOUNT a
  	where
  		1=1
  		<if test="ordergoodsStoreAccountVo.ordergoodsStoreId != null">
  		and a.ORDERGOODS_STORE_ID = #{ordergoodsStoreAccountVo.ordergoodsStoreId,jdbcType=INTEGER}
  		</if>
  		<if test="ordergoodsStoreAccountVo.isEnable != null and ordergoodsStoreAccountVo.isEnable != -1">
  		and a.IS_ENABLE = #{ordergoodsStoreAccountVo.isEnable,jdbcType=BIT}
  		</if>
  		<if test="ordergoodsStoreAccountVo.traderContactIds != null and ordergoodsStoreAccountVo.traderContactIds.size > 0">
  		and a.TRADER_CONTACT_ID in 
  			<foreach collection="ordergoodsStoreAccountVo.traderContactIds" item="traderContactId" open="(" close=")" separator=",">
				#{traderContactId,jdbcType=INTEGER}
			</foreach>
  		</if>
  	order by a.IS_ENABLE desc,a.ADD_TIME desc
  </select>
  
  <select id="getTraderCotactIds" parameterType="java.lang.Integer" resultType="java.lang.Integer">
  	select 
  		TRADER_CONTACT_ID
  	from
		T_ORDERGOODS_STORE_ACCOUNT 
	where
		ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
  </select>
  
  <select id="getOrdergoodsStoreAccount" parameterType="com.vedeng.ordergoods.model.OrdergoodsStoreAccount" resultType="com.vedeng.ordergoods.model.OrdergoodsStoreAccount">
  	select 
    <include refid="Base_Column_List" />
    from T_ORDERGOODS_STORE_ACCOUNT
    where 
    1=1
    <if test="traderContactId != null">
    	and TRADER_CONTACT_ID = #{traderContactId,jdbcType=INTEGER}
    </if>
    <if test="ordergoodsStoreId != null">
    	and ORDERGOODS_STORE_ID = #{ordergoodsStoreId,jdbcType=INTEGER}
   	</if>
   	<if test="webAccountId != null">
   		and WEB_ACCOUNT_ID = #{webAccountId,jdbcType=INTEGER}
   	</if>
  </select>
  
   <select id="getOrdergoodsStoreAccountByTraderContactIds" parameterType="List" resultType="com.vedeng.ordergoods.model.vo.OrdergoodsStoreAccountVo">
  	select
  		a.ORDERGOODS_STORE_ACCOUNT_ID, a.WEB_ACCOUNT_ID, a.ORDERGOODS_STORE_ID, a.TRADER_CONTACT_ID, 
    	a.TRADER_ADDRESS_ID, a.IS_ENABLE, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER
  	from
  		T_ORDERGOODS_STORE_ACCOUNT a
  	where
  		1=1
  		<if test="traderContactIds != null and traderContactIds.size > 0">
  		and a.TRADER_CONTACT_ID in 
  			<foreach collection="traderContactIds" item="traderContactId" open="(" close=")" separator=",">
				#{traderContactId,jdbcType=INTEGER}
			</foreach>
  		</if>
  	order by a.IS_ENABLE desc,a.ADD_TIME desc
  </select>
</mapper>