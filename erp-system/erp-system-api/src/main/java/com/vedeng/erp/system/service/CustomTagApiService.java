package com.vedeng.erp.system.service;

import com.vedeng.erp.system.dto.CustomTagDto;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 外部调用tagApi
 * @date 2022/7/25 17:39
 */
public interface CustomTagApiService {

    /**
     * 根据id获取标签
     *
     * @param ids ids
     *            userId
     * @return List<CustomTagDto>
     */
    List<CustomTagDto> getTagByIds(List<Integer> ids,Integer userId);

    /**
     * 获取用户所有自定义+默认
     *
     * @param type 业务类型
     * @return list
     */
    List<CustomTagDto> getUserTags(Integer type);

    /**
     * 获取用户所有自定义+默认
     *
     * @param type 业务类型
     * @return list
     */
    List<CustomTagDto> getAllTags(Integer type);

    /**
     * 根据id获取标签
     */
    List<CustomTagDto> getByIdList(List<Integer> idList);
}
