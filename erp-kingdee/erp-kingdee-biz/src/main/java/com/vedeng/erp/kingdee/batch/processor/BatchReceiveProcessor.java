package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.BatchWarehouseGoodsOutInItemDtoMapper;
import com.vedeng.erp.kingdee.batch.repository.BatchWmsOutputOrderDtoMapper;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeStorageOutDto;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 领用出库单
 * @date 2022/11/18 13:55
 */
@Service
@Slf4j
public class BatchReceiveProcessor implements ItemProcessor<BatchWarehouseGoodsOutInDto, KingDeeStorageOutDto> {
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;
    @Autowired
    private BatchWmsOutputOrderDtoMapper batchWmsOutputOrderDtoMapper;
    @Autowired
    private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

    @Override
    public KingDeeStorageOutDto process(BatchWarehouseGoodsOutInDto batchWarehouseGoodsOutInDto) throws Exception {
        KingDeeStorageOutDto dto = new KingDeeStorageOutDto();
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());

        // 判断是否数据已存在
        boolean old = kingDeeBaseApi.isExist(dto);
        if(old){
            log.info("领用出库单,数据已存在:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
            return null;
        }

        log.info("领用出库单,BatchReceiveProcessorService.process:{}", JSON.toJSONString(batchWarehouseGoodsOutInDto));
        BatchWmsOutputOrderDto wmsOrderDto = batchWmsOutputOrderDtoMapper.findByOrderNo(batchWarehouseGoodsOutInDto.getRelateNo());
        if (wmsOrderDto == null) {
            return null;
        }

        batchWarehouseGoodsOutInDto.setBatchWarehouseGoodsOutInItemDtos(batchWarehouseGoodsOutInItemDtoMapper.findByOutInNo(batchWarehouseGoodsOutInDto.getOutInNo()));

        Map<Integer, BatchWmsOutputOrderGoodsDto> map = wmsOrderDto.getBatchWmsOutputOrderGoodsDtos().stream()
                .collect(Collectors.toMap(BatchWmsOutputOrderGoodsDto::getGoodsId, c -> c, (k1, k2) -> k1));
        dto.setFId("0");
        dto.setFDate(DateUtil.formatDateTime(batchWarehouseGoodsOutInDto.getOutInTime()));
        dto.setFBillNo(batchWarehouseGoodsOutInDto.getOutInNo());
        dto.setFQzokBddjtId(wmsOrderDto.getId().toString());

        List<KingDeeStorageOutDetailDto> detailList = new ArrayList<>();
        if (CollUtil.isEmpty(batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos())) {
            return null;
        }
        batchWarehouseGoodsOutInDto.getBatchWarehouseGoodsOutInItemDtos().forEach(d -> {
            KingDeeStorageOutDetailDto detailDto = new KingDeeStorageOutDetailDto();
            BatchWmsOutputOrderGoodsDto wmsInputOrderGoodsDto = map.get(d.getGoodsId());
            detailDto.setFMaterialId(wmsInputOrderGoodsDto.getSkuNo());
            detailDto.setFQty(d.getNum().abs().toString());
            detailDto.setFQzokYsddh(wmsOrderDto.getOrderNo());
            detailDto.setFQzokGsywdh(wmsOrderDto.getOrderNo());
            detailDto.setFQzokPch(d.getVedengBatchNumber());
            detailDto.setFQzokXlh(d.getBarcodeFactory());
            detailDto.setFQzokYwlx("领用单");
            detailDto.setFQzokSfzf("否");
            detailDto.setFStockId("CK9999");
            detailDto.setFQzokBddjhid(wmsInputOrderGoodsDto.getWmsOutputOrderId().toString());
            detailList.add(detailDto);
        });
        dto.setFEntity(detailList);
        return dto;
    }





}
