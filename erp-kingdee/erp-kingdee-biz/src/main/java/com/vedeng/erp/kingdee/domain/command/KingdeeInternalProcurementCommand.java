package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 内部采销
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class KingdeeInternalProcurementCommand {

    /**
     * 单据内码
     */
    private Integer FID;

    /**
     * 单据编号
     */
    private String FBillNo;

    /**
     * 归属业务单号
     */
    private String F_QZOK_GSYWDH;

    /**
     * ERP的单据ID
     */
    private String F_QZOK_BDDJTID;

    /**
     * 单据日期
     */
    private String F_QZOK_Date;

    /**
     * 业务类型
     */
    private String F_QZOK_YWLX;

    /**
     * 组织ID
     */
    private KingDeeNumberCommand F_QZOK_OrgId;

    /**
     * 单据明细
     */
    private List<FEntity> FEntity;

    /**
     * 单据扩展明细
     */
    private List<F_QZOK_Entity> F_QZOK_Entity;


    @Data
    public static class FEntity {

        /**
         * 流转ID
         */
        private String F_QZOK_YYLZID;

        /**
         * 贝登流转ID
         */
        private String F_QZOK_BLZID;

        /**
         * 组织
         */
        private KingDeeNumberCommand F_QZOK_ZZ;

        /**
         * 采购供应商
         */
        private KingDeeNumberCommand F_QZOK_CGGYS;

        /**
         * 销售客户
         */
        private KingDeeNumberCommand F_QZOK_XSKH;

        /**
         * 是否开票
         */
        private Integer F_QZOK_SFKP;

        /**
         * 采购订单
         */
        private Integer F_QZOK_CGDD;

        /**
         * 采购入库
         */
        private Integer F_QZOK_CGRK;

        /**
         * 采购付款
         */
        private Integer F_QZOK_CGFK;

        /**
         * 采购发票
         */
        private Integer F_QZOK_CGFP;

        /**
         * 销售订单
         */
        private Integer F_QZOK_XSDD;

        /**
         * 销售出库
         */
        private Integer F_QZOK_XSCK;

        /**
         * 销售收款
         */
        private Integer F_QZOK_XSSK;

        /**
         * 销售发票
         */
        private Integer F_QZOK_XSFP;

        private List<F_QZOK_SubEntity> f_QZOK_SubEntity = new ArrayList<>();
    }

    @Data
    public static class F_QZOK_Entity {

        /**
         * 流转ID
         */
        private String F_QZOK_JDLZID;

        /**
         * 贝登流转ID
         */
        private String F_QZOK_BDLZID;

        /**
         * 物料编码
         */
        private KingDeeNumberCommand F_QZOK_SPDM;

        /**
         * 采购单价
         */
        private BigDecimal F_QZOK_CGDJ;

        /**
         * 采购税率
         */
        private BigDecimal F_QZOK_CGSL;

        /**
         * 采购税额
         */
        private BigDecimal F_QZOK_SE;

        /**
         * 采购价税合计
         */
        private BigDecimal F_QZOK_JSHJ;

        /**
         * 销售单价
         */
        private BigDecimal F_QZOK_XSDJ;

        /**
         * 销售税率
         */
        private BigDecimal F_QZOK_XSSL;

        /**
         * 销售税额
         */
        private BigDecimal F_QZOK_XSSE;

        /**
         * 销售价税合计
         */
        private BigDecimal F_QZOK_XSJSHJ;

        /**
         * 数量
         */
        private BigDecimal F_QZOK_SL;

        private List<F_QZOK_SubEntity> f_QZOK_SubEntity = new ArrayList<>();
    }


    @Data
    public static class F_QZOK_SubEntity {

        /**
         * 销售电子发票地址
         */
        private String F_QZOK_DZFP;

        /**
         * 采购电子发票地址
         */
        private String F_QZOK_CGDZFP;

        /**
         * 销售发票号
         */
        private String F_QZOK_XSFPH;

        /**
         * 采购发票号
         */
        private String F_QZOK_CGFPH;
    }
}
