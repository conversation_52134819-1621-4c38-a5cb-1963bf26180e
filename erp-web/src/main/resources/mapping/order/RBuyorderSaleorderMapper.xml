<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.order.dao.RBuyorderSaleorderMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.order.model.RBuyorderSaleorder" >
    <id column="R_BUYORDER_J_SALEORDER_ID" property="rBuyorderJSaleorderId" jdbcType="INTEGER" />
    <result column="BUYORDER_GOODS_ID" property="buyorderGoodsId" jdbcType="INTEGER" />
    <result column="SALEORDER_GOODS_ID" property="saleorderGoodsId" jdbcType="INTEGER" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
  </resultMap>
  <sql id="Base_Column_List" >
    R_BUYORDER_J_SALEORDER_ID, BUYORDER_GOODS_ID, SALEORDER_GOODS_ID,NUM
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select 
    <include refid="Base_Column_List" />
    from T_R_BUYORDER_J_SALEORDER
    where R_BUYORDER_J_SALEORDER_ID = #{rBuyorderJSaleorderId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_R_BUYORDER_J_SALEORDER
    where R_BUYORDER_J_SALEORDER_ID = #{rBuyorderJSaleorderId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.order.model.RBuyorderSaleorder" >
    insert into T_R_BUYORDER_J_SALEORDER (R_BUYORDER_J_SALEORDER_ID, BUYORDER_GOODS_ID, NUM,
      SALEORDER_GOODS_ID)
    values (#{rBuyorderJSaleorderId,jdbcType=INTEGER}, #{buyorderGoodsId,jdbcType=INTEGER}, #{num,jdbcType=INTEGER},
      #{saleorderGoodsId,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.order.model.RBuyorderSaleorder" >
    insert into T_R_BUYORDER_J_SALEORDER
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="rBuyorderJSaleorderId != null" >
        R_BUYORDER_J_SALEORDER_ID,
      </if>
      <if test="buyorderGoodsId != null" >
        BUYORDER_GOODS_ID,
      </if>
      <if test="saleorderGoodsId != null" >
        SALEORDER_GOODS_ID,
      </if>
      <if test="num != null" >
        NUM,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="rBuyorderJSaleorderId != null" >
        #{rBuyorderJSaleorderId,jdbcType=INTEGER},
      </if>
      <if test="buyorderGoodsId != null" >
        #{buyorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null" >
        #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.order.model.RBuyorderSaleorder" >
    update T_R_BUYORDER_J_SALEORDER
    <set >
      <if test="buyorderGoodsId != null" >
        BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="saleorderGoodsId != null" >
        SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
    </set>
    where R_BUYORDER_J_SALEORDER_ID = #{rBuyorderJSaleorderId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.order.model.RBuyorderSaleorder" >
    update T_R_BUYORDER_J_SALEORDER
    set BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER},
      SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER},
      NUM = #{num,jdbcType=INTEGER}
    where R_BUYORDER_J_SALEORDER_ID = #{rBuyorderJSaleorderId,jdbcType=INTEGER}
  </update>
    <!-- 查询采购订单对应的直发销售商品 -->
    <select id="getRBuyorderSaleorderInfoList" resultMap="BaseResultMap" >
  	SELECT
		a.R_BUYORDER_J_SALEORDER_ID,
		a.BUYORDER_GOODS_ID,
		a.SALEORDER_GOODS_ID,
		a.NUM
	FROM
		T_R_BUYORDER_J_SALEORDER a
	LEFT JOIN T_BUYORDER_GOODS b ON a.BUYORDER_GOODS_ID = b.BUYORDER_GOODS_ID
	LEFT JOIN T_SALEORDER_GOODS c ON a.SALEORDER_GOODS_ID = c.SALEORDER_GOODS_ID
	WHERE
		b.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
	AND c.IS_DELETE = 0
	AND c.DELIVERY_DIRECT = 1
  </select>
  <select id="getRBuyorderSaleorderInfoListByBuyorderIds" resultMap="BaseResultMap" parameterType="java.lang.Integer">
  	SELECT
		a.R_BUYORDER_J_SALEORDER_ID,
		a.BUYORDER_GOODS_ID,
		a.SALEORDER_GOODS_ID,
		a.NUM
	FROM
		T_R_BUYORDER_J_SALEORDER a
	LEFT JOIN T_BUYORDER_GOODS b ON a.BUYORDER_GOODS_ID = b.BUYORDER_GOODS_ID
	LEFT JOIN T_SALEORDER_GOODS c ON a.SALEORDER_GOODS_ID = c.SALEORDER_GOODS_ID
	WHERE
		b.BUYORDER_ID in
		<foreach collection="list" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
	AND c.IS_DELETE = 0
	AND c.DELIVERY_DIRECT = 1
  </select>
  <select id="getRBuyorderSaleorderListByParam" resultMap="BaseResultMap" >
  	select
  		a.R_BUYORDER_J_SALEORDER_ID, a.BUYORDER_GOODS_ID, a.SALEORDER_GOODS_ID,a.NUM
  	from
  		T_R_BUYORDER_J_SALEORDER a
  		left join T_BUYORDER_GOODS b on a.BUYORDER_GOODS_ID = b.BUYORDER_GOODS_ID
  		where b.BUYORDER_ID = #{buyorderId,jdbcType=INTEGER}
  </select>
  <select id="getBuyOrderGoodsIdBySaleOrderGoodsId" resultType="java.lang.Integer">
    select BUYORDER_GOODS_ID
    from T_R_BUYORDER_J_SALEORDER where SALEORDER_GOODS_ID = #{saleOrderGoodsId}
  </select>
  <select id="getBuyorderGoodsNumByParam" resultType="java.lang.Integer">
  	select
  		COALESCE(SUM(a.NUM),0)
  	from
  		T_R_BUYORDER_J_SALEORDER a
  	left join T_BUYORDER_GOODS b on a.BUYORDER_GOODS_ID = b.BUYORDER_GOODS_ID
  	left join T_BUYORDER c on c.BUYORDER_ID = b.BUYORDER_ID
  		where c.STATUS between 0 and 2 and a.BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
  </select>
  <select id="getBuyorderGoodsNum" resultType="java.lang.Integer">
  	SELECT
  		COALESCE(SUM(B.NUM),0)
  	FROM T_R_BUYORDER_J_SALEORDER B
	     LEFT JOIN T_BUYORDER_GOODS C ON B.BUYORDER_GOODS_ID = C.BUYORDER_GOODS_ID
	     LEFT JOIN T_BUYORDER D ON C.BUYORDER_ID = D.BUYORDER_ID
	WHERE D.STATUS <![CDATA[ <> ]]> 3 AND C.IS_DELETE = 0 AND B.SALEORDER_GOODS_ID = #{saleorderGoodsId,jdbcType=INTEGER}
  </select>

  <select id="queryBuyorderGoodsIdsBySaleorderGoodsIds" resultType="java.lang.Integer">
    SELECT B.BUYORDER_GOODS_ID
    FROM T_R_BUYORDER_J_SALEORDER A
    LEFT JOIN T_BUYORDER_GOODS B ON A.BUYORDER_GOODS_ID = B.BUYORDER_GOODS_ID
    LEFT JOIN T_BUYORDER C ON C.BUYORDER_ID=B.BUYORDER_ID
    WHERE
        A.NUM >0
    AND C.`STATUS`!=3
    AND B.IS_DELETE=0
    AND A.SALEORDER_GOODS_ID IN
      <foreach collection="saleorderGoodsIds" item="goodsId" index="index"
               open="(" close=")" separator=",">
          #{goodsId}
      </foreach>
    </select>
    <select id="getSaleOrderIdByBuyOrderGoodsId" resultType="java.lang.Integer">
        SELECT
            DISTINCT SG.SALEORDER_ID
        FROM
            T_R_BUYORDER_J_SALEORDER BS
            LEFT JOIN T_SALEORDER_GOODS SG ON SG.SALEORDER_GOODS_ID = BS.SALEORDER_GOODS_ID
        WHERE
            BS.BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
            AND SG.IS_DELETE = 0
    </select>
    <select id="getBuyorderIdBySaleorderIdAndGoodsId" resultType="java.util.Map">
        SELECT
            BG.BUYORDER_ID,SG.DELIVERY_NUM
        FROM
            T_SALEORDER_GOODS SG
            LEFT JOIN T_R_BUYORDER_J_SALEORDER BS ON SG.SALEORDER_GOODS_ID = BS.SALEORDER_GOODS_ID
            LEFT JOIN T_BUYORDER_GOODS BG ON BS.BUYORDER_GOODS_ID = BG.BUYORDER_GOODS_ID
        WHERE
            SG.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER}
            AND SG.GOODS_ID = #{goodsId,jdbcType=INTEGER}
            AND SG.IS_DELETE = 0
            AND BG.IS_DELETE = 0
    </select>

    <select id="querySaleorderGoodsIdByBuyorderGoodsId" resultType="java.lang.Integer">
        SELECT SALEORDER_GOODS_ID
        FROM T_R_BUYORDER_J_SALEORDER
        WHERE BUYORDER_GOODS_ID = #{buyorderGoodsId,jdbcType=INTEGER}
    </select>

  <select id="getBuyorderUserBySaleorderGoodsIds" resultType="java.lang.Integer">
      select distinct TB.USER_ID from T_R_BUYORDER_J_SALEORDER TRBJS left join T_BUYORDER_GOODS TBG on TRBJS.BUYORDER_GOODS_ID = TBG.BUYORDER_GOODS_ID
      left join T_BUYORDER TB on TBG.BUYORDER_ID = TB.BUYORDER_ID
      where TRBJS.SALEORDER_GOODS_ID in
      <foreach collection="saleorderGoodsIds" item="goodsId" index="index"
               open="(" close=")" separator=",">
          #{goodsId}
      </foreach>
    </select>

  <select id="getBuyorderUserBySaleorderIdAndDirect" resultType="java.lang.Integer">
      select distinct TB.USER_ID from T_R_BUYORDER_J_SALEORDER TRBJS left join T_BUYORDER_GOODS TBG on TRBJS.BUYORDER_GOODS_ID = TBG.BUYORDER_GOODS_ID
                                                                     left join T_BUYORDER TB on TBG.BUYORDER_ID = TB.BUYORDER_ID
          left join T_SALEORDER_GOODS TSG on TRBJS.SALEORDER_GOODS_ID = TSG.SALEORDER_GOODS_ID
      where TSG.SALEORDER_ID = #{saleorderId,jdbcType=INTEGER} and TB.DELIVERY_DIRECT = 1
      and TSG.IS_DELETE =0
    </select>
</mapper>