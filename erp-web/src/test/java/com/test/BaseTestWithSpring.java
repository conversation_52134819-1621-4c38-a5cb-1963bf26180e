package com.test;

import org.junit.runner.RunWith;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR> [<EMAIL>]
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring.xml", "classpath:spring-mybatis.xml"})
//@Transactional(value = BaseTestWithSpring.TARGET_TX_MGR_NAME, rollbackFor = Exception.class)
public abstract class BaseTestWithSpring {

    final static String TARGET_TX_MGR_NAME = "transactionManager";
}
