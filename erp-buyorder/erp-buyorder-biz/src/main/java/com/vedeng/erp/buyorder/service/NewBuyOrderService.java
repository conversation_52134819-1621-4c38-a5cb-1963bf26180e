package com.vedeng.erp.buyorder.service;

import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.buyorder.dto.BuyOrderExpressDto;
import com.vedeng.order.model.vo.BuyorderVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 订单流重构 service
 * @Date 2021/10/25 19:18
 */
public interface NewBuyOrderService {

    /**
     * 根据skuId计算在途数量
     *
     * @param skuId skuId
     * @return 在途数量
     */
    int getSkuOnWayBySkuId(@Param("skuId") Integer skuId);

    /**
     * 锁定采购单
     *
     * @param buyOrderId   采购单id
     * @param lockOrUnLock 解锁/锁定
     */
    void updateBuyOrderLockStatus(Integer buyOrderId, boolean lockOrUnLock);

    /**
     * 获取采购单关联的销售单收货信息，用于普发改直发时 填充收货信息
     *
     * @param bv 采购单信息
     * @return BuyorderVo
     */
    BuyorderVo getDirectDeliveryReceiveInfo(BuyorderVo bv);

    /**
     * 生成采购单
     *
     * @param buyorderVo buyorderVo
     * @param user       当前登录用户
     * @return buyOrderId
     */
    Integer createBuyOrder(BuyorderVo buyorderVo, User user);

    /**
     * 根基采购单获取关联的销售单id集合
     *
     * @param buyorderId 采购单id
     * @return 关联的销售单id集合
     */
    List<Integer> getSaleorderIdListByBuyorderId(Integer buyorderId);

    /**
     * 删除快递信息
     *
     * @param expressId 快递id
     * @return ResultInfo
     */
    ResultInfo deleteExpressByExpressId(Integer expressId);

    PageInfo<BuyOrderExpressDto> needConfirmList(PageParam<BuyOrderExpressDto> buyOrderExpressDtoPageParam);

    BigDecimal getUsedRebateCharge(Integer buyorderId,Integer traderId);
}
