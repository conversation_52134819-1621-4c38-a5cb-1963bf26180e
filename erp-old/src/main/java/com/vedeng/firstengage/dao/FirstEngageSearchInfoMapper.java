package com.vedeng.firstengage.dao;

import java.util.Map;

import com.vedeng.firstengage.model.FirstEngageSearchInfo;

public interface FirstEngageSearchInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_SEARCH_INFO
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    int deleteByPrimaryKey(Integer firstEngageSearchInfoId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_SEARCH_INFO
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    int insert(FirstEngageSearchInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_SEARCH_INFO
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    int insertSelective(FirstEngageSearchInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_SEARCH_INFO
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    FirstEngageSearchInfo selectByPrimaryKey(Integer firstEngageSearchInfoId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_SEARCH_INFO
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    int updateByPrimaryKeySelective(FirstEngageSearchInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_FIRST_ENGAGE_SEARCH_INFO
     *
     * @mbg.generated Wed Mar 20 18:39:49 CST 2019
     */
    int updateByPrimaryKey(FirstEngageSearchInfo record);

    /**
     * 根据关键词内容删除关键词
     * <p>Title: deleteByContent</p>  
     * <p>Description: </p>  
     * @param delMap
     * @return  
     * <AUTHOR>
     * @date 2019年4月1日
     */
	Integer deleteByContent(Map<String, Object> delMap);

	/**
	 * 根据关键词内容新增关键词
	 * <p>Title: insertSelectiveInfo</p>  
	 * <p>Description: </p>  
	 * @param delMap  
	 * <AUTHOR>
	 * @date 2019年4月1日
	 */
	void insertSelectiveInfo(Map<String, Object> delMap);
}