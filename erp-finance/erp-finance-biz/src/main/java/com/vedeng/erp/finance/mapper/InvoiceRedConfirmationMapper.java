package com.vedeng.erp.finance.mapper;
import com.vedeng.erp.finance.dto.RedConfirmationStatusStatistics;
import org.apache.ibatis.annotations.Param;

import com.vedeng.erp.finance.domain.entity.InvoiceRedConfirmationEntity;
import com.vedeng.erp.finance.domain.dto.InvoiceRedConfirmationDto;

import java.util.List;


/**
 * @description ${end}
 * <AUTHOR>
 * @date 2023/10/19 8:58
 **/
public interface InvoiceRedConfirmationMapper {
    int deleteByPrimaryKey(Integer invoiceRedConfirmationId);

    int insert(InvoiceRedConfirmationEntity record);

    int insertSelective(InvoiceRedConfirmationEntity record);

    InvoiceRedConfirmationEntity selectByPrimaryKey(Integer invoiceRedConfirmationId);

    int updateByPrimaryKeySelective(InvoiceRedConfirmationEntity record);

    int updateByPrimaryKey(InvoiceRedConfirmationEntity record);

    List<InvoiceRedConfirmationDto> findByAll(InvoiceRedConfirmationDto query);

    List<InvoiceRedConfirmationEntity> initCheckByAfterSaleBusinessOrderNo(@Param("afterSaleBusinessOrderNo") String afterSaleBusinessOrderNo, @Param("blueInvoiceNo") String blueInvoiceNo);

    List<RedConfirmationStatusStatistics> findRedConfirmationStatus();

    int updateBatchSelective(List<InvoiceRedConfirmationEntity> list);

    List<String> findUuid();

}