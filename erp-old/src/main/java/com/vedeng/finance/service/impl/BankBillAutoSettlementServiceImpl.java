package com.vedeng.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderDataUpdateConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.ErpCharUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.common.redis.redission.RedissonLockUtils;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.finance.dto.BankBillDto;
import com.vedeng.erp.finance.dto.BankBillQueryDto;
import com.vedeng.erp.finance.service.BankBillApiService;
import com.vedeng.erp.saleorder.dto.CheckSettlementDto;
import com.vedeng.erp.saleorder.service.SaleOrderApiService;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.CapitalBillDetail;
import com.vedeng.finance.model.vo.SettlementMessageVo;
import com.vedeng.finance.service.BankBillAutoSettlementService;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.finance.service.FinanceSendMessageService;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.OrderCommonService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.UserService;
import com.wms.service.other.LogicalSaleorderChooseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description
 * @date 2024/1/6 10:00
 **/
@Service
@Slf4j
public class BankBillAutoSettlementServiceImpl implements BankBillAutoSettlementService {

    @Autowired
    private BankBillApiService bankBillApiService;

    @Autowired
    @Qualifier("capitalBillService")
    private CapitalBillService capitalBillService;

    @Autowired
    @Qualifier("saleorderService")
    private SaleorderService saleorderService;

    @Autowired
    @Qualifier("userService")
    private UserService userService;

    @Value("${autoBankTime}")
    public int autoBankTime;

    @Resource
    private OrderCommonService orderCommonService;

    @Autowired
    private SaleOrderApiService saleOrderApiService;


    @Autowired
    private LogicalSaleorderChooseServiceImpl logicalSaleorderChooseServiceImpl;

    // 和手动点的业务 同一个key
    private final static String REDIS_KEY = "ERP:BATCH:SETTLEMENT:";
    private final static String REDIS_KEY_LOAD_TOTAL = "ERP:BATCH:SETTLEMENT:LOADING:TOTAL";
    private final static String REDIS_KEY_LOAD_DONE = "ERP:BATCH:SETTLEMENT:LOADING:DONE";

    @Override
    public void doSettlement(BankBillDto bankBillDto, Integer saleOrderId, Date endTime) {

        String key = REDIS_KEY + bankBillDto.getBankBillId();
        // 加锁
        boolean lock = RedissonLockUtils.tryLock(key);

        if (!lock) {
            log.info("加锁失败，bankBillId:{},存在处理中的结款业务", bankBillDto.getBankBillId());
            return;
        }


        try {
            log.info("doSettlement: 入参：{},{}", JSON.toJSONString(bankBillDto), saleOrderId);
            BankBillDto check = bankBillApiService.checkConstructionBankBill(bankBillDto);
            if (Objects.isNull(check)) {
                log.info("bankBillId:{},数据状态已经改变", bankBillDto.getBankBillId());
                return;
            }
            Integer integer = checkBankBillSaleOrder(bankBillDto, endTime);
            if (Objects.isNull(integer)) {
                log.info("bankBillId:{},销售单：{}状态已经改变", bankBillDto.getBankBillId(), saleOrderId);
                return;
            }
            saleOrderId = integer;

            // 处理业务
            CapitalBill data = new CapitalBill();
            CurrentUser currentUser = CurrentUser.getCurrentUser();
            User belongUser = new User();
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderId(saleOrderId);
            Saleorder saleorderInfo = saleorderService.getBaseSaleorderInfo(saleorder);

            if (saleorderInfo.getTraderId() != null) {
                // 1客户，2供应商
                belongUser = userService.getUserByTraderId(saleorderInfo.getTraderId(), 1);
                if (belongUser != null && belongUser.getUserId() != null) {
                    belongUser = userService.getUserById(belongUser.getUserId());
                }
            }
            // 资金流水赋值
            data.setType(0);
            data.setCreator(currentUser.getId());
            data.setAddTime(DateUtil.sysTimeMillis());
            data.setCompanyId(currentUser.getCompanyId());
            data.setBankBillId(bankBillDto.getBankBillId());
            // 根据借款金额判断是付预付款，还是付账期

            ResultInfo<?> result = new ResultInfo<>();

            // 交易方式银行
            data.setTraderMode(521);
            data.setTranFlow(bankBillDto.getTranFlow());
            data.setCurrencyUnitId(1);
            data.setTraderTime(DateUtil.sysTimeMillis());
            data.setTraderType(1);
            data.setPayerBankAccount(bankBillDto.getAccno2());
            data.setPayerBankName(bankBillDto.getCadbankNm());
            data.setPayer(bankBillDto.getAccName1());
            data.setPayee(ErpConstant.BD);
            data.setAmount(bankBillDto.getAmt().abs());
            data.setTraderSubject(1);
            List<CapitalBillDetail> capitalBillDetails = new ArrayList<>();
            CapitalBillDetail capitalBillDetail = new CapitalBillDetail();

            capitalBillDetail.setOrderType(1);
            capitalBillDetail.setOrderNo(saleorderInfo.getSaleorderNo());
            capitalBillDetail.setRelatedId(saleorderInfo.getSaleorderId());
            capitalBillDetail.setTraderType(1);
            capitalBillDetail.setTraderId(saleorderInfo.getTraderId());
            capitalBillDetail.setUserId(saleorderInfo.getUserId());
            capitalBillDetail.setBussinessType(526);//交易类型订单收款
            capitalBillDetail.setAmount(data.getAmount());
            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetail.setOrgName(belongUser.getOrgName());
                capitalBillDetail.setOrgId(belongUser.getOrgId());
            }
            // 订单收款
            capitalBillDetails.add(capitalBillDetail);
            data.setCapitalBillDetails(capitalBillDetails);

            CapitalBillDetail capitalBillDetailInfo = new CapitalBillDetail();
            capitalBillDetailInfo.setOrderType(1);
            capitalBillDetailInfo.setOrderNo(saleorderInfo.getSaleorderNo());
            capitalBillDetailInfo.setRelatedId(saleorderInfo.getSaleorderId());
            capitalBillDetailInfo.setTraderType(1);
            capitalBillDetailInfo.setTraderId(saleorderInfo.getTraderId());
            capitalBillDetailInfo.setUserId(saleorderInfo.getUserId());
            capitalBillDetailInfo.setBussinessType(526);//交易类型订单收款
            capitalBillDetailInfo.setAmount(data.getAmount());
            if (belongUser != null && belongUser.getOrgName() != null && belongUser.getOrgId() != null) {
                capitalBillDetailInfo.setOrgName(belongUser.getOrgName());
                capitalBillDetailInfo.setOrgId(belongUser.getOrgId());
            }
            //VDERP-1327  订单结款自动生成流水记录
            String payer = data.getPayer();
            data.setCapitalBillDetail(capitalBillDetailInfo);
            result = capitalBillService.saveAddCapitalBill(data);
            //VDERP-1327  订单结款自动生成流水记录
            if (!StringUtils.isEmpty(payer) && result.getCode() == 0) {
                if (payer.equals(ErpConst.TAOBAO) || payer.equals(ErpConst.WEIXIN)) {
                    capitalBillService.saveSecondCapitalBill(saleorderInfo, data);
                }
            }

            if (result.getCode() == 0) {
                User user = new User();
                user.setUserId(currentUser.getId());
                // 下发wms
                logicalSaleorderChooseServiceImpl.chooseLogicalSaleorder(saleorderInfo, user);
                // 订单售后采购改动通知
                orderCommonService.updateSaleOrderDataUpdateTime(saleOrderId, null, OrderDataUpdateConstant.SALE_ORDER_PAY);
            }

            if (result.getCode() == 0 && Objects.nonNull(belongUser) && !StringUtils.isEmpty(belongUser.getUserId())){
                SettlementMessageVo settlementMessageVo = new SettlementMessageVo();
                settlementMessageVo.setUserId(belongUser.getUserId());
                settlementMessageVo.setSaleOrderNo(saleorderInfo.getSaleorderNo());
                settlementMessageVo.setTraderName(saleorderInfo.getTraderName());
                settlementMessageVo.setActualAmount(saleorderInfo.getTotalAmount());
                settlementMessageVo.setCurrentAmount(data.getAmount());

                BigDecimal totalAmount1 = saleorderInfo.getTotalAmount();
                BigDecimal realPayAmount = saleorderInfo.getRealPayAmount();
                BigDecimal realPayAmount1 = realPayAmount.add(data.getAmount());
                settlementMessageVo.setRemainAmount(totalAmount1.subtract(realPayAmount1));
                log.info("自动结款发送结款信息:{}",JSON.toJSON(settlementMessageVo));
                financeSendMessageService.sendMessage(settlementMessageVo);
            }else {
                log.info("发送结款信息失败:{}",saleorderInfo.getSaleorderNo());
            }
           
        } catch (Exception e) {
            log.error("建设银行bankBillId:{},自动结款异常：", bankBillDto.getBankBillId(), e);
        } finally {
            RedissonLockUtils.unlock(key);
            log.info("结款释放锁成功, key = [{}]", key);
        }
    }
    
    @Resource
    private FinanceSendMessageService financeSendMessageService;


    @Override
    public void doWithPage(Date beginTime, Date endTime, boolean loading) {

        // 分页
        int pageSize = 100;
        int pageNum = 1;
        PageParam<BankBillQueryDto> page = new PageParam<>();
        page.setPageSize(pageSize);
        page.setPageNum(pageNum);
        BankBillQueryDto bankBillDto = new BankBillQueryDto();
        //1）非手续费：T_BANK_BILL.IS_FEE = 0 OR T_BANK_BILL.IS_FEE IS NULL
        //2）未忽略：T_BANK_BILL.STATUS = 0
        //3）未结算：T_BANK_BILL.MATCHED_AMOUNT = 0
        //4）收入：T_BANK_BILL.FLAG1 = 1
        //5）建行：T_BANK_BILL.BANK_TAG = 1
        bankBillDto.setBeginTime(beginTime);
        bankBillDto.setEndTime(endTime);
//        bankBillDto.setBankTag(1);
        bankBillDto.setStatus(0);
        bankBillDto.setFlag1(1);
        bankBillDto.setMatchedAmount(BigDecimal.ZERO);
        page.setParam(bankBillDto);

        PageInfo<BankBillDto> data = bankBillApiService.queryConstructionBankBill(page);
        // 总条数
        if (loading) {
            long total = data.getTotal();
            RedisUtil.KeyOps.delete(REDIS_KEY_LOAD_TOTAL);
            RedisUtil.KeyOps.delete(REDIS_KEY_LOAD_DONE);
            RedisUtil.StringOps.incrBy(REDIS_KEY_LOAD_TOTAL, total);
            RedisUtil.StringOps.incrBy(REDIS_KEY_LOAD_DONE, 0);
        }

        pageNum = data.getPages();

        while (true) {
            log.info("分页执行进度 {},{}", pageSize, pageNum);
            // 调用开票
            PageParam<BankBillQueryDto> pageDes = new PageParam<>();
            pageDes.setPageSize(pageSize);
            pageDes.setPageNum(pageNum);
            pageDes.setParam(bankBillDto);
            PageInfo<BankBillDto> bankBillDtoPageInfo = bankBillApiService.queryConstructionBankBill(pageDes);
            List<BankBillDto> list = bankBillDtoPageInfo.getList();
            if (list.isEmpty()) {
                break;
            }
            list.forEach(x -> {
                try {
                    Integer saleOrderId = checkBankBillSaleOrder(x, endTime);
                    // 按条处理
                    if (Objects.nonNull(saleOrderId)) {
                        doSettlement(x, saleOrderId, endTime);
                    }

                } catch (Exception e) {
                    log.error("交行流水：{}, doWithPage 执行失败", JSON.toJSONString(x), e);
                }
                if (loading) {
                    RedisUtil.StringOps.incrBy(REDIS_KEY_LOAD_DONE, 1);
                }

            });

            if (!bankBillDtoPageInfo.isHasPreviousPage()) {
                break;
            }
            pageNum = bankBillDtoPageInfo.getPrePage();
            log.info("分页查询进度 {},{}", pageSize, pageNum);
        }
    }


    @Override
    public Integer checkBankBillSaleOrder(BankBillDto bankBillDto, Date date) {

        log.info("checkBankBill:入参：{}", JSON.toJSONString(bankBillDto));
        //1 订单已生效：生效状态 = 已生效
        //2 进行中：订单单据状态 = 进行中
        //3 未收款：收款状态 = 未收款
        //4 未锁定：订单没有锁定
        //5 非账期订单：T_SALEORDER.HAVE_ACCOUNT_PERIOD = 0
        //6 名称相同：T_BANK_BILL.ACC_NAME1 = T_SALEORDER.TRADER_NAME
        //7 金额相同：T_BANK_BILL.AMT = T_SALEORDER.REAL_TOTAL_AMOUNT
        //8 交易时间5天内：绝对值（T_BANK_BILL.REAL_TRANDATETIME- T_SALEORDER.VALID_TIME）<= 5 /*以交易时间为起点，前后5天*/
        //9 有且仅有一条

        List<Integer> integers = matchOrder(bankBillDto, date);
        if (CollUtil.isEmpty(integers)) {
            // 判断TraderName是否有中英文空号，如果有统一先转中文，再转中文，然后再转左中右英，最后转左英右中
            String accName1 = bankBillDto.getAccName1();
            boolean isContains = ErpCharUtils.containsBrackets(accName1);
            if (!isContains){
                log.info("未匹配到订单数据");
                return null;
            }
            log.info("含中英文括号：{}", accName1);
            for (int i = 0 ; i < 3; i++){
                switch (i){
                    case 0:
                        accName1 = ErpCharUtils.toChinese(accName1);
                        break;
                    case 1:
                        accName1 = ErpCharUtils.toEnglish(accName1);
                        break;
                    case 2:
                        accName1 = ErpCharUtils.toLeftChineseRightEnglish(accName1);
                        break;
                    case 3:
                        accName1 = ErpCharUtils.toLeftEnglishRightChinese(accName1);
                        break;
                }
                bankBillDto.setAccName1(accName1);
                integers = matchOrder(bankBillDto, date);
                if (CollUtil.isNotEmpty(integers)){
                    log.info("匹配到订单数据：{}", JSON.toJSONString(integers));
                    break;
                }
            }

            if (CollUtil.isEmpty(integers)) {
                log.info("未匹配到订单数据");
                return null;
            }
        }

        if (integers.size() == 1) {
            return integers.get(0);
        }
        log.info("bankBill:{},存在多条匹配的订单数据：{}", bankBillDto.getBankBillId(), JSON.toJSONString(integers));
        return null;
    }

    private List<Integer> matchOrder(BankBillDto bankBillDto, Date date) {
        CheckSettlementDto data = new CheckSettlementDto();
        data.setTraderName(bankBillDto.getAccName1());
        data.setAmount(bankBillDto.getAmt());
        long beginTime = cn.hutool.core.date.DateUtil.offsetDay(bankBillDto.getRealTrandatetime(), -autoBankTime).getTime();
        long endTime = cn.hutool.core.date.DateUtil.offsetDay(bankBillDto.getRealTrandatetime(), autoBankTime).getTime();
        long currentTime = cn.hutool.core.date.DateUtil.offsetSecond(date, -10).getTime();
        log.info("checkBankBillSaleOrder,endTime:{},currentTime:{}", endTime, currentTime);
        data.setBeginTime(beginTime);
        data.setEndTime(Math.min(currentTime, endTime));
        List<Integer> integers = saleOrderApiService.checkSettlement(data);
        return integers;
    }

   


}
