package com.vedeng.price.dao;

import com.vedeng.price.model.PriceChangeReadRecord;
import java.util.List;

public interface PriceChangeReadRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_READ_RECORED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    int deleteByPrimaryKey(Long priceChangeReadRecoredId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_READ_RECORED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    int insert(PriceChangeReadRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_READ_RECORED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    PriceChangeReadRecord selectByPrimaryKey(Long priceChangeReadRecoredId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_READ_RECORED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    List<PriceChangeReadRecord> selectAll();

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table T_PRICE_CHANGE_READ_RECORED
     *
     * @mbggenerated Tue Aug 24 15:38:49 CST 2021
     */
    int updateByPrimaryKey(PriceChangeReadRecord record);

    PriceChangeReadRecord getLatestReadRecordByUserId(Integer userId);
}