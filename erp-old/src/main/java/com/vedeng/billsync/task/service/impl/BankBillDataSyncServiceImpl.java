package com.vedeng.billsync.task.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.billsync.dao.BankBillExtMapper;
import com.vedeng.billsync.dao.TmpAlipayBillDataExtMapper;
import com.vedeng.billsync.dao.TmpWechatBillDataExtMapper;
import com.vedeng.billsync.task.model.entity.BankBillExtDo;
import com.vedeng.billsync.task.model.entity.TmpAlipayBillDataExtDo;
import com.vedeng.billsync.task.model.entity.TmpWechatBillDataExtDo;
import com.vedeng.billsync.task.service.BankBillDataSyncService;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.service.CapitalBillService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class BankBillDataSyncServiceImpl implements BankBillDataSyncService {

    @Autowired
    TmpAlipayBillDataExtMapper tmpAlipayBillDataExtMapper;

    @Autowired
    TmpWechatBillDataExtMapper tmpWechatBillDataExtMapper;

    @Autowired
    BankBillExtMapper bankBillExtMapper;
    @Autowired
    CapitalBillService capitalBillService;

    @Autowired
    private SaleorderMapper saleorderMapper;

    @Autowired
    private AfterSalesMapper afterSalesMapper;
    @Override
    public ResultInfo syncBillDate2bankBill(Date queryDate,String type,Integer tag) {

        try {

            //区别支付宝和微信分开进行

            if(tag.equals(4)) {
                //支付宝
                saveAplipay(queryDate);
            }

            if(tag.equals(5)) {
                //微信拆分成两条
                saveWechat(queryDate);
            }

            if(tag.equals(-1)){
                //支付宝
                saveAplipay(queryDate);
                //微信
                saveWechat(queryDate);
            }
        } catch (Exception e) {
            log.info("数据同步失败，错误信息：{}",e.getMessage().toString());
            return new ResultInfo(-1,"插入失败");
        }
        return new ResultInfo(0,"插入流水成功");
    }

    @Override
    public ResultInfo syncBillDate2bankBillHistory(Date queryDate, Integer tag,String type) {
        if ("log".equals(type)){
            try {
                log.info("历史数据任务日志-开始");
                //区别支付宝和微信分开进行
                if(tag.equals(4)) {
                    //支付宝
                    saveAplipayHistoryLog(queryDate);
                }
                if(tag.equals(5)) {
                    //微信拆分成两条
                    saveWechatHistoryLog(queryDate);
                }
                log.info("历史数据任务日志-结束");
            } catch (Exception e) {
                log.info("更新前台流水历史数据成功失败，错误信息：{}",e.getMessage().toString());
                return new ResultInfo(-1,"插入失败");
            }
        }
        if ("history".equals(type)){
            try {
                log.info("历史数据任务-开始");
                //区别支付宝和微信分开进行
                if(tag.equals(4)) {
                    //支付宝
                    saveAplipayHistory(queryDate);
                }
                if(tag.equals(5)) {
                    //微信拆分成两条
                    saveWechatHistory(queryDate);
                }
                log.info("历史数据任务-结束");
            } catch (Exception e) {
                log.info("更新前台流水历史数据成功失败，错误信息：{}",e.getMessage().toString());
                return new ResultInfo(-1,"插入失败");
            }
        }



        return new ResultInfo(0,"更新前台流水历史数据成功");
    }

    /**
     * 保存微信数据
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/22 9:41.
     * @author: Randy.Xu.
     * @param queryDate
     * @return: void.
     * @throws:  .
     */
    private void saveWechatHistoryLog(Date queryDate) {
        List<TmpWechatBillDataExtDo> tmpWechatBillDataExtDoList = tmpWechatBillDataExtMapper.getWechatBillListByTime(queryDate);
        if (CollectionUtils.isNotEmpty(tmpWechatBillDataExtDoList)) {
            for (TmpWechatBillDataExtDo tmpWechatBillDataExtDo : tmpWechatBillDataExtDoList) {
                List<BankBillExtDo> bankBillExtDoList = convertWechatDo2BankBill(tmpWechatBillDataExtDo);
                if (CollectionUtils.isNotEmpty(bankBillExtDoList)) {
                    for (BankBillExtDo bankBillExtDo : bankBillExtDoList) {
                        BankBillExtDo wechatBankBillExtDoByTranFlow = bankBillExtMapper.getWechatBankBillExtDoByTranFlow(bankBillExtDo);
                        if (null == wechatBankBillExtDoByTranFlow){
                            //没有就插入
                            log.info("历史数据任务-插入微信流水,微信流水信息", bankBillExtDo.getTranFlow());
                        }else {
                            log.info("历史数据任务-微信流水有数据，流水号:{}", bankBillExtDo.getTranFlow());
                        }
                    }

                }
            }
        }else {
            log.info("历史数据任务-未查询到数据:{}", queryDate);
        }
    }

    /**
     * 保存微信数据
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/22 9:41.
     * @author: Randy.Xu.
     * @param queryDate
     * @return: void.
     * @throws:  .
     */
    private void saveWechat(Date queryDate) {
        List<TmpWechatBillDataExtDo> tmpWechatBillDataExtDoList = tmpWechatBillDataExtMapper.getWechatBillListByTime(queryDate);
        if (CollectionUtils.isNotEmpty(tmpWechatBillDataExtDoList)) {
            for (TmpWechatBillDataExtDo tmpWechatBillDataExtDo : tmpWechatBillDataExtDoList) {
                List<BankBillExtDo> bankBillExtDoList = convertWechatDo2BankBill(tmpWechatBillDataExtDo);
                if (CollectionUtils.isNotEmpty(bankBillExtDoList)) {
                    for (BankBillExtDo bankBillExtDo : bankBillExtDoList) {
                        BankBillExtDo wechatBankBillExtDoByTranFlow = bankBillExtMapper.getWechatBankBillExtDoByTranFlow(bankBillExtDo);
                        if (null == wechatBankBillExtDoByTranFlow){
                            //没有就插入
                            bankBillExtMapper.insertSingleByObject(bankBillExtDo);
                            log.info("插入微信流水,微信流水信息", bankBillExtDo.getTranFlow());
                        }else{
                            //存在
                            bankBillExtDo.setBankBillId(wechatBankBillExtDoByTranFlow.getBankBillId());
                            bankBillExtMapper.updateSingleByObjectSelective(bankBillExtDo);
                            log.info("更新已存在微信流水,微信流水信息", bankBillExtDo.getTranFlow());
                        }

                        if((bankBillExtDo.getIsFee()==null||bankBillExtDo.getIsFee()==0)){
                            List<CapitalBill> capitalBillListByOrderNo = capitalBillService.getCapitalBillListByOrderNo(bankBillExtDo);
                            if (CollUtil.isNotEmpty(capitalBillListByOrderNo)) {
                                for (int i = 0; i < capitalBillListByOrderNo.size(); i++) {
                                    CapitalBill capitalBill = capitalBillListByOrderNo.get(i);
                                    // 记录更新前的信息
                                    log.info("微信流水处理自动匹配资金流水 CapitalBill: {}", JSON.toJSONString(capitalBill));
                                    CapitalBill update = new CapitalBill();
                                    update.setCapitalBillId(capitalBill.getCapitalBillId());
                                    update.setBankBillId(bankBillExtDo.getBankBillId());
                                    update.setTranFlow(bankBillExtDo.getTranFlow());
                                    // 付款
                                    if (bankBillExtDo.getFlag1().equals(0)) {
                                        update.setPayee(bankBillExtDo.getAccName1());
                                        update.setPayeeBankAccount(bankBillExtDo.getAccno2());
                                        update.setPayeeBankName(bankBillExtDo.getCadbankNm());
                                    }
                                    // 收款
                                    if (bankBillExtDo.getFlag1().equals(1)) {
                                        update.setPayer(bankBillExtDo.getAccName1());
                                        update.setPayerBankAccount(bankBillExtDo.getAccno2());
                                        update.setPayerBankName(bankBillExtDo.getCadbankNm());
                                    }
                                    log.info("更新 CapitalBill: {}", JSON.toJSONString(update));
                                    capitalBillService.updateCapitalBillBankData(update);
                                    // 记录更新后的信息
                                    log.info("微信自动匹配资金流水CapitalBill 已更新：TranFlow={}, BankBillId={}", bankBillExtDo.getTranFlow(), bankBillExtDo.getBankBillId());
                                }
                            } else {
                                log.info("未找到 OrderNo 对应的 CapitalBill：{}", bankBillExtDo.getOrderNo());
                            }

                        }else {
                            log.info("手续费用 bankBillId：{}", bankBillExtDo.getBankBillId());
                        }
                    }

                }
            }
        }
    }

    /**
     * 保存支付宝数据
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/22 9:42.
     * @author: Randy.Xu.
     * @param queryDate
     * @return: void.
     * @throws:  .
     */
    private void saveAplipay(Date queryDate) {
        List<TmpAlipayBillDataExtDo> tmpAlipayBillDataExtDoList = tmpAlipayBillDataExtMapper.getAlipayBillListByTime(queryDate);

        if (CollectionUtils.isNotEmpty(tmpAlipayBillDataExtDoList)) {
            for (TmpAlipayBillDataExtDo tmpAlipayBillDataExtDo : tmpAlipayBillDataExtDoList) {
                BankBillExtDo bankBillExtDo = convertAlipayDo2BankBill(tmpAlipayBillDataExtDo);
                if (null != bankBillExtDo) {
                    //首先查询库中有无该项
                    BankBillExtDo bankBillExtDoByTranFlow = bankBillExtMapper.getAliBankBillExtDoByTranFlow(bankBillExtDo);
                    if (null == bankBillExtDoByTranFlow) {
                        //没有就插入
                        bankBillExtMapper.insertSingleByObject(bankBillExtDo);
                        log.info("插入支付宝流水，流水号:{}", bankBillExtDo.getTranFlow());
                    } else {
                        bankBillExtDo.setBankBillId(bankBillExtDoByTranFlow.getBankBillId());
                        log.info("已存在支付宝流水，支付宝流水号：{}", bankBillExtDo.getTranFlow());
                        bankBillExtMapper.updateSingleByObjectSelective(bankBillExtDo);
                        log.info("更新已存在支付宝流水，支付宝流水号：{}", bankBillExtDo.getTranFlow());
                    }
                }

                if(Objects.nonNull(bankBillExtDo)&&(bankBillExtDo.getIsFee()==null||bankBillExtDo.getIsFee()==0)){
                    List<CapitalBill> capitalBillListByOrderNo = capitalBillService.getCapitalBillListByOrderNo(bankBillExtDo);
                    if (CollUtil.isNotEmpty(capitalBillListByOrderNo)) {
                        for (int i = 0; i < capitalBillListByOrderNo.size(); i++) {
                            CapitalBill capitalBill = capitalBillListByOrderNo.get(i);
                            // 记录更新前的信息
                            log.info("支付宝流水处理自动匹配资金流水 CapitalBill: {}", JSON.toJSONString(capitalBill));
                            CapitalBill update = new CapitalBill();
                            update.setCapitalBillId(capitalBill.getCapitalBillId());
                            update.setBankBillId(bankBillExtDo.getBankBillId());
                            update.setTranFlow(bankBillExtDo.getTranFlow());
                            // 付款
                            if (bankBillExtDo.getFlag1().equals(0)) {
                                update.setPayee(bankBillExtDo.getAccName1());
                                update.setPayeeBankAccount(bankBillExtDo.getAccno2());
                                update.setPayeeBankName(bankBillExtDo.getCadbankNm());
                            }
                            // 收款
                            if (bankBillExtDo.getFlag1().equals(1)) {
                                update.setPayer(bankBillExtDo.getAccName1());
                                update.setPayerBankAccount(bankBillExtDo.getAccno2());
                                update.setPayerBankName(bankBillExtDo.getCadbankNm());
                            }
                            log.info("更新 CapitalBill: {}", JSON.toJSONString(update));
                            capitalBillService.updateCapitalBillBankData(update);
                            // 记录更新后的信息
                            log.info("支付宝自动匹配资金流水CapitalBill 已更新：TranFlow={}, BankBillId={}", bankBillExtDo.getTranFlow(), bankBillExtDo.getBankBillId());
                        }
                    } else {
                        log.info("未找到 OrderNo 对应的 CapitalBill：{}", bankBillExtDo.getOrderNo());
                    }

                }else {
                    log.info("手续费用 bankBillId：{}", JSON.toJSONString(bankBillExtDo));
                }

                try {
                    // 新增支付宝银行流水后匹配销售售后资金流水
                    matchCapitalBill(bankBillExtDo);
                } catch (Exception e) {
                    log.error("新增支付宝银行流水后匹配销售售后资金流水异常", e);
                }

            }
        }
    }

    private void matchCapitalBill(BankBillExtDo bankBillExtDo) {
        if (Objects.nonNull(bankBillExtDo) &&
                StringUtils.isNotBlank(bankBillExtDo.getCapitalSearchFlow()) &&
                Objects.nonNull(bankBillExtDo.getAmt()) &&
                ErpConst.ONE.equals(bankBillExtDo.getFlag1()) &&
                (Objects.isNull(bankBillExtDo.getIsFee()) || ErpConst.ZERO.equals(bankBillExtDo.getIsFee()))) {
            log.info("新增支付宝银行流水后匹配销售售后资金流水：{}", JSON.toJSONString(bankBillExtDo));
            List<CapitalBill> capitalBillList = capitalBillService.matchAlipayCapitalBillByCapitalSearchFlow(bankBillExtDo.getCapitalSearchFlow());
            if (CollectionUtils.isEmpty(capitalBillList)) {
                log.info("新增支付宝银行流水后匹配销售售后资金流水，未匹配到销售售后资金流水：{}", bankBillExtDo.getCapitalSearchFlow());
            } else if (capitalBillList.size() > 1) {
                log.error("新增支付宝银行流水后匹配销售售后资金流水，支付宝订单号匹配多条资金流水：{}", JSON.toJSONString(capitalBillList));
            } else {
                CapitalBill capitalBill = capitalBillList.get(0);
                BigDecimal resultAmount = bankBillExtDo.getAmt()
                        .subtract(Objects.isNull(bankBillExtDo.getMatchedAmount()) ? BigDecimal.ZERO : bankBillExtDo.getMatchedAmount())
                        .subtract(capitalBill.getAmount());
                if (resultAmount.compareTo(BigDecimal.ZERO) < 0) {
                    log.error("新增支付宝银行流水后匹配销售售后资金流水，交易金额大于流水可结算金额：{}", JSON.toJSONString(capitalBill));
                } else {
                    BigDecimal updateMatchedAmount = Objects.isNull(bankBillExtDo.getMatchedAmount()) ? capitalBill.getAmount() : bankBillExtDo.getMatchedAmount().add(capitalBill.getAmount());
                    bankBillExtMapper.updataBankBillMatchedAmount(bankBillExtDo.getBankBillId(), updateMatchedAmount);
                    log.info("新增支付宝银行流水后匹配销售售后资金流水，更新银行流水：bankBillId={}, matchedAmount={}", bankBillExtDo.getBankBillId(), updateMatchedAmount);
                    capitalBillService.updateCapitalBillByBankBillIdAndTranFlow(bankBillExtDo.getBankBillId(), capitalBill.getCapitalBillId(), bankBillExtDo.getTranFlow());
                    log.info("新增支付宝银行流水后匹配销售售后资金流水，更新销售售后资金流水：capitalBillId={}, bankBillId={}, tranFlow={}", capitalBill.getCapitalBillId(), bankBillExtDo.getBankBillId(), bankBillExtDo.getTranFlow());
                }
            }
        }
    }

    /**
     * 刷新支付宝历史前台流水数据日志
     * @jira: .
     * @notes: .
     */
    private void saveAplipayHistoryLog(Date queryDate) {
        List<TmpAlipayBillDataExtDo> tmpAlipayBillDataExtDoList = tmpAlipayBillDataExtMapper.getAlipayBillListByTime(queryDate);

        if (CollectionUtils.isNotEmpty(tmpAlipayBillDataExtDoList)) {
            for (TmpAlipayBillDataExtDo tmpAlipayBillDataExtDo : tmpAlipayBillDataExtDoList) {
                BankBillExtDo bankBillExtDo = convertAlipayDo2BankBill(tmpAlipayBillDataExtDo);
                if (null != bankBillExtDo) {
                    //首先查询库中有无该项
                    BankBillExtDo bankBillExtDoByTranFlow = bankBillExtMapper.getAliBankBillExtDoByTranFlow(bankBillExtDo);
                    if (null == bankBillExtDoByTranFlow) {
                        log.info("历史数据任务-插入支付宝流水，流水号:{}", bankBillExtDo.getTranFlow());
                    }else {
                        log.info("历史数据任务-支付宝流水有数据，流水号:{}", bankBillExtDo.getTranFlow());
                    }
                }
            }
        }else {
            log.info("历史数据任务-未查询到数据:{}", queryDate);
        }
    }


    /**
     * 刷新支付宝历史前台流水数据
     * @jira: .
     * @notes: .
     */
    private void saveAplipayHistory(Date queryDate) {
        List<TmpAlipayBillDataExtDo> tmpAlipayBillDataExtDoList = tmpAlipayBillDataExtMapper.getAlipayBillListByTime(queryDate);

        if (CollectionUtils.isNotEmpty(tmpAlipayBillDataExtDoList)) {
            for (TmpAlipayBillDataExtDo tmpAlipayBillDataExtDo : tmpAlipayBillDataExtDoList) {
                BankBillExtDo bankBillExtDo = convertAlipayDo2BankBill(tmpAlipayBillDataExtDo);
                if (null != bankBillExtDo) {
                    //首先查询库中有无该项
                    BankBillExtDo bankBillExtDoByTranFlow = bankBillExtMapper.getAliBankBillExtDoByTranFlow(bankBillExtDo);
                    if (null == bankBillExtDoByTranFlow) {
                        log.info("历史数据任务-插入支付宝流水，流水号:{}", bankBillExtDo.getTranFlow());
                    } else {
                        bankBillExtDo.setBankBillId(bankBillExtDoByTranFlow.getBankBillId());
                        log.info("历史数据任务-更新已存在支付宝流水，支付宝流水号：{}", bankBillExtDo.getTranFlow());
                    }
                }
                if (Objects.nonNull(bankBillExtDo) && (Objects.isNull(bankBillExtDo.getIsFee()) || bankBillExtDo.getIsFee() == 0)) {
                    List<CapitalBill> capitalBillListByOrderNo = capitalBillService.getCapitalBillListByOrderNo(bankBillExtDo);
                    if (CollUtil.isNotEmpty(capitalBillListByOrderNo)) {
                        for (int i = 0; i < capitalBillListByOrderNo.size(); i++) {
                            CapitalBill capitalBill = capitalBillListByOrderNo.get(i);
                            // 记录更新前的信息
                            log.info("历史数据任务-支付宝流水处理自动匹配资金流水 CapitalBill: {}", JSON.toJSONString(capitalBill));

                            CapitalBill update = new CapitalBill();
                            update.setCapitalBillId(capitalBill.getCapitalBillId());
                            update.setBankBillId(bankBillExtDo.getBankBillId());
                            update.setTranFlow(bankBillExtDo.getTranFlow());
                            // 付款
                            if (bankBillExtDo.getFlag1().equals(0)) {
                                update.setPayee(bankBillExtDo.getAccName1());
                                update.setPayeeBankAccount(bankBillExtDo.getAccno2());
                                update.setPayeeBankName(bankBillExtDo.getCadbankNm());
                            }
                            // 收款
                            if (bankBillExtDo.getFlag1().equals(1)) {
                                update.setPayer(bankBillExtDo.getAccName1());
                                update.setPayerBankAccount(bankBillExtDo.getAccno2());
                                update.setPayerBankName(bankBillExtDo.getCadbankNm());
                            }
                            log.info("更新 CapitalBill: {}", JSON.toJSONString(update));
                            capitalBillService.updateCapitalBillBankData(update);

                            // 记录更新后的信息
                            log.info("历史数据任务-支付宝自动匹配资金流水CapitalBill 已更新：TranFlow={}, BankBillId={}", bankBillExtDo.getTranFlow(), bankBillExtDo.getBankBillId());
                        }
                    } else {
                        log.info("历史数据任务-未找到 OrderNo 对应的 CapitalBill：{}", bankBillExtDo.getOrderNo());
                    }

                } else {
                    log.info("手续费用 bankBillId：{}", JSON.toJSONString(bankBillExtDo));
                }
            }
        }else {
            log.info("历史数据任务-未查询到数据:{}", queryDate);
        }
    }
    private void saveWechatHistory(Date queryDate) {
        List<TmpWechatBillDataExtDo> tmpWechatBillDataExtDoList = tmpWechatBillDataExtMapper.getWechatBillListByTime(queryDate);
        if (CollectionUtils.isNotEmpty(tmpWechatBillDataExtDoList)) {
            for (TmpWechatBillDataExtDo tmpWechatBillDataExtDo : tmpWechatBillDataExtDoList) {
                List<BankBillExtDo> bankBillExtDoList = convertWechatDo2BankBill(tmpWechatBillDataExtDo);
                if (CollectionUtils.isNotEmpty(bankBillExtDoList)) {
                    for (BankBillExtDo bankBillExtDo : bankBillExtDoList) {
                        BankBillExtDo wechatBankBillExtDoByTranFlow = bankBillExtMapper.getWechatBankBillExtDoByTranFlow(bankBillExtDo);
                        if (null == wechatBankBillExtDoByTranFlow){
                            log.info("历史数据任务-插入微信流水,微信流水信息", bankBillExtDo.getTranFlow());
                        }else{
                            //存在
                            bankBillExtDo.setBankBillId(wechatBankBillExtDoByTranFlow.getBankBillId());
                            log.info("历史数据任务-更新已存在微信流水,微信流水信息", bankBillExtDo.getTranFlow());
                        }
                        List<CapitalBill> capitalBillListByOrderNo = capitalBillService.getCapitalBillListByOrderNo(bankBillExtDo);
                        if((bankBillExtDo.getIsFee()==null||bankBillExtDo.getIsFee()==0)){
                            if (CollUtil.isNotEmpty(capitalBillListByOrderNo)) {
                                for (int i = 0; i < capitalBillListByOrderNo.size(); i++) {
                                    CapitalBill capitalBill = capitalBillListByOrderNo.get(i);
                                    // 记录更新前的信息
                                    log.info("历史数据任务-微信流水处理自动匹配资金流水 CapitalBill: {}", JSON.toJSONString(capitalBill));
                                    CapitalBill update = new CapitalBill();
                                    update.setCapitalBillId(capitalBill.getCapitalBillId());
                                    update.setBankBillId(bankBillExtDo.getBankBillId());
                                    update.setTranFlow(bankBillExtDo.getTranFlow());
                                    // 付款
                                    if (bankBillExtDo.getFlag1().equals(0)) {
                                        update.setPayee(bankBillExtDo.getAccName1());
                                        update.setPayeeBankAccount(bankBillExtDo.getAccno2());
                                        update.setPayeeBankName(bankBillExtDo.getCadbankNm());
                                    }
                                    // 收款
                                    if (bankBillExtDo.getFlag1().equals(1)) {
                                        update.setPayer(bankBillExtDo.getAccName1());
                                        update.setPayerBankAccount(bankBillExtDo.getAccno2());
                                        update.setPayerBankName(bankBillExtDo.getCadbankNm());
                                    }
                                    log.info("更新 CapitalBill: {}", JSON.toJSONString(update));
                                    capitalBillService.updateCapitalBillBankData(update);
                                    // 记录更新后的信息
                                    log.info("历史数据任务-微信自动匹配资金流水CapitalBill 已更新：TranFlow={}, BankBillId={}", bankBillExtDo.getTranFlow(), bankBillExtDo.getBankBillId());
                                }
                            } else {
                                log.info("历史数据任务-未找到 OrderNo 对应的 CapitalBill：{}", bankBillExtDo.getOrderNo());
                            }

                        }else {
                            log.info("手续费用 bankBillId：{}", bankBillExtDo.getBankBillId());
                        }
                    }

                }
            }
        }else {
            log.info("历史数据任务-未查询到数据:{}", queryDate);
        }
    }

    /**
     * 转换流水
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/17 11:26.
     * @author: Randy.Xu.
     * @param tmpAlipayBillDataExtDo
     * @return: com.vedeng.billsync.task.model.entity.BankBillExtDo.
     * @throws:  .
     */
    public BankBillExtDo convertAlipayDo2BankBill(TmpAlipayBillDataExtDo tmpAlipayBillDataExtDo){
        //返回对象
        BankBillExtDo bankBillExtDo = new BankBillExtDo();
        //正流水收款
        if(tmpAlipayBillDataExtDo.getIncome() != null && tmpAlipayBillDataExtDo.getIncome().compareTo(BigDecimal.ZERO) != 0){
            bankBillExtDo.setCompanyId(1);
            bankBillExtDo.setBankTag(4);
            bankBillExtDo.setTranFlow(tmpAlipayBillDataExtDo.getFinancialTurnover());
            bankBillExtDo.setCapitalSearchFlow(tmpAlipayBillDataExtDo.getBusinessTurnover());
            bankBillExtDo.setTrandate(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setTrantime(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandate(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandatetime(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setMessage(tmpAlipayBillDataExtDo.getBusinessType());
            bankBillExtDo.setAmt(tmpAlipayBillDataExtDo.getIncome());
            bankBillExtDo.setFlag1(1);
            bankBillExtDo.setSyncDate(tmpAlipayBillDataExtDo.getSyncTime());
            if("收费".equals(tmpAlipayBillDataExtDo.getBusinessType())){
                bankBillExtDo.setIsFee(1);
                bankBillExtDo.setTranFlow(tmpAlipayBillDataExtDo.getFinancialTurnover());
            }
            if(null != tmpAlipayBillDataExtDo.getOtherAccount()){
                String traderName = tmpAlipayBillDataExtDo.getOtherAccount();
                int index = traderName.lastIndexOf("(");
                if(index !=-1 ){
                    bankBillExtDo.setAccName1(traderName.substring(0,index));
                    String accNo2 = traderName.substring(index+1);
                    if(accNo2.endsWith(")")){
                        bankBillExtDo.setAccno2(accNo2.substring(0,accNo2.length()-1));
                    }else{
                        bankBillExtDo.setAccno2(accNo2);
                    }

                }
            }
            bankBillExtDo.setStatus(0);

            bankBillExtDo.setDet(tmpAlipayBillDataExtDo.getRemark());
            bankBillExtDo.setDet(tmpAlipayBillDataExtDo.getOrderNo());
            //订单号升级--修改原逻辑为根据订单类型判断start
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderNo(tmpAlipayBillDataExtDo.getOrderNo());
            saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
            if(saleorder !=null){
                if(null != tmpAlipayBillDataExtDo.getOrderNo()
                        && (ErpConst.ZERO.equals(saleorder.getOrderType())
                        || ErpConst.FIVE.equals(saleorder.getOrderType())
                        || ErpConst.ONE.equals(saleorder.getOrderType()))){
                    bankBillExtDo.setOrderNo(tmpAlipayBillDataExtDo.getOrderNo());
                    bankBillExtDo.setMatchedAmount(tmpAlipayBillDataExtDo.getIncome());
                }
            }else {
                AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(tmpAlipayBillDataExtDo.getOrderNo());
                if(null != tmpAlipayBillDataExtDo.getOrderNo()
                        && afterSales != null){
                    bankBillExtDo.setOrderNo(tmpAlipayBillDataExtDo.getOrderNo());
                    bankBillExtDo.setMatchedAmount(tmpAlipayBillDataExtDo.getIncome());
                }
            }
            //订单号升级--修改原逻辑为根据订单类型判断end
            return bankBillExtDo;
        }
        //负流水付款
        if(tmpAlipayBillDataExtDo.getOutlay() !=null && tmpAlipayBillDataExtDo.getOutlay().compareTo(BigDecimal.ZERO) !=0){
            bankBillExtDo.setCompanyId(1);
            bankBillExtDo.setBankTag(4);
            bankBillExtDo.setTranFlow(tmpAlipayBillDataExtDo.getFinancialTurnover());
            bankBillExtDo.setCapitalSearchFlow(tmpAlipayBillDataExtDo.getBusinessTurnover());
            bankBillExtDo.setTrandate(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setTrantime(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandate(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandatetime(tmpAlipayBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setMessage(tmpAlipayBillDataExtDo.getBusinessType());
            bankBillExtDo.setAmt(tmpAlipayBillDataExtDo.getOutlay().abs());
            bankBillExtDo.setFlag1(0);
            bankBillExtDo.setSyncDate(tmpAlipayBillDataExtDo.getSyncTime());
            if("收费".equals(tmpAlipayBillDataExtDo.getBusinessType())){
                bankBillExtDo.setIsFee(1);
                bankBillExtDo.setTranFlow(tmpAlipayBillDataExtDo.getFinancialTurnover());
            }
            if(null != tmpAlipayBillDataExtDo.getOtherAccount()){
                String traderName = tmpAlipayBillDataExtDo.getOtherAccount();
                int index = traderName.lastIndexOf("(");
                if(index !=-1 ){
                    bankBillExtDo.setAccName1(traderName.substring(0,index));
                    String accNo2 = traderName.substring(index+1);
                    if(accNo2.endsWith(")")){
                        bankBillExtDo.setAccno2(accNo2.substring(0,accNo2.length()-1));
                    }else{
                        bankBillExtDo.setAccno2(accNo2);
                    }

                }
            }
            bankBillExtDo.setStatus(0);
            bankBillExtDo.setDet(tmpAlipayBillDataExtDo.getRemark());

            //订单号升级--修改原逻辑为根据订单类型判断start
            Saleorder saleorder = new Saleorder();
            saleorder.setSaleorderNo(tmpAlipayBillDataExtDo.getOrderNo());
            saleorder = saleorderMapper.getSaleorderBySaleorderNo(saleorder);
            if(saleorder !=null){
                if(null != tmpAlipayBillDataExtDo.getOrderNo()
                        && (ErpConst.ZERO.equals(saleorder.getOrderType())
                        || ErpConst.FIVE.equals(saleorder.getOrderType())
                        || ErpConst.ONE.equals(saleorder.getOrderType()))){
                    bankBillExtDo.setOrderNo(tmpAlipayBillDataExtDo.getOrderNo());
                    bankBillExtDo.setMatchedAmount(tmpAlipayBillDataExtDo.getOutlay().abs());
                }
            }else {
                AfterSales afterSales = afterSalesMapper.getAfterSalesByNo(tmpAlipayBillDataExtDo.getOrderNo());
                if(null != tmpAlipayBillDataExtDo.getOrderNo()
                        && afterSales != null){
                    bankBillExtDo.setOrderNo(tmpAlipayBillDataExtDo.getOrderNo());
                    bankBillExtDo.setMatchedAmount(tmpAlipayBillDataExtDo.getOutlay().abs());
                }
            }
            //订单号升级--修改原逻辑为根据订单类型判断end
            /**
            if(null != tmpAlipayBillDataExtDo.getOrderNo()
                    && (tmpAlipayBillDataExtDo.getOrderNo().startsWith("VS")
                    || tmpAlipayBillDataExtDo.getOrderNo().startsWith("HC")
                    || tmpAlipayBillDataExtDo.getOrderNo().startsWith("BD")
                    || tmpAlipayBillDataExtDo.getOrderNo().startsWith("SH"))){
                bankBillExtDo.setOrderNo(tmpAlipayBillDataExtDo.getOrderNo());
                bankBillExtDo.setMatchedAmount(tmpAlipayBillDataExtDo.getOutlay().abs());
            }**/
            return  bankBillExtDo;
        }
        return null;
    }

    /**
     * 转换微信流水拆分两条
     * @jira: .
     * @notes: .
     * @version: 1.0.
     * @date: 2020/12/17 14:22.
     * @author: Randy.Xu.
     * @param tmpWechatBillDataExtDo
     * @return: com.vedeng.billsync.task.model.entity.BankBillExtDo.
     * @throws:  .
     */
    public List<BankBillExtDo> convertWechatDo2BankBill(TmpWechatBillDataExtDo tmpWechatBillDataExtDo){
        List<BankBillExtDo> bankBillExtDoList = new ArrayList<>();

        //区别正负流水
        //正流水

        if(StringUtils.isBlank(tmpWechatBillDataExtDo.getRefundType())){
            BankBillExtDo bankBillExtDo = new BankBillExtDo();
            BankBillExtDo handlngBankBillExtDO = new BankBillExtDo();
            //正流水主项
            bankBillExtDo.setCompanyId(1);
            bankBillExtDo.setBankTag(5);
            bankBillExtDo.setTranFlow(tmpWechatBillDataExtDo.getWxOrderNo());
            bankBillExtDo.setTrandate(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setTrantime(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandate(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandatetime(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setAmt(tmpWechatBillDataExtDo.getOrderAmountDue());
            bankBillExtDo.setFlag1(1);
            bankBillExtDo.setStatus(0);
            bankBillExtDo.setMatchedAmount(new BigDecimal(0).setScale(4,RoundingMode.HALF_UP));
            bankBillExtDo.setAccno2(tmpWechatBillDataExtDo.getUserId());
            bankBillExtDo.setOrderNo(tmpWechatBillDataExtDo.getSkuName());
            bankBillExtDo.setMessage(tmpWechatBillDataExtDo.getBusinessType());
            bankBillExtDo.setDet("");
            bankBillExtDo.setMatchedAmount(tmpWechatBillDataExtDo.getOrderAmountDue());
            bankBillExtDo.setSyncDate(tmpWechatBillDataExtDo.getSyncTime());
//            正流水手续费项
            BeanUtils.copyProperties(bankBillExtDo,handlngBankBillExtDO);
            handlngBankBillExtDO.setAmt(tmpWechatBillDataExtDo.getHandlingFee());
            handlngBankBillExtDO.setFlag1(0);
            handlngBankBillExtDO.setTranFlow(bankBillExtDo.getTranFlow()+"_fee");
            handlngBankBillExtDO.setRelatedBill(bankBillExtDo.getTranFlow());
            handlngBankBillExtDO.setIsFee(1);
            handlngBankBillExtDO.setMatchedAmount(tmpWechatBillDataExtDo.getHandlingFee());

            bankBillExtDoList.add(bankBillExtDo);
            bankBillExtDoList.add(handlngBankBillExtDO);

            return  bankBillExtDoList;
        }else{
            //负流水
            BankBillExtDo bankBillExtDo = new BankBillExtDo();
            BankBillExtDo handlngBankBillExtDO = new BankBillExtDo();
            bankBillExtDo.setCompanyId(1);
            bankBillExtDo.setBankTag(5);
            bankBillExtDo.setTranFlow(tmpWechatBillDataExtDo.getWxRefundNo());
            bankBillExtDo.setTrandate(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setTrantime(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandate(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setRealTrandatetime(tmpWechatBillDataExtDo.getOccurrenceTime());
            bankBillExtDo.setAmt(tmpWechatBillDataExtDo.getRefundAmount());
            bankBillExtDo.setFlag1(0);
            bankBillExtDo.setStatus(0);
            bankBillExtDo.setMatchedAmount(new BigDecimal(0).setScale(4,RoundingMode.HALF_UP));
            bankBillExtDo.setAccno2(tmpWechatBillDataExtDo.getUserId());
            bankBillExtDo.setOrderNo(tmpWechatBillDataExtDo.getRefundNo());
            bankBillExtDo.setMessage(tmpWechatBillDataExtDo.getBusinessType());
            bankBillExtDo.setDet("");
            bankBillExtDo.setMatchedAmount(tmpWechatBillDataExtDo.getRefundAmount());
            bankBillExtDo.setSyncDate(tmpWechatBillDataExtDo.getSyncTime());
//            负流水手续费项
            BeanUtils.copyProperties(bankBillExtDo,handlngBankBillExtDO);
            handlngBankBillExtDO.setAmt(tmpWechatBillDataExtDo.getHandlingFee().abs());
            handlngBankBillExtDO.setFlag1(1);
            handlngBankBillExtDO.setTranFlow(bankBillExtDo.getTranFlow()+"_fee");
            handlngBankBillExtDO.setRelatedBill(bankBillExtDo.getTranFlow());
            handlngBankBillExtDO.setIsFee(1);
            handlngBankBillExtDO.setMatchedAmount(tmpWechatBillDataExtDo.getHandlingFee().abs());

            bankBillExtDoList.add(bankBillExtDo);
            bankBillExtDoList.add(handlngBankBillExtDO);

            return bankBillExtDoList;
        }
    }
}
