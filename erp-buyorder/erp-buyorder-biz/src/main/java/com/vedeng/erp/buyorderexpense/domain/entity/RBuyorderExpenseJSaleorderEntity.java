package com.vedeng.erp.buyorderexpense.domain.entity;

import java.util.Date;

import com.vedeng.common.mybatis.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * @description 销售单和采购费用单采购数量关系表（细粒度到商品）
 * <AUTHOR>
 * @date 2023/1/5 13:46
 **/

@Getter
@Setter
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RBuyorderExpenseJSaleorderEntity extends BaseEntity {
    /**
    * 主键
    */
    private Integer tRBuyorderExpenseJSaleorderId;


    /**
    * 销售单表id
    */
    private Integer saleorderId;

    /**
    * 销售单明细表id
    */
    private Integer saleorderGoodsId;

    /**
    * 采购费用单id
    */
    private Integer buyorderExpenseId;

    /**
    * 采购费用单明细id
    */
    private Integer buyorderExpenseItemId;

    /**
    * skuId
    */
    private Integer skuId;

    /**
    * sku
    */
    private String skuNo;

    /**
    * 关联采购数量
    */
    private Integer num;
}