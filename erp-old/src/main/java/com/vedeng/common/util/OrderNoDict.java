package com.vedeng.common.util;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.dao.AfterSalesDetailMapper;
import com.vedeng.aftersales.dao.AfterSalesMapper;
import com.vedeng.aftersales.model.AfterSales;
import com.vedeng.aftersales.model.AfterSalesDetail;
import com.vedeng.bean.web.response.RestfulResult;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.SysOptionConstant;
import com.vedeng.common.http.NewHttpClientUtils;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.buyorder.service.GeBusinessChanceInfoService;
import com.vedeng.common.redis.utils.RedisUtil;
import com.vedeng.order.api.aftersales.dto.AfterSalesNoGenerateReqDto;
import com.vedeng.order.api.buyoroder.dto.BuyOrderNoGenerateReqDto;
import com.vedeng.order.api.constant.*;
import com.vedeng.order.api.fallback.OrderFallbackService;
import com.vedeng.order.api.order.dto.SaleOrderNoGenerateReqDto;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Buyorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.orderstream.aftersales.constant.AfterSalesProcessEnum;
import com.vedeng.trader.dao.TraderCustomerMapper;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderSupplier;
import com.vedeng.trader.model.vo.TraderCustomerVo;
import com.vedeng.aftersales.component.AfterSalesTypeEnum;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 * <b>Description:</b><br>
 * 订单单号生成
 * 
 * <AUTHOR>
 * @Note <b>ProjectName:</b> dbcenter <br>
 *       <b>PackageName:</b> com.vedeng.common.tools <br>
 *       <b>ClassName:</b> OrderNoDict <br>
 *       <b>Date:</b> 2017年6月23日 上午9:44:55
 */
@Service
@Slf4j
public class OrderNoDict {
	public static Logger logger = LoggerFactory.getLogger(OrderNoDict.class);
	public static String prefixNum;

	public static String dictMapFile;

	public static String dictMap;

	public static Integer saltNum = 1487245;

	public static Integer[] dictMapList = new Integer[] { 0, 7, 1, 6, 9, 3, 4, 5, 2, 8 };

	public static Integer ADC_ORDER_TYPE = 16;
	
	public static Integer LEND_OUT_TYPE =17;

	public static Integer EL_OUT_TYPE =18;

	public static Integer LEND_OUT_NEW_TYPE =21;

	public static Integer SCRAP_OUT_TYPE =20;

	public static Integer SURPLUSIN_ORDER =22;

	public static Integer SAMPLE_OUT_NEW_TYPE =28;

	private static String zero =  "0000000";

	public OrderNoDict() {

	}
	@Autowired
	private SaleorderMapper saleorderMapper;
	@Autowired
	private TraderCustomerMapper traderCustomerMapper;
	@Autowired
	private BuyorderMapper buyorderMapper;
	@Autowired
	private OrderFallbackService orderFallbackService;
	@Autowired
	private TraderSupplierMapper traderSupplierMapper;
	@Autowired
	private AfterSalesMapper afterSalesMapper;
	@Autowired
	private AfterSalesDetailMapper afterSalesDetailMapper;
	@Autowired
	private GeBusinessChanceInfoService geBusinessChanceInfoService;
	@Value("${order_url}")
	private String orderUrl;
	/**
	 * <b>Description:</b><br>
	 * 生成订单编码
	 * 
	 * @param originalNum
	 *            主键ID
	 * @param type
	 *            类型 1:询价 2：报价 3：销售订单 4：备货订单 5：采购订单 6：售后订单 7：订单修改申请 8：工单 9:订货订单
	 *            10:财务流水 11文件寄送12销售订单修改申请 13随机数14备货采购单 15采购订单修改申请 16 ADK  17外借单
	 *            23:JCO 24:JCF 25:ZXF 27:ge授权书
	 * @return
	 * @Note <b>Author:</b> Jerry <br>
	 *       <b>Date:</b> 2017年6月23日 上午10:10:27
	 */
	public String getOrderNum(Integer originalNum, Integer type) {
		StringBuffer orderApiUrl = new StringBuffer(orderUrl);
		RestfulResult resultInfo = new RestfulResult();
		switch (type){
			//原VS订单
			case 3:
			//原jco
			case 23:
			//原JCF
			case 24:
			//原ZXF
			case 25:{
				//请求获取订单号的接口
				orderApiUrl.append("/order/saleOrder/orderNo");
				SaleOrderNoGenerateReqDto saleOrderNoGenerateReqDto = new SaleOrderNoGenerateReqDto();
				Saleorder saleorder = saleorderMapper.selectByPrimaryKey(originalNum);
				saleOrderNoGenerateReqDto.setOrderChannelCode(OrderChannelEnum.OFFLINE.getCode());
				saleOrderNoGenerateReqDto.setTraderId(saleorder.getTraderId());
				TraderCustomerVo traderCustomer = traderCustomerMapper.getCustomerInfo(saleorder.getTraderId());
				if(SysOptionConstant.ID_2.equals(traderCustomer.getBelongPlatform()) || SysOptionConstant.ID_6.equals(traderCustomer.getBelongPlatform())) {
					//医械购
					saleOrderNoGenerateReqDto.setProductionLineCode(ProductionLineEnum.YI_XIE_GOU.getCode());
				}else if(SysOptionConstant.ID_3.equals(traderCustomer.getBelongPlatform())){
					//科研购
					saleOrderNoGenerateReqDto.setProductionLineCode(ProductionLineEnum.KE_YAN_GOU.getCode());
				}else if(SysOptionConstant.ID_5.equals(traderCustomer.getBelongPlatform())){
					//其他
					saleOrderNoGenerateReqDto.setProductionLineCode(ProductionLineEnum.OTHER.getCode());
				}else {
					saleOrderNoGenerateReqDto.setProductionLineCode(ProductionLineEnum.B2B.getCode());
				}
				if(SysOptionConstant.ID_6.equals(traderCustomer.getBelongPlatform())) {
					//集采
					saleOrderNoGenerateReqDto.setTraderTypeCode(TraderTypeEnum.ORGANIZATION.getCode());
				}else {
					if(SysOptionConstant.ID_465.equals(traderCustomer.getCustomerNature())){
						//分销
						saleOrderNoGenerateReqDto.setTraderTypeCode(TraderTypeEnum.DISTRIBUTION.getCode());
					}else if(SysOptionConstant.ID_466.equals(traderCustomer.getCustomerNature())){
						//终端
						saleOrderNoGenerateReqDto.setTraderTypeCode(TraderTypeEnum.TERMINAL.getCode());
					}
				}
				resultInfo = (RestfulResult) JSONObject.toBean(NewHttpClientUtils.httpPost(orderApiUrl.toString(), JSON.toJSONString(saleOrderNoGenerateReqDto)),RestfulResult.class);
				logger.info("调用order服务返回信息{},请求参数{},",JSON.toJSONString(resultInfo),JSON.toJSONString(saleOrderNoGenerateReqDto));
				if(resultInfo == null || "fail".equals(resultInfo.getCode()) || "FAIL".equals(resultInfo.getCode())){
					//如果调用远程失败，本地调用service方法创建单号
					return orderFallbackService.saleOrderNoGenerate(saleOrderNoGenerateReqDto);
				}else {
					return resultInfo.getData().toString();
				}
			}
			case 5:
			case 14:{
				//原采购订单
				//请求获取采购单号的接口
				orderApiUrl.append("/order/buyOrder/buyOrderNo");
				Buyorder buyorder = buyorderMapper.selectByPrimaryKey(originalNum);
				if(buyorder.getTraderId() == null || ErpConst.ZERO.equals(buyorder.getTraderId())){
					//如果没有设置供应商--走历史订单创建模块-创建临时订单号
					return makeOrderNum(originalNum, type);
				}else {
					//存在供应商的情况，且采购单号为旧单号时，--调用新订单号规则，创建采购单号
					if(buyorder.getBuyorderNo().startsWith("VP") || buyorder.getBuyorderNo().startsWith("VB")){
						BuyOrderNoGenerateReqDto buyOrderNoGenerateReqDto = new BuyOrderNoGenerateReqDto();
						if(ErpConst.ONE.equals(buyorder.getOrderType())){
							//备货订单
							buyOrderNoGenerateReqDto.setBuyOrderTypeCode(BuyOrderTypeEnum.STOCK_UP.getCode());
						}else if (ErpConst.ZERO.equals(buyorder.getOrderType())){
							//销售单采购
							buyOrderNoGenerateReqDto.setBuyOrderTypeCode(BuyOrderTypeEnum.UN_STOCK_UP.getCode());
						}
						TraderSupplier traderSupplier = traderSupplierMapper.getSuplierInfoByTraderId(buyorder.getTraderId());
						if(ErpConst.ONE.equals(traderSupplier.getTraderType())){
							//生产厂家
							buyOrderNoGenerateReqDto.setProviderTypeCode(ProviderTypeEnum.PRODUCER.getCode());
						}else if(ErpConst.TWO.equals(traderSupplier.getTraderType())){
							buyOrderNoGenerateReqDto.setProviderTypeCode(ProviderTypeEnum.DEALER.getCode());
						}
						buyOrderNoGenerateReqDto.setProviderId(buyorder.getTraderId());
						resultInfo = (RestfulResult) JSONObject.toBean(NewHttpClientUtils.httpPost(orderApiUrl.toString(), JSON.toJSONString(buyOrderNoGenerateReqDto)),RestfulResult.class);
						logger.info("调用order服务返回信息{},请求参数{},",JSON.toJSONString(resultInfo),JSON.toJSONString(buyOrderNoGenerateReqDto));
						if(resultInfo == null || "fail".equals(resultInfo.getCode()) || "FAIL".equals(resultInfo.getCode())){
							//如果调用远程失败，本地调用service方法创建单号
							return orderFallbackService.buyOrderNoGenerate(buyOrderNoGenerateReqDto);
						}else {
							return resultInfo.getData().toString();
						}
					}
					else {
						return buyorder.getBuyorderNo();
					}
				}
			}
			case 6:{
				//原售后订单
				orderApiUrl.append("/order/aftersales/afterSalesNo");
				AfterSales afterSales = afterSalesMapper.selectByPrimaryKey(originalNum);
				AfterSalesNoGenerateReqDto afterSalesNoGenerateReqDto = new AfterSalesNoGenerateReqDto();
				//默认传1
				afterSalesNoGenerateReqDto.setAfterSalesTypeCode(ErpConst.ONE);
				if(SysOptionConstant.ID_535.equals(afterSales.getSubjectType()) || SysOptionConstant.ID_537.equals(afterSales.getSubjectType())){
					//销售单售后
					afterSalesNoGenerateReqDto.setAfterSalesProductionLineCode(AfterSalesProductionLineEnum.SALE_ORDER.getCode());
					Saleorder saleorder = saleorderMapper.selectByPrimaryKey(afterSales.getOrderId());
					if(saleorder == null){
						AfterSalesDetail afterSalesDetail = afterSalesDetailMapper.selectadtbyid(afterSales);
						afterSalesNoGenerateReqDto.setTraderId(afterSalesDetail.getTraderId());
					}else {
						afterSalesNoGenerateReqDto.setTraderId(saleorder.getTraderId());
					}
					switch (AfterSalesProcessEnum.getInstance(afterSales.getType())){
						case AFTERSALES_TH: {
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_TH.getCode());
							break;
						}
						case AFTERSALES_HH:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_HH.getCode());
							break;
						}
						case AFTERSALES_AT: case AFTERSALES_ATY: case AFTERSALES_ATN:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_AT.getCode());
							break;
						}
						case AFTERSALES_TP:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_TP.getCode());
							break;
						}
						case AFTERSALES_JZ:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_JZ.getCode());
							break;
						}
						case OTHER:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.OTHER.getCode());
							break;
						}
						case AFTERSALES_WX:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_WX.getCode());
							break;
						}
						case LOST_TICKET:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.LOST_TICKET.getCode());
							break;
						}
						case NONPERFORMENCE_PAYMENT:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.COMPENSATE.getCode());
							break;
						}
						case DELIVERY_COMPLAINT:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.DELIVERY_COMPLAINT.getCode());
							break;
						}
						case INSTALL_COMPLAINT:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.INSTALL_COMPLAINT.getCode());
							break;
						}
						case MAINTAIN_COMPLAINT:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.REPAIR_COMPLAINT.getCode());
							break;
						}
						case AFTERASALES_THIRD_AT:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.THIRD_AT.getCode());
							break;
						}
						case AFTERASALES_THIRD_WX:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.THIRD_WX.getCode());
							break;
						}
						case THIRD_CONSULTATION:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.THIRD_JZ.getCode());
							break;
						}
						case AFTERSALES_TK:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_TK.getCode());
							break;
						}
						case THIRD_OTHER:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.THIRD_OTHER.getCode());
							break;
						}
						case THIRD_AMOUNT_RETURN:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.THIRD_TK.getCode());
							break;
						}
						default:{
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(ErpConst.ZERO);
							break;
						}
					}
				}else if(SysOptionConstant.ID_536.equals(afterSales.getSubjectType())){
					//采购售后
					afterSalesNoGenerateReqDto.setAfterSalesProductionLineCode(AfterSalesProductionLineEnum.BUY_ORDER.getCode());
					Buyorder buyorder = buyorderMapper.selectByPrimaryKey(afterSales.getOrderId());
					afterSalesNoGenerateReqDto.setTraderId(buyorder.getTraderId());
					switch (afterSales.getType()){
						case 546:{
							//采购售后退货
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_TH.getCode());
							break;
						}
						case 547:{
							//采购售后换货
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_HH.getCode());
							break;
						}
						case 548:{
							//采购售后换货
							afterSalesNoGenerateReqDto.setAfterSalesBusinessTypeCode(AfterSalesBusinessTypeEnum.AFTER_SALES_TP.getCode());
						}
						default:{
							break;
						}
					}
				}
				resultInfo = (RestfulResult) JSONObject.toBean(NewHttpClientUtils.httpPost(orderApiUrl.toString(), JSON.toJSONString(afterSalesNoGenerateReqDto)),RestfulResult.class);
				logger.info("调用order服务返回信息{},请求参数{},",JSON.toJSONString(resultInfo),JSON.toJSONString(afterSalesNoGenerateReqDto));
				if(resultInfo == null || "fail".equals(resultInfo.getCode()) || "FAIL".equals(resultInfo.getCode())){
					//如果调用远程失败，本地调用service方法创建单号
					return orderFallbackService.afterSalesNoGenerate(afterSalesNoGenerateReqDto);
				}else {
					return resultInfo.getData().toString();
				}
			}
			// ge 授权书编号
			case 27:{
				return getGeAuthorizationNoByRedis("myGeAuthorizationNoByRedis", "GESQ", 3);
			}
			default:{
				return makeOrderNum(originalNum, type);
			}
		}
	}

	protected String makeOrderNum(Integer originalNum, Integer type) {
		originalNum = originalNum * 3 + saltNum;

		Date d = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat("yy");
		String dateNowStr = sdf.format(d);
		String distNum = String.valueOf(dateNowStr);

		SimpleDateFormat ymd = new SimpleDateFormat("yyMMdd");
		String yearMonthDay = ymd.format(d);

		String s = String.valueOf(originalNum);
		String[] ss = s.split("");

		for (String v : ss) {
			distNum += dictMapList[Integer.parseInt(v)];
		}
		if (type != null) {
			switch (type) {
			case 1:
				break;
			case 2:
				distNum = "VD" + distNum;
				break;
			case 3:
				distNum = "VS" + distNum;
				break;
			case 4:
				distNum = "BH" + distNum;
				break;
			case 5:
				distNum = "VP" + distNum;
				break;
			case 6:
				distNum = "SH" + distNum;
				break;
			case 7:
				distNum = "MD" + distNum;
				break;
			case 8:
				distNum = "WR" + distNum;
				break;
			case 9:
				distNum = "DH" + distNum;
				break;
			case 10:// 年+月+日+时+分+秒+8位加密数字
				SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");// 设置日期格式
				String dateStr = df.format(new Date());
				distNum = dateStr + distNum;
				break;
			case 11:// 文件寄送
				distNum = "WJ" + distNum;
				break;
			case 12:// 销售订单修改
				distNum = "XG" + distNum;
				break;
			case 13:// 随机数
				break;
			case 14:
				distNum = "VB" + distNum;
				break;
			case 15:
				distNum = "XV" + distNum;
				break;
			case 16:
				distNum = "ADK" + distNum;
				break;
			case 17:
				distNum = "JY" + distNum;
				break;
			case 18:
				distNum = "EL" + distNum;
				break;
			case 19:
				distNum = "ZY" + distNum;
				break;
			case 20:
				distNum = "BF" + distNum;
				break;
			case 21:
				distNum = "WJD" + distNum;
				break;
			case 22:
				distNum = "PY" + distNum;
				break;
			case 23:
				distNum = "JCO" +distNum;
				break;
			case 24:
				distNum = "JCF" + distNum;
				break;
			case 25:
				distNum = "ZXF" + distNum;
				break;
			case 26:
				//GE商机号生成
				//三位随机数
				int random=(int)(Math.random()*900)+100;
				distNum = "GE" + yearMonthDay + String.valueOf(random);
				//GE商机号校验重复
				if(geBusinessChanceInfoService.geBusinessChanceNoIsExist(distNum)){
					//商机号存在，重新生成
					makeOrderNum(0,26);
				}
				break;
			case 28:
				distNum = "YP" + yearMonthDay;
			default:
				break;
			}

		}
		return distNum;
	}

	/**
	 * 根据redis获取当日自增的编号
	 * prefx+yyMMdd+num 例子：GESQ220215001
	 * num的位数由length限制，redis中的值未到0补全
	 * @param key redis中的key
	 * @param prefix 编号前缀 如 GESQ
	 * @param length 自增的数字的num
	 * @return String
	 */
	private String getGeAuthorizationNoByRedis(String key, String prefix, Integer length) {

		// 时间
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date());
		cal.set(Calendar.HOUR_OF_DAY, 23);
		cal.set(Calendar.MINUTE, 59);
		cal.set(Calendar.SECOND, 59);
		// 自增
		long l = RedisUtil.StringOps.incrBy(key, 1);
		if (l == 0) {
			l = RedisUtil.StringOps.incrBy(key, 1);
		}
		// 当日
		int expireTime = (int) ((cal.getTimeInMillis() - System.currentTimeMillis()) / 1000);
		RedisUtil.KeyOps.expire(key, expireTime, TimeUnit.SECONDS);
		StringBuilder suffix = new StringBuilder();
		StringBuilder append = suffix.append(zero).append(l);
		// 规则 GESQ 220215001
		String str = append.substring(append.length() - length, append.length());
		SimpleDateFormat formatter = new SimpleDateFormat("yyMMdd");
		String timeStr = formatter.format(cal.getTimeInMillis());
		String authorizationNo = prefix + timeStr + str;
		log.info("组装后的编号:{}",authorizationNo);
		return authorizationNo;
	}

	public static void main(String[] args) {
//		System.out.println(OrderNoDict.getOrderNum(2, 1));
//		System.out.println(OrderNoDict.getOrderNum(3, 2));
//		System.out.println(OrderNoDict.getOrderNum(22167, 3));
//		System.out.println(OrderNoDict.getOrderNum(5, 4));
//		System.out.println(OrderNoDict.getOrderNum(6, 5));
//		System.out.println(OrderNoDict.getOrderNum(7, 6));
//		System.out.println(OrderNoDict.getOrderNum(8, 7));
//		System.out.println(OrderNoDict.getOrderNum(9, 8));
//		System.out.println(OrderNoDict.getOrderNum(10, 9));
//		System.out.println(OrderNoDict.getOrderNum(10, 10));
//		System.out.println(OrderNoDict.getOrderNum(10, 21));
//		System.out.println(OrderNoDict.getOrderNum(50745, 1));
//		System.out.println(OrderNoDict.getOrderNum(112254, 18));
//
//		String orderNum = OrderNoDict.getOrderNum(2673509, 10);
//		System.out.println(OrderNoDict.getOrderNum(2673509, 10));


	}

}
