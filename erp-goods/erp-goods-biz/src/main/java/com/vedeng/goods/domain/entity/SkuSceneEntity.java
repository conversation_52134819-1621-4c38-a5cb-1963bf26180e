package com.vedeng.goods.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 场景方案
 */
@Getter
@Setter
public class SkuSceneEntity extends BaseEntity {
    /**
     * 主键
     */
    private Long id;

    /**
     * 场景编号（唯一）
     */
    private String sceneNo;

    /**
     * 场景名称
     */
    private String name;

    /**
     * 场景描述
     */
    private String description;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 状态（0=下架，1=上架）
     */
    private Integer status;

    /**
     * 是否删除
     */
    private Integer isDelete;
}