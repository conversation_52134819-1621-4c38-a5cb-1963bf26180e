package com.vedeng.erp.kingdee.batch.job;

import cn.hutool.core.date.DateUtil;
import com.vedeng.erp.kingdee.batch.common.base.BaseJob;
import com.vedeng.erp.kingdee.batch.common.handle.CustomSkipPolicy;
import com.vedeng.erp.kingdee.batch.common.reader.CommonMybatisItemReader;
import com.vedeng.erp.kingdee.batch.dto.BatchInvoiceDto;
import com.vedeng.erp.kingdee.batch.dto.BatchReceiveFeeDto;
import com.vedeng.erp.kingdee.batch.processor.*;
import com.vedeng.erp.kingdee.batch.tasklet.PushInvoiceVoucherTasklet;
import com.vedeng.erp.kingdee.batch.writer.AfterSaleOutPutFeeInvoiceWriter;
import com.vedeng.erp.kingdee.batch.writer.AfterSaleReceiveFeeWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleOrderAfterSaleExpenseInvoiceWriter;
import com.vedeng.erp.kingdee.batch.writer.BatchSaleOrderAfterSaleExpenseReceivableWriter;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import com.vedeng.erp.kingdee.dto.KingDeeRedInvoiceDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.Step;
import org.springframework.batch.core.configuration.annotation.StepScope;
import org.springframework.batch.core.launch.support.RunIdIncrementer;
import org.springframework.batch.item.support.CompositeItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @create 2023−01-06 下午4:11
 * @description 销售单售后 逆向流程推送费用相关
 * 1.售后手续费
 * 2.退货退票
 * 3.仅退票
 */
@Slf4j
@Configuration
@SuppressWarnings("all")
public class SaleOrderExpenseAfterSaleBatchJob extends BaseJob {
    @Autowired
    private BatchSaleOrderAfterSaleExpenseReceivableProcessor batchSaleOrderAfterSaleExpenseReceivableProcessor;
    @Autowired
    private BatchSaleOrderAfterSaleExpenseReceivableWriter batchSaleOrderAfterSaleExpenseReceivableWriter;
    @Autowired
    private BatchSaleOrderAfterSaleExpenseInvoiceProcessor batchSaleOrderAfterSaleExpenseInvoiceProcessor;
    @Autowired
    private BatchSaleOrderAfterSaleExpenseInvoiceWriter batchSaleOrderAfterSaleExpenseInvoiceWriter;
    @Autowired
    private PushInvoiceVoucherTasklet pushInvoiceVoucherTasklet;
    @Autowired
    private AfterSaleReceiveFeeProcessor afterSaleReceiveFeeProcessor;
    @Autowired
    private AfterSaleReceiveFeeWriter afterSaleReceiveFeeWriter;
    @Autowired
    private AfterSaleOutPutFeeInvoiceProcessor afterSaleOutPutFeeInvoiceProcessor;
    @Autowired
    private AfterSaleOutPutFeeInvoiceWriter afterSaleOutPutFeeInvoiceWriter;
    @Autowired
    private BatchHistorySaleRedInvoiceCompositeProcessor batchHistorySaleBlueInvoiceCompositeProcessor;

    public Job saleOrderExpenseAfterSaleJob() {
        return jobBuilderFactory.get("saleOrderExpenseAfterSaleJob")
                .incrementer(new RunIdIncrementer())
                .listener(jobListener)
                .start(afterSaleReceiveFee())
                .next(afterSaleOutPutFeeInvoice())
                .next(afterSaleExpenseReceiveCommon())
                .next(afterSaleExpenseInvoice())
                .next(saveInvoiceVoucher())
                .build();
    }

    /**
     * 售后手续费和三方费用推送费用应收单
     */
    private Step afterSaleReceiveFee() {
        return stepBuilderFactory.get("销售售后手续费和三方费用应收单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchReceiveFeeDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchBatchBackBlueInvoiceDtoItemReader(null, null))
                .processor(afterSaleReceiveFeeProcessor)
                .writer(afterSaleReceiveFeeWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 售后手续费和三方费用推送费用蓝票
     */
    private Step afterSaleOutPutFeeInvoice() {
        return stepBuilderFactory.get("销售售后手续费和三方费用蓝票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, BatchReceiveFeeDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchOutPutFeeInvoiceReader(null, null))
                .processor(afterSaleOutPutFeeInvoiceProcessor)
                .writer(afterSaleOutPutFeeInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }


    /**
     * 销售售后 产生的费用应收单
     */
    private Step afterSaleExpenseReceiveCommon() {
        return stepBuilderFactory.get("销售费用售后应收单")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, KingDeeReceiveFeeDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleBackRedInvoiceDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryAfterSaleExpenseReceiveCommonProcessor())
                .writer(batchSaleOrderAfterSaleExpenseReceivableWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 销售售后 产生的费用红票
     */
    private Step afterSaleExpenseInvoice() {
        return stepBuilderFactory.get("销售费用售后红票")
                // <输入对象, 输出对象> chunk通俗的讲类似于SQL的commit; 这里表示处理(processor)100条后写入(writer)一次
                .<BatchInvoiceDto, KingDeeRedInvoiceDto>chunk(1)
                .faultTolerant()
                .retryLimit(1)
                .retry(Exception.class)
                .skipPolicy(new CustomSkipPolicy())
                .reader(batchAfterSaleBackRedInvoiceDtoItemReader(null, null))
                .processor(compositeItemExcelHistoryAfterSaleExpenseInvoiceProcessor())
                .writer(batchSaleOrderAfterSaleExpenseInvoiceWriter)
                .listener(baseReadListener)
                .listener(baseProcessListener)
                .listener(baseWriteListener)
                .build();
    }

    /**
     * 21-22历史红票Excel处理及生成负向费用应收单
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveFeeDto> compositeItemExcelHistoryAfterSaleExpenseReceiveCommonProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeReceiveFeeDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleBlueInvoiceCompositeProcessor, batchSaleOrderAfterSaleExpenseReceivableProcessor));
        return compositeItemProcessor;
    }

    /**
     * 21-22历史红票Excel处理及生成费用红票
     *
     * @return
     */
    @Bean
    public CompositeItemProcessor<BatchInvoiceDto, KingDeeRedInvoiceDto> compositeItemExcelHistoryAfterSaleExpenseInvoiceProcessor() {
        CompositeItemProcessor<BatchInvoiceDto, KingDeeRedInvoiceDto> compositeItemProcessor = new CompositeItemProcessor<>();
        compositeItemProcessor.setDelegates(Arrays.asList(batchHistorySaleBlueInvoiceCompositeProcessor, batchSaleOrderAfterSaleExpenseInvoiceProcessor));
        return compositeItemProcessor;
    }

    /**
     * 发票存入金蝶凭证信息表
     */
    private Step saveInvoiceVoucher() {
        return stepBuilderFactory.get("销售费用红票存入金蝶凭证信息表")
                .tasklet(pushInvoiceVoucherTasklet)
                .build();
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchBatchBackBlueInvoiceDtoItemReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                           @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                //开票
                .tag(1)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(endTime).getTime())
                //订单类型为'销售售后' SUBJECT_TYPE = 535
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "saleOrderAfterSaleReceiveFeeInvoice", batchInvoiceDto);
    }

    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchOutPutFeeInvoiceReader(@Value("#{jobParameters['beginTime']}") String beginTime,
                                                                                @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 蓝字有效
                .colorType(2)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                //开票
                .tag(1)
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() :
                        DateUtil.parseDateTime(endTime).getTime())
                //订单类型为'销售售后' SUBJECT_TYPE = 535
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "saleOrderAfterSaleOutPutFeeInvoice", batchInvoiceDto);
    }


    /**
     * 销售退票
     */
    @Bean
    @StepScope
    public CommonMybatisItemReader<BatchInvoiceDto> batchAfterSaleBackRedInvoiceDtoItemReader(
            @Value("#{jobParameters['beginTime']}") String beginTime,
            @Value("#{jobParameters['endTime']}") String endTime) {
        BatchInvoiceDto batchInvoiceDto = BatchInvoiceDto
                .builder()
                // 红字有效
                .colorType(1)
                .isEnable(1)
                // 审核通过
                .validStatus(1)
                .companyId(1)
                // 销售开票
                .type(505)
                // 通过时间是当天的
                .beginTime(beginTime == null ? DateUtil.beginOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(beginTime).getTime())
                .endTime(endTime == null ? DateUtil.endOfDay(DateUtil.yesterday()).getTime() : DateUtil.parseDateTime(endTime).getTime())
                .build();
        return new CommonMybatisItemReader<>(sqlSessionFactory, BatchInvoiceDto.class.getSimpleName(), "saleorderRedInvoiceFindByAll", batchInvoiceDto);
    }

}
