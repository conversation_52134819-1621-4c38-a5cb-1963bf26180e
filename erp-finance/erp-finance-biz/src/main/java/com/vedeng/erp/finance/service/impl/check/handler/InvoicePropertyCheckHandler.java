package com.vedeng.erp.finance.service.impl.check.handler;

import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.erp.finance.service.AbstractCheckHandler;
import com.vedeng.erp.finance.dto.InvoiceCheckRequestDto;
import com.vedeng.erp.finance.dto.InvoiceCheckResultDto;
import com.vedeng.erp.finance.enums.CheckHandlerEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 发票形式校验
 */
@Service
@Slf4j
public class InvoicePropertyCheckHandler extends AbstractCheckHandler {
    @Override
    public void handleSalesCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("发票形式校验-销售,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        invoicePropertyCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
    }

    @Override
    public void handleAfterCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        log.info("发票形式校验-售后,invoiceCheckRequestDto:{}", invoiceCheckRequestDto);
        invoicePropertyCheck(invoiceCheckRequestDto, invoiceCheckResultDto);
    }

    private void invoicePropertyCheck(InvoiceCheckRequestDto invoiceCheckRequestDto, InvoiceCheckResultDto invoiceCheckResultDto) {
        Integer invoiceProperty = invoiceCheckRequestDto.getInvoiceProperty();
        if (!ErpConstant.THREE.equals(invoiceProperty)) {
            CheckHandlerEnum checkHandlerEnum = getCheckHandlerEnum();
            buildResult(invoiceCheckResultDto, checkHandlerEnum.getPromptText());
        }
    }
}
