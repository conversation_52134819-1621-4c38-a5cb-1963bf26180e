package com.vedeng.finance.dao;

import com.vedeng.finance.dto.HxInvoiceSearchDTO;
import com.vedeng.finance.model.*;
import com.vedeng.finance.model.po.HxInvoiceJBuyorderGoodsPO;
import com.vedeng.finance.vo.HxInvoiceDetailVo;
import com.vedeng.finance.vo.HxInvoiceVo;
import com.vedeng.finance.vo.InvoiceEntryStashVo;
import com.vedeng.order.model.BuyorderGoods;
import com.vedeng.order.model.dto.BuyorderGoodsRecordDTO;
import com.vedeng.order.model.vo.BuyorderVo;
import org.apache.ibatis.annotations.Param;

import javax.inject.Named;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date created in 2020/5/20 13:23
 */
@Named("hxInvoiceMapper")
public interface HxInvoiceMapper {

    int deleteByPrimaryKey(Integer hxInvoiceId);

    int insert(HxInvoice record);

    int insertSelective(HxInvoice record);

    HxInvoice selectByPrimaryKey(Integer hxInvoiceId);

    int updateByPrimaryKeySelective(HxInvoice record);

    int updateByPrimaryKey(HxInvoice record);

    List<HxInvoice> getHxInvoiceByCodeAndNumAndColorType(@Param("invoiceCode") String invoiceCode, @Param("invoiceNum") String invoiceNum, @Param("colorType") Integer colorType);

    List<HxInvoice> getHxInvoiceByCodeAndNum(@Param("invoiceCode") String invoiceCode, @Param("invoiceNum") String invoiceNum);

    /**
     * 获取所有的航信推送的进项发票
     *
     * @param map 参数
     * @return 结果
     */
    List<HxInvoiceVo> getHxInvoiceAllListPage(Map<String, Object> map);

    /**
     * 获取待认领的航信推送的进项发票
     *
     * @return 结果
     */
    List<HxInvoiceVo> getHxInvoiceWaitListPage(Map<String, Object> map);


    /**
     * 获取航信推送的费用票
     *
     * @param map 参数
     * @return 结果
     */
    List<HxInvoiceVo> getHxInvoiceCostListPage(Map<String, Object> map);


    /**
     * 获取航信推送的异常发票
     *
     * @param map 参数
     * @return 结果
     */
    List<HxInvoiceVo> getHxInvoiceExceptionListPage(Map<String, Object> map);

    /**
     * 获取航信推送的负数票
     *
     * @param map 参数
     * @return 结果
     */
    List<HxInvoiceVo> getHxInvoiceNegativeListPage(Map<String, Object> map);

    /**
     * 条件获取待录票列表
     *
     * @param map 参数
     * @return 结果
     * <AUTHOR>
     * @date 2020/5/23
     */
    List<HxInvoiceVo> getSupplyHxInvoiceWaitListPage(Map<String, Object> map);

    /**
     * 条件获取待录票列表(接入老系统录票重新计算)
     *
     * @param map 参数
     * @return 结果
     * <AUTHOR>
     * @date 2020/11/24
     */
    List<HxInvoiceVo> getSupplyHxInvoiceWaitOldListPage(Map<String, Object> map);

    /**
     * 条件获取待退票列表
     *
     * @param map 参数
     * @return 结果
     * * <AUTHOR>
     * * @date 2020/5/23
     */
    List<HxInvoiceVo> getSupplyHxInvoiceWaitRetuenListPage(Map<String, Object> map);

    /**
     * 条件获取审核中列表
     *
     * @param map 参数
     * @return 结果
     * * <AUTHOR>
     * * @date 2020/5/25
     */
    List<HxInvoiceVo> getSupplyHxInvoiceVerifyingListPage(Map<String, Object> map);

    /**
     * 条件获取已审核列表
     *
     * @param map 参数
     * @return 结果
     * * <AUTHOR>
     * * @date 2020/5/25
     */
    List<HxInvoiceVo> getSupplyHxInvoiceVerifiedListPage(Map<String, Object> map);

    /**
     * 保存更新航信的发票状态信息
     *
     * @param hxInvoiceId
     * @param invoiceStatus
     * @return
     * @Author:hugo
     * @date:2020/5/26
     */
    Integer saveHxInvoiceStatus(@Param("hxInvoiceId") Integer hxInvoiceId, @Param("invoiceStatus") Integer invoiceStatus);

    /**
     * @param map 参数
     * @return
     * @describe 获取航信进项票相关信息
     * <AUTHOR>
     * @date 2020 5/27
     */
    HxInvoiceVo getHxInvoiceInfoById(Map<String, Object> map);

    /**
     * @param hxInvoiceId 参数
     * @return
     * @describe 获取航信进项票相关信息
     * <AUTHOR>
     * @date 2020 5/27
     */
    HxInvoiceVo getHxInvoiceInfoByIdNew(Integer hxInvoiceId);

    /**
     * @param map 参数
     * @return
     * @describe 获取航信进项票相关商品信息
     * <AUTHOR>
     * @date 2020/5/27
     */
    List<HxInvoiceDetailVo> getHxInvoiceDetailsByHxInvoiceId(Map<String, Object> map);

    /**
     * @param map 参数
     * @return
     * @describe 获取已经录票得商品相关信息
     * <AUTHOR>
     * @date 2020/5/27 14:08:36
     */
    List<InvoiceEntryStashVo> getInvoiceEntryStashsByDetailId(Map<String, Object> map);

    /**
     * @param invoiceEntryStash 参数
     * @return
     * @describe 添加录票记录
     * <AUTHOR>
     * @date 2020/5/27 17:14:28
     */
    Integer insertInvoiceEntryStash(InvoiceEntryStash invoiceEntryStash);

    /**
     * @param invoiceEntryStash 参数
     * @return
     * @describe 更新录票记录信息
     * <AUTHOR>
     * @date 2020 5/27 17:30:01
     */
    Integer updateInvoiceEntryStash(InvoiceEntryStash invoiceEntryStash);

    /**
     * @param invoiceEntryStashId 参数
     * @return
     * @describe 获取录票记录基础信息
     * <AUTHOR>
     * @date 2020/5/28 10:06
     */
    InvoiceEntryStash getInvoiceEntryStashBaseInfoById(Integer invoiceEntryStashId);

    /**
     * @param map 参数
     * @return
     * @describe 查询带录票的订单
     * <AUTHOR>
     * @date 2020/5/28
     */
    List<BuyorderVo> getInvoiceBuyorderList(Map<String, Object> map);

    /**
     * @param hxInvoiceDetail 参数
     * @return
     * @describe 添加航信录票详情
     * <AUTHOR>
     * @date 2020/6/1 10:42:28
     */
    Integer insertHxInvoiceDetail(HxInvoiceDetail hxInvoiceDetail);

    /**
     * @param hxInvoiceId 参数
     * @return
     * @describe 清除发票已录票信息
     * <AUTHOR>
     * @date 2020/6/1 15:20:01
     */
    Integer deleteInvoiceEntryStash(Integer hxInvoiceId);

    /**
     * @param hxInvoiceDetailId 参数
     * @return
     * @describe 通过ID过去航信发票详情
     * <AUTHOR>
     * @date 2020/6/2 13:07:02
     */
    HxInvoiceDetail getHxInvoiceDetailById(Integer hxInvoiceDetailId);

    /**
     * @param hxInvoice 参数
     * @return
     * @describe 更新航信发票信息
     * <AUTHOR>
     * @date 2020/6/2 15:17:28
     */
    Integer updateHxInvoice(HxInvoice hxInvoice);

    /**
     * @param invoiceEntryStashId 参数
     * @return
     * @describe 根据ID获取采购商品基本信息
     * <AUTHOR>
     * @date 2020/6/2 15:34:28
     */
    BuyorderGoods getBuyorderGoodsBaseInfoByInvoiceEntryStashId(Integer invoiceEntryStashId);

    /**
     * @param hxInvoiceId 参数
     * @return
     * @describe hugo
     * <AUTHOR>
     * @date 2020/6/2 16:28:28
     */
    HxInvoice getHxInvocieBaseInfoById(Integer hxInvoiceId);

    /**
     * @param buyorderGoodsId
     * @return
     * @describe 根据采购商品ID获取其录票信息
     * <AUTHOR>
     * @date 2020/6/3 16:35:48
     */
    BuyorderGoodsRecordDTO getBuyorderGoodsRecordDTOByGoodsId(Integer buyorderGoodsId);

    /**
     * @param hxInvoiceJBuyorderGoods
     * @return
     * @describe 添加航信发票商品和采购单商品对照表
     * <AUTHOR>
     * @date 2020/6/5 13:49:18
     */
    Integer insertHxInvoiceJBuyorderGoods(HxInvoiceJBuyorderGoodsPO hxInvoiceJBuyorderGoods);

    /**
     * @param hxInvoice 航信发票
     * @return
     * @describe 标记航信发票的退票处理状态
     * <AUTHOR>
     * @date 2020/6/9 15:25:25
     */
    Integer updateInvoiceRefundStatus(HxInvoice hxInvoice);

    /**
     * describe 批量保存更新航信的发票状态信息
     *
     * @param map
     * @return
     * @Author:hugo
     * @date:2020/06/17
     */
    Integer batchSaveHxInvoiceStatus(Map<String, Object> map);

    /**
     * @desc 根据条件查询航信发票（手动录票时使用）
     * @param hxInvoice 航信发票
     * @return
     * <AUTHOR>
     * @date  2020/6/30 16:52:20
     */
    List<HxInvoice> getHxInvoiceInfoByCondition(HxInvoice hxInvoice);

    /**
     * 获取航信发票已审核金额统计数据
     *
     * @return
     */
    Invoice getSupplyHxInvoiceVerifiedCount();

    /**
     * 获取航信发票审核中金额统计数据
     * @return
     */
    Invoice getSupplyHxInvoiceVerifingCount(Map<String, Object> map);

    /**
     * 根据航信发票ID获取发票基本信息
     *
     * @param invoiceIds
     * @return
     */
    List<Invoice> getHxInvoiceBaseInfoByInvoiceIds(@Param("invoiceIds") List<Integer> invoiceIds);

    /**
     * 获取航信发票待认领统计数据
     *
     * @param invoiceStatus 航信发票流转状态
     * @return
     */
    Invoice getHxInvoiceCount(@Param("invoiceStatus") Integer invoiceStatus);

    /**
     * 航信统计信息公共接口
     * @param searchDTO
     * @return
     */
    Invoice getHxInvoiceCountCommonInfo(HxInvoiceSearchDTO searchDTO);

    /**
     * 进项票(财务) 页面 统计信息
     *
     * @param searchDTO
     * @return
     */
    Invoice getHxInvoiceCountWithAllPage(HxInvoiceSearchDTO searchDTO);

    List<HxInvoice> getValidHxInvoice();

    /**
     * 接入旧的录票系统计算航信发票录票金额 (旧录票系统的计算方式)
     * 注: 包含本次计算在内
     *
     * @param invoiceNo
     * @param hxInvoiceId
     * @return
     */
    HxInvoiceVo getRecordedAmountByInvoiceInfo(@Param("invoiceNo") String invoiceNo, @Param("hxInvoiceId") Integer hxInvoiceId);

    /**
     * 保存航信发票路径信息
     * @param invoice
     * @return
     */
    int saveHxInvoiceHref(Invoice invoice);

    /**
     * 查询出无效票列表
     * @param map
     * @return
     */
    List<HxInvoiceVo> getHxInvoiceInvalidListPage(Map<String, Object> map);

    /**
     * 根据HxInvoiceId查询录票
     */
    List<Invoice> listInvoiceByHxInvoiceId(@Param("hxInvoiceId") Integer hxInvoiceId);

    /**
     *  获取所有无效票列表
     * @return
     */
    List<Integer> getInValidList();

    /**
     *  获取所有异常票
     * @return
     */
    List<HxInvoice> getInExceptionList();

    /**
     * 航信发票ID获取已录票金额
     *
     * @param hxInvoiceId
     * @return
     */
    BigDecimal getRecordedAmountByHxInvoiceId(Integer hxInvoiceId);

    /**
     * 主键获取已录信息
     * @param hxInvoiceId
     * @return
     */
    HxInvoiceVo getHxInvoiceRecordInfoByHxInvoiceId(Integer hxInvoiceId);

    /**
     * 条件获取最近的航信发票
     * @param invoiceCode
     * @param invoiceNum
     * @return
     */
    HxInvoice getRecentHxInvoiceInfoByCondition(@Param("invoiceCode") String invoiceCode, @Param("invoiceNum") String invoiceNum);
}
