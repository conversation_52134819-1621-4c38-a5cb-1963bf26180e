package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail;
import com.vedeng.erp.saleorder.dto.AfterSalesInstallServiceRecordDetailDto;
import com.vedeng.logistics.model.WarehouseGoodsOutInItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AfterSalesInstallServiceRecordDetailMapper {
    int deleteByPrimaryKey(Integer afterSalesServiceDetailId);

    int insert(AfterSalesInstallServiceRecordDetail record);

    int insertSelective(AfterSalesInstallServiceRecordDetail record);

    AfterSalesInstallServiceRecordDetail selectByPrimaryKey(Integer afterSalesServiceDetailId);

    int updateByPrimaryKeySelective(AfterSalesInstallServiceRecordDetail record);

    int updateByPrimaryKey(AfterSalesInstallServiceRecordDetail record);

    /**
     * <AUTHOR>
     * @desc 根据主表id查询信息
     * @param afterSalesServiceId
     * @return
     */
    List<AfterSalesInstallServiceRecordDetail> queryInfoByServiceId(@Param("afterSalesServiceId")Integer afterSalesServiceId);

    /**
     * <AUTHOR>
     * @desc 根据售后单商品id查询记录
     * @param afterSalesGoodsId
     * @return
     */
    List<AfterSalesInstallServiceRecordDetail> queryInfoByAftersalesGoodsId(@Param("afterSalesGoodsId")Integer afterSalesGoodsId);

    /**
     * 逻辑删除
     * @param afterSalesServiceId
     * @return
     */
    int logicDelete(Integer afterSalesServiceId);

    /**
     * 查询补充码相同的安调记录数据
     * @param supplCode
     * @return
     */
    int selectSupplCode(String supplCode);


}
