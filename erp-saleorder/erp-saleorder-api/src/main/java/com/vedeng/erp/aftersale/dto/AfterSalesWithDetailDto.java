package com.vedeng.erp.aftersale.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AfterSalesWithDetailDto {

    private Integer subjectType;

    private Integer afterType;

    private Integer orderId;

    private String afterSalesNo;

    private Integer companyId;

    private Integer afterSalesDetailId;

    private Integer afterSalesId;

    /**
     * 售后原因（字典库）
     */
    private Integer reason;

    /**
     * 详细说明
     */
    private String comments;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 联系人ID
     */
    private Integer traderContactId;

    /**
     * 联系人
     */
    private String traderContactName;

    /**
     * 手机
     */
    private String traderContactMobile;

    /**
     * 电话
     */
    private String traderContactTelephone;

    /**
     * 款项退还0无 1退到客户余额 2退给客户
     */
    private Integer refund;

    /**
     * 地区最小级ID
     */
    private Integer areaId;

    /**
     * 收货地址ID
     */
    private Integer addressId;

    /**
     * 收货地区
     */
    private String area;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 售后服务费
     */
    private BigDecimal serviceAmount;

    /**
     * 发票类型 字典表
     */
    private Integer invoiceType;

    /**
     * 是否寄送发票 0否 1是
     */
    private Integer isSendInvoice;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款手续费
     */
    private BigDecimal refundFee;

    /**
     * 实际退款金额
     */
    private BigDecimal realRefundAmount;

    /**
     * 已付款金额（不含账期）
     */
    private BigDecimal paymentAmount;

    /**
     * 退款状态0无退款1未退款 2部分退款 3已退款
     */
    private Integer refundAmountStatus;

    /**
     * 手续费状态0未收款 1部分收款 2已收款
     */
    private Integer serviceAmountStatus;

    /**
     * 开票状态0未开票 1部分开票 2全部开票
     */
    private Integer invoiceStatus;

    /**
     * 开票时间
     */
    private Long invoiceTime;

    /**
     * 收票状态0未收票 1部分收票 2全部收票
     */
    private Integer receiveInvoiceStatus;

    /**
     * 收票时间
     */
    private Long receiveInvoiceTime;

    /**
     * 付款状态 0未付款 1部分付款 2全部付款
     */
    private Integer paymentStatus;

    /**
     * 付款时间
     */
    private Long paymentTime;

    /**
     * 收款状态 0未收款 1部分收款 2全部收款
     */
    private Integer receivePaymentStatus;

    /**
     * 收款时间
     */
    private Long receivePaymentTime;

    /**
     * 交易方式 字典库
     */
    private Integer traderMode;

    /**
     * 交易主体1对公2对私
     */
    private Integer traderSubject;

    /**
     * 收款方
     */
    private String payee;

    /**
     * 开户银行
     */
    private String bank;

    /**
     * 开户行支付联行号
     */
    private String bankCode;

    /**
     * 银行帐号
     */
    private String bankAccount;

    /**
     * 偿还账期金额
     */
    private BigDecimal periodAmount;

    /**
     * 收票公司ID
     */
    private Integer invoiceTraderId;

    /**
     * 收票公司名称
     */
    private String invoiceTraderName;

    /**
     * 收票联系人ID
     */
    private Integer invoiceTraderContactId;

    /**
     * 收票联系人名称
     */
    private String invoiceTraderContactName;

    /**
     * 收票联系人手机
     */
    private String invoiceTraderContactMobile;

    /**
     * 收票联系人电话
     */
    private String invoiceTraderContactTelephone;

    /**
     * 收票地址ID
     */
    private Integer invoiceTraderAddressId;

    /**
     * 收票地区
     */
    private String invoiceTraderArea;

    /**
     * 收票地址
     */
    private String invoiceTraderAddress;

    /**
     * 开票备注
     */
    private String invoiceComments;

    /**
     * 最终应退金额
     */
    private BigDecimal finalRefundableAmount;

    /**
     * 第一责任部门
     */
    private Integer firstResponsibleDepartment;

    /**
     * 售后联系人名称
     */
    private String afterConnectUsername;

    /**
     * 售后联系人电话
     */
    private String afterConnectPhone;

    /**
     * 款项退还信息备注
     */
    private String refundComment;
}
