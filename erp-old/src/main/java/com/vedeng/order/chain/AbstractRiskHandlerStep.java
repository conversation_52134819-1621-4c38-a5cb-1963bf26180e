package com.vedeng.order.chain;


import com.vedeng.order.chain.model.RiskModel;
import com.vedeng.order.service.impl.SupplierCommonServiceImpl;
import com.wms.service.chain.HandlerStepContext;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @ClassName AbstractRiskHandlerStep.java
 * @Description TODO 风控责任链
 * @createTime 2020年12月08日 11:36:00
 */
@Service
public abstract class AbstractRiskHandlerStep extends SupplierCommonServiceImpl implements RiskHandlerStep{

    private RiskHandlerStep next;

    public RiskHandlerStep getNext() {
        return next;
    }

    public void setNext(RiskHandlerStep next) {
        this.next = next;
    }

    @Override
    public RiskModel dealWith(HandlerStepContext context) {

        RiskModel riskModel = this.doDealWith(context);

        if(riskModel.getIsCheck() && next != null){
            context.put("riskModel",riskModel);
            return  next.dealWith(context);
        }

        return  riskModel;

    }


    public abstract RiskModel doDealWith(HandlerStepContext context);

}
