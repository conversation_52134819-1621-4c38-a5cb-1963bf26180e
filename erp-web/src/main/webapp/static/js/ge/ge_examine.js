//提交
function editSubmit(){
    checkLogin();
    var $form = $("#editForm");

    //验证重复审核
    var oldStatus = $form.find("#oldStatus").val();

    if(oldStatus==1 || oldStatus==2 ){
        var oldStatusStr="";
        if(oldStatus==1){
            oldStatusStr="可跟进"
        }else  if(oldStatus==2){
            oldStatusStr="不可跟进"
        }

        warnTips("status","报单状态已置为"+oldStatusStr);
        return false;
    }
    $("input[name='status']:checked").val();
   var bdStatus = $("select[name='status']").find("option:selected").val();

   if(!bdStatus){
       warnTips("status","请填写报单状态");
       return false;
   }
   //如果报单状态 是不可跟进 后面的输入框校验必填
   if(bdStatus==2){
        if(!$("#content").val()){
            warnTips("content","请填写报单状态输入框");
            return false;
        }
   }

   //account名  若是否有account是有 account名 和account地址必填    0无 1有
    var isHavingAccount =$("select[name='isHavingAccount']").find("option:selected").val();
   if(!isHavingAccount){
       warnTips("isHavingAccount","请填写是否有account");
       return false;
   }
   if(isHavingAccount==1){
        if(!$("#accountName").val()){
            warnTips("accountName","请填写account名");
            return false;
        }
       if ($("#geExamine").find("#province :selected").val() == 0) {
           warnTips("sales_area_msg_div","请填写account地址");//文本框ID和提示用语
           return false;
       }
       if ($("#geExamine").find("#city :selected").val() == 0) {
           warnTips("sales_area_msg_div","请填写account地址");//文本框ID和提示用语
           return false;
       }
       if ($("#geExamine").find("#zone :selected").val() == 0) {
           warnTips("sales_area_msg_div","请填写account地址");//文本框ID和提示用语
           return false;
       }

       if(!$("#accountAddress").val()){
           warnTips("accountAddress","请填写account地址");//文本框ID和提示用语
           return false;
       }
   }


   //是否有MPC 0无 1有
    var isHavingMpc = $("#isHavingMpc").val();
   if(!isHavingMpc){
       warnTips("isHavingMpc","请填写是否有MPC");
       return false;
   }
   if(isHavingMpc==1){
        if(!$("#mpcDetail").val()){
            warnTips("mpcDetail","请填写MPC详情");
            return false;
        }
   }
    
    var salesArea = ($("#geExamine").find("#province :selected").text()=="请选择"?"":$("#geExamine").find("#province :selected").text()) + " "
        + ($("#geExamine").find("#city :selected").text()=="请选择"?"":$("#geExamine").find("#city :selected").text()) + " "
        + ($("#geExamine").find("#zone :selected").text()=="请选择"?"":$("#geExamine").find("#zone :selected").text());
    $("#quotePayMoneForm").find("#salesArea").val(salesArea);
    var province = $("#geExamine").find("#province :selected").val()=="0"?"":$("#geExamine").find("#province :selected").val();
    var city = $("#geExamine").find("#city :selected").val();
    var zone = $("#geExamine").find("#zone :selected").val();
    var zoneName = $("#zone :selected").text();
    var provinceName = $("#province :selected").val()=="0"?"":$("#province :selected").text();
    var cityName = $("#city :selected").text();
    $form.find("#accountArea").val(zoneName=="请选择"?(cityName=="请选择"?provinceName:provinceName + cityName):provinceName + cityName + zoneName);
    $form.find("#accountAreaId").val(zone=="0"?(city=="0"?province:city):zone);
    $form.submit();

}

function setMust1(){
    var isHavingAccount = $("#isHavingAccount").val();
    if(isHavingAccount == 1){
        $("#accountMust1").show();
        $("#accountMust2").show();
    }else {
        $("#accountMust1").hide();
        $("#accountMust2").hide();
    }
}
function setMust2(){
    var isHavingMpc = $("#isHavingMpc").val();
    if(isHavingMpc == 1){
        $("#mpcMust1").show();
    }else {
        $("#mpcMust1").hide();
    }
}