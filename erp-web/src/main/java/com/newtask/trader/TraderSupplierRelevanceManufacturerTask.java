package com.newtask.trader;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.goods.manufacturer.dao.ManufacturerMapper;
import com.vedeng.goods.manufacturer.model.Manufacturer;
import com.vedeng.trader.dao.TraderSupplierMapper;
import com.vedeng.trader.model.TraderSupplier;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商历史数据关系处理
 * @date 2022/6/1 9:26
 */
@Component
@JobHandler(value = "TraderSupplierRelevanceManufacturerTask")
public class TraderSupplierRelevanceManufacturerTask extends AbstractJobHandler {

    @Autowired
    private ManufacturerMapper manufacturerMapper;

    @Autowired
    private TraderSupplierMapper traderSupplierMapper;


    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        List<TraderSupplier> traderSuppliers = traderSupplierMapper.getAllByTraderType(1);
        List<String> traderSupplierNames = traderSuppliers.stream().map(TraderSupplier::getTraderSupplierName).collect(Collectors.toList());
        Map<String, Integer> map = traderSuppliers.stream().collect(Collectors.toMap(TraderSupplier::getTraderSupplierName, TraderSupplier::getTraderId));

        List<Manufacturer> manufacturers = manufacturerMapper.getByManufacturerNameIn(traderSupplierNames);
        manufacturers.forEach(manufacturer -> {
            Integer traderId = map.get(manufacturer.getManufacturerName());
            if (traderId != null && (manufacturer.getRelateId() == null || manufacturer.getRelateId() == 0)) {
                manufacturer.setRelateId(traderId);
            }
        });
        manufacturerMapper.updateBatchNameSelective(manufacturers);
        return ReturnT.SUCCESS;
    }

}
