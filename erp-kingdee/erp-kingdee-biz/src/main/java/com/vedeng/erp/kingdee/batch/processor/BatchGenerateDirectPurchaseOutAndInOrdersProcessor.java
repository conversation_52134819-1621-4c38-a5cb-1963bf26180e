package com.vedeng.erp.kingdee.batch.processor;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsInEnum;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsOutEnum;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseOutInSourceEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.item.ItemProcessor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生成直发采购出库单和入库单process
 */
@Service
@Slf4j
public class BatchGenerateDirectPurchaseOutAndInOrdersProcessor implements ItemProcessor<BatchExpressDto,BatchGenerateDirectPurchaseOutAndInOrdersDto> {

    @Autowired
    private BatchExpressDetailDtoMapper batchExpressDetailDtoMapper;
    @Autowired
    private BatchBuyorderGoodsDtoMapper batchBuyorderGoodsDtoMapper;
    @Autowired
    private BatchBuyorderDtoMapper batchBuyorderDtoMapper;
    @Autowired
    private BatchRExpressWarehouseGoodsOutInDtoMapper batchRExpressWarehouseGoodsOutInDtoMapper;

    @Override
    public BatchGenerateDirectPurchaseOutAndInOrdersDto process(BatchExpressDto batchExpressDto) throws Exception {
        log.info("批量生成直发采购出库单和入库单process开始，参数：{}", JSON.toJSONString(batchExpressDto));

        List<BatchRExpressWarehouseGoodsOutInDto> rExpressWarehouseGoodsOutInDtoList = batchRExpressWarehouseGoodsOutInDtoMapper.findByExpressIdAndIsDelete(batchExpressDto.getExpressId(), KingDeeConstant.ZERO);
        if (CollUtil.isNotEmpty(rExpressWarehouseGoodsOutInDtoList)) {
            log.error("该快递单已经生成过直发采购出库单或入库单，参数：{}", JSON.toJSONString(batchExpressDto));
            return null;
        }

        List<BatchExpressDetailDto> batchExpressDetailDtos = batchExpressDetailDtoMapper.findByExpressIdAndBusinessType(batchExpressDto.getExpressId(), KingDeeConstant.ID_515);

        if (CollUtil.isEmpty(batchExpressDetailDtos)) {
            log.error("未查询到该快递单关联的快递明细信息，参数：{}", JSON.toJSONString(batchExpressDto));
            return null;
        }

        List<BatchExpressDetailDto> batchExpressDetailDtoList = batchExpressDetailDtos
                .stream()
                .filter(batchExpressDetailDto -> Objects.nonNull(batchExpressDetailDto.getNum()) && batchExpressDetailDto.getNum() != 0)
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(batchExpressDetailDtoList)) {
            log.error("该快递单下的所有快递明细中的NUM为0，参数：{}", JSON.toJSONString(batchExpressDetailDtos));
            return null;
        }

        List<Integer> relatedIds = batchExpressDetailDtoList.stream().map(BatchExpressDetailDto::getRelatedId).collect(Collectors.toList());

        List<BatchBuyorderGoodsDto> batchBuyorderGoodsDtoList = batchBuyorderGoodsDtoMapper.findByBuyorderGoodsIdInAndIsDelete(relatedIds, KingDeeConstant.ZERO);
        if (CollUtil.isEmpty(batchBuyorderGoodsDtoList)) {
            log.error("未查询到该快递单关联的采购商品信息，参数：{}", relatedIds);
            return null;
        }

        List<Integer> buyOrderIds = batchBuyorderGoodsDtoList.stream().map(BatchBuyorderGoodsDto::getBuyorderId).distinct().collect(Collectors.toList());
        if (buyOrderIds.size() > 1) {
            log.error("该采购直发快递关联了多个采购单，数据异常，参数：{}", buyOrderIds);
            return null;
        }

        BatchBuyorderDto batchBuyorderDto = batchBuyorderDtoMapper.selectByPrimaryKey(buyOrderIds.get(0));
        if (Objects.isNull(batchBuyorderDto)) {
            log.error("未查询到该快递单关联的采购单信息，参数：{}", buyOrderIds.get(0));
            return null;
        }

        List<Map<String, Object>> goodsIdAndSendNum = batchExpressDetailDtoList.stream()
                .map(batchExpressDetailDto -> {
                    Map<String, Object> goodsIdAndSendNumMap = new HashMap<>();
                    goodsIdAndSendNumMap.put("byOrderGoodsId", batchExpressDetailDto.getRelatedId());
                    goodsIdAndSendNumMap.put("sendNum", batchExpressDetailDto.getNum());
                    return goodsIdAndSendNumMap;
                })
                .collect(Collectors.toList());

        String id_sendN_sendedN_sumN = batchExpressDetailDtoList.stream()
                .map(batchExpressDetailDto -> batchExpressDetailDto.getRelatedId() + "|" + batchExpressDetailDto.getNum() + "_")
                .reduce("", (a, b) -> a + b);

        log.info("批量生成直发采购出库单和入库单process，goodsIdAndSendNum：{}", JSON.toJSONString(goodsIdAndSendNum));
        log.info("批量生成直发采购出库单和入库单process，id_sendN_sendedN_sumN：{}", id_sendN_sendedN_sumN);

        return BatchGenerateDirectPurchaseOutAndInOrdersDto
                .builder()
                .batchBuyorderDto(batchBuyorderDto)
                .goodsIdAndSendNum(goodsIdAndSendNum)
                .batchExpressDto(batchExpressDto)
                .id_sendN_sendedN_sumN(id_sendN_sendedN_sumN)
                .build();
    }
}
