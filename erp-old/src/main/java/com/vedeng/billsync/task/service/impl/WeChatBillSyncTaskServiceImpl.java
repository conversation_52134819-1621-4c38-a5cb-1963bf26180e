package com.vedeng.billsync.task.service.impl;

import com.alibaba.fastjson.TypeReference;
import com.vedeng.billsync.dao.TmpWechatBillDataExtMapper;
import com.vedeng.billsync.task.model.entity.TmpWechatBillDataExtDo;
import com.vedeng.billsync.task.model.entity.TmpWechatBillDataPo;
import com.vedeng.billsync.task.service.WeChatBillSyncTaskService;
import com.vedeng.common.constant.ApiUrlConstant;
import com.vedeng.common.http.HttpRestClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.util.DateUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;

/**
 * <AUTHOR>
 * @Date: 2020/12/16 12:12
 */
@Service
public class WeChatBillSyncTaskServiceImpl implements WeChatBillSyncTaskService, ApiUrlConstant {

    public final static Logger logger = LoggerFactory.getLogger(WeChatBillSyncTaskServiceImpl.class);

    @Resource
    TmpWechatBillDataExtMapper tmpWechatBillDataExtMapper;

    @Value("${api_url}")
    protected String apiUrl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResultInfo syncWeChatBillList(String billDate) {
        //总条数
        int totalItem;
        //成功插入条数
        int successInsertItem = 0;
        try {
            String url = apiUrl + API_WECHAT_SYNC_BILL_DATA;
            String billDateW = DateUtil.formatStrDate(billDate, DateUtil.DATE_FORMAT, DateUtil.DATE_FORMAT_NO);
            TypeReference<ResultInfo<List<TmpWechatBillDataPo>>> typeReference = new TypeReference<ResultInfo<List<TmpWechatBillDataPo>>>() {
            };
            Map<String, String> mapParam = new HashMap<String, String>(1);
            mapParam.put("billDate", billDateW);
            Map<String, String> mapHeaders = new HashMap<String, String>(1);
            mapHeaders.put("version", "v1");
            ResultInfo<List<TmpWechatBillDataPo>> result = HttpRestClientUtil.restGet(url, typeReference, mapHeaders, mapParam);
            if (null != result && result.getCode() == 0) {
                List<TmpWechatBillDataPo> tmpWechatBillDataExtPoLists = result.getData();
                if (CollectionUtils.isNotEmpty(tmpWechatBillDataExtPoLists)) {
                    Integer deleteResult = tmpWechatBillDataExtMapper.deleteBySycTime(billDate);
                    logger.info("类型 微信 操作 删除 {} 账单 删除数据返回结果 {}", billDate, deleteResult);
                    totalItem = tmpWechatBillDataExtPoLists.size();
                    for (TmpWechatBillDataPo weChatBillPoData : tmpWechatBillDataExtPoLists) {
                        TmpWechatBillDataExtDo tmpWechatBillDataExtDo = new TmpWechatBillDataExtDo();
                        tmpWechatBillDataExtDo.setOccurrenceTime(weChatBillPoData.getOccurrenceTime());
                        tmpWechatBillDataExtDo.setPublicId(weChatBillPoData.getPublicId());
                        tmpWechatBillDataExtDo.setMerchantNo(weChatBillPoData.getMerchantNo());
                        tmpWechatBillDataExtDo.setSpecialMerchantNo(weChatBillPoData.getSpecialMerchantNo());
                        tmpWechatBillDataExtDo.setDeviceNo(weChatBillPoData.getDeviceNo());
                        tmpWechatBillDataExtDo.setWxOrderNo(weChatBillPoData.getWxOrderNo());
                        tmpWechatBillDataExtDo.setOrderNo(weChatBillPoData.getOrderNo());
                        tmpWechatBillDataExtDo.setUserId(weChatBillPoData.getUserId());
                        tmpWechatBillDataExtDo.setBusinessType(weChatBillPoData.getBusinessType());
                        tmpWechatBillDataExtDo.setBusinessStatus(weChatBillPoData.getBusinessStatus());
                        tmpWechatBillDataExtDo.setBank(weChatBillPoData.getBank());
                        tmpWechatBillDataExtDo.setCurrency(weChatBillPoData.getCurrency());
                        Optional.ofNullable(weChatBillPoData.getOrderAmountDue()).ifPresent(item -> tmpWechatBillDataExtDo.setOrderAmountDue(new BigDecimal(item)));
                        Optional.ofNullable(weChatBillPoData.getCouponAmount()).ifPresent(item -> tmpWechatBillDataExtDo.setCouponAmount(new BigDecimal(item)));
                        tmpWechatBillDataExtDo.setWxRefundNo(weChatBillPoData.getWxRefundNo());
                        tmpWechatBillDataExtDo.setRefundNo(weChatBillPoData.getRefundNo());
                        Optional.ofNullable(weChatBillPoData.getRefundAmount()).ifPresent(item -> tmpWechatBillDataExtDo.setRefundAmount(new BigDecimal(item)));
                        Optional.ofNullable(weChatBillPoData.getRechargeRefundAmount()).ifPresent(item -> tmpWechatBillDataExtDo.setRechargeRefundAmount(new BigDecimal(item)));
                        tmpWechatBillDataExtDo.setRefundType(weChatBillPoData.getRefundType());
                        tmpWechatBillDataExtDo.setRefundStatus(weChatBillPoData.getRefundStatus());
                        tmpWechatBillDataExtDo.setSkuName(weChatBillPoData.getSkuName());
                        tmpWechatBillDataExtDo.setDataPack(weChatBillPoData.getDataPack());
                        Optional.ofNullable(weChatBillPoData.getHandlingFee()).ifPresent(item -> tmpWechatBillDataExtDo.setHandlingFee(new BigDecimal(item)));
                        tmpWechatBillDataExtDo.setRate(weChatBillPoData.getRate());
                        Optional.ofNullable(weChatBillPoData.getOrderAmount()).ifPresent(item -> tmpWechatBillDataExtDo.setOrderAmount(new BigDecimal(item)));
                        Optional.ofNullable(weChatBillPoData.getReqRefundAmount()).ifPresent(item -> tmpWechatBillDataExtDo.setReqRefundAmount(new BigDecimal(item)));
                        tmpWechatBillDataExtDo.setSyncTime(DateUtil.StringToDate(billDate));
                        tmpWechatBillDataExtDo.setAddTime(new Timestamp(System.currentTimeMillis()));
                        Integer insertResult = tmpWechatBillDataExtMapper.insert(tmpWechatBillDataExtDo);
                        if (insertResult == 1) {
                            successInsertItem++;
                        }
                        logger.info("类型 微信 操作 插入 {} 账单 商户订单号 {} 插入数据库返回结果 {}",  billDate , tmpWechatBillDataExtDo.getOrderNo(), insertResult);
                    }
                } else {
                    return new ResultInfo(-1, "微信账单插入失败！数据返回为空！");
                }
            } else {
                if (null == result){
                    result = new ResultInfo(-1,"Result接受参数为NULL,请检查API地址是否正确！");
                }
                return new ResultInfo(-1, "同步微信账单失败！" + " Message:" + result.getMessage() + " 状态码：" + result.getStatus());
            }
        } catch (Exception e) {
            logger.error("同步微信账单失败！", e);
            throw e;
        }
        return new ResultInfo(0, "微信账单插入成功！总条数[ " + totalItem + " ],成功插入条数[ " + successInsertItem + " ]。");
    }
}
