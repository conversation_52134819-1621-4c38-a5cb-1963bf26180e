<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<div id="app" style="display: none;">
    <template v-if="buyorderExpense.buyorderExpenseId==0">
        <p style="font-size: 14px">温馨提示：</p>
        <p style="font-size: 14px">1、基于采购单创建，费用单将直接以采购单的供应商作为此订单供应商，并且可同步至所选择的采购订单，建立查看关系</p>
        <p style="font-size: 14px">2、独立创建，则不与任何采购订单有直接关系，独立进行货款票运转</p>
        <br>
        <div style="margin-left: 25px">
            <el-radio-group v-model="baseBuyOrder" @change="independentChange">
                <el-radio-button :label='true'>基于采购单创建</el-radio-button>
                <el-radio-button :label='false'>独立创建</el-radio-button>
            </el-radio-group>
        </div>
        <br>

        <br>
    </template>

    <el-form :rules="rules" ref="buyorderExpenseForm" :model="buyorderExpense" label-width="120px">
        <el-select style="display: none">
        </el-select>
        <template v-if="baseBuyOrder">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>采购订单</span>
                    <el-button v-if=" buyorderExpense.buyorderExpenseId == 0" style="float: right; padding: 3px 0"
                               type="text" @click="chooseValidBuyorder()">选择采购订单
                    </el-button>
                    <el-button v-if=" buyorderExpense.buyorderExpenseId != 0" style="float: right; padding: 3px 0"
                               type="text" @click="chooseValidBuyorder()">重新选择
                    </el-button>

                </div>
                <el-table :data="buyorder" border style="width: 100%">
                    <el-table-column
                            label="采购单号"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.buyorderNo}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="供应商"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="创建人"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.createName }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="创建人部门"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.buyDepartmentName }}</span>
                        </template>
                    </el-table-column>

                    <el-table-column
                            label="创建时间"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.addTime }}</span>
                        </template>
                    </el-table-column>
                </el-table>
            </el-card>
        </template>
        <template v-if="baseBuyOrder">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>供应商信息</span>
                </div>
                <el-table :data="buyorderTraderSupplierInfo" border style="width: 100%"

                >
                    <el-table-column
                            label="供应商"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="联系人"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderContactStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="联系人地址"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderAddressStr }}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="供应商备注"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderComments }}</span>
                        </template>
                    </el-table-column>

                </el-table>

            </el-card>
        </template>
        <template v-if="!baseBuyOrder">
            <el-card class="box-card">
                <div slot="header" class="clearfix">
                    <span>供应商信息</span>
                    <el-button v-if="buyorderExpense.traderId == ''" style="float: right; padding: 3px 0"
                               type="text" @click="chooseValidTrader()">选择供应商
                    </el-button>
                    <el-button v-if="buyorderExpense.traderId != ''" style="float: right; padding: 3px 0"
                               type="text" @click="chooseValidTrader()">重新选择
                    </el-button>
                </div>
                <el-table :data="buyorderTraderSupplierInfo" border style="width: 100%"

                >
                    <el-table-column
                            label="供应商"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderName}}</span>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="联系人"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <el-select
                                    v-model="scope.row.traderContactStr"
                                    filterable
                                    clearable
                                    reserve-keyword
                                    placeholder="请选择联系人"
                                    style="width: 350px;"
                                    @clear="traderContactClear(scope.row)"
                            >
                                <el-option
                                        v-for="item in traderConcatDatas"
                                        :key="item.traderContactId"
                                        :label="item.name+ '/' + item.mobile+'/'+item.telephone"
                                        :value="item.traderContactId"
                                        @click.native="traderConcatSelect(item,scope.row)"
                                >
                                    <%--<span style="float: left">{{ item.name + '/' + item.mobile+'/'+item.telephone }}</span>--%>
                                </el-option>

                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column
                            label="联系人地址"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <%--<span>{{ scope.row.traderAddressStr }}</span>--%>

                            <el-select
                                    v-model="scope.row.traderAddressStr"
                                    filterable
                                    clearable
                                    reserve-keyword
                                    placeholder="请选择联系人地址"
                                    style="width: 350px;"
                                    @clear="traderAddressClear(scope.row)"
                            >
                                <el-option
                                        v-for="item in traderAddressDatas"
                                        :key="item.traderAddressId"
                                        :label="item.area+ ' ' + item.address"
                                        :value="item.traderAddressId"
                                        @click.native="traderAddressSelect(item,scope.row)"
                                >
                                    <%--<span style="float: left">{{ item.name + '/' + item.mobile+'/'+item.telephone }}</span>--%>
                                </el-option>

                            </el-select>

                        </template>
                    </el-table-column>
                    <el-table-column
                            label="供应商备注"
                            align="center"
                    >
                        <template slot-scope="scope">
                            <span>{{ scope.row.traderComments }}</span>
                        </template>
                    </el-table-column>

                </el-table>

            </el-card>
        </template>

        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>虚拟商品信息</span>
                <el-button style="float: right; padding: 3px 0" type="text" @click="chooseExpenseSku()">添加虚拟商品
                </el-button>
            </div>
            <el-table :data="buyorderExpense.buyorderExpenseGoodsList"
                      border
                      show-summary
                      :summary-method="getSummaries"
                      :key="renderTableAgain"
                      ref="tableData"
                      :header-cell-style="{background:'#f5f7fa'}"
                      :expand-row-keys="expends"
                      :row-key="getRowKeys"
                      :row-class-name="getRowClass">

                <el-table-column type="expand">
                    <template slot="header" slot-scope="scope">
                        <el-button type="text" size="mini" @click="toggleRowExpansion">{{ isExpand ? "收起" : "展开" }}
                        </el-button>
                    </template>

                    <template slot-scope="props">
                        <el-table :data="props.row.buyOrderSaleOrderGoodsDetailDtos"
                                  size="mini"
                                  :header-cell-style="{ 'text-align': 'center' }"
                                  :cell-style="{ 'text-align': 'center' }"
                                  style="margin-left:10px;width: 99%"
                                  border>
                            <el-table-column
                                    prop="saleorderNo"
                                    label="关联单号">
                            </el-table-column>
                            <el-table-column
                                    prop="applicantName"
                                    label="申请人">
                            </el-table-column>

                            <el-table-column prop="buyNum" label="采购数量/需采数量">

                                <template slot-scope="scope">
                                    <el-form-item label-width="0"
                                                  :prop="'buyorderExpenseGoodsList.'+props.$index+'.buyOrderSaleOrderGoodsDetailDtos.'+scope.$index+'.buyNum'"
                                                  :rules="[
                                                  {
                                                  ...rules.buyNum[1],
                                                  row: scope.row,
                                                  }]">
                                        <el-input v-model="scope.row.buyNum"
                                                  style="width:100px"
                                                  maxlength="10"
                                                  @input="handleSaleOrderNumberValue(scope.row)"
                                                  @change="saleOrderBuyNumChanged()">
                                        </el-input>
                                        / <span style="font-size: 12px">{{scope.row.num}}</span>
                                    </el-form-item>
                                </template>
                            </el-table-column>

                            <el-table-column
                                    prop="price"
                                    label="销售价">
                            </el-table-column>
                            <el-table-column
                                    prop="deliveryCycle"
                                    label="销售货期">
                            </el-table-column>
                            <el-table-column
                                    prop="insideComments"
                                    label="内部备注">
                            </el-table-column>
                            <el-table-column
                                    prop="goodsComments"
                                    label=产品备注>
                            </el-table-column>
                            <el-table-column
                                    prop="terminalTraderName"
                                    label="终端客户名称">
                            </el-table-column>
                        </el-table>
                    </template>
                </el-table-column>
                <el-table-column
                        label="订货号"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.sku }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="产品名称"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.goodsName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="费用类别"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.expenseCategoryName }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="是否可库存管理"
                        align="center"
                >
                    <template slot-scope="scope">
                        <span>{{ scope.row.haveStockManage == 1 ? '是' : '否' }}</span>
                    </template>
                </el-table-column>

                <el-table-column
                        label="采购数量"
                        align="center"
                        min-width="50%"
                        prop="num"
                >
                    <template slot-scope="scope">
                        <el-form-item label-width="0"
                                      v-if="scope.row.source == 0"
                                      :prop="'buyorderExpenseGoodsList.' + scope.$index + '.num'"
                                      :rules="rules.num">
                            <el-input v-model="scope.row.num" maxlength="10"
                                      @input="handleInputNumberValue(scope.row)"
                                      @change="buyNumChanged(scope.row)"
                            ></el-input>
                        </el-form-item>

                        <el-form-item label-width="0" v-if="scope.row.source == 1">
                            {{scope.row.num}}
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                        label="单价"
                        align="center"
                        min-width="50%"
                >
                    <template slot-scope="scope">
                        <el-form-item label-width="0" :prop="'buyorderExpenseGoodsList.' + scope.$index + '.price'"
                                      :rules="rules.price">
                            <el-input v-model="scope.row.price" maxlength="13"
                                      @input="handleInputAmountValue(scope.row)"
                                      @change="buyPriceChanged(scope.row)"
                            >
                            </el-input>
                        </el-form-item>
                    </template>
                </el-table-column>

                <el-table-column
                        label="总额"
                        align="center"
                        prop="oneBuyorderExpenseGoodsAmount"
                >
                    <template slot-scope="scope">
                        <span>{{ (scope.row.oneBuyorderExpenseGoodsAmount).toFixed(2) }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                        label="采购备注"
                        align="center"
                >
                    <template slot-scope="scope">
                        <el-form-item label-width="0">
                            <el-input type="textarea" autosize v-model="scope.row.insideComments"
                                      maxlength="100"></el-input>
                        </el-form-item>
                    </template>
                </el-table-column>
                <el-table-column label="操作" align="center">

                    <template slot-scope="scope">
                        <el-tooltip effect="light" content="已关联销售订单信息，无法删除" placement="top-start"
                                    :disabled="scope.row.source == 0">
                            <div style="display:inline-block;">
                                <el-button
                                        size="mini"
                                        type="danger"
                                        @click="handleDelete(scope.$index, scope.row)"
                                        :disabled="scope.row.source == 1">
                                    删除
                                </el-button>
                            </div>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>
        </el-card>
        <template v-if="!baseBuyOrder">
            <br>
            <el-form-item label="费用来源" prop="expenseSource" :rules="rules.expenseSource">
                <el-input
                        type="textarea"
                        maxlength="500"
                        rows="1"
                        v-model="buyorderExpense.expenseSource"
                        style="width: 22%;"
                >
                </el-input>
            </el-form-item>
        </template>
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>付款信息</span>
            </div>

            <el-form-item label="付款方式">
                <el-select v-model="buyorderExpense.paymentType" placeholder="请选择" style="width: 20%;">
                    <el-option :value="item.sysOptionDefinitionId" v-for="item in paymentTermList"
                               :label="item.title" :key="item.sysOptionDefinitionId">
                    </el-option>
                </el-select>
            </el-form-item>

            <el-form-item label="付款金额" prop="prepaidAmount">
                <el-input v-model="buyorderExpense.prepaidAmount" maxlength="13"
                          style="width: 20%;"
                          @input="(val)=>{limitPrepaidAmountInput(val)}"
                          @change="(val)=>{amountInputChanged(val)}"
                          :disabled="buyorderExpense.paymentType != 424"
                ></el-input>
            </el-form-item>

            <el-form-item v-if="buyorderExpense.paymentType != 419 " label="账期支付" prop="accountPeriodAmount">
                <el-input v-model="buyorderExpense.accountPeriodAmount" maxlength="13"
                          style="width: 20%;"
                          @input="(val)=>{limitAccountPeriodAmountInput(val)}"
                          @change="(val)=>{amountInputChanged(val)}"
                          :disabled="buyorderExpense.paymentType != 424"
                ></el-input>
            </el-form-item>

            <div v-if="buyorderExpense.paymentType == 424 " style="display: flex">
                <el-form-item label="尾款" prop="retainageAmount" style="width: 25%;">
                    <el-input v-model="buyorderExpense.retainageAmount" maxlength="13"
                              @input="(val)=>{limitRetainageAmountInput(val)}"
                              @change="(val)=>{amountInputChanged(val)}"
                    ></el-input>
                </el-form-item>
                <el-form-item label="尾款期限" prop="retainageAmountMonth">
                    <el-input v-model="buyorderExpense.retainageAmountMonth"
                              style="width: 30%"
                              @input="(val)=>{limitRetainageAmountMonthInput(val)}"
                    ></el-input>
                    <span>个月</span>
                </el-form-item>

            </div>

            <el-form-item label="付款备注" prop="name">
                <el-input type="textarea" rows="4" v-model="buyorderExpense.paymentComments"
                          placeholder="对内使用，适用于向财务部同事告知付款要求"
                          style="width: 40%" maxlength="256"></el-input>
            </el-form-item>
            <span style="color: #999;">供应商当前帐期剩余额度{{traderSupplierInfo.periodAmount}}元，帐期天数{{traderSupplierInfo.periodDay}}天；如需更改帐期，您需要在供应商详情财务信息中申请帐期；</span>
            <p v-if="checkPaymentFlag" style="color: #F56C6C;">支付金额总额与总金额不符，请验证</p>
        </el-card>
        <el-card class="box-card">
            <div slot="header" class="clearfix">
                <span>收票信息</span>
            </div>
            <el-form-item label="收票种类" prop="name">
                <el-select v-model="buyorderExpense.invoiceType" placeholder="请选择" style="width: 20%;">
                    <el-option :value="item.sysOptionDefinitionId" v-for="item in receiptTypes"
                               :label="item.title" :key="item.sysOptionDefinitionId"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="收票备注" prop="name">
                <el-input type="textarea" rows="4" v-model="buyorderExpense.invoiceComments"
                          placeholder="对内使用，适用于向财务部同事告知收票要求"
                          style="width: 40%;" maxlength="256"></el-input>
            </el-form-item>
        </el-card>

        <el-form-item style="width: 100%;text-align: center;margin-top: 20px">
            <el-button type="primary" @click="onSubmit('buyorderExpenseForm')" :disabled="formDisabled">确定</el-button>
        </el-form-item>

    </el-form>
</div>
<script src="${pageContext.request.contextPath}/static/api/buyorder/buyorderExpense.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/system/systemDictionary.js?rnd=${resourceVersionKey}"></script>
<script src="${pageContext.request.contextPath}/static/api/trader/traderSupplier.js?rnd=${resourceVersionKey}"></script>

<script type="text/javascript">

    const buyorderExpenseInfo = {
        buyorderExpenseId: ${buyorderExpenseId}
    }

    const saleOrderGoodsIdList = eval('${saleOrderGoodsIdList}')

    const sendThis = (_this) => {
        vm = _this;
    }

    new Vue({
        el: '#app',

        data() {

            var checkPrice = (rule, value, callback) => {
                if (Number(value) === 0) {
                    callback(new Error("单价不可为0"));
                } else {
                    callback();
                }
            }

            var checkPeriodAmountbalance = (rule, value, callback) => {
                if ((Number(this.traderSupplierInfo.periodAmount) - Number(value)) < 0) {
                    callback(new Error("账期余额不足"));
                } else {
                    callback();
                }
            }

            var checkBuyNum = (rule, value, callback) => {
                if (0 < Number(value) && Number(value) <= Number(rule.row.num)) {
                    callback();
                }else if(Number(value) === 0){
                    callback(new Error("请填写采购数量"));
                }else {
                    callback(new Error("数量必须大于等于1且小于等于" + rule.row.num));
                }
            }

            return {
                formDisabled: false,
                //表单校验
                rules: {
                    traderName: [{max: 50, message: '最大支持输入50个汉字', trigger: 'blur'},],
                    price: [
                        {required: true, message: "请填写单价", trigger: 'blur'},
                        {validator: checkPrice, trigger: 'blur'}
                    ],
                    num: {required: true, message: "请填写采购数量", trigger: 'blur'},
                    buyNum: [
                        {required: true, message: "请填写采购数量", trigger: 'blur'},
                        {validator: checkBuyNum, trigger: 'blur'}
                    ],
                    prepaidAmount: [
                        {required: true, message: "付款金额不可为空", trigger: 'blur'},
                    ],
                    accountPeriodAmount: [
                        {required: true, message: "账期金额不可为空", trigger: 'blur'},
                        {validator: checkPeriodAmountbalance, trigger: 'blur'}
                    ],
                    retainageAmount: [
                        {required: true, message: "尾款金额不可为空", trigger: 'blur'},
                    ],
                    retainageAmountMonth: [
                        {required: true, message: "尾款期限不可为空", trigger: 'blur'},
                    ],
                    expenseSource: [
                        {required: true, message: "请填写费用来源", trigger: 'blur'},
                    ]
                },
                // 区分非直属的费用单的不同类型
                baseBuyOrder: true,
                //采购费用单
                buyorderExpense: {
                    buyorderExpenseId: buyorderExpenseInfo.buyorderExpenseId,
                    orderType: 1,
                    traderId: '',
                    traderName: '',
                    traderContactId: '',
                    traderContactName: '',
                    traderContactMobile: '',
                    traderContactTelephone: '',
                    traderAddressId: '',
                    traderArea: '',
                    traderAddress: '',
                    traderComments: '',
                    expenseSource: null,
                    buyorderExpenseGoodsList: [],
                    totalAmount: parseFloat('0').toFixed(2),
                    buyorderId: 0,
                    buyorderNo: '',
                    paymentType: 0,
                    prepaidAmount: parseFloat('0').toFixed(2),
                    accountPeriodAmount: parseFloat('0').toFixed(2),
                    retainageAmount: parseFloat('0').toFixed(2),
                    retainageAmountMonth: null,
                    paymentComments: '',
                    invoiceType: 0,
                    invoiceComments: '',
                    source:0
                },
                //采购单信息
                buyorder: [
                    // {
                    //     buyorderNo: '',
                    //     traderName: '',
                    //     createName: '',
                    //     buyDepartmentName: '',
                    //     addTime: '',
                    // }
                ],
                // 客户联系人
                traderConcatDatas: [],
                // 联系地址
                traderAddressDatas: [],
                //付款方式
                paymentTermList: [],
                //付款方式
                receiptTypes: [],
                //采购单中供应商信息
                buyorderTraderSupplierInfo: [],
                //供应商信息
                traderSupplierInfo: {
                    periodAmount: '-',
                    periodDay: '-',
                },
                //展示校验金额提示
                checkPaymentFlag: false,
                //重新渲染表格
                renderTableAgain: true,
                //默认展开
                isExpand: true,
                expends:[]
            };
        },

        created() {
            sendThis(this);

            getSysOptionDefinitionList({parentCode: "PAYMENT_TYPE"}).then((result) => {
                this.paymentTermList = result.data.data;
                if (this.buyorderExpense.paymentType === 0) {
                    this.$nextTick(() => {
                        this.buyorderExpense.paymentType = 419;
                    })
                }
            });

            getSysOptionDefinitionList({parentCode: "INVOICE_TYPE"}).then((result) => {
                this.receiptTypes = result.data.data;
                <!-- 屏蔽17%税率 -->
                let arry = [];
                this.receiptTypes.forEach(c => {
                    if (c.sysOptionDefinitionId !== 429 && c.sysOptionDefinitionId !== 430) {
                        arry.push(c);
                    }
                })
                this.receiptTypes = arry;
                if (this.buyorderExpense.invoiceType === 0) {
                    this.$nextTick(() => {
                        this.buyorderExpense.invoiceType = 972;
                    })
                }
            });
            //渲染编辑页数据
            if (buyorderExpenseInfo.buyorderExpenseId !== '' && buyorderExpenseInfo.buyorderExpenseId !== 0) {

                getBuyorderExpenseEditInfo({"buyorderExpenseId": this.buyorderExpense.buyorderExpenseId}).then(res => {
                    this.buyorderExpense = _.cloneDeep(res.data.data)
                    this.buyorderExpense = Object.assign({}, this.buyorderExpense, this.buyorderExpense.buyorderExpenseDetailDto);
                    this.buyorderExpense.creator = res.data.data.creator
                    this.buyorderExpense.creatorName = res.data.data.creatorName
                    this.buyorderExpense.buyorderExpenseGoodsList = [];
                    this.buyorderExpense.buyorderExpenseItemDtos.map(item => {
                        item = Object.assign({}, item, item.buyorderExpenseItemDetailDto);
                        if (_.size(item.buyOrderSaleOrderGoodsDetailDtos) > 0) {
                            //来源 0 = 自主创建，1 = 待采购列表
                            item.buyOrderSaleOrderGoodsDetailDtos.map(d=>{
                                item.goodsId = d.goodsId
                                //编辑时加上当前单据数量
                                d.num = d.buyNum + d.num
                                item.source = 1;
                                this.source = 1;
                            })
                        }else{
                            item.source = 0;
                        }
                        this.buyorderExpense.buyorderExpenseGoodsList.push(item)
                    })

                    this.buyorderExpense.buyorderExpenseGoodsList.map(item => {
                        item.oneBuyorderExpenseGoodsAmount = item.num * item.price;
                    })
                    this.buyorder = [];
                    if (this.buyorderExpense.buyorderId == 0) {
                        this.baseBuyOrder = false;
                    } else {
                        this.buyorder.push({
                            buyorderNo: this.buyorderExpense.buyorderNo,
                            traderName: this.buyorderExpense.traderName,
                            createName: this.buyorderExpense.buyOrderDto.creatorName,
                            buyDepartmentName: this.buyorderExpense.buyOrderDto.orgName,
                            addTime: parseTime(this.buyorderExpense.buyOrderDto.addTime),
                        });
                    }

                    this.buyorderTraderSupplierInfo.push({
                        traderName: this.buyorderExpense.traderName,
                        traderContactStr: this.buyorderExpense.traderContactName + "/" + this.buyorderExpense.traderContactMobile + "/" + this.buyorderExpense.traderContactTelephone,
                        traderAddressStr: this.buyorderExpense.traderArea + " " + this.buyorderExpense.traderAddress,
                        traderComments: this.buyorderExpense.traderComments,
                    });

                    // 供应商联系人
                    getTraderConcatData(this.buyorderExpense.traderId).then(res => {
                        vm.traderConcatDatas = res.data.data.list;
                    });
                    // 供应商地址
                    getTraderAddressData(this.buyorderExpense.traderId).then(res => {
                        vm.traderAddressDatas = res.data.data;
                    });

                    getTraderSupplierInfoById({traderId: this.buyorderExpense.traderId}).then((result) => {
                        this.traderSupplierInfo = result.data.data;
                        this.buyorderExpense.periodDay = this.traderSupplierInfo.periodDay;
                    });
                    this.getExpends();
                });
            }
            if (saleOrderGoodsIdList != null && saleOrderGoodsIdList.length>0) {
                byPreBuyorderGetDetail(saleOrderGoodsIdList).then(res => {
                    this.buyorderExpense = _.cloneDeep(res.data.data)
                    this.buyorderExpense.orderType =  1
                    this.buyorderExpense.paymentType = 419
                    this.buyorderExpense.invoiceType = 972;
                    this.buyorderExpense.totalAmount = parseFloat('0').toFixed(2),
                    this.buyorderExpense.prepaidAmount =  parseFloat('0').toFixed(2),
                    this.buyorderExpense.accountPeriodAmount =  parseFloat('0').toFixed(2),
                    this.buyorderExpense.retainageAmount = parseFloat('0').toFixed(2),

                    this.buyorderExpense = Object.assign({}, this.buyorderExpense, this.buyorderExpense.buyorderExpenseDetailDto);
                    this.buyorderExpense.buyorderExpenseGoodsList = [];
                    this.buyorderExpense.buyorderExpenseItemDtos.map(item => {
                        item = Object.assign({}, item, item.buyorderExpenseItemDetailDto);
                        item.oneBuyorderExpenseGoodsAmount = 0.0;
                        item.num = 0;
                        item.price = 0.0;
                        if (_.size(item.buyOrderSaleOrderGoodsDetailDtos) > 0) {
                            //来源 0 = 自主创建，1 = 待采购列表
                            item.source = 1;
                            this.source = 1;
                        }
                        item.buyOrderSaleOrderGoodsDetailDtos.map(m => {
                            item.goodsId = m.goodsId
                            m.buyNum = m.num
                        })
                        this.buyorderExpense.buyorderExpenseGoodsList.push(item)
                        this.saleOrderBuyNumChanged()
                    })
                    this.buyorderExpense.buyorderExpenseId = buyorderExpenseInfo.buyorderExpenseId
                    this.getExpends();
                })
            }
        },

        mounted() {
            loadingApp()
        },

        watch: {
            'buyorderExpense.totalAmount': function (val) {
                this.computedAmount()
            },
            'buyorderExpense.paymentType': function (val) {
                this.paymentTypeChanged()
            }
        },

        methods: {
            //提交表单
            onSubmit(form) {
                if (this.baseBuyOrder && (this.buyorderExpense.buyorderId == null || this.buyorderExpense.buyorderId === 0)) {
                    this.$message({
                        showClose: true,
                        message: '请选择采购订单!',
                        type: 'error'
                    });
                    return;
                }
                if (!this.baseBuyOrder && this.buyorderExpense.traderId == '') {
                    this.$message({
                        showClose: true,
                        message: '请选择供应商信息!',
                        type: 'error'
                    });
                    return;
                }

                if (this.buyorderExpense.traderContactId === '') {
                    this.$message({
                        showClose: true,
                        message: '请选择供应商联系人信息!',
                        type: 'error'
                    });
                    return;
                }
                if (this.buyorderExpense.traderAddressId === '') {
                    this.$message({
                        showClose: true,
                        message: '请选择供应商地址信息!',
                        type: 'error'
                    });
                    return;
                }
                if (this.buyorderExpense.buyorderExpenseGoodsList.length === 0) {
                    this.$message({
                        showClose: true,
                        message: '请添加虚拟商品!',
                        type: 'error'
                    });
                    return;
                }
                this.$refs[form].validate((valid) => {
                    if (valid && this.checkPaymentPrice()) {
                        this.formDisabled = true;
                        if (this.buyorderExpense.buyorderExpenseId&&this.buyorderExpense.buyorderExpenseId!=0) {
                            let updateData = JSON.parse(JSON.stringify(this.buyorderExpense));
                            updateData.buyorderExpenseItemDtos = [];
                            updateData.buyorderExpenseDetailDto = this.buyorderExpense;
                            updateData.buyorderExpenseGoodsList.forEach(function (item, index) {
                                updateData.buyorderExpenseItemDtos.push(
                                    {
                                        ...item,
                                        buyorderExpenseItemDetailDto: item
                                    }
                                )
                            })
                            update(updateData).then(res => {
                                if (res.data.code === 0) {
                                    openTab("采购费用订单详情", '/buyorderExpense/details.do?buyorderExpenseId=' + this.buyorderExpense.buyorderExpenseId)
                                    this.closeThis()
                                } else {
                                    this.$message({
                                        showClose:true,
                                        duration: 3000,
                                        message: res.data.message,
                                        type: 'error',
                                    });
                                }
                            })
                        } else {
                            let addData = JSON.parse(JSON.stringify(this.buyorderExpense));
                            addData.buyorderExpenseItemDtos = [];
                            addData.buyorderExpenseDetailDto = this.buyorderExpense;
                            addData.buyorderExpenseGoodsList.forEach(function (item, index) {
                                addData.buyorderExpenseItemDtos.push(
                                    {
                                        ...item,
                                        buyorderExpenseItemDetailDto: item
                                    }
                                )
                            })
                            add(addData).then(res => {
                                if (res.data.code === 0) {
                                    let buyorderExpenseId = res.data.data;
                                    openTab("采购订单详情详情", '/buyorderExpense/details.do?buyorderExpenseId=' + buyorderExpenseId)
                                    this.closeThis()
                                } else {
                                    this.$message({
                                        showClose:true,
                                        duration: 3000,
                                        message: res.data.message,
                                        type: 'error',
                                    });
                                }
                            })
                        }

                    }
                })
            },

            // 选择联系人
            traderConcatSelect(item, row) {
                this.buyorderExpense.traderContactId = item.traderContactId;
                this.buyorderExpense.traderContactName = item.name;
                this.buyorderExpense.traderContactMobile = item.mobile;
                this.buyorderExpense.traderContactTelephone = item.telephone;
                this.buyorderExpense.traderContactNameShow = item.name + "/" + item.mobile + "/" + item.telephone;

                row.traderContactStr = this.buyorderExpense.traderContactNameShow;
            },

            // 清空联系人
            traderContactClear(row) {
                this.buyorderExpense.traderContactId = '';
                this.buyorderExpense.traderContactName = '';
                this.buyorderExpense.traderContactMobile = '';
                this.buyorderExpense.traderContactTelephone = '';
                this.buyorderExpense.traderContactNameShow = '';

                row.traderContactStr = this.buyorderExpense.traderContactNameShow;
            },

            traderAddressSelect(item, row) {
                this.buyorderExpense.traderAddressId = item.traderAddressId;
                this.buyorderExpense.traderArea = item.area;
                this.buyorderExpense.traderAddress = item.address;
                this.buyorderExpense.traderAddressStr = item.area + " " + item.address;

                row.traderAddressStr = this.buyorderExpense.traderAddressStr;
            },

            // 清空地址
            traderAddressClear(row) {
                this.buyorderExpense.traderAddressId = '';
                this.buyorderExpense.traderArea = '';
                this.buyorderExpense.traderAddress = '';
                this.buyorderExpense.traderAddressStr = '';

                row.traderAddressStr = this.buyorderExpense.traderAddressStr;
            },

            // 更改类型
            independentChange() {
                if (this.baseBuyOrder) {
                    this.$message(' 已采用【基于采购单】创建采购费用订单');
                } else {
                    this.$message(' 已采用【独立创建】创建采购费用订单');
                }

                this.buyorderExpense.buyorderId = 0;
                this.buyorderExpense.buyorderNo = '';
                this.buyorderExpense.traderId = '';
                this.buyorderExpense.traderName = '';
                this.buyorderExpense.traderContactId = '';
                this.buyorderExpense.traderContactName = '';
                this.buyorderExpense.traderContactMobile = '';
                this.buyorderExpense.traderContactTelephone = '';
                this.buyorderExpense.traderAddressId = '';
                this.buyorderExpense.traderArea = '';
                this.buyorderExpense.traderAddress = '';
                this.buyorderExpense.traderComments = '';
                this.buyorder = [];

                this.buyorderTraderSupplierInfo = [];
                if (vm.buyorderExpense.traderId != '') {
                    getTraderSupplierInfoById({traderId: vm.buyorderExpense.traderId}).then((result) => {
                        vm.traderSupplierInfo = result.data.data;
                        vm.buyorderExpense.periodDay = vm.traderSupplierInfo.periodDay;
                    });
                }


            },

            //支付金额总额与总金额不符，请验证
            checkPaymentPrice() {
               if (Number(this.buyorderExpense.totalAmount).toFixed(2) !==
                    (Number(this.buyorderExpense.prepaidAmount) + Number(this.buyorderExpense.accountPeriodAmount) + Number(this.buyorderExpense.retainageAmount)).toFixed(2)) {
                    this.checkPaymentFlag = true;
                    return false;
                }
                this.checkPaymentFlag = false;
                return true;
            },

            //处理商品数量输入
            handleInputNumberValue(row) {
                row.num = this.limitInputNumber(row.num);
            },

            //处理销售关联模块商品数量输入
            handleSaleOrderNumberValue(row) {
                row.buyNum = this.limitInputNumber(row.buyNum);
            },

            //处理商品金额输入
            handleInputAmountValue(row) {
                row.price = this.limitInputNumberDecimal(row.price);
            },

            //限制数字
            limitInputNumber(value) {
                if (value != undefined) {
                    value = value.replace(/[^0-9]/g, '');// 只能输入数字
                    value = value.replace(/^(0+)|[^\d]+/g, '');// 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                }
                return value;
            },

            //限制数字和小数
            limitInputNumberDecimal(value) {
                if (value != undefined) {
                    value = value.replace(/[^\d.]/g, '') // 只能输入数字和.
                    value = value.replace(/^\./g, '')  //第一个字符不能是.
                    value = value.replace(/\.{2,}/g, '.') // 不能连续输入.
                    value = value.replace(/(\.\d+)\./g, '$1') // .后面不能再输入.
                    value = value.replace(/^0+(\d)/, '$1') // 第一位0开头，0后面为数字，则过滤掉，取后面的数字
                    value = value.replace(/(\d{10})\d*/, '$1') // 最多保留15位整数
                    value = value.replace(/(\.\d{2})\d*/, '$1')// 最多保留2位小数
                }
                return value;
            },

            //商品数量changed
            buyNumChanged(row) {
                this.computedTotalAmount(row);
            },

            // 销售订单关联模块采购数量变化
            saleOrderBuyNumChanged() {
                this.buyorderExpense.buyorderExpenseGoodsList.map(item => {
                    if (item.buyOrderSaleOrderGoodsDetailDtos != null || item.buyOrderSaleOrderGoodsDetailDtos > 0) {
                        item.num = _.sumBy(item.buyOrderSaleOrderGoodsDetailDtos, function (o) {
                            return Number(o.buyNum || 0);
                        })
                    }
                    item.num = Number(item.num)
                    this.buyPriceChanged(item);
                })
            },

            //商品价格changed
            buyPriceChanged(row) {
                if (row.price) {
                    row.price = Number(row.price);
                    this.computedTotalAmount(row);
                    row.price = parseFloat(row.price).toFixed(2);
                }
            },

            //计算每个商品总额
            computedTotalAmount(obj) {
                if (obj.num !== undefined && obj.price !== undefined) {
                    obj.oneBuyorderExpenseGoodsAmount = Number(obj.num) * Number(obj.price)
                }
            },

            //合计行
            getSummaries(param) {
                const {columns, data} = param;
                const sums = [];
                console.log(columns)
                columns.forEach((column, index) => {
                    if (index === 0) {
                        sums[index] = '合计';
                        return;
                    }
                    const values = data.map(item => Number(item[column.property]));
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                        if (index === 5) {
                            sums[index] = '总件数：' + sums[index];
                        }
                        if (index === 7) {
                            this.buyorderExpense.totalAmount = parseFloat(sums[index]).toFixed(2);
                            sums[index] = '总金额：' + this.buyorderExpense.totalAmount;
                        }
                    }
                });
                return sums;
            },

            //计算付款信息模块付款和账期金额
            computedAmount() {
                if (this.buyorderExpense.totalAmount !== 0) {
                    let prePayRate = 0
                    switch (this.buyorderExpense.paymentType) {
                        case 419:
                            prePayRate = 1
                            break;
                        case 3175:
                            prePayRate = 0.9
                            break;
                        case 420:
                            prePayRate = 0.8
                            break;
                        case 421:
                            prePayRate = 0.5
                            break;
                        case 422:
                            prePayRate = 0.3
                            break;
                        case 3174:
                            prePayRate = 0.1
                            break;
                        case 423:
                            prePayRate = 0
                            break;
                    }

                    if (this.buyorderExpense.paymentType !== 424) {
                        this.buyorderExpense.prepaidAmount = (this.buyorderExpense.totalAmount * prePayRate).toFixed(2);
                        this.buyorderExpense.accountPeriodAmount = (this.buyorderExpense.totalAmount * (1 - prePayRate)).toFixed(2);
                    }
                }
            },

            //改变付款方式
            paymentTypeChanged() {
                if (this.buyorderExpense.paymentType === 424) {
                    this.buyorderExpense.prepaidAmount = parseFloat('0').toFixed(2);
                    this.buyorderExpense.accountPeriodAmount = parseFloat('0').toFixed(2);
                    this.buyorderExpense.retainageAmount = parseFloat('0').toFixed(2);
                    this.buyorderExpense.retainageAmountMonth = 1;
                } else {
                    this.$refs['buyorderExpenseForm'].clearValidate(['prepaidAmount', 'accountPeriodAmount']);
                    this.buyorderExpense.retainageAmount = parseFloat('0').toFixed(2);
                    this.buyorderExpense.retainageAmountMonth = null;
                    this.computedAmount();
                }
                this.checkPaymentFlag = false;

            },

            //限制付款金额
            limitPrepaidAmountInput(value) {
                this.buyorderExpense.prepaidAmount = this.limitInputNumberDecimal(value);
            },

            //限制账期支付
            limitAccountPeriodAmountInput(value) {
                this.buyorderExpense.accountPeriodAmount = this.limitInputNumberDecimal(value);
            },

            //限制尾款
            limitRetainageAmountInput(value) {
                this.buyorderExpense.retainageAmount = this.limitInputNumberDecimal(value);
            },

            //限制尾款期限
            limitRetainageAmountMonthInput(value) {
                this.buyorderExpense.retainageAmountMonth = this.limitInputNumber(value);
            },

            //金额输入结束
            amountInputChanged(value) {
                if (this.buyorderExpense.prepaidAmount) {
                    this.buyorderExpense.prepaidAmount = parseFloat(this.buyorderExpense.prepaidAmount).toFixed(2);
                }
                if (this.buyorderExpense.accountPeriodAmount) {
                    this.buyorderExpense.accountPeriodAmount = parseFloat(this.buyorderExpense.accountPeriodAmount).toFixed(2);
                }
                if (this.buyorderExpense.retainageAmount) {
                    this.buyorderExpense.retainageAmount = parseFloat(this.buyorderExpense.retainageAmount).toFixed(2);
                }
            },

            //删除费用单商品
            handleDelete(index, row) {
                this.buyorderExpense.buyorderExpenseGoodsList.splice(index, 1)
                this.renderTableAgain = !this.renderTableAgain;
            },

            //关闭iframe
            closeThis() {
                window.parent.closableTab.close($(window.parent.document).find('[role=presentation].active .glyphicon.small'), window.parent.closableTab.resizeMove);
            },

            toggleRowExpansion() {
                this.isExpand = !this.isExpand;
                if (this.isExpand) {
                    this.getExpends()
                } else {
                    this.expends = []
                }
            },


            getRowClass(row) {
                if (row.row.buyOrderSaleOrderGoodsDetailDtos === null || row.row.buyOrderSaleOrderGoodsDetailDtos === undefined  || row.row.buyOrderSaleOrderGoodsDetailDtos.length === 0) {
                    return 'row-expand-cover'
                }
            },
            // 获取扩展行展开信息
            getExpends() {
                let ids = []
                this.buyorderExpense.buyorderExpenseGoodsList.map(item=>{
                    if (item.buyOrderSaleOrderGoodsDetailDtos !== null
                        && item.buyOrderSaleOrderGoodsDetailDtos !== undefined
                        && item.buyOrderSaleOrderGoodsDetailDtos.length > 0) {
                        ids.push(item.buyorderExpenseItemId)
                    }
                })
                this.expends = ids;
            },
            getRowKeys(row) {
                return row.buyorderExpenseItemId
            },

            //添加虚拟商品
            chooseExpenseSku() {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ['80%', '80%'],
                    title: '添加虚拟商品',
                    content: '/ezadmin/list/list-expenseSkuList?SOURCE='+this.source
                });
            },

            //添加采购单
            chooseValidBuyorder() {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ['80%', '80%'],
                    title: '选择采购订单',
                    content: '/ezadmin/list/list-validBuyorderList?default_empty=1'
                });
            },

            //选择供应商弹框
            chooseValidTrader() {
                layer.open({
                    type: 2,
                    shadeClose: false,
                    area: ['80%', '80%'],
                    title: '选择供应商',
                    content: '/order/buyorder/getSupplierByName.do?supplierName=请输入名称&callbackFuntion=callbackFuntionVue&pop=pop'
                });
            }
        }
    });



    //选择商品后回调
    function ez_callback_chooseSku(ids, lines) {
        let json = JSON.parse(lines)[0];
        //查询是否已有相同虚拟商品信息
        let existFlag = false;
        if (vm.buyorderExpense.buyorderExpenseGoodsList.length > 0) {
            vm.buyorderExpense.buyorderExpenseGoodsList.find(item => {
                if (item.goodsId === Number(json.SKU_ID)) {
                    existFlag = true;
                    return true;
                }
            })
        }

        if (existFlag) {
            layer.alert("已有相同虚拟商品信息，不允许重复添加！");
        } else {
            vm.buyorderExpense.buyorderExpenseGoodsList.push(
                {
                    goodsId: Number(json.SKU_ID),
                    sku: json.SKU_NO,
                    goodsName: json.SHOW_NAME,
                    expenseCategoryId: json.COST_CATEGORY_ID,
                    expenseCategoryName: json.CATEGORY_NAME,
                    haveStockManage: json.HAVE_STOCK_MANAGE,
                    oneBuyorderExpenseGoodsAmount: 0.00,
                    source: 0,
                    num: '',
                    price: 0,
                }
            );
            layer.closeAll();
        }
    }




    //选择供应商页面回显
    function callbackFuntionVue(data) {
        vm.buyorderExpense.traderId = data.traderId;
        // 供应商联系人
        getTraderConcatData(data.traderId).then(res => {
            vm.traderConcatDatas = res.data.data.list;

        });
        // 供应商地址
        getTraderAddressData(data.traderId).then(res => {
            vm.traderAddressDatas = res.data.data;

        });
        vm.buyorderExpense.traderName = data.traderSupplierName;
        vm.buyorderExpense.traderContactId = '';
        vm.buyorderExpense.traderContactName = '';
        vm.buyorderExpense.traderContactMobile = '';
        vm.buyorderExpense.traderContactTelephone = '';
        vm.buyorderExpense.traderAddressId = '';
        vm.buyorderExpense.traderArea = '';
        vm.buyorderExpense.traderAddress = '';
        vm.buyorderExpense.traderComments = data.traderComments;
        vm.buyorder = [];
        vm.buyorder.push(
            {
                buyorderNo: '',
                traderName: '',
                createName: '',
                buyDepartmentName: '',
                addTime: '',
            }
        );

        vm.buyorderTraderSupplierInfo = [];
        vm.buyorderTraderSupplierInfo.push(
            {
                traderName: vm.buyorderExpense.traderName,
                traderContactStr: '',
                traderAddressStr: '',
                traderComments: vm.buyorderExpense.traderComments,

            }
        );

        vm.traderSupplierInfo = {periodAmount: data.periodAmount, periodDay: data.periodDay};
        vm.buyorderExpense.periodDay = vm.traderSupplierInfo.periodDay;
        // getTraderSupplierInfoById({traderId: vm.buyorderExpense.traderId}).then((result) => {
        //     vm.traderSupplierInfo = result.data.data;
        //     vm.buyorderExpense.periodDay = vm.traderSupplierInfo.periodDay;
        // });

        layer.closeAll();

    }

    //选择采购单后回调
    function ez_callback_chooseBuyorder(ids, lines) {
        let json = JSON.parse(lines)[0];
        vm.buyorderExpense.buyorderId = json.BUYORDER_ID;
        vm.buyorderExpense.buyorderNo = json.BUYORDER_NO;
        vm.buyorderExpense.traderId = json.TRADER_ID;
        vm.buyorderExpense.traderName = json.TRADER_NAME;
        vm.buyorderExpense.traderContactId = json.TRADER_CONTACT_ID;
        vm.buyorderExpense.traderContactName = json.TRADER_CONTACT_NAME;
        vm.buyorderExpense.traderContactMobile = json.TRADER_CONTACT_MOBILE;
        vm.buyorderExpense.traderContactTelephone = json.TRADER_CONTACT_TELEPHONE;
        vm.buyorderExpense.traderAddressId = json.TRADER_ADDRESS_ID;
        vm.buyorderExpense.traderArea = json.TRADER_AREA;
        vm.buyorderExpense.traderAddress = json.TRADER_ADDRESS;
        vm.buyorderExpense.traderComments = json.TRADER_COMMENTS;
        vm.buyorder = [];
        vm.buyorder.push(
            {
                buyorderNo: json.BUYORDER_NO,
                traderName: json.TRADER_NAME,
                createName: json.CREATOR_NAME,
                buyDepartmentName: json.ORG_NAME,
                addTime: json.ADD_TIME,
            }
        );

        vm.buyorderTraderSupplierInfo = [];
        vm.buyorderTraderSupplierInfo.push(
            {
                traderName: vm.buyorderExpense.traderName,
                traderContactStr: vm.buyorderExpense.traderContactName + "/" + vm.buyorderExpense.traderContactMobile + "/" + vm.buyorderExpense.traderContactTelephone,
                traderAddressStr: vm.buyorderExpense.traderArea + " " + vm.buyorderExpense.traderAddress,
                traderComments: vm.buyorderExpense.traderComments,

            }
        );

        getTraderSupplierInfoById({traderId: vm.buyorderExpense.traderId}).then((result) => {
            vm.traderSupplierInfo = result.data.data;
            vm.buyorderExpense.periodDay = vm.traderSupplierInfo.periodDay;
        });

        layer.closeAll();
    }


</script>

<style>
    .el-card .el-card__header {
        padding: 5px 20px;
        background-color: #f2f2f2;
    }

    .el-radio-button__inner {
        width: 225px;
    }

    .el-card .el-card__header {
        padding: 7px 10px;
        background-color: #f2f2f2;
        font-size: 16px;
        height: 35px;
        font-weight: 700;
    }

    .row-expand-cover td .el-table__expand-icon{ visibility: hidden !important; }


</style>
