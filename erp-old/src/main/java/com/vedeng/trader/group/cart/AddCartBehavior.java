package com.vedeng.trader.group.cart;

import com.vedeng.track.api.dto.business.req.BusinessReqDto;
import com.vedeng.trader.group.BaseBehavior;
import com.vedeng.trader.group.visit.TrackBaseBehvior;

import java.util.BitSet;
/**
 * <b>Description:</b>最近有加购<br>
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class AddCartBehavior extends TrackBaseBehvior {
    @Override
    public BitSet calculate() {

        BusinessReqDto reqDto=new BusinessReqDto();
        reqDto.setDayType(getPeriod());
        BitSet middleResult=getAddCartData(reqDto);
        BitSet result=getBaseTraderSet();
        result.and(middleResult);
        return result;
    }
}
