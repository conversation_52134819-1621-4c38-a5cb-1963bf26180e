package com.vedeng.erp.buyorder.service;

import com.vedeng.erp.aftersale.dto.AfterBuyorderInvoiceGoodsDto;
import com.vedeng.erp.aftersale.dto.AfterSalesDto;
import com.vedeng.finance.model.Invoice;

import java.math.BigDecimal;
import java.util.List;

/**
 * 采购售后service
 * <AUTHOR>
 */
public interface NewAfterBuyorderService {

    /**
     * 点击无需退票
     * <AUTHOR>
     * @param afterSalesInvoiceId 售后退票ID
     * @param afterSalesId 售后单ID
     * @return ResultInfo resultInfo
     */
    void withOutRetureTicket(Integer afterSalesInvoiceId, Integer afterSalesId);

    /**
     * 点击申请冲销
     * <AUTHOR>
     * @param invoiceId 发票记录ID
     * @param afterSalesId 售后单ID
     * @return ResultInfo resultInfo
     */
    void applyCoverTicket(Integer invoiceId,Integer afterSalesId);

    /**
     * 保存冲销审核信息
     * <AUTHOR>
     * @param invoiceReversalId 冲销申请ID
     * @param isPass 是否通过
     * @param auditComment 审核备注
     * @return ResultInfo resultInfo
     */
    void verifyCoverSave(Integer invoiceReversalId,boolean isPass,String auditComment);

    /**
     * 根据采购单id查询可做仅退票采购单商品信息（SKU+发票号+发票代码）纬度
     * <AUTHOR>
     * @param buyorderId
     * @return
     */
    List<AfterBuyorderInvoiceGoodsDto> queryAllCanBackInvoiceGoods(Integer buyorderId);

    /**
     * 根据发票号，发票代码校验可录票金额是否满足条件
     * <AUTHOR>
     * @param invoiceNo
     * @param invocieCode
     * @param amount
     * @return
     */
    boolean verifyHxInvoiceLeftAmount(String invoiceNo, String invocieCode, BigDecimal amount);

    /**
     * 查询采购售后仅退票基础信息
     * <AUTHOR>
     * @param afterSalesId
     * @return
     */
    AfterSalesDto viewTpDetail(Integer afterSalesId);

    /**
     * 关闭采购仅退票售后单
     * @param afterSalesId
     * @param buyorderId
     */
    void closeTpAfterSales(Integer afterSalesId,Integer buyorderId);

    /**
     * 保存仅退票信息
     * <AUTHOR>
     * @param invoice
     * @param detailGoodsIdList
     * @param invoiceAmountList
     * @param invoicePriceList
     * @param invoiceNumList
     */
    void saveTpReturnInvoice(Invoice invoice, List<Integer> detailGoodsIdList, List<BigDecimal> invoiceAmountList,
                             List<BigDecimal> invoicePriceList, List<BigDecimal> invoiceNumList);
}
