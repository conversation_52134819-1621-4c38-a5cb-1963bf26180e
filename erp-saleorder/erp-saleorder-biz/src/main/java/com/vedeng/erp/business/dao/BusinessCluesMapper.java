package com.vedeng.erp.business.dao;

import com.vedeng.erp.business.domain.entity.BusinessClues;
import com.vedeng.erp.business.domain.vo.BusinessCluesVo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【T_BUSINESS_CLUES(线索表)】的数据库操作Mapper
 * @createDate 2022-03-10 17:01:59
 * @Entity com.vedeng.erp.business.domain.entity.BusinessClues
 */
public interface BusinessCluesMapper {

    int deleteByPrimaryKey(Integer id);

    int insert(BusinessClues record);

    int insertSelective(BusinessClues record);

    BusinessClues selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(BusinessClues record);

    int updateByPrimaryKey(BusinessClues record);

    /**
     * 查询线索列表
     */
    List<BusinessCluesVo> getBusinessClueslistpage(Map<String, Object> map);

}
