<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.CategoryDepartmentMapper">
    <sql id="Base_Column_List">
        ID,
        CATEGORY_ID,
        DEPARTMENT_ID,
        IS_DELETED,
        CREATOR,
        UPDATER,
        ADD_TIME,
        MOD_TIME
    </sql>
    <resultMap id="BaseResultMap" type="com.vedeng.goods.model.CategoryDepartmentDo">
        <id column="ID" jdbcType="INTEGER" property="id"/>
        <result column="CATEGORY_ID" jdbcType="INTEGER" property="categoryId"/>
        <result column="DEPARTMENT_ID" jdbcType="INTEGER" property="departmentId"/>
        <result column="IS_DELETED" jdbcType="BIT" property="isDeleted"/>
        <result column="CREATOR" jdbcType="INTEGER" property="creator"/>
        <result column="UPDATER" jdbcType="INTEGER" property="updater"/>
        <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime"/>
        <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime"/>
    </resultMap>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
        delete
        from V_CATEGORY_DEPARTMENT
        where ID = #{id,jdbcType=INTEGER}
    </delete>
    <insert id="insert" parameterType="com.vedeng.goods.model.CategoryDepartmentDo">
        insert into V_CATEGORY_DEPARTMENT (ID, CATEGORY_ID, DEPARTMENT_ID,
                                           IS_DELETED, CREATOR, UPDATER,
                                           ADD_TIME, MOD_TIME)
        values (#{id,jdbcType=INTEGER}, #{categoryId,jdbcType=INTEGER}, #{departmentId,jdbcType=INTEGER},
                #{isDeleted,jdbcType=BIT}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER},
                #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP})
    </insert>
    <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.CategoryDepartmentDo">
        update V_CATEGORY_DEPARTMENT
        set CATEGORY_ID   = #{categoryId,jdbcType=INTEGER},
            DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER},
            IS_DELETED    = #{isDeleted,jdbcType=BIT},
            CREATOR       = #{creator,jdbcType=INTEGER},
            UPDATER       = #{updater,jdbcType=INTEGER},
            ADD_TIME      = #{addTime,jdbcType=TIMESTAMP},
            MOD_TIME      = #{modTime,jdbcType=TIMESTAMP}
        where ID = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select ID,
               CATEGORY_ID,
               DEPARTMENT_ID,
               IS_DELETED,
               CREATOR,
               UPDATER,
               ADD_TIME,
               MOD_TIME
        from V_CATEGORY_DEPARTMENT
        where ID = #{id,jdbcType=INTEGER}
    </select>
    <select id="selectAll" resultMap="BaseResultMap">
        select ID,
               CATEGORY_ID,
               DEPARTMENT_ID,
               IS_DELETED,
               CREATOR,
               UPDATER,
               ADD_TIME,
               MOD_TIME
        from V_CATEGORY_DEPARTMENT
    </select>
    <select id="listByCategoryId" resultMap="BaseResultMap">
        select ID, CATEGORY_ID, DEPARTMENT_ID, IS_DELETED, CREATOR, UPDATER, ADD_TIME, MOD_TIME
        from V_CATEGORY_DEPARTMENT
        where CATEGORY_ID = #{thirdLevelCategoryId, jdbcType=INTEGER}
        <if test="deleted!=null">
            and IS_DELETED= #{deleted, jdbcType=BIT}
        </if>
    </select>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO V_CATEGORY_DEPARTMENT (ID, CATEGORY_ID, DEPARTMENT_ID, IS_DELETED, CREATOR, UPDATER, ADD_TIME,
        MOD_TIME)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.id,jdbcType=INTEGER}, #{item.categoryId,jdbcType=INTEGER}, #{item.departmentId,jdbcType=INTEGER},
            #{item.isDeleted,jdbcType=BIT}, #{item.creator,jdbcType=INTEGER}, #{item.updater,jdbcType=INTEGER},
            #{item.addTime,jdbcType=TIMESTAMP}, #{item.modTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <update id="batchUpdate" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" separator=";">
            update V_CATEGORY_DEPARTMENT
            set CATEGORY_ID = #{item.categoryId,jdbcType=INTEGER},
            DEPARTMENT_ID = #{item.departmentId,jdbcType=INTEGER},
            IS_DELETED = #{item.isDeleted,jdbcType=BIT},
            CREATOR = #{item.creator,jdbcType=INTEGER},
            UPDATER = #{item.updater,jdbcType=INTEGER},
            ADD_TIME = #{item.addTime,jdbcType=TIMESTAMP},
            MOD_TIME = #{item.modTime,jdbcType=TIMESTAMP}
            where ID = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>
    <select id="countGoodsAssociatedWithDepartment" resultType="java.lang.Integer">
        select count(sku.SKU_ID)
        from V_CATEGORY_DEPARTMENT dep
        left join V_BASE_CATEGORY c on dep.CATEGORY_ID=c.BASE_CATEGORY_ID
        left join V_CORE_SPU spu on c.BASE_CATEGORY_ID=spu.CATEGORY_ID
        left join V_CORE_SKU sku on spu.SPU_ID=sku.SPU_ID and sku.CHECK_STATUS <![CDATA[ != ]]> 4
        where dep.DEPARTMENT_ID = #{departmentId,jdbcType=INTEGER} and dep.IS_DELETED = #{deleted,jdbcType=TINYINT}
    </select>

    <select id="findByCategoryIdAndDepartmentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from V_CATEGORY_DEPARTMENT
        where CATEGORY_ID=#{categoryId,jdbcType=INTEGER} and DEPARTMENT_ID=#{departmentId,jdbcType=INTEGER}
    </select>

</mapper>