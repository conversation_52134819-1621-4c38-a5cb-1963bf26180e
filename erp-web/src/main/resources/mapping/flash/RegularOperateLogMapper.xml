<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.flash.dao.RegularOperateLogMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.flash.model.RegularOperateLog" >
    <id column="LOG_ID" property="logId" jdbcType="INTEGER" />
    <result column="PARENT_ID" property="parentId" jdbcType="INTEGER" />
    <result column="REGULAR_ID" property="regularId" jdbcType="INTEGER" />
    <result column="LOG_TYPE" property="logType" jdbcType="TINYINT" />
    <result column="SKU_ID" property="skuId" jdbcType="INTEGER" />
    <result column="SKU_NO" property="skuNo" jdbcType="VARCHAR" />
    <result column="SKU_NAME" property="skuName" jdbcType="VARCHAR" />
    <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR" />
    <result column="SPU_TYPE" property="spuType" jdbcType="VARCHAR" />
    <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR" />
    <result column="SPEC" property="spec" jdbcType="VARCHAR" />
    <result column="UNIT" property="unit" jdbcType="VARCHAR" />
    <result column="COST" property="cost" jdbcType="VARCHAR" />
    <result column="AMOUNT" property="amount" jdbcType="DECIMAL" />
    <result column="YXG_AMOUNT" property="yxgAmount" jdbcType="DECIMAL" />
    <result column="THREE_MONTH_AMOUNT" property="threeMonthAmount" jdbcType="DECIMAL" />
    <result column="YXG_THREE_MONTH_AMOUNT" property="yxgThreeMonthAmount" jdbcType="DECIMAL" />
    <result column="ONE_MONTH_AMOUNT" property="oneMonthAmount" jdbcType="DECIMAL" />
    <result column="YXG_ONE_MONTH_AMOUNT" property="yxgOneMonthAmount" jdbcType="DECIMAL" />
    <result column="OWER_USER_ID" property="owerUserId" jdbcType="INTEGER" />
    <result column="OWER_USER" property="owerUser" jdbcType="VARCHAR" />
    <result column="STOCK" property="stock" jdbcType="INTEGER" />
    <result column="INTRANSIT_STOCK" property="intransitStock" jdbcType="INTEGER" />
    <result column="ORDER_STOCK" property="orderStock" jdbcType="INTEGER" />
    <result column="FORECAST_SAFE_STOCK" property="forecastSafeStock" jdbcType="INTEGER" />
    <result column="SAFE_STOCK" property="safeStock" jdbcType="INTEGER" />
    <result column="STOCK_WARN" property="stockWarn" jdbcType="VARCHAR" />
    <result column="SUPPLEMENT_STOCK" property="supplementStock" jdbcType="INTEGER" />
    <result column="RECEIVE_TIMES" property="receiveTimes" jdbcType="INTEGER" />
    <result column="OUT_STOCK_TIMES" property="outStockTimes" jdbcType="INTEGER" />
    <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT" />
    <result column="OPERATE_USER_ID" property="operateUserId" jdbcType="INTEGER" />
    <result column="OPERATE_USER_NAME" property="operateUserName" jdbcType="VARCHAR" />
    <result column="OPERATE_TIME" property="operateTime" jdbcType="BIGINT" />
    <result column="OPERATE_REASON" property="operateReason" jdbcType="VARCHAR" />
    <result column="SALEORDER_ID" property="saleorderId" jdbcType="VARCHAR" />
    <result column="LAST_ADD_TIME" property="lastAddTime" jdbcType="BIGINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    LOG_ID, PARENT_ID, REGULAR_ID, LOG_TYPE, SKU_ID, SKU_NO, SKU_NAME, BRAND_NAME, SPU_TYPE,
    CATEGORY_NAME, SPEC, UNIT, COST, AMOUNT, YXG_AMOUNT, THREE_MONTH_AMOUNT, YXG_THREE_MONTH_AMOUNT,
    ONE_MONTH_AMOUNT, YXG_ONE_MONTH_AMOUNT, OWER_USER_ID, OWER_USER, STOCK, INTRANSIT_STOCK,
    ORDER_STOCK, FORECAST_SAFE_STOCK, SAFE_STOCK, STOCK_WARN, SUPPLEMENT_STOCK, RECEIVE_TIMES,
    OUT_STOCK_TIMES, OPERATE_TYPE, OPERATE_USER_ID, OPERATE_USER_NAME, OPERATE_TIME,
    OPERATE_REASON, SALEORDER_ID,LAST_ADD_TIME
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >
    select
    <include refid="Base_Column_List" />
    from T_REGULAR_OPERATE_LOG
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >
    delete from T_REGULAR_OPERATE_LOG
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.flash.model.RegularOperateLog" keyColumn="LOG_ID" useGeneratedKeys="true" keyProperty="logId">
    insert into T_REGULAR_OPERATE_LOG (PARENT_ID, REGULAR_ID,
      LOG_TYPE, SKU_ID, SKU_NO,
      SKU_NAME, BRAND_NAME, SPU_TYPE,
      CATEGORY_NAME, SPEC, UNIT,
      COST, AMOUNT, YXG_AMOUNT,
      THREE_MONTH_AMOUNT, YXG_THREE_MONTH_AMOUNT,
      ONE_MONTH_AMOUNT, YXG_ONE_MONTH_AMOUNT, OWER_USER_ID,
      OWER_USER, STOCK, INTRANSIT_STOCK,
      ORDER_STOCK, FORECAST_SAFE_STOCK, SAFE_STOCK,
      STOCK_WARN, SUPPLEMENT_STOCK, RECEIVE_TIMES,
      OUT_STOCK_TIMES, OPERATE_TYPE, OPERATE_USER_ID,
      OPERATE_USER_NAME, OPERATE_TIME, OPERATE_REASON,SALEORDER_ID,LAST_ADD_TIME
      )
    values (#{parentId,jdbcType=INTEGER}, #{regularId,jdbcType=INTEGER},
      #{logType,jdbcType=TINYINT}, #{skuId,jdbcType=INTEGER}, #{skuNo,jdbcType=VARCHAR},
      #{skuName,jdbcType=VARCHAR}, #{brandName,jdbcType=VARCHAR}, #{spuType,jdbcType=VARCHAR},
      #{categoryName,jdbcType=VARCHAR}, #{spec,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR},
      #{cost,jdbcType=VARCHAR}, #{amount,jdbcType=DECIMAL}, #{yxgAmount,jdbcType=DECIMAL},
      #{threeMonthAmount,jdbcType=DECIMAL}, #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      #{oneMonthAmount,jdbcType=DECIMAL}, #{yxgOneMonthAmount,jdbcType=DECIMAL}, #{owerUserId,jdbcType=INTEGER},
      #{owerUser,jdbcType=VARCHAR}, #{stock,jdbcType=INTEGER}, #{intransitStock,jdbcType=INTEGER},
      #{orderStock,jdbcType=INTEGER}, #{forecastSafeStock,jdbcType=INTEGER}, #{safeStock,jdbcType=INTEGER},
      #{stockWarn,jdbcType=VARCHAR}, #{supplementStock,jdbcType=INTEGER}, #{receiveTimes,jdbcType=INTEGER},
      #{outStockTimes,jdbcType=INTEGER}, #{operateType,jdbcType=TINYINT}, #{operateUserId,jdbcType=INTEGER},
      #{operateUserName,jdbcType=VARCHAR}, #{operateTime,jdbcType=BIGINT}, #{operateReason,jdbcType=VARCHAR},
      #{saleorderId,jdbcType=INTEGER}, #{lastAddTime,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.flash.model.RegularOperateLog" >
    insert into T_REGULAR_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        LOG_ID,
      </if>
      <if test="parentId != null" >
        PARENT_ID,
      </if>
      <if test="regularId != null" >
        REGULAR_ID,
      </if>
      <if test="logType != null" >
        LOG_TYPE,
      </if>
      <if test="skuId != null" >
        SKU_ID,
      </if>
      <if test="skuNo != null" >
        SKU_NO,
      </if>
      <if test="skuName != null" >
        SKU_NAME,
      </if>
      <if test="brandName != null" >
        BRAND_NAME,
      </if>
      <if test="spuType != null" >
        SPU_TYPE,
      </if>
      <if test="categoryName != null" >
        CATEGORY_NAME,
      </if>
      <if test="spec != null" >
        SPEC,
      </if>
      <if test="unit != null" >
        UNIT,
      </if>
      <if test="cost != null" >
        COST,
      </if>
      <if test="amount != null" >
        AMOUNT,
      </if>
      <if test="yxgAmount != null" >
        YXG_AMOUNT,
      </if>
      <if test="threeMonthAmount != null" >
        THREE_MONTH_AMOUNT,
      </if>
      <if test="yxgThreeMonthAmount != null" >
        YXG_THREE_MONTH_AMOUNT,
      </if>
      <if test="oneMonthAmount != null" >
        ONE_MONTH_AMOUNT,
      </if>
      <if test="yxgOneMonthAmount != null" >
        YXG_ONE_MONTH_AMOUNT,
      </if>
      <if test="owerUserId != null" >
        OWER_USER_ID,
      </if>
      <if test="owerUser != null" >
        OWER_USER,
      </if>
      <if test="stock != null" >
        STOCK,
      </if>
      <if test="intransitStock != null" >
        INTRANSIT_STOCK,
      </if>
      <if test="orderStock != null" >
        ORDER_STOCK,
      </if>
      <if test="forecastSafeStock != null" >
        FORECAST_SAFE_STOCK,
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK,
      </if>
      <if test="stockWarn != null" >
        STOCK_WARN,
      </if>
      <if test="supplementStock != null" >
        SUPPLEMENT_STOCK,
      </if>
      <if test="receiveTimes != null" >
        RECEIVE_TIMES,
      </if>
      <if test="outStockTimes != null" >
        OUT_STOCK_TIMES,
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE,
      </if>
      <if test="operateUserId != null" >
        OPERATE_USER_ID,
      </if>
      <if test="operateUserName != null" >
        OPERATE_USER_NAME,
      </if>
      <if test="operateTime != null" >
        OPERATE_TIME,
      </if>
      <if test="operateReason != null" >
        OPERATE_REASON,
      </if>
      <if test="lastAddTime != null" >
        LAST_ADD_TIME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="logId != null" >
        #{logId,jdbcType=INTEGER},
      </if>
      <if test="parentId != null" >
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="regularId != null" >
        #{regularId,jdbcType=INTEGER},
      </if>
      <if test="logType != null" >
        #{logType,jdbcType=TINYINT},
      </if>
      <if test="skuId != null" >
        #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null" >
        #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="spuType != null" >
        #{spuType,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null" >
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null" >
        #{spec,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="cost != null" >
        #{cost,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        #{amount,jdbcType=DECIMAL},
      </if>
      <if test="yxgAmount != null" >
        #{yxgAmount,jdbcType=DECIMAL},
      </if>
      <if test="threeMonthAmount != null" >
        #{threeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgThreeMonthAmount != null" >
        #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="oneMonthAmount != null" >
        #{oneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgOneMonthAmount != null" >
        #{yxgOneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="owerUserId != null" >
        #{owerUserId,jdbcType=INTEGER},
      </if>
      <if test="owerUser != null" >
        #{owerUser,jdbcType=VARCHAR},
      </if>
      <if test="stock != null" >
        #{stock,jdbcType=INTEGER},
      </if>
      <if test="intransitStock != null" >
        #{intransitStock,jdbcType=INTEGER},
      </if>
      <if test="orderStock != null" >
        #{orderStock,jdbcType=INTEGER},
      </if>
      <if test="forecastSafeStock != null" >
        #{forecastSafeStock,jdbcType=INTEGER},
      </if>
      <if test="safeStock != null" >
        #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="stockWarn != null" >
        #{stockWarn,jdbcType=VARCHAR},
      </if>
      <if test="supplementStock != null" >
        #{supplementStock,jdbcType=INTEGER},
      </if>
      <if test="receiveTimes != null" >
        #{receiveTimes,jdbcType=INTEGER},
      </if>
      <if test="outStockTimes != null" >
        #{outStockTimes,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateUserId != null" >
        #{operateUserId,jdbcType=INTEGER},
      </if>
      <if test="operateUserName != null" >
        #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        #{operateTime,jdbcType=BIGINT},
      </if>
      <if test="operateReason != null" >
        #{operateReason,jdbcType=VARCHAR},
      </if>
      <if test="lastAddTime != null" >
        #{lastAddTime,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.flash.model.RegularOperateLog" >
    update T_REGULAR_OPERATE_LOG
    <set >
      <if test="parentId != null" >
        PARENT_ID = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="regularId != null" >
        REGULAR_ID = #{regularId,jdbcType=INTEGER},
      </if>
      <if test="logType != null" >
        LOG_TYPE = #{logType,jdbcType=TINYINT},
      </if>
      <if test="skuId != null" >
        SKU_ID = #{skuId,jdbcType=INTEGER},
      </if>
      <if test="skuNo != null" >
        SKU_NO = #{skuNo,jdbcType=VARCHAR},
      </if>
      <if test="skuName != null" >
        SKU_NAME = #{skuName,jdbcType=VARCHAR},
      </if>
      <if test="brandName != null" >
        BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      </if>
      <if test="spuType != null" >
        SPU_TYPE = #{spuType,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null" >
        CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="spec != null" >
        SPEC = #{spec,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        UNIT = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="cost != null" >
        COST = #{cost,jdbcType=VARCHAR},
      </if>
      <if test="amount != null" >
        AMOUNT = #{amount,jdbcType=DECIMAL},
      </if>
      <if test="yxgAmount != null" >
        YXG_AMOUNT = #{yxgAmount,jdbcType=DECIMAL},
      </if>
      <if test="threeMonthAmount != null" >
        THREE_MONTH_AMOUNT = #{threeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgThreeMonthAmount != null" >
        YXG_THREE_MONTH_AMOUNT = #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="oneMonthAmount != null" >
        ONE_MONTH_AMOUNT = #{oneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="yxgOneMonthAmount != null" >
        YXG_ONE_MONTH_AMOUNT = #{yxgOneMonthAmount,jdbcType=DECIMAL},
      </if>
      <if test="owerUserId != null" >
        OWER_USER_ID = #{owerUserId,jdbcType=INTEGER},
      </if>
      <if test="owerUser != null" >
        OWER_USER = #{owerUser,jdbcType=VARCHAR},
      </if>
      <if test="stock != null" >
        STOCK = #{stock,jdbcType=INTEGER},
      </if>
      <if test="intransitStock != null" >
        INTRANSIT_STOCK = #{intransitStock,jdbcType=INTEGER},
      </if>
      <if test="orderStock != null" >
        ORDER_STOCK = #{orderStock,jdbcType=INTEGER},
      </if>
      <if test="forecastSafeStock != null" >
        FORECAST_SAFE_STOCK = #{forecastSafeStock,jdbcType=INTEGER},
      </if>
      <if test="safeStock != null" >
        SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      </if>
      <if test="stockWarn != null" >
        STOCK_WARN = #{stockWarn,jdbcType=VARCHAR},
      </if>
      <if test="supplementStock != null" >
        SUPPLEMENT_STOCK = #{supplementStock,jdbcType=INTEGER},
      </if>
      <if test="receiveTimes != null" >
        RECEIVE_TIMES = #{receiveTimes,jdbcType=INTEGER},
      </if>
      <if test="outStockTimes != null" >
        OUT_STOCK_TIMES = #{outStockTimes,jdbcType=INTEGER},
      </if>
      <if test="operateType != null" >
        OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      </if>
      <if test="operateUserId != null" >
        OPERATE_USER_ID = #{operateUserId,jdbcType=INTEGER},
      </if>
      <if test="operateUserName != null" >
        OPERATE_USER_NAME = #{operateUserName,jdbcType=VARCHAR},
      </if>
      <if test="operateTime != null" >
        OPERATE_TIME = #{operateTime,jdbcType=BIGINT},
      </if>
      <if test="operateReason != null" >
        OPERATE_REASON = #{operateReason,jdbcType=VARCHAR},
      </if>
    </set>
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.flash.model.RegularOperateLog" >
    update T_REGULAR_OPERATE_LOG
    set PARENT_ID = #{parentId,jdbcType=INTEGER},
      REGULAR_ID = #{regularId,jdbcType=INTEGER},
      LOG_TYPE = #{logType,jdbcType=TINYINT},
      SKU_ID = #{skuId,jdbcType=INTEGER},
      SKU_NO = #{skuNo,jdbcType=VARCHAR},
      SKU_NAME = #{skuName,jdbcType=VARCHAR},
      BRAND_NAME = #{brandName,jdbcType=VARCHAR},
      SPU_TYPE = #{spuType,jdbcType=VARCHAR},
      CATEGORY_NAME = #{categoryName,jdbcType=VARCHAR},
      SPEC = #{spec,jdbcType=VARCHAR},
      UNIT = #{unit,jdbcType=VARCHAR},
      COST = #{cost,jdbcType=VARCHAR},
      AMOUNT = #{amount,jdbcType=DECIMAL},
      YXG_AMOUNT = #{yxgAmount,jdbcType=DECIMAL},
      THREE_MONTH_AMOUNT = #{threeMonthAmount,jdbcType=DECIMAL},
      YXG_THREE_MONTH_AMOUNT = #{yxgThreeMonthAmount,jdbcType=DECIMAL},
      ONE_MONTH_AMOUNT = #{oneMonthAmount,jdbcType=DECIMAL},
      YXG_ONE_MONTH_AMOUNT = #{yxgOneMonthAmount,jdbcType=DECIMAL},
      OWER_USER_ID = #{owerUserId,jdbcType=INTEGER},
      OWER_USER = #{owerUser,jdbcType=VARCHAR},
      STOCK = #{stock,jdbcType=INTEGER},
      INTRANSIT_STOCK = #{intransitStock,jdbcType=INTEGER},
      ORDER_STOCK = #{orderStock,jdbcType=INTEGER},
      FORECAST_SAFE_STOCK = #{forecastSafeStock,jdbcType=INTEGER},
      SAFE_STOCK = #{safeStock,jdbcType=INTEGER},
      STOCK_WARN = #{stockWarn,jdbcType=VARCHAR},
      SUPPLEMENT_STOCK = #{supplementStock,jdbcType=INTEGER},
      RECEIVE_TIMES = #{receiveTimes,jdbcType=INTEGER},
      OUT_STOCK_TIMES = #{outStockTimes,jdbcType=INTEGER},
      OPERATE_TYPE = #{operateType,jdbcType=TINYINT},
      OPERATE_USER_ID = #{operateUserId,jdbcType=INTEGER},
      OPERATE_USER_NAME = #{operateUserName,jdbcType=VARCHAR},
      OPERATE_TIME = #{operateTime,jdbcType=BIGINT},
      OPERATE_REASON = #{operateReason,jdbcType=VARCHAR}
    where LOG_ID = #{logId,jdbcType=INTEGER}
  </update>

  <select id="getRegularOperateLogList" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.flash.model.RegularOperateLog">
    SELECT
    FROM_UNIXTIME(CONVERT(OPERATE_TIME/1000,SIGNED),'%Y-%m-%d') AS operateTimeStr,
    FROM_UNIXTIME(CONVERT(LAST_ADD_TIME/1000,SIGNED),'%Y-%m-%d') AS lastAddTimeStr,
    <include refid="Base_Column_List" />
    FROM T_REGULAR_OPERATE_LOG
    WHERE
      OPERATE_TIME >= #{startDate,jdbcType=BIGINT}
    <![CDATA[AND OPERATE_TIME <= #{endDate,jdbcType=BIGINT}]]>
    AND PARENT_ID IS NULL
    order by OPERATE_TIME desc
  </select>

  <select id="getChileRegularSku" parameterType="com.vedeng.flash.dto.PrepareStockCommand" resultType="com.vedeng.flash.model.RegularOperateLog">
    SELECT
    <include refid="Base_Column_List" />
    FROM T_REGULAR_OPERATE_LOG
    WHERE PARENT_ID = #{logId, jdbcType=INTEGER}
  </select>
  <select id="selectBySkuId" resultType="com.vedeng.flash.model.RegularOperateLog">
    select
        *
    from
        T_REGULAR_OPERATE_LOG
    where
        SKU_ID = #{skuId,jdbcType=INTEGER}
        and OPERATE_TYPE = 0
    order by OPERATE_TIME desc
  </select>
</mapper>
