package com.newtask;

import com.alibaba.fastjson.JSON;
import com.vedeng.goods.dao.CoreSkuMapper;
import com.vedeng.goods.model.CoreSku;
import com.vedeng.goods.service.GoodsService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date created in 2020/10/13 18:43
 */
@Component
@JobHandler("initPriceAndStockOfSku")
public class InitPriceAndStockOfSku extends BaseHandler{

    @Autowired
    private GoodsService goodsService;

    @Autowired
    private CoreSkuMapper coreSkuMapper;

    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        int startId = 0;
        int leftCount;
        if (StringUtils.isNotBlank(param)){
            if (param.startsWith("V")){
                goodsService.updatePriceInfoOfSku(Collections.singletonList(param));
                goodsService.updateStockInfoOfSku(Collections.singletonList(param));
            } else {
                do {
                    List<CoreSku> skuList = coreSkuMapper.getSkuListOfUninitPriceAndStock(startId,0,50);
                    leftCount = skuList.size();
                    if (leftCount > 0){
                        List<String> skuNoList = skuList.stream().map(CoreSku::getSkuNo).filter(StringUtils::isNotBlank).filter(sku -> sku.startsWith("V")).collect(Collectors.toList());
                        goodsService.updatePriceInfoOfSku(skuNoList);
                        goodsService.updateStockInfoOfSku(skuNoList);
                        startId = skuList.get(skuList.size() - 1).getSkuId();
                    }
                } while (leftCount > 0);
            }

        } else {
            int i=0;
            do {
                List<CoreSku> skuList = coreSkuMapper.getSkuListOfUninitPriceAndStock(startId,1,50);
                leftCount = skuList.size();
                if (leftCount > 0){
                    List<String> skuNoList = skuList.stream().map(CoreSku::getSkuNo).filter(StringUtils::isNotBlank).filter(sku -> sku.startsWith("V")).collect(Collectors.toList());
                    XxlJobLogger.log(i+++JSON.toJSONString(skuNoList));
                    goodsService.updatePriceInfoOfSku(skuNoList);
                    goodsService.updateStockInfoOfSku(skuNoList);
                    startId = skuList.get(skuList.size() - 1).getSkuId();
                }
            } while (leftCount > 0);
        }
        return ReturnT.SUCCESS;
    }
}
