package com.vedeng.erp.kingdee.repository.mappers;

import com.vedeng.erp.kingdee.domain.entity.KingDeePurchaseVatSpecialInvoiceEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface KingDeePurchaseVatSpecialInvoiceMapper {
    /**
     * delete by primary key
     * @param purchaseVatSpecialInvoiceId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer purchaseVatSpecialInvoiceId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(KingDeePurchaseVatSpecialInvoiceEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(KingDeePurchaseVatSpecialInvoiceEntity record);

    /**
     * select by primary key
     * @param purchaseVatSpecialInvoiceId primary key
     * @return object by primary key
     */
    KingDeePurchaseVatSpecialInvoiceEntity selectByPrimaryKey(Integer purchaseVatSpecialInvoiceId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(KingDeePurchaseVatSpecialInvoiceEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(KingDeePurchaseVatSpecialInvoiceEntity record);

    int updateBatchSelective(List<KingDeePurchaseVatSpecialInvoiceEntity> list);

    int batchInsert(@Param("list") List<KingDeePurchaseVatSpecialInvoiceEntity> list);

    List<KingDeePurchaseVatSpecialInvoiceEntity> findByFQzokBddjtid(@Param("list") List<String> invoiceIds);

    KingDeePurchaseVatSpecialInvoiceEntity selectByFQzokBddjtid(String FQzokBddjtid);
}