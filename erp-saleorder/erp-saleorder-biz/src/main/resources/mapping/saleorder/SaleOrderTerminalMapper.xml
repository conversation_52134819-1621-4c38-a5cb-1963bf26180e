<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.saleorder.dao.SaleOrderTerminalMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity">
    <id column="ID" jdbcType="INTEGER" property="id" />
    <result column="SALE_ORDER_ID" jdbcType="INTEGER" property="saleOrderId" />
    <result column="TERMINAL_NAME" jdbcType="VARCHAR" property="terminalName" />
    <result column="DWH_TERMINAL_ID" jdbcType="VARCHAR" property="dwhTerminalId" />
    <result column="UNIFIED_SOCIAL_CREDIT_IDENTIFIER" jdbcType="VARCHAR" property="unifiedSocialCreditIdentifier" />
    <result column="ORGANIZATION_CODE" jdbcType="VARCHAR" property="organizationCode" />
    <result column="IS_DELETED" jdbcType="INTEGER" property="isDeleted" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
  </resultMap>
  <sql id="Base_Column_List">
    ID, SALE_ORDER_ID, TERMINAL_NAME, DWH_TERMINAL_ID, UNIFIED_SOCIAL_CREDIT_IDENTIFIER, 
    ORGANIZATION_CODE, IS_DELETED, ADD_TIME, MOD_TIME, CREATOR, UPDATER, CREATOR_NAME, 
    UPDATER_NAME
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from T_SALE_ORDER_TERMINAL
    where ID = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from T_SALE_ORDER_TERMINAL
    where ID = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity" useGeneratedKeys="true">
    insert into T_SALE_ORDER_TERMINAL (SALE_ORDER_ID, TERMINAL_NAME, DWH_TERMINAL_ID, 
      UNIFIED_SOCIAL_CREDIT_IDENTIFIER, ORGANIZATION_CODE, 
      IS_DELETED, ADD_TIME, MOD_TIME, 
      CREATOR, UPDATER, CREATOR_NAME, 
      UPDATER_NAME)
    values (#{saleOrderId,jdbcType=INTEGER}, #{terminalName,jdbcType=VARCHAR}, #{dwhTerminalId,jdbcType=VARCHAR}, 
      #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR}, #{organizationCode,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=INTEGER}, #{addTime,jdbcType=TIMESTAMP}, #{modTime,jdbcType=TIMESTAMP}, 
      #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, #{creatorName,jdbcType=VARCHAR}, 
      #{updaterName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="ID" keyProperty="id" parameterType="com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity" useGeneratedKeys="true">
    insert into T_SALE_ORDER_TERMINAL
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleOrderId != null">
        SALE_ORDER_ID,
      </if>
      <if test="terminalName != null">
        TERMINAL_NAME,
      </if>
      <if test="dwhTerminalId != null">
        DWH_TERMINAL_ID,
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER,
      </if>
      <if test="organizationCode != null">
        ORGANIZATION_CODE,
      </if>
      <if test="isDeleted != null">
        IS_DELETED,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleOrderId != null">
        #{saleOrderId,jdbcType=INTEGER},
      </if>
      <if test="terminalName != null">
        #{terminalName,jdbcType=VARCHAR},
      </if>
      <if test="dwhTerminalId != null">
        #{dwhTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity">
    update T_SALE_ORDER_TERMINAL
    <set>
      <if test="saleOrderId != null">
        SALE_ORDER_ID = #{saleOrderId,jdbcType=INTEGER},
      </if>
      <if test="terminalName != null">
        TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
      </if>
      <if test="dwhTerminalId != null">
        DWH_TERMINAL_ID = #{dwhTerminalId,jdbcType=VARCHAR},
      </if>
      <if test="unifiedSocialCreditIdentifier != null">
        UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      </if>
      <if test="organizationCode != null">
        ORGANIZATION_CODE = #{organizationCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
    </set>
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity">
    update T_SALE_ORDER_TERMINAL
    set SALE_ORDER_ID = #{saleOrderId,jdbcType=INTEGER},
      TERMINAL_NAME = #{terminalName,jdbcType=VARCHAR},
      DWH_TERMINAL_ID = #{dwhTerminalId,jdbcType=VARCHAR},
      UNIFIED_SOCIAL_CREDIT_IDENTIFIER = #{unifiedSocialCreditIdentifier,jdbcType=VARCHAR},
      ORGANIZATION_CODE = #{organizationCode,jdbcType=VARCHAR},
      IS_DELETED = #{isDeleted,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR}
    where ID = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectBySaleOrderId" resultType="com.vedeng.erp.saleorder.domain.entity.SaleOrderTerminalEntity">
    select
    <include refid="Base_Column_List" />
    from T_SALE_ORDER_TERMINAL
    where SALE_ORDER_ID = #{saleOrderId,jdbcType=INTEGER}
  </select>
</mapper>