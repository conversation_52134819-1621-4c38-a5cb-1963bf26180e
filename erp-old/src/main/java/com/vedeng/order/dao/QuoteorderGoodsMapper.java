package com.vedeng.order.dao;

import com.vedeng.order.model.QuoteorderGoods;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface QuoteorderGoodsMapper {
    /**
     * <b>Description:</b><br> 验证报价中产品是否存在
     * @param record
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2017年7月7日 下午4:09:54
     */
    int vailQuoteGoods(QuoteorderGoods record);

    /**
     * <b>Description:</b><br> 添加报价产品信息
     * @param record
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2017年7月4日 上午10:29:51
     */
    int insertQuoteGoods(QuoteorderGoods record);

    /**
     * <b>Description:</b><br> 验证产品总金额是否等于原来的总金额
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2017年7月28日 上午9:50:34
     */
    Integer vailQuoteTotle(QuoteorderGoods quoteGoods);

    /**
     * <b>Description:</b><br> 修改主表中报价总金额
     * @param quoteGoods
     * @return
     * @Note
     * <b>Author:</b> duke
     * <br><b>Date:</b> 2017年7月24日 下午4:23:51
     */
    int updateQuoteTotal(QuoteorderGoods quoteGoods);

    /**
     * <b>Description:</b>保存报价单商品<br>
     * @param
     * @return
     * @Note
     * <b>@Author:calvin</b>
     * <br><b>@Date:</b> 2020/10/19
     */
    int editQuoteGoods(QuoteorderGoods record);

    List<QuoteorderGoods> selectQuoteGoodsById(@Param(value="quoteOrderId")Integer quoteOrderId, @Param(value="viewType")Integer viewType);

    int updateQuoteGoodsInfo(QuoteorderGoods qg);

    List<QuoteorderGoods> getAllQuoteOrderGoods(@Param("quoteorderId")Integer quoteorderId);

    void updateQuoteorderGoods2Valid(@Param("goodsIdList") List<Integer> goodsIdList, @Param("quoteorderId") Integer quoteorderId);

    void updateDefaultDeliveryDirectCommentByQuoteGoodsId(@Param("quoteGoodsIdList") List<Integer> quoteGoodsIdList);

    QuoteorderGoods selectQuoteGoodsId(Integer quoteorderGoodsId);
}
