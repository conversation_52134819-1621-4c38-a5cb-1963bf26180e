package com.newtask;

import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.vedeng.erp.saleorder.api.SaleorderSyncService;
import com.vedeng.erp.saleorder.enums.PCOrderStatusEnum;
import com.vedeng.erp.saleorder.enums.SaleorderSyncEnum;
import com.vedeng.logistics.service.ExpressService;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.Saleorder;
import com.vedeng.trader.dao.WebAccountMapper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
* @ClassName: BDSaleorderTask
* @Description: TODO(BD订单待用户确认订单定时任务)
* <AUTHOR>
* @date 2019年8月1日
*
*/

@Component
@JobHandler(value="BDSaleorderTask")
public class BDSaleorderTask extends AbstractJobHandler {
	
	private Logger logger = LoggerFactory.getLogger(BDSaleorderTask.class);
	@Value("${mjx_url}")
	private String mjxUrl;
	
	@Autowired
	private SaleorderMapper saleorderMapper;
	@Autowired
	@Qualifier("expressService")
	protected ExpressService expressService;
	@Resource
	private SaleorderSyncService saleorderSyncService;

	
	@Autowired
	private WebAccountMapper webAccountMapper;
	
	public ResultInfo UserValidationStatus() {
		XxlJobLogger.log("BDSaleorderTask.UserValidationStatus | begin ...............");
		logger.info("BDSaleorderTask.UserValidationStatus | begin ...............");
		// 查询订单状态为4订单类型为1的订单
		Integer orderType =1;
		Integer status =4 ;
		List<Saleorder> saleorderlist = saleorderMapper.getSaleorderListByStatus(status,orderType);
		Long time = DateUtil.sysTimeMillis();
		logger.info("BDSaleorderTask.UserValidationStatus | num:{} ...............");
		Long t = null;
		XxlJobLogger.log("saleorderlist++++"+saleorderlist);
//		OrderData orderData = new OrderData();
 		ResultInfo r = new ResultInfo();
//		Integer i = 0;
		if(saleorderlist!=null) {
		for (Saleorder saleorder : saleorderlist) {
			t = time-saleorder.getBdMobileTime();
			//超过14天未确认关闭订单
			if(t>**********) {//**********
				saleorder.setStatus(3);
				saleorder.setCloseComments("客户超时未确认");
				saleorder.setModTime(time);
				saleorder.setUpdateDataTime(new Date());
				saleorderMapper.updateByPrimaryKeySelective(saleorder);
 				//重构
				saleorderSyncService.syncSaleorderStatus2Mjx(saleorder.getSaleorderId()
						, PCOrderStatusEnum.CANCEL, SaleorderSyncEnum.CANCEL_BD_TIMEOUT);

			}
		}
		}
	    XxlJobLogger.log("WeChatSendOrder.sendWxTemplateForOrder | end ...............");
        logger.info("WeChatSendOrder.sendWxTemplateForOrder | end ...............");
        return r;
	}
	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		XxlJobLogger.log("XXL-JOB, Hello World.");
		UserValidationStatus();
		return SUCCESS;
	}



}
