<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>商品信息待办项</title>
    <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
</head>
<body id="goodsTodo" datasource="erp-datasourcetarget" fixednumber append_column_url append_row_url empty_show firstcol="numbers">
<div id="appendHead"></div>
<div class="layui-fluid">
    <div class="layui-tab">
        <ul class="layui-tab-title" id="tab">

        </ul>
    </div>
    <form class="layui-form" id="search">

        <div class=" layui-inline ">
            <label class="layui-form-label">订货号</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="SKU_NO" placeholder style alias="K" jdbctype data datatype oper>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">审核状态</label>
            <div class="layui-input-inline">
                <input class=" layui-input list-search-item " type="text" name="CHECK_STATUS" placeholder style alias="K" jdbctype data='[{"VALUE":"待完善","KEY":"1"},{"VALUE":"审核不通过","KEY":"2"},{"VALUE":"审核通过","KEY":"3"},{"VALUE":"待提交审核","KEY":"5"}]' datatype="JSON" oper>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">产品经理</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="MANAGER_ID" placeholder style alias="U1.USER_ID" jdbctype  data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE"  oper>
                </select> </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">产品助理</label>
            <div class="layui-input-inline">
                <select class=" layui-input list-search-item " type="search" name="ASSISTANT_ID" placeholder style alias="U2.USER_ID" jdbctype  data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE"  oper>
                </select>
            </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">产品助理hidden</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="hidden" name="ASSIGNMENT_ASSISTANT_ID" placeholder style alias="P" jdbctype="BODY"  data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE"  oper>
                </object>> </div>
        </div>

        <div class=" layui-inline ">
            <label class="layui-form-label">产品经理hidden</label>
            <div class="layui-input-inline">
                <object class=" layui-input list-search-item " type="hidden" name="ASSIGNMENT_MANAGER_ID" placeholder style alias="P" jdbctype="BODY"  data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE"  oper>
                </object> </div>
        </div>

    </form>
    <hr class="layui-border-blue">
    <div class="btn-group   bd-highlight" id="tableButton">

    </div>
    <table id="table" class="layui-table" style=" width:100%">
        <thead>
        <tr id="column">
            <th item_name="SKU_NO" name="SKU_NO" body="td-link" url="/todolist/supplyChain/editSkuInfo.do?sku=${SKU}" opentype="PARENT" head="th">SKU</th>
            <th item_name="SKU_NAME" name="SKU_NAME" body="td-text" head="th">商品名称</th>
            <th item_name="LEVEL_NAME" name="LEVEL_NAME" body="td-text" head="th">商品等级</th>
            <th item_name="POSITION_NAME" name="POSITION_NAME" body="td-text" head="th">商品档位</th>
            <th item_name="ORDER_NO" name="ORDER_NO" body="td-text" head="th">关联单号</th>
            <th item_name="MOD_TIME" name="MOD_TIME" body="td-text" head="th">修改时间</th>
            <th item_name="RISK_TIME" name="RISK_TIME" body="td-text" head="th">首次被风控时间</th>

            <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-text" head="th">审核状态</th>
            <th item_name="MANAGER_ID" name="MANAGER_ID" body="td-select" head="th"  data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE" >产品经理</th>
            <th item_name="ASSISTANT_ID" name="ASSISTANT_ID" body="td-select" head="th"  data="select USER_ID K,USERNAME V FROM T_USER " DATATYPE="KVSQLCACHE" >产品助理</th></tr>
        <th type="rowbutton" id="rowbutton">
            <button type="single" url="/todolist/supplyChain/editSkuInfo.do?sku=${SKU}" opentype="PARENT" windowname="${SKU_NAME}">编辑</button>
        </th>
        </thead>
        <tbody>
        <tr>
            <td> express:</td>
            <td colspan="100">
                <pre id="express" class="layui-code" orderby="" groupby>StringBuilder sql=new StringBuilder();

sql.append("
SELECT
K.SKU_NO,
K.SKU_NAME,
K.SKU_NO AS SKU,
GL.LEVEL_NAME,
GP.POSITION_NAME,
L.ORDER_NO,
K.MOD_TIME,
FROM_UNIXTIME(L.ADD_TIME/1000) RISK_TIME,
K.CHECK_STATUS,
U1.USER_ID MANAGER_ID,

U2.USER_ID ASSISTANT_ID,
P.ASSIGNMENT_MANAGER_ID,

P.ASSIGNMENT_ASSISTANT_ID
FROM
(SELECT BUZ_ID, GROUP_CONCAT(DISTINCT BUZ_EXTRA) ORDER_NO, ADD_TIME FROM T_TODO_LIST WHERE `STATUS` = 0 AND BUZ_TYPE = 7
 GROUP BY BUZ_ID) L

JOIN V_CORE_SKU K ON L.BUZ_ID = K.SKU_ID
LEFT JOIN V_GOODS_LEVEL GL ON K.GOODS_LEVEL_NO = GL.ID
LEFT JOIN V_GOODS_POSITION GP ON K.GOODS_POSITION_NO = GP.ID
JOIN V_CORE_SPU P ON K.SPU_ID = P.SPU_ID

");
sid=$("ASSIGNMENT_ASSISTANT_ID");
mid=$("ASSIGNMENT_MANAGER_ID");
 sql.append(" AND ( P.ASSIGNMENT_MANAGER_ID IN  ("+mid+")
  OR P.ASSIGNMENT_ASSISTANT_ID IN  ("+sid+") )
  ");
  sql.append("
LEFT JOIN T_USER U1 ON P.ASSIGNMENT_MANAGER_ID = U1.USER_ID
LEFT JOIN T_USER U2 ON P.ASSIGNMENT_ASSISTANT_ID = U2.USER_ID
where 1=1
");
return search(sql);</pre>

            </td>
        </tr>
        <tr>
            <td> count:</td>
            <td colspan="100">
                <pre id="count" class="layui-code"></pre>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div id="appendFoot"></div>
<script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script>
<script>
    layui.use(function () {

    })
</script>

</body></html>