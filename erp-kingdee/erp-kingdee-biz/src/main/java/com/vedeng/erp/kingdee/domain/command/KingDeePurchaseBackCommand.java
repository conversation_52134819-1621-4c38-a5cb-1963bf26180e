package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.erp.kingdee.dto.KingDeePurchaseBackDto;
import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 采购退料单 dto  https://www.yuque.com/manhuo/gf1570/gs91wo
 * <AUTHOR>
 * @version 1.0
 * @description: 预处理金蝶采购退料单 dto  由erp实际业务转换
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeePurchaseBackCommand {

    /**
     *  0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    private String fBillNo;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String f_qzok_bddjtId;
    /**
     * 单据日期 填单据日期，格式yyyy-MM-dd
     */
    private String fDate;
    /**
     * 退料类型 默认：B
     */
    private String fMrType;

    /**
     * 单据类型
     */
    private String fBusinessType;
    /**
     * 退料方式 默认：B
     */
    private String fMrMode;

    /**
     * 退料数量 填写实际的退料数量 FPURMRBENTRY_Link为此数组总计退货数量
     */
    private List<FPurmrbEntry> fpurmrbentry;

    //Fnumber
    /**
     * 单据类型  填单据类型编码，默认填TLD01_SYS
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();

    /**
     * 退料组织 填写组织编码
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();

    /**
     * 供应商 填写供应商编码
     */
    private KingDeeNumberCommand fSupplierId = new KingDeeNumberCommand();




    /**
     * fPurmrbEntry
     */
    @NoArgsConstructor
    @Data
    public static class FPurmrbEntry {

        /**
         * 退料数量 填写实际的退料数量 FPURMRBENTRY_Link为此数组总计退货数量
         */
        private String fRmrealqty;

        /**
         * 含税价 采购单含税单价
         */
        private String fTaxPrice;
        /**
         * 税率 采购单税率
         */
        private String fEntryTaxRate;
        /**
         * 备注
         */
        private String fNote;
        /**
         * 原始订单号
         */
        private String f_qzok_ysddh;
        /**
         * 归属业务单号
         */
        private String f_qzok_gsywdh;
        /**
         * 业务类型
         */
        private String f_qzok_ywlx;
        /**
         * 批次号
         */
        private String f_qzok_pch;
        /**
         * 序列号
         */
        private String f_qzok_xlh;
        /**
         * 授权类型
         */
        private String f_qzok_sqlx;
        /**
         * 是否直发	否
         */
        private String f_qzok_sfzf;
        /**
         * 贝登订单行ID
         */
        private String f_qzok_bddjhid;
        /**
         * 源单类型 关联退料单必填，默认：STK_InStock
         */
        private String fsourcetype;
        /**
         * fpurmrbentry_link
         */
        private List<FPurmrentryLink> fpurmrbentry_link;

        //Fnumber
        /**
         * 物料 填写物料的编码 SKU
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();

        /**
         * 仓库 填写仓库编码, 默认值ck9999
         */
        private KingDeeNumberCommand fStockId = new KingDeeNumberCommand();



        /**
         * fPurmrentryLink
         */
        @NoArgsConstructor
        @Data
        public static class FPurmrentryLink {
            /**
             * 关联内码 关联退料单必填，默认0
             */
            private String fLinkId;
            /**
             * 转换规则 关联退料单必填，默认STK_InStock-PUR_MRB	默认STK_InStock-PUR_MRB
             */
            private String fpurmrbentry_link_fruleid;
            /**
             * 推进路线 关联退料单必填，默认0
             */
            private String fpurmrbentry_link_fflowlineid;
            /**
             * 源单表内码 关联退料单必填，默认0	默认0
             */
            private String fpurmrbentry_link_fstableid;
            /**
             * 源单表 默认：T_STK_INSTOCKENTRY
             */
            private String fpurmrbentry_link_fstablename;
            /**
             * 源单内码 关联退料单必填，上游单据内码
             */
            private String fpurmrbentry_link_fsbillid;
            /**
             * 源单分录内码 关联退料单必填，上游单据行内码
             */
            private String fpurmrbentry_link_fsid;
            /**
             * 原始携带计价数量 取退料的数量
             */
            private String fpurmrbentry_link_fbasicunitqtyold;
            /**
             * 修改后计价数量 取退料的数量
             */
            private String fpurmrbentry_link_fbasicunitqty;
            /**
             * 原始基本数量 取退料的数量
             */
            private String fpurmrbentry_link_fcarrybaseqtyold;
            /**
             * 修改后基本数量 取退料的数量
             */
            private String fpurmrbentry_link_fcarrybaseqty;
        }
    }
}
