package com.vedeng.goods.domain.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
    * 售后标准-供应商维护政策
    */
@Data
public class AfterSaleSupplyPolicy {
    /**
    * 主键
    */
    private Long supplyPolicyId;

    /**
    * SKU
    */
    private String skuNo;

    /**
    * 供应商的ID
    */
    private Long traderId;

    /**
    * 供应商的名称
    */
    private String traderName;

    /**
    * 售后服务商 1.原厂服务 2.该供应商提供服务
    */
    private Integer serviceProviderType;

    /**
    * 安装政策 类型0-收费安装 1-免费安装 2-不可安装
    */
    private Integer installPolicyInstallType;

    /**
    * 安装政策-安装区域
    */
    private String installPolicyInstallArea;

    /**
    * 安装政策-安装费
    */
    private BigDecimal installPolicyInstallFee;

    /**
    * 安装政策-装机资质
    */
    private Integer installPolicyHaveInstallationQualification;

    /**
    * 安装政策-免费远程装机
    */
    private Integer installPolicyFreeRemoteInstall;

    /**
    * 安装政策-响应时间
    */
    private String installPolicyResponseTime;

    /**
    * 安装政策-上门时间
    */
    private String installPolicyVisitTime;

    /**
    * 安装政策-安装时间
    */
    private String installPolicyInstallTime;

    /**
    * 技术指导-是否提供技术维护
    */
    private Integer technicalDirectSupplyMaintain;

    /**
    * 技术指导-响应时间
    */
    private String technicalDirectResponseTime;

    /**
    * 技术指导-时效时间
    */
    private String technicalDirectEffectTime;

    /**
    * 保修政策-是否保修
    */
    private Integer guaranteePolicyIsGuarantee;

    /**
    * 保修政策-保修方式
    */
    private String guaranteePolicyGuaranteeType;

    /**
    * 保修政策-主机保修期
    */
    private String guaranteePolicyHostGuaranteePeriod;

    /**
    * 保修政策-配件保修期
    */
    private String guaranteePolicyPartsGuaranteePeriod;

    /**
    * 保修政策-周期计算方式
    */
    private Integer guaranteePolicyCycleCaltype;

    /**
    * 保修政策-保修区域 0-全国 1-部分区域
    */
    private Integer guaranteePolicyArea;

    /**
    * 保修政策-保修区域说明
    */
    private String guaranteePolicyAreaComment;

    /**
    * 保修政策-响应时间
    */
    private String guaranteePolicyResponseTime;

    /**
    * 保修政策-上门时间
    */
    private String guaranteePolicyVisitTime;

    /**
    * 保修政策-维修时效
    */
    private String guaranteePolicyRepaireTime;

    /**
    * 保修政策-保修备注
    */
    private String guaranteePolicyRepaireComment;

    /**
    * 退货政策-是否支持退货
    */
    private Integer returnPolicySupportReturn;

    /**
    * 退货政策-退货条件
    */
    private String returnPolicyCondition;

    /**
    * 退货政策-是否需要鉴定
    */
    private Integer returnPolicyNeedIdentify;

    /**
    * 退货政策-鉴定方式
    */
    private String returnPolicyIdentifyType;

    /**
    * 退货政策-退货期限
    */
    private String returnPolicyReturnPeriod;

    /**
    * 退货政策-周期计算方式
    */
    private Integer returnPolicyCycleCaltyp;

    /**
    * 退货政策-包装要求
    */
    private String returnPolicyPackagingRequirements;

    /**
    * 退货政策-退货备注
    */
    private String returnPolicyReturnComments;

    /**
    * 换货政策-是否支持换货
    */
    private Integer exchangePolicySupportChange;

    /**
    * 换货政策-换货条件
    */
    private String exchangePolicyExchangeContition;

    /**
    * 换货政策-是否需要鉴定
    */
    private Integer exchangePolicyNeedIdentify;

    /**
    * 换货政策-鉴定方式
    */
    private String exchangePolicyIdentifyType;

    /**
    * 换货政策-换货期限
    */
    private String exchangePolicyExchangePeriod;

    /**
    * 换货政策-周期计算方式
    */
    private Integer exchangePolicyCycleCaltyp;

    /**
    * 换货政策-包装要求
    */
    private String exchangePolicyPackagingRequirements;

    /**
    * 换货政策-换货备注
    */
    private String exchangePolicyExchangeComments;

    /**
    * 保外政策-是否提供有偿维修
    */
    private Integer parolePolicySupportRepair;

    /**
    * 保外政策-是否提供翻新服务
    */
    private Integer parolePolicySupportRenovation;

    /**
    * 保外政策-可否提供纸箱
    */
    private Integer parolePolicySupplyBox;

    /**
    * 保外政策-可否提供附件
    */
    private Integer parolePolicySupplyAttachment;

    /**
    * 超期处理政策-可否提供备用机
    */
    private String overduePolicySupplyBackup;

    /**
    * 超期处理政策-超期处理详情
    */
    private String overduePolicyDetail;

    /**
    * 添加人
    */
    private String creator;

    /**
    * 最近一次编辑人
    */
    private String updator;

    /**
    * 添加时间
    */
    private String addTime;

    /**
    * 最近一次编辑时间
    */
    private String modTime;
}