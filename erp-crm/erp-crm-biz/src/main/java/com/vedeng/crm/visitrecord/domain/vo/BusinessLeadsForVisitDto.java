package com.vedeng.crm.visitrecord.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class BusinessLeadsForVisitDto {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 线索编号
     */
    private String leadsNo;

    /**
     * 客户ID
     */
    private Integer traderId;

    /**
     * 客户名称
     */
    private String traderName;

    /**
     * 天眼查标识Y是N否
     */
    private String tycFlag;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 归属人id
     */
    private Integer belongerId;

    /**
     * 归属人
     */
    private String belonger;

    /**
     * 归属人头像
     */
    private String belongerPic;

    /**
     * 线索跟进状态(0未分配，1.未处理、2.跟进中、3.已关闭、4.已商机)
     */
    private Integer followStatus;

    /**
     * 线索跟进状态-名称
     */
    private String followStatusStr;

    /**
     * 线索类型字典库
     */
    private Integer clueType;
    /**
     * 线索类型-名称
     */
    private String clueTypeName;


    /**
     * 返回给crm的前端erp的超链接
     */
    private String traderNameLink;

    /**
     * ERP内部链接
     */
    private String traderNameInnerLink;


}
