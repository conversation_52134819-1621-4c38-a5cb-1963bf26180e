package com.newtask;

import com.vedeng.billsync.task.service.AlipayBillSyncTaskService;
import com.vedeng.billsync.task.service.BankBillDataSyncService;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.common.util.DateUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 定时任务同步支付宝账单
 * @Date 2020/12/16 11:11
 */
@Component
@JobHandler(value = "AlipayBillSyncTask")
public class AlipayBillSyncTask extends AbstractJobHandler {

    @Autowired
    private AlipayBillSyncTaskService alipayBillSyncTaskService;

    @Autowired
    private BankBillDataSyncService bankBillDataSyncService;

    @Override
    public ReturnT<String> doExecute(String billDate) {
        XxlJobLogger.log("XXL-JOB, AlipayBillSyncTask. 参数: " + billDate);
        ResultInfo resultInfo;
        try {
            if (StringUtils.isNotBlank(billDate)) {
                if ( ! billDate.matches("^\\d{4}-\\d{2}-\\d{2}$")){
                    XxlJobLogger.log("XXL-JOB, AlipayBillSyncTask. 请输入日期格式: YYYY-MM-DD");
                    return FAIL;
                }
                resultInfo = alipayBillSyncTaskService.syncAlipayBillList(billDate);
                // add by Randy.Xu 2020/12/22 9:57 .Desc: . begin
                Date queryDate = new Date(DateUtil.convertLong(billDate, DateUtil.DATE_FORMAT));
                bankBillDataSyncService.syncBillDate2bankBill(queryDate,"1",4);
                // add by Randy.Xu 2020/12/22 9:57 .Desc: . end

            } else {
                billDate = DateUtil.DateToString(DateUtil.getPreviousDayByDateTime(new Date()), DateUtil.DATE_FORMAT);
                resultInfo = alipayBillSyncTaskService.syncAlipayBillList(billDate);
                // add by Randy.Xu 2020/12/22 9:57 .Desc: . begin
                Date queryDate = new Date(DateUtil.convertLong(billDate, DateUtil.DATE_FORMAT));
                bankBillDataSyncService.syncBillDate2bankBill(queryDate,"1",4);
                // add by Randy.Xu 2020/12/22 9:57 .Desc: . end

            }
            if (resultInfo != null) {
                XxlJobLogger.log("XXL-JOB, AlipayBillSyncTask. 任务状态: " + resultInfo.getMessage());
                if (resultInfo.getCode() == 0) {
                    return SUCCESS;
                }

            }


        } catch (Exception e) {
            XxlJobLogger.log("同步支付宝账单失败！Exception:" + e);
            return FAIL;
        }
        return FAIL;
    }
}
