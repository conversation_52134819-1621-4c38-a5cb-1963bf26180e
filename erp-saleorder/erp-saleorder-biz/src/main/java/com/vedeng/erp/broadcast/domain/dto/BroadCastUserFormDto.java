package com.vedeng.erp.broadcast.domain.dto;

import lombok.*;
import javax.validation.constraints.NotNull;

/**
 * 播报用户表单DTO
 * 用于新增和编辑播报用户关联关系的表单数据传输
 * 只包含必要的业务字段，简化前端交互
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastUserFormDto {
    
    /**
     * 主键ID
     * 新增时为null，编辑时必须提供
     */
    private Integer id;
    
    /**
     * ERP用户ID
     * 必填字段，关联ERP系统中的用户
     */
    @NotNull(message = "ERP用户ID不能为空")
    private Integer erpUserId;
    
    /**
     * AED用户ID
     * 必填字段，关联AED系统中的销售用户
     */
    @NotNull(message = "AED用户ID不能为空")
    private Integer aedUserId;
} 