package com.vedeng.erp.kingdee.batch.common.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.SkipListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: skip监听器
 * @date 2023/2/22 10:22
 */
@Component
@Slf4j
public class BaseSkipListener implements SkipListener<String, String> {

    @Override
    public void onSkipInRead(Throwable t) {
        log.error("在读取数据的时候遇到异常并跳过，异常：{}", t.getMessage(), t);
    }

    @Override
    public void onSkipInWrite(String item, Throwable t) {
        log.error("在输出数据的时候遇到异常并跳过，待输出数据：{} ，异常：{}", item, t.getMessage(), t);
    }

    @Override
    public void onSkipInProcess(String item, Throwable t) {
        log.error("在处理数据的时候遇到异常并跳过，待输出数据：{} ，异常：{}", item, t.getMessage(), t);
    }
}
