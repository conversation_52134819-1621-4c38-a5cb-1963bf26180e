package com.vedeng.mobile.system.mapper;

import com.vedeng.mobile.system.model.TraderContact;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * 交易者联系人(TraderContact)表数据库访问层
 *
 * <AUTHOR> @since 2022-07-12 16:53:41
 */
@Repository("mobileTraderContactMapper")
public interface TraderContactMapper {

    /**
     * 查询指定行数据
     *
     * @param TraderContact 查询条件
     * @return 对象列表
     */
    List<TraderContact> findByAll(TraderContact TraderContact);

    /**
     * 根据客户ID和NAME查询联系人信息
     * @param TraderContact
     * @return
     */
    List<TraderContact> findByTraderIdAndName(TraderContact TraderContact);

    /**
     * 根据客户ID和NAME查询联系人信息，然后更新手机号等信息
     * @param TraderContact
     * @return
     */
    int updateContractInfoByTraderContractId(TraderContact TraderContact);


    /**
     * 新增数据
     *
     * @param traderContact 实例对象
     * @return 影响行数
     */
    int insert(TraderContact traderContact);

    /**
     * 新增指定的列
     * @param traderContact
     * @return
     */
    int insertSelective(TraderContact traderContact);

    /**
     * 查询联系人信息
     * @param traderContactId id
     * @return TraderContact 信息
     */
    TraderContact selectByTraderContactId(@Param("traderContactId")Integer traderContactId);

    /**
     *姓名、手机、电话多字段模糊查询
     * @param traderContact 参数
     * @return list
     */
    List<TraderContact> matchTraderContractSearch(TraderContact traderContact);


    /**
     * 根据联系人ID 批量查询联系人信息下
     * @param traderContactId 联系人id集合
     * @return list
     */
    List<TraderContact> getTraderContacts(List<Integer> traderContactId);

    /**
     * @description: 获取供应商下联系人
     * @return:
     * @author: Strange
     * @date: 2022/8/24
     **/
    List<TraderContact> getSupplierContact(Integer traderId);

    /**
     * 查询客户联系人信息(迁移EZ列表的sql)
     *
     * @param traderContactIdList 联系人id集合
     * @return TraderContact
     */
    List<TraderContact> getTraderContactInfoByIdList(@Param("traderContactIdList") List<Integer> traderContactIdList);

    /**
     * 查询客户联系人信息中，在职的且最新沟通的一个联系人
     *
     * @param traderId traderId
     * @return 联系人Id
     */
    List<Integer> getLatestCommunicateContact(@Param("traderId") Integer traderId);

    /**
     * 根据职位优先级 查询客户联系人
     *
     * @param traderId traderId
     * @return 联系人Id
     */
    List<Integer> getTraderContactListBySort(@Param("traderId") Integer traderId);

}

