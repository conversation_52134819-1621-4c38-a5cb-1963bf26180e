package com.vedeng.erp.business.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.utils.validator.ValidatorUtils;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.business.domain.dto.AssignLeadsDto;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadMergeDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.business.service.InitializationService;
import com.vedeng.erp.system.common.annotation.CustomDataOperAnnotation;
import com.vedeng.erp.system.common.enums.CustomDataOperBizTypeEnums;
import com.vedeng.erp.system.common.enums.CustomDataOperTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 业务数据操作api
 * @date 2022/7/9 14:46
 */
@ExceptionController
@RestController
@RequestMapping("/businessLeads")
@Slf4j
public class BusinessLeadsApi {

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Autowired
    private InitializationService initializationService;

    /**
     *线索列表分页查询
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/page", method = RequestMethod.POST)
    public R<PageInfo<BusinessLeadsDto>> page(@RequestBody PageParam<BusinessLeadsDto> businessLeadsDto) {
        return R.success(businessLeadsService.page(businessLeadsDto));
    }

    /**
     *新增线索
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> add(@RequestBody BusinessLeadsDto businessLeadsDto) {
        ValidatorUtils.validate(businessLeadsDto, AddGroup.class);
        return R.success(businessLeadsService.add(businessLeadsDto));
    }

    /**
     *根据参数查询对应的线索0
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getLeadsListByDto", method = RequestMethod.POST)
    public R<?> getLeadsListByDto(@RequestBody BusinessLeadsDto businessLeadsDto) {
        return R.success(businessLeadsService.getLeadsListByDto(businessLeadsDto));
    }

    /**
     *编辑线索提交
     */
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @NoNeedAccessAuthorization
    public R<?> update(@RequestBody BusinessLeadsDto businessLeadsDto) {
        ValidatorUtils.validate(businessLeadsDto, UpdateGroup.class);
        businessLeadsService.update(businessLeadsDto);
        return R.success();
    }

    /**
     *关闭线索
     */
    @RequestMapping(value = "/closedLeads", method = RequestMethod.POST)
    public R<?> closedLeads(@RequestBody BusinessLeadsDto businessLeadsDto) {
        businessLeadsService.closedLeads(businessLeadsDto);
        return R.success();
    }

    /**
     *更新线索分级
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/updateLeadsStatus", method = RequestMethod.POST)
    public R<?> updateLeadsStatus(@RequestBody BusinessLeadsDto businessLeadsDto) {
        businessLeadsService.updateLeadsStatus(businessLeadsDto);
        return R.success();
    }

    /**
     *线索列表置顶
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/top", method = RequestMethod.POST)
    @CustomDataOperAnnotation(operBizType = CustomDataOperBizTypeEnums.BUSINESS_LEADS,
            dataOperType = CustomDataOperTypeEnums.TOP)
    public R<?> top(@RequestParam Integer id) {
        return R.success();
    }


    /**
     *线索列表取消置顶
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/unTop", method = RequestMethod.POST)
    @CustomDataOperAnnotation(operBizType = CustomDataOperBizTypeEnums.BUSINESS_LEADS,
            dataOperType = CustomDataOperTypeEnums.UN_TOP)
    public R<?> unTop(@RequestParam Integer id) {
        return R.success();
    }


    /**
     * 线索初始化
     * @param file 文件
     * @return R
     * @throws IOException
     */
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    public R<?> importExcel(@RequestPart("file") MultipartFile file) throws IOException {
        initializationService.importBusinessLeads(file);
        return R.success();
    }

    /**
     *分配线索
     */
    @RequestMapping(value = "/assign", method = RequestMethod.POST)
    public R<?> assign(@RequestBody AssignLeadsDto assignLeadsDto) {
        ValidatorUtils.validate(assignLeadsDto, AddGroup.class);
        businessLeadsService.assign(assignLeadsDto);
        return R.success();
    }

    /**
     *获取单个线索详情
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getOne", method = RequestMethod.POST)
    public R<BusinessLeadsDto> getOne(@RequestParam Integer id) {
        return R.success(businessLeadsService.getOne(id));
    }

//    /**
//     *更新线索首次跟进时间
//     */
//    @NoNeedAccessAuthorization
//    @RequestMapping(value = "/updateLeadsFirstFollowTime", method = RequestMethod.POST)
//    public R<?> updateLeadsFirstFollowTime(@RequestParam Integer id) {
//        businessLeadsService.updateLeadsFirstFollowTime(id);
//        return R.success();
//    }
//
//    /**
//     *更新线索跟进状态
//     */
//    @NoNeedAccessAuthorization
//    @RequestMapping(value = "/updateLeadsFollowStatus", method = RequestMethod.POST)
//    public R<?> updateLeadsFollowStatus(@RequestParam Integer id) {
//        businessLeadsService.updateLeadsFollowStatus(id);
//        return R.success();
//    }

    /**
     *线索转商机
     */
    @NoNeedAccessAuthorization
    @RequestMapping(value = "/getLeadsToChance", method = RequestMethod.POST)
    public R<BusinessChanceDto> getLeadsToChance(@RequestParam Integer id) {
        return R.success(businessLeadsService.getLeadsToChance(id));
    }

    /**
     * 线索合并查询
     * @param leadNo
     * @return
     */
    @ResponseBody
    @RequestMapping("/getLeadMerge")
    @NoNeedAccessAuthorization
    public R<List<BusinessLeadMergeDto>> getLeadMerge(@RequestParam("leadNo") String leadNo) {
        List<BusinessLeadMergeDto> leadMerge = businessLeadsService.getLeadMerge(leadNo);
        return R.success(leadMerge);
    }

}
