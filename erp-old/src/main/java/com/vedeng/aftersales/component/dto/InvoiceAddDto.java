package com.vedeng.aftersales.component.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @program: erp.vedeng.com
 * @description:
 * @author: Pusan
 * @create: 2021-07-07 11:25
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class InvoiceAddDto implements Serializable {

    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 发票号
     */
    private String invoiceNo;

    /**
     * 发票代码
     */
    private String invoiceCode;

    /**
     * 发票属性 1纸质发票2电子发票
     */
    private String invoiceProperty;

    /**
     * 发票来源电子发票链接
     */
    private String invoiceHref;

    /**
     * 开票申请类型 字典库
     */
    private String invoiceType;

    /**
     * 发票金额
     */
    private String invoiceAmount;

    /**
     * 添加时间
     */
    private String invoiceAddTime;

    /**
     * 红蓝字 1红2蓝
     */
    private String invoiceColorType;

    /**
     * 发票作废状态 0：未作废 1：已作废
     */
    private String invoiceCancleStatus;

    /**
     * 发票是否有售后 0 无 1 有
     */
    private Integer hasAfterSaleOrder;

}
