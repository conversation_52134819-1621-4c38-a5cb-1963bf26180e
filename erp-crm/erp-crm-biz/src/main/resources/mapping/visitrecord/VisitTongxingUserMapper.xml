<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.crm.visitrecord.mapper.VisitTongxingUserMapper">

    <select id="selectTongxingUserByRecordId" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo">
        SELECT 
            t.ID as id,
            t.RECORD_ID as recordId,
            t.TONGXING_USER_ID as tongxingUserId,
            u.NUMBER AS tongxingJobNumber,
            u.USERNAME as userName,
            DETAIL.ALIAS_HEAD_PICTURE AS aliasHeadPicture,
            t.ADD_TIME as addTime,
            t.ADD_USER_ID as addUserId
        FROM 
            T_VISIT_RECORD_TONGXING_USER t
            LEFT JOIN T_USER u ON t.TONGXING_USER_ID = u.USER_ID
            left join T_USER_DETAIL  DETAIL ON DETAIL.USER_ID= t.TONGXING_USER_ID
        WHERE 
            t.RECORD_ID = #{recordId}
    </select>


    <select id="selectByVisitRecordId" resultType="com.vedeng.crm.visitrecord.domain.vo.VisitTongxingUserVo">
        SELECT
        ID,
        RECORD_ID,
        TONGXING_USER_ID,
        ADD_TIME,
        ADD_USER_ID
        FROM T_VISIT_RECORD_TONGXING_USER
        WHERE RECORD_ID = #{recordId}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO T_VISIT_RECORD_TONGXING_USER (
        RECORD_ID,
        TONGXING_USER_ID,
        ADD_TIME,
        ADD_USER_ID
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.recordId},
            #{item.tongxingUserId},
            NOW(),
            #{item.addUserId}
            )
        </foreach>
    </insert>

    <delete id="deleteNotInList">
        DELETE FROM T_VISIT_RECORD_TONGXING_USER
        WHERE RECORD_ID = #{recordId}
        AND TONGXING_USER_ID NOT IN
        <foreach collection="tongxingUserIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>

    <delete id="deleteByRecordId">
        DELETE FROM T_VISIT_RECORD_TONGXING_USER
        WHERE RECORD_ID = #{recordId}

    </delete>

</mapper> 