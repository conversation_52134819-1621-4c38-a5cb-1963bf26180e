<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.goods.dao.GoodsAttachmentGenerateMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.goods.model.GoodsAttachmentGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <id column="GOODS_ATTACHMENT_ID" jdbcType="INTEGER" property="goodsAttachmentId" />
    <result column="GOODS_ID" jdbcType="INTEGER" property="goodsId" />
    <result column="ATTACHMENT_TYPE" jdbcType="INTEGER" property="attachmentType" />
    <result column="DOMAIN" jdbcType="VARCHAR" property="domain" />
    <result column="URI" jdbcType="VARCHAR" property="uri" />
    <result column="ALT" jdbcType="VARCHAR" property="alt" />
    <result column="SORT" jdbcType="INTEGER" property="sort" />
    <result column="IS_DEFAULT" jdbcType="TINYINT" property="isDefault" />
    <result column="STATUS" jdbcType="TINYINT" property="status" />
    <result column="DISPLAY_NAME" jdbcType="VARCHAR" property="displayName" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    GOODS_ATTACHMENT_ID, GOODS_ID, ATTACHMENT_TYPE, `DOMAIN`, URI, ALT, SORT, IS_DEFAULT, 
    `STATUS`, DISPLAY_NAME
  </sql>
  <select id="selectByExample" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerateExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from T_GOODS_ATTACHMENT
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from T_GOODS_ATTACHMENT
    where GOODS_ATTACHMENT_ID = #{goodsAttachmentId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    delete from T_GOODS_ATTACHMENT
    where GOODS_ATTACHMENT_ID = #{goodsAttachmentId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerateExample">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    delete from T_GOODS_ATTACHMENT
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <selectKey keyProperty="goodsAttachmentId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_ATTACHMENT (GOODS_ID, ATTACHMENT_TYPE, `DOMAIN`, 
      URI, ALT, SORT, IS_DEFAULT, 
      `STATUS`, DISPLAY_NAME)
    values (#{goodsId,jdbcType=INTEGER}, #{attachmentType,jdbcType=INTEGER}, #{domain,jdbcType=VARCHAR}, 
      #{uri,jdbcType=VARCHAR}, #{alt,jdbcType=VARCHAR}, #{sort,jdbcType=INTEGER}, #{isDefault,jdbcType=TINYINT}, 
      #{status,jdbcType=TINYINT},#{displayName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    <selectKey keyProperty="goodsAttachmentId" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_GOODS_ATTACHMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        GOODS_ID,
      </if>
      <if test="attachmentType != null">
        ATTACHMENT_TYPE,
      </if>
      <if test="domain != null">
        `DOMAIN`,
      </if>
      <if test="uri != null">
        URI,
      </if>
      <if test="alt != null">
        ALT,
      </if>
      <if test="sort != null">
        SORT,
      </if>
      <if test="isDefault != null">
        IS_DEFAULT,
      </if>
      <if test="status != null">
        `STATUS`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="goodsId != null">
        #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="attachmentType != null">
        #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="domain != null">
        #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null">
        #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null">
        #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerateExample" resultType="java.lang.Integer">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    select count(*) from T_GOODS_ATTACHMENT
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    update T_GOODS_ATTACHMENT
    <set>
      <if test="record.goodsAttachmentId != null">
        GOODS_ATTACHMENT_ID = #{record.goodsAttachmentId,jdbcType=INTEGER},
      </if>
      <if test="record.goodsId != null">
        GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      </if>
      <if test="record.attachmentType != null">
        ATTACHMENT_TYPE = #{record.attachmentType,jdbcType=INTEGER},
      </if>
      <if test="record.domain != null">
        `DOMAIN` = #{record.domain,jdbcType=VARCHAR},
      </if>
      <if test="record.uri != null">
        URI = #{record.uri,jdbcType=VARCHAR},
      </if>
      <if test="record.alt != null">
        ALT = #{record.alt,jdbcType=VARCHAR},
      </if>
      <if test="record.sort != null">
        SORT = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.isDefault != null">
        IS_DEFAULT = #{record.isDefault,jdbcType=TINYINT},
      </if>
      <if test="record.status != null">
        `STATUS` = #{record.status,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    update T_GOODS_ATTACHMENT
    set GOODS_ATTACHMENT_ID = #{record.goodsAttachmentId,jdbcType=INTEGER},
      GOODS_ID = #{record.goodsId,jdbcType=INTEGER},
      ATTACHMENT_TYPE = #{record.attachmentType,jdbcType=INTEGER},
      `DOMAIN` = #{record.domain,jdbcType=VARCHAR},
      URI = #{record.uri,jdbcType=VARCHAR},
      ALT = #{record.alt,jdbcType=VARCHAR},
      SORT = #{record.sort,jdbcType=INTEGER},
      IS_DEFAULT = #{record.isDefault,jdbcType=TINYINT},
      `STATUS` = #{record.status,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    update T_GOODS_ATTACHMENT
    <set>
      <if test="goodsId != null">
        GOODS_ID = #{goodsId,jdbcType=INTEGER},
      </if>
      <if test="attachmentType != null">
        ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      </if>
      <if test="domain != null">
        `DOMAIN` = #{domain,jdbcType=VARCHAR},
      </if>
      <if test="uri != null">
        URI = #{uri,jdbcType=VARCHAR},
      </if>
      <if test="alt != null">
        ALT = #{alt,jdbcType=VARCHAR},
      </if>
      <if test="sort != null">
        SORT = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isDefault != null">
        IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      </if>
      <if test="status != null">
        `STATUS` = #{status,jdbcType=TINYINT},
      </if>
    </set>
    where GOODS_ATTACHMENT_ID = #{goodsAttachmentId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.goods.model.GoodsAttachmentGenerate">
    <!--
      WARNING - @mbggenerated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jun 05 11:49:39 CST 2019.
    -->
    update T_GOODS_ATTACHMENT
    set GOODS_ID = #{goodsId,jdbcType=INTEGER},
      ATTACHMENT_TYPE = #{attachmentType,jdbcType=INTEGER},
      `DOMAIN` = #{domain,jdbcType=VARCHAR},
      URI = #{uri,jdbcType=VARCHAR},
      ALT = #{alt,jdbcType=VARCHAR},
      SORT = #{sort,jdbcType=INTEGER},
      IS_DEFAULT = #{isDefault,jdbcType=TINYINT},
      `STATUS` = #{status,jdbcType=TINYINT}
    where GOODS_ATTACHMENT_ID = #{goodsAttachmentId,jdbcType=INTEGER}
  </update>
</mapper>