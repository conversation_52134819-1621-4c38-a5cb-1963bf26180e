$(function (){

    $("#btn").click(function () {
        layer.confirm("确认修改客群标签吗？",{
            btn:['确定','取消']
        },function (i){
            layer.close(i);
            $.ajax({
                type:"POST",
                url:'/tuoke/traderCustomerLabel/saveTraderCustomerLabel.do',
                data:$('form').serialize(),
                dataType:'json',
                success:function (data){
                    if(data.code==0){
                        parent.layer.msg('修改成功', {
                            icon: 1,
                            time: 1000 //2秒关闭（如果不配置，默认是3秒）
                        }, function(){
                            let index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
                            parent.layer.close(index); //再执行关闭
                            parent.location.reload(true);
                        });
                        // layer.alert(data.message, {icon: 1},
                        //     function () {
                        //         $('#close-layer').click();
                        //     });
                    }else{
                        layer.alert(data.message, {icon: 2},
                            function (index) {
                                layer.close(index);
                                return false;
                            }
                        );
                    }
                },
                error:function(data){
                    /*$(".tip-loadingNewData").remove();*/
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            });
        })
    });

    $('#cancel').on('click', function() {
        index = parent.layer.getFrameIndex(window.name);
        parent.layer.close(index); //执行关闭
    });


})