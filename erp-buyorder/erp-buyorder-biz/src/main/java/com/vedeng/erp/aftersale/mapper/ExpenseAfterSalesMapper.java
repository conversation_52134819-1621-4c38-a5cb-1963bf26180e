package com.vedeng.erp.aftersale.mapper;

import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesItemDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface ExpenseAfterSalesMapper {
    /**
     * delete by primary key
     *
     * @param expenseAfterSalesId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long expenseAfterSalesId);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    long insert(ExpenseAfterSalesEntity record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    long insertSelective(ExpenseAfterSalesEntity record);

    /**
     * select by primary key
     *
     * @param expenseAfterSalesId primary key
     * @return object by primary key
     */
    ExpenseAfterSalesEntity selectByPrimaryKey(Long expenseAfterSalesId);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ExpenseAfterSalesEntity record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ExpenseAfterSalesEntity record);

    /**
     * 查询费用售后单 只有售后单主体以及状态 不包含售后商品
     * @param buyorderExpenseId 费用单id
     * @return 费用售后单
     */
    List<ExpenseAfterSalesDto> selectByExpenseId(Integer buyorderExpenseId);

    /**
     * 查询费用售后详情页信息
     *
     * @param expenseAfterSalesId primaryKey
     * @return ExpenseAfterSalesDto
     */
    ExpenseAfterSalesDto getExpenseAfterSalesDetail(Long expenseAfterSalesId);

    /**
     * 根据采购费用售后单号查询采购费用信息
     * <AUTHOR>
     * @param expenseAfterSalesNo
     * @return
     */
    ExpenseAfterSalesDto queryInfoByNo(String expenseAfterSalesNo);

    /**
     * 查询费用单关联的所有已完结的 费用售后单id集合
     *
     * @param buyorderExpenseId 费用单id
     * @return 费用售后单id集合
     */
    List<Long> getAllCompletedAfterSales(@Param("buyorderExpenseId") Integer buyorderExpenseId);
}