package com.vedeng.crm.task.mapper;
import com.vedeng.crm.task.domain.dto.TaskItemDto;
import com.vedeng.crm.task.domain.dto.TaskReminder;
import org.apache.ibatis.annotations.Param;
import java.util.Date;

import com.vedeng.crm.task.domain.dto.TaskDto;
import com.vedeng.crm.task.domain.dto.TaskQueryDto;
import com.vedeng.crm.task.domain.entity.TaskItemEntity;
import com.vedeng.crm.task.domain.vo.MyTaskVo;

import java.util.List;

public interface TaskItemMapper {
    int deleteByPrimaryKey(Long taskItemId);

    int insert(TaskItemEntity record);

    int insertSelective(TaskItemEntity record);

    TaskItemEntity selectByPrimaryKey(Long taskItemId);

    int updateByPrimaryKeySelective(TaskItemEntity record);

    int updateByPrimaryKey(TaskItemEntity record);

    List<TaskItemEntity> findAllByTaskId(@Param("taskId")Long taskId);

    List<TaskItemEntity> findAllByTaskIdNotDone(@Param("taskId")Long taskId);



    List<TaskReminder> findDeadlineReminder(@Param("productSchemeWarningTime") Integer productSchemeWarningTime,
                                            @Param("itemInquiryWarningTime") Integer itemInquiryWarningTime,
                                            @Param("generalInquiryWarningTime") Integer generalInquiryWarningTime,
                                            @Param("quoteWarningTime") Integer quoteWarningTime);
    
    int updateIsDeadlineReminderByTaskItemId(@Param("updatedIsDeadlineReminder")Integer updatedIsDeadlineReminder,@Param("taskItemId")Long taskItemId);

	int updateIsTimeoutReminderByTaskItemId(@Param("updatedIsTimeoutReminder")Integer updatedIsTimeoutReminder,@Param("taskItemId")Long taskItemId);

    List<TaskReminder> findTimeoutReminder();

    List<TaskReminder> findAlertBussinessChanceTask();

    int countMyTodoTimeout(@Param("subUserIds") List<Integer> subUserIds);

    int countMyInitiativeTimeout(@Param("subUserIds") List<Integer> subUserIds);

    List<TaskItemDto> findByBizIdAndBizTypeAndMainTaskType(@Param("bizId")Integer bizId, @Param("bizType")Integer bizType);

    List<TaskItemDto> findByBizIdAndBizTypeForCloseBiz(@Param("bizId")Integer bizId, @Param("bizTypeList")List<Integer> bizTypeList);


}