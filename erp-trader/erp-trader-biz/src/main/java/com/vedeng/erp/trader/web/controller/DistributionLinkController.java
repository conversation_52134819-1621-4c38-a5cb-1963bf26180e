package com.vedeng.erp.trader.web.controller;

import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.base.ExceptionController;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.ModelAndView;

/**
 * 经销链路
 *
 */
@Controller
@ExceptionController
@RequestMapping("/distributionLink")
public class DistributionLinkController {

    @RequestMapping("/distribution/link")
    public ModelAndView distributionLink() {
        ModelAndView mv = new ModelAndView("vue/view/tradercustomer/distribution_link_terminal");
        return mv;
    }
}
