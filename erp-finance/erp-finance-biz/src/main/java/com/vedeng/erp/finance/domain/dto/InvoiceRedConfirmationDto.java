package com.vedeng.erp.finance.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 发票红字确认单
 * @date 2023/10/13 9:33
 */
@Getter
@Setter
public class InvoiceRedConfirmationDto extends BaseDto {

    private Integer invoiceRedConfirmationId;

    /**
     * 售后业务类型
     * 1 销售售后
     * 2 采购售后
     * 3 采购费用售后
     */
    private Integer afterSaleBusinessOrderType;

    /**
     * 业务类型
     */
    private String afterSaleTitle;

    /**
     * 源单业务单据id
     */
    private Integer businessOrderId;

    /**
     * 源单业务单据号
     */
    private String businessOrderNo;

    /**
     * 售后业务id
     */
    private Integer afterSaleBusinessOrderId;

    /**
     * 售后业务单号
     */
    private String afterSaleBusinessOrderNo;


    /**
     * 蓝票号码
     */
    private String blueInvoiceNo;

    /**
     * 蓝票开票时间
     */
    private Date blueInvoiceTime;


    /**
     * 蓝票id
     */
    private Integer blueInvoiceId;

    /**
     * 蓝票类型
     */
    private Integer blueInvoiceType;

    /**
     * 状态
     * 0 初始化
     * 1 已申请
     * 2 已确认
     * 3 已开票
     * 4 已作废
     */
    private Integer redConfirmationStatus;

    /**
     * 红冲范围
     * 0全部
     * 1部分
     */
    private Integer redConfirmationScope;

    /**
     * 销方名称
     */
    private String seller;

    /**
     * 销售方税号
     */
    private String sellerTaxNumber;

    /**
     * 购方名称
     */
    private String buyer;

    /**
     * 购买方税号
     */
    private String buyerTaxNumber;

    /**
     * 申请日期（调用申请后）
     */
    private Date applyTime;

    /**
     * 红字申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 红字申请税额
     */
    private BigDecimal applyTaxAmount;

    /**
     * 红字申请价税合计
     */
    private BigDecimal applyPricePlusTaxes;

    /**
     * 红字申请原因
     * 01 开票有误
     * 02 销货退回
     * 03 服务中止
     * 04 销售折让
     */
    private String applyReason;

    /**
     * 录入方身份类型
     * 0 销方
     * 1 购方
     */
    private Integer inputPersonType;

    /**
     * 购销方
     * 0-销售方
     *
     * 1-购买方
     */
    private Integer sellerBuyerType;


    /**
     * 红字确认单唯一标识符
     */
    private String uuid;

    /**
     * 税务局定义的红字确认单单据编号
     */
    private String invoiceRedConfirmationNo;

    /**
     * 税务局定义的红字确认单状态
     * 01 无需确认
     * 02 销方录入待购方确认
     * 03 购方录入待销方确认
     * 04 购销双方已确认
     * 05 作废（销方录入购方否认）
     * 06 作废（购方录入销方否认）
     * 07 作废（超 72 小时未确认）
     * 08 作废（发起方已撤销）
     * 09 作废（确认后撤销）
     */
    private String taxesRedConfirmationStatus;

    /**
     * 确认日期
     */
    private Date confirmTime;

    /**
     * 确认方身份类型（申请方为销方，确认方为购方；申请方为购方，确认方为销方）
     * 0 销方
     * 1 购方
     */
    private Integer confirmPersonType;

    /**
     * 增值税发票状态
     * 00 已勾选未确认
     * 01 已确认
     * 03 未勾选
     */
    private String valueAddedTaxStatus;

    /**
     * 消费税发票状态
     * 00 未勾选
     * 01 已勾选
     */
    private String consumptionTaxStatus;

    /**
     * 发票入账状态
     * 00 未入账
     * 01 已入账
     */
    private String enterAccountStatus;

    /**
     * 已开具红字发票标志
     * Y 已开具
     * N 未开具
     */
    private String alreadyRedInvoiceFlag;

    /**
     * 红票号码
     */
    private String redInvoiceNo;

    /**
     * 红票id
     */
    private Integer redInvoiceId;

    /**
     * 红票开票日期
     */
    private Date openRedInvoiceTime;

    /**
     * 税金系统接口数据更新时间
     */
    private Date apiDataUpdateTime;

    /**
     * 0-1，1-2，2-3
     */
    private Integer remainingTimeOfVoid;

    /**
     * 作废剩余时间
     */
    private Integer remainingTime;

    /**
     * 剩余可红字金额 (不含税)
     */
    private BigDecimal surplusAmount;

    /**
     * 剩余红字税额
     */
    private BigDecimal surplusUaxAmount;

    /**
     * 剩余红字价税合计
     */
    private BigDecimal surplusPricePlusTaxes;

    /**
     * 明细
     */
    private List<InvoiceRedConfirmationItemDto> invoiceRedConfirmationItemDtoList = new ArrayList<>();

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 按钮
     */
    List<InvoiceRedConfirmationServiceViewButton> buttonList;

    /**
     * 蓝票开票日期
     */
    private Date openInvoiceTime;

    /**
     * 购买方纳税人识别号(客户-财务信息：税务登记号)
     */
    private String taxNum;


    /**
     * 状态 集合
     * 0 初始化
     * 1 已申请
     * 2 已确认
     * 3 已开票
     * 4 已作废
     */
    private List<Integer> redConfirmationStatusList;

}
