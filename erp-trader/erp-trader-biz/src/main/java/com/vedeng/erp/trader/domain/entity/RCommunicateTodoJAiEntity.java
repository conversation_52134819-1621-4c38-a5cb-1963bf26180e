package com.vedeng.erp.trader.domain.entity;

import com.vedeng.common.mybatis.domain.BaseEntity;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * 通话待办关联AI结果表
 */
@Getter
@Setter
public class RCommunicateTodoJAiEntity extends BaseEntity {
    /**
     * 主键
     */
    private Integer communicateInfoId;

    /**
     * 交易者ID
     */
    private Integer traderId;

    /**
     * 沟通记录ID
     */
    private Integer communicateRecordId;

    /**
     * 商机ID
     */
    private Integer businessId;

    /**
     * 商机编号
     */
    private String businessNo;

    /**
     * 是否生成商机
     */
    private Integer createBusinessChange;

    /**
     * 是否更新客户标签
     */
    private Integer updateTraderSign;

    /**
     * 客户标签
     */
    private String traderSign;

    /**
     * 是否同步联系人职位信息
     */
    private Integer syncContactPosition;

    /**
     * 客户联系人ID
     */
    private Integer customerContactId;

    /**
     * 客户联系人名称
     */
    private String customerContactUsername;

    /**
     * 客户联系人号码
     */
    private String customerContactPhone;

    /**
     * 联系人职位
     */
    private String customerContactPosition;
}