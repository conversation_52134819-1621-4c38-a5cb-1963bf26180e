package com.vedeng.customerbillperiod.constant;

/**
 * @Author: daniel
 * @Date: 2021/7/26 15 41
 * @Description: 客户账期申请操作类型
 */
public enum CustomerBillPeriodApplyTypeEnum {

    /**
     * 新增账期申请
     */
    ADD(1,"新增"),

    MODIFY(2,"修改")
    ;

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    CustomerBillPeriodApplyTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
