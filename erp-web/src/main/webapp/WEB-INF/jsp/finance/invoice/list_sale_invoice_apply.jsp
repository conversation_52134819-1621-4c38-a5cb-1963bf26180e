<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="开票申请列表" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src='<%=basePath%>static/js/finance/invoice/list_sale_invoice_apply.js?rnd=${resourceVersionKey}'></script>
<link rel="stylesheet" type="text/css" href="${pageContext.request.contextPath}/static/layui/css/layui.css">
<script src="/webjars/ezadmin/plugins/layui/layui.js" charset="utf-8"></script>
<script type="text/javascript"
		src="${pageContext.request.contextPath}/webjars/ezadmin/plugins/cascader/xm-select.js"></script>
<script src="/webjars/ezadmin/plugins/layui/layui.js"></script>

<link href="/webjars/ezadmin/plugins/layui/css/layui.css?v=2.7.6" rel="stylesheet">

<link href="/webjars/ezadmin/plugins/viewer/viewer.min.css" rel="stylesheet">
<%--
<link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet">
--%>
<link href="/webjars/ezadmin/layui/css/ezlist.css?v=1711100573488" rel="stylesheet">
<link href="/webjars/ezadmin/plugins/cascader/cascader.css" rel="stylesheet">
<script src="/webjars/ezadmin/plugins/cascader/cascader.js?1=1" type="text/javascript" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_firstletter.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyin_dict_notone.js" ></script>
<script src="/webjars/ezadmin/plugins/pingyin/pinyinUtil.js" ></script>
<style>
	#ID-demo-layer-direction-r {
		padding-left: 4px;
		padding-top: 20px;
	}
	#ID-layer-invoice-limit {
		padding-top: 20px;
		padding-left: 8px;
		font-size: 16px;
		display: flex;
		flex-direction: column;
	}
	.xm-option-icon {
		height: auto;
		background: none;
	}
	.table {
		width: 100%;
		table-layout: fixed;
	}
	/* 固定"操作"列 */
	td:last-child {
		position: sticky;
		right: 0;
		background-color: #fff; /* 可选，背景颜色，使列在右侧固定时更清晰 */
		z-index: 2; /* 确保列位于上方 */
	}

	/* 固定"操作"列 */
	th:last-child {
		position: sticky;
		right: 0;
		background-color: #E5E5E5; /* 可选，背景颜色，使列在右侧固定时更清晰 */
		z-index: 2; /* 确保列位于上方 */
	}
	xm-select {
		min-height: 27px;
		line-height: 36px;
	}

	.loading-icon {
		border: 5px solid #f3f3f3;
		border-radius: 50%;
		border-top: 5px solid #3498db;
		width: 45px;
		height: 45px;
		animation: spin 2s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	li i{
		height: auto;
		background: none;
	}
	a, a:hover, a:focus {
		text-decoration: none;
		outline-style: none;
		color: #3384ef;
		cursor: pointer;
	}

	.el-input__inner{
		line-height:30px;
		padding-left: 5px;    
		height: 26px;
		border: 1px solid #ccc;
		border-radius: 2px;
	}
	.el-cascader{
		width: 180px;
	}


</style>
<input type="hidden" id="ticketReasonEqualList" value="${invoiceApply.ticketReasonEqualList}">
<script>
	function showAlert() {
		// 使用layer弹出层显示提示信息
		layer.alert('售后开票暂不支持修改', {
			title: '提示', // 弹窗标题
			icon: 2, // 图标，根据需要选择，例如：0（感叹号），1（正确），2（错误），等等...
			skin: 'layer-ext-moon' // 可选，添加自定义样式
		});
	}
</script>
<div class="main-container">
	<div class="list-pages-search">
		<form method="post" id="search" action="<%=basePath%>finance/invoice/getSaleInvoiceApplyListPage.do">
			<ul style="display: flex;flex-wrap: wrap">
				<li>
					<label class="infor_name">关联单号</label>
					<input type="text" class="input-middle" name="saleorderNo" id="saleorderNo" value="${invoiceApply.saleorderNo}" />
				</li>
				<li>
					<label class="infor_name">客户名称</label>
					<input type="text" class="input-middle" name="traderName" id="traderName" value="${invoiceApply.traderName}" />
				</li>
				<li>
					<label class="infor_name">是否标记</label>
					<select class="input-middle" name="isSign" id="isSign">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.isSign eq 0}">selected</c:if> value="0">未标记</option>
						<option <c:if test="${invoiceApply.isSign eq 1}">selected</c:if> value="1">已标记</option>
					</select>
				</li>
				<li>
					<label class="infor_name">提前开票</label>
					<select class="input-middle" name="isAdvance" id="isAdvance">
						<option value="">全部</option>
						<option <c:if test="${invoiceApply.isAdvance eq 0}">selected</c:if> value="0">否</option>
						<option <c:if test="${invoiceApply.isAdvance eq 1}">selected</c:if> value="1">是</option>
					</select>
				</li>
				<li>
					<label class="infor_name">收款状态</label>
					<select class="input-middle" name="paymentStatus" id="paymentStatus">
						<option value="">全部</option>
						<option <c:if test="${invoiceApply.paymentStatus eq 0}">selected</c:if> value="0">未收款</option>
						<option <c:if test="${invoiceApply.paymentStatus eq 1}">selected</c:if> value="1">部分收款</option>
						<option <c:if test="${invoiceApply.paymentStatus eq 2}">selected</c:if> value="2">已收款</option>
					</select>
				</li>
				<li>
					<label class="infor_name">发货状态</label>
					<select class="input-middle" name="deliveryStatus" id="deliveryStatus">
						<option value="">全部</option>
						<option <c:if test="${invoiceApply.deliveryStatus eq 0}">selected</c:if> value="0">未发货</option>
						<option <c:if test="${invoiceApply.deliveryStatus eq 1}">selected</c:if> value="1">部分发货</option>
						<option <c:if test="${invoiceApply.deliveryStatus eq 2}">selected</c:if> value="2">全部发货</option>
					</select>
				</li>
				<li>
					<label class="infor_name">订单来源</label>
					<select class="input-middle" name="orderSourceType" id="orderSourceType">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.orderSourceType eq 0}">selected</c:if> value="0">线上</option>
						<option <c:if test="${invoiceApply.orderSourceType eq 1}">selected</c:if> value="1">线下</option>
					</select>
				</li>
				<li>
					<label class="infor_name">合同状态</label>
					<select class="input-middle" name="contractVerifyStatus" id="contractVerifyStatus">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.contractVerifyStatus eq 5}">selected</c:if> value="5">未上传</option>
						<option <c:if test="${invoiceApply.contractVerifyStatus eq 4}">selected</c:if> value="4">待审核</option>
						<option <c:if test="${invoiceApply.contractVerifyStatus eq 0}">selected</c:if> value="0">审核中</option>
						<option <c:if test="${invoiceApply.contractVerifyStatus eq 1}">selected</c:if> value="1">审核通过</option>
						<option <c:if test="${invoiceApply.contractVerifyStatus eq 2}">selected</c:if> value="2">审核不通过</option>
					</select>
				</li>
				<li>
					<label class="infor_name">确认单状态</label>
					<select class="input-middle" name="confirmationFormAudit" id="confirmationFormAudit">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.confirmationFormAudit eq 0}">selected</c:if> value="0">待提交审核</option>
						<option <c:if test="${invoiceApply.confirmationFormAudit eq 1}">selected</c:if> value="1">审核中</option>
						<option <c:if test="${invoiceApply.confirmationFormAudit eq 2}">selected</c:if> value="2">审核通过</option>
						<option <c:if test="${invoiceApply.confirmationFormAudit eq 3}">selected</c:if> value="3">审核不通过</option>
					</select>
				</li>
				<li>
					<label class="infor_name">账期支付</label>
					<select class="input-middle" name="haveAccountPeriod" id="haveAccountPeriod">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.haveAccountPeriod eq 0}">selected</c:if> value="0">否</option>
						<option <c:if test="${invoiceApply.haveAccountPeriod eq 1}">selected</c:if> value="1">是</option>
					</select>
				</li>
				<li>
					<label class="infor_name">售后状态</label>
					<select class="input-middle" name="serviceStatus" id="serviceStatus">
						<option value="">全部</option>
						<option <c:if test="${invoiceApply.serviceStatus eq 0}">selected</c:if> value="0">无售后</option>
						<option <c:if test="${invoiceApply.serviceStatus eq 1}">selected</c:if> value="1">售后中</option>
						<option <c:if test="${invoiceApply.serviceStatus eq 2}">selected</c:if> value="2">售后完成</option>
						<option <c:if test="${invoiceApply.serviceStatus eq 3}">selected</c:if> value="3">售后关闭</option>
					</select>
				</li>
				<li>
					<label class="infor_name">在线催办</label>
					<select class="input-middle" name="urage" id="urage">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.urage eq 0}">selected</c:if> value="0">否</option>
						<option <c:if test="${invoiceApply.urage eq 1}">selected</c:if> value="1">是</option>
					</select>
				</li>
				<li>
					<label class="infor_name">申请方式</label>
					<select class="input-middle" name="applyMethod" id="applyMethod">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.applyMethod eq 0}">selected</c:if> value="0">销售手动申请</option>
						<option <c:if test="${invoiceApply.applyMethod eq 1}">selected</c:if> value="1">系统自动推送</option>
						<option <c:if test="${invoiceApply.applyMethod eq 2}">selected</c:if> value="2">票货同行物流部申请</option>
						<option <c:if test="${invoiceApply.applyMethod eq 3}">selected</c:if> value="3">客户在线申请开票</option>
						<option <c:if test="${invoiceApply.applyMethod eq 4}">selected</c:if> value="4">售后手动申请</option>
					</select>
				</li>
				<li>
					<label class="infor_name">发票形式</label>
					<select class="input-middle" name="invoiceProperty" id="invoiceProperty">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.invoiceProperty eq 1}">selected</c:if> value="1">纸质发票</option>
						<option <c:if test="${invoiceApply.invoiceProperty eq 2}">selected</c:if> value="2">电子发票</option>
						<option <c:if test="${invoiceApply.invoiceProperty eq 3}">selected</c:if> value="3">数电发票</option>
					</select>
				</li>
				
				<li>
					<div style="display: flex;  flex-wrap: nowrap;">
						<label class="infor_name">销售部门</label>
						<div class="layui-input-inline">
							<input value="${invoiceApply.orgId }" type="hidden" name="orgId" id="orgId" class="hidden-reset ez-laycascader" ez_url="/system/org/orgList.do" ez_value="orgId" ez_label="orgName" ez_children="child" autocomplete="off">
						</div>
					</div>
				</li>

				<li>
					<label class="infor_name">发票类型</label>
					<select class="input-middle" name="invoiceType" id="invoiceType">
						<option value="-1">全部</option>
						<c:forEach var="invoiceType" items="${invoiceTypeList}">
							<option value="${invoiceType.sysOptionDefinitionId}"
									<c:if test="${invoiceApply.invoiceType eq invoiceType.sysOptionDefinitionId}">selected</c:if>>
									${invoiceType.title}
							</option>
						</c:forEach>
					</select>
				</li>
				<li>
					<label class="infor_name">申请来源</label>
					<select class="input-middle" name="type" id="type">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.type eq 505}">selected</c:if> value="505">销售开票</option>
						<option <c:if test="${invoiceApply.type eq 504}">selected</c:if> value="504">售后开票</option>
					</select>
				</li>
				<li>
					<label class="infor_name">开票信息</label>
					<select class="input-middle" name="invoiceInfoType" id="invoiceInfoType">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.invoiceInfoType eq 0}">selected</c:if> value="0">标准开票信息</option>
						<option <c:if test="${invoiceApply.invoiceInfoType eq 1}">selected</c:if> value="1">自定开票信息</option>
					</select>
				</li>
				<li>
					<label class="infor_name">票面备注</label>
					<select class="input-middle" name="invoiceComments" id="invoiceComments">
						<!-- add by Tomcat.Hui 2019/10/17 19:34 .Desc:VDERP-1342 财务管理-开票申请列表：筛选问题 . start -->
						<option value="-1">全部</option>
						<!-- add by Tomcat.Hui 2019/10/17 19:34 .Desc:VDERP-1342 财务管理-开票申请列表：筛选问题 . start -->
						<option <c:if test="${invoiceApply.invoiceComments eq 0}">selected</c:if> value="0">有</option>
						<option <c:if test="${invoiceApply.invoiceComments eq 1}">selected</c:if> value="1">无</option>
					</select>
				</li>
				<li>
					<label class="infor_name">开票留言</label>
					<select class="input-middle" name="orderComments" id="orderComments">
						<option value="-1">全部</option>
						<option <c:if test="${invoiceApply.orderComments eq 0}">selected</c:if> value="0">有</option>
						<option <c:if test="${invoiceApply.orderComments eq 1}">selected</c:if> value="1">无</option>
					</select>
				</li>
				<li style="margin-left: 42px">
					<label>不通过原因</label>
					<select class="input-middle" name="ticketReason" id="ticketReason" style="margin-left: 5px;width: 59px;">
						<option <c:if test="${invoiceApply.ticketReason eq 'equal'}">selected</c:if> value="equal">等于</option>
						<option <c:if test="${invoiceApply.ticketReason eq 'contain'}">selected</c:if> value="contain">包含</option>
						<option <c:if test="${invoiceApply.ticketReason eq 'notContain'}">selected</c:if> value="notContain">不包含</option>
					</select>
				</li>
				<li style="width: 104px">
					<div id="ticketReasonEqual" name = "ticketReasonEqual"></div>
				</li>

			</ul>
			<div class="tcenter">
				<span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();" id="searchSpan">搜索</span>
				<span class="bt-small bg-light-blue bt-bg-style mr20" onclick="resetPage();">重置</span>

				<span class="bt-small bg-light-blue bt-bg-style pop-new-data"
					  layerparams='{"width":"700px","height":"550px","title":"集中开票客户维护","link":"./getCollectInvoiceTraderName.do"}'>
						集中开票客户维护
					</span>
				<span class="bg-light-blue bt-bg-style bt-small pop-new-data" layerParams='{"width":"400px","height":"200px","title":"批量标记","link":"./uplodebatchsign.do"}'>批量标记</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="invoiceAutoOpen();">自动开票</span>
				<span class="bg-light-blue bt-bg-style bt-small" onclick="showInvoiceLimit()">查看开票额度</span>
				<a class="addtitle" href="javascript:void(0);"
				   tabTitle='{"num":"getInvoiceApplyConfiguration", "link":"/invoice/invoiceApply/getInvoiceApplyConfiguration.do", "title":"开票配置"}'
				   id="invoiceConfigurationLink" style="text-decoration: none;">
					<span class="bg-light-blue bt-bg-style bt-small" id="invoiceConfigurationSpan">开票配置</span>
				</a>
<%--				<span class="layui-icon layui-icon-set" onclick="showCustomIcon()" style="display: inline-block;float: right;padding-top: 3px"></span>--%>
			</div>
		</form>
	</div>
	<div class="list-page">
		<div class="fixdiv">
			<div style="width: 3430px;" class='superdiv'>
				<table class="table table-bordered table-striped table-condensed table-centered" id="invoice_apply_list_tab">
					<thead>
					<tr>
						<th class="wid6">
							<input type="checkbox" name="checkAllOpt" style="vertical-align:middle;" onchange="checkAllOpt(this);">
						</th>
						<th class="wid4">序号</th>
						<th class="wid12">关联单号</th>
						<th class="wid22">客户名称</th>
						<th class="wid10">订单实际金额</th>
						<th class="wid12">申请开票金额</th>
						<th class="wid12">发票类型</th>
						<th class="wid10">开票信息</th>
						<th class="wid22">票面备注</th>
						<th class="wid12">开票留言</th>
						<th class="wid12">提前申请原因</th>
						<th class="wid12">不通过原因</th>
						<th class="wid7">账期支付</th>
						<th class="wid8">售后状态</th>
						<th class="wid8">收款状态</th>
						<th class="wid8">发货状态</th>
						<th class="wid8">收货状态</th>
						<th class="wid7">合同状态</th>
						<th class="wid8">确认单状态</th>
						<th class="wid7">提前开票</th>
						<th class="wid7">是否标记</th>
						<th class="wid10">发票形式</th>
						<th class="wid7">在线催办</th>
						<th class="wid10">申请来源</th>
						<th class="wid12">申请方式</th>
						<th class="wid8">申请时间</th>
						<th class="wid8">申请人</th>
						<th class="wid7">客户类型</th>
						<th class="wid12">销售部门</th>
						<th class="wid8">归属销售</th>
						<th class="wid9">申请生效时间</th>
						<th class="wid8">订单来源</th>
						<th class="wid30">操作</th>
					</tr>
					</thead>
					<tbody>
					<c:set var="pageAmount" value="0"></c:set><!-- 当前页总额 -->
					<c:set var="pageNum" value="0"></c:set><!-- 当前页记录数 -->
					<c:forEach var="list" items="${openInvoiceApplyList}" varStatus="num">
						<c:set var="pageNum" value="${pageNum + 1}"></c:set>
						<tr>
							<td><input type="checkbox" name="checkName" value="${list.invoiceApplyId}" isSign="${list.isSign}" isAuto="${list.isAuto}" onchange="checkedOnly(this)" amount="${list.totalAmount}"/></td>
							<td>${num.count}</td>
							<!-- 关联单号-->
							<td>
								<c:if test="${list.type == 505}">
								<a class="addtitle" style="color:#3384ef" href="javascript:void(0);" tabtitle='{"num":"viewfinancesaleorder${list.relatedId}","link":"./finance/invoice/viewSaleorder.do?saleorderId=${list.relatedId}","title":"订单信息"}'>${list.saleorderNo}</a>
								</c:if>
								<c:if test="${list.type == 504}">
									<a class="addtitle" style="color:#3384ef" href="javascript:void(0);" tabtitle='{"num":"viewfinancesaleorder${list.relatedId}","link":"/finance/after/getFinanceAfterSaleDetail.do?afterSalesId=${list.relatedId}&subjectType=${list.afterSaleSubjectType}&type=${afterSaleType}","title":"订单信息"}'>${list.saleorderNo}</a>
								</c:if>
							</td>
							<!-- 客户名称-->
							<td>
								<a class="addtitle" style="color:#3384ef" href="javascript:void(0);" tabtitle='{"num":"viewfinancecustomer${list.traderId}", "link":"./trader/customer/baseinfo.do?traderId=${list.traderId}", "title":"客户信息"}'>${list.traderName}</a>
							</td>
							<!-- 订单实际金额-->
							<td>
								<fmt:formatNumber type="number" value="${list.totalAmount}" pattern="0.00" maxFractionDigits="2" />
							</td>
							<!-- 申请开票金额-->
							<td><fmt:formatNumber type="number" value="${empty list.applyAmount ? list.totalAmount : list.applyAmount}" pattern="0.00" maxFractionDigits="2" /></td><!-- -->
							<c:set var="pageAmount" value="${(empty list.applyAmount ? list.totalAmount : list.applyAmount) + pageAmount}"></c:set>
							<!-- 发票类型 -->
							<td>${list.invoiceTypeStr}</td>
							<!-- 开票信息-->
							<td>
								<c:choose>
									<c:when test="${list.invoiceInfoType eq 0}">标准开票信息</c:when>
									<c:when test="${list.invoiceInfoType eq 1}">自定开票信息</c:when>
									<c:otherwise>--</c:otherwise>
								</c:choose>
							</td>
							<!--票面备注 -->
							<td>${list.invoiceComments}</td>
							<!-- 开票留言-->
							<td>${list.orderComments}</td>
							<!-- 提前申请原因-->
							<td>${list.advanceValidReason}</td>
							<!--开票校验不通过原因 -->
							<td>${list.openNoPassRuleCode}</td>
							<!-- 账期支付-->
							<td>
								<c:if test="${list.type == 505}">
									<c:choose>
										<c:when test="${list.haveAccountPeriod eq 1}">是</c:when>
										<c:otherwise>否</c:otherwise>
									</c:choose>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 售后状态-->
							<td>
								<c:if test="${list.type == 505}">
									<c:choose>
										<c:when test="${list.serviceStatus eq 0}">无</c:when>
										<c:when test="${list.serviceStatus eq 1}">售后中</c:when>
										<c:when test="${list.serviceStatus eq 2}">售后完成</c:when>
										<c:otherwise>售后关闭</c:otherwise>
									</c:choose>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 收款状态-->
							<td><!-- -->
								<c:choose>
									<c:when test="${list.paymentStatus eq 0}">未收款</c:when>
									<c:when test="${list.paymentStatus eq 1}">部分收款</c:when>
									<c:otherwise>已收款</c:otherwise>
								</c:choose>
							</td>
							<!-- 发货状态-->
							<td><!-- -->
								<c:if test="${list.type == 505}">
									<c:choose>
										<c:when test="${list.deliveryStatus eq 0}">未发货</c:when>
										<c:when test="${list.deliveryStatus eq 1}">部分发货</c:when>
										<c:otherwise>全部发货</c:otherwise>
									</c:choose>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 收货状态-->
							<td>
								<c:if test="${list.type == 505}">
									<c:choose>
										<c:when test="${list.arrivalStatus eq 0}">未收货</c:when>
										<c:when test="${list.arrivalStatus eq 1}">部分收货</c:when>
										<c:otherwise>全部收货</c:otherwise>
									</c:choose>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 合同状态-->
							<td>
								<c:if test="${list.type == 505}">
									<c:choose>
										<c:when test="${list.contractVerifyStatus eq 4}">待审核</c:when>
										<c:when test="${list.contractVerifyStatus eq 0}">审核中</c:when>
										<c:when test="${list.contractVerifyStatus eq 1}">审核通过</c:when>
										<c:when test="${list.contractVerifyStatus eq 2}">审核不通过</c:when>
										<c:when test="${list.contractVerifyStatus eq 5}">未上传</c:when>
										<c:otherwise>--</c:otherwise>
									</c:choose>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 确认单状态-->
							<td>
								<c:if test="${list.type == 505}">
									<c:choose>
										<c:when test="${list.confirmationFormAudit eq 0}">待提交审核</c:when>
										<c:when test="${list.confirmationFormAudit eq 1}">审核中</c:when>
										<c:when test="${list.confirmationFormAudit eq 2}">审核通过</c:when>
										<c:when test="${list.confirmationFormAudit eq 3}">审核不通过</c:when>
										<c:otherwise>--</c:otherwise>
									</c:choose>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 提前开票-->
							<td>
								<c:choose>
									<c:when test="${list.isAdvance eq 0}">否</c:when>
									<c:otherwise>是</c:otherwise>
								</c:choose>
							</td>
							<!-- 是否标记-->
							<td>
								<c:choose>
									<c:when test="${list.isSign eq 0}">未标记</c:when>
									<c:otherwise>已标记</c:otherwise>
								</c:choose>
							</td>
							<!-- 发票形式-->
							<td>
								<c:choose>
									<c:when test="${list.invoiceProperty eq 1}">纸质发票</c:when>
									<c:when test="${list.invoiceProperty eq 2}">电子发票</c:when>
									<c:when test="${list.invoiceProperty eq 3}">数电发票</c:when>
									<c:otherwise>--</c:otherwise>
								</c:choose>
							</td>
							<!-- 在线催办-->
							<td>
								<c:choose>
									<c:when test="${list.urage eq 0}">未催办</c:when>
									<c:otherwise>已催办</c:otherwise>
								</c:choose>
							</td>
							<!-- 申请来源-->
							<td>
								<c:choose>
									<c:when test="${list.type eq 505}">销售开票</c:when>
									<c:when test="${list.type eq 504}">售后开票</c:when>
									<c:otherwise>--</c:otherwise>
								</c:choose>
							</td>
							<!-- 申请方式-->
							<td>
								<c:if test="${list.applyMethod eq 0}">销售手动申请</c:if>
								<c:if test="${list.applyMethod eq 1}">系统自动推送</c:if>
								<c:if test="${list.applyMethod eq 2}">票货同行物流部申请</c:if>
								<c:if test="${list.applyMethod eq 3}">客户在线申请开票</c:if>
								<c:if test="${list.applyMethod eq 4}">售后手动申请</c:if>
							</td>
							<!-- 申请时间-->
							<td><!-- -->
								<c:if test="${list.creator eq 0}"><date:date value="${list.arrivalTime}" /></c:if>
								<c:if test="${list.creator ne 0}"><date:date value="${list.addTime}" /></c:if>
							</td>
							<!-- 申请人 -->
							<td>
								<!--  add by Tomcat.Hui 2019/11/23 9:42 .Desc:  VDERP-1325 分批开票 开票人为空显示自动申请. start -->
								<c:choose>
									<c:when test="${list.applyMethod eq 1}">
										自动申请
									</c:when>
									<c:when test="${list.applyMethod eq 3}">
										${list.signerName}
									</c:when>
									<c:otherwise>
										<c:forEach varStatus="userNum" var="user" items="${userList}">
											<c:if test="${list.creator eq user.userId}">
												${user.username}
											</c:if>
										</c:forEach>
									</c:otherwise>
								</c:choose>
								<!--  add by Tomcat.Hui 2019/11/23 9:42 .Desc:   VDERP-1325 分批开票 开票人为空显示自动申请. end -->
							</td>
							<!-- 客户类型-->
							<td>
								<c:if test="${list.customerNature eq 465}">分销</c:if>
								<c:if test="${list.customerNature eq 466}">终端</c:if>
							</td>
							<!-- 销售部门-->
							<td>
								<c:if test="${list.type == 505}">
									<c:forEach var="org" items="${traderUserList}" varStatus="num">
										<c:if test="${list.traderId eq org.traderId}">${org.orgName}</c:if>
									</c:forEach>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!-- 归属销售-->
							<td>
								<c:if test="${list.type == 505}">
									<c:forEach items="${traderUserList}" var="user" varStatus="status">
										<c:if test="${user.traderId eq list.traderId}">
											${user.username}
										</c:if>
									</c:forEach>
								</c:if>
								<c:if test="${list.type == 504}">
									""
								</c:if>
							</td>
							<!--申请生效时间 -->
							<td>${list.applicationEffectiveTime}</td>
							<td>${list.orderSourceTypeStr}</td>
							<!--  add by Tomcat.Hui 2019/11/23 9:42 .Desc:  VDERP-1325 分批开票 增加字段. start -->
							<!--  add by Tomcat.Hui 2019/11/23 9:42 .Desc:  VDERP-1325 分批开票 增加字段. end -->
							<!-- add by Tomcat.Hui 2019/9/3 19:34 .Desc:VDERP-1214 开票申请界面优化 . start -->
							<!-- add by Tomcat.Hui 2019/9/3 19:34 .Desc:VDERP-1214 开票申请界面优化 . end -->
							<td><!-- -->
								<c:if test="${list.invoiceStStatus ne 1}">
									<!-- 此处审核费提前开票审核  和  提前开票申请已通过的记录，故：isAdvance为0，在js中作区分 -->
									<span class="pop-new-data delete"  layerparams='{"width":"600px","height":"220px","title":"确认审核","link":"./auditOpenInvoice.do?invoiceApplyId=${list.invoiceApplyId}&isAdvance=0"}'>驳回</span>
									<button class="edit-user clcforbid" onclick="openEInvoice(${list.invoiceApplyId},${list.relatedId},'${formToken}');" id="openEinvoiceId">开票</button>
									<c:if test="${list.type == 505}">
										<span class="edit-user pop-new-data" layerparams='{"width":"630px","height":"427px","title":"修改",
											  "link":"<%=basePath%>order/saleorder/editOrderRatioInit.do?orderId=${list.relatedId}&invoiceType=${list.invoiceType}&invoiceProperty=${list.invoiceProperty}&sendInvoice=${list.sendInvoice}&invoiceApplyId=${list.invoiceApplyId}"}'>修改
									</span>
									</c:if>
									<c:if test="${list.type == 504}">
										<span class="edit-user pop-new-data" layerparams='{"width":"630px","height":"427px","title":"修改",
											  "link":"<%=basePath%>order/saleorder/editOrderRatioInit.do?orderId=${list.relatedId}&invoiceType=${list.invoiceType}&invoiceProperty=${list.invoiceProperty}&sendInvoice=${list.sendInvoice}&invoiceApplyId=${list.invoiceApplyId}"}'>修改</span>
									</c:if>
								</c:if>
<%--								<span class="edit-user pop-new-data" layerParams='{"width":"80%","height":"600px","title":"查看开票申请","link":"../../finance/invoice/selectInvoiceItems.do?relatedId=${list.relatedId}&invoiceApplyId=${list.invoiceApplyId}&editFlag=false"}' >查看开票申请</span>--%>
								<span class="edit-user pop-new-data" layerParams='{"width":"80%","height":"600px","title":"查看开票申请","link":"/invoice/invoiceApply/invoiceApplyDetail.do?invoiceApplyId=${list.invoiceApplyId}"}' >查看开票申请</span>
							</td>
						</tr>
					</c:forEach>
					<c:if test="${empty openInvoiceApplyList}">
						<tr>
							<td colspan="22">
								<!-- 查询无结果弹出 --> 查询无结果！请尝试使用其他搜索条件。
							</td>
						</tr>
					</c:if>
					</tbody>
				</table>
			</div>
		</div>
		<div>
			<c:if test="${!empty openInvoiceApplyList}">
				<div class="table-style4 f_left" style="margin:0px;margin-left: 20px;">
					<div class="print-record">
						<span class="bt-border-style border-blue" onclick="invoiceSign(1);">标记</span>
					</div>
					<div class="print-record">
						<span class="bt-border-style border-blue" onclick="invoiceSign(0);">取消标记</span>
					</div>
					<div class="print-record">
						<span class="bt-border-style border-blue" onclick="invoiceBatch('${formToken}');">开票</span>
					</div>
					<div class="print-record">
						<span class="bt-border-style border-blue" onclick="invoiceBatchRefuse('${formToken}');">驳回</span>
					</div>
				</div>
			</c:if>
			<tags:page page="${page}" />
			<div class="clear"></div>
			<div class="fixtablelastline">
					<span style="float:left">
						【已勾选 条目：<span id="checkNum">0</span> 申请开票总金额：<span id="checkAmount"><fmt:formatNumber type="number" value="0.00" pattern="0.00" maxFractionDigits="2" /></span>】
					</span>
				【全部结果 条目：${page.totalRecord} 申请开票总金额：<fmt:formatNumber type="number" value="${applyAmountMoney}" pattern="0.00" maxFractionDigits="2" />】
				【本页统计 条目：${pageNum} 申请开票总金额：<fmt:formatNumber type="number" value="${pageAmount}" pattern="0.00" maxFractionDigits="2" />】
			</div>
            开票检查项目：1 全部收款，2 专票对公收款，3 无进行中货款票售后，4 不存在已完成的仅退票售后，5 申请数量合规，6 申请商品未超额，7 数电发票，8 无开票留言
		</div>
	</div>

</div>
<script type="text/javascript">
	$(document).ready(function() {
		$(".ez-laycascader").each(function(){
			var _this=$(this);
			renderCascader(_this);
		})
	})
	initTicketReasonEqualList();
	function initTicketReasonEqualList(){
		var initValueStr = $('#ticketReasonEqualList').val();
		var initValue = JSON.parse(initValueStr);
		$.ajax({
			url: '/invoiceApply/api/getCheckRuleList.do?ruleType=2',
			method: 'GET',
			contentType: 'application/x-www-form-urlencoded',
			success: function (response) {
				var responseData = JSON.parse(response); // 将 JSON 字符串解析为 JavaScript 对象或数组
				var processedData = responseData.data.map(function (item) {
					return {
						name: item.ruleCode,
						value: item.ruleCode,
					};
				});

				window.ticketReasonEqual = xmSelect.render({
					el: '#ticketReasonEqual',
					name: 'ticketReasonEqual',
					data: processedData,
					initValue: initValue,
				});
			},
		});
	}

	function resetTicketReasonEqualList(){
		$("#ticketReasonEqualList").val('[]');
		initTicketReasonEqualList();
	}


    function invoiceAutoOpen() {
        $.ajax({
            type: "POST",
            url: page_url + "/invoiceAuto/api/open.do",
            dataType: 'json',
            success: function (data) {

                if (data.code == 0) {
                    var index = layer.open({
                        type: 1,
                        // skin: 'my-style',
                        title: false,
                        closeBtn: false,
                        area: '300px;',
                        shade: 0.8,
                        id: 'LAY_layuipro',
                        resize: false,
                        btnAlign: 'c',
                        moveType: 1,
                        content: '<div class="loading-icon" style="margin: 20px auto;"></div><span id="percentText" style="display: block; text-align: center;">0%</span><br><span style="display: block; text-align: center;">正在处理中，请不要关闭当前页面</span><br><br>'
                    });
                    // 模拟加载过程
                    var percent = 0;
                    var interval = setInterval(function() {
                        $.ajax({
                            type: "POST",
                            url: page_url + "/invoiceAuto/api/openLoading.do",
                            dataType: 'json',
                            success: async function (data) {

                                if (data.code == 0) {
                                    percent = data.data;
                                    // 更新百分比数字
                                    $('#percentText').text(percent + '%');
                                    if (percent >= 100) {
                                        await sleep(2000);
										clearInterval(interval);
                                        search();
                                        // 隐藏弹出层
                                        layer.close(index);
                                    }

                                } else {
                                    layer.alert(data.message);
                                }

                            },
                        });
                    }, 2000);

                } else {
                    layer.alert(data.message);
                }

            },
            error: function (data) {
                if (data.status == 1001) {
                    layer.alert("当前操作无权限")
                }
            }
        });

    }
    async function sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
	function istrue(c){
		return  (c||'true')=='true'||(c||'1')=='1';
	}
	function createParentChildStructure(data) {
		// 创建一个字典来保存所有节点，按照它们的 K 值索引
		const dataMap = data.reduce((map, node) => {
			map[node.orgId] = { ...node, child: [] };
			return map;
		}, {});
		// 最终的父节点数组
		const result = [];

		// 遍历所有节点，构建父子关系
		data.forEach(node => {
			// 如果 level 值为 "3"，则是顶层节点，添加到结果数组中
			if (node.level === "3" || node.level == 3) {
				result.push(dataMap[node.orgId]);
			} else if (node.parentId in dataMap) {
				// 如果 P 值在 dataMap 中，则找到其父节点，并将当前节点添加为子节点
				dataMap[node.parentId].child.push(dataMap[node.orgId]);
			}
		});

		return result;
	}
	function renderCascader(cas){
		layui.use('layCascader', function () {
			try {
				var layCascader = layui.layCascader;
				var _this = $(cas);
				var url = _this.attr("ez_url");
				var ezurl = _this.attr("ezlist_url");
				var value = _this.attr("ez_value") || 'VALUE';
				var label = _this.attr("ez_label") || 'LABEL';
				var children = _this.attr("ez_children") || 'CHILDREN';
				var multiple = istrue(_this.attr("multi"));
				var itemsJson = _this.attr("itemsJson");
				var itemPlaceholder = _this.attr("placeholder") || '请选择';
				var paramValue = [] ;
				if(_this.val().length >0){
					paramValue=parseInt(_this.val());
				}
				var collapseTags = istrue(_this.attr("collapsetags"));
				var showAllLevels = istrue(_this.attr("showalllevels"));

				$.get(url || ezurl, function (data) {
					var jsonData = JSON.parse(data);
					var res  = createParentChildStructure(jsonData.data);
					var prop = {};
					prop.value = value;
					prop.label = label;
					prop.children = children;
					prop.multiple = false;
					prop.checkStrictly = true;

					if (ezurl) {
						res = flatToTree(res, 0);
					}
					var org = layCascader({
						name:"orgId",
						elem: _this[0],
						props: prop,
						filterable: true,
						filterMethod: function (node, val) {//重写搜索方法。
							if (val == node.data[label]) {//把value相同的搜索出来

								return true;
							}
							if ((node.data[label] + node.data[label]).indexOf(val) != -1) {//名称中包含的搜索出来
								return true;
							}
							//  console.log(node.data.orgName+node.data.orgNames+'##'+(node.data.orgId+'').indexOf(val));
							return !ezpingyin(val, (node.data[label] + node.data[label]), (node.data[value] + ''));
						},
						clearable: true,
						placeholder: itemPlaceholder,
						collapseTags: collapseTags,
						showAllLevels: showAllLevels,
						value: paramValue,
						options: res,
						showLastLevels: false, // 显示完整路径
						multiple: false // 单选模式
					});
					org.change(function (value, node) {
						$("#orgId").val(value);
					});
				})
			}catch (e) {
				console.log(e);
			}
		})
	}
</script>


<%@ include file="../../common/footer.jsp"%>
