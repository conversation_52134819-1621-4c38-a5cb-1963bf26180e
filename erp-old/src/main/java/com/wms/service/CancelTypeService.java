package com.wms.service;


import lombok.NonNull;

/**
 * wms取消接口类
 *
 * <AUTHOR>
 * @date 2022/1/6 12:56
 * @see <a href="http://wiki.ivedeng.com/pages/viewpage.action?pageId=150208772">WMS接口重构</a>
 **/
public interface CancelTypeService {

    // =================入库单取消=====================

    /**
     * <b>入库单类型-采购单 取消 服务</b><br/>
     * poType: WmsInterfaceOrderType.INPUT_PURCHASE:"PO" 入库单类型-采购单<br/>
     * interfaceType: WMSContant.CANCEL_PO:"POC" 入库单取消接口
     * @param docNo 单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功，false 取消失败
     */
    boolean cancelInputPurchaseMethod(@NonNull String docNo, @NonNull String erpCancelReason);

    /**
     * 入库单类型-直发采购单 取消服务
     * @param docNo 订单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功，false 取消失败
     */
    boolean cancelInputDeliveryDirectPurchaseMethod(@NonNull String docNo, @NonNull String erpCancelReason);

    /**
     * <b>入库单类型-销售售后退货 取消 服务</b><br/>
     * poType:WmsInterfaceOrderType.INPUT_SALE_RETURN:"RA" 库单类型-销售售后退货<br/>
     * interfaceType:WMSContant.CANCEL_PO:"POC" 入库单取消接口
     * @param docNo 单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功 false 取消失败
     */
    boolean cancelInputSaleReturnMethod(@NonNull String docNo, @NonNull String erpCancelReason);



    /**
     * <b>换货单类型- 采购换货单 取消 服务</b><br/>
     * poType:WmsInterfaceOrderType.EXG_PURCHASE:"PS" 换货单类型- 采购换货单<br/>
     * interfaceType:WMSContant.CANCEL_PO:"POC" 入库单取消接口
     * @param docNo 单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功 false 取消失败
     */
    boolean cancelExgPurchaseMethod(@NonNull String docNo, @NonNull String erpCancelReason);

    /**
     * <b>换货单类型- 销售换货单 取消服务</b><br/>
     * poType:WmsInterfaceOrderType.EXCHANG_SALEORDER:"SS" 换货单类型- 销售换货单<br/>
     * interfaceType:WMSContant.CANCEL_PO:"POC" 入库单取消接口
     * @param docNo 单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功 false 取消失败
     */
    boolean cancelExchangSaleorderMethod(@NonNull String docNo, @NonNull String erpCancelReason);

    //======================出库单取消===========================

    /**
     * <b>出库单类型-采购售后退货 取消 服务</b><br/>
     * poType:WmsInterfaceOrderType.OUT_PURCHASE_RETURN:"PT" 换货单类型- 销售换货单<br/>
     * interfaceType:WMSContant.CANCEL_ORGGINCAL_SALESORDER:"SOC" 出库单撤销接口
     * @param docNo 单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功 false 取消失败
     */
    boolean cancelOutPurchaseReturnMethod(@NonNull String docNo, @NonNull String erpCancelReason);

    /**
     * <b>出库单类型-销售出库 取消 服务</b><br/>
     * poType:WmsInterfaceOrderType.OUT_SALE_OUT:"SO" 换货单类型- 销售换货单<br/>
     * interfaceType:WMSContant.CANCEL_ORGGINCAL_SALESORDER:"SOC" 出库单撤销接口
     * @param docNo 单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功 false 取消失败
     */
    boolean cancelOutSaleOutMethod(@NonNull String docNo, @NonNull String erpCancelReason);

    /**
     * 出库单类型-直发销售 出库取消接口
     * @param docNo 订单号
     * @param erpCancelReason 取消原因
     * @return boolean true 取消成功，false 取消失败
     */
    boolean cancelInputDeliveryDirectSaleReturnMethod(@NonNull String docNo, @NonNull String erpCancelReason);




}
