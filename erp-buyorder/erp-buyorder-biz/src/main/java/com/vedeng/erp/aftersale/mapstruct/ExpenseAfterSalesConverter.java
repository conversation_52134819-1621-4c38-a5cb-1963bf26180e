package com.vedeng.erp.aftersale.mapstruct;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.aftersale.domain.entity.ExpenseAfterSalesEntity;
import com.vedeng.erp.aftersale.dto.ExpenseAfterSalesDto;
import com.vedeng.erp.buyorder.dto.BuyorderExpenseDetailDto;
import com.vedeng.erp.buyorder.dto.OrderRemarkDto;
import com.vedeng.erp.buyorderexpense.domain.entity.BuyorderExpenseDetailEntity;
import org.mapstruct.*;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.mapstruct
 * @Date 2022/10/20 19:59
 */
@Repository
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE, builder = @Builder(disableBuilder = true))
public interface ExpenseAfterSalesConverter extends BaseMapStruct<ExpenseAfterSalesEntity, ExpenseAfterSalesDto> {

    @Override
    @Mapping(target = "orderDesc", source = "orderDesc", qualifiedByName = "jsonArrayToObject")
    ExpenseAfterSalesDto toDto(ExpenseAfterSalesEntity entity);

    @Override
    @Mapping(target = "orderDesc", source = "orderDesc", qualifiedByName = "objectToJsonArray")
    ExpenseAfterSalesEntity toEntity(ExpenseAfterSalesDto dto);

    /**
     * entity 中JSONArray 转 原对象
     * @param source JSONArray
     * @return List<BusinessChanceGoodsDto> dto中的对象
     */
    @Named("jsonArrayToObject")
    default List<OrderRemarkDto> jsonArrayToObject(JSONArray source) {
        if (CollUtil.isEmpty(source)) {
            return Collections.emptyList();
        }
        return source.toJavaList(OrderRemarkDto.class);
    }
    /**
     * dto 原对象中 转 JSONArray
     * @param source 对象
     * @return JSONArray JSONArray
     */
    @Named("objectToJsonArray")
    default JSONArray objectToJsonArray(List<OrderRemarkDto> source) {
        if (CollUtil.isEmpty(source)) {
            return null;
        }
        return JSONArray.parseArray(JSON.toJSONString(source));
    }
}
