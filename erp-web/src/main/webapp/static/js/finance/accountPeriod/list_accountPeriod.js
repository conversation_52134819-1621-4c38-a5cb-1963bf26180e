
function exportAccountPeriod(num){
	checkLogin();
	if(num == 1){
		location.href = page_url + '/finance/accountperiod/export_'+getNowDateStr()+'.do?' + $("#search").serialize();
	}else{
		location.href = page_url + '/finance/accountperiod/exportAccountperiod.do?' + $("#search").serialize();
	}
}

function exportAccountPeriodListModify() {
	checkLogin();
	location.href = page_url + '/report/finance/exportAccountPeriodList.do?' + $("#search").serialize();
}

function exportAccountPeriodList(){
	checkLogin();

	//location.href = page_url + '/report/finance/exportAccountPeriodListNew.do?' + $("#search").serialize();
	var traderName = $("#traderName").val();
	var status = $('#status option:selected').text();
	if (status=="全部"){
		status="";
	}
	var creator = $('#creator option:selected').text();
	if (creator=="全部"){
		creator="";
	}
	var startTime = $("#startTime").val();
	var endTime = $("#endTime").val();
	var billPeriodType = $('#billPeriodType option:selected').text();
	if (billPeriodType=="全部"){
		billPeriodType="";
	}
	var o=$('#departmentInfo option:selected');
	var departmentInfo="";
	for(var i=0;i<o.length;i++){
		if(o[i].selected){
			if (i==o.length-1){
				departmentInfo+=o[i].text;
			} else{
				departmentInfo+=o[i].text+',';
			}

		}
	}
	debugger
	//var departmentInfo = $('#departmentInfo option:selected').text();
	departmentInfo = encodeURIComponent(departmentInfo);
	var url = $("#ezDomain").val()+ "/ezlist/export/export.html?pageId="+$("#ezId").val()+"&exportType=2&pageName=客户账期申请导出&DEFAULT_PAGE_SIZE=10&CLICK_NUM_=0&CUSTOMER_NAME=" +traderName+"&USERNAME="+creator+"&ADD_TIME_START="+startTime+"&ADD_TIME_END="+endTime+"&BILL_PERIOD_TYPE="+billPeriodType+"&CHECK_STATUS="+status;
	url = encodeURI(url)+"&ORG_NAME="+departmentInfo+"&more_less_flag=1&";
	location.href = url;
	//http://ez.ivedeng.com/ezlist/export/export.html?pageId=319&exportType=2&pageName=%E5%AE%A2%E6%88%B7%E8%B4%A6%E6%9C%9F%E7%94%B3%E8%AF%B7%E5%AF%BC%E5%87%BA&DEFAULT_PAGE_SIZE=10&CLICK_NUM_=0
	// &CUSTOMER_NAME=21&USERNAME=12&ADD_TIME_START=2021-08-16&ADD_TIME_END=2021-08-17&BILL_PERIOD_TYPE=21&CHECK_STATUS=12&ORG_NAME=3&more_less_flag=1&
}

function reset(){
	$("form").find("input[type='text']").val('');
	$("form").find(".hidden-reset").val('');
	$.each($("form select"),function(i,n){
		$(this).children("option:first").prop("selected",true);
	});
	var array = new Array();
	$("#departmentInfo").select2().val(array).trigger("change");
}
$(function () {
	$("#departmentInfo").select2();

	if ($("#departmentInfoList").val()){
		var arr = new Array();
		arr = $("#departmentInfoList").val().replace('[','').replace(']','').replace(/\s/g,'').split(',');
		$("#departmentInfo").select2().val(arr).trigger("change");
		$(".select2-container-multi").css("width","178px");
	}

})