package com.wms.dao;

import com.wms.dao.generate.VWmsOutSncodeOrderMapper;
import com.wms.model.ddi.DdiInstallstionExtDto;
import org.apache.ibatis.annotations.Param;

import java.sql.Timestamp;
import java.util.List;

public interface VWmsOutSncodeOrderExtMapper extends VWmsOutSncodeOrderMapper {

    List<DdiInstallstionExtDto> getAllinstallationOrderSaleOrder(@Param("skuList") List<String> skuList, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);

    List<DdiInstallstionExtDto> getAllinstallationOrderAfterSaleOrder(@Param("skuList") List<String> skuList, @Param("startTime") Timestamp startTime, @Param("endTime") Timestamp endTime);
}