package com.vedeng.erp.kingdee.mapstruct;

import com.vedeng.erp.kingdee.common.base.BaseCommandMapStruct;
import com.vedeng.erp.kingdee.domain.command.KingDeeReceiveFeeCommand;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDetailDto;
import com.vedeng.erp.kingdee.dto.KingDeeReceiveFeeDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 费用应收单
 * @date
 */
@Mapper(componentModel = "spring")
public interface KingDeeReceiveFeeCommandConvertor extends BaseCommandMapStruct<KingDeeReceiveFeeCommand, KingDeeReceiveFeeDto> {

    @Mapping(target = "fid", source = "fid")
    @Mapping(target = "fdate", source = "fdate")
    @Mapping(target = "f_Qzok_Bddjtid", source = "FQzokBddjtid")
    @Mapping(target = "fbusinesstype", source = "fbusinesstype")
    @Mapping(target = "FSetAccountType", source = "FSetAccountType")
    @Mapping(target = "FBillTypeID.FNumber", source = "FBillTypeID")
    @Mapping(target = "fcustomerid.FNumber", source = "fcustomerid")
    @Mapping(target = "fsettleorgid.FNumber", source = "fsettleorgid")
    @Override
    KingDeeReceiveFeeCommand toCommand(KingDeeReceiveFeeDto dto);

    @Mapping(target = "FPriceQty", source = "FPriceQty")
    @Mapping(target = "FTaxPrice", source = "FTaxPrice")
    @Mapping(target = "FEntryTaxRate", source = "FEntryTaxRate")
    @Mapping(target = "f_Qzok_Bddjhid", source = "FQzokBddjhid")
    @Mapping(target = "fcostid.FNumber", source = "fcostid")
    KingDeeReceiveFeeCommand.FEntityDetail toCommand(KingDeeReceiveFeeDetailDto dto);

}
