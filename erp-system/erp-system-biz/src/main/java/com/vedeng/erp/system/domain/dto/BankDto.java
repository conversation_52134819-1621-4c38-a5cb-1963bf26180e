package com.vedeng.erp.system.domain.dto;

import cn.hutool.core.util.StrUtil;
import com.vedeng.common.core.base.BaseDto;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.utils.validator.group.AddGroup;
import com.vedeng.common.core.utils.validator.group.UpdateGroup;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 银行信息
 * @date 2022/8/11 10:54
 */
@Getter
@Setter
public class BankDto extends BaseDto {


    /**
     * 主键
     */
    private Integer bankId;

    /**
     * 银联号
     */
    @NotNull(message = "银联号不能为空", groups = {AddGroup.class, UpdateGroup.class})
    @Size(min = 1, max = 255, message = "银联号在1-255字符之间", groups = {AddGroup.class, UpdateGroup.class})
    private String bankNo;

    /**
     * 支行名称
     */
    @NotNull(message = "支行名称不能为空", groups = {AddGroup.class})
    @Size(min = 1, max = 255, message = "支行名称在1-255字符之间", groups = {AddGroup.class, UpdateGroup.class})
    private String bankName;

    /**
     * 来源（0:银行同步 1:手动添加）
     */
    private Integer source;

    /**
     * 是否删除
     */
    private Integer isDel;


    /**
     * 是否禁用(当银行同步联行号覆盖手动添加联行号时，手动添加联行号则不可用)
     */
    private Integer disableFlag;

    public String getBankNo() {
        return StrUtil.trim(bankNo);
    }

    public String getBankName() {
        return StrUtil.trim(bankName);
    }


    public void setBankName(String bankName) {
        this.bankName = StrUtil.trim(bankName);
    }

    public void setBankNo(String bankNo) {
        this.bankNo = StrUtil.trim(bankNo);
    }

    public void manualAdd() {
        this.isDel = ErpConstant.F;
        this.setDisableFlag(ErpConstant.F);
        this.setSource(1);
    }

    public void systemAdd() {
        this.isDel = ErpConstant.F;
        this.setDisableFlag(ErpConstant.F);
        this.setSource(0);
    }

    public BankDto() {

    }

    public BankDto(String bankNo, String bankName) {
        this.bankNo = bankNo;
        this.bankName = bankName;
    }
}