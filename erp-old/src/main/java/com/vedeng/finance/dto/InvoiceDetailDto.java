package com.vedeng.finance.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 发票详情运输对象
 * <AUTHOR>
 */
@Data
public class InvoiceDetailDto {
    /**
     * 发票ID
     */
    private Integer invoiceId;

    /**
     * 关联表ID
     */
    private Integer relatedId;


    /**
     * 商品详情ID
     */
    private Integer detailGoodsId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 发票数量
     */
    private BigDecimal invoiceNum;

    /**
     * 总额
     */
    private BigDecimal totalAmount;
}
