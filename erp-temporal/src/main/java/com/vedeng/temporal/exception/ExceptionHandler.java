package com.vedeng.temporal.exception;

import com.vedeng.temporal.domain.dto.CompanyBusinessResponse;
import com.vedeng.temporal.domain.dto.CompanyBusinessRequest;
import com.vedeng.temporal.notification.NotificationContext;
import com.vedeng.temporal.notification.TemporalNotificationService;
import com.vedeng.temporal.notification.NotificationLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 异常处理工具类（精简版）
 *
 * 核心功能：
 * 1. 统一的异常处理和转换
 * 2. 简化的处理流程，减少冗余代码
 * 3. 集成通知服务和日志记录
 *
 * <AUTHOR> 4.0 sonnet
 * @version 6.0 (精简版)
 * @since 2025-01-26
 */
@Component
@Slf4j
public class ExceptionHandler {

    @Autowired
    private ErrorClassifier errorClassifier;

    @Autowired
    private TemporalNotificationService notificationService;

    /**
     * 处理业务异常（统一入口）
     * 
     * @param e 原始异常
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @return CompanyBusinessResponse（业务异常）或抛出RuntimeException（技术异常）
     */
    public CompanyBusinessResponse handleBusinessException(Exception e, String operationName,
                                                         String businessId, String companyCode) {
        try {
            // 构建业务上下文
            String businessContext = buildBusinessContext(operationName, businessId, companyCode);
            
            // 分类并创建标准业务异常
            BusinessProcessException businessException = classifyException(e, businessContext);
            
            // 根据异常类型处理
            return processException(businessException, operationName, businessId);
            
        } catch (Exception handlingException) {
            // 异常处理过程中出现异常，使用兜底策略
            log.error("异常处理过程中发生错误，操作: {}, 业务ID: {}", operationName, businessId, handlingException);
            return CompanyBusinessResponse.failure("系统异常，请稍后重试", "SYSTEM_ERROR");
        }
    }

    /**
     * 处理Activity异常并抛出（用于Activity层）
     * 
     * @param e 原始异常
     * @param operationName 操作名称
     * @param businessId 业务ID
     * @param companyCode 公司代码
     * @throws BusinessProcessException 业务异常时抛出
     * @throws RuntimeException 技术异常时抛出（触发Temporal重试）
     */
    public void handleAndThrowActivityException(Exception e, String operationName, 
                                              String businessId, String companyCode) {
        String businessContext = buildBusinessContext(operationName, businessId, companyCode);
        BusinessProcessException businessException = classifyException(e, businessContext);
        
        // 发送通知
        sendNotification(businessException, operationName, businessId);
        
        // 根据异常类型抛出
        if (businessException.isRetryable()) {
            log.warn("技术异常，将由Temporal重试: {}", businessException.getMessage());
            throw new RuntimeException(businessException.getMessage(), businessException);
        } else {
            log.error("业务异常，停止重试: {}", businessException.getMessage());
            throw businessException;
        }
    }

    // ========== 私有辅助方法 ==========
    
    /**
     * 分类异常并创建标准业务异常
     */
    private BusinessProcessException classifyException(Exception e, String businessContext) {
        if (e instanceof BusinessProcessException) {
            return (BusinessProcessException) e;
        }
        
        return errorClassifier.classifyAndCreateException(e, null, businessContext);
    }
    
    /**
     * 处理分类后的业务异常
     */
    private CompanyBusinessResponse processException(BusinessProcessException e, String operationName, String businessId) {
        // 发送通知
        sendNotification(e, operationName, businessId);
        
        if (e.isRetryable()) {
            // 技术异常：记录日志并抛出RuntimeException让Temporal重试
            log.warn("技术异常，将由Temporal重试: 操作={}, 业务ID={}, 错误码={}, 消息={}", 
                    operationName, businessId, e.getErrorCode(), e.getMessage());
            throw new RuntimeException(e.getMessage(), e);
            
        } else {
            // 业务异常：记录日志并返回失败响应
            log.error("业务异常，停止重试: 操作={}, 业务ID={}, 错误码={}, 消息={}", 
                    operationName, businessId, e.getErrorCode(), e.getMessage());
            return CompanyBusinessResponse.failure(e.getMessage(), e.getErrorCode());
        }
    }
    
    /**
     * 发送通知
     */
    private void sendNotification(BusinessProcessException e, String operationName, String businessId) {
        try {
            // 从业务上下文中提取公司代码
            String companyCode = extractCompanyFromContext(e.getBusinessContext());
            
            NotificationContext context = NotificationContext.builder()
                    .operationName(operationName)
                    .businessId(businessId)
                    .targetCompany(companyCode)
                    .build()
                    .withException(e, 0L);

            if (e.isRetryable()) {
                notificationService.sendTechnicalErrorNotification(context);
            } else {
                notificationService.sendBusinessFailureNotification(context);
            }
        } catch (Exception notificationException) {
            log.warn("发送通知失败: {}", notificationException.getMessage());
        }
    }
    
    /**
     * 构建业务上下文
     */
    private String buildBusinessContext(String operationName, String businessId, String companyCode) {
        StringBuilder sb = new StringBuilder();
        if (operationName != null) sb.append("Operation=").append(operationName);
        if (businessId != null) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("BusinessId=").append(businessId);
        }
        if (companyCode != null) {
            if (sb.length() > 0) sb.append(", ");
            sb.append("Company=").append(companyCode);
        }
        return sb.length() > 0 ? sb.toString() : null;
    }
    
    /**
     * 从业务上下文中提取公司代码
     */
    private String extractCompanyFromContext(String businessContext) {
        if (businessContext == null) return null;
        String pattern = "Company=";
        int start = businessContext.indexOf(pattern);
        if (start == -1) return null;
        start += pattern.length();
        int end = businessContext.indexOf(", ", start);
        return end == -1 ? businessContext.substring(start) : businessContext.substring(start, end);
    }
}