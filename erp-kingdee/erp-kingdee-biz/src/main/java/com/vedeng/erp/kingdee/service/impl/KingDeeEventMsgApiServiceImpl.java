package com.vedeng.erp.kingdee.service.impl;

import com.vedeng.erp.kingdee.service.KingDeeEventMsgApiService;
import com.vedeng.infrastructure.kingdee.mapper.KingDeeEventMsgMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2023/7/15
 */
@Service
@Slf4j
public class KingDeeEventMsgApiServiceImpl implements KingDeeEventMsgApiService {

    @Autowired
    private KingDeeEventMsgMapper kingDeeEventMsgMapper;

    public void deleteEventMsgByMessageid(Integer kingDeeEventMsgId,Integer userId){
        kingDeeEventMsgMapper.deleteEventMsg(kingDeeEventMsgId,userId);
    }

}
