package com.vedeng.buyorder.controller;

import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.vedeng.buyorder.service.ErpBuyOrderService;
import com.vedeng.common.core.annotation.NoNeedAccessAuthorization;
import com.vedeng.common.core.utils.HttpClientUtil;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.erp.system.dto.BaseCompanyInfoDetailDto;
import com.vedeng.erp.system.dto.BaseCompanyInfoDto;
import com.vedeng.erp.system.service.BaseCompanyInfoApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Controller
@Qualifier("BdBuyOrderController")
@RequestMapping("/buyorder/bdorder")
public class BdBuyOrderController {

    @Autowired
    private ErpBuyOrderService erpBuyOrderService;
    @Autowired
    private BaseCompanyInfoApiService baseCompanyInfoApiService;

    /**
     * 根据贝登的采购订单号，查询对应的销售订单号
     * @param buyOrderNoList
     * @return
     */
    @ResponseBody
    @RequestMapping("/getBdSaleOrderListByBuyOrderNo")
    @NoNeedAccessAuthorization
    public ResultInfo<List<Map<String,String>>> getBdSaleOrderListByBuyOrderNo(@RequestBody List<String> buyOrderNoList) {
        List<BaseCompanyInfoDto> baseCompanyInfoDtos = baseCompanyInfoApiService.findAll();
        //定义一个采购单号前缀对应的Map
        Map<String,String> orderPrefixMap = new HashMap<>();// {"EGCG","http://qaygyx.erp.ivedeng.com","YXBCG":"http://qayxb.erp.ivedeng.com"}
        for (BaseCompanyInfoDto baseCompanyInfoDto : baseCompanyInfoDtos) {
            BaseCompanyInfoDetailDto detailDto = baseCompanyInfoDto.getBaseCompanyInfoDetailDto();
            if(detailDto != null && StringUtils.isNotBlank(detailDto.getOrderCgOrderNoPrefix())) {
                if(detailDto.getOrderCgOrderNoPrefix().contains(",")){//支持多个单号前缀
                    String[] split = detailDto.getOrderCgOrderNoPrefix().split(",");
                    for (int i = 0; i < split.length; i++) {
                        orderPrefixMap.put(split[i], baseCompanyInfoDto.getErpDomain());
                    }
                }else{
                    orderPrefixMap.put(detailDto.getOrderCgOrderNoPrefix(),baseCompanyInfoDto.getErpDomain());
                }
            }
        }
        ResultInfo<List<Map<String,String>>> resultInfo = new ResultInfo<List<Map<String,String>>>();

        // 按前缀分组采购单号
        Map<String, List<String>> groupedOrderNos = new HashMap<>();
        for (String buyOrderNo : buyOrderNoList) {
            boolean matched = false;
            for (String  key: orderPrefixMap.keySet()) {
                if (buyOrderNo.startsWith(key)) {
                    String orderSysPrefix = orderPrefixMap.get(key);//根据域名地址来进行分组统计
                    groupedOrderNos.computeIfAbsent(orderSysPrefix, k -> new ArrayList<>()).add(buyOrderNo);
                    matched = true;
                    break;
                }
            }
            // 如果没有匹配到前缀，可以记录日志或处理异常情况
            if (!matched) {// TODO: 处理未匹配到前缀的单号
                log.error("buyOrderNo 未匹配到对应的来源系统" + buyOrderNo);
            }
        }

        // 调用各个ERP系统的接口
        List<Map<String, String>> allResults = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : groupedOrderNos.entrySet()) {
            try {
                String erpDomain = entry.getKey();
                List<String> orderNos = entry.getValue();
                JSONObject jsonObject = HttpClientUtil.httpPost(erpDomain + "/buyorder/bdorder/getBdSaleOrderListByBuyOrderNoForOtherErp.do", JSON.toJSONString(orderNos));
                ResultInfo<List<Map<String, String>>> resultInfoTemp =
                        JSON.parseObject(jsonObject.toJSONString(), new com.alibaba.fastjson.TypeReference<ResultInfo<List<Map<String, String>>>>() {
                        });
                allResults.addAll(resultInfoTemp.getData());
            } catch (Exception e) {
                log.error("查询目标系统的采购订单号对应的销售订单失败",e);
            }
        }
        resultInfo.setData(allResults);
        return resultInfo;
    }

    /**
     * 根据贝登的采购订单号，查询对应的销售订单号
     * @param buyOrderNoList
     * @return
     */
    @ResponseBody
    @RequestMapping("/getBdSaleOrderListByBuyOrderNoForOtherErp")
    @NoNeedAccessAuthorization
    public ResultInfo<List<Map<String,String>>> getBdSaleOrderListByBuyOrderNoForOtherErp(@RequestBody List<String> buyOrderNoList) {
        if(buyOrderNoList == null || buyOrderNoList.isEmpty()) {
            return ResultInfo.error("请输入对应的采购单号");
        }
        ResultInfo<List<Map<String,String>>> resultInfo = new ResultInfo<List<Map<String,String>>>();
        List<Map<String,String>> list = erpBuyOrderService.getBdSaleOrderListByBuyOrderNo(buyOrderNoList);
        resultInfo.setData(list);
        return resultInfo;
    }






}
