package com.vedeng.trader.dao;

import com.vedeng.trader.model.WebAccountInvitationLog;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface WebAccountInvitationLogMapper {
    int deleteByPrimaryKey(Long id);

    int insert(WebAccountInvitationLog record);

    int insertSelective(WebAccountInvitationLog record);

    WebAccountInvitationLog selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(WebAccountInvitationLog record);

    int updateByPrimaryKey(WebAccountInvitationLog record);

    Integer countInvited(@Param("registerMobile") String mobile);
}