package com.vedeng.goods.service;

import com.vedeng.goods.model.BaseAttributeValue;
import com.vedeng.goods.model.vo.BaseAttributeValueVo;
import com.vedeng.goods.model.vo.BaseAttributeVo;
import com.vedeng.goods.model.vo.BaseCategoryVo;

import java.util.Collection;
import java.util.List;

public interface BaseAttributeValueService {

    /**
     * 查询属性值列表
     * <AUTHOR>
     * @param baseAttributeValueVo
     * @param list
     * @return
     */
    List<BaseAttributeValueVo> getBaseAttributeValueVoList(BaseAttributeValueVo baseAttributeValueVo,List<BaseAttributeVo> list);

    /**
     * 根据属性ID查询属性值列表
     * <AUTHOR>
     * @param baseAttributeValueVo
     * @return
     */
    List<BaseAttributeValueVo> getBaseAttributeValueVoListByAttrId(BaseAttributeValueVo baseAttributeValueVo);

    /**
     * 根据分类ID查询分类关联的属性值列表
     * <AUTHOR>
     * @param list
     * @return
     */
    List<BaseAttributeValueVo> getAttrValueListByCategoryId(List<BaseCategoryVo> list);

    /**
     * 查询属性值列表,正叙
     * <AUTHOR>
     * @return
     */
    List<BaseAttributeValueVo> getBaseAttributeValueVoListASC(BaseAttributeValueVo baseAttributeValueVo,List<BaseAttributeVo> list);

    /**
     * @description 根据属性值Id获取属性值列表
     * <AUTHOR>
     * @param
     * @date 2019/5/22
     */
    List<BaseAttributeValue> listAttributeValueByIds(List<Integer> attributeValueIdList);



    /**
     * 根据skuId获取属性值列表
     *
     * @param skuIdList
     */
    List<BaseAttributeValue> listAttributeValueBySkuIds( List<Integer> skuIdList);

}
