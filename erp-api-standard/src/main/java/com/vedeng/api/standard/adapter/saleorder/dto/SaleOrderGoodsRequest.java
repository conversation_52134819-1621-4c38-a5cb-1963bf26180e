package com.vedeng.api.standard.adapter.saleorder.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class SaleOrderGoodsRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer saleorderGoodsId;
    private Integer saleorderId;
    private Integer goodsId;
    private String sku;
    private String goodsName;
    private String brandName;
    private Integer brandId;
    private String model;
    private String unitName;
    private String spec;
    private String spuType;
    private String productChineseName;
    private String manufacturerName;
    private String productCompanyLicence;
    private BigDecimal price;
    private BigDecimal realPrice;
    private BigDecimal maxSkuRefundAmount;
    private BigDecimal jxMarketPrice;
    private BigDecimal jxSalePrice;
    private Integer currencyUnitId;
    private Integer num;
    private Integer dbBuyNum;
    private String deliveryCycle;
    private Integer deliveryDirect;
    private String deliveryDirectComments;
    private String registrationNumber;
    private String supplierName;
    private BigDecimal referenceCostPrice;
    private String referencePrice;
    private String referenceDeliveryCycle;
    private Integer reportStatus;
    private String reportComments;
    private Integer haveInstallation;
    private String goodsComments;
    private String insideComments;
    private Integer arrivalUserId;
    private Integer arrivalStatus;
    private Long arrivalTime;
    private Integer isDelete;
    private Long addTime;
    private Integer creator;
    private Long modTime;
    private Integer updater;
    private Integer deliveryNum;
    private Integer deliveryStatus;
    private Long deliveryTime;
    private Integer isIgnore;
    private Long ignoreTime;
    private Integer ignoreUserId;
    private String purchasingPrice;
    private String goodsLevelName;
    private String goodsPositionName;
    private String manageCategoryName;
    private String materialCode;
    private String storageAddress;
    private Integer noOutNum;
    private Integer nowNum;
    private String allPrice;
    private String prices;
    private Integer pickCnt;
    private Integer totalNum;
    private String isOut;
    private Integer eNum;
    private Integer buyNum;
    private Integer warehouseNum;
    private BigDecimal averagePrice;
    private BigDecimal averageDeliveryCycle;
    private Integer bussinessId;
    private Integer bussinessType;
    private Integer companyId;
    private BigDecimal cgPrice;
    private Integer categoryId;
    private BigDecimal settlementPrice;
    private Integer areaControl;
    private BigDecimal channelPrice;
    private String channelDeliveryCycle;
    private String delivery;
    private String buyOrderNo;
    private Integer expressNum;
    private BigDecimal expressPrice;
    private String serviceNo;
    private Integer pCountAll;
    private String creatorNm;
    private BigDecimal settlePrice;
    private Integer barcodeId;
    private BigDecimal costPrice;
    private Long satisfyDeliveryTime;
    private String satisfyDeliveryTimeStr;
    private Integer buyorderStatus;
    private Integer afterReturnNum;
    private BigDecimal afterReturnAmount;
    private Integer warehouseReturnNum;
    private Integer terminalTraderId;
    private Integer terminalTraderType;
    private String terminalTraderName;
    private Integer salesAreaId;
    private String salesArea;
    private Integer customerNature;
    private String goodsUserIdStr;
    private Integer lockedStatus;
    private Integer occupyNum;
    private Integer isActionGoods;
    private Integer actionOccupyNum;
    private Integer actionLockCount;
    private Integer isCoupons;
    private String checkStatus;
    private String assis;
    private String manager;
    private String skuNew;
    private String goodsNameNew;
    private String brandNameNew;
    private String modelNew;
    private String unitNameNew;
    private String saleorderNo;
    private Long validTime;
    private Integer paymentStatus;
    private String title;
    private String userName;
    private Integer isNeedReport;
    private Integer isAuthorized;
    private Integer contractedGoodsFlag;
    private String skuTags;
    private Integer aging;
    private Integer warnLevel;
    private Long agingTime;
    private Integer shNum;
    private Integer joinBuyorderUser;
    private Long joinBuyorderOrderTime;
    private Integer status;
    private Integer goodsLevelNo;
    private Integer goodsPositionNo;
    private Integer purchaseStatusData;
    private Integer isLowerGoods;
    private BigDecimal checkPrice;
    private String dwhTerminalId;
    private String unifiedSocialCreditIdentifier;
    private String organizationCode;
    private Integer provinceId;
    private Integer cityId;
    private Integer areaId;
    private String provinceName;
    private String cityName;
    private String areaName;
    private BigDecimal TNNum;
    private String TNDateStr;
    private BigDecimal maoLiBuyPrice;
    private String maoLiBuyPriceDesc;
    private String imgUrl;
    private Integer perchaseTime;
    private Integer perfermenceDeliveryTime;
    private String labelCodes;
    private String labelNames;
    private Integer apparatusType;
    private String companyName;
    private String declareRange;
    private String declareDeliveryRange;
    // getter/setter略，可用lombok @Data注解
}
