package com.wms.service.validator;

/**
 * 简易的校验器的链式构造器类
 */
public class WmsValidatorChainBuild{

    private WMSValidatorChain validatorChain = new WMSValidatorChain();

    public static WmsValidatorChainBuild newBuild(){
        WmsValidatorChainBuild build = new WmsValidatorChainBuild();
        return build;
    }

    public WMSValidatorChain create(){
        return validatorChain;
    }

    public WmsValidatorChainBuild setPurchaseReturnCloseValidator(PurchaseReturnCloseValidator purchaseReturnCloseValidator){
        validatorChain.add(purchaseReturnCloseValidator);
        return this;
    }

    public WmsValidatorChainBuild setPurchaseExgCloseValidator(PurchaseExgCloseValidator purchaseExgCloseValidator){
        validatorChain.add(purchaseExgCloseValidator);
        return this;
    }

    public WmsValidatorChainBuild setPurchaseAfterSaleCloseValidator(PurchaseAfterSaleCloseValidator purchaseAfterSaleCloseValidator){
        validatorChain.add(purchaseAfterSaleCloseValidator);
        return this;
    }
}
