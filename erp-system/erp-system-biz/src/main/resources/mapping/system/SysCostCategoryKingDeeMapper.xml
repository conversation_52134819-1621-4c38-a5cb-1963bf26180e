<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.vedeng.erp.system.mapper.SysCostCategoryKingDeeMapper">
  <resultMap id="BaseResultMap" type="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee">
    <!--@mbg.generated-->
    <!--@Table T_SYS_COST_CATEGORY_KING_DEE-->
    <id column="COST_CATEGORY_KING_DEE_ID" jdbcType="INTEGER" property="costCategoryKingDeeId" />
    <result column="COST_CATEGORY_KING_DEE_NO" jdbcType="VARCHAR" property="costCategoryKingDeeNo" />
    <result column="COST_CATEGORY_KING_DEE_NAME" jdbcType="VARCHAR" property="costCategoryKingDeeName" />
    <result column="IS_DELETE" jdbcType="BOOLEAN" property="isDelete" />
    <result column="ADD_TIME" jdbcType="TIMESTAMP" property="addTime" />
    <result column="CREATOR" jdbcType="INTEGER" property="creator" />
    <result column="CREATOR_NAME" jdbcType="VARCHAR" property="creatorName" />
    <result column="MOD_TIME" jdbcType="TIMESTAMP" property="modTime" />
    <result column="UPDATER" jdbcType="INTEGER" property="updater" />
    <result column="UPDATER_NAME" jdbcType="VARCHAR" property="updaterName" />
    <result column="REMARK" jdbcType="VARCHAR" property="remark" />
    <result column="UPDATE_REMARK" jdbcType="VARCHAR" property="updateRemark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    COST_CATEGORY_KING_DEE_ID, COST_CATEGORY_KING_DEE_NO, COST_CATEGORY_KING_DEE_NAME, 
    IS_DELETE, ADD_TIME, CREATOR, CREATOR_NAME, MOD_TIME, UPDATER, UPDATER_NAME, REMARK, 
    UPDATE_REMARK
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from T_SYS_COST_CATEGORY_KING_DEE
    where COST_CATEGORY_KING_DEE_ID = #{costCategoryKingDeeId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--@mbg.generated-->
    delete from T_SYS_COST_CATEGORY_KING_DEE
    where COST_CATEGORY_KING_DEE_ID = #{costCategoryKingDeeId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="COST_CATEGORY_KING_DEE_ID" keyProperty="costCategoryKingDeeId" parameterType="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SYS_COST_CATEGORY_KING_DEE (COST_CATEGORY_KING_DEE_NO, COST_CATEGORY_KING_DEE_NAME, 
      IS_DELETE, ADD_TIME, CREATOR, 
      CREATOR_NAME, MOD_TIME, UPDATER, 
      UPDATER_NAME, REMARK, UPDATE_REMARK
      )
    values (#{costCategoryKingDeeNo,jdbcType=VARCHAR}, #{costCategoryKingDeeName,jdbcType=VARCHAR}, 
      #{isDelete,jdbcType=BOOLEAN}, #{addTime,jdbcType=TIMESTAMP}, #{creator,jdbcType=INTEGER}, 
      #{creatorName,jdbcType=VARCHAR}, #{modTime,jdbcType=TIMESTAMP}, #{updater,jdbcType=INTEGER}, 
      #{updaterName,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{updateRemark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="COST_CATEGORY_KING_DEE_ID" keyProperty="costCategoryKingDeeId" parameterType="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into T_SYS_COST_CATEGORY_KING_DEE
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="costCategoryKingDeeNo != null">
        COST_CATEGORY_KING_DEE_NO,
      </if>
      <if test="costCategoryKingDeeName != null">
        COST_CATEGORY_KING_DEE_NAME,
      </if>
      <if test="isDelete != null">
        IS_DELETE,
      </if>
      <if test="addTime != null">
        ADD_TIME,
      </if>
      <if test="creator != null">
        CREATOR,
      </if>
      <if test="creatorName != null">
        CREATOR_NAME,
      </if>
      <if test="modTime != null">
        MOD_TIME,
      </if>
      <if test="updater != null">
        UPDATER,
      </if>
      <if test="updaterName != null">
        UPDATER_NAME,
      </if>
      <if test="remark != null">
        REMARK,
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="costCategoryKingDeeNo != null">
        #{costCategoryKingDeeNo,jdbcType=VARCHAR},
      </if>
      <if test="costCategoryKingDeeName != null">
        #{costCategoryKingDeeName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        #{updateRemark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee">
    <!--@mbg.generated-->
    update T_SYS_COST_CATEGORY_KING_DEE
    <set>
      <if test="costCategoryKingDeeNo != null">
        COST_CATEGORY_KING_DEE_NO = #{costCategoryKingDeeNo,jdbcType=VARCHAR},
      </if>
      <if test="costCategoryKingDeeName != null">
        COST_CATEGORY_KING_DEE_NAME = #{costCategoryKingDeeName,jdbcType=VARCHAR},
      </if>
      <if test="isDelete != null">
        IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      </if>
      <if test="addTime != null">
        ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="creatorName != null">
        CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      </if>
      <if test="modTime != null">
        MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updater != null">
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="updaterName != null">
        UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        REMARK = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="updateRemark != null">
        UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR},
      </if>
    </set>
    where COST_CATEGORY_KING_DEE_ID = #{costCategoryKingDeeId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee">
    <!--@mbg.generated-->
    update T_SYS_COST_CATEGORY_KING_DEE
    set COST_CATEGORY_KING_DEE_NO = #{costCategoryKingDeeNo,jdbcType=VARCHAR},
      COST_CATEGORY_KING_DEE_NAME = #{costCategoryKingDeeName,jdbcType=VARCHAR},
      IS_DELETE = #{isDelete,jdbcType=BOOLEAN},
      ADD_TIME = #{addTime,jdbcType=TIMESTAMP},
      CREATOR = #{creator,jdbcType=INTEGER},
      CREATOR_NAME = #{creatorName,jdbcType=VARCHAR},
      MOD_TIME = #{modTime,jdbcType=TIMESTAMP},
      UPDATER = #{updater,jdbcType=INTEGER},
      UPDATER_NAME = #{updaterName,jdbcType=VARCHAR},
      REMARK = #{remark,jdbcType=VARCHAR},
      UPDATE_REMARK = #{updateRemark,jdbcType=VARCHAR}
    where COST_CATEGORY_KING_DEE_ID = #{costCategoryKingDeeId,jdbcType=INTEGER}
  </update>


  <select id="selectKingDeeCategoryByNameOrId" resultType="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee">
    select
    <include refid="Base_Column_List" />
    from T_SYS_COST_CATEGORY_KING_DEE
    where IS_DELETE = 0
    and (COST_CATEGORY_KING_DEE_NAME like CONCAT('%',#{category,jdbcType=VARCHAR},'%')
           or COST_CATEGORY_KING_DEE_NO = #{category,jdbcType=VARCHAR}
          )
  </select>
  <select id="selectKingDeeCategory" resultType="com.vedeng.erp.system.domain.entity.SysCostCategoryKingDee">
    select
    <include refid="Base_Column_List" />
    from T_SYS_COST_CATEGORY_KING_DEE
    where IS_DELETE = 0
    order by ADD_TIME desc
  </select>


  <insert id="insertBatchKingDeeCategory" parameterType="java.util.List">
    insert into T_SYS_COST_CATEGORY_KING_DEE (
      COST_CATEGORY_KING_DEE_NO,COST_CATEGORY_KING_DEE_NAME,CREATOR,CREATOR_NAME,UPDATER,UPDATER_NAME
    )
    values
    <foreach collection="list" index="index" item="SysCostCategoryKingDee" separator=",">
      (#{SysCostCategoryKingDee.costCategoryKingDeeNo,jdbcType=VARCHAR}, #{SysCostCategoryKingDee.costCategoryKingDeeName,jdbcType=VARCHAR},
      #{SysCostCategoryKingDee.creator,jdbcType=INTEGER},#{SysCostCategoryKingDee.creatorName,jdbcType=VARCHAR},  #{SysCostCategoryKingDee.updater,jdbcType=INTEGER},
      #{SysCostCategoryKingDee.updaterName,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="selectByKingDeeNo" parameterType="String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from T_SYS_COST_CATEGORY_KING_DEE
    where COST_CATEGORY_KING_DEE_NO = #{costCategoryKingDeeNo,jdbcType=VARCHAR} AND IS_DELETE = 0
  </select>

  <select id="listFindRepeatInfo" resultType="java.lang.Integer" parameterType="java.util.List">
    select
           count(*)
    from T_SYS_COST_CATEGORY_KING_DEE
    where COST_CATEGORY_KING_DEE_NO in
    <foreach collection="list" item="SysCostCategoryKingDee" open="(" close=")" separator=",">
      #{SysCostCategoryKingDee.costCategoryKingDeeNo,jdbcType=VARCHAR}
    </foreach>
  </select>

</mapper>