package com.vedeng.erp.aftersale.service.impl;

import cn.hutool.core.util.StrUtil;
import com.vedeng.erp.aftersale.domain.entity.AfterSalesInstallServiceRecordDetail;
import com.vedeng.erp.aftersale.mapper.AfterSalesInstallServiceRecordDetailMapper;
import com.vedeng.erp.aftersale.vo.AfterSalesInstallServiceRecordVo;
import com.vedeng.erp.aftersale.mapper.AfterSalesInstallServiceRecordMapper;
import com.vedeng.erp.aftersale.service.AfterSalesInstallServiceRecordDetailService;
import com.vedeng.infrastructure.kingdee.common.KingDeeBaseApi;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeFormConstant;
import com.vedeng.infrastructure.kingdee.domain.command.OperateExtCommand;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description com.vedeng.erp.aftersale.service.Impl
 * @Date 2022/5/11 16:12
 */
@Service
public class AfterSalesInstallServiceRecordDetailServiceImpl implements AfterSalesInstallServiceRecordDetailService {
    public static Logger logger = LoggerFactory.getLogger(AfterSalesInstallServiceRecordDetailServiceImpl.class);
    @Resource
    private AfterSalesInstallServiceRecordMapper afterSalesInstallServiceRecordMapper;
    @Autowired
    private KingDeeBaseApi kingDeeBaseApi;

    @Resource
    private AfterSalesInstallServiceRecordDetailMapper afterSalesInstallServiceRecordDetailMapper;

    @Override
    public List<AfterSalesInstallServiceRecordVo> getInstallDetailList(Integer afterSalesId) {
        return afterSalesInstallServiceRecordMapper.getInstallServiceRecordList(afterSalesId);
    }

    @Override
    @Transactional
    public int deleteServiceRecord(Integer afterSalesServiceId) {
        afterSalesInstallServiceRecordDetailMapper.logicDelete(afterSalesServiceId);
        return afterSalesInstallServiceRecordMapper.logicDelete(afterSalesServiceId);
    }

    @Override
    @Transactional
    public int deleteServiceRecordDetail(Integer afterSalesServiceId,Integer afterSalesInstallDetailId) {
        //删除明细数据,如果明细数据为空,则删除主表数据
        logger.info("删除安调服务记录详情,afterSalesServiceId:{},afterSalesInstallDetailId:{}",afterSalesServiceId,afterSalesInstallDetailId);
        afterSalesInstallServiceRecordDetailMapper.deleteByPrimaryKey(afterSalesInstallDetailId);
        //afterSalesInstallDetailId转为String列表
        ArrayList<String> strings = new ArrayList<>();
        strings.add(afterSalesInstallDetailId.toString());
        OperateExtCommand operateExtCommand = new OperateExtCommand(KingDeeFormConstant.QZOK_ATHD, null, KingDeeConstant.ORG_ID.toString(), strings);
        kingDeeBaseApi.unAudit(operateExtCommand);
        kingDeeBaseApi.delete(operateExtCommand);

        List<AfterSalesInstallServiceRecordDetail> afterSalesInstallServiceRecordDetailList = afterSalesInstallServiceRecordDetailMapper.queryInfoByServiceId(afterSalesServiceId);
        if(afterSalesInstallServiceRecordDetailList.size() == 0){
            logger.info("安调服务记录详情为空,删除主表数据,afterSalesServiceId:{}",afterSalesServiceId);
            afterSalesInstallServiceRecordMapper.deleteByPrimaryKey(afterSalesServiceId);
            return 0;
        }
        return afterSalesInstallDetailId;
    }

    @Override
    public boolean querySupplCodeIsExtend(String supplCode) {
        if (Objects.isNull(supplCode)) {
            return false;
        }
        // 存在重复的数量大于0
        int i = afterSalesInstallServiceRecordDetailMapper.selectSupplCode(supplCode);
        return i>0;
    }
}
