<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<!DOCTYPE html>
<html>
<head>
    <title>拜访计划</title>
    <link rel="stylesheet" href="/static/css/common/common.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/ui/ui.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/records.css?staticResourceVersion=${requestScope.staticResourceVersion}">
    <link rel="stylesheet" href="/static/css/pages/visitRecord.css?staticResourceVersion=${requestScope.staticResourceVersion}">
</head>

<body>
    <!--隐藏的登录用户信息 -->
    <jsp:include page="../common/head_import.jsp"></jsp:include>

    <div class="page-wrap" id="page-container">
        <page-header :side="true"></page-header>
        <div class="page-container">
            <div class="page-main">
                <div class="list-title">拜访计划</div>
                <div class="list-top-option">
                    <ui-button type="primary" icon="icon-add" @click="gotoAdd">新建拜访计划</ui-button>
                </div>
                <ui-search-container 
                    ref="listContainer" 
                    :can-choose="false" 
                    :is-list-sort="true" 
                    :list-option="false" 
                    list-name="03" 
                    :default-tab="defaultTab" 
                    :headers="tableHeaders" 
                    url="/crm/visitrecord/profile/page" 
                    :search-params="searchParams" 
                    @call="handlerCallNumber" 
                    :left-fixed-number="1"
                >
                    <template v-slot:filter-list>
                        <ui-search-item label="客户名称" :lock="true">
                            <ui-input v-model="searchParams.customerName"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="拜访编号">
                            <ui-input v-model="searchParams.visitRecordNo"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="拜访状态">
                            <ui-select :data="visitStatusList"  multiple-type="fixed" placeholder="全部" v-model="searchParams.visitRecordStatusList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="拜访人">
                            <ui-select v-if="!isUserLoading" :data="visitorList" multiple-type="fixed" :avatar="true" placeholder="全部" v-model="searchParams.visitorIdList" :cansearch="true" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="同行人">
                            <ui-select v-if="!isUserLoading" :data="tongxingList" :avatar="true" multiple-type="fixed" placeholder="全部" v-model="searchParams.tongxingIdList" :cansearch="true" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="创建人">
                            <ui-select  v-if="!isUserLoading" :data="creatorList" :avatar="true" multiple-type="fixed" placeholder="全部" v-model="searchParams.creatorIdList" clearable :cansearch="true"></ui-select>
                        </ui-search-item>
                        <ui-search-item label="计划拜访时间">
                            <ui-date-range v-model="searchParams.planVisitDate" @change="handlerFilterDateChange('planVisitDate', $event)"></ui-date-range>
                        </ui-search-item>
                        <ui-search-item label="拜访完成时间">
                            <ui-date-range v-model="searchParams.completeDate" @change="handlerFilterDateChange('completeDate', $event)"></ui-date-range>
                        </ui-search-item>
                        <ui-search-item label="创建时间">
                            <ui-date-range v-model="searchParams.addTime" @change="handlerFilterDateChange('addTime', $event)"></ui-date-range>
                        </ui-search-item>
                        <ui-search-item label="客户类型">
                            <ui-select :data="customerNatureList" placeholder="全部" v-model="searchParams.customerNature" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="联系人姓名">
                            <ui-input v-model="searchParams.contactName" maxlength="50"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="联系人手机">
                            <ui-input v-model="searchParams.contactMobile"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="固话">
                            <ui-input v-model="searchParams.contactTele" placeholder="固话、手机、其他联系方式"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="拜访目标">
                            <ui-select :data="visitTypeList" multiple-type="fixed" placeholder="全部" v-model="searchParams.visitTargetList" clearable></ui-select>
                        </ui-search-item>
                        <ui-search-item label="线索/商机编号">
                            <ui-input v-model="searchParams.bussinessChanceNo" maxlength="50"></ui-input>
                        </ui-search-item>
                        <ui-search-item label="沟通记录">
                            <ui-input v-model="searchParams.commucateContent" maxlength="50"></ui-input>
                        </ui-search-item>
                    </template>
                    
                    <template v-slot:visitRecordNo="{ row }">
                        <div v-if="row.visitRecordNo" class="td-link" @click="GLOBAL.link({name:'查看拜访计划', url: '/crm/visitRecord/profile/detail?id=' + row.id})">{{row.visitRecordNo}}</div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:area="{ row }">
                        <div v-if="row.provinceName" class="text-line-1" :title="getAreaStr(row)">{{getAreaStr(row)}}</div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:traderName="{ row }">
                        <div v-if="row.customerName">
                            <ui-trader-name :info="{
                                traderName: row.customerName,
                                traderNameLink: row.visitCustomerVo ? row.visitCustomerVo.traderNameLink : '',
                                traderNameInnerLink: row.visitCustomerVo ? row.visitCustomerVo.traderNameInnerLink : '',
                                tycFlag: row.visitCustomerVo ? row.visitCustomerVo.tycFlag : false,
                                traderId: row.traderId,
                                needValid: true,
                                nameFlex: true
                            }"></ui-trader-name>
                        </div>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:customerGrade="{ row }">
                        <ui-trader-grade v-if="row.visitCustomerVo && row.visitCustomerVo.customerGrade" :grade="row.visitCustomerVo.customerGrade"></ui-trader-grade>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:visitorName="{ row }">
                        <div class="text-line-1" :title="getVisitorUsers(row)">{{ getVisitorUsers(row) }}</div>
                    </template>
                    <template v-slot:visitAddress="{ row }">
                        <template v-if="row.visitAddress">
                            <div class="visit-address-wrap">
                                <div class="visit-address-txt text-line-1" :title="row.visitAddress">{{ row.visitAddress }}</div>
                                <ui-view-map :address="getAreaStr(row).replace(/-/g, '') + row.visitAddress" :notxt="true"></ui-view-map>
                            </div>
                        </template>
                        <template v-else>-</template>
                    </template>
                    <template v-slot:visitRecordStatus="{ row }">
                        <div class="visit-status-tag" :class="'status-' + row.visitRecordStatus">
                            {{ {1: '待拜访', 2: '拜访中', 3: '已拜访', 4: '已关闭'}[row.visitRecordStatus] || '-' }}
                        </div>
                    </template>
                    <template v-slot:option="{ row }">
                        <div class="option-wrap">
                            <div class="td-link" @click="GLOBAL.link({name:'查看拜访计划', url: '/crm/visitRecord/profile/detail?id=' + row.id})">查看</div>
                        </div>
                    </template>
                </ui-search-container>
            </div>
        </div>
        <!-- 天眼查详情 -->
        <ui-tyc-detail ref="tycDetail"></ui-tyc-detail>
        <!-- 列表操作提示 -->
        <ui-list-option-tip></ui-list-option-tip>
    </div>
    <script src="/static/js/common/vue.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/axios.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/vuedraggable.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/lodash.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/moment.min.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/ui/ui.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/common/layout.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
    <script src="/static/js/pages/visitRecord/index.js?staticResourceVersion=${requestScope.staticResourceVersion}"></script>
</body>
</html>
