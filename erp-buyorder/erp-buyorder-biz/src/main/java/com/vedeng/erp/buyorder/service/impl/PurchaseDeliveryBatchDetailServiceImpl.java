package com.vedeng.erp.buyorder.service.impl;

import com.vedeng.erp.buyorder.domain.entity.PurchaseDeliveryDirectBatchDetail;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchDetailDto;
import com.vedeng.erp.buyorder.mapper.PurchaseDeliveryDirectBatchDetailMapper;
import com.vedeng.erp.buyorder.service.PurchaseDeliveryBatchDetailService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PurchaseDeliveryBatchDetailServiceImpl implements PurchaseDeliveryBatchDetailService {
    @Resource
    private PurchaseDeliveryDirectBatchDetailMapper purchaseDeliveryDirectBatchDetailMapper;

    @Override
    public Integer queryExpressDetailIdById(Integer purchaseDeliveryDirectBatchDetailId) {
        PurchaseDeliveryDirectBatchDetail purchaseDeliveryDirectBatchDetail = purchaseDeliveryDirectBatchDetailMapper.selectByPrimaryKey(purchaseDeliveryDirectBatchDetailId);
        if(purchaseDeliveryDirectBatchDetail == null){
            return 0;
        }
        return purchaseDeliveryDirectBatchDetail.getExpressDetailId();
    }

    @Override
    public int updateOutNumById(Integer purchaseDeliveryDirectBatchDetailId, Integer num) {
        return purchaseDeliveryDirectBatchDetailMapper.updateOutNumById(purchaseDeliveryDirectBatchDetailId,num);
    }

    @Override
    public PurchaseDeliveryBatchDetailDto queryInfoByDetailId(Integer purchaseDeliveryDirectBatchDetailId) {
        PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto = new PurchaseDeliveryBatchDetailDto();
        PurchaseDeliveryDirectBatchDetail purchaseDeliveryDirectBatchDetail = purchaseDeliveryDirectBatchDetailMapper.selectByPrimaryKey(purchaseDeliveryDirectBatchDetailId);
        BeanUtils.copyProperties(purchaseDeliveryDirectBatchDetail,purchaseDeliveryBatchDetailDto);
        return purchaseDeliveryBatchDetailDto;
    }

    @Override
    public int updateArrivalCountByDetailId(Integer purchaseDeliveryDirectBatchDetailId, Integer num) {
        return purchaseDeliveryDirectBatchDetailMapper.updateArrivalCountByDetailId(purchaseDeliveryDirectBatchDetailId, num);
    }

    @Override
    public List<PurchaseDeliveryBatchDetailDto> queryInfoByExpressDetailId(Integer expressDetailId) {
        List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetailList = purchaseDeliveryDirectBatchDetailMapper.queryInfoByExpressDetailId(expressDetailId);
        List<PurchaseDeliveryBatchDetailDto> purchaseDeliveryDirectBatchDetailDtos = new ArrayList<>();
        for(PurchaseDeliveryDirectBatchDetail purchaseDeliveryDirectBatchDetail : purchaseDeliveryDirectBatchDetailList){
            PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto = new PurchaseDeliveryBatchDetailDto();
            BeanUtils.copyProperties(purchaseDeliveryDirectBatchDetail,purchaseDeliveryBatchDetailDto);
            purchaseDeliveryDirectBatchDetailDtos.add(purchaseDeliveryBatchDetailDto);
        }
        return purchaseDeliveryDirectBatchDetailDtos;
    }

    @Override
    public Integer queryMainIdByDetailId(Integer purchaseDeliveryDirectBatchDetailId) {
        return purchaseDeliveryDirectBatchDetailMapper.queryMainIdByDetailId(purchaseDeliveryDirectBatchDetailId);
    }

    @Override
    public List<PurchaseDeliveryBatchDetailDto> queryInfoByMainId(Integer purchaseDeliveryDirectBatchInfoId) {
        List<PurchaseDeliveryDirectBatchDetail> purchaseDeliveryDirectBatchDetailList = purchaseDeliveryDirectBatchDetailMapper.queryInfoByMainId(purchaseDeliveryDirectBatchInfoId);
        List<PurchaseDeliveryBatchDetailDto> purchaseDeliveryDirectBatchDetailDtos = new ArrayList<>();
        for(PurchaseDeliveryDirectBatchDetail purchaseDeliveryDirectBatchDetail : purchaseDeliveryDirectBatchDetailList){
            PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto = new PurchaseDeliveryBatchDetailDto();
            BeanUtils.copyProperties(purchaseDeliveryDirectBatchDetail,purchaseDeliveryBatchDetailDto);
            purchaseDeliveryDirectBatchDetailDtos.add(purchaseDeliveryBatchDetailDto);
        }
        return purchaseDeliveryDirectBatchDetailDtos;
    }
}
