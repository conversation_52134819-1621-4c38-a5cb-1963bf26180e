package com.vedeng.erp.broadcast.domain.dto;

import lombok.*;
import org.hibernate.validator.constraints.NotBlank;

/**
 * 播报部门表单DTO  
 * 用于新增和编辑播报部门的表单数据传输
 * 只包含必要的业务字段，简化前端交互
 * 支持两级部门结构：一级部门(大区/业务部) + 二级小组
 * 
 * <AUTHOR>
 * @since 2025-01-27
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BroadCastDeptFormDto {
    
    /**
     * 部门ID
     * 新增时为null，编辑时必须提供
     * 对应T_BROADCAST_DEPT表的主键ID
     */
    private Integer id;
    
    /**
     * 父级部门ID
     * 创建一级部门时为null或0，创建二级小组时填写所属一级部门的ID
     */
    private Integer deptId;
    
    /**
     * 小组ID
     * 编辑已有部门小组时使用，用于指定要编辑的小组
     * 新增时为null，编辑时可选提供
     */
    private Integer groupId;
    
    /**
     * 部门名称
     * 必填字段，小组的名称
     * 二级小组如：线上组、线下组、线上一组等
     */
    @NotBlank(message = "小组名称不能为空")
    private String groupName;
    
    /**
     * AED用户ID列表
     * 可选字段，配置的AED用户ID，逗号分隔
     * 用于指定该部门或小组对应的AED销售人员
     */
    private String aedUserIds;
} 