package com.vedeng.trader.group.cart;

import com.vedeng.track.api.dto.business.req.BusinessReqDto;
import com.vedeng.trader.group.visit.TrackBaseBehvior;
import org.apache.commons.collections.CollectionUtils;

import java.util.BitSet;

/**
 * <b>Description:</b>加购商品<br>
 * @param
 * @return
 * @Note
 * <b>Author:calvin</b>
 * <br><b>Date:</b> 2020/5/25
 */
public class AddGood2CartBehvior extends TrackBaseBehvior {
    @Override
    public BitSet calculate() {
        Integer type=getChooseType();
        if(type==0||type==1){
            getGoods(type);
        }
        BitSet middleResult=new BitSet();
        if(CollectionUtils.isNotEmpty(getSkuList())) {
            BusinessReqDto reqDto = new BusinessReqDto();
            reqDto.setDayType(getPeriod());
            reqDto.setSkuNoList(getSkuList());
            middleResult= getAddCartData(reqDto);
        }
        BitSet result=getBaseTraderSet();
        result.and(middleResult);
        return result;
    }
}
