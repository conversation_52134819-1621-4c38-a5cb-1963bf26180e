<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<c:set var="title" value="公海条件配置" scope="application"/>
<%@ include file="../../common/common.jsp" %>
<link rel="stylesheet"
      href="${pageContext.request.contextPath}/static/new/css/pages/goods/vgoods/spu/spu_view.css?rnd=${resourceVersionKey}">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/new/css/common/global.css">
<link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/trader/public/public_rules.css">

<div class="content">
    <span style="width: 100%; margin-bottom: 30px; background-color: #B0E0E6; font-size: 14px">
        <div class="line-distance">1. 公海条件配置提交后将于次日9：00生效（开始公海预警）。提交后的第10日19点，满足公海条件的客户纳入公海；</div>
        <div class="line-distance">2. 新客保护：对创建天数在保护期内的客户进行保护,保护期内不纳入公海，关联客户合并保护；</div>
        <div class="line-distance">3. 豁免人员：对右侧《公海豁免人员》选中人员名下客户进行保护，豁免人员名下客户均不纳入公海；</div>
        <div class="line-distance">4. 不纳入公海的客户：有账期未归还或未完结的售后订单的客户、解锁保护期内的客户、撤销公海保护期间的客户不被纳入公海，关联客户不被纳入公海；</div>
        <div class="line-distance">5. 您可以调整订单周期和沟通次数两个数值，修改客户纳入公海的条件，关联客户可合并数据后再行计算数值；</div>
        <div class="line-distance">6. 符合纳入公海条件的客户及其关联客户将于每日19点统一纳入公海，并在纳入公海前10日（含当日）每日9点进行公海预警。</div>
    </span>


    <div class="parts content1">
        <div class="title-container">
            <div class="table-title nobor" style="font-size: 14px">当前纳入公海条件</div>
        </div>
        <table class="table table-bordered table-striped table-condensed table-centered">
            <thead>
            <tr>
                <th style="font-size: 14px;width: 600px">条件</th>
                <th style="font-size: 14px">操作</th>
            </tr>
            </thead>

            <tbody>
            <tr>
                <td style="font-size: 14px">
                    <div>新客保护期：<span
                            style="color: red">${ruleDetailVo.publicCustomerCalculateRules.customerCreatedDays}</span>天
                    </div>
                    <div>不满足，近<span style="color: red">${ruleDetailVo.publicCustomerCalculateRules.validOrderDays}</span>天，生效订单数大于
                        <span style="color: red">
                            ${ruleDetailVo.publicCustomerCalculateRules.validOrderCount}</span>个<span
                                style="color: red">且</span>近
                        <span style="color: red">${ruleDetailVo.publicCustomerCalculateRules.communicationDays}</span>天，接通电话数大于
                        <span style="color: red">
                            ${ruleDetailVo.publicCustomerCalculateRules.communicationCount}</span>个，将纳入公海
                    </div>
                    <div>解锁保护期：<span
                            style="color: red">${ruleDetailVo.publicCustomerCalculateRules.lockProtectDays}</span>天
                    </div>
                </td>
                <td style="padding-top: 15px">
                    <span class="bt-small bg-light-blue bt-bg-style pop-new-data"
                          layerparams='{"width":"700px","height":"500px","title":"公海条件配置","link":"/trader/customer/config/calculate/rules.do"}'>
							公海条件配置
					</span>
                </td>
            </tr>
            </tbody>

        </table>
    </div>

    <div class="detail-block">
        <div class="title-container">
            <div class="table-title nobor" style="font-size: 14px">公海条件修改记录</div>
        </div>

        <div class="status-title">
            <sapn style="margin-left: 30px; font-size: 14px">修改时间</sapn>
            <sapn style="margin-left: 230px; font-size: 14px">修改人</sapn>
            <sapn style="margin-left: 21%; font-size: 14px">修改内容</sapn>
        </div>

        <div class="status-list">
            <c:choose>
                <c:when test="${not empty ruleDetailVo.publicCustomerCalculateRulesChangeDtoList}">
                    <c:forEach items="${ruleDetailVo.publicCustomerCalculateRulesChangeDtoList}"
                               var="publicCustomerCalculateRulesChangeDto" varStatus="index">
                        <c:if test="${index.count>10}">
                            <div class="status-more J-optional-more">
                        </c:if>
                        <div class="status-cnt" style="font-size: 14px">
                            <span style="width:300px; padding-top: 10px"><date:date
                                    value="${publicCustomerCalculateRulesChangeDto.addTime} "/></span>
                            <span style="width:250px; padding-top: 10px">${publicCustomerCalculateRulesChangeDto.creatorName}</span>
                            <span style="flex:1;">
                            <div><span style="color: red">更新：
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldCustomerCreatedDays}">
                                    新客保护期：${publicCustomerCalculateRulesChangeDto.customerCreatedDays}天；
                                </c:if>
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldValidOrderDays}">
                                    不满足，近${publicCustomerCalculateRulesChangeDto.validOrderDays}天，生效订单数大于
                                    ${publicCustomerCalculateRulesChangeDto.validOrderCount}个
                                </c:if>
                                <c:choose>
                                    <c:when test="${not empty publicCustomerCalculateRulesChangeDto.oldValidOrderDays and empty publicCustomerCalculateRulesChangeDto.oldCommunicationDays}">
                                        
                                    </c:when>
                                    <c:when test="${not empty publicCustomerCalculateRulesChangeDto.oldValidOrderDays and not empty publicCustomerCalculateRulesChangeDto.oldCommunicationDays}">
                                        且
                                    </c:when>
                                </c:choose>
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldCommunicationDays}">
                                    近${publicCustomerCalculateRulesChangeDto.communicationDays}天，接通电话数大于
                                    ${publicCustomerCalculateRulesChangeDto.communicationCount}个，将纳入公海；
                                </c:if>
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldLockProtectDays}">
                                    解锁保护期为${publicCustomerCalculateRulesChangeDto.lockProtectDays}天；
                                </c:if>
                                </span>
                            </div>
                            <div>原内容：
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldCustomerCreatedDays}">
                                    新客保护期：${publicCustomerCalculateRulesChangeDto.oldCustomerCreatedDays}天；
                                </c:if>
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldValidOrderDays}">
                                    不满足，近${publicCustomerCalculateRulesChangeDto.oldValidOrderDays}天，生效订单数大于
                                    ${publicCustomerCalculateRulesChangeDto.oldValidOrderCount}个
                                </c:if>
                                <c:choose>
                                    <c:when test="${not empty publicCustomerCalculateRulesChangeDto.oldValidOrderDays and empty publicCustomerCalculateRulesChangeDto.oldCommunicationDays}">
                                        
                                    </c:when>
                                    <c:when test="${not empty publicCustomerCalculateRulesChangeDto.oldValidOrderDays and not empty publicCustomerCalculateRulesChangeDto.oldCommunicationDays}">
                                        且
                                    </c:when>
                                </c:choose>
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldCommunicationDays}">
                                    近${publicCustomerCalculateRulesChangeDto.oldCommunicationDays}天，接通电话数大于
                                    ${publicCustomerCalculateRulesChangeDto.oldCommunicationCount}个，将纳入公海；
                                </c:if>
                                <c:if test="${not empty publicCustomerCalculateRulesChangeDto.oldLockProtectDays}">
                                    解锁保护期为${publicCustomerCalculateRulesChangeDto.oldLockProtectDays}天；
                                </c:if>
                            </div>
                        </span>
                        </div>
                        <c:if test="${index.count>10}">
                            </div>
                        </c:if>
                    </c:forEach>

                </c:when>
                <c:otherwise>
                    <div class="status-cnt">
                        <div class="status-item"></div>
                        <div class="status-item"></div>
                        <div class="status-item" style="text-align: center;">暂无修改记录！</div>
                    </div>
                </c:otherwise>
            </c:choose>
        </div>

        <c:if test="${ruleDetailVo.publicCustomerCalculateRulesChangeDtoList.size() > 5 }">
            <div class="detail-optional J-toggle-show">
                <span class="toggle-txt J-more">展开更多<i style="background: none;" class="vd-icon icon-down"></i></span>
                <span class="toggle-txt J-less" style="display: none;">收起<i style="background: none;"
                                                                            class="vd-icon icon-up"></i></span>
            </div>
        </c:if>
    </div>


</div>

<script>
    $('.J-toggle-show').click(function () {
        var $optionalWrap = $('.J-optional-more');
        var isShow = $optionalWrap.hasClass('show');
        var $less = $(this).find('.J-less');
        var $more = $(this).find('.J-more');

        if (isShow) {
            $optionalWrap.removeClass('show').slideUp(200);
            $less.hide();
            $more.show();
        } else {
            $optionalWrap.addClass('show').slideDown(200);
            $less.show();
            $more.hide();
        }
    });
</script>
