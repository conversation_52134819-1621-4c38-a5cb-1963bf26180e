package com.vedeng.goods.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CoreSkuQueryDetailDto {

    /**
     * 商品基础信息
     */
    private BaseInfo baseInfo;
    /**
     * 库存信息
     */
    private Inventory inventory;
    /**
     * 核价信息
     */
    private CheckPriceInfo checkPriceInfo;
    /**
     * 售后政策
     */
    private AfterSalesPolicy afterSalesPolicy;

    /**
     * 商品基础信息
     */
    @Data
    @Builder
    public static class BaseInfo {

        /**
         * 商品名称
         */
        private String skuName;

        /**
         * 订货号
         */
        private String sku;

        /**
         * 品牌
         */
        private String brandName;

        /**
         * 规格
         */
        private String spec;

        /**
         * 型号
         */
        private String model;

        /**
         * 单位
         */
        private String unitName;

        /**
         * 最小起订量
         */
        private Integer minOrder;

        /**
         * 属性
         */
        private String attributes;

        /**
         * 制造商型号
         */
        private String manufacturerModel;
    }

    /**
     * 库存信息
     */
    @Data
    @Builder
    public static class Inventory {

        /**
         * 可用库存
         */
        private Integer availableStockNum;

        /**
         * 库存量
         */
        private Integer stockNum;

        /**
         * 占用库存
         */
        private Integer occupyNum;

        /**
         * 活动锁定库存
         */
        private Integer actionLockNum;

        /**
         * 在途（订单/数量/预计)
         */
        private String onWayData;

        /**
         * 采购到货时长（工作日）
         */
        private Integer purchaseTime;
    }

    /**
     * 核价信息
     */
    @Data
    @Builder
    public static class CheckPriceInfo {

        /**
         * 是否需报备 1-是 0-否
         */
        private Integer isNeedReport;

        /**
         * 市场价
         */
        private BigDecimal marketPrice;

        /**
         * 终端价
         */
        private BigDecimal terminalPrice;

        /**
         * 经销价
         */
        private BigDecimal distributionPrice;

        /**
         * 安装费
         */
        private BigDecimal installationPrice;

    }

    /**
     * 售后政策
     */
    @Data
    @Builder
    public static class AfterSalesPolicy {

        /**
         * 产品是否可安装 1-是 0-否
         */
        private Integer isInstallable;

        /**
         * 是否保修 1-是 0-否
         */
        private Integer guaranteePolicyIsGuarantee;

        /**
         * 是否提供上门安装服务 安装类型:0-收费安装 1-免费安装 2-不可安装
         */
        private Integer installPolicyInstallType;

        /**
         * 是否支持退货 1-是 0-否
         */
        private Integer returnPolicySupportReturn;

        /**
         * 是否免费远程指导装机 1-是 0-否
         */
        private Integer installPolicyFreeRemoteInstall;

        /**
         * 安装费
         */
        private BigDecimal installPolicyInstallFee;

        /**
         * 安装区域：省市区json格式值
         */
        private String provinceCityJsonvalue;
    }


}
