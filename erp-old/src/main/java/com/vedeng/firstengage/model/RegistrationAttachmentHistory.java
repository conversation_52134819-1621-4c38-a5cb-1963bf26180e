package com.vedeng.firstengage.model;

public class RegistrationAttachmentHistory {
    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.REIGISTRATION_ATTACHMENT_HISTORY_ID
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Integer reigistrationAttachmentHistoryId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Integer registrationNumberId;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.ATTACHMENT_TYPE
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Integer attachmentType;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.NAME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private String name;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.DOMAIN
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private String domain;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.URI
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private String uri;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.ADD_TIME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Long addTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.MOD_TIME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Long modTime;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.CREATOR
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Integer creator;

    /**
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column T_REIGISTRATION_ATTACHMENT_HISTORY.UPDATOR
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    private Integer updator;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.REIGISTRATION_ATTACHMENT_HISTORY_ID
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.REIGISTRATION_ATTACHMENT_HISTORY_ID
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Integer getReigistrationAttachmentHistoryId() {
        return reigistrationAttachmentHistoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.REIGISTRATION_ATTACHMENT_HISTORY_ID
     *
     * @param reigistrationAttachmentHistoryId the value for T_REIGISTRATION_ATTACHMENT_HISTORY.REIGISTRATION_ATTACHMENT_HISTORY_ID
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setReigistrationAttachmentHistoryId(Integer reigistrationAttachmentHistoryId) {
        this.reigistrationAttachmentHistoryId = reigistrationAttachmentHistoryId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.REGISTRATION_NUMBER_ID
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Integer getRegistrationNumberId() {
        return registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.REGISTRATION_NUMBER_ID
     *
     * @param registrationNumberId the value for T_REIGISTRATION_ATTACHMENT_HISTORY.REGISTRATION_NUMBER_ID
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setRegistrationNumberId(Integer registrationNumberId) {
        this.registrationNumberId = registrationNumberId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.ATTACHMENT_TYPE
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.ATTACHMENT_TYPE
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Integer getAttachmentType() {
        return attachmentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.ATTACHMENT_TYPE
     *
     * @param attachmentType the value for T_REIGISTRATION_ATTACHMENT_HISTORY.ATTACHMENT_TYPE
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.NAME
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.NAME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.NAME
     *
     * @param name the value for T_REIGISTRATION_ATTACHMENT_HISTORY.NAME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.DOMAIN
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.DOMAIN
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public String getDomain() {
        return domain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.DOMAIN
     *
     * @param domain the value for T_REIGISTRATION_ATTACHMENT_HISTORY.DOMAIN
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setDomain(String domain) {
        this.domain = domain;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.URI
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.URI
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public String getUri() {
        return uri;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.URI
     *
     * @param uri the value for T_REIGISTRATION_ATTACHMENT_HISTORY.URI
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setUri(String uri) {
        this.uri = uri;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.ADD_TIME
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.ADD_TIME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Long getAddTime() {
        return addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.ADD_TIME
     *
     * @param addTime the value for T_REIGISTRATION_ATTACHMENT_HISTORY.ADD_TIME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setAddTime(Long addTime) {
        this.addTime = addTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.MOD_TIME
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.MOD_TIME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Long getModTime() {
        return modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.MOD_TIME
     *
     * @param modTime the value for T_REIGISTRATION_ATTACHMENT_HISTORY.MOD_TIME
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setModTime(Long modTime) {
        this.modTime = modTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.CREATOR
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.CREATOR
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Integer getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.CREATOR
     *
     * @param creator the value for T_REIGISTRATION_ATTACHMENT_HISTORY.CREATOR
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setCreator(Integer creator) {
        this.creator = creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.UPDATOR
     *
     * @return the value of T_REIGISTRATION_ATTACHMENT_HISTORY.UPDATOR
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public Integer getUpdator() {
        return updator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column T_REIGISTRATION_ATTACHMENT_HISTORY.UPDATOR
     *
     * @param updator the value for T_REIGISTRATION_ATTACHMENT_HISTORY.UPDATOR
     *
     * @mbggenerated Tue Nov 24 19:24:12 CST 2020
     */
    public void setUpdator(Integer updator) {
        this.updator = updator;
    }
}