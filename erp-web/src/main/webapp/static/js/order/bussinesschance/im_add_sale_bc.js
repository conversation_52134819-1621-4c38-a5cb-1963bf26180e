function checkProdList() {
    var listLen = $('.J-prod-list .J-prod-item').length;

    if (listLen) {
        $('.J-prod-wrap').show();
        $('.J-list-no-data').hide();
        $('.J-table-error').empty();
    } else {
        $('.J-prod-wrap').hide();
        $('.J-list-no-data').show();
    }

    resetName();
}

function resetName(){
    $('.J-prod-item').each(function(i){
        $(this).find('input').each(function () {
            var name = $(this).attr('name');
            name = name.replace(/quoteorderGoods\[\S*\]/g, 'quoteorderGoods[' + i + ']');
            $(this).attr('name', name);
        })
    });
}

function warnTableTips(errele, formele, tip){
    $(errele).empty().append('<div class="warning">'+tip+'</div>');
    formele && $(formele).addClass("errorbor").focus();
}


$(function () {
    var skuId=$("input[name='skuId']").val();
    console.log(skuId)
    if(skuId){
        selectGoods(skuId);
    }
    $("[name=submit]").click(function () {
        checkLogin();
        var totalPrice = parseFloat($.trim($('.J-total-price').html()) || '0');
        var num = $("#paymentTypeId").val();
        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if(num=="424"){//自定义
            var prepaidAmount = $("#prepaidAmount").val();
            if(prepaidAmount.length > 14){
                warnTips("prepaidAmountError","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }

            if(prepaidAmount!=""){
                if(!reg.test(prepaidAmount)){
                    warnTips("prepaidAmount","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
            }

            var accountPeriodAmount = $("#accountPeriodAmount").val();
            if(accountPeriodAmount.length > 14){
                warnTips("accountPeriodAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }

            if(accountPeriodAmount!=""){
                if(!reg.test(accountPeriodAmount)){
                    warnTips("accountPeriodAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
                // TODO 26_V02 去除校验账期
                // var accountPeriodLeft = Number($("#accountPeriodLeft").val());//账期最大限额
                // if(Number(accountPeriodAmount) > accountPeriodLeft){
                //     warnTips("accountPeriodAmountError","帐期支付金额超过帐期剩余额度:"+accountPeriodLeft);//文本框ID和提示用语
                //     return false;
                // }
            }else{
                warnTips("accountPeriodAmountError","账期支付金额不允许小于0");//文本框ID和提示用语
                return false;
            }

            var retainageAmount = $("#retainageAmount").val();
            if(retainageAmount.length > 14){
                warnTips("retainageAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }
            if(retainageAmount!=""){
                if(!reg.test(retainageAmount)){
                    warnTips("retainageAmountError","金额输入错误！仅允许使用大于零的数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }else if(Number(retainageAmount) > Number(totalPrice)*0.1){
                    warnTips("retainageAmountError","尾款不允许超过合同金额10%");//文本框ID和提示用语
                    return false;
                }
            }else{
                warnTips("retainageAmountError","尾款不允许小于0");//文本框ID和提示用语
                return false;
            }
            var retainageAmountMonth = $("#retainageAmountMonth").val();
            if(retainageAmountMonth.length>0){
                var re = /^[0-9]+$/ ;
                if(retainageAmountMonth=="0" || !re.test(retainageAmountMonth)){
                    warnTips("retainageAmountError","尾款期限必须为大于0的整数");//文本框ID和提示用语
                    return false;
                }else if(Number(retainageAmountMonth) > 24){
                    warnTips("retainageAmountError","尾款期限不允许超过24个月");//文本框ID和提示用语
                    return false;
                }
            }

            if(prepaidAmount!="" && accountPeriodAmount!="" && retainageAmount!=""){
                if(Number(prepaidAmount) + Number(accountPeriodAmount) + Number(retainageAmount) != Number(totalPrice)){
                    warnTips("retainageAmountError"," 支付金额总额与总金额不符");//文本框ID和提示用语
                    return false;
                }
            }
        }else if(num!="419"){//419先货后款，预付100%
            var accountPeriodAmount = $("#accountPeriodAmount").val();
            if(accountPeriodAmount.length > 14){
                warnTips("accountPeriodAmount","金额输入错误！长度应该在1-12个数字之间");//文本框ID和提示用语
                return false;
            }

            if(accountPeriodAmount!=""){
                if(!reg.test(accountPeriodAmount)){
                    warnTips("accountPeriodAmountError","金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
                    return false;
                }
                // TODO 26_V02 去除校验账期
                // var accountPeriodLeft = Number($("#accountPeriodLeft").val());//账期最大限额
                // if(Number(accountPeriodAmount) > accountPeriodLeft){
                //     warnTips("accountPeriodAmountError","帐期支付金额超过帐期剩余额度:"+accountPeriodLeft);//文本框ID和提示用语
                //     return false;
                // }
            }else{
                warnTips("accountPeriodAmountError","账期支付金额必须大于0");//文本框ID和提示用语
                return false;
            }
        }
        if( $(".J-prod-item").length == 0){
            warnTableTips(".J-table-error", null,"请选择报价产品");
            return false;
        }
        var prodFlag1 = {
            flag: true,
            ele: null
        };
        var prodFlag2 = {
            flag: true,
            ele: null
        };
        var prodFlag3 = {
            flag: true,
            ele: null
        };
        var prodFlag4= {
            flag: true,
            ele: null
        };
        var prodFlag5 = {
            flag: true,
            ele: null
        };
        $('.J-prod-item').each(function(i){
            if(!$(this).find('.J-item-price').val() && prodFlag1.flag){
                prodFlag1.flag = false;
                prodFlag1.ele = $(this).find('.J-item-price');
            }
            if(!$(this).find('.J-item-num').val() && prodFlag2.flag){
                prodFlag2.flag = false;
                prodFlag2.ele = $(this).find('.J-item-num');
            }
            if(!$(this).find('.J-item-haveInstallation:checked').length && prodFlag3.flag){
                prodFlag3.flag = false;
                prodFlag3.ele = $(this).find('.J-item-haveInstallation');
            }
            if(!$(this).find('.J-item-deliveryCycle').val() && prodFlag4.flag){
                prodFlag4.flag = false;
                prodFlag4.ele = $(this).find('.J-item-deliveryCycle');
            }
            if(!$(this).find('.J-item-deliveryDirect:checked').length && prodFlag5.flag){
                prodFlag5.flag = false;
                prodFlag5.ele = $(this).find('.J-item-deliveryDirect');
            }
        });
        if(!prodFlag1.flag){
            warnTableTips(".J-table-error", prodFlag1.ele,"请输入报价");
            return false;
        }
        if(!prodFlag2.flag){
            warnTableTips(".J-table-error",prodFlag2.ele, "请输入数量");
            return false;
        }
        if(!prodFlag3.flag){
            warnTableTips(".J-table-error", prodFlag3.ele,"请选择是否含安调");
            return false;
        }
        if(!prodFlag4.flag){
            warnTableTips(".J-table-error", prodFlag4.ele,"请输入货期");
            return false;
        }
        if(!prodFlag5.flag){
            warnTableTips(".J-table-error",  prodFlag5.ele,"请选择是否直发");
            return false;
        }
        if ($("#traderId_1").val() == "") {
            $('#e-traderId').css("display", "");
            $("#traderId_1").addClass("errorbor");
            return false;
        } else {
            $('#e-traderId').css("display", "none");
            $("#traderId_1").removeClass("errorbor");
        }

        if ($("#areaId_1").val() == "") {
            $('#e-areaId').css("display", "");
            $("#areaId_1").addClass("errorbor");
            return false;
        } else {
            $('#e-areaId').css("display", "none");
            $("#areaId_1").removeClass("errorbor");
        }

        if ($("#customerType_1").val() == "") {
            $('#e-ccustomerType').css("display", "");
            $("#customerType_1").addClass("errorbor");
            return false;
        } else {
            $('#e-customerType').css("display", "none");
            $("#customerType_1").removeClass("errorbor");
        }
        if ($("#traderContactId_1").val() == "") {
            $('#e-traderContactId').css("display", "");
            $("#traderContactId_1").addClass("errorbor");
            return false;
        } else {
            $('#e-traderContactId').css("display", "none");
            $("#traderContactId_1").removeClass("errorbor");
        }
        var isPolicymaker = $('input:radio[name="isPolicymaker"]:checked').val();
        if (isPolicymaker == undefined || isPolicymaker == "") {
            $("#e-isPolicymaker").css("display", "");
            $("#e-isPolicymaker").addClass("errorbor");
            return false;
        } else {
            $("#e-isPolicymaker").css("display", "none");
            $("#e-isPolicymaker").removeClass("errorbor");
        }
        if ($("#communication_1").val() == "") {
            $('#e-communication').css("display", "");
            $("#communication_1").addClass("errorbor");
            return false;
        } else {
            $('#e-communication').css("display", "none");
            $("#communication_1").removeClass("errorbor");
        }
        var goodsCategory = $('input:radio[name="goodsCategory"]:checked').val();
        if (goodsCategory == undefined || goodsCategory == "") {
            $("#e-goodsCategory").css("display", "");
            $("#e-goodsCategory").addClass("errorbor");
            return false;
        } else {
            $("#e-goodsCategory").css("display", "none");
            $("#e-goodsCategory").removeClass("errorbor");
        }
        var bussinessLevel = $('input:radio[name="bussinessLevel"]:checked').val();
        if (bussinessLevel == undefined || bussinessLevel == "") {
            $("#e-bussinessLevel").css("display", "");
            $("#e-bussinessLevel").addClass("errorbor");
            return false;
        } else {
            $("#e-bussinessLevel").css("display", "none");
            $("#e-bussinessLevel").removeClass("errorbor");
        }
        var bussinessStage = $('input:radio[name="bussinessStage"]:checked').val();
        if (bussinessStage == undefined || bussinessStage == "") {
            $("#e-bussinessStage").css("display", "");
            $("#e-bussinessStage").addClass("errorbor");
            return false;
        } else {
            $("#e-bussinessStage").css("display", "none");
            $("#e-bussinessStage").removeClass("errorbor");
        }
        var enquiryType = $('input:radio[name="enquiryType"]:checked').val();
        if (enquiryType == undefined || enquiryType == "") {
            $("#e-enquiryType").css("display", "");
            $("#e-bussinessStage").addClass("errorbor");
            return false;
        } else {
            $("#e-enquiryType").css("display", "none");
            $("#e-enquiryType").removeClass("errorbor");
        }
        var orderRate = $('input:radio[name="orderRate"]:checked').val();
        if (orderRate == undefined || orderRate == "") {
            $("#e-orderRate").css("display", "");
            $("#e-orderRate").addClass("errorbor");
            return false;
        } else {
            $("#e-orderRate").css("display", "none");
            $("#e-orderRate").removeClass("errorbor");
        }
        var amount = $("#amount_1").val().trim();
        var reg = /(^[1-9]([0-9]+)?(\.[0-9]{1,2})?$)|(^(0){1}$)|(^[0-9]\.[0-9]([0-9])?$)/;
        if (amount.length == 0) {
            warnTips("amount_1", "交易金额不允许为空");//文本框ID和提示用语
            return false;
        } else if (amount.length > 0 && !reg.test(amount)) {
            warnTips("amount_1", "交易金额输入错误！仅允许使用数字，最多精确到小数后两位");//文本框ID和提示用语
            return false;
        } else if (Number(amount) > 100000000) {
            warnTips("amount_1", "交易金额不允许超过一亿");//文本框ID和提示用语
            return false;
        }
        var orderTime = $("#orderTimeStr").val();
        if (orderTime == '') {
            warnTips("orderTimeStr", "预计成单时间不允许为空");
            return false;
        }
        var purchasingType = $('input:radio[name="purchasingType"]:checked').val();
        if (purchasingType == undefined || purchasingType == "") {
            $("#e-purchasingType").css("display", "");
            $("#e-purchasingType").addClass("errorbor");
            return false;
        } else {
            $("#e-purchasingType").css("display", "none");
            $("#e-purchasingType").removeClass("errorbor");
        }
        var paymentTerm = $('input:radio[name="paymentTerm"]:checked').val();
        if (paymentTerm == undefined || paymentTerm == "") {
            $("#e-paymentTerm").css("display", "");
            $("#e-paymentTerm").addClass("errorbor");
            return false;
        } else {
            $("#e-paymentTerm").css("display", "none");
            $("#e-paymentTerm").removeClass("errorbor");
        }
        var purchasingTime = $('input:radio[name="purchasingTime"]:checked').val();
        if (purchasingTime == undefined || purchasingTime == "") {
            $("#e-purchasingTime").css("display", "");
            $("#e-purchasingTime").addClass("errorbor");
            return false;
        } else {
            $("#e-purchasingTime").css("display", "none");
            $("#e-purchasingTime").removeClass("errorbor");
        }

        var period = $("#period_1").val().trim();
        var reg1 = /(^[1-9]\d*$)/;
        if (period.length == 0) {
            warn2Tips("period_1", "报价有效期不允许为空");//文本框ID和提示用语
            return false;
        } else if (period.length > 0 && !reg1.test(period)) {
            warn2Tips("period_1", "报价有效期输入错误！仅允许使用正整数.");//文本框ID和提示用语
            return false;
        }else if(period <0 || period >30){
            warn2Tips("period_1", "报价有效期在1天-30天之间");//文本框ID和提示用语
            return false;
        }
        // var additionalClause = $("#additionalClause").val();
        // if (additionalClause == '') {
        //     warnTips("additionalClause", "附加条款不允许为空");
        //     return false;
        // }
        // var quoteComments = $("#quoteComments").val();
        // if (quoteComments == '') {
        //     warnTips("quoteComments", "内部备注不允许为空");
        //     return false;
        // }


        $('[name=applyType]').val($(this).data('type'));
    });

    //选完客户名之后，带入其他字段
    window.setTraderInfo = function (traderItem) {
        $('[name=traderName]').val(traderItem.traderName);
        $('.J-trader-select-wrap .J-text').html(traderItem.traderName);
        console.log(traderItem)
        var layerParams = JSON.parse($('.J-add-prod').attr('layerparams'));
        layerParams.link = '/order/quote/addQuoteGoods.do?optType=1&traderId=' + traderItem.traderId + '&quoteorderId=';
        $('.J-add-prod').attr('layerparams', JSON.stringify(layerParams));
        var layerParams1 = JSON.parse($('.J-add-contact').attr('layerparams'));
        layerParams1.link = '/trader/customer/toAddContactPage.do?pageType=1&traderId='+traderItem.traderId+'&traderCustomerId='+traderItem.traderCustomerId;
        $('.J-add-contact').attr('layerparams', JSON.stringify(layerParams1));
        $("#trader_name_1").attr("value", traderItem.traderName);
        $("#customerType_1").attr("value", traderItem.customerType);
        $("#areaId_1").attr("value", traderItem.areaId);
        $("#areaIds_1").attr("value", traderItem.areaIds);
        $("#accountPeriodLeft").attr("value", traderItem.accountPeriodLeft);
        $("[name=checkTraderArea]").attr("value", traderItem.address);

        document.getElementById("traderAddress").innerText = traderItem.address;
        document.getElementById("customerTypeStr").innerText = traderItem.customerTypeStr;

        $('#traderContactId_1').empty();
        if(traderItem.traderContactList.length !=1) {
            $('#traderContactId_1').append('<option value="">请选择</option>');
        }
        $.each(traderItem.traderContactList, function(i, item){
            $('#traderContactId_1').append('<option value="' + item.traderContactId + '">' + item.name + '/' + item.telephone + '/' + item.mobile + '</option>');
        });

        if ($('.J-prod-item').length) {
            var skuIds = [];

            $('.J-prod-item').each(function () {
                skuIds.push($(this).find('.J-sku').val());
            });
            $.ajax({
                url: page_url + '/goods/goods/getSkuInfo.do',
                data: {
                    skuIds: skuIds.join(','),
                    traderId: $('[name=traderId]').val()
                },
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        $.each(res.data, function (i, sku) {
                            $('.J-prod-item').each(function () {
                                var skuId = $(this).find('.J-sku').val();
                                if (sku.goodsId == skuId) {
                                    if (sku.channelPrice && !$(this).find('.J-item-price').val()) {
                                        $(this).find('.J-item-price').val(sku.channelPrice).trigger('blur');
                                    }

                                    $(this).find('.J-item-price1').html(sku.channelPrice);
                                    $(this).find('.J-item-price2').html(sku.channelPrice);
                                    $(this).find('.J-item-price3').html(sku.referenceDeliveryCycle);
                                    $(this).find('.J-item-price4').html(sku.settlementPrice);
                                }
                            });
                        });
                    } else {
                        layer.alert(res.message);
                    }
                }
            });
            $(".J-item-price").addClass("errorbor");
        }
    };

    var checkHistoryBusiness = function (traderId, callback) {
        return;
        $.ajax({
            url: page_url +'/order/bussinesschance/checkTraderHasHistoryBussiness.do',
            data: {
                traderId: traderId
            },
            dataType: 'json',
            success: function (res) {
                if (res.code == -1) {
                    var layerParams = {
                        width: "1000px",
                        height: "700px",
                        title: "历史商机选择页",
                        link: page_url +"/order/bussinesschance/traderHistoryBussinessIndex.do?traderId=" + traderId
                    };

                    $('.J-history-business-layer').attr('layerParams', JSON.stringify(layerParams)).click();
                }else{
                    $("#bussinessParentId").attr("value", "");
                }
                callback && callback();
            }
        })
    };

    if($("#optType").val() != "edit"){
        checkHistoryBusiness($("#traderId_1").val());
    }
    new SuggestSelect({
        wrap: '.J-trader-select-wrap',
        placeholder: '请选择客户公司完整名称',
        searchPlaceholder: '请输入客户公司名称',
        asyncSearch: true,
        searchUrl: page_url + '/trader/customer/searchCustomer.do',
        asyncSearchName: 'searchTraderName',
        input: $('[name=traderId]'),
        emptyTmpl: $('.J-empty').html(),
        dataparse: function (data) {
            var resData = [];
            $.each(data.data, function (i, obj) {
                resData.push({
                    label: obj.traderName,
                    value: obj.traderId,
                    tip: obj.personal,
                    disabled: !obj.isBelong,
                    params: obj
                })
            });

            return resData;
        },
        beforeChange: function (id, item) {
            var traderItem = item.params;
            if ($('.J-prod-item').length) {
                layer.confirm('选择之后已选商品会价格会有变动，确定重新选择？', function () {
                    checkHistoryBusiness(traderItem.traderId, function () {
                        $('.J-trader-select-wrap .J-text').html(traderItem.traderName);
                        $('[name=traderId]').val(traderItem.traderId);
                        setTraderInfo(traderItem);
                    });

                    layer.closeAll();
                });

                return false;
            }else{
                checkHistoryBusiness(traderItem.traderId);
            }

            return true;
        },
        onchange: function (data, item) {
            var traderItem = item.params;

            setTraderInfo(traderItem);
        },
        afterSearch: function () {
            var layerParams = JSON.parse($('.J-eye-layer').attr('layerparams'));

            layerParams.link = '/trader/customer/add.do?optType=1 &traderName=' + $('.J-suggest-select-input').val();
            $('.J-eye-layer').attr('layerparams', JSON.stringify(layerParams));
        }
    });

    checkProdList();

    //编辑页面选择客户名渲染
    if ($('[name="traderName"]').val()) {
        $('.J-trader-select-wrap .J-text').html($('[name="traderName"]').val());
    }

    $(document).on('click', '.J-prod-del', function () {
        var $this = $(this);
        layer.confirm('确认删除此产品？', '', function () {
            $this.parents('.J-prod-item:first').remove();
            checkProdList();
            resetTotalValue();
            layer.closeAll();
        })
    });

    $(document).on('blur', '.J-num-parse', function () {
        var val = $.trim($(this).val());

        val = val.replace(/[^\d.]/g, '');

        $(this).val(val);
    });

    var resetTotalValue = function(){
        var totalNum = 0;
        var totalPrice = 0;

        $('.J-prod-item').each(function () {
            var itemTotal = $.trim($(this).find('.J-item-total').html());
            var itemNum = $.trim($(this).find('.J-item-num').val());

            if(itemTotal){
                totalPrice += parseFloat(itemTotal);
            }

            if(itemNum){
                totalNum += parseFloat(itemNum);
            }
        });

        $('.J-total-price').html(totalPrice.toFixed(2));
        $('.J-total-num').html(totalNum);
        checkPaymentType();
    };

    $(document).on('blur', '.J-item-price, .J-item-num', function () {
        var $this = $(this);
        setTimeout(function () {
            var $parent = $this.parents('.J-prod-item:first');
            var price = $.trim($parent.find('.J-item-price').val());
            var num = $.trim($parent.find('.J-item-num').val());
            var $total = $parent.find('.J-item-total');

            if (price && num) {
                $total.html((parseFloat(price) * parseFloat(num)).toFixed(2));
            }
            if(price >0){
                $this.removeClass("errorbor");
            }
            resetTotalValue();
        }, 100)
    });

    $('.J-trader-select-wrap').on('click', '.J-eye-look', function () {
        window.localStorage.setItem('needClick', '1');
        $('.J-eye-layer').click();
    })
    $('.J-trader-select-wrap').on('click', '.J-eye-look1', function () {
        $('.J-eye-layer1').click();
    })

    //付款计划
    var percentObj = {
        419: 1,
        420: 0.8,
        421: 0.5,
        422: 0.3,
        423: 0
    };

    var checkPaymentType = function(){
        var totalPrice = parseFloat($.trim($('.J-total-price').html()) || '0');
        var val = $('#paymentTypeId').val();
        var percent = percentObj[val];

        if(val){
            $('.J-payment-wrap1').show();
            if(percent == 1){
                $('.J-payment-wrap2').hide().find(':checkbox').prop('checked', false);
            }else{
                $('.J-payment-wrap2').show();
            }
        }else{
            $('.J-payment-wrap1, .J-payment-wrap2').hide();
        }

        if(percent || percent === 0){
            $('.J-prepaidAmount-input').attr('readonly', true).val((totalPrice * percent).toFixed(2));
            $('.J-accountPeriodAmount-input').attr('readonly', true).val((totalPrice * (1 - percent)).toFixed(2));
        }

        if(val == 424){
            $('.J-prepaidAmount-input').attr('readonly', false).val(0);
            $('.J-accountPeriodAmount-input').attr('readonly', false).val(0);
            $('.J-last-pay').show();
        }else{
            $('.J-last-pay').hide();
            $('.J-last-pay-input').val(0);
        }
    };
    checkPaymentType();
    $('#paymentTypeId').change(function(){
        checkPaymentType();
    })

    $('.J-close-tab').click(function () {
        layer.confirm('取消后，页面填写的所有内容将丢失，确认取消？', function(){
            $(window.parent.document).find('.active[role=presentation]').find('[tabclose]').click();
        })
    });

});

function checkSku(sku) {
    var flag = false;
    $('.J-prod-item').each(function () {
        if ($(this).find('.J-sku').val() == sku) {
            flag = true;
        }
    });

    return flag;
}

function selectGoods(skuid) {
    layer.closeAll();
    $.ajax({
        url: page_url + '/goods/goods/getSkuInfo.do',
        data: {
            skuIds: skuid,
            traderId: $('[name=traderId]').val()
        },
        dataType: 'json',
        success: function (res) {
            if (res.code == 0) {
                var prodTmpl = template($('.J-prod-tmpl').html());

                if (checkSku(skuid)) {
                    layer.alert('已选过该产品');
                } else {
                    $('.J-prod-list').append(prodTmpl(res.data[0]));
                    checkProdList();
                }
            } else {
                layer.alert(res.message);
            }
        }
    });
}
function selectGoods2(goodsName,brandName,model,unitName) {
    console.log(brandName);
    layer.closeAll();
    var prodTmpl = template($('.J-prod-tmpl').html());
    var goods = {
        goodsName: goodsName,
        sku:"",
        goodsId:0,
        brandName: brandName,
        model:model,
        unitName:unitName,
        stockNum:0,
        referenceDeliveryCycle:"",
        settlementPrice:"",
        channelPrice:""
    };
    $('.J-prod-list').append(prodTmpl(goods));
    checkProdList();
}

function newCustomer(traderId) {

    $.ajax({
        url: '/trader/customer/searchCustomer.do',
        data: {
            traderId: traderId
        },
        dataType: 'json',
        success: function (res) {
            if (res.code == 0) {
                setTraderInfo(res.data[0]);
            }else {
                layer.alert(res.message);
            }
        }
    });
    layer.closeAll();
}

function selectQuote(bussinessChanceId) {
    $("#bussinessParentId").attr("value", bussinessChanceId);
    inQuote(bussinessChanceId);
    layer.closeAll();
}
function inQuote(bussinessChanceId) {
    $.ajax({
        url: '/order/bussinesschance/getBussinessChanceAndQuoteInfo.do',
        data: {
            bussinessChanceId: bussinessChanceId
        },
        dataType: 'json',
        success: function (res) {
            if (res.code == 0) {
                setBussinceAndQuoteInfo(res.data);
            }else {
                layer.alert(res.message);
            }
        }
    });

}
function setBussinceAndQuoteInfo(item) {
    $("#amount_1").attr("value", item.amount);
    var  date = new Date(item.orderTime);
    var month = date.getMonth()+1;
    $("#orderTimeStr").attr("value", date.getFullYear()+'-'+month+'-'+date.getDate());
    $('[name =communication]').val(item.communication);
    fillRadio("goodsCategory",item.goodsCategory);
    fillRadio("bussinessLevel",item.bussinessLevel);
    fillRadio("bussinessStage",item.bussinessStage);
    fillRadio("enquiryType",item.enquiryType);
    fillRadio("orderRate",item.orderRate);
    //报价
    fillRadio("purchasingType",item.quoteorder.purchasingType);
    fillRadio("paymentTerm",item.quoteorder.paymentTerm);
    fillRadio("purchasingTime",item.quoteorder.purchasingTime);
    $("#period_1").attr("value", item.quoteorder.period);
    $("#projectProgress").attr("value", item.quoteorder.projectProgress);
    $("#additionalClause").attr("value", item.quoteorder.additionalClause);
    $("#quoteComments").attr("value", item.quoteorder.comments);
    if(item.quoteorder.invoiceType > 0){
        $('[name =invoiceType]').val(item.quoteorder.invoiceType);
    }
    if(item.quoteorder.freightDescription > 0){
        $('[name =freightDescription]').val(item.quoteorder.freightDescription);
    }
}
function fillRadio(radioName,radioValue){
    var ro=document.getElementsByName(radioName);
    for(var i=0;i<ro.length;i++){
        if(ro[i].value==radioValue){
            ro[i].checked=true;
            break;
        }
    }
}
function newContact(item) {
    $('#traderContactId_1').append('<option value="' + item.traderContactId + '">' + item.name + '/' + item.telephone + '/' + item.mobile + '</option>');
    layer.closeAll();
}


