<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.vedeng.erp</groupId>
        <artifactId>erp-trader</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>erp-trader-biz</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <name>erp-trader-biz</name>
    <description>erp-trader-biz</description>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-crm-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.crm.admin</groupId>
            <artifactId>crm-admin-api</artifactId>
            <version>1.0.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-infrastructure</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-core</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-old</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-trader-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-saleorder-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-saleorder-biz</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-system-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.vedeng.onedataapi</groupId>
            <artifactId>onedataapi-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <optional>true</optional>
        </dependency>
        
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-common-trace</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.vedeng.erp</groupId>
            <artifactId>erp-crm-api</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
