package com.vedeng.goods.manufacturer.dto;

import com.vedeng.system.model.Attachment;
import com.vedeng.trader.model.vo.TraderCertificateVo;
import lombok.Data;

import java.util.List;

/**
 * @Author: Cherny.chen
 * @Create: 2021/6/10 22:35
 */
@Data
public class TransCertificateDto {

    /**
     * 同步类型
     */
    private int syncType;

    /**
     * 客户id
     */
    private Integer traderId;

    /**
     * sysId
     */
    private int sysOptionDefinitionId;

    /**
     * 基本信息
     */
    private TraderCertificateVo baseInfo;

    /**
     * 新文件
     */
    private List<Attachment> sourceList;

    /**
     * 老文件
     */
    private List<TraderCertificateVo> oldInfoList;

}
