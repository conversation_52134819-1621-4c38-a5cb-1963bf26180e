package com.newtask.saleorder;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.order.dao.SaleorderGoodsMapper;
import com.vedeng.order.model.SaleorderGoods;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.activiti.editor.language.json.converter.util.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;


@JobHandler(value = "GoodsAvgPriceTask")
@Component
public class GoodsAvgPriceTask extends AbstractJobHandler {

	Logger log = LoggerFactory.getLogger(GoodsAvgPriceTask.class);

	@Autowired
	SaleorderGoodsMapper saleorderGoodsMapper;
	@Autowired
	com.vedeng.goods.dao.CoreSkuGenerateMapper coreSkuGenerateMapper;

	@Override
	public ReturnT<String> doExecute(String param) throws Exception {
		List<SaleorderGoods> list=saleorderGoodsMapper.caculateSkuLastestYearPrice();
		if(CollectionUtils.isNotEmpty(list)){
				list.forEach(item->{
					String userId="0";
					try {
						if(StringUtils.contains(item.getUnitName(),",")){
							userId = StringUtils.substring(item.getUnitName(), 0, item.getUnitName().indexOf(","));
						}else{
							userId=item.getUnitName();
						}

					}catch (Exception e){
						log.error("【execute】处理异常",e);
					}
					coreSkuGenerateMapper.updateSkuLatestYearAvgPrice(item.getGoodsId(),
					item.getPrice(),
							NumberUtils.toInt(userId));

				});
		}
		return SUCCESS;
	}

}
