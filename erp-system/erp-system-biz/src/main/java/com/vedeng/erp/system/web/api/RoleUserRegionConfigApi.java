package com.vedeng.erp.system.web.api;

import com.github.pagehelper.PageInfo;
import com.vedeng.common.core.base.ExceptionController;
import com.vedeng.common.core.base.R;
import com.vedeng.common.mybatis.domain.PageParam;
import com.vedeng.erp.system.dto.RoleUserRegionConfigDto;
import com.vedeng.erp.system.service.RoleUserRegionConfigApiService;
import com.vedeng.erp.system.vo.BusinessUserVO;
import com.vedeng.erp.system.vo.RoleUserRegionConfigQueryVO;
import com.vedeng.erp.system.vo.RoleUserRegionConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 人员与产线区域配置Controller
 * <AUTHOR>
 */
@ExceptionController
@RestController
@RequestMapping("/system/role-user-region-config")
@Slf4j
public class RoleUserRegionConfigApi {

    @Autowired
    private RoleUserRegionConfigApiService roleUserRegionConfigApiService;

    /**
     * 分页查询人员与产线区域配置
     */
    @RequestMapping("/page")
    public R<PageInfo<RoleUserRegionConfigVO>> page(@RequestBody PageParam<RoleUserRegionConfigQueryVO> pageParam) {
        return R.success(roleUserRegionConfigApiService.pageRoleUserRegionConfig(pageParam));
    }

    /**
     * 获取人员与产线区域配置详情
     */
    @RequestMapping("/get")
    public R<RoleUserRegionConfigDto> getInfo(@RequestParam("id") Long id) {
        return R.success(roleUserRegionConfigApiService.getById(id));
    }

    /**
     * 新增人员与产线区域配置
     */
    @RequestMapping("/add")
    public R<Boolean> add(@RequestBody RoleUserRegionConfigDto dto) {
        return R.success(roleUserRegionConfigApiService.saveRoleUserRegionConfig(dto));
    }

    /**
     * 修改人员与产线区域配置
     */
    @RequestMapping("/update")
    public R<Boolean> update(@RequestBody RoleUserRegionConfigDto dto) {
        return R.success(roleUserRegionConfigApiService.updateRoleUserRegionConfig(dto));
    }

    /**
     * 删除人员与产线区域配置
     */
    @RequestMapping("/delete")
    public R<Boolean> remove(@RequestParam("id") Long id) {
        return R.success(roleUserRegionConfigApiService.deleteRoleUserRegionConfig(id));
    }

    /**
     * 批量删除人员与产线区域配置
     */
    @RequestMapping("/batch-delete")
    public R<Boolean> batchRemove(@RequestBody List<Long> ids) {
        return R.success(roleUserRegionConfigApiService.batchDeleteRoleUserRegionConfig(ids));
    }

    /**
     * 获取所有业务人员列表（线上/线下销售、产线人员）
     */
    @RequestMapping("/business-users")
    public R<List<BusinessUserVO>> listAllBusinessUsers(@RequestParam(value = "userName", required = false) String userName) {
        return R.success(roleUserRegionConfigApiService.listAllBusinessUsers(userName));
    }

    /**
     * 获取人员与产线区域配置列表
     */
    @RequestMapping("/list")
    public R<List<RoleUserRegionConfigVO>> listRoleUserRegionConfig(@RequestBody RoleUserRegionConfigQueryVO query) {
        return R.success(roleUserRegionConfigApiService.listRoleUserRegionConfig(query));
    }

} 