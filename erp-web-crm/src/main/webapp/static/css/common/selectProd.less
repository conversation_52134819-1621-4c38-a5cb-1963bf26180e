@import (less) './mixin.css';

.ui-select-prod-wrap {
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    background: rgba(0, 0, 0, .6);
    z-index: 9999;

    .ui-select-prod-main {
        width: calc(100% - 40px);
        height: calc(100% - 40px);
        min-width: 1200px;
        max-width: 1400px;
        background: #fff;
        border-radius: 5px;
        position: relative;
        overscroll-behavior: contain;
        margin: 20px auto;

        .ui-select-prod-title {
            padding: 10px 20px 13px 20px;
            font-size: 16px;
            background: #F5F7FA;
            line-height: 21px;
            border-radius: 5px 5px 0 0;
            border-bottom: 1px solid #e1e5e8;
        }

        .ui-select-prod-close {
            position: absolute;
            top: 0;
            right: 0;
            width: 44px;
            height: 44px;
            font-size: 24px;
            color: #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;

            &:hover {
                color: #666;
            }
        }

        .ui-select-prod-needs-wrap {
            background: #E0F3FF;
            padding: 10px 20px;
            margin: -20px -20px 10px -20px;
           
            .ui-select-prod-needs-list {
                display: flex;
                flex-wrap: wrap;
                margin-bottom: -5px;
                margin-right: -20px;
            }

            .ui-select-prod-needs-item {
                display: flex;
                align-items: center;
                margin-right: 20px;
                margin-bottom: 5px;

                .ui-select-prod-needs-label {
                    color: #999;
                }

                .ui-select-prod-needs-txt {
                    color: #FF6600;
                    flex: 1;
                }
            }
        }

        .ui-select-prod-inner {
            position: relative;
            height: 100%;

            &.no-needs {
                height: calc(100% + 28px);
            }
        }

        .ui-select-prod-cnt {
            position: relative;
            padding: 20px;
            height: calc(100% - 45px);

            .ui-select-prod-options {
                position: absolute;
                top: 0;
                right: 0;
                display: flex;
                align-items: center;

                .option-item {
                    color: #09f;
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    cursor: pointer;
                    margin-right: 21px;
                    position: relative;
                    line-height: 33px;

                    &:hover {
                        color: #f60;
                    }

                    .vd-ui_icon {
                        font-size: 16px;
                        margin-right: 5px;
                    }

                    &::before {
                        content: "";
                        width: 1px;
                        height: 11px;
                        background: #e1e5e8;
                        position: absolute;
                        top: 11px;
                        right: -10px;
                    }

                    &:last-child {
                        margin-right: 0;

                        &::before {
                            display: none;
                        }
                    }
                }
            }

            .ui-select-prod-search {
                display: flex;
                align-items: center;
                margin-bottom: 10px;

                .search-input-wrap {
                    position: relative;
                    margin-right: 10px;

                    .search-drop-wrap {
                        background: #fff;
                        border-radius: 3px;
                        border: 1px solid #BABFC2;
                        box-shadow: rgba(0, 0, 0, 0.1) 0px 5px 10px;
                        position: absolute;
                        top: 30px;
                        left: 0;
                        z-index: 9;
                        width: 100%;

                        .ai-keywords-wrap {
                            padding: 10px;
                            font-size: 12px;
                            border-bottom: solid 1px #E1E5E8;

                            .ai-keywords-tip {
                                display: flex;
                                align-items: center;

                                img {
                                    width: 24px;
                                    height: 24px;
                                    margin-right: 5px;
                                }

                                .ai-tip-txt {
                                    flex: 1;
                                    display: flex;
                                    align-items: center;
                                    color: #999;

                                    @keyframes circle {
                                        0% {
                                            transform: rotate(0deg);
                                        }
                    
                                        100% {
                                            transform: rotate(360deg);
                                        }
                                    }
                    
                                    .icon-loading {
                                        font-size: 16px;
                                        margin-left: 5px;
                                        animation: circle 2.2s linear infinite;
                                    }

                                    .txt-link {
                                        color: #09f;
                                        cursor: pointer;

                                        &:hover {
                                            color: #f60;
                                        }
                                    }
                                }
                            }

                            .ai-keywords-list {
                                display: flex;
                                flex-wrap: wrap;
                                margin-top: 5px;
                                margin-right: -5px;
                                margin-bottom: -5px;

                                .ai-keywords-item {
                                    padding: 4px 10px;
                                    background: #E0F3FF;
                                    border-radius: 3px;
                                    margin-right: 5px;
                                    margin-bottom: 5px;
                                    cursor: pointer;

                                    &:hover {
                                        background: #B3E1FF;
                                    }

                                    &:active {
                                        background: #7FCCFF;
                                    }
                                }
                            }
                        }

                        .history-title {
                            font-size: 12px;
                            color: #999;
                            line-height: 30px;
                            padding: 0 10px;
                        }

                        .history-item {
                            padding: 6px 10px;
                            cursor: pointer;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;

                            &:hover {
                                background: #f5f7fa;
                            }
                        }
                    }

                    .history-clear {
                        padding: 10px;
                        display: flex;
                        justify-content: flex-end;
                        cursor: pointer;

                        .clear-btn {
                            color: #999;

                            &:hover {
                                color: #f60;
                            }
                        }
                    }
                }

                .vd-ui-button {
                    margin-right: 20px;
                }

                .vd-ui-cascader {
                    margin-right: 20px;
                }

                .vd-ui-checkbox-item {
                    .vd-ui-checkbox-inner {
                        font-size: 12px;
                        align-items: center;
                        margin-right: 20px;
                    }

                    .vd-ui-checkbox-icon {
                        margin-top: 1px;
                    }
                }

            }

            .ui-select-prod-filter {
                margin-bottom: 10px;

                .ui-select-prod-filter-list {
                    display: flex;
                    flex-wrap: wrap;
                    height: 26px;
                    overflow: hidden;
                    margin-bottom: 10px;

                    .filter-item {
                        display: flex;
                        align-items: center;
                        background: #F5F7FA;
                        border-radius: 3px;
                        font-size: 12px;
                        padding: 4px 10px;
                        margin-right: 5px;
                        cursor: pointer;

                        &:hover {
                            background: #EBEFF2;
                        }

                        &.active {
                            background: #E0F3FF;

                            .icon-down {
                                transform: rotate(180deg);
                            }
                        }

                        .item-txt {
                            margin-right: 3px;
                        }

                        .icon-down {
                            font-size: 16px;
                            color: #666;
                            line-height: 1;
                            transition: transform .22s ease;
                        }
                    }
                }

                .ui-select-prod-filter-selected {
                    font-size: 0;
                    overflow: hidden;
                    white-space: nowrap;

                    .icon-app-right {
                        font-size: 16px;
                        line-height: 1;
                        margin: 0 5px;
                        color: #999;
                        vertical-align: 2px;
                    }

                    .selected-keyword {
                        color: #09f;
                        cursor: pointer;
                        max-width: 200px;
                        display: inline-block;
                        font-size: 12px;

                        &:hover {
                            color: #f60;
                        }
                    }

                    .selected-item {
                        display: inline-block;
                        align-items: center;
                        border: 1px dashed #d7dade;
                        padding: 1px 0 1px 7px;
                        color: #f60;
                        cursor: pointer;
                        margin-right: 5px;
                        height: 22px;

                        .item-txt {
                            max-width: 175px;
                            font-size: 12px;
                            display: inline-block;
                        }

                        .icon-delete {
                            color: #999;
                            font-size: 16px;
                            line-height: 1;
                            vertical-align: 2px;
                        }

                        &:hover {
                            border: 1px solid #f60;

                            .icon-delete {
                                color: #f60;
                            }
                        }
                    }
                }
            }

            .ui-select-prod-filter-drop {
                position: absolute;
                width: calc(100% + 40px);
                left: -20px;
                top: 72px;
                height: calc(100% - 80px);
                background: rgba(0, 0, 0, .3);
                z-index: 9;

                .filter-drop-cnt {
                    width: 100%;
                    top: 0;
                    max-height: calc(100% - 20px);
                    position: absolute;
                    left: 0;
                    padding: 20px;
                    background: #fff;
                    display: flex;
                    flex-direction: column;

                    .filter-drop-list {
                        flex: 1;
                        overflow-y: auto;
                        overscroll-behavior: contain;
                        .scrollbar;
                    }

                    .filter-drop-list-inner {
                        margin-top: -10px;
                        display: flex;
                        flex-wrap: wrap;

                        .filer-item-link {
                            cursor: pointer;
                            margin-right: 20px;
                            margin-top: 10px;
                            font-size: 12px;

                            &:hover {
                                color: #f60;
                            }
                        }
                    }

                    .vd-ui-checkbox-item {
                        margin-top: 10px;

                        .vd-ui-checkbox-icon {
                            margin-top: 0;
                        }

                        .vd-ui-checkbox-inner {
                            font-size: 12px;
                            align-items: center;
                            margin-right: 20px;
                        }
                    }

                    .filter-drop-btn {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        margin-top: 20px;

                        .vd-ui-button {
                            margin-right: 10px;

                            &:last-child {
                                margin-right: 0;
                            }
                        }
                    }
                }
            }

            .ui-select-prod-body {
                max-height: calc(100vh - 271px);
                overflow-y: auto;
                margin-top: -1px;
                .scrollbar;
                overscroll-behavior: contain;

                &.scrollY {
                    margin-right: -6px;
                }

                .red {
                    color: #E64545;
                }
            }

            .ui-select-prod-table {
                width: 100%;
                border-collapse: collapse;
                border-spacing: 0;
                text-align: left;

                .ui-select-prod-tr {
                    &:hover {
                        .ui-select-prod-td {
                            background: #FAFBFC;
                        }
                    }

                    &.on-select {
                        .ui-select-prod-td {
                            background: #F0F9FF;
                        }

                        &:hover {
                            .ui-select-prod-td {
                                background: #F0F9FF;
                            }
                        }
                    }
                }

                .ui-select-prod-th {
                    font-size: 12px;
                    text-align: left;
                    font-weight: normal;
                    color: #999;
                    padding: 8px 9px;
                    border: solid 1px #E1E5E8;
                    background: #FAFBFC;

                    .vd-ui-checkbox-icon {
                        margin-right: 0;
                        margin-top: 1px;
                    }

                    &.right {
                        text-align: right;
                    }

                    &.center {
                        text-align: center;
                    }

                    .th-inner {
                        display: flex;
                        align-items: center;
                        height: 34px;
                        margin: -8px -9px;
                        cursor: pointer;
                        padding-left: 8px;

                        &:hover {
                            background: #EBEFF2;
                        }

                        .th-inner-txt {
                            flex: 1;
                        }

                        .th-inner-sort {
                            width: 28px;
                            position: relative;
                            height: 34px;

                            &::before,
                            &::after {
                                content: '';
                                position: absolute;
                                width: 0;
                                height: 0;
                                border: 4px solid transparent;
                                border-bottom: 6px solid #D7DADE;
                                top: 7px;
                                left: 12px;
                            }

                            &::after {
                                border: 4px solid transparent;
                                border-top: 6px solid #D7DADE;
                                top: 19px;
                            }

                            &.sort-up {
                                &::before {
                                    border-bottom-color: #f60;
                                }
                            }

                            &.sort-down {
                                &::after {
                                    border-top-color: #f60;
                                }
                            }
                        }
                    }
                }

                .ui-select-prod-td {
                    font-size: 12px;
                    text-align: left;
                    padding: 8px 9px;
                    border: solid 1px #E1E5E8;

                    .vd-ui-checkbox-icon {
                        margin-right: 0;
                        margin-top: 1px;
                    }

                    &.right {
                        text-align: right;
                    }

                    &.center {
                        text-align: center;
                    }

                    .ui-select-td-option {
                        color: #09f;
                        cursor: pointer;

                        &:hover {
                            color: #f60;
                        }
                    }

                    .td-link {
                        color: #09f;

                        &:hover {
                            color: #f60;
                        }

                        .td-link-txt {
                            margin-left: 5px;
                        }
                    }

                    .prod-tag {
                        width: 16px;
                        height: 16px;
                        display: inline-block;
                        margin-left: 3px;
                        vertical-align: -3px;

                        &:first-child {
                            margin-left: 0;
                        }

                        &.tag-1 {
                            background-image: url(../../image/goods-icon/prod-icon-1.jpg);
                        }

                        &.tag-2 {
                            background-image: url(../../image/goods-icon/prod-icon-2.jpg);
                        }

                        &.tag-3 {
                            background-image: url(../../image/goods-icon/prod-icon-3.jpg);
                        }

                        &.tag-4 {
                            background-image: url(../../image/goods-icon/prod-icon-4.jpg);
                        }

                        &.tag-5 {
                            background-image: url(../../image/goods-icon/prod-icon-5.jpg);
                        }

                        &.tag-6 {
                            background-image: url(../../image/goods-icon/prod-icon-6.jpg);
                        }
                    }

                    .fee-icon {
                        width: 16px;
                        height: 16px;
                        margin-right: 5px;
                        display: inline-block;
                        vertical-align: -3px;
                        background-image: url(../../image/goods-icon/no-fee.svg);
                        background-size: 100% 100%;
                    }
                }
            }

            .ui-page-wrap {
                position: absolute;
                bottom: 20px;
                width: 100%;
                left: 0;
                height: 33px;

                .vd-ui-page {
                    justify-content: flex-start;
                }

                .ui-prod-footer-btns {
                    position: absolute;
                    right: 0;
                    top: 0;
                    display: flex;
                    align-items: center;

                    .vd-ui-button {
                        margin-left: 10px;
                    }
                }
            }

            .prod-list-empty {
                text-align: center;
                padding: 80px 0;

                .icon-info2 {
                    font-size: 48px;
                    color: #09f;
                    margin-bottom: 20px;
                    line-height: 1;
                    display: inline-block;
                }
            }

            .ui-prod-loading {
                position: absolute;
                width: 100%;
                height: 100%;
                top: 0;
                left: 0;
                background: rgba(255, 255, 255, 0.6);
                display: flex;
                align-items: center;
                justify-content: center;

                @keyframes circle {
                    0% {
                        transform: rotate(0deg);
                    }

                    100% {
                        transform: rotate(360deg);
                    }
                }

                .icon-loading {
                    font-size: 48px;
                    color: #0099FF;
                    animation: circle 2.2s linear infinite;
                }
            }
        }
    }

    .td-tip-wrap {
        position: fixed;
        top: 200px;
        left: 200px;
        background: #fff;
        padding: 9px 15px;
        box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
        font-size: 12px;
        pointer-events: none;

        &::before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            border: 6px solid transparent;
            border-top-color: #fff;
            left: 15px;
            bottom: -12px;
        }
    }
}