package com.vedeng.erp.buyorder.mapper;

import com.vedeng.erp.buyorder.domain.entity.GeAuthorizationCertificate;

import java.util.List;

public interface GeAuthorizationCertificateMapper {
    int deleteByPrimaryKey(Integer authorizationCertificateId);

    int insert(GeAuthorizationCertificate record);

    int insertSelective(GeAuthorizationCertificate record);

    GeAuthorizationCertificate selectByPrimaryKey(Integer authorizationCertificateId);

    int updateByPrimaryKeySelective(GeAuthorizationCertificate record);

    int updateByParam(GeAuthorizationCertificate record);

    int updateByPrimaryKey(GeAuthorizationCertificate record);

    List<GeAuthorizationCertificate> selectByGeAuId(GeAuthorizationCertificate re);
}