.new-ui .input-text {
  border: solid 1px #BABFC2;
  border-radius: 3px;
  padding: 0 10px;
  line-height: 26px;
  height: 26px;
  font-size: 12px;
}
.new-ui .input-text:hover {
  border-color: #969B9E;
}
.new-ui .input-text::placeholder {
  color: #999;
}
.new-ui .input-text:focus {
  border-color: #0099FF;
}
.table.new-table .option-wrap {
  text-align: center;
}
.table.new-table .option-wrap .option-item {
  margin-right: 21px;
  color: #3384EF;
  cursor: pointer;
  position: relative;
  display: inline-block;
  vertical-align: top;
}
.table.new-table .option-wrap .option-item::before {
  content: "";
  width: 1px;
  height: 12px;
  background: #e1e5e8;
  position: absolute;
  right: -10px;
  top: 2px;
}
.table.new-table .option-wrap .option-item:last-child {
  margin-right: 0;
}
.table.new-table .option-wrap .option-item:last-child::before {
  display: none;
}
.table.new-table .option-wrap .option-item:hover {
  color: #f60;
}
.tyc-dialog-wrap {
  font-size: 14px;
  line-height: 1.5;
  padding: 20px;
}
.tyc-dialog-wrap .tyc-search-wrap {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}
.tyc-dialog-wrap .tyc-search-wrap .input-text {
  margin-right: 10px;
  width: 360px;
  height: 33px;
  line-height: 33px;
  font-size: 14px;
}
.tyc-dialog-wrap .tyc-search-wrap .btn {
  margin-right: 20px;
}
.tyc-dialog-wrap .tyc-search-wrap .search-tip {
  color: #999;
  font-size: 12px;
}
.tyc-dialog-wrap .tyc-list-empty {
  padding-top: 130px;
  padding-bottom: 130px;
  text-align: center;
  display: none;
}
.tyc-dialog-wrap .tyc-list-empty .empty-img {
  width: 110px;
  height: 95px;
  background-image: url(../../images/order/search-empty.svg);
  background-size: 100% 100%;
  margin: 0 auto 40px auto;
}
.tyc-dialog-wrap .list-table-tr {
  display: flex;
  align-items: center;
  margin-top: -1px;
}
.tyc-dialog-wrap .list-table-tr .th-item,
.tyc-dialog-wrap .list-table-tr .td-item {
  border: 1px solid #E1E5E8;
  border-left: 0;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(1),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(1) {
  width: 500px;
  border-left: 1px solid #E1E5E8;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(2),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(2) {
  width: 160px;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(3),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(3) {
  width: 160px;
}
.tyc-dialog-wrap .list-table-tr .th-item:nth-child(4),
.tyc-dialog-wrap .list-table-tr .td-item:nth-child(4) {
  width: 100px;
}
.tyc-dialog-wrap .list-table-tr .th-item {
  background-color: #FAFBFC;
  padding: 7px 20px;
  color: #999;
}
.tyc-dialog-wrap .list-table-tr .td-item {
  border-left: 0;
  padding: 13px 20px;
}
.tyc-dialog-wrap .list-table-tr .text-line-1 {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
.tyc-dialog-wrap .list-table-tr .td-option {
  display: inline-block;
  cursor: pointer;
  color: #09f;
}
.tyc-dialog-wrap .list-table-tr .td-option:hover {
  color: #f60;
}
.tyc-dialog-wrap .list-table-body {
  max-height: 478px;
  overflow: auto;
  overscroll-behavior: contain;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-track {
  background: transparent;
  width: 6px;
  height: 6px;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-thumb {
  background: #D7DADE;
  width: 6px;
  height: 6px;
  border-radius: 3px;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-thumb:hover {
  background: #BABFC2;
}
.tyc-dialog-wrap .list-table-body::-webkit-scrollbar-thumb:active {
  background: #969B9E;
}
.tyc-dialog-wrap .list-table-body.on-scroll {
  margin-right: -6px;
}
.new-ui .btn {
  background: #F5F7FA;
  border: 1px solid #BABFC2;
  border-radius: 3px;
  padding: 5px 15px;
  cursor: pointer;
  margin-right: 10px;
  display: inline-block;
}
.new-ui .btn:hover {
  background: #EBEFF2;
}
.new-ui .btn:active {
  background: #E1E5E8;
}
.new-ui .btn.btn-primary {
  background: #0099FF;
  color: #fff;
  border-color: #0099FF;
}
.new-ui .btn.btn-primary:hover {
  background: #0087E0;
  border-color: #0087E0;
}
.new-ui .btn.btn-primary:active {
  background: #006CB3;
  border-color: #006CB3;
}
.new-ui .input-select {
  border: 1px solid #BABFC2;
  height: 26px;
  line-height: 26px;
  width: 100%;
  border-radius: 3px;
  padding: 0 8px;
  cursor: pointer;
}
.new-ui .input-select:hover {
  border-color: #969B9E;
}
.new-ui .input-select:focus {
  border-color: #09f;
}
.new-ui .input-select.error {
  border-color: #e64545;
  color: #333;
  background: #fce9e9;
}
.new-ui .input-select.error option {
  background: #fff;
}
.new-ui .input-select::placeholder {
  background: #999;
}
.new-ui .vd-ui-search-related > .vd-ui-related-wrap {
  top: 26px;
}
.new-ui .vd-ui-cascader .vd-ui-cascader-wrapper > .vd-ui-input > input {
  height: 26px;
  font-size: 12px;
}
.new-ui .vd-ui-cascader .vd-ui-cascader-wrapper > .vd-ui-input .icon > i {
  line-height: 24px;
}
.new-ui .vd-ui-search-related > .vd-ui-input > input {
  height: 26px;
  font-size: 12px;
}
.new-ui .new-tip-wrap {
  display: inline-block;
  position: relative;
}
.new-ui .new-tip-wrap .icon-info1 {
  color: #09f;
  margin-left: 5px;
  vertical-align: -2px;
}
.new-ui .new-tip-wrap .new-tip-cnt {
  position: absolute;
  top: -8px;
  left: 30px;
  border-radius: 3px;
  background: #fff;
  padding: 6px 10px;
  box-shadow: rgba(0, 0, 0, 0.12) 0px 3px 6px;
  display: none;
  white-space: nowrap;
}
.new-ui .new-tip-wrap .new-tip-cnt::before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border: 5px solid transparent;
  border-right-color: #fff;
  left: -10px;
  top: 9px;
}
.new-ui .new-tip-wrap:hover .new-tip-cnt {
  display: block;
}
