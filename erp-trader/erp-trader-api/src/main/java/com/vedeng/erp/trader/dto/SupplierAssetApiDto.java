package com.vedeng.erp.trader.dto;

import com.vedeng.common.core.base.BaseDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 供应商资产
 * @date 2023/11/16 20:00
 */
@Getter
@Setter
public class SupplierAssetApiDto extends BaseDto {

    /**
     * 主键
     */
    private Integer supplierAssetId;

    /**
     * 交易人id
     */
    private Integer traderId;

    /**
     * 供应商id
     */
    private Integer traderSupplierId;

    /**
     * 资产类型
     * 1:返利
     * 2:余额
     * 3:信用
     */
    private Integer assetType;

    /**
     * 资产
     */
    private BigDecimal asset;

    /**
     * 可用资产
     */
    private BigDecimal applyAsset;

    /**
     * 占用资产
     */
    private BigDecimal occupyAsset;

}
