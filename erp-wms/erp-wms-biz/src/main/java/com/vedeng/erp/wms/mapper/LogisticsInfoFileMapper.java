package com.vedeng.erp.wms.mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

import com.vedeng.erp.wms.domain.entity.LogisticsInfoFileEntity;

public interface LogisticsInfoFileMapper {
    /**
     * delete by primary key
     * @param logisticsInfoFileId primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer logisticsInfoFileId);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(LogisticsInfoFileEntity record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(LogisticsInfoFileEntity record);

    /**
     * select by primary key
     * @param logisticsInfoFileId primary key
     * @return object by primary key
     */
    LogisticsInfoFileEntity selectByPrimaryKey(Integer logisticsInfoFileId);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(LogisticsInfoFileEntity record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(LogisticsInfoFileEntity record);

    List<LogisticsInfoFileEntity> findAllByExpressId(@Param("expressId")Integer expressId);


}