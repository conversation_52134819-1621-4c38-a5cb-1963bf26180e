package com.wms.service.processor.input;

import com.alibaba.fastjson.JSON;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.stock.StockOperateTypeConst;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.erp.buyorder.dto.PurchaseDeliveryBatchDetailDto;
import com.vedeng.erp.buyorder.service.PurchaseDeliveryBatchDetailService;
import com.vedeng.logistics.dao.BarcodeMapper;
import com.vedeng.logistics.dao.ExpressDetailMapper;
import com.vedeng.logistics.model.Barcode;
import com.vedeng.logistics.model.Express;
import com.vedeng.logistics.model.ExpressDetail;
import com.vedeng.logistics.model.WarehouseGoodsOperateLogVirtual;
import com.vedeng.logistics.service.WarehouseGoodsOperateLogVirtualService;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.vo.BuyorderGoodsVo;
import com.vedeng.order.model.vo.BuyorderVo;
import com.vedeng.stock.api.stock.dto.WarehouseDto;
import com.wms.constant.LogicalEnum;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.constant.WmsLogicalOperateTypeEnum;
import com.wms.dto.InputOrderDto;
import com.wms.dto.InputOrderGoodsDto;
import com.wms.dto.StockCalculateDto;
import com.wms.service.processor.AbstractInputOrderProcessor;
import com.wms.service.util.WmsCommonUtil;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 直发采购单下发WMS采购入库任务方法回调
 *
 * <AUTHOR>
 */
@Service
public class DeliveryDirectPurchaseInputOrderProcessor extends AbstractInputOrderProcessor {
    private static final Logger logger = LoggerFactory.getLogger(DeliveryDirectPurchaseInputOrderProcessor.class);

    @Autowired
    private WarehouseGoodsOperateLogVirtualService warehouseGoodsOperateLogVirtualService;

    @Autowired
    private PurchaseDeliveryBatchDetailService purchaseDeliveryBatchDetailService;

    @Resource
    private BarcodeMapper barcodeMapper;

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private ExpressDetailMapper expressDetailMapper;

    @Override
    protected void updateOrderData(InputOrderDto requestBean) throws Exception {
    }

    @Override
    protected void insertOrUpdateWarehouseLog(InputOrderDto orderDto) {
        logger.info("直发采购单下发WMS采购入库任务方法回调添加出入库日志信息 orderDto:{}", JSON.toJSONString(orderDto));

        String buyOrderNo = WmsCommonUtil.getOriginalOrderNo(orderDto.getASNReference1());

        BuyorderVo buyOrder = buyorderMapper.getBuyorderVoByBuyorderNo(buyOrderNo);

        Map<String, BigDecimal> skuPriceMap = buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyOrder.getBuyorderId())
                .stream().collect(Collectors.toMap(BuyorderGoodsVo::getSku, BuyorderGoodsVo::getPrice));

        final long currentTimeMillis = System.currentTimeMillis();


        orderDto.getDetails().forEach(detail -> {

            Integer expressDetailId = purchaseDeliveryBatchDetailService.queryExpressDetailIdById(Integer.parseInt(detail.getUserDefine4()));
            if (expressDetailId == null) {
                logger.error("同行单数据中快递信息异常 同行单详情ID:{}", detail.getUserDefine4());
                throw new RuntimeException("同行单数据中快递信息异常 同行单详情ID:" + detail.getUserDefine4());
            }

            Express expressInfo = expressDetailMapper.getExpressByDetailId(expressDetailId);
            if (expressInfo == null) {
                logger.error("同行单数数据关联包裹信息异常 expressDetailId:{}", expressDetailId);
                throw new RuntimeException("同行单数数据关联包裹信息异常 expressDetailId:" + expressDetailId);
            }

            ExpressDetail expressDetailInfo = expressDetailMapper.selectByPrimaryKey(expressDetailId);
            if (expressDetailInfo == null){
                logger.error("同行单数数据关联包裹详情信息异常 expressDetailId:{}", expressDetailId);
                throw new RuntimeException("同行单数数据关联包裹详情信息异常 expressDetailId:" + expressDetailId);
            }


            PurchaseDeliveryBatchDetailDto purchaseDeliveryBatchDetailDto = purchaseDeliveryBatchDetailService.queryInfoByDetailId(Integer.parseInt(detail.getUserDefine4()));
            if (purchaseDeliveryBatchDetailDto == null){
                logger.error("直发入库关联同行单数据异常 同行单ID:{}", detail.getUserDefine4());
                throw new RuntimeException("直发入库关联同行单数据异常 同行单ID:" + detail.getUserDefine4());
            }

            if (purchaseDeliveryBatchDetailDto.getWmsHandledArrivalCount() + detail.getReceivedQty().intValue() > purchaseDeliveryBatchDetailDto.getArrivalCount()){
                logger.error("直发入库回传数据超量 wmsHandledArrivalCount:{}, ReceivedQty:{}, arrivalCount:{}",
                        purchaseDeliveryBatchDetailDto.getWmsHandledArrivalCount(), detail.getReceivedQty().intValue(), purchaseDeliveryBatchDetailDto.getArrivalCount());
                throw new RuntimeException("直发入库回传数据超量,同行单ID" + detail.getUserDefine4());
            }

            purchaseDeliveryBatchDetailService.updateArrivalCountByDetailId(Integer.parseInt(detail.getUserDefine4()), detail.getReceivedQty().intValue());


            WarehouseGoodsOperateLogVirtual warehouseGoodsOperateLogVirtual = new WarehouseGoodsOperateLogVirtual();

            //新增贝登条码
            Integer barcodeId = barcodeMapper.getBarcodeIdByBarcode(detail.getLotAtt11());
            if (barcodeId == null) {
                Barcode barcode = new Barcode();
                barcode.setBarcode(detail.getLotAtt11());
                barcode.setType(getBarcodeType(orderDto, detail));
                barcode.setDetailGoodsId(getRelateId(orderDto, detail));
                barcode.setGoodsId(Integer.valueOf(detail.getSKU().substring(1)));
                barcode.setIsEnable(ErpConst.ONE);
                barcode.setAddTime(currentTimeMillis);
                barcode.setModTime(currentTimeMillis);
                barcodeMapper.insertSelective(barcode);
                barcodeId = barcode.getBarcodeId();
            }


            warehouseGoodsOperateLogVirtual.setBarcodeId(barcodeId);
            warehouseGoodsOperateLogVirtual.setCompanyId(ErpConst.ONE);
            warehouseGoodsOperateLogVirtual.setIsEnable(ErpConst.ONE);
            warehouseGoodsOperateLogVirtual.setOperateType(getOperateType(orderDto));
            warehouseGoodsOperateLogVirtual.setRelatedId(expressDetailInfo.getRelatedId());
            warehouseGoodsOperateLogVirtual.setGoodsId(Integer.valueOf(detail.getSKU().substring(1)));
            warehouseGoodsOperateLogVirtual.setNum(detail.getReceivedQty().intValue());

            //效期
            warehouseGoodsOperateLogVirtual.setExpirationDate(DateUtil.convertLong(detail.getLotAtt02() + " 00:00:00", DateUtil.TIME_FORMAT));

            // 入库时间加上当前时分秒
            LocalDateTime localDateTime = LocalDateTime.now();
            String format = localDateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            //入库时间
            warehouseGoodsOperateLogVirtual.setCheckStatusTime(DateUtil.convertLong(detail.getLotAtt03() + " "+format, DateUtil.TIME_FORMAT));

            //生产日期
            warehouseGoodsOperateLogVirtual.setProductDate(DateUtil.convertLong(detail.getLotAtt01() + " 00:00:00", DateUtil.TIME_FORMAT));

            // addTime 改 + 时分秒
            DateTimeFormatter ftf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            String format1 = ftf.format(LocalDateTime.ofInstant(Instant.ofEpochMilli(expressInfo.getDeliveryTime()), ZoneId.systemDefault()));
            format1 = format1 + " " + format;
            warehouseGoodsOperateLogVirtual.setAddTime(DateUtil.convertLong(format1, DateUtil.TIME_FORMAT));
            warehouseGoodsOperateLogVirtual.setModTime(currentTimeMillis);
            //灭菌批号
            warehouseGoodsOperateLogVirtual.setSterilzationBatchNumber(detail.getLotAtt05());

            //批次号
            warehouseGoodsOperateLogVirtual.setBatchNumber("NO-LOT".equals(detail.getLotAtt04()) ? "" : detail.getLotAtt04());

            warehouseGoodsOperateLogVirtual.setLogicalWarehouseId(LogicalEnum.getLogicalWarehouseIdByCode(detail.getLotAtt08()));
            //北登批次码
            warehouseGoodsOperateLogVirtual.setVedengBatchNumer(detail.getLotAtt11());

            //剩余库存数量
            warehouseGoodsOperateLogVirtual.setLastStockNum(detail.getReceivedQty().intValue());
            warehouseGoodsOperateLogVirtual.setCreator(ErpConst.NJ_ADMIN_ID);
            warehouseGoodsOperateLogVirtual.setUpdater(ErpConst.NJ_ADMIN_ID);

            //入库类型
            warehouseGoodsOperateLogVirtual.setLogType(0);
            warehouseGoodsOperateLogVirtual.setComments(orderDto.getASNReference1());
            warehouseGoodsOperateLogVirtual.setDedicatedBuyorderNo(detail.getLotAtt07());

            //商品成本价
            BigDecimal costPrice = skuPriceMap.getOrDefault(detail.getSKU(), BigDecimal.ZERO);
            warehouseGoodsOperateLogVirtual.setCostPrice(costPrice);
            warehouseGoodsOperateLogVirtual.setNewCostPrice(costPrice);

            warehouseGoodsOperateLogVirtual.setIsExpress(ErpConst.ONE);
            warehouseGoodsOperateLogVirtual.setCreateTime(currentTimeMillis);

//            if (ErpConst.ONE.equals(purchaseDeliveryBatchDetailDto.getUnionSequence())){
            warehouseGoodsOperateLogVirtual.setBarcodeFactory(detail.getLotAtt12());
//            }

            logger.info("单据入库，新增入库日志数据:{}" + JSON.toJSONString(warehouseGoodsOperateLogVirtual));

            //新增采购入库日志
            warehouseGoodsOperateLogVirtualService.insertSelective(warehouseGoodsOperateLogVirtual);
        });

    }

    @Override
    protected int getOperateType(InputOrderDto orderDto) {
        return StockOperateTypeConst.WAREHOUSE_IN;
    }

    @Override
    protected int getWmsLogicalOperateType(InputOrderDto orderDto) {
        return WmsLogicalOperateTypeEnum.BUYORDER_TYPE.getOperateTypeCode();
    }

    @Override
    protected int getRelateId(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return Integer.parseInt(goodsDto.getUserDefine1());
    }

    @Override
    protected List<WarehouseDto> getWarehouseStockList(List<StockCalculateDto> stockCalculateList) {
        return null;
    }

    @Override
    protected int getBarcodeType(InputOrderDto orderDto, InputOrderGoodsDto goodsDto) {
        return 1;
    }

    @Override
    protected void commonValidator(InputOrderDto requestBean) throws Exception {
        if (requestBean == null || CollectionUtils.isEmpty(requestBean.getDetails())) {
            logger.error("入库回传数据异常，请检查。:{}", JSON.toJSONString(requestBean));
            throw new Exception("入库回传数据异常，请检查。");
        }

        if (!WmsInterfaceOrderType.DELIVERY_DIRECT_IN.equals(requestBean.getASNType())) {
            logger.error("直发采购单:{}的类型错误!" + requestBean.getASNReference1());
            throw new Exception("直发采购单:" + requestBean.getASNReference1() + "的类型错误!");
        }

        if (CollectionUtils.isNotEmpty(requestBean.getDetails().stream()
                .filter(item -> StringUtil.isBlank(item.getUserDefine4()))
                .collect(Collectors.toList()))) {
            logger.error("直发采购单:{}的同行单数据异常。" + requestBean.getASNNO());
            throw new Exception("直发采购单:" + requestBean.getASNNO() + "的同行单数据异常!");
        }


        if (CollectionUtils.isNotEmpty(requestBean.getDetails().stream()
                .filter(item -> StringUtil.isBlank(item.getLotAtt11())).collect(Collectors.toList()))) {
            logger.error("入库类型没有贝登批次码,消费失败:{}", requestBean.getASNReference1());
            throw new RuntimeException(requestBean.getASNReference1() + "入库类型没有贝登批次码,消费失败");
        }

    }

    @Override
    protected void synchronizeStockData(InputOrderDto inputOrderDto) throws Exception {

    }

    @Override
    protected void customHandle(InputOrderDto requestBean) throws Exception {

    }

    @Override
    protected void insertOrUpdateLogicalOrderGoods(InputOrderDto orderDto) throws Exception {

    }
}
