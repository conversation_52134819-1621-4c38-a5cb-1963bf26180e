package com.vedeng.crm.presales.facade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.base.R;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.crm.api.FollowUpRecordApiService;
import com.vedeng.crm.api.ShareApiService;
import com.vedeng.crm.business.business.service.CrmBusinessChanceService;
import com.vedeng.crm.presales.dto.PreSalesDataEnum;
import com.vedeng.crm.presales.dto.PreSalesInfoDto;
import com.vedeng.crm.presales.dto.PreSalesInfoType;
import com.vedeng.crm.presales.facade.PreSalesFacade;
import com.vedeng.crm.presales.mapstruct.PreSalesInfoToChanceConverter;
import com.vedeng.crm.presales.mapstruct.PreSalesInfoToLeadsConverter;
import com.vedeng.erp.business.common.enums.BusinessLeadsFollowStatusEnums;
import com.vedeng.erp.business.domain.dto.BusinessChanceDto;
import com.vedeng.erp.business.domain.dto.BusinessLeadsDto;
import com.vedeng.erp.business.domain.entity.BusinessLeadsEntity;
import com.vedeng.erp.business.service.BusinessChanceService;
import com.vedeng.erp.business.service.BusinessLeadsService;
import com.vedeng.erp.common.dto.RSalesJBusinessOrderDto;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.trader.common.enums.CommunicateRecordTypeEnum;
import com.vedeng.erp.trader.domain.vo.TraderCustomerInfoVo;
import com.vedeng.erp.trader.dto.CommunicateRecordDto;
import com.vedeng.erp.trader.dto.RSalesJTraderDto;
import com.vedeng.erp.trader.service.RSalesJTraderApiService;
import com.vedeng.erp.trader.service.TraderCustomerBaseService;
import com.vedeng.goods.dto.BusinessOrderCategoryDto;
import com.vedeng.goods.service.BusinessOrderCategoryApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.vedeng.common.core.domain.CurrentUser.getCurrentUser;

/**
 * @Description
 * <AUTHOR>
 * @Version V1.0.0
 * @Since 1.0
 * @Date 2024/12/9
 */
@Slf4j
@Service
public class PreSalesFacadeImpl implements PreSalesFacade {

    @Autowired
    private BusinessLeadsService businessLeadsService;

    @Autowired
    private CrmBusinessChanceService crmBusinessChanceService;

    @Autowired
    private FollowUpRecordApiService followUpRecordApiService;

    @Autowired
    private PreSalesInfoToLeadsConverter preSalesInfoToLeadsConverter;

    @Autowired
    private PreSalesInfoToChanceConverter preSalesInfoToChanceConverter;


    private boolean checkIsBusinessChance(PreSalesInfoDto preSaleInfo){
        //先行判断该线索提交内容是否符合商机的条件
        //业务类型）商机类型-字典值(小产品、大单品、综合项目、AED、应急)
        Integer businessType = preSaleInfo.getBusinessType();
        //产品信息
        String goodsInfo = preSaleInfo.getGoodsInfo();
        //预计成单金额
        BigDecimal amount = preSaleInfo.getAmount();
        //预计成单日期-列表字段
        Date orderTimeDate = preSaleInfo.getOrderTime();
        if(businessType !=null && StringUtils.isNotBlank(goodsInfo) && amount != null &&
                amount.compareTo(BigDecimal.ZERO)>0
                && orderTimeDate !=null){
            return true;
        }
        return false;
    }

    @Override
    public PreSalesInfoType addPreSalesInfo(PreSalesInfoDto preSaleInfo) {
        boolean isBusinessChance = checkIsBusinessChance(preSaleInfo);
        log.info("销售自建线索商机时,{},{}",isBusinessChance,preSaleInfo);
        if(isBusinessChance){
            //以下是走新建商机的逻辑
            BusinessChanceDto businessChanceDto =  preSalesInfoToChanceConverter.toDto(preSaleInfo);
            if(preSaleInfo.getBelongerId()!= null && preSaleInfo.getBelongerId() !=0){
                businessChanceDto.setUserId(preSaleInfo.getBelongerId());
                businessChanceDto.setUsername(preSaleInfo.getBelonger());
            }
            BusinessChanceDto data = crmBusinessChanceService.add(businessChanceDto);
            return PreSalesInfoType.builder().preSaleDataEnum(PreSalesDataEnum.BUSSINESS_CHANCE).dataId(data.getBussinessChanceId()).dataNo(data.getBussinessChanceNo()).build();
        }
        //以下是新建线索的处理逻辑
        BusinessLeadsDto businessLeadsDto = preSalesInfoToLeadsConverter.toDto(preSaleInfo);
        Integer leadsId = businessLeadsService.add(businessLeadsDto);
        businessLeadsDto.setId(leadsId);
        // 调用crm新增跟进记录接口
        CommunicateRecordDto communicateRecordDto = businessLeadsDto.getCommunicateRecordDto();
        if (Objects.nonNull(communicateRecordDto)) {
            log.info("CRM线索新增跟进记录，communicateRecordDto:{}", JSON.toJSONString(communicateRecordDto));
            communicateRecordDto.setCommunicateType(CommunicateRecordTypeEnum.BUSINESS_LEAD.getCode());
            communicateRecordDto.setRelatedId(leadsId);
            communicateRecordDto.setTraderName(businessLeadsDto.getTraderName());
            communicateRecordDto.setTraderId(businessLeadsDto.getTraderId());
            communicateRecordDto.setBelongUserId(businessLeadsDto.getCreator());
            communicateRecordDto.setBelongUserName(businessLeadsDto.getCreatorName());
            communicateRecordDto.setFollowUpType(5901);
            communicateRecordDto.setContactMob(businessLeadsDto.getPhone());
            communicateRecordDto.setTelephone(businessLeadsDto.getTelephone());
            communicateRecordDto.setContact(businessLeadsDto.getContact());
            communicateRecordDto.setBegintime(System.currentTimeMillis());
            communicateRecordDto.setBeginTimeDate(new Date());
            communicateRecordDto.setEndtime(System.currentTimeMillis()+2*60*1000);
            communicateRecordDto.setEndTimeDate(new Date(System.currentTimeMillis()+2*60*1000));
            communicateRecordDto.setContentSuffix("首次创建"+(StringUtils.isBlank(businessLeadsDto.getGoodsInfo())?"":("#"+businessLeadsDto.getGoodsInfo()+"#")));
            followUpRecordApiService.add(communicateRecordDto);
        }

        OrderTerminalDto orderTerminalDto = businessLeadsDto.getOrderTerminalDto();
        if (Objects.nonNull(orderTerminalDto) && StrUtil.isNotBlank(orderTerminalDto.getTerminalName())) {
            CurrentUser currentUser = getCurrentUser();
            orderTerminalDto.setTerminalTraderNature(businessLeadsDto.getTerminalTraderNature());//将线索的终端商性质传递给订单终端
            orderTerminalDto.setTerminalName(orderTerminalDto.getTerminalName());
            orderTerminalDto.setAreaId(businessLeadsDto.getCountyId());//设置为区的ID
            if(businessLeadsDto.getCountyId() !=null && businessLeadsDto.getCountyId()>0){
                orderTerminalDto.setAreaName(
                        (StringUtils.isEmpty(businessLeadsDto.getProvince())?"":businessLeadsDto.getProvince())
                        +" "
                        +(StringUtils.isEmpty(businessLeadsDto.getCity())?"":businessLeadsDto.getCity())
                        +" "
                        +(StringUtils.isEmpty(businessLeadsDto.getCounty())?"":businessLeadsDto.getCounty())
                        );
            }
            orderTerminalDto.setBusinessId(leadsId);
            orderTerminalDto.setBusinessNo(businessLeadsDto.getLeadsNo());
            orderTerminalDto.setBusinessType(ErpConstant.THREE);//线索
            orderTerminalDto.setAddTime(new Date());
            orderTerminalDto.setModTime(new Date());
            orderTerminalDto.setCreator(currentUser.getId());
            orderTerminalDto.setUpdater(currentUser.getId());
            orderTerminalDto.setCreatorName(currentUser.getUsername());
            orderTerminalDto.setUpdaterName(currentUser.getUsername());
        }

        //checkAutoShareForBusinessLeads(businessLeadsDto);

        return PreSalesInfoType.builder().preSaleDataEnum(PreSalesDataEnum.BUSSINESS_LEADS).dataId(leadsId).dataNo(businessLeadsDto.getLeadsNo()).build();
    }


    public void checkAutoShareForBusinessLeads( BusinessLeadsDto businessLeadsDto){
        CurrentUser currentUser = getCurrentUser();
        //如果是建档客户的情况下，判断当前客户，是否存在分享给当前操作人的情况
        if(businessLeadsDto.getTraderId() != null && businessLeadsDto.getTraderId()> 0){
            TraderCustomerInfoVo traderCustomerInfoVo = traderCustomerBaseService.getTraderCustomerInfo(businessLeadsDto.getTraderId());
            if(traderCustomerInfoVo != null && !currentUser.getId().equals(traderCustomerInfoVo.getSaleId())){ // 如果创建商机的人，不是当前客户的归属人,进入判断是否分享的逻辑
                RSalesJTraderDto rSalesJTraderDto =  rSalesJTraderApiService.getShareTraderByUserId(businessLeadsDto.getTraderId(),currentUser.getId());//分享的记录
                if(rSalesJTraderDto != null){
                    RSalesJBusinessOrderDto rSalesJBusinessOrderDto = new RSalesJBusinessOrderDto();
                    //查看是否已经分享记录，当前是创建，肯定没有
                    Integer businessId = businessLeadsDto.getId();//商机id
                    Integer currentUserId = currentUser.getId();//当前操作人
                    String currentUserUsername = currentUser.getUsername();//当前操作人

                    rSalesJBusinessOrderDto.setAddTime(new Date());
                    rSalesJBusinessOrderDto.setModTime(new Date());
                    rSalesJBusinessOrderDto.setCreator(currentUser.getId());
                    rSalesJBusinessOrderDto.setUpdater(currentUser.getId());
                    rSalesJBusinessOrderDto.setBusinessNo(businessLeadsDto.getLeadsNo());
                    rSalesJBusinessOrderDto.setBusinessId(businessId);
                    rSalesJBusinessOrderDto.setBusinessType(5);//业务类型 1.商机 2.报价 3.订单 4.售后 5.线索

                    rSalesJBusinessOrderDto.setSaleUserId( currentUserId );
                    rSalesJBusinessOrderDto.setSaleUserName( currentUserUsername);
                    shareApiService.shareAutoBusiness(rSalesJBusinessOrderDto,currentUser);
                }

            }
        }
    }

    @Autowired
    private RSalesJTraderApiService rSalesJTraderApiService;

    @Autowired
    private ShareApiService shareApiService;

    @Autowired
    private TraderCustomerBaseService traderCustomerBaseService;

    @Autowired
    private BusinessOrderCategoryApiService businessOrderCategoryService;


    @Override
    public PreSalesInfoType updatePreSalesInfo(PreSalesInfoDto preSaleInfo) {
        PreSalesInfoType preSalesInfoType = preSaleInfo.getPreSalesInfoType();
        if(preSaleInfo.getPreSalesInfoType() == null){
            log.error("编辑线索或商机时，id必传");
            throw new ServiceException("编辑线索或商机时，id必传");
        }
        if(PreSalesDataEnum.BUSSINESS_LEADS.equals(preSalesInfoType.getPreSaleDataEnum())){
            BusinessLeadsDto oldBusinessLeadsEntity = businessLeadsService.findById(preSalesInfoType.getDataId());
            if (oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.CLOSE.getType())
                    || oldBusinessLeadsEntity.getFollowStatus().equals(BusinessLeadsFollowStatusEnums.OPPORTUNITY.getType())) {
                throw new ServiceException("已关闭，已商机的线索无法再次编辑，请核实线索状态。");
            }
            BusinessLeadsDto businessLeadsDto = preSalesInfoToLeadsConverter.toDto(preSaleInfo);
            businessLeadsDto.setId(preSalesInfoType.getDataId());
            businessLeadsService.update(businessLeadsDto);

            //checkAutoShareForBusinessLeads(businessLeadsDto);

            String categoryIds = null;
            List<BusinessOrderCategoryDto> byBusinessIdAndType = businessOrderCategoryService.getByBusinessIdAndType(businessLeadsDto.getId(), ErpConstant.ZERO);
            if(CollUtil.isNotEmpty(byBusinessIdAndType)){
                // 分类使用逗号拼接
                categoryIds = byBusinessIdAndType.stream().map(BusinessOrderCategoryDto::getCategoryId).map(String::valueOf).collect(Collectors.joining(","));
            }


            if(checkIsBusinessChance(preSaleInfo)){
//                BusinessChanceDto newChance = businessLeadsService.getLeadsToChance(preSalesInfoType.getDataId());
                BusinessChanceDto newChance = preSalesInfoToChanceConverter.toDto(preSaleInfo);

                newChance.setUserId(oldBusinessLeadsEntity.getBelongerId());
                newChance.setUsername(oldBusinessLeadsEntity.getBelonger());
                newChance.setLeadsNo(oldBusinessLeadsEntity.getLeadsNo());
                newChance.setBusinessLeadsId(preSalesInfoType.getDataId());
                newChance.setCategoryIds(categoryIds);
                BusinessChanceDto resultChance =  crmBusinessChanceService.add(newChance);
                //走线索转商机的逻辑
                return PreSalesInfoType.builder().preSaleDataEnum(PreSalesDataEnum.BUSSINESS_CHANCE).dataId(resultChance.getBussinessChanceId()).build();
            }
            return PreSalesInfoType.builder().preSaleDataEnum(PreSalesDataEnum.BUSSINESS_LEADS).dataId(preSalesInfoType.getDataId()).build();

        }else if(PreSalesDataEnum.BUSSINESS_CHANCE.equals(preSalesInfoType.getPreSaleDataEnum())){
            BusinessChanceDto businessChanceDto = preSalesInfoToChanceConverter.toDto(preSaleInfo);
            if(preSaleInfo.getCountyId() !=null && preSaleInfo.getCountyId()>0){
                businessChanceDto.setTerminalTraderRegion(preSaleInfo.getProvinceId()+","+preSaleInfo.getCityId()+","+preSaleInfo.getCountyId());
            }

            businessChanceDto.setBussinessChanceId(preSalesInfoType.getDataId());
            BusinessChanceDto resultChanceDto =  crmBusinessChanceService.update(businessChanceDto);
            return PreSalesInfoType.builder().preSaleDataEnum(PreSalesDataEnum.BUSSINESS_CHANCE).dataId(resultChanceDto.getBussinessChanceId()).build();
        }else{
            log.error("传入的PreSalesInfoType类型错误");
            throw new ServiceException("传入的PreSalesInfoType类型错误");
        }

    }


}
