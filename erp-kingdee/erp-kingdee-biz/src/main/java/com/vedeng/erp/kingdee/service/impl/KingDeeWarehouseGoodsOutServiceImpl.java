package com.vedeng.erp.kingdee.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.common.core.domain.CurrentUser;
import com.vedeng.common.core.utils.numgenerator.BillNumGenerator;
import com.vedeng.common.core.utils.numgenerator.bean.BillGeneratorBean;
import com.vedeng.common.core.utils.numgenerator.enums.BillType;
import com.vedeng.erp.kingdee.batch.common.enums.StockOperateTypeConst;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseGoodsOutEnum;
import com.vedeng.erp.kingdee.batch.common.enums.WarehouseOutInSourceEnum;
import com.vedeng.erp.kingdee.batch.dto.*;
import com.vedeng.erp.kingdee.batch.repository.*;
import com.vedeng.erp.kingdee.service.KingDeeWarehouseGoodsOutService;
import com.vedeng.infrastructure.file.domain.Attachment;
import com.vedeng.infrastructure.file.mapper.AttachmentMapper;
import com.vedeng.infrastructure.kingdee.common.constant.KingDeeConstant;
import com.vedeng.infrastructure.kingdee.common.exception.KingDeeException;
import com.vedeng.infrastructure.oss.service.OssUtilsService;
import com.vedeng.infrastructure.oss.service.domain.UrlToPdfParam;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class KingDeeWarehouseGoodsOutServiceImpl implements KingDeeWarehouseGoodsOutService {

	private static final double SCALE = 0.6;

	private static final int LT_LENGTH = 10;
	
	private static final int AT_LENGTH = 16;
	
	private static final String RENDER_URL = "/api/render";

	private final Integer TRADER_TYPE_CUSTOMER = 1;

	public static final String str_6840 = "6840体外诊断试剂";

	@Value("${oss_http}")
    private String ossHttp;
	
	@Value("${html2Pdf.domain}")
    private String html2PdfDomain;

	//出库单验收报告pdf中图片地址
	@Value("${warehouseOutReport.imgpath}")
	private String imgPath;
	
	@Autowired
    private OssUtilsService ossUtilsService;
	
	@Autowired
	private AttachmentMapper attachmentMapper;

	@Autowired
	private BatchBuyorderDtoMapper batchBuyorderDtoMapper;

	@Autowired
	private BatchAfterSalesDtoMapper batchAfterSalesDtoMapper;

	@Autowired
	private BatchWarehouseGoodsOutInDtoMapper batchWarehouseGoodsOutInDtoMapper;

	@Autowired
	private BatchWarehouseGoodsOutInItemDtoMapper batchWarehouseGoodsOutInItemDtoMapper;

	@Autowired
	private BatchRExpressWarehouseGoodsOutInDtoMapper batchRExpressWarehouseGoodsOutInDtoMapper;

	@Autowired
	private BatchCoreSkuDtoMapper batchCoreSkuDtoMapper;

	@Autowired
	private BatchWmsOutputOrderDtoMapper batchWmsOutputOrderDtoMapper;

	@Autowired
	private BatchSaleorderDtoMapper batchSaleorderDtoMapper;

	@Autowired
	private BatchWmsOutOrderMapper batchWmsOutOrderMapper;

	@Autowired
	private BatchWmsInOutPersonMapper batchWmsInOutPersonMapper;


	/**
	 * 生成出库复核单
	 */
	@Override
	public void createWarehouseGoodsOutReport(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList) {
		log.info("出库单主信息：{}，出库单明细信息：{}",warehouse, JSON.toJSONString(warehouseGoodsOutInItemList));
		//生产销售出库、销售换货出库、采购退货出库、采购换货出库的复核报告
		if(CollectionUtils.isEmpty(warehouseGoodsOutInItemList)) {
			return;
		}
		//拼接HTML文档
		StringBuilder sb = new StringBuilder();
		Integer outInType = warehouse.getOutInType();
		boolean isOne = WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
				WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
				WarehouseGoodsOutEnum.PURCHASE_RETURN_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
				WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
				WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getErpCode().equals(outInType);

		boolean isTwo = WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
				WarehouseGoodsOutEnum.INVENTORY_LOSS_WAREHOUSE_OUT.getErpCode().equals(outInType) ||
				WarehouseGoodsOutEnum.UNIT_CONVERSION_OUT.getErpCode().equals(outInType) ;
		if (isOne) {
			templateOneOut(warehouse, warehouseGoodsOutInItemList, sb);
		} else if (isTwo) {
			templateTwoOut(warehouse, warehouseGoodsOutInItemList, sb);

		} else {
			templateOtherOut(warehouse, warehouseGoodsOutInItemList, sb);
		}

		try{
			htmlStrToPdfGetFile(sb.toString(),warehouse);
		}catch (Exception e){
			log.error("转换PDF失败...",e);
		}
	}

	private void templateOtherOut(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList, StringBuilder sb) {
		String preFix = "<html><head><title>出库报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>出库报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >单据类型：outInType</span><span style=\"margin-left:60px\">出库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:80px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:140px\">订货号</th><th rowspan=2 style=\"width:140px\">型号/规格</th><th rowspan=2 style=\"width:140px\">单位</th><th rowspan=2 style=\"width:160px\">出库数量</th><th rowspan=2 style=\"width:190px\">质量状态</th><th rowspan=2 style=\"width:160px\">处置方式</th></tr></thead><tbody id=\"effect\"style=\"text-align: center;\">";
		String companyName = "/";
		Integer outInType = warehouse.getOutInType();
		String serviceByErpCode = WarehouseGoodsOutEnum.getTypeByErpCode(outInType);
		String disposalMethod = WarehouseGoodsOutEnum.SCRAP_WAREHOUSE_OUT.getErpCode().equals(outInType) ? "交由第三方销毁" : "内部使用";
		log.info("出库单关联单号：{},客户/供应商名称：{}", warehouse.getRelateNo(),companyName);

		//替换：复核报告编号（outNo）、关联订单号(relateNo)、替换客户/供应商名称(outInCompany)
		sb.append(preFix.replace("outInNo", warehouse.getOutInNo())
				.replace("outInCompany",companyName)
				.replace("outInType",serviceByErpCode)
				.replace("relateNo", warehouse.getRelateNo()));

		//替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、拣货数量（outNum）、复核数量（checkNum）、复核结果（checkResult）
		//出库状态（goodsOutStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、效期（goodsPeriod）、随货单据（goodsDocuments）、其他（others）
		for(int i = 0; i < warehouseGoodsOutInItemList.size(); i++){
			BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
			//如果为采购入库
			BatchOutInDetailDto skuInfo = new BatchOutInDetailDto();
			Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
			querySkuInfo(skuInfo, skuId);
			String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>outNum</td><td>qualityStatus</td><td>disposalMethod</td></tr></tbody>";
			StringBuilder model = new StringBuilder();
			if (StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append(skuInfo.getModel());
			}
			if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append("/");
			}
			model.append(skuInfo.getSpec());
			sb.append(middleFix.replace("pageIndex",String.valueOf(i+1))
					.replace("goodsName",skuInfo.getSkuName())
					.replace("skuNo", skuInfo.getSkuNo())
					.replace("unit", skuInfo.getUnitName())
					.replace("goodsModel", model.toString())
					.replace("qualityStatus", "不合格")
					.replace("outNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
					.replace("disposalMethod",disposalMethod)
			);
		}

		BatchWmsOutOrderDto data = new BatchWmsOutOrderDto();
		getPickerAndReviewer(warehouse, data);

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >拣货员：sorter</span><span style=\"margin-left:240px\">出库员：checker</span><span style=\"margin-left:300px\">出库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">出库完成</span></div></div></div></body></html>";
		//替换：拣货员（sorter）、复核员（checker）、复核出库时间（checkTime）、复核图片
		sb.append(surFix.replace("sorter",data.getPicker())
				.replace("checker",data.getReviewer())
				.replace("checkTime",DateUtil.formatDate(warehouse.getOutInTime()))
		);
		log.info("create html in param:{}", sb);
	}

	private void templateTwoOut(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList, StringBuilder sb) {
		String preFix = "<html><head><title>出库报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>出库报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >单据类型：outInType</span><span style=\"margin-left:60px\">出库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:80px;height:30px\">序号</th><th rowspan=2 style=\"width:20%\">产品名称</th><th rowspan=2 style=\"width:140px\">订货号</th><th rowspan=2 style=\"width:140px\">型号/规格</th><th rowspan=2 style=\"width:140px\">单位</th><th rowspan=2 style=\"width:15%\">注册证编号</th><th rowspan=2 style=\"width:160px\">出库数量</th><th rowspan=2 style=\"width:120px\">出库状态</th></tr></thead><tbody id=\"effect\"style=\"text-align: center;\">";

		String companyName = "/";
		Integer outInType = warehouse.getOutInType();
		String serviceByErpCode = WarehouseGoodsOutEnum.getTypeByErpCode(outInType);
		if(WarehouseGoodsOutEnum.LEND_WAREHOUSE_OUT.getErpCode().equals(outInType)){
			// 样品出 外借
			BatchWmsOutputOrderDto outputOrder = batchWmsOutputOrderDtoMapper.findByOrderNo(warehouse.getRelateNo());
			if (Objects.nonNull(outputOrder)) {
				companyName = StrUtil.isNotEmpty(outputOrder.getBorrowTraderName())?outputOrder.getBorrowTraderName():"/";
			}
		}
		BatchWmsOutOrderDto data = new BatchWmsOutOrderDto();
		getPickerAndReviewer(warehouse, data);

		//替换：复核报告编号（outNo）、关联订单号(relateNo)、替换客户/供应商名称(outInCompany)
		sb.append(preFix.replace("outInNo", warehouse.getOutInNo())
				.replace("outInCompany",companyName)
				.replace("outInType",serviceByErpCode)
				.replace("relateNo", warehouse.getRelateNo()));

		//替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、拣货数量（outNum）、复核数量（checkNum）、复核结果（checkResult）
		//出库状态（goodsOutStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、效期（goodsPeriod）、随货单据（goodsDocuments）、其他（others）
		for(int i = 0; i < warehouseGoodsOutInItemList.size(); i++){
			BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
			//如果为采购入库
			BatchOutInDetailDto skuInfo = new BatchOutInDetailDto();
			Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
			querySkuInfo(skuInfo, skuId);
			String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>outNum</td><td>goodsOutStatus</td></tr>";
			StringBuilder model = new StringBuilder();
			if (StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append(skuInfo.getModel());
			}
			if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append("/");
			}
			model.append(skuInfo.getSpec());
			sb.append(middleFix.replace("pageIndex", String.valueOf(i + 1))
					.replace("goodsName", skuInfo.getSkuName())
					.replace("skuNo", skuInfo.getSkuNo())
					.replace("goodsModel", model.toString())
					.replace("unit", skuInfo.getUnitName())
					.replace("registerNo", skuInfo.getRegistrationNumber())
					.replace("outNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
					.replace("goodsOutStatus", "已出库")
			);
		}

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span >拣货员：sorter</span><span style=\"margin-left:600px\">出库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">出库完成</span></div></div></div></body></html>";
		//替换：拣货员（sorter）、复核员（checker）、复核出库时间（checkTime）、复核图片
		sb.append(surFix.replace("sorter", data.getPicker())
				.replace("checkTime", DateUtil.formatDate(warehouse.getOutInTime()))
		);
		log.info("create html in param:{}", sb);
	}

	private void templateOneOut(BatchWarehouseGoodsOutInDto warehouse, List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList, StringBuilder sb) {

		String preFix = "<html><head><title>出库复核报告</title><meta http-equiv=\"X-UA-Compatible\"content=\"IE=edge\"/><meta http-equiv=\"content-type\"content=\"text/html; charset=utf-8\"/></head><style>.ax_default{font-family:'Arial Normal','Arial',sans-serif;font-weight:400;font-style:normal;font-size:13px;letter-spacing:normal;color:#333333;vertical-align:none;text-align:center;line-height:normal;text-transform:none;margin-left:180px}.H2{font-family:'Arial Normal','Arial',sans-serif;font-weight:bold;font-style:normal;font-size:24px;text-align:left;margin-left:680px}.label{font-size:14px;text-align:left}.imgs{font-size:14px;text-align:right}body{margin:0px;background-image:none;position:relative;left:-176px;width:1371px;margin-left:auto;margin-right:auto;text-align:left}#base{position:absolute;z-index:0}td{text-align-last:center;border:solid 1px#000}th{border:solid 1px#000}table{border-spacing:0;border-collapse:collapse}</style><body><div id=\"base\"class=\"\"><!--Unnamed(矩形)--><div id=\"u0\"class=\"ax_default H2\"><div id=\"u0_text\"class=\"text \"><p><span>出库复核报告</span></p></div></div><!--Unnamed(矩形)--><div id=\"u1\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>单据类型：outInType</span><span style=\"margin-left:60px\">出库单编号：outInNo</span><span style=\"margin-left:60px\">关联订单号：relateNo</span><span style=\"margin-left:60px\">供应商/客户名称：outInCompany</span></p></div></div><!--Unnamed(表格)--><div id=\"u3\"class=\"ax_default\"><table><thead><tr><th rowspan=2 style=\"width:50px;height:30px\">序号</th><th rowspan=2 style=\"width:25%\">产品名称</th><th rowspan=2 style=\"width:80px\">订货号</th><th rowspan=2 style=\"width:100px\">型号/规格</th><th rowspan=2 style=\"width:60px\">单位</th><th rowspan=2 style=\"width:15%\">注册证编号</th><th rowspan=2 style=\"width:80px\">拣货数量</th><th rowspan=2 style=\"width:80px\">复核数量</th><th rowspan=2 style=\"width:80px\">复核结果</th><th colspan=6 style=\"width:80px;height:35px\">复核项目</th><th rowspan=2 style=\"width:80px\">出库状态</th><tr><th style=\"width:70px\">外观</th><th style=\"width:70px\">包装</th><th style=\"width:70px\">标签</th><th style=\"width:70px\">效期</th><th style=\"width:80px\">随货单据</th><th style=\"width:70px\">其他</th></tr></tr></thead><tbody id=\"effect\"style=\"text-align: center;\">";
		String companyName = "/";
		Integer outInType = warehouse.getOutInType();
		String serviceByErpCode = WarehouseGoodsOutEnum.getTypeByErpCode(outInType);
		if(WarehouseGoodsOutEnum.SALE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
			//销售出库
			BatchSaleorderDto saleorderByOrderNo = batchSaleorderDtoMapper.selectBySaleorderNo(warehouse.getRelateNo());
			companyName = StrUtil.isNotEmpty(saleorderByOrderNo.getTraderName())?saleorderByOrderNo.getTraderName():"/";
		}else if (WarehouseGoodsOutEnum.SALE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
			//销售换货出库
			BatchAfterSalesDto afterSalesByNo = batchAfterSalesDtoMapper.findByAfterSalesNo(warehouse.getRelateNo());
			BatchSaleorderDto saleorderByOrderNo = batchSaleorderDtoMapper.selectBySaleorderNo(afterSalesByNo.getOrderNo());
			companyName = StrUtil.isNotEmpty(saleorderByOrderNo.getTraderName())?saleorderByOrderNo.getTraderName():"/";
		}else if (WarehouseGoodsOutEnum.PURCHASE_RETURN_WAREHOUSE_OUT.getErpCode().equals(outInType) || WarehouseGoodsOutEnum.PURCHASE_EXCHANGE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
			//采购换货、采购退货
			BatchAfterSalesDto afterSalesByNo = batchAfterSalesDtoMapper.findByAfterSalesNo(warehouse.getRelateNo());
			BatchBuyorderDto buyOrderByOrderNo = batchBuyorderDtoMapper.selectByBuyorderNo(afterSalesByNo.getOrderNo());
			companyName = StrUtil.isNotEmpty(buyOrderByOrderNo.getTraderName())?buyOrderByOrderNo.getTraderName():"/";
		} else if (WarehouseGoodsOutEnum.SAMPLE_WAREHOUSE_OUT.getErpCode().equals(outInType)){
			BatchWmsOutputOrderDto outputOrder = batchWmsOutputOrderDtoMapper.findByOrderNo(warehouse.getRelateNo());
			if (Objects.nonNull(outputOrder)) {
				companyName = StrUtil.isNotEmpty(outputOrder.getBorrowTraderName())?outputOrder.getBorrowTraderName():"/";
			}
		}


		BatchWmsOutOrderDto data = new BatchWmsOutOrderDto();
		getPickerAndReviewer(warehouse, data);


		//替换：复核报告编号（outNo）、关联订单号(relateNo)、替换客户/供应商名称(outInCompany)
		sb.append(preFix.replace("outInNo", warehouse.getOutInNo())
				.replace("outInCompany",companyName)
				.replace("outInType",serviceByErpCode)
				.replace("relateNo", warehouse.getRelateNo()));

		//替换：验收报告明细 序号（pageIndex）、产品名称（goodsName）、订货号（skuNo）、规格型号（goodsModel）、注册证编号（registerNo）、拣货数量（outNum）、复核数量（checkNum）、复核结果（checkResult）
		//出库状态（goodsOutStatus）、外观（goodsFace）、包装（goodsPackage）、标签（goodsSign）、效期（goodsPeriod）、随货单据（goodsDocuments）、其他（others）
		for(int i = 0; i < warehouseGoodsOutInItemList.size(); i++){
			BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = warehouseGoodsOutInItemList.get(i);
			//如果为采购入库
			BatchOutInDetailDto skuInfo = new BatchOutInDetailDto();
			Integer skuId =  warehouseGoodsOutInItem.getGoodsId();
			querySkuInfo(skuInfo, skuId);
			String middleFix = "<tr><td>pageIndex</td><td>goodsName</td><td>skuNo</td><td>goodsModel</td><td>unit</td><td>registerNo</td><td>outNum</td><td>checkNum</td><td>checkResult</td><td>goodsFace</td><td>goodsPackage</td><td>goodsSign</td><td>goodsPeriod</td><td>goodsDocuments</td><td>others</td><td>goodsOutStatus</td></tr>";
			StringBuilder model = new StringBuilder();
			if (StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append(skuInfo.getModel());
			}
			if (StrUtil.isNotEmpty(skuInfo.getSpec())&&StrUtil.isNotEmpty(skuInfo.getModel())) {
				model.append("/");
			}
			model.append(skuInfo.getSpec());
			sb.append(middleFix.replace("pageIndex",String.valueOf(i+1))
					.replace("goodsName",skuInfo.getSkuName())
					.replace("skuNo", skuInfo.getSkuNo())
					.replace("goodsModel", model.toString())
					.replace("unit", skuInfo.getUnitName())
					.replace("registerNo", skuInfo.getRegistrationNumber())
					.replace("outNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
					.replace("checkNum", String.valueOf(warehouseGoodsOutInItem.getNum().abs()))
					.replace("checkResult","合格")
					.replace("goodsOutStatus","已出库")
					.replace("goodsFace","合格")
					.replace("goodsPackage","合格")
					.replace("goodsSign","合格")
					.replace("goodsPeriod","合格")
					.replace("goodsDocuments","合格")
					.replace("others","合格")
			);
		}

		String surFix = "</tbody></table></div><!--Unnamed(矩形)--><div id=\"u43\"class=\"ax_default label\"><div id=\"u0_text\"class=\"text \"><p><span>拣货员：sorter</span><span style=\"margin-left:240px\">复核员：checker</span><span style=\"margin-left:300px\">出库时间：checkTime</span></p></div></div><div class=\"ax_default imgs\"><div><span style=\"color: red;border: 3px solid red;font-size: 25px;padding: 10px\">出库完成</span></div></div></div></body></html>";
		//替换：拣货员（sorter）、复核员（checker）、复核出库时间（checkTime）、复核图片
		sb.append(surFix.replace("sorter", data.getPicker())
				.replace("checker", data.getReviewer())
				.replace("checkTime", DateUtil.formatDate(warehouse.getOutInTime())));
		log.info("create html in param:{}", sb);
	}
	private void getPickerAndReviewer(BatchWarehouseGoodsOutInDto warehouse, BatchWmsOutOrderDto data) {
		if (StrUtil.isNotEmpty(warehouse.getWmsNo())) {
			List<BatchWmsOutOrderDto> wmsOutOrderDtos = batchWmsOutOrderMapper.selectByWmsNo(warehouse.getWmsNo());
			if (CollUtil.isNotEmpty(wmsOutOrderDtos)) {
				data.setPicker(wmsOutOrderDtos.get(0).getPicker());
				data.setReviewer(wmsOutOrderDtos.get(0).getReviewer());
			}
		}
		if (StrUtil.isEmpty(data.getPicker())) {
			DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate("2023-11-28");
			DateTime outInDate = cn.hutool.core.date.DateUtil.parseDate(cn.hutool.core.date.DateUtil.formatDateTime(warehouse.getOutInTime()));
			String s = cn.hutool.core.date.DateUtil.formatDate(outInDate);
			int compare = cn.hutool.core.date.DateUtil.compare(dateTime, outInDate);
			if (compare<0) {

				List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByType(1003);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setPicker(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}
			} else {

				List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByTypeAndDate(3, s);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setPicker(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}
			}
		}

		if (StrUtil.isEmpty(data.getReviewer())) {
			DateTime dateTime = cn.hutool.core.date.DateUtil.parseDate("2023-11-28");
			DateTime outInDate = cn.hutool.core.date.DateUtil.parseDate(cn.hutool.core.date.DateUtil.formatDateTime(warehouse.getOutInTime()));
			String s = cn.hutool.core.date.DateUtil.formatDate(outInDate);
			int compare = cn.hutool.core.date.DateUtil.compare(dateTime, outInDate);
			if (compare<0) {
				List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByType(1004);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setReviewer(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}
			} else {
				List<BatchWmsInOutPersonDto> wmsInOutPersonDtos = batchWmsInOutPersonMapper.selectByTypeAndDate(4, s);
				if (CollUtil.isNotEmpty(wmsInOutPersonDtos)) {
					data.setReviewer(wmsInOutPersonDtos.get((int) (Math.random() * wmsInOutPersonDtos.size())).getName());
				}

			}

		}
	}

	@Retryable(value = NullPointerException.class, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 1.5))
	public void htmlStrToPdfGetFile(String html, BatchWarehouseGoodsOutInDto warehouseGoodsOutIn) {
		String html2PdfUrl = html2PdfDomain + RENDER_URL;
		UrlToPdfParam urlToPdfParam = new UrlToPdfParam();
		urlToPdfParam.setHtml(html);
		UrlToPdfParam.Pdf pdf = new UrlToPdfParam.Pdf();
		UrlToPdfParam.Pdf.PdfMargin margin = new UrlToPdfParam.Pdf.PdfMargin("1cm", "1cm", "1cm", "0cm");
		pdf.setMargin(margin);
		pdf.setScale(SCALE);
		urlToPdfParam.setPdf(pdf);
		// 上传出库单报告返回oss链接
		String ossUrl = ossUtilsService.migrateFile2Oss(html2PdfUrl, "pdf", "复核单" + warehouseGoodsOutIn.getOutInNo(), urlToPdfParam);
		if (StringUtil.isBlank(ossUrl)) {
			log.error("上传oss返回Url为空，可能出现IO异常，准备重试");
			throw new NullPointerException("返回的ossUrl为空");
		}
		log.info("生成出库复核报告,出库单号：{},返回地址ossUrl:{}", warehouseGoodsOutIn.getOutInNo(), ossUrl);

		Attachment attachment = new Attachment();
		attachment.setAttachmentType(KingDeeConstant.WAREHOUSER_ATTACHMET_TYPE);
		attachment.setAttachmentFunction(KingDeeConstant.WAREHOUSE_ATTACHMET_OUT_FUNCTION);
		attachment.setRelatedId(Integer.parseInt(String.valueOf(warehouseGoodsOutIn.getWarehouseGoodsOutInId())));
		String domainAndUri = ossUrl.split(ossHttp)[1];
		int domainIndex = domainAndUri.indexOf('/');
		String domain = domainAndUri.substring(0, domainIndex);
		String uri = domainAndUri.substring(domainIndex);
		attachment.setDomain(domain);
		attachment.setUri(uri);
		attachment.setName("复核单");
		attachment.setSuffix("pdf");
		attachment.setAddTime(System.currentTimeMillis());
		attachmentMapper.insertSelective(attachment);
	}


	/**
	 * 获取商品明细信息
	 */
	@Override
	public void querySkuInfo(BatchOutInDetailDto skuInfo, Integer skuId) {
		List<Integer> skuIdList = Arrays.asList(skuId);
		List<Map<String, Object>> maps = batchCoreSkuDtoMapper.skuTipList(skuIdList);
		if(CollUtil.isNotEmpty(maps)) {
			Map<String,Object> sku = maps.get(0);
			if(Objects.nonNull(sku.get("FIRST_ENGAGE_ID"))) {
				skuInfo.setFirstEngageId(Integer.valueOf(sku.get("FIRST_ENGAGE_ID").toString()));
				Integer new_standard_category_id = Integer.valueOf(sku.get("NEW_STANDARD_CATEGORY_ID").toString());
				Integer old_standard_category_id = Integer.valueOf(sku.get("OLD_STANDARD_CATEGORY_ID").toString());

				// 新国标
				if( null != new_standard_category_id && new_standard_category_id > 0){
					// 根据新国标分类id查询新国标分类
					String s = batchCoreSkuDtoMapper.selectStandardCategoryByCategoryId(new_standard_category_id);
					if (str_6840.equals(s)) {
						skuInfo.setIs6840(true);
					}
				}
				// 旧国标
				if(null != old_standard_category_id && old_standard_category_id > 0){
					String title= batchCoreSkuDtoMapper.selectByOldStandardCategory(old_standard_category_id);
					if (str_6840.equals(title)) {
						skuInfo.setIs6840(true);
					}
				}
			}
			skuInfo.setSkuId(Integer.valueOf(sku.get("SKU_ID").toString()));
			skuInfo.setSpuId(Integer.valueOf(sku.get("SPU_ID").toString()));
			skuInfo.setSkuNo(sku.get("SKU_NO").toString());
			skuInfo.setSkuName(sku.get("SHOW_NAME").toString());
			skuInfo.setBrandName(sku.get("BRAND_NAME").toString());
			if(Objects.nonNull(sku.get("SPEC"))){
				skuInfo.setSpec(sku.get("SPEC").toString());
			}
			skuInfo.setModel(sku.get("MODEL").toString());
			skuInfo.setRegistrationNumber(sku.get("REGISTRATION_NUMBER").toString());
			skuInfo.setUnitName(sku.get("UNIT_NAME").toString());
		}
	}

	/**
	 * 直发销售出库记录
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void insertSaleorderDirectOut(BatchUserDto session_user, BatchBuyorderDto buyorder, String id_sendN_sendedN_sumN, BatchExpressDto express, Map<Integer, Boolean> splitOrNotMap) {
		//组装主表数据
		BatchWarehouseGoodsOutInDto warehouseGoodsOutIn = new BatchWarehouseGoodsOutInDto();
		List<BatchWarehouseGoodsOutInItemDto> warehouseGoodsOutInItemList = new ArrayList<>();
		Map<Integer,Integer> getNumMap=new HashMap<>();
		if (StringUtils.isBlank(id_sendN_sendedN_sumN)) {
			log.info("id_sendN_sendedN_sumN为空,id_sendN_sendedN_sumN:{}",id_sendN_sendedN_sumN);
			return;
		}
		List<BatchSaleAndBuyOrderGoodsDto> saleOrderList=new ArrayList<>();
		String[] idAndSendNumAllArray = id_sendN_sendedN_sumN.split("_");
		List<Integer> buyOrderGoodsIds=new ArrayList<>();
		for (String idAndSendNumArray : idAndSendNumAllArray) {
			if (StringUtils.isBlank(idAndSendNumArray)){
				break;
			}
			String[] idAndSendNum = idAndSendNumArray.split("\\|");
			Integer buyOrderGoodsId = Integer.parseInt(idAndSendNum[0]);
			Integer sendNum = Integer.parseInt(idAndSendNum[1]);
			buyOrderGoodsIds.add(buyOrderGoodsId);
			getNumMap.put(buyOrderGoodsId,sendNum);
		}
		//根据采购订单的产品id匹配关联销售单及销售产品id
		saleOrderList = batchBuyorderDtoMapper.getSaleAndBuyOrderGoodsByIds(buyOrderGoodsIds);
		if (CollectionUtils.isEmpty(saleOrderList)){
			log.info("出库单未匹配到关联销售单及销售产品saleOrderList:{}",JSON.toJSONString(saleOrderList));
			return;
		}
		//获取采购单下的销售单列表
		List<BatchWarehouseGoodsOutInItemDto> itemList=new ArrayList<>();
		List<Integer> needSplitList = new ArrayList<>();
		//根据采购订单的产品id匹配关联销售单及销售产品id
		for (BatchSaleAndBuyOrderGoodsDto bo : saleOrderList) {
			BatchWarehouseGoodsOutInItemDto warehouseGoodsOutInItem = new BatchWarehouseGoodsOutInItemDto();
			//如果匹配到该销售订单，则封装itemlist
			warehouseGoodsOutInItem.setGoodsId(bo.getGoodsId());
			warehouseGoodsOutInItem.setNum(new BigDecimal(getNumMap.get(bo.getBuyorderGoodsId())));
			warehouseGoodsOutInItem.setRelatedId(bo.getSaleorderGoodsId());
			itemList.add(warehouseGoodsOutInItem);
			warehouseGoodsOutIn.setRelateNo(bo.getSaleorderNo());
			if (Boolean.TRUE.equals(splitOrNotMap.get(bo.getBuyorderGoodsId()))) {
				needSplitList.add(bo.getSaleorderGoodsId());
			}
		}
		//出入库类型
		warehouseGoodsOutIn.setOutInType(StockOperateTypeConst.WAREHOUSE_OUT);
		//保存主表信息
		convertToWarehouseGoodsOutIn(warehouseGoodsOutIn, session_user, batchWarehouseGoodsOutInDtoMapper::getSaleTraderNameBySaleOrder, express);
		log.info("出库单主表数据插入warehouseGoodsOutIn:{}",JSON.toJSONString(warehouseGoodsOutIn));
		batchWarehouseGoodsOutInDtoMapper.insertSelective(warehouseGoodsOutIn);
		BatchWarehouseGoodsOutInDto insertFlag = batchWarehouseGoodsOutInDtoMapper.queryInfoByNo(warehouseGoodsOutIn.getOutInNo(), warehouseGoodsOutIn.getOutInType());
		if (Objects.isNull(insertFlag)){
			log.error("出库单主表数据插入失败warehouseGoodsOutIn:{}",JSON.toJSONString(warehouseGoodsOutIn));
			throw new KingDeeException("出库单主表数据插入失败");
		}
		//保存item表信息
		for (BatchWarehouseGoodsOutInItemDto item : itemList) {

			if (needSplitList.contains(item.getRelatedId())) {
				//查询采购入库该商品的sn码列表
				List<String> orderNos = Arrays.asList(buyorder.getBuyorderNo());
				List<String> snList = new ArrayList<>();
				if (Boolean.TRUE.equals(splitOrNotMap.get(-1))) {
					snList = batchWarehouseGoodsOutInDtoMapper.getBarcodFactoryListByOrderNo(orderNos, item.getGoodsId());
				} else {
					snList = batchWarehouseGoodsOutInDtoMapper.getGiftBarcodFactoryListByOrderNo(orderNos, item.getGoodsId());
				}
				//查询售后已使用的全部sn码
				List<String> existSnList = batchWarehouseGoodsOutInDtoMapper.getExistBarcodFactoryList(buyorder.getBuyorderNo(), item.getGoodsId());

				List<String> usedBarcodFactoryList = batchWarehouseGoodsOutInDtoMapper.getUsedBarcodFactoryList(warehouseGoodsOutIn.getRelateNo(), item.getGoodsId());

				snList = snList.stream().filter(x -> !existSnList.contains(x) && !usedBarcodFactoryList.contains(x)).collect(Collectors.toList());
				log.info("出库单sn码列表snList:{},existSnList:{},usedBarcodFactoryList{}", JSON.toJSONString(snList), JSON.toJSONString(existSnList), JSON.toJSONString(usedBarcodFactoryList));
				Integer num = item.getNum().intValue();
				for (int i=0;i<num;i++){
					//生成SN码
					String sn = CollUtil.isNotEmpty(snList)&&snList.size()>=i?snList.get(i):"";
					BatchWarehouseGoodsOutInItemDto logDirect = new BatchWarehouseGoodsOutInItemDto();
					logDirect.setBarcodeFactory(sn);
					logDirect.setNum(new BigDecimal(KingDeeConstant.ONE));
					logDirect.setBatchNumber("");
					logDirect.setRelatedId(item.getRelatedId());
					logDirect.setGoodsId(item.getGoodsId());
					convertTowarehouseGoodsOutInItem(logDirect, warehouseGoodsOutIn, session_user, express);
					warehouseGoodsOutInItemList.add(logDirect);
					log.info("出库单从表数据插入warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
					batchWarehouseGoodsOutInItemDtoMapper.insertSelective(logDirect);
				}
			}else{
				//查询采购入库该商品的批次码列表
				List<String> batchList = new ArrayList<>();
				if (Boolean.TRUE.equals(splitOrNotMap.get(-1))) {
					batchList = batchWarehouseGoodsOutInDtoMapper.getBatchNumberListByOrderNo(buyorder.getBuyorderNo(), item.getGoodsId());
				} else {
					batchList = batchWarehouseGoodsOutInDtoMapper.getGiftBatchNumberListByOrderNo(buyorder.getBuyorderNo(), item.getGoodsId());
				}

				BatchWarehouseGoodsOutInItemDto logDirect = new BatchWarehouseGoodsOutInItemDto();
				logDirect.setNum(item.getNum());
				logDirect.setBatchNumber("");
				if (CollUtil.isNotEmpty(batchList)){
					//打乱批次列表顺序，随机取厂商批号
					Collections.shuffle(batchList);
					logDirect.setBatchNumber(batchList.get(0));
				}
				logDirect.setBarcodeFactory("");
				logDirect.setRelatedId(item.getRelatedId());
				logDirect.setGoodsId(item.getGoodsId());
				convertTowarehouseGoodsOutInItem(logDirect, warehouseGoodsOutIn, session_user, express);
				warehouseGoodsOutInItemList.add(logDirect);
				log.info("出库单从表数据插入warehouseGoodsOutInItem:{}",JSON.toJSONString(logDirect));
				batchWarehouseGoodsOutInItemDtoMapper.insertSelective(logDirect);
			}
		}

		BatchRExpressWarehouseGoodsOutInDto rExpressWarehouseGoodsOutInDto = new BatchRExpressWarehouseGoodsOutInDto();
		rExpressWarehouseGoodsOutInDto.setWarehouseGoodsOutInId(warehouseGoodsOutIn.getWarehouseGoodsOutInId().intValue());
		rExpressWarehouseGoodsOutInDto.setExpressId(express.getExpressId());
		CurrentUser currentUser = CurrentUser.getCurrentUser();
		rExpressWarehouseGoodsOutInDto.setCreator(currentUser.getId());
		rExpressWarehouseGoodsOutInDto.setCreatorName(currentUser.getUsername());
		rExpressWarehouseGoodsOutInDto.setUpdater(currentUser.getId());
		rExpressWarehouseGoodsOutInDto.setUpdaterName(currentUser.getUsername());
		batchRExpressWarehouseGoodsOutInDtoMapper.insertSelective(rExpressWarehouseGoodsOutInDto);
		try {
			//生成出库复核单
			createWarehouseGoodsOutReport(warehouseGoodsOutIn,warehouseGoodsOutInItemList);
		}catch (Exception e){
			log.error("生成出库复核单失败",e);
		}
	}

	private void convertTowarehouseGoodsOutInItem(BatchWarehouseGoodsOutInItemDto logDirect, BatchWarehouseGoodsOutInDto warehouseGoodsOutIn, BatchUserDto user, BatchExpressDto express) {
		String outInNo = warehouseGoodsOutIn.getOutInNo();
		logDirect.setOutInNo(outInNo);
		logDirect.setCompanyId(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getCompanyId());
		logDirect.setOperateType(warehouseGoodsOutIn.getOutInType());
		logDirect.setCheckStatusTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(express.getArrivalTime())));
		logDirect.setRecheckStatus(KingDeeConstant.ONE);
		Integer userId = user != null ? user.getUserId() : 2;
		logDirect.setCreator(userId);
		logDirect.setUpdater(userId);
		logDirect.setVedengBatchNumber(logDirect.getBatchNumber());
		logDirect.setLogType(1);
	}

	private void convertToWarehouseGoodsOutIn(BatchWarehouseGoodsOutInDto warehouseGoodsOutIn, BatchUserDto user, Function<String,String> companyFunction, BatchExpressDto express) {
		//生成erp出库单号
		BillGeneratorBean billGeneratorBean = new BillGeneratorBean(BillType.WAREHOUSE_GOODS_OUT_IN_CK);
		String outInNo = new BillNumGenerator().distribution(billGeneratorBean);
		// 销售类型，查询收发货方
		String outInCompany = companyFunction.apply(warehouseGoodsOutIn.getRelateNo());
		warehouseGoodsOutIn.setOutInNo(outInNo);
		warehouseGoodsOutIn.setOutInCompany(outInCompany);
		warehouseGoodsOutIn.setOutInTime(new Date(express.getArrivalTime()));
		warehouseGoodsOutIn.setSource(WarehouseOutInSourceEnum.DIRECT_DELIVERY.getSource());
		Integer userId = user != null ? user.getUserId() : 2;
		String userName = user != null ? user.getUsername() : "";
		warehouseGoodsOutIn.setCreator(userId);
		warehouseGoodsOutIn.setUpdater(userId);
		warehouseGoodsOutIn.setCreatorName(userName);
		warehouseGoodsOutIn.setUpdaterName(userName);
	}

}
