package com.vedeng.erp.saleorder.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.vedeng.authorization.model.User;
import com.vedeng.authorization.service.AuthService;
import com.vedeng.common.constant.BCConstants;
import com.vedeng.common.constant.ErpConst;
import com.vedeng.common.constant.OrderConstant;
import com.vedeng.common.core.constants.ErpConstant;
import com.vedeng.common.core.exception.ServiceException;
import com.vedeng.common.model.ResultInfo;
import com.vedeng.common.trace.data.TrackParamsData;
import com.vedeng.common.trace.enums.EventTrackingEnum;
import com.vedeng.common.trace.track.TrackStrategy;
import com.vedeng.common.trace.track.factory.TrackStrategyFactory;
import com.vedeng.erp.saleorder.dto.OrderTerminalDto;
import com.vedeng.erp.saleorder.dto.QuoteLinkOrderDto;
import com.vedeng.erp.saleorder.dto.QuoteToOrderDto;
import com.vedeng.erp.saleorder.dto.SaleorderInfoDto;
import com.vedeng.erp.saleorder.service.OrderTerminalApiService;
import com.vedeng.erp.saleorder.service.QuoteToOrderApiService;
import com.vedeng.erp.saleorder.strategy.OrderTerminalContext;
import com.vedeng.erp.saleorder.strategy.SaleOrderTerminalStrategy;
import com.vedeng.order.dao.BussinessChanceMapper;
import com.vedeng.order.dao.QuoteorderMapper;
import com.vedeng.order.dao.SaleorderMapper;
import com.vedeng.order.model.BussinessChance;
import com.vedeng.order.model.QuoteLinkBdLog;
import com.vedeng.order.model.Quoteorder;
import com.vedeng.order.model.Saleorder;
import com.vedeng.order.service.QuoteService;
import com.vedeng.order.service.SaleorderService;
import com.vedeng.system.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
public class QuoteToOrderApiServiceImpl implements QuoteToOrderApiService {

    @Resource
    private UserService userService;
    @Resource
    private SaleorderService saleorderService;
    @Resource
    private AuthService authService;
    @Autowired
    private OrderTerminalApiService orderTerminalApiService;
    @Autowired
    private SaleOrderTerminalStrategy saleOrderTerminalStrategy;
    @Autowired
    private TrackStrategyFactory trackStrategyFactory;
    @Resource
    private QuoteService quoteService;
    @Resource
    private SaleorderMapper saleorderMapper;
    @Resource
    private QuoteorderMapper quoteorderMapper;
    @Resource
    private BussinessChanceMapper bussinessChanceMapper;
    
    @Override
    public Integer convert(QuoteToOrderDto quoteToOrderDto) {
        log.info("CRM商机转订单，接口入参:{}", JSON.toJSONString(quoteToOrderDto));
        Integer quoteOrderId = quoteToOrderDto.getQuoteOrderId();
        Integer userId = quoteToOrderDto.getUserId();
        if (Objects.isNull(quoteOrderId) || Objects.isNull(userId)) {
            log.info("CRM商机转订单，接口入参异常:{}", JSON.toJSONString(quoteToOrderDto));
            return 0;
        }
        User user = userService.getUserById(userId);
        Saleorder saleorder = saleorderService.quoteorderToSaleorder(quoteOrderId, user);
        if (Objects.isNull(saleorder)) {
            log.info("CRM商机转订单，创建销售订单失败");
            return 0;
        }
        addTrackCreateSaleOrder(userId,saleorder.getSaleorderNo(),saleorder.getTraderId(),"报价转");

        //先取报价单的ID
        Quoteorder quoteorder =  quoteorderMapper.selectQuoteInfoById(quoteOrderId);
        Optional<OrderTerminalDto> quoteTerminalInfo = Optional.ofNullable(orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(quoteorder.getBussinessChanceId(), 1));


        // 报价转订单设置默认值
        Saleorder updateOrder = new Saleorder();
        updateOrder.setSaleorderId(saleorder.getSaleorderId());
        // 收货信息默认
        updateOrder.setIsPrintout(4);
        updateOrder.setFreightDescription(470);
        // 收票信息默认
        updateOrder.setInvoiceType(972);
        // 全部调整为自动数电发票 VDERP-15896
        updateOrder.setInvoiceMethod(ErpConst.FOUR);
        updateOrder.setIsNew(ErpConst.ONE);
        if (quoteTerminalInfo.isPresent()) {
            updateOrder.setTerminalTraderNature(quoteTerminalInfo.get().getTerminalTraderNature());
            updateOrder.setSalesAreaId(quoteTerminalInfo.get().getAreaId());//销售区域按商机里的终端来
            updateOrder.setSalesArea(quoteTerminalInfo.get().getProvinceName() + quoteTerminalInfo.get().getCityName() + quoteTerminalInfo.get().getAreaName());//销售区域按商机里的终端来
        }
        saleorderService.updateByPrimaryKeySelective(updateOrder);

        // VDERP-15595 报价转订单，同步关联终端信息 不需要报价单的，换成取商机的
        // Optional<OrderTerminalDto> quoteTerminalInfo = Optional.ofNullable(orderTerminalApiService.getTerminalInfoByBusinessIdAndBusinessType(saleorder.getQuoteorderId(), 2));
        if (quoteTerminalInfo.isPresent()) {
            OrderTerminalDto exist = quoteTerminalInfo.get();
            OrderTerminalDto orderTerminalDto = new OrderTerminalDto();
            orderTerminalDto.setBusinessId(saleorder.getSaleorderId());
            orderTerminalDto.setBusinessNo(saleorder.getSaleorderNo());
            orderTerminalDto.setBusinessType(ErpConst.ZERO);
            orderTerminalDto.setDwhTerminalId(exist.getDwhTerminalId());
            orderTerminalDto.setUnifiedSocialCreditIdentifier(exist.getUnifiedSocialCreditIdentifier());
            orderTerminalDto.setCityId(exist.getCityId());
            orderTerminalDto.setAreaId(exist.getAreaId());
            orderTerminalDto.setProvinceName(exist.getProvinceName());
            orderTerminalDto.setCityName(exist.getCityName());
            orderTerminalDto.setAreaName(exist.getAreaName());
            orderTerminalDto.setTerminalName(exist.getTerminalName());
            orderTerminalDto.setOrganizationCode(exist.getOrganizationCode());
            orderTerminalDto.setProvinceId(exist.getProvinceId());
            orderTerminalDto.setTerminalTraderNature(exist.getTerminalTraderNature());//5604
            orderTerminalDto.setNatureTypeName(exist.getNatureTypeName());//非公基层
            OrderTerminalContext orderTerminalContext = new OrderTerminalContext();
            orderTerminalContext.setOrderTerminalStrategy(saleOrderTerminalStrategy);
            orderTerminalContext.executeStrategy(orderTerminalDto);
        }
        return saleorder.getSaleorderId();
    }

    @Override
    public void link(QuoteLinkOrderDto quoteLinkOrderDto) {
        log.info("CRM商机转订单，关联订单，接口入参:{}", JSON.toJSONString(quoteLinkOrderDto));
        Integer businessChanceId = quoteLinkOrderDto.getBusinessChanceId();
        Integer quoteOrderId = quoteLinkOrderDto.getQuoteOrderId();
        String saleOrderNo = quoteLinkOrderDto.getSaleOrderNo();
        Integer userId = quoteLinkOrderDto.getUserId();
        if (Objects.isNull(businessChanceId) || Objects.isNull(quoteOrderId) ||
                StrUtil.isBlank(saleOrderNo) || Objects.isNull(userId)) {
            throw new ServiceException("参数异常");
        }
        Saleorder saleorder = saleorderService.getBySaleOrderNo(saleOrderNo);
        if (Objects.isNull(saleorder)) {
            throw new ServiceException("单号无法查询，请确认是否存在。");
        }
        Integer saleOrderId = saleorder.getSaleorderId();
        Integer status = saleorder.getStatus();
        Integer quoteorderId = saleorder.getQuoteorderId();
        if (ErpConstant.THREE.equals(status)) {
            throw new ServiceException("该订单已关闭，无法关联商机。");
        }
        if (Objects.nonNull(quoteorderId) && quoteorderId > 0) {
            Quoteorder validateQuote = quoteService.getQuoteOrderInfoById(quoteorderId);
            throw new ServiceException("该单号已被" + validateQuote.getBussinessChanceNo() + "商机关联，请确认。");
        }
        Quoteorder quoteorder = quoteService.getQuoteOrderInfoById(quoteOrderId);
        if(StrUtil.isNotBlank(quoteorder.getSaleorderId())){
            throw new ServiceException("商机已转订单，关联失败，请重新关联");
        }
        QuoteLinkBdLog queryLog = new QuoteLinkBdLog();
        queryLog.setBdOrderId(saleOrderId);
        queryLog.setIsEnable(ErpConst.ONE);
        QuoteLinkBdLog validateLog = quoteService.getQuoteLinkBdLogByInfo(queryLog);
        if (Objects.nonNull(validateLog)) {
            Quoteorder validateQuote = quoteService.getQuoteOrderInfoById(validateLog.getQuoteId());
            throw new ServiceException("该单号已被" + validateQuote.getBussinessChanceNo() + "商机关联，请确认。");
        }
        QuoteLinkBdLog quoteLinkBdLogByQuoteInfo = quoteService.getQuoteLinkBdLogByQuoteId(quoteOrderId);
        if (Objects.nonNull(quoteLinkBdLogByQuoteInfo)) {
            throw new ServiceException("该商机已关联订单，请确认。");
        }
        QuoteLinkBdLog log = new QuoteLinkBdLog();
        log.setQuoteId(quoteOrderId);
        log.setBdOrderId(saleOrderId);
        log.setCreator(userId);
        log.setAddTime(System.currentTimeMillis());
        log.setIsEnable(ErpConst.ONE);
        QuoteLinkBdLog insertData = quoteService.saveQuoteLinkBdLog(log);
        // 更新商机关联订单
        Saleorder updateSaleOrder = new Saleorder();
        updateSaleOrder.setSaleorderId(saleOrderId);
        updateSaleOrder.setQuoteorderId(quoteOrderId);
        saleorderMapper.updateByPrimaryKeySelective(updateSaleOrder);
        Quoteorder updateQuoteOrder = new Quoteorder();
        updateQuoteOrder.setQuoteorderId(quoteOrderId);
        updateQuoteOrder.setLinkBdStatus(OrderConstant.QUOTE_LINK_BD_PASS_STATUS);
        updateQuoteOrder.setFollowOrderStatus(ErpConst.ONE);
        updateQuoteOrder.setFollowOrderTime(System.currentTimeMillis());
        quoteorderMapper.updateQuote(updateQuoteOrder);

        BussinessChance businessChanceUpdate = new BussinessChance();
        businessChanceUpdate.setBussinessChanceId(businessChanceId);
        businessChanceUpdate.setIsLinkBd(ErpConst.ONE);
        businessChanceUpdate.setStatus(BCConstants.BC_ORDERING);
        bussinessChanceMapper.updateByPrimaryKeySelective(businessChanceUpdate);
    }

    @Override
    public SaleorderInfoDto getSaleOrder(QuoteLinkOrderDto quoteLinkOrderDto) {
        SaleorderInfoDto saleorderInfoDto = new SaleorderInfoDto();
        if (Objects.nonNull(quoteLinkOrderDto) && Objects.nonNull(quoteLinkOrderDto.getBusinessChanceId())) {
            Saleorder saleorder = saleorderMapper.getByBusinessChanceId(quoteLinkOrderDto.getBusinessChanceId());
            if (Objects.nonNull(saleorder)) {
                saleorderInfoDto.setSaleorderId(saleorder.getSaleorderId());
                saleorderInfoDto.setSaleorderNo(saleorder.getSaleorderNo());
                saleorderInfoDto.setTraderId(saleorder.getTraderId());
                saleorderInfoDto.setAddTime(saleorder.getAddTime());
                saleorderInfoDto.setTotalAmount(saleorder.getTotalAmount());
                saleorderInfoDto.setStatus(saleorder.getStatus());
            }
        }
        return saleorderInfoDto;
    }

    @Override
    public Integer isExistSaleOrder(QuoteLinkOrderDto quoteLinkOrderDto) {
        if (Objects.isNull(quoteLinkOrderDto) || Objects.isNull(quoteLinkOrderDto.getQuoteOrderId())) {
            throw new ServiceException("参数异常");
        }
        return saleorderMapper.isExistSaleOrder(quoteLinkOrderDto.getQuoteOrderId());
    }

    private void addTrackCreateSaleOrder(Integer userId,String orderNo,Integer traderId,String orderSource) {
        EventTrackingEnum eventTrackingEnum = EventTrackingEnum.SALE_CREATE_ORDER_BACK;
        try {
            User user = authService.getUserById(userId);
            TrackParamsData trackParamsData = new TrackParamsData();
            Map<String, Object> trackParams = new HashMap<>();
            trackParams.put("traderId",traderId);
            trackParams.put("track_user", user);
            trackParams.put("orderSource",orderSource);
            trackParams.put("orderNo",orderNo);
            TrackStrategy trackStrategy = trackStrategyFactory.getStrategyByType(eventTrackingEnum);
            trackParamsData.setEventTrackingEnum(eventTrackingEnum);
            trackParamsData.setTrackParams(trackParams);
            trackParamsData.setTrackResult(ResultInfo.success());
            trackStrategy.track(trackParamsData);
        }catch(Exception e) {
            log.error("埋点：{}，失败，不影响正常业务",eventTrackingEnum.getArchivedName(),e);
        }
    }
    
}
