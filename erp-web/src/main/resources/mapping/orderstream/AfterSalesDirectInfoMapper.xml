<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.orderstream.aftersales.dao.AfterSalesDirectInfoMapper" >
  <resultMap id="BaseResultMap" type="com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo" >

    <id column="AFTER_SALES_DIRECT_ID" property="afterSalesDirectId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_ID" property="afterSalesId" jdbcType="INTEGER" />
    <result column="AFTER_SALES_GOODS_ID" property="afterSalesGoodsId" jdbcType="INTEGER" />
    <result column="TYPE" property="type" jdbcType="BIT" />
    <result column="FACTORY_CODE" property="factoryCode" jdbcType="VARCHAR" />
    <result column="GOOD_CREATE_TIME" property="goodCreateTime" jdbcType="BIGINT" />
    <result column="GOOD_VAILD_TIME" property="goodVaildTime" jdbcType="BIGINT" />
    <result column="NUM" property="num" jdbcType="INTEGER" />
    <result column="ADD_TIME" property="addTime" jdbcType="BIGINT" />
    <result column="MODE_TIME" property="modeTime" jdbcType="BIGINT" />
    <result column="CREATOR" property="creator" jdbcType="INTEGER" />
    <result column="UPDATER" property="updater" jdbcType="INTEGER" />
    <result column="IS_DELETE" property="isDelete" jdbcType="BIT" />
  </resultMap>
  <sql id="Base_Column_List" >

    AFTER_SALES_DIRECT_ID, AFTER_SALES_ID, AFTER_SALES_GOODS_ID, `TYPE`, FACTORY_CODE, 
    GOOD_CREATE_TIME, GOOD_VAILD_TIME, NUM, ADD_TIME, MODE_TIME, CREATOR, UPDATER, IS_DELETE
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Integer" >

    select 
    <include refid="Base_Column_List" />
    from T_AFTER_SALES_DIRECT_INFO
    where AFTER_SALES_DIRECT_ID = #{afterSalesDirectId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer" >

    delete from T_AFTER_SALES_DIRECT_INFO
    where AFTER_SALES_DIRECT_ID = #{afterSalesDirectId,jdbcType=INTEGER}
  </delete>
  <insert id="insert" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo" >

    <selectKey resultType="java.lang.Integer" keyProperty="afterSalesDirectId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_AFTER_SALES_DIRECT_INFO (AFTER_SALES_ID, AFTER_SALES_GOODS_ID, 
      `TYPE`, FACTORY_CODE, GOOD_CREATE_TIME, 
      GOOD_VAILD_TIME, NUM, ADD_TIME, 
      MODE_TIME, CREATOR, UPDATER, 
      IS_DELETE)
    values (#{afterSalesId,jdbcType=INTEGER}, #{afterSalesGoodsId,jdbcType=INTEGER}, 
      #{type,jdbcType=BIT}, #{factoryCode,jdbcType=VARCHAR}, #{goodCreateTime,jdbcType=BIGINT}, 
      #{goodVaildTime,jdbcType=BIGINT}, #{num,jdbcType=INTEGER}, #{addTime,jdbcType=BIGINT}, 
      #{modeTime,jdbcType=BIGINT}, #{creator,jdbcType=INTEGER}, #{updater,jdbcType=INTEGER}, 
      #{isDelete,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo" >

    <selectKey resultType="java.lang.Integer" keyProperty="afterSalesDirectId" order="AFTER" >
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into T_AFTER_SALES_DIRECT_INFO
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID,
      </if>
      <if test="afterSalesGoodsId != null" >
        AFTER_SALES_GOODS_ID,
      </if>
      <if test="type != null" >
        `TYPE`,
      </if>
      <if test="factoryCode != null" >
        FACTORY_CODE,
      </if>
      <if test="goodCreateTime != null" >
        GOOD_CREATE_TIME,
      </if>
      <if test="goodVaildTime != null" >
        GOOD_VAILD_TIME,
      </if>
      <if test="num != null" >
        NUM,
      </if>
      <if test="addTime != null" >
        ADD_TIME,
      </if>
      <if test="modeTime != null" >
        MODE_TIME,
      </if>
      <if test="creator != null" >
        CREATOR,
      </if>
      <if test="updater != null" >
        UPDATER,
      </if>
      <if test="isDelete != null" >
        IS_DELETE,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="afterSalesId != null" >
        #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null" >
        #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        #{type,jdbcType=BIT},
      </if>
      <if test="factoryCode != null" >
        #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null" >
        #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null" >
        #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="num != null" >
        #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modeTime != null" >
        #{modeTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        #{isDelete,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo" >

    update T_AFTER_SALES_DIRECT_INFO
    <set >
      <if test="afterSalesId != null" >
        AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      </if>
      <if test="afterSalesGoodsId != null" >
        AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      </if>
      <if test="type != null" >
        `TYPE` = #{type,jdbcType=BIT},
      </if>
      <if test="factoryCode != null" >
        FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      </if>
      <if test="goodCreateTime != null" >
        GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      </if>
      <if test="goodVaildTime != null" >
        GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      </if>
      <if test="num != null" >
        NUM = #{num,jdbcType=INTEGER},
      </if>
      <if test="addTime != null" >
        ADD_TIME = #{addTime,jdbcType=BIGINT},
      </if>
      <if test="modeTime != null" >
        MODE_TIME = #{modeTime,jdbcType=BIGINT},
      </if>
      <if test="creator != null" >
        CREATOR = #{creator,jdbcType=INTEGER},
      </if>
      <if test="updater != null" >
        UPDATER = #{updater,jdbcType=INTEGER},
      </if>
      <if test="isDelete != null" >
        IS_DELETE = #{isDelete,jdbcType=BIT},
      </if>
    </set>
    where AFTER_SALES_DIRECT_ID = #{afterSalesDirectId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.vedeng.orderstream.aftersales.model.AfterSalesDirectInfo" >

    update T_AFTER_SALES_DIRECT_INFO
    set AFTER_SALES_ID = #{afterSalesId,jdbcType=INTEGER},
      AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER},
      `TYPE` = #{type,jdbcType=BIT},
      FACTORY_CODE = #{factoryCode,jdbcType=VARCHAR},
      GOOD_CREATE_TIME = #{goodCreateTime,jdbcType=BIGINT},
      GOOD_VAILD_TIME = #{goodVaildTime,jdbcType=BIGINT},
      NUM = #{num,jdbcType=INTEGER},
      ADD_TIME = #{addTime,jdbcType=BIGINT},
      MODE_TIME = #{modeTime,jdbcType=BIGINT},
      CREATOR = #{creator,jdbcType=INTEGER},
      UPDATER = #{updater,jdbcType=INTEGER},
      IS_DELETE = #{isDelete,jdbcType=BIT}
    where AFTER_SALES_DIRECT_ID = #{afterSalesDirectId,jdbcType=INTEGER}
  </update>
  <select id="selectStockList" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List"/>
    FROM T_AFTER_SALES_DIRECT_INFO
    WHERE
    AFTER_SALES_GOODS_ID = #{afterSalesGoodsId,jdbcType=INTEGER}
    AND TYPE = #{type,jdbcType=BIT}
    AND IS_DELETE = 0
  </select>
</mapper>