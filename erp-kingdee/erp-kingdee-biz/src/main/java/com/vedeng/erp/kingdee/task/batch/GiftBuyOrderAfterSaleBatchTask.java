package com.vedeng.erp.kingdee.task.batch;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.erp.kingdee.batch.job.BuyOrderGiftAfterSaleBatchJob;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import org.springframework.batch.core.Job;
import org.springframework.batch.core.JobParameters;
import org.springframework.batch.core.launch.JobLauncher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * GiftBuyOrderAfterSaleBatchTask
 * <AUTHOR>
 */
@JobHandler(value = "GiftBuyOrderAfterSaleBatchTask")
@Component
public class GiftBuyOrderAfterSaleBatchTask extends AbstractJobHandler {

    @Autowired
    private BuyOrderGiftAfterSaleBatchJob batchJob;

    @Autowired
    private JobLauncher jobLauncher;

    /**
     * {"beginTime":"2022-11-01 00:00:00",
     * "endTime":"2022-12-01 00:00:00",
     * "timestamp":"1666687179395"}
     */
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================赠品出库batch开始====================");
        JobParameters jobParameters = new TaskBatchHandle().buildJobParameters(param);
        Job job = batchJob.giftBuyOrderAfterSale();
        jobLauncher.run(job, jobParameters);
        XxlJobLogger.log("==================赠品出库batch结束====================");
        return SUCCESS;
    }


}

