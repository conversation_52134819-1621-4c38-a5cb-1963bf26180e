package com.vedeng.goods.model;

import java.io.Serializable;

public class GoodsAttachment implements Serializable{
    private Integer goodsAttachmentId;

    private Integer goodsId;

    private Integer attachmentType;

    private String domain;

    private String uri;

    private String alt;

    private Integer sort;

    private Integer isDefault;

    private Integer status;

    /**
     * oss资源标识
     */
    private String ossResourceId;
    // end by fran<PERSON> for[3865 供应商资质中，增加销售人授权书，销售人信息]  at 2018-06-21

    private String originalFilepath;

    private Integer synSuccess;

    private Long costTime;

    public Integer getGoodsAttachmentId() {
        return goodsAttachmentId;
    }

    public void setGoodsAttachmentId(Integer goodsAttachmentId) {
        this.goodsAttachmentId = goodsAttachmentId;
    }

    public Integer getGoodsId() {
        return goodsId;
    }

    public void setGoodsId(Integer goodsId) {
        this.goodsId = goodsId;
    }

    public Integer getAttachmentType() {
        return attachmentType;
    }

    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain == null ? null : domain.trim();
    }

    public String getUri() {
        return uri;
    }

    public void setUri(String uri) {
        this.uri = uri == null ? null : uri.trim();
    }

    public String getAlt() {
        return alt;
    }

    public void setAlt(String alt) {
        this.alt = alt == null ? null : alt.trim();
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getOssResourceId() {
        return ossResourceId;
    }

    public void setOssResourceId(String ossResourceId) {
        this.ossResourceId = ossResourceId;
    }

    public String getOriginalFilepath() {
        return originalFilepath;
    }

    public void setOriginalFilepath(String originalFilepath) {
        this.originalFilepath = originalFilepath;
    }

    public Integer getSynSuccess() {
        return synSuccess;
    }

    public void setSynSuccess(Integer synSuccess) {
        this.synSuccess = synSuccess;
    }

    public Long getCostTime() {
        return costTime;
    }

    public void setCostTime(Long costTime) {
        this.costTime = costTime;
    }
}