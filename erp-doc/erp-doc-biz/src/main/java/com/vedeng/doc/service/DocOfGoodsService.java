package com.vedeng.doc.service;

import com.vedeng.doc.dto.DocOfGoodsQueryDetailDto;
import com.vedeng.doc.query.DocOfGoodsDetailQuery;

import java.util.List;

/**
 * 资料库商品资料_内部接口
 *
 * <AUTHOR>
 */
public interface DocOfGoodsService  {

    /**
     * 查询资料库商品资料信息
     *
     * @param docOfGoodsDetailQuery 参数
     * @return DocOfGoodsQueryDetailDto
     */
    List<DocOfGoodsQueryDetailDto> queryDocOfGoodsDetail(DocOfGoodsDetailQuery docOfGoodsDetailQuery);

}
