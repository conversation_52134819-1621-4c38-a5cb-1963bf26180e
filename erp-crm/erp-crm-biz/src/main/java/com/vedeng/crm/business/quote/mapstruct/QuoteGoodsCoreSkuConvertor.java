package com.vedeng.crm.business.quote.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.crm.business.quote.domain.dto.CrmCoreSkuInfoDto;
import com.vedeng.crm.business.quote.domain.dto.QuoteGoodsInsertRequestDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface QuoteGoodsCoreSkuConvertor extends BaseMapStruct<CrmCoreSkuInfoDto, QuoteGoodsInsertRequestDto> {

}
