package com.wms.service.other;

import com.alibaba.fastjson.JSON;
import com.vedeng.aftersales.service.AfterSalesService;
import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.User;
import com.vedeng.common.constant.SpecialDeliveryEnum;
import com.vedeng.common.constant.goods.GoodsConstants;
import com.vedeng.common.util.DateUtil;
import com.vedeng.common.util.StringUtil;
import com.vedeng.order.dao.BuyorderGoodsMapper;
import com.vedeng.order.dao.BuyorderMapper;
import com.vedeng.order.model.Buyorder;
import com.wms.constant.WMSContant;
import com.wms.constant.WmsInterfaceOrderType;
import com.wms.dto.PutPurchaseOrderDto;
import com.wms.dto.PutPurchaseOrderGoodsDto;
import com.wms.dto.WmsResponse;
import com.wms.service.DoPutService;
import com.wms.service.WMSInterfaceFactory;
import com.wms.service.WmsInterface;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 下发接口实现类
 *
 * <AUTHOR>
 * @date 2022/1/10 16:32
 **/
@Service("doPutService")
@Slf4j
public class DoPutServiceImpl implements DoPutService {

    @Resource
    private BuyorderMapper buyorderMapper;

    @Resource
    private BuyorderGoodsMapper buyorderGoodsMapper;

    @Resource
    private UserMapper userMapper;

    @Autowired
    private AfterSalesService afterSalesService;

    @Autowired
    private WMSInterfaceFactory wmsInterfaceFactory;



    @Override
    public WmsResponse doPutPurchaseOrderMethod(@NonNull Integer buyOrderId,String notes) throws Exception{
        Buyorder buyorder = buyorderMapper.selectByPrimaryKey(buyOrderId);
        if (buyorder == null) {
            return null;
        }

        //若采购单是直发就不下发采购单
        if(buyorder.getDeliveryDirect() == 1){
            log.info("采购单是直发不下发WMS,buyOrderNo:{}",buyorder.getBuyorderNo());
            return null;
        }

        List<PutPurchaseOrderGoodsDto> details = this.getDetails(buyorder);

        //如果detail为空就不下发
        if(CollectionUtils.isEmpty(details)){
            log.info("若采购订单中只包含特殊商品，则整个订单不下传WMS, buyOrderNo : {}",buyorder.getBuyorderNo());
            return null;
        }

        PutPurchaseOrderDto putPurchaseOrderDto = new PutPurchaseOrderDto();

        putPurchaseOrderDto.setPoType(WmsInterfaceOrderType.INPUT_PURCHASE);
        putPurchaseOrderDto.setDocNo(buyorder.getBuyorderNo());
        putPurchaseOrderDto.setPoCreationTime(DateUtil.convertString(buyorder.getAddTime(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime1(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));
        putPurchaseOrderDto.setExpectedArriveTime2(DateUtil.convertString(System.currentTimeMillis(),"yyyy-MM-dd HH:mm:ss"));

        putPurchaseOrderDto.setSupplierId(buyorder.getTraderId());
        putPurchaseOrderDto.setSupplierName(buyorder.getTraderName());

        User user = userMapper.getUserInfoByUserId(buyorder.getCreator());
        putPurchaseOrderDto.setPoReferenceA(StringUtil.isEmpty(user.getOrgName()) ? "/" : user.getOrgName());
        putPurchaseOrderDto.setPoReferenceB(user.getUsername());
        if (StringUtils.isEmpty(notes)) {
            putPurchaseOrderDto.setNotes(buyorder.getLogisticsComments());
        } else {
            putPurchaseOrderDto.setNotes(notes);
        }


        //入库单的详情
        putPurchaseOrderDto.setDetails(details);

        //wms采购入库单
        WmsInterface wmsInterface = wmsInterfaceFactory.getWmsInterface(WMSContant.PUT_PURCHASE_ORDER);

        //XxlJobLogger.log("采购单:"+buyorder.getBuyorderNo()+",开始下发WMS,参数:" + JSON.toJSONString(putPurchaseOrderDto));
        log.info("历史采购单:"+buyorder.getBuyorderNo()+",开始下发WMS,参数:" + JSON.toJSONString(putPurchaseOrderDto));
        return wmsInterface.request(putPurchaseOrderDto);
    }

    /**
     * 获取具体的商品信息，计算数量
     * @param buyorder 采购单对象
     * @return
     */
    private List<PutPurchaseOrderGoodsDto> getDetails(Buyorder buyorder) {

        List<PutPurchaseOrderGoodsDto> details = new ArrayList<>();

        buyorderGoodsMapper.getBuyorderGoodsVoListByBuyorderIdNoSpecial(buyorder.getBuyorderId())
                .stream()
                .forEach(buyOrderGood -> {

                    if (GoodsConstants.VIRTUAL_GOODS.contains(buyOrderGood.getGoodsId())){
                        return;
                    }

                    PutPurchaseOrderGoodsDto detailItem = new PutPurchaseOrderGoodsDto();
                    detailItem.setSku(buyOrderGood.getSku());
                    detailItem.setDedi04(buyOrderGood.getBuyorderGoodsId() + StringUtils.EMPTY);

                    //采购单数量 - 采购单到货数量 - 已完结售后数量
                    detailItem.setOrderedQty(buyOrderGood.getNum()
                            - buyOrderGood.getArrivalNum()
                            - afterSalesService.getFinishAfterSaleNum(buyOrderGood.getBuyorderGoodsId()));

                    if (SpecialDeliveryEnum.NEED_SPECIAL_DELIVERY.getCode().equals(
                            buyorderGoodsMapper.getSpecialDeliveryByRelatedId(buyOrderGood.getBuyorderGoodsId()))){
                        detailItem.setLotAtt07(buyorder.getBuyorderNo());
                    }

                    if(detailItem.getOrderedQty() > 0){
                        details.add(detailItem);
                    }

                });

        return details;

    }






}
