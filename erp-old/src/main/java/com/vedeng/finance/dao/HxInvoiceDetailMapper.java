package com.vedeng.finance.dao;


import com.vedeng.finance.model.HxInvoiceDetail;

import javax.inject.Named;

/**
 * <AUTHOR>
 * @date 2020/08/06
 **/
@Named("hxInvoiceDetailMapper")
public interface HxInvoiceDetailMapper {

    int deleteByPrimaryKey(Integer hxInvoiceDetailId);

    int insert(HxInvoiceDetail record);

    int insertSelective(HxInvoiceDetail record);

    HxInvoiceDetail selectByPrimaryKey(Integer hxInvoiceDetailId);

    int updateByPrimaryKeySelective(HxInvoiceDetail record);

    int updateByPrimaryKey(HxInvoiceDetail record);
}
