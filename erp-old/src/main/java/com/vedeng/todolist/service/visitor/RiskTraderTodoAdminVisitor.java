package com.vedeng.todolist.service.visitor;

import com.vedeng.authorization.dao.UserMapper;
import com.vedeng.authorization.model.Organization;
import com.vedeng.common.util.DateUtil;
import com.vedeng.todolist.constant.TodoListBuzSceneEnum;
import com.vedeng.todolist.dao.TodoListMapper;
import com.vedeng.todolist.dto.RiskTraderTodoDto;
import com.vedeng.todolist.dto.SupplyChainAdminTodoDtoBuild;
import com.vedeng.todolist.model.TodoList;
import com.wms.service.context.ThreadLocalContext;
import org.apache.commons.collections.map.HashedMap;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 管理者页面-> 供应商的待办项
 */
@Service
public class RiskTraderTodoAdminVisitor implements TodoVisitor<SupplyChainAdminTodoDtoBuild>{

    @Resource
    private TodoListMapper todoListMapper;

    @Resource
    private UserMapper userMapper;

    private long firstDayTimestamp;

    private List<Integer> userIdList;

    private Map<Integer,List<Integer>> userIdByOrgId = new HashedMap();

    @Override
    public void visitor(SupplyChainAdminTodoDtoBuild todoDtoBuild) {

        firstDayTimestamp = DateUtil.getFirstDayofMonth();

        List<Organization> orgaList =  ThreadLocalContext.get("orgaList");

        Set<Integer> userIdSet = new HashSet<>();

        orgaList.stream().forEach(organization -> {
            List<Integer> userIds = userMapper.getUserIdListByOrgId(organization.getOrgId());
            userIdByOrgId.put(organization.getOrgId(),userIds);
            userIdSet.addAll(userIds);
        });

        userIdList = new ArrayList<>(userIdSet);

        Map<Organization, RiskTraderTodoDto> traderTodoMap = new LinkedHashMap<>();

        //获取所有未处理代办
        List<TodoList> unHandlerTraderTodoList = todoListMapper.getUnHandledRiskCheckTraderSupplyTodoListByUserList(
                                                                    TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA.getBuzSceneId(),
                                                                    userIdList);

        //获取当月处理的待办
        List<TodoList> handlerTraderTodoList = todoListMapper.getHandledRiskCheckTraderSupplyTodoListByUserList(TodoListBuzSceneEnum.RISK_CHECK_TRADER_SUPPLY_DATA.getBuzSceneId(),
                                                              userIdList,
                                                              firstDayTimestamp);

        orgaList.stream().forEach(organization -> {


            List<Integer> orgaUserIdList = userIdByOrgId.get(organization.getOrgId());

            //查出属于当前部门的代办
            List<TodoList> unHandlerTodoList = unHandlerTraderTodoList.stream()
                                                                      .filter(traderTodo -> traderTodoBelongToOrga(traderTodo,orgaUserIdList))
                                                                      .collect(Collectors.toList());

            //查出当月已处理的待办
            List<TodoList> handlerTodoList = handlerTraderTodoList.stream()
                                                                   .filter(traderTodo -> traderTodoBelongToOrga(traderTodo,orgaUserIdList))
                                                                   .collect(Collectors.toList());

            RiskTraderTodoDto riskTraderTodoDto = handlerTodoList(organization.getOrgId(),unHandlerTodoList,handlerTodoList);

            if(riskTraderTodoDto.getTraderSupplyTodoListCount() == 0){
                return;
            }

            traderTodoMap.put(organization,riskTraderTodoDto);

        });

        todoDtoBuild.get().setTraderTodoMap(traderTodoMap);
    }


    /**
     * 判断一个trader代办 是否属于某个部门
     * @param traderTodo
     * @param orgaUserIdList
     * @return
     */
    private boolean traderTodoBelongToOrga(TodoList traderTodo,List<Integer> orgaUserIdList){
        return orgaUserIdList.contains(traderTodo.getAssignmentAssistantId()) || orgaUserIdList.contains(traderTodo.getAssignmentManagerId());
    }

    /**
     * 处理一个部门的代办
     * @param unHandlerTodoList
     * @param handlerTodoList
     * @return
     */
    private RiskTraderTodoDto handlerTodoList(Integer orgaId,List<TodoList> unHandlerTodoList,List<TodoList> handlerTodoList) {

        RiskTraderTodoDto traderTodoDto  = new RiskTraderTodoDto();

        Set<Long> noRepeatSet = unHandlerTodoList.parallelStream().map(value -> Long.valueOf(value.getBuzId())).collect(Collectors.toSet());
        traderTodoDto.setTraderSupplyTodoListCount(noRepeatSet.size());

        Integer riskCheckSkuTodoListGroupByOrder = unHandlerTodoList.parallelStream().map(TodoList::getBuzExtra).filter(StringUtils::isNotBlank).collect(Collectors.toSet()).size();
        traderTodoDto.setTraderSupplyTodoListCountGroupByOrder(riskCheckSkuTodoListGroupByOrder);

        //本月已处理代办
        Integer handlerCurrentMonth = handlerTodoList.parallelStream().map(TodoList::getBuzId).collect(Collectors.toSet()).size();
        traderTodoDto.setHandlerCurrentMonth(handlerCurrentMonth);

        //当月新增的已处理的代办
        int handlerCurrentMonthAnd =  handlerTodoList.stream().filter(skuTodo -> skuTodo.getAddTime() > firstDayTimestamp).map(TodoList::getBuzId).collect(Collectors.toSet()).size();

        //当月新增的未处理的代办
        Integer unhandlerCurrentMonth = unHandlerTodoList.stream().filter(skuTodo -> skuTodo.getAddTime() > firstDayTimestamp).map(TodoList::getBuzId).collect(Collectors.toSet()).size();

        traderTodoDto.setAddCurrentMonth(unhandlerCurrentMonth + handlerCurrentMonthAnd);

        NumberFormat numberFormat = NumberFormat.getInstance();
        numberFormat.setMaximumFractionDigits(0);

        int allTodoListCurrentMonth = traderTodoDto.getTraderSupplyTodoListCount() + handlerCurrentMonth;
        traderTodoDto.setPercent(allTodoListCurrentMonth > 0? numberFormat.format((float)handlerCurrentMonth/(float)(allTodoListCurrentMonth) * 100) : "0");

        String userIdList = userIdByOrgId.get(orgaId).stream().map(String::valueOf).collect(Collectors.joining(","));
        traderTodoDto.setUserIdList(userIdList);

        return traderTodoDto;
    }
}
