<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c"%>
<c:set var="title" value="供应商信息(财务专用)修改" scope="application" />
<%@ include file="../../common/common.jsp"%>
<script type="text/javascript" src="<%= basePath %>static/js/jquery/ajaxfileupload.js?rnd=${resourceVersionKey}"></script>
<script>
    function deliveryOrderReturnSubmit () {
        checkLogin();
        var traderType = $("#traderType").val();
        if (traderType == '') {
            layer.alert("请选择供应商类别",{ icon: 2 });
            return;
        }

        $.ajax({
            url:'./saveSupplier.do',
            data:$('#traderFinanciaForm').serialize(),
            type:"POST",
            dataType : "json",
            async: false,
            success:function(data)
            {
                if(data.code===0){
                    window.parent.location.reload();
                }else{
                    layer.alert(data.message);
                }
            },
            error:function(data){
                if(data.status ===1001){
                    layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                }else{
                    layer.alert(data.message);
                }
            }
        });

    }


</script>
<div class="addElement">
    <div class="add-main adddepart">
        <form id="traderFinanciaForm" method="post" enctype="multipart/form-data">
            <table  class="layui-table" style=" width:100%">
                <tr>
                    <td>供应商ID：${trader.traderId}</td>
                    <td>供应商名称：${trader.traderName}</td>
                </tr>
                <tr>
                    <td>归属采购：${trader.username}</td>
                    <td>
                        <span style="color: red">*</span>
                        供应商类别:
                        <select class="input-middle" name="traderType" id="traderType">
                             <option value="">选择</option>
                            <option value="1" <c:if test="${trader.traderType eq 1}">selected="selected"</c:if>>生产厂家</option>
                            <option value="2" <c:if test="${trader.traderType eq 2}">selected="selected"</c:if>>经销商</option>
                         </select>
                    </td>
                </tr>
            </table>
            <div class="add-tijiao tcenter">
                <input type="hidden" value="${trader.traderSupplierFinanceId}" name="traderSupplierFinanceId"
                       id="traderSupplierFinanceId">
                <button type="button" class="bt-bg-style bg-deep-green" id="outIn_attach_return_submit"
                        onclick="deliveryOrderReturnSubmit()">提交
                </button>
                <button class="dele" type="button" id="close-layer">取消</button>
            </div>
        </form>
    </div>
</div>
<%@ include file="../../common/footer.jsp"%>