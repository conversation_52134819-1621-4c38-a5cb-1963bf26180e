package com.vedeng.order.service;

import com.vedeng.aftersales.model.vo.AfterSalesVo;
import com.vedeng.customerbillperiod.exception.CustomerBillPeriodException;
import com.vedeng.finance.model.CapitalBill;
import com.vedeng.finance.model.Invoice;
import com.vedeng.order.model.Saleorder;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * <p>
 * 订单账期服务
 */
public interface OrderAccountPeriodService {

    /**
     * 关闭订单处理订单的账期业务
     *
     * @param saleorder
     * @throws CustomerBillPeriodException
     */
    void dealCloseOrderCustomerBillPeriodOfOrder(Saleorder saleorder) throws CustomerBillPeriodException;

    /**
     * 订单生成账期逾期编码
     *
     * @param saleOrderId
     * @param managementType
     * @param relatedId
     * @throws CustomerBillPeriodException
     */
    void dealCustomerBillPeriodManagement(Integer saleOrderId, Integer managementType, Integer relatedId, BigDecimal amount) throws CustomerBillPeriodException;

    /**
     * 添加流水处理订单冻结和占用的账期金额释放
     * (已弃用)
     *
     * @param capitalBill
     * @throws CustomerBillPeriodException
     */
    void dealOrderCustomerBillPeriodWithAddCapitalBill(CapitalBill capitalBill) throws CustomerBillPeriodException;

    /**
     * 添加流水处理订单冻结和占用的账期金额释放
     *
     * @param orderId
     * @param creditRepaymentAmount
     * @param capitalBillId
     * @throws CustomerBillPeriodException
     */
    void unfreezingBillPeriodByCreditRepayment(Integer orderId, BigDecimal creditRepaymentAmount, Integer capitalBillId) throws CustomerBillPeriodException;


    /**
     * 撤销订单生效，解冻订单占用的账期金额
     * @param saleOrderId 订单id
     */
    void unfreezingBillPeriodByCancelValidSaleOrder(Integer saleOrderId);

    /**
     * 售后完结处理账期逾期编码 (节点暂时更换)
     *
     * @param afterSalesId
     * @throws CustomerBillPeriodException
     */
    void dealAccountPeriodOverdueCodeByConfirmAfterSales(Integer afterSalesId) throws CustomerBillPeriodException;

    /**
     * 售后退票处理账期逾期编码
     *
     * @param invoice
     * @throws CustomerBillPeriodException
     */
    void dealAccountPeriodOverdueCodeByRefundInvoice(Invoice invoice) throws CustomerBillPeriodException;

    /**
     * 执行退款运算处理订单冻结和占用的账期金额释放
     *
     * @param afterSales
     * @throws CustomerBillPeriodException
     */
    void dealOrderCustomerBillPeriodWithRefundOperation(AfterSalesVo afterSales) throws CustomerBillPeriodException;

    /**
     * 订单生效时，发起冻结客户账期金额的操作
     * @param saleorder 订单
     * @return 操作结果
     */
    Boolean freezingBillPeriodAmountWhenOrderValid(Saleorder saleorder);


    /**
     * 订单占用相应的账期金额
     * @param saleorder 订单
     */
    void occupyBillPeriodAmount(Saleorder saleorder);

    /**
     * 是否部分付款
     * @param paymentType
     * @return
     */
    boolean isPartialPayment(Integer paymentType);
}
