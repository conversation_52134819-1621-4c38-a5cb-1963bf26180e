<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8" %>
<%@ taglib uri="http://java.sun.com/jsp/jstl/core" prefix="c" %>
<%@ include file="../../../common/common.jsp" %>
<script src="${pageContext.request.contextPath}/static/js/finance/invoice/hx_invoice/hx_invoice_common.js?rnd=${resourceVersionKey}"
        charset="utf-8"></script>
<div class="layui-tab-item">
    <div class="searchfunc ">
        <form method="post" id="search" action="/finance/invoice/hx_invoice.do?id=2">
            <ul>
                <li>
                    <label class="keyword">关键词</label>
                    <input type="text" class="input-small" name="keyword" id="keyword"
                           value="${invoiceSearch.keyword}"/>
                </li>

                <li>
                    <label class="infor_name">票种</label>
                    <select class="input-middle selector" name="invoiceTaxRate">
                        <option value="0"
                                <c:if test="${invoiceSearch.invoiceTaxRate == 0}">selected</c:if> >全部
                        </option>
                        <option value="971"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 13 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            13%增值税普通发票
                        </option>
                        <option value="972"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 13 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                            13%增值税专用发票
                        </option>
                        <option value="681"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 16 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            16%增值税普通发票
                        </option>
                        <option value="682"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 16 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                            16%增值税专用发票
                        </option>
                        <option value="683"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 6 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            6%增值税普通发票
                        </option>
                        <option value="684"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 6 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                            6%增值税专用发票
                        </option>
                        <option value="685"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 3 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            3%增值税普通发票
                        </option>
                        <option value="686"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 3 && invoiceSearch.invoiceCategory == '1'}">selected="selected"</c:if>>
                            3%增值税专用发票
                        </option>
                        <option value="687"
                                <c:if test="${invoiceSearch.invoiceTaxRate eq 0 && invoiceSearch.invoiceCategory == '0'}">selected="selected"</c:if>>
                            0%增值税普通发票
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">红蓝字</label>
                    <select class="input-smaller96 selector" name="colorType">
                        <option value="">全部</option>
                        <option value="1" <c:if test="${invoiceSearch.colorType eq 1}">selected="selected"</c:if>>蓝字有效
                        </option>
                        <option value="2" <c:if test="${invoiceSearch.colorType eq 2}">selected="selected"</c:if>>红字有效
                        </option>
                        <option value="3" <c:if test="${invoiceSearch.colorType eq 3}">selected="selected"</c:if>>蓝字作废
                        </option>
                        <option value="4" <c:if test="${invoiceSearch.colorType eq 4}">selected="selected"</c:if>>失控
                        </option>
                        <option value="5" <c:if test="${invoiceSearch.colorType eq 5}">selected="selected"</c:if>>异常
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name" style="overflow: visible">开票时间</label>
                    <input class="Wdate f_left input-smaller96 m0" type="text" placeholder="请选择日期" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',maxDate:'#F{$dp.$D(\'endAddDateStr\')}'})"
                           name="startAddDateStr" id="startAddDateStr"
                           value='<date:date value ="${startAddDateStr}" format="yyyy-MM-dd"/>'>
                    <div class="gang">-</div>
                    <input class="Wdate f_left input-smaller96" type="text" placeholder="请选择日期" autocomplete="off"
                           onFocus="WdatePicker({dateFmt:'yyyy-MM-dd',minDate:'#F{$dp.$D(\'startAddDateStr\')}'})"
                           name="endAddDateStr" id="endAddDateStr"
                           value='<date:date value ="${endAddDateStr}" format="yyyy-MM-dd"/>'>
                </li>

                <li>
                    <label class="infor_name">发票总额</label>
                    <input class="f_left input-smaller96 mr5" type="text" name="invoiceAmountFrom"
                           id="invoiceAmountFrom" value='${invoiceSearch.invoiceAmountFrom}'
                           onchange="checkValue(this)">
                    <div class="gang">-</div>
                    <input class="f_left input-smaller96" type="text" name="invoiceAmountTo" id="invoiceAmountTo"
                           value='${invoiceSearch.invoiceAmountTo}' onchange="checkValue(this)">
                </li>

                <li class="fold-li" style="display: list-item">
                    <label class="infor_name">认证状态</label>
                    <select class="input-smaller96 selector" name="authStatus">
                        <option value="">全部</option>
                        <option value="0" <c:if test="${invoiceSearch.authStatus eq 0}">selected="selected"</c:if>>
                            未认证
                        </option>
                        <option value="1" <c:if test="${invoiceSearch.authStatus eq 1}">selected="selected"</c:if>>
                            认证通过
                        </option>
                        <option value="2" <c:if test="${invoiceSearch.authStatus eq 2}">selected="selected"</c:if>>
                            认证失败
                        </option>
                    </select>
                </li>

                <li>
                    <label class="infor_name">认证方式</label>
                    <select class="input-middle selector" name="authMode" id="authMode">
                        <option value="">全部</option>
                        <option value="0" <c:if test="${invoiceSearch.authMode eq 0}">selected</c:if>>线下认证</option>
                        <option value="1" <c:if test="${invoiceSearch.authMode eq 1}">selected</c:if>>接口认证</option>
                    </select>
                </li>
            </ul>
            <div class="tcenter">
                <span class="confSearch bt-small bt-bg-style bg-light-blue" onclick="search();"
                      id="searchSpan">查询</span>
                <span class="bt-small bg-light-blue bt-bg-style mr20" onclick="searchReset();">重置</span>
            </div>
        </form>
        <br>
        <div class="content">
            <div class="inputfloat" style='margin:0px 0 15px 0;'>
                <input type="checkbox" id="check_All" name="check_All" onclick="checkallInvoice();"
                       value="checkbox_one">
                <span>全选</span>
                <span class="bt-small  btn-small bt-bg-style bg-light-blue" onclick="exportCostList()">导出</span>
                <button type="button" class="bt-bg-style bg-light-blue bt-small pop-new-data"
                        layerparams='{"width":"500px","height":"200px","title":"提交接口认证","link":"/finance/invoice/batchAuthInvoiceInit.do?isHxInvoiceFlag=1"}'>
                    提交接口认证
                </button>
            </div>
            <div class="">
                <div style="width:1752px;" class='superdiv'>
                    <table class="table table-bordered table-striped table-condensed table-centered">
                        <thead>
                        <tr>
                            <th class="wid3">选择</th>
                            <th class="wid3">序号</th>
                            <th class="wid6">发票号码</th>
                            <th class="wid6">发票代码</th>
                            <th class="wid8">开票方名称</th>
                            <th class="wid6">发票总额</th>
                            <th class="wid6">不含税金额</th>
                            <th class="wid6">税额</th>
                            <th class="wid7">票种</th>
                            <th class="wid10">开票时间</th>
                            <th class="wid5">红蓝字</th>
                            <th class="wid6">认证状态</th>
                            <th class="wid9">认证失败原因</th>
                            <th class="wid6">认证方式</th>
                            <th class="wid10">认证时间</th>
                            <th class="wid10">操作</th>
                        </tr>
                        </thead>
                        <tbody>
                        <c:set var="lzyxAmount" value="0"></c:set><!-- 蓝字有效总额 -->
                        <c:set var="lzyxTaxAmount" value="0"></c:set><!-- 蓝字有效税额 -->
                        <c:set var="lzyxTaxFreeAmount" value="0"></c:set><!-- 蓝字有效不含税额 -->

                        <c:set var="lzzfAmount" value="0"></c:set><!-- 蓝字作废总额 -->
                        <c:set var="lzzfTaxAmount" value="0"></c:set><!-- 蓝字作废税额 -->
                        <c:set var="lzzfTaxFreeAmount" value="0"></c:set><!-- 蓝字作废不含税额 -->

                        <c:set var="hzyxAmount" value="0"></c:set><!-- 红字有效总额 -->
                        <c:set var="hzyxTaxAmount" value="0"></c:set><!-- 红字有效税额 -->
                        <c:set var="hzyxTaxFreeAmount" value="0"></c:set><!-- 红字有效不含税额 -->


                        <c:set var="pageNum" value="0"></c:set><!-- 当前页记录数 -->

                        <c:forEach var="item" items="${list}" varStatus="status">
                            <c:set var="pageNum" value="${pageNum + 1}"></c:set>
                            <tr>
                                <td>
                                    <c:if test="${item.canIdentification}">
                                        <input type="checkbox" name="ck" onclick="checkOneHxInvoice()"
                                               value="${item.hxInvoiceId}" invoiceId="${item.hxInvoiceId}">
                                    </c:if>
                                </td>
                                <td>${status.count}</td>
                                <td>${item.invoiceNum}</td>
                                <td>${item.invoiceCode}</td>
                                <td>${item.salerName}</td>
                                <td>
                                    <fmt:formatNumber value="${item.amount}" type="numer" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <fmt:formatNumber value="${item.invoiceAmount}" type="numer" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <fmt:formatNumber value="${item.taxAmount}" type="numer" pattern="0.00"
                                                      maxFractionDigits="2"/>
                                </td>
                                <td>
                                    <c:if test="${item.taxRate != null && item.invoiceCategory != null}">
                                        ${item.taxRate}%增值税<c:if test="${item.invoiceCategory == '0'}">普通</c:if><c:if
                                            test="${item.invoiceCategory == '1'}">专用</c:if>发票
                                    </c:if>
                                </td>
                                <td>
                                    <c:if test="${item.createTime != null}">
                                        <jsp:useBean id="createTimeDateValue" class="java.util.Date"/>
                                        <jsp:setProperty name="createTimeDateValue" property="time"
                                                         value="${item.createTime}"/>
                                        <fmt:formatDate value="${createTimeDateValue}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </c:if>
                                </td>
                                <td>
                                    <c:if test="${item.colorType eq 1}">
                                        蓝字有效
                                        <c:set var="lzyxAmount" value="${lzyxAmount + item.amount}"></c:set>
                                        <c:set var="lzyxTaxAmount" value="${lzyxTaxAmount + item.taxAmount}"></c:set>
                                        <c:set var="lzyxTaxFreeAmount" value="${lzyxTaxFreeAmount + item.invoiceAmount}"></c:set>
                                    </c:if>
                                    <c:if test="${item.colorType eq 2}">
                                        红字有效
                                        <c:set var="hzyxAmount" value="${hzyxAmount + item.amount}"></c:set>
                                        <c:set var="hzyxTaxAmount" value="${hzyxTaxAmount + item.taxAmount}"></c:set>
                                        <c:set var="hzyxTaxFreeAmount" value="${hzyxTaxFreeAmount + item.invoiceAmount}"></c:set>
                                    </c:if>
                                    <c:if test="${item.colorType eq 3}">
                                        蓝字作废
                                        <c:set var="lzzfAmount" value="${lzzfAmount + item.amount}"></c:set>
                                        <c:set var="lzzfTaxAmount" value="${lzzfTaxAmount + item.taxAmount}"></c:set>
                                        <c:set var="lzzfTaxFreeAmount" value="${lzzfTaxFreeAmount + item.invoiceAmount}"></c:set>
                                    </c:if>
                                    <c:if test="${item.colorType eq 4}">失控</c:if>
                                    <c:if test="${item.colorType eq 5}">异常</c:if>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${item.authStatus != null and item.authStatus == 1}">已认证</c:when>
                                        <c:when test="${item.authStatus != null and item.authStatus == 0}">未认证</c:when>
                                        <c:otherwise>未认证</c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    <c:if test="${item.authStatus eq 2}">
                                        ${item.authFailReason}
                                    </c:if>
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${item.authStatus == 1 and item.authMode != null and item.authMode eq 0}">
                                            线下认证
                                        </c:when>
                                        <c:when test="${item.authStatus == 1 and item.authMode != null  and item.authMode eq 1}">
                                            接口认证
                                        </c:when>
                                    </c:choose>
                                </td>
                                <td>
                                    <c:if test="${item.authStatus != null and item.authStatus == 1 and item.authTime != null}">
                                        <jsp:useBean id="authTimeDateValue" class="java.util.Date"/>
                                        <jsp:setProperty name="authTimeDateValue" property="time"
                                                         value="${item.authTime}"/>
                                        <fmt:formatDate value="${authTimeDateValue}" pattern="yyyy-MM-dd HH:mm:ss"/>
                                    </c:if>
                                </td>
                                <td>
                                    <a onclick="viewAndDownloadHxInvoiceHref('${item.invoiceCode}','${item.invoiceNum}','${item.hxInvoiceId}',0)">
                                        <font style="color: #438DEF"
                                              id="viewInvocieFont${item.hxInvoiceId}">查看发票</font>
                                    </a>
                                    <a onclick="saveHxInvoiceStatus(${item.hxInvoiceId},4)"
                                       href="javascript:void(0)">
                                        <font style="color: #438DEF">标为待认领票</font>
                                    </a>
                                </td>
                            </tr>
                            <tr style="display: none" id="imgTr${item.hxInvoiceId}">
                                <td colspan="16" height="350px" id="invoiceImg${item.hxInvoiceId}">
                                </td>
                            </tr>
                            <input id="imgFlag${item.hxInvoiceId}" value="0" type="hidden">
                            <input id="imgSrc${item.hxInvoiceId}" value="${item.attachment}" type="hidden">
                        </c:forEach>
                        </tbody>
                    </table>

                    <c:if test="${empty list}">
                        <!-- 查询无结果弹出 -->
                        <div class="noresult">查询无结果！请尝试使用其他搜索条件。</div>
                    </c:if>
                </div>
            </div>
            <div>
                <tags:page page="${page}"/>
                <div class="total">
                    <div class="clear"></div>
                    <div class="fixtablelastline" style="height: initial;">
                        【本页统计 条目：${pageNum}
                        总金额：<fmt:formatNumber type="number" value="${lzyxAmount - lzzfAmount - hzyxAmount}" pattern="0.00" maxFractionDigits="2" />；
                        不含税总金额：<fmt:formatNumber type="number" value="${lzyxTaxFreeAmount - lzzfTaxFreeAmount - hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                        税额总金额：<fmt:formatNumber type="number" value="${lzyxTaxAmount - lzzfTaxAmount - hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；】
                        <br/>
                        【蓝字有效金额总计： <fmt:formatNumber type="number" value="${lzyxAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字有效不含税总金额： <fmt:formatNumber type="number" value="${lzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字有效税额总金额： <fmt:formatNumber type="number" value="${lzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                        蓝字作废金额总计：<fmt:formatNumber type="number" value="${lzzfAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字作废不含税总金额：<fmt:formatNumber type="number" value="${lzzfTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字作废税额总金额：<fmt:formatNumber type="number" value="${lzzfTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                        红字有效金额总计：<fmt:formatNumber type="number" value="${hzyxAmount}" pattern="0.00" maxFractionDigits="2" />
                        红字有效不含税总金额：<fmt:formatNumber type="number" value="${hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />
                        红字有效税额总金额：<fmt:formatNumber type="number" value="${hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" /> 】
                    </div>
                    <div class="clear"></div>
                    <div class="fixtablelastline" style="height: initial;">
                        【全部结果  蓝字有效发票：<fmt:formatNumber type="number" value="${invoice.lzyxNum == null ? 0 : invoice.lzyxNum}" pattern="0" maxFractionDigits="0" />；
                        蓝字作废：<fmt:formatNumber type="number" value="${invoice.lzzfNum == null ? 0 : invoice.lzzfNum}" pattern="0" maxFractionDigits="0" />；
                        红字有效：<fmt:formatNumber type="number" value="${invoice.hzyxNum == null ? 0 : invoice.hzyxNum}" pattern="0" maxFractionDigits="0" /> 】
                    </div>
                    <div class="clear"></div>
                    <div class="fixtablelastline" style="height: initial;">
                        【全部结果 条目：${invoice.invoiceCount}
                        总金额：<fmt:formatNumber type="number" value="${invoice.amountCount == null ? 0 : invoice.amountCount}" pattern="0.00" maxFractionDigits="2" /> ；
                        不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxFreeAmount - invoice.lzzfTaxFreeAmount - invoice.hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                        税额总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxAmount - invoice.lzzfTaxAmount - invoice.hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；】
                        <br/>
                        【蓝字有效金额总计：<fmt:formatNumber type="number" value="${invoice.lzyxAmount == null ? 0 : invoice.lzyxAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字有效不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxFreeAmount == null ? 0 : invoice.lzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字有效税额总金额：<fmt:formatNumber type="number" value="${invoice.lzyxTaxAmount == null ? 0 : invoice.lzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                        蓝字作废金额总计：<fmt:formatNumber type="number" value="${invoice.lzzfAmount == null ? 0 : invoice.lzzfAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字作废不含税总金额：<fmt:formatNumber type="number" value="${invoice.lzzfTaxFreeAmount == null ? 0 : invoice.lzzfTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />；
                        蓝字作废税额总金额：<fmt:formatNumber type="number" value="${invoice.lzzfTaxAmount == null ? 0 : invoice.lzzfTaxAmount}" pattern="0.00" maxFractionDigits="2" />；

                        红字有效金额总计：<fmt:formatNumber type="number" value="${invoice.hzyxAmount == null ? 0 : invoice.hzyxAmount}" pattern="0.00" maxFractionDigits="2" />
                        红字有效不含税总金额：<fmt:formatNumber type="number" value="${invoice.hzyxTaxFreeAmount == null ? 0 : invoice.hzyxTaxFreeAmount}" pattern="0.00" maxFractionDigits="2" />
                        红字有效税额总金额：<fmt:formatNumber type="number" value="${invoice.hzyxTaxAmount == null ? 0 : invoice.hzyxTaxAmount}" pattern="0.00" maxFractionDigits="2" />】
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    /**
     * 重置搜索框
     */
    function searchReset() {
        $('#keyword').val('');
        $('#startAddDateStr').val('');
        $('#endAddDateStr').val('');
        $('#invoiceAmountFrom').val('');
        $('#invoiceAmountTo').val('');
        $('.selector').each(function (i, j) {
            var options = $(j).find("option");
            options.attr("selected", false);
            options.first().attr("selected", true);
        })
    }

    /**
     * 校验数字框输入合法性
     */
    function checkValue(obj) {
        if ($(obj).val() != undefined && $(obj).val() != '') {
            var reg = /^\d+(?=\.{0,1}\d+$|$)/;
            if (!reg.test($(obj).val())) {
                layer.alert('请输入数字!');
                $(obj).val('');
            }
        }
    }

    /**
     * @description 全选
     */
    function checkallInvoice() {
        var checked = $("#check_All").is(":checked");
        var checkboxs = document.getElementsByName("ck");
        for (var i = 0; i < checkboxs.length; i++) {
            if (checked) {
                checkboxs[i].checked = true;
            } else {
                checkboxs[i].checked = false;
            }
        }
    }

    /**
     * @description 单选
     */
    function checkOneHxInvoice() {
        var checked = true;
        var checkboxs = document.getElementsByName("ck");
        for (var i = 0; i < checkboxs.length; i++) {
            if (!(checkboxs[i].checked)) {
                checked = false;
                break;
            }
        }
        $("#check_All").prop("checked", checked);
    }

    /**
     * exportCostListSpan
     */
    function exportCostList() {
        parent.exportCostListInfo();
    }

    /**
     * 更新航信发票的状态
     * @param hxInvoiceId 航信发票的ID
     * @param invoiceStatus 航信发票的状态
     */
    function saveHxInvoiceStatus(hxInvoiceId, invoiceStatus) {
        layer.confirm('确定将该发票标记为待认领发票吗？', {title: '标记发票状态'}, function (index) {
            $.ajax({
                url: '/supplyChain/invoice/saveHxInvoiceStatus.do',
                data: {
                    hxInvoiceId: hxInvoiceId,
                    invoiceStatus: invoiceStatus
                },
                type: 'post',
                dataType: 'json',
                success: function (res) {
                    if (res.code == 0) {
                        layer.alert('标记成功', function () {
                            window.location.reload();
                        });
                    } else {
                        layer.alert('操作失败');
                    }
                },
                error:function(data){
                    if(data.status ==1001){
                        layer.alert("您没有操作权限，申请开通权限请联系研发部Aadi。\n请求的接口地址是：" + ((this.url && this.url.startsWith("http"))==true?(new URL(this.url).pathname):this.url));
                    }
                }
            })
        }, function () {
            layer.close(index);
        })
    }
</script>

<%@ include file="../../../common/footer.jsp" %>