package com.vedeng.erp.finance.service;

import com.vedeng.erp.finance.domain.dto.BankReceiptRecordDto;

import java.util.List;

public interface BankReceiptRecordService {
    BankReceiptRecordDto getById(Long bankReceiptAliasId);

    List<BankReceiptRecordDto> findByAccountNoAndAccountType(String accountNo, Integer accountType);

    void tryCreateBankReceiptRecord(BankReceiptRecordDto bankReceiptRecordDto);
}
