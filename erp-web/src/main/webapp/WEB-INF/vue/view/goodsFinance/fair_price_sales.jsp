<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>

<div id="app" style="display: none">
    <el-card class="box-card" shadow="never">
        <div slot="header" class="clearfix">
            <span></span>
        </div>

        <template>
            <el-table :data="fairValueDto.saleOrderDataDtoList" border :header-cell-style="{textAlign: 'center'}" style="width: 100%">
                <el-table-column align="center" label="销售日期">
                    <template slot-scope="scope">
                        <span>{{parseTime(scope.row.saleDate)}}</span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="订单号">
                    <template slot-scope="scope">
                        <span>{{ scope.row.saleOrderNo }}</span>
                    </template>
                </el-table-column>

                <el-table-column align="center" label="销售单价">
                    <template slot-scope="scope">
                        <span>{{formatNum(scope.row.price)}}</span>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </el-card>
    <el-row style="text-align: center; margin-bottom: 20px;margin-top: 20px">
        <el-button type="primary" size="small" @click="closeLayer()" style="background-color: #d58c0f">关闭</el-button>
    </el-row>
</div>

<script type="text/javascript">
    const fairValueId = '${fairValueId}';
    const sendThis = (_this) => {
        vm = _this;
    };

    new Vue({
        el: '#app',
        data() {
            return {
                fairValueDto:{
                    saleOrderDataDtoList: []
                }
            };
        },
        mounted(){
            loadingApp();
        },
        created() {
            this.initData();
        },
        methods: {
            closeThis(){
                parent.layer.close(index);
            },
            formatNum(data) {
                if (data == null) {
                    data = 0;
                }
                return data.toFixed(2);
            },
            closeLayer(){
                var index = parent.layer.getFrameIndex(window.name);
                parent.layer.close(index);
            },
            async initData() {
                if (fairValueId != null && fairValueId != "") {
                    await viewData(fairValueId).then(res => {
                        this.fairValueDto = res.data.data;
                    });
                }
            }
        }
    });

    /**
     * 查询历史公允价信息
     * @param data
     */
    function viewData(data){
        return axios({
            url: '/goods/finance/getFairPriceInfoDto.do',
            method: 'post',
            params:{fairValueId:data}
        })
    }
</script>