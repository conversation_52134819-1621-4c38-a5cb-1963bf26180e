package com.vedeng.erp.business.mapstruct;

import com.vedeng.common.mybatis.domain.BaseMapStruct;
import com.vedeng.erp.business.domain.entity.BusinessChanceSupportRecord;
import com.vedeng.erp.business.dto.BusinessSupportReqDto;
import org.mapstruct.Mapper;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BusinessSupportRecordConvertor extends BaseMapStruct<BusinessChanceSupportRecord, BusinessSupportReqDto> {
}