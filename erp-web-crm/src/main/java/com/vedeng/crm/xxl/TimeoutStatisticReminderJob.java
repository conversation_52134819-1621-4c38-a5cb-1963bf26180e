package com.vedeng.crm.xxl;

import com.vedeng.common.trace.xxl.AbstractJobHandler;
import com.vedeng.crm.task.service.TaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 待办任务超时统计提醒
 */
@Component
@JobHandler(value="TimeoutStatisticReminderJob")
@Slf4j
public class TimeoutStatisticReminderJob extends AbstractJobHandler {

    @Autowired
    private TaskService taskService;
    
    @Override
    public ReturnT<String> doExecute(String param) throws Exception {
        XxlJobLogger.log("==================待办任务超时统计提醒job开始====================");
        // 我的待办
        taskService.myTodoTimeoutStatistic();
        // 我的发起
        taskService.myInitiativeTimeoutStatistic();
        XxlJobLogger.log("==================待办任务超时统计提醒job结束====================");
        return SUCCESS;
    }
}
