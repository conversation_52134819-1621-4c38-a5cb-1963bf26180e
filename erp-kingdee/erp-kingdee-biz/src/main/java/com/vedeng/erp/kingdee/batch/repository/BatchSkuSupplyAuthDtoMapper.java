package com.vedeng.erp.kingdee.batch.repository;
import org.apache.ibatis.annotations.Param;

/**
 * @description ${end}
 * <AUTHOR>
 * @date 2022/11/30 15:31
 **/
public interface BatchSkuSupplyAuthDtoMapper {

    /**
     * 授权类型
     * @param traderSupplyId 供营商id
     * @param time 采购单生效时间
     * @param skuNo sku 号
     * @return
     */
    String findByTraderSupplyIdAndValidStartTimeBeforeAndValidEndTimeAfter(@Param("traderSupplyId")Integer traderSupplyId,@Param("time")long time,@Param("sku")String skuNo);


}