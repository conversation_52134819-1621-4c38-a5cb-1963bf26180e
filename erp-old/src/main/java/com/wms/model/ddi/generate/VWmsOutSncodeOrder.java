package com.wms.model.ddi.generate;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * V_WMS_OUT_SNCODE_ORDER
 * <AUTHOR>
@Data
public class VWmsOutSncodeOrder implements Serializable {
    /**
     * 出库SN码追溯表id
     */
    private Integer outSncodeOrderId;

    /**
     * 关联订单id
     */
    private Integer orderId;

    /**
     * 关联订单商品id
     */
    private Integer orderGoodsId;

    /**
     * 业务类型 0销售出库 1销售售后出库 2采购售后出库 3借货出库 4报废出库 5调拔出库 6样品出库 7领用出库
     */
    private Byte operateType;

    /**
     * WMS上架交易号
     */
    private String wmsOrderNo;

    /**
     * SKU
     */
    private String sku;

    /**
     * 商品id
     */
    private Integer skuId;

    /**
     * 厂家SN码
     */
    private String snCode;

    /**
     * 贝登追溯码
     */
    private String serialNo;

    /**
     * 出库时间
     */
    private Date outTime;

    /**
     * 生产日期
     */
    private Date productDate;

    /**
     * 有效期至
     */
    private Date expirationDate;

    /**
     * 厂家批号
     */
    private String batchNumber;

    /**
     * 贝登批次码
     */
    private String vedengBatchNumer;

    /**
     * 备注
     */
    private String comments;

    /**
     * 创建时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date modeTime;

    /**
     * 是否删除  0否  1是
     */
    private Boolean isDelete;

    /**
     * 创建人
     */
    private Integer creator;

    /**
     * 更新人
     */
    private Integer updater;

    private static final long serialVersionUID = 1L;
}