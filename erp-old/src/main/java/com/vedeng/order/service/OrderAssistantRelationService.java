package com.vedeng.order.service;

import com.vedeng.common.page.Page;
import com.vedeng.order.model.OrderAssistantRelationDo;
import com.vedeng.order.model.dto.OrderAssistantRelationDto;

import java.util.List;
import java.util.Map;

public interface OrderAssistantRelationService {
    List<OrderAssistantRelationDo> getBingdedInfoByOrderAssId(Integer orderAssitantUserId);

    Map<String, Object> getAllBindedInfoByOrderAssIdSelectiveListPage(Integer orderAssitantUserId, Page page);
}
