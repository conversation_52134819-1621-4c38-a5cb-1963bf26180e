package com.vedeng.erp.kingdee.domain.command;

import com.vedeng.infrastructure.kingdee.domain.command.KingDeeNumberCommand;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 其他出库   https://www.yuque.com/manhuo/gf1570/ug85q9
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date
 */
@NoArgsConstructor
@Data
@Getter
@Setter
public class KingDeeStorageOutCommand {

    /**
     * 单据内码 0：表示新增 非0：云星空系统单据FID值，表示修改
     */
    private String fId;

    /**
     * 单据编号 填写单据编号，若为空则调用系统的编码规则生成
     */
    private String fBillNo;
    /**
     * 贝登单据头ID 贝登单据头ID号（预留）
     */
    private String F_QZOK_BDDJTID;

    /**
     * 库存方向 如果是入库，则默认值 ：GENERAL
     */
    private String fStockDirect;
    /**
     * 单据日期 格式yyyy-MM-dd
     */
    private String fDate;

    /**
     * 明细
     */
    private List<FEntity> fEntity;

    //Fnumber
    /**
     * 单据类型 填单据类型编码，默认QTCKD01_SYS
     */
    private KingDeeNumberCommand fBillTypeId = new KingDeeNumberCommand();

    /**
     * 库存组织 填写组织编码 默认101,配置化
     */
    private KingDeeNumberCommand fStockOrgId = new KingDeeNumberCommand();

    /**
     * 客户 填写客户编码
     */
    private KingDeeNumberCommand fCustId = new KingDeeNumberCommand();
    /**
     * 部门 填写部门编码，默认值 ：BM9999
     */
    private KingDeeNumberCommand fDeptId = new KingDeeNumberCommand();



    /**
     * FEntity
     */
    @NoArgsConstructor
    @Data
    public static class FEntity {

        /**
         * 实收数量
         */
        private String fQty;
        /**
         * 原始订单号
         */
        private String F_QZOK_YSDDH;
        /**
         * 归属业务单号
         */
        private String F_QZOK_GSYWDH;
        /**
         * 业务类型
         */
        private String F_QZOK_YWLX;
        /**
         * 批次号
         */
        private String F_QZOK_PCH;
        /**
         * 序列号
         */
        private String F_QZOK_XLH;
        /**
         * 授权类型
         */
        private String F_QZOK_SQLX;
        /**
         * 是否直发
         */
        private String F_QZOK_SFZF;
        /**
         * 贝登订单行ID
         */
        private String F_QZOK_BDDJHID;

        //Fnumber
        /**
         * 填写物料编码 SKU
         */
        private KingDeeNumberCommand fMaterialId = new KingDeeNumberCommand();
        /**
         * 仓库 填写仓库编码，默认值 ：CK9999
         */
        private KingDeeNumberCommand fStockId = new KingDeeNumberCommand();

    }

}
