<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.vedeng.aftersales.dao.EngineerMapper" >

<update id="updateByPrimaryKey" parameterType="com.vedeng.aftersales.model.Engineer" >
    update T_ENGINEER
    <set >
        <if test="companyId != null" >
            COMPANY_ID = #{companyId,jdbcType=INTEGER},
        </if>
        <if test="name != null" >
            NAME = #{name,jdbcType=VARCHAR},
        </if>
        <if test="owner != null" >
            OWNER = #{owner,jdbcType=BIT},
        </if>
        <if test="sex != null" >
            SEX = #{sex,jdbcType=BIT},
        </if>
        <if test="sex != null" >
            SEX = #{sex,jdbcType=BIT},
        </if>
        <if test="mobile != null" >
            MOBILE = #{mobile,jdbcType=VARCHAR},
        </if>
        <if test="telephone != null" >
            TELEPHONE = #{telephone,jdbcType=VARCHAR},
        </if>
        <if test="weixin != null" >
            WEIXIN = #{weixin,jdbcType=VARCHAR},
        </if>
        <if test="workYear != null" >
            WORK_YEAR = #{workYear,jdbcType=BIT},
        </if>
        <if test="areaId != null" >
            AREA_ID = #{areaId,jdbcType=INTEGER},
        </if>
        <if test="areaIds != null" >
            AREA_IDS = #{areaIds,jdbcType=VARCHAR},
        </if>
        <if test="company != null" >
            COMPANY = #{company,jdbcType=VARCHAR},
        </if>
        <if test="isEnable != null" >
            IS_ENABLE = #{isEnable,jdbcType=BIT},
        </if>
        <if test="disableTime != null" >
            DISABLE_TIME = #{disableTime,jdbcType=BIGINT},
        </if>
        <if test="disableReason != null" >
            DISABLE_REASON = #{disableReason,jdbcType=VARCHAR},
        </if>
        <if test="bank != null" >
            BANK = #{bank,jdbcType=VARCHAR},
        </if>
        <if test="bankCode != null" >
            BANK_CODE = #{bankCode,jdbcType=VARCHAR},
        </if>
        <if test="bankAccount != null" >
            BANK_ACCOUNT = #{bankAccount,jdbcType=VARCHAR},
        </if>
        <if test="serviceScore != null" >
            SERVICE_SCORE = #{serviceScore,jdbcType=DECIMAL},
        </if>
        <if test="skillScore != null" >
            SKILL_SCORE = #{skillScore,jdbcType=DECIMAL},
        </if>
        <if test="serviceProducts != null" >
            SERVICE_PRODUCTS = #{serviceProducts,jdbcType=VARCHAR},
        </if>
        <if test="comments != null" >
            COMMENTS = #{comments,jdbcType=VARCHAR},
        </if>
        <if test="modTime != null" >
            MOD_TIME = #{modTime,jdbcType=BIGINT},
        </if>
        <if test="updater != null" >
            UPDATER = #{updater,jdbcType=INTEGER},
        </if>
        <if test="card != null" >
            CARD = #{card,jdbcType=VARCHAR},
        </if>
    </set>
    where ENGINEER_ID = #{engineerId,jdbcType=INTEGER}
</update>

    <select id="selectByPrimaryKey" resultType="com.vedeng.aftersales.model.Engineer">
    SELECT
        *
    FROM
        `T_ENGINEER`
    WHERE
        ENGINEER_ID = #{engineerId,jdbcType=INTEGER}
  </select>

    <select id="getEngineerVoListPage" parameterType="Map" resultType="com.vedeng.aftersales.model.vo.EngineerVo">
        SELECT
        a.ENGINEER_ID, a.NAME, a.OWNER, a.SEX, a.MOBILE, a.TELEPHONE, a.WEIXIN, a.WORK_YEAR, a.AREA_ID, a.AREA_IDS, a.COMPANY, a.IS_ENABLE,
        a.DISABLE_TIME, a.DISABLE_REASON, a.BANK, a.BANK_CODE, a.BANK_ACCOUNT, a.SERVICE_SCORE, a.SKILL_SCORE,
        a.SERVICE_PRODUCTS, a.COMMENTS, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,snum.NUM as SERVICE_TIMES
        FROM
        T_ENGINEER a
        left join T_AFTER_SALES_INSTALLSTION b on a.ENGINEER_ID = b.ENGINEER_ID
        LEFT JOIN (SELECT c.ENGINEER_ID, count(c.ENGINEER_ID) as NUM
        from T_ENGINEER c
        left join T_AFTER_SALES_INSTALLSTION d on c.ENGINEER_ID = d.ENGINEER_ID
        where c.IS_ENABLE = 1
        GROUP BY c.ENGINEER_ID ) as snum on snum.ENGINEER_ID = a.ENGINEER_ID
        WHERE a.IS_ENABLE = 1
        AND IFNULL(a.TRADER_SUPPLIER_ID, 0)!= 0
        <if test="engineerVo.companyId != null">
            AND a.COMPANY_ID = #{engineerVo.companyId}
        </if>
        <if test="engineerVo.afterSalesId != null and engineerVo.afterSalesId !=0">
            AND b.AFTER_SALES_ID = #{engineerVo.afterSalesId}
        </if>
        <if test="engineerVo.name != null and engineerVo.name !=''">
            AND a.NAME like CONCAT('%',#{engineerVo.name},'%' )
        </if>
        GROUP BY a.ENGINEER_ID
        ORDER BY snum.NUM desc, IF(a.AREA_ID = #{engineerVo.areaId} ,0,1)
    </select>

    <select id="queryCompanyListPage" parameterType="Map" resultType="com.vedeng.aftersales.model.vo.EngineerVo">
        SELECT
        a.ENGINEER_ID, a.NAME, a.OWNER, a.SEX, a.MOBILE, a.TELEPHONE, a.WEIXIN, a.WORK_YEAR, a.AREA_ID, a.AREA_IDS, a.COMPANY, a.IS_ENABLE,
        a.DISABLE_TIME, a.DISABLE_REASON, a.BANK, a.BANK_CODE, a.BANK_ACCOUNT, a.SERVICE_SCORE, a.SKILL_SCORE,
        a.SERVICE_PRODUCTS, a.COMMENTS, a.ADD_TIME, a.CREATOR, a.MOD_TIME, a.UPDATER,IFNULL(snum.NUM,0) as SERVICE_TIMES
        FROM
        T_ENGINEER a
        left join T_AFTER_SALES_INSTALLSTION b on a.ENGINEER_ID = b.ENGINEER_ID
        LEFT JOIN (SELECT c.ENGINEER_ID, count(c.ENGINEER_ID) as NUM
        from T_ENGINEER c
        join T_AFTER_SALES_INSTALLSTION d on c.ENGINEER_ID = d.ENGINEER_ID
        GROUP BY c.ENGINEER_ID ) as snum on snum.ENGINEER_ID = a.ENGINEER_ID
        WHERE 1 = 1
        <if test="engineerVo.isEnable != null and engineerVo.isEnable !=-1">
            AND a.IS_ENABLE = #{engineerVo.isEnable}
        </if>
        <if test="engineerVo.name != null and engineerVo.name !=''">
            AND a.NAME like CONCAT('%',#{engineerVo.name},'%' )
        </if>
        GROUP BY a.ENGINEER_ID
        ORDER BY snum.NUM desc
    </select>
</mapper>