<!doctype html>
<html lang="en"> 
 <head> 
  <meta charset="UTF-8"> 
  <title>客户账期申请记录</title>
  <link href="https://cdn.staticfile.org/layui/2.6.13/css/layui.css" rel="stylesheet"> 
 </head> 
 <body id="maXLYs5wr08" datasource="erp-reportdatasource" fixednumber="2" append_column_url="" append_row_url="" empty_show="" firstcol="numbers">
  <div id="appendHead"></div> 
  <div class="layui-fluid"> 
   <div class="layui-tab"> 
    <ul class="layui-tab-title" id="tab"> 
    </ul> 
   </div> 
   <form class="layui-form" id="search">
    <div class=" layui-inline "><label class="layui-form-label">当前审核人</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="20" name="VERIFY_USERNAME" placeholder="" style="" alias="VI" jdbctype="" data="SELECT DISTINCT lower(u.USERNAME) K, lower(u.USERNAME) V
FROM T_USER u 
WHERE   u.IS_DISABLED=0 and u.COMPANY_ID=1  order by u.USERNAME;" datatype="KVSQL" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">部门</label>
     <div class="layui-input-inline"><object type="search-org" class=" layui-input list-search-item " name="ORG_ID" placeholder="" style="" alias="E" jdbctype="NUMBER" data="" datatype="" oper="IN"></object>
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">审核状态</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="21" name="CHECK_STATUS" placeholder="" style="" alias="T1" jdbctype="" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">账期类型</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="21" name="BILL_PERIOD_TYPE" placeholder="" style="" alias="" jdbctype="" data="[{&quot;V&quot;:&quot;正式账期&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;临时账期&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;订单账期&quot;,&quot;K&quot;:&quot;3&quot;}]" datatype="JSON" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">申请日期</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="daterange" name="ADD_TIME" placeholder="" style="" alias="" jdbctype="DATE" data="" datatype="" oper="BETWEEN"></object>
     </div>
    </div>
    <div class=" layui-inline ">
     <label class="layui-form-label">申请人</label>
     <div class="layui-input-inline"><object class=" layui-input list-search-item " type="20" name="USERNAME" placeholder="" style="" alias="" jdbctype="" data="SELECT DISTINCT u.USER_ID K, lower(u.USERNAME) V
FROM T_USER u
         LEFT JOIN T_R_USER_POSIT up ON u.USER_ID = up.USER_ID
         LEFT JOIN T_POSITION p on up.POSITION_ID = p.POSITION_ID
         LEFT JOIN T_ORGANIZATION o ON p.ORG_ID = o.ORG_ID
WHERE (o.TYPE = 311 or o.TYPE=310) and u.IS_DISABLED=0 and u.COMPANY_ID=1  order by u.USERNAME;" datatype="KVSQL" oper=""></object> 
     </div>
    </div>
    <div class=" layui-inline "><label class="layui-form-label">客户名称</label>
     <div class="layui-input-inline">
      <object class=" layui-input list-search-item " type="19" name="TRADER_NAME" placeholder="" style="" alias="b" jdbctype="" data="" datatype="TEXT" oper="LIKE"></object> 
     </div>
    </div>
   </form> 
   <hr class="layui-border-blue"> 
   <div class="btn-group   bd-highlight" id="tableButton">
       <button item_name="导出" name="导出" url="http://ezadmin.ivedeng.com/ezadmin/list/export-maXLYs5wr08" opentype="_BLANK_PARAM_COLUMN" windowname="" style type="table">导出</button>
   </div>
   <table id="table" class="layui-table" style=" width:100%"> 
    <thead> 
     <tr id="column">
      <th type="rowbutton" id="rowbutton">
          <button item_name="查看" name="查看" url="/finance/accountperiod/getAccountPeriodApply.do?billPeriodApplyId=${BILL_PERIOD_APPLY_ID}&traderId=${TRADER_ID}&traderCustomerId=${TRADER_CUSTOMER_ID}&billPeriodType=${BILL_PERIOD_TYPE}&traderAccountPeriodApplyId=&traderType=1"
                  opentype="PARENT" windowname=""
                  style type="single">查看</button>
      </th>
      <th item_name="TRADER_NAME" name="TRADER_NAME" body="td-link" order="0" url="/trader/customer/baseinfo.do?traderId=${TRADER_ID}" opentype="PARENT" windowname="" datatype="" data="" style="" head="18">客户名称</th>
      <th item_name="CUSTOMER_NATURE" name="CUSTOMER_NATURE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户性质</th>
      <th item_name="CUSTOMER_TYPE" name="CUSTOMER_TYPE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">客户类型</th>
      <th item_name="USERNAME" name="USERNAME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请人</th>
      <th item_name="ADD_TIME" name="ADD_TIME" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">申请日期</th>
      <th item_name="BILL_PERIOD_TYPE" name="BILL_PERIOD_TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;正式账期&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;临时账期&quot;,&quot;K&quot;:&quot;2&quot;},{&quot;V&quot;:&quot;订单账期&quot;,&quot;K&quot;:&quot;3&quot;}]" style="" head="18">账期类型</th>
      <th item_name="OPERATE_TYPE" name="OPERATE_TYPE" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;新增&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;调整&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">申请类型</th>
      <th item_name="BEFORE_APPLY_AMOUNT" name="BEFORE_APPLY_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期原额度</th>
      <th item_name="CREDIT_USABLE_AMOUNT" name="CREDIT_USABLE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">账期可用额</th>
      <th item_name="COUNT_OF_OVERDUE" name="COUNT_OF_OVERDUE" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期次数</th>
      <th item_name="OVERDUE_AMOUNT" name="OVERDUE_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">逾期未还金额</th>
      <th item_name="BEFORE_BILL_PERIOD_END" name="BEFORE_BILL_PERIOD_END" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">原有效期</th>
      <th item_name="APPLY_AMOUNT" name="APPLY_AMOUNT" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">本次额度申请（元）</th>
      <th item_name="SETTLEMENT_PERIOD" name="SETTLEMENT_PERIOD" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18" jdbctype="NUMBER">本次结算周期（天）</th>
      <th item_name="BILL_PERIOD_END" name="BILL_PERIOD_END" body="td-text" order="0" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">本次申请有效期</th>
      <th item_name="CHECK_STATUS" name="CHECK_STATUS" body="td-select" order="0" url="" opentype="MODEL" windowname="" datatype="JSON" data="[{&quot;V&quot;:&quot;审核中&quot;,&quot;K&quot;:&quot;0&quot;},{&quot;V&quot;:&quot;审核通过&quot;,&quot;K&quot;:&quot;1&quot;},{&quot;V&quot;:&quot;审核不通过&quot;,&quot;K&quot;:&quot;2&quot;}]" style="" head="18">审核状态</th>
      <th item_name="VERIFY_USERNAME" name="VERIFY_USERNAME" body="td-text" order="1" url="" opentype="MODEL" windowname="" datatype="" data="" style="" head="18">当前审核人</th>
     </tr> 
    </thead> 
    <tbody> 
     <tr> 
      <td colspan="100"> express: <pre id="express" class="layui-code" orderby="ORDER BY  T1.BILL_PERIOD_TYPE,b.TRADER_ID" groupby="group BY 	T1.BILL_PERIOD_APPLY_ID">StringBuilder sql=new StringBuilder("SELECT
	b.TRADER_NAME  ,
	b.TRADER_ID,

	d.TITLE AS CUSTOMER_NATURE,
	f.TITLE AS CUSTOMER_TYPE,
	tu.USERNAME,
	FROM_UNIXTIME( IF ( T1.ADD_TIME = 0, NULL, T1.ADD_TIME ) / 1000, '%Y-%m-%d' ) AS ADD_TIME,
T1.BILL_PERIOD_TYPE,
T1.OPERATE_TYPE ,
	T1.BEFORE_APPLY_AMOUNT,
CASE
		
		WHEN TT3.CREDIT_USABLE_AMOUNT IS NULL THEN
		0.00 ELSE TT3.CREDIT_USABLE_AMOUNT 
	END CREDIT_USABLE_AMOUNT,
	SUM( IF ( T3.OVERDUE_AMOUNT IS NOT NULL, 1, 0 ) ) COUNT_OF_OVERDUE,
	SUM( IFNULL( T3.UNRETURNED_AMOUNT, 0 ) ) OVERDUE_AMOUNT,
CASE
		
		WHEN FROM_UNIXTIME( IF ( T1.BEFORE_BILL_PERIOD_END = 0, NULL, T1.BEFORE_BILL_PERIOD_END ) / 1000, '%Y-%m-%d %H:%i:%s' ) IS NULL THEN
		'-' ELSE FROM_UNIXTIME( IF ( T1.BEFORE_BILL_PERIOD_END = 0, NULL, T1.BEFORE_BILL_PERIOD_END ) / 1000, '%Y-%m-%d %H:%i:%s' ) 
	END BEFORE_BILL_PERIOD_END,
	T1.BILL_PERIOD_APPLY_ID,
	T1.BILL_PERIOD_ID,
	T1.CUSTOMER_ID,
	T1.CREATOR,
CASE
		
		WHEN T2.SETTLEMENT_TYPE = 1 THEN
		'产品发货' ELSE '产品开票' 
	END SETTLEMENT_TYPE,
	T1.APPLY_AMOUNT,
	T1.SETTLEMENT_PERIOD,
CASE
		
		WHEN T1.BILL_PERIOD_TYPE = 3 THEN
		'-' 
		WHEN FROM_UNIXTIME( IF ( T1.BILL_PERIOD_END = 0, NULL, T1.BILL_PERIOD_END ) / 1000, '%Y-%m-%d %H:%i:%s' ) IS NULL THEN
		'-' ELSE FROM_UNIXTIME( IF ( T1.BILL_PERIOD_END = 0, NULL, T1.BILL_PERIOD_END ) / 1000, '%Y-%m-%d %H:%i:%s' ) 
	END BILL_PERIOD_END,
 T1.CHECK_STATUS  ,
	E.ORG_ID,
	E.ORG_NAME ,a.TRADER_CUSTOMER_ID,VI.VERIFY_USERNAME
FROM
	T_CUSTOMER_BILL_PERIOD_APPLY T1
	LEFT JOIN T_TRADER_CUSTOMER a ON T1.CUSTOMER_ID = a.TRADER_CUSTOMER_ID
	LEFT JOIN T_TRADER b ON a.TRADER_ID = b.TRADER_ID

	LEFT JOIN T_R_USER_POSIT C ON T1.CREATOR= C.USER_ID
	LEFT JOIN T_POSITION D ON C.POSITION_ID = D.POSITION_ID
	LEFT JOIN T_ORGANIZATION E ON D.ORG_ID = E.ORG_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION d ON a.CUSTOMER_NATURE = d.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_SYS_OPTION_DEFINITION f ON a.CUSTOMER_TYPE = f.SYS_OPTION_DEFINITION_ID
	LEFT JOIN T_CUSTOMER_BILL_PERIOD_USE_DETAIL T2 ON T1.BILL_PERIOD_ID = T2.BILL_PERIOD_ID 
	AND T2.USE_TYPE = 1
	LEFT JOIN T_CUSTOMER_BILL_PERIOD_OVERDUE_MANAGEMENT_DETAIL T3 ON T2.BILL_PERIOD_USE_DETAIL_ID = T3.BILL_PERIOD_USE_DETAIL_ID 
	AND T3.OVERDUE_AMOUNT &gt; 0
	LEFT JOIN T_USER tu ON T1.CREATOR = tu.USER_ID
	LEFT JOIN T_CUSTOMER_BILL_PERIOD T4 ON T2.BILL_PERIOD_ID = T4.BILL_PERIOD_ID
	LEFT JOIN (
	SELECT
		TT1.BILL_PERIOD_ID AS BILL_PERIOD_ID,
		( TT1.APPLY_AMOUNT - IFNULL( sum( TT2.AMOUNT ), 0 ) ) AS CREDIT_USABLE_AMOUNT 
	FROM
		T_CUSTOMER_BILL_PERIOD TT1
		LEFT JOIN T_CUSTOMER_BILL_PERIOD_USE_DETAIL TT2 ON TT1.BILL_PERIOD_ID = TT2.BILL_PERIOD_ID 
	GROUP BY
		TT1.BILL_PERIOD_ID 
	) TT3 ON T4.BILL_PERIOD_ID = TT3.BILL_PERIOD_ID 
 left join T_VERIFIES_INFO VI ON VI.RELATE_TABLE='T_CUSTOMER_BILL_PERIOD_APPLY'
    AND VI.RELATE_TABLE_KEY=T1.BILL_PERIOD_APPLY_ID
WHERE
	1 = 1 



  AND tu.USER_ID not in (341
,681
,751
,779
,892
,893
,894
,897
,917 
,929 
,955 
,964

    ) ");
 
list=search(sql);
return list;</pre> count: <pre id="count" class="layui-code"></pre></td> 
     </tr> 
    </tbody> 
   </table> 
  </div> 
  <div id="appendFoot"></div> 
  <script src="https://cdn.staticfile.org/layui/2.6.13/layui.js"></script> 
  <script>
    layui.use(function () {

    })
</script> 
 </body>
</html>