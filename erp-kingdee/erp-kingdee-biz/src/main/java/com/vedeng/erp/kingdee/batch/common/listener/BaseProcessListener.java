package com.vedeng.erp.kingdee.batch.common.listener;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.batch.core.ItemProcessListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: process监听器
 * @date 2023/2/22 10:22
 */
@Component
@Slf4j
public class BaseProcessListener<T, S> implements ItemProcessListener<T, S> {


    @Override
    public void beforeProcess(T item) {
        log.info("step 处理器之前 :{}", JSONObject.toJSONString(item));
    }

    @Override
    public void afterProcess(T item, S result) {
        log.info("step 处理器之后 process:{} result:{}", JSONObject.toJSONString(item), JSONObject.toJSONString(result));
    }

    @Override
    public void onProcessError(T item, Exception e) {
        log.error("step 处理器异常 process:{} ", JSONObject.toJSONString(item), e);
    }

}
