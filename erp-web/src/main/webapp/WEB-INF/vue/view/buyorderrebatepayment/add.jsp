<%@ page language="java" contentType="text/html; charset=UTF-8"
         pageEncoding="UTF-8" %>
<%@ include file="../common/common.jsp" %>
<head>
    <title>售后订单开票申请</title>
</head>

<body>
<div id="app" style="display: none;">
    <div class="container">
        <div class="input_class">
            <span class="span_class">返利可用余额：</span>
            <el-input v-model="enableRebateCharge" readonly size="medium" class="no-border  red-text"></el-input>
        </div>
        <div class="input_class">
            <span class="span_class">返利总额：</span>
            <el-input v-model="rebateTotalAmount" placeholder="请输入" size="medium" @input="limitToTwoDecimal"></el-input>
        </div>
        <div class="input_class">
            <span class="span_class">数量：</span>
            <el-input v-model="num" readonly size="medium" class="no-border"></el-input>
        </div>
        <div class="input_class">
            <span class="span_class">返利单价：</span>
            <el-input v-model="rebatePrice" placeholder="自动计算" readonly size="medium" class="no-border"></el-input>
        </div>
        <div class="input_class">
            <span class="span_class">返利后单价：</span>
            <el-input v-model="afterRebatePrice" placeholder="自动计算" readonly size="medium" class="no-border"></el-input>
        </div>
        <div class="button_group">
            <el-button @click="cancel()">取消</el-button>
            <el-button type="primary" @click="submit()">确定</el-button>
        </div>
    </div>
</div>
</body>
<script src="https://cdn.jsdelivr.net/npm/decimal.js-light@2.5.1/decimal.min.js"></script>
<script type="text/javascript">
    function isDivisibleWithinTwoDecimals(a, b) {
        let aInt = Math.round(a * 100);
        let bInt = Math.round(b * 100);
        let result = aInt / bInt;
        return !result.toString().match(/^\d+\.\d{3,}$/);
    }
    /**
     * 比较两个金额（以元为单位的小数），避免浮点精度误差
     * @param {number|string} a - 金额A（如 rebateTotalAmount）
     * @param {number|string} b - 金额B（如 price * num）
     * @returns {number} 返回 1 表示 a > b，0 表示 a === b，-1 表示 a < b
     */
    function compareAmount(a, b) {
        const amountA = Math.round(Number(a) * 100); // 转为分单位，整数比较
        const amountB = Math.round(Number(b) * 100);
        if (amountA > amountB) return 1;
        if (amountA < amountB) return -1;
        return 0;
    }


    let addRebateItem = ${addRebateItem};

    window.myVueInstance = new Vue({
        el: '#app',
        data() {
            return {
                rebateTotalAmount: null,
                rebatePrice: null,
                afterRebatePrice: null,
                enableRebateCharge: '',
                price: null,
                num: null,
                goodsId: null,
                buyorderGoodsId: null,
                flag: false,
                initialRender: true, // 标志变量，表示是否是初次渲染
                availableRebateCharge: 0

            }
        },

        mounted() {
            loadingApp()
            const parentTables = parent.document.querySelectorAll('table');
            let totalRebateValues = [];
            // 遍历每个表格
            parentTables.forEach(table => {
                // 获取每个表格中的所有name为totalRebate的span元素
                const totalRebateSpans = table.querySelectorAll('span[name="totalRebate"]');
                // 遍历并获取值
                totalRebateValues = totalRebateValues.concat(Array.from(totalRebateSpans).map(span => span.textContent));
            });
            console.log(totalRebateValues);
            // 订单中已经使用的返利
            const sumTotalRebate = totalRebateValues.reduce((acc, val) => Number(acc) + Number(val), 0).toFixed(2);
            // 使用总返利加上已使用的返利
            this.rebateTotalAmount = Number(addRebateItem.totalRebate) ? Number(addRebateItem.totalRebate) : '0.00';
            this.enableRebateCharge = Number(Number(addRebateItem.validRebateCharge) + Number(addRebateItem.usedRebateCharge) - Number(sumTotalRebate)).toFixed(2);
            this.availableRebateCharge = Number(Number(addRebateItem.validRebateCharge) + Number(addRebateItem.usedRebateCharge)).toFixed(2);
            this.price = addRebateItem.price;
            this.num = addRebateItem.num;
            this.goodsId = addRebateItem.goodsId;
            this.buyorderGoodsId = addRebateItem.buyorderGoodsId;
        },
        watch: {
            rebateTotalAmount(newVal, oldValue) {
                console.log(newVal, oldValue)
                newVal = Number(newVal)
                oldValue = Number(oldValue)
                this.rebatePrice = (isDivisibleWithinTwoDecimals(newVal, this.num) == true) ? Number(newVal / this.num).toFixed(2) : newVal / this.num;
                this.afterRebatePrice = Number(Number(this.price) - Number(this.rebatePrice)).toFixed(2);


                if (this.initialRender) {
                    this.initialRender = false;
                } else {
                    this.enableRebateCharge = Number(Number(this.enableRebateCharge) +oldValue - newVal).toFixed(2);
                }
            }

        },
        methods: {
            limitToTwoDecimal(value) {
                // 仅允许输入数字和小数点
                let newValue = value.replace(/[^0-9.]/g, '');

                // 确保仅有一个小数点，并限制小数点后两位
                const parts = newValue.split('.');
                if (parts.length > 2) {
                    newValue = `${parts[0]}.${parts.slice(1).join('')}`;
                }
                if (parts.length === 2 && parts[1].length > 2) {
                    newValue = `${parts[0]}.${parts[1].slice(0, 2)}`;
                }
                // 检查输入是否有效，如果无效则重置为0
                if (!newValue || isNaN(newValue)) {
                    newValue = '';
                }

                this.rebateTotalAmount = newValue;
            },
            cancel() {
                layer.closeAll();
                parent.layer.closeAll();
            },
            submit() {
                if (Number(this.rebatePrice) != Number(this.rebatePrice).toFixed(2)) {
                    this.$message.error('返利单价最多保留两位小数');
                    this.rebateTotalAmount = '0.00';
                    return
                }
                if (this.rebateTotalAmount < 0) {
                    this.$message.error('返利总额不能小于0');
                    this.rebateTotalAmount = '0.00';
                    return
                }
                // if (Number(this.rebateTotalAmount) > Number(this.price * this.num)) {
                //     this.$message.error('返利总额不能大于sku总额');
                //     this.rebateTotalAmount = '0.00';
                //     return;
                // }
                if (compareAmount(this.rebateTotalAmount, this.price * this.num) > 0) {
                    this.$message.error('返利总额不能大于sku总额');
                    this.rebateTotalAmount = '0.00';
                    return;
                }
                if (Number(this.enableRebateCharge) < Number("0.00")) {
                    this.$message.error('返利总额不能大于可用余额');
                    this.rebateTotalAmount = '0.00';
                    return;
                }
                if (!this.flag) {
                    this.flag = false;
                    if (window.parent.submitRebateChargeItem) {
                        window.parent.submitRebateChargeItem(
                            {
                                goodsId: this.goodsId,
                                buyorderGoodsId: this.buyordergoodsId,
                                rebateTotalAmount: this.rebateTotalAmount,
                                rebatePrice: this.rebatePrice,
                                afterRebatePrice: this.afterRebatePrice,
                                num: this.num
                            }
                        );
                        layer.closeAll();
                        parent.layer.closeAll();
                    }
                }
            }
        },
    });
</script>
<style>
    .container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 90vh; /* 高度可根据需求调整 */
    }

    .input_class {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .span_class {
        width: 120px; /* 可以根据需要调整宽度 */
        text-align: right;
        margin-right: 10px;
    }

    .el-input, .el-input-number {
        width: 120px;
        flex: 1;
    }

    .button_group {
        display: flex;
        justify-content: center;
        margin-top: 20px;
    }

    .button_group .el-button {
        margin: 0 10px;
    }

    .no-border .el-input__inner {
        border: none;
    }

    .red-text .el-input__inner {
        color: red;
    }
</style>
</html>
