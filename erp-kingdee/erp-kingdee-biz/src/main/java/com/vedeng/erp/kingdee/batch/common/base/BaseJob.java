package com.vedeng.erp.kingdee.batch.common.base;

import com.vedeng.erp.kingdee.batch.common.listener.BaseProcessListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseReadListener;
import com.vedeng.erp.kingdee.batch.common.listener.BaseWriteListener;
import com.vedeng.erp.kingdee.batch.common.listener.JobListener;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.batch.core.configuration.annotation.JobBuilderFactory;
import org.springframework.batch.core.configuration.annotation.StepBuilderFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 基础batchJob
 * @date 2023/6/13 14:35
 */
public class BaseJob {

    @Autowired
    protected JobBuilderFactory jobBuilderFactory;
    @Autowired
    protected JobListener jobListener;
    @Autowired
    protected StepBuilderFactory stepBuilderFactory;
    @Autowired
    @Qualifier("sqlSessionFactoryKingDee")
    protected SqlSessionFactory sqlSessionFactory;
    @Autowired
    protected BaseProcessListener baseProcessListener;
    @Autowired
    protected BaseReadListener baseReadListener;
    @Autowired
    protected BaseWriteListener baseWriteListener;
    @Autowired
    protected TaskExecutor kingDeeGlobalTaskExecutor;
}
