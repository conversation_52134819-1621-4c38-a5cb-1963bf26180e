package com.vedeng.billsync.dao;


import com.vedeng.billsync.task.model.entity.TmpWechatBillDataExtDo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface TmpWechatBillDataExtMapper extends TmpWechatBillDataMapper {
    int deleteBySycTime(String billDate);

    List<TmpWechatBillDataExtDo> getWechatBillListByTime(@Param("queryDate") Date queryDate);
}